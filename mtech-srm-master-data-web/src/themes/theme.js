import cssVars from 'css-vars-ponyfill' // css var 的垫片

// type TThemeType = 'default' | 'dark'

export default class ChangeTheme {
  styleId = ''
  isIE = false
  theme = ''

  constructor(id) {
    this.styleId = id
    this.isIE = this.checkIsIE()
  }

  change(theme) {
    this.theme = theme
    Array.from(document.querySelectorAll('link[data-mt-theme]')).forEach((link) => {
      const url = new URL(link.href)
      const href = this.getLinkHref(url.origin, theme)
      const newLink = this.createLink(href, theme)
      link.parentElement.appendChild(newLink)
    })

    if (this.isIE) {
      setTimeout(() => {
        cssVars({
          watch: true
        })
      })
    }
  }

  add(theme, container, origin) {
    const href = this.getLinkHref(origin, theme)
    const link = this.createLink(href, theme)

    container.appendChild(link)
  }

  getLinkHref(origin, theme) {
    const href = `${origin}/themes/${theme}.css`

    return href
  }

  createLink(href, theme) {
    const link = document.createElement('link')
    link.onload = this.deleteLink(href)
    link.setAttribute('rel', 'stylesheet')
    link.setAttribute('type', 'text/css')
    link.setAttribute('href', href)
    link.dataset.mtTheme = theme

    return link

    // document.head.appendChild(link);
  }

  deleteLink(href) {
    return () => {
      const linkList = document.querySelectorAll('link')
      const themeDir = `${location.origin}/themes`

      for (let i = 0; i < linkList.length; i++) {
        if (linkList[i].href !== href && linkList[i].href.startsWith(themeDir)) {
          linkList[i].remove()
        }
      }
    }
  }

  checkIsIE() {
    // TOOD: IE浏览器 的判断
    return false
  }
}
