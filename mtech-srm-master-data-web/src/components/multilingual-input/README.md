# @digis/multilingual-input

### 功能：
多语言输入框
### 引入：
```js
import Vue from "vue";
import MtMultilingualInput from "@digis/multilingual-input";
import "@digis/multilingual-input/build/esm/bundle.css";
Vue.use(MtMultilingualInput);

```
### 属性

|     属性名     |                          定义                           |     参数      |类型|
| :------------: | :-----------------------------------------------------: | :-----------: | :-----------: |
|  groupCode   |              应用编码                 |     'srm'/'middle-platform'···     | string|
|  v-model   |                格式化数据                 |     ''       |string|


### 示例
```html
    <mt-multilingual-input
      group-code="srm"
      v-model="'@{xxjdsdj|登录}@'"
    >
    </mt-multilingual-input>
```