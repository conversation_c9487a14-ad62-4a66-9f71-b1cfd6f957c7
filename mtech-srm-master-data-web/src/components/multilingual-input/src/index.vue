<template>
  <div class="mt-multilingual-input">
    <div class="mt-multilingual-input-main">
      <mt-input v-bind="$attrs" v-on="$listeners" disabled v-model="name"> </mt-input>
      <div class="cell-uploader" @click="showDialog">
        <img style="height: 16px" src="./images/icon_input_multilanguage.svg" />
      </div>
      <mt-dialog
        ref="MtDictionaryEditorToast"
        css-class="dialog-form-flex"
        :header="$t('多语言编辑')"
        :buttons="buttons"
        :open="onOpen"
        :show-close-icon="false"
      >
        <div>
          <mt-form ref="multilingualInputRuleForm" :model="ruleForm" :rules="rules">
            <mt-form-item
              class="form-item"
              :label="item.name"
              v-for="(item, i) in langList"
              :key="i"
              :prop="i == 0 ? item.shortCode : ''"
            >
              <mt-input
                item-label-style="left"
                v-model="ruleForm[item.shortCode]"
                :disabled="false"
                :show-clear-button="true"
                type="text"
              ></mt-input>
            </mt-form-item>
          </mt-form>
        </div>
      </mt-dialog>
    </div>
  </div>
</template>
<script>
import { API } from '@mtech-common/http'

export default {
  name: 'MtMultilingualInput',
  props: {
    value: {
      type: String,
      default: ''
    },
    groupCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      rules: {
        zh: [{ required: true, message: this.$t('请输入基础语言'), trigger: 'blur' }]
      },
      ruleForm: {},
      langList: [],
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: () => {
            this.$refs.multilingualInputRuleForm.validate((valid) => {
              if (valid) {
                this.$emit('getFormData', this.ruleForm)
                this.createAndUpdate()
              } else {
                console.log('error submit!!')
                return false
              }
            })
          },
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  computed: {
    name() {
      return this.getInputName(this.value)
    }
  },
  watch: {
    value() {
      this.queryAll()
    }
  },
  mounted() {
    this.getLanList()
    this.queryAll()
  },
  methods: {
    showDialog() {
      this.$refs.MtDictionaryEditorToast.ejsRef.show()
    },
    hide() {
      this.$refs.MtDictionaryEditorToast.ejsRef.hide()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    getLanList() {
      API.get('/i18n/common/lang/list').then((res) => {
        this.langList.push(...res.data)
      })
    },
    queryAll() {
      if (this.value) {
        API.get('/i18n/tenant/translationDict/queryByCode', {
          code: this.getInputCode(this.value)
        }).then((res) => {
          // this.langList.push(...res.data)
          this.ruleForm = { ...res.data.displayMap }
        })
      } else {
        this.ruleForm = {}
      }
    },
    getInputName(str) {
      return str.slice(str.indexOf('|') + 1, str.indexOf('}'))
    },
    getInputCode(str) {
      return str.slice(str.indexOf('{') + 1, str.indexOf('|'))
    },
    createAndUpdate() {
      let params = {
        displayMap: this.ruleForm,
        groupCode: this.groupCode
      }
      if (this.value) {
        params.code = this.getInputCode(this.value)
      }
      API.post('/i18n/tenant/translationDict/createAndUpdate', params)
        .then((res) => {
          // this.langList.push(...res.data)
          console.log('object11', res)
          if (res.code == 200) {
            this.$emit('input', res.data)
            this.$emit('confirmRequest', { content: res, type: 'success' })
            this.hide()
          } else {
            this.$emit('confirmRequest', { content: res, type: 'error' })
          }
        })
        .catch((error) => {
          this.$emit('confirmRequest', { content: error, type: 'error' })
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-form-flex .e-dlg-content {
  padding: 40px !important;
  .mt-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 390px;
      .mt-form-item__content {
        width: 100%;
      }
    }
  }
}
.mt-multilingual-input {
  width: 100%;
  .mt-multilingual-input-main {
    position: relative;
    .cell-uploader {
      cursor: pointer;
      position: absolute;
      right: 0;
      top: 0;
      height: 30px;
      line-height: 38px;
    }
  }
}
::v-deep .e-input-group.e-control-wrapper .e-input[readonly] {
  padding-right: 140px;
  overflow: hidden; //超出的文本隐藏
  text-overflow: ellipsis; //溢出用省略号显示
  white-space: nowrap;
}
</style>
