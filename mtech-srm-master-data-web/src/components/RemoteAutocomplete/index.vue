<template>
  <mt-multi-select
    v-if="multiple === true"
    ref="multiSelectRef"
    v-model="value"
    v-bind="$attrs"
    v-on="$listeners"
    :show-clear-button="clearable"
    :data-source="options"
    :fields="{ text: 'label', value: fields.value }"
    :placeholder="placeholder"
    :allow-filtering="true"
    :filtering="filterMethodThrottle"
    filter-type="Contains"
    :show-drop-down-icon="true"
    @change="change"
    @select="select"
    @removed="removed"
    @open="open"
    @close="close"
  ></mt-multi-select>
  <mt-select
    v-else
    v-model="value"
    :show-clear-button="clearable"
    :data-source="options"
    :fields="{ text: 'label', value: fields.value }"
    :placeholder="placeholder"
    :allow-filtering="true"
    :filtering="filterMethodThrottle"
    filter-type="Contains"
    @change="change"
    @open="open"
    @close="close"
    v-bind="$attrs"
    v-on="$listeners"
  >
  </mt-select>
</template>

<script>
import Vue from 'vue'
import { API } from '@mtech-common/http'
import { CheckBoxPlugin } from '@syncfusion/ej2-vue-buttons'
import { cloneDeep, debounce } from 'lodash'

Vue.use(CheckBoxPlugin)

const SELECTALL_LIMIT = 100

export default {
  name: 'RemoteAutocomplete',
  model: {
    prop: 'modelVal',
    event: 'syncChange'
  },
  props: {
    modelVal: {
      type: [String, Number, Array],
      default: null
    },
    dataSource: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 数据源匹配对象
    fields: {
      type: Object,
      default: () => {
        return {
          text: 'text',
          value: 'value'
        }
      }
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 请求url
    url: {
      type: String,
      default: ''
    },
    // 请求方法
    method: {
      type: String,
      default: 'post'
    },
    // 请求传入的参数
    params: {
      type: [Object, Array],
      default: () => null
    },
    // 输入框查询的key值
    paramsKey: {
      type: String,
      default: 'fuzzyParam'
    },
    // 通过rules查询时候的查询字段
    searchFields: {
      type: Array,
      default: () => null
    },
    recordsPosition: {
      type: String,
      default: 'data.records'
    },
    // 额外的rules params
    ruleParams: {
      type: Array,
      default: () => {
        return []
      }
    },
    placeholder: {
      type: String,
      default: null
    },
    clearable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      options: [], // 数据源
      dataLimit: 20, //查询条数
      page: 1, //当前查询页数
      total: 0, //总条数
      checkedList: [], //多选缓存当前list
      isFill: false, //数据回填标识，初始value回填时判断
      observe: null,
      queryText: null //当前下拉列表的查询条件
    }
  },
  computed: {
    headerTemplate() {
      const parentVm = this
      const headerVue = Vue.component('headerTemplate', {
        template: `<div class="select-all-container"><ejs-checkbox v-model="checked" :checked="checked" @change="toggleSelectAll" label='全选' /></div>`,
        data() {
          return {
            checked: false
          }
        },
        methods: {
          toggleSelectAll(e) {
            if (e.checked && parentVm.options?.length > SELECTALL_LIMIT) {
              this.$toast({
                content: '当前数据过多，无法全选，请输入更多内容以缩小数据范围',
                type: 'warning'
              })
              this.$nextTick(() => {
                this.checked = false
              })
              return
            }
            parentVm.$refs.multiSelectRef.ejsRef.selectAll(e.checked)
          }
        }
      })
      return function () {
        return {
          template: headerVue
        }
      }
    },
    value: {
      get() {
        return this.modelVal
      },
      set(val) {
        this.$emit('syncChange', val)
      }
    },
    // 防抖
    filterMethodThrottle() {
      let time = null
      if (this.dataSource.length !== 0) return null
      return (query) => {
        if (time) {
          clearTimeout(time)
        }
        time = setTimeout(() => {
          this.remoteMethod(query, 'filter')
          clearTimeout(time)
        }, 500)
      }
    }
  },
  watch: {
    dataSource: {
      handler(val) {
        if (val) {
          this.options = this.arrSet([...val])
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      // 初始modelValue非空，则回填select数据
      this.modelVal && (this.isFill = true)
      this.modelVal?.length === 0 && (this.isFill = false)

      if (this.dataSource.length === 0) this.remoteMethod(this.modelVal || '', 'init')
    },
    remoteMethod(e = { text: '' }, type) {
      if (type === 'filter') {
        this.page = 1
      }
      // 非url接口请求，直接赋值
      if (!this.url) {
        this.options = []
        return
      }
      /**
       * 目前有两种形式的传参
       * 1.通过paramsKey入参 default
       * 2.通过rules入参 需传递searchFields: ['supplierCode', 'supplierName'],
       */
      const params = this.getParams(e)
      this.isFill = false //初始话选然后置为false
      API[this.method](this.url, params || {}).then((resbonse) => {
        if (resbonse.code === 200 && resbonse.data) {
          const res = cloneDeep(resbonse)
          let _total = res.data.total
          let _option = []
          const _dataList = this.deepGet(res, this.recordsPosition || 'data.records')
          let _records = _dataList ? [...this.checkedList, ..._dataList] : [...this.checkedList]
          //如果type类型是add，options 追加数据
          this.total = _total
          _option = type === 'add' ? [...this.options, ..._records] : [..._records]
          // 数据去重
          this.options = this.arrSet(_option)
          if (type === 'init') {
            this.checkedList = this.options.filter((i) => {
              if (this.modelVal?.includes(i.code)) {
                return i
              }
            })
          }
          this.queryText = e.text // 缓存当前的查询条件
          this.$nextTick(() => {
            if (e.updateData && typeof e.updateData == 'function') {
              e.updateData(this.options)
            }
            // 初始加载组件时候不作监听
            if (type !== 'init' && Number(_total) > this.dataLimit) {
              // loadMore在数据更新后会被删除，需要重新监听
              this.addTargetListener()
            }
          })
        }
      })
    },
    // 级联获取对象数据，如获取data.records数据
    deepGet(obj, keys, defaultVal) {
      return keys.split(/\./).reduce((o, j) => (o || {})[j], obj) || defaultVal
    },
    // 去重 & 拼接label - value
    arrSet(arr) {
      let obj = {}
      const res = arr.reduce((setArr, item) => {
        let _field = item[this.fields.value]
        let _label = this.generateLabel(item)
        if (!obj[_field]) {
          obj[_field] = true
          item.label = _label //拼接codeValue
          setArr.push(item)
        } else {
          let _findIndex = setArr.findIndex((i) => i[_field] == _field)
          setArr[_findIndex] = {
            ...setArr[_findIndex],
            ...item,
            label: _label //拼接codeValue
          }
        }
        return setArr
      }, [])
      return res
    }, // 生成label (目前支持code-value形式)
    generateLabel(data) {
      let _text = this.fields.text.split('-')
      let _label =
        _text?.length > 1
          ? data[_text[0]] + '-' + data[_text[1]]
          : data[this.fields.value] + ' - ' + data[this.fields.text]
      return _label
    },
    // 获取入参条件
    getParams(e) {
      // 1.通过rules查询
      if (this.searchFields) {
        let searchRules = this.getSearchRules(e.text)
        return {
          condition: 'and',
          page: { current: this.page, pages: 1, size: this.dataLimit },
          rules: [...searchRules, ...this.ruleParams] //拼接额外的ruleParams
        }
      }
      // 2.通过paramsKey查询数据
      return {
        ...this.params,
        page: { current: this.page, pages: 1, size: this.dataLimit },
        [this.paramsKey]: this.isFill ? this.modelVal : e.text
        // ...searchRules[0],
      }
    },
    // 获取rule入参
    getSearchRules(value) {
      let searchFields = this.searchFields
      let rules = []
      if (this.isFill) {
        // 初始数据回填时候根据value定义的字段拼接值进行查询
        rules.push({
          condition: 'or',
          label: '',
          field: this.fields.value, //根据交互的字段精准查询
          type: 'string',
          operator: 'in',
          value: this.modelVal
        })
      } else if (searchFields) {
        // 拼接查询字段
        searchFields.forEach((item) => {
          rules.push({
            condition: 'or',
            label: '',
            field: item,
            type: 'string',
            operator: 'contains',
            value: value || ''
          })
        })
      }
      // 合并rules params并返回
      return [
        {
          condition: 'and',
          rules
        }
      ]
    },
    // change 事件
    change(e) {
      this.$emit('change', e.itemData)
    },
    // 多选 - 选择
    select(val) {
      this.checkedList.push(val?.itemData)
    },
    // 多选 - 移除
    removed(val) {
      this.checkedList = this.checkedList.filter((item) => item.id != val.itemData.id)
    },
    // 下拉打开列表事件
    open() {
      setTimeout(() => {
        this.initScroll()
        if (Number(this.total) > this.dataLimit) {
          this.addTargetListener()
        }
        let _inputEl = document.querySelector('.e-multi-select-list-wrapper .e-input-filter')
        // 针对特殊情况不触发keyup事件
        _inputEl.addEventListener(
          'input',
          debounce(() => {
            this.remoteMethod({ text: _inputEl.value }, 'filter')
            this.queryText = _inputEl.value
          }, 500)
        )
      }, 1000)
    },
    // 下拉关闭列表事件
    close() {
      this.observe && this.observe.disconnect()
    },
    /**---------------------------下拉滚动逻辑--------------------- */
    // 下拉滚动初始化
    initScroll() {
      let parent = document.querySelector('.e-multi-select-list-wrapper .e-content.e-dropdownbase')
      this.observe = new IntersectionObserver(
        (entries) => {
          entries.forEach((item) => {
            // dom显示，判断触底
            if (item.isIntersecting) {
              this.page++
              this.remoteMethod({ text: this.queryText }, 'add')
              this.observe.unobserve(item.target)
            }
          })
        },
        { root: parent }
      )
    },
    // 列表尾部添加target DOM元素
    setObserveTarget() {
      let oUl = document.querySelector(
        this.multiple
          ? '.e-multi-select-list-wrapper .e-content.e-dropdownbase'
          : '.e-popup .e-content.e-dropdownbase'
      ) // 列表ul
      let cP = document.createElement('p')
      cP.id = 'loadMore'
      cP.style.textAlign = 'center'
      cP.style.lineHeight = '30px'
      cP.textContent = '加载更多...'
      oUl && oUl.appendChild(cP)
    },
    // 监听target
    addTargetListener() {
      this.setObserveTarget() // 追加li节点
      const dom = document.getElementById('loadMore')
      if (dom) this.observe.observe(dom)
    }
  }
}
</script>

<style lang="scss">
.select-all-container {
  height: 32px;
  display: flex;
  align-items: center;
  padding-left: 16px;
}
</style>
