// 下面三个列上的过滤用
import { createElement } from '@syncfusion/ej2-base'
import { DataManager } from '@syncfusion/ej2-data'
import { DropDownList } from '@syncfusion/ej2-dropdowns'
import { TextBox } from '@syncfusion/ej2-inputs'
import { utils } from '@mtech-common/utils'
import Vue from 'vue'
import ComonColumnTemplate from '../../pluginColumnTemplate/src/main.vue'
import { API } from '@mtech-common/http'
import { adaptiveInit, adaptive, removeResizeListener } from '../../table-height/adaptive'

const saveUserMemory = '/lowcodeWeb/tenant/user-memory/save'
const hasOwnKey = (obj, key) => {
  return Object.hasOwnProperty.call(obj, key)
}

export default {
  props: {
    dataSource: {
      type: Array,
      default: () => {
        return []
      },
    },
    asyncConfig: {
      type: Object,
      default() {
        return {}
      },
    },
    columnData: {
      type: Array,
      default: () => {
        return []
      },
    },
    gridLines: {
      type: String,
      default: 'Horizontal', // None,Both,Horizontal,Vertical,Default
    },
    allowSelection: {
      type: Boolean,
      default: true,
    },
    selectionSettings: {
      type: Object,
      default() {
        return {
          type: 'Multiple',
          checkboxOnly: true,
        }
      },
    },
    pageSettings: {
      type: Object,
      default() {
        return {
          currentPage: 1,
          pageSize: 20,
          pageSizes: [10, 20, 50, 100, 200],
          totalRecordsCount: 0,
        }
      },
    },
    allowReordering: {
      type: Boolean,
      default: true,
    },
    allowResizing: {
      type: Boolean,
      default: true,
    },
    allowPaging: {
      type: Boolean,
      default: true,
    },
    allowSorting: {
      type: Boolean,
      default: true,
    },
    allowFiltering: {
      type: Boolean,
      default: false,
    },
    allowGrouping: {
      type: Boolean,
      default: false,
    },
    filterSettings: {
      type: Object,
      default: null,
    },
    frozenColumns: {
      type: Number,
      required: false,
      default: 0,
    },
    ignoreColumns: {
      type: Array,
      required: false,
      default: () => {
        return []
      },
    },
    childMapping: {
      type: String,
      default: 'subtasks',
    },
    autoWidthColumns: {
      type: Number,
      required: false,
      default: 5,
    },
    allowExcelExport: {
      type: Boolean,
      default: true,
    },
    useRowSelect: {
      type: Boolean,
      default: true,
    },
    tabsIndex: {
      type: Number,
      default: 0,
    },
    /** 是否开启自适应高度，默认开启 */
    isAdaptiveHeight: {
      type: Boolean,
      default: true,
    },
    enableVirtualization: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      gridId: `grid-${parseInt(Math.random(10000) * 10000)}`,
      asyncDataList: [],
      queryBuilderRules: null,
      gridHeightCfg: {
        height: 'auto',
      },
      localPagination: false, // 前端本地处理分页
      asyncParams: {},
      pageSetting: {
        ...{
          currentPage: 1,
          pageSize: 20,
          pageSizes: [10, 20, 50, 100, 200],
          totalRecordsCount: 0,
        },
        ...this.pageSettings,
      },
      selectionColumn: {
        type: 'checkbox',
        width: 50,
        allowFiltering: false, // checkbox列，不参与数据筛选
        allowResizing: false, // checkbox列，不参与列宽变化
        allowSorting: false, // checkbox列，不参与排序变化
        allowEditing: false, // 序号列，不参与数据编辑
        ignore: true, // checkbox列，不参与数据筛选
      },
      lineIndexColumn: {
        field: 'lineIndexComponent',
        headerText: this.$t('序号'),
        allowFiltering: false, // 序号列，不参与数据筛选
        allowResizing: true, // 序号列，不参与列宽变化
        allowReordering: false, // 序号列，不参与列顺序变化
        allowSorting: false, // 序号列，不参与排序变化
        allowEditing: false, // 序号列，不参与数据编辑
        ignore: true, // 序号列，不参与数据筛选
        width: 65,
        template: function () {
          return {
            template: Vue.component('actionOption', {
              template: '<div>{{+data.index+1}}</div>',
              data() {
                return { data: {} }
              },
            }),
          }
        },
      },
    }
  },
  computed: {
    isSetAdativeHeight() {
      let res = true
      if (this.$attrs.height) {
        res = false
        this.gridHeightCfg.height = this.$attrs.height
      }
      return res
    },
    dynamicVirtualization() {
      if (this.gridHeightCfg.height === 'auto') return false
      return this.enableVirtualization && (this.gridHeightCfg.height ? true : false)
    },
    gridData() {
      const _config = this.asyncConfig
      if (_config?.url) {
        // 存在aync配置，并且存在url，异步获取数据
        return this.asyncDataList
      } else {
        // 使用dataSource,不在组件内异步处理数据
        return this.dataSource
      }
    },
    // 序列化Grid的Column字段参数
    getGridColumnData() {
      const column = utils.cloneDeep(this.columnData)
      // 移除autoWidth的判断，‘设置列’，调整列顺序且存在‘冻结列’时，冻结列会值为auto  前端列，看起来是消失了
      // const _autoWidth = 'auto'
      // if (column.length >= this.autoWidthColumns) {
      //   _autoWidth = '150'
      // }
      const _attr = utils.cloneDeep(this.$attrs)
      if (typeof _attr?.lineSelection === 'boolean' && _attr?.lineSelection) {
        if (Array.isArray(column) && column.length) {
          column.splice(0, 0, this.selectionColumn)
        }
      } else if (typeof _attr?.lineSelection === 'number') {
        if (Array.isArray(column) && column.length) {
          column.splice(_attr?.lineSelection, 0, this.selectionColumn)
        }
      } else if (typeof _attr?.lineSelection === 'string' && _attr?.lineSelection === 'end') {
        if (Array.isArray(column) && column.length) {
          column.splice(column.length, 0, this.selectionColumn)
        }
      }
      if (typeof _attr?.lineIndex === 'boolean' && _attr?.lineIndex) {
        if (Array.isArray(column) && column.length) {
          if (column[0]?.type === 'checkbox') {
            column.splice(1, 0, this.lineIndexColumn)
          } else {
            column.splice(0, 0, this.lineIndexColumn)
          }
        }
      } else if (typeof _attr?.lineIndex === 'number') {
        if (Array.isArray(column) && column.length) {
          column.splice(_attr?.lineIndex, 0, this.lineIndexColumn)
        }
      } else if (typeof _attr?.lineIndex === 'string' && _attr?.lineIndex === 'end') {
        if (Array.isArray(column) && column.length) {
          column.splice(column.length, 0, this.lineIndexColumn)
        }
      }

      const columns = utils.cloneDeep(column)
      if (this.$attrs.freezeOperationColumn) {
        let _lastCellTools = []
        columns.forEach((_col) => {
          if (_col?.cellTools && Array.isArray(_col?.cellTools)) {
            _lastCellTools = _lastCellTools.concat(_col?.cellTools)
            _col.cellTools = []
          }
        })
        if (_lastCellTools.length) {
          const _col = {
            field: 'lastFixedColumn',
            headerText: this.$t('操作'),
            textAlign: 'Center',
            freeze: 'Right',
            width: '200',
            cellTools: _lastCellTools,
          }
          columns.push(_col)
        }
      }

      columns.forEach((_col, i) => {
        const _tabIndex = this.$parent.$parent.currentTabIndex
        const _contentConfig = this.$parent.$parent.contentConfig[_tabIndex]
        let _gridMemory = {}
        if (_contentConfig?.gridId) {
          _gridMemory = { ...JSON.parse(sessionStorage.getItem(_contentConfig.gridId)) }
        }
        if (_gridMemory?.colWidth && _gridMemory.colWidth[_col.field]) {
          _col.width = _gridMemory.colWidth[_col.field].toString().replace('px', '')
        } else if (_col?.headerText && !Object.hasOwnProperty.call(_col, 'width')) {
          if (_col.headerText.includes('行号') || _col.headerText.includes('序号')) {
            _col.width = '65'
          } else if (
            _col.headerText === '币种' ||
            _col.headerText === '国家' ||
            _col.headerText === '省份' ||
            _col.headerText === '城市' ||
            _col.headerText.slice(-1) === '人'
          ) {
            _col.width = '100'
          } else if (
            _col.headerText.includes('品类编') ||
            _col.headerText.includes('工厂编') ||
            _col.headerText.includes('状态')
          ) {
            _col.width = '120'
          } else if (
            _col.headerText.includes('联系电话') ||
            _col.headerText.includes('联系方式') ||
            _col.headerText.includes('品类名')
          ) {
            _col.width = '140'
          } else if (_col.headerText.includes('税率')) {
            _col.width = '160'
          } else {
            _col.width = '200'
          }
        }
        if (
          _col?.headerText &&
          _col?.width &&
          Number(_col.width.toString().replace('px', '')) > 10 &&
          !Object.hasOwnProperty.call(_col, 'minWidth')
        ) {
          _col.minWidth = '50'
        }
        _col.type = _col?.type || 'string'
        if (
          (Object.hasOwnProperty.call(_col, 'valueConverter') &&
            _col.valueConverter.type !== 'map') ||
          Object.hasOwnProperty.call(_col, 'valueAccessor')
        ) {
          _col.allowFiltering = false
        }
        if (!Object.hasOwnProperty.call(_col, 'allowGlobalSorting')) {
          _col.allowGlobalSorting = false
        }
        // 处理单元格通用模板
        if (_col?.cellTools || _col?.valueConverter || _col?.cssClass) {
          // cellTools、cssClass、valueConverter参数 用ComonColumnTemplate处理
          _col.template = this.serializeCommonColumnTemplate(_col, i)
        }

        if (_col?.valueConverter) {
          if (_col.valueConverter?.map) {
            this.serializeColumnFilter(_col)
          } else if (_col.valueConverter?.type === 'function') {
            _col.filter = this.converterColFilter(_col)
          }
          // 处理普通列
        } else {
          _col.filter = this.converterColFilter(_col)
        }
      })

      return columns
    },
    // 是否存在冻结列
    hasFrozenColumn() {
      // grid中设置了frozenColumns
      if (this.frozenColumns > 0) {
        return true
      }
      //  else if (this.columnData[0]?.type === 'checkbox' && this.frozenColumns < 1) {
      //   this.frozenColumns = 1
      //   return true
      // }
      // column中设置了isFrozen
      let _hasFrozon = false
      for (let i = 0; i < this.columnData; i++) {
        if (this.columnData[i].isFrozen) {
          _hasFrozon = true
          break
        }
      }
      return _hasFrozon
    },
    serializeFilterSetting() {
      if (this.filterSettings) {
        return this.filterSettings
      } else {
        return {
          type: 'Menu',
          operators: {
            stringOperator: [
              { text: this.$t('包含'), value: 'contains' },
              { text: this.$t('等于'), value: 'equal' },
              { text: this.$t('不等于'), value: 'notequal' },
            ],
            numberOperator: [
              { text: this.$t('等于'), value: 'equal' },
              { text: this.$t('不等于'), value: 'notequal' },
              { text: this.$t('大于'), value: 'greaterthan' },
              { text: this.$t('大于等于'), value: 'greaterthanorequal' },
              { text: this.$t('小于'), value: 'lessthan' },
              { text: this.$t('小于等于'), value: 'lessthanorequal' },
            ],
            dateOperator: [
              { text: this.$t('包含'), value: 'contains' },
              { text: this.$t('不等于'), value: 'notequal' },
              { text: this.$t('大于'), value: 'greaterthan' },
              { text: this.$t('大于等于'), value: 'greaterthanorequal' },
              { text: this.$t('小于'), value: 'lessthan' },
              { text: this.$t('小于等于'), value: 'lessthanorequal' },
            ],
            booleanOperator: [{ text: this.$t('等于'), value: 'equal' }],
            selectOperator: [
              { text: this.$t('等于'), value: 'equal' },
              { text: this.$t('不等于'), value: 'notequal' },
            ],
          },
        }
      }
    },
  },
  watch: {
    dataSource: {
      handler(n, o) {
        const _config = this.asyncConfig
        if (_config?.url) {
          // 存在aync配置，并且存在url，异步获取数据
          // return this.asyncDataList;
        } else {
          // 直接使用dataSource，赋值表格数据
          this.defineGridUseDataSource(n)
          // editFieldCacheMapGenerator(this.columnData, this.$attrs['edit-settings'] || this.$attrs.editSettings)
        }
      },
      immediate: true,
      deep: true,
    },
    asyncConfig: {
      handler(n, o) {
        if (n?.url) {
          // 存在aync配置，并且存在url，异步获取数据
          const { isUseCustomSearch, isCustomSearchRules } = this.$attrs
          // 如果是自定义规则但是用了JD的入参规则
          const isCustomSearchButJDRules = isUseCustomSearch && !isCustomSearchRules
          let rules = isCustomSearchButJDRules ? this.getCustomSearchRules() : null
          this.loadGridDataUseQueryBuilder(rules, 'auto') // 调整数据重新获取的逻辑，页码重置,queryBuilder 清空过滤条件
        } else {
          // 直接使用dataSource，赋值表格数据
          this.defineGridUseDataSource(this.dataSource)
        }
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {
    if (!this.asyncConfig?.url) {
      // 使用dataSource,不在组件内异步处理数据
      this.defineGridUseDataSource(this.gridData)
    }
    // 当不传入height设定时就做自适应计算
    if (!this.$attrs.height) this.handleAdaptive()
  },
  beforeDestroy() {
    removeResizeListener(this.$refs.gridRef.$el)
  },
  methods: {
    handleAdaptive(bottomOffset = 100) {
      setTimeout(() => {
        adaptiveInit(this.$refs.gridRef.$el, this.gridHeightCfg, bottomOffset)
        adaptive(this.$refs.gridRef.$el, this.gridHeightCfg, bottomOffset)
      }, 20)
    },
    refreshTableHeight() {},
    // 设置自定义快捷查询的规则
    getCustomSearchRules() {
      const searchFormModel = this.searchFormModelList[this.tabsIndex]
      const { customMatchRules = {} } = this.$attrs
      const ruleList = []
      for (let key in searchFormModel) {
        if (Object.hasOwnProperty.call(searchFormModel, key)) {
          if (!searchFormModel[key]) continue // 没有值的字段忽略
          ruleList.push({
            field: key,
            operator: customMatchRules[key] || 'contains',
            value: searchFormModel[key],
          })
        }
      }
      return {
        form: searchFormModel,
        rules: {
          condition: 'and',
          rules: ruleList,
        },
      }
    },
    handleResizeStop(e) {
      const _tabIndex = this.$parent.$parent.currentTabIndex
      const _contentConfig = this.$parent.$parent.contentConfig[_tabIndex]
      if (Object.hasOwnProperty.call(_contentConfig, 'gridId')) {
        const _gridMemory = JSON.parse(sessionStorage.getItem(_contentConfig.gridId))
        if (!_gridMemory) return
        if (!_gridMemory?.colWidth) {
          _gridMemory.colWidth = {
            [e.column.field]: e.column.width,
          }
        } else {
          _gridMemory.colWidth[e.column.field] = e.column.width
        }
        sessionStorage.setItem(_contentConfig.gridId, JSON.stringify(_gridMemory))
        API.post(saveUserMemory, { gridId: _contentConfig.gridId, gridMemory: _gridMemory })
      }
    },
    // 加载数据，通过queryBuilder
    loadGridDataUseQueryBuilder(rules = null, toggleType) {
      this.queryBuilderRules = rules
      this.pageSetting.currentPage = 1 // 页码重置： queryBuilder 过滤查询、清空查询
      this.loadGridData(toggleType)
    },
    // 刷新列表数据
    refreshGridData() {
      if (this.$attrs.isUseCustomSearch) this.queryBuilderRules = this.getCustomSearchRules()
      this.loadGridData()
    },
    // 重新获取列表数据
    reloadGridData() {
      this.pageSetting.currentPage = 1 // 分页参数，页码重置
      this.pageSetting.pageSize = 20
      this.loadGridData()
    },
    async loadGridData(toggleType) {
      if (this.enableVirtualization) {
        this?.$refs?.gridRef?.ejsRef?.clearSelection()
      }
      const { currentPage, pageSize, orders } = this.pageSetting
      let _url = this.asyncConfig?.url
      if (_url) {
        const _methods = this.asyncConfig?.methods || 'post'
        let _useLoading = true
        let params = {}

        if (this.allowPaging || this.enableVirtualization) {
          params.page = { current: currentPage, size: pageSize, orders }
        }
        // 使用自定义快捷查询以及自定义查询规则
        if (this.$attrs.isCustomSearchRules) {
          params = {
            ...params,
            ...this.searchFormModelList[this.tabsIndex],
          }
        } else if (this.queryBuilderRules) {
          params = { ...params, ...this.queryBuilderRules }
        }
        if (hasOwnKey(this.asyncConfig, 'loading')) {
          // 配置中单独配置了loading
          _useLoading = this.asyncConfig.loading
        }
        if (_useLoading) {
          this.$emit('handleToggleLoading', true)
        }
        if (hasOwnKey(this.asyncConfig, 'params')) {
          // 配置中单独配置入参params
          params = { ...params, ...this.asyncConfig.params }
        }
        if (hasOwnKey(this.asyncConfig, 'query') && (_methods === 'post' || _methods === 'put')) {
          // 配置中单独配置入参query
          _url = `${_url}?${this.toQueryParams(this.asyncConfig.query)}`
        }
        if (hasOwnKey(this.asyncConfig, 'condition')) {
          // 配置中单独配置入参condition
          params.condition = this.asyncConfig.condition
        }
        if (hasOwnKey(this.asyncConfig, 'defaultRules')) {
          // 配置中单独配置入参defaultRules
          params.defaultRules = this.asyncConfig.defaultRules
        }
        if (hasOwnKey(this.asyncConfig, 'page')) {
          // 配置中单独配置入参page
          const _page = { ...this.asyncConfig.page, ...params.page }
          params.page = _page
        }
        if (hasOwnKey(this.asyncConfig, 'pageFlag')) {
          // 配置中单独配置入参pageFlag
          params.pageFlag = this.asyncConfig.pageFlag
        }
        // if (hasOwnKey(this.asyncConfig, 'rules')) { // 配置中单独配置入参rules
        //   params.rules = this.asyncConfig.rules
        // }
        if (hasOwnKey(this.asyncConfig, 'rules')) {
          // 配置中单独配置入参rules
          if (hasOwnKey(params, 'rules') && Array.isArray(params.rules)) {
            // 当前params已有rules
            // params.rules = params.rules.concat(this.asyncConfig.rules)
          } else {
            params.rules = this.asyncConfig.rules
          }
        }
        if (hasOwnKey(this.asyncConfig, 'queryBuilderWrap')) {
          // 配置中定义了queryBuilder包裹容器
          const _queryBuilder = {}
          if (hasOwnKey(params, 'condition')) {
            // 解析参数condition
            _queryBuilder.condition = params.condition
            delete params.condition
          }
          if (hasOwnKey(params, 'defaultRules')) {
            // 解析参数defaultRules
            _queryBuilder.defaultRules = params.defaultRules
            delete params.defaultRules
          }
          if (hasOwnKey(params, 'page')) {
            // 解析参数page
            _queryBuilder.page = params.page
            delete params.page
          }
          if (hasOwnKey(params, 'pageFlag')) {
            // 解析参数pageFlag
            _queryBuilder.pageFlag = params.pageFlag
            delete params.pageFlag
          }
          if (hasOwnKey(params, 'rules')) {
            // 解析参数rules
            _queryBuilder.rules = params.rules
            delete params.rules
          }
          params[this.asyncConfig.queryBuilderWrap] = _queryBuilder
        }

        if (hasOwnKey(this.asyncConfig, 'params')) {
          // 配置中单独配置入参params   且params是Array类型  强制替换
          if (Array.isArray(this.asyncConfig.params)) {
            params = this.asyncConfig.params
          }
        }
        const _recordsPosition = this.asyncConfig?.recordsPosition || 'data.records'
        let _totalPosition = 'data.total'
        if (hasOwnKey(this.asyncConfig, 'totalPosition')) {
          // 配置中定义了totalPosition
          _totalPosition = this.asyncConfig.totalPosition
        } else if (_recordsPosition !== 'data.records') {
          // 兼容之前的recordsPosition
          // 如果recordsPosition=data.dataDTO.records 那么totalPosition =data.dataDTO.total
          if (_recordsPosition.indexOf('.') > -1) {
            const _split = _recordsPosition.split('.')
            _split[_split.length - 1] = 'total'
            _totalPosition = _split.join('.')
          } else {
            _totalPosition = 'total'
          }
        }
        if (hasOwnKey(this.asyncConfig, 'localPagination')) {
          // 配置中定义了localPagination
          this.localPagination = this.asyncConfig.localPagination
        }
        let _axiosConfig = {}
        if (hasOwnKey(this.asyncConfig, 'axiosConfig')) {
          // 配置中定义了axiosConfig
          _axiosConfig = this.asyncConfig.axiosConfig
        }
        // 配置了默认不加载 且 没有输入任何查询调价 且 非自定义查询 不去发起查询接口请求
        const isNotAutoQueryOldType =
          this.asyncConfig.ignoreDefaultSearch &&
          (!params?.rules || params.rules.length === 0) &&
          !this.$attrs.isCustomSearchRules
        // 配置了默认不加载 且 非手动查询 且 自定义查询 不去发起查询接口请求
        const isNotAutoQueryNewType =
          this.asyncConfig.ignoreDefaultSearch &&
          this.$attrs.isUseCustomSearch &&
          toggleType !== 'handle'
        if (isNotAutoQueryOldType || isNotAutoQueryNewType) {
          if (_useLoading) {
            // 关闭loading
            this.$emit('handleToggleLoading', false)
          }
          this.$set(this, 'asyncDataList', [])
          return
        }
        this.asyncParams = params
        try {
          // 如果是本地mock数据则将baseURL清空，不然没法proxy
          if (_url.includes('mock-api')) {
            _axiosConfig.baseURL = ''
          }
          const res = await API[_methods](`${_url}`, params, _axiosConfig)
          if (_useLoading) {
            this.$emit('handleToggleLoading', false)
          }
          if (res.code === 200) {
            // this.pageSetting.totalRecordsCount = +this.deepGet(res, _totalPosition)
            let _dataList = []
            _dataList = this.deepGet(res, this.asyncConfig?.recordsPosition || 'data.records')
            _dataList = Array.isArray(_dataList) ? _dataList : []
            if (
              _dataList &&
              this.asyncConfig?.serializeList &&
              typeof this.asyncConfig.serializeList === 'function'
            ) {
              // 需要执行数据序列化
              _dataList = this.asyncConfig.serializeList(_dataList)
            }
            if (
              _dataList &&
              this.asyncConfig?.asyncSerializeList &&
              typeof this.asyncConfig.asyncSerializeList === 'function'
            ) {
              // 需要执行'异步'数据序列化
              _dataList = await this.asyncConfig.asyncSerializeList(_dataList)
            }
            if (_dataList && this.asyncConfig?.transform) {
              // 需要转化为树型结构
              _dataList = this.serializeDataToTree(_dataList, this.asyncConfig)
            }
            _dataList = Array.isArray(_dataList) ? _dataList : []
            this.asyncDataList = []
            this.$nextTick(() => {
              this.$set(this, 'asyncDataList', _dataList)
              this.setFrozenPlaceHolder(this.gridData)
              const _total = +this.deepGet(res, _totalPosition)
              if (!isNaN(_total)) {
                // 总条数，是数字类型
                this.pageSetting.totalRecordsCount = _total
              } else {
                // 不是数字类型，则前端本地分页
                this.pageSetting.totalRecordsCount = _dataList.length
                this.localPagination = true
              }
              if (typeof this.asyncConfig.afterAsyncData === 'function') {
                // 需要执行数据获取后的一些逻辑操作
                this.asyncConfig.afterAsyncData(res)
              }
              this.$nextTick(() => {
                this?.$refs?.gridRef?.ejsRef?.refresh()
              })
            })
          } else {
            this.$set(this, 'asyncDataList', [])
          }
        } catch (e) {
          this.$set(this, 'asyncDataList', [])
          if (_useLoading) {
            this.$emit('handleToggleLoading', false)
          }
        }
      } else {
        this.asyncDataList = this.dataSource
        this.pageSetting.totalRecordsCount = this.dataSource.length
      }
    },
    // 级联获取对象数据，如获取data.records数据
    deepGet(obj, keys, defaultVal) {
      return keys.split(/\./).reduce((o, j) => (o || {})[j], obj) || defaultVal
    },
    toQueryParams(params) {
      const keys = Object.keys(params)
      const queryParams = []

      keys.forEach((k) => {
        queryParams.push(`${k}=${params[k]}`)
      })

      return queryParams.join('&')
    },
    serializeDataToTree(arr, config) {
      const pid = config?.parentId || 'parentId'
      const cid = this.childMapping
      var top = []
      var sub = []
      var tempObj = {}

      arr.forEach(function (item) {
        if (config?.rootTag) {
          if (item[pid] === config.rootTag) {
            // 顶级分类
            top.push(item)
          } else {
            sub.push(item) // 其他分类
          }
        } else {
          if (!item[pid]) {
            // 顶级分类
            top.push(item)
          } else {
            sub.push(item) // 其他分类
          }
        }

        item[cid] = [] // 默然添加children属性
        tempObj[item.id] = item // 用当前分类的id做key，存储在tempObj中
      })

      sub.forEach(function (item) {
        // 取父级
        const _obj = {}
        _obj[cid] = []
        var parent = tempObj[item[pid]] || _obj
        // 把当前分类加入到父级的children中
        parent[cid].push(item)
      })

      return top
    },
    // 通用的一个列模板，主要处理cellTools、cssClass、valueConverter参数
    serializeCommonColumnTemplate(_col, columnIndex) {
      const getTemplateData = (_c) => {
        const _data = {
          templateField: _c.field,
          columnIndex: columnIndex,
        }
        if (hasOwnKey(_c, 'cellTools')) {
          _data.cellTools = _c.cellTools
          _data.cssClass = 'field-content'
        }
        if (hasOwnKey(_c, 'valueConverter')) {
          _data.valueConverter = _c.valueConverter
        }
        if (hasOwnKey(_c, 'cssClass')) {
          _data.cssClass = _c.cssClass
        }
        return _data
      }
      return () => {
        return {
          template: ComonColumnTemplate,
          data: getTemplateData(_col),
        }
      }
    },

    // 处理列筛选
    serializeColumnFilter(_col) {
      const _map = _col.valueConverter.map
      if (Array.isArray(_map)) {
        const _querySeleteList = _map
        _col.type = 'string'
        _col.queryType = 'number'
        _col.querySeleteList = _querySeleteList
        _col.queryTemplate = {
          type: 'select',
          options: {
            ds: _querySeleteList,
          },
        }
        _col.filter = this.colFilter(_col)
      } else if (Object.prototype.toString.call(_map) === '[object Object]') {
        const _querySeleteList = []
        const _querySeleteData = _map
        for (const i in _querySeleteData) {
          _querySeleteList.push({ label: _querySeleteData[i], text: _querySeleteData[i], value: i })
        }
        _col.type = 'string'
        _col.queryType = 'number'
        _col.querySeleteList = _querySeleteList
        _col.queryTemplate = {
          type: 'select',
          options: {
            ds: _querySeleteList,
          },
        }
        _col.filter = this.colFilter(_col)
      }
    },
    // 处理列过滤(下拉框渲染)
    colFilter(col) {
      if (!col.querySeleteList || col.querySeleteList.length < 1) {
        return
      }
      let dropInstance = null
      const _fields = col?.valueConverter?.fields ?? { text: 'text', value: 'value' }
      const _seleteList = [...col.querySeleteList]
      const placeholder = this.$t('请选择')
      _seleteList.forEach((e) => {
        e.label = e[_fields.text]
        e.value = e[_fields.value]
      })
      return {
        ui: {
          create: function (args) {
            const flValInput = createElement('input', { className: 'flm-input' })
            args.target.appendChild(flValInput)
            dropInstance = new DropDownList({
              dataSource: new DataManager(_seleteList),
              fields: { text: 'label', value: 'value' },
              placeholder: col.palceHolder || placeholder,
              popupHeight: '200px',
            })
            dropInstance.appendTo(flValInput)
          },
          write: function (args) {
            dropInstance.value = args.filteredValue
          },
          read: function (args) {
            args.fltrObj.filterByColumn(args.column.field, args.operator, dropInstance.value)
          },
        },
      }
    },
    // 处理列过滤(valueConverter)
    converterColFilter(col) {
      let converterColInstance = null
      const inpitPlaceholder = this.$t('请输入')
      return {
        ui: {
          create: function (args) {
            const converterColInput = createElement('input', {
              className: 'flm-input',
            })
            args.target.appendChild(converterColInput)
            converterColInstance = new TextBox({
              placeholder: col.palceHolder || inpitPlaceholder,
              value: '',
              autocomplete: 'off',
            })
            converterColInstance.appendTo(converterColInput)
          },
          write: function (args) {
            converterColInstance.value = args.filteredValue ? args.filteredValue : ''
          },
          read: function (args) {
            // TODO 过滤问题
            args.fltrObj.filterByColumn(
              args.column.field,
              args.operator,
              converterColInstance.value,
            )
          },
        },
      }
    },
    // 切换页码
    currentChange(index) {
      const _config = this.asyncConfig
      if (_config?.url) {
        // 存在aync配置，并且存在url，在组件内部执行页面切换
        if (!isNaN(index)) {
          // 以下代码，和mtech-ui/page保持一致
          let number = parseInt(index)
          if (number < 1) number = 1
          if (number > this.pageSetting.totalRecordsCount) {
            number = this.pageSetting.totalRecordsCount
          }
          this.pageSetting.currentPage = number
          if (!this.localPagination) {
            // 如果不是本地处理分页，则执行异步请求
            this.loadGridData()
            // 换页时将滚动条重置
            if (this.$refs.gridRef.$el && this.$refs.gridRef.$el.querySelector('.e-content')) {
              this.$refs.gridRef.$el.querySelector('.e-content').scrollTop = 0
            }
          }
        }
      }
    },
    // 切换每页显示条数
    sizeChange(count) {
      const _config = this.asyncConfig
      if (_config?.url) {
        // 存在aync配置，并且存在url，在组件内部执行每页显示条数切换
        this.pageSetting.currentPage = 1
        this.pageSetting.pageSize = count
        if (!this.localPagination) {
          // 如果不是本地处理分页，则执行异步请求
          this.loadGridData()
          // 修改页码时将滚动条重置
          if (this.$refs.gridRef.$el && this.$refs.gridRef.$el.querySelector('.e-content')) {
            this.$refs.gridRef.$el.querySelector('.e-content').scrollTop = 0
          }
        }
      }
    },
    // 设置无数据时的表格placeHolder，包含列冻结的情况
    setFrozenPlaceHolder(newVal) {
      if (newVal && this.hasFrozenColumn) {
        const _parent = document.getElementById(this.gridId)
        if (!_parent) return
        if (newVal.length < 1 && _parent.getElementsByClassName('hasFrozenColumn').length < 1) {
          var NewNode = document.createElement('div')
          NewNode.className = 'hasFrozenColumn'
          NewNode.innerHTML = this.$t('无记录可显示')
          NewNode.style =
            'float: left;width: 100%;height: 50px; text-align: center; line-height: 50px; color: #999;border: 1px solid #e8e8e8;border-top: 0'
          if (_parent) {
            _parent.insertBefore(NewNode, _parent.getElementsByClassName('e-gridcontent')[0])
          }
        } else {
          if (_parent.getElementsByClassName('hasFrozenColumn').length > 0) {
            _parent.removeChild(_parent.getElementsByClassName('hasFrozenColumn')[0])
          }
        }
      }
    },

    dataBound() {
      if (this.autoFitColumns.length > 0) {
        this.$refs['pageGrid-' + this.configIndex].$refs.ejsRef.autoFitColumns(this.autoFitColumns)
      }
    },
    // 表格操作
    actionHandler(param) {
      if (
        param.requestType === 'sorting' &&
        param?.direction &&
        this.columnData.find((e) => e.field === param.columnName)?.allowGlobalSorting
      ) {
        const _columnqueryParams = {
          column: param.columnName.replace(/[A-Z]/g, '_$&').toLowerCase(),
          asc: param.direction === 'Ascending',
        }
        if (this.pageSetting) {
          this.pageSetting.orders = []
          this.pageSetting.orders.push(_columnqueryParams)
        }
        this.$emit('handleQuerySearch', this.queryBuilderRules)
      } else if (
        param.requestType === 'sorting' &&
        !Object.hasOwnProperty.call(param, 'direction') &&
        this.columnData.find((e) => e.field === param.columnName)?.allowGlobalSorting
      ) {
        this.pageSetting.orders = []
        this.$emit('handleQuerySearch', this.queryBuilderRules)
      }
      if (param.requestType === 'add') {
        const _parent = document.getElementById(this.gridId)
        const _elements = _parent.getElementsByClassName('hasFrozenColumn')
        if (_elements.length > 0) {
          _parent.removeChild(_elements[0])
        }
      }
      if (param.action === 'filter') {
        // 列过滤数据
        const _columnqueryParams = []
        if (param.columns && param.columns.length > 0) {
          param.columns.forEach((item) => {
            if (item.value) {
              _columnqueryParams.push({
                field: item.field,
                operator: item.operator,
                value: item.value,
              })
            }
          })
          this.queryBuilderRules = { rules: _columnqueryParams }
          // this.$refs.gridRef.ejsRef.clearFiltering()
          this.$emit('handleQuerySearch', { rules: _columnqueryParams })
        }
      } else if (param.action === 'clearFilter') {
        // 列过滤数据-重置
        const _columnqueryParams = this.queryBuilderRules?.rules ?? []
        _columnqueryParams.forEach((item, index) => {
          if (item.field === param.currentFilterColumn.field) {
            _columnqueryParams.splice(index, 1)
          }
        })
        this.queryBuilderRules = { rules: _columnqueryParams }
        this.$emit('handleQuerySearch', { rules: _columnqueryParams })
      } else if (param.requestType === 'refresh') {
        if (this.$refs?.gridRef?.ejsRef?.closeEdit) {
          this.$refs.gridRef.ejsRef.closeEdit()
        }
      }
    },
    // 使用dataSource进行表格数据渲染
    defineGridUseDataSource(data = []) {
      this.asyncDataList = data ?? []
      this.pageSetting.totalRecordsCount = data.length
      this.setFrozenPlaceHolder(data)
    },
  },
  beforeDestroy() {},
}
