<template>
  <div class="vertical-list-container mt-flex">
    <div class="column-list-close-handler"
         :class="{ 'list-closed': cardIsClosed }"
         @click="toggleColStatus">
      <mt-svg-icon class="list-closed-svg"
                   icon="Materail"></mt-svg-icon>
    </div>
    <div class="column-list-container mt-flex-direction-column"
         :class="{ 'column-list-closed': cardIsClosed }">
      <div class="list-description">
        <div class="list-title">{{listConfig.title}}</div>
        <div class="svg-option-item"
             @click="handleAddEvent">
          <mt-svg-icon icon="add"></mt-svg-icon>
          <span>{{$t('新增')}}</span>
        </div>
        <div class="query-select">
          <div class="svg-option-item">
            <mt-svg-icon icon="panel-filter"></mt-svg-icon>
            <span>{{$t('筛选')}}</span>
          </div>
          <div class="label-tags-container mt-flex">
            <div class="label-tags-item"
                 v-for="(item, index) in queryResult"
                 :key="index">
              <span>{{ item.label[listConfig.queryFields['text']] || "" }}</span>
              <span class="label-tag-symbol">{{ item.symbol }}</span>
              <span class="label-tag-name">{{ item.name }}</span>
              <mt-svg-icon class="label-tag-delete-svg"
                           icon="cross-close"
                           @click.native="handleRemoveQuery(index)"></mt-svg-icon>
            </div>
          </div>
          <div class="list-query-container">
            <mt-select class="static-element-style"
                       ref="queryLabel"
                       cssClass='ddl-inlinecss'
                       v-model="query.label"
                       :floatLabelType="'Never'"
                       :data-source="listConfig.queryDataSource"
                       :fields="listConfig.queryFields"></mt-select>
            <mt-select class="static-element-style symbol-element-style"
                       v-model="query.symbol"
                       :data-source="symbolList"></mt-select>
            <mt-input class="static-element-style"
                      v-model="query.name"></mt-input>
            <mt-svg-icon class="list-query-svg query-checkbox"
                         icon="checkbox"
                         @click.native="handleAddQuery"></mt-svg-icon>
            <mt-svg-icon class="list-query-svg"
                         icon="cross"
                         @click.native="handleResetQuery"></mt-svg-icon>
          </div>
        </div>
        <div class="svg-option-item">
          <mt-svg-icon icon="M_PV_Bookmark"></mt-svg-icon>
          <span>{{$t('标签')}}</span>
        </div>
      </div>
      <div class="list-content">
        <ul>
          <li :class="['column-list-item',{ 'list-item-active':index===selectColumnItem}]"
              v-for="(item, index) in listConfig.dataSource"
              :key="index"
              @click="handleSelectColumnItem(item, index)">
            <div class="list-item-title">{{ item[listConfig.columnFields.title] }}</div>
            <div class="list-item-field mt-flex"
                 v-for="(temp, _index) in listConfig.columnFields.columnData"
                 :key="index + '-' + _index">
              <span class="item-key">{{ temp.headerText }}</span>
              <span class="item-value">{{ item[temp.field] }}</span>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import MtSelect from '@mtech-ui/select'
import MtInput from '@mtech-ui/input'
import MtSvgIcon from './components/MtSvgIcon'

export default {
  name: 'CommonListVertical',
  props: {
    listConfig: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  components: { MtSvgIcon, MtSelect, MtInput },
  data() {
    return {
      cardIsClosed: false,
      selectColumnItem: 0,
      query: { label: '', symbol: '', name: '' },
      queryResult: [],
      symbolList: [this.$t('相同'), '=', '≥', '>', '<', '≤', this.$t('空'), this.$t('非空'), this.$t('包含'), this.$t('不包含')]
    }
  },
  mounted() { },
  computed: {},
  methods: {
    toggleColStatus() {
      this.cardIsClosed = !this.cardIsClosed
    },
    handleAddEvent() {
      this.$emit('handleAddEvent')
    },
    handleSelectColumnItem(item, index) {
      this.selectColumnItem = index
      this.$emit('handleSelectItem', item)
    },
    handleRemoveQuery(index) {
      this.$emit('handleRemoveQuery', index)
      this.queryResult.splice(index, 1)
    },
    handleAddQuery() {
      this.$emit('handleAddQuery', this.query)
      this.queryResult.push({
        label: this.$refs.queryLabel.ejsRef.getDataByValue(this.query.label),
        symbol: this.query.symbol,
        name: this.query.name
      })
      this.query = { label: '', symbol: '', name: '' }
    },
    handleResetQuery() {
      this.query = { label: '', symbol: '', name: '' }
    },
    getQueryResult() {
      return this.queryResult
    }
  }
}
</script>
<style lang="scss" scoped>
html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  /*font-size: 100%;*/
  font: inherit;
  vertical-align: baseline;
}
/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section,
main {
  display: block;
}
body {
  line-height: 1;
}
ol,
ul {
  list-style: none;
}
blockquote,
q {
  quotes: none;
}
blockquote:before,
blockquote:after,
q:before,
q:after {
  content: '';
  content: none;
}
//scrollBar-color
$scrollBar-track-color: #ffffff;
$scrollBar-thumb-color: #d8d8d8;
$scrollBar-thumb-hover-color: rgb(200, 200, 200);
$scrollBar-thumb-active-color: rgb(190, 190, 190);
//修改谷歌内核浏览器滚动条样式
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
::-webkit-scrollbar-track {
  border-radius: 2px;
  background-color: $scrollBar-track-color;
}

::-webkit-scrollbar-thumb {
  background-color: $scrollBar-thumb-color;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: $scrollBar-thumb-hover-color;
}

::-webkit-scrollbar-thumb:active {
  background-color: $scrollBar-thumb-hover-color;
}

.mt-flex {
  display: flex;
  position: relative;
}

.mt-flex-direction-column {
  display: flex;
  flex-direction: column;
  position: relative;
}
.e-float-input.e-control-wrapper.e-input-group .e-input-group-icon,
.e-float-input.e-input-group .e-input-group-icon {
  display: none;
}
.e-input + .e-input-group-icon,
.e-input-group .e-input + .e-input-group-icon,
.e-input-group.e-control-wrapper .e-input + .e-input-group-icon {
  display: none;
}
.vertical-list-container {
  margin-right: 10px;
  height: 100vh;

  .column-list-close-handler {
    width: 24px;
    height: 36px;
    background: rgba(0, 70, 156, 0.3);
    position: absolute;
    top: 90px;
    left: 400px;
    z-index: 2;
    border-left: 2px solid #00469c;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    color: #fff;
    animation: open-card-animation 0.3s ease-in;

    .list-closed-svg {
      font-size: 12px;
      margin-left: -3px;
    }

    &.list-closed {
      left: 10px;
      animation: close-card-animation 0.3s ease-out;

      .list-closed-svg {
        margin-left: 3px;
        transform: rotate(180deg);
      }
    }

    @keyframes close-card-animation {
      0% {
        left: 400px;
      }
      100% {
        left: 10px;
      }
    }
    @keyframes open-card-animation {
      0% {
        left: 10px;
      }
      100% {
        left: 400px;
      }
    }
  }
  .column-list-container {
    height: 100%;
    visibility: visible;
    width: 400px;
    overflow: hidden;
    opacity: 1;
    flex: 0 0 auto;
    background: #fff;
    position: relative;
    animation: open-list-animation 0.3s ease-in;

    .svg-option-item {
      margin-top: 20px;
    }

    &.column-list-closed {
      visibility: hidden;
      width: 0;
      opacity: 0;
      animation: close-list-animation 0.3s ease-out;
    }

    @keyframes close-list-animation {
      0% {
        visibility: visible;
        width: 400px;
        opacity: 1;
      }
      100% {
        visibility: hidden;
        width: 0;
        opacity: 0;
      }
    }
    @keyframes open-list-animation {
      0% {
        visibility: hidden;
        width: 0;
        opacity: 0;
      }
      100% {
        visibility: visible;
        width: 400px;
        opacity: 1;
      }
    }

    .list-description {
      padding: 20px;
      width: 400px;
      box-sizing: border-box;
      border-right: 1px solid rgba(232, 232, 232, 1);

      .list-title {
        font-size: 24px;
        font-weight: 500;
      }
      .label-tags-container {
        margin-top: 10px;
        flex-wrap: wrap;

        .label-tags-item {
          display: inline-block;
          width: auto;
          font-size: 14px;
          height: 20px;
          line-height: 20px;
          position: relative;
          color: #9a9a9a;
          margin-right: 20px;

          span.label-tag-name {
            color: #182b3e;
          }

          span.label-tag-symbol {
            margin: 0 4px;
          }

          .label-tag-delete-svg {
            margin-left: 4px;
            &:hover {
              color: #00469c;
            }
          }
        }
      }
      .list-query-container {
        margin-top: 10px;

        .static-element-style {
          width: 100px;
          height: 22px;
          margin-right: 5px;
        }
        .symbol-element-style {
          width: 70px;
        }
        .list-query-svg {
          margin: 0 5px;

          &.query-checkbox {
            color: #00469c;
          }
        }
      }
    }
    .list-content {
      flex: 1;
      width: 400px;
      overflow-y: scroll;

      li.column-list-item {
        padding: 6px 20px;
        border: 1px solid rgba(232, 232, 232, 1);
        .list-item-title {
          font-size: 14px;
          color: #292929;
          font-weight: 500;
          height: 25px;
          line-height: 25px;
        }

        .list-item-field {
          height: 20px;
          line-height: 20px;
          margin: 6px 0;
          .item-key {
            width: 70px;
            font-size: 10px;
            color: #9a9a9a;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .item-value {
            flex: 1;
            color: #292929;
            padding-left: 6px;
          }
        }

        &.list-item-active {
          border-left: none;
          position: relative;
          &:before {
            content: '';
            height: 100%;
            width: 0;
            position: absolute;
            border-left: 2px solid #00469c;
            left: 0;
            top: 0;
            animation: list-item-active-animation 0.2s ease;
          }
          @keyframes list-item-active-animation {
            0% {
              top: 50%;
              height: 0;
            }
            100% {
              top: 0;
              height: 100%;
            }
          }
        }
      }
    }
  }
}
</style>
