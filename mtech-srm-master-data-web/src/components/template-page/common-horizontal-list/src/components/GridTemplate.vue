<template>
  <div :class="['grid-container', { 'grid-no-page-setting': !allowPaging }]">
    <mt-data-grid
      v-bind="$attrs"
      v-on="$listeners"
      :class="[hasFrozenColumn && `hasFrozen`]"
      :id="'pageGrid-' + configIndex"
      :ref="'pageGrid-' + configIndex"
      :data-source="gridData"
      :height="gridHeight"
      :width="gridWidth"
      :row-template="rowTemplate"
      :edit-settings="editSettings"
      :column-data="getGridColumnData(columnData, configIndex)"
      @currentChange="currentChange"
      @sizeChange="sizeChange"
      :grid-lines="gridLines"
      :allow-reordering="allowResizing"
      :allow-resizing="allowResizing"
      :allow-paging="allowPaging"
      :page-settings="pageSettings"
      :allow-sorting="allowSorting"
      :sort-settings="sortSettings"
      :allow-selection="allowSelection"
      :selection-settings="selectionSettings"
      :frozen-columns="frozenColumns"
      :show-column-chooser="true"
      :row-data-bound="rowDataBound"
      :query-cell-info="queryCellInfo"
      :data-bound="dataBound"
      :allow-filtering="allowFiltering"
      :filter-settings="filterOptions"
      :action-begin="actionHandler"
    />
  </div>
</template>
<script>
// 下面三个列上的过滤用
import { DropDownList } from '@syncfusion/ej2-dropdowns'
import { createElement } from '@syncfusion/ej2-base'
import { DataManager } from '@syncfusion/ej2-data'

import MtDataGrid from '@mtech-ui/data-grid'
import ComonColumnTemplate from './ColumnCommonTemplate.vue'
import ColumnCheckboxTemplate from './ColumnCheckboxTemplate.vue'
import ColumnDropdownTemplate from './ColumnDropdownTemplate.vue'
export default {
  name: 'GridTemplate',
  props: {
    dataSource: {
      type: Array,
      default: () => {
        return []
      }
    },
    columnData: {
      type: Array,
      default: () => {
        return []
      }
    },
    gridHeight: {
      type: String,
      default: '100%'
    },
    gridWidth: {
      type: String,
      default: 'auto'
    },
    configIndex: {
      type: Number,
      default: 0
    },
    allowSelection: {
      type: Boolean,
      required: false,
      default: true
    },
    allowEditing: {
      type: Boolean,
      required: false,
      default: false
    },
    editSettings: {
      type: Object,
      required: false,
      default() {
        return {
          allowEditing: true,
          allowAdding: true,
          allowDeleting: true,
          mode: 'Normal',
          allowEditOnDblClick: false,
          newRowPosition: 'Top'
        }
      }
    },
    selectionSettings: {
      type: Object,
      required: false,
      default() {
        return {
          type: 'Multiple',
          checkboxOnly: true
        }
      }
    },
    allowReordering: {
      type: Boolean,
      required: false,
      default: true
    },
    allowResizing: {
      type: Boolean,
      required: false,
      default: true
    },
    allowPaging: {
      type: Boolean,
      required: false,
      default: true
    },
    pageSettings: {
      type: Object,
      required: false,
      default() {
        return { pageSizes: [10, 50, 100, 200], pageCount: 4 }
      }
    },
    allowSorting: {
      type: Boolean,
      required: false,
      default: true
    },
    sortSettings: {
      type: Object,
      required: false,
      default() {
        return {}
      }
    },
    allowFiltering: {
      type: Boolean,
      required: false,
      default: true
    },
    // filterSettings: {
    //   type: Object,
    //   required: false,
    //   default: () => {
    //     return { type: "Menu" }
    //   }
    // },
    gridLines: {
      type: String,
      required: false,
      default: 'Both' //None,Both,Horizontal,Vertical,Default
    },
    totalPages: {
      type: Number,
      required: false,
      default: 1
    },
    frozenColumns: {
      type: Number,
      required: false,
      default: 0
    },
    autoFitColumns: {
      type: Array,
      required: false,
      default: () => {
        return []
      }
    },
    rowTemplate: {
      type: Function,
      default: () => {}
    },
    rowDataBound: {
      type: Function,
      default: () => {}
    },
    queryCellInfo: {
      type: Function,
      default: () => {}
    },
    useToolTemplate: {
      type: Boolean,
      default: true
    }
  },
  components: {
    MtDataGrid
  },
  data() {
    return {
      filterOptions: {
        type: 'Menu',
        operators: {
          stringOperator: [
            { text: this.$t('包含'), value: 'contains' },
            { text: this.$t('等于'), value: 'equal' },
            { text: this.$t('不等于'), value: 'notequal' }
          ],
          numberOperator: [
            { text: this.$t('等于'), value: 'equal' },
            { text: this.$t('不等于'), value: 'notequal' },
            { text: this.$t('大于'), value: 'greaterthan' },
            { text: this.$t('大于等于'), value: 'greaterthanorequal' },
            { text: this.$t('小于'), value: 'lessthan' },
            { text: this.$t('小于等于'), value: 'lessthanorequal' }
          ],
          dateOperator: [
            { text: this.$t('包含'), value: 'contains' },
            { text: this.$t('不等于'), value: 'notequal' },
            { text: this.$t('大于'), value: 'greaterthan' },
            { text: this.$t('大于等于'), value: 'greaterthanorequal' },
            { text: this.$t('小于'), value: 'lessthan' },
            { text: this.$t('小于等于'), value: 'lessthanorequal' }
          ],
          booleanOperator: [{ text: this.$t('等于'), value: 'equal' }],
          selectOperator: [
            { text: this.$t('等于'), value: 'equal' },
            { text: this.$t('不等于'), value: 'notequal' }
          ]
        }
      },
      columnqueryParams: [] // 列上的搜索条件
    }
  },

  provide() {
    return {
      parentVm: this
    }
  },
  computed: {
    gridData: {
      get() {
        return this.dataSource
      },
      set() {}
    },
    //序列化Grid的Column字段参数
    getGridColumnData() {
      return (column, index) => {
        for (let i in column) {
          let _col = column[i]

          //处理单元格通用模板
          if (
            Object.prototype.hasOwnProperty.call(_col, 'cellTools') ||
            Object.prototype.hasOwnProperty.call(_col, 'valueConverter') ||
            Object.prototype.hasOwnProperty.call(_col, 'cssClass')
          ) {
            //cellTools、cssClass、valueConverter参数 用ComonColumnTemplate处理
            _col['template'] = this.serializeCommonColumnTemplate(_col, index, i)
          }

          //处理单元格直接使用checkbox的情况
          if (Object.prototype.hasOwnProperty.call(_col, 'cellType')) {
            //cellType参数 用ColumnCheckboxTemplate处理
            _col['template'] = this.serializeCellTypeTemplate(_col, index)
          }

          //处理单元格编辑模板
          if (Object.prototype.hasOwnProperty.call(_col, 'editingParams')) {
            this.serializeCellEditTemplate(_col, index)
          }

          // 处理列搜索
          // if(this.allowFiltering) {
          if (Object.prototype.hasOwnProperty.call(_col, 'valueConverter')) {
            if (
              _col.valueConverter &&
              _col.valueConverter.type == 'map' &&
              Object.prototype.toString.call(_col.valueConverter.map) == '[object Object]'
            ) {
              let _querySeleteList = [],
                _querySeleteData = _col.valueConverter.map
              for (let i in _querySeleteData) {
                _querySeleteList.push({ label: _querySeleteData[i], value: i })
              }
              _col.type = 'string'
              _col.queryType = 'number'
              _col.querySeleteList = _querySeleteList
              _col.queryTemplate = {
                type: 'select',
                options: {
                  ds: _querySeleteList
                }
              }
              _col.filter = this.colFilter(_col)
            }
          }
          // }
        }
        return column
      }
    },
    //是否存在冻结列
    hasFrozenColumn() {
      if (this.frozenColumns > 0) {
        return true
      } else if (this.columnData[0]?.type === 'checkbox' && this.frozenColumns < 1) {
        this.frozenColumns = 1
        return true
      }
      // this.columnData.forEach(item => {
      //   if (item.isFrozen) {
      //     return true
      //   }
      // })
      return false
    }
  },

  watch: {
    dataSource(newVal) {
      // log('gridData', newVal)
      this.setFrozenColumn(newVal)
    }
  },
  mounted() {
    // this.setFrozenColumn(this.gridData)
  },
  methods: {
    //通用的一个列模板，主要处理cellTools、cssClass、valueConverter参数
    serializeCommonColumnTemplate(_col, index, columnIndex) {
      let getTemplateData = (_c, _index) => {
        let _data = {
          templateField: _c['field'],
          tabIndex: _index,
          columnIndex: columnIndex,
          gridTemplate: this.$refs['pageGrid-' + _index].ejsRef
        }
        if (Object.prototype.hasOwnProperty.call(_c, 'cellTools')) {
          _data['cellTools'] = _c['cellTools']
          _data['cssClass'] = 'field-content'
        }
        if (Object.prototype.hasOwnProperty.call(_c, 'valueConverter')) {
          _data['valueConverter'] = _c['valueConverter']
        }
        if (Object.prototype.hasOwnProperty.call(_c, 'cssClass')) {
          _data['cssClass'] = _c['cssClass']
        }
        return _data
      }
      return () => {
        return {
          template: ComonColumnTemplate,
          data: getTemplateData(_col, index)
        }
      }
    },
    //使用CheckboxTemplate模板，主要处理cellType参数
    serializeCellTypeTemplate(_col, index) {
      let getTemplateData = (_c, _index) => {
        let _data = {
          templateField: _c['field'],
          tabIndex: _index,
          gridTemplate: this.$refs['pageGrid-' + _index].ejsRef
        }
        return _data
      }
      return () => {
        let _ret = {}
        switch (_col['cellType']) {
          //处理checkbox格式
          case 'checkbox':
            _ret = {
              template: ColumnCheckboxTemplate,
              data: getTemplateData(_col, index)
            }
            break
          default:
            _ret = {}
            break
        }
        return _ret
      }
    },
    //处理单元格编辑使用的模板，根据type不同，使用不同单元格模板
    serializeCellEditTemplate(_col, index) {
      let _editingParams = _col.editingParams
      let _type = _editingParams['type']

      let getTemplateData = (_c, _index) => {
        let _data = {
          templateField: _c['field'],
          tabIndex: _index,
          editingParams: _editingParams,
          gridTemplate: this.$refs['pageGrid-' + _index].ejsRef
        }
        return _data
      }

      switch (_type) {
        case 'select':
          _col['editType'] = 'dropdownedit'
          _col['editTemplate'] = () => {
            return {
              template: ColumnDropdownTemplate,
              data: getTemplateData(_col, index)
            }
          }
          break
        case 'date':
          _col['editType'] = 'datepickeredit'
          break
        case 'number':
          _col['editType'] = 'numericedit'
          break
        case 'boolean':
          _col['editType'] = 'booleanedit'
          break
        default:
          _col['editType'] = 'stringedit'
          break
      }
    },
    currentChange(index) {
      this.$emit('handleGridCurrentChange', {
        currentPage: index,
        tabIndex: this.configIndex,
        grid: this.$refs[`pageGrid-${this.configIndex}`].ejsRef
      })
    },
    sizeChange(count) {
      this.$emit('handleGridSizeChange', {
        count: count,
        tabIndex: this.configIndex,
        grid: this.$refs[`pageGrid-${this.configIndex}`].ejsRef
      })
    },
    setFrozenColumn(newVal) {
      if (newVal && this.hasFrozenColumn) {
        let _parent = this.$refs[`pageGrid-${this.configIndex}`].$refs.ejsRef.$el
        if (newVal.length == 0 && _parent.getElementsByClassName('hasFrozenCloumn').length <= 0) {
          var NewNode = document.createElement('div')
          NewNode.className = 'hasFrozenCloumn'
          // NewNode.innerHTML = 'No records to display';
          NewNode.innerHTML = this.$t('无记录可显示')
          NewNode.style =
            'height: 50px; text-align: center; line-height: 50px; color: #999;border: 1px solid #e8e8e8;border-top: 0'
          if (_parent)
            _parent.insertBefore(NewNode, _parent.getElementsByClassName('e-gridcontent')[0])
        } else {
          if (_parent.getElementsByClassName('hasFrozenCloumn').length > 0) {
            _parent.removeChild(_parent.getElementsByClassName('hasFrozenCloumn')[0])
          }
        }
      }
    },

    dataBound() {
      if (this.autoFitColumns.length > 0)
        this.$refs['pageGrid-' + this.configIndex].$refs.ejsRef.autoFitColumns(this.autoFitColumns)
    },

    // 列过滤(下拉框渲染)
    colFilter(col) {
      if (!col.querySeleteList || col.querySeleteList.length == 0) {
        return
      }
      let dropInstance = null
      return {
        ui: {
          create: function (args) {
            let dataArr = [...col.querySeleteList]
            let flValInput = createElement('input', { className: 'flm-input' })
            args.target.appendChild(flValInput)
            dropInstance = new DropDownList({
              dataSource: new DataManager(dataArr),
              fields: { text: 'label', value: 'value' },
              placeholder: col.palceHolder || this.$t('请选择'),
              popupHeight: '200px'
            })
            dropInstance.appendTo(flValInput)
          },
          write: function (args) {
            dropInstance.value = args.filteredValue
          },
          read: function (args) {
            args.fltrObj.filterByColumn(args.column.field, args.operator, dropInstance.value)
          }
        }
      }
    },

    actionHandler(param) {
      // 自动过滤问题/??
      if (param.action == 'filter') {
        let _columnqueryParams = []
        if (param.columns && param.columns.length > 0) {
          param.columns.forEach((item) => {
            _columnqueryParams.push({
              field: item.field,
              operator: item.operator,
              value: item.value
            })
          })
          this.columnqueryParams = _columnqueryParams
          // this.pageSetting.currentPage = 1;
          // this.getData();
          this.$emit('handleQuerySearch', { rules: this.columnqueryParams })
          return false
        }
      } else if (param.action == 'clearFilter') {
        let _columnqueryParams = this.columnqueryParams
        _columnqueryParams.forEach((item, index) => {
          if (item.field == param.currentFilterColumn.field) {
            _columnqueryParams.splice(index, 1)
          }
        })
        this.columnqueryParams = _columnqueryParams
        // this.pageSetting.currentPage = 1;
        // this.getData();
        this.$emit('handleQuerySearch', { rules: this.columnqueryParams })
        return false
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.grid-container {
  width: 100%;
  height: 100%;
  background: #fff;
  ::v-deep .mt-data-grid {
    .e-grid {
      border: none;
      .e-gridcontent {
        border-top: 0;
        border-bottom: 0;
        .e-content {
          overflow-y: auto !important; //mt-data-grid中，overflow-y:scroll
        }
        .e-scrollbar {
          .e-frozenscrollbar {
            border-top: none;
          }
          .e-movablescrollbar {
            //mt-data-grid中，overflow-x:scroll
            overflow-x: auto;
            min-height: unset !important;
            max-height: unset !important;
            height: 10px !important;
            .e-movablechild {
              min-height: unset !important;
              max-height: unset !important;
              height: 10px !important;
            }
          }
        }
      }
      th.e-headercell {
        vertical-align: middle;
      }
    }

    //底部mt-page相关样式
    .mt-pagertemplate {
      margin: 0;
      padding: 10px 0;
      box-sizing: border-box;
      border-top: 1px solid #e8e8e8;
    }
    //存在冻结列，相关样式
    &.hasFrozen {
      .e-frozenheader > .e-table,
      .e-frozencontent > .e-table {
        border-right: 0;
        box-shadow: 1px 0px 5px 0 #e8e8e8;
        position: relative;
      }
      .e-emptyrow {
        display: none;
      }
      .e-movablecontent {
        td.e-active:first-of-type:before {
          display: none;
        }
      }
    }

    //scrollBar-color
    $scrollBar-track-color: #ffffff;
    $scrollBar-thumb-color: #d8d8d8;
    $scrollBar-thumb-hover-color: rgb(200, 200, 200);
    $scrollBar-thumb-active-color: rgb(190, 190, 190);
    //修改谷歌内核浏览器滚动条样式
    ::-webkit-scrollbar {
      width: 10px;
      height: 10px;
    }
    ::-webkit-scrollbar-track {
      border-radius: 2px;
      background-color: $scrollBar-track-color;
    }

    ::-webkit-scrollbar-thumb {
      background-color: $scrollBar-thumb-color;
      border-radius: 2px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background-color: $scrollBar-thumb-hover-color;
    }

    ::-webkit-scrollbar-thumb:active {
      background-color: $scrollBar-thumb-hover-color;
    }
  }
}
</style>
