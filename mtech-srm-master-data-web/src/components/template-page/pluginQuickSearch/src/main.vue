<template>
  <div
    slot="slot-filter"
    :class="[dialogTargetClass]"
    class="top-filter quick-search toggle-search-bar"
  >
    <div class="search-options">
      <!-- 左侧功能按钮 -->
      <div class="left-options" v-show="totalSize > 0 || isUseCustomSearch">
        <div class="sort-box" @click="expandChange">
          <span>{{ basicExpand ? $t('收起') : $t('展开') }}</span>
          <i
            class="mt-icons mt-icon-MT_DownArrow"
            :class="basicExpand ? 'expendIcon' : 'unExpendIcon'"
          />
        </div>
      </div>
      <div class="left-options" v-if="!!gridId && !isUseCustomSearch">
        <div class="search-template-bar">
          <div @click="showTemplates()">
            <span class="quick-search-template">{{ $t('搜索模板') }}</span>
            <div style="float: left">
              <i class="trangle-up" v-show="isShowTemplates"></i>
              <i class="trangle-down" v-show="!isShowTemplates"></i>
            </div>
          </div>
          <!-- 常显的模板 -->
          <div class="show-template-items">
            <!-- 从外面传入的开发者配置的默认模板，没有删除icon的 -->
            <div
              :class="[
                'search-template default-template',
                item.templateName == currentSearchTemplate && 'search-template-active',
              ]"
              v-for="(item, index) in defaultSearchTemplates"
              :key="'defaultSearchTemplates-combination-rule-' + index"
            >
              <span class="item name" @click="searchByTemplate(item)">{{ item.templateName }}</span>
            </div>
            <!-- 常规模板 -->
            <div
              :class="[
                'search-template',
                item.templateName == currentSearchTemplate && 'search-template-active',
              ]"
              v-for="(item, index) in splitTemplates"
              :key="'searchTemplates-combination-rule-' + index"
            >
              <span class="item name name-special" @click="searchByTemplate(item)">{{
                item.templateName
              }}</span>
              <div
                class="item remove"
                @click="removeSearchTemplate(item, index)"
                :ref="'remove-' + index"
              >
                <mt-icon name="icon_input_clear"></mt-icon>
              </div>
            </div>
          </div>
          <div v-if="isShowMoreTempalte" class="more-template-entry" @click="showTemplates()">
            <span>{{ $t('更多') }}</span>
            <i class="mt-icons mt-icon-MT_RightArrow more-template-icon" />
          </div>
        </div>
        <!-- 弹窗中模板item -->
        <div
          :class="[
            'templates-popup',
            defaultSearchTemplates.length == 0 &&
              searchTemplates.length == 0 &&
              'templates-popup-no-data',
          ]"
          v-show="isShowTemplates"
        >
          <span
            class="no-data"
            v-show="defaultSearchTemplates.length == 0 && searchTemplates.length == 0"
            >{{ $t('暂无可用的搜索模板') }}</span
          >
          <!-- 默认模板 -->
          <div
            :class="[
              'templates-popup-template',
              item.templateName == currentSearchTemplate && 'search-template-active',
            ]"
            v-for="(item, index) in defaultSearchTemplates"
            :key="'defaultSearchTemplates-combination-rule-' + index"
          >
            <span
              class="item template-name"
              :title="item.templateName"
              @click="searchByTemplate(item)"
            >
              {{ item.templateName }}
            </span>
            <div
              class="set-default-normal"
              @click="setDefaultTemplate(item)"
              :ref="'default-' + index"
              v-show="defaultTemplate !== item.templateName"
            >
              {{ $t('设为默认') }}
            </div>
            <div
              class="set-default"
              @click="cancelDefaultTemplate()"
              :ref="'default-' + index"
              v-show="defaultTemplate == item.templateName"
            >
              {{ $t('取消默认') }}
            </div>
          </div>
          <!-- 非默认模板 -->
          <div
            :class="[
              'templates-popup-template',
              item.templateName == currentSearchTemplate && 'search-template-active',
            ]"
            v-for="(item, index) in searchTemplates"
            :key="'searchTemplates-combination-rule-' + index"
          >
            <span
              class="item template-name"
              :title="item.templateName"
              @click="searchByTemplate(item)"
            >
              {{ item.templateName }}
            </span>
            <div
              class="set-default"
              @click="setDefaultTemplate(item)"
              :ref="'default-' + index"
              v-show="defaultTemplate !== item.templateName"
            >
              {{ $t('设为默认') }}
            </div>
            <div
              class="set-default"
              @click="cancelDefaultTemplate()"
              :ref="'default-' + index"
              v-show="defaultTemplate == item.templateName"
            >
              {{ $t('取消默认') }}
            </div>
            <div
              class="item remove"
              @click="removeSearchTemplate(item, index)"
              :ref="'remove-' + index"
            >
              <mt-icon name="icon_outline_Delete"></mt-icon>
            </div>
          </div>
        </div>
      </div>
      <!-- 右侧功能按钮 -->
      <div class="search-btns-warp font-style">
        <span
          v-if="!isUseCustomSearch && basicExpand"
          v-waves
          class="search-btn"
          type="info"
          @click="openColumnSelection"
        >
          {{ $t('设置搜索条件') }}
        </span>
        <span
          class="search-btn"
          type="info"
          v-waves
          v-if="!!gridId && !isUseCustomSearch && basicExpand"
          @click="componentSave"
          >{{ $t('保存为模板') }}
        </span>
        <span class="search-btn" v-waves type="info" @click="componentReset">{{ $t('重置') }}</span>
        <span class="search-btn" v-waves type="primary" @click="componentSearch">{{
          $t('查询')
        }}</span>
      </div>
    </div>
    <div :class="{ 'form-box': true, opend: basicExpand }" :style="getCustomStyle()">
      <div v-if="isUseCustomSearch" class="custom-search-container" @keyup.enter="handleKeyup">
        <slot name="custom-search-form" v-bind="{ searchFormModel }"></slot>
      </div>
      <mt-form
        v-else
        ref="searchFormRef"
        :model="searchFormObject"
        :rules="searchFormRules"
        autocomplete="off"
      >
        <mt-form-item
          :prop="rule.field"
          :label="rule.headerText"
          v-for="(rule, index) in serializeColumns"
          :key="index"
          label-style="top"
        >
          <div v-if="showElementByType(rule, 'number')" style="display: flex">
            <div class="operator-list" v-if="showElementByType(rule, 'number')">
              <mt-select
                v-model="rule.searchOptions.operator"
                :data-source="operatorList"
                popup-width="50px"
              ></mt-select>
            </div>
            <div class="custom-input-number" v-if="showElementByType(rule, 'number')">
              <mt-input-number
                :ref="rule.field"
                v-model="searchFormObject[rule.field]"
                type="number"
                :show-spin-button="false"
                :show-clear-button="rule.searchOptions.clearButton"
                :placeholder="rule.searchOptions.placeholder"
                @keyup.enter.native="componentSearch"
              />
            </div>
          </div>
          <mt-input
            :ref="rule.field"
            :title="searchFormObject[rule.field]"
            v-if="showElementByType(rule, 'text')"
            v-model="searchFormObject[rule.field]"
            :show-clear-button="rule.searchOptions.clearButton"
            :placeholder="rule.searchOptions.placeholder"
            :maxlength="rule.searchOptions.maxQueryValueLength"
            type="text"
            @keyup.enter.native="componentSearch"
          />
          <mt-select
            :ref="rule.field"
            v-if="showElementByType(rule, 'select')"
            v-model="searchFormObject[rule.field]"
            :allow-filtering="checkAllowFiltering(rule.field)"
            :filtering="onFiltering"
            :data-source="getElementDataSource(rule)"
            :fields="getElementDataFields(rule)"
            :placeholder="rule.searchOptions.placeholder"
            :show-clear-button="rule.searchOptions.clearButton"
            :popup-width="rule.searchOptions.popupWidth"
            @focus="getFocousItem(rule)"
            @keyup.enter.native="componentSearch"
          ></mt-select>
          <mt-multi-select
            :ref="rule.field"
            v-if="showElementByType(rule, 'multi-select')"
            v-model="searchFormObject[rule.field]"
            :allow-filtering="checkAllowFiltering(rule.field)"
            :filtering="onFiltering"
            :data-source="getElementDataSource(rule)"
            :fields="getElementDataFields(rule)"
            :placeholder="rule.searchOptions.placeholder"
            :show-clear-button="rule.searchOptions.clearButton"
            :popup-width="rule.searchOptions.popupWidth"
            :show-select-all="rule.searchOptions.showSelectAll"
            :select-all-text="$t('全选')"
            :un-select-all-text="$t('全不选')"
            @focus="getFocousItem(rule)"
          ></mt-multi-select>
          <mt-date-picker
            :ref="rule.field"
            v-if="showElementByType(rule, 'date')"
            :open-on-focus="true"
            :show-clear-button="rule.searchOptions.clearButton"
            :allow-edit="false"
            v-model="searchFormObject[rule.field]"
            :placeholder="rule.searchOptions.placeholder"
            @keyup.enter.native="componentSearch"
          ></mt-date-picker>
          <mt-time-picker
            :ref="rule.field"
            v-if="showElementByType(rule, 'time')"
            :open-on-focus="true"
            :show-clear-button="rule.searchOptions.clearButton"
            :allow-edit="false"
            v-model="searchFormObject[rule.field]"
            :placeholder="rule.searchOptions.placeholder"
            @keyup.enter.native="componentSearch"
          ></mt-time-picker>
          <mt-date-time-picker
            :ref="rule.field"
            v-if="showElementByType(rule, 'datetime')"
            :open-on-focus="true"
            :show-clear-button="rule.searchOptions.clearButton"
            :allow-edit="false"
            v-model="searchFormObject[rule.field]"
            :placeholder="rule.searchOptions.placeholder"
            @keyup.enter.native="componentSearch"
          ></mt-date-time-picker>
          <mt-date-range-picker
            :ref="rule.field"
            v-if="showElementByType(rule, 'date-range')"
            :open-on-focus="true"
            :show-clear-button="rule.searchOptions.clearButton"
            :allow-edit="false"
            v-model="searchFormObject[rule.field]"
            :placeholder="rule.searchOptions.placeholder"
            @keyup.enter.native="componentSearch"
          ></mt-date-range-picker>
          <mt-date-picker
            :ref="rule.field"
            v-if="showElementByType(rule, 'date-month')"
            start="Year"
            depth="Year"
            format="MMMM y"
            :open-on-focus="true"
            :show-clear-button="rule.searchOptions.clearButton"
            v-model="searchFormObject[rule.field]"
            :placeholder="rule.searchOptions.placeholder"
          ></mt-date-picker>
          <mt-switch
            v-if="showElementByType(rule, 'switch')"
            v-model="searchFormObject[rule.field]"
            :on-label="getSwitchLabel(rule)['onLabel']"
            :off-label="getSwitchLabel(rule)['offLabel']"
          ></mt-switch>
          <mt-checkbox
            v-if="showElementByType(rule, 'checkbox')"
            v-model="searchFormObject[rule.field]"
            @change="handleChangeCheckBox($event, rule)"
          ></mt-checkbox>
          <mt-drop-down-tree
            v-if="showElementByType(rule, 'drop-down-tree')"
            id="tree"
            :placeholder="rule.searchOptions.placeholder"
            v-model="searchFormObject[rule.field]"
            :fields="getElementTreeDataFields(rule)"
            :show-clear-button="rule.searchOptions.clearButton"
            :popup-width="rule.searchOptions.popupWidth"
          ></mt-drop-down-tree>
          <RemoteAutocomplete
            v-if="showElementByType(rule, 'remote-autocomplete') && remoteRefresh"
            :ref="rule.field"
            v-model="searchFormObject[rule.field]"
            :url="rule.searchOptions.url"
            :multiple="rule.searchOptions.multiple"
            :placeholder="rule.searchOptions.placeholder || $t('请选择')"
            :fields="getElementDataFields(rule)"
            :search-fields="rule.searchOptions.searchFields"
            :records-position="rule.searchOptions.recordsPosition"
            :data-source="rule.searchOptions.dataSource"
            :clearable="rule.searchOptions.clearable"
            :method="rule.searchOptions.method"
            :params="rule.searchOptions.params"
            :params-key="rule.searchOptions.paramsKey"
            :rule-params="rule.searchOptions.ruleParams"
          ></RemoteAutocomplete>
        </mt-form-item>
      </mt-form>
    </div>
    <plugin-column-selection
      v-if="showColumnSelection"
      :visible-columns="visibleColumns"
      :resource-columns="resourceColumnData"
      :dialog-position="dialogPosition"
      :selection-type="selectionType"
      @confirmColumnsDialog="handleConfirmColumnSelection"
      @cancelColumnsDialog="handleCancelColumnsSelection"
      :dialog-target="dialogTargetClass"
    />
    <save-dialog
      v-if="dialogShow"
      :dialog-data="dialogData"
      @handleAddDialogShow="handleAddDialogShow"
      @confirmSuccess="confirmSuccess"
    ></save-dialog>
  </div>
</template>
<script>
import saveDialog from './saveDialog.vue'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import { utils } from '@mtech-common/utils'
import MtForm from '@mtech-ui/form'
import MtFormItem from '@mtech-ui/form-item'
import MtInput from '@mtech-ui/input'
import MtInputNumber from '@mtech-ui/input-number'
import MtSelect from '@mtech-ui/select'
import MtMultiSelect from '@mtech-ui/multi-select'
import MtSwitch from '@mtech-ui/switch'
import MtCheckbox from '@mtech-ui/checkbox'
import MtDatePicker from '@mtech-ui/date-picker'
import MtTimePicker from '@mtech-ui/time-picker'
import MtDateTimePicker from '@mtech-ui/date-time-picker'
import MtDateRangePicker from '@mtech-ui/date-range-picker'
import MtDropDownTree from '@mtech-ui/drop-down-tree'

// import pluginColumnSelection from '@mtech/plugin-column-selection'
// import '@mtech/plugin-column-selection/build/esm/bundle.css'
import pluginColumnSelection from '../../pluginColumnSelection/src/main.vue'

import { API } from '@mtech-common/http'
const saveUserMemory = '/lowcodeWeb/tenant/user-memory/save'
export default {
  name: 'PluginQuickSearch',
  components: {
    MtForm,
    MtFormItem,
    MtSelect,
    MtSwitch,
    MtCheckbox,
    MtInput,
    MtInputNumber,
    MtDatePicker,
    MtTimePicker,
    MtDateTimePicker,
    MtDateRangePicker,
    MtMultiSelect,
    MtDropDownTree,
    pluginColumnSelection,
    saveDialog,
    RemoteAutocomplete,
  },
  props: {
    columnData: {
      type: Array,
      default: () => {
        return []
      },
    },
    resourceColumnData: {
      type: Array,
      default: () => {
        return []
      },
    },
    maxQueryValueLength: {
      type: Number,
      default: 50,
    },
    // 支持搜索过滤的字段列表
    allowFilteringFields: {
      type: Array,
      default: () => {
        return []
      },
    },
    gridId: {
      type: String,
      default: '',
    },
    // 默认搜索模板
    defaultSearchTemplates: {
      type: Array,
      default: () => {
        return []
      },
    },
    searchResetWithAPI: {
      type: Boolean,
      default: true,
    },
    saveSelectedRecordWithSearch: {
      type: Boolean,
      default: true,
    },
    isUseCustomSearch: {
      type: Boolean,
      default: false,
    },
    // 自定义快捷查询form model
    // searchFormModel: {
    //   type: Object,
    //   default: () => {
    //     return {}
    //   }
    // },
    tabsIndex: {
      type: Number,
      default: 0,
    },
    isCustomSearchHandle: {
      type: Boolean,
      default: false,
    },
    customMatchRules: {
      type: Object,
      default: () => {
        return {}
      },
    },
    defaultMaxHeight: {
      type: [String, Number],
      default: '60',
    },
    maxHeight: {
      type: String,
      default: '600px',
    },
    minRows: {
      type: [String, Number],
      default: 1,
    },
  },
  inject: {
    searchFormModelList: {
      value: 'searchFormModelList',
      default: () => {
        return [{}]
      },
    },
  },
  data() {
    return {
      dialogTargetClass: `dialog-target-${parseInt(Math.random(10000) * 10000)}`,
      operatorList: [
        { text: '=', value: 'equal' },
        { text: '≠', value: 'notequal' },
        { text: '>', value: 'greaterthan' },
        { text: '≥', value: 'greaterthanorequal' },
        { text: '<', value: 'lessthan' },
        { text: '≤', value: 'lessthanorequal' },
      ],
      basicExpand: false,
      totalSize: 0,
      isShowTemplates: false,
      currentSearchTemplate: '',
      defaultTemplate: '',
      serializeColumns: [],
      searchFormObject: {},
      searchFormRules: {},
      dialogData: null,
      dialogShow: false, // 是否显示弹窗
      searchTemplates: [],
      // allowFilteringFields: [],
      selectionType: 'search',
      searchColumnData: [],
      visibleColumns: [],
      showColumnSelection: false,
      dialogPosition: {
        X: 0,
        Y: 0,
      },
      focousItem: {},
      defaultQuery: {
        queryMethods: 'post',
        queryItem: 'fuzzyNameOrCode',
      },
      dataSourceMap: {},
      remoteRefresh: true, // 当重新选择模板之后需要重新初始化此组件
    }
  },
  computed: {
    searchFormModel() {
      return this.searchFormModelList && this.searchFormModelList[this.tabsIndex]
    },
    getSwitchLabel() {
      return (rule) => {
        return {
          onLabel: rule?.searchOptions?.onLabel ?? this.$t('打开'),
          offLabel: rule?.searchOptions?.offLabel ?? this.$t('关闭'),
        }
      }
    },
    // 校验当前字段的下拉框，需要模糊搜索
    checkAllowFiltering() {
      return (field) => {
        return this.allowFilteringFields.indexOf(field) > -1
      }
    },
    showElementByType() {
      return (rule, type) => {
        if (rule?.field) {
          if (rule?.searchOptions?.elementType) {
            return rule?.searchOptions?.elementType === type
          } else {
            return type === 'text'
          }
        } else {
          return false
        }
      }
    },
    getElementDataSource() {
      return (rule) => {
        if (rule?.searchOptions?.dataSource) {
          return rule?.searchOptions?.dataSource
        } else {
          return []
        }
      }
    },
    getElementDataFields() {
      return (rule) => {
        if (rule?.searchOptions?.fields) {
          return rule?.searchOptions?.fields
        } else {
          return { text: 'text', value: 'value' }
        }
      }
    },
    getElementTreeDataFields() {
      return (rule) => {
        if (rule?.searchOptions?.fields) {
          return rule?.searchOptions?.fields
        } else {
          return {
            value: 'value',
            text: 'text',
            dataSource: [],
            child: 'child',
          }
        }
      }
    },
    isShowMoreTempalte() {
      const totalLen = this.defaultSearchTemplates.length + this.searchTemplates.length
      return totalLen > 3
    },
    splitTemplates() {
      const totalTemplates = this.defaultSearchTemplates.concat(this.searchTemplates) || []
      // 将active模板移到最前面
      const currentIdx = totalTemplates.findIndex(
        (item) => item.templateName === this.currentSearchTemplate,
      )
      if (currentIdx > -1) {
        const currentTemplate = totalTemplates[currentIdx]
        totalTemplates.splice(currentIdx, 1)
        totalTemplates.unshift(currentTemplate)
      }
      const defaultLen = this.defaultSearchTemplates.length
      if (0 < defaultLen < 4) {
        const normalLen = 3 - defaultLen
        return totalTemplates.splice(0, normalLen)
      } else {
        return this.defaultSearchTemplates.splice(0, 3)
      }
    },
  },
  created() {
    this.onFiltering = utils.debounce(this.onFiltering, 1000)
    this.searchTemplateInit()
  },
  mounted() {
    // 初始化时莫名触发了trigger，需要重置掉校验结果
    this.$nextTick(() => {
      this.$parent.$parent.$refs.searchFormRef?.clearValidate()
    })
  },
  watch: {
    columnData: {
      handler(n) {
        if (n && Array.isArray(n) && n.length > 0) {
          this.serializeColumnData(n)
        } else {
          // this.serializeColumnData(searchColumns);
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    handleKeyup(e) {
      if (e?.keyCode === 13) {
        this.componentSearch()
      }
    },
    searchTemplateInit() {
      if (this.gridId) {
        let _gridInfo = JSON.parse(sessionStorage.getItem(this.gridId))
        if (_gridInfo?.searchTemplates?.length > 0) {
          this.searchTemplates = _gridInfo.searchTemplates
        }
        if (_gridInfo?.defaultTemplate) {
          this.defaultTemplate = _gridInfo.defaultTemplate
          this.currentSearchTemplate = _gridInfo.defaultTemplate
        } else if (this.defaultSearchTemplates.length > 0) {
          this.currentSearchTemplate = this.defaultSearchTemplates[0].templateName
        }
        if (this.currentSearchTemplate) {
          let _template = this.searchTemplates.find(
            (e) => e.templateName == this.currentSearchTemplate,
          )
          if (_template) {
            setTimeout(() => {
              this.initSearchByTemplate(_template)
            }, 0)
          } else if (this?.defaultSearchTemplates[0]?.templateName == this.currentSearchTemplate) {
            setTimeout(() => {
              this.initSearchByTemplate(this.defaultSearchTemplates[0])
            }, 0)
          }
        }
      }
    },
    expandChange() {
      this.basicExpand = !this.basicExpand
    },
    getCustomStyle() {
      return {
        maxHeight: this.basicExpand
          ? this.maxHeight
          : this.minRows
          ? `${this.minRows * Number(this.defaultMaxHeight)}px`
          : `${this.defaultMaxHeight}px`,
      }
    },
    showTemplates() {
      this.isShowTemplates = !this.isShowTemplates
    },
    setDefaultTemplate(item) {
      let _gridMemory = JSON.parse(sessionStorage.getItem(this.gridId))
      _gridMemory.defaultTemplate = item.templateName
      sessionStorage.setItem(this.gridId, JSON.stringify(_gridMemory))
      API.post(saveUserMemory, {
        gridId: this.gridId,
        gridMemory: _gridMemory,
      }).then((res) => {
        if (res.code === 200) {
          this.defaultTemplate = item.templateName
        }
      })
    },
    cancelDefaultTemplate() {
      let _gridMemory = JSON.parse(sessionStorage.getItem(this.gridId))
      _gridMemory.defaultTemplate = ''
      sessionStorage.setItem(this.gridId, JSON.stringify(_gridMemory))
      API.post(saveUserMemory, {
        gridId: this.gridId,
        gridMemory: _gridMemory,
      }).then((res) => {
        if (res.code === 200) {
          this.defaultTemplate = ''
        }
      })
    },
    initSearchByTemplate(item) {
      let arr = []
      for (let i in item.searchRule.form) {
        let _item = this.resourceColumnData.find((el) => el.field === i)
        arr.push({ ..._item })
      }
      this.serializeColumnData(arr)
      this.searchFormObject = { ...item.searchRule.form }
    },
    searchByTemplate(item) {
      this.refreshRemoteAutocomplete()
      if (this.currentSearchTemplate == item.templateName) {
        // 点击currentTemplate有反选的作用
        if (this.currentSearchTemplate !== '' && !!this.gridId) {
          let _gridMemory = JSON.parse(sessionStorage.getItem(this.gridId))
          _gridMemory.currentSearchTemplate = ''
          sessionStorage.setItem(this.gridId, JSON.stringify(_gridMemory))
          API.post(saveUserMemory, {
            gridId: this.gridId,
            gridMemory: _gridMemory,
          }).then((res) => {
            if (res.code === 200) {
              this.currentSearchTemplate = ''
              this.$emit('handleQuickReset')
            }
          })
        }
      } else {
        let _gridMemory = JSON.parse(sessionStorage.getItem(this.gridId))
        _gridMemory.currentSearchTemplate = item.templateName
        sessionStorage.setItem(this.gridId, JSON.stringify(_gridMemory))
        API.post(saveUserMemory, {
          gridId: this.gridId,
          gridMemory: _gridMemory,
        }).then((res) => {
          if (res.code === 200) {
            this.currentSearchTemplate = item.templateName
          }
        })
        this.$emit('handleQuickSearch', item.searchRule)
        let arr = []
        for (let i in item.searchRule.form) {
          let _item = this.resourceColumnData.find((x) => x.field == i)
          arr.push({ ..._item })
        }
        this.serializeColumnData(arr)
        this.searchFormObject = { ...item.searchRule.form }
      }
      this.isShowTemplates = false
    },
    refreshRemoteAutocomplete() {
      this.remoteRefresh = false
      this.$nextTick(() => {
        this.remoteRefresh = true
      })
    },
    // 移除搜索条件
    removeSearchTemplate(item, i) {
      const _resetFlag = this.currentSearchTemplate === item.templateName ? true : false
      const _resetDefault = this.defaultTemplate === item.templateName ? true : false
      const _searchTemplates = utils.cloneDeep(this.searchTemplates)
      _searchTemplates.splice(i, 1)
      let _gridMemory = JSON.parse(sessionStorage.getItem(this.gridId))
      _gridMemory.searchTemplates = _searchTemplates
      if (_resetFlag) {
        _gridMemory.currentSearchTemplate = ''
      }
      if (_resetDefault) {
        _gridMemory.defaultTemplate = ''
      }
      sessionStorage.setItem(this.gridId, JSON.stringify(_gridMemory))
      API.post(saveUserMemory, {
        gridId: this.gridId,
        gridMemory: _gridMemory,
      }).then((res) => {
        if (res.code === 200) {
          this.searchTemplates = _searchTemplates
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          if (_resetFlag) {
            this.currentSearchTemplate = ''
            this.$emit('handleQuickReset')
          }
        }
      })
    },
    confirmSuccess() {
      if (this.gridId) {
        let _gridInfo = JSON.parse(sessionStorage.getItem(this.gridId))
        this.searchTemplates = _gridInfo.searchTemplates
      }
    },
    changeData(args, rule) {
      // if(rule.searchOptions.type==="select"){
      //   this.searchFormObject[rule.field] = args.itemData[rule.searchOptions.fields.value];
      // }
    },
    getFocousItem(e) {
      this.focousItem = e
    },
    onFiltering(e) {
      this.defaultQuery = {
        ...this.defaultQuery,
        dataSource: this.dataSourceMap[this.focousItem.field],
        ...this.focousItem.searchOptions,
      }
      if (this.defaultQuery?.queryUrl) {
        if (!this.defaultQuery?.queryParams) {
          this.defaultQuery.queryParams = {}
        }
        this.defaultQuery.queryParams[this.defaultQuery.queryItem] = e.text
        API[this.defaultQuery.queryMethods](
          `${this.defaultQuery.queryUrl}`,
          this.defaultQuery.queryParams,
        ).then((res) => {
          this.dataSourceMap[this.focousItem.field] = res.data
          this.$nextTick(() => {
            e.updateData(this.dataSourceMap[this.focousItem.field])
          })
        })
      } else {
        if (!this.defaultQuery?.fields) {
          e.updateData(this.defaultQuery.dataSource.filter((x) => x.includes(e.text)))
        } else {
          e.updateData(
            this.defaultQuery.dataSource.filter((x) =>
              x[this.defaultQuery.fields.text].includes(e.text),
            ),
          )
        }
      }
    },
    // 序列化columnData
    serializeColumnData(list) {
      this.serializeColumns.length = 0
      if (!this.isUseCustomSearch) this.searchFormObject = {}
      const _resourceColumnData = utils.cloneDeep(this.resourceColumnData)
      const _columns = utils.cloneDeep(list)
      _columns.forEach((e) => {
        let _field = _resourceColumnData.find((x) => x.field == e.field)
        let defaultValue = null
        if (_field && _field?.searchOptions) {
          e.searchOptions = _field.searchOptions
          defaultValue = _field.searchOptions.default
        }
        if (_field && _field?.valueConverter) {
          e.valueConverter = _field.valueConverter
        }
        this.searchFormObject[e.field] = defaultValue
      })
      this.totalSize = list.length
      _columns.forEach((e) => {
        const _options = e?.searchOptions ?? {}
        const _valueConverter = e?.valueConverter
        if (_valueConverter?.map) {
          if (Array.isArray(_valueConverter?.map)) {
            _options.dataSource = _valueConverter?.map
          } else {
            const _list = []
            const _converterData = _valueConverter?.map
            for (const i in _converterData) {
              _list.push({ text: _converterData[i], value: i })
            }
            _options.dataSource = _list
          }
          _options.fields = _valueConverter?.fields
            ? _valueConverter?.fields
            : { text: 'text', value: 'value' }
          _options.type = _options.type ?? 'string'
          _options.elementType = _options.elementType ?? 'select'
          _options.operator = _options.operator ?? 'equal'
        }
        let _elementType = 'text'
        let _type = _options.type ? _options.type : e.type ?? 'string'
        switch (_type) {
          case 'date':
            _elementType = 'date'
            _type = 'string'
            break
          case 'time':
            _elementType = 'time'
            _type = 'string'
            break
          case 'datetime':
            _elementType = 'datetime'
            _type = 'string'
            break
          case 'number':
            _elementType = 'number'
            _type = 'number'
            break
        }
        _options.type = _type
        _options.elementType = _options.elementType ? _options.elementType : _elementType ?? 'text'
        if (_options.elementType === 'number') {
          const defaultValue = _options.default || null
          if (!this.isUseCustomSearch) this.searchFormObject[e.field] = defaultValue
        }
        if (_options.elementType === 'text' && !_options?.maxQueryValueLength) {
          _options.maxQueryValueLength = this.maxQueryValueLength
        }
        _options.operator = _options.operator
          ? _options.operator
          : _options.elementType === 'select' || _options.elementType === 'number'
          ? 'equal'
          : 'contains'
        _options.placeholder = _options?.placeholder ? _options.placeholder : ''
        _options.clearButton = _options?.clearButton ? _options.clearButton : true
        _options.popupWidth = _options?.popupWidth ? _options.popupWidth : '100%'
        e.searchOptions = _options
      })
      this.serializeColumns = _columns
      this.visibleColumns = _columns
      // this.$nextTick(() => {
      //   this.defineDefaultValue()
      // })
      this.defineFormRule()
    },
    getSingleRule(field, value) {
      const _source = this.serializeColumns.find((e) => e.field === field)
      let _value = utils.cloneDeep(value)
      if (_source?.field) {
        const _options = _source?.searchOptions
        if (typeof _options?.customRule === 'function') {
          // 用户自定义规则
          return _options.customRule(_value)
        }
        if (typeof _options?.serializeValue === 'function') {
          // 用户自定义，数据序列化
          _value = _options.serializeValue(value)
        } else if (_options?.dateFormat) {
          // 用户自定义dateFormat
          if (
            _options?.elementType === 'date' ||
            _options?.elementType === 'time' ||
            _options?.elementType === 'datetime'
          ) {
            // type为date、datetime，按照用户自定义的dateFormat，序列化数据
            _value = this.formatDate(_value, _options.dateFormat)
          }
        } else {
          if (_options?.elementType === 'date') {
            // 默认的date序列化
            _value = this.formatDate(_value)
          }
          if (_options?.elementType === 'datetime' || _options?.elementType === 'time') {
            // 默认的datetime、time序列化
            _value = this.formatDate(_value, 'YYYY-mm-dd HH:MM:SS')
          }
        }
        return {
          label: _source?.headerText,
          field: _options?.renameField ? _options?.renameField : field,
          type: _options.type,
          operator: _options.operator,
          value: _value,
        }
      } else {
        return null
      }
    },
    // 序列化queryBuilder需要的规则
    serializeRules() {
      const _object = Object.assign({}, this.searchFormObject)
      const rules = []
      for (const i in _object) {
        if (
          _object[i] ||
          (_object[i] === 0 &&
            (this.$refs[i][0].ejsValue == 0 ||
              this.serializeColumns.find((item) => item.field == i).searchOptions.elementType !==
                'number')) ||
          _object[i] === false
        ) {
          const _rule = this.getSingleRule(i, _object[i])
          if (_rule && _rule?.value?.toString().length > 0) {
            // 存在rule，且rule中存在有效数据
            rules.push(_rule)
          }
        }
      }
      return {
        condition: 'and',
        rules,
      }
    },
    // checkbox选择数据
    handleChangeCheckBox(e, item) {
      this.searchFormObject[item.field] = e.checked
    },
    // 按照默认值，初始化表单数据
    defineDefaultValue() {
      this.serializeColumns.forEach((e) => {
        const defaultValue = e?.searchOptions?.default || null
        this.$refs[e.field][0].ejsValue = defaultValue
        this.searchFormObject[e.field] = defaultValue
      })
    },
    // 按照默认值，初始化表单校验规则
    defineFormRule() {
      const _validIsTextElement = (rule, type) => {
        if (rule?.field) {
          if (rule?.searchOptions?.elementType) {
            return rule?.searchOptions?.elementType === type
          } else {
            return type === 'text'
          }
        } else {
          return false
        }
      }
      this.searchFormRules = {}
      this.serializeColumns.forEach((e) => {
        if (_validIsTextElement(e, 'text')) {
          //   this.searchFormRules[e.field] = [
          //     {
          //       max: this.maxQueryValueLength,
          //       message: `${this.$t('搜索值长度，不超过')}${this.maxQueryValueLength}`,
          //       trigger: 'blur'
          //     }
          //   ]
        }
      })
    },
    getOffsetSum(elem) {
      var top = 0
      var left = 0
      while (elem) {
        top = top + parseInt(elem.offsetTop)
        left = left + parseInt(elem.offsetLeft)
        elem = elem.offsetParent
      }
      return { offsetTop: top, offsetLeft: left }
    },
    // 设置快速查询条件
    openColumnSelection(event) {
      setTimeout(() => {
        const _target = event?.target
        const _offSet = this.getOffsetSum(_target)
        const { offsetTop, offsetLeft } = _offSet
        const selfWidth = _target.clientWidth
        const selfHeight = _target.clientHeight
        const documentHeight = document.body.clientHeight
        const documentWidth = document.body.clientWidth
        let X = 0
        let Y = 0
        if (documentWidth - offsetLeft - selfWidth > 400) {
          X = offsetLeft - 10
        } else {
          X = offsetLeft - 400 + selfWidth + 10
        }
        if (documentHeight - offsetTop - selfHeight > 400) {
          Y = selfHeight + offsetTop + 10
        } else {
          Y = offsetTop - 10 - 400
        }
        this.dialogPosition = { X, Y }
        this.showColumnSelection = true
      }, 20)
    },
    // 搜索操作
    componentSearch() {
      if (this.isUseCustomSearch) {
        // 为了兼容用template-page嵌套template-page的tab类型页面，需要多一层$parent
        const validate = this.$parent.$parent.$refs.searchFormRef?.validate
          ? this.$parent.$parent.$refs.searchFormRef.validate
          : this.$parent.$parent.$parent.$refs.searchFormRef?.validate
          ? this.$parent.$parent.$parent.$refs.searchFormRef.validate
          : this.$parent.$parent.$parent.$parent.$refs.searchFormRef?.validate
          ? this.$parent.$parent.$parent.$parent.$refs.searchFormRef.validate
          : this.$parent.$parent.$parent.$parent.$parent.$refs.searchFormRef?.validate

        validate((valid) => {
          if (valid) {
            if (this.isCustomSearchHandle) {
              this.$emit('handleCustomSearch', this.searchFormModel)
            } else {
              this.handleSearchThing()
            }
          }
        })
      } else {
        this.$refs.searchFormRef.validate((valid) => {
          if (valid) {
            this.handleSearchThing()
          }
        })
      }
    },
    handleSearchThing() {
      if (this.gridId) {
        let _gridMemory = JSON.parse(sessionStorage.getItem(this.gridId))
        _gridMemory.currentSearchTemplate = this.currentSearchTemplate || ''
        sessionStorage.setItem(this.gridId, JSON.stringify(_gridMemory))
        API.post(saveUserMemory, {
          gridId: this.gridId,
          gridMemory: _gridMemory,
        }).then((res) => {
          if (res.code === 200) {
            // this.currentSearchTemplate = ''
          }
        })
      }
      // saveSelectedRecordWithSearch暂时用不上，也没看出有使用上的必要
      if (!this.saveSelectedRecordWithSearch) {
        if (this.$parent.$refs[`pageGridRef-${this.$parent.currentTabIndex}`][0]) {
          this.$parent.$refs[
            `pageGridRef-${this.$parent.currentTabIndex}`
          ][0].$refs.pluginGridRef.$refs.gridRef.selectIdRecords = []
        }
      }

      let _rules = {}
      if (!this.$attrs.isCustomSearchRules) {
        _rules = this.isUseCustomSearch ? this.getCustomSearchRules() : this.getSearchRules()
      }
      this.$emit('handleQuickSearch', _rules)
    },
    // 设置自定义快捷查询的规则
    getCustomSearchRules() {
      const ruleList = []
      const searchFormModel = this.searchFormModel || {}
      const customMatchRules = this.customMatchRules || {}
      for (let key in searchFormModel) {
        if (Object.hasOwnProperty.call(searchFormModel, key)) {
          if (!searchFormModel[key]) continue // 没有值的字段忽略
          ruleList.push({
            field: key,
            operator: customMatchRules[key] || 'contains',
            value: searchFormModel[key],
          })
        }
      }
      return {
        form: searchFormModel,
        rules: {
          condition: 'and',
          rules: ruleList,
        },
      }
    },
    // 重置操作
    componentReset() {
      if (this.isUseCustomSearch) {
        this.$emit('handleCustomReset')
        // this.$parent.$parent.$refs.searchFormRef?.resetFields()
        this.$nextTick(() => {
          this.$forceUpdate()
        })
      } else {
        this.defineDefaultValue()
      }
      // if (this.searchResetWithAPI) {
      //   if (this.currentSearchTemplate !== '' && !!this.gridId) {
      //     let _gridMemory = JSON.parse(sessionStorage.getItem(this.gridId))
      //     _gridMemory.currentSearchTemplate = ''
      //     sessionStorage.setItem(this.gridId, JSON.stringify(_gridMemory))
      //     API.post(saveUserMemory, {
      //       gridId: this.gridId,
      //       gridMemory: _gridMemory,
      //     }).then((res) => {
      //       if (res.code === 200) {
      //         this.currentSearchTemplate = ''
      //         // 下面这行代码看不出来有什么意义-lbj-2023.04.15
      //         this.componentReset()
      //       }
      //     })
      //   }
      //   this.$emit('handleQuickReset')
      // }
    },
    componentSave() {
      this.$refs.searchFormRef.validate((valid) => {
        // if (valid) {
        //   let _searchData = this.getSearchRules()
        //   if (_searchData.rules.rules.length > 0) {
        //     let _templateNames = this.searchTemplates
        //       .concat(this.defaultSearchTemplates)
        //       .map((e) => e.templateName)
        //     this.dialogData = {
        //       gridId: this.gridId,
        //       searchRules: this.getSearchRules(),
        //       templateNames: _templateNames
        //     }
        //     this.dialogShow = true
        //   } else {
        //     this.$toast({
        //       content: this.$t('查询条件不能为空'),
        //       type: 'warning'
        //     })
        //   }
        // }
        /*------暂时将需要填值才能保存模板的设定去掉------23.10.04-lbj--------*/
        if (valid) {
          let _templateNames = this.searchTemplates
            .concat(this.defaultSearchTemplates)
            .map((e) => e.templateName)
          this.dialogData = {
            gridId: this.gridId,
            searchRules: this.getSearchRules(),
            templateNames: _templateNames,
          }
          this.dialogShow = true
        }
      })
    },
    handleAddDialogShow(flag) {
      this.dialogShow = flag
    },
    // 抛出方法，供外部调用
    getSearchRules() {
      return {
        form: this.searchFormObject,
        rules: this.serializeRules(),
      }
    },
    formatDate(date, fmt = 'Y-m-d') {
      if (!date) {
        return
      }
      date = new Date(date)
      let ret
      const opt = {
        'Y+': date.getFullYear().toString(),
        'm+': (date.getMonth() + 1).toString(),
        'd+': date.getDate().toString(),
        'H+': date.getHours().toString(),
        'M+': date.getMinutes().toString(),
        'S+': date.getSeconds().toString(),
      }
      for (const k in opt) {
        ret = new RegExp('(' + k + ')').exec(fmt)
        if (ret) {
          fmt = fmt.replace(
            ret[1],
            ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, '0'),
          )
        }
      }
      return fmt
    },
    // 列设置弹框-确认操作
    handleConfirmColumnSelection(e) {
      const { type, value } = e
      if (type === 'search') {
        const _visibleColumns = value.visibleColumns
        this.$emit('handleSaveMemory', { savedSearchItem: _visibleColumns })
        this.visibleColumns = _visibleColumns
        this.serializeColumnData(_visibleColumns)
        this.showColumnSelection = false
      }
    },
    // 列设置弹框-取消操作
    handleCancelColumnsSelection() {
      this.showColumnSelection = false
    },
  },
}
</script>
<style>
html,
body {
  margin: 0;
  padding: 0;
}
</style>
<style lang="scss" scoped>
.top-filter {
  background: #fff;
  // padding-top: 10px;
  font-weight: 500;

  .accordion-main {
    margin-right: 20px;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 25px;
  }

  .left-status {
    margin: 0px 20px 20px 0px;
    clear: both;
  }

  .titles {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
  }
  &-small {
    height: 34px;
    overflow: hidden;
  }
}
.quick-search {
  box-sizing: border-box;
  width: 100%;
  display: flex;
  flex-direction: column;
  .search-options {
    height: 36px;
    color: #6386c1;
    .left-options {
      position: relative;
      height: 100%;
      float: left;
      margin-left: 12px;
      max-width: calc(100% - 100px);
      display: flex;
      align-items: center;
      .show-template-items,
      .more-template-entry {
        height: 100%;
        display: flex;
        align-items: center;
        cursor: pointer;
        .more-template-icon {
          margin: 0 0 -4px 2px;
        }
      }
    }
    .search-template-bar {
      position: relative;
      display: flex;
      align-items: center;
    }
    .search-btns-warp {
      height: 100%;
      float: right;
      display: flex;
      align-items: center;
      margin-right: 12px;
      .search-btn {
        display: block;
        height: 28px;
        line-height: 26px;
        padding: 0 16px;
        border-radius: 4px;
        margin-left: 10px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        font-weight: 400;
        cursor: pointer;
        user-select: none;
        border: 1px solid #4a556b;
        box-sizing: border-box;
      }
      .search-btn[type='info'] {
        background: #fff;
        color: #4a556b;
        // background: #19a2d5;
        // color: #fff;
      }
      .search-btn[type='primary'] {
        background: #4a556b;
        color: #fff;
      }
    }
    .sort-box {
      height: 100%;
      // margin-left: 16px;
      display: flex;
      align-items: center;
      cursor: pointer;
      .mt-icons {
        font-size: 12px;
        margin-left: 2px;
      }
      .expendIcon {
        transform: rotate(180deg);
        margin-top: -4px;
      }
      .unExpendIcon {
        margin-top: 4px;
      }
    }
  }
  .font-style {
    font-size: 13px;
    font-weight: bold;
  }
  .ml-26 {
    margin-left: 26px;
  }
  .form-box {
    margin: 0px 10px 0 10px;
    padding-bottom: 8px;
    background: #f5f5f5;
    border-radius: 8px;
    transition: max-height 0.5s cubic-bezier(0, 1, 0, 1);
    position: relative;
    overflow: hidden;
  }
  .opend {
    height: auto;
    transition: max-height 0.5405s linear;
  }
  .custom-search-container {
    // margin: 0px 10px;
    // padding-bottom: 8px;
    background: #f5f5f5;
    border-radius: 8px;
  }
  .mt-form {
    width: 100%;
    padding: 5px 10px 0 11px;
    box-sizing: border-box;
    display: grid;
    justify-content: space-between;
    grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
    grid-gap: 10px;
    padding: 10px;
    ::v-deep .mt-form-item {
      // width: 300px;
      margin-bottom: 0px !important;
      ::v-deep .mt-form-item-label {
        .label {
          text-align: left !important;
          margin-right: 5px !important;
        }
        div:nth-child(2) {
          width: inherit;
        }
        .ant-select-selection {
          background-color: rgba(245, 245, 245, 0);
        }
      }
      .mt-input {
        width: 100%;
      }
      ::v-deep .ant-select-selection {
        background-color: transparent;
      }
    }
    ::v-deep .mt-form-item .error-label-label-top {
      top: 86% !important;
    }
  }
  ::v-deep .operator-list {
    width: 50px !important;
    // margin-right: 5px;
  }
  ::v-deep .custom-input-number {
    width: 100%;
    .mt-input-number {
      width: 100%;
      margin-bottom: 0px;
      input {
        height: 23px;
        padding-right: 30px;
      }
    }
  }
  &-template {
    cursor: pointer;
    line-height: 22px;
    display: inline-block;
    float: left;
    margin-left: 10px;
  }
  .templates-popup {
    width: 300px;
    position: absolute;
    top: 30px;
    z-index: 999;
    background-color: white;
    padding: 8px 8px;
    border-radius: 6px;
    max-height: 300px;
    overflow-y: auto;
    box-shadow: 1px 1px 4px 0px #aaa;
    &-no-data {
      top: 30px;
    }
    &-template {
      height: 32px;
      width: 100%;
      display: flex;
      align-items: center;
      padding-left: 8px;
    }
    &-template:hover {
      background: #f0f7ff;
    }
    .remove {
      cursor: pointer;
      &:hover {
        color: var(--common-tp-column-handler-hover-bg);
      }
    }
    .set-default {
      color: #6386c1;
      margin-right: 10px;
      cursor: pointer;
    }
    .set-default-normal {
      color: #6386c1;
      margin-right: 25px;
      cursor: pointer;
    }
    .no-data {
      width: 100%;
      line-height: 30px;
      display: inline-block;
    }
    .template-name {
      display: inline-block;
      width: 160px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #666;
      font-weight: 500;
      margin-right: 16px;
      cursor: pointer;
    }
  }
  .trangle-up {
    border-bottom: 6px solid #4f5b6c;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    height: 0;
    width: 0;
    display: block;
    margin: 9px 0 0 4px;
    cursor: pointer;
  }
  .trangle-down {
    border-top: 6px solid #4f5b6c;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    height: 0;
    width: 0;
    display: block;
    margin: 9px 0 0 4px;
    cursor: pointer;
  }
  .search-template {
    display: flex;
    align-items: center;
    line-height: 22px;
    border-radius: 2px;
    margin: 0 6px;
    padding: 0 4px;
    border-radius: 4px;
    background-color: #f5f5f5;
    &-active {
      background-color: #d2deee;
    }
    .name {
      float: left;
      font-size: 14px;
      font-weight: normal;
      color: rgba(41, 41, 41, 1);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 82px;
      &-special {
        margin-right: 4px;
      }
      cursor: pointer;
      &:hover {
        color: var(--common-tp-column-handler-hover-bg);
      }
    }
    .remove {
      float: left;
      width: 16px;
      flex-shrink: 0;
      margin-right: 0;
      .mt-icons {
        color: var(--common-tp-column-remove-color);
        font-weight: 400;
        font-size: 14px;
        cursor: pointer;
        &:hover {
          color: var(--common-tp-column-handler-hover-bg);
        }
      }
    }
  }
  ::v-deep .e-dlg-container .column-selction {
    max-height: none !important;
  }
}
</style>
