/*
 * @Author: your name
 * @Date: 2021-08-12 16:27:35
 * @LastEditTime: 2021-08-12 17:18:43
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\components\loading\index.js
 */
import loadingVue from './loading'

const loading = {
  install: function (Vue) {
    let Layer = Vue.extend(loadingVue)
    let layer = new Layer()
    document.body.appendChild(layer.$mount().$el)

    Vue.prototype.$loading = (option) => {
      layer.params = {
        type: 'loading',
        show: true,
        txt: 'loading',
        isOpen: false,
        ...option
      }
    }

    Vue.prototype.$hloading = () => {
      layer.params.show = false
    }
  }
}

export default loading
