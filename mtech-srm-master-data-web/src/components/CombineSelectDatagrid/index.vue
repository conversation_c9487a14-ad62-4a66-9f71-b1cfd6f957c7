<template>
  <div class="combine-wrap" @click="mctClick">
    <mt-select
      :placeholder="placeholder"
      :readonly="true"
      :data-source="dataSource"
      v-model="typeId"
      :fields="{ text: 'dictName', value: 'id' }"
      @focus="gridShow = true"
    ></mt-select>

    <div class="table-wrap" v-show="gridShow">
      <mt-data-grid
        ref="dataGrid"
        :data-source="dataSource"
        :column-data="columnData"
        :allow-paging="true"
        :allow-filtering="false"
        :filter-settings="filterOptions"
        :page-settings="pageSettings"
        @rowSelected="getSelectedRecords"
        @sizeChange="handleSizeChange"
        @currentChange="handleCurrentChange"
      ></mt-data-grid>
      <div class="close-btn" @click="gridShow = false">关闭</div>
      <!-- <div class="close-btn" @click="handleClear">清除</div> -->
    </div>
  </div>
</template>

<script>
export default {
  props: {
    dictId: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    },
    columnData: {
      type: Array,
      default: () => []
    }
    // dataSource: {
    //   type: Array,
    //   default: () => []
    // },
    // pageSettings: {
    //   type: Object,
    //   default: () => {}
    // }
  },
  data() {
    return {
      gridShow: false,
      typeId: '',
      filterOptions: {
        type: 'Menu'
      },
      dataSource: [],
      pageSettings: {
        currentPage: 1,
        // 一页条数
        pageSize: 10,
        // 下拉可选一页展示条数
        pageSizes: [10, 20, 50, 100, 200],
        // 总条数
        totalRecordsCount: 10
      }
    }
  },

  watch: {
    dictId(newVal) {
      this.typeId = newVal
    }
  },

  mounted() {
    document.onclick = () => {
      this.gridShow = false
    }
    this.getDictTypeData()
  },

  methods: {
    getDictTypeData() {
      let params = {
        page: {
          size: this.pageSettings.pageSize,
          current: this.pageSettings.currentPage
        }
      }
      this.$loading()
      this.$API.baseMainData.getDictionaryTypeData(params).then((res) => {
        this.$hloading()
        this.dataSource = res.data.records
        this.pageSettings.totalRecordsCount = +res.data.total
      })
    },

    handleSizeChange(size) {
      this.pageSettings.pageSize = size
      this.getDictTypeData()
    },
    handleCurrentChange(current) {
      this.pageSettings.currentPage = current
      this.getDictTypeData()
    },

    getSelectedRecords() {
      let Obj = this.$refs.dataGrid.ejsRef.getSelectedRecords()
      console.log(Obj)
      this.typeId = Obj[0]?.id
      this.gridShow = false
      this.$emit('rowSelect', Obj[0])
    },

    mctClick(e) {
      e.stopPropagation()
      return false
    },

    handleClear() {
      this.typeId = null
      this.gridShow = false
      this.$emit('rowSelect', null)
    }
  }
}
</script>

<style lang="scss" scoped>
.combine-wrap {
  position: relative;

  .table-wrap {
    position: absolute;
    z-index: 99;
    top: 0;
    left: 0;
    background: #fff;
    opacity: 1;
    min-width: 600px;
    overflow: hidden;
    box-shadow: 0px 0px 6px #ccc;

    /deep/ .mt-data-grid {
      max-height: 400px;
      display: flex;
      flex-direction: column;
      .e-grid {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        border: 1px solid transparent;
        border-bottom: 1px solid #e0e0e0;

        .e-gridheader {
          flex-shrink: 0;
        }

        .e-gridcontent {
          flex: 1;
          overflow-y: auto;
        }
      }
      .mt-pagertemplate {
        margin: 10px 0 5px;
        height: auto;
        flex-shrink: 0;
      }
    }
    .close-btn {
      text-align: right;
      padding: 5px 20px 10px;
      cursor: pointer;
      background: #efefef;
    }
  }
}
</style>
