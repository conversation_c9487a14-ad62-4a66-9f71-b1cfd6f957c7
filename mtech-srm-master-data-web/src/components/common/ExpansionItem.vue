<!--
 * @Author: your name
 * @Date: 2021-10-27 13:54:29
 * @LastEditTime: 2021-11-01 11:11:21
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\components\common\ExpansionItem.vue
-->
<template>
  <div :class="['expansion-item__wrapper', dense ? 'expansion-item--dense' : 'mt-pa-20']">
    <div class="expansion-item--header full-width flex justify-between items-center">
      <div class="expansion-item--header__title mt-pl-10">{{ title }}</div>
      <div class="flex justify-between items-center">
        <slot name="actions"></slot>
        <mt-icon
          :name="visiable ? 'icon_Sort_up' : 'icon_Sort_down'"
          class="expansion-item--header__icon"
          @click.native="visiable = !visiable"
        />
      </div>
    </div>
    <div v-if="visiable" class="expansion-item--content mt-pt-20">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ExpansionItem',
  props: {
    dense: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visiable: true
    }
  }
}
</script>

<style lang="scss" scoped>
.expansion-item__wrapper {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  .expansion-item--header {
    &__title {
      font-size: 16px;
      font-family: PingFangSC;
      font-weight: 600;
      color: rgba(41, 41, 41, 1);
      border-left: 3px solid #00469c;
      border-radius: 2px 0 0 2px;
    }
    &__icon {
      font-size: 12px;
      color: #a3b0c6;
    }
  }
}
.expansion-item--dense {
  padding: 10px 20px;
}
</style>
