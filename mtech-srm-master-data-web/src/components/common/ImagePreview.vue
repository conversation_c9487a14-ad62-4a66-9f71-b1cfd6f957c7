<!--
 * @Author: your name
 * @Date: 2021-11-19 14:03:55
 * @LastEditTime: 2021-11-30 15:27:17
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\components\common\ImagePreview.vue
-->
<template>
  <div class="position-relative wrapper">
    <img class="full-size wrapper-image" :src="`${prefix}${path}`" alt="加载失败" />
    <div class="wrapper-actions">
      <slot>
        <div class="flex justify-end items-center full-size">
          <span class="wrapper-actions--btn" @click="del">{{ $t('删除') }}</span>
        </div>
      </slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    path: {
      type: String,
      default:
        '/src=http%3A%2F%2Fimg.redocn.com%2Fsheying%2F20150610%2Flansetiankongyiduobaiyun_4494244.jpg&refer=http%3A%2F%2Fimg.redocn.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=jpeg?sec=1639894368&t=3c2b71ef9a8e1873b21d43ab0eb682f2'
    },
    prefix: {
      type: String,
      default: 'https://gimg2.baidu.com/image_search'
    }
  },
  data() {
    return {}
  },
  methods: {
    delete() {
      this.$emit('del')
    }
  }
}
</script>

<style lang="scss" scoped>
.position-relative {
  position: relative;
  width: 248px;
  height: 116px;
}
.full-size {
  width: 100%;
  height: 100%;
}
.wrapper {
  &:hover &-actions {
    display: block;
  }
  &-image {
    position: absolute;
    left: 0;
    top: 0;
  }
  &-actions {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 36px;
    padding: 0 10px;
    display: none;
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0.79) 0%,
      rgba(0, 0, 0, 0.22) 61.61989865603147%,
      rgba(0, 0, 0, 0) 100%
    );
    &--btn {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgb(250, 250, 250);
    }
  }
}
</style>
