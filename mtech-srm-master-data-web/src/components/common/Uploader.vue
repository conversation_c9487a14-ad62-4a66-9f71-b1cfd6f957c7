<!--
 * @Author: your name
 * @Date: 2021-11-16 15:11:04
 * @LastEditTime: 2021-12-06 15:20:28
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\components\common\Uploader.vue
-->
<template>
  <mt-dialog ref="dialog" :header="header" :buttons="btns">
    <div>
      <div class="text-center template">
        <a
          :href="`${prefixPath}/static/${templateFileConfig.templateId}.xls`"
          target="_blank"
          :download="`标准导入模板<${templateFileConfig.templateId}>.xls`"
          >{{ $t('下载标准导入模板') }}</a
        >
      </div>
      <div class="flex justify-center">
        <mt-uploader
          v-bind="$attrs"
          v-on="$listeners"
          :async-settings="asyncSettings"
          :auto-upload="autoUpload"
          :uploading="uploading"
          :name="name"
          @success="success"
          :tips="tips"
        >
        </mt-uploader>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { HEADER_TOKEN_KEY, getToken } from '@mtech-sso/single-sign-on'

export default {
  props: {
    templateFileConfig: {
      type: Object,
      required: true,
      default() {
        return {}
      }
    },
    header: {
      type: String,
      required: false,
      default() {
        return this.$t('导入')
      }
    },
    name: {
      type: String,
      required: false,
      default: 'file'
    },
    autoUpload: {
      type: Boolean,
      required: false,
      default: true
    },
    asyncSettings: {
      type: Object,
      required: true,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      tips: {
        first: this.$t('请拖拽文件或点击上传'),
        second: this.$t('注：文件最大不可超过50M， 文件格式仅支持')
      },
      prefixPath: window.__POWERED_BY_QIANKUN__ ? '/masterData' : '',
      uploaderListeners: null,
      btns: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  methods: {
    open() {
      this.$refs.dialog.ejsRef.show()
    },
    uploading(args) {
      args.currentRequest.setRequestHeader(HEADER_TOKEN_KEY, getToken())
    },
    success(args) {
      let res = args.e.currentTarget.response
      res = res ? JSON.parse(res) : res
      if (res.code === 200) {
        this.$toast({
          content: this.$t('导入成功'),
          type: 'success'
        })
        this.save()
        this.$emit('success', args)
        this.$emit('uploadSuccess', args)
      } else {
        this.save()
        this.$toast({
          content: this.$t(res.msg),
          type: 'warning'
        })
      }
    },
    save() {
      this.cancel()
    },
    cancel() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
.template {
  height: 60px;
  line-height: 40px;
  font-size: 18px;
}
</style>
