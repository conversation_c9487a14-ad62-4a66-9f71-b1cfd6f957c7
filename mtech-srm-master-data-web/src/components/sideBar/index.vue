<template>
  <div
    class="e-control e-sidebar e-lib e-right e-transition e-touch e-over"
    :class="{ 'e-open': show, 'e-close': !show }"
  >
    <div class="close-item" @click="closeSideBar">
      <mt-icon name="arrowhead-right" />
    </div>
    <div class="side-bar-box">
      <slot></slot>
    </div>
  </div>
</template>

<script>
let documentFramge = document.createDocumentFragment()
export default {
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    show(nv) {
      if (nv) {
        let overlay = document.createElement('div')
        overlay.setAttribute('class', 'e-sidebar-overlay1')
        documentFramge.appendChild(overlay)
        document.querySelector('#masterdataApp').appendChild(documentFramge)
      } else {
        if (documentFramge) {
          let overlay = document.querySelector('.e-sidebar-overlay1')
          document.querySelector('#masterdataApp').removeChild(overlay)
        }
      }
    }
  },
  data() {
    return {}
  },
  methods: {
    closeSideBar() {
      // console.log(this.$parent);
      this.$parent.isShow = false
    }
  },
  created() {
    let overlay = document.querySelector('.e-sidebar-overlay1')
    if (overlay) {
      document.querySelector('#app').removeChild(overlay)
    }
  }
}
</script>

<style lang="scss" scoped>
.e-sidebar-overlay1 {
  background-color: rgba(0, 0, 0, 0.6);
  height: 100%;
  left: 0;
  opacity: 0.5;
  pointer-events: auto;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 99;
}

.side-bar-box {
  overflow: hidden;
  width: auto;
  z-index: 1001;
}

.close-item {
  position: absolute;
  left: -11px;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 60px;
  background: rgba(243, 243, 243, 1);
  border-radius: 8px 0 0 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transform: scale(0.8);
}

.ejs-sidebar {
  display: block;
}

.e-sidebar {
  -webkit-tap-highlight-color: transparent;
  background: #fff;
  height: 100%;
  overflow: visible !important;
  position: fixed;
  top: 0;
  transition: none;
  vertical-align: middle;
  visibility: hidden;
  will-change: transform;
  border-radius: 8px 0 0 8px;
}

.e-sidebar.e-right.e-open {
  -webkit-transform: translateX(0);
  -ms-transform: translateX(0);
  transform: translateX(0);
  transition: -webkit-transform 0.5s ease;
  transition: transform 0.5s ease;
  transition: transform 0.5s ease, -webkit-transform 0.5s ease;
  visibility: visible;
}

.e-sidebar.e-right.e-close {
  box-shadow: none;
  -webkit-transform: translateX(100%);
  -ms-transform: translateX(100%);
  transform: translateX(100%);
  transition: visibility 0.5s, -webkit-transform 0.5s ease;
  transition: transform 0.5s ease, visibility 0.5s;
  transition: transform 0.5s ease, visibility 0.5s, -webkit-transform 0.5s ease;
  visibility: hidden;
}

.e-sidebar.e-left.e-open {
  -webkit-transform: translateX(0);
  -ms-transform: translateX(0);
  transform: translateX(0);
  transition: -webkit-transform 0.5s ease;
  transition: transform 0.5s ease;
  transition: transform 0.5s ease, -webkit-transform 0.5s ease;
  visibility: visible;
}

.e-sidebar.e-right.e-transition.e-close {
  transition: visibility 0.5s, -webkit-transform 0.5s ease;
  transition: transform 0.5s ease, visibility 0.5s;
  transition: transform 0.5s ease, visibility 0.5s, -webkit-transform 0.5s ease;
}

.e-sidebar.e-right {
  border-left: 1px solid rgba(0, 0, 0, 0.12);
  left: auto;
  right: 0;
  top: 0;
}
</style>
