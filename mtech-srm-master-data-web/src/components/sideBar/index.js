/*
 * @Author: your name
 * @Date: 2021-08-12 16:27:35
 * @LastEditTime: 2021-08-12 17:18:54
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\components\sideBar\index.js
 */
import sideBar from './index.vue'

const instance = {
  install: function (Vue) {
    let Layer = Vue.extend(sideBar)
    let layer = new Layer()
    document.body.appendChild(layer.$mount().$el)

    Vue.prototype.$sideBar = () => {
      layer.params.show = true
    }

    Vue.prototype.$hsideBar = () => {
      layer.params.show = false
    }
  }
}

export default instance
