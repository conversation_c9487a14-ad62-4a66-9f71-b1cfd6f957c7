<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog upload-excel-dialog"
    :header="$t('上传')"
    :buttons="buttons"
    @close="handleClose"
  >
    <div class="tips" v-show="isShowTips">
      <span>{{ $t('请使用批量导入模板') }}</span>
      <span class="downs" @click="downloadTemplate">
        <mt-icon name="icon_list_download"></mt-icon>
        {{ $t('下载模板') }}</span
      >
    </div>
    <div>
      <upload-file ref="uploader" :is-single-file="true" @change="fileChange"></upload-file>
      <div v-if="isShowError">
        {{ $t('文件内容存在错误，下载请点击')
        }}<span class="downs" @click="downloadResult">{{ $t('此处') }}</span>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { download, getHeadersFileNameExcel } from '@/utils/file.js'
import UploadFile from '@/components/Upload/cellUploadExCel.vue'
import Vue from 'vue'
import { i18n } from '@/main.js'
export default {
  components: {
    UploadFile
  },
  props: {
    // 下载模板的组合参数
    downTemplateParams: {
      type: Object,
      default: () => {}
    },
    // 上传参数
    uploadParams: {
      type: Object,
      default: () => {}
    },
    /**
     * 请求地址：
     * requestUrls:
     *   templateUrlPre: purchaseRequest
     *   templateUrl, //模板地址（例如 downloadItemTemplate）；
     *   uploadUrl    //上传地址（例如 uploadRequestItem）
     */
    requestUrls: {
      type: Object,
      default: () => {}
    },
    downTemplateName: {
      type: String,
      default: ''
    },
    // 是否显示 下载模板 提示，默认显示
    isShowTips: {
      type: Boolean,
      default: () => true
    }
  },
  data() {
    return {
      isShowError: false,
      resultExcel: null,
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      uploadData: null,
      fileLength: 0
    }
  },
  methods: {
    handleClose() {
      this.uploadData = []
      this.isShowError = false
      this.$emit('closeUploadExcel')
    },
    fileChange(files, data) {
      this.fileLength = files.length
      this.uploadData = data
      if (!data) {
        this.isShowError = false
        this.resultExcel = null
      }
    },
    // 下载模板
    downloadTemplate() {
      this.$API[this.requestUrls.templateUrlPre]
        [this.requestUrls.templateUrl](this.downTemplateParams)
        .then((res) => {
          const fileName = getHeadersFileNameExcel(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
    },

    // 下载上传结果（失败结果）
    downloadResult() {
      const fileName = getHeadersFileNameExcel(this.resultExcel)
      download({
        fileName: `${fileName}`,
        blob: this.resultExcel.data
      })
    },

    confirm() {
      this.$loading()
      if (!this.fileLength) {
        this.$toast({ content: this.$t('请选择上传文件!'), type: 'error' })
        return
      }
      if (this.uploadParams != null) {
        Object.keys(this.uploadParams).forEach((item) => {
          this.uploadData.append(item, this.uploadParams[item])
        })
      }
      this.$API[this.requestUrls.templateUrlPre]
        [this.requestUrls.uploadUrl](this.uploadData)
        .then((res) => {
          this.$hloading()
          let _this = this
          if (res.data?.type === 'application/json') {
            const reader = new FileReader()
            reader.readAsText(res.data, 'utf-8')
            reader.onload = function () {
              try {
                const readerRes = reader.result
                const resObj = JSON.parse(readerRes)
                console.log(resObj)
                if (resObj?.code == 200) {
                  _this.$emit('upExcelConfirm', resObj)
                } else {
                  Vue.prototype.$toast({
                    content: resObj.msg,
                    type: 'error'
                  })
                }
              } catch (error) {
                console.log(error)

                Vue.prototype.$toast({
                  content: i18n.t('响应数据格式错误，解析失败'),
                  type: 'error'
                })
                console.warn(error)
              }
            }
          } else if (typeof res.data === 'string' && !res.errorStackTrace) {
            this.$toast({ content: this.$t(`${res.data}`), type: 'warning' })
            return
          } else if (res.data && res.data.msg) {
            _this.$emit('upExcelConfirm', res.data.msg)
          } else if (!res.data && res.msg === this.$t('执行成功')) {
            _this.$emit('upExcelConfirm', res.msg)
          } else if (res.code === 200 && !res.data && res.msg) {
            _this.$emit('upExcelConfirm', res.msg)
          } else {
            // 导入失败 返回'application/x-msdownload'类型文件流
            this.isShowError = true
            this.resultExcel = res
            this.$toast({ content: this.$t('导入失败！'), type: 'warning' })
          }
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('导入失败，请重试'),
            type: 'error'
          })
        })
    }
  }
}
</script>

<style lang="scss">
.upload-excel-dialog {
  .tips {
    margin-bottom: 15px;
  }
  .downs {
    margin-left: 10px;
    color: #005ca9;
    cursor: pointer;
  }
}
</style>
