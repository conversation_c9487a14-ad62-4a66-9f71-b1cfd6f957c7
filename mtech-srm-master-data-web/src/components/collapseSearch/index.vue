<template>
  <div class="collapse-search-container toggle-container" :class="{ isExpended: isExpended }">
    <div class="header-bar">
      <div class="toggle-tag" @click="isExpended = !isExpended">
        <span>{{ isExpended ? $t('收起') : $t('展开') }}</span>
        <i
          class="mt-icons mt-icon-MT_DownArrow"
          :class="isExpended ? 'expendIcon' : 'unExpendIcon'"
        />
      </div>
      <div v-if="showButton" class="button-group" :class="{ onlyButton: !isExpended }">
        <slot name="buttons-bar">
          <span type="info" @click="reset()">{{ $t('重置') }}</span>
          <span type="primary" @click="search()">{{ $t('查询') }}</span>
        </slot>
      </div>
    </div>
    <div
      class="search-area"
      :class="{ opend: isExpended, 'display-gird': isGridDisplay, 'main-form': !isGridDisplay }"
      :style="getCustomStyle()"
      @keyup.enter="handleKeyup"
    >
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    backgroundColor: {
      type: String,
      require: false,
      default: '#f9f9f9'
    },
    showButton: {
      type: Boolean,
      require: false,
      default: true
    },
    defaultMaxHeight: {
      type: [String, Number],
      default: '60'
    },
    maxHeight: {
      type: String,
      default: '600px'
    },
    minRows: {
      type: [String, Number],
      default: 1
    },
    // 是否为gird布局
    isGridDisplay: {
      type: Boolean,
      default: false
    },
    expended: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isExpended: false
    }
  },
  watch: {
    expended: {
      handler: function (val) {
        this.isExpended = val
      },
      immediate: true
    }
  },
  methods: {
    handleKeyup(e) {
      if (e?.keyCode === 13) {
        this.search()
      }
    },
    getCustomStyle() {
      return {
        backgroundColor: this.backgroundColor,
        maxHeight: this.isExpended
          ? this.maxHeight
          : this.minRows
          ? `${this.minRows * Number(this.defaultMaxHeight)}px`
          : `${this.defaultMaxHeight}px`
      }
    },
    reset() {
      this.$emit('reset')
    },
    search() {
      this.$emit('search')
    }
  }
}
</script>

<style lang="scss" scoped>
.collapse-search-container {
  padding: 0px 0px 12px;
  .header-bar {
    height: 36px;
    background: #f9f9f9;
    border-bottom: 1px dashed #e9e9e9;
    .toggle-tag {
      height: 100%;
      float: left;
      color: #2783fe;
      display: flex;
      align-items: center;
      font-size: 14px;
      position: relative;
      cursor: pointer;
      user-select: none;
      margin-left: 12px;
      .mt-icons {
        font-size: 12px;
        margin-left: 2px;
      }
      .expendIcon {
        transform: rotate(180deg);
        margin-top: -4px;
      }
      .unExpendIcon {
        margin-top: 4px;
      }
    }
    .button-group {
      height: 100%;
      float: right;
      border-radius: 0 0 8px 8px;
      display: flex;
      align-items: center;
      margin-right: 12px;
      span {
        display: block;
        height: 28px;
        line-height: 26px;
        padding: 0 16px;
        border-radius: 4px;
        margin-left: 12px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        cursor: pointer;
        user-select: none;
        border: 1px solid #4a556b;
        box-sizing: border-box;
      }
      span[type='info'] {
        background: #fff;
        color: #4a556b;
      }
      span[type='primary'] {
        background: #4a556b;
        color: #fff;
      }
    }
  }

  .search-area {
    background: #f9f9f9;
    border-radius: 8px 8px 0 0;
    padding-bottom: 12px;
    transition: max-height 0.5s cubic-bezier(0, 1, 0, 1);
    // position: relative;
    overflow: hidden;
    // display: none;
  }
  .main-form {
    ::v-deep .mt-form-item {
      width: calc(33% - 20px);
      min-width: 260px;
      display: inline-flex;
      margin-top: 5px;
      margin-right: 20px;
      margin-bottom: 8px;
      .full-width {
        width: calc(100% - 20px) !important;
      }
      .e-ddt .e-ddt-icon {
        background: unset;
        &::before {
          content: '\e36a';
          font-size: 16px;
        }
      }
      .mt-form-item-label {
        .label {
          width: 120px;
          text-align: right;
        }
        div:last-child {
          width: calc(100% - 120px);
        }
      }
      .error-label-label {
        padding-top: 0px;
      }
    }
    .check-area {
      transform: translateY(10px);
    }
  }
  .display-gird {
    // margin: 0px 10px 0 10px;
    // padding-bottom: 8px;
    // background: #f5f5f5;
    border-radius: 0 0 8px 8px;
    .mt-form {
      width: 100%;
      padding: 5px 10px 0 11px;
      box-sizing: border-box;
      display: grid;
      grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
      justify-content: space-between;
      grid-gap: 10px;
      padding: 10px 10px 0 10px;
      ::v-deep .mt-form-item {
        // width: 300px;
        margin-bottom: 0px;
        .error-label-label-top {
          top: 86% !important;
        }
        ::v-deep .mt-form-item-label {
          .label {
            text-align: left !important;
            margin-right: 5px !important;
          }
          div:nth-child(2) {
            width: inherit;
          }
          .ant-select-selection {
            background-color: rgba(245, 245, 245, 0);
          }
          .operator-list {
            width: 80px !important;
            margin-right: 5px;
          }
          .custom-input-number {
            width: 100%;
            .mt-input-number {
              width: 100%;
              margin-bottom: 0px;
            }
          }
        }
        .mt-input {
          width: 100%;
        }
        ::v-deep .ant-select-selection {
          background-color: transparent;
        }
      }
    }
  }
  .onlyButton {
    border-radius: 8px;
  }
}
.isExpended {
  .opend {
    display: block;
    height: auto;
    transition: max-height 0.5405s linear;
  }
}
</style>
