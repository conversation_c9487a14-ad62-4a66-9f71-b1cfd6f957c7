<template>
  <div
    v-if="isExternal"
    :style="styleExternalIcon"
    class="svg-external-icon svg-icon"
    v-on="$listeners"
  />
  <div
    v-else
    class="mt-tooltip-container"
    @mouseenter="svgMouseEnterEvent"
    @mouseleave="svgMouseLeaveEvent"
  >
    <!-- <mt-tooltip opens-on="Custom" :content="svgTooltip" ref="tooltipForSvgIcon">
      <svg :class="svgClass" aria-hidden="true" v-on="$listeners">
        <use :xlink:href="iconName" />
      </svg>
    </mt-tooltip> -->
  </div>
</template>

<script>
export default {
  name: 'SvgIcon',
  props: {
    iconClass: {
      type: String,
      required: true
    },
    className: {
      type: String,
      default: ''
    },
    svgTooltip: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  computed: {
    isExternal() {
      return /^(https?:|mailto:|tel:)/.test(this.iconClass)
    },
    iconName() {
      return `#icon-${this.iconClass}`
    },
    svgClass() {
      if (this.className) {
        return 'svg-icon ' + this.className
      } else {
        return 'svg-icon'
      }
    },
    styleExternalIcon() {
      return {
        mask: `url(${this.iconClass}) no-repeat 50% 50%`,
        '-webkit-mask': `url(${this.iconClass}) no-repeat 50% 50%`
      }
    }
  },
  methods: {
    svgMouseEnterEvent(args) {
      if (this.svgTooltip) {
        this.$refs.tooltipForSvgIcon.open(args.target)
      }
    },
    svgMouseLeaveEvent(args) {
      if (this.svgTooltip) {
        this.$refs.tooltipForSvgIcon.close(args.target)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.mt-tooltip-container {
  display: inline-block;
  .mt-tooptip {
    width: auto;
  }
}

.svg-icon {
  cursor: pointer;
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
  &:hover {
    color: #00469c;
  }
}

.svg-external-icon {
  background-color: currentColor;
  mask-size: cover !important;
  display: inline-block;
}
</style>
