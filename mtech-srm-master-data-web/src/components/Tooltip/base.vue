<template>
  <transition name="fade" @after-leave="destroyedToast">
    <!-- <mt-tooltip
      class="tooltip-component"
      :content="content"
      ref="tooltipForComponent"
      @beforeClose="destroyedToast"
      :style="toastContainerStyle"
      :is-sticky="isSticky"
      :position="position"
      :open-delay="openDelay"
      :close-delay="closeDelay"
    >
      <div :style="targetStyle"></div>
    </mt-tooltip> -->
  </transition>
</template>

<script>
export default {
  name: 'Tooltip',
  props: {
    content: {
      type: String,
      default: ''
    },
    isSticky: {
      type: Boolean,
      default: false
    },
    position: {
      type: String,
      default: 'TopCenter'
    },
    coordinate: {
      type: Object,
      default: () => {
        return {
          offsetTop: 0,
          offsetLeft: 0,
          offsetWidth: 0,
          offsetHeight: 0,
          clientWidth: 0,
          clientHeight: 0
        }
      }
    },
    openDelay: {
      type: Number,
      default: 0
    },
    closeDelay: {
      type: Number,
      default: 0
    },
    timeOut: {
      type: Number,
      default: 500000
    }
  },
  data() {
    return {}
  },
  computed: {
    toastContainerStyle() {
      return {
        width: this.coordinate.clientWidth + 'px',
        height: this.coordinate.clientHeight + 'px',
        left: this.coordinate.offsetLeft + 'px',
        top: this.coordinate.offsetTop + 'px'
      }
    },
    targetStyle() {
      return {
        width: this.coordinate.clientWidth + 'px',
        height: this.coordinate.clientHeight + 'px'
      }
    }
  },
  mounted() {
    setTimeout(() => {
      this.$refs.tooltipForComponent.open(this.$refs.tooltipForComponent.$el)
      if (!this.isSticky) {
        setTimeout(() => {
          this.$refs.tooltipForComponent.close(this.$refs.tooltipForComponent.$el)
        }, 2000)
      }
    }, 0)
  },
  methods: {
    destroyedToast() {
      this.$emit('close')
    }
  },
  beforeDestory() {
    this.$emit('close')
  }
}
</script>

<style lang="scss" scoped>
.tooltip-component {
  width: 0;
  height: 0;
  background: transparent;
  position: absolute;
}
</style>
