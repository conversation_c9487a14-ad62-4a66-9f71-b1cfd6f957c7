/*
 * @Author: your name
 * @Date: 2021-08-12 16:27:35
 * @LastEditTime: 2021-08-12 17:19:24
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\components\Tooltip\index.js
 */
import Vue from 'vue'
import MtTooltip from './base.vue'

const MtTooltipConstructor = Vue.extend(MtTooltip)

const NAME = '$tooltip'
const COMPONENT = (options) => {
  const { ...rest } = options
  const instance = new MtTooltipConstructor({
    propsData: {
      ...rest
    }
  })
  console.log('options', options)
  instance.vm = instance.$mount()
  options.target.after(instance.vm.$el)
  // document.body.appendChild(instance.vm.$el);

  instance.vm.visible = true
  instance.vm.$on('closed', () => {
    console.warn('mt-tooltip-closed')
    instance.vm.$destroy()
  })

  instance.vm.$on('close', () => {
    console.warn('mt-tooltip-close')
    instance.vm.visible = false
    instance.vm.$el.remove()
  })

  return instance.vm
}
export { NAME, COMPONENT }
