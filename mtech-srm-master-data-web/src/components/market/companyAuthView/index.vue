// 授权公司弹出框
<template>
  <mt-dialog ref="dialog" :header="header" :buttons="buttons" @beforeClose="cancel">
    <div class="dialog-content">
      <div class="table-container">
        <div class="table-title">
          {{ $t(`已授权公司（总计`) }}
          {{ authList.length }}
          {{ $t(`家公司）`) }}
        </div>
        <div class="table-content">
          <mt-template-page ref="authRef" :template-config="authConfig" />
        </div>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { utils } from '@mtech-common/utils'
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('确认') }
        }
      ],
      factorInfo: {
        id: null
      },
      authToolTip: {
        msg: '',
        enableMove: false
      },
      unAuthToolTip: {
        msg: '',
        enableMove: false
      },
      allList: [], //全部公司列表
      unAuthList: [], //未授权公司列表
      authList: [], //当前授权公司列表
      originList: [], //初始获取的已授权公司列表
      authConfig: [
        {
          grid: {
            allowFiltering: true,
            allowPaging: false,
            columnData: [
              {
                field: 'relationCode',
                headerText: this.$t('公司编码')
              },
              {
                field: 'relationName',
                headerText: this.$t('公司名称')
              }
            ],
            dataSource: []
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.$t('公司信息')
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.factorInfo = { ...this.modalData.data }
      this.getAuthList()
    }
  },
  methods: {
    //获取当前授权列表
    getAuthList() {
      if (this.factorInfo.id) {
        const DEFAULTPARAM = {
          page: {
            current: 1,
            size: 1000
          },
          pageFlag: false,
          condition: '',
          relationType: 1,
          defaultRules: [
            {
              label: '因子ID',
              field: 'costFactorId',
              type: 'number',
              operator: 'equal',
              value: this.factorInfo.id
            }
          ]
        }
        this.modalData.getAuthList(DEFAULTPARAM).then((res) => {
          this.originList = utils.cloneDeep(res.data.records)
          this.authList = utils.cloneDeep(res.data.records)
          this.$set(this.authConfig[0].grid, 'dataSource', this.authList)
        })
      } else if (this.modalData.localList) {
        this.originList = utils.cloneDeep(this.modalData.localList)
        this.authList = utils.cloneDeep(this.modalData.localList)
        this.$set(this.authConfig[0].grid, 'dataSource', this.authList)
      }
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  padding: 20px 0 0 0;
  .table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    .table-title {
      flex-shrink: 0;
      font-size: 16px;
      color: #292929;
      display: inline-block;
      padding-left: 13px;
      position: relative;
      margin-bottom: 20px;

      &:before {
        content: '';
        position: absolute;
        width: 3px;
        height: 14px;
        background: #6386c1;
        border-radius: 0 2px 2px 0;
        left: 0;
        top: 2px;
      }
    }
    .table-content {
      flex: 1;
      border-bottom: 1px solid #e8e8e8;
    }
  }
  .cross-container {
    width: 60px;
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    .mt-tooptip {
      width: auto;
      margin-top: 30px;
      &:first-of-type {
        margin-top: 0;
      }
      .cross {
        background: #fbfbfb;
        height: 30px;
        width: 30px;
        border-radius: 50%;
        position: relative;
        border: 2px solid #6386c1;
        &:after {
          content: '';
          width: 10px;
          height: 10px;
          background: 0 0;
          border-left: none;
          border-top: none;
          border-right: 2px solid #6386c1;
          border-bottom: 2px solid #6386c1;
          position: absolute;
          top: 8px;
          left: 6px;
          transform: rotate(-45deg);
        }
        &.cross-left {
          &:after {
            border-left: 2px solid #6386c1;
            border-top: 2px solid #6386c1;
            border-right: none;
            border-bottom: none;
            left: 10px;
          }
        }
        &.cross-disabled {
          border-width: 1px;
          border-color: #9daabf;
          &:after {
            border-width: 1px;
            border-color: #9daabf;
          }
        }
      }
    }
  }
}
</style>
