## 顶部按钮工具栏说明

- 因为目前集成的可拖拽的列配置按钮，导致同 vxe 自带的 toolbar 会产生样式上错乱，所以目前的做法是先放弃使用 vxe 的 toolbarConfig

插槽内部的按钮循环渲染可以参考以下代码，vxe 自带的 toolbar 事件也只是提供了按钮编码以及表格的 ref，可以通过以下代码进行替代

```javascript=
const toolBar = [
  { code: 'ForecastAdd', name: i18n.t('新增'), icon: 'vxe-icon-square-plus', status: 'primary' },
  {
    code: 'ForecastDelete',
    name: i18n.t('删除'),
    icon: 'vxe-icon-delete',
    status: 'primary'
  }
]
```

```html=
<template slot="custom-tools">
  <vxe-button
    v-for="item in toolbar"
    :key="item.code"
    :status="item.status"
    :icon="item.icon"
    size="small"
    @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
    >{{ item.name }}</vxe-button>
</template>
```
