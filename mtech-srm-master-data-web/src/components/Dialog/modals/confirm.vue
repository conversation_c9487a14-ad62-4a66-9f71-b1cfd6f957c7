<template>
  <div class="modal">
    <div class="modal-dialog modal-dialog-small">
      <div class="modal-header">
        <h4 class="title">{{ modal['title'] }}</h4>
        <a class="icon-false close" @click="cancel()"></a>
      </div>
      <div class="modal-content">
        <form class="form-inline form-responsive">
          <div class="columns modal-margin">
            <div class="form-group">
              <div class="form-content">
                <h4 class="title">{{ modal['message'] }}</h4>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <p class="button-group">
          <mt-button :is-primary="true" @click.native="confirm" css-class="e-info">{{
            $t('确认')
          }}</mt-button>
          <mt-button @click.native="cancel">{{ $t('取消') }}</mt-button>
        </p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    modal() {
      return Object.assign(
        {
          title: this.$t('操作'),
          message: this.$t('要删除该数据吗？')
        },
        this.modalData
      )
    }
  },
  mounted() {},
  methods: {
    confirm() {
      if (Object.prototype.hasOwnProperty.call(this.modalData, 'confirm')) {
        this.modalData.confirm().then((res) => {
          this.$toast({
            content: res.message ? res.message : this.$t('操作成功'),
            type: 'success'
          })
          this.$emit('confirm-function', res.message)
        })
      } else {
        this.$emit('confirm-function')
      }
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
