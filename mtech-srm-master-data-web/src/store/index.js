/*
 * @Author: your name
 * @Date: 2021-08-12 16:27:35
 * @LastEditTime: 2021-08-12 17:02:58
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\store\index.js
 */
import Vue from 'vue'
import Vuex from 'vuex'
import createPersistedState from 'vuex-persistedstate'
import roleManagement from './modules/roleManagement'

Vue.use(Vuex)

export default new Vuex.Store({
  plugins: [createPersistedState()],
  ...roleManagement
})
