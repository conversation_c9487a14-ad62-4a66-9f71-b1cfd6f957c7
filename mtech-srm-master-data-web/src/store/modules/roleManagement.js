/*
 * @Author: your name
 * @Date: 2021-08-12 16:27:35
 * @LastEditTime: 2021-08-12 17:12:34
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\store\modules\roleManagement.js
 */
const state = {
  roleRowInfo: {},
  user: {}
}

const mutations = {
  setRoleRowInfo(state, data) {
    state.roleRowInfo = data
  },
  setUser(state, data) {
    state.user = data
  }
}

const actions = {}

export default {
  state,
  mutations,
  actions
}
