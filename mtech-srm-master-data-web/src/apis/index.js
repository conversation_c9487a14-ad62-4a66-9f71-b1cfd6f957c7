/*
 * @Author: your name
 * @Date: 2021-08-19 15:47:13
 * @LastEditTime: 2021-08-19 15:55:31
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\apis\index.js
 */
const modules = {}

const apiJSFile = require.context('./modules', true, /\.js$/)
// console.log("apiJSFile", apiJSFile);

apiJSFile.keys().forEach((key) => {
  const mod = apiJSFile(key)
  modules[mod.NAME] = mod.__esModule && mod.default ? mod.default : mod
})

export default modules
