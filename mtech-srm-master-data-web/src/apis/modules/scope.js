import { API as $axios } from '@mtech-common/http'
$axios.$post = $axios.post
$axios.$get = $axios.get
const IAM = `/iam/tenant`
const IAMMASTER = `/iam/admin`
/**
 * @description: 规则明细 - 查询
 * @param {*}
 * @return {*}
 */
// 租户明细查询
export const QueryDataItemApi = `${IAM}/permission-data/query/item/page`
// 平台用户明细查询
export const QueryDataItemApiPlatform = `${IAMMASTER}/permission-data/query/item/page`

export const NAME = 'scope'
/**
 * @description: 维度 - 查询 srm
 * @param {*}
 * @return {*}
 */
export const QueryDimensionApi = (data) => {
  return $axios.$post(`${IAM}/permission-dimension/query`, data)
}

/**
 * @description: 维度 - 查询admin
 * @param {*}
 * @return {*}
 */
export const QueryDimensionApiAdmin = (data) => {
  return $axios.$post(`${IAMMASTER}/permission-dimension/query`, data)
}
/**
 * @description: 规则 - 保存/编辑srm
 * @param {*}
 * @return {*}
 */
export const QueryDataSaveApi = (data) => {
  return $axios.$post(`${IAM}/permission-data/save`, data)
}
/**
 * @description: 规则 - 保存/编辑admin
 * @param {*}
 * @return {*}
 */
export const QueryDataSaveApiAdmin = (data) => {
  return $axios.$post(`${IAMMASTER}/permission-data/save`, data)
}
/**
 * @description: 绑定 - 用户组 平台
 *
 * @param {*}
 * @return {*}
 */
export const DataBindApiAdmin = (data) => {
  return $axios.$post(`${IAMMASTER}/permission-data/bind`, data)
}

/**
 * @description: 绑定 - 规则-srm
 * @param {*}
 * @return {*}
 */
export const DataBindApi = (data) => {
  return $axios.$post(`${IAM}/permission-data/bind`, data)
}
/**
 * @description: 解绑 - 规则
 * @param {*}
 * @return {*}
 */
export const DataUnbindApi = (data) => {
  return $axios.$post(`${IAM}/permission-data/unbind`, data)
}

/**
 * @description: 解绑 - 规则
 * @param {*}
 * @return {*}
 */
export const DataUnbindApiAdmin = (data) => {
  return $axios.$post(`${IAMMASTER}/permission-data/unbind`, data)
}
/**
 * @description: 元素 - 查询
 * @param {*}
 * @return {*}
 */
export const QueryMetadataApi = (data) => {
  return $axios.$post(`${IAM}/metadata-table/query/page`, data)
}
/**
 * @description: 用户组 - 查询- admin
 * @param {*}
 * @return {*}
 */
// 绑定用户查询
export const QueryUserGroupApi = `/iam/admin/user-group/paged-query`

/**
 * @description: 用户组 - 查询- srm
 * @param {*}
 * @return {*}
 */
export const QueryUserGroupApiTanent = `/iam/tenant/user-group/paged-query`
/**
 * @description: 用户 - 查询-admin
 * @param {*}
 * @return {*}
 */
export const QueryUserApi = `/iam/admin/account/users/page`
/**
 * @description: 用户 - 查询-admin
 * @param {*}
 * @return {*}
 */
export const QueryUserApiTenant = `/iam/admin/account/users/page`
/**
 * @description: 业务类型 - 查询 srm
 * @param {*}
 * @return {*}
 */
export const QueryTableApi = (data) => {
  return $axios.get(`${IAM}/metadata-table/query/list`, data)
}
/**
 * @description: 业务类型 - 查询 admin
 * @param {*}
 * @return {*}
 */
export const QueryTableApiAdmin = (data) => {
  return $axios.get(`${IAMMASTER}/metadata-table/query/list`, data)
}
/**
 * @description: 权限规则 - 删除
 * @param {*}
 * @return {*}
 */
export const DeleteRulesApi = (data) => {
  return $axios.$post(`${IAM}/permission-data/remove`, data)
}

/**
 * @description: 权限规则 - 删除
 * @param {*}
 * @return {*}
 */
export const DeleteRulesApiAdmin = (data) => {
  return $axios.$post(`${IAMMASTER}/permission-data/remove`, data)
}

/**
 * @description: 权限规则明细 - 删除 srm
 * @param {*}
 * @return {*}
 */
export const DeleteRulesDetailApi = (data) => {
  return $axios.$post(`${IAM}/permission-data/remove/item`, data)
}
/**
 * @description: 权限规则明细 - 删除  admin
 * @param {*}
 * @return {*}
 */
export const DeleteRulesDetailApiAdmin = (data) => {
  return $axios.$post(`${IAMMASTER}/permission-data/remove/item`, data)
}

/**
 * @description: 权限规则树 - 查询srm
 * @param {*}
 * @return {*}
 */
export const QueryRulesTreeApi = (data) => {
  return $axios.$post(`${IAM}/permission-data/query/list`, data)
}
/**
 * @description: 权限规则树 - 查询admin
 * @param {*}
 * @return {*}
 */
export const QueryRulesTreeApiAdmin = (data) => {
  return $axios.$post(`${IAMMASTER}/permission-data/query/list`, data)
}
/**
 * @description: 规则明细 - 编辑 srm
 * @param {*}
 * @return {*}
 */
export const UpdateItemAPi = (data) => {
  return $axios.$post(`${IAM}/permission-data/modify/item`, data)
}

/**
 * @description: 规则明细 - 编辑 srm
 * @param {*}
 * @return {*}
 */
export const UpdateItemAPiAdmin = (data) => {
  return $axios.$post(`${IAMMASTER}/permission-data/modify/item`, data)
}

export const FilterItemApi = (data) => {
  return $axios.get(`/iam/common/selectable/comparisonEnum`, data)
}
// 新增明细srm
export const itemSaveApi = (data) => {
  return $axios.$post(`${IAM}/permission-data/save/item`, data)
}
// 新增明细Admin
export const itemSaveApiAdmin = (data) => {
  return $axios.$post(`${IAMMASTER}/permission-data/save/item`, data)
}

export const itemUpdateApi = (data) => {
  return $axios.$post(`${IAM}/permission-data/modify/item`, data)
}

// 已绑定用户组 srm
export const QueryBindDetailGroupApi = `${IAM}/subject-permission-data-rel/user-group/page`
// 已绑定用户 platfrom
export const QueryBindDetailGroupApiPlatform = `${IAMMASTER}/subject-permission-data-rel/user-group/page`
// 已绑定用户 srm
export const QueryBindDetailUserApi = `${IAM}/subject-permission-data-rel/user/page`
// 已绑定用户 platfrom
export const QueryBindDetailUserApiPlatform = `${IAMMASTER}/subject-permission-data-rel/user/page`
// 租户级-用户接口-当前租户分页查询用户信息
export const userTenantPageQuery = `/masterDataManagement/tenant/user/currentTenantPagedQuery`
