/**
 * 物料 item
 */

import { API } from '@mtech-common/http'
export const NAME = 'item'

const PROXY_MASTER_DATA = '/masterDataManagement'

const APIS = {
  pagedQueryPost: (data) => {
    return API.post(
      `${PROXY_MASTER_DATA}/tenant/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
      data
    )
  },
  pagedQueryCategoriesPost: (data) => {
    return API.post(`${PROXY_MASTER_DATA}/tenant/permission/queryCategories`, data)
  },
  pagedQuery: `${PROXY_MASTER_DATA}/tenant/item/paged-query?BU_CODE=${localStorage.getItem(
    'currentBu'
  )}`
}

export default APIS
