import { API as $axios } from '@mtech-common/http'
$axios.$post = $axios.post
$axios.$get = $axios.get
// 字典
export const NAME = 'dataScope'
const IAM = `/iam/tenant`
// const IAMMASTER = `/iam/admin`
const urlPath = {
  role: '/iam',
  tree: '/masterDataManagement'
}
// 主数据相关

/**
 * @description: 获取所有业务类型    树
 * @param {*}
 * @return {*}
 */

export const getBusinessApi = (data = { dictCode: 'businessType' }) =>
  $axios.post(`${urlPath.tree}/tenant/dict-item/dict-code`, data)

export const getPriceTypeApi = (data = { dictCode: 'PRICE_CATEGORY' }) =>
  $axios.post(`${urlPath.tree}/tenant/dict-item/dict-code`, data)

// 获取绩效模板类型

export const getPerformanceTemApi = (data = { dictCode: 'MB-TYPE' }) =>
  $axios.post(`${urlPath.tree}/tenant/dict-item/dict-code`, data)
export const getReconciliaTypeApi = (data = { dictCode: 'RECONCILIATION_TYPE' }) =>
  $axios.post(`${urlPath.tree}/tenant/dict-item/dict-code`, data)

/**
 * @description: 获取所有公司树结构
 * @param {*}
 * @return {*}
 */

export const getCompayTreeApi = (data = { orgLevelCode: 'ORG02' }) =>
  $axios.post(`${urlPath.tree}/tenant/organization/getStatedLimitTree`, data)

/**
 * @description: 获取所有工厂树结构
 * @param {*}
 * @return {*}
 */

export const getSiteTreeApi = (data = { orgLevelCode: 'ORG06' }) =>
  $axios.post(`${urlPath.tree}/tenant/organization/getStatedLimitTree`, data)

/**
 * @description: 获取所有采购组织树结构
 * @param {*}
 * @return {*}
 */

export const getOrgTreeApi = (data) =>
  //  {organizationTypeCode:"BUORG002ADM"} 采购组织
  $axios.post(
    `${urlPath.tree}/tenant/business-organization/criteria-query?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`,
    data
  )

/**
 * @description: 获取所有采购组树结构
 * @param {*}
 * @return {*}
 */

export const getOrgGroupTreeApi = (data) =>
  //  {groupTypeCode:"BG001JH"} 计划组织
  //  {groupTypeCode:"BG001CG"}  采购组
  $axios.post(
    `${urlPath.tree}/tenant/business-group/criteria-query?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`,
    data
  )
/**
 * @description: 获取所有供应商树结构
 * @param {*}
 * @return {*}
 */

export const getSupplierApi = `${urlPath.tree}/tenant/supplier/paged-query-distinct-no-scope`

/**
 * @description: 获取所有客户树结构
 * @param {*}
 * @return {*}
 */

export const getCustomApi = `${urlPath.tree}/tenant/customer/paged-query`

/**
 * @description: 获取所有用户列表分页
 * @param {*}
 * @return {*}
 */
export const getEmployeeApi = `${urlPath.tree}/tenant/user/currentTenantPagedQuery`

/**
 * @description: 获取所有品类树
 * @param {*}
 * @return {*}
 */

export const getCateTreeApi = `${urlPath.tree}/tenant/category/paged-query`

/**
 * @description: 获取所有物料
 * @param {*}
 * @return {*}
 */

export const getMarticApi = `${urlPath.tree}/tenant/item/paged-query?BU_CODE=${localStorage.getItem(
  'currentBu'
)}`
// 物料组
export const getMarticGroupApi = `${urlPath.tree}/tenant/item-group/paged-query`

/**
 * @description: 获取所有SKU
 * @param {*}
 * @return {*}
 */

export const getSkuApi = `${urlPath.tree}/tenant/sku/page-query`

/**
 * @description: 获取当前租户下的所有员工
 * @param {*}
 * @return {*}
 */

export const getMasteremployeeList = (data) =>
  $axios.get(`${urlPath.tree}/tenant/employee/currentTenantEmployees`, data)

//     接口相关

/**
 * @description: 查询单据列表
 * @param {*}
 * @return {*}
 */

export const billsListApi = (data) => $axios.get(`${IAM}/permission-dimension/bill-type/list`, data)

/**
 * @description: 查询单据列表
 * @param {*}
 * @return {*}
 */

export const dimensionRecordsApi = `${IAM}/permission-dimension/bill-type/dimension/page`

/**
 * @description: 设置维度是否需要
 * @param {*}
 * @return {*}
 */
export const setDimensionIsRequire = (data) =>
  $axios.post(`${IAM}/dimension-active/bill-type/using-dimension/switch`, data)

/**
 * @description: 查询那个维度是需要的。回显用
 * @param {*}
 * @return {*}
 */
export const queryRequireItem = (data) =>
  $axios.post(`${IAM}/dimension-active/bill-type/using-dimension/list`, data)

/**
 * @description: 查询已授权用户列表
 * @param {*}
 * @return {*}
 */
export const queryUserPageAllow = `${IAM}/granted-subject/auth/users/page`

/**
 * @description: 查询已授权用户组列表
 * @param {*}
 * @return {*}
 */
export const queryUserGroupPageAllow = `${IAM}/granted-subject/auth/user-group/page`

/**
 * @description: 设置维度是否需要
 * @param {*}
 * @return {*}
 */
export const addUserAllow = (data) => $axios.post(`${IAM}/granted-subject/auth/users`, data)

/**
 * @description: 获取管控维度枚举类
 * @param {*}
 * @return {*}
 */
export const getDimensionEnumApi = (data) =>
  $axios.get(`${urlPath.role}/common/selectable/permissionDimensionEnum`, data)

/**
 * @description: 用户新增权限
 * @param {*}
 * @return {*}
 */
export const addRoleDataApi = (data) =>
  $axios.post(`${IAM}/subject-permission-data-rel/item-value/assign`, data)

/**
 * @description: 删除用户批量或用户组
 * @param {*}
 * @return {*}
 */
export const deleteRoleAPi = (data) => $axios.delete(`${IAM}/granted-subject/auth/subject`, data)

/**
 * @description: 该接口用于获取回显的维度值,勾选已经拥有的纬度值
 * @param {*}
 * @return {*}
 */
export const getSelectedRoleDataQuery = (data) =>
  $axios.post(`${IAM}/subject-permission-data-rel/item-value/query`, data)

/**
 * @description: 该接口用于获取回显的维度值,勾选已经拥有的纬度值 交集
 * @param {*}
 * @return {*}
 */
export const getSelectedAndRoleDataQuery = (data) =>
  $axios.post(`${IAM}/user/permission/summary/queryUserPermission`, data)

export const getPermissionTotal = `${IAM}/user/permission/summary/queryPage`

/**
 * @description: 导出
 * @param {*}
 * @return {*}
 */

export const exportExcelLoad = (data) =>
  $axios.post(`${IAM}/subject-permission-data-rel/item-value/export`, data, {
    responseType: 'blob'
  })

/**
 * @description: 导入
 * @param {*}
 * @return {*}
 */

export const importExcelLoad = (data) =>
  $axios.post(`${IAM}/subject-permission-data-rel/item-value/import`, data)

export const queryPageOrganization = `${
  urlPath.tree
}/tenant/business-organization/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`
export const queryPageOrganizationGroup = `${
  urlPath.tree
}/tenant/business-group/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`
// 获取分页工厂
export const queryPageSitPage = `${
  urlPath.tree
}/tenant/site/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`

// 获取公司分页

export const queryPageCompanyPage = `${urlPath.tree}/tenant/organization/paged-query`

// 获取部门分页

export const queryPageDept = `${urlPath.tree}/tenant/organization/paged-query`

/**
 * @description: 根据用户ID查询关联的角色
 * @param {*}
 * @return {*}
 */
export const getRoleListQuery = (data) =>
  $axios.post(`${IAM}/user/permission/summary/queryRoleList`, data)

/**
 * @description: 导入
 * @param {*}
 * @return {*}
 */

export const importPermission = (data) =>
  $axios.post(
    `/supplier/tenant/user/permission/importExcel?BU_CODE=${localStorage.getItem('currentBu')}`,
    data,
    {
      responseType: 'blob'
    }
  )

/**
 * @description: 导出模板
 * @param {*}
 * @return {*}
 */

export const exportPermissionTemplate = (data) =>
  $axios.get('/supplier/tenant/user/permission/downloadImportTemplate', data, {
    responseType: 'blob'
  })
