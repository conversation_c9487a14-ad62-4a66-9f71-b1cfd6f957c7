/*
 * @Author: your name
 * @Date: 2021-12-16 13:58:18
 * @LastEditTime: 2021-12-20 11:51:34
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\apis\modules\tradePartner.js
 */
import { API } from '@mtech-common/http'

// 贸易伙伴
export const NAME = 'tradePartner'

const urlPath = {
  role: '/iam',
  tree: '/masterDataManagement'
}

/**
 * @description: 贸易伙伴 - 新增
 * @param {*}
 * @return {*}
 */
export const tradePartnerAdd = (data) =>
  API.post(`${urlPath.tree}/tenant/trading-partner/add`, data, {
    useNotify: true
  })

/**
 * @description: 贸易伙伴 - 新增验证
 * @param {*}
 * @return {*}
 */
export const tradePartnerAddValid = (data) =>
  API.get(`${urlPath.tree}/tenant/trading-partner/add-valid`, data)

/**
 * @description: 贸易伙伴 - 删除
 * @param {*}
 * @return {*}
 */
export const tradePartnerDelete = (data) =>
  API.delete(`${urlPath.tree}/tenant/trading-partner/batch-delete`, data, {
    useNotify: true
  })

/**
 * @description: 贸易伙伴 - 编辑
 * @param {*}
 * @return {*}
 */
export const tradePartnerEdit = (data) =>
  API.put(`${urlPath.tree}/tenant/trading-partner/update`, data, {
    useNotify: true
  })

/**
 * @description: 贸易伙伴 - 编辑验证
 * @param {*}
 * @return {*}
 */
export const tradePartnerEditValid = (data) =>
  API.get(`${urlPath.tree}/tenant/trading-partner/update-valid`, data)

/** TODO:
 * @description: 贸易伙伴 - 同步
 * @param {*}
 * @return {*}
 */
export const tradePartnerSync = (data) =>
  API.post(`${urlPath.tree}/tenant/trading-partner/batch-delete`, data, {
    useNotify: true
  })
