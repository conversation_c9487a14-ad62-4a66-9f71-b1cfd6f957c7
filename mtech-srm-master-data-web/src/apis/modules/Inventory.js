/*
 * @Author: your name
 * @Date: 2021-09-03 17:39:36
 * @LastEditTime: 2021-09-09 13:46:15
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\apis\modules\roles.js
 */
import { API } from '@mtech-common/http'
//业务组织
export const NAME = 'Inventory'

const urlPath = {
  role: '/iam',
  tree: '/masterDataManagement',
  srm: 'srm-purchase-execute'
}
// 供方--盘点单列表
export const querySupHeader = (data) => {
  return API.post(`${urlPath.srm}/tenant/inventory/querySupHeader`, data)
}
// 供方--创建盘点单--公司接口
export const findSpecifiedChildrenLevelOrgs = (data) => {
  return API.post(`${urlPath.tree}/tenant/organization/findSpecifiedChildrenLevelOrgs`, data)
}
// 供方--创建盘点单--工厂接口
export const getSiteInfo = (data) => {
  return API.get(
    `${urlPath.tree}/tenant/site/getSiteInfo?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )
}
// 供方--创建盘点单--查询相关附件
export const queryFileByDocId = (data) => {
  return API.get(`${urlPath.tree}/tenant/file/queryFileByDocId`, data)
}
// 供方--创建盘点单--保存
export const saveSubmit = (data) => {
  return API.post(`${urlPath.srm}/tenant/inventory/saveSubmit`, data)
}
//
