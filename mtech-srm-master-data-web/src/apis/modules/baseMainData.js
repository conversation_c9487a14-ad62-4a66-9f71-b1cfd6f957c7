import { API } from '@mtech-common/http'

// 基础数据管理
export const NAME = 'baseMainData'

const urlPath = {
  role: '/iam',
  commonBase: '/masterDataManagement/common',
  tenantBase: '/masterDataManagement/tenant',
  adminBase: '/masterDataManagement/admin'
}

const PROXY_BASE = location.href.includes('platform') ? urlPath.adminBase : urlPath.tenantBase

// const PROXY_BASE = urlPath.tenantBase; // 登录站那边根据租户id隔离了

// 通用级 - 所有国家
export const getAllCountry = (data = {}) => API.post(`${PROXY_BASE}/country/queryAll`, data)

// 通用级 - 所有税种
export const getAllTax = (data = {}) => API.post(`${PROXY_BASE}/tax-type/queryAll`, data)

// 通用级 - 获取国家下的所有税种
export const getTaxByCountry = (data = {}) =>
  API.get(`${PROXY_BASE}/tax-type/getByCountryId/${data}`)

// 通用级 - 所有  地区类型-dictCode: "areaType"  单位类型-dictCode: "unit"  字典父级-dictCode: "dictType"
export const getAllAreaTypeOrUnit = (data = {}) =>
  API.post(`${PROXY_BASE}/dict-item/dict-code`, data)

// 通用级 - 所有时区
export const getAllTimeZone = (data = {}) => API.post(`${PROXY_BASE}/time-zone/queryAll`, data)

export const getCurrencyDataAll = (data = {}) =>
  API.get(`${PROXY_BASE}/currency/queryActiveCurrency`, data)

// 列表---------------------国家-------------------------------------
export const getCountryData = (data = {}) => API.post(`${PROXY_BASE}/country/paged-query`, data)

// 列表---------------------地区-------------------------------------
export const getAreaData = (data = {}) => API.post(`${PROXY_BASE}/area/paged-query`, data)

// 添加
export const addAreaData = (data = {}) => API.post(`${PROXY_BASE}/area/add`, data)

export const getAddRulesArea = (data = {}) => API.get(`${PROXY_BASE}/area/add-valid`, data)

// 编辑
export const editAreaData = (data = {}) => API.put(`${PROXY_BASE}/area/update`, data)

export const getUpdateRulesArea = (data = {}) => API.get(`${PROXY_BASE}/area/update-valid`, data)

// 删除
export const deleteAreaData = (data = {}) => {
  return API.delete(`${PROXY_BASE}/area/batch-delete`, data)
}

// 更新状态
export const updateAreaStatus = (data = {}) => API.put(`${PROXY_BASE}/area/batch-update`, data)

// 列表---------------------区域-------------------------------------
export const getRegionData = (data = {}) => API.post(`${PROXY_BASE}/region/paged-query`, data)

// 添加
export const addRegionData = (data = {}) => API.post(`${PROXY_BASE}/region/add`, data)

export const getAddRulesRegion = (data = {}) => API.get(`${PROXY_BASE}/region/add-valid`, data)

// 编辑
export const editRegionData = (data = {}) => API.put(`${PROXY_BASE}/region/update`, data)

export const getUpdateRulesRegion = (data = {}) =>
  API.get(`${PROXY_BASE}/region/update-valid`, data)

// 删除
export const deleteRegionData = (data = {}) => {
  return API.delete(`${PROXY_BASE}/region/batch-delete`, data)
}

// 更新状态
export const updateRegionStatus = (data = {}) => API.put(`${PROXY_BASE}/region/batch-update`, data)

// 列表---------------------货币-------------------------------------
export const getCurrencyData = (data = {}) => API.post(`${PROXY_BASE}/currency/paged-query`, data)

// 列表---------------------汇率-------------------------------------
export const getExchangeRateData = (data = {}) =>
  API.post(
    `${PROXY_BASE}/currency-exchange/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )
// 添加
export const addExchangeRateData = (data = {}) =>
  API.post(`${PROXY_BASE}/currency-exchange/add`, data)

export const getAddRulesExchangeRate = (data = {}) =>
  API.get(`${PROXY_BASE}/currency-exchange/add-valid`, data)

// 编辑
export const editExchangeRateData = (data = {}) =>
  API.put(`${PROXY_BASE}/currency-exchange/update`, data)

export const getUpdateRulesExchangeRate = (data = {}) =>
  API.get(`${PROXY_BASE}/currency-exchange/update-valid`, data)

// 删除
export const deleteExchangeRateData = (data = {}) => {
  return API.delete(`${PROXY_BASE}/currency-exchange/batch-delete`, data)
}

// 列表---------------------税种-------------------------------------
export const getTaxCategoryData = (data = {}) =>
  API.post(`${PROXY_BASE}/tax-type/paged-query`, data)

// 添加
export const addTaxCategoryData = (data = {}) => API.post(`${PROXY_BASE}/tax-type/add`, data)

export const getAddRulesTaxCategory = (data = {}) =>
  API.get(`${PROXY_BASE}/tax-type/add-valid`, data)

// 编辑
export const editTaxCategoryData = (data = {}) => API.put(`${PROXY_BASE}/tax-type/update`, data)

export const getUpdateRulesTaxCategory = (data = {}) =>
  API.get(`${PROXY_BASE}/tax-type/update-valid`, data)

// 删除
export const deleteTaxCategoryData = (data = {}) => {
  return API.delete(`${PROXY_BASE}/tax-type/batch-delete`, data)
}

// 更新状态
export const updateTaxCategoryStatus = (data = {}) =>
  API.put(`${PROXY_BASE}/tax-type/batch-update`, data)

// 列表---------------------语言-------------------------------------
export const getLanguageData = (data = {}) => API.post(`${PROXY_BASE}/language/paged-query`, data)

// 列表---------------------单位-------------------------------------
export const getUnitData = (data = {}) =>
  API.post(`${PROXY_BASE}/unit/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`, data)
// 添加
export const addUnitData = (data = {}) => API.post(`${PROXY_BASE}/unit/add`, data)

export const getAddRulesUnit = (data = {}) => API.get(`${PROXY_BASE}/unit/add-valid`, data)

// 编辑
export const editUnitData = (data = {}) => API.put(`${PROXY_BASE}/unit/update`, data)

export const getUpdateRulesUnit = (data = {}) => API.get(`${PROXY_BASE}/unit/update-valid`, data)

// 删除
export const deleteUnitData = (data = {}) => {
  return API.delete(`${PROXY_BASE}/unit/batch-delete`, data)
}

// 更新状态
export const updateUnitStatus = (data = {}) => API.put(`${PROXY_BASE}/unit/batch-update`, data)

// 列表---------------------税率-------------------------------------
export const getTaxItemData = (data = {}) =>
  API.post(`${PROXY_BASE}/tax-item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`, data)

// 添加
export const addTaxItemData = (data = {}) => API.post(`${PROXY_BASE}/tax-item/add`, data)

export const getAddRulesTaxItem = (data = {}) => API.get(`${PROXY_BASE}/tax-item/add-valid`, data)

// 编辑
export const editTaxItemData = (data = {}) => API.put(`${PROXY_BASE}/tax-item/update`, data)

export const getUpdateRulesTaxItem = (data = {}) =>
  API.get(`${PROXY_BASE}/tax-item/update-valid`, data)

// 删除
export const deleteTaxItemData = (data = {}) => {
  return API.delete(`${PROXY_BASE}/tax-item/batch-delete`, data)
}

// 更新状态
export const updateTaxItemStatus = (data = {}) =>
  API.put(`${PROXY_BASE}/tax-item/batch-update`, data)

// 列表---------------------时区-------------------------------------
export const getTimeZoneData = (data = {}) => API.post(`${PROXY_BASE}/time-zone/paged-query`, data)

// 列表---------------------数据字典类型-------------------------------------
export const getDictionaryTypeData = (data = {}) => API.post(`${PROXY_BASE}/dict/query`, data)

// 添加
export const addDictTypeData = (data = {}) => API.post(`${PROXY_BASE}/dict/add`, data)

export const getAddRulesDictType = (data = {}) => API.get(`${PROXY_BASE}/dict/add-valid`, data)

// 编辑
export const editDictTypeData = (data = {}) => API.put(`${PROXY_BASE}/dict/update`, data)

export const getUpdateRulesDictType = (data = {}) =>
  API.get(`${PROXY_BASE}/dict/update-valid`, data)

// 删除
export const deleteDictTypeData = (data = {}) => {
  return API.delete(`${PROXY_BASE}/dict/batch-delete`, data)
}

// 更新状态
export const updateDictTypeStatus = (data = {}) => API.put(`${PROXY_BASE}/dict/batch-update`, data)

// export const deleteDictData = data => {
//   return axios
//     .delete(`/api${PROXY_BASE}/dict/batch-delete`, {
//       data: data
//     })
//     .then(res => {
//       console.log(res);
//     });
// };

// 列表---------------------数据字典-------------------------------------
export const getDictionaryData = (data = {}) => API.post(`${PROXY_BASE}/dict-item/query`, data)

// 添加
export const addDictData = (data = {}) => API.post(`${PROXY_BASE}/dict-item/add`, data)

export const getAddRulesDict = (data = {}) => API.get(`${PROXY_BASE}/dict-item/add-valid`, data)

// 编辑
export const editDictData = (data = {}) => API.post(`${PROXY_BASE}/dict-item/update`, data)

export const getUpdateRulesDict = (data = {}) =>
  API.get(`${PROXY_BASE}/dict-item/update-valid`, data)

// 删除
export const deleteDictData = (data = {}) =>
  API.delete(`${PROXY_BASE}/dict-item/batch-delete`, data)

// 选择树形时，父级id
export const getDictItemByParentId = (data = {}) =>
  API.post(`${PROXY_BASE}/dict-item/getByDictId`, data)

// 更新状态
export const updateDictStatus = (data = {}) => API.put(`${PROXY_BASE}/dict-item/batch-update`, data)
