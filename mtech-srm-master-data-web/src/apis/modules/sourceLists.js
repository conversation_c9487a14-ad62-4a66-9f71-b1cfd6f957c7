import { API } from '@mtech-common/http'
export const NAME = 'sourceLists'
// const PROXY_BASE = '/supplier'
const PROXY_MASTER = '/masterDataManagement'

// 子表的查询
export const criteriaQuery = (data = {}) => {
  return API.post(`${PROXY_MASTER}/tenant/supply-source-list/criteria-query`, data)
}

// 根据父级ID查询下级的品类列表
export const listByParent = (data = {}) => {
  return API.post(`${PROXY_MASTER}/tenant/category/list-by-parent`, data)
}

// 工厂
export const fuzzyQuery = (data = {}) => {
  return API.post(
    `${PROXY_MASTER}/tenant/site/fuzzy-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )
}
// 物料
export const fuzzyPagedQuery = (data = {}) => {
  return API.post(
    `${PROXY_MASTER}/tenant/item/fuzzy-paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )
}

// 物料
export const itemPagedQuery = (data = {}) => {
  return API.post(
    `${PROXY_MASTER}/tenant/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )
}

// 新增
export const batchAdd = (data = {}) => {
  return API.post(`${PROXY_MASTER}/tenant/supply-source-list/batch-add`, data)
}

// 模糊搜索品类
export const queryForTree = (data = {}) => {
  return API.post(`${PROXY_MASTER}/tenant/category/query-for-tree`, data)
}

// 供应商
export const pagedQuery = (data = {}) => {
  return API.post(`${PROXY_MASTER}/tenant/supplier/paged-query`, data)
}

// 采购组织
export const queryByOrgcode = (data = {}) => {
  return API.get(
    `${PROXY_MASTER}/tenant/business-partner/list-purchasing-groups`, //?companyOrgCode=CG0120211108111109&partnerCode=*********
    data
  )
}

// 同步SAP
export const batchSyncOutward = (data = {}) => {
  return API.post(`${PROXY_MASTER}/tenant/supply-source-list/batch-sync-outward`, data)
}

// 编辑
export const batchUpdate = (data = {}) => {
  return API.put(`${PROXY_MASTER}/tenant/supply-source-list/batch-update`, data)
}
// 保存并同步SAP 批量新增并同步
export const batchAddAndSync = (data = {}) => {
  return API.post(`${PROXY_MASTER}/tenant/supply-source-list/batch-add-and-sync`, data)
}
// 保存并同步SAP 批量新增并同步
export const batchUpdateAndSync = (data = {}) => {
  return API.put(`${PROXY_MASTER}/tenant/supply-source-list/batch-update-and-sync`, data)
}

// 供应商信息接口
export const criteriaQuerySupplier = (data = {}) => {
  return API.post(`${PROXY_MASTER}/tenant/supply/source/list-available-suppliers`, data)
}

// 暂用
export const getChildrenCompanyOrganization2 = (data = {}) => {
  return API.post(`${PROXY_MASTER}/tenant/organization/findSpecifiedChildrenLevelOrgs`, data)
}

// 根据字典类型编码获取字典详情
export const queryDict = (data = {}) => {
  return API.post(`/masterDataManagement/tenant/dict-item/dict-code`, data)
}
