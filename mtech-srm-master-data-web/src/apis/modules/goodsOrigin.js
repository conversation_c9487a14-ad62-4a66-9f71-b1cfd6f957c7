/*
 * @Author: your name
 * @Date: 2021-08-26 17:43:31
 * @LastEditTime: 2021-10-22 10:38:55
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\apis\modules\goodsOrigin.js
 */
import { API } from '@mtech-common/http'

// 字典
export const NAME = 'goodsOrigin'

const urlPath = {
  role: '/iam',
  tree: '/masterDataManagement'
}

/**
 * @description: 获取公司下拉列表
 * @param {*}
 * @return {*}
 */
export const companyGet = (data) =>
  API.get(`${urlPath.tree}/tenant/organization/getOrganizationInfo`, data)

/**
 * @description: 获取公司下的工厂/地点
 * @param {*}
 * @return {*}
 */
export const siteGet = (data) =>
  API.get(
    `${urlPath.tree}/tenant/site/getSiteInfo?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )

/**
 * @description: 货源清单 - 新增
 * @param {*}
 * @return {*}
 */
export const originListAdd = (data) =>
  API.post(`${urlPath.tree}/tenant/supply-source-list/add`, data, {
    useNotify: true
  })

/**
 * @description: 货源清单 - 修改状态
 * @param {*}
 * @return {*}
 */
export const originStatusUpdate = (data) =>
  API.put(`${urlPath.tree}/tenant/supply-source-list/batch-update`, data, {
    useNotify: true
  })

/**
 * @description: 货源清单 - 修改状态
 * @param {*}
 * @return {*}
 */
export const originDelete = (data) =>
  API.delete(`${urlPath.tree}/tenant/supply-source-list/batch-delete`, data, {
    useNotify: true
  })

/**
 * @description: 货源清单 - 新增
 * @param {*}
 * @return {*}
 */
export const getPriceRecordInfo = (data) =>
  API.post(`${urlPath.tree}/tenant/supply-source-list/getPriceRecordInfo`, data, {
    useNotify: true
  })
