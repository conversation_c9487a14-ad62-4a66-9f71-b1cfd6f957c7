/*
 * @Author: your name
 * @Date: 2021-08-25 09:57:39
 * @LastEditTime: 2021-10-21 10:14:04
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\apis\modules\org.js
 */
import { API } from '@mtech-common/http'

// 组织
export const NAME = 'permission'

const urlPath = {
  role: '/iam',
  tree: '/masterDataManagement'
}

/**
 * @description: 工厂/地点 - 新增
 * @param {*}
 * @return {*}
 */
export const siteAdd = (data) =>
  API.post(`${urlPath.tree}/tenant/site/add`, data, {
    useNotify: true
  })

/**
 * @description: 工厂/地点 - 新增验证
 * @param {*}
 * @return {*}
 */
export const siteAddValid = (data) => API.get(`${urlPath.tree}/tenant/site/add-valid`, data)

/**
 * @description: 工厂/地点 - 编辑
 * @param {*}
 * @return {*}
 */
export const siteEdit = (data) =>
  API.put(`${urlPath.tree}/tenant/site/update`, data, {
    useNotify: true
  })

/**
 * @description: 工厂/地点 - 编辑验证
 * @param {*}
 * @return {*}
 */
export const siteEditValid = (data) => API.get(`${urlPath.tree}/tenant/site/update-valid`, data)

/**
 * @description: 工厂/地点 - 更新状态
 * @param {*}
 * @return {*}
 */
export const siteStatusUpdate = (data) =>
  API.put(`${urlPath.tree}/tenant/site/batch-update`, data, {
    useNotify: true
  })

/**
 * @description: 工厂/地点 - 删除
 * @param {*}
 * @return {*}
 */
export const siteDelete = (data) =>
  API.delete(`${urlPath.tree}/tenant/site/batch-delete`, data, {
    useNotify: true
  })

/**
 * @description: 获取当前租户下公司
 * @param {*}
 * @return {*}
 */
export const companyGet = (data) =>
  API.get(`${urlPath.tree}/tenant/organization/getOrganizationInfo`, data)

/**
 * @description: 查看脱敏数据
 * @param {*}
 * @return {*}
 */
export const checkDeliveryConfigInfo = (data = {}) =>
  API.get(`/srm-purchase-execute/common/dataDesensitize?desensitize=${data.key}`)
