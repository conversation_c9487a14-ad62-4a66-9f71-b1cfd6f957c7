/*
 * @Author: your name
 * @Date: 2021-10-26 17:14:30
 * @LastEditTime: 2021-11-30 15:52:22
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\apis\modules\sku.js
 */
import { API } from '@mtech-common/http'

// sku
export const NAME = 'sku'

const urlPath = {
  role: '/iam',
  tree: '/masterDataManagement'
}

/**
 * @description: SPU - 列表删除
 * @param {*}
 * @return {*}
 */
export const spuDel = (data) =>
  API.delete(`${urlPath.tree}/tenant/spu/delete`, data, {
    useNotify: true
  })

/**
 * @description: SKU - 列表删除
 * @param {*}
 * @return {*}
 */
export const skuDel = (data) =>
  API.delete(`${urlPath.tree}/tenant/sku/batch-delete`, data, {
    useNotify: true
  })

/**
 * @description: 根据SPUID查询SKU
 * @param {*}
 * @return {*}
 */
export const skuQueryBySpu = (data) => API.post(`${urlPath.tree}/tenant/sku/find-by-spu`, data)

/**
 * @description: 根据特征属性生成SKU
 * @param {*}
 * @return {*}
 */
export const skuGenerate = (data) =>
  API.post(`${urlPath.tree}/tenant/sku/generate`, data, {
    useNotify: true
  })

/**
 * @description: 根据物料查询特征属性
 * @param {*}
 * @return {*}
 */
export const attrDataGetByMat = (data) =>
  API.post(`${urlPath.tree}/tenant/item-classification/query-by-item`, data)

/**
 * @description: SPU - 基础信息查询
 * @param {*}
 * @return {*}
 */
export const baseInfoGet = (data) => API.post(`${urlPath.tree}/tenant/spu/detail`, data)

/**
 * @description: 品类获取
 * @param {*}
 * @return {*}
 */
export const cateTreeNodeDetailGet = (data) =>
  API.get(`${urlPath.tree}/tenant/category/eshoptree-in-client`, data)

/**
 * @description: SPU - 新增 - 基础信息保存
 * @param {*}
 * @return {*}
 */
export const baseInfoSave = (data) =>
  API.post(`${urlPath.tree}/tenant/spu/add`, data, {
    useNotify: true
  })

/**
 * @description: SPU - 编辑 - 基础信息保存
 * @param {*}
 * @return {*}
 */
export const baseInfoEdit = (data) =>
  API.put(`${urlPath.tree}/tenant/spu/update`, data, {
    useNotify: true
  })

/**
 * @description: SKU - 描述 图片列表查询
 * @param {*}
 * @return {*}
 */
export const skuImageListGet = (data) =>
  API.post(`${urlPath.tree}/tenant/sku-images/spu-img-list`, data)

/**
 * @description: SKU - 描述 SPU图片保存
 * @param {*}
 * @return {*}
 */
export const spuImageSave = (data) =>
  API.post(`${urlPath.tree}/tenant/sku-images/set-spu-img`, data)

/**
 * @description: SKU - 描述 SKU图片保存
 * @param {*}
 * @return {*}
 */
export const skuImageSave = (data) =>
  API.post(`${urlPath.tree}/tenant/sku-images/add-sku-img`, data)

/**
 * @description: SKU - 价格信息查询
 * @param {*}
 * @return {*}
 */
export const priceDataGet = (data) => API.post(`${urlPath.tree}/tenant/sku/sku-price-list`, data)

/**
 * @description: SKU - 价格信息保存
 * @param {*}
 * @return {*}
 */
export const priceDataSave = (data) =>
  API.post(`${urlPath.tree}/tenant/sku/sku-price-batch-save`, data, {
    useNotify: true
  })

/**
 * @description: SKU - 规格参数查询by skuId
 * @param {*}
 * @return {*}
 */
export const paramsDataGet = (data) =>
  API.post(`${urlPath.tree}/tenant/sku_specification/list-by-sku`, data)

/**
 * @description: SKU - 规格参数新增
 * @param {*}
 * @return {*}
 */
export const paramsDataAddSave = (data) =>
  API.post(`${urlPath.tree}/tenant/sku_specification/add`, data, {
    useNotify: true
  })

/**
 * @description: SKU - 规格参数修改
 * @param {*}
 * @return {*}
 */
export const paramsDataEditSave = (data) =>
  API.put(`${urlPath.tree}/tenant/sku_specification/update-specification-value`, data, {
    useNotify: true
  })

/**
 * @description: SKU - 规格参数删除
 * @param {*}
 * @return {*}
 */
export const paramsDataDel = (data) =>
  API.delete(`${urlPath.tree}/tenant/sku_specification/batch-delete`, data, {
    useNotify: true
  })

/**
 * @description: SKU - 规格参数应用于其他SKU
 * @param {*}
 * @return {*}
 */
export const applyToOther = (data) =>
  API.put(`${urlPath.tree}/tenant/sku_specification/copy-specification-to-others`, data, {
    useNotify: true
  })

/**
 * @description: SKU - 包装信息查询
 * @param {*}
 * @return {*}
 */
export const packageDataGet = (data) =>
  API.post(`${urlPath.tree}/tenant/sku-package/find-by-spu`, data)

/**
 * @description: SKU - 包装信息新增
 * @param {*}
 * @return {*}
 */
export const packageDataAddSave = (data) =>
  API.post(`${urlPath.tree}/tenant/sku-package/add`, data, {
    useNotify: true
  })

/**
 * @description: SKU - 包装信息批量新增
 * @param {*}
 * @return {*}
 */
export const packageDataBatchAddSave = (data) =>
  API.post(`${urlPath.tree}/tenant/sku-package/batch-add`, data, {
    useNotify: true
  })

/**
 * @description: SKU - 包装信息编辑
 * @param {*}
 * @return {*}
 */
export const packageDataEditSave = (data) =>
  API.post(`${urlPath.tree}/tenant/sku-package/batch-update`, data, {
    useNotify: true
  })

/**
 * @description: SKU - 包装信息删除
 * @param {*}
 * @return {*}
 */
export const packageDataDel = (data) =>
  API.delete(`${urlPath.tree}/tenant/sku-package/batch-delete`, data, {
    useNotify: true
  })

/**
 * @description: SKU - 根据SPU查询售后服务
 * @param {*}
 * @return {*}
 */
export const serviceDataGet = (data) =>
  API.post(`${urlPath.tree}/tenant/spu_after_sale/find-by-spu`, data)

/**
 * @description: SKU - 售后服务保存
 * @param {*}
 * @return {*}
 */
export const serviceSave = (data) =>
  API.post(`${urlPath.tree}/tenant/spu_after_sale/save`, data, {
    useNotify: true
  })

/**
 * @description: SKU - 详情查询
 * @param {*}
 * @return {*}
 */
export const skuInfoGet = (data) => API.post(`${urlPath.tree}/tenant/sku/detail`, data)

/**
 * @description: SKU - 编辑
 * @param {*}
 * @return {*}
 */
export const skuInfoUpdate = (data) =>
  API.post(`${urlPath.tree}/tenant/sku/update`, data, {
    useNotify: true
  })
