/*
 * @Author: your name
 * @Date: 2021-08-26 17:43:31
 * @LastEditTime: 2022-01-20 16:38:29
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\apis\modules\dict.js
 */
import { API } from '@mtech-common/http'

// 字典
export const NAME = 'material'

const urlPath = {
  role: '/iam',
  tree: '/masterDataManagement'
}

/**
 * @description: 品项与工厂 - 批量删除
 * @param {*}
 * @return {*}
 */
export const matSiteDelete = (data) =>
  API.delete(`${urlPath.tree}/tenant/item-org-rel/batch-delete`, data, {
    useNotify: true
  })

/**
 * @description: 工厂 - 条件所有
 * @param {*}
 * @return {*}
 */
export const factoryGet = (data) =>
  API.post(
    `${urlPath.tree}/tenant/site/criteria-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )

/**
 * @description: 组织下品项品类
 * @param {*}
 * @return {*}
 */
export const orgItemQuery = (data) =>
  API.post(
    `${urlPath.tree}/tenant/item-org-rel/criteria-query?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`,
    data
  )

/**
 * @description: 品项品类关联关系 - 新增
 * @param {*}
 * @return {*}
 */
export const cateItemAdd = (data) =>
  API.post(`${urlPath.tree}/tenant/item-org-rel/add`, data, {
    useNotify: true
  })

/**
 * @description: 品项组 - 新增
 * @param {*}
 * @return {*}
 */
export const matGroupAdd = (data) =>
  API.post(`${urlPath.tree}/tenant/item-group/add`, data, {
    useNotify: true
  })

/**
 * @description: 品项组 - 新增验证
 * @param {*}
 * @return {*}
 */
export const matGroupAddValid = (data) =>
  API.get(`${urlPath.tree}/tenant/item-group/add-valid`, data)

/**
 * @description: 品项组 - 删除
 * @param {*}
 * @return {*}
 */
export const matGroupDelete = (data) =>
  API.delete(`${urlPath.tree}/tenant/item-group/batch-delete`, data, {
    useNotify: true
  })

/**
 * @description: 品项组 - 编辑
 * @param {*}
 * @return {*}
 */
export const matGroupEdit = (data) =>
  API.put(`${urlPath.tree}/tenant/item-group/update`, data, {
    useNotify: true
  })

/**
 * @description: 品项组 - 编辑验证
 * @param {*}
 * @return {*}
 */
export const matGroupEditValid = (data) =>
  API.get(`${urlPath.tree}/tenant/item-group/update-valid`, data)

/**
 * @description: 品项组 - 修改状态
 * @param {*}
 * @return {*}
 */
export const matGroupStatusUpdate = (data) =>
  API.put(`${urlPath.tree}/tenant/item-group/change-status`, data, {
    useNotify: true
  })

/**
 * @description: 更新品项组织关系，同时更新品项品类关联关系
 * @param {*}
 * @return {*}
 */
export const itemOrgRelUpdate = (data) =>
  API.post(`${urlPath.tree}/tenant/item-org-rel/update`, data, {
    useNotify: true
  })

/**
 * @description: 品项 - 基础数据 物料图片新增
 * @param {*}
 * @return {*}
 */
export const imageAdd = (data) =>
  API.post(`${urlPath.tree}/tenant/item-files/add`, data, {
    useNotify: true
  })

/**
 * @description: 品项 - 基础数据 图片查询
 * @param {*}
 * @return {*}
 */
export const imageQuery = (data) => API.post(`${urlPath.tree}/tenant/item-files/query`, data)

/**
 * @description: 品项 - 基础数据 图片路径获取（文件服务）
 * @param {*}
 * @return {*}
 */
export const imagePath = (data) => API.post(`/file/user/file/files/path?useType=2`, data)

/**
 * @description: 品项 - 基础数据 图片删除
 * @param {*}
 * @return {*}
 */
export const imageDelete = (data) =>
  API.delete(`${urlPath.tree}/tenant/item-files/delete`, data, {
    useNotify: true
  })
/**
 * @description: 品项 - 基础数据 特性列表获取
 * @param {*}
 * @return {*}
 */
export const baseAttrGet = (data) =>
  API.post(`${urlPath.tree}/tenant/item-classification/query-by-item`, data)

/**
 * @description: 新增页面公司下拉(POST)(不支持模糊查询)
 * @param {*}
 * @return {*}
 */
export const findSpecifiedChildrenLevelOrgs = (data) =>
  API.post(`${urlPath.tree}/tenant/organization/findSpecifiedChildrenLevelOrgs`, data)
/**
 * @description: 新增页面选择完公司以后查询工厂接口(POST)(不支持模糊查询)
 * @param {*}
 * @return {*}
 */
export const findSiteInfoByParentId = (data) =>
  API.post(
    `${urlPath.tree}/tenant/site/findSiteInfoByParentId?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`,
    data
  )
/**
 * @description: 新增页面选择完工厂以后查询物料接口(POST)(支持模糊查询)
 * @param {*}
 * @return {*}
 */
export const itemfuzzyQuery = (data) =>
  API.post(
    `${urlPath.tree}/tenant/item-org-rel/fuzzy-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )
/**
 * @description: 新增页面保存接口
 * @param {*}
 * @return {*}
 */
export const JITadd = (data) => API.post(`${urlPath.tree}/tenant/item-org-jit/add`, data)
/**
 * @description: 编辑保存接口
 * @param {*}
 * @return {*}
 */
export const JITupdate = (data) => API.put(`${urlPath.tree}/tenant/item-org-jit/update`, data)
/**
 * @description: JIT删除
 * @param {*}
 * @return {*}
 */
export const batchDelete = (data) =>
  API.post(`${urlPath.tree}/tenant/item-org-jit/batch-delete`, data)
/**
 * @description: 批量状态修改
 * @param {*}
 * @return {*}
 */
export const batchUpdateStatus = (data) =>
  API.post(`${urlPath.tree}/tenant/item-org-jit/batch-update-status`, data)
/**
 * @description:  JIT分页列表(POST)
 * @param {*}
 * @return {*}
 */
export const pagedQuery = (data) =>
  API.post(`${urlPath.tree}/tenant/item-org-jit/paged-query`, data)

/**
 * @description:  jit导入维护(POST)
 * @param {*}
 * @return {*}
 */
export const importData = (data) =>
  API.post(`${urlPath.tree}/tenant/item-org-jit/import-data`, data)

/**
 * @description: jit导出维维护(POST)
 * @param {*}
 * @return {*}
 */
export const exportData = (data) => API.get(`${urlPath.tree}/tenant/item-org-jit/export-data`, data)
/**
 * @description: 直送销售 - 导入
 * @param {*}
 * @return {*}
 */
export const locationImportData = (data) =>
  API.post(`${urlPath.tree}/tenant/site-location-manage/import-data`, data)
/**
 * @description: 直送销售 - 导出
 * @param {*}
 * @return {*}
 */
export const locationExportData = (data) =>
  API.post(`${urlPath.tree}/tenant/site-location-manage/export-data`, data)
/**
 * @description: 直送销售 - 加工商模糊查询
 * @param {*}
 * @return {*}
 */
export const criteriaQuery = (data) =>
  API.post(`${urlPath.tree}/tenant/supplier/criteria-query`, data)
//直送---新增
export const siteAdd = (data) => API.post(`${urlPath.tree}/tenant/site-location-manage/add`, data)
//直送---新增----工厂选仓库
export const locationFuzzyQuery = (data) =>
  API.post(`${urlPath.tree}/tenant/location/fuzzy-query`, data)
//直送---编辑---保存
export const sitelocationmanangeUpdate = (data) =>
  API.put(`${urlPath.tree}/tenant/site-location-manange/update`, data)
//直送---删除
export const sitelocationmanageBatchdelete = (data) =>
  API.post(`${urlPath.tree}/tenant/site-location-manage/batch-delete`, data)
//直送---激活失效
export const sitelocationmanageBatchupdatestatus = (data) =>
  API.post(`${urlPath.tree}/tenant/site-location-manage/batch-update-status`, data)
