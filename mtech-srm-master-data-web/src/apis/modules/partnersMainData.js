/**
 * 组合物料 pack_item_rel
 */

import { API } from '@mtech-common/http'
export const NAME = 'partnersMainData'
const PROXY_MASTER_DATA = '/masterDataManagement'

const APIS = {
  // 详情查询
  detail: (data) => {
    return API.get(`${PROXY_MASTER_DATA}/tenant/supplier/detail`, data)
  },
  // 付款条件-分页查询
  pagePaymentTermsApi: (data) => {
    return API.post(
      `${PROXY_MASTER_DATA}/tenant/payment-terms/paged-query?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      data
    )
  },

  // 供应商供货物料管理-分页查询
  pageSupplyMaterialManagementApi: (data) => {
    return API.post(`${PROXY_MASTER_DATA}/tenant/supplierItemManage/queryPage`, data)
  },
  // 供应商供货物料管理-保存
  saveSupplyMaterialManagementApi: (data) => {
    return API.post(`${PROXY_MASTER_DATA}/tenant/supplierItemManage/save`, data)
  },
  // 供应商供货物料管理-删除
  deleteSupplyMaterialManagementApi: (data) => {
    return API.post(`${PROXY_MASTER_DATA}/tenant/supplierItemManage/delete`, data)
  },
  // 供应商供货物料管理-导入模板
  tempSupplyMaterialManagementApi: (data) => {
    return API.get(`${PROXY_MASTER_DATA}/tenant/supplierItemManage/downloadTemplate`, data, {
      responseType: 'blob'
    })
  },
  // 供应商供货物料管理-导入
  importSupplyMaterialManagementApi: (data) => {
    return API.post(`${PROXY_MASTER_DATA}/tenant/supplierItemManage/import`, data, {
      responseType: 'blob'
    })
  },
  // 供应商供货物料管理-导出
  exportSupplyMaterialManagementApi: (data) => {
    return API.post(`${PROXY_MASTER_DATA}/tenant/supplierItemManage/export`, data, {
      responseType: 'blob'
    })
  },
  // 供应商品类关系-导出
  exportSupplySourceApi: (data) => {
    return API.post(`${PROXY_MASTER_DATA}/tenant/supply/source/export`, data, {
      responseType: 'blob'
    })
  }
}

export default APIS
