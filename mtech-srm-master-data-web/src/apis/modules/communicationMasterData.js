import { API } from '@mtech-common/http'

// 通讯主数据
export const NAME = 'communicationMasterData'

const urlPath = {
  role: '/iam',
  commonBase: '/masterDataManagement/common',
  tenantBase: '/masterDataManagement/tenant',
  adminBase: '/masterDataManagement/admin'
}

const PROXY_BASE = location.href.includes('platform') ? urlPath.adminBase : urlPath.tenantBase

// 贸易伙伴-分页
export const pageTradePartnerApi = (data = {}) =>
  API.post(`${PROXY_BASE}/tradingPartnerSj/paged-query`, data)

// 贸易伙伴-导入
export const importTradePartnerApi = (data) =>
  API.post(`${PROXY_BASE}/tradingPartnerSj/import`, data, {
    responseType: 'blob'
  })

// 贸易伙伴-导入模板
export const downloadTradePartnerApi = (data) =>
  API.get(`${PROXY_BASE}/tradingPartnerSj/getTemplate`, data, {
    responseType: 'blob'
  })

// 方案组-分页
export const pageSchemaGroupApi = (data = {}) =>
  API.post(`${PROXY_BASE}/schemaGroupSj/paged-query`, data)

// 方案组-导入
export const importSchemaGroupApi = (data) =>
  API.post(`${PROXY_BASE}/schemaGroupSj/import`, data, {
    responseType: 'blob'
  })

// 方案组-导入模板
export const downloadSchemaGroupApi = (data) =>
  API.get(`${PROXY_BASE}/schemaGroupSj/getTemplate`, data, {
    responseType: 'blob'
  })

// 付款条件-分页
export const pagePaymentTermsApi = (data = {}) =>
  API.post(`${PROXY_BASE}/paymentTermsSj/paged-query`, data)

// 付款条件-导入
export const importPaymentTermsApi = (data) =>
  API.post(`${PROXY_BASE}/paymentTermsSj/import`, data, {
    responseType: 'blob'
  })

// 付款条件-导入模板
export const downloadPaymentTermsApi = (data) =>
  API.get(`${PROXY_BASE}/paymentTermsSj/getTemplate`, data, {
    responseType: 'blob'
  })

// 计量单位-分页
export const pageUnitApi = (data = {}) => API.post(`${PROXY_BASE}/unitSj/paged-query`, data)

// 计量单位-导入
export const importUnitApi = (data) =>
  API.post(`${PROXY_BASE}/unitSj/import`, data, {
    responseType: 'blob'
  })

// 计量单位-导入模板
export const downloadUnitApi = (data) =>
  API.get(`${PROXY_BASE}/unitSj/importTemplate`, data, {
    responseType: 'blob'
  })

// 物料组与品类关联关系-分页
export const pageMaterialCategoryRelApi = (data = {}) =>
  API.post(`${PROXY_BASE}/_matkl_category_rel_sj/paged-query`, data)

// 物料组与品类关联关系-新增
export const addMaterialCategoryRelApi = (data = {}) =>
  API.post(`${PROXY_BASE}/_matkl_category_rel_sj/add`, data)

// 物料组与品类关联关系-删除
export const deleteMaterialCategoryRelApi = (data = {}) =>
  API.delete(`${PROXY_BASE}/_matkl_category_rel_sj/deleteByIds`, data)

// 物料组与品类关联关系-导入
export const importMaterialCategoryRelApi = (data) =>
  API.post(`${PROXY_BASE}/_matkl_category_rel_sj/import`, data, {
    responseType: 'blob'
  })

// 物料组与品类关联关系-导入模板
export const downloadMaterialCategoryRelApi = (data) =>
  API.get(`${PROXY_BASE}/_matkl_category_rel_sj/getTemplate`, data, {
    responseType: 'blob'
  })

// 物料组与品类关联关系-导出
export const exportMaterialCategoryRelApi = (data) =>
  API.post(`${PROXY_BASE}/_matkl_category_rel_sj/exportData`, data, {
    responseType: 'blob'
  })
