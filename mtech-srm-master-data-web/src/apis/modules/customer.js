/*
 * @Author: your name
 * @Date: 2022-03-15 15:57:54
 * @LastEditTime: 2022-03-20 16:49:39
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\apis\modules\customer.js
 */
import { API } from '@mtech-common/http'

// sku
export const NAME = 'customer'

const urlPath = {
  role: '/iam',
  tree: '/masterDataManagement'
}

/**
 * @description: 客户 - 新增
 * @param {*}
 * @return {*}
 */
export const customerAdd = (data) =>
  API.post(`${urlPath.tree}/tenant/customer/add`, data, {
    useNotify: true
  })

/**
 * @description: 客户 - 新增验证
 * @param {*}
 * @return {*}
 */
export const customerAddValid = (data) => API.get(`${urlPath.tree}/tenant/customer/add-valid`, data)

/**
 * @description: 客户 - 编辑
 * @param {*}
 * @return {*}
 */
export const customerEdit = (data) =>
  API.put(`${urlPath.tree}/tenant/customer/update`, data, {
    useNotify: true
  })

/**
 * @description: 客户 - 编辑验证
 * @param {*}
 * @return {*}
 */
export const customerEditValid = (data) =>
  API.get(`${urlPath.tree}/tenant/customer/update-valid`, data)

/**
 * @description: 客户 - 更新状态
 * @param {*}
 * @return {*}
 */
export const customerStatusUpdate = (data) =>
  API.put(`${urlPath.tree}/tenant/customer/batch-update-status`, data, {
    useNotify: true
  })

/**
 * @description: 客户 - 删除
 * @param {*}
 * @return {*}
 */
export const customerDelete = (data) =>
  API.post(`${urlPath.tree}/tenant/customer/batch-delete`, data, {
    useNotify: true
  })

/**
 * @description: 获取bp
 * @param {*}
 * @return {*}
 */
export const bpGet = (data) => API.post(`${urlPath.tree}/tenant/business-partner/fuzzy-query`, data)

// 公司
export const getChildrenCompanyOrganization2 = (data) =>
  API.post(`${urlPath.tree}/tenant/organization/findSpecifiedChildrenLevelOrgs`, data)

// 供应商信息接口
export const criteriaQuerySupplier = (data) =>
  API.post(`${urlPath.tree}/tenant/supplier/criteria-query`, data)
