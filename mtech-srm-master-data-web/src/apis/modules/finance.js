/*
 * @Author: your name
 * @Date: 2022-02-16 14:26:13
 * @LastEditTime: 2022-04-07 14:19:57
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\apis\modules\finance.js
 */
import { API } from '@mtech-common/http'

// sku
export const NAME = 'finance'

const urlPath = {
  role: '/iam',
  tree: '/masterDataManagement'
}

/**
 * @description: 科目表 - 条件查询
 * @param {*}
 * @return {*}
 */
export const subCourseGet = (data) =>
  API.post(`${urlPath.tree}/tenant/account-chart/criteria-query`, data)

/**
 * @description: 科目表 - 新增
 * @param {*}
 * @return {*}
 */
export const subCourseListAdd = (data) =>
  API.post(`${urlPath.tree}/tenant/account-chart/add`, data, {
    useNotify: true
  })

/**
 * @description: 科目表 - 新增验证
 * @param {*}
 * @return {*}
 */
export const subCourseListAddValid = (data) =>
  API.get(`${urlPath.tree}/tenant/account-chart/add-valid`, data)

/**
 * @description: 科目表 - 编辑
 * @param {*}
 * @return {*}
 */
export const subCourseListEdit = (data) =>
  API.put(`${urlPath.tree}/tenant/account-chart/update`, data, {
    useNotify: true
  })

/**
 * @description: 科目表 - 编辑验证
 * @param {*}
 * @return {*}
 */
export const subCourseListEditValid = (data) =>
  API.get(`${urlPath.tree}/tenant/account-chart/update-valid`, data)

/**
 * @description: 科目表 - 列表删除
 * @param {*}
 * @return {*}
 */
export const subCourseListDelete = (data) =>
  API.delete(`${urlPath.tree}/tenant/account-chart/batch-delete`, data, {
    useNotify: true
  })

/**
 * @description: 科目 - 新增
 * @param {*}
 * @return {*}
 */
export const subCourseDetailAdd = (data) =>
  API.post(`${urlPath.tree}/tenant/account-subject/add`, data, {
    useNotify: true
  })

/**
 * @description: 科目 - 新增验证
 * @param {*}
 * @return {*}
 */
export const subCourseDetailAddValid = (data) =>
  API.get(`${urlPath.tree}/tenant/account-subject/add-valid`, data)

/**
 * @description: 科目 - 编辑
 * @param {*}
 * @return {*}
 */
export const subCourseDetailEdit = (data) =>
  API.put(`${urlPath.tree}/tenant/account-subject/update`, data, {
    useNotify: true
  })

/**
 * @description: 科目 - 编辑验证
 * @param {*}
 * @return {*}
 */
export const subCourseDetailEditValid = (data) =>
  API.get(`${urlPath.tree}/tenant/account-subject/update-valid`, data)

/**
 * @description: 科目 - 列表删除
 * @param {*}
 * @return {*}
 */
export const subCourseDetailDelete = (data) =>
  API.delete(`${urlPath.tree}/tenant/account-subject/batch-delete`, data, {
    useNotify: true
  })

/**
 * @description: 会计年度 - 条件查询
 * @param {*}
 * @return {*}
 */
export const fiscalYearGet = (data) =>
  API.post(`${urlPath.tree}/tenant/fiscal-year/criteria-query`, data)

/**
 * @description: 会计年度 - 新增
 * @param {*}
 * @return {*}
 */
export const fiscalYearAdd = (data) =>
  API.post(`${urlPath.tree}/tenant/fiscal-year/add`, data, {
    useNotify: true
  })

/**
 * @description: 会计年度 - 新增验证
 * @param {*}
 * @return {*}
 */
export const fiscalYearAddValid = (data) =>
  API.get(`${urlPath.tree}/tenant/fiscal-year/add-valid`, data)

/**
 * @description: 会计年度 - 编辑
 * @param {*}
 * @return {*}
 */
export const fiscalYearEdit = (data) =>
  API.put(`${urlPath.tree}/tenant/fiscal-year/update`, data, {
    useNotify: true
  })

/**
 * @description: 会计年度 - 编辑验证
 * @param {*}
 * @return {*}
 */
export const fiscalYearEditValid = (data) =>
  API.get(`${urlPath.tree}/tenant/fiscal-year/update-valid`, data)

/**
 * @description: 会计年度 - 列表删除
 * @param {*}
 * @return {*}
 */
export const fiscalYearDelete = (data) =>
  API.delete(`${urlPath.tree}/tenant/fiscal-year/batch-delete`, data, {
    useNotify: true
  })

/**
 * @description: 会计年度明细 - 新增
 * @param {*}
 * @return {*}
 */
export const fiscalYearItemAdd = (data) =>
  API.post(`${urlPath.tree}/tenant/fiscal-year-item/add`, data, {
    useNotify: true
  })

/**
 * @description: 会计年度明细 - 新增验证
 * @param {*}
 * @return {*}
 */
export const fiscalYearItemAddValid = (data) =>
  API.get(`${urlPath.tree}/tenant/fiscal-year-item/add-valid`, data)

/**
 * @description: 会计年度明细 - 编辑
 * @param {*}
 * @return {*}
 */
export const fiscalYearItemEdit = (data) =>
  API.put(`${urlPath.tree}/tenant/fiscal-year-item/update`, data, {
    useNotify: true
  })

/**
 * @description: 会计年度明细 - 编辑验证
 * @param {*}
 * @return {*}
 */
export const fiscalYearItemEditValid = (data) =>
  API.get(`${urlPath.tree}/tenant/fiscal-year-item/update-valid`, data)

/**
 * @description: 会计年度明细 - 列表删除
 * @param {*}
 * @return {*}
 */
export const fiscalYearItemDelete = (data) =>
  API.delete(`${urlPath.tree}/tenant/fiscal-year-item/batch-delete`, data, {
    useNotify: true
  })

/**
 * @description: 实体 - 列表分页查询
 * @param {*}
 * @return {*}
 */
export const entityList = (data) => API.post(`${urlPath.tree}/tenant/entity/paged-query`, data)

/**
 * @description: 实体 - 根据id查详情
 * @param {*}
 * @return {*}
 */
export const entityDetails = (data) => API.get(`${urlPath.tree}/tenant/entity/findById`, data)

/**
 * @description: 实体详情 - 分配科目表 - 保存
 * @param {*}
 * @return {*}
 */
export const chartAssign = (data) =>
  API.post(`${urlPath.tree}/tenant/entity-account-chart-rel/add`, data, {
    useNotify: true
  })

/**
 * @description: 实体详情 - 分配科目表 - 删除
 * @param {*}
 * @return {*}
 */
export const chartAssignedDelete = (data) =>
  API.delete(`${urlPath.tree}/tenant/entity-account-chart-rel/batch-delete`, data, {
    useNotify: true
  })

/**
 * @description: 实体详情 - 分配科目 - 修改名称
 * @param {*}
 * @return {*}
 */
export const subjectAssignEdit = (data) =>
  API.put(`${urlPath.tree}/tenant/entity-account-subject/update`, data, {
    useNotify: true
  })

/**
 * @description: 实体详情 - 分配科目 - 保存
 * @param {*}
 * @return {*}
 */
export const subjectAssign = (data) =>
  API.post(`${urlPath.tree}/tenant/entity-account-subject/addSubjectsToEntity`, data, {
    useNotify: true
  })

/**
 * @description: 实体详情 - 分配科目 - 删除
 * @param {*}
 * @return {*}
 */
export const subjectAssignedDelete = (data) =>
  API.delete(`${urlPath.tree}/tenant/entity-account-subject/batch-delete`, data, {
    useNotify: true
  })

/**
 * @description: 实体详情 - 会计年度 - 已分配查询
 * @param {*}
 * @return {*}
 */
export const fiscalYearAssigned = (data) =>
  API.post(`${urlPath.tree}/tenant/entity-account-period-rel/criteria-query`, data)

/**
 * @description: 实体详情 - 分配会计年度 - 保存
 * @param {*}
 * @return {*}
 */
export const fiscalYearSave = (data) =>
  API.post(`${urlPath.tree}/tenant/entity-account-period-rel/add`, data, {
    useNotify: true
  })
