/*
 * @Author: your name
 * @Date: 2021-08-26 17:43:31
 * @LastEditTime: 2022-01-25 13:57:32
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\apis\modules\dict.js
 */
import { API } from '@mtech-common/http'

// 字典
export const NAME = 'dict'

const urlPath = {
  role: '/iam',
  tree: '/masterDataManagement'
}

/**
 * @description: 国家 - 查询所有
 * @param {*}
 * @return {*}
 */
export const countryGet = (data) => API.post(`${urlPath.tree}/common/country/queryAll`, data)

/**
 * @description: 货币 - 查询已激活
 * @param {*}
 * @return {*}
 */
export const currencyGet = (data) =>
  API.get(`${urlPath.tree}/common/currency/queryActiveCurrency`, data)

/**
 * @description: 字典 - common
 * @param {*}
 * @return {*}
 */
export const dictTypeGet = (data) => API.post(`${urlPath.tree}/common/dict-item/dict-code`, data)

/**
 * @description: 字典 - tenant
 * @param {*}
 * @return {*}
 */
export const TenantDictTypeGet = (data) =>
  API.post(`${urlPath.tree}/tenant/dict-item/dict-code`, data)

/**
 * @description: 字典 - 获取字典明细树
 * @param {*}
 * @return {*}
 */
export const TenantDictTreeGet = (data) =>
  API.post(`${urlPath.tree}/tenant/dict-item/item-tree`, data)

export const TenantDictTreeI18nGet = (data) =>
  API.post(`${urlPath.tree}/tenant/dict-item/item-tree-i18n`, data)

/**
 * @description: 字典-通用 - 获取字典明细树
 * @param {*}
 * @return {*}
 */
export const commonDictTreeGet = (data) =>
  API.post(`${urlPath.tree}/common/dict-item/item-tree`, data)

/**
 * @description: 单位 - 条件查询
 * @param {*}
 * @return {*}
 */
export const unitDataGet = (data) =>
  API.post(
    `${urlPath.tree}/tenant/unit/criteria-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )

// 租户级-根据id获取字典类型详情-国际化
export const getTenantDictByIdI18n = (data) =>
  API.get(`${urlPath.tree}/tenant/dict/find-by-id-i18n`, data)

// 通用级-根据id获取字典类型详情-国际化
export const getDictItemByIdI18n = (data) =>
  API.get(`${urlPath.tree}/tenant/dict-item/find-by-id-i18n`, data)
// 导出字典
export const exportDict = () =>
  API.get(
    `${urlPath.tree}/tenant/dict/export-data`,
    {},
    {
      responseType: 'blob'
    }
  )
// 字典导出json
export const exportDataJson = (data) =>
  API.post(`${urlPath.tree}/common/dict/export-data-json`, data)
// 字典导入json
export const importDataJson = (data) =>
  API.post(`${urlPath.tree}/common/dict/import-data-json`, data)
// 字典明细导出json
export const exportItemDataJson = (data) =>
  API.post(`${urlPath.tree}/tenant/dict-item/export-data-json`, data)
// 字典明细导入json
export const importItemDataJson = (data) =>
  API.post(`${urlPath.tree}/tenant/dict-item/import-data-json`, data)
// 序号生成器导出json
export const exportCodeGeneratorJson = (data) =>
  API.post(`${urlPath.tree}/tenant/code-generator/export-json`, data)
// 序号生成器导入json
export const importCodeGeneratorJson = (data) =>
  API.post(`${urlPath.tree}/tenant/code-generator/import-json`, data)
