import { API } from '@mtech-common/http'
//业务组织
export const NAME = 'itemManagement'

const urlPath = {
  role: '/iam',
  tree: '/masterDataManagement'
}
//common/dict-item/item-tree
/**
 * @description: 字典  1级
 * @return {*}
 * @param data
 */
export const itemTreelistOne = (data) => {
  return API.post(`${urlPath.tree}/tenant/dict-item/item-tree`, data)
} //
/**
 * @description: 字典  2级
 * @param {*}
 * @return {*}
 */
export const itemTreelistTwo = (data) => {
  return API.post(
    `${urlPath.tree}/tenant/unit/criteria-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )
}
/**
 * @description: 物料组
 * @param {*}
 * @return {*}
 */
export const itemGroupNamelist = (data) => {
  return API.post(`${urlPath.tree}/tenant/item-group/criteria-query `, data)
}
/**
 * @description: 业务组
 * @param {*}
 * @return {*}
 */
export const businessGroupNamelist = (data) => {
  return API.post(
    `${urlPath.tree}/tenant/business-group/criteria-query?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`,
    data
  )
}

// "/api/file/user/file/files/path?useType=2",
export const imagePath = (data) => API.post(`/file/user/file/files/path?useType=2`, data)
// 图片查询
export const imageQuery = (data) => API.post(`${urlPath.tree}//tenant/item-files/query`, data)
// 图片删除
export const imageDelete = (data) =>
  API.delete(`${urlPath.tree}//tenant/item-files/delete`, data, {
    useNotify: true
  })
// 图片新增
export const image = (data) =>
  API.post(`${urlPath.tree}/tenant/item-files/add`, data, {
    useNotify: true
  })
/**
 * @description:质量编辑验证(详情)
 * @param {*}
 * @return {*}
 */
export const itemQualityUpdateValid = (data) => {
  return API.get(`${urlPath.tree}/tenant/item-quality/update-valid`, data)
}
/**
 * @description:质量编辑(详情)
 * @param {*}
 * @return {*}
 */
export const itemQualityUpdate = (data) => {
  return API.post(`${urlPath.tree}/tenant/item-quality/update`, data, {
    useNotify: true
  })
}
/**
 * @description:质量(详情)
 * @param {*}
 * @return {*}
 */
export const itemQualityDetail = (data) => {
  return API.post(`${urlPath.tree}/tenant/item-quality/detail`, data)
}
/**
 * @description:储存编辑验证(详情)
 * @param {*}
 * @return {*}
 */
export const itemWarehouseUpdateValid = (data) => {
  return API.get(`${urlPath.tree}/tenant/item-warehouse/update-valid`, data)
}
/**
 * @description:储存编辑(详情)
 * @param {*}
 * @return {*}
 */
export const itemWarehouseUpdate = (data) => {
  return API.post(`${urlPath.tree}/tenant/item-warehouse/update`, data, {
    useNotify: true
  })
}
/**
 * @description:储存(详情)
 * @param {*}
 * @return {*}
 */
export const itemWarehouseDetail = (data) => {
  return API.post(`${urlPath.tree}/tenant/item-warehouse/detail`, data)
}
/**
 * @description:储存编辑验证(基础信息)
 * @param {*}
 * @return {*}
 */
export const itemWarehouseBasicUpdateValid = (data) => {
  return API.get(`${urlPath.tree}/tenant/item-warehouse/basic-update-valid`, data)
}
/**
 * @description:储存编辑(基础信息)
 * @param {*}
 * @return {*}
 */
export const itemWarehouseBasicUpdate = (data) => {
  return API.post(`${urlPath.tree}/tenant/item-warehouse/basic-update`, data, {
    useNotify: true
  })
}
/**
 * @description:储存编辑(基础信息)同时
 * @param {*}
 * @return {*}
 */
export const itemWarehouseBothUpdate = (data) => {
  return API.post(`${urlPath.tree}/tenant/item-warehouse/both-update`, data, {
    useNotify: true
  })
}
/**
 * @description:采购编辑(基础信息)同时
 * @param {*}
 * @return {*}
 */
export const itemPurchasingBothUpdate = (data) => {
  return API.post(`${urlPath.tree}/tenant/item-purchasing/both-update`, data, {
    useNotify: true
  })
}
/**
 * @description:储存(基础信息)
 * @param {*}
 * @return {*}
 */
export const temWarehouseBasicDetail = (data) => {
  return API.post(`${urlPath.tree}/tenant/item-warehouse/basic-detail`, data)
}
/**
 * @description:采购编辑验证(详情)
 * @param {*}
 * @return {*}
 */
export const itemPurchasingUpdateValid = (data) => {
  return API.get(`${urlPath.tree}/tenant/item-purchasing/update-valid`, data)
}
/**
 * @description:采购编辑(详情)
 * @param {*}
 * @return {*}
 */
export const itemPurchasingUpdate = (data) => {
  return API.post(`${urlPath.tree}/tenant/item-purchasing/update`, data, {
    useNotify: true
  })
}
/**
 * @description:采购(详情)
 * @param {*}
 * @return {*}
 */
export const itemPurchasingDetail = (data) => {
  return API.post(`${urlPath.tree}/tenant/item-purchasing/detail`, data)
}
/**
 * @description:采购编辑验证(基础信息)
 * @param {*}
 * @return {*}
 */
export const itemPurchasingBasicUpdateValid = (data) => {
  return API.get(`${urlPath.tree}/tenant/item-purchasing/basic-update-valid`, data)
}
/**
 * @description:采购编辑(基础信息)
 * @param {*}
 * @return {*}
 */
export const itemPurchasingBasicUpdate = (data) => {
  return API.post(`${urlPath.tree}/tenant/item-purchasing/basic-update`, data, {
    useNotify: true
  })
}
/**
 * @description:采购(基础信息)
 * @param {*}
 * @return {*}
 */
export const itemPurchasingBasicDetail = (data) => {
  return API.post(`${urlPath.tree}/tenant/item-purchasing/basic-detail`, data)
}
/**
 * @description:计划编辑验证
 * @param {*}
 * @return {*}
 */
export const itemPlanningUpdateValid = (data) => {
  return API.get(`${urlPath.tree}/tenant/item-planning/update-valid`, data)
}
/**
 * @description:计划编辑
 * @param {*}
 * @return {*}
 */
export const itemPlanningUpdate = (data) => {
  return API.post(`${urlPath.tree}/tenant/item-planning/update`, data, {
    useNotify: true
  })
}
/**
 * @description:计划
 * @param {*}
 * @return {*}
 */
export const itemPlanningDetail = (data) => {
  return API.post(`${urlPath.tree}/tenant/item-planning/detail`, data)
}
/**
 * @description: 获取关联的工厂列表
 * @param {*}
 * @return {*}
 */
export const itemFindRelSites = (data) => {
  return API.post(
    `${urlPath.tree}/tenant/item/find-rel-sites?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )
}
/**
 * @description: 品项管理 - 列表
 * @param {*}
 * @return {*}
 */
export const itemlist = (data) => {
  return API.post(
    `${urlPath.tree}/tenant/item/fuzzy-paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )
}
/**
 * @description: 品项管理 - 详情
 * @param {*}
 * @return {*}
 */
export const itemDetails = (data) => {
  return API.get(
    `${urlPath.tree}/tenant/item/item?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )
}
/**
 * @description: 品项管理 - 新增
 * @param {*}
 * @return {*}
 */
export const itemAdd = (data) => {
  return API.post(`${urlPath.tree}/tenant/item/add`, data, {
    useNotify: true
  })
}
/**
 * @description: 品项管理 - 新增校验
 * @param {*}
 * @return {*}
 */
export const itemAddValid = (data) => {
  return API.get(`${urlPath.tree}/tenant/item/add-valid`, data)
}

/**
 * @description: 品项管理 - 编辑校验
 * @param {*}
 * @return {*}
 */
export const itemUpdataValid = (data) => {
  return API.get(`${urlPath.tree}/tenant/item/update-basic-valid`, data)
}

/**
 * @description: 品项管理 - 编辑
 * @param {*}
 * @return {*}
 */
export const itemUpdata = (data) => {
  return API.put(`${urlPath.tree}/tenant/item/update-basic`, data, {
    useNotify: true
  })
}
/**
 * @description: 品项管理详情 - 编辑校验
 * @param {*}
 * @return {*}
 */
export const itemUpdataValids = (data) => {
  return API.get(`${urlPath.tree}/tenant/item/update-valid`, data)
}

/**
 * @description: 品项管理详情 - 编辑
 * @param {*}
 * @return {*}/api/masterDataManagement/tenant/item/update-basic
 */
export const itemUpdatas = (data) => {
  return API.put(`${urlPath.tree}/tenant/item/update`, data, {
    useNotify: true
  })
}
/**
 * @description: 品项管理 - 编辑
 * @param {*}
 * @return {*}/api/masterDataManagement/tenant/item/update-basic
 */
export const classificationAdd = (data) => {
  return API.put(`${urlPath.tree}/tenant/item-classification/add`, data, {
    useNotify: true
  })
}
/**
 * @description: 品项管理 - 删除
 * @param {*}
 * @return {*}
 */
export const batchDelete = (data) => {
  return API.post(`${urlPath.tree}/tenant/item/batche-del-item`, data, {
    useNotify: true
  })
}

/**
 * @description: 下属组织 - 新增
 * @param {*}
 * @return {*}
 */

export const addpost = (data) => {
  return API.post(
    `${urlPath.tree}/tenant/business-org-org-rel/add?BU_CODE=${localStorage.getItem('currentBu')}`,
    data,
    {
      useNotify: true
    }
  )
}

/**
 * @description: 特性 - 新增
 * @param {*}
 * @return {*}
 */

export const texingAdd = (data) => {
  return API.post(`${urlPath.tree}/tenant/item-classification/batch-add`, data, {
    useNotify: true
  })
}
/**
 * @description: 特性 - 列表
 * @param {*}
 * @return {*}
 */
export const texinglist = (data) => {
  return API.post(`${urlPath.tree}/tenant/item-classification/query-by-item`, data)
}
//
/**
 * @description: 特性 - 删除
 * @param {*}
 * @return {*}
 */
export const texingDelete = (data) => {
  return API.delete(`${urlPath.tree}/tenant/item-classification/batch-delete`, data, {
    useNotify: true
  })
}
/**
 * @description: 特性 - 编辑
 * @param {*}
 * @return {*}
 */
export const texingedit = (data) => {
  return API.post(`${urlPath.tree}/tenant/item-classification/batch-update`, data)
}
/**
 * @description: 计量单位 - 列表
 * @param {*}
 * @return {*}
 */
export const danweilist = (data) => {
  return API.post(`${urlPath.tree}/tenant/item-unit-exchange/list-by-item`, data)
}
export const unit = (data) => {
  return API.post(
    `${urlPath.tree}/tenant/unit/criteria-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )
}
/**
 * @description: 计量单位 - 新增
 * @param {*}
 * @return {*}
 */
export const unitadd = (data) => {
  return API.post(`${urlPath.tree}/tenant/item-unit-exchange/batch-add`, data)
}
/**
 * @description: 计量单位 - 新增
 * @param {*}
 * @return {*}
 */
export const unitdelete = (data) => {
  return API.delete(`${urlPath.tree}/tenant/item-unit-exchange/batch-delete`, data)
}
// tenant/item-unit-exchange/add
//
/**
 * @description: 品类 - 列表
 * @param {*}
 * @return {*}
 */
export const pinleilist = (data) => {
  return API.get(`${urlPath.tree}/tenant/category-item/find-by-item`, data)
}
/**
 * @description: 品类 - 列表
 * @param {*}
 * @return {*}
 */
export const getFuzzyCompanyTree = (data) => {
  return API.post(`${urlPath.tree}/tenant/category-type-org/getFuzzyCompanyTree`, data)
}
//
/**
 * @description: 品类 - 列表
 * @param {*}
 * @return {*}
 */
export const criteriaQuery = (data) => {
  return API.post(`${urlPath.tree}/tenant/category-type/criteria-query`, data)
}

/**
 * @description: 品类 - 列表
 * @param {*}
 * @return {*}
 */
export const getTree = (data) => {
  return API.post(`${urlPath.tree}/tenant/category/getTree`, data)
}
/**
 * @description: 品类 - 新增
 * @param {*}
 * @return {*}
 */
export const pinleiAdd = (data) => {
  return API.post(`${urlPath.tree}/tenant/category-item/add`, data)
}
/**
 * @description: 品类 - 删除
 * @param {*}
 * @return {*}
 */
export const pinleidelete = (data) => {
  return API.delete(`${urlPath.tree}/tenant/category-item/batch-delete`, data)
}
