/*
 * @Author: your name
 * @Date: 2021-08-25 09:57:39
 * @LastEditTime: 2022-01-12 11:19:40
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\apis\modules\category.js
 */
import { API } from '@mtech-common/http'

// 品类树
export const NAME = 'category'

const urlPath = {
  role: '/iam',
  tree: '/masterDataManagement'
}

/**
 * @description: 组织树 - 查询
 * @param {*}
 * @return {*}
 */
export const treeDataGet = (data) =>
  API.post(`${urlPath.tree}/tenant/category-type-org/getFuzzyCompanyTree`, data)

/**
 * @description: 品类树 - 新增
 * @param {*}
 * @return {*}
 */
export const cateTreeAdd = (data) =>
  API.post(`${urlPath.tree}/tenant/category-type/add`, data, {
    useNotify: true
  })

/**
 * @description: 品类树 - 新增验证
 * @param {*}
 * @return {*}
 */
export const cateTreeAddValid = (data) =>
  API.get(`${urlPath.tree}/tenant/category-type/add-valid`, data)

/**
 * @description: 品类树 - 编辑
 * @param {*}
 * @return {*}
 */
export const cateTreeEdit = (data) =>
  API.put(`${urlPath.tree}/tenant/category-type/update`, data, {
    useNotify: true
  })

/**
 * @description: 品类树 - 编辑验证
 * @param {*}
 * @return {*}
 */
export const cateTreeEditValid = (data) =>
  API.get(`${urlPath.tree}/tenant/category-type/update-valid`, data)

/**
 * @description: 品类树 - 删除
 * @param {*}
 * @return {*}
 */
export const cateTreeDelete = (data) =>
  API.post(`${urlPath.tree}/tenant/category-type/batch-delete`, data, {
    useNotify: true
  })

/**
 * @description: 品类树 - 条件查询
 * @param {*}
 * @return {*}
 */
export const cateTypeQueryNoPages = (data) =>
  API.post(`${urlPath.tree}/tenant/category-type/criteria-query`, data)

/**
 * @description: 品类树 - 根据品类类别id以及组织id查询详情
 * @param {*}
 * @return {*}
 */
export const cateTreeInfoGet = (data) =>
  API.post(`${urlPath.tree}/tenant/category-type-org/category-type-org-detail`, data)

/**
 * @description: 品类树 - 根据ID获取树结构数据
 * @param {*}
 * @return {*}
 */
export const cateTreeNodeDetailGet = (data) =>
  API.post(`${urlPath.tree}/tenant/category/getTree`, data)

/**
 * @description: 跟随上级
 * @param {*}
 * @return {*}
 */
export const followUpLevelChange = (data) =>
  API.post(`${urlPath.tree}/tenant/category-type-org/category-type-org-update`, data, {
    useNotify: true
  })

/**
 * @description: 复制品类
 * @param {*}
 * @return {*}
 */
export const cateCopy = (data) =>
  API.post(`${urlPath.tree}/tenant/category/copy-category`, data, {
    useNotify: true
  })

/**
 * @description: 品类树节点 - 新增
 * @param {*}
 * @return {*}
 */
export const cateTreeNodeAdd = (data) =>
  API.post(`${urlPath.tree}/tenant/category/add`, data, {
    useNotify: true
  })

/**
 * @description: 品类树节点 - 新增验证
 * @param {*}
 * @return {*}
 */
export const cateTreeNodeAddValid = (data) =>
  API.get(`${urlPath.tree}/tenant/category/add-valid`, data)

/**
 * @description: 品类树节点 - 编辑
 * @param {*}
 * @return {*}
 */
export const cateTreeNodeEdit = (data) =>
  API.put(`${urlPath.tree}/tenant/category/update`, data, {
    useNotify: true
  })

/**
 * @description: 品类树节点 - 编辑验证
 * @param {*}
 * @return {*}
 */
export const cateTreeNodeEditValid = (data) =>
  API.get(`${urlPath.tree}/tenant/category/update-valid`, data)

/**
 * @description: 品类树节点 - 删除
 * @param {*}
 * @return {*}
 */
export const cateTreeNodeDelete = (data) =>
  API.delete(
    `${urlPath.tree}/tenant/category/delete?id=${data.id}`,
    {},
    {
      useNotify: true
    }
  )

/**
 * @description: 关联品类tab - treegrid - 条件查询
 * @param {*}
 * @return {*}
 */
export const cateRelateQuery = (data) =>
  API.get(`${urlPath.tree}/tenant/category-rel/query-by-category`, data)

/**
 * @description: 品类关联 - 新增
 * @param {*}
 * @return {*}
 */
export const cateRelate = (data) =>
  API.post(`${urlPath.tree}/tenant/category-rel/category-rel-add`, data, {
    useNotify: true
  })

/**
 * @description: 物料/品项 - 条件查询
 * @param {*}
 * @return {*}
 */
export const materialQuery = (data) =>
  API.get(`${urlPath.tree}/tenant/category-item/find-by-category`, data)

/**
 * @description: 物料/品项 - 添加
 * @param {*}
 * @return {*}
 */
export const materialAdd = (data) =>
  API.post(`${urlPath.tree}/tenant/category-item/batch-add`, data, {
    useNotify: true
  })

/**
 * @description: 物料/品项 - 添加
 * @param {*}
 * @return {*}
 */
export const materialDel = (data) =>
  API.delete(`${urlPath.tree}/tenant/category-item/batch-delete`, data, {
    useNotify: true
  })

/**
 * @description: SKU - 条件查询
 * @param {*}
 * @return {*}
 */
export const skuQuery = (data) => API.post(`${urlPath.tree}/tenant/sku/list-by-category`, data)

/**
 * @description: 系列物料公式 - 条件查询
 * @param {*}
 * @return {*}
 */
export const categorySeriesFormulaQuery = (data) =>
  API.post(`${urlPath.tree}/tenant/category-series-formula/criteria-query`, data)

/**
 * @description: 系列物料公式 - 批量新增
 * @param {*}
 * @return {*}
 */
export const categorySeriesFormulaBatchAdd = (data) =>
  API.post(`${urlPath.tree}/tenant/category-series-formula/batch-add`, data)

//
/**
 * @description: 系列物料公式 - 批量删除
 * @param {*}
 * @return {*}
 */
export const categorySeriesFormulaBatchDelete = (data) => {
  return API.delete(`${urlPath.tree}/tenant/category-series-formula/batch-delete`, data)
}
export const pagedQueryCategoryRel = (data) =>
  API.post(`${urlPath.tree}/tenant/category-rel/pagedQueryCategoryRel`, data)
export const addCategoryRel = (data) =>
  API.post(`${urlPath.tree}/tenant/category-rel/addCategoryRel`, data)
export const delCategoryRel = (data) =>
  API.post(`${urlPath.tree}/tenant/category-rel/delCategoryRel`, data)
