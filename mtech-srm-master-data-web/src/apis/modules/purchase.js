/*
 * @Author: your name
 * @Date: 2021-09-03 17:39:36
 * @LastEditTime: 2021-09-09 13:46:15
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\apis\modules\roles.js
 */
import { API } from '@mtech-common/http'
//业务组织
export const NAME = 'purchase'

const urlPath = {
  role: '/iam',
  tree: '/masterDataManagement'
}

/**
 * @description: 业务组织 - 新增
 * @param {*}
 * @return {*}
 */
export const treeAdd = (data) => {
  return API.post(`${urlPath.tree}/tenant/business-organization/add`, data, {
    useNotify: true
  })
}
/**
 * @description: 角色 - 新增校验
 * @param {*}
 * @return {*}
 */
export const roleAddValid = (data) => {
  return API.get(`${urlPath.tree}/tenant/business-organization/add-valid`, data)
}

/**
 * @description: 角色 - 编辑校验
 * @param {*}
 * @return {*}
 */
export const updataRolesValid = (data) => {
  return API.get(`${urlPath.tree}/tenant/business-organization/update-valid`, data)
}

/**
 * @description: 业务组织 - 编辑
 * @param {*}
 * @return {*}
 */
export const updataRoles = (data) => {
  return API.put(`${urlPath.tree}/tenant/business-organization/update`, data, {
    useNotify: true
  })
}

/**
 * @description: 业务组织 - 删除
 * @param {*}
 * @return {*}
 */
export const batchDelete = (data) => {
  return API.delete(`${urlPath.tree}/tenant/business-organization/batch-delete`, data, {
    useNotify: true
  })
}
/**
 * @description: 下属组织 - 删除
 * @param {*}
 * @return {*}
 */
export const batchDeletes = (data) => {
  return API.delete(`${urlPath.tree}/tenant/business-org-org-rel/batch-delete`, data, {
    useNotify: true
  })
}
/**
 * @description: 下属组织 - 新增
 * @param {*}
 * @return {*}
 */

export const addpost = (data) => {
  return API.post(
    `${urlPath.tree}/tenant/business-org-org-rel/add?BU_CODE=${localStorage.getItem('currentBu')}`,
    data,
    {
      useNotify: true
    }
  )
}
