/*
 * @Author: your name
 * @Date: 2021-08-25 09:57:39
 * @LastEditTime: 2022-03-20 13:39:02
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\apis\modules\org.js
 */
import { API } from '@mtech-common/http'

// 组织
export const NAME = 'org'

const urlPath = {
  role: '/iam',
  tree: '/masterDataManagement'
}

/**
 * @description: 获取组织树
 * @param {*}
 * @return {*}
 */
export const getAdmTree = (data) =>
  API.post(`${urlPath.tree}/tenant/organization/getFuzzyCompanyTree`, data)

/**
 * @description: 获取组织树 含工厂
 * @param {*}
 * @return {*}
 */
export const getOrgTree = (data) =>
  API.post(`${urlPath.tree}/tenant/category-type-org/getFuzzyCompanyTree`, data)

/**
 * @description: 获取组织树 业务组关系新增用
 * @param {*}
 * @return {*}
 */
export const getOrgTreeInGroup = (data) =>
  API.post(`${urlPath.tree}/tenant/organization/findSpecifiedChildrenLevelOrgs`, data)

/**
 * @description: 节点选择获取详情信息
 * @param {*}
 * @return {*}
 */
export const getNodeInfo = (data) =>
  API.post(`${urlPath.tree}/tenant/organization/findOrgCompanyByOrgId`, data)

/**
 * @description: 获取公司
 * @param {*}
 * @return {*}
 */
export const getCompany = (data) => API.post(`${urlPath.tree}/tenant/entity/criteria-query`, data)

/**
 * @description: 新增组织节点保存 - 公司
 * @param {*}
 * @return {*}
 */
export const cNodeSave = (data) =>
  API.post(`${urlPath.tree}/tenant/organization/addEntityToOrg`, data, {
    useNotify: true
  })

/**
 * @description: 新增组织节点保存 - 公司验证
 * @param {*}
 * @return {*}
 */
export const cNodeSaveValid = (data) =>
  API.get(`${urlPath.tree}/tenant/organization/addEntityToOrg-valid`, data)

/**
 * @description: 新增组织节点保存 - 事业群
 * @param {*}
 * @return {*}
 */
export const bNodeSave = (data) =>
  API.post(`${urlPath.tree}/tenant/bg/add`, data, {
    useNotify: true
  })

/**
 * @description: 新增组织节点保存 - 事业群验证
 * @param {*}
 * @return {*}
 */
export const bNodeSaveValid = (data) => API.get(`${urlPath.tree}/tenant/bg/add-valid`, data)

/**
 * @description: 新增组织节点保存 - 事业群
 * @param {*}
 * @return {*}
 */
export const clientNodeSave = (data) =>
  API.post(`${urlPath.tree}/tenant/client/add-palte`, data, {
    useNotify: true
  })

/**
 * @description: 组织节点删除
 * @param {*}
 * @return {*}
 */
export const nodeDel = (data) =>
  API.delete(`${urlPath.tree}/tenant/organization/delOrgDetail`, data, {
    useNotify: true
  })

/**
 * @description: 新增组织节点保存 - 事业群验证
 * @param {*}
 * @return {*}
 */
export const clientNodeSaveValid = (data) =>
  API.get(`${urlPath.tree}/tenant/client/add-palte-valid`, data)

/**
 * @description: 获取公司下部门树节点
 * @param {*}
 * @return {*}
 */
export const getDepartTreeData = (data) =>
  API.post(`${urlPath.tree}/tenant/organization/getCompanyDepartmentTree`, data)

/**
 * @description: 新增部门/子部门
 * @param {*}
 * @return {*}
 */
export const departSave = (data) =>
  API.post(`${urlPath.tree}/tenant/department/add`, data, {
    useNotify: true
  })

/**
 * @description: 新增部门/子部门验证
 * @param {*}
 * @return {*}
 */
export const departSaveValid = (data) =>
  API.get(`${urlPath.tree}/tenant/department/add-valid`, data)

/**
 * @description: 编辑部门/子部门
 * @param {*}
 * @return {*}
 */
export const departEditSave = (data) =>
  API.post(`${urlPath.tree}/tenant/organization/updateOrgDetail`, data, {
    useNotify: true
  })

/**
 * @description: 编辑部门/子部门验证
 * @param {*}
 * @return {*}
 */
export const departEditSaveValid = (data) =>
  API.get(`${urlPath.tree}/tenant/organization/updateOrgDetail-valid`, data)

/**
 * @description: 岗位 - 条件查询
 * @param {*}
 * @return {*}
 */
export const stationAllGet = (data) =>
  API.post(`${urlPath.tree}/tenant/standard-station/queryExcludeStandardStations`, data)

/**
 * @description: 部门下岗位 - 条件查询
 * @param {*}
 * @return {*}
 */
export const stationSelectGet = (data) =>
  API.post(`${urlPath.tree}/tenant/department/findStationInDepartment`, data)

/**
 * @description: 新增岗位
 * @param {*}
 * @return {*}
 */
export const stationAddSave = (data) =>
  API.post(`${urlPath.tree}/tenant/standard-station/add`, data, {
    useNotify: true
  })

/**
 * @description: 新增岗位验证
 * @param {*}
 * @return {*}
 */
export const stationAddSaveValid = (data) =>
  API.get(`${urlPath.tree}/tenant/standard-station/add-valid`, data)

/**
 * @description: 更新岗位
 * @param {*}
 * @return {*}
 */
export const stationEditSave = (data) =>
  API.put(`${urlPath.tree}/tenant/standard-station/update`, data, {
    useNotify: true
  })

/**
 * @description: 更新岗位验证
 * @param {*}
 * @return {*}
 */
export const stationEditSaveValid = (data) =>
  API.get(`${urlPath.tree}/tenant/standard-station/update-valid`, data)

/**
 * @description: 岗位列表 - 删除
 * @param {*}
 * @return {*}
 */
export const stationDelete = (data) =>
  API.delete(`${urlPath.tree}/tenant/standard-station/batch-delete`, data, {
    useNotify: true
  })

/**
 * @description: 管理岗位
 * @param {*}
 * @return {*}
 */
export const stationManageSave = (data) =>
  API.post(`${urlPath.tree}/tenant/department/departmentChooseStations`, data, {
    useNotify: true
  })

export const getChildrenCompanyOrganization = (data) =>
  API.post(`${urlPath.tree}/tenant/organization/getChildrenCompanyOrganization`, data)

export const getChildrenDepartmentOrganization = (data) =>
  API.post(`${urlPath.tree}/tenant/organization/getChildrenDepartmentOrganization`, data)

export const getUserInfo = (data) => API.get(`${urlPath.role}/common/account/userinfo`, data)
