/**
 * 组合物料 pack_item
 */

import { API } from '@mtech-common/http'
export const NAME = 'packItem'
const PROXY_MASTER_DATA = '/masterDataManagement'

const APIS = {
  // 分页查询
  pagedQuery: `${PROXY_MASTER_DATA}/tenant/pack_item/paged-query`,

  // 保存
  save: (data) => {
    return API.post(`${PROXY_MASTER_DATA}/tenant/pack_item/save`, data)
  },

  // 批量更新状态
  batchUpdate: (data) => {
    return API.put(`${PROXY_MASTER_DATA}/tenant/pack_item/batch-update`, data)
  }
}

export default APIS
