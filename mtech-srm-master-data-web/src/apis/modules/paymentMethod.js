/*
 * @Author: your name
 * @Date: 2021-08-25 09:57:39
 * @LastEditTime: 2021-10-21 10:14:04
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\apis\modules\org.js
 */
import { API } from '@mtech-common/http'

// 付款方式
export const NAME = 'paymentMethod'

const urlPath = {
  role: '/iam',
  tree: '/masterDataManagement'
}

/**
 * @description: 查询分配组织
 * @param {*}
 * @return {*}
 */
export const criteriaQuery = (data) =>
  API.post(`${urlPath.tree}/tenant/paymethod-org-rel/criteria-query`, data)
/**
 * @description: 右侧弹框删除接口
 * @param {*}
 * @return {*}
 */
export const batchDelete = (data) =>
  API.delete(`${urlPath.tree}/tenant/paymethod-org-rel/batch-delete`, data)
/**
 * @description: 点击新增模糊查询所有公司
 * @param {*}
 * @return {*}
 */
export const findSpecifiedChildrenLevelOrgs = (data) =>
  API.post(`${urlPath.tree}/tenant/organization/findSpecifiedChildrenLevelOrgs`, data)
/**
 * @description: 批量新增接口
 * @param {*}
 * @return {*}
 */
export const batchAdd = (data) =>
  API.post(`${urlPath.tree}/tenant/paymethod-org-rel/batch-add`, data)
