/*
 * @Author: your name
 * @Date: 2021-09-03 17:39:36
 * @LastEditTime: 2022-02-23 11:03:48
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\apis\modules\roles.js
 */
import { API } from '@mtech-common/http'

export const NAME = 'roles'

const urlPath = {
  role: '/iam',
  tree: '/masterDataManagement'
}

/**
 * @description: 角色 - 新增 srm
 * @param {*}
 * @return {*}
 */
export const roleAdd = (data) => {
  return API.post(`${urlPath.role}/tenant/role/add`, data, {
    useNotify: true
  })
}
/**
 * @description: 角色 - 新增 platform
 * @param {*}
 * @return {*}
 */
export const roleAddAdmin = (data) => {
  return API.post(`${urlPath.role}/admin/role/add`, data, {
    useNotify: true
  })
}
/**
 * @description: 角色 - 新增校验 srm
 * @param {*}
 * @return {*}
 */
export const roleAddValid = (data) => {
  return API.get(`${urlPath.role}/tenant/role/add-valid`, data)
}
/**
 * @description: 角色 - 新增校验 admin
 * @param {*}
 * @return {*}
 */
export const roleAddValidAdmin = (data) => {
  return API.get(`${urlPath.role}/admin/role/add-valid`, data)
}
/**
 * @description: 角色 - 编辑 srm
 * @param {*}
 * @return {*}
 */
export const updataRoles = (data) => {
  return API.put(`${urlPath.role}/tenant/role/update`, data, {
    useNotify: true
  })
}
/**
 * @description: 角色 - 编辑 admin
 * @param {*}
 * @return {*}
 */
export const updataRolesAdmin = (data) => {
  return API.put(`${urlPath.role}/admin/role/update`, data, {
    useNotify: true
  })
}
/**
 * @description: 角色 - 编辑校验
 * @param {*}
 * @return {*}
 */
export const updataRolesValid = (data) => {
  return API.get(`${urlPath.role}/tenant/role/update-valid`, data)
}

/**
 * @description: 角色 - 删除 srm
 * @param {*}
 * @return {*}
 */
export const batchDelete = (data) => {
  return API.delete(`${urlPath.role}/tenant/role/batch-delete`, data, {
    useNotify: true
  })
}
/**
 * @description: 角色 - 删除 平台
 * @param {*}
 * @return {*}
 */
export const batchDeleteAdmin = (data) => {
  return API.delete(`${urlPath.role}/admin/role/batch-delete`, data, {
    useNotify: true
  })
}
/**
 * @description: 获取指定范围树结构数据
 * @param {*}
 * @return {*}
 */
export const getStatedLimitTree = (data) => {
  return API.post(`${urlPath.tree}/tenant/organization/getStatedLimitTree`, data)
}

/**
 * @description: 用户组明细 条件查询接口 srm
 * @param {*}
 * @return {*}
 */
export const userGroupCriteriaQuery = (data) => {
  return API.post(`${urlPath.role}/tenant/user-group-item/criteria-query`, data)
}

/**
 * @description: 用户组明细 条件查询接口 admin
 * @param {*}
 * @return {*}
 */
export const userGroupCriteriaQueryAdmin = (data) => {
  return API.post(`${urlPath.role}/admin/user-group-item/criteria-query`, data)
}

/**
 * @description: 用户组明细 新增/编辑 srm
 * @param {*}
 * @return {*}
 */
export const userGroupSet = (data) => {
  return API.post(`${urlPath.role}/tenant/role/set-items`, data, {
    useNotify: true
  })
}
/**
 * @description: 用户组明细 新增/编辑 admin
 * @param {*}
 * @return {*}
 */
export const userGroupSetAdmin = (data) => {
  return API.post(`${urlPath.role}/admin/role/set-items`, data, {
    useNotify: true
  })
}
/**
 * @description: 用户组 - 删除 srm
 * @param {*}
 * @return {*}
 */
export const userGroupDelete = (data) => {
  return API.delete(`${urlPath.role}/tenant/user-group/batch-delete`, data, {
    useNotify: true
  })
}
/**
 * @description: 用户组 - 删除admin
 * @param {*}
 * @return {*}
 */
export const userGroupDeleteAdmin = (data) => {
  return API.delete(`${urlPath.role}/admin/user-group/batch-delete`, data, {
    useNotify: true
  })
}
/**
 * @description: 查询页面元素树结构
 * @param {*}
 * @return {*}
 */
export const menuTreeGet = (data) => {
  return API.post(`${urlPath.role}/tenant/permission/list-element-tree`, data)
}
/**
 * @description: 查询页面元素树结构
 * @param {*}
 * @return {*}
 */
export const menuTreeGetAdmin = (data) => {
  return API.post(`${urlPath.role}/admin/permission/list-element-tree`, data)
}
/**
 * @description: 功能权限 - 获取已分配
 * @param {*}
 * @return {*}
 */
export const actionNodeSelectGet = (data) => {
  return API.post(`${urlPath.role}/tenant/role-permission-rel/list`, data)
}
/**
 * @description: 功能权限 - 获取已分配admin
 * @param {*}
 * @return {*}
 */
export const actionNodeSelectGetAdmin = (data) => {
  return API.post(`${urlPath.role}/admin/role-permission-rel/list`, data)
}
/**
 * @description: 功能权限 - 保存分配
 * @param {*}
 * @return {*}
 */
export const actionNodeSelectSave = (data) => {
  return API.post(`${urlPath.role}/tenant/role-permission-rel/set`, data, {
    useNotify: true
  })
}
/**
 * @description: 功能权限 - 保存分配
 * @param {*}
 * @return {*}
 */
export const actionNodeSelectSaveAdmin = (data) => {
  return API.post(`${urlPath.role}/admin/role-permission-rel/set`, data, {
    useNotify: true
  })
}
/**
 * @description: 获取用户信息接口
 * @param {*}
 * @return {*}
 */
export const getStatedQuery = (data) => {
  return API.post(`${urlPath.tree}/tenant/user/criteria-query`, data)
}

/**
 * @description: 查询高级用户
 * @param {*}
 * @return {*}
 */
export const getPlatformUser = (data) => {
  return API.post(`/iam/tenant/account/management/info/list`, data)
}
/**
 * @description: 查询高级用户
 * @param {*}
 * @return {*}
 */
export const getPlatformUserAdmin = (data) => {
  return API.post(`/iam/admin/account/management/info/list`, data)
}
/**
 * @description: 增加用户组 srm
 * @param {*}
 * @return {*}
 */
export const setUserGroupBase = (data) => {
  return API.post(`/iam/tenant/user-group/add`, data)
}

/**
 * @description: 增加用户组 srm
 * @param {*}
 * @return {*}
 */
export const updateUserGroupBase = (data) => {
  return API.post(`/iam/tenant/user-group/update`, data)
}

/**
 * @description: 增加用户组 srm
 * @param {*}
 * @return {*}
 */
export const updateUserGroupAdmin = (data) => {
  return API.post(`/iam/admin/user-group/update`, data)
}
/**
 * @description: 增加用户组 admin
 * @param {*}
 * @return {*}
 */
export const setUserGroupBaseAdmin = (data) => {
  return API.post(`/iam/admin/user-group/add`, data)
}
/**
 * @description: 增加用户组
 * @param {*}
 * @return {*}
 */
export const getMasterUser = (data) => {
  return API.post(`${urlPath.tree}/tenant/user/criteria-query`, data)
}
/**
 * @description: 获取员工列表
 * @param {*}
 * @return {*}
 */
export const getMasterEmplyee = (data) => {
  return API.get(`${urlPath.tree}/tenant/employee/currentTenantEmployees`, data)
}
/**
 * @description: 增加用户组srm
 * @param {*}
 * @return {*}
 */
export const groupItemAdd = (data) => {
  return API.post(`/iam/tenant/user-group-item/batch-add`, data)
}

/**
 * @description: 增加用户组平台
 * @param {*}
 * @return {*}
 */
export const groupItemAddAdmin = (data) => {
  return API.post(`/iam/admin/user-group-item/batch-add`, data)
}

/**
 * @description: 删除用户组 Srm
 * @param {*}
 * @return {*}
 */
export const deleteGroupSApi = (data) => {
  // return API.delete(`/iam/tenant/user-group/batch-delete`, data);
  return API.post(`/iam/tenant/subject-role-rel/batch-delete`, data)
}

/**
 * @description: 删除用户组 Srm
 * @param {*}
 * @return {*}
 */
export const deleteGroupSAdmin = (data) => {
  // return API.delete(`/iam/tenant/user-group/batch-delete`, data);
  return API.post(`/iam/admin/subject-role-rel/batch-delete`, data)
}
/**
 * @description: 删除用户组 平台
 * @param {*}
 * @return {*}
 */
export const deleteGroupSApiAdmin = (data) => {
  return API.delete(`/iam/admin/user-group/batch-delete`, data)
}

// 获取用户组列表 platform
export const getUserGroupApiAdmin = `/iam/admin/user-group/paged-query`
// 获取用户组列表 srm
export const getUserGroupApi = `/iam/tenant/user-group/paged-query`

// 查询角色列表 srm
export const rolePageQuery = `/iam/tenant/role/paged-query`

// 查询角色列表 srm
export const rolePageQueryAdmin = `/iam/admin/role/paged-query`
/**
 * @description: 用户组 - 查询- srm
 * @param {*}
 * @return {*}
 */
export const QueryUserGroupApiTanent = `/iam/tenant/user-group/paged-query`

/**
 * @description: 用户组 - 查询- admin
 * @param {*}
 * @return {*}
 */
export const QueryUserGroupApiAdmin = `/iam/admin/user-group/paged-query`

/**
 * @description: 增加用户组平台
 * @param {*}
 * @return {*}
 */
export const setUserGroupTanent = (data) => {
  return API.post(`/iam/tenant/subject-role-rel/bind`, data)
}
//--new
export const setUserGroupTanentUser = (data) => {
  return API.post(`/iam/tenant/subject-role-rel/bind-user`, data)
}

/**
 * @description: 增加用户组平台
 * @param {*}
 * @return {*}
 */
export const setUserGroupAdmin = (data) => {
  return API.post(`/iam/admin/subject-role-rel/bind`, data)
}
// 用户查询

export const QueryUserPageTenant = `/masterDataManagement/tenant/user/currentTenantPagedQuery`

// 用户查询--new
export const rolePagedQuery = `/masterDataManagement/tenant/employee/role-paged-query`

export const rolePagedQueryNew = `/masterDataManagement/tenant/employee/role-paged-queryNew`

/**
 * @description: 获取角色已经分配的权限
 * @param {*}
 * @return {*}
 */
export const selectMenuTreeGet = (data) => {
  return API.post(`${urlPath.role}/tenant/role-permission-rel/list-tree`, data)
}
