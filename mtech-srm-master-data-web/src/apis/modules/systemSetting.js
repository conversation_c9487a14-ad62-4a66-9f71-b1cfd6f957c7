import { API } from '@mtech-common/http'

// 系统设置
export const NAME = 'systemSetting'

const urlPath = {
  role: '/iam',
  tree: '/masterDataManagement'
}

// 编码生成器 - 新增
export const addGenerator = (data) => API.post(`${urlPath.tree}/tenant/code-generator/add`, data)

// 编码生成器 - 保存（新增/修改）
export const saveGenerator = (data) => API.post(`${urlPath.tree}/tenant/code-generator/save`, data)

// 编码生成器 - 保存（新增/修改）
export const saveGeneratorValid = (data) =>
  API.get(`${urlPath.tree}/tenant/code-generator/save-valid`, data)

export const addGeneratorValid = (data) =>
  API.get(`${urlPath.tree}/tenant/code-generator/add-valid`, data)

// 编码生成器 - 删除
export const deleteGenerator = (data) =>
  API.delete(`${urlPath.tree}/tenant/code-generator/batch-delete`, data)

// 编码生成器 - 修改
export const updateGenerator = (data) =>
  API.put(`${urlPath.tree}/tenant/code-generator/update`, data)

export const updateGeneratorValid = (data) =>
  API.get(`${urlPath.tree}/tenant/code-generator/update-valid`, data)

// 编码生成器 - 查看
export const getGenerator = (data) =>
  API.post(`${urlPath.tree}/tenant/code-generator/paged-query`, data)

// 编码生成器 - 编辑时获取规则列表
export const getRuleListById = (data) =>
  API.get(`${urlPath.tree}/tenant/code-generator/getByCodeGeneratorId`, data)

// 编码生成器 - 编辑时获取规则列表
export const queryByCodeGeneratorId = (data) =>
  API.get(`${urlPath.tree}/tenant/code-generator/queryByCodeGeneratorId`, data)

// 编码生成器 - 获取服务中的mapper列表
export const getMapperCodeByServiceName = (data) =>
  API.get(`${urlPath.tree}/tenant/code-generator/queryMapperCodes`, data)

// 编码生成器 - 获取服务中的mapper列表
export const getFieldCodesByMapperCode = (data) =>
  API.get(`${urlPath.tree}/tenant/code-generator/queryFieldCodes`, data)

// 编码生成器 - 获取服务中的mapper列表
export const getParamFieldCodesByMapperCode = (data) =>
  API.get(`${urlPath.tree}/tenant/code-generator/queryParamFieldCodes`, data)

// 校验编码规则名称是否已存在
export const validateName = (data) =>
  API.post(`${urlPath.tree}/tenant/code-generator/validateName`, data)
