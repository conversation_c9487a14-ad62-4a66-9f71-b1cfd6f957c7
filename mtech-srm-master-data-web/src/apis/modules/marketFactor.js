/*
 * @Author: your name
 * @Date: 2021-08-26 17:43:31
 * @LastEditTime: 2021-10-22 10:38:55
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\apis\modules\goodsOrigin.js
 */
import { API } from '@mtech-common/http'

// 字典
export const NAME = 'marketFactor'

const urlPath = {
  tree: '/masterDataManagement'
}

//新增
export const add = (data) => API.post(`${urlPath.tree}/tenant/item-cost-factor/add`, data)
//新增校验
export const addValid = (data) => API.get(`${urlPath.tree}/tenant/item-cost-factor/add-valid`, data)
//删除
export const batchDelete = (data) =>
  API.post(`${urlPath.tree}/tenant/item-cost-factor/batch-delete`, data)
//失效
export const batchInvalid = (data) =>
  API.post(`${urlPath.tree}/tenant/item-cost-factor/batch-invalid`, data)
//激活
export const batchValid = (data) =>
  API.post(`${urlPath.tree}/tenant/item-cost-factor/batch-valid`, data)
//更新
export const update = (data) => API.put(`${urlPath.tree}/tenant/item-cost-factor/update`, data)
//更新-校验
export const updateValid = (data) =>
  API.get(`${urlPath.tree}/tenant/item-cost-factor/update-valid`, data)
//单位
export const fuzzyQuery = (data) =>
  API.post(
    `${urlPath.tree}/tenant/unit/fuzzy-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )
//导入
export const importData = (data) =>
  API.post(`${urlPath.tree}/tenant/item-cost-factor/import-data`, data)
//导出
export const exportData = (data) =>
  API.post(`${urlPath.tree}/tenant/item-cost-factor/export-data-post`, data, {
    responseType: 'blob'
  })
//导入
export const importDataVendor = (data) =>
  API.post(`${urlPath.tree}/tenant/supply/source/import-data`, data, {
    responseType: 'blob'
  })
//行内 -- 上传
export const uploadFile = (data) =>
  API.post(`${urlPath.tree}/tenant/item-cost-factor/upload-file?id=${data.get('id')}`, data, {
    responseType: 'blob'
  })

//行内 -- 下载
export const downloadFile = (data) =>
  API.get(`${urlPath.tree}/tenant/item-cost-factor/download-file`, data, {
    responseType: 'blob'
  })
// 行内 -- 适用范围-保存成本因子关联
export const saveRel = (data) => API.post(`${urlPath.tree}/tenant/costFactorRel/saveRel`, data)
// 行内 -- 适用范围-删除成本因子关联
export const deleteRel = (data) => API.post(`${urlPath.tree}/tenant/costFactorRel/deleteRel`, data)

// 新增属性大类
export const addAttri = (data) =>
  API.post(`${urlPath.tree}/tenant/item-cost-factor/attribute/add`, data)

// 更新属性大类
export const updateAttri = (data) =>
  API.put(`${urlPath.tree}/tenant/item-cost-factor/attribute/update`, data)

//新增校验
export const addAttrValid = (data) =>
  API.get(`${urlPath.tree}/tenant/item-cost-factor/attribute/add-valid`, data)

//更新校验
export const updateAttrValid = (data) =>
  API.get(`${urlPath.tree}/tenant/item-cost-factor/attribute/update-valid`, data)

// 成本因子-导入模板
export const downloadCostFactorItemTemplate = (data) =>
  API.post(`${urlPath.tree}/tenant/item-cost-factor/downloadCostFactorItemTemplate`, data, {
    responseType: 'blob'
  })
// 成本因子-导入
export const importCostFactor = (data) =>
  API.post(`${urlPath.tree}/tenant/item-cost-factor/importCostFactor`, data, {
    responseType: 'blob'
  })

// 属性大类-导入模板
export const downloadPropertyTemplate = (data) =>
  API.post(`${urlPath.tree}/tenant/item-cost-factor/downloadCostFactorPropertyTemplate`, data, {
    responseType: 'blob'
  })
// 属性大类-导入
export const importProperty = (data) =>
  API.post(`${urlPath.tree}/tenant/item-cost-factor/importProperty`, data, {
    responseType: 'blob'
  })
// 属性大类-导出
export const exportProperty = (data) =>
  API.post(`${urlPath.tree}/tenant/item-cost-factor/exportCostFactorProperty`, data, {
    responseType: 'blob'
  })
export const getAuthCompanyList = (data) =>
  API.post(`/masterDataManagement/tenant/costFactorRel/queryRel`, data)
