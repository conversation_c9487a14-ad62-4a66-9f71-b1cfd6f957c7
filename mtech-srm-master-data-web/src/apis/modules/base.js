/*
 * @Author: your name
 * @Date: 2021-08-26 16:51:19
 * @LastEditTime: 2022-03-20 17:00:08
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\apis\modules\base.js
 */
import { API } from '@mtech-common/http'

// 组织
export const NAME = 'base'

const urlPath = {
  role: '/iam',
  tree: '/masterDataManagement'
}

/**
 * @description: 企业列表 - 分页查询
 * @param {*}
 * @return {*}
 */
export const dataEnterpriseGet = (data) =>
  API.post(`${urlPath.tree}/admin/enterprise/paged-query`, data)

/**
 * @description: 企业列表 - 新增
 * @param {*}
 * @return {*}
 */
export const enterpriseAdd = (data) =>
  API.post(`${urlPath.tree}/admin/enterprise/add`, data, {
    useNotify: true
  })

/**
 * @description: 企业列表 - 新增验证
 * @param {*}
 * @return {*}
 */
export const enterpriseAddValid = (data) =>
  API.get(`${urlPath.tree}/admin/enterprise/add-valid`, data)

/**
 * @description: 企业列表 - 删除
 * @param {*}
 * @return {*}
 */
export const enterpriseDelete = (data) =>
  API.delete(`${urlPath.tree}/admin/enterprise/batch-delete`, data, {
    useNotify: true
  })

/**
 * @description: 企业列表 - 编辑
 * @param {*}
 * @return {*}
 */
export const enterpriseEdit = (data) =>
  API.put(`${urlPath.tree}/admin/enterprise/update`, data, {
    useNotify: true
  })

/**
 * @description: 企业列表 - 编辑验证
 * @param {*}
 * @return {*}
 */
export const enterpriseEditValid = (data) =>
  API.get(`${urlPath.tree}/admin/enterprise/update-valid`, data)

/**
 * @description: 公司列表 - 分页查询
 * @param {*}
 * @return {*}
 */
export const dataCompanyGet = (data) => API.post(`${urlPath.tree}/tenant/entity/paged-query`, data)

/**
 * @description: 公司列表 - 条件查询
 * @param {*}
 * @return {*}
 */
export const dataCompanyNoPageGet = (data) =>
  API.post(`${urlPath.tree}/tenant/entity/criteria-query`, data)

/**
 * @description: 公司 - 查询代加入租户的与企业列表
 * @param {*}
 * @return {*}
 */
export const waitAssignEnterpriseGet = (data) =>
  API.get(`${urlPath.tree}/admin/enterprise/query-pending-join-tenant`, data)

/**
 * @description: 公司列表 - 新增
 * @param {*}
 * @return {*}
 */
export const companyAdd = (data) =>
  API.post(`${urlPath.tree}/tenant/entity/add`, data, {
    useNotify: true
  })

/**
 * @description: 公司列表 - 新增验证
 * @param {*}
 * @return {*}
 */
export const companyAddValid = (data) => API.get(`${urlPath.tree}/tenant/entity/add-valid`, data)

/**
 * @description: 公司列表 - 删除
 * @param {*}
 * @return {*}
 */
export const companyDelete = (data) =>
  API.delete(`${urlPath.tree}/tenant/entity/batch-delete`, data, {
    useNotify: true
  })

/**
 * @description: 公司列表 - 编辑
 * @param {*}
 * @return {*}
 */
export const companyEdit = (data) =>
  API.put(`${urlPath.tree}/tenant/entity/update`, data, {
    useNotify: true
  })

/**
 * @description: 公司列表 - 编辑验证
 * @param {*}
 * @return {*}
 */
export const companyEditValid = (data) =>
  API.get(`${urlPath.tree}/tenant/entity/update-valid`, data)

/**
 * @description: 公司列表 - 修改状态
 * @param {*}
 * @return {*}
 */
export const companyStatusUpdate = (data) =>
  API.put(`${urlPath.tree}/tenant/supply-source-list/batch-update`, data, {
    useNotify: true
  })

/**
 * @description: 员工列表 - 分页查询
 * @param {*}
 * @return {*}
 */
export const dataStaffGet = (data) =>
  API.post(`${urlPath.tree}/tenant/organization/criteriaQueryOrganizationEmployee`, data)

/**
 * @description: 员工列表 - 删除
 * @param {*}
 * @return {*}
 */
export const staffDelete = (data) => API.delete(`${urlPath.tree}/tenant/entity/batch-delete`, data)

/**
 * @description: 根据名称获取账号
 * @param {*}
 * @return {*}
 */
export const accountGet = (data) =>
  API.post(`${urlPath.role}/tenant/account/getAccountUserNameByName`, data)

/**
 * @description: 公司数据 - 获取全部
 * @param {*}
 * @return {*}
 */
export const companyGet = (data) =>
  API.post(`${urlPath.tree}/tenant/organization/getChildrenCompanyOrganization`, data)

/**
 * @description: 部门数据 - 获取当前公司下
 * @param {*} organizationId
 * @return {*}
 */
export const departGet = (data) =>
  API.post(`${urlPath.tree}/tenant/organization/getChildrenDepartmentOrganization`, data)

/**
 * @description: 岗位数据 - 获取当前部门下
 * @param {*} organizationId
 * @return {*}
 */
export const stationGet = (data) =>
  API.post(`${urlPath.tree}/tenant/organization/getChildrenStationOrganization`, data)

export const staffModifyEmployeeStatus = (data) =>
  API.post(`${urlPath.tree}/tenant/employee/modifyEmployeeStatus`, data, {
    useNotify: true
  })

/**
 * @description: 员工 - 新增
 * @param {*}
 * @return {*}
 */
export const staffAddSave = (data) =>
  API.post(`${urlPath.tree}/tenant/employee/addEmployee`, data, {
    useNotify: true
  })

/**
 * @description: 员工 - 新增验证
 * @param {*}
 * @return {*}
 */
export const staffAddSaveValid = (data) =>
  API.get(`${urlPath.tree}/tenant/employee/addEmployee-valid`, data)

/**
 * @description: 员工 - 修改
 * @param {*}
 * @return {*}
 */
export const staffEditSave = (data) =>
  API.post(`${urlPath.tree}/tenant/employee/editEmployee`, data, {
    useNotify: true
  })

/**
 * @description: 员工 - 修改验证
 * @param {*}
 * @return {*}
 */
export const staffEditSaveValid = (data) =>
  API.get(`${urlPath.tree}/tenant/employee/editEmployee-valid`, data)

/**
 * @description: 用户 - 查询
 * @param {*}
 * @return {*}
 */
export const getUserData = (data) => API.post(`${urlPath.tree}/tenant/user/paged-query`, data)

/**
 * @description: 用户 - 修改状态
 * @param {*}
 * @return {*}
 */
export const updateUserStatus = (data) => API.put(`${urlPath.tree}/tenant/user/batch-update`, data)

/**
 * @description: 业务组 - 分页查询
 * @param {*}
 * @return {*}
 */
export const geGroupData = (data) =>
  API.post(
    `${urlPath.tree}/tenant/business-group/paged-query?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`,
    data
  )

/**
 * @description: 业务组 - 条件查询
 * @param {*}
 * @return {*}
 */
export const groupGetNoPage = (data) =>
  API.post(
    `${urlPath.tree}/tenant/business-group/criteria-query?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`,
    data
  )

/**
 * @description: 业务组 - 新增
 * @param {*}
 * @return {*}
 */
export const groupAdd = (data) =>
  API.post(`${urlPath.tree}/tenant/business-group/add`, data, {
    useNotify: true
  })

/**
 * @description: 业务组 - 新增验证
 * @param {*}
 * @return {*}
 */
export const groupAddValid = (data) =>
  API.get(`${urlPath.tree}/tenant/business-group/add-valid`, data)

/**
 * @description: 业务组 - 编辑
 * @param {*}
 * @return {*}
 */
export const groupEdit = (data) =>
  API.put(`${urlPath.tree}/tenant/business-group/update`, data, {
    useNotify: true
  })

/**
 * @description: 业务组 - 编辑验证
 * @param {*}
 * @return {*}
 */
export const groupEditValid = (data) =>
  API.get(`${urlPath.tree}/tenant/business-group/update-valid`, data)

/**
 * @description: 业务组 - 删除
 * @param {*}
 * @return {*}
 */
export const groupDelete = (data) =>
  API.delete(`${urlPath.tree}/tenant/business-group/batch-delete`, data, {
    useNotify: true
  })

/**
 * @description: 业务组成员 - 查询待选
 * @param {*}
 * @return {*}
 */
export const memberWaitAssignGet = (data) =>
  API.post(`${urlPath.tree}/tenant/business-group/toSelectEmployees`, data)

/**
 * @description: 业务组成员 - 查询现有
 * @param {*}
 * @return {*}
 */
export const memberAssignedGet = (data) =>
  API.post(`${urlPath.tree}/tenant/business-group/employeesInBusinessGroup`, data)

/*
 * @description: 业务组成员 - 查询现有
 */
export const employeesInBusinessGroupPage = `${urlPath.tree}/tenant/business-group/employeesInBusinessGroupPage`

/**
 * @description: 业务组成员 - 新增
 * @param {*}
 * @return {*}
 */
export const memberAssignedAdd = (data) =>
  API.post(`${urlPath.tree}/tenant/business-group/setEmployeesToBusinessGroup`, data, {
    useNotify: true
  })

/**
 * 业务组批量新增员工
 * @param {*} data
 * @returns
 */
export const addBusinessGroupAndEmployeeRel = (data) =>
  API.post(`${urlPath.tree}/tenant/business-group/addBusinessGroupAndEmployeeRel`, data)

/**
 * 业务组批量删除员工
 * @param {*} data
 * @returns
 */
export const delBusinessGroupAndEmployeeRel = (data) =>
  API.delete(`${urlPath.tree}/tenant/business-group/delBusinessGroupAndEmployeeRel`, data)

/**
 * @description: 业务组分配品类 - 查询待分配品类
 * @param {*}
 * @return {*}
 */
export const cateWaitTreeGet = (data) =>
  API.post(`${urlPath.tree}/tenant/business-group-category-rel/getCategoryTreeByOrganization`, data)

/**
 * @description: 业务组分配品类 - 保存
 * @param {*}
 * @return {*}
 */
export const cateAssignSave = (data) =>
  API.post(
    `${urlPath.tree}/tenant/business-group-category-rel/addOrgCategoryToBusinessGroup`,
    data,
    {
      useNotify: true
    }
  )

/**
 * @description: 业务组分配品类 - 删除
 * @param {*}
 * @return {*}
 */
export const cateDelete = (data) =>
  API.delete(`${urlPath.tree}/tenant/business-group-category-rel/batch-delete`, data, {
    useNotify: true
  })

/**
 * @description: 业务组分配品项 - 品项组查询
 * @param {*}
 * @return {*}
 */
export const matGroupGet = (data) =>
  API.post(`${urlPath.tree}/tenant/item-group/criteria-query`, data, {})

/**
 * @description: 业务组分配品项 - 保存
 * @param {*}
 * @return {*}
 */
export const matAssignSave = (data) =>
  API.post(
    `${urlPath.tree}/tenant/business-group-item-group-rel/addBusinessGroupItemGroups`,
    data,
    {
      useNotify: true
    }
  )

/**
 * @description: 业务组分配品项 - 删除
 * @param {*}
 * @return {*}
 */
export const matDelete = (data) =>
  API.delete(`${urlPath.tree}/tenant/business-group-item-group-rel/batch-delete`, data, {
    useNotify: true
  })

/**
 * @description: 业务组分配品项 - 查询待分配品项
 * @param {*}
 * @return {*}
 */
export const businessGroup = (data) =>
  API.post(
    `${urlPath.tree}/tenant/business-group-item-group-rel/getBusinessGroupOrgItemGroupInfo`,
    data
  )

/**
 * @description: 业务组分配品项 - 查询待分配品项
 */
export const getBusinessGroupOrgItemGroupInfoPage = `${urlPath.tree}/tenant/business-group-item-group-rel/getBusinessGroupOrgItemGroupInfoPage`

/**
 * @description: 业务组分配品类 - 查询待分配品类
 * @param {*}
 * @return {*}
 */
export const categoryInfo = (data) =>
  API.post(
    `${urlPath.tree}/tenant/business-group-category-rel/getBusinessGroupOrgCategoryInfo`,
    data
  )

/**
 * @description: 业务组分配品类 - 查询待分配品类
 */
export const getBusinessGroupOrgCategoryInfoPage = `${urlPath.tree}/tenant/business-group-category-rel/getBusinessGroupOrgCategoryInfoPage`
