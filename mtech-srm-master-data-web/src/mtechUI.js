/*
 * @Author: your name
 * @Date: 2021-08-12 16:27:35
 * @LastEditTime: 2022-06-09 10:02:20
 * @LastEditors: zhaoriyang3 <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\mtechUI.js
 */
import Vue from 'vue'
import './styles/index.scss'

import MtDialog from '@mtech-ui/dialog'
import MtButton from '@mtech-ui/button'
import MtForm from '@mtech-ui/form'
import MtRow from '@mtech-ui/row'
import MtCol from '@mtech-ui/col'
import MtFormItem from '@mtech-ui/form-item'
import MtInput from '@mtech-ui/input'
import MtSelect from '@mtech-ui/select'
import MtTabs from '@mtech-ui/tabs'
import MtTooltip from '@mtech-ui/tooltip'
import MtMultiSelect from '@mtech-ui/multi-select'
import MtInputNumber from '@mtech-ui/input-number'
import MtCheckbox from '@mtech-ui/checkbox'
import MtDatePicker from '@mtech-ui/date-picker'
import MtDateTimePicker from '@mtech-ui/date-time-picker'
import MtDateRangePicker from '@mtech-ui/date-range-picker'
import MtRadio from '@mtech-ui/radio'
import MtTreeView from '@mtech-ui/tree-view'
import MtDropDownTree from '@mtech-ui/drop-down-tree'
import MtUploader from '@mtech-ui/uploader'
import MtProgress from '@mtech-ui/progress'
import MtPage from '@mtech-ui/page'
import MtDataGrid from '@mtech-ui/data-grid'
import MtTreeGrid from '@mtech-ui/tree-grid'
import MtTag from '@mtech-ui/tag'
import MtSwitch from '@mtech-ui/switch'
import MtToast from '@mtech-ui/toast'
import MtIcon from '@mtech-ui/icon'

Vue.component('mt-col', MtCol)
Vue.component('mt-row', MtRow)
Vue.component('mt-dialog', MtDialog)
Vue.component('mt-button', MtButton)
Vue.component('mt-form', MtForm)
Vue.component('mt-form-item', MtFormItem)
Vue.component('mt-input', MtInput)
Vue.component('mt-select', MtSelect)
Vue.component('mt-tabs', MtTabs)
Vue.component('mt-tooltip', MtTooltip)
Vue.component('mt-multi-select', MtMultiSelect)
Vue.component('mt-date-picker', MtDatePicker)
Vue.component('mt-date-time-picker', MtDateTimePicker)
Vue.component('mt-date-range-picker', MtDateRangePicker)
Vue.component('mt-radio', MtRadio)
Vue.component('mt-multi-select', MtMultiSelect)
Vue.component('mt-inputNumber', MtInputNumber)
Vue.component('mt-input-number', MtInputNumber)
Vue.component('mt-checkbox', MtCheckbox)
Vue.component('mt-tree-view', MtTreeView)
Vue.component('mt-treeView', MtTreeView)
Vue.component('mt-toast', MtToast)
Vue.component('mt-icon', MtIcon)
Vue.component('mt-DropDownTree', MtDropDownTree)
Vue.component('mt-page', MtPage)
Vue.component('mt-data-grid', MtDataGrid)
Vue.component('mt-tree-grid', MtTreeGrid)
Vue.component('mt-tag', MtTag)
Vue.component('mt-switch', MtSwitch)
Vue.component('mt-icon', MtIcon)
Vue.component('mt-uploader', MtUploader)
Vue.component('mt-progress', MtProgress)

// mt-dialog-transfer组件
import MtDialogTransfer from '@mtech/dialog-transfer'
import '@mtech/dialog-transfer/build/esm/bundle.css'
Vue.component('mt-dialog-transfer', MtDialogTransfer)

// 引入权限
import commonPermission from '@mtech/common-permission'
Vue.use(commonPermission)

// mt-template-page组件
import MtTemplatePage from '@/components/template-page'
Vue.component('mt-template-page', MtTemplatePage)

// mt-common-tree组件
import MtCommonTree from '@mtech/common-tree-view'
import '@mtech/common-tree-view/build/esm/bundle.css'
Vue.component('mt-common-tree', MtCommonTree)

import MtMicroLoading from '@/components/micro-loading'
import '@mtech/common-loading/build/esm/bundle.css'
Vue.component('mt-loading', MtMicroLoading)

import loading from '@/components/loading'
Vue.use(loading)

import * as Dialog from '@/components/Dialog'
import * as Toast from '@/components/Toast'
// import * as ToolTip from '@/components/Tooltip'
Vue.prototype[Dialog['NAME']] = Dialog['COMPONENT']
Vue.prototype[Toast['NAME']] = Toast['COMPONENT']
// Vue.prototype[ToolTip['NAME']] = ToolTip['COMPONENT']
