/*
 * @Author: your name
 * @Date: 2021-08-12 16:23:05
 * @LastEditTime: 2022-03-15 15:49:44
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\router\modules\common.js
 */
const common = [
  {
    path: 'entity',
    name: 'Entity',
    component: () => import('@/views/base/Entity.vue')
  },
  {
    path: 'entity-info',
    name: 'EntityInfo',
    component: () => import('@/views/base/components/EntityInfo.vue')
  },
  {
    path: 'enterprise',
    name: 'Enterprise',
    component: () => import('@/views/base/Enterprise.vue')
  },
  {
    path: 'company',
    name: 'Company',
    component: () => import('@/views/base/Company.vue')
  },
  // {
  //   path: "station", // 岗位--暂时没发现在哪里使用
  //   name: "Station",
  //   component: () => import("@/views/base/Station.vue"),
  // },
  {
    path: 'staff',
    name: 'Staff',
    component: () => import('@/views/base/Staff.vue')
  },
  {
    path: 'org',
    name: 'Org',
    component: () => import('@/views/permission/Org.vue')
  },
  {
    path: 'group',
    name: 'Group',
    component: () => import('@/views/base/Group.vue')
  },
  {
    path: 'user',
    name: 'user',
    component: () => import('@/views/base/User.vue')
  },
  {
    path: 'site',
    name: 'site',
    component: () => import('@/views/permission/Site.vue')
  },
  {
    path: 'trade-partner',
    name: 'TradePartner',
    component: () => import('@/views/tradePartner/TradePartner.vue')
  },
  {
    path: 'work-center',
    name: 'WorkCenter',
    component: () => import('@/views/workCenter/WorkCenter.vue')
  },
  {
    path: 'cost-center',
    name: 'CostCenter',
    component: () => import('@/views/costCenter/CostCenter.vue')
  },
  {
    path: 'profit-center',
    name: 'ProfitCenter',
    component: () => import('@/views/profitCenter/ProfitCenter.vue')
  },
  {
    path: 'payment-terms',
    name: 'PaymentTerms',
    component: () => import('@/views/paymentTerms/PaymentTerms.vue')
  },

  {
    path: 'payment-method',
    name: 'paymentMethod',
    component: () => import('@/views/paymentMethod/index.vue'),
    meta: {
      title: '付款方式'
    },
    children: [
      {
        path: 'purchase',
        name: 'purchase',
        component: () => import('@/views/paymentMethod/purchase/index'),
        meta: {
          title: '付款方式'
        }
      }
    ]
  },
  {
    path: 'schema-group',
    name: 'SchemaGroup',
    component: () => import('@/views/schemaGroup/SchemaGroup.vue')
  },
  {
    path: 'custom',
    name: 'Custom',
    component: () => import('@/views/custom/Customer.vue')
  },
  {
    path: 'test',
    name: 'test',
    component: () => import('@/views/permission/components/AreaSelector.vue')
  }
]

export default common
