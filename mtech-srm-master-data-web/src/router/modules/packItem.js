/**
 * 组合物料
 */

const Router = [
  {
    path: 'pack-item',
    name: 'pack-item',
    component: () => import('@/views/packItem/layout.vue'),
    redirect: { name: 'pack-item-list' },
    children: [
      {
        path: 'list',
        name: 'pack-item-list',
        component: () => import('@/views/packItem/list/index.vue')
      },
      {
        path: 'edit',
        name: 'pack-item-edit',
        component: () => import('@/views/packItem/edit/index.vue')
      }
    ]
  }
]

export default Router
