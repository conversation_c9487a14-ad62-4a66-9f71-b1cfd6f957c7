/*
 * @Author: your name
 * @Date: 2021-08-12 16:27:35
 * @LastEditTime: 2021-11-30 16:44:45
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\router\modules\backgroundSetting.js
 */
const routers = [
  // srm权限管理
  {
    path: 'role-management',
    name: 'roleManagement',
    component: () => import('@/views/permission/Role.vue'),
    meta: {
      title: '角色管理'
    }
  },
  {
    path: 'data-permission',
    name: 'dataPermission',
    component: () => import('@/views/permission/DataPermission.vue'),
    meta: {
      title: '单据权限维度'
    }
  },
  {
    path: 'data-permission-role',
    name: 'dataPermission',
    component: () => import('@/views/permission/DataPermissionRole.vue'),
    meta: {
      title: '数据权限配置'
    }
  },
  {
    path: 'data-permission-setting',
    name: 'dataPermission',
    component: () => import('@/views/permission/DataPermissionSetting.vue'),
    meta: {
      title: '数据权限配置'
    }
  },
  {
    path: 'data-permission-total',
    name: 'data-permission-total',
    component: () => import('@/views/permission/PermissionTotal.vue'),
    meta: {
      title: '用户权限汇总'
    }
  },
  {
    path: 'tenant-data-scope',
    name: 'dataScope',
    component: () => import('@/views/permission/TenantDataScope.vue'),
    meta: {
      title: '数据权限设置'
    }
  },
  // 高级用角色管理
  {
    path: 'role-master-management',
    name: 'roleManagement',
    component: () => import('@/views/permission/RoleMaster.vue'),
    meta: {
      title: '高级角色管理'
    }
  },
  // 用户组管理
  {
    path: 'usergroup-management',
    name: 'roleManagement',
    component: () => import('@/views/permission/UserGroup.vue'),
    meta: {
      title: '用户组管理'
    }
  },
  // 高级用户组管理
  {
    path: 'usergroup-master-management',
    name: 'roleManagement',
    component: () => import('@/views/permission/UserGroupMaster.vue'),
    meta: {
      title: '高级用户组管理'
    }
  }

  // {
  //   path: "data-authority",
  //   name: "dataAuthority",
  //   component: () => import("@/views/dataAuthority/index.vue"),
  //   meta: {
  //     title: "角色管理",
  //   },
  // },
  // {
  //   path: "grid",
  //   name: "grid",
  //   component: () => import("@/views/dataAuthority/grid.vue"),
  //   meta: {
  //     title: "角色管理",
  //   },
  // },
]

export default routers
