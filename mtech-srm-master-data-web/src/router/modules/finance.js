/*
 * @Author: your name
 * @Date: 2022-02-16 14:09:05
 * @LastEditTime: 2022-02-17 10:01:57
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\router\modules\finance.js
 */
const category = [
  {
    path: 'subject-course',
    name: 'SubjectCourse',
    component: () => import('@/views/subjectCourse/SubjectCourse.vue')
  },
  {
    path: 'subject-course-detail',
    name: 'SubjectCourseDetail',
    component: () => import('@/views/subjectCourse/SubjectCourseDetail.vue')
  },
  {
    path: 'fiscal-year',
    name: 'FiscalYear',
    component: () => import('@/views/fiscalYear/FiscalYear.vue')
  }
]

export default category
