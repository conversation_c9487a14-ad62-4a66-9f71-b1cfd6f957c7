const routers = [
  {
    path: 'communication-master-data',
    name: 'communicationMasterData',
    component: () => import('@/views/communicationMasterData/index.vue'),
    meta: {
      title: '通讯主数据'
    },
    children: [
      {
        path: 'trade-partner',
        name: 'tradePartner',
        component: () => import('@/views/communicationMasterData/tradePartner/index.vue'),
        meta: {
          title: '贸易伙伴'
        }
      },
      {
        path: 'schema-group',
        name: 'schemaGroup',
        component: () => import('@/views/communicationMasterData/schemaGroup/index.vue'),
        meta: {
          title: '方案组'
        }
      },
      {
        path: 'payment-terms',
        name: 'paymentTerms',
        component: () => import('@/views/communicationMasterData/paymentTerms/index.vue'),
        meta: {
          title: '付款条件'
        }
      },
      {
        path: 'unit',
        name: 'unit',
        component: () => import('@/views/communicationMasterData/unit/index.vue'),
        meta: {
          title: '计量单位'
        }
      },
      {
        path: 'material-category-rel',
        name: 'materialCategoryRel',
        component: () => import('@/views/communicationMasterData/materialCategoryRel/index.vue'),
        meta: {
          title: '物料组与品类关联关系'
        }
      }
    ]
  }
]

export default routers
