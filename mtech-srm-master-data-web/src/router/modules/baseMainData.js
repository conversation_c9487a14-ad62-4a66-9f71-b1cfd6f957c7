/*
 * @Author: your name
 * @Date: 2021-08-12 16:27:35
 * @LastEditTime: 2021-08-12 17:13:24
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\router\modules\backgroundSetting.js
 */
const routers = [
  {
    path: 'base-management',
    name: 'baseManagement',
    component: () => import('@/views/baseMainData/index.vue'),
    meta: {
      title: '基础数据'
    },
    children: [
      {
        path: 'set-country',
        name: 'set-country',
        component: () => import('@/views/baseMainData/setCountry/index.vue'),
        meta: {
          title: '国家管理'
        }
      },
      {
        path: 'set-area',
        name: 'set-area',
        component: () => import('@/views/baseMainData/setArea/index.vue'),
        meta: {
          title: '地区管理'
        }
      },
      {
        path: 'set-region',
        name: 'set-region',
        component: () => import('@/views/baseMainData/setRegion/index.vue'),
        meta: {
          title: '区域管理'
        }
      },
      {
        path: 'set-currency',
        name: 'set-currency',
        component: () => import('@/views/baseMainData/setCurrency/index.vue'),
        meta: {
          title: '货币管理'
        }
      },
      {
        path: 'set-exchange-rate',
        name: 'set-exchange-rate',
        component: () => import('@/views/baseMainData/setExchangeRate/index.vue'),
        meta: {
          title: '汇率管理'
        }
      },
      {
        path: 'set-tax-category',
        name: 'set-tax-category',
        component: () => import('@/views/baseMainData/setTaxCategory/index.vue'),
        meta: {
          title: '税种管理'
        }
      },
      {
        path: 'set-tax-item',
        name: 'set-tax-item',
        component: () => import('@/views/baseMainData/setTaxItem/index.vue'),
        meta: {
          title: '税目管理'
        }
      },
      {
        path: 'set-language',
        name: 'set-language',
        component: () => import('@/views/baseMainData/setLanguage/index.vue'),
        meta: {
          title: '语言管理'
        }
      },
      {
        path: 'set-time-zone',
        name: 'set-time-zone',
        component: () => import('@/views/baseMainData/setTimeZone/index.vue'),
        meta: {
          title: '时区管理'
        }
      },
      {
        path: 'set-unit',
        name: 'set-unit',
        component: () => import('@/views/baseMainData/setUnit/index.vue'),
        meta: {
          title: '单位管理'
        }
      },
      {
        path: 'set-dictionary',
        name: 'set-dictionary',
        component: () => import('@/views/baseMainData/setDictionary/index.vue'),
        meta: {
          title: '字典管理'
        }
      },
      {
        path: 'set-dictionary-detail',
        name: 'set-dictionary-detail',
        component: () => import('@/views/baseMainData/setDictionaryDetail/index.vue'),
        meta: {
          title: '字典明细管理'
        }
      }
    ]
  }
]

export default routers
