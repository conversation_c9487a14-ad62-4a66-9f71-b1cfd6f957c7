/*
 * @Author: your name
 * @Date: 2021-09-29 09:59:42
 * @LastEditTime: 2021-12-13 10:46:05
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\router\modules\material.js
 */
const material = [
  {
    path: 'material-site',
    name: 'MaterialSite',
    component: () => import('@/views/material/MaterialSite.vue')
  },
  //品项地点jit标识
  {
    path: 'material-JIT',
    name: 'MaterialJIT',
    component: () => import('@/views/material/MaterialJIT.vue')
  },
  //销售直送配置
  {
    path: 'salessent-straight',
    name: 'salessentStraight',
    component: () => import('@/views/material/SalesSentStraight.vue')
  },
  {
    path: 'mat-site-add',
    name: 'MatSiteAdd',
    component: () => import('@/views/material/components/MatSiteAdd.vue')
  },
  {
    path: 'material-group',
    name: 'MaterialGroup',
    component: () => import('@/views/material/MaterialGroup.vue')
  }
]

export default material
