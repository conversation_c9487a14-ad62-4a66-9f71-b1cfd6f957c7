/*
 * @Author: your name
 * @Date: 2021-08-12 16:27:35
 * @LastEditTime: 2021-10-21 10:08:19
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\router\modules\backgroundSetting.js
 */
const routers = [
  {
    path: 'partners-management',
    name: 'PartnersManagement',
    component: () => import('@/views/partnersMainData/Index.vue'),
    meta: {
      title: '合作伙伴管理'
    },
    children: [
      {
        path: 'my-supplier',
        name: 'my-supplier',
        component: () => import('@/views/partnersMainData/mySupplier/Index.vue'),
        meta: {
          title: '我的供应商'
        }
      },
      {
        path: 'my-supplier-details',
        name: 'my-supplier-details',
        component: () => import('@/views/partnersMainData/mySupplier/components/index.vue'),
        meta: {
          title: '我的供应商详情'
        }
      },
      {
        path: 'supplier-category-relationships',
        name: 'supplier-category-relationships',
        component: () => import('@/views/partnersMainData/supplierCategory/Index.vue'),
        meta: {
          title: '供应商品类关系'
        }
      },
      {
        path: 'supply-material-management',
        name: 'supply-material-management',
        component: () => import('@/views/partnersMainData/supplyMaterialManagement/index.vue'),
        meta: {
          title: '供应商供货物料管理'
        }
      }
    ]
  }
]
export default routers
