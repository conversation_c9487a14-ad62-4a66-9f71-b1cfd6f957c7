/*
 * @Author: your name
 * @Date: 2021-08-12 16:27:35
 * @LastEditTime: 2021-08-19 15:49:00
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\router\index.js
 */

var modules = []

const routerJSFile = require.context('./modules', true, /\.js$/)
// console.log("apiJSFile", routerJSFile);

routerJSFile.keys().forEach((key) => {
  const mod = routerJSFile(key)
  let _mode = mod.default ? mod.default : mod
  modules = modules.concat(_mode)
})

const routes = [
  {
    path: '/masterdata',
    component: () => import('@/views/public.vue'),
    children: modules
  }
]

// console.log("routes", routes);

export default routes
