import { i18n } from '@/main.js'

export const statusOptions = [
  { text: i18n.t('草稿'), value: 0 },
  { text: i18n.t('激活'), value: 1 },
  { text: i18n.t('分发'), value: 2 },
  { text: i18n.t('失效'), value: 3 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'schemaGroupCode',
    title: i18n.t('方案组代码'),
    minWidth: 120
  },
  {
    field: 'schemaGroupName',
    title: i18n.t('方案组名称'),
    minWidth: 200
  },
  {
    field: 'statusId',
    title: i18n.t('状态'),
    minWidth: 200,
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === Number(cellValue))
      return item ? item.text : ''
    }
  },
  {
    field: 'businessOrganizationCode',
    title: i18n.t('业务组织代码'),
    minWidth: 120
  },
  {
    field: 'businessOrganizationName',
    title: i18n.t('业务组织名称'),
    minWidth: 200
  },
  {
    field: 'businessOrganizationDescription',
    title: i18n.t('业务组织描述'),
    minWidth: 200
  },
  {
    field: 'schemaGroupDescription',
    title: i18n.t('方案组描述'),
    minWidth: 200
  }
]
