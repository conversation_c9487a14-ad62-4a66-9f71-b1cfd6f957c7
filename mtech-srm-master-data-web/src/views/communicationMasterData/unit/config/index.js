import { i18n } from '@/main.js'

export const statusOptions = [
  { text: i18n.t('草稿'), value: 0 },
  { text: i18n.t('激活'), value: 1 },
  { text: i18n.t('分发'), value: 2 },
  { text: i18n.t('失效'), value: 3 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'unitCode',
    title: i18n.t('单位代码'),
    minWidth: 120
  },
  {
    field: 'unitName',
    title: i18n.t('单位名称'),
    minWidth: 120
  },
  {
    field: 'unitEnglishName',
    title: i18n.t('单位英文名称'),
    minWidth: 120
  },
  {
    field: 'typeCode',
    title: i18n.t('类型编码')
  },
  {
    field: 'typeName',
    title: i18n.t('类型名称')
  },
  {
    field: 'unitSymbol',
    title: i18n.t('符号')
  },
  {
    field: 'unitDescription',
    title: i18n.t('单位描述'),
    minWidth: 200
  },
  {
    field: 'statusId',
    title: i18n.t('状态'),
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === Number(cellValue))
      return item ? item.text : ''
    }
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人'),
    minWidth: 120
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人'),
    minWidth: 120
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160
  }
]
