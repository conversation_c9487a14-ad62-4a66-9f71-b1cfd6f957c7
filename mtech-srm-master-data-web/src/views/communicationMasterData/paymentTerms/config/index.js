import { i18n } from '@/main.js'

export const statusOptions = [
  { text: i18n.t('草稿'), value: 0 },
  { text: i18n.t('激活'), value: 1 },
  { text: i18n.t('分发'), value: 2 },
  { text: i18n.t('失效'), value: 3 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'paymentTermsCode',
    title: i18n.t('付款条款代码'),
    minWidth: 120
  },
  {
    field: 'paymentTermsName',
    title: i18n.t('付款条款名称'),
    minWidth: 200
  },
  {
    field: 'statusId',
    title: i18n.t('状态'),
    minWidth: 200,
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === Number(cellValue))
      return item ? item.text : ''
    }
  },
  {
    field: 'startTime',
    title: i18n.t('开始生效日期'),
    minWidth: 160
  },
  {
    field: 'endTime',
    title: i18n.t('有效截止日期'),
    minWidth: 160
  },
  {
    field: 'paymentTermsDescription',
    title: i18n.t('付款条款描述'),
    minWidth: 200
  }
]
