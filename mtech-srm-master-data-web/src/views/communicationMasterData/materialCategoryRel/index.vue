<!-- 物料组与品类关联关系 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item :label="$t('品类类型')" prop="categoryTypeCode">
          <mt-select
            v-model="searchFormModel.categoryTypeCode"
            :data-source="categoryTypeOptions"
            :fields="{ text: 'text', value: 'value' }"
            :allow-filtering="true"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('物料组')" prop="matkl">
          <mt-input
            v-model="searchFormModel.matkl"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('品类编码')" prop="categoryCode">
          <mt-input
            v-model="searchFormModel.categoryCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('品类名称')" prop="categoryName">
          <mt-input
            v-model="searchFormModel.categoryName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="scTableRef"
      grid-id="3430439c-b0dd-4020-a79a-e42bfb12d8bf"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      @edit-closed="editComplete"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #categoryTypeCodeEdit="{ row }">
        <vxe-select
          v-model="row.categoryTypeCode"
          :options="categoryTypeOptions"
          :option-props="{ label: 'label', value: 'value' }"
          :placeholder="$t('请选择')"
          transfer
          clearable
          @change="(e) => categoryTypeCodeChange(e, row)"
        />
      </template>
      <template #categoryTypeNameEdit="{ row }">
        <vxe-input
          v-model="row.categoryTypeName"
          :placeholder="$t('请选择品类类型')"
          transfer
          disabled
        />
      </template>
      <template #matklEdit="{ row }">
        <vxe-input v-model="row.matkl" :placeholder="$t('请输入')" transfer clearable />
      </template>
      <template #matklDescEdit="{ row }">
        <vxe-input v-model="row.matklDesc" :placeholder="$t('请输入')" transfer clearable />
      </template>
      <template #categoryCodeEdit="{ row }">
        <vxe-input v-model="row.categoryCode" :placeholder="$t('请输入')" transfer clearable />
      </template>
      <template #categoryNameEdit="{ row }">
        <vxe-input v-model="row.categoryName" :placeholder="$t('请输入')" transfer clearable />
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, statusOptions, categoryTypeOptions } from './config'
import { getHeadersFileName, download } from '@/utils/file.js'
import { cloneDeep } from 'lodash'
export default {
  components: {
    CollapseSearch,
    ScTable
  },
  data() {
    return {
      searchFormModel: {},
      toolbar: [
        { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
        { code: 'clearEdit', name: this.$t('取消编辑'), status: 'info', loading: false },
        { code: 'delete', name: this.$t('删除'), status: 'info', loading: false },
        { code: 'import', name: this.$t('导入'), status: 'info', loading: false },
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 500]
      },
      columns: columnData,
      loading: false,
      tableData: [],
      editRules: {
        categoryTypeCode: [{ required: true, message: this.$t('必填') }],
        categoryTypeName: [{ required: true, message: this.$t('必填') }],
        matkl: [{ required: true, message: this.$t('必填') }],
        matklDesc: [{ required: true, message: this.$t('必填') }],
        categoryCode: [{ required: true, message: this.$t('必填') }],
        categoryName: [{ required: true, message: this.$t('必填') }]
      },

      statusOptions,
      categoryTypeOptions,

      isAdding: false // 新增中
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    },
    editConfig() {
      return {
        enabled: true,
        trigger: 'click',
        mode: 'row',
        showStatus: true,
        beforeEditMethod: this.beforeEditMethod
      }
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    beforeEditMethod({ row }) {
      if ([1, '1'].includes(row.statusId)) {
        // this.$toast({ content: this.$t('激活状态的数据不可编辑'), type: 'warning' })
        return false
      }
      return true
    },
    categoryTypeCodeChange(e, row) {
      let selectedItem = this.categoryTypeOptions.find((v) => v.value === e.value)
      row.categoryTypeName = selectedItem?.text
    },
    dateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[field + 'End'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.isAdding = false
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.communicationMasterData
        .pageMaterialCategoryRelApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const ids = selectedRecords.map((v) => v.id)
      if (['delete'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'clearEdit':
          this.isAdding = false
          this.tableRef.clearEdit()
          this.tableRef.removeInsertRow()
          break
        case 'delete':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除？')
            },
            success: () => {
              this.handleDelete(ids)
            }
          })
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleAdd() {
      if (this.isAdding) {
        return
      }
      this.isAdding = true
      const item = {}
      this.tableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.tableRef.setEditRow(currentViewRecords[0])
      })
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        const buttonText = args.$event.target.innerText
        const skipEditButtons = [this.$t('取消编辑'), this.$t('刷新')]
        if (skipEditButtons.includes(buttonText)) {
          return
        }
        //1、 校验必填
        if (
          !row.categoryTypeCode ||
          !row.categoryTypeName ||
          !row.matkl ||
          !row.matklDesc ||
          !row.categoryCode ||
          !row.categoryName
        ) {
          this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
          this.tableRef.setEditRow(row)
          return
        }
        // 2、 保存
        this.handleSave(row)
      }
    },
    handleSave(row) {
      let params = cloneDeep(row)
      if (params.id?.includes('row_')) {
        params.id = null
      }
      this.$API.communicationMasterData
        .addMaterialCategoryRelApi(params)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
            this.handleSearch()
          }
        })
        .catch(() => {
          // 当出现错误时，指定行进入编辑状态
          this.tableRef.setEditRow(row)
        })
    },
    handleDelete(ids) {
      this.$API.communicationMasterData.deleteMaterialCategoryRelApi(ids).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.communicationMasterData.importMaterialCategoryRelApi,
          downloadTemplateApi: this.$API.communicationMasterData.downloadMaterialCategoryRelApi,
          paramsKey: 'importFile'
        },
        success: () => {
          this.handleSearch()
        }
      })
    },
    handleExport(e) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$API.communicationMasterData
        .exportMaterialCategoryRelApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>
