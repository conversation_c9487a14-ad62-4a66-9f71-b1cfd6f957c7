import { i18n } from '@/main.js'

export const statusOptions = [
  { text: i18n.t('草稿'), value: 0 },
  { text: i18n.t('激活'), value: 1 },
  { text: i18n.t('分发'), value: 2 },
  { text: i18n.t('失效'), value: 3 }
]

export const categoryTypeOptions = [
  { label: 'product' + '-' + i18n.t('生产品类'), text: i18n.t('生产品类'), value: 'product' },
  { label: 'common' + '-' + i18n.t('非生产品类'), text: i18n.t('非生产品类'), value: 'common' }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'categoryTypeCode',
    title: i18n.t('品类类型代码'),
    minWidth: 120,
    editRender: {},
    slots: {
      edit: 'categoryTypeCodeEdit'
    }
  },
  {
    field: 'categoryTypeName',
    title: i18n.t('品类类型名称'),
    minWidth: 120,
    editRender: {},
    slots: {
      edit: 'categoryTypeNameEdit'
    }
  },
  {
    field: 'matkl',
    title: i18n.t('物料组'),
    minWidth: 120,
    editRender: {},
    slots: {
      edit: 'matklEdit'
    }
  },
  {
    field: 'matklDesc',
    title: i18n.t('物料组描述'),
    minWidth: 120,
    editRender: {},
    slots: {
      edit: 'matklDescEdit'
    }
  },
  {
    field: 'categoryCode',
    title: i18n.t('品类编码'),
    minWidth: 120,
    editRender: {},
    slots: {
      edit: 'categoryCodeEdit'
    }
  },
  {
    field: 'categoryName',
    title: i18n.t('品类名称'),
    minWidth: 120,
    editRender: {},
    slots: {
      edit: 'categoryNameEdit'
    }
  },
  {
    field: 'statusId',
    title: i18n.t('状态'),
    minWidth: 200,
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === Number(cellValue))
      return item ? item.text : ''
    }
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人'),
    minWidth: 120
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人'),
    minWidth: 120
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160
  }
]
