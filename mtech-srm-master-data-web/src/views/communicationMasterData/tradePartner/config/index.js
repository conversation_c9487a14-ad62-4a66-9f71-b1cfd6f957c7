import { i18n } from '@/main.js'

export const statusOptions = [
  { text: i18n.t('草稿'), value: 0 },
  { text: i18n.t('激活'), value: 1 },
  { text: i18n.t('分发'), value: 2 },
  { text: i18n.t('失效'), value: 3 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'tradingPartnerCode',
    title: i18n.t('贸易伙伴代码'),
    minWidth: 120
  },
  {
    field: 'tradingPartnerName',
    title: i18n.t('贸易伙伴名称'),
    minWidth: 200
  },
  {
    field: 'statusId',
    title: i18n.t('状态'),
    minWidth: 200,
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === Number(cellValue))
      return item ? item.text : ''
    }
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人'),
    minWidth: 120
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人'),
    minWidth: 120
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160
  }
]
