<!--
 * @Author: your name
 * @Date: 2021-11-01 10:08:35
 * @LastEditTime: 2022-04-08 09:53:14
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\sku\components\SkuService.vue
-->
<template>
  <div>
    <expansion-item :title="$t('退货')">
      <div>
        <mt-radio
          v-for="radio in backRadioData"
          :key="radio.itemCode"
          v-model="serviceForm.returnPolicyCode"
          :val="radio.itemCode"
          inline
          ><temp-resolution
            v-model="serviceForm.returnPolicyValue"
            :data="radio.itemName"
          ></temp-resolution
        ></mt-radio>
      </div>
    </expansion-item>

    <expansion-item :title="$t('换货')" class="mt-mt-20">
      <div>
        <mt-radio
          v-for="radio in switchRadioData"
          :key="radio.itemCode"
          v-model="serviceForm.exchangePolicyCode"
          :val="radio.itemCode"
          inline
          ><temp-resolution
            v-model="serviceForm.exchangePolicyValue"
            :data="radio.itemName"
          ></temp-resolution
        ></mt-radio>
      </div>
    </expansion-item>

    <expansion-item :title="$t('质保')" class="mt-mt-20">
      <div class="flex justify-start items-center">
        <span>{{ $t('质保期限') }}:</span>
        <mt-input-number v-model="serviceForm.warrantyPolicyValue"></mt-input-number>
        <span>{{ $t('月') }}</span>
      </div>
    </expansion-item>

    <expansion-item :title="$t('特殊售后说明')" class="mt-mt-20">
      <div>
        <div>{{ $t('售后说明') }}</div>
        <mt-input
          v-model="serviceForm.specialDescription"
          :multiline="true"
          :rows="3"
          type="text"
          :placeholder="$t('请输入说明内容，不超过200字')"
        ></mt-input>
      </div>
    </expansion-item>
  </div>
</template>

<script>
import ExpansionItem from '@/components/common/ExpansionItem.vue'
import TempResolution from './TempResolution.vue'

export default {
  components: {
    ExpansionItem,
    TempResolution
  },
  props: {
    spuId: {
      type: String,
      default: '',
      required: true
    }
  },
  data() {
    return {
      serviceForm: {
        returnPolicyCode: '',
        exchangePolicyCode: ''
      },
      backRadioData: [],
      switchRadioData: [],
      flag: false
    }
  },
  mounted() {
    this.dictBackDataGet()
    this.dictSwitchDataGet()
    if (this.spuId) {
      this.dataGet()
    }
  },
  methods: {
    dataGet() {
      const query = { id: this.spuId }
      this.$API.sku.serviceDataGet(query).then((res) => {
        this.serviceForm = res.data
      })
    },
    save(tab) {
      this.switchRadioData.forEach((el) => {
        if (el.itemCode === this.serviceForm.exchangePolicyCode) {
          this.serviceForm.exchangePolicyText = el.itemName
        }
      })
      this.backRadioData.forEach((el) => {
        if (el.itemCode === this.serviceForm.returnPolicyCode) {
          this.serviceForm.returnPolicyText = el.itemName
        }
      })

      const data = { ...this.serviceForm, spuId: this.spuId }
      this.$API.sku
        .serviceSave(data)
        .then(() => {
          this.$emit('success', tab)
        })
        .catch(() => {})
    },
    dictBackDataGet() {
      const query = { dictCode: 'SPU_RETURN_POLICY' }
      this.$API.dict.TenantDictTypeGet(query).then((res) => {
        this.backRadioData = res.data
      })
    },
    dictSwitchDataGet() {
      const query = { dictCode: 'SPU_EXCHANGE_POLICY' }
      this.$API.dict.TenantDictTypeGet(query).then((res) => {
        this.switchRadioData = res.data
      })
    }
  }
}
</script>

<style></style>
