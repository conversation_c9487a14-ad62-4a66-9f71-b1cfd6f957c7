<!--
 * @Author: your name
 * @Date: 2021-10-27 14:31:58
 * @LastEditTime: 2022-01-26 11:02:44
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\sku\components\BaseInfo.vue
-->
<template>
  <div>
    <expansion-item :title="$t('新增方式')">
      <div>
        <mt-radio
          v-model="typeForm.addType"
          val="0"
          :label="$t('新增SPU')"
          inline
          @change="addTypeChange"
        ></mt-radio>
        <mt-radio
          v-model="typeForm.addType"
          val="1"
          :label="$t('引用物料新增')"
          inline
          @change="addTypeChange"
        ></mt-radio>
      </div>
    </expansion-item>

    <expansion-item :title="$t('SPU基础信息')" class="mt-mt-20">
      <div class="flex justify-between">
        <div class="form-item">
          <div class="form-item-label">SPU{{ $t('名称') }}</div>
          <mt-input
            v-model="baseForm.spuName"
            :readonly="false"
            :show-clear-button="false"
            type="text"
          ></mt-input>
        </div>
        <div class="form-item">
          <div class="form-item-label">{{ $t('品牌') }}</div>
          <mt-select
            v-model="baseForm.brand"
            :data-source="options.BRAND"
            :show-clear-button="false"
            :placeholder="$t('请选择品牌')"
          ></mt-select>
        </div>
        <div class="form-item">
          <div class="form-item-label">{{ $t('状态') }}</div>
          <mt-select
            v-model="baseForm.submit"
            :data-source="options.STATUS"
            :show-clear-button="false"
            :placeholder="$t('请选择状态')"
          ></mt-select>
        </div>
        <div class="form-item">
          <div class="form-item-label">{{ $t('所属品类') }}</div>
          <mt-DropDownTree
            id="multi-template"
            v-model="baseForm.categoryId"
            :fields="dropDownTree"
            :allow-filtering="true"
            :placeholder="$t('请选择所属品类')"
          ></mt-DropDownTree>
        </div>
        <div class="form-item">
          <div class="form-item-label">{{ $t('产地') }}</div>
          <mt-input
            v-model="baseForm.madeIn"
            :readonly="false"
            :show-clear-button="false"
            type="text"
          ></mt-input>
        </div>
      </div>
      <div class="mt-mt-20">
        <div class="form-item-label mt-mb-20">SPU{{ $t('详细描述') }}</div>
        <mt-rich-text-editor
          ref="MtRichTextEditor"
          :height="255"
          :toolbar-settings="toolbarSettings"
          v-model="baseForm.description"
        >
        </mt-rich-text-editor>
      </div>
    </expansion-item>

    <expansion-item :title="$t('特征属性')" class="mt-mt-20">
      <div>
        <div class="flex">
          <div class="mt-pa-10 custom-btn" @click="spAttrsAdd">
            <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('新增') }}
          </div>
          <div class="mt-pa-10 custom-btn" @click="spAttrsDel">
            <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('删除') }}
          </div>
        </div>
        <mt-data-grid
          v-if="columnsSpAttrs"
          id="dataGrid1"
          ref="dataGrid"
          :data-source="dataSpAttrs"
          :edit-settings="{
            allowEditing: true,
            allowAdding: true,
            allowDeleting: true,
            mode: 'Batch'
          }"
          :selection-settings="{ type: 'Multiple' }"
          :column-data="columnsSpAttrs"
        ></mt-data-grid>
      </div>
    </expansion-item>

    <expansion-item :title="$t('SKU信息')" class="mt-mt-20">
      <template #actions>
        <span class="mt-pa-10 mt-mr-10 custom-btn" @click="skuGenerate">
          <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('生成SKU') }}
        </span>
      </template>
      <div>
        <div class="flex">
          <div class="mt-pa-10 custom-btn" @click="skuDelete">
            <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('删除') }}
          </div>
        </div>
        <mt-data-grid
          v-if="columnsSku"
          ref="dataGridSku"
          :data-source="dataSku"
          :edit-settings="{
            allowEditing: true,
            allowAdding: true,
            allowDeleting: true,
            mode: 'Batch'
          }"
          :selection-settings="{ type: 'Multiple' }"
          :column-data="columnsSku"
        ></mt-data-grid>
      </div>
    </expansion-item>

    <mt-dialog
      ref="dialog"
      :header="$t('选择物料')"
      css-class="create-proj-dialog"
      :buttons="buttons"
    >
      <mt-template-page
        ref="tepPage"
        :hidden-tabs="true"
        :template-config="matChooseConfig"
      ></mt-template-page>
    </mt-dialog>
  </div>
</template>

<script>
import { utils } from '@mtech-common/utils'
import ExpansionItem from '@/components/common/ExpansionItem.vue'
import {
  OPTIONS,
  TOOLBAR_SETTINGS,
  MAT_CHOOSE_CONFIG,
  COLUMNS_SP_ATTRS,
  COLUMNS_SKU,
  DROPDOWN_TREE
} from '../config/spuDefend.config'

export default {
  components: {
    ExpansionItem
  },
  props: {
    spuId: {
      type: String,
      default: '',
      required: false
    }
  },
  data() {
    return {
      options: OPTIONS,
      dropDownTree: DROPDOWN_TREE,
      toolbarSettings: TOOLBAR_SETTINGS,
      matChooseConfig: MAT_CHOOSE_CONFIG,
      columnsSpAttrs: COLUMNS_SP_ATTRS(),
      columnsSku: COLUMNS_SKU([], this),
      dataSpAttrs: [],
      dataSku: [],

      typeForm: {
        addType: '0'
      },
      baseForm: {},
      itemForm: {},
      currentSkuIndex: 0, // 记录当前操作的SKU行
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  mounted() {
    this.attrTypeGet()
    this.cateTreeNodeDetailGet()
    if (this.spuId) {
      this.baseInfoGet()
    }
  },
  methods: {
    baseInfoGet() {
      const query = { id: this.spuId }
      this.$API.sku.baseInfoGet(query).then((res) => {
        const { classifications, skus, addType, itemId, itemCode, itemName, ...baseInfo } = res.data
        this.typeForm.addType = addType
        this.baseForm = baseInfo
        this.itemForm = { id: itemId, itemCode, itemName }
        this.dataSpAttrs = classifications
        this.dataSku = skus
      })
    },
    spAttrsAdd() {
      this.$refs.dataGrid.ejsRef.addRecord({
        id: Math.random(),
        classificationCode: '',
        classificationName: '',
        classificationTypeName: '',
        classificationValueNames: '',
        classificationDescription: ''
      })
    },
    gridDataUpdate(ref, dataKey) {
      const changes = this.$refs[ref].ejsRef.getBatchChanges()
      console.log('changes', changes)
      // changes事件中数据分为add change delete三种，其中delete和change都只针对原dataSource
      const { addedRecords, deletedRecords, changedRecords } = changes
      let data = utils.cloneDeep(this[dataKey])

      if (deletedRecords.length) {
        deletedRecords.forEach((el) => {
          data = data.filter((e) => e.id !== el.id)
        })
      }
      if (changedRecords.length) {
        changedRecords.forEach((el) => {
          data = data.map((e) => {
            if (el.id === e.id) {
              return el
            }
            return e
          })
        })
      }
      this[dataKey] = utils.cloneDeep(data.concat(addedRecords))
    },
    spAttrsDel() {
      this.$refs.dataGrid.ejsRef.deleteRecord()
    },
    skuGenerate() {
      this.gridDataUpdate('dataGrid', 'dataSpAttrs')
      const data = {
        itemId: this.itemForm.id,
        spuId: this.spuId,
        spuClassificationBatchAddRequests: this.dataSpAttrs
      }
      this.$API.sku.skuGenerate(data).then((res) => {
        this.dataSku = res.data
      })
    },
    skuDelete() {
      this.$refs.dataGridSku.ejsRef.deleteRecord()
      this.gridDataUpdate('dataGridSku', 'dataSku')
    },
    baseInfoSave(tab) {
      let itemInfo = {}
      this.gridDataUpdate('dataGrid', 'dataSpAttrs')
      this.gridDataUpdate('dataGridSku', 'dataSku')
      if (this.typeForm.addType === '1') {
        itemInfo = {
          itemId: this.itemForm.id,
          itemCode: this.itemForm.itemCode,
          itemName: this.itemForm.itemName
        }
      }
      const data = {
        ...this.typeForm,
        ...this.baseForm,
        ...itemInfo,
        classificationList: this.dataSpAttrs,
        skuList: this.dataSku
      }
      if (!this.spuId) {
        this.$API.sku.baseInfoSave(data).then((res) => {
          this.$emit('success', tab, res.data.id)
        })
      } else {
        this.$API.sku.baseInfoEdit(data).then(() => {
          this.$emit('success', tab, this.spuId)
        })
      }
    },
    addTypeChange(e) {
      if (String(e) === '1') {
        this.$refs.dialog.ejsRef.show()
      }
    },
    async save() {
      const currentTab = this.$refs.tepPage.getCurrentTabRef()
      const selectedRows = currentTab.grid.getSelectedRecords()

      if (selectedRows.length === 1) {
        this.itemForm = selectedRows[0]
      }
      if (String(this.typeForm.addType) === '0') {
        this.$refs.dataGridSku.ejsRef.updateCell(this.currentSkuIndex, 'itemId', this.itemForm.id)
        this.$refs.dataGridSku.ejsRef.updateCell(
          this.currentSkuIndex,
          'itemName',
          this.itemForm.itemName
        )
        this.$refs.dataGridSku.ejsRef.updateCell(
          this.currentSkuIndex,
          'itemCode',
          this.itemForm.itemCode
        )
      }
      if (String(this.typeForm.addType) === '1') {
        const { itemName } = this.itemForm
        this.baseForm.spuName = itemName
        await this.attrDataGetByMat()
        // this.$refs.dataGridSku.ejsRef.updateCell(
        //   this.currentSkuIndex,
        //   "itemId",
        //   this.itemForm.id
        // );
        // this.$refs.dataGridSku.ejsRef.updateCell(
        //   this.currentSkuIndex,
        //   "itemName",
        //   this.itemForm.itemName
        // );
        // this.$refs.dataGridSku.ejsRef.updateCell(
        //   this.currentSkuIndex,
        //   "itemCode",
        //   this.itemForm.itemCode
        // );
      }
      this.cancel()
    },
    async attrDataGetByMat() {
      const query = { id: this.itemForm.id }
      await this.$API.sku.attrDataGetByMat(query).then((res) => {
        this.dataSpAttrs = res.data
      })
    },
    cancel() {
      this.$refs.dialog.ejsRef.hide()
    },
    attrTypeGet() {
      const query = { dictCode: 'ITEM_CLASIFICATION_TYPE' }
      this.$API.dict.dictTypeGet(query).then((res) => {
        this.columnsSpAttrs = COLUMNS_SP_ATTRS(
          res.data || [
            {
              dictName: this.$t('选项1'),
              dictCode: 'opt1'
            }
          ]
        )
      })
    },
    cateTreeNodeDetailGet() {
      this.$API.sku.cateTreeNodeDetailGet().then((res) => {
        this.dropDownTree = Object.assign({}, this.dropDownTree, {
          dataSource: res.data.treeList || []
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.form-item {
  width: calc(20% - 20px);
  min-width: 120px;
}
.form-item-label {
  font-size: 14px;
  color: #2f353c;
}
.custom-btn {
  color: #4f5b6d;
  font-size: 14px;
  cursor: pointer;
}
</style>
