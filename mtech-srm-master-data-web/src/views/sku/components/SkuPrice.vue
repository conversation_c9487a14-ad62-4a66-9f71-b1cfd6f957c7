<!--
 * @Author: your name
 * @Date: 2021-11-01 09:28:10
 * @LastEditTime: 2021-11-11 14:26:49
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\sku\components\SkuPrice.vue
-->
<template>
  <div>
    <expansion-item :title="$t('SKU销售价格')">
      <div>
        <mt-data-grid
          v-if="columns"
          id="dataGridPrice"
          ref="dataGridPrice"
          :data-source="data"
          :edit-settings="{
            allowEditing: true,
            allowAdding: true,
            allowDeleting: true,
            mode: 'Batch'
          }"
          :selection-settings="{ type: 'Multiple' }"
          :column-data="columns"
        ></mt-data-grid>
      </div>
    </expansion-item>
  </div>
</template>

<script>
import { utils } from '@mtech-common/utils'
import ExpansionItem from '@/components/common/ExpansionItem.vue'
import { COLUMNS_PRICE } from '../config/spuDefend.config'

export default {
  components: {
    ExpansionItem
  },
  props: {
    spuId: {
      type: String,
      default: 'xxx',
      required: true
    }
  },
  data() {
    return {
      data: [],
      columns: COLUMNS_PRICE()
    }
  },
  mounted() {
    this.priceDataGet()
    this.currencyUnitGet()
  },
  methods: {
    priceDataGet() {
      const query = { id: this.spuId }
      this.$API.sku.priceDataGet(query).then((res) => {
        this.data = res.data
      })
    },
    gridDataUpdate(ref, dataKey) {
      const changes = this.$refs[ref].ejsRef.getBatchChanges()
      const { addedRecords, deletedRecords, changedRecords } = changes
      let data = utils.cloneDeep(this[dataKey])

      if (deletedRecords.length) {
        deletedRecords.forEach((el) => {
          data = data.filter((e) => e.id !== el.id)
        })
      }
      if (changedRecords.length) {
        changedRecords.forEach((el) => {
          data = data.map((e) => {
            if (el.id === e.id) {
              return el
            }
            return e
          })
        })
      }
      this[dataKey] = utils.cloneDeep(data.concat(addedRecords))
    },
    priceDataSave(tab) {
      this.gridDataUpdate('dataGridPrice', 'data')
      const data = {
        skuPriceUpdateRequestList: this.data,
        submit: false
      }
      this.$API.sku.priceDataSave(data).then(() => {
        this.$emit('success', tab)
      })
    },
    currencyUnitGet() {
      const query = { page: { current: 1, size: 500 } }
      this.$API.baseMainData.getCurrencyData(query).then((res) => {
        this.columns = COLUMNS_PRICE(res.data.records)
      })
    }
  }
}
</script>

<style></style>
