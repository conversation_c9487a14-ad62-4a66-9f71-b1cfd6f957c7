<!--
 * @Author: your name
 * @Date: 2021-10-29 14:49:56
 * @LastEditTime: 2021-10-29 16:40:10
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\sku\components\StepperTabs.vue
-->
<template>
  <div class="flex justify-between items-center mt-pa-20 stepper-tabs">
    <div class="flex justify-start stepper-tabs--step">
      <div
        v-for="(tab, index) in data"
        :key="index"
        :class="['tab', value === tab.value ? 'active' : '']"
        @click="emitClick(tab)"
      >
        {{ tab.label }}
      </div>
    </div>

    <div>
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    value: {
      type: String,
      required: true,
      default: ''
    },
    data: {
      type: Array,
      required: true,
      default() {
        return []
      }
    }
  },
  data() {
    return {}
  },
  watch: {
    value(val) {
      this.$emit('input', val)
    }
  },
  methods: {
    emitClick(tab) {
      this.$emit('click', tab)
    }
  }
}
</script>

<style lang="scss" scoped>
.stepper-tabs {
  width: 100%;
  height: 60px;
  background: rgba(255, 255, 255, 1);
  border-radius: 8px 8px 0 0;
  box-shadow: inset 0 -1px 0 0 rgba(232, 232, 232, 1);
  &--step {
    min-width: 560px;
    border: 1px solid rgba(232, 232, 232, 1);
    .tab {
      width: 146px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      border-right: 1px solid rgba(232, 232, 232, 1);
      cursor: pointer;
      &:last-child {
        border-right: none;
      }
    }
    .active {
      color: #00469c;
      background: rgba(245, 246, 249, 1);
      border-bottom: 2px solid #00469c;
    }
  }
}
</style>
