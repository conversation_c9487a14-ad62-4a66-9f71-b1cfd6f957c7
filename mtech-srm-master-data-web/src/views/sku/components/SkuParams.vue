<!--
 * @Author: your name
 * @Date: 2021-11-01 10:05:33
 * @LastEditTime: 2021-11-30 16:48:29
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\sku\components\SkuParams.vue
-->
<template>
  <div>
    <expansion-item :title="$t('新增方式')">
      <div>
        <mt-select
          :width="300"
          :data-source="dataSku"
          :fields="{ text: 'name', value: 'id' }"
          :show-clear-button="false"
          :placeholder="$t('请选择区域')"
          v-model="skuInfo.skuId"
          @input="paramsDataGet"
        ></mt-select>
      </div>
    </expansion-item>

    <expansion-item :title="$t('SKU规格参数')" class="mt-mt-20" dense>
      <template #actions>
        <mt-button css-class="e-flat" :is-primary="true" :disabled="false" @click="checkAll">{{
          $t('全选')
        }}</mt-button>
        <mt-button css-class="e-flat" :is-primary="true" :disabled="false" @click="paramsDel">{{
          $t('删除')
        }}</mt-button>
        <mt-button css-class="e-flat" :is-primary="true" :disabled="false" @click="applyToOther"
          >{{ $t('应用于其他') }}SKU</mt-button
        >
      </template>
      <div>
        <mt-checkbox-group v-model="paramsChecked" :disabled="false">
          <mt-row :gutter="20">
            <mt-col :sm="8" :md="8" :lg="8" :xl="8" v-for="item in paramsData" :key="item.id">
              <div class="params-item mt-pa-20">
                <div class="flex justify-between items-center params-item--header">
                  <!-- <span class="params-item__label">{{ $t("附件名称") }}</span> -->
                  <input
                    class="e-input"
                    type="text"
                    :readonly="false"
                    :placeholder="$t('请输入规格参数名称')"
                    v-model="item.specificationName"
                    @blur="paramsAddOrEditSave(item)"
                  />
                  <mt-checkbox :fields="{ id: 'id' }" :content="{ id: item.id }"></mt-checkbox>
                </div>
                <div>
                  <div class="e-input-group">
                    <input
                      class="e-input"
                      type="text"
                      :placeholder="$t('请输入规格参数')"
                      v-model="item.specificationValueName"
                      @blur="paramsAddOrEditSave(item)"
                    />
                  </div>
                </div>
              </div>
            </mt-col>

            <mt-col :sm="8" :md="8" :lg="8" :xl="8">
              <div
                class="params-action flex justify-center items-center cursor-pointer"
                @click="paramsAdd"
              >
                <div class="text-center">
                  <mt-icon
                    name="icon_solid_add"
                    class="cursor-pointer params-action__icon"
                  ></mt-icon>
                  <div class="params-action__label mt-mt-10">
                    {{ $t('新增附件') }}
                  </div>
                </div>
              </div>
            </mt-col>
          </mt-row>
        </mt-checkbox-group>
      </div>
    </expansion-item>
  </div>
</template>

<script>
import ExpansionItem from '@/components/common/ExpansionItem.vue'
export default {
  components: {
    ExpansionItem
  },
  props: {
    spuId: {
      type: String,
      default: '',
      required: true
    }
  },
  data() {
    return {
      dataSku: [],
      skuInfo: {},

      paramsData: [],
      paramsChecked: []
    }
  },
  mounted() {
    this.skuQueryBySpu()
  },
  methods: {
    skuQueryBySpu() {
      const query = { id: this.spuId }
      this.$API.sku.skuQueryBySpu(query).then((res) => {
        this.dataSku = res.data
        this.skuInfo.skuId = res.data[0]?.id
      })
    },
    paramsDataGet() {
      const query = { id: this.skuInfo.skuId }
      this.$API.sku.paramsDataGet(query).then((res) => {
        this.paramsData = res.data
        this.paramsChecked = []
      })
    },
    checkAll() {
      this.paramsChecked = this.paramsData.map((el) => el.id)
    },
    paramsAdd() {
      const lastItem = this.paramsData[this.paramsData.length - 1]
      if (lastItem && (!lastItem.specificationName || !lastItem.specificationValueName)) {
        return this.$toast({
          content: this.$t('请完整填写附件名称和数量'),
          type: 'warning'
        })
      }
      this.paramsData.push({
        id: `UID${Math.random()}`,
        specificationName: '',
        specificationValueName: ''
      })
    },
    async paramsDel() {
      const localDelIds = this.paramsChecked.filter((el) => el.startsWith('UID'))
      const originDelIds = this.paramsChecked.filter((el) => !el.startsWith('UID'))
      const data = { ids: originDelIds }
      this.paramsData = this.paramsData.filter((e) => !localDelIds.includes(e.id))
      originDelIds.length &&
        (await this.$API.sku.paramsDataDel(data).then(() => {
          this.paramsDataGet()
        }))
      this.paramsChecked = []
    },
    paramsAddOrEditSave(params) {
      if (
        params.specificationName &&
        params.specificationValueName &&
        params.id.startsWith('UID')
      ) {
        // eslint-disable-next-line no-unused-vars
        const { id, ...fileField } = params
        const data = { ...fileField, skuId: this.skuInfo.skuId }
        this.$API.sku.paramsDataAddSave(data).then((res) => {
          params.id = res.data.id
        })
      }
      if (
        params.specificationName &&
        params.specificationValueName &&
        !params.id.startsWith('UID')
      ) {
        const data = { ...params }
        this.$API.sku.paramsDataEditSave(data).then(() => {})
      }
    },
    applyToOther() {
      const data = {
        currentSkuId: this.skuInfo.skuId,
        specificationIds: this.paramsChecked
      }
      this.$API.sku.paramsDataEditSave(data).then(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.params-item {
  height: 104px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(155, 170, 193, 0.4);
  border-radius: 8px;
  &--header {
    /deep/ input.e-input {
      border-bottom: 0 !important;
    }
  }
  &__label {
    height: 31px;
    line-height: 31px;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
  }
}
.params-action {
  height: 104px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(155, 170, 193, 0.4);
  border-radius: 8px;
  &__icon {
    color: #98aac3;
  }
  &__label {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(152, 170, 195, 1);
  }
}
</style>
