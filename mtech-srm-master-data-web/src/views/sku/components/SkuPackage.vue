<!--
 * @Author: your name
 * @Date: 2021-11-01 09:56:14
 * @LastEditTime: 2021-11-30 17:22:02
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\sku\components\SkuPackage.vue
-->
<template>
  <div>
    <expansion-item :title="$t('SKU包装清单')" dense>
      <template #actions>
        <div class="flex justify-between items-center mt-mr-10">
          <div class="e-input-group">
            <mt-icon
              name="icon_search"
              class="mt-mr-10 search-icon cursor-pointer"
              @click.native="dataGet"
            ></mt-icon>
            <input
              v-model="searchForm.fuzzyName"
              class="e-input"
              type="text"
              :placeholder="$t('搜索')"
            />
          </div>
          <mt-button css-class="e-flat" :is-primary="true" @click="batchAdd">{{
            $t('批量新增')
          }}</mt-button>
          <mt-tool-tip
            :content="$t('批量新增后的内容将应用于所有属性，如有差异支持在属性内单独修改')"
            target="#box"
            position="TopCenter"
            class="tooltip"
          >
            <mt-icon id="box" name="icon_solid_Information" class="tooltip-icon"></mt-icon>
          </mt-tool-tip>
        </div>
      </template>
      <div>
        <div v-for="sku in data" :key="sku.skuId" class="attr-file attr-file--border mt-mb-20">
          <div class="flex justify-between items-center">
            <div v-if="sku.classificationFullname">
              <span class="attr-file--title__label"
                >{{ attrSplit(sku.classificationFullname) }}：</span
              >
              <span
                v-for="valStr in attrValSplit(sku.classificationFullname)"
                :key="valStr"
                class="attr-file--title__value"
                >{{ valStr }}</span
              >
            </div>
            <div>
              <mt-button css-class="e-flat" :is-primary="true" @click="fileAllCheck(sku)">{{
                $t('全选')
              }}</mt-button>
              <mt-button css-class="e-flat" :is-primary="true" @click="fileDel(sku)">{{
                $t('删除')
              }}</mt-button>
            </div>
          </div>
          <mt-checkbox-group v-model="sku.fileAddChecked" :disabled="false">
            <div class="attr-file--list flex justify-start mt-mt-20">
              <div v-for="file in sku.packageList" :key="file.id" class="attr-file--item mt-pa-20">
                <div class="flex justify-between items-center">
                  <span class="attr-file--item__label">{{ $t('附件名称') }}</span>
                  <mt-checkbox :fields="{ id: 'id' }" :content="{ id: file.id }"></mt-checkbox>
                </div>
                <div class="mt-mt-10">
                  <div class="e-input-group">
                    <input
                      class="e-input"
                      v-model="file.accessoryName"
                      type="text"
                      :placeholder="$t('请输入附件名称')"
                      @input="flag = true"
                      @blur="fileAddOrEditSave(file, sku.skuId)"
                    />
                  </div>
                </div>
                <div class="flex justify-between items-center mt-mt-20">
                  <span class="attr-file--item__label">{{ $t('附件数量') }}</span>
                </div>
                <div class="mt-mt-10">
                  <div class="e-input-group">
                    <input
                      class="e-input"
                      v-model="file.accessoryQuantity"
                      type="text"
                      :placeholder="$t('请输入附件数量')"
                      @input="flag = true"
                      @blur="fileAddOrEditSave(file, sku.skuId)"
                    />
                  </div>
                </div>
              </div>

              <div class="attr-file--action flex justify-center items-center" @click="fileAdd(sku)">
                <div class="text-center">
                  <mt-icon
                    name="icon_solid_add"
                    class="cursor-pointer attr-file--action__icon"
                  ></mt-icon>
                  <div class="attr-file--action__label mt-mt-10">
                    {{ $t('新增附件') }}
                  </div>
                </div>
              </div>
            </div>
          </mt-checkbox-group>
        </div>
      </div>
    </expansion-item>

    <mt-dialog ref="dialog" :header="$t('批量新增')" :buttons="buttons">
      <div class="attr-file mt-mb-10">
        <div class="flex justify-end items-center">
          <mt-button css-class="e-flat" :is-primary="true" @click="fileItemDel">{{
            $t('删除')
          }}</mt-button>
        </div>
        <mt-checkbox-group v-model="batchAddChecked" :disabled="false">
          <div class="attr-file--list flex justify-start mt-mt-20">
            <div v-for="item in batchAddData" :key="item.id" class="attr-file--item mt-pa-20">
              <div class="flex justify-between items-center">
                <span class="attr-file--item__label">{{ $t('附件名称') }}</span>
                <mt-checkbox :fields="{ id: 'id' }" :content="{ id: item.id }"></mt-checkbox>
              </div>
              <div class="mt-mt-10">
                <mt-input
                  v-model="item.accessoryName"
                  type="text"
                  :placeholder="$t('请输入附件名称')"
                ></mt-input>
              </div>
              <div class="flex justify-between items-center mt-mt-20">
                <span class="attr-file--item__label">{{ $t('附件数量') }}</span>
              </div>
              <div class="mt-mt-10">
                <mt-input
                  v-model="item.accessoryQuantity"
                  type="text"
                  :placeholder="$t('请输入附件数量')"
                ></mt-input>
              </div>
            </div>

            <div
              class="attr-file--action flex justify-center items-center cursor-pointer"
              @click="fileItemAdd"
            >
              <div class="text-center">
                <mt-icon
                  name="icon_solid_add"
                  class="cursor-pointer attr-file--action__icon"
                ></mt-icon>
                <div class="attr-file--action__label mt-mt-10">
                  {{ $t('新增附件') }}
                </div>
              </div>
            </div>
          </div>
        </mt-checkbox-group>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import { utils } from '@mtech-common/utils'
import ExpansionItem from '@/components/common/ExpansionItem.vue'

export default {
  components: {
    ExpansionItem
  },
  props: {
    spuId: {
      type: String,
      default: '',
      required: true
    }
  },
  data() {
    return {
      data: [
        {
          packageList: [
            {
              id: `UID${Math.random()}`,
              accessoryName: '',
              accessoryQuantity: ''
            }
          ]
        }
      ],
      searchForm: {
        accessoryQuantity: 2
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      batchAddChecked: [],
      batchAddData: [],
      flag: false
    }
  },
  mounted() {
    this.packageDataGet()
    this.fileAddOrEditSave = utils.debounce(this.fileAddOrEditSave)
  },
  methods: {
    async packageDataGet() {
      const query = { id: this.spuId }
      await this.$API.sku.packageDataGet(query).then((res) => {
        this.data = res.data.map((el) => {
          el.fileAddChecked = []
          return el
        })
      })
    },
    fileAdd(sku) {
      const lastItem = sku.packageList[sku.packageList.length - 1]
      if (lastItem && (!lastItem.accessoryName || !lastItem.accessoryQuantity)) {
        return this.$toast({
          content: this.$t('请完整填写附件名称和数量'),
          type: 'warning'
        })
      }
      sku.packageList.push({
        id: `UID${Math.random()}`,
        accessoryName: '',
        accessoryQuantity: ''
      })
    },
    fileAddOrEditSave(file, skuId) {
      if (this.flag) {
        if (file.accessoryName && file.accessoryQuantity && file.id.startsWith('UID')) {
          // eslint-disable-next-line no-unused-vars
          const { id, ...fileField } = file
          const data = { ...fileField, skuId }
          this.$API.sku.packageDataAddSave(data).then(() => {
            this.flag = false
          })
        }
        if (file.accessoryName && file.accessoryQuantity && !file.id.startsWith('UID')) {
          const data = [file]
          this.$API.sku.packageDataEditSave(data).then(() => {
            this.flag = false
          })
        }
      }
    },
    async fileDel(sku) {
      const localDelIds = sku.fileAddChecked.filter((el) => el.startsWith('UID'))
      const originDelIds = sku.fileAddChecked.filter((el) => !el.startsWith('UID'))
      const data = { ids: originDelIds }
      sku.packageList = sku.packageList.filter((e) => !localDelIds.includes(e.id))
      originDelIds.length &&
        (await this.$API.sku.packageDataDel(data).then(() => {
          this.packageDataGet()
        }))
      sku.fileAddChecked = []
    },
    fileAllCheck(sku) {
      sku.fileAddChecked = sku.packageList.map((el) => el.id)
    },
    batchAdd() {
      this.batchAddData = []
      this.batchAddChecked = []
      this.fileItemAdd()
      this.$refs.dialog.ejsRef.show()
    },
    fileItemAdd() {
      const lastItem = this.batchAddData[this.batchAddData.length - 1]
      if (lastItem && (!lastItem.accessoryName || !lastItem.accessoryQuantity)) {
        return this.$toast({
          content: this.$t('请完整填写附件名称和数量'),
          type: 'warning'
        })
      }
      this.batchAddData.push({
        id: `UID${Math.random()}`,
        accessoryName: '',
        accessoryQuantity: null
      })
    },
    fileItemDel() {
      this.batchAddData = this.batchAddData.filter((e) => !this.batchAddChecked.includes(e.id))
      this.batchAddChecked = []
    },
    cancel() {
      this.$refs.dialog.ejsRef.hide()
    },
    save() {
      this.batchAddData.forEach((el) => {
        delete el.id
        el.spuId = this.spuId
      })
      this.$API.sku.packageDataBatchAddSave(this.batchAddData).then(() => {
        this.packageDataGet()
      })
    },
    attrSplit(str) {
      return str?.split(':')[0]
    },
    attrValSplit(str) {
      let valStr = str?.split(':')[1]
      return valStr.split(';')
    }
  }
}
</script>

<style lang="scss" scoped>
.tooltip {
  width: 20px;
  text-align: center;
  &-icon {
    color: #a0aec4;
  }
}
.attr-file {
  padding: 30px;
  &--title {
    &__label {
      font-size: 16px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(41, 41, 41, 1);
    }
    &__value {
      width: 44px;
      height: 20px;
      padding: 2px 5px;
      background: #eff2f8;
      border-radius: 2px;
      color: #6386c1;
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
    }
  }
  &--list {
    overflow-x: scroll;
  }
  &--item {
    min-width: 280px;
    height: 186px;
    background: rgba(250, 250, 250, 1);
    border-radius: 8px;
    margin: 0 20px 0 0;
    &__label {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(41, 41, 41, 1);
    }
  }
  &--action {
    min-width: 280px;
    height: 186px;
    background: rgba(250, 250, 250, 1);
    border-radius: 8px;
    &__icon {
      color: #98aac3;
    }
    &__label {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(152, 170, 195, 1);
    }
  }
}
.attr-file--border {
  border: 1px solid rgba(155, 170, 193, 0.4);
  border-radius: 8px;
}
.e-input-group {
  width: 250px;
  /deep/.mt-icons {
    font-family: element-icons, e-icons;
    font-style: normal;
    font-variant: normal;
    font-weight: 400;
    height: 18px;
    line-height: 30px;
    text-transform: none;
  }
  .search-icon {
    color: #98aac3;
  }
}
</style>
