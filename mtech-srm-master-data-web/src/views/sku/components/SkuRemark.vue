<!--
 * @Author: your name
 * @Date: 2021-10-29 14:19:51
 * @LastEditTime: 2021-11-30 15:35:43
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\sku\components\SkuRemark.vue
-->
<template>
  <div>
    <expansion-item :title="$t('SPU图片')">
      <div class="">
        <mt-uploader
          v-if="!dataSpu.spuImgId"
          :async-settings="asyncSettings"
          name="UploadFiles"
          :uploading="uploading"
          @success="success"
        >
          <div class="uploader-spu mt-py-20">
            <div class="uploader-spu--icon">
              <mt-icon name="icon_Department" />
            </div>
            <div class="uploader-spu--remark">
              {{ $t('请拖拽图片或点击上传') }}
            </div>
            <div class="uploader-spu--tips">{{ $t('注：图片格式支持（.jpg .png）') }}</div>
          </div>
        </mt-uploader>
        <image-preview v-else @del="delSpuImage"></image-preview>
      </div>
    </expansion-item>

    <expansion-item :title="$t('SKU图片')">
      <div></div>
      <mt-data-grid
        ref="dataGridSku"
        :data-source="dataSku"
        :column-data="columnsSku"
      ></mt-data-grid>
    </expansion-item>
  </div>
</template>

<script>
import Vue from 'vue'
import ExpansionItem from '@/components/common/ExpansionItem.vue'
import ImagePreview from '@/components/common/ImagePreview.vue'
import { HEADER_TOKEN_KEY, getToken } from '@mtech-sso/single-sign-on'

export default {
  components: {
    ExpansionItem,
    ImagePreview
  },
  props: {
    spuId: {
      type: String,
      default: '',
      required: true
    }
  },
  data() {
    return {
      asyncSettings: {
        saveUrl: '/api/file/user/file/uploadPrivate?useType=2'
      },

      dataSku: [],
      dataSpu: {},
      columnsSku: (function (that) {
        return [
          {
            width: '250',
            headerText: this.$t('属性'),
            field: 'classificationFullname'
          },
          {
            width: 'auto',
            headerText: this.$t('图片'),
            field: 'imgList',
            template: function () {
              return {
                template: Vue.component('sku-uploader', {
                  components: {
                    ImagePreview
                  },
                  template: `
                  <div class="row-uploader flex justify-start mt-pa-10">
                    <mt-uploader
                      :async-settings="asyncSettings"
                      name="UploadFiles"
                      :uploading="uploading"
                      @success="success"
                    >
                      <div class="row-uploader--icon">
                        <mt-icon name="icon_Department" />
                      </div>
                    </mt-uploader>
                    <image-preview v-for="img in data.imgList" :key="img.id" class="row-uploader--img mt-ml-10" @del="delSpuImage"></image-preview>
                  </div>
                `,
                  data() {
                    return {
                      data: {},
                      asyncSettings: {
                        saveUrl: '/api/file/user/file/uploadPrivate?useType=2'
                      }
                    }
                  },
                  methods: {
                    uploading(args) {
                      args.currentRequest.setRequestHeader(HEADER_TOKEN_KEY, getToken())
                    },
                    success({ e }) {
                      const response = JSON.parse(e?.currentTarget?.response)
                      const { fileName, id: fileId } = response.data
                      const data = { fileName, fileId, skuId: this.data.id }
                      that.$API.sku.skuImageSave(data).then(() => {
                        that.skuImageListGet()
                      })
                    }
                  }
                })
              }
            }
          }
        ]
      })(this)
    }
  },
  mounted() {
    this.skuImageListGet()
  },
  methods: {
    skuImageListGet() {
      const query = { id: this.spuId }
      this.$API.sku.skuImageListGet(query).then((res) => {
        const { skuImageList, ...spuImage } = res.data
        this.dataSpu = spuImage
        this.dataSku = skuImageList
      })
    },
    delSpuImage() {},
    uploading(args) {
      args.currentRequest.setRequestHeader(HEADER_TOKEN_KEY, getToken())
    },
    success({ e }) {
      const response = JSON.parse(e?.currentTarget?.response)
      const { fileName, id: fileId } = response.data
      const data = { fileName, fileId, spuId: this.spuId }
      this.$API.sku.spuImageSave(data).then(() => {
        this.skuImageListGet()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.uploader-spu {
  width: 248px;
  height: 116px;
  background: rgba(251, 252, 253, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  text-align: center;
  &--icon {
    color: rgba(152, 170, 195, 1);
  }
  &--remark {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(152, 170, 195, 1);
    margin-top: 14px;
  }
  &--tips {
    font-size: 12px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(241, 62, 62, 1);
    margin-top: 14px;
  }
}
::v-deep .row-uploader {
  background: rgba(245, 245, 245, 1);
  box-shadow: inset 0 -1px 0 0 rgba(232, 232, 232, 1), inset 1px 0 0 0 rgba(232, 232, 232, 1),
    inset -1px 0 0 0 rgba(232, 232, 232, 1);
  &--icon {
    width: 60px;
    height: 60px;
    line-height: 60px;
    text-align: center;
    background: rgba(251, 251, 251, 1);
    color: rgba(152, 170, 195, 1);
  }
  &--img {
    width: 60px;
    height: 60px;
    background: rgba(251, 251, 251, 1);
  }
}
</style>
