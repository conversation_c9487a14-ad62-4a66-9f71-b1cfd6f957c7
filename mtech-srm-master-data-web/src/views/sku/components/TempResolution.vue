<!--
 * @Author: your name
 * @Date: 2021-11-05 09:40:24
 * @LastEditTime: 2021-11-11 13:53:43
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\sku\components\TempResolution.vue
-->
<template>
  <div class="flex justify-start items-center">
    <span>{{ strMap[0] }}</span>
    <mt-input
      v-if="htmlMark && htmlMark === 'NUM'"
      type="number"
      :placeholder="$t('请输入')"
      :value="value"
      @input="emitInput"
      :width="120"
    />
    <span>{{ strMap[1] }}</span>
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: String,
      required: true,
      default: ''
    },
    value: {
      type: [String, Number],
      required: true,
      default: ''
    }
  },
  data() {
    return {
      htmlMark: '',
      strMap: []
    }
  },
  watch: {
    data() {
      this.htmlResolution()
    }
  },
  mounted() {
    this.htmlResolution()
  },
  methods: {
    htmlResolution() {
      const data = this.data
      const startIndex = data.indexOf('{') + 1
      const endIndex = data.indexOf('}')
      this.htmlMark = data.substring(startIndex, endIndex)
      this.strMap = data.split(/\$\{[A-Z]*\}/)
    },
    emitInput(e) {
      this.$emit('input', e)
      this.$emit('change', e)
    }
  }
}
</script>

<style lang="scss" scoped></style>
