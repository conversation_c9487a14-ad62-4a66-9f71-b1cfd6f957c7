<!--
 * @Author: your name
 * @Date: 2021-10-26 17:15:57
 * @LastEditTime: 2021-12-29 13:49:56
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\sku\SpuAdd.vue
-->
<template>
  <div class="mt-pt-20">
    <stepper-tabs v-model="currentTab" :data="tabData" @click="tabSwitch">
      <mt-button css-class="e-flat" :is-primary="true">{{ $t('保存草稿') }}</mt-button>
      <mt-button css-class="e-flat" :is-primary="true">{{ $t('提交') }}</mt-button>
      <mt-button css-class="e-flat" :is-primary="true">{{ $t('取消') }}</mt-button>
    </stepper-tabs>
    <div class="mt-mt-20">
      <base-info
        ref="baseInfo"
        v-if="currentTab === 'tab1'"
        @success="success"
        :spu-id="spuId"
      ></base-info>
      <sku-remark v-if="currentTab === 'tab2'" @success="success" :spu-id="spuId"></sku-remark>
      <sku-price
        ref="skuPrice"
        v-if="currentTab === 'tab3'"
        @success="success"
        :spu-id="spuId"
      ></sku-price>
      <sku-params
        ref="skuarams"
        v-if="currentTab === 'tab4'"
        @success="success"
        :spu-id="spuId"
      ></sku-params>
      <sku-package
        ref="skuPackage"
        v-if="currentTab === 'tab5'"
        @success="success"
        :spu-id="spuId"
      ></sku-package>
      <sku-service
        ref="skuService"
        v-if="currentTab === 'tab6'"
        @success="success"
        :spu-id="spuId"
      ></sku-service>
    </div>
  </div>
</template>

<script>
import StepperTabs from './components/StepperTabs.vue'
import BaseInfo from './components/BaseInfo.vue'
import SkuRemark from './components/SkuRemark.vue'
import SkuPrice from './components/SkuPrice.vue'
import SkuParams from './components/SkuParams.vue'
import SkuPackage from './components/SkuPackage.vue'
import SkuService from './components/SkuService.vue'

export default {
  components: {
    StepperTabs,
    BaseInfo,
    SkuRemark,
    SkuPrice,
    SkuParams,
    SkuPackage,
    SkuService
  },
  data() {
    return {
      currentTab: 'tab1',
      tabData: [
        { label: '1.基本信息', value: 'tab1' },
        { label: '2.SKU描述', value: 'tab2' },
        { label: '3.SKU价格', value: 'tab3' },
        { label: '4.规格参数', value: 'tab4' },
        { label: '5.SKU包装', value: 'tab5' },
        { label: '6.SKU售后', value: 'tab6' }
      ],
      spuId: this.$route.query.spuId
    }
  },
  methods: {
    tabSwitch(tab) {
      if (this.currentTab === 'tab1') {
        this.$refs.baseInfo.baseInfoSave(tab)
      }
      if (this.currentTab === 'tab3') {
        this.$refs.skuPrice.priceDataSave(tab)
      }
      if (['tab2', 'tab4', 'tab5'].includes(this.currentTab)) {
        this.currentTab = tab.value
      }
      if (this.currentTab === 'tab6') {
        this.$refs.skuService.save(tab)
      }
    },
    success(tab, spuId) {
      this.currentTab = tab.value
      if (spuId) {
        this.spuId = spuId
      }
    }
  }
}
</script>

<style></style>
