import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-11-12 14:11:54
 * @LastEditTime: 2021-11-16 11:33:09
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\sku\config\skuInfo.config.js
 */

export const OPTIONS = {
  CURRENCY_UNITS: [],
  SIZE_UNITS: []
}

export const PAGE_CONFIG = [
  {
    toolbar: [],
    grid: {
      columnData: [
        {
          width: '50',
          type: 'checkbox',
          showInColumnChooser: false
        },
        {
          field: 'itemCode',
          cssClass: 'lick',
          headerText: i18n.t('物料/品项编号')
        },
        {
          field: 'itemName',
          headerText: i18n.t('物料/品项名称')
        },
        {
          field: 'itemDescription',
          headerText: i18n.t('规格型号')
        },
        {
          field: 'oldItemCode',
          headerText: i18n.t('旧物料编号')
        },
        {
          field: 'baseMeasureUnitName',
          headerText: i18n.t('基本计量单位')
        },
        {
          field: 'itemGroupName',
          headerText: i18n.t('物料组')
        },
        {
          field: 'statusId',
          headerText: i18n.t('启用状态'),
          width: '150',
          valueConverter: {
            type: 'map',
            map: {
              0: i18n.t('草稿'),
              1: i18n.t('激活'),
              2: i18n.t('分发'),
              3: i18n.t('失效'),
              '-1': i18n.t('待审核'),
              '-2': i18n.t('准备')
            }
          }
        }
      ],
      asyncConfig: {
        url: `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem(
          'currentBu'
        )}`,
        params: {}
      }
    }
  }
]

export const COLUMNS_SKU = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    headerText: i18n.t('属性编码'),
    field: 'classificationCode'
  },
  {
    headerText: i18n.t('属性名称'),
    field: 'classificationName'
  },
  {
    headerText: i18n.t('属性类别'),
    field: 'classificationTypeName'
  },
  {
    headerText: i18n.t('属性值'),
    field: 'classificationValueNames'
  },
  {
    headerText: i18n.t('属性描述'),
    field: 'classificationDescription'
  }
]
