import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-10-27 14:38:14
 * @LastEditTime: 2021-11-30 16:12:23
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\sku\config\spuDefend.config.js
 */
import Vue from 'vue'
import { getComponent } from '@syncfusion/ej2-base'
// import { DropDownList } from "@syncfusion/ej2-vue-dropdowns";
import { Query } from '@syncfusion/ej2-data'

export const OPTIONS = {
  TYPES_ADD: [
    {
      label: i18n.t('新增SPU'),
      value: 0
    },
    {
      label: i18n.t('引用物料新增'),
      value: 1
    }
  ],
  BRAND: [
    {
      text: i18n.t('马牌'),
      value: '0'
    },
    {
      text: i18n.t('壳牌'),
      value: '1'
    },
    {
      text: '3M',
      value: '2'
    }
  ],
  STATUS: [
    {
      text: i18n.t('草稿'),
      value: 0
    },
    {
      text: i18n.t('启售'),
      value: 1
    },
    {
      text: i18n.t('停售'),
      value: 2
    }
  ],
  CATEGORY: []
}

export const DROPDOWN_TREE = {
  dataSource: [],
  value: 'id',
  text: 'name',
  child: 'children'
}

export const MAT_CHOOSE_CONFIG = [
  {
    toolbar: [],
    grid: {
      columnData: [
        {
          width: '50',
          type: 'checkbox',
          showInColumnChooser: false
        },
        {
          field: 'itemCode',
          cssClass: 'lick',
          headerText: i18n.t('物料/品项编号')
        },
        {
          field: 'itemName',
          headerText: i18n.t('物料/品项名称')
        },
        {
          field: 'itemDescription',
          headerText: i18n.t('规格型号')
        },
        {
          field: 'oldItemCode',
          headerText: i18n.t('旧物料编号')
        },
        {
          field: 'baseMeasureUnitName',
          headerText: i18n.t('基本计量单位')
        },
        {
          field: 'itemGroupName',
          headerText: i18n.t('物料组')
        },
        {
          field: 'statusId',
          headerText: i18n.t('启用状态'),
          width: '150',
          valueConverter: {
            type: 'map',
            map: {
              0: i18n.t('草稿'),
              1: i18n.t('激活'),
              2: i18n.t('分发'),
              3: i18n.t('失效'),
              '-1': i18n.t('待审核'),
              '-2': i18n.t('准备')
            }
          }
        }
      ],
      asyncConfig: {
        url: `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem(
          'currentBu'
        )}`,
        params: {}
      }
    }
  }
]

export const TOOLBAR_SETTINGS = {
  enable: true,
  enableFloating: true,
  type: 'Expand',
  items: [
    'Bold',
    'Italic',
    'Underline',
    '|',
    'Formats',
    'Alignments',
    'OrderedList',
    'UnorderedList',
    '|',
    'CreateLink',
    'Image',
    'backgroundColor',
    '|',
    'SourceCode',
    'Undo',
    'Redo'
  ],
  itemConfigs: {}
}

// let typeDropdown, elem;
export const COLUMNS_SP_ATTRS = function (data = []) {
  return [
    {
      width: '50',
      type: 'checkbox'
    },
    {
      headerText: i18n.t('属性编码'),
      field: 'classificationCode'
    },
    {
      headerText: i18n.t('属性名称'),
      field: 'classificationName'
    },
    {
      headerText: i18n.t('属性类别编码'),
      field: 'classificationTypeCode',
      visible: false
    },
    {
      headerText: i18n.t('属性类别'),
      field: 'classificationTypeName',
      editType: 'dropdownedit',
      edit: {
        // create: () => {
        //   elem = document.createElement("input");
        //   return elem;
        // },
        // read: () => {
        //   return typeDropdown.text;
        // },
        // destroy: () => {
        //   typeDropdown.destroy();
        // },
        // write: () => {
        //   typeDropdown = new DropDownList({
        //     dataSource: data,
        //     fields: { value: "dictCode", text: "dictName" },
        //     change: () => {},
        //     placeholder: "Select a country",
        //     floatLabelType: "Never",
        //   });
        //   typeDropdown.appendTo(elem);
        // },
        params: {
          allowFiltering: true,
          dataSource: data,
          fields: { text: 'dictName', value: 'dictName' },
          query: new Query(),
          change: (e) => {
            const { itemData } = e
            const grid = new getComponent('dataGrid1', 'grid')
            const rowIndex = grid.editModule.editModule.cellDetails.rowIndex
            grid.updateCell(rowIndex, 'classificationTypeCode', itemData.dictCode)
          }
        }
      }
    },
    {
      headerText: i18n.t('属性值'),
      field: 'classificationValueNames',
      headerTemplate: function () {
        return {
          template: Vue.component('datetemplate', {
            template: `<div style="color: #292929; font-weight: 500; font-size: 14px;">
              {{ headerText }}
              <mt-tool-tip content="属性值之间使用“ ；”相隔" target="#tip">
                <mt-icon id="tip" name="icon_solid_Information" class="mt-ml-10" style="color: #9BAAC1;" />
              </mt-tool-tip>
            </div>`,
            data: function () {
              return { headerText: i18n.t('属性值') }
            }
          })
        }
      }
    },
    {
      headerText: i18n.t('属性描述'),
      field: 'classificationDescription'
    }
  ]
}

export const COLUMNS_SKU = function (data = [], that) {
  return [
    {
      width: '50',
      type: 'checkbox'
    },
    {
      width: '150',
      headerText: i18n.t('SKU编码'),
      field: 'barCode'
    },
    {
      width: '150',
      headerText: i18n.t('SKU名称'),
      field: 'name'
    },
    {
      width: '150',
      headerText: i18n.t('特征属性'),
      field: 'classificationFullname',
      allowEditing: false
    },
    {
      width: '150',
      headerText: i18n.t('物料ID'),
      field: 'itemId',
      visible: false
    },
    {
      width: '150',
      headerText: i18n.t('物料编号'),
      field: 'itemCode',
      allowEditing: false
    },
    {
      width: '250',
      headerText: i18n.t('物料名称'),
      field: 'itemName',
      allowEditing: false,
      template: function () {
        return {
          template: Vue.component('relate', {
            //$parent.$parent.$parent.$emit('relate', data);
            template: `
              <div>
                <div v-if="!data.itemName" class="mt-pa-10 relate-btn" @click="handleClicked">
                  <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />选择物料
                </div>
                <div v-else>{{ data.itemName }}
                  <span class="mt-pa-10 relate-btn" @click="handleClicked">
                    <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />修改物料
                  </span>
                </div>
              </div>
            `,
            data() {
              return {
                data: {}
              }
            },
            methods: {
              handleClicked() {
                that.currentSkuIndex = this.data.index
                that.$refs.dialog.ejsRef.show()
              }
            }
          })
        }
      }
    },
    {
      width: '150',
      headerText: i18n.t('规格型号'),
      field: 'specificationModel'
    },
    {
      width: '150',
      headerText: i18n.t('SKU毛重'),
      field: 'grossWet'
    },
    {
      width: '150',
      headerText: i18n.t('SKU净重'),
      field: 'netWet'
    },
    {
      width: '150',
      headerText: '体积（缺）',
      field: 'itemName'
    },
    {
      width: '150',
      headerText: i18n.t('长宽高尺寸单位'),
      field: 'sizeUnitName',
      editType: 'dropdownedit',
      edit: {
        params: {
          allowFiltering: true,
          dataSource: data,
          fields: { text: 'text', value: 'text' },
          query: new Query()
        }
      }
    },
    {
      width: '150',
      headerText: i18n.t('长'),
      field: 'skuLength'
    },
    {
      width: '150',
      headerText: i18n.t('宽'),
      field: 'skuWidth'
    },
    {
      width: '150',
      headerText: i18n.t('高'),
      field: 'skuHeight'
    },
    {
      width: '150',
      headerText: i18n.t('制造商'),
      field: 'manufacturerName'
    },
    {
      width: '150',
      headerText: i18n.t('最小订购量'),
      field: 'minPurchaseQuantity'
    },
    {
      width: '150',
      headerText: i18n.t('最大订购量'),
      field: 'maxPurchaseQuantity'
    },
    {
      width: '150',
      headerText: i18n.t('订购倍数'),
      field: 'purchaseFolds'
    },
    {
      width: '150',
      headerText: '计划提前期（缺）',
      field: 'itemName'
    },
    {
      width: '150',
      headerText: i18n.t('计划前置提前期'),
      field: 'processingTime'
    },
    {
      width: '150',
      headerText: i18n.t('计划生产提前期'),
      field: 'manufactureTime'
    },
    {
      width: '150',
      headerText: i18n.t('计划后置提前期'),
      field: 'receiveingTime'
    }
  ]
}

export const COLUMNS_PRICE = function (data = []) {
  return [
    {
      width: '50',
      type: 'checkbox'
    },
    {
      headerText: i18n.t('SKU名称'),
      field: 'name',
      allowEditing: false
    },
    {
      headerText: i18n.t('市场价'),
      field: 'marketPrice'
    },
    {
      headerText: i18n.t('基础销售价'),
      field: 'basicSellingPrice'
    },
    {
      headerText: i18n.t('货币单位'),
      field: 'currencyName',
      editType: 'dropdownedit',
      edit: {
        params: {
          allowFiltering: true,
          dataSource: data,
          fields: { text: 'currencyName', value: 'currencyName' },
          query: new Query(),
          change: (e) => {
            const { itemData } = e
            const grid = new getComponent('dataGridPrice', 'grid')
            const rowIndex = grid.editModule.editModule.cellDetails.rowIndex
            grid.updateCell(rowIndex, 'currencyId', itemData.id)
          }
        }
      }
    },
    {
      headerText: i18n.t('货币单位id'),
      field: 'currencyId',
      visible: false
    }
  ]
}
