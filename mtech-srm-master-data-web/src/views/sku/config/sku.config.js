import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-10-26 13:35:55
 * @LastEditTime: 2021-11-11 17:41:50
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\sku\config\sku.config.js
 */
export const PAGE_PLUGIN = [
  {
    title: i18n.t('商品SPU'),
    toolbar: ['Add', 'Delete'],
    grid: {
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'spuName',
          headerText: i18n.t('商品SPU编码'),
          width: '200',
          cellTools: ['edit', 'delete']
        },
        {
          field: 'spuName',
          width: '200',
          headerText: i18n.t('商品SPU名称')
        },
        {
          field: 'headImgId',
          width: '200',
          headerText: i18n.t('图片')
        },
        {
          field: 'categoryNamePath',
          width: '200',
          headerText: i18n.t('所属类目')
        },
        {
          field: 'stationNum',
          width: '200',
          headerText: i18n.t('发布时间')
        },
        {
          field: 'employeeNum',
          width: '200',
          headerText: i18n.t('发布人')
        },
        {
          field: 'statusId',
          headerText: i18n.t('状态'),
          width: '200',
          valueConverter: {
            type: 'map',
            map: {
              0: i18n.t('草稿'),
              1: i18n.t('激活'),
              2: i18n.t('分发'),
              3: i18n.t('失效'),
              '-1': i18n.t('待审核'),
              '-2': i18n.t('准备')
            }
          }
        }
      ],
      asyncConfig: {
        url: '/masterDataManagement/tenant/spu/page-query',
        params: {}
      }
    }
  },
  {
    title: this.$t('SKU列表'),
    toolbar: ['Delete'],
    grid: {
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'name',
          headerText: this.$t('SKU编号'),
          width: '200',
          cellTools: ['edit', 'delete']
        },
        {
          field: 'name',
          width: '200',
          headerText: this.$t('SKU名称')
        },
        {
          field: 'ShipAddress',
          width: '200',
          headerText: i18n.t('创建类型')
        },
        {
          field: 'itemCode',
          width: '200',
          headerText: i18n.t('商品SPU/物料编号')
        },
        {
          field: 'itemName',
          width: '200',
          headerText: i18n.t('商品SPU/物料名称')
        },
        {
          field: 'headImgId',
          width: '200',
          headerText: this.$t('SKU图片')
        },
        {
          field: 'specificationModel',
          width: '200',
          headerText: i18n.t('规格型号')
        },
        {
          field: 'manufacturerName',
          width: '200',
          headerText: i18n.t('制造商')
        },
        {
          field: 'ShipAddress',
          width: '200',
          headerText: i18n.t('交货期')
        },
        {
          field: 'statusId',
          headerText: i18n.t('状态'),
          width: '150',
          valueConverter: {
            type: 'map',
            map: {
              0: i18n.t('草稿'),
              1: i18n.t('激活'),
              2: i18n.t('分发'),
              3: i18n.t('失效'),
              '-1': i18n.t('待审核'),
              '-2': i18n.t('准备')
            }
          }
        }
      ],
      asyncConfig: {
        url: '/masterDataManagement/tenant/sku/page-query',
        params: {}
      }
    }
  }
]
