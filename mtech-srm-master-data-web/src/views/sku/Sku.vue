<!--
 * @Author: your name
 * @Date: 2021-10-26 13:34:16
 * @LastEditTime: 2021-11-16 09:48:57
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\sku\Sku.vue
-->
<template>
  <div class="full-height mt-pt-20">
    <mt-template-page
      ref="tepPage"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
  </div>
</template>

<script>
import { PAGE_PLUGIN } from './config/sku.config'

export default {
  data() {
    return {
      pageConfig: PAGE_PLUGIN
    }
  },
  methods: {
    async handleClickToolBar(e) {
      const { toolbar, tabIndex, grid } = e
      const rowSelected = grid?.getSelectedRecords?.()

      if (toolbar.id === 'Add' && tabIndex === 0) {
        this.$router.push('/masterdata/spu-defend')
      }
      if (toolbar.id === 'Delete' && tabIndex === 0 && rowSelected.length) {
        this.handleDelete(rowSelected, 'spu')
      }
      if (toolbar.id === 'Delete' && tabIndex === 1 && rowSelected.length) {
        this.handleDelete(rowSelected, 'sku')
      }
    },
    handleClickCellTool(e) {
      const { data, tool, tabIndex } = e

      if (tool.id === 'edit' && tabIndex === 0) {
        this.$router.push(`/masterdata/spu-defend?spuId=${data.id}`)
      }
      if (tool.id === 'delete' && tabIndex === 0) {
        this.handleDelete([data], 'spu')
      }
      if (tool.id === 'edit' && tabIndex === 1) {
        this.$router.push(`/masterdata/sku-info?skuId=${data.id}`)
      }
      if (tool.id === 'delete' && tabIndex === 1) {
        this.handleDelete([data], 'sku')
      }
    },
    handleDelete(rows, type) {
      const dict = {
        spu: 'spuDel',
        sku: 'skuDel'
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.sku[dict[type]](data).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    }
  }
}
</script>

<style></style>
