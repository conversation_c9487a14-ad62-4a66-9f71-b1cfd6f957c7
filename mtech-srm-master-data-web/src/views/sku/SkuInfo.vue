<!--
 * @Author: your name
 * @Date: 2021-11-12 13:49:39
 * @LastEditTime: 2022-04-08 09:53:06
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\sku\SkuInfo.vue
-->
<template>
  <div>
    <div class="flex justify-end items-center mt-pa-20 stepper-tabs">
      <div>
        <mt-button css-class="e-flat" :is-primary="true" @click="save">{{ $t('保存') }}</mt-button>
        <mt-button css-class="e-flat" :is-primary="true" @click="submit">{{
          $t('提交')
        }}</mt-button>
        <mt-button css-class="e-flat" :is-primary="true" @click="cancel">{{
          $t('取消')
        }}</mt-button>
      </div>
    </div>

    <expansion-item :title="$t('新增方式')" class="mt-mt-20">
      <div>{{ spuInfoForm.addTypeName }}</div>
    </expansion-item>

    <expansion-item :title="$t('SPU基础信息')" class="mt-mt-20">
      <div class="flex justify-between base-info">
        <div class="uploader"></div>
        <mt-form ref="ruleForm" :model="spuInfoForm" :rules="rulesBase" class="base">
          <div class="flex justify-start mt-gutter-20">
            <mt-form-item prop="spuName" :label="$t('SPU名称')">
              <mt-input
                v-model="spuInfoForm.spuName"
                :readonly="false"
                :show-clear-button="false"
                :width="240"
                type="text"
              ></mt-input>
            </mt-form-item>
            <mt-form-item prop="brand" :label="$t('品牌')">
              <mt-input
                v-model="spuInfoForm.brand"
                :readonly="true"
                :show-clear-button="false"
                :width="240"
                type="text"
              ></mt-input>
            </mt-form-item>
            <mt-form-item prop="madeIn" :label="$t('产地')">
              <mt-input
                v-model="spuInfoForm.madeIn"
                :readonly="true"
                :show-clear-button="false"
                :width="240"
                type="text"
              ></mt-input>
            </mt-form-item>
          </div>
          <div>
            <mt-form-item prop="description" :label="$t('SPU详细描述')">
              <mt-input
                v-model="spuInfoForm.description"
                :multiline="true"
                :readonly="true"
                :rows="3"
                type="text"
                :placeholder="$t('请输入说明内容，不超过200字')"
              ></mt-input>
            </mt-form-item>
          </div>
        </mt-form>
      </div>
      <div class="base-title">{{ $t('特征属性') }}</div>
      <div>
        <mt-data-grid
          id="dataGrid1"
          ref="dataGrid"
          :data-source="dataSpAttrs"
          :column-data="columnsSpAttrs"
        ></mt-data-grid>
      </div>
    </expansion-item>

    <expansion-item :title="$t('SKU信息')" class="mt-mt-20">
      <div>
        <mt-form ref="ruleForm" :model="skuInfoForm" :rules="rulesSku">
          <mt-row :gutter="20">
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="barCode" :label="$t('SKU编码')">
                <mt-input
                  v-model="skuInfoForm.barCode"
                  :readonly="false"
                  :show-clear-button="false"
                  type="text"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="name" :label="$t('SKU名称')">
                <mt-input
                  v-model="skuInfoForm.name"
                  :readonly="false"
                  :show-clear-button="false"
                  type="text"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="specificationModel" :label="$t('规格型号')">
                <mt-input
                  v-model="skuInfoForm.specificationModel"
                  :readonly="false"
                  :show-clear-button="false"
                  type="text"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="itemCode" :label="$t('物料编码')">
                <mt-input
                  v-model="skuInfoForm.itemCode"
                  :readonly="true"
                  :show-clear-button="false"
                  type="text"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="itemName" :label="$t('物料名称')">
                <mt-input
                  v-model="skuInfoForm.itemName"
                  :readonly="true"
                  :show-clear-button="false"
                  type="text"
                ></mt-input>
              </mt-form-item> </mt-col
            ><mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="grossWet" :label="$t('SKU毛重')">
                <mt-input
                  v-model="skuInfoForm.grossWet"
                  :readonly="false"
                  :show-clear-button="false"
                  type="text"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="netWet" :label="$t('SKU净重')">
                <mt-input
                  v-model="skuInfoForm.netWet"
                  :readonly="false"
                  :show-clear-button="false"
                  type="text"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="skuLength" :label="$t('长度')">
                <mt-input
                  v-model="skuInfoForm.skuLength"
                  :readonly="false"
                  :show-clear-button="false"
                  type="text"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="skuWidth" :label="$t('宽度')">
                <mt-input
                  v-model="skuInfoForm.skuWidth"
                  :readonly="false"
                  :show-clear-button="false"
                  type="text"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="skuHeight" :label="$t('高度')">
                <mt-input
                  v-model="skuInfoForm.skuHeight"
                  :readonly="false"
                  :show-clear-button="false"
                  type="text"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="sizeUnitId" :label="$t('长宽高尺寸单位（缺）')">
                <mt-select
                  v-model="skuInfoForm.sizeUnitId"
                  :data-source="options.SIZE_UNITS"
                  :fields="{ text: 'dictName', value: 'id' }"
                  :readonly="false"
                  :show-clear-button="false"
                  :placeholder="$t('请选择')"
                ></mt-select>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="spuName" :label="$t('体积（缺）')">
                <mt-input
                  v-model="skuInfoForm.spuName"
                  :readonly="false"
                  :show-clear-button="false"
                  type="text"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="sizeUnitId" :label="$t('体积单位')">
                <mt-select
                  v-model="skuInfoForm.sizeUnitId"
                  :data-source="options.SIZE_UNITS"
                  :fields="{ text: 'dictName', value: 'id' }"
                  :readonly="false"
                  :show-clear-button="false"
                  :placeholder="$t('请选择')"
                ></mt-select>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="purchaseFolds" :label="$t('订购倍数')">
                <mt-input
                  v-model="skuInfoForm.purchaseFolds"
                  :readonly="false"
                  :show-clear-button="false"
                  type="text"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="maxPurchaseQuantity" :label="$t('最大订购量')">
                <mt-input
                  v-model="skuInfoForm.maxPurchaseQuantity"
                  :readonly="false"
                  :show-clear-button="false"
                  type="text"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="minPurchaseQuantity" :label="$t('最小订购量')">
                <mt-input
                  v-model="skuInfoForm.minPurchaseQuantity"
                  :readonly="false"
                  :show-clear-button="false"
                  type="text"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="planDays" :label="$t('计划提前期（缺）')">
                <mt-input
                  v-model="skuInfoForm.planDays"
                  :readonly="true"
                  :show-clear-button="false"
                  type="text"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="processingTime" :label="$t('计划前置提前期')">
                <mt-input
                  v-model="skuInfoForm.processingTime"
                  :readonly="false"
                  :show-clear-button="false"
                  type="number"
                  @input="planDaysGet"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="manufactureTime" :label="$t('计划生产提前期')">
                <mt-input
                  v-model="skuInfoForm.manufactureTime"
                  :readonly="false"
                  :show-clear-button="false"
                  type="number"
                  @input="planDaysGet"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="receiveingTime" :label="$t('计划后置提前期')">
                <mt-input
                  v-model="skuInfoForm.receiveingTime"
                  :readonly="false"
                  :show-clear-button="false"
                  type="number"
                  @input="planDaysGet"
                ></mt-input>
              </mt-form-item>
            </mt-col>
          </mt-row>
        </mt-form>
      </div>
    </expansion-item>

    <expansion-item :title="$t('SKU销售价格')" class="mt-mt-20">
      <div>
        <mt-form ref="ruleForm" :model="priceForm" :rules="rulesPrice">
          <mt-row :gutter="20">
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="marketPrice" :label="$t('市场价')">
                <mt-input
                  v-model="priceForm.marketPrice"
                  :readonly="false"
                  :show-clear-button="false"
                  type="text"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="basicSellingPrice" :label="$t('基本销售价')">
                <mt-input
                  v-model="priceForm.basicSellingPrice"
                  :readonly="false"
                  :show-clear-button="false"
                  type="text"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="currencyId" :label="$t('货币单位')">
                <mt-select
                  v-model="priceForm.currencyId"
                  :data-source="options.CURRENCY_UNITS"
                  :fields="{ text: 'currencyName', value: 'id' }"
                  :readonly="false"
                  :show-clear-button="false"
                  :placeholder="$t('请选择货币单位')"
                ></mt-select>
              </mt-form-item>
            </mt-col>
          </mt-row>
        </mt-form>
      </div>
    </expansion-item>

    <expansion-item :title="$t('SKU规格参数')" class="mt-mt-20" dense>
      <template #actions>
        <mt-button css-class="e-flat" :is-primary="true" :disabled="false" @click="paramsDel">{{
          $t('删除')
        }}</mt-button>
      </template>
      <div>
        <mt-row :gutter="20">
          <mt-col :sm="8" :md="8" :lg="8" :xl="8" v-for="item in paramsData" :key="item.id">
            <div class="params-item mt-pa-20">
              <div class="flex justify-between items-center params-item--header">
                <!-- <span class="params-item__label">{{ $t("附件名称") }}</span> -->
                <input
                  class="e-input"
                  type="text"
                  :readonly="false"
                  :placeholder="$t('请输入规格参数名称')"
                  v-model="item.specificationName"
                  @blur="paramsAddOrEditSave(item)"
                />
                <mt-checkbox></mt-checkbox>
              </div>
              <div>
                <div class="e-input-group">
                  <input
                    class="e-input"
                    type="text"
                    :placeholder="$t('请输入规格参数')"
                    v-model="item.specificationValueName"
                    @blur="paramsAddOrEditSave(item)"
                  />
                </div>
              </div>
            </div>
          </mt-col>

          <mt-col :sm="8" :md="8" :lg="8" :xl="8">
            <div
              class="params-action flex justify-center items-center cursor-pointer"
              @click="paramsAdd"
            >
              <div class="text-center">
                <mt-icon name="icon_solid_add" class="cursor-pointer params-action__icon"></mt-icon>
                <div class="params-action__label mt-mt-10">
                  {{ $t('新增附件') }}
                </div>
              </div>
            </div>
          </mt-col>
        </mt-row>
      </div>
    </expansion-item>

    <expansion-item :title="$t('SKU包装清单')" class="mt-mt-20" dense>
      <template #actions>
        <mt-button css-class="e-flat" :is-primary="true" :disabled="false" @click="fileDel">{{
          $t('删除')
        }}</mt-button>
      </template>
      <mt-checkbox-group v-model="packageChecked" :disabled="false">
        <mt-row :gutter="20" class="attr-file--list">
          <mt-col :sm="8" :md="8" :lg="8" :xl="8" v-for="item in packageData" :key="item.id">
            <div class="attr-file--item mt-pa-20">
              <div class="flex justify-between items-center">
                <span class="attr-file--item__label">{{ $t('附件名称') }}</span>
                <mt-checkbox :fields="{ id: 'id' }" :content="{ id: item.id }"></mt-checkbox>
              </div>
              <div class="mt-mt-10">
                <div class="e-input-group">
                  <input
                    class="e-input"
                    type="text"
                    :placeholder="$t('请输入附件名称')"
                    v-model="item.accessoryName"
                    @input="flag = true"
                    @blur="fileAddOrEditSave(item)"
                  />
                </div>
              </div>
              <div class="flex justify-between items-center mt-mt-20">
                <span class="attr-file--item__label">{{ $t('附件数量') }}</span>
              </div>
              <div class="mt-mt-10">
                <div class="e-input-group">
                  <input
                    class="e-input"
                    type="text"
                    :placeholder="$t('请输入附件数量')"
                    v-model="item.accessoryQuantity"
                    @input="flag = true"
                    @blur="fileAddOrEditSave(item)"
                  />
                </div>
              </div>
            </div>
          </mt-col>

          <mt-col :sm="8" :md="8" :lg="8" :xl="8">
            <div class="attr-file--action flex justify-center items-center" @click="fileAdd">
              <div class="text-center">
                <mt-icon
                  name="icon_solid_add"
                  class="cursor-pointer attr-file--action__icon"
                ></mt-icon>
                <div class="attr-file--action__label mt-mt-10">
                  {{ $t('新增附件') }}
                </div>
              </div>
            </div>
          </mt-col>
        </mt-row>
      </mt-checkbox-group>
    </expansion-item>
  </div>
</template>

<script>
import ExpansionItem from '@/components/common/ExpansionItem.vue'
import { OPTIONS, PAGE_CONFIG, COLUMNS_SKU } from './config/skuInfo.config.js'

export default {
  components: {
    ExpansionItem
  },
  data() {
    return {
      options: OPTIONS,
      pageConfig: PAGE_CONFIG,

      spuInfoForm: {},
      rulesBase: {},
      dataSpAttrs: [],
      columnsSpAttrs: COLUMNS_SKU,

      skuInfoForm: {},
      rulesSku: {},

      priceForm: {},
      rulesPrice: {},

      paramsData: [],
      paramsChecked: [],

      packageData: [],
      packageChecked: [],

      skuId: this.$route.query.skuId,
      flag: false
    }
  },
  mounted() {
    this.skuInfoGet()
    this.currencyUnitGet()
  },
  methods: {
    skuInfoGet() {
      const query = { id: this.skuId }
      this.$API.sku.skuInfoGet(query).then((res) => {
        const {
          basicSellingPrice,
          currencyId,
          currencyName,
          marketPrice,
          spu,
          skuPackage,
          specifications,
          ...skuInfo
        } = res.data

        this.spuInfoForm = spu
        this.dataSpAttrs = spu.classifications
        this.skuInfoForm = skuInfo
        this.priceForm = {
          basicSellingPrice,
          currencyId,
          currencyName,
          marketPrice
        }
        this.paramsData = specifications
        this.packageData = skuPackage
      })
    },
    fileAdd() {
      const lastItem = this.packageData[this.packageData.length - 1]
      if (lastItem && (!lastItem.accessoryName || !lastItem.accessoryQuantity)) {
        return this.$toast({
          content: this.$t('请完整填写附件名称和数量'),
          type: 'warning'
        })
      }
      this.packageData.push({
        id: `UID${Math.random()}`,
        accessoryName: '',
        accessoryQuantity: ''
      })
    },
    async fileDel() {
      const localDelIds = this.packageChecked.filter((el) => el.startsWith('UID'))
      const originDelIds = this.packageChecked.filter((el) => !el.startsWith('UID'))
      const data = { ids: originDelIds }
      this.packageData = this.packageData.filter((e) => !localDelIds.includes(e.id))
      originDelIds.length &&
        (await this.$API.sku.packageDataDel(data).then(() => {
          this.skuInfoGet()
        }))
      this.packageChecked = []
    },
    fileAddOrEditSave(file) {
      if (file.accessoryName && file.accessoryQuantity && file.id.startsWith('UID')) {
        // eslint-disable-next-line no-unused-vars
        const { id, ...fileField } = file
        const data = { ...fileField, skuId: this.skuId }
        this.$API.sku.packageDataAddSave(data).then((res) => {
          file.id = res.data.id
          this.flag = false
        })
      }
      if (file.accessoryName && file.accessoryQuantity && !file.id.startsWith('UID')) {
        const data = [file]
        this.$API.sku.packageDataEditSave(data).then(() => {
          this.flag = false
        })
      }
    },

    paramsAdd() {
      const lastItem = this.paramsData[this.paramsData.length - 1]
      if (lastItem && (!lastItem.specificationName || !lastItem.specificationValueName)) {
        return this.$toast({
          content: this.$t('请完整填写附件名称和数量'),
          type: 'warning'
        })
      }
      this.paramsData.push({
        id: `UID${Math.random()}`,
        specificationName: '',
        specificationValueName: ''
      })
    },
    async paramsDel() {
      const localDelIds = this.paramsChecked.filter((el) => el.startsWith('UID'))
      const originDelIds = this.paramsChecked.filter((el) => !el.startsWith('UID'))
      const data = { ids: originDelIds }
      this.paramsData = this.paramsData.filter((e) => !localDelIds.includes(e.id))
      originDelIds.length &&
        (await this.$API.sku.paramsDataDel(data).then(() => {
          this.skuInfoGet()
        }))
      this.paramsChecked = []
    },
    paramsAddOrEditSave(params) {
      if (
        params.specificationName &&
        params.specificationValueName &&
        params.id.startsWith('UID')
      ) {
        // eslint-disable-next-line no-unused-vars
        const { id, ...fileField } = params
        const data = { ...fileField, skuId: this.skuId }
        this.$API.sku.paramsDataAddSave(data).then((res) => {
          params.id = res.data.id
          this.flag = false
        })
      }
      if (
        params.specificationName &&
        params.specificationValueName &&
        !params.id.startsWith('UID')
      ) {
        const data = { ...params }
        this.$API.sku.paramsDataEditSave(data).then(() => {
          this.flag = false
        })
      }
    },

    save() {
      const data = {
        ...this.spuInfoForm,
        ...this.skuInfoForm,
        ...this.priceForm
      }
      this.$API.sku.skuInfoUpdate(data).then(() => {})
    },
    submit() {},
    cancel() {
      this.$router.push('/masterdata/sku')
    },
    planDaysGet() {
      this.skuInfoForm.planDays =
        this.skuInfoForm.processingTime * 1 +
        this.skuInfoForm.receiveingTime * 1 +
        this.skuInfoForm.manufactureTime * 1
    },
    currencyUnitGet() {
      const query = { page: { current: 1, size: 5000 } }
      this.$API.baseMainData.getCurrencyData(query).then((res) => {
        this.options.CURRENCY_UNITS = res.data.records
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.stepper-tabs {
  width: 100%;
  height: 60px;
  background: rgba(255, 255, 255, 1);
  border-radius: 8px 8px 0 0;
  box-shadow: inset 0 -1px 0 0 rgba(232, 232, 232, 1);
}
.base-info {
  .uploader {
    width: 160px;
    height: 160px;
    background-color: #ccc;
  }
  .base {
    width: calc(100% - 190px);
    height: 160px;
    padding-top: 5px;
  }
}
.base-title {
  height: 20px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(154, 154, 154, 1);
  margin-top: 40px;
}
.params-item {
  height: 104px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(155, 170, 193, 0.4);
  border-radius: 8px;
  &--header {
    /deep/ input.e-input {
      border-bottom: 0 !important;
    }
  }
  &__label {
    height: 31px;
    line-height: 31px;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
  }
}
.params-action {
  height: 104px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(155, 170, 193, 0.4);
  border-radius: 8px;
  &__icon {
    color: #98aac3;
  }
  &__label {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(152, 170, 195, 1);
  }
}
.attr-file--list {
  max-height: 400px;
  overflow: auto;
}
.attr-file--item {
  height: 186px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(155, 170, 193, 0.4);
  border-radius: 8px;
  &__label {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
  }
}
.attr-file--action {
  height: 186px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(155, 170, 193, 0.4);
  border-radius: 8px;
  &__icon {
    color: #98aac3;
  }
  &__label {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(152, 170, 195, 1);
  }
}
</style>
