import { i18n } from '@/main.js'
import Vue from 'vue'
const operators = [
  { value: 'equal', key: '等于' },
  { value: 'notequal', key: '不等于' },
  { value: 'contains', key: '包含' },
  { value: 'greaterthan', key: '大于' },
  { value: 'greaterthanorequal', key: '大于等于' },
  { value: 'lessthan', key: '小于' },
  { value: 'lessthanorequal', key: '小于等于' }
]
const Mixins = {
  data() {
    const _this = this
    return {
      editColumns: [
        {
          type: 'checkbox',
          width: '50',
          showInColumnChooser: false
        },
        {
          field: 'roleName',
          headerText: i18n.t('角色名称'),
          width: '300',
          textAlign: 'left',
          isPrimaryKey: 'true',
          template: () => {
            return {
              template: Vue.component('todo-column-template', {
                template: `<span @click="clickExpanding" :class="{'block-item': data.leafFlag === 0 && !data.isExpand}">
                                    <template v-if="data.leafFlag === 0 && !data.isExpand">
                                        <mt-icon name="arrowhead-right" style="cursor: pointer"/>
                                    </template>
                                    {{data.roleName}}
                                </span>`,
                data() {
                  return {}
                },
                methods: {
                  clickExpanding(e) {
                    if (this.data.leafFlag === 1) return
                    e.stopPropagation()
                    _this.getChildRoles(this.data)
                  }
                }
              })
            }
          }
        },
        {
          field: 'roleCode',
          headerText: i18n.t('角色编码'),
          width: '200',
          textAlign: 'left',
          showColumnChooser: true
        },
        {
          field: 'progress',
          headerText: i18n.t('状态'),
          width: '160',
          textAlign: 'left',
          showColumnChooser: true,
          template: () => {
            return {
              template: Vue.component('todo-column-template', {
                template: `<div class="status-box mt-flex" :class="{'disabled': status === 1, 'openly': status === 0 }">
                                    <template v-if="status === 0">
                                        <div class="green"></div>
                                        <div class="status-txt">{{ $t("启用") }}</div>
                                    </template>
                                    <template v-else>
                                        <div class="dark"></div>
                                        <div class="status-txt">{{ $t("停用") }}</div>
                                    </template>
                                </div>`,
                data() {
                  return {
                    status: 0
                  }
                },
                methods: {}
              })
            }
          }
        },
        {
          field: 'createUserName',
          headerText: i18n.t('创建人'),
          width: '160',
          textAlign: 'left',
          showColumnChooser: true
        },
        {
          headerText: i18n.t('描述'),
          width: '160',
          textAlign: 'left',
          showColumnChooser: true,
          template: () => {
            return {
              template: Vue.component('todo-column-template', {
                template: `
                                  <div class="todo-list-column mt-flex">
                                    <div class="operator-btn" @click.stop="showDetail">{{ $t("描述") }}</div>
                                  </div>`,
                data: function () {
                  return { data: {} }
                },
                methods: {
                  showDetail() {
                    let { data } = this
                    _this.showDetail(data)
                  }
                }
              })
            }
          }
        },
        {
          headerText: i18n.t('操作'),
          textAlign: 'left',
          width: '260',
          showColumnChooser: true,
          template: () => {
            return {
              template: Vue.component('todo-column-template', {
                template: `
                                  <div class="todo-list-column mt-flex">
                                    <div class="operator-btn" @click.stop="editRow">{{ $t("编辑") }}</div>
                                    <div class="operator-btn" @click.stop="deleteRow">{{ $t("删除") }}</div>
                                  </div>`,
                data: function () {
                  return { data: {} }
                },
                methods: {
                  // 新增下级
                  // <div class="operator-btn" @click.stop="addChild">{{ $t("增加下级") }}</div>
                  addChild() {
                    let { data } = this
                    _this.addChildRole(data)
                  },
                  // 编辑行
                  editRow() {
                    let { data } = this
                    _this.editRow(data)
                  },
                  deleteRow() {
                    let { data } = this
                    _this.deleteRow(data)
                  }
                }
              })
            }
          }
        }
      ],
      editColumnsSimple: [
        {
          field: 'roleName',
          headerText: i18n.t('角色名称'),
          label: i18n.t('角色名称'),
          width: '150',
          operators: operators
        },
        {
          field: 'roleCode',
          headerText: i18n.t('角色编码'),
          label: i18n.t('角色编码'),
          width: '150',
          operators: operators
        },
        {
          field: 'createUserName',
          headerText: i18n.t('创建人'),
          label: i18n.t('创建人'),
          width: '150',
          operators: operators
        }
      ]
    }
  }
}

export default Mixins
