<template>
  <div class="role-container">
    <div class="tree-grid-box">
      <!-- 顶部toolbar -->
      <div class="toolbar mt-flex">
        <div class="left-box mt-flex">
          <div class="tool-item" @click="addMainRoles">
            <mt-icon name="icon_solid_Createproject" />
            <span>{{ $t('新增') }}</span>
            <!-- <span>{{ $t("一级新增") }}</span> -->
          </div>
          <div class="tool-item" @click="batchDeleteArr">
            <mt-icon name="icon_solid_Delete1" />
            <span>{{ $t('批量删除') }}</span>
          </div>
        </div>
        <div class="right-box mt-flex">
          <div class="tool-item" @click="filterRoles">
            <mt-icon name="icon_solid_Filter" />
            <span>{{ $t('筛选') }}</span>
          </div>
          <div class="tool-item">
            <mt-icon name="printer-01" />
            <span>{{ $t('导出') }}</span>
          </div>
          <div class="tool-item">
            <mt-icon name="icon_solid_Refresh" @click="refreshGrid" />
            <span>{{ $t('刷新') }}</span>
          </div>
          <div class="tool-item" @click="showSetting">
            <mt-icon name="a-icon_solid_Settingup" />
            <span>{{ $t('设置') }}</span>
          </div>
        </div>
      </div>

      <!-- 右侧toolbar的筛选按钮 -->
      <div class="query-build-box" v-if="showQueryBuilder">
        <div class="query-btns">
          <mt-button @click="handleQuerySearch">
            <i class="mt-icons mt-icon-BT_Excelfilter"></i>
            {{ $t('过滤') }}
          </mt-button>
          <mt-button @click="handleQueryReset">
            <i class="mt-icons mt-icon-BT_Excelclear"></i> {{ $t('重置') }}
          </mt-button>
        </div>
        <mt-query-builder
          ref="queryBuilder"
          :data-source="inlineEditData"
          width="100%"
          :column-data="editColumnsSimple"
        >
        </mt-query-builder>
      </div>

      <div class="main-grid flex1">
        <!-- @expanding="expanding" -->
        <mt-tree-grid
          ref="treeGrid"
          class="grid-wraps"
          :data-source="inlineEditData"
          :columns="editColumns"
          :show-column-chooser="true"
          child-mapping="subtasks"
          :tree-column-index="1"
          :allow-paging="true"
          grid-lines="Horizontal"
          :page-settings="pageSettings"
          :allow-sorting="true"
          :row-selected="rowSelected"
          :row-deselected="rowDeselected"
          :allow-filtering="true"
          :filter-settings="filterSettings"
          :selection-settings="selectionSettings"
          :auto-check-hierarchy="true"
          @currentChange="handleCurrentChange"
          @sizeChange="handleGridSizeChange"
        ></mt-tree-grid>
      </div>
    </div>

    <mt-dialog ref="dialogPop" css-class="dialog-box" :header="$t('查看详情')" width="900">
      <div class="dialog-warp">
        <div class="dialog-inner-title">{{ $t('角色描述') }}</div>
        <div class="dialog-des">
          {{ roleDescription }}
        </div>
      </div>
    </mt-dialog>

    <!-- 侧边栏 -->
    <!-- <template v-if="isOpen">
            <side-bar-tp
                ref="sideBarTp"
                @closeSideBar="closeSideBar"
            ></side-bar-tp>
        </template> -->
  </div>
</template>

<script>
import Vue from 'vue'
// import sideBarTp from "./components/sideBarTp";
import Mixins from './data/column'
import { isEmpty } from '@/utils/util'
import { mapState, mapMutations } from 'vuex'

function FEArr(arrayItem, parentItemIdArray, finalData) {
  let parentId = parentItemIdArray.shift()
  let parentIndex = arrayItem.findIndex((v) => v.id === parentId)

  if (parentItemIdArray.length > 0) {
    FEArr(arrayItem[parentIndex].subtasks, parentItemIdArray, finalData)
  } else if (parentItemIdArray.length === 0) {
    arrayItem[parentIndex].subtasks = finalData
    arrayItem[parentIndex].isExpand = true
  }
}
export default {
  name: 'RoleManagement',
  mixins: [Mixins],
  components: {
    // "side-bar-tp": sideBarTp
  },
  computed: {
    ...mapState(['roleRowInfo'])
  },
  data() {
    return {
      // 重置排序搜索
      resetConTemplate: function () {
        return {
          template: Vue.component('add-button', {
            template: `
              <span class='e-icons e-reset-con' @click="resetCondition">123123</span>
            `,
            data() {
              return {
                data: {},
                content: this.$t('刷新') //_this.tanslate('刷新')
              }
            },
            methods: {
              resetCondition: this.resetCondition
            }
          })
        }
      },
      toolbarOptionsArr: [],
      toolbar: [],
      selectionSettings: {
        checkboxMode: 'Default',
        type: 'Multiple',
        persistSelection: true
      },
      filterSettings: {
        type: 'Menu'
      },
      pageSettings: {
        pageSize: 10
      },
      inlineEditData: [],

      // 已选择的行数据
      selectRowData: [],

      // 是否开启新增 编辑权限抽屉
      isOpen: false,

      // 临时存储父对象
      tmpParentData: {},

      // 滑框的目的 add: 增加 edit： 编辑
      sidePageMethod: 'add',

      roleDescription: '',

      rules: [],
      showQueryBuilder: false
    }
  },
  methods: {
    ...mapMutations({
      setRoleRowInfo: 'setRoleRowInfo'
    }),

    handleQuerySearch() {
      this.rules = []
      let { rules = [] } = this.$refs.queryBuilder.$refs.ejsRef.getRules()
      console.log(rules)
      this.rules = rules
      // console.log(this.$refs.queryBuilder.$refs.ejsRef.getRule());
      this.pageInfo = {
        current: 1,
        size: 10
      }
      this.queryRoleListByPage()
    },
    handleQueryReset() {
      this.$refs.queryBuilder.$refs.ejsRef.reset()
      this.rules = []
      this.handleQuerySearch()
    },

    showSetting() {
      console.log(this.$refs.treeGrid)
      this.$refs.treeGrid.ejsRef.openColumnChooser(50, 50)
    },
    // 展开叶子节点
    // expanding(args) {
    //     console.log("expanding");
    //     let { data } = args;
    //     if (!data.leafFlag) {
    //         // args.data.id
    //         // let index = this.treePropsObj.data.findIndex(item => item.sourceOrderId == args.data.id)
    //         // this.treePropsObj.data[index].children = []
    //         // this.treePropsObj.data[index].children = [...data.list]
    //         // this.treePropsObj = Object.assign({}, this.treePropsObj)
    //     }
    // },
    // 一级新增
    addMainRoles() {
      this.sidePageMethod = 'add'
      // 清空缓存的夫对象
      this.tmpParentData = {}
      this.addRoles()
    },
    addRoles() {
      this.isOpen = true

      // 打开抽屉
      // this.$nextTick(() => {
      //     this.$refs.sideBarTp.isShow = true;
      // });

      this.$dialog({
        modal: () => import('./components/sideBarTp'),
        data: {
          sidePageMethod: this.sidePageMethod,
          tmpParentData: this.tmpParentData,
          isShow: true
        },
        success: () => {
          this.queryRoleListByPage()
        },
        close: () => {
          this.closeSideBar()
        }
      })
    },
    // 关闭侧边栏
    closeSideBar() {
      // 清空缓存的夫对象
      this.tmpParentData = {}
      console.log(this.tmpParentData)
      // 打开抽屉
      this.$nextTick(() => {
        // this.$refs.sideBarTp.isShow = false;
      })
    },
    add() {
      let refs = this.$refs.treeGrid
      console.log(refs)
    },

    childMap(DataArr, isSelect = true) {
      console.log(this.inlineEditData, isSelect)
      let idArr = []
      let indexArr = []

      function loopData(vArr, index = -1) {
        if (vArr.length === 0) return
        vArr.forEach((v) => {
          if (index === -1) {
            let beforeIndex = indexArr.slice(-1)[0]
            indexArr.push(++beforeIndex)
          }
          if (v.subtasks) {
            loopData(v.subtasks)
          }
        })
      }

      DataArr.forEach((v) => {
        indexArr.push(v.index)
        idArr.push(v)
        if (v.subtasks) {
          loopData(DataArr, v.index)
        }
      })

      console.log(indexArr)
      return indexArr
    },

    // 选中
    rowSelected() {
      // 未开启选择阻止触发，换页阻止触发
      // 获取全部数据
      let currData = this.$refs.treeGrid.ejsRef.getSelectedRecords()
      // console.log(currData, "treeGridSelected");

      // 递归测试 start
      // let selectRowIndex = this.childMap(currData, true)
      // this.$refs.treeGrid.ejsRef.selectCheckboxes(selectRowIndex)
      // 递归测试 end

      currData.forEach((item) => {
        if (!this.selectRowData.find((element) => element.id == item.id)) {
          this.selectRowData.push(item)
        }
      })
      this.selectRowData = [...this.selectRowData]
      // console.log(this.selectRowData);
    },

    // 取消选中
    rowDeselected(args) {
      let plantArr = this.plantTreeData(this.inlineEditData)
      console.log(plantArr)
      let rowIndex = args.rowIndex
      let deselectedId = plantArr[rowIndex].id
      let ind = this.selectRowData.findIndex((element) => element.id == deselectedId)
      if (ind > -1) {
        this.selectRowData.splice(ind, 1)
      }
      // console.log("selectRowData", this.selectRowData);
    },

    // 批量删除操作
    batchDeleteArr() {
      // 只要叶子节点就可以
      let ids = this.selectRowData.map((v) => {
        return v.id
      })

      if (!ids || ids.length === 0) {
        this.$toast({
          content: this.$t('请勾选一个角色')
        })
        return
      }

      this.deleteRow(ids, 'array')
    },

    /**
     * 查询角色列表
     * @params id
     */
    queryRoleList(id) {
      this.$API.roles
        .roleQuery({
          id: id
        })
        .then((res) => {
          console.log(res)
        })
    },

    // 扁平化数据
    plantTreeData(data) {
      let treeArr = []
      function setChild(treeArr, subtasks) {
        subtasks.forEach((v) => {
          treeArr.push(v)
          if (!!v.subtasks && v.subtasks.length > 0) {
            setChild(treeArr, v.subtasks)
          }
        })
      }
      setChild(treeArr, data)
      return treeArr
    },

    // 渲染树状数组
    renderTreeData(data) {
      let treeDataLv0 = data.filter((item) => item.treeLevel === 0)

      function getChildArr(parent) {
        parent.forEach((v) => {
          let childArr = data.filter((vc) => vc.parentId === v.id) || []

          if (childArr.length > 0) {
            v.subtasks = childArr
            getChildArr(childArr)
          }
        })
      }

      treeDataLv0.length > 0 && getChildArr(treeDataLv0)
      return treeDataLv0
    },

    /**
     * 分页查询角色限列表数据
     */
    queryRoleListByPage() {
      let queryData = {
        condition: '',
        defaultRules: [
          {
            condition: '',
            field: 'parentId',
            label: '',
            operator: 'equal',
            rules: [],
            type: '',
            value: 0
          }
        ],
        page: {
          current: this.pageSettings.pageCount || 1, // 有用
          desc: [],
          descs: [],
          maxLimit: 10,
          pages: 10,
          size: this.pageSettings.pageSize || 10, // 有用
          total: 0
        },
        pageFlag: false,
        rules: this.rules
      }

      this.$API.roles
        .roleQueryByPage(queryData)
        .then((result) => {
          if (result.code === 200 && !isEmpty(result.data) && !isEmpty(result.data.records)) {
            this.inlineEditData = result.data.records.map((v) => {
              return {
                ...v,
                isExpand: false, // 是否展开
                leafFlagF: !v.leafFlag
              }
            })
            this.$nextTick(() => {
              this.closeSideBar()
            })
          } else {
            this.inlineEditData = []
          }
          this.$hloading()
        })
        .catch(() => {
          this.$nextTick(() => {
            this.$hloading()
            // console.log(document.querySelector('.e-emptyrow'))
          })
        })
    },

    currentChange(page) {
      this.pageSettings.pageCount = page
      this.queryRoleListByPage()
    },
    handleCurrentChange(page) {
      console.log(page)
    },
    // 每页size改变
    handleGridSizeChange(e) {
      console.log('页码size改变，', e)
      // this[`tab${this.currentTabIndex}PageInfo`].size = e.count;
      // this.getTabData();
    },

    /**
     * 添加子角色
     */
    addChildRole(data = {}) {
      console.log('add')
      this.sidePageMethod = 'add'
      // 清空缓存的夫对象
      this.tmpParentData = {}
      // 缓存当前父列
      this.tmpParentData = data
      this.setRoleRowInfo(data)

      this.addRoles()
    },
    // 编辑行
    editRow(data = {}) {
      console.log('edit')
      // 清空缓存的夫对象
      this.tmpParentData = {}
      // 缓存当前父列
      this.tmpParentData = data
      this.setRoleRowInfo(data)

      this.sidePageMethod = 'edit'
      this.addRoles()
    },
    // 删除行
    deleteRow(data = {}, type = 'single') {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('删除叶子节点将删除其子节点，确认删除该角色吗？')
        },
        close: () => {},
        success: () => {
          this.deleteRowFun(data, type)
        }
      })
    },

    // 删除的接口
    deleteRowFun(data, type) {
      let query = [data.id]
      if (type === 'array') {
        query = data
      }
      this.$API.roles
        .batchDelete({
          ids: query
        })
        .then((result) => {
          if (result.code === 200) {
            this.$toast({
              type: 'success',
              content: result.msg
            })

            this.$nextTick(() => {
              this.queryRoleListByPage()
            })
          } else {
            this.$toast({
              content: result.msg
            })
          }
        })
        .catch(() => {})
    },

    getChildRoles(data) {
      this.$API.roles
        .criteriaQuery({
          parentId: data.id
        })
        .then((res) => {
          if (res.code === 200 && !isEmpty(res.data)) {
            let parentItemIdArray =
              !!data.treePath && data.treePath.indexOf(',') > 0
                ? data.treePath.split(',')
                : data.treePath
                ? [data.treePath]
                : []
            parentItemIdArray.push(data.id)

            FEArr(this.inlineEditData, parentItemIdArray, res.data)

            // 子子的话 需要递归查询
            // this.inlineEditData[index].subtasks = res.data;
            // this.inlineEditData[index].isExpand = true;
            this.inlineEditData = JSON.parse(JSON.stringify(this.inlineEditData))
            this.$forceUpdate()
          }
        })
    },
    /**
     * 分页查询角色限列表数据
     */
    queryRoleListByPage2(data) {
      let queryData = {
        condition: '',
        defaultRules: [
          {
            condition: '',
            field: 'parentId',
            label: '',
            operator: 'equal',
            type: '',
            value: data.id
          }
        ],
        page: {
          current: this.pageSettings.pageCount || 1, // 有用
          desc: [],
          descs: [],
          maxLimit: 10,
          pages: 10,
          size: this.pageSettings.pageSize || 10, // 有用
          total: 0
        },
        pageFlag: false,
        rules: []
      }

      this.$API.roles
        .roleQueryByPage(queryData)
        .then((result) => {
          console.log(result)
        })
        .catch(() => {
          this.$nextTick(() => {
            // console.log(document.querySelector('.e-emptyrow'))
          })
        })
    },
    // 弹框展示角色详情
    showDetail(data) {
      this.roleDescription = data.roleDescription
      this.$refs.dialogPop.ejsRef.show()
    },

    filterRoles() {
      this.showQueryBuilder = !this.showQueryBuilder
    },

    refreshGrid() {
      this.$refs.treeGrid.refresh()
    }
  },
  created() {
    // 查询角色列表
    this.$loading()
    this.queryRoleListByPage()
  }
}
</script>

<style lang="scss" scoped>
.role-container {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border: 1px solid #e8e8e8;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  overflow: hidden;
  height: 100%;
  padding: 0 20px;
  background: #fff;
  display: flex;
  flex-direction: column;

  .tree-grid-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .flex1 {
    flex: 1;
  }

  .toolbar {
    width: 100%;
    height: 50px;
    line-height: 50px;
    justify-content: space-between;
    padding: 0 10px;

    .left-box {
      flex: 1;
      justify-content: flex-start;
      .tool-item {
        margin-left: 20px;
      }
      .tool-item:nth-child(1) {
        margin-left: 0;
      }
    }

    .right-box {
      flex: 1;
      justify-content: flex-end;
      .tool-item {
        margin-right: 20px;
      }
      .tool-item:last-child {
        margin-right: 0;
      }
    }

    .tool-item {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: #9baac1;
      cursor: pointer;
      span {
        display: inline-block;
        color: #212d3d;
        margin-left: 6px;
      }
    }
  }

  .main-grid {
    overflow-x: auto;
  }
}

.role-container /deep/.e-grid {
  border-radius: 8px;
  overflow: hidden;
}

.role-container /deep/.e-grid .e-gridheader,
.role-container /deep/.e-grid .e-gridheader tr:first-child th {
  background: #fff;
}

.role-container .main-grid /deep/.e-grid th.e-headercell:nth-child(1) {
  background: #fff !important;
  border-right: 0 !important;
}

.role-container .main-grid /deep/ .e-treegridexpand:hover::before,
.role-container .main-grid /deep/ .e-treegridcollapse:hover::before {
  color: #9daabf;
}

.role-container .main-grid /deep/ .e-treegridexpand,
.role-container .main-grid /deep/ .e-treegridcollapse {
  color: #9daabf;
}

.role-container .main-grid /deep/.e-checkbox-wrapper .e-frame.e-check,
.role-container .main-grid /deep/ .e-css.e-checkbox-wrapper .e-frame.e-check {
  background-color: #00469c !important;
}

.role-container /deep/.e-grid .e-headercell {
  color: #161f2b;
  font-weight: 500;
}

.role-container /deep/.e-grid .e-rowcell {
  color: #35404e;
}

.role-container /deep/.e-grid .e-frame {
  width: 16px !important;
  height: 16px !important;
  border: 1px solid #9daabf !important;
}

.role-container /deep/.e-grid .e-headercell {
  font-weight: 500;
  color: #161f2b;
}

.role-container /deep/.e-grid .e-row {
  height: 50px !important;
}

.role-container /deep/.e-grid .e-treecolumn-container {
  color: #35404e;
  font-weight: 600;
}

.role-container /deep/.e-grid .e-gridheader,
.e-grid .e-gridheader tr:first-child th {
  border-radius: 8px 8px 0 0;
}

.role-container /deep/.e-grid .e-headertext {
  font-weight: 500;
  color: #161f2b;
}

.role-container /deep/ .disabled {
  align-items: center;
  color: #b9babc;
  .green {
    display: block;
    width: 6px;
    height: 6px;
    background: #b9babc;
    border-radius: 100%;
    margin-right: 4px;
  }
}

.role-container /deep/.block-item {
  display: inline-block;
  position: relative;
  left: -20px;
}

.role-container /deep/.block-item i.mt-icon-arrowhead-right {
  display: inline-block;
  vertical-align: middle;
  color: #9daabf;
}

.role-container /deep/ .openly {
  align-items: center;
  color: #59d83b;
  .green {
    display: block;
    width: 6px;
    height: 6px;
    background: #59d83b;
    color: #161f2b;
    border-radius: 100%;
    margin-right: 4px;
  }
}

.role-container /deep/ .todo-list-column {
  color: #6386c1;
  font-size: 14px;
}

.role-container /deep/ .operator-btn {
  color: #6386c1;
  font-size: 14px;
  padding-right: 20px;
  cursor: pointer;
}

.role-container /deep/ .operator-btn:hover {
  font-weight: 600;
}

.dialog-box {
  .dialog-warp {
    margin-top: 20px;
    width: 860px;
    height: 90%;
    background: linear-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)),
      linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 1));
    border-radius: 8px 8px;
    display: flex;
    flex-direction: column;

    .dialog-inner-title {
      height: 50px;
      line-height: 50px;
      background: linear-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)),
        linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 1));
      border: 1px solid rgba(232, 232, 232, 1);
      border-radius: 8px 8px 0 0;

      font-size: 16px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(41, 41, 41, 1);
      padding: 0 20px;
    }
  }
  .dialog-des {
    border-left: 1px solid #e8e8e8;
    border-right: 1px solid #e8e8e8;
    border-bottom: 1px solid #e8e8e8;
    padding: 10px 20px 20px;
    height: 100%;
    overflow: auto;
    .user-list {
      height: 14px;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(41, 41, 41, 1);
      margin-top: 20px;
    }
  }
}
</style>
<style lang="scss">
.role-container {
  .grid-wraps {
    display: flex;
    flex-direction: column;
    height: 100%;
    .e-treegrid {
      flex: 1;
      border: 1px solid #e0e0e0;
      border-radius: 8px 8px 8px 8px;
    }
    .e-grid {
      border-right: 0;
      border-left: 0;
    }
    .e-gridheader {
      border-left: none;
      border-right: none;
      border-top: none;
    }
    .mt-pagertemplate {
      margin: 0;
      padding: 10px 0;
      border-top: 1px solid #e8e8e8;
    }
  }
}
</style>
