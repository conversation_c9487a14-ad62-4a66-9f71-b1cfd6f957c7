<template>
  <div class="side-bar-box">
    <!-- <side-bar :show="isShow"> -->
    <div class="side-content">
      <div class="title-banner mt-flex">
        <div class="title-txt">{{ $t('新增/编辑') }}</div>
        <div class="close" @click="closeSideBar()">
          <mt-icon name="close" />
        </div>
      </div>

      <div class="setting-box">
        <div class="create-banner mt-flex">
          <div class="banner-title">{{ $t('创建角色') }}</div>
          <template v-if="isUse">
            <div class="status-btn" @click="useRole(0)">{{ $t('启用') }}</div>
          </template>
          <template v-else>
            <div class="status-btn" @click="useRole(1)">{{ $t('停用') }}</div>
          </template>
        </div>

        <!-- 输入的form表单 -->
        <div class="creator-form">
          <mt-form ref="ruleFormSubmit" :model="ruleFormSubmit" :rules="rulesSubmit">
            <mt-row :span="24">
              <mt-col :span="12">
                <mt-form-item prop="roleName">
                  <mt-row align="middle" type="flex">
                    <mt-col :span="6">
                      <label class="input-label" for="roleName">角色名称:</label>
                    </mt-col>
                    <mt-col :span="18">
                      <mt-input
                        v-model="ruleFormSubmit.roleName"
                        class="input-item"
                        css-class="e-outline"
                        :show-clear-button="true"
                        type="text"
                        :placeholder="$t('请输入角色名称')"
                      ></mt-input>
                    </mt-col>
                  </mt-row>
                </mt-form-item>
              </mt-col>

              <mt-col :span="12">
                <mt-form-item prop="roleCode">
                  <mt-row align="middle" type="flex">
                    <mt-col :span="6">
                      <label class="input-label" for="roleCode">角色编码:</label>
                    </mt-col>
                    <mt-col :span="18">
                      <mt-input
                        v-model="ruleFormSubmit.roleCode"
                        class="input-item"
                        css-class="e-outline"
                        :show-clear-button="true"
                        type="text"
                        :placeholder="$t('请输入角色编码')"
                        :disabled="editDisabled"
                      ></mt-input>
                    </mt-col>
                  </mt-row>
                </mt-form-item>
              </mt-col>
            </mt-row>

            <mt-row class="sec-row">
              <mt-col :span="3">
                <label class="input-label" for="roleDescription">角色描述:</label>
              </mt-col>
              <mt-col :span="21">
                <mt-input
                  v-model="ruleFormSubmit.roleDescription"
                  :multiline="true"
                  :rows="4"
                  class="input-item"
                  css-class="e-outline"
                  :show-clear-button="true"
                  type="text"
                  :placeholder="$t('请输入角色描述')"
                ></mt-input>
              </mt-col>
            </mt-row>

            <mt-row :gutter="20" class="sec-row">
              <mt-col :span="12">
                <mt-row :gutter="5" align="middle" type="flex">
                  <mt-col :span="6">创建人：</mt-col>
                  <mt-col :span="18">{{ ruleFormSubmit.creatorName }}</mt-col>
                </mt-row>
              </mt-col>
              <mt-col :span="12">
                <mt-row :gutter="5" align="middle" type="flex">
                  <mt-col :span="6">创建时间：</mt-col>
                  <mt-col :span="18">
                    {{ ruleFormSubmit.creatorTime }}
                  </mt-col>
                </mt-row>
              </mt-col>
            </mt-row>
          </mt-form>
        </div>

        <!-- tab grid -->
        <div class="tab-box mt-flex">
          <div class="left-tabs mt-flex">
            <div class="tab-item" :class="{ active: tabId === 0 }" @click="selectTab(0)">
              分配用户
            </div>
            <div class="tab-item" :class="{ active: tabId === 1 }" @click="selectTab(1)">
              功能权限
            </div>
            <div class="tab-item" :class="{ active: tabId === 2 }" @click="selectTab(2)">
              字段权限
            </div>
          </div>
          <div class="right-btn" @click="newAdd" v-if="tabId === 0">
            <mt-icon name="M_Expand" />新增分配
          </div>
        </div>
        <div class="tab-content">
          <template v-if="tabId === 0">
            <assignUser ref="assignUser" :role-id="modalData.tmpParentData.id"></assignUser>
          </template>
          <template v-else-if="tabId === 1">
            <assignAuthority></assignAuthority>
          </template>
          <template v-else-if="tabId === 2">
            <FieldPermissions></FieldPermissions>
          </template>
        </div>
      </div>

      <!-- 底部关闭按钮 -->
      <div class="btn-box">
        <div class="btn-item" @click="closeSideBar">{{ $t('取消') }}</div>
        <div class="btn-item" @click="submitRole">{{ $t('确定') }}</div>
      </div>
    </div>
    <!-- </side-bar> -->
  </div>
</template>

<script>
import assignUser from './assignUser'
// const assignUser from "" = () => import("./assignUser");
const assignAuthority = () => import('./assignAuthority')
const FieldPermissions = () => import('./FieldPermissions')
import { formatDate } from '@/utils/util'
// import sideBar from "../../../components/sideBar";
export default {
  components: {
    // sideBar,
    assignUser,
    assignAuthority,
    FieldPermissions
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  mounted() {
    console.log(this.modalData)
    this.editDisabled = false
    if (this.sidePageMethod === 'edit') {
      this.editDisabled = true
      this.ruleFormSubmit = Object.assign(this.ruleFormSubmit, this.modalData.tmpParentData)
      console.log(this.ruleFormSubmit)
    } else if (this.sidePageMethod != 'edit') {
      console.log('reset input inline')
      this.ruleFormSubmit = {
        roleName: '',
        roleCode: '',
        creatorName: 'test',
        roleDescription: '',
        creatorTime: formatDate(new Date())
      }
    }
    // 获取用户的列表
    this.$nextTick(() => {
      this.$refs.assignUser.getUserQueryFun()
    })
  },
  computed: {
    sidePageMethod() {
      return this.modalData.sidePageMethod
    },
    isShow() {
      return this.modalData.isShow
    }
  },
  watch: {
    'modalData.isShow'(nv) {
      this.editDisabled = false
      if (nv && this.sidePageMethod === 'edit') {
        this.editDisabled = true
        this.ruleFormSubmit = Object.assign(this.ruleFormSubmit, this.modalData.tmpParentData)
        console.log(this.ruleFormSubmit)
      } else if (this.sidePageMethod != 'edit') {
        console.log('reset input inline')
        this.ruleFormSubmit = {
          roleName: '',
          roleCode: '',
          creatorName: 'test',
          roleDescription: '',
          creatorTime: formatDate(new Date())
        }
      }
      // 获取用户的列表
      if (nv) {
        this.$refs.assignUser.getUserQueryFun()
      }
    }
  },
  data() {
    return {
      editDisabled: false, // 编辑的时候 角色编码不能编辑 置灰
      // isShow: false,
      isUse: false,
      ruleFormSubmit: {
        roleName: '',
        roleCode: '',
        creatorName: 'test',
        roleDescription: '',
        creatorTime: formatDate(new Date())
      },
      rulesSubmit: {
        roleName: [
          {
            required: true,
            message: this.$t('请输入角色名称'),
            trigger: 'blur'
          },
          {
            min: 3,
            max: 15,
            message: '长度在 3 到 15 个字符',
            trigger: 'blur'
          }
        ],
        roleCode: [
          {
            required: true,
            message: this.$t('请输入角色编码'),
            trigger: 'blur'
          }
        ],
        roleDescription: [
          {
            required: false,
            message: this.$t('请输入角色描述'),
            trigger: 'blur'
          },
          {
            min: 2,
            max: 200,
            message: '长度在 2 到 200 个字符',
            trigger: 'blur'
          }
        ]
      },
      tabId: 0
    }
  },
  methods: {
    useRole(isUse) {
      this.isUse = isUse
    },
    selectTab(id) {
      this.tabId = id
    },
    newAdd() {
      this.$refs.assignUser.popDialogTransfer()
    },
    closeSideBar() {
      this.$emit('cancel-function')
    },
    closeDialog() {
      console.log('out closedialog')
      this.$refs.dialog.ejsRef.show()
    },
    // 打开详情dialog 阻止底部的sidebar手势关闭
    // openDetailDialog() {
    //     this.isEnableGestures = false;
    // },

    /**
     * 确认新增角色
     */
    submitRole() {
      let { sidePageMethod, tmpParentData } = this.modalData
      let queryData = {}
      let ajaxFun = this.$API.roles.roleAdd

      let { roleCode, roleName, roleDescription = '' } = this.ruleFormSubmit
      let { id = '', sn = '' } = tmpParentData

      let query = {
        applicationId: '20691546527256611', // 来自登录信息
        roleCode: roleCode,
        roleDescription: roleDescription,
        roleName: roleName,
        parentId: 0
      }
      if (!!id || !!sn) {
        query = {
          ...query,
          parentId: id,
          sn
        }
      }

      if (sidePageMethod === 'add') {
        ajaxFun = this.$API.roles.roleAdd
        queryData = query
      } else {
        ajaxFun = this.$API.roles.updataRoles
        queryData = {
          id,
          roleDescription: roleDescription || '',
          roleName: roleName || '',
          sn
        }
      }

      ajaxFun(queryData).then((result) => {
        if (result.code === 200) {
          this.$toast({
            content: result.msg,
            type: 'success'
          })
          // 更新
          this.$nextTick(() => {
            this.$emit('confirm-function', {})
            // this.$parent.queryRoleListByPage();
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.side-bar-box {
  // max-width: 50%;
  // position: relative;
  // z-index: 1001;

  background: rgba(0, 0, 0, 0.2);
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1001;
}

.side-bar-box /deep/.mt-form-item {
  width: 100%;
  margin-bottom: 0;
}

.side-bar-box /deep/.input-label {
  padding-left: 5px;
}

.side-bar-box /deep/.mt-row {
  width: 100%;
}

.side-bar-box /deep/.mt-col {
  font-size: 14px;
}

.side-bar-box /deep/.side-content {
  width: 800px;
  height: 100vh;
  position: fixed;
  top: 0;
  right: 0;
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 8px 0 0 8px;
  // box-shadow: 0 2px 6px 0 rgba(0,0,0,6%);

  padding-bottom: 60px;
  padding-top: 60px;
  overflow: auto;

  // span.e-error {
  //     display: none;
  // }

  .mt-form-item__content {
    flex: 1;
  }

  span.e-error.error-label {
    display: block;
  }

  .title-banner {
    width: 800px;
    height: 60px;
    line-height: 60px;
    padding: 0 20px 0 30px;
    background: rgba(243, 243, 243, 1);
    border-radius: 8px 0 0 0;
    justify-content: space-between;
    align-items: center;
    position: fixed;
    top: 0;
    right: 0;
    z-index: 999;

    .title-txt {
      font-size: 16px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(41, 41, 41, 1);
    }
    .close {
      cursor: pointer;
    }
  }

  .setting-box {
    padding: 20px;

    .create-banner {
      width: auto;
      height: 14px;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(41, 41, 41, 1);
      border-left: 2px solid #00469c;
      border-radius: 2px;
      padding-left: 10px;
      margin-top: 10px;
      justify-content: space-between;
      align-items: center;

      .status-btn {
        height: 20px;
        line-height: 20px;
        padding: 0 10px;
        font-weight: 500;
        color: #00469c;
        background: #f6f8fc;
        border: 1px solid rgba(0, 70, 156, 0.06);
        border-radius: 4px;
        text-align: center;
        box-sizing: border-box;
        font-size: 14px;
        font-family: PingFangSC;
        color: #9a9a9a;
        cursor: pointer;
      }
      .status-btn:hover {
        opacity: 0.8;
      }
    }

    .creator-form {
      width: auto;
      padding-top: 26px;

      .form-row {
        width: 100%;
        .form-item {
          flex: 1;
          align-items: center;

          .input-label {
            width: 50px;
            height: 46px;
            line-height: 46px;
          }
          .e-outline {
            flex: 1;
          }
        }
      }
      .sec-row {
        margin-top: 20px;
      }
    }
    .tab-box {
      width: 100%;
      margin-top: 20px;
      margin-bottom: 20px;
      height: 26px;
      line-height: 26px;
      justify-content: space-between;

      .tab-item {
        width: 100px;
        text-align: center;
        height: 26px;
        line-height: 26px;
        box-sizing: border-box;
        font-size: 14px;
        font-family: PingFangSC;
        color: #9a9a9a;
        cursor: pointer;
      }
      div.tab-item.active {
        font-weight: 500;
        color: #00469c;
        background: rgba(246, 248, 252, 1);
        border: 1px solid rgba(0, 70, 156, 0.06);
        border-radius: 4px;
      }
      .right-btn {
        i {
          margin-right: 6px;
        }
      }
    }
  }

  .btn-box {
    position: fixed;
    bottom: 0;
    right: 0;
    width: 800px;
    height: 60px;
    line-height: 60px;
    border-radius: 0 0 0 8px;
    box-shadow: inset 0 1px 0 0 rgba(232, 232, 232, 1);
    display: inline-flex;
    justify-content: flex-end;
    background: #fff;

    .btn-item {
      cursor: pointer;
      height: 60px;
      line-height: 60px;
      padding-left: 30px;
      padding-right: 30px;
      font-size: 14px;
      color: #0043a8;
    }
  }
}
</style>
