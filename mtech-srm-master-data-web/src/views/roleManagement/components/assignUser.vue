<template>
  <div class="assign-container">
    <!-- locale="zh" -->
    <mt-data-grid
      ref="dataGrid"
      :data-source="dataSource"
      :column-data="columnData"
      :allow-paging="false"
      gird-lines="Horizontal"
      grid-lines="Horizontal"
      :allow-sorting="true"
      :allow-filtering="true"
      :filter-settings="filterSettings"
    ></mt-data-grid>
    <!-- 查看详情的弹框 -->
    <mt-dialog ref="dialog" css-class="dialog-box" :header="$t('查看详情')" width="900">
      <div class="dialog-warp">
        <div class="dialog-inner-title">{{ $t('用户详情') }}</div>
        <div class="dialog-des">
          <div v-for="item in detailDialogData" :key="item" class="user-list">
            {{ item }}
          </div>
        </div>
      </div>
    </mt-dialog>

    <!-- 编辑的弹框 -->
    <mt-dialog-transfer
      :left-title="$t('类型名称')"
      :right-title="userGroupType.userGroupTypeName"
      :visible="showDialogTransfer"
      :fields-left="filedsIcon"
      :fields-right="filedsIcon2"
      :close="closeTs"
      @save="saveDT"
      @serchData="serchDataDT"
    >
      <template #searchLeft>
        <div class="select-box mt-flex">
          <div class="select-wrap">
            <!-- <select v-model="userGroupType.userGroupTypeCode" @change="groupTypeChange">
                        <option disabled value="">{{ $t("请选择") }}</option>
                        <option value="PR001USER">{{ $t("用户") }}</option>
                        <option value="PR002STATION">{{ $t("岗位") }}</option>
                        <option value="PR003DEPART">{{ $t("部门") }}</option>
                    </select> -->
            <mt-select
              :width="100"
              :data-source="userGroupTypeCodeArr"
              :show-clear-button="false"
              :allow-filtering="true"
              @change="groupTypeChange"
              v-model="userGroupType.userGroupTypeCode"
              :placeholder="$t('请选择')"
            ></mt-select>
          </div>
          <div class="search-box">
            <mt-DropDownTree
              id="filter"
              :fields="filedsIcon"
              filter-bar-placeholder="Search"
              :allow-filtering="true"
              :placeholder="$t('输入关键字搜索')"
              @select="selectItem"
            ></mt-DropDownTree>
          </div>
        </div>
      </template>
    </mt-dialog-transfer>
  </div>
</template>

<script>
import Vue from 'vue'
import { isEmpty } from '@/utils/util'
import { mapState } from 'vuex'

export default {
  computed: {
    ...mapState(['roleRowInfo'])
  },
  props: {
    roleId: {
      type: String,
      default: () => {
        return 0
      }
    }
  },
  data() {
    const _this = this
    return {
      showDialogTransfer: false,
      //左边树状图初始数据
      filedsIcon: {
        dataSource: [],
        id: 'id',
        text: 'name',
        child: 'children',
        iconCss: 'icon',
        imageUrl: 'image',

        value: 'id'
      },
      //右边暂存框初始化数据
      filedsIcon2: {
        dataSource: []
      },
      // 用户 岗位 部门三行都是空的 true
      assignUserWithnoData: [],

      detailDialogData: [],
      filterSettings: { type: 'Menu', hierarchyMode: 'Both' },
      dataSource: [],
      columnData: [
        {
          field: 'userGroupTypeName',
          headerText: this.$t('分配类型'),
          textAlign: 'left'
        },
        {
          field: 'userGroupContent',
          headerText: this.$t('类型名称'),
          textAlign: 'left'
        },
        {
          field: 'updateUserName',
          headerText: this.$t('更新人'),
          textAlign: 'left'
        },
        {
          field: 'updateTime',
          headerText: this.$t('更新时间'),
          textAlign: 'left'
        },
        {
          field: 'operator',
          headerText: this.$t('操作'),
          textAlign: 'left',
          width: '150',
          template: () => {
            return {
              template: Vue.component('todo-column-template', {
                template: `
                                  <div class="todo-list-column mt-flex jce">
                                    <div class="operator-btn" @click.prevent.stop="editUser()">{{ $t("编辑") }}</div>
                                    <div class="operator-btn" @click.prevent.stop="deleteRow()">{{ $t("删除") }}</div>
                                    <div class="operator-btn" @click.prevent.stop="dialogDetail()">{{ $t("详情") }}</div>
                                  </div>`,
                data: function () {
                  return {}
                },
                methods: {
                  editUser() {
                    let { data } = this
                    _this.userGroupInfo = data
                    _this.popDialogTransfer(data.userGroupTypeName)
                  },
                  dialogDetail() {
                    let { data } = this
                    let typeNameArrStr = data.userGroupContent
                    let userGroupTypeName = data.userGroupTypeName
                    if (!typeNameArrStr) {
                      this.$toast({
                        content: `您还没添加${userGroupTypeName}，请编辑添加！`
                      })
                      return
                    }
                    _this.detailDialogData =
                      !!typeNameArrStr && typeNameArrStr.indexOf(',') >= 0
                        ? typeNameArrStr.split(',')
                        : [typeNameArrStr]

                    this.$nextTick(() => {
                      _this.$refs.dialog.ejsRef.show()
                    })
                  },
                  deleteRow() {
                    let { data } = this
                    let userGroupTypeName = data.userGroupTypeName
                    this.$dialog({
                      data: {
                        title: this.$t('删除'),
                        message: this.$t(
                          `删除将删除${userGroupTypeName}下的所有勾选信息，确认删除吗？`
                        )
                      },
                      success: () => {
                        // 删除接口
                        this.deleteRowFun(data.id)
                      }
                    })
                  },
                  deleteRowFun(id) {
                    this.$API.roles
                      .getUserDelete({
                        ids: [id]
                      })
                      .then((result) => {
                        if (result.code === 200) {
                          this.$toast({
                            type: 'success',
                            content: this.$t('删除成功')
                          })
                          this.$nextTick(() => {
                            _this.getUserQueryFun()
                          })
                        } else {
                          this.$toast({
                            content: this.$t('删除失败，请重试！')
                          })
                        }
                      })
                  }
                }
              })
            }
          }
        }
      ],
      // 新增类型
      userGroupType: {
        userGroupTypeName: '',
        orgLevelCode: '',
        userGroupTypeCode: ''
      },
      // 点击编辑的用户组信息
      userGroupInfo: {},
      // 用户 岗位 部门 里面详细的用户的信息
      userGroupContentList: [],
      userGroupTypeCodeArr: [
        { text: this.$t('用户'), value: 'PR001USER' },
        { text: this.$t('岗位'), value: 'PR002STATION' },
        { text: this.$t('部门'), value: 'PR003DEPART' }
      ]
    }
  },
  methods: {
    selectItem(item) {
      console.log(item)
    },
    closeTs() {
      this.showDialogTransfer = false
    },
    // 获取三行数据
    getUserQueryFun() {
      this.$API.roles
        .getUserQuery({
          condition: '',
          defaultRules: [
            {
              field: 'role_id',
              value: this.roleId,
              operator: 'equal'
            }
          ],
          page: {
            current: 1,
            maxLimit: 0,
            pages: 0,
            records: [],
            searchCount: true,
            size: 10,
            total: 10
          },
          pageFlag: false,
          rules: []
        })
        .then((result) => {
          if (result.code === 200 && !isEmpty(result.data) && !isEmpty(result.data.records)) {
            this.dataSource = result.data.records
            // 存储对应的 三中code 没创建的行 没有code
            this.assignUserWithnoData = this.dataSource.map((v) => v.userGroupTypeCode)
          } else {
            this.dataSource = []
            // 用户列表没有数据（没有三行数据 要新建三行数据）
            this.assignUserWithnoData = []
          }
        })
    },

    // 弹框transfer
    // data 编辑的时候弹框 带上行数据data
    popDialogTransfer(userGroupTypeName = this.$t('用户')) {
      let orgLevelCode = ''
      let userGroupTypeCode = ''
      if (userGroupTypeName === this.$t('用户')) {
        orgLevelCode = 'ORG05'
        userGroupTypeCode = 'PR001USER'
      }
      if (userGroupTypeName === this.$t('部门')) {
        orgLevelCode = 'ORG03'
        userGroupTypeCode = 'PR003DEPART'
      }
      if (userGroupTypeName === this.$t('岗位')) {
        orgLevelCode = 'ORG04'
        userGroupTypeCode = 'PR002STATION'
      }
      this.userGroupType = {
        userGroupTypeName: userGroupTypeName,
        orgLevelCode: orgLevelCode,
        userGroupTypeCode: userGroupTypeCode
      }

      this.getTreeData()
    },

    // 渲染右侧树形数据
    renderFileds2(treeArray) {
      // 层级类型
      let orgLeveLTypeCode = this.userGroupType.orgLevelCode

      let checkRightArr = []
      function loopChild(itemCode, array) {
        for (let i = 0; i < array.length; i++) {
          let arrItem = array[i]
          if (arrItem.orgLeveLTypeCode === orgLeveLTypeCode) {
            if (arrItem.id === itemCode) {
              // 只有名称（会不会导致重复？）
              checkRightArr.push(arrItem)
              arrItem.isChecked = true
            } else {
              arrItem.isChecked = false
            }
          }
          // 继续循环children数组
          if (!!arrItem.children && !isEmpty(arrItem.children)) {
            arrItem.expanded = true
            loopChild(itemCode, arrItem.children)
          }
        }
      }

      this.userGroupContentList.forEach((v) => {
        loopChild(v.itemCode, treeArray)
      })

      this.filedsIcon2 = Object.assign({}, { dataSource: checkRightArr })
    },

    // 获取树状数据
    getTreeData() {
      // this.showDialogTransfer = true  //// 测试数据
      this.$loading()
      // 获取当前行的userGroupContent详细用户信息
      const userGroupCriteriaQuery = this.userGroupCriteriaQuery()
      const getStatedLimitTree = this.getStatedLimitTree()
      Promise.all([userGroupCriteriaQuery, getStatedLimitTree])
        .then((result) => {
          this.$hloading()
          console.log(result)
          let userGroupResult = result[0]
          let treeData = result[1]
          if (userGroupResult.code === 200 && !isEmpty(userGroupResult.data)) {
            this.userGroupContentList = userGroupResult.data
          }
          if (treeData.code === 200 && !isEmpty(treeData.data)) {
            // 组织右侧数状数据
            this.renderFileds2(treeData.data)
            // 穿梭框的数据
            this.filedsIcon = Object.assign({}, this.filedsIcon, {
              dataSource: treeData.data
            })

            // 弹穿梭框
            !this.showDialogTransfer &&
              this.$nextTick(() => {
                this.showDialogTransfer = true
              })
          } else {
            this.$toast({
              content: '获取数据失败，请重试!'
            })
          }
        })
        .catch((e) => {
          console.log(e)
          this.$hloading()
          this.$toast({
            content: '获取数据失败，请重试!'
          })
        })
    },
    // 获取点击 行的content中的用户信息  itemCode 匹配 tree中的 id
    userGroupCriteriaQuery() {
      if (!this.userGroupInfo.userGroupContent) {
        return Promise.resolve({})
      }
      return this.$API.roles.userGroupCriteriaQuery({
        roleId: this.roleId,
        tenantId: 100100,
        userGroupId: this.userGroupInfo.id,
        userGroupTypeCode: this.userGroupType.userGroupTypeCode
      })
    },
    getStatedLimitTree() {
      return this.$API.roles.getStatedLimitTree({
        orgLevelCode: this.userGroupType.orgLevelCode,
        tenantId: 100100, // 租户id
        orgType: 'ORG001ADM'
      })
    },

    // 是否已经注册过 用户、部门、岗位
    getRowGroupType(userGroupTypeCode) {
      console.log(this.roleId)
      if (
        this.assignUserWithnoData.length === 0 ||
        this.assignUserWithnoData.indexOf(userGroupTypeCode) === -1
      ) {
        // 新增空行接口
        return this.$API.roles
          .UserGroupAdd({
            applicationId: 0,
            roleId: this.roleId,
            tenantId: 100100,
            userGroupContent: '',
            userGroupTypeCode: this.userGroupType.userGroupTypeCode
          })
          .then((result) => {
            if (result.code === 200 && !isEmpty(result.data)) {
              // 增加新的 userGroupTypeCode
              this.assignUserWithnoData.push(userGroupTypeCode)
              return Promise.resolve(result.data)
            } else {
              this.$toast({
                content: this.$t('新增失败，请重试！')
              })
              return Promise.reject()
            }
          })
      } else {
        let index = this.dataSource.findIndex((v) => v.userGroupTypeCode === userGroupTypeCode)
        return Promise.resolve(this.dataSource[index])
      }
    },

    //返回值为右边的暂存框数据  // userGroupTypeCode 分类： PR001USER：用户，PR002STATION：岗位，PR003DEPART：部门
    async saveDT(data) {
      let userGroupTypeCode = this.userGroupType.userGroupTypeCode
      this.showDialogTransfer = false
      let lineInfo = await this.getRowGroupType(userGroupTypeCode)
      let queryData = data.map((v) => {
        return {
          itemCode: v.orgCode, // 勾选的code数组
          itemId: v.id, // 勾选的code数组
          itemName: v.name, //勾选的name数组
          roleId: this.roleId,
          tenantId: 100100,
          userGroupId: lineInfo.id,
          userGroupTypeCode: userGroupTypeCode
        }
      })

      // 添加用户 部门 岗位 里面的详情 接口
      this.$API.roles
        .userGroupItemAdd({
          items: queryData
        })
        .then((result) => {
          console.log(result)
          if (result.code === 200) {
            this.$toast({
              type: 'success',
              content: this.$t('新增成功！')
            })
            this.$nextTick(() => {
              // 重新获取三行数据
              this.getUserQueryFun()
            })
          }
        })
        .catch((e) => {
          console.log(e)
        })
    },

    groupTypeChange(userGroupTypeCode) {
      let { value } = userGroupTypeCode
      let userGroupTypeName = ''
      if (value === 'PR001USER') {
        userGroupTypeName = this.$t('用户')
      }
      if (value === 'PR003DEPART') {
        userGroupTypeName = this.$t('部门')
      }
      if (value === 'PR002STATION') {
        userGroupTypeName = this.$t('岗位')
      }
      this.popDialogTransfer(userGroupTypeName)
    },

    serchDataDT(data) {
      console.log(data)
      //调用接口搜索data
    }
  },
  created() {
    this.getUserQueryFun()
  }
}
</script>

<style lang="scss" scoped>
.assign-container {
  width: auto;
  max-width: 50vw;
}
.assign-container /deep/ .operator-btn {
  color: #6386c1;
  font-size: 14px;
  padding-right: 10px;
  cursor: pointer;
}
.assign-container /deep/ .e-templatecell {
  padding-left: 0 !important;
  padding-right: 0 !important;
}
.assign-container /deep/ .todo-list-column {
  color: #6386c1;
  font-size: 14px;
  display: flex;
  justify-content: space-evenly;
}
.assign-container /deep/ .operator-btn {
  color: #6386c1;
  font-size: 14px;
  margin-right: 0;
  cursor: pointer;
}

.dialog-box {
  .dialog-warp {
    margin-top: 20px;
    width: 860px;
    height: 100%;
    background: linear-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)),
      linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 1));
    border-radius: 8px 8px;
    display: flex;
    flex-direction: column;

    .dialog-inner-title {
      height: 50px;
      line-height: 50px;
      background: linear-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)),
        linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 1));
      border: 1px solid rgba(232, 232, 232, 1);
      border-radius: 8px 8px 0 0;

      font-size: 16px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(41, 41, 41, 1);
      padding: 0 20px;
    }
  }
  .dialog-des {
    border-left: 1px solid #e8e8e8;
    border-right: 1px solid #e8e8e8;
    border-bottom: 1px solid #e8e8e8;
    padding: 0 20px 20px;
    height: 100%;
    overflow: auto;
    .user-list {
      height: 14px;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(41, 41, 41, 1);
      margin-top: 20px;
    }
  }
}
</style>
<style lang="scss">
.select-box {
  .select-wrap {
    width: 100px;
    box-sizing: border-box;

    select {
      width: 100%;
      height: 18px;
      font-size: 12px;
      color: #292929;
      border: solid 1px#E8E8E8;
      appearance: auto;
      -moz-appearance: auto;
      -webkit-appearance: auto;
    }
  }
  .search-box {
    margin-left: 16px;
    width: 130px;
    height: 18px;
    box-sizing: border-box;
  }
}
</style>
