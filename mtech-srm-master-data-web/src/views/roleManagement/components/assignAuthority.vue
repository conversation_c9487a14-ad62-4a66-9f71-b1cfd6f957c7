<template>
  <div class="assign-container">
    <mt-tree-grid
      ref="treeGrid2"
      :data-source="inlineEditData"
      :columns="editColumns"
      child-mapping="subtasks"
      :tree-column-index="0"
      :allow-paging="false"
      grid-lines="Horizontal"
      :allow-sorting="true"
      :allow-filtering="true"
      :filter-settings="filterSettings"
      :auto-check-hierarchy="true"
    ></mt-tree-grid>
  </div>
</template>

<script>
import Vue from 'vue'
export default {
  data() {
    let _this = this
    return {
      filterSettings: { type: 'Menu', hierarchyMode: 'Both' },
      inlineEditData: [
        {
          authorityName: 'Planning',
          authorityType: 100,
          checkboxVal: false,
          subtasks: [
            {
              authorityName: this.$t('操作'),
              authorityType: 1030,
              checkboxVal: false,
              subtasks: [
                {
                  authorityName: this.$t('编辑'),
                  authorityType: 'yes',
                  checkboxVal: false
                },
                {
                  authorityName: this.$t('操作'),
                  checkboxVal: false,
                  authorityType: 3333
                },
                {
                  authorityName: this.$t('删除'),
                  authorityType: 3333,
                  checkboxVal: false
                }
              ]
            },
            {
              authorityName: 'Planning2',
              authorityType: 1020,
              checkboxVal: false
            },
            {
              authorityName: 'Planning2',
              authorityType: 1010,
              checkboxVal: true
            }
          ]
        }
      ],
      editColumns: [
        {
          field: 'authorityName',
          headerText: this.$t('权限名称'),
          width: '60%',
          textAlign: 'left',
          isPrimaryKey: 'true'
        },
        {
          field: 'authorityType',
          headerText: this.$t('权限类型'),
          width: '20.58%',
          textAlign: 'left'
        },
        {
          headerText: this.$t('操作'),
          textAlign: 'left',
          template: () => {
            return {
              template: Vue.component('check-box-template', {
                template: `
                                  <div class="todo-list-column">
                                     <mt-checkbox
                                        class="checkbox-item"
                                        v-model="checkboxVal"
                                        label=""
                                        @change="handleChange"
                                    ></mt-checkbox>
                                  </div>`,
                data: function () {
                  return {}
                },
                created() {
                  this.checkboxVal = this.data.checkboxVal
                },
                methods: {
                  handleChange(val) {
                    let { checked } = val
                    _this.checkboxEvent(checked, this.data)
                  }
                }
              })
            }
          }
        }
      ]
    }
  },
  methods: {
    checkboxEvent(checked = false, row = {}) {
      let rowIndex = row.index
      let index = 0
      function reduceArr(arr) {
        for (let val of arr) {
          if (rowIndex === index) {
            console.log(val)
            val.checkboxVal = checked
            break
          }
          index++
          if (!!val.subtasks && val.subtasks.length > 0) {
            reduceArr(val.subtasks)
          }
        }
      }
      reduceArr(this.inlineEditData)
    },

    // 获取权限树形数据
    getAuthorityList() {}
  }
}
</script>

<style lang="scss" scoped>
.assign-container {
  width: auto;
  max-width: 50vw;
  min-width: 50vw;
}
</style>
