<template>
  <div class="assign-container">
    <!-- locale="zh" -->
    <mt-data-grid
      ref="dataGrid3"
      :data-source="dataSource"
      :column-data="columnData"
      :allow-paging="false"
      gird-lines="Horizontal"
      grid-lines="Horizontal"
      :allow-sorting="true"
      :allow-filtering="true"
      :filter-settings="filterSettings"
    ></mt-data-grid>

    <mt-dialog
      ref="dialog"
      css-class="dialog-box"
      :header="$t('控制')"
      width="900"
      :buttons="buttons"
    >
      <div class="dialog-warp">
        <mt-data-grid
          ref="dataGrid3"
          :data-source="dataSource1"
          :column-data="columnData1"
          :allow-paging="false"
          gird-lines="Horizontal"
          grid-lines="Horizontal"
          :allow-sorting="true"
          :allow-filtering="true"
          :filter-settings="filterSettings"
        ></mt-data-grid>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
export default {
  data() {
    let _this = this
    return {
      detailDialogData: [],
      filterSettings: { type: 'Menu', hierarchyMode: 'Both' },
      dataSource: [
        {
          one: this.$t('用户1'),
          two: '张三,王五,里斯',
          three: 'xx',
          type: 'tab'
        },
        {
          one: this.$t('用户2'),
          two: '张三,王五,',
          three: 'xx',
          type: 'control'
        },
        {
          one: this.$t('用户3'),
          two: '张三,王五,里斯',
          three: 'xx',
          type: 'tips'
        }
      ],
      columnData: [
        {
          field: 'one',
          headerText: this.$t('一级菜单'),
          textAlign: 'left'
        },
        {
          field: 'two',
          headerText: this.$t('二级菜单'),
          textAlign: 'left'
        },
        {
          field: 'three',
          headerText: this.$t('三级菜单'),
          textAlign: 'left'
        },
        {
          field: 'type',
          headerText: this.$t('类型'),
          textAlign: 'left'
        },
        {
          field: 'operator',
          headerText: this.$t('字段权限'),
          textAlign: 'left',
          width: '150',
          template: () => {
            return {
              template: Vue.component('todo-column-template', {
                template: `
                                  <div class="todo-list-column">
                                    <div class="operator-btn" @click.prevent.stop="openCtDialog()">{{ $t("控制") }}</div>
                                  </div>`,
                data: function () {
                  return {}
                },
                methods: {
                  openCtDialog() {
                    console.log()
                    this.$nextTick(() => {
                      _this.$refs.dialog.ejsRef.show()
                    })
                  }
                }
              })
            }
          }
        }
      ],
      dataSource1: [
        {
          one: this.$t('用户'),
          two: '张三,王五,里斯',
          three: this.$t('是'),
          defaultOn: true
        },
        {
          one: this.$t('部门'),
          two: '张三,王五,',
          three: this.$t('否'),
          defaultOn: false
        },
        {
          one: this.$t('岗位'),
          two: '张三,王五,里斯',
          three: this.$t('是'),
          defaultOn: true
        }
      ],
      columnData1: [
        {
          field: 'one',
          headerText: this.$t('字段编码'),
          textAlign: 'left'
        },
        {
          field: 'two',
          headerText: this.$t('字段名称'),
          textAlign: 'left'
        },
        {
          field: 'three',
          headerText: this.$t('是否允许修改'),
          textAlign: 'left',
          template: () => {
            return {
              template: Vue.component('txt-node', {
                template: `
                                  <div v-if="three === this.$t('是')" style="color: #6386C1">{{ $t("是") }}</div>
                                  <div v-else style="color: #ddd">{{ $t("否") }}</div>
                                `,
                data: function () {
                  return {}
                },
                created() {
                  this.three = this.data.three
                },
                methods: {}
              })
            }
          }
        },
        {
          field: 'operator',
          headerText: this.$t('是否可见'),
          textAlign: 'left',
          width: '150',
          template: () => {
            return {
              template: Vue.component('switch-box', {
                template: `
                                  <div class="todo-list-column">
                                    <mt-switch class="item" v-model="data.defaultOn" onLabel="" offLabel="" @change="handleChange"></mt-switch>
                                  </div>`,
                data: function () {
                  return {}
                },
                created() {
                  this.defaultOn = this.data.defaultOn
                },
                updated() {
                  console.log('updata')
                },

                methods: {
                  handleChange(val) {
                    let checked = val
                    _this.checkboxEvent(checked, this.data)
                  }
                }
              })
            }
          }
        }
      ],
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  methods: {
    checkboxEvent(checked = false, row = {}) {
      for (let i = 0; i < this.dataSource1.length; i++) {
        if (i === +row.index) {
          this.dataSource1[i].defaultOn = checked
          break
        }
      }
    },
    hide() {
      this.$refs.dialog.ejsRef.hide()
    },
    save() {
      console.log('保存')
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
.assign-container {
  width: auto;
  max-width: 50vw;
}
.assign-container /deep/ .operator-btn {
  color: #6386c1;
  font-size: 14px;
  padding-right: 10px;
}
.assign-container /deep/ .e-templatecell {
  padding-left: 0 !important;
  padding-right: 0 !important;
}
.assign-container /deep/ .todo-list-column {
  color: #6386c1;
  font-size: 14px;
  display: flex;
  justify-content: space-evenly;
}
.assign-container /deep/ .operator-btn {
  color: #6386c1;
  font-size: 14px;
  margin-right: 0;
}

.dialog-box {
  .dialog-warp {
    margin-top: 20px;
    width: 860px;
    height: 100%;
  }
}
</style>
