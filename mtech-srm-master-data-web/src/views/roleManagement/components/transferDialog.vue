<template>
  <div class="mt-dialog-transfer" id="mt-dialog-transfer">
    <mt-dialog
      ref="toast"
      v-bind="$attrs"
      v-on="$listeners"
      :header="header"
      width="800px"
      css-class="transfer-dialog-wrap"
      :buttons="buttons"
    >
      <div class="mt-dialog-transfer-l-box">
        <div class="mt-dialog-transfer-s-box">
          <div class="sTitle">{{ leftTitle }}</div>
          <div class="e-input-group" id="mt-serch">
            <span class="e-input-group-icon e-input-serch" @click="serch"></span>
            <input
              class="e-input"
              type="text"
              :placeholder="$t('请输入搜索内容')"
              v-model="leftSerch"
              @keyup.enter="serch"
            />
          </div>
          <div class="selectbox">
            <select v-model="userGroupTypeCode">
              <option disabled value="">{{ $t('请选择') }}</option>
              <option value="PR001USER">{{ $t('用户') }}</option>
              <option value="PR002STATION">{{ $t('岗位') }}</option>
              <option value="PR003DEPART">{{ $t('部门') }}</option>
            </select>
          </div>
          <div class="mt-dialog-transfer-tree-box">
            <mt-treeView
              ref="leftTree"
              css-class="e-outline"
              v-bind="$attrs"
              v-on="$listeners"
              :fields="fieldsLeft"
              :show-check-box="true"
              :enable-persistence="true"
              @nodeSelected="nodeChecked"
            ></mt-treeView>
          </div>
        </div>
        <div class="mt-dialog-transfer-icon-box">
          <div style="width: 30px">
            <mt-button
              icon-css="e-btn-sb-icons e-input-toleft"
              css-class="e-small e-round"
              @click="dataToLeft"
              style="margin: 15px 0"
            ></mt-button>
            <mt-button
              icon-css="e-btn-sb-icons e-input-toright"
              css-class="e-small e-round"
              style="margin: 15px 0"
              @click="dataToRight"
            ></mt-button>
          </div>
        </div>
        <div class="mt-dialog-transfer-s-box">
          <div class="sTitle">{{ rightTitle }}</div>
          <div class="e-input-group" id="mt-serch">
            <span class="e-input-group-icon e-input-serch"></span>
            <input
              class="e-input"
              type="text"
              :placeholder="$t('请输入搜索内容')"
              v-model="rightSerch"
            />
          </div>
          <div class="mt-dialog-transfer-tree-box2">
            <div v-for="(item, i) in rightData" :key="i">
              <mt-checkbox
                class="checkbox-item"
                v-model="item.value"
                :label="item[fieldsLeft.text]"
                @change="handleChange"
                v-if="!item.unShow"
              ></mt-checkbox>
            </div>
          </div>
        </div>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
export default {
  props: {
    fieldsLeft: {
      type: Object,
      required: true,
      default: () => {
        return {}
      }
    },
    fieldsRight: {
      type: Array,
      required: false,
      default: () => []
    },
    leftTitle: {
      type: String,
      required: false,
      default: this.$t('左小标题')
    },
    rightTitle: {
      type: String,
      required: false,
      default: this.$t('右小标题')
    },
    header: {
      type: String,
      required: false,
      default: this.$t('此处为弹窗大标题')
    }
  },
  data() {
    return {
      userGroupTypeCode: 'PR001USER',
      target: document.getElementById('mt-dialog-transfer'),
      rightSerch: '',
      buttons: [
        {
          click: () => {
            this.$refs.toast.ejsRef.hide()
          },
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: () => {
            this.save()
            this.$refs.toast.ejsRef.hide()
          },
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      leftSerch: '',
      leftTreeDown: {
        dataSource: this.fieldsLeft.dataSource,
        value: this.fieldsLeft.id,
        text: this.fieldsLeft.text,
        child: this.fieldsLeft.child
      },
      rightData: []
    }
  },
  watch: {
    userGroupTypeCode(val) {
      this.$emit('groupTypeChange', val)
    },
    rightSerch(val) {
      this.rightData.forEach((e) => {
        if (e.nodeText.indexOf(val) > -1) {
          e.unShow = false
        } else {
          e.unShow = true
        }
      })
    }
  },
  created() {
    this.rightData = this.fieldsRight
  },
  methods: {
    save() {
      this.$emit('getRightData', this.rightData, this.userGroupTypeCode)
    },
    nodeChecked(args) {
      console.log(args)
    },
    dataToRight() {
      this.fieldsLeft.dataSource = this.$refs.leftTree.ejsRef.getTreeData()
      console.log(this.fieldsLeft)
      this.getAllLeaf(this.$refs.leftTree.ejsRef.getTreeData())
    },
    handleChange() {},
    dataToLeft() {
      let delA = []
      this.rightData.forEach((ele) => {
        if (ele.value) {
          delA.push(ele.nodeId)
          this.$refs.leftTree.ejsRef.uncheckAll([ele.nodeId])
        }
      })
      if (delA.length !== 0) {
        delA.forEach((e) => {
          this.rightData.forEach((e2, i) => {
            if (e === e2.nodeId) {
              this.rightData.splice(i, 1)
            }
          })
        })
      }
    },
    getAllLeaf(data) {
      let result = []
      let _this = this
      function getLeaf(data) {
        for (let i = 0; i < data.length; i++) {
          if (!data[i][`${_this.fieldsLeft.child}`]) {
            if (data[i].isChecked) {
              result.push(data[i])
            }
          } else {
            getLeaf(data[i][`${_this.fieldsLeft.child}`])
          }
        }
      }
      getLeaf(data)
      this.rightData = result
    },
    serch() {
      this.$emit('serchData', this.leftSerch)
      console.log(this.fieldsLeft)
    }
  }
}
</script>
