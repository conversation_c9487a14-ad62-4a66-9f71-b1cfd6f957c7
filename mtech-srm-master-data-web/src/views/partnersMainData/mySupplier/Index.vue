<template>
  <div class="set-country">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import { columnData } from './data/index'
export default {
  data() {
    return {
      pageConfig: [
        {
          gridId: 'f95a466d-20c3-4620-88c3-e7e68b9a0510',
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar

            tools: [[], ['Filter', 'Refresh', 'Setting']]
          },
          grid: {
            columnData: columnData,
            dataSource: [],
            asyncConfig: {
              url: '/masterDataManagement/tenant/supplier/paged-query'
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },

  methods: {
    handleClickCellTitle(e) {
      //这里是跳转
      let { field, data } = e
      if (field === 'supplierCode') {
        this.$router.push({
          path: 'my-supplier-details',
          query: {
            partnerCode: data.partnerCode,
            orgCode: data.organizationCode
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.set-country /deep/ .lick {
  color: #005ca9 !important;
  cursor: pointer;
}
.set-country {
  height: 100%;
}
</style>
