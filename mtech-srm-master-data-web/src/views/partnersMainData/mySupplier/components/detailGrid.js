import { i18n } from '@/main.js'

export const gridColumnData = [
  {
    type: 'checkbox',
    width: '60px'
  },
  {
    width: '150',
    field: 'purchasingGroupCode',
    headerText: i18n.t('采购组织编码')
  },
  {
    width: '150',
    field: 'currencyCode',
    headerText: i18n.t('币种编码')
  },
  {
    width: '150',
    field: 'taxCode',
    headerText: i18n.t('税码编码')
  },
  {
    width: '150',
    field: 'isTransferFactoryName',
    headerText: i18n.t('是否转厂')
  },
  {
    width: '150',
    field: 'confirmControlCode',
    headerText: i18n.t('确认控制编码')
  },
  {
    width: '150',
    field: 'drawerPartyName',
    headerText: i18n.t('出票方')
  },
  {
    width: '250',
    field: 'caseGroupCode',
    headerText: i18n.t('方案组编码')
  },
  {
    width: '150',
    field: 'globalTradeConditionCode',
    headerText: i18n.t('国际贸易条件编码')
  },
  {
    width: '150',
    field: 'tradeTermSecond',
    headerText: i18n.t('国际贸易条件（部分2）')
  },
  {
    width: '150',
    field: 'pricingDateControlName',
    headerText: i18n.t('定价日期控制')
  },
  {
    width: '150',
    field: 'orderGoodsAddrName',
    headerText: i18n.t('订货地址')
  },
  {
    width: '150',
    field: 'payConditionName',
    headerText: i18n.t('付款条件'),
  },
  {
    width: '150',
    field: 'statusId',
    headerText: i18n.t('生效状态'),
    formatter: (column, data) => {
      return data['statusId'] == 1 ? i18n.t('生效') : data['statusId'] == 0 ? i18n.t('失效') : ''
    }
  }
]
