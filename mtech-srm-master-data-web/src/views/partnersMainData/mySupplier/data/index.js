import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-09-17 14:48:52
 * @LastEditTime: 2021-12-29 13:41:58
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\partnersMainData\mySupplier\data\index.js
 */
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '250',
    field: 'supplierCode',
    cssClass: 'lick',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '250',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '200',
    field: 'organizationName',
    headerText: i18n.t('归属公司名称')
  },
  {
    width: '200',
    field: 'organizationCode',
    headerText: i18n.t('归属公司编码')
  },
  // {
  //   width: "150",
  //   field: "stageId",
  //   headerText: i18n.t("阶段"),
  //   valueConverter:{
  //     type:"map",
  //     map:{"0":i18n.t("正式")}
  //   }
  // },
  {
    field: 'statusId',
    headerText: i18n.t('状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('注册'),
        2: i18n.t('潜在'),
        3: i18n.t('新合格'),
        4: i18n.t('临时'),
        10: i18n.t('合格'),
        11: i18n.t('预合格'),
        20: i18n.t('冻结'),
        30: i18n.t('黑名单'),
        40: i18n.t('退出')
      }
    }
  }
]
