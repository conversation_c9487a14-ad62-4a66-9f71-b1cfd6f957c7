<template>
  <div class="set-country">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>
    <!-- 导入弹框 -->
    <uploadExcelDialog
      ref="uploadExcelRefs"
      :down-template-name="downTemplateName"
      :upload-params="uploadParams"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></uploadExcelDialog>
  </div>
</template>

<script>
import { columnData } from './data/index'
import { getHeadersFileName, download } from '@/utils/util'
export default {
  components: {
    uploadExcelDialog: require('./components/lineUpload.vue').default
  },
  data() {
    return {
      downTemplateName: this.$t('上传'),
      uploadParams: {}, // 明细行上传excel的
      pageConfig: [
        {
          gridId: '202bd62a-b8d4-4358-9589-0237b18d2ad3',
          useBaseConfig: false,
          toolbar: [
            [
              {
                id: 'export',
                title: this.$t('导出')
              }
            ],
            [
              'Filter',
              'Refresh',
              'Setting'
              // {
              //   id: 'import',
              //   icon: 'icon_solid_Import',
              //   title: this.$t('导入'),
              // },
            ]
          ],
          grid: {
            columnData: columnData,
            dataSource: [],
            asyncConfig: {
              url: '/masterDataManagement/tenant/supply/source/paged-query'
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },

  methods: {
    // 显示隐藏上传弹框
    showUploadExcel(flag) {
      if (flag) {
        console.log(this.$refs.uploadExcelRefs, 'this.$refs.uploadExcelRefs')
        this.$refs.uploadExcelRefs.uploadData = null // 清空数据
        this.$refs.uploadExcelRefs.fileLength = 0
        this.$refs.uploadExcelRefs.$refs.uploader.files = []
        this.$refs.uploadExcelRefs.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRefs.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    handleClickCellTitle(e) {
      //这里是跳转
      console.log(e)
    },
    handleClickToolBar(e) {
      const { toolbar } = e
      if (toolbar.id === 'export') {
        this.handleExport()
      }
      if (toolbar.id === 'import') {
        this.handleClickUpload()
      }
    },
    handleExport() {
      const asyncParams = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.asyncParams || {}
      const params = {
        ...asyncParams
      }
      this.$API.partnersMainData
        .exportSupplySourceApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
    },
    //导入
    handleClickUpload() {
      this.$dialog({
        modal: () => import(/* webpackChunkName: "components/upload" */ './components/upload.vue'),
        data: {
          title: this.$t('导入')
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 上传成功后，获取到的数据
    upExcelConfirm() {
      this.$refs.uploadExcelRefs.$refs.dialog.$refs.ejsRef.hide()
      this.$toast({ content: this.$t('导入成功'), type: 'success' })
      this.updateList()
    }
  }
}
</script>

<style lang="scss" scoped>
.set-country /deep/ .lick {
  color: #005ca9 !important;
  cursor: pointer;
}
.set-country {
  height: 100%;
}
</style>
