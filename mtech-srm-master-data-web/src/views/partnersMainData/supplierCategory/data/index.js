import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'organizationCode',
    headerText: i18n.t('公司编码')
  },
  {
    width: '200',
    field: 'organizationName',
    headerText: i18n.t('公司名称')
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '200',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '200',
    field: 'supplier',
    headerText: i18n.t('供应商层级')
  },
  {
    width: '100',
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    width: '200',
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    field: 'purchaseOrgCode',
    headerText: i18n.t('采购组织编码')
  },
  {
    width: '200',
    field: 'purchaseOrgName',
    headerText: i18n.t('采购组织名称')
  },
  {
    width: '150',
    field: 'offerAttribute',
    headerText: i18n.t('报价属性'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('寄售价'),
        2: i18n.t('标准价'),
        3: i18n.t('委外')
      }
    }
  },
  {
    width: '150',
    field: 'priceEffectiveMode',
    headerText: i18n.t('价格生效方式'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('按出库生效'),
        1: i18n.t('按订单生效'),
        5: i18n.t('按入库生效'),
        NULL: i18n.t('按出库生效')
      }
    }
  },
  {
    field: 'statusId',
    headerText: i18n.t('状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('注册'),
        2: i18n.t('潜在'),
        3: i18n.t('新合格'),
        4: i18n.t('临时'),
        10: i18n.t('合格'),
        11: i18n.t('预合格'),
        20: i18n.t('冻结'),
        40: i18n.t('退出')
      }
    }
  },
  {
    field: 'categoryType',
    headerText: i18n.t('供应类型'),
    width: 150,
    valueConverter: {
      type: 'map',
      map: {
        2: i18n.t('代理'),
        1: i18n.t('原厂')
      }
    }
  }
]
