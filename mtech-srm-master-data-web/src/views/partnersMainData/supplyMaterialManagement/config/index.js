import { i18n } from '@/main.js'
import dayjs from 'dayjs'

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂编码'),
    minWidth: 120,
    editRender: {},
    slots: {
      edit: 'siteCodeEdit'
    }
  },
  {
    field: 'siteName',
    title: i18n.t('工厂名称'),
    minWidth: 200,
    editRender: {},
    slots: {
      edit: 'siteNameEdit'
    }
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码'),
    minWidth: 120,
    editRender: {},
    slots: {
      edit: 'itemCodeEdit'
    }
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称'),
    minWidth: 200,
    editRender: {},
    slots: {
      edit: 'itemNameEdit'
    }
  },
  {
    field: 'specModel',
    title: i18n.t('规格型号'),
    minWidth: 200,
    editRender: {},
    slots: {
      edit: 'specModelEdit'
    }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码'),
    minWidth: 120,
    editRender: {},
    slots: {
      edit: 'supplierCodeEdit'
    }
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    minWidth: 200,
    editRender: {},
    slots: {
      edit: 'supplierNameEdit'
    }
  },
  {
    field: 'supplierItemCode',
    title: i18n.t('供方物料编码'),
    minWidth: 160,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'supplierItemName',
    title: i18n.t('供方物料名称'),
    minWidth: 200,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人'),
    minWidth: 120
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人'),
    minWidth: 120
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  }
]
