<!-- 采访-供应商供货物料管理 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item :label="$t('工厂')" prop="siteCode">
          <RemoteAutocomplete
            v-model="searchFormModel.siteCode"
            :url="$API.masterData.getSiteListUrl"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('物料编码')" prop="itemCode">
          <mt-input
            v-model="searchFormModel.itemCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供方物料编码')" prop="supplierItemCode">
          <mt-input
            v-model="searchFormModel.supplierItemCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供应商')" prop="supplierCode">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCode"
            url="/masterDataManagement/tenant/supplier/paged-query"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('物料名称')" prop="itemName">
          <mt-input
            v-model="searchFormModel.itemName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('规格型号')" prop="specModel">
          <mt-input
            v-model="searchFormModel.specModel"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供方物料名称')" prop="supplierItemName">
          <mt-input
            v-model="searchFormModel.supplierItemName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('创建人')" prop="createUserName">
          <mt-input
            v-model="searchFormModel.createUserName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateTimeChange(e, 'createTime')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('更新人')" prop="updateUserName">
          <mt-input
            v-model="searchFormModel.updateUserName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="updateTime" :label="$t('更新时间')">
          <mt-date-range-picker
            v-model="searchFormModel.updateTime"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateTimeChange(e, 'updateTime')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="3f07e111-5adc-4270-af81-8d97f1bf00d6"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      keep-source
      :edit-config="{
        trigger: 'dblclick',
        mode: 'row',
        showStatus: true
      }"
      @edit-actived="editBegin"
      @edit-closed="editComplete"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #siteCodeEdit="{ row }">
        <vxe-pulldown ref="xDownSite" transfer>
          <template #default>
            <vxe-input
              :value="row.siteCode"
              :placeholder="$t('请选择工厂')"
              readonly
              @click="focusSiteCode"
            />
          </template>
          <template #dropdown>
            <vxe-input
              prefix-icon="vxe-icon-search"
              :placeholder="$t('搜索')"
              @keyup="keyupSiteCode"
              style="width: 100%"
            />
            <vxe-list height="200" class="predict-vxe-dropdown" :data="siteOptions" auto-resize>
              <template #default="{ items }">
                <div
                  v-show="siteOptions.length"
                  class="predict-vxe-list-item"
                  v-for="item in items"
                  :key="item.value"
                  @click="selectSiteCode(item, row)"
                >
                  <span>{{ item.label }}</span>
                </div>
                <div v-show="!siteOptions.length" class="predict-vxe-list-item">
                  <span>{{ $t('暂无数据') }}</span>
                </div>
              </template>
            </vxe-list>
          </template>
        </vxe-pulldown>
      </template>
      <template #siteNameEdit="{ row }">
        <vxe-input :value="row.siteName" :placeholder="$t('请选择工厂')" disabled />
      </template>
      <template #itemCodeEdit="{ row }">
        <vxe-pulldown ref="xDownItem" transfer>
          <template #default>
            <vxe-input
              :value="row.itemCode"
              :placeholder="$t('请选择物料')"
              readonly
              :disabled="!row.siteCode"
              @click="focusItemCode"
            />
          </template>
          <template #dropdown>
            <vxe-input
              prefix-icon="vxe-icon-search"
              :placeholder="$t('搜索')"
              @keyup="keyupItemCode"
              style="width: 100%"
            />
            <vxe-list height="200" class="predict-vxe-dropdown" :data="itemOptions" auto-resize>
              <template #default="{ items }">
                <div
                  v-show="itemOptions.length"
                  class="predict-vxe-list-item"
                  v-for="item in items"
                  :key="item.value"
                  @click="selectItemCode(item, row)"
                >
                  <span>{{ item.label }}</span>
                </div>
                <div v-show="!itemOptions.length" class="predict-vxe-list-item">
                  <span>{{ $t('暂无数据') }}</span>
                </div>
              </template>
            </vxe-list>
          </template>
        </vxe-pulldown>
      </template>
      <template #itemNameEdit="{ row }">
        <vxe-input :value="row.itemName" :placeholder="$t('请选择物料')" disabled />
      </template>
      <template #specModelEdit="{ row }">
        <vxe-input :value="row.specModel" :placeholder="$t('请选择物料')" disabled />
      </template>
      <template #supplierCodeEdit="{ row }">
        <vxe-pulldown ref="xDownSupplier" transfer>
          <template #default>
            <vxe-input
              :value="row.supplierCode"
              :placeholder="$t('请选择供应商')"
              readonly
              @click="focusSupplierCode"
            />
          </template>
          <template #dropdown>
            <vxe-input
              prefix-icon="vxe-icon-search"
              :placeholder="$t('搜索')"
              @keyup="keyupSupplierCode"
              style="width: 100%"
            />
            <vxe-list height="200" class="predict-vxe-dropdown" :data="supplierOptions" auto-resize>
              <template #default="{ items }">
                <div
                  v-show="supplierOptions.length"
                  class="predict-vxe-list-item"
                  v-for="item in items"
                  :key="item.value"
                  @click="selectSupplierCode(item, row)"
                >
                  <span>{{ item.label }}</span>
                </div>
                <div v-show="!supplierOptions.length" class="predict-vxe-list-item">
                  <span>{{ $t('暂无数据') }}</span>
                </div>
              </template>
            </vxe-list>
          </template>
        </vxe-pulldown>
      </template>
      <template #supplierNameEdit="{ row }">
        <vxe-input :value="row.supplierName" :placeholder="$t('请选择供应商')" disabled />
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData } from './config'
import { getHeadersFileName, download } from '@/utils/util'
import { utils } from '@mtech-common/utils'

export default {
  components: {
    CollapseSearch,
    ScTable,
    RemoteAutocomplete: require('@/components/RemoteAutocomplete').default
  },
  data() {
    return {
      searchFormModel: {},
      toolbar: [
        { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
        { code: 'closeEdit', name: this.$t('取消编辑'), status: 'info', loading: false },
        { code: 'delete', name: this.$t('删除'), status: 'info', loading: false },
        { code: 'import', name: this.$t('导入'), status: 'info', loading: false },
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 500]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      getSiteDataSource: () => {},
      siteOptions: [],
      siteCode: null,
      getItemDataSource: () => {},
      itemOptions: [],
      getSupplierDataSource: () => {},
      supplierOptions: []
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.getSiteCodeList()
    this.getSiteDataSource = utils.debounce(this.getSiteCodeList, 500)
    this.getSupplierCodeList()
    this.getSupplierDataSource = utils.debounce(this.getSupplierCodeList, 500)
    this.getTableData()
  },
  methods: {
    dateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[field + 'End'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    focusSiteCode() {
      this.$refs.xDownSite.showPanel()
    },
    keyupSiteCode(e) {
      this.getSiteDataSource(e)
    },
    selectSiteCode(e, row) {
      this.siteCode = e.siteCode
      row.siteCode = e.siteCode
      row.siteName = e.siteName
      row.itemCode = ''
      row.itemName = ''
      this.getItemCodeList()
      this.getItemDataSource = utils.debounce(this.getItemCodeList, 500)
      this.$refs.xDownSite.hidePanel()
    },
    getSiteCodeList(e = { value: '' }) {
      const { value } = e
      // 工厂下拉
      let params = {
        fuzzyParam: value || '',
        dataLimit: 50
      }
      this.$API.masterData.postSiteFuzzyQuery(params).then((res) => {
        const list = res.data || []
        const newData = list.map((i) => {
          return {
            ...i,
            label: `${i.siteCode}-${i.siteName}`,
            value: i.siteCode
          }
        })
        this.siteOptions = [...newData]
      })
    },
    focusItemCode() {
      this.$refs.xDownItem.showPanel()
    },
    keyupItemCode(e) {
      this.getItemDataSource(e)
    },
    selectItemCode(e, row) {
      row.itemCode = e.itemCode
      row.itemName = e.itemName
      row.specModel = e.itemDescription
      this.$refs.xDownItem.hidePanel()
    },
    getItemCodeList(e = { value: '' }) {
      const { value } = e
      let siteCode = this.siteCode
      // 物料下拉
      let params = {
        page: { current: 1, size: 50 },
        condition: 'and',
        rules: [
          {
            field: 'organizationCode',
            type: 'string',
            operator: 'contains',
            value: siteCode
          },
          {
            field: 'itemCode',
            type: 'string',
            operator: 'contains',
            value: value
          }
        ]
      }
      this.$API.masterData.getItemPage(params).then((res) => {
        const list = res.data?.records || []
        const newData = list.map((i) => {
          return {
            ...i,
            label: `${i.itemCode}-${i.itemName}`,
            value: i.itemCode
          }
        })
        this.itemOptions = [...newData]
      })
    },
    focusSupplierCode() {
      this.$refs.xDownSupplier.showPanel()
    },
    keyupSupplierCode(e) {
      this.getSupplierDataSource(e)
    },
    selectSupplierCode(e, row) {
      row.supplierCode = e.supplierCode
      row.supplierName = e.supplierName
      this.$refs.xDownSupplier.hidePanel()
    },
    getSupplierCodeList(e = { value: '' }) {
      const { value } = e
      // 供应商下拉
      let params = {
        page: { current: 1, size: 50 },
        condition: 'and',
        rules: [
          {
            field: 'supplierCode',
            type: 'string',
            operator: 'contains',
            value: value
          }
        ]
      }
      this.$API.masterData.supplierPagedQuery(params).then((res) => {
        const list = res.data?.records || []
        const newData = list.map((i) => {
          return {
            ...i,
            label: `${i.supplierCode}-${i.supplierName}`,
            value: i.supplierCode
          }
        })
        this.supplierOptions = [...newData]
      })
    },
    editBegin(args) {
      const { row } = args
      if (args.$event) {
        this.getSiteCodeList({ value: row.siteCode })
        this.getSupplierCodeList({ value: row.supplierCode })
      }
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        if (args.$event.target.innerText === this.$t('取消编辑')) {
          this.tableRef.clearEdit()
          return
        }
        // 1、 校验必填
        if (!this.isValidData(row)) {
          this.tableRef.setEditRow(row)
          return
        }
        // 2、 调保存接口
        this.handleSave(row)
      }
    },
    isValidData(data) {
      const { siteCode, itemCode, supplierCode } = data
      let valid = false
      if (!siteCode) {
        this.$toast({ content: this.$t('工厂不能为空'), type: 'warning' })
      } else if (!itemCode) {
        this.$toast({ content: this.$t('物料不能为空'), type: 'warning' })
      } else if (!supplierCode) {
        this.$toast({ content: this.$t('供应商不能为空'), type: 'warning' })
      } else {
        valid = true
      }

      return valid
    },
    handleSave(row) {
      let params = { ...row }
      if (params.id.includes('row_')) {
        params.id = null
      }
      this.$API.partnersMainData
        .saveSupplyMaterialManagementApi(params)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.handleSearch()
          }
        })
        .catch(() => {
          // 当出现错误时，指定行进入编辑状态
          this.tableRef.setEditRow(row)
        })
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.partnersMainData
        .pageSupplyMaterialManagementApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const ids = []
      selectedRecords.forEach((item) => {
        ids.push(item.id)
      })
      const commonToolbar = ['delete']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'closeEdit':
          this.tableRef.clearEdit()
          this.handleSearch()
          break
        case 'delete':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除？')
            },
            success: () => {
              this.handleDelete(ids)
            }
          })
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleAdd() {
      const item = {
        id: null
      }
      this.tableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.tableRef.setEditRow(currentViewRecords[0])
      })
    },
    handleDelete(ids) {
      this.$API.partnersMainData.deleteSupplyMaterialManagementApi({ ids }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.partnersMainData.importSupplyMaterialManagementApi,
          downloadTemplateApi: this.$API.partnersMainData.tempSupplyMaterialManagementApi,
          paramsKey: 'excel'
        },
        success: () => {
          this.handleSearch()
        }
      })
    },
    handleExport(e) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$API.partnersMainData
        .exportSupplyMaterialManagementApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>

<style>
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
</style>
