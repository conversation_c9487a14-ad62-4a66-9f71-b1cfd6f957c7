import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-10-21 10:06:19
 * @LastEditTime: 2021-12-30 09:53:27
 * @LastEditors: your name
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\baseMainData\setExchangeRate\data\index.js
 */
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '190',
    field: 'sourceCurrencyCode',
    headerText: i18n.t('源货币代码'),
    cellTools: ['Edit', 'Delete']
  },
  {
    width: '190',
    field: 'sourceCurrencyName',
    headerText: i18n.t('源货币名称')
  },
  {
    width: '190',
    field: 'targetCurrencyCode',
    headerText: i18n.t('目标货币代码')
  },
  {
    width: '190',
    field: 'targetCurrencyName',
    headerText: i18n.t('目标货币名称')
  },
  {
    width: '190',
    field: 'rate',
    headerText: i18n.t('汇率（%）')
  },
  {
    width: '190',
    field: 'startTime',
    headerText: i18n.t('有效开始时间')
  },
  {
    width: '190',
    field: 'endTime',
    headerText: i18n.t('有效截止时间')
  },
  {
    width: '150',
    field: 'rateDescription',
    headerText: i18n.t('说明')
  },
  {
    width: 'auto',
    field: 'updateTime',
    headerText: i18n.t('修改时间')
  }
]
