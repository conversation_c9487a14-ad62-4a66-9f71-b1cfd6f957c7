<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="addForm" :rules="rules">
      <mt-form-item prop="sourceCurrencyId" :label="$t('源货币')">
        <mt-select
          ref="sourceSelect"
          v-model="addForm.sourceCurrencyId"
          :data-source="currencySource"
          @change="handleCurrencyChange($event, 'currencyTarget')"
          :allow-filtering="true"
          :show-clear-button="true"
          :fields="{ text: 'currencyName', value: 'id' }"
          :disabled="dialogData && dialogData.dialogType == 'edit'"
          :placeholder="$t('请选择源货币')"
        ></mt-select>
      </mt-form-item>

      <mt-form-item prop="targetCurrencyId" :label="$t('目标货币')">
        <mt-select
          ref="targetSelect"
          v-model="addForm.targetCurrencyId"
          :data-source="currencyTarget"
          :allow-filtering="true"
          :show-clear-button="true"
          :fields="{ text: 'currencyName', value: 'id' }"
          @change="handleCurrencyChange($event, 'currencySource')"
          :disabled="dialogData && dialogData.dialogType == 'edit'"
          :placeholder="$t('请选择目标货币')"
        ></mt-select>
      </mt-form-item>

      <mt-form-item prop="rate" :label="$t('汇率（%）')">
        <mt-input-number
          v-model.number="addForm.rate"
          :show-clear-button="true"
          :placeholder="$t('请输入汇率')"
        ></mt-input-number>
      </mt-form-item>

      <mt-form-item prop="effectiveTime" :label="$t('有效时间')" class="fullWidth">
        <mt-date-range-picker
          v-model="addForm.effectiveTime"
          format="yyyy-MM-dd"
          :show-clear-button="true"
          :placeholder="$t('选择开始时间和结束时间')"
        ></mt-date-range-picker>
      </mt-form-item>

      <mt-form-item prop="rateDescription" :label="$t('说明')" class="fullWidth">
        <mt-input
          v-model.trim="addForm.rateDescription"
          :disabled="false"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入说明')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import common from '@/utils/constant'
import { formatDate, formatRules } from '@/utils/util'
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      addForm: {
        targetCurrencyId: '',
        sourceCurrencyId: '',
        rate: null,
        effectiveTime: null,
        rateDescription: ''
      },
      rules: {
        // unitName: [
        //   { required: true, message: this.$t("请输入中文名称"), trigger: "blur" }
        // ]
      },
      currencyAll: [],
      currencyTarget: [],
      currencySource: []
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.$refs.ruleForm.resetFields()
    if (this.dialogData?.row) {
      let _addForm = JSON.parse(JSON.stringify(this.dialogData?.row))
      if (_addForm.startTime && _addForm.endTime) {
        _addForm.effectiveTime = [_addForm.startTime, _addForm.endTime]
      }
      this.addForm = _addForm
    }
    this.getAllCurrency()
    if (this.dialogData?.dialogType == 'add') {
      this.dialogTitle = this.$t('新增')
    } else {
      this.dialogTitle = this.$t('编辑')
    }
    this.getRules()
  },

  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    getAllCurrency() {
      this.$API.baseMainData.getCurrencyDataAll().then((res) => {
        console.log(res)
        this.currencyAll = res.data
        this.currencyTarget = res.data
        this.currencySource = res.data
      })
    },

    handleCurrencyChange(val, keys) {
      // console.log(this.$t("改变后的值"), val, keys);
      let _currencyAll = JSON.parse(JSON.stringify(this.currencyAll))
      if (val?.value) {
        _currencyAll = _currencyAll.filter((item) => item.id != val.value)
        // console.log(_currencyAll);
      }
      this[keys] = _currencyAll
    },

    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // console.log(this.addForm);
          let params = {
            applicationId: common.applicationId,
            ...this.addForm
          }
          if (this.addForm.effectiveTime && this.addForm.effectiveTime.length > 0) {
            params.startTime = formatDate(
              new Date(this.addForm.effectiveTime[0]),
              'yyyy-MM-dd hh:mm:ss'
            )
            params.endTime = formatDate(
              new Date(this.addForm.effectiveTime[1]),
              'yyyy-MM-dd hh:mm:ss'
            )
          }
          delete params.effectiveTime
          let _request = this.dialogData?.requestUrl
          // console.log(this.dialogData, _request);
          this.$API.baseMainData[_request](params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('handleAddDialogShow', false)
              this.$emit('confirmSuccess')
            }
          })
        }
      })
    },

    handleClose() {
      this.$emit('handleAddDialogShow', false)
    },

    getRules() {
      if (this.dialogData?.dialogType == 'add') {
        this.$API.baseMainData.getAddRulesExchangeRate().then((res) => {
          if (res.code == 200) this.rules = formatRules(res.data)
          this.rules.effectiveTime = [
            {
              required: true,
              message: this.$t('请选择有效时间'),
              trigger: 'blur'
            }
          ]
        })
      } else {
        this.$API.baseMainData.getUpdateRulesExchangeRate().then((res) => {
          if (res.code == 200) this.rules = formatRules(res.data)
          this.rules.effectiveTime = [
            {
              required: true,
              message: this.$t('请选择有效时间'),
              trigger: 'blur'
            }
          ]
        })
      }
    }
  }
}
</script>

<style></style>
