<template>
  <div class="set-country">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>

    <add-dialog
      v-if="addDialogShow"
      :dialog-data="dialogData"
      @handleAddDialogShow="handleAddDialogShow"
      @confirmSuccess="confirmSuccess"
    ></add-dialog>

    <add-detail :key="addDetailKey" :row-info="rowInfo" v-if="showDefault"></add-detail>

    <dialog-uploader
      ref="uploader"
      :template-file-config="templateFileConfig"
      :async-settings="asyncSettings"
      @uploadSuccess="uploadSuccess"
    ></dialog-uploader>
  </div>
</template>

<script>
import { getHeadersFileName, download } from '@/utils/util'
import common from '@/utils/constant'
import { columnData } from './data'
import DialogUploader from '@/components/common/Uploader.vue'
export default {
  components: {
    addDialog: require('./component/addDialog.vue').default,
    addDetail: require('./component/addDetail.vue').default,
    DialogUploader
  },
  data() {
    return {
      addDetailKey: 1,
      currentTabIndex: 0,
      pageConfig: [
        {
          toolbar: {
            tools: []
          },
          grid: {
            columnData: columnData,
            dataSource: [],
            frozenColumns: 1,
            asyncConfig: {
              url: `${common.PROXY_BASE}/dict/paged-query`
            }
          }
        }
      ],
      addDialogShow: false,
      dialogData: null,
      showDefault: false,
      rowInfo: null,

      templateFileConfig: {
        templateId: this.$t('字典主数据导入模板')
      },
      asyncSettings: {
        saveUrl: `/api${common.PROXY_BASE}/dict/import-data`
      }
    }
  },
  created() {
    this.setTools()
  },
  methods: {
    setTools() {
      let tools = [
        'Add',
        'Delete',
        {
          id: 'active',
          icon: 'icon_Activation',
          title: this.$t('激活')
        },
        {
          id: 'inactive',
          icon: 'icon_solid_Disable1',
          title: this.$t('失效')
        },
        {
          id: 'json_import',
          icon: 'icon_solid_Import',
          title: this.$t('导入(JSON)')
          // permission: ['O_02_1334'],
        },
        {
          id: 'json_export',
          icon: 'icon_solid_pushorder',
          title: this.$t('导出(JSON)')
          // permission: ['O_02_1335'],
        },
        {
          id: 'import',
          icon: 'icon_solid_Import',
          title: this.$t('导入')
        },
        {
          id: 'export',
          icon: 'icon_solid_pushorder',
          title: this.$t('导出')
        }
      ]
      this.pageConfig[this.currentTabIndex].toolbar.tools.push(tools)
      this.pageConfig[this.currentTabIndex].toolbar.tools.push(['Filter', 'Refresh', 'Setting'])
      // this.$API.org.getUserInfo({}).then((resData) => {
      //   const { tenantId } = resData.data
      // })
    },
    handleClickToolBar(e) {
      let _checkedRecords = []
      if (Object.hasOwnProperty.call(e, 'grid')) {
        _checkedRecords = e.grid.getSelectedRecords()
      } else {
        _checkedRecords = e.treeGrid.getCheckedRecords()
      }
      if (
        _checkedRecords.length <= 0 &&
        !(
          e.toolbar.id == 'Add' ||
          e.toolbar.id == 'Filter' ||
          e.toolbar.id == 'Refresh' ||
          e.toolbar.id == 'Setting' ||
          e.toolbar.id === 'import' ||
          e.toolbar.id === 'export' ||
          e.toolbar.id === 'json_import'
        )
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _id = []
      _checkedRecords.map((item) => _id.push(item.id))
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'Delete') {
        this.handleDelete(_id)
      } else if (e.toolbar.id == 'active') {
        this.handleUpdateStatus(_id, 1, '确认进行激活操作？关联的字典明细也将被激活')
      } else if (e.toolbar.id == 'inactive') {
        this.handleUpdateStatus(_id, 3, '确认进行失效操作？关联的字典明细也将失效')
      } else if (e.toolbar.id === 'json_import') {
        this.$dialog({
          modal: () => import('./component/importDialog.vue'),
          data: {
            title: this.$t('导入JSON')
          },
          success: (res) => {
            this.$toast({
              content: res.msg ? res.msg : this.$t('导入成功'),
              type: 'success'
            })
            this.confirmSuccess()
          }
        })
      } else if (e.toolbar.id === 'json_export') {
        this.$API.dict
          .exportDataJson(_id)
          .then((res) => {
            const { code, data } = res
            if (code === 200) {
              this.$dialog({
                data: {
                  title: this.$t('复制到剪贴板(原有剪贴板内容将会被覆盖)'),
                  // message: '复制当前导出内容将会覆盖原有剪贴板内容，您是否确认复制到剪贴板？'
                  message: data
                },
                success: () => {
                  this.copyToClipboard(JSON.stringify(data))
                  this.$toast({
                    content: res.message ? res.message : this.$t('复制成功'),
                    type: 'success'
                  })
                }
              })
            }
          })
          .catch((err) => {
            console.log(err)
          })
      }

      if (e.toolbar.id === 'import') {
        this.$refs.uploader.open()
      }
      if (e.toolbar.id === 'export') {
        this.handleExport()
        return
      }
    },

    copyToClipboard(str) {
      const el = document.createElement('textarea')
      el.value = str
      el.setAttribute('readonly', '')
      el.style.position = 'absolute'
      el.style.left = '-9999px'
      document.body.appendChild(el)
      el.select()
      document.execCommand('copy')
      document.body.removeChild(el)
    },

    handleExport() {
      //导出
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 1000 },
        pageFlag: true,
        rules: rule.rules || []
      }
      this.$API.dict
        .exportDict(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .catch((err) => {
          console.log(err)
        })
    },
    handleAdd() {
      this.addDialogShow = true
      this.dialogData = {
        dialogType: 'add',
        requestUrl: 'addDictTypeData'
      }
    },

    handleEdit(row) {
      this.$API.dict.getTenantDictByIdI18n({ id: row.id }).then((res) => {
        if (res.code == 200) {
          this.addDialogShow = true
          this.dialogData = {
            dialogType: 'edit',
            requestUrl: 'editDictTypeData',
            row: res.data
          }
        }
      })
    },

    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('确认删除？')
        },
        success: () => {
          this.$loading()
          this.$API.baseMainData
            .deleteDictTypeData({ idList: ids })
            .then((res) => {
              this.$hloading()
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.confirmSuccess()
              }
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },

    handleUpdateStatus(ids, flag, msg) {
      this.$dialog({
        data: {
          title: this.$t('操作'),
          message: msg
        },
        success: () => {
          this.$loading()
          this.$API.baseMainData
            .updateDictTypeStatus({ ids: ids, statusId: flag })
            .then((res) => {
              this.$hloading()
              if (res.code == 200) {
                this.confirmSuccess()
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
              }
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },

    handleClickCellTool(e) {
      if (e.tool.id == 'delete') {
        this.handleDelete([e.data.id])
      } else if (e.tool.id == 'edit') {
        let _row = e.data
        delete _row.cellTools
        delete _row.column
        delete _row.gridTemplate
        this.handleEdit(_row)
      } else if (e.tool.id == 'addChild') {
        this.showDefault = true
        this.rowInfo = e.data
        this.addDetailKey++
      }
    },

    confirmSuccess() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    handleAddDialogShow(flag) {
      this.addDialogShow = flag
    },

    uploadSuccess() {
      // this.$refs.uploader.save()
      this.$refs.templateRef.refreshCurrentGridData()
    }
  }
}
</script>

<style lang="scss">
.side-detail {
  width: 80% !important;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .info {
    width: 100%;
    ul {
      display: flex;
      li {
        flex: 1;
        display: flex;
      }
    }
  }

  .set-country {
    flex: 1;
  }
}
</style>
