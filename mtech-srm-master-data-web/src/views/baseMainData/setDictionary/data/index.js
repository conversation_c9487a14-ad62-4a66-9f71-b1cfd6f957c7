import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-10-21 10:06:19
 * @LastEditTime: 2021-12-06 15:27:26
 * @LastEditors: your name
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\baseMainData\setDictionary\data\index.js
 */
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '250',
    field: 'dictCode',
    headerText: i18n.t('代码'),
    cellTools: [
      'Edit',
      'Delete',
      {
        id: 'addChild',
        icon: 'icon_solid_Createorder',
        title: i18n.t('新增明细')
      }
    ]
  },
  {
    width: '250',
    field: 'dictName',
    headerText: i18n.t('中文名称')
  },
  {
    width: '250',
    field: 'dictEnglishName',
    headerText: i18n.t('英文名称')
  },
  {
    width: '200',
    field: 'dictTypeDescription',
    headerText: i18n.t('类型')
  },
  {
    field: 'statusId',
    headerText: i18n.t('状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('草稿'),
        1: i18n.t('激活'),
        2: i18n.t('分发'),
        3: i18n.t('失效'),
        '-1': i18n.t('待审核'),
        '-2': i18n.t('准备')
      }
    }
  },
  {
    width: 'auto',
    field: 'dictDescription',
    headerText: i18n.t('说明')
  }
  // {
  //   // width: "150",
  //   field: "updateTime",
  //   headerText: i18n.t("修改时间"),
  // }
]
