<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="addForm" :rules="rules">
      <mt-form-item prop="dictCode" :label="$t('代码')">
        <mt-input
          v-model.trim="addForm.dictCode"
          :show-clear-button="true"
          type="text"
          :disabled="dialogData && dialogData.dialogType == 'edit'"
          :placeholder="$t('请输入代码')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="dictType" :label="$t('类型')">
        <mt-select
          v-model.trim="addForm.dictType"
          :data-source="detailTypeList"
          :show-clear-button="true"
          :fields="{ text: 'label', value: 'value' }"
          :disabled="dialogData && dialogData.dialogType == 'edit'"
          :placeholder="$t('请选择类型')"
        ></mt-select>
      </mt-form-item>

      <mt-form-item prop="dictName" :label="$t('名称')">
        <template>
          <mt-multilingual-input
            ref="multilingualtor"
            v-model.trim="addForm.dictName"
            group-code="master-data-management"
            :disabled="false"
            :placeholder="$t('请输入名称')"
          >
          </mt-multilingual-input>
        </template>
        <!-- <mt-input
          v-model.trim="addForm.dictName"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入名称')"
        ></mt-input> -->
      </mt-form-item>

      <!-- <mt-form-item prop="dictEnglishName" :label="$t('英文名称')">
        <mt-input
          v-model.trim="addForm.dictEnglishName"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入英文名称')"
        ></mt-input>
      </mt-form-item> -->

      <mt-form-item prop="dictDescription" :label="$t('说明')" class="fullWidth">
        <mt-input
          v-model.trim="addForm.dictDescription"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入说明')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import common from '@/utils/constant'
import { formatRules } from '@/utils/util'
import MtMultilingualInput from '@/components/multilingual-input/src'

export default {
  components: {
    MtMultilingualInput
  },
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      addForm: {
        dictCode: '',
        dictName: '',
        dictEnglishName: '',
        dictDescription: '',
        dictType: ''
      },
      rules: {},
      statusList: common.statusList,
      detailTypeList: [
        {
          label: this.$t('列表'),
          value: 0
        },
        {
          label: this.$t('层级'),
          value: 1
        }
      ]
    }
  },

  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.$refs.ruleForm.resetFields()
    if (this.dialogData?.row) {
      this.addForm = JSON.parse(JSON.stringify(this.dialogData?.row))
    }
    if (this.dialogData?.dialogType == 'add') {
      this.dialogTitle = this.$t('新增')
    } else {
      this.dialogTitle = this.$t('编辑')
    }
    this.getRules()
  },

  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },

    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = {
            applicationId: common.applicationId,
            ...this.addForm
          }
          // console.log('11111111111', this.$refs.multilingualtor)
          params.dictEnglishName = this.$refs.multilingualtor?.ruleForm?.en
          let _request = this.dialogData?.requestUrl
          console.log(this.dialogData, _request)
          this.$API.baseMainData[_request](params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('handleAddDialogShow', false)
              this.$emit('confirmSuccess')
            }
          })
        }
      })
    },

    handleClose() {
      this.$emit('handleAddDialogShow', false)
    },
    getRules() {
      if (this.dialogData?.dialogType == 'add') {
        this.$API.baseMainData.getAddRulesDictType().then((res) => {
          if (res.code == 200) this.rules = formatRules(res.data)
        })
      } else {
        this.$API.baseMainData.getUpdateRulesDictType().then((res) => {
          if (res.code == 200) this.rules = formatRules(res.data)
        })
      }
    }
  }
}
</script>

<style lang="scss">
@import '../../../../../node_modules/@digis/multilingual-input/build/esm/bundle.css';
</style>
