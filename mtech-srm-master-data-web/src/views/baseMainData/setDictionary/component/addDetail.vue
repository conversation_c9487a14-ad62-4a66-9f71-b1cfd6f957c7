<template>
  <mt-side-bar
    position="Right"
    ref="sidebar"
    :show-backdrop="true"
    :close-on-document-click="false"
    class="side-detail"
  >
    <div class="right-btn">
      <div class="left-title">{{ $t('新增明细') }}</div>
      <mt-icon name="icon_Close_1" @click.native="handleClose"></mt-icon>
    </div>

    <div class="blue-title">{{ $t('基础信息') }}</div>

    <div class="info">
      <ul>
        <li>
          <div class="left-label">{{ $t('字典类型代码') }}：</div>
          <div class="right-val">
            {{ (rowInfo && rowInfo.dictCode) || '-' }}
          </div>
        </li>
        <li>
          <div class="left-label">{{ $t('字典类型名称') }}：</div>
          <div class="right-val">
            {{ (rowInfo && rowInfo.dictName) || '-' }}
          </div>
        </li>
        <li>
          <div class="left-label">{{ $t('字典类型英文名称') }}：</div>
          <div class="right-val">
            {{ (rowInfo && rowInfo.dictEnglishName) || '-' }}
          </div>
        </li>
      </ul>
    </div>

    <div class="blue-title mt0">{{ $t('明细列表') }}</div>

    <diction-detail v-if="isDetailShow" :row-info="rowInfo"></diction-detail>
    <div style="height: 30px"></div>
  </mt-side-bar>
</template>

<script>
import MtSideBar from '@mtech-ui/side-bar'

export default {
  components: {
    MtSideBar,
    dictionDetail: require('../../setDictionaryDetail/index.vue').default
  },
  props: {
    rowInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      dictionKey: 1,
      isDetailShow: false
    }
  },
  mounted() {
    this.$refs.sidebar.show()
    this.$nextTick(() => {
      this.isDetailShow = true
    })
  },
  methods: {
    handleClose() {
      this.$refs.sidebar.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
.side-detail {
  width: 80% !important;
  top: 60px;
  height: calc(100% - 60px);
  overflow: auto;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px 0 0 0;

  .right-btn {
    width: 100%;
    font-size: 16px;
    line-height: 1;
    padding: 14px 20px;
    background: rgba(243, 243, 243, 1);
    color: #292929;
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      cursor: pointer;
    }
    .mt-icons {
      font-size: 14px;
      cursor: pointer;
    }
  }

  .info {
    width: 100%;
    padding: 0 40px;
    font-size: 15px;
    ul {
      display: flex;
      li {
        flex: 1;
        display: flex;
        .left-label {
          font-weight: bold;
        }
      }
    }
  }

  .set-country {
    flex: 1;
    .common-template-page {
      background: transparent;

      .page-grid-container {
        box-shadow: unset;
      }

      .e-grid {
        flex: 1;
        height: auto !important;
      }
    }
  }

  .blue-title {
    font-size: 14px;
    padding-left: 8px;
    margin: 20px;
    position: relative;
    color: #2f353c;
    &::before {
      content: '';
      width: 3px;
      height: 70%;
      position: absolute;
      left: 0;
      top: 15%;
      background: rgba(0, 70, 156, 1);
      border-radius: 5px 0 0 5px;
    }

    &.mt0 {
      margin-bottom: 0;
    }
  }
}
</style>
