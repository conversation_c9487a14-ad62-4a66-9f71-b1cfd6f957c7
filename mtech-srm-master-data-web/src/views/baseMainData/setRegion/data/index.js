import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2022-01-06 14:21:44
 * @LastEditTime: 2022-02-28 16:45:23
 * @LastEditors: your name
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\baseMainData\setRegion\data\index.js
 */
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'regionCode',
    headerText: i18n.t('代码'),
    cellTools: ['Edit', 'Delete']
  },
  {
    width: '200',
    field: 'regionName',
    headerText: i18n.t('名称')
  },
  {
    width: '200',
    field: 'regionDescription',
    headerText: i18n.t('说明')
  },
  {
    width: '200',
    field: 'statusId',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('草稿'),
        1: i18n.t('激活'),
        2: i18n.t('分发'),
        3: i18n.t('失效'),
        '-1': i18n.t('待审核'),
        '-2': i18n.t('准备')
      }
    }
  },
  {
    width: 'auto',
    field: 'updateTime',
    headerText: i18n.t('修改时间')
  }
]
