<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="addForm" :rules="rules">
      <mt-form-item prop="areaCode" :label="$t('代码')">
        <mt-input
          v-model.trim="addForm.areaCode"
          :show-clear-button="true"
          type="text"
          :disabled="dialogData && dialogData.dialogType == 'edit'"
          :placeholder="$t('请输入代码')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="areaName" :label="$t('名称')">
        <mt-input
          v-model.trim="addForm.areaName"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入名称')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="countryId" :label="$t('国家')">
        <mt-select
          v-model="addForm.countryId"
          :data-source="countryList"
          :allow-filtering="true"
          :disabled="true"
          :fields="{ text: 'shortName', value: 'id' }"
          :show-clear-button="true"
          :placeholder="$t('请选择国家')"
        ></mt-select>
      </mt-form-item>

      <mt-form-item prop="areaLevelId" :label="$t('类型')">
        <mt-select
          v-model="addForm.areaLevelId"
          :data-source="areatypeList"
          :fields="{ text: 'itemName', value: 'id' }"
          :show-clear-button="true"
          :disabled="dialogData && dialogData.dialogType == 'edit'"
          :placeholder="$t('请选择类型')"
        ></mt-select>
      </mt-form-item>

      <mt-form-item prop="timeZoneId" :label="$t('时区')">
        <mt-select
          v-model="addForm.timeZoneId"
          :data-source="timeZoneList"
          :fields="{ text: 'timeZoneName', value: 'id' }"
          :show-clear-button="true"
          :placeholder="$t('请选择时区')"
        ></mt-select>
      </mt-form-item>

      <!-- <mt-form-item prop="statusId" :label="$t('状态')">
        <mt-select
          v-model.number="addForm.statusId"
          :data-source="statusList"
          :fields="{ text: 'label', value: 'value' }"
          :show-clear-button="true"
          :placeholder="$t('请选择状态')"
        ></mt-select>
      </mt-form-item> -->

      <mt-form-item prop="telephoneCode" :label="$t('电话区号')">
        <mt-input
          v-model.trim="addForm.telephoneCode"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入电话区号')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="lon" :label="$t('经度')">
        <mt-input
          v-model.trim="addForm.lon"
          :show-clear-button="true"
          type="number"
          :placeholder="$t('请输入经度')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="lat" :label="$t('纬度')">
        <mt-input
          v-model.trim="addForm.lat"
          :show-clear-button="true"
          type="number"
          :placeholder="$t('请输入纬度')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="zipCode" :label="$t('邮编')">
        <mt-input
          v-model.trim="addForm.zipCode"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入邮编')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import common from '@/utils/constant'
import { formatRules } from '@/utils/util'
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      addForm: {
        areaCode: '',
        areaName: '',
        areaLevelId: '',
        countryId: '',
        telephoneCode: '',
        zipCode: '',
        lon: '',
        lat: '',
        timeZoneId: ''
      },
      rules: {
        // regionCode: [
        //   { required: true, message: this.$t("请输入中文名称"), trigger: "blur" }
        // ]
      },
      statusList: common.statusList,
      countryList: [],
      areatypeList: [],
      timeZoneList: []
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.$refs.ruleForm.resetFields()
    if (this.dialogData?.row) {
      this.addForm = JSON.parse(JSON.stringify(this.dialogData?.row))
    }
    if (this.dialogData?.dialogType == 'add') {
      this.dialogTitle = this.$t('新增')
    } else {
      this.dialogTitle = this.$t('编辑')
    }
    this.getAllData()
    this.getRules()
  },

  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },

    getAllData() {
      this.$API.baseMainData.getAllCountry().then((res) => {
        this.countryList = res.data
        // 默认中国
        this.addForm.countryId = this.countryList.filter((item) =>
          item.shortName.includes('中国')
        )[0].id
      })

      this.$API.baseMainData.getAllAreaTypeOrUnit({ dictCode: 'areaType' }).then((res) => {
        this.areatypeList = res.data
      })

      this.$API.baseMainData.getAllTimeZone().then((res) => {
        this.timeZoneList = res.data
      })
    },

    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = {
            applicationId: common.applicationId,
            ...this.addForm
          }
          let _request = this.dialogData?.requestUrl
          console.log(this.dialogData, _request)
          this.$API.baseMainData[_request](params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('handleAddDialogShow', false)
              this.$emit('confirmSuccess')
            }
          })
        }
      })
    },

    handleClose() {
      this.$emit('handleAddDialogShow', false)
    },

    getRules() {
      if (this.dialogData?.dialogType == 'add') {
        this.$API.baseMainData.getAddRulesArea().then((res) => {
          if (res.code == 200) this.rules = formatRules(res.data)
        })
      } else {
        this.$API.baseMainData.getUpdateRulesArea().then((res) => {
          if (res.code == 200) this.rules = formatRules(res.data)
        })
      }
    }
  }
}
</script>

<style></style>
