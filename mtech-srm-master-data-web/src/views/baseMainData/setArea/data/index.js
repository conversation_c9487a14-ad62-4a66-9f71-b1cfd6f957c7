import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '180',
    field: 'areaCode',
    headerText: i18n.t('代码'),
    cellTools: ['Edit', 'Delete']
  },
  {
    width: '150',
    field: 'areaName',
    headerText: i18n.t('名称')
  },
  {
    width: '100',
    field: 'areaLevelDescription',
    headerText: i18n.t('类型')
  },
  {
    width: '150',
    field: 'countryName',
    headerText: i18n.t('所属国家')
  },
  {
    width: '150',
    field: 'wholeName',
    headerText: i18n.t('完整名称')
  },
  {
    width: '150',
    field: 'telephoneCode',
    headerText: i18n.t('电话区号')
  },
  {
    width: '150',
    field: 'zipCode',
    headerText: i18n.t('邮编')
  },
  {
    width: '150',
    field: 'lon',
    headerText: i18n.t('经度')
  },
  {
    width: '150',
    field: 'lat',
    headerText: i18n.t('纬度')
  },
  {
    width: '150',
    field: 'timeZoneName',
    headerText: i18n.t('时区')
  },
  {
    field: 'statusId',
    headerText: i18n.t('状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('草稿'),
        1: i18n.t('激活'),
        2: i18n.t('分发'),
        3: i18n.t('失效'),
        '-1': i18n.t('待审核'),
        '-2': i18n.t('准备')
      }
    }
  },
  {
    width: '150',
    field: 'updateTime',
    headerText: i18n.t('修改时间')
  }
]
