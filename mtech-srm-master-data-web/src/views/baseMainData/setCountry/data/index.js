import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-10-21 10:06:19
 * @LastEditTime: 2021-12-30 09:50:56
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\baseMainData\setCountry\data\index.js
 */
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'countryCode',
    headerText: i18n.t('代码')
  },
  {
    width: '150',
    field: 'shortName',
    headerText: i18n.t('中文名称')
  },
  {
    width: '150',
    field: 'englishShortName',
    headerText: i18n.t('英文名称')
  },
  {
    width: '150',
    field: 'alpha2Code',
    headerText: i18n.t('二代字母代码')
  },
  {
    width: '150',
    field: 'numericCode',
    headerText: i18n.t('数字代码')
  },
  {
    field: 'statusId.value',
    headerText: i18n.t('状态'),
    width: '150',
    ignore: true,
    allowFiltering: false,
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('草稿'),
        1: i18n.t('激活'),
        2: i18n.t('分发'),
        3: i18n.t('失效'),
        '-1': i18n.t('待审核'),
        '-2': i18n.t('准备')
      }
    }
  },
  // {
  //   // width: "150",
  //   field: "updateTime",
  //   headerText: "修改时间"
  // },
  {
    width: 'auto',
    field: 'createTime',
    headerText: i18n.t('创建时间')
  }
]
