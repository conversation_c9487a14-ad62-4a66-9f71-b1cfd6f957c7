<!--
 * @Author: your name
 * @Date: 2021-10-21 10:06:19
 * @LastEditTime: 2021-12-06 15:24:00
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\baseMainData\setCountry\index.vue
-->
<template>
  <div class="set-country">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>

    <dialog-uploader
      ref="uploader"
      :template-file-config="templateFileConfig"
      :async-settings="asyncSettings"
      @success="success"
    ></dialog-uploader>
  </div>
</template>

<script>
import common from '@/utils/constant'
import { columnData } from './data'
import DialogUploader from '@/components/common/Uploader.vue'
export default {
  components: {
    DialogUploader
  },
  data() {
    return {
      currentTabIndex: 0,
      queryBuilderRules: null,
      pageConfig: [
        {
          frozenColumns: 1,
          useBaseConfig: false,
          toolbar: [
            [
              {
                id: 'import',
                icon: 'icon_solid_Import',
                title: this.$t('导入'),
                visibleCondition: () => true
              },
              {
                id: 'export',
                icon: 'icon_solid_pushorder',
                title: this.$t('导出'),
                visibleCondition: () => false
              }
            ],
            ['Filter', 'Refresh', 'Setting']
          ],
          grid: {
            columnData: columnData,
            dataSource: [],
            frozenColumns: 1,
            asyncConfig: {
              url: `${common.PROXY_BASE}/country/paged-query`
            }
          }
        }
      ],

      templateFileConfig: {
        templateId: this.$t('国家主数据导入模板')
      },
      asyncSettings: {
        saveUrl: `/api${common.PROXY_BASE}/country/import-data`
      }
    }
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar } = e

      if (toolbar.id === 'import') {
        this.$refs.uploader.open()
      }
      if (toolbar.id === 'export') {
        this.$refs.uploader.open()
      }
    },
    success() {
      this.$refs.uploader.save()
      this.$refs.templateRef.refreshCurrentGridData()
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
