<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="addForm" :rules="rules">
      <mt-form-item prop="taxItemCode" :label="$t('代码')">
        <mt-input
          v-model.trim="addForm.taxItemCode"
          :show-clear-button="true"
          type="text"
          :disabled="dialogData && dialogData.dialogType == 'edit'"
          :placeholder="$t('请输入代码')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="taxItemName" :label="$t('名称')">
        <mt-input
          v-model.trim="addForm.taxItemName"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入名称')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="countryId" :label="$t('国家')">
        <mt-select
          v-model="addForm.countryId"
          :data-source="countryList"
          :allow-filtering="true"
          :disabled="dialogData && dialogData.dialogType == 'edit'"
          :fields="{ text: 'shortName', value: 'id' }"
          :show-clear-button="true"
          @change="handleCountryChange"
          :placeholder="$t('请选择国家')"
        ></mt-select>
      </mt-form-item>

      <mt-form-item prop="taxTypeId" :label="$t('税种')">
        <mt-select
          v-model="addForm.taxTypeId"
          :data-source="taxList"
          :fields="{ text: 'typeName', value: 'id' }"
          :show-clear-button="true"
          :disabled="dialogData && dialogData.dialogType == 'edit'"
          :placeholder="$t('请选择税种')"
        ></mt-select>
      </mt-form-item>

      <mt-form-item prop="taxRate" :label="$t('汇率（%）')">
        <mt-input-number
          v-model.number="addForm.taxRate"
          :disabled="false"
          :show-clear-button="true"
          :placeholder="$t('请输入税率')"
        ></mt-input-number>
      </mt-form-item>

      <mt-form-item prop="taxItemDescription" :label="$t('描述')" class="fullWidth">
        <mt-input
          v-model.trim="addForm.taxItemDescription"
          :disabled="false"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入描述')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import common from '@/utils/constant'
import { formatRules } from '@/utils/util'
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      addForm: {
        taxItemCode: '',
        taxItemName: '',
        taxTypeId: '',
        taxRate: null,
        countryId: '',
        taxItemDescription: ''
      },
      rules: {
        // taxRate: [
        //   {
        //     type: "number",
        //     message: "请输入number税种的",
        //     trigger: "blur"
        //   }
        // ]
      },
      statusList: common.statusList,
      taxList: [],
      countryList: []
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.$refs.ruleForm.resetFields()
    if (this.dialogData?.row) {
      this.addForm = JSON.parse(JSON.stringify(this.dialogData?.row))
    }
    this.getAllCountry()
    // this.getAllTax();  // 先选国家再选税种

    if (this.dialogData?.dialogType == 'add') {
      this.dialogTitle = this.$t('新增')
    } else {
      this.dialogTitle = this.$t('编辑')
    }
    this.getRules()
  },

  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    getAllCountry() {
      this.$API.baseMainData.getAllCountry().then((res) => {
        this.countryList = res.data
      })
    },
    getAllTax(countryId) {
      this.$API.baseMainData.getTaxByCountry(countryId).then((res) => {
        this.taxList = res.data
      })
    },
    confirm() {
      // console.log("rules:", this.rules);
      // console.log("addForm: ", this.addForm);
      // console.log(this.$t("税种是"), typeof this.addForm.taxRate);
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = {
            applicationId: common.applicationId,
            ...this.addForm
          }
          let _request = this.dialogData?.requestUrl
          console.log(this.dialogData, _request)
          this.$API.baseMainData[_request](params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('handleAddDialogShow', false)
              this.$emit('confirmSuccess')
            }
          })
        }
      })
    },

    handleCountryChange(e) {
      console.log(this.$t('国家改变了'), e)
      if (e.value) {
        this.getAllTax(e.value)
      } else {
        this.taxList = []
      }
    },

    handleClose() {
      this.$emit('handleAddDialogShow', false)
    },

    getRules() {
      if (this.dialogData?.dialogType == 'add') {
        this.$API.baseMainData.getAddRulesTaxItem().then((res) => {
          if (res.code == 200) this.rules = formatRules(res.data)
          console.log('校验规则：', this.rules)
        })
      } else {
        this.$API.baseMainData.getUpdateRulesTaxItem().then((res) => {
          if (res.code == 200) this.rules = formatRules(res.data)
          console.log('校验规则：', this.rules)
        })
      }
    }
  }
}
</script>

<style></style>
