import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-10-21 10:06:19
 * @LastEditTime: 2021-12-06 15:30:21
 * @LastEditors: your name
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\baseMainData\setTaxCategory\data\index.js
 */
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    // width: "200",
    field: 'typeCode',
    headerText: i18n.t('代码'),
    cellTools: [
      'Edit',
      'Delete'
      // {
      //   id: "active",
      //   icon: "icon_Activation",
      //   title: i18n.t("激活"),
      //   visibleCondition: data => {
      //     console.log("data", data);
      //     return data["statusId"] == 3;
      //   }
      // },
      // {
      //   id: "inactive",
      //   icon: "icon_solid_Disable1",
      //   title: i18n.t("失效"),
      //   visibleCondition: data => {
      //     return data["statusId"] == 1;
      //   }
      // }
    ]
  },
  {
    // width: "200",
    field: 'typeName',
    headerText: i18n.t('名称')
  },
  {
    // width: "200",
    field: 'countryName',
    headerText: i18n.t('所属国家')
  },
  {
    // width: "200",
    field: 'typeDescription',
    headerText: i18n.t('说明')
  },
  {
    field: 'statusId',
    headerText: i18n.t('状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('草稿'),
        1: i18n.t('激活'),
        2: i18n.t('分发'),
        3: i18n.t('失效'),
        '-1': i18n.t('待审核'),
        '-2': i18n.t('准备')
      }
    }
  },
  {
    width: 'auto',
    field: 'updateTime',
    headerText: i18n.t('修改时间')
  }
]
