<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="addForm" :rules="rules">
      <mt-form-item prop="typeCode" :label="$t('代码')">
        <mt-input
          v-model.trim="addForm.typeCode"
          :show-clear-button="true"
          type="text"
          :disabled="dialogData && dialogData.dialogType == 'edit'"
          :placeholder="$t('请输入代码')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="typeName" :label="$t('名称')">
        <mt-input
          v-model.trim="addForm.typeName"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入名称')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="countryId" :label="$t('所属国家')">
        <mt-select
          v-model="addForm.countryId"
          :data-source="countryList"
          :allow-filtering="true"
          :disabled="dialogData && dialogData.dialogType == 'edit'"
          :fields="{ text: 'shortName', value: 'id' }"
          :show-clear-button="true"
          :placeholder="$t('请选择所属国家')"
        ></mt-select>
      </mt-form-item>

      <mt-form-item prop="typeDescription" :label="$t('描述')" class="fullWidth">
        <mt-input
          v-model.trim="addForm.typeDescription"
          :disabled="false"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入描述')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import common from '@/utils/constant'
import { formatRules } from '@/utils/util'
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      addForm: {
        typeCode: '',
        typeName: '',
        countryId: '',
        typeDescription: ''
      },
      rules: {
        // typeCode: [
        //   { required: true, message: this.$t("请输入中文名称"), trigger: "blur" }
        // ]
      },
      statusList: common.statusList,
      countryList: []
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.$refs.ruleForm.resetFields()
    if (this.dialogData?.row) {
      this.addForm = JSON.parse(JSON.stringify(this.dialogData?.row))
    }

    this.getAllCountry()

    if (this.dialogData?.dialogType == 'add') {
      this.dialogTitle = this.$t('新增')
    } else {
      this.dialogTitle = this.$t('编辑')
    }
    this.getRules()
  },

  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    getAllCountry() {
      this.$API.baseMainData.getAllCountry().then((res) => {
        this.countryList = res.data
        console.log(this.countryList)
      })
    },

    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = {
            applicationId: common.applicationId,
            ...this.addForm
          }
          let _request = this.dialogData?.requestUrl
          console.log(this.dialogData, _request)
          this.$API.baseMainData[_request](params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('handleAddDialogShow', false)
              this.$emit('confirmSuccess')
            }
          })
        }
      })
    },

    handleClose() {
      this.$emit('handleAddDialogShow', false)
    },

    getRules() {
      if (this.dialogData?.dialogType == 'add') {
        this.$API.baseMainData.getAddRulesTaxCategory().then((res) => {
          if (res.code == 200) this.rules = formatRules(res.data)
          this.rules.effectiveTime = [
            {
              required: true,
              message: this.$t('请选择有效时间'),
              trigger: 'blur'
            }
          ]
        })
      } else {
        this.$API.baseMainData.getUpdateRulesTaxCategory().then((res) => {
          if (res.code == 200) this.rules = formatRules(res.data)
          this.rules.effectiveTime = [
            {
              required: true,
              message: this.$t('请选择有效时间'),
              trigger: 'blur'
            }
          ]
        })
      }
    }
  }
}
</script>

<style></style>
