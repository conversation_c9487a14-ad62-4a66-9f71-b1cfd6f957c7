import { i18n } from '@/main.js'
export const columnData = [
  // {
  //   width: "100",
  //   type: "checkbox",
  //   showInColumnChooser: false,
  //   textAlign:"center"
  // },

  {
    width: 200,
    field: 'itemCode',
    headerText: i18n.t('代码'),
    textAlign: 'left',
    cssClass: 'codeStyle',
    showCheckbox: true,
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_table_edit',
        title: i18n.t('编辑')
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除')
      },
      {
        id: 'addNext',
        icon: 'icon_Activation',
        title: i18n.t('新增下级'),
        visibleCondition: (data) => {
          return data['dictType'] == 1
        }
      }
    ]
  },
  {
    width: '70',
    field: 'sortNo',
    headerText: i18n.t('序号')
  },
  {
    // width: "200",
    field: 'itemName',
    headerText: i18n.t('中文名称')
  },
  {
    // width: "200",
    field: 'itemEnglishName',
    headerText: i18n.t('英文名称')
  },
  // {
  //   // width: "200",
  //   field: "dictCode",
  //   headerText: "字典类型代码"
  // },
  // {
  //   // width: "200",
  //   field: "dictName",
  //   headerText: "字典类型名称"
  // },
  {
    // width: "200",
    field: 'defaultDescription',
    headerText: i18n.t('是否默认')
  },
  {
    // width: "150",
    field: 'itemDescription',
    headerText: i18n.t('说明')
  },
  {
    field: 'statusId',
    headerText: i18n.t('状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('草稿'),
        1: i18n.t('激活'),
        2: i18n.t('分发'),
        3: i18n.t('失效'),
        '-1': i18n.t('待审核'),
        '-2': i18n.t('准备')
      }
    }
  },
  {
    // width: "150",
    field: 'updateTime',
    headerText: i18n.t('修改时间')
  }
]
