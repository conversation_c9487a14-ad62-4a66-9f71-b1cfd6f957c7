<template>
  <div class="set-country">
    <mt-template-page
      ref="templateDetailRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>

    <add-dialog
      v-if="addDialogShow"
      :dialog-data="dialogData"
      @handleAddDialogShow="handleAddDialogShow"
      @confirmSuccess="confirmSuccess"
    ></add-dialog>
  </div>
</template>

<script>
// import common from '@/utils/constant'
import { columnData } from './data'
export default {
  props: {
    rowInfo: {
      type: Object,
      default: () => {}
    }
  },
  components: {
    addDialog: require('./component/addDialog.vue').default
  },
  data() {
    return {
      dataSource: [],
      currentTabIndex: 0,
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: {
            tools: []
          },
          treeGrid: {
            autoCheckHierarchy: true,
            allowPaging: false,
            childMapping: 'children',
            columnData: columnData,
            dataSource: []
            // asyncConfig: {
            //   url: `${common.PROXY_BASE}/dict-item/paged-query`,
            //   defaultRules: [
            //     {
            //       field: "dictCode",
            //       operator: "equal",
            //       type: "string",
            //       value: this.rowInfo?.dictCode,
            //     },
            //   ],
            // },
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },
  created() {
    this.setTools()
  },
  mounted() {
    this.getData()
  },

  methods: {
    setTools() {
      this.$API.org.getUserInfo({}).then((resData) => {
        const { tenantId } = resData.data
        let tools = [
          'Add',
          'Delete',
          {
            id: 'active',
            icon: 'icon_Activation',
            title: this.$t('激活')
          },
          {
            id: 'inactive',
            icon: 'icon_solid_Disable1',
            title: this.$t('失效')
          }
        ]
        if (tenantId === '-99') {
          tools.push(
            {
              id: 'json_import',
              icon: 'icon_solid_Import',
              title: this.$t('导入(JSON)')
              // permission: ['O_02_1336'],
            },
            {
              id: 'json_export',
              icon: 'icon_solid_pushorder',
              title: this.$t('导出(JSON)')
              // permission: ['O_02_1337'],
            }
          )
        }
        this.pageConfig[this.currentTabIndex].toolbar.tools.push(tools)
      })
    },
    getData() {
      this.$API.dict
        .TenantDictTreeGet({ dictCode: this.rowInfo?.dictCode })
        .then((res) => {
          if (res.data) {
            this.$set(this.pageConfig[0].treeGrid, 'dataSource', res.data)
          } else {
            this.$set(this.pageConfig[0].treeGrid, 'dataSource', [])
          }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    handleClickToolBar(e) {
      console.log('handleClickToolBar1=', e)
      let _checkedRecords = []
      if (Object.hasOwnProperty.call(e, 'grid')) {
        _checkedRecords = e.grid.getSelectedRecords()
      } else {
        _checkedRecords = e.treeGrid.getCheckedRecords()
      }
      if (
        _checkedRecords.length <= 0 &&
        (e.toolbar.id == 'Delete' ||
          e.toolbar.id == 'active' ||
          e.toolbar.id == 'inactive' ||
          e.toolbar.id == 'json_export')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _id = []
      _checkedRecords.map((item) => _id.push(item.id))
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'Delete') {
        this.handleDelete(_id)
      } else if (e.toolbar.id == 'active') {
        this.handleUpdateStatus(_id, 1, '确认进行激活操作？关联的数据也将被激活')
      } else if (e.toolbar.id == 'inactive') {
        this.handleUpdateStatus(_id, 3, '确认进行失效操作？关联的数据也将失效')
      } else if (e.toolbar.id === 'json_import') {
        this.$dialog({
          modal: () => import('./component/importDialog.vue'),
          data: {
            title: this.$t('导入JSON'),
            data: this.rowInfo
          },
          success: (res) => {
            this.$toast({
              content: res.msg ? res.msg : this.$t('导入成功'),
              type: 'success'
            })
            this.confirmSuccess()
          }
        })
      } else if (e.toolbar.id === 'json_export') {
        this.$API.dict
          .exportItemDataJson(_id)
          .then((res) => {
            const { code, data } = res
            if (code === 200) {
              this.$dialog({
                data: {
                  title: this.$t('复制到剪贴板(原有剪贴板内容将会被覆盖)'),
                  // message: '复制当前导出内容将会覆盖原有剪贴板内容，您是否确认复制到剪贴板？'
                  message: data
                },
                success: () => {
                  this.copyToClipboard(JSON.stringify(data))
                  this.$toast({
                    content: res.message ? res.message : this.$t('复制成功'),
                    type: 'success'
                  })
                }
              })
            }
          })
          .catch((err) => {
            console.log(err)
          })
      }
    },

    copyToClipboard(str) {
      const el = document.createElement('textarea')
      el.value = str
      el.setAttribute('readonly', '')
      el.style.position = 'absolute'
      el.style.left = '-9999px'
      document.body.appendChild(el)
      el.select()
      document.execCommand('copy')
      document.body.removeChild(el)
    },

    handleAdd() {
      this.addDialogShow = true
      this.dialogData = {
        dialogType: 'add',
        requestUrl: 'addDictData',
        dictId: this.rowInfo?.id
      }
    },

    handleEdit(row, type) {
      this.addDialogShow = true
      this.dialogData = {
        dialogType: type,
        requestUrl: type == 'edit' ? 'editDictData' : 'addDictData',
        row: row,
        dictId: this.rowInfo?.id
      }
    },

    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('确认删除？')
        },
        success: () => {
          this.$loading()
          this.$API.baseMainData
            .deleteDictData({ idList: ids })
            .then((res) => {
              this.$hloading()
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.confirmSuccess()
              }
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },

    handleUpdateStatus(ids, flag, msg) {
      this.$dialog({
        data: {
          title: this.$t('操作'),
          message: msg
        },
        success: () => {
          this.$loading()
          this.$API.baseMainData
            .updateDictStatus({ ids: ids, statusId: flag })
            .then((res) => {
              this.$hloading()
              if (res.code == 200) {
                this.confirmSuccess()
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
              }
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },

    handleClickCellTool(e) {
      console.log(e)
      if (e.tool.id == 'delete') {
        this.handleDelete([e.data.id])
      } else if (e.tool.id == 'edit') {
        this.$API.dict.getDictItemByIdI18n({ id: e.data.id }).then((res) => {
          if (res.code == 200) {
            this.handleEdit(res.data, 'edit')
          }
        })
      } else if (e.tool.id == 'addNext') {
        let _row = {
          parentId: e.data.id,
          dictId: e.data.dictId,
          dictName: e.data.dictName
        }
        this.handleEdit(_row, 'addNext')
      }
    },

    confirmSuccess() {
      // this.$refs.templateDetailRef.refreshCurrentGridData();
      this.getData()
    },
    handleAddDialogShow(flag) {
      this.addDialogShow = flag
    }
  }
}
</script>

<style lang="scss">
/deep/ .grid-edit-column {
  padding: 0 !important;
}
.codeStyle {
  padding-left: 30px;
}
</style>
