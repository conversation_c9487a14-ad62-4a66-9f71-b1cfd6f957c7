<template>
  <!-- @beforeClose="cancel" -->
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form class="dialog-mt-form" ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="remark" :label="$t('导入内容')">
          <mt-input
            type="text"
            :multiline="true"
            :rows="20"
            v-model="formObject.remark"
            :placeholder="$t('请输入导入内容')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      //v-model获取的值
      formObject: {
        remark: '' // 导入内容
      },
      //必填项
      formRules: {
        remark: [
          {
            required: true,
            message: this.$t('请输入导入内容'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    headStates() {
      return this.modalData.headStates
    },
    salesData() {
      return this.modalData.data
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    //点击确认
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          try {
            const params = {
              dictId: this.salesData.id,
              ...JSON.parse(this.formObject.remark)
            }
            if (params instanceof Array || params instanceof Object) {
              this.$API.dict
                .importItemDataJson(params)
                .then((res) => {
                  const { code } = res
                  if (code === 200) {
                    this.$emit('confirm-function', res)
                  }
                })
                .catch((err) => {
                  console.log(err)
                })
            } else {
              this.$toast({
                content: this.$t('导入内容数据格式有误'),
                type: 'warning'
              })
            }
          } catch {
            this.$toast({
              content: this.$t('导入内容数据格式有误'),
              type: 'warning'
            })
          }
        }
      })
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
// .dialog-main {
//   .e-dlg-content {
//     // padding: 0;
//     .dialog-content {
//       padding: 40px;
//       .mt-form {
//         display: flex;
//         justify-content: space-between;
//         align-items: center;
//         flex-wrap: wrap;
//         .mt-form-item {
//           width: 100%;
//         }
//       }
//     }
//   }
// }
</style>
