<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="addForm" :rules="rules">
      <mt-form-item prop="itemCode" :label="$t('代码')">
        <mt-input
          v-model.trim="addForm.itemCode"
          :show-clear-button="true"
          type="text"
          :disabled="dialogData && dialogData.dialogType == 'edit'"
          :placeholder="$t('请输入代码')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="itemName" :label="$t('名称')">
        <mt-multilingual-input
          v-model.trim="addForm.itemName"
          :show-clear-button="true"
          group-code="master-data-management"
          :disabled="false"
          :placeholder="$t('请输入名称')"
        >
        </mt-multilingual-input>
        <!-- <mt-input
          v-model.trim="addForm.itemName"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入名称')"
        ></mt-input> -->
      </mt-form-item>

      <!-- <mt-form-item prop="itemEnglishName" :label="$t('英文名称')">
        <mt-input
          v-model.trim="addForm.itemEnglishName"
          :disabled="false"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入英文名称')"
        ></mt-input>
      </mt-form-item> -->

      <mt-form-item prop="defaultValue" :label="$t('是否默认')">
        <mt-select
          v-model.number="addForm.defaultValue"
          :data-source="defaultValueList"
          :fields="{ text: 'label', value: 'value' }"
          :show-clear-button="true"
          :placeholder="$t('请选择是否默认')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="sortNo" :label="$t('序号')">
        <mt-input
          v-model.number="addForm.sortNo"
          :show-clear-button="true"
          :placeholder="$t('请输入序号')"
        ></mt-input>
      </mt-form-item>

      <!-- <mt-form-item prop="dictId" :label="$t('字典类型')">
        <mt-select
          v-if="dialogData && dialogData.dialogType != 'add'"
          :disabled="true"
          v-model="addForm.dictId"
          :data-source="dictList"
          :placeholder="$t('请选择字典类型')"
        ></mt-select>

        <combine-select-datagrid
          v-else
          :dict-id="addForm.dictId"
          :disabled="dialogData && dialogData.dialogType == 'addNext'"
          :column-data="columnData"
          :placeholder="$t('请选择状态')"
          @rowSelect="handleRowSelect"
        ></combine-select-datagrid>
      </mt-form-item> -->

      <mt-form-item prop="itemDescription" :label="$t('说明')" class="fullWidth">
        <mt-input
          v-model.trim="addForm.itemDescription"
          :disabled="false"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入说明')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import common from '@/utils/constant'
import { formatRules } from '@/utils/util'
import { columnData } from '../../setDictionary/data'
import MtMultilingualInput from '@/components/multilingual-input/src'

export default {
  components: {
    MtMultilingualInput
  },
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      addForm: {
        itemCode: '',
        itemName: '',
        itemEnglishName: '',
        sortNo: '',
        // dictId: "",
        defaultValue: 0,
        itemDescription: ''
      },
      rules: {},
      showParentSelect: false, // 根据选择的类型来
      dictByParentList: [],
      dictList: [],

      columnData,
      dictTypeData: [],
      statusList: common.statusList,
      defaultValueList: [
        {
          label: this.$t('否'),
          value: 0
        },
        {
          label: this.$t('是'),
          value: 1
        }
      ]
    }
  },

  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.$refs.ruleForm.resetFields()
    if (this.dialogData?.row) {
      console.log(this.dialogData.row)
      // if (this.dialogData.dialogType != "add") {
      //   this.dictList = [
      //     {
      //       text: this.dialogData?.row?.dictName,
      //       value: this.dialogData?.row?.dictId
      //     }
      //   ];
      // }
      console.log('dictList', this.dictList)
      this.addForm = JSON.parse(JSON.stringify(this.dialogData?.row))
    }
    if (this.dialogData?.dialogType == 'add') {
      this.dialogTitle = this.$t('新增')
    } else if (this.dialogData?.dialogType == 'edit') {
      this.dialogTitle = this.$t('编辑')
    } else {
      this.dialogTitle = this.$t('新增')
      this.addForm.defaultValue = 0
    }
    this.getRules()
  },

  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = {
            applicationId: common.applicationId,
            dictId: this.dialogData?.dictId,
            ...this.addForm
          }
          let _request = this.dialogData?.requestUrl
          console.log(this.dialogData, _request)
          this.$API.baseMainData[_request](params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('handleAddDialogShow', false)
              this.$emit('confirmSuccess')
            }
          })
        }
      })
    },

    handleClose() {
      this.$emit('handleAddDialogShow', false)
    },

    handleRowSelect(row) {
      console.log('选中的id', row, row?.id)
      // this.addForm.dictId = row?.id;
      // this.$set(this.addForm, "dictId", row?.id);
      if (row.dictType == 1 && row?.id) {
        this.showParentSelect = true
        this.$API.baseMainData.getDictItemByParentId({ id: row?.id }).then((res) => {
          this.dictByParentList = res.data
        })
      } else {
        this.showParentSelect = false
      }
    },

    getRules() {
      if (this.dialogData?.dialogType != 'edit') {
        this.$API.baseMainData.getAddRulesDict().then((res) => {
          if (res.code == 200) this.rules = formatRules(res.data)
          console.log(this.$t('校验规则'), this.rules)
        })
      } else {
        this.$API.baseMainData.getUpdateRulesDict().then((res) => {
          console.log(res, 222222)
          if (res.code == 200) this.rules = formatRules(res.data)
          console.log(this.$t('校验规则'), this.rules)
        })
      }
    }
  }
}
</script>

<style lang="scss">
@import '../../../../../node_modules/@digis/multilingual-input/build/esm/bundle.css';
</style>
