<template>
  <div class="set-country">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>

    <add-dialog
      v-if="addDialogShow"
      :dialog-data="dialogData"
      @handleAddDialogShow="handleAddDialogShow"
      @confirmSuccess="confirmSuccess"
    ></add-dialog>
  </div>
</template>

<script>
import common from '@/utils/constant'
import { columnData } from './data'
export default {
  components: {
    addDialog: require('./component/addUnit.vue').default
  },
  data() {
    return {
      currentTabIndex: 0,
      pageConfig: [
        {
          toolbar: {
            tools: [
              [
                'Add',
                'Delete',
                {
                  id: 'active',
                  icon: 'icon_Activation',
                  title: this.$t('激活')
                },
                {
                  id: 'inactive',
                  icon: 'icon_solid_Disable1',
                  title: this.$t('失效')
                },
                {
                  id: 'import',
                  icon: 'icon_solid_Import',
                  title: this.$t('导入'),
                  visibleCondition: () => false
                },
                {
                  id: 'export',
                  icon: 'icon_solid_pushorder',
                  title: this.$t('导出'),
                  visibleCondition: () => false
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: columnData,
            dataSource: [],
            frozenColumns: 1,
            asyncConfig: {
              url: `${common.PROXY_BASE}/unit/paged-query?BU_CODE=${localStorage.getItem(
                'currentBu'
              )}`
            }
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    handleClickToolBar(e) {
      console.log(e.grid.getSelectedRecords(), e)
      if (
        e.grid.getSelectedRecords().length <= 0 &&
        !(
          e.toolbar.id == 'Add' ||
          e.toolbar.id == 'Filter' ||
          e.toolbar.id == 'Refresh' ||
          e.toolbar.id == 'Setting'
        )
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _id = []
      e.grid.getSelectedRecords().map((item) => _id.push(item.id))
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'Delete') {
        this.handleDelete(_id)
      } else if (e.toolbar.id == 'active') {
        this.handleUpdateStatus(_id, 1, this.$t('确认进行激活操作？'))
      } else if (e.toolbar.id == 'inactive') {
        this.handleUpdateStatus(_id, 3, this.$t('确认进行失效操作？'))
      }
    },

    handleAdd() {
      this.addDialogShow = true
      this.dialogData = {
        dialogType: 'add',
        requestUrl: 'addUnitData'
      }
    },

    handleEdit(row) {
      this.addDialogShow = true
      this.dialogData = {
        dialogType: 'edit',
        requestUrl: 'editUnitData',
        row: row
      }
    },

    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('确认删除？')
        },
        success: () => {
          this.$loading()
          this.$API.baseMainData
            .deleteUnitData({ ids: ids })
            .then((res) => {
              this.$hloading()
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.confirmSuccess()
              }
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },

    handleUpdateStatus(ids, flag, msg) {
      this.$dialog({
        data: {
          title: this.$t('操作'),
          message: msg
        },
        success: () => {
          this.$loading()
          this.$API.baseMainData
            .updateUnitStatus({ ids: ids, statusId: flag })
            .then((res) => {
              this.$hloading()
              if (res.code == 200) {
                this.confirmSuccess()
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
              }
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },

    handleClickCellTool(e) {
      console.log(e)
      if (e.tool.id == 'delete') {
        this.handleDelete([e.data.id])
      } else if (e.tool.id == 'edit') {
        let _row = e.data
        delete _row.cellTools
        delete _row.column
        delete _row.gridTemplate
        delete _row.gridRef
        delete _row.tabIndex
        this.handleEdit(_row)
      }
    },

    confirmSuccess() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    handleAddDialogShow(flag) {
      this.addDialogShow = flag
    }
  }
}
</script>

<style></style>
