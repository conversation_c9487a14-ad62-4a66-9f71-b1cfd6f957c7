<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="addForm" :rules="rules">
      <mt-form-item prop="unitCode" :label="$t('代码')">
        <mt-input
          v-model.trim="addForm.unitCode"
          :show-clear-button="true"
          type="text"
          :disabled="dialogData && dialogData.dialogType == 'edit'"
          :placeholder="$t('请输入代码')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="typeId" :label="$t('类型')">
        <mt-select
          v-model="addForm.typeId"
          :data-source="allUnitList"
          :fields="{ text: 'itemName', value: 'id' }"
          :show-clear-button="true"
          :disabled="dialogData && dialogData.dialogType == 'edit'"
          :placeholder="$t('请选择类型')"
        ></mt-select>
      </mt-form-item>

      <mt-form-item prop="unitName" :label="$t('中文名称')">
        <mt-input
          v-model.trim="addForm.unitName"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入中文名称')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="unitEnglishName" :label="$t('英文名称')">
        <mt-input
          v-model.trim="addForm.unitEnglishName"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入英文名称')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="unitSymbol" :label="$t('符号')">
        <mt-input
          v-model.trim="addForm.unitSymbol"
          :disabled="false"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入符号')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="unitDescription" :label="$t('描述')" class="fullWidth">
        <mt-input
          v-model.trim="addForm.unitDescription"
          :disabled="false"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入描述')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import common from '@/utils/constant'
import { formatRules } from '@/utils/util'
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      addForm: {
        unitCode: '',
        unitName: '',
        unitEnglishName: '',
        typeId: '',
        unitSymbol: '',
        unitDescription: ''
      },
      rules: {},
      statusList: common.statusList,
      allUnitList: []
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.$refs.ruleForm.resetFields()
    if (this.dialogData?.row) {
      this.addForm = JSON.parse(JSON.stringify(this.dialogData?.row))
    }
    this.getCommonUnit()
    if (this.dialogData?.dialogType == 'add') {
      this.dialogTitle = this.$t('新增')
    } else {
      this.dialogTitle = this.$t('编辑')
    }
    this.getRules()
  },

  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    getCommonUnit() {
      this.$API.baseMainData.getAllAreaTypeOrUnit({ dictCode: 'unit' }).then((res) => {
        console.log(res)
        this.allUnitList = res.data
      })
    },

    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = {
            applicationId: common.applicationId,
            ...this.addForm
          }
          let _request = this.dialogData?.requestUrl
          console.log(this.dialogData, _request)
          this.$API.baseMainData[_request](params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('handleAddDialogShow', false)
              this.$emit('confirmSuccess')
            }
          })
        }
      })
    },

    handleClose() {
      this.$emit('handleAddDialogShow', false)
    },

    getRules() {
      if (this.dialogData?.dialogType == 'add') {
        this.$API.baseMainData.getAddRulesUnit().then((res) => {
          if (res.code == 200) this.rules = formatRules(res.data)
        })
      } else {
        this.$API.baseMainData.getUpdateRulesUnit().then((res) => {
          if (res.code == 200) this.rules = formatRules(res.data)
        })
      }
    }
  }
}
</script>

<style></style>
