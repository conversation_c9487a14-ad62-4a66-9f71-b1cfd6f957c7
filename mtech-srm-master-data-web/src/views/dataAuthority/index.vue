<template>
  <div class="grid-box">
    <mt-horizontal-list
      :data-source="pageConfig"
      :hidden-tabs="true"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @handleSelectTab="handleSelectTab"
      @handleGridCurrentChange="handleGridCurrentChange"
      @handleGridSizeChange="handleGridSizeChange"
    ></mt-horizontal-list>
  </div>
</template>

<script>
import { columnDataMain } from './data'
export default {
  data() {
    return {
      pageConfig: [
        {
          title: this.$t('供应商邀请'),
          toolbar: [
            [
              {
                id: 'Claim',
                icon: 'icon_solid_Createproject',
                title: this.$t('新增')
              },
              {
                id: 'Distribute',
                icon: 'delete-05',
                title: this.$t('删除')
              },
              {
                id: 'Distribute',
                icon: 'M_Properties',
                title: this.$t('操作')
              }
            ],
            ['Filter', 'Refresh', 'Setting']
          ],
          grid: {
            columnData: columnDataMain(this.emitCheckBox, this.emitRadio),
            dataSource: [],
            pageSettings: {
              pageSizes: [10, 50, 100, 200],
              totalRecordsCount: 0,
              pageSize: 10
            }
          }
        }
      ],
      currentTabIndex: 0,
      tab0PageInfo: {
        size: 10,
        current: 1
      },
      tab1PageInfo: {
        size: 10,
        current: 1
      }
    }
  },
  mounted() {
    this.getTabData()
  },
  methods: {
    emitCheckBox(data) {
      console.log(data)
    },
    emitRadio(data, d) {
      console.log(data, d)
    },
    getTabData() {
      this.$set(this.pageConfig[0].grid, 'dataSource', [
        {
          inviteCode: this.$t('供应商管理'),
          inviteRound: this.$t('供应商申请管理'),
          supplierName: this.$t('供应商列表'),
          porName: this.$t('生产商，供应商'),
          lineMan: this.$t('李先生'),
          lineNum: '<EMAIL>',
          purName: 'ceshiceshi',
          inviteLink: 'www.xxx.com',
          createMan: 'ceshim',
          createTime: new Date(),
          alist: [
            { label: this.$t('创建人'), id: 1 },
            { label: this.$t('采购组织'), id: 2 },
            { label: this.$t('采购组'), id: 3 },
            { label: this.$t('公司'), id: 4 }
          ],
          blist: [
            {
              label: this.$t('直接下属'),
              value: '0'
            },
            {
              label: this.$t('所有下属'),
              value: '1'
            }
          ]
        },
        {
          inviteCode: this.$t('供应商邀请管理'),
          inviteRound: this.$t('供应商邀请管理'),
          supplierName: this.$t('供应商列表'),
          porName: this.$t('生产商，供应商'),
          lineMan: this.$t('李先生'),
          lineNum: '<EMAIL>',
          purName: 'ceshiceshi',
          inviteLink: 'www.xxx.com',
          createMan: 'ceshim',
          createTime: new Date(),
          alist: [
            { label: this.$t('创建人'), id: 1 },
            { label: this.$t('采购组织'), id: 2 },
            { label: this.$t('采购组'), id: 3 },
            { label: this.$t('公司'), id: 4 }
          ],
          blist: [
            {
              label: this.$t('直接下属'),
              value: '0'
            },
            {
              label: this.$t('所有下属'),
              value: '1'
            }
          ]
        }
      ])
      console.log('getTabData--------------')
    },

    // -----------------页码--------------------------
    handleGridCurrentChange(e) {
      console.log('页码改变，', e, e.currentPage)
      // this[`tab${this.currentTabIndex}PageInfo`].current = e.currentPage;
      // this.getTabData();
    },
    // 每页size改变
    handleGridSizeChange(e) {
      console.log('页码size改变，', e)
      // this[`tab${this.currentTabIndex}PageInfo`].size = e.count;
      // this.getTabData();
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      // if (e.tool.id == "edit") {
      //     //编辑操作
      //     this.handleEditGroupConfig(e.data);
      // } else if (e.tool.id == "start") {
      //     //启用操作
      //     this.handleUpdateConfigStatus([e.data.id], 1);
      // } else if (e.tool.id == "stop") {
      //     //停用操作
      //     this.handleUpdateConfigStatus([e.data.id], 2);
      // } else if (e.tool.id == "delete") {
      //     this.handleDeleteConfig([e.data.id]);
      // }
    },

    //单元格标题点击操作
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
    },

    // -----------------顶部按钮--------------------------
    handleClickToolBar(e) {
      console.log(e, e.grid.getSelectedRecords())
      if (e.toolbar.id === 'addNew') {
        return
      }

      if (
        e.grid.getSelectedRecords().length <= 0 &&
        !(e.toolbar.id == 'Filter' || e.toolbar.id == 'Refresh' || e.toolbar.id == 'Setting')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      // 已认领的不能点击 分配、认领按钮
      let _claimStatus = [],
        _id = []

      e.grid.getSelectedRecords().map((item) => {
        _id.push(item.id), _claimStatus.push(item.claimStatus)
      })
      // if (e.toolbar.id == "Claim") {

      // }
    },

    // 弹窗：认领点击
    handleClaim(ids, claimStatus) {
      if (claimStatus && claimStatus.length > 0 && claimStatus.some((item) => item == 2)) {
        this.$toast({
          content: this.$t('已认领的需求无法进行该操作'),
          type: 'warning'
        })
        return
      }
      // let requestUrl =
      //     this.currentTabIndex == 0 ? "claimById" : "claimByIdDetail";
      // this.$dialog({
      //     data: {
      //         title: this.$t("认领"),
      //         message: "是否确认认领？",
      //         confirm: () =>
      //             this.$API.source[requestUrl]({ idList: ids }),
      //     },
      //     success: () => {
      //         this.getTabData();
      //     },
      // });
    },

    // 弹窗：分配点击
    handleDistribute(ids, claimStatus) {
      if (claimStatus && claimStatus.length > 0 && claimStatus.some((item) => item == 2)) {
        this.$toast({
          content: this.$t('已认领的需求无法进行该操作'),
          type: 'warning'
        })
        return
      }
      let _distributeArr = this.tenantUserList.map((item) => {
        let _email = item.email ? ' - ' + item.email : ''
        let _phoneNum = item.phoneNum ? ' - ' + item.phoneNum : ''
        return {
          text: item.employeeName + ' - ' + item.employeeCode + _email + _phoneNum,
          value: item.id
        }
      })
      this.$dialog({
        data: {
          title: this.$t('分配'),
          distributeArr: _distributeArr,
          requestUrl: this.currentTabIndex == 0 ? 'distributeById' : 'distributeByIdDetail',
          ids
        },
        success: () => {
          this.getTabData()
        }
      })
    },

    // 刷新页面
    handleRefresh() {
      this[`tab${this.currentTabIndex}PageInfo`].current = 1
      this.getTabData()
    },
    // -----------------tab页签--------------------------
    handleSelectTab(e) {
      console.log(e)
      this.currentTabIndex = e
      this.getTabData()
    }
  }
}
</script>

<style lang="scss" scoped>
.grid-box {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border: 1px solid #e8e8e8;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  overflow: hidden;
  height: 100%;
  background: #fff;
}
</style>
<style lang="scss">
.operate-box {
  .operator-btn {
    margin-right: 10px;
  }
}
</style>
