import { i18n } from '@/main.js'
import Vue from 'vue'
const Mixins = {
  data() {
    const _this = this
    return {
      columnData: [
        {
          width: '50',
          type: 'checkbox',
          showInColumnChooser: false
        },
        {
          field: 'a',
          headerText: i18n.t('一级菜单'),
          textAlign: 'left'
        },
        {
          field: 'b',
          headerText: i18n.t('二级菜单'),
          textAlign: 'left'
        },
        {
          field: 't',
          headerText: i18n.t('tab名称'),
          textAlign: 'left'
        },
        {
          field: 'dataAuthority',
          headerText: i18n.t('数据权限控制'),
          textAlign: 'left',
          width: '260',
          template: () => {
            return {
              template: Vue.component('checkbox-list', {
                template: `
                                  <div class="checkbox-list" v-if="!!data.alist && data.alist.length > 0">
                                    <mt-checkbox-group v-model="groupVal" :disabled="false">
                                        <mt-checkbox
                                            v-for="item in data.alist"
                                            :key="item.id"
                                            :content="item"
                                            class="group-item"
                                        ></mt-checkbox>
                                    </mt-checkbox-group>
                                  </div>`,
                data: function () {
                  return {
                    groupVal: []
                  }
                },
                watch: {
                  groupVal(newValue) {
                    _this.emitCheckBox(newValue)
                  }
                }
              })
            }
          }
        },
        {
          field: 'upAuthority',
          headerText: i18n.t('上级数据权限'),
          textAlign: 'left',
          width: '200',
          template: () => {
            return {
              template: Vue.component('checkbox-list', {
                template: `
                                  <div class="checkbox-list" v-if="!!data.blist && data.blist.length > 0">
                                    <mt-radio v-model="radioVal" :dataSource="data.blist" @input="onchang"></mt-radio>
                                  </div>`,
                data: function () {
                  return {
                    radioVal: ''
                  }
                },
                methods: {
                  onchang(v) {
                    _this.emitRadio(this.radioVal, v)
                  }
                }
              })
            }
          }
        },
        {
          field: 'upDataMan',
          headerText: i18n.t('更新人'),
          textAlign: 'left',
          width: '100'
        },
        {
          field: 'upDataTime',
          headerText: i18n.t('更新时间'),
          textAlign: 'left',
          width: '160'
        }
      ]
    }
  }
}
export default Mixins
