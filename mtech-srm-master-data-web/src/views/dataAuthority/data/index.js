import { i18n } from '@/main.js'
import Vue from 'vue'
// const statusArr = [
//   'normal',
//   'green',
//   'red',
//   'disabled'
// ]
export const columnDataMain = (emitCheckBox, emitRadio) => {
  return [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '210',
      field: 'inviteCode',
      cssClass: 'field-content',
      headerText: i18n.t('一级菜单'),
      cellTools: []
    },
    {
      width: '120',
      field: 'inviteRound',
      headerText: i18n.t('二级菜单')
    },
    {
      width: '210',
      field: 'supplierName',
      headerText: i18n.t('tab名称')
    },
    {
      field: 'dataAuthority',
      headerText: i18n.t('数据权限控制'),
      textAlign: 'left',
      width: '400',
      template: () => {
        return {
          template: Vue.component('checkbox-list', {
            template: `
                    <div class="checkbox-list" v-if="!!data.alist && data.alist.length > 0">
                      <mt-checkbox-group class=" mt-flex" v-model="groupVal" :disabled="false">
                          <mt-checkbox
                              v-for="item in data.alist"
                              :key="item.id"
                              :content="item"
                              class="group-item"
                          ></mt-checkbox>
                      </mt-checkbox-group>
                    </div>`,
            data: function () {
              return {
                groupVal: []
              }
            },
            watch: {
              groupVal(v) {
                emitCheckBox(v)
              }
            }
          })
        }
      }
    },
    {
      field: 'upAuthority',
      headerText: i18n.t('上级数据权限'),
      textAlign: 'left',
      width: '200',
      template: () => {
        return {
          template: Vue.component('checkbox-list', {
            template: `
                    <div class="checkbox-list" v-if="!!data.blist && data.blist.length > 0">
                      <mt-radio v-model="radioVal" :dataSource="data.blist" @input="onchang"></mt-radio>
                    </div>`,
            data: function () {
              return {
                radioVal: ''
              }
            },
            methods: {
              onchang(v) {
                emitRadio(this.radioVal, v)
              }
            }
          })
        }
      }
    },
    {
      width: '190',
      field: 'purName',
      headerText: i18n.t('备注')
    },
    {
      width: '150',
      field: 'createTime',
      headerText: i18n.t('创建时间'),
      queryType: 'date',
      format: 'yyyy-MM-dd hh:mm:ss'
    }
    // {
    //   width: "150",
    //   field: "remark",
    //   headerText: i18n.t("操作"),
    //   template: () => {
    //     // let _this = this
    //     return {
    //       template: Vue.component("operate-template", {
    //         template: `
    //                 <div class="operate-box mt-flex">
    //                   <div class="operator-btn" @click.stop="inviteEdit">{{ $t("编辑") }}</div>
    //                   <div class="operator-btn" @click.stop="inviteDelete">{{ $t("删除") }}</div>
    //                 </div>`,
    //         data: function () {
    //           return { data: {} };
    //         },
    //         methods: {
    //           // 新增下级
    //           inviteEdit() {
    //             let { data } = this
    //             console.log(data)
    //           },
    //           inviteDelete() {
    //             let { data } = this
    //             console.log(data)
    //           }
    //         }
    //       }),
    //     };
    //   },
    // },
  ]
}

export const columnDateOrination = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    // width: "220",
    field: 'c1',
    type: 'string',
    headerText: i18n.t('公司代码')
  },
  {
    width: '380',
    field: 'c2',
    type: 'string',
    headerText: i18n.t('公司名称')
  },
  {
    width: '380',
    field: 'c3',
    type: 'string',
    headerText: i18n.t('上级公司')
  },
  {
    width: '220',
    field: 'c4',
    type: 'string',
    headerText: i18n.t('更新时间')
  }
]

export const columnDateSupplier = (deleteItemFun) => {
  return [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '300',
      field: 'c1',
      type: 'string',
      headerText: i18n.t('公司')
    },
    {
      width: '300',
      field: 'c2',
      type: 'string',
      headerText: i18n.t('工厂')
    },
    {
      width: '300',
      field: 'c3',
      type: 'string',
      headerText: i18n.t('品类名称')
    },
    {
      // width: "220",
      type: 'string',
      headerText: i18n.t('操作'),
      template: () => {
        return {
          template: Vue.component('operate-template', {
            template: `
                <div class="operate-box mt-flex">
                  <div class="operator-btn" @click.stop="deleteItem">{{ $t("删除") }}</div>
                </div>`,
            data: function () {
              return { data: {} }
            },
            methods: {
              // 新增下级
              deleteItem() {
                let { data } = this
                deleteItemFun()
                console.log(data)
              }
            }
          })
        }
      }
    }
  ]
}

// 明细
export const columnDataDetail = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'historyPriceNum',
    headerText: i18n.t('历史价格记录'),
    cellTools: [
      { id: 'Claim', icon: 'icon_solid_Submit', title: '认领' },
      { id: 'Distribute', icon: 'icon_solid_export', title: '分配' }
    ],
    type: 'number'
  },
  {
    width: '150',
    field: 'claimStatus',
    headerText: i18n.t('认领状态'),
    type: 'string',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未认领'),
        2: i18n.t('已认领')
      }
    },
    queryType: 'number',
    queryTemplate: {
      type: 'select',
      options: {
        ds: [
          { label: i18n.t('未认领'), value: 0 },
          { label: i18n.t('已认领'), value: 2 }
        ]
      }
    }
  },
  {
    width: '150',
    field: 'status',
    headerText: i18n.t('单据状态'),
    type: 'string',
    valueConverter: {
      type: 'map',
      map: {
        '-1': i18n.t('关闭'),
        0: i18n.t('草稿'),
        1: i18n.t('已提交'),
        2: i18n.t('发布'),
        8: i18n.t('已完成')
      }
    },
    queryType: 'string',
    queryTemplate: {
      type: 'select',
      options: {
        ds: [
          { label: i18n.t('关闭'), value: '-1' },
          { label: i18n.t('草稿'), value: 0 },
          { label: i18n.t('已提交'), value: 1 },
          { label: i18n.t('发布'), value: 2 },
          { label: i18n.t('已完成'), value: 8 }
        ]
      }
    }
  },
  {
    width: '150',
    field: 'sourceHeaderCode',
    type: 'string',
    headerText: i18n.t('源单据编码')
  },
  {
    width: '150',
    field: 'sourceHeaderName',
    headerText: i18n.t('源单据名称')
  },
  {
    width: '150',
    field: 'porCode',
    headerText: i18n.t('单据编码')
  },
  {
    width: '150',
    field: 'porName',
    headerText: i18n.t('单据名称')
  },
  { width: '150', field: 'lineNo', headerText: '行号' },
  { width: '150', field: 'itemCode', headerText: '品项代码' },
  { width: '150', field: 'itemName', headerText: '品项名称' },
  { width: '150', field: 'categoryName', headerText: '品类' },
  {
    width: '150',
    field: 'siteName',
    headerText: i18n.t('地点/工厂')
  },
  { width: '150', field: 'lineNo', headerText: '库存地点' },
  {
    width: '150',
    field: 'quantity',
    headerText: i18n.t('数量'),
    type: 'number'
  },
  { width: '150', field: 'unitName', headerText: '基本单位' },
  {
    width: '150',
    field: 'requiredDeliveryDate',
    headerText: i18n.t('要求交货日期'),
    queryType: 'date',
    format: 'yyyy-MM-dd'
  },
  { width: '150', field: 'purGroupName', headerText: '采购组' },
  { width: '150', field: 'itemName', headerText: '业务类型' },
  {
    width: '150',
    field: 'companyCode',
    headerText: i18n.t('公司代码')
  },
  {
    width: '150',
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    width: '150',
    field: 'deptName',
    headerText: i18n.t('申请部门')
  },
  {
    width: '150',
    field: 'purName',
    headerText: i18n.t('申请人')
  },
  {
    width: '150',
    field: 'applyTime',
    headerText: i18n.t('申请时间'),
    queryType: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

// 分配弹窗的人员
export const distributeArr = [
  {
    id: 1,
    name: i18n.t('林'),
    account: 'lin',
    email: '<EMAIL>',
    phone: '***********'
  },
  {
    id: 2,
    name: i18n.t('林高'),
    account: 'lin',
    email: '<EMAIL>',
    phone: '***********'
  },
  {
    id: 3,
    name: i18n.t('高林高子'),
    account: 'lin',
    email: '<EMAIL>',
    phone: '***********'
  }
]

// 历史价格弹窗
export const historyPriceColumnData = [
  {
    width: '200',
    field: 'time',
    headerText: i18n.t('价格时间'),
    valueConverter: {
      type: 'date',
      format: 'yyyy-MM-dd hh:mm:ss'
    }
  },
  {
    width: '150',
    field: 'price',
    headerText: i18n.t('价格')
  },
  {
    width: '150',
    field: 'currencyName',
    headerText: i18n.t('币种')
  },
  {
    width: '150',
    field: 'supplierName',
    headerText: i18n.t('供应商')
  }
]

export const historyPriceSource = [
  {
    field1: '2021/06/02  12:12:12',
    field2: '12',
    field3: i18n.t('比特币'),
    field4: i18n.t('供应商')
  }
]
