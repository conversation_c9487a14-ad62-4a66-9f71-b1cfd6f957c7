import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2022-03-15 15:47:25
 * @LastEditTime: 2022-03-16 17:47:17
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\custom\config\custom.config.js
 */
export const PAGE_CONFIG = [
  {
    toolbar: [
      'Add',
      'Delete',
      {
        id: 'Active',
        icon: 'icon_solid_Createproject',
        title: i18n.t('激活')
      },
      {
        id: 'Negative',
        icon: 'icon_solid_Createproject',
        title: i18n.t('失效')
      }
    ],
    grid: {
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'customerCode',
          headerText: i18n.t('客户编码'),
          width: '200',
          cellTools: ['edit', 'delete', 'preview']
        },
        {
          field: 'customerName',
          width: '200',
          headerText: i18n.t('客户名称')
        },
        {
          field: 'partnerCode',
          width: '200',
          headerText: i18n.t('BP代码')
        },
        {
          field: 'statusId',
          headerText: i18n.t('状态'),
          width: '150',
          valueConverter: {
            type: 'map',
            map: {
              0: i18n.t('草稿'),
              1: i18n.t('激活'),
              2: i18n.t('分发'),
              3: i18n.t('失效'),
              '-1': i18n.t('待审核'),
              '-2': i18n.t('准备')
            }
          }
        },
        {
          field: 'customerDescription',
          width: '150',
          headerText: i18n.t('描述')
        },
        {
          field: 'customerExternalCode',
          headerText: i18n.t('第三方编码'),
          width: 'auto'
        }
      ],
      asyncConfig: {
        url: '/masterDataManagement/tenant/customer/paged-query',
        params: {}
      }
    }
  }
]

export const FIELDS = {
  BP: {
    text: 'partnerName',
    value: 'partnerCode'
  }
}
export const RULES = {}

export const OPTIONS = {
  BP: []
}
