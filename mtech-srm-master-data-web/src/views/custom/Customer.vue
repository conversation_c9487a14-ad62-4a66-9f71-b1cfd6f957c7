<!--
 * @Author: your name
 * @Date: 2022-03-15 15:46:36
 * @LastEditTime: 2022-03-20 16:49:23
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\custom\Customer.vue
-->
<template>
  <div class="full-height mt-pt-20">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
    <mt-dialog ref="dialog" :header="headerTitle" css-class="create-proj-dialog" :buttons="buttons">
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="customerCode" :label="$t('客户编码')">
          <mt-input
            v-model="ruleForm.customerCode"
            :disabled="type !== 'add'"
            type="text"
            :placeholder="$t('请输入客户编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="customerName" :label="$t('客户名称')">
          <mt-input
            v-model="ruleForm.customerName"
            :disabled="disabled"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入客户名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('公司')" prop="organizationId">
          <mt-select
            :disabled="type !== 'add'"
            v-model="ruleForm.organizationId"
            :data-source="orgList"
            :fields="{ text: 'orgName', value: 'id' }"
            :placeholder="$t('请选择公司')"
            @change="changeOrg"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('关联供应商')" prop="partnerCode">
          <mt-select
            :disabled="disabled"
            v-model="ruleForm.partnerCode"
            :data-source="fuzzyList"
            :allow-filtering="true"
            :show-clear-button="true"
            :filtering="onSiteFilter"
            :fields="{ text: 'codeName', value: 'partnerCode' }"
            :placeholder="$t('请选择关联供应商')"
          ></mt-select>
        </mt-form-item>
        <!-- <mt-form-item prop="partnerCode" label="BP">
          <mt-select
            v-model="ruleForm.partnerCode"
            :disabled="disabled"
            :fields="fields.BP"
            :data-source="options.BP"
            :show-clear-button="true"
            :allow-filtering="true"
            :filtering="bpGet"
            :placeholder="$t('请选择业务伙伴')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="customerExternalCode" :label="$t('第三方编码')">
          <mt-input
            v-model="ruleForm.customerExternalCode"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入第三方编码')"
          ></mt-input>
        </mt-form-item> -->
        <mt-form-item prop="customerDescription" :label="$t('描述')">
          <mt-input
            v-model="ruleForm.customerDescription"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入描述')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import { PAGE_CONFIG, RULES, OPTIONS, FIELDS } from './config/custom.config'
import { debounce, formatRules } from '@/utils/util'
export default {
  data() {
    return {
      options: OPTIONS,
      pageConfig: PAGE_CONFIG,
      rules: RULES,
      fields: FIELDS,

      pageInfo: {
        current: 1,
        size: 10
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      orgList: [],
      fuzzyList: [],
      input: '',
      ruleForm: {
        id: '',
        customerCode: '',
        customerName: '',
        organizationId: '',
        partnerCode: '',
        customerDescription: '',
        organizationCode: ''
      },
      type: 'add', // add edit preview
      changeStatus: false
    }
  },
  computed: {
    headerTitle() {
      if (this.type === 'add') {
        return this.$t('新增')
      }
      if (this.type === 'edit') {
        return this.$t('编辑')
      }
      return this.$t('预览')
    },
    disabled() {
      if (this.type === 'preview') {
        return true
      }
      return false
    }
  },
  mounted() {
    this.getChildrenCompanyOrganization()
    this.onSiteFilter = debounce(this.onSiteFilter, 1000)
  },
  methods: {
    getChildrenCompanyOrganization() {
      this.$loading()
      this.$API.customer['getChildrenCompanyOrganization2']({
        organizationLevelCodes: ['ORG02', 'ORG01'],
        orgType: 'ORG001PRO',
        includeItself: true
      })
        .then((result) => {
          this.$hloading()
          if (result.data) {
            this.orgList = result.data.filter((item) => {
              return item.orgLevelTypeCode === 'ORG02' || item.orgLevelTypeCode === 'ORG01'
            })
          } else {
            this.orgList = []
          }
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    changeOrg(e) {
      if (this.changeStatus) {
        this.ruleForm.partnerCode = ''
        this.fuzzyList = []
      } else {
        this.changeStatus = true
      }
      let { itemData } = e
      this.ruleForm.organizationCode = itemData.orgCode
      this.ruleForm.organizationId = e.value
      this.fuzzyQuery()
    },
    async fuzzyQuery() {
      let obj = {
        fuzzyNameOrCode: this.input,
        inStatus: [1, 2, 10],
        organizationCode: this.ruleForm.organizationCode
      }
      await this.$API.customer.criteriaQuerySupplier(obj).then((res) => {
        res.data.forEach((item) => {
          item.codeName = item.supplierCode + '-' + item.supplierName
        })
        this.fuzzyList = res.data
      })
    },
    async onSiteFilter(e) {
      this.input = e.text
      await this.fuzzyQuery()
      e.updateData(this.fuzzyList)
      // e.updateData(this.fuzzyList.filter((x) => x.siteName.includes(e.text)));
    },
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const rowSelected = grid.getSelectedRecords()
      if (toolbar.id === 'Add') {
        this.handleAction('add')
        this.rulesGet('customerAddValid')
        this.bpGet()
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDelete(rowSelected)
      }
      if (toolbar.id === 'Active' && rowSelected.length) {
        this.handleStatus('active', rowSelected)
      }
      if (toolbar.id === 'Negative' && rowSelected.length) {
        this.handleStatus('negative', rowSelected)
      }
    },
    handleClickCellTool(e) {
      const { data, tool } = e
      if (tool.id === 'edit') {
        this.handleAction('edit', data)
        this.rulesGet('customerEditValid')
        this.bpGet()
      }
      if (tool.id === 'delete') {
        this.handleDelete([data])
      }
      if (tool.id === 'preview') {
        this.handleAction('preview', data)
      }
    },
    handleAction(type, data) {
      this.type = type
      for (let a in this.ruleForm) {
        this.ruleForm[a] = ''
      }
      if (this.type !== 'add') {
        this.changeStatus = false
        for (let i in this.ruleForm) {
          this.$set(this.ruleForm, i, data[i])
        }
      }
      this.$refs.dialog.ejsRef.show()
    },
    handleStatus(type, rows) {
      const dict = {
        active: { label: this.$t('激活'), value: 1 },
        negative: { label: this.$t('失效'), value: 0 }
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: `确认${dict[type]?.label}选中的数据？`
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids, status: dict[type]?.value }
          this.$API.customer.customerStatusUpdate(data).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    },
    handleDelete(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.customer.customerDelete(data).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    },
    // companyChange(e) {
    //   this.ruleForm.parentCode = e.itemData.orgCode;
    // },
    save() {
      let that = this
      if (that.type === 'preview') {
        that.cancel()
        return false
      }
      that.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (that.type === 'add') {
            const data = {
              ...that.ruleForm
            }
            that.$API.customer.customerAdd(data).then(() => {
              that.$refs.tepPage.refreshCurrentGridData()
              that.cancel()
            })
          }
          if (that.type === 'edit') {
            // delete this.ruleForm.customerExternalCode;
            that.$API.customer.customerEdit(that.ruleForm).then(() => {
              that.$refs.tepPage.refreshCurrentGridData()
              that.cancel()
            })
          }
        }
      })
    },
    cancel() {
      this.$refs.dialog.ejsRef.hide()
    },
    rulesGet(type) {
      this.$API.customer[type]().then((res) => {
        this.rules = formatRules(res.data)
      })
    },
    bpGet(e) {
      this.$API.customer.bpGet({ fuzzyNameOrCode: e?.text }).then((res) => {
        this.options = Object.assign({}, this.options, { BP: res.data })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/.btn-text {
  color: #4f5b6d;
}
/deep/ .mt-data-grid {
  .status-label {
    font-size: 12px;
    padding: 4px;
    border-radius: 2px;
    &.status-0 {
      color: #9baac1;
      background: rgba(155, 170, 193, 0.1);
    }
    &.status-1 {
      color: #6386c1;
      background: rgba(99, 134, 193, 0.1);
    }
  }
}
/deep/.mt-dialog {
  display: none;
}
</style>
