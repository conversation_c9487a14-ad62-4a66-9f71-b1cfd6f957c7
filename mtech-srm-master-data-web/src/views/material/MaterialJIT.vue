<!--
 * @Author: your name
 * @Date: 2021-09-29 09:53:32
 * @LastEditTime: 2022-01-11 09:56:02
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\material\MaterialSite.vue
-->
<template>
  <div class="full-height">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>
  </div>
</template>

<script>
import { PAGE_PLUGIN } from './config/materialJIT.config'
import { download, getHeadersFileName } from '@/utils/file.js'
export default {
  data() {
    return {
      pageConfig: PAGE_PLUGIN,
      newJITData: [] //选择完JIT以后的数组
    }
  },
  computed: {},
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      const _selectRows = e.grid.getSelectedRecords()
      if (
        _selectRows.length <= 0 &&
        (e.toolbar.id == 'Download' ||
          e.toolbar.id == 'Edit' ||
          e.toolbar.id == 'delete' ||
          e.toolbar.id == 'activate' ||
          e.toolbar.id == 'failure')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //新增
      if (e.toolbar.id === 'Add') {
        this.handleClickAdd()
      }
      //编辑
      else if (e.toolbar.id === 'Edit') {
        this.handleClickEdit(_selectRows)
      }
      // 删除
      else if (e.toolbar.id === 'delete') {
        this.handleClickdelete(_selectRows)
      }
      //激活
      else if (e.toolbar.id === 'activate') {
        this.handleClickactivate(_selectRows)
      }
      //失效
      else if (e.toolbar.id === 'failure') {
        this.handleClickfailure(_selectRows)
      }
      //导入
      else if (e.toolbar.id === 'upload') {
        this.handleClickUpload()
      }
      //导出
      else if (e.toolbar.id === 'Download') {
        this.handleClickDownload(_selectRows)
      }
    },
    //新增
    handleClickAdd() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "material/components/AddDialog.vue" */ './components/AddDialogNew.vue'
          ),
        data: {
          title: this.$t('新增'),
          headStates: 'Add'
        },
        success: () => {
          this.$refs.tepPage.refreshCurrentGridData()
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        }
      })
    },
    //编辑
    handleClickEdit(_selectRows) {
      if (_selectRows.length > 1) {
        this.$toast({ content: this.$t('只能编辑一行'), type: 'warning' })
        return
      }
      console.log(_selectRows)
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "material/components/AddDialog.vue" */ './components/AddDialogNew.vue'
          ),
        data: {
          title: this.$t('编辑'),
          data: _selectRows,
          headStates: 'edit'
        },
        success: () => {
          this.$refs.tepPage.refreshCurrentGridData()
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        }
      })
    },
    //删除
    handleClickdelete(_selectRows) {
      const _selectIds = []
      _selectRows.map((item) => {
        _selectIds.push(item.id)
      })
      let parameter = {
        ids: _selectIds
      }
      this.$API.material.batchDelete(parameter).then(() => {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        this.$refs.tepPage.refreshCurrentGridData()
      })
    },
    //激活
    handleClickactivate(_selectRows) {
      const _selectIds = []
      _selectRows.map((item) => {
        _selectIds.push(item.id)
      })
      let parameter = {
        ids: _selectIds,
        statusId: 1 //状态1激活,3失效
      }
      this.$API.material.batchUpdateStatus(parameter).then(() => {
        this.$refs.tepPage.refreshCurrentGridData()
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
      })
    },
    //失效
    handleClickfailure(_selectRows) {
      const _selectIds = []
      _selectRows.map((item) => {
        _selectIds.push(item.id)
      })
      let parameter = {
        ids: _selectIds,
        statusId: 3 //状态1激活,3失效
      }
      this.$API.material.batchUpdateStatus(parameter).then(() => {
        this.$refs.tepPage.refreshCurrentGridData()
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
      })
    },
    //导入
    handleClickUpload() {
      console.log('导入')
      this.$dialog({
        modal: () => import(/* webpackChunkName: "components/upload" */ './components/upload.vue'),
        data: {
          title: this.$t('上传')
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        }
      })
    },
    //导出
    handleClickDownload(_selectRows) {
      // console.log("导出");
      if (_selectRows.length > 1) {
        this.$toast({ content: this.$t('只能编辑一行'), type: 'warning' })
        return
      }
      const id = _selectRows[0].id
      const pramas = {
        accountId: id
      }
      this.$API.material.exportData(pramas).then((res) => {
        // console.log("=========", res);
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-subtitle {
  border-left: 3px solid #00469c;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: #2f353c;
  padding-left: 10px;
}
</style>
