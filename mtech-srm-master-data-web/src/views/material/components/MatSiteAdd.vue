<!--
 * @Author: your name
 * @Date: 2021-09-29 10:36:26
 * @LastEditTime: 2021-12-29 14:30:39
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\material\components\MatSiteAdd.vue
-->
<template>
  <div class="full-height mt-pt-20">
    <div class="add-wrapper full-height bg-white">
      <add-steppers v-model="currentStep">
        <div class="actions-wrapper">
          <mt-button css-class="e-flat" :is-primary="true" @click="stepBack">{{
            $t('返回')
          }}</mt-button>
          <mt-button
            v-if="currentStep < 2"
            css-class="e-flat"
            :is-primary="true"
            @click="stepNext"
            >{{ $t('下一步') }}</mt-button
          >
          <!-- <mt-button css-class="e-flat" :is-primary="true">{{ $t("确定") }}</mt-button> -->
        </div>
      </add-steppers>

      <div v-if="currentStep === 1" class="stepper-pannel mt-px-20">
        <mt-template-page
          ref="stepPage1"
          :hidden-tabs="true"
          :template-config="step1PageConfig"
        ></mt-template-page>
      </div>

      <div v-if="currentStep === 2" class="stepper-pannel mt-px-20">
        <mt-template-page
          ref="stepPage2"
          :hidden-tabs="true"
          :template-config="step2PageConfig"
          @handleClickToolBar="handleClickToolBar"
        ></mt-template-page>
      </div>
    </div>

    <mt-dialog
      ref="dialog"
      :header="$t('批量维护工厂')"
      css-class="create-proj-dialog"
      :buttons="buttons"
    >
      <div>
        <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
          <div class="full-width dialog-subtitle mt-mb-20">
            {{ $t('维护工厂') }}
          </div>
          <mt-form-item prop="countryId" :label="$t('工厂/地点')">
            <mt-select
              v-model="ruleForm.countryId"
              :fields="fields.FACTORY"
              :data-source="options.FACTORY"
              :show-clear-button="false"
              :allow-filtering="true"
              :placeholder="$t('请选择工厂/地点')"
              @select="factorySelect"
            ></mt-select>
          </mt-form-item>
        </mt-form>
        <!-- <div class="full-width dialog-subtitle mt-mb-20">{{ $t("关联品类") }}</div>
        <mt-data-grid
          ref="dataGrid"
          v-if="visible"
          :data-source="dataSource"
          :column-data="columns"
          :allow-selection="false"
        ></mt-data-grid> -->
      </div>
    </mt-dialog>

    <mt-toast ref="toastRef"></mt-toast>
  </div>
</template>

<script>
import AddSteppers from './AddSteppers.vue'
import {
  ITEM1_PAGE_PLUGIN,
  ITEM2_PAGE_PLUGIN,
  OPTIONS,
  FIELDS
  // COLUMNS,
} from '../config/materialSite.config'
export default {
  components: { AddSteppers },
  data() {
    return {
      step1PageConfig: ITEM1_PAGE_PLUGIN,
      step2PageConfig: ITEM2_PAGE_PLUGIN,
      options: OPTIONS,
      fields: FIELDS,
      // columns: COLUMNS(this),
      // dataSource: [],

      ruleForm: {
        countryId: ''
      },
      rules: {},
      currentStep: 1,
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],

      visible: false
    }
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const rowSelect = grid.getSelectedRecords()
      const commonToolbar = ['Add', 'Filter', 'Refresh', 'Setting']
      if (rowSelect.length === 0 && !commonToolbar.includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (toolbar.id === 'editSite' && rowSelect.length) {
        this.handleEditSite(rowSelect)
      }
    },
    handleEditSite() {
      this.factoryGet()
      // this.cateTreeTypeGet();
      this.ruleForm.countryId = ''
      this.$refs.dialog.ejsRef.show()
    },
    cancel() {
      this.$refs.dialog.ejsRef.hide()
    },
    save() {
      // const categoryIds = [];
      const currentTab = this.$refs.stepPage2.getCurrentTabRef()
      const selectedRows = currentTab.grid.getSelectedRecords()
      const itemIds = selectedRows.map((e) => e.id)

      // for (let i = 0; i < this.dataSource.length; i++) {
      //   const el = this.dataSource[i];

      //   if (el.categoryId.length) {
      //     categoryIds.push(el.categoryId[0]);
      //   }
      // }

      const data = {
        organizationId: this.ruleForm.countryId,
        // categoryIds,
        itemIds
      }
      this.$API.material.cateItemAdd(data).then(() => {
        const step2PageDataSource = this.step2PageConfig[0].grid.dataSource // step2 表格的数据
        // 删除 step2 表格已操作过的数据
        itemIds.forEach((itemId) => {
          for (let index = 0; index < step2PageDataSource.length; index++) {
            if (step2PageDataSource[index].id === itemId) {
              step2PageDataSource.splice(index, 1)
              break
            }
          }
        })
        if (step2PageDataSource.length > 0) {
          // 如果仍有未操作的数据
          this.$set(this.step2PageConfig[0].grid, 'dataSource', step2PageDataSource)
        } else {
          // 没有未操作过的数据，返回“品项地点关系列表”
          this.$router.push('/masterdata/material-site')
        }
        this.cancel()
      })
    },

    factorySelect() {
      // this.$set(this, "columns", COLUMNS(this, e.itemData.id));
      // this.$bus.$emit("change", e.itemData.organizationId);
    },
    stepBack() {
      if (this.currentStep > 1) {
        this.currentStep--
      } else {
        // 当前为第一步“选择物料”时，返回“品项地点关系列表”
        this.$router.push('/masterdata/material-site')
      }
    },
    stepNext() {
      const currentTab = this.$refs.stepPage1.getCurrentTabRef()
      const rowSelect = currentTab.grid.getSelectedRecords()

      if (this.currentStep === 1 && rowSelect.length) {
        this.currentStep++
        this.$set(this.step2PageConfig[0].grid, 'dataSource', rowSelect)
      } else {
        this.$refs.toastRef.show({
          cssClass: 'e-toast-danger',
          icon: 'e-error toast-icons',
          content: this.$t('请先勾选再执行该操作')
        })
      }
    },
    factoryGet() {
      this.$API.material.factoryGet({}).then((res) => {
        const data = res.data.map((item) => {
          return {
            theCodeName: item.siteCode + '-' + item.siteName,
            ...item
          }
        })
        this.$set(this.options, 'FACTORY', data)
      })
    }
    // cateTreeTypeGet() {
    //   this.$API.category.cateTypeQueryNoPages({}).then((res) => {
    //     const data = res.data.map((e) => {
    //       e.categoryId = [];
    //       return e;
    //     });
    //     this.$set(this, "dataSource", data);
    //     this.visible = true;
    //   });
    // },

    // eventOff() {
    //   this.visible = false;
    //   this.$bus.$off("change");
    // },
  }
}
</script>

<style lang="scss" scoped>
.add-wrapper {
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px 8px 0 0;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  .stepper-pannel {
    height: calc(100% - 60px);
    border-top: 1px solid rgba(232, 232, 232, 1);
    padding-bottom: 0;
  }
}

.dialog-subtitle {
  border-left: 3px solid #00469c;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: #2f353c;
  padding-left: 10px;
}
</style>
