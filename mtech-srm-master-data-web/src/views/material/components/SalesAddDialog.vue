<template>
  <!-- @beforeClose="cancel" -->
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form class="dialog-mt-form" ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item v-if="headStates == 'Add'" prop="companyCode" :label="$t('公司名称')">
          <mt-select
            v-model="formObject.companyCode"
            float-label-type="Never"
            :data-source="companySelect"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            :disabled="weight <= 0 && headStates == 'edit'"
            @change="changecompanySelect"
            :placeholder="$t('请选择公司')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item v-else prop="companyName" :label="$t('公司名称')">
          <mt-input
            v-model="formObject.companyName"
            :disabled="weight <= 0 && headStates == 'edit'"
            :placeholder="$t('请选择公司')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item v-if="headStates == 'Add'" prop="siteCode" :label="$t('工厂名称')">
          <mt-select
            v-model="formObject.siteCode"
            float-label-type="Never"
            :data-source="siteSelect"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :disabled="weight <= 1 || headStates == 'edit'"
            @change="changesiteSelect"
            :placeholder="$t('请先选择公司')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item v-else prop="siteName" :label="$t('工厂名称')">
          <mt-input
            v-model="formObject.siteName"
            float-label-type="Never"
            :disabled="weight <= 1 || headStates == 'edit'"
          ></mt-input>
        </mt-form-item>

        <mt-form-item v-if="headStates == 'Add'" prop="locationCode" :label="$t('仓库名称')">
          <mt-select
            v-model="formObject.locationCode"
            float-label-type="Never"
            :show-clear-button="true"
            :allow-filtering="true"
            :data-source="locationSelect"
            :fields="{ text: 'locationName', value: 'locationCode' }"
            :disabled="weight <= 2 && headStates != 'edit'"
            :filtering="inputPersonnelitem"
            @change="changelocationSelect"
            :placeholder="$t('请先选择工厂')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item v-else prop="locationName" :label="$t('仓库名称')">
          <mt-input
            v-model="formObject.locationName"
            float-label-type="Never"
            :disabled="weight <= 1 || headStates == 'edit'"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('加工商')">
          <mt-select
            v-model="formObject.supplierCode"
            float-label-type="Never"
            :show-clear-button="true"
            :allow-filtering="true"
            :data-source="supplierArr"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :disabled="weight <= 1 && headStates != 'edit'"
            :filtering="inputPersonnesupplier"
            @change="changesupplierSelect"
            :placeholder="$t('请选择加工商')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="ifDirectSend" :label="$t('直送')">
          <mt-select
            v-model="formObject.ifDirectSend"
            float-label-type="Never"
            :data-source="salesSelect"
            :show-clear-button="true"
            @change="changeifDirectSend"
            :placeholder="$t('请选择是否直送')"
          ></mt-select>
        </mt-form-item>
        <!-- <mt-form-item prop="ifSell" :label="$t('销售')">
          <mt-select
            v-model="formObject.ifSell"
            float-label-type="Never"
            :data-source="salesSelect"
            :show-clear-button="true"
            @change="changeifSell"
            :placeholder="$t('请选择是否销售')"
          ></mt-select>
        </mt-form-item> -->
        <mt-form-item prop="ifSell" :label="$t('委外方式')">
          <mt-select
            v-model="formObject.ifSell"
            float-label-type="Never"
            :data-source="salesSelectList"
            :show-clear-button="true"
            @change="changeifSell"
            :placeholder="$t('请选择是否销售')"
            :fields="{ text: 'label', value: 'value' }"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
// import { processors } from "../config/moduls.js";
import { utils } from '@mtech-common/utils'
export default {
  data() {
    return {
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      //v-model获取的值
      formObject: {
        companyName: '', //公司名称
        siteName: '', // 工厂名称
        locationName: '', //仓库名称
        ifDirectSend: '', //是否直送
        ifSell: '', //是否销售
        supplierName: '' //加工商
      },
      //必填项
      formRules: {
        companyName: [
          {
            required: true,
            message: this.$t('公司名称'),
            trigger: 'blur'
          }
        ],
        siteName: [
          {
            required: true,
            message: this.$t('工厂名称'),
            trigger: 'blur'
          }
        ],
        locationName: [
          {
            required: true,
            message: this.$t('仓库名称'),
            trigger: 'blur'
          }
        ],
        ifDirectSend: [
          {
            required: true,
            message: this.$t('是否直送'),
            trigger: 'blur'
          }
        ],
        ifSell: [
          {
            required: true,
            message: this.$t('委外方式'),
            trigger: 'blur'
          }
        ],
        supplierName: [
          {
            required: true,
            message: this.$t('加工商'),
            trigger: 'blur'
          }
        ]
      },
      //公司下拉框
      companySelect: [],
      //工厂下拉框
      siteSelect: [],
      //仓库下拉框
      locationSelect: [],
      //加工商
      supplierArr: [],
      //直送销售标识
      salesSelect: [
        { text: this.$t('否'), value: 'N' },
        { text: this.$t('是'), value: 'Y' }
      ],
      // 直送 - 委外方式选择项
      salesSelectList: [
        { label: this.$t('销售委外'), value: '1' },
        { label: this.$t('标准委外'), value: '0' },
        { label: this.$t('工序委外'), value: '3' }
      ],
      weight: 1 //权重值判断那些下拉框可以编辑
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    salesData() {
      return this.modalData.data
    },
    headStates() {
      return this.modalData.headStates
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.inputPersonnelitem = utils.debounce(this.inputPersonnelitem, 1000)
    this.inputPersonnesupplier = utils.debounce(this.inputPersonnesupplier, 1000)
    if (this.headStates == 'Add') {
      this.initialCallInterface()
    } else if (this.headStates == 'edit') {
      this.editCallInterface()
    }
  },
  methods: {
    //新增调用接口
    initialCallInterface() {
      //公司
      let parameter = {
        fuzzyParam: '',
        organizationLevelCodes: ['ORG02'],
        orgType: 'ORG001PRO'
      }
      this.$API.material.findSpecifiedChildrenLevelOrgs(parameter).then((res) => {
        this.companySelect = res.data
      })
    },
    //编辑回选
    editCallInterface() {
      let salesData = this.salesData[0]
      this.formObject.id = salesData.id
      this.formObject.companyCode = salesData.companyCode
      this.formObject.companyName = salesData.companyName
      this.formObject.siteName = salesData.siteName
      this.formObject.siteCode = salesData.siteCode
      this.formObject.ifDirectSend = salesData.ifDirectSend
      this.formObject.ifSell = salesData.ifSell
      this.formObject.locationName = salesData.locationName
      this.formObject.locationCode = salesData.locationCode
      this.weight = 0
      let parameter = {
        organizationCode: salesData.companyCode,
        fuzzyNameOrCode: salesData.supplierCode
      }
      this.$API.material.criteriaQuery(parameter).then((res) => {
        this.supplierArr = res.data
        this.formObject.supplierCode = salesData.supplierCode
        this.formObject.supplierName = salesData.supplierName
      })
    },
    //公司下拉框事件
    changecompanySelect(e) {
      // console.log(e, "公司");
      if (this.headStates == 'Add') {
        let id = e.itemData.id
        this.formObject.companyCode = e.itemData.orgCode
        this.formObject.companyName = e.itemData.orgName
        let parameter = {
          parentId: id,
          tenantId: 10000
        }
        this.weight = 2 //让工厂下拉框可以选择
        this.$API.material.findSiteInfoByParentId(parameter).then((res) => {
          // let res = factorySelects;
          this.siteSelect = res.data
          this.formObject.siteName = ''
          this.formObject.siteCode = ''
          this.formObject.locationName = ''
          this.formObject.locationCode = ''
        })
      }
      //加工商
      let data = {
        organizationCode: e.itemData.orgCode,
        fuzzyNameOrCode: ''
      }
      this.$API.material.criteriaQuery(data).then((res) => {
        this.supplierArr = res.data
      })
    },
    //工厂下拉框事件
    changesiteSelect(e) {
      if (this.headStates == 'Add') {
        this.formObject.siteCode = e.itemData.siteCode
        this.formObject.siteName = e.itemData.siteName
        this.weight = 3
        //查仓库
        let parameter = {
          commonCode: e.itemData.siteCode,
          fuzzyParam: ''
        }
        this.$API.material.locationFuzzyQuery(parameter).then((res) => {
          this.locationSelect = res.data
          this.formObject.locationName = ''
          this.formObject.locationCode = ''
        })
      }
    },
    //仓库模糊查询事件
    inputPersonnelitem(e) {
      let parameter = {
        commonCode: this.formObject.siteCode,
        fuzzyParam: e.text
      }
      this.$API.material.locationFuzzyQuery(parameter).then((res) => {
        this.locationSelect = res.data
      })
    },
    //仓库下拉框事件
    changelocationSelect(e) {
      this.formObject.locationCode = e.itemData.locationCode
      this.formObject.locationName = e.itemData.locationName
    },
    //加工商模糊查询事件
    inputPersonnesupplier(e) {
      //加工商
      let data = {
        organizationCode: this.formObject.companyCode,
        fuzzyNameOrCode: e.text
      }
      this.$API.material.criteriaQuery(data).then((res) => {
        this.supplierArr = res.data
      })
    },
    //加工商下拉事件
    changesupplierSelect(e) {
      this.formObject.supplierName = e.itemData.supplierName
      this.formObject.supplierCode = e.itemData.supplierCode
    },
    //直送下拉框事件
    changeifDirectSend(e) {
      this.formObject.ifDirectSend = e?.value ?? ''
    },
    //销售下拉框事件
    changeifSell(e) {
      this.formObject.ifSell = e?.value ?? ''
    },
    //点击确认
    confirm() {
      console.log(this.formObject)
      let formObject = this.formObject
      if (formObject.companyName == '') {
        this.$toast({
          content: this.$t('公司名称不能为空'),
          type: 'warning'
        })
        return
      }
      if (formObject.siteName == '') {
        this.$toast({
          content: this.$t('工厂名称不能为空'),
          type: 'warning'
        })
        return
      }
      if (formObject.locationName == '') {
        this.$toast({
          content: this.$t('仓库名称不能为空'),
          type: 'warning'
        })
        return
      }
      if (formObject.supplierName == '') {
        this.$toast({
          content: this.$t('加工商名称不能为空'),
          type: 'warning'
        })
        return
      }
      if (formObject.ifDirectSend == '') {
        this.$toast({
          content: this.$t('直送不能为空'),
          type: 'warning'
        })
        return
      }
      if (formObject.ifSell == '') {
        this.$toast({
          content: this.$t('销售不能为空'),
          type: 'warning'
        })
        return
      }
      let parameter = {
        companyCode: formObject.companyCode, //公司编码
        // companyName: formObject.companyName, //公司名称
        // siteName: formObject.siteName, //工厂名称
        siteCode: formObject.siteCode, //编码
        locationCode: formObject.locationCode, //仓库编码
        // locationName: formObject.locationName, //仓库名称
        ifDirectSend: formObject.ifDirectSend, //是否直送
        ifSell: formObject.ifSell, //是否销售
        supplierCode: formObject.supplierCode //加工商编码
        // supplierName: formObject.supplierName, //加工商名称
      }
      if (this.headStates == 'Add') {
        this.$API.material.siteAdd(parameter).then(() => {
          this.$emit('confirm-function')
        })
      }
      if (this.headStates == 'edit') {
        parameter.id = formObject.id
        this.$API.material.sitelocationmanangeUpdate(parameter).then(() => {
          this.$emit('confirm-function')
        })
      }
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    padding: 0;
    .dialog-content {
      padding: 40px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 400px;
        }
      }
    }
  }
}
</style>
