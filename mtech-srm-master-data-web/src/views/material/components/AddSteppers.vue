<!--
 * @Author: your name
 * @Date: 2021-09-29 10:37:41
 * @LastEditTime: 2021-10-19 16:19:31
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\material\components\AddSteppers.vue
-->
<template>
  <div class="ctx-wrapper flex justify-between items-center full-width mt-px-20">
    <div class="steppers-wrapper flex justify-start items-center">
      <div :class="['stepper', value === 1 ? 'active' : '']">
        <span class="step">1</span>
        <span class="title">{{ $t('选择物料') }}</span>
      </div>
      <div :class="['stepper', value === 2 ? 'active' : '']">
        <span class="step">2</span>
        <span class="title">{{ $t('维护关系') }}</span>
      </div>
    </div>
    <slot></slot>
  </div>
</template>

<script>
export default {
  props: {
    value: {
      type: Number,
      default: 1,
      required: true
    }
  },
  data() {
    return {}
  },
  watch: {
    currentStep(val) {
      this.$emit('input', val)
    }
  },
  methods: {
    back() {
      this.$emit('back', this.currentStep)
    },
    next() {
      this.$emit('next', this.currentStep)
    }
  }
}
</script>

<style lang="scss" scoped>
.ctx-wrapper {
  height: 60px;
  .steppers-wrapper {
    overflow: hidden;
    .stepper {
      position: relative;
      .step {
        display: inline-block;
        width: 24px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        font-size: 14px;
        color: #6386c1;
        border-radius: 50%;
        border: 1px solid #6386c1;
      }
      .title {
        display: inline-block;
        margin-left: 10px;
        height: 22px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(41, 41, 41, 1);
      }
      .title:after {
        content: '';
        left: 100%;
        margin-left: 8px;
        background: #0000001f;
        height: 1px;
        position: absolute;
        top: 50%;
        width: 50px;
      }
      &.active {
        .step {
          color: #fff;
          background-color: #6386c1;
        }
        .title {
          font-weight: 500;
          margin-left: 5px;
          padding: 2px 5px;
          vertical-align: top;
          color: #6386c1;
          border-bottom: 2px solid #6386c1;
        }
      }
    }
    .stepper:not(:first-child) {
      margin-left: 70px;
    }
  }
}
</style>
