<!--
 * @Author: your name
 * @Date: 2021-09-29 09:53:32
 * @LastEditTime: 2022-01-11 09:56:02
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\material\MaterialSite.vue
-->
<template>
  <div class="full-height mt-pt-20">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>

    <mt-dialog ref="dialog" :header="headerTitle" css-class="create-proj-dialog" :buttons="buttons">
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <div class="full-width dialog-subtitle mt-mb-20">
          {{ $t('基本信息') }}
        </div>
        <mt-form-item prop="itemCode" :label="$t('物料/品项编号')">
          <mt-input
            v-model="ruleForm.itemCode"
            :readonly="true"
            :show-clear-button="false"
            type="text"
            :placeholder="$t('请输入物料/品项编号')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('物料/品项名称')">
          <mt-input
            v-model="ruleForm.itemName"
            :readonly="true"
            :show-clear-button="false"
            type="text"
            :placeholder="$t('请输入物料/品项名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="itemDescription" :label="$t('规格型号')">
          <mt-input
            v-model="ruleForm.itemDescription"
            :readonly="true"
            :show-clear-button="false"
            type="text"
            :placeholder="$t('请输入规格型号')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="itemDescription" :label="$t('旧物料编码')">
          <mt-input
            v-model="ruleForm.itemDescription"
            :readonly="true"
            :show-clear-button="false"
            type="text"
            :placeholder="$t('请输入旧物料编码')"
          ></mt-input>
        </mt-form-item>
        <!-- <mt-form-item prop="clientCategoryCode" :label="$t('集团品类编号')">
          <mt-input
            v-model="ruleForm.clientCategoryCode"
            :readonly="true"
            :show-clear-button="false"
            type="text"
            :placeholder="$t('请输入集团品类编号')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="clientCategoryName" :label="$t('集团品类名称')">
          <mt-input
            v-model="ruleForm.clientCategoryName"
            :readonly="true"
            :show-clear-button="false"
            type="text"
            :placeholder="$t('请输入集团品类名称')"
          ></mt-input>
        </mt-form-item> -->
        <mt-form-item prop="organizationId" :label="$t('工厂/地点')">
          <mt-select
            v-model="ruleForm.organizationId"
            :fields="fields.FACTORY"
            :data-source="options.FACTORY"
            :readonly="readonly"
            :show-clear-button="false"
            :allow-filtering="true"
            :placeholder="$t('请选择工厂/地点')"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import { PAGE_PLUGIN, RULES, OPTIONS, FIELDS } from './config/materialSite.config'
export default {
  data() {
    return {
      pageConfig: PAGE_PLUGIN,
      rules: RULES,
      options: OPTIONS,
      fields: FIELDS,

      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {},
      visible: false,
      type: 'edit'
    }
  },
  computed: {
    headerTitle() {
      if (this.type === 'edit') {
        return this.$t('编辑')
      }
      return this.$t('预览')
    },
    readonly() {
      return this.type === 'preview'
    }
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const rowSelected = grid?.getSelectedRecords?.()

      if (toolbar.id === 'Add') {
        this.$router.push('/masterdata/mat-site-add')
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDelete(rowSelected)
      }
    },
    async handleClickCellTool(e) {
      const { data, tool } = e

      if (tool.id === 'edit') {
        await this.factoryGet()
        this.handleAction('edit', data)
      }
      if (tool.id === 'delete') {
        this.handleDelete([data])
      }
      if (tool.id === 'preview') {
        await this.factoryGet()
        this.handleAction('preview', data)
      }
    },
    handleAction(type, data) {
      this.type = type
      this.$refs.ruleForm.resetFields()
      this.ruleForm = Object.assign({}, data)
      this.$refs.dialog.ejsRef.show()
    },
    save() {
      if (this.type === 'preview') {
        this.cancel()
        return false
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.type === 'edit') {
            this.$API.material
              .itemOrgRelUpdate({
                ...this.ruleForm,
                orgId: this.ruleForm.organizationId
              })
              .then(() => {
                this.$refs.tepPage.refreshCurrentGridData()
                this.cancel()
              })
          }
        }
      })
    },
    cancel() {
      this.$refs.dialog.ejsRef.hide()
    },
    handleDelete(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.material.matSiteDelete(data).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    },
    async factoryGet() {
      await this.$API.material.factoryGet({}).then((res) => {
        const data = res.data.map((item) => {
          return {
            theCodeName: item.siteCode + '-' + item.siteName,
            ...item
          }
        })
        this.$set(this.options, 'FACTORY', data)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-subtitle {
  border-left: 3px solid #00469c;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: #2f353c;
  padding-left: 10px;
}
</style>
