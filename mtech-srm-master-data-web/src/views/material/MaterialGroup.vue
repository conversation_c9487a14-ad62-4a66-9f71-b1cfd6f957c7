<!--
 * @Author: your name
 * @Date: 2021-12-13 10:40:50
 * @LastEditTime: 2021-12-13 11:51:32
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\material\MaterialGroup.vue
-->
<template>
  <div class="full-height mt-pt-20">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
    <mt-dialog ref="dialog" :header="headerTitle" css-class="create-proj-dialog" :buttons="buttons">
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="code" :label="$t('品项组编码')">
          <mt-input
            v-model="ruleForm.code"
            :disabled="disabled"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入品项组编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="name" :label="$t('品项组名称')">
          <mt-input
            v-model="ruleForm.name"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入品项组名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="groupTypeId" :label="$t('品项组分类')">
          <mt-select
            v-model="ruleForm.groupTypeId"
            :disabled="disabled"
            :fields="fields.GROUP_TYPE"
            :data-source="options.GROUP_TYPE"
            :show-clear-button="true"
            :allow-filtering="true"
            :placeholder="$t('请选择品项组分类')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="description" :label="$t('描述')">
          <mt-input
            v-model="ruleForm.description"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入描述')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="joinItemGroupList" :label="$t('关联物料组')">
          <textarea
            v-if="disabled"
            cols="30"
            rows="3"
            v-model="joinItemGroupStr"
            :disabled="disabled"
            style="width: 100%"
          ></textarea>
          <RemoteAutocomplete
            v-if="!disabled && isShowRemote"
            style="flex: 1"
            :disabled="disabled"
            v-model="joinItemGroupList"
            url="/masterDataManagement/tenant/item-group/paged-query"
            multiple
            :placeholder="$t('请选择物料组')"
            :fields="{ text: 'name', value: 'code' }"
            :search-fields="['name', 'code']"
          ></RemoteAutocomplete>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import { PAGE_CONFIG, RULES, OPTIONS, FIELDS } from './config/materialGroup.config'
import { formatRules } from '@/utils/util'
export default {
  components: {
    RemoteAutocomplete: require('@/components/RemoteAutocomplete').default
  },
  data() {
    return {
      options: OPTIONS,
      pageConfig: PAGE_CONFIG,
      rules: RULES,
      fields: FIELDS,

      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {},
      joinItemGroupList: [],
      type: 'add', // add edit preview
      joinItemGroupStr: '',
      isShowRemote: false
    }
  },
  computed: {
    headerTitle() {
      if (this.type === 'add') {
        return this.$t('新增')
      }
      if (this.type === 'edit') {
        return this.$t('编辑')
      }
      return this.$t('预览')
    },
    disabled() {
      if (this.type === 'preview') {
        return true
      }
      return false
    }
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const rowSelected = grid.getSelectedRecords()
      if (toolbar.id === 'Add') {
        this.dictGroupTypeGet()
        this.handleAction('add')
        this.rulesGet('matGroupAddValid')
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDelete(rowSelected)
      }
      if (toolbar.id === 'Active' && rowSelected.length) {
        this.handleStatus('active', rowSelected)
      }
      if (toolbar.id === 'Negative' && rowSelected.length) {
        this.handleStatus('negative', rowSelected)
      }
    },
    handleClickCellTool(e) {
      const { data, tool } = e
      if (tool.id === 'edit') {
        this.dictGroupTypeGet()
        this.handleAction('edit', data)
        this.rulesGet('matGroupEditValid')
      }
      if (tool.id === 'delete') {
        this.handleDelete([data])
      }
      if (tool.id === 'preview') {
        this.dictGroupTypeGet()
        this.handleAction('preview', data)
      }
    },
    handleAction(type, data) {
      this.isShowRemote = false
      this.joinItemGroupList = []
      this.type = type
      this.$refs.ruleForm.resetFields()
      this.ruleForm = Object.assign({}, data)
      if (type === 'add') {
        this.joinItemGroupList = []
      } else {
        this.joinItemGroupStr = data.joinItemGroupStr
        this.joinItemGroupList = JSON.parse(JSON.stringify(data.joinItemGroupCodeList))
      }
      setTimeout(() => {
        this.isShowRemote = true
        this.$refs.dialog.ejsRef.show()
      }, 0)
    },
    handleStatus(type, rows) {
      const dict = {
        active: { label: this.$t('激活'), value: 1 },
        negative: { label: this.$t('失效'), value: 3 }
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: `确认${dict[type]?.label}选中的数据？`
        },
        success: () => {
          const id = rows.map((e) => e.id)
          const data = { id, statusId: dict[type]?.value }
          this.$API.material.matGroupStatusUpdate(data).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    },
    handleDelete(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.material.matGroupDelete(data).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    },
    save() {
      if (this.type === 'preview') {
        this.cancel()
        return false
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.joinItemGroupList.length) {
            const params = {
              condition: 'and',
              page: { current: 1, pages: 1, size: 999999 },
              rules: [
                {
                  condition: 'and',
                  rules: [
                    {
                      condition: 'or',
                      label: '',
                      field: 'code',
                      type: 'string',
                      operator: 'in',
                      value: this.joinItemGroupList
                    }
                  ]
                }
              ]
            }
            this.$API.masterData.getItemGroupList(params).then((res) => {
              const data = {
                ...this.ruleForm,
                joinItemGroupList:
                  res.data?.records.map((i) => {
                    return {
                      code: i.code,
                      name: i.name
                    }
                  }) || []
              }
              if (this.type === 'add') {
                this.$API.material.matGroupAdd(data).then(() => {
                  this.$refs.tepPage.refreshCurrentGridData()
                  this.cancel()
                })
              }
              if (this.type === 'edit') {
                delete data.tabIndex
                this.$API.material.matGroupEdit(data).then(() => {
                  this.$refs.tepPage.refreshCurrentGridData()
                  this.cancel()
                })
              }
            })
          } else {
            const data = {
              ...this.ruleForm,
              joinItemGroupList: []
            }
            if (this.type === 'add') {
              this.$API.material.matGroupAdd(data).then(() => {
                this.$refs.tepPage.refreshCurrentGridData()
                this.cancel()
              })
            }
            if (this.type === 'edit') {
              delete data.tabIndex
              this.$API.material.matGroupEdit(data).then(() => {
                this.$refs.tepPage.refreshCurrentGridData()
                this.cancel()
              })
            }
          }
        }
      })
    },
    cancel() {
      this.$refs.dialog.ejsRef.hide()
    },
    dictGroupTypeGet() {
      const query = { dictCode: 'ITEM-GROUP-TYPE' }
      this.$API.dict.TenantDictTypeGet(query).then((res) => {
        this.options.GROUP_TYPE = res.data
      })
    },
    rulesGet(type) {
      this.$API.material[type]().then((res) => {
        this.rules = formatRules(res.data)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-subtitle {
  border-left: 3px solid #eda133;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  padding-left: 10px;
}
/deep/.btn-text {
  color: #4f5b6d;
}
/deep/ .mt-data-grid {
  .status-label {
    font-size: 12px;
    padding: 4px;
    border-radius: 2px;
    &.status-0 {
      color: #9baac1;
      background: rgba(155, 170, 193, 0.1);
    }
    &.status-1 {
      color: #6386c1;
      background: rgba(99, 134, 193, 0.1);
    }
  }
}
/deep/.mt-dialog {
  display: none;
}
</style>
