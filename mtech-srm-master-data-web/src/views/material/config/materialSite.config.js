import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-09-29 09:58:01
 * @LastEditTime: 2021-12-30 13:54:42
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\material\config\materialSite.config.js
 */
import Vue from 'vue'
export const RULES = {}

export const PAGE_PLUGIN = [
  {
    toolbar: ['Add', 'Delete'],
    grid: {
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'itemCode',
          headerText: i18n.t('物料/品项编号'),
          width: '350',
          cellTools: ['edit', 'delete', 'preview']
        },
        {
          field: 'itemName',
          width: '200',
          headerText: i18n.t('物料/品项名称')
        },
        {
          field: 'itemDescription',
          width: '200',
          headerText: i18n.t('规格型号')
        },
        // {
        //   field: "clientCategoryCode",
        //   width: "200",
        //   headerText: i18n.t("集团品类编号"),
        // },
        {
          field: 'organizationCode',
          width: '200',
          headerText: i18n.t('工厂/地点编号')
        },
        {
          field: 'organizationName',
          width: '200',
          headerText: i18n.t('工厂/地点名称')
        },
        {
          field: 'independenceFlag',
          width: '200',
          headerText: i18n.t('是否内需跟单'),
          valueConverter: {
            type: 'map',
            map: {
              1: i18n.t('是'),
              2: i18n.t('否')
            }
          }
        },
        {
          field: 'updateTime',
          width: '200',
          headerText: i18n.t('更新时间')
        },
        {
          field: 'updateUserName',
          width: '200',
          headerText: i18n.t('更新人')
        }
      ],
      asyncConfig: {
        url: `/masterDataManagement/tenant/item-org-rel/paged-query-for-site?BU_CODE=${localStorage.getItem(
          'currentBu'
        )}`,
        params: {}
      }
    }
  }
]

export const ITEM1_PAGE_PLUGIN = [
  {
    toolbar: [],
    grid: {
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'itemCode',
          headerText: i18n.t('物料/品项编码'),
          width: '200'
        },
        {
          field: 'itemName',
          width: '200',
          headerText: i18n.t('物料/品项名称')
        },
        {
          field: 'itemDescription',
          width: '200',
          headerText: i18n.t('规格型号')
        },
        {
          field: 'itemOldCode',
          width: '200',
          headerText: i18n.t('旧物料编码')
        }
        // {
        //   field: "roleDescription",
        //   width: "200",
        //   headerText: i18n.t("集团品类编号"),
        // },
        // {
        //   field: "roleDescription",
        //   width: "200",
        //   headerText: i18n.t("集团品类名称"),
        // },
      ],
      asyncConfig: {
        url: `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem(
          'currentBu'
        )}`,
        params: {}
      }
    }
  }
]

export const ITEM2_PAGE_PLUGIN = [
  {
    toolbar: [
      {
        id: 'editSite',
        icon: 'icon_solid_Createproject',
        title: i18n.t('批量维护工厂')
      }
    ],
    grid: {
      dataSource: [],
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'itemCode',
          headerText: i18n.t('物料/品项编码'),
          width: '200'
        },
        {
          field: 'itemName',
          width: '200',
          headerText: i18n.t('物料/品项名称')
        },
        {
          field: 'itemDescription',
          width: '200',
          headerText: i18n.t('规格型号')
        },
        {
          field: 'itemOldCode',
          width: '200',
          headerText: i18n.t('旧物料编码')
        },
        // {
        //   field: "roleDescription",
        //   width: "200",
        //   headerText: i18n.t("集团品类编号"),
        // },
        // {
        //   field: "roleDescription",
        //   width: "200",
        //   headerText: i18n.t("集团品类名称"),
        // },
        // FIXME: categoryCode 和 categoryName 在 categoryResponse 对象中
        {
          field: 'categoryCode',
          width: '200',
          headerText: i18n.t('品类项编号')
        },
        {
          field: 'categoryName',
          width: '200',
          headerText: i18n.t('品类项名称')
        }
      ]
    }
  }
]

export const OPTIONS = {
  FACTORY: []
}
export const FIELDS = {
  FACTORY: {
    text: 'theCodeName',
    value: 'organizationId'
  }
}

export const COLUMNS = function (that) {
  return [
    {
      headerText: i18n.t('品类树编号'),
      field: 'typeCode'
    },
    {
      headerText: i18n.t('品类树名称'),
      field: 'typeName'
    },
    {
      headerText: i18n.t('品类项名称'),
      field: 'categoryId',
      template: function () {
        return {
          template: Vue.component('relate', {
            template: `
              <div>
                <mt-DropDownTree
                  :id="data.id + Date.parse(new Date())"
                  v-model="data.categoryId"
                  :fields="dropDownTree"
                  :allow-filtering="true"
                  :placeholder="$t('请选择品类名称')"
                  @input="change"
                ></mt-DropDownTree>
              </div>
            `,
            data() {
              return {
                data: {},
                dropDownTree: {
                  dataSource: [],
                  value: 'id',
                  text: 'name',
                  child: 'children'
                },
                visible: false
              }
            },
            mounted() {
              this.$bus.$on('change', (e) => {
                this.dataGet(e)
              })
            },
            methods: {
              dataGet(id) {
                const query = {
                  categoryTypeId: this.data.id,
                  organizationId: id
                }
                this.visible = false
                that.$API.category.cateTreeNodeDetailGet(query).then((res) => {
                  this.dropDownTree = Object.assign({}, this.dropDownTree, {
                    dataSource: res.data || []
                  })
                  this.visible = true
                })
              },
              change() {
                const index = this.data.index
                // const selectedItem = this.dropDownTree.dataSource.filter(
                //   (item) => item.id === e[0]
                // );
                // const { name, categoryCode } = selectedItem[0];

                // this.data.categoryName = name;
                // this.data.categoryCode = categoryCode;
                that.dataSource[index] = this.data
              }
            }
          })
        }
      }
    }
    // {
    //   headerText: i18n.t("品类项编码"),
    //   field: "categoryCode",
    // },
  ]
}
