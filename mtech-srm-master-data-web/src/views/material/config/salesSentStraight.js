import { i18n } from '@/main.js'
// import { utils } from "@mtech-common/utils";
// import { processor } from "./moduls.js";

/*
 * @Author: your name
 * @Date: 2021-09-29 09:58:01
 * @LastEditTime: 2021-12-30 13:54:42
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\material\config\materialSite.config.js
 */
// import Vue from "vue";
// import { i18n } from "@/main.js";
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'Edit', icon: 'icon_solid_edit', title: i18n.t('编辑') },
  { id: 'delete', icon: 'icon_solid_Delete', title: i18n.t('删除') },
  { id: 'activate', icon: 'icon_table_enable', title: i18n.t('激活') },
  { id: 'failure', icon: 'icon_table_disable', title: i18n.t('失效') },
  { id: 'upload', icon: 'icon_solid_upload', title: i18n.t('导入') }
  // { id: "Download", icon: "icon_solid_Download", title: i18n.t("导出") },
]
export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'statusDescription',
    headerText: i18n.t('状态')
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
    // width: "350",
    // cellTools: ["edit", "delete", "preview"],
  },
  {
    field: 'siteName',
    // width: "200",
    headerText: i18n.t('工厂名称')
  },
  {
    field: 'locationCode',
    // width: "200",
    headerText: i18n.t('仓库编码')
  },
  {
    field: 'locationName',
    // width: "200",
    headerText: i18n.t('仓库名称')
  },
  {
    field: 'ifDirectSend',
    // width: "200",
    headerText: i18n.t('是否直送'),
    valueConverter: {
      type: 'map',
      map: { N: '否', Y: '是' }
    }
  },
  {
    field: 'ifSell',
    // width: "200",
    headerText: i18n.t('委外方式'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('销售委外'),
        0: i18n.t('标准委外'),
        3: i18n.t('工序委外')
      }
    }
  },
  {
    field: 'supplierCode',
    // width: "200",
    headerText: i18n.t('加工商编码')
  },
  {
    field: 'supplierName',
    // width: "200",
    headerText: i18n.t('加工商名称')
  }
  // {
  //   field: "updateUserName",
  //   // width: "200",
  //   headerText: i18n.t("更新人"),
  // },
  // {
  //   field: "updateTime",
  //   // width: "200",
  //   headerText: i18n.t("更新时间"),
  // },
]
export const PAGE_PLUGIN = [
  {
    toolbar,
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      columnData,
      // dataSource: [],
      asyncConfig: {
        url: '/masterDataManagement/tenant/site-location-manage/paged-query',
        params: {}
      }
    }
  }
]
