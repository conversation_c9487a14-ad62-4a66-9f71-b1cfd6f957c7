import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-12-13 10:41:18
 * @LastEditTime: 2021-12-13 11:51:36
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\material\config\materialGroup.config.js
 */
export const PAGE_CONFIG = [
  {
    toolbar: [
      'Add',
      'Delete',
      {
        id: 'Active',
        icon: 'icon_solid_Createproject',
        title: i18n.t('激活')
      },
      {
        id: 'Negative',
        icon: 'icon_solid_Createproject',
        title: i18n.t('失效')
      }
    ],
    grid: {
      frozenColumns: 2,
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'code',
          headerText: i18n.t('品项组编码'),
          width: '250',
          cellTools: ['edit', 'delete', 'preview']
        },
        {
          field: 'name',
          width: '200',
          headerText: i18n.t('品项组名称')
        },
        {
          field: 'statusDesc',
          headerText: i18n.t('状态'),
          width: '150'
        },
        {
          field: 'groupTypeCode',
          width: '150',
          headerText: i18n.t('分类编码')
        },
        {
          field: 'groupTypeName',
          width: '150',
          headerText: i18n.t('分类名称')
        },
        {
          field: 'description',
          width: '150',
          headerText: i18n.t('描述')
        },
        {
          field: 'joinItemGroupStr',
          width: '150',
          headerText: i18n.t('关联物料组')
        },
        {
          field: 'createUserName',
          width: '150',
          headerText: i18n.t('创建人')
        },
        {
          width: 'auto',
          field: 'createTime',
          headerText: i18n.t('创建时间'),
          valueConverter: {
            type: 'date'
          }
        }
      ],
      asyncConfig: {
        url: '/masterDataManagement/tenant/item-group/paged-query',
        params: {},
        serializeList: (list) => {
          return list.map((item) => {
            let joinItemGroupStr = ''
            const joinItemGroupCodeList = []
            if (item.joinItemGroupList) {
              item.joinItemGroupList?.forEach((itm, idx) => {
                if (idx === 0) {
                  joinItemGroupStr += `${itm.code} - ${itm.name}`
                } else {
                  joinItemGroupStr += `; ${itm.code} - ${itm.name}`
                }
                joinItemGroupCodeList.push(itm.code)
              })
            }
            return {
              ...item,
              joinItemGroupStr,
              joinItemGroupCodeList
            }
          })
        }
      }
    }
  }
]

export const FIELDS = {
  GROUP_TYPE: {
    text: 'itemName',
    value: 'id'
  }
}
export const RULES = {}

export const OPTIONS = {
  GROUP_TYPE: []
}
