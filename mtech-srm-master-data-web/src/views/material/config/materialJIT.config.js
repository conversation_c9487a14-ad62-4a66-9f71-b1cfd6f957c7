import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-09-29 09:58:01
 * @LastEditTime: 2021-12-30 13:54:42
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\material\config\materialSite.config.js
 */
// import Vue from "vue";
// import { i18n } from "@/main.js";
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'Edit', icon: 'icon_solid_edit', title: i18n.t('编辑') },
  { id: 'delete', icon: 'icon_solid_Delete', title: i18n.t('删除') },
  { id: 'activate', icon: 'icon_table_enable', title: i18n.t('启用') },
  { id: 'failure', icon: 'icon_table_disable', title: i18n.t('停用') },
  { id: 'upload', icon: 'icon_solid_upload', title: i18n.t('导入') }
  // { id: "Download", icon: "icon_solid_Download", title: i18n.t("导出") },
]
const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
    // width: "350",
    // cellTools: ["edit", "delete", "preview"],
  },
  {
    field: 'itemName',
    // width: "200",
    headerText: i18n.t('物料名称')
  },
  {
    field: 'itemDescription',
    // width: "200",
    headerText: i18n.t('物料规格型号')
  },
  {
    field: 'itemIdentification',
    // width: "200",
    headerText: i18n.t('JIT标识'),
    valueConverter: {
      type: 'map',
      map: { N: i18n.t('否'), Y: i18n.t('是') }
    }
  },
  {
    field: 'siteCode',
    // width: "200",
    headerText: i18n.t('工厂编码')
  },
  {
    field: 'siteName',
    // width: "200",
    headerText: i18n.t('工厂名称')
  },
  {
    field: 'statusDescription',
    // width: "200",
    headerText: i18n.t('是否启用')
  }
  // {
  //   field: "updateTime",
  //   // width: "200",
  //   headerText: i18n.t("更新时间"),
  // },
  // {
  //   field: "updateUserName",
  //   // width: "200",
  //   headerText: i18n.t("更新人"),
  // },
]
export const PAGE_PLUGIN = [
  {
    toolbar,
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      columnData,
      asyncConfig: {
        url: '/masterDataManagement/tenant/item-org-jit/paged-query',
        params: {}
      }
    }
  }
]
