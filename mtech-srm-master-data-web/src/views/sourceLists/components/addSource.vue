<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item class="form-item" :label="$t('公司')" label-style="top" prop="organizationId">
          <mt-select
            :disabled="isEdit"
            v-model="formInfo.organizationId"
            :data-source="orgList"
            :fields="{ text: 'orgName', value: 'id' }"
            :placeholder="$t('请选择公司')"
            @change="changeOrg"
            width="414"
          ></mt-select>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('工厂')" label-style="top" prop="siteOrgId">
          <mt-select
            :disabled="isEdit"
            v-model="formInfo.siteOrgId"
            :data-source="fuzzyList"
            :allow-filtering="true"
            :filtering="onSiteFilter"
            :fields="{ text: 'codeName', value: 'organizationId' }"
            :placeholder="$t('请选择工厂')"
            width="414"
            @change="changeSite"
            @open="openFun"
          ></mt-select>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('物料')" label-style="top" prop="itemId">
          <mt-select
            :disabled="isEdit"
            v-model="formInfo.itemId"
            :data-source="pageList"
            :allow-filtering="true"
            :filtering="onFiltering"
            :fields="{ text: 'itemCodeName', value: 'id' }"
            :placeholder="$t('请选择物料')"
            width="414"
            @open="openItem"
          ></mt-select>
        </mt-form-item>
        <!-- <mt-form-item
          class="form-item form-item-spec"
          :label="$t('转储类货源')"
          label-style="top"
          prop="isDump"
        >
          <mt-select
            :disabled="isEdit"
            v-model="formInfo.isDump"
            :data-source="supplierList"
            :placeholder="$t('请选择转储类货源')"
            width="414"
          ></mt-select>
        </mt-form-item> -->
      </mt-form>
      <!-- 表格 -->
      <mt-template-page
        style="height: 300px"
        ref="templateRefEdit"
        :key="templateKey"
        :use-tool-template="false"
        :template-config="templateConfigEdit"
        @handleClickToolBar="handleClickToolBarEdit"
        @actionBegin="actionBegin"
        @actionComplete="actionComplete"
        @selectedChanged="selectedChanged"
      ></mt-template-page>
    </div>
  </mt-dialog>
</template>
<script>
import { cloneDeep } from 'lodash'
import bus from '@/utils/bus'
import { forecastColumnData, contactsInfo } from '../config/columns'
import Component from './editComponents/columnComponent'
import { utils } from '@mtech-common/utils'
export default {
  data() {
    return {
      selectedOtherInfo: {},
      nowEditRowFlag: '', //当前编辑的行id
      addId: '1',
      isEdit: false, //是否编辑 1是编辑 2不是编辑
      formInfo: {
        categoryId: '',
        organizationId: '',
        organizationCode: '',
        siteOrgId: '',
        itemId: '',
        isDump: '',
        supplierAddRequestList: []
      },
      inputName: '',
      siteState: true,
      rules: {
        organizationId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        siteOrgId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        itemId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
        // isDump: [
        //   { required: true, message: this.$t("请选择"), trigger: "blur" },
        // ],
      },

      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        },
        {
          click: this.confirmSynch,
          buttonModel: { isPrimary: 'true', content: this.$t('保存并同步') }
        }
      ],

      orgList: [],
      fuzzyList: [],
      pageList: [],
      supplierList: [
        { text: this.$t('是'), value: 1 },
        { text: this.$t('否'), value: 0 }
      ],
      templateConfigEdit: [
        {
          toolbar: {
            useBaseConfig: false,
            tools: [
              this.modalData.type == 'edit'
                ? [
                    {
                      id: 'endEdit',
                      icon: 'icon_solid_Closeorder',
                      title: this.$t('保存')
                    }
                  ]
                : [
                    {
                      id: 'add',
                      icon: 'icon_card_plus',
                      title: this.$t('新增')
                    },
                    {
                      id: 'del',
                      icon: 'icon_list_delete',
                      title: this.$t('删除')
                    },
                    {
                      id: 'endEdit',
                      icon: 'icon_solid_Closeorder',
                      title: this.$t('保存')
                    }
                  ]
            ]
          },
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowSorting: false,
            allowFiltering: false,
            columnData: forecastColumnData,
            dataSource: []
          }
        }
      ],
      allData: [],
      mrpLists: [],
      stageTypeFields: {
        text: 'itemName',
        value: 'itemCode'
      },
      supplierData: [],
      supplierFields: {
        text: 'supplierName',
        value: 'id'
      },
      orgCodeLists: [],
      codeFields: {
        text: 'organizationName',
        value: 'id'
      },
      categoryId: '',
      categoryCode: '',
      isInner: '',
      inputState: false,
      templateKey: 1
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    type() {
      return this.modalData.type
    },
    param() {
      return this.modalData.param
    }
  },
  created() {
    // 公司
    this.getChildrenCompanyOrganization()
    // 工厂
    // this.fuzzyQuery();
    // 物料
    // this.fuzzyPagedQuery();
  },
  async mounted() {
    bus.$on(`dumpSiteNameChange`, (txt) => {
      console.log('在双重单元格中，监听到了物料编码变化了--itemCodeChange2', this.fieldName, txt)
      this.nowRowHasItemCode = txt ? true : false
    })
    this.categoryId = this.param.categoryId
    this.categoryCode = this.param.categoryCode
    if (this.type == 'edit') {
      this.isEdit = true
      let record = JSON.parse(sessionStorage.getItem('editRecord'))
      this.formInfo = record[0]
      this.formInfo.siteOrgId = record[0].siteId
      this.formInfo.isDump = record[0].isDump ? 1 : 0
    } else {
      this.isEdit = false
      this.formInfo = {
        categoryId: '',
        organizationId: '',
        organizationCode: '',
        siteOrgId: '',
        itemId: '',
        isDump: '',
        supplierAddRequestList: []
      }
    }
    this.show() //弹窗出现
    // 初始化行内编辑
    console.log(1, '12')
    await this.getAllData()
    this.handleColumnsInfo()
    if (this.type == 'edit') {
      // 查询表格的数据
      this.getSupplierList()
    }
    this.onSiteFilter = utils.debounce(this.onSiteFilter, 1000)
    this.onFiltering = utils.debounce(this.onFiltering, 1000)
  },
  methods: {
    selectedChanged(val) {
      this.isInner = val.itemInfo.isInner
      Object.assign(this.selectedOtherInfo, val.itemInfo || {})
      console.log(this.selectedOtherInfo, '最新的额外数据导入的')
    },
    async onFiltering(e) {
      if (this.itemName !== e.text) {
        this.itemName = e.text
        await this.fuzzyPagedQuery()
        e.updateData(this.pageList)
        this.itemState = false
      }
      // e.updateData(this.pageList.filter((x) => x.itemName.includes(e.text)));
    },
    async onSiteFilter(e) {
      if (this.inputName !== e.text) {
        this.inputName = e.text
        await this.fuzzyQuery()
        e.updateData(this.fuzzyList)
        this.siteState = false
      }
      // e.updateData(this.fuzzyList.filter((x) => x.siteName.includes(e.text)));
    },
    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    getSupplierList() {
      let data = this.formInfo
      let obj = {
        itemCode: data.itemCode,
        organizationCode: data.organizationCode,
        siteCode: data.siteCode
      }
      this.$API.sourceLists
        .criteriaQuery(obj)
        .then((res) => {
          res.data.forEach((item) => {
            item.isDis = true
          })
          this.formInfo.supplierAddRequestList = res.data
          res.data.forEach((item) => {
            item.addId = this.addId++
            // if (item.timeInfo && item.timeInfo.length === 10) {
            //   item.timeInfo = new Date(item.timeInfo);
            // } else {
            //   item.timeInfo = null;
            // }
            // item.isEntry = "1"; //是否是带入的数据
          })
          this.allData = res.data
          this.$set(this.templateConfigEdit[0].grid, 'dataSource', this.allData)
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          if (!this.inputState) {
            let supplierAddRequestList = this.$refs.templateRefEdit
              .getCurrentUsefulRef()
              .gridRef.ejsRef.getCurrentViewRecords()
            for (let index in supplierAddRequestList) {
              if (
                !!supplierAddRequestList[index].supplierName &&
                !!supplierAddRequestList[index].purchaseOrganizationName &&
                !!supplierAddRequestList[index].startTime &&
                !!supplierAddRequestList[index].endTime &&
                !!supplierAddRequestList[index].mrp
              ) {
                if (supplierAddRequestList[index].isDumpDesc != this.$t('否')) {
                  // 转存储为是的话，采购工厂必填
                  if (!supplierAddRequestList[index].dumpSiteName) {
                    this.$toast({
                      content: this.$t(
                        '列表行号为' + (parseInt(index) + 1) + this.$t('采购工厂字段未输入')
                      ),
                      type: 'warning'
                    })
                    return
                  }
                }
              } else {
                let str = []
                if (!supplierAddRequestList[index].supplierName) {
                  str.push('"供应商"')
                }
                if (!supplierAddRequestList[index].purchaseOrganizationName) {
                  str.push('"采购组织"')
                }
                if (!supplierAddRequestList[index].startTime) {
                  str.push('"有效自"')
                }
                if (!supplierAddRequestList[index].endTime) {
                  str.push('"有效止"')
                }
                if (!supplierAddRequestList[index].mrp) {
                  str.push('"MRP"')
                }
                if (str) {
                  this.$toast({
                    content: this.$t(
                      '列表行号为' + (parseInt(index) + 1) + str.join(',') + this.$t('字段未输入')
                    ),
                    type: 'warning'
                  })
                  return
                }
              }
            }
          }
          if (this.type == 'add') {
            this.$loading()
            this.formInfo.categoryId = this.categoryId
            this.formInfo.supplierAddRequestList = this.$refs.templateRefEdit
              .getCurrentUsefulRef()
              .gridRef.ejsRef.getCurrentViewRecords()
            // 行内供应商的数据会被带着，这块去掉
            // this.formInfo.supplierAddRequestList.forEach(item=>{
            //   item.supplierData = [];
            // })
            /*-------
            逻辑调整了:
            1.新建了 '货源清单' 的列表,后端兼容了老页面和新的页面
            2.this.$API.sourceLists.batchAdd(this.formInfo) 原来直接传 this.formInfo
            3.新的传参方式: let params = {
                         paramObj:{}, //老页面参数
                         request:null //新页面参数
            }
            4.老页面  request 为 null   新页面 paramObj 为 null
             ----------*/
            let params = {
              paramObj: { ...this.formInfo }, //老页面参数
              request: null //新页面参数
            }
            this.$API.sourceLists
              .batchAdd(params)
              .then(() => {
                this.$hloading()
                this.$toast({
                  content: this.$t('保存成功'),
                  type: 'success'
                })
                this.$emit('confirm-function')
              })
              .catch((err) => {
                this.$hloading()
                this.$toast({
                  content: err.msg,
                  type: 'error'
                })
              })
          } else if (this.type == 'edit') {
            this.$loading()
            let updateList = this.$refs.templateRefEdit
              .getCurrentUsefulRef()
              .gridRef.ejsRef.getCurrentViewRecords()
            let params = {
              paramObj: { updateList }, //老页面参数
              request: null //新页面参数
            }
            //老的传参方式{updateList}
            this.$API.sourceLists
              .batchUpdate(params)
              .then(() => {
                this.$hloading()
                this.$toast({
                  content: this.$t('保存成功'),
                  type: 'success'
                })
                this.$emit('confirm-function')
              })
              .catch((err) => {
                this.$hloading()
                this.$toast({
                  content: err.msg,
                  type: 'error'
                })
              })
          }
        }
      })
    },
    // 保存并同步
    confirmSynch() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          if (!this.inputState) {
            let supplierAddRequestList = this.$refs.templateRefEdit
              .getCurrentUsefulRef()
              .gridRef.ejsRef.getCurrentViewRecords()
            for (let index in supplierAddRequestList) {
              if (
                !!supplierAddRequestList[index].supplierName &&
                !!supplierAddRequestList[index].purchaseOrganizationName &&
                !!supplierAddRequestList[index].startTime &&
                !!supplierAddRequestList[index].endTime &&
                !!supplierAddRequestList[index].mrp
              ) {
                if (supplierAddRequestList[index].isDumpDesc != this.$t('否')) {
                  // 转存储为是的话，采购工厂必填
                  if (!supplierAddRequestList[index].dumpSiteName) {
                    this.$toast({
                      content: this.$t(
                        '列表行号为' + (parseInt(index) + 1) + this.$t('采购工厂字段未输入')
                      ),
                      type: 'warning'
                    })
                    return
                  }
                }
              } else {
                let str = []
                if (!supplierAddRequestList[index].supplierName) {
                  str.push('"供应商"')
                }
                if (!supplierAddRequestList[index].purchaseOrganizationName) {
                  str.push('"采购组织"')
                }
                if (!supplierAddRequestList[index].startTime) {
                  str.push('"有效自"')
                }
                if (!supplierAddRequestList[index].endTime) {
                  str.push('"有效止"')
                }
                if (!supplierAddRequestList[index].mrp) {
                  str.push('"MRP"')
                }
                if (str) {
                  this.$toast({
                    content: this.$t(
                      '列表行号为' + (parseInt(index) + 1) + str.join(',') + this.$t('字段未输入')
                    ),
                    type: 'warning'
                  })
                  return
                }
              }
            }
          }
          if (this.type == 'add') {
            this.$loading()
            this.formInfo.categoryId = this.categoryId
            this.formInfo.supplierAddRequestList = this.$refs.templateRefEdit
              .getCurrentUsefulRef()
              .gridRef.ejsRef.getCurrentViewRecords()
            // 行内供应商的数据会被带着，这块去掉
            // this.formInfo.supplierAddRequestList.forEach((item) => {
            //   item.supplierData = [];
            // });
            let params = {
              paramObj: { ...this.formInfo }, //老页面参数
              request: null //新页面参数
            }
            this.$API.sourceLists
              .batchAddAndSync(params)
              .then(() => {
                this.$hloading()
                this.$toast({
                  content: this.$t('保存并同步发送成功'),
                  type: 'success'
                })
                this.$emit('confirm-function')
              })
              .catch((err) => {
                this.$hloading()
                this.$toast({
                  content: err.msg,
                  type: 'error'
                })
              })
          } else if (this.type == 'edit') {
            this.$loading()
            let updateList = this.$refs.templateRefEdit
              .getCurrentUsefulRef()
              .gridRef.ejsRef.getCurrentViewRecords()
            let params = {
              paramObj: { updateList }, //老页面参数
              request: null //新页面参数
            }
            this.$API.sourceLists
              .batchUpdateAndSync(params)
              .then(() => {
                this.$hloading()
                this.$toast({
                  content: this.$t('保存并同步发送成功'),
                  type: 'success'
                })
                this.$emit('confirm-function')
              })
              .catch((err) => {
                this.$hloading()
                this.$toast({
                  content: err.msg,
                  type: 'error'
                })
              })
          }
        }
      })
    },
    changeOrg(e) {
      if (this.type !== 'edit') {
        this.formInfo.siteOrgId = ''
        this.fuzzyList = []
        this.formInfo.itemId = ''
        this.pageList = []
        this.templateKey++
      }
      let { itemData } = e
      this.formInfo.orgCode = itemData.orgCode
      this.formInfo.organizationId = e.value
      console.log(itemData, 'itemDataitemData')
      // 把值传给行内select
      this.$store.commit('setUser', {
        organizationCode: itemData.orgCode,
        categoryCode: this.categoryCode
      })
      this.fuzzyQuery()
    },
    getChildrenCompanyOrganization() {
      this.$loading()
      this.$API.sourceLists['getChildrenCompanyOrganization2']({
        organizationLevelCodes: ['ORG02', 'ORG01'],
        orgType: 'ORG001PRO',
        includeItself: true
      })
        .then((result) => {
          this.$hloading()
          if (result.data) {
            this.orgList = result.data?.filter((item) => {
              return item.orgLevelTypeCode === 'ORG02' || item.orgLevelTypeCode === 'ORG01'
            })
          } else {
            this.orgList = []
          }
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    openFun() {
      if (!this.siteState) {
        this.inputName = ''
        this.fuzzyQuery()
      }
    },
    async fuzzyQuery() {
      this.$loading()
      await this.$API.sourceLists
        .fuzzyQuery({
          fuzzyParam: this.inputName,
          organizationId: this.formInfo.organizationId
        })
        .then((res) => {
          this.$hloading()
          res.data?.forEach((item) => {
            item.codeName = item.siteCode + '-' + item.siteName
          })
          this.fuzzyList = res.data
          this.siteState = true
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    changeSite(e) {
      console.log(e, e.itemData, 'itemDataitemData')
      if (this.type !== 'edit') {
        this.formInfo.itemId = ''
        this.pageList = []
      }
      this.formInfo.siteOrgId = e.value
      // 物料
      this.fuzzyPagedQuery()
    },
    openItem() {
      if (!this.itemState) {
        this.itemName = ''
        this.fuzzyPagedQuery()
      }
    },
    async fuzzyPagedQuery() {
      this.$loading()
      let obj = {
        page: {
          current: 1,
          size: 20
        },
        enableEs: false,
        condition: 'and',
        rules: [
          {
            field: 'organizationId',
            type: 'number',
            operator: 'equal',
            value: this.formInfo.siteOrgId
          },
          {
            field: 'categoryCode',
            type: 'string',
            operator: 'in',
            value: [this.categoryCode]
          }
        ]
      }

      if (this.itemName) {
        obj.rules.push({
          condition: 'and',
          rules: [
            {
              field: 'itemCode',
              type: 'string',
              operator: 'contains',
              value: this.itemName || ''
            },
            {
              condition: 'or',
              field: 'itemName',
              type: 'string',
              operator: 'contains',
              value: this.itemName || ''
            }
          ]
        })
      }
      await this.$API.sourceLists
        .itemPagedQuery(obj)
        .then((res) => {
          this.$hloading()
          if (this.type == 'edit') {
            //编辑状态下，当前数据，可能不在下拉列表中
            let _find = res.data.records.find((e) => e.id === this.formInfo.itemId)
            if (!_find) {
              res.data.records.push({
                itemCode: this.formInfo.itemCode,
                itemName: this.formInfo.itemName,
                id: this.formInfo.itemId
              })
            }
          }
          res.data.records?.forEach((item) => {
            item.itemCodeName = item.itemCode + '-' + item.itemName
          })
          this.pageList = res.data.records || []
          this.itemState = true
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    async getAllData() {
      console.log(1, '里面111111111111')
      this.$loading()
      await this.getMRP()
      await this.getSupplier()
      // await this.queryByOrgcode();
      this.$hloading()
    },
    async getMRP() {
      await this.$API.sourceLists['queryDict']({
        dictCode: 'MRP'
      })
        .then((res) => {
          this.mrpLists = res.data
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    async getSupplier() {
      let obj = {
        categoryCodeList: [this.categoryCode],
        organizationCode: this.formInfo.organizationCode || ''
      }
      await this.$API.sourceLists
        .criteriaQuerySupplier(obj)
        .then((res) => {
          this.supplierData = res.data
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    async queryByOrgcode() {
      await this.$API.sourceLists
        .queryByOrgcode({
          // organizationTypeCode: "BUORG002ADM",//原来
          supplierInternalCode: this,
          orgCode: ''
        })
        .then((res) => {
          this.orgCodeLists = res.data
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    // 处理表头数据
    handleColumnsInfo() {
      forecastColumnData.length = 0 // 清空表头数据
      // 固定的表头
      const forecastColumns = this.formatTableColumnData(
        contactsInfo(this.type, this.supplierData, this.orgCodeLists, this.mrpLists)
      )
      const columns = [].concat(forecastColumns)
      columns.forEach((item) => {
        forecastColumnData.push(item)
      })
    },
    // 行内样式
    formatTableColumnData(data) {
      const colData = []
      data.forEach((col) => {
        const defaultCol = {
          ...col
        }
        col.field = col.field ? col.field : col.type
        if (col.field === 'checkbox') {
          defaultCol.editTemplate = Component.empty
        } else if (col.field === 'syncStatus') {
          // 状态 不可修改
          defaultCol.editTemplate = Component.select({
            dataKey: col.field,
            dataList: [
              { text: this.$t('已同步'), value: 1 },
              { text: this.$t('未同步'), value: 0 }
            ],
            state: true
          })
        } else if (col.field === 'supplierId') {
          // 供应商
          defaultCol.editTemplate = Component.select({
            dataKey: col.field,
            fields: this.supplierFields,
            dataList: this.supplierData,
            state: true
          })
        } else if (col.field === 'purchaseOrganizationId') {
          // 采购组织
          defaultCol.editTemplate = Component.select({
            dataKey: col.field,
            fields: this.codeFields,
            dataList: this.orgCodeLists,
            state: true
          })
        } else if (col.field === 'startTime') {
          // 开始时间
          defaultCol.editTemplate = Component.datePicker({
            dataKey: col.field,
            fields: this.stageTypeFields,
            dataList: this.postList
          })
        } else if (col.field === 'endTime') {
          // 结束时间
          defaultCol.editTemplate = Component.datePicker({
            dataKey: col.field
          })
        } else if (col.field === 'limitTradeTotal') {
          // 总数
          defaultCol.editTemplate = Component.number({
            dataKey: col.field,
            precisionNum: 0
          })
        } else if (col.field === 'limitTradeTimes') {
          // 次数
          defaultCol.editTemplate = Component.number({
            dataKey: col.field,
            precisionNum: 0
          })
        } else if (col.field === 'statusDescription') {
          // 失效
          defaultCol.editTemplate = Component.input({
            dataKey: col.field
          })
        } else if (col.field === 'mrp') {
          // mrp
          defaultCol.editTemplate = Component.select({
            dataKey: col.field,
            fields: this.stageTypeFields,
            dataList: this.mrpLists,
            state: false
          })
        }
        colData.push(defaultCol)
      })
      return colData
    },
    actionBegin(args) {
      let { data, requestType } = args
      let { user } = this.$store.state
      if (user.dataList) {
        data.supplierData = user.dataList
      }
      // 行内数据新增
      if (requestType == 'add') {
        data.isDumpDesc = this.$t('否') //默认为否
        data.isDis = false
        args.data.addId = this.addId++
        this.nowEditRowFlag = args.data.addId
      } else if (requestType == 'beginEdit') {
        this.nowEditRowFlag = args.rowData.addId
      } else if (requestType == 'save') {
        data.isInner = this.isInner
      }
    },
    actionComplete(item) {
      let { data, requestType } = item
      if (requestType == 'save') {
        // 验证必输
        if (
          !!data.supplierName &&
          !!data.purchaseOrganizationName &&
          !!data.startTime &&
          !!data.endTime &&
          !!data.mrp
        ) {
          if (data.isDumpDesc == this.$t('否')) {
            this.inputState = true
          } else {
            // 转存储为是的话，采购工厂必填
            if (data.dumpSiteName) {
              this.inputState = true
            } else {
              this.$toast({
                content: this.$t('"采购工厂"字段未输入'),
                type: 'warning'
              })
              this.inputState = false
            }
          }
        } else {
          let str = []
          if (!data.supplierName) {
            str.push('"供应商"')
          }
          if (!data.purchaseOrganizationName) {
            str.push('"采购组织"')
          }
          if (!data.startTime) {
            str.push('"有效自"')
          }
          if (!data.endTime) {
            str.push('"有效止"')
          }
          if (!data.mrp) {
            str.push('"MRP"')
          }
          if (str) {
            this.$toast({
              content: this.$t(str.join(',') + this.$t('字段未输入')),
              type: 'warning'
            })
          }
          this.inputState = false
        }
      }
      let row = this.getRow()
      console.log('actionComplete', item, row)
      this.$refs.templateRefEdit.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
    },
    getRow() {
      //获取编辑或者新增的数据
      let currentRecords = this.$refs.templateRefEdit
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      let row = cloneDeep(this.selectedOtherInfo)
      let info = {}
      currentRecords.some((item) => {
        if (item.addId == this.nowEditRowFlag) {
          Object.assign(item, row)
          info = item
        }
      })
      return info
    },
    handleClickToolBarEdit(args) {
      const { toolbar, grid } = args
      if (toolbar.id == 'add') {
        // 新增
        this.$refs.formInfo.validate((valid) => {
          if (valid) {
            this.$refs.templateRefEdit.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
          }
        })
      } else if (toolbar.id == 'del') {
        if (this.type == 'edit') {
          this.$toast({
            content: this.$t('编辑状态下不可删除行数据'),
            type: 'warning'
          })
        } else {
          // 选中的数据
          let selectedRecords = grid.getSelectedRecords()
          if (selectedRecords.length > 0) {
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: this.$t('确认删除选中的数据？')
              },
              success: () => {
                let allData = this.$refs.templateRefEdit
                  .getCurrentUsefulRef()
                  .gridRef.ejsRef.getCurrentViewRecords()
                selectedRecords.forEach((item) => {
                  allData.forEach((sitem, sindex) => {
                    if (item.cid == sitem.cid) {
                      allData.splice(sindex, 1)
                    }
                  })
                })
                this.$set(this.templateConfigEdit[0].grid, 'dataSource', allData)
              }
            })
          } else {
            this.$toast({
              content: this.$t('请选择数据'),
              type: 'warning'
            })
          }
        }
      } else if (toolbar.id == 'endEdit') {
        this.$refs.templateRefEdit.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    },
    cancel() {
      this.$emit('cancel-function')
    }
  },
  destroyed() {
    sessionStorage.removeItem('editRecord')
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
.form-item-spec {
  width: 414px;
}
/deep/ .form-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
/deep/ .full-width {
  width: 100% !important;
}
</style>
