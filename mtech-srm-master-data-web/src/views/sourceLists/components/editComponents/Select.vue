<template>
  <div>
    <mt-select
      :id="data.column.field"
      v-model="model"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @open="startOpen"
      @change="selectChange"
      :open-dispatch-change="false"
      :disabled="isDisabled"
      :allow-filtering="true"
      :filtering="serchText"
    ></mt-select>
  </div>
</template>
<script>
import bus from '@/utils/bus'
export default {
  data() {
    return {
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'label', value: 'value' },
      dataSource: [],
      itemCode: '',
      itemId: '',
      purchase: '', //采购组code
      plan: '', //计划组code
      isDisabled: false,
      deliveryMethod: '', //配送方式0直送 1非直送
      siteCode: '', //工厂code
      processorCode: '', //加工商code
      warehouseCode: '', //库存地点编号
      model: undefined
    }
  },
  async mounted() {
    this.model = this.data[this.data.column.field]
    if (this.data.column.field === 'dumpSiteName') {
      if (this.data.isDumpDesc == this.$t('否')) {
        this.isDisabled = true
      } else {
        this.isDisabled = false
      }
      //监听isDump变化
      bus.$on('isDumpChange', (val) => {
        if (!val) {
          console.log('=isDump变=', val, this.data)
          this.data.dumpSiteName = null
          this.isDisabled = true
        } else {
          this.isDisabled = false
        }
      })
      //工厂下拉
      this.getFactoryItem()
    }
    if (this.data.column.field === 'isDumpDesc') {
      this.dataSource = [
        { text: this.$t('是'), value: true },
        { text: this.$t('否'), value: false }
      ]
      this.fields = { text: 'text', value: 'text' }

      // 供应商为内部供应商时，“转存类货源”字段可选择，否则不可选默认否
      if (this.data.isInner == false) {
        this.isDisabled = true
      } else {
        this.isDisabled = false
      }

      bus.$on('isSupplierChange', (val) => {
        if (!val) {
          this.data.isDump = false
          this.data.isDumpDesc = this.$t('否')
          this.isDisabled = true
          bus.$emit('isDumpChange', this.data.isDump)
        } else {
          this.isDisabled = false
        }
      })
    }
    if (this.data.column.field === 'supplierName') {
      this.getSupplier(this.$store.state.user)
      this.dataSource = this.data.column.selectOptions
    }
    if (this.data.column.field === 'purchaseOrganizationName') {
      let orgCode = this.data.organizationCode
      let supplierInternalCode = this.data.partnerCode

      this.queryByOrgcode(orgCode, supplierInternalCode)

      bus.$on('isOrgnationChange', (val) => {
        this.data.orgCode = val.orgCode
        this.data.supplierInternalCode = val.supplierInternalCode
        this.$store.commit('setRoleRowInfo', {
          orgCode: val.orgCode,
          supplierInternalCode: val.supplierInternalCode
        })
        this.data.purchaseOrganizationName = null
        // this.data.purchaseOrganizationId = null;
      })
    }
  },
  methods: {
    getSupplier(invitationList) {
      let obj = {
        categoryCodeList: [invitationList.categoryCode],
        organizationCode: invitationList.organizationCode || ''
      }

      this.$API.sourceLists.criteriaQuerySupplier(obj).then((res) => {
        const findItem = res.data.find((e) => e.supplierCode === this.data.supplierCode)

        if (!findItem) {
          if (this.data.supplierName && this.data.supplierCode) {
            res.data.push({
              supplierName: this.data.supplierName,
              supplierCode: this.data.supplierCode
            })
          }
        }

        res.data.forEach((item) => {
          item.codeName = item.supplierName
          item.code = item.supplierCode

          if (this.data.supplierCode === item.supplierCode) {
            item.codeName = this.data.supplierName
          }
        })
        this.dataSource = res.data
      })
      if (this.data.syncStatus != null) {
        this.isDisabled = true
      }
      this.fields = { text: 'codeName', value: 'codeName' }
    },
    serchText(val) {
      console.log('搜索值', val)
      if (this.data.column.field == 'itemCode') {
        this.getCategoryItem(val && val.text ? val.text : '')
      }
    },
    setDisabled1() {
      if (this.data.status === 4) {
        this.isDisabled = true
      } else {
        this.isDisabled = false
      }
    },
    setDisabled() {
      if (this.data.isEntry === '1') {
        this.isDisabled = true
      }
      if (this.data.isEntry === '2') {
        this.isDisabled = false
      }
    },
    getFactoryItem() {
      this.$loading()
      this.$API.sourceLists.fuzzyQuery({}).then((res) => {
        this.$hloading()
        this.dataSource = res.data
      })
      if (this.data.isDump == 0) {
        this.isDisabled = true
      }
      this.fields = { text: 'siteName', value: 'siteName' }
    },
    startOpen() {
      // 公司与供应商联动
      if (this.data.column.field === 'supplierName') {
        this.getSupplier(this.$store.state.user)
        this.dataSource = this.data.column.selectOptions
      }
      // “采购组织”字段需要根据公司+供应商带出对应的值集
      if (this.data.column.field === 'purchaseOrganizationName') {
        // 如果没有选择供应商提示，先选择供应商
        // 查询接口
        let orgCode = this.data.organizationCode
        let supplierInternalCode = this.data.partnerCode
        if (!!orgCode && !!supplierInternalCode) {
          this.queryByOrgcode(orgCode, supplierInternalCode)
        } else {
          this.queryByOrgcode()
        }
      }

      console.log(this.data, '下拉打开时最新的行数据')
      if (this.data.column.field === 'dumpSiteName') {
        if (!this.dataSource.length) {
          this.getFactoryItem()
        }
      } else if (this.data.column.field === 'isDumpDesc') {
        this.dataSource = [
          { text: this.$t('是'), value: true },
          { text: this.$t('否'), value: false }
        ]
        this.fields = { text: 'text', value: 'text' }
      }
    },
    selectChange(val) {
      let { organizationCode, partnerCode } = val.itemData
      console.log(val.itemData, '下拉数据的信息')
      console.log('val', val, this.data)
      if (this.data.column.field === 'isDumpDesc') {
        this.data.isDump = val.itemData.value
        bus.$emit('isDumpChange', this.data.isDump) //传给物料描述
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'isDumpDesc',
          itemInfo: {
            isDump: val.itemData.value,
            isDumpDesc: val.value
          }
        })
      }
      if (this.data.column.field === 'dumpSiteName') {
        bus.$emit('dumpSiteNameChange', val) //传给物料描述
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'dumpSiteName',
          itemInfo: {
            dumpSiteOrgId: val.itemData.organizationId
          }
        })
      }
      if (this.data.column.field === 'supplierName') {
        console.log('isSupplierChange1', val)
        bus.$emit('isSupplierChange', val.itemData.isInner)
        // isInner字段，判断是否内部供应商 ,内部供应商时，“转存类货源”字段可选择，否则不可选默认否
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'supplierName',
          itemInfo: {
            supplierId: val.itemData.id,
            isInner: val.itemData.isInner,
            isDump: val.itemData.isInner ? true : false
          }
        })
        this.data.orgCode = organizationCode
        this.data.supplierInternalCode = partnerCode
        bus.$emit('isOrgnationChange', {
          orgCode: this.data.orgCode,
          supplierInternalCode: this.data.supplierInternalCode
        })
      }
      if (this.data.column.field === 'purchaseOrganizationName') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'purchaseOrganizationName',
          itemInfo: {
            purchaseOrganizationId: val.itemData.id
          }
        })
      }
    },
    async queryByOrgcode(orgCode, supplierInternalCode) {
      let roleRowInfo = this.$store.state.roleRowInfo
      let companyOrgCode = orgCode ? orgCode : roleRowInfo.orgCode
      let partnerCode = supplierInternalCode
        ? supplierInternalCode
        : roleRowInfo.supplierInternalCode

      if (!!companyOrgCode && !!partnerCode) {
        await this.$API.sourceLists
          .queryByOrgcode({
            companyOrgCode: companyOrgCode || '',
            partnerCode: partnerCode || ''
          })
          .then((res) => {
            if (this.data.purchaseOrganizationId && this.data.purchaseOrganizationName) {
              const findItem = res.data.find((e) => e.id === this.data.purchaseOrganizationId)

              if (!findItem) {
                res.data.push({
                  name: this.data.purchaseOrganizationName,
                  id: this.data.purchaseOrganizationId,
                  code: this.data.purchaseOrganizationCode
                })
              }
            }

            this.dataSource = res.data
            this.fields = { text: 'name', value: 'name' }
          })
          .catch((err) => {
            this.$toast({
              content: err.msg,
              type: 'error'
            })
          })
      }
    }
  }
}
</script>
