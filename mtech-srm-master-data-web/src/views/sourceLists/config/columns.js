import { i18n } from '@/main.js'
import Select from '../components/editComponents/Select.vue'
// 已认证主表
export const masterTable = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    width: '150'
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    width: '150'
  },
  {
    field: 'organizationCode',
    headerText: i18n.t('公司'),
    width: '150'
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂'),
    width: '150'
  },
  {
    field: 'organizationName',
    headerText: i18n.t('公司名称'),
    width: '150'
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂/地点'),
    width: '150'
  },
  {
    field: 'activeTotal',
    headerText: i18n.t('有效货源个数'),
    width: '150'
  }
]
// 已认证子表
export const sublistTable = [
  {
    field: 'supplierName',
    headerText: i18n.t('供应商'),
    width: '150'
  },
  {
    field: 'organizationName',
    headerText: i18n.t('采购组织'),
    width: '150'
  },
  {
    field: 'startTime',
    headerText: i18n.t('有效自'),
    width: '150'
  },
  {
    field: 'endTime',
    headerText: i18n.t('有效止'),
    width: '150'
  },
  {
    field: 'limitTradeTotal',
    headerText: i18n.t('限制交易总数量'),
    width: '150'
  },
  {
    field: 'limitTradeTimes',
    headerText: i18n.t('限制交易次数'),
    width: '150'
  },
  {
    field: 'statusId',
    headerText: i18n.t('货源状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('激活'), 0: i18n.t('失效') }
    }
  },
  {
    field: 'syncStatus',
    headerText: i18n.t('状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: [
        {
          value: 0,
          text: i18n.t('未同步'),
          cssClass: 'title-#6386c1'
        },
        {
          value: 1,
          text: i18n.t('已同步'),
          cssClass: 'title-#6386c1'
        },
        {
          value: 2,
          text: i18n.t('同步失败'),
          cssClass: 'title-#9baac1'
        }
      ]
    }
  }
]

export const forecastColumnData = [
  {
    allowEditing: true,
    field: 'checkBox',
    fieldCode: 'checkBox',
    showInColumnChooser: false,
    type: 'checkbox',
    width: '50'
  },
  {
    width: '150',
    field: 'thePrimaryKey', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true,
    allowEditing: false
  }
]

export const contactsInfo = (type, supplierData, orgCodeLists, mrpLists) => {
  return [
    {
      width: '50',
      type: 'checkbox'
    },
    {
      field: 'syncStatus',
      headerText: i18n.t('状态'),
      width: '150',
      visible: type == 'edit' ? true : false,
      valueConverter: {
        type: 'map',
        map: { 0: i18n.t('未同步'), 1: i18n.t('已同步') }
      },
      searchOptions: {
        elementType: 'select',
        dataSource: [
          { key: 0, value: i18n.t('未同步') },
          { key: 1, value: i18n.t('已同步') }
        ],
        fields: { text: 'value', value: 'key' }
      }
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商'),
      width: '300',
      editTemplate: () => {
        return { template: Select }
      }
    },
    {
      field: 'purchaseOrganizationName',
      headerText: i18n.t('采购组织'),
      width: '150',
      editTemplate: () => {
        return { template: Select }
      }
      // valueAccessor: function (field, data, column) {
      //   let dataSource = orgCodeLists || [];
      //   return dataSource.filter((i) => i.id == data[field])?.[0]?.organizationName;
      // },
    },
    {
      field: 'startTime',
      headerText: i18n.t('有效自'),
      width: '150',
      valueConverter: { type: 'date', format: 'YYYY-MM-DD' }
    },
    {
      field: 'endTime',
      headerText: i18n.t('有效止'),
      width: '150',
      valueConverter: { type: 'date', format: 'YYYY-MM-DD' }
    },
    {
      field: 'limitTradeTotal',
      headerText: i18n.t('限制交易总数量'),
      width: '150'
    },
    {
      field: 'limitTradeTimes',
      headerText: i18n.t('限制交易次数'),
      width: '150'
    },
    {
      field: 'isDumpDesc',
      headerText: i18n.t('转储类货源'),
      width: '150',
      editTemplate: () => {
        return { template: Select }
      }
    },
    {
      field: 'dumpSiteName',
      headerText: i18n.t('采购工厂'),
      selectOptions: [],
      width: '150',
      editTemplate: () => {
        return { template: Select }
      }
    },
    {
      field: 'dumpSiteOrgId',
      visible: false,
      headerText: i18n.t('采购工厂'),
      selectOptions: [],
      width: '150'
    },
    {
      field: 'mrp',
      headerText: 'MRP',
      width: '350',
      valueAccessor: function (field, data) {
        let dataSource = mrpLists || []
        return dataSource.filter((i) => i.itemCode == data[field])?.[0]?.itemName
      }
    }
  ]
}

// 资源库
export const sourceLibrary = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'itemCode',
    width: '120',
    headerText: i18n.t('物料编码'),
    searchOptions: {
      customRule: (e) => {
        return {
          label: i18n.t('物料编码'),
          field: 'item.itemCode',
          type: 'string',
          operator: 'contains',
          value: e
        }
      }
    }
  },
  {
    field: 'itemName',
    width: '120',
    headerText: i18n.t('物料名称'),
    searchOptions: {
      customRule: (e) => {
        return {
          label: i18n.t('物料名称'),
          field: 'item.itemName',
          type: 'string',
          operator: 'contains',
          value: e
        }
      }
    }
  },
  {
    field: 'categoryName',
    width: '120',
    headerText: i18n.t('一级品类'),
    searchOptions: {
      customRule: (e) => {
        return {
          label: i18n.t('一级品类'),
          field: 'source.categoryName',
          type: 'string',
          operator: 'contains',
          value: e
        }
      }
    }
  },
  {
    field: 'supplierCode',
    width: '120',
    headerText: i18n.t('供应商代码')
  },
  {
    field: 'supplierName',
    width: '120',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'organizationName',
    width: '120',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'siteName',
    width: '120',
    headerText: i18n.t('工厂/地点')
  },
  {
    field: 'statusId',
    width: '120',
    headerText: i18n.t('一级品类状态'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('注册'),
        2: i18n.t('潜在'),
        3: i18n.t('新合格'),
        4: i18n.t('临时'),
        10: i18n.t('合格'),
        11: i18n.t('预合格'),
        20: i18n.t('冻结'),
        40: i18n.t('退出')
      }
    }
  }
]
