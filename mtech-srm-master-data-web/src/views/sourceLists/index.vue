<template>
  <div class="dataPre-box">
    <div class="DS">
      <div class="DS-search">
        <MtIcon class="DS-search-icon" name="icon_input_search" />
        <mt-input
          style="width: 80%"
          :placeholder="$t('搜索品类')"
          v-model="inputText"
          @input="onInput"
        ></mt-input>
      </div>
      <div class="DS-tree">
        <mt-treeView
          id="mtTreeView"
          class="tree-view--template"
          ref="treeViewAn"
          :fields="fields"
          :auto-check="true"
          :show-check-box="false"
          @nodeSelecting="nodeSelecting"
        ></mt-treeView>
      </div>
    </div>
    <div style="width: 75%; height: 100%">
      <mt-template-page
        style="height: 100%"
        :template-config="tabConfig"
        :current-tab="currentTab"
        @handleSelectTab="handleSelectTab"
      >
        <div class="DS-right" slot="slot-0">
          <mt-template-page
            ref="groupRef"
            :use-tool-template="false"
            :template-config="groupTable"
            @handleClickToolBar="handleClickToolBar"
          ></mt-template-page>
        </div>
        <div slot="slot-1" style="height: 100%; overflow-y: auto">
          <mt-template-page ref="templateRef" :template-config="pageConfig" @input="input" />
        </div>
      </mt-template-page>
    </div>
  </div>
</template>
<script>
import { masterTable, sublistTable, sourceLibrary } from './config/columns'
import Vue from 'vue'
import { utils } from '@mtech-common/utils'
export default {
  data() {
    const that = this
    return {
      inputText: '',
      groupTable: [
        {
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'add', icon: 'icon_table_new', title: this.$t('新增') },
                { id: 'edit', icon: 'icon_table_edit', title: this.$t('编辑') },
                {
                  id: 'SAP',
                  icon: 'icon_table_synchronize',
                  title: this.$t('同步SAP')
                }
              ]
            ]
          },
          grid: {
            columnData: masterTable,
            asyncConfig: {
              url: '/masterDataManagement/tenant/supply-source-list/group-paged-query',
              defaultRules: [
                {
                  field: 'categoryCode',
                  operator: 'equal',
                  type: 'string',
                  value: ''
                }
              ]
            },

            // 展开的详情里面添加表格
            detailTemplate: function () {
              return {
                template: Vue.component('detailTemplate', {
                  template: `<div style="height:100%; min-height:200px">
                  <mt-template-page
                    style="height:100%"
                    ref="childRef"
                    :template-config="childTable"
                  ></mt-template-page>
                </div>`,
                  data: function () {
                    return {
                      data: {},
                      childTable: [
                        {
                          grid: {
                            dataSource: [],
                            columnData: sublistTable
                          }
                        }
                      ]
                    }
                  },
                  mounted() {
                    this.$loading()
                    let data = this.data
                    let obj = {
                      itemCode: data.itemCode,
                      organizationCode: data.organizationCode,
                      siteCode: data.siteCode,
                      categoryCode: that.categoryCode
                    }
                    this.$API.sourceLists
                      .criteriaQuery(obj)
                      .then((res) => {
                        this.$hloading()
                        this.childTable[0].grid.dataSource = res.data
                      })
                      .catch((err) => {
                        this.$hloading()
                        this.$toast({
                          content: err.msg,
                          type: 'error'
                        })
                      })
                  }
                })
              }
            }
          }
        }
      ],
      // 资源库
      pageConfig: [
        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          toolbar: [
            // { id: "tijiao", icon: "icon_solid_Createproject", title: "提交" },
          ],
          grid: {
            allowFiltering: false,
            allowGrouping: true,
            columnData: sourceLibrary,
            asyncConfig: {
              url: '/masterDataManagement/tenant/supply/source/paged-query',
              defaultRules: [
                {
                  field: 'categoryCode',
                  type: 'string',
                  operator: 'equal',
                  value: 0
                }
              ]
            }
          }
        }
      ],

      currentTab: 0,
      tabConfig: [
        { title: this.$t('已认证') },
        {
          title: this.$t('资源库')
        }
      ],
      fields: {
        dataSource: [],
        id: 'id',
        text: 'categoryName',
        parentID: 'parentId',
        child: 'children'
      },
      categoryId: 0, //查询顶级传0即可
      categoryCode: '',
      childLists: [],
      treeData: [],
      initData: []
    }
  },
  created() {
    if (this.$route.query.tab) {
      this.currentTab = Number(this.$route.query.tab)
    }
  },
  mounted() {
    // 品类接口
    this.init()
  },
  methods: {
    // 搜索品类
    onInput(e) {
      if (e !== '') {
        let result = utils.debounce(this.searchCateGory, 1000)
        result()
      } else {
        this.fields.dataSource = []
        this.categoryId = 0
        this.init()
      }
    },
    searchCateGory() {
      if (this.inputText.trim() == '') {
        return
      }
      this.$loading()
      this.$API.sourceLists
        .queryForTree({ keyword: this.inputText })
        .then((res) => {
          this.$hloading()
          // 如果搜索的字段是第一层，则通过节点删除之前添加现有的方式，如果搜索的字段是里层的，则直接赋值
          let dataId = []
          let dataName = []
          this.initData.map((m) => {
            dataId.push(m.id)
            dataName.push(m.categoryName)
          })
          let nameStr = dataName.join(',')
          // 判断搜索字段是否是父级数据
          if (nameStr.indexOf(this.inputText) != -1) {
            const ref = this.$refs.treeViewAn
            ref.ejsInstances.removeNodes(dataId)
            ref.ejsInstances.addNodes(res.data.treeList)
          } else {
            this.fields.dataSource = res.data.treeList
          }
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    nodeSelecting(e) {
      let { nodeData } = e
      this.categoryId = nodeData.id
      this.treeData = []
      // 要code
      let data = this.$refs.treeViewAn.ejsInstances.treeData
      this.floatCompanyList(data)
      this.treeData.forEach((item) => {
        if (item.id == nodeData.id) {
          this.categoryCode = item.categoryCode
        }
      })
      if (!nodeData.expanded && !nodeData.hasChildren) {
        // 查询下级品类接口
        this.init()
      }
      // 查询表格接口
      this.handleSelectTab(this.currentTab)
    },
    handleSelectTab(e) {
      this.currentTab = e
      if (e == 0) {
        this.groupTable[0].grid.asyncConfig.defaultRules[0].value = this.categoryCode
      } else {
        this.pageConfig[0].grid.asyncConfig.defaultRules[0].value = this.categoryCode
      }
    },
    input(index, key, value) {
      this.$set(this.topInfo.scoreDetailList[index], key, value)
    },
    init() {
      this.$loading()
      this.$API.sourceLists
        .listByParent({ id: this.categoryId })
        .then((res) => {
          this.$hloading()
          if (res.data.length > 0) {
            const ref = this.$refs.treeViewAn
            ref.ejsInstances.addNodes(res.data)
            this.initData = res.data
          }
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    async handleClickToolBar(e) {
      let { toolbar } = e
      if (toolbar.id == 'add') {
        if (this.categoryId == 0) {
          this.$toast({
            content: this.$t('请选择品类'),
            type: 'warning'
          })
        } else {
          this.$dialog({
            modal: () => import('./components/addSource.vue'),
            data: {
              title: this.$t('新增'),
              type: 'add',
              param: {
                categoryId: this.categoryId,
                categoryCode: this.categoryCode
              }
            },
            success: () => {
              this.$refs.groupRef.refreshCurrentGridData()
            },
            close: () => {}
          })
        }
      } else if (toolbar.id == 'edit') {
        let record = e.grid.getSelectedRecords()
        if (record.length == 0 || record.length > 1) {
          this.$toast({
            content: this.$t('请选择一条数据进行编辑'),
            type: 'warning'
          })
        } else if (record.length == 1) {
          sessionStorage.setItem('editRecord', JSON.stringify(record))
          this.$dialog({
            modal: () => import('./components/addSource.vue'),
            data: {
              title: this.$t('编辑'),
              type: 'edit',
              param: {
                categoryId: this.categoryId,
                categoryCode: this.categoryCode
              }
            },
            success: () => {
              this.$refs.groupRef.refreshCurrentGridData()
            },
            close: () => {}
          })
        }
      } else if (toolbar.id == 'SAP') {
        let record = e.grid.getSelectedRecords()
        if (record.length == 0) {
          this.$toast({
            content: this.$t('请选择数据'),
            type: 'warning'
          })
        } else {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认将所选数据同步SAP？')
            },
            success: async () => {
              // 需要同步的数据是子表的数据，record只能拿到主表行的，
              // 根据主表行的数据，查询子表，
              this.$loading()
              await Promise.all(
                record.map(async (item) => {
                  await this.getChildList(item)
                })
              )

              let ids = []
              this.childLists.forEach((item) => {
                ids.push(item.id)
              })
              this.$API.sourceLists
                .batchSyncOutward({ ids })
                .then(() => {
                  this.$hloading()
                  this.$refs.groupRef.refreshCurrentGridData()
                  this.$toast({
                    content: this.$t('同步发送成功'),
                    type: 'success'
                  })
                })
                .catch((err) => {
                  this.$hloading()
                  this.$toast({
                    content: err.msg,
                    type: 'error'
                  })
                })
            }
          })
        }
      }
    },
    async getChildList(item) {
      let data = item
      let obj = {
        itemCode: data.itemCode,
        organizationCode: data.organizationCode,
        siteCode: data.siteCode
      }
      await this.$API.sourceLists
        .criteriaQuery(obj)
        .then((res) => {
          this.childLists = this.childLists.concat(res.data)
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    // 扁平化树
    floatCompanyList(treeData) {
      for (let i in treeData) {
        this.treeData.push(treeData[i])
        if (treeData[i].children) {
          this.floatCompanyList(treeData[i].children)
        }
      }
    }
  }
}
</script>
<style scoped lang="scss">
// @import "./components/css/bundle.css";
/deep/ .common-template-page .repeat-template {
  height: calc(100% - 50px);
}
/deep/.e-treeview {
  height: 100%;
}
/deep/.e-treeview .e-ul {
  height: 100%;
  // padding: 0 0 0 2px !important
}
/deep/.e-detailrow .grid-container .mt-data-grid .e-grid .e-gridcontent .e-content {
  height: auto !important;
}
.dataPre-box {
  height: 100%;
  display: flex;
  background: rgba(255, 255, 255, 1);
  .DS-right {
    width: 100%;
    height: 100%;
    overflow-y: scroll;
    border: 1px solid rgba(232, 232, 232, 1);
  }
  .DS {
    width: 25%;
    height: 100%;
    border: 1px solid rgba(232, 232, 232, 1);
    margin-right: 10px;
    .DS-tree {
      height: calc(100% - 50px);
      overflow-y: auto;
    }
    .DS-search {
      display: flex;
      height: 46px;
      line-height: 46px;
      border-bottom: 1px solid #e8e8e8;
      .DS-search-icon {
        color: #979797;
        font-size: 16px;
        padding: 15px 10px;
      }
    }
    .data-screen-select {
      color: #585e6b;
      font-weight: 400;
      width: 108px;
      height: 22px;
      background: rgba(250, 250, 250, 1);
      border: 1px solid rgba(232, 232, 232, 1);
      border-radius: 4px;
      // margin-left: 30px;
      display: flex;
      flex-direction: row;
      align-content: center;
      flex-wrap: wrap;
      // position: absolute;
      // z-index: 99;
      cursor: pointer;

      span {
        padding-left: 4px;
        font-size: 12px;
        width: 85px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .selectClass {
        height: 22px;
        line-height: 22px;
        font-size: 12px;
        // color: #292929;
        // background:transparent;
        border: none;
        border-radius: 4px;

        .e-control {
          min-height: 16px !important;
        }

        .e-input-group-icon {
          margin: 0 !important;
        }
      }
    }
    .DS-content {
      width: 100%;
      height: 100%;
      display: flex;
      .left-box {
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 1);

        margin-right: 16px;
        .DS-top {
          width: 90%;
          height: 40px;
          line-height: 40px;
          margin: auto;
        }
        .DS-left {
          margin-top: 10px;
          width: 100%;
          height: calc(100% - 10px);
          border: 1px solid rgba(232, 232, 232, 1);
          border-radius: 6px;
          font-size: 14px;
          color: #4d5b6f;
          .left-title {
            height: 50px;
            line-height: 50px;
            padding-left: 20px;
          }
          .DS-search {
            display: flex;
            height: 46px;
            line-height: 46px;
            border-bottom: 1px solid #e8e8e8;
            .DS-search-icon {
              color: #979797;
              font-size: 16px;
              padding: 15px 10px;
            }
          }
        }
      }
    }
  }
}
//选择文件 导入文件
.import-process {
  width: 360px;
  height: 24px;
  margin: 45px auto 0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  ul {
    width: 40px;
    height: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    li {
      width: 4px;
      height: 4px;
      border-radius: 50%;
      background-color: #6386c1;
    }
  }

  .choose {
    width: 99px;
    height: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    i {
      display: block;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background-color: #6386c1;
      text-align: center;
      line-height: 24px;
      color: #fff;
      font-size: 14px;
      font-style: normal;
    }
    span {
      display: block;
      width: 71px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(0, 70, 156, 1);
      border-bottom: 2px solid #6386c1;
    }
  }
  .import {
    width: 99px;
    height: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    i {
      display: block;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      border: 1px solid #6386c1;
      text-align: center;
      line-height: 24px;
      color: #6386c1;
      font-size: 14px;
      font-style: normal;
    }
    span {
      display: block;
      width: 71px;
      height: 24px;
      text-align: center;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 500;
      color: black;
      line-height: 24px;
    }
  }
}
//主体
.uploader-box {
  width: 100%;
  margin: 50px auto 0;
  .has-file {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 20px;
    .text-ellipsis {
      padding-right: 20px;
    }
  }
  .form-box {
    width: 100%;
    .form-item {
      width: 820px;
      margin: 0 auto;
      .cell-upload {
        position: relative;
        width: 820px;
        height: 250px;
        background: rgba(251, 252, 253, 1);
        border: 1px solid rgba(232, 232, 232, 1);
        margin: 0 auto;
        //覆盖的选择文件框
        .upload-input {
          width: 100%;
          height: 75%;
          background: rgba(251, 252, 253, 1);
          border: 1px solid rgba(232, 232, 232, 1);
          box-sizing: border-box;
          position: absolute;
          z-index: 2;
          top: 0;
          background: transparent;
          opacity: 0;
        }
        //添加文档和说明文字区域
        .upload-box {
          width: 100%;
          height: 100%;
          //十字架
          .plus-icon {
            width: 60px;
            height: 60px;
            position: relative;
            margin: 50px auto 0;
            border: 1px dashed #000;
            &::before {
              content: ' ';
              display: inline-block;
              width: 60px;
              height: 2px;
              background: #98aac3;
              position: absolute;
              top: 50%;
              left: -1px;
            }

            &::after {
              content: ' ';
              display: inline-block;
              width: 2px;
              height: 60px;
              background: #98aac3;
              position: absolute;
              top: -1px;
              left: 50%;
            }
          }
          //文字
          .right-state {
            text-align: center;

            .plus-txt {
              margin: 20px auto 0;
              width: 270px;
              height: 24px;
              font-size: 24px;
              font-family: PingFangSC;
              font-weight: normal;
              color: rgba(152, 170, 195, 1);
            }
            .warn-text {
              margin: 16px auto 0;
              font-size: 12px;
              width: 369px;
              height: 21px;
              font-size: 14px;
              font-family: PingFangSC;
              font-weight: normal;
              color: rgba(241, 62, 62, 1);
            }
          }
        }
      }
    }
  }
  //导入模板规范
  .template-to-import {
    width: 566px;
    height: 24px;
    font-size: 16px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(41, 41, 41, 1);
    margin-top: 10px;
    margin-left: 15px;
    .import-the-template {
      color: #00469c;
    }
  }
  .specification {
    width: 639px;
    height: 98px;
    margin-top: 31px;
    margin-left: 15px;
    font-size: 16px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(154, 154, 154, 1);
  }
}
</style>
