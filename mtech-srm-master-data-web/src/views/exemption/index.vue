<template>
  <div class="notice-hander hander">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      @handleClickToolBar="handleClickToolBar"
      @actionComplete="actionComplete"
      @actionBegin="actionBegin"
    />
  </div>
</template>
<script>
import { rowDataTemp } from './config/variable'
import { pageConfig, NewRowData } from './config'
import { i18n } from '@/main.js'
import { cloneDeep } from 'lodash'
export default {
  data() {
    return {
      i18n,
      pageConfig: pageConfig
    }
  },
  methods: {
    actionBegin(args) {
      console.log('之前的动作-----', args)
      const { requestType, action, rowIndex, rowData } = args
      if (requestType === 'add') {
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        const newRowData = cloneDeep(NewRowData)
        rowDataTemp.push(newRowData)
      } else if (requestType === 'save' && action === 'add') {
        args.data = rowDataTemp[rowDataTemp.length - 1]
        args.rowData = rowDataTemp[rowDataTemp.length - 1]
      } else if (requestType === 'save' && action === 'edit') {
        // 即将保存编辑，保存行编辑后的数据，使数据状态保持
        args.data = rowData
      } else if (requestType === 'beginEdit') {
        if (args.data !== -1) {
          // 非草稿状态不可编辑
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        }
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        rowDataTemp.push(rowData)
      }
    },
    actionComplete(args) {
      if (args.requestType == 'save') {
        const { rowIndex } = args
        const rowData = rowDataTemp[rowDataTemp.length - 1]
        const params = {
          ...rowData
        }
        const isValid = () => {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        }
        if (!params.applyType && params.applyType !== 0) {
          isValid()
          this.$toast({ content: this.$t('请选择申请类型！'), type: 'warning' })
          return
        }
        if (!params.releaseDimensionType && params.releaseDimensionType !== 0) {
          isValid()
          this.$toast({ content: this.$t('请选择放行维度！'), type: 'warning' })
          return
        }
        if (!params.companyCode) {
          isValid()
          this.$toast({ content: this.$t('请选择公司编码！'), type: 'warning' })
          return
        }
        if (!params.itemCode) {
          isValid()
          this.$toast({ content: this.$t('请选择物料编码！'), type: 'warning' })
          return
        }
        // if (!params.applyQty && params.applyType === 0) {
        //   isValid()
        //   this.$toast({ content: this.$t('请输入申请数量！'), type: 'warning' })
        //   return
        // }
        if (!params.validStartTime) {
          isValid()
          this.$toast({ content: this.$t('请选择开始日期！'), type: 'warning' })
          return
        }
        if (!params.validEndTime) {
          isValid()
          this.$toast({ content: this.$t('请选择结束日期！'), type: 'warning' })
          return
        }
        if (!params.applyReason) {
          isValid()
          this.$toast({ content: this.$t('请输入申请理由！'), type: 'warning' })
          return
        }
        if (!params.needPolicy && params.needPolicy !== 0) {
          isValid()
          this.$toast({ content: this.$t('请选择临时对策是否需要！'), type: 'warning' })
          return
        }
        if (!params.engineerUserName && params.needPolicy === 1) {
          isValid()
          this.$toast({ content: this.$t('临时对策选择为是，请输入工艺工程师！'), type: 'warning' })
          return
        }
        params.validStartTime = new Date(rowData.validStartTime).getTime()
        params.validEndTime = new Date(rowData.validEndTime).getTime()
        if (!args.rowData.status || args.rowData.status === -2 || args.rowData.status === -5) {
          this.$API.sourceFiles.saveExemptionApply(params).then((res) => {
            if (res.code === 200) {
              this.$refs.templateRef.refreshCurrentGridData()
              this.$toast({
                content: this.$t('保存成功'),
                type: 'success'
              })
            }
          })
        }
      }
    },
    getRow() {
      //获取编辑或者新增的数据
      let currentRecords = this.$refs.templateRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      let row = cloneDeep(this.selectedOtherInfo)
      let info = {}
      currentRecords.some((item) => {
        if (item.addId == this.nowEditRowFlag) {
          Object.assign(item, row)
          info = item
        }
      })
      return info
    },
    // 表头操作
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const selected = grid.getSelectedRecords()
      const idList = []
      if (toolbar.id == 'Submit' || toolbar.id == 'Delete' || toolbar.id === 'Restart') {
        if (selected.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        } else {
          for (let i = 0; i < selected.length; i++) {
            const item = selected[i]
            if (
              item.status != 0 &&
              item.status != -5 &&
              item.status != -2 &&
              toolbar.id == 'Submit'
            ) {
              this.$toast({
                content: this.$t('仅可提交状态为“草稿”、“审批驳回”、“审批撤回”状态的数据'),
                type: 'warning'
              })
              return
            }
            if (item.status != 0 && toolbar.id == 'Delete') {
              this.$toast({
                content: this.$t('仅可删状态为“草稿”状态的数据'),
                type: 'warning'
              })
              return
            }
            idList.push(item.id)
          }
        }
      }
      if (toolbar.id === 'Add') {
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id === 'Import') {
        // 导入
        this.$dialog({
          modal: () => import('@/components/uploadDialog'),
          data: {
            title: this.$t('导入'),
            paramsKey: 'importFile',
            importApi: this.$API.sourceFiles.importExemptionApply,
            downloadTemplateApi: this.$API.sourceFiles.exportExemptionApply
          },
          success: () => {
            // 列表数据拼接，公司列表数据回显
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      } else if (toolbar.id === 'Submit') {
        // 提交
        console.log('选择', selected)
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认提交选中的数据？')
          },
          success: () => {
            this.$API.sourceFiles.submitExemptionApply(idList).then((res) => {
              if (res.code === 200) {
                this.$refs.templateRef.refreshCurrentGridData()
                this.$toast({
                  content: this.$t('提交成功'),
                  type: 'success'
                })
              }
            })
          }
        })
      } else if (toolbar.id === 'Delete') {
        // 删除
        console.log('选择', selected)
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            this.$API.sourceFiles.deleteExemptionApply(idList).then((res) => {
              if (res.code === 200) {
                this.$refs.templateRef.refreshCurrentGridData()
                this.$toast({
                  content: this.$t('删除成功'),
                  type: 'success'
                })
              }
            })
          }
        })
      } else if (toolbar.id === 'Restart') {
        // 同步
        console.log('选择', selected)
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认同步选中的数据？')
          },
          success: () => {
            this.$API.sourceFiles.syncExemptionApply(idList).then((res) => {
              if (res.code === 200) {
                this.$refs.templateRef.refreshCurrentGridData()
                this.$toast({
                  content: this.$t('同步成功'),
                  type: 'success'
                })
              }
            })
          }
        })
      }
    }
  }
}
</script>
<style>
.notice-hander .mt-select-index {
  display: inline-block;
}
</style>
<style lang="scss" scope>
.hander {
  // width: 100%;
  // height: 100%;
  // display: flex;
  // flex-direction: column;
  .e-content {
    height: calc(100vh - 230px) !important;
  }
}
</style>
