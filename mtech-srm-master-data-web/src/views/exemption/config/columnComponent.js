import Vue from 'vue'
import { timeNumberToDate, timeStringToDate, addCodeNameKeyInList } from '@/utils/util'
import { rowDataTemp } from './variable'
import { utils } from '@mtech-common/utils'

export const ColumnComponent = {
  // 时间日期显示
  timeDate: (args) => {
    const { dataKey, hasTime } = args

    const template = () => {
      return {
        template: Vue.component('date', {
          template: `<div><div v-if="hasTime">{{data[dataKey] | timeFormat}}</div><div>{{data[dataKey] | dateFormat}}</div></div>`,
          data: function () {
            return { data: {}, dataKey, hasTime }
          },
          filters: {
            dateFormat(value) {
              let str = ''
              // 数据库时间戳默认值为 0，为 0 时不显示
              if (value == 0) {
                return str
              }
              if (isNaN(Number(value))) {
                str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
              } else {
                str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
              }

              return str
            },
            timeFormat(value) {
              let str = ''
              // 数据库时间戳默认值为 0，为 0 时不显示
              if (value == 0) {
                return str
              }
              if (isNaN(Number(value))) {
                str = timeStringToDate({ formatString: 'HH:MM:SS', value })
              } else {
                str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
              }

              return str
            }
          }
        })
      }
    }

    return template
  },
  // 供应商
  supplierSelect: () => {
    return {
      template: Vue.component('supplierSelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              v-model="data.supplierCode"
              :filtering="doGetDataSource"
              :data-source="supplierOptions"
              :disabled="disabled || !(!data.status || data.status === -2 || data.status === -5)"
              :fields="{ text: 'theCodeName', value: 'supplierCode' }"
              :show-clear-button="true"
              :allow-filtering="true"
              @change="supplierCodeChange"
              :open-dispatch-change="true"
              :placeholder="$t('请选择')"
            ></mt-select>
          </div>
        </div>`,
        data() {
          return {
            data: {},
            supplierOptions: [], // 供应商 下拉选项
            doGetDataSource: () => {},
            disabled: false
          }
        },
        mounted() {
          this.initGetSupplier()
          this.doGetDataSource = utils.debounce(this.getSupplier, 1000)
          this.$bus.$on('releaseDimensionTypeChange', (res) => {
            if (res === 0) {
              this.disabled = true
              this.data.supplierCode = null
              this.data.supplierName = null
            } else {
              this.disabled = false
            }
          })
        },
        methods: {
          // 主数据 获取供应商
          getSupplier(args) {
            const { text, updateData, setSelectData } = args
            const params = {
              fuzzyNameOrCode: text
            }
            this.$API.masterData
              .getSupplier(params)
              .then((res) => {
                if (res) {
                  const list = res?.data || []
                  this.supplierOptions = addCodeNameKeyInList({
                    firstKey: 'supplierCode',
                    secondKey: 'supplierName',
                    list
                  })
                  if (updateData) {
                    this.$nextTick(() => {
                      updateData(this.supplierOptions)
                    })
                  }
                  if (setSelectData) {
                    this.$nextTick(() => {
                      setSelectData()
                    })
                  }
                }
              })
              .catch(() => {})
          },
          // 初始化检索 供应商
          initGetSupplier() {
            const selectData = this.data.supplierCode
            this.getSupplier({
              text: selectData,
              setSelectData: () => {
                // api获取数据后重新赋值，防止没有赋上值得情况
                this.data.supplierCode = selectData
              }
            })
          },
          // 供应商 change
          supplierCodeChange(args) {
            const { itemData } = args
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].supplierId = itemData.id
              rowDataTemp[rowDataTemp.length - 1].supplierCode = itemData.supplierCode
              rowDataTemp[rowDataTemp.length - 1].supplierName = itemData.supplierName
            } else {
              rowDataTemp[rowDataTemp.length - 1].supplierId = null
              rowDataTemp[rowDataTemp.length - 1].supplierCode = null
              rowDataTemp[rowDataTemp.length - 1].supplierName = null
            }
          }
        }
      })
    }
  },
  // 物料
  itemSelect: () => {
    return {
      template: Vue.component('itemSelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              :disabled="!(!data.status || data.status === -2 || data.status === -5)"
              v-model="data.itemCode"
              :filtering="doGetDataSource"
              :data-source="itemOptions"
              :fields="{ text: 'theCodeName', value: 'itemCode' }"
              :show-clear-button="true"
              :allow-filtering="true"
              @change="itemCodeChange"
              :open-dispatch-change="true"
              :placeholder="$t('请选择')"
            ></mt-select>
          </div>
        </div>`,
        data() {
          return {
            data: {},
            itemOptions: [], // 物料 下拉选项
            doGetDataSource: () => {}
          }
        },
        mounted() {
          this.initGetItem()
          this.doGetDataSource = utils.debounce(this.getItem, 1000)
        },
        methods: {
          // 主数据 获取物料
          getItem(args) {
            const { text, updateData, setSelectData } = args
            const params = {
              page: { current: 1, size: 100 },
              condition: 'or',
              rules: [
                { field: 'itemCode', label: '', operator: 'equal', type: 'string', value: text },
                {
                  field: 'itemName',
                  label: '',
                  operator: 'contains',
                  type: 'string',
                  value: text
                }
              ]
            }
            if (text) {
              this.$API.item
                .pagedQueryPost(params)
                .then((res) => {
                  if (res) {
                    const list = res?.data?.records || []
                    this.itemOptions = addCodeNameKeyInList({
                      firstKey: 'itemCode',
                      secondKey: 'itemName',
                      list
                    })
                    if (updateData) {
                      this.$nextTick(() => {
                        updateData(this.itemOptions)
                      })
                    }
                    if (setSelectData) {
                      this.$nextTick(() => {
                        setSelectData()
                      })
                    }
                  }
                })
                .catch(() => {})
            }
          },
          // 初始化检索 物料
          initGetItem() {
            const selectData = this.data.itemCode
            this.getItem({
              text: selectData,
              setSelectData: () => {
                // api获取数据后重新赋值，防止没有赋上值得情况
                this.data.itemCode = selectData
              }
            })
          },
          // 物料 change
          itemCodeChange(args) {
            const { itemData } = args
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].itemId = itemData.id
              rowDataTemp[rowDataTemp.length - 1].itemCode = itemData.itemCode
              rowDataTemp[rowDataTemp.length - 1].itemName = itemData.itemName
            } else {
              rowDataTemp[rowDataTemp.length - 1].itemId = null
              rowDataTemp[rowDataTemp.length - 1].itemName = null
              rowDataTemp[rowDataTemp.length - 1].itemName = null
            }
          }
        }
      })
    }
  },
  // 当前租户下员工
  engineerSelect: () => {
    return {
      template: Vue.component('engineerSelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              v-model="data.engineerUserCode"
              :filtering="doGetDataSource"
              :data-source="employeeOptions"
              :disabled="disabled || !(!data.status || data.status === -2 || data.status === -5)"
              :fields="{ text: 'theCodeName', value: 'accountName' }"
              :show-clear-button="true"
              :allow-filtering="true"
              @change="employeeChange"
              :open-dispatch-change="true"
              :placeholder="$t('请选择')"
            ></mt-select>
          </div>
        </div>`,
        data() {
          return {
            data: {},
            employeeOptions: [], // 员工 下拉选项
            doGetDataSource: () => {},
            disabled: false
          }
        },
        mounted() {
          this.initGetEmployee()
          this.doGetDataSource = utils.debounce(this.getEmployee, 1000)
          this.$bus.$on('waySelectChange', (res) => {
            if (res === 0) {
              this.disabled = true
              this.data.engineerUserCode = ''
            } else {
              this.disabled = false
            }
          })
        },
        methods: {
          // 主数据 获取员工
          getEmployee(args) {
            const { text, updateData, setSelectData } = args
            const params = {
              fuzzyName: isNaN(text) ? text : this.data.engineerUserCode
            }
            if (text) {
              this.$API.dataScope
                .getMasteremployeeList(params)
                .then((res) => {
                  if (res) {
                    const list = res?.data || []
                    this.employeeOptions = addCodeNameKeyInList({
                      firstKey: 'employeeName',
                      secondKey: 'departmentOrgName',
                      thirdKey: 'accountName',
                      list
                    })
                    if (updateData) {
                      this.$nextTick(() => {
                        updateData(this.employeeOptions)
                      })
                    }
                    if (setSelectData) {
                      this.$nextTick(() => {
                        setSelectData()
                      })
                    }
                  }
                })
                .catch(() => {})
            }
          },
          // 初始化检索 员工
          initGetEmployee() {
            const selectData = this.data.engineerUserCode
            this.getEmployee({
              text: selectData,
              setSelectData: () => {
                // api获取数据后重新赋值，防止没有赋上值得情况
                this.data.engineerUserCode = selectData
              }
            })
          },
          // 员工 change
          employeeChange(args) {
            const { itemData } = args
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].engineerUserId = itemData.uid
              rowDataTemp[rowDataTemp.length - 1].engineerUserCode = itemData.accountName
              rowDataTemp[rowDataTemp.length - 1].engineerUserName = itemData.employeeName
            } else {
              rowDataTemp[rowDataTemp.length - 1].engineerUserId = null
              rowDataTemp[rowDataTemp.length - 1].engineerUserCode = null
              rowDataTemp[rowDataTemp.length - 1].engineerUserName = null
            }
          }
        }
      })
    }
  },
  // 公司
  companySelect: () => {
    return {
      template: Vue.component('companySelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              :disabled="!(!data.status || data.status === -2 || data.status === -5)"
              v-model="data.companyCode"
              :filtering="doGetDataSource"
              :data-source="companyOptions"
              :fields="{ text: 'theCodeName', value: 'orgCode' }"
              :show-clear-button="true"
              :allow-filtering="true"
              @change="companyCodeChange"
              :open-dispatch-change="true"
              :placeholder="$t('请选择')"
            ></mt-select>
          </div>
        </div>`,

        data() {
          return {
            data: {},
            companyOptions: [], // 公司 下拉选项
            doGetDataSource: () => {}
          }
        },
        mounted() {
          this.initGetCompany()
          this.doGetDataSource = utils.debounce(this.getCompany, 1000)
        },
        methods: {
          // 主数据 获取公司
          getCompany(args) {
            const { text, updateData, setSelectData } = args
            this.$API.masterData
              .OrgFindSpecifiedChildrenLevelOrgs({
                fuzzyParam: text || '',
                organizationLevelCodes: ['ORG02', 'ORG01'],
                orgType: 'ORG001PRO',
                includeItself: true,
                organizationIds: []
              })
              .then((res) => {
                const list = res?.data || []
                this.companyOptions = addCodeNameKeyInList({
                  firstKey: 'orgCode',
                  secondKey: 'orgName',
                  list
                })
                if (updateData) {
                  this.$nextTick(() => {
                    updateData(this.companyOptions)
                  })
                }
                if (setSelectData) {
                  this.$nextTick(() => {
                    setSelectData()
                  })
                }
              })
          },
          // 初始化检索 公司
          initGetCompany() {
            const selectData = this.data.companyCode
            this.getCompany({
              text: selectData,
              setSelectData: () => {
                // api获取数据后重新赋值，防止没有赋上值得情况
                this.data.companyCode = selectData
              }
            })
          },
          // 公司 change
          companyCodeChange(e) {
            const { itemData } = e
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].companyId = itemData.id
              rowDataTemp[rowDataTemp.length - 1].companyCode = itemData.orgCode
              rowDataTemp[rowDataTemp.length - 1].companyName = itemData.orgName
            } else {
              rowDataTemp[rowDataTemp.length - 1].companyId = null
              rowDataTemp[rowDataTemp.length - 1].companyCode = null
              rowDataTemp[rowDataTemp.length - 1].companyName = null
            }
          }
        }
      })
    }
  },
  // 带红星的表头
  requiredHeader: (args) => {
    const { headerText } = args
    const template = () => {
      return {
        template: Vue.component('requiredHeaderComponent', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{ headerText }}</span>
              </div>
            `,
          data: function () {
            return {
              data: {},
              headerText
            }
          },
          beforeDestroy() {},
          mounted() {},
          methods: {}
        })
      }
    }
    return template
  }
}
