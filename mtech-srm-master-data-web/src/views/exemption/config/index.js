import { i18n } from '@/main.js'
import Vue from 'vue'
import { rowDataTemp } from './variable'
import { ColumnComponent as Component } from './columnComponent'
import { MasterDataSelect } from '@/utils/constants'
import { formatDate } from '@/utils/util'
// list toolbar
export const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'Import', icon: 'icon_solid_Import', title: i18n.t('导入') },
  { id: 'Submit', icon: 'icon_solid_upload', title: i18n.t('提交'), permission: ['O_02_0341'] },
  { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') },
  { id: 'Restart', icon: 'icon_table_restart', title: i18n.t('同步至QMS') }
]

// 新增行固定数据
export const NewRowData = {
  thePrimaryKey: 'add' + Math.random().toString(36).substring(3, 8),
  field: null,
  name: null,
  remark: null,
  sortNo: null,
  status: null
}

// list column
export const columnData = [
  {
    type: 'checkbox',
    width: '60',
    allowEditing: false,
    ignore: true
  },
  {
    field: '_index',
    width: '80',
    headerText: i18n.t('序号')
  },
  {
    field: 'applyType',
    headerText: i18n.t('申请类型'),
    valueConverter: {
      type: 'map',
      fields: { text: 'text', value: 'value' },
      map: [
        { text: i18n.t('豁免申请'), value: 0, cssClass: ['btns warning'] },
        { text: i18n.t('特采申请'), value: 1, cssClass: ['btns warning'] }
      ]
    },
    editTemplate: function () {
      return {
        template: Vue.component('statusSelect', {
          template: `<div>
          <mt-select
          :disabled="!(!data.status || data.status === -2 || data.status === -5)"
          v-model="data.applyType"
          :data-source="dataSource"
          @change="statusSelectChange"
        ></mt-select>
        </div>`,
          data() {
            return {
              dataSource: [
                { text: i18n.t('豁免申请'), value: 0 },
                { text: i18n.t('特采申请'), value: 1 }
              ]
            }
          },
          methods: {
            statusSelectChange(e) {
              console.log('限制-----', this.data)
              rowDataTemp[rowDataTemp.length - 1]['applyType'] = e.value
              // if (e.value === 0) {
              //   rowDataTemp[rowDataTemp.length - 1].validStartTime = null
              //   rowDataTemp[rowDataTemp.length - 1].validEndTime = null
              // } else {
              //   rowDataTemp[rowDataTemp.length - 1]['applyQty'] = null
              // }
              // this.$bus.$emit('limitSelectChange', e.value)
            }
          }
        })
      }
    }
  },
  {
    field: 'releaseDimensionType',
    headerText: i18n.t('放行维度'),
    headerTemplate: Component.requiredHeader({
      headerText: i18n.t('放行维度')
    }),
    valueConverter: {
      type: 'map',
      fields: { text: 'text', value: 'value' },
      map: [
        { text: '物料+供应商', value: 1, cssClass: ['btns warning'] },
        { text: i18n.t('物料'), value: 0, cssClass: ['btns warning'] }
      ]
    },
    editTemplate: function () {
      return {
        template: Vue.component('statusSelect', {
          template: `<div>
          <mt-select
          :disabled="!(!data.status || data.status === -2 || data.status === -5)"
          v-model="data.releaseDimensionType"
          :data-source="dataSource"
          @change="statusSelectChange"
        ></mt-select>
        </div>`,
          data() {
            return {
              dataSource: [
                { text: '物料+供应商', value: 1 },
                { text: i18n.t('物料'), value: 0 }
              ]
            }
          },
          mounted() {
            this.$nextTick(() => {
              this.$bus.$emit('releaseDimensionTypeChange', this.data.releaseDimensionType)
            })
          },
          methods: {
            statusSelectChange(e) {
              if (e.value === 0) {
                rowDataTemp[rowDataTemp.length - 1]['supplierCode'] = null
                rowDataTemp[rowDataTemp.length - 1]['supplierName'] = null
              }
              this.$bus.$emit('releaseDimensionTypeChange', e.value)
              rowDataTemp[rowDataTemp.length - 1]['releaseDimensionType'] = e.value
            }
          }
        })
      }
    }
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司编码'),
    headerTemplate: Component.requiredHeader({
      headerText: i18n.t('公司编码')
    }),
    editTemplate: Component.companySelect
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称'),
    allowEditing: false
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    editTemplate: Component.supplierSelect
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    allowEditing: false
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    headerTemplate: Component.requiredHeader({
      headerText: i18n.t('物料编码')
    }),
    editTemplate: Component.itemSelect
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    allowEditing: false
  },
  {
    field: 'applyQty',
    headerText: i18n.t('申请数量'),
    editTemplate: function () {
      return {
        template: Vue.component('timeOutSecondsInput', {
          template: `<div>
          <mt-input
          :disabled="!(!data.status || data.status === -2 || data.status === -5)"
          v-model="data.applyQty"
          @change="applyCountChange"
        ></mt-input>
        </div>`,

          data() {
            return {}
          },
          methods: {
            applyCountChange(e) {
              rowDataTemp[rowDataTemp.length - 1]['applyQty'] = e
            }
          }
        })
      }
    }
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('开始日期'),
    headerTemplate: Component.requiredHeader({
      headerText: i18n.t('开始日期')
    }),
    template: Component.timeDate({
      dataKey: 'validStartTime',
      hasTime: false
    }),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    // editType: 'datepickeredit',
    // format: 'yMd',
    editTemplate: function () {
      return {
        template: Vue.component('companySelect', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <mt-date-picker
                :disabled="!(!data.status || data.status === -2 || data.status === -5)"
                v-model="data.validStartTime"
                :placeholder="$t('选择开始日期')"
                @change="timeChange"
              ></mt-date-picker>
            </div>
          </div>`,

          data() {
            return {
              data: {}
            }
          },
          created() {
            this.data.validStartTime = formatDate(new Date(Number(this.data.validStartTime)))
          },
          methods: {
            timeChange(e) {
              // const { itemData } = e
              if (e) {
                rowDataTemp[rowDataTemp.length - 1].validStartTime = e
              } else {
                rowDataTemp[rowDataTemp.length - 1].validStartTime = null
              }
            }
          }
        })
      }
    }
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('结束日期'),
    headerTemplate: Component.requiredHeader({
      headerText: i18n.t('结束日期')
    }),
    template: Component.timeDate({
      dataKey: 'validEndTime',
      hasTime: false
    }),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    // editType: 'datepickeredit',
    // format: 'yMd',
    editTemplate: function () {
      return {
        template: Vue.component('companySelect', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <mt-date-picker
                :disabled="!(!data.status || data.status === -2 || data.status === -5)"
                v-model="data.validEndTime"
                :placeholder="$t('选择开始日期')"
                @change="timeChange"
              ></mt-date-picker>
            </div>
          </div>`,

          data() {
            return {
              data: {}
            }
          },
          created() {
            this.data.validEndTime = formatDate(new Date(Number(this.data.validEndTime)))
          },
          methods: {
            timeChange(e) {
              // const { itemData } = e
              if (e) {
                rowDataTemp[rowDataTemp.length - 1].validEndTime = e
              } else {
                rowDataTemp[rowDataTemp.length - 1].validEndTime = null
              }
            }
          }
        })
      }
    }
  },
  {
    field: 'applyReason',
    headerText: i18n.t('申请理由'),
    headerTemplate: Component.requiredHeader({
      headerText: i18n.t('申请理由')
    }),
    editTemplate: function () {
      return {
        template: Vue.component('reasonInput', {
          template: `<div>
          <mt-input
          :disabled="!(!data.status || data.status === -2 || data.status === -5)"
          v-model="data.applyReason"
          @change="reasonChange"
        ></mt-input>
        </div>`,
          methods: {
            reasonChange(e) {
              rowDataTemp[rowDataTemp.length - 1]['applyReason'] = e
            }
          }
        })
      }
    }
  },
  {
    field: 'needPolicy',
    headerText: i18n.t('临时对策是否需要'),
    headerTemplate: Component.requiredHeader({
      headerText: i18n.t('临时对策是否需要')
    }),
    valueConverter: {
      type: 'map',
      fields: { text: 'text', value: 'value' },
      map: [
        { text: i18n.t('是'), value: 1, cssClass: ['btns warning'] },
        { text: i18n.t('否'), value: 0, cssClass: ['btns warning'] }
      ]
    },
    editTemplate: function () {
      return {
        template: Vue.component('statusSelect', {
          template: `<div>
          <mt-select
          :disabled="!(!data.status || data.status === -2 || data.status === -5)"
          v-model="data.needPolicy"
          :data-source="dataSource"
          @change="wayChange"
        ></mt-select>
        </div>`,
          data() {
            return {
              dataSource: [
                { text: i18n.t('是'), value: 1 },
                { text: i18n.t('否'), value: 0 }
              ]
            }
          },
          mounted() {
            this.$nextTick(() => {
              this.$bus.$emit('waySelectChange', this.data.needPolicy)
            })
          },
          methods: {
            wayChange(e) {
              rowDataTemp[rowDataTemp.length - 1]['needPolicy'] = e.value
              if (e.value === 0) {
                rowDataTemp[rowDataTemp.length - 1]['engineerUserName'] = null
              }
              this.$bus.$emit('waySelectChange', e.value)
            }
          }
        })
      }
    }
  },
  {
    field: 'engineerUserName',
    headerText: i18n.t('工艺工程师'),
    editTemplate: Component.engineerSelect
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      fields: { text: 'text', value: 'value' },
      map: [
        { text: i18n.t('草稿'), value: 0, cssClass: ['btns warning'] },
        { text: i18n.t('审批中'), value: 1, cssClass: ['btns warning'] },
        { text: i18n.t('审批通过'), value: 2, cssClass: ['btns warning'] },
        { text: i18n.t('审批驳回'), value: -2, cssClass: ['btns warning'] },
        { text: i18n.t('审批撤回'), value: -5, cssClass: ['btns warning'] },
        { text: i18n.t('审批废弃'), value: -4, cssClass: ['btns warning'] },
        { text: i18n.t('同步成功'), value: 4, cssClass: ['btns warning'] },
        { text: i18n.t('同步失败'), value: 5, cssClass: ['btns warning'] }
      ]
    },
    editTemplate: function () {
      return {
        template: Vue.component('statusSelect', {
          template: `<span>{{ dataSource.filter(i => i.value === data.status)[0] ? dataSource.filter(i => i.value === data.status)[0]['text'] : '' }}</span>`,
          data() {
            return {
              dataSource: [
                { text: i18n.t('草稿'), value: 0 },
                { text: i18n.t('审批中'), value: 1 },
                { text: i18n.t('审批通过'), value: 2 },
                { text: i18n.t('审批驳回'), value: -2 },
                { text: i18n.t('审批撤回'), value: -5 },
                { text: i18n.t('审批废弃'), value: -4 },
                { text: i18n.t('同步成功'), value: 4 },
                { text: i18n.t('同步失败'), value: 5 }
              ]
            }
          }
        })
      }
    },
    allowEditing: false
  },
  {
    field: 'createUserName',
    headerText: i18n.t('申请人'),
    allowEditing: false
  },
  {
    field: 'createTime',
    headerText: i18n.t('申请日期'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    allowEditing: false
  },
  {
    field: 'updateTime',
    headerText: i18n.t('修改时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    allowEditing: false
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('最后更新人'),
    allowEditing: false
  }
]

// list pageConfig
export const pageConfig = [
  {
    toolbar: toolbar,
    gridId: '617278cd-cc59-4aa1-86cb-cd3a66786710',
    activatedRefresh: false,
    grid: {
      editSettings: {
        allowAdding: true,
        allowEditing: true,
        allowDeleting: true,
        mode: 'Normal', // 默认normal模式
        allowEditOnDblClick: true,
        newRowPosition: 'Top'
      },
      height: 'auto',
      columnData,
      asyncConfig: {
        url: '/sourcing/tenant/exemptionApplyInfo/pageQuery',
        params: {},
        serializeList: (list) => {
          return list.map((item, index) => {
            return {
              ...item,
              _index: index + 1
            }
          })
        }
      }
    }
  }
]
