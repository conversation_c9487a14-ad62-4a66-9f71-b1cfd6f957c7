<template>
  <div class="top-info">
    <!-- 顶部信息 -->
    <nav>
      <div class="detail-info">
        <div class="name-wrap"></div>
        <div class="btns-wrap">
          <mt-button class="e-flat">{{ $t('重置') }}</mt-button>
          <mt-button class="e-flat">{{ $t('查询') }}</mt-button>
          <div class="pack-up">
            <mt-button class="e-flat">{{ $t('收起') }}</mt-button>
            <div class="rotate">
              <MtIcon name="MT_DownArrow" />
            </div>
          </div>
        </div>
      </div>
      <div class="formInput">
        <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
          <mt-form-item prop="supplierName" :label="$t('供应商')">
            <mt-select
              v-model="formObject.supplierName"
              float-label-type="Never"
              :data-source="seletArr"
              :placeholder="$t('请选择供应商')"
            ></mt-select>
            <!-- :fields="{ text: 'supplierName', value: 'supplierName' }" -->
          </mt-form-item>
          <mt-form-item prop="companyName" :label="$t('公司')">
            <mt-select
              v-model="formObject.companyName"
              float-label-type="Never"
              :data-source="seletArr"
              :placeholder="$t('请选择公司')"
            ></mt-select>
            <!-- :fields="{ text: 'companyName', value: 'companyName' }" -->
          </mt-form-item>
          <mt-form-item prop="siteName" :label="$t('工厂')">
            <mt-select
              v-model="formObject.siteName"
              float-label-type="Never"
              :data-source="seletArr"
              :placeholder="$t('请选择工厂')"
            ></mt-select>
            <!-- :fields="{ text: 'siteName', value: 'siteName' }" -->
          </mt-form-item>
          <mt-form-item prop="itemName" :label="$t('物料')">
            <mt-select
              v-model="formObject.itemName"
              float-label-type="Never"
              :data-source="seletArr"
              :placeholder="$t('请选择物料')"
            ></mt-select>
            <!-- :fields="{ text: 'itemName', value: 'itemName' }" -->
          </mt-form-item>
          <mt-form-item prop="date" :label="$t('日期')">
            <mt-date-picker
              v-model="formObject.date"
              start="Year"
              depth="Year"
              format=" yyyy-MM"
              :placeholder="$t('选择日期')"
            ></mt-date-picker>
            <!-- :fields="{ text: 'date', value: 'date' }" -->
          </mt-form-item>
          <mt-form-item prop="state" :label="$t('状态')">
            <mt-select
              v-model="formObject.state"
              :data-source="seletArr"
              float-label-type="Never"
              :placeholder="$t('请输入状态')"
            ></mt-select>
          </mt-form-item>
        </mt-form>
      </div>
    </nav>
  </div>
</template>
<script>
export default {
  data() {
    return {
      formObject: {
        itemName: '', //物料
        companyName: '', //公司
        supplierName: '', //供应商
        siteName: '', //工厂
        state: '' //状态
      },
      seletArr: [],
      formRules: [] //必填
    }
  },
  mounted() {},
  methods: {}
}
</script>
<style lang="scss" scoped>
.top-info {
  width: 100%;
  background-color: #fff;
  nav {
    //按钮
    .detail-info {
      padding: 20px 20px 3px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .name-wrap {
      }
      .btns-wrap {
        /deep/ .mt-button {
          margin-right: 0;
          button {
            background: transparent;
            //border: 1px solid rgba(0, 70, 156, 0.1);
            border-radius: 4px;
            box-shadow: unset;
            padding: 6px 12px 4px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: 500;
            color: rgba(0, 70, 156, 1);
          }
        }
        .pack-up {
          display: inline-block;
          position: relative;
          .rotate {
            position: absolute;
            right: -5px;
            top: 5px;
            transform: rotate(180deg);
            .mt-icons {
              color: #c8d5e9;
            }
          }
        }
      }
    }
    //表单
    .formInput {
      width: 100%;
      .mt-form {
        width: 100%;
        padding: 20px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        .mt-form-item {
          width: 360px;
        }
      }
    }
  }
}
</style>
