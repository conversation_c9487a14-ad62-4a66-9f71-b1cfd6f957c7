<template>
  <div class="inventory">
    <div class="inventory-details">
      <!-- 头部 -->
      <top-info></top-info>
      <mt-tabs
        ref="tabs"
        tab-id="tabs"
        :e-tab="false"
        :data-source="tabSource"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>
      <!-- 待盘点明细 -->
      <inventory-list
        v-if="tabsIndex == 0"
        :inventory-details-arr="inventoryDetailsArr"
        @InventoryDetails="InventoryDetails"
      ></inventory-list>
      <!-- 相关附件 -->
      <related-accessory v-if="tabsIndex == 1"></related-accessory>
    </div>
  </div>
</template>
<script>
import topInfo from './components/topInfo.vue'
import inventoryList from './inventoryList/index.vue'
import relatedAccessory from './relatedAccessory/index.vue'
export default {
  components: {
    topInfo,
    inventoryList,
    relatedAccessory
  },
  data() {
    return {
      tabSource: [{ title: this.$t('待盘点明细') }, { title: this.$t('相关附件') }],
      tabsIndex: 0,
      inventoryDetailsArr: [] //盘点明细数组
    }
  },
  computed: {
    id() {
      return this.$route.query.id
    }
  },
  mounted() {
    if (this.id) {
      this.initialCallEditor()
    } else {
      this.initialCallInterface()
    }
  },
  methods: {
    //初始调用
    initialCallInterface() {},
    //初始调用--编辑
    initialCallEditor() {
      // docId=1513410622596796417&parentId=0&docType=po
      let parameter = {
        docId: this.id,
        parentId: 0,
        docType: 'pd'
      }
      //查询相关附件
      this.$API.Inventory.queryFileByDocId(parameter).then((res) => {
        console.log(res)
      })
    },
    InventoryDetails(e) {
      console.log(e, '新增值')
      this.inventoryDetailsArr = e
    },
    // tabs切换
    handleSelectTab(e, item) {
      this.tabsIndex = e
      console.log(item)
      // if (item.title == "相关附件") {
      // }
      // if (item.title == "待盘点明细") {
      // }
    }
  }
}
</script>
<style lang="scss" scoped>
.inventory {
  height: 100%;
  .inventory-details {
    height: 100%;
    display: flex;
    flex-direction: column;
    .top-info {
      flex-shrink: 0;
    }
    .difference-details {
      height: 100%;
      flex: 1;
    }
  }
}
</style>
