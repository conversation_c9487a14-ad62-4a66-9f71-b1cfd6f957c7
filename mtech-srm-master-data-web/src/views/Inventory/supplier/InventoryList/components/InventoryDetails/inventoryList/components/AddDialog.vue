<template>
  <!-- @beforeClose="cancel" -->
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form class="dialog-mt-form" ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="itemCode" :label="$t('物料编码')">
          <mt-input
            v-model="formObject.itemCode"
            float-label-type="Never"
            :placeholder="$t('请输入物料编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('物料名称')">
          <mt-input
            v-model="formObject.itemName"
            float-label-type="Never"
            :placeholder="$t('请输入物料名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="inventoryQuantity" :label="$t('盘点数量')">
          <mt-input
            v-model="formObject.inventoryQuantity"
            float-label-type="Never"
            :placeholder="$t('请输入物料名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="endProductStatus" :label="$t('是否成品')">
          <mt-select
            v-model="formObject.endProductStatus"
            float-label-type="Never"
            :data-source="endProductStatusData"
            @change="changeTaxItem"
            :placeholder="$t('请选择是否成品')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="unit" :label="$t('单位')">
          <mt-input
            v-model="formObject.unit"
            float-label-type="Never"
            :placeholder="$t('请输入单位')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')">
          <mt-input
            v-model="formObject.remark"
            float-label-type="Never"
            :placeholder="$t('请输入备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formObject: {
        itemCode: '', // 物料编码
        itemName: '', // 物料名称
        inventoryQuantity: '', //盘点数量
        endProductStatus: '', //是否成品
        unit: '', //单位
        remark: '' //备注
      },
      //必填项
      formRules: {
        endProductStatus: [
          {
            required: true,
            message: this.$t('是否成品'),
            trigger: 'blur'
          }
        ],
        itemCode: [
          {
            required: true,
            message: this.$t('物料编码'),
            trigger: 'blur'
          }
        ],
        itemName: [
          {
            required: true,
            message: this.$t('物料名称'),
            trigger: 'blur'
          }
        ],
        inventoryQuantity: [
          {
            required: true,
            message: this.$t('盘点数量'),
            trigger: 'blur'
          }
        ],
        unit: [
          {
            required: true,
            message: this.$t('单位'),
            trigger: 'blur'
          }
        ],
        remark: [
          {
            required: true,
            message: this.$t('备注'),
            trigger: 'blur'
          }
        ]
      },
      endProductStatusData: [
        { text: this.$t('是'), value: 0 },
        { text: this.$t('否'), value: 1 }
      ]
      // company: false,
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
    // inventoryDetails() {
    //   return this.modalData.inventoryDetails;
    // },
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    // if (this.modalData && this.modalData.data) {
    //   this.company = true;
    //   this.formObject = { ...this.modalData.data };
    // }
  },
  methods: {
    changeTaxItem(e) {
      this.formObject.endProductStatus = e.itemData.endProductStatus
    },
    //点击确认
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = { ...this.formObject }
          if (!this.company) {
            delete params.id
          }
          console.log(params)
          this.$emit('confirm-function', params)
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    padding: 0;
    .dialog-content {
      padding: 40px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 400px;
        }
      }
    }
  }
}
</style>
