<template>
  <div class="top-info">
    <!-- 顶部信息 -->
    <nav>
      <div class="detail-info">
        <div class="name-wrap">
          <span class="code">{{
            (quotedPriceData && quotedPriceData.rfxCode) || $t('盘点单号')
          }}</span>
          <span class="tags tags-0">{{ $t('草稿') }}</span>
          <span class="tags tags-1">{{ $t('进行中') }}</span>
          <span class="tags tags-2">{{ $t('XXX') }}</span>
          <span class="founder">{{ $t('创建人:') }} </span>
          <span class="date-created">{{ $t('创建日期:') }} </span>
        </div>
        <div class="btns-wrap">
          <mt-button class="e-flat" @click="$router.push('/masterdata/supplier-inventory-list')">{{
            $t('返回')
          }}</mt-button>
          <mt-button class="e-flat">{{ $t('保存') }}</mt-button>
          <div class="pack-up" @click="packupClick">
            <mt-button class="e-flat">{{ $t('提交') }}</mt-button>
            <div class="rotate">
              <MtIcon name="MT_DownArrow" />
            </div>
          </div>
        </div>
      </div>
      <div class="formInput">
        <mt-form>
          <mt-form-item prop="company" :label="$t('公司')">
            <mt-select
              v-model="formObject.company"
              float-label-type="Never"
              :data-source="companySelect"
              :fields="{ text: 'orgName', value: 'id' }"
              @change="companySelectChange"
              :placeholder="$t('请选择公司')"
            ></mt-select>
            <!-- :fields="{ text: 'orgName', value: 'id' }" -->
          </mt-form-item>
          <mt-form-item prop="factory" :label="$t('工厂')">
            <mt-select
              v-model="formObject.factory"
              float-label-type="Never"
              :data-source="factorySelect"
              :fields="{ text: 'siteName', value: 'id' }"
              :placeholder="$t('请选择工厂')"
            ></mt-select>
            <!-- :fields="{ text: 'factory', value: 'factory' }" -->
          </mt-form-item>
          <mt-form-item prop="date" :label="$t('日期')">
            <mt-date-picker
              v-model="formObject.date"
              start="Year"
              depth="Year"
              format=" yyyy-MM"
              :placeholder="$t('选择日期')"
            ></mt-date-picker>
            <!-- :fields="{ text: 'date', value: 'date' }" -->
          </mt-form-item>
          <mt-form-item prop="state" :label="$t('备注')">
            <mt-input
              v-model="formObject.state"
              float-label-type="Never"
              :placeholder="$t('请输入备注')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </nav>
  </div>
</template>
<script>
export default {
  data() {
    return {
      formObject: {
        company: '', //公司
        factory: '', //工厂
        // years: "", //年
        // month: "", //月份
        date: '', //日期
        state: '' //状态
      },
      companySelect: [], //公司下拉数据
      factorySelect: [] //工厂下拉数据
    }
  },
  mounted() {
    this.initialCallInterface()
  },
  methods: {
    //初始调用
    initialCallInterface() {
      let companyData = {
        organizationLevelCodes: ['ORG02', 'ORG01'],
        orgType: 'ORG001PRO',
        includeItself: true,
        organizationIds: []
      }
      this.$API.Inventory.findSpecifiedChildrenLevelOrgs(companyData).then((res) => {
        // console.log(res,"公司下拉框数据");
        this.companySelect = res.data
      })
    },
    //change 公司
    companySelectChange(e) {
      // console.log(e.itemData.id);
      let parameter = { organizationId: e.itemData.id }
      this.$API.Inventory.getSiteInfo(parameter).then((res) => {
        console.log(res)
        this.factorySelect = res.data
      })
    },
    packupClick() {
      console.log('收起')
    }
  }
}
</script>
<style lang="scss" scoped>
.top-info {
  width: 100%;
  background-color: #fff;
  nav {
    //按钮
    .detail-info {
      padding: 20px 20px 3px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .name-wrap {
        span {
          margin-left: 30px;
        }
        span:nth-of-type(1) {
          margin-left: 0;
        }
        // 盘点单号
        .code {
          font-size: 20px;
          font-family: DINAlternate;
          font-weight: bold;
          color: rgba(41, 41, 41, 1);
        }
        //状态
        .tags {
          font-size: 12px;
          font-family: PingFangSC;
          font-weight: 500;
          padding: 4px;
          border-radius: 2px;
        }
        .tags-0 {
          color: rgb(155, 170, 193);
          background: rgba(155, 170, 193, 0.1);
        }
        .tags-1 {
          color: rgba(237, 161, 51, 1);
          background: rgba(237, 161, 51, 0.1);
        }
        .tags-2 {
          color: rgb(237, 86, 51);
          background: rgba(237, 86, 51, 0.1);
        }
        //创建人
        .founder {
        }
        //创建日期
        .date-created {
        }
      }
      .btns-wrap {
        /deep/ .mt-button {
          margin-right: 0;
          button {
            background: transparent;
            //border: 1px solid rgba(0, 70, 156, 0.1);
            border-radius: 4px;
            box-shadow: unset;
            padding: 6px 12px 4px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: 500;
            color: rgba(0, 70, 156, 1);
          }
        }
        .pack-up {
          display: inline-block;
          position: relative;
          .rotate {
            position: absolute;
            right: -5px;
            top: 5px;
            transform: rotate(180deg);
            .mt-icons {
              color: #c8d5e9;
            }
          }
        }
      }
    }
    //表单
    .formInput {
      width: 100%;
      padding: 0 20px 0;
      box-sizing: border-box;
      .mt-form {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        .mt-form-item {
          width: 360px;
        }
        .mt-form-item:nth-of-type(1),
        .mt-form-item:nth-of-type(4) {
          margin-left: 0;
        }
        .mt-form-item:nth-of-type(4) {
          width: 100%;
        }
      }
    }
  }
}
</style>
