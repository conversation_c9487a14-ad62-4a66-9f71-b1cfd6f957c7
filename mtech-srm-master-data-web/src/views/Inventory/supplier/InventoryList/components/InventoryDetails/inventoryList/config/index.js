import { i18n } from '@/main.js'
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'delete', icon: 'icon_solid_Closeorder', title: i18n.t('删除') }
]
const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
    // width: "350",
    // cellTools: ["edit", "delete", "preview"],
  },
  {
    field: 'itemName',
    // width: "200",
    headerText: i18n.t('物料名称')
  },
  {
    field: 'inventoryQuantity',
    // width: "200",
    headerText: i18n.t('盘点数量')
  },
  {
    field: 'endProductStatus',
    // width: "200",
    headerText: i18n.t('是否成品'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('是'), 1: i18n.t('否') }
    }
  },
  {
    field: 'unit',
    // width: "200",
    headerText: i18n.t('单位')
  },
  {
    field: 'remark',
    // width: "200",
    headerText: i18n.t('备注')
  }
]
export const pageConfig = () => [
  {
    toolbar: toolbar,
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      columnData,
      dataSource: []
    }
  }
]
