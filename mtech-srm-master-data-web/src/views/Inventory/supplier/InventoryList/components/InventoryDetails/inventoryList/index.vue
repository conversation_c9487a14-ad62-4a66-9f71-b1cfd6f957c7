<!--盘点单列表-->
<template>
  <div class="full-height">
    <mt-template-page
      ref="tepPage"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>
    <!--
      :hidden-tabs="true"
         @handleChangeItemIdentification="handleChangeItemIdentification"
      @handleClickToolBar="handleClickToolBar"
     -->
  </div>
</template>
<script>
import { pageConfig } from './config/index'
import { utils } from '@mtech-common/utils'
export default {
  props: {
    inventoryDetailsArr: {
      type: Array,
      required: true
    }
  },

  data() {
    return {
      pageConfig: pageConfig(),
      inventoryDetails: [] //盘点明细
    }
  },
  watch: {
    inventoryDetailsArr() {
      this.watchInventoryDetails()
    }
  },
  mounted() {
    // console.log(this.inventoryDetailsArr, "this.inventoryDetailsArr");
    this.inventoryDetails = this.inventoryDetailsArr
    this.$set(this.pageConfig[0].grid, 'dataSource', this.inventoryDetails)
  },
  methods: {
    handleClickToolBar(e) {
      const _selectRows = e.grid.getSelectedRecords()
      if (_selectRows.length <= 0 && e.toolbar.id == 'delete') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        this.handleClickToolBarAdd()
      }
      if (e.toolbar.id == 'delete') {
        this.handleClickToolBarDelete(_selectRows)
      }
    },
    //头部--新增
    handleClickToolBarAdd() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/Inventory/supplier/InventoryList/components/AddDialog" */ './components/AddDialog.vue'
          ),
        data: {
          title: this.$t('新增'),
          inventoryDetails: this.inventoryDetails
        },
        success: (data) => {
          let _dataSource = utils.cloneDeep(this.pageConfig[0].grid['dataSource'])
          _dataSource.push(data)
          _dataSource.forEach((item, index) => {
            item.lineIndex = index
          })
          this.$set(this.pageConfig[0].grid, 'dataSource', _dataSource)
          this.$emit('InventoryDetails', _dataSource)
        }
      })
    },
    //头部--删除
    handleClickToolBarDelete(_selectRows) {
      console.log(_selectRows)
    },
    //监听数据发生改变
    watchInventoryDetails() {
      // console.log(this.inventoryDetailsArr, "inventoryDetailsArr");
      this.inventoryDetails = this.inventoryDetailsArr
      this.$set(this.pageConfig[0].grid, 'dataSource', this.inventoryDetails)
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  flex: 1;
}
</style>
