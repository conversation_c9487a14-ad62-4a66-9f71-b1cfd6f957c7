import { i18n } from '@/main.js'
const toolbar = [
  // { id: "confirm", icon: "icon_solid_Createorder", title: "确认" },
  // { id: "refuse", icon: "icon_solid_Closeorder", title: "拒绝" },
]
const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'fileName',
    headerText: i18n.t('文件名称')
  },
  {
    field: 'fileSize',
    // width: "200",
    headerText: i18n.t('文件大小')
  },
  {
    field: 'fileType',
    // width: "200",
    headerText: i18n.t('文件类型')
  },
  {
    field: 'createUserName',
    // width: "200",
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    // width: "200",
    headerText: i18n.t('创建时间')
  }
]
export const pageConfig = () => [
  {
    toolbar: toolbar,
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      columnData
    }
  }
]
