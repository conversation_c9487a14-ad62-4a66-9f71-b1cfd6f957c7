import { i18n } from '@/main.js'
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('创建盘点单') },
  { id: 'submit', icon: 'icon_solid_Submit', title: i18n.t('提交') },
  { id: 'delete', icon: 'icon_solid_Delete', title: i18n.t('删除') }
  // { id: "submit", icon: "icon_solid_submit", title: "对比差异" },
]
const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    // width: "350",
    // cellTools: ["edit", "delete", "preview"],
    valueConverter: {
      type: 'map',
      map: [
        { status: 0, label: i18n.t('草稿'), cssClass: 'title-#9baac1' },
        { status: 1, label: i18n.t('已提交'), cssClass: 'title-#6386c1' },
        { status: 2, label: i18n.t('变更提交'), cssClass: 'title-#9baac1' },
        { status: 3, label: i18n.t('已对比'), cssClass: 'title-#6386c1' }
      ],
      fields: { text: 'label', value: 'status' }
    },
    cellTools: [
      {
        id: 'submit',
        // icon: "icon_solid_Createorder",
        title: i18n.t('提交'),
        visibleCondition: (data) => {
          return data['status'] === 0
        }
      },
      {
        id: 'editor',
        // icon: "icon_solid_Cancel",
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data['status'] == 0 || data['status'] == 3
        }
      },
      {
        id: 'delete',
        // icon: "icon_solid_Cancel",
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data['status'] == 0
        }
      }
    ]
  },
  {
    field: 'inventoryCode',
    // width: "200",
    headerText: i18n.t('盘点编码')
  },
  {
    field: 'companyName',
    // width: "200",
    headerText: i18n.t('公司')
  },
  {
    field: 'siteName',
    // width: "200",
    headerText: i18n.t('工厂')
  },
  {
    field: 'year',
    // width: "200",
    headerText: i18n.t('年份')
  },
  {
    field: 'month',
    // width: "200",
    headerText: i18n.t('月份')
  },
  {
    field: 'createTime',
    // width: "200",
    headerText: i18n.t('创建时间')
  },
  {
    field: 'contrastTime',
    // width: "200",
    headerText: i18n.t('对比时间')
  },
  {
    field: 'remark',
    // width: "200",
    headerText: i18n.t('备注')
  }
]
export const pageConfig = [
  {
    toolbar,
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      columnData,
      asyncConfig: {
        url: '/masterDataManagement/tenant/inventory/querySupHeader',
        params: {}
      }
    }
  }
]
