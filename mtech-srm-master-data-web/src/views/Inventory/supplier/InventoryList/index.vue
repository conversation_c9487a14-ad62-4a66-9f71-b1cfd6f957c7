<!--盘点单列表-->
<template>
  <div class="full-height">
    <mt-template-page
      ref="tepPage"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
    <!--
      :hidden-tabs="true"
         @handleChangeItemIdentification="handleChangeItemIdentification"
      @handleClickToolBar="handleClickToolBar"
     -->
  </div>
</template>
<script>
import { pageConfig } from './config/index'
import { inventoryList } from './config/modus'
export default {
  data() {
    return {
      pageConfig: pageConfig,
      inventoryList
    }
  },
  mounted() {
    this.initialCallInterface()
  },
  methods: {
    initialCallInterface() {
      let res = inventoryList
      console.log(res, 'inventoryList盘点单列表数据')
      this.$set(this.pageConfig[0].grid, 'dataSource', res.data.records)
    },

    //表头点击
    handleClickToolBar(e) {
      const _selectRows = e.grid.getSelectedRecords()
      if (_selectRows.length <= 0 && (e.toolbar.id == 'submit' || e.toolbar.id == 'delete')) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        //新增
        this.handleClickToolBarAdd()
      }
      if (e.toolbar.id == 'submit') {
        //提交
        this.handleClickToolBarSubmit(_selectRows)
      }
      if (e.toolbar.id == 'delete') {
        //删除
        this.handleClickToolBarDelete(_selectRows)
      }
    },
    //行内点击
    handleClickCellTool(e) {
      const { data, tool } = e
      if (tool.id == 'submit') {
        //提交
        this.handleClickCellToolSubmit(data)
      } else if (tool.id == 'editor') {
        //编辑
        this.handleClickCellToolEditor(data)
      } else if (tool.id == 'delete') {
        //删除
        this.handleClickCellToolDelete(data)
      }
    },
    //头部--创建盘点单
    handleClickToolBarAdd() {
      this.$router.push({
        path: `supplier-inventory-list-create`
      })
    },
    //头部--提交
    handleClickToolBarSubmit() {
      console.log('提交')
    },
    //头部--删除
    handleClickToolBarDelete() {
      console.log('删除')
    },
    //行内--提交
    handleClickCellToolSubmit(data) {
      let _selectRows = [data]
      this.handleClickToolBarSubmit(_selectRows)
    },
    //行内--编辑
    handleClickCellToolEditor(data) {
      this.$router.push({
        path: `supplier-inventory-list-create`,
        query: {
          id: data.id ? data.id : ''
        }
      })
    },
    //行内--删除
    handleClickCellToolDelete(data) {
      let _selectRows = [data]
      this.handleClickToolBarDelete(_selectRows)
    }
  }
}
</script>
<style lang="scss" scoped></style>
