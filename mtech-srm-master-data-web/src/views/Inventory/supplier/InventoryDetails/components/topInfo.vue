<template>
  <div class="top-info">
    <!-- 顶部信息 -->
    <nav>
      <div class="detail-info">
        <div class="name-wrap"></div>
        <div class="btns-wrap">
          <mt-button class="e-flat">{{ $t('重置') }}</mt-button>
          <mt-button class="e-flat">{{ $t('查询') }}</mt-button>
          <div class="pack-up">
            <mt-button class="e-flat">{{ $t('收起') }}</mt-button>
            <div class="rotate">
              <MtIcon name="MT_DownArrow" />
            </div>
          </div>
        </div>
      </div>
      <div class="formInput">
        <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
          <mt-form-item prop="clientCompany" :label="$t('客户公司')">
            <mt-select
              v-model="formObject.clientCompany"
              float-label-type="Never"
              :data-source="seletArr"
              :placeholder="$t('请选择客户公司')"
            ></mt-select>
            <!-- :fields="{ text: 'clientCompany', value: 'clientCompany' }" -->
          </mt-form-item>
          <mt-form-item prop="factory" :label="$t('工厂')">
            <mt-select
              v-model="formObject.factory"
              float-label-type="Never"
              :data-source="seletArr"
              :placeholder="$t('请选择工厂')"
            ></mt-select>
            <!-- :fields="{ text: 'factory', value: 'factory' }" -->
          </mt-form-item>
          <mt-form-item prop="itemName" :label="$t('物料')">
            <mt-select
              v-model="formObject.itemName"
              float-label-type="Never"
              :data-source="seletArr"
              :placeholder="$t('请选择物料')"
            ></mt-select>
            <!-- :fields="{ text: 'itemName', value: 'itemName' }" -->
          </mt-form-item>
          <mt-form-item prop="state" :label="$t('状态')">
            <mt-input
              v-model="formObject.state"
              float-label-type="Never"
              :placeholder="$t('请输入状态')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="date" :label="$t('日期')">
            <mt-date-picker
              v-model="formObject.date"
              start="Year"
              depth="Year"
              format=" yyyy-MM"
              :placeholder="$t('选择日期')"
            ></mt-date-picker>
            <!-- :fields="{ text: 'date', value: 'date' }" -->
          </mt-form-item>
        </mt-form>
      </div>
    </nav>
  </div>
</template>
<script>
export default {
  data() {
    return {
      formObject: {
        clientCompany: '', //客户公司
        factory: '', //工厂
        itemName: '', //物料
        state: '', //状态
        date: '' //日期
      }
    }
  },
  mounted() {},
  methods: {}
}
</script>
<style lang="scss" scoped>
.top-info {
  width: 100%;
  min-width: 1264px;
  background-color: #fff;
  nav {
    //按钮
    .detail-info {
      padding: 20px 20px 3px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .name-wrap {
      }
      .btns-wrap {
        /deep/ .mt-button {
          margin-right: 0;
          button {
            background: transparent;
            //border: 1px solid rgba(0, 70, 156, 0.1);
            border-radius: 4px;
            box-shadow: unset;
            padding: 6px 12px 4px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: 500;
            color: rgba(0, 70, 156, 1);
          }
        }
        .pack-up {
          display: inline-block;
          position: relative;
          .rotate {
            position: absolute;
            right: -5px;
            top: 5px;
            transform: rotate(180deg);
            .mt-icons {
              color: #c8d5e9;
            }
          }
        }
      }
    }
    //表单
    .formInput {
      width: 100%;
      padding: 0 20px 0;
      box-sizing: border-box;
      .mt-form {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: start;
        flex-wrap: wrap;
        .mt-form-item {
          width: 360px;
          margin-left: 70px;
        }
        .mt-form-item:nth-last-child(2) {
          margin-left: 0px;
        }
        .mt-form-item:nth-of-type(1) {
          margin-left: 0px;
        }
      }
    }
  }
}
</style>
