import { i18n } from '@/main.js'
import Vue from 'vue'
import { getComponent } from '@syncfusion/ej2-base'
// import { DropDownList } from "@syncfusion/ej2-vue-dropdowns";
import { Query } from '@syncfusion/ej2-data'
export const selectArr = [
  { text: i18n.t('启用'), value: '1' },
  { text: i18n.t('关闭'), value: '0' }
]
export const planDataSource = [
  {
    header: {
      text: i18n.t('默认数据源')
    }
  }
]
export const dataSource = [
  {
    title: i18n.t('基础数据'),
    key: 'BasicNews',
    content: '0'
  },
  {
    title: i18n.t('计划'),
    key: 'plan',
    content: '1'
  },
  {
    title: i18n.t('采购'),
    key: 'purchase',
    content: '2'
  },
  {
    title: i18n.t('存储'),
    key: 'store',
    content: '3'
  },
  {
    title: i18n.t('质量'),
    key: 'quality',
    content: '4'
  }
]
export const dataSources = [
  {
    text: i18n.t('基本信息')
  },
  {
    text: i18n.t('特性')
  },
  {
    text: i18n.t('量纲')
  },
  {
    text: i18n.t('计量单位')
  },
  {
    text: i18n.t('品类')
  },
  {
    text: i18n.t('舍入')
  },
  {
    text: i18n.t('研发')
  },
  {
    text: i18n.t('运输')
  },
  {
    text: i18n.t('行业属性')
  }
]
export const COLUMNS_SP_ATTRS = function (data = []) {
  return [
    {
      width: '50',
      type: 'checkbox'
    },
    {
      headerText: i18n.t('属性编码'),
      field: 'itemClassificationCode'
    },
    {
      headerText: i18n.t('属性名称'),
      field: 'itemClassificationName'
    },
    {
      headerText: i18n.t('属性类别编码'),
      field: 'itemClassificationTypeCode',
      visible: false
    },
    {
      headerText: i18n.t('属性类别'),
      field: 'itemClassificationTypeName',
      editType: 'dropdownedit',
      edit: {
        // create: () => {
        //   elem = document.createElement("input");
        //   return elem;
        // },
        // read: () => {
        //   return typeDropdown.text;
        // },
        // destroy: () => {
        //   typeDropdown.destroy();
        // },
        // write: () => {
        //   typeDropdown = new DropDownList({
        //     dataSource: data,
        //     fields: { value: "dictCode", text: "dictName" },
        //     change: () => {},
        //     placeholder: "Select a country",
        //     floatLabelType: "Never",
        //   });
        //   typeDropdown.appendTo(elem);
        // },
        params: {
          allowFiltering: true,
          dataSource: data,
          fields: { text: 'name', value: 'name' },
          query: new Query(),
          change: (e) => {
            const { itemData } = e
            const grid = new getComponent('dataGrid1', 'grid')
            const rowIndex = grid.editModule.editModule.cellDetails.rowIndex
            grid.updateCell(rowIndex, 'itemClassificationTypeCode', itemData.itemCode)
          }
        }
      }
    },
    {
      headerText: i18n.t('属性值'),
      field: 'itemClassificationValueNames',
      headerTemplate: function () {
        return {
          template: Vue.component('datetemplate', {
            template: `<div style="color: #292929; font-weight: 500; font-size: 14px;">
              {{ headerText }}
              <mt-tool-tip content="属性值之间使用“ ；”相隔" target="#tip">
                <mt-icon id="tip" name="icon_solid_Information" class="mt-ml-10" style="color: #9BAAC1;" />
              </mt-tool-tip>
            </div>`,
            data: function () {
              return { headerText: i18n.t('属性值') }
            }
          })
        }
      }
    },
    {
      headerText: i18n.t('属性描述'),
      field: 'itemClassificationDescription'
    }
  ]
}
export const pageConfigs = function (data = []) {
  return [
    {
      width: '50',
      type: 'checkbox'
    },
    {
      field: 'unitCode',
      headerText: i18n.t('单位代码'),
      // visible: false,
      allowEditing: false
      // validationRules:{
      //   disabled:true
      // }
      // width: "350",
    },
    {
      headerText: i18n.t('类型id'),
      field: 'unitTypeId',
      visible: false
    },
    {
      headerText: i18n.t('单位id'),
      field: 'unitId',
      visible: false
    },
    {
      field: 'unitName',
      // width: "200",
      headerText: i18n.t('单位名称'),
      editType: 'dropdownedit',
      edit: {
        params: {
          allowFiltering: true,
          dataSource: data,
          fields: { text: 'unitName', value: 'unitName' },
          query: new Query(),
          change: (e) => {
            const { itemData } = e
            console.log(itemData)
            const grid = new getComponent('danwei1', 'grid')
            const rowIndex = grid.editModule.editModule.cellDetails.rowIndex
            grid.updateCell(rowIndex, 'unitId', itemData.id)
            grid.updateCell(rowIndex, 'unitTypeId', itemData.typeId)
            grid.updateCell(rowIndex, 'unitCode', itemData.unitCode)
            // console.log(grid)
          }
        }
      }
    },
    {
      headerText: i18n.t('是否固定比例换算'),
      field: 'fixRatio',
      visible: false
    },
    {
      field: 'fixRationame',
      // width: "200",
      headerText: i18n.t('是否固定比例换算'),
      editType: 'dropdownedit',
      edit: {
        params: {
          allowFiltering: true,
          dataSource: [
            {
              name: i18n.t('是'),
              value: 1
            },
            {
              name: i18n.t('否'),
              value: 0
            }
          ],
          fields: { text: 'name', value: 'name' },
          query: new Query(),
          change: (e) => {
            const { itemData } = e
            console.log(itemData)
            const grid = new getComponent('danwei1', 'grid')
            const rowIndex = grid.editModule.editModule.cellDetails.rowIndex
            grid.updateCell(rowIndex, 'fixRatio', itemData.value)
          }
        }
      }
    },
    {
      field: 'calculateRatio',
      // width: "200",
      headerText: '单位换算(其他单位：基本单位)'
    }
  ]
}
export const classConfigs = function () {
  return [
    {
      width: '50',
      type: 'checkbox'
    },
    {
      field: 'categoryOrgName',
      // width: "200",
      headerText: i18n.t('组织名称')
    },
    {
      field: 'categoryTypeCode',
      headerText: i18n.t('品类类别编号')
      // width: "350",
    },
    {
      field: 'categoryTypeName',
      // width: "200",
      headerText: i18n.t('品类类别名称')
    },
    // {
    //   field: "itemClassificationTypeName",
    //   // width: "200",
    //   headerText: i18n.t("应用领域"),
    // },
    {
      field: 'categoryCode',
      // width: "200",
      headerText: i18n.t('品类编号')
    },
    {
      field: 'categoryName',
      // width: "200",
      headerText: i18n.t('品类名称')
    }
  ]
}
