import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2022-01-19 13:56:28
 * @LastEditTime: 2022-01-19 17:33:56
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\matterItemManagement\data\storage.config.js
 */
export const OPTIONS = {
  UNIT_TIME: [],
  TYPE: [],
  UNIT_TYPE: [],
  UNIT1: [],
  UNIT2: [],
  YES_OR_NO: [
    {
      label: i18n.t('是'),
      value: 1
    },
    {
      label: i18n.t('否'),
      value: 0
    }
  ],
  DELIVERY_TYPE: [],
  YES_OR_NO_LABEL: [
    {
      label: i18n.t('是'),
      value: i18n.t('是')
    },
    {
      label: i18n.t('否'),
      value: i18n.t('否')
    }
  ],
  A_B_C: [
    {
      label: 'A',
      value: 'A'
    },
    {
      label: 'B',
      value: 'B'
    },
    {
      label: 'C',
      value: 'C'
    }
  ],
  IS_ALLOWED: [
    {
      label: i18n.t('允许'),
      value: 1
    },
    {
      label: i18n.t('不允许'),
      value: 0
    }
  ]
}
