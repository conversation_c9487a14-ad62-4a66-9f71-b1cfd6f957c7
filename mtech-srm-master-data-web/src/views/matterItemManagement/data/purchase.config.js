import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2022-01-19 17:22:05
 * @LastEditTime: 2022-01-19 18:33:23
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\matterItemManagement\data\purchase.config.js
 */
export const OPTIONS = {
  UNIT_TIME: [],
  UNIT: [],
  IS_ALLOWED: [
    {
      label: i18n.t('允许'),
      value: 1
    },
    {
      label: i18n.t('不允许'),
      value: 0
    }
  ],
  TAX: [
    {
      label: i18n.t('增值税'),
      value: '1'
    },
    {
      label: i18n.t('普通税'),
      value: '0'
    }
  ],
  IS_SALES: [
    {
      label: i18n.t('寄售'),
      value: 1
    },
    {
      label: i18n.t('不寄售'),
      value: 0
    }
  ]
}
