import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-10-28 14:11:58
 * @LastEditTime: 2021-12-29 20:09:38
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\matterItemManagement\data\index.js
 */
export const config = [
  {
    useToolTemplate: false,
    toolbar: {
      tools: [
        [
          // 'Add',
          // 'Delete',
          // {
          //   id: "import",
          //   icon: "icon_solid_Import",
          //   title: i18n.t("导入"),
          //   visibleCondition: (data) => {
          //     console.log("import>>>>>", data);
          //     return false;
          //   },
          // },
          // {
          //   id: "export",
          //   icon: "icon_solid_pushorder",
          //   title: i18n.t("导出"),
          //   visibleCondition: (data) => {
          //     console.log("export>>>>>", data);
          //     return false;
          //   },
          // }
        ],
        ['Filter', 'Refresh', 'Setting']
      ]
    },
    // toolbar: [
    //   ["Add", "Delete"],
    //   ["Filter", "Refresh", "Setting"],
    // ],
    grid: {
      columnData: [
        {
          width: '50',
          type: 'checkbox',
          showInColumnChooser: false
        },
        {
          field: 'itemCode',
          cssClass: 'lick',
          headerText: i18n.t('物料/品项编号')
          // cellTools: ['edit', 'delete'],
        },
        {
          field: 'itemName',
          headerText: i18n.t('物料/品项名称')
        },
        {
          field: 'itemDescription',
          headerText: i18n.t('规格型号')
        },
        {
          field: 'oldItemCode',
          headerText: i18n.t('旧物料编号')
        },
        {
          width: '180',
          field: 'baseMeasureUnitName',
          headerText: i18n.t('基本计量单位类型')
        },
        {
          field: 'itemGroupCode',
          headerText: i18n.t('品项组')
        },
        {
          field: 'screenDpi',
          headerText: i18n.t('屏幕分辨率（分辨率）')
        },
        {
          field: 'screenSize',
          headerText: i18n.t('屏幕尺寸（屏幕可视对角线尺寸）')
        },
        {
          field: 'structNo',
          headerText: i18n.t('结构号（图号）')
        },
        {
          field: 'laboratoryCode',
          headerText: i18n.t('实验室/设计室')
        },
        {
          field: 'supplierMaterialCode',
          headerText: i18n.t('供应商物料编码')
        },
        {
          field: 'extMaterialGroupCode',
          headerText: i18n.t('外部物料组编码')
        },
        {
          field: 'supplierCode',
          headerText: i18n.t('供应商编码')
        },
        {
          field: 'ocSupplierCode',
          headerText: i18n.t('OC供应商编码')
        },
        {
          field: 'origSupplierCode',
          headerText: i18n.t('原供应商编码')
        },
        {
          field: 'statusId',
          headerText: i18n.t('启用状态'),
          width: '150',
          valueConverter: {
            type: 'map',
            map: {
              0: i18n.t('草稿'),
              1: i18n.t('激活'),
              2: i18n.t('分发'),
              3: i18n.t('失效'),
              '-1': i18n.t('待审核'),
              '-2': i18n.t('准备')
            }
          }
        },
        {
          field: 'updateTime',
          headerText: i18n.t('更新时间')
        },
        {
          field: 'updateUserName',
          headerText: i18n.t('更新人')
        }
      ],
      asyncConfig: {
        url: `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem(
          'currentBu'
        )}`
      }
    }
  }
]
