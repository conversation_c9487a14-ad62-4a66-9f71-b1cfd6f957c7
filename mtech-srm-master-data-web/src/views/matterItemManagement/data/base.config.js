import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2022-01-13 15:44:16
 * @LastEditTime: 2022-01-24 11:29:57
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\matterItemManagement\data\base.config.js
 */
import Vue from 'vue'
// import { getComponent } from "@syncfusion/ej2-base";
// import { Query } from "@syncfusion/ej2-data";

export const OPTIONS = {
  MATERIAL_TYPE: [],
  UNIT_TYPE: [],
  UNIT: [],
  UNIT_WEIGHT: [],
  UNIT_VOLUME: [],
  UNIT_SIZE: [],
  UNIT_ROUND_TYPE: [],
  UNIT_TIME: [],
  IS_SEALED: [
    {
      label: i18n.t('密闭'),
      value: '1'
    },
    {
      label: i18n.t('不密闭'),
      value: '0'
    }
  ],
  IS_ALLOWED: [
    {
      label: i18n.t('允许'),
      value: 1
    },
    {
      label: i18n.t('不允许'),
      value: 0
    }
  ]
}

export const PAGE_CONFIG_BASE_ATTR = function (data = []) {
  return [
    {
      toolbar: ['Add', 'Delete', 'Edit'],
      grid: {
        height: 200,
        dataSource: [],
        columnData: [
          {
            width: '50',
            type: 'checkbox'
          },
          {
            headerText: this.$t('属性id'),
            field: 'id',
            visible: false,
            isPrimaryKey: true
          },
          {
            headerText: i18n.t('属性编码'),
            field: 'itemClassificationCode',
            width: '200'
          },
          {
            headerText: i18n.t('属性名称'),
            field: 'itemClassificationName',
            width: '200'
          },
          {
            headerText: i18n.t('属性类别编码'),
            field: 'itemClassificationTypeId'
            // visible: false,
          },
          {
            headerText: i18n.t('属性类别'),
            field: 'itemClassificationTypeName',
            width: '200',
            editConfig: {
              type: 'select',
              dataSource: data
            }
            // editType: "dropdownedit",
            // edit: {
            //   params: {
            //     allowFiltering: true,
            //     dataSource: that.options.ITEM_CLASIFICATION_TYPE,
            //     fields: { text: "name", value: "name" },
            //     query: new Query(),
            //     change: (e) => {
            //       const { itemData } = e;
            //       const { grid } = that.$refs.tepPage.getCurrentTabRef();
            //       console.log(grid, itemData);
            //       // const editRowIndex =
            //       //   grid.ej2Instances.editModule.editModule.editRowIndex;
            //       // const grid = new getComponent("dataGrid1", "grid");
            //       // const { previousData } =
            //       //   grid.ej2Instances.editModule.editModule;
            //       // grid.ej2Instances.setCellValue(
            //       //   previousData.id,
            //       //   "itemClassificationTypeId",
            //       //   itemData.name
            //       // );
            //       const rowIndex =
            //         grid.ej2Instances.editModule.editModule.cellDetails
            //           .rowIndex;
            //       grid.updateCell(
            //         rowIndex,
            //         "itemClassificationTypeId",
            //         itemData.itemCode
            //       );
            //     },
            //   },
            // },
          },
          {
            headerText: i18n.t('属性值'),
            field: 'itemClassificationValueNames',
            width: '300',
            headerTemplate: function () {
              return {
                template: Vue.component('datetemplate', {
                  template: `<div style="color: #292929; font-weight: 500; font-size: 14px;">
                    {{ headerText }}
                    <mt-tool-tip content="属性值之间使用“ ；”相隔" target="#tip">
                      <mt-icon id="tip" name="icon_solid_Information" class="mt-ml-10" style="color: #9BAAC1;" />
                    </mt-tool-tip>
                  </div>`,
                  data: function () {
                    return { headerText: this.$t('属性值') }
                  }
                })
              }
            }
          },
          {
            headerText: i18n.t('属性描述'),
            field: 'itemClassificationDescription',
            width: 'auto'
          }
        ],
        editSettings: {
          allowEditing: true,
          allowAdding: true,
          allowDeleting: true,
          mode: 'Normal'
        },
        allowPaging: false
      }
    }
  ]
}
