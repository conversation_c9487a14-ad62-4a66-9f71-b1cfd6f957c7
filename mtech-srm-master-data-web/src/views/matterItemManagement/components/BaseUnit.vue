<!--
 * @Author: your name
 * @Date: 2022-01-14 10:15:32
 * @LastEditTime: 2022-01-27 10:42:20
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\matterItemManagement\components\BaseAttr.vue
-->
<template>
  <div>
    <div class="flex">
      <div class="mt-pa-10 custom-btn" @click="handleAdd">
        <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('新增') }}
      </div>
      <div class="mt-pa-10 custom-btn" @click="handleDelete">
        <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('删除') }}
      </div>
      <div class="mt-pa-10 custom-btn" @click="handleSave">
        <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('保存') }}
      </div>
    </div>
    <mt-data-grid
      v-if="columnsSpAttrs"
      id="dataGrid2"
      ref="dataGrid2"
      :data-source="dataSpAttrs"
      :edit-settings="{
        allowEditing: true,
        allowAdding: true,
        allowDeleting: true,
        mode: 'Batch'
      }"
      :selection-settings="{ type: 'Multiple' }"
      :column-data="columnsSpAttrs"
    ></mt-data-grid>
  </div>
</template>

<script>
import { getComponent } from '@syncfusion/ej2-base'
// import { DropDownList } from "@syncfusion/ej2-vue-dropdowns";
import { Query } from '@syncfusion/ej2-data'
import { utils } from '@mtech-common/utils'
const COLUMNS_SP_ATTRS = function (data = []) {
  return [
    {
      width: '50',
      type: 'checkbox'
    },
    {
      field: 'unitCode',
      headerText: this.$t('单位代码'),
      allowEditing: false
    },
    {
      headerText: this.$t('类型id'),
      field: 'unitTypeId',
      visible: false
    },
    {
      headerText: this.$t('单位id'),
      field: 'unitId',
      visible: false
    },
    {
      field: 'unitName',
      // width: "200",
      headerText: this.$t('单位名称'),
      editType: 'dropdownedit',
      edit: {
        params: {
          allowFiltering: true,
          dataSource: data,
          fields: { text: 'unitName', value: 'unitName' },
          query: new Query(),
          change: (e) => {
            const { itemData } = e
            const grid = new getComponent('dataGrid2', 'grid')
            const rowIndex = grid.editModule.editModule.cellDetails.rowIndex
            grid.updateCell(rowIndex, 'unitId', itemData.id)
            grid.updateCell(rowIndex, 'unitTypeId', itemData.typeId)
            grid.updateCell(rowIndex, 'unitCode', itemData.unitCode)
          }
        }
      }
    },
    {
      headerText: this.$t('是否固定比例换算'),
      field: 'fixRatio',
      visible: false
    },
    {
      field: 'fixRationame',
      // width: "200",
      headerText: this.$t('是否固定比例换算'),
      editType: 'dropdownedit',
      edit: {
        params: {
          allowFiltering: true,
          dataSource: [
            {
              name: this.$t('是'),
              value: 1
            },
            {
              name: this.$t('否'),
              value: 0
            }
          ],
          fields: { text: 'name', value: 'name' },
          query: new Query(),
          change: (e) => {
            const { itemData } = e
            const grid = new getComponent('dataGrid2', 'grid')
            const rowIndex = grid.editModule.editModule.cellDetails.rowIndex
            grid.updateCell(rowIndex, 'fixRatio', itemData.value)
          }
        }
      }
    },
    {
      field: 'calculateRatio',
      // width: "200",
      headerText: this.$t('单位换算(其他单位：基本单位)'),
      editType: 'numericedit'
    }
  ]
}
export default {
  props: {
    itemInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dataSpAttrs: [],
      columnsSpAttrs: COLUMNS_SP_ATTRS()
    }
  },
  watch: {
    'itemInfo.id'() {
      this.getGridData()
    }
  },
  mounted() {
    this.dictTreeGet()
    if (this.itemInfo.id) {
      this.getGridData()
    }
  },
  methods: {
    getGridData() {
      const query = {
        id: this.itemInfo.id
      }
      this.$API.itemManagement.danweilist(query).then((res) => {
        this.dataSpAttrs = res.data.map((e) => {
          e.fixRationame = e.fixRatio === 1 ? this.$t('是') : this.$t('否')
          return e
        })
      })
    },
    handleAdd() {
      this.$refs.dataGrid2.ejsRef.addRecord({
        id: Math.random(),
        calculateRatio: '',
        fixRationame: '',
        fixRatio: '',
        unitId: '',
        unitName: '',
        unitCode: '',
        unitTypeId: ''
      })
    },
    gridDataUpdate(ref = 'dataGrid2', dataKey = 'dataSpAttrs') {
      const changes = this.$refs[ref].ejsRef.getBatchChanges()
      console.log('changes', changes)
      // changes事件中数据分为add change delete三种，其中delete和change都只针对原dataSource
      const { addedRecords, deletedRecords, changedRecords } = changes
      let data = utils.cloneDeep(this[dataKey])

      if (deletedRecords.length) {
        deletedRecords.forEach((el) => {
          data = data.filter((e) => e.id !== el.id)
        })
      }
      if (changedRecords.length) {
        changedRecords.forEach((el) => {
          data = data.map((e) => {
            if (el.id === e.id) {
              return el
            }
            return e
          })
        })
      }
      this[dataKey] = utils.cloneDeep(data.concat(addedRecords))
    },
    handleDelete() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const checkedRows = this.$refs.dataGrid2.ejsRef.getSelectedRecords()
          // const localRows = checkedRows.filter((row) => row.id < 1);
          const originRows = checkedRows.filter((row) => row.id > 1)
          const originIds = originRows.map((row) => row.id)
          const data = { ids: originIds }

          this.$refs.dataGrid2.ejsRef.deleteRecord()
          this.gridDataUpdate()

          originIds.length &&
            this.$API.itemManagement.unitdelete(data).then(() => {
              this.getGridData()
            })
        }
      })
    },
    handleSave() {
      this.gridDataUpdate()

      const data = this.dataSpAttrs.map((e) => {
        e.itemId = e.itemId || this.itemInfo.id
        if (e.id < 1) {
          delete e.id
        }
        return e
      })
      this.$API.itemManagement
        .unitadd(data)
        .then(() => {
          this.getGridData()
        })
        .catch(() => {
          this.getGridData()
        })
    },
    dictTreeGet() {
      const query = { dictCode: '' }
      return this.$API.dict.unitDataGet(query).then((res) => {
        this.columnsSpAttrs = COLUMNS_SP_ATTRS(res.data)
      })
    }
  }
}
</script>

<style></style>
