<template>
  <div>
    <div style="display: flex">
      <div style="height: 100%">
        <div class="e-input-group">
          <mt-icon
            name="icon_search"
            style="margin: 0 10px 0 0; padding-top: 10px"
            class="search-icon cursor-pointer"
          ></mt-icon>
          <input
            v-model="value"
            class="e-input"
            type="text"
            @change="search"
            :placeholder="$t('请输入组织编码或名称')"
          />
        </div>
        <div
          v-for="(item, index) in dataSource"
          :class="{ active: organizationId === item.organizationId }"
          style="cursor: pointer; padding: 15px 60px 15px 15px"
          :key="index"
          @click="handleSelectTab(item)"
        >
          <span>{{ item.text }}</span>
        </div>
      </div>
      <div
        v-if="ruleForm && rules"
        style="padding: 20px; overflow: auto; flex: 1; position: relative"
      >
        <div style="text-align: right; padding: 0 20px; color: #00469c">
          <span style="cursor: pointer" @click="holds">{{ text }}</span>
        </div>
        <div style="padding: 30px 0">
          <div
            style="
              width: 3px;
              display: inline-block;
              height: 12px;
              background: rgba(0, 70, 156, 1);
              border-radius: 2px 0 0 2px;
            "
          ></div>
          <div
            style="
              display: inline-block;
              margin-left: 2px;
              font-size: 16px;
              font-weight: 600;
              color: rgba(41, 41, 41, 1);
            "
          >
            {{ $t('质量信息') }}
          </div>
        </div>
        <div>
          <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
            <div class="div">
              <mt-form-item prop="skipQualityControl" :label="$t('质量免检标识')">
                <mt-radio
                  v-model.number="ruleForm.skipQualityControl"
                  :disabled="disabled"
                  :data-source="skipQualityControl"
                ></mt-radio>
              </mt-form-item>
              <mt-form-item prop="qualityControlDuration" :label="$t('校验间隔')">
                <mt-input
                  v-model.number="ruleForm.qualityControlDuration"
                  :show-clear-button="true"
                  type="text"
                  :disabled="disabled"
                  :placeholder="$t('请输入校验间隔')"
                ></mt-input>
              </mt-form-item>
              <mt-form-item prop="qualityControlKeyId" :label="$t('质量管理控制键')">
                <mt-select
                  v-model="ruleForm.qualityControlKeyId"
                  :data-source="qualityControlKey"
                  :disabled="disabled"
                  :fields="{ text: 'name', value: 'id' }"
                  :show-clear-button="true"
                  :placeholder="$t('请输入')"
                  @change="change($event, 'qualityControlKeyName')"
                ></mt-select>
              </mt-form-item>
              <div class="mt-form-item"></div>
            </div>
          </mt-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { formatRules } from '@/utils/util'
export default {
  name: 'Quality',
  props: {
    form: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: 'preview'
    }
  },
  data() {
    return {
      value: '',
      ruleForm: {},
      rules: null,
      dataSource: [],
      itemOrgRelId: null,
      organizationId: '-99',
      organizationCode: 'organizationCode',
      organizationName: this.$t('默认数据源'),
      skipQualityControl: [
        {
          label: this.$t('不免检'),
          value: 0
        },
        {
          label: this.$t('免检'),
          value: 1
        }
      ],
      qualityControlKey: [],
      edit: this.type !== 'preview'
    }
  },
  computed: {
    text() {
      if (this.edit) return this.$t('保存')
      return this.$t('编辑')
    },
    disabled() {
      return !this.edit
    }
  },
  created() {
    this.init()
  },
  methods: {
    search() {
      this.$API.itemManagement
        .itemFindRelSites({ id: this.form.id, keyword: this.value })
        .then((res) => {
          this.dataSource = []
          res.data.forEach((ele) => {
            let obj = {
              text: ele.organizationName,
              ...ele
            }
            this.dataSource.push(obj)
          })
          console.log(res)
        })
    },
    change(value, name) {
      console.log(value, name)
      this.ruleForm[name] = value.itemData.name
    },
    holds() {
      if (this.edit) {
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            let data = {
              ...this.ruleForm,
              itemId: this.form.id,
              itemOrgRelId: this.itemOrgRelId,
              organizationId: this.organizationId,
              organizationCode: this.organizationCode,
              organizationName: this.organizationName
            }
            this.$API.itemManagement.itemQualityUpdate(data).then((res) => {
              console.log(res)
              this.edit = false
            })
          }
        })
      } else {
        this.edit = true
      }
    },
    init() {
      this.$API.itemManagement
        .itemTreelistOne({
          dictCode: 'ITEM-QC-CONTROL-KEY'
        })
        .then((res) => {
          this.qualityControlKey = res.data
        })
      this.$API.itemManagement
        .itemQualityDetail({
          itemId: this.form.id,
          organizationId: this.organizationId,
          organizationCode: this.organizationCode
        })
        .then((res) => {
          this.ruleForm = res.data || {}
        })
      this.$API.itemManagement.itemQualityUpdateValid().then((res) => {
        this.rules = formatRules(res.data)
      })
      this.$API.itemManagement
        .itemFindRelSites({ id: this.form.id, keyword: this.value })
        .then((res) => {
          this.dataSource = []
          res.data.forEach((ele) => {
            let obj = {
              text: ele.organizationName,
              ...ele
            }
            this.dataSource.push(obj)
          })
          console.log(res)
        })
    },
    handleSelectTab(item) {
      if (this.organizationId != item.organizationId) {
        this.organizationId = item.organizationId
        this.itemOrgRelId = item.id
        this.organizationCode = item.organizationCode
        this.organizationName = item.organizationName
        this.init()
      }
      console.log(123123, item)
    }
  }
}
</script>

<style lang="scss" scoped>
.tabs /deep/ .e-tab-header {
  background: rgb(250, 250, 250);
}
.icon {
  width: 3px;
  display: inline-block;
  height: 12px;
  background: rgba(0, 70, 156, 1);
  border-radius: 2px 0 0 2px;
}
.e-input-group {
  margin: 15px 0;
}
.title {
  display: inline-block;
  margin-left: 2px;
  font-size: 16px;
  font-weight: 600;
  color: rgba(41, 41, 41, 1);
}
.active {
  background: #f5f6f9;
  color: #00469c !important;
}
.mt-form {
  display: flex;
  flex-wrap: wrap;
  .div {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    .mt-form-item {
      flex: 1;
      margin: 5px 10px;
    }
  }
}
</style>
