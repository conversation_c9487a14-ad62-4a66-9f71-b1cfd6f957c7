<!--
 * @Author: your name
 * @Date: 2022-01-12 20:56:59
 * @LastEditTime: 2022-01-19 18:45:28
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\matterItemManagement\components\Quality.vue
-->
<template>
  <div class="quality-wrapper flex justify-between full-height">
    <div class="left full-height">
      <div class="left-header mt-pa-10">
        <div class="e-input-group left-header--search">
          <mt-icon
            name="icon_search"
            class="left-header--search__icon mt-mr-10 cursor-pointer"
          ></mt-icon>
          <input
            v-model="searchForm.keyword"
            class="e-input"
            type="text"
            @input="getRelSite"
            :placeholder="$t('请输入组织编码或名称')"
          />
        </div>
      </div>
      <div class="left-list">
        <div
          v-for="site in siteList"
          :key="site.organizationId"
          :class="[
            'left-list--item',
            currentSite.organizationId === site.organizationId ? 'active' : ''
          ]"
          @click="itemChange(site)"
        >
          <div class="left-list--item__title">
            {{ site.organizationName }}
          </div>
        </div>
      </div>
    </div>
    <div class="right">
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <div class="right-pannel mt-pa-20">
          <div class="subtitle mt-my-20 mt-px-10">{{ $t('质量要求') }}</div>
          <mt-row :gutter="20">
            <mt-col :sm="8" :md="6" :lg="6" :xl="6" class="col-height-74">
              <mt-form-item prop="skipQualityControl" :label="$t('质量免检标识')">
                <mt-radio
                  v-model="ruleForm.skipQualityControl"
                  :data-source="options.IS_FREE_MARK"
                  :disabled="readonly"
                ></mt-radio>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="qualityControlDuration" :label="$t('校验间隔')">
                <mt-input
                  v-model="ruleForm.qualityControlDuration"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  type="text"
                  :placeholder="$t('请输入校验间隔')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="qualityControlKeyId" :label="$t('质量管理控制键')">
                <mt-select
                  v-model="ruleForm.qualityControlKeyId"
                  :data-source="options.QUALITY_KEY"
                  :readonly="readonly"
                  :fields="{ text: 'name', value: 'id' }"
                  :show-clear-button="!readonly"
                  :placeholder="$t('请输入质量管理控制键')"
                ></mt-select>
              </mt-form-item>
            </mt-col>
          </mt-row>
        </div>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { utils } from '@mtech-common/utils'
import { OPTIONS } from '../data/quality.config'

export default {
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    childData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      options: OPTIONS,

      searchForm: {},

      itemInfo: this.childData,
      siteList: [],
      currentSite: {},

      ruleForm: {},
      rules: {}
    }
  },
  computed: {
    readonly() {
      return !this.isEdit
    }
  },
  watch: {
    childData: {
      handler(val) {
        this.itemInfo = val
      },
      deep: true
    },
    'itemInfo.id'() {
      this.getRelSite()
    }
  },
  mounted() {
    this.getRelSite = utils.debounce(this.getRelSite, 500)
    this.dataInit()
  },
  methods: {
    dataInit() {
      this.getRelSite()
      this.dictTreeGet('QUALITY_KEY', 'ITEM-QC-CONTROL-KEY')
    },
    getRelSite() {
      const query = {
        id: this.itemInfo.id,
        keyword: this.searchForm.keyword
      }
      this.$API.itemManagement.itemFindRelSites(query).then((res) => {
        this.siteList = res.data
        const data = res.data
        const currentItem = data[0] // 默认数据源是始终存在的，此处取data[0]没问题

        this.siteList = data
        this.itemChange(currentItem)
      })
    },
    itemChange(item) {
      this.currentSite = item

      this.getRelSiteDetail()
    },
    getRelSiteDetail() {
      const { organizationId, organizationCode } = this.currentSite
      const query = {
        itemId: this.itemInfo.id,
        organizationCode,
        organizationId
      }
      this.$API.itemManagement.itemQualityDetail(query).then((res) => {
        this.ruleForm = res.data || {}
      })
    },
    dataEmit() {
      const {
        id: itemOrgRelId,
        organizationId,
        organizationCode,
        organizationName
      } = this.currentSite

      return {
        ...this.ruleForm,
        itemId: this.itemInfo.id,
        itemOrgRelId,
        organizationId,
        organizationCode,
        organizationName
      }
    },

    dictTreeGet(option, dictCode) {
      const query = { dictCode }
      this.$API.dict.TenantDictTreeGet(query).then((res) => {
        this.options[option] = res.data
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.quality-wrapper {
  .left {
    width: 208px;
    border-right: 1px solid rgba(232, 232, 232, 1);
    height: 100%;
    overflow: auto;
    &-header {
      border-bottom: 1px solid #e8e8e8;
      &--search {
        &__icon {
          margin-top: 7px;
        }
      }
    }
    &-list {
      height: calc(100% - 56px);
      padding: 5px 0;
      overflow: auto;
      &--item {
        height: 50px;
        padding: 15px 20px;
        &__title {
          height: 20px;
          line-height: 20px;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          color: #292929;
        }
      }
      .active {
        background: rgba(245, 246, 249, 1);
        div {
          color: rgba(0, 70, 156, 1);
        }
      }
    }
    &-arrow {
      position: absolute;
      right: -12px;
      top: 85px;
      width: 12px;
      height: 60px;
      line-height: 60px;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 0 4px 4px 0;
      font-size: 12px;
      color: #fff;
    }
  }

  .right {
    width: calc(100% - 147px);
    height: 100%;
    overflow: auto;
    &-pannel {
      .subtitle {
        font-size: 16px;
        font-family: PingFangSC;
        font-weight: 600;
        color: rgba(41, 41, 41, 1);
        border-radius: 2px 0 0 2px;
        border-left: 3px solid rgba(0, 70, 156, 1);
      }
    }

    /deep/ .label {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      margin-bottom: 10px;
    }

    .col-height-74 {
      height: 74px;
    }
  }
}
</style>
