<!--
 * @Author: your name
 * @Date: 2022-01-14 15:01:05
 * @LastEditTime: 2022-01-26 15:09:08
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\matterItemManagement\components\Preview.vue
-->
<template>
  <div class="position-relative wrapper">
    <img class="full-size wrapper-image" :src="fileData.filePath" />
    <div class="wrapper-actions">
      <slot>
        <div class="flex justify-end items-center full-size">
          <span class="wrapper-actions--btn" @click="del">{{ $t('删除') }}</span>
        </div>
      </slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    fileData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  methods: {
    del() {
      this.$emit('del', this.fileData)
    }
  }
}
</script>

<style lang="scss" scoped>
.position-relative {
  position: relative;
  width: 100%;
  height: 100%;
}
.full-size {
  width: 100%;
  height: 100%;
}
.wrapper {
  &:hover &-actions {
    display: block;
  }
  &-image {
    position: absolute;
    left: 0;
    top: 0;
  }
  &-actions {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 36px;
    padding: 0 10px;
    display: none;
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0.79) 0%,
      rgba(0, 0, 0, 0.22) 61.61989865603147%,
      rgba(0, 0, 0, 0) 100%
    );
    &--btn {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgb(250, 250, 250);
    }
  }
}
</style>
