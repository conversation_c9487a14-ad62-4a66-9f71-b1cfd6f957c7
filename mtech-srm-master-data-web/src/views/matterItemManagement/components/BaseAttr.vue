<!--
 * @Author: your name
 * @Date: 2022-01-14 10:15:32
 * @LastEditTime: 2022-01-26 15:05:44
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\matterItemManagement\components\BaseAttr.vue
-->
<template>
  <div>
    <div class="flex">
      <div class="mt-pa-10 custom-btn" @click="handleAdd">
        <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('新增') }}
      </div>
      <div class="mt-pa-10 custom-btn" @click="handleDelete">
        <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('删除') }}
      </div>
      <div class="mt-pa-10 custom-btn" @click="handleSave">
        <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('保存') }}
      </div>
    </div>
    <mt-data-grid
      v-if="columnsSpAttrs"
      id="dataGrid"
      ref="dataGrid"
      :data-source="dataSpAttrs"
      :edit-settings="{
        allowEditing: true,
        allowAdding: true,
        allowDeleting: true,
        mode: 'Batch'
      }"
      :selection-settings="{ type: 'Multiple' }"
      :column-data="columnsSpAttrs"
    ></mt-data-grid>
  </div>
</template>

<script>
import Vue from 'vue'
import { getComponent } from '@syncfusion/ej2-base'
// import { DropDownList } from "@syncfusion/ej2-vue-dropdowns";
import { Query } from '@syncfusion/ej2-data'
import { utils } from '@mtech-common/utils'
const COLUMNS_SP_ATTRS = function (data = []) {
  return [
    {
      width: '50',
      type: 'checkbox'
    },
    {
      headerText: 'ID',
      field: 'id',
      isPrimaryKey: true,
      visible: false
    },
    {
      headerText: this.$t('属性编码'),
      field: 'itemClassificationCode'
    },
    {
      headerText: this.$t('属性名称'),
      field: 'itemClassificationName'
    },
    {
      headerText: this.$t('属性类别编码'),
      field: 'itemClassificationTypeCode',
      visible: false
    },
    {
      headerText: this.$t('属性类别'),
      field: 'itemClassificationTypeName',
      editType: 'dropdownedit',
      edit: {
        params: {
          allowFiltering: true,
          dataSource: data,
          fields: { text: 'name', value: 'name' },
          query: new Query(),
          change: (e) => {
            const { itemData } = e
            const grid = new getComponent('dataGrid', 'grid')
            const rowIndex = grid.editModule.editModule.cellDetails.rowIndex
            grid.updateCell(rowIndex, 'itemClassificationTypeCode', itemData.itemCode)
          }
        }
      }
    },
    {
      headerText: this.$t('属性值'),
      field: 'itemClassificationValueNames',
      headerTemplate: function () {
        return {
          template: Vue.component('datetemplate', {
            template: `<div style="color: #292929; font-weight: 500; font-size: 14px;">
              {{ headerText }}
              <mt-tool-tip content="属性值之间使用“ ；”相隔" target="#tip">
                <mt-icon id="tip" name="icon_solid_Information" class="mt-ml-10" style="color: #9BAAC1;" />
              </mt-tool-tip>
            </div>`,
            data: function () {
              return { headerText: this.$t('属性值') }
            }
          })
        }
      }
    },
    {
      headerText: this.$t('属性描述'),
      field: 'itemClassificationDescription'
    }
  ]
}
export default {
  props: {
    itemInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dataSpAttrs: [],
      columnsSpAttrs: COLUMNS_SP_ATTRS()
    }
  },
  watch: {
    'itemInfo.id'() {
      this.getGridData()
    }
  },
  mounted() {
    this.dictTreeGet()
    if (this.itemInfo.id) {
      this.getGridData()
    }
  },
  methods: {
    getGridData() {
      const query = {
        id: this.itemInfo.id
      }
      this.$API.material.baseAttrGet(query).then((res) => {
        this.dataSpAttrs = res.data
      })
    },
    handleAdd() {
      this.$refs.dataGrid.ejsRef.addRecord({
        id: Math.random(),
        itemClassificationCode: '',
        itemClassificationName: '',
        itemClassificationTypeCode: '',
        itemClassificationTypeName: '',
        itemClassificationValueNames: '',
        itemClassificationDescription: ''
      })
    },
    gridDataUpdate(ref = 'dataGrid', dataKey = 'dataSpAttrs') {
      const changes = this.$refs[ref].ejsRef.getBatchChanges()
      console.log('changes', changes)
      // changes事件中数据分为add change delete三种，其中delete和change都只针对原dataSource
      const { addedRecords, deletedRecords, changedRecords } = changes
      let data = utils.cloneDeep(this[dataKey])

      if (deletedRecords.length) {
        deletedRecords.forEach((el) => {
          data = data.filter((e) => e.id !== el.id)
        })
      }
      if (changedRecords.length) {
        changedRecords.forEach((el) => {
          data = data.map((e) => {
            if (el.id === e.id) {
              return el
            }
            return e
          })
        })
      }
      this[dataKey] = utils.cloneDeep(data.concat(addedRecords))
    },
    handleDelete() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const checkedRows = this.$refs.dataGrid.ejsRef.getSelectedRecords()
          // const localRows = checkedRows.filter((row) => row.id < 1);
          const originRows = checkedRows.filter((row) => row.id > 1)
          const originIds = originRows.map((row) => row.id)
          const data = { ids: originIds }

          this.$refs.dataGrid.ejsRef.deleteRecord()
          this.gridDataUpdate()

          originIds.length &&
            this.$API.itemManagement.texingDelete(data).then(() => {
              this.getGridData()
            })
        }
      })
    },
    handleSave() {
      this.gridDataUpdate()

      const data = this.dataSpAttrs.map((e) => {
        e.itemId = e.itemId || this.itemInfo.id
        if (e.id < 1) {
          delete e.id
        }
        return e
      })
      this.$API.itemManagement
        .texingedit(data)
        .then(() => {
          this.getGridData()
        })
        .catch(() => {
          this.getGridData()
        })
    },
    dictTreeGet() {
      const query = { dictCode: 'ITEM_CLASIFICATION_TYPE' }
      return this.$API.dict.TenantDictTreeGet(query).then((res) => {
        this.columnsSpAttrs = COLUMNS_SP_ATTRS(res.data)
      })
    }
  }
}
</script>

<style></style>
