<!--
 * @Author: your name
 * @Date: 2022-01-24 16:39:58
 * @LastEditTime: 2022-02-21 11:18:04
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\matterItemManagement\components\BaseCate.vue
-->
<template>
  <div>
    <div class="flex">
      <div class="mt-pa-10 custom-btn" @click="handleAdd">
        <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('新增') }}
      </div>
      <div class="mt-pa-10 custom-btn" @click="handleDelete">
        <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('删除') }}
      </div>
    </div>
    <mt-data-grid
      id="dataGrid3"
      ref="dataGrid3"
      :data-source="dataSource"
      :column-data="columnData"
    ></mt-data-grid>
    <mt-dialog css-class="create-proj-dialog" ref="dialog" :header="$t('新增')" :buttons="buttons">
      <mt-form ref="ruleForm" :model="ruleForm">
        <mt-form-item prop="organizationId" :label="$t('组织名称')">
          <mt-DropDownTree
            id="filter"
            :fields="orgFields"
            :placeholder="$t('请选择组织')"
            @select="orgChange"
          ></mt-DropDownTree>
        </mt-form-item>
        <mt-form-item prop="type" :label="$t('品类种类')">
          <mt-select
            :data-source="dataCateType"
            :fields="{ text: 'typeName', value: 'id' }"
            :show-clear-button="true"
            :placeholder="$t('请选择品类种类')"
            @select="cateTypeChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="categoryId" :label="$t('品类')">
          <mt-DropDownTree
            id="category"
            :fields="cateFields"
            :placeholder="$t('请选择品类')"
            @select="(e) => (ruleForm.categoryId = e.itemData.id)"
          ></mt-DropDownTree>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
export default {
  props: {
    itemInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dataSource: [],
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'categoryOrgName',
          // width: "200",
          headerText: this.$t('组织名称')
        },
        {
          field: 'categoryTypeCode',
          headerText: this.$t('品类类别编号')
          // width: "350",
        },
        {
          field: 'categoryTypeName',
          // width: "200",
          headerText: this.$t('品类类别名称')
        },
        // {
        //   field: "itemClassificationTypeName",
        //   // width: "200",
        //   headerText: this.$t("应用领域"),
        // },
        {
          field: 'categoryCode',
          // width: "200",
          headerText: this.$t('品类编号')
        },
        {
          field: 'categoryName',
          // width: "200",
          headerText: this.$t('品类名称')
        }
      ],

      ruleForm: {},
      rules: {},
      orgFields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      dataCateType: [],
      cateFields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  watch: {
    'itemInfo.id'() {
      this.getGridData()
    }
  },
  mounted() {
    if (this.itemInfo.id) {
      this.getGridData()
    }
  },
  methods: {
    getGridData() {
      const query = {
        itemId: this.itemInfo.id
      }
      this.$API.itemManagement.pinleilist(query).then((res) => {
        this.dataSource = res.data
      })
    },
    handleAdd(data) {
      this.$refs.ruleForm.resetFields()
      this.ruleForm = Object.assign({}, data)
      this.$refs.dialog.ejsRef.show()
      this.orgTreeDataGet()
      this.cateTypeDataGet()
    },
    handleDelete() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const rows = this.$refs.dataGrid3.ejsRef.getSelectedRecords()
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.itemManagement.pinleidelete(data).then(() => {
            this.getGridData()
          })
        }
      })
    },
    save() {
      const data = {
        categoryId: this.ruleForm.categoryId,
        itemId: this.itemInfo.id
      }
      this.$API.itemManagement.pinleiAdd(data).then(() => {
        this.cancel()
        this.getGridData()
      })
    },
    cancel() {
      this.$refs.dialog.ejsRef.hide()
    },

    orgChange(e) {
      this.ruleForm.organizationId = e.itemData.id
      this.cateGet()
    },
    cateTypeChange(e) {
      this.ruleForm.type = e.itemData.id
      this.cateGet()
    },
    cateGet() {
      if (this.ruleForm.type && this.ruleForm.organizationId) {
        const query = {
          categoryTypeId: this.ruleForm.type,
          organizationId: this.ruleForm.organizationId
        }
        this.$API.itemManagement.getTree(query).then((res) => {
          this.cateFields = Object.assign({}, this.cateFields, {
            dataSource: res.data
          })
        })
      }
    },
    orgTreeDataGet() {
      this.$API.itemManagement.getFuzzyCompanyTree({ fuzzyName: '' }).then((res) => {
        this.orgFields = Object.assign({}, this.orgFields, {
          dataSource: res.data
        })
      })
    },
    cateTypeDataGet() {
      this.$API.itemManagement.criteriaQuery({}).then((res) => {
        this.dataCateType = res.data
      })
    }
  }
}
</script>

<style></style>
