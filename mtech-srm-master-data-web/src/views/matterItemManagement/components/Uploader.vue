<!--
 * @Author: your name
 * @Date: 2022-01-14 14:05:01
 * @LastEditTime: 2022-01-26 16:03:55
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\matterItemManagement\components\Uploader.vue
-->
<template>
  <div>
    <mt-uploader
      name="UploadFiles"
      :async-settings="asyncSettings"
      :uploading="uploading"
      :allowed-extensions="allowedExtensions"
      @success="success"
    >
      <div class="uploader flex justify-center items-center">
        <img class="uploader-icon" :src="defaultIcon" alt="" />
      </div>
    </mt-uploader>
  </div>
</template>

<script>
import { HEADER_TOKEN_KEY, getToken } from '@mtech-sso/single-sign-on'
export default {
  props: {
    asyncSettings: {
      type: Object,
      default: () => {}
    },
    allowedExtensions: {
      type: String,
      default: '.jpg,.png'
    }
  },
  data() {
    return {
      defaultIcon:
        'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAYAAAA6/NlyAAAACXBIWXMAAAsTAAALEwEAmpwYAAAA7ElEQVRo3u3WQQ6DIBCFYbokkX178mpvoO0pxCu5JWUspCx1NJi0P8mLRIPyhVE0plJrB5/TxMwpTT7/cw0wYMCAAQMGDBgwYMCAAQMGDBgwYMCAAQP+S3AxkdW596Nm3CWNc4KN/Tn1l2uaeWhSDdx9x9qEFbStBS3BzdbEiS7H7jltiHeP1yTAW7HC13YQ9Og089DEFO/T6uQV2pEQ7xF23kMVo8XKcUdCQp8CVpf0xkjZ2k8Zx4f3/rSSrpr0oTr1o1UreetxRYnV35b40wIMGDBgwIABAwYMGDBgwIABAwYMGDBgwIABH9beZ0P+g1n2xFAAAAAASUVORK5CYII='
    }
  },
  methods: {
    uploading(args) {
      args.currentRequest.setRequestHeader(HEADER_TOKEN_KEY, getToken())
    },
    success({ e }) {
      const response = JSON.parse(e?.currentTarget?.response)
      this.$emit('success', response)
    }
  }
}
</script>

<style lang="scss" scoped>
.uploader {
  width: 148px;
  height: 148px;
  border: 1px dashed #c0ccda;
  border-radius: 6px;
  background-color: #fbfdff;
  &:hover {
    border-color: #00469c;
    // box-shadow: 0 0 8px 0 rgb(232 237 250 / 60%),
    //   0 2px 4px 0 rgb(232 237 250 / 50%);
  }
  &-icon {
    width: 28px;
  }
}
</style>
