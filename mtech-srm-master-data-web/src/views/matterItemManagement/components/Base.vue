<!--
 * @Author: your name
 * @Date: 2022-01-12 20:54:56
 * @LastEditTime: 2022-01-27 13:48:47
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\matterItemManagement\components\Base.vue
-->
<template>
  <div class="base-view flex justify-between full-width full-height">
    <div class="left mt-pa-20">
      <div v-for="nav in navList" :key="nav.name" class="left-nav" @click="navChange(nav)">
        <span :class="['mt-pl-10', currentNav === nav.name ? 'active' : '']">
          {{ nav.name }}
        </span>
      </div>
    </div>
    <div class="right" id="right-view">
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <div id="基本信息" class="right-pannel mt-pa-20">
          <div class="subtitle mt-my-20 mt-px-10">{{ $t('基本信息') }}</div>
          <mt-row :gutter="20">
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="cloudItemCode" :label="$t('云平台编码')">
                <mt-input
                  v-model="ruleForm.cloudItemCode"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  type="text"
                  :placeholder="$t('请输入云平台编码')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="unspscCode" :label="$t('UNSPSC编号')">
                <mt-input
                  v-model="ruleForm.unspscCode"
                  :readonly="readonly"
                  :show-clear-button="!readonly"
                  type="text"
                  :placeholder="$t('请输入UNSPSC编号')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="unspscName" :label="$t('UNSPSC名称')">
                <mt-input
                  v-model="ruleForm.unspscName"
                  :readonly="readonly"
                  :show-clear-button="!readonly"
                  type="text"
                  :placeholder="$t('请输入UNSPSC名称')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="secondMeasureUnitId" :label="$t('双计量单位')">
                <div class="flex">
                  <mt-select
                    v-model="ruleForm.secondMeasureUnitTypeId"
                    :data-source="options.UNIT_TYPE"
                    :readonly="readonly"
                    :fields="{ text: 'name', value: 'id' }"
                    :show-clear-button="!readonly"
                    @change="unitTypeChange"
                  ></mt-select>
                  <mt-select
                    v-model="ruleForm.secondMeasureUnitId"
                    :data-source="options.UNIT"
                    :readonly="readonly"
                    :fields="{ text: 'unitName', value: 'id' }"
                    :show-clear-button="!readonly"
                  ></mt-select>
                </div>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="materialTypeId" :label="$t('材料类型')">
                <mt-select
                  v-model="ruleForm.materialTypeId"
                  :data-source="options.MATERIAL_TYPE"
                  :readonly="readonly"
                  :fields="{ text: 'name', value: 'id' }"
                  :show-clear-button="!readonly"
                  :placeholder="$t('请输入材料类型')"
                ></mt-select>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="manufacturerName" :label="$t('制造商')">
                <mt-input
                  v-model="ruleForm.manufacturerName"
                  :readonly="readonly"
                  :show-clear-button="!readonly"
                  type="text"
                  :placeholder="$t('请输入制造商')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="manufacturerItemCode" :label="$t('制造商物料编码')">
                <mt-input
                  v-model="ruleForm.manufacturerItemCode"
                  :readonly="readonly"
                  :show-clear-button="!readonly"
                  type="text"
                  :placeholder="$t('请输入制造商物料编码')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="createUserName" :label="$t('创建人')">
                <mt-input
                  v-model="ruleForm.createUserName"
                  :show-clear-button="false"
                  :readonly="true"
                  type="text"
                  :placeholder="$t('请输入创建人')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="createTime" :label="$t('创建时间')">
                <mt-input
                  v-model="ruleForm.createTime"
                  :show-clear-button="false"
                  :readonly="true"
                  type="text"
                  :placeholder="$t('请输入创建时间')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
          </mt-row>
          <div class="mt-mb-10">{{ $t('物料图片') }}</div>
          <div class="right-pannel--file flex justify-start">
            <div
              v-for="file in matImagePathList"
              :key="file.id"
              class="right-pannel--file__preview mt-mr-20"
            >
              <preview :file-data="file" @del="fileDel($event, 'matImagePathList')"></preview>
            </div>
            <uploader
              v-if="uploaderShow"
              :async-settings="asyncSettings"
              @success="matSuccess"
            ></uploader>
          </div>
        </div>
        <div id="特性" class="right-pannel mt-pa-20">
          <div class="subtitle mt-my-20 mt-px-10">{{ $t('特性') }}</div>
          <base-attr :item-info="childData"></base-attr>
        </div>
        <div id="量纲" class="right-pannel mt-pa-20">
          <div class="subtitle mt-my-20 mt-px-10">{{ $t('量纲') }}</div>
          <mt-row :gutter="20">
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="grossWeight" :label="$t('毛重')">
                <mt-input-number
                  v-model="ruleForm.grossWeight"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入毛重')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="netWeight" :label="$t('净重')">
                <mt-input-number
                  v-model="ruleForm.netWeight"
                  :readonly="readonly"
                  :show-clear-button="!readonly"
                  :placeholder="$t('请输入净重')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="unspscName" :label="$t('重量单位')">
                <mt-select
                  v-model="ruleForm.unspscName"
                  :data-source="options.UNIT_WEIGHT"
                  :readonly="readonly"
                  :fields="{ text: 'unitName', value: 'id' }"
                  :show-clear-button="!readonly"
                  :placeholder="$t('请输入重量单位')"
                ></mt-select>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="dimensionName" :label="$t('量纲')">
                <mt-input
                  v-model="ruleForm.dimensionName"
                  :readonly="readonly"
                  :show-clear-button="!readonly"
                  type="text"
                  :placeholder="$t('请输入量纲')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="volume" :label="$t('体积')">
                <mt-input-number
                  v-model="ruleForm.volume"
                  :readonly="readonly"
                  :show-clear-button="!readonly"
                  :placeholder="$t('请输入体积')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="volumeUnitId" :label="$t('体积单位')">
                <mt-select
                  v-model="ruleForm.volumeUnitId"
                  :data-source="options.UNIT_VOLUME"
                  :readonly="readonly"
                  :fields="{ text: 'unitName', value: 'id' }"
                  :show-clear-button="!readonly"
                  :placeholder="$t('请输入体积单位')"
                ></mt-select>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="itemLength" :label="$t('长度')">
                <mt-input-number
                  v-model="ruleForm.itemLength"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入长度')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="itemWidth" :label="$t('宽度')">
                <mt-input-number
                  v-model="ruleForm.itemWidth"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入宽度')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="itemHeight" :label="$t('高度')">
                <mt-input-number
                  v-model="ruleForm.itemHeight"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入高度')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="sizeUnitId" :label="$t('长宽高尺寸单位')">
                <mt-select
                  v-model="ruleForm.sizeUnitId"
                  :data-source="options.UNIT_SIZE"
                  :readonly="readonly"
                  :fields="{ text: 'unitName', value: 'id' }"
                  :show-clear-button="!readonly"
                  :placeholder="$t('请输入长宽高尺寸单位')"
                ></mt-select>
              </mt-form-item>
            </mt-col>
          </mt-row>
        </div>
        <div id="计量单位" class="right-pannel mt-pa-20">
          <div class="subtitle mt-my-20 mt-px-10">{{ $t('计量单位') }}</div>
          <base-unit :item-info="childData"></base-unit>
        </div>
        <div id="品类" class="right-pannel mt-pa-20">
          <div class="subtitle mt-my-20 mt-px-10">{{ $t('品类') }}</div>
          <base-cate :item-info="childData"></base-cate>
        </div>
        <div id="舍入" class="right-pannel mt-pa-20">
          <div class="subtitle mt-my-20 mt-px-10">{{ $t('舍入') }}</div>
          <mt-row :gutter="20">
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="roundTypeId" :label="$t('舍入方式')">
                <mt-select
                  v-model="ruleForm.roundTypeId"
                  :data-source="options.UNIT_ROUND_TYPE"
                  :readonly="readonly"
                  :fields="{ text: 'name', value: 'id' }"
                  :show-clear-button="!readonly"
                  :placeholder="$t('请输入舍入方式')"
                ></mt-select>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="roundLength" :label="$t('舍入位数')">
                <mt-input-number
                  v-model="ruleForm.roundLength"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入舍入位数')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
          </mt-row>
        </div>
        <div id="研发" class="right-pannel mt-pa-20">
          <div class="subtitle mt-my-20 mt-px-10">{{ $t('研发') }}</div>
          <mt-row :gutter="20">
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="development.dsdToleranceType" :label="$t('DSD附加公差类型')">
                <mt-input
                  v-model="ruleForm.development.dsdToleranceType"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  type="text"
                  :placeholder="$t('请输入DSD附加公差类型')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item
                prop="development.toleranceDsdStructure"
                :label="$t('公差类型的DSD结构')"
              >
                <mt-input
                  v-model="ruleForm.development.toleranceDsdStructure"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  type="text"
                  :placeholder="$t('请输入公差类型的DSD结构')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="development.versionNumber" :label="$t('版本变更号')">
                <mt-input
                  v-model="ruleForm.development.versionNumber"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  type="text"
                  :placeholder="$t('请输入版本变更号')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="development.graphNumber" :label="$t('图号')">
                <mt-input
                  v-model="ruleForm.development.graphNumber"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  type="text"
                  :placeholder="$t('请输入图号')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
          </mt-row>
          <div class="mt-mb-10">{{ $t('图纸附加') }}</div>
          <div class="right-pannel--file flex justify-start">
            <div
              v-for="file in cadImagePathList"
              :key="file.id"
              class="right-pannel--file__preview mt-mr-20"
            >
              <preview :file-data="file" @del="fileDel($event, 'cadImagePathList')"></preview>
            </div>
            <uploader
              v-if="uploaderShow"
              :async-settings="asyncSettings"
              @success="cadSuccess"
            ></uploader>
          </div>
        </div>
        <div id="运输" class="right-pannel mt-pa-20">
          <div class="subtitle mt-my-20 mt-px-10">{{ $t('运输') }}</div>
          <mt-row :gutter="20">
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="transport.packageDemand" :label="$t('包装方式')">
                <mt-input
                  v-model="ruleForm.transport.packageDemand"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入包装方式')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="transport.storageDemand" :label="$t('贮藏要求')">
                <mt-input
                  v-model="ruleForm.transport.storageDemand"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入贮藏要求')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="transport.allowPackageQuantity" :label="$t('允许包装量')">
                <mt-input-number
                  v-model="ruleForm.transport.allowPackageQuantity"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入允许包装量')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="transport.allowPackageWeight" :label="$t('允许包装重量')">
                <mt-input-number
                  v-model="ruleForm.transport.allowPackageWeight"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入允许包装重量')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item
                prop="transport.allowPackageWeightUnitId"
                :label="$t('允许包装重量单位')"
              >
                <mt-select
                  v-model="ruleForm.transport.allowPackageWeightUnitId"
                  :data-source="options.UNIT_WEIGHT"
                  :readonly="readonly"
                  :fields="{ text: 'unitName', value: 'id' }"
                  :show-clear-button="!readonly"
                  :placeholder="$t('请输入允许包装重量单位')"
                ></mt-select>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="transport.packageMaxHeight" :label="$t('包装最大高度')">
                <mt-input-number
                  v-model="ruleForm.transport.packageMaxHeight"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入包装最大高度')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="transport.packageMaxHeightUnitId" :label="$t('包装最大高度单位')">
                <mt-select
                  v-model="ruleForm.transport.packageMaxHeightUnitId"
                  :data-source="options.UNIT_SIZE"
                  :readonly="readonly"
                  :fields="{ text: 'unitName', value: 'id' }"
                  :show-clear-button="!readonly"
                  :placeholder="$t('请输入包装最大高度单位')"
                ></mt-select>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item
                prop="transport.packageMaxCapacityUnitId"
                :label="$t('包装允许最大单位')"
              >
                <mt-select
                  v-model="ruleForm.transport.packageMaxCapacityUnitId"
                  :data-source="options.UNIT_SIZE"
                  :readonly="readonly"
                  :fields="{ text: 'unitName', value: 'id' }"
                  :show-clear-button="!readonly"
                  :placeholder="$t('请输入包装允许最大单位')"
                ></mt-select>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="transport.transportMaterialType" :label="$t('运输材料类型')">
                <mt-input
                  v-model="ruleForm.transport.transportMaterialType"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入运输材料类型')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6" class="col-height-74">
              <mt-form-item prop="transport.airtightTransport" :label="$t('运输时密闭')">
                <mt-radio
                  v-model="ruleForm.transport.airtightTransport"
                  :data-source="options.IS_SEALED"
                  :disabled="readonly"
                ></mt-radio>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="transport.packingLevel" :label="$t('填充水平')">
                <mt-input
                  v-model="ruleForm.transport.packingLevel"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入填充水平')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6" class="col-height-74">
              <mt-form-item prop="transport.allowStacking" :label="$t('允许堆垛')">
                <mt-radio
                  v-model="ruleForm.transport.allowStacking"
                  :data-source="options.IS_ALLOWED"
                  :disabled="readonly"
                ></mt-radio>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="transport.stackingLevel" :label="$t('堆垛系数')">
                <mt-input-number
                  v-model="ruleForm.transport.stackingLevel"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入堆垛系数')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="transport.totalShelfLife" :label="$t('总保质期')">
                <mt-input-number
                  v-model="ruleForm.transport.totalShelfLife"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入总保质期')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="transport.remainingShelfLife" :label="$t('剩余保质期')">
                <mt-input-number
                  v-model="ruleForm.transport.remainingShelfLife"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入剩余保质期')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="transport.storageShelfLife" :label="$t('储存保质期')">
                <mt-input-number
                  v-model="ruleForm.transport.storageShelfLife"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入储存保质期')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="transport.shelfLifeUnitId" :label="$t('保质期时间单位')">
                <mt-select
                  v-model="ruleForm.transport.shelfLifeUnitId"
                  :data-source="options.UNIT_TIME"
                  :readonly="readonly"
                  :fields="{ text: 'unitName', value: 'id' }"
                  :show-clear-button="!readonly"
                  :placeholder="$t('请输入保质期时间单位')"
                ></mt-select>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="transport.bottomFloorQuantity" :label="$t('底层数')">
                <mt-input-number
                  v-model="ruleForm.transport.bottomFloorQuantity"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入底层数')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="transport.topFloorQuantity" :label="$t('顶层数')">
                <mt-input-number
                  v-model="ruleForm.transport.topFloorQuantity"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入顶层数')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="transport.stackingFactor" :label="$t('堆叠因子')">
                <mt-input-number
                  v-model="ruleForm.transport.stackingFactor"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入堆叠因子')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="transport.loadWeight" :label="$t('负载')">
                <mt-input-number
                  v-model="ruleForm.transport.loadWeight"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入负载')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item
                prop="transport.packingMaterialAllowDeep"
                :label="$t('包装材料允许深度')"
              >
                <mt-input-number
                  v-model="ruleForm.transport.packingMaterialAllowDeep"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入包装材料允许深度')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item
                prop="transport.packingMaterialAllowWidth"
                :label="$t('包装材料允许宽度')"
              >
                <mt-input-number
                  v-model="ruleForm.transport.packingMaterialAllowWidth"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入包装材料允许宽度')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item
                prop="transport.packingMaterialMaxHeight"
                :label="$t('包装材料最大堆放高度')"
              >
                <mt-input-number
                  v-model="ruleForm.transport.packingMaterialMaxHeight"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入包装材料最大堆放高度')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item
                prop="transport.packingMaterialMinHeight"
                :label="$t('包装材料最小堆放高度')"
              >
                <mt-input-number
                  v-model="ruleForm.transport.packingMaterialMinHeight"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入包装材料最小堆放高度')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item
                prop="transport.toleranceOverMaxValueHeight"
                :label="$t('公差超过最大值 堆高（VSO）')"
              >
                <mt-input-number
                  v-model="ruleForm.transport.toleranceOverMaxValueHeight"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入公差超过最大值 堆高（VSO）')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item
                prop="transport.airtightPkmMaterialQuantity"
                :label="$t('每个封闭PKM（VSO）的材料数量')"
              >
                <mt-input-number
                  v-model="ruleForm.transport.airtightPkmMaterialQuantity"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入每个封闭PKM（VSO）的材料数量')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item
                prop="transport.vehicleSpaceOptimizationUnit"
                :label="$t('度量单位的车辆空间优化')"
              >
                <mt-input
                  v-model="ruleForm.transport.vehicleSpaceOptimizationUnit"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入度量单位的车辆空间优化')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item
                prop="transport.airtightPackingMaterialDemand"
                label="要求的封闭包装材料（VSO）"
              >
                <mt-input
                  v-model="ruleForm.transport.airtightPackingMaterialDemand"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  placeholder="请输入要求的封闭包装材料（VSO）"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="transport.returnCode" :label="$t('返回码')">
                <mt-input
                  v-model="ruleForm.transport.returnCode"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入返回码')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="transport.returnLogisticsLevel" :label="$t('返回物流级别')">
                <mt-input
                  v-model="ruleForm.transport.returnLogisticsLevel"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入返回物流级别')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
          </mt-row>
        </div>
        <div id="行业属性" class="right-pannel mt-pa-20">
          <div class="subtitle mt-my-20 mt-px-10">{{ $t('行业属性') }}</div>
          <mt-row :gutter="20">
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="businessProperties.highViscosity" :label="$t('高粘度')">
                <mt-input
                  v-model="ruleForm.businessProperties.highViscosity"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入高粘度')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="businessProperties.bulkOrLiquid" :label="$t('散装/液体')">
                <mt-input
                  v-model="ruleForm.businessProperties.bulkOrLiquid"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入散装/液体')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item
                prop="businessProperties.variantColorFeature"
                :label="$t('变体颜色的特征值')"
              >
                <mt-input
                  v-model="ruleForm.businessProperties.variantColorFeature"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入变体颜色的特征值')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item
                prop="businessProperties.variantSizeFeature"
                :label="$t('变体主要大小的特征值')"
              >
                <mt-input
                  v-model="ruleForm.businessProperties.variantSizeFeature"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入变体主要大小的特征值')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item
                prop="businessProperties.variantSecondSizeFeature"
                :label="$t('变体第二尺寸的特征值')"
              >
                <mt-input
                  v-model="ruleForm.businessProperties.variantSecondSizeFeature"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入变体第二尺寸的特征值')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item
                prop="businessProperties.featureForEvaluate"
                :label="$t('用于评估目的的特征值')"
              >
                <mt-input
                  v-model="ruleForm.businessProperties.featureForEvaluate"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入用于评估目的的特征值')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="businessProperties.maintenanceCode" :label="$t('保养代码')">
                <mt-input
                  v-model="ruleForm.businessProperties.maintenanceCode"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入保养代码')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item
                prop="businessProperties.lowspeedSalerManagement"
                label="慢速销售者管理:时尚程度"
              >
                <mt-input
                  v-model="ruleForm.businessProperties.lowspeedSalerManagement"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入慢速销售者管理:时尚程度')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="businessProperties.fashionLevel" :label="$t('时尚等级')">
                <mt-input
                  v-model="ruleForm.businessProperties.fashionLevel"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入时尚等级')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
          </mt-row>
        </div>
        <div id="系列物料属性" class="right-pannel mt-pa-20">
          <div class="subtitle mt-my-20 mt-px-10">{{ $t('系列物料属性') }}</div>
          <mt-row :gutter="20">
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="seriesProperties,length" :label="$t('长度(mm)')">
                <mt-input-number
                  v-model="ruleForm.seriesProperties.length"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入长度(mm)')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="seriesProperties,width" :label="$t('宽度(mm)')">
                <mt-input-number
                  v-model="ruleForm.seriesProperties.width"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入宽度(mm)')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="seriesProperties,height" :label="$t('高度(mm)')">
                <mt-input-number
                  v-model="ruleForm.seriesProperties.height"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入高度(mm)')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="seriesProperties,makeup" :label="$t('拼版数量')">
                <mt-input-number
                  v-model="ruleForm.seriesProperties.makeup"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入拼版数量')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="seriesProperties,pages" :label="$t('页数')">
                <mt-input-number
                  v-model="ruleForm.seriesProperties.pages"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入页数')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="seriesProperties,mouldCost" :label="$t('模具费')">
                <mt-input-number
                  v-model="ruleForm.seriesProperties.mouldCost"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入模具费')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="seriesProperties,firstEditionNumber" :label="$t('首次打板数量')">
                <mt-input-number
                  v-model="ruleForm.seriesProperties.firstEditionNumber"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入首次打板数量')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="seriesProperties,projectCost" :label="$t('工程费')">
                <mt-input-number
                  v-model="ruleForm.seriesProperties.projectCost"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入工程费')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="seriesProperties,weight" :label="$t('重量(g)')">
                <mt-input-number
                  v-model="ruleForm.seriesProperties.weight"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('请输入重量(g)')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="seriesProperties,otherFee" :label="$t('其它费用')">
                <mt-input-number
                  v-model="ruleForm.seriesProperties.otherFee"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  :placeholder="$t('其它费用')"
                ></mt-input-number>
              </mt-form-item>
            </mt-col>
          </mt-row>
        </div>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { OPTIONS } from '../data/base.config'
import Uploader from './Uploader.vue'
import Preview from './Preview.vue'
import BaseAttr from './BaseAttr.vue'
import BaseUnit from './BaseUnit.vue'
import BaseCate from './BaseCate.vue'
export default {
  components: {
    Uploader,
    Preview,
    BaseAttr,
    BaseUnit,
    BaseCate
  },
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    childData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    var validateNum = (rule, value, callback) => {
      let fieldArr = rule.fullField.split(',')
      let _value = this.ruleForm[fieldArr[0]][fieldArr[1]] + ''
      if (_value === '') {
        callback()
      } else {
        let arr = _value.split('.')
        if ((arr[0] + '').length > 10) {
          callback(new Error('整数位最多10位数字'))
        } else if (arr[1] && (arr[1] + '').length > 2) {
          callback(new Error('小数位最多2位数字'))
        } else {
          callback()
        }
      }
    }
    return {
      options: OPTIONS,

      navList: [
        { name: this.$t('基本信息') },
        { name: this.$t('特性') },
        { name: this.$t('量纲') },
        { name: this.$t('计量单位') },
        { name: this.$t('品类') },
        { name: this.$t('舍入') },
        { name: this.$t('研发') },
        { name: this.$t('运输') },
        { name: this.$t('行业属性') },
        { name: this.$t('系列物料属性') }
      ],
      currentNav: this.$t('基本信息'),
      breakPointList: [],

      rules: {
        'seriesProperties,length': {
          validator: validateNum,
          trigger: 'blur'
        },
        'seriesProperties,width': {
          validator: validateNum,
          trigger: 'blur'
        },
        'seriesProperties,height': {
          validator: validateNum,
          trigger: 'blur'
        },
        'seriesProperties,makeup': {
          validator: validateNum,
          trigger: 'blur'
        },
        'seriesProperties,pages': {
          validator: validateNum,
          trigger: 'blur'
        },
        'seriesProperties,mouldCost': {
          validator: validateNum,
          trigger: 'blur'
        },
        'seriesProperties,firstEditionNumber': {
          validator: validateNum,
          trigger: 'blur'
        },
        'seriesProperties,projectCost': {
          validator: validateNum,
          trigger: 'blur'
        },
        'seriesProperties,weight': {
          validator: validateNum,
          trigger: 'blur'
        },
        'seriesProperties,otherFee': {
          validator: validateNum,
          trigger: 'blur'
        }
      },
      ruleForm: {
        development: {},
        transport: {},
        businessProperties: {}
      },

      asyncSettings: {
        saveUrl: '/api/file/user/file/uploadPrivate?useType=2'
      },
      uploaderShow: false,
      matImagePathList: [], // 物料图片
      cadImagePathList: [] // 图纸附加
    }
  },
  computed: {
    readonly() {
      return !this.isEdit
    }
  },
  watch: {
    childData: {
      handler(val) {
        this.ruleForm = val
      },
      deep: true
    },
    'ruleForm.id'() {
      this.initImageList('matImagePathList') // 初始化物料图片列表
      this.initImageList('cadImagePathList') // 初始化图纸附加列表
    }
  },
  mounted() {
    this.dataInit()
    // FIXME: 临时解决一个奇怪的问题
    setTimeout(() => {
      this.uploaderShow = true
    }, 0)
    if (this.childData.id) {
      this.ruleForm = this.childData
      // 处理tab切换时图片获取
      this.initImageList('matImagePathList')
      this.initImageList('cadImagePathList')
    }
    // TODO: 对于锚点高度的计算，需要等待页面渲染完成。后面需要优化此处
    this.$nextTick(() => {
      setTimeout(() => {
        this.getBreakPointList()
      }, 2000)
      this.$el.querySelector('#right-view').addEventListener('scroll', this.handleScroll)
    })
  },
  beforeDestroy() {
    this.$el.querySelector('#right-view').removeEventListener('scroll', this.handleScroll)
  },
  methods: {
    navChange(nav) {
      this.currentNav = nav.name

      this.$el.querySelector(`#${nav.name}`).scrollIntoView()
    },
    dataInit() {
      this.dictTreeGet('UNIT_TYPE', 'unit') // 计量单位 1级
      // this.unitDataGet("UNIT", this.ruleForm.secondMeasureUnitTypeId, "typeId"); // 计量单位 2级
      this.dictTreeGet('MATERIAL_TYPE', 'MATERIAL-TYPE') // 材料类型

      this.unitDataGet('UNIT_WEIGHT', 'WEIGHT') // 重量单位
      this.unitDataGet('UNIT_VOLUME', 'VOLUME') // 体积单位
      this.unitDataGet('UNIT_SIZE', 'LENGTH') // 尺寸单位

      this.dictTreeGet('UNIT_ROUND_TYPE', 'ROUNDING-TYPE') // 舍入方式

      this.unitDataGet('UNIT_TIME', 'TIME') // 时间单位
    },
    matSuccess(e) {
      const { fileName, id: fileId } = e.data
      const data = {
        fileName,
        fileId,
        dataType: 'image',
        itemId: this.ruleForm.id
      }

      this.$API.material.imageAdd(data).then(() => {
        this.initImageList('matImagePathList')
      })
    },
    cadSuccess(e) {
      const { fileName, id: fileId } = e.data
      const data = {
        fileName,
        fileId,
        dataType: 'develop',
        itemId: this.ruleForm.id
      }

      this.$API.material.imageAdd(data).then(() => {
        this.initImageList('cadImagePathList')
      })
    },
    dataEmit() {
      return this.ruleForm
    },

    // 获取文件id
    initImageList(whichList) {
      const type = {
        matImagePathList: 'image',
        cadImagePathList: 'develop'
      }
      const query = {
        itemId: this.ruleForm.id,
        dataType: type[whichList]
      }
      this.$API.material.imageQuery(query).then((res) => {
        const ids = res.data.map((e) => e.fileId)
        if (ids.length) {
          this.getImagePath(ids, whichList, res.data)
        } else {
          this[whichList] = []
        }
      })
    },
    // 文件服务 根据文件id获取路径
    getImagePath(ids, whichList, originData) {
      const query = { ids }

      this.$API.material.imagePath(query).then((res) => {
        const ids = Object.keys(res.data) || []
        this[whichList] = ids.map((id) => {
          const item = originData.filter((e) => e.fileId === id)
          return {
            id,
            filePath: res.data?.[id],
            fileId: item[0].id
          }
        })
      })
    },
    fileDel(file, whichList) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除图片？')
        },
        success: () => {
          this.$API.itemManagement.imageDelete({ id: file.fileId }).then(() => {
            this.initImageList(whichList)
          })
        }
      })
    },
    getBreakPointList() {
      const ctxHeightList = this.navList.map((nav) => {
        return this.$el.querySelector(`#${nav.name}`).offsetHeight
      })
      this.breakPointList = ctxHeightList.map((h, i) => {
        let toTop = 0
        for (let j = 0; j < i; j++) {
          toTop += ctxHeightList[j]
        }
        return toTop
      })
    },
    handleScroll() {
      const length = this.$el.querySelector('#right-view').scrollTop
      const index = this.breakPointList.findIndex((e) => {
        return e > length
      })
      this.currentNav = this.navList[index - 1].name
    },
    unitTypeChange(e) {
      this.unitDataGet('UNIT', e.value, 'typeId')
    },
    dictTreeGet(option, dictCode) {
      const query = { dictCode }
      this.$API.dict.TenantDictTreeGet(query).then((res) => {
        this.options[option] = res.data
      })
    },
    dictDataGet(option, dictCode) {
      const query = { dictCode }
      this.$API.dict.TenantDictTypeGet(query).then((res) => {
        this.options[option] = res.data
      })
    },
    unitDataGet(option, value = '', field = 'typeCode') {
      const query = {}
      query[field] = value

      this.$API.dict.unitDataGet(query).then((res) => {
        this.options[option] = res.data
      })
    },
    groupTypeGet(option, groupTypeCode) {
      const query = { groupTypeCode }
      this.$API.base.groupGetNoPage(query).then((res) => {
        this.options[option] = res.data
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.left {
  width: 147px;
  border-right: 1px solid rgba(232, 232, 232, 1);
  height: 100%;
  overflow: auto;
  &-nav {
    width: 96px;
    height: 50px;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(154, 154, 154, 1);
    padding: 18px 10px;
    .active {
      font-size: 14px;
      font-weight: 500;
      color: rgba(41, 41, 41, 1);
      border-left: 3px solid rgba(0, 70, 156, 1);
      border-radius: 2px;
    }
  }
}
.right {
  width: calc(100% - 147px);
  height: 100%;
  overflow: auto;
  &-pannel {
    border-bottom: 1px solid rgba(232, 232, 232, 1);
    .subtitle {
      font-size: 16px;
      font-family: PingFangSC;
      font-weight: 600;
      color: rgba(41, 41, 41, 1);
      border-radius: 2px 0 0 2px;
      border-left: 3px solid rgba(0, 70, 156, 1);
    }
    &--file {
      width: 100%;
      overflow-x: auto;
      &__preview {
        width: 148px;
        height: 148px;
      }
    }
  }

  /deep/ .label {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: normal;
    margin-bottom: 10px;
  }

  .col-height-74 {
    height: 74px;
  }
}
</style>
