<template>
  <div>
    <div style="display: flex; height: 100%">
      <div style="padding: 0 20px 500px 20px; overflow: auto">
        <div
          v-for="(item, index) in dataSources"
          style="padding: 15px 0; cursor: pointer"
          :key="index"
          @click="goAnchor(index)"
        >
          <span style="padding: 0 15px; color: #9a9a9a" :class="{ active: activeBtn === index }">{{
            item.text
          }}</span>
        </div>
      </div>
      <div id="box" style="padding: 20px; overflow: auto; flex: 1; position: relative">
        <div style="padding-bottom: 700px; position: absolute; top: 0">
          <div id="key0">
            <div style="text-align: right; padding: 0 20px; color: #00469c">
              <span style="cursor: pointer" @click="holds">{{ text }}</span>
            </div>
            <div style="padding: 30px 0">
              <div class="icon"></div>
              <div class="title">{{ $t('基础信息') }}</div>
            </div>
            <div>
              <mt-form ref="form" :model="form" :rules="rules">
                <div class="div">
                  <mt-form-item prop="cloudItemCode" :label="$t('云平台编码')">
                    <mt-input
                      v-model="form.cloudItemCode"
                      :show-clear-button="true"
                      :disabled="disabled"
                      type="text"
                      :placeholder="$t('请输入云平台编码')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="unspscCode" :label="$t('UNSPSC编号')">
                    <mt-input
                      v-model="form.unspscCode"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入UNSPSC编号')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="unspscName" :label="$t('UNSPSC名称')">
                    <mt-input
                      v-model="form.unspscName"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入UNSPSC名称')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="secondMeasureUnitId" :label="$t('双计量单位')">
                    <div style="display: flex">
                      <mt-select
                        v-model="form.secondMeasureUnitTypeId"
                        :data-source="defaultValueListOne"
                        :disabled="disabled"
                        :fields="{ text: 'name', value: 'id' }"
                        :show-clear-button="true"
                        @change="companyList"
                      ></mt-select>
                      <mt-select
                        v-model="form.secondMeasureUnitId"
                        :data-source="defaultValueListTwo"
                        :disabled="disabled"
                        :fields="{ text: 'unitName', value: 'id' }"
                        :show-clear-button="true"
                        @change="companyLists"
                      ></mt-select>
                    </div>
                  </mt-form-item>
                </div>
                <div class="div">
                  <mt-form-item prop="materialTypeId" :label="$t('材料类型')">
                    <mt-select
                      v-model="form.materialTypeId"
                      :data-source="MATERIAL"
                      :disabled="disabled"
                      :fields="{ text: 'name', value: 'id' }"
                      :show-clear-button="true"
                      :placeholder="$t('请输入材料类型')"
                      @change="change($event, 'materialTypeName')"
                    ></mt-select>
                  </mt-form-item>
                  <mt-form-item prop="manufacturerName" :label="$t('制造商')">
                    <mt-input
                      v-model="form.manufacturerName"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入制造商')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="manufacturerItemCode" :label="$t('制造商物料编码')">
                    <mt-input
                      v-model="form.manufacturerItemCode"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入制造商物料编码')"
                    ></mt-input>
                  </mt-form-item>
                  <div class="mt-form-item"></div>
                </div>
                <div class="div">
                  <mt-form-item prop="createUserName" :label="$t('创建人')">
                    <mt-input
                      v-model="form.createUserName"
                      :show-clear-button="true"
                      :disabled="true"
                      type="text"
                      :placeholder="$t('请输入创建人')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="createTime" :label="$t('创建时间')">
                    <mt-input
                      v-model="form.createTime"
                      :show-clear-button="true"
                      :disabled="true"
                      type="text"
                      :placeholder="$t('请输入创建时间')"
                    ></mt-input>
                  </mt-form-item>
                  <div class="mt-form-item"></div>
                  <div class="mt-form-item"></div>
                </div>
                <div class="div">
                  <mt-form-item :label="$t('物料图片')">
                    <div
                      style="padding: 10px 0; display: flex; flex-wrap: wrap; flex-direction: row"
                    >
                      <div
                        style="width: 160px; height: 160px; margin: 10px; position: relative"
                        v-for="(item, index) in imageList"
                        :key="index"
                      >
                        <span
                          v-if="!disabled"
                          @click.stop="imagedel(item)"
                          style="position: absolute; right: 5px; top: 5px"
                        >
                          <i class="mt-icons mt-icon-icon_Close_1"></i>
                        </span>
                        <img style="width: 100%; height: 100%" :src="item.url" alt="" />
                      </div>
                      <mt-uploader
                        v-if="!disabled"
                        :async-settings="asyncSettings"
                        name="UploadFiles"
                        :uploading="uploading"
                        @success="success"
                      >
                        <div class="uploader-spu mt-py-20">
                          <div class="uploader-spu--icon">
                            <mt-icon name="icon_Department" />
                          </div>
                          <div class="uploader-spu--remark">
                            {{ $t('请拖拽图片或点击上传') }}
                          </div>
                          <div class="uploader-spu--tips">
                            {{ $t('注：图片格式支持（.jpg .png）') }}
                          </div>
                        </div>
                      </mt-uploader>

                      <!-- <image-preview v-else @del="delSpuImage"></image-preview> -->
                    </div>
                  </mt-form-item>
                </div>
              </mt-form>
            </div>
          </div>
          <div class="hr"></div>
          <div id="key1">
            <div style="padding: 30px 0">
              <div class="icon"></div>
              <div class="title">{{ $t('特性') }}</div>
            </div>
            <div>
              <div class="flex">
                <div class="mt-pa-10 custom-btn" @click="spAttrsAdd('dataGrid', 'dataSpAttrs')">
                  <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('新增') }}
                </div>
                <div
                  class="mt-pa-10 custom-btn"
                  @click="spAttrsDel('dataGrid', 'dataSpAttrs', 'texingDelete')"
                >
                  <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('删除') }}
                </div>
                <div
                  class="mt-pa-10 custom-btn"
                  @click="texinAdd('dataGrid', 'dataSpAttrs', 'texingedit')"
                >
                  <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('保存') }}
                </div>
                <!-- <div
                class="mt-pa-10 custom-btn"
                @click="texinDel('dataGrid', 'dataSpAttrs')"
              >
                <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />取消
              </div> -->
              </div>
              <mt-data-grid
                v-if="columnsSpAttrs"
                id="dataGrid1"
                ref="dataGrid"
                :data-source="dataSpAttrs"
                :edit-settings="{
                  allowEditing: true,
                  allowAdding: true,
                  allowDeleting: true,
                  mode: 'Batch'
                }"
                :selection-settings="{ type: 'Multiple' }"
                :column-data="columnsSpAttrs"
              ></mt-data-grid>
            </div>
          </div>
          <div class="hr"></div>
          <div id="key2">
            <div style="padding: 30px 0">
              <div class="icon"></div>
              <div class="title">{{ $t('量纲') }}</div>
            </div>
            <div>
              <mt-form ref="form" :model="form" :rules="rules">
                <div class="div">
                  <mt-form-item prop="grossWeight" :label="$t('毛重')">
                    <mt-inputNumber
                      v-model.number="form.grossWeight"
                      :show-clear-button="true"
                      :disabled="disabled"
                      :show-spin-button="false"
                      :placeholder="$t('请输入毛重')"
                    ></mt-inputNumber>
                  </mt-form-item>
                  <mt-form-item prop="netWeight" :label="$t('净重')">
                    <mt-inputNumber
                      v-model.number="form.netWeight"
                      :disabled="disabled"
                      :show-clear-button="true"
                      :show-spin-button="false"
                      :placeholder="$t('请输入净重')"
                    ></mt-inputNumber>
                  </mt-form-item>
                  <mt-form-item prop="weightUnitId" :label="$t('重量单位')">
                    <mt-select
                      v-model="form.weightUnitId"
                      :data-source="weight"
                      :disabled="disabled"
                      :fields="{ text: 'unitName', value: 'id' }"
                      :show-clear-button="true"
                      :placeholder="$t('请输入重量单位')"
                      @change="change($event, 'weightUnitName')"
                    ></mt-select>
                  </mt-form-item>
                  <mt-form-item prop="dimensionName" :label="$t('量纲')">
                    <mt-inputNumber
                      v-model="form.dimensionName"
                      :disabled="disabled"
                      :show-clear-button="true"
                      :show-spin-button="false"
                      :placeholder="$t('请输入量纲')"
                    ></mt-inputNumber>
                  </mt-form-item>
                </div>
                <div class="div">
                  <mt-form-item prop="volume" :label="$t('体积')">
                    <mt-inputNumber
                      v-model.number="form.volume"
                      :disabled="disabled"
                      :show-clear-button="true"
                      :show-spin-button="false"
                      :placeholder="$t('请输入体积')"
                    ></mt-inputNumber>
                  </mt-form-item>
                  <mt-form-item prop="volumeUnitId" :label="$t('体积单位')">
                    <mt-select
                      v-model="form.volumeUnitId"
                      :data-source="volume"
                      :disabled="disabled"
                      :fields="{ text: 'unitName', value: 'id' }"
                      :show-clear-button="true"
                      :placeholder="$t('请输入体积单位')"
                      @change="change($event, 'volumeUnitName')"
                    ></mt-select>
                  </mt-form-item>
                  <mt-form-item prop="itemLength" :label="$t('长度')">
                    <mt-inputNumber
                      v-model.number="form.itemLength"
                      :disabled="disabled"
                      :show-clear-button="true"
                      :show-spin-button="false"
                      :placeholder="$t('请输入长度')"
                    ></mt-inputNumber>
                  </mt-form-item>
                  <mt-form-item prop="itemWidth" :label="$t('宽度')">
                    <mt-inputNumber
                      v-model.number="form.itemWidth"
                      :disabled="disabled"
                      :show-clear-button="true"
                      :show-spin-button="false"
                      :placeholder="$t('请输入宽度')"
                    ></mt-inputNumber>
                  </mt-form-item>
                </div>
                <div class="div">
                  <mt-form-item prop="itemHeight" :label="$t('高度')">
                    <mt-inputNumber
                      v-model.number="form.itemHeight"
                      :disabled="disabled"
                      :show-clear-button="true"
                      :show-spin-button="false"
                      :placeholder="$t('请输入高度')"
                    ></mt-inputNumber>
                  </mt-form-item>
                  <mt-form-item prop="sizeUnitId" :label="$t('长宽高尺寸单位')">
                    <mt-select
                      v-model="form.sizeUnitId"
                      :data-source="size"
                      :disabled="disabled"
                      :fields="{ text: 'unitName', value: 'id' }"
                      :show-clear-button="true"
                      :placeholder="$t('请输入长宽高尺寸单位')"
                      @change="change($event, 'sizeUnitName')"
                    ></mt-select>
                  </mt-form-item>
                  <div class="mt-form-item"></div>
                  <div class="mt-form-item"></div>
                </div>
              </mt-form>
            </div>
          </div>
          <div class="hr"></div>
          <div id="key3">
            <div style="padding: 30px 0">
              <div class="icon"></div>
              <div class="title">{{ $t('计量单位') }}</div>
            </div>
            <div>
              <div class="flex">
                <div class="mt-pa-10 custom-btn" @click="spAttrsAdd('danwei', 'danweiList')">
                  <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('新增') }}
                </div>
                <div
                  class="mt-pa-10 custom-btn"
                  @click="spAttrsDel('danwei', 'danweiList', 'unitdelete')"
                >
                  <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('删除') }}
                </div>
                <div
                  class="mt-pa-10 custom-btn"
                  @click="texinAdd('danwei', 'danweiList', 'unitadd')"
                >
                  <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('保存') }}
                </div>
                <!-- <div
                class="mt-pa-10 custom-btn"
                @click="texinDel('danwei', 'danweiList')"
              >
                <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />取消
              </div> -->
              </div>
              <mt-data-grid
                v-if="danweiArr"
                id="danwei1"
                ref="danwei"
                :data-source="danweiList"
                :edit-settings="{
                  allowEditing: true,
                  allowAdding: true,
                  allowDeleting: true,
                  mode: 'Batch'
                }"
                :selection-settings="{ type: 'Multiple' }"
                :column-data="danweiArr"
              ></mt-data-grid>
            </div>
          </div>
          <div class="hr"></div>
          <div id="key4">
            <div style="padding: 30px 0">
              <div class="icon"></div>
              <div class="title">{{ $t('品类') }}</div>
            </div>
            <div>
              <div class="flex">
                <div class="mt-pa-10 custom-btn" @click="pinleiadd">
                  <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('新增') }}
                </div>
                <div
                  class="mt-pa-10 custom-btn"
                  @click="spAttrsDel('pinlei', 'dataSourceList', 'pinleidelete')"
                >
                  <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('删除') }}
                </div>
              </div>
              <mt-data-grid
                v-if="pinleiArr"
                id="pinlei1"
                ref="pinlei"
                :data-source="dataSourceList"
                :column-data="pinleiArr"
              ></mt-data-grid>
            </div>
          </div>
          <div class="hr"></div>
          <div id="key5">
            <div style="padding: 30px 0">
              <div class="icon"></div>
              <div class="title">{{ $t('舍入') }}</div>
            </div>
            <div>
              <mt-form ref="form" :model="form" :rules="rules">
                <div class="div">
                  <mt-form-item prop="roundTypeId" :label="$t('舍入方式')">
                    <mt-select
                      v-model="form.roundTypeId"
                      :disabled="disabled"
                      :data-source="ROUNDING"
                      :fields="{ text: 'name', value: 'id' }"
                      :show-clear-button="true"
                      :placeholder="$t('请选择舍入方式')"
                      @change="change($event, 'roundTypeName')"
                    ></mt-select>
                  </mt-form-item>
                  <mt-form-item prop="roundLength" :label="$t('舍入位数')">
                    <mt-input-number
                      v-model="form.roundLength"
                      :disabled="disabled"
                      :min="min"
                    ></mt-input-number>
                  </mt-form-item>
                  <div class="mt-form-item"></div>
                  <div class="mt-form-item"></div>
                </div>
              </mt-form>
            </div>
          </div>
          <div class="hr"></div>
          <div id="key6">
            <div style="padding: 30px 0">
              <div class="icon"></div>
              <div class="title">{{ $t('研发') }}</div>
            </div>
            <div>
              <mt-form ref="form" :model="development" :rules="rules">
                <div class="div">
                  <mt-form-item prop="dsdToleranceType" :label="$t('DSD附加公差类型')">
                    <mt-input
                      v-model="development.dsdToleranceType"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入DSD附加公差类型')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="toleranceDsdStructure" :label="$t('公差类型的DSD结构')">
                    <mt-input
                      v-model="development.toleranceDsdStructure"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入公差类型的DSD结构')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="versionNumber" :label="$t('版本变更号')">
                    <mt-input
                      v-model="development.versionNumber"
                      :disabled="true"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入版本变更号')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="graphNumber" :label="$t('图号')">
                    <mt-input
                      v-model="development.graphNumber"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入图号')"
                    ></mt-input>
                  </mt-form-item>
                </div>
              </mt-form>
              <div style="padding: 10px">{{ $t('图纸附加') }}</div>
              <div style="padding: 10px 0; display: flex; flex-wrap: wrap; flex-direction: row">
                <div
                  style="width: 160px; height: 160px; margin: 10px; position: relative"
                  v-for="(item, index) in developList"
                  :key="index"
                >
                  <span
                    v-if="!disabled"
                    @click.stop="imagedel(item)"
                    style="position: absolute; right: 5px; top: 5px"
                  >
                    <i class="mt-icons mt-icon-icon_Close_1"></i>
                  </span>
                  <img style="width: 100%; height: 100%" :src="item.url" alt="" />
                </div>
                <mt-uploader
                  v-if="!disabled"
                  :async-settings="asyncSettings"
                  name="UploadFiles"
                  :uploading="uploading"
                  @success="success1"
                >
                  <div class="uploader-spu mt-py-20">
                    <div class="uploader-spu--icon">
                      <mt-icon name="icon_Department" />
                    </div>
                    <div class="uploader-spu--remark">
                      {{ $t('请拖拽图片或点击上传') }}
                    </div>
                    <div class="uploader-spu--tips">
                      {{ $t('注：图片格式支持（.jpg .png）') }}
                    </div>
                  </div>
                </mt-uploader>
                <!-- imagedel -->
                <!-- <image-preview v-else @del="delSpuImage"></image-preview> -->
              </div>
            </div>
          </div>
          <div class="hr"></div>
          <div id="key7">
            <div style="padding: 30px 0">
              <div class="icon"></div>
              <div class="title">{{ $t('运输') }}</div>
            </div>
            <div>
              <mt-form ref="form" :model="transport" :rules="rules">
                <div class="div">
                  <mt-form-item prop="packageDemand" :label="$t('包装方式')">
                    <mt-input
                      v-model="transport.packageDemand"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="storageDemand" :label="$t('贮藏要求')">
                    <mt-input
                      v-model="transport.storageDemand"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="allowPackageQuantity" :label="$t('允许包装量')">
                    <mt-input
                      v-model="transport.allowPackageQuantity"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="allowPackageWeight" :label="$t('允许包装重量')">
                    <mt-input
                      v-model.number="transport.allowPackageWeight"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                </div>
                <div class="div">
                  <mt-form-item prop="allowPackageWeightUnitId" :label="$t('允许包装重量单位')">
                    <mt-select
                      v-model="transport.allowPackageWeightUnitId"
                      :data-source="allowPackageWeight"
                      :disabled="disabled"
                      :fields="{ text: 'unitName', value: 'id' }"
                      :show-clear-button="true"
                      :placeholder="$t('请输入')"
                      @change="change($event, 'allowPackageWeightUnitName')"
                    ></mt-select>
                  </mt-form-item>
                  <mt-form-item prop="packageMaxHeight" :label="$t('包装最大高度')">
                    <mt-input
                      v-model="form.packageMaxHeight"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="packageMaxHeightUnitId" :label="$t('包装最大高度单位')">
                    <mt-select
                      v-model="form.packageMaxHeightUnitId"
                      :data-source="packageMaxHeight"
                      :disabled="disabled"
                      :fields="{ text: 'unitName', value: 'id' }"
                      :show-clear-button="true"
                      :placeholder="$t('请输入')"
                      @change="change($event, 'packageMaxHeightUnitName')"
                    ></mt-select>
                  </mt-form-item>
                  <mt-form-item prop="packageMaxCapacityUnitId" :label="$t('包装允许最大单位')">
                    <mt-select
                      v-model="form.packageMaxCapacityUnitId"
                      :data-source="packageMaxCapacity"
                      :disabled="disabled"
                      :fields="{ text: 'unitName', value: 'id' }"
                      :show-clear-button="true"
                      :placeholder="$t('请输入')"
                      @change="change($event, 'packageMaxCapacityUnitName')"
                    ></mt-select>
                  </mt-form-item>
                </div>
                <div class="div">
                  <mt-form-item prop="transportMaterialType" :label="$t('运输材料类型')">
                    <mt-input
                      v-model="transport.MaterialType"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="airtightTransport" :label="$t('运输时密闭')">
                    <mt-radio
                      v-model="transport.airtightTransport"
                      :disabled="disabled"
                      :data-source="airtightTransportList"
                    ></mt-radio>
                  </mt-form-item>
                  <mt-form-item prop="packingLevel" :label="$t('填充水平')">
                    <mt-input
                      v-model="transport.packingLevel"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="allowStacking" :label="$t('允许堆垛')">
                    <mt-radio
                      v-model.number="transport.allowStacking"
                      :disabled="disabled"
                      :data-source="allowStackingList"
                    ></mt-radio>
                  </mt-form-item>
                </div>
                <div class="div">
                  <mt-form-item prop="stackingLevel" :label="$t('堆垛系数')">
                    <mt-input
                      v-model="transport.stackingLevel"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="totalShelfLife" :label="$t('总保质期')">
                    <mt-input
                      v-model="transport.totalShelfLife"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="remainingShelfLife" :label="$t('剩余保质期')">
                    <mt-input
                      v-model="transport.remainingShelfLife"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="storageShelfLife" :label="$t('储存保质期')">
                    <mt-input
                      v-model="transport.storageShelfLife"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                </div>
                <div class="div">
                  <mt-form-item prop="shelfLifeUnitId" :label="$t('保质期时间单位')">
                    <mt-select
                      v-model="transport.shelfLifeUnitId"
                      :data-source="shelfLife"
                      :disabled="disabled"
                      :fields="{ text: 'unitName', value: 'id' }"
                      :show-clear-button="true"
                      :placeholder="$t('请输入')"
                      @change="change($event, 'shelfLifeUnitName')"
                    ></mt-select>
                  </mt-form-item>
                  <mt-form-item prop="bottomFloorQuantity" :label="$t('底层数')">
                    <mt-input
                      v-model="transport.bottomFloorQuantity"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="topFloorQuantity" :label="$t('顶层数')">
                    <mt-input
                      v-model="transport.topFloorQuantity"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="stackingFactor" :label="$t('堆叠因子')">
                    <mt-input
                      v-model="transport.stackingFactor"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                </div>
                <div class="div">
                  <mt-form-item prop="loadWeight" :label="$t('负载')">
                    <mt-input
                      v-model="transport.loadWeight"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="packingMaterialAllowDeep" :label="$t('包装材料允许深度')">
                    <mt-input
                      v-model="transport.packingMaterialAllowDeep"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="packingMaterialAllowWidth" :label="$t('包装材料允许宽度')">
                    <mt-input
                      v-model="transport.packingMaterialAllowWidth"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="packingMaterialMaxHeight" :label="$t('包装材料最大堆放高度')">
                    <mt-input
                      v-model="transport.packingMaterialMaxHeight"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                </div>
                <div class="div">
                  <mt-form-item prop="packingMaterialMinHeight" :label="$t('包装材料最小堆放高度')">
                    <mt-input
                      v-model="transport.packingMaterialMinHeight"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item
                    prop="toleranceOverMaxValueHeight"
                    :label="$t('公差超过最大值') + $t('堆高（VSO）')"
                  >
                    <mt-input
                      v-model="transport.toleranceOverMaxValueHeight"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item
                    prop="airtightPkmMaterialQuantity"
                    :label="$t('每个封闭PKM（VSO）的材料数量')"
                  >
                    <mt-input
                      v-model="transport.airtightPkmMaterialQuantity"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item
                    prop="vehicleSpaceOptimizationUnit"
                    :label="$t('度量单位的车辆空间优化')"
                  >
                    <mt-input
                      v-model="transport.vehicleSpaceOptimizationUnit"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                </div>
                <div class="div">
                  <mt-form-item
                    prop="airtightPackingMaterialDemand"
                    :label="$t('要求的封闭包装材料（VSO）')"
                  >
                    <mt-input
                      v-model="transport.airtightPackingMaterialDemand"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="returnCode" :label="$t('返回码')">
                    <mt-input
                      v-model="transport.returnCode"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="returnLogisticsLevel" :label="$t('返回物流级别')">
                    <mt-input
                      v-model="transport.returnLogisticsLevel"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <div class="mt-form-item"></div>
                </div>
              </mt-form>
            </div>
          </div>
          <div class="hr"></div>
          <div id="key8">
            <div style="padding: 30px 0">
              <div class="icon"></div>
              <div class="title">{{ $t('行业属性') }}</div>
            </div>
            <div>
              <mt-form ref="form" :model="businessProperties" :rules="rules">
                <div class="div">
                  <mt-form-item prop="highViscosity" :label="$t('高粘度')">
                    <mt-input
                      v-model="businessProperties.highViscosity"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="bulkOrLiquid" :label="$t('散装/液体')">
                    <mt-input
                      v-model="businessProperties.bulkOrLiquid"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="variantColorFeature" :label="$t('变体颜色的特征值')">
                    <mt-input
                      v-model="businessProperties.variantColorFeature"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="variantSizeFeature" :label="$t('变体主要大小的特征值')">
                    <mt-input
                      v-model="businessProperties.variantSizeFeature"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                </div>
                <div class="div">
                  <mt-form-item prop="variantSecondSizeFeature" :label="$t('变体第二尺寸的特征值')">
                    <mt-input
                      v-model="businessProperties.variantSecondSizeFeature"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="featureForEvaluate" :label="$t('用于评估目的的特征值')">
                    <mt-input
                      v-model="businessProperties.featureForEvaluate"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="maintenanceCode" :label="$t('保养代码')">
                    <mt-input
                      v-model="businessProperties.maintenanceCode"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item
                    prop="lowspeedSalerManagement"
                    :label="$t('慢速销售者管理：时尚程度')"
                  >
                    <mt-input
                      v-model="businessProperties.lowspeedSalerManagement"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                </div>
                <div class="div">
                  <mt-form-item prop="fashionLevel" :label="$t('时尚等级')">
                    <mt-input
                      v-model="businessProperties.fashionLevel"
                      :disabled="disabled"
                      :show-clear-button="true"
                      type="text"
                      :placeholder="$t('请输入')"
                    ></mt-input>
                  </mt-form-item>
                  <div class="mt-form-item"></div>
                  <div class="mt-form-item"></div>
                  <div class="mt-form-item"></div>
                </div>
              </mt-form>
            </div>
          </div>
        </div>
      </div>
    </div>
    <mt-dialog
      css-class="create-proj-dialog"
      ref="pinleiShow"
      :header="$t('新增')"
      :buttons="buttonsUser"
    >
      <mt-form ref="ruleForm" :model="ruleForm">
        <mt-form-item prop="organizationId" :label="$t('组织名称')">
          <mt-DropDownTree
            id="filter"
            :fields="fields"
            :placeholder="$t('请选择组织')"
            @select="vaulechange"
          ></mt-DropDownTree>
        </mt-form-item>
        <mt-form-item prop="type" :label="$t('品类种类')">
          <mt-select
            :data-source="dataArr"
            :fields="{ text: 'typeName', value: 'id' }"
            :show-clear-button="true"
            :placeholder="$t('请选择品类种类')"
            @select="dataArrSelect"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="categoryId" :label="$t('品类')">
          <mt-DropDownTree
            id="category"
            :fields="categoryfields"
            :placeholder="$t('请选择品类')"
            @select="categorychange"
          ></mt-DropDownTree>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import { dataSources, COLUMNS_SP_ATTRS, pageConfigs, classConfigs } from '../../data/details.js'
// import ImagePreview from "@/components/common/ImagePreview.vue";
import { utils } from '@mtech-common/utils'
import { HEADER_TOKEN_KEY, getToken } from '@mtech-sso/single-sign-on'
export default {
  components: {
    // ImagePreview,
  },
  name: 'BasicNews',
  props: {
    form: {
      type: Object,
      default: () => {}
    },
    rules: {
      type: Object,
      default: () => {}
    },
    development: {
      type: Object,
      default: () => {}
    },
    businessProperties: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: 'preview'
    },
    transport: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dataSourceList: [],
      dataArr: [],
      min: 0,
      categoryfields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      fields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      editing: {
        allowDeleting: true,
        allowEditing: true,
        allowAdding: true
      },
      ruleForm: {
        organizationId: '',
        categoryId: '',
        type: ''
      },
      asyncSettings: {
        saveUrl: '/api/file/user/file/uploadPrivate?useType=2'
      },
      // useType=develop
      dataSources: dataSources,
      airtightTransportList: [
        {
          label: this.$t('密闭'),
          value: '1'
        },
        {
          label: this.$t('不密闭'),
          value: '0'
        }
      ],
      allowStackingList: [
        {
          label: this.$t('允许'),
          value: 1
        },
        {
          label: this.$t('不允许'),
          value: 0
        }
      ],
      edit: this.type !== 'preview',
      classConfigs: classConfigs,
      defaultValueList: [],
      defaultValueListOne: [],
      defaultValueListTwo: [],
      ROUNDING: [],
      MATERIAL: [],
      weight: [],
      volume: [],
      size: [],
      allowPackageWeight: [],
      packageMaxHeight: [],
      packageMaxCapacity: [],
      shelfLife: [],
      activeBtn: 0,
      danweiArr: [],
      danweiList: [],

      pinleiArr: [],
      columnsSpAttrs: null,
      dataSpAttrs: [],
      arr: [
        'danweiArr',
        'columnsSpAttrs',
        'weight',
        'MATERIAL',
        'ROUNDING',
        'volume',
        'size',
        'allowPackageWeight',
        'packageMaxHeight',
        'packageMaxCapacity',
        'shelfLife'
      ],
      buttonsUser: [
        {
          click: this.cancelUser,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.saveUser,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      numArr: [],
      developList: [],
      imageList: []
    }
  },
  computed: {
    text() {
      if (this.edit) return this.$t('保存')
      return this.$t('编辑')
    },
    disabled() {
      return !this.edit
    },
    headerTitle() {
      if (this.type === 'add') {
        return this.$t('新增')
      }
      if (this.type === 'edit') {
        return this.$t('编辑')
      }
      return this.$t('预览')
    }
  },
  created() {
    this.companyList()
    this.promiseAll()
    this.init()
    this.imageInit()
    // this.roundingList();
    // this.materialList();
    // this.weightList();
    //   "dictCode": "MATERIAL-TYPE"
  },
  mounted() {
    this.$el.querySelector('#box').addEventListener('scroll', this.handleScroll)
    for (let i = 0; i < 8; i++) {
      let height = this.numArr[this.numArr.length - 1] || 0
      height += this.$el.querySelector('#key' + i).offsetHeight
      this.numArr.push(height)
    }
  },
  methods: {
    imagedel(item) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除图片？')
        },
        success: () => {
          this.$API.itemManagement.imageDelete({ id: item.id }).then(() => {
            this.imageInit()
          })
        }
      })
    },
    delSpuImage() {},
    imageInit() {
      this.imageList = []
      this.developList = []
      this.$API.itemManagement
        .imageQuery({ itemId: this.form.id, dataType: 'image' })
        .then((res) => {
          //  this.imageList=res.data||[]imagedel
          let ids = res.data.map((item) => {
            return item.fileId
          })
          let id = res.data.map((item) => {
            return { id: item.id, fid: item.fileId }
          })
          if (ids.length <= 0) {
            return
          }
          this.$API.itemManagement.imagePath({ ids }).then((_res) => {
            //  console.log(_res.data)
            for (const iterator in _res.data) {
              id.forEach((ele) => {
                if (ele.fid == iterator) {
                  this.imageList.push({ ...ele, url: _res.data[iterator] })
                }
              })
            }
            //  _res.data.forEach((ele,index)=>{
            //    console.log(ele)
            //    this.imageList.push({url:ele,id:index})
            //  })
            //  this.imageList=_res.data
          })
        })
      this.$API.itemManagement
        .imageQuery({ itemId: this.form.id, dataType: 'develop' })
        .then((res) => {
          //  this.developList=res.data||[]
          let ids = res.data.map((item) => {
            return item.fileId
          })
          let id = res.data.map((item) => {
            return { id: item.id, fid: item.fileId }
          })
          if (ids.length <= 0) {
            return
          }
          this.$API.itemManagement.imagePath({ ids }).then((_res) => {
            //  console.log(_res)
            for (const iterator in _res.data) {
              id.forEach((ele) => {
                if (ele.fid == iterator) {
                  this.developList.push({ ...ele, url: _res.data[iterator] })
                }
              })
            }
            //  _res.data.forEach((ele,index)=>{
            //    this.developList.push({url:_res.data[iterator],id:iterator})
            //  })
            //  this.developList=_res.data
          })
        })
    },
    init() {
      this.$API.itemManagement.texinglist({ id: this.form.id }).then((res) => {
        console.log(res)
        this.dataSpAttrs = res.data
      })
      this.$API.itemManagement.pinleilist(`itemId=${this.form.id}`).then((res) => {
        console.log(11111, res.data)
        this.dataSourceList = res.data
        this.pinleiArr = classConfigs()
        console.log(this.dataSourceList)
      })
      this.$API.itemManagement.danweilist({ id: this.form.id }).then((res) => {
        // console.log(res)
        this.danweiList = res.data.map((item) => {
          if (item.fixRatio == 1) {
            return { ...item, fixRationame: this.$t('是') }
          } else {
            return { ...item, fixRationame: this.$t('否') }
          }
        })
      })
    },
    categoryfn() {
      let obj = {
        categoryTypeId: this.ruleForm.type,
        organizationId: this.ruleForm.organizationId
      }
      this.$API.itemManagement.getTree(obj).then((res) => {
        console.log(res)
        let obj = {
          dataSource: res.data,
          value: 'id',
          text: 'name',
          child: 'children'
        }
        this.$set(this, 'categoryfields', obj)
      })
    },
    categorychange(value, text) {
      console.log('value :', value, 'text:', text)
      console.log(this.ruleForm)
      this.ruleForm.categoryId = value?.itemData?.id || ''
      // categoryId:this.ruleForm.categoryId,
    },
    dataArrSelect(value, text) {
      console.log('value :', value, 'text:', text)
      console.log(this.ruleForm)
      this.ruleForm.type = value?.itemData?.id || ''
      if (this.ruleForm.type && this.ruleForm.organizationId) {
        this.categoryfn()
      }
    },
    vaulechange(value, text) {
      console.log('value :', value, 'text:', text)
      console.log(this.ruleForm)
      this.ruleForm.organizationId = value?.itemData?.id || ''
      if (this.ruleForm.type && this.ruleForm.organizationId) {
        this.categoryfn()
      }
    },
    cancelUser() {
      this.$refs.pinleiShow.ejsRef.hide()
    },
    saveUser() {
      let obj = {
        categoryId: this.ruleForm.categoryId,
        itemId: this.form.id
      }
      this.$API.itemManagement.pinleiAdd(obj).then((res) => {
        console.log(res)
        if (res.code == 200) {
          this.$refs.pinleiShow.ejsRef.hide()
          this.init()
        }
      })

      // this.$refs.pinleiShow.ejsRef.hide();
    },
    pinleiadd() {
      this.$refs.pinleiShow.ejsRef.show()
      this.showFn()
    },
    showFn() {
      this.$API.itemManagement.criteriaQuery({}).then((res) => {
        this.dataArr = res.data
      })
      this.$API.itemManagement.getFuzzyCompanyTree({ fuzzyName: '' }).then((res) => {
        let obj = {
          dataSource: res.data,
          value: 'id',
          text: 'name',
          child: 'children'
        }
        this.$set(this, 'fields', obj)
        // this.fields.dataSources=
      })
    },
    success({ e }) {
      const response = JSON.parse(e?.currentTarget?.response)
      console.log(response)
      const { fileName, id: fileId } = response.data
      const data = {
        fileName,
        fileId,
        dataType: 'image',
        itemId: this.form.id
      }
      this.$API.itemManagement.image(data).then(() => {
        // that.skuImageListGet();
        this.imageInit()
      })
    },
    success1({ e }) {
      const response = JSON.parse(e?.currentTarget?.response)
      const { fileName, id: fileId } = response.data
      const data = {
        fileName,
        fileId,
        dataType: 'develop',
        itemId: this.form.id
      }
      this.$API.itemManagement.image(data).then(() => {
        this.imageInit()
        // that.skuImageListGet();
      })
    },
    uploading(args) {
      args.currentRequest.setRequestHeader(HEADER_TOKEN_KEY, getToken())
    },
    texinAdd(text, data, url) {
      this.gridDataUpdate(text, data)
      console.log(1, this[data])
      this.$API.itemManagement[url](this[data])
        .then((res) => {
          console.log(res)
          this.init()
        })
        .catch(() => {
          this.init()
        })
    },
    texinDel(text) {
      this.$refs[text].ejsRef.deleteRecord()
    },
    spAttrsDel(text, data, url) {
      let arr = this.$refs[text].ejsRef.getSelectedRecords()
      console.log(arr)
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = arr.map((e) => e.id)
          const data = { ids }
          this.$API.itemManagement[url](data).then(() => {
            this.init()
          })
        }
      })
      //
    },
    spAttrsAdd(text) {
      let obj = {}
      if (text == 'danwei') {
        obj = {
          calculateRatio: '',
          fixRatio: '',
          unitId: '',
          unitName: '',
          unitCode: '',
          unitTypeId: '',
          itemId: this.form.id
        }
      } else {
        obj = {
          itemId: this.form.id,
          itemClassificationCode: '',
          itemClassificationName: '',
          itemClassificationTypeCode: '',
          itemClassificationTypeName: '',
          itemClassificationValueNames: '',
          itemClassificationDescription: ''
        }
      }
      console.log(this.$refs[text])
      this.$refs[text].ejsRef.addRecord(obj)
    },
    handleScroll() {
      let scrollTop = this.$el.querySelector('#box').scrollTop
      // console.log(scrollTop, this.numArr);
      if (scrollTop <= this.numArr[0]) {
        // console.log("000>>>>>>>>>");
        this.activeBtn = 0
      } else if (this.numArr[1] > scrollTop && scrollTop > this.numArr[0]) {
        // console.log("111>>>>>>>>>");
        this.activeBtn = 1
      } else if (this.numArr[2] > scrollTop && scrollTop >= this.numArr[1]) {
        // console.log("222>>>>>>>>>");
        this.activeBtn = 2
      } else if (this.numArr[3] > scrollTop && scrollTop >= this.numArr[2]) {
        // console.log("333>>>>>>>>>");
        this.activeBtn = 3
      } else if (this.numArr[4] > scrollTop && scrollTop >= this.numArr[3]) {
        // console.log("444>>>>>>>>>");
        this.activeBtn = 4
      } else if (this.numArr[5] > scrollTop && scrollTop >= this.numArr[4]) {
        // console.log("555>>>>>>>>>");
        this.activeBtn = 5
      } else if (this.numArr[6] > scrollTop && scrollTop >= this.numArr[5]) {
        // console.log("666>>>>>>>>>");
        this.activeBtn = 6
      } else if (this.numArr[7] > scrollTop && scrollTop >= this.numArr[6]) {
        // console.log("777>>>>>>>>>");
        this.activeBtn = 7
      } else if (scrollTop >= this.numArr[7]) {
        // console.log("888>>>>>>>>>");
        this.activeBtn = 8
      }
    },
    goAnchor(index) {
      this.activeBtn = index
      let height = 0
      if (index !== 0) {
        for (let i = 0; i < index; i++) {
          height += this.$el.querySelector('#key' + i).offsetHeight + 100
        }
      }
      this.$el.querySelector('#box').scrollTop = height
      // this.$el.querySelector("#key").style.top = -height + "px";
    },
    promiseAll() {
      let arr = [
        this.$API.itemManagement.unit({}),
        this.$API.itemManagement.itemTreelistOne({
          dictCode: 'ITEM_CLASIFICATION_TYPE'
        }),
        this.$API.itemManagement.itemTreelistTwo({
          typeCode: 'WEIGHT'
        }),
        this.$API.itemManagement.itemTreelistOne({
          dictCode: 'MATERIAL-TYPE'
        }),
        this.$API.itemManagement.itemTreelistOne({
          dictCode: 'ROUNDING-TYPE'
        }),
        this.$API.itemManagement.itemTreelistTwo({
          typeCode: 'VOLUME'
        }),
        this.$API.itemManagement.itemTreelistTwo({
          typeCode: 'LENGTH'
        }),
        this.$API.itemManagement.itemTreelistTwo({
          typeCode: 'WEIGHT'
        }),
        this.$API.itemManagement.itemTreelistTwo({
          typeCode: 'LENGTH'
        }),
        this.$API.itemManagement.itemTreelistTwo({
          typeCode: 'LENGTH'
        }),
        this.$API.itemManagement.itemTreelistTwo({
          typeCode: 'TIME'
        })
      ]
      Promise.all(arr).then((res) => {
        console.log(res)

        res.forEach((ele, index) => {
          if (this.arr[index] == 'columnsSpAttrs') {
            this.columnsSpAttrs = COLUMNS_SP_ATTRS(
              ele.data || [
                {
                  dictName: this.$t('选项1'),
                  dictCode: 'opt1'
                }
              ]
            )
          } else if (this.arr[index] == 'danweiArr') {
            this.danweiArr = pageConfigs(
              ele.data || [
                {
                  dictName: this.$t('选项1'),
                  dictCode: 'opt1'
                }
              ]
            )
          } else {
            this[this.arr[index]] = ele.data
          }
        })
        console.log(res)
      })
    },
    change(value, name) {
      //change
      console.log(value, name)
      this.form[name] = value.itemData.name || value.itemData.unitName
    },
    companyList(value) {
      console.log(value)
      let data = {}
      let str = 'itemTreelistOne'
      if (value) {
        str = 'itemTreelistTwo'
        this.form.secondMeasureUnitTypeName = value.itemData.name
        this.form.secondMeasureUnitTypeCode = value.itemData.itemCode
        this.$set(data, 'typeCode', value.itemData.itemCode)
      } else {
        this.$set(data, 'dictCode', 'unit')
        if (this.form.secondMeasureUnitTypeCode) {
          this.$API.itemManagement
            .itemTreelistTwo({
              typeCode: this.form.secondMeasureUnitTypeCode
            })
            .then((res) => {
              this.$set(this, 'defaultValueListTwo', res.data)
            })
        }
      }
      this.$API.itemManagement[str](data).then((res) => {
        if (value) this.$set(this, 'defaultValueListTwo', res.data)
        else this.$set(this, 'defaultValueListOne', res.data)
      })
    },
    companyLists(e) {
      this.form.secondMeasureUnitName = e.itemData.unitName
      this.form.secondMeasureUnitCode = e.itemData.unitCode
    },
    gridDataUpdate(ref, dataKey) {
      const changes = this.$refs[ref].ejsRef.getBatchChanges()
      console.log('changes', changes)
      // changes事件中数据分为add change delete三种，其中delete和change都只针对原dataSource
      const { addedRecords, deletedRecords, changedRecords } = changes
      let data = utils.cloneDeep(this[dataKey])

      if (deletedRecords.length) {
        deletedRecords.forEach((el) => {
          data = data.filter((e) => e.id !== el.id)
        })
      }
      if (changedRecords.length) {
        changedRecords.forEach((el) => {
          data = data.map((e) => {
            if (el.id === e.id) {
              return el
            }
            return e
          })
        })
      }
      this[dataKey] = utils.cloneDeep(data.concat(addedRecords))
      console.log(this[dataKey])
    },
    holds() {
      //
      // this.gridDataUpdate("dataGrid", "dataSpAttrs")
      if (this.edit) {
        console.log(123, this.form)

        this.$refs.form.validate((valid) => {
          if (valid) {
            this.form.transport = this.transport
            this.form.development = this.development
            this.form.businessProperties = this.businessProperties
            this.$emit('holds', this.form)
            this.edit = false
          }
        })
      } else {
        this.$emit('rulesGet', 'itemUpdataValids')
        this.edit = true
      }
    },
    handleSelectTab(i, item) {
      console.log(i, item)
    },
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const rowSelected = grid?.getSelectedRecords?.()

      if (toolbar.id === 'Add') {
        console.log(e)
        grid.addRecord()
        console.log('新增')
        // this.handleAction("add");
        // this.rulesGet("roleAddValid");
      }
      if (toolbar.id === 'Preserve') {
        console.log(this.$t('保存'), rowSelected)
        console.log(grid)
        console.log(grid.getSelectedRows())
        // let data={}
        // this.$API.itemManagement.classificationAdd(){
        //
        // }
        // this.$refs.classification.refreshCurrentGridData();
      }
      if (toolbar.id === 'Cancel') {
        console.log('取消')
        this.$refs.classification.refreshCurrentGridData()
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDelete(rowSelected)
      }
    },
    handleClickCellTitle(e) {
      console.log(e)
    },
    handleClickCellTool(e) {
      const { data, tool } = e
      console.log(data, tool)
    },
    handleDelete(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.roles.batchDelete(data).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/.mt-input-number {
  height: 30px;
  padding: 0px 10px;
}
.tabs /deep/ .e-tab-header {
  background: rgb(250, 250, 250);
}
.icon {
  width: 3px;
  display: inline-block;
  height: 12px;
  background: rgba(0, 70, 156, 1);
  border-radius: 2px 0 0 2px;
}
.active {
  border-left: 5px solid #00469c;
  color: #292929 !important;
}
.hr {
  margin: 50px 0;
  width: 100%;
  height: 1px;
  border: 1px solid rgba(232, 232, 232, 1);
}
.title {
  display: inline-block;
  margin-left: 2px;
  font-size: 16px;
  font-weight: 600;
  color: rgba(41, 41, 41, 1);
}
.mt-form {
  display: flex;
  flex-wrap: wrap;
  .div {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    .mt-form-item {
      flex: 1;
      margin: 5px 10px;
    }
  }
}

.uploader-spu {
  width: 160px;
  height: 160px;
  background: rgba(251, 252, 253, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  text-align: center;
  &--icon {
    color: rgba(152, 170, 195, 1);
  }
  &--remark {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(152, 170, 195, 1);
    margin-top: 14px;
  }
  &--tips {
    font-size: 12px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(241, 62, 62, 1);
    margin-top: 14px;
  }
}
::v-deep .row-uploader {
  background: rgba(245, 245, 245, 1);
  box-shadow: inset 0 -1px 0 0 rgba(232, 232, 232, 1), inset 1px 0 0 0 rgba(232, 232, 232, 1),
    inset -1px 0 0 0 rgba(232, 232, 232, 1);
  &--icon {
    width: 60px;
    height: 60px;
    line-height: 60px;
    text-align: center;
    background: rgba(251, 251, 251, 1);
    color: rgba(152, 170, 195, 1);
  }
  &--img {
    width: 60px;
    height: 60px;
    background: rgba(251, 251, 251, 1);
  }
}
</style>
