<template>
  <div style="padding-bottom: 500px; height: 100%; overflow: auto">
    <div style="padding: 20px; border-bottom: 3px solid rgb(250, 250, 250)">
      <div style="text-align: right; padding: 0 20px; color: #00469c">
        <span style="cursor: pointer" @click="holds">{{ text }}</span>
      </div>
      <div style="padding: 30px 0">
        <div
          style="
            width: 3px;
            display: inline-block;
            height: 12px;
            background: rgba(0, 70, 156, 1);
            border-radius: 2px 0 0 2px;
          "
        ></div>
        <div
          style="
            display: inline-block;
            margin-left: 2px;
            font-size: 16px;
            font-weight: 600;
            color: rgba(41, 41, 41, 1);
          "
        >
          {{ $t('最小剩余货架寿命') }}
        </div>
      </div>
      <div>
        <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
          <div class="div">
            <mt-form-item prop="minShelfLifeLeft" :label="$t('最小剩余货架寿命')">
              <mt-input
                v-model.number="ruleForm.minShelfLifeLeft"
                :show-clear-button="true"
                :disabled="disabled"
                type="text"
                :placeholder="$t('请输入最小剩余货架寿命')"
              ></mt-input>
            </mt-form-item>
            <mt-form-item prop="totalShelfLife" :label="$t('总货架寿命')">
              <mt-input
                v-model.number="ruleForm.totalShelfLife"
                :show-clear-button="true"
                :disabled="disabled"
                type="text"
                :placeholder="$t('请输入总货架寿命')"
              ></mt-input>
            </mt-form-item>
            <mt-form-item prop="shelfLifeUnitId" :label="$t('寿命单位')">
              <mt-select
                v-model="ruleForm.shelfLifeUnitId"
                :data-source="shelfLife"
                :disabled="disabled"
                :fields="{ text: 'unitName', value: 'id' }"
                :show-clear-button="true"
                :placeholder="$t('请输入')"
                @change="change($event, 'shelfLifeUnitName')"
              ></mt-select>
            </mt-form-item>
            <div class="mt-form-item"></div>
          </div>
          <div class="div">
            <mt-form-item prop="stoagePercent" :label="$t('仓储百分比')">
              <mt-input
                v-model.number="ruleForm.stoagePercent"
                :show-clear-button="true"
                :disabled="disabled"
                type="text"
                :placeholder="$t('请输入仓储百分比')"
              ></mt-input>
            </mt-form-item>
            <mt-form-item prop="dangerousTypeId" :label="$t('危险品类型')">
              <mt-select
                v-model="ruleForm.dangerousTypeId"
                :data-source="dangerousType"
                :disabled="disabled"
                :fields="{ text: 'name', value: 'id' }"
                :show-clear-button="true"
                :placeholder="$t('请输入')"
                @change="change($event, 'dangerousTypeName')"
              ></mt-select>
            </mt-form-item>
            <div class="mt-form-item"></div>
            <div class="mt-form-item"></div>
          </div>
        </mt-form>
      </div>
    </div>
    <div style="display: flex">
      <div v-if="dataSource.length > 0" style="height: 100%">
        <div class="e-input-group">
          <mt-icon
            name="icon_search"
            style="margin: 0 10px 0 0; padding-top: 10px"
            class="search-icon cursor-pointer"
          ></mt-icon>
          <input
            v-model="value"
            class="e-input"
            type="text"
            @change="search"
            :placeholder="$t('请输入组织编码或名称')"
          />
        </div>
        <div
          v-for="(item, index) in dataSource"
          :class="{ active: organizationId === item.organizationId }"
          style="cursor: pointer; padding: 15px 60px 15px 15px"
          :key="index"
          @click="handleSelectTab(item)"
        >
          <span>{{ item.text }}</span>
        </div>
      </div>
      <div
        v-if="ruleForm && rules"
        style="padding: 20px; overflow: auto; flex: 1; position: relative"
      >
        <div style="padding: 30px 0">
          <div
            style="
              width: 3px;
              display: inline-block;
              height: 12px;
              background: rgba(0, 70, 156, 1);
              border-radius: 2px 0 0 2px;
            "
          ></div>
          <div
            style="
              display: inline-block;
              margin-left: 2px;
              font-size: 16px;
              font-weight: 600;
              color: rgba(41, 41, 41, 1);
            "
          >
            {{ $t('补货') }}
          </div>
        </div>
        <div>
          <mt-form ref="ruleForm" :model="ruleForms" :rules="rules1">
            <div class="div">
              <mt-form-item prop="deliveryUnitId" :label="$t('发货单位')">
                <div style="display: flex">
                  <mt-select
                    v-model="ruleForms.deliveryUnitTypeId"
                    :data-source="defaultValueListOne"
                    :disabled="disabled"
                    :fields="{ text: 'name', value: 'id' }"
                    :show-clear-button="true"
                    @change="companyList"
                  ></mt-select>
                  <mt-select
                    v-model="ruleForms.deliveryUnitId"
                    :data-source="defaultValueListTwo"
                    :disabled="disabled"
                    :fields="{ text: 'unitName', value: 'id' }"
                    :show-clear-button="true"
                    @change="companyLists"
                  ></mt-select>
                </div>
              </mt-form-item>
              <mt-form-item prop="stevedoringUnitId" :label="$t('装卸单位')">
                <div style="display: flex">
                  <mt-select
                    v-model="ruleForms.stevedoringUnitTypeId"
                    :data-source="stevedoringOne"
                    :disabled="disabled"
                    :fields="{ text: 'name', value: 'id' }"
                    :show-clear-button="true"
                    @change="typeCompanyList"
                  ></mt-select>
                  <mt-select
                    v-model="ruleForms.stevedoringUnitId"
                    :data-source="stevedoringTwo"
                    :disabled="disabled"
                    :fields="{ text: 'unitName', value: 'id' }"
                    :show-clear-button="true"
                    @change="typeCompanyLists"
                  ></mt-select>
                </div>
              </mt-form-item>
              <mt-form-item prop="batchManagementName" :label="$t('是否启用批次管理')">
                <mt-radio
                  v-model="ruleForms.batchManagementName"
                  :disabled="disabled"
                  :data-source="batchManagement"
                ></mt-radio>
              </mt-form-item>
              <div class="mt-form-item"></div>
            </div>
            <div class="div">
              <mt-form-item prop="temperatureRequirement" :label="$t('温度条件')">
                <mt-input
                  v-model="ruleForms.temperatureRequirement"
                  :show-clear-button="true"
                  type="text"
                  :disabled="disabled"
                  :placeholder="$t('请输入温度条件')"
                ></mt-input>
              </mt-form-item>
              <mt-form-item prop="storageRequirement" :label="$t('存储条件')">
                <mt-input
                  v-model="ruleForms.storageRequirement"
                  :show-clear-button="true"
                  type="text"
                  :disabled="disabled"
                  :placeholder="$t('请输入存储条件')"
                ></mt-input>
              </mt-form-item>
              <mt-form-item prop="containerRequirement" :label="$t('集装箱需求')">
                <mt-input
                  v-model="ruleForms.containerRequirement"
                  :show-clear-button="true"
                  type="text"
                  :disabled="disabled"
                  :placeholder="$t('请输入集装箱需求')"
                ></mt-input>
              </mt-form-item>
              <mt-form-item prop="deliveryTypeId" :label="$t('发货类型')">
                <mt-select
                  v-model="ruleForms.deliveryTypeId"
                  :data-source="deliveryTyp"
                  :disabled="disabled"
                  :fields="{ text: 'name', value: 'id' }"
                  :show-clear-button="true"
                  :placeholder="$t('请输入')"
                  @change="change($event, 'deliveryTypeName')"
                ></mt-select>
              </mt-form-item>
            </div>
            <div class="div">
              <mt-form-item prop="itemLocked" :label="$t('物料被锁定')">
                <mt-radio
                  v-model.number="ruleForms.itemLocked"
                  :disabled="disabled"
                  :data-source="itemLocked"
                ></mt-radio>
              </mt-form-item>
              <mt-form-item prop="abcTypes" :label="$t('ABC分类')">
                <mt-radio
                  v-model="ruleForms.abcTypes"
                  :disabled="disabled"
                  :data-source="abcTypes"
                ></mt-radio>
              </mt-form-item>
              <div class="mt-form-item"></div>
              <div class="mt-form-item"></div>
            </div>
            <div class="div">
              <mt-form-item prop="allowNegativeStore" :label="$t('允许负库存')">
                <mt-radio
                  v-model.number="ruleForms.allowNegativeStore"
                  :disabled="disabled"
                  :data-source="allowNegativeStore"
                ></mt-radio>
              </mt-form-item>
              <mt-form-item prop="targetStorage" :label="$t('目标库存')">
                <mt-input
                  v-model.number="ruleForms.targetStorage"
                  :show-clear-button="true"
                  :disabled="disabled"
                  type="text"
                  :placeholder="$t('请输入目标库存')"
                ></mt-input>
              </mt-form-item>
              <mt-form-item prop="minSafeStorage" :label="$t('最小安全库存')">
                <mt-input
                  v-model.number="ruleForms.minSafeStorage"
                  :show-clear-button="true"
                  :disabled="disabled"
                  type="text"
                  :placeholder="$t('请输入最小安全库存')"
                ></mt-input>
              </mt-form-item>
              <div class="mt-form-item"></div>
            </div>
          </mt-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { formatRules } from '@/utils/util'

export default {
  name: 'Store',
  props: {
    form: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: 'preview'
    }
  },
  data() {
    return {
      value: '',
      ruleForm: {},
      rules: null,
      ruleForms: {},
      rules1: null,
      defaultValueListOne: [],
      defaultValueListTwo: [],
      stevedoringOne: [],
      stevedoringTwo: [],
      itemOrgRelId: null,
      organizationId: '-99',
      organizationCode: 'organizationCode',
      organizationName: this.$t('默认数据源'),
      dataSource: [],
      shelfLife: [],
      dangerousType: [],
      arr: ['shelfLife', 'dangerousType', 'deliveryTyp'],
      allowNegativeStore: [
        {
          label: this.$t('不允许'),
          value: 0
        },
        {
          label: this.$t('允许'),
          value: 1
        }
      ],
      abcTypes: [
        {
          label: 'A',
          value: 'A'
        },
        {
          label: 'B',
          value: 'B'
        },
        {
          label: 'C',
          value: 'C'
        }
      ],
      itemLocked: [
        {
          label: this.$t('否'),
          value: 0
        },
        {
          label: this.$t('是'),
          value: 1
        }
      ],
      deliveryTyp: [],
      batchManagement: [
        {
          label: this.$t('是'),
          value: this.$t('是')
        },
        {
          label: this.$t('否'),
          value: this.$t('否')
        }
      ],
      edit: this.type !== 'preview'
    }
  },
  computed: {
    text() {
      if (this.edit) return this.$t('保存')
      return this.$t('编辑')
    },
    disabled() {
      return !this.edit
    }
  },
  created() {
    this.promiseAll()
    this.companyList()
    this.typeCompanyList()
    this.init()
  },
  methods: {
    search() {
      this.$API.itemManagement
        .itemFindRelSites({ id: this.form.id, keyword: this.value })
        .then((res) => {
          this.dataSource = []
          res.data.forEach((ele) => {
            let obj = {
              text: ele.organizationName,
              ...ele
            }
            this.dataSource.push(obj)
          })
          console.log(res)
        })
    },
    change(value, name) {
      console.log(value, name)
      this.ruleForm[name] =
        value.itemData.name ||
        value.itemData.unitName ||
        value.itemData.groupName ||
        value.itemData.label
    },
    companyList(value) {
      console.log(value)
      let data = {}
      let str = 'itemTreelistOne'
      if (value) {
        str = 'itemTreelistTwo'
        this.form.deliveryUnitTypeName = value.itemData.name
        this.form.deliveryUnitTypeCode = value.itemData.itemCode
        this.$set(data, 'typeCode', value.itemData.itemCode)
      } else {
        this.$set(data, 'dictCode', 'unit')
        if (this.form.deliveryUnitTypeCode) {
          this.$API.itemManagement
            .itemTreelistTwo({
              typeCode: this.form.deliveryUnitTypeCode
            })
            .then((res) => {
              this.$set(this, 'defaultValueListTwo', res.data)
            })
        }
      }
      this.$API.itemManagement[str](data).then((res) => {
        if (value) this.$set(this, 'defaultValueListTwo', res.data)
        else this.$set(this, 'defaultValueListOne', res.data)
      })
    },
    companyLists(e) {
      this.form.deliveryUnitName = e.itemData.unitName
      this.form.deliveryUnitCode = e.itemData.unitCode
    },
    typeCompanyList(value) {
      console.log(value)
      let data = {}
      let str = 'itemTreelistOne'
      if (value) {
        str = 'itemTreelistTwo'
        this.form.stevedoringUnitName = value.itemData.name
        this.form.stevedoringUnitCode = value.itemData.itemCode
        this.$set(data, 'typeCode', value.itemData.itemCode)
      } else {
        this.$set(data, 'dictCode', 'unit')
        if (this.form.stevedoringUnitCode) {
          this.$API.itemManagement
            .itemTreelistTwo({
              typeCode: this.form.stevedoringUnitCode
            })
            .then((res) => {
              this.$set(this, 'stevedoringTwo', res.data)
            })
        }
      }
      this.$API.itemManagement[str](data).then((res) => {
        if (value) this.$set(this, 'stevedoringTwo', res.data)
        else this.$set(this, 'stevedoringOne', res.data)
      })
    },
    typeCompanyLists(e) {
      this.form.stevedoringUnitTypeName = e.itemData.unitName
    },
    promiseAll() {
      let arr = [
        this.$API.itemManagement.itemTreelistTwo({
          typeCode: 'TIME'
        }),
        this.$API.itemManagement.itemTreelistOne({
          dictCode: 'DAMGEROUS-TYPE'
        }),
        this.$API.itemManagement.itemTreelistOne({
          dictCode: 'DELIVERY-TYPE'
        })
      ]
      Promise.all(arr).then((res) => {
        res.forEach((ele, index) => {
          this[this.arr[index]] = ele.data
        })
      })
    },
    holds() {
      if (this.edit) {
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            let data = {
              ...this.ruleForm,
              itemId: this.form.id,
              itemOrgRelId: this.itemOrgRelId,
              organizationId: this.organizationId,
              organizationCode: this.organizationCode,
              organizationName: this.organizationName
            }
            let datas = {
              ...this.ruleForms,
              itemId: this.form.id,
              itemOrgRelId: this.itemOrgRelId,
              organizationId: this.organizationId,
              organizationCode: this.organizationCode,
              organizationName: this.organizationName
            }
            this.$API.itemManagement
              .itemWarehouseBothUpdate({
                basicUpdateRequest: data,
                warehouseUpdateRequest: datas
              })
              .then((res) => {
                console.log(res)
                this.edit = false
              })
          }
        })
      } else {
        this.edit = true
      }
    },
    init() {
      this.$API.itemManagement
        .itemWarehouseDetail({
          itemId: this.form.id,
          organizationCode: this.organizationCode,
          organizationId: this.organizationId
        })
        .then((res) => {
          this.ruleForms = res.data || {}
        })
      this.$API.itemManagement.temWarehouseBasicDetail({ id: this.form.id }).then((res) => {
        this.ruleForm = res.data || {}
      })
      this.$API.itemManagement.itemWarehouseUpdateValid().then((res) => {
        this.rules1 = formatRules(res.data)
      })
      this.$API.itemManagement
        .itemFindRelSites({ id: this.form.id, keyword: this.value })
        .then((res) => {
          this.dataSource = []
          res.data.forEach((ele) => {
            let obj = {
              text: ele.organizationName,
              ...ele
            }
            this.dataSource.push(obj)
          })
          console.log(res)
        })
      this.$API.itemManagement.itemWarehouseBasicUpdateValid().then((res) => {
        this.rules = formatRules(res.data)
      })
    },
    handleSelectTab(item) {
      if (this.organizationId != item.organizationId) {
        this.organizationId = item.organizationId
        this.itemOrgRelId = item.id
        this.organizationCode = item.organizationCode
        this.organizationName = item.organizationName
        this.init()
      }
      console.log(123123, item)
    }
  }
}
</script>

<style lang="scss" scoped>
.tabs /deep/ .e-tab-header {
  background: rgb(250, 250, 250);
}
.icon {
  width: 3px;
  display: inline-block;
  height: 12px;
  background: rgba(0, 70, 156, 1);
  border-radius: 2px 0 0 2px;
}
.title {
  display: inline-block;
  margin-left: 2px;
  font-size: 16px;
  font-weight: 600;
  color: rgba(41, 41, 41, 1);
}
.e-input-group {
  margin: 15px 0;
}
.active {
  background: #f5f6f9;
  color: #00469c !important;
}
.mt-form {
  display: flex;
  flex-wrap: wrap;
  .div {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    .mt-form-item {
      flex: 1;
      margin: 5px 10px;
    }
  }
}
</style>
