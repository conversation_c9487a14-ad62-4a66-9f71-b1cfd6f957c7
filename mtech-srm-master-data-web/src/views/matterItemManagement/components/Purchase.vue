<!--
 * @Author: your name
 * @Date: 2022-01-12 20:55:56
 * @LastEditTime: 2022-01-26 10:08:22
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\matterItemManagement\components\Purchase.vue
-->
<template>
  <div class="storage-wrapper full-height">
    <div class="top mt-pa-20">
      <mt-form ref="baseForm" :model="baseForm" :rules="baseRules">
        <div class="subtitle mt-my-20 mt-px-10">{{ $t('基础信息') }}</div>
        <mt-row :gutter="20">
          <mt-col style="width: 20%">
            <mt-form-item prop="purchaseUnitId" :label="$t('订购单位')">
              <div class="flex">
                <mt-select
                  v-model="baseForm.purchaseUnitTypeId"
                  :data-source="options.UNIT_TYPE"
                  :readonly="readonly"
                  :fields="{ text: 'name', value: 'id' }"
                  :show-clear-button="!readonly"
                  :placeholder="$t('请选择')"
                  @change="unitTypeChange"
                ></mt-select>
                <mt-select
                  v-model="baseForm.purchaseUnitId"
                  :data-source="options.UNIT"
                  :readonly="readonly"
                  :fields="{ text: 'unitName', value: 'id' }"
                  :show-clear-button="!readonly"
                  :placeholder="$t('请选择')"
                ></mt-select>
              </div>
            </mt-form-item>
          </mt-col>
          <mt-col style="width: 20%" class="col-height-74">
            <mt-form-item prop="allowMultiUnit" :label="$t('允许采购多单位')">
              <mt-radio
                v-model="baseForm.allowMultiUnit"
                :data-source="options.IS_ALLOWED"
                :disabled="readonly"
              ></mt-radio>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
    <div class="bottom flex justify-between">
      <div class="left full-height">
        <div class="left-header mt-pa-10">
          <div class="e-input-group left-header--search">
            <mt-icon
              name="icon_search"
              class="left-header--search__icon mt-mr-10 cursor-pointer"
            ></mt-icon>
            <input
              v-model="searchForm.keyword"
              class="e-input"
              type="text"
              @input="getRelSite"
              :placeholder="$t('请输入组织编码或名称')"
            />
          </div>
        </div>
        <div class="left-list">
          <div
            v-for="site in siteList"
            :key="site.organizationId"
            :class="[
              'left-list--item',
              currentSite.organizationId === site.organizationId ? 'active' : ''
            ]"
            @click="itemChange(site)"
          >
            <div class="left-list--item__title">
              {{ site.organizationName }}
            </div>
          </div>
        </div>
      </div>
      <div class="right full-height">
        <mt-form ref="detailForm" :model="detailForm" :rules="detailRules">
          <div class="right-pannel mt-pa-20">
            <div class="subtitle mt-my-20 mt-px-10">{{ $t('补货') }}</div>
            <mt-row :gutter="20">
              <mt-col :sm="8" :md="6" :lg="6" :xl="6">
                <mt-form-item prop="purchaseGroupId" :label="$t('采购组')">
                  <mt-select
                    v-model="detailForm.purchaseGroupId"
                    :data-source="options.GROUP_PUR"
                    :readonly="readonly"
                    :fields="{ text: 'groupName', value: 'id' }"
                    :show-clear-button="!readonly"
                    :placeholder="$t('请输入采购组')"
                  ></mt-select>
                </mt-form-item>
              </mt-col>
              <mt-col :sm="8" :md="6" :lg="6" :xl="6">
                <mt-form-item prop="logisticsGroupId" :label="$t('物流组')">
                  <mt-select
                    v-model="detailForm.logisticsGroupId"
                    :data-source="options.GROUP_LOG"
                    :readonly="readonly"
                    :fields="{ text: 'groupName', value: 'id' }"
                    :show-clear-button="!readonly"
                    :placeholder="$t('请输入物流组')"
                  ></mt-select>
                </mt-form-item>
              </mt-col>
              <mt-col :sm="8" :md="6" :lg="6" :xl="6">
                <mt-form-item prop="taxTypeId" :label="$t('税收分类')">
                  <mt-select
                    v-model="detailForm.taxTypeId"
                    :data-source="options.TAX"
                    :readonly="readonly"
                    :fields="{ text: 'label', value: 'value' }"
                    :show-clear-button="!readonly"
                    :placeholder="$t('请输入税收分类')"
                  ></mt-select>
                </mt-form-item>
              </mt-col>
              <mt-col :sm="8" :md="6" :lg="6" :xl="6" class="col-height-74">
                <mt-form-item prop="allowConsign" :label="$t('寄售控制')">
                  <mt-radio
                    v-model="detailForm.allowConsign"
                    :data-source="options.IS_SALES"
                    :disabled="readonly"
                  ></mt-radio>
                </mt-form-item>
              </mt-col>
              <mt-col :sm="8" :md="6" :lg="6" :xl="6">
                <mt-form-item prop="insufficientDeliveryLimit" :label="$t('交货不足限定')">
                  <mt-input
                    v-model="detailForm.insufficientDeliveryLimit"
                    :show-clear-button="!readonly"
                    :readonly="readonly"
                    type="text"
                    :placeholder="$t('请输入交货不足限定')"
                  ></mt-input>
                </mt-form-item>
              </mt-col>
              <mt-col :sm="8" :md="6" :lg="6" :xl="6">
                <mt-form-item prop="overDeliveryLimit" :label="$t('过量交货限定')">
                  <mt-input
                    v-model="detailForm.overDeliveryLimit"
                    :show-clear-button="!readonly"
                    :readonly="readonly"
                    type="text"
                    :placeholder="$t('请输入过量交货限定')"
                  ></mt-input>
                </mt-form-item>
              </mt-col>
              <mt-col :sm="8" :md="6" :lg="6" :xl="6">
                <mt-form-item prop="extraCostPercent" :label="$t('附加费用百分比')">
                  <mt-input
                    v-model="detailForm.extraCostPercent"
                    :show-clear-button="!readonly"
                    :readonly="readonly"
                    type="text"
                    :placeholder="$t('请输入附加费用百分比')"
                  ></mt-input>
                </mt-form-item>
              </mt-col>
              <mt-col :sm="8" :md="6" :lg="6" :xl="6">
                <mt-form-item prop="minDeliveryPercent" :label="$t('最小交货数量')">
                  <mt-input
                    v-model="detailForm.minDeliveryPercent"
                    :show-clear-button="!readonly"
                    :readonly="readonly"
                    type="text"
                    :placeholder="$t('请输入最小交货数量')"
                  ></mt-input>
                </mt-form-item>
              </mt-col>
            </mt-row>
          </div>
        </mt-form>
      </div>
    </div>
  </div>
</template>

<script>
import { utils } from '@mtech-common/utils'
import { OPTIONS } from '../data/purchase.config'

export default {
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    childData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      options: OPTIONS,

      searchForm: {},

      itemInfo: this.childData,
      siteList: [],
      currentSite: {},

      baseForm: {},
      baseRules: {},

      detailForm: {},
      detailRules: {}
    }
  },
  computed: {
    readonly() {
      return !this.isEdit
    }
  },
  watch: {
    childData: {
      handler(val) {
        this.itemInfo = val
      },
      deep: true
    },
    'itemInfo.id'() {
      this.getRelSite()
      this.getBaseData()
    }
  },
  mounted() {
    this.getRelSite = utils.debounce(this.getRelSite, 500)
    this.dataInit()
  },
  methods: {
    dataInit() {
      this.getBaseData() // 顶部基础数据
      this.getRelSite() // 左侧工厂nav数据

      this.dictTreeGet('UNIT_TYPE', 'unit') // 订购单位 1级
      // this.unitDataGet("UNIT", this.baseForm.purchaseUnitTypeId, "typeId"); // 订购单位 2级

      this.businessGroupGet('GROUP_PUR', 'BG001CG') // 采购组
      this.businessGroupGet('GROUP_LOG', 'BG001WULIU') // 物流组
    },
    getBaseData() {
      const query = {
        id: this.itemInfo.id
      }
      this.$API.itemManagement.itemPurchasingBasicDetail(query).then((res) => {
        this.baseForm = res.data || {}
      })
    },
    getRelSite() {
      const query = {
        id: this.itemInfo.id,
        keyword: this.searchForm.keyword
      }
      this.$API.itemManagement.itemFindRelSites(query).then((res) => {
        this.siteList = res.data
        const data = res.data
        const currentItem = data[0] // 默认数据源是始终存在的，此处取data[0]没问题

        this.siteList = data
        this.itemChange(currentItem)
      })
    },
    itemChange(item) {
      this.currentSite = item

      this.getRelSiteDetail()
    },
    getRelSiteDetail() {
      const { organizationId, organizationCode } = this.currentSite
      const query = {
        itemId: this.itemInfo.id,
        organizationCode,
        organizationId
      }
      this.$API.itemManagement.itemPurchasingDetail(query).then((res) => {
        if (res.data) {
          this.detailForm = res.data || {}
          let data = this.options.GROUP_PUR
          let num = 0
          // 能不能判断下只有列表中没有匹配到的时候才去查？id不能匹配的毕竟是少数
          data?.forEach((item) => {
            if (this.detailForm.purchaseGroupCode == item.groupCode) {
              this.detailForm.purchaseGroupId = item.id
              num++
            }
          })
          if (num == 0) {
            this.detailForm.purchaseGroupId = ''
            this.businessGroupGet('GROUP_PUR', 'BG001CG') // 采购组
          }
        } else {
          this.detailForm = {}
        }
      })
    },
    unitTypeChange(e) {
      this.unitDataGet('UNIT', e.value, 'typeId')
    },
    dataEmit() {
      this.baseForm.itemId = this.itemInfo.id
      this.detailForm.itemId = this.itemInfo.id
      this.detailForm.organizationId = this.currentSite.organizationId
      this.detailForm.itemOrgRelId = this.currentSite.id

      return {
        basicUpdateRequest: this.baseForm,
        purchasingUpdateRequest: this.detailForm
      }
    },

    dictTreeGet(option, dictCode) {
      const query = { dictCode }
      this.$API.dict.TenantDictTreeGet(query).then((res) => {
        this.options[option] = res.data
      })
    },
    unitDataGet(option, value = '', field = 'typeCode') {
      const query = {}
      query[field] = value

      this.$API.dict.unitDataGet(query).then((res) => {
        this.options[option] = res.data
      })
    },
    businessGroupGet(option, groupTypeCode) {
      let that = this
      const query = { groupTypeCode }
      if (option == 'GROUP_PUR') {
        query.groupCode = that.detailForm.purchaseGroupCode
      }
      that.$API.itemManagement.businessGroupNamelist(query).then((res) => {
        let arr = that.options[option] || []
        that.options[option] = arr.concat(res.data)
        if (query.groupCode) {
          that.options[option].forEach((item) => {
            if (that.detailForm.purchaseGroupCode == item.groupCode) {
              that.detailForm.purchaseGroupId = res.data[0].id
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.storage-wrapper {
  .top {
    border-bottom: 1px solid #e8e8e8;
    .col-width-20percent {
      width: 20%;
    }
    .subtitle {
      font-size: 16px;
      font-family: PingFangSC;
      font-weight: 600;
      color: rgba(41, 41, 41, 1);
      border-radius: 2px 0 0 2px;
      border-left: 3px solid rgba(0, 70, 156, 1);
    }

    /deep/ .label {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      margin-bottom: 10px;
    }
  }
  .bottom {
    height: calc(100% - 171px);
    .left {
      width: 208px;
      border-right: 1px solid rgba(232, 232, 232, 1);
      &-header {
        border-bottom: 1px solid #e8e8e8;
        &--search {
          &__icon {
            margin-top: 7px;
          }
        }
      }
      &-list {
        height: calc(100% - 56px);
        padding: 5px 0;
        overflow: auto;
        &--item {
          height: 50px;
          padding: 15px 20px;
          &__title {
            height: 20px;
            line-height: 20px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: 500;
            color: #292929;
          }
        }
        .active {
          background: rgba(245, 246, 249, 1);
          div {
            color: rgba(0, 70, 156, 1);
          }
        }
      }
      &-arrow {
        position: absolute;
        right: -12px;
        top: 85px;
        width: 12px;
        height: 60px;
        line-height: 60px;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 0 4px 4px 0;
        font-size: 12px;
        color: #fff;
      }
    }

    .right {
      width: calc(100% - 208px);
      height: 100%;
      overflow: auto;
      &-pannel {
        .subtitle {
          font-size: 16px;
          font-family: PingFangSC;
          font-weight: 600;
          color: rgba(41, 41, 41, 1);
          border-radius: 2px 0 0 2px;
          border-left: 3px solid rgba(0, 70, 156, 1);
        }
      }

      /deep/ .label {
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: normal;
        margin-bottom: 10px;
      }

      .col-height-74 {
        height: 74px;
      }
    }
  }
}
</style>
