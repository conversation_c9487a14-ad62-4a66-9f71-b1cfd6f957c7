<template>
  <div style="padding-bottom: 500px; height: 100%; overflow: auto">
    <div style="display: flex">
      <div style="height: 100%">
        <div class="e-input-group">
          <mt-icon
            name="icon_search"
            style="margin: 0 10px 0 0; padding-top: 10px"
            class="search-icon cursor-pointer"
          ></mt-icon>
          <input
            v-model="value"
            class="e-input"
            @change="search"
            type="text"
            :placeholder="$t('请输入组织编码或名称')"
          />
        </div>
        <div
          v-for="(item, index) in dataSource"
          :class="{ active: organizationId === item.organizationId }"
          style="cursor: pointer; padding: 15px 60px 15px 15px"
          :key="index"
          @click="handleSelectTab(item)"
        >
          <span>{{ item.text }}</span>
        </div>
      </div>
      <div
        v-if="ruleForm && rules"
        style="padding: 20px; overflow: auto; flex: 1; position: relative"
      >
        <div style="text-align: right; padding: 0 20px; color: #00469c">
          <span style="cursor: pointer" @click="holds">{{ text }}</span>
        </div>
        <div style="padding: 30px 0">
          <div
            style="
              width: 3px;
              display: inline-block;
              height: 12px;
              background: rgba(0, 70, 156, 1);
              border-radius: 2px 0 0 2px;
            "
          ></div>
          <div
            style="
              display: inline-block;
              margin-left: 2px;
              font-size: 16px;
              font-weight: 600;
              color: rgba(41, 41, 41, 1);
            "
          >
            {{ $t('计划信息') }}
          </div>
        </div>
        <div>
          <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
            <div class="div">
              <mt-form-item prop="purchaseFolds" :label="$t('订购倍数')">
                <mt-input
                  v-model.number="ruleForm.purchaseFolds"
                  :show-clear-button="true"
                  :disabled="disabled"
                  type="text"
                  :placeholder="$t('请输入订购倍数')"
                ></mt-input>
              </mt-form-item>
              <mt-form-item prop="maxPurchaseQuantity" :label="$t('最大订购量')">
                <mt-input
                  v-model.number="ruleForm.maxPurchaseQuantity"
                  :show-clear-button="true"
                  :disabled="disabled"
                  type="text"
                  :placeholder="$t('请输入最大订购量')"
                ></mt-input>
              </mt-form-item>
              <mt-form-item prop="minPurchaseQuantity" :label="$t('最小订购量')">
                <mt-input
                  v-model.number="ruleForm.minPurchaseQuantity"
                  :show-clear-button="true"
                  :disabled="disabled"
                  type="text"
                  :placeholder="$t('请输入最小订购量')"
                ></mt-input>
              </mt-form-item>
              <mt-form-item prop="fixBatchSize" :label="$t('固定批量大小')">
                <mt-input
                  v-model.number="ruleForm.fixBatchSize"
                  :show-clear-button="true"
                  :disabled="disabled"
                  type="text"
                  :placeholder="$t('请输入固定批量大小')"
                ></mt-input>
              </mt-form-item>
            </div>
            <div class="div">
              <mt-form-item prop="insufficientDeliveryTolerance" :label="$t('交付不足公差')">
                <mt-input
                  v-model.number="ruleForm.insufficientDeliveryTolerance"
                  :show-clear-button="true"
                  :disabled="disabled"
                  type="text"
                  :placeholder="$t('请输入交付不足公差')"
                ></mt-input>
              </mt-form-item>
              <mt-form-item prop="overDeliveryTolerance" :label="$t('超额交付公差')">
                <mt-input
                  v-model.number="ruleForm.overDeliveryTolerance"
                  :show-clear-button="true"
                  :disabled="disabled"
                  type="text"
                  :placeholder="$t('请输入超额交付公差')"
                ></mt-input>
              </mt-form-item>
              <mt-form-item prop="autoPurchaseOrder" :label="$t('自动转化采购订单')">
                <mt-radio
                  v-model.number="ruleForm.autoPurchaseOrder"
                  :disabled="disabled"
                  :data-source="autoPurchaseList"
                ></mt-radio>
              </mt-form-item>
              <mt-form-item :label="$t('计划提前期')">
                <mt-input
                  v-model.number="plannedLeadTime"
                  :show-clear-button="true"
                  :disabled="true"
                  type="text"
                  :placeholder="$t('请输入计划提前期')"
                ></mt-input>
              </mt-form-item>
            </div>
            <div class="div">
              <mt-form-item prop="processingTime" :label="$t('计划前置提前期 订单处理（天）')">
                <mt-input
                  v-model.number="ruleForm.processingTime"
                  :show-clear-button="true"
                  :disabled="disabled"
                  type="text"
                  :placeholder="$t('请输入计划前置提前期')"
                ></mt-input>
              </mt-form-item>
              <mt-form-item prop="processingTime" :label="$t('计划生产提前期订单下单到收货（天）')">
                <!--            缺少-->
                <mt-input
                  v-model.number="ruleForm.manufactureTime"
                  :show-clear-button="true"
                  :disabled="disabled"
                  type="text"
                  :placeholder="$t('请输入计划生产提前期')"
                ></mt-input>
              </mt-form-item>
              <mt-form-item prop="receiveingTime" :label="$t('计划后置提前期质检收货（天）')">
                <mt-input
                  v-model.number="ruleForm.receiveingTime"
                  :show-clear-button="true"
                  :disabled="disabled"
                  type="text"
                  :placeholder="$t('请输入计划后置提前期')"
                ></mt-input>
              </mt-form-item>
              <div class="mt-form-item"></div>
            </div>
          </mt-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { formatRules } from '@/utils/util'

export default {
  name: 'Plan',
  props: {
    form: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: 'preview'
    }
  },
  data() {
    return {
      value: '',
      ruleForm: null,
      rules: null,
      edit: this.type !== 'preview',
      organizationId: '-99',
      itemOrgRelId: '',
      organizationCode: 'organizationCode',
      organizationName: this.$t('默认数据源'),
      dataSource: [],
      activeBtn: 0,
      autoPurchaseList: [
        {
          label: this.$t('是'),
          value: 1
        },
        {
          label: this.$t('否'),
          value: 0
        }
      ]
    }
  },
  computed: {
    plannedLeadTime() {
      let num1 = this.ruleForm.receiveingTime || 0
      let num2 = this.ruleForm.manufactureTime || 0
      let num3 = this.ruleForm.processingTime || 0
      console.log(num1, num2, num3)
      return num1 + num2 + num3
    },
    text() {
      if (this.edit) return this.$t('保存')
      return this.$t('编辑')
    },
    disabled() {
      return !this.edit
    }
  },
  created() {
    this.init()
    this.initFn()
  },
  methods: {
    search(e) {
      this.init()
      console.log(e.target.value)
    },
    holds() {
      if (this.edit) {
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            let data = {
              ...this.ruleForm,
              itemId: this.form.id,
              organizationId: this.organizationId,
              itemOrgRelId: this.itemOrgRelId,
              organizationCode: this.organizationCode,
              organizationName: this.organizationName
            }
            this.$API.itemManagement.itemPlanningUpdate(data).then((res) => {
              console.log(res)
              this.edit = false
            })
          }
        })
      } else {
        this.edit = true
      }
    },
    initFn() {
      this.$API.itemManagement
        .itemPlanningDetail({
          itemId: this.form.id,
          organizationCode: this.organizationCode,
          organizationId: this.organizationId
        })
        .then((res) => {
          this.ruleForm = res.data || {}
          console.log(res)
        })
    },
    init() {
      this.$API.itemManagement
        .itemFindRelSites({ id: this.form.id, keyword: this.value })
        .then((res) => {
          this.dataSource = []
          res.data.forEach((ele) => {
            let obj = {
              text: ele.organizationName,
              ...ele
            }
            this.dataSource.push(obj)
          })
          console.log(res)
        })
      this.$API.itemManagement.itemPlanningUpdateValid().then((res) => {
        this.rules = formatRules(res.data)
      })
    },
    handleSelectTab(item) {
      if (this.organizationId != item.organizationId) {
        this.organizationId = item.organizationId
        this.itemOrgRelId = item.id
        this.organizationCode = item.organizationCode
        this.organizationName = item.organizationName
        this.initFn()
      }
      console.log(123123, item)
    }
  }
}
</script>

<style lang="scss" scoped>
.e-input-group {
  margin: 15px 0;
}
.active {
  background: #f5f6f9;
  color: #00469c !important;
}
.mt-form {
  display: flex;
  flex-wrap: wrap;
  .div {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    .mt-form-item {
      flex: 1;
      margin: 5px 10px;
    }
  }
}
</style>
