<!--
 * @Author: your name
 * @Date: 2022-01-12 20:55:36
 * @LastEditTime: 2022-01-27 14:02:04
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\matterItemManagement\components\Plan.vue
-->
<template>
  <div class="plan-wrapper flex justify-between full-height">
    <div class="left full-height">
      <div class="left-header mt-pa-10">
        <div class="e-input-group left-header--search">
          <mt-icon
            name="icon_search"
            class="left-header--search__icon mt-mr-10 cursor-pointer"
          ></mt-icon>
          <input
            v-model="searchForm.keyword"
            class="e-input"
            type="text"
            @input="getRelSite"
            :placeholder="$t('请输入组织编码或名称')"
          />
        </div>
      </div>
      <div class="left-list">
        <div
          v-for="site in siteList"
          :key="site.organizationId"
          :class="[
            'left-list--item',
            currentSite.organizationId === site.organizationId ? 'active' : ''
          ]"
          @click="itemChange(site)"
        >
          <div class="left-list--item__title">
            {{ site.organizationName }}
          </div>
        </div>
      </div>
    </div>
    <div class="right">
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <div class="right-pannel mt-pa-20">
          <div class="subtitle mt-my-20 mt-px-10">{{ $t('计划信息') }}</div>
          <mt-row :gutter="20">
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="purchaseFolds" :label="$t('订购倍数')">
                <mt-input
                  v-model="ruleForm.purchaseFolds"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  type="text"
                  :placeholder="$t('请输入订购倍数')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="maxPurchaseQuantity" :label="$t('最大订购量')">
                <mt-input
                  v-model="ruleForm.maxPurchaseQuantity"
                  :readonly="readonly"
                  :show-clear-button="!readonly"
                  type="text"
                  :placeholder="$t('请输入最大订购量')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="minPurchaseQuantity" :label="$t('最小订购量')">
                <mt-input
                  v-model="ruleForm.minPurchaseQuantity"
                  :readonly="readonly"
                  :show-clear-button="!readonly"
                  type="text"
                  :placeholder="$t('请输入最小订购量')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="fixBatchSize" :label="$t('固定批量大小')">
                <mt-input
                  v-model="ruleForm.fixBatchSize"
                  :readonly="readonly"
                  :show-clear-button="!readonly"
                  type="text"
                  :placeholder="$t('请输入固定批量大小')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="insufficientDeliveryTolerance" :label="$t('请输入交付不足公差')">
                <mt-input
                  v-model="ruleForm.insufficientDeliveryTolerance"
                  :readonly="readonly"
                  :show-clear-button="!readonly"
                  type="text"
                  :placeholder="$t('请输入请输入交付不足公差')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="overDeliveryTolerance" :label="$t('超额交付公差')">
                <mt-input
                  v-model="ruleForm.overDeliveryTolerance"
                  :readonly="readonly"
                  :show-clear-button="!readonly"
                  type="text"
                  :placeholder="$t('请输入超额交付公差')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6" class="col-height-74">
              <mt-form-item prop="autoPurchaseOrder" :label="$t('自动转化采购订单')">
                <mt-radio
                  v-model="ruleForm.autoPurchaseOrder"
                  :data-source="options.YES_OR_NO"
                  :disabled="readonly"
                ></mt-radio>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="plannedLeadTime" :label="$t('计划提前期')">
                <mt-input
                  v-model="ruleForm.plannedLeadTime"
                  :show-clear-button="!readonly"
                  :readonly="true"
                  type="text"
                  :placeholder="$t('请输入计划提前期')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="processingTime" :label="$t('计划前置提前期')">
                <mt-input
                  v-model="ruleForm.processingTime"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  type="text"
                  :placeholder="$t('请输入计划前置提前期')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="manufactureTime" :label="$t('计划生产提前期')">
                <mt-input
                  v-model="ruleForm.manufactureTime"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  type="text"
                  :placeholder="$t('请输入计划生产提前期')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="6" :xl="6">
              <mt-form-item prop="receiveingTime" :label="$t('计划后置提前期')">
                <mt-input
                  v-model="ruleForm.receiveingTime"
                  :show-clear-button="!readonly"
                  :readonly="readonly"
                  type="text"
                  :placeholder="$t('请输入计划后置提前期')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
          </mt-row>
        </div>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { utils } from '@mtech-common/utils'
import { OPTIONS } from '../data/plan.config'

export default {
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    childData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      options: OPTIONS,

      searchForm: {},

      itemInfo: this.childData,
      siteList: [],
      currentSite: {},

      ruleForm: {},
      rules: {}
    }
  },
  computed: {
    readonly() {
      return !this.isEdit
    },
    plannedLeadTime() {
      return (
        this.ruleForm.processingTime * 1 +
        this.ruleForm.manufactureTime * 1 +
        this.ruleForm.receiveingTime * 1
      )
    }
  },
  watch: {
    childData: {
      handler(val) {
        this.itemInfo = val
      },
      deep: true
    },
    'itemInfo.id'() {
      this.getRelSite()
    },
    plannedLeadTime(val) {
      this.ruleForm.plannedLeadTime = val
    }
  },
  mounted() {
    this.getRelSite = utils.debounce(this.getRelSite, 500)
    this.dataInit()
  },
  methods: {
    dataInit() {
      this.getRelSite()
    },
    getRelSite() {
      const query = {
        id: this.itemInfo.id,
        keyword: this.searchForm.keyword
      }
      this.$API.itemManagement.itemFindRelSites(query).then((res) => {
        this.siteList = res.data
        const data = res.data
        const currentItem = data[0] // 默认数据源是始终存在的，此处取data[0]没问题

        this.siteList = data
        this.itemChange(currentItem)
      })
    },
    itemChange(item) {
      this.currentSite = item

      this.getRelSiteDetail()
    },
    getRelSiteDetail() {
      const { organizationId, organizationCode } = this.currentSite
      const query = {
        itemId: this.itemInfo.id,
        organizationCode,
        organizationId
      }
      this.$API.itemManagement.itemPlanningDetail(query).then((res) => {
        this.ruleForm = res.data || {}
      })
    },
    dataEmit() {
      const {
        id: itemOrgRelId,
        organizationId,
        organizationCode,
        organizationName
      } = this.currentSite

      return {
        itemId: this.itemInfo.id,
        itemOrgRelId,
        organizationId,
        organizationCode,
        organizationName,
        ...this.ruleForm
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.plan-wrapper {
  .left {
    width: 208px;
    border-right: 1px solid rgba(232, 232, 232, 1);
    height: 100%;
    overflow: auto;
    &-header {
      border-bottom: 1px solid #e8e8e8;
      &--search {
        &__icon {
          margin-top: 7px;
        }
      }
    }
    &-list {
      height: calc(100% - 56px);
      padding: 5px 0;
      overflow: auto;
      &--item {
        height: 50px;
        padding: 15px 20px;
        &__title {
          height: 20px;
          line-height: 20px;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          color: #292929;
        }
      }
      .active {
        background: rgba(245, 246, 249, 1);
        div {
          color: rgba(0, 70, 156, 1);
        }
      }
    }
    &-arrow {
      position: absolute;
      right: -12px;
      top: 85px;
      width: 12px;
      height: 60px;
      line-height: 60px;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 0 4px 4px 0;
      font-size: 12px;
      color: #fff;
    }
  }

  .right {
    width: calc(100% - 147px);
    height: 100%;
    overflow: auto;
    &-pannel {
      .subtitle {
        font-size: 16px;
        font-family: PingFangSC;
        font-weight: 600;
        color: rgba(41, 41, 41, 1);
        border-radius: 2px 0 0 2px;
        border-left: 3px solid rgba(0, 70, 156, 1);
      }
    }

    /deep/ .label {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      margin-bottom: 10px;
    }

    .col-height-74 {
      height: 74px;
    }
  }
}
</style>
