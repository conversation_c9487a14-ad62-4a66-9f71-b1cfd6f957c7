<!--
 * @Author: your name
 * @Date: 2022-01-12 20:56:35
 * @LastEditTime: 2022-01-26 10:10:44
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\matterItemManagement\components\Storage.vue
-->
<template>
  <div class="storage-wrapper full-height">
    <div class="top mt-pa-20">
      <mt-form ref="baseForm" :model="baseForm" :rules="baseRules">
        <div class="subtitle mt-my-20 mt-px-10">{{ $t('基础信息') }}</div>
        <mt-row :gutter="20">
          <mt-col style="width: 20%">
            <mt-form-item prop="minShelfLifeLeft" :label="$t('最小剩余货架寿命')">
              <mt-input
                v-model.number="baseForm.minShelfLifeLeft"
                :show-clear-button="!readonly"
                :readonly="readonly"
                type="text"
                :placeholder="$t('请输入最小剩余货架寿命')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col style="width: 20%">
            <mt-form-item prop="totalShelfLife" :label="$t('总货架寿命')">
              <mt-input
                v-model.number="baseForm.totalShelfLife"
                :show-clear-button="!readonly"
                :readonly="readonly"
                type="text"
                :placeholder="$t('请输入总货架寿命')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col style="width: 20%">
            <mt-form-item prop="shelfLifeUnitId" :label="$t('寿命单位')">
              <mt-select
                v-model="baseForm.shelfLifeUnitId"
                :data-source="options.UNIT_TIME"
                :readonly="readonly"
                :fields="{ text: 'unitName', value: 'id' }"
                :show-clear-button="!readonly"
                :placeholder="$t('请输入寿命单位')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col style="width: 20%">
            <mt-form-item prop="stoagePercent" :label="$t('仓储百分比')">
              <mt-input
                v-model.number="baseForm.stoagePercent"
                :show-clear-button="!readonly"
                :readonly="readonly"
                type="text"
                :placeholder="$t('请输入仓储百分比')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col style="width: 20%">
            <mt-form-item prop="dangerousTypeId" :label="$t('危险品类型')">
              <mt-select
                v-model="baseForm.dangerousTypeId"
                :data-source="options.TYPE"
                :readonly="readonly"
                :fields="{ text: 'name', value: 'id' }"
                :show-clear-button="!readonly"
                :placeholder="$t('请输入危险品类型')"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
    <div class="bottom flex justify-between">
      <div class="left full-height">
        <div class="left-header mt-pa-10">
          <div class="e-input-group left-header--search">
            <mt-icon
              name="icon_search"
              class="left-header--search__icon mt-mr-10 cursor-pointer"
            ></mt-icon>
            <input
              v-model="searchForm.keyword"
              class="e-input"
              type="text"
              @input="getRelSite"
              :placeholder="$t('请输入组织编码或名称')"
            />
          </div>
        </div>
        <div class="left-list">
          <div
            v-for="site in siteList"
            :key="site.organizationId"
            :class="[
              'left-list--item',
              currentSite.organizationId === site.organizationId ? 'active' : ''
            ]"
            @click="itemChange(site)"
          >
            <div class="left-list--item__title">
              {{ site.organizationName }}
            </div>
          </div>
        </div>
      </div>
      <div class="right full-height">
        <mt-form ref="detailForm" :model="detailForm" :rules="detailRules">
          <div class="right-pannel mt-pa-20">
            <div class="subtitle mt-my-20 mt-px-10">{{ $t('存储要求') }}</div>
            <mt-row :gutter="20">
              <mt-col :sm="8" :md="6" :lg="6" :xl="6">
                <mt-form-item prop="deliveryUnitId" :label="$t('发货单位')">
                  <div class="flex">
                    <mt-select
                      v-model="detailForm.deliveryUnitTypeId"
                      :data-source="options.UNIT_TYPE"
                      :readonly="readonly"
                      :fields="{ text: 'name', value: 'id' }"
                      :show-clear-button="!readonly"
                      :placeholder="$t('请选择')"
                      @change="unit1TypeChange"
                    ></mt-select>
                    <mt-select
                      v-model="detailForm.deliveryUnitId"
                      :data-source="options.UNIT1"
                      :readonly="readonly"
                      :fields="{ text: 'unitName', value: 'id' }"
                      :show-clear-button="!readonly"
                      :placeholder="$t('请选择')"
                    ></mt-select>
                  </div>
                </mt-form-item>
              </mt-col>
              <mt-col :sm="8" :md="6" :lg="6" :xl="6">
                <mt-form-item prop="stevedoringUnitId" :label="$t('装卸单位')">
                  <div class="flex">
                    <mt-select
                      v-model="detailForm.stevedoringUnitTypeId"
                      :data-source="options.UNIT_TYPE"
                      :readonly="readonly"
                      :fields="{ text: 'name', value: 'id' }"
                      :show-clear-button="!readonly"
                      :placeholder="$t('请选择')"
                      @change="unit2TypeChange"
                    ></mt-select>
                    <mt-select
                      v-model="detailForm.stevedoringUnitId"
                      :data-source="options.UNIT2"
                      :readonly="readonly"
                      :fields="{ text: 'unitName', value: 'id' }"
                      :show-clear-button="!readonly"
                      :placeholder="$t('请选择')"
                    ></mt-select>
                  </div>
                </mt-form-item>
              </mt-col>
              <mt-col :sm="8" :md="6" :lg="6" :xl="6" class="col-height-74">
                <mt-form-item prop="batchManagementName" :label="$t('是否启用批次管理')">
                  <mt-radio
                    v-model="detailForm.batchManagementName"
                    :data-source="options.YES_OR_NO_LABEL"
                    :disabled="readonly"
                  ></mt-radio>
                </mt-form-item>
              </mt-col>
              <mt-col :sm="8" :md="6" :lg="6" :xl="6">
                <mt-form-item prop="temperatureRequirement" :label="$t('温度条件')">
                  <mt-input
                    v-model="detailForm.temperatureRequirement"
                    :show-clear-button="!readonly"
                    :readonly="readonly"
                    type="text"
                    :placeholder="$t('请输入温度条件')"
                  ></mt-input>
                </mt-form-item>
              </mt-col>
              <mt-col :sm="8" :md="6" :lg="6" :xl="6">
                <mt-form-item prop="storageRequirement" :label="$t('存储条件')">
                  <mt-input
                    v-model="detailForm.storageRequirement"
                    :show-clear-button="!readonly"
                    :readonly="readonly"
                    type="text"
                    :placeholder="$t('请输入存储条件')"
                  ></mt-input>
                </mt-form-item>
              </mt-col>
              <mt-col :sm="8" :md="6" :lg="6" :xl="6">
                <mt-form-item prop="containerRequirement" :label="$t('集装箱需求')">
                  <mt-input
                    v-model="detailForm.containerRequirement"
                    :show-clear-button="!readonly"
                    :readonly="readonly"
                    type="text"
                    :placeholder="$t('请输入集装箱需求')"
                  ></mt-input>
                </mt-form-item>
              </mt-col>
              <mt-col :sm="8" :md="6" :lg="6" :xl="6">
                <mt-form-item prop="deliveryTypeId" :label="$t('发货类型')">
                  <mt-select
                    v-model="detailForm.deliveryTypeId"
                    :data-source="options.DELIVERY_TYPE"
                    :readonly="readonly"
                    :fields="{ text: 'name', value: 'id' }"
                    :show-clear-button="!readonly"
                    :placeholder="$t('请输入发货类型')"
                  ></mt-select>
                </mt-form-item>
              </mt-col>
              <mt-col :sm="8" :md="6" :lg="6" :xl="6" class="col-height-74">
                <mt-form-item prop="itemLocked" :label="$t('物料被锁定')">
                  <mt-radio
                    v-model="detailForm.itemLocked"
                    :data-source="options.YES_OR_NO"
                    :disabled="readonly"
                  ></mt-radio>
                </mt-form-item>
              </mt-col>
              <mt-col :sm="8" :md="6" :lg="6" :xl="6" class="col-height-74">
                <mt-form-item prop="abcTypes" :label="$t('ABC分类')">
                  <mt-radio
                    v-model="detailForm.abcTypes"
                    :data-source="options.A_B_C"
                    :disabled="readonly"
                  ></mt-radio>
                </mt-form-item>
              </mt-col>
              <mt-col :sm="8" :md="6" :lg="6" :xl="6" class="col-height-74">
                <mt-form-item prop="allowNegativeStore" :label="$t('允许负库存')">
                  <mt-radio
                    v-model="detailForm.allowNegativeStore"
                    :data-source="options.IS_ALLOWED"
                    :disabled="readonly"
                  ></mt-radio>
                </mt-form-item>
              </mt-col>
              <mt-col :sm="8" :md="6" :lg="6" :xl="6">
                <mt-form-item prop="targetStorage" :label="$t('目标库存')">
                  <mt-input
                    v-model="detailForm.targetStorage"
                    :show-clear-button="!readonly"
                    :readonly="readonly"
                    type="text"
                    :placeholder="$t('请输入目标库存')"
                  ></mt-input>
                </mt-form-item>
              </mt-col>
              <mt-col :sm="8" :md="6" :lg="6" :xl="6">
                <mt-form-item prop="minSafeStorage" :label="$t('最小安全库存')">
                  <mt-input
                    v-model="detailForm.minSafeStorage"
                    :show-clear-button="!readonly"
                    :readonly="readonly"
                    type="text"
                    :placeholder="$t('请输入最小安全库存')"
                  ></mt-input>
                </mt-form-item>
              </mt-col>
            </mt-row>
          </div>
        </mt-form>
      </div>
    </div>
  </div>
</template>

<script>
import { utils } from '@mtech-common/utils'
import { OPTIONS } from '../data/storage.config'

export default {
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    childData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      options: OPTIONS,

      searchForm: {},

      itemInfo: this.childData,
      siteList: [],
      currentSite: {},

      baseForm: {},
      baseRules: {},

      detailForm: {},
      detailRules: {}
    }
  },
  computed: {
    readonly() {
      return !this.isEdit
    }
  },
  watch: {
    childData: {
      handler(val) {
        this.itemInfo = val
      },
      deep: true
    },
    'itemInfo.id'() {
      this.getRelSite()
      this.getBaseData()
    }
  },
  mounted() {
    this.getRelSite = utils.debounce(this.getRelSite, 500)
    this.dataInit()
  },
  methods: {
    dataInit() {
      this.getBaseData() // 顶部基础数据
      this.getRelSite() // 左侧工厂nav数据

      this.unitDataGet('UNIT_TIME', 'TIME') // 寿命单位
      this.dictTreeGet('TYPE', 'DAMGEROUS-TYPE') // 危险品类型

      this.dictTreeGet('UNIT_TYPE', 'unit') // 发货/装卸单位 1级
      this.unitDataGet('UNIT1', this.detailForm.deliveryUnitTypeId, 'typeId') // 发货单位 2级
      this.unitDataGet('UNIT2', this.detailForm.stevedoringUnitTypeId, 'typeId') // 装卸单位 2级
      this.dictTreeGet('DELIVERY_TYPE', 'DELIVERY-TYPE') // 发货/装卸单位 1级
    },
    getBaseData() {
      const query = {
        id: this.itemInfo.id
      }
      this.$API.itemManagement.temWarehouseBasicDetail(query).then((res) => {
        this.baseForm = res.data || {}
      })
    },
    getRelSite() {
      const query = {
        id: this.itemInfo.id,
        keyword: this.searchForm.keyword
      }
      this.$API.itemManagement.itemFindRelSites(query).then((res) => {
        this.siteList = res.data
        const data = res.data
        const currentItem = data[0] // 默认数据源是始终存在的，此处取data[0]没问题

        this.siteList = data
        this.itemChange(currentItem)
      })
    },
    itemChange(item) {
      this.currentSite = item

      this.getRelSiteDetail()
    },
    getRelSiteDetail() {
      const { organizationId, organizationCode } = this.currentSite
      const query = {
        itemId: this.itemInfo.id,
        organizationCode,
        organizationId
      }
      this.$API.itemManagement.itemWarehouseDetail(query).then((res) => {
        this.detailForm = res.data || {}
      })
    },
    unit1TypeChange(e) {
      this.unitDataGet('UNIT1', e.value, 'typeId')
    },
    unit2TypeChange(e) {
      this.unitDataGet('UNIT2', e.value, 'typeId')
    },
    dataEmit() {
      this.baseForm.itemId = this.itemInfo.id
      this.detailForm.itemId = this.itemInfo.id
      this.detailForm.organizationId = this.currentSite.organizationId
      this.detailForm.itemOrgRelId = this.currentSite.id

      return {
        basicUpdateRequest: this.baseForm,
        warehouseUpdateRequest: this.detailForm
      }
    },

    dictTreeGet(option, dictCode) {
      const query = { dictCode }
      this.$API.dict.TenantDictTreeGet(query).then((res) => {
        this.options[option] = res.data
      })
    },
    unitDataGet(option, value = '', field = 'typeCode') {
      const query = {}
      query[field] = value

      this.$API.dict.unitDataGet(query).then((res) => {
        this.options[option] = res.data
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.storage-wrapper {
  .top {
    border-bottom: 1px solid #e8e8e8;
    .col-width-20percent {
      width: 20%;
    }
    .subtitle {
      font-size: 16px;
      font-family: PingFangSC;
      font-weight: 600;
      color: rgba(41, 41, 41, 1);
      border-radius: 2px 0 0 2px;
      border-left: 3px solid rgba(0, 70, 156, 1);
    }

    /deep/ .label {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      margin-bottom: 10px;
    }
  }
  .bottom {
    height: calc(100% - 171px);
    .left {
      width: 208px;
      border-right: 1px solid rgba(232, 232, 232, 1);
      &-header {
        border-bottom: 1px solid #e8e8e8;
        &--search {
          &__icon {
            margin-top: 7px;
          }
        }
      }
      &-list {
        height: calc(100% - 56px);
        padding: 5px 0;
        overflow: auto;
        &--item {
          height: 50px;
          padding: 15px 20px;
          &__title {
            height: 20px;
            line-height: 20px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: 500;
            color: #292929;
          }
        }
        .active {
          background: rgba(245, 246, 249, 1);
          div {
            color: rgba(0, 70, 156, 1);
          }
        }
      }
      &-arrow {
        position: absolute;
        right: -12px;
        top: 85px;
        width: 12px;
        height: 60px;
        line-height: 60px;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 0 4px 4px 0;
        font-size: 12px;
        color: #fff;
      }
    }

    .right {
      width: calc(100% - 208px);
      height: 100%;
      overflow: auto;
      &-pannel {
        .subtitle {
          font-size: 16px;
          font-family: PingFangSC;
          font-weight: 600;
          color: rgba(41, 41, 41, 1);
          border-radius: 2px 0 0 2px;
          border-left: 3px solid rgba(0, 70, 156, 1);
        }
      }

      /deep/ .label {
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: normal;
        margin-bottom: 10px;
      }

      .col-height-74 {
        height: 74px;
      }
    }
  }
}
</style>
