<template>
  <div style="padding-bottom: 500px; height: 100%; overflow: auto">
    <div style="padding: 20px; border-bottom: 3px solid rgb(250, 250, 250)">
      <div style="text-align: right; padding: 0 20px; color: #00469c">
        <span style="cursor: pointer" @click="holds">{{ text }}</span>
      </div>
      <div style="padding: 30px 0">
        <div
          style="
            width: 3px;
            display: inline-block;
            height: 12px;
            background: rgba(0, 70, 156, 1);
            border-radius: 2px 0 0 2px;
          "
        ></div>
        <div
          style="
            display: inline-block;
            margin-left: 2px;
            font-size: 16px;
            font-weight: 600;
            color: rgba(41, 41, 41, 1);
          "
        >
          {{ $t('基础信息') }}
        </div>
      </div>
      <div>
        <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
          <div class="div">
            <mt-form-item prop="purchaseUnitId" :label="$t('订购单位')">
              <div style="display: flex">
                <mt-select
                  v-model="ruleForm.purchaseUnitTypeId"
                  :data-source="defaultValueListOne"
                  :disabled="disabled"
                  :fields="{ text: 'name', value: 'id' }"
                  :show-clear-button="true"
                  @change="companyList"
                ></mt-select>
                <mt-select
                  v-model="ruleForm.purchaseUnitId"
                  :data-source="defaultValueListTwo"
                  :disabled="disabled"
                  :fields="{ text: 'unitName', value: 'id' }"
                  :show-clear-button="true"
                  @change="companyLists"
                ></mt-select>
              </div>
            </mt-form-item>
            <mt-form-item prop="allowMultiUnit" :label="$t('允许采购多单位')">
              <!-- <mt-radio v-model="ruleForm.allowMultiUnit" :dataSource="autoPurchaseList" ></mt-radio> -->
              <mt-radio
                v-model.number="ruleForm.allowMultiUnit"
                :disabled="disabled"
                :data-source="autoPurchaseList"
              ></mt-radio>
            </mt-form-item>
            <div class="mt-form-item"></div>
            <div class="mt-form-item"></div>
          </div>
        </mt-form>
      </div>
    </div>
    <div style="display: flex">
      <div style="height: 100%">
        <div class="e-input-group">
          <mt-icon
            name="icon_search"
            style="margin: 0 10px 0 0; padding-top: 10px"
            class="search-icon cursor-pointer"
          ></mt-icon>
          <input
            v-model="value"
            class="e-input"
            @change="search"
            type="text"
            :placeholder="$t('请输入组织编码或名称')"
          />
        </div>
        <div
          v-for="(item, index) in dataSource"
          :class="{ active: organizationId === item.organizationId }"
          style="cursor: pointer; padding: 15px 60px 15px 15px"
          :key="index"
          @click="handleSelectTab(item)"
        >
          <span>{{ item.text }}</span>
        </div>
      </div>
      <div
        v-if="ruleForm && rules"
        style="padding: 20px; overflow: auto; flex: 1; position: relative"
      >
        <div style="padding: 30px 0">
          <div
            style="
              width: 3px;
              display: inline-block;
              height: 12px;
              background: rgba(0, 70, 156, 1);
              border-radius: 2px 0 0 2px;
            "
          ></div>
          <div
            style="
              display: inline-block;
              margin-left: 2px;
              font-size: 16px;
              font-weight: 600;
              color: rgba(41, 41, 41, 1);
            "
          >
            {{ $t('补货') }}
          </div>
        </div>
        <div>
          <mt-form ref="ruleForm" :model="ruleForms" :rules="rules1">
            <div class="div">
              <mt-form-item prop="purchaseGroupId" :label="$t('采购组')">
                <mt-select
                  v-model="ruleForms.purchaseGroupId"
                  :data-source="purchaseGroup"
                  :disabled="disabled"
                  :fields="{ text: 'groupName', value: 'id' }"
                  :show-clear-button="true"
                  :placeholder="$t('请输入')"
                  @change="changes($event, 'purchaseGroupName')"
                ></mt-select>
              </mt-form-item>
              <mt-form-item prop="logisticsGroupId" :label="$t('物流组')">
                <mt-select
                  v-model="ruleForms.logisticsGroupId"
                  :data-source="logisticsGroup"
                  :disabled="disabled"
                  :fields="{ text: 'groupName', value: 'id' }"
                  :show-clear-button="true"
                  :placeholder="$t('请输入')"
                  @change="changes($event, 'logisticsGroupName')"
                ></mt-select>
              </mt-form-item>
              <div class="mt-form-item"></div>
              <div class="mt-form-item"></div>
            </div>
            <div class="div">
              <mt-form-item prop="taxTypeId" :label="$t('税收分类')">
                <mt-select
                  v-model="ruleForms.taxTypeId"
                  :data-source="taxType"
                  :disabled="disabled"
                  :fields="{ text: 'label', value: 'value' }"
                  :show-clear-button="true"
                  :placeholder="$t('请输入')"
                  @change="changes($event, 'taxTypeName')"
                ></mt-select>
              </mt-form-item>
              <mt-form-item prop="allowConsign" :label="$t('寄售控制')">
                <mt-radio
                  v-model.number="ruleForms.allowConsign"
                  :disabled="disabled"
                  :data-source="allowConsignList"
                ></mt-radio>
              </mt-form-item>
              <div class="mt-form-item"></div>
              <div class="mt-form-item"></div>
            </div>
            <div class="div">
              <mt-form-item prop="insufficientDeliveryLimit" :label="$t('交货不足限定')">
                <mt-input
                  v-model.number="ruleForms.insufficientDeliveryLimit"
                  :disabled="disabled"
                  :show-clear-button="true"
                  type="text"
                  :placeholder="$t('请输入交货不足限定')"
                ></mt-input>
              </mt-form-item>
              <mt-form-item prop="overDeliveryLimit" :label="$t('过量交货限定')">
                <mt-input
                  v-model.number="ruleForms.overDeliveryLimit"
                  :disabled="disabled"
                  :show-clear-button="true"
                  type="text"
                  :placeholder="$t('请输入过量交货限定')"
                ></mt-input>
              </mt-form-item>
              <mt-form-item prop="extraCostPercent" :label="$t('附加费用百分比')">
                <mt-input
                  v-model.number="ruleForms.extraCostPercent"
                  :disabled="disabled"
                  :show-clear-button="true"
                  type="text"
                  :placeholder="$t('请输入附加费用百分比')"
                ></mt-input>
              </mt-form-item>
              <mt-form-item prop="minDeliveryPercent" :label="$t('最小交货数量')">
                <mt-input
                  v-model.number="ruleForms.minDeliveryPercent"
                  :disabled="disabled"
                  :show-clear-button="true"
                  type="text"
                  :placeholder="$t('请输入最小交货数量')"
                ></mt-input>
              </mt-form-item>
            </div>
          </mt-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { formatRules } from '@/utils/util'
export default {
  name: 'Purchase',
  props: {
    form: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: 'preview'
    }
  },
  data() {
    return {
      ruleForms: {},
      ruleForm: {},
      defaultValueListOne: [],
      defaultValueListTwo: [],
      itemOrgRelId: null,
      purchaseGroup: [],
      value: '',
      logisticsGroup: [],
      arr: ['logisticsGroup', 'purchaseGroup'],
      autoPurchaseList: [
        {
          label: this.$t('允许'),
          value: 1
        },
        {
          label: this.$t('不允许'),
          value: 0
        }
      ],
      taxType: [
        {
          label: this.$t('增值税'),
          value: 1
        },
        {
          label: this.$t('普通税'),
          value: 0
        }
      ],
      allowConsignList: [
        {
          label: this.$t('寄售'),
          value: 1
        },
        {
          label: this.$t('不寄售'),
          value: 0
        }
      ],
      rules: null,
      rules1: null,
      organizationId: '-99',
      organizationCode: 'organizationCode',
      organizationName: this.$t('默认数据源'),
      dataSource: [],
      edit: this.type !== 'preview' ? true : false
    }
  },
  computed: {
    text() {
      if (this.edit) return this.$t('保存')
      return this.$t('编辑')
    },
    disabled() {
      return !this.edit
    }
  },
  created() {
    this.promiseAll()
    this.companyList()
    this.init()
  },
  methods: {
    search() {
      this.$API.itemManagement
        .itemFindRelSites({ id: this.form.id, keyword: this.value })
        .then((res) => {
          this.dataSource = []
          res.data.forEach((ele) => {
            let obj = {
              text: ele.organizationName,
              ...ele
            }
            this.dataSource.push(obj)
          })
          console.log(res)
        })
    },
    promiseAll() {
      let arr = [
        this.$API.itemManagement.businessGroupNamelist({
          groupTypeCode: 'BG001WULIU'
        }),
        this.$API.itemManagement.businessGroupNamelist({
          groupTypeCode: 'BG001CG'
        })
      ]
      Promise.all(arr).then((res) => {
        res.forEach((ele, index) => {
          this[this.arr[index]] = ele.data
        })
      })
    },
    companyList(value) {
      console.log(value)
      let data = {}
      let str = 'itemTreelistOne'
      if (value) {
        str = 'itemTreelistTwo'
        this.form.purchaseUnitTypeName = value.itemData.name
        this.form.purchaseUnitTypeCode = value.itemData.itemCode
        this.$set(data, 'typeCode', value.itemData.itemCode)
      } else {
        this.$set(data, 'dictCode', 'unit')
        if (this.form.purchaseUnitTypeCode) {
          this.$API.itemManagement
            .itemTreelistTwo({
              typeCode: this.form.purchaseUnitTypeCode
            })
            .then((res) => {
              this.$set(this, 'defaultValueListTwo', res.data)
            })
        }
      }
      this.$API.itemManagement[str](data).then((res) => {
        if (value) this.$set(this, 'defaultValueListTwo', res.data)
        else this.$set(this, 'defaultValueListOne', res.data)
      })
    },
    companyLists(e) {
      this.form.purchaseUnitName = e.itemData.unitName
      this.form.purchaseUnitCode = e.itemData.unitCode
    },
    change(value, name) {
      console.log(value, name)
      this.ruleForm[name] =
        value.itemData.groupName ||
        value.itemData.unitName ||
        value.itemData.name ||
        value.itemData.label
    },
    changes(value, name) {
      console.log(value, name)
      this.ruleForms[name] =
        value.itemData.groupName ||
        value.itemData.unitName ||
        value.itemData.name ||
        value.itemData.label
    },
    holds() {
      console.log(this.edit)
      if (this.edit) {
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            let data = {
              ...this.ruleForm,
              itemId: this.form.id,
              organizationId: this.organizationId,
              itemOrgRelId: this.itemOrgRelId,
              organizationCode: this.organizationCode,
              organizationName: this.organizationName
            }
            let datas = {
              ...this.ruleForms,
              itemId: this.form.id,
              organizationId: this.organizationId,
              itemOrgRelId: this.itemOrgRelId,
              organizationCode: this.organizationCode,
              organizationName: this.organizationName,
              orderUnitId: this.ruleForm.purchaseUnitId
            }
            this.$API.itemManagement
              .itemPurchasingBothUpdate({
                basicUpdateRequest: data,
                purchasingUpdateRequest: datas
              })
              .then((res) => {
                console.log(res)
                this.edit = false
              })
          }
        })
      } else {
        this.edit = true
      }
    },
    init() {
      this.$API.itemManagement
        .itemPurchasingDetail({
          itemId: this.form.id,
          organizationCode: this.organizationCode,
          organizationId: this.organizationId
        })
        .then((res) => {
          this.ruleForms = res.data || {}
          this.ruleForms.allowConsign = this.ruleForms.allowConsign || 0
        })
      this.$API.itemManagement.itemPurchasingBasicDetail({ id: this.form.id }).then((res) => {
        this.ruleForm = res.data || {}
        this.ruleForm.allowMultiUnit = this.ruleForm.allowMultiUnit || 0
        console.log(this.ruleForm)
      })
      this.$API.itemManagement.itemPurchasingUpdateValid().then((res) => {
        this.rules1 = formatRules(res.data)
      })
      this.$API.itemManagement
        .itemFindRelSites({ id: this.form.id, keyword: this.value })
        .then((res) => {
          this.dataSource = []
          res.data.forEach((ele) => {
            let obj = {
              text: ele.organizationName,
              ...ele
            }
            this.dataSource.push(obj)
          })
          console.log(res)
        })
      this.$API.itemManagement.itemPurchasingBasicUpdateValid().then((res) => {
        this.rules = formatRules(res.data)
      })
    },
    handleSelectTab(item) {
      if (this.organizationId !== item.organizationId) {
        this.organizationId = item.organizationId
        this.itemOrgRelId = item.id
        this.organizationCode = item.organizationCode
        this.organizationName = item.organizationName
        this.init()
      }
      console.log(123123, item)
    }
  }
}
</script>

<style lang="scss" scoped>
.tabs /deep/ .e-tab-header {
  background: rgb(250, 250, 250);
}
.e-input-group {
  margin: 15px 0;
}
.icon {
  width: 3px;
  display: inline-block;
  height: 12px;
  background: rgba(0, 70, 156, 1);
  border-radius: 2px 0 0 2px;
}
.title {
  display: inline-block;
  margin-left: 2px;
  font-size: 16px;
  font-weight: 600;
  color: rgba(41, 41, 41, 1);
}
.active {
  background: #f5f6f9;
  color: #00469c !important;
}
.mt-form {
  display: flex;
  flex-wrap: wrap;
  .div {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    .mt-form-item {
      flex: 1;
      margin: 5px 10px;
    }
  }
}
</style>
