<template>
  <div class="set-country">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
    <mt-dialog
      css-class="create-proj-dialog"
      ref="addDialog"
      :header="headerTitle"
      :buttons="buttonsUser"
    >
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <!-- <div class="full-width dialog-subtitle mt-mb-20">{{ $t("创建角色") }}</div> -->
        <mt-form-item prop="itemCode" :label="$t('物料/品项编码')">
          <mt-input
            v-model="ruleForm.itemCode"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入物料/品项编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('物料/品项名称')">
          <mt-input
            v-model="ruleForm.itemName"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入物料/品项名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="itemDescription" :label="$t('规格型号')">
          <mt-input
            v-model="ruleForm.itemDescription"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入规格型号')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="oldItemCode" :label="$t('旧物料编码')">
          <mt-input
            v-model="ruleForm.oldItemCode"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入旧物料编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="baseMeasureUnitId" :label="$t('基本计量单位')">
          <div style="display: flex">
            <mt-select
              v-model="ruleForm.baseMeasureUnitTypeId"
              :data-source="defaultValueListOne"
              :fields="{ text: 'name', value: 'id' }"
              :show-clear-button="true"
              @change="companyList"
            ></mt-select>
            <mt-select
              v-model="ruleForm.baseMeasureUnitId"
              :data-source="defaultValueListTwo"
              :fields="{ text: 'unitName', value: 'id' }"
              :show-clear-button="true"
              @change="companyLists"
            ></mt-select>
          </div>
        </mt-form-item>
        <mt-form-item prop="itemGroupId" :label="$t('品项组')">
          <mt-select
            v-model="ruleForm.itemGroupId"
            :data-source="itemGroupList"
            :fields="{ text: 'name', value: 'id' }"
            :show-clear-button="true"
            @change="itemGroupChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="statusId" :label="$t('启用状态')">
          <mt-select
            v-model="ruleForm.statusId"
            :data-source="selectArr"
            :show-clear-button="true"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('请选择启用状态')"
            @change="statusIdChange"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import { config } from './data/index'
import { formatRules } from '@/utils/util'
export default {
  data() {
    return {
      pageConfig: config,
      ruleForm: {},
      rules: {},
      type: 'add',
      defaultValueListOne: [],
      defaultValueListTwo: [],
      itemGroupList: [],
      selectArr: [
        {
          label: this.$t('激活'),
          value: '1'
        },
        {
          label: this.$t('失效'),
          value: '3'
        }
      ],
      buttonsUser: [
        {
          click: this.cancelUser,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.nextStep,
          buttonModel: { isPrimary: 'true', content: this.$t('下一步') }
        },
        {
          click: this.saveUser,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  computed: {
    headerTitle() {
      if (this.type === 'add') {
        return this.$t('新增')
      }
      if (this.type === 'edit') {
        return this.$t('编辑')
      }
      return this.$t('预览')
    }
  },
  created() {
    this.companyList()
    this.init()
  },
  methods: {
    itemGroupChange(e) {
      this.ruleForm.itemGroupName = e.itemData.name
    },
    statusIdChange(e) {
      this.ruleForm.statusDescription = e.itemData.label
    },
    init() {
      let data = {}
      this.$API.itemManagement.itemGroupNamelist(data).then((res) => {
        console.log(res)
        this.$set(this, 'itemGroupList', res.data)
      })
    },
    companyList(value) {
      console.log(value)
      let data = {}
      let str = 'itemTreelistOne'
      if (value) {
        str = 'itemTreelistTwo'
        this.ruleForm.baseMeasureUnitTypeName = value.itemData.name
        this.ruleForm.baseMeasureUnitTypeCode = value.itemData.itemCode
        this.$set(data, 'typeCode', value.itemData.itemCode)
      } else {
        this.$set(data, 'dictCode', 'unit')
        if (this.ruleForm.baseMeasureUnitTypeCode) {
          this.$API.itemManagement
            .itemTreelistTwo({
              typeCode: this.ruleForm.baseMeasureUnitTypeCode
            })
            .then((res) => {
              this.$set(this, 'defaultValueListTwo', res.data)
            })
        }
      }
      this.$API.itemManagement[str](data).then((res) => {
        if (value) this.$set(this, 'defaultValueListTwo', res.data)
        else this.$set(this, 'defaultValueListOne', res.data)
      })
    },
    companyLists(e) {
      this.ruleForm.baseMeasureUnitName = e.itemData.unitName
      this.ruleForm.baseMeasureUnitCode = e.itemData.unitCode
    },
    cancelUser() {
      console.log('取消')
      this.$refs.addDialog.ejsRef.hide()
    },
    saveUser() {
      console.log('确定')
      this.add()
    },
    nextStep() {
      console.log('下一步')

      this.add(true)
    },
    add(value) {
      let str = 'itemUpdata'
      if (this.type === 'add') str = 'itemAdd'
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$API.itemManagement[str](this.ruleForm).then((res) => {
            if (value) {
              console.log(res)
              this.$router.push({
                path: 'matter-item-management-details',
                query: {
                  id: res?.data?.id || this.ruleForm.id,
                  type: 'edit'
                }
              })
            }
            this.$refs.templateRef.refreshCurrentGridData()
            this.cancelUser()
          })
        }
      })
    },
    rulesGet(type) {
      this.$API.itemManagement[type]().then((res) => {
        this.rules = formatRules(res.data)
      })
    },
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const rowSelected = grid?.getSelectedRecords?.()
      if (toolbar.id === 'Add') {
        this.type = 'add'
        this.ruleForm = Object.assign({}, {})
        this.rulesGet('itemAddValid')
        this.$refs.addDialog.ejsRef.show()
        console.log('新增')
      }
      if (toolbar.id == 'import' && rowSelected.length) {
        console.log(e)
        // visibleCondition
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDelete(rowSelected)
        console.log('删除')
      }
    },
    handleClickCellTool(e) {
      this.$set(this, 'ruleForm', {})
      const { data, tool } = e
      console.log(data, tool)
      if (tool.id === 'edit') {
        this.type = 'edit'
        this.rulesGet('itemUpdataValid')
        this.$refs.ruleForm.resetFields()
        this.ruleForm = Object.assign({}, data)
        console.log(this.ruleForm)
        this.companyList()
        this.init()
        console.log(this.defaultValueListOne)
        console.log(this.defaultValueListTwo)
        this.$refs.addDialog.ejsRef.show()
      }
      if (tool.id === 'delete') {
        this.handleDelete([data])
      }
    },
    handleClickCellTitle(e) {
      //这里是跳转
      this.$router.push({
        path: 'matter-item-management-details',
        query: {
          id: e.data.id,
          type: 'preview'
        }
      })
      console.log(this.$t('跳转'), e)
    },
    handleDelete(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          console.log(data)
          this.$API.itemManagement.batchDelete(data).then(() => {
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.set-country /deep/ .lick {
  color: #005ca9 !important;
  cursor: pointer;
}
.set-country {
  height: 100%;
}
</style>
