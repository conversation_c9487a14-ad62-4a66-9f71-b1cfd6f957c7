<template>
  <div class="set-country mat-detail">
    <div style="background: rgb(250, 250, 250); display: flex; height: 100%">
      <div :class="hide_show ? 'ce' : 'ce show'">
        <div v-show="hide_show" class="left mt-mt-20">
          <div class="mt-pa-20 left-border">
            <div class="left-title mt-mb-20">{{ $t('物料品项管理') }}</div>
            <div class="e-input-group flex items-center">
              <mt-icon name="icon_search" class="mt-mr-10 search-icon cursor-pointer"></mt-icon>
              <input
                v-model="paramObj.keyword"
                class="e-input"
                type="text"
                @change="search"
                :placeholder="$t('请输入物料编号、名称、规格或型号')"
              />
            </div>
          </div>
          <div
            v-for="(item, index) in arr"
            :key="index"
            :class="item.id === id ? 'itemHover item' : 'item'"
            @click="click(item)"
          >
            <div class="item_id">{{ item.itemCode }}</div>
            <div class="item_name">{{ item.itemDescription }}</div>
          </div>
        </div>
        <div
          style="
            position: absolute;
            right: -10px;
            top: 65px;
            width: 12px;
            height: 60px;
            line-height: 60px;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 0 4px 4px 0;
          "
          @click="hide"
        >
          <i
            style="color: white"
            :class="
              hide_show ? 'mt-icons mt-icon-icon_arrow_left' : 'mt-icons mt-icon-icon_arrow_right'
            "
          ></i>
        </div>
      </div>
      <div style="flex: 1; height: 100%">
        <div style="height: 100%">
          <div style="width: 100%; background: white; border-radius: 8px">
            <div style="padding: 20px; width: 100%; margin-top: 20px">
              <div style="text-align: right; padding: 0 20px; color: #00469c">
                <span style="cursor: pointer" @click="back">{{ $t('返回') }}</span>
              </div>
              <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
                <div class="div">
                  <mt-form-item prop="itemCode" :label="$t('物料/品项编码')">
                    <mt-input
                      v-model="ruleForm.itemCode"
                      :show-clear-button="true"
                      :disabled="true"
                      type="text"
                      :placeholder="$t('请输入物料/品项编码')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="itemName" :label="$t('物料/品项名称')">
                    <mt-input
                      v-model="ruleForm.itemName"
                      :show-clear-button="true"
                      :disabled="true"
                      type="text"
                      :placeholder="$t('请输入物料/品项名称')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="itemDescription" :label="$t('规格型号')">
                    <mt-input
                      v-model="ruleForm.itemDescription"
                      :show-clear-button="true"
                      :disabled="true"
                      type="text"
                      :placeholder="$t('请输入规格型号')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="baseMeasureUnitName" :label="$t('基本计量单位')">
                    <mt-input
                      v-model="ruleForm.baseMeasureUnitName"
                      :show-clear-button="true"
                      :disabled="true"
                      type="text"
                      :placeholder="$t('请输入基本计量单位')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="itemGroupName" :label="$t('品项组')">
                    <mt-input
                      v-model="ruleForm.itemGroupName"
                      :show-clear-button="true"
                      :disabled="true"
                      type="text"
                      :placeholder="$t('请输入品项组')"
                    ></mt-input>
                  </mt-form-item>
                </div>
                <div class="div">
                  <mt-form-item prop="oldItemCode" :label="$t('旧物料编码')">
                    <mt-input
                      v-model="ruleForm.oldItemCode"
                      :show-clear-button="true"
                      :disabled="true"
                      type="text"
                      :placeholder="$t('请输入旧物料编码')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="statusDescription" :label="$t('启用状态')">
                    <mt-input
                      v-model="ruleForm.statusDescription"
                      :show-clear-button="true"
                      :disabled="true"
                      type="text"
                      :placeholder="$t('请输入更新人')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="updateUserName" :label="$t('更新人')">
                    <mt-input
                      v-model="ruleForm.updateUserName"
                      :show-clear-button="true"
                      :disabled="true"
                      type="text"
                      :placeholder="$t('请输入更新人')"
                    ></mt-input>
                  </mt-form-item>
                  <mt-form-item prop="updateTime" :label="$t('更新时间')">
                    <mt-input
                      v-model="ruleForm.updateTime"
                      :show-clear-button="true"
                      :disabled="true"
                      type="text"
                      :placeholder="$t('请输入更新时间')"
                    ></mt-input>
                  </mt-form-item>
                  <div class="mt-form-item"></div>
                </div>
              </mt-form>
            </div>
          </div>
          <div style="background: white; margin-top: 20px; height: 100%">
            <div>
              <mt-tabs
                :e-tab="false"
                :selected-item="selectedItem"
                :data-source="dataSource"
                @handleSelectTab="handleSelectTab"
              ></mt-tabs>
            </div>
            <div style="height: 100%">
              <component
                v-if="show"
                :is="tabs_key"
                :form="ruleForm"
                :rules="rules"
                :type="type"
                :development="ruleForm.development"
                :business-properties="ruleForm.businessProperties"
                :transport="ruleForm.transport"
                @holds="holds"
                @rulesGet="rulesGet"
              ></component>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import BasicNews from '../components/basicNews/Index.vue'
import plan from '../components/plan/Index.vue'
import purchase from '../components/purchase/Index.vue'
import store from '../components/store/Index.vue'
import quality from '../components/quality/Index.vue'
import { dataSource } from '../data/details'
import { formatRules } from '@/utils/util'

export default {
  components: {
    BasicNews,
    plan,
    purchase,
    store,
    quality
  },
  data() {
    return {
      dataSource: dataSource,
      show: false,
      // pageSettings: { pageSize: 10, pageCount: 1 },
      // totalPages: 10,
      buttonsUser: [
        {
          click: this.cancelUser,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.saveUser,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      value: '',
      ruleForm: {
        development: {},
        businessProperties: {},
        transport: {}
      },
      rules: null,
      asyncSettings: {
        saveUrl: 'https://ej2.syncfusion.com/services/api/uploadbox/Save',
        removeUrl: 'https://ej2.syncfusion.com/services/api/uploadbox/Remove'
      },
      tabs_key: 'BasicNews',
      selectedItem: 0,
      arr: [],
      id: null,
      paramObj: {
        currentPage: 1,
        keyword: '',
        pageSize: 10
      },
      hide_show: true,
      type: 'preview'
      // pageConfig: config
    }
  },
  created() {
    this.id = this.$route.query.id ? this.$route.query.id : null
    this.type = this.$route.query.type ? this.$route.query.type : 'preview'
    console.log(this.$route.query)
    this.details_fn(true)
  },
  methods: {
    back() {
      this.$router.go(-1) //返回上一层
    },
    search(e) {
      this.init()
      console.log(e.target.value)
    },
    hide() {
      this.hide_show = !this.hide_show
    },
    goToPage(num) {
      console.log('num:', num)
    },
    changePageSize(size) {
      console.log('size:', size)
    },
    click(item) {
      this.id = item.id

      this.tabs_key = 'BasicNews'
      this.selectedItem = 0
      this.details_fn()
    },
    holds(form) {
      this.$API.itemManagement.itemUpdatas(form).then(() => {
        this.details_fn()
      })
    },
    rulesGet(type) {
      this.$API.itemManagement[type]().then((res) => {
        this.rules = formatRules(res.data)
      })
    },
    init() {
      this.$API.itemManagement.itemlist(this.paramObj).then((res) => {
        this.$set(this, 'arr', res.data.records)
      })
    },
    details_fn(value) {
      this.show = false
      if (value) {
        this.init()
      }
      console.log(this.id)
      // const data = `id=${this.id}`;
      const data = {
        id: this.id
      }
      this.$API.itemManagement.itemDetails(data).then((res) => {
        this.$set(this, 'ruleForm', res.data)
        this.ruleForm['transport'] = res.data?.transport || {}
        this.ruleForm['businessProperties'] = res.data?.businessProperties || {}
        this.ruleForm['development'] = res.data?.development || {}
        this.show = true
      })
    },
    handleSelectTab(item, i) {
      this.tabs_key = i.key
      console.log(this.tabs_key)
      console.log(item)
      console.log(i)
    },
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const rowSelected = grid?.getSelectedRecords?.()

      if (toolbar.id === 'Add') {
        // this.handleAction("add");
        console.log('新增')
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDelete(rowSelected)
        console.log('删除')
      }
    },
    handleClickCellTool(e) {
      const { data, tool } = e
      console.log(data, tool)
      if (tool.id === 'edit') {
        this.$router.push('')
      }
      if (tool.id === 'delete') {
        this.handleDelete([data])
      }
    },
    handleClickCellTitle(e) {
      //这里是跳转
      console.log(this.$t('跳转'), e)
    },
    handleDelete(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          console.log(data)
          // this.$API.itemManagement.batchDelete(data).then(() => {
          //   this.$refs.tepPage.refreshCurrentGridData();
          // });
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.mat-detail {
  .left {
    background: #fff;
    &-border {
      border-bottom: 1px solid #e8e8e8;
    }
    &-title {
      font-size: 18px;
    }
  }
}
</style>
<style lang="scss" scoped>
.set-country /deep/ .lick {
  color: #005ca9 !important;
  cursor: pointer;
}
.set-country /deep/ .mt-tabs-container,
.set-country /deep/ .e-disabled {
  background: white !important;
}
.set-country {
  height: 100%;
  overflow: hidden;
  .ce {
    width: 20%;
    height: 100%;
    margin: 0 10px;
    position: relative;
  }
  .show {
    width: 0 !important;
  }
  .left {
    border-radius: 8px;
  }
  .mt-form {
    display: flex;
    flex-wrap: wrap;
    .div {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      .mt-form-item {
        flex: 1;
        margin: 5px 10px;
      }
    }
  }
  .itemHover {
    background: #f5f6f9;
  }
  .item {
    height: 70px;
    padding: 15px 20px;
    cursor: pointer;
    .item_id {
      height: 20px;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(0, 70, 156, 1);
    }
    .item_name {
      height: 20px;
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(154, 154, 154, 1);
    }
  }
}
.e-input-group {
  /deep/.mt-icons {
    font-family: element-icons, e-icons;
    font-style: normal;
    font-variant: normal;
    font-weight: 400;
    line-height: 38px;
    text-transform: none;
  }
  .search-icon {
    color: #98aac3;
  }
}
</style>
