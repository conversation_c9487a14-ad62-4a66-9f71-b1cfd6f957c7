<!--
 * @Author: your name
 * @Date: 2022-01-12 16:23:52
 * @LastEditTime: 2022-02-28 17:34:29
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\matterItemManagement\details\ItemInfo.vue
-->
<template>
  <div class="info flex justify-between mt-pt-20 full-height">
    <div :class="['left full-height', isShow ? 'left-show' : 'left-hide']">
      <div v-show="isShow" class="left-header mt-pa-20">
        <div class="left-header--title mt-mb-20">{{ $t('物料品项管理') }}</div>
        <div class="e-input-group left-header--search">
          <mt-icon
            name="icon_search"
            class="left-header--search__icon mt-mr-10 cursor-pointer"
          ></mt-icon>
          <input
            v-model="searchForm.keyword"
            class="e-input"
            type="text"
            @input="getList"
            :placeholder="$t('请输入物料编号、名称、规格或型号')"
          />
        </div>
      </div>
      <div class="left-list">
        <div
          v-for="item in itemList"
          :key="item.id"
          :class="['left-list--item', currentItem.id === item.id ? 'active' : '']"
          @click="itemChange(item)"
        >
          <div class="left-list--item__title">{{ item.itemCode }}</div>
          <div class="left-list--item__subtitle">
            {{ item.itemDescription }}
          </div>
        </div>
      </div>
      <div class="left-arrow" @click="isShow = !isShow">
        <mt-icon :name="isShow ? 'icon_arrow_left' : 'icon_arrow_right'"></mt-icon>
      </div>
    </div>
    <div :class="['right full-height', isShow ? 'right-show' : 'right-hide']">
      <div class="item-info mt-pa-20">
        <div class="flex justify-end mt-mb-20">
          <mt-button
            css-class="e-flat"
            :is-primary="true"
            @click="$router.push('/masterdata/matter-item-management')"
            >{{ $t('返回') }}</mt-button
          >
        </div>
        <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
          <mt-row :gutter="20">
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="itemCode" :label="$t('物料/品项编码')">
                <mt-input
                  v-model="ruleForm.itemCode"
                  :show-clear-button="true"
                  :disabled="true"
                  type="text"
                  :placeholder="$t('请输入物料/品项编码')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="itemName" :label="$t('物料/品项名称')">
                <mt-input
                  v-model="ruleForm.itemName"
                  :show-clear-button="true"
                  :disabled="true"
                  type="text"
                  :placeholder="$t('请输入物料/品项名称')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="itemDescription" :label="$t('规格型号')">
                <mt-input
                  v-model="ruleForm.itemDescription"
                  :show-clear-button="true"
                  :disabled="true"
                  type="text"
                  :placeholder="$t('请输入规格型号')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="baseMeasureUnitName" :label="$t('基本计量单位')">
                <mt-input
                  v-model="ruleForm.baseMeasureUnitName"
                  :show-clear-button="true"
                  :disabled="true"
                  type="text"
                  :placeholder="$t('请输入基本计量单位')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="itemGroupName" :label="$t('品项组')">
                <mt-input
                  v-model="ruleForm.itemGroupName"
                  :show-clear-button="true"
                  :disabled="true"
                  type="text"
                  :placeholder="$t('请输入品项组')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="oldItemCode" :label="$t('旧物料编码')">
                <mt-input
                  v-model="ruleForm.oldItemCode"
                  :show-clear-button="true"
                  :disabled="true"
                  type="text"
                  :placeholder="$t('请输入旧物料编码')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="statusDescription" :label="$t('启用状态')">
                <mt-input
                  v-model="ruleForm.statusDescription"
                  :show-clear-button="true"
                  :disabled="true"
                  type="text"
                  :placeholder="$t('请输入更新人')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="updateUserName" :label="$t('更新人')">
                <mt-input
                  v-model="ruleForm.updateUserName"
                  :show-clear-button="true"
                  :disabled="true"
                  type="text"
                  :placeholder="$t('请输入更新人')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="updateTime" :label="$t('更新时间')">
                <mt-input
                  v-model="ruleForm.updateTime"
                  :show-clear-button="true"
                  :disabled="true"
                  type="text"
                  :placeholder="$t('请输入更新时间')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
          </mt-row>
        </mt-form>
      </div>

      <div class="item-detail mt-mt-20">
        <div class="item-detail--tabs flex justify-between items-center mt-px-10">
          <mt-tabs
            :e-tab="false"
            :data-source="dataSource"
            overflow-mode="Popup"
            @handleSelectTab="tabSelected"
          ></mt-tabs>
          <mt-button
            v-if="!isEdit"
            css-class="e-flat mt-mr-10"
            :is-primary="true"
            @click="isEdit = !isEdit"
            >{{ $t('编辑') }}</mt-button
          >
          <mt-button v-else css-class="e-flat mt-mr-10" :is-primary="true" @click="save">{{
            $t('保存')
          }}</mt-button>
        </div>
        <div class="item-detail--pannel">
          <!-- <div style="height: 1000px"></div> -->
          <component ref="viewRef" :is="view" :is-edit="isEdit" :child-data="childData"></component>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { utils } from '@mtech-common/utils'
import Base from '../components/Base.vue'
import Plan from '../components/Plan.vue'
import Purchase from '../components/Purchase.vue'
import Storage from '../components/Storage.vue'
import Quality from '../components/Quality.vue'

export default {
  components: {
    Base,
    Plan,
    Purchase,
    Storage,
    Quality
  },
  data() {
    return {
      searchForm: {
        keyword: ''
      },
      itemList: [],
      currentItem: {},

      ruleForm: {},
      rules: {},

      dataSource: [
        { title: this.$t('基础数据'), view: 'Base' },
        { title: this.$t('计划'), view: 'Plan' },
        { title: this.$t('采购'), view: 'Purchase' },
        { title: this.$t('存储'), view: 'Storage' },
        { title: this.$t('质量'), view: 'Quality' }
      ],
      selectedIndex: 0,

      isShow: true,
      isEdit: false
    }
  },
  computed: {
    view() {
      return this.dataSource[this.selectedIndex].view
    },
    childData() {
      let data = {}

      switch (this.selectedIndex) {
        case 0:
          data = this.ruleForm
          break
        default:
          data = { id: this.ruleForm.id }
          break
      }
      return data
    }
  },
  mounted() {
    this.getList = utils.debounce(this.getList, 500)
    this.getList()
  },
  methods: {
    getList() {
      const query = {
        currentPage: 1,
        pageSize: 10,
        ...this.searchForm
      }
      this.$API.itemManagement.itemlist(query).then(async (res) => {
        const data = res.data.records
        const currentItemId = this.$route.query.id
        const currentIndex = data.findIndex((e) => e.id === currentItemId)

        this.itemList = data
        if (currentIndex > -1) {
          this.itemChange(data[currentIndex])
        } else {
          await this.getDetails()
          this.itemList.unshift(this.ruleForm)
          this.itemChange(data[0])
        }
      })
    },
    itemChange(item) {
      this.currentItem = item
      this.getDetails()
    },
    async getDetails() {
      const query = {
        id: this.currentItem.id || this.$route.query.id
      }
      await this.$API.itemManagement.itemDetails(query).then((res) => {
        this.ruleForm = res.data
      })
    },
    tabSelected(e) {
      this.selectedIndex = e
    },
    save() {
      // TODO: 1、保存失败时，交互优化 2、表单校验
      this.$refs.viewRef.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.isEdit = !this.isEdit
          const data = this.$refs.viewRef.dataEmit()
          if (this.selectedIndex === 0) {
            this.$API.itemManagement.itemUpdatas(data).then(() => {})
          } else if (this.selectedIndex === 1) {
            this.$API.itemManagement.itemPlanningUpdate(data).then(() => {})
          } else if (this.selectedIndex === 2) {
            this.$API.itemManagement.itemPurchasingBothUpdate(data).then(() => {})
          } else if (this.selectedIndex === 3) {
            this.$API.itemManagement.itemWarehouseBothUpdate(data).then(() => {})
          } else if (this.selectedIndex === 4) {
            this.$API.itemManagement.itemQualityUpdate(data).then(() => {})
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .mt-tabs {
  background: none;
}
/deep/#tabElment {
  height: 36px !important;
}
.info {
  .left {
    position: relative;
    background-color: #fff;
    border-radius: 8px;
    &-show {
      width: 281px;
    }
    &-hide {
      width: 0px;
    }
    &-header {
      border-bottom: 1px solid #e8e8e8;
      &--title {
        font-size: 20px;
        font-family: PingFangSC;
        font-weight: 600;
        color: rgba(41, 41, 41, 1);
      }
      &--search {
        &__icon {
          margin-top: 7px;
        }
      }
    }
    &-list {
      height: calc(100% - 136px);
      padding: 5px 0;
      overflow: auto;
      &--item {
        height: 70px;
        padding: 15px 20px;
        &__title {
          height: 20px;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(0, 70, 156, 1);
        }
        &__subtitle {
          height: 19px;
          font-size: 12px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(154, 154, 154, 1);
        }
      }
      .active {
        background: rgba(245, 246, 249, 1);
      }
    }
    &-arrow {
      position: absolute;
      right: -12px;
      top: 85px;
      width: 12px;
      height: 60px;
      line-height: 60px;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 0 4px 4px 0;
      font-size: 12px;
      color: #fff;
    }
  }
  .right {
    &-show {
      width: calc(100% - 291px);
    }
    &-hide {
      width: calc(100% - 10px);
    }
    .item-info {
      background-color: #fff;
      height: 228px;
      border: 1px solid rgba(232, 232, 232, 1);
      border-radius: 8px;
      /deep/ .label {
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(154, 154, 154, 1);
        margin-bottom: 10px;
      }
    }

    .item-detail {
      height: calc(100% - 248px);
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(232, 232, 232, 1);
      border-radius: 8px;
      &--tabs {
        height: 52px;
        border-bottom: 1px solid rgba(232, 232, 232, 1);
      }
      &--pannel {
        height: calc(100% - 52px);
      }
    }
  }
}
</style>
