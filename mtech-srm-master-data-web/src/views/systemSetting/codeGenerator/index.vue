<template>
  <div class="code-generator">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>

    <!-- <div class="left-tree">
      <div class="titles">{{ $t("业务类型") }}</div>
      <mt-treeView :fields="fileds"></mt-treeView>
    </div>
    <div class="right-box">
      <div class="top-title">{{ $t("采购订单") }}</div>
      <div class="bottom-table">
        <mt-horizontal-list
          :hidden-tabs="true"
          :data-source="pageConfig"
          @handleClickToolBar="handleClickToolBar"
          @handleClickCellTool="handleClickCellTool"
          @handleGridCurrentChange="handleGridCurrentChange"
          @handleGridSizeChange="handleGridSizeChange"
          @handleQuerySearch="handleQuerySearch"
          @handleQueryReset="handleQueryReset"
        ></mt-horizontal-list>
      </div>
    </div> -->

    <!-- <add-side-bar
      v-if="showDefault"
      :add-detail-show.sync="addDetailShow"
    ></add-side-bar> -->
    <add-drawer
      v-if="addDetailShow"
      :add-detail-show.sync="addDetailShow"
      :model="rowInfo"
      @confirmSuccess="handleQueryReset"
    ></add-drawer>
  </div>
</template>

<script>
// import AddDrawer from './component/addDrawer.vue';
import { columnData } from './data'
export default {
  components: {
    // addSideBar: require("./component/addSideBar.vue").default
    addDrawer: require('./component/addDrawer.vue').default
  },
  data() {
    return {
      fileds: {
        dataSource: [],
        id: 'id',
        text: 'name',
        child: 'subChild'
      },
      currentTabIndex: 0,
      queryBuilderRules: null,
      pageConfig: [
        {
          toolbar: {
            tools: [
              // ["Add", "Delete"],
              // ["Filter", "Refresh", "Setting"],
            ]
          },
          grid: {
            columnData: columnData,
            ignoreFields: ['scopeDesc', 'scopeNames'],
            dataSource: [],
            asyncConfig: {
              url: `/masterDataManagement/tenant/code-generator/paged-query`
            }
          }
        }
      ],
      showDefault: false,
      addDetailShow: false,
      rowInfo: null
    }
  },

  created() {
    this.showTools()
  },

  mounted() {
    // this.getData();
  },

  methods: {
    getData() {
      let params = {
        page: {
          size: this.pageConfig[this.currentTabIndex].grid.pageSettings.pageSize,
          current: this.pageConfig[this.currentTabIndex].grid.pageSettings.currentPage
        }
      }
      if (this.queryBuilderRules) params = { ...params, ...this.queryBuilderRules }

      this.$loading()
      this.$API.systemSetting.getGenerator(params).then((res) => {
        this.$hloading()
        this.$set(this.pageConfig[this.currentTabIndex].grid, 'dataSource', res.data.records)
        this.$set(
          this.pageConfig[this.currentTabIndex].grid.pageSettings,
          'totalRecordsCount',
          +res.data.total
        )
      })
    },
    showTools() {
      this.$API.org.getUserInfo({}).then((resData) => {
        let userInfo = resData.data
        this.showPageTools(userInfo.tenantId)
      })
    },
    showPageTools(tenantId) {
      let tools = []
      if (tenantId === '-99') {
        tools.push('Add')
      }
      tools.push('Delete')
      if (tenantId === '-99') {
        tools.push(
          {
            id: 'json_import',
            icon: 'icon_solid_Import',
            title: this.$t('导入(JSON)')
            // permission: ['O_02_1338'],
          },
          {
            id: 'json_export',
            icon: 'icon_solid_pushorder',
            title: this.$t('导出(JSON)')
            // permission: ['O_02_1339'],
          }
        )
      }
      this.pageConfig[this.currentTabIndex].toolbar.tools.push(tools)
      this.pageConfig[this.currentTabIndex].toolbar.tools.push(['Filter', 'Refresh', 'Setting'])
    },
    handleClickToolBar(e) {
      console.log(e.grid.getSelectedRecords(), e)
      if (
        e.grid.getSelectedRecords().length <= 0 &&
        !(
          e.toolbar.id == 'Add' ||
          e.toolbar.id == 'Filter' ||
          e.toolbar.id == 'Refresh' ||
          e.toolbar.id == 'Setting' ||
          e.toolbar.id === 'json_import'
        )
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _id = []
      e.grid.getSelectedRecords().map((item) => _id.push(item.id))
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'Delete') {
        this.handleDelete(_id)
      } else if (e.toolbar.id === 'json_import') {
        this.$dialog({
          modal: () => import('./component/importDialog.vue'),
          data: {
            title: this.$t('导入JSON')
          },
          success: (res) => {
            this.$toast({
              content: res.msg ? res.msg : this.$t('导入成功'),
              type: 'success'
            })
            this.handleQueryReset()
          }
        })
      } else if (e.toolbar.id === 'json_export') {
        this.$API.dict
          .exportCodeGeneratorJson(_id)
          .then((res) => {
            const { code, data } = res
            if (code === 200) {
              this.$dialog({
                data: {
                  title: this.$t('复制到剪贴板(原有剪贴板内容将会被覆盖)'),
                  // message: '复制当前导出内容将会覆盖原有剪贴板内容，您是否确认复制到剪贴板？'
                  message: data
                },
                success: () => {
                  this.copyToClipboard(JSON.stringify(data))
                  this.$toast({
                    content: res.message ? res.message : this.$t('复制成功'),
                    type: 'success'
                  })
                }
              })
            }
          })
          .catch((err) => {
            console.log(err)
          })
      }
    },

    copyToClipboard(str) {
      const el = document.createElement('textarea')
      el.value = str
      el.setAttribute('readonly', '')
      el.style.position = 'absolute'
      el.style.left = '-9999px'
      document.body.appendChild(el)
      el.select()
      document.execCommand('copy')
      document.body.removeChild(el)
    },

    handleClickCellTool(e) {
      if (e.tool.id == 'delete') {
        this.handleDelete([e.data.id])
      } else if (e.tool.id == 'edit' || e.tool.id == 'copy') {
        let _row = e.data
        delete _row.cellTools
        delete _row.column
        delete _row.gridTemplate
        delete _row.gridRef
        delete _row.tabIndex
        this.handleEdit(_row)
      }
    },

    handleAdd() {
      this.showDefault = true
      this.addDetailShow = true
      this.rowInfo = {
        id: null,
        generatorCode: '',
        generatorName: '',
        timeRange: [],
        dialogType: 'add'
      }
    },

    handleEdit(row) {
      this.showDefault = true
      this.addDetailShow = true
      this.rowInfo = {
        id: row.id,
        dialogType: 'edit'
      }
    },

    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('确认删除？')
        },
        success: () => {
          this.$loading()
          this.$API.systemSetting.deleteGenerator({ ids: ids }).then((res) => {
            this.$hloading()
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.handleQueryReset()
            }
          })
        }
      })
    },

    // -----------------查询--------------------------
    handleQuerySearch(rules) {
      this.queryBuilderRules = rules
      console.log('rules', rules)
      this.initPageInfo()
      this.getData()
    },

    handleQueryReset() {
      this.queryBuilderRules = null
      this.initPageInfo()
      this.getData()
    },

    // -----------------页码--------------------------
    initPageInfo() {
      // this.$set(this.pageConfig[0].grid.pageSettings, "currentPage", 1);
      this.$refs.templateRef.refreshCurrentGridData()
    },
    handleGridCurrentChange(e) {
      console.log('页码改变，', e, e.currentPage)
      this.$set(this.pageConfig[0].grid.pageSettings, 'currentPage', +e.currentPage)
      this.getData()
    },
    // 每页size改变
    handleGridSizeChange(e) {
      console.log('页码size改变，', e)
      this.initPageInfo()
      this.$set(this.pageConfig[0].grid.pageSettings, 'pageSize', +e.count)
      this.getData()
    }
  }
}
</script>

<style lang="scss" scoped>
.code-generator {
  width: 100%;
  height: 100%;
  padding-top: 20px;
  display: flex;
  .left-tree {
    width: 400px;
    height: 100%;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px 0 0 0;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

    .titles {
      font-size: 16px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(35, 43, 57, 1);
      margin: 20px;
    }
  }
  .right-box {
    flex: 1;
    display: flex;
    flex-direction: column;

    .top-title {
      width: 100%;
      font-size: 20px;
      padding: 20px;
      line-height: 1;
      background: linear-gradient(rgba(99, 134, 193, 0.06), rgba(99, 134, 193, 0.06)),
        linear-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 1));
      border: 1px solid rgba(232, 232, 232, 1);
      border-left: unset;
      border-radius: 0 8px 0 0;
    }

    .bottom-table {
      padding: 20px 0 0 20px;
      width: 100%;
      flex: 1;

      .common-template-page {
        background: transparent;
      }
    }
  }
}
</style>
