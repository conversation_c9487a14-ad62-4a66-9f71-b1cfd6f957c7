import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    // width: "180",
    field: 'generatorCode',
    headerText: i18n.t('业务编码'),
    cellTools: [
      {
        id: 'copy',
        icon: 'copy',
        title: i18n.t('复制'),
        visibleCondition: (data) => {
          return !data.editable
        }
      },
      {
        id: 'edit',
        icon: 'table-edit',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data.editable
        }
      },
      {
        id: 'delete',
        icon: 'table-delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data.editable
        }
      }
    ]
  },
  {
    // width: "150",
    field: 'generatorName',
    headerText: i18n.t('规则名称')
  },
  {
    // width: "80",
    field: 'scopeDesc',
    headerText: i18n.t('适用类别'),
    ignore: true,
    allowFiltering: false
  },
  {
    // width: "150",
    field: 'scopeNames',
    headerText: i18n.t('适用范围'),
    ignore: true,
    allowFiltering: false
  },
  // {
  //   width: "100",
  //   field: "areaLevelDescription",
  //   headerText: i18n.t("重置规则")
  // },
  // {
  //   width: "150",
  //   field: "countryName",
  //   headerText: i18n.t("步长")
  // },
  // {
  //   width: "150",
  //   field: "wholeName",
  //   headerText: i18n.t("序列长度")
  // },
  // {
  //   width: "150",
  //   field: "telephoneCode",
  //   headerText: i18n.t("当前流水号")
  // },
  // {
  //   width: "150",
  //   field: "zipCode",
  //   headerText: i18n.t("可用数量")
  // },
  {
    // width: "150",
    field: 'startTime',
    headerText: i18n.t('生效日期')
  },
  {
    // width: "150",
    field: 'endTime',
    headerText: i18n.t('失效日期')
  },
  // {
  //   width: "150",
  //   field: "timeZoneName",
  //   headerText: i18n.t("编码详情")
  // },
  {
    // width: "150",
    field: 'generatorDescription',
    headerText: i18n.t('描述')
  },
  {
    // width: "150",
    field: 'updateTime',
    headerText: i18n.t('修改时间')
  }
]
// 参数类型
export const PARAM_TYPE_ENUM = {
  custom: { paramType: 0, paramTypeName: i18n.t('自定义') },
  serial: { paramType: 1, paramTypeName: i18n.t('流水号') },
  time: { paramType: 2, paramTypeName: i18n.t('时间') },
  consts: { paramType: 3, paramTypeName: i18n.t('固定值') },
  //  fields: { paramType: 4, paramTypeName: i18n.t("业务参数") },
  random: { paramType: 5, paramTypeName: i18n.t('随机数') }
}

export const SCOPE_TYPE_ENUM = {
  SAAS: { code: 0, desc: i18n.t('全平台') },
  TENANT: { code: 1, desc: i18n.t('租户内') },
  ORG: { code: 2, desc: i18n.t('组织机构') },
  DEPT: { code: 3, desc: i18n.t('部门') }
}

export const TIME_FORMATS = [
  'yy',
  'MM',
  'ww',
  'dd',
  'HH',
  'mm',
  'ss',
  'SSS',
  'yyyy',
  'yyMM',
  'yyyyMM',
  'yyMMdd',
  'yyyyMMdd',
  'yyMMddHHmm',
  'yyyyMMddHHmm'
]

export const RESET_CYCLE_TYPE = {
  0: i18n.t('永不'),
  1: i18n.t('按天重置'),
  2: i18n.t('按周重置'),
  3: i18n.t('按月重置'),
  4: i18n.t('按年重置')
}
