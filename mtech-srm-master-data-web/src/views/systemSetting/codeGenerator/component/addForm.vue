<template>
  <mt-form ref="ruleForm" :model="addForm" :rules="rules">
    <mt-form-item prop="paramType" :label="$t('类型')">
      <mt-select
        v-model.number="addForm.paramType"
        :data-source="typeList"
        :placeholder="$t('请选择类型')"
        :fields="{ text: 'paramTypeName', value: 'paramType' }"
        @change="handleTypeChange"
      ></mt-select>
    </mt-form-item>

    <mt-form-item prop="paramCode" :label="$t('代码')">
      <mt-input
        v-model.trim="addForm.paramCode"
        :show-clear-button="true"
        :placeholder="$t('请输入代码')"
      ></mt-input>
    </mt-form-item>

    <!-- 流水号 -->
    <mt-form-item prop="minValue" :label="$t('最小值')" v-show="addForm.paramType == 1">
      <mt-input-number
        v-model="addForm.minValue"
        :min="1"
        :show-clear-button="true"
        :placeholder="$t('请输入最小值')"
      ></mt-input-number>
    </mt-form-item>

    <mt-form-item prop="incrValue" :label="$t('增量值')" v-show="addForm.paramType == 1">
      <mt-input-number
        v-model="addForm.incrValue"
        :min="1"
        :show-clear-button="true"
        :placeholder="$t('请输入增量值')"
      ></mt-input-number>
    </mt-form-item>

    <mt-form-item prop="digit" :label="$t('位数')" v-show="addForm.paramType == 1">
      <mt-input-number
        v-model="addForm.digit"
        :min="1"
        :show-clear-button="true"
        :placeholder="$t('请输入位数')"
      ></mt-input-number>
    </mt-form-item>

    <mt-form-item prop="autoFill" :label="$t('是否自动填充')" v-show="addForm.paramType == 1">
      <mt-select
        v-model="addForm.autoFill"
        :data-source="autoFillInList"
        :fields="{ text: 'label', value: 'value' }"
        :placeholder="$t('请选择是否自动填充')"
      ></mt-select>
    </mt-form-item>

    <mt-form-item prop="recycle" :label="$t('是否可回收')" v-show="addForm.paramType == 1">
      <mt-select
        v-model="addForm.recycle"
        :data-source="autoFillInList"
        :fields="{ text: 'label', value: 'value' }"
        :placeholder="$t('请选择是否可回收')"
      ></mt-select>
    </mt-form-item>

    <mt-form-item prop="resetCycle" :label="$t('起始值重置周期')" v-show="addForm.paramType == 1">
      <mt-select
        v-model="addForm.resetCycle"
        :data-source="resetList"
        :show-clear-button="true"
        :placeholder="$t('请选择起始值重置周期')"
      ></mt-select>
    </mt-form-item>

    <!-- 时间戳 -->
    <mt-form-item prop="timeFormat" :label="$t('时间格式')" v-show="addForm.paramType == 2">
      <mt-select
        v-model="addForm.timeFormat"
        :data-source="dateList"
        :show-clear-button="true"
        :placeholder="$t('请选择时间格式')"
      ></mt-select>
    </mt-form-item>

    <!-- 固定值 -->
    <mt-form-item prop="constantValue" :label="$t('内容')" v-show="addForm.paramType == 3">
      <mt-input
        v-model.trim="addForm.constantValue"
        :show-clear-button="true"
        type="text"
        :placeholder="$t('请输入内容')"
      ></mt-input>
    </mt-form-item>

    <!-- 动态参数 -->
    <mt-form-item prop="fieldCode" :label="$t('参数名称')" v-show="addForm.paramType == 4">
      <mt-select
        v-model.number="addForm.fieldName"
        :data-source="paramFields"
        :fields="{ text: 'fieldDesc', value: 'fieldCode' }"
        :placeholder="$t('请选择参数名称')"
      ></mt-select>
    </mt-form-item>

    <!-- 随机值 -->
    <mt-form-item prop="length" :label="$t('位数')" v-show="addForm.paramType == 5">
      <mt-input-number
        v-model="addForm.length"
        :min="1"
        :show-clear-button="true"
        :placeholder="$t('请输入位数')"
      ></mt-input-number>
    </mt-form-item>
  </mt-form>
</template>

<script>
import { PARAM_TYPE_ENUM, TIME_FORMATS, RESET_CYCLE_TYPE } from '../data'
export default {
  props: {
    serviceName: {
      type: String,
      default: ''
    },
    mapperCode: {
      type: String,
      default: ''
    }
  },
  computed: {
    typeList() {
      let types = []
      for (const v in PARAM_TYPE_ENUM) {
        types.push(PARAM_TYPE_ENUM[v])
      }
      return types
    },
    dateList() {
      let formats = []
      for (const i in TIME_FORMATS) {
        let format = TIME_FORMATS[i]
        formats.push({ value: format, text: format })
      }
      return formats
    },
    resetList() {
      let reset = []
      for (const v in RESET_CYCLE_TYPE) {
        reset.push({ value: Number(v), text: RESET_CYCLE_TYPE[v] })
      }
      return reset
    }
  },
  data() {
    return {
      addForm: {
        paramType: 0,
        paramTypeName: '',
        paramCode: '',
        constantValue: '',
        timeFormat: '',
        minValue: null,
        incrValue: null,
        digit: null,
        autoFill: 0,
        resetCycle: 0,
        length: 2,
        recycle: 0,
        fieldName: ''
      },
      // 自定义校验
      rules0: {
        paramType: [{ required: true, message: this.$t('请选择类型'), trigger: 'blur' }],
        paramCode: [
          { required: true, message: this.$t('请输入代码'), trigger: 'blur' },
          { minLength: 2, message: this.$t('代码长度不可少于2个字符'), trigger: 'blur' },
          {
            maxLength: 36,
            message: this.$t('代码长度不可超过36个字符'),
            trigger: 'blur'
          }
        ]
      },
      // 固定值校验
      rules3: {
        paramType: [{ required: true, message: this.$t('请选择类型'), trigger: 'blur' }],
        paramCode: [
          { required: true, message: this.$t('请输入代码'), trigger: 'blur' },
          { minLength: 2, message: this.$t('代码长度不可少于2个字符'), trigger: 'blur' },
          {
            maxLength: 36,
            message: this.$t('代码长度不可超过36个字符'),
            trigger: 'blur'
          }
        ],
        constantValue: [
          { required: true, message: this.$t('请输入内容'), trigger: 'blur' },
          { minLength: 2, message: this.$t('内容长度不可少于2个字符'), trigger: 'blur' },
          {
            maxLength: 200,
            message: this.$t('内容长度不可超过200个字符'),
            trigger: 'blur'
          }
        ]
      },
      // 随机值
      rules5: {
        paramType: [{ required: true, message: this.$t('请选择类型'), trigger: 'blur' }],
        paramCode: [
          { required: true, message: this.$t('请输入代码'), trigger: 'blur' },
          { minLength: 2, message: this.$t('代码长度不可少于2个字符'), trigger: 'blur' },
          {
            maxLength: 36,
            message: this.$t('代码长度不可超过36个字符'),
            trigger: 'blur'
          }
        ],
        length: [
          { required: true, message: this.$t('请输入内容'), trigger: 'blur' },
          {
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (value < 1) {
                callback(new Error('随机数长度不可小于1'))
              } else if (value > 10) {
                callback(new Error('随机数长度不可大于10'))
              } else {
                callback()
              }
            }
          }
        ]
      },
      paramFields: [],
      // 流水号校验
      rules1: {
        paramType: [{ required: true, message: this.$t('请选择类型'), trigger: 'blur' }],
        paramCode: [
          { required: true, message: this.$t('请输入代码'), trigger: 'blur' },
          { minLength: 2, message: this.$t('代码长度不可少于2个字符'), trigger: 'blur' },
          {
            maxLength: 36,
            message: this.$t('代码长度不可超过36个字符'),
            trigger: 'blur'
          }
        ],
        minValue: [
          {
            required: true,
            message: this.$t('请输入最小值'),
            trigger: 'blur'
          }
        ],
        incrValue: [
          {
            required: true,
            message: this.$t('请输入增量值'),
            trigger: 'blur'
          }
        ],
        digit: [
          {
            required: true,
            message: this.$t('请输入位数'),
            trigger: 'blur'
          }
        ],
        autoFill: [
          {
            required: true,
            message: this.$t('请选择是否自动填充'),
            trigger: 'blur'
          }
        ]
      },
      // 时间校验
      rules2: {
        paramType: [{ required: true, message: this.$t('请选择类型'), trigger: 'blur' }],
        paramCode: [
          { required: true, message: this.$t('请输入代码'), trigger: 'blur' },
          { minLength: 2, message: this.$t('代码长度不可少于2个字符'), trigger: 'blur' },
          {
            maxLength: 36,
            message: this.$t('代码长度不可超过36个字符'),
            trigger: 'blur'
          }
        ],
        timeFormat: [
          {
            required: true,
            message: this.$t('请选择时间类型'),
            trigger: 'blur'
          }
        ]
      },

      rules: {},
      customTypeList: [
        {
          label: this.$t('动态传入'),
          value: 0
        },
        {
          label: this.$t('自定义脚本'),
          value: 1
        }
      ],
      autoFillInList: [
        { label: this.$t('是'), value: 1 },
        { label: this.$t('否'), value: 0 }
      ]
    }
  },

  watch: {
    addForm(newVal) {
      // console.log(this.$t("formInfo有了新newVal"), newVal);
      this.rules = this[`rules${newVal.paramType}`]
      this.addForm.paramTypeName = this.getTypeName()
      this.$emit('changeTypeName')
    },
    serviceName() {
      this.paramFields = []
    },
    mapperCode() {
      this.paramFields = []
    }
  },

  mounted() {
    this.rules = this.rules0
  },

  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },

    handleTypeChange(event) {
      this.$refs.ruleForm.clearValidate()
      this.rules = this[`rules${event.value}`]
      this.addForm.paramTypeName = this.getTypeName(event.value)
      // console.log(this.$t("类型改变时"), event.value, this.addForm.paramTypeName);
      this.$emit('changeTypeName')
      // console.log("event", event);
      if (event.value == 4) {
        this.queryParamFields(this.serviceName, this.mapperCode)
      }
    },

    getTypeName(val) {
      val = val ?? this.addForm.paramType
      for (const t in PARAM_TYPE_ENUM) {
        const type = PARAM_TYPE_ENUM[t]
        if (type.paramType == val) {
          return type.paramTypeName
        }
      }
      return ''
    },

    queryParamFields(serviceName, mapperCode) {
      if (this.paramFields.length == 0) {
        if (serviceName && mapperCode) {
          const query = {
            serviceName,
            mapperCode
          }
          this.$API.systemSetting.getParamFieldCodesByMapperCode(query).then((res) => {
            this.paramFields = res.data
          })
        } else {
          this.$toast({
            content: this.$t(`请先选择所属服务及MapperCode`)
          })
        }
      }
    },

    handleClose() {
      this.$emit('handleAddDialogShow', false)
    }
  }
}
</script>

<style></style>
