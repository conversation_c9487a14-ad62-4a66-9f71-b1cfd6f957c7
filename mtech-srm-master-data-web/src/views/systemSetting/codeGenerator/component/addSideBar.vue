<template>
  <mt-side-bar
    position="Right"
    ref="sidebar"
    :show-backdrop="true"
    :enable-dock="true"
    class="side-generator"
    @close="handleClose"
  >
    <div class="right-btn">
      <div class="left-title">{{ $t('新增明细') }}</div>
      <mt-icon name="icon_Close_1" @click.native="handleClose"></mt-icon>
    </div>

    <div class="blue-title">{{ $t('基础信息') }}</div>

    <mt-form ref="refs" v-model="baseForm" :rules="baseRules">
      <mt-form-item prop="" :label="$t('业务编码')">
        <mt-input
          v-model="baseForm.code"
          :show-clear-button="true"
          :disabled="rowInfo && rowInfo.dialogType == 'edit'"
          :placeholder="$t('请输入业务编码')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="" :label="$t('规则名称')">
        <mt-input
          v-model="baseForm.code"
          :show-clear-button="true"
          :placeholder="$t('请输入规则名称')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="" :label="$t('生效日期')">
        <mt-date-picker v-model="baseForm.start" :placeholder="$t('选择生效日期')"></mt-date-picker>
      </mt-form-item>

      <mt-form-item prop="" :label="$t('失效日期')">
        <mt-date-picker v-model="baseForm.start" :placeholder="$t('选择失效日期')"></mt-date-picker>
      </mt-form-item>

      <mt-form-item prop="" :label="$t('描述')" class="fullWidth">
        <mt-input
          v-model="baseForm.code"
          :multiline="true"
          :show-clear-button="true"
          :placeholder="$t('请输入描述')"
        ></mt-input>
      </mt-form-item>
    </mt-form>

    <div class="title-box">
      <div class="blue-title">
        <div class="left-title">{{ $t('规则库') }}</div>
      </div>
      <div class="right-add" @click="handleAddDialogShow">
        <mt-icon name="icon_solid_Createorder"></mt-icon>
        <span>{{ $t('新增') }}</span>
      </div>
    </div>

    <div class="rule-box">
      <draggable v-model="myArray" :force-fallback="true" drag-class="rule-item-handler">
        <transition-group>
          <div v-for="element in myArray" :key="element.id" class="one-rule">
            <div class="rule-item-handler"></div>
            <span>{{ element.name }}</span>
          </div>
        </transition-group>
      </draggable>

      <div class="form-detail" v-if="myArray && myArray.length > 0">
        <add-form :form-info="myArray && myArray[currentIndex]"></add-form>
      </div>
    </div>

    <mt-dialog
      ref="dialog"
      css-class="create-proj-dialog"
      :header="dialogTitle"
      :buttons="buttons"
      @close="handleAddDialogHide"
      :open="onOpen"
    >
      <add-form ref="addFormRef"></add-form>
    </mt-dialog>
  </mt-side-bar>
</template>

<script>
import draggable from 'vuedraggable'
export default {
  components: {
    draggable,
    addForm: require('./addForm.vue').default
  },
  props: {
    addDetailShow: {
      type: Boolean,
      default: false
    },
    rowInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      baseForm: {},
      baseRules: {},
      showAddDialog: false,
      myArray: [
        {
          id: 1,
          name: this.$t('固定值')
        },
        {
          id: 2,
          name: this.$t('时间')
        },
        {
          id: 3,
          name: this.$t('流水号')
        }
      ],
      currentIndex: 0,
      dialogTitle: this.$t('新增规则库'),
      buttons: [
        {
          click: this.handleAddDialogHide, // 不支持传参
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  watch: {
    addDetailShow(newVal) {
      if (newVal) {
        this.$refs.sidebar.show()
      } else {
        this.$refs.sidebar.hide()
      }
    }
  },
  methods: {
    handleClose() {
      console.log('触发了close--')
      this.$emit('update:addDetailShow', false)
    },
    handleAddDialogShow() {
      this.$refs.dialog.ejsRef.show()
    },
    handleAddDialogHide() {
      // console.log("准备隐藏了---22222222");
      this.$refs.dialog.ejsRef.hide()
    },
    onOpen(args) {
      args.preventFocus = true
    },

    // 新增表单的提交
    confirm() {
      console.log(this.$t('提交'), this.$refs.addFormRef)
    }
  }
}
</script>

<style lang="scss" scoped>
.side-generator {
  width: 45% !important;
  min-width: 800px;
  height: 100%;
  overflow: auto;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px 0 0 0;

  .right-btn {
    width: 100%;
    font-size: 16px;
    line-height: 1;
    padding: 22px 20px;
    background: rgba(243, 243, 243, 1);
    color: #292929;
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      cursor: pointer;
    }
    .mt-icons {
      font-size: 14px;
      cursor: pointer;
    }
  }

  .blue-title {
    font-size: 14px;
    padding-left: 8px;
    margin: 20px;
    position: relative;
    color: #2f353c;
    &::before {
      content: '';
      width: 3px;
      height: 80%;
      position: absolute;
      left: 0;
      top: 10%;
      background: #eda133;
      border-radius: 5px 0 0 5px;
    }

    &.mt0 {
      margin-bottom: 0;
    }
  }

  .title-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 20px;
    .blue-title {
      margin-top: 10px;
    }
    .right-add {
      cursor: pointer;
      font-size: 14px;
      color: #4d5b6f;
      span {
        margin-left: 6px;
      }
    }
  }

  /deep/ .mt-form {
    width: 100%;
    padding: 0 30px;
    .mt-form-item {
      width: calc((100% - 20px) / 2);
      display: inline-block;

      &:nth-child(2n + 1) {
        margin-right: 20px;
      }

      &.fullWidth {
        width: 100%;
      }
    }
  }

  .rule-box {
    .one-rule {
      display: inline-flex;
      font-size: 14px;
      color: #182b3e;
      line-height: 1;
      padding: 14px 20px;
      background: rgba(255, 255, 255, 1);
      border-radius: 4px;
      box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.06);

      .rule-item-handler {
        background: #dedede;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 4px 0 0 4px;
        cursor: move;
        &:before {
          content: '';
          height: 16px;
          border-left: 1px solid #ffffff;
          margin-left: 3px;
        }
        &:after {
          content: '';
          height: 16px;
          border-left: 1px solid #ffffff;
          margin: 0 3px;
        }

        &:hover {
          background: #6386c1;
        }
      }
    }
  }
}
</style>
