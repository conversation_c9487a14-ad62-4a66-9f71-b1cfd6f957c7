/* eslint-disable prettier/prettier */
<template>
  <div class="generator-box">
    <div
      position="Right"
      ref="sidebar"
      :show-backdrop="true"
      :enable-dock="true"
      class="side-generator"
      @close="handleClose"
    >
      <div class="top-box">
        <div class="left-title">
          {{ addMode ? $t('新增') : $t('编辑') }}
        </div>
        <mt-icon name="icon_Close_1" @click.native="handleClose"></mt-icon>
      </div>

      <div class="main-box">
        <div class="blue-title">{{ $t('基础信息') }}</div>

        <mt-form ref="mtForm" :model="formModel" :rules="formRules">
          <!-- <mt-form-item prop="serviceName" :label="$t('所属服务')">
            <mt-select
              v-model="formModel.serviceName"
              :data-source="serviceNames"
              :fields="{ text: 'itemName', value: 'itemCode' }"
              :allow-filtering="true"
              :show-clear-button="true"
              :disabled="!addMode"
              :placeholder="$t('请选择所属服务')"
              @change="serviceNameChanged"
            />
          </mt-form-item>
          <mt-form-item prop="mapperCode" :label="$t('MapperCode')">
            <mt-select
              :data-source="mapperCodes"
              v-model="formModel.mapperCode"
              :fields="{ text: 'desc', value: 'code' }"
              :allow-filtering="true"
              :show-clear-button="true"
              :disabled="!addMode"
              :placeholder="$t('请选择服务内MapperCode')"
              @change="mapperCodeChanged"
            />
          </mt-form-item> -->

          <mt-form-item prop="generatorCode" :label="$t('业务编码')">
            <mt-select
              v-model="formModel.generatorCode"
              :data-source="fieldCodes"
              :fields="{ text: 'fieldDesc', value: 'fieldCode' }"
              :allow-filtering="true"
              :show-clear-button="true"
              :disabled="!addMode"
              :placeholder="$t('请输入业务编码')"
              v-show="fieldCodes.length > 0"
              @change="fieldCodeChanged"
            />
            <mt-input
              v-model.trim="formModel.generatorCode"
              :show-clear-button="true"
              :disabled="!addMode"
              :placeholder="$t('请输入业务编码')"
              v-show="fieldCodes.length === 0"
            />
          </mt-form-item>

          <mt-form-item prop="generatorName" :label="$t('规则名称')">
            <mt-input
              v-model.trim="formModel.generatorName"
              :show-clear-button="true"
              :placeholder="$t('请输入规则名称')"
            />
          </mt-form-item>

          <mt-form-item
            :label="$t('所属公司')"
            prop="companyId"
            v-show="this.userInfo.tenantId !== '-99'"
          >
            <mt-select
              :fields="companyFields"
              :data-source="companyFields.dataSource"
              v-model="formModel.companyId"
              :show-check-box="true"
              id="companyIdDropDownTree"
              :placeholder="$t('请选择')"
              @select="changeCompany"
            />
          </mt-form-item>

          <mt-form-item
            :label="$t('所属部门')"
            prop="departmentId"
            v-show="this.userInfo.tenantId !== '-99'"
          >
            <mt-multi-select
              v-if="departmentFields.dataSource.length > 0"
              :data-source="departmentFields.dataSource"
              :fields="{ value: 'id', text: 'orgName' }"
              :show-clear-button="true"
              v-model="formModel.departmentList"
              :placeholder="$t('请选择所属部门')"
            />
            <mt-select v-else :data-source="[]" :placeholder="$t('请选择所属部门')" />
          </mt-form-item>

          <mt-form-item prop="timeRange" :label="$t('有效时间')">
            <mt-date-range-picker
              v-model="formModel.timeRange"
              format="yyyy-MM-dd"
              :placeholder="$t('选择开始时间和结束时间')"
            />
          </mt-form-item>

          <mt-form-item prop="generatorDescription" :label="$t('描述')" class="fullWidth">
            <mt-input
              v-model.trim="formModel.generatorDescription"
              :multiline="true"
              :show-clear-button="true"
              :placeholder="$t('请输入描述')"
            />
          </mt-form-item>
        </mt-form>

        <div class="title-box">
          <div class="blue-title">
            <div class="left-title">{{ $t('规则库') }}</div>
          </div>
          <div class="right-add" @click="handleAddDialogShow">
            <mt-icon name="icon_solid_Createorder"></mt-icon>
            <span>{{ $t('新增') }}</span>
          </div>
        </div>

        <div class="rule-box">
          <draggable
            v-model="codeParamList"
            :force-fallback="true"
            drag-class="rule-item-handler"
            @start="handleDragStart"
            @end="handleDragEnd"
          >
            <!-- <transition-group> -->
            <div
              v-for="(item, index) in codeParamList"
              :key="`${index}-${item.paramTypeName}`"
              :class="[
                'one-rule',
                currentIndex == index && 'one-rule-active',
                draggingIndex == index && 'one-rule-dragging'
              ]"
            >
              <div class="rule-item-handler"></div>
              <span class="rule-name" @click="handleChangeTag(index)">{{
                item.paramTypeName
              }}</span>
              <mt-icon
                name="icon_Close_2"
                class="close-icon"
                @click.native="handleDelete(index)"
              ></mt-icon>
            </div>
            <!-- </transition-group> -->
          </draggable>

          <div class="form-detail" v-for="(item, index) in codeParamList" :key="index">
            <add-form
              :ref="`addFormRef-${index}`"
              v-show="index == currentIndex"
              :mapper-code="formModel.mapperCode"
              :service-name="formModel.serviceName"
              @changeTypeName="changeTypeName"
            ></add-form>
          </div>
        </div>
      </div>

      <div class="bottom-box">
        <mt-button css-class="e-flat" @click="handleClose">{{ $t('取消') }}</mt-button>
        <mt-button css-class="e-flat" :is-primary="true" @click="confirm">{{
          $t('提交')
        }}</mt-button>
      </div>
    </div>
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import { PARAM_TYPE_ENUM, SCOPE_TYPE_ENUM } from '../data'
export default {
  components: {
    draggable,
    addForm: require('./addForm.vue').default
  },
  props: {
    addDetailShow: {
      type: Boolean,
      default: false
    },
    model: {
      type: Object,
      default: () => {
        return {
          id: null
        }
      }
    }
  },
  created() {
    this.getUserData()
  },
  computed: {
    addMode() {
      return !this.model.id
    },
    saasUser() {
      return this.userInfo.tenantId !== '-99'
    }
  },
  data() {
    return {
      userInfo: {},
      formModel: {
        generatorCode: '',
        generatorName: '',
        timeRange: []
      },
      formRules: {
        generatorCode: [
          {
            trigger: 'blur',
            required: true,
            message: this.$t('请输入规则编码')
          }
        ],
        generatorName: [
          {
            trigger: 'blur',
            required: true,
            message: this.$t('请输入编码名称')
          },
          {
            trigger: 'blur',
            validator: this.validateGeneratorName
          }
        ],
        timeRange: [
          {
            required: true,
            message: this.$t('请选择有效时间'),
            trigger: 'blur'
          }
        ]
      },
      fieldCodes: [],
      mapperCodes: [],
      serviceNames: [],
      codeParamList: [],
      companyFields: {
        dataSource: [],
        value: 'id',
        text: 'orgName'
      },
      departmentFields: {
        dataSource: [],
        value: 'id',
        text: 'orgName'
      },
      originModel: {
        paramType: 0,
        paramTypeName: '',
        paramCode: '',
        constantValue: '',
        timeFormat: '',
        minValue: null,
        incrValue: null,
        digit: null,
        autoFill: 1,
        resetCycle: 0,
        recycle: 0
      },
      currentIndex: 0,
      draggingIndex: null
    }
  },

  mounted() {
    this.formModel = Object.assign(this.formModel, this.model)
    this.getRules()
    // this.serviceNameGet();
    this.getRuleBase()
  },

  methods: {
    getRules() {
      // this.$API.systemSetting.saveGeneratorValid().then((res) => {
      //   if (res.code == 200) {
      //     this.formRules = formatRules(res.data);
      //     delete this.formRules.startTime;
      //     delete this.formRules.endTime;
      //     // this.formRules['timeRange'] = [
      //     //   {
      //     //     required: true,
      //     //     message: this.$t("请选择有效范围"),
      //     //     trigger: "blur",
      //     //   },
      //     // ];
      //   }
      // });
    },

    getRuleBase() {
      this.$API.systemSetting
        .queryByCodeGeneratorId({ codeGeneratorId: this.formModel?.id })
        .then((res) => {
          if (res.code == 200) {
            let codeGenerator = res.data.codeGenerator
            let endTime = codeGenerator?.endTime
            let startTime = codeGenerator?.startTime
            if (!this.formModel?.id) {
              startTime = new Date()
              endTime = new Date('2099-12-31')
            }
            this.formModel = Object.assign(this.formModel, codeGenerator)
            this.formModel.timeRange = [new Date(startTime), new Date(endTime)]
            if (res.data.scopes) {
              let scopes = res.data.scopes
              this.formModel.companyId = 0
              this.formModel.departmentList = []
              scopes.forEach((scope) => {
                if (scope.scope === SCOPE_TYPE_ENUM.ORG.code) {
                  this.formModel.companyId = scope.scopeId
                } else if (scope.scope === SCOPE_TYPE_ENUM.DEPT.code) {
                  let exist = false
                  this.formModel.departmentList.forEach((deptId) => {
                    if (!exist && deptId === scope.scopeId) {
                      exist = true
                    }
                  })
                  if (!exist) {
                    this.formModel.departmentList.push(scope.scopeId)
                  }
                }
              })
            }
            if (this.formModel.serviceName) {
              this.getMapperCodeByServiceName(this.formModel.serviceName)
              this.getFieldCodesByMapperCode(this.formModel.serviceName, this.formModel.mapperCode)
            }
            this.codeParamList = this.join(res.data)
            this.$nextTick(() => {
              this.codeParamList.forEach((item, index) => {
                this.$refs[`addFormRef-${index}`][0].addForm = item
              })
            })
          }
        })
    },

    generatorNameBlur(name) {
      let param = {
        generatorName: name,
        id: this.formModel.id,
        generatorCode: this.formModel.generatorCode
      }
      this.$API.systemSetting.validateName(param).then(
        (res) => {
          return res
        },
        (error) => {
          return error
        }
      )
    },

    handleDragStart(event) {
      this.draggingIndex = event.oldIndex
      console.log(this.$t('开始拖拽'), event)
    },
    handleDragEnd(event) {
      console.log(this.$t('拖放结束'), event)
      this.draggingIndex = null
      this.handleChangeTag(event.newIndex, event.oldIndex)
    },
    handleClose() {
      // console.log("触发了close--");
      this.$emit('update:addDetailShow', false)
    },
    handleAddDialogShow() {
      this.codeParamList.push(Object.assign({}, this.originModel, PARAM_TYPE_ENUM.custom))
      this.currentIndex = this.codeParamList.length - 1
      this.$nextTick(() => {
        this.$refs[`addFormRef-${this.currentIndex}`][0].addForm =
          this.codeParamList[this.currentIndex]
      })
    },
    handleAddDialogHide() {
      // console.log("准备隐藏了---22222222");
      this.$refs.dialog.ejsRef.hide()
    },
    onOpen(args) {
      args.preventFocus = true
    },

    handleChangeTag(newIndex) {
      this.currentIndex = newIndex
      this.$refs[`addFormRef-${newIndex}`][0].addForm = this.codeParamList[newIndex]
    },
    changeTypeName() {
      this.$set(
        this.codeParamList,
        this.currentIndex,
        this.$refs[`addFormRef-${this.currentIndex}`][0].addForm
      )
    },

    generatorCodeSelectBlur(event) {
      if (event.target.value) {
        this.formModel.generatorCode = event.target.value
      }
    },

    changeCompany(event) {
      const { itemData } = event
      this.departmentList = []
      this.formModel.departmentList = []
      if (itemData) {
        this.formModel.companyId = itemData.id
        this.getChildrenDepartmentOrganization(itemData.id)
      }
    },
    getUserData() {
      this.$API.org.getUserInfo({}).then((resData) => {
        this.userInfo = resData.data
        this.formModel.tenantId = this.userInfo.tenantId
        if (this.userInfo.tenantId !== '-99') {
          this.getChildrenCompanyOrganization()
        }
      })
    },

    // 获取当前组织下公司列表
    getChildrenCompanyOrganization() {
      this.$loading()
      this.$API.customer['getChildrenCompanyOrganization2']({
        organizationLevelCodes: ['ORG02', 'ORG01'],
        orgType: 'ORG001COM',
        includeItself: true
      })
        .then((result) => {
          this.$hloading()
          this.companyFields.dataSource = result.data
          this.$nextTick(() => {
            let companyId = 0
            if (this.formModel.companyId) {
              companyId = this.formModel.companyId
            } else {
              companyId = this.companyFields.dataSource[0].id
            }
            this.getChildrenDepartmentOrganization(companyId)
          })
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },

    // 获取当前组织下部门列表
    getChildrenDepartmentOrganization(orgId) {
      this.$API.org
        .getChildrenDepartmentOrganization({
          organizationId: orgId
        })
        .then((result) => {
          this.departmentFields.dataSource = result?.data || []
        })
    },
    serviceNameGet() {
      const query = { dictCode: 'serviceName' }
      this.$API.dict.dictTypeGet(query).then((res) => {
        this.serviceNames = res.data
      })
    },
    serviceNameChanged(option) {
      this.getMapperCodeByServiceName(option?.itemData?.itemCode)
    },
    getMapperCodeByServiceName(serviceName) {
      const query = { serviceName }
      this.$API.systemSetting.getMapperCodeByServiceName(query).then((res) => {
        this.mapperCodes = res.data
      })
    },
    mapperCodeChanged(option) {
      if (this.formModel.serviceName) {
        this.getFieldCodesByMapperCode(this.formModel.serviceName, option?.itemData?.code)
      }
    },
    getFieldCodesByMapperCode(serviceName, mapperCode) {
      if (serviceName) {
        const query = {
          serviceName,
          mapperCode
        }
        let thiz = this
        this.$API.systemSetting.getFieldCodesByMapperCode(query).then((res) => {
          thiz.fieldCodes = res.data
        })
      }
    },
    fieldCodeChanged(option) {
      this.formModel.generatorName = option?.itemData?.fieldDesc
    },
    handleDelete(index) {
      this.codeParamList.splice(index, 1)
      if (this.currentIndex > 0) {
        this.currentIndex--
      }
      this.handleChangeTag(this.currentIndex)
    },

    validateGeneratorName(rule, value, callback) {
      if (value === '' || !value) {
        callback(new Error('请输入规则名称'))
      } else {
        let msg = this.generatorNameBlur(value)
        if (msg) {
          callback(new Error(msg))
        } else {
          callback()
        }
      }
    },

    // 新增表单的提交
    confirm() {
      this.$refs.mtForm.validate((valid) => {
        if (valid) {
          // 校验规则
          if (!this.codeParamList.length) {
            this.$toast({ content: this.$t('请先新增规则'), type: 'warning' })
            return
          }
          // let validNum = 0;
          for (let i = 0; i < this.codeParamList.length; i++) {
            this.$refs[`addFormRef-${i}`][0].$refs.ruleForm.validate((valid) => {
              if (!valid) {
                let param = this.codeParamList[i]
                this.$toast({
                  content: this.$t('请将') + param.paramTypeName + this.$t('数据填写完整'),
                  type: 'warning'
                })
                return
              }
            })
          }
          let endTime = this.formModel.timeRange.end
          if (!endTime) {
            endTime = this.formModel.timeRange[1]
          }
          endTime = ('' + endTime).replace('00:00:00', '23:59:59')
          this.formModel.endTime = new Date(endTime)
          let startTime = this.formModel.timeRange.start
          if (!startTime) {
            startTime = this.formModel.timeRange[0]
          }
          this.formModel.startTime = startTime
          let scopes = this.scopes(this.formModel)
          let paramModel = Object.assign({
            scopes: scopes,
            codeGenerator: this.formModel
          })
          this.fork(paramModel, this.codeParamList)
          this.$API.systemSetting.saveGenerator(paramModel).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.handleClose()
              this.$emit('confirmSuccess')
            }
          })
        }
      })
    },

    scopes(formModel) {
      let scopes = []
      if (formModel.departmentList && formModel.departmentList.length > 0) {
        formModel.departmentList.forEach((item) => {
          scopes.push({
            scope: SCOPE_TYPE_ENUM.DEPT.code,
            scopeId: item.id ? item.id : item
          })
        })
      }
      if (formModel.companyId) {
        scopes.push({
          scope: SCOPE_TYPE_ENUM.ORG.code,
          scopeId: formModel.companyId
        })
      }
      return scopes
    },

    join(resData) {
      let paramList = []
      if (resData.serial) {
        paramList.push(Object.assign(resData.serial, PARAM_TYPE_ENUM.serial))
      }
      if (resData.times) {
        resData.times.forEach((p) => {
          paramList.push(Object.assign(p, PARAM_TYPE_ENUM.time))
        })
      }
      if (resData.fields) {
        resData.fields.forEach((p) => {
          paramList.push(Object.assign(p, PARAM_TYPE_ENUM.fields))
        })
      }
      if (resData.consts) {
        resData.consts.forEach((p) => {
          paramList.push(Object.assign(p, PARAM_TYPE_ENUM.consts))
        })
      }
      if (resData.randoms) {
        resData.randoms.forEach((p) => {
          paramList.push(Object.assign(p, PARAM_TYPE_ENUM.random))
        })
      }
      if (resData.customs) {
        resData.customs.forEach((p) => {
          paramList.push(Object.assign(p, PARAM_TYPE_ENUM.custom))
        })
      }
      if (resData.scopes) {
        let scopes = resData.scopes
        if (!this.formModel.departmentList) {
          this.formModel.departmentList = []
        }
        scopes.forEach((scope) => {
          if (scope.scope === SCOPE_TYPE_ENUM.ORG.code) {
            this.formModel.companyId = scope.scopeId
          } else if (scope.scope === SCOPE_TYPE_ENUM.DEPT.code) {
            let exist = false
            this.formModel.departmentList.forEach((deptId) => {
              if (!exist && deptId === scope.scopeId) {
                exist = true
              }
            })
            if (!exist) {
              debugger
              this.formModel.departmentList.push(scope.scopeId)
            }
          }
        })
      }
      // 排序
      paramList.sort((p1, p2) => p1.sort - p2.sort)
      return paramList
    },

    fork(param, codeParamList) {
      for (let i = 0; i < codeParamList.length; i++) {
        let p = codeParamList[i]
        p.sort = i + 1
        if (p.paramType == PARAM_TYPE_ENUM.custom.paramType) {
          // 自定义
          if (!param.customs) {
            param.customs = []
          }
          param.customs.push(p)
        } else if (p.paramType == PARAM_TYPE_ENUM.serial.paramType) {
          // 流水号
          param.serial = p
        } else if (p.paramType == PARAM_TYPE_ENUM.time.paramType) {
          // 时间戳
          if (!param.times) {
            param.times = []
          }
          param.times.push(p)
        } else if (p.paramType == PARAM_TYPE_ENUM.consts.paramType) {
          // 固定值
          if (!param.consts) {
            param.consts = []
          }
          param.consts.push(p)
        } else if (PARAM_TYPE_ENUM.fields && p.paramType == PARAM_TYPE_ENUM.fields.paramType) {
          // 动态参数
          if (!param.fields) {
            param.fields = []
          }
          param.fields.push(p)
        } else if (p.paramType == PARAM_TYPE_ENUM.random.paramType) {
          // 随机值
          if (!param.randoms) {
            param.randoms = []
          }
          param.randoms.push(p)
        }
      }
      return param
    }
  }
}
</script>

<style lang="scss" scoped>
.generator-box {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 100;
  background: rgba(56, 56, 56, 0.5);
}
.side-generator {
  width: 45% !important;
  min-width: 800px;
  height: calc(100% - 60px);
  overflow: auto;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px 0 0 0;
  position: fixed;
  top: 60px;
  right: 0;
  z-index: 100;

  .top-box {
    width: 100%;
    font-size: 16px;
    line-height: 1;
    padding: 14px 20px;
    background: rgba(243, 243, 243, 1);
    color: #292929;
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      cursor: pointer;
    }
    .mt-icons {
      font-size: 14px;
      cursor: pointer;
    }
  }

  .main-box {
    flex: 1;
    overflow: auto;
    .blue-title {
      font-size: 14px;
      padding-left: 8px;
      margin: 20px;
      position: relative;
      color: #2f353c;
      &::before {
        content: '';
        width: 3px;
        height: 80%;
        position: absolute;
        left: 0;
        top: 10%;
        background: #eda133;
        border-radius: 5px 0 0 5px;
      }

      &.mt0 {
        margin-bottom: 0;
      }
    }

    .title-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-right: 20px;
      .blue-title {
        margin-top: 10px;
      }
      .right-add {
        cursor: pointer;
        font-size: 14px;
        color: #4d5b6f;
        span {
          margin-left: 6px;
        }
      }
    }

    /deep/ .mt-form {
      width: 100%;
      padding: 0 30px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .mt-form-item {
        width: calc((100% - 20px) / 2);
        display: inline-block;

        &.fullWidth {
          width: 100%;
        }
      }
    }

    .rule-box {
      .one-rule {
        height: 40px;
        margin: 0 20px 20px 20px;
        display: inline-flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        color: #182b3e;
        line-height: 1;
        background: rgba(255, 255, 255, 1);
        border-radius: 4px;
        box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.06);
        cursor: pointer;

        .rule-item-handler {
          height: 100%;
          background: #dedede;
          position: relative;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 4px 0 0 4px;
          cursor: move;
          &:before {
            content: '';
            height: 16px;
            border-left: 1px solid #ffffff;
            margin-left: 3px;
          }
          &:after {
            content: '';
            height: 16px;
            border-left: 1px solid #ffffff;
            margin: 0 3px;
          }

          &:hover {
            background: #6386c1;
          }
        }

        .rule-name {
          height: 40px;
          line-height: 40px;
          padding: 0 14px;
        }

        .close-icon {
          margin-right: 10px;
          color: #dedede;
          cursor: pointer;
        }

        &-active {
          .rule-item-handler {
            background: #6386c1;
          }
          .close-icon {
            color: #6386c1;
          }
        }

        &-dragging {
          .rule-item-handler {
            background: #eda133;
          }
          .close-icon {
            color: #eda133;
          }
        }
      }
    }
  }

  .bottom-box {
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 20px;
    text-align: right;
    border-top: 1px solid #e8e8e8;
  }
}
</style>
