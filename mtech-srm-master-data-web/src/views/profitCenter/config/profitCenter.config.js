import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-12-24 13:43:09
 * @LastEditTime: 2022-03-21 11:35:26
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\workCenter\config\workCenter.config.js
 */
export const PAGE_CONFIG = [
  {
    toolbar: [
      // "Add",
      // "Delete",
      {
        id: 'Sync',
        icon: 'icon_solid_Createproject',
        title: i18n.t('同步'),
        visibleCondition: () => false
      }
    ],
    grid: {
      frozenColumns: 2,
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'profitCenterName',
          headerText: i18n.t('利润中心名称'),
          width: '250'
          // cellTools: ["edit", "delete", "preview"],
        },
        {
          field: 'profitCenterCode',
          width: '200',
          headerText: i18n.t('利润中心编码')
        },
        {
          field: 'entities',
          width: '200',
          headerText: i18n.t('实体企业')
        },
        {
          field: 'startTime',
          width: '200',
          headerText: i18n.t('开始生效日期')
        },
        {
          field: 'endTime',
          width: '200',
          headerText: i18n.t('有效截至日期')
        },
        {
          field: 'statusId',
          headerText: i18n.t('状态'),
          width: '150',
          valueConverter: {
            type: 'map',
            map: {
              0: i18n.t('草稿'),
              1: i18n.t('激活'),
              2: i18n.t('分发'),
              3: i18n.t('失效'),
              '-1': i18n.t('待审核'),
              '-2': i18n.t('准备')
            }
          }
        },
        {
          field: 'profitCenterDesc',
          headerText: i18n.t('利润中心描述'),
          width: 'auto'
        }
      ],
      asyncConfig: {
        url: '/masterDataManagement/tenant/profit-center/paged-query',
        params: {}
      }
    }
  }
]
