<!--
 * @Author: your name
 * @Date: 2021-09-03 15:27:09
 * @LastEditTime: 2022-03-10 14:35:27
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\base\Group.vue
-->
<template>
  <div class="full-height mt-pt-20">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
    <mt-dialog ref="dialog" :header="headerTitle" css-class="create-proj-dialog" :buttons="buttons">
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="groupName" :label="$t('业务组名称')">
          <mt-input
            v-model="ruleForm.groupName"
            :disabled="disabled"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入业务组名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="groupCode" :label="$t('业务组编码')" v-if="type === 'preview'">
          <mt-input
            v-model="ruleForm.groupCode"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入业务组编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="groupTypeCode" :label="$t('业务组类型')">
          <mt-select
            v-model="ruleForm.groupTypeCode"
            :disabled="disabled"
            :data-source="options.GROUP_TYPE"
            :show-clear-button="true"
            :allow-filtering="true"
            :placeholder="$t('请选择业务组类型')"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </mt-dialog>

    <member-manage
      ref="memberManage"
      @save="$refs.tepPage.refreshCurrentGridData()"
    ></member-manage>

    <cate-assign ref="cateAssign"></cate-assign>
    <mat-assign ref="matAssign"></mat-assign>
  </div>
</template>

<script>
import { PAGE_CONFIG, RULES, OPTIONS } from './config/group.config'
import { formatRules } from '@/utils/util'
import MemberManage from './components/MemberManage.vue'
import CateAssign from './components/CateAssign.vue'
import MatAssign from './components/MatAssign.vue'
export default {
  components: {
    MemberManage,
    CateAssign,
    MatAssign
  },
  data() {
    return {
      options: OPTIONS,
      pageConfig: PAGE_CONFIG,
      rules: RULES,

      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {},
      type: 'add' // add edit preview
    }
  },
  computed: {
    headerTitle() {
      if (this.type === 'add') {
        return this.$t('新增')
      }
      if (this.type === 'edit') {
        return this.$t('编辑')
      }
      return this.$t('预览')
    },
    disabled() {
      if (this.type === 'preview') {
        return true
      }
      return false
    }
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const rowSelected = grid.getSelectedRecords()
      if (toolbar.id === 'Add') {
        this.groupTypeGet()
        this.handleAction('add')
        this.rulesGet('groupAddValid')
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDelete(rowSelected)
      }
    },
    handleClickCellTool(e) {
      const { data, tool } = e
      if (tool.id === 'edit') {
        this.groupTypeGet()
        this.handleAction('edit', data)
        this.rulesGet('groupEditValid')
      }
      if (tool.id === 'delete') {
        this.handleDelete([data])
      }
      if (tool.id === 'member_manage') {
        this.handleMemberManage(data)
      }
      if (tool.id === 'cate_assign') {
        this.handleCateAssign(data)
      }
      if (tool.id === 'mat_assign') {
        this.handleMatAssign(data)
      }
      if (tool.id === 'preview') {
        this.groupTypeGet()
        this.handleAction('preview', data)
      }
    },
    handleAction(type, data) {
      this.type = type
      this.$refs.ruleForm.resetFields()
      this.ruleForm = Object.assign({}, data)
      this.$refs.dialog.ejsRef.show()
    },
    handleDelete(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.base.groupDelete(data).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    },
    handleMemberManage(row) {
      this.$refs.memberManage.show(row.id)
    },
    handleCateAssign(row) {
      this.$refs.cateAssign.show(row)
    },
    handleMatAssign(row) {
      this.$refs.matAssign.show(row)
    },
    save() {
      if (this.type === 'preview') {
        this.cancel()
        return false
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.type === 'add') {
            const data = {
              ...this.ruleForm
            }
            this.$API.base.groupAdd(data).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
          if (this.type === 'edit') {
            delete this.ruleForm.tabIndex
            this.$API.base.groupEdit(this.ruleForm).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
        }
      })
    },
    cancel() {
      this.$refs.dialog.ejsRef.hide()
    },
    async groupTypeGet() {
      const query = { dictCode: 'BUSINESSGROUP' }
      await this.$API.dict.TenantDictTypeGet(query).then((res) => {
        this.options = Object.assign({}, this.options, {
          GROUP_TYPE: res.data.map((e) => {
            return {
              text: e.itemName,
              value: e.itemCode
            }
          })
        })
      })
    },
    rulesGet(type) {
      this.$API.base[type]().then((res) => {
        this.rules = formatRules(res.data)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-subtitle {
  border-left: 3px solid #eda133;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  padding-left: 10px;
}
/deep/.btn-text {
  color: #4f5b6d;
}
/deep/ .mt-data-grid {
  .status-label {
    font-size: 12px;
    padding: 4px;
    border-radius: 2px;
    &.status-0 {
      color: #9baac1;
      background: rgba(155, 170, 193, 0.1);
    }
    &.status-1 {
      color: #6386c1;
      background: rgba(99, 134, 193, 0.1);
    }
  }
}
/deep/.mt-dialog {
  display: none;
}
</style>
