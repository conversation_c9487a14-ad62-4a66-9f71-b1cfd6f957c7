<!--
 * @Author: your name
 * @Date: 2021-08-18 14:44:45
 * @LastEditTime: 2021-08-23 21:18:48
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\base\Company.vue
-->
<template>
  <div class="full-height mt-mt-20">
    <mt-horizontal-list
      :hidden-tabs="true"
      :data-source="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @handleChangeCellCheckBox="handleChangeCellCheckBox"
      @handleSelectTab="handleSelectTab"
      @handleGridCurrentChange="handleGridCurrentChange"
      @handleGridSizeChange="handleGridSizeChange"
    ></mt-horizontal-list>
    <mt-dialog ref="dialog" :header="$t('新增')" css-class="create-proj-dialog" :buttons="buttons">
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="name" :label="$t('岗位名称')">
          <mt-input
            v-model="ruleForm.name"
            :disabled="false"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入岗位名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="age" :label="$t('岗位编码')">
          <mt-input
            v-model="ruleForm.age"
            type="text"
            :placeholder="$t('请输入岗位编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="telephone" :label="$t('所属公司')">
          <mt-input
            v-model="ruleForm.telephone"
            type="text"
            :placeholder="$t('请输入所属公司')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="mail" :label="$t('对应角色')">
          <mt-select
            :data-source="[]"
            :show-clear-button="true"
            :placeholder="$t('请选择对应角色')"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      pageConfig: [
        {
          toolbar: [['Add'], ['Filter', 'Refresh', 'Setting']],
          grid: {
            frozenColumns: 2,
            autoFitColumns: [],
            dataSource: [{ code: 123, name: this.$t('大公司1'), status: 1 }],
            columnData: [
              {
                width: '50',
                type: 'checkbox'
              },
              {
                field: 'name',
                width: '200',
                headerText: this.$t('岗位名称'),
                cellTools: ['preview', 'edit', 'delete']
              },
              {
                field: 'code',
                headerText: this.$t('岗位编号'),
                width: '200'
              },
              {
                field: 'status',
                headerText: this.$t('状态'),
                width: '150',
                valueConverter: {
                  type: 'map',
                  map: [
                    {
                      status: 0,
                      label: this.$t('无效'),
                      cssClass: ['status-label', 'status-0']
                    },
                    {
                      status: 1,
                      label: this.$t('有效'),
                      cssClass: ['status-label', 'status-0']
                    }
                  ],
                  fields: { text: 'label', value: 'status' }
                }
              },
              {
                field: 'uporg',
                width: '200',
                headerText: this.$t('归属租户')
              },
              {
                field: 'createName',
                width: '150',
                headerText: this.$t('创建人')
              },
              {
                field: 'createTime',
                headerText: this.$t('创建时间'),
                valueConverter: {
                  type: 'date'
                }
              }
            ]
          }
        }
      ],
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {},
      rules: {}
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    handleSelectTab(e) {
      //切换顶部Tabs
      console.log('use-handleSelectTab', e)
    },
    handleClickToolBar(e) {
      // 表格顶部 toolbar
      console.log('use-handleClickToolBar', e)
    },
    handleClickCellTool(a) {
      // 单元格 图标点击 tool
      console.log('use-handleClickCellTool', a)
    },
    handleChangeCellCheckBox(a) {
      // 单元格,Checkbox change事件
      console.log('use-handleChangeCellCheckBox', a)
    },
    handleClickCellTitle(a) {
      //单元格Title点击
      console.log('use-handleClickCellTitle', a)
    },
    handleGridCurrentChange(data) {
      // 换页触发事件
      console.log('use-handleGridCurrentChange', data)
    },
    handleGridSizeChange(data) {
      //pageSize变化触发
      console.log('use-handleGridSizeChange', data)
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-subtitle {
  border-left: 3px solid #eda133;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  padding-left: 10px;
}
/deep/.btn-text {
  color: #4f5b6d;
}
/deep/ .mt-data-grid {
  .status-label {
    font-size: 12px;
    padding: 4px;
    border-radius: 2px;
    &.status-0 {
      color: #9baac1;
      background: rgba(155, 170, 193, 0.1);
    }
    &.status-1 {
      color: #6386c1;
      background: rgba(99, 134, 193, 0.1);
    }
  }
}
</style>
