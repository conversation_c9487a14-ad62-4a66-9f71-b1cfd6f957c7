<template>
  <div class="full-height mt-pt-20">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>
  </div>
</template>

<script>
import { columnData } from './config/user.config.js'
export default {
  data() {
    return {
      pageConfig: [
        {
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'active',
                  icon: 'icon_Activation',
                  title: this.$t('激活')
                },
                {
                  id: 'inactive',
                  icon: 'icon_solid_Disable1',
                  title: this.$t('失效')
                }
              ],
              [
                'Filter',
                'Setting',
                {
                  id: 'import',
                  icon: 'icon_solid_Import',
                  title: this.$t('导入'),
                  visibleCondition: () => false
                },
                {
                  id: 'export',
                  icon: 'icon_solid_pushorder',
                  title: this.$t('导出'),
                  visibleCondition: () => false
                }
              ]
            ]
          },
          grid: {
            columnData: columnData,
            dataSource: [],
            asyncConfig: {
              url: '/masterDataManagement/tenant/user/paged-query'
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },

  methods: {
    handleClickToolBar(e) {
      const selected = e.grid.getSelectedRecords()
      let notAllowed = []
      let _id = []
      if (
        selected.length <= 0 &&
        !(
          e.toolbar.id == 'Add' ||
          e.toolbar.id == 'Filter' ||
          e.toolbar.id == 'Refresh' ||
          e.toolbar.id == 'Setting'
        )
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      selected.map((item) => _id.push(item.id))
      if (e.toolbar.id == 'active') {
        notAllowed = selected.filter((el) => el.statusId === '1')
        if (notAllowed.length) {
          return this.$toast({
            content: this.$t('已选行中包含已激活状态，请勿重复操作'),
            type: 'warning'
          })
        }
        this.handleUpdateStatus(_id, 1, '确认进行激活操作？')
      } else if (e.toolbar.id == 'inactive') {
        notAllowed = selected.filter((el) => el.statusId === '3')
        if (notAllowed.length) {
          return this.$toast({
            content: this.$t('已选行中包含已失效状态，请勿重复操作'),
            type: 'warning'
          })
        }
        this.handleUpdateStatus(_id, 3, '确认进行失效操作？')
      }
    },

    handleUpdateStatus(ids, flag, msg) {
      this.$dialog({
        data: {
          title: this.$t('操作'),
          message: msg
        },
        success: () => {
          this.$loading()
          this.$API.base.updateUserStatus({ ids: ids, statusId: flag }).then((res) => {
            this.$hloading()
            if (res.code == 200) {
              this.$refs.templateRef.refreshCurrentGridData()
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
            }
          })
        }
      })
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
