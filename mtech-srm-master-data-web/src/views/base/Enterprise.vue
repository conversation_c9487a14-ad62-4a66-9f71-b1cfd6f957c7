<!--
 * @Author: your name
 * @Date: 2022-01-24 17:47:36
 * @LastEditTime: 2022-03-07 17:19:13
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\base\Enterprise.vue
-->
<template>
  <div class="full-height mt-pt-20">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
    <mt-dialog ref="dialog" :header="headerTitle" css-class="create-proj-dialog" :buttons="buttons">
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <!-- <div class="full-width dialog-subtitle mt-mb-20">{{ $t("基本信息") }}</div> -->
        <mt-form-item prop="identityTypeCode" :label="$t('企业编码类型')">
          <mt-select
            v-model="ruleForm.identityTypeCode"
            :disabled="disabled"
            :fields="fields.TYPE_CODE"
            :data-source="options.TYPE_CODE"
            :show-clear-button="!disabled"
            :allow-filtering="true"
            :placeholder="$t('请选择国家/地区')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="identityCode" :label="$t('企业编码')">
          <mt-input
            v-model="ruleForm.identityCode"
            :disabled="disabled"
            :show-clear-button="!disabled"
            type="text"
            :placeholder="$t('请输入企业编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="enterpriseName" :label="$t('企业名称')">
          <mt-input
            v-model="ruleForm.enterpriseName"
            :disabled="disabled"
            :show-clear-button="!disabled"
            type="text"
            :placeholder="$t('请输入企业名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="enterpriseRegisterTypeCode" :label="$t('企业类型')">
          <mt-select
            v-model="ruleForm.enterpriseRegisterTypeCode"
            :disabled="disabled"
            :fields="fields.ENTERPRISE_TYPE"
            :data-source="options.ENTERPRISE_TYPE"
            :show-clear-button="!disabled"
            :allow-filtering="true"
            :placeholder="$t('请选择企业类型')"
            @select="(e) => (ruleForm.enterpriseRegisterTypeName = e.itemData.name)"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="countryCode" :label="$t('国家')">
          <mt-select
            v-model="ruleForm.countryCode"
            :disabled="disabled"
            :fields="fields.COUNTRY"
            :data-source="options.COUNTRY"
            :show-clear-button="!disabled"
            :allow-filtering="true"
            :placeholder="$t('请选择国家')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="capitalCurrency" :label="$t('货币')">
          <mt-select
            v-model="ruleForm.capitalCurrency"
            :disabled="disabled"
            :fields="fields.CURRENCY"
            :data-source="options.CURRENCY"
            :show-clear-button="!disabled"
            :allow-filtering="true"
            :placeholder="$t('请选择货币')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="corporation" :label="$t('企业法人')">
          <mt-input
            v-model="ruleForm.corporation"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入企业法人')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="defaultAdminName" :label="$t('管理员')">
          <mt-input
            v-model="ruleForm.defaultAdminName"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入管理员')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="defaultAdminPhone" :label="$t('管理员电话')">
          <mt-input
            v-model="ruleForm.defaultAdminPhone"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入管理员电话')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="defaultAdminEmail" :label="$t('管理员邮箱')">
          <mt-input
            v-model="ruleForm.defaultAdminEmail"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入管理员邮箱')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import { PAGE_CONFIG, RULES, OPTIONS, FIELDS } from './config/enterprise.config'
import { formatRules } from '@/utils/util'
export default {
  data() {
    return {
      options: OPTIONS,
      pageConfig: PAGE_CONFIG,
      rules: RULES,
      fields: FIELDS,

      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {},
      type: 'add' // add edit preview
    }
  },
  computed: {
    headerTitle() {
      if (this.type === 'add') {
        return this.$t('新增')
      }
      if (this.type === 'edit') {
        return this.$t('编辑')
      }
      return this.$t('预览')
    },
    disabled() {
      if (this.type === 'preview') {
        return true
      }
      return false
    }
  },
  methods: {
    initData() {
      this.countryGet()
      this.currencyGet()
      this.commonDictTreeGet('TYPE_CODE', 'EnterpriseIdentityType')
      this.commonDictTreeGet('ENTERPRISE_TYPE', 'EnterpriseType')
    },
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const rowSelected = grid.getSelectedRecords()
      if (toolbar.id === 'Add') {
        this.initData()
        this.handleAction('add')
        this.rulesGet('enterpriseAddValid')
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDelete(rowSelected)
      }
    },
    handleClickCellTool(e) {
      const { data, tool } = e
      if (tool.id === 'edit') {
        this.initData()
        this.handleAction('edit', data)
        this.rulesGet('enterpriseEditValid')
      }
      if (tool.id === 'delete') {
        this.handleDelete([data])
      }
      if (tool.id === 'preview') {
        this.initData()
        this.handleAction('preview', data)
      }
    },
    handleAction(type, data) {
      this.type = type
      this.$refs.ruleForm.resetFields()
      this.ruleForm = Object.assign({}, data)
      this.$refs.dialog.ejsRef.show()
    },
    handleDelete(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.base.enterpriseDelete(data).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    },
    save() {
      if (this.type === 'preview') {
        this.cancel()
        return false
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.type === 'add') {
            const data = {
              ...this.ruleForm
            }
            this.$API.base.enterpriseAdd(data).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
          if (this.type === 'edit') {
            delete this.ruleForm.tabIndex
            delete this.ruleForm.approvalTime
            delete this.ruleForm.businessStartDate
            delete this.ruleForm.enterpriseRegisteredDate
            this.$API.base.enterpriseEdit(this.ruleForm).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
        }
      })
    },
    cancel() {
      this.$refs.dialog.ejsRef.hide()
    },
    countryGet() {
      this.$API.dict.countryGet().then((res) => {
        this.options = Object.assign({}, this.options, { COUNTRY: res.data })
      })
    },
    currencyGet() {
      this.$API.dict.currencyGet().then((res) => {
        this.options = Object.assign({}, this.options, { CURRENCY: res.data })
      })
    },
    commonDictTreeGet(option, dictCode) {
      const query = { dictCode }
      this.$API.dict.commonDictTreeGet(query).then((res) => {
        this.options[option] = res.data
      })
    },
    rulesGet(type) {
      this.$API.base[type]().then((res) => {
        this.rules = formatRules(res.data)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-subtitle {
  border-left: 3px solid #eda133;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  padding-left: 10px;
}
/deep/.btn-text {
  color: #4f5b6d;
}
/deep/ .mt-data-grid {
  .status-label {
    font-size: 12px;
    padding: 4px;
    border-radius: 2px;
    &.status-0 {
      color: #9baac1;
      background: rgba(155, 170, 193, 0.1);
    }
    &.status-1 {
      color: #6386c1;
      background: rgba(99, 134, 193, 0.1);
    }
  }
}
/deep/.mt-dialog {
  display: none;
}
</style>
