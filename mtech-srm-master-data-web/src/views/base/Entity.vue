<!--
 * @Author: your name
 * @Date: 2022-02-21 13:56:06
 * @LastEditTime: 2022-03-25 13:55:11
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\base\Entity.vue
-->
<template>
  <div class="full-height mt-pt-20">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
    <mt-dialog ref="dialog" :header="headerTitle" css-class="create-proj-dialog" :buttons="buttons">
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="entityCode" :label="$t('主体代码')">
          <mt-input
            v-model="ruleForm.entityCode"
            :disabled="disabled"
            :show-clear-button="!disabled"
            type="text"
            :placeholder="$t('请输入主体代码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="entityName" :label="$t('主体名称')">
          <mt-input
            v-model="ruleForm.entityName"
            :disabled="disabled"
            :show-clear-button="!disabled"
            type="text"
            :placeholder="$t('请输入主体名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="countryId" :label="$t('国家')">
          <mt-select
            v-model="ruleForm.countryId"
            :disabled="disabled"
            :fields="fields.COUNTRY"
            :data-source="options.COUNTRY"
            :show-clear-button="!disabled"
            :allow-filtering="true"
            :placeholder="$t('请选择国家')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="currencyId" :label="$t('本位币')">
          <mt-select
            v-model="ruleForm.currencyId"
            :disabled="disabled"
            :fields="fields.CURRENCY"
            :data-source="options.CURRENCY"
            :show-clear-button="!disabled"
            :allow-filtering="true"
            :placeholder="$t('请选择本位币')"
          ></mt-select>
        </mt-form-item>
        <!-- <mt-form-item prop="entityType" :label="$t('法律主体')">
          <mt-select
            v-model="ruleForm.entityType"
            :disabled="disabled"
            :fields="fields.YES_OR_NO"
            :data-source="options.YES_OR_NO"
            :show-clear-button="!disabled"
            :allow-filtering="true"
            :placeholder="$t('请选择法律主体')"
          ></mt-select>
        </mt-form-item> -->
        <!-- <mt-form-item
          v-if="ruleForm.entityType === 1"
          prop="enterpriseId"
          :label="$t('关联企业')"
        >
          <mt-select
            v-model="ruleForm.enterpriseId"
            :disabled="disabled"
            :fields="fields.ENTERPRISE"
            :data-source="options.ENTERPRISE"
            :show-clear-button="!disabled"
            :allow-filtering="true"
            :placeholder="$t('请选择关联企业')"
          ></mt-select>
        </mt-form-item> -->
        <mt-form-item prop="externalCode" :label="$t('第三方编码')">
          <mt-input
            v-model="ruleForm.externalCode"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入第三方编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="entityDescription" :label="$t('描述')">
          <mt-input
            v-model="ruleForm.entityDescription"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入描述')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import { PAGE_CONFIG, RULES, OPTIONS, FIELDS } from './config/entity.config'
import { formatRules } from '@/utils/util'
export default {
  data() {
    return {
      options: OPTIONS,
      pageConfig: PAGE_CONFIG,
      rules: RULES,
      fields: FIELDS,

      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {},
      type: 'add' // add edit preview
    }
  },
  computed: {
    headerTitle() {
      if (this.type === 'add') {
        return this.$t('新增')
      }
      if (this.type === 'edit') {
        return this.$t('编辑')
      }
      return this.$t('预览')
    },
    disabled() {
      if (this.type === 'preview') {
        return true
      }
      return false
    }
  },
  methods: {
    initData() {
      this.countryGet()
      this.currencyGet()
      // this.waitAssignEnterpriseGet();
    },
    handleClickCellTitle(e) {
      this.$router.push({
        path: 'entity-info',
        query: {
          id: e.data.id,
          type: 'preview'
        }
      })
    },
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const rowSelected = grid.getSelectedRecords()
      if (toolbar.id === 'Add') {
        this.initData()
        this.handleAction('add')
        this.rulesGet('companyAddValid')
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDelete(rowSelected)
      }
      if (toolbar.id === 'Active' && rowSelected.length) {
        this.handleStatus('active', rowSelected)
      }
      if (toolbar.id === 'Negative' && rowSelected.length) {
        this.handleStatus('negative', rowSelected)
      }
    },
    handleClickCellTool(e) {
      const { data, tool } = e
      if (tool.id === 'edit') {
        this.initData()
        this.handleAction('edit', data)
        this.rulesGet('companyEditValid')
      }
      if (tool.id === 'delete') {
        this.handleDelete([data])
      }
      if (tool.id === 'preview') {
        this.initData()
        this.handleAction('preview', data)
      }
    },
    handleAction(type, data) {
      this.type = type
      this.$refs.ruleForm.resetFields()
      this.ruleForm = Object.assign({}, data)
      this.$refs.dialog.ejsRef.show()
    },
    handleDelete(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.base.companyDelete(data).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    },
    handleStatus(type, rows) {
      const dict = {
        active: { label: this.$t('激活'), value: 1 },
        negative: { label: this.$t('失效'), value: 3 }
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认${dict[type]?.label}选中的数据？`)
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids, statusId: dict[type]?.value }
          this.$API.base.companyStatusUpdate(data).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    },
    save() {
      if (this.type === 'preview') {
        this.cancel()
        return false
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.type === 'add') {
            const data = {
              ...this.ruleForm,
              entityType: 3
            }
            this.$API.base.companyAdd(data).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
          if (this.type === 'edit') {
            delete this.ruleForm.tabIndex
            delete this.ruleForm.approvalTime
            delete this.ruleForm.businessStartDate
            delete this.ruleForm.enterpriseRegisteredDate
            this.ruleForm.statusId = this.ruleForm.statusId.value
            this.$API.base.companyEdit(this.ruleForm).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
        }
      })
    },
    cancel() {
      this.$refs.dialog.ejsRef.hide()
    },
    countryGet() {
      this.$API.dict.countryGet().then((res) => {
        this.options = Object.assign({}, this.options, { COUNTRY: res.data })
      })
    },
    currencyGet() {
      this.$API.dict.currencyGet().then((res) => {
        this.options = Object.assign({}, this.options, { CURRENCY: res.data })
      })
    },
    waitAssignEnterpriseGet() {
      this.$API.base.waitAssignEnterpriseGet().then((res) => {
        this.options = Object.assign({}, this.options, {
          ENTERPRISE: res.data
        })
      })
    },
    rulesGet(type) {
      this.$API.base[type]().then((res) => {
        this.rules = formatRules(res.data)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-subtitle {
  border-left: 3px solid #eda133;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  padding-left: 10px;
}
/deep/.btn-text {
  color: #4f5b6d;
}
/deep/ .mt-data-grid {
  .status-label {
    font-size: 12px;
    padding: 4px;
    border-radius: 2px;
    &.status-0 {
      color: #9baac1;
      background: rgba(155, 170, 193, 0.1);
    }
    &.status-1 {
      color: #6386c1;
      background: rgba(99, 134, 193, 0.1);
    }
  }
}
/deep/.mt-dialog {
  display: none;
}
</style>
