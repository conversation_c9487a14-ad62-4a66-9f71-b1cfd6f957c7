import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-08-20 09:43:34
 * @LastEditTime: 2022-01-25 14:21:52
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\base\config\COMPANY.config.js
 */
export const PAGE_CONFIG = [
  {
    toolbar: ['Add', 'Delete'],
    grid: {
      frozenColumns: 2,
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'identityCode',
          headerText: i18n.t('企业编码'),
          width: '250',
          cellTools: ['edit', 'delete', 'preview']
        },
        {
          field: 'enterpriseName',
          width: '200',
          headerText: i18n.t('企业名称')
        },
        {
          field: 'enterpriseRegisterTypeName',
          headerText: i18n.t('企业类型'),
          width: '200'
        },
        {
          field: 'corporation',
          width: '200',
          headerText: i18n.t('企业法人')
        },
        {
          field: 'defaultAdminName',
          width: '200',
          headerText: i18n.t('管理员')
        },
        {
          field: 'defaultAdminPhone',
          width: '200',
          headerText: i18n.t('管理员电话')
        },
        {
          field: 'createUserName',
          width: '150',
          headerText: i18n.t('创建人')
        },
        {
          width: 'auto',
          field: 'createTime',
          headerText: i18n.t('创建时间')
        }
      ],
      asyncConfig: {
        url: '/masterDataManagement/admin/enterprise/paged-query',
        params: {}
      }
    }
  }
]

export const FIELDS = {
  TYPE_CODE: {
    text: 'name',
    value: 'itemCode'
  },
  COUNTRY: {
    text: 'shortName',
    value: 'countryCode'
  },
  ENTERPRISE_TYPE: {
    text: 'name',
    value: 'itemCode'
  },
  CURRENCY: {
    text: 'currencyName',
    value: 'currencyCode'
  }
}
export const RULES = {}

export const OPTIONS = {
  TYPE_CODE: [],
  COUNTRY: [],
  ENTERPRISE_TYPE: [],
  CURRENCY: []
}
