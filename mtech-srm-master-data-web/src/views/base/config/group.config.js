import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-08-20 09:43:34
 * @LastEditTime: 2022-03-20 16:43:26
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\base\config\COMPANY.config.js
 */
export const PAGE_CONFIG = [
  {
    toolbar: ['Add', 'Delete'],
    grid: {
      frozenColumns: 2,
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'groupName',
          headerText: i18n.t('业务组名称'),
          width: '380',
          cellTools: [
            'edit',
            'delete',
            {
              id: 'member_manage',
              icon: 'icon_solid_Createproject',
              title: i18n.t('成员管理')
            },
            {
              id: 'cate_assign',
              icon: 'icon_solid_Createproject',
              title: i18n.t('分配品类')
            },
            {
              id: 'mat_assign',
              icon: 'icon_solid_Createproject',
              title: i18n.t('分配品项')
            },
            'preview'
          ]
        },
        {
          field: 'groupCode',
          width: '200',
          headerText: i18n.t('业务组编码')
        },
        {
          field: 'groupTypeName',
          width: '200',
          headerText: i18n.t('业务组类型')
        },
        {
          field: 'employeeNum',
          width: '150',
          headerText: i18n.t('业务组成员')
        },
        {
          width: 'auto',
          field: 'updateUserName',
          headerText: i18n.t('更新人')
        }
      ],
      asyncConfig: {
        url: `/masterDataManagement/tenant/business-group/paged-query?BU_CODE=${localStorage.getItem(
          'currentBu'
        )}`,
        params: {}
      }
    }
  }
]

export const RULES = {}

export const OPTIONS = {
  GROUP_TYPE: []
}

export const TS_LEFT_FIELDS = {
  dataSource: [],
  id: 'employeeId',
  text: 'employeeName',
  child: 'children',
  value: 'employeeId'
}

export const TS_RIGHT_FIELDS = {
  dataSource: []
}

export const PAGE_ASSIGN_PLUGIN = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'orgCode',
    headerText: i18n.t('组织编码'),
    width: '150',
    cellTools: ['delete']
  },
  {
    field: 'orgName',
    width: '200',
    headerText: i18n.t('组织名称')
  },
  {
    field: 'orgLevelTypeName',
    width: '100',
    headerText: i18n.t('组织层级')
  },
  {
    field: 'categoryCode',
    width: '150',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    width: '150',
    headerText: i18n.t('品类名称')
  }
]

export const CHOOSE_PAGE_CONFIG = [
  {
    toolbar: [],
    treeGrid: {
      allowPaging: false,
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'orgName',
          headerText: i18n.t('品类名称'),
          width: '150'
        },
        {
          field: 'userGroupContent',
          width: '200',
          headerText: i18n.t('品类编码')
        },
        {
          field: 'categoryCount',
          width: '150',
          headerText: i18n.t('物料数')
        }
      ],
      asyncConfig: {
        url: '/masterDataManagement/tenant/business-group-category-rel/getBusinessGroupOrgCategoryInfo',
        params: {},
        recordsPosition: 'data.orgCategoryInfos'
      }
    }
  }
]

export const DROPDOWN_TREE = {
  dataSource: [],
  value: 'id',
  text: 'name',
  child: 'children'
}
export const DROPDOWN_TREE_CATE = {
  dataSource: [],
  value: 'id',
  text: 'name',
  child: 'children'
}

export const MAT_PAGE_ASSIGN_PLUGIN = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'orgCode',
    headerText: i18n.t('组织编码'),
    width: '150',
    cellTools: ['delete']
  },
  {
    field: 'orgName',
    width: '200',
    headerText: i18n.t('组织名称')
  },
  {
    field: 'orgLevelTypeName',
    width: '100',
    headerText: i18n.t('组织层级')
  },
  {
    field: 'itemGroupCode',
    width: '150',
    headerText: i18n.t('品项组编码')
  },
  {
    field: 'itemGroupName',
    width: '150',
    headerText: i18n.t('品项组名称')
  }
]

export const DROPDOWN_TREE_MAT = {
  dataSource: [],
  value: 'id',
  text: 'orgName',
  child: 'children'
}
