import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-08-20 09:43:34
 * @LastEditTime: 2022-03-25 13:52:03
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\base\config\COMPANY.config.js
 */
export const PAGE_CONFIG = [
  {
    toolbar: [
      'Add',
      'Delete',
      {
        id: 'Active',
        icon: 'icon_solid_Createproject',
        title: i18n.t('激活')
      },
      {
        id: 'Negative',
        icon: 'icon_solid_Createproject',
        title: i18n.t('失效')
      }
    ],
    grid: {
      frozenColumns: 2,
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'entityCode',
          headerText: i18n.t('主体代码'),
          width: '250',
          cellTools: ['edit', 'delete', 'preview']
        },
        {
          field: 'entityName',
          width: '200',
          headerText: i18n.t('主体名称')
        },
        {
          field: 'entityType',
          headerText: i18n.t('主体类型'),
          width: '200',
          valueConverter: {
            type: 'map',
            map: [
              { text: i18n.t('法律主体'), value: 0, cssClass: '' },
              { text: i18n.t('财务主体'), value: 3, cssClass: '' }
            ]
          }
        },
        {
          field: 'accountChartCodes',
          width: '200',
          headerText: i18n.t('科目表')
        },
        {
          field: 'countryName',
          width: '200',
          headerText: i18n.t('国家')
        },
        {
          field: 'currencyName',
          width: '200',
          headerText: i18n.t('本位币')
        },
        {
          field: 'externalCode',
          width: '200',
          headerText: i18n.t('第三方编码')
        },
        {
          field: 'entityDescription',
          width: '200',
          headerText: i18n.t('描述')
        },
        {
          field: 'statusId.value',
          headerText: i18n.t('状态'),
          ignore: true,
          allowFiltering: false,
          width: '150',
          valueConverter: {
            type: 'map',
            map: {
              0: i18n.t('草稿'),
              1: i18n.t('激活'),
              2: i18n.t('分发'),
              3: i18n.t('失效'),
              '-1': i18n.t('待审核'),
              '-2': i18n.t('准备')
            }
          }
        },
        {
          field: 'createUserName',
          width: '150',
          headerText: i18n.t('创建人')
        },
        {
          width: 'auto',
          field: 'createTime',
          headerText: i18n.t('创建时间')
        }
      ],
      asyncConfig: {
        url: '/masterDataManagement/tenant/entity/paged-query',
        params: {},
        rules: [
          {
            condition: '',
            field: 'entity_type',
            label: '',
            operator: 'equal',
            type: '',
            value: 0
          },
          {
            condition: 'or',
            field: 'entity_type',
            label: '',
            operator: 'equal',
            type: '',
            value: 3
          }
        ]
      }
    }
  }
]

export const FIELDS = {
  YES_OR_NO: {
    text: 'text',
    value: 'value'
  },
  COUNTRY: {
    text: 'shortName',
    value: 'id'
  },
  ENTERPRISE: {
    text: 'enterpriseName',
    value: 'id'
  },
  CURRENCY: {
    text: 'currencyName',
    value: 'id'
  }
}
export const RULES = {}

export const OPTIONS = {
  YES_OR_NO: [
    {
      text: i18n.t('是'),
      value: 0
    },
    {
      text: i18n.t('否'),
      value: 3
    }
  ],
  COUNTRY: [],
  ENTERPRISE: [],
  CURRENCY: []
}
