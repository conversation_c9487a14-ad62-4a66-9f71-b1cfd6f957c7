import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-09-08 14:40:54
 * @LastEditTime: 2022-03-04 13:28:36
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\base\config\user.config.js
 */
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '250',
    field: 'userCode',
    headerText: i18n.t('代码')
  },
  {
    width: '150',
    field: 'fullName',
    headerText: i18n.t('姓名')
  },
  // {
  //   // width: "150",
  //   field: "userName",
  //   headerText: "用户名"
  // },
  {
    width: '150',
    field: 'telephone',
    headerText: i18n.t('手机号')
  },
  {
    width: '150',
    field: 'email',
    headerText: i18n.t('邮箱')
  },
  {
    width: '150',
    field: 'userDescription',
    headerText: i18n.t('说明')
  },
  {
    field: 'statusId',
    headerText: i18n.t('状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('草稿'),
        1: i18n.t('激活'),
        2: i18n.t('分发'),
        3: i18n.t('失效'),
        '-1': i18n.t('待审核'),
        '-2': i18n.t('准备')
      }
    }
  },
  {
    width: 'auto',
    field: 'createTime',
    headerText: i18n.t('创建时间')
  }
]
