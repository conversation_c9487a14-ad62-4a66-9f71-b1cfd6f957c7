import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-08-20 09:43:34
 * @LastEditTime: 2022-04-29 18:50:37
 * @LastEditors: OBKoro1
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\base\config\company.config.js
 */
export const PAGE_CONFIG = [
  {
    // toolbar: ["Add","Delete"],
    toolbar: ['Add'],
    grid: {
      frozenColumns: 2,
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'entityCode',
          headerText: i18n.t('公司编码'),
          width: '250',
          cellTools: ['preview']
          // cellTools: ["edit", "delete", "preview"],
        },
        {
          field: 'entityName',
          width: '200',
          headerText: i18n.t('公司名称')
        },
        {
          field: 'statusId.label',
          headerText: i18n.t('状态'),
          width: '150'
          // valueConverter: {
          //   type: "map",
          //   map: [
          //     {
          //       status: 0,
          //       label: i18n.t("无效"),
          //       cssClass: ["status-label", "status-0"]
          //     },
          //     {
          //       status: 1,
          //       label: i18n.t("有效"),
          //       cssClass: ["status-label", "status-0"]
          //     }
          //   ],
          //   fields: { text: "label", value: "status" }
          // }
        },
        {
          field: 'corporation',
          width: '150',
          headerText: i18n.t('公司法人')
        },
        {
          field: 'phoneNumber',
          width: '150',
          headerText: i18n.t('联系电话')
        },
        {
          field: 'createUserName',
          width: '150',
          headerText: i18n.t('创建人')
        },
        {
          width: 'auto',
          field: 'createTime',
          headerText: i18n.t('创建时间'),
          valueConverter: {
            type: 'date'
          }
        }
      ],
      asyncConfig: {
        url: '/masterDataManagement/tenant/entity/paged-query',
        params: {},
        rules: [
          {
            condition: '',
            field: 'entity_type',
            label: '',
            operator: 'equal',
            type: '',
            value: 0
          }
        ]
      }
    }
  }
]

export const FIELDS = {
  COUNTRY: {
    text: 'shortName',
    value: 'id'
  }
}
export const RULES = {}

export const OPTIONS = {
  YES_OR_NO: [
    {
      text: i18n.t('是'),
      value: 1
    },
    {
      text: i18n.t('否'),
      value: 0
    }
  ],
  IS_USEFUL: [
    {
      label: i18n.t('启用'),
      value: '1'
    },
    {
      label: i18n.t('禁用'),
      value: '0'
    }
  ],
  COUNTRY: []
}
