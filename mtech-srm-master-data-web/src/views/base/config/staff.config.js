import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-08-20 09:43:34
 * @LastEditTime: 2022-06-10 16:19:31
 * @LastEditors: zhaoriyang3 <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\base\config\staff.config.js
 */
export const DROPDOWN_TREE = {
  dataSource: [],
  value: 'id',
  text: 'name',
  child: 'children'
}

export const PAGE_CONFIG = [
  {
    useBaseConfig: false,
    useToolTemplate: false,
    toolbar: [
      [
        {
          id: 'Add',
          icon: 'icon_solid_Createorder',
          // permission: ["O_02_1169"],
          title: i18n.t('新增')
        }
        // {
        //   id: "Delete",
        //   icon: "icon_solid_Delete",
        //   permission: ["O_02_1170"],
        //   title: i18n.t("删除"),
        // },
        // {
        //   id: "active",
        //   icon: "icon_solid_Createorder",
        //   title: i18n.t("激活"),
        // },
      ],
      [
        'Filter',
        'Refresh',
        'Setting',
        {
          id: 'import',
          icon: 'icon_solid_Import',
          title: i18n.t('导入'),
          visibleCondition: () => true
        },
        {
          id: 'export',
          icon: 'icon_solid_pushorder',
          title: i18n.t('导出'),
          visibleCondition: () => false
        }
      ]
    ],
    grid: {
      frozenColumns: 2,
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'employeeName',
          width: '250',
          headerText: i18n.t('员工姓名'),
          cellTools: [
            'edit',
            'preview',
            {
              id: 'Active',
              icon: 'icon_list_enable',
              title: i18n.t('激活'),
              visibleCondition: (data) => data.statusId.value != 1
            },
            {
              id: 'StopUse',
              icon: 'icon_list_disable',
              title: i18n.t('停用'),
              visibleCondition: (data) => data.statusId.value == 1
            }
          ]
        },
        {
          field: 'accountName',
          headerText: i18n.t('账号'),
          width: '200',
          ignore: true
        },
        {
          field: 'statusId.label',
          headerText: i18n.t('状态'),
          width: '150',
          ignore: true
        },
        {
          field: 'sourceTypeName',
          headerText: i18n.t('来源'),
          width: '150'
        },
        {
          field: 'companyName',
          width: '200',
          headerText: i18n.t('归属公司')
        },
        {
          field: 'departmentName',
          width: '200',
          headerText: i18n.t('部门')
        },
        {
          field: 'stationName',
          width: '200',
          headerText: i18n.t('岗位'),
          ignore: true
        },
        {
          field: 'createUserName',
          width: '150',
          headerText: i18n.t('创建人')
        },
        {
          width: 'auto',
          field: 'createTime',
          headerText: i18n.t('创建时间'),
          valueConverter: {
            type: 'date'
          }
        }
      ],
      asyncConfig: {
        url: '/masterDataManagement/tenant/organization/organizationEmployeePagedQuery',
        params: {}
      }
    }
  }
]

export const RULES = {}

export const OPTIONS = {
  COMPANY: [],
  DEPART: [],
  STATION: [],
  YES_OR_NO: [
    {
      text: i18n.t('是'),
      value: 1
    },
    {
      text: i18n.t('否'),
      value: 0
    }
  ],
  IS_USEFUL: [
    {
      label: i18n.t('启用'),
      value: '1'
    },
    {
      label: i18n.t('禁用'),
      value: '0'
    }
  ]
}

export const FIELDS = {
  COMPANY: {
    text: 'orgName',
    value: 'id'
  },
  DEPART: {
    text: 'orgName',
    value: 'id'
  },
  STATION: {
    text: 'orgName',
    value: 'id'
  }
}
