import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2022-02-25 15:18:30
 * @LastEditTime: 2022-04-07 13:40:08
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\base\config\entityInfoTab1.config.js
 */
export const PAGE_CONFIG1 = [
  {
    toolbar: ['Add', 'Delete'],
    grid: {
      allowPaging: false,
      frozenColumns: 2,
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'accountChartCode',
          headerText: i18n.t('科目表代码'),
          width: '250'
        },
        {
          field: 'accountChartName',
          width: '200',
          headerText: i18n.t('科目表名称')
        },
        {
          field: 'accountChartType',
          headerText: i18n.t('科目表类型'),
          width: '200',
          valueConverter: {
            type: 'map',
            map: [
              { text: i18n.t('运营科目表'), value: 0, cssClass: '' },
              { text: i18n.t('国家科目表'), value: 1, cssClass: '' },
              { text: i18n.t('集团科目表'), value: 2, cssClass: '' }
            ]
          }
        },
        {
          field: 'countryName',
          width: 'auto',
          headerText: i18n.t('国家')
        }
      ],
      dataSource: [],
      asyncConfig: {
        // url: "/masterDataManagement/tenant/entity-account-chart-rel/criteria-query",
        // params: {},
      }
    }
  }
]

export const PAGE_CONFIG2 = [
  {
    toolbar: ['Add', 'Delete'],
    grid: {
      allowPaging: false,
      frozenColumns: 2,
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'accountSubjectCode',
          headerText: i18n.t('科目代码'),
          width: '250',
          cellTools: ['edit']
        },
        {
          field: 'accountSubjectName',
          width: '200',
          headerText: i18n.t('科目名称')
        },
        {
          field: 'accountChartName',
          width: '200',
          headerText: i18n.t('科目表')
        },
        {
          field: 'accountChartTypeName',
          headerText: i18n.t('科目表类型'),
          width: '200'
        },
        {
          field: 'reconciliationAccount',
          width: 'auto',
          headerText: i18n.t('是否统驭科目'),
          valueConverter: {
            type: 'map',
            map: [
              { text: i18n.t('是'), value: 1, cssClass: '' },
              { text: i18n.t('否'), value: 0, cssClass: '' }
            ]
          }
        }
      ],
      dataSource: [],
      asyncConfig: {
        // url: "/masterDataManagement/tenant/entity-account-subject/criteria-query",
        // params: {},
      }
    }
  }
]

export const PAGE_CONFIG3 = [
  {
    // toolbar: ["Add", "Delete"],
    grid: {
      allowPaging: false,
      frozenColumns: 2,
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'accountChartCode',
          headerText: i18n.t('科目表代码'),
          width: '250'
        },
        {
          field: 'accountChartName',
          width: '200',
          headerText: i18n.t('科目表名称')
        },
        {
          field: 'accountChartType',
          headerText: i18n.t('科目表类型'),
          width: '200',
          valueConverter: {
            type: 'map',
            map: [
              { text: i18n.t('运营科目表'), value: 0, cssClass: '' },
              { text: i18n.t('国家科目表'), value: 1, cssClass: '' },
              { text: i18n.t('集团科目表'), value: 2, cssClass: '' }
            ]
          }
        },
        {
          field: 'countryName',
          width: '200',
          headerText: i18n.t('国家')
        }
      ],
      dataSource: [],
      asyncConfig: {
        // url: "/masterDataManagement/tenant/entity-account-chart-rel/ready-account-charts",
        // params: {},
        // methods: "get",
        // recordsPosition: "data",
      }
    }
  }
]

export const PAGE_CONFIG4 = [
  {
    grid: {
      allowPaging: false,
      frozenColumns: 2,
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'accountSubjectCode',
          headerText: i18n.t('科目代码'),
          width: '200'
        },
        {
          field: 'accountSubjectName',
          width: '200',
          headerText: i18n.t('科目名称')
        },
        {
          field: 'accountChartName',
          width: 'auto',
          headerText: i18n.t('科目表')
        }
      ],
      dataSource: [],
      asyncConfig: {}
    }
  }
]

export const FIELDS = {
  FISCAL_YEAR: {
    text: 'fiscalYearName',
    value: 'id'
  }
}
export const RULES = {}

export const OPTIONS = {
  FISCAL_YEAR: []
}
