import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2022-02-25 15:18:30
 * @LastEditTime: 2022-03-24 18:10:15
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\base\config\entityInfoTab1.config.js
 */
export const PAGE_CONFIG1 = [
  {
    grid: {
      allowPaging: false,
      frozenColumns: 2,
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'costCenterCode',
          headerText: i18n.t('成本中心代码'),
          width: '250'
        },
        {
          field: 'costCenterName',
          width: 'auto',
          headerText: i18n.t('成本中心名称')
        }
      ],
      dataSource: [],
      asyncConfig: {}
    }
  }
]

export const PAGE_CONFIG2 = [
  {
    grid: {
      allowPaging: false,
      frozenColumns: 2,
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'profitCenterCode',
          headerText: i18n.t('利润中心代码'),
          width: '250'
        },
        {
          field: 'profitCenterName',
          width: 'auto',
          headerText: i18n.t('利润中心名称')
        }
      ],
      dataSource: [],
      asyncConfig: {}
    }
  }
]
