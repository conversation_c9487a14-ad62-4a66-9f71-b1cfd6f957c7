<template>
  <mt-dialog
    ref="dialog"
    :header="$t('成员管理')"
    :enable-resize="false"
    :position="{ X: 'right', Y: 'top' }"
    css-class="create-proj-dialog right-wrapper"
    :buttons="buttons"
    :visible="visible"
    @close="closeDialog"
  >
    <div class="full-height">
      <mt-template-page
        v-if="visible"
        ref="templatePage"
        :hidden-tabs="true"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
      ></mt-template-page>
    </div>

    <MemberStaffSelecter
      ref="memberSelecter"
      :business-group-id="groupId"
      @save="saveMemberStaffSelecter"
    />
  </mt-dialog>
</template>

<script>
import MemberStaffSelecter from './MemberStaffSelecter'

export default {
  components: {
    MemberStaffSelecter
  },
  data() {
    return {
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          toolbar: {
            useBaseConfig: false,
            tools: [['Add', 'Delete']]
          },
          grid: {
            dataSource: [],
            asyncConfig: {
              url: this.$API.base.employeesInBusinessGroupPage,
              params: {},
              recordsPosition: 'data.records'
            },
            columnData: [
              {
                width: 50,
                type: 'checkbox'
              },
              {
                field: 'employeeName',
                width: '200',
                headerText: this.$t('成员姓名')
              }
            ]
          }
        }
      ],
      groupId: '',
      visible: false
    }
  },
  methods: {
    show(groupId) {
      this.groupId = groupId
      this.pageConfig[0].grid.asyncConfig.params.businessGroupId = groupId
      this.$refs.dialog.ejsRef.show()
      this.visible = true
    },
    async initData() {
      this.$refs.templatePage.refreshCurrentGridData()
    },
    hide() {
      this.$refs.dialog.ejsRef.hide()
      this.closeDialog()
    },
    save() {
      this.hide()
    },
    saveMemberStaffSelecter() {
      this.initData()
    },
    handleClickToolBar({ toolbar, grid }) {
      this['handleClickToolBar' + toolbar.id](grid)
    },
    handleClickToolBarAdd() {
      this.$refs.memberSelecter.show()
    },
    closeDialog() {
      this.visible = false
    },
    handleClickToolBarDelete(grid) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const records = (grid.getMtechGridRecords || grid.getSelectedRecords)()
          const employeeIds = records.map((e) => e.employeeId)
          this.$API.base
            .delBusinessGroupAndEmployeeRel({
              businessGroupId: this.groupId,
              employeeIds
            })
            .then(() => {
              this.$refs.templatePage.refreshCurrentGridData()
            })
        }
      })
    }
  }
}
</script>
