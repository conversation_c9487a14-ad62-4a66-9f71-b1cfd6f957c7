<!--
 * @Author: your name
 * @Date: 2022-02-25 15:02:26
 * @LastEditTime: 2022-04-07 14:20:16
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\base\components\EntityInfoTab1.vue
-->
<template>
  <div class="tab1-info full-height mt-px-20">
    <div class="subtitle mt-my-20 mt-px-10">{{ $t('基本信息') }}</div>
    <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
      <mt-row :gutter="20">
        <mt-col :sm="8" :md="6" :lg="4" :xl="4">
          <mt-form-item prop="fiscalYearId" :label="$t('会计年度')">
            <mt-select
              v-model="ruleForm.fiscalYearId"
              :fields="fields.FISCAL_YEAR"
              :data-source="options.FISCAL_YEAR"
              :allow-filtering="true"
              :placeholder="$t('请选择会计年度')"
              @select="fiscalYearChange"
            ></mt-select>
          </mt-form-item>
        </mt-col>
      </mt-row>
    </mt-form>
    <div class="subtitle mt-my-20 mt-px-10">{{ $t('科目表') }}</div>
    <div class="tab1-info--table">
      <mt-template-page
        ref="tepPage1"
        :hidden-tabs="true"
        :template-config="pageConfig1"
        @handleClickToolBar="handleClickToolBar1"
      ></mt-template-page>
    </div>
    <div class="subtitle mt-my-20 mt-px-10">{{ $t('会计科目') }}</div>
    <div class="tab1-info--table">
      <mt-template-page
        ref="tepPage2"
        :hidden-tabs="true"
        :template-config="pageConfig2"
        @handleClickToolBar="handleClickToolBar2"
        @handleClickCellTool="handleClickCellTool"
      ></mt-template-page>
    </div>

    <mt-dialog
      ref="dialog1"
      :header="$t('新增')"
      css-class="create-proj-dialog"
      :buttons="buttons1"
      @close="cancel1"
    >
      <mt-template-page
        ref="tepPage3"
        :hidden-tabs="true"
        :template-config="pageConfig3"
      ></mt-template-page>
    </mt-dialog>

    <mt-dialog
      ref="dialog2"
      :header="$t('新增')"
      css-class="create-proj-dialog"
      :buttons="buttons2"
      @close="cancel2"
    >
      <mt-template-page
        ref="tepPage4"
        :hidden-tabs="true"
        :template-config="pageConfig4"
      ></mt-template-page>
    </mt-dialog>

    <mt-dialog
      ref="dialog3"
      :header="$t('编辑')"
      css-class="create-proj-dialog"
      :buttons="buttons3"
      @close="cancel3"
    >
      <mt-form ref="editForm" :model="editForm" :rules="rules">
        <mt-form-item prop="accountSubjectName" :label="$t('科目名称')">
          <mt-input
            v-model="editForm.accountSubjectName"
            type="text"
            :placeholder="$t('请输入科目名称')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import {
  PAGE_CONFIG1,
  PAGE_CONFIG2,
  PAGE_CONFIG3,
  PAGE_CONFIG4,
  RULES,
  OPTIONS,
  FIELDS
} from '../config/entityInfoTab1.config'
export default {
  props: {
    entityInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      options: OPTIONS,
      pageConfig1: [],
      pageConfig2: [],
      pageConfig3: [],
      pageConfig4: [],
      rules: RULES,
      fields: FIELDS,

      ruleForm: {
        fiscalYearId: ''
      },

      editForm: {},

      buttons1: [
        {
          click: this.cancel1,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save1,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      buttons2: [
        {
          click: this.cancel2,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save2,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      buttons3: [
        {
          click: this.cancel3,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save3,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      entityId: this.entityInfo.id || this.$route.query.id
    }
  },
  mounted() {
    this.fiscalYearGet()
    if (this.entityInfo.id) {
      this.init()
    }
  },
  watch: {
    entityInfo: {
      handler() {
        this.entityId = this.entityInfo.id
        this.init()
      },
      deep: true
    }
  },
  methods: {
    init() {
      this.fiscalYearAssigned()
      this.list1Get(this.entityId)
      this.list2Get(this.entityId)
    },
    list1Get(entityId) {
      PAGE_CONFIG1[0].grid.asyncConfig = Object.assign(
        {},
        {
          url: '/masterDataManagement/tenant/entity-account-chart-rel/criteria-query',
          params: {
            entityId
          },
          recordsPosition: 'data'
        }
      )
      this.pageConfig1 = PAGE_CONFIG1
    },
    list2Get(entityId) {
      PAGE_CONFIG2[0].grid.asyncConfig = Object.assign(
        {},
        {
          url: '/masterDataManagement/tenant/entity-account-subject/criteria-query',
          params: {
            entityId
          },
          recordsPosition: 'data'
        }
      )
      this.pageConfig2 = PAGE_CONFIG2
    },
    handleClickToolBar1(e) {
      const { toolbar, grid } = e
      const rowSelected = grid.getSelectedRecords()
      if (toolbar.id === 'Add') {
        this.handleAction1('add')
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDelete1(rowSelected)
      }
    },
    handleAction1() {
      this.$refs.dialog1.ejsRef.show()
      PAGE_CONFIG3[0].grid.asyncConfig = Object.assign(
        {},
        {
          url: '/masterDataManagement/tenant/entity-account-chart-rel/ready-account-charts',
          params: {
            entityId: this.entityId
          },
          methods: 'get',
          recordsPosition: 'data'
        }
      )
      this.pageConfig3 = PAGE_CONFIG3
    },
    save1() {
      const currentTab = this.$refs.tepPage3.getCurrentTabRef()
      const rowSelect = currentTab.grid.getSelectedRecords()
      const accountChartIds = rowSelect.map((e) => e.id)

      const data = {
        accountChartIds,
        entityId: this.entityId
      }
      this.$API.finance.chartAssign(data).then(() => {
        this.$refs.tepPage1.refreshCurrentGridData()
        this.$refs.tepPage2.refreshCurrentGridData()
        this.cancel1()
      })
    },
    cancel1() {
      this.pageConfig3[0].grid = Object.assign({}, this.pageConfig3[0].grid, {
        dataSource: [],
        asyncConfig: {}
      })
      this.$refs.dialog1.ejsRef.hide()
    },
    handleDelete1(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.finance.chartAssignedDelete(data).then(() => {
            this.$refs.tepPage1.refreshCurrentGridData()
            this.$refs.tepPage2.refreshCurrentGridData()
          })
        }
      })
    },
    handleClickToolBar2(e) {
      const { toolbar, grid } = e
      const rowSelected = grid.getSelectedRecords()
      if (toolbar.id === 'Add') {
        this.handleAction2('add')
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDelete2(rowSelected)
      }
    },
    handleClickCellTool(e) {
      const { data, tool } = e
      if (tool.id === 'edit') {
        this.handleAction2('edit', data)
      }
    },
    handleAction2(type, data) {
      if (type === 'add') {
        PAGE_CONFIG4[0].grid.asyncConfig = Object.assign(
          {},
          {
            url: '/masterDataManagement/tenant/entity-account-subject/findPendingAddSubjectByEntityId',
            params: {
              entityId: this.entityId
            },
            methods: 'post',
            recordsPosition: 'data'
          }
        )
        this.pageConfig4 = PAGE_CONFIG4
        this.$refs.dialog2.ejsRef.show()
      } else if (type === 'edit') {
        this.$refs.editForm.resetFields()
        this.editForm = Object.assign({}, data)
        this.$refs.dialog3.ejsRef.show()
      }
    },
    save2() {
      const currentTab = this.$refs.tepPage4.getCurrentTabRef()
      const rowSelect = currentTab.grid.getSelectedRecords()
      const accountSubjectIds = rowSelect.map((e) => e.id)

      const data = {
        accountSubjectIds,
        entityId: this.entityId
      }
      this.$API.finance.subjectAssign(data).then(() => {
        this.$refs.tepPage1.refreshCurrentGridData()
        this.$refs.tepPage2.refreshCurrentGridData()
        this.cancel2()
      })
    },
    cancel2() {
      this.pageConfig4[0].grid = Object.assign({}, this.pageConfig4[0].grid, {
        dataSource: [],
        asyncConfig: {}
      })
      this.$refs.dialog2.ejsRef.hide()
    },
    handleDelete2(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.finance.subjectAssignedDelete(data).then(() => {
            this.$refs.tepPage1.refreshCurrentGridData()
            this.$refs.tepPage2.refreshCurrentGridData()
          })
        }
      })
    },
    save3() {
      const data = {
        ...this.editForm
      }
      this.$API.finance.subjectAssignEdit(data).then(() => {
        this.$refs.tepPage2.refreshCurrentGridData()
        this.cancel3()
      })
    },
    cancel3() {
      this.$refs.dialog3.ejsRef.hide()
    },

    fiscalYearChange(e) {
      if (e.itemData.id) {
        const data = {
          entityId: this.entityId,
          fiscalYearId: e.itemData.id
        }
        this.$API.finance.fiscalYearSave(data).then(() => {})
      }
    },
    fiscalYearAssigned() {
      const query = {
        entityId: this.entityId
      }
      this.$API.finance.fiscalYearAssigned(query).then((res) => {
        this.ruleForm.fiscalYearId = res.data[0]?.fiscalYearId
      })
    },
    fiscalYearGet() {
      this.$API.finance.fiscalYearGet({}).then((res) => {
        this.options = Object.assign({}, this.options, {
          FISCAL_YEAR: res.data
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.tab1-info {
  overflow: auto;
  .subtitle {
    font-size: 16px;
    font-family: PingFangSC;
    font-weight: 600;
    color: rgba(41, 41, 41, 1);
    border-radius: 2px 0 0 2px;
    border-left: 3px solid rgba(0, 70, 156, 1);
  }
  &--table {
    /deep/.mt-data-grid > div[role='grid'] {
      border-left: 1px solid #e6e9ed;
      border-right: 1px solid #e6e9ed;
    }
  }
}
</style>
