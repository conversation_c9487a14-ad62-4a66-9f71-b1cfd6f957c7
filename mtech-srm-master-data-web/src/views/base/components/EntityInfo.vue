<!--
 * @Author: your name
 * @Date: 2022-02-23 16:38:10
 * @LastEditTime: 2022-03-28 15:22:01
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\base\components\EntityInfo.vue
-->
<template>
  <div class="info flex justify-between mt-pt-20 full-height">
    <div :class="['left full-height', isShow ? 'left-show' : 'left-hide']">
      <div v-show="isShow" class="left-header mt-pa-20">
        <div class="left-header--title mt-mb-20">{{ $t('主体管理') }}</div>
        <div class="e-input-group left-header--search">
          <mt-icon
            name="icon_search"
            class="left-header--search__icon mt-mr-10 cursor-pointer"
          ></mt-icon>
          <input
            v-model="searchForm.keyword"
            class="e-input"
            type="text"
            @input="getList"
            :placeholder="$t('请输入主体编号、名称')"
          />
        </div>
      </div>
      <div class="left-list">
        <div
          v-for="item in itemList"
          :key="item.id"
          :class="['left-list--item', currentItem.id === item.id ? 'active' : '']"
          @click="itemChange(item)"
        >
          <div class="left-list--item__title">{{ item.entityCode }}</div>
          <div class="left-list--item__subtitle">
            {{ item.entityName }}
          </div>
        </div>
      </div>
      <div class="left-arrow" @click="isShow = !isShow">
        <mt-icon :name="isShow ? 'icon_arrow_left' : 'icon_arrow_right'"></mt-icon>
      </div>
    </div>
    <div :class="['right full-height', isShow ? 'right-show' : 'right-hide']">
      <div class="item-info mt-pa-20">
        <div class="flex justify-end mt-mb-20">
          <mt-button
            css-class="e-flat"
            :is-primary="true"
            @click="$router.push('/masterdata/entity')"
            >{{ $t('返回') }}</mt-button
          >
        </div>
        <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
          <mt-row :gutter="20">
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="entityCode" :label="$t('主体代码')">
                <mt-input
                  v-model="ruleForm.entityCode"
                  :show-clear-button="true"
                  :disabled="true"
                  type="text"
                  :placeholder="$t('请输入主体代码')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="entityName" :label="$t('主体名称')">
                <mt-input
                  v-model="ruleForm.entityName"
                  :show-clear-button="true"
                  :disabled="true"
                  type="text"
                  :placeholder="$t('请输入主体名称')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="entityType" :label="$t('主体类型')">
                <mt-input
                  v-model="ruleForm.entityType"
                  :show-clear-button="true"
                  :disabled="true"
                  type="text"
                  :placeholder="$t('请输入主体类型')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="enterpriseName" :label="$t('关联企业')">
                <mt-input
                  v-model="ruleForm.enterpriseName"
                  :show-clear-button="true"
                  :disabled="true"
                  type="text"
                  :placeholder="$t('请输入关联企业')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="externalCode" :label="$t('第三方编码')">
                <mt-input
                  v-model="ruleForm.externalCode"
                  :show-clear-button="true"
                  :disabled="true"
                  type="text"
                  :placeholder="$t('请输入第三方编码')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="countryName" :label="$t('国家')">
                <mt-input
                  v-model="ruleForm.countryName"
                  :show-clear-button="true"
                  :disabled="true"
                  type="text"
                  :placeholder="$t('请输入国家')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="currencyName" :label="$t('本位币')">
                <mt-input
                  v-model="ruleForm.currencyName"
                  :show-clear-button="true"
                  :disabled="true"
                  type="text"
                  :placeholder="$t('请输入本位币')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="statusDescription" :label="$t('状态')">
                <mt-input
                  v-model="ruleForm.statusDescription"
                  :show-clear-button="true"
                  :disabled="true"
                  type="text"
                  :placeholder="$t('请输入状态')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="8" :md="6" :lg="4" :xl="4">
              <mt-form-item prop="entityDescription" :label="$t('描述')">
                <mt-input
                  v-model="ruleForm.entityDescription"
                  :show-clear-button="true"
                  :disabled="true"
                  type="text"
                  :placeholder="$t('请输入描述')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
          </mt-row>
        </mt-form>
      </div>

      <div class="item-detail mt-mt-20">
        <div class="item-detail--tabs flex justify-between items-center mt-px-10">
          <mt-tabs
            :e-tab="false"
            :data-source="dataSource"
            overflow-mode="Popup"
            @handleSelectTab="tabSelected"
          ></mt-tabs>
        </div>
        <div class="item-detail--pannel">
          <!-- <div style="height: 1000px"></div> -->
          <component ref="viewRef" :is="view" :entity-info="ruleForm"></component>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { utils } from '@mtech-common/utils'
import EntityInfoTab1 from './EntityInfoTab1.vue'
import EntityInfoTab2 from './EntityInfoTab2.vue'

export default {
  components: {
    EntityInfoTab1,
    EntityInfoTab2
  },
  data() {
    return {
      searchForm: {
        keyword: ''
      },
      itemList: [],
      currentItem: {},

      ruleForm: {},
      rules: {},

      dataSource: [
        { title: this.$t('财务'), view: 'EntityInfoTab1' },
        { title: this.$t('管理会计'), view: 'EntityInfoTab2' }
      ],
      selectedIndex: 0,

      isShow: true,
      isEdit: false
    }
  },
  computed: {
    view() {
      return this.dataSource[this.selectedIndex].view
    }
  },
  mounted() {
    this.getList = utils.debounce(this.getList, 500)
    this.getList()
  },
  methods: {
    getList() {
      const query = {
        page: {
          currentPage: 1,
          pageSize: 10
        },
        rules: [
          {
            condition: '',
            field: 'entity_type',
            label: '',
            operator: 'equal',
            type: '',
            value: 0
          },
          {
            condition: 'or',
            field: 'entity_type',
            label: '',
            operator: 'equal',
            type: '',
            value: 3
          }
        ]
      }
      this.$API.finance.entityList(query).then(async (res) => {
        const data = res.data.records
        const currentItemId = this.$route.query.id
        const currentIndex = data.findIndex((e) => e.id === currentItemId)

        this.itemList = data
        if (currentIndex > -1) {
          this.itemChange(data[currentIndex])
        } else {
          await this.getDetails()
          this.itemList.unshift(this.ruleForm)
          this.itemChange(data[0])
        }
      })
    },
    itemChange(item) {
      this.currentItem = item
      this.getDetails()
    },
    async getDetails() {
      const query = {
        entityId: this.currentItem.id || this.$route.query.id
      }
      await this.$API.finance.entityDetails(query).then((res) => {
        this.ruleForm = res.data
      })
    },
    tabSelected(e) {
      this.selectedIndex = e
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .mt-tabs {
  background: none;
}

.info {
  .left {
    position: relative;
    background-color: #fff;
    border-radius: 8px;
    &-show {
      width: 281px;
    }
    &-hide {
      width: 0px;
    }
    &-header {
      border-bottom: 1px solid #e8e8e8;
      &--title {
        font-size: 20px;
        font-family: PingFangSC;
        font-weight: 600;
        color: rgba(41, 41, 41, 1);
      }
      &--search {
        &__icon {
          margin-top: 7px;
        }
      }
    }
    &-list {
      height: calc(100% - 136px);
      padding: 5px 0;
      overflow: auto;
      &--item {
        height: 70px;
        padding: 15px 20px;
        &__title {
          height: 20px;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(0, 70, 156, 1);
        }
        &__subtitle {
          height: 19px;
          font-size: 12px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(154, 154, 154, 1);
        }
      }
      .active {
        background: rgba(245, 246, 249, 1);
      }
    }
    &-arrow {
      position: absolute;
      right: -12px;
      top: 85px;
      width: 12px;
      height: 60px;
      line-height: 60px;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 0 4px 4px 0;
      font-size: 12px;
      color: #fff;
    }
  }
  .right {
    &-show {
      width: calc(100% - 291px);
    }
    &-hide {
      width: calc(100% - 10px);
    }
    .item-info {
      background-color: #fff;
      height: 228px;
      border: 1px solid rgba(232, 232, 232, 1);
      border-radius: 8px;
      /deep/ .label {
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(154, 154, 154, 1);
        margin-bottom: 10px;
      }
    }

    .item-detail {
      height: calc(100% - 248px);
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(232, 232, 232, 1);
      border-radius: 8px;
      &--tabs {
        height: 52px;
        border-bottom: 1px solid rgba(232, 232, 232, 1);
      }
      &--pannel {
        height: calc(100% - 52px);
      }
    }
  }
}
</style>
