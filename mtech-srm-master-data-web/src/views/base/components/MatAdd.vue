<!--
 * @Author: your name
 * @Date: 2021-09-11 15:17:36
 * @LastEditTime: 2022-03-20 14:50:53
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\base\components\ChooseCate.vue
-->
<template>
  <div>
    <mt-dialog ref="dialog" :header="headerTitle" css-class="create-proj-dialog" :buttons="buttons">
      <div class="full-height">
        <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
          <mt-form-item prop="organizationId" :label="$t('组织')">
            <mt-DropDownTree
              id="orgDownTree"
              v-model="ruleForm.organizationId"
              :fields="dropDownTree"
              filter-bar-placeholder="Search"
              :allow-filtering="true"
              :placeholder="$t('请选择组织')"
            ></mt-DropDownTree>
          </mt-form-item>
          <mt-form-item prop="itemGroupIds" :label="$t('品项组')">
            <mt-select
              v-model="ruleForm.itemGroupIds"
              :disabled="disabled"
              :data-source="options.MAT_GROUP"
              :show-clear-button="true"
              :allow-filtering="true"
              :placeholder="$t('请选择品项组')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="itemCodes" :label="$t('品项/物料')">
            <mt-input
              v-model="ruleForm.itemCodes"
              :disabled="disabled"
              :show-clear-button="true"
              type="text"
              :placeholder="$t('请输入品项/物料')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="businessGroupName" :label="$t('业务组名称')">
            <mt-input
              v-model="ruleForm.businessGroupName"
              :disabled="true"
              :show-clear-button="true"
              type="text"
              :placeholder="$t('请输入业务组名称')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import { RULES, DROPDOWN_TREE_MAT } from '../config/group.config'
import { formatRules } from '@/utils/util'
export default {
  props: {
    groupInfo: {
      required: true,
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      dropDownTree: DROPDOWN_TREE_MAT,
      // pageConfig: CHOOSE_PAGE_CONFIG,
      options: {
        MAT_GROUP: []
      },
      rules: RULES,

      ruleForm: {
        businessGroupName: '',
        organizationId: [],
        itemGroupIds: '',
        itemCodes: ''
      },

      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      type: 'add'
    }
  },
  computed: {
    headerTitle() {
      if (this.type === 'add') {
        return this.$t('新增')
      }
      if (this.type === 'edit') {
        return this.$t('编辑')
      }
      return this.$t('预览')
    },
    disabled() {
      if (this.type === 'preview') {
        return true
      }
      return false
    }
  },
  methods: {
    save() {
      const data = {
        businessGroupId: this.groupInfo.id,
        organizationId: this.ruleForm.organizationId.join(''),
        itemGroupIds: [this.ruleForm.itemGroupIds],
        itemCodes: this.ruleForm.itemCodes
      }
      this.$API.base.matAssignSave(data).then(() => {
        this.$emit('save')
        this.hide()
      })
    },
    // eslint-disable-next-line no-unused-vars
    async show(type, data) {
      this.$refs.dialog.ejsRef.show()
      this.type = type
      await this.dropDownTreeDataGet()
      this.matGroupGet()
      this.ruleForm = {
        businessGroupName: this.groupInfo.groupName,
        organizationId: [],
        itemGroupIds: '',
        itemCodes: ''
      }
    },
    hide() {
      this.$refs.dialog.ejsRef.hide()
    },
    async dropDownTreeDataGet() {
      const query = {
        organizationLevelCodes: ['ORG06'],
        orgType: 'ORG001PRO',
        includeItself: true
      }
      await this.$API.org.getOrgTreeInGroup(query).then((res) => {
        this.dropDownTree = Object.assign({}, this.dropDownTree, {
          dataSource: res.data
          // dataSource: TreeDataSetSelect(res.data, this.ruleForm.organizationId)
        })
      })
    },
    async matGroupGet() {
      await this.$API.base.matGroupGet({}).then((res) => {
        this.options = Object.assign({}, this.options, {
          MAT_GROUP: res.data.map((el) => {
            return {
              text: el.name,
              value: el.id
            }
          })
        })
      })
    },
    rulesGet(type) {
      this.$API.base[type]().then((res) => {
        this.rules = formatRules(res.data)
      })
    }
  }
}
</script>

<style></style>
