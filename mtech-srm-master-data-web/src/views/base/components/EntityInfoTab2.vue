<!--
 * @Author: your name
 * @Date: 2022-02-25 15:02:26
 * @LastEditTime: 2022-03-24 17:40:00
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\base\components\EntityInfoTab1.vue
-->
<template>
  <div class="tab2-info full-height mt-px-20">
    <div class="subtitle mt-my-20 mt-px-10">{{ $t('成本中心') }}</div>
    <div class="tab2-info--table">
      <mt-template-page
        ref="tepPage1"
        :hidden-tabs="true"
        :template-config="pageConfig1"
      ></mt-template-page>
    </div>
    <div class="subtitle mt-my-20 mt-px-10">{{ $t('利润中心') }}</div>
    <div class="tab2-info--table">
      <mt-template-page
        ref="tepPage2"
        :hidden-tabs="true"
        :template-config="pageConfig2"
      ></mt-template-page>
    </div>
  </div>
</template>

<script>
import { PAGE_CONFIG1, PAGE_CONFIG2 } from '../config/entityInfoTab2.config'
export default {
  props: {
    entityInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      pageConfig1: PAGE_CONFIG1,
      pageConfig2: PAGE_CONFIG2,

      entityId: this.entityInfo.id || this.$route.query.id
    }
  },
  watch: {
    entityInfo: {
      handler() {
        this.entityId = this.entityInfo.id
        this.init()
      },
      deep: true
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.list1Get(this.entityId)
      this.list2Get(this.entityId)
    },
    list1Get(entityId) {
      this.pageConfig1[0].grid.asyncConfig = Object.assign(
        {},
        {
          url: `/masterDataManagement/tenant/cost-center/getByEntityId?BU_CODE=${localStorage.getItem(
            'currentBu'
          )}`,
          params: {
            entityId
          },
          methods: 'get',
          recordsPosition: 'data'
        }
      )
    },
    list2Get(entityId) {
      this.pageConfig2[0].grid.asyncConfig = Object.assign(
        {},
        {
          url: '/masterDataManagement/tenant/profit-center/getByEntityId',
          params: {
            entityId
          },
          methods: 'get',
          recordsPosition: 'data'
        }
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.tab2-info {
  overflow: auto;
  .subtitle {
    font-size: 16px;
    font-family: PingFangSC;
    font-weight: 600;
    color: rgba(41, 41, 41, 1);
    border-radius: 2px 0 0 2px;
    border-left: 3px solid rgba(0, 70, 156, 1);
  }
  &--table {
    /deep/.mt-data-grid > div[role='grid'] {
      border-left: 1px solid #e6e9ed;
      border-right: 1px solid #e6e9ed;
    }
  }
}
</style>
