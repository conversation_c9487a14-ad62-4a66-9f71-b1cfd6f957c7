<!--
 * @Author: your name
 * @Date: 2021-09-11 15:17:36
 * @LastEditTime: 2022-03-20 14:47:14
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\base\components\ChooseCate.vue
-->
<template>
  <div>
    <mt-dialog ref="dialog" :header="headerTitle" css-class="create-proj-dialog" :buttons="buttons">
      <div class="full-height">
        <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
          <mt-form-item prop="organizationId" :label="$t('组织名称')">
            <mt-DropDownTree
              id="orgDownTree"
              v-model="ruleForm.organizationId"
              :fields="dropDownTree"
              filter-bar-placeholder="Search"
              :allow-filtering="true"
              :placeholder="$t('请选择组织名称')"
              @select="cateTreeDataGet"
            ></mt-DropDownTree>
          </mt-form-item>
          <mt-form-item prop="categoryIds" :label="$t('品类名称')">
            <mt-DropDownTree
              id="cateDownTree"
              v-model="ruleForm.categoryIds"
              :fields="dropDownTreeCate"
              filter-bar-placeholder="Search"
              :allow-filtering="true"
              :placeholder="$t('请选择品类名称')"
            ></mt-DropDownTree>
          </mt-form-item>
          <mt-form-item prop="itemCodes" :label="$t('品项/物料')">
            <mt-input
              v-model="ruleForm.itemCodes"
              :disabled="disabled"
              :show-clear-button="true"
              type="text"
              :placeholder="$t('请输入品项/物料')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="businessGroupName" :label="$t('业务组名称')">
            <mt-input
              v-model="ruleForm.businessGroupName"
              :disabled="true"
              :show-clear-button="true"
              type="text"
              :placeholder="$t('请输入业务组名称')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
        <!-- <mt-template-page
          v-if="visible"
          ref="catePage"
          :hidden-tabs="true"
          :template-config="pageConfig"
        ></mt-template-page> -->
        <!-- <div>
          <mt-checkbox-group v-model="ruleForm.categoryIds" :disabled="false">
            <mt-tree-view
              :fields="fields"
              :node-template="nodeTemplate"
            ></mt-tree-view>
          </mt-checkbox-group>
        </div> -->
      </div>
    </mt-dialog>
  </div>
</template>

<script>
// function TreeDataSetSelect(data, id) {
//   for (let i = 0; i < data.length; i++) {
//     const el = data[i];

//     if (el.id === id) {
//       el.selected = true;
//     }
//     if (el.children) {
//       TreeDataSetSelect(el.children, id);
//     }
//   }
//   return data;
// }
import {
  // CHOOSE_PAGE_CONFIG,
  RULES,
  DROPDOWN_TREE,
  DROPDOWN_TREE_CATE
} from '../config/group.config'
import { formatRules } from '@/utils/util'
import Vue from 'vue'
export default {
  props: {
    groupInfo: {
      required: true,
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      dropDownTree: DROPDOWN_TREE,
      dropDownTreeCate: DROPDOWN_TREE_CATE,
      // pageConfig: CHOOSE_PAGE_CONFIG,
      fields: {
        dataSource: [],
        id: 'id',
        text: 'name',
        child: 'children'
      },
      nodeTemplate: function () {
        return {
          template: Vue.component('cateTree', {
            // template: `
            //   <div>
            //     <div v-if="!data.children?.length">
            //       <mt-checkbox class="group-item" :content="data" :fields="item"></mt-checkbox>
            //     </div>
            //     <div v-else>
            //       {{ data.name }}
            //     </div>
            //   </div>
            // `,
            template: `
              <div class="full-width">
                <mt-checkbox class="group-item" :content="data" :fields="item"></mt-checkbox>
              </div>
            `,
            data() {
              return {
                data: {},
                item: { id: 'id', label: 'name' }
              }
            }
          })
        }
      },
      rules: RULES,

      ruleForm: {
        businessGroupName: '',
        organizationId: [],
        categoryIds: [],
        itemCodes: ''
      },

      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      type: 'add'
    }
  },
  computed: {
    headerTitle() {
      if (this.type === 'add') {
        return this.$t('新增')
      }
      if (this.type === 'edit') {
        return this.$t('编辑')
      }
      return this.$t('预览')
    },
    disabled() {
      if (this.type === 'preview') {
        return true
      }
      return false
    }
  },
  methods: {
    save() {
      const data = {
        businessGroupId: this.groupInfo.id,
        organizationId: this.ruleForm.organizationId.join(''),
        categoryIds: this.ruleForm.categoryIds,
        itemCodes: this.ruleForm.itemCodes
      }
      this.$API.base.cateAssignSave(data).then(() => {
        this.$emit('save')
        this.hide()
      })
    },
    // eslint-disable-next-line no-unused-vars
    async show(type, data) {
      this.$refs.dialog.ejsRef.show()
      this.type = type
      await this.dropDownTreeDataGet()
      this.ruleForm = {
        businessGroupName: this.groupInfo.groupName,
        organizationId: [],
        categoryIds: [],
        itemCodes: ''
      }
      this.fields = Object.assign({}, this.fields, {
        dataSource: []
      })
    },
    hide() {
      this.$refs.dialog.ejsRef.hide()
    },
    async dropDownTreeDataGet() {
      await this.$API.org.getOrgTree({}).then((res) => {
        this.dropDownTree = Object.assign({}, this.dropDownTree, {
          dataSource: res.data
          // dataSource: TreeDataSetSelect(res.data, this.ruleForm.organizationId)
        })
      })
    },
    async cateTreeDataGet(e) {
      const query = {
        businessGroupId: this.groupInfo.id,
        organizationId: e.itemData.id
      }
      this.ruleForm.categoryIds = []
      await this.$API.base.cateWaitTreeGet(query).then((res) => {
        // this.fields = Object.assign({}, this.fields, {
        //   dataSource: res.data,
        // });
        this.dropDownTreeCate = Object.assign({}, this.dropDownTreeCate, {
          dataSource: res.data
        })
      })
    },
    rulesGet(type) {
      this.$API.base[type]().then((res) => {
        this.rules = formatRules(res.data)
      })
    }
  }
}
</script>

<style></style>
