<!--
 * @Author: your name
 * @Date: 2021-09-11 14:11:48
 * @LastEditTime: 2022-03-20 17:00:26
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\base\components\CateAssign.vue
-->
<template>
  <div>
    <mt-dialog
      ref="dialog"
      :header="$t('分配品项')"
      :enable-resize="false"
      :position="{ X: 'right', Y: 'top' }"
      css-class="create-proj-dialog right-wrapper"
      :buttons="buttons"
      @close="closeDialog"
    >
      <div class="full-height">
        <mt-template-page
          ref="matPage"
          v-if="visible"
          :hidden-tabs="true"
          :template-config="pageConfig"
          @handleClickToolBar="handleClickToolBar"
          @handleClickCellTool="handleClickCellTool"
        ></mt-template-page>
      </div>
    </mt-dialog>

    <mat-add ref="matAdd" :group-info="groupInfo" @save="saveInfo"></mat-add>
  </div>
</template>

<script>
import { MAT_PAGE_ASSIGN_PLUGIN } from '../config/group.config'
import MatAdd from './MatAdd.vue'
export default {
  components: {
    MatAdd
  },
  data() {
    return {
      pageConfig: [
        {
          toolbar: {
            useBaseConfig: false,
            tools: [['Add', 'Delete']]
          },
          gridId: 'b0f59c81-9979-4f12-a51d-ba4ad2df41b4',
          grid: {
            dataSource: [],
            columnData: MAT_PAGE_ASSIGN_PLUGIN,
            asyncConfig: {
              url: this.$API.base.getBusinessGroupOrgItemGroupInfoPage,
              params: {},
              recordsPosition: 'data.records'
            }
          }
        }
      ],

      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      visible: false,
      groupInfo: {}
    }
  },
  methods: {
    saveInfo() {
      this.$refs.matPage.refreshCurrentGridData()
    },
    getData() {
      this.$refs.matPage.refreshCurrentGridData()
    },
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const rowSelected = grid?.getSelectedRecords?.()

      if (toolbar.id === 'Add') {
        this.handleAction('add')
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDelete(rowSelected)
      }
    },
    handleClickCellTool(e) {
      const { data, tool } = e

      if (tool.id === 'edit') {
        this.handleAction('edit', data)
      }
      if (tool.id === 'delete') {
        this.handleDelete([data])
      }
    },
    handleDelete(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.base.matDelete(data).then(() => {
            this.$refs.matPage.refreshCurrentGridData()
          })
        }
      })
    },
    handleAction(type, data) {
      this.$refs.matAdd.show(type, data)
    },
    save() {
      this.hide()
    },
    show(row) {
      this.groupInfo = row
      this.$refs.dialog.ejsRef.show()
      this.pageConfig[0].grid.asyncConfig.params.businessGroupId = row.id
      this.visible = true
    },
    hide() {
      this.$refs.dialog.ejsRef.hide()
      this.closeDialog()
    },
    closeDialog() {
      this.visible = false
    }
  }
}
</script>

<style lang="scss">
.e-dlg-container > .right-wrapper {
  height: 100% !important;
  .e-dlg-header-content {
    background-color: rgba(243, 243, 243, 1);
    border-radius: 8px 0 0 0;
    .e-dlg-closeicon-btn {
      .e-btn-icon {
        color: #292929 !important;
      }
    }
    .e-dlg-header {
      color: #292929;
    }
  }
  .e-dlg-content {
    background-color: #f8f8f8;
  }
  .e-footer-content {
    background: rgba(255, 255, 255, 1);
    border-radius: 0 0 0 8px;
    box-shadow: inset 0 1px 0 0 rgba(232, 232, 232, 1);
  }
}
</style>
