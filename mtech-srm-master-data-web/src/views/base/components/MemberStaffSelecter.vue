<template>
  <mt-dialog ref="dialog" :header="$t('新增人员')" :buttons="buttons" :visible="visible">
    <div class="full-height">
      <RemoteAutocomplete
        v-model="value"
        multiple
        :fields="{ text: 'employeeName', value: 'employeeCode' }"
        url="/masterDataManagement/tenant/employee/paged-query"
        :search-fields="[
          'id',
          'employeeCode',
          'employeeDescription',
          'employeeName',
          'email',
          'phoneNum'
        ]"
        :placeholder="$t('请选择')"
      />
    </div>
  </mt-dialog>
</template>

<script>
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: {
    RemoteAutocomplete
  },
  props: {
    employeeIds: {
      type: Array,
      default: () => []
    },
    businessGroupId: {
      type: [Number, String],
      default: null
    }
  },
  data() {
    return {
      value: null,
      visible: false,
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  methods: {
    show() {
      this.value = [...this.employeeIds]
      this.$refs.dialog.ejsRef.show()
      this.visible = true
    },
    hide() {
      this.visible = false
    },
    save() {
      const data = {
        businessGroupId: this.businessGroupId,
        employeeIds: this.value
      }

      this.$API.base.addBusinessGroupAndEmployeeRel(data).then(() => {
        this.hide()
        this.$emit('save')
      })
    }
  }
}
</script>
