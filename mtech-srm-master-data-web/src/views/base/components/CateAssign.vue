<!--
 * @Author: your name
 * @Date: 2021-09-11 14:11:48
 * @LastEditTime: 2022-03-10 11:19:47
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\base\components\CateAssign.vue
-->
<template>
  <div>
    <mt-dialog
      ref="dialog"
      :header="$t('分配品类')"
      :enable-resize="false"
      :position="{ X: 'right', Y: 'top' }"
      css-class="create-proj-dialog right-wrapper"
      :buttons="buttons"
      @close="closeDialog"
    >
      <div class="full-height">
        <mt-template-page
          v-if="visible"
          ref="catePage"
          :hidden-tabs="true"
          :template-config="pageConfig"
          @handleClickToolBar="handleClickToolBar"
          @handleClickCellTool="handleClickCellTool"
        ></mt-template-page>
      </div>
    </mt-dialog>

    <choose-cate ref="chooseCate" :group-info="groupInfo" @save="saveInfo"></choose-cate>
  </div>
</template>

<script>
import { PAGE_ASSIGN_PLUGIN } from '../config/group.config'
import ChooseCate from './ChooseCate.vue'
export default {
  components: {
    ChooseCate
  },
  data() {
    return {
      pageConfig: [
        {
          toolbar: {
            useBaseConfig: false,
            tools: [['Add', 'Delete']]
          },
          gridId: '0f2f927c-8ab4-41a2-b7cf-9137eccfe58a',
          grid: {
            dataSource: [],
            columnData: PAGE_ASSIGN_PLUGIN,
            asyncConfig: {
              url: this.$API.base.getBusinessGroupOrgCategoryInfoPage,
              params: {},
              recordsPosition: 'data.records'
            }
          }
        }
      ],

      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      visible: false,
      groupInfo: {}
    }
  },
  methods: {
    saveInfo() {
      this.$refs.catePage.refreshCurrentGridData()
    },
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const rowSelected = grid?.getSelectedRecords?.()

      if (toolbar.id === 'Add') {
        this.handleAction('add')
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDelete(rowSelected)
      }
    },
    handleClickCellTool(e) {
      const { data, tool } = e

      if (tool.id === 'edit') {
        this.handleAction('edit', data)
      }
      if (tool.id === 'delete') {
        this.handleDelete([data])
      }
    },
    handleDelete(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.base.cateDelete(data).then(() => {
            this.$refs.catePage.refreshCurrentGridData()
          })
        }
      })
    },
    handleAction(type, data) {
      this.$refs.chooseCate.show(type, data)
    },
    save() {
      this.hide()
    },
    show(row) {
      this.groupInfo = row
      this.$refs.dialog.ejsRef.show()
      this.pageConfig[0].grid.asyncConfig.params.businessGroupId = row.id
      this.visible = true
    },
    hide() {
      this.$refs.dialog.ejsRef.hide()
      this.closeDialog()
    },
    closeDialog() {
      this.visible = false
    },
    getData() {
      this.$refs.catePage.refreshCurrentGridData()
    }
  }
}
</script>

<style lang="scss">
.e-dlg-container > .right-wrapper {
  height: 100% !important;
  .e-dlg-header-content {
    background-color: rgba(243, 243, 243, 1);
    border-radius: 8px 0 0 0;
    .e-dlg-closeicon-btn {
      .e-btn-icon {
        color: #292929 !important;
      }
    }
    .e-dlg-header {
      color: #292929;
    }
  }
  .e-dlg-content {
    background-color: #f8f8f8;
  }
  .e-footer-content {
    background: rgba(255, 255, 255, 1);
    border-radius: 0 0 0 8px;
    box-shadow: inset 0 1px 0 0 rgba(232, 232, 232, 1);
  }
}
</style>
