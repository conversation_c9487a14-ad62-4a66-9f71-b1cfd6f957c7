<!--
 * @Author: your name
 * @Date: 2021-08-18 14:44:45
 * @LastEditTime: 2022-06-10 16:18:31
 * @LastEditors: zhaoriyang3 <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\base\Staff.vue
-->
<template>
  <div class="full-height mt-pt-20">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
    <mt-dialog ref="dialog" :header="headerTitle" css-class="create-proj-dialog" :buttons="buttons">
      <mt-form ref="staffForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="employeeName" :label="$t('员工姓名')">
          <mt-input
            v-model="ruleForm.employeeName"
            :disabled="disabled"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入员工姓名')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="companyOrganizationId" :label="$t('归属公司')">
          <mt-DropDownTree
            id="dropDownTreeCom"
            v-model="ruleForm.companyOrganizationId"
            :enabled="!disabled"
            :fields="dropDownTree"
            filter-bar-placeholder="Search"
            :allow-filtering="true"
            :placeholder="$t('请选择归属公司')"
            :show-clear-button="false"
            @select="downTreeSelect"
          ></mt-DropDownTree>
        </mt-form-item>
        <mt-form-item prop="departmentOrganizationId" :label="$t('部门')">
          <mt-select
            v-model="ruleForm.departmentOrganizationId"
            :disabled="disabled"
            :data-source="options.DEPART"
            :fields="fields.DEPART"
            :show-clear-button="true"
            :placeholder="$t('请选择部门')"
            @change="stationGet"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="stationOrgId" :label="$t('岗位')">
          <mt-select
            v-model="ruleForm.stationOrgId"
            :disabled="disabled"
            :data-source="options.STATION"
            :fields="fields.STATION"
            :show-clear-button="true"
            :placeholder="$t('请选择岗位')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="phoneNum" :label="$t('手机号')">
          <mt-input
            v-model="ruleForm.phoneNum"
            :disabled="disabled"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入员工手机号，可用于系统登录')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="email" :label="$t('邮箱')">
          <mt-input
            v-model="ruleForm.email"
            :disabled="disabled"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入邮箱')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="accountName" :label="$t('账号（用于系统登录）')">
          <mt-input
            v-model="ruleForm.accountName"
            :readonly="type != 'add'"
            :disabled="disabled"
            :show-clear-button="type == 'add'"
            type="text"
            :placeholder="$t('请输入员工登录账号')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item v-if="type === 'add'" prop="initPassWord" :label="$t('初始密码')">
          <mt-input
            v-model="ruleForm.initPassWord"
            :readonly="disabled"
            :show-clear-button="true"
            type="text"
            ::placeholder="$t('请输入员工账号初始密码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item v-if="type === 'add'" prop="confirmPassWord" :label="$t('确认初始密码')">
          <mt-input
            v-model="ruleForm.confirmPassWord"
            :readonly="disabled"
            :show-clear-button="true"
            type="text"
            ::placeholder="$t('请输入员工账号初始密码')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </mt-dialog>

    <dialog-uploader
      ref="uploader"
      :template-file-config="templateFileConfig"
      :async-settings="asyncSettings"
      @success="success"
    ></dialog-uploader>
  </div>
</template>

<script>
import { PAGE_CONFIG, RULES, OPTIONS, FIELDS, DROPDOWN_TREE } from './config/staff.config'
import { formatRules } from '@/utils/util'
import DialogUploader from '@/components/common/Uploader.vue'
export default {
  components: {
    DialogUploader
  },
  data() {
    return {
      options: OPTIONS,
      fields: FIELDS,
      pageConfig: PAGE_CONFIG,
      dropDownTree: DROPDOWN_TREE,
      rules: RULES,
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {},
      pageInfo: {
        current: 1,
        size: 10
      },
      type: 'add', // add edit preview
      currentRow: {},
      flag: false,

      templateFileConfig: {
        templateId: this.$t('员工主数据导入模板')
      },
      asyncSettings: {
        saveUrl: `/api/masterDataManagement/tenant/employee/import-data`
      }
    }
  },
  computed: {
    headerTitle() {
      if (this.type === 'add') {
        return this.$t('新增')
      }
      if (this.type === 'edit') {
        return this.$t('编辑')
      }
      return this.$t('预览')
    },
    disabled() {
      if (this.type === 'preview') {
        return true
      }
      return false
    }
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const rowSelected = grid.getSelectedRecords()
      if (toolbar.id === 'Add') {
        this.handleAction('add')
        this.companyGet()
        this.rulesGet('staffAddSaveValid')
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDelete(rowSelected)
      }
      if (toolbar.id === 'import') {
        this.$refs.uploader.open()
      }
      if (toolbar.id === 'export') {
        this.$refs.uploader.open()
      }
    },
    handleClickCellTool(e) {
      const { data, tool } = e
      if (tool.id === 'edit') {
        this.handleAction('edit', data)
        this.companyGet()
        this.rulesGet('staffEditSaveValid')
      } else if (tool.id === 'delete') {
        this.handleDelete([data])
      } else if (tool.id === 'preview') {
        this.companyGet()
        this.handleAction('preview', data)
      } else if (tool.id === 'Active') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('激活员工后，该员工可登录系统，您是否确认进行激活？')
          },
          success: () => {
            this.handleStatus(data)
          }
        })
      } else if (tool.id === 'StopUse') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('停用员工后，该员工将无法登录系统，您是否确认进行停用？')
          },
          success: () => {
            this.handleStatus(data)
          }
        })
      }
    },
    handleStatus(data) {
      this.$API.base
        .staffModifyEmployeeStatus({
          employeeId: data.employeeId,
          statusId: data.statusId.value == 1 ? 0 : 1
        })
        .then(() => {
          this.$refs.tepPage.refreshCurrentGridData()
          this.cancel()
        })
    },
    handleAction(type, data) {
      this.type = type
      this.currentRow = Object.assign({}, data)
      // delete data?.departmentOrganizationId;
      // delete data?.stationOrgId;
      this.$refs.staffForm.resetFields()
      this.ruleForm = Object.assign({}, { companyOrganizationId: [] }, data)
      this.$refs.dialog.ejsRef.show()
    },
    handleDelete(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.base.staffDelete(data).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    },
    downTreeSelect(e) {
      this.departGet({ value: e.itemData.id })
    },
    save() {
      if (this.type === 'preview') {
        this.cancel()
        return false
      }

      if (this.type === 'add') {
        if (this.ruleForm.initPassWord !== this.ruleForm.confirmPassWord) {
          this.$toast({
            content: this.$t('两次密码输入不一致'),
            type: 'error'
          })

          return false
        }
      }

      this.$refs.staffForm.validate((valid) => {
        if (valid) {
          const data = Object.assign({}, this.ruleForm)

          if (!data.existAccount) {
            // delete data.accountName;
            // delete data.existAccount;
          }
          data.companyOrganizationId = data.companyOrganizationId?.join(',')
          if (this.type === 'add') {
            data.orgId = this.ruleForm.stationOrgId
            // delete data.stationOrgId;
            this.$API.base.staffAddSave(data).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
          if (this.type === 'edit') {
            data.stationOrganizationId = this.ruleForm.stationOrgId
            // delete data.stationOrgId;
            this.$API.base.staffEditSave(data).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
        }
      })
    },
    cancel() {
      this.flag = false
      this.$refs.dialog.ejsRef.hide()
    },
    success() {
      this.$refs.uploader.save()
      this.$refs.tepPage.refreshCurrentGridData()
    },
    accountGet() {
      if (this.ruleForm.employeeName) {
        const data = {
          name: this.ruleForm.employeeName,
          tenantId: 0
        }
        this.$API.base.accountGet(data).then((res) => {
          this.$set(this.ruleForm, 'accountName', res.data)
        })
      }
    },
    companyGet() {
      const data = { orgType: 'ORG001ADM' }
      this.$API.org.getAdmTree(data).then((res) => {
        this.dropDownTree = Object.assign({}, this.dropDownTree, {
          dataSource: res.data
        })
        this.flag = true
        this.$nextTick(() => {
          if (this.type === 'add' && res.data?.length == 1 && res.data[0].children?.length <= 0) {
            this.ruleForm.companyOrganizationId = [res.data[0]?.id]
          }

          if (this.currentRow.companyOrganizationId) {
            this.ruleForm.companyOrganizationId = [this.currentRow.companyOrganizationId]
          }
          this.departGet({ value: this.currentRow?.companyOrganizationId })
        })
      })
    },
    departGet(e) {
      if (e.value) {
        const data = {
          organizationId: e.value,
          onlyCurrentLevel: 1
        }
        this.$set(this.options, 'DEPART', [])
        this.$API.base.departGet(data).then((res) => {
          this.$set(this.options, 'DEPART', res.data)
          if (this.currentRow.departmentOrganizationId) {
            this.ruleForm.departmentOrganizationId = this.currentRow.departmentOrganizationId
          }
        })
      }
    },
    stationGet(e) {
      if (e.value) {
        const data = {
          organizationId: e.value,
          onlyCurrentLevel: 1
        }
        this.$set(this.options, 'STATION', [])
        this.$API.base.stationGet(data).then((res) => {
          this.$set(this.options, 'STATION', res.data)
          if (this.currentRow.stationOrgId) {
            this.ruleForm.stationOrgId = this.currentRow.stationOrgId
          }
        })
      }
    },
    rulesGet(type) {
      this.$API.base[type]().then((res) => {
        const rules = formatRules(res.data)
        this.rules = { ...rules }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-subtitle {
  border-left: 3px solid #eda133;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  padding-left: 10px;
}
/deep/.btn-text {
  color: #4f5b6d;
}
/deep/ .mt-data-grid {
  .status-label {
    font-size: 12px;
    padding: 4px;
    border-radius: 2px;
    &.status-0 {
      color: #9baac1;
      background: rgba(155, 170, 193, 0.1);
    }
    &.status-1 {
      color: #6386c1;
      background: rgba(99, 134, 193, 0.1);
    }
  }
}
/deep/.mt-dialog {
  display: none;
}
</style>
