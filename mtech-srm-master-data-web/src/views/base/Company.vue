<!--
 * @Author: your name
 * @Date: 2021-08-18 14:44:45
 * @LastEditTime: 2022-03-07 15:30:31
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\base\Company.vue
-->
<template>
  <div class="full-height mt-pt-20">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
    <mt-dialog ref="dialog" :header="headerTitle" css-class="create-proj-dialog" :buttons="buttons">
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <div class="full-width dialog-subtitle mt-mb-20">
          {{ $t('基本信息') }}
        </div>
        <mt-form-item prop="entityName" :label="$t('公司名称')">
          <mt-input
            v-model="ruleForm.entityName"
            :disabled="disabled"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入公司名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="entityCode" :label="$t('公司编码')" v-if="type === 'preview'">
          <mt-input
            v-model="ruleForm.entityCode"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入公司编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="entityUnifiedCode" :label="$t('社会统一信用码')">
          <mt-input
            v-model="ruleForm.entityUnifiedCode"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入社会统一信用码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="corporation" :label="$t('公司法人')">
          <mt-input
            v-model="ruleForm.corporation"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入公司法人')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="phoneNumber" :label="$t('联系电话')">
          <mt-input
            v-model="ruleForm.phoneNumber"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入联系电话')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="email" :label="$t('邮箱')">
          <mt-input
            v-model="ruleForm.email"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入邮箱')"
          ></mt-input>
        </mt-form-item>
        <!-- <mt-form-item prop="center" :label="$t('所属成本中心')">
          <mt-select
            v-model="ruleForm.center"
            :disabled="disabled"
            :data-source="[]"
            :show-clear-button="true"
            :placeholder="$t('请选择所属成本中心')"
          ></mt-select>
        </mt-form-item> -->
        <mt-form-item prop="countryId" :label="$t('国家/地区')">
          <mt-input
            v-if="type === 'preview'"
            v-model="ruleForm.countryName"
            :disabled="disabled"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请选择国家/地区')"
          ></mt-input>
          <mt-select
            v-else
            v-model="ruleForm.countryId"
            :disabled="disabled"
            :fields="fields.COUNTRY"
            :data-source="options.COUNTRY"
            :show-clear-button="true"
            :allow-filtering="true"
            :placeholder="$t('请选择国家/地区')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="bankName" :label="$t('开户银行')">
          <mt-input
            v-model="ruleForm.bankName"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入开户银行')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="bankAccount" :label="$t('开户行账号')">
          <mt-input
            v-model="ruleForm.bankAccount"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入开户行账号')"
          ></mt-input>
        </mt-form-item>
        <!-- <div class="full-width flex justify-between">
          <div class="dialog-subtitle mt-mt-10 mt-mb-20">{{ $t("工厂地址管理") }}</div>
          <mt-button css-class="e-flat btn-text mt-mt-10" icon-position="Right"
            ><mt-icon
              name="icon_solid_Createproject"
              class="mt-mr-10"
            />{{ $t("添加") }}</mt-button
          >
        </div> -->

        <!-- <div class="full-width dialog-subtitle mt-mb-20 mt-mt-10">
          公司系统管理员信息
        </div>
        <mt-form-item prop="username" :label="$t('姓名')">
          <mt-input
            v-model="ruleForm.username"
            :disabled="disabled"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入姓名')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="phone" :label="$t('联系电话')">
          <mt-input
            v-model="ruleForm.phone"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入联系电话')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="account" :label="$t('管理员账号')">
          <mt-input
            v-model="ruleForm.account"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入管理员账号')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="password" :label="$t('账号密码')">
          <mt-input
            v-model="ruleForm.password"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入账号密码')"
          ></mt-input>
        </mt-form-item> -->
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import { PAGE_CONFIG, RULES, OPTIONS, FIELDS } from './config/company.config'
import { formatRules } from '@/utils/util'
export default {
  data() {
    return {
      options: OPTIONS,
      pageConfig: PAGE_CONFIG,
      rules: RULES,
      fields: FIELDS,

      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {},
      type: 'add' // add edit preview
    }
  },
  computed: {
    headerTitle() {
      if (this.type === 'add') {
        return this.$t('新增')
      }
      if (this.type === 'edit') {
        return this.$t('编辑')
      }
      return this.$t('预览')
    },
    disabled() {
      if (this.type === 'preview') {
        return true
      }
      return false
    }
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const rowSelected = grid.getSelectedRecords()
      if (toolbar.id === 'Add') {
        this.countryGet()
        this.handleAction('add')
        this.rulesGet('companyAddValid')
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDelete(rowSelected)
      }
    },
    handleClickCellTool(e) {
      const { data, tool } = e
      if (tool.id === 'edit') {
        this.countryGet()
        this.handleAction('edit', data)
        this.rulesGet('companyEditValid')
      }
      if (tool.id === 'delete') {
        this.handleDelete([data])
      }
      if (tool.id === 'preview') {
        this.countryGet()
        this.handleAction('preview', data)
      }
    },
    handleAction(type, data) {
      this.type = type
      this.$refs.ruleForm.resetFields()
      this.ruleForm = Object.assign({}, data)
      this.$refs.dialog.ejsRef.show()
    },
    handleDelete(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.base.companyDelete(data).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    },
    save() {
      if (this.type === 'preview') {
        this.cancel()
        return false
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.type === 'add') {
            const data = {
              ...this.ruleForm,
              entityType: 0,
              orgTypeCode: 'ORG001COM'
            }
            this.$API.base.companyAdd(data).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
          if (this.type === 'edit') {
            delete this.ruleForm.tabIndex
            this.ruleForm.statusId = this.ruleForm.statusId.value
            this.$API.base.companyEdit(this.ruleForm).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
        }
      })
    },
    cancel() {
      this.$refs.dialog.ejsRef.hide()
    },
    async countryGet() {
      await this.$API.dict.countryGet().then((res) => {
        this.options = Object.assign({}, this.options, { COUNTRY: res.data })
      })
    },
    rulesGet(type) {
      this.$API.base[type]().then((res) => {
        this.rules = formatRules(res.data)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-subtitle {
  border-left: 3px solid #eda133;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  padding-left: 10px;
}
/deep/.btn-text {
  color: #4f5b6d;
}
/deep/ .mt-data-grid {
  .status-label {
    font-size: 12px;
    padding: 4px;
    border-radius: 2px;
    &.status-0 {
      color: #9baac1;
      background: rgba(155, 170, 193, 0.1);
    }
    &.status-1 {
      color: #6386c1;
      background: rgba(99, 134, 193, 0.1);
    }
  }
}
/deep/.mt-dialog {
  display: none;
}
</style>
