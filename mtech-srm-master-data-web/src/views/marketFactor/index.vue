<!--
 * @Author: your name
 * @Date: 2021-09-29 09:53:32
 * @LastEditTime: 2022-01-11 09:56:02
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\material\MaterialSite.vue
-->
<template>
  <div class="full-height">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>

    <!-- 导入弹框 -->
    <uploadExcelDialog
      ref="uploadExcelRefs"
      :is-show-tips="isShowTips"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    />
  </div>
</template>

<script>
import { download, getHeadersFileNameExcel } from '@/utils/file.js'
import mixin from './config/mixin'

export default {
  components: {
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog').default
    // lineUploadDialog: require('./components/lineUpload').default,
  },
  mixins: [mixin],
  data() {
    return {
      type: 'list',
      pageConfig: [],
      downTemplateName: this.$t('上传'),
      downTemplateParams: {}, // 下载模板参数
      uploadParams: {},
      requestUrls: {},
      isShowTips: true
    }
  },
  computed: {},
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      const _selectRows = e.grid.getSelectedRecords()
      if (
        _selectRows.length <= 0 &&
        (e.toolbar.id == 'Edit' ||
          e.toolbar.id == 'delete' ||
          e.toolbar.id == 'activate' ||
          e.toolbar.id == 'failure')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //新增
      if (e.toolbar.id === 'Add') {
        if (e.tabIndex == 0) {
          this.handleClickAdd()
        } else {
          this.handleAddAttr()
        }
      }
      //编辑
      else if (e.toolbar.id === 'Edit') {
        if (e.tabIndex == 0) {
          this.handleClickEdit(_selectRows)
        } else {
          this.handleAttrEdit(_selectRows)
        }
      }
      // 删除
      else if (e.toolbar.id === 'delete') {
        this.handleClickdelete(_selectRows)
      }
      //启用
      else if (e.toolbar.id === 'activate') {
        this.handleClickactivate(_selectRows)
      }
      //停用
      else if (e.toolbar.id === 'failure') {
        this.handleClickfailure(_selectRows)
      }
      //导入
      else if (e.toolbar.id === 'upload') {
        this.handleClickUpload(e.tabIndex)
      }
      //导出
      else if (e.toolbar.id === 'Download') {
        this.handleClickDownload(e.tabIndex)
      }
    },
    //新增属性大类
    handleAddAttr() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/settings/market/factor/components/attrDialog" */ './components/attrDialog.vue'
          ),
        data: {
          title: this.$t('新增属性大类'),
          headStates: 'Add'
        },
        success: () => {
          this.$refs.tepPage.refreshCurrentGridData()
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        }
      })
    },
    //新增
    handleClickAdd() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "material/components/AddDialog.vue" */ './components/AddDialog.vue'
          ),
        data: {
          title: this.$t('新增'),
          headStates: 'Add'
        },
        success: () => {
          this.$refs.tepPage.refreshCurrentGridData()
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        }
      })
    },
    //编辑
    handleClickEdit(_selectRows) {
      if (_selectRows.length > 1) {
        this.$toast({ content: this.$t('只能编辑一行'), type: 'warning' })
        return
      }
      if (_selectRows[0].statusId !== '3') {
        this.$toast({ content: this.$t('只能编辑状态为【失效】的记录！'), type: 'warning' })
        return
      }
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "material/components/AddDialog.vue" */ './components/AddDialog.vue'
          ),
        data: {
          title: this.$t('编辑'),
          data: _selectRows[0],
          headStates: 'edit'
        },
        success: () => {
          this.$refs.tepPage.refreshCurrentGridData()
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        }
      })
    },
    handleAttrEdit(_selectRows) {
      if (_selectRows.length > 1) {
        this.$toast({ content: this.$t('只能编辑一行'), type: 'warning' })
        return
      }
      console.log(_selectRows)
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/settings/market/factor/components/attrDialog" */ './components/attrDialog.vue'
          ),
        data: {
          title: this.$t('编辑'),
          data: _selectRows[0],
          headStates: 'edit'
        },
        success: () => {
          this.$refs.tepPage.refreshCurrentGridData()
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        }
      })
    },
    //删除
    handleClickdelete(_selectRows) {
      const item = _selectRows.find((row) => row.statusId !== '3')
      if (item) {
        this.$toast({ content: this.$t('只能删除状态为【失效】的单据！'), type: 'warning' })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const _selectIds = []
          _selectRows.map((item) => {
            _selectIds.push(item.id)
          })
          let parameter = {
            ids: _selectIds
          }
          this.$API.marketFactor.batchDelete(parameter).then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    },
    //启用
    handleClickactivate(_selectRows) {
      for (let i = 0; i < _selectRows.length; i++) {
        if (_selectRows[i].statusId !== '3') {
          this.$toast({ content: this.$t('只能启用状态为【失效】的单据！'), type: 'warning' })
          return
        }
      }

      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认启用选中的数据？')
        },
        success: () => {
          const _selectIds = []
          _selectRows.map((item) => {
            _selectIds.push(item.id)
          })
          let parameter = {
            ids: _selectIds
          }
          this.$API.marketFactor.batchValid(parameter).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
          })
        }
      })
    },
    //停用
    handleClickfailure(_selectRows) {
      for (let i = 0; i < _selectRows.length; i++) {
        if (_selectRows[i].statusId !== '1') {
          this.$toast({ content: this.$t('只能停用状态为【激活】的单据！'), type: 'warning' })
          return
        }
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认停用选中的数据？')
        },
        success: () => {
          const _selectIds = []
          _selectRows.map((item) => {
            _selectIds.push(item.id)
          })
          let parameter = {
            ids: _selectIds,
            statusId: 3 //状态1激活,3失效
          }
          this.$API.marketFactor.batchInvalid(parameter).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
          })
        }
      })
    },
    //导入
    handleClickUpload(curTabIndex) {
      this.isShowTips = true
      curTabIndex === 0
        ? (this.requestUrls = {
            templateUrlPre: 'marketFactor',
            templateUrl: 'downloadCostFactorItemTemplate',
            uploadUrl: 'importCostFactor'
          })
        : (this.requestUrls = {
            templateUrlPre: 'marketFactor',
            templateUrl: 'downloadPropertyTemplate',
            uploadUrl: 'importProperty'
          })
      this.showUploadExcel(true)
    },
    //导出
    handleClickDownload(curTabIndex) {
      const currentUsefulRef = this.$refs.tepPage.getCurrentUsefulRef().pluginRef
      const params = currentUsefulRef?.asyncParams
      const funcName = curTabIndex === 0 ? 'exportData' : 'exportProperty'
      this.$API.marketFactor[funcName](params).then((res) => {
        const fileName = getHeadersFileNameExcel(res)
        download({ fileName: `${fileName}`, blob: res.data })
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
      })
    },

    //行内上传-下载
    handleClickCellTitle(e) {
      if (e.field == 'fileId') {
        this.handleClickCellTitleFileId(e.data)
      } else if (e.field === 'applyRange') {
        this.$dialog({
          modal: () => import('./components/applyRange/index.vue'),
          data: {
            title: this.$t('适用范围'),
            data: e.data
          }
        })
      } else if (e.field == 'companyAuth') {
        const editable = e.data.statusId === '3'
        const comp = editable
          ? import('@/components/market/companyAuth/index.vue')
          : import('@/components/market/companyAuthView/index.vue')
        // 公司授权的弹框
        this.$dialog({
          modal: () => comp,
          data: {
            data: e.data,
            queryId: 'costModelId',
            getAuthList: this.$API.marketFactor.getAuthCompanyList
          },
          success: async (data) => {
            if (editable) {
              const params = {
                ...e.data,
                companyRelSaveRequestList: data.companyRelList
              }
              const res = await this.$API.marketFactor.update(params)
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功！'), type: 'success' })
              }
            }
          }
        })
      }
    },
    handleClickCellTitleFileId(data) {
      if (data.fileId == '0') {
        this.isShowTips = false
        this.requestUrls = {
          templateUrlPre: 'marketFactor',
          uploadUrl: 'uploadFile'
        }
        this.uploadParams = {
          id: data.id
        }
        this.showUploadExcel(true)
      } else {
        let parameter = {
          id: data.id,
          fileId: data.fileId
        }
        this.$API.marketFactor.downloadFile(parameter).then((res) => {
          const fileName = getHeadersFileNameExcel(res)
          download({ fileName: `${fileName}`, blob: res.data })
          this.$toast({
            content: this.$t('正在下载'),
            type: 'success'
          })
        })
      }
    },
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRefs.uploadData = null // 清空数据
        this.$refs.uploadExcelRefs.fileLength = 0
        this.$refs.uploadExcelRefs.$refs.uploader.files = []
        this.$refs.uploadExcelRefs.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRefs.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 上传成功后，获取到的数据
    upExcelConfirm() {
      this.$refs.uploadExcelRefs.$refs.dialog.$refs.ejsRef.hide()
      this.$toast({ content: this.$t('导入成功'), type: 'success' })
      this.updateList()
    },
    //更新数据
    updateList() {
      this.$refs[`tepPage`].refreshCurrentGridData()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/mixin.scss';

::v-deep {
  .dialog-subtitle {
    border-left: 3px solid #00469c;
    border-radius: 2px;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: #2f353c;
    padding-left: 10px;
  }
  .uploads {
    margin: 0 5px;
    color: rgba(99, 134, 193, 1);
  }
  .uploads:hover {
    cursor: pointer;
  }

  .e-rowcell.sticky-col-0,
  .e-headercell.sticky-col-0 {
    @include sticky-col;
    left: 0px;
  }

  .e-rowcell.sticky-col-1,
  .e-headercell.sticky-col-1 {
    @include sticky-col;
    left: 50px;
  }

  .e-rowcell.sticky-col-2,
  .e-headercell.sticky-col-2 {
    @include sticky-col;
    left: 200px;
  }
}
</style>
