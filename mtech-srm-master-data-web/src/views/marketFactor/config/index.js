import { i18n } from '@/main.js'
import { formatDate } from '@/utils/util'
/*
 * @Author: your name
 * @Date: 2021-09-29 09:58:01
 * @LastEditTime: 2021-12-30 13:54:42
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\material\config\materialSite.config.js
 */
// import Vue from "vue";
// import { i18n } from "@/main.js";
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'delete', icon: 'icon_solid_Delete', title: i18n.t('删除') },
  { id: 'Edit', icon: 'icon_solid_edit', title: i18n.t('编辑') },
  { id: 'activate', icon: 'icon_table_enable', title: i18n.t('启用') },
  { id: 'failure', icon: 'icon_table_disable', title: i18n.t('停用') },
  { id: 'upload', icon: 'icon_solid_upload', title: i18n.t('导入') },
  { id: 'Download', icon: 'icon_solid_Download', title: i18n.t('导出') }
]
const toolbarTwo = [
  { id: 'activate', icon: 'icon_table_enable', title: i18n.t('启用') },
  { id: 'failure', icon: 'icon_table_disable', title: i18n.t('停用') }
]
const materialQualityCols = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    width: '200',
    field: 'itemCode',
    headerText: i18n.t('材质编码')
  },
  {
    width: '200',
    field: 'itemName',
    headerText: i18n.t('材质描述')
  },
  {
    width: '200',
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    width: '200',
    field: 'categoryName',
    headerText: i18n.t('品类描述')
  },
  {
    width: '200',
    field: 'baseMeasureUnitName',
    headerText: i18n.t('基本单位')
  },

  {
    field: 'costFactorSpecification',
    // width: "200",
    headerText: i18n.t('规格描述')
  },
  {
    width: '200',
    field: 'materialQuality',
    headerText: i18n.t('材质')
  },
  {
    field: 'companyName',
    // width: "200",
    headerText: i18n.t('公司')
  },
  {
    field: 'statusDescription',
    // width: "200",
    headerText: i18n.t('是否生效')
  },

  {
    field: 'createUserName',
    // width: "200",
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    // width: "200",
    headerText: i18n.t('创建时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return formatDate(new Date(parseInt(e)), 'yyyy-MM-dd')
      }
    }
  }
]

const columnData = (that) => [
  {
    width: '50',
    type: 'checkbox',
    allowResizing: false,
    customAttributes: {
      class: 'sticky-col-0'
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('成本因子编码'),
    allowResizing: false,
    width: 150,
    customAttributes: {
      class: 'sticky-col-1'
    }
  },
  {
    field: 'statusDescription',
    headerText: i18n.t('是否生效'),
    allowResizing: false,
    width: 100,
    customAttributes: {
      class: 'sticky-col-2'
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('成本因子名称')
    // width: "350",
    // cellTools: ["edit", "delete", "preview"],
  },
  {
    field: 'costFactorSpecification',
    // width: "200",
    headerText: i18n.t('规格')
  },
  {
    field: 'relationCode',
    width: '0',
    headerText: i18n.t('公司编码')
  },
  {
    field: 'relationName',
    width: '0',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'companyAuth', //companyRelList
    headerText: i18n.t('适用公司'),
    cssClass: 'field-content',
    ignore: true,
    valueConverter: {
      type: 'placeholder',
      placeholder: i18n.t('适用公司')
    }
  },
  {
    field: 'applyRange',
    headerText: i18n.t('适用范围'),
    cssClass: 'field-content',
    ignore: true,
    valueConverter: {
      type: 'placeholder',
      placeholder: i18n.t('适用范围')
    }
  },
  {
    field: 'costFactorProperty',
    headerText: i18n.t('属性大类'),
    width: 120,
    formatter: function ({ field }, item) {
      const selectItem = that.attributeSelect?.find((t) => t.itemName === item[field])
      return selectItem ? selectItem.itemName : item[field]
    },
    searchOptions: {
      elementType: 'select',
      dataSource: that.attributeSelect,
      fields: { text: 'itemName', value: 'itemName' }
    }
  },
  {
    field: 'costFactorGroup',
    headerText: i18n.t('属性中类'),
    width: 120,
    formatter: function ({ field }, item) {
      const selectItem = that.costFactorGroupSelect?.find((t) => t.itemName === item[field])
      return selectItem ? selectItem.itemName : item[field]
    },
    searchOptions: {
      elementType: 'select',
      dataSource: that.costFactorGroupSelect,
      fields: { text: 'itemName', value: 'itemName' }
    }
  },
  {
    field: 'baseMeasureUnitName',
    headerText: i18n.t('单位'),
    width: 140,
    formatter: function (column, item) {
      return item.baseMeasureUnitCode + '-' + item.baseMeasureUnitName
    }
  },
  {
    field: 'maxQuotePercent',
    headerText: i18n.t('报价上限（%）'),
    width: 150,
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return e?.toFixed(2)
      }
    }
  },
  {
    field: 'minQuotePercent',
    headerText: i18n.t('报价下限（%）'),
    width: 150,
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return e?.toFixed(2)
      }
    }
  },
  {
    field: 'costFactorRemark',
    headerText: i18n.t('备注')
  },
  {
    field: 'fileId',
    width: 120,
    headerText: i18n.t('附件'),
    cssClass: 'field-content',
    ignore: true,
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e == '0') {
          return i18n.t('点击上传')
        } else {
          return i18n.t('点击下载')
        }
      }
    }
    // template: function () {
    //   return {
    //     template: Vue.component("actionOption", {
    //       template:
    //         '<div v-if="send" class="sendDiv"> <span class="uploads" v-for="(item,index) in send" @click="channelButtonClick(item)" :key="index">{{item.name}}</span></div>',
    //       data() {
    //         return {
    //           data: {},
    //           send: [
    //             { name: i18n.t("上传"), code: "upload" },
    //             { name: i18n.t("下载"), code: "download" },
    //           ],
    //         };
    //       },
    //       mounted() {
    //         // this.processtheData()
    //       },
    //       methods: {
    //         processtheData() {
    //           // console.log(this.data.channel, '渠道')
    //           if (this.data.channel && this.data.channel.length > 0) {
    //             // console.log(this.data.channel, this.send)
    //             this.data.channel.map((item) => {
    //               // console.log(item)
    //               this.send.map((itemTow) => {
    //                 // console.log(itemTow.code)
    //                 if (item === itemTow.code) {
    //                   itemTow.state = true;
    //                 }
    //               });
    //             });
    //           }
    //         },
    //         channelButtonClick(item) {
    //           const id = this.data.id;
    //           const fileId = this.data.fileId;
    //           const parameter = {
    //             id: id,
    //             code: item.code,
    //             fileId: fileId,
    //           };
    //           this.$parent.$emit("uploadAndDownload", parameter);
    //         },
    //       },
    //     }),
    //   };
    // },
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    format: 'yyyy-MM-dd',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return formatDate(new Date(parseInt(e)), 'yyyy-MM-dd')
      }
    },
    searchOptions: {
      elementType: 'date-range',
      operator: 'between',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return e.map((x, i) => {
          if (i === 1) {
            return Number(new Date(x.toString().replace('00:00:00', '23:59:59')))
          }
          return Number(new Date(x.toString()))
        })
      }
    }
  },
  {
    field: 'createUserName',
    // width: "200",
    headerText: i18n.t('创建人')
  }
]

const attributeTypeCols = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    width: '200',
    field: 'attributeCode',
    headerText: i18n.t('属性编码')
  },
  {
    width: '200',
    field: 'attributeName',
    headerText: i18n.t('属性名称')
  },
  {
    width: '200',
    field: 'attributeTypeCode',
    headerText: i18n.t('类型'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('实际定额'), 1: i18n.t('条件基价') }
    }
  },
  {
    width: '200',
    field: 'parentAttributeName',
    headerText: i18n.t('父类')
  },
  {
    field: 'statusDescription',
    // width: "200",
    headerText: i18n.t('是否生效')
  }
]

export const moldColumnData = [
  // {
  //   width: '50',
  //   type: 'checkbox'
  // },
  {
    field: 'itemCode',
    headerText: i18n.t('模具编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('模具描述')
  },
  {
    field: 'mouldType',
    headerText: i18n.t('模具类型'),
    valueConverter: {
      type: 'map',
      map: {
        0: '',
        1: i18n.t('基础模具'),
        2: i18n.t('复制模具'),
        3: i18n.t('基础模改模'),
        4: i18n.t('复制模改模')
      }
    }
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类描述')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司')
  },
  {
    field: 'baseMeasureUnitCode',
    headerText: i18n.t('基本单位编码')
  },
  {
    field: 'baseMeasureUnitName',
    headerText: i18n.t('基本单位名称')
  },
  {
    field: 'statusDescription',
    headerText: i18n.t('状态')
  }
]

export const PAGE_PLUGIN = (that) => [
  {
    title: i18n.t('成本因子'),
    gridId: '9c4fd6e6-5a50-416e-9d57-d617ed9c8c72',
    toolbar,
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      columnData: columnData(that),
      asyncConfig: {
        url: '/masterDataManagement/tenant/item-cost-factor/paged-query',
        // recordsPosition: "data",
        params: {
          onlyCurrentLevel: 0
        }
        // params: {
        //   condition: "and",
        //   tenantId: "10000",
        //   defaultRules: [
        //     {
        //       field: "organizationId",
        //       operator: "equal",
        //       type: "long",
        //       value: {},
        //     },
        //   ],
        // },
      }
    }
  },
  {
    title: i18n.t('属性大类'),
    gridId: '8bf1dff4-9b06-4210-90ed-3942521531bc',
    toolbar,
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      columnData: attributeTypeCols,
      asyncConfig: {
        url: '/masterDataManagement/tenant/item-cost-factor/paged-query',
        // recordsPosition: "data",
        params: {
          onlyCurrentLevel: 1
        }
      }
    }
  },
  {
    title: i18n.t('材质'),
    gridId: '382cecb1-5177-4889-9c20-707d3c93b2b5',
    toolbar: toolbarTwo,
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      columnData: materialQualityCols,
      asyncConfig: {
        url: '/masterDataManagement/tenant/item-cost-factor/paged-query',
        // recordsPosition: "data",
        params: {
          onlyCurrentLevel: 2
        }
      }
    }
  },
  {
    title: i18n.t('模具'),
    gridId: '227209e8-d13d-48ee-a598-c2ebdc5066a4',
    toolbar: [],
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      columnData: moldColumnData,
      asyncConfig: {
        url: '/masterDataManagement/tenant/item-cost-factor/paged-query',
        // recordsPosition: "data",
        params: {
          onlyCurrentLevel: 3
        }
      }
    }
  }
]
