import { PAGE_PLUGIN } from './index'

export default {
  data() {
    return {
      attributeSelect: [], //属性大类
      costFactorGroupSelect: [] // 属性中类
    }
  },
  mounted() {
    this.initDictItems()
  },
  methods: {
    // 初始化字典
    async initDictItems() {
      const tasks = [
        // 属性大类
        this.$API.itemManagement.itemTreelistOne({
          dictCode: 'COST_MODEL_ITEM_LEVEL_TREE',
        }),
        // 属性中类
        this.$API.masterData.dictionaryGetList({ dictCode: 'COST_FACTOR_GROUP' }),
      ]
      const result = await Promise.all(tasks)
      if (result) {
        this.attributeSelect = result[0]?.data || []
        this.costFactorGroupSelect = result[1]?.data || []
        // 列表页面，字典初始化完成后，重新刷新pageConfig，否则查询条件下拉列表没有值
        this.type === 'list' && (this.pageConfig = PAGE_PLUGIN(this))
      }
    }
  }
}
