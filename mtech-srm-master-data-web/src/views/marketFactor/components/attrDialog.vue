// 行情因子新增
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="rules">
        <mt-form-item prop="attributeCode" :label="$t('属性代码')">
          <mt-input
            v-model="formObject.attributeCode"
            float-label-type="Never"
            :placeholder="$t('请输入属性代码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="attributeName" :label="$t('属性名称')">
          <mt-input
            v-model="formObject.attributeName"
            float-label-type="Never"
            :placeholder="$t('请输入属性名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="attributeTypeCode" :label="$t('类型')">
          <mt-select
            v-model="formObject.attributeTypeCode"
            float-label-type="Never"
            :data-source="attributeTypeList"
            :placeholder="$t('请选择类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="parentAttributeCode" :label="$t('父类')">
          <mt-select
            ref="unitRef"
            v-model="formObject.parentAttributeCode"
            float-label-type="Never"
            :data-source="parentAttributeList"
            :allow-filtering="true"
            filter-type="Contains"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :placeholder="$t('请选择父类')"
            @change="parentAttributeChange"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { formatRules } from '@/utils/util'
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      parentAttributeList: [],
      rules: {},
      formObject: {},
      editStatus: false,
      attributeTypeList: [
        { text: this.$t('实际定额'), value: 0 },
        { text: this.$t('条件基价'), value: 1 }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData[0],
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    headStates() {
      return this.modalData.headStates
    }
  },
  mounted() {
    this.getRules()
    this.getOrderType()
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.formObject = { ...this.modalData.data }
    }
  },
  methods: {
    getRules() {
      if (this.headStates == 'Add') {
        this.$API.marketFactor.addAttrValid().then((res) => {
          if (res.code == 200) this.rules = formatRules(res.data)
        })
      } else {
        this.$API.marketFactor.updateAttrValid().then((res) => {
          if (res.code == 200) this.rules = formatRules(res.data)
        })
      }
    },
    parentAttributeChange(e) {
      if (e?.value) {
        this.formObject.parentAttributeName = e.itemData.itemName
      } else {
        this.formObject.parentAttributeName = ''
      }
    },
    // 获取父类下拉
    getOrderType() {
      this.$API.baseMainData
        .getAllAreaTypeOrUnit({ dictCode: 'parentAttributeList' })
        .then((res) => {
          this.parentAttributeList = res.data
        })
    },
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let parameter = { ...this.formObject }
          if (this.headStates == 'Add') {
            this.$API.marketFactor.addAttri(parameter).then(() => {
              this.$emit('confirm-function')
            })
          }
          if (this.headStates == 'edit') {
            this.$API.marketFactor.updateAttri(parameter).then(() => {
              this.$emit('confirm-function')
            })
          }
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/ .div-auth {
    background: #fafafa;
    height: 35px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    color: #0f0f0f;
    font-size: 12px;
    cursor: pointer;
    border-radius: 5px;
  }
}
</style>
