import { i18n } from '@/main.js'

export const costTypeList = [{ type: i18n.t('品类'), ref: 'categoryRef' }]

export const pageConfig = (that) => {
  const columnMap = {
    0: i18n.t('品类')
  }
  const columnData = [
    {
      width: '50',
      type: 'checkbox'
    },
    {
      field: 'relationCode',
      headerText: i18n.t(`${columnMap[that.activeType]}编码`)
    },
    {
      field: 'relationName',
      headerText: i18n.t(`${columnMap[that.activeType]}名称`)
    }
  ]
  return [
    {
      useToolTemplate: false,
      toolbar: {
        useBaseConfig: false,
        tools: [
          [
            {
              id: 'add',
              icon: 'icon_solid_Createorder',
              title: i18n.t('新增'),
              visibleCondition: () => that.editAble
            },
            {
              id: 'del',
              icon: 'icon_solid_delete',
              title: i18n.t('删除'),
              visibleCondition: () => that.editAble
            }
          ]
        ]
      },
      grid: {
        allowFiltering: true,
        columnData,
        asyncConfig: {
          url: '/masterDataManagement/tenant/costFactorRel/queryRel',
          defaultRules: [
            {
              label: '因子ID',
              field: 'costFactorId',
              type: 'number',
              operator: 'equal',
              value: that.modalData.data?.id
            }
          ],
          params: {
            pageFlag: false,
            condition: '',
            relationType: that.activeType
          }
        }
      }
    }
  ]
}

export const categoryColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]
export const categoryPageConfig = (that) => [
  {
    gridId: '5a81e158-324c-44d8-8745-ea2fafb6db70',
    useToolTemplate: false,
    toolbar: [],
    grid: {
      allowFiltering: true,
      columnData: categoryColumnData,
      asyncConfig: {
        url: that.$API.dataScope.getCateTreeApi
      }
    }
  }
]
