<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <ul class="type-list">
        <li
          v-for="(item, index) in costTypeList"
          :key="index"
          @click="activeType = index"
          :class="[{ active: activeType == index }]"
        >
          {{ item.type }}
        </li>
      </ul>

      <div class="grid-content">
        <mt-template-page
          ref="templateRef"
          class="son-grid"
          :template-config="templateConfig"
          @handleClickToolBar="handleClickToolBar"
        />
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { costTypeList, pageConfig } from './config'

export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      pageConfig: pageConfig(this),
      dataSourceArr: [],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('确认') }
        }
      ],
      costTypeList,
      activeType: 0
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    editAble() {
      // 1-激活，3-失效
      // return this.modalData.data.statusId !== '1'
      return true
    },
    templateConfig() {
      return pageConfig(this)
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef?.show()
  },
  methods: {
    cancel() {
      this.$emit('cancel-function')
    },
    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      const _selectGridRecords = gridRef.getMtechGridRecords()
      if (toolbar.id == 'del' && _selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (toolbar.id === 'add') {
        this.handleAdd()
      } else if (toolbar.id === 'del') {
        this.handleDelete(_selectGridRecords)
      }
    },
    // 新增
    handleAdd() {
      this.$dialog({
        modal: () => import('./selectDialog.vue'),
        data: {
          title: costTypeList[this.activeType].type,
          relationType: this.activeType
        },
        success: async (list) => {
          const relList = []
          const { id, itemCode } = this.modalData.data
          list.forEach((item) =>
            relList.push({
              relationId: item.id,
              relationCode: item.categoryCode,
              relationName: item.categoryName
            })
          )
          const params = {
            costFactorId: id,
            costFactorCode: itemCode,
            relList,
            relationType: this.activeType
          }
          const res = await this.$API.marketFactor.saveRel(params)
          if (res.code === 200) {
            this.$toast({ content: this.$t('操作成功！'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        }
      })
    },
    // 删除
    handleDelete(_selectRecords) {
      const idList = []
      _selectRecords.forEach((item) => idList.push(item.id))
      const params = {
        costFactorId: this.modalData.data.id,
        relationType: this.activeType,
        idList
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认执行删除操作？')
        },
        success: () => {
          this.$API.marketFactor.deleteRel(params).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: res.message || this.$t('删除成功'),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
.type-list {
  width: 150px;
  margin-right: 15px;
  flex-shrink: 0;
  text-align: center;
  li {
    line-height: 50px;
    border: 1px solid #e8e8e8;
    border-top: none;
    &:nth-of-type(1) {
      border-top: 1px solid #e8e8e8;
    }
    &:hover {
      background-color: #eeeeee;
    }
    &.active {
      background-color: #dadada;
    }
  }
}
.grid-content {
  position: relative;
  flex: 1;
  .son-grid {
    position: absolute;
  }
}
</style>
