<template>
  <!-- @beforeClose="cancel" -->
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form class="dialog-mt-form" ref="addFormRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="itemCode" v-if="headStates == 'edit'" :label="$t('成本因子编码')">
          <mt-input
            v-model="formObject.itemCode"
            float-label-type="Never"
            :disabled="true"
            :placeholder="$t('成本因子编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('成本因子名称')">
          <mt-input
            v-model="formObject.itemName"
            float-label-type="Never"
            max-length="20"
            :disabled="headStates == 'edit'"
            :placeholder="$t('请输入成本因子')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('适用公司')">
          <div class="company-auth" @click="handleAuthDialog">
            {{ authText }}
          </div>
        </mt-form-item>
        <mt-form-item prop="costFactorSpecification" :label="$t('规格')">
          <mt-input
            v-model="formObject.costFactorSpecification"
            float-label-type="Never"
            :disabled="headStates == 'edit'"
            :placeholder="$t('请输入规格')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="costFactorProperty" :label="$t('属性大类')">
          <mt-select
            v-model="formObject.costFactorProperty"
            float-label-type="Never"
            :show-clear-button="true"
            :data-source="attributeSelect"
            :fields="{ text: 'itemName', value: 'itemName' }"
            :disabled="headStates == 'edit'"
            :placeholder="$t('请选择属性大类')"
            @change="handleSelectChange($event, 'costFactorProperty')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="costFactorGroup" :label="$t('属性中类')">
          <mt-select
            v-model="formObject.costFactorGroup"
            float-label-type="Never"
            :data-source="costFactorGroupSelect"
            :fields="{ text: 'itemName', value: 'itemName' }"
            :show-clear-button="true"
            :disabled="headStates == 'edit'"
            :placeholder="$t('请选择属性中类')"
            @change="handleSelectChange($event, 'costFactorGroup')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="baseMeasureUnitCode" :label="$t('单位')">
          <mt-select
            v-model="formObject.baseMeasureUnitCode"
            float-label-type="Never"
            :data-source="baseMeasureUnitCodeSelect"
            :fields="{ text: 'text', value: 'unitCode' }"
            :allow-filtering="true"
            :show-clear-button="true"
            :filtering="inputbaseMeasureUnitCode"
            :placeholder="$t('请选择单位')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="maxQuotePercent" :label="$t('报价上限（%）')">
          <mt-input-number
            v-model="formObject.maxQuotePercent"
            :show-clear-button="true"
            precision="2"
            min="0"
            :placeholder="$t('请输入报价上限')"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item prop="minQuotePercent" :label="$t('报价下限（%）')">
          <mt-input-number
            v-model="formObject.minQuotePercent"
            :show-clear-button="true"
            precision="2"
            min="0"
            :placeholder="$t('请输入报价下限')"
          ></mt-input-number>
        </mt-form-item>
      </mt-form>
      <mt-form ref="dialogRef1" :model="formObject">
        <mt-form-item class="remark" prop="costFactorRemark" :label="$t('备注')">
          <mt-input
            v-model="formObject.costFactorRemark"
            float-label-type="Never"
            :placeholder="$t('字数不超过200字')"
            maxlength="200"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { utils } from '@mtech-common/utils'
import mixin from '../config/mixin'

export default {
  mixins: [mixin],
  data() {
    return {
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      //v-model获取的值
      formObject: {},
      //必填项
      formRules: {
        itemName: [
          {
            required: true,
            message: this.$t('请输入成本因子名称'),
            trigger: 'blur'
          }
        ],
        costFactorSpecification: [
          {
            required: true,
            message: this.$t('请输入规格'),
            trigger: 'blur'
          }
        ],
        costFactorProperty: [
          {
            required: true,
            message: this.$t('请选择属性大类'),
            trigger: 'blur'
          }
        ],
        costFactorGroup: [
          {
            required: true,
            message: this.$t('请选择属性中类'),
            trigger: 'blur'
          }
        ],
        baseMeasureUnitCode: [
          {
            required: true,
            message: this.$t('请选择单位'),
            trigger: 'blur'
          }
        ],
        companyCode: [
          {
            required: true,
            message: this.$t('请选择公司'),
            trigger: 'blur'
          }
        ]
      },
      //------------下拉
      companySelect: [], //公司
      baseMeasureUnitCodeSelect: [], //单位
      companyRelSaveRequestList: [],
      authText: ''
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    AddData() {
      return this.modalData.data
    },
    headStates() {
      return this.modalData.headStates
    }
  },
  mounted() {
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.formObject = { ...this.modalData.data }
    }
    this.$refs['dialog'].ejsRef.show()
    this.inputbaseMeasureUnitCode = utils.debounce(this.inputbaseMeasureUnitCode, 1000)
    if (this.headStates == 'Add') {
      this.initialCallInterface()
    } else if (this.headStates == 'edit') {
      this.initialCallInterface()
      this.editCallInterface()
    }
    this.getAuthList()
    this.getRules()
  },
  methods: {
    getFormAuthText() {
      let _list = this?.formObject?.companyRelSaveRequestList ?? []
      if (_list.length) {
        let _name = []
        _list.forEach((e) => {
          _name.push(e.relationName)
        })
        this.authText = _name.join(',')
      } else {
        this.authText = `${this.$t('请选择适用公司')}`
      }
    },
    //公司授权弹框
    handleAuthDialog() {
      this.$dialog({
        modal: () => import('@/components/market/companyAuth/index.vue'),
        data: {
          localList: this.companyRelSaveRequestList,
          data: this.formObject,
          queryId: 'costModelId',
          getAuthList: this.$API.marketFactor.getAuthCompanyList
        },
        success: (res) => {
          let { companyRelList } = res
          this.companyRelSaveRequestList = companyRelList
          this.$set(this.formObject, 'companyRelSaveRequestList', companyRelList)
          this.getFormAuthText()
        }
      })
    },
    //新增调用接口
    initialCallInterface() {
      let parameter = {
        fuzzyParam: '',
        organizationLevelCodes: ['ORG02'],
        orgType: 'ORG001PRO'
      }
      this.$API.material.findSpecifiedChildrenLevelOrgs(parameter).then((res) => {
        res.data?.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.companySelect = res.data
      })

      let pramas = {
        commonCode: '',
        dataLimit: 0,
        fuzzyParam: '',
        tenantId: 0
      }
      this.$API.marketFactor.fuzzyQuery(pramas).then((res) => {
        res.data?.forEach((item) => {
          item.text = item.unitCode + '-' + item.unitName
        })
        this.baseMeasureUnitCodeSelect = res.data
      })
    },
    //单位模糊查询事件
    inputbaseMeasureUnitCode(e) {
      let parameter = {
        commonCode: '',
        dataLimit: 0,
        fuzzyParam: e.text,
        tenantId: 0
      }
      this.$API.marketFactor.fuzzyQuery(parameter).then((res) => {
        res.data?.forEach((item) => {
          item.text = item.unitCode + '-' + item.unitName
        })
        this.baseMeasureUnitCodeSelect = res.data
      })
    },
    //编辑回选
    async editCallInterface() {
      let { baseMeasureUnitCode } = this.AddData
      let pramas = {
        commonCode: '',
        dataLimit: 0,
        fuzzyParam: baseMeasureUnitCode ?? '',
        tenantId: 0
      }
      await this.$API.marketFactor.fuzzyQuery(pramas).then((res) => {
        res.data?.forEach((item) => {
          item.text = item.unitCode + '-' + item.unitName
        })
        this.baseMeasureUnitCodeSelect = res.data
      })
      let parameter = {
        fuzzyParam: '',
        organizationLevelCodes: ['ORG02'],
        orgType: 'ORG001PRO'
      }
      await this.$API.material.findSpecifiedChildrenLevelOrgs(parameter).then((res) => {
        this.companySelect = res.data
      })

      await this.assignment()
    },
    assignment() {
      this.formObject = { ...this.AddData }
    },
    getAuthList() {
      const DEFAULTPARAM = {
        page: {
          current: 1,
          size: 1000
        },
        pageFlag: false,
        condition: '',
        relationType: 1,
        defaultRules: [
          {
            label: '因子ID',
            field: 'costFactorId',
            type: 'number',
            operator: 'equal',
            value: this.formObject.id
          }
        ]
      }
      this.$API.marketFactor.getAuthCompanyList(DEFAULTPARAM).then((res) => {
        if (res.code === 200) {
          this.$set(this.formObject, 'companyRelSaveRequestList', res.data.records)
          this.getFormAuthText()
        }
      })
    },

    //点击确认
    confirm() {
      this.$refs.addFormRef.validate((valid) => {
        if (valid) {
          let parameter = { ...this.formObject }
          if (
            !parameter?.companyRelSaveRequestList ||
            parameter?.companyRelSaveRequestList?.length <= 0
          ) {
            this.$toast({
              content: this.$t('请选择适用公司')
            })
            return
          }
          if (this.headStates == 'Add') {
            this.$API.marketFactor.add(parameter).then(() => {
              this.$emit('confirm-function')
            })
          }
          if (this.headStates == 'edit') {
            this.$API.marketFactor.update(parameter).then(() => {
              this.$emit('confirm-function')
            })
          }
        }
      })
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    },
    getRules() {
      if (this.headStates == 'Add') {
        this.$API.marketFactor.addValid().then((res) => {
          let formatRules = formatRules(res.data)
          if (res.code == 200) this.formRules = { ...formatRules }
        })
      } else {
        this.$API.marketFactor.updateValid().then((res) => {
          let formatRules = formatRules(res.data)
          if (res.code == 200) this.formRules = { ...formatRules }
        })
      }
    },
    //选择框事件监听
    handleSelectChange(event, field) {
      const { itemCode } = event.itemData
      if (field === 'costFactorProperty') {
        this.formObject.costFactorAttrCode = itemCode
      }
      if (field === 'costFactorGroup') {
        this.formObject.costFactorGroupCode = itemCode
      }
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    padding: 0;
    .dialog-content {
      padding: 40px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 400px;
        }
        .remark {
          width: 100% !important;
        }
      }
    }
  }
}
.company-auth {
  background: #fafafa;
  margin-top: 6px;
  display: flex;
  align-items: center;
  padding: 6px 10px;
  color: #0f0f0f;
  font-size: 12px;
  cursor: pointer;
  border-radius: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
