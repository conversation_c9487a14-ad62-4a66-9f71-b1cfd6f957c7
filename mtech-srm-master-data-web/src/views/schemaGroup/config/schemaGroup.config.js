import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-12-24 13:43:09
 * @LastEditTime: 2021-12-29 17:16:20
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\workCenter\config\workCenter.config.js
 */
export const PAGE_CONFIG = [
  {
    toolbar: [
      // "Add",
      // "Delete",
      {
        id: 'Sync',
        icon: 'icon_solid_Createproject',
        title: i18n.t('同步'),
        visibleCondition: () => false
      }
    ],
    grid: {
      frozenColumns: 2,
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'schemaGroupCode',
          headerText: i18n.t('方案组代码'),
          width: '250'
          // cellTools: ["edit", "delete", "preview"],
        },
        {
          field: 'schemaGroupName',
          headerText: i18n.t('案组名称'),
          width: '200'
        },
        {
          field: 'statusId',
          headerText: i18n.t('状态'),
          width: '150',
          valueConverter: {
            type: 'map',
            map: {
              0: i18n.t('草稿'),
              1: i18n.t('激活'),
              2: i18n.t('分发'),
              3: i18n.t('失效'),
              '-1': i18n.t('待审核'),
              '-2': i18n.t('准备')
            }
          }
        },
        {
          field: 'businessOrganizationCode',
          headerText: i18n.t('业务组织代码'),
          width: '150'
        },
        {
          field: 'businessOrganizationName',
          headerText: i18n.t('业务组织名称'),
          width: '200'
        },
        {
          field: 'businessOrganizationDescription',
          headerText: i18n.t('业务组织描述'),
          width: '200'
        },
        {
          field: 'schemaGroupDescription',
          headerText: i18n.t('方案组描述'),
          width: 'auto'
        }
      ],
      asyncConfig: {
        url: `/masterDataManagement/tenant/schema-group/paged-query?BU_CODE=${localStorage.getItem(
          'currentBu'
        )}`,
        params: {}
      }
    }
  }
]
