<!--
 * @Author: your name
 * @Date: 2021-09-06 10:15:13
 * @LastEditTime: 2022-01-05 14:17:00
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\permission\Role.vue
-->
<template>
  <div class="full-height mt-pt-20">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>

    <mt-dialog
      ref="curdDialog"
      :header="headerTitle"
      :enable-resize="false"
      :position="{ X: 'right', Y: 'top' }"
      css-class="create-proj-dialog right-wrapper"
      :buttons="buttons"
    >
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <!-- <div class="full-width dialog-subtitle mt-mb-20">{{ $t("创建角色") }}</div> -->
        <mt-form-item prop="organizationName" :label="$t('业务组织名称')">
          <mt-input
            v-model="ruleForm.organizationName"
            :disabled="disabled"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="organizationCode" :label="$t('业务组织编码')">
          <mt-input
            v-model="ruleForm.organizationCode"
            :disabled="type == 'edit' ? true : disabled"
            type="text"
            :placeholder="$t('请输入')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="organizationTypeCode" :label="$t('业务组织类型')">
          <mt-select
            v-model="ruleForm.organizationTypeCode"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :data-source="options.GROUP_TYPES"
            :disabled="disabled"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
      </mt-form>
      <mt-template-page
        v-show="headerTitle == $t('预览')"
        ref="previewPage"
        :hidden-tabs="true"
        :template-config="previewConfig"
        @handleClickToolBar="handleClickToolBarUser"
        @handleClickCellTool="handleClickCellToolUser"
      ></mt-template-page>
    </mt-dialog>

    <mt-dialog
      ref="userDialog"
      :header="$t('分配下属组织')"
      :enable-resize="false"
      :position="{ X: 'right', Y: 'top' }"
      css-class="create-proj-dialog right-wrapper"
      :buttons="buttonsUser"
    >
      <!-- <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="roleName" label="业务组织名称：">
          <mt-input
            v-model="ruleForm.organizationName"
            :disabled="true"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入业务组织名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="roleCode" :label="$t('业务组织编码')">
          <mt-input
            v-model="ruleForm.organizationCode"
            :disabled="true"
            type="text"
            :placeholder="$t('请输入业务组织编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="organizationTypeCode" :label="$t('业务组织类型')">
          <mt-select
            v-model="ruleForm.organizationTypeCode"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :data-source="options.GROUP_TYPES"
            :disabled="true"
            :show-clear-button="true"
            :placeholder="$t('请选择业务组织类型')"
          ></mt-select>
        </mt-form-item>
      </mt-form> -->
      <mt-template-page
        ref="userPage"
        :hidden-tabs="true"
        :template-config="userPageConfig"
        @handleClickToolBar="handleClickToolBarUser"
        @handleClickCellTool="handleClickCellToolUser"
      ></mt-template-page>
    </mt-dialog>

    <mt-dialog
      ref="addDialog"
      :header="$t('新增')"
      css-class="create-proj-dialog"
      :buttons="buttonsUser"
    >
      <!-- <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="roleName" label="业务组织名称：">
          <mt-input
            v-model="ruleForm.organizationName"
            :disabled="true"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入业务组织名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="roleCode" :label="$t('业务组织编码')">
          <mt-input
            v-model="ruleForm.organizationCode"
            :disabled="true"
            type="text"
            :placeholder="$t('请输入业务组织编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="organizationTypeCode" :label="$t('业务组织类型')">
          <mt-select
            v-model="ruleForm.organizationTypeCode"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :data-source="options.GROUP_TYPES"
            :disabled="true"
            :show-clear-button="true"
            :placeholder="$t('请选择业务组织类型')"
            @change="chooseName"
          ></mt-select>
        </mt-form-item>
      </mt-form> -->
      <div class="companyQuery">
        <mt-input
          v-model="companyQuery"
          :show-clear-button="true"
          :width="200"
          type="text"
          :placeholder="$t('请输入公司名称')"
          @input="inputPersonnel"
        ></mt-input>
        <!-- <mt-button>{{ $t("确认") }}</mt-button> -->
      </div>
      <mt-template-page
        ref="userPages"
        :hidden-tabs="true"
        :template-config="userPageConfigs"
      ></mt-template-page>
    </mt-dialog>
  </div>
</template>

<script>
import {
  OPTIONS,
  PAGE_PLUGIN,
  previewConfig,
  USER_DIALOG_PAGE_PLUGIN,
  USER_DIALOG_PAGE_PLUGINS
} from './config/role.config'
import { formatRules } from '@/utils/util'
export default {
  data() {
    return {
      pageConfig: PAGE_PLUGIN,
      userPageConfig: USER_DIALOG_PAGE_PLUGIN,
      userPageConfigs: USER_DIALOG_PAGE_PLUGINS,
      previewConfig: previewConfig,
      options: OPTIONS,
      assignParams: {}, //分配下属组织参数
      paymethodCode: '',
      timer: '', //延时器
      ruleForm: {},
      rules: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      buttonsUser: [
        {
          click: this.cancelUser,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.saveUser,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ids: '',
      businessOrganizationId: '',
      type: 'add',
      roleInfo: {},
      showDialogTransfer: false,
      userGroupInfo: {},
      userGroupContentList: [],
      changeState: false
    }
  },
  computed: {
    headerTitle() {
      if (this.type === 'add') {
        return this.$t('新增')
      }
      if (this.type === 'edit') {
        return this.$t('编辑')
      }
      return this.$t('预览')
    },
    disabled() {
      if (this.type === 'preview') {
        return true
      }
      return false
    }
  },
  mounted() {},
  methods: {
    chooseName(e) {
      if (this.changeState) {
        let { itemData } = e
        this.ruleForm.organizationTypeName = itemData.itemName
        this.ruleForm.organizationTypeId = itemData.id
        this.ruleForm.organizationTypeCode = e.value
      } else {
        this.changeState = true
      }
    },
    rulesGet(type) {
      this.$API.purchase[type]().then((res) => {
        this.rules = formatRules(res.data)
      })
    },
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const rowSelected = grid?.getSelectedRecords?.()

      if (toolbar.id === 'Add') {
        this.handleAction('add')
        this.typeGet()
        this.rulesGet('roleAddValid')
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDelete(rowSelected)
      }
    },
    handleClickCellTool(e) {
      const { data, tool } = e
      this.paymethodCode = data.paymethodCode
      if (tool.id === 'edit') {
        this.handleAction('edit', data)
        this.typeGet()
        this.rulesGet('updataRolesValid')
      }

      if (tool.id === 'user') {
        this.assignParams = { paymethodCode: this.paymethodCode }
        this.assignSubordinateOrganization(this.assignParams)
        this.$refs.userDialog.ejsRef.show()
      }
      if (tool.id === 'delete') {
        this.handleDelete([data])
      }
      if (tool.id === 'preview') {
        this.ids = data.tenantId
        this.businessOrganizationId = data.id
        this.$set(this.previewConfig[0].grid.asyncConfig, 'defaultRules', [
          {
            field: 'businessOrganizationId',
            operator: 'equal',
            value: data.id
          }
        ])
        this.typeGet()
        this.handleAction('preview', data)
      }
    },
    handleAction(type, data) {
      this.type = type
      this.$refs.ruleForm.resetFields()
      this.ruleForm = Object.assign({}, data)
      this.$refs.curdDialog.ejsRef.show()
    },
    handleDelete(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.purchase.batchDelete(data).then(() => {
            //
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    },
    cancel() {
      this.$refs.curdDialog.ejsRef.hide()
    },
    save() {
      if (this.type === 'preview') {
        this.cancel()
        return false
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.type === 'add') {
            const data = {
              ...this.ruleForm,
              parentId: 0
            }
            // /api/masterDataManagement/tenant/business-organization/add
            this.$API.purchase.treeAdd(data).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
          if (this.type === 'edit') {
            this.$API.purchase.updataRoles(this.ruleForm).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
        }
      })
    },
    handleClickToolBarUser(e) {
      // console.log(123123, e);
      const { toolbar, grid } = e
      const rowSelected = grid?.getSelectedRecords?.()

      if (toolbar.id === 'Add') {
        this.handleActionUser()
      }
      if (toolbar.id === 'Delete' && rowSelected.length == 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        console.log(rowSelected)
        this.handleDeleteUser(rowSelected)
      }
      //刷新数据
      if (toolbar.id === 'refreshDataByLocal') {
        this.assignSubordinateOrganization()
      }
    },
    handleClickCellToolUser(e) {
      const { data, tool } = e
      if (tool.id === 'delete') {
        console.log(data)
        this.handleDeleteUser([data])
      }
    },
    async handleActionUser() {
      this.showDialogTransfer = true
      this.$refs.addDialog.ejsRef.show()
      // this.userPageConfigs[0].grid.asyncConfig.params = Object.assign(
      //   {},
      //   { roleId: this.ids }
      // );
      // console.log("新增");
      let params = {
        organizationLevelCodes: ['ORG02', 'ORG01'],
        orgType: 'ORG001PRO',
        includeItself: true,
        fuzzyParam: ''
      }
      this.$API.paymentMethod.findSpecifiedChildrenLevelOrgs(params).then((res) => {
        console.log(res)
        this.$set(this.userPageConfigs[0].grid, 'dataSource', res.data)
      })
    },
    handleDeleteUser(rows) {
      console.log(rows)
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.paymentMethod.batchDelete(data).then(() => {
            this.$refs.userPage.refreshCurrentGridData()
          })
        }
      })
    },
    cancelUser(show) {
      let str = this.showDialogTransfer ? 'addDialog' : 'userDialog'
      if (this.showDialogTransfer && show === true) {
        const currentTab = this.$refs.userPages.getCurrentTabRef()
        const rowSelected = currentTab.grid.getSelectedRecords()
        const organizationIds = rowSelected.map((e) => e.id)

        console.log(organizationIds, this.paymethodCode, 'organizationIds...paymethodCode')
        const data = {
          organizationIds,
          paymethodCode: this.paymethodCode
        }
        this.$API.paymentMethod.batchAdd(data).then(() => {
          this.showDialogTransfer = false
          this.$refs.userPage.refreshCurrentGridData()
          this.$refs[str].ejsRef.hide()
        })
      } else {
        this.$refs[str].ejsRef.hide()
        this.showDialogTransfer = false
      }
    },
    saveUser() {
      this.cancelUser(true)
    },
    typeGet(dictCode = 'BUORG') {
      const query = { dictCode }
      this.$API.dict.TenantDictTypeGet(query).then((res) => {
        this.options.GROUP_TYPES = res.data
      })
    },
    //右边弹框刷新接口调用
    assignSubordinateOrganization() {
      this.$API.paymentMethod.criteriaQuery(this.assignParams).then((res) => {
        console.log(res.data)
        this.$set(this.userPageConfig[0].grid, 'dataSource', res.data)
      })
    },
    //模糊查询
    inputPersonnel(e) {
      // console.log(e, "模糊查询的值");
      clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        let params = {
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true,
          fuzzyParam: e
        }
        this.$API.paymentMethod.findSpecifiedChildrenLevelOrgs(params).then((res) => {
          this.$set(this.userPageConfigs[0].grid, 'dataSource', res.data)
        })
      }, 500)
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .e-rowcell {
  text-align: left;
}
.dialog-subtitle {
  border-left: 3px solid #00469c;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  padding-left: 10px;
}
</style>
<style lang="scss">
.e-dlg-container > .right-wrapper {
  height: 100% !important;
  .e-dlg-header-content {
    background-color: rgba(243, 243, 243, 1);
    border-radius: 8px 0 0 0;
    .e-dlg-closeicon-btn {
      .e-btn-icon {
        color: #292929 !important;
      }
    }
    .e-dlg-header {
      color: #292929;
    }
  }
  .e-dlg-content {
    background-color: #f8f8f8;
  }
  .e-footer-content {
    background: rgba(255, 255, 255, 1);
    border-radius: 0 0 0 8px;
    box-shadow: inset 0 1px 0 0 rgba(232, 232, 232, 1);
  }
}
.companyQuery {
  margin-bottom: 20px;
}
</style>
