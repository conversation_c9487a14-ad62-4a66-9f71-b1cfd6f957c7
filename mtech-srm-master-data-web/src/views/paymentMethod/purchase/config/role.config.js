import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-08-24 15:18:49
 * @LastEditTime: 2022-01-05 13:37:57
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\permission\config\org.config.js
 */
export const OPTIONS = {
  GROUP_TYPES: []
}

export const PAGE_PLUGIN = [
  {
    toolbar: [],
    grid: {
      columnData: [
        {
          width: '50',
          type: 'checkbox',
          showInColumnChooser: false
        },
        {
          field: 'paymethodCode',
          headerText: i18n.t('付款方式编码'),
          cellTools: [
            {
              id: 'user',
              icon: 'icon_solid_Createproject',
              title: i18n.t('分配下属组织')
            }
          ]
        },
        {
          field: 'paymethodName',
          headerText: i18n.t('付款方式名称')
        }
      ],
      asyncConfig: {
        url: '/masterDataManagement/tenant/paymethod-org-rel/paged-query-pay-method'
      }
    }
  }
]
export const previewConfig = [
  {
    toolbar: {
      tools: []
    },
    grid: {
      columnData: [
        {
          field: 'organizationName',
          width: '200',
          headerText: i18n.t('组织名称')
        },
        {
          field: 'organizationCode',
          width: '150',
          headerText: i18n.t('组织编码')
        },
        {
          field: 'organizationLevelTypeName',
          headerText: i18n.t('组织类型'),
          width: '150'
        }
      ],
      asyncConfig: {
        url: 'masterDataManagement/tenant/business-org-org-rel/paged-query',
        params: {}
      }
    }
  }
]
//分配下属组织
export const USER_DIALOG_PAGE_PLUGIN = [
  {
    toolbar: {
      tools: [
        ['Add', 'Delete'],
        ['Refresh', 'Setting']
      ]
    },
    grid: {
      columnData: [
        {
          width: '50',
          type: 'checkbox',
          showInColumnChooser: false
        },
        {
          field: 'organizationCode',
          width: '200',
          headerText: i18n.t('公司编码')
          // cellTools: ["delete"],
        },
        {
          field: 'organizationName',
          width: '150',
          headerText: i18n.t('公司名称')
        }
      ]
      // asyncConfig: {
      //   // url: "masterDataManagement/tenant/paymethod-org-rel/criteria-query",
      //   // params: {},
      // },
    }
  }
]
export const USER_DIALOG_PAGE_PLUGINS = [
  {
    grid: {
      columnData: [
        {
          width: '50',
          type: 'checkbox',
          showInColumnChooser: false
        },
        {
          field: 'orgName',
          width: '200',
          headerText: i18n.t('组织名称')
        },
        {
          field: 'orgTypeCode',
          width: '150',
          headerText: i18n.t('组织编码')
        },
        {
          field: 'orgLevelTypeName',
          headerText: i18n.t('组织类型'),
          width: '150'
        }
      ]
      // asyncConfig: {
      //   url: "masterDataManagement/tenant/business-organization/getCompanySite",
      //   params: {},
      // },
    }
  }
]
