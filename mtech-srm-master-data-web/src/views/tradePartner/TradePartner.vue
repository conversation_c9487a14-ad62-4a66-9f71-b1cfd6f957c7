<!--
 * @Author: your name
 * @Date: 2021-12-15 13:52:05
 * @LastEditTime: 2021-12-20 16:04:39
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\tradePartner\TradePartner.vue
-->
<template>
  <div class="full-height mt-pt-20">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>

    <mt-dialog ref="dialog" :header="headerTitle" css-class="create-proj-dialog" :buttons="buttons">
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="tradingPartnerCode" :label="$t('贸易伙伴代码')">
          <mt-input
            v-model="ruleForm.tradingPartnerCode"
            :disabled="disabled"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入贸易伙伴代码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="tradingPartnerName" :label="$t('贸易伙伴名称')">
          <mt-input
            v-model="ruleForm.tradingPartnerName"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入贸易伙伴名称')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import { PAGE_CONFIG, RULES } from './config/tradePartner.config'
import { formatRules } from '@/utils/util'

export default {
  data() {
    return {
      pageConfig: PAGE_CONFIG,
      rules: RULES,

      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {},
      type: 'add' // add edit preview
    }
  },
  computed: {
    headerTitle() {
      if (this.type === 'add') {
        return this.$t('新增')
      }
      if (this.type === 'edit') {
        return this.$t('编辑')
      }
      return this.$t('预览')
    },
    disabled() {
      if (this.type === 'preview') {
        return true
      }
      return false
    }
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const rowSelected = grid.getSelectedRecords()
      if (toolbar.id === 'Add') {
        this.handleAction('add')
        this.rulesGet('tradePartnerAddValid')
      }
      if (toolbar.id === 'Sync') {
        this.handleSync()
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDelete(rowSelected)
      }
    },
    handleClickCellTool(e) {
      const { data, tool } = e
      if (tool.id === 'edit') {
        this.handleAction('edit', data)
        this.rulesGet('tradePartnerEditValid')
      }
      if (tool.id === 'delete') {
        this.handleDelete([data])
      }
      if (tool.id === 'preview') {
        this.handleAction('preview', data)
      }
    },
    handleAction(type, data) {
      this.type = type
      this.$refs.ruleForm.resetFields()
      this.ruleForm = Object.assign({}, data)
      this.$refs.dialog.ejsRef.show()
    },
    handleSync() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认执行同步操作吗？')
        },
        success: () => {
          this.$API.tradePartner.tradePartnerSync().then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    },
    handleDelete(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.tradePartner.tradePartnerDelete(data).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    },
    save() {
      if (this.type === 'preview') {
        this.cancel()
        return false
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.type === 'add') {
            const data = {
              ...this.ruleForm
            }
            this.$API.tradePartner.tradePartnerAdd(data).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
          if (this.type === 'edit') {
            delete this.ruleForm.tabIndex
            this.$API.tradePartner.tradePartnerEdit(this.ruleForm).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
        }
      })
    },
    cancel() {
      this.$refs.dialog.ejsRef.hide()
    },
    rulesGet(type) {
      this.$API.tradePartner[type]().then((res) => {
        this.rules = formatRules(res.data)
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
