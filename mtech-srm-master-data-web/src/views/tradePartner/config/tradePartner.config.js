import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-12-16 13:37:36
 * @LastEditTime: 2021-12-20 11:19:37
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\tradePartner\config\tradePartner.config.js
 */
export const PAGE_CONFIG = [
  {
    toolbar: [
      'Add',
      'Delete',
      {
        id: 'Sync',
        icon: 'icon_solid_Createproject',
        title: i18n.t('同步')
      }
    ],
    grid: {
      frozenColumns: 2,
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'tradingPartnerCode',
          headerText: i18n.t('贸易伙伴代码'),
          width: '250',
          cellTools: ['edit', 'delete', 'preview']
        },
        {
          field: 'tradingPartnerName',
          width: '200',
          headerText: '	贸易伙伴名称'
        },
        {
          field: 'statusId',
          headerText: i18n.t('状态'),
          width: '150',
          valueConverter: {
            type: 'map',
            map: {
              0: i18n.t('草稿'),
              1: i18n.t('激活'),
              2: i18n.t('分发'),
              3: i18n.t('失效'),
              '-1': i18n.t('待审核'),
              '-2': i18n.t('准备')
            }
          }
        },
        {
          field: 'createUserName',
          width: '150',
          headerText: i18n.t('创建人')
        },
        {
          width: 'auto',
          field: 'createTime',
          headerText: i18n.t('创建时间'),
          valueConverter: {
            type: 'date'
          }
        }
      ],
      asyncConfig: {
        url: `/masterDataManagement/tenant/trading-partner/paged-query?BU_CODE=${localStorage.getItem(
          'currentBu'
        )}`,
        params: {}
      }
    }
  }
]

export const RULES = {}
