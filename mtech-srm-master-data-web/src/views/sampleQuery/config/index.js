import { i18n } from '@/main.js'
// list toolbar
export const toolbar = []

// list column
export const columnData = [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    field: 'sortNum',
    width: '80',
    headerText: i18n.t('序号')
  },
  {
    field: 'quotaNo',
    headerText: i18n.t('样品确认单号')
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  // {
  //   field: 'supplierName', //
  //   headerText: i18n.t('申请数量'),
  // },
  {
    field: 'startTime',
    headerText: i18n.t('开始时间')
  },
  {
    field: 'endTime',
    headerText: i18n.t('结束时间')
  },
  {
    field: 'dataStatus',
    headerText: i18n.t('数据状态')
  },
  {
    field: 'conclusion',
    headerText: i18n.t('样品确认结论'),
    valueConverter: {
      type: 'map',
      map: {
        OK: i18n.t('合格'),
        'NG-01': i18n.t('不合格(重新送样)'),
        'NG-02': i18n.t('不合格(不重新送样)')
      }
    }
  },
  {
    field: 'limitTradeTotal',
    headerText: i18n.t('限制交易总量')
  },
  {
    field: 'limitTradeTimes',
    headerText: i18n.t('限制交易次数')
  },
  // {
  //   field: 'status',
  //   headerText: i18n.t('状态'),
  //   valueConverter: {
  //     type: 'map',
  //     fields: { text: 'text', value: 'value' },
  //     map: [
  //       { text: i18n.t('草稿'), value: 0, cssClass: ['btns warning'] }, //
  //       { text: i18n.t('有效'), value: 1, cssClass: ['btns warning'] },
  //       { text: i18n.t('失效'), value: 2, cssClass: ['btns warning'] },
  //     ],
  //   },
  // },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('申请人')
  },
  {
    field: 'createDate',
    headerText: i18n.t('申请日期')
  },
  {
    field: 'modifyDate',
    headerText: i18n.t('修改时间')
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('最后更新人')
  }
]

// list pageConfig
export const pageConfig = [
  {
    toolbar: toolbar,
    gridId: '152b71fa-a2f3-3574-49dc-266a8bacc705',
    activatedRefresh: false,
    grid: {
      height: 'auto',
      columnData,
      asyncConfig: {
        url: '/masterDataManagement/tenant/supplySourceListPlm/pagedQuery',
        serializeList: (list) => {
          list.forEach((item, index) => {
            item.sortNum = index + 1
          })
          return list
        },
        params: {}
      }
    }
  }
]
