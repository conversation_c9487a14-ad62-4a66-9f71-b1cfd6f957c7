<template>
  <div class="notice-hander hander">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      @handleClickToolBar="handleClickToolBar"
      @actionComplete="actionComplete"
      @actionBegin="actionBegin"
    />
  </div>
</template>
<script>
import { pageConfig } from './config'
import { i18n } from '@/main.js'
export default {
  data() {
    return {
      i18n,
      pageConfig: pageConfig
    }
  },
  methods: {}
}
</script>
