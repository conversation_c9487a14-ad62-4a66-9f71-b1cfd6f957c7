<template>
  <div class="pd10">
    <div class="toolbar">
      <mt-button css-class="e-flat" :is-primary="true" @click="cancel">
        {{ $t('取消') }}
      </mt-button>
      <mt-button css-class="e-flat" :is-primary="true" @click="save">
        {{ $t('保存') }}
      </mt-button>
    </div>
    <p class="grid-title mb10">{{ $t('父件') }}</p>
    <mt-template-page
      ref="templateParent"
      :template-config="pageConfigParent"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    />
    <p class="grid-title mt20">{{ $t('子件') }}</p>
    <mt-template-page
      ref="templateChildren"
      :template-config="pageConfigChildren"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @actionComplete="actionCompleteChildren"
    />
  </div>
</template>

<script src="./script.js" />

<style scoped lang="scss">
.pd10 {
  padding: 10px;
}
.mb10 {
  margin-bottom: 10px;
}
.mt20 {
  margin-top: 20px;
}
.toolbar {
  text-align: right;
}
.grid-title {
  color: #0044a0;
  position: relative;
  padding-left: 10px;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 2px;
    bottom: 2px;
    width: 2px;
    background-color: #0044a0;
  }
}
</style>
