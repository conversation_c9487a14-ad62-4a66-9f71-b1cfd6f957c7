export function columnData() {
  const result = []
  if (this.modalData.multiple) {
    result.push({
      type: 'checkbox',
      width: '60'
    })
  }
  result.push(
    ...[
      {
        width: '150',
        field: 'itemCode',
        headerText: this.$t('物料编号'),
        cssClass: 'click'
      },
      {
        width: '150',
        field: 'itemName',
        headerText: this.$t('物料名称'),
        cssClass: 'click'
      },
      {
        width: '150',
        field: 'itemGroupName',
        headerText: this.$t('物料组名称'),
        cssClass: 'click'
      },
      {
        width: '150',
        field: 'categoryCode',
        headerText: this.$t('品类编码'),
        cssClass: 'click'
      },
      {
        width: '150',
        field: 'categoryName',
        headerText: this.$t('品类名称'),
        cssClass: 'click'
      },
      {
        width: '150',
        field: 'itemDescription',
        headerText: this.$t('规格型号'),
        cssClass: 'click'
      },
      {
        width: '150',
        field: 'oldItemCode',
        headerText: this.$t('旧物料编号'),
        cssClass: 'click'
      },
      {
        width: '150',
        field: 'manufacturerName',
        headerText: this.$t('制造商'),
        cssClass: 'click'
      },
      {
        width: '150',
        field: 'isPriceRecord',
        ignore: true,
        allowFiltering: false,
        headerText: this.$t('是否有价格记录'),
        valueConverter: {
          type: 'map',
          map: { 0: this.$t('否'), 1: this.$t('是') }
        }
      }
    ]
  )
  return result
}

export default {
  data() {
    return {
      selectRowsCache: [],
      data: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: true, content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: [],
          gridId: '8fd37151-0774-db16-50c1-e0e3e947c4d2',
          grid: {
            allowFiltering: true,
            ignoreFields: ['isPriceRecord'],
            columnData: columnData.call(this),
            allowSorting: false,
            allowSelection: true,
            selectionSettings: {
              type: 'Multiple',
              mode: 'Row'
            },
            asyncConfig: {
              url: this.$API.item.pagedQuery,
              defaultRules: []
            }
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    }
  },
  created() {
    if (typeof this.modalData.queryParams === 'function') {
      this.pageConfig[0].grid.asyncConfig.defaultRules = this.modalData.queryParams()?.rules || []
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    confirm() {
      const grid = this.$refs.templateRef.getCurrentTabRef().grid
      const records = (grid.getMtechGridRecords || grid.getSelectedRecords)()
      if (records.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      this.$emit('confirm-function', records)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
