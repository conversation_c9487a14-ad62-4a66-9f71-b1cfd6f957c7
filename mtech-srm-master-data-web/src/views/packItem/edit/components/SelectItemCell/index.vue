<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <div class="cell-disabled" style="flex: 1; overflow: hidden">
        {{ displayValue }}
      </div>
      <mt-icon
        v-if="showClear"
        style="width: 20px"
        name="icon_list_refuse"
        @click.native="handleClear"
      ></mt-icon>
      <mt-icon style="width: 20px" name="icon_input_search" @click.native="showDialog"></mt-icon>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SelectItemCell',
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    onUpdate: {
      type: Function,
      default: () => {}
    },
    renderText: {
      type: Function,
      default: () => {}
    },
    showClear: {
      type: Boolean,
      default: true
    },
    queryParams: {
      type: Function,
      default: () => {}
    }
  },
  computed: {
    displayValue() {
      return typeof this.renderText === 'function'
        ? this.renderText(this.data)
        : this.data[this.data.column.field]
    }
  },
  methods: {
    handleClear() {
      this.onUpdate(null, +this.data.index)
    },
    showDialog() {
      const that = this
      this.$dialog({
        modal: () => import('../SelectItemDialog'),
        data: {
          title: this.$t('选择物料'),
          queryParams: this.queryParams
        },
        success: (records) => {
          that.onUpdate(records[0], +that.data.index)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
    }
    .cell-disabled,
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      line-height: 33px;
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
</style>
