import { createEditInstance } from '@/utils/ej/dataGrid'
import { createTpl } from '@/utils/ej/dataGrid/template'
import SelectItemCell from './components/SelectItemCell'

function columnDataParent() {
  const that = this
  return [
    {
      field: 'itemCode',
      headerText: this.$t('物料'),
      width: 300,
      template: createTpl({
        component: SelectItemCell,
        props: {
          renderText: (row) => [row.itemCode, row.itemName].filter((e) => e).join('-'),
          showClear: false,
          queryParams: () => {
            const itemCodes = that.getItemRelSaveRequests().map((e) => e.itemCode)
            return {
              condition: 'and',
              rules: itemCodes.map((code) => ({
                field: 'itemCode',
                type: 'string',
                operator: 'notequal',
                value: code
              }))
            }
          },
          onUpdate: (row, index) => {
            if (row === null) {
              that.pageConfigParent[0].grid.dataSource.splice(index, 1)
            } else {
              that.$set(that.pageConfigParent[0].grid.dataSource, index, row)
            }
          }
        }
      })
    },
    {
      field: 'statusDescription',
      headerText: this.$t('状态')
    },
    {
      field: 'itemDescription',
      headerText: this.$t('规格型号')
    },
    {
      field: 'baseMeasureUnitName',
      headerText: this.$t('基本计量单位')
    },
    {
      field: 'itemGroupName',
      headerText: this.$t('品项组')
    },
    {
      field: 'oldItemCode',
      headerText: this.$t('旧物料编码')
    }
  ]
}

function pageConfigParent() {
  return [
    {
      useToolTemplate: false,
      useBaseConfig: false,
      fieldDefines: [],
      grid: {
        height: 'auto',
        allowPaging: false,
        columnData: columnDataParent.call(this),
        dataSource: [],
        allowFiltering: false
      }
    }
  ]
}

function columnDataChildren() {
  const editInstance = createEditInstance()

  return [
    {
      type: 'checkbox',
      width: '60'
    },
    {
      field: 'itemCode',
      headerText: this.$t('物料'),
      allowEditing: false,
      width: 300,
      valueConverter: {
        type: 'function',
        filter: (_, item) => {
          return `${item.itemCode}-${item.itemName}`
        }
      }
    },
    {
      field: 'statusDescription',
      headerText: this.$t('状态'),
      allowEditing: false
    },
    {
      field: 'itemDescription',
      headerText: this.$t('规格型号'),
      allowEditing: false
    },
    // 单机用量：数值型，两位小数，手工录入，10位；含义是指一个组合物料中包含的子散件的标准用量；
    {
      field: 'unitCount',
      headerText: this.$t('单机用量'),
      allowEditing: true,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          min: 0,
          max: 1e9,
          precision: 2
        })
      })
    },
    {
      field: 'baseMeasureUnitName',
      headerText: this.$t('基本计量单位'),
      allowEditing: false
    },
    {
      field: 'itemGroupName',
      headerText: this.$t('品项组'),
      allowEditing: false
    },
    {
      field: 'oldItemCode',
      headerText: this.$t('旧物料编码'),
      allowEditing: false
    }
  ]
}

function pageConfigChildren() {
  return [
    {
      useToolTemplate: false,
      useBaseConfig: false,
      toolbar: [
        [
          {
            id: 'methods@add',
            icon: 'icon_solid_Createorder',
            title: this.$t('新增')
          },
          {
            id: 'methods@del',
            icon: 'icon_solid_Delete',
            title: this.$t('删除')
          }
        ]
      ],
      fieldDefines: [],
      grid: {
        height: 'auto',
        allowPaging: false,
        columnData: columnDataChildren.call(this),
        dataSource: [],
        allowFiltering: false,
        editSettings: {
          allowEditing: true,
          mode: 'Normal',
          showConfirmDialog: false,
          showDeleteConfirmDialog: false,
          newRowPosition: 'Top'
        }
      }
    }
  ]
}
export default {
  data() {
    return {
      pageConfigParent: pageConfigParent.call(this),
      pageConfigChildren: pageConfigChildren.call(this)
    }
  },
  computed: {
    isEdit() {
      return !!this.$route.query.id
    }
  },
  mounted() {
    this.initData()
  },
  // activated() {
  //   this.initData();
  // },
  methods: {
    async initData() {
      if (!this.isEdit) {
        this.pageConfigParent[0].grid.dataSource = [{}]
        this.pageConfigChildren[0].grid.dataSource = []
      } else {
        await Promise.all([this.initDataSourceParent(), this.initDataSourceChildren()])
      }
    },
    async initDataSourceParent() {
      const res = await this.$API.item
        .pagedQueryPost({
          page: { current: 1, size: 1000 },
          pageFlag: false,
          rules: [
            {
              field: 'id',
              type: 'string',
              operator: 'equal',
              value: this.$route.query.itemId
            }
          ]
        })
        .catch((e) => console.warn(e))
      if (res?.data?.records) {
        this.pageConfigParent[0].grid.dataSource = res.data.records
      }
    },
    async initDataSourceChildren() {
      const res = await this.$API.packItemRel
        .pagedQueryPost({
          page: { current: 1, size: 1000 },
          pageFlag: false,
          rules: [
            {
              field: 'parentItemId',
              type: 'number',
              operator: 'equal',
              value: this.$route.query.itemId
            }
          ]
        })
        .catch((e) => console.warn(e))
      if (res?.data?.records) {
        this.pageConfigChildren[0].grid.dataSource = res.data.records
      }
    },
    handleClickToolBar(arg) {
      const { toolbar } = arg
      if (toolbar.id.indexOf('methods@') === 0) {
        this[toolbar.id.replace(/^methods@/, '')]?.(arg)
      }
    },
    handleClickCellTool(arg) {
      const { tool } = arg
      if (tool.id.indexOf('methods@') === 0) {
        this[tool.id.replace(/^methods@/, '')]?.(arg)
      }
    },
    add() {
      const parentItemCode = this.getParentItemCode()
      const itemCodes = this.getItemRelSaveRequests().map((e) => e.itemCode)
      parentItemCode && itemCodes.push(parentItemCode)
      this.$dialog({
        modal: () => import('./components/SelectItemDialog'),
        data: {
          title: this.$t('选择物料'),
          multiple: true,
          queryParams: () => {
            if (!itemCodes?.length) {
              return {}
            }
            return {
              condition: 'and',
              rules: itemCodes.map((code) => ({
                field: 'itemCode',
                type: 'string',
                operator: 'notequal',
                value: code
              }))
            }
          }
        },
        success: (records) => {
          for (const record of records) {
            if (
              !this.pageConfigChildren[0].grid.dataSource.find(
                (e) => e.itemCode === record.itemCode
              )
            ) {
              this.pageConfigChildren[0].grid.dataSource.push({
                ...record,
                unitCount: 1
              })
            }
          }
        }
      })
    },
    del() {
      const grid = this.$refs.templateChildren.getCurrentTabRef().grid
      const records = (grid.getMtechGridRecords || grid.getSelectedRecords)()
      if (records.length === 0) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        return
      }
      const itemCodes = records.map((e) => e.itemCode)
      this.pageConfigChildren[0].grid.dataSource =
        this.pageConfigChildren[0].grid.dataSource.filter((e) => !itemCodes.includes(e.itemCode))
    },
    cancel() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('是否取消操作?')
        },
        success: async () => {
          this.$router.go(-1)
        }
      })
    },
    actionCompleteChildren({ requestType, rowData, rowIndex }) {
      if (requestType === 'save') {
        this.$set(this.pageConfigChildren[0].grid.dataSource, rowIndex, rowData)
      }
    },
    getParentItemCode() {
      return this.pageConfigParent[0].grid.dataSource?.[0]?.itemCode
    },
    getItemRelSaveRequests() {
      return this.pageConfigChildren[0].grid.dataSource.map(({ itemCode, unitCount }) => ({
        itemCode,
        unitCount
      }))
    },
    async save() {
      const grid = this.$refs.templateChildren.getCurrentTabRef().grid
      grid.endEdit()
      const parentItemCode = this.getParentItemCode()
      const itemRelSaveRequests = this.getItemRelSaveRequests()
      if (itemRelSaveRequests.length === 0) {
        this.$toast({ content: this.$t('请选择子件'), type: 'warning' })
        return
      }
      if (!parentItemCode) {
        this.$toast({ content: this.$t('请选择父件'), type: 'warning' })
        return
      }
      const res = await this.$API.packItem
        .save({
          id: this.$route.query.id,
          itemRelSaveRequests: itemRelSaveRequests,
          parentItemCode,
          statusId: 1
        })
        .catch((e) => console.warn(e))
      if (res) {
        this.$toast({ content: res.msg, type: 'success' })
        setTimeout(() => {
          this.$router.push({ name: 'pack-item-list' })
        }, 1500)
      }
    }
  }
}
