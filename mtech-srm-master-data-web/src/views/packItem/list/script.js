import ChildrenGrid from './components/ChildrenGrid'

function columnData() {
  return [
    {
      type: 'checkbox',
      width: '60'
    },
    {
      field: 'itemCode',
      headerText: this.$t('物料'),
      width: 300,
      cssClass: 'field-content',
      cellTools: [
        {
          id: 'methods@editRow',
          icon: 'icon_solid_edit',
          title: this.$t('编辑')
        }
      ],
      valueConverter: {
        type: 'function',
        filter: (_, item) => {
          return `${item.itemCode}-${item.itemName}`
        }
      }
    },
    {
      field: 'statusId',
      headerText: this.$t('状态'),
      valueConverter: {
        type: 'map',
        map: { 0: this.$t('失效'), 1: this.$t('激活') }
      }
    },
    {
      field: 'itemDescription',
      headerText: this.$t('规格型号')
    },
    {
      field: 'baseMeasureUnitName',
      headerText: this.$t('基本计量单位')
    },
    {
      field: 'itemGroupName',
      headerText: this.$t('品项组')
    },
    {
      field: 'oldItemCode',
      headerText: this.$t('旧物料编码')
    }
  ]
}

function pageConfig() {
  return [
    {
      toolbar: [
        {
          id: 'methods@add',
          icon: 'icon_solid_Createorder',
          title: this.$t('新增')
        },
        {
          id: 'methods@enableRows',
          icon: 'icon_solid_Createorder',
          title: this.$t('激活')
        },
        {
          id: 'methods@disableRows',
          icon: 'icon_solid_Createorder',
          title: this.$t('失效')
        }
      ],
      fieldDefines: [],
      grid: {
        detailTemplate: () => {
          return {
            template: {
              extends: ChildrenGrid
            }
          }
        },
        gridId: '023d6b16-9e86-c353-4b92-789cbbe4548f',
        asyncConfig: {
          url: this.$API.packItem.pagedQuery
        },
        columnData: columnData.call(this),
        allowFiltering: false,
        class: 'pe-edit-grid custom-toolbar-grid'
      }
    }
  ]
}

export default {
  data() {
    return {
      form: {
        data: {},
        rules: {}
      },
      pageConfig: pageConfig.call(this)
    }
  },
  methods: {
    handleClickToolBar(arg) {
      const { toolbar } = arg
      if (toolbar.id.indexOf('methods@') === 0) {
        this[toolbar.id.replace(/^methods@/, '')]?.(arg)
      }
    },
    handleClickCellTool(arg) {
      const { tool } = arg
      if (tool.id.indexOf('methods@') === 0) {
        this[tool.id.replace(/^methods@/, '')]?.(arg)
      }
    },
    handleClickCellTitle({ field, data }) {
      if (field === 'itemCode') {
        this.editRow({ data })
      }
    },
    add() {
      this.$router.push({ name: 'pack-item-edit' })
    },
    editRow({ data }) {
      this.$router.push({
        name: 'pack-item-edit',
        query: {
          id: data.id,
          itemId: data.itemId
        }
      })
    },
    confirmRows(message) {
      return new Promise((c) => {
        const grid = this.$refs.templateRef.getCurrentTabRef().grid
        const records = (grid.getMtechGridRecords || grid.getSelectedRecords)()
        if (records.length === 0) {
          this.$toast({ type: 'warning', content: this.$t('请选择一条数据') })
          c()
          return
        }
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message
          },
          success: () => {
            c(records)
          }
        })
      })
    },
    async enableRows() {
      const rows = await this.confirmRows(this.$t('是否激活选中数据'))
      if (rows) {
        const res = await this.$API.packItem
          .batchUpdate({
            ids: rows.map((e) => e.id),
            statusId: 1
          })
          .catch((e) => console.warn(e))
        if (res?.code === 200) {
          this.$toast({ type: 'success', content: this.$t('操作成功') })
        }
      }
      this.$refs.templateRef.refreshCurrentGridData()
    },
    async disableRows() {
      const rows = await this.confirmRows(this.$t('是否失效选中数据'))
      if (rows) {
        const res = await this.$API.packItem
          .batchUpdate({
            ids: rows.map((e) => e.id),
            statusId: 0
          })
          .catch((e) => console.warn(e))
        if (res?.code === 200) {
          this.$toast({ type: 'success', content: this.$t('操作成功') })
        }
      }
      this.$refs.templateRef.refreshCurrentGridData()
    }
  }
}
