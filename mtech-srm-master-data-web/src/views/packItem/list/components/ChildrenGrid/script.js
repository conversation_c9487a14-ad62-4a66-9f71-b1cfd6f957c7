function columnData() {
  return [
    {
      field: 'itemCode',
      headerText: this.$t('物料'),
      width: 300,
      valueConverter: {
        type: 'function',
        filter: (_, item) => {
          return `${item.itemCode}-${item.itemName}`
        }
      }
    },
    {
      field: 'itemDescription',
      headerText: this.$t('规格型号')
    },
    {
      field: 'unitCount',
      headerText: this.$t('单机用量')
    },
    {
      field: 'baseMeasureUnitName',
      headerText: this.$t('基本计量单位')
    },
    {
      field: 'itemGroupName',
      headerText: this.$t('品项组')
    },
    {
      field: 'oldItemCode',
      headerText: this.$t('旧物料编码')
    }
  ]
}

export default {
  data: function () {
    return {
      // data: {},
      pageConfig: [
        {
          useToolTemplate: false,
          useBaseConfig: false,
          grid: {
            height: 'auto',
            columnData: columnData.call(this),
            asyncConfig: {
              url: this.$API.packItemRel.pagedQuery
            }
          }
        }
      ]
    }
  },
  created() {
    this.pageConfig[0].grid.asyncConfig.rules = [
      {
        field: 'parentItemId',
        type: 'number',
        operator: 'equal',
        value: this.data.itemId
      }
    ]
  }
}
