import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-08-24 15:18:49
 * @LastEditTime: 2022-04-29 18:48:50
 * @LastEditors: OBKoro1
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\organizationMasterData\purchase\config\role.config.js
 */
export const OPTIONS = {
  GROUP_TYPES: []
}

export const PAGE_PLUGIN = [
  {
    // toolbar: ["Add", "Delete"],
    toolbar: [],
    grid: {
      columnData: [
        {
          width: '50',
          type: 'checkbox',
          showInColumnChooser: false
        },
        {
          field: 'organizationName',
          headerText: i18n.t('业务组织名称'),
          width: '350',
          cellTools: [
            {
              id: 'user',
              icon: 'icon_solid_Createproject',
              title: i18n.t('分配下属组织')
            },
            // "edit",
            // "delete",
            'preview'
          ]
        },
        {
          field: 'organizationCode',
          width: '200',
          headerText: i18n.t('业务组织编号')
        },
        {
          field: 'organizationTypeName',
          width: '200',
          headerText: i18n.t('业务组织类型')
        },
        {
          field: 'updateUserName',
          width: '200',
          headerText: i18n.t('更新人')
        },
        {
          field: 'updateTime',
          width: '200',
          headerText: i18n.t('更新时间')
        },
        {
          field: 'statusId',
          headerText: i18n.t('状态'),
          width: '150',
          valueConverter: {
            type: 'map',
            map: {
              0: i18n.t('草稿'),
              1: i18n.t('激活'),
              2: i18n.t('分发'),
              3: i18n.t('失效'),
              '-1': i18n.t('待审核'),
              '-2': i18n.t('准备')
            }
          }
        }
      ],
      asyncConfig: {
        url: `/masterDataManagement/tenant/business-organization/paged-query?BU_CODE=${localStorage.getItem(
          'currentBu'
        )}`
      }
    }
  }
]
export const previewConfig = [
  {
    toolbar: {
      tools: []
    },
    grid: {
      columnData: [
        {
          field: 'organizationName',
          width: '200',
          headerText: i18n.t('组织名称')
        },
        {
          field: 'organizationCode',
          width: '150',
          headerText: i18n.t('组织编码')
        },
        {
          field: 'organizationLevelTypeName',
          headerText: i18n.t('组织类型'),
          width: '150'
        }
      ],
      asyncConfig: {
        url: 'masterDataManagement/tenant/business-org-org-rel/paged-query',
        params: {}
      }
    }
  }
]

export const USER_DIALOG_PAGE_PLUGIN = [
  {
    toolbar: {
      tools: [
        // ["Add", "Delete"],
        ['Add'],
        ['Refresh', 'Setting']
      ]
    },
    grid: {
      columnData: [
        {
          width: '50',
          type: 'checkbox',
          showInColumnChooser: false
        },

        {
          field: 'organizationName',
          width: '200',
          headerText: i18n.t('组织名称')
          // cellTools: ["delete"],
        },
        {
          field: 'organizationCode',
          width: '150',
          headerText: i18n.t('组织编码')
        },
        {
          field: 'organizationLevelTypeName',
          headerText: i18n.t('组织类型'),
          width: '150'
        },
        {
          field: 'organizationTypeCode',
          width: '150',
          headerText: i18n.t('组织类型编码')
        }
      ],
      asyncConfig: {
        url: 'masterDataManagement/tenant/business-org-org-rel/paged-query',
        params: {}
      }
    }
  }
]
export const USER_DIALOG_PAGE_PLUGINS = [
  {
    grid: {
      columnData: [
        {
          width: '50',
          type: 'checkbox',
          showInColumnChooser: false
        },
        {
          field: 'orgName',
          width: '200',
          headerText: i18n.t('组织名称')
        },
        {
          field: 'orgCode',
          width: '150',
          headerText: i18n.t('组织编码')
        },
        {
          field: 'orgLevelTypeName',
          headerText: i18n.t('组织类型'),
          width: '150'
        }
      ],
      asyncConfig: {
        url: 'masterDataManagement/tenant/business-organization/getCompanySite',
        params: {}
      }
    }
  }
]
