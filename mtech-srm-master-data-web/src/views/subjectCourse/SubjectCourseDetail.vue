<!--
 * @Author: your name
 * @Date: 2022-02-16 10:32:01
 * @LastEditTime: 2022-03-24 15:22:08
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\subjectCourse\SubjectCourse.vue
-->
<template>
  <div class="full-height mt-pt-20">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
    <mt-dialog ref="dialog" :header="headerTitle" css-class="create-proj-dialog" :buttons="buttons">
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="accountChartCode" :label="$t('科目表名称')">
          <mt-select
            v-model="ruleForm.accountChartCode"
            :fields="fields.ACCOUNT_CHART"
            :data-source="options.ACCOUNT_CHART"
            :allow-filtering="true"
            :show-clear-button="true"
            :disabled="type !== 'add'"
            :placeholder="$t('请选择科目表类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="accountSubjectCode" :label="$t('科目代码')">
          <mt-input
            v-model="ruleForm.accountSubjectCode"
            :disabled="type !== 'add'"
            :show-clear-button="false"
            type="text"
            :placeholder="$t('请输入科目代码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="accountSubjectName" :label="$t('科目名称')">
          <mt-input
            v-model="ruleForm.accountSubjectName"
            :show-clear-button="false"
            type="text"
            :disabled="type !== 'add'"
            :placeholder="$t('请输入科目名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="accountSubjectType" :label="$t('科目类型')">
          <mt-select
            v-model="ruleForm.accountSubjectType"
            :data-source="options.TYPES"
            :fields="fields.TYPES"
            :allow-filtering="true"
            :show-clear-button="true"
            :disabled="type !== 'add'"
            :placeholder="$t('请选择科目类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="reconciliationAccount" :label="$t('统驭科目')">
          <mt-select
            v-model="ruleForm.reconciliationAccount"
            :data-source="options.YES_OR_NO"
            :allow-filtering="true"
            :show-clear-button="true"
            :disabled="type !== 'add'"
            :placeholder="$t('请选择统驭科目')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="externalCode" :label="$t('第三方编码')">
          <mt-input
            v-model="ruleForm.externalCode"
            :disabled="type !== 'add'"
            :show-clear-button="false"
            type="text"
            :placeholder="$t('请输入第三方编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="accountSubjectDescription" :label="$t('科目描述')">
          <mt-input
            v-model="ruleForm.accountSubjectDescription"
            :readonly="disabled"
            :show-clear-button="false"
            type="text"
            :placeholder="$t('请输入科目描述')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import { PAGE_CONFIG, RULES, OPTIONS, FIELDS } from './config/detail.config'
import { formatRules } from '@/utils/util'
export default {
  data() {
    return {
      options: OPTIONS,
      pageConfig: PAGE_CONFIG,
      rules: RULES,
      fields: FIELDS,

      pageInfo: {
        current: 1,
        size: 10
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {},
      type: 'add' // add edit preview
    }
  },
  computed: {
    headerTitle() {
      if (this.type === 'add') {
        return this.$t('新增')
      }
      if (this.type === 'edit') {
        return this.$t('编辑')
      }
      return this.$t('预览')
    },
    disabled() {
      if (this.type === 'preview') {
        return true
      }
      return false
    }
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const rowSelected = grid.getSelectedRecords()
      if (toolbar.id === 'Add') {
        this.handleAction('add')
        this.subCourseGet()
        this.dictDataGet('TYPES', 'accountSubjectType')
        this.rulesGet('subCourseDetailAddValid')
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDelete(rowSelected)
      }
    },
    handleClickCellTool(e) {
      const { data, tool } = e
      if (tool.id === 'edit') {
        this.handleAction('edit', data)
        this.subCourseGet()
        this.dictDataGet('TYPES', 'accountSubjectType')
        this.rulesGet('subCourseDetailEditValid')
      }
      if (tool.id === 'delete') {
        this.handleDelete([data])
      }
      if (tool.id === 'preview') {
        this.handleAction('preview', data)
        this.subCourseGet()
      }
    },
    handleAction(type, data) {
      this.type = type
      this.$refs.ruleForm.resetFields()
      this.ruleForm = Object.assign({}, data)
      this.$refs.dialog.ejsRef.show()
    },
    handleDelete(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.finance.subCourseDetailDelete(data).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    },
    save() {
      if (this.type === 'preview') {
        this.cancel()
        return false
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.type === 'add') {
            const data = {
              ...this.ruleForm
            }
            this.$API.finance.subCourseDetailAdd(data).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
          if (this.type === 'edit') {
            this.$API.finance.subCourseDetailEdit(this.ruleForm).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
        }
      })
    },
    cancel() {
      this.$refs.dialog.ejsRef.hide()
    },
    rulesGet(type) {
      this.$API.finance[type]().then((res) => {
        this.rules = formatRules(res.data)
      })
    },
    subCourseGet() {
      this.$API.finance.subCourseGet({}).then((res) => {
        this.options = Object.assign({}, this.options, {
          ACCOUNT_CHART: res.data
        })
      })
    },
    dictDataGet(option, dictCode) {
      const query = { dictCode }
      this.$API.dict.TenantDictTypeGet(query).then((res) => {
        this.options[option] = res.data
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/.btn-text {
  color: #4f5b6d;
}
/deep/ .mt-data-grid {
  .status-label {
    font-size: 12px;
    padding: 4px;
    border-radius: 2px;
    &.status-0 {
      color: #9baac1;
      background: rgba(155, 170, 193, 0.1);
    }
    &.status-1 {
      color: #6386c1;
      background: rgba(99, 134, 193, 0.1);
    }
  }
}
/deep/.mt-dialog {
  display: none;
}
</style>
