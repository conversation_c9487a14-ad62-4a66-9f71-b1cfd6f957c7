import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2022-02-16 14:07:34
 * @LastEditTime: 2022-02-16 17:57:29
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\subjectCourse\config\subjectCourse.config.js
 */

export const FIELDS = {
  TYPES: {
    text: 'text',
    value: 'value'
  },
  COUNTRY: {
    text: 'shortName',
    value: 'countryCode'
  }
}
export const RULES = {}

export const OPTIONS = {
  TYPES: [
    { text: i18n.t('运营科目表'), value: 0, cssClass: '' },
    { text: i18n.t('国家科目表'), value: 1, cssClass: '' },
    { text: i18n.t('集团科目表'), value: 2, cssClass: '' }
  ],
  COUNTRY: []
}

export const PAGE_CONFIG = [
  {
    toolbar: ['Add', 'Delete'],
    grid: {
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'accountChartCode',
          headerText: i18n.t('科目表编码'),
          width: '200',
          cellTools: ['edit', 'delete', 'preview']
        },
        {
          field: 'accountChartName',
          width: '200',
          headerText: i18n.t('科目表名称')
        },
        {
          field: 'accountChartType',
          width: '200',
          headerText: i18n.t('科目表类型'),
          valueConverter: {
            type: 'map',
            map: OPTIONS.TYPES
          }
        },
        {
          field: 'accountChartDescription',
          width: '200',
          headerText: i18n.t('科目表描述')
        },
        {
          field: 'countryName',
          width: '200',
          headerText: i18n.t('国家名称')
        },
        {
          field: 'externalCode',
          width: '200',
          headerText: i18n.t('外部编码')
        }
        // {
        //   field: "externalName",
        //   width: "200",
        //   headerText: i18n.t("外部名称"),
        // },
      ],
      asyncConfig: {
        url: '/masterDataManagement/tenant/account-chart/paged-query',
        params: {}
      }
    }
  }
]
