import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2022-02-16 14:47:57
 * @LastEditTime: 2022-03-29 17:55:40
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\subjectCourse\config\detail.config.js
 */

export const FIELDS = {
  ACCOUNT_CHART: {
    text: 'accountChartName',
    value: 'accountChartCode'
  },
  TYPES: {
    text: 'itemName',
    value: 'itemCode'
  }
}
export const RULES = {}

export const OPTIONS = {
  ACCOUNT_CHART: [],
  TYPES: [],
  YES_OR_NO: [
    {
      text: i18n.t('是'),
      value: 1,
      cssClass: ''
    },
    {
      text: i18n.t('否'),
      value: 0,
      cssClass: ''
    }
  ]
}

export const PAGE_CONFIG = [
  {
    toolbar: ['Add', 'Delete'],
    grid: {
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'accountSubjectCode',
          headerText: i18n.t('科目代码'),
          width: '200',
          cellTools: ['edit', 'delete', 'preview']
        },
        {
          field: 'accountSubjectName',
          width: '200',
          headerText: i18n.t('科目名称')
        },
        {
          field: 'accountChartCode',
          width: '200',
          headerText: i18n.t('科目表代码')
        },
        {
          field: 'accountChartName',
          width: '200',
          headerText: i18n.t('科目表名称')
        },
        {
          field: 'accountSubjectTypeName',
          width: '200',
          headerText: i18n.t('科目类型')
        },
        {
          field: 'reconciliationAccount',
          width: '200',
          headerText: i18n.t('统驭科目'),
          valueConverter: {
            type: 'map',
            map: OPTIONS.YES_OR_NO
          }
        },
        {
          field: 'statusId',
          headerText: i18n.t('状态'),
          width: '150',
          valueConverter: {
            type: 'map',
            map: {
              0: i18n.t('草稿'),
              1: i18n.t('激活'),
              2: i18n.t('分发'),
              3: i18n.t('失效'),
              '-1': i18n.t('待审核'),
              '-2': i18n.t('准备')
            }
          }
        },
        {
          field: 'accountSubjectDescription',
          width: '200',
          headerText: i18n.t('科目描述')
        },
        {
          field: 'externalCode',
          width: 'auto',
          headerText: i18n.t('第三方编码')
        }
      ],
      asyncConfig: {
        url: '/masterDataManagement/tenant/account-subject/paged-query',
        params: {}
      }
    }
  }
]
