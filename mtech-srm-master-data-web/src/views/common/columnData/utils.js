// 添加显示字段
export const addArrTextField = (arr, code, name) => {
  arr.forEach((e) => {
    e.__text = `${e[code]} - ${e[name]}`
  })
  return arr
}

export const addArrCodeField = (arr, code) => {
  arr.forEach((e) => {
    e.__text = `${e[code]}`
  })
  return arr
}

// 根据显示字段生成fields
export const makeTextFields = (value) => ({ value, text: '__text' })

// 通用过滤事件
export const filteringByText = function (e) {
  if (!e.text) {
    e.updateData(this.dataSource)
  } else {
    e.updateData(
      this.dataSource.filter(
        ({ __text }) => __text.toUpperCase().indexOf(e.text.toUpperCase()) > -1
      )
    )
  }
}
