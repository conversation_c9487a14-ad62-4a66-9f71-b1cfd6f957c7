<template>
  <div class="notice-hander hander">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      @handleClickToolBar="handleClickToolBar"
      @actionComplete="actionComplete"
      @actionBegin="actionBegin"
    />
    <!-- 导入弹框 -->
    <!-- <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :params-key="paramsKey"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog> -->
  </div>
</template>
<script>
import { rowDataTemp } from './config/variable'
import { pageConfig, NewRowData } from './config'
import { i18n } from '@/main.js'
import { cloneDeep } from 'lodash'
// import UploadExcelDialog from '@/components/Upload/uploadExcelDialog'
export default {
  // components: {
  //   // UploadExcelDialog,
  // },
  data() {
    return {
      i18n,
      pageConfig: pageConfig
      // downTemplateParams: {
      //   pageFlag: false,
      // }, // 导入下载模板参数
      // uploadParams: {}, // 导入文件参数
      // paramsKey: 'importFile', // 导入文件key名
      // // 导入请求接口配置
      // requestUrls: {
      //   templateUrlPre: 'sourceFiles',
      //   templateUrl: 'exportSampleConfirm', // 下载模板接口方法名
      //   uploadUrl: 'importSampleConfirm', // 上传接口方法名
      // },
    }
  },
  methods: {
    // 显示隐藏上传弹框
    // showUploadExcel(flag) {
    //   console.log(flag)
    //   if (flag) {
    //     this.$refs.uploadExcelRef.uploadData = null // 清空数据
    //     this.$refs.uploadExcelRef.fileLength = 0
    //     this.$refs.uploadExcelRef.$refs.uploader.files = []
    //     this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
    //   } else {
    //     this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
    //   }
    // },
    // 上传成功后
    upExcelConfirm() {
      // this.showUploadExcel(false)
      this.$toast({
        content: this.$t('导入成功'),
        type: 'success'
      })
      this.$refs.templateRef.refreshCurrentGridData()
    },
    actionBegin(args) {
      const { requestType, action, rowIndex, rowData } = args
      if (requestType === 'add') {
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        const newRowData = cloneDeep(NewRowData)
        rowDataTemp.push(newRowData)
      } else if (requestType === 'save' && action === 'add') {
        args.data = rowDataTemp[rowDataTemp.length - 1]
        args.rowData = rowDataTemp[rowDataTemp.length - 1]
      } else if (requestType === 'save' && action === 'edit') {
        // 即将保存编辑，保存行编辑后的数据，使数据状态保持
        args.data = rowData
      } else if (requestType === 'beginEdit') {
        console.log('进入编辑之前------------', args)
        if (args.rowData.status === 1 || args.rowData.status === 2) {
          // 非草稿状态不可编辑
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        }
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        rowDataTemp.push(rowData)
      }
    },
    actionComplete(args) {
      if (args.requestType == 'save') {
        const { rowIndex } = args
        const rowData = rowDataTemp[rowDataTemp.length - 1]
        const params = {
          ...rowData
          // ext: JSON.parse(rowData.ext) ? JSON.parse(rowData.ext) : rowData.ext,
          // parameter: JSON.parse(rowData.parameter)
          //   ? JSON.parse(rowData.parameter)
          //   : rowData.parameter
        }
        console.log('保存入参----------------', params)
        const isValid = () => {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        }
        if (!params.companyCode) {
          isValid()
          this.$toast({ content: this.$t('请选择公司编码！'), type: 'warning' })
          return
        }
        if (!params.categoryCode) {
          isValid()
          this.$toast({ content: this.$t('请选择品类编码！'), type: 'warning' })
          return
        }
        if (!params.engineerUserCode) {
          isValid()
          this.$toast({ content: this.$t('请选择SQE工程师！'), type: 'warning' })
          return
        }
        if (!params.qualityUserCode) {
          isValid()
          this.$toast({ content: this.$t('请选择品质部长！'), type: 'warning' })
          return
        }
        if (!params.directorUserCode) {
          isValid()
          this.$toast({ content: this.$t('请选择质量总监！'), type: 'warning' })
          return
        }
        if (args.rowData.status !== 1 && args.rowData.status !== 2) {
          this.$API.sourceFiles.saveSampleConfirm(params).then((res) => {
            if (res.code === 200) {
              this.$refs.templateRef.refreshCurrentGridData()
              this.$toast({
                content: this.$t('保存成功'),
                type: 'success'
              })
            }
          })
        }
      }
    },
    getRow() {
      //获取编辑或者新增的数据
      let currentRecords = this.$refs.templateRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      let row = cloneDeep(this.selectedOtherInfo)
      let info = {}
      currentRecords.some((item) => {
        if (item.addId == this.nowEditRowFlag) {
          Object.assign(item, row)
          info = item
        }
      })
      return info
    },
    // 表头操作
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const selected = grid.getSelectedRecords()
      const idList = []
      if (toolbar.id == 'Effect' || toolbar.id == 'Invalid' || toolbar.id == 'Delete') {
        if (selected.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        } else {
          for (let i = 0; i < selected.length; i++) {
            const item = selected[i]
            if (item.status != 0 && item.status != 2 && toolbar.id == 'Effect') {
              this.$toast({
                content: this.$t('仅可对状态为“草稿”、“失效”状态的数据进行生效'),
                type: 'warning'
              })
              return
            }
            if (item.status != 1 && toolbar.id == 'Invalid') {
              this.$toast({
                content: this.$t('仅可对状态为“有效”状态的数据进行生效'),
                type: 'warning'
              })
              return
            }
            if (item.status != 0 && toolbar.id == 'Delete') {
              this.$toast({
                content: this.$t('仅可删状态为“草稿”状态的数据'),
                type: 'warning'
              })
              return
            }
            idList.push(item.id)
          }
        }
      }
      if (toolbar.id === 'Add') {
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id === 'Import') {
        // 导入
        // this.showUploadExcel(true)
        this.$dialog({
          modal: () => import('@/components/uploadDialog'),
          data: {
            title: this.$t('导入'),
            paramsKey: 'importFile',
            importApi: this.$API.sourceFiles.importSampleConfirm,
            downloadTemplateApi: this.$API.sourceFiles.exportSampleConfirm
          },
          success: () => {
            // 列表数据拼接，公司列表数据回显
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      } else if (toolbar.id === 'Delete') {
        // 删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            this.$API.sourceFiles.deleteSampleConfirm(idList).then((res) => {
              if (res.code === 200) {
                this.$refs.templateRef.refreshCurrentGridData()
                this.$toast({
                  content: this.$t('删除成功'),
                  type: 'success'
                })
              }
            })
          }
        })
      }
      if (toolbar.id === 'Effect' || toolbar.id === 'Invalid') {
        // 生效 & 失效
        console.log('选择', selected)
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认修改选中的数据的状态？')
          },
          success: () => {
            const status = toolbar.id === 'Effect' ? 1 : 2
            this.$API.sourceFiles.updateStatusSampleConfirm({ idList, status }).then((res) => {
              if (res.code === 200) {
                this.$refs.templateRef.refreshCurrentGridData()
                this.$toast({
                  content: this.$t('操作成功'),
                  type: 'success'
                })
              }
            })
          }
        })
      }
    }
  }
}
</script>
<style>
.notice-hander .mt-select-index {
  display: inline-block;
}
</style>
<style lang="scss" scope>
.hander {
  // width: 100%;
  // height: 100%;
  // display: flex;
  // flex-direction: column;
  .e-content {
    height: calc(100vh - 230px) !important;
  }
}
</style>
