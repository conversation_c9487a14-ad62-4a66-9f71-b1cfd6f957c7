import { i18n } from '@/main.js'
import Vue from 'vue'
// import { rowDataTemp } from './variable'
import { ColumnComponent as Component } from './columnComponent'
import { MasterDataSelect } from '@/utils/constants'
// list toolbar
export const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'Import', icon: 'icon_solid_Import', title: i18n.t('导入') },
  { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') },
  { id: 'Effect', icon: 'icon_table_restart', title: i18n.t('生效') },
  { id: 'Invalid', icon: 'icon_solid_Cancel', title: i18n.t('失效') }
]

// 新增行固定数据
export const NewRowData = {
  thePrimaryKey: 'add' + Math.random().toString(36).substring(3, 8),
  field: null,
  name: null,
  remark: null,
  sortNo: null,
  status: null
}

// list column
export const columnData = [
  {
    type: 'checkbox',
    width: '60',
    allowEditing: false
  },
  {
    type: 'index',
    width: '80',
    headerText: i18n.t('序号'),
    allowEditing: false
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司编码'),
    headerTemplate: Component.requiredHeader({
      headerText: i18n.t('公司编码')
    }),
    editTemplate: Component.companySelect
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称'),
    allowEditing: false
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    headerTemplate: Component.requiredHeader({
      headerText: i18n.t('品类编码')
    }),
    editTemplate: Component.categorySelect
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    allowEditing: false
  },
  {
    field: 'engineerUserName',
    headerText: i18n.t('SQE工程师'),
    headerTemplate: Component.requiredHeader({
      headerText: i18n.t('SQE工程师')
    }),
    editTemplate: Component.engineerSelect
  },
  {
    field: 'qualityUserName',
    headerText: i18n.t('品质部长'),
    headerTemplate: Component.requiredHeader({
      headerText: i18n.t('品质部长')
    }),
    editTemplate: Component.qualitySelect
  },
  {
    field: 'directorUserName',
    headerText: i18n.t('质量总监'),
    headerTemplate: Component.requiredHeader({
      headerText: i18n.t('质量总监')
    }),
    editTemplate: Component.directorSelect
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      fields: { text: 'text', value: 'value' },
      map: [
        { text: i18n.t('草稿'), value: 0, cssClass: ['btns warning'] },
        { text: i18n.t('有效'), value: 1, cssClass: ['btns warning'] },
        { text: i18n.t('失效'), value: 2, cssClass: ['btns warning'] }
      ]
    },
    editTemplate: function () {
      return {
        template: Vue.component('statusSelect', {
          template: `<span>{{ dataSource.filter(i => i.value === data.status)[0] ? dataSource.filter(i => i.value === data.status)[0]['text'] : '' }}</span>`,
          data() {
            return {
              dataSource: [
                { text: i18n.t('草稿'), value: 0 },
                { text: i18n.t('有效'), value: 1 },
                { text: i18n.t('失效'), value: 2 }
              ]
            }
          }
        })
      }
    },
    allowEditing: false
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    allowEditing: false
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    allowEditing: false
  },
  {
    field: 'updateTime',
    headerText: i18n.t('修改时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    allowEditing: false
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('最后更新人'),
    allowEditing: false
  }
]

// list pageConfig
export const pageConfig = [
  {
    toolbar: toolbar,
    gridId: '2879db4e-6ad4-4480-99ce-dcf1339f7bb2',
    activatedRefresh: false,
    grid: {
      editSettings: {
        allowAdding: true,
        allowEditing: true,
        allowDeleting: true,
        mode: 'Normal', // 默认normal模式
        allowEditOnDblClick: true,
        newRowPosition: 'Top'
      },
      height: 'auto',
      columnData,
      asyncConfig: {
        url: '/sourcing/tenant/sampleConfirmationConfig/pageQuery',
        params: {}
      }
    }
  }
]
