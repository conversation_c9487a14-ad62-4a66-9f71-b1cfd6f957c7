import { i18n } from '@/main.js'
import { createEditInstance } from '@/utils/ej/dataGrid'
import {
  addArrTextField,
  makeTextFields
  // filteringByText,
  // addArrCodeField,
} from '@/views/common/columnData/utils'
import { useFiltering } from '@/utils/ej/select'
// import { fieldLinkUpdate } from '@/utils/ej/dataGrid/utils'

/*
 * @Author: your name
 * @Date: 2021-09-18 14:12:28
 * @LastEditTime: 2022-01-12 11:07:47
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\category\config\categoryEdit.config.js
 */
import Vue from 'vue'

export const TREE_PLUGIN = {
  dataSource: [],
  id: 'id',
  text: 'name',
  child: 'children',
  selected: 'isSelected'
}

export const TREE_DETAIL_PLUGIN = {
  nodeTemplate: function () {
    return {
      template: Vue.component('common', {
        template: `
          <div class="action-boxs">
            <mt-tag type="primary" size="mini" style="width:34px" squareness>{{ $t('启用') }}</mt-tag>
            <div>{{mtData.categoryCode+"-"+mtData.name}}</div>
          </div>
        `,
        data() {
          return { data: {} }
        },
        props: {
          mtData: {
            type: Object,
            default: () => {}
          }
        }
      })
    }
  },
  dataSource: [],
  id: 'id',
  text: 'name',
  child: 'children',
  selected: 'isSelected'
}

export const PAGE_PLUGIN = [
  {
    title: i18n.t('关联品类'),
    treeGrid: {
      columnData: [
        {
          field: 'orgName',
          headerText: i18n.t('关联品类层级'),
          width: '200'
        },
        {
          field: 'id',
          headerText: i18n.t('关联品类项'),
          template: function () {
            return {
              template: Vue.component('relate', {
                //$parent.$parent.$parent.$emit('relate', data);
                template: `
                  <div class="mt-pa-10 add-btn" @click="emitClick">
                    <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />关联品类
                  </div>
                `,
                data() {
                  return {
                    data: {}
                  }
                },
                methods: {
                  emitClick() {
                    console.log(this.$parent)
                  }
                }
              })
            }
          }
        }
      ],
      allowPaging: false,
      asyncConfig: {
        url: '/masterDataManagement/tenant/category-rel/query-by-category',
        methods: 'get',
        params: {},
        recordsPosition: 'data'
      },
      childMapping: 'children'
    }
  },
  {
    title: i18n.t('物料/品项'),
    toolbar: [],
    grid: {
      columnData: [
        {
          field: 'standardStationName',
          headerText: i18n.t('物料/品项编号'),
          width: '200'
        },
        {
          field: 'standardStationCode',
          width: '200',
          headerText: i18n.t('物料/品项名称')
        },
        {
          field: 'ShipAddress',
          width: '200',
          headerText: i18n.t('规格型号')
        },
        {
          field: 'ShipAddress',
          width: '200',
          headerText: i18n.t('旧物料编号')
        },
        {
          field: 'Freight',
          headerText: i18n.t('基本计量单位')
        }
      ],
      asyncConfig: {
        url: '/masterDataManagement/tenant/standard-station/paged-query',
        defaultRules: [
          {
            field: 'organizationId',
            operator: 'equal',
            value: ''
          }
        ],
        params: {}
      }
    }
  },
  {
    title: i18n.t('SKU列表'),
    grid: {
      columnData: [
        {
          field: 'standardStationName',
          headerText: i18n.t('SKU编号'),
          width: '200'
        },
        {
          field: 'standardStationCode',
          width: '200',
          headerText: i18n.t('SKU名称')
        },
        {
          field: 'ShipAddress',
          width: '200',
          headerText: i18n.t('规格型号')
        },
        {
          field: 'Freight',
          headerText: i18n.t('制造商')
        }
      ],
      asyncConfig: {
        url: '/masterDataManagement/tenant/standard-station/paged-query',
        defaultRules: [
          {
            field: 'organizationId',
            operator: 'equal',
            value: ''
          }
        ],
        params: {}
      }
    }
  }
]

export const OPTIONS = {
  YES_OR_NO: [
    {
      text: i18n.t('是'),
      value: 1
    },
    {
      text: i18n.t('否'),
      value: 0
    }
  ]
}

export const DROPDOWN_TREE = {
  dataSource: [],
  value: 'id',
  text: 'name',
  child: 'children'
}

export const MAT_PAGE_PLUGIN = [
  {
    grid: {
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'itemCode',
          headerText: i18n.t('物料/品项编号'),
          width: '350'
        },
        {
          field: 'itemName',
          width: '200',
          headerText: i18n.t('物料/品项名称')
        },
        {
          field: 'itemDescription',
          width: '200',
          headerText: i18n.t('规格型号')
        },
        {
          field: 'itemOldCode',
          width: '200',
          headerText: i18n.t('旧物料编号')
        },
        {
          field: 'itemBaseMesureUnit',
          width: '200',
          headerText: i18n.t('基本计量单位')
        }
      ],
      dataSource: [],
      // asyncConfig: {
      //   url: "/masterDataManagement/tenant/category-item/item-without-bind",
      //   params: {},
      // },
      asyncConfig: {}
    }
  }
]

export const FORMULA_ADD = () => {
  return [
    {
      useToolTemplate: false,
      toolbar: {
        useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
        tools: [
          [
            {
              id: 'add',
              icon: 'icon_solid_Submit',
              title: i18n.t('新建')
            },
            {
              id: 'del',
              icon: 'icon_solid_Submit',
              title: i18n.t('删除')
            }
          ]
        ]
      },
      gridId: '85128e0e-0077-4251-b27a-2c4538fa31bb',
      grid: {
        columnData: [],
        editSettings: {
          allowEditing: true,
          allowAdding: true,
          allowDeleting: true,
          mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
          showConfirmDialog: false,
          showDeleteConfirmDialog: false,
          newRowPosition: 'Bottom'
        },
        dataSource: [],
        asyncConfig: {}
      }
    }
  ]
}

export const columnDataFive = ({
  // categoryCodeData,
  categoryPagedQuery
  // self
}) =>
  // bus
  {
    // 税率
    // const categoryCodeList = addArrCodeField(
    //   categoryCodeData,
    //   "categoryCode",
    //   "categoryName",
    //   "categoryCodeId"
    // );
    const editInstance = createEditInstance()
      .onChange(async (ctx, { field }, event) => {
        if (['categoryCode'].includes(field)) {
          if (event.itemData) {
            ctx.setValueByField('categoryName', event.itemData.categoryName)
            ctx.setValueByField('categoryCode', event.itemData.categoryCode)
            ctx.setValueByField('categoryId', event.itemData.id)
          } else {
            ctx.setValueByField('categoryName', null)
            ctx.setValueByField('categoryCode', null)
            ctx.setValueByField('categoryId', null)
          }
        }
      })
      .onInput(async (ctx, { field }) => {
        if (field === 'categoryCode') {
          // fieldLinkUpdate(ctx, field, {
          //   categoryCode: "categoryCode",
          //   categoryId: "categoryId",
          //   categoryName: "categoryName",
          // });
        }
      })
    let categoryDataSource = []
    async function categoryPagedQueryEx(value) {
      let params = {
        page: { current: 1, size: 20 }
      }
      if (value) {
        params = {
          ...params,
          condition: 'or',
          rules: [
            {
              field: 'categoryName',
              type: 'string',
              operator: 'contains',
              value
            },
            {
              field: 'categoryCode',
              type: 'string',
              operator: 'contains',
              value
            }
          ]
        }
      }
      const res = await categoryPagedQuery(params).catch(() => {})
      const records = res?.data?.records || []
      return addArrTextField(records, 'categoryCode', 'categoryName')
    }
    const queryCategoryDataSource = async (...arg) => {
      const records = await categoryPagedQueryEx(...arg)
      mergeCategoryDataSource(records)
      return records
    }

    function mergeCategoryDataSource(records) {
      addArrTextField(records, 'categoryCode', 'categoryName')
      categoryDataSource = records
      initCategoryDataSource()
    }

    function initCategoryDataSource() {
      // editInstance.setOptions("categoryName", {
      //   dataSource: categoryDataSource,
      // });
      editInstance.setOptions('categoryCode', {
        dataSource: categoryDataSource
      })
    }
    // const busFields = ["categoryName", "categoryCode", "categoryId"];
    // busFields.forEach((busField) => {
    //   bus.$on(`${busField}Change`, async (data) => {
    //     if (busField === "categoryCode") {
    //       mergeCategoryDataSource([
    //         {
    //           categoryName: data.categoryName,
    //           categoryCode: data.categoryCode,
    //           id: data.id,
    //         },
    //       ]);
    //       editInstance.setValueByField("categoryName", data.categoryName);
    //       editInstance.setValueByField("categoryCode", data.categoryCode);
    //       editInstance.setValueByField("categoryId", data.id);
    //     }
    //   });
    // });
    return [
      {
        width: '50',
        type: 'checkbox'
      },
      {
        headerText: i18n.t('品类节点编码'),
        field: 'parentOrgCategoryCode',
        allowEditing: false
      },
      {
        headerText: i18n.t('品类节点名称'),
        field: 'parentOrgCategoryName',
        allowEditing: false
      },
      {
        headerText: i18n.t('关联品类节点编码'),
        field: 'categoryCode',
        edit: editInstance.create({
          getEditConfig: ({ rowData }) => ({
            type: 'select',
            'allow-filtering': true,
            'show-clear-button': true,
            // fields: makeTextFields("categoryCode"),
            placeholder: i18n.t('请选择关联品类编码'),
            // dataSource: categoryCodeList,
            created: async function () {
              // 品类是搜索带出的,非一次性加载
              // 品类名称需要和品类编码的 dataSource 保持一致
              // 品类名称需要和品类编码保持互相联动
              const categoryCode = rowData.categoryCode
              initCategoryDataSource()
              await queryCategoryDataSource().catch((err) => console.warn(err))
              if (!categoryDataSource.find((e) => e.categoryCode === categoryCode)) {
                await queryCategoryDataSource(categoryCode).catch((err) => console.warn(err))
              }
              setTimeout(() => {
                editInstance.setValueByField('categoryCode', categoryCode)
              }, 0)
            },
            filtering: useFiltering(async function (e) {
              e.updateData(await queryCategoryDataSource(e.text))
            }),
            fields: makeTextFields('categoryCode'),
            dataSource: []
            // filtering: useFiltering(filteringByText),
          })
        })
      },
      {
        headerText: i18n.t('关联品类节点名称'),
        field: 'categoryName',
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            readonly: true,
            disabled: true
          })
        })
      }
    ]
  }
export const PAGE_CONFIG = [
  {
    // toolbar: ["Add","Delete"],
    // toolbar: ["Add", "Delete", "active"],
    toolbar: [
      'Add',
      'Delete',
      {
        id: 'Save',
        icon: 'icon_solid_Createproject',
        title: i18n.t('保存')
      }
    ],
    grid: {
      editSettings: {
        allowEditing: true,
        allowAdding: true,
        allowDeleting: true,
        mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
        showConfirmDialog: false,
        showDeleteConfirmDialog: false,
        newRowPosition: 'Top'
      },
      allowSorting: false,
      allowFiltering: false,
      dataSource: [],
      frozenColumns: 2,
      columnData: []
    }
  }
]
