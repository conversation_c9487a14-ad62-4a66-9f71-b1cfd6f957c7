import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-09-14 15:42:57
 * @LastEditTime: 2022-01-06 09:42:19
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\permission\config\category-tree.config.js
 */
export const PAGE_CONFIG = [
  {
    toolbar: ['Add', 'Delete'],
    grid: {
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'typeCode',
          headerText: i18n.t('品类树编号'),
          width: '200',
          cellTools: ['edit', 'delete', 'preview']
        },
        {
          field: 'typeName',
          width: '200',
          headerText: i18n.t('品类树名称')
        },
        {
          field: 'updateUserName',
          width: '150',
          headerText: i18n.t('更新人')
        },
        {
          field: 'updateTime',
          headerText: i18n.t('更新时间')
          // valueConverter: {
          //   type: "date",
          // },
        }
      ],
      asyncConfig: {
        url: '/masterDataManagement/tenant/category-type/paged-query',
        params: {}
      }
    }
  }
]

export const FIELDS = {
  COMPANY: {
    text: 'orgName',
    value: 'id'
  }
}
export const RULES = {}

export const OPTIONS = {
  COMPANY: []
}
