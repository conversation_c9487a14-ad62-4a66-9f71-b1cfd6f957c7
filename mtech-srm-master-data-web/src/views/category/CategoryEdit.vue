<!--
 * @Author: your name
 * @Date: 2021-09-17 17:09:35
 * @LastEditTime: 2022-02-28 17:55:19
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\category\CategoryEdit.vue
-->
<template>
  <div class="flex justify-between full-height mt-pt-20">
    <div v-if="hasOrgTree" class="full-height bg-white org-tree-wrapper" style="height: 800px">
      <org-tree @select="orgTreeNodeSelect" @hideTree="hasOrgTree = false"></org-tree>
    </div>

    <div class="full-height ctx-wrapper">
      <div class="ctx-top-wrapper bg-white">
        <div class="flex justify-between items-center mt-px-10 header-tabs">
          <mt-tabs
            :e-tab="false"
            :data-source="dataSource"
            overflow-mode="Popup"
            :wdith="600"
            @handleSelectTab="cateTabSelected"
          ></mt-tabs>
          <div v-if="hasOrgTree">
            <mt-button css-class="e-flat" :is-primary="true" @click="cateCopy">{{
              $t('复制品类')
            }}</mt-button>
          </div>
        </div>

        <div class="mt-pa-20">
          <div v-if="hasOrgTree">
            <mt-switch
              class="item"
              v-model="followUpLevel"
              on-:label="$t('跟随上级')"
              off-:label="$t('跟随上级')"
              :active-value="1"
              :inactive-value="0"
              @change="followUpLevelChange"
            ></mt-switch>
          </div>
          <div class="flex justify-between mt-py-20">
            <div class="form-item">
              <div class="form-item-label">{{ $t('品类树编号') }}</div>
              <mt-input
                v-model="cateTreeInfo.typeCode"
                :readonly="true"
                :show-clear-button="false"
                type="text"
              ></mt-input>
            </div>
            <div class="form-item">
              <div class="form-item-label">{{ $t('品类树名称') }}</div>
              <mt-input
                v-model="cateTreeInfo.typeName"
                :readonly="true"
                :show-clear-button="false"
                type="text"
              ></mt-input>
            </div>
            <!-- <div class="form-item">
              <div class="form-item-label">{{ $t("应用领域") }}</div>
              <mt-input
                v-model="cateTreeInfo.domainNames"
                :readonly="true"
                :show-clear-button="false"
                type="text"
              ></mt-input>
            </div> -->
            <div class="form-item">
              <div class="form-item-label">{{ $t('更新人') }}</div>
              <mt-input
                v-model="cateTreeInfo.updateUserName"
                :readonly="true"
                :show-clear-button="false"
                type="text"
              ></mt-input>
            </div>
            <div class="form-item">
              <div class="form-item-label">{{ $t('更新时间') }}</div>
              <mt-input
                v-model="cateTreeInfo.updateTime"
                :readonly="true"
                :show-clear-button="false"
                type="text"
              ></mt-input>
            </div>
          </div>
          <div>
            <div class="form-item-label">{{ $t('备注') }}</div>
            <mt-input
              v-model="cateTreeInfo.typeDescription"
              :readonly="true"
              :show-clear-button="false"
              type="text"
              :multiline="true"
              :rows="2"
            ></mt-input>
          </div>
        </div>
      </div>

      <div v-if="!followUpLevelChangeFlag" class="ctx-bot-wrapper mt-mt-10">
        <category-detail
          ref="cateDetail"
          :org-info="orgNodeSelectInfo"
          :tab-info="cateTabSelectInfo"
          :has-org-tree="hasOrgTree"
        ></category-detail>
      </div>
    </div>

    <mt-dialog
      ref="dialog"
      :header="$t('复制品类')"
      css-class="create-proj-dialog"
      :buttons="buttons"
    >
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="fromOrganizationId" :label="$t('组织名称')">
          <mt-DropDownTree
            id="multi-template"
            v-model="ruleForm.fromOrganizationId"
            :fields="dropDownTree"
            :item-template="iTemplate"
            :allow-filtering="true"
            :placeholder="$t('请选择组织名称')"
          ></mt-DropDownTree>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import { DROPDOWN_TREE } from './config/categoryEdit.config'
import OrgTree from './components/OrgTree.vue'
import CategoryDetail from './components/CategoryDetail.vue'
export default {
  components: {
    OrgTree,
    CategoryDetail
  },
  data() {
    return {
      dropDownTree: DROPDOWN_TREE,
      dataSource: [],
      hasOrgTree: true,

      followUpLevel: 1,
      orgNodeSelectInfo: {},
      cateTabSelectInfo: {},
      cateTreeInfo: {},
      ruleForm: {
        fromOrganizationId: []
      },
      rules: {},

      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      iTemplate() {
        return {
          template: Vue.component('iTemplate', {
            template: `
              <div class="dt-wrapper">
                <span :class="'label-tree label-' + labelClass[data.oegLevelTypeName]">{{ data.oegLevelTypeName }}</span>
                <span>{{ data.name }}</span>
              </div>
            `,
            data() {
              return {
                data: {},
                labelClass: {
                  集团: 'group',
                  事业群: 'team',
                  公司: 'company',
                  子公司: 'child-company'
                }
              }
            }
          })
        }
      }
    }
  },
  computed: {
    followUpLevelChangeFlag() {
      return this.followUpLevel
    }
  },
  mounted() {
    this.cateTypeQueryNoPages()
  },
  methods: {
    cateTypeQueryNoPages() {
      // this.$refs.tabLeft.ejsRef.ej2Instances.headerPlacement = "left";
      this.$API.category.cateTypeQueryNoPages({}).then((res) => {
        this.dataSource = res.data.map((e) => {
          return {
            title: e.typeName,
            typeName: e.typeName,
            id: e.id,
            typeCode: e.typeCode
          }
        })
        this.cateTabSelected(0)
      })
    },
    orgTreeNodeSelect(nodeInfo) {
      this.orgNodeSelectInfo = nodeInfo
      if (this.cateTabSelectInfo.id) {
        this.cateTreeInfoGet()
      }
    },
    cateTabSelected(e) {
      this.cateTabSelectInfo = this.dataSource[e]
      if (this.orgNodeSelectInfo.id) {
        this.cateTreeInfoGet()
      }
    },
    cateTreeInfoGet() {
      const query = {
        baseTypeId: this.cateTabSelectInfo.id,
        organizationId: this.orgNodeSelectInfo.id
      }
      this.$API.category
        .cateTreeInfoGet(query)
        .then((res) => {
          this.cateTreeInfo = res.data
          this.followUpLevel = res.data.followParent
        })
        .catch(() => {
          this.cateTreeInfo = {}
        })
    },
    followUpLevelChange(val) {
      const data = {
        followParent: val,
        baseTypeId: this.cateTabSelectInfo.id,
        organizationId: this.orgNodeSelectInfo.id
      }
      this.$API.category
        .followUpLevelChange(data)
        .then(() => {
          this.followUpLevelChangeFlag = val
        })
        .catch(() => {
          this.followUpLevelChangeFlag = !val
          this.followUpLevel = !val
        })
    },
    cateCopy() {
      this.treeDataGet()
      this.$refs.dialog.ejsRef.show()
    },
    treeDataGet() {
      this.$API.category.treeDataGet({}).then((res) => {
        this.dropDownTree = Object.assign({}, this.dropDownTree, {
          dataSource: res.data
        })
      })
    },
    cancel() {
      this.$refs.dialog.ejsRef.hide()
    },
    save() {
      const data = {
        categoryTypeId: this.cateTabSelectInfo.id,
        fromOrganizationId: this.ruleForm.fromOrganizationId.join(''),
        toOrganizationId: this.orgNodeSelectInfo.id
      }
      this.$API.category.cateCopy(data).then(() => {
        this.cateTreeInfoGet()
        this.$refs.cateDetail.treeDataGet()
        this.cancel()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.org-tree-wrapper {
  width: 343px;
  overflow: auto;
  flex-shrink: 0;
  margin-right: 20px;
}
.ctx-wrapper {
  flex: 1;
  .ctx-top-wrapper {
    height: 290px;
    border: 1px solid rgba(232, 232, 232, 1);
    .header-tabs {
      height: 60px;
      border-bottom: 1px solid rgba(232, 232, 232, 1);
    }
    .form-item {
      width: calc(25% - 10px);
      min-width: 120px;
    }
    .form-item-label {
      font-size: 14px;
      color: #9a9a9a;
    }
  }

  .ctx-bot-wrapper {
    height: calc(100% - 300px);
  }
}

::v-deep .mt-tabs {
  background: none;
}
/deep/#tabElment {
  height: 36px !important;
}
/deep/.mt-dialog {
  display: none;
}
</style>

<style lang="scss">
.dt-wrapper {
  .label-tree {
    font-family: PingFangSC;
    display: inline-block;
    width: 38px;
    height: 16px;
    line-height: 16px;
    font-size: 12px;
    text-align: center;
    border-radius: 2px;
  }
  .label-group {
    background: rgba(154, 170, 193, 0.1);
    color: #9aaac1;
  }
  .label-team {
    background: rgba(236, 242, 250, 1);
    color: #4e86cb;
  }
  .label-company {
    background: rgba(78, 134, 203, 0.1);
    color: #5a86c6;
  }
  .label-child-company {
    background: rgba(255, 154, 0, 0.1);
    color: #f99e00;
  }
}
</style>
