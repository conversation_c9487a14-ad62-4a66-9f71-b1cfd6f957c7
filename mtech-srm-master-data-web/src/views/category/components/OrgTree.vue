<!--
 * @Author: your name
 * @Date: 2021-09-17 17:10:40
 * @LastEditTime: 2022-03-09 10:05:41
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\category\components\OrgTree.vue
-->
<template>
  <div>
    <mt-common-tree :fields="treePlugin" un-button @nodeSelected="nodeSelected"></mt-common-tree>
  </div>
</template>

<script>
const labelClass = {
  集团: 'group',
  事业群: 'team',
  公司: 'company',
  子公司: 'child-company',
  板块: 'team'
}
function handleTreeData(data) {
  for (let i = 0; i < data.length; i++) {
    const el = data[i]

    el.label = el.oegLevelTypeName
    el.labelClass = `label-tree label-${labelClass[el.oegLevelTypeName]}`
    if (el.parentId === '0') {
      // el.expanded = "expanded";
      el.isSelected = true
    }
    if (el.children) {
      handleTreeData(el.children)
    }
  }
  return data
}
import { TREE_PLUGIN } from '../config/categoryEdit.config'
export default {
  data() {
    return {
      treePlugin: TREE_PLUGIN,

      nodeSelectInfo: {}
    }
  },
  mounted() {
    this.treeDataGet()
  },
  methods: {
    treeDataGet() {
      this.$API.category.treeDataGet({}).then((res) => {
        if (!res.data[0].children?.length) {
          this.$emit('hideTree')
        }
        this.treePlugin = Object.assign({}, this.treePlugin, {
          dataSource: handleTreeData(res.data)
        })
        const rootNode = this.treePlugin.dataSource.filter((e) => e.isSelected)
        this.nodeSelected({ nodeData: rootNode[0] })
      })
    },
    nodeSelected(e) {
      if (e) {
        this.nodeSelectInfo = e.nodeData
        this.$emit('select', this.nodeSelectInfo)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .label-tree {
  font-family: PingFangSC;
  display: inline-block;
  width: 38px;
  height: 16px;
  line-height: 16px;
  font-size: 12px;
  text-align: center;
  border-radius: 2px;
}
/deep/ .label-group {
  background: rgba(154, 170, 193, 0.1);
  color: #9aaac1;
}
/deep/ .label-team {
  background: rgba(236, 242, 250, 1);
  color: #4e86cb;
}
/deep/ .label-company {
  background: rgba(78, 134, 203, 0.1);
  color: #5a86c6;
}
/deep/ .label-child-company {
  background: rgba(255, 154, 0, 0.1);
  color: #f99e00;
}
</style>
