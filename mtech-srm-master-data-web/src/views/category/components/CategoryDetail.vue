<!--
 * @Author: your name
 * @Date: 2021-09-22 13:43:10
 * @LastEditTime: 2022-03-09 10:06:05
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\category\components\CategoryDetail.vue
-->
<template>
  <div class="flex justify-between full-height" style="height: 500px">
    <div class="left-wrapper full-height bg-white">
      <div class="mt-pa-10 add-btn" @click="dialogOpen('add', { parentId: 0 })">
        <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('新增根节点') }}
      </div>
      <div class="DS-search">
        <MtIcon class="DS-search-icon" name="icon_input_search" />
        <mt-input
          style="width: 80%"
          class="input-item"
          type="text"
          :placeholder="$t('请输入搜索关键字')"
          v-model="inputText"
          @input="onInput"
        ></mt-input>
      </div>
      <mt-common-tree
        :fields="treePlugin"
        un-button
        @onButton="onButton"
        @nodeSelected="nodeSelected"
      ></mt-common-tree>
    </div>
    <div class="right-wrapper full-height bg-white">
      <div v-show="treePluginDataSource.length" class="mt-pa-20 detail-info">
        <div class="flex justify-between">
          <div class="form-item">
            <div class="form-item-label">{{ $t('编号') }}</div>
            <mt-input
              v-model="nodeInfo.categoryCode"
              :readonly="true"
              :show-clear-button="false"
              type="text"
            ></mt-input>
          </div>
          <div class="form-item">
            <div class="form-item-label">{{ $t('名称') }}</div>
            <mt-input
              v-model="nodeInfo.name"
              :readonly="true"
              :show-clear-button="false"
              type="text"
            ></mt-input>
          </div>
          <div class="form-item">
            <div class="form-item-label">{{ $t('是否末级') }}</div>
            <mt-select
              v-model="nodeInfo.leafNode"
              :readonly="true"
              :data-source="options.YES_OR_NO"
              :show-clear-button="false"
              :allow-filtering="true"
              :placeholder="$t('请选择是否末级节点')"
            ></mt-select>
          </div>
        </div>
        <div class="flex justify-between mt-mt-10">
          <div class="form-item">
            <div class="form-item-label">{{ $t('最近更新人') }}</div>
            <mt-input
              v-model="nodeInfo.updateUserName"
              :readonly="true"
              :show-clear-button="false"
              type="text"
            ></mt-input>
          </div>
          <div class="form-item">
            <div class="form-item-label">{{ $t('最近更新时间') }}</div>
            <mt-input
              v-model="nodeInfo.updateTime"
              :readonly="true"
              :show-clear-button="false"
              type="text"
            ></mt-input>
          </div>
          <div class="form-item"></div>
        </div>
      </div>

      <div v-show="treePluginDataSource.length" class="detail-tabs">
        <detail-tabs
          ref="detailTabs"
          :node-info="nodeInfo"
          :org-info="orgInfo"
          :tab-info="tabInfo"
          :has-org-tree="hasOrgTree"
        ></detail-tabs>
        <!-- <mt-template-page
          ref="tepPage"
          :template-config="pageConfig"
          @handleSelectTab="handleSelectTab"
          @relate="cateRelate"
        ></mt-template-page> -->
      </div>
    </div>

    <mt-dialog ref="dialog" :header="headerTitle" css-class="create-proj-dialog" :buttons="buttons">
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="parentName" :label="$t('父节点名称')">
          <mt-input
            v-model="ruleForm.parentName"
            :readonly="true"
            :show-clear-button="false"
            type="text"
            :placeholder="$t('请输入父节点名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="leafNode" :label="$t('是否末级节点')">
          <mt-select
            v-model="ruleForm.leafNode"
            :readonly="type !== 'add'"
            :data-source="options.YES_OR_NO"
            :show-clear-button="false"
            :allow-filtering="true"
            :placeholder="$t('请选择是否末级节点')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="categoryCode" :label="$t('编号')">
          <mt-input
            v-model="ruleForm.categoryCode"
            :readonly="readonly"
            :show-clear-button="false"
            type="text"
            :placeholder="$t('请输入编号')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="categoryName" :label="$t('名称')">
          <mt-input
            v-model="ruleForm.categoryName"
            :readonly="readonly"
            :show-clear-button="false"
            type="text"
            :placeholder="$t('请输入名称')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import {
  TREE_DETAIL_PLUGIN,
  // PAGE_PLUGIN,
  OPTIONS
} from '../config/categoryEdit.config'
import { formatRules } from '@/utils/util'
import DetailTabs from './DetailTabs.vue'
import { i18n } from '@/main.js'
import { utils } from '@mtech-common/utils'

const actions = [
  { text: i18n.t('新增品类'), value: 'Add' },
  { text: i18n.t('编辑品类'), value: 'Edit' },
  { text: i18n.t('删除'), value: 'Delete' }
]
const actionsNoAdd = [
  { text: i18n.t('编辑品类'), value: 'Edit' },
  { text: i18n.t('删除'), value: 'Delete' }
]
function handleTreeData(data) {
  for (let i = 0; i < data.length; i++) {
    const el = data[i]

    if (el.parentId === '0') {
      // el.expanded = "expanded";
      el.isSelected = true
    }
    el.setOperation = el.leafNode ? actionsNoAdd : actions
    if (el.children) {
      handleTreeData(el.children)
    }
  }
  return data
}

let currentNode
function nodeInfoGet(data, val) {
  for (let i = 0; i < data.length; i++) {
    const el = data[i]

    if (el.id === val) {
      currentNode = el
      break
    } else if (el.children) {
      nodeInfoGet(el.children, val)
    }
  }
  return currentNode
}

export default {
  components: { DetailTabs },
  props: {
    orgInfo: {
      required: true,
      type: Object,
      default() {
        return {}
      }
    },
    tabInfo: {
      required: true,
      type: Object,
      default() {
        return {}
      }
    },
    hasOrgTree: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      inputText: '',
      treePlugin: TREE_DETAIL_PLUGIN,
      // pageConfig: PAGE_PLUGIN,
      options: OPTIONS,

      rules: {},
      ruleForm: {},
      nodeInfo: {},

      // currentTab: 0,
      type: 'add',
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      treePluginDataSource: []
    }
  },
  computed: {
    headerTitle() {
      if (this.type === 'add') {
        return this.$t('新增')
      }
      if (this.type === 'edit') {
        return this.$t('编辑')
      }
      return this.$t('预览')
    },
    readonly() {
      if (this.type === 'preview') {
        return true
      }
      return false
    }
  },
  watch: {
    orgInfo() {
      this.treeDataGet()
    },
    tabInfo() {
      this.treeDataGet()
    }
  },
  mounted() {
    this.treeDataGet()
  },
  methods: {
    filterTree(nodes, query) {
      for (let index = 0; index < nodes.length; index++) {
        if (nodes[index].children) {
          this.filterTree(nodes[index].children, query)
        } else {
          if (
            !nodes[index].categoryName.includes(query) &&
            !nodes[index].categoryCode.includes(query) &&
            !nodes[index].name.includes(query)
          ) {
            nodes.splice(index, 1)
            index--
          }
        }
        if (
          nodes[index] != undefined &&
          nodes[index].children != undefined &&
          nodes[index].children.length == 0
        ) {
          if (
            !nodes[index].categoryName.includes(query) &&
            !nodes[index].categoryCode.includes(query) &&
            !nodes[index].name.includes(query)
          ) {
            nodes.splice(index, 1)
            index--
          }
        }
      }
    },
    onInput(e) {
      let dataSource = utils.cloneDeep(this.treePluginDataSource)
      this.filterTree(dataSource, e)
      this.treePlugin.dataSource = dataSource
    },
    treeDataGet() {
      if (this.tabInfo?.id && this.orgInfo?.id) {
        const query = {
          categoryTypeId: this.tabInfo.id,
          organizationId: this.orgInfo.id
        }
        this.$API.category.cateTreeNodeDetailGet(query).then((res) => {
          this.treePlugin = Object.assign({}, this.treePlugin, {
            dataSource: handleTreeData(res.data || [])
          })
          const rootNode = this.treePlugin.dataSource.filter((e) => e.isSelected)
          this.nodeSelected({ nodeData: rootNode[0] })
          this.treePluginDataSource = utils.cloneDeep(this.treePlugin.dataSource)
        })
      }
    },
    onButton(e) {
      const { onBtn, id, parentId, name, leafNode, categoryCode } = e

      if (onBtn.value === 'Add') {
        const nodeInfo = {
          parentId: id,
          parentName: name
        }
        this.dialogOpen('add', nodeInfo)
      }
      if (onBtn.value === 'Edit') {
        let parentName = ''
        if (parentId) {
          const parentNode = nodeInfoGet(this.treePlugin.dataSource, parentId)
          parentName = parentNode.name
        }
        const nodeInfo = {
          parentName,
          parentId,
          id,
          leafNode,
          categoryCode,
          categoryName: name
        }
        this.dialogOpen('edit', nodeInfo)
      }
      if (onBtn.value === 'Delete') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            const data = { id }
            this.$API.category.cateTreeNodeDelete(data).then(() => {
              this.treeDataGet()
              this.$refs.detailTabs.handleTabs()
            })
          }
        })
      }
    },
    dialogOpen(type, nodeInfo) {
      this.type = type
      this.rulesGet(type === 'add' ? 'cateTreeNodeAddValid' : 'cateTreeNodeEditValid')
      this.$refs.dialog.ejsRef.show()
      this.ruleForm = Object.assign({}, nodeInfo)
    },
    nodeSelected(e) {
      const { nodeData } = e
      this.nodeInfo = nodeInfoGet(this.treePlugin.dataSource, nodeData?.id) || {}
      // this.$refs.detailTabs.handleTabs();
      // this.handleTabs();
      // this.gridDataGet();
    },
    // handleSelectTab(e) {
    //   this.currentTab = e;
    //   this.nodeSelected();
    // },
    // handleTabs() {
    //   if (this.nodeInfo.leafNode) {
    //     this.pageConfig = PAGE_PLUGIN;
    //   } else {
    //     this.pageConfig = PAGE_PLUGIN.slice(1);
    //   }
    // },
    // gridDataGet() {
    //   if (this.nodeInfo.leafNode) {
    //     if (this.currentTab === 0) {
    //       this.pageConfig[0].treeGrid.asyncConfig.params = Object.assign(
    //         {},
    //         this.pageConfig[0].treeGrid.asyncConfig.params,
    //         { id: this.nodeInfo?.id }
    //       );
    //     }
    //   }
    // },
    save() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.type === 'add') {
            const data = {
              ...this.ruleForm,
              categoryTypeId: this.tabInfo.id,
              organizationId: this.orgInfo.id
            }
            this.$API.category.cateTreeNodeAdd(data).then(() => {
              this.treeDataGet()
              this.$refs.detailTabs.handleTabs()
              this.cancel()
            })
          }
          if (this.type === 'edit') {
            this.$API.category.cateTreeNodeEdit(this.ruleForm).then(() => {
              this.treeDataGet()
              this.$refs.detailTabs.handleTabs()
              this.cancel()
            })
          }
        }
      })
    },
    cancel() {
      this.$refs.dialog.ejsRef.hide()
    },
    rulesGet(type) {
      this.$API.category[type]().then((res) => {
        this.rules = formatRules(res.data)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.DS-search {
  display: flex;
  height: 46px;
  line-height: 46px;
  border-bottom: 1px solid #e8e8e8;
  .DS-search-icon {
    color: #979797;
    font-size: 16px;
    padding: 15px 10px;
  }
}
/deep/.mt-tag .tag-label-box .tag-label {
  font-size: 12px !important;
}
/deep/.mt-tag .tag-label-box {
  margin-right: 0px !important;
}
/deep/.mt-tag .tag-label-box {
  padding: 0 5px !important;
}
.left-wrapper {
  width: 261px;
  border: 1px solid rgba(232, 232, 232, 1);
  overflow: auto;
  .add-btn {
    color: #4f5b6d;
    font-size: 14px;
    cursor: pointer;
    border-bottom: 1px solid rgba(232, 232, 232, 1);
  }
  /deep/ .mt-commom-tree-view .action-boxs .btn-box .more {
    left: -64px;
  }
}
.right-wrapper {
  width: calc(100% - 271px);
  border: 1px solid rgba(232, 232, 232, 1);
  .detail-tabs {
    height: calc(100% - 148px);
  }
  .form-item {
    width: calc(33% - 10px);
    min-width: 160px;
  }
  .form-item-label {
    font-size: 14px;
    color: #9a9a9a;
  }
}

/deep/.mt-dialog {
  display: none;
}
/deep/ .e-rowcell {
  text-align: left;
}
</style>
