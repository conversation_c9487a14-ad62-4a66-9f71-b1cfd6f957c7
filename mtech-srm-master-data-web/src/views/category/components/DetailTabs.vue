<!--
 * @Author: your name
 * @Date: 2021-09-28 09:46:43
 * @LastEditTime: 2022-02-28 16:57:46
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\category\components\DetailTabs.vue
-->
<template>
  <div class="full-height">
    <div class="flex items-center mt-px-10 header-tabs">
      <mt-tabs
        :data-source="tabData"
        overflow-mode="Popup"
        :wdith="600"
        @handleSelectTab="tabSelected"
      ></mt-tabs>
    </div>

    <div v-if="tabData.length === 5 && currentTab === 0" class="mt-pa-20 tab-pannel">
      <div></div>
      <mt-tree-grid
        :allow-selection="false"
        :data-source="dataSource"
        :columns="columns()"
        child-mapping="children"
      ></mt-tree-grid>
    </div>
    <div
      v-if="
        (tabData.length === 5 && currentTab === 1) || (tabData.length === 4 && currentTab === 0)
      "
      class="mt-pa-20 tab-pannel"
    >
      <div class="flex justify-start">
        <div class="mt-pa-10 add-btn" @click="dialogMatOpen">
          <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('添加') }}
        </div>
        <div class="mt-pa-10 add-btn" @click="dialogMatDel">
          <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('删除') }}
        </div>
      </div>
      <mt-data-grid
        ref="dataGrid2"
        :data-source="dataSource2"
        :column-data="columns2"
      ></mt-data-grid>
    </div>
    <div
      v-if="
        (tabData.length === 5 && currentTab === 2) || (tabData.length === 4 && currentTab === 1)
      "
      class="mt-pa-20 tab-pannel"
    >
      <mt-data-grid
        ref="dataGrid3"
        :data-source="dataSource3"
        :column-data="columns3"
      ></mt-data-grid>
    </div>
    <div
      v-if="
        (tabData.length === 5 && currentTab === 3) || (tabData.length === 4 && currentTab === 2)
      "
      class="mt-pa-20 tab-pannel"
    >
      <div class="flex justify-start">
        <div class="mt-pa-10 add-btn" @click="dialogFormulaOpen">
          <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('添加') }}
        </div>
        <div class="mt-pa-10 add-btn" @click="dialogFormulaDel">
          <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('删除') }}
        </div>
      </div>
      <mt-data-grid
        ref="dataGrid4"
        :data-source="dataSource4"
        :column-data="columns4"
      ></mt-data-grid>
    </div>
    <div
      v-if="
        (tabData.length === 5 && currentTab === 4) || (tabData.length === 4 && currentTab === 3)
      "
      class="mt-pa-20 tab-pannel"
    >
      <!-- <div class="flex justify-start">
        <div class="mt-pa-10 add-btn" @click="dialogPOrgCoryOpen">
          <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('添加') }}
        </div>
        <div class="mt-pa-10 add-btn" @click="dialogPOrgCoryDel">
          <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />{{ $t('删除') }}
        </div>
      </div> -->
      <!-- <mt-data-grid
        ref="dataGrid5"
        :data-source="dataSource5"
        :column-data="columns5"
      ></mt-data-grid> -->
      <mt-template-page
        ref="dataGrid5"
        :template-config="pageConfigColumns5"
        @handleClickToolBar="handleClickToolBarTwo"
        @actionBegin="actionBegin"
      ></mt-template-page>
      <!-- @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool" -->
    </div>
    <mt-dialog
      ref="dialog"
      :header="$t('关联品类')"
      css-class="create-proj-dialog"
      :buttons="buttons"
    >
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="parentCategoryId" :label="$t('品类名称')">
          <mt-DropDownTree
            id="multi-template2"
            v-model="ruleForm.parentCategoryId"
            :fields="dropDownTree"
            :allow-filtering="true"
            :placeholder="$t('请选择品类名称')"
          ></mt-DropDownTree>
        </mt-form-item>
      </mt-form>
    </mt-dialog>

    <mt-dialog
      ref="dialogMat"
      :header="$t('添加')"
      css-class="create-proj-dialog"
      :buttons="buttonsMat"
    >
      <mt-template-page
        ref="matPage"
        :hidden-tabs="true"
        :template-config="pageConfig"
      ></mt-template-page>
    </mt-dialog>

    <mt-dialog
      ref="dialogFormula"
      :header="$t('关联系列物料计算公式')"
      css-class="create-proj-dialog"
      :buttons="buttonsFormula"
    >
      <mt-template-page
        ref="templatePageFormula"
        :hidden-tabs="true"
        :template-config="pageConfigFormula"
        @handleClickToolBar="handleClickToolBar"
        @actionComplete="actionComplete"
      ></mt-template-page>
    </mt-dialog>
    <mt-dialog css-class="create-proj-dialog" ref="dialog" :header="$t('新增')">
      <mt-form ref="ruleForms" :model="ruleForms">
        <mt-form-item prop="organizationId" :label="$t('组织名称')">
          <mt-DropDownTree
            id="filter"
            :fields="orgFields"
            :placeholder="$t('请选择组织')"
            @select="orgChange"
          ></mt-DropDownTree>
        </mt-form-item>
        <mt-form-item prop="type" :label="$t('品类种类')">
          <mt-select
            :data-source="dataCateType"
            :fields="{ text: 'typeName', value: 'id' }"
            :show-clear-button="true"
            :placeholder="$t('请选择品类种类')"
            @select="cateTypeChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="categoryId" :label="$t('品类')">
          <mt-DropDownTree
            id="category"
            :fields="cateFields"
            :placeholder="$t('请选择品类')"
            @select="(e) => (ruleForms.categoryId = e.itemData.id)"
          ></mt-DropDownTree>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import {
  MAT_PAGE_PLUGIN,
  FORMULA_ADD,
  PAGE_CONFIG,
  columnDataFive
} from '../config/categoryEdit.config'
import { i18n } from '@/main.js'
import { createEditInstance } from '@/utils/ej/dataGrid/index'
import { utils } from '@mtech-common/utils'
import { sliceArray } from '@/utils/arr'
const tabData = [
  // { title: i18n.t("关联品类") },
  { title: i18n.t('物料/品项') },
  { title: i18n.t('SKU列表') },
  { title: i18n.t('关联系列物料公式') },
  { title: i18n.t('品类关联关系') }
]
export default {
  props: {
    orgInfo: {
      required: true,
      type: Object,
      default() {
        return {}
      }
    },
    tabInfo: {
      required: true,
      type: Object,
      default() {
        return {}
      }
    },
    nodeInfo: {
      required: true,
      type: Object,
      default() {
        return {}
      }
    },
    hasOrgTree: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      pageConfigColumns5: PAGE_CONFIG,
      categoryCodeData: [],
      editColumns: [],
      ruleForms: {},
      orgFields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      dataCateType: [],
      cateFields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      dropDownTree: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      pageConfig: MAT_PAGE_PLUGIN,

      pageConfigFormula: FORMULA_ADD(),
      tabData: tabData,
      currentTab: 0,

      dataSource: [],
      columns(that = this) {
        return [
          {
            headerText: this.$t('关联品类层级'),
            field: 'orgName'
          },
          {
            headerText: this.$t('关联品类项'),
            field: 'id',
            template: function () {
              return {
                template: Vue.component('relate', {
                  //$parent.$parent.$parent.$emit('relate', data);
                  template: `
                    <div>
                      <div v-if="!data.parentCategoryId" class="mt-pa-10 relate-btn" @click="handleClicked">
                        <mt-icon name="icon_solid_Createproject" class="mt-mr-10" />关联品类
                      </div>
                      <div v-else>{{ data.parentCategoryName }}</div>
                    </div>
                  `,
                  data() {
                    return {
                      data: {}
                    }
                  },
                  methods: {
                    handleClicked() {
                      that.dropDownTreeDataGet(this.data.id)
                    }
                  }
                })
              }
            }
          }
        ]
      },
      loading: false,
      dataSource2: [],
      columns2: [
        {
          type: 'checkbox',
          width: 50
        },
        {
          headerText: this.$t('物料/品项编号'),
          field: 'itemCode'
        },
        {
          headerText: this.$t('物料/品项名称'),
          field: 'itemName'
        },
        {
          headerText: this.$t('规格型号'),
          field: 'itemDescription'
        },
        {
          headerText: this.$t('旧物料编号'),
          field: 'itemOldCode'
        },
        {
          headerText: this.$t('基本计量单位'),
          field: 'itemBaseMesureUnit'
        }
      ],
      dataSource3: [],
      columns3: [
        {
          headerText: this.$t('SKU编号'),
          field: 'barCode'
        },
        {
          headerText: this.$t('SKU名称'),
          field: 'name'
        },
        {
          headerText: this.$t('规格型号'),
          field: 'specificationModel'
        },
        {
          headerText: this.$t('制造商'),
          field: 'manufacturerName'
        }
      ],
      dataSource4: [],
      columns4: [
        {
          headerText: this.$t('系列物料计算公式'),
          field: 'formulaName'
        },
        {
          headerText: this.$t('系列物料计算公式'),
          field: 'formula'
        }
      ],
      // dataSource5: [],
      // columns5: [
      //   {
      //     headerText: this.$t("品类节点Id"),
      //     field: "categoryId",
      //   },
      //   {
      //     headerText: this.$t("品类节点编码"),
      //     field: "categoryCode",
      //   },
      //   {
      //     headerText: this.$t("品类节点名称"),
      //     field: "categoryName",
      //   },
      //   {
      //     headerText: this.$t("关联品类节点Id"),
      //     field: "parentOrgCategoryId",
      //   },
      //   {
      //     headerText: this.$t("品类节点编码"),
      //     field: "parentOrgCategoryCode",
      //   },
      //   {
      //     headerText: this.$t("品类节点名称"),
      //     field: "parentOrgCategoryName",
      //   },
      // ],
      ruleForm: {
        parentCategoryId: ''
      },
      rules: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      buttonsMat: [
        {
          click: this.cancelMat,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.saveMat,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      buttonsFormula: [
        {
          click: this.cancelFormula,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.saveFormula,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
      // buttonsTwo: [
      //   {
      //     click: this.POrgCoryCancel,
      //     buttonModel: { content: this.$t("取消") },
      //   },
      //   {
      //     click: this.POrgCorySave,
      //     buttonModel: { isPrimary: "true", content: this.$t("确定") },
      //   },
      // ],
    }
  },
  watch: {
    nodeInfo() {
      this.handleTabs()
    }
  },
  async mounted() {
    await this.getDropDownData().catch(() => {})
    this.handleUnionColumns()
    this.$set(this.pageConfigColumns5[0].grid, 'columnData', this.editColumns)
  },
  methods: {
    async getDropDownData() {
      const tasks = []

      // 获取币种
      let params = {
        page: { current: 1, size: 20 }
      }
      tasks.push(() =>
        this.$API.sourcing.queryCategorys(params).then((res) => {
          if (res.code == 200) {
            console.log(1, res?.data?.records)
            this.categoryCodeData = res.data.records
            this.categoryCodeData.forEach((i) => {
              i.categoryId = i.id
            })
            console.log(11, this.categoryCodeData)
          }
          // this.categoryCodeData = res?.data?.records || [];
        })
      )
      console.log(11111111, this.categoryCodeData)

      for (const task of sliceArray(tasks, 5)) {
        await Promise.all(task.map((fn) => fn())).catch(() => {})
      }
    },
    handleUnionColumns() {
      this.requiredCols = []
      let params = {
        categoryPagedQuery: this.$API.sourcing.queryCategorys,
        self: this
      }
      this.editColumns = columnDataFive(params, {
        $on: (event, fn) => {
          this.busEvent.add(event)
          this.$bus.$on(event, fn)
        },
        $emit: (event, fn) => {
          this.busEvent.add(event)
          this.$bus.$emit(event, fn)
        }
      })
    },
    async dialogPOrgCoryOpen(data) {
      this.$refs.ruleForm.resetFields()
      this.ruleForm = Object.assign({}, data)
      this.$refs.dialog.ejsRef.show()
      await this.orgTreeDataGet()
      await this.cateTypeDataGet()
    },
    orgTreeDataGet() {
      this.$API.itemManagement.getFuzzyCompanyTree({ fuzzyName: '' }).then((res) => {
        this.orgFields = Object.assign({}, this.orgFields, {
          dataSource: res.data
        })
      })
      console.log(11111, this.orgFields.dataSource)
    },
    cateTypeDataGet() {
      this.$API.itemManagement.criteriaQuery({}).then((res) => {
        this.dataCateType = res.data
      })
      console.log(11111111, this.dataCateType)
    },
    orgChange(e) {
      this.ruleForm.organizationId = e.itemData.id
      this.cateGet()
    },
    cateTypeChange(e) {
      this.ruleForm.type = e.itemData.id
      this.cateGet()
    },
    cateGet() {
      if (this.ruleForm.type && this.ruleForm.organizationId) {
        const query = {
          categoryTypeId: this.ruleForm.type,
          organizationId: this.ruleForm.organizationId
        }
        this.$API.itemManagement.getTree(query).then((res) => {
          this.cateFields = Object.assign({}, this.cateFields, {
            dataSource: res.data
          })
        })
      }
    },
    POrgCoryCancel() {
      this.$refs.dialog.ejsRef.hide()
    },
    dialogPOrgCoryDel() {
      const rowSelected = this.$refs.dataGrid5.ejsRef.getSelectedRecords()
      if (rowSelected.length == 0) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
      } else {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            const ids = rowSelected.map((e) => e.id)
            const data = { ids }
            this.$API.category.delCategoryRel(data).then(() => {
              this.pagedQueryCategoryRel()
            })
          }
        })
      }
    },
    actionBegin(args) {
      let data = {
        parentOrgCategoryId: this.nodeInfo?.id,
        parentOrgCategoryCode: this.nodeInfo?.categoryCode,
        parentOrgCategoryName: this.nodeInfo?.categoryName
      }
      // console.log(1111111, data);
      args.rowData = { ...args.rowData, ...data }
      args.data = { ...args.rowData, ...data }
    },
    handleClickToolBarTwo(e) {
      const { toolbar, grid } = e
      const rowSelected = grid.getSelectedRecords()
      if (toolbar.id === 'Add') {
        // this.setNewRecordsTag(tag, _data);
        this.endEdit()
        this.$refs.dataGrid5.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
        return
      } else if (toolbar.id === 'Delete') {
        if (rowSelected.length == 0) {
          this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        } else {
          this.endEdit()
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除选中的数据？')
            },
            success: () => {
              const ids = rowSelected.map((e) => e.id)
              const data = { ids }
              this.$API.category.delCategoryRel(data).then(() => {
                this.pagedQueryCategoryRel()
              })
            }
          })
        }
      } else if (toolbar.id === 'Save') {
        this.endEdit()
        // 设置延时，待失去焦点后在进行数据校验保存
        setTimeout(() => {
          this.POrgCorySave()
        }, 100)
      }
    },
    // 结束编辑状态
    endEdit() {
      let _current = this.$refs.dataGrid5.getCurrentTabRef()
      _current?.grid.endEdit()
    },
    POrgCorySave() {
      let flag = false
      let ids = []
      let rowSelected = []
      let dataSource = this.$refs.dataGrid5.getCurrentTabRef().grid.getCurrentViewRecords()
      dataSource.map((e) => {
        if (!e.id) {
          rowSelected.push(e)
        }
      })
      rowSelected.map((e) => {
        if (e.categoryId == this.nodeInfo?.id) {
          flag = true
        } else {
          ids.push(e.categoryId)
        }
      })
      let params = {
        parentCategoryId: this.nodeInfo?.id,
        categoryIds: ids
      }
      if (flag) {
        this.$toast({
          content: this.$t('品类ig和关联品类id一样不能保存'),
          type: 'warning'
        })
      } else if (ids.length <= 0) {
        this.$toast({
          content: this.$t('没有可保存的数据'),
          type: 'warning'
        })
      } else {
        this.$API.category.addCategoryRel(params).then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('添加成功'), type: 'success' })
            this.pagedQueryCategoryRel()
            this.$refs.dialog.ejsRef.hide()
          }
        })
      }
    },
    handleTabs() {
      // if (this.nodeInfo.leafNode && this.hasOrgTree) {
      //   this.tabData = tabData;
      // } else {
      // this.tabData = tabData
      // }
      this.dataGet()
    },
    dataGet() {
      // if (this.nodeInfo.leafNode && this.hasOrgTree) {
      //   if (this.currentTab === 0) {
      //     const query = { id: this.nodeInfo?.id };
      //     this.$API.category.cateRelateQuery(query).then((res) => {
      //       this.$set(this, "dataSource", res.data || []);
      //     });
      //   }
      //   if (this.currentTab === 1) {
      //     const query = { id: this.nodeInfo?.id };
      //     this.$API.category.materialQuery(query).then((res) => {
      //       this.$set(this, "dataSource2", res.data || []);
      //     });
      //   }
      //   if (this.currentTab === 2) {
      //     const query = { id: this.nodeInfo?.id };
      //     this.$API.category.skuQuery(query).then((res) => {
      //       this.$set(this, "dataSource3", res.data || []);
      //     });
      //   }
      //   if (this.currentTab === 3) {
      //     const query = { categoryId: this.nodeInfo?.id };
      //     this.$API.category.categorySeriesFormulaQuery(query).then((res) => {
      //       this.$set(this, "dataSource4", res.data || []);
      //     });
      //   }
      //   if (this.currentTab === 4) {
      //     const query = { categoryId: this.nodeInfo?.id };
      //     this.$API.category.pagedQueryCategoryRel(query).then((res) => {
      //       this.$set(this, "dataSource5", res.data || []);
      //     });
      //   }
      // } else {
      if (this.currentTab === 0) {
        const query = { id: this.nodeInfo?.id }
        this.$API.category.materialQuery(query).then((res) => {
          this.$set(this, 'dataSource2', res.data || [])
        })
      }
      if (this.currentTab === 1) {
        const query = { id: this.nodeInfo?.id }
        this.$API.category.skuQuery(query).then((res) => {
          this.$set(this, 'dataSource3', res.data || [])
        })
      }
      if (this.currentTab === 2) {
        const query = { categoryId: this.nodeInfo?.id }
        this.$API.category.categorySeriesFormulaQuery(query).then((res) => {
          this.$set(this, 'dataSource4', res.data || [])
        })
      }
      if (this.currentTab === 3) {
        this.pagedQueryCategoryRel()
      }
    },
    pagedQueryCategoryRel() {
      const query = {
        page: {
          current: 1,
          size: 20
        },
        defaultRules: [
          {
            field: 'parent_org_category_code',
            operator: 'equal',
            type: 'string',
            value: this.nodeInfo?.categoryCode
          }
        ]
      }

      // { categoryId: this.nodeInfo?.categoryCode };
      this.$API.category.pagedQueryCategoryRel(query).then((res) => {
        this.$set(this.pageConfigColumns5[0].grid, 'dataSource', res.data.records)
        // this.$set(this, "dataSource5", res.data.records || []);
      })
    },
    tabSelected(e) {
      this.currentTab = e
      this.dataGet()
    },
    dropDownTreeDataGet(id) {
      const query = {
        categoryTypeId: this.tabInfo.id,
        organizationId: id
      }
      this.$API.category.cateTreeNodeDetailGet(query).then((res) => {
        this.dropDownTree = Object.assign({}, this.dropDownTree, {
          dataSource: res.data || []
        })
        this.$refs.dialog.ejsRef.show()
      })
    },
    cancel() {
      this.$refs.dialog.ejsRef.hide()
    },
    save() {
      const data = {
        categoryId: this.nodeInfo.id,
        parentCategoryId: this.ruleForm.parentCategoryId.join('')
      }
      this.$API.category.cateRelate(data).then(() => {
        this.dataGet()
        this.cancel()
      })
    },

    dialogMatOpen() {
      this.$refs.dialogMat.ejsRef.show()
      this.pageConfig[0].grid.asyncConfig = Object.assign(
        {},
        {
          url: '/masterDataManagement/tenant/category-item/item-without-bind',
          params: {
            categoryTypeId: this.tabInfo.id,
            organizationId: this.orgInfo.id
          }
        }
      )
      this.$refs.matPage.refreshCurrentGridData()
    },
    actionComplete(args) {
      if (args.requestType == 'beginEdit') {
        this.rowIndex = args.rowIndex
      }
    },
    dialogFormulaDel() {
      const rowSelected = this.$refs.dataGrid4.ejsRef.getSelectedRecords()
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rowSelected.map((e) => e.id)
          const data = { ids }
          this.$API.category.categorySeriesFormulaBatchDelete(data).then(() => {
            this.dataGet()
          })
        }
      })
    },
    async dialogFormulaOpen() {
      let tempId = 0
      let _this = this
      this.$API.dict.TenantDictTypeGet({ dictCode: 'seriesItemFormula' }).then((res) => {
        let formulaSelectOptions = res.data
        let columnDataFormula = () => {
          const editInstance = createEditInstance().onChange((ctx, { field }, event) => {
            if (field === 'formulaName') {
              ctx.setValueByField(field, event.itemData.itemName)
              ctx.setValueByField('formula', event.itemData.itemDescription)
              ctx.setValueByField('tempId', tempId)
              const grid = this.$refs.templatePageFormula.getCurrentTabRef().grid //获取表格实例
              grid.$options.propsData.dataSource[this.rowIndex].formulaName =
                event.itemData.itemName
              grid.$options.propsData.dataSource[this.rowIndex].formula =
                event.itemData.itemDescription
              grid.$options.propsData.dataSource[this.rowIndex].tempId = tempId
              tempId++
            }
          })
          return [
            {
              width: '50',
              type: 'checkbox'
            },
            {
              field: 'formulaName',
              headerText: i18n.t('系列物料计算公式'),
              width: '350',
              edit: editInstance.create({
                getEditConfig: () => ({
                  type: 'mt-select',
                  fields: { value: 'itemName', text: 'itemName' },
                  'show-clear-button': true,
                  created: function () {
                    setTimeout(() => {
                      let selectOptions = utils.cloneDeep(formulaSelectOptions)
                      let gridData =
                        _this.$refs.templatePageFormula.getCurrentTabRef().grid.dataSource
                      for (let item of gridData) {
                        let index = selectOptions.findIndex((e) => {
                          return e.itemName == item.formulaName && e.itemDescription == item.formula
                        })
                        if (index != -1) {
                          selectOptions.splice(index, 1)
                        }
                      }
                      editInstance.setOptions('formulaName', {
                        dataSource: selectOptions
                      })
                    }, 0)
                  },
                  change: function (e) {
                    if (e.itemData) {
                      editInstance.setValueByField('formulaName', e.itemData.itemName)
                      editInstance.setValueByField('formula', e.itemData.itemDescription)
                    } else {
                      editInstance.setValueByField('formulaName', '')
                      editInstance.setValueByField('formula', '')
                    }
                  },
                  dataSource: []
                })
              })
            },
            {
              field: 'formula',
              headerText: i18n.t('系列物料计算公式'),
              width: '350',
              allowEditing: false,
              edit: editInstance.create({
                getEditConfig: () => ({
                  type: 'text',
                  readonly: true,
                  disabled: true
                })
              })
            }
          ]
        }
        this.$set(this.pageConfigFormula[0].grid, 'columnData', columnDataFormula())
      })
      this.$refs.dialogFormula.ejsRef.show()
    },
    cancelMat() {
      this.$refs.dialogMat.ejsRef.hide()
    },
    saveMat() {
      const currentTab = this.$refs.matPage.getCurrentTabRef()
      const rowSelect = currentTab.grid.getSelectedRecords()
      const ids = rowSelect.map((e) => e.id)

      const data = {
        categoryIds: [this.nodeInfo.id],
        itemIds: ids,
        organizationId: this.orgInfo.id
      }
      this.$API.category.materialAdd(data).then(() => {
        this.dataGet()
        this.cancelMat()
      })
    },
    handleClickToolBar(e) {
      if (e.toolbar.id == 'add') {
        const dataSource =
          this.$refs.templatePageFormula.getCurrentTabRef().grid.$options.propsData.dataSource //获取表格实例
        dataSource.unshift({
          formula: null,
          formulaName: null
        })
        this.$refs.templatePageFormula.getCurrentTabRef().grid.refresh()
      } else if (e.toolbar.id == 'del') {
        let _selectGridRecords = e.gridRef.getMtechGridRecords()
        if (_selectGridRecords.length < 1) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        for (let item of _selectGridRecords) {
          const dataSource =
            this.$refs.templatePageFormula.getCurrentTabRef().grid.$options.propsData.dataSource //获取表格实例
          dataSource.splice(
            dataSource.findIndex((e) => {
              return e.tempId == item.tempId
            }),
            1
          )
        }
        this.$refs.templatePageFormula.getCurrentTabRef().gridRef.selectIdRecords = []
        this.$refs.templatePageFormula.getCurrentTabRef().grid.refresh()
      }
    },
    cancelFormula() {
      this.$refs.dialogFormula.ejsRef.hide()
    },
    saveFormula() {
      let dataSource = utils.cloneDeep(
        this.$refs.templatePageFormula.getCurrentTabRef().grid.dataSource
      ) //获取表格实例
      if (dataSource.length == 0) {
        this.$toast({
          content: this.$t('请添加系列物料计算公式'),
          type: 'warning'
        })
        return
      }
      for (let index in dataSource) {
        if (dataSource[index].formula == null && dataSource[index].formula == null) {
          this.$toast({
            content: this.$t('请选择行号为') + (parseInt(index) + 1) + this.$t('系列物料计算公式'),
            type: 'warning'
          })
          return
        }
      }
      let existFormulaToastStr = []
      let existFormula = this.dataSource4
      for (let item of existFormula) {
        let index = dataSource.findIndex((e) => {
          return e.formulaName == item.formulaName && e.formula == item.formula
        })
        if (index != -1) {
          existFormulaToastStr.push(item.formulaName)
          dataSource.splice(index, 1)
        }
      }
      if (existFormulaToastStr.length != 0) {
        this.$toast({
          content: existFormulaToastStr.join(',') + this.$t('已存在，请勿重复添加'),
          type: 'warning'
        })
        return
      }
      for (let item of dataSource) {
        item.tempId = undefined
      }
      this.$API.category
        .categorySeriesFormulaBatchAdd({
          categoryId: this.nodeInfo?.id,
          formulaList: dataSource
        })
        .then((res) => {
          if (res.code == 200) {
            this.$toast({
              content: res.message ? res.message : this.$t('操作成功'),
              type: 'success'
            })
            this.cancelFormula()
            this.dataGet()
          }
        })
      return
    },
    dialogMatDel() {
      const rowSelected = this.$refs.dataGrid2.ejsRef.getSelectedRecords()

      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rowSelected.map((e) => e.id)
          const data = { ids }
          this.$API.category.materialDel(data).then(() => {
            this.dataGet()
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.header-tabs {
  height: 50px;
  border-top: 1px solid rgba(232, 232, 232, 1);
  border-bottom: 1px solid rgba(232, 232, 232, 1);
}
.tab-pannel {
  height: calc(100% - 50px);
  .add-btn {
    color: #4f5b6d;
    font-size: 14px;
    cursor: pointer;
  }
}

/deep/.mt-dialog {
  display: none;
}
</style>
