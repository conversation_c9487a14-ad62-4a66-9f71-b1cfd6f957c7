<!--
 * @Author: your name
 * @Date: 2021-09-17 14:30:54
 * @LastEditTime: 2021-10-14 17:58:58
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\category\CategoryTree.vue
-->
<template>
  <div class="full-height mt-pt-20">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
    <mt-dialog ref="dialog" :header="headerTitle" css-class="create-proj-dialog" :buttons="buttons">
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="typeName" :label="$t('品类树名称')">
          <mt-input
            v-model="ruleForm.typeName"
            :readonly="disabled"
            :show-clear-button="false"
            type="text"
            :placeholder="$t('请输入品类树名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="typeCode" :label="$t('品类树编码')">
          <mt-input
            v-model="ruleForm.typeCode"
            :readonly="type !== 'add'"
            :show-clear-button="false"
            type="text"
            :placeholder="$t('请输入品类树编码')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import { PAGE_CONFIG, RULES, OPTIONS, FIELDS } from './config/categoryTree.config'
import { formatRules } from '@/utils/util'
export default {
  data() {
    return {
      options: OPTIONS,
      pageConfig: PAGE_CONFIG,
      rules: RULES,
      fields: FIELDS,

      pageInfo: {
        current: 1,
        size: 10
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {},
      type: 'add' // add edit preview
    }
  },
  computed: {
    headerTitle() {
      if (this.type === 'add') {
        return this.$t('新增')
      }
      if (this.type === 'edit') {
        return this.$t('编辑')
      }
      return this.$t('预览')
    },
    disabled() {
      if (this.type === 'preview') {
        return true
      }
      return false
    }
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const rowSelected = grid.getSelectedRecords()
      if (toolbar.id === 'Add') {
        this.handleAction('add')
        this.rulesGet('cateTreeAddValid')
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDelete(rowSelected)
      }
    },
    handleClickCellTool(e) {
      const { data, tool } = e
      if (tool.id === 'edit') {
        this.handleAction('edit', data)
        this.rulesGet('cateTreeEditValid')
      }
      if (tool.id === 'delete') {
        this.handleDelete([data])
      }
      if (tool.id === 'preview') {
        this.handleAction('preview', data)
      }
    },
    handleAction(type, data) {
      this.type = type
      this.$refs.ruleForm.resetFields()
      this.ruleForm = Object.assign({}, data)
      this.$refs.dialog.ejsRef.show()
    },
    handleDelete(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.category.cateTreeDelete(data).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    },
    companyChange(e) {
      this.ruleForm.parentCode = e.itemData.orgCode
    },
    save() {
      if (this.type === 'preview') {
        this.cancel()
        return false
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.type === 'add') {
            const data = {
              ...this.ruleForm
            }
            this.$API.category.cateTreeAdd(data).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
          if (this.type === 'edit') {
            this.$API.category.cateTreeEdit(this.ruleForm).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
        }
      })
    },
    cancel() {
      this.$refs.dialog.ejsRef.hide()
    },
    rulesGet(type) {
      this.$API.category[type]().then((res) => {
        this.rules = formatRules(res.data)
      })
    },
    companyGet() {
      this.$API.category.companyGet().then((res) => {
        this.options = Object.assign({}, this.options, { COMPANY: res.data })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/.btn-text {
  color: #4f5b6d;
}
/deep/ .mt-data-grid {
  .status-label {
    font-size: 12px;
    padding: 4px;
    border-radius: 2px;
    &.status-0 {
      color: #9baac1;
      background: rgba(155, 170, 193, 0.1);
    }
    &.status-1 {
      color: #6386c1;
      background: rgba(99, 134, 193, 0.1);
    }
  }
}
/deep/.mt-dialog {
  display: none;
}
</style>
