import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-10-13 13:49:04
 * @LastEditTime: 2021-12-29 10:21:57
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\goodsOrigin\config\originList.config.js
 */
export const PAGE_PLUGIN = [
  {
    toolbar: [],
    grid: {
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'organizationName',
          width: '200',
          headerText: i18n.t('公司')
        },
        {
          field: 'siteName',
          width: '200',
          headerText: i18n.t('工厂/地点')
        },
        {
          field: 'siteCode',
          width: '200',
          headerText: i18n.t('工厂/地点编码')
        },
        {
          field: 'categoryCode',
          width: '200',
          headerText: i18n.t('品类编码')
        },
        {
          field: 'categoryName',
          width: '200',
          headerText: i18n.t('品类名称')
        },
        {
          field: 'itemCode',
          width: '200',
          headerText: i18n.t('品项编码')
        },
        {
          field: 'itemName',
          width: '200',
          headerText: i18n.t('品项名称')
        },
        // {
        //   field: "updateUserName",
        //   width: "200",
        //   headerText: "SKU",
        // },
        {
          field: 'supplierName',
          width: '200',
          headerText: i18n.t('供应商名称')
        },
        {
          field: 'supplierCode',
          width: '200',
          headerText: i18n.t('供应商编码')
        },
        {
          field: 'startTime',
          width: '200',
          headerText: i18n.t('有效起始')
        },
        {
          field: 'endTime',
          width: '200',
          headerText: i18n.t('有效截至')
        }
        // {
        //   field: "supplierContract",
        //   width: "200",
        //   headerText: i18n.t("来源合同"),
        // },
        // {
        //   field: "updateUserName",
        //   width: "200",
        //   headerText: i18n.t("来源价格条款"),
        // },
      ],
      asyncConfig: {
        url: '/masterDataManagement/tenant/supply/source/paged-query',
        params: {}
      }
    }
  }
]
