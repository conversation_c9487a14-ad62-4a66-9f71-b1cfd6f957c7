<!--
 * @Author: your name
 * @Date: 2021-10-13 13:45:18
 * @LastEditTime: 2021-10-25 14:07:03
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\goodsOrigin\originList.vue
-->
<template>
  <div class="full-height mt-pt-20">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>

    <origin-add ref="originAdd"></origin-add>

    <origin-preview ref="originPreview"></origin-preview>
  </div>
</template>

<script>
import { PAGE_PLUGIN } from './config/originList.config'
import OriginAdd from './components/OriginAdd.vue'
import OriginPreview from './components/OriginPreview.vue'

export default {
  components: {
    OriginAdd,
    OriginPreview
  },
  data() {
    return {
      pageConfig: PAGE_PLUGIN
    }
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const rowSelected = grid?.getSelectedRecords?.()

      if (toolbar.id === 'Add') {
        this.$refs.originAdd.show()
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDelete(rowSelected)
      }
      if (toolbar.id === 'Active' && rowSelected.length) {
        this.handleStatus('active', rowSelected)
      }
      if (toolbar.id === 'Negative' && rowSelected.length) {
        this.handleStatus('negative', rowSelected)
      }
    },
    handleClickCellTool(e) {
      const { data, tool } = e

      if (tool.id === 'delete') {
        this.handleDelete([data])
      }
      if (tool.id === 'preview') {
        this.$refs.originPreview.show(data)
      }
    },
    handleDelete(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.goodsOrigin.originDelete(data).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    },
    handleStatus(type, rows) {
      const dict = {
        active: { label: this.$t('激活'), value: 1 },
        negative: { label: this.$t('失效'), value: 3 }
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: `确认${dict[type]?.label}选中的数据？`
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids, statusId: dict[type]?.value }
          this.$API.goodsOrigin.originStatusUpdate(data).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    }
  }
}
</script>

<style></style>
