<!--
 * @Author: your name
 * @Date: 2021-10-25 15:26:06
 * @LastEditTime: 2021-10-26 13:42:27
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\goodsOrigin\components\OriginAddDialog.vue
-->
<template>
  <div>
    <mt-dialog
      ref="dialog"
      :header="$t('新增')"
      :enable-resize="false"
      css-class="create-proj-dialog"
      :buttons="buttons"
    >
      <div class="full-height">
        <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
          <mt-form-item prop="organizationId" :label="$t('公司名称')">
            <mt-select
              v-model="ruleForm.organizationId"
              :fields="fields.COMPANY"
              :data-source="options.COMPANY"
              :show-clear-button="true"
              :allow-filtering="true"
              :placeholder="$t('请选择公司名称')"
              @change="siteGet"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="siteId" :label="$t('工厂/地点')">
            <mt-select
              v-model="ruleForm.siteId"
              :fields="fields.SITE"
              :data-source="options.SITE"
              :show-clear-button="true"
              :allow-filtering="true"
              :placeholder="$t('请选择工厂/地点')"
            ></mt-select>
          </mt-form-item>
        </mt-form>
        <mt-template-page
          ref="originAddPage"
          :key="originAddKey"
          :hidden-tabs="true"
          :template-config="pageConfig"
          @handleClickCellTool="handleClickCellTool"
        ></mt-template-page>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import { PAGE_MAT_PLUGIN, OPTIONS, FIELDS } from '../config/originList.config'

export default {
  data() {
    return {
      originAddKey: 1,
      pageConfig: PAGE_MAT_PLUGIN,

      options: OPTIONS,
      fields: FIELDS,

      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],

      ruleForm: {},
      rules: {}
    }
  },
  methods: {
    handleClickCellTool(e) {
      const { data, tool } = e

      if (tool.id === 'preview') {
        this.handleAction('preview', data)
      }
    },
    handleAction() {
      this.$refs.viewDialog.ejsRef.show()
    },
    show() {
      this.$refs.dialog.ejsRef.show()
      this.$refs.ruleForm.resetFields()
      // this.visible = true;
      this.companyGet()
    },
    save() {
      const currentTab = this.$refs.originAddPage?.getCurrentTabRef()
      const selectedRows = currentTab.grid.getSelectedRecords()
      const data = {
        ...this.ruleForm,
        itemIds: selectedRows.map((e) => e.itemId)
      }

      this.$API.goodsOrigin.originListAdd(data).then(() => {
        this.hide()
        this.$parent.$refs.tepPage.refreshCurrentGridData()
      })
    },
    hide() {
      this.$refs.dialog.ejsRef.hide()
      // this.visible = false;
    },

    companyGet() {
      this.$API.goodsOrigin.companyGet().then((res) => {
        this.$set(this.options, 'COMPANY', res.data)
      })
    },
    siteGet(e) {
      if (e.value) {
        this.ruleForm.siteId = ''
        this.$set(this.options, 'SITE', [])
        const query = { organizationId: e.value }
        this.$API.goodsOrigin.siteGet(query).then((res) => {
          this.$set(this.options, 'SITE', res.data)
        })

        // 查询表格数据
        this.$set(this.pageConfig[0].grid.asyncConfig.defaultRules[0], 'value', e.value)
        this.originAddKey++
      }
    }
  }
}
</script>

<style lang="scss">
/deep/.mt-dialog {
  display: none;
}
</style>
