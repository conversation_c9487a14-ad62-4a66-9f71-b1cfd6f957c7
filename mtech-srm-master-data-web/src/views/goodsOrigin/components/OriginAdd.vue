<!--
 * @Author: your name
 * @Date: 2021-10-14 10:33:33
 * @LastEditTime: 2022-01-10 14:24:24
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\goodsOrigin\components\OriginAdd.vue
-->
<template>
  <div>
    <mt-dialog
      ref="dialog"
      :header="$t('新增')"
      :enable-resize="false"
      :position="{ X: 'right', Y: 'top' }"
      css-class="create-proj-dialog right-wrapper"
      :buttons="buttons"
      @close="hide"
    >
      <div class="full-height">
        <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
          <mt-form-item prop="organizationId" :label="$t('公司名称')">
            <mt-select
              v-model="ruleForm.organizationId"
              :fields="fields.COMPANY"
              :data-source="options.COMPANY"
              :show-clear-button="true"
              :allow-filtering="true"
              :placeholder="$t('请选择公司名称')"
              @change="siteGet"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="siteId" :label="$t('工厂/地点')">
            <mt-select
              v-model="ruleForm.siteId"
              :fields="fields.SITE"
              :data-source="options.SITE"
              :show-clear-button="true"
              :allow-filtering="true"
              :placeholder="$t('请选择工厂/地点')"
              @change="matDataGet"
            ></mt-select>
          </mt-form-item>
        </mt-form>
        <mt-template-page
          ref="originAddPage"
          :hidden-tabs="true"
          :template-config="pageConfig"
          @handleClickCellTool="handleClickCellTool"
        ></mt-template-page>
      </div>
    </mt-dialog>

    <mt-dialog
      ref="viewDialog"
      :header="$t('预览')"
      :enable-resize="false"
      css-class="create-proj-dialog"
      :buttons="buttonsView"
    >
      <div class="full-height">
        <mt-template-page
          ref="originViewPage"
          :hidden-tabs="true"
          :template-config="pageViewConfig"
        ></mt-template-page>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import { PAGE_MAT_PLUGIN, PAGE_VIEW_PLUGIN, OPTIONS, FIELDS } from '../config/originList.config'

export default {
  data() {
    return {
      pageConfig: PAGE_MAT_PLUGIN,
      pageViewConfig: [
        {
          grid: {
            allowPaging: false,
            columnData: PAGE_VIEW_PLUGIN,
            dataSource: []
            // asyncConfig: {
            //   url: "/masterDataManagement/tenant/supply-source-list/getPriceRecordInfo",
            //   recordsPosition: "data",
            //   params: [],
            // },
          }
        }
      ],
      options: OPTIONS,
      fields: FIELDS,

      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      buttonsView: [
        {
          click: this.hideView,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.hideView,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],

      ruleForm: {},
      rules: {}
    }
  },
  methods: {
    handleClickCellTool(e) {
      const { data, tool } = e

      if (tool.id === 'preview') {
        this.handleAction('preview', data)
      }
    },
    handleAction(type, row) {
      let obj = {
        itemId: row.itemId,
        siteId: this.ruleForm.siteId
      }
      this.$API.goodsOrigin.getPriceRecordInfo(obj).then((res) => {
        if (res.data) {
          this.$set(this.pageViewConfig[0].grid, 'dataSource', res.data)
        } else {
          this.$set(this.pageViewConfig[0].grid, 'dataSource', [])
        }
      })
      // this.$set(this.pageViewConfig[0].grid.asyncConfig, "params", {
      //   itemId: row.itemId,
      //   siteId: this.ruleForm.siteId,
      // });

      this.$refs.viewDialog.ejsRef.show()
    },
    matDataGet(e) {
      if (e) {
        this.$set(this.pageConfig[0].grid.asyncConfig, 'defaultRules', [
          {
            field: 'organizationId',
            operator: 'equal',
            value: e.itemData?.organizationId || ''
          }
        ])
      }
    },
    show() {
      this.$refs.dialog.ejsRef.show()
      this.ruleForm = {}
      this.$refs.ruleForm.resetFields()

      this.companyGet()
    },
    save() {
      const currentTab = this.$refs.originAddPage.getCurrentTabRef()
      const selectedRows = currentTab.grid.getSelectedRecords()
      const data = {
        ...this.ruleForm,
        itemIds: selectedRows.map((e) => e.itemId)
      }

      this.$API.goodsOrigin.originListAdd(data).then(() => {
        this.hide()
        this.$parent.$refs.tepPage.refreshCurrentGridData()
      })
    },
    hide() {
      this.matDataGet({ itemData: {} })
      this.$refs.dialog.ejsRef.hide()
    },
    hideView() {
      this.$refs.viewDialog.ejsRef.hide()
    },
    companyGet() {
      this.$API.goodsOrigin.companyGet().then((res) => {
        this.$set(this.options, 'COMPANY', res.data)
      })
    },
    siteGet(e) {
      if (e.value) {
        this.ruleForm.siteId = ''
        this.$set(this.options, 'SITE', [])
        const query = { organizationId: e.value }
        this.$API.goodsOrigin.siteGet(query).then((res) => {
          this.$set(this.options, 'SITE', res.data)
        })
      }
    }
  }
}
</script>

<style lang="scss">
.e-dlg-container > .right-wrapper {
  height: 100% !important;
  .e-dlg-header-content {
    background-color: rgba(243, 243, 243, 1);
    border-radius: 8px 0 0 0;
    .e-dlg-closeicon-btn {
      .e-btn-icon {
        color: #292929 !important;
      }
    }
    .e-dlg-header {
      color: #292929;
    }
  }
  .e-dlg-content {
    background-color: #f8f8f8;
  }
  .e-footer-content {
    background: rgba(255, 255, 255, 1);
    border-radius: 0 0 0 8px;
    box-shadow: inset 0 1px 0 0 rgba(232, 232, 232, 1);
  }
}

/deep/.mt-dialog {
  display: none;
}
</style>
