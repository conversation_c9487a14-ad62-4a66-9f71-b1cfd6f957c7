<!--
 * @Author: your name
 * @Date: 2021-10-25 14:00:19
 * @LastEditTime: 2021-10-25 14:25:23
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\goodsOrigin\components\OriginPreview.vue
-->
<template>
  <mt-dialog
    ref="dialog"
    :header="$t('预览')"
    :enable-resize="false"
    css-class="create-proj-dialog"
    :buttons="buttons"
  >
    <div class="full-height">
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <div class="full-width dialog-subtitle mt-mb-20">
          {{ $t('基本信息') }}
        </div>
        <mt-form-item prop="supplySourceListCode" :label="$t('货源编码')">
          <mt-input
            v-model="ruleForm.supplySourceListCode"
            :show-clear-button="false"
            :readonly="true"
            :placeholder="$t('请输入货源编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="organizationName" :label="$t('公司名称')">
          <mt-input
            v-model="ruleForm.organizationName"
            :show-clear-button="false"
            :readonly="true"
            :placeholder="$t('请输入公司名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="siteName" :label="$t('工厂/地点')">
          <mt-input
            v-model="ruleForm.siteName"
            :show-clear-button="false"
            :readonly="true"
            :placeholder="$t('请输入工厂/地点')"
          ></mt-input>
        </mt-form-item>
        <div class="full-width dialog-subtitle mt-mb-20">
          {{ $t('物料信息') }}
        </div>
        <mt-form-item prop="itemCode" :label="$t('物料编码')">
          <mt-input
            v-model="ruleForm.itemCode"
            :show-clear-button="false"
            :readonly="true"
            :placeholder="$t('请输入物料编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('物料名称')">
          <mt-input
            v-model="ruleForm.itemName"
            :show-clear-button="false"
            :readonly="true"
            :placeholder="$t('请输入物料名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="categoryName" :label="$t('品类名称')">
          <mt-input
            v-model="ruleForm.categoryName"
            :show-clear-button="false"
            :readonly="true"
            :placeholder="$t('请输入品类名称')"
          ></mt-input>
        </mt-form-item>
        <div class="full-width dialog-subtitle mt-mb-20">
          {{ $t('货源信息') }}
        </div>
        <mt-form-item prop="supplierName" :label="$t('供应商名称')">
          <mt-input
            v-model="ruleForm.supplierName"
            :show-clear-button="false"
            :readonly="true"
            :placeholder="$t('请输入供应商名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商编码')">
          <mt-input
            v-model="ruleForm.supplierCode"
            :show-clear-button="false"
            :readonly="true"
            :placeholder="$t('请输入供应商编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="startTime" :label="$t('有效起始')">
          <mt-input
            v-model="ruleForm.startTime"
            :show-clear-button="false"
            :readonly="true"
            :placeholder="$t('请输入有效起始')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="endTime" :label="$t('有效截至')">
          <mt-input
            v-model="ruleForm.endTime"
            :show-clear-button="false"
            :readonly="true"
            :placeholder="$t('请输入有效截至')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      ruleForm: {},
      rules: {},

      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.hide,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  methods: {
    show(row) {
      this.ruleForm = { ...row }
      this.$refs.dialog.ejsRef.show()
    },
    hide() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-subtitle {
  border-left: 3px solid #eda133;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  padding-left: 10px;
}
</style>
