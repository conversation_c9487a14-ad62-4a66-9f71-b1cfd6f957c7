<!--
 * @Author: your name
 * @Date: 2021-09-06 10:15:13
 * @LastEditTime: 2022-02-11 17:10:57
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\permission\Role.vue
-->
<template>
  <div class="full-height mt-pt-20">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>

    <mt-dialog
      ref="curdDialog"
      :header="headerTitle"
      :enable-resize="false"
      :position="{ X: 'right', Y: 'top' }"
      css-class="create-proj-dialog right-wrapper"
      :buttons="buttons"
    >
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <!-- <div class="full-width dialog-subtitle mt-mb-20">{{ $t("创建角色") }}</div> -->
        <mt-form-item prop="roleName" :label="$t('角色名称')">
          <mt-input
            v-model="ruleForm.roleName"
            :disabled="disabled"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入角色名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="roleCode" :label="$t('角色编码')">
          <mt-input
            v-model="ruleForm.roleCode"
            :disabled="disabledCode"
            type="text"
            :placeholder="$t('请输入角色编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="roleDescription" :label="$t('角色描述')">
          <mt-input
            v-model="ruleForm.roleDescription"
            :multiline="true"
            :rows="3"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入角色描述')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </mt-dialog>

    <mt-dialog
      ref="userDialog"
      :header="subjectType ? $t('分配用户组') : $t('分配用户')"
      :enable-resize="false"
      :position="{ X: 'right', Y: 'top' }"
      css-class="create-proj-dialog right-wrapper"
      :buttons="buttonsUser"
    >
      <mt-template-page
        ref="userPage"
        :hidden-tabs="true"
        :template-config="userPageConfig"
        @handleClickToolBar="handleClickToolBarUser"
        @handleClickCellTool="handleClickCellToolUser"
      ></mt-template-page>
    </mt-dialog>

    <mt-dialog
      ref="permDialog"
      :header="$t('分配权限')"
      :enable-resize="false"
      :position="{ X: 'right', Y: 'top' }"
      css-class="create-proj-dialog right-wrapper"
      :buttons="buttonsPerm"
      @close="permDialogClose"
      @open="permDialogOpen"
    >
      <perm-assign ref="permAssign" :role-info="roleInfo" :urls="urls"></perm-assign>
    </mt-dialog>

    <mt-dialog-transfer
      v-if="showDialogTransfer"
      :header="$t('分配用户')"
      :left-title="$t('类型名称:')"
      :right-title="userGroupInfo.userGroupTypeName"
      :filter="{ orgLeveLTypeCode: orgLeveLTypeCode }"
      :visible="showDialogTransfer"
      :fields-left="fieldsLeft"
      :fields-right="fieldsRight"
      :close="closeTs"
      @save="addUserGroupFn"
      @serchData="serchDataDT"
      class="role_ts"
    >
      <template #searchLeft>
        <div class="select-box mt-flex">
          <div class="select-wrap">
            <mt-select
              :width="100"
              :data-source="userGroupType"
              :show-clear-button="false"
              :readonly="true"
              :allow-filtering="true"
              @change="groupTypeChange"
              v-model="userGroupInfo.userGroupTypeCode"
              :placeholder="$t('请选择')"
            ></mt-select>
          </div>
          <div class="mt-pl-10">
            <mt-input
              v-model="searchForm.userName"
              v-if="false"
              type="text"
              :placeholder="$t('输入名称查询')"
              @input="getTreeData"
            ></mt-input>
          </div>
        </div>
      </template>
    </mt-dialog-transfer>
  </div>
</template>

<script>
import {
  PAGE_PLUGIN,
  USER_DIALOG_PAGE_PLUGIN,
  TS_LEFT_FIELDS,
  TS_RIGHT_FIELDS,
  USER_GROUP_TYPES,
  ORG_CODE_TYPES,
  columnDataRoleUser,
  columnDataRoleUserGroup
} from './config/role.config'
import { formatRules } from '@/utils/util'
import { utils } from '@mtech-common/utils'
import PermAssign from './components/PermAssign.vue'
export default {
  components: { PermAssign },
  data() {
    return {
      pageConfig: PAGE_PLUGIN(this.$API.roles.rolePageQueryAdmin),
      userPageConfig: USER_DIALOG_PAGE_PLUGIN('/iam/admin/user-group/criteria-query'),
      fieldsLeft: TS_LEFT_FIELDS,
      fieldsRight: TS_RIGHT_FIELDS,
      userGroupType: USER_GROUP_TYPES,
      orgCodeTypes: ORG_CODE_TYPES,

      ruleForm: {},
      searchForm: {},
      rules: {},
      currentRoleId: '',
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      buttonsUser: [
        {
          click: this.cancelUser,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.saveUser,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      buttonsPerm: [
        {
          click: this.cancelPerm,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.savePerm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      type: 'add',
      typeAssign: 'add',

      roleInfo: {},
      showDialogTransfer: false,
      userGroupInfo: { userGroupTypeCode: 'USER' },
      userGroupContentList: [],
      orgLeveLTypeCode: 'USER',
      readonly: false,
      urls: {
        list: this.$API.roles.menuTreeGetAdmin,
        selectNodes: this.$API.roles.actionNodeSelectGetAdmin
      },
      subjectType: 0
    }
  },
  computed: {
    headerTitle() {
      if (this.type === 'add') {
        return this.$t('新增')
      }
      if (this.type === 'edit') {
        return this.$t('编辑')
      }
      return this.$t('预览')
    },
    disabled() {
      if (this.type === 'preview') {
        return true
      }
      return false
    },
    disabledCode() {
      if (this.type === 'add') {
        return false
      }
      return true
    }
  },
  mounted() {
    this.getTreeData = utils.debounce(this.getTreeData, 1000)
    console.log(utils.getAppCode())
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const rowSelected = grid?.getSelectedRecords?.()

      if (toolbar.id === 'Add') {
        this.handleAction('add')
        this.rulesGet('roleAddValidAdmin')
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDelete(rowSelected)
      }
    },
    handleClickCellTool(e) {
      const { data, tool } = e

      if (tool.id === 'edit') {
        this.handleAction('edit', data)
        this.rulesGet('updataRolesValid')
      }
      if (tool.id === 'userGroup' || tool.id === 'user') {
        this.subjectType = 0
        if (tool.id === 'userGroup') {
          this.subjectType = 1
        }
        this.$refs.userDialog.ejsRef.show()
        const columnData = this.subjectType ? columnDataRoleUserGroup : columnDataRoleUser
        this.userPageConfig[0].gridId = 'CAC33B3D-C975-01A8-A3CF-FA837069E968'
        this.userPageConfig[0].grid = Object.assign({}, this.userPageConfig[0].grid, {
          columnData,
          asyncConfig: {
            params: { roleId: data.id, subjectType: this.subjectType },
            url: '/iam/admin/subject-role-rel/queryPageByRoleIdAndType'
          }
        })
      }
      if (tool.id === 'perm') {
        this.roleInfo = data
        this.$refs.permDialog.ejsRef.show()
      }
      if (tool.id === 'delete') {
        this.handleDelete([data])
      }
      if (tool.id === 'preview') {
        this.handleAction('preview', data)
      }
    },
    handleAction(type, data) {
      this.type = type
      this.$refs.ruleForm.resetFields()
      this.ruleForm = Object.assign({}, data)
      this.$refs.curdDialog.ejsRef.show()
    },
    handleDelete(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.roles.batchDeleteAdmin(data).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    },
    cancel() {
      this.$refs.curdDialog.ejsRef.hide()
    },
    save() {
      if (this.type === 'preview') {
        this.cancel()
        return false
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.type === 'add') {
            const data = {
              ...this.ruleForm,
              parentId: 0
            }
            this.$API.roles.roleAddAdmin(data).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
          if (this.type === 'edit') {
            this.$API.roles.updataRolesAdmin(this.ruleForm).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
        }
      })
    },
    handleClickToolBarUser(e) {
      const { toolbar, grid } = e
      const rowSelected = grid?.getSelectedRecords?.()

      if (toolbar.id === 'Add') {
        if (!this.subjectType) {
          this.readonly = false
          this.handleActionUser('add', {
            userGroupTypeCode: 'USER',
            userGroupTypeName: this.$t('用户'),
            orgLeveLTypeCode: '_USER'
          })
        }
        if (this.subjectType) {
          this.showUserGroupDilog()
        }
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDeleteUser(rowSelected)
      }
    },
    handleClickCellToolUser(e) {
      const { data, tool } = e
      data.userGroupTypeCode = 'USER'
      data.userGroupTypeName = this.$t('用户')
      if (tool.id === 'edit') {
        this.readonly = true
        this.handleActionUser('edit', data)
      }
      if (tool.id === 'delete') {
        this.handleDeleteUser([data])
      }
      if (tool.id === 'preview') {
        this.handleActionUser('preview', data)
      }
    },
    async handleActionUser(type, data) {
      this.typeAssign = type
      this.showDialogTransfer = true
      this.userGroupInfo = Object.assign({}, data)
      await this.userGroupTypeGet()
      if (type === 'edit') {
        const query = {
          itemData: {
            text: this.userGroupInfo.userGroupTypeName,
            value: this.userGroupInfo.userGroupTypeCode
          }
        }

        await this.groupTypeChange(query)
        this.getSelectNodes(data)
      }
    },
    handleDeleteUser(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.roles.deleteGroupSAdmin(data).then(() => {
            this.$refs.userPage.refreshCurrentGridData()
          })
        }
      })
    },
    async groupTypeChange(e) {
      const { itemData } = e
      const { id } = this.userGroupInfo
      let orgLevelCode = ''

      this.orgCodeTypes.forEach((e) => {
        if (e.code === itemData.value) {
          orgLevelCode = e.orgLevelCode
          this.orgLeveLTypeCode = e.orgLevelCode
        }
      })
      this.userGroupInfo = {
        id,
        orgLevelCode,
        userGroupTypeName: itemData.text,
        userGroupTypeCode: itemData.value
      }
      await this.getTreeData()
    },
    async getTreeData() {
      const { userName } = this.searchForm
      const query = {
        userName: userName ? userName : ''
      }
      await this.$API.roles.getPlatformUserAdmin(query).then((res) => {
        this.fieldsLeft = Object.assign({}, this.fieldsLeft, {
          dataSource: res.data.map((item) => {
            return {
              ...item,
              name: item.nickName,
              id: item.userId,
              orgLeveLTypeCode: '_USER'
            }
          })
        })
      })
    },
    getSelectNodes(row) {
      const query = {
        userGroupId: row.id,
        roleId: row.roleId
      }
      this.$API.roles.userGroupCriteriaQueryAdmin(query).then((res) => {
        this.fieldsRight = Object.assign({}, this.fieldsRight, {
          dataSource: res.data.map((e) => {
            return {
              id: e.itemId,
              name: e.itemName,
              itemCode: e.itemCode
            }
          })
        })
      })
    },
    serchDataDT() {},
    closeTs() {
      this.fieldsRight = Object.assign({}, this.fieldsRight, {
        dataSource: []
      })
      this.showDialogTransfer = false
    },
    async addUserGroupFn(e) {
      const subjectIdList = e.map((item) => item.id)
      const roleId = this.userPageConfig[0].grid.asyncConfig.params.roleId
      const params = {
        applicationId: this.getApplicationId(),
        roleId,
        subjectIdList,
        subjectType: this.subjectType
      }
      const res = await this.$API.roles.setUserGroupAdmin(params)
      this.$toast({
        content: res.msg,
        type: 'success'
      })
      this.closeTs()
      this.$refs.userPage.refreshCurrentGridData()
    },
    showUserGroupDilog() {
      this.$dialog({
        modal: () => import('./components/UserGroupDialog.vue'),
        data: {
          title: this.$t('绑定用户组'),
          url: this.$API.roles.QueryUserGroupApiAdmin
        },
        success: (e) => {
          this.addUserGroupFn(e)
          // this.$refs.tepPage.refreshCurrentGridData();
        },
        close: () => {}
      })
    },
    cancelUser() {
      this.$refs.userDialog.ejsRef.hide()
    },
    saveUser() {
      this.cancelUser()
    },
    async permDialogOpen() {
      await this.$refs.permAssign.nodeAssignedGet()
      await this.$refs.permAssign.treeDataGet()
    },
    permDialogClose() {
      this.$refs.permAssign.clearDataSource()
    },
    cancelPerm() {
      this.$refs.permDialog.ejsRef.hide()
    },
    savePerm() {
      const ids = this.$refs.permAssign.nodeCheckedGet()
      const data = {
        roleId: this.roleInfo.id,
        permissionIds: ids,
        applicationCode: utils.getAppCode()
      }

      this.$API.roles.actionNodeSelectSaveAdmin(data).then(() => {
        this.$refs.tepPage.refreshCurrentGridData()
        this.cancelPerm()
      })
    },
    async userGroupTypeGet() {
      const query = { dictCode: 'userGroupType' }
      await this.$API.dict.dictTypeGet(query).then((res) => {
        this.userGroupType = res.data.map((e) => {
          return {
            text: e.itemName,
            value: e.itemCode
          }
        })
      })
    },
    rulesGet(type) {
      this.$API.roles[type]().then((res) => {
        let rules = formatRules(res.data)
        rules.roleCode.push({
          validator: this.roleCodeValitor,
          trigger: 'blur'
        })
        this.rules = rules
      })
    },
    getApplicationId() {
      const platformRole = utils.getAppCode()
      const json = this.$store.state.user.applicationList?.find(
        (e) => e.applicationCode == platformRole
      )
      return json?.id ?? '2'
    },
    roleCodeValitor(rule, value, callback) {
      const reg = /[\u4e00-\u9fa5]/
      if (reg.test(value)) {
        callback(new Error('不能包含中文字符'))
      } else {
        callback()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .e-rowcell {
  text-align: left;
}
.dialog-subtitle {
  border-left: 3px solid #00469c;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  padding-left: 10px;
}
</style>
<style lang="scss">
.e-dlg-container > .right-wrapper {
  height: 100% !important;
  .e-dlg-header-content {
    background-color: rgba(243, 243, 243, 1);
    border-radius: 8px 0 0 0;
    .e-dlg-closeicon-btn {
      .e-btn-icon {
        color: #292929 !important;
      }
    }
    .e-dlg-header {
      color: #292929;
    }
  }
  .e-dlg-content {
    background-color: #f8f8f8;
  }
  .e-footer-content {
    background: rgba(255, 255, 255, 1);
    border-radius: 0 0 0 8px;
    box-shadow: inset 0 1px 0 0 rgba(232, 232, 232, 1);
  }
}
.select-box {
  width: 250px !important;
}
</style>
