<template>
  <div class="dataScope">
    <div class="data-warper">
      <div class="data-left" :class="{ wid50: fold }">
        <div class="solt" @click="toggle">
          <MtIcon name="a-icon_Shrinkexpand" />
        </div>
        <div style="padding-left: 15px; min-width: 260px">
          <div class="buttons">
            <!-- v-permission="['O_02_0155']" -->
            <a class="btn" @click="addDataRules" v-permission="['O_02_0155']">
              <i class="mt-icons mt-icon-icon_solid_Createorder"></i>{{ $t('新增') }}
            </a>
            <a class="btn" @click="addDataRules" v-permission="['O_01_0006']">
              <i class="mt-icons mt-icon-icon_solid_Createorder"></i>{{ $t('新增') }}
            </a>
            <!-- v-permission="['O_02_0157'] -->
            <a class="btn" @click="editDataRules" v-permission="['O_02_0157']">
              <i class="mt-icons mt-icon-icon_solid_Createorder"></i>{{ $t('编辑') }}
            </a>
            <a class="btn" @click="editDataRules" v-permission="['O_01_0008']">
              <i class="mt-icons mt-icon-icon_solid_Createorder"></i>{{ $t('编辑') }}
            </a>
            <!-- v-permission="['O_02_0156', 'O_01_0007']" -->
            <a class="btn" @click="deleteDataRules" v-permission="['O_02_0156']">
              <i class="mt-icons mt-icon-icon_outline_Delete"></i>{{ $t('删除') }}
            </a>
            <a class="btn" @click="deleteDataRules" v-permission="['O_01_0007']">
              <i class="mt-icons mt-icon-icon_outline_Delete"></i>{{ $t('删除') }}
            </a>
          </div>
          <h3 class="dataTitle">{{ $t('数据权限规则') }}</h3>
          <div class="rulesFilter">
            <mt-input
              type="text"
              :placeholder="$t('规则搜索条件')"
              v-model="treeForm.permissionDataName"
            ></mt-input>
          </div>
          <div class="treeWraper">
            <ul class="cateTree">
              <li
                :class="{ active: index == cateIndex }"
                v-for="(item, index) in filedsIcon.dataSource"
                :key="index"
                @click="checkCateNode(item, index)"
              >
                {{ item.permissionDataName }}
              </li>
            </ul>
            <!-- <mt-treeView
              :fields="filedsIcon"
              :show-check-box="true"
              :node-template="Template"
              selected-nodes="1498648440037384194"
              @nodeOnCheck="nodeCheck"
              @nodeSelected="nodeClick"
              ref="tree"
            ></mt-treeView> -->
          </div>
        </div>
      </div>
      <div class="data-right" :class="{ wid100: fold }">
        <!-- <h3 class="dataTitle noMargin">{{ $t("权限规则明细") }}</h3>
        <h4>{{ itemSeltedName }}</h4> -->
        <mt-template-page
          ref="tepPage"
          :template-config="pageConfig"
          @handleClickToolBar="handleClickToolBar"
          @handleSelectTab="handleSelectTab"
          @handleClickCellTool="handleClickCellTool"
        ></mt-template-page>
      </div>
    </div>
  </div>
</template>
<script>
import { pageConfig } from './config/scope.config.js'
import treeTemplateVue from './components/treeTemplateVue.vue'
import { utils } from '@mtech-common/utils'
export default {
  data() {
    return {
      ruleForm: {},
      filedsIcon: {
        dataSource: [],
        id: 'id',
        text: 'permissionDataName',
        child: 'nodeChild'
      },
      pageConfig: pageConfig(
        this.$API.scope.QueryDataItemApi,
        this.$API.scope.QueryBindDetailGroupApi,
        this.$API.scope.QueryBindDetailUserApi
      ),
      controlScopeList: [], //管控维度
      BusinessTypeList: [], // 单据类型
      filterForm: {
        BusinessCode: null,
        ScopeCode: null
      },

      treeForm: {
        // 规则树请求参数
        applicationId: null,
        permissionDataCode: null,
        permissionDataName: null,
        permissionId: null
      },
      treeSelected: [],
      itemSeltedKey: null,
      itemSeltedName: null,
      fold: false,
      Template: function () {
        return {
          template: treeTemplateVue
        }
      },
      currentTab: 0,
      cateIndex: 0
    }
  },
  methods: {
    // 业务类型改变事件
    changeHandle({ itemData }) {
      if (!itemData) {
        this.filterForm.BusinessCode = null
      }
      this.filterForm.ScopeCode = null
      const params = {
        serviceName: itemData.serviceName ?? '',
        tableName: itemData.tableName ?? ''
      }
      this.getContralScope(params)
    },
    handleClickToolBar({ toolbar, grid }) {
      const { id } = toolbar
      if (toolbar.id == 'Add') {
        this.addDataRulesDetail()
      }
      if (toolbar.id == 'Delete') {
        const records = grid.getSelectedRecords()
        if (records.length == 0) {
          this.$toast({ content: this.$t('请选择一条明细'), type: 'warning' })
          return
        }
        const ids = records.map((item) => {
          return item.id
        })
        this.deletDetail(ids)
      }
      if (id === 'bind_user_group') {
        this.bindUserGroup()
      }
      if (id === 'bind_user') {
        this.bindUser()
      }
      if (id === 'unbind_user_group') {
        const records = grid.getSelectedRecords()
        if (records.length == 0) {
          this.$toast({
            content: this.$t('请至少选中一条记录'),
            type: 'warning'
          })
          return
        }
        this.unbindUser(records)
      }
      if (id === 'unbind_user') {
        const records = grid.getSelectedRecords()
        if (records.length == 0) {
          this.$toast({
            content: this.$t('请至少选中一条记录'),
            type: 'warning'
          })
          return
        }
        this.unbindUser(records)
      }
    },
    handleClickCellTool({ tool, data }) {
      if (tool.id == 'edit') {
        this.$dialog({
          modal: () => import('./components/EditRulesDialog.vue'),
          data: {
            title: this.$t('编辑数据规则明细'),
            data: data,
            _this: this,
            url: this.$API.scope.UpdateItemAPi,
            listUrl: [this.$API.scope.QueryTableApi, this.$API.scope.QueryDimensionApi]
          },
          success: () => {
            this.$refs.tepPage.refreshCurrentGridData()
          },
          close: () => {}
        })
      }
    },
    // 新增规则
    addDataRules() {
      // if(!this.filterForm.ScopeCode){
      //     this.$toast({ content: this.$t("请选择管控维度"), type: "warning" });
      //      return;
      // }
      this.$dialog({
        modal: () => import('@/views/permission/components/AddDataRule.vue'),
        data: {
          title: this.$t('新增数据规则'),
          data: this.treeForm,
          _this: this,
          actionUrl: this.$API.scope.QueryDataSaveApi
        },
        success: () => {
          this.getRulesTree()
        },
        close: () => {}
      })
    },
    // 新增数据规则详细
    addDataRulesDetail() {
      if (!this.itemSeltedKey) {
        this.$toast({ content: this.$t('请选择规则'), type: 'warning' })
        return
      }

      this.$dialog({
        modal: () => import('./components/AddRulesDialog.vue'),
        data: {
          title: this.$t('新增数据规则详细'),
          data: {
            permissionDataCode:
              this.itemSeltedKey ?? this.filedsIcon.dataSource[0].permissionDataCode,
            url: this.$API.scope.itemSaveApi
          },
          _this: this
        },
        success: () => {
          this.$refs.tepPage.refreshCurrentGridData()
        },
        close: () => {}
      })
    },
    // 绑定用户组
    bindUserGroup() {
      this.$dialog({
        modal: () => import('./components/UserGroupDialog.vue'),
        data: {
          title: this.$t('绑定用户组'),
          data: this.treeSelected,
          _this: this,
          url: this.$API.scope.QueryUserGroupApiTanent,
          actionUrl: this.$API.scope.DataBindApi
        },
        success: () => {
          this.$refs.tepPage.refreshCurrentGridData()
        },
        close: () => {}
      })
    },
    // 绑定用户
    bindUser() {
      this.$dialog({
        modal: () => import('./components/userDialogTenant.vue'),
        data: {
          title: this.$t('绑定用户'),
          data: this.treeSelected,
          _this: this,
          url: this.$API.scope.userTenantPageQuery,
          actionUrl: this.$API.scope.DataBindApi
        },
        success: () => {
          this.$refs.tepPage.refreshCurrentGridData()
        },
        close: () => {}
      })
    },
    async getTable() {
      const res = await this.$API.scope.QueryTableApi({})
      this.BusinessTypeList = res.data || []
    },
    async getContralScope(data) {
      const res = await this.$API.scope.QueryDimensionApi(data)
      this.controlScopeList = res.data || []
    },
    async getRulesTree() {
      const res = await this.$API.scope.QueryRulesTreeApi(this.treeForm)
      this.filedsIcon.dataSource = res.data
      this.cateIndex = 0
      const CODE = res?.data?.length > 0 ? this.returnPermissDataCode(res.data[0]['id']) : ''
      this.itemSeltedKey = CODE
      this.treeSelected = [res.data[0]['id']]
      this.refreshData(this.currentTab)
    },
    setTreeFrom() {
      const params = this.controlScopeList.filter(
        (v) => v.dimensionCode == this.filterForm.ScopeCode
      )
      if (params.length != 0) {
        const [t] = params
        const { applicationId, dimensionName, dimensionCode, id } = t
        this.treeForm.applicationId = applicationId
        this.treeForm.permissionDataCode = dimensionCode
        this.treeForm.permissionDataName = dimensionName
        this.treeForm.permissionId = id
      } else {
        this.resetTree()
      }
    },
    scopeHandle({ itemData }) {
      if (!itemData) {
        this.filterForm.ScopeCode = null
      }
      this.$nextTick(() => {
        this.setTreeFrom()
        //  this.getRulesTree()
      })
    },
    resetTree() {
      this.treeForm = {
        applicationId: null,
        permissionDataCode: null,
        permissionDataName: null,
        permissionId: null
      }
    },
    editDataRules() {
      if (this.treeSelected.length == 0) {
        this.$toast({ content: this.$t('请选择一条规则'), type: 'warning' })
        return
      }
      const [datas] = this.filedsIcon.dataSource.filter((v) => v.id == this.treeSelected[0])
      this.$dialog({
        modal: () => import('./components/AddDataRule.vue'),
        data: {
          title: this.$t('编辑数据规则'),
          data: datas,
          _this: this,
          actionUrl: this.$API.scope.QueryDataSaveApi
        },
        success: () => {
          this.getRulesTree()
        },
        close: () => {}
      })
    },
    nodeCheck(e) {
      console.log(e)
      this.$nextTick(() => {
        this.treeSelected = e
      })
    },
    nodeClick({ nodeData }) {
      if (nodeData) {
        const { id } = nodeData
        const code = this.returnPermissDataCode(id)
        const a = JSON.parse(JSON.stringify(this.pageConfig))
        a[0].grid.asyncConfig.params.permissionDataCode = code
        this.pageConfig = a
        this.itemSeltedKey = code
        this.refreshData(this.currentTab)
      }
    },
    deleteDataRules() {
      if (this.treeSelected.length == 0) {
        this.$toast({ content: this.$t('请选择一条规则'), type: 'warning' })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除规则吗？')
        },
        success: () => {
          this.deleteRulesFn().then(() => {
            this.getRulesTree()
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    },
    deleteRulesFn() {
      return new Promise((resolve, reject) => {
        this.$API.scope
          .DeleteRulesApi({
            ids: this.treeSelected
          })
          .then((res) => {
            this.$toast({ content: res.msg, type: 'success' })
            resolve(true)
          })
          .catch(() => {
            reject(false)
          })
      })
    },
    async deletDetail(data) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的明细吗？')
        },
        success: async () => {
          const res = await this.$API.scope.DeleteRulesDetailApi({ ids: data })
          if (res) {
            this.$refs.tepPage.refreshCurrentGridData()
          }
        }
      })
    },
    returnPermissDataCode(id) {
      let code = ''
      const [t] = this.filedsIcon.dataSource.filter((v) => v.id == id)
      code = t.permissionDataCode ?? ''
      this.itemSeltedName = t.permissionDataName ?? ''
      return code
    },
    toggle() {
      this.fold = !this.fold
    },
    handleSelectTab(e) {
      this.currentTab = e
      this.refreshData(e)
    },
    refreshData(e) {
      const a = JSON.parse(JSON.stringify(this.pageConfig))
      a[e].grid.asyncConfig.params.permissionDataCode = this.itemSeltedKey
      this.pageConfig = a
    },
    checkCateNode(data, index) {
      this.cateIndex = index
      const { id } = data
      this.treeSelected = [id]
      const code = this.returnPermissDataCode(id)
      this.itemSeltedKey = code
      this.refreshData(this.currentTab)
    },
    unbindUser(data) {
      this.$dialog({
        title: this.$t('提示'),
        message: this.$t('确认解绑选中的数据？'),
        success: () => {
          this.unbindServes(data)
        }
      })
    },
    unbindServes(data) {
      const t = data.map((v) => {
        return v.id
      })
      this.$API.scope.DataUnbindApi({ ids: t }).then((res) => {
        this.$toast({ content: res.msg, type: 'success' })

        this.$refs.tepPage.refreshCurrentGridData()
      })
    }
  },
  created() {
    this.getTable()
    this.getRulesTree = utils.debounce(this.getRulesTree, 500)
    this.getRulesTree()
  },
  watch: {
    'treeForm.permissionDataName': function () {
      this.getRulesTree()
    }
  }
}
</script>
<style lang="scss" scoped>
.dataScope {
  background: #fff;
  height: 100%;
}
.dataHeader {
  padding: 20px;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 8px 8px 0 0;
  // box-shadow: 0 0 10px 0 rgba(137,120,120 ,0.6);
}
.data-warper {
  display: flex;
  height: 100%;
  .data-left {
    width: 300px;
    margin-right: 10px;
    padding: 20px 15px;
    background: #fff;
    position: relative;
    position: relative;
    transition: width 0.3s linear;
    overflow: hidden;
    .solt {
      position: absolute;
      top: 28px;
      right: 8px;
      color: #98aac3;
      cursor: pointer;
    }
  }
  .data-right {
    width: calc(100% - 300px);
    border-left: 1px solid #e8e8e8;
  }
}
.buttons {
  margin-top: 5px;
  display: flex;
  flex-wrap: wrap;
}

/deep/ .mt-form {
  display: flex;
}
/deep/ .mt-form-item {
  margin-right: 20px;
}
.btn {
  background-color: transparent;
  outline: 0;
  word-break: keep-all;
  font-size: 14px;
  color: #4f5b6d;
  font-weight: 400;
  border: 0;
  margin-right: 10px;
  margin-top: 10px;
  display: block;
  cursor: pointer;
  i {
    margin-right: 5px;
  }
}
.dataTitle {
  font-weight: bold;
  padding: 20px;
  margin-top: 20px;
  padding-left: 0;
}
.noMargin {
  margin-top: 10px;
}
.rulesFilter {
  // padding-left: 35px;
}
h4 {
  font-size: 14px;
  font-weight: bold;
  padding: 0 20px;
}
/deep/ .mt-tree-view {
  width: 100% !important;
}
.data-right.wid100 {
  width: calc(100% - 30px);
}
.data-left.wid50 {
  width: 30px;
}
.top20 {
  margin-top: 10px;
}
.treeWraper {
  height: calc(100vh - 160px);
  overflow: auto;
}

.cateTree {
  list-style: none;
  margin-top: 20px;
  li {
    user-select: none;
    padding: 10px 0;
    font-size: 12px;
    padding-left: 35px;
    cursor: pointer;
    line-height: 1.5;
  }
  .active {
    // border-left: 3px solid #00469c;
    color: #00469c;
    position: relative;
    &::before {
      content: '';
      background-color: #00469c;
      position: absolute;
      top: 0;
      left: 0;
      width: 3px;
      bottom: 0;
    }
  }
}
</style>
