<!--
 * @Author: your name
 * @Date: 2021-08-19 10:07:52
 * @LastEditTime: 2022-04-08 09:48:42
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\permission\Org.vue
-->
<template>
  <div class="full-height">
    <div class="org-wrapper full-width full-height mt-pt-20 flex justify-between">
      <div class="tree-wrapper">
        <div class="mt-pa-20">
          <div class="e-input-group e-outline">
            <input
              v-model="searchForm.fuzzyName"
              class="e-input"
              type="text"
              :placeholder="$t('请输入组织名称')"
            />
            <mt-icon
              name="icon_search"
              class="mt-mr-10 search-icon cursor-pointer"
              @click.native="treeDataGet"
            ></mt-icon>
          </div>
        </div>
        <mt-common-tree
          :fields="treePlugin"
          @onButton="onButton"
          @nodeSelected="nodeSelected"
        ></mt-common-tree>
      </div>
      <div class="right-wrapper">
        <div class="header-wrapper mt-pa-20 flex justify-between">
          <div>
            <span class="code">{{ nodeInfo.orgCode }}</span>
            <span class="info">
              <span class="field-label">{{ $t('公司名称：') }}</span>
              <span>{{ nodeInfo.orgName }}</span>
              <span class="field-label">{{ $t('上级单位：') }}</span>
              <span>{{ nodeInfo.parentOrgName }}</span>
              <span class="field-label">{{ $t('归属产业群：') }}</span>
              <span>{{ $t('暂无') }}</span>
            </span>
            <div class="mt-mt-10 text-name">
              {{ nodeInfo.orgName }}
            </div>
          </div>
          <div>
            <!-- <mt-button css-class="e-flat e-danger" icon-position="Right"
              ><mt-icon
                name="icon_solid_close"
                class="mt-mr-10"
              />{{ $t("关闭") }}</mt-button
            > -->
          </div>
        </div>
        <div class="table-wrapper mt-mt-10 mt-ml-10">
          <mt-template-page
            ref="tepPage"
            :template-config="pageConfig"
            @handleClickToolBar="handleClickToolBar"
            @handleClickCellTool="handleClickCellTool"
            @handleSelectTab="handleSelectTab"
          ></mt-template-page>
        </div>
      </div>
    </div>
    <mt-dialog
      ref="nodeDialog"
      :header="$t('新增')"
      css-class="create-proj-dialog"
      :buttons="buttons"
    >
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="type" :label="$t('组织类型')">
          <mt-select
            v-model="ruleForm.type"
            :data-source="options.ORG_TYPES"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
            @change="typeChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="entityId" :label="$t('公司名称')" v-if="ruleForm.type === $t('公司')">
          <mt-select
            v-model="ruleForm.entityId"
            :fields="{ text: 'entityName', value: 'id' }"
            :data-source="options.COMPANY"
            :allow-filtering="true"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="bgName" :label="$t('事业群名称')" v-if="ruleForm.type === $t('事业群')">
          <mt-input v-model="ruleForm.bgName" type="text" :placeholder="$t('请输入')"></mt-input>
        </mt-form-item>
        <mt-form-item prop="clientName" :label="$t('板块名称')" v-if="ruleForm.type === $t('板块')">
          <mt-input
            v-model="ruleForm.clientName"
            type="text"
            :placeholder="$t('请输入')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </mt-dialog>

    <mt-dialog
      ref="departDialog"
      :header="headerTitle"
      css-class="create-proj-dialog"
      :buttons="buttonsDepart"
    >
      <mt-form ref="departForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="departmentName" :label="$t('部门名称')">
          <mt-input
            v-model="ruleForm.departmentName"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入')"
          ></mt-input>
        </mt-form-item>
        <!-- <mt-form-item prop="nameEn" :label="$t('英文名称')">
          <mt-input
            v-model="ruleForm.nameEn"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入英文名称')"
          ></mt-input>
        </mt-form-item> -->
        <mt-form-item
          prop="parentId"
          :label="$t('上级部门')"
          v-if="type === 'add' && ruleForm.parentId && ruleForm.parentId.length"
        >
          <mt-DropDownTree
            id="departDownTree"
            v-model="ruleForm.parentId"
            :fields="dropDownTree"
            filter-bar-placeholder="Search"
            :allow-filtering="true"
            :placeholder="$t('请选择')"
            :show-clear-button="false"
            @select="departDownTreeSelect"
          ></mt-DropDownTree>
          <!-- <mt-select
            v-model="ruleForm.upperDepart"
            :disabled="disabled"
            :data-source="[]"
            :show-clear-button="true"
            :placeholder="$t('请选择上级部门')"
          ></mt-select> -->
        </mt-form-item>
        <mt-form-item prop="departmentCode" :label="$t('部门编码')" v-if="type === 'preview'">
          <mt-input
            :disabled="true"
            v-model="ruleForm.departmentCode"
            type="text"
            :placeholder="$t('请输入')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="departmentLevel" :label="$t('部门级别')" v-if="type === 'preview'">
          <mt-input
            :disabled="true"
            v-model="ruleForm.departmentLevel"
            type="text"
            :placeholder="$t('请输入')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="stationNum" :label="$t('对应岗位')" v-if="type === 'preview'">
          <mt-input
            :disabled="true"
            v-model="ruleForm.stationNum"
            type="text"
            :placeholder="$t('请输入')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="employeeNum" :label="$t('部门人数')" v-if="type === 'preview'">
          <mt-input
            :disabled="true"
            v-model="ruleForm.employeeNum"
            type="text"
            :placeholder="$t('请输入')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="createUserName" :label="$t('创建人')" v-if="type === 'preview'">
          <mt-input
            :disabled="true"
            v-model="ruleForm.createUserName"
            type="text"
            :placeholder="$t('请输入')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')" v-if="type === 'preview'">
          <mt-input
            :disabled="true"
            v-model="ruleForm.createTime"
            type="text"
            :placeholder="$t('请输入')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </mt-dialog>

    <mt-dialog
      ref="stationDialog"
      :header="headerTitle"
      css-class="create-proj-dialog"
      :buttons="buttonsStation"
    >
      <mt-form ref="stationForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="standardStationName" :label="$t('岗位名称')">
          <mt-input
            v-model="ruleForm.standardStationName"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="standardStationCode" :label="$t('岗位编码')" v-if="type === 'preview'">
          <mt-input
            :disabled="true"
            v-model="ruleForm.standardStationCode"
            type="text"
            :placeholder="$t('请输入')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="createUserName" :label="$t('创建人')" v-if="type === 'preview'">
          <mt-input
            :disabled="true"
            v-model="ruleForm.createUserName"
            type="text"
            :placeholder="$t('请输入')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')" v-if="type === 'preview'">
          <mt-input
            :disabled="true"
            v-model="ruleForm.createTime"
            type="text"
            :placeholder="$t('请输入')"
          ></mt-input>
        </mt-form-item>
        <!-- <mt-form-item prop="role" :label="$t('对应角色')">
          <mt-select
            v-model="ruleForm.role"
            :disabled="disabled"
            :data-source="[]"
            :show-clear-button="true"
            :placeholder="$t('请选择对应角色')"
          ></mt-select>
        </mt-form-item> -->
      </mt-form>
    </mt-dialog>

    <list-box :params="listboxParams" ref="stationManageDialog"></list-box>
  </div>
</template>

<script>
import { i18n } from '@/main.js'

const labelClass = {
  集团: 'group',
  事业群: 'team',
  板块: 'team',
  公司: 'company',
  子公司: 'child-company'
}
function handleTreeData(data) {
  for (let i = 0; i < data.length; i++) {
    const el = data[i]

    el.label = el.oegLevelTypeName
    el.labelClass = `label-tree label-${labelClass[el.oegLevelTypeName]}`
    // el.setOperation = [{ text: i18n.t("新增") }, { text: i18n.t("删除") }];
    el.setOperation = [{ text: i18n.t('新增') }]
    if (el.parentId === '0') {
      // el.expanded = "expanded";
      el.isSelected = true
    }
    if (el.children) {
      handleTreeData(el.children)
    }
  }
  return data
}
import { TREE_PLUGIN, PAGE_PLUGIN, OPTIONS, DROPDOWN_TREE } from './config/org.config'
import { formatRules } from '@/utils/util'
import ListBox from './components/ListBox.vue'
export default {
  components: {
    ListBox
  },
  data() {
    return {
      options: OPTIONS,
      treePlugin: TREE_PLUGIN,
      pageConfig: PAGE_PLUGIN,
      dropDownTree: DROPDOWN_TREE,

      searchForm: {},
      nodeInfo: {},
      ruleForm: {
        parentId: []
      },
      rules: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      buttonsDepart: [
        {
          click: this.cancelDepart,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.saveDepart,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      buttonsStation: [
        {
          click: this.cancelStation,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.saveStation,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      type: 'add',
      nodeSelectInfo: {},
      currentTab: 0,
      listboxParams: {}
    }
  },
  computed: {
    headerTitle() {
      if (['add', 'add_row'].includes(this.type)) {
        return this.$t('新增')
      }
      if (this.type === 'edit') {
        return this.$t('编辑')
      }
      return this.$t('预览')
    },
    disabled() {
      if (this.type === 'preview') {
        return true
      }
      return false
    }
  },
  mounted() {
    this.treeDataGet()
  },
  methods: {
    treeDataGet() {
      this.$API.org
        .getAdmTree({
          fuzzyName: this.searchForm.fuzzyName,
          orgType: 'ORG001ADM'
        })
        .then((res) => {
          this.treePlugin = Object.assign({}, this.treePlugin, {
            dataSource: handleTreeData(res.data)
          })
          const rootNode = this.treePlugin.dataSource.filter((e) => e.isSelected)
          this.nodeSelected({ nodeData: rootNode[0] })
        })
    },
    onButton(e) {
      const { onBtn } = e

      if (onBtn.text === this.$t('新增')) {
        this.$refs.ruleForm.resetFields()
        this.$refs.nodeDialog.ejsRef.show()
      }
      if (onBtn.text === this.$t('删除')) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            const data = { ids: [this.nodeSelectInfo.id] }
            this.$API.org.nodeDel(data).then(() => {
              this.treeDataGet()
            })
          }
        })
      }
    },
    nodeSelected(e) {
      if (e) {
        this.nodeSelectInfo = e.nodeData
      }
      this.$API.org.getNodeInfo({ id: this.nodeSelectInfo.id }).then((res) => {
        this.nodeInfo = res.data
      })
      this.gridDataGet()
    },
    gridDataGet() {
      if (this.currentTab === 0) {
        this.pageConfig[0].treeGrid.asyncConfig.params = Object.assign(
          {},
          this.pageConfig[0].treeGrid.asyncConfig.params,
          { organizationId: this.nodeSelectInfo.id }
        )
        this.$refs.tepPage.refreshCurrentGridData()
      }
      if (this.currentTab === 1) {
        this.$set(this.pageConfig[1].grid.asyncConfig, 'defaultRules', [
          {
            field: 'organizationId',
            operator: 'equal',
            value: this.nodeSelectInfo.id
          }
        ])
        this.$refs.tepPage.refreshCurrentGridData()
      }
    },
    typeChange(e) {
      if (e.value === this.$t('公司')) {
        this.$API.org.getCompany({}).then((res) => {
          this.options.COMPANY = res.data
        })
        this.rulesGet('cNodeSaveValid')
      }
      if (e.value === this.$t('事业群')) {
        this.rulesGet('bNodeSaveValid')
      }
      if (e.value === this.$t('板块')) {
        this.rulesGet('clientNodeSaveValid')
      }
    },
    async handleClickToolBar(e) {
      const { toolbar, tabIndex, grid } = e
      const rowSelected = grid?.getSelectedRecords?.()

      if (toolbar.id === 'Add' && tabIndex === 0) {
        this.departDataGet()
        this.handleAction('add')
        this.rulesGet('departSaveValid')
      }
      if (toolbar.id === 'Add' && tabIndex === 1) {
        this.handleActionStation('add')
        this.rulesGet('stationAddSaveValid')
      }
      if (toolbar.id === 'Delete' && tabIndex === 0 && rowSelected.length) {
        this.handleDelete(rowSelected)
      }
      if (toolbar.id === 'Delete' && tabIndex === 1 && rowSelected.length) {
        this.handleDeleteStation(rowSelected)
      }
    },
    async handleClickCellTool(e) {
      const { data, tool, tabIndex } = e

      if (tool.id === 'edit' && tabIndex === 0) {
        await this.departDataGet()
        this.handleAction('edit', data)
        this.rulesGet('departEditSaveValid')
      }
      if (tool.id === 'add_child' && tabIndex === 0) {
        this.handleAddChild(data)
        this.rulesGet('departSaveValid')
      }
      if (tool.id === 'station_manage' && tabIndex === 0) {
        this.handleManageStation(data)
      }
      if (tool.id === 'delete' && tabIndex === 0) {
        this.handleDelete([data])
      }
      if (tool.id === 'preview' && tabIndex === 0) {
        this.departDataGet()
        this.handleAction('preview', data)
      }
      if (tool.id === 'edit' && tabIndex === 1) {
        this.handleActionStation('edit', data)
        this.rulesGet('stationEditSaveValid')
      }
      if (tool.id === 'delete' && tabIndex === 1) {
        this.handleDeleteStation([data])
      }
      if (tool.id === 'preview' && tabIndex === 1) {
        this.handleActionStation('preview', data)
      }
    },
    handleSelectTab(e) {
      this.currentTab = e
      this.nodeSelected()
    },
    handleAction(type, data) {
      this.type = type
      this.$refs.departForm.resetFields()
      this.$refs.departDialog.ejsRef.show()
      this.ruleForm = Object.assign(
        {},
        {
          id: data?.id || '',
          departmentCode: data?.departmentCode || '',
          departmentName: data?.name || '',
          departmentLevel: data?.departmentLevel,
          employeeNum: data?.employeeNum,
          stationNum: data?.stationNum,
          createUserName: data?.createUserName,
          createTime: data?.createTime,
          parentId: [data?.parentId || this.nodeSelectInfo.id]
        }
      )
    },
    departDownTreeSelect(e) {
      this.ruleForm.parentId = [e.itemData.id]
    },
    handleActionStation(type, data) {
      this.type = type
      // this.$refs.stationForm.resetFields();
      this.$refs.stationDialog.ejsRef.show()
      this.ruleForm = Object.assign({}, data)
    },
    handleDelete(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.org.nodeDel(data).then(() => {
            this.gridDataGet()
          })
        }
      })
    },
    handleDeleteStation(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.org.stationDelete(data).then(() => {
            this.gridDataGet()
          })
        }
      })
    },
    handleManageStation(row) {
      this.listboxParams = Object.assign(
        {},
        {
          companyOrganizationId: this.nodeSelectInfo.id,
          departmentId: row.id,
          departmentOrganizationId: row.id
        }
      )
      const dialog = this.$refs.stationManageDialog
      dialog.$refs.dialog.ejsRef.show()
    },
    handleAddChild(data) {
      this.type = 'add_row'
      this.$refs.departForm.resetFields()
      this.ruleForm = Object.assign({}, { parentId: [data.id] })
      this.$refs.departDialog.ejsRef.show()
    },
    cancel() {
      this.$refs.nodeDialog.ejsRef.hide()
    },
    save() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.ruleForm.type === this.$t('公司')) {
            const data = {
              entityId: this.ruleForm.entityId,
              parentId: this.nodeSelectInfo.id
            }
            this.$API.org.cNodeSave(data).then(() => {
              this.treeDataGet()
              this.cancel()
            })
          }
          if (this.ruleForm.type === this.$t('事业群')) {
            const data = {
              bgName: this.ruleForm.bgName,
              orgTypeCode: 'ORG001COM',
              parentId: this.nodeSelectInfo.id
            }
            this.$API.org.bNodeSave(data).then(() => {
              this.treeDataGet()
              this.cancel()
            })
          }
          if (this.ruleForm.type === this.$t('板块')) {
            const data = {
              clientName: this.ruleForm.clientName,
              orgTypeCode: 'ORG001COM',
              applicationId: 0,
              parentId: this.nodeSelectInfo.id
            }
            this.$API.org.clientNodeSave(data).then(() => {
              this.treeDataGet()
              this.cancel()
            })
          }
        }
      })
    },
    cancelDepart() {
      this.$refs.departDialog.ejsRef.hide()
    },
    saveDepart() {
      if (this.type === 'preview') {
        this.cancelDepart()
        return false
      }
      this.$refs.departForm.validate((valid) => {
        if (valid) {
          if (['add_row', 'add'].includes(this.type)) {
            const data = {
              departmentName: this.ruleForm.departmentName,
              orgTypeCode: 'ORG001ADM',
              applicationId: 0,
              parentId: this.ruleForm.parentId.join(',')
            }
            this.$API.org.departSave(data).then(() => {
              this.gridDataGet()
              this.cancelDepart()
            })
          }
          if (this.type === 'edit') {
            const data = {
              orgName: this.ruleForm.departmentName,
              organizationId: this.ruleForm.id
            }
            this.$API.org.departEditSave(data).then(() => {
              this.gridDataGet()
              this.cancelDepart()
            })
          }
        }
      })
    },
    cancelStation() {
      this.$refs.stationDialog.ejsRef.hide()
    },
    saveStation() {
      if (this.type === 'preview') {
        this.cancelStation()
        return false
      }
      this.$refs.stationForm.validate((valid) => {
        if (valid) {
          if (this.type === 'add') {
            const data = Object.assign({}, this.ruleForm, {
              organizationId: this.nodeSelectInfo.id,
              tenantId: 0
            })
            this.$API.org.stationAddSave(data).then(() => {
              this.gridDataGet()
              this.cancelStation()
            })
          }
          if (this.type === 'edit') {
            this.$API.org.stationEditSave(this.ruleForm).then(() => {
              this.gridDataGet()
              this.cancelStation()
            })
          }
        }
      })
    },
    async departDataGet() {
      const query = { organizationId: this.nodeSelectInfo.id }
      await this.$API.org
        .getDepartTreeData(query)
        .then((res) => {
          this.dropDownTree = Object.assign({}, this.dropDownTree, {
            dataSource: res.data
          })
        })
        .catch((err) => {
          this.dropDownTree = Object.assign({}, this.dropDownTree, {
            dataSource: []
          })
          console.error(err)
        })
    },
    rulesGet(type) {
      this.$API.org[type]().then((res) => {
        this.rules = formatRules(res.data)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.org-wrapper {
  display: flex;
  .tree-wrapper {
    width: 400px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px 0 0 0;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    overflow: auto;
    .e-input-group {
      /deep/.mt-icons {
        font-family: element-icons, e-icons;
        font-style: normal;
        font-variant: normal;
        font-weight: 400;
        line-height: 38px;
        text-transform: none;
      }
      .search-icon {
        color: #98aac3;
      }
    }
  }
  .right-wrapper {
    width: calc(100% - 400px);
    .header-wrapper {
      height: 84px;
      width: 100%;
      color: #9daabf;
      font-size: 14px;
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(232, 232, 232, 1);
      border-radius: 0 8px 0 0;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      .code {
        font-size: 20px;
        color: #292929;
      }
      .info {
        height: 20px;
        .field-label {
          margin-left: 30px;
        }
      }
      .text-name {
        color: #292929;
      }
    }

    .table-wrapper {
      width: calc(100% - 10px);
      height: calc(100% - 94px);
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(232, 232, 232, 1);
      border-radius: 8px 8px 0 0;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    }
  }
}

/deep/ .label-tree {
  font-family: PingFangSC;
  display: inline-block;
  width: 38px;
  height: 16px;
  line-height: 16px;
  font-size: 12px;
  text-align: center;
  border-radius: 2px;
}
/deep/ .label-group {
  background: rgba(154, 170, 193, 0.1);
  color: #9aaac1;
}
/deep/ .label-team {
  background: rgba(236, 242, 250, 1);
  color: #4e86cb;
}
/deep/ .label-company {
  background: rgba(78, 134, 203, 0.1);
  color: #5a86c6;
}
/deep/ .label-child-company {
  background: rgba(255, 154, 0, 0.1);
  color: #f99e00;
}
/deep/ .field-content {
  text-align: left;
  color: #00469c;
  font-size: 14px;
  cursor: pointer;
}
/deep/ .e-rowcell {
  text-align: left;
}
/deep/.mt-dialog {
  display: none;
}
</style>
