<!--
 * @Author: your name
 * @Date: 2021-09-06 10:15:13
 * @LastEditTime: 2022-03-22 17:15:57
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\permission\Role.vue
-->
<template>
  <div class="full-height mt-pt-20">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>

    <mt-dialog
      ref="curdDialog"
      :header="headerTitle"
      :enable-resize="false"
      :position="{ X: 'right', Y: 'top' }"
      css-class="create-proj-dialog right-wrapper"
      :buttons="buttons"
    >
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <!-- <div class="full-width dialog-subtitle mt-mb-20">{{ $t("创建角色") }}</div> -->
        <mt-form-item prop="roleName" :label="$t('角色名称')">
          <mt-input
            v-model="ruleForm.roleName"
            :disabled="disabled"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入角色名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="roleCode" :label="$t('角色编码')">
          <mt-input
            v-model="ruleForm.roleCode"
            :disabled="disabledCode"
            type="text"
            :placeholder="$t('请输入角色编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="roleDescription" :label="$t('角色描述')">
          <mt-input
            v-model="ruleForm.roleDescription"
            :multiline="true"
            :rows="3"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入角色描述')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </mt-dialog>

    <mt-dialog
      ref="userDialog"
      :header="subjectType ? $t('分配用户组') : $t('分配用户')"
      :enable-resize="false"
      :position="{ X: 'right', Y: 'top' }"
      css-class="create-proj-dialog right-wrapper user-dialog"
      :buttons="buttonsUser"
    >
      <mt-template-page
        ref="userPage"
        :hidden-tabs="true"
        :template-config="userPageConfig"
        @handleClickToolBar="handleClickToolBarUser"
        @handleClickCellTool="handleClickCellToolUser"
      ></mt-template-page>
    </mt-dialog>

    <mt-dialog
      ref="permDialog"
      :header="$t('分配权限')"
      :enable-resize="false"
      :position="{ X: 'right', Y: 'top' }"
      css-class="create-proj-dialog right-wrapper"
      :buttons="buttonsPerm"
      @close="permDialogClose"
      @open="permDialogOpen"
    >
      <perm-assign ref="permAssign" :role-info="roleInfo" :urls="urls"></perm-assign>
    </mt-dialog>

    <!-- <mt-dialog-transfer
      v-if="showDialogTransfer"
      :header="$t('分配用户组')"
      :left-title="$t('类型名称')"
      :right-title="userGroupInfo.userGroupTypeName"
      :filter="{ orgLeveLTypeCode: orgLeveLTypeCode }"
      :visible="showDialogTransfer"
      :fields-left="fieldsLeft"
      :fields-right="fieldsRight"
      :close="closeTs"
      @save="userSelectSave"
      @serchData="serchDataDT"
      class="role_ts"
    >
      <template #searchLeft>
        <div class="select-box mt-flex">
          <div class="select-wrap">
            <mt-select
              :width="100"
              :data-source="userGroupType"
              :show-clear-button="false"
              :readonly="readonly"
              :allow-filtering="true"
              @change="groupTypeChange"
              v-model="userGroupInfo.userGroupTypeCode"
              :placeholder="$t('请选择')"
            ></mt-select>
          </div>
          <div class="mt-pl-10">
            <mt-input
              v-model="searchForm.fuzzyName"
              type="text"
              :placeholder="$t('输入名称查询')"
              @input="inputChange"
            ></mt-input>
          </div> -->
    <!-- <div class="search-box">
            <mt-DropDownTree
              id="filter"
              :fields="fieldsLeft"
              filter-bar-placeholder="Search"
              :allow-filtering="true"
              :placeholder="$t('输入关键字搜索')"
            ></mt-DropDownTree>
          </div> -->
    <!-- </div>
      </template>
    </mt-dialog-transfer> -->
  </div>
</template>

<script>
import {
  PAGE_PLUGIN,
  USER_DIALOG_PAGE_PLUGIN,
  TS_LEFT_FIELDS,
  TS_RIGHT_FIELDS,
  USER_GROUP_TYPES,
  ORG_CODE_TYPES,
  columnDataRoleUser,
  columnDataRoleUserGroup
} from './config/role.config'
import { formatRules } from '@/utils/util'
import { utils } from '@mtech-common/utils'
import PermAssign from './components/PermAssign.vue'
export default {
  components: { PermAssign },
  data() {
    return {
      pageConfig: PAGE_PLUGIN(this.$API.roles.rolePageQuery),
      userPageConfig: USER_DIALOG_PAGE_PLUGIN(null),
      fieldsLeft: TS_LEFT_FIELDS,
      fieldsRight: TS_RIGHT_FIELDS,
      userGroupType: USER_GROUP_TYPES,
      orgCodeTypes: ORG_CODE_TYPES,

      ruleForm: {},
      searchForm: {},
      rules: {
        roleCode: [
          {
            required: true,
            message: this.$t('请输入角色编码'),
            triggr: 'blur'
          },
          { validator: this.roleCodeValitor, trigger: 'blur' }
        ],
        roleName: [
          {
            required: true,
            message: this.$t('请输入角色名称'),
            triggr: 'blur'
          }
        ]
      },
      currentRoleId: '',
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      buttonsUser: [
        {
          click: this.saveUser,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      buttonsPerm: [
        {
          click: this.cancelPerm,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.savePerm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      type: 'add',
      typeAssign: 'add',

      roleInfo: {},
      showDialogTransfer: false,
      userGroupInfo: {},
      userGroupContentList: [],
      orgLeveLTypeCode: '',
      readonly: false,
      urls: {
        list: this.$API.roles.menuTreeGet,
        selectNodes: this.$API.roles.actionNodeSelectGet
      },
      subjectType: 0, //当前点击的是 用户还是用户组
      isRenderPerm: false
    }
  },
  computed: {
    headerTitle() {
      if (this.type === 'add') {
        return this.$t('新增')
      }
      if (this.type === 'edit') {
        return this.$t('编辑')
      }
      return this.$t('预览')
    },
    disabled() {
      if (this.type === 'preview') {
        return true
      }
      return false
    },
    disabledCode() {
      if (this.type === 'add') {
        return false
      }
      return true
    }
  },
  mounted() {
    this.getTreeData = utils.debounce(this.getTreeData, 1000)
    this.getUserData = utils.debounce(this.getUserData, 1000)
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const rowSelected = grid?.getSelectedRecords?.()
      if (toolbar.id === 'Add') {
        this.handleAction('add')
        // this.rulesGet("roleAddValid");
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDelete(rowSelected)
      }
    },
    handleClickCellTool(e) {
      const { data, tool } = e

      if (tool.id === 'edit') {
        this.handleAction('edit', data)
        // this.rulesGet("updataRolesValid");
      }
      if (tool.id === 'userGroup' || tool.id === 'user') {
        this.subjectType = 0
        if (tool.id === 'userGroup') {
          this.subjectType = 1
        }
        this.$refs.userDialog.ejsRef.show()
        const columnData = this.subjectType ? columnDataRoleUserGroup : columnDataRoleUser
        this.userPageConfig[0].grid = Object.assign({}, this.userPageConfig[0].grid, {
          columnData,
          asyncConfig: {
            // params: { roleId: data.id ,subjectType:this.subjectType},

            params: {
              roleId: data.id,
              defaultRules: [
                {
                  label: 'roleId',
                  field: 'roleId',
                  type: 'string',
                  operator: 'equal',
                  value: data.id
                }
              ]
            },
            url: '/iam/tenant/subject-role-rel/paged-query'
          }
        })
        this.userPageConfig[0].gridId = '********-C5E0-BC38-D14D-C71CC383CC48'
      }
      if (tool.id === 'perm') {
        this.roleInfo = data
        this.$refs.permDialog.ejsRef.show()
        this.$nextTick(() => {
          this.isRenderPerm = true
        })
      }
      if (tool.id === 'delete') {
        this.handleDelete([data])
      }
      if (tool.id === 'preview') {
        this.handleAction('preview', data)
      }
    },
    handleAction(type, data) {
      this.type = type
      this.ruleForm = Object.assign({}, data)
      this.$refs.curdDialog.ejsRef.show()
      this.$refs.ruleForm.resetFields()
    },
    handleDelete(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.roles.batchDelete(data).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    },
    cancel() {
      this.$refs.curdDialog.ejsRef.hide()
    },
    save() {
      if (this.type === 'preview') {
        this.cancel()
        return false
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          console.log(this.type)
          if (this.type === 'add') {
            const data = {
              ...this.ruleForm,
              parentId: 0
            }
            this.$API.roles.roleAdd(data).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
          if (this.type === 'edit') {
            this.$API.roles.updataRoles(this.ruleForm).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
        }
      })
    },
    handleClickToolBarUser(e) {
      const { toolbar, grid } = e
      const rowSelected = grid?.getSelectedRecords?.()

      if (toolbar.id === 'Add') {
        this.readonly = false
        // this.handleActionUser("add");
        this.showUserGroupDilog()
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDeleteUser(rowSelected)
      }
    },
    handleClickCellToolUser(e) {
      const { data, tool } = e

      if (tool.id === 'edit') {
        this.readonly = true
        this.handleActionUser('edit', data)
      }
      if (tool.id === 'delete') {
        this.handleDeleteUser([data])
      }
      if (tool.id === 'preview') {
        this.handleActionUser('preview', data)
      }
    },
    async handleActionUser(type, data) {
      this.typeAssign = type
      this.showDialogTransfer = true
      this.userGroupInfo = Object.assign({}, data)
      await this.userGroupTypeGet()
      if (type === 'edit') {
        const query = {
          itemData: {
            text: this.userGroupInfo.userGroupTypeName,
            value: this.userGroupInfo.userGroupTypeCode
          }
        }

        await this.groupTypeChange(query)
        this.getSelectNodes(data)
      }
    },
    handleDeleteUser(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids, subjectType: this.subjectType }
          this.$API.roles.deleteGroupSApi(data).then(() => {
            this.$refs.userPage.refreshCurrentGridData()
          })
        }
      })
    },
    async groupTypeChange(e) {
      const { itemData } = e
      const { id } = this.userGroupInfo
      let orgLevelCode = ''

      this.orgCodeTypes.forEach((e) => {
        if (e.code === itemData.value) {
          orgLevelCode = e.orgLevelCode
          this.orgLeveLTypeCode = e.orgLevelCode
        }
      })
      this.userGroupInfo = {
        id,
        orgLevelCode,
        userGroupTypeName: itemData.text,
        userGroupTypeCode: itemData.value
      }
      if (itemData.value === 'USER') {
        await this.getUserData()
      } else {
        await this.getTreeData()
      }
    },
    async getTreeData() {
      const { fuzzyName } = this.searchForm
      const query = {
        orgLevelCode: this.userGroupInfo.orgLevelCode,
        tenantId: 100100, // 租户id
        fuzzyName,
        orgType: 'ORG001ADM'
      }
      await this.$API.roles.getStatedLimitTree(query).then((res) => {
        this.fieldsLeft = Object.assign({}, this.fieldsLeft, {
          dataSource: res.data
        })
      })
    },
    // 用户单独获取数据
    async getUserData() {
      const query = {
        userName: this.searchForm.fuzzyName
      }
      await this.$API.roles.getStatedQuery(query).then((res) => {
        this.fieldsLeft = Object.assign({}, this.fieldsLeft, {
          dataSource: res.data.map((item) => {
            return {
              ...item,
              name: item.userName,
              orgCode: item.userCode,
              orgLeveLTypeCode: '_USER'
            }
          })
        })
      })
    },
    getSelectNodes(row) {
      const query = {
        userGroupId: row.id,
        roleId: row.roleId
      }
      this.$API.roles.userGroupCriteriaQuery(query).then((res) => {
        this.fieldsRight = Object.assign({}, this.fieldsRight, {
          dataSource: res.data.map((e) => {
            return {
              id: e.itemId,
              name: e.itemName,
              itemCode: e.itemCode
            }
          })
        })
      })
    },
    serchDataDT() {},
    closeTs() {
      this.fieldsRight = Object.assign({}, this.fieldsRight, {
        dataSource: []
      })
      this.fieldsLeft = Object.assign({}, this.fieldsLeft, {
        dataSource: []
      })
      this.showDialogTransfer = false
    },
    userSelectSave(e) {
      const { userGroupTypeCode, id } = this.userGroupInfo
      const userGroupSetRequest = {
        id,
        roleId: this.userPageConfig[0].grid.asyncConfig.params.roleId || '',
        userGroupTypeCode
      }
      const userGroupItemList = e.map((item) => {
        return {
          itemId: item.id,
          itemCode: item.orgCode || item.itemCode,
          itemName: item.name
        }
      })

      const data = {
        userGroupSetRequest,
        userGroupItemList,
        applicationId: this.getApplicationId()
      }
      this.$API.roles.userGroupSet(data).then(() => {
        this.closeTs()
        this.$refs.userPage.refreshCurrentGridData()
      })
    },
    cancelUser() {
      this.$refs.userDialog.ejsRef.hide()
    },
    saveUser() {
      this.cancelUser()
    },
    async permDialogOpen() {
      await this.$refs.permAssign.nodeAssignedGet()
      await this.$refs.permAssign.treeDataGet()
    },
    permDialogClose() {
      this.$refs.permAssign.clearDataSource()
    },
    cancelPerm() {
      this.$refs.permDialog.ejsRef.hide()
      this.isRenderPerm = false
    },
    savePerm() {
      const ids = this.$refs.permAssign.nodeCheckedGet()
      const data = {
        roleId: this.roleInfo.id,
        permissionIds: ids,
        applicationCode: utils.getAppCode()
      }

      this.$API.roles.actionNodeSelectSave(data).then(() => {
        this.$refs.tepPage.refreshCurrentGridData()
        this.cancelPerm()
      })
    },
    async userGroupTypeGet() {
      const query = { dictCode: 'userGroupType' }
      await this.$API.dict.dictTypeGet(query).then((res) => {
        this.userGroupType = res.data.map((e) => {
          return {
            text: e.itemName,
            value: e.itemCode
          }
        })
      })
    },
    rulesGet(type) {
      this.$API.roles[type]().then((res) => {
        let rules = formatRules(res.data)
        rules.roleCode.push({
          validator: this.roleCodeValitor,
          trigger: 'blur'
        })
        this.rules = rules
      })
    },
    getApplicationId() {
      const platformRole = utils.getAppCode()
      const json = this.$store.state.user.applicationList?.find(
        (e) => e.applicationCode == platformRole
      )
      return json?.id ?? '2'
    },
    inputChange() {
      if (this.userGroupInfo.userGroupTypeCode == 'USER') {
        this.getUserData()
      } else {
        this.getTreeData()
      }
    },
    showUserGroupDilog() {
      if (this.subjectType) {
        this.$dialog({
          modal: () => import('./components/UserGroupDialog.vue'),
          data: {
            title: this.$t('绑定用户组'),
            url: this.$API.roles.QueryUserGroupApiTanent
          },
          success: (e) => {
            this.addUserGroupFn(e)
            // this.$refs.tepPage.refreshCurrentGridData();
          },
          close: () => {}
        })
      } else {
        this.$dialog({
          modal: () => import('./components/userDialogTenant.vue'),
          data: {
            title: this.$t('绑定用户'),
            url: this.$API.roles.rolePagedQueryNew
          },
          success: (e) => {
            this.addUserGroupFn(e)
          },
          close: () => {}
        })
      }
    },
    async addUserGroupFn(e) {
      const roleId = this.userPageConfig[0].grid.asyncConfig.params.roleId
      let arr = []
      e.map((item) => {
        arr.push({
          subjectId: item.userId,
          subjectName: item.employeeName,
          subjectCode: item.employeeCode,
          phoneNum: item.phoneNum,
          deptName: item.departmentName,
          roleId: roleId
        })
      })
      // console.log(subjectIdList, roleId, this.getApplicationId())

      // const params = {
      //   applicationId: this.getApplicationId(),
      //   roleId,
      //   subjectIdList,
      //   subjectType: this.subjectType,
      // }
      const res = await this.$API.roles.setUserGroupTanentUser(arr)
      this.$toast({
        content: res.msg,
        type: 'success'
      })
      this.$refs.userPage.refreshCurrentGridData()
    },
    roleCodeValitor(rule, value, callback) {
      const reg = /[\u4e00-\u9fa5]/
      if (reg.test(value)) {
        callback(new Error(this.$t('不能包含中文字符')))
      } else {
        callback()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../../node_modules/@mtech/dialog-transfer/build/esm/bundle.css';

/deep/ .e-rowcell {
  text-align: left;
}
.dialog-subtitle {
  border-left: 3px solid #00469c;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  padding-left: 10px;
}
</style>
<style lang="scss">
.e-dlg-container > .right-wrapper {
  height: 100% !important;
  .e-dlg-header-content {
    background-color: rgba(243, 243, 243, 1);
    border-radius: 8px 0 0 0;
    .e-dlg-closeicon-btn {
      .e-btn-icon {
        color: #292929 !important;
      }
    }
    .e-dlg-header {
      color: #292929;
    }
  }
  .e-dlg-content {
    background-color: #f8f8f8;
  }
  .e-footer-content {
    background: rgba(255, 255, 255, 1);
    border-radius: 0 0 0 8px;
    box-shadow: inset 0 1px 0 0 rgba(232, 232, 232, 1);
  }
}
.select-box {
  width: 250px !important;
}
.user-dialog .e-dlg-content .mt-form .mt-form-item {
  min-width: 140px;
}
</style>
