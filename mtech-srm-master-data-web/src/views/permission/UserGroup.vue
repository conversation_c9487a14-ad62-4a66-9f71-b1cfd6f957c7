<template>
  <div class="full-height mt-pt-20">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
    <mt-dialog
      ref="curdDialog"
      :header="headerTitle"
      :enable-resize="false"
      :position="{ X: 'right', Y: 'top' }"
      css-class="create-proj-dialog right-wrapper"
      :buttons="buttons"
    >
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="userGroupTypeCode" :label="$t('用户组类型')">
          <mt-select
            v-model="ruleForm.userGroupTypeCode"
            :disabled="disabledCode"
            :data-source="userGroupType"
            :show-clear-button="false"
            :placeholder="$t('请选择用户组类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="userGroupName" :label="$t('用户组名称')">
          <mt-input
            v-model.trim="ruleForm.userGroupName"
            :disabled="disabled"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入用户组名称')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
    <mt-dialog-transfer
      v-if="showDialogTransfer"
      :header="$t('分配用户组')"
      :left-title="$t('类型名称')"
      :right-title="userGroupInfo.userGroupTypeName"
      :visible="showDialogTransfer"
      :fields-left="fieldsLeft"
      :filter="{ orgLeveLTypeCode: orgLeveLTypeCode }"
      :fields-right="fieldsRight"
      :close="closeTs"
      @save="userSelectSave"
      class="role_ts"
    >
      <template #searchLeft>
        <div class="select-box mt-flex">
          <div class="select-wrap">
            <mt-select
              :width="100"
              :data-source="userGroupType"
              :show-clear-button="false"
              :readonly="true"
              :allow-filtering="true"
              v-model="userGroupInfo.userGroupTypeCode"
              :placeholder="$t('请选择')"
            ></mt-select>
          </div>
          <div class="mt-pl-10">
            <mt-input
              v-model="searchForm.fuzzyName"
              type="text"
              :placeholder="$t('输入名称查询')"
              @input="inputChange"
            ></mt-input>
          </div>
        </div>
      </template>
    </mt-dialog-transfer>
  </div>
</template>

<script>
import {
  USER_GROUP_CONFIG,
  USER_DIALOG_PAGE_PLUGIN,
  TS_LEFT_FIELDS,
  TS_RIGHT_FIELDS,
  USER_GROUP_TYPES,
  ORG_CODE_TYPES
} from './config/role.config'
import { utils } from '@mtech-common/utils'
export default {
  data() {
    return {
      pageConfig: USER_GROUP_CONFIG(this.$API.roles.getUserGroupApi),
      userPageConfig: USER_DIALOG_PAGE_PLUGIN,
      fieldsLeft: TS_LEFT_FIELDS,
      fieldsRight: TS_RIGHT_FIELDS,
      userGroupType: USER_GROUP_TYPES,
      orgCodeTypes: ORG_CODE_TYPES,
      ruleForm: {},
      searchForm: {},
      rules: {
        userGroupName: [
          {
            required: true,
            message: this.$t('请输入用户组名称'),
            trigger: 'blur'
          }
        ],
        userGroupTypeCode: [
          {
            required: true,
            message: this.$t('请输入用户组类型'),
            trigger: 'blur'
          }
        ]
      },
      currentRoleId: '',
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      type: 'add',
      typeAssign: 'add',
      roleInfo: {},
      showDialogTransfer: false,
      userGroupInfo: {},
      userGroupContentList: [],
      orgLeveLTypeCode: '',
      readonly: false
    }
  },
  computed: {
    headerTitle() {
      if (this.type === 'add') {
        return this.$t('新增')
      }
      if (this.type === 'edit') {
        return this.$t('编辑')
      }
      return this.$t('预览')
    },
    disabled() {
      if (this.type === 'preview') {
        return true
      }
      return false
    },
    disabledCode() {
      if (this.type === 'edit') {
        return true
      }
      return false
    }
  },
  mounted() {
    this.getTreeData = utils.debounce(this.getTreeData, 1000)
    this.getUserData = utils.debounce(this.getUserData, 1000)
    this.getEmplyeeData = utils.debounce(this.getEmplyeeData, 1000)
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const rowSelected = grid?.getSelectedRecords?.()
      if (toolbar.id === 'Add') {
        this.handleAction('add')
      }
      if (toolbar.id === 'Delete') {
        if (rowSelected.length == 0) {
          this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
          return
        }
        this.handleDelete(rowSelected)
      }
    },
    handleClickCellTool(e) {
      const { data, tool } = e
      if (tool.id === 'edit') {
        this.handleAction('edit', data)
      }
      if (tool.id === 'user') {
        this.readonly = false
        this.type = 'preview'
        const [orgLevelCodeJson] = ORG_CODE_TYPES.filter((i) => i.code == data.userGroupTypeCode)

        this.handleActionUser('add', {
          userGroupTypeCode: data.userGroupTypeCode,
          userGroupTypeName: data.userGroupTypeName,
          orgLevelCode: orgLevelCodeJson?.orgLevelCode ?? '',
          id: data.id
        })
      }
      if (tool.id === 'delete') {
        this.handleDelete([data])
      }
    },
    async handleAction(type, data) {
      this.type = type
      this.ruleForm = Object.assign({}, data)
      this.$refs.curdDialog.ejsRef.show()
      this.$refs.ruleForm.resetFields()
      await this.userGroupTypeGet()
    },
    handleDelete(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.roles.userGroupDelete(data).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    },
    cancel() {
      this.$refs.curdDialog.ejsRef.hide()
    },
    save() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.type === 'add') {
            this.$API.roles.setUserGroupBase(this.ruleForm).then((res) => {
              this.$toast({ content: res.msg, type: 'success' })
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
          if (this.type === 'edit') {
            this.$API.roles.updateUserGroupBase(this.ruleForm).then((res) => {
              this.$toast({ content: res.msg, type: 'success' })
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
        }
      })
    },
    async handleActionUser(type, data) {
      this.typeAssign = type
      this.showDialogTransfer = true
      this.searchForm.fuzzyName = ''
      this.fieldsLeft = Object.assign({}, this.fieldsLeft, {
        dataSource: []
      })
      this.userGroupInfo = Object.assign({}, data)
      this.orgLeveLTypeCode = data.orgLevelCode
      await this.userGroupTypeGet()
      if (data && data.userGroupTypeCode == 'USER') {
        await this.getUserData()
      } else if (data && data.userGroupTypeCode == 'EMPLOYEE') {
        await this.getEmplyeeData()
      } else {
        await this.getTreeData()
      }
      this.getSelectNodes(data)
    },
    async getTreeData() {
      const { fuzzyName } = this.searchForm
      const query = {
        orgLevelCode: this.userGroupInfo.orgLevelCode,
        tenantId: 100100, // 租户id
        fuzzyName,
        orgType: 'ORG001ADM'
      }
      await this.$API.roles.getStatedLimitTree(query).then((res) => {
        this.fieldsLeft = Object.assign({}, this.fieldsLeft, {
          dataSource: res.data
        })
      })
    },
    getSelectNodes(row) {
      const query = {
        userGroupId: row.id,
        roleId: row.roleId
      }
      this.$API.roles.userGroupCriteriaQuery(query).then((res) => {
        this.fieldsRight = Object.assign({}, this.fieldsRight, {
          dataSource: res.data.map((e) => {
            return {
              id: e.itemId,
              name: e.itemName,
              itemCode: e.itemCode
            }
          })
        })
      })
    },
    closeTs() {
      this.fieldsRight = Object.assign({}, this.fieldsRight, {
        dataSource: []
      })
      this.showDialogTransfer = false
    },
    userSelectSave(e) {
      const { userGroupTypeCode, id } = this.userGroupInfo
      const userGroupItemList = e.map((item) => {
        return {
          itemId: item.id,
          itemCode: item.orgCode || item.itemCode,
          itemName: item.name,
          userGroupId: id,
          userGroupTypeCode: userGroupTypeCode
        }
      })

      this.$API.roles
        .groupItemAdd({ userGroupItems: userGroupItemList, userGroupId: id })
        .then((res) => {
          this.$toast({ content: res.msg, type: 'success' })
          this.closeTs()
        })
    },
    cancelUser() {
      this.$refs.userDialog.ejsRef.hide()
    },

    async userGroupTypeGet() {
      const query = { dictCode: 'userGroupType' }
      await this.$API.dict.dictTypeGet(query).then((res) => {
        this.userGroupType = res.data.map((e) => {
          return {
            text: e.itemName,
            value: e.itemCode
          }
        })
      })
    },
    async getUserData() {
      const query = {
        userName: this.searchForm.fuzzyName ?? ''
      }
      await this.$API.roles.getMasterUser(query).then((res) => {
        this.fieldsLeft = Object.assign({}, this.fieldsLeft, {
          dataSource: res.data.map((item) => {
            return {
              ...item,
              name: item.userName,
              orgCode: item.userCode,
              orgLeveLTypeCode: '_USER'
            }
          })
        })
      })
    },
    async getEmplyeeData() {
      const query = {
        fuzzyName: this.searchForm.fuzzyName ?? ''
      }
      await this.$API.roles.getMasterEmplyee(query).then((res) => {
        this.fieldsLeft = Object.assign({}, this.fieldsLeft, {
          dataSource: res.data.map((item) => {
            return {
              ...item,
              name: `${item.employeeName}--${item.departmentOrgName}--${item.employeeCode}`,
              orgCode: item.employeeCode,
              id: item.employeeId,
              orgLeveLTypeCode: '_EMPLOYEE'
            }
          })
        })
      })
    },
    inputChange() {
      if (this.userGroupInfo.userGroupTypeCode == 'USER') {
        this.getUserData()
      } else if (this.userGroupInfo.userGroupTypeCode == 'EMPLOYEE') {
        this.getEmplyeeData()
        // this.getTreeData();
      } else {
        this.getTreeData()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .e-rowcell {
  text-align: left;
}
.dialog-subtitle {
  border-left: 3px solid #00469c;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  padding-left: 10px;
}
</style>
<style lang="scss">
.e-dlg-container > .right-wrapper {
  height: 100% !important;
  .e-dlg-header-content {
    background-color: rgba(243, 243, 243, 1);
    border-radius: 8px 0 0 0;
    .e-dlg-closeicon-btn {
      .e-btn-icon {
        color: #292929 !important;
      }
    }
    .e-dlg-header {
      color: #292929;
    }
  }
  .e-dlg-content {
    background-color: #f8f8f8;
  }
  .e-footer-content {
    background: rgba(255, 255, 255, 1);
    border-radius: 0 0 0 8px;
    box-shadow: inset 0 1px 0 0 rgba(232, 232, 232, 1);
  }
}
.select-box {
  width: 250px !important;
}
</style>
