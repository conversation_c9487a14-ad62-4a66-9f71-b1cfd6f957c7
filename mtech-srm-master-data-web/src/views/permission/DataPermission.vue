<template>
  <div class="dataScope">
    <div class="data-warper">
      <div class="data-left">
        <div style="padding-left: 15px; min-width: 260px">
          <div class="rulesFilter">
            <mt-input
              type="text"
              css-class="e-outline"
              @input="filterLocal"
              :placeholder="$t('单据名称搜索')"
              v-model="treeForm.billTypeName"
            ></mt-input>
          </div>
          <div class="treeWraper">
            <ul class="cateTree">
              <li
                :class="{ active: index == cateIndex }"
                v-for="(item, index) in dataSource"
                :key="index"
                @click="checkCateNode(item, index)"
              >
                {{ item.billTypeName }}
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="data-right">
        <!-- <h3 class="dataTitle noMargin">{{ $t("权限规则明细") }}</h3>
        <h4>{{ itemSeltedName }}</h4> -->
        <mt-template-page
          ref="tepPage"
          :template-config="pageConfig"
          @handleClickToolBar="handleClickToolBar"
          @handleSelectTab="handleSelectTab"
          @handleClickCellTool="handleClickCellTool"
          @requireAction="requireAction"
        ></mt-template-page>
      </div>
    </div>
  </div>
</template>
<script>
import { pageConfig } from './config/data.config.js'
import { utils } from '@mtech-common/utils'
export default {
  data() {
    return {
      pageConfig: pageConfig('', []),
      dataSource: [],
      cateIndex: 0,
      treeForm: {
        billTypeName: null
      },
      dataSourceCloneList: [],
      isRequireItemArray: ['demo_code'] // 已经选择维度 需要回显
    }
  },
  methods: {
    checkCateNode(item, index) {
      this.cateIndex = index
      // this.queryRequireItem(item);
      this.getDemisson(item)
    },
    filterLocal() {
      if (!this.treeForm.billTypeName) {
        this.dataSource = JSON.parse(JSON.stringify(this.dataSourceCloneList))
        return
      }
      this.dataSource = this.dataSourceCloneList.filter((e) =>
        e.billTypeName.includes(this.treeForm.billTypeName)
      )
    },
    handleClickToolBar() {},
    handleSelectTab() {},
    handleClickCellTool() {},
    requireAction(e, value) {
      this.setRequire(e, value)
    },
    async initBills() {
      try {
        this.$loading()
        const res = await this.$API.dataScope.billsListApi()
        this.$hloading()
        this.dataSource = res?.data || []
        this.dataSourceCloneList = utils.cloneDeep(this.dataSource)
        if (this.dataSource.length) {
          this.getDemisson(this.dataSource[this.cateIndex])
        }
      } catch (error) {
        this.$hloading()
      }
    },
    getDemisson(item) {
      const { serviceName, billTypeCode } = item
      const pageConfig = utils.cloneDeep(this.pageConfig)
      pageConfig[0].grid.asyncConfig.url = this.$API.dataScope.dimensionRecordsApi
      pageConfig[0].grid.asyncConfig.params = {
        serviceName,
        billTypeCode
      }
      pageConfig[0].grid.asyncConfig.serializeList = (list) => {
        return list.map((item, index) => {
          return {
            ...item,
            _index: index + 1,
            valid: !!+item.valid
          }
        })
      }
      this.pageConfig = pageConfig
    },

    async setRequire(item, enable) {
      try {
        this.$loading()
        const { dimensionCode } = item
        const { serviceName, billTypeCode } = this.dataSource[this.cateIndex]
        const { tableName } = item
        const res = await this.$API.dataScope.setDimensionIsRequire({
          serviceName,
          tableName,
          billTypeCode,
          dimensionCode,
          enable: +enable
        })
        console.log(res)
        this.$hloading()
        this.getDemisson(this.dataSource[this.cateIndex])
      } catch (error) {
        this.$hloading()
      }
    },
    searchTextFomat(n) {
      const res =
        /[`~!@#$%^&*()\+=<>?"{}|,\/;'\\[\]·~！@#￥%……&*（）——\+={}|《》？：“”【】、；‘’，。、]/g
      this.$nextTick(() => {
        this.treeForm.billTypeName = n.replace(res, '')
      })
    }
    // async queryRequireItem(item) {

    //   return new Promise((resolve,reject) =>{
    //           const { serviceName, tableName, billTypeCode } = item;
    //           const res = await this.$API.dataScope.queryRequireItem({
    //             serviceName,
    //             tableName,
    //             billTypeCode,
    //           });
    //           this.isRequireItemArray = [...res.data];
    //           resolve(res.data)
    //   })

    // },
  },
  created() {
    this.filterLocal = utils.debounce(this.filterLocal, 1000)
    this.searchTextFomat = utils.debounce(this.searchTextFomat, 200)
    this.initBills()
  },
  watch: {
    'treeForm.billTypeName': function (n) {
      this.searchTextFomat(n)
    }
  }
}
</script>
<style lang="scss" scoped>
.dataScope {
  background: #fff;
  height: 100%;
}
.dataHeader {
  padding: 20px;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 8px 8px 0 0;
  // box-shadow: 0 0 10px 0 rgba(137,120,120 ,0.6);
}
.data-warper {
  display: flex;
  height: 100%;
  .data-left {
    width: 300px;
    margin-right: 10px;
    padding: 20px 15px;
    background: #fff;
    position: relative;
    position: relative;
    transition: width 0.3s linear;
    overflow: hidden;
    .solt {
      position: absolute;
      top: 28px;
      right: 8px;
      color: #98aac3;
      cursor: pointer;
    }
  }
  .data-right {
    width: calc(100% - 300px);
    border-left: 1px solid #e8e8e8;
    padding: 0 10px 0 20px;
  }
}
.buttons {
  margin-top: 5px;
  display: flex;
  flex-wrap: wrap;
}

/deep/ .mt-form {
  display: flex;
}
/deep/ .mt-form-item {
  margin-right: 20px;
}
.btn {
  background-color: transparent;
  outline: 0;
  word-break: keep-all;
  font-size: 14px;
  color: #4f5b6d;
  font-weight: 400;
  border: 0;
  margin-right: 10px;
  margin-top: 10px;
  display: block;
  cursor: pointer;
  i {
    margin-right: 5px;
  }
}
.dataTitle {
  font-weight: bold;
  padding: 20px;
  margin-top: 20px;
  padding-left: 0;
}
.noMargin {
  margin-top: 10px;
}
.rulesFilter {
  // padding-left: 35px;
}
h4 {
  font-size: 14px;
  font-weight: bold;
  padding: 0 20px;
}
/deep/ .mt-tree-view {
  width: 100% !important;
}
.data-right.wid100 {
  width: calc(100% - 30px);
}
.data-left.wid50 {
  width: 30px;
}
.top20 {
  margin-top: 10px;
}
.treeWraper {
  height: calc(100vh - 160px);
  overflow: auto;
}

.cateTree {
  list-style: none;
  margin-top: 20px;
  li {
    user-select: none;
    padding: 10px 0;
    font-size: 14px;
    padding-left: 10px;
    cursor: pointer;
    line-height: 1.5;
  }
  .active {
    // border-left: 3px solid #00469c;
    color: #00469c;
    position: relative;
    background-color: rgba(0, 70, 156, 0.16078431372549);
  }
}
</style>
