import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-09-14 15:42:57
 * @LastEditTime: 2022-04-29 18:52:05
 * @LastEditors: OBKoro1
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\permission\config\site.config.js
 */
export const PAGE_CONFIG = [
  {
    toolbar: [
      'Add',
      // "Delete",
      {
        id: 'Active',
        icon: 'icon_solid_Createproject',
        title: i18n.t('激活')
      },
      {
        id: 'Negative',
        icon: 'icon_solid_Createproject',
        title: i18n.t('失效')
      }
    ],
    grid: {
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'siteCode',
          headerText: i18n.t('地点编码'),
          width: '200',
          // cellTools: ["edit", "delete", "preview"],
          cellTools: ['edit', 'preview']
        },
        {
          field: 'siteName',
          width: '200',
          headerText: i18n.t('地点名称')
        },
        {
          field: 'siteTypeCode',
          width: '200',
          headerText: i18n.t('地点类型编码')
        },
        {
          field: 'siteTypeName',
          width: '200',
          headerText: i18n.t('地点类型名称')
        },
        {
          field: 'parentName',
          width: '150',
          headerText: i18n.t('归属公司')
        },
        {
          field: 'parentCode',
          width: '150',
          headerText: i18n.t('归属公司编码')
        },
        {
          field: 'statusId',
          headerText: i18n.t('状态'),
          width: '150',
          valueConverter: {
            type: 'map',
            map: {
              0: i18n.t('草稿'),
              1: i18n.t('激活'),
              2: i18n.t('分发'),
              3: i18n.t('失效'),
              '-1': i18n.t('待审核'),
              '-2': i18n.t('准备')
            }
          }
        },
        {
          field: 'createUserName',
          width: '150',
          headerText: i18n.t('创建人')
        },
        {
          field: 'createTime',
          headerText: i18n.t('创建时间')
          // valueConverter: {
          //   type: "date",
          // },
        }
      ],
      asyncConfig: {
        url: `/masterDataManagement/tenant/site/paged-query?BU_CODE=${localStorage.getItem(
          'currentBu'
        )}`,
        params: {}
      }
    }
  }
]

export const FIELDS = {
  COMPANY: {
    text: 'orgName',
    value: 'id'
  },
  SITE_TYPE: {
    text: 'itemName',
    value: 'itemCode'
  }
}
export const RULES = {}

export const OPTIONS = {
  COMPANY: [],
  SITE_TYPE: []
}
