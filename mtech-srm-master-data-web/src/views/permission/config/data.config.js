import { i18n } from '@/main'
import Vue from 'vue'
import inputView from '../components/inputView.vue'
export const pageConfig = (i) => [
  {
    useToolTemplate: false,
    toolbar: [],
    grid: {
      lineIndex: true,
      columnData: columnData(),
      asyncConfig: {
        params: {},
        url: i
      }
    }
  }
]
export const userPageConfig = (i) => [
  {
    useToolTemplate: false,
    toolbar: [],
    grid: {
      columnData: [
        {
          type: 'checkbox',
          width: 50
        },
        {
          field: 'userName',
          headerText: i18n.t('姓名')
        },
        {
          field: 'telephone',
          headerText: i18n.t('电话'),
          template: () => ({ template: inputView })
        },
        {
          field: 'userCode',
          headerText: i18n.t('工号')
        },
        {
          field: 'email',
          headerText: i18n.t('邮箱'),
          template: () => ({ template: inputView })
        }
      ],
      asyncConfig: {
        params: {},
        url: i
      }
    }
  }
]

export const columnData = () => {
  return [
    {
      field: 'dimensionName',
      headerText: i18n.t('维度名称')
    },
    {
      field: 'tableName',
      headerText: i18n.t('表名称')
    },
    {
      field: 'valid',
      headerText: i18n.t('是否需要'),
      ignore: true,
      template: function (data) {
        return {
          template: Vue.component('isRequire', {
            template: `<mt-checkbox class="group-item" v-model="isRequire" @change="changeHandle"></mt-checkbox>`,
            data: function () {
              return {
                data
              }
            },
            created() {
              this.isRequire = this.data.valid
            },
            methods: {
              changeHandle(e) {
                this.$parent.$emit('requireAction', this.data, e.checked)
              }
            }
          })
        }
      }
    }
  ]
}

export const columnDataSetting = [
  {
    width: 50,
    type: 'checkBox'
  },
  {
    field: '_index',
    headerText: i18n.t('序号'),
    ignore: true,
    width: 100
  },
  {
    field: 'userName',
    headerText: i18n.t('账号'),
    ignore: true
  },
  {
    field: 'company',
    headerText: i18n.t('公司')
  },
  {
    field: 'department',
    headerText: i18n.t('部门')
  },

  {
    field: 'station',
    headerText: i18n.t('岗位')
  },
  {
    field: 'userCode',
    headerText: i18n.t('工号')
  },
  {
    field: 'fullName',
    headerText: i18n.t('姓名')
  },
  {
    field: 'mobile',
    headerText: i18n.t('电话'),
    template: () => ({ template: inputView })
  },
  {
    field: 'mail',
    headerText: i18n.t('邮箱'),
    template: () => ({ template: inputView })
  },
  {
    field: 'k',
    headerText: i18n.t('权限分配'),
    ignore: true,
    template: function (data) {
      return {
        template: Vue.component('isRequire', {
          template: `<span @click="action" style="color:#00469c;cursor: pointer;">{{ i18n.t("点击分配") }}</span>`,
          data: function () {
            return {
              data,
              i18n
            }
          },
          created() {},
          methods: {
            action() {
              this.$parent.$emit('handleClickCellTitle', {
                field: 'action',
                data: this.data
              })
            }
          }
        })
      }
    }
  }
]

export const settingPageConfig = (i) => [
  {
    title: i18n.t('用户'),
    useToolTemplate: false,
    toolbar: [
      {
        id: 'add',
        title: i18n.t('新增')
      },
      {
        id: 'delete',
        title: i18n.t('删除')
      },
      {
        id: 'import',
        title: i18n.t('导入')
      }
      // {
      //   id: "export",
      //   title: i18n.t("导出"),
      // },
    ],
    gridId: '39496cef-340d-4ca8-b175-605c78211b52',
    grid: {
      columnData: columnDataSetting,
      asyncConfig: {
        params: { subjectType: 0 },
        url: i,
        serializeList: (list) => {
          return list.map((item, index) => {
            return {
              ...item,
              _index: index + 1
            }
          })
        }
      }
    }
  }
  // {
  //   title: i18n.t("用户组"),
  //   useToolTemplate: false,
  //   toolbar: [
  //     {
  //       id: "add_group",
  //       title: i18n.t("新增"),
  //     },
  //     {
  //       id: "delete_group",
  //       title: i18n.t("删除"),
  //     },
  //   ],
  //   grid: {
  //     columnData: [
  //       {
  //         type: "checkbox",
  //         width: 50,
  //       },
  //       {
  //         field: "userGroupName",
  //         headerText: i18n.t("用户组名称"),
  //       },
  //       {
  //         field: "userGroupTypeName",
  //         headerText: i18n.t("用户组类型名称"),
  //       },
  //       {
  //         field: "k",
  //         headerText: i18n.t("权限分配"),
  //         ignore: true,
  //         template: function (data) {
  //           return {
  //             template: Vue.component("isRequire", {
  //               template: `<span @click="action" style="color:#00469c;cursor: pointer;"> {{i18n.t("点击分配") }} </span>`,
  //               data: function () {
  //                 return {
  //                   data,
  //                   i18n,
  //                 };
  //               },
  //               created() {},
  //               methods: {
  //                 action() {
  //                   this.$parent.$emit("handleClickCellTitle", {
  //                     field: "action",
  //                     data: this.data,
  //                     type: "userGroup",
  //                   });
  //                 },
  //               },
  //             }),
  //           };
  //         },
  //       },
  //     ],
  //     lineIndex: true,
  //     asyncConfig: {
  //       params: { subjectType: 1 },
  //       url: v,
  //     },
  //   },
  // },
]

// const columnDataSet = (fieldName) => [
//   {
//     field: fieldName,
//     headerText: i18n.t("编号-名称"),
//     textAlign: "left",
//   },
//   {
//     type: "checkbox",
//     width: 50,
//   },
//   // {
//   //   field: "isRequire",
//   //   headerText: i18n.t("是否配置"),
//   //   template: function (data) {
//   //     return {
//   //       template: Vue.component("isRequire", {
//   //         template: `<span  class="e-treecell">
//   //         <div class="e-checkbox-wrapper e-css">
//   //           <input class="e-checkselect e-focus" type="checkbox">
//   //           <span class="e-frame e-icons" :class="{'e-uncheck': !data.vaild,'e-check':data.vaild == 1}"></span>
//   //           <span class="e-label"> </span>
//   //         </div>
//   //       </span>`,
//   //         data: function () {
//   //           return {
//   //             data,
//   //           };
//   //         },
//   //       }),
//   //     };
//   //   },
//   // },
// ];
// const columnDataSetUrl = [
//   {
//     field: "name",
//     headerText: i18n.t("编号-名称"),
//   },
//   // {
//   //   field: "isRequire",
//   //   headerText: i18n.t("是否配置"),
//   //   template: function (data) {
//   //     return {
//   //       template: Vue.component("isRequire", {
//   //         template: `<span  class="e-treecell">
//   //         <div class="e-checkbox-wrapper e-css">
//   //           <input class="e-checkselect e-focus" type="checkbox">
//   //           <span class="e-frame e-icons e-uncheck"></span>
//   //           <span class="e-label"> </span>
//   //         </div>
//   //       </span>`,
//   //         data: function () {
//   //           return {
//   //             data,
//   //           };
//   //         },
//   //       }),
//   //     };
//   //   },
//   // },
// ];
// export const dataSetPageConfig =   (url, dataSource,fieldName = "name",rowDataBound,dataBound)=>{
//   console.log(fieldName)
//   if (!url) {
//     // dataSource = dataSource.map((item) => {
//     //   if(selectedRowIds.includes(item.id)){
//     //      item.vaild = 1;
//     //   }
//     //   return {
//     //     ...item
//     //   }
//     // })
//     return [
//       {
//         useToolTemplate: false,
//         treeGrid: {
//           allowPaging: false,
//           columnData: columnDataSet(fieldName),
//           childMapping: "children",
//           // autoCheckHierarchy: true,
//           dataSource,
//           rowDataBound:function(e){
//             console.log(e,333333)
//           },
//           dataBound,
//         },
//       },
//     ];
//   }
//   if (url) {
//     return [
//       {
//         useToolTemplate: false,

//         grid: {
//           columnData: columnDataSet(fieldName),
//           rowDataBound,
//           dataBound,
//           asyncConfig: {
//             params: {},
//             url,
//             // serializeList:(list) =>{
//             //     list  = list.map((item)=>{
//             //      if(selectedRowIds.includes(item.id)){
//             //        item.vaild = 1;
//             //      }
//             //      return {
//             //        ...item,
//             //        vaild:1
//             //      }
//             //    })
//             //    return list;
//             // }
//           },
//           rowDataBound:function(e){
//             console.log(e,333333)
//           }
//         },
//       },
//     ];
//   }
// };

export const dataSetPageConfig = (rowDataBound, dataBound) => [
  {
    useToolTemplate: false,
    toolbar: {
      tools: [[], ['ResetSearch', 'quickSearch', 'Refresh', 'Setting']]
    },
    treeGrid: {
      allowPaging: false,
      autoCheck: false,
      autoCheckHierarchy: false,
      columnData: [
        {
          field: 'itemName',
          headerText: '名称',
          textAlign: 'left'
        },
        {
          type: 'checkbox'
        }
      ],
      childMapping: 'children',
      dataSource: [],
      rowDataBound: rowDataBound,
      dataBound: dataBound
    }
  }
]

export const dataSetPageConfigPage = (rowDataBound, dataBound) => [
  {
    useToolTemplate: false,
    toolbar: [],
    grid: {
      ignoreColumns: ['parentName'],
      columnData: [
        {
          field: 'itemName',
          headerText: '名称',
          textAlign: 'left'
        },
        {
          type: 'checkbox'
        }
      ],
      rowDataBound,
      dataBound,
      dataSource: [],
      asyncConfig: {
        params: {},
        url: ''
      }
    }
  }
]

export const columnDataSetFn = (field, otherField = []) =>
  [
    {
      type: 'checkbox',
      width: '50',
      textAlign: 'left'
    },
    {
      field: field.code,
      headerText: i18n.t('编号'),
      textAlign: 'left'
    },
    {
      field: field.name,
      headerText: i18n.t('名称'),
      textAlign: 'left'
    }
  ].concat([...otherField])

export const pageConfigItem = [
  {
    useToolTemplate: false,
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [
        [
          { id: 'select', title: i18n.t('选择物料') },
          { id: 'add', title: i18n.t('新增物料') },
          { id: 'delete', title: i18n.t('删除') }
        ],
        []
      ]
    },
    grid: {
      allowPaging: false,
      columnData: [
        { type: 'checkbox', width: 50 },
        {
          field: '_index',
          headerText: i18n.t('序号'),
          textAlign: 'left',
          cellTools: [
            {
              id: 'edit',
              title: i18n.t('编辑'),
              visibleCondition: (data) => {
                return data.dimensionValueSource == 1 && data.local
              }
            },
            { id: 'delete', title: i18n.t('删除') }
          ]
        },
        {
          field: 'dimensionValueSource',
          headerText: i18n.t('新增类型'),
          textAlign: 'left',
          valueConverter: {
            type: 'map',
            map: {
              0: i18n.t('物料主数据'),
              1: i18n.t('物料类')
            }
          }
        },
        {
          field: 'dimensionCodeValue',
          headerText: i18n.t('物料编码'),
          textAlign: 'left'
        },
        {
          field: 'dimensionNameValue',
          headerText: i18n.t('物料/物料类名称'),
          textAlign: 'left'
        }
      ],
      dataSource: []
    }
  }
]

export const pageConfigTotal = () => [
  {
    useToolTemplate: false,
    toolbar: [],
    grid: {
      lineIndex: true,
      // frozenColumns: 2,
      columnData: [
        {
          field: 'employeeName',
          headerText: i18n.t('姓名'),
          cellTools: [
            {
              id: 'feature',
              title: '查看功能权限'
            },
            {
              id: 'data',
              title: '查看数据权限'
            }
          ]
        },
        {
          field: 'externalCode',
          headerText: i18n.t('账号'),
          // ignore: true,
          searchOptions: {
            operator: 'likeright'
          }
        },
        {
          field: 'companyName',
          headerText: i18n.t('公司'),
          ignore: true
        },
        {
          field: 'departmentName',
          headerText: i18n.t('部门'),
          ignore: true
        },

        {
          field: 'stationName',
          headerText: i18n.t('岗位'),
          ignore: true
        }
      ],
      asyncConfig: {
        params: {},
        url: '/masterDataManagement/tenant/organization/organizationEmployeePagedQuery'
      }
    }
  }
]
