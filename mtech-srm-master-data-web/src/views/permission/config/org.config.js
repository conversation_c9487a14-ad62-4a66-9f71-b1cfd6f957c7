import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-08-24 15:18:49
 * @LastEditTime: 2021-12-17 17:26:08
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\permission\config\org.config.js
 */
export const TREE_PLUGIN = {
  dataSource: [],
  id: 'id',
  text: 'name',
  child: 'children',
  selected: 'isSelected'
}

export const PAGE_PLUGIN = [
  {
    title: i18n.t('部门信息'),
    toolbar: {
      tools: [['Add'], ['Refresh']]
    },
    treeGrid: {
      columnData: [
        // {
        //   width: "50",
        //   type: "checkbox",
        // },
        {
          field: 'name',
          headerText: i18n.t('部门名称'),
          width: '350',
          cellTools: [
            {
              id: 'add_child',
              icon: 'icon_solid_Createproject',
              title: i18n.t('新增下级')
            },
            {
              id: 'station_manage',
              icon: 'icon_solid_Createproject',
              title: i18n.t('岗位管理')
            },
            'edit',
            // "delete",
            'preview'
          ]
        },
        {
          field: 'departmentCode',
          width: '200',
          headerText: i18n.t('部门编码')
        },
        // {
        //   field: "englishName",
        //   width: "200",
        //   headerText: i18n.t("英文名称"),
        // },
        {
          field: 'departmentLevel',
          width: '200',
          headerText: i18n.t('部门级别')
        },
        {
          field: 'stationNum',
          width: '200',
          headerText: i18n.t('对应岗位')
        },
        {
          field: 'employeeNum',
          width: '200',
          headerText: i18n.t('部门人数')
        },
        {
          field: 'createUserName',
          width: '200',
          headerText: i18n.t('创建人')
        },
        {
          field: 'createTime',
          width: '200',
          headerText: i18n.t('创建时间')
        }
      ],
      asyncConfig: {
        url: '/masterDataManagement/tenant/organization/getCompanyDepartmentTree',
        params: {},
        recordsPosition: 'data'
      },
      childMapping: 'children'
    }
  },
  {
    title: i18n.t('岗位信息'),
    toolbar: ['Add', 'Delete'],
    grid: {
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'standardStationName',
          headerText: i18n.t('岗位名称'),
          width: '200',
          cellTools: ['edit', 'delete', 'preview']
        },
        {
          field: 'standardStationCode',
          width: '200',
          headerText: i18n.t('岗位编码')
        },
        {
          field: 'createUserName',
          width: '200',
          headerText: i18n.t('创建人')
        },
        {
          field: 'createTime',
          headerText: i18n.t('创建时间')
        }
      ],
      asyncConfig: {
        url: '/masterDataManagement/tenant/standard-station/paged-query',
        defaultRules: [
          {
            field: 'organizationId',
            operator: 'equal',
            value: ''
          }
        ],
        params: {}
      }
    }
  }
]

export const OPTIONS = {
  ORG_TYPES: [
    { text: i18n.t('事业群'), value: '事业群' },
    { text: i18n.t('公司'), value: '公司' },
    { text: i18n.t('板块'), value: '板块' }
  ],
  COMPANY: []
}

export const DROPDOWN_TREE = {
  dataSource: [],
  value: 'id',
  text: 'name',
  child: 'children'
}
