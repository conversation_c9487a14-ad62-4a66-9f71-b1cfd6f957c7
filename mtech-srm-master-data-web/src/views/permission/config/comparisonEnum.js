// import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2022-04-06 13:43:25
 * @LastEditTime: 2022-04-06 13:54:37
 * @LastEditors: your name
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\permission\config\comparisonEnum.js
 */
import { FilterItemApi } from '../../../apis/modules/scope'

export const ConditionFn = async () => {
  const res = await FilterItemApi()
  return res.data
}
