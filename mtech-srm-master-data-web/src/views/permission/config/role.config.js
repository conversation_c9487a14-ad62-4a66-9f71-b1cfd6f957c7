import { i18n } from '@/main.js'
export const rolePermissionList = []
/*
 * @Author: your name
 * @Date: 2021-08-24 15:18:49
 * @LastEditTime: 2021-12-29 17:14:26
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\permission\config\org.config.js
 */
export const PAGE_PLUGIN = (url) => [
  {
    toolbar: ['Add', 'Delete'],
    grid: {
      columnData: [
        { type: 'checkbox', width: 50 },
        {
          field: 'roleName',
          headerText: i18n.t('角色名称'),
          width: '400',
          cellTools: [
            {
              id: 'user',
              icon: 'icon_solid_Createproject',
              title: i18n.t('分配用户')
            },
            {
              id: 'userGroup',
              icon: 'icon_solid_Createproject',
              title: i18n.t('分配用户组')
            },
            {
              id: 'perm',
              icon: 'icon_solid_Createproject',
              title: i18n.t('分配权限')
            },
            'edit',
            'delete',
            'preview'
          ]
        },
        {
          field: 'roleCode',
          width: '200',
          headerText: i18n.t('角色编码')
        },
        {
          field: 'sn',
          width: '200',
          headerText: i18n.t('状态'),
          valueConverter: {
            type: 'map',
            map: { 0: i18n.t('启用'), 1: i18n.t('禁用') }
          }
        },
        {
          field: 'roleDescription',
          width: '200',
          headerText: i18n.t('角色描述')
        },
        {
          field: 'createTime',
          width: '200',
          headerText: i18n.t('创建时间')
        }
      ],
      asyncConfig: {
        url,
        params: {}
      }
    }
  }
]
export const columnDataRoleUser = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'subjectName',
    headerText: i18n.t('用户名称'),
    width: '200'
    // cellTools: ["edit", "delete"],
  },
  {
    field: 'subjectCode',
    width: '200',
    headerText: i18n.t('工号')
  },
  {
    field: 'deptName',
    width: '200',
    headerText: i18n.t('部门')
  },
  {
    field: 'subjectType',
    width: '150',
    headerText: i18n.t('类型'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('用户'), 1: i18n.t('用户组') }
    }
  }
]
export const columnDataRoleUserGroup = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'subjectName',
    headerText: i18n.t('用户组名称'),
    width: '200'
    // cellTools: ["edit", "delete"],
  },
  {
    field: 'id',
    width: '200',
    headerText: i18n.t('用户组编码')
  },
  {
    field: 'subjectType',
    width: '150',
    headerText: i18n.t('类型'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('用户'), 1: i18n.t('用户组') }
    }
  }
]
export const USER_DIALOG_PAGE_PLUGIN = (url) => {
  return [
    {
      toolbar: ['Add', 'Delete'],
      grid: {
        allowPaging: true,
        columnData: columnDataRoleUser,
        asyncConfig: {
          url,
          params: {}
        }
      }
    }
  ]
}

export const PERM_DIALOG_PAGE_PLUGIN = function (dataBound, rowDataBound) {
  return [
    {
      title: i18n.t('功能权限'),
      treeGrid: {
        allowPaging: false,
        autoCheckHierarchy: true,
        // selectionSettings: { type: "Multiple" },
        columnData: [
          {
            field: 'name',
            headerText: i18n.t('权限名称'),
            width: '350'
          },
          {
            field: 'permissionTypeName',
            width: '200',
            headerText: i18n.t('权限类型')
          },
          {
            field: 'businessType',
            headerText: i18n.t('所属类型'),
            width: '80',
            valueAccessor: (field, data) => {
              let name
              switch (data.businessType) {
                case 0:
                  name = i18n.t('通用')
                  break
                case 1:
                  name = i18n.t('采方')
                  break
                case 2:
                  name = i18n.t('供方')
                  break
                default:
                  name = ''
                  break
              }
              return name
            }
          },
          {
            field: 'permissionDescription',
            width: '80',
            headerText: i18n.t('备注')
          },
          {
            width: '50',
            type: 'checkbox'
          }
        ],
        dataSource: [],
        childMapping: 'children',

        rowDataBound,
        dataBound
      }
    }
    // TODO: 暂时不做
    // {
    //   title: i18n.t("字段权限"),
    //   treeGrid: {
    //     columnData: [
    //       {
    //         field: "roleName",
    //         headerText: i18n.t("菜单名称"),
    //         width: "350",
    //         cellTools: [
    //           {
    //             id: "setting",
    //             icon: "icon_solid_Createproject",
    //             title: i18n.t("配置")
    //           }
    //         ]
    //       },
    //       {
    //         field: "roleCode",
    //         width: "200",
    //         headerText: i18n.t("类型")
    //       }
    //     ],
    //     asyncConfig: {
    //       url: "/iam/tenant/role/paged-query",
    //       params: {}
    //     },
    //     childMapping: "children"
    //   }
    // }
  ]
}

export const USER_GROUP_TYPES = []

export const ORG_CODE_TYPES = [
  { code: 'EMPLOYEE', text: i18n.t('员工'), orgLevelCode: '_EMPLOYEE' },
  { code: 'STATION', text: i18n.t('岗位'), orgLevelCode: 'ORG04' },
  { code: 'DEPART', text: i18n.t('部门'), orgLevelCode: 'ORG03' },
  { code: 'USER', text: i18n.t('用户'), orgLevelCode: '_USER' }
]

export const TS_LEFT_FIELDS = {
  dataSource: [],
  id: 'id',
  text: 'name',
  child: 'children',
  iconCss: 'icon',
  imageUrl: 'image',
  value: 'id'
}

export const TS_RIGHT_FIELDS = {
  dataSource: []
}

export const TS_LEFT_FIELDS_MASTER = {
  dataSource: [],
  id: 'id',
  text: 'userName',
  child: 'children',
  iconCss: 'icon',
  imageUrl: 'image',
  value: 'id'
}

export const USER_GROUP_CONFIG = (url) => [
  {
    toolbar: ['Add', 'Delete'],
    grid: {
      columnData: [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'userGroupName',
          headerText: i18n.t('用户组名称'),
          width: '200px',
          cellTools: [
            'edit',
            'delete',
            {
              id: 'user',
              title: i18n.t('分配用户组'),
              icon: 'icon_solid_Createproject'
            }
          ]
        },
        {
          field: 'userGroupTypeName',
          width: '200',
          headerText: i18n.t('用户组类型')
        },
        {
          field: 'createUserName',
          width: '200',
          headerText: i18n.t('创建人')
        },
        {
          field: 'createTime',
          width: '200',
          headerText: i18n.t('创建时间')
        },
        {
          field: 'updateTime',
          width: '200',
          headerText: i18n.t('更新时间')
        }
      ],
      asyncConfig: {
        url,
        params: {}
      }
    }
  }
]

export const PERM_DIALOG_PAGE_PLUGIN_REVIEW = function (dataBound, rowDataBound, actionComplete) {
  return [
    {
      treeGrid: {
        allowPaging: false,
        autoCheckHierarchy: true,
        // selectionSettings: { type: "Multiple" },
        columnData: [
          {
            field: 'name',
            headerText: i18n.t('权限名称'),
            width: '350'
          },
          {
            field: 'permissionTypeName',
            width: '200',
            headerText: i18n.t('权限类型')
          },
          {
            width: '50',
            type: 'checkbox'
          }
        ],
        dataSource: [],
        childMapping: 'children',

        rowDataBound,
        dataBound,
        actionComplete
      }
    }
  ]
}

export const PERM_DIALOG_PAGE_PLUGIN_REVIEW_EMPTY = [
  {
    treeGrid: {
      allowPaging: false,
      autoCheckHierarchy: true,
      columnData: [
        {
          field: 'name',
          headerText: i18n.t('权限名称'),
          width: '350'
        },
        {
          field: 'permissionTypeName',
          width: '200',
          headerText: i18n.t('权限类型')
        },
        {
          width: '50',
          type: 'checkbox'
        }
      ],
      dataSource: [],
      childMapping: 'children'
    }
  }
]
