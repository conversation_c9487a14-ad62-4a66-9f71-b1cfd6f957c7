import { i18n } from '@/main.js'
import { ConditionFn } from './comparisonEnum'
let enums = []
ConditionFn().then((res) => {
  enums = res
})

export const columnData = () => {
  return [
    {
      width: 50,
      type: 'checkbox'
    },
    {
      field: 'id',
      headerText: 'ID',
      width: 200,
      cellTools: ['edit']
    },
    {
      field: 'dimensionCode1',
      headerText: i18n.t('用户组')
    },
    {
      field: 'dimensionCode',
      headerText: i18n.t('管控维度')
    },
    {
      field: 'permissionDataCode',
      headerText: i18n.t('管控规则')
    },
    {
      field: 'ruleInCondition',
      headerText: i18n.t('比较条件'),
      valueConverter: {
        type: 'function',
        filter: (data) => {
          const [t] = enums.filter((v) => v.value == data)
          return t.label
        }
      }
    },

    {
      field: 'ruleOutCondition',
      headerText: i18n.t('逻辑运算符'),
      valueConverter: {
        type: 'map',
        map: {
          0: 'AND',
          1: 'OR'
        }
      }
    },
    {
      field: 'ruleValue',
      headerText: i18n.t('规则值')
    },
    {
      field: 'isLimitField',
      headerText: i18n.t('是否固定'),
      valueConverter: {
        type: 'map',
        map: {
          0: i18n.t('不限制'),
          1: i18n.t('限制')
        }
      }
    },
    {
      field: 'isEnable',
      headerText: i18n.t('是否启用'),
      valueConverter: {
        type: 'map',
        map: {
          0: i18n.t('禁用'),
          1: i18n.t('启用')
        }
      }
    },
    {
      field: 'itemOrder',
      headerText: i18n.t('顺序')
    },
    {
      field: 'serviceName',
      headerText: i18n.t('服务名称')
    },
    {
      field: 'tableName',
      headerText: i18n.t('业务表名称')
    }
  ]
}

export const columnDataUserGroup = [
  {
    width: 50,
    type: 'checkBox'
  },
  {
    field: 'userGroupName',
    headerText: i18n.t('用户组名称'),
    width: '230'
  },
  {
    field: 'userGroupContent',
    headerText: i18n.t('内容'),
    width: '230'
  },
  {
    field: 'userGroupTypeName',
    headerText: i18n.t('用户组类型')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('创建人')
  }
]

export const columnDataUserTenant = [
  {
    width: 50,
    type: 'checkBox'
  },
  {
    field: 'employeeName',
    headerText: i18n.t('用户名称'),
    width: 95
  },
  {
    field: 'externalCode',
    headerText: i18n.t('账号'),
    width: 95
  },
  {
    field: 'departmentName',
    headerText: i18n.t('部门')
  },
  {
    field: 'phoneNum',
    headerText: i18n.t('电话'),
    width: 100
  },
  {
    field: 'employeeCode',
    headerText: i18n.t('工号'),
    width: 100
  },
  {
    field: 'statusId',
    headerText: i18n.t('激活状态'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return e?.label
      }
    }
  }
]
export const columnDataUser = [
  {
    width: 50,
    type: 'checkBox'
  },
  {
    field: 'userName',
    headerText: i18n.t('用户名称')
  },
  {
    field: 'nickName',
    headerText: i18n.t('昵称')
  },
  {
    field: 'accountId',
    headerText: i18n.t('账号ID')
  }
]
const relationConfig = [
  {
    width: 50,
    type: 'checkBox'
  },
  {
    field: 'subjectName',
    headerText: i18n.t('名称')
  },
  {
    field: 'createTime',
    headerText: i18n.t('绑定时间')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('绑定人')
  }
]
export const pageConfig = (i, m, n) => [
  {
    title: i18n.t('明细'),
    useToolTemplate: false,
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [
        [
          {
            id: 'Add',
            title: i18n.t('新增'),
            permission: ['O_02_0158']
          },
          {
            id: 'Add',
            title: i18n.t('新增'),
            permission: ['O_01_0009']
          },
          {
            id: 'Delete',
            title: i18n.t('删除'),
            permission: ['O_02_0159']
          },
          {
            id: 'Delete',
            title: i18n.t('删除'),
            permission: ['O_01_0010']
          }
        ],
        ['filter', 'refresh', 'setting']
      ]
    },
    dataSource: [],
    grid: {
      columnData: columnData(),
      asyncConfig: {
        params: {},
        url: i
      }
    }
  },
  {
    title: i18n.t('用户组'),
    useToolTemplate: false,
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [
        [
          {
            id: 'bind_user_group',
            title: i18n.t('绑定用户组'),
            permission: ['O_02_0154']
          },
          {
            id: 'bind_user_group',
            title: i18n.t('绑定用户组'),
            permission: ['O_01_0005']
          },
          {
            id: 'unbind_user_group',
            title: i18n.t('解绑用户组')
            // permission: ["O_01_0009"],
          }
        ],
        ['filter', 'refresh', 'setting']
      ]
    },
    dataSource: [],
    grid: {
      columnData: relationConfig,
      asyncConfig: {
        params: {},
        url: m
      }
    }
  },
  {
    title: i18n.t('用户'),
    useToolTemplate: false,
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [
        [
          {
            id: 'bind_user',
            title: i18n.t('绑定用户')
            // permission: ["O_02_0153"],
          },
          {
            id: 'bind_user',
            title: i18n.t('绑定用户'),
            permission: ['O_01_0004']
          },
          {
            id: 'unbind_user',
            title: i18n.t('解绑用户')
            // permission: ["O_01_0009"],
          }
        ],
        ['filter', 'refresh', 'setting']
      ]
    },
    dataSource: [],
    grid: {
      columnData: relationConfig,
      asyncConfig: {
        params: {},
        url: n
      }
    }
  }
]
