<!--
 * @Author: your name
 * @Date: 2021-09-14 15:41:41
 * @LastEditTime: 2022-01-25 17:16:59
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\permission\Site.vue
-->
<template>
  <div class="full-height mt-pt-20">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
    <mt-dialog ref="dialog" :header="headerTitle" css-class="create-proj-dialog" :buttons="buttons">
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="siteName" :label="$t('地点名称')">
          <mt-input
            v-model="ruleForm.siteName"
            :disabled="disabled"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入地点名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="siteCode" :label="$t('地点编码')">
          <mt-input
            v-model="ruleForm.siteCode"
            :disabled="type !== 'add'"
            type="text"
            :placeholder="$t('请输入地点编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="siteTypeCode" :label="$t('地点类型')">
          <mt-select
            ref="companySelector"
            v-model="ruleForm.siteTypeCode"
            :disabled="disabled"
            :fields="fields.SITE_TYPE"
            :data-source="options.SITE_TYPE"
            :show-clear-button="true"
            :allow-filtering="true"
            :placeholder="$t('请选择地点类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="parentId" :label="$t('所属公司')">
          <mt-select
            ref="companySelector"
            v-model="ruleForm.parentId"
            :disabled="disabled"
            :fields="fields.COMPANY"
            :data-source="options.COMPANY"
            :show-clear-button="true"
            :allow-filtering="true"
            :placeholder="$t('请选择所属公司')"
            @change="companyChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="parentCode" :label="$t('公司编码')">
          <mt-input
            v-model="ruleForm.parentCode"
            :disabled="true"
            type="text"
            :placeholder="$t('请输入公司编码')"
          ></mt-input>
        </mt-form-item>
        <!-- <mt-form-item prop="bankAccount" :label="$t('详细地址')">
          <mt-input
            v-model="ruleForm.bankAccount"
            :disabled="disabled"
            type="text"
            :placeholder="$t('请输入详细地址')"
          ></mt-input>
        </mt-form-item> -->
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import { PAGE_CONFIG, RULES, OPTIONS, FIELDS } from './config/site.config'
import { formatRules } from '@/utils/util'
export default {
  data() {
    return {
      options: OPTIONS,
      pageConfig: PAGE_CONFIG,
      rules: RULES,
      fields: FIELDS,

      pageInfo: {
        current: 1,
        size: 10
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {},
      type: 'add' // add edit preview
    }
  },
  computed: {
    headerTitle() {
      if (this.type === 'add') {
        return this.$t('新增')
      }
      if (this.type === 'edit') {
        return this.$t('编辑')
      }
      return this.$t('预览')
    },
    disabled() {
      if (this.type === 'preview') {
        return true
      }
      return false
    }
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const rowSelected = grid.getSelectedRecords()
      if (toolbar.id === 'Add') {
        this.handleAction('add')
        this.rulesGet('siteAddValid')
        this.companyGet()
        this.TenantDictTypeGet('SITE_TYPE', 'SITE-TYPE')
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDelete(rowSelected)
      }
      if (toolbar.id === 'Active' && rowSelected.length) {
        this.handleStatus('active', rowSelected)
      }
      if (toolbar.id === 'Negative' && rowSelected.length) {
        this.handleStatus('negative', rowSelected)
      }
    },
    handleClickCellTool(e) {
      const { data, tool } = e
      if (tool.id === 'edit') {
        this.handleAction('edit', data)
        this.rulesGet('siteEditValid')
        this.companyGet()
        this.TenantDictTypeGet('SITE_TYPE', 'SITE-TYPE')
      }
      if (tool.id === 'delete') {
        this.handleDelete([data])
      }
      if (tool.id === 'preview') {
        this.handleAction('preview', data)
      }
    },
    handleAction(type, data) {
      this.type = type
      this.$refs.ruleForm.resetFields()
      this.ruleForm = Object.assign({}, data)
      this.$refs.dialog.ejsRef.show()
    },
    handleStatus(type, rows) {
      const dict = {
        active: { label: this.$t('激活'), value: 1 },
        negative: { label: this.$t('失效'), value: 3 }
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: `确认${dict[type]?.label}选中的数据？`
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids, statusId: dict[type]?.value }
          this.$API.permission.siteStatusUpdate(data).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    },
    handleDelete(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.permission.siteDelete(data).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    },
    companyChange(e) {
      this.ruleForm.parentCode = e.itemData.orgCode
    },
    save() {
      if (this.type === 'preview') {
        this.cancel()
        return false
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.type === 'add') {
            const data = {
              ...this.ruleForm
            }
            this.$API.permission.siteAdd(data).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
          if (this.type === 'edit') {
            this.$API.permission.siteEdit(this.ruleForm).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
        }
      })
    },
    cancel() {
      this.$refs.dialog.ejsRef.hide()
    },
    rulesGet(type) {
      this.$API.permission[type]().then((res) => {
        this.rules = formatRules(res.data)
      })
    },
    companyGet() {
      this.$API.permission.companyGet().then((res) => {
        this.options = Object.assign({}, this.options, { COMPANY: res.data })
      })
    },
    TenantDictTypeGet(option, dictCode) {
      const query = { dictCode }
      this.$API.dict.TenantDictTypeGet(query).then((res) => {
        this.options[option] = res.data
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/.btn-text {
  color: #4f5b6d;
}
/deep/ .mt-data-grid {
  .status-label {
    font-size: 12px;
    padding: 4px;
    border-radius: 2px;
    &.status-0 {
      color: #9baac1;
      background: rgba(155, 170, 193, 0.1);
    }
    &.status-1 {
      color: #6386c1;
      background: rgba(99, 134, 193, 0.1);
    }
  }
}
/deep/.mt-dialog {
  display: none;
}
</style>
