<template>
  <div class="full-height mt-pt-20">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
    <mt-dialog
      ref="permDialog"
      :header="header"
      :enable-resize="false"
      :position="{ X: 'right', Y: 'top' }"
      css-class="create-proj-dialog right-wrapper"
      @close="permDialogClose"
      :buttons="buttonsPerm"
    >
      <div class="feature-wrapper feature-content">
        <div class="feature-left">
          <ul class="role-list" v-if="roleInculesList.length">
            <li
              v-for="(item, index) in roleInculesList"
              :class="{ active: index === index_ }"
              :key="index"
              @click="changeIndex(index)"
            >
              {{ item.roleName }}
            </li>
          </ul>
          <span class="noDataTip" v-else>{{ $t('暂无权限') }}</span>
        </div>
        <div class="feature-right" v-show="roleInculesList.length">
          <permAssignDisabled
            ref="permAssign"
            :role-info="roleInfo"
            :urls="urls"
          ></permAssignDisabled>
        </div>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import { pageConfigTotal as pageConfig } from './config/data.config.js'

import permAssignDisabled from './components/permAssignDisabled.vue'
// import { formatRules } from "@/utils/util";
export default {
  components: { permAssignDisabled },
  data() {
    return {
      pageConfig: pageConfig(this.$API.dataScope.getPermissionTotal),
      roleInfo: {},
      urls: {
        list: this.$API.roles.selectMenuTreeGet,
        selectNodes: this.$API.roles.actionNodeSelectGet
      },
      permShow: false,
      modalData: {},
      roleInculesList: [],
      buttonsPerm: [
        {
          click: this.cancelPerm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      index_: 0,
      emplyeeName: ''
    }
  },
  methods: {
    handleClickCellTool(e) {
      const { data, tool } = e
      if (tool.id === 'feature') {
        this.featureDialogShow(data)
      }
      if (tool.id === 'data') {
        this.dataDialogShow(data)
      }
    },
    featureDialogShow(data) {
      // this.modalData = data;
      this.emplyeeName = data.employeeName
      this.getRoleListById(data)
      this.show()
    },
    dataDialogShow(data) {
      this.$dialog({
        modal: () => import('./components/dataPermDialog.vue'),
        data,
        success: () => {},
        close: () => {}
      })
    },
    cancelPerm() {
      this.$refs.permDialog.ejsRef.hide()
    },
    show() {
      this.$refs.permDialog.ejsRef.show()
    },
    changeIndex(index) {
      if (index === this.index_) return
      this.index_ = index
      this.$refs.permAssign?.clearDataSource()
      this.roleInfo = this.roleInculesList[this.index_]
      if (this.roleInculesList.length) {
        this.permDialogOpen()
      }
    },
    async getRoleListById(data) {
      // ?? '17706479458443265'
      const param = {
        userId: data.userId,
        tenantId: this.$store.state.user?.tenantId ?? '17706479458443265'
      }
      const res = await this.$API.dataScope.getRoleListQuery(param)
      this.roleInculesList = res.data || []
      this.changeIndex(0)
      this.permDialogOpen()
    },
    async permDialogOpen() {
      if (!this.roleInculesList.length) return
      await this.$refs.permAssign.nodeAssignedGet(this.roleInculesList[this.index_])
    },
    permDialogClose() {
      this.$refs.permAssign.clearDataSource()
    }
  },
  computed: {
    header() {
      return `${this.emplyeeName}&nbsp;&nbsp;&nbsp;&nbsp;${this.$t('功能权限汇总')}`
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../../node_modules/@mtech/dialog-transfer/build/esm/bundle.css';

/deep/ .e-rowcell {
  text-align: left;
}
.dialog-subtitle {
  border-left: 3px solid #00469c;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  padding-left: 10px;
}
</style>
<style lang="scss">
.create-proj-dialog .e-dlg-content {
  padding-top: 0 !important;
}
.e-dlg-container > .right-wrapper {
  height: 100% !important;
  .e-dlg-header-content {
    background-color: rgba(243, 243, 243, 1);
    border-radius: 8px 0 0 0;
    .e-dlg-closeicon-btn {
      .e-btn-icon {
        color: #292929 !important;
      }
    }
    .e-dlg-header {
      color: #292929;
    }
  }
  .e-dlg-content {
    background-color: #f8f8f8;
  }
  .e-footer-content {
    background: rgba(255, 255, 255, 1);
    border-radius: 0 0 0 8px;
    box-shadow: inset 0 1px 0 0 rgba(232, 232, 232, 1);
  }
}
.select-box {
  width: 250px !important;
}
.feature-wrapper {
  display: flex;
  height: 100%;
  padding-top: 20px;
  .feature-left {
    width: 220px;
    padding-right: 20px;
    height: 100%;
    overflow-x: auto;
    .role-list {
      li {
        cursor: pointer;
        font-size: 16px;
        line-height: 1.5;
        padding: 10px 0 10px 10px;
      }
      li.active {
        background-color: rgba(0, 70, 156, 0.161);
        color: #00469c;
        position: relative;
        border-radius: 4px;
      }
    }
  }
  .feature-right {
    flex: 1;
    height: 100%;
  }
}
.feature-wrapper.feature-content {
  .e-spinner-pane.e-spin-show {
    display: none;
  }
  .e-checkbox-wrapper .e-frame,
  .e-css.e-checkbox-wrapper .e-frame {
    background-color: #f1f1f1;
    border-color: #bdbdbd;
  }
  .e-grid .e-rowcell .e-css.e-checkbox-wrapper {
    pointer-events: none;
    cursor: default;
  }
  .e-checkbox-wrapper .e-frame.e-check,
  .e-css.e-checkbox-wrapper .e-frame.e-check {
    border-color: #bdbdbd;
    color: #00469c;
  }
  .e-checkbox-wrapper.e-checkbox-disabled .e-frame,
  .e-css.e-checkbox-wrapper.e-checkbox-disabled .e-frame {
    background-color: #fff;
    border-color: #bdbdbd;
    color: #bdbdbd;
  }
  .e-grid td.e-rowcell .e-check {
    background-color: #bdbdbd !important;
  }
  .e-columnheader .e-css.e-checkbox-wrapper {
    pointer-events: none;
    cursor: default;
  }

  .template-wrap {
    .e-content {
      height: calc(100vh - 200px) !important;
    }
  }
}
.emplyeeName {
  line-height: 50px;
  font-weight: bold;
  font-size: 18px;
}
.noDataTip {
  font-size: 16px;
}
</style>
