<template>
  <div class="dataScope">
    <div class="data-warper">
      <mt-template-page
        ref="tepPage"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleSelectTab="handleSelectTab"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
      ></mt-template-page>

      <mt-dialog-transfer
        v-if="showDialogTransfer"
        :header="$t('添加用户')"
        :left-title="$t('类型名称:')"
        :right-title="emplyeeTypeInfo.emplyeeTypeName"
        :visible="showDialogTransfer"
        :fields-left="fieldsLeft"
        :fields-right="fieldsRight"
        :close="closeTs"
        @save="userSelectSave"
        @serchData="serchDataDT"
        class="role_ts"
      >
        <template #searchLeft>
          <div class="select-box mt-flex">
            <div class="select-wrap">
              <mt-select
                :width="100"
                :data-source="emplyeeTypeList"
                :show-clear-button="false"
                :readonly="true"
                :allow-filtering="true"
                v-model="emplyeeTypeInfo.emplyeeTypeCode"
                :placeholder="$t('请选择')"
              ></mt-select>
            </div>
            <div class="mt-pl-10">
              <mt-input
                v-model="searchForm.fuzzyName"
                type="text"
                :placeholder="$t('输入名称查询')"
                @input="inputChange"
              ></mt-input>
            </div>
          </div>
        </template>
      </mt-dialog-transfer>
    </div>
    <input
      type="file"
      accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
      ref="uploadExcel"
      v-show="1 > 2"
      @change="importExecl"
    />
  </div>
</template>
<script>
import { settingPageConfig, userPageConfig } from './config/data.config.js'
import { utils } from '@mtech-common/utils'
import { download, getHeadersFileName } from '@/utils/file.js'
export default {
  data() {
    return {
      pageConfig: settingPageConfig(
        this.$API.dataScope.queryUserPageAllow,
        this.$API.dataScope.queryUserGroupPageAllow
      ),
      userPageConfig: userPageConfig(this.$API.dataScope.getEmployeeApi),
      rules: {
        userId: [{ required: true, message: this.$t('请选择用户'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formParam: {
        userId: null
      },
      userList: [],
      emplyeeTypeList: [{ text: this.$t('用户'), value: 0 }],
      emplyeeTypeInfo: {
        emplyeeTypeCode: 0,
        emplyeeTypeName: this.$t('用户')
      },
      showDialogTransfer: false,
      fieldsLeft: {
        dataSource: [],
        id: 'uid',
        text: 'fullEmployeeName',
        child: 'children',
        iconCss: 'icon',
        imageUrl: 'image',
        value: 'id'
      },
      fieldsRight: {
        dataSource: []
      },
      searchForm: {}
    }
  },
  methods: {
    handleClickToolBar({ toolbar, grid }) {
      if (toolbar.id == 'add') {
        this.addUsers()
      }
      if (toolbar.id == 'delete') {
        const rowSelected = grid.getSelectedRecords()
        if (!rowSelected.length) {
          this.$toast({
            content: this.$t('请选择一条记录'),
            type: 'warning'
          })
          return
        }
        const subjectIdList = rowSelected.map((e) => e.subjectId)
        this.deleteUsers(subjectIdList, 0)
      }
      if (toolbar.id === 'add_group') {
        this.addUserGroup()
      }
      if (toolbar.id === 'delete_group') {
        const rowSelected = grid.getSelectedRecords()
        if (!rowSelected.length) {
          this.$toast({
            content: this.$t('请选择一条记录'),
            type: 'warning'
          })
          return
        }
        const subjectIdList = rowSelected.map((e) => e.subjectId)
        this.deleteUsers(subjectIdList, 1)
      }
      if (toolbar.id === 'export') {
        this.exportExcel()
      }
      if (toolbar.id === 'import') {
        // this.$refs.uploadExcel.click();
        this.handleClickUpload()
      }
    },
    //导入
    handleClickUpload() {
      console.log('导入')
      this.$dialog({
        modal: () =>
          import(/* webpackChunkName: "components/upload" */ './components/locationUpload.vue'),
        data: {
          title: this.$t('上传')
        },
        success: () => {
          this.$refs.tepPage.refreshCurrentGridData()
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        }
      })
    },
    handleSelectTab() {},
    handleClickCellTool() {},
    handleClickCellTitle(e) {
      const { field, data, type } = e
      if (field == 'action') {
        this.actionPermission(data, type)
      }
    },
    actionPermission(data, type) {
      if (type === 'userGroup') {
        const { subjectId, userGroupName, userGroupTypeName } = data
        window.localStorage.setItem(
          'RoleCurrent',
          JSON.stringify({
            subjectId,
            userGroupName,
            userGroupTypeName
          })
        )
        this.$router.push({
          path: '/masterdata/data-permission-setting',
          query: {
            subjectId,
            subjectType: 1
          }
        })
        return
      }
      const { subjectId, company, department, fullName, station, userCode } = data
      window.localStorage.setItem(
        'RoleCurrent',
        JSON.stringify({
          subjectId,
          company,
          department,
          fullName,
          station,
          userCode
        })
      )
      this.$router.push({
        path: '/masterdata/data-permission-setting',
        query: {
          subjectId,
          subjectType: 0
        }
      })
    },
    addUsers() {
      this.dialogShow()
    },
    dialogShow() {
      this.showDialogTransfer = true
      this.getEmplyeeList()
      // this.$dialog({
      //   modal: () => import("./components/userDialogTenantNew.vue"),
      //   data: {
      //     title: this.$t("添加用户"),
      //   },
      //   success: () => {
      //     this.$refs.tepPage.refreshCurrentGridData();
      //   },
      //   close: () => {},
      // });
    },
    deleteUsers(data, type) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          this.deleteHandle(data, type)
        }
      })
    },
    async deleteHandle(subjectIdList, type) {
      const res = await this.$API.dataScope.deleteRoleAPi({
        subjectIdList,
        subjectType: type
      })
      this.$toast({
        content: res.msg,
        type: 'success'
      })
      this.$refs.tepPage.refreshCurrentGridData()
    },
    showDialog() {
      this.$refs.permDialog.ejsRef.show()
    },
    hideDialog() {
      this.$refs.permDialog.ejsRef.hide()
    },
    closeTs() {
      this.fieldsRight = Object.assign({}, this.fieldsRight, {
        dataSource: []
      })
      this.fieldsLeft = Object.assign({}, this.fieldsLeft, {
        dataSource: []
      })
      this.searchForm.fuzzyName = ''
      this.showDialogTransfer = false
    },
    async save() {
      const subjectIdList = utils.cloneDeep(this.formParam.userId)
      const res = await this.$API.dataScope.addUserAllow({
        subjectIdList,
        subjectType: 0
      })
      if (res.code == 200) {
        this.$toast({ content: res.msg, type: 'success' })
        this.$refs.tepPage.refreshCurrentGridData()
        this.formParam.userId = null
      }
    },
    serchDataDT() {},
    async userSelectSave(e) {
      const subjectIdList = e.map((item) => {
        return item.uid
      })
      if (!subjectIdList.length) return
      const res = await this.$API.dataScope.addUserAllow({
        subjectIdList,
        subjectType: 0
      })
      console.log(res)
      this.closeTs()
      this.$refs.tepPage.refreshCurrentGridData()
    },
    cancel() {
      this.hideDialog()
    },
    async getEmplyeeList() {
      const res = await this.$API.dataScope.getMasteremployeeList(this.searchForm)
      this.fieldsLeft = Object.assign({}, this.fieldsLeft, {
        dataSource: res.data.map((i) => {
          return {
            ...i,
            fullEmployeeName: `${i.employeeName}——${i.departmentOrgName}——${i.phoneNum}`
          }
        })
      })
    },
    inputChange() {
      this.getEmplyeeList()
    },
    async addUserGroup() {
      this.$dialog({
        modal: () => import('./components/UserGroupDialog.vue'),
        data: {
          title: this.$t('绑定用户组'),
          url: this.$API.roles.QueryUserGroupApiTanent
        },
        success: (e) => {
          this.addUserGroupFn(e)
          // this.$refs.tepPage.refreshCurrentGridData();
        },
        close: () => {}
      })
    },
    async addUserGroupFn(e) {
      const subjectIdList = e.map((d) => d.id)
      const res = await this.$API.dataScope.addUserAllow({
        subjectIdList,
        subjectType: 1
      })
      if (res.code == 200) {
        this.$toast({ content: res.msg, type: 'success' })
        this.$refs.tepPage.refreshCurrentGridData()
        this.formParam.userId = null
      }
    },
    async exportExcel() {
      const res = await this.$API.dataScope.exportExcelLoad()
      const fileName = getHeadersFileName(res)
      download({ fileName: `${fileName}`, blob: res.data })
    },
    async importExecl(event) {
      const _files = event.target.files
      const lastIndex = _files['0'].name.lastIndexOf('.')
      const text = _files['0'].name.substring(lastIndex + 1, _files['0'].name.length)
      if (text !== 'xlsx') {
        // console.log('格式为非xslx')
        this.$toast({
          content: this.$t('您选择需要上传的文件不是.xslx格式')
        })
        return
      }
      const params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (_files.length < 1) {
        this.$toast({
          content: this.$t('您未选择的文件不是')
        })
        return
      }
      const _data = new FormData()
      let isOutOfRange = false
      for (let i = 0; i < _files.length; i++) {
        _data.append('upload', _files[i])
        if (_files[i].size > params.limit * 1024) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$toast({
          content: params.msg
        })
      }
      const res = await this.$API.dataScope.importExcelLoad(_data)
      this.$toast({
        content: res.msg,
        type: 'success'
      })
      this.$refs.tepPage.refreshCurrentGridData()
    }
  },
  created() {},
  mounted() {
    this.getEmplyeeList = utils.debounce(this.getEmplyeeList, 1000)
  },
  watch: {}
}
</script>
<style lang="scss" scoped>
@import '../../../node_modules/@mtech/dialog-transfer/build/esm/bundle.css';

/deep/ .e-rowcell {
  text-align: left;
}
.dialog-subtitle {
  border-left: 3px solid #00469c;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  padding-left: 10px;
}
</style>
<style lang="scss">
.e-dlg-container > .right-wrapper {
  height: 100% !important;
  .e-dlg-header-content {
    background-color: rgba(243, 243, 243, 1);
    border-radius: 8px 0 0 0;
    .e-dlg-closeicon-btn {
      .e-btn-icon {
        color: #292929 !important;
      }
    }
    .e-dlg-header {
      color: #292929;
    }
  }
  .e-dlg-content {
    background-color: #f8f8f8;
  }
  .e-footer-content {
    background: rgba(255, 255, 255, 1);
    border-radius: 0 0 0 8px;
    box-shadow: inset 0 1px 0 0 rgba(232, 232, 232, 1);
  }
}
.select-box {
  width: 250px !important;
}
.dataScope {
  background: #fff;
  height: 100%;
}
.dataHeader {
  padding: 20px;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 8px 8px 0 0;
  // box-shadow: 0 0 10px 0 rgba(137,120,120 ,0.6);
}
.data-warper {
  display: flex;
  height: 100%;
  .data-left {
    width: 300px;
    margin-right: 10px;
    padding: 20px 15px;
    background: #fff;
    position: relative;
    position: relative;
    transition: width 0.3s linear;
    overflow: hidden;
    .solt {
      position: absolute;
      top: 28px;
      right: 8px;
      color: #98aac3;
      cursor: pointer;
    }
  }
  .data-right {
    width: calc(100% - 300px);
    border-left: 1px solid #e8e8e8;
  }
}
.buttons {
  margin-top: 5px;
  display: flex;
  flex-wrap: wrap;
}

/deep/ .mt-form {
  display: flex;
}
/deep/ .mt-form-item {
  margin-right: 20px;
}
.btn {
  background-color: transparent;
  outline: 0;
  word-break: keep-all;
  font-size: 14px;
  color: #4f5b6d;
  font-weight: 400;
  border: 0;
  margin-right: 10px;
  margin-top: 10px;
  display: block;
  cursor: pointer;
  i {
    margin-right: 5px;
  }
}
.dataTitle {
  font-weight: bold;
  padding: 20px;
  margin-top: 20px;
  padding-left: 0;
}
.noMargin {
  margin-top: 10px;
}
.rulesFilter {
  // padding-left: 35px;
}
h4 {
  font-size: 14px;
  font-weight: bold;
  padding: 0 20px;
}
/deep/ .mt-tree-view {
  width: 100% !important;
}
.data-right.wid100 {
  width: calc(100% - 30px);
}
.data-left.wid50 {
  width: 30px;
}
.top20 {
  margin-top: 10px;
}
.treeWraper {
  height: calc(100vh - 160px);
  overflow: auto;
}

.cateTree {
  list-style: none;
  margin-top: 20px;
  li {
    user-select: none;
    padding: 10px 0;
    font-size: 12px;
    padding-left: 35px;
    cursor: pointer;
    line-height: 1.5;
  }
  .active {
    // border-left: 3px solid #00469c;
    color: #00469c;
    position: relative;
    &::before {
      content: '';
      background-color: #00469c;
      position: absolute;
      top: 0;
      left: 0;
      width: 3px;
      bottom: 0;
    }
  }
}
.user {
  height: 100%;
}
</style>
