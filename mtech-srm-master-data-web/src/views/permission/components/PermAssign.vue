<!--
 * @Author: your name
 * @Date: 2021-09-07 17:19:15
 * @LastEditTime: 2021-12-17 15:08:55
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\permission\components\PermAssign.vue
-->
<template>
  <div style="height: 100%">
    <mt-template-page ref="permPage" :template-config="pageConfig"></mt-template-page>
  </div>
</template>

<script>
import { utils } from '@mtech-common/utils'
import { PERM_DIALOG_PAGE_PLUGIN } from '../config/role.config'
export default {
  props: {
    roleInfo: {
      required: true,
      type: Object,
      default() {
        return {}
      }
    },
    urls: {
      required: true,
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      pageConfig: PERM_DIALOG_PAGE_PLUGIN(this.dataBound, this.rowDataBound),

      nodeIdAssigned: [],
      nodeIndexAssigned: []
    }
  },
  methods: {
    async treeDataGet() {
      const query = { applicationCode: utils.getAppCode() }
      await this.urls.list(query).then((res) => {
        this.pageConfig[0].treeGrid = Object.assign({}, this.pageConfig[0].treeGrid, {
          dataSource: res.data
        })
      })
    },
    async nodeAssignedGet() {
      const query = { roleId: this.roleInfo.id }
      this.nodeIdAssigned = []
      await this.urls.selectNodes(query).then((res) => {
        this.nodeIdAssigned = res.data
        this.nodeIndexAssigned = []
      })
    },
    clearDataSource() {
      this.pageConfig[0].treeGrid.dataSource = []
    },
    dataBound() {
      const currentTab = this.$refs.permPage.getCurrentTabRef()
      currentTab.treeGrid.collapseAll()
      if (this.nodeIndexAssigned.length) {
        this.$nextTick(() => {
          currentTab.treeGrid.selectRows(this.nodeIndexAssigned)
        })
      }
    },
    rowDataBound(e) {
      if (this.nodeIdAssigned.includes(e.data.permissionCode)) {
        this.nodeIndexAssigned.push(parseInt(e.row.getAttribute('aria-rowindex')))
      }
    },
    nodeCheckedGet() {
      const currentTab = this.$refs.permPage.getCurrentTabRef()
      const rowSelect = currentTab.treeGrid.getSelectedRecords()
      const ids = []
      rowSelect.map((e) => {
        ids.push(e.id)
        this.getDeepParentId(e.parentItem, ids)
      })
      return [...new Set(ids)]
    },
    getDeepParentId(obj, ids) {
      if (obj) {
        ids.push(obj.id)
      }
      if (obj?.parentItem) {
        this.getDeepParentId(obj.parentItem, ids)
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
