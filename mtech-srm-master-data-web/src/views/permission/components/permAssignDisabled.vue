<!--
 * @Author: your name
 * @Date: 2021-09-07 17:19:15
 * @LastEditTime: 2021-12-17 15:08:55
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\permission\components\PermAssign.vue
-->
<template>
  <div style="height: 100%">
    <mt-template-page
      ref="permPage"
      :template-config="pageConfig"
      v-show="pageConfig[0].treeGrid.dataSource.length"
    ></mt-template-page>
    <mt-template-page
      :template-config="pageEmptyConfig"
      v-show="!pageConfig[0].treeGrid.dataSource.length"
    >
    </mt-template-page>
    <mt-loading class="loading" v-show="loadingShow"></mt-loading>
  </div>
</template>

<script>
// import { utils } from '@mtech-common/utils'
import {
  PERM_DIALOG_PAGE_PLUGIN_REVIEW,
  PERM_DIALOG_PAGE_PLUGIN_REVIEW_EMPTY
} from '../config/role.config'
export default {
  props: {
    roleInfo: {
      required: true,
      type: Object,
      default() {
        return {}
      }
    },
    urls: {
      required: true,
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      pageConfig: PERM_DIALOG_PAGE_PLUGIN_REVIEW(
        this.dataBound,
        this.rowDataBound,
        this.actionComplete
      ),
      pageEmptyConfig: PERM_DIALOG_PAGE_PLUGIN_REVIEW_EMPTY,
      nodeIdAssigned: [],
      nodeIndexAssigned: [],
      loadingShow: false
    }
  },
  methods: {
    async nodeAssignedGet(data) {
      const query = { roleId: data?.id }
      this.loadingShow = true
      await this.urls.list(query).then((res) => {
        this.nodeIndexAssigned = []
        if (!res.data || !res.data.length) {
          this.loadingShow = false
        }
        this.pageConfig[0].treeGrid = Object.assign({}, this.pageConfig[0].treeGrid, {
          dataSource: res.data || []
        })
      })
    },
    clearDataSource() {
      this.pageConfig[0].treeGrid.dataSource = []
    },
    dataBound() {
      const currentTab = this.$refs.permPage.getCurrentTabRef()
      currentTab.treeGrid.collapseAll()
      this.$nextTick(() => {
        currentTab.treeGrid.selectRows(this.nodeIndexAssigned)
      })
    },
    rowDataBound(e) {
      this.nodeIndexAssigned.push(parseInt(e.row.getAttribute('aria-rowindex')))
    },
    actionComplete(e) {
      if (e.rows.length) {
        this.loadingShow = false
      }
    },
    nodeCheckedGet() {
      const currentTab = this.$refs.permPage.getCurrentTabRef()
      const rowSelect = currentTab.treeGrid.getSelectedRecords()
      const ids = rowSelect.map((e) => e.id)
      return ids
    }
  }
}
</script>

<style lang="scss" scoped>
.loading {
  top: 0;
  left: 0;
  z-index: 999;
  /deep/ .loadingImg {
    position: relative;
    left: 90px;
  }
}
</style>
