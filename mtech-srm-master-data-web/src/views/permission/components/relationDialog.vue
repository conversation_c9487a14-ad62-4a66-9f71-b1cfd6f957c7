<template>
  <mt-dialog ref="dialog" :close="cancel" :buttons="button" :header="header">
    <div class="user">
      <mt-template-page
        ref="templateRef"
        :padding-top="true"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
      />
    </div>
  </mt-dialog>
</template>
<script>
import { relationConfig } from '../config/scope.config'
export default {
  data() {
    return {
      button: [
        {
          click: this.confirm,
          buttonModel: { content: this.i18n('确定') }
        }
      ],
      pageConfig: [
        {
          toolbar: {
            useBaseConfig: false, // 代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [{ id: 'unbind', icon: 'icon_solid_Createorder', title: this.$t('解绑') }],
              ['Refresh']
            ]
          },
          grid: {
            columnData: relationConfig,
            asyncConfig: {
              url: this.$API.scope.QueryBindDetailApi,
              params: {}
            }
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    confirm() {
      this.$emit('cancel-function')
    },
    handleClickToolBar(e) {
      const { grid } = e
      const sections = grid.getSelectedRecords()
      if (sections.length == 0) {
        this.$toast({ content: this.$t('请选择一条记录'), type: 'warning' })
        return
      }
      this.unbind(sections).then(() => {
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },
    unbind(data) {
      return new Promise((resolve, reject) => {
        const t = data.map((v) => {
          return v.id
        })
        this.$API.scope
          .DataUnbindApi({ ids: t })
          .then((res) => {
            this.$toast({ content: res.msg, type: 'success' })
            resolve(true)
          })
          .catch(() => {
            reject(false)
          })
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    i18n(Key) {
      console.log(this.modalData)
      return this.modalData._this.$t(Key)
    }
  },
  created() {
    let t = this.pageConfig
    t[0].grid.asyncConfig.params = this.modalData.data
    this.pageConfig = t
  }
}
</script>
<style lang="scss" scoped>
.user {
  height: 100%;
}
/deep/ .common-template-page.template-hidden-tabs {
  padding: 0;
}
</style>
