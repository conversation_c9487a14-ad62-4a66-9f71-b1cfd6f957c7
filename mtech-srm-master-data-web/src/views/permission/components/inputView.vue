<template>
  <div>
    <span>{{ data[data.column.field] }}</span>
    <span v-if="data.encryptMap && data.encryptMap[data.column.field]">
      <img
        v-if="data[data.column.field].includes('**')"
        src="../../../assets/icons/img/eye-fill.png"
        class="eyeStyle"
        @click="checkInfo"
      />
      <img
        v-else
        src="../../../assets/icons/img/eye-close.png"
        class="eyeStyle"
        @click="checkInfo"
      />
    </span>
  </div>
</template>
<script>
export default {
  data() {
    return {
      data: {},
      oldValue: null
    }
  },
  mounted() {
    this.oldValue = this.data[this.data.column.field]
  },
  methods: {
    checkInfo() {
      let val = this.data[this.data.column.field]
      let _val = this.data.encryptMap[this.data.column.field]
      if (val && !val?.includes('**')) {
        this.data[this.data.column.field] = this.oldValue
        return
      }
      this.$API.permission.checkDeliveryConfigInfo({ key: _val || '' }).then((res) => {
        if (res && res.code === 200) {
          this.data[this.data.column.field] = res.data || ''
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.eyeStyle {
  cursor: pointer;
  margin-left: 5px;
  height: 0.8rem;
}
</style>
