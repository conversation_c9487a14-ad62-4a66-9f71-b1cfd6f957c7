<template>
  <div class="dataPermDialog">
    <mt-dialog
      ref="dataPermDialog"
      :header="header"
      :enable-resize="false"
      :position="{ X: 'right', Y: 'top' }"
      css-class="create-proj-dialog right-wrapper"
      :buttons="buttonsPerm"
    >
      <div class="feature-wrapper data-content">
        <div class="feature-left">
          <ul class="role-list">
            <li
              v-for="(item, index) in enumsList"
              :class="{ active: index == activeIndex }"
              :key="index"
              @click="getSelectedPermission(item, index)"
            >
              {{ item.label }}
            </li>
          </ul>
        </div>
        <div class="feature-right">
          <mt-template-page ref="tepPage" :template-config="pageConfig"></mt-template-page>
        </div>
      </div>
    </mt-dialog>
  </div>
</template>
<script>
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttonsPerm: [
        {
          click: this.cancelPerm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      activeIndex: 0,
      pageConfig: [
        {
          useToolTemplate: false,
          grid: {
            lineIndex: true,
            columnData: [
              {
                field: 'dimensionCodeValue',
                headerText: this.$t('编码')
              },
              {
                field: 'dimensionNameValue',
                headerText: this.$t('名称')
              }
            ],
            dataSource: []
          }
        }
      ],
      enumsList: [],
      currentValue: ''
    }
  },
  mounted() {
    this.getDimensionEnumList()
    this.$refs.dataPermDialog.ejsRef.show()
  },
  methods: {
    cancelPerm() {
      this.$emit('cancel-function')
    },
    changeIndex(index) {
      this.index_ = index
    },
    // 获取维度枚举
    async getDimensionEnumList() {
      const res = await this.$API.dataScope.getDimensionEnumApi()
      this.enumsList = res.data
      if (this.enumsList.length) {
        this.getSelectedPermission(this.enumsList[this.activeIndex], this.activeIndex)
      }
    },
    getSelectedPermission(data, index) {
      this.activeIndex = index
      this.currentValue = data.value
      this.getValueList()
    },
    async getValueList() {
      const roots = {
        subjectId: this.modalData.userId,
        subjectType: 0,
        dimensionCode: this.currentValue
      }
      const res = await this.$API.dataScope.getSelectedAndRoleDataQuery(roots)
      this.pageConfig[0].grid.dataSource = res.data
    }
  },
  computed: {
    header() {
      return `${this.modalData.employeeName}&nbsp;&nbsp;&nbsp;&nbsp;${this.$t('数据权限汇总')}`
    }
  }
}
</script>
<style scoped lang="scss">
.feature-wrapper.data-content {
  display: flex;
  height: 100%;
  .feature-left {
    width: 220px;
    padding-right: 20px;
    height: 100%;
    overflow-x: auto;
    .role-list {
      li {
        cursor: pointer;
        font-size: 16px;
        line-height: 1.5;
        padding: 10px 0 10px 10px;
      }
      li.active {
        background-color: rgba(0, 70, 156, 0.161);
        color: #00469c;
        position: relative;
        border-radius: 4px;
      }
    }
  }
  .feature-right {
    flex: 1;
    height: 100%;
  }

  .template-wrap {
    .e-content {
      height: calc(100vh - 240px) !important;
    }
  }
  /deep/ .mt-select-index {
    display: none;
  }
}
</style>
