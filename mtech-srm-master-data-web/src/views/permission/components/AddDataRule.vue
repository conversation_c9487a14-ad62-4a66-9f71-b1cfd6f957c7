<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :close="cancel"
    :buttons="button"
    :header="header"
  >
    <div class="rules">
      <mt-form ref="dialogForm" :model="addRules" :rules="rules">
        <mt-form-item prop="permissionDataName" :label="$t('规则名称')">
          <mt-input
            :placeholder="$t('请输入规则名称')"
            v-model="addRules.permissionDataName"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="permissionDataCode" :label="$t('规则编码')">
          <mt-input
            :placeholder="$t('请输入规则编码')"
            v-model="addRules.permissionDataCode"
            :disabled="!!id"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
export default {
  data() {
    return {
      button: [
        {
          click: this.cancel,
          buttonModel: { content: this.i18n('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { content: this.i18n('确定') }
        }
      ],
      addRules: {
        permissionDataCode: null,
        permissionDataName: null,
        applicationId: '',
        id: null,
        permissionId: ''
      },
      rules: {
        permissionDataCode: [
          { required: true, trigger: 'blur', message: this.$t('请输入规则编码') }
        ],
        permissionDataName: [
          { required: true, trigger: 'blur', message: this.$t('请输入规则名称') }
        ]
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    applicationId() {
      return this.modalData?.data?.applicationId || null
    },
    permissionId() {
      return this.modalData?.data.permissionId || null
    },
    id() {
      return this.modalData?.data.id || null
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    cancel() {
      this.$emit('cancel-function')
    },
    confirm() {
      this.$refs.dialogForm.validate((value) => {
        if (value) {
          this.save()
        }
      })
    },
    async save() {
      const res = await this.modalData.actionUrl(this.addRules)
      if (res) {
        this.$emit('confirm-function')
      }
    },
    i18n(Key) {
      return this.modalData._this.$t(Key)
    }
  },
  created() {
    if (this.id) {
      const {
        permissionDataCode,
        permissionDataName
        //  applicationId,
        //  permissionId
      } = this.modalData.data
      this.addRules.id = this.id
      this.addRules.permissionDataCode = permissionDataCode
      this.addRules.permissionDataName = permissionDataName
      //  this.addRules.applicationId = applicationId;
      //  this.addRules.permissionId = permissionId;
    }
    //  else{
    //      this.addRules.applicationId = this.applicationId;
    //      this.addRules.permissionId = this.permissionId;
    //  }
  }
}
</script>
<style lang="scss" scoped></style>
