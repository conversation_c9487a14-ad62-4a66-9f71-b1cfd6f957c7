<!--
 * @Author: your name
 * @Date: 2021-09-15 14:08:35
 * @LastEditTime: 2021-09-16 15:03:06
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\permission\components\AreaSelector.vue
-->
<template>
  <div>
    <mt-select
      :width="300"
      :data-source="dataFirst"
      :show-clear-button="true"
      :placeholder="$t('请选择区域')"
      @change="changeFirst"
    ></mt-select>
    <mt-select
      :width="300"
      :data-source="dataSecond"
      :show-clear-button="true"
      :placeholder="$t('请选择区域')"
      @change="changeSecond"
    ></mt-select>
    <mt-select
      :width="300"
      :data-source="dataThird"
      :show-clear-button="true"
      :placeholder="$t('请选择区域')"
      @change="changeThird"
    ></mt-select>
    <mt-select
      :width="300"
      :data-source="dataFour"
      :show-clear-button="true"
      :placeholder="$t('请选择区域')"
      @change="changeFour"
    ></mt-select>
  </div>
</template>

<script>
export default {
  data() {
    return {
      dataFirst: [],
      dataSecond: [],
      dataThird: [],
      dataFour: []
    }
  },
  mounted() {
    this.dataFirstGet()
  },
  methods: {
    changeFirst(e) {
      console.log(e)
      this.dataSecondGet()
    },
    changeSecond(e) {
      console.log(e)
      this.dataThirdGet()
    },
    changeThird(e) {
      console.log(e)
      this.dataFourGet()
    },
    changeFour(e) {
      console.log(e)
    },
    dataFirstGet() {
      setTimeout(() => {
        this.dataFirst = [
          { text: this.$t('区域1'), value: 0 },
          { text: this.$t('区域3'), value: 1 }
        ]
      }, 500)
    },
    dataSecondGet() {
      setTimeout(() => {
        this.dataSecond = [
          { text: this.$t('区域3'), value: 0 },
          { text: this.$t('区域4'), value: 1 }
        ]
      }, 500)
    },
    dataThirdGet() {
      setTimeout(() => {
        this.dataThird = [
          { text: this.$t('区域5'), value: 0 },
          { text: this.$t('区域6'), value: 1 }
        ]
      }, 500)
    },
    dataFourGet() {
      setTimeout(() => {
        this.dataFour = [
          { text: this.$t('区域7'), value: 0 },
          { text: this.$t('区域8'), value: 1 }
        ]
      }, 500)
    }
  }
}
</script>

<style></style>
