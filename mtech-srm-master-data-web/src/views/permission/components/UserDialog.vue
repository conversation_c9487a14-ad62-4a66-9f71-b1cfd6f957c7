<template>
  <mt-dialog ref="dialog" :close="cancel" :buttons="button" :header="header">
    <div class="user">
      <mt-template-page ref="templateRef" :padding-top="true" :template-config="pageConfig" />
    </div>
  </mt-dialog>
</template>
<script>
import { columnDataUser } from '../config/scope.config'
export default {
  data() {
    return {
      button: [
        {
          click: this.cancel,
          buttonModel: { content: this.i18n('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { content: this.i18n('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          toolbar: { useBaseConfig: true, tools: [[], []] },
          grid: {
            columnData: columnDataUser,
            asyncConfig: {
              url: this.modalData.url,
              params: {}
            }
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    ids() {
      return this.modalData.data
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    async confirm() {
      const currentTab = this.$refs.templateRef.getCurrentTabRef()
      const selectedRows = currentTab.grid.getSelectedRecords().map((v) => {
        return v.accountId
      })
      const params = {
        ids: this.ids,
        userIds: selectedRows,
        userGroupIds: [],
        subjectType: 0
      }
      const res = await this.modalData.actionUrl(params)
      if (res.code == 200) {
        this.$toast({ content: res.msg, type: 'success' })
        this.$emit('confirm-function')
      }
    },
    cancel() {
      this.$emit('cancel-function')
    },
    i18n(Key) {
      return this.modalData._this.$t(Key)
    }
  },
  created() {}
}
</script>
<style lang="scss" scoped>
.user {
  height: 100%;
}
/deep/ .common-template-page.template-hidden-tabs {
  padding: 0;
}
</style>
