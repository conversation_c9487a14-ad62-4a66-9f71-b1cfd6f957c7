<template>
  <mt-dialog ref="dialog" :close="cancel" :buttons="button" :header="header">
    <div class="user">
      <mt-template-page ref="templateRef" :padding-top="true" :template-config="pageConfig">
        <template v-slot:quick-search-form>
          <div class="custom-form-box">
            <mt-form ref="searchFormRef" :model="searchFormModel">
              <mt-form-item prop="employeeName" :label="$t('用户名称')">
                <mt-input v-model="searchFormModel.employeeName"></mt-input>
              </mt-form-item>
              <mt-form-item prop="externalCode" :label="$t('账号')">
                <mt-input v-model="searchFormModel.externalCode"></mt-input>
              </mt-form-item>
              <mt-form-item prop="departmentName" :label="$t('部门')">
                <mt-input v-model="searchFormModel.departmentName"></mt-input>
              </mt-form-item>
              <mt-form-item prop="phoneNum" :label="$t('电话')">
                <mt-input v-model="searchFormModel.phoneNum"></mt-input>
              </mt-form-item>
              <mt-form-item prop="employeeCode" :label="$t('工号')">
                <mt-input v-model="searchFormModel.employeeCode"></mt-input>
              </mt-form-item>
              <mt-form-item prop="statusId" :label="$t('激活状态')">
                <mt-select
                  v-model="searchFormModel.statusId"
                  css-class="rule-element"
                  :data-source="statusIdList"
                  :show-clear-button="true"
                  :allow-filtering="true"
                  filter-type="Contains"
                  :placeholder="$t('激活状态')"
                />
              </mt-form-item>
            </mt-form>
          </div>
        </template>
      </mt-template-page>
    </div>
  </mt-dialog>
</template>
<script>
import { columnDataUserTenant } from '../config/scope.config'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      statusIdList: [
        { text: this.$t('草稿'), value: 0 },
        { text: this.$t('激活'), value: 1 },
        { text: this.$t('分发'), value: 2 },
        { text: this.$t('失效'), value: 3 },
        { text: this.$t('待审核'), value: -1 },
        { text: this.$t('准备'), value: -2 }
      ],
      button: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          toolbar: { useBaseConfig: true, tools: [[], []] },
          gridId: 'BE5B2780-2CDF-8DCC-A7A9-EF67085DE9B1',
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          grid: {
            columnData: columnDataUserTenant,
            asyncConfig: {
              url: this.modalData.url,
              params: {}
              // serializeList: (list) => {
              //   return list.map((item) => {
              //     return {
              //       ...item,
              //       employeeName: item.employeeName,
              //     }
              //   })
              // },
            }
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    ids() {
      return this.modalData.data
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    async confirm() {
      const currentTab = this.$refs.templateRef.getCurrentTabRef()
      const selectedRows = currentTab.grid.getSelectedRecords()
      if (!selectedRows.length) {
        this.$toast({
          content: this.$t('请选择一条数据'),
          type: 'warning'
        })
        return
      }
      this.$emit('confirm-function', selectedRows)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  },
  created() {}
}
</script>
<style lang="scss" scoped>
.user {
  height: 100%;
}
/deep/ .common-template-page.template-hidden-tabs {
  padding: 0;
}
</style>
