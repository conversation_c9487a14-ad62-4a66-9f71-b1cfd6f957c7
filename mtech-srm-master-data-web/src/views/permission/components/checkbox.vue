<template>
  <div>
    <mt-checkbox class="checkbox-item" v-model="isRequire" disabled></mt-checkbox>
  </div>
</template>
<script>
export default {
  props: {
    rolePermissionList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      isRequire: false
    }
  },
  created() {
    // this.isRequire = this.rolePermissionList.includes(this.data.permissionCode);
  }
}
</script>
