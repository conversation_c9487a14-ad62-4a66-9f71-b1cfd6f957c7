<!--
 * @Author: your name
 * @Date: 2021-08-31 13:51:38
 * @LastEditTime: 2021-12-02 18:04:27
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\permission\components\ListBox.vue
-->
<template>
  <mt-dialog ref="dialog" :header="$t('岗位管理')" :buttons="buttons" @open="dataGet">
    <div class="full-width flex justify-between">
      <div class="left-wrapper side-wrapper">
        <div class="header-ctx">
          <div class="title mt-pl-20">{{ $t('待选岗位') }}</div>
          <div class="e-input-group e-outline">
            <mt-icon name="icon_search" class="mt-ml-10 search-icon cursor-pointer"></mt-icon>
            <input class="e-input" type="text" :placeholder="$t('请输入搜索内容')" />
          </div>
        </div>
        <mt-list-box
          ref="left"
          :height="400"
          :data-source="groupA"
          :fields="fieldsA"
          scope="#listbox"
          :toolbar-settings="toolbarSettings"
          :selection-settings="selectionSettings"
        ></mt-list-box>
      </div>
      <div class="right-wrapper side-wrapper">
        <div class="header-ctx">
          <div class="title mt-pl-20">{{ $t('现有岗位') }}</div>
          <div class="e-input-group e-outline">
            <mt-icon name="icon_search" class="mt-ml-10 search-icon cursor-pointer"></mt-icon>
            <input class="e-input" type="text" :placeholder="$t('请输入搜索内容')" />
          </div>
        </div>
        <mt-list-box
          ref="right"
          :height="400"
          listbox-id="listbox"
          :data-source="groupB"
          :fields="fieldsB"
          scope="combined-list"
          :selection-settings="selectionSettings"
        ></mt-list-box>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
// import Vue from "vue";
// var itemTemplate = Vue.component("itemTemplate", {
//   template: `
//     <div>
//       {{data.Name}}
//     </div>
//   `,
//   data() {
//     return {
//       data: {}
//     };
//   }
// });
export default {
  props: {
    params: {
      required: true,
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.stationManageSave,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      groupA: [],
      groupB: [],
      groupCopy: {},
      fieldsA: { text: 'standardStationName', value: 'id' },
      fieldsB: { text: 'stationName', value: 'id' },
      toolbarSettings: {
        items: ['moveTo', 'moveFrom']
      },
      selectionSettings: {
        showCheckbox: true
      }
      // itemtemplate: function() {
      //   return {
      //     template: itemTemplate
      //   };
      // }
    }
  },
  methods: {
    dataGet() {
      this.stationAllGet()
      this.stationSelectGet()
    },
    stationAllGet() {
      const { companyOrganizationId, departmentOrganizationId } = this.params
      const query = { companyOrganizationId, departmentOrganizationId }
      this.$API.org.stationAllGet(query).then((res) => {
        this.groupA = res.data
        this.groupCopy.groupA = res.data
      })
    },
    stationSelectGet() {
      const { departmentId } = this.params
      const query = { organizationId: departmentId }
      this.$API.org.stationSelectGet(query).then((res) => {
        this.groupB = res.data
        this.groupCopy.groupB = res.data
      })
    },
    stationManageSave() {
      const currentRight = this.$refs.right.ejsRef.getDataList()
      const selected = currentRight.map((s) => {
        let stationName = s.standardStationName
        // 默认值为空格字符串的当无效值处理
        if (typeof stationName === 'string' && stationName.trim() === '') {
          stationName = s.stationName
        }
        return {
          standardStationId: s.standardStationId || s.id,
          stationName
        }
      })
      const data = {
        choosedStandardStations: selected,
        organizationId: this.params.departmentId
      }
      this.$API.org.stationManageSave(data).then(() => {
        this.cancel()
      })
    },
    cancel() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
.left-wrapper {
  width: 52%;
  .header-ctx {
    width: calc(100% - 61px);
  }
}
.right-wrapper {
  width: 47%;
}

.side-wrapper {
  .header-ctx {
    border: 1px solid #e0e0e0;
    border-bottom: 0px solid #e0e0e0;
  }
  .title {
    height: 40px;
    line-height: 40px;
    background: rgba(255, 255, 255, 1);
    border-radius: 4px 4px 0 0;
    box-shadow: inset 0 -1px 0 0 rgba(232, 232, 232, 1);
    font-size: 16px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
  }
  /deep/.e-outline {
    border: none;
    margin-bottom: 0;
    height: 40px;
    line-height: 40px;
    .search-icon {
      color: rgba(151, 151, 151, 1);
      margin-top: 13px;
    }
  }
}
</style>
