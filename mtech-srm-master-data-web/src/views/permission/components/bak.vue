<!--
 * @Author: your name
 * @Date: 2021-09-07 17:19:15
 * @LastEditTime: 2021-12-17 15:08:55
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\permission\components\PermAssign.vue
-->
<template>
  <div style="height: 100%">
    <mt-template-page
      ref="permPage"
      :template-config="pageConfig"
      v-if="this.pageConfig[0].treeGrid.dataSource.length"
    ></mt-template-page>
    <mt-template-page
      ref="permPage"
      :template-config="pageConfigEmpaty"
      v-if="!this.pageConfig[0].treeGrid.dataSource.length"
    ></mt-template-page>
  </div>
</template>

<script>
import { utils } from '@mtech-common/utils'
import { PERM_DIALOG_PAGE_PLUGIN_REVIEW } from '../config/role.config'
export default {
  props: {
    roleInfo: {
      required: true,
      type: Object,
      default() {
        return {}
      }
    },
    urls: {
      required: true,
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      pageConfig: PERM_DIALOG_PAGE_PLUGIN_REVIEW(this.dataBound),
      pageConfigEmpaty: [
        {
          title: this.$t('功能权限'),
          treeGrid: {
            allowPaging: false,
            loadChildOnDemand: true,
            columnData: [
              {
                field: 'name',
                headerText: this.$t('权限名称'),
                textAlign: 'left',
                width: '250'
              },
              {
                field: 'permissionTypeName',
                width: '200',
                textAlign: 'left',
                headerText: this.$t('权限类型')
              },
              {
                field: 'isSelected',
                width: '80',
                textAlign: 'left',
                headerText: this.$t('是否分配')
              }
            ],
            dataSource: [],
            childMapping: 'children'
          }
        }
      ],
      nodeIdAssigned: [],
      nodeIndexAssigned: []
    }
  },
  methods: {
    async treeDataGet() {
      const query = { applicationCode: utils.getAppCode() }
      await this.urls.list(query).then((res) => {
        this.pageConfig[0].treeGrid = Object.assign({}, this.pageConfig[0].treeGrid, {
          dataSource: res.data
        })
      })
    },
    async nodeAssignedGet() {
      const query = { roleId: this.roleInfo.id }
      this.nodeIdAssigned = []
      await this.urls.selectNodes(query).then((res) => {
        this.nodeIdAssigned = res.data
      })
    },
    clearDataSource() {
      this.pageConfig[0].treeGrid.dataSource = []
    },
    dataBound() {
      const currentTab = this.$refs.permPage.getCurrentTabRef()
      currentTab.treeGrid.collapseAll()
    },
    rowDataBound(e) {
      console.log('rowDataBound')
      if (this.nodeIdAssigned.includes(e.data.permissionCode)) {
        this.nodeIndexAssigned.push(parseInt(e.row.getAttribute('aria-rowindex')))
      }
    },
    nodeCheckedGet() {
      const currentTab = this.$refs.permPage.getCurrentTabRef()
      const rowSelect = currentTab.treeGrid.getSelectedRecords()
      const ids = rowSelect.map((e) => e.id)
      return ids
    }
  }
}
</script>

<style lang="scss" scoped></style>
