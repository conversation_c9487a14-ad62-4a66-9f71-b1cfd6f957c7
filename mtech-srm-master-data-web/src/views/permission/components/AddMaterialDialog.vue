<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :close="cancel"
    :buttons="button"
    :header="header"
  >
    <div class="rules">
      <mt-form ref="dialogForm" :model="addMaterial" :rules="rules">
        <mt-form-item prop="dimensionCodeValue" :label="$t('物料编码')">
          <mt-input
            :placeholder="$t('请输入物料编码')"
            v-model="addMaterial.dimensionCodeValue"
            max-length="64"
          ></mt-input>
        </mt-form-item>

        <mt-form-item :label="$t('物料名称')">
          <mt-input
            :placeholder="$t('请输入物料名称')"
            max-length="64"
            v-model="addMaterial.dimensionNameValue"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
export default {
  data() {
    return {
      button: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { content: this.$t('确定') }
        }
      ],
      addMaterial: {
        dimensionCodeValue: null,
        dimensionNameValue: null,
        dimensionValueSource: 1
      },
      rules: {
        dimensionCodeValue: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请输入物料编码')
          },
          {
            validator: this.validatorItemCode,
            trigger: 'blur'
          }
        ]
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    cancel() {
      this.$emit('cancel-function')
    },
    confirm() {
      this.$refs.dialogForm.validate((value) => {
        if (value) {
          this.save()
        }
      })
    },
    async save() {
      this.$refs.dialogForm.validate((val) => {
        if (val) {
          this.$emit('confirm-function', [
            {
              ...this.addMaterial,
              dimensionIdValue: this.addMaterial.dimensionCodeValue,
              local: true
            }
          ])
        }
      })
    },
    changeHandle() {},
    validatorItemCode(rule, value, callback) {
      const reg = /[\u4e00-\u9fa5]/
      if (reg.test(value)) {
        callback(new Error(this.$t('不能包含中文字符')))
      } else {
        callback()
      }
    }
  },
  created() {
    if (this.modalData.data) {
      this.addMaterial = { ...this.modalData.data }
    }
  }
}
</script>
<style lang="scss" scoped></style>
