<template>
  <div class="permDialog">
    <mt-dialog
      ref="permDialog"
      :header="$t('权限预览')"
      :enable-resize="false"
      :position="{ X: 'right', Y: 'top' }"
      css-class="create-proj-dialog right-wrapper"
      :buttons="buttonsPerm"
    >
      <div class="feature-wrapper">
        <div class="feature-left">
          <ul class="role-list">
            <li
              v-for="(item, index) in roleInculesList"
              :class="{ active: index === index_ }"
              :key="index"
              @click="changeIndex(index)"
            >
              {{ item.roleName }}
            </li>
          </ul>
        </div>
        <div class="feature-right">
          <permAssignDisabled
            ref="permAssign"
            :role-info="roleInfo"
            :urls="urls"
          ></permAssignDisabled>
        </div>
      </div>
    </mt-dialog>
  </div>
</template>
<script>
import permAssignDisabled from './PermAssign.vue'
export default {
  components: { permAssignDisabled },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttonsPerm: [
        {
          click: this.cancelPerm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      urls: {
        list: this.$API.roles.menuTreeGet,
        selectNodes: this.$API.roles.actionNodeSelectGet
      },
      roleInfo: {},
      index_: 0,
      roleInculesList: []
    }
  },
  created() {},
  mounted() {
    // this.show();
  },
  methods: {
    async permDialogOpen() {
      await this.$refs.permAssign.nodeAssignedGet(this.roleInfo)
      await this.$refs.permAssign.treeDataGet()
    },
    cancelPerm() {
      this.$emit('cancel-function')
    },
    show() {
      this.$refs.permDialog.ejsRef.show()
    },
    changeIndex(index) {
      this.index_ = index
      this.$refs.permAssign?.clearDataSource()
      this.roleInfo = this.roleInculesList[this.index_]
      if (this.roleInculesList.length) {
        this.permDialogOpen()
      }
    },
    async getRoleListById() {
      // ?? '1478914592740052994'
      const param = {
        userId: this.modalData.userId,
        tenantId: this.$store.state.user?.tenantId
      }
      const res = await this.$API.dataScope.getRoleListQuery(param)
      this.roleInculesList = res.data || []
      this.changeIndex(0)
    }
  },
  watch: {
    modalData: {
      handler() {
        this.getRoleListById()
      },
      deep: true
    }
  }
}
</script>
<style scoped lang="scss">
.permDialog {
  height: 100%;
  display: none;
}
.feature-wrapper {
  display: flex;
  height: 100%;
  .feature-left {
    width: 220px;
    padding-right: 20px;
    height: 100%;
    overflow-x: auto;
    .role-list {
      li {
        cursor: pointer;
        font-size: 16px;
        line-height: 1.5;
        padding: 10px 0 10px 10px;
      }
      li.active {
        background-color: rgba(0, 70, 156, 0.161);
        color: #00469c;
        position: relative;
        border-radius: 4px;
      }
    }
  }
  .feature-right {
    flex: 1;
    height: 100%;
  }
}
</style>
