<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :close="cancel"
    :buttons="button"
    :header="header"
  >
    <div class="rules">
      <mt-form ref="dialogForm" :model="filterForm" :rules="rules">
        <mt-form-item prop="a" :label="$t('单据类型')">
          <mt-select
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="BusinessTypeList"
            :fields="{ text: 'tablePermissionLabel', value: 'id' }"
            v-model="bid"
            :placeholder="$t('请选择单据类型')"
            @change="changeHandle"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="dimensionCode" :label="$t('管控维度')">
          <mt-select
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="controlScopeList"
            :fields="{ text: 'dimensionName', value: 'dimensionCode' }"
            :placeholder="$t('请选择管控维度')"
            v-model="filterForm.dimensionCode"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="ruleInCondition" :label="$t('比较条件')">
          <mt-select
            float-label-type="Never"
            :data-source="filterList"
            :fields="{ text: 'label', value: 'value' }"
            :allow-filtering="true"
            v-model="filterForm.ruleInCondition"
            :placeholder="$t('请选择比较条件')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="ruleValue" :label="$t('比较值')">
          <mt-input :placeholder="$t('请输入比较值')" v-model="filterForm.ruleValue"></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('是否开启')">
          <mt-switch
            class="item"
            active-value="1"
            inactive-value="0"
            v-model="filterForm.isEnable"
          ></mt-switch>
        </mt-form-item>
        <mt-form-item prop="ruleOutCondition" :label="$t('逻辑运算符')">
          <mt-select
            float-label-type="Never"
            :data-source="OutCondition"
            :fields="{ text: 'label', value: 'value' }"
            :allow-filtering="true"
            v-model="filterForm.ruleOutCondition"
            :placeholder="$t('请选择逻辑运算符')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="itemOrder" :label="$t('顺序')">
          <mt-inputNumber :min="0" v-model="filterForm.itemOrder"></mt-inputNumber>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
export default {
  data() {
    return {
      button: [
        {
          click: this.cancel,
          buttonModel: { content: this.i18n('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { content: this.i18n('确定') }
        }
      ],
      BusinessTypeList: [],
      controlScopeList: [],
      filterForm: {
        dimensionCode: null,
        fieldNameList: null,
        id: null,
        isEnable: '1',
        isLimitField: null,
        itemOrder: 0,
        permissionDataCode: null,
        ruleInCondition: null,
        ruleOutCondition: null,
        ruleValue: null,
        serviceName: null,
        tableName: null
      },
      filterList: [],
      OutCondition: [
        { value: 0, label: 'AND' },
        { value: 1, label: 'OR' }
      ],
      rules: {
        dimensionCode: [
          {
            required: true,
            message: this.$t('请选择管控维度'),
            trigger: 'blur'
          }
        ],
        ruleInCondition: [
          {
            required: true,
            message: this.$t('请选择比较条件'),
            trigger: 'blur'
          }
        ],
        ruleOutCondition: [
          {
            required: true,
            message: this.$t('请选择逻辑运算符'),
            trigger: 'blur'
          }
        ],
        ruleValue: [{ required: true, message: this.$t('请输入比较值'), trigger: 'blur' }],
        itemOrder: [{ required: true, message: this.$t('请输入顺序'), trigger: 'blur' }]
      },
      bid: null
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    cancel() {
      this.$emit('cancel-function')
    },
    confirm() {
      this.$refs.dialogForm.validate((value) => {
        if (value) {
          this.save().then(() => {
            this.$emit('confirm-function')
          })
        }
      })
    },
    async getTable() {
      const res = await this.$API.scope.QueryTableApiAdmin({})
      this.BusinessTypeList = res.data || []
    },
    // 业务类型改变事件
    changeHandle({ itemData }) {
      if (!itemData) {
        this.bid = null
      }
      this.filterForm.dimensionCode = null
      const params = {
        serviceName: itemData.serviceName ?? '',
        tableName: itemData.tableName ?? ''
      }
      this.getContralScope(params)
    },
    async getContralScope(data) {
      const res = await this.$API.scope.QueryDimensionApiAdmin(data)
      const t = res.data.filter((i) => i.valid != 0)
      this.controlScopeList = t || []
    },
    async getFilterItem() {
      const res = await this.$API.scope.FilterItemApi({})
      this.filterList = res.data
    },
    save() {
      return new Promise((resolve, reject) => {
        const [btJosn] = this.BusinessTypeList.filter((v) => v.id == this.bid)
        const { tableName, serviceName } = btJosn
        this.filterForm.tableName = tableName
        this.filterForm.serviceName = serviceName
        this.modalData.data
          .url([this.filterForm])
          .then((res) => {
            this.$toast({ content: res.msg, type: 'success' })
            resolve(true)
          })
          .catch(() => {
            reject(false)
          })
      })
    },
    i18n(Key) {
      return this.modalData._this.$t(Key)
    }
  },
  created() {
    this.getTable()
    this.getFilterItem()
    this.filterForm.permissionDataCode = this.modalData.data.permissionDataCode
  }
}
</script>
<style lang="scss" scoped></style>
