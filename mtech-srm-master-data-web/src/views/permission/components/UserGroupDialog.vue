<template>
  <mt-dialog ref="dialog" :header="header" css-class="create-proj-dialog" :buttons="button">
    <div class="user">
      <mt-template-page ref="templateRef" :padding-top="true" :template-config="pageConfig" />
    </div>
  </mt-dialog>
</template>
<script>
import { columnDataUserGroup } from '../config/scope.config'
export default {
  data() {
    return {
      button: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          toolbar: { useBaseConfig: true, tools: [[], []] },
          gridId: '075BCD92-6120-77A8-F083-3B3C62937E9D',
          grid: {
            columnData: columnDataUserGroup,
            asyncConfig: {
              url: this.modalData.url,
              params: {}
            }
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    ids() {
      return this.modalData.data
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    async confirm() {
      const currentTab = this.$refs.templateRef.getCurrentTabRef()
      const selectedRows = currentTab.grid.getSelectedRecords()
      if (!selectedRows.length) {
        this.$toast({
          content: this.$t('请选择一条数据'),
          type: 'warning'
        })
        return
      }
      this.$emit('confirm-function', selectedRows)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  },
  created() {}
}
</script>
<style lang="scss" scoped>
.user {
  height: 100%;
}
/deep/ .common-template-page.template-hidden-tabs {
  padding: 0;
}
</style>
