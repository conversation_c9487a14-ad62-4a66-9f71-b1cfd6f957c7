<template>
  <mt-dialog ref="dialog" :close="cancel" :buttons="button" :header="header">
    <div class="user">
      <mt-template-page ref="templateRef" :padding-top="true" :template-config="pageConfig" />
    </div>
  </mt-dialog>
</template>
<script>
export default {
  data() {
    return {
      button: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          toolbar: [],
          grid: {
            columnData: [
              {
                type: 'checkbox',
                width: 50
              },
              {
                field: 'itemCode',
                headerText: this.$t('物料编码')
              },
              {
                field: 'itemName',
                headerText: this.$t('物料名称')
              }
            ],
            asyncConfig: {
              url: this.$API.dataScope.getMarticApi,
              params: {}
            }
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    async confirm() {
      const currentTab = this.$refs.templateRef.getCurrentTabRef()
      const selectedRows = currentTab.grid.getSelectedRecords().map((item) => {
        return {
          dimensionValueSource: 0,
          dimensionNameValue: item.itemName,
          dimensionCodeValue: item.itemCode,
          dimensionIdValue: item.id
        }
      })

      if (!selectedRows.length) return
      this.$emit('confirm-function', selectedRows)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  },
  created() {}
}
</script>
<style lang="scss" scoped>
.user {
  height: 100%;
}
/deep/ .common-template-page.template-hidden-tabs {
  padding: 0;
}
</style>
