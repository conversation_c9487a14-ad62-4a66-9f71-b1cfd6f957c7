<template>
  <div class="data-permission-setting">
    <div class="data-permission-header">
      <div class="header-left">
        <template v-if="currentUserInfo && currentUserInfo.fullName !== undefined">
          <div class="items">{{ $t('公司：') }}{{ currentUserInfo.company }}</div>
          <div class="items">{{ $t('部门：') }}{{ currentUserInfo.department }}</div>
          <div class="items">{{ $t('岗位：') }}{{ currentUserInfo.station }}</div>
          <div class="items">{{ $t('工号：') }}{{ currentUserInfo.userCode }}</div>
          <div class="items">{{ $t('姓名：') }}{{ currentUserInfo.fullName }}</div>
        </template>
        <template v-else>
          <div class="items">{{ $t('用户组名称：') }}{{ currentUserInfo.userGroupName }}</div>
          <div class="items">{{ $t('用户组类型：') }}{{ currentUserInfo.userGroupTypeName }}</div>
        </template>
      </div>

      <!-- @click="backDetail" -->
      <div class="action">
        <mt-button type="text" @click="back" class="detail-header-button">{{
          $t('返回')
        }}</mt-button>
        <mt-button
          type="text"
          v-if="currentValue !== 'ITEM'"
          class="detail-header-button"
          @click="submit"
          >{{ $t('保存') }}</mt-button
        >
        <mt-button type="text" v-else class="detail-header-button" @click="Materialsubmit">{{
          $t('保存')
        }}</mt-button>
      </div>
    </div>
    <div class="data-permission-buessness-header">
      <div class="data-permission-buessness">
        <span
          :class="{ active: index == activeIndex }"
          v-for="(item, index) in enumsList"
          @click="selectBusiness(item, index)"
          :key="index"
          >{{ item.label }}</span
        >
      </div>
    </div>
    <mt-checkbox
      :label="$t('全部权限')"
      class="checkbox-item"
      v-model="isNewProduct"
      @change="compressorChange"
    ></mt-checkbox>
    <div class="table-wrap">
      <div class="table-wrap" v-show="currentValue !== 'ITEM'">
        <mt-template-page
          ref="templateRef"
          class="treeTemplate"
          v-if="isTree"
          :use-tool-template="false"
          :template-config="pageConfig"
          @handleClickToolBar="handleClickToolBarTree"
        ></mt-template-page>
        <mt-template-page
          v-if="!isTree && this.pageConfigPage[0].grid.asyncConfig.url"
          ref="templateRef"
          :use-tool-template="false"
          :template-config="pageConfigPage"
        ></mt-template-page>
        <div v-if="!isTree && !this.pageConfigPage[0].grid.asyncConfig.url">
          <mt-template-page
            :use-tool-template="false"
            :template-config="pageEmpty"
          ></mt-template-page>
        </div>
      </div>
      <div class="marticWrap" v-show="currentValue === 'ITEM'">
        <mt-template-page
          ref="templateItemRef"
          :use-tool-template="false"
          @handleClickToolBar="handleClickToolBar"
          @handleClickCellTool="handleClickCellToolItem"
          :template-config="pageConfigItem"
        ></mt-template-page>
      </div>
    </div>
  </div>
</template>
<script>
import {
  dataSetPageConfig,
  dataSetPageConfigPage,
  columnDataSetFn,
  pageConfigItem
} from './config/data.config'
import _ from 'lodash'
import { utils } from '@mtech-common/utils'
export default {
  data() {
    return {
      isNewProduct: false,
      pageConfig: dataSetPageConfig(this.rowDataBound, this.dataBound), //树表格
      pageConfigPage: dataSetPageConfigPage(this.rowDataBound, this.dataBound), // 不是树
      pageEmpty: [
        {
          useToolTemplate: false,
          grid: {
            toolbar: [],
            columnData: [
              {
                field: 'itemCode',
                headerText: this.$t('编码'),
                textAlign: 'left'
              },
              {
                field: 'itemName',
                headerText: this.$t('名称'),
                textAlign: 'left'
              },
              {
                type: 'checkbox'
              }
            ],
            dataSource: []
          }
        }
      ],
      pageConfigItem: pageConfigItem, // 物料表格
      activeIndex: 0, //当前点的第几个tab
      enumsList: [], // 所有需要权限的列表
      formValue: {
        //各个主数据取得字段 映射
        BUSINESS_TYPE: {
          id: 'id',
          // itemCode: null,
          // itemName: null,
          code: 'itemCode',
          name: 'itemName'
        },
        COMPANY: {
          id: 'id',
          // orgCode: null,
          name: 'orgName',
          code: 'orgCode'
        },
        SITE: {
          id: 'id',
          // orgCode: null,
          name: 'siteName',
          code: 'siteCode'
        },
        CUSTOMER: {
          id: 'id',
          // customerCode: null,
          // customerName: null,
          code: 'customerCode',
          name: 'customerName'
        },
        SUPPLIER: {
          id: 'id',
          // supplierCode: null,
          // supplierName: null,
          code: 'supplierCode',
          name: 'supplierName'
        },
        PURCHASE_ORG: {
          // organizationCode: null,
          id: 'id',
          organizationName: null,
          name: 'organizationName',
          code: 'organizationCode'
        },
        PLAN_GROUP: {
          // organizationCode: null,
          id: 'id',
          // organizationName: null,
          code: 'groupCode',
          name: 'groupName'
        },
        PURCHASE_GROUP: {
          // organizationCode: null,
          id: 'id',
          // organizationName: null,
          code: 'groupCode',
          name: 'groupName'
        },
        CATEGORY: {
          // categoryCode: null,
          // categoryName: null,
          id: 'id',
          code: 'categoryCode',
          name: 'categoryName'
        },
        ITEM_GROUP: {
          name: 'name',
          code: 'code',
          id: 'id'
        },
        ITEM: {
          // itemCode: null,
          // itemName: null,
          id: 'id',
          code: 'itemCode',
          name: 'itemName'
        },
        CREATE_USER: {
          // userCode: null,
          // userName: null,
          id: 'id',
          code: 'userCode',
          name: 'employeeName'
        },
        SKU: {
          id: 'id',
          code: 'id',
          name: 'name'
        },
        DEPT: {
          id: 'id',
          // orgCode: null,
          name: 'orgName',
          code: 'orgCode'
        },
        PRICE_CATEGORY: {
          id: 'id',
          code: 'itemCode',
          name: 'itemName'
        },
        'MB-TYPE': {
          id: 'id',
          code: 'itemCode',
          name: 'itemName'
        },
        RECONCILIATION_TYPE: {
          id: 'id',
          code: 'itemCode',
          name: 'itemName'
        }
      },
      currentValue: null, //当前选择得tab值
      currentUserInfo: null, // 当前得用户信息
      subjectId: null, // 用户iD
      selectRowIds: [], // 选择得id集合
      nodeIndexAssigned: [], // 需要回显得索引
      isTree: true, // 是不是树
      selectRowClone: [], // 已经选择所有得值得备份
      needRefresh: false,
      nameLike: null,
      sourceDataList: []
    }
  },
  created() {
    const currentUserInfo = window.localStorage.getItem('RoleCurrent') ?? ''
    if (currentUserInfo) {
      this.currentUserInfo = JSON.parse(currentUserInfo)
      this.subjectId = this.currentUserInfo?.subjectId
    }
    this.getDimensionEnumList()
  },
  computed: {
    subjectType() {
      return (
        this.$route.query.subjectType || (this.currentUserInfo.fullName != undefined ? 0 : 1) || 0
      )
    }
  },
  methods: {
    compressorChange(e) {
      this.isNewProduct = e.checked
    },
    handleClickToolBar(e) {
      const { grid, toolbar } = e
      const records = grid.getSelectedRecords()
      if (toolbar.id == 'add') {
        this.addMartialDialogShow()
      }
      if (toolbar.id == 'select') {
        this.selectMartialDialogShow()
      }
      if (!records.length && toolbar.id == 'delete') {
        this.$toast({ content: this.$t('请选择一数据'), type: 'warning' })
        return
      }
      if (toolbar.id == 'delete') {
        this.deleteDialogShow(records)
      }
    },
    handleClickToolBarTree(e) {
      const { toolbar, form } = e
      if (toolbar.id === 'filterDataByLocal') {
        this.changeType({ value: this.currentValue }, form)
      }
      if (toolbar.id === 'refreshDataByLocal' || toolbar.id === 'resetDataByLocal') {
        this.changeType({ value: this.currentValue })
      }
    },
    handleClickCellToolItem(e) {
      const { tool, data } = e
      if (tool.id == 'edit') {
        this.editItemCustom(data)
      }
      if (tool.id == 'delete') {
        this.deleteDialogShow([data])
      }
    },
    editItemCustom(data) {
      this.$dialog({
        modal: () => import('./components/AddMaterialDialog.vue'),
        data: {
          title: this.$t('编辑物料'),
          data
        },
        success: (e) => {
          this.itemSourceReplace(e)
        },
        close: () => {}
      })
    },
    //物料删除得弹框
    deleteDialogShow(records) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确定删除？')
        },
        success: () => {
          this.delteMaterialFn(records)
        },
        close: () => {}
      })
    },
    //物料删除得方法
    delteMaterialFn(records) {
      let allDateSource = utils.cloneDeep(this.pageConfigItem[0].grid.dataSource)
      const indexArr = records.map((i) => i._index)
      for (let i = allDateSource.length - 1; i >= 0; i--) {
        if (indexArr.includes(allDateSource[i]._index)) {
          allDateSource.splice(i, 1)
        }
      }
      this.pageConfigItem[0].grid = Object.assign({}, this.pageConfigItem[0].grid, {
        dataSource: allDateSource.map((item, index) => {
          return {
            ...item,
            _index: index + 1
          }
        })
      })
    },
    // 添加物料得弹框
    addMartialDialogShow() {
      this.$dialog({
        modal: () => import('./components/AddMaterialDialog.vue'),
        data: {
          title: this.$t('添加物料')
        },
        success: (e) => {
          this.itemSourceInsert(e)
        },
        close: () => {}
      })
    },
    // 选择物料得弹框
    selectMartialDialogShow() {
      this.$dialog({
        modal: () => import('./components/SelectMartialDialog.vue'),
        data: {
          title: this.$t('选择物料')
        },
        success: (e) => {
          this.itemSourceInsert(e)
        },
        close: () => {}
      })
    },
    // 保存方法
    submit() {
      let selectedRows = []
      const currentTab = this.$refs.templateRef.getCurrentUsefulRef()
      const grid = currentTab.gridRef
      selectedRows = grid.getMtechGridRecords()
      let params = selectedRows.map((item) => {
        let eJes = {}
        const currentJson = item.taskData ?? item
        eJes.dimensionIdValue = currentJson[this.formValue[this.currentValue]['id']]
        eJes.dimensionNameValue = currentJson[this.formValue[this.currentValue]['name']]
        eJes.dimensionCodeValue = currentJson[this.formValue[this.currentValue]['code']]
        return eJes
      })

      params = params.filter((e) => e)
      const baseDataArry = utils.cloneDeep(this.selectRowClone)
      const selectDemissIds = params.map((item) => item.dimensionIdValue)
      const defaultDemissIds = baseDataArry.map((item) => item.dimensionIdValue)
      const newDemissIds = [] // 新增的
      selectDemissIds.map((item) => {
        if (!defaultDemissIds.includes(item)) {
          newDemissIds.push(item)
        }
      })
      const delDemissIds = []
      defaultDemissIds.map((item) => {
        if (!selectDemissIds.includes(item)) {
          delDemissIds.push(item)
        }
      })
      const dimensionDataList = []
      const dimensionCancelDataList = []
      newDemissIds.map((item) => {
        const pageconfig = params
        const newArr = _.find(pageconfig, { dimensionIdValue: item })
        dimensionDataList.push(newArr)
      })
      delDemissIds.map((item) => {
        const newArr = _.find(this.selectRowClone, { dimensionIdValue: item })
        dimensionCancelDataList.push(newArr)
      })
      const requestparam = {
        subjectId: this.subjectId,
        subjectType: this.subjectType,
        dimensionCode: this.currentValue,
        dimensionDataList,
        dimensionCancelDataList,
        authAll: this.isNewProduct
      }
      this.$API.dataScope.addRoleDataApi(requestparam).then((res) => {
        this.$toast({
          content: res.msg,
          type: 'success'
        })
        this.needRefresh = false
        this.changeType(this.enumsList[this.activeIndex])
      })
    },
    //点击tab
    selectBusiness(item, index) {
      if (index === this.activeIndex) {
        this.needRefresh = true
      } else {
        this.needRefresh = false
      }
      this.activeIndex = index
      this.currentValue = item.value
      this.changeType(item)
    },
    changeType(data, param) {
      switch (data.value) {
        case 'BUSINESS_TYPE':
          this.treeMapRender(
            `getBusinessApi`,
            { dictCode: 'businessType', ...param },
            this.formValue[`BUSINESS_TYPE`],
            '59069432-4dc2-4fbe-b166-4cf5fb1422cd'
          )
          break
        case 'COMPANY':
          this.noTreeMapRender(
            `queryPageCompanyPage`, // 调用接口的名字
            {
              defaultRules: [
                {
                  field: 'orgLevelTypeCode',
                  type: 'string',
                  operator: 'equal',
                  value: 'ORG02'
                },
                {
                  field: 'showParentName'
                }
              ]
            }, // 调用接口传的参数
            this.formValue[`COMPANY`], // 列字段的名字
            'd1ec9fd4-cfe8-4e0e-833c-50456fd1971d'
          )
          break
        case 'SITE':
          this.noTreeMapRender(
            `queryPageSitPage`,
            null,
            this.formValue[`SITE`], // 列字段的名字
            '0c1e2ab5-7b11-4670-b7f8-2ed44ace9a03'
          )
          break
        case 'PLAN_GROUP':
          this.treeMapRender(
            `getOrgGroupTreeApi`,
            { groupTypeCode: 'BG001JH', ...param },
            this.formValue[`PLAN_GROUP`], // 列字段的名字
            'cb27022a-427b-4427-9e05-e483db1a2dc2'
          )
          break
        case 'PURCHASE_GROUP':
          this.noTreeMapRender(
            `queryPageOrganizationGroup`,
            null,
            this.formValue[`PURCHASE_GROUP`], // 列字段的名字
            'c1816d32-730b-4d68-aaa9-2e01ee30f23b'
          )
          break
        case 'PURCHASE_ORG':
          this.noTreeMapRender(
            `queryPageOrganization`,
            null,
            this.formValue[`PURCHASE_ORG`], // 列字段的名字
            '4bb0a385-e47d-4c12-a6be-c3c47d128497'
          )
          break
        case 'SUPPLIER':
          this.noTreeMapRender(
            `getSupplierApi`,
            null,
            this.formValue[`SUPPLIER`],
            'c7e61075-5a76-425a-8b8a-5c007daa020a'
          ) // 列字段的名字);
          break
        case 'CUSTOMER':
          this.noTreeMapRender(
            `getCustomApi`,
            null,
            this.formValue[`CUSTOMER`],
            '9d15501a-45b8-4568-a3bb-e2f945934c01'
          )
          break
        case 'CATEGORY':
          this.noTreeMapRender(
            `getCateTreeApi`,
            null,
            this.formValue[`CATEGORY`],
            '360f09c5-8070-40ed-af3e-ce21ae388e03'
          )
          break
        case 'ITEM':
          this.materialThrow('c7684d1b-6b34-4381-98e4-6d57f5bef1d1')
          //   this.noTreeMapRender(`getMarticApi`, null, `itemName`);
          break
        case 'ITEM_GROUP':
          this.noTreeMapRender(
            `getMarticGroupApi`,
            null,
            this.formValue[`ITEM_GROUP`],
            'e005d19e-bdcb-400a-b551-b096a0b83a69'
          )
          break
        case 'SKU':
          this.noTreeMapRender(
            `getSkuApi`,
            null,
            this.formValue[`SKU`],
            '0a63595e-41f8-4e09-a8dc-8bff85066d07'
          )
          break
        case 'CREATE_USER':
          this.noTreeMapRender(
            `getEmployeeApi`,
            null,
            this.formValue[`CREATE_USER`],
            '26963a0a-1848-4f38-9b4b-1207c9644836'
          )
          break
        case 'DEPT':
          this.noTreeMapRender(
            `queryPageDept`, // 调用接口的名字
            {
              defaultRules: [
                {
                  field: 'orgLevelTypeCode',
                  operator: 'equal',
                  type: 'string',
                  value: 'ORG03'
                }
              ]
            }, // 调用接口传的参数
            this.formValue[`DEPT`], // 列字段的名字
            'de98f54c-480f-435c-bd4a-4aa1e92f8e5b'
          )
          break
        case 'PRICE_CATEGORY':
          this.treeMapRender(
            `getPriceTypeApi`,
            { dictCode: 'PRICE_CATEGORY', ...param },
            this.formValue[`PRICE_CATEGORY`],
            '0122074d-3b3b-43e3-bd52-5a6722e9a416'
          )
          break
        case 'MB-TYPE':
          this.treeMapRender(
            `getPerformanceTemApi`,
            { dictCode: 'MB-TYPE', ...param },
            this.formValue[`MB-TYPE`],
            '0122074d-3b3b-43e3-bd52-5a6722e9a446'
          )
          break
        case 'RECONCILIATION_TYPE':
          this.treeMapRender(
            `getReconciliaTypeApi`,
            { dictCode: 'RECONCILIATION_TYPE', ...param },
            this.formValue[`RECONCILIATION_TYPE`],
            'f9543bf7-3ed5-4820-9973-dfc983222092'
          )
          break
        default:
          break
      }
    },
    // 渲染树表格
    async treeMapRender(apiName, param, field, gridId) {
      this.pageConfig[0].gridId = gridId
      const roots = {
        subjectId: this.subjectId,
        subjectType: this.subjectType,
        dimensionCode: this.currentValue
      }
      this.nodeIndexAssigned = []
      // const urls =
      //   this.$route.query.source == 1 ? `getSelectedAndRoleDataQuery` : `getSelectedRoleDataQuery`
      // const selectRows = await this.$API.dataScope[urls](roots)
      let selectRows = null
      if (this.$route.query.source == 1) {
        selectRows = await this.$API.dataScope.getSelectedAndRoleDataQuery(roots)
        // if()
      } else {
        selectRows = await this.$API.dataScope.getSelectedRoleDataQuery(roots)

        if (
          selectRows.data.length == 1 &&
          selectRows.data[0].dimensionCodeValue == 'ALL_PERMISSION'
        ) {
          this.isNewProduct = true
        } else {
          this.isNewProduct = false
        }
      }
      this.selectRowIds = selectRows.data.map((i) => i.dimensionIdValue)
      this.selectRowClone = selectRows.data

      const res = await this.$API.dataScope[apiName](param)
      const result = res.data ? (Array.isArray(res.data) ? res.data : res.data.treeList) : []
      const data = result.map((item) => {
        if (item.children === undefined) {
          item.children = []
        }
        return {
          ...item
        }
      })
      const columnData = columnDataSetFn(field)
      this.pageConfig[0].treeGrid = Object.assign({}, this.pageConfig[0].treeGrid, {
        dataSource: data,
        columnData: columnData,
        autoCheckHierarchy: false
      })
      this.isTree = true
      // this.$refs.templateRef.refreshCurrentGridData();
      // this.pageConfig = pageConfig;
    },
    // 渲染不是树表格
    async noTreeMapRender(apiName, data, field, gridId) {
      this.pageConfigPage[0].gridId = gridId
      let otherFields = []
      if (this.currentValue === 'SITE') {
        otherFields = [
          {
            field: 'parentName',
            headerText: this.$t('归属公司'),
            textAlign: 'left'
          }
        ]
      }
      if (this.currentValue === 'COMPANY') {
        otherFields = [
          {
            field: 'parentName',
            headerText: this.$t('上级单位'),
            textAlign: 'left',
            ignore: true
          }
        ]
      }
      // 0726 供应商 - 增加-多个供应商编码 查询
      if (this.currentValue === 'SUPPLIER') {
        otherFields = [
          {
            field: 'supplierCodes',
            headerText: this.$t('供应商编码'),
            textAlign: 'left',
            width: '0',
            searchOptions: { maxQueryValueLength: 10000 }
          }
        ]
      }
      // 0726 品类 - 增加 - 多个品类编码 查询
      if (this.currentValue === 'CATEGORY') {
        otherFields = [
          {
            field: 'categoryCodes',
            headerText: this.$t('品类编码'),
            textAlign: 'left',
            width: '0',
            searchOptions: { maxQueryValueLength: 10000 }
          }
        ]
      }
      this.pageConfigPage[0].grid.asyncConfig.url = ''
      const roots = {
        subjectId: this.subjectId,
        subjectType: this.subjectType,
        dimensionCode: this.currentValue
      }
      // const urls =
      //   this.$route.query.source == 1 ? `getSelectedAndRoleDataQuery` : `getSelectedRoleDataQuery`
      // const selectRows = await this.$API.dataScope[urls](roots)
      let selectRows = null
      if (this.$route.query.source == 1) {
        selectRows = await this.$API.dataScope.getSelectedAndRoleDataQuery(roots)
        // if()
      } else {
        selectRows = await this.$API.dataScope.getSelectedRoleDataQuery(roots)

        if (
          selectRows.data.length == 1 &&
          selectRows.data[0].dimensionCodeValue == 'ALL_PERMISSION'
        ) {
          this.isNewProduct = true
        } else {
          this.isNewProduct = false
        }
      }
      this.selectRowIds = selectRows.data.map((i) => i.dimensionIdValue)

      this.selectRowClone = selectRows.data
      this.nodeIndexAssigned = []
      console.log('121231312', this.$API.dataScope[apiName])
      this.pageConfigPage[0].grid = Object.assign({}, this.pageConfigPage[0].grid, {
        dataSource: [],
        asyncConfig: {
          url: this.$API.dataScope[apiName],
          serializeList: (list) => {
            this.nodeIndexAssigned = [] //翻页清空选中
            if (this.currentValue === 'CREATE_USER') {
              list.map((e) => {
                e.employeeName = e.userName
              })
            }
            return list
          },
          afterAsyncData: this.afterAsyncData,
          params: data
        },
        columnData: columnDataSetFn(field, otherFields)
      })
      this.isTree = false
      // if (this.needRefresh) {
      //   this.$refs.templateRef.refreshCurrentGridData();
      // }
      // this.$nextTick(() => {
      //   const currentTab = this.$refs.templateRef.getCurrentUsefulRef();
      //   const grid = currentTab.gridRef;
      //   grid.selectIdRecords = this.selectRowIds.map((item) => {
      //     return {
      //       id: item,
      //       [this.formValue[this.currentValue].name]:
      //         this.getSeletedRowName(item),
      //     };
      //   });
      // });
    },
    // 获取维度枚举
    async getDimensionEnumList() {
      const res = await this.$API.dataScope.getDimensionEnumApi()
      this.enumsList = res.data
      if (this.enumsList.length) {
        this.selectBusiness(this.enumsList[this.activeIndex], this.activeIndex)
      }
    },
    // 回显checkbox得函数
    dataBound() {
      const currentTab = this.$refs.templateRef.getCurrentUsefulRef()
      const grid = currentTab.gridRef
      if (this.isTree) {
        grid.selectIdRecords = this.selectRowIds.map((item) => {
          return {
            id: item
          }
        })
      }
    },
    // 哪些checkBox需要回显
    rowDataBound(e) {
      if (this.selectRowIds.includes(e.data.id)) {
        this.nodeIndexAssigned.push(parseInt(e.row.getAttribute('aria-rowindex')))
      }
    },
    back() {
      this.$router.push({
        path: '/masterdata/data-permission-role'
      })
    },
    itemSourceReplace(e) {
      const [cut] = e
      const dataSource = utils.cloneDeep(this.pageConfigItem[0].grid.dataSource)
      dataSource.splice(cut._index - 1, 1, cut)
      this.pageConfigItem[0].grid = Object.assign({}, this.pageConfigItem[0].grid, {
        dataSource: dataSource
      })
    },
    itemSourceInsert(e) {
      let datas = []
      e.map((item) => {
        if (item.dimensionValueSource == 0) {
          const source = this.pageConfigItem[0].grid.dataSource.filter(
            (i) => i.dimensionIdValue == item.dimensionIdValue
          )
          if (!source.length) {
            datas.push(item)
          }
        } else {
          datas.push(item)
        }
      })
      const dataSource = this.pageConfigItem[0].grid.dataSource.concat(datas).map((item, index) => {
        return {
          ...item,
          _index: index + 1
        }
      })
      this.pageConfigItem[0].grid = Object.assign({}, this.pageConfigItem[0].grid, {
        dataSource: dataSource
      })
    },

    async materialThrow(gridId) {
      const roots = {
        subjectId: this.subjectId,
        subjectType: this.subjectType,
        dimensionCode: this.currentValue
      }
      // const urls =
      //   this.$route.query.source == 1 ? `getSelectedAndRoleDataQuery` : `getSelectedRoleDataQuery`
      // const selectRows = await this.$API.dataScope[urls](roots)
      let selectRows = null
      if (this.$route.query.source == 1) {
        selectRows = await this.$API.dataScope.getSelectedAndRoleDataQuery(roots)
        // if()
      } else {
        selectRows = await this.$API.dataScope.getSelectedRoleDataQuery(roots)

        if (
          selectRows.data.length == 1 &&
          selectRows.data[0].dimensionCodeValue == 'ALL_PERMISSION'
        ) {
          this.isNewProduct = true
        } else {
          this.isNewProduct = false
        }
      }
      this.selectRowClone = utils.cloneDeep(selectRows.data)
      this.pageConfigItem[0].gridId = gridId
      this.pageConfigItem[0].grid = Object.assign({}, this.pageConfigItem[0].grid, {
        dataSource: selectRows.data.map((item, index) => {
          return {
            ...item,
            _index: index + 1
          }
        })
      })
    },
    Materialsubmit() {
      const selectedRows = this.pageConfigItem[0].grid.dataSource
      const baseDataArry = utils.cloneDeep(this.selectRowClone)
      const selectDemissIds = selectedRows.map((item) => item.dimensionIdValue).filter((e) => e)
      const defaultDemissIds = baseDataArry.map((item) => item.dimensionIdValue)
      const newDemissIds = [] // 新增的
      selectDemissIds.map((item) => {
        if (!defaultDemissIds.includes(item)) {
          newDemissIds.push(item)
        }
      })
      const delDemissIds = []
      defaultDemissIds.map((item) => {
        if (!selectDemissIds.includes(item)) {
          delDemissIds.push(item)
        }
      })
      const dimensionDataList = [].concat(selectedRows.filter((i) => !i.dimensionIdValue))
      const dimensionCancelDataList = []
      newDemissIds.map((item) => {
        const newArr = _.find(selectedRows, { dimensionIdValue: item })
        dimensionDataList.push(newArr)
      })
      delDemissIds.map((item) => {
        const newArr = _.find(this.selectRowClone, { dimensionIdValue: item })
        dimensionCancelDataList.push(newArr)
      })
      const requestparam = {
        subjectId: this.subjectId,
        subjectType: this.subjectType,
        dimensionCode: this.currentValue,
        dimensionDataList,
        dimensionCancelDataList
      }
      this.$API.dataScope.addRoleDataApi(requestparam).then((res) => {
        this.$toast({
          content: res.msg,
          type: 'success'
        })
        this.needRefresh = false
        this.changeType(this.enumsList[this.activeIndex])
      })
    },
    getSeletedRowName(dimensionIdValue) {
      return _.find(this.selectRowClone, { dimensionIdValue })?.dimensionNameValue
    },
    getSeletedRowCode(dimensionIdValue) {
      return _.find(this.selectRowClone, { dimensionIdValue })?.dimensionCodeValue
    },
    afterAsyncData(e) {
      this.sourceDataList = e.data.records
    }
  },
  watch: {
    sourceDataList: {
      handler(n, o) {
        if (JSON.stringify(n) === JSON.stringify(o) || !o.length || !this.needRefresh) {
          this.$nextTick(() => {
            const currentTab = this.$refs.templateRef.getCurrentUsefulRef()
            const grid = currentTab.gridRef
            grid.selectIdRecords = this.selectRowIds.map((item) => {
              return {
                id: item,
                [this.formValue[this.currentValue].name]: this.getSeletedRowName(item),
                [this.formValue[this.currentValue].code]: this.getSeletedRowCode(item)
              }
            })
            this.needRefresh = true
          })
        }
      },
      deep: true
    }
  }
}
</script>
<style lang="scss" scoped>
.data-permission-setting {
  padding: 20px;
  height: 100%;
  background: #fff;
  .data-permission-header {
    width: 100%;
    height: 94px;
    background: rgba(99, 134, 193, 0.08);
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    .header-left {
      display: flex;
    }
    .items {
      margin-right: 50px;
      font-size: 15px;
    }
  }
  .data-permission-buessness {
    display: flex;
    font-size: 14px;
    padding: 20px 0;
    align-items: center;
    span {
      margin-right: 30px;
      display: block;
      height: 35px;
      line-height: 35px;
      cursor: pointer;
    }
    .active {
      border-bottom: 2px solid rgba(0, 70, 156, 1);
      color: rgba(0, 70, 156, 1);
    }
  }
  .table-wrap {
    height: calc(100vh - 225px);
  }
}
.detail-header-button {
  color: rgba(0, 70, 156, 1);
}
.marticWrap {
  height: calc(100vh - 300px);
}
::v-deep .marticWrap .e-gridcontent {
  height: calc(100vh - 300px) !important;
}
/deep/ .treeTemplate .e-grid .e-gridheader .e-headercell .e-headercelldiv.e-headerchkcelldiv {
  padding: 0 1.8em 0 2.1em;
}
</style>
