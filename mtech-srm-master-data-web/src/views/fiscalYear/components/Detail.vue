<!--
 * @Author: your name
 * @Date: 2022-02-17 13:37:14
 * @LastEditTime: 2022-02-17 14:44:30
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\fiscalYear\components\Detail.vue
-->
<template>
  <div>
    <mt-dialog
      ref="dialog"
      :header="$t('期间管理')"
      :enable-resize="false"
      :position="{ X: 'right', Y: 'top' }"
      css-class="create-proj-dialog right-wrapper"
      :buttons="buttons"
    >
      <div class="full-height">
        <mt-form ref="rowInfo" :model="rowInfo">
          <mt-row :gutter="20">
            <mt-col :sm="12" :md="12" :lg="12" :xl="12">
              <mt-form-item prop="fiscalYearCode" :label="$t('会计年度代码')">
                <mt-input
                  v-model="rowInfo.fiscalYearCode"
                  :readonly="true"
                  :show-clear-button="false"
                  type="text"
                  :placeholder="$t('请输入会计年度代码')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :sm="12" :md="12" :lg="12" :xl="12">
              <mt-form-item prop="fiscalYearName" :label="$t('会计年度名称')">
                <mt-input
                  v-model="rowInfo.fiscalYearName"
                  :readonly="true"
                  :show-clear-button="false"
                  type="text"
                  :placeholder="$t('请输入会计年度名称')"
                ></mt-input>
              </mt-form-item>
            </mt-col>
          </mt-row>
        </mt-form>

        <mt-template-page
          v-if="visible"
          ref="tepPage"
          :hidden-tabs="true"
          :template-config="pageConfig"
          @handleClickToolBar="handleClickToolBar"
          @handleClickCellTool="handleClickCellTool"
        ></mt-template-page>
      </div>
    </mt-dialog>

    <mt-dialog
      ref="dialogDetailAction"
      :header="headerTitle"
      css-class="create-proj-dialog"
      :buttons="detailBtn"
    >
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="fiscalYearItemName" :label="$t('期间名称')">
          <mt-input
            v-model="ruleForm.fiscalYearItemName"
            :readonly="disabled"
            :show-clear-button="false"
            type="text"
            :placeholder="$t('请输入期间名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="fiscalYearItemCode" :label="$t('期间代码')">
          <mt-input
            v-model="ruleForm.fiscalYearItemCode"
            :readonly="type !== 'add'"
            :show-clear-button="false"
            type="text"
            :placeholder="$t('请输入期间代码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="fiscalYearType" :label="$t('类型')">
          <mt-select
            v-model="ruleForm.fiscalYearType"
            :fields="fields.ITEM_TYPES"
            :data-source="options.ITEM_TYPES"
            :allow-filtering="true"
            :show-clear-button="true"
            :placeholder="$t('请选择类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="externalCode" :label="$t('第三方编码')">
          <mt-input
            v-model="ruleForm.externalCode"
            :readonly="type !== 'add'"
            :show-clear-button="false"
            type="text"
            :placeholder="$t('请输入第三方编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="fiscalYearItemDescription" :label="$t('期间描述')">
          <mt-input
            v-model="ruleForm.fiscalYearItemDescription"
            :readonly="disabled"
            :show-clear-button="false"
            type="text"
            :placeholder="$t('请输入期间描述')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import { PAGE_ASSIGN_PLUGIN, RULES, OPTIONS, FIELDS } from '../config/fiscalYear.config'
import { formatRules } from '@/utils/util'
export default {
  data() {
    return {
      pageConfig: PAGE_ASSIGN_PLUGIN,
      options: OPTIONS,
      fields: FIELDS,

      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      visible: false,
      rowInfo: {},

      detailBtn: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.detailSave,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {},
      type: 'add', // add edit preview
      rules: RULES
    }
  },
  computed: {
    headerTitle() {
      if (this.type === 'add') {
        return this.$t('新增')
      }
      if (this.type === 'edit') {
        return this.$t('编辑')
      }
      return this.$t('预览')
    },
    disabled() {
      if (this.type === 'preview') {
        return true
      }
      return false
    }
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const rowSelected = grid?.getSelectedRecords?.()

      if (toolbar.id === 'Add') {
        this.handleAction('add')
        this.rulesGet('fiscalYearItemAddValid')
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDelete(rowSelected)
      }
    },
    handleClickCellTool(e) {
      const { data, tool } = e

      if (tool.id === 'edit') {
        this.handleAction('edit', data)
        this.rulesGet('fiscalYearItemEditValid')
      }
      if (tool.id === 'delete') {
        this.handleDelete([data])
      }
    },
    handleDelete(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.finance.fiscalYearItemDelete(data).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    },
    handleAction(type, data) {
      this.type = type
      this.$refs.ruleForm.resetFields()
      this.ruleForm = Object.assign({}, data)
      this.$refs.dialogDetailAction.ejsRef.show()
    },
    detailSave() {
      if (this.type === 'preview') {
        this.cancel()
        return false
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const data = {
            fiscalYearId: this.rowInfo.id,
            ...this.ruleForm
          }
          if (this.type === 'add') {
            this.$API.finance.fiscalYearItemAdd(data).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
          if (this.type === 'edit') {
            this.$API.finance.fiscalYearItemEdit(data).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
        }
      })
    },
    cancel() {
      this.$refs.dialogDetailAction.ejsRef.hide()
    },
    save() {
      this.hide()
    },
    show(row) {
      this.rowInfo = row
      this.$refs.dialog.ejsRef.show()
      this.visible = true
      this.pageConfig[0].grid.asyncConfig.params = Object.assign({}, { fiscalYearId: row.id })
    },
    hide() {
      this.$refs.dialog.ejsRef.hide()
      this.visible = false
    },
    rulesGet(type) {
      this.$API.finance[type]().then((res) => {
        this.rules = formatRules(res.data)
      })
    }
  }
}
</script>

<style lang="scss">
.e-dlg-container > .right-wrapper {
  height: 100% !important;
  .e-dlg-header-content {
    background-color: rgba(243, 243, 243, 1);
    border-radius: 8px 0 0 0;
    .e-dlg-closeicon-btn {
      .e-btn-icon {
        color: #292929 !important;
      }
    }
    .e-dlg-header {
      color: #292929;
    }
  }
  .e-dlg-content {
    background-color: #f8f8f8;
  }
  .e-footer-content {
    background: rgba(255, 255, 255, 1);
    border-radius: 0 0 0 8px;
    box-shadow: inset 0 1px 0 0 rgba(232, 232, 232, 1);
  }
}
</style>
