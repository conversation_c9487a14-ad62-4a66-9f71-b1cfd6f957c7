import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2022-02-17 10:02:45
 * @LastEditTime: 2022-02-17 15:54:40
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\fiscalYear\config\fiscalYear.config.js
 */

export const FIELDS = {
  TYPES: {
    text: 'text',
    value: 'value'
  },
  ITEM_TYPES: {
    text: 'text',
    value: 'value'
  }
}
export const RULES = {}

export const OPTIONS = {
  TYPES: [
    { text: i18n.t('日历年'), value: 0, cssClass: '' },
    { text: i18n.t('日期年'), value: 1, cssClass: '' }
  ],
  ITEM_TYPES: [
    { text: i18n.t('正常期间'), value: '0', cssClass: '' },
    { text: i18n.t('特殊期间'), value: '1', cssClass: '' }
  ]
}

export const PAGE_CONFIG = [
  {
    toolbar: ['Add', 'Delete'],
    grid: {
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'fiscalYearCode',
          headerText: i18n.t('会计年度代码'),
          width: '250',
          cellTools: [
            'edit',
            'delete',
            'preview',
            {
              id: 'view_detail',
              icon: 'icon_solid_Createproject',
              title: i18n.t('期间管理')
            }
          ]
        },
        {
          field: 'fiscalYearName',
          width: '200',
          headerText: i18n.t('会计年度名称')
        },
        {
          field: 'fiscalYearType',
          width: '200',
          headerText: i18n.t('会计年度类型'),
          valueConverter: {
            type: 'map',
            map: OPTIONS.TYPES
          }
        },
        {
          field: 'fiscalYearDescription',
          width: '200',
          headerText: i18n.t('会计年度描述')
        },
        {
          field: 'commonPeriodNum',
          width: '200',
          headerText: i18n.t('正常期间数')
        },
        {
          field: 'specialPeriodNum',
          width: '200',
          headerText: i18n.t('特殊期间数')
        },
        {
          field: 'statusId',
          headerText: i18n.t('状态'),
          width: '150',
          valueConverter: {
            type: 'map',
            map: {
              0: i18n.t('草稿'),
              1: i18n.t('激活'),
              2: i18n.t('分发'),
              3: i18n.t('失效'),
              '-1': i18n.t('待审核'),
              '-2': i18n.t('准备')
            }
          }
        },
        {
          field: 'externalCode',
          width: '200',
          headerText: i18n.t('第三方编码')
        }
      ],
      asyncConfig: {
        url: '/masterDataManagement/tenant/fiscal-year/paged-query',
        params: {}
      }
    }
  }
]

export const PAGE_ASSIGN_PLUGIN = [
  {
    toolbar: ['Add', 'Delete'],
    grid: {
      allowPaging: false,
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'fiscalYearItemCode',
          headerText: i18n.t('期间代码'),
          width: '150',
          cellTools: ['edit', 'delete']
        },
        {
          field: 'fiscalYearItemName',
          width: '200',
          headerText: i18n.t('期间名称')
        },
        {
          field: 'fiscalYearItemType',
          width: '100',
          headerText: i18n.t('期间类型'),
          valueConverter: {
            type: 'map',
            map: OPTIONS.ITEM_TYPES
          }
        },
        {
          field: 'statusId',
          headerText: i18n.t('状态'),
          width: '150',
          valueConverter: {
            type: 'map',
            map: {
              0: i18n.t('草稿'),
              1: i18n.t('激活'),
              2: i18n.t('分发'),
              3: i18n.t('失效'),
              '-1': i18n.t('待审核'),
              '-2': i18n.t('准备')
            }
          }
        },
        {
          field: 'fiscalYearItemDescription',
          width: '150',
          headerText: i18n.t('期间描述')
        },
        {
          field: 'externalCode',
          width: '150',
          headerText: i18n.t('第三方编码')
        }
      ],
      asyncConfig: {
        url: '/masterDataManagement/tenant/fiscal-year-item/criteria-query',
        params: {},
        recordsPosition: 'data'
      }
    }
  }
]
