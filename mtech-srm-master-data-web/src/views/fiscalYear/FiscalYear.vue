<!--
 * @Author: your name
 * @Date: 2022-02-17 09:59:02
 * @LastEditTime: 2022-02-17 14:27:47
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\src\views\fiscalYear\FiscalYear.vue
-->
<template>
  <div class="full-height mt-pt-20">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
    <mt-dialog ref="dialog" :header="headerTitle" css-class="create-proj-dialog" :buttons="buttons">
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="fiscalYearName" :label="$t('会计年度名称')">
          <mt-input
            v-model="ruleForm.fiscalYearName"
            :readonly="disabled"
            :show-clear-button="false"
            type="text"
            :placeholder="$t('请输入会计年度名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="fiscalYearCode" :label="$t('会计年度编码')">
          <mt-input
            v-model="ruleForm.fiscalYearCode"
            :readonly="type !== 'add'"
            :show-clear-button="false"
            type="text"
            :placeholder="$t('请输入会计年度编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="fiscalYearType" :label="$t('会计年度类型')">
          <mt-select
            v-model="ruleForm.fiscalYearType"
            :fields="fields.TYPES"
            :data-source="options.TYPES"
            :allow-filtering="true"
            :show-clear-button="true"
            :placeholder="$t('请选择会计年度类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="commonPeriodNum" :label="$t('正常期间数')">
          <mt-input-number
            v-model="ruleForm.commonPeriodNum"
            :readonly="type !== 'add'"
            :show-clear-button="false"
            :placeholder="$t('请输入正常期间数')"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item prop="specialPeriodNum" :label="$t('特殊期间数')">
          <mt-input-number
            v-model="ruleForm.specialPeriodNum"
            :readonly="type !== 'add'"
            :show-clear-button="false"
            :placeholder="$t('请输入特殊期间数')"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item prop="externalCode" :label="$t('第三方编码')">
          <mt-input
            v-model="ruleForm.externalCode"
            :readonly="type !== 'add'"
            :show-clear-button="false"
            type="text"
            :placeholder="$t('请输入第三方编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="fiscalYearDescription" :label="$t('会计年度描述')">
          <mt-input
            v-model="ruleForm.fiscalYearDescription"
            :readonly="disabled"
            :show-clear-button="false"
            type="text"
            :placeholder="$t('请输入会计年度描述')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </mt-dialog>

    <detail ref="detail"></detail>
  </div>
</template>

<script>
import { PAGE_CONFIG, RULES, OPTIONS, FIELDS } from './config/fiscalYear.config'
import Detail from './components/Detail.vue'

import { formatRules } from '@/utils/util'
export default {
  components: {
    Detail
  },
  data() {
    return {
      options: OPTIONS,
      pageConfig: PAGE_CONFIG,
      rules: RULES,
      fields: FIELDS,

      pageInfo: {
        current: 1,
        size: 10
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {},
      type: 'add' // add edit preview
    }
  },
  computed: {
    headerTitle() {
      if (this.type === 'add') {
        return this.$t('新增')
      }
      if (this.type === 'edit') {
        return this.$t('编辑')
      }
      return this.$t('预览')
    },
    disabled() {
      if (this.type === 'preview') {
        return true
      }
      return false
    }
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const rowSelected = grid.getSelectedRecords()
      if (toolbar.id === 'Add') {
        this.handleAction('add')
        this.rulesGet('fiscalYearAddValid')
      }
      if (toolbar.id === 'Delete' && rowSelected.length) {
        this.handleDelete(rowSelected)
      }
    },
    handleClickCellTool(e) {
      const { data, tool } = e
      if (tool.id === 'edit') {
        this.handleAction('edit', data)
        this.rulesGet('fiscalYearEditValid')
      }
      if (tool.id === 'delete') {
        this.handleDelete([data])
      }
      if (tool.id === 'preview') {
        this.handleAction('preview', data)
      }
      if (tool.id === 'view_detail') {
        this.$refs.detail.show(data)
      }
    },
    handleAction(type, data) {
      this.type = type
      this.$refs.ruleForm.resetFields()
      this.ruleForm = Object.assign({}, data)
      this.$refs.dialog.ejsRef.show()
    },
    handleDelete(rows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const ids = rows.map((e) => e.id)
          const data = { ids }
          this.$API.finance.fiscalYearDelete(data).then(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          })
        }
      })
    },
    save() {
      if (this.type === 'preview') {
        this.cancel()
        return false
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.type === 'add') {
            const data = {
              ...this.ruleForm
            }
            this.$API.finance.fiscalYearAdd(data).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
          if (this.type === 'edit') {
            this.$API.finance.fiscalYearEdit(this.ruleForm).then(() => {
              this.$refs.tepPage.refreshCurrentGridData()
              this.cancel()
            })
          }
        }
      })
    },
    cancel() {
      this.$refs.dialog.ejsRef.hide()
    },
    rulesGet(type) {
      this.$API.finance[type]().then((res) => {
        this.rules = formatRules(res.data)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/.btn-text {
  color: #4f5b6d;
}
/deep/ .mt-data-grid {
  .status-label {
    font-size: 12px;
    padding: 4px;
    border-radius: 2px;
    &.status-0 {
      color: #9baac1;
      background: rgba(155, 170, 193, 0.1);
    }
    &.status-1 {
      color: #6386c1;
      background: rgba(99, 134, 193, 0.1);
    }
  }
}
/deep/.mt-dialog {
  display: none;
}
</style>
