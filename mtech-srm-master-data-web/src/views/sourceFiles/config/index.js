import { i18n } from '@/main.js'
// import { formatDate } from '@/utils/util'
// import Vue from 'vue'
export const columnData = [
  {
    width: 150,
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    allowResizing: false,
    searchOptions: {
      operator: 'likeright'
    },
    customAttributes: {
      class: 'sticky-col-1'
    }
  },
  {
    with: 150,
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    allowResizing: false,
    customAttributes: {
      class: 'sticky-col-2'
    }
  },
  {
    width: 120,
    field: 'organizationCode',
    headerText: i18n.t('公司代码'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    field: 'organizationName',
    headerText: i18n.t('公司名称')
  },
  {
    width: 120,
    field: 'siteCode',
    headerText: i18n.t('工厂代码'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: 120,
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: 120,
    field: 'purchaseOrganizationCode',
    headerText: i18n.t('采购组织代码')
  },
  {
    field: 'purchaseOrganizationName',
    headerText: i18n.t('采购组织名称')
  },
  {
    width: 120,
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    field: 'statusId',
    headerText: i18n.t('货源状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('草稿'),
        1: i18n.t('激活'),
        3: i18n.t('失效'),
        4: i18n.t('下发SAP失败'),
        5: i18n.t('冻结')
      }
    }
  },
  {
    width: 120,
    field: 'sourceType',
    headerText: i18n.t('货源类型'),
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('临时货源'), 2: i18n.t('一次性货源'), 3: i18n.t('正式货源') }
    }
  },
  {
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未同步'),
        1: i18n.t('同步成功'),
        2: i18n.t('同步失败'),
        3: i18n.t('同步中')
      }
    }
  },
  {
    field: 'failReason',
    headerText: i18n.t('SAP错误信息')
  },
  {
    width: 150,
    field: 'startTime',
    headerText: i18n.t('有效时间从'),
    valueConverter: {
      type: 'date'
    },
    searchOptions: {
      type: 'date',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return Number(new Date(e.toString()))
      }
    }
  },
  {
    width: 150,
    field: 'endTime',
    headerText: i18n.t('有效时间至'),
    valueConverter: {
      type: 'date'
    },
    searchOptions: {
      type: 'date',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return Number(new Date(e.toString()))
      }
    }
  },
  {
    width: '100',
    field: 'limitTradeTotal',
    headerText: i18n.t('限制数量')
  },
  {
    width: '100',
    field: 'limitTradeTimes',
    headerText: i18n.t('限制次数')
  },
  {
    field: 'mrp',
    headerText: i18n.t('MRP'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('与MRP有关的记录'),
        2: i18n.t('与MRP计划表自动生成的计划行有关的记录')
      }
    }
  },
  {
    width: '120',
    field: 'isDump',
    headerText: i18n.t('转储类货源'),
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('是'), 0: i18n.t('否') }
    }
  },
  {
    field: 'dumpSiteName',
    headerText: i18n.t('采购工厂')
  },
  {
    field: 'sourceCode',
    headerText: i18n.t('来源单号')
  },
  {
    field: 'quotaNo',
    headerText: i18n.t('样品确认单号')
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间')
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'id',
    headerText: i18n.t('ID')
  }
]
