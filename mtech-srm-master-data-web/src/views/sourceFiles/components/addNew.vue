<template>
  <div class="inventory">
    <!-- 头部 -->
    <div class="top-info">
      <!-- 顶部信息 -->
      <div class="detail-info">
        <div class="name-wrap"></div>
        <div class="btns-wrap">
          <mt-button
            css-class="e-flat"
            class="detail-header-button"
            @click="$router.push('/masterdata/source-files')"
            >{{ $t('返回') }}</mt-button
          >
          <mt-button
            css-class="e-flat"
            class="detail-header-button"
            @click="clickButtonSave('0')"
            >{{ $t('保存') }}</mt-button
          >
          <mt-button
            css-class="e-flat"
            class="detail-header-button"
            @click="clickButtonSave('1')"
            >{{ $t('保存并同步') }}</mt-button
          >
        </div>
      </div>
      <div class="formInput">
        <mt-form ref="dialogRef" :model="formObject" :rules="queryId ? editRules : rules">
          <mt-form-item prop="organizationId" v-if="queryId" :label="$t('公司')">
            <mt-select
              v-model="formObject.organizationId"
              float-label-type="Never"
              :disabled="isEdit"
              :data-source="orgList"
              :show-clear-button="true"
              :fields="{ text: 'orgName', value: 'id' }"
              @change="change($event, 'organizationId')"
              @select="select($event, 'organizationId')"
              :placeholder="$t('请选择公司')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="orgCodes" v-else :label="$t('公司')">
            <mt-multi-select
              v-model="formObject.orgCodes"
              float-label-type="Never"
              :data-source="orgList"
              :allow-filtering="true"
              :show-clear-button="true"
              :fields="{ text: 'orgName', value: 'orgCode' }"
              :filtering="(e) => filteringResource(e, orgList, 'orgName')"
              @change="change($event, 'orgCodes')"
              :placeholder="$t('请选择公司')"
            ></mt-multi-select>
          </mt-form-item>
          <mt-form-item prop="supplierId" v-if="queryId" :label="$t('供应商')">
            <mt-select
              v-model="formObject.supplierId"
              float-label-type="Never"
              :disabled="isEdit"
              :data-source="supplierList"
              :allow-filtering="true"
              :show-clear-button="true"
              :fields="{ text: 'supplierName', value: 'id' }"
              :filtering="(e) => filteringResource(e, supplierList, 'supplierName')"
              @change="change($event, 'supplierName')"
              :placeholder="$t('请选择供应商')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="supplierCodes" v-else :label="$t('供应商')">
            <mt-multi-select
              v-model="formObject.supplierCodes"
              float-label-type="Never"
              :data-source="supplierList"
              :allow-filtering="true"
              :show-clear-button="true"
              :fields="{ text: 'supplierName', value: 'supplierCode' }"
              :filtering="(e) => filteringResource(e, supplierList, 'supplierName')"
              @change="change($event, 'supplierCodes')"
              :placeholder="$t('请选择供应商')"
            ></mt-multi-select>
          </mt-form-item>
          <mt-form-item prop="purchaseOrganizationId" v-if="queryId" :label="$t('采购组织')">
            <mt-select
              v-model="formObject.purchaseOrganizationId"
              float-label-type="Never"
              :data-source="organizationList"
              :disabled="isEdit"
              :allow-filtering="true"
              :show-clear-button="true"
              :fields="{ text: 'organizationName', value: 'id' }"
              @change="change($event, 'purchaseOrganizationName')"
              :filtering="(e) => filteringResource(e, organizationList, 'organizationName')"
              :placeholder="$t('请选择采购组织')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="organizationCodes" v-else :label="$t('采购组织')">
            <mt-multi-select
              v-model="formObject.organizationCodes"
              float-label-type="Never"
              :data-source="organizationList"
              :allow-filtering="true"
              :show-clear-button="true"
              :fields="{ text: 'organizationName', value: 'organizationCode' }"
              @change="change($event, 'organizationCodes')"
              :filtering="(e) => filteringResource(e, organizationList, 'organizationName')"
              :placeholder="$t('请选择采购组织')"
            ></mt-multi-select>
          </mt-form-item>
          <mt-form-item v-if="queryId" prop="siteId" :label="$t('工厂')">
            <mt-select
              v-model="formObject.siteId"
              float-label-type="Never"
              :disabled="isEdit"
              :data-source="siteList"
              :fields="{ text: 'siteName', value: 'id' }"
              :allow-filtering="true"
              :filtering="(e) => filteringResource(e, siteList, 'siteName')"
              @change="change($event, 'siteName')"
              @select="select($event, 'siteName')"
              :placeholder="$t('请选择工厂')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item v-else prop="siteCodes" :label="$t('工厂')">
            <mt-multi-select
              v-model="formObject.siteCodes"
              float-label-type="Never"
              :data-source="siteList"
              :show-clear-button="true"
              :allow-filtering="true"
              :fields="{ text: 'siteName', value: 'siteCode' }"
              :filtering="(e) => filteringResource(e, siteList, 'siteName')"
              @change="change($event, 'siteCodes')"
              @select="select($event, 'siteCodes')"
              :placeholder="$t('请选择工厂')"
            ></mt-multi-select>
          </mt-form-item>
          <mt-form-item prop="categoryId" v-if="queryId" :label="$t('品类')">
            <mt-select
              v-model="formObject.categoryId"
              float-label-type="Never"
              :disabled="isEdit"
              :data-source="categoryList"
              :allow-filtering="true"
              :show-clear-button="true"
              :fields="{ text: 'categoryName', value: 'id' }"
              :filtering="(e) => filteringResource(e, categoryList, 'categoryName')"
              @change="change($event, 'categoryName')"
              @select="select($event, 'categoryName')"
              :placeholder="$t('请选择品类')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="categoryCodes" v-else :label="$t('品类')">
            <mt-multi-select
              v-model="formObject.categoryCodes"
              float-label-type="Never"
              :data-source="categoryList"
              :allow-filtering="true"
              :show-clear-button="true"
              :fields="{ text: 'categoryName', value: 'categoryCode' }"
              :filtering="(e) => filteringResource(e, categoryList, 'categoryName')"
              @change="change($event, 'categoryCodes')"
              :placeholder="$t('请选择品类')"
            ></mt-multi-select>
          </mt-form-item>
        </mt-form>
      </div>
    </div>
    <mt-template-page
      v-if="destroy"
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @selectedChanged="selectedChanged"
      @actionComplete="actionComplete"
      @actionBegin="actionBegin"
    ></mt-template-page>
  </div>
</template>
<script>
import { cloneDeep } from 'lodash'
// import { utils } from '@mtech-common/utils'
import { columnData } from './config/index'
import { formatDate } from '@/utils/util'
export default {
  data() {
    return {
      isEdit: false,
      destroy: true,
      addOneRowFlag: false,
      formObject: {
        orgCodes: [],
        orgList: [],
        supplierCodes: [],
        supplierList: [],
        organizationCodes: [],
        organizationList: [],
        categoryCodes: [],
        categoryList: [],
        siteCodes: [],
        siteList: [],
        organizationId: '',
        supplierId: '',
        purchaseOrganizationId: '',
        siteId: '',
        categoryId: ''
      },
      orgList: [], //公司
      supplierList: [], //供应商
      organizationList: [], //采购组织
      siteList: [], //工厂
      categoryList: [], //品类
      editFormObject: {},
      rules: {
        orgCodes: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        supplierCodes: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        organizationCodes: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        categoryCodes: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        siteCodes: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      editRules: {
        organizationId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        supplierId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        purchaseOrganizationId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        siteId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        categoryId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      historyValue: {},
      pageConfig: [
        {
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [['Add', 'Delete'], []]
          },
          useToolTemplate: false,
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowEditing: true, //开启表格编辑操作
            columnData: columnData,
            // frozenColumns: 1,
            dataSource: []
          }
        }
      ],
      editDataSource: [],
      addId: 1,
      editAddId: '',
      supplyGoods: {}
    }
  },
  computed: {
    queryId() {
      return this.$route.query.id
    }
  },
  async mounted() {
    if (this.queryId) {
      this.isEdit = true
      this.$set(this.pageConfig[0], 'toolbar', {
        useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
        tools: [[], []]
      })
      this.editRowData()
    } else {
      this.companyOrganization()
    }
  },
  methods: {
    async editRowData() {
      await this.$API.sourceFiles.queryDetail({ id: this.queryId }).then((res) => {
        this.editFormObject = { ...res.data }
        sessionStorage.setItem('sourceFilesFormObject', JSON.stringify(this.editFormObject))
        this.editDataSource = [
          {
            addId: 1,
            itemId: this.editFormObject.itemId,
            itemName: this.editFormObject.itemName,
            itemCode: this.editFormObject.itemCode,
            sourceType: this.editFormObject.sourceType,
            startTime: this.editFormObject.startTime
              ? formatDate(new Date(this.editFormObject.startTime), 'yyyy-MM-dd')
              : formatDate(new Date(), 'yyyy-MM-dd'),
            endTime:
              !this.editFormObject.endTime || !this.editFormObject.startTime
                ? formatDate(new Date(), 'yyyy-MM-dd')
                : formatDate(new Date(this.editFormObject.endTime), 'yyyy-MM-dd'),
            limitTradeTotal: this.editFormObject.limitTradeTotal,
            limitTradeTimes: this.editFormObject.limitTradeTimes,
            isDump: this.editFormObject.isDumpDesc == this.$t('否') ? 0 : 1,
            dumpSiteName: this.editFormObject.dumpSiteName,
            mrp: this.editFormObject.mrp,
            statusId: this.editFormObject.statusId,
            dumpSiteOrgId: this.editFormObject.dumpSiteOrgId,
            dumpSiteCode: this.editFormObject.dumpSiteCode,
            mrpId: this.editFormObject.mrpId,
            mrpCode: this.editFormObject.mrpCode
          }
        ]
        setTimeout(() => {
          this.$set(this.pageConfig[0].grid, 'dataSource', this.editDataSource)
        }, 0.167)
      })
      await this.companyOrganization()
    },
    async companyOrganization() {
      this.$loading()
      await this.$API.sourceFiles
        .company({})
        .then((res) => {
          this.orgList = cloneDeep(res.data)

          if (this.queryId) {
            // setTimeout(() => {
            this.formObject.organizationId = this.editFormObject.organizationId
            this.formObject.organizationCode = this.editFormObject.organizationCode
            this.formObject.organizationName = this.editFormObject.organizationName
            // }, 0.17)
          }
          this.$hloading()
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    change(e, item) {
      let { itemData } = e
      let _this = this
      if (item === 'organizationId') {
        this.siteList = []
        this.categoryList = []
        this.supplierList = []
        this.organizationList = []
        this.formObject.organizationName = itemData.orgName
        this.formObject.organizationCode = itemData.orgCode
        this.formObject.organizationId = itemData.id
        this.formObject.orgLevelTypeCode = itemData.orgLevelTypeCode
        if (e.e != null) {
          let _dataSource = this.$refs.templateRef
            .getCurrentUsefulRef()
            .gridRef.ejsRef.getCurrentViewRecords()
          if (_dataSource.length > 0 || this.addOneRowFlag) {
            this.destroy = false
            setTimeout(() => {
              this.destroy = true
            }, 0.167)
            this.addOneRowFlag = false
            sessionStorage.removeItem('sourceFilesFormObject')
          }
        }
        //工厂
        this.siteList = [
          {
            id: this.editFormObject.siteId,
            siteCode: this.editFormObject.siteCode,
            siteName: this.editFormObject.siteCode + '-' + this.editFormObject.siteName
          }
        ]
        // 供应商
        this.supplierList = [
          {
            id: this.editFormObject.supplierId,
            supplierCode: this.editFormObject.supplierCode,
            supplierName: this.editFormObject.supplierCode + '-' + this.editFormObject.supplierName
          }
        ]
        if (this.queryId) {
          this.formObject.siteId = this.editFormObject.siteId
          this.formObject.siteName =
            this.editFormObject.siteCode + '-' + this.editFormObject.siteName
          this.formObject.siteCode = this.editFormObject.siteCode
          this.formObject.supplierId = this.editFormObject.supplierId
          this.formObject.supplierCode = this.editFormObject.supplierCode
          this.formObject.supplierName =
            this.editFormObject.supplierCode + '-' + this.editFormObject.supplierName
        }
      }
      if (item === 'supplierName') {
        this.formObject.supplierName = itemData.supplierName
        this.formObject.supplierCode = itemData.supplierCode
        this.formObject.supplierId = itemData.id
        if (e.e != null) {
          let _dataSource = this.$refs.templateRef
            .getCurrentUsefulRef()
            .gridRef.ejsRef.getCurrentViewRecords()
          if (_dataSource.length > 0 || this.addOneRowFlag) {
            this.destroy = false
            setTimeout(() => {
              this.destroy = true
            }, 0.167)
            this.addOneRowFlag = false
            sessionStorage.removeItem('sourceFilesFormObject')
          }
        }
        //品类
        this.categoryList = [
          {
            id: this.editFormObject.categoryId,
            categoryCode: this.editFormObject.categoryCode,
            categoryName: this.editFormObject.categoryCode + '-' + this.editFormObject.categoryName
          }
        ]
        //采购组织
        this.organizationList = [
          {
            id: this.editFormObject.purchaseOrganizationId,
            organizationCode: this.editFormObject.purchaseOrganizationCode,
            organizationName:
              this.editFormObject.purchaseOrganizationCode +
              '-' +
              this.editFormObject.purchaseOrganizationName
          }
        ]
        if (this.queryId) {
          this.formObject.categoryId = this.editFormObject.categoryId
          this.formObject.categoryCode = this.editFormObject.categoryCode
          this.formObject.categoryName =
            this.editFormObject.categoryCode + '-' + this.editFormObject.categoryName
          this.formObject.purchaseOrganizationId = this.editFormObject.purchaseOrganizationId
          this.formObject.purchaseOrganizationCode = this.editFormObject.purchaseOrganizationCode
          this.formObject.purchaseOrganizationName =
            this.editFormObject.purchaseOrganizationCode +
            '-' +
            this.editFormObject.purchaseOrganizationName
        }
      }
      if (item === 'purchaseOrganizationName') {
        this.formObject.purchaseOrganizationName = itemData.organizationName
        this.formObject.purchaseOrganizationCode = itemData.organizationCode
        this.formObject.purchaseOrganizationId = itemData.id
      }
      if (item === 'siteName') {
        this.formObject.siteName = itemData.siteName
        this.formObject.siteCode = itemData.siteCode
        this.formObject.siteId = itemData.id
        if (e.e != null) {
          let _dataSource = this.$refs.templateRef
            .getCurrentUsefulRef()
            .gridRef.ejsRef.getCurrentViewRecords()
          if (_dataSource.length > 0 || this.addOneRowFlag) {
            this.destroy = false
            setTimeout(() => {
              this.destroy = true
            }, 0.167)
            this.addOneRowFlag = false
            sessionStorage.removeItem('sourceFilesFormObject')
          }
        }
      }
      if (item === 'categoryName') {
        this.formObject.categoryName = itemData.categoryName
        this.formObject.categoryCode = itemData.categoryCode
        this.formObject.categoryId = itemData.id
        if (e.e != null) {
          let _dataSource = this.$refs.templateRef
            .getCurrentUsefulRef()
            .gridRef.ejsRef.getCurrentViewRecords()
          if (_dataSource.length > 0 || this.addOneRowFlag) {
            this.destroy = false
            setTimeout(() => {
              this.destroy = true
            }, 0.167)
            this.addOneRowFlag = false
            sessionStorage.removeItem('sourceFilesFormObject')
          }
        }
      }
      //---------------------------->>> 新的逻辑
      if (item === 'orgCodes') {
        for (const key in this.formObject) {
          if (this.formObject[key] instanceof String) {
            this.formObject[key] = ''
          } else if (this.formObject[key] instanceof Array) {
            this.formObject[key] = []
          } else {
            this.formObject[key] = null
          }
        }
        this.siteList = []
        this.categoryList = []
        this.supplierList = []
        this.organizationList = []
        if (e.value.length > 0) {
          this.formObject.orgList.length = 0
          this.formObject.orgCodes = e.value
          this.orgList.forEach((item) => {
            if (e.value.indexOf(item.orgCode) != -1) {
              this.formObject.orgList.push({
                orgCode: item.orgCode,
                orgName: item.orgName,
                orgId: item.id
              })
            }
          })
        }
        if (e.e != null) {
          let _dataSource = this.$refs.templateRef
            .getCurrentUsefulRef()
            .gridRef.ejsRef.getCurrentViewRecords()
          if (_dataSource.length > 0 || this.addOneRowFlag) {
            this.destroy = false
            setTimeout(() => {
              this.destroy = true
            }, 0.167)
            this.addOneRowFlag = false
            sessionStorage.removeItem('sourceFilesFormObject')
          }
        }
        let _orgIds = this.formObject.orgList.map((item) => item.orgId)
        //工厂
        this.$API.sourceFiles.queryUnionSetSite({ organizationIdList: _orgIds }).then((res) => {
          let _data = res.data
          _data.map((item) => {
            item.siteName = item.siteCode + '-' + item.siteName
          })
          _this.siteList = _data
          if (_this.queryId) {
            _this.formObject.siteId = _this.editFormObject.siteId
            _this.formObject.siteName =
              _this.editFormObject.siteCode + '-' + _this.editFormObject.siteName
            _this.formObject.siteCode = _this.editFormObject.siteCode
          }
        })
        // 供应商
        let obj = {
          organizationCodeList: this.formObject.orgCodes
        }
        this.$API.sourceFiles
          .queryIntersectionSupplier(obj)
          .then((res) => {
            let _data = res.data
            _data.map((item) => {
              item.supplierName = item.supplierCode + '-' + item.supplierName
            })
            _this.supplierList = _data
            if (_this.queryId) {
              _this.formObject.supplierId = _this.editFormObject.supplierId
              _this.formObject.supplierCode = _this.editFormObject.supplierCode
              _this.formObject.supplierName =
                _this.editFormObject.supplierCode + '-' + _this.editFormObject.supplierName
            }
          })
          .catch((err) => {
            this.$toast({
              content: err.msg,
              type: 'error'
            })
          })
      }
      if (item === 'supplierCodes') {
        this.organizationList = []
        this.formObject.organizationList = []
        this.formObject.organizationCodes = []
        this.categoryList = []
        this.formObject.categoryList = []
        this.formObject.categoryCodes = []
        if (e.value.length > 0) {
          this.formObject.supplierList.length = 0
          this.formObject.supplierCodes = e.value
          this.supplierList.forEach((item) => {
            if (e.value.indexOf(item.supplierCode) != -1) {
              this.formObject.supplierList.push({
                supplierCode: item.supplierCode,
                supplierName: item.supplierName,
                partnerCode: item.partnerCode
              })
            }
          })
        }
        if (e.e != null) {
          let _dataSource = this.$refs.templateRef
            .getCurrentUsefulRef()
            .gridRef.ejsRef.getCurrentViewRecords()
          if (_dataSource.length > 0 || this.addOneRowFlag) {
            this.destroy = false
            setTimeout(() => {
              this.destroy = true
            }, 0.167)
            this.addOneRowFlag = false
            sessionStorage.removeItem('sourceFilesFormObject')
          }
        }
        //品类
        this.$API.sourceFiles
          .queryIntersectionCategory({
            organizationCodeList: this.formObject.orgCodes,
            supplierCodeList: this.formObject.supplierCodes
          })
          .then((res) => {
            let _data = res.data
            _data.map((item) => {
              item.categoryName = item.categoryCode + '-' + item.categoryName
            })
            _this.categoryList = _data
            if (_this.queryId) {
              _this.formObject.categoryId = _this.editFormObject.categoryId
              _this.formObject.categoryCode = _this.editFormObject.categoryCode
              _this.formObject.categoryName =
                _this.editFormObject.categoryCode + '-' + _this.editFormObject.categoryName
            }
          })
        //采购组织
        let _partnerCodeList = this.formObject.supplierList.map((item) => item.partnerCode)
        this.$API.sourceFiles
          .queryIntersectionBusiness({
            organizationCodeList: this.formObject.orgCodes,
            partnerCodeList: _partnerCodeList
          })
          .then((res) => {
            let _data = res.data
            _data.map((item) => {
              item.organizationName = item.organizationCode + '-' + item.organizationName
            })
            _this.organizationList = _data
            if (_this.queryId) {
              _this.formObject.purchaseOrganizationId = _this.editFormObject.purchaseOrganizationId
              _this.formObject.purchaseOrganizatioName =
                _this.editFormObject.purchaseOrganizationCode +
                '-' +
                _this.editFormObject.purchaseOrganizatioName
              _this.formObject.purchaseOrganizationCode =
                _this.editFormObject.purchaseOrganizationCode
            }
          })
      }
      if (item === 'organizationCodes') {
        if (e.value.length > 0) {
          this.formObject.organizationList.length = 0
          this.formObject.organizationCodes = e.value
          this.organizationList.forEach((item) => {
            if (e.value.indexOf(item.organizationCode) != -1) {
              this.formObject.organizationList.push({
                organizationCode: item.organizationCode,
                organizationName: item.organizationName,
                organizationId: item.id
              })
            }
          })
        }
        if (e.e != null) {
          let _dataSource = this.$refs.templateRef
            .getCurrentUsefulRef()
            .gridRef.ejsRef.getCurrentViewRecords()
          if (_dataSource.length > 0 || this.addOneRowFlag) {
            this.destroy = false
            setTimeout(() => {
              this.destroy = true
            }, 0.167)
            this.addOneRowFlag = false
            sessionStorage.removeItem('sourceFilesFormObject')
          }
        }
      }
      if (item === 'siteCodes') {
        if (e.value.length > 0) {
          this.formObject.siteList.length = 0
          this.formObject.siteCodes = e.value
          this.siteList.forEach((item) => {
            if (e.value.indexOf(item.siteCode) != -1) {
              this.formObject.siteList.push({
                siteCode: item.siteCode,
                siteName: item.siteName,
                siteId: item.id
              })
            }
          })
        }
        if (e.e != null) {
          let _dataSource = this.$refs.templateRef
            .getCurrentUsefulRef()
            .gridRef.ejsRef.getCurrentViewRecords()
          if (_dataSource.length > 0 || this.addOneRowFlag) {
            this.destroy = false
            setTimeout(() => {
              this.destroy = true
            }, 0.167)
            this.addOneRowFlag = false
            sessionStorage.removeItem('sourceFilesFormObject')
          }
        }
      }
      if (item === 'categoryCodes') {
        if (e.value.length > 0) {
          this.formObject.categoryList.length = 0
          this.formObject.categoryCodes = e.value
          this.categoryList.forEach((item) => {
            if (e.value.indexOf(item.categoryCode) != -1) {
              this.formObject.categoryList.push({
                categoryCode: item.categoryCode,
                categoryName: item.categoryName,
                categoryId: item.id
              })
            }
          })
        }
        if (e.e != null) {
          let _dataSource = this.$refs.templateRef
            .getCurrentUsefulRef()
            .gridRef.ejsRef.getCurrentViewRecords()
          if (_dataSource.length > 0 || this.addOneRowFlag) {
            this.destroy = false
            setTimeout(() => {
              this.destroy = true
            }, 0.167)
            this.addOneRowFlag = false
            sessionStorage.removeItem('sourceFilesFormObject')
          }
        }
      }
    },
    handleClickToolBar(e) {
      if (e.toolbar.id === 'Add') {
        this.handleClickToolBarAdd()
      }
      if (e.toolbar.id === 'Delete') {
        // console.log('delete--', e.grid.getSelectedRecords())
        let _list = e.grid.getSelectedRecords()
        if (_list.length < 1) return
        this.handleClickToolBarDelete()
      }
    },
    handleClickToolBarAdd() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          if (this.addOneRowFlag) {
            this.$toast({ content: this.$t('行信息未填写完整,不能新增'), type: 'warning' })
            return
          }
          sessionStorage.setItem('sourceFilesFormObject', JSON.stringify(this.formObject))
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
        }
      })
    },
    handleClickToolBarDelete() {
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord()
    },
    actionBegin(args) {
      let { requestType } = args
      this.addOneRowFlag = false
      if (requestType == 'add') {
        args.data.addId = this.addId++ // 新增时是addId，后台获取过来的数据是id
        this.addOneRowFlag = true
      }
      if (requestType == 'save') {
        if (args.data.startTime) {
          args.data.startTime = formatDate(new Date(args.data.startTime), 'yyyy-MM-dd')
        }
        if (args.data.endTime) {
          args.data.endTime = formatDate(new Date(args.data.endTime), 'yyyy-MM-dd')
        }
        if (Object.values(this.supplyGoods).length > 0) {
          let _supplyGoods = cloneDeep(this.supplyGoods)
          args.data = {
            ...args.data,
            ..._supplyGoods
          }
          for (let key in this.supplyGoods) {
            delete this.supplyGoods[key]
          }
        }
      }
      console.log(this.supplyGoods)
    },
    actionComplete(args) {
      const { rowIndex } = args
      if (args.requestType == 'save') {
        if (!args.data.itemName) {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          this.$toast({ content: this.$t('物料不能为空'), type: 'warning' })
          return
        }
        if (!args.data.sourceType) {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          this.$toast({ content: this.$t('货源类型不能为空'), type: 'warning' })
          return
        }
        if (!args.data.startTime) {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          this.$toast({ content: this.$t('开始日期不能为空'), type: 'warning' })
          return
        }
        if (!args.data.endTime) {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          this.$toast({ content: this.$t('结束日期不能为空'), type: 'warning' })
          return
        }
        if (
          args.data.isDump === '' ||
          args.data.isDump === null ||
          args.data.isDump === undefined
        ) {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          this.$toast({ content: this.$t('是否转储类货源不能为空'), type: 'warning' })
          return
        }
        if (!args.data.mrp) {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          this.$toast({ content: this.$t('MRP不能为空'), type: 'warning' })
          return
        }
        if (Number(new Date(args.data.startTime)) > Number(new Date(args.data.endTime))) {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          this.$toast({ content: this.$t('开始日期不能大于结束日期'), type: 'warning' })
          return
        }
        // this.handleAddRow()
        // this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.refresh()
        // this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
      }
    },
    handleAddRow() {
      //获取编辑或者新增的数据
      let currentRecords = this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.dataSource
      let row = cloneDeep(this.supplyGoods)
      currentRecords.some((item) => {
        if (item.addId == this.editAddId) {
          Object.assign(item, row)
        }
      })
    },
    selectedChanged(val) {
      Object.assign(this.supplyGoods, val.itemInfo || {})
    },
    clickButtonSave(val) {
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      console.log(this.formObject)
      setTimeout(() => {
        this.$refs.dialogRef.validate((valid) => {
          if (valid) {
            let _dataSource = cloneDeep(
              this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.getCurrentViewRecords()
            )
            _dataSource.map((item) => {
              item.isDump = item.isDump == 0 ? false : true
            })
            let params = {
              categoryCodeList: this.formObject.categoryCodes,
              organizationCodeList: this.formObject.orgCodes,
              purchaseOrganizationCodeList: this.formObject.organizationCodes,
              siteCodeList: this.formObject.siteCodes,
              supplierCodeList: this.formObject.supplierCodes,
              itemAddNewRequestList: _dataSource
            }
            if (!this.editFormObject.id && val == '0') {
              this.$API.sourceFiles
                .multiAdd(params)
                .then(() => {
                  this.$router.push({
                    path: '/masterdata/source-files'
                  })
                  this.$toast({
                    content: this.$t('保存成功'),
                    type: 'success'
                  })
                })
                .catch((err) => {
                  this.$hloading()
                  this.$toast({
                    content: err.msg,
                    type: 'error'
                  })
                })
            } else if (!this.editFormObject.id && val == '1') {
              this.$API.sourceFiles
                .multiAddAndSync(params)
                .then(() => {
                  this.$router.push({
                    path: '/masterdata/source-files'
                  })
                  this.$toast({
                    content: this.$t('保存并同步发送成功'),
                    type: 'success'
                  })
                })
                .catch((err) => {
                  this.$hloading()
                  this.$toast({
                    content: err.msg,
                    type: 'error'
                  })
                })
            } else if (this.editFormObject.id && val == '0') {
              let _updateList = [
                {
                  ...this.formObject,
                  ..._dataSource[0],
                  id: this.editFormObject.id
                }
              ]
              delete _updateList[0].orgCodes,
                delete _updateList[0].orgList,
                delete _updateList[0].supplierCodes,
                delete _updateList[0].supplierList,
                delete _updateList[0].organizationCodes,
                delete _updateList[0].organizationList,
                delete _updateList[0].categoryCodes,
                delete _updateList[0].categoryList,
                delete _updateList[0].siteCodes,
                delete _updateList[0].siteList
              let obj = {
                updateList: _updateList
              }
              this.$API.sourceFiles
                .batchUpdate(obj)
                .then(() => {
                  this.$router.push({
                    path: '/masterdata/source-files'
                  })
                  this.$toast({
                    content: this.$t('保存成功'),
                    type: 'success'
                  })
                })
                .catch((err) => {
                  this.$hloading()
                  this.$toast({
                    content: err.msg,
                    type: 'error'
                  })
                })
            } else if (this.editFormObject.id && val == '1') {
              let _updateList = [
                {
                  ...this.formObject,
                  ..._dataSource[0],
                  id: this.editFormObject.id
                }
              ]
              delete _updateList[0].orgCodes,
                delete _updateList[0].orgList,
                delete _updateList[0].supplierCodes,
                delete _updateList[0].supplierList,
                delete _updateList[0].organizationCodes,
                delete _updateList[0].organizationList,
                delete _updateList[0].categoryCodes,
                delete _updateList[0].categoryList,
                delete _updateList[0].siteCodes,
                delete _updateList[0].siteList
              let obj = {
                updateList: _updateList
              }
              this.$API.sourceFiles
                .batchUpdateAndSync(obj)
                .then(() => {
                  this.$router.push({
                    path: '/masterdata/source-files'
                  })
                  this.$toast({
                    content: this.$t('保存成功'),
                    type: 'success'
                  })
                })
                .catch((err) => {
                  this.$hloading()
                  this.$toast({
                    content: err.msg,
                    type: 'error'
                  })
                })
            }
          }
        })
      }, 100)
    },
    handleClickCellTitle() {},
    // 模糊搜索，不区分大小写模糊搜索
    filteringResource(e, dataSource, key) {
      if (typeof e.text === 'string' && e.text) {
        e.updateData(
          dataSource?.filter((f) => f[key]?.toUpperCase().includes(e?.text?.toUpperCase()))
        )
      } else {
        e.updateData(dataSource)
      }
    }
  }
}
</script>
<style lang="scss">
.inventory {
  height: 100vh;

  .top-info {
    width: 100%;
    padding: 20px;
    box-sizing: border-box;
    background-color: #fff;

    //按钮
    .detail-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .name-wrap {
      }
      .btns-wrap {
        .detail-header-button {
          .e-flat {
            color: rgba(0, 70, 156, 1);
          }
        }
      }
    }
    //表单
    .formInput {
      width: 100%;
      .mt-form {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        .mt-form-item {
          width: 400px;
        }
        .mt-form-item:nth-last-child(1) {
          width: 405px;
          .mt-input {
            display: inline-block;
            width: 320px;
          }
          /deep/.e-btn {
            padding: 6px 8px 4px;
            margin-left: 10px;
          }
        }
        &::after {
          content: '';
          width: 400px;
        }
      }
      .mt-form:nth-of-type(2) {
        width: 100%;
        .mt-form-item {
          width: 100%;
          .mt-input {
            width: 100%;
          }
        }
      }
      .applyOrganization {
        position: relative;
        .mt-input:hover + .hover {
          display: block;
        }
      }
      .hover {
        display: none;
        position: absolute;
        top: -110px;
        left: 0;
        z-index: 1;
        width: 400px;
        height: 100px;
        background: #fff;
        border: 1px solid #fee;
        border-radius: 5px;
      }
    }
  }
}
</style>
