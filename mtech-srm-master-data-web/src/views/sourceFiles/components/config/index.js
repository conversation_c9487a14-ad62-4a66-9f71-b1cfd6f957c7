import { i18n } from '@/main.js'
import Select from '../pages/Select.vue'
import dataSelect from '../pages/dataSelect.vue'
import input from '../pages/input.vue'
import numberInput from '../pages/numberInput.vue'
import Vue from 'vue'
export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'addId',
    headerText: 'addId',
    width: '0',
    visible: false,
    allowEditing: false
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('物料名称')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    editTemplate: () => {
      return { template: input }
    }
  },
  {
    field: 'sourceType', //字段待定
    headerText: i18n.t('货源类型'),
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('货源类型')}}</span>
              </div>
            `
        })
      }
    },
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('临时货源'), 2: i18n.t('一次性货源'), 3: i18n.t('正式货源') }
    },
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    width: 150,
    field: 'startTime',
    headerText: i18n.t('有效期从'),
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('有效期从')}}</span>
              </div>
            `
        })
      }
    },
    valueConverter: {
      type: 'date'
    },
    editTemplate: () => {
      return { template: dataSelect }
    }
  },
  {
    width: 150,
    field: 'endTime',
    headerText: i18n.t('有效期至'),
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('有效期至')}}</span>
              </div>
            `
        })
      }
    },
    valueConverter: {
      type: 'date'
    },
    editTemplate: () => {
      return { template: dataSelect }
    }
  },
  {
    width: 120,
    field: 'limitTradeTotal',
    headerText: i18n.t('限制交易数量'),
    editTemplate: () => {
      return { template: numberInput }
    }
  },
  {
    width: 120,
    field: 'limitTradeTimes',
    headerText: i18n.t('限制交易次数'),
    editTemplate: () => {
      return { template: numberInput }
    }
  },
  {
    field: 'mrp',
    headerText: i18n.t('MRP'),
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('MRP')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: () => {
      return { template: Select }
    },
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('与MRP有关的记录'),
        2: i18n.t('与MRP计划表自动生成的计划行有关的记录')
        // 与MRP有关的记录: i18n.t('与MRP有关的记录'),
        // 与MRP计划表自动生成的计划行有关的记录: i18n.t('与MRP计划表自动生成的计划行有关的记录'),
      }
    }
  },
  {
    width: 140,
    field: 'isDump',
    headerText: i18n.t('是否转储类货源'),
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('是否转储类货源')}}</span>
              </div>
            `
        })
      }
    },
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('是'), 0: i18n.t('否') }
    },
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    field: 'dumpSiteName',
    headerText: i18n.t('采购工厂名称'),
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    field: 'dumpSiteCode',
    headerText: i18n.t('采购工厂编码'),
    editTemplate: () => {
      return { template: input }
    }
  }

  // {
  //   field: 'statusId',
  //   headerText: i18n.t('货源状态'),
  //   valueConverter: {
  //     type: 'map',
  //     map: { 1: i18n.t('激活'), 0: i18n.t('失效') },
  //   },
  //   editTemplate: () => {
  //     return { template: Select }
  //   },
  // },
]
