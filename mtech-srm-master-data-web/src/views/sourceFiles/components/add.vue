<template>
  <div class="inventory">
    <!-- 头部 -->
    <div class="top-info">
      <!-- 顶部信息 -->
      <div class="detail-info">
        <div class="name-wrap"></div>
        <div class="btns-wrap">
          <mt-button
            css-class="e-flat"
            class="detail-header-button"
            @click="$router.push('/masterdata/source-files')"
            >{{ $t('返回') }}</mt-button
          >
          <mt-button
            css-class="e-flat"
            class="detail-header-button"
            @click="clickButtonSave('0')"
            >{{ $t('保存') }}</mt-button
          >
          <mt-button
            css-class="e-flat"
            class="detail-header-button"
            @click="clickButtonSave('1')"
            >{{ $t('保存并同步') }}</mt-button
          >
        </div>
      </div>
      <div class="formInput">
        <mt-form ref="dialogRef" :model="formObject" :rules="rules">
          <mt-form-item prop="organizationId" :label="$t('公司')">
            <mt-select
              v-model="formObject.organizationId"
              float-label-type="Never"
              :disabled="isEdit"
              :data-source="orgList"
              :show-clear-button="true"
              :fields="{ text: 'orgName', value: 'id' }"
              @change="change($event, 'organizationId')"
              @select="select($event, 'organizationId')"
              :placeholder="$t('请选择公司')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="supplierId" :label="$t('供应商')">
            <mt-select
              v-model="formObject.supplierId"
              float-label-type="Never"
              :disabled="isEdit"
              :data-source="supplierList"
              :allow-filtering="true"
              :show-clear-button="true"
              :fields="{ text: 'supplierName', value: 'id' }"
              :filtering="(e) => filteringResource(e, supplierList, 'supplierName')"
              @change="change($event, 'supplierName')"
              :placeholder="$t('请选择供应商')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="purchaseOrganizationId" :label="$t('采购组织')">
            <mt-select
              v-model="formObject.purchaseOrganizationId"
              float-label-type="Never"
              :data-source="orgCodeLists"
              :allow-filtering="true"
              :show-clear-button="true"
              :fields="{ text: 'organizationName', value: 'id' }"
              @change="change($event, 'purchaseOrganizationName')"
              :filtering="(e) => filteringResource(e, orgCodeLists, 'organizationName')"
              :placeholder="$t('请选择采购组织')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item v-if="queryId" prop="siteCode" :label="$t('工厂')">
            <mt-select
              v-model="formObject.siteCode"
              float-label-type="Never"
              :disabled="isEdit"
              :data-source="siteList"
              :fields="{ text: 'siteName', value: 'siteCode' }"
              :allow-filtering="true"
              :filtering="(e) => filteringResource(e, siteList, 'siteName')"
              @change="change($event, 'siteName')"
              @select="select($event, 'siteName')"
              :placeholder="$t('请选择工厂')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item v-else prop="siteOrgCodes" :label="$t('工厂')">
            <mt-multi-select
              v-model="formObject.siteOrgCodes"
              float-label-type="Never"
              :disabled="isEdit"
              :data-source="siteList"
              :show-clear-button="true"
              :allow-filtering="true"
              :fields="{ text: 'siteName', value: 'siteCode' }"
              :filtering="(e) => filteringResource(e, siteList, 'siteName')"
              @change="change($event, 'siteOrgCodes')"
              @select="select($event, 'siteOrgCodes')"
              :placeholder="$t('请选择工厂')"
            ></mt-multi-select>
          </mt-form-item>
          <!-- <mt-form-item prop="supplierId" :label="$t('供应商')">
            <mt-select
              v-model="formObject.supplierId"
              float-label-type="Never"
              :disabled="isEdit"
              :data-source="supplierList"
              :allow-filtering="true"
              :fields="{ text: 'supplierName', value: 'id' }"
              @change="change($event, 'supplierName')"
              :placeholder="$t('请选择供应商')"
            ></mt-select>
          </mt-form-item> -->
          <mt-form-item prop="categoryId" :label="$t('品类')">
            <mt-select
              v-model="formObject.categoryId"
              float-label-type="Never"
              :disabled="isEdit"
              :data-source="categoryList"
              :allow-filtering="true"
              :show-clear-button="true"
              :fields="{ text: 'categoryName', value: 'id' }"
              :filtering="(e) => filteringResource(e, categoryList, 'categoryName')"
              @change="change($event, 'categoryName')"
              @select="select($event, 'categoryName')"
              :placeholder="$t('请选择品类')"
            ></mt-select>
          </mt-form-item>
        </mt-form>
      </div>
    </div>
    <mt-template-page
      v-if="destroy"
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @selectedChanged="selectedChanged"
      @actionComplete="actionComplete"
      @actionBegin="actionBegin"
    ></mt-template-page>
  </div>
</template>
<script>
import { cloneDeep } from 'lodash'
// import { utils } from '@mtech-common/utils'
import { columnData } from './config/index'
import { formatDate } from '@/utils/util'
export default {
  data() {
    return {
      detailsObj: null,
      isEdit: false,
      destroy: true,
      addOneRowFlag: false,
      formObject: {
        organizationId: '',
        purchaseOrganizationId: '',
        // siteId: '',
        supplierId: '',
        categoryId: ''
      },
      editFormObject: {},
      rules: {
        organizationId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        purchaseOrganizationId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        siteOrgCodes: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        supplierId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        categoryId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      orgList: [], //公司
      orgCodeLists: [], //采购组织
      siteList: [], //工厂
      supplierList: [], //供应商
      categoryList: [], //品类
      historyValue: {},
      pageConfig: [
        {
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [['Add', 'Delete'], []]
          },
          useToolTemplate: false,
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowEditing: true, //开启表格编辑操作
            columnData: columnData,
            // frozenColumns: 1,
            dataSource: []
          }
        }
      ],
      editDataSource: [],
      addId: 1,
      editAddId: '',
      supplyGoods: {}
    }
  },
  computed: {
    queryId() {
      return this.$route.query.id
    }
  },
  async mounted() {
    if (this.queryId) {
      this.isEdit = true
      delete this.rules.siteOrgCodes
      ;(this.rules.siteOrgCodes = [
        { required: true, message: this.$t('请选择'), trigger: 'blur' }
      ]),
        this.$set(this.pageConfig[0], 'toolbar', {
          useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
          tools: [[], []]
        })
      ;(this.formObject.siteId = ''), this.editRowData()
    } else {
      this.companyOrganization()
    }
  },
  methods: {
    async editRowData() {
      await this.$API.sourceFiles.queryDetail({ id: this.queryId }).then((res) => {
        this.editFormObject = { ...res.data }
        sessionStorage.setItem('sourceFilesFormObject', JSON.stringify(this.editFormObject))
        this.editDataSource = [
          {
            addId: 1,
            itemId: this.editFormObject.itemId,
            itemName: this.editFormObject.itemName,
            itemCode: this.editFormObject.itemCode,
            sourceType: this.editFormObject.sourceType,
            startTime: this.editFormObject.startTime
              ? formatDate(new Date(this.editFormObject.startTime), 'yyyy-MM-dd')
              : formatDate(new Date(), 'yyyy-MM-dd'),
            endTime:
              !this.editFormObject.endTime || !this.editFormObject.startTime
                ? formatDate(new Date(), 'yyyy-MM-dd')
                : formatDate(new Date(this.editFormObject.endTime), 'yyyy-MM-dd'),
            limitTradeTotal: this.editFormObject.limitTradeTotal,
            limitTradeTimes: this.editFormObject.limitTradeTimes,
            isDump: this.editFormObject.isDumpDesc == this.$t('否') ? 0 : 1,
            dumpSiteName: this.editFormObject.dumpSiteName,
            mrp: this.editFormObject.mrp,
            statusId: this.editFormObject.statusId,
            dumpSiteOrgId: this.editFormObject.dumpSiteOrgId,
            dumpSiteCode: this.editFormObject.dumpSiteCode,
            mrpId: this.editFormObject.mrpId,
            mrpCode: this.editFormObject.mrpCode
          }
        ]
        setTimeout(() => {
          this.$set(this.pageConfig[0].grid, 'dataSource', this.editDataSource)
        }, 0.167)
      })
      await this.companyOrganization()
    },
    async companyOrganization() {
      this.$loading()
      await this.$API.sourceFiles
        .company({})
        .then((res) => {
          this.orgList = cloneDeep(res.data)

          if (this.queryId) {
            setTimeout(() => {
              this.formObject.organizationId = this.editFormObject.organizationId
              this.formObject.organizationCode = this.editFormObject.organizationCode
              this.formObject.organizationName = this.editFormObject.organizationName
            }, 0.17)
          }
          this.$hloading()
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
      // await this.$API.sourceLists['getChildrenCompanyOrganization2']({
      //   organizationLevelCodes: ['ORG02', 'ORG01'],
      //   orgType: 'ORG001PRO',
      //   includeItself: true,
      // })
      //   .then((result) => {
      //     this.$hloading()
      //     if (result.data) {
      //       this.orgList = result.data?.filter((item) => {
      //         return item.orgLevelTypeCode === 'ORG02' || item.orgLevelTypeCode === 'ORG01'
      //       })
      //       if (this.queryId) {
      //         // alert('1')
      //         console.log(this.editFormObject)
      //         setTimeout(() => {
      //           this.formObject.organizationId = this.editFormObject.organizationId
      //           this.formObject.organizationCode = this.editFormObject.organizationCode
      //           this.formObject.organizationName = this.editFormObject.organizationName
      //         }, 0.17)
      //       }
      //     } else {
      //       this.orgList = []
      //     }
      //   })
      //   .catch((err) => {
      //     this.$hloading()
      //     this.$toast({
      //       content: err.msg,
      //       type: 'error',
      //     })
      //   })
    },
    change(e, item) {
      let { itemData } = e
      let _this = this
      if (item === 'organizationId') {
        for (const key in this.formObject) {
          this.formObject[key] = ''
        }
        this.categoryList = []
        this.supplierList = []
        this.formObject.organizationName = itemData.orgName
        this.formObject.organizationCode = itemData.orgCode
        this.formObject.organizationId = itemData.id
        this.formObject.orgLevelTypeCode = itemData.orgLevelTypeCode
        if (e.e != null) {
          let _dataSource = this.$refs.templateRef
            .getCurrentUsefulRef()
            .gridRef.ejsRef.getCurrentViewRecords()
          if (_dataSource.length > 0 || this.addOneRowFlag) {
            this.destroy = false
            setTimeout(() => {
              this.destroy = true
            }, 0.167)
            this.addOneRowFlag = false
            sessionStorage.removeItem('sourceFilesFormObject')
          }
        }
        let params = { organizationId: this.formObject.organizationId }
        //采购组织
        this.$API.sourceFiles.queryBusinessOrg(params).then((res) => {
          let _data = res.data
          _data.map((item) => {
            item.organizationName = item.organizationCode + '-' + item.organizationName
          })
          _this.orgCodeLists = _data
          if (_this.queryId) {
            _this.formObject.purchaseOrganizationId = _this.editFormObject.purchaseOrganizationId
            _this.formObject.purchaseOrganizatioName =
              _this.editFormObject.purchaseOrganizationCode +
              '-' +
              _this.editFormObject.purchaseOrganizatioName
            _this.formObject.purchaseOrganizationCode =
              _this.editFormObject.purchaseOrganizationCode
          }
        })
        //工厂
        this.$API.Inventory.getSiteInfo(params).then((res) => {
          let _data = res.data
          _data.map((item) => {
            item.siteName = item.siteCode + '-' + item.siteName
          })
          _this.siteList = _data
          if (_this.queryId) {
            _this.formObject.siteId = _this.editFormObject.siteId
            _this.formObject.siteName =
              _this.editFormObject.siteCode + '-' + _this.editFormObject.siteName
            _this.formObject.siteCode = _this.editFormObject.siteCode
          }
        })
        //供应商
        let obj = {
          page: {
            current: 1,
            size: 9999
          },
          condition: 'and',
          rules: [
            {
              label: this.$t('公司代码'),
              field: 'organizationCode',
              type: 'string',
              operator: 'contains',
              value: this.formObject.organizationCode
            }
          ]
        }
        this.$API.sourceFiles
          .querySupplier(obj)
          .then((res) => {
            let _data = res.data.records
            _data.map((item) => {
              item.supplierName = item.supplierCode + '-' + item.supplierName
            })
            _this.supplierList = _data
            if (_this.queryId) {
              _this.formObject.supplierId = _this.editFormObject.supplierId
              _this.formObject.supplierCode = _this.editFormObject.supplierCode
              _this.formObject.supplierName =
                _this.editFormObject.supplierCode + '-' + _this.editFormObject.supplierName
            }
          })
          .catch((err) => {
            this.$toast({
              content: err.msg,
              type: 'error'
            })
          })
      }
      if (item === 'purchaseOrganizationName') {
        this.formObject.purchaseOrganizationName = itemData.organizationName
        this.formObject.purchaseOrganizationCode = itemData.organizationCode
        this.formObject.purchaseOrganizationId = itemData.id
      }
      if (item === 'siteOrgCodes') {
        console.log(e)
        if (e.e != null) {
          let _dataSource = this.$refs.templateRef
            .getCurrentUsefulRef()
            .gridRef.ejsRef.getCurrentViewRecords()
          if (_dataSource.length > 0 || this.addOneRowFlag) {
            this.destroy = false
            setTimeout(() => {
              this.destroy = true
            }, 0.167)
            this.addOneRowFlag = false
            sessionStorage.removeItem('sourceFilesFormObject')
          }
        }
        this.formObject.siteOrgCodes = e.value
        // this.formObject.siteCode = itemData.siteCode
        // this.formObject.siteId = itemData.id
      }

      if (item === 'siteName') {
        this.formObject.siteName = itemData.siteName
        this.formObject.siteCode = itemData.siteCode
        this.formObject.siteId = itemData.organizationId
        if (e.e != null) {
          let _dataSource = this.$refs.templateRef
            .getCurrentUsefulRef()
            .gridRef.ejsRef.getCurrentViewRecords()
          if (_dataSource.length > 0 || this.addOneRowFlag) {
            this.destroy = false
            setTimeout(() => {
              this.destroy = true
            }, 0.167)
            this.addOneRowFlag = false
            sessionStorage.removeItem('sourceFilesFormObject')
          }
        }
      }
      if (item === 'supplierName') {
        // alert('4')
        this.formObject.supplierName = itemData.supplierName
        this.formObject.supplierCode = itemData.supplierCode
        this.formObject.supplierId = itemData.id
        this.categoryList = []
        this.formObject.categoryId = ''
        this.formObject.categoryCode = ''
        this.formObject.categoryName = ''
        if (e.e != null) {
          let _dataSource = this.$refs.templateRef
            .getCurrentUsefulRef()
            .gridRef.ejsRef.getCurrentViewRecords()
          if (_dataSource.length > 0 || this.addOneRowFlag) {
            this.destroy = false
            setTimeout(() => {
              this.destroy = true
            }, 0.167)
            this.addOneRowFlag = false
            sessionStorage.removeItem('sourceFilesFormObject')
          }
        }
        //品类
        this.$API.sourceFiles
          .queryCategorys({
            organizationCode: this.formObject.organizationCode,
            supplierId: this.formObject.supplierId
          })
          .then((res) => {
            let _data = res.data
            _data.map((item) => {
              item.categoryName = item.categoryCode + '-' + item.categoryName
            })
            this.categoryList = _data
            // alert('2')
            if (this.queryId) {
              // alert('3')
              this.formObject.categoryId = this.editFormObject.categoryId
              this.formObject.categoryCode = this.editFormObject.categoryCode
              this.formObject.categoryName =
                this.editFormObject.categoryCode + '-' + this.editFormObject.categoryName
            }
          })
      }
      if (item === 'categoryName') {
        this.formObject.categoryName = itemData.categoryName
        this.formObject.categoryCode = itemData.categoryCode
        this.formObject.categoryId = itemData.id
        if (e.e != null) {
          let _dataSource = this.$refs.templateRef
            .getCurrentUsefulRef()
            .gridRef.ejsRef.getCurrentViewRecords()
          if (_dataSource.length > 0 || this.addOneRowFlag) {
            this.destroy = false
            setTimeout(() => {
              this.destroy = true
            }, 0.167)
            this.addOneRowFlag = false
            sessionStorage.removeItem('sourceFilesFormObject')
          }
        }
      }
    },
    select() {
      // let _dataSource = this.$refs.templateRef
      //   .getCurrentUsefulRef()
      //   .gridRef.ejsRef.getCurrentViewRecords()
      // if (_dataSource.length == 0 && this.addOneRowFlag) {
      //   Object.assign(this.historyValue, this.formObject)
      //   this.$dialog({
      //     data: {
      //       title: this.$t('提示'),
      //       message: this.$t('切换会导致已编辑数据丢失是否继续?'),
      //     },
      //     success: () => {
      //       this.historyValue = {}
      //       this.destroy = false
      //       setTimeout(() => {
      //         this.destroy = true
      //       }, 0.167)
      //       this.addOneRowFlag = false
      //       sessionStorage.setItem('sourceFilesFormObject', JSON.stringify(this.formObject))
      //     },
      //     close: () => {
      //       this.formObject = {
      //         ...this.historyValue,
      //       }
      //     },
      //   })
      //   return
      // }
      // if (_dataSource.length == 0) return
      // if (item === 'organizationId') {
      //   Object.assign(this.historyValue, cloneDeep(this.formObject))
      //   this.$dialog({
      //     data: {
      //       title: this.$t('提示'),
      //       message: this.$t('切换会导致已编辑数据丢失是否继续?'),
      //     },
      //     success: () => {
      //       this.historyValue = {}
      //       this.destroy = false
      //       setTimeout(() => {
      //         this.destroy = true
      //       }, 0.167)
      //       sessionStorage.removeItem('sourceFilesFormObject')
      //     },
      //     close: () => {
      //       debugger
      //       this.formObject = {
      //         ...this.historyValue,
      //       }
      //     },
      //   })
      // }
      // if (item === 'siteOrgCodes') {
      //   Object.assign(this.historyValue, this.formObject)
      //   this.$dialog({
      //     data: {
      //       title: this.$t('提示'),
      //       message: this.$t('切换会导致已编辑数据丢失是否继续?'),
      //     },
      //     success: () => {
      //       this.historyValue = {}
      //       this.destroy = false
      //       setTimeout(() => {
      //         this.destroy = true
      //       }, 0.167)
      //       sessionStorage.removeItem('sourceFilesFormObject')
      //     },
      //     close: () => {
      //       this.formObject = {
      //         ...this.historyValue,
      //       }
      //     },
      //   })
      // }
      // if (item === 'categoryName') {
      //   Object.assign(this.historyValue, this.formObject)
      //   this.$dialog({
      //     data: {
      //       title: this.$t('提示'),
      //       message: this.$t('切换会导致已编辑数据丢失是否继续?'),
      //     },
      //     success: () => {
      //       this.historyValue = {}
      //       this.destroy = false
      //       setTimeout(() => {
      //         this.destroy = true
      //       }, 0.167)
      //       sessionStorage.removeItem('sourceFilesFormObject')
      //     },
      //     close: () => {
      //       this.formObject = {
      //         ...this.historyValue,
      //       }
      //     },
      //   })
      // }
    },
    handleClickToolBar(e) {
      if (e.toolbar.id === 'Add') {
        this.handleClickToolBarAdd()
      }
      if (e.toolbar.id === 'Delete') {
        // console.log('delete--', e.grid.getSelectedRecords())
        let _list = e.grid.getSelectedRecords()
        if (_list.length < 1) return
        this.handleClickToolBarDelete()
      }
    },
    handleClickToolBarAdd() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          console.log(this.formObject)
          sessionStorage.setItem('sourceFilesFormObject', JSON.stringify(this.formObject))
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
        }
      })
    },
    handleClickToolBarDelete() {
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord()
    },
    actionBegin(args) {
      let { requestType } = args
      this.addOneRowFlag = false
      if (requestType == 'add') {
        args.data.addId = this.addId++ // 新增时是addId，后台获取过来的数据是id
        this.addOneRowFlag = true
      }
      if (requestType == 'save') {
        if (args.data.startTime) {
          args.data.startTime = formatDate(new Date(args.data.startTime), 'yyyy-MM-dd')
        }
        if (args.data.endTime) {
          args.data.endTime = formatDate(new Date(args.data.endTime), 'yyyy-MM-dd')
        }
        if (Object.values(this.supplyGoods).length > 0) {
          let _supplyGoods = cloneDeep(this.supplyGoods)
          args.data = {
            ...args.data,
            ..._supplyGoods
          }
          for (let key in this.supplyGoods) {
            delete this.supplyGoods[key]
          }
        }
      }
      console.log(this.supplyGoods)
    },
    actionComplete(args) {
      const { rowIndex } = args
      if (args.requestType == 'save') {
        if (!args.data.itemName) {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          this.$toast({ content: this.$t('物料不能为空'), type: 'warning' })
          return
        }
        if (!args.data.sourceType) {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          this.$toast({ content: this.$t('货源类型不能为空'), type: 'warning' })
          return
        }
        if (!args.data.startTime) {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          this.$toast({ content: this.$t('开始日期不能为空'), type: 'warning' })
          return
        }
        if (!args.data.endTime) {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          this.$toast({ content: this.$t('结束日期不能为空'), type: 'warning' })
          return
        }
        console.log(args.data.isDump)
        if (
          args.data.isDump === '' ||
          args.data.isDump === null ||
          args.data.isDump === undefined
        ) {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          this.$toast({ content: this.$t('是否转储类货源不能为空'), type: 'warning' })
          return
        }
        if (!args.data.mrp) {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          this.$toast({ content: this.$t('MRP不能为空'), type: 'warning' })
          return
        }
        if (Number(new Date(args.data.startTime)) > Number(new Date(args.data.endTime))) {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          this.$toast({ content: this.$t('开始日期不能大于结束日期'), type: 'warning' })
          return
        }
        // this.handleAddRow()
        // this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.refresh()
        // this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
      }
    },
    handleAddRow() {
      //获取编辑或者新增的数据
      let currentRecords = this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.dataSource
      let row = cloneDeep(this.supplyGoods)
      currentRecords.some((item) => {
        if (item.addId == this.editAddId) {
          Object.assign(item, row)
        }
      })
    },
    selectedChanged(val) {
      Object.assign(this.supplyGoods, val.itemInfo || {})
    },
    clickButtonSave(val) {
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      setTimeout(() => {
        this.$refs.dialogRef.validate((valid) => {
          if (valid) {
            let _dataSource = cloneDeep(
              this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.getCurrentViewRecords()
            )
            _dataSource.map((item) => {
              item.isDump = item.isDump == 0 ? false : true
              // item.mrpName = item.mrp
              // delete item.mrp
            })
            let params = {
              paramObj: null, //老页面参数
              request: {
                ...this.formObject,
                itemAddNewRequestList: _dataSource
              } //新页面参数
            }
            if (!this.editFormObject.id && val == '0') {
              console.log(params)
              this.$API.sourceFiles
                .batchAdd(params)
                .then(() => {
                  this.$router.push({
                    path: '/masterdata/source-files'
                  })
                  this.$toast({
                    content: this.$t('保存成功'),
                    type: 'success'
                  })
                })
                .catch((err) => {
                  this.$hloading()
                  this.$toast({
                    content: err.msg,
                    type: 'error'
                  })
                })
            } else if (!this.editFormObject.id && val == '1') {
              this.$API.sourceFiles
                .andSync(params)
                .then(() => {
                  this.$router.push({
                    path: '/masterdata/source-files'
                  })
                  this.$toast({
                    content: this.$t('保存并同步发送成功'),
                    type: 'success'
                  })
                })
                .catch((err) => {
                  this.$hloading()
                  this.$toast({
                    content: err.msg,
                    type: 'error'
                  })
                })
            } else if (this.editFormObject.id && val == '0') {
              let _updateList = [
                {
                  ...params.request,
                  //  params.request.siteId
                  ...params.request.itemAddNewRequestList[0],
                  id: this.editFormObject.id
                }
              ]
              delete _updateList[0].itemAddNewRequestList
              let obj = {
                updateList: _updateList
              }
              console.log(obj)
              this.$API.sourceFiles
                .batchUpdate(obj)
                .then(() => {
                  this.$router.push({
                    path: '/masterdata/source-files'
                  })
                  this.$toast({
                    content: this.$t('保存成功'),
                    type: 'success'
                  })
                })
                .catch((err) => {
                  this.$hloading()
                  this.$toast({
                    content: err.msg,
                    type: 'error'
                  })
                })
            } else if (this.editFormObject.id && val == '1') {
              let _updateList = [
                {
                  ...params.request,
                  ...params.request.itemAddNewRequestList[0],
                  id: this.editFormObject.id
                }
              ]
              delete _updateList[0].itemAddNewRequestList
              let obj = {
                updateList: _updateList
              }
              this.$API.sourceFiles
                .batchUpdateAndSync(obj)
                .then(() => {
                  this.$router.push({
                    path: '/masterdata/source-files'
                  })
                  this.$toast({
                    content: this.$t('保存成功'),
                    type: 'success'
                  })
                })
                .catch((err) => {
                  this.$hloading()
                  this.$toast({
                    content: err.msg,
                    type: 'error'
                  })
                })
            }
          }
        })
      }, 100)
    },
    handleClickCellTitle() {},
    // 模糊搜索，不区分大小写模糊搜索
    filteringResource(e, dataSource, key) {
      if (typeof e.text === 'string' && e.text) {
        e.updateData(
          dataSource?.filter((f) => f[key]?.toUpperCase().includes(e?.text?.toUpperCase()))
        )
      } else {
        e.updateData(dataSource)
      }
    }
  }
}
</script>
<style lang="scss">
.inventory {
  height: 100vh;

  .top-info {
    width: 100%;
    padding: 20px;
    box-sizing: border-box;
    background-color: #fff;

    //按钮
    .detail-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .name-wrap {
      }
      .btns-wrap {
        .detail-header-button {
          .e-flat {
            color: rgba(0, 70, 156, 1);
          }
        }
      }
    }
    //表单
    .formInput {
      width: 100%;
      .mt-form {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        .mt-form-item {
          width: 400px;
        }
        .mt-form-item:nth-last-child(1) {
          width: 405px;
          .mt-input {
            display: inline-block;
            width: 320px;
          }
          /deep/.e-btn {
            padding: 6px 8px 4px;
            margin-left: 10px;
          }
        }
        &::after {
          content: '';
          width: 400px;
        }
      }
      .mt-form:nth-of-type(2) {
        width: 100%;
        .mt-form-item {
          width: 100%;
          .mt-input {
            width: 100%;
          }
        }
      }
      .applyOrganization {
        position: relative;
        .mt-input:hover + .hover {
          display: block;
        }
      }
      .hover {
        display: none;
        position: absolute;
        top: -110px;
        left: 0;
        z-index: 1;
        width: 400px;
        height: 100px;
        background: #fff;
        border: 1px solid #fee;
        border-radius: 5px;
      }
    }
  }
}
</style>
