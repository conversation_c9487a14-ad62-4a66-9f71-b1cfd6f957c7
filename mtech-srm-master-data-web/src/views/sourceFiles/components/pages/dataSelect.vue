<template>
  <div>
    <mt-date-picker
      v-if="data.column.field === 'startTime'"
      :id="data.column.field"
      v-model="data[data.column.field]"
      :open-on-focus="true"
      :disabled="isDisabled"
      :allow-edit="false"
      :placeholder="$t('请选择开始时间')"
    ></mt-date-picker>
    <mt-date-picker
      v-else
      :id="data.column.field"
      v-model="data[data.column.field]"
      :open-on-focus="true"
      :min="new Date(this.data['startTime'])"
      :disabled="isDisabled"
      :allow-edit="false"
      :placeholder="$t('请选择结束时间')"
    ></mt-date-picker>
  </div>
</template>

<script>
// import { Query } from '@syncfusion/ej2-data'
// import utils from '@/utils/utils'

// import { log } from 'console'

export default {
  data() {
    return {
      isDisabled: false
      // limitTradeTotal: null,
      // limitTradeTimes: null,
      // formObject: null,
    }
  },

  mounted() {
    // this.formObject = JSON.parse(sessionStorage.getItem('sourceFilesFormObject'))
    // this.$bus.$on('sourceFilesLimitTradeNumber', (params) => {
    //   if (!this.formObject.id) return
    //   if (params.code == 'limitTradeTotal') {
    //     //判断限制交易数量发生变化
    //     this.limitTradeTotal = params.value
    //   } else if (params.code == 'limitTradeTimes') {
    //     this.limitTradeTimes = params.value
    //   }
    //   console.log(
    //     this.limitTradeTotal,
    //     this.formObject['limitTradeTotal'],
    //     this.limitTradeTimes,
    //     this.formObject['limitTradeTimes'],
    //   )
    //   if (
    //     (!this.limitTradeTotal && !this.limitTradeTimes) ||
    //     (this.limitTradeTotal == this.formObject['limitTradeTotal'] &&
    //       this.limitTradeTimes == this.formObject['limitTradeTimes']) ||
    //     (this.limitTradeTotal == this.formObject['limitTradeTotal'] && !this.limitTradeTimes) ||
    //     (this.limitTradeTimes == this.formObject['limitTradeTimes'] && !this.limitTradeTotal)
    //   ) {
    //     this.data[this.data.column.field] = new Date(this.formObject[this.data.column.field])
    //   } else {
    //     this.data[this.data.column.field] = new Date()
    //   }
    //   // if (
    //   //   params.value == this.formObject[params.code] &&
    //   //   this.data['limitTradeTimes'] == this.formObject['limitTradeTimes']
    //   // ) {
    //   //   this.data[this.data.column.field] = new Date(this.formObject[this.data.column.field])
    //   // } else {
    //   //   this.data[this.data.column.field] = new Date()
    //   // }
    // })
    // // if (this.data.column.field === 'startTime' && !this.formObject.id) {
    //   this.data[this.data.column.field] = new Date()
    //   this.isDisabled = true
    // } else if (this.data.column.field === 'startTime') {
    //   this.isDisabled = true
    // }
  },
  methods: {}
}
</script>
