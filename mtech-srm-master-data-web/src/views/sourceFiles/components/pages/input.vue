<template>
  <div id="cell-changed">
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      :disabled="disabled"
      @input="onInput"
    ></mt-input>
  </div>
</template>
<script>
export default {
  data() {
    return { data: {}, disabled: false }
  },
  mounted() {
    if (this.data.column.field == 'itemCode') {
      this.disabled = true
      this.$bus.$on(`sourceFiles${this.data.column.field}Bus`, (val) => {
        this.data[`${this.data.column.field}`] = val
      })
    }
    if (this.data.column.field == 'dumpSiteCode') {
      this.disabled = true
      this.$bus.$on(`sourceFiles${this.data.column.field}Bus`, (val) => {
        this.data[`${this.data.column.field}`] = val
      })
    }
  },
  methods: {
    onInput(e) {
      console.log(e)
    }
  }
}
</script>
