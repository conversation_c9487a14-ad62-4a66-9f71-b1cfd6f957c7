<template>
  <div>
    <mt-multi-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @change="selectChange"
      :open-dispatch-change="true"
      :allow-filtering="true"
      :filtering="(e) => filteringResource(e, data.column.field)"
      :disabled="isDisabled"
    ></mt-multi-select>
  </div>
</template>

<script>
// import { utils } from '@mtech-common/utils'
import { cloneDeep } from 'lodash'

export default {
  data() {
    return {
      placeholder: this.$t('请选择'),

      fields: { text: '', value: '' },

      dataSource: [],
      isDisabled: false
    }
  },

  mounted() {
    //物料
    if (this.data.column.field === 'itemName') {
      this.fields = { text: 'itemName', value: 'itemName' }
      let _sourceFilesFormObject = JSON.parse(sessionStorage.getItem('sourceFilesFormObject'))
      this.fuzzyPagedQuery(_sourceFilesFormObject)
    }
  },

  methods: {
    selectChange(e) {
      if (e.e == null) return
      if (this.data.column.field === 'itemName') {
        let params = {
          fieldCode: 'itemName',
          itemInfo: {
            itemId: e.itemData.id,
            itemCode: e.itemData.itemCode,
            itemName: e.itemData.itemName,
            itemDescription: e.itemData.itemDescription
          }
        }
        this.$parent.$emit('selectedChanged', params)
        this.$bus.$emit('sourceFilesitemCodeBus', e.itemData.itemCode)
      }
    },
    async fuzzyPagedQuery(formObject) {
      console.log(formObject.id)
      let params = null
      if (formObject.id) {
        params = {
          categoryCode: formObject.categoryCode,
          fuzzyName: '',
          orgCodes: [formObject.siteCode]
        }
      } else {
        params = {
          categoryCode: formObject.categoryCode,
          fuzzyName: '',
          orgCodes: [...formObject.siteOrgCodes]
        }
      }
      this.$API.sourceFiles.siteids(params).then((res) => {
        let _data = cloneDeep(res.data)
        _data.forEach((item) => {
          item.itemName = item.itemCode + '-' + item.itemName
        })
        this.dataSource = _data || []
        if (formObject.id) {
          this.isDisabled = true
          this.data.itemCode = formObject.itemCode
          this.data.itemName = formObject.itemCode + '-' + formObject.itemName
          this.data.itemId = formObject.itemId
        }
      })
    },
    getFactoryItem() {
      this.$loading()
      this.$API.sourceLists.fuzzyQuery({}).then((res) => {
        this.$hloading()
        this.dataSource = res.data
      })
      this.fields = { text: 'siteName', value: 'siteName' }
    },
    filteringResource(e, key) {
      if (key === 'itemName') {
        let _sourceFilesFormObject = JSON.parse(sessionStorage.getItem('sourceFilesFormObject'))
        this.filterFuzzyPagedQuery(e.text, _sourceFilesFormObject)
      }
    },
    async filterFuzzyPagedQuery(text, formObject) {
      if (!formObject) return
      let params = {
        categoryCode: formObject.categoryCode,
        fuzzyName: text,
        orgCodes: [...formObject.siteOrgCodes]
      }
      this.$API.sourceFiles.siteids(params).then((res) => {
        let _data = cloneDeep(res.data)
        _data.forEach((item) => {
          item.itemName = item.itemCode + '-' + item.itemName
        })
        this.dataSource = _data || []
        if (formObject.id) {
          this.isDisabled = true
          this.data.itemCode = formObject.itemCode
          this.data.itemName = formObject.itemCode + '-' + formObject.itemName
          this.data.itemId = formObject.itemId
        }
      })
    }
  },
  beforeDestroy() {
    this.$bus.$off('sourceFilesitemExtendTypeDescBus')
    this.$bus.$off('sourceFilesdumpSiteCodeBus')
    this.$bus.$off('sourceFilesisDumpDescBus')
  }
}
</script>
