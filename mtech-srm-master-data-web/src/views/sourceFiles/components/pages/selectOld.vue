<template>
  <div>
    <mt-select
      v-if="data.column.field !== 'itemName'"
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @open="startOpen"
      @change="selectChange"
      :open-dispatch-change="true"
      :allow-filtering="true"
      :filtering="(e) => filteringResource(e, dataSource, data.column.field)"
      :disabled="isDisabled"
    ></mt-select>
    <mt-select
      v-else
      :id="data.column.field"
      v-model="data[data.column.field]"
      :popup-width="450"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @open="startOpen"
      @change="selectChange"
      :open-dispatch-change="true"
      :allow-filtering="true"
      :filtering="itemNamefiltering"
      :disabled="isDisabled"
    ></mt-select>
    <!--
      :filtering="filtering"
     -->
  </div>
</template>

<script>
import { utils } from '@mtech-common/utils'
import { cloneDeep } from 'lodash'

export default {
  data() {
    return {
      placeholder: this.$t('请选择'),

      fields: { text: '', value: '' },

      dataSource: [],
      isDisabled: false
    }
  },

  mounted() {
    this.itemNamefiltering = utils.debounce(this.itemNamefiltering, 1000)
    //物料
    if (this.data.column.field === 'itemName') {
      this.fields = { text: 'itemName', value: 'itemName' }
      let _sourceFilesFormObject = JSON.parse(sessionStorage.getItem('sourceFilesFormObject'))
      this.fuzzyPagedQuery(
        this.data[this.data.column.field] ? this.data[this.data.column.field] : '',
        _sourceFilesFormObject
      )
    }
    //货源类型
    if (this.data.column.field === 'sourceType') {
      this.fields = { text: 'text', value: 'value' }
      this.dataSource = [
        { text: this.$t('临时货源'), value: '1' },
        { text: this.$t('一次性货源'), value: '2' },
        { text: this.$t('正式货源'), value: '3' }
      ]
    }
    //是否转储类货源
    if (this.data.column.field === 'isDump') {
      this.fields = { text: 'text', value: 'value' }
      this.dataSource = [
        { text: this.$t('是'), value: 1 },
        { text: this.$t('否'), value: 0 }
      ]
    }
    //采购工厂
    if (this.data.column.field === 'dumpSiteName') {
      if (!this.data.isDump || this.data.isDump == '0') {
        this.isDisabled = true
      } else {
        this.isDisabled = false
      }
      //监听isDump变化
      this.$bus.$on('sourceFilesisDumpDescBus', (val) => {
        if (!val || val == '0') {
          this.data.dumpSiteName = null
          this.isDisabled = true
        } else {
          this.isDisabled = false
        }
      })
      //工厂下拉
      this.getFactoryItem()
    }
    //mrp
    if (this.data.column.field === 'mrp') {
      this.fields = { text: 'text', value: 'value' }
      this.dataSource = [
        { text: this.$t('与MRP有关的记录'), value: '1' },
        { text: this.$t('与MRP计划表自动生成的计划行有关的记录'), value: '2' }
      ]
    }
    //货源状态
    if (this.data.column.field === 'statusId') {
      this.fields = { text: 'text', value: 'value' }
      this.dataSource = [
        { text: this.$t('激活'), value: '1' },
        { text: this.$t('失效'), value: '0' }
      ]
    }
  },

  methods: {
    startOpen() {},
    selectChange(e) {
      if (e.e == null) return
      if (this.data.column.field === 'itemName') {
        let params = {
          fieldCode: 'itemName',
          itemInfo: {
            itemId: e.itemData.id,
            itemCode: e.itemData.itemCode,
            itemName: e.itemData.itemName,
            itemDescription: e.itemData.itemDescription
          }
        }
        this.$parent.$emit('selectedChanged', params)
        this.$bus.$emit('sourceFilesitemCodeBus', e.itemData.itemCode)
      }
      if (this.data.column.field === 'dumpSiteName') {
        let params = {
          fieldCode: 'dumpSiteName',
          itemInfo: {
            dumpSiteOrgId: e.itemData.organizationId,
            dumpSiteCode: e.itemData.siteCode,
            dumpSiteName: e.itemData.siteName
          }
        }
        this.$parent.$emit('selectedChanged', params)
        this.$bus.$emit('sourceFilesdumpSiteCodeBus', e.itemData.siteCode)
      }
      // if (this.data.column.field === 'mrp') {
      //   let params = {
      //     fieldCode: 'mrp',
      //     itemInfo: {
      //       mrpId: e.itemData.id,
      //       mrpCode: e.itemData.itemCode,
      //       mrp: e.itemData.itemName,
      //     },
      //   }
      //   this.$parent.$emit('selectedChanged', params)
      // }
      if (this.data.column.field === 'isDump') {
        this.$bus.$emit('sourceFilesisDumpDescBus', e.value)
      }
    },
    async fuzzyPagedQuery(text, formObject) {
      let params = null
      if (formObject.id) {
        this.dataSource = [
          { itemCode: formObject.itemCode, itemName: formObject.itemName, id: formObject.itemId }
        ]
        this.isDisabled = true
        this.data.itemCode = formObject.itemCode
        this.data.itemName = formObject.itemName
        this.data.itemId = formObject.itemId
      } else {
        params = {
          categoryCodeList: formObject.categoryCodes,
          fuzzyName: text,
          siteCodeList: formObject.siteCodes
        }
        this.$API.sourceFiles.queryUnionSetItem(params).then((res) => {
          let _data = cloneDeep(res.data)
          _data.forEach((item) => {
            item.itemName = item.itemCode + '-' + item.itemName
          })
          this.dataSource = _data || []
          if (formObject.id) {
            this.isDisabled = true
            this.data.itemCode = formObject.itemCode
            this.data.itemName = formObject.itemName
            this.data.itemId = formObject.itemId
          }
        })
      }
    },
    // async filterFuzzyPagedQuery(text, formObject) {
    //   let params = {
    //     categoryCode: formObject.categoryCode,
    //     fuzzyName: text,
    //     orgCodes: [...formObject.siteOrgCodes],
    //   }
    //   this.$API.sourceFiles.siteids(params).then((res) => {
    //     let _data = cloneDeep(res.data)
    //     _data.forEach((item) => {
    //       item.itemName = item.itemCode + '-' + item.itemName
    //     })
    //     this.dataSource = _data || []
    //   })
    //   // console.log(text, formObject)
    //   // let obj = {
    //   //   page: {
    //   //     current: 1,
    //   //     size: 20,
    //   //   },
    //   //   condition: 'or',
    //   //   defaultRules: [
    //   //     {
    //   //       field: 'organizationId',
    //   //       type: 'Array',
    //   //       operator: 'in',
    //   //       value: formObject.siteOrgCodes,
    //   //     },
    //   //     {
    //   //       field: 'categoryCode',
    //   //       type: 'string',
    //   //       operator: 'in',
    //   //       value: formObject.categoryCode,
    //   //     },
    //   //   ],
    //   //   rules: [
    //   //     {
    //   //       field: 'itemCode',
    //   //       type: 'string',
    //   //       operator: 'contains',
    //   //       value: text || '',
    //   //     },
    //   //     {
    //   //       field: 'itemName',
    //   //       type: 'string',
    //   //       operator: 'contains',
    //   //       value: text || '',
    //   //     },
    //   //   ],
    //   // }
    //   // await this.$API.sourceLists
    //   //   .itemPagedQuery(obj)
    //   //   .then((res) => {
    //   //     res.data.records?.forEach((item) => {
    //   //       item.itemName = item.itemCode + '-' + item.itemName
    //   //     })
    //   //     this.dataSource = res.data.records || []
    //   //   })
    //   //   .catch((err) => {
    //   //     this.$toast({
    //   //       content: err.msg,
    //   //       type: 'error',
    //   //     })
    //   //   })
    // },
    getFactoryItem() {
      this.$loading()
      this.$API.sourceLists.fuzzyQuery({}).then((res) => {
        this.$hloading()
        this.dataSource = res.data
      })
      if (this.data.isDump == 0) {
        this.isDisabled = true
      }
      this.fields = { text: 'siteName', value: 'siteName' }
    },
    filteringResource(e, dataSource, key) {
      if (key === 'sourceType' || key === 'isDump' || key === 'mrp' || key === 'statusId') {
        if (typeof e.text === 'string' && e.text) {
          e.updateData(
            dataSource?.filter((f) => f['text']?.toUpperCase().includes(e?.text?.toUpperCase()))
          )
        } else {
          e.updateData(dataSource)
        }
      } else if (key === 'dumpSiteName') {
        if (typeof e.text === 'string' && e.text) {
          e.updateData(
            dataSource?.filter((f) => f['siteName']?.toUpperCase().includes(e?.text?.toUpperCase()))
          )
        } else {
          e.updateData(dataSource)
        }
      }
    },
    itemNamefiltering(e) {
      console.log(e)
      let _sourceFilesFormObject = JSON.parse(sessionStorage.getItem('sourceFilesFormObject'))
      this.fuzzyPagedQuery(e.text, _sourceFilesFormObject)
    }
  },
  beforeDestroy() {
    this.$bus.$off('sourceFilesitemExtendTypeDescBus')
    this.$bus.$off('sourceFilesdumpSiteCodeBus')
    this.$bus.$off('sourceFilesisDumpDescBus')
  }
}
</script>
