<template>
  <div id="cell-changed">
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      :disabled="disabled"
      type="number"
      :min="0"
      @input="onInput"
    ></mt-input>
  </div>
</template>
<script>
export default {
  data() {
    return { data: {}, disabled: false }
  },
  mounted() {},
  methods: {
    onInput() {
      // let params = {
      //   code: this.data.column.field,
      //   value: e,
      // }
      // this.$bus.$emit('sourceFilesLimitTradeNumber', params)
    }
  },
  beforeDestroy() {
    // this.$bus.$off('sourceFilesLimitTradeNumber')
  }
}
</script>
