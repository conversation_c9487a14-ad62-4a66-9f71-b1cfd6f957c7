<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
    <!-- 导入弹框 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import { columnData } from './config/index'
// import { download, getHeadersFileName } from '@/utils/file.js'
import UploadExcelDialog from '@/components/Upload/uploadExcelDialog'
export default {
  components: {
    UploadExcelDialog
  },
  data() {
    return {
      pageConfig: [
        {
          gridId: '85fc2975-5571-4445-821d-80e197ef7888',
          toolbar: [
            'Add',
            {
              id: 'upload',
              icon: 'icon_solid_upload',
              title: this.$t('导入')
            },
            'Edit',
            'delete',
            {
              id: 'SAP',
              icon: 'icon_solid_export',
              title: this.$t('同步SAP')
            },
            {
              id: 'disable',
              icon: 'icon_table_disable',
              title: this.$t('失效')
            }
          ],
          useToolTemplate: false,
          grid: {
            virtualPageSize: 30,
            enableVirtualization: true,
            customSelection: true,
            selectionSettings: {
              persistSelection: true,
              type: 'Multiple',
              checkboxOnly: true
            },
            pageSettings: {
              currentPage: 1,
              pageSize: 20,
              pageSizes: [20, 50, 100, 200, 500, 1000],
              totalRecordsCount: 0
            },
            showSelected: false,
            columnData: columnData,
            asyncConfig: {
              ignoreDefaultSearch: true,
              url: '/masterDataManagement/tenant/supply-source-list/paged-query',
              serializeList: (list) => {
                list.forEach((item) => {
                  item.isDump = item.isDump ? 1 : 0
                })
                return list
              }
            }
          }
        }
      ],
      downTemplateParams: {
        pageFlag: false
      }, // 导入下载模板参数
      uploadParams: {}, // 导入文件参数
      // 导入请求接口配置
      requestUrls: {
        templateUrlPre: 'sourceFiles',
        templateUrl: 'exportData', // 下载模板接口方法名
        uploadUrl: 'importData' // 上传接口方法名
      }
    }
  },
  methods: {
    handleClickToolBar(item) {
      let records = item.data ? [item.data] : item.gridRef.getCustomSelectedRows()
      if (records.length <= 0 && !(item.toolbar.id == 'Add' || item.toolbar.id == 'upload')) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (item.toolbar.id == 'Add') {
        this.handleClickToolBarAdd()
      } else if (item.toolbar.id == 'Edit') {
        this.handleClickToolBarEdit(records)
      } else if (item.toolbar.id == 'Delete') {
        this.handleClickToolBarDelete(records)
      } else if (item.toolbar.id == 'disable') {
        this.handleClickToolBarDisable(records) //失效
      } else if (item.toolbar.id == 'upload') {
        this.handleClickToolBarUpload(true) //导入
      } else if (item.toolbar.id == 'SAP') {
        this.handleClickToolBarSAP(records) //同步SAP
      }
    },
    handleClickToolBarAdd() {
      this.$router.push({
        path: '/masterdata/source-files-add'
      })
    },
    handleClickToolBarEdit(records) {
      if (records.length > 1) {
        this.$toast({ content: this.$t('只能选择一行'), type: 'warning' })
        return
      }
      if (records[0].statusId == 1) {
        this.$toast({ content: this.$t('激活状态不可编辑'), type: 'warning' })
        return
      }
      if (records[0].statusId == 5) {
        this.$toast({ content: this.$t('冻结状态不可编辑'), type: 'warning' })
        return
      }
      this.$router.push({
        path: '/masterdata/source-files-add',
        query: {
          id: records[0].id
        }
      })
    },
    handleClickToolBarDelete(records) {
      this.$loading()
      let statusIds = []
      let freeze = []
      records.map((item) => {
        if (item.statusId == 1) {
          statusIds.push(item)
        } else if (item.statusId == 5) {
          freeze.push(item)
        }
      })
      if (statusIds.length > 0) {
        this.$toast({ content: this.$t('激活状态不可删除'), type: 'warning' })
        return
      }
      if (freeze.length > 0) {
        this.$toast({ content: this.$t('冻结状态不可删除'), type: 'warning' })
        return
      }

      let ids = records.map((item) => item.id)
      this.$API.sourceFiles
        .batchDelete({ ids })
        .then(() => {
          this.$hloading()
          this.$refs.templateRef.refreshCurrentGridData()
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    handleClickToolBarDisable(records) {
      this.$loading()
      let freeze = []
      records.map((item) => {
        if (item.statusId == 5) {
          freeze.push(item)
        }
      })
      if (freeze.length > 0) {
        this.$toast({ content: this.$t('冻结状态不能失效'), type: 'warning' })
        return
      }
      let ids = records.map((item) => item.id)
      this.$API.sourceFiles
        .batchInvalid({ ids })
        .then(() => {
          this.$hloading()
          this.$refs.templateRef.refreshCurrentGridData()
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        })
        .catch((err) => {
          this.$hloading()
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    handleClickToolBarUpload() {
      this.showUploadExcel(true)
    },
    // 显示隐藏上传弹框
    showUploadExcel(flag) {
      console.log(flag)
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 上传成功后
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.$toast({
        content: this.$t('导入成功'),
        type: 'success'
      })
      this.$refs.templateRef.refreshCurrentGridData()
    },
    handleClickToolBarSAP(records) {
      let freeze = []
      records.map((item) => {
        if (item.statusId == 5) {
          freeze.push(item)
        }
      })
      if (freeze.length > 0) {
        this.$toast({ content: this.$t('冻结状态不能同步SAP'), type: 'warning' })
        return
      }
      const checkResult = records.map((i) => {
        return {
          categoryCode: i.categoryCode,
          companyCode: i.organizationCode,
          errorInfo: i.failReason,
          itemCode: i.itemCode,
          supplierCode: i.supplierCode,
          siteCode: i.siteCode
        }
      })
      this.$API.sourceFiles.checkExemptionApply(checkResult).then((res) => {
        if (res.code === 200) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认将所选数据同步SAP？')
            },
            success: async () => {
              this.$loading()
              let ids = records.map((item) => item.id)
              this.$API.sourceLists
                .batchSyncOutward({ ids })
                .then(() => {
                  this.$hloading()
                  this.$refs.templateRef.refreshCurrentGridData()
                  this.$toast({
                    content: this.$t('同步发送成功'),
                    type: 'success'
                  })
                })
                .catch((err) => {
                  this.$hloading()
                  this.$toast({
                    content: err.msg,
                    type: 'error'
                  })
                })
            }
          })
        }
      })
    },
    handleClickCellTitle(e) {
      console.log(e)
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/styles/mixin.scss';

.full-height {
  height: 100%;
}
.dialog-content {
  margin-top: 20px;
}
::v-deep {
  .e-rowcell.custom-checkbox,
  .e-headercell.custom-checkbox {
    @include sticky-col;
    left: 0px;
  }
  .e-rowcell.sticky-col-1,
  .e-headercell.sticky-col-1 {
    @include sticky-col;
    left: 50px;
  }
  .e-rowcell.sticky-col-2,
  .e-headercell.sticky-col-2 {
    @include sticky-col;
    left: 200px;
  }
}
</style>
