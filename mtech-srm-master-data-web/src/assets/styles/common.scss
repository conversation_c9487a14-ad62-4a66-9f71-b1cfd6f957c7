
.mt-pl-10 { padding-left: 10px; }
.mt-pl-20 { padding-left: 20px; }
.mt-pa-10 { padding: 10px; }
.mt-pa-20 { padding: 20px; }
.mt-pb-20 { padding-bottom: 20px; }
.mt-pt-20 { padding-top: 20px; }
.mt-px-10 { padding: 0 10px; }
.mt-px-20 { padding: 0 20px; }
.mt-py-10 { padding: 10px 0; }
.mt-py-20 { padding: 20px 0; }

.mt-ma-10 { margin: 10px; }
.mt-mt-10 { margin-top: 10px; }
.mt-mb-10 { margin-bottom: 10px; }
.mt-ml-10 { margin-left: 10px; }
.mt-mr-10 { margin-right: 10px; }
.mt-mr-20 { margin-right: 20px; }
.mt-mt-20 { margin-top: 20px; }
.mt-mb-20 { margin-bottom: 20px; }
.mt-my-20 { margin: 20px 0; }

.full-width { width: 100%; }
.full-height { height: 100%; }

.flex { display: flex; }
.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }
.justify-start { justify-content: start; }
.justify-end { justify-content: end; }
.items-center { align-items: center; }

.mt-gutter-20 > *:not(:last-child) {
  margin-right: 20px;
}

.display-none { display: none; }

.bg-white {
  background-color: #fff;
}

.cursor-pointer {
  cursor: pointer;
}

.mt-dialog {
  display: none;
}
