//input,select
$input-bg: #f9f9f9;
$input-border: $border;
$input-bg-disabled: rgb(239, 239, 239);
$input-border-focus: rgb(102, 175, 211);
$input-color-placeholder: #ccc;
//default
$input-padding-default-vertical: $padding-default-vertical;
$input-padding-default-horizontal: $padding-default-horizontal;
$input-height-default: 30px;
$input-font-size-default: $font-size-default;
$input-border-radius-default: $border-radius-default;
//small
$input-padding-small-vertical: $padding-small-vertical;
$input-padding-small-horizontal: $padding-small-horizontal;
$input-height-small: 28px;
$input-font-size-small: $font-size-small;
$input-border-radius-small: $border-radius-default;
//medium
$input-padding-medium-vertical: $padding-medium-vertical;
$input-padding-medium-horizontal: $padding-medium-horizontal;
$input-height-medium: 40px;
$input-font-size-medium: $font-size-medium;
$input-border-radius-medium: $border-radius-medium;
//large
$input-padding-large-vertical: $padding-large-vertical; //  padding:8px null;
$input-padding-large-horizontal: $padding-large-horizontal;
$input-height-large: 48px;
$input-font-size-large: $font-size-large;
$input-border-radius-large: $border-radius-large;