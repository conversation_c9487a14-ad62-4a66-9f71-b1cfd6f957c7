<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>形状结合@4x</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="物料主数据详情-新增/编辑（展开）" transform="translate(-992.000000, -787.000000)" fill="#98AAC3">
            <g id="Group-3" transform="translate(742.000000, 432.000000)">
                <g id="component/selector/normal备份-3" transform="translate(10.000000, 268.000000)">
                    <g id="编组-8备份-4" transform="translate(180.000000, 27.000000)">
                        <path d="M80.6666667,60 L80.666,79.333 L100,79.3333333 L100,80.6666667 L80.666,80.666 L80.6666667,100 L79.3333333,100 L79.333,80.666 L60,80.6666667 L60,79.3333333 L79.333,79.333 L79.3333333,60 L80.6666667,60 Z" id="形状结合"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>