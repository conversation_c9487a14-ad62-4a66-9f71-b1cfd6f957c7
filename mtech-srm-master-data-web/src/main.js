/*
 * @Author: your name
 * @Date: 2021-08-12 16:27:35
 * @LastEditTime: 2022-04-07 17:39:09
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\main.js
 */
import Vue from 'vue'
import App from './App.vue'
import '@mtech-micro-frontend/vue-cli-plugin-micro/public-path.js'
import { sso } from '@mtech-sso/single-sign-on'
import VueRouter from 'vue-router'
import routes from './router'
import store from './store'
import APIS from '@/apis'

import '@/assets/main.scss'
import './mtechUI'
import '@/assets/icons'
import waves from '@/directive/waves'

import { setLocal } from '@mtech-ui/base'
import { baseConfig } from '@mtech-common/http'
import Bus from '@/utils/bus.js'

import indexDB from '@digis/internationalization'

Vue.use(waves)
Vue.prototype.$bus = Bus
baseConfig.setDefault({ baseURL: '/api' })

baseConfig.addNotify({
  success: function (msg) {
    // 可以使用$toast组件，msg当前版本下默认为接口response.msg
    Vue.prototype.$toast({
      content: msg,
      type: 'success'
    })
  },
  error: function (msg) {
    Vue.prototype.$toast({
      content: msg,
      type: 'error'
    })
  }
})

Vue.prototype.$API = APIS
Vue.prototype.$store = store

const i18n = indexDB.digisI18n(Vue, 'master-data') //第二个参数是当前使用的应用code

const internationlization = localStorage.getItem('internationlization')
if (internationlization === 'zh' || internationlization === 'zh-CH') {
  setLocal('zh-CN')
}

Vue.config.productionTip = false
Vue.use(VueRouter)

let instance = null
let router = null

function render(props = {}) {
  const { container } = props

  router = new VueRouter({
    // mode: "hash",
    // base: window.__POWERED_BY_QIANKUN__ ? "/mdm" : "/",
    // base:"/",
    routes
  })

  router.beforeEach((to, from, next) => {
    next()
  })
  indexDB.layoutCreatLanguage().finally(() => {
    instance = new Vue({
      router,
      store,
      i18n,
      render: (h) => h(App)
    }).$mount(container ? container.querySelector('#masterdataApp') : '#masterdataApp')
  })
}

if (!window.__POWERED_BY_QIANKUN__) {
  sso()
  render()
}

function storeTest(props) {
  const user = props.data.user ? props?.data?.user?.userInfo : props?.data?.app?.userInfo
  store.commit('setUser', user)
  props.onGlobalStateChange &&
    props.onGlobalStateChange(
      (value, prev) => console.log(`[onGlobalStateChange - ${props.name}]:`, value, prev),
      true
    )
  props.setGlobalState &&
    props.setGlobalState({
      ignore: props.name,
      user: {
        name: props.name
      }
    })
}

//single-spa的生命周期函数
export async function bootstrap({ fns = [] } = {}) {
  Array.isArray(fns) &&
    fns.map((i) => {
      Vue.prototype[i.name] = i
    })
  console.log('%c ', 'color: green;', 'app bootstraped')
}

export async function mount(props) {
  // 在这里挂载vue
  storeTest(props)
  render(props)
  console.log('%c ', 'color: green;', 'app mount', props)
}

export async function unmount() {
  // 在这里unmount实例的vue
  instance.$destroy()
  instance.$el.innerHTML = ''
  instance = null
  router = null
  console.log('%c ', 'color: green;', 'app unmount')
}

export { i18n }
