/*
 * @Author: your name
 * @Date: 2021-08-12 16:27:35
 * @LastEditTime: 2021-08-12 17:02:47
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\utils\util.js
 */

import Vue from 'vue'
const isString = (obj) => Object.prototype.toString.call(obj) === '[object String]'

const isPainObject = (o) =>
  o && Object.prototype.toString.call(o) === '[object Object]' && 'isPrototypeOf' in o

const isEmpty = (obj) => {
  /* eslint-disable */
    if (obj == null) return true
    if (Array.isArray(obj) || isString(obj)) return obj.length === 0
    for (let key in obj) {
        if (obj.hasOwnProperty(key)) return false
    }
    return true
}


const formatDate = (date = new Date(), format = 'yyyy-MM-dd') => {
    if (isString(date) || isNumber(date)) {
        date = newDate(date)
    }

    let o = {
        'M+': date.getMonth() + 1,
        'd+': date.getDate(),
        'h+': date.getHours(),
        'm+': date.getMinutes(),
        's+': date.getSeconds(),
        'q+': Math.floor((date.getMonth() + 3) / 3),
        S: date.getMilliseconds()
    }
    let w = [['日', '一', '二', '三', '四', '五', '六'], ['周日', '周一', '周二', '周三', '周四', '周五', '周六'], ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']]
    let now = new Date()
    let today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    let start = new Date(date.getFullYear(), date.getMonth(), date.getDate())
    let diff = (start - today) / 86400000
    let text

    switch (diff) {
        case 0:
            text = '今天'
            break
        // case 1:
        //     text = '明天'
        //     break
        // case 2:
        //     text = '后天'
        //     break
        default:
            text = ''
    }

    if (/(y+)/.test(format)) {
        format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
    }

    if (/(w+)/.test(format)) {
        if (text) {
            format = format.replace(RegExp.$1, text)
        } else {
            format = format.replace(RegExp.$1, w[RegExp.$1.length - 1][date.getDay()])
        }
    }

    for (let k in o) {
        if (new RegExp('(' + k + ')').test(format)) {
            format = format.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length))
        }
    }

    return format
}

const newDate = val => (isString(val) ? new Date(val.replace(/-/g, '/')) : new Date(val))

const isNumber = num => {
    return parseFloat(num).toString() !== 'NaN'
}

// {
//   "Name": {
//     "required": [true, "请输入"],
//     "email": [true, "请输入正确的email"],
//     "url": [true, "请输入正确的url"],
//     "date": [true, "请输入正确的date"],
//     "dateIso": [true, "请输入正确的dateIso"],
//     "number": [true, "请输入number"],
//     "maxLength": [2, "最大长度2"],
//     "minLength": [2, "最小长度2"],
//     "rangeLength": [[1, 5], "长度在1-5个之间"],
//     "range": [[1, 5], "1-5之间的数字"],
//     "max": [5, "小于5的数字"],
//     "min": [5, "大于5的数字"],
//     "regex": ["^[A-z]+$", "字母"]
//     }
//   }
const formatRules = (rules)=> {
  if (Object.prototype.toString.call(rules) != "[object Object]") {
    return {};
  }
  let res = {};
  for (var i in rules) {
    let _oneRule = [];
    for (var j in rules[i]) {
      if(typeof rules[i][j][0] == 'boolean' && j != 'required') {
        _oneRule.push({
          type: j,
          message: rules[i][j][1],
          trigger: "blur"
        });
      } else {
        _oneRule.push({
          [j]: rules[i][j][0],
          message: rules[i][j][1],
          trigger: "blur"
        });
      }

    }
    res[i] = _oneRule;
  }
  return res;
}
let timeid = null;
const debounce = (func, wait = 0) => {
  debugger
  if (typeof func !== "function") {
    throw new TypeError("need a function arguments");
  }
  let result;

  return function () {
    let context = this;
    let args = arguments;

    if (timeid) {
      clearTimeout(timeid);
    }
    timeid = setTimeout(function () {
      result = func.apply(context, args);
    }, wait);

    return result;
  };
};
//导出
const getHeadersFileName = (data) => {
  const contentDisposition = data?.headers['content-disposition'] || ''
  const prefix = 'attachment;filename='
  const prefix1 = 'attachment; filename='
  const prefix2 = 'attachment;filename*='
  const prefix3 = "attachment;filename*=utf-8'zh_cn'"
  let fileName = contentDisposition
  if (contentDisposition.indexOf(prefix) === 0) {
    fileName = decodeURI(contentDisposition.slice(prefix.length))
  }
  // 兼容不同格式
  if (contentDisposition.indexOf(prefix1) === 0) {
    fileName = decodeURI(contentDisposition.slice(prefix1.length))
  }
  if (contentDisposition.indexOf(prefix2) === 0) {
    fileName = decodeURI(contentDisposition.slice(prefix2.length))
  }
  if (contentDisposition.indexOf(prefix3) === 0) {
    fileName = decodeURI(contentDisposition.slice(prefix3.length))
  }

  return fileName
}
const download = (data) => {
  const { fileName, blob } = data;

  if (!blob) {
    return;
  }

  if (blob?.type === "application/json") {
    const reader = new FileReader();
    reader.readAsText(blob, "utf-8");
    reader.onload = function () {
      const readerRes = reader.result;
      const resObj = JSON.parse(readerRes);
      Vue.prototype.$toast({
        content: resObj.msg,
        type: "error",
      });
    };

    return;
  }
  const a = document.createElement("a");
  a.href = URL.createObjectURL(blob);
  a.style.display = "none";
  a.download = fileName;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
};


const formateTime = (date, fmt = "Y-m-d") => {
  if (!date) {
    return;
  }
  let ret;
  // YYYY/mm/dd HH:MM:SS
  const opt = {
    "Y+": date.getFullYear().toString(),
    "m+": (date.getMonth() + 1).toString(),
    "d+": date.getDate().toString(),
    "H+": date.getHours().toString(),
    "M+": date.getMinutes().toString(),
    "S+": date.getSeconds().toString(),
  };
  for (let k in opt) {
    ret = new RegExp("(" + k + ")").exec(fmt);
    if (ret) {
      fmt = fmt.replace(
        ret[1],
        ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, "0")
      );
    }
  }
  return fmt;
};

/**
 * 时间戳转日期显示
 * @param data: { formatString: <"YYYY-mm-dd"/"HH:MM:SS">, value: "时间戳" }
 * @returns String
 */
const timeNumberToDate = (data) => {
  const { formatString, value } = data;
  if (formatString && value) {
    const date = new Date(Number(value));
    if (isNaN(date.getTime())) {
      return value;
    } else {
      return formateTime(date, formatString);
    }
  } else {
    return value;
  }
};

/**
 * 时间字符串转日期显示
 * @param data data: { formatString: <"YYYY-mm-dd"/"HH:MM:SS">, value: "yyyy-mm-dd hh:mm:ss" }
 * @returns String
 */
const timeStringToDate = (data) => {
  const { formatString, value } = data;
  if (formatString && value) {
    const date = new Date(value);
    if (isNaN(date.getTime())) {
      return value;
    } else {
      return formateTime(date, formatString);
    }
  } else {
    return value;
  }
};

// 在数组中增加 theCodeName 字段
const addCodeNameKeyInList = (args) => {
  const { firstKey, secondKey, thirdKey, fourthKey, list } = args;
  if (list) {
    return list.map((item) => {
      const itemData = { ...item, theCodeName: "" };
      if (
        itemData[firstKey] &&
        itemData[secondKey] &&
        itemData[thirdKey] &&
        itemData[fourthKey]
      ) {
        // firstKey-secondKey-thirdKey-fourthKey
        itemData.theCodeName = `${itemData[firstKey]}-${itemData[secondKey]}-${itemData[thirdKey]}-${itemData[fourthKey]}`;
      } else if (
        itemData[firstKey] &&
        itemData[secondKey] &&
        itemData[thirdKey]
      ) {
        // firstKey-secondKey-thirdKey
        itemData.theCodeName = `${itemData[firstKey]}-${itemData[secondKey]}-${itemData[thirdKey]}`;
      } else if (itemData[firstKey] && itemData[secondKey]) {
        // firstKey-secondKey
        itemData.theCodeName = `${itemData[firstKey]}-${itemData[secondKey]}`;
      } else if (item[firstKey]) {
        // firstKey
        itemData.theCodeName = itemData[firstKey];
      } else if (itemData[secondKey]) {
        // secondKey
        itemData.theCodeName = itemData[secondKey];
      }
      return itemData;
    });
  } else {
    return [];
  }
};
export {
    newDate,
    formatDate,
    isNumber,
    isEmpty,
    isPainObject,
    formatRules,
    debounce,
    getHeadersFileName,
    download,
    timeNumberToDate,
    timeStringToDate,
    addCodeNameKeyInList
}
