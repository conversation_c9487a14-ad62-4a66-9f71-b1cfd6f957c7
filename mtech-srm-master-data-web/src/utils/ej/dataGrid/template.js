import Vue from 'vue'

/**
 *
 * @param {ComponentOptions} component
 * @param {object} props
 * @returns
 */
export function createTpl({ component, props }) {
  return function () {
    return {
      template: Vue.extend({
        components: {
          comp: component
        },
        data() {
          return {
            props
          }
        },
        template: `<component is="comp" :data="data" v-bind="props" />`
      })
    }
  }
}
