// import { i18n } from '@/main.js'

// 表格编辑 的 RequestType
export const RequestType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  sorting: 'sorting',
  refresh: 'refresh'
}

// 表格编辑 的 action
export const ActionType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  edit: 'edit'
}

// 行编辑触发的 change 事件类型
export const ComponentChangeType = {
  code: 'code', // 例：下拉框选择“供应商”，供应商编码（code)要被修改
  link: 'link' // 例：计划组：根据物料+工厂+计划组的业务组类型 code 带出
}

// 新增行固定数据
export const NewRowData = {
  thePrimaryKey: 'add' + Math.random().toString(36).substring(3, 8),
  companyCode: null, // 公司编号
  companyName: null, // 	公司名称
  reconciliationPerson: null, // 对账人
  reconciliationPersonCode: null, // 对账人代码
  reconciliationStatus: 0, // 允许本月对账 0-不允许；1-允许
  supplierCode: null, // 供应商/客户编号
  supplierName: null // 	供应商/客户名称
}

// 表格 EditSettings
export const EditSettings = {
  allowEditing: true,
  allowAdding: true,
  allowDeleting: true,
  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Top'
} // 编辑设置

// 表格 column 的主数据选择 searchOptions 的配置 默认配置
// searchable: true, // 默认 可搜索
// 可多选字段：行政公司，业务公司，工厂，物料，状态，供应商，客户，工作中心，库存地点
export const MasterDataSelect = {
  // 时间区间选择
  timeRange: {
    elementType: 'date-range',
    operator: 'between',
    serializeValue: (e) => {
      let obj = e.map((x) => Number(new Date(x.toString())))
      obj[1] = obj[1] + Number(86400000 - 1440000)

      //自定义搜索值，规则
      return obj
    }
  },
  // 日期范围选择 00:00:00 - 23:59:59
  dateRange: {
    elementType: 'date-range',
    operator: 'between',
    serializeValue: (e) => {
      //自定义搜索值，规则
      return e.map((x, i) => {
        if (i === 1) {
          return Number(new Date(x.toString().replace('00:00:00', '23:59:59')))
        }
        return Number(new Date(x.toString()))
      })
    }
  },
  // 单个时间的值：时间戳
  timeStamp: {
    elementType: 'date',
    serializeValue: (e) => {
      //自定义搜索值，规则
      return Number(new Date(e.toString()))
    }
  },
  // 单个时间的值：非时间戳
  timeOnly: {
    elementType: 'datetime',
    dateFormat: 'YYYY-mm-dd HH:MM:SS'
  },
  // 单位
  unit: {
    elementType: 'remote-autocomplete',
    fields: { text: 'unitName', value: 'unitCode' },
    url: `/masterDataManagement/tenant/unit/paged-query?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`,
    searchFields: ['id', 'unitCode', 'unitDescription', 'unitEnglishName', 'unitName', 'unitSymbol']
  },
  // 货币
  money: {
    elementType: 'remote-autocomplete',
    fields: { text: 'currencyName', value: 'currencyCode' },
    url: '/masterDataManagement/tenant/currency/paged-query',
    searchFields: [
      'id',
      'currencyCode',
      'currencyEnglishName',
      'currencyName',
      'numericCode',
      'statusDescription'
    ]
  },
  // 币种
  moneyType: {
    elementType: 'remote-autocomplete',
    renameField: 'currencyId',
    fields: { text: 'currencyName', value: 'id' },
    url: '/masterDataManagement/tenant/currency/paged-query',
    searchFields: [
      'id',
      'currencyCode',
      'currencyEnglishName',
      'currencyName',
      'numericCode',
      'statusDescription'
    ]
  },
  // 税率
  taxRate: {
    elementType: 'remote-autocomplete',
    fields: { text: 'taxTypeName', value: 'taxTypeCode' }, // taxTypeCode taxRate
    operator: 'equal',
    url: `/masterDataManagement/tenant/tax-item/paged-query?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`,
    searchFields: [
      'id',
      'taxItemCode',
      'taxItemDescription',
      'taxItemName',
      'taxTypeCode',
      'taxTypeName',
      'countryName'
    ]
  },
  // 税率
  saleTaxRate: {
    elementType: 'remote-autocomplete',
    fields: { text: 'taxTypeName', value: 'taxRate' }, // taxTypeCode taxRate
    operator: 'equal',
    url: `/masterDataManagement/tenant/tax-item/paged-query?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`,
    searchFields: [
      'id',
      'taxItemCode',
      'taxItemDescription',
      'taxItemName',
      'taxTypeCode',
      'taxTypeName',
      'countryName'
    ]
  },
  // 成本中心
  costCenter: {
    elementType: 'remote-autocomplete',
    fields: { text: 'costCenterName', value: 'costCenterCode' },
    url: `/masterDataManagement/tenant/cost-center/paged-query?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`,
    searchFields: ['id', 'costCenterCode', 'costCenterDesc', 'costCenterName']
  },
  // 利润中心
  profitCenter: {
    elementType: 'remote-autocomplete',
    fields: { text: 'profitCenterName', value: 'profitCenterCode' },
    url: '/masterDataManagement/tenant/profit-center/paged-query',
    searchFields: ['id', 'profitCenterCode', 'profitCenterDesc', 'profitCenterName']
  },
  // 行政公司
  administrativeCompany: {
    elementType: 'remote-autocomplete',
    fields: { text: 'orgName', value: 'orgCode' },
    params: {
      orgType: 'ORG001ADM',
      organizationLevelCodes: ['ORG01', 'ORG02']
    },
    multiple: true,
    operator: 'in',
    url: '/masterDataManagement/tenant/organization/specified-level-paged-query'
  },
  // 业务公司
  businessCompany: {
    elementType: 'remote-autocomplete',
    type: 'select', // 下拉选择
    selectType: 'administrativeCompany', // 业务公司
    fields: { text: 'orgName', value: 'orgCode' },
    params: {
      organizationLevelCodes: ['ORG01', 'ORG02']
    },
    multiple: true,
    operator: 'in',
    url: '/masterDataManagement/tenant/organization/specified-level-paged-query'
  },
  // 部门
  department: {
    elementType: 'remote-autocomplete',
    fields: { text: 'departmentName', value: 'departmentCode' },
    url: '/masterDataManagement/tenant/department/paged-query',
    searchFields: [
      'id',
      'departmentCode',
      'departmentDescription',
      'departmentName',
      'organizationId'
    ]
  },
  // 岗位
  job: {
    elementType: 'remote-autocomplete',
    fields: { text: 'stationName', value: 'stationCode' },
    url: '/masterDataManagement/tenant/station/paged-query',
    searchFields: ['id', 'stationCode', 'stationDescription', 'stationName']
  },
  // 员工
  staff: {
    elementType: 'remote-autocomplete',
    fields: { text: 'employeeName', value: 'employeeCode' },
    url: '/masterDataManagement/tenant/employee/paged-query',
    searchFields: ['id', 'employeeCode', 'employeeDescription', 'employeeName', 'email', 'phoneNum']
  },
  // 工厂
  factoryAddress: {
    elementType: 'remote-autocomplete',
    fields: { text: 'siteName', value: 'siteCode' },
    multiple: true,
    operator: 'in',
    url: `/masterDataManagement/tenant/site/paged-query?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`,
    searchFields: ['id', 'siteCode', 'siteDescription', 'siteGroupId', 'siteName', 'siteTypeName']
  },
  // 工厂 供方
  factorySupplierAddress: {
    elementType: 'remote-autocomplete',
    fields: { text: 'siteName', value: 'siteCode' },
    multiple: true,
    operator: 'in',
    url: `masterDataManagement/auth/site/auth-fuzzy?BU_CODE=${localStorage.getItem('currentBu')}`,
    paramsKey: 'fuzzyParam',
    recordsPosition: 'data'
  },
  // 分厂 （交货计划） 供方
  subSiteCodeSupplier: {
    elementType: 'remote-autocomplete',
    fields: { text: 'subSiteName', value: 'subSiteCode' },
    multiple: true,
    operator: 'in',
    url: '/srm-purchase-execute/tenant/supplierJitInfo/condition',
    searchFields: ['subSiteCode']
  },
  // 分厂库存地点 （交货计划） 供方
  subSiteAddressSupplier: {
    elementType: 'remote-autocomplete',
    fields: { text: 'subSiteAddress', value: 'subSiteAddressCode' },
    multiple: true,
    operator: 'in',
    url: '/srm-purchase-execute/tenant/supplierJitInfo/condition',
    searchFields: ['subSiteAddress']
  },
  // 分厂 （jit计划） 采
  subSiteCodeBuyer: {
    elementType: 'remote-autocomplete',
    fields: { text: 'subSiteName', value: 'subSiteCode' },
    multiple: true,
    operator: 'in',
    url: '/srm-purchase-execute/tenant/buyerJitInfo/condition',
    searchFields: ['subSiteCode']
  },
  // 分厂库存地点 （jit计划） 采方
  subSiteAddressBuyer: {
    elementType: 'remote-autocomplete',
    fields: { text: 'subSiteAddress', value: 'subSiteAddressCode' },
    multiple: true,
    operator: 'in',
    url: '/srm-purchase-execute/tenant/buyerJitInfo/condition',
    searchFields: ['subSiteAddress']
  },
  // 关联采购订单 （交货计划） 供方
  orderCodeSupplier: {
    elementType: 'remote-autocomplete',
    fields: { text: 'saleOrder', value: 'saleOrder' },
    multiple: true,
    operator: 'in',
    url: '/srm-purchase-execute/tenant/supplierJitInfo/condition',
    searchFields: ['saleOrder']
  },
  // 物料 供方
  itemSupplier: {
    elementType: 'remote-autocomplete',
    fields: { text: 'itemName', value: 'itemCode' },
    multiple: true,
    operator: 'likeright',
    url: `masterDataManagement/tenant/item/paged-auth?BU_CODE=${localStorage.getItem('currentBu')}`,
    searchFields: ['itemCode', 'itemName']
  },
  // 公司 供方
  companySupplier: {
    elementType: 'remote-autocomplete',
    fields: { text: 'orgName', value: 'orgCode' },
    multiple: true,
    operator: 'in',
    url: '/masterDataManagement/auth/company/auth-fuzzy',
    paramsKey: 'fuzzyParam',
    recordsPosition: 'data'
  },
  // VMI 仓
  vmiWarehouse: {
    elementType: 'remote-autocomplete',
    // selectType: 'vmiWarehouse', // Vmi仓
    fields: { text: 'vmiWarehouseName', value: 'vmiWarehouseCode' },
    multiple: true,
    operator: 'in',
    url: '/srm-purchase-execute/tenant/vmi_warehouse_supplier_rel/listByQueryBuilderForSupplier',
    searchFields: ['base.vmiWarehouseCode', 'base.vmiWarehouseName'],
    recordsPosition: 'data'
  },
  // VMI 仓
  vmiWarehouseSupplier: {
    elementType: 'remote-autocomplete',
    // selectType: 'vmiWarehouse', // Vmi仓
    fields: { text: 'vmiWarehouseName', value: 'vmiWarehouseCode' },
    multiple: true,
    operator: 'in',
    url: '/srm-purchase-execute/tenant/vmi_warehouse_supplier_rel/listByQueryBuilderForSupplier',
    searchFields: ['base.vmiWarehouseCode', 'base.vmiWarehouseName'],
    recordsPosition: 'data'
  },
  // 钢材VMI 仓
  vmiWarehouseSteel: {
    elementType: 'remote-autocomplete',
    // selectType: 'vmiWarehouse', // Vmi仓
    fields: { text: 'vmiWarehouseName', value: 'vmiWarehouseCode' },
    multiple: true,
    operator: 'in',
    url: '/srm-purchase-execute/tenant/vmiSteel/vmi-receive-order/supplier-page-query',
    searchFields: ['vmiWarehouseCode', 'vmiWarehouseName']
  },
  // 库存地点
  stockAddress: {
    elementType: 'remote-autocomplete',
    fields: { text: 'locationName', value: 'locationCode' },
    multiple: true,
    operator: 'in',
    url: '/masterDataManagement/tenant/location/paged-query',
    searchFields: ['id', 'locationCode', 'locationDescription', 'locationName']
  },
  stockAddressName: {
    elementType: 'remote-autocomplete',
    fields: { text: 'locationCode', value: 'locationName' },
    multiple: true,
    operator: 'likeright',
    url: '/masterDataManagement/tenant/location/paged-query',
    searchFields: ['locationCode', 'locationName']
  },
  // 库存地点 供方
  stockSupplierAddressName: {
    elementType: 'remote-autocomplete',
    multiple: true,
    operator: 'likeright',
    fields: { text: 'locationName', value: 'locationCode' },
    url: '/masterDataManagement/auth/location/auth-fuzzy',
    paramsKey: 'fuzzyParam',
    recordsPosition: 'data'
  },
  // 库存地点 供方 以name为查询条件
  stockSupplierAddressOrName: {
    elementType: 'remote-autocomplete',
    multiple: true,
    operator: 'likeright',
    fields: { text: 'locationName', value: 'locationName' },
    url: '/masterDataManagement/auth/location/auth-fuzzy',
    paramsKey: 'fuzzyParam',
    recordsPosition: 'data'
  },
  // 采购组 供方
  businessCodeName: {
    elementType: 'remote-autocomplete',
    multiple: true,
    operator: 'in',
    url: `/masterDataManagement/auth/business-group/auth-fuzzy?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}?BU_CODE=${localStorage.getItem('currentBu')}`,
    fields: { text: 'groupName', value: 'groupCode' },
    paramsKey: 'fuzzyParam',
    recordsPosition: 'data'
  },
  // 工作中心
  workCenter: {
    elementType: 'remote-autocomplete',
    fields: { text: 'workCenterName', value: 'workCenterCode' },
    multiple: true,
    operator: 'in',
    url: '/masterDataManagement/tenant/work-center/paged-query',
    searchFields: [
      'id',
      'workCenterCode',
      'workCenterDescription',
      'workCenterName',
      'siteCode',
      'siteName'
    ]
  },
  // 业务组
  businessGroup: {
    elementType: 'remote-autocomplete',
    fields: { text: 'groupName', value: 'groupCode' },
    url: `/masterDataManagement/tenant/business-group/paged-query?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`,
    searchFields: ['groupCode', 'groupName']
  },
  // 业务组 多选
  businessGroupIn: {
    elementType: 'remote-autocomplete',
    fields: { text: 'groupName', value: 'groupCode' },
    multiple: true,
    operator: 'in',
    url: `/masterDataManagement/tenant/business-group/paged-query?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`,
    searchFields: ['groupCode', 'groupName']
  },
  // 业务组织
  businessGroupUnit: {
    elementType: 'remote-autocomplete',
    fields: { text: 'organizationName', value: 'organizationCode' },
    url: `/masterDataManagement/tenant/business-organization/paged-query?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`,
    searchFields: [
      'id',
      'organizationCode',
      'organizationDescription',
      'organizationName',
      'organizationTypeName'
    ]
  },
  // 国家
  country: {
    elementType: 'remote-autocomplete',
    fields: { text: 'shortName', value: 'countryCode' },
    url: '/masterDataManagement/tenant/country/paged-query',
    searchFields: [
      'id',
      'alpha2Code',
      'countryCode',
      'englishShortName',
      'numericCode',
      'shortName',
      'statusDescription'
    ]
  },
  // 收货地址
  receivingAddress: {
    elementType: 'remote-autocomplete',
    // selectType: 'receivingAddress', // 收货地址
    fields: { text: 'title', value: 'TODO' },
    url: '',
    searchFields: []
  },
  // 供应商
  supplier: {
    elementType: 'remote-autocomplete',
    fields: { text: 'supplierName', value: 'supplierCode' },
    multiple: true,
    operator: 'in',
    url: '/masterDataManagement/tenant/supplier/paged-query',
    searchFields: ['id', 'supplierCode', 'supplierDescription', 'supplierName']
  },
  // 供应商
  supplierAll: {
    elementType: 'remote-autocomplete',
    fields: { text: 'supplierName', value: 'supplierCode' },
    multiple: true,
    operator: 'in',
    url: '/masterDataManagement/tenant/supplier/paged-query',
    searchFields: ['id', 'supplierCode', 'supplierDescription', 'supplierName']
  },
  // 供应商 供方
  supplierSu: {
    elementType: 'remote-autocomplete',
    fields: { text: 'supplierName', value: 'supplierCode' },
    multiple: true,
    operator: 'in',
    url: '/srm-purchase-execute/tenant/vmi_warehouse_supplier_rel/listByQueryBuilderForSupplier',
    searchFields: ['supplierCode', 'supplierName'],
    recordsPosition: 'data'
  },
  // 钢材供应商 供方
  supplierVMI: {
    elementType: 'remote-autocomplete',
    fields: { text: 'supplierName', value: 'supplierCode' },
    multiple: true,
    operator: 'in',
    url: '/srm-purchase-execute/tenant/vmiSteel/vmi_warehouse_supplier_rel/criteria-query-for-supplier',
    searchFields: ['supplierCode', 'supplierName'],
    recordsPosition: 'data'
  },
  // 钣金供应商 供方
  SteelSupplier: {
    elementType: 'remote-autocomplete',
    fields: { text: 'processorName', value: 'processorCode' },
    multiple: true,
    operator: 'in',
    url: '/srm-purchase-execute/tenant/vmiSteel/vmi-pickup-order/supplier-page-query',
    searchFields: ['processorCode', 'processorName']
  },
  // 原材料供应商 供方
  materialSupplier: {
    elementType: 'remote-autocomplete',
    fields: { text: 'materialSupplierName', value: 'materialSupplierCode' },
    multiple: true,
    operator: 'in',
    url: '/srm-purchase-execute/tenant/supplierOrderDelivery',
    searchFields: ['materialSupplierCode', 'materialSupplierName']
  },
  // 第三方物流 供方
  thirdPartSupplier: {
    elementType: 'remote-autocomplete',
    type: 'select', // 下拉选择
    selectType: 'supplier', // 供应商
    fields: { text: 'thirdPartyLogisticsName', value: 'thirdPartyLogisticsCode' },
    multiple: true,
    operator: 'in',
    url: '/srm-purchase-execute/tenant/vmi_warehouse_supplier_rel/listByQueryBuilderForSupplier',
    searchFields: ['thirdPartyLogisticsCode', 'thirdPartyLogisticsName'],
    recordsPosition: 'data'
  },
  // 第三方物流 供应商
  supplierThird: {
    elementType: 'remote-autocomplete',
    fields: { text: 'supplierName', value: 'supplierCode' },
    multiple: true,
    operator: 'in',
    url: '/srm-purchase-execute/tenant/vmi_warehouse_supplier_rel/listByQueryBuilderForLogistic',
    searchFields: ['supplierCode', 'supplierName'],
    recordsPosition: 'data'
  },
  // 第三方物流 VMI仓
  vmiThird: {
    elementType: 'remote-autocomplete',
    fields: { text: 'vmiWarehouseName', value: 'vmiWarehouseCode' },
    multiple: true,
    operator: 'in',
    url: 'srm-purchase-execute/tenant/vmi_warehouse_supplier_rel/listByQueryBuilderForLogistic',
    searchFields: ['base.vmiWarehouseCode', 'base.vmiWarehouseName'],
    recordsPosition: 'data'
  },
  // sku --- 待完善
  sku: {
    elementType: 'remote-autocomplete',
    // type: 'dialog', // 弹框选择
    // dialogType: 'sku', // sku
    fields: { text: 'name', value: 'barCode' },
    operator: 'in',
    url: '/masterDataManagement/tenant/sku/page-query',
    searchFields: ['name', 'barCode']
  },
  // 物料
  material: {
    elementType: 'remote-autocomplete',
    multiple: true,
    operator: 'likeright',
    fields: { text: 'itemName', value: 'itemCode' },
    url: `masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`,
    searchFields: ['itemCode', 'itemName']
  },
  // 货源关系 --- 待完善
  supply: {
    elementType: 'remote-autocomplete',
    // type: 'dialog', // 弹框选择
    // dialogType: 'supply', // 货源关系
    fields: { text: 'supplierName', value: 'supplierCode' },
    operator: 'in',
    url: '/masterDataManagement/tenant/supply-source-list/paged-query',
    searchFields: ['supplierCode', 'supplierName']
  },
  // 品类 --- 带完善
  category: {
    elementType: 'remote-autocomplete',
    // type: 'dialog', // 弹框选择
    // dialogType: 'category', // 品类
    fields: { text: 'categoryName', value: 'categoryCode' },
    operator: 'in',
    url: '/masterDataManagement/tenant/category/paged-query',
    supplierSearchFields: ['categoryCode', 'categoryName']
  },
  // 品类 建议用这个
  categoryName: {
    elementType: 'remote-autocomplete',
    renameField: 'categoryCode',
    fields: { text: 'categoryName', value: 'categoryCode' },
    multiple: true,
    operator: 'in',
    url: '/masterDataManagement/tenant/category/paged-query',
    supplierSearchFields: ['categoryCode', 'categoryName']
  },
  // 字典 OrderType 订单类型 --- 待完善
  dictOrderType: {
    elementType: 'remote-autocomplete',
    multiple: true,
    fields: { text: 'itemName', value: 'itemCode' },
    url: '/masterDataManagement/tenant/dict-item/dict-code',
    params: {
      dictCode: 'OrderType' // 字典类型编码
    },
    recordsPosition: 'data'
  },
  // 字典 供方 OrderType 订单类型 --- 带完善
  dictOrderSupplierType: {
    elementType: 'remote-autocomplete',
    multiple: true,
    fields: { text: 'itemName', value: 'itemCode' },
    url: '/masterDataManagement/auth/dict-item/auth-find',
    params: {
      dictCode: 'OrderType' // 字典类型编码
    },
    recordsPosition: 'data'
  },
  // 字典 payMethod 付款方式 --- 带完善
  dictPaymentMode: {
    elementType: 'remote-autocomplete',
    multiple: true,
    fields: { text: 'itemName', value: 'itemCode' },
    url: '/masterDataManagement/tenant/dict-item/dict-code',
    params: {
      dictCode: 'payMethod' // 字典类型编码
    },
    recordsPosition: 'data'
  },
  // 送货单列表 明细下拉
  deliverSupplierVmiWarehouseCode: {
    elementType: 'remote-autocomplete',
    fields: { text: 'vmiWarehouseName', value: 'vmiWarehouseCode' },
    multiple: true,
    operator: 'in',
    url: '/srm-purchase-execute/tenant/supplierOrderDeliveryItem/page',
    searchFields: ['vmiWarehouseCode']
  },
  deliverSupplierThirdTenantCode: {
    elementType: 'remote-autocomplete',
    fields: { text: 'thirdTenantName', value: 'thirdTenantCode' },
    multiple: true,
    operator: 'in',
    url: '/srm-purchase-execute/tenant/supplierOrderDeliveryItem/page',
    searchFields: ['thirdTenantCode']
  },
  deliver: {
    elementType: 'remote-autocomplete',
    fields: { text: 'vmiWarehouseName', value: 'vmiWarehouseCode' },
    multiple: true,
    operator: 'in',
    url: '/srm-purchase-execute/tenant/buyerOrderDelivery/query/item/page',
    searchFields: ['vmiWarehouseCode']
  },
  deliverThirdTenantCode: {
    elementType: 'remote-autocomplete',
    fields: { text: 'thirdTenantName', value: 'thirdTenantCode' },
    multiple: true,
    operator: 'in',
    url: '/srm-purchase-execute/tenant/buyerOrderDelivery/query/item/page?conditionType=thirdTenantCode',
    searchFields: ['thirdTenantCode']
  }
}
