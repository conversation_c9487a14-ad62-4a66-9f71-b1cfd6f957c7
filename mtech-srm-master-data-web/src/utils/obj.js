/**
 * @param {Object} target
 * @param {String} key
 * @returns
 */
export const getValueByPath = function (target, key) {
  if (typeof target !== 'object' || typeof key !== 'string') {
    return
  }
  let result = target
  for (const k of key.split('.')) {
    result = result[k]
    if (result === undefined || result === null) {
      return result
    }
  }
  return result
}

/**
 *
 * @param {Object} target
 * @param {String} key
 * @param {*} value
 * @returns
 */
export const setValueByPath = function (target, key, value) {
  if (typeof target !== 'object' || typeof key !== 'string') {
    return
  }
  let result = target
  const keys = key.split('.')
  for (let i = 0; i < keys.length; i++) {
    if (result[keys[i]] === undefined || result[keys[i]] === null) {
      result[keys[i]] = {}
    }
    if (i === keys.length - 1) {
      result[keys[i]] = value
    } else {
      result = result[keys[i]]
    }
  }
  return target
}
