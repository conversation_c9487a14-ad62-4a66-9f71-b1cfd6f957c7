import { i18n } from '@/main.js'

const urlPath = {
  role: '/iam',
  commonBase: '/masterDataManagement/common',
  tenantBase: '/masterDataManagement/tenant'
}

const PROXY_BASE = location.href.includes('platform') ? urlPath.commonBase : urlPath.tenantBase

console.log('PROXY_BASE', PROXY_BASE)

export default {
  PROXY_BASE: PROXY_BASE,
  applicationId: 0,
  activeStatusId: 1,
  statusList: [
    // {
    //   label: "草稿",
    //   value: "0"
    // },
    {
      label: i18n.t('激活'),
      value: 1
    },
    // {
    //   label: "分发",
    //   value: "2"
    // },
    {
      label: i18n.t('失效'),
      value: 3
    }
  ]
}
