/*
 * @Author: your name
 * @Date: 2021-10-21 10:06:19
 * @LastEditTime: 2022-03-24 09:53:00
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-master-data-web\vue.config.js
 */
'use strict'
const path = require('path')
const DictionaryPlugin = require('@digis/dictionary-plugin')

function resolve(dir) {
  return path.join(__dirname, dir)
}
const name = 'mtech-srm-master-data-web'

const port = process.env.port || process.env.npm_config_port || 8083 // dev port

// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
  publicPath: './',
  outputDir: 'dist',
  assetsDir: 'static',
  lintOnSave: process.env.NODE_ENV === 'development',
  productionSourceMap: false,
  devServer: {
    port: port,
    open: true,
    overlay: {
      warnings: false,
      errors: true
    },
    clientLogLevel: 'info',
    proxy: {
      '/api': {
        // target: "http://srm.dev.qeweb.com",
        // target: "http://srm.test.qeweb.com",
        target: 'http://srm-sit-gw.eads.tcl.com',
        // target: 'http://srm-sit-gw.eads.tcl.com',
        changeOrigin: true,
        pathRewrite: {
          // "^/api/masterDataManagement": ""
        }
      }
      // "/api": {
      //   target: "http://10.14.242.158:9113", //huanghongchao
      //   // target: "http://10.14.241.40:9111", // guozhanqi
      //   // target: "http://10.14.242.35:8014", // zhaochang
      //   changeOrigin: true,
      //   pathRewrite: {
      //     "^/api/masterDataManagement": "",
      //   },
      // },
      // "/api/tree": {
      //   // target: "http://10.14.243.139:8014",
      //   target: "http://srm.dev.qeweb.com/api/masterDataManagement",
      //   changeOrigin: true,
      //   pathRewrite: {
      //     "^/api/tree": ""
      //   }
      // },
    }
  },
  configureWebpack: {
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    name: name,
    resolve: {
      alias: {
        '@': resolve('src'),
        vue$: 'vue/dist/vue.esm.js'
      }
    },
    plugins: [
      new DictionaryPlugin() // new DictionaryPlugin({NODE_ENV:'production'}) 执行的插件的环境 默认为'production'
    ]
  },
  chainWebpack(config) {
    // fix HMR un-useful
    config.resolve.symlinks(true)

    // it can improve the speed of the first screen, it is recommended to turn on preload
    config.plugin('preload').tap(() => [
      {
        rel: 'preload',
        // to ignore runtime.js
        // https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/cli-service/lib/config/app.js#L171
        fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
        include: 'initial'
      }
    ])

    // when there are many pages, it will cause too many meaningless requests
    config.plugins.delete('prefetch')

    // set svg-sprite-loader
    config.module.rule('svg').exclude.add(resolve('src/assets/icons')).end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()
  },
  css: {
    requireModuleExtension: true, // 是否开启支持‘foo.module.css’样式
    extract: true, // 是否使用css分离插件 ExtractTextPlugin 抽离css
    sourceMap: process.env.NODE_ENV !== 'production' // 是否在构建样式地图，false将提高构建速度
  },
  pluginOptions: {
    'style-resources-loader': {
      preProcessor: 'scss',
      patterns: [path.resolve(__dirname, 'src/themes/_mtechUI.scss')]
    }
  }
}
