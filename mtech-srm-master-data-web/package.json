{"name": "mtech-srm-mdm-web", "version": "0.1.0", "private": true, "scripts": {"build": "vue-cli-service build", "lint": "vue-cli-service lint", "build:report": "vue-cli-service build --report --report-json", "buildAll": "npm-run-all theme build --continue-on-error", "dev": "vue-cli-service serve", "serve": "vue-cli-service serve", "serveAll": "npm-run-all theme serve --continue-on-error", "dictionary": "node build/dict.js", "upgrade:mtech-ui": "npx update-by-scope -t latest @mtech-ui npm install", "prepare": "husky install", "lint-staged": "lint-staged"}, "lint-staged": {"src/**/*.{js,jsx,vue,ts,tsx}": ["eslint --fix"]}, "dependencies": {"@babel/runtime-corejs3": "^7.7.2", "@digis/component-props-state": "^1.2.6", "@digis/dictionary-plugin": "^1.1.11", "@digis/internationalization": "^1.1.17", "@digis/multilingual-input": "^1.1.28", "@mtech-common/http": "^1.0.5", "@mtech-common/utils": "^1.0.0", "@mtech-sso/single-sign-on": "^1.2.5-tcl.7", "@mtech-ui/base": "^1.10.10", "@mtech-ui/button": "^1.11.14", "@mtech-ui/checkbox": "^1.11.14", "@mtech-ui/col": "^1.11.14", "@mtech-ui/date-picker": "^1.11.14", "@mtech-ui/date-range-picker": "^1.11.14", "@mtech-ui/date-time-picker": "^1.11.14", "@mtech-ui/dialog": "^1.11.14", "@mtech-ui/drop-down-tree": "^1.11.14", "@mtech-ui/form": "^1.11.14", "@mtech-ui/form-item": "^1.11.14", "@mtech-ui/input": "^1.11.14", "@mtech-ui/input-number": "^1.11.14", "@mtech-ui/listbox": "^1.11.14", "@mtech-ui/loading": "^1.11.14", "@mtech-ui/mtech-ui": "^1.11.14", "@mtech-ui/multi-select": "^1.11.27", "@mtech-ui/query-builder": "^1.11.27", "@mtech-ui/row": "^1.11.14", "@mtech-ui/select": "^1.11.27", "@mtech-ui/switch": "^1.11.14", "@mtech-ui/tabs": "^1.11.22", "@mtech-ui/time-picker": "^1.11.14", "@mtech-ui/tooltip": "^1.11.14", "@mtech/common-loading": "^1.0.0", "@mtech/common-permission": "^1.2.4", "@mtech/common-tree-view": "^0.1.41", "@mtech/dialog-transfer": "^1.1.4", "axios": "^0.21.1", "core-js": "^3.6.5", "css-vars-ponyfill": "^2.4.7", "i": "^0.3.6", "js-cookie": "^2.2.1", "lodash": "^4.17.21", "npm": "^7.20.0", "sortablejs": "^1.15.0", "vue": "^2.6.11", "vue-router": "^3.2.0", "vuedraggable": "^2.24.3", "vuex": "^3.4.0", "vuex-persistedstate": "^4.0.0", "vxe-table": "^3.6.13", "xe-utils": "^3.5.11"}, "devDependencies": {"@commitlint/cli": "^17.0.1", "@commitlint/config-conventional": "^17.0.0", "@digis-vue-cli-preset/vue-cli-plugin-performance": "^1.1.5", "@mtech-micro-frontend/vue-cli-plugin-micro": "^1.0.0", "@mtech/eslint-config-vue": "^0.0.4", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "^4.5.12", "@vue/cli-plugin-vuex": "^4.5.12", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "chalk": "^2.4.2", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "husky": "^8.0.3", "lint-staged": "^13.1.4", "node-sass": "^4.14.1", "npm-run-all": "^4.1.5", "sass-loader": "^8.0.0", "style-resources-loader": "^1.4.1", "svg-sprite-loader": "^6.0.9", "vue-class-component": "^8.0.0-rc.1", "vue-cli-plugin-style-resources-loader": "~0.1.5", "vue-template-compiler": "^2.6.11", "webpack-fix-style-only-entries": "^0.6.1", "webpack-glob-entry": "^2.1.1"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["@mtech/eslint-config-vue"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "mtMicro": {"type": "slave"}}