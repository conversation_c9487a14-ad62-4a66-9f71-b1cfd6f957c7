const fs = require('fs')
const path = require('path')
const templatePattern = /<template>([\s\S]*?)<\/template>/
const scriptPattern = /<script>([\s\S]*?)<\/script>/
/**
 * 匹配规则（只针对vue文件进行优化）
 * 1.匹配template里面的内容
 *  1) 中文匹配规则，查找{{ $t('')}} 、中文：（）：、<!-- 中文内容 -->
 *  2) 过滤<!-- 中文内容 --> {{ $('')}}
 *  3) 包裹 {{ $t('') }}
 *  4）过滤掉外层引号
 * 2.匹配script里面的内容
 *  1) 中文匹配引号里面的内容 : ''| = ''| ? '' |+ ''
 *  2) 包裹 {{ this.$t('') }}
 *  3) 过滤掉外层引号
 */

let glossarys = []
// 1.扫描文件
scanFolder('./src/views')
// 2.筛选词条（如果该词条已存在，则不写入output.json文件）
const dictContent = fs.readFileSync('./dict.json', 'utf8')
const dictionary = JSON.parse(dictContent)?.data?.dict || {}
let newGlossays = Array.from(new Set(glossarys))
newGlossays = newGlossays.filter((value) => !dictionary[value])
// 3.写入json文件输出
const jsonData = JSON.stringify(newGlossays, null, '\t')
fs.writeFile('output.json', jsonData, (err) => {
  if (err) {
    console.log('写入失败')
    return
  }
  console.log('写入成功')
})

// 扫描文件
function scanFolder(folderPath) {
  // 读取文件
  const files = fs.readdirSync(folderPath)
  // 文件逐个读取
  files.forEach((file) => {
    const filePath = path.join(folderPath, file)
    const stats = fs.statSync(filePath)
    if (stats.isFile()) {
      const extname = path.extname(file)
      if (extname !== '.vue') return
      const fileContent = fs.readFileSync(filePath, 'utf8')
      // 替换template中的中文内容
      let { matches: templateMatches, content: templateContent } = matchTemplateChinese(fileContent)
      const updatedTemplateContent = fileContent.replace(
        templatePattern,
        `<template>${templateContent}</template>`
      )
      glossarys.push(...templateMatches)
      // 替换script中的中文内容
      let { matches: scriptMatches, content: scriptContent } =
        matchScriptChinese(updatedTemplateContent)
      const updateContent = updatedTemplateContent.replace(
        scriptPattern,
        `<script>${scriptContent}</script>`
      )
      glossarys.push(...scriptMatches)
      // 更新文件
      fs.writeFileSync(filePath, updateContent, 'utf8')
    } else if (stats.isDirectory()) {
      scanFolder(filePath)
    }
  })
}

// 匹配替换template里面的内容
function matchTemplateChinese(fileContent) {
  const templateMatch = fileContent.match(templatePattern)
  if (!templateMatch || templateMatch?.length < 2)
    return {
      matches: [],
      content: fileContent
    }

  const templateContent = templateMatch[1]
  // 中文匹配规则，查找{{ t('')}} 、中文：（）：、<!-- 中文内容 -->
  const chinesePattern =
    /t\([\s\n]*(['"`]).*\1[\s\n]*\)|<!--[\s\S]*?-->|(?<=[=?:]\s?)(["'])`?[a-zA-Z\\$\\{\\}\\/-]*[\u4e00-\u9fa5]+[（）()、，。：”%0-9a-zA-Z/]*[\\.png\\.jpg\\.xlsx]*`?\2|[（“]?[\u4e00-\u9fa5]+[（）()、，：”0-9a-zA-Z/]*[\u4e00-\u9fa5）：，。”%]+|([（“]?[\u4e00-\u9fa5][）：，。”]*)/g
  const matches = templateContent.match(chinesePattern) || []
  // 过滤<!-- 中文内容 --> {{ $('')}
  const filteredMatches = matches.filter(
    (match) =>
      !/t\([\s\n]*(['"`]).*\1[\s\n]*\)/.test(match) &&
      !/^<!--[\s\S]*?-->$/.test(match) &&
      !/(['"`])(.*?)[\\.png\\.jpg\\.xlsx]\1/g.test(match)
  )
  // 包裹 {{ $t('') }} 及 $t(')
  const wrappedMatches = filteredMatches.map((match) => {
    if (/(['"])`(.*?)`\1/g.test(match)) {
      return `"$t('${match.replace(/^(['"])`(.*?)`\1$/, '$2')}')"`
    } else if (/(['"`])(.*?)\1/g.test(match)) {
      return `"$t('${match.replace(/^[`'"](.*)[`'"]$/, '$1')}')"`
    }
    return `{{ $t('${match}') }}`
  })
  // 替换template内容
  const replacedContent = templateContent.replace(chinesePattern, (r) => {
    if (
      !/t\([\s\n]*(['"`]).*\1[\s\n]*\)/.test(r) &&
      !/^<!--[\s\S]*?-->$/.test(r) &&
      !/(['"`])(.*?)[\\.png\\.jpg\\.xlsx]\1/g.test(r)
    ) {
      return wrappedMatches.shift()
    }
    return r
  })
  let replaceQuoteMatches = filteredMatches?.map((match) => {
    return match.replace(/^[`'"](.*)[`'"]$/, '$1')
  })
  return {
    matches: replaceQuoteMatches,
    content: replacedContent
  }
}

// 匹配替换script里面的内容（针对python里面的查询作添加优化）
function matchScriptChinese(fileContent) {
  const scriptMatch = fileContent.match(scriptPattern)
  if (!scriptMatch || scriptMatch?.length < 2)
    return {
      matches: [],
      content: fileContent
    }
  const scriptContent = scriptMatch[1]
  // 中文匹配引号里面的内容 : ''| = ''| ? '' |+ '' ，不作中文的()处理，如果这类情况，则改成中文
  const chinesePattern =
    /(?<!\/\/.*)(?<=[=:+?]+\s)([`'"])([a-zA-Z0-9-']*[\u4e00-\u9fa5]+[\u4e00-\u9fa50-9a-zA-Z/-（）()：、，。~”'"&？%]*['"`])(?=(?:[^'"`]*['"`][^'"`]*['"`])*[^'"`]*$)/g
  const matches = scriptContent.match(chinesePattern) || []
  // 包裹内容
  const wrappedMatches = matches?.map((match) => `this.$t(${match})`) || []
  // 替换script内容
  const replacedContent = scriptContent.replace(chinesePattern, (r) => {
    if (
      !/\s*\$t\(['"`].*?['"`]\)\s*/.test(r) &&
      !/console\.(?:log|info|error)\(['"`].*?['"`]\)/.test(r)
    ) {
      return wrappedMatches.shift()
    }
    return r
  })
  let replaceQuoteMatches = matches?.map((match) => {
    return match.replace(/^[`'"](.*)[`'"]$/, '$1')
  })
  return {
    matches: replaceQuoteMatches,
    content: replacedContent
  }
}
