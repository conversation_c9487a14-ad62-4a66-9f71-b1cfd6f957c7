const fs = require("fs");
const path = require("path");
let file = __dirname + "/dict.json";
let filePath = "src";
let arrDict = [];

fileDisplay(filePath);
// function dedupe(array) {
//   return new Promise((reject, resolve) => {
//     reject(Array.from(new Set(array)));
//   });
// }
function dedupe(arr) {
  var arr1 = []; // 新建一个数组来存放arr中的值
  for (var i = 0, len = arr.length; i < len; i++) {
    if (arr1.indexOf(arr[i]) === -1) {
      arr1.push(arr[i]);
    }
  }
  return arr1;
}
/**
 * 读取原有文件方法
 * @param arr 需要写入的内容
 */
function createFile(arr) {
  writeF(dedupe(arr));
  // dedupe(arr).then((a) => {
  //   writeF(a);
  // });
}
/**
 * 文件写入方法
 * @param data 需要写入的内容
 */
function writeF(data) {
  // const jsonData = data;
  const jsonData = JSON.stringify(data);
  // 写入文件
  fs.writeFile(file, jsonData, (err) => {
    if (err) {
      return console.log(err);
    }
    // console.log(`字典文件地址:${file},以'<splitMark>'隔开`)
  });
}
/**
 * 文件遍历方法
 * @param filePath 需要遍历的文件路径
 */
function fileDisplay(filePath) {
  // 根据文件路径读取文件，返回文件列表
  fs.readdir(filePath, (err, files) => {
    if (err) {
      // console.warn('请使用build打包同步生成字典文件')
    } else {
      // 遍历读取到的文件列表
      files.forEach((filename) => {
        // 获取当前文件的绝对路径
        const filedir = path.join(filePath, filename);
        // 根据文件路径获取文件信息，返回一个fs.Stats对象
        fs.stat(filedir, (eror, stats) => {
          if (eror) {
            console.warn("获取文件stats失败");
          } else {
            const isFile = stats.isFile(); // 是文件
            const isDir = stats.isDirectory(); // 是文件夹
            if (isFile) {
              // console.log(filedir)
              const dictKey = fs
                .readFileSync(filedir, "utf8")
                .match(/(?<=(\.|\$)t\(("|'))(.*?)(?=("|')\))/g);
              if (dictKey != null) {
                dictKey.forEach((item) => {
                  if (item.indexOf("(") === -1 && item.indexOf(")") === -1) {
                    arrDict.push(item);
                    console.log(arrDict);
                  }
                });
                createFile(arrDict);
              }
            }
            if (isDir) {
              fileDisplay(filedir); // 递归，如果是文件夹，就继续遍历该文件夹下面的文件
            }
          }
        });
      });
    }
  });
}
