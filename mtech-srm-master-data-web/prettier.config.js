module.exports = {
  // 指定行字符串显示长度。
  printWidth: 100,
  // 在语句的末尾打印分号。
  semi: false,
  // 是否要在 VUE 文件中缩进 <script> 和 <style> 标记中的代码。
  vueIndentScriptAndStyle: false,
  // 使用单引号而不是双引号。
  singleQuote: true,
  // 在可能的多线逗号分离的语法结构中打印尾随逗号。 （例如单行阵列，例如，从未获得尾随逗号。）
  trailingComma: 'none',
  proseWrap: 'never',
  endOfLine: 'auto',
  tabWidth: 2,
  bracketSpacing: true,
  jsxSingleQuote: true,
  jsxBracketSameLine: true
}
