const ora = require('ora')
const rm = require('rimraf')
const path = require('path')
const chalk = require('chalk')
const webpack = require('webpack')
const FixStyleOnlyEntriesPlugin = require('webpack-fix-style-only-entries')
const MiniCssExtractPlugin = require('mini-css-extract-plugin')
const globEntry = require('webpack-glob-entry')

const resolve = dir => path.join(process.cwd(), dir)


const scssSourceDir = 'src/themes'
const scssTargetDir = 'public'
const scssTargetSubDir = 'themes'

const getEntry = () => {
  const entryFiles = globEntry(`${scssSourceDir}/*.scss`)
  return Object.keys(entryFiles).filter(key => !key.startsWith('_')).reduce((total, curr) => {
    total[`themes/${curr}`] = resolve(entryFiles[curr])
    return total
  }, {})
}

const getWebpackConfig = () => {

  const themeArs = require('minimist')(process.argv.slice(2), {
    boolean: ["watch"]
  });


  const webpackConfig = {
    mode: 'production',
    devtool: 'none',
    entry: getEntry(),
    output: {
      path: resolve(scssTargetDir)
    },
    module: {
      rules: [
        {
          test: /\.scss$/,
          exclude: /(node_modules|bower_components)/,
          use: [
            {
              loader: MiniCssExtractPlugin.loader
            },
            {
              loader: 'css-loader',
              options: {
                sourceMap: true,
                url: false
              }
            },
            'sass-loader'
          ]
        }
      ]
    },
    plugins: [new FixStyleOnlyEntriesPlugin(), new MiniCssExtractPlugin()],
  }

  const watchConfig = {
    // 默认false 不开启
    watch: true,
    //只有开启监听模式时，watchOptions才有意义
    watchOptions: {
      //默认为空，不监听的文件或者文件夹，支持正则匹配
      ignored: ["src/!(stylesTheme)/**/*"],
      //监听到变化发生后会等300ms再去执行，默认300ms
      aggregateTimeout: 300,
      //判断文件是否发生变化是通过不停询问系统指定文件有没有变化实现的，ms为单位下面意思是 每秒检查一次变动
      poll: 1000
    }
  }

  if (themeArs.watch) {
    return [
      {
        ...webpackConfig,
        ...watchConfig
      }
    ]
  } else {
    return [webpackConfig]
  }

}


const spinner = ora('building for themes...')
spinner.start()

rm(resolve(scssTargetDir + '/' + scssTargetSubDir), err => {
  if (err) throw err
  webpack(getWebpackConfig(), (err, stats) => {
    spinner.stop()
    if (err) throw err
    process.stdout.write(stats.toString({
      colors: true,
      modules: false,
      children: false, // If you are using ts-loader, setting this to true will make TypeScript errors show up during build.
      chunks: false,
      chunkModules: false
    }) + '\n\n')

    if (stats.hasErrors()) {
      console.log(chalk.red('  Theme build failed with errors.\n'))
      console.log(stats.toString())
      process.exit(1)
    }

    console.log(chalk.cyan('  Theme build complete.\n'))
  })
})
