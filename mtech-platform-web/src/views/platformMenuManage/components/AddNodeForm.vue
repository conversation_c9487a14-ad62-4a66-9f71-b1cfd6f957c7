<template>
  <div class="demo-block">
    <mt-dialog ref="dialog" css-class="dialog-form-flex" header="新增节点" :buttons="buttons" :open="onOpen">
      <div>   
      <mt-form
        ref="menuRuleForm"
        :model="menuRuleForm"
        :rules="rules"
        class="formClass"
      >
        <mt-form-item class="form-item" label="父节点编码" v-if="isZero">
          <mt-input
            v-model="menuRuleForm.parentCode"
            :disabled="true"
            :showClearButton="true"
            type="text"
          ></mt-input>
        </mt-form-item>
        <mt-form-item label="父节点名称" v-if="isZero">
          <mt-input v-model="menuRuleForm.parentName" :disabled="true" type="text"></mt-input>
        </mt-form-item>
        <!-- <mt-form-item prop="permissionCode" label="编码">
          <mt-input
            v-model="menuRuleForm.permissionCode"
            type="text"
            onkeyup="this.value=this.value.replace(/\s+/g,'')"
            placeholder="请输入编码"
          ></mt-input>
        </mt-form-item> -->
        <mt-form-item prop="functionType" label="功能类型">
          <mt-select
            v-model="menuRuleForm.functionType"
            :dataSource="functionTypeArrNoChild"
            :fields="{text:'itemName', value:'itemCode'}"
            :showClearButton="true"
            :change="functionTypeChange"
            placeholder="请选择功能类型"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="menuType" label="菜单类型" v-if="isWhat == 'menu'">
          <mt-select
            v-model="menuRuleForm.menuType"
            :dataSource="menuTypeArr"
            :showClearButton="true"
            placeholder="请选择菜单类型"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="permissionName" label="名称">
          <mt-multilingual-input
            v-model="menuRuleForm.permissionName"
            group-code="iam"
            type="text"
            placeholder="请输入名称"
            :fields="{text:'itemName', value:'dictId'}"
          ></mt-multilingual-input>
        </mt-form-item>
        <!-- <mt-form-item label="图标">
          <mt-input
            v-model="menuRuleForm.icon"
            type="text"
            placeholder="请输入名称"
            :fields="{text:'itemName', value:'dictId'}"
          ></mt-input>
        </mt-form-item> -->
        <mt-form-item prop="businessType" label="业务类型">
          <mt-select
            v-model="menuRuleForm.businessType"
            :dataSource="businessTypeArr"
            :showClearButton="true"
            placeholder="请选择业务类型"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="buCode" label="事业部编码">
           <mt-input
            v-model="menuRuleForm.buCode"
            type="text"
            onkeyup="this.value=this.value.replace(/\s+/g,'')"
            placeholder="请输入事业部编码"
          ></mt-input>
        </mt-form-item>
        <mt-form-item label="路由（开头）" v-if="isWhat == 'menu'">
          <mt-input
            v-model="menuRuleForm.path"
            type="text"
            onkeyup="this.value=this.value.replace(/\s+/g,'')"
            placeholder="请输入路由"
          ></mt-input>
        </mt-form-item>
        <mt-form-item label="链接地址" v-if="menuRuleForm.menuType === 2">
          <mt-input
            v-model="menuRuleForm.linkUrl"
            type="text"
            onkeyup="this.value=this.value.replace(/\s+/g,'')"
            placeholder="请输入链接地址"
          ></mt-input>
        </mt-form-item>
        
        <!-- <mt-form-item label="后端Api" v-if="isWhat == 'opt'||isWhat == 'list'">
          <div class="apiDelClass" v-if="cApiBtn"><div style="color:#9A9A9A;">请选择后端Api</div><mt-button cssClass="e-flat" :isPrimary='true' @click="chooseApi">选择</mt-button></div>
          <div class="apiDelClass" v-else><span>{{menuRuleForm.api.uri}}</span><span @click="delApi" style="color:#f44336;cursor: pointer;">删除</span></div>
        </mt-form-item> -->
        <mt-form-item prop="sn" label="序号">
          <mt-inputNumber 
            v-model="menuRuleForm.sn"
            type="text"
            :min="0"
            placeholder="请输入序号"
          ></mt-inputNumber>
        </mt-form-item>
        <mt-form-item prop="permissionDescription" label="备注" style="flex:100%">
          <mt-input
            :multiline="true"
            :rows="3"
            v-model="menuRuleForm.permissionDescription"
            type="text"
            placeholder="请输入备注"
          ></mt-input>
        </mt-form-item>
      </mt-form>
      <div class="tips" v-if="isWhat == 'menu'">
        <p>"菜单类型"选项说明：</p>
        <p>"左侧菜单"表示该菜单会展示在左侧菜单栏</p>
        <p>"详情页"表示该菜单不会展示在左侧菜单栏</p>
      </div>
      </div>
    </mt-dialog>
    <ChooseApiDialog :setObj='setObj' ref="ChooseApiDialog"></ChooseApiDialog>
  </div>
</template>

<script>
import ChooseApiDialog from './ChooseApiDialog.vue'
export default {
  components: { ChooseApiDialog },
  data() {
    return {
      isWhat: '',
      isZero: true,
      cApiBtn: true,
      setObj: { header: '选择Api' },
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: '取消' }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: '确定' }
        }
      ],
      functionTypeArr: [],
      businessTypeArr: [
        { text: '通用', value: '0' },
        { text: '采方', value: '1' },
        { text: '供方', value: '2' }
      ],
      menuTypeArr: [
        { text: '左侧菜单', value: 0 },
        { text: '详情页', value: 1 },
        { text: '链接', value: 2 }
      ],
      parentId: 0,
      menuType: '',
      permissionTypeCode: '',
      permissionTypeName: '',
      menuRuleForm: {
        parentCode: '',
        parentName: '',
        // permissionCode: '',
        businessType: '',
        functionType: '',
        permissionName: '',
        icon: '',
        path: '',
        api: { apiId: '', apiName: '' },
        sn: 0,
        meunType: '',
        permissionDescription: '' // 备注
      },
      rules: {
        // permissionCode: [
        //   { required: true, message: '请输入编码', trigger: 'blur' }
        //   // { pattern: /^[0-9a-zA-Z_]{1,}$/, message: '编码格式不正确', trigger: 'submit' }
        // ],
        functionType: [{ required: true, message: '请选择功能类型', trigger: 'blur' }],
        menuType: [{ required: true, message: '请选择菜单类型', trigger: 'blur' }],
        permissionName: [{ required: true, message: '请输入', trigger: 'blur' }],
        businessType: [{ required: true, message: '请选择业务类型', trigger: 'blur' }],
        path: [
          { required: false, pattern: /^[^\u4e00-\u9fa5]+$/, message: '请输入正确的路由格式', trigger: 'blur' }
        ],
        api: [{ required: true, message: '请选择Api', trigger: 'blur' }],
        sn: [{ required: true, message: '请输入序号', trigger: 'blur' }]
      }
    }
  },
  computed: {
    userInfo() {
      return this.$store.state.user.userInfo
    },
    functionTypeArrNoChild() {
      if (this.menuType === '' || this.menuType === 0 || !this.isZero) {
        return [...this.functionTypeArr]
      } else {
        return this.functionTypeArr.filter((e) => e.itemCode !== 'menu')
      }
    }
  },
  mounted() {
    this.$api.detail.getDictCode({ dictCode: 'permissionType' }).then(r => {
      // this.functionTypeArr = r.data
      this.functionTypeArr = []
      r.data.forEach(e => {
        if (e.itemCode !== 'field' && e.itemCode !== 'data') {
          this.functionTypeArr.push(e)
        }
      })
    })
  },
  methods: {
    reserForm() {
      this.menuRuleForm = {
        parentCode: '',
        parentName: '',
        // permissionCode: '',
        businessType: '',
        functionType: '',
        permissionName: '',
        icon: '',
        path: '',
        api: { apiId: '', apiName: '' },
        sn: 0,
        menuType: '',
        permissionDescription: '' // 备注
      }
    },
    functionTypeChange(e) {
      console.log('functionTypeChange', e)
      this.permissionTypeCode = e.itemData.itemCode
      this.permissionTypeName = e.itemData.itemName
      this.isWhat = e.itemData.itemCode
    },
    onOpen: function (args) {
      args.preventFocus = true
    },
    chooseApi() {
      this.$refs.ChooseApiDialog.clearRowSelection()
      this.$refs.ChooseApiDialog.show()
    },
    delApi() {
      this.cApiBtn = true
      this.menuRuleForm.api = { id: '', name: '' }
    },
    show(e) {
      console.log(e)
      this.$refs.dialog.ejsRef.show()
      this.menuRuleForm.parentName = e.name
      this.menuRuleForm.parentCode = e.id
    },
    hide() {
      this.$refs.dialog.ejsRef.hide()
      this.$parent.AddNodeFormShow = false
    },
    save() {
      this.$refs.menuRuleForm.validate((valid) => {
        if (valid) {
          const params = {
            applicationId: this.$parent.applicationId,
            businessType: this.menuRuleForm.businessType,
            permissionTypeId: this.menuRuleForm.functionType,
            icon: this.menuRuleForm.icon,
            parentId: this.parentId,
            path: this.menuRuleForm.path,
            permissionTypeCode: this.permissionTypeCode,
            // permissionCode: this.menuRuleForm.permissionCode,
            permissionDescription: this.menuRuleForm.permissionDescription,
            permissionName: this.menuRuleForm.permissionName,
            permissionTypeName: this.permissionTypeName,
            sn: this.menuRuleForm.sn,
            apiName: this.menuRuleForm.api.apiName,
            apiId: this.menuRuleForm.api.apiId,
            tenantId: this.userInfo.tenantId
          }
          if (this.menuRuleForm.functionType === 'menu') {
            params.menuType = this.menuRuleForm.menuType
            params.buCode = this.menuRuleForm.buCode
            params.linkUrl = this.menuRuleForm.linkUrl
            this.$api.detail.addMenu(params).then(r => {
              this.$parent.getListElementTree({ applicationId: this.$parent.applicationId })
              this.hide()
            })
          } else if (this.menuRuleForm.functionType === 'tab') {
            this.$api.detail.addOther(params).then(r => {
              this.$parent.getListElementTree({ applicationId: this.$parent.applicationId })
              this.hide()
            })
          } else {
            this.$api.detail.addOpt(params).then(r => {
              this.$parent.getListElementTree({ applicationId: this.$parent.applicationId })
              this.hide()
            })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>
<style lang="scss">
@import "../../../../node_modules/@digis/multilingual-input/build/esm/bundle.css";
.dialog-form-flex .e-dlg-content {
  padding: 40px !important;
  .mt-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 390px;
      .mt-form-item__content {
        width: 100%;
      }
    }
  }
}
.apiDelClass{
    height: 31px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    line-height: 35px;
    padding: 0 0 0 10px;
    border-bottom: 1px solid rgb(232, 232, 232);
}
.tips{
  color:#f44336;
  line-height: 25px;
}
</style>
