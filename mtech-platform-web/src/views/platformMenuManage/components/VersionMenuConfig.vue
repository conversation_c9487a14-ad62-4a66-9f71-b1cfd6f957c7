<template>
  <TreeTransferLayout
    v-model="visible"
    :id="data.id"
    :applicationData="currApplication"
    :sourceField="sourceField"
    :targentField ="targentField"
    :sourceDataTree="sourceTreeDataSource"
    :targetDataList="targetTreeDataSource"
    @save="saveAuth"
  >

      <template slot="sourceTitle">
        <span>功能名称</span>
        <ApplicationSelect class="application-select" @change="changeApplaction"/>
      </template>

      <template  slot="targetTitle">
        <span>已分配功能</span>
      </template>

  </TreeTransferLayout>
</template>

<script lang="ts">
import { Component, Vue, Prop } from '@mtech/vue-property-decorator'
import ApplicationSelect from './ApplicationSelect.vue'
import TreeTransferLayout from '@/components/TreeTransferLayout.vue'

@Component({
  components: {
    ApplicationSelect,
    TreeTransferLayout
  }
})
export default class VersionMenuConfig extends Vue {
  @Prop()
  value !: boolean

  @Prop()
  data !: any

  sourceField = {
    id: 'id',
    text: 'name',
    child: 'children'
  }

  targentField = {
    id: 'permissionId',
    text: 'permissionName',
    parentID: 'parentPermissionId',
    hasChildren: 'hasChildren'
  }

  sourceTreeDataSource:any[] = []
  targetTreeDataSource:any[] = []
  currApplication: any = {}

  get visible() {
    if (this.value) {
      this.getVersionPermission()
    }
    return this.value
  }

  set visible(v: boolean) {
    this.$emit('input', v)
  }

  changeApplaction(application: any) {
    this.currApplication = {
      applicationId: application.id,
      applicationName: application.applicationName
    }

    this.$api.detail.getListElementTreePlatform({
      applicationId: application.id
    }).then((res:any) => {
      if (res.code === 200) {
        this.sourceTreeDataSource = res.data || []
      }
    })
  }

  saveAuth(params: any) {
    this.$api.platmenu.versionPermissionAdd(params).then((res: { code: number; msg: any }) => {
      if (res.code === 200) {
        this.$toast({
          content: res.msg || '版本权限添加成功',
          type: 'success'
        })
        this.visible = false
      }
    }).catch((error: any) => {
      this.$toast({
        content: error.msg || '版本权限删除成功',
        type: 'error'
      })
    })
  }

  // 获取该版本已分配的权限
  private getVersionPermission() {
    this.$api.platmenu.versionPermissionFind({
      id: this.data.id
    }).then((res: { code: number; data: any[] }) => {
      if (res.code === 200) {
        this.targetTreeDataSource = res?.data || []
      }
    })
  }
}

</script>

<style lang="scss" scoped>

.content--wrap {
  flex-wrap: nowrap;
  height: 100%;
}
.content-box {
  border: 1px solid #E8E8E8;
  border-radius: 4px;
  display: flex;
  flex-direction: column;

  .content-box--title {
    border-bottom: 1px solid #E8E8E8;
    padding: 0 20px;
    line-height: 40px;

    >span {
      font-size: 16px;
      color: #292929;
      font-weight: 500;
    }

    .application-select {
      float: right;
      width: 100px;
    }
  }
  .content-treeview {
    flex:1;
    overflow: auto;
  }
  .content-treeview--target {
    overflow: auto;
  }
  .application-name {
    line-height: 40px;
    padding: 0 30px;
    color: #292929;
    font-weight: 500;
  }

}

.content-arrow {
  margin: auto;

  .content-arrow--btn {
    height: 30px;
    width: 30px;
    border: 1px solid #e8e8e8;
    line-height: 30px;
    border-radius: 50%;
    text-align: center;
    color: #98AAC3;
    margin: 10px;
    cursor: pointer;
  }
}

</style>
