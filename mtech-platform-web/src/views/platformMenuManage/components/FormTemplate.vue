<template>
  <div class="demo-block">
      <mt-form
        ref="menuRuleForm"
        :model="menuRuleForm"
        :rules="rules"
        class="formClass"
      >
        <mt-form-item prop="permissionCode" label="编码">
          <mt-input
            v-model="menuRuleForm.permissionCode"
            type="text"
            :disabled="true"
            onkeyup="this.value=this.value.replace(/\s+/g,'')"
            placeholder="请输入编码"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="businessType" label="业务类型">
          <mt-select
            v-model="menuRuleForm.businessType"
            :dataSource="businessTypeArr"
            :showClearButton="true"
            placeholder="请选择业务类型"
            :disabled="true"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="buCode" label="事业部编码">
          <mt-input
          v-model="menuRuleForm.buCode"
          type="text"
          onkeyup="this.value=this.value.replace(/\s+/g,'')"
          placeholder="请输入事业部编码"
        ></mt-input>
        </mt-form-item>
        <mt-form-item prop="functionType" label="功能类型">
          <mt-select
            v-model="menuRuleForm.functionType"
            :disabled="true"
            :dataSource="functionTypeArr"
            :fields="{text:'itemName', value:'itemCode'}"
            :showClearButton="true"
            :change="functionTypeChange"
            placeholder="请选择功能类型"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="menuType" label="菜单类型" v-if="isWhat == 'menu'">
          <mt-select
            v-model="menuRuleForm.menuType"
            :dataSource="menuTypeArr"
            :showClearButton="true"
            placeholder="请选择菜单类型"
            disabled
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="permissionName" label="名称">
          <mt-multilingual-input
            v-model="menuRuleForm.permissionName"
            group-code="srm"
            code="eSeSDdQVuClt"
            :disabled="false"
            :placeholder="$t('请输入名称')"
          >
          </mt-multilingual-input>
          <!-- <mt-input
            v-model="menuRuleForm.permissionName"
            type="text"
            placeholder="请输入名称"
            :fields="{text:'itemName', value:'dictId'}"
          ></mt-input> -->
      </mt-form-item>
      <!-- <mt-form-item label="图标">
          <mt-input
            v-model="menuRuleForm.icon"
            type="text"
            placeholder="请输入名称"
            :fields="{text:'itemName', value:'dictId'}"
          ></mt-input>
        </mt-form-item> -->
      <mt-form-item label="路由（开头）" v-if="isWhat == 'menu'">
        <mt-input
          v-model="menuRuleForm.path"
          type="text"
          onkeyup="this.value=this.value.replace(/\s+/g,'')"
          placeholder="请输入路由"
        ></mt-input>
      </mt-form-item>
      <mt-form-item label="链接地址" v-if="menuRuleForm.menuType === 2">
        <mt-input
          v-model="menuRuleForm.linkUrl"
          type="text"
          onkeyup="this.value=this.value.replace(/\s+/g,'')"
          placeholder="请输入链接地址"
        ></mt-input>
      </mt-form-item>
      <mt-form-item label="后端Api" v-if="isWhat == 'opt' || isWhat == 'list'">
        <div class="apiDelClass" v-if="cApiBtn">
          <div style="color: #9a9a9a">请选择后端Api</div>
          <mt-button cssClass="e-flat" :isPrimary="true" @click="chooseApi"
            >选择</mt-button
          >
        </div>
        <div class="apiDelClass" v-else>
          <span>{{ menuRuleForm.api.uri }}</span
          ><span @click="delApi" style="color: #f44336; cursor: pointer"
            >删除</span
          >
        </div>
      </mt-form-item>
      <mt-form-item prop="sn" label="序号">
        <mt-inputNumber
          v-model="menuRuleForm.sn"
          type="text"
          :min="0"
          placeholder="请输入序号"
        ></mt-inputNumber>
      </mt-form-item>
      <mt-form-item class="form-item" label="父节点">
        <mt-DropDownTree
          ref="ddtInstance"
          :fields="fileds"
          id="baseTreeSelect"
          :allowFiltering="true"
          :itemTemplate="iTemplate"
          placeholder="请选择"
          :popupHeight="300"
          v-model="parentCode"
          v-if="parentCode.length > 0"
        >
        </mt-DropDownTree>
      </mt-form-item>
      <!-- <mt-form-item label="父节点名称" v-if="isZero">
          <mt-input v-model="menuRuleForm.parentName" :disabled="true" type="text"></mt-input>
        </mt-form-item> -->
      <mt-form-item
        prop="permissionDescription"
        label="备注"
        style="flex: 100%"
      >
        <mt-input
          :multiline="true"
          :rows="3"
          v-model="menuRuleForm.permissionDescription"
          type="text"
          placeholder="请输入备注"
        ></mt-input>
      </mt-form-item>
    </mt-form>
    <ApiListDialog :setObj="setObj" :permissionId="menuRuleForm.id" ref="ApiListDialog"></ApiListDialog>
  </div>
</template>

<script>
import ApiListDialog from './ApiListDialog.vue'
import Vue from 'vue'
export default {
  components: { ApiListDialog },
  name: 'FormTemplate2',
  props: {
    parentData: {
      type: Object,
      default: () => {}
    },
    treeCodeTree: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      // isWhat: 'menu',
      isZero: true,
      // cApiBtn: true,
      setObj: { header: '选择Api' },
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: '取消' }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: '确定' }
        }
      ],
      functionTypeArr: [],
      businessTypeArr: [
        { text: '通用', value: 0 },
        { text: '采方', value: 1 },
        { text: '供方', value: 2 }
      ],
      menuTypeArr: [
        { text: '左侧菜单', value: 0 },
        { text: '详情页', value: 1 },
        { text: '链接', value: 2 }
      ],
      parentId: 0,
      permissionTypeCode: '',
      permissionTypeName: '',
      menuRuleForm: {
        parentId: '',
        parentCode: '',
        parentName: '',
        permissionCode: '',
        businessType: '',
        functionType: '',
        permissionName: '',
        menuType: '',
        icon: '',
        path: '',
        api: { apiId: '', apiName: '' },
        sn: 0,
        permissionDescription: '' // 备注
      },
      rules: {
        permissionCode: [
          { required: true, message: '请输入编码', trigger: 'blur' }
          // { pattern: /^[0-9a-zA-Z_]{1,}$/, message: '编码格式不正确', trigger: 'submit' }
        ],
        functionType: [
          { required: true, message: '请选择功能类型', trigger: 'blur' }
        ],
        menuType: [{ required: true, message: '请选择菜单类型', trigger: 'blur' }],
        path: [
          {
            required: false,
            pattern: /^[^\u4e00-\u9fa5]+$/,
            message: '请输入正确的路由格式',
            trigger: 'blur'
          }
        ],
        permissionName: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        businessType: [
          { required: true, message: '请选择业务类型', trigger: 'blur' }
        ],
        // api: [{ required: true, message: '请选择Api', trigger: 'blur' }],
        sn: [{ required: true, message: '请输入序号', trigger: 'blur' }]
      },
      iTemplate: function (e) {
        return {
          template: Vue.component('iTemplate', {
            template:
              '<div class="head">{{data.permissionCode}}_{{data.name}} </div>',
            data() {
              return {
                data: {}
              }
            }
          })
        }
      },
      parentCode: []
    }
  },
  computed: {
    userInfo() {
      return this.$store.state.user.userInfo
    },
    isWhat() {
      return this.parentData.permissionTypeCode
    },
    cApiBtn() {
      if (this.menuRuleForm.api) {
        return !this.menuRuleForm.api.apiName
      } else {
        return true
      }
    },
    fileds() {
      return { ...this.treeCodeTree, value: 'id', parentValue: 'parentId' }
    }
  },
  watch: {
    parentData: {
      handler() {
        if (this.$refs.ddtInstance) {
          this.$refs.ddtInstance.ejsInstances?.refresh()
        }
        this.menuRuleForm.businessType = this.parentData.businessType
        this.menuRuleForm.sn = this.parentData.sn
        this.menuRuleForm.functionType = this.parentData.permissionTypeCode
        this.menuRuleForm.permissionName = this.parentData.permissionName
        this.menuRuleForm.permissionDescription =
          this.parentData.permissionDescription
        this.menuRuleForm.permissionCode = this.parentData.permissionCode
        this.menuRuleForm.parentId = this.parentData.parentId
        this.menuRuleForm.parentCode = this.parentData.parentCode
        this.menuRuleForm.businessType = this.parentData.businessType
        this.menuRuleForm.parentName = this.parentData.parentName
        this.menuRuleForm.buCode = this.parentData.buCode
        if (this.parentData.menu) {
          this.menuRuleForm.path = this.parentData.menu.path
          this.menuRuleForm.icon = this.parentData.menu.icon
          this.menuRuleForm.id = this.parentData.menu.id
          this.menuRuleForm.menuType = this.parentData.menu.menuType
          this.menuRuleForm.linkUrl = this.parentData.menu?.linkUrl
        } else {
          this.menuRuleForm.id = this.parentData.id
        }
        this.menuRuleForm.api = this.parentData.api
        this.parentData.parentId === '0'
          ? (this.isZero = false)
          : (this.isZero = true)
        this.parentCode = [this.menuRuleForm.parentId]
        // this.isWhat = this.parentData.permissionTypeCode
        // console.log('parentData:', this.parentData)
      },
      immediate: true
    },

    menuRuleForm: {
      handler() {
        console.log('menuRuleForm:', this.menuRuleForm)
      },
      deep: true
    }
  },
  mounted() {
    this.$api.detail.getDictCode({ dictCode: 'permissionType' }).then((r) => {
      this.functionTypeArr = []
      r.data.forEach((e) => {
        if (e.itemCode !== 'field' && e.itemCode !== 'data') {
          this.functionTypeArr.push(e)
        }
      })
    })
  },
  methods: {
    functionTypeChange(e) {
      console.log('functionTypeChange', e)
      this.permissionTypeCode = e.itemData.itemCode
      this.permissionTypeName = e.itemData.itemName
    },
    onOpen: function (args) {
      args.preventFocus = true
    },
    chooseApi() {
      // this.$refs.ApiListDialog.clearRowSelection()
      this.$refs.ApiListDialog.show()
    },
    delApi() {
      this.cApiBtn = true
      this.menuRuleForm.api = { apiId: '', apiName: '' }
    },
    save() {
      // if (
      //   !this.menuRuleForm.api?.apiId &&
      //   (this.isWhat === 'opt' || this.isWhat === 'list')
      // ) {
      //   this.$toast({
      //     content: '请选择后端api',
      //     type: 'warning'
      //   })
      //   return
      // }
      this.$refs.menuRuleForm.validate((valid) => {
        if (valid) {
          const params = {
            applicationId: this.$parent.applicationId,
            businessType: this.menuRuleForm.businessType,
            permissionTypeId: this.menuRuleForm.permissionTypeCode,
            icon: this.menuRuleForm.icon,
            parentId: this.parentCode.join(),
            path: this.menuRuleForm.path,
            permissionTypeCode: this.permissionTypeCode,
            permissionCode: this.menuRuleForm.permissionCode,
            permissionDescription: this.menuRuleForm.permissionDescription,
            permissionName: this.menuRuleForm.permissionName,
            permissionTypeName: this.permissionTypeName,
            sn: this.menuRuleForm.sn,
            id: this.menuRuleForm.id,
            apiId: this.menuRuleForm.api ? this.menuRuleForm.api.apiId : '',
            apiName: this.menuRuleForm.api ? this.menuRuleForm.api.apiName : '',
            tenantId: this.userInfo.tenantId,
            buCode: this.menuRuleForm.buCode,
            linkUrl: this.menuRuleForm.linkUrl
          }
          if (this.menuRuleForm.functionType === 'menu') {
            params.menuType = this.menuRuleForm.menuType
            this.$api.detail.updateMenu(params).then((r) => {
              console.log(this.$parent)
              this.$parent.$parent.getListElementTree({
                applicationId: this.$parent.$parent.applicationId
              })
            })
          } else if (this.menuRuleForm.functionType === 'opt' || this.menuRuleForm.functionType === 'list') {
            this.$api.detail.updateOpt(params).then((r) => {
              this.$parent.$parent.getListElementTree({
                applicationId: this.$parent.$parent.applicationId
              })
              this.hide()
            })
          } else {
            this.$api.detail
              .updateOther(params)
              .then((r) => {
                console.log(r)
                this.$parent.$parent.getListElementTree({
                  applicationId: this.$parent.$parent.applicationId
                })
              })
              .catch((e) => {
                this.$toast({ type: 'error', content: e.msg })
              })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    templates(e) {
      return {
        template: Vue.component('headerTemplate', {
          template: '<div class="head"> Employee List </div>',
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../../../node_modules/@digis/multilingual-input/build/esm/bundle.css';
.btn-submit {
  margin-right: 30px;
}
.demo-block {
  // height: 100%;
  .formClass {
    display: flex;
    height: 100%;
    flex-direction: row;
    flex-flow: row wrap;
    justify-content: space-between;
    .mt-form-item {
      flex: 45%;
      width: 100%;
      margin: 0 40px 30px 0;
    }
    .lastItem {
      flex: 100%;
    }
  }
}
</style>
