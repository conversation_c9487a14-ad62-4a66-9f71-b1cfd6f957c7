<template>
  <mt-select 
    :dataSource="dataArr"
    :showClearButton="false"
    :fields="{ text: 'applicationName', value: 'id' }"
    cssClass="selectClass"
    v-model="applicationId"
    :change="arrChange"
  ></mt-select>
</template>

<script lang="ts">
import { Component, Vue } from '@mtech/vue-property-decorator' 

@Component({
  components: { 
  }
})
export default class ApplicationSelect extends Vue {
  dataArr: any[] = []
  applicationId = ''

  created() {
    this.getApplicationQuery()
  }

  arrChange(e: any) {  
    this.$emit('change', e.itemData) 
  }

  private async getApplicationQuery() {
    const res = await this.$api.detail.getApplicationQuery({
      applicationCode: '',
      applicationDescription: '',
      applicationName: ''
    })

    if (res.code === 200 && res.data) {
      this.dataArr = res.data
      this.applicationId = res.data[0].id
      this.arrChange({
        itemData: res.data[0]
      })
    }
  }
}
</script>
