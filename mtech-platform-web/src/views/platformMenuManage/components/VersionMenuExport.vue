<template>
  <mt-dialog
    ref="dialog"
    header="菜单导出"
    :buttons="buttons"
    @close="hide">
    <div class="content--wrap" >
      <mt-row class="treeselect-content--wrap" type="flex" justify="space-between">
        <mt-col class="content-box" :span="24">
          <div class="content-box--title">
            <ApplicationSelect class="application-select" @change="changeApplaction"/>
          </div>

          <div class="search">
          </div>

          <div class="content-treeview">
            <!-- <div
              v-for="(sourceTreeData) in sourceTreeViewList"
              :key="sourceTreeData.applicationName">
              <template v-if="sourceTreeData.dataSource.length">
                <p class="application-name">{{sourceTreeData.applicationName}}</p>
                <mt-treeView
                  class="tree-view--template"
                  ref="treeViewSource"
                  :fields="sourceTreeData"
                  :loadOnDemand="false"
                  :auto-check="true"
                  :show-check-box="true"
                ></mt-treeView>
              </template>
            </div> -->
            <mt-treeView
              class="tree-view--template"
              ref="treeViewSource"
              :fields="sourceTreeDataSource"
              :loadOnDemand="false"
              :auto-check="true"
              :show-check-box="true"
            ></mt-treeView>
          </div>

        </mt-col>
      </mt-row>
    </div>
  </mt-dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from '@mtech/vue-property-decorator'
import ApplicationSelect from './ApplicationSelect.vue'

interface IParamPermission {
  applicationId: string
  applicationName: string
  parentPermissionId?:string
  permissionId: string
  permissionName: string
  permissionType: string,
  permissionCode: string,
  hasChildren?: boolean
  isChecked?: boolean | string
  versionId?: string
}

@Component({
  components: {
    ApplicationSelect
  }
})
export default class VersionMenuExport extends Vue {
  @Prop()
  value !: boolean

  @Prop()
  data !: any

  sourceField = {
    id: 'id',
    text: 'name',
    child: 'children'
  }

  buttons = [
    {
      click: this.hide,
      buttonModel: { content: '取消' }
    },
    {
      click: this.handleConfirm,
      buttonModel: { isPrimary: 'true', content: '确定' }
    }
  ]

  sourceTreeDataSource:any = {
    id: 'id',
    text: 'name',
    child: 'children',
    dataSource: []
  }

  currApplication: any = {}

  // created() {
  //   this.$api.detail.getListElementTree({
  //     applicationId: '2'
  //   }).then((res:any) => {
  //     if (res.code === 200) {
  //       this.sourceTreeDataSource.dataSource = res.data || []
  //     }
  //   })
  // }

  get visible() {
    return this.value
  }

  set visible(v: boolean) {
    this.$emit('input', v)
  }

  hide() {
    const ref: any = this.$refs.dialog
    ref?.ejsRef?.hide()
    this.visible = false
  }

  show() {
    this.$nextTick(() => {
      const ref: any = this.$refs.dialog
      ref.ejsRef.show()
    })
  }

  @Watch('visible', { immediate: true })
  onWatchVisible(val: boolean) {
    if (val) {
      this.show()
    } else {
      this.hide()
    }
  }

  changeApplaction(application: any) {
    this.currApplication = {
      applicationId: application.id,
      applicationName: application.applicationName
    }

    this.$api.detail.getListElementTreePlatform({
      applicationId: application.id
    }).then((res:any) => {
      if (res.code === 200) {
        this.sourceTreeDataSource.dataSource = res.data || []
      }
    })
  }

  private handleConfirm() {
    const ref: any = this.$refs.treeViewSource
    const ejsInstances: any = ref?.ejsInstances
    const list = ejsInstances.getTreeData()
    const params = this.createTargetTreeByTree(list, ejsInstances, this.currApplication.applicationId, this.currApplication.applicationName)
    this.$api.platmenu.permissionExport(params).then((res: any) => {
      const { code, data } = res
      if (code === 200) {
        this.$dialog({
          data: {
            title: '复制到剪贴板(原有剪贴板内容将会被覆盖)',
            // message: '复制当前导出内容将会覆盖原有剪贴板内容，您是否确认复制到剪贴板？'
            message: data
          },
          success: () => {
            this.copyToClipboard(JSON.stringify(data))
            this.$toast({
              content: res.message ? res.message : '复制成功',
              type: 'success'
            })
            this.hide()
          }
        })
      }
    })
  }

  copyToClipboard(str: string) {
    const el = document.createElement('textarea')
    el.value = str
    el.setAttribute('readonly', '')
    el.style.position = 'absolute'
    el.style.left = '-9999px'
    document.body.appendChild(el)
    el.select()
    document.execCommand('copy')
    document.body.removeChild(el)
  }

  // 将原始权限树（树状）中被选中的数据转换为列表
  private createTargetTreeByTree(sourceTreeData: any[], ejsInstances: any, applicationId: string, applicationName: string) {
    const targetTreeDataList: IParamPermission[] = []

    sourceTreeData.forEach((v) => {
      // 判断该节点是否被选中或是否可能处于 mixed 状态
      if (v.isChecked || ejsInstances.getNode(v.id).isChecked === 'mixed') {
        const targetTreeNode: IParamPermission = {
          applicationId: applicationId,
          applicationName: applicationName,
          permissionId: v.id,
          permissionName: v.name,
          permissionType: v.permissionTypeId,
          permissionCode: v.permissionCode,
          hasChildren: !!v.children,
          isChecked: v.isChecked,
          versionId: this.data.id
        }

        if (v.parentId && v.parentId !== '0') {
          targetTreeNode.parentPermissionId = v.parentId
        }
        targetTreeDataList.push(targetTreeNode)

        if (v.children) {
          const list = this.createTargetTreeByTree(v.children, ejsInstances, applicationId, applicationName)
          targetTreeDataList.push(...list)
        }
      }
    })

    return targetTreeDataList
  }
}

</script>

<style lang="scss" scoped>
.content--wrap {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 40px 22px 0;
}

.treeselect-content--wrap {
  flex: 1;
  flex-wrap: nowrap;
  height: 100%;
}

.tree-view--template {
  /deep/ .mt-tree-view {
    display: block;
  }
  /deep/ .e-treeview .e-ul{
    overflow: hidden;
  }
}
.content-box {
  border: 1px solid #E8E8E8;
  border-radius: 4px;
  display: flex;
  flex-direction: column;

  .content-box--title {
    border-bottom: 1px solid #E8E8E8;
    padding: 0 20px;
    line-height: 40px;

    >span {
      font-size: 16px;
      color: #292929;
      font-weight: 500;
    }

    .application-select {
      float: left;
      width: 100px;
    }
  }
  .content-treeview {
    overflow: auto;
  }
  .application-name {
    line-height: 40px;
    padding: 0 30px;
    color: #292929;
    font-weight: 500;
  }

}
</style>
