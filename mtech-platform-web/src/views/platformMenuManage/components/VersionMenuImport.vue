<template> 
  <mt-dialog 
    ref="dialog"
    header="菜单导入"   
    :buttons="buttons"
    @close="hide">
    <div style="padding-top: 18px" >
      <mt-form ref="form" :model="approveForm" :rules="formRules">
        <mt-form-item label="导入内容" prop="importJson" >
          <mt-input type="text" :multiline="true" :rows="20" v-model="approveForm.importJson"/>
        </mt-form-item>
      </mt-form> 
    </div>
  </mt-dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from '@mtech/vue-property-decorator'
import ApplicationSelect from './ApplicationSelect.vue'

@Component({
  components: {
    ApplicationSelect
  }
})
export default class VersionMenuExport extends Vue {
  @Prop()
  value !: boolean

  @Prop()
  data !: any

  approveForm :any = {
    importJson: ''
  }

  buttons = [
    {
      click: this.hide,
      buttonModel: { content: '取消' }
    },
    {
      click: this.handleConfirm,
      buttonModel: { isPrimary: 'true', content: '确定' }
    }
  ]

  formRules = { 
    importJson: [{ required: true, message: '请输入导入内容', trigger: 'blur' }]
  }
 
  get visible() {
    return this.value
  }

  set visible(v: boolean) {
    this.$emit('input', v)
  }

  hide() {   
    const ref: any = this.$refs.dialog
    ref?.ejsRef?.hide()
    this.visible = false
  }

  show() {   
    this.$nextTick(() => {
      const ref: any = this.$refs.dialog
      ref.ejsRef.show()
    })
  }

  @Watch('visible', { immediate: true }) 
  onWatchVisible(val: boolean) { 
    if (val) {
      this.show()
    } else {
      this.hide()
    }
  }

  private handleConfirm() {
    (this.$refs.form as any).validate((valid: boolean) => {
      if (valid) {
        try {
          const params: any = JSON.parse(this.approveForm.importJson)
          if (params instanceof Array || params instanceof Object) {
            this.$api.platmenu.permissionImport(params).then((res: any) => {
              const { code, message } = res
              if (code === 200) {
                // 导入成功
                this.$toast({
                  content: message as any ? message : '导入成功',
                  type: 'success'
                })
                // (this.$parent as any).getListElementTree({ applicationId: (this.$parent as any).applicationId })
                this.hide()
              }
            })
          } else {
            this.$toast({
              content: '导入内容数据格式有误',
              type: 'warning'
            })
          }
        } catch {
          this.$toast({
            content: '导入内容数据格式有误',
            type: 'warning'
          })
        }
      }
    })
  }
}

</script>

<style lang="scss" scoped></style>
