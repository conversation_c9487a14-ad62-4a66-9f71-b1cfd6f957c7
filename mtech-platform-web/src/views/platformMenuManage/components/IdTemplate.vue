<template>
  <div class="grid-edit-column mt-flex-direction-column">
    <div>
      {{ data.id }}
      <!-- {{data.abc}} -->
    </div>
    <div class="column-tool mt-flex flow-id-template">
      <div v-for="(item, toolbarIndex) in buttons" :key="toolbarIndex" class="template-svg" :name="item.icon" @click="handleClickCellTool(item)">
        <i :class="['mt-icons', 'mt-icon-' + item.icon]"></i>
        <span class="icon-title">{{ item.title }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import Bus from '@/utils/bus'

export default {
  name: 'ColumnTemplate',
  data() {
    return {
      data: {},
      buttons: [
        { id: 'Edit', icon: 'icon_solid_edit', title: '编辑' },
        { id: 'Copy', icon: 'Copy', title: '复制' },
        { id: 'Delete', icon: 'icon_solid_Delete', title: '删除' }
      ]
    }
  },
  mounted() {},
  methods: {
    handleClickCellTool(item) {
      Bus.$emit('handleClickCellToolFlow', {
        tool: item,
        data: this.data,
        gridRef: this.data.gridRef,
        tabIndex: this.data.tabIndex,
        grid: this.data.gridTemplate
      })
    },
    handleClickCellTitle() {
      Bus.$emit('handleClickCellTitleFlow', {
        field: this.data.templateField,
        data: this.data,
        gridRef: this.data.gridRef,
        tabIndex: this.data.tabIndex,
        grid: this.data.gridTemplate
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.mt-flex {
  display: flex;
  position: relative;
}

.mt-flex-direction-column {
  display: flex;
  flex-direction: column;
  position: relative;
}

.field-content {
  color: #00469c;
  font-size: 14px;
  cursor: pointer;
  &:hover {
    font-weight: 500;
  }
}
.grid-edit-column {
  height: 49px;
  padding-left: 10px;
  justify-content: center;
  .column-tool {
    .template-svg {
      cursor: pointer;
      margin-left: 10px;
      line-height: 1;
      &:first-of-type {
        margin-left: 0;
      }
      .mt-icons,
      .icon-title {
        font-size: 12px;
        color: #9daabf;
      }
      .icon-title {
        font-family: 'Roboto', 'Segoe UI', 'GeezaPro', 'DejaVu Serif', 'sans-serif', '-apple-system', 'BlinkMacSystemFont';
      }
    }
  }
}
</style>
