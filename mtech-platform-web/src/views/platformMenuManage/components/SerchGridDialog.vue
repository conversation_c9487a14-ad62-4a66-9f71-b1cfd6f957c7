<template>
  <div class="demo-block">
    <mt-dialog ref="serchDialog" css-class="dialog-form-flex" :header="setObj.header" :buttons="buttons">
         <mt-template-page
          :hiddenTabs="true"
          :templateConfig="componentTreeConfig"
          ref="treeTemplatePage"
        >
        </mt-template-page>
    </mt-dialog>
  </div>
</template>

<script>
import { componentTreeConfig } from '../config/index'
export default {
  props: {
    setObj: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      componentTreeConfig,
      parentId: '0',
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: '取消' }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: '确定' }
        }
      ]
    }
  },
  methods: {
    show(e) {
      this.$refs.serchDialog.ejsRef.show()
    },
    hide() {
      this.$refs.serchDialog.ejsRef.hide()
    },
    save() {
      const sData = this.$refs.treeTemplatePage.getCurrentTabRef().treeGrid.getCheckedRecords()
      const setArr = []
      sData.forEach(e => {
        if (e.level === 1) {
          e.parentId = this.parentId
          e.metadataTableFieldId = e.id
          e.metadataTableName = e.tableName
            
          setArr.push(e)
        }
      })
      console.log(sData)
      this.$api.detail.addPermission(setArr).then(() => {
        this.hide()
        this.$parent.refreshMain()
      })
    }
  }
}
</script>
<style lang="scss">
.dialog-form-flex .e-dlg-content {
  padding: 40px !important;
  .mt-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 390px;
      .mt-form-item__content {
        width: 100%;
      }
    }
  }
}
</style>
