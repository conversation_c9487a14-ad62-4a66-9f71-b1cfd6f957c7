<template> 
  <mt-dialog 
    ref="dialog"
    :header="header"   
    :buttons="buttons"
    @close="hide">

    <mt-form ref="form" class="content" :model="formData" :rules="formRules" > 

        <mt-form-item label="版本号" prop="no" >
          <mt-input 
            v-model="formData.no"  
            placeholder="请输入版本号" />
        </mt-form-item>

        <mt-form-item label="版本名称"  prop="name" >
          <mt-input 
            v-model="formData.name"  
            placeholder="请输入版本名称" />
        </mt-form-item> 

        <mt-form-item label="备注"  prop="remark" >
          <mt-input 
            v-model="formData.remark" 
            :multiline="true" 
            :rows="2" 
            placeholder="字数不超过200字" />
        </mt-form-item> 

    </mt-form>

  </mt-dialog> 
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from '@mtech/vue-property-decorator' 

@Component({
  components: { 
  }
})
export default class VersionEditContent extends Vue {
  @Prop()
  value !: boolean

  @Prop()
  data !: any

  buttons = [
    {
      click: this.hide,
      buttonModel: { content: '取消' }
    },
    {
      click: this.handleConfirm,
      buttonModel: { isPrimary: 'true', content: '确定' }
    }
  ]

  formData:any = {}

  formRules = {
    no: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
    name: [{ required: true, message: '请输入版本名称', trigger: 'blur' }],
    remark: [{ max: 200, message: '字数不超过200字', trigger: 'blur' }]
  }

  get visible() {
    return this.value
  }

  set visible(v: boolean) {
    this.$emit('input', v)
  }

  get isEdit() {
    return this.data && this.data.no
  }

  get header() {
    if (this.isEdit) {
      return '编辑应用版本'
    } else {
      return '新增应用版本'
    }
  }

  @Watch('visible') 
  onWatchVisible(val: boolean) {
    if (val) {
      this.show()
    } 
  }

  hide() {
    this.visible = false
    const ref: any = this.$refs.dialog
    ref.ejsRef.hide()
  }

  show() {  
    this.resetFormData()
    const ref: any = this.$refs.dialog
    ref.ejsRef.show() 
    if (this.data) {
      this.formData = { ...this.data }
    }
  }

  handleConfirm() {
    (this.$refs.form as any).validate((valid: boolean) => {
      if (valid) {
        if (this.isEdit) { 
          this.updateVersion()
        } else {
          this.addVersion()
        }
        this.hide()
      } else {
        this.$toast({
          content: '输入有误，请检查',
          type: 'warning'
        })
      }
    })
  }

  private addVersion() {
    this.$api.platmenu.versionAdd({  
      no: this.formData.no,
      remark: this.formData.remark, 
      name: this.formData.name
    }).then((res: any) => {
      if (res.code === 200) {
        this.$toast({
          content: res.msg || '版本新增成功',
          type: 'success'
        })
        this.$emit('add', res.data)
        this.hide()
      }
    }).catch((error: any) => {
      this.$toast({
        content: error.msg || '版本新增失败',
        type: 'error'
      }) 
    })
  }

  private updateVersion() {
    this.$api.platmenu.versionUpdate({  
      no: this.formData.no,
      remark: this.formData.remark,
      id: this.formData.id,
      name: this.formData.name
    }).then((res: any) => {
      if (res.code === 200) {
        this.$toast({
          content: res.msg || '版本更新成功',
          type: 'success'
        })
        this.$emit('update', res.data)
        this.hide()
      }
    }).catch((error: any) => {
      this.$toast({
        content: error.msg || '版本更新失败',
        type: 'error'
      }) 
    })
  }

  private resetFormData() {
    this.formData = {
      id: '',
      name: '',
      no: '',
      remark: ''
    }
  }
}

</script>

<style lang="scss"> 
</style>
