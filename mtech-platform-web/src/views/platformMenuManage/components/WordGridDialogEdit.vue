<template>
<div>
    <mt-dialog ref="DataGridDialogEdit" header="编辑" :buttons="buttons" css-class="dialog-form-flex">
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="fieldAlias" label="字段别名">
          <mt-input
            v-model="ruleForm.fieldAlias"
            type="text"
            placeholder="请输入字段别名"
          ></mt-input>
        </mt-form-item>
        <mt-form-item  label="备注">
          <mt-input
            :multiline="true"
            v-model="ruleForm.fieldDescription"
            type="text"
            placeholder="请输入备注"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
</div>
</template>

<script>

export default {
  data() {
    return {
      parentData: {},
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: '取消' }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: '确定' }
        }
      ],
      ruleForm: {
        fieldAlias: '',
        fieldDescription: ''
      },
      rules: {
        fieldAlias: [
          { required: true, message: '请输入字段别名', trigger: 'blur' }
        ]
      }
    }
  },
  mounted() {
    this.ruleForm.fieldAlias = this.parentData.fieldAlias
    this.ruleForm.fieldDescription = this.parentData.fieldDescription
  },
  methods: {
    show() {
      this.$refs.DataGridDialogEdit.ejsRef.show()
    },
    hide() {
      this.$refs.DataGridDialogEdit.ejsRef.hide()
    },
    save() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.parentData.fieldAlias = this.ruleForm.fieldAlias
          this.parentData.fieldDescription = this.ruleForm.fieldDescription
          this.$api.detail.updataPermission(this.parentData).then(() => {
            this.hide()
            this.$parent.refreshMain()
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>
