<template>
  <div class="demo-block">
    <mt-dialog
      ref="serchApiDialog"
      css-class="dialog-form-flex"
      header="API列表"
      :position="{ X: 'right', Y: 'top' }"
      height="100%"
      width="90%"
      :buttons="buttons"
    >
      <mt-template-page
        :hiddenTabs="true"
        :templateConfig="componentApiListConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @actionBegin="actionBegin"
        ref="apiTemplatePage"
        class="apiListTemplate"
      >
      <div
        slot="slot-filter-0"
        class="filter-container"
      >  
       <mt-form>
        <mt-form-item label="URL地址">
          <mt-input v-model="uri" placeholder="请输入URL地址"></mt-input>
        </mt-form-item>
       </mt-form>
       <div class="btn-group">
        <span type="info" @click="reset">重置</span>
        <span type="primary" @click="search">查询</span>
       </div>
      </div>
      </mt-template-page>
      <ChooseApiDialog :setObj="setObj" :permissionId="permissionId" ref="ChooseApiDialog" @refreshApiList="search"></ChooseApiDialog>
    </mt-dialog>
  </div>
</template>

<script>
import { componentApiListConfig } from '../config/index'
import ChooseApiDialog from './ChooseApiDialog.vue'
export default {
  components: {
    ChooseApiDialog
  },
  props: {
    setObj: {
      type: Object,
      default: () => {}
    },
    permissionId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      componentApiListConfig,
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: '取消' }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: '确定' }
        }
      ],
      typeObj: {
        header: '新增'
      },
      apiRuleForm: {},
      uri: ''
    }
  },
 
  mounted() {
   
  },
  methods: {
    reset() {
      this.uri = ''
      this.getApiList()
    },
    search() {
      this.getApiList()
    },
    actionBegin(e) {
      console.log('值变化：', e)
    },
    show() {
      // this.componentApiListConfig = componentApiListConfig(this.permissionId)
      this.getApiList()
      this.$refs.serchApiDialog.ejsRef.show()
    },
    hide() {
      this.$refs.serchApiDialog.ejsRef.hide()
    },
    getApiList() {
      this.$api.platmenu.queryOptApiList({
        id: this.permissionId,
        uri: this.uri || null
      }).then((res) => {
        this.$set(this.componentApiListConfig[0].grid, 'dataSource', res.data)
      })
    },
  
    handleClickToolBar(e) {
      const id = e.toolbar.id
      if (id === 'Add') {
        this.addApiDialog()
      } else if (id === 'Delete') {
        this.handleDelete()
      }
    },
    handleDelete() {
      const selectData = this.$refs.apiTemplatePage
        .getCurrentTabRef()
        .grid.getSelectedRecords()
      if (selectData.length === 0) {
        this.$toast({
          content: '请选择要删除的数据',
          type: 'warning'
        })
        return
      }
      const apiList = selectData.map((item) => {
        return {
          apiId: item.apiId
        }
      })
      this.$api.platmenu.deleteOptApiList({  
        permissionId: this.permissionId,
        apiList
      }).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: res.msg || '删除成功',
            type: 'success'
          }) 
          this.getApiList()
        }
      })
    },
    handleClickCellTool(e) {
      const data = e.data
      const id = e.tool.id
      if (id === 'edit') {
        const params = {}
        params.applicationId = data.applicationId
        params.apiCategoryId = data.apiCategoryId
        params.apiName = data.apiName
        params.apiDescription = data.apiDescription
        params.uri = data.uri
        params.id = data.id
        params.requestMethod = data.requestMethod
        this.apiRuleForm = params
      }
      this.typeObj = {
        header: '编辑'
      }
      this.addApiDialog()
    },
    addApiDialog() {
      this.$refs.ChooseApiDialog.show()
    },
    async addApiSave(params) {
      let res = null
      if (!params.id) {
        res = await this.$api.platmenu.addApiSave(params)
      }
      if (params.id) {
        res = await this.$api.platmenu.editApiSave(params)
      }
      this.$toast({
        content: res.msg,
        type: 'success'
      })
      this.$refs.apiTemplatePage.refreshCurrentGridData()
    },
    clearRowSelection() {
      this.$refs.apiTemplatePage.refreshCurrentGridData()
    }
  }
}
</script>
<style lang="scss">

.apiListTemplate .e-content{
  height:calc(100vh - 310px) !important;
}
.dialog-form-flex .e-dlg-content {
  padding: 40px !important;
  .mt-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 390px;
      .mt-form-item__content {
        width: 100%;
      }
    }
  }
}
.filter-container {
  position: relative;
  .btn-group {
    align-items: center;
    display: flex;
    position: absolute;
    height: 100%;
    right: 0;
    top:0;
    span {
      border: 1px solid #4a556b;
      border-radius: 4px;
      box-sizing: border-box;
      cursor: pointer;
      display: block;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      font-weight: 400;
      height: 28px;
      line-height: 26px;
      margin-left: 10px;
      padding: 0 16px;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }
    span[type="info"] {
      background: #fff;
      color: #4a556b;
    }
    span[type="primary"] {
      background: #4a556b;
      color: #fff;
    } 
  }
}

</style>
