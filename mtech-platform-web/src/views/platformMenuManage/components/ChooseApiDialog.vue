<template>
  <div class="demo-block">
    <mt-dialog
      ref="serchApiDialog"
      css-class="dialog-form-flex"
      :header="setObj.header"
      :position="{ X: 'right', Y: 'top' }"
      height="100%"
      width="80%"
      :buttons="buttons"
    >
      <mt-template-page
        :hiddenTabs="true"
        :templateConfig="componentApiConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @actionBegin="actionBegin"
        ref="apiTemplatePage"
        class="apiTemplate"
      >
      </mt-template-page>
      <addApiDialog
        :setObj="typeObj"
        @addApiSave="addApiSave"
        :form="apiRuleForm"
        ref="addApiDialog"
      ></addApiDialog>
    </mt-dialog>
  </div>
</template>

<script>
import { componentApiConfig } from '../config/index'
import addApiDialog from './addApiDialog.vue'
export default {
  components: {
    addApiDialog
  },
  props: {
    setObj: {
      type: Object,
      default: () => {}
    },
    permissionId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      componentApiConfig,
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: '取消' }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: '确定' }
        }
      ],
      typeObj: {
        header: '新增'
      },
      apiRuleForm: {}
    }
  },
  mounted() {
    
  },
  methods: {
    actionBegin(e) {
      console.log('值变化：', e)
    },
    show() {
      this.$refs.serchApiDialog.ejsRef.show()
    },
    hide() {
      this.$refs.serchApiDialog.ejsRef.hide()
    },
    save() {
      const selectData = this.$refs.apiTemplatePage
        .getCurrentTabRef()
        .grid.getSelectedRecords()
      if (selectData.length === 0) {
        this.$toast({
          content: '请选择数据',
          type: 'warning'
        })
        return
      }
      const apiList = selectData.map((item) => {
        return {
          apiId: item.id
        }
      })
      this.$api.platmenu.addOptApiList({  
        permissionId: this.permissionId,
        apiList
      }).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: res.msg || '添加成功',
            type: 'success'
          }) 
          this.$refs.apiTemplatePage.getCurrentUsefulRef().gridRef.ejsRef.clearSelection();
          this.$emit('refreshApiList')
        }
      })
    },
    handleClickToolBar(e) {
      const id = e.toolbar.id
      if (id === 'Add') {
        this.apiRuleForm = {
          applicationId: 2,
          apiCategoryId: null,
          apiName: '',
          apiType: 1,
          apiDescription: null,
          uri: null,
          id: null,
          requestMethod: null
        }
        this.typeObj = {
          header: '新增'
        }
        this.addApiDialog()
      }
    },
    handleClickCellTool(e) {
      const data = e.data
      const id = e.tool.id
      if (id === 'edit') {
        const params = {}
        params.applicationId = data.applicationId
        params.apiCategoryId = data.apiCategoryId
        params.apiName = data.apiName
        params.apiDescription = data.apiDescription
        params.uri = data.uri
        params.id = data.id
        params.requestMethod = data.requestMethod
        this.apiRuleForm = params
      }
      this.typeObj = {
        header: '编辑'
      }
      this.addApiDialog()
    },
    addApiDialog() {
      this.$refs.addApiDialog.show()
    },
    async addApiSave(params) {
      let res = null
      if (!params.id) {
        res = await this.$api.platmenu.addApiSave(params)
      }
      if (params.id) {
        res = await this.$api.platmenu.editApiSave(params)
      }
      this.$toast({
        content: res.msg,
        type: 'success'
      })
      this.$refs.apiTemplatePage.refreshCurrentGridData()
    },
    clearRowSelection() {
      this.$refs.apiTemplatePage.refreshCurrentGridData()
    }
  }
}
</script>
<style lang="scss">

.apiTemplate .e-content{
  height:calc(100vh - 510px) !important;
}
.dialog-form-flex .e-dlg-content {
  padding: 40px !important;
  .mt-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 390px;
      .mt-form-item__content {
        width: 100%;
      }
    }
  }
}
</style>
