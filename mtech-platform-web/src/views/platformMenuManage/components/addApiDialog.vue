<template>
  <div class="demo-block">
    <mt-dialog
      ref="serchApiDialog"
      css-class="dialog-form-flex"
      :header="setObj.header"
      :buttons="buttons"
    >
      <mt-form
        ref="apiRuleFormRef"
        :model="apiRuleForm"
        :rules="rules"
        class="formClass"
      >
        <mt-form-item class="form-item" label="api分类" prop="apiCategoryId">
          <mt-select
            :dataSource="cateArray"
            v-model="apiRuleForm.apiCategoryId"
            :showClearButton="true"
            :fields="{ text: 'categoryName', value: 'id' }"
            :change="functionTypeChange"
            placeholder="请选择api分类"
          ></mt-select>
        </mt-form-item>
        <mt-form-item label="请输入API名称" prop="apiName">
          <mt-input placeholder="请输入API名称" v-model="apiRuleForm.apiName" max-length="50">
          </mt-input>
          <!-- <mt-multilingual-input
            group-code="iam"
            type="text"
            placeholder="请输入API名称"
            v-model="apiRuleForm.apiName"
          ></mt-multilingual-input> -->
        </mt-form-item>
        <mt-form-item label="URl" prop="uri">
          <mt-input
            type="text"
            placeholder="请输入URl"
            v-model="apiRuleForm.uri"
          ></mt-input>
        </mt-form-item>
        <mt-form-item label="请求类型" prop="requestMethod">
          <mt-select
            :dataSource="methodsArray"
            v-model="apiRuleForm.requestMethod"
            :showClearButton="true"
            :change="functionTypeChange"
            placeholder="请选择"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
export default {
  props: {
    setObj: {
      type: Object,
      default: () => {}
    },
    form: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: '取消' }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: '确定' }
        }
      ],
      cateArray: [],
      rules: {
        apiCategoryId: [
          { required: true, message: this.$t('请选择api分类'), trigger: 'blur' }
        ],
        uri: [
          { required: true, message: this.$t('请输入url地址'), trigger: 'blur' },
          { validator: this.urlCheck, trigger: 'blur' }
        ],
        apiName: [
          { required: true, message: this.$t('请输入API名称'), trigger: 'blur' }
        ],
        requestMethod: [
          { required: true, message: this.$t('请选择请求方式'), trigger: 'blur' }
        ]
      },
      apiRuleForm: {
        applicationId: 2,
        apiCategoryId: null,
        apiName: '',
        apiType: 1,
        apiDescription: null,
        uri: null,
        id: null,
        requestMethod: null
      },
      methodsArray: [
        { text: 'GET', value: 'get' },
        { text: 'POST', value: 'post' },
        { text: 'DELETE', value: 'delete' },
        { text: 'PUT', value: 'put' }
      ]
    }
  },
  methods: {
    actionBegin(e) {
      console.log('值变化：', e)
    },
    show() {
      this.$refs.apiRuleFormRef.resetFields()
      this.$refs.serchApiDialog.ejsRef.show()
      this.$nextTick(() => {
        this.apiName = this.apiRuleForm.apiName
      })
    },
    hide() {
      this.$refs.serchApiDialog.ejsRef.hide()
    },
    save() {
      this.$refs.apiRuleFormRef.validate((value) => {
        if (value) {
          this.$emit('addApiSave', this.apiRuleForm)
          this.hide()
        }
      })
    },
    handleClickToolBar(e) {
      const id = e.toolbar.id
      if (id === 'Add') {
        this.addApiDialog()
      }
    },
    addApiDialog() {},
    async getApiCateList() {
      const res = await this.$api.platmenu.queryApiCategryList({})
      this.cateArray = res.data || []
    },
    functionTypeChange(e) {},
    urlCheck(rule, value, callback) {
      const reg = /[\u4e00-\u9fa5]/g
      const ts = /[`~!@#$%^&*()\+=<>?:"{}|,.;'\\[\]·~！@#￥%……&*（）——\+={}|《》？：“”【】、；‘’，。、]/im
      if (reg.test(value) || ts.test(value)) {
        callback(new Error(this.$t('url不可包含中文以及/-_之外的特殊字符')))
      } else {
        callback()
      }
    }

  },
  mounted() {
    this.getApiCateList()
  },
  watch: {
    form: {
      handler: function (value) {
        for (const key in this.apiRuleForm) {
          if (Object.hasOwnProperty.call(this.apiRuleForm, key)) {
            this.apiRuleForm[key] = value[key]
            this.apiRuleForm.requestMethod = value.requestMethod?.toLocaleLowerCase()
          }
        }
      },
      deep: true
    }
  }
}
</script>
<style lang="scss">
.dialog-form-flex .e-dlg-content {
  padding: 40px !important;
  .mt-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 390px;
      .mt-form-item__content {
        width: 100%;
      }
    }
  }
}
</style>
