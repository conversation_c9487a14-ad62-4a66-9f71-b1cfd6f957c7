<template>
  <div class="demo-block">
    <mt-dialog ref="serchDialog" css-class="dialog-form-flex" :header="setObj.header" :buttons="buttons">
         <mt-template-page
          :hiddenTabs="true"
          :templateConfig="componentDataConfig"
          ref="dataTemplatePage"
        >
        </mt-template-page>
    </mt-dialog>
  </div>
</template>

<script>
import { componentDataConfig } from '../config/index'
export default {
  props: {
    setObj: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      parentId: '0',
      componentDataConfig,
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: '取消' }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: '确定' }
        }
      ]
    }
  },
  computed: {
    userInfo() {
      return this.$store.state.user.userInfo
    }
  },
  methods: {
    show(e) {
      this.$refs.serchDialog.ejsRef.show()
    },
    hide() {
      this.$refs.serchDialog.ejsRef.hide()
    },
    save() {
      console.log(this.$refs.dataTemplatePage.getCurrentTabRef().grid.getSelectedRecords())
      const sData = this.$refs.dataTemplatePage.getCurrentTabRef().grid.getSelectedRecords()
      const setArr = []
      sData.forEach(e => {
        e.tenantId = this.userInfo.tenantId
        e.permissionDimensionId = e.id
        e.parentId = this.parentId
        setArr.push(e)
      })
      this.$api.detail.addBatch(setArr).then(() => {
        this.hide()
        this.$parent.refreshMain()
      })
    }
  }
}
</script>
<style lang="scss">
.dialog-form-flex .e-dlg-content {
  padding: 40px !important;
  .mt-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 390px;
      .mt-form-item__content {
        width: 100%;
      }
    }
  }
}
</style>
