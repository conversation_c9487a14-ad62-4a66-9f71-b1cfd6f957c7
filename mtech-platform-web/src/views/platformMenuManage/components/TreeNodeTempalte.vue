<template>
  <div class="treeviewdiv">
    <mt-icon name="icon_Tab" style="margin-right: 6px; color: #6386C1;" v-if="data.code.split('-').length > 2"></mt-icon>
    <mt-icon name="icon_File1" style="margin-right: 6px; color: #EDC951;" v-else></mt-icon>
    
    <span class="treeName">{{ data.name }}</span>
    <span class="tree-node tree-node--cai" 
      v-if="['0', '5', '05-01', '05-01-01', '05-01-02', '05-01-04'].includes(data.code)">采</span>
    <span class="tree-node tree-node--gong"
      v-if="['1', '4', '05-02', '05-01-05'].includes(data.code)"
    >供</span>
    <span style="float: right">
      <mt-icon name="icon_solid_add" class="icon icon-zoomin"></mt-icon>
      <mt-icon name="icon_solid_delete_2" class="icon icon-zoomout"></mt-icon>
    </span>
  </div>
</template>
<script>
export default {
  data() {
    return {
      data: {}
    }
  }
}
</script>

<style scoped lang="scss">
.treeviewdiv {
  width: 100%;
  .tree-node {
    display: inline-block;
    padding: 3px;
    border-radius: 2px;
    margin-left: 8px;
    line-height: 16px;
  }
  .tree-node--cai {
    color: #6386C1;
    background: rgba(99,134,193,0.1);
  }
  .tree-node--gong {
    color: #EDA133;
    background: rgba(237,161,51,0.1);
  }
  .icon {
    margin-right: 24px;
    font-size: 16px;
  }
  .icon-zoomin {
    color: #6386c1;
  }
  .icon-zoomout {
    color: #ed5633;
  }
}
</style>
