<template>
  <div class="platform-version router-page--wrap">
    <mt-template-page
      ref="tempaltePageRef"
      :hiddenTabs="true"
      :templateConfig="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>

    <VersionEditContent
      v-model="isShowContentDialog"
      :data="currentVersionData"
      @add="refreshTempaltePage"
      @update="refreshTempaltePage"
    />

    <VersionMenuConfig
      v-if="isShowMenuDialog"
      v-model="isShowMenuDialog"
      :data="currentVersionData"
      @add="refreshTempaltePage"
      @update="refreshTempaltePage"
    />

    <VersionMenuExport
      v-if="isShowExportDialog"
      v-model="isShowExportDialog"
      :data="currentVersionData"
    />
    <VersionMenuImport v-if="isShowImportDialog" v-model="isShowImportDialog"></VersionMenuImport>
  </div>
</template>
<script lang="ts">
import { Component, Vue } from '@mtech/vue-property-decorator'
import { versionPageConfig } from './config/index'
import VersionEditContent from './components/VersionEditContent.vue'
import VersionMenuConfig from './components/VersionMenuConfig.vue'
import VersionMenuExport from './components/VersionMenuExport.vue'
import VersionMenuImport from './components/VersionMenuImport.vue'

@Component({
  components: {
    VersionEditContent,
    VersionMenuConfig,
    VersionMenuExport,
    VersionMenuImport
  }
})
export default class PlatformPermission extends Vue {
  pageConfig: any[] = versionPageConfig
  currentVersionData: any = {}
  isShowContentDialog = false
  isShowMenuDialog = false
  isShowExportDialog = false
  isShowImportDialog = false

  handleClickToolBar(e: any) {
    console.log('use-handleClickToolBar', e)
    const { toolbar, grid } = e
    if (toolbar.id === 'MenuConfig') {
      const data = grid.getSelectedRecords()
      if (data.length === 1) {
        this.isShowMenuDialog = true
        this.currentVersionData = data[0]
      } else {
        this.$toast({
          content: '请选择唯一一个版本进行菜单配置',
          type: 'warning'
        })
      }
    } else if (toolbar.id === 'Version') {
      this.isShowContentDialog = true
      this.currentVersionData = {}
    } else if (toolbar.id === 'ExportMenu') {
      const data = grid.getSelectedRecords()
      if (data.length === 1) {
        this.isShowExportDialog = true
        this.currentVersionData = data[0]
      } else {
        this.$toast({
          content: '请选择唯一一个版本进行菜单导出',
          type: 'warning'
        })
      }
    } else if (toolbar.id === 'ImportMenu') {
      // 导入
      this.isShowImportDialog = true
    } else if (toolbar.id === 'MenuDispatch') {
      // 菜单下发
      const data = grid.getSelectedRecords()
      if (data.length > 0) {
        this.$dialog({
          data: {
            title: '提示',
            message: '确认下发菜单？'
          },
          success: () => {
            this.handleDispatchMenu(data)
          }
        })
      } else {
        this.$toast({
          content: '请选择版本进行菜单下发',
          type: 'warning'
        })
      }
    }
  }

  handleClickCellTool(e: any) {
    const { data, tool } = e
    if (tool.id === 'edit') {
      this.isShowContentDialog = true
      this.currentVersionData = data
    } else if (tool.id === 'delete') {
      this.deleteVersion(data)
    }
  }

  handleClickCellTitle(e: any) {
    if (e.field === 'no') {
      this.$router.push({
        path: '/platform/detail'
      })
    }
  }

  refreshTempaltePage() {
    const ref = this.$refs.tempaltePageRef as any
    ref.refreshCurrentGridData()
  }

  private deleteVersion(data: any) {
    this.$api.platmenu
      .versionDelete({
        id: data.id
      })
      .then((res: any) => {
        if (res.code === 200) {
          this.$toast({
            content: res.msg || '版本删除成功',
            type: 'success'
          })
          this.refreshTempaltePage()
        }
      })
      .catch((error: any) => {
        this.$toast({
          content: error.msg || '版本删除失败',
          type: 'error'
        })
      })
  }

  private handleDispatchMenu(data: any[]) {
    let params = data.map((item) => {
      return {
        versionId: item.id
      }
    })
    this.$api.platmenu
      .versionDispatch(params)
      .then((res: any) => {
        if (res.code === 200) {
          this.$toast({
            content: res.msg || '菜单下发成功',
            type: 'success'
          })
          this.refreshTempaltePage()
        }
      })
      .catch((error: any) => {
        this.$toast({
          content: error.msg || '菜单下发失败',
          type: 'error'
        })
      })
  }
}
</script>
<style lang="scss"></style>
