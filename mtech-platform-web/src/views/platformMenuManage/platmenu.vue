<template>
  <div class="platmenu-page--wrap">
    <div class="platmenu-page--header">
      <!-- <p class="header-first--wrap mb-10">
        <span class="title mr-10">A0001</span>
        <span class="version mr-20">V 1.0.1</span>
        <span class="name mr-20">
          <mt-icon name="icon_Creator"></mt-icon> - {{userInfo}}</span
        >
        <span class="date mr-20">
          <mt-icon name="icon_Date"></mt-icon> - 2020/12/11 15：45：01</span
        >
        <span class="date">
          <mt-icon name="icon_Date"></mt-icon> - 2020/12/11 15：45：01</span
        >
      </p> -->

      <p class="header-second--wrap">
        <mt-select
          :width="90"
          :dataSource="dataArr"
          :showClearButton="false"
          :fields="{ text: 'applicationName', value: 'id' }"
          cssClass="selectClass"
          v-model="applicationId"
          :change="arrChange"
        ></mt-select>
      </p>

      <div class="header-third--wrap">
        <!-- <span class="mr-20"> <mt-icon name="icon_solid_Configuration"  class="mr-8"></mt-icon>菜单配置</span>
        <span> <mt-icon name="icon_solid_Delete" class="mr-8" ></mt-icon>删除</span> -->
      </div>
    </div>

    <div class="platmenu--wrap">
      <div class="tree-view--wrap">
        <span
          class="mr-20 addClass"
          @click="addMainNode"
          style="cursor: pointer"
        >
          <mt-icon name="icon_solid_Configuration" class="mr-8"></mt-icon>
          新增根节点</span
        >
        <mt-common-tree
          v-if="treeViewDataShow"
          ref="treeViewMenu"
          class="tree-view--template"
          :fields="treeViewData"
          :enablePersistence="true"
          @onButton="onButton"
          @nodeSelected="nodeSelected"
        ></mt-common-tree>
      </div>

      <div class="platmenu-content">
        <mt-template-page
          ref="mainTemplatePage"
          :templateConfig="componentConfig"
          :currentTab="currentTab"
          @handleClickToolBar="handleClickToolBar"
          @handleClickCellTool="handleClickCellTool"
          @handleClickCellTitle="handleClickCellTitle"
        >
          <user-list slot="slot-0" class="user-list">
            <FormTemplate
              ref="FormTemplate"
              :parentData="setData"
              :treeCodeTree="treeCodeTree"
            />
          </user-list>
        </mt-template-page>
      </div>
      <AddNodeForm v-if="AddNodeFormShow" ref="AddNodeForm"></AddNodeForm>
      <SerchGridDialog :setObj="setObj" ref="SerchGridDialog"></SerchGridDialog>
      <SerchDataGridDialog
        :setObj="setObj"
        ref="SerchDataGridDialog"
      ></SerchDataGridDialog>
      <DataGridDialogEdit ref="DataGridDialogEdit"></DataGridDialogEdit>
      <WordGridDialogEdit ref="WordGridDialogEdit"></WordGridDialogEdit>
    </div>
  </div>
</template>
<script lang="ts">
import FormTemplate from './components/FormTemplate.vue'
import AddNodeForm from './components/AddNodeForm.vue'
import SerchGridDialog from './components/SerchGridDialog.vue'
import SerchDataGridDialog from './components/SerchDataGridDialog.vue'
import DataGridDialogEdit from './components/DataGridDialogEdit.vue'
import WordGridDialogEdit from './components/WordGridDialogEdit.vue'
import { Component, Vue } from '@mtech/vue-property-decorator'
import Bus from '@/utils/bus'
import { treeViewData, componentConfig2 } from './config/index'
import { utils } from '@mtech-common/utils'

// import { strategyListColumnData } from './config'

@Component({
  components: {
    FormTemplate,
    AddNodeForm,
    SerchGridDialog,
    SerchDataGridDialog,
    DataGridDialogEdit,
    WordGridDialogEdit
  }
})
export default class WorkflowList extends Vue {
  componentConfig: any = componentConfig2
  dataArr = []
  applicationId = '10548041430523932'
  treeViewData = treeViewData
  treeCodeTree = {}
  setObj = { header: '' }
  setData = {}
  currentTab = 0
  parentId = '0'
  nodeId = ''
  treeViewDataShow = false
  AddNodeFormShow = false
  userInfo = this.$store.state.user.userInfo

  /**
   * 生命周期
   * **/
  async created() {}

  mounted() {
    Bus.$on('handleClickCellToolFlow', this.handleClickCellTool)
    Bus.$on('handleClickCellTitleFlow', this.handleClickCellTitle)
    this.getApplicationQuery()
  }

  addMainNode() {
    this.AddNodeFormShow = true
    this.$nextTick(() => {
      console.log(this.$refs.AddNodeForm)
      const fuc: any = this.$refs.AddNodeForm
      fuc.isZero = false
      fuc.parentId = 0
      fuc.reserForm()
      fuc.show()
    })
  }

  nodeSelected(e: any) {
    this.currentTab = 0
    this.queryCriteria(e.nodeData)
  }

  // 在点击节点或者初始化的时候调用
  queryCriteria(e: any) {
    this.nodeId = e.id 
    this.treeCodeTree = this.filterTreeByNode(e.id)
    this.$api.detail.queryCriteria({ id: e.id }).then((r: any) => {
      if (r.data) {
        const d: any = r.data[0]
        this.setData = r.data[0]
        // this.$set(this.setData, 'parentName', e.text)
        this.$set(this.setData, 'id', e.id)
        this.parentId = d.id
        if (d.permissionTypeCode !== 'list') {
          this.componentConfig = componentConfig2
        } else {
          this.componentConfig = componentConfig2
          const a: any = this.componentConfig
          a[1].grid.asyncConfig.params.id = e.id
          a[2].grid.asyncConfig.params.id = e.id
          this.componentConfig = a
        }
      }
    })
  }

  onButton(e: any) {
    if (e.onBtn.text === '删除') {
      this.$dialog({
        data: {
          title: '删除',
          message: '是否确认删除？'
        },
        success: () => {
          this.$api.detail
            .deleteBatch({
              ids: [e.id]
            })
            .then((r: any) => {
              console.log(r)
              this.getListElementTree({ applicationId: this.applicationId })
            })
        }
      })
    } else {
      this.AddNodeFormShow = true
      this.$nextTick(() => {
        console.log(this.setData)
        const fuc: any = this.$refs.AddNodeForm
        fuc.isZero = true
        fuc.parentId = e.id
        fuc.menuType = (this.setData as any)?.menu?.menuType
        fuc.show(e)
      })
    }
  }

  addOptSucces() {
    const func: any = this.$refs.treeViewMenu
    // 可在次添加默认选中的节点
    const id: any = this.nodeId || func.getCommonMethods().getTreeData()[0].id
    this.queryCriteria(func.getCommonMethods().getNode(id))
    // func.getCommonMethods().expandAll()
  }

  handleClickToolBar(e: any) {
    console.log('use-handleClickToolBar', e)
    if (e.tabIndex === 0) {
      const fuc: any = this.$refs.FormTemplate
      console.log(fuc.menuRuleForm)
      fuc.save()
    } else if (e.tabIndex === 1) {
      if (e.toolbar.id === 'Add') {
        const func: any = this.$refs.SerchGridDialog
        this.setObj.header = '新增'
        func.parentId = this.parentId
        func.show()
        func.reserForm()
      } else if (e.toolbar.id === 'Delete') {
        const func: any = this.$refs.mainTemplatePage
        // console.log()
        const arr: any = func.getCurrentTabRef().grid.getSelectedRecords()
        const pushArr: any = []
        if (arr.length > 0) {
          arr.forEach((e: any) => {
            pushArr.push(e.permissionId)
          })
        } else {
          this.$toast({
            content: '请至少选择一个！',
            type: 'warning'
          })
        }
        this.delWordG(pushArr)
      }
    } else if (e.tabIndex === 2) {
      if (e.toolbar.id === 'Add') {
        const func: any = this.$refs.SerchDataGridDialog
        this.setObj.header = '新增'
        func.parentId = this.parentId
        func.show()
      } else if (e.toolbar.id === 'Delete') {
        const func: any = this.$refs.mainTemplatePage
        // console.log()
        const arr: any = func.getCurrentTabRef().grid.getSelectedRecords()
        const pushArr: any = []
        if (arr.length > 0) {
          arr.forEach((e: any) => {
            pushArr.push(e.id)
          })
        } else {
          this.$toast({
            content: '请至少选择一个！',
            type: 'warning'
          })
        }
        this.delDataG(pushArr)
      }
    }
  }

  // 删除字段权限
  delWordG(pushArr: any) {
    this.$dialog({
      data: {
        title: '删除',
        message: '是否确认删除？'
      },
      success: () => {
        this.$api.detail
          .deletePermission({
            ids: pushArr
          })
          .then((r: any) => {
            console.log(r)
            this.refreshMain()
          })
      }
    })
  }

  // 删除数据权限
  delDataG(pushArr: any) {
    this.$dialog({
      data: {
        title: '删除',
        message: '是否确认删除？'
      },
      success: () => {
        this.$api.detail
          .deleteDataBatch({
            ids: pushArr
          })
          .then((r: any) => {
            console.log(r)
            this.refreshMain()
          })
      }
    })
  }

  // 刷新main列表
  refreshMain() {
    const func: any = this.$refs.mainTemplatePage
    func.refreshCurrentGridData()
  }

  // 行内编辑删除操作
  handleClickCellTool(e: any) {
    console.log('use-handleClickCellTool', e)
    if (e.tabIndex === 1) {
      if (e.tool.id === 'delete') {
        this.delWordG([e.data.permissionId])
      } else if (e.tool.id === 'edit') {
        const func: any = this.$refs.WordGridDialogEdit
        func.parentData = e.data
        func.show()
      }
    } else if (e.tabIndex === 2) {
      if (e.tool.id === 'delete') {
        this.delDataG([e.data.id])
      } else if (e.tool.id === 'edit') {
        const func: any = this.$refs.DataGridDialogEdit
        func.parentData = e.data
        func.show()
      }
    }
  }

  handleClickCellTitle(e: any) {
    console.log('use-handleClickCellTitle', e)
    if (e.field === 'column1') {
      // todo title click
    }
  }

  handleGridCurrentChange(data: any) {
    console.log('use-handleGridCurrentChange', data)
    this.fetchGridData({ currentPage: data.currentPage - 1 })
  }

  handleGridSizeChange(data: any) {
    console.log('use-handleGridSizeChange', data)
    this.fetchGridData({
      count: data.count
    })
  }

  arrChange(e: any) {
    console.log('change', e)
    this.treeViewDataShow = false
    this.getListElementTree({ applicationId: e.value })
  }

  async getApplicationQuery() {
    const res = await this.$api.detail.getApplicationQuery({
      applicationCode: '',
      applicationDescription: '',
      applicationName: ''
    })

    if (res.code === 200 && res.data) {
      this.dataArr = res.data
      const a: any = this.dataArr[0]
      this.applicationId = a.id
    }
  }

  // 查询页面元素树结构
  async getListElementTree(data: any) {
    const res = await this.$api.detail.getListElementTreePlatform(data)
    if (res.code === 200) {
      if (res.data) {
        res.data.forEach((e: any) => {
          if (!e.children) {
            e.children = []
          }
        })
      }
      this.treeViewData = Object.assign({}, this.treeViewData, {
        dataSource: res.data || []
      })
      console.log('start', this.treeViewData)
      this.treeViewDataShow = true
      console.log('end', this.treeViewData)
      this.$nextTick(() => {
        this.addOptSucces()
      })
    }
  }

  async fetchGridData(event: { count?: number; currentPage?: number }) {
    const res = await this.$api.detail.queryApi({
      current: event.currentPage
    })

    if (res.code === 200 && res.data) {
    }
  }

  filterTreeByNode(id: any) {
    const sourceData = utils.cloneDeep(this.treeViewData)
    const parentData:any = [{ id: '0', name: '根节点', parentId: -1, permissionCode: '0', permissionTypeCode: 'menu', permissionTypeName: '菜单', weight: 1 }] 
    parentData[0].children = this.findDataAsc(sourceData.dataSource, id)
    sourceData.dataSource = utils.cloneDeep(parentData)
    return sourceData
  }

  findDataAsc(data: any, id: any) {
    data.forEach((i: any, _i: any) => {
      if (i.id === id) {
        data = data.filter((t: any) => t.id !== i.id)
      }
      if (i.children && i.children.length) {
        i.children = this.findDataAsc(i.children, id)
      }
    })
    return data
  }
}
</script>
<style lang="scss" scoped>
/deep/ .mt-commom-tree-view .action-boxs .btn-box .more {
  left: -70px;
  &::before {
    left: 67px;
  }
}
/deep/ .selectClass {
  border: none !important;
}
.platmenu-page--wrap {
  display: flex;
  height: 100%;
  flex-direction: column;
}
.user-list {
  height: 100%;
  width: 100%;
  background: #fff;
  padding: 20px;
  box-sizing: border-box;
}
.addClass {
  display: inline-block;
  margin: 10px;
}
.platmenu-page--header {
  padding: 20px;
  background: #fff;
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px 8px 0 0;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

  .mr-20 {
    margin-right: 20px;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .mr-8 {
    margin-right: 8px;
  }
  .mb-20 {
    margin-bottom: 20px;
  }
  .mb-10 {
    margin-bottom: 10px;
  }

  .header-first--wrap {
    line-height: 20px;
    .title {
      color: #292929;
      font-size: 20px;
      font-weight: 500;
    }
    .version {
      padding: 3px 6px;
      font-size: 12px;
      color: rgba(237, 161, 51, 1);
      background: rgba(237, 161, 51, 0.1);
      border-radius: 2px;
      display: inline-block;
    }
    .name,
    .date {
      font-size: 14px;
      color: rgba(157, 170, 191, 1);
    }
  }

  .header-second--wrap {
    .title {
      font-size: 14px;
      color: rgba(41, 41, 41, 1);
    }
  }

  .header-third--wrap {
    color: #4f5b6d;
    > span {
      cursor: pointer;
    }
  }
}

.platmenu--wrap {
  flex: 1;
  width: 100%;
  display: flex;
  font-size: 14px;
}

.platmenu-content {
  margin: 0 0 0 10px;
  width: 100%;
  border: 1px solid rgba(232, 232, 232, 1);
  border-top: none;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
}

.grid-tab--wrap {
  border: 1px solid rgba(232, 232, 232, 1);
  padding: 0 30px;
  white-space: nowrap;

  .grid-tab--item {
    display: inline-block;
    color: #9a9a9a;
    margin: 12px 60px 12px 0;
    border: 1px solid transparent;
    border-radius: 4px;
    padding: 6px 10px;

    &.active {
      color: #00469c;
      background: rgba(0, 70, 156, 0.06);
      border-color: rgba(0, 70, 156, 0.1);
    }
  }
}

.tree-view--wrap {

  flex: 0 0 400px;
  background: #fff;
  height: calc(100vh - -62px);
  padding-bottom: 30px;
  overflow: auto;
  border-right: 1px solid #eee;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

  .trew-node--add {
    line-height: 50px;
    padding: 0 20px;
    color: #4f5b6d;
  }

  .tree-view--template {
    width: 100%;
  }

  ::v-deep .e-list-text {
    width: 100% !important;
  }

  /deep/ .e-list-text {
    width: 100% !important;
  }
}

.data-grid--wrap {
  height: calc(100% - 55px);
  overflow: hidden;
}
</style>
