import Vue from 'vue'
// import ListRowTemplate from '@/components/pageComponents/workflow/ListRowTemplate.vue'
import ColumnTimeTemplate from '../../../components/ColumnTimeTemplate.vue'

export const versionPageConfig = [
  {
    useToolTemplate: false,
    toolbar: [
      { id: 'Version', icon: 'icon_solid_Createorder', title: '新增应用版本' },
      { id: 'MenuConfig', icon: 'icon_solid_Configuration', title: '菜单配置' },
      { id: 'ExportMenu', icon: 'icon_solid_export', title: '导出' },
      { id: 'ImportMenu', icon: 'icon_solid_Import', title: '导入' },
      { id: 'MenuDispatch', icon: 'icon_solid_Submit', title: '菜单下发' }
    ],
    grid: {
      columnData: [{
        width: '40',
        type: 'checkbox'
      },
      {
        field: 'no',
        headerText: '版本号',
        cellTools: [
          'edit',
          'delete'
          // { id: 'MenuConfig', icon: 'icon_solid_Configuration', title: '菜单配置' }
        ]
      },
      {
        field: 'name',
        headerText: '版本名称'
      },
      {
        field: 'updateUserName',
        headerText: '操作人'
      },
      {
        field: 'createTime',
        headerText: '发布日期',
        template() {
          return {
            template: ColumnTimeTemplate
          }
        }
      },
      {
        field: 'updateTime',
        headerText: '更新日期',
        template() {
          return {
            template: ColumnTimeTemplate
          }
        }
      },
      {
        field: 'remark',
        headerText: '备注'
      }],
      dataSource: [],
      asyncConfig: {
        url: '/platform/admin/version/query-page',
        methods: 'post',
        params: {
        },
        serializeList(list: any[]) {
          return list.map(v => {
            v.updateUserName = v.updateUserName || v.createUserName
            return v
          })
        }
      }
    }
  }
]

export const menuListToolBar = [
  [
    { id: 'Add', icon: 'icon_solid_Configuration', title: '新增' },
    { id: 'Delete', icon: 'icon_solid_Delete', title: '删除' }
  ],
  [{ id: 'Refresh', icon: 'icon_solid_Refresh', title: '刷新' }]
]
export const menuListColumnData = [
  {
    width: '40',
    type: 'checkbox'
  },
  {
    width: '230px',
    field: 'field',
    headerText: '字段名'
  },
  {
    field: 'grid',
    headerText: '取自表'
  },
  {
    field: 'gridField',
    headerText: '表字段'
  }
]

export const treeViewData = {
  nodeTemplate: function () {
    return {
      template: Vue.component('common', {
        template: `<div class="action-boxs">
                      <div>
                        <span class="tree-node-style" style="background:#EDC951" v-if="mtData.permissionTypeCode == 'menu'">菜单</span>
                        <span class="tree-node-style" style="background:#00469C" v-else-if="mtData.permissionTypeCode == 'tab'">Tab</span>
                        <span class="tree-node-style" style="background:#6386C1" v-else-if="mtData.permissionTypeCode == 'list'">Table</span>
                        <span class="tree-node-style" style="background:#EDA133" v-else-if="mtData.permissionTypeCode == 'opt'">Opt</span>
                        <span>({{mtData.permissionCode}})</span>
                        <span>{{mtData.name}}</span>
                        <span class="tree-node-style2" v-if="mtData.businessType == 1">采</span>
                        <span class="tree-node-style3" v-else-if="mtData.businessType == 2">供</span>
                      </div>
                    </div>`,

        data() {
          return { data: {} }
        },
        props: {
          mtData: {
            type: Object,
            default: () => {}
          }
        }
      })
    }
  },
  dataSource: [
  ],
  id: 'id',
  text: 'name',
  child: 'children',
  iconCss: 'icon',
  imageUrl: 'image'
}

export const componentConfig = [
  {
    title: '基础信息',
    useToolTemplate: false,
    toolbar: {
      useBaseConfig: false, // 代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [[{
        id: 'require-group',
        icon: 'icon_solid_edit',
        title: '保存'
      }], []]
    }
  },
  {
    title: '字段权限',
    toolbar: ['Add', 'Delete'],
    grid: {
      columnData: [
        {
          type: 'checkbox',
          width: 50
        }, {
          field: 'id',
          width: '150',
          headerText: '表编码',
          cssClass: 'user-content-style', // 单元格自定义title样式
          cellTools: ['edit', 'delete']
        },
        {
          field: 'metadataTableName',
          width: '150',
          headerText: '表名',
          valueConverter: {
            type: 'placeholder'
          }
        },
        {
          field: 'fieldName',
          width: '150',
          headerText: '字段名',
          valueConverter: {
            type: 'placeholder'
          }
        },
        {
          field: 'fieldAlias',
          width: '150',
          headerText: '字段别名',
          valueConverter: {
            type: 'placeholder'
          }
        },
        {
          field: 'fieldDescription',
          width: '150',
          headerText: '备注',
          valueConverter: {
            type: 'placeholder'
          }
        }],
      asyncConfig: {
        recordsPosition: 'data',
        url: '/iam/tenant/permission-field/query-by-parent',
        params: { id: 0 }
      }
    }
  },
  {
    title: '数据权限',
    toolbar: ['Add', 'Delete'],
    grid: {
      columnData: [
        {
          type: 'checkbox',
          width: 50
        }, {
          field: 'dimensionCode',
          width: '150',
          headerText: '维度代码',
          cssClass: 'user-content-style', // 单元格自定义title样式
          cellTools: ['edit', 'delete']
        },
        {
          field: 'dimension_name',
          width: '150',
          headerText: '维度名称',
          valueConverter: {
            type: 'placeholder'
          }
        },
        {
          field: 'dimensionDescription',
          width: '150',
          headerText: '维度描述',
          valueConverter: {
            type: 'placeholder'
          }
        },
        {
          field: 'metadataTableFieldCode',
          width: '150',
          headerText: '字段代码',
          valueConverter: {
            type: 'placeholder'
          }
        },
        {
          field: 'fieldAlias',
          width: '150',
          headerText: '字段别名',
          valueConverter: {
            type: 'placeholder'
          }
        },
        {
          field: 'fieldDescription',
          width: '150',
          headerText: '备注',
          valueConverter: {
            type: 'placeholder'
          }
        }],
      asyncConfig: {
        recordsPosition: 'data',
        url: '/iam/tenant/permission-data-item/query-by-parent',
        params: { id: 0 }
      }
    }
  }
]

export const componentConfig2 = [
  {
    title: '基础信息',
    useToolTemplate: false,
    toolbar: {
      useBaseConfig: false, // 代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [[{
        id: 'require-group',
        icon: 'icon_solid_edit',
        title: '保存'
      }], []]
    }
  }
]

export const componentApiConfig = [
  {
    title: 'Api',
    toolbar: [],
    useToolTemplate: false,
    gridId: '10b6ac5e-7cb5-4dc7-9dd8-3526c906f2ea',
    grid: {
      // selectionSettings: { persistSelection: true, checkboxMode: 'ResetOnRowClick', type: 'Single', checkboxOnly: true },
      columnData: [
        {
          type: 'checkbox',
          width: 50
        },
        {
          field: 'apiCategoryName',
          width: '80',
          headerText: 'API分类',
          searchOptions: {
            maxQueryValueLength: 100000
          }
        },
        {
          field: 'uri',
          width: '250',
          headerText: 'URL地址'
        },
        {
          field: 'requestMethod',
          width: '80',
          headerText: '请求方式'
        },
        {
          field: 'apiName',
          width: '150',
          headerText: 'API名称'
        }
      ],
      asyncConfig: {
        loading: false,
        url: '/iam/tenant/api/paged-query',
        params: {}
      }
    }
  }
]
export const componentApiListConfig = [
  {
    title: 'Api',
    toolbar: {
      useBaseConfig: false,
      tools: [['add', 'delete']]
    },
    useToolTemplate: false,
    grid: {
      allowPaging: false,
      columnData: [
        {
          type: 'checkbox',
          width: 50
        },
        {
          field: 'apiCategoryName',
          width: '80',
          headerText: 'API分类'
        },
        {
          field: 'requestMethod',
          width: '80',
          headerText: '请求方式'
        },
        {
          field: 'uri',
          width: '250',
          headerText: 'URL地址'
        },
        {
          field: 'apiName',
          width: '150',
          headerText: 'API名称'
        }
      ],
      dataSource: []
      // asyncConfig: {
      //   loading: false,
      //   url: '/iam/tenant/permission/queryOptApiList',
      //   params: { id },
      //   recordsPosition: 'data'
      // }
    }
  }
]
export const componentTreeConfig = [
  {
    title: '字段权限',
    toolbar: [],
    useToolTemplate: false,
    treeGrid: {
      autoCheckHierarchy: true,
      childMapping: 'tableFieldResponseList',
      // treeColumnIndex: 1,
      columnData: [
        {
          field: 'tableCode',
          headerText: '表编码',
          showCheckbox: true
        },
        {
          field: 'fieldName',
          headerText: '字段名'
        },
        {
          field: 'tableName',
          headerText: '表名'
        }
      ],
      asyncConfig: {
        loading: false,
        url: '/iam/tenant/metadata-table/paged-tree-query',
        params: {},
        serializeList: (arr:any) => {
          arr.forEach((e:any) => {
            console.log('arr.forEach:', e)
            if (e.tableFieldResponseList) {
              e.tableFieldResponseList.forEach((e2:any) => {
                e2.tableCode = e2.fieldName
              })
            }
          })
          return arr
        }
      }
    }
  }
]

export const componentDataConfig = [
  {
    title: '数据权限',
    toolbar: [],
    useToolTemplate: false,
    grid: {
      columnData: [
        {
          type: 'checkbox',
          width: 50
        }, {
          field: 'dimensionCode',
          width: '150',
          headerText: '维度代码'
        },
        {
          field: 'dimensionName',
          width: '150',
          headerText: '维度名称'
        },
        {
          field: 'dimensionDescription',
          width: '150',
          headerText: '维度描述'
        },
        {
          field: 'metadataTableFieldCode',
          width: '150',
          headerText: '字段代码 '
        }
      ],
      asyncConfig: {
        loading: false,
        url: '/iam/tenant/permission-dimension/paged-query',
        params: {}
      }
    }
  }
]
