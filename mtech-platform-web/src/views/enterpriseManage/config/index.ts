import Vue from 'vue'
import ColumnTimeTemplate from '../../../components/ColumnTimeTemplate.vue'

export const enterprisePageConfig = [
  {  
    useToolTemplate: false,
    toolbar: [ 
      { id: 'View', icon: 'icon_solid_Createorder', title: '查看' } 
    ],
    grid: {
      columnData: [{
        width: '40',
        type: 'checkbox'
      },       
      {
        field: 'enterpriseName',
        headerText: '企业名称'
      },
      {
        field: 'code', 
        headerText: '企业编码' 
      }, 
      {
        field: 'defaultAdminName', 
        headerText: '企业管理员' 
      }, 
      {
        field: 'phoneNo', 
        headerText: '联系电话' 
      }, 
      {
        field: 'createUserName', 
        headerText: '创建人' 
      }, 
      {
        field: 'createTime',
        headerText: '创建时间',
        template() {
          return {
            template: ColumnTimeTemplate
          }
        }
      }, 
      {
        field: 'status',
        headerText: '状态',
        valueConverter: {
          type: 'map',
          fields: { text: 'label', value: 'status' },
          map: [      
            { status: -2, label: '创建中', cssClass: ['status', 'status-creating'] },
            { status: -1, label: '注册失败', cssClass: ['status', 'status-failed'] },
            { status: 0, label: '待审批', cssClass: ['status', 'status-unapproval'] },
            { status: 1, label: '注册成功', cssClass: ['status', 'status-success'] }
          ]
        },
        cellTools: [ 
          { id: 'View', icon: 'icon_solid_Submit', title: '查看' }
        ]
      }],
      dataSource: [],
      asyncConfig: {
        url: '/platform/common/enterprise/query',
        methods: 'post',
        params: { 
        } 
      }
    }
  }
]
