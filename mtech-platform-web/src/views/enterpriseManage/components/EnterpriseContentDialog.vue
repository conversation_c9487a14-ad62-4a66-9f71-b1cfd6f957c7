<template> 
  <mt-dialog 
    ref="dialog"
    header="查看"   
    :buttons="buttons"
    @close="hide">  

    <mt-form class="form-content--wrap">
      <mt-row :gutter="20" type="flex">
        <mt-col :span="6">
          <img class="enterprise-logo" :src="formData.enterpriseLogoFileId" />
        </mt-col>

        <mt-col :span="18">
          <mt-row :gutter="20">
            <mt-col :span="12">
              <mt-form-item label="企业名称">
                <mt-input v-model="formData.enterpriseName" :readonly="true" :showClearButton="false"></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :span="12">
              <mt-form-item label="性质">
                <mt-input v-model="formData.enterpriseRegisterTypeName" :readonly="true" :showClearButton="false"></mt-input>
              </mt-form-item> 
            </mt-col>
          </mt-row>
          <mt-row :gutter="20">
           <mt-col :span="12">
             <mt-form-item label="企业编码">
                <mt-input v-model="formData.code" :readonly="true" :showClearButton="false"></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :span="12">
              <mt-form-item label="法人代表">
                <mt-input v-model="formData.corporation" :readonly="true" :showClearButton="false"></mt-input>
              </mt-form-item>
            </mt-col>
          </mt-row>
        </mt-col> 

      </mt-row>

      <mt-row :gutter="20" >
        <mt-col :span="12">
          <mt-form-item label="统一社会信用码">
            <mt-input v-model="formData.identityCode" :readonly="true" :showClearButton="false"></mt-input>
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item label="成立时间">
            <mt-input v-model="formData.createTime" :readonly="true"></mt-input>
          </mt-form-item>
        </mt-col>
      </mt-row>

      <mt-row :gutter="20" >
        <mt-col :span="12">
          <mt-form-item label="注册资金">
            <mt-input v-model="formData.registerCapital" :readonly="true" :showClearButton="false"></mt-input>
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item label="营业期限">
            <mt-input v-model="formData.businessDateRange" :readonly="true"></mt-input>
          </mt-form-item>
        </mt-col>
      </mt-row>

      <mt-row :gutter="20" >
        <mt-col :span="12">
          <mt-form-item label="开户银行">
            <mt-input v-model="formData.bankName" :readonly="true"></mt-input> 
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item label="开户行账号">
            <mt-input v-model="formData.bankAccount" :readonly="true"></mt-input>
          </mt-form-item>
        </mt-col>
      </mt-row> 

      <mt-row :gutter="20" >   
        <mt-col :span="24">
          <mt-form-item label="注册地址"> 
            <mt-row> 
              <mt-col :span="6">
                <mt-input v-model="formData.registerAddressCountryName" :readonly="true"></mt-input>
              </mt-col>

              <mt-col :span="6">
                <mt-input v-model="formData.registerAddressProvinceName" :readonly="true"></mt-input>
              </mt-col>

              <mt-col :span="6">
                <mt-input v-model="formData.registerAddressCityName" :readonly="true"></mt-input>
              </mt-col>

              <mt-col :span="6">
                <mt-input v-model="formData.registerAddressDetail" :readonly="true"></mt-input>
              </mt-col> 
            </mt-row>
          </mt-form-item>
        </mt-col>   
      </mt-row>

      <mt-row :gutter="20" >
        <mt-col :span="24">
          <mt-form-item label="经营范围">
            <mt-input v-model="formData.businessScope" :readonly="true"></mt-input>
          </mt-form-item>
        </mt-col> 
      </mt-row>

      <mt-row :gutter="20" >
        <mt-col :span="24">
          <mt-form-item label="类型"> 
            <BusinessType :disabled="true" :type="formData.businessType" />
          </mt-form-item>
        </mt-col> 
      </mt-row>

      <div class="subtitle--wrap">
        <div class="title">资质附件</div>
      </div>

      <div class="enterprise-proof--wrap">
        <div class="proof" v-for="proof in formData.enterpriseInfoProofFileDTOList" :key="proof.fileId" >
          <img :src="proof.fileUrl" />
        </div> 
      </div> 

      <div class="subtitle--wrap">
        <div class="title">公司联系人信息</div>
      </div>

      <mt-row :gutter="20" >
        <mt-col :span="12">
          <mt-form-item label="姓名">
            <mt-input v-model="formData.defaultAdminName" :readonly="true"></mt-input>
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item label="联系电话">
            <mt-input v-model="formData.defaultAdminPhone" :readonly="true"></mt-input>
          </mt-form-item>
        </mt-col>
      </mt-row>

      <mt-row :gutter="20" >
        <mt-col :span="12">
          <mt-form-item label="管理员账号">
            <mt-input v-model="formData.defaultAdminAccountId" :readonly="true"></mt-input>
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <!-- <mt-form-item label="账号密码">
            <mt-input v-model="formData.enterpriseName" :readonly="true"></mt-input>
          </mt-form-item> -->
        </mt-col>
      </mt-row>

    </mt-form>
    
  </mt-dialog> 
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from '@mtech/vue-property-decorator'  
import BusinessType from '../../personal/components/createEnterprise/BusinessType.vue'

@Component({
  components: { 
    BusinessType
  }
})
export default class EnterpriseContentDialog extends Vue {
  @Prop()
  value !: boolean 

  @Prop() 
  id !: string

  formData: any = {}

  buttons = [
    {
      click: this.hide,
      buttonModel: { content: '取消' }
    },
    {
      click: this.handleConfirm,
      buttonModel: { isPrimary: 'true', content: '确定' }
    }
  ] 

  get visible() {
    return this.value
  }

  set visible(v: boolean) {
    this.$emit('input', v)
  }
 
  @Watch('visible') 
  onWatchVisible(val: boolean) {
    if (val) {
      this.show()
    } 
  }

  hide() {
    this.visible = false
    const ref: any = this.$refs.dialog
    ref.ejsRef.hide()
  }

  show() {   
    this.getEnterpriseDetail()
    const ref: any = this.$refs.dialog
    ref.ejsRef.show()
  }

  handleConfirm() {   
  }

  private getEnterpriseDetail() {
    this.$api.enterprise.enterpriseDetail({
      id: this.id
    }).then((res: { code: number; data: any }) => {
      if (res.code === 200 && res.data) {
        const { 
          businessStartDate = '', 
          businessEndDate = '', 
          enterpriseLogoFileId = '',
          // enterpriseInfoIdentityDTOList = [], 
          enterpriseInfoInvoiceDTOList = [],
          // enterpriseInfoProofFileDTOList = [],
          longTermFlag 
        } = res.data

        this.formData = res.data
        // this.formData.identityCode = enterpriseInfoIdentityDTOList[0]?.identityCode
        this.formData.businessDateRange = businessStartDate + ' 至 ' + (longTermFlag === 1 ? '永久' : businessEndDate)
        this.formData.bankName = enterpriseInfoInvoiceDTOList[0]?.bankName
        this.formData.bankAccount = enterpriseInfoInvoiceDTOList[0]?.bankAccount
        // this.formData.proofFileList = enterpriseInfoProofFileDTOList || []

        this.setLogoFileId(enterpriseLogoFileId)
        this.setProofFileList()
      }
    })
  }

  private setProofFileList() {
    // const urlList:string[] = []
    (this.formData.enterpriseInfoProofFileDTOList || []).forEach(async (item: any) => {
      // const url = await this.$api.file.imageUrl({ id: item.fileId }) 
      const url = await this.getImageUrl(item.fileId)
      // urlList.push(url)
      this.$set(item, 'fileUrl', url)
    })  
    // this.formData.proofFileList = urlList
  }

  private async setLogoFileId(id: string) {
    const url = await this.getImageUrl(id) 
    this.formData.enterpriseLogoFileId = url
  }

  private async getImageUrl(id: string) {
    const url = await this.$api.file.imageUrl({ id: id }) 
    return url?.data || ''
  }
}

</script>

<style lang="scss" scoped>
.form-content--wrap {
  margin-top: 20px;
}
.enterprise-logo {
  height: 128px;
  border: 1px dashed #E8E8E8;
  width: 180px;
  background: #FBFCFD;
}  

.subtitle--wrap {
  margin-bottom: 20px;
  .title {
    color: #292929;
    font-weight: 500;
    &::before {
      content: " ";
      border-left: 2px solid #00469C;
      border-top-left-radius: 1px;
      border-bottom-left-radius: 1px;
      margin-right: 10px;
    }
  }
}
.enterprise-proof--wrap {
  margin-top: 20px;
  display: flex;
  .proof {
    margin: 0 28px 28px 0;
    border: 1px solid rgba(226,225,225,1);
    border-radius: 4px;
    width: 200px;
    height: 125px;
    text-align: center;
  }
  img {    
    max-width: 100%;
    max-height: 100%; 
  }
}
</style>
