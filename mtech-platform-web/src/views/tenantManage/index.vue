<template>
  <div class="tenant-manage router-page--wrap">
    <mt-template-page
      ref="tempaltePageRef"
      :hiddenTabs="true"
      :templateConfig="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool" 
    > 
    </mt-template-page> 

    <TenantAddDialog 
      v-model="isShowAddDialog" 
      @save="refreshTempaltePage"
     />

     <TenantAuthDialog
        v-if="isShowAuthDialog"
        v-model="isShowAuthDialog"
        :id="currTenantData.id"
        @refresh="refreshTempaltePage"
      />
  
  </div>
</template>
<script lang="ts">
import { Component, Vue } from '@mtech/vue-property-decorator'  
import { tenantPageConfig } from './config/index'
import TenantAddDialog from './components/TenantAddDialog.vue'
import TenantAuthDialog from './components/TenantAuthDialog.vue'
// import { strategyListColumnData, strategyListToolBar, getStrategyListGridData } from './mock/config'

@Component({
  components: { 
    TenantAddDialog,
    TenantAuthDialog
  }
})
export default class TenantManage extends Vue {
  pageConfig: any[] = tenantPageConfig

  isShowAddDialog = false  
  isShowAuthDialog = false
  currTenantData :any = {} 
 
  handleClickToolBar(e: any) { 
    const { toolbar, grid } = e
    const selected = grid.getSelectedRecords()
    if (toolbar.id === 'Add') { 
      this.showAddTenantDialog()
      return
    } 
    if (selected.length === 1) {
      this.disOrEnableTenant(selected[0].id, toolbar.id === 'Enable' ? 1 : 0)
    } else {
      this.$toast({
        content: '请选择一条数据',
        type: 'warning'
      })  
    }
  }

  handleClickCellTool(e: any) {  
    const { tool, data } = e
    if (tool.id === 'AuthConfig') {
      this.showAuthConfigDialog(data)
    }
  }

  handleClickCellTitle(e: any) {
    console.log('use-handleClickCellTitle', e)
    if (e.field === 'column1') {
      // todo title click
    }
  }

  refreshTempaltePage() {
    const ref = this.$refs.tempaltePageRef as any
    ref.refreshCurrentGridData()
  }

  private showAddTenantDialog() {
    this.isShowAddDialog = true
  }

  private showAuthConfigDialog(data: any) {
    this.isShowAuthDialog = true
    this.currTenantData = data
  }

  private disOrEnableTenant(id: string, enableFlag: number) {
    this.$api.tenant.tenantDisOrEnable({
      id,
      enableFlag
    }).then((res:any) => {
      if (res.code === 200) { 
        this.refreshTempaltePage()
        this.$toast({
          content: res.msg || `${enableFlag === 0 ? '禁用' : '启用'}成功`,
          type: 'success'
        })  
      }
    }).catch((err:any) => {
      this.$toast({
        content: err.msg || `${enableFlag === 0 ? '禁用' : '启用'}失败`,
        type: 'error'
      })  
    })
  }
}
</script>
<style lang="scss" scoped > 
.tenant-manage { 
  .tenant-status--dot {
    content: ' ';
    width: 8px;
    height: 8px;
    margin-right: 8px;
    border-radius: 50%;
    display: inline-block;
  }
  /deep/ .status {
    border-radius: 3px;
    padding: 2px 3px;
    margin-bottom: 4px;
    display: inline-block;
  }

  /deep/ .status-cooperation {
    color: #6386c1;
    background: #eef2f9;
  }

  /deep/ .status-uncooperation {
    color: #9baac1;
    background: #f5f6f8;
  }
 
  /deep/  .status-valid {
    color: #6386c1;
    &::before {
      @extend .tenant-status--dot;
      background: #6386c1;
    }
  }

  /deep/ .status-unvalid {
    color: #9baac1;
    &::before {
      @extend .tenant-status--dot;
      background: #9baac1;
    }
  }
}

</style>
