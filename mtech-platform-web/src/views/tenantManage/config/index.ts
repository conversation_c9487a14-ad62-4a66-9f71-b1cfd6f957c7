import Vue from 'vue'
import StatusTenant from '../components/StatusTenant.vue'
import ColumnTimeTemplate from '../../../components/ColumnTimeTemplate.vue'

export const tenantPageConfig = [
  {   
    toolbar: [ 
      'add', 
      { id: 'Disable', icon: 'icon_solid_Cancel', title: '禁用' },
      { id: 'Enable', icon: 'icon_solid_Submit', title: '启用' } 
    ],
    
    grid: {
      // selectionSettings: { checkboxOnly: true },
      columnData: [
        {
          width: '40',
          type: 'checkbox'
        },
        {
          field: 'code',
          headerText: '租户编码' 
        },
        {
          field: 'name',
          headerText: '租户名称'
        },
        {
          field: 'businessType',
          headerText: '类型',
          valueConverter: {
            type: 'map',
            // fields: { text: 'label', value: 'businessType' },
            map: [
              { value: 1, text: '供应商', cssClass: [] },
              { value: 2, text: '采购商', cssClass: [] },
              { value: 3, text: '供应商， 采购商', cssClass: [] }
            ]
          } 
        }, 
        {
          field: 'validStartDate',
          headerText: '开始时间',
          template() {
            return {
              template: ColumnTimeTemplate
            }
          }
        },
        {
          field: 'validEndDate',
          headerText: '截止时间',
          template() {
            return {
              template: ColumnTimeTemplate
            }
          }
        },
        {
          field: 'updateUserName',
          headerText: '操作人'
        },
        {
          field: 'updateTime',
          headerText: '更新日期',
          template() {
            return {
              template: ColumnTimeTemplate
            }
          }
        },
        {
          field: 'status',
          headerText: '权限分配状态',
          valueConverter: {
            type: 'map',
            fields: { text: 'label', value: 'status' },
            map: [ 
              { status: 0, label: '待审核', cssClass: ['status', 'status-uncooperation'] },
              { status: 1, label: '审核通过', cssClass: ['status', 'status-cooperation'] },
              { status: 2, label: '未通过', cssClass: ['status', 'status-uncooperation'] },
              { status: -1, label: '未分配', cssClass: ['status', 'status-uncooperation'] }
            ]
          }, 
          cellTools: [ 
            { id: 'AuthConfig', icon: 'icon_solid_Configuration', title: '权限分配' }
          ]
        },  
        {
          field: 'enableFlag',
          headerText: '租户状态', 
          valueConverter: {
            type: 'map',
            fields: { text: 'label', value: 'status' },
            map: [ 
              { status: 0, label: '无效', cssClass: ['tenant-status', 'status-unvalid'] },
              { status: 1, label: '有效', cssClass: ['tenant-status', 'status-valid'] } 
            ]
          } 
        }
      ],
      dataSource: [],
      asyncConfig: {
        url: '/platform/admin/tenant/query-page',
        methods: 'post',
        params: { 
        },
        serializeList(list: any[]) { 
          return list.map(v => {
            // v.businessTypeName = v.businessType === 1 ? '采方' : '供方'
            v.updateUserName = v.updateUserName || v.createUserName 
            return v
          })
        }
      }
    }
  }
]

export const enterprisePageConfig = [
  {
    grid: {
      columnData: [
        {
          width: '40',
          type: 'checkbox'
        },
        {
          field: 'enterpriseName',
          headerText: '企业名称' 
        },
        {
          field: 'enterpriseShortName',
          headerText: '企业简称'
        },
        {
          field: 'enterpriseTypeName',
          headerText: '企业类型' 
        },
        {
          field: 'businessType',
          headerText: '类型',
          valueConverter: {
            type: 'map',
            fields: { text: 'label', value: 'businessType' },
            map: [
              { businessType: 1, label: '供应商', cssClass: [] },
              { businessType: 2, label: '采购商', cssClass: [] },
              { businessType: 3, label: '供应商， 采购商', cssClass: [] }
            ]
          } 
        }
      ],
      dataSource: [],
      asyncConfig: {
        url: '/platform/admin/enterprise/availableList',
        methods: 'post',
        defaultRules: [ 
          {
            condition: 'and',
            field: 'converted_tenant_id',
            operator: 'equal',
            type: 'int',
            value: -99  
          },
          {
            condition: 'and',
            field: 'joined_tenant_id',
            operator: 'equal',
            type: 'int',
            value: -99  
          },
          {
            condition: 'and',
            field: 'status',
            operator: 'equal',
            type: 'int',
            value: 1  
          }
        ],
        params: {}
      }
    }
  }
]
