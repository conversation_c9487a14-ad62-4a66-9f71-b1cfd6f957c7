<template>
  <div class="action-boxs">
    <template v-if="data.isPay">
      <span class="status-dot status-yes-dot"></span>
      <span class="status-yes">是</span>
    </template>
    <template v-if="!data.isPay">
      <span class="status-dot status-no-dot"></span>
      <span class="status-no">否</span>
    </template>
  </div>
</template>

<script>
export default {
  data() {
    return {
      data: {}
    }
  },
  methods: {
    clickTabMore() {
      console.log(this.data)
    }
  }
}
</script>

<style lang="scss" scoped>
.status-dot {
  border: 3px solid #fff;
  border-radius: 50%;
  margin-right: 6px;
  display: inline-block;
  width: 0;
  height: 0;
}
.status-no-dot {
  border-color: rgba(154, 154, 154, 1);
}
.status-yes-dot {
  border-color: rgba(138, 204, 64, 1);
}
.status-yes {
  color: rgba(138, 204, 64, 1);
}
.status-no {
  color: rgba(154, 154, 154, 1);
}
</style>
