<template>
  <div class="action-boxs">
    <span v-if="data.status === 0" class="status status-uncooperation">待审核</span>
    <span v-if="data.status === 1" class="status status-cooperation">审核通过</span>
    <span v-if="data.status === 2" class="status status-uncooperation">未通过</span>
    <span v-if="data.status === 3" class="status status-uncooperation">未分配</span>
  </div>
</template>

<script>
export default {
  data() {
    return {
      data: {}
    }
  },
  methods: {
    clickTabMore() {
      console.log(this.data)
    }
  }
}
</script>

<style lang="scss" scoped>
.status {
  border-radius: 3px;
  padding: 5px 10px;
}

.status-cooperation {
  color: #6386c1;
  background: #eef2f9;
}

.status-uncooperation {
  color: #9baac1;
  background: #f5f6f8;
}
</style>
