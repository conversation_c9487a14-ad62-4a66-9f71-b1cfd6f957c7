<template> 
  <TreeTransferLayout 
    v-model="visible" 
    :id="formData.versionId" 
    :sourceField="sourceTreeField"
    :targentField ="treeField"
    :sourceDataList="sourceTreeDataSource"
    :targetDataList="targetTreeDataSource"
    @save="handleSaveAuth"
   >
    <mt-form ref="form" class="content" :model="formData" :rules="formRules" > 
      <mt-row :gutter="50">
        <mt-col :span="12">
          <mt-form-item label="开通时间" prop="range" >
            <mt-date-range-picker  v-model="formData.range" separator="至" placeholder="选择开通时间"></mt-date-range-picker> 
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item label="选择版本" prop="versionId" >
            <mt-select 
              v-model="formData.versionId"
              :dataSource="selectDataSource" 
              placeholder="请选择版本"
              @change="changeVersion"
            ></mt-select>
          </mt-form-item>          
        </mt-col>
      </mt-row>

      <mt-row :gutter="50">
        <mt-col :span="12">
          <mt-form-item label="租户编码" prop="tenantCode" >
            <mt-input 
              v-model="formData.tenantCode"  
              placeholder="租户编码" />
          </mt-form-item>
        </mt-col> 
        <mt-col :span="12">
          <mt-form-item label="类型" prop="businessType" >
            <mt-input 
              v-model="formData.businessTypeName"  
              :readonly="true"
              :showClearButton="false"
              placeholder="类型" />
          </mt-form-item>
        </mt-col> 
      </mt-row> 
<!-- 
      <mt-row :gutter="50"> 
        <mt-col :span="12">
          <mt-form-item label="独立账户" prop="singleAccountFlag" >
            <mt-radio 
              v-model="formData.singleAccountFlag" 
              :dataSource="radioDataSource" ></mt-radio>
          </mt-form-item>
        </mt-col>
      </mt-row>  -->

    </mt-form> 
  </TreeTransferLayout>
</template>

<script lang="ts">
import { Component, Vue, Prop } from '@mtech/vue-property-decorator'  
import TreeTransferLayout from '@/components/TreeTransferLayout.vue'   
import dayjs from 'dayjs'

@Component({
  components: { 
    TreeTransferLayout
  }
})
export default class TenantAuthDialog extends Vue {
  @Prop()
  value !: boolean 

  @Prop() 
  id !: string

  formData:any = {
    versionId: '',
    tenantCode: ''
    // singleAccountFlag: '0'
  }

  formRules = {
    range: [
      { required: true, validator: this.validateRange, trigger: 'blur' }
    ], 
    tenantCode: [
      { required: true, message: '请输入租户编码', trigger: 'blur' }
    ]
  }

  radioDataSource = [
    { label: '是', value: '1' },
    { label: '否', value: '0' }
  ]

  selectDataSource: any[] = [] 

  treeField = {
    id: 'permissionId',
    text: 'permissionName',
    parentID: 'parentPermissionId',  
    hasChildren: 'hasChildren'
  }

  sourceTreeField = {
    id: 'permissionId',
    text: 'permissionName',
    parentID: 'parentPermissionId',  
    hasChildren: 'hasChildren',
    applicationId: 'applicationId',
    applicationName: 'applicationName',
    permissionType: 'permissionType'
  }

  sourceTreeDataSource:any[] = []

  targetTreeDataSource:any[] = []
  currApplication: any = {}

  get visible() {
    if (this.value) {
      this.getVersionList()
      this.getTenantVersionQuery()
    }
    return this.value
  }

  set visible(v: boolean) {
    this.$emit('input', v)
  } 

  changeVersion(event: any) {  
    this.getVersionPermission(event.value)  
  }

  handleSaveAuth(versions: any[]) {
    // debugger
    (this.$refs.form as any).validate((valid: any) => {
      if (valid) {
        this.saveAuth(versions)
      }
    })
  }

  saveAuth(params: any[]) {
    // debugger
    this.$api.tenant.tenantVersionAssign(this.formatParam(params)).then((res: { code: number; msg: any }) => {
      if (res.code === 200) {
        this.$toast({
          content: res.msg || '权限分配成功',
          type: 'success'
        }) 
        this.visible = false
        this.$emit('refresh')
      }
    }).catch((error: any) => {
      this.$toast({
        content: error.msg || '权限分配失败',
        type: 'error'
      }) 
    })
  }

  private formatParam(versions: any[]) {
    const params = { 
      // singleAccountFlag: Number(this.formData.singleAccountFlag),
      tenantCode: this.formData.tenantCode, 
      versionId: this.formData.versionId,
      validStartDate: dayjs(this.formData.range[0]).format('YYYY-MM-DD'),
      validEndDate: dayjs(this.formData.range[1]).format('YYYY-MM-DD'),
      selectedVersionList: versions,
      tenantId: this.id
    } 
    
    return params
  }
 
  private getVersionList() {
    this.$api.tenant.versionDropdownList().then((res: any) => {
      if (res.code === 200) {
        this.selectDataSource = res.data?.map((v: { name: any; id: any }) => {
          return {
            text: v.name,
            value: v.id
          }
        })
      }
    })
  }

  // 租户权限分配详情
  private getTenantVersionQuery() {
    this.$api.tenant.tenantVersionDetail({
      tenantId: this.id
    }).then((res: { code: number; data: any }) => {
      if (res.code === 200 && res.data) {
        this.formData = {
          // singleAccountFlag: res.data.singleAccountFlag + '',
          businessTypeName: res.data.businessTypeName,
          tenantCode: res.data.tenantCode, 
          versionId: res.data.versionId, 
          range: [res.data.validStartDate, res.data.validEndDate]  
        }
        this.targetTreeDataSource = res.data.permissions || [] 
      }
    })
  }

  // 获取该版本下的所有权限
  private getVersionPermission(id: string) {
    this.$api.platmenu.versionPermissionFind({
      id
    }).then((res: { code: number; data: any[] }) => {
      if (res.code === 200) {
        this.sourceTreeDataSource = res?.data || []
      }
    })
  }
 
  validateRange(rule:any, value:any, callback:any) {  
    if (value[0] && value[1]) {  
      callback() 
    } else {      
      callback(new Error('请选择开通时间'))
    }
  }
}

</script>

<style lang="scss"> 
</style>
