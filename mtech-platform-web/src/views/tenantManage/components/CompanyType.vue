<template>
  <div class="action-boxs">
    <span v-if="data.category === 0" >采方</span>
    <span v-if="data.category === 1" >供方</span>
    <span v-if="data.category === 2" >采方、供方</span> 
  </div>
</template>

<script>
export default {
  data() {
    return {
      data: {}
    }
  },
  methods: {
    clickTabMore() {
      console.log(this.data)
    }
  }
}
</script>

<style lang="scss" scoped>
.status {
  border-radius: 3px;
  padding: 5px 10px;
}

.status-cooperation {
  color: #6386c1;
  background: #eef2f9;
}

.status-uncooperation {
  color: #9baac1;
  background: #f5f6f8;
}
</style>
