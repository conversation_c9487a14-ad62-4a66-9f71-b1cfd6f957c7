<template>
  <mt-dialog
    ref="toast"
    v-bind="$attrs"
    :header="header"
    :visible="visible"
    width="900px"
    height="950px"
    :buttons="buttons"
    v-on="$listeners"
    @close="close"
  >
    <div class="dialog-content--wrap"> 

      <div class="dialog-transfer--wrap">
        <template v-if="step === 1">
          <div class="transfer-tree--box">
            <div class="body-item--name">
              <span class="name">{{ leftTitle }}</span>
              <div class="icon">
                <mt-select
                  id="permissionList"
                  :value="appModule.value"
                  :data-source="appModule.data"
                  width="100px"
                  popup-width="120px"
                  float-label-type="Never"
                  placeholder="权限名称" 
                ></mt-select>
                <!-- <mt-icon name="TableAlignLeft"></mt-icon>
                <span>V 5.0</span>
                <mt-icon name="MT_Bottomfil"></mt-icon> -->
              </div>
            </div>

            <div class="body-item--search">
              <mt-icon name="MT_Search" class="search"></mt-icon>
              <mt-input placeholder="请输入搜索内容" float-label-type="Never"> </mt-input>
            </div>

            <div class="transfer-tree">
              <mt-treeView
                ref="leftTree"
                v-bind="$attrs"
                :fields="configLeft"
                :auto-check="autoCheck"
                :show-check-box="true"
                :node-template="treeTempalte"
                v-on="$listeners"
              ></mt-treeView>
            </div>
          </div>

          <div class="transfer-icon--box">
            <div style="width: 30px">
              <mt-button icon-css="e-btn-sb-icons e-input-toleft" css-class="e-small e-round" style="margin: 15px 0" @click="dataToLeft"></mt-button>
              <mt-button
                icon-css="e-btn-sb-icons e-input-toright"
                css-class="e-small e-round"
                style="margin: 15px 0"
                @click="dataToRight"
              ></mt-button>
            </div>
          </div>
        </template>

        <div class="transfer-tree--box">
          <div class="body-item--name">
            <span class="name">{{ rightTitle }}</span>
          </div>
          <div class="body-item--search">
            <mt-icon class="search" name="MT_Search"></mt-icon>
            <mt-input placeholder="请输入搜索内容" float-label-type="Never"> </mt-input>
          </div>

          <div class="transfer-tree">
            <mt-treeView
              ref="rightTree"
              v-bind="$attrs"
              :fields="configRight"
              :auto-check="autoCheck"
              :show-check-box="true"
              v-on="$listeners"
            ></mt-treeView>
          </div>
        </div>

        <template v-if="step === 2">
          <div class="transfer-icon--box">
            <div style="width: 30px">
              <mt-button icon-css="e-btn-sb-icons e-input-toleft" css-class="e-small e-round" style="margin: 15px 0" @click="dataToLeft"></mt-button>
              <mt-button
                icon-css="e-btn-sb-icons e-input-toright"
                css-class="e-small e-round"
                style="margin: 15px 0"
                @click="dataToRight"
              ></mt-button>
            </div>
          </div>

          <div class="transfer-tree--box">
            <div class="body-item--name">
              <span class="name">{{ rightTitle }}</span>
            </div>
            <div class="body-item--search">
              <mt-icon class="search" name="MT_Search"></mt-icon>
              <mt-input placeholder="请输入搜索内容" float-label-type="Never"> </mt-input>
            </div>

            <div class="transfer-tree">
              <mt-treeView
                ref="rightTree"
                v-bind="$attrs"
                :fields="configRight"
                :auto-check="autoCheck"
                :show-check-box="true"
                v-on="$listeners"
              ></mt-treeView>
            </div>
          </div>
        </template>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
/**
 * @description: 获取当前已勾选的末级节点（不包含非末级节点）
 * @param {Array} 全部节点
 * @param {String} 子节点数组的key
 * @param {Array} 已勾选节点
 * @return {Array} 已勾选节点
 */
import AuthTreeTemplate from './AuthTreeTemplate.vue'

const nodeCheckedGetWithoutParent = (nodes, childArrayKey, rst = []) => {
  nodes.forEach((n) => {
    if (n[childArrayKey] && !!n[childArrayKey].length) rst = nodeCheckedGetWithoutParent(n[childArrayKey], childArrayKey, rst)
    else if (n.isChecked) return rst.push(n)
  })
  return rst
}

export default {
  model: {
    prop: 'visible',
    event: 'input'
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    fieldsLeft: {
      type: Object,
      required: false,
      default: () => {
        return {
          dataSource: [],
          id: 'nodeId',
          text: 'nodeText',
          child: 'nodeChild'
        }
      }
    },
    fieldsRight: {
      type: Object,
      required: false,
      default: () => {
        return {
          dataSource: []
        }
      }
    },

    header: {
      type: String,
      required: false,
      default: '此处为弹窗大标题'
    },
    // 允许自动检查全选和反选，参见ejs2文档Properties：autoCheck
    autoCheck: {
      type: Boolean,
      required: false,
      default: () => true
    }
  },
  data() {
    return {
      leftTitle: '权限名称',
      rightTitle: '已分配租户权限',

      rightSerch: '',
      leftSerch: '',
      step: 1,
      buttons: [
        {
          click: () => {
            this.hide()
          },
          buttonModel: { content: '取消' }
        },
        {
          click: () => {
            this.nextStep()
          },
          buttonModel: { isPrimary: 'true', content: '下一步' }
        }
      ],
      firstButtons: [
        {
          click: () => {
            this.hide()
          },
          buttonModel: { content: '取消' }
        },
        {
          click: () => {
            this.save()
          },
          buttonModel: { isPrimary: 'true', content: '确定' }
        }
      ],
      nextButtons: [
        {
          click: () => {
            this.hide()
          },
          buttonModel: { content: '取消' }
        },
        {
          click: () => {
            this.prevStep()
          },
          buttonModel: { isPrimary: 'true', content: '上一步' }
        },
        {
          click: () => {
            this.save()
          },
          buttonModel: { isPrimary: 'true', content: '确定' }
        }
      ],
      appModule: {
        value: '采方普通版',
        data: ['采方普通版', '采方高级版', '采方旗舰版', '供方普通版', '供方高级版', '供方旗舰版'] 
      },

      configLeft: {},
      configRight: {},
      checkedRight: [],
      treeTempalte: () => {
        return {
          template: AuthTreeTemplate
        }
      }
    }
  },
  computed: {
    // buttons() {
    //   if (this.step === 1) return this.firstButtons
    //   if (this.step === 2) return this.nextButtons
    //   return []
    // }
  },
  watch: {
    fieldsLeft() {
      this.configLeft = Object.assign({}, this.fieldsLeft)
    },
    fieldsRight() {
      this.configRight = Object.assign({}, this.fieldsRight)
    },
    step: {
      immediate: true,
      handler(val) {
        if (val === 1) {
          this.buttons = [...this.firstButtons]
        }
        if (val === 2) {
          this.buttons = [...this.nextButtons]
        }
      }
    }
  },
  created() {
    this.configLeft = Object.assign({}, this.fieldsLeft)
    this.configRight = Object.assign({}, this.fieldsRight)
  },
  methods: {
    save() {
      this.$emit('save', this.configRight.dataSource)
      this.hide()
    },
    hide() {
      this.$refs.toast.ejsRef.hide()
      this.$emit('input', false)
    },
    close() {
      this.$emit('input', false)
    },
    nextStep() {
      this.step = 2
    },
    prevStep() {
      this.step = 1
    },
    nodeKeyGet(nodes) {
      return nodes.map((n) => n[this.fieldsLeft.id])
    },
    dataToRight() {
      const currentAllData = this.$refs.leftTree.ejsRef.getTreeData()
      const nodeCheckedWithoutParent = nodeCheckedGetWithoutParent(currentAllData, this.configLeft.child)
      const nodeAdded = []
      nodeCheckedWithoutParent.forEach((e) => {
        const index = this.configRight.dataSource.findIndex((n) => e[this.configLeft.id] === n[this.configLeft.id])
        if (index === -1) nodeAdded.push(e)
      })
      this.configRight.dataSource = this.configRight.dataSource.concat(nodeAdded)
      // FIXME: 目前节点移动到右边后，默认为checked状态，若不需要，注释下面这行代码
      // this.checkedRight = this.nodeKeyGet(this.configRight.dataSource)
    },
    dataToLeft() {
      this.checkedRight.forEach((c) => {
        this.configRight.dataSource = this.configRight.dataSource.filter((e) => c !== e[this.configLeft.id])
      })
      this.checkedRight = []
    }
  }
}
</script>

<style lang="scss" scope>
 
.dialog-content--wrap {
  padding: 20px 0;
  height: 100%;
  display: flex;
  flex-direction: column;

  .display-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .body-item-box {
    height: 40px;
    padding: 0 20px;
    border-bottom: 1px solid #e8e8e8;
  }

  .tab-bar--wrap {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    margin-bottom: 20px;

    .tab-bar--item {
      display: inline-block;
      color: #9a9a9a;
      padding: 3px 5px;
      margin-right: 60px;

      &.active {
        color: #00469c;
        background: rgba(0, 70, 156, 0.06);
        border: 1px solid rgba(0, 70, 156, 0.1);
        border-radius: 4px;
        cursor: pointer;
      }

      &:last-of-type {
        margin-right: 0;
      }
    }
  }

  .dialog-transfer--wrap {
    flex: 1;
    display: flex;
    height: 500px;
    
    .transfer-tree--box {
      flex: 1;
      border: 1px solid #e8e8e8;
      .transfer-tree {
        height: calc(100% - 80px);
        overflow: auto;
      }
    }
    .transfer-icon--box {
      display: flex;
      align-items: center; /*垂直居中*/
      justify-content: center; /*水平居中*/
      width: 60px;
      max-height: 99%;

      .e-input-toleft:before {
        content: '\e904';
        font-family: e-icons;
        font-size: 16px;
        color: #979797;
      }
      .e-input-toright:before {
        content: '\e913';
        font-family: e-icons;
        font-size: 16px;
        color: #979797;
      }
    }
  }

  .body-item--name {
    @extend .display-flex;
    @extend .body-item-box;

    line-height: 40px;

    .name {
      color: #292929;
      font-size: 16px;
    }

    .icon {
      > * {
        margin-left: 6px;
      }
    }
  }

  .body-item--search {
    @extend .display-flex;
    @extend .body-item-box;

    .search {
      margin-right: 12px;
      margin-top: 3px;

      /deep/ .e-input {
        border-bottom: none;
        margin-top: 8px;
      }
    }
  }
}
.mt5 {
  margin-top: 5px;
}
.checkbox-item {
  display: block;
  margin-bottom: 15px;
}

.mt-pagertemplate {
  display: none;
}

#mt-serch {
  box-sizing: border-box;
  border-bottom: 1px solid #e8e8e8;
  margin: 0;
  height: 40px;
  padding-left: 10px;
}
.group-item {
  display: block;
}
</style>
