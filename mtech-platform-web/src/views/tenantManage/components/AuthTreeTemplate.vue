<template>
  <div>
    <div class="treeviewdiv">
      <span class="treeName">{{ data.name }}</span>
      <span v-if="['1', '05-01-01', '05-01-03', '05-01-06'].includes(data.code)" class="nodebadge node-cai"> 采 </span>
      <span v-if="['2', '04-01', '04-02', '05-01-04'].includes(data.code)" class="nodebadge node-gong"> 供 </span>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      data: {}
    }
  }
}
</script>

<style scoped lang="scss">
.treeviewdiv {
  .nodebadge {
    margin-left: 6px;
    line-height: 14px;
    border-radius: 2px;
    display: inline-block;
  }
  .node-cai {
    color: #6386c1;
    background: rgba(99, 134, 193, 0.1);
  }
  .node-gong {
    color: rgba(237, 161, 51, 1);
    background: rgba(237, 161, 51, 0.1);
  }
}
</style>
