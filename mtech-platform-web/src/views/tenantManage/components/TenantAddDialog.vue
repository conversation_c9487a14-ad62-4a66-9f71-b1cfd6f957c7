<template> 
  <mt-dialog 
    ref="dialog"
    header="新增"   
    :buttons="buttons"
    @close="hide"> 
    <mt-template-page
      ref="tempaltePageRef"
      class="tenant-add--dialog"
      :hiddenTabs="true"
      :templateConfig="pageConfig" 
    > 
    </mt-template-page> 
  </mt-dialog> 
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from '@mtech/vue-property-decorator' 
import { enterprisePageConfig } from '../config'

@Component({
  components: { 
  }
})
export default class TenantAddDialog extends Vue {
  @Prop()
  value !: boolean 

  pageConfig = enterprisePageConfig

  buttons = [
    {
      click: this.hide,
      buttonModel: { content: '取消' }
    },
    {
      click: this.handleConfirm,
      buttonModel: { isPrimary: 'true', content: '确定' }
    }
  ] 

  get visible() {
    return this.value
  }

  set visible(v: boolean) {
    this.$emit('input', v)
  }
 
  @Watch('visible') 
  onWatchVisible(val: boolean) {
    if (val) { 
      this.show()
    } 
  }

  hide() {
    this.visible = false
    const ref: any = this.$refs.dialog
    ref.ejsRef.hide()
  }

  show() {   
    const ref: any = this.$refs.dialog
    ref.ejsRef.show()  
  }

  handleConfirm() {  
    const ref = this.$refs.tempaltePageRef as any
    const { grid } = ref?.getCurrentTabRef()
    const selectedList = grid?.getSelectedRecords() || []

    if (selectedList.length === 0) {
      this.hide()
    } else if (selectedList.length === 1) {
      this.$api.tenant.tenantAdd({
        enterpriseId: selectedList[0].id
      }).then((res: { code: number; msg: any; data: any }) => {
        if (res.code === 200) {
          this.$toast({
            content: res.msg || '租户添加成功',
            type: 'success'
          }) 
          this.hide()
          this.$emit('save', res.data)
          ref.refreshCurrentGridData()
        }
      }).catch((err: { msg: any }) => {
        this.$toast({
          content: err.msg || '租户添加失败',
          type: 'error'
        }) 
      })
    } else {
      this.$toast({
        content: '只能选择一条数据',
        type: 'warning'
      }) 
    }
  }
}

</script>

<style lang="scss" scoped> 
.tenant-add--dialog {
  // padding: 40px 22px 0;
}
</style>
