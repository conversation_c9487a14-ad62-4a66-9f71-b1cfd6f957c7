<template>
    <div class="router-page--wrap">
        <mt-template-page
        ref="mainTemplatePage"
        :hiddenTabs="true"
        :templateConfig="templateConfig"
        @handleClickCellTool="handleClickCellTool" 
        >
        </mt-template-page>

        <mt-dialog 
            ref="dialog" 
            css-class="dialog-form-flex" 
            header="个人信息"
            :open="onOpen" >
            <div class="dialog">
                <div class="title">
                    <img :src="dialogData.photo">
                    <div class="name">{{dialogData.userName}}</div>
                    <div class="detail">
                        <div>
                            <mt-icon name="icon_solid_Personal1"></mt-icon>
                            账号：{{dialogData.account}}
                        </div>
                        <div>
                            <!-- TODO -->
                            手机：{{dialogData.telephone}}
                        </div>
                        <div>
                            邮箱：{{dialogData.email}}
                        </div>
                    </div>
                </div>
                <!-- 后台木有返回id呀 -->
                <div class="content" v-for="(item, index) in dialogData.employeeDeatils" :key="index">
                    <div class="top">所属企业{{index + 1}}</div>
                    <div class="employee-deatils">
                        <div class="employee-content">
                            <span>公司名称：</span><br/>
                            <mt-input :readonly="true" :showClearButton="false" type="text" v-model="item.companyName"></mt-input>
                        </div>
                        <div class="employee-content">
                            <span>所属部门：</span><br/>
                            <mt-input :readonly="true" :showClearButton="false" type="text" v-model="item.departmentName"></mt-input>
                        </div>
                        <div class="employee-content">
                            <span>用户角色：</span><br/>
                            <mt-input :readonly="true" :showClearButton="false" type="text" v-model="item.role"></mt-input>
                        </div>
                    </div>
                </div>
            </div>
        </mt-dialog>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from '@mtech/vue-property-decorator'
import ColumnTimeTemplate from '@/components/ColumnTimeTemplate.vue'

@Component({
  components: { 
    
  }
})
export default class User extends Vue {
    dialogData = {}
    templateConfig = [
      { 
        title: '',
        toolbar: [],
        useToolTemplate: false,
        grid: {
          columnData: [
            {
              width: '40',
              type: 'checkbox'
            },
            {
              field: 'index', 
              headerText: '序号' 
            },
            {
              field: 'account', 
              headerText: '用户账号' 
            },
            {
              field: 'userName', 
              headerText: '真实姓名' 
            },
            {
              field: 'telephone', 
              headerText: '联系电话' 
            },
            {
              field: 'userType', 
              headerText: '用户类型',
              valueConverter: {
                type: 'map',
                fields: { text: 'label', value: 'userType' },
                map: [
                  { userType: 1, label: '个人用户', cssClass: ['user-type'] },
                  { userType: 2, label: '企业用户', cssClass: ['user-type', 'user-type-business'] }
                ]
              }
            },
            {
              field: 'defualtEntity', 
              headerText: '所属公司（主）' 
            },
            {
              field: 'entityType', 
              headerText: '公司类型' 
            },
            {
              field: 'role', 
              headerText: '用户角色' 
            },
            {
              field: 'createUserName', 
              headerText: '创建人' 
            },
            {
              field: 'createTime', 
              headerText: '申请时间',
              template() {
                return {
                  template: ColumnTimeTemplate
                }
              }
            },
            {
              field: 'statusId', 
              headerText: '状态',
              valueConverter: {
                type: 'map',
                fields: { text: 'label', value: 'statusId' },
                map: [
                  { statusId: '0', label: '无效', cssClass: ['invalid'] },
                  { statusId: '1', label: '有效', cssClass: ['valid'] }
                ]
              },
              cellTools: [ 
                { id: 'Search', icon: 'icon_solid_examine', title: '查看' }
              ]
            }
          ],
          dataSource: [],
          asyncConfig: {
            url: '/masterDataManagement/findUserPlatformInfos',  
            serializeList: function(data: any) {
              data.forEach((v: any, index: any) => {
                v.index = index + 1
              })
              return data
            }
          }
        }
      }
    ]

    handleClickCellTool(e: any) {
      const { data, tool } = e
      if (tool.id === 'Search') {
        (this as any).dialogData = {} as any
        (this as any).$refs.dialog.ejsRef.show()
        const res = (this as any).$api.enterprise.getUserDetail({
          userId: data.id
        })
        res.then((r: any) => {
          if (r.code === 200 && r.data) {
            (this as any).dialogData = Object.assign({}, data, r.data)
          }
        })
      }
    }

    onOpen(args: any) {
      args.preventFocus = true
    }
}
</script>

<style lang="scss" scoped>
/deep/ .user-type{
    display: inline-block;
    width: 66px;
    height: 20px;
    line-height: 22px;
    color: #EDA133;
    background-color: rgba(237,161,51,0.1);
    border-radius: 2px;
    text-align: center;
}
/deep/ .user-type-business{
    background-color: rgba(99,134,193,0.1);
    color: #6386C1;
}
/deep/ .invalid{
    color: #9A9A9A;
}
/deep/ .valid{
    color: #6386C1;
}
.dialog{
    // padding: 40px;
    .title{
        img{
            width: 60px;
            height: 60px;
            border-radius: 50%;
            float: left;
            margin-right: 20px;
        }
        .name{
            color: #292929;
            font-size: 18px;
            font-weight: bold;
        }
        .detail{
            color: #9A9A9A;
            width: 100%;
            margin-top: 20px;
            div{
                width: 25%;
                display: inline-block;
            }
        }
    }
    .content{
        margin-top: 30px;
        .top{
            color: #292929;
            font-size: 14px;
            padding-left: 10px;
            border-left: 2px solid #00469C;
        }
        .employee-deatils{
            color: #3A3A3A;
            font-size: 14px;
            margin-top: 10px;
            span{
                padding-left: 10px;
            }
            .employee-content{
                width: calc(50% - 10px); 
                display: inline-block;
                margin: 5px 10px 5px 0;
            }
        }
    }
}
</style>
