import Vue from 'vue'

export const treeViewData = {
  nodeTemplate: function () {
    return {
      template: Vue.component('common', {
        template: `<div class="action-boxs">
                      <div>
                        <span class="tree-node-style" style="background:#EDC951" v-if="MData.permissionTypeCode == 'menu'">菜单</span>
                        <span class="tree-node-style" style="background:#00469C" v-else-if="MData.permissionTypeCode == 'tab'">Tab</span>
                        <span class="tree-node-style" style="background:#6386C1" v-else-if="MData.permissionTypeCode == 'list'">Table</span>
                        <span class="tree-node-style" style="background:#EDA133" v-else-if="MData.permissionTypeCode == 'opt'">Opt</span>
                        <span>{{MData.name}}</span>
                        <span class="tree-node-style2" v-if="MData.businessType == 1">采</span>
                        <span class="tree-node-style3" v-else-if="MData.businessType == 2">供</span>
                      </div>
                    </div>`,

        data() {
          return { data: {} }
        },
        props: {
          MData: {
            type: Object,
            default: () => {}
          }
        }
      })
    }
  },
  dataSource: [
  ],
  id: 'id',
  text: 'name',
  child: 'children',
  iconCss: 'icon',
  imageUrl: 'image'
}

export const componentConfig = [
  {
    title: '字段权限',
    toolbar: ['Add', 'Delete'],
    grid: {
      columnData: [
        {
          type: 'checkbox',
          width: 50
        }, {
          field: 'code',
          width: '150',
          headerText: '模板编码',
          cssClass: 'user-content-style', // 单元格自定义title样式
          cellTools: ['edit', 'delete']
        },
        {
          field: 'businessName',
          width: '150',
          headerText: '关联业务类型',
          valueConverter: {
            type: 'placeholder'
          }
        },
        {
          field: 'name',
          width: '150',
          headerText: '模板名称',
          valueConverter: {
            type: 'placeholder'
          }
        },
        {
          field: 'isActive',
          width: '150',
          headerText: '状态',
          valueConverter: {
            type: 'map',
            map: [{ id: true, title: '已发布', cssClass: 'isActiveClass' }, { id: false, title: '草稿', cssClass: 'isActiveClassFalse' }],
            fields: { text: 'title', value: 'id' }
          },
          cellTools: [{ id: 'publish', icon: 'icon_Share_2', title: '发布', visibleCondition: (data:any) => { return data.isActive === false } }]
        },
        {
          field: 'version',
          width: '150',
          headerText: '版本号',
          valueConverter: {
            type: 'placeholder'
          }
        }],
      asyncConfig: {
        recordsPosition: 'data.data',
        url: '/lowcodeWeb/tenant/form-design/queryBuilder/list',
        params: {
          classifyId: '',
          name: '',
          businessType: ''
        }
      }
    }
  }
]

export const componentTemplateConfig = [
  {
    title: '字段权限',
    toolbar: ['Add', 'Delete'],
    grid: {
      columnData: [
        {
          type: 'checkbox',
          width: 50
        }, {
          field: 'code',
          width: '150',
          headerText: '模板编码',
          cssClass: 'user-content-style', // 单元格自定义title样式
          cellTools: ['edit', 'delete']
        },
        {
          field: 'name',
          width: '150',
          headerText: '模板名称',
          valueConverter: {
            type: 'placeholder'
          }
        },
        {
          field: 'isActive',
          width: '150',
          headerText: '状态',
          valueConverter: {
            type: 'map',
            map: [{ id: true, title: '已发布', cssClass: 'isActiveClass' }, { id: false, title: '草稿', cssClass: 'isActiveClassFalse' }],
            fields: { text: 'title', value: 'id' }
          },
          cellTools: [{ id: 'publish', icon: 'icon_Share_2', title: '发布', visibleCondition: (data:any) => { return data.isActive === false } }]
        },
        {
          field: 'version',
          width: '150',
          headerText: '版本号',
          valueConverter: {
            type: 'placeholder'
          },
          cellTools: [{ id: 'history', icon: 'icon_list_edit', title: '查看历史版本' }]
        }],
      asyncConfig: {
        recordsPosition: 'data.data',
        url: '/lowcodeWeb/tenant/form-template/list',
        params: {
          classifyId: '',
          name: '',
          businessType: '',
          current: 1,
          size: 10
        }
      }
    }
  }
]
export const componentFieldConfig = [
  {
    title: '字段权限',
    toolbar: ['Add', 'Delete'],
    grid: {
      columnData: [
        {
          type: 'checkbox',
          width: 50
        }, {
          field: 'name',
          width: '150',
          headerText: '字段名',
          cssClass: 'user-content-style', // 单元格自定义title样式
          cellTools: ['edit', { id: 'publish', icon: 'icon_solid_pushorder', title: '发布', visibleCondition: (data:any) => { return data.isActive === false } }, 'delete']
        },
        {
          field: 'field',
          width: '150',
          headerText: '字段',
          valueConverter: {
            type: 'placeholder'
          }
        },
        {
          field: 'businessType',
          width: '150',
          headerText: '关联业务类型'
          // valueConverter: {
          //   type: 'map',
          //   map: { true: '已发布', false: '草稿' }
          // }
        }],
      asyncConfig: {
        recordsPosition: 'data.data',
        url: '/lowcodeWeb/tenant/field/list',
        params: {
          current: 1,
          size: 10
        }
      }
    }
  }
]
export const dataSource = {
  customButton: [{ text: '关闭', type: 'text', id: 'close', icon: 'mt-icons mt-icon-icon_solid_close' }, { text: '发布', type: 'text', id: 'publish', icon: 'mt-icons mt-icon-icon_solid_pushorder' }],
  saveFormItem: true,
  showDemo: true,
  fields: [
  ],
  formRef: 'mtForm',
  formModel: 'formData',
  size: 'medium',
  labelPosition: 'right',
  labelWidth: 100,
  formRules: 'rules',
  disabled: false,
  span: 24,
  parserData: [],
  lifecycleEvents: { mounted: [], beforeDestroy: [] },
  methods: []
}
