<template>
  <div class="hello">
      <div class="header" v-if="showHeader">
        <mt-form ref="ruleForm" :model="ruleForm" :rules="rules" class="form-class">
            <mt-form-item prop="code" class="form-item" label="模板编码">
                <mt-input
                :disabled="enterType == 'edit'"
                v-model="ruleForm.code"
                :width="300"
                :showClearButton="true"
                type="text"
                placeholder="请输入模板编码"
                ></mt-input>
            </mt-form-item>
            <mt-form-item prop="name" label="模板名称">
                <mt-input
                :width="300"
                v-model="ruleForm.name"
                type="text"
                placeholder="请输入模板名称"
                ></mt-input>
            </mt-form-item>
            <mt-form-item prop="version" label="版本号">
                <mt-input
                :disabled="true"
                :width="300"
                v-model="ruleForm.version"
                type="text"
                placeholder="请输入版本号"
                ></mt-input>
            </mt-form-item>
        </mt-form>
      </div>
    <Deylop ref="Deylop" v-if="enterType!='edit'||dataSource.fields.length>0" @onSave="onSave" @onShowDemo="onShowDemo" :dataSource="dataSource" @onButton='onButton'></Deylop>
  </div>
</template>

<script>
import Deylop from '@mtech-form-design/deploy'
import { dataSource } from '../config'
export default {
  components: {
    Deylop
  },
  props: {
    msg: String
  },
  data() {
    return {
      isActive: false,
      dataSource,
      template: {},
      showHeader: true,
      ruleForm: {
        code: '',
        name: '',
        version: '1'
      },
      rules: {
        code: [
          { required: true, message: '请输入编码', trigger: 'blur' },
          {
            min: 4,
            max: 25,
            message: '长度在 4 到 25 个字符',
            trigger: 'blur'
          }
        ],
        name: [{ required: true, message: '请输入名称', trigger: 'blur' },
          {
            min: 4,
            max: 25,
            message: '长度在 4 到 25 个字符',
            trigger: 'blur'
          }],
        version: [{ required: true, message: '请选择日期和时间', trigger: 'blur' }]
      },
      selectArr: {
        dataSource: [],
        value: 'itemCode',
        text: 'name',
        parentID: 'parentId',
        child: 'children'
      }
    
    }
  },
  computed: {
    classifyId() {
      return this.$route.query.classifyId
    },
    enterType() {
      return this.$route.query.type
    },
    id() {
      return this.$route.query.id
    },
    version() {
      return this.$route.query.version || '1'
    }
  },
  mounted() {
    // this.$api.form.getBusinessType().then(r => {
    //   // this.$set(this.selectArr, 'dataSource', r.data)
    //   this.selectArr = Object.assign({}, this.selectArr, {
    //     dataSource: r.data
    //   })
    // })
    if (this.enterType === 'edit') {
      this.$api.form.getTemplate({ id: this.id, version: this.version }).then(r => {
        this.ruleForm = {
          ...r.data
        }
        this.dataSource = Object.assign({}, this.dataSource, r.data.template)
      })
    }
  },
  methods: {
    onShowDemo(data) {
      this.showHeader = !data
    },
    onSave(data) {
      console.log(data)
      this.template = data
      this.isActive = false
      this.submitForm('ruleForm')
    },
    // 自定义按钮事件
    onButton(data) {
      if (data.id === 'publish') {
        this.template = this.$refs.Deylop.getFormData()
        this.isActive = true
        this.submitForm('ruleForm')
      } else if (data.id === 'close') {
        this.$dialog({
          data: {
            title: '警告',
            message: '是否确定退出？'
          },
          success: () => {
            this.$router.go(-1)
          }
        })
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const params = {
            ...this.ruleForm,
            classifyId: this.classifyId,
            isActive: this.isActive,
            template: this.template
          }
          if (this.enterType === 'edit') {
            params.id = this.id
            this.$api.form.updataTemplate(params).then(r => {
              if (r.code === 200) {
                this.$router.go(-1)
              }
            })
          } else {
            this.$api.form.addTemplate(params).then(r => {
              if (r.code === 200) {
                this.$router.go(-1)
              }
            })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
    // reset(formName) {
    //   this.$refs[formName].resetFields()
    // }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.header{
    height: 91px;
    background: linear-gradient(rgba(250, 250, 250, 1), rgba(250, 250, 250, 1)),
    linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 1));
    border-bottom: 1px solid rgba(232,232,232,1);
    width:100%;
    overflow: hidden;
    overflow-y: auto;
    display: flex;
    .form-class{
        width: 100%;
        display: flex;
        height: 100%;
        align-items: center;
        align-content: space-between;
        justify-content: space-between;
        flex-direction: row;
        flex-wrap: wrap;
        box-sizing: border-box;
        // padding-top: 11px;
        padding: 11px 63px 0 10px;

    }
}
/deep/ .mt-container{
    padding:0 !important
}
/deep/ .rightScroll{
    height: calc(100vh - 71px);
}
/deep/ .e-input-group-icon.e-ddt-icon{
  background-color: transparent !important;
}
/deep/ .e-ddt.e-input-group.e-control-wrapper .e-clear-icon{
  background-color: transparent !important;
}
</style>
