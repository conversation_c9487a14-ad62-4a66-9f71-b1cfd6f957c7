<template>
  <div class="demo-block">
    <mt-dialog ref="dialog" css-class="dialog-form-flex" :header="header" :buttons="buttons">
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="name" label="字段名称">
          <mt-input
            v-model="ruleForm.name"
            :disabled="false"
            :showClearButton="true"
            type="text"
            placeholder="请输入字段名称"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="field" label="字段">
          <mt-input v-model="ruleForm.field" type="text" placeholder="请输入字段"></mt-input>
        </mt-form-item>
        <mt-form-item prop="businessType" label="业务类型">
          <mt-DropDownTree
            id="filter"
            :fields="selectArr"
            v-if="selectArr.dataSource.length>0"
            v-model="ruleForm.businessType"
            placeholder="请选择关联业务类型"
          ></mt-DropDownTree>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>

export default {
  components: {
  },
  data() {
    return {
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: '取消' }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: '确定' }
        }
      ],
      header: '新增',
      selectArr: {
        dataSource: [],
        value: 'itemCode',
        text: 'name',
        parentID: 'parentId',
        child: 'children'
      },
      ruleForm: {
        name: '',
        field: '',
        businessType: '',
        id: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入字段名称', trigger: 'blur' },
          {
            min: 3,
            max: 5,
            message: '长度在 3 到 5 个字符',
            trigger: 'blur'
          }
        ],
        field: [{ required: true, message: '请输入字段', trigger: 'blur' }],
        businessType: [{ required: true, message: '请选择业务类型', trigger: 'blur' }]
      }
    }
  },
  mounted() {
    this.$api.form.getBusinessType().then(r => {
      this.selectArr = Object.assign({}, this.selectArr, {
        dataSource: r.data
      })
      // this.ruleForm.businessType = 'FORM_DESIGN_BUSINESS_TYPE'
    })
  },
  methods: {
    show() {
      this.$refs.dialog.ejsRef.show()
    },
    hide() {
      this.$refs.dialog.ejsRef.hide()
      this.$parent.AddFieldFormShow = false
    },
    save() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.header === '新增') {
            this.$api.form.addField(this.ruleForm).then(r => {
              this.$parent.refreshMain()
              this.hide()
            })
          } else {
            Array.isArray(this.ruleForm.businessType) ? this.ruleForm.businessType = this.ruleForm.businessType[0] : ''
            this.$api.form.updateField(this.ruleForm).then(r => {
              this.$parent.refreshMain()
              this.hide()
            })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>
<style lang="scss">
.dialog-form-flex .e-dlg-content {
  padding: 40px !important;
  .mt-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 390px;
      .mt-form-item__content {
        width: 100%;
      }
    }
  }
}
.apiDelClass{
    height: 31px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    line-height: 35px;
    padding: 0 0 0 10px;
    border-bottom: 1px solid rgb(232, 232, 232);
}
</style>
