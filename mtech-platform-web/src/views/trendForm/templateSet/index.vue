<template>
  <div class="platmenu-page--wrap">
    <div v-if="historyShow" class="hsitory-box">
      <div class="history-header">
        <div class="history-left">
          <i class="mt-icons mt-icon-icon_input_search"></i>
          <mt-select
            :dataSource="historyArr"
            :showClearButton="true"
            :allowFiltering="true"
            :fields="{text:'versionName',value:'version'}"
            @change="historyChange"
            v-model="historyArrValue"
            placeholder="请选择版本"
          ></mt-select>
        </div>
        <div class="history-button">
          <i class="mt-icons mt-icon-icon_list_recall" @click="gobackVersion">回滚</i>
          <i class="mt-icons mt-icon-icon_solid_close" @click="historyShow=false">关闭</i>
        </div>
      </div>
      <Parser v-if="parserShow" :formConf="formConf"></Parser>
    </div>
    <div class="platmenu--wrap" v-else>
      <div class="tree-view--wrap">
          <CategoryTemplateTree class="tree-view--wrap" type="processModel" @nodeSelected="nodeSelected" />
      </div>

      <div class="platmenu-content">
        <mt-template-page
          ref="mainTemplatePage"
          :hiddenTabs="true"
          :templateConfig="componentConfig"
          @handleClickToolBar="handleClickToolBar"
          @handleClickCellTool="handleClickCellTool"
          @handleClickCellTitle="handleClickCellTitle"
        >
        </mt-template-page>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue } from '@mtech/vue-property-decorator'
import CategoryTemplateTree from '../components/CategoryTemplateTree.vue'
import Parser from './historyTemplate.vue'
import Bus from '@/utils/bus'
import { treeViewData, componentTemplateConfig } from '../config'

@Component({
  components: { CategoryTemplateTree, Parser }
})
export default class TemplateSet extends Vue {
  componentConfig:any = componentTemplateConfig
  dataArr = []
  applicationId = '10548041430523932'
  treeViewData = treeViewData
  setObj = { header: '' }
  setData = {}
  parentId = '0'
  treeViewShow = false
  classifyId=''
  historyShow = false
  historyArr = []
  historyArrValue = '1'
  historyId = ''
  formConf= {}
  parserShow = false
  historyTemplate:any = {}

  /**
   * 生命周期
   * **/
  async created() {}

  mounted() {
    Bus.$on('handleClickCellToolFlow', this.handleClickCellTool)
    Bus.$on('handleClickCellTitleFlow', this.handleClickCellTitle)
    // this.getFind()
  }

  addMainNode() {
    console.log(this.$refs.AddNodeForm)
    const fuc:any = this.$refs.AddNodeForm
    fuc.parentId = '0'
    fuc.reserForm()
    fuc.show()
  }

  nodeSelected(e:any) {
    console.log(e)
    this.queryCriteria(e.nodeData)
  }

  // 在点击节点或者初始化的时候调用
  queryCriteria(e:any) {
    const a:any = this.componentConfig
    a[0].grid.asyncConfig.params.classifyId = e.id
    this.classifyId = e.id
  }

  addOptSucces(func:any) {
    // const func:any = this.$refs.treeView
    // 可在次添加默认选中的节点
    const id:any = func.getCommonMethods().getTreeData()[0].id
    this.queryCriteria(func.getCommonMethods().getNode(id))

    func.getCommonMethods().expandAll()
  }

  handleClickToolBar(e: any) {
    console.log('use-handleClickToolBar', e)
    if (e.toolbar.id === 'Add') {
      this.$router.push({
        path: '/platform/FormTemplateEdit',
        query: { classifyId: this.classifyId, type: 'add', id: '' }
      })
      // const routeUrl = this.$router.resolve({
      //   path: '/platform/FormTemplateEdit',
      //   query: { classifyId: this.classifyId, type: 'add', id: '' }
      // })
      // window.open(routeUrl.href, '_blank')
    } else if (e.toolbar.id === 'Delete') {
      const func:any = this.$refs.mainTemplatePage
      // console.log()
      const arr:any = func.getCurrentTabRef().grid.getSelectedRecords()
      const pushArr:any = []
      if (arr.length > 0) {
        arr.forEach((e:any) => {
          pushArr.push(e.id)
        })
      } else {
        this.$toast({
          content: '请至少选择一个！',
          type: 'warning'
        })
      }
      this.delTemplate(pushArr)
    }
  }

  // 删除字段权限
  delTemplate(pushArr:any) {
    this.$dialog({
      data: {
        title: '删除',
        message: '是否确认删除？'
      },
      success: () => {
        this.$api.form.deleteTemplate({
          ids: pushArr
        }).then((r:any) => {
          console.log(r)
          this.refreshMain()
        })
      }
    })
  }

  // 删除数据权限
  delDataG(pushArr:any) {
    this.$dialog({
      data: {
        title: '删除',
        message: '是否确认删除？'
      },
      success: () => {
        this.$api.detail.deleteDataBatch({
          ids: pushArr
        }).then((r:any) => {
          console.log(r)
          this.refreshMain()
        })
      }
    })
  }

  // 刷新main列表
  refreshMain() {
    const func:any = this.$refs.mainTemplatePage
    func.refreshCurrentGridData()
  }

  // 行内编辑删除操作
  handleClickCellTool(e: any) {
    console.log('use-handleClickCellTool', e)
    if (e.tool.id === 'delete') {
      this.delTemplate([e.data.id])
    } else if (e.tool.id === 'edit') {
      // const func:any = this.$refs.WordGridDialogEdit
      this.classifyId = e.data.classifyId
      this.$router.push({
        path: '/platform/FormTemplateEdit',
        query: { id: e.data.id, type: 'edit', classifyId: this.classifyId, version: e.data.version }
      })
    } else if (e.tool.id === 'publish') {
      this.$api.form.updataTemplate({ id: e.data.id, isActive: true }).then((r:any) => {
        if (r.code === 200) {
          const func:any = this.$refs.mainTemplatePage
          func.refreshCurrentGridData()
        }
      })
    } else if (e.tool.id === 'history') {
      this.$api.form.getMyHistoryTemplateList({ id: e.data.id, current: 1, size: 99999 }).then((r:any) => {
        this.historyShow = true
        this.historyId = e.data.id
        this.historyArr = r.data.data.map((e:any) => {
          return { versionName: `V${e.version}`, version: `${e.version}` }
        })
        this.historyArrValue = `${r.data.data[0].version}`
        this.getTemplate()
      })
    }
  }

  gobackVersion() {
    this.$dialog({
      data: {
        title: '警告',
        message: '是否确定回滚？'
      },
      success: () => {
        this.$api.form.updataTemplate(this.historyTemplate).then((r:any) => {
          this.historyShow = false
          this.parserShow = false
        })
      }
    })
  }

  handleClickCellTitle(e: any) {
    console.log('use-handleClickCellTitle', e)
    if (e.field === 'column1') {
      // todo title click
    }
  }

  handleGridCurrentChange(data: any) {
    console.log('use-handleGridCurrentChange', data)
    this.fetchGridData({ currentPage: data.currentPage - 1 })
  }

  handleGridSizeChange(data: any) {
    console.log('use-handleGridSizeChange', data)
    this.fetchGridData({
      count: data.count
    })
  }

  // 历史版本选择
  historyChange(e:any) {
    this.historyArrValue = e.value
    this.getTemplate()
  }

  // 获取模板详情
  getTemplate() {
    const parpams = { version: this.historyArrValue, id: this.historyId }
    this.$api.form.getTemplate(parpams).then((r:any) => {
      this.formConf = r.data.template
      this.parserShow = true
      this.historyTemplate = r.data
    })
  }

  async fetchGridData(event: { count?: number; currentPage?: number }) {
    const res = await this.$api.detail.queryApi({
      current: event.currentPage
    })

    if (res.code === 200 && res.data) {
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .selectClass{
  border: none !important;
}
.platmenu-page--wrap {
  display: flex;
  height: 100%;
  flex-direction: column;
}
.user-list {
  height: 100%;
  width: 100%;
  background: #fff;
  padding: 20px;
  box-sizing: border-box;
}
.addClass {
  display: inline-block;
  margin: 10px;
}
.platmenu-page--header {
  padding: 20px;
  background: #fff;
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px 8px 0 0;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

  .mr-20 {
    margin-right: 20px;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .mr-8 {
    margin-right: 8px;
  }
  .mb-20 {
    margin-bottom: 20px;
  }
  .mb-10 {
    margin-bottom: 10px;
  }

  .header-second--wrap {
    .title {
      font-size: 14px;
      color: rgba(41, 41, 41, 1);
    }
  }

  .header-third--wrap {
    color: #4f5b6d;
    > span {
      cursor: pointer;
    }
  }
}

.platmenu--wrap {
  flex: 1;
  width: 100%;
  display: flex;
  font-size: 14px;
}

.platmenu-content {
  margin: 0 0 0 10px;
  width: 100%;
  border: 1px solid rgba(232, 232, 232, 1);
  border-top: none;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
}

.grid-tab--wrap {
  border: 1px solid rgba(232, 232, 232, 1);
  padding: 0 30px;
  white-space: nowrap;

  .grid-tab--item {
    display: inline-block;
    color: #9a9a9a;
    margin: 12px 60px 12px 0;
    border: 1px solid transparent;
    border-radius: 4px;
    padding: 6px 10px;

    &.active {
      color: #00469c;
      background: rgba(0, 70, 156, 0.06);
      border-color: rgba(0, 70, 156, 0.1);
    }
  }
}

.tree-view--wrap {
  flex: 0 0 400px;
  background: #fff;
  border-right: 1px solid #eee;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

  .trew-node--add {
    line-height: 50px;
    padding: 0 20px;
    color: #4f5b6d;
  }

  .tree-view--template {
    width: 100%;
  }

  ::v-deep .e-list-text {
    width: 100% !important;
  }

  /deep/ .e-list-text {
    width: 100% !important;
  }
}

.data-grid--wrap {
  height: calc(100% - 55px);
  overflow: hidden;
}
.hsitory-box{
  height: 100%;
  width: 100%;
  background: rgba(255,255,255,1);
  border: 1px solid rgba(232,232,232,1);
  border-radius: 8px 8px 0 0;
  box-shadow:  0 0 10px 0 rgba(137,120,120,0.06);
  .history-header{
    padding: 0 25px;
    height: 50px;
    width: 100%;
    box-shadow: inset 0 -1px 0 0 rgba(232,232,232,1);
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    .history-left{
      display: flex;
      align-items: center;
      i{
        color:#98AAC3;
        font-size: 14px;
        margin: 0 10px 0 0;
      }
    }
    .history-button{
      width: 112px;
      display: flex;
      justify-content: space-between;
      margin: 0 20px;
      font-size: 14px;
      color: rgba(79,91,109,1);
      i{
        cursor: pointer;
      }
    }
  }
}
</style>
