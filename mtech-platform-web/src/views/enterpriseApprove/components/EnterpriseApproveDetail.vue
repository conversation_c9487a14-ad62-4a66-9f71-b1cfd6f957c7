<template> 
  <mt-dialog
    ref="dialog" 
    header="查看"
    :buttons="footButtons"
    @close="hide"
  >
    <div class="approve diolog">
      <mt-form ref="form" :model="approveForm" :rules="formRules">
        <mt-form-item label="审批意见" prop="rejectReason" >
          <mt-input
            :readonly="isView"
            :show-clear-button="false"
            type="text"
            v-model="approveForm.rejectReason"
          ></mt-input>
        </mt-form-item>
      </mt-form>  
      <EnterpriseDetail v-if="visible" :enterpriseId="id" class="mb--20" @success="saveFormData"  /> 
    </div>
  </mt-dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from '@mtech/vue-property-decorator'   
import EnterpriseDetail from '@/components/enterpriseDetail/index.vue'

@Component({
  components: {  
    EnterpriseDetail
  }
})
export default class EnterpriseApproveList extends Vue { 
  @Prop()
  value !: boolean

  @Prop()
  id !: string

  @Prop(
    {
      default: false
    }
  )
  isView !: boolean

  approveForm :any = {
    rejectReason: ''
  }
  
  get formRules() {
    if (this.isView) {
      return {}
    } else {
      return {
        rejectReason: [
          { required: true, message: '请输入审批意见', trigger: 'blur' }
        ] 
      }
    }
  }

  get footButtons() {
    const buttons:any[] = [
      {
        click: this.hide,
        buttonModel: { content: '取消' }
      }
    ]
    if (this.isView) {
      // buttons.push({
      //   click: this.hide,
      //   buttonModel: { isPrimary: 'true', content: '通过' }
      // })
    } else {
      buttons.push({
        click: this.handleReject,
        buttonModel: { content: '驳回' }
      },
      {
        click: this.handleConfirm,
        buttonModel: { isPrimary: 'true', content: '通过' }
      })
    }
    return buttons
  }

  get visible() {
    return this.value
  }

  set visible(v: boolean) {
    this.$emit('input', v)
  }
 
  @Watch('visible') 
  onWatchVisible(val: boolean) {
    if (val) {
      this.show()
    } 
  }

  hide() {
    this.visible = false
    this.approveForm = {}
    const ref: any = this.$refs.dialog
    ref.ejsRef.hide()
  }

  show() { 
    const ref: any = this.$refs.dialog
    ref.ejsRef.show()
  } 

  handleConfirm() {
    this.approveEnterprise(true)
  }

  handleReject() {
    this.approveEnterprise()
  }

  saveFormData(data: any) {
    const { rejectReason = '' } = data
    this.$set(this.approveForm, 'rejectReason', rejectReason) 
  }

  private approveEnterprise(isSuccess?: boolean) {
    (this.$refs.form as any).validate((valid: boolean) => {
      if (valid) { 
        let promise 
        if (isSuccess) {
          promise = this.$api.enterprise.enterpriseFlowApprove
        } else {
          promise = this.$api.enterprise.enterpriseFlowBack
        } 

        promise({
          enterpriseId: this.id,
          reason: this.approveForm.rejectReason
        }).then((res:any) => {
          this.hide()
          this.$emit('refresh')
          this.$toast({
            content: res.msg || (isSuccess ? '审批已通过' : '审批已驳回'),
            type: 'success'
          })
        }).catch((err:any) => {
          this.$toast({
            content: err.msg || (isSuccess ? '审批通过失败' : '审批驳回失败'),
            type: 'error'
          })
        }) 
      }  
    })
  }
}
 
</script>

<style lang="scss" scoped>
.mb--20 {
  margin-bottom: 20px;
}
</style>
