<template>
  <div class="action-boxs">
    <span v-if="data.status === 0" class="status status-uncooperation">待审核</span>
    <span v-if="data.status === 1" class="status status-cooperation">已通过</span>
    <span v-if="data.status === 2" class="status status-rejected">已拒绝</span> 
  </div>
</template>

<script>
export default {
  data() {
    return {
      data: {}
    }
  },
  methods: { 
  }
}
</script>

<style lang="scss" scoped>
.status {
  border-radius: 3px;
  padding: 5px 10px;
}

.status-cooperation {
  color: #8ACC40;
  background: rgba(138,204,64,0.1);
}

.status-uncooperation {
  color: #EDA133;
  background:  rgba(237,161,51,0.1);
}

.status-rejected {
  color: #ED5633;
  background:rgba(237,86,51,0.1);
}
</style>
