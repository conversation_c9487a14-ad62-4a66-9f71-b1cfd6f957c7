<template>
  <div class="enterprise-manage router-page--wrap">
    
    <mt-template-page
      ref="tempaltePageRef"
      :templateConfig="pageConfig" 
      @handleClickCellTool="handleClickCellTool" 
    > 
    </mt-template-page> 

    <EnterpriseApproveDetail 
      v-model="isShowDialog" 
      :id="currentRowDataId" 
      :isView="currentRowIsView"
      @refresh="refreshTempaltePage" />
 
  </div>
</template>
<script lang="ts">
import { Component, Vue } from '@mtech/vue-property-decorator' 
import { enterprisePageConfig } from './config/index' 
import EnterpriseApproveDetail from './components/EnterpriseApproveDetail.vue'

@Component({
  components: {   
    EnterpriseApproveDetail
  }
})
export default class EnterpriseManage extends Vue {
  pageConfig: any[] = enterprisePageConfig
  currentRowDataId = ''
  currentRowIsView = false
  isShowDialog = false  
  
  handleClickCellTool(e: any) {  
    const { data, tool } = e
    if (tool.id === 'Approval' || tool.id === 'View') {
      this.isShowDialog = true
      this.currentRowDataId = data.id
      this.currentRowIsView = tool.id === 'View'
    } 
  } 

  refreshTempaltePage() {
    const ref = this.$refs.tempaltePageRef as any
    ref.refreshCurrentGridData()
  }
}
</script>
<style lang="scss" scoped>
.enterprise-manage {
  /deep/ .status {
    line-height: 18px;
    border-radius: 2px;
    padding: 0 4px;
  }

  /deep/ .status-creating {
    background: rgba(237,161,51,0.1);
    color: #EDA133;
  }

  /deep/ .status-failed {
    background: rgba(237,86,51,0.1);
    color: #ED5633;
  }

  /deep/ .status-unapproval {
    background: rgba(148,151,157,0.1);
    color: #9A9A9A;
  }

  /deep/ .status-success {
    background: rgba(99,134,193,0.1);
    color: #6386C1;
  }
} 
</style>
