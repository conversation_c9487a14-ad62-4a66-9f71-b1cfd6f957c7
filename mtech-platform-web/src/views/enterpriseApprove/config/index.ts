import ColumnTimeTemplate from '../../../components/ColumnTimeTemplate.vue'
 
const columnData = [
  {
    width: '40',
    type: 'checkbox'
  },    
  {
    field: 'createTime',
    headerText: '注册时间',
    template() {
      return {
        template: ColumnTimeTemplate
      }
    }
  },
  {
    field: 'identityTypeName',
    headerText: '企业身份代码'
  },
  {
    field: 'identityCode',
    headerText: '企业代码'
  },
  {
    field: 'enterpriseName', 
    headerText: '企业名称' 
  },  
  {
    field: 'status',
    headerText: '状态',
    valueConverter: {
      type: 'map',
      fields: { text: 'label', value: 'status' },
      map: [      
        { status: -1, label: '创建中', cssClass: ['status', 'status-creating'] }, 
        { status: 0, label: '待审核', cssClass: ['status', 'status-unapproval'] },
        { status: 1, label: '审核通过', cssClass: ['status', 'status-success'] },
        { status: 2, label: '审核未通过', cssClass: ['status', 'status-failed'] },
        { status: 3, label: '被驳回', cssClass: ['status', 'status-failed'] }
      ]
    },
    cellTools: [ 
      {
        id: 'Approval',
        icon: 'icon_solid_Submit',
        title: '审核',
        visibleCondition: (data: { status: number }) => { 
          return data.status === 0
        } 
      },
      {
        id: 'View',
        icon: 'icon_solid_Submit',
        title: '查看',
        visibleCondition: (data: { status: number }) => {
          return data.status !== 0
        } 
      }
    ]
  }
]

const reviewingColumnData = [
  ...columnData
]
const rejectedColumnData = [
  ...columnData
]
const passedColumnData = [
  ...columnData
]

rejectedColumnData.splice(1, 0, { 
  field: 'approvalTime',
  headerText: '驳回时间',
  template() {
    return {
      template: ColumnTimeTemplate
    }
  } 
})

passedColumnData.splice(1, 0, { 
  field: 'approvalTime',
  headerText: '审核时间',
  template() {
    return {
      template: ColumnTimeTemplate
    }
  } 
}) 

export const enterprisePageConfig = [
  {
    useToolTemplate: false,
    title: '待审核', // 建议使用此配置
    toolbar: [],
    grid: {
      columnData: reviewingColumnData,
      dataSource: [],
      asyncConfig: {
        url: '/platform/admin/enterprise/queryReviewing',
        methods: 'post',
        params: { 
        } 
      }
    }
  },
  {
    useToolTemplate: false,
    title: '已驳回', // title的设置是向下兼容，仅用于只有Tab标题显示的场景
    toolbar: [],
    grid: {
      columnData: rejectedColumnData,
      dataSource: [],
      asyncConfig: {
        url: '/platform/admin/enterprise/queryRejected',
        methods: 'post',
        params: { 
        } 
      }
    }
  },
  {
    useToolTemplate: false,
    title: '已通过', // title的设置是向下兼容，仅用于只有Tab标题显示的场景
    toolbar: [],
    grid: {
      columnData: passedColumnData,
      dataSource: [],
      asyncConfig: {
        url: '/platform/admin/enterprise/queryPassed',
        methods: 'post',
        params: { 
        } 
      } 
    }
  }
]
