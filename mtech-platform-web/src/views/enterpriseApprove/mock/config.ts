import StatusTenant from '../components/StatusTenant.vue'

export const strategyListToolBar = [
  [{ id: 'Delete', icon: 'icon_solid_Delete', title: '删除' }], 
  [
    { id: 'Filter', icon: 'icon_solid_Filter', title: '过滤' },
    { id: 'Refresh', icon: 'icon_solid_Refresh', title: '刷新' },    
    'Setting'
  ]
]

export const strategyListColumnData = [
  {
    width: '40',
    type: 'checkbox'
  },
  {
    field: 'code',
    headerText: '公司编码', 
    cellTools: [
      {
        id: 'Edit',
        icon: 'icon_solid_edit',
        title: '审核',
        visibleCondition: (data: { status: number }) => {
          return data.status === 0
        } 
      },
      {
        id: 'Copy',
        icon: 'Copy',
        title: '重新审核',
        visibleCondition: (data: { status: number }) => {
          return data.status === 2
        } 
      },
      { id: 'Delete', icon: 'icon_solid_Delete', title: '删除' }
    ]
  },
  {
    field: 'name',
    headerText: '公司名称'
  },
  {
    field: 'category',
    headerText: '企业类型'
  }, 
  {
    field: 'status',
    headerText: '状态',
    template() {
      return {
        template: StatusTenant
      }
    }
  }, 
  {
    field: 'control',
    headerText: '操作人'
  },
  {
    field: 'updateDate',
    headerText: '更新日期'
  } 
]

export const getStrategyListGridData = (len = 10) => {
  const res = []
  for (let i = 0; i < len; i++) {
    res.push({
      code: 'DJBM00192020112' + i,
      name: '南网电工',
      category: '',
      status: i % 3,
      control: '王鹏',
      updateDate: '2021-07-16 12:00:00' 
    })
  }
  return res
}
