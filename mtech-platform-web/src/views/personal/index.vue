<template>
  <div class="personal">
    <div class="content-top">
      <div class="account-title">
        <img :src="accountDetail.photo" alt="" />
        <div class="account-name">{{ accountDetail.nickName }}</div>
        <div class="account-business">
          {{ companyName }}-{{ departmentName }}-{{ tenantName }}
        </div>
      </div>
      <mt-tabs
        :eTab="false"
        :dataSource="dataSource"
        class="mt-tabs"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>
    </div>
    <div class="content-body">
      <div v-if="item === 0">
        <Account :account="accountDetail" />
      </div>
      <div v-if="item === 1">
        <Invitation />
      </div>
      <div v-if="item === 2">
        <Business />
      </div>
      <div v-if="item === 3 && isShowAddress" >
        <Address />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from '@mtech/vue-property-decorator'
import Invitation from './components/Invitation.vue'
import Business from './components/Business.vue'
import Account from './components/Account.vue'
import Address from './components/Address.vue'

@Component({
  components: {
    Invitation,
    Business,
    Account,
    Address
  }
})
export default class Personal extends Vue {
  dataSource: any[] = [
    {
      title: '账号信息',
      content: '1'
    },
    {
      title: '我的邀请',
      content: '2'
    },
    {
      title: '我的企业',
      content: '3'
    },
    {
      title: '我的地址',
      content: '4'
    },
    {
      title: '专家论坛',
      content: '5'
    }
  ];

  item = 0;
  accountDetail: any = {};
  companyName = '';
  departmentName = '';
  tenantName = '';
  isShowAddress = false;

  mounted() {
    (this as any).getAccountDetail();
    (this as any).getTenantOrg();
    (this as any).addressCheckExistBuyer()
  }

  handleSelectTab(item: number) {
    this.item = item
  }

  getAccountDetail() {
    const res = (this as any).$api.enterprise.getAccountDetail()
    res.then((r: any) => {
      if (r.code === 200 && r.data) {
        (this as any).accountDetail = r.data
      }
    })
  }

  getTenantOrg() {
    const res = (this as any).$api.enterprise.getTenantOrg()
    res.then((r: any) => {
      if (r.code === 200 && r.data) {
        (this as any).companyName = r.data.companyName as string;
        (this as any).departmentName = r.data.departmentName as string;
        (this as any).tenantName = r.data.tenantName as string
      }
    })
  }

  addressCheckExistBuyer() {
    const res = (this as any).$api.address.addressCheckExistBuyer()
    res.then((r: any) => {
      if (r.code === 200 && r.data) {
        (this as any).isShowAddress = r.data
      }
    })
  }
}
</script>

<style lang="scss" scoped>
.personal {
  background-color: #e8e8e8;
  .content-top {
    background-color: white;
    margin-bottom: 10px;
    padding: 5px 20px 5px 20px;
    .account-title {
      margin-top: 10px;
      padding-left: 80px;
      height: 90px;
      img {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        margin: 10px 20px 0 0;
        position: absolute;
        left: 50px;
      }
      .account-name {
        line-height: 50px;
        font-size: 18px;
        color: #9a9a9a;
      }
      .account-business {
        line-height: 10px;
        color: #eda133;
      }
    }
    .mt-tabs {
      width: 100%;
      /deep/ .mt-tabs-container {
        width: 100%;
      }
    }
  }
  .content-body {
    background-color: white;
    padding-bottom: 20px;
    min-height: 100%;
  }
}
</style>
