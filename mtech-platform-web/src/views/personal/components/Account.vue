<template>
    <div class="account">
        <div class="title">
            账号信息
        </div>
        <div class="body">
            <div class="body-title">基本信息</div>
            <div class="account-basic">
                <div class="account-basic-content">
                    <div class="account-left">
                        <div class="account-left-title">{{account.userName || '设置账号'}}</div>
                        <div class="account-left-tips" v-if="!account.userName">
                            您还没有账号，点击设置账号
                        </div>
                        <div class="account-left-tips" v-if="account.userName">
                            您已有账号，并不可进行修改
                        </div>
                    </div>
                    <div v-if="!account.userName" class="account-right" @click="changeAccountDetail('USERNAME')">
                        <div class="account-right-change">
                            <mt-icon name="icon_solid_Configuration"></mt-icon>
                            设置
                        </div>
                    </div>
                </div>

                <div class="account-basic-content">
                    <div class="account-left">
                        <div class="account-left-title">
                            登录密码
                            <!-- 注册时候必填，产品说没有未设置 -->
                            <div class="account-left-btn">已设置</div>
                        </div>
                        <div class="account-left-tips">安全性高的密码可以使账号更安全，建议您定期更换密码，设置一个包含字母、符号或数字中至少两项长度超过6位数的密码。</div>
                    </div>
                    <div class="account-right">
                        <div class="line line-active"></div>
                        <div class="line" :class="account.passwordStrength > 0 ? 'line-active' : ''"></div>
                        <div class="line line-end" :class="account.passwordStrength > 1 ? 'line-active' : ''"></div>
                        <div class="account-right-change" @click="changeAccountDetail('PASSWORD')">
                            <mt-icon name="icon_solid_Configuration"></mt-icon>
                            修改
                        </div>
                    </div>
                </div>

                <div class="account-basic-content">
                    <div class="account-left">
                        <div class="account-left-title">绑定手机
                            <div class="account-left-btn">已绑定</div>
                        </div>
                        <div class="account-left-tips">可用手机号加密码登录平台，可通过手机号找回密码</div>
                    </div>
                    <div class="account-right">
                        <div class="account-right-content">
                            <span>{{account._phone}}</span>
                            <mt-icon name="icon_Hiddenpassword" @click.native="showPhone"></mt-icon>
                        </div>
                        <div class="account-right-change" @click="changeAccountDetail('MOBILE')">
                            <mt-icon name="icon_solid_Configuration"></mt-icon>
                            修改
                        </div>
                    </div>
                </div>

                <div class="account-basic-content">
                    <div class="account-left">
                        <div class="account-left-title">邮箱绑定
                            <div class="account-left-btn">已绑定</div>
                        </div>
                        <div class="account-left-tips">可通过验证码的方式修改绑定邮箱</div>
                    </div>
                    <div class="account-right">
                        <div class="account-right-content">
                            <span>{{account._email}}</span>
                            <mt-icon name="icon_Hiddenpassword" @click.native="showEmail"></mt-icon>
                        </div>
                        <div class="account-right-change" @click="changeAccountDetail('MAIL')">
                            <mt-icon name="icon_solid_Configuration"></mt-icon>
                            绑定
                        </div>
                    </div>
                </div>
            </div>

            <div class="body-title">当前租户信息</div>
            <div class="detail-account" v-for="item in accountList" :key="item.id">
                <div class="account-body">
                    <div class="account-logo">
                        <img :src="'/api/file/user/file/viewPublicImageFile?id=' + item.logoFileId">
                    </div>
                    <div class="account-top">
                        员工编号：{{item.employeeCode}}
                        <div class="stationName" v-if="item.stationName">{{item.stationName}}</div>
                        <div class="departmentName" v-if="item.departmentName">{{item.departmentName}}</div>
                    </div>
                    <div class="account-detail">
                        <div>
                            员工姓名：{{item.employeeName}}
                        </div>
                        <div>
                            部门：{{item.departmentName}}
                        </div>
                        <div>
                            岗位：{{item.stationName}}
                        </div>
                        <div>
                            适用系统：{{item.application}}
                        </div>
                        <!-- <div>
                            独立账户体系：{{Number(item.independentAccount) === 1 ? '是' : '否'}}
                        </div> -->
                        <div>
                            企业编码：{{item.entityCode}}
                        </div>
                        <div>
                            账号：{{item.accountName}}
                        </div>
                        <!-- 目前还没确定怎么展示，请和春杰、门康沟通 -->
                        <!-- <div>
                            密码：{{item.accountPassword}}
                        </div> -->
                    </div>
                </div>
            </div>
        </div>

        <mt-dialog ref="dialog" css-class="dialog-form-flex" :header="headerMsg" :buttons="buttons" :open="onOpen">
            <div class="dialog-change-account">
                <!-- 设置账号 -->
                <div v-if="changeType === 'USERNAME'">
                    <div class="content">
                        <div class="title">姓名</div>
                        <mt-input 
                            :readonly="true" 
                            :show-clear-button="false" 
                            type="text" 
                            v-model="account.nickName">
                        </mt-input>
                    </div>
                    <div class="content">
                        <div class="title">账户名</div>
                        <mt-input 
                            type="text" 
                            v-model="modifiedUsername"
                            placeholder="请输入账户名">
                        </mt-input>
                    </div>
                    <div class="content">
                        <div class="tips">注意：您的账户名一经确认将无法更换，推荐采用系统推荐账户名进行登录。</div>
                    </div>
                </div>

                <!-- 修改邮箱 -->
                <div v-if="changeType === 'MAIL'" class="dialog-change-account">
                    <div class="content">
                        <div class="title"><span class="red">*</span>验证码</div>
                        <mt-input
                            placeholder="请输入验证码"
                            v-model="validateCode"
                            type="text">
                        </mt-input>
                        <div class="verification-code-btn" @click="getVerificationCode">获取验证码</div>
                    </div>
                    <div class="content">
                        <div class="title">新邮箱</div>
                        <mt-input 
                            type="text"
                            v-model="modifiedMail"
                            placeholder="请输入账户名"
                            @blur="validEmail">
                        </mt-input>
                        <p class="red phone--error" v-if="isShowEmailError">请输入正确的邮箱地址</p>
                    </div>
                </div>

                <!-- 修改手机号 -->
                <div v-if="changeType === 'MOBILE'" class="dialog-change-account">
                    <div class="content">
                        <div class="title"><span class="red">*</span>验证码</div>
                        <mt-input 
                            placeholder="请输入验证码"
                            v-model="validateCode"
                            type="text">
                        </mt-input>
                        <div class="verification-code-btn" @click="getVerificationCode">获取验证码</div>
                    </div>
                    <div class="content">
                        <div class="title">新手机号</div>
                        <mt-input 
                            type="text"
                            v-model="modifiedMobile"
                            placeholder="请输入新手机号"
                            @blur="validPhone">
                        </mt-input>
                        <p class="red phone--error" v-if="isShowPhoneError">请输入正确的手机号</p>
                    </div>
                    <div class="content">
                        <div class="title"><span class="red">*</span>验证码</div>
                        <mt-input
                            v-model="validateCodeNew"
                            placeholder="请输入验证码"
                            type="text">
                        </mt-input>
                        <div class="verification-code-btn" @click="getVerificationCodeNew">获取验证码</div>
                    </div>
                </div>

                <!-- 修改密码   -->
                <div v-if="changeType === 'PASSWORD'" class="dialog-change-account">
                    <div class="content">
                        <div class="title"><span class="red">*</span>验证码</div>
                        <mt-input 
                            v-model="validateCode"
                            placeholder="请输入验证码"
                            type="text">
                        </mt-input>
                        <div class="verification-code-btn" @click="getVerificationCode">获取验证码</div>
                    </div>
                    <div class="content">
                        <div class="title">新密码</div>
                        <mt-input 
                            type="password"
                            v-model="modifiedPassword"
                            placeholder="请输入账户名">
                        </mt-input>
                    </div>
                    <div class="content">
                        <div class="title">确认密码</div>
                        <mt-input 
                            type="password"
                            v-model="confirmPassword"
                            placeholder="请输入确认密码">
                        </mt-input>
                    </div>
                </div>
            </div>
        </mt-dialog>
    </div>
</template>
<script lang="ts">
import { phoneRegex, emailRegex } from '@/utils/validator'

export default {
  name: 'Account',
  props: {
    account: {
      type: Object,
      default: () => {} 
    }
  },
  data() {
    return {
      isShowPhoneError: false,
      isShowEmailError: false,
      accountList: [],
      changeType: '',
      headerMsg: '',
      modifiedUsername: '',
      validateMethod: 'SMS',
      confirmPassword: '',
      modifiedMail: '',
      modifiedMobile: '',
      modifiedPassword: '',
      validateCode: '',
      validateCodeNew: '',
      buttons: [
        {
          click: () => {
            (this as any).changeType = '' as string
            (this as any).confirmPassword = '' as string
            (this as any).modifiedMail = '' as string
            (this as any).modifiedMobile = '' as string
            (this as any).modifiedPassword = '' as string
            (this as any).modifiedUsername = '' as string
            (this as any).validateCode = '' as string
            (this as any).validateCodeNew = '' as string
            (this as any).$refs.dialog.ejsRef.hide()
          },
          buttonModel: { content: '取消' }
        },
        {
          click: async () => {
            const res = (this as any).$api.enterprise.changeAccount({
              changeType: (this as any).changeType,
              confirmPassword: (this as any).confirmPassword,
              modifiedMail: (this as any).modifiedMail,
              modifiedMobile: (this as any).modifiedMobile,
              modifiedPassword: (this as any).modifiedPassword,
              modifiedUsername: (this as any).modifiedUsername,
              validateCode: (this as any).validateCode,
              validateMethod: (this as any).validateMethod,
              newMobileValidateCode: (this as any).validateCodeNew
            })
            res.then((r: any) => {
              if (r.code === 200 && r.data) {
                (this as any).changeType = '' as string
                (this as any).confirmPassword = '' as string
                (this as any).modifiedMail = '' as string
                (this as any).modifiedMobile = '' as string
                (this as any).modifiedPassword = '' as string
                (this as any).modifiedUsername = '' as string
                (this as any).validateCode = '' as string
                (this as any).validateCodeNew = '' as string
                (this as any).$refs.dialog.ejsRef.hide()
              }
            })
          },
          buttonModel: { isPrimary: 'true', content: '确认' }
        }
      ]
    }
  },
  mounted() {
    (this as any).getAccount()
  },
  methods: {
    // 获取当前租户信息
    async getAccount() {
      const res = (this as any).$api.enterprise.getAccount()
      res.then((r: any) => {
        if (r.code === 200 && r.data) {
          (this as any).accountList = r.data
        }
      })
    },
    showPhone() {
      (this as any).$forceUpdate();
      (this as any).$set((this as any).account, '_phone', (this as any).account.phone)
    },
    showEmail() {
      (this as any).$forceUpdate();
      (this as any).$set((this as any).account, '_email', (this as any).account.email)
    },
    // 修改账号/密码/手机/邮箱
    changeAccountDetail(type: any) {
      (this as any).changeType = '' as string
      (this as any).confirmPassword = '' as string
      (this as any).modifiedMail = '' as string
      (this as any).modifiedMobile = '' as string
      (this as any).modifiedPassword = '' as string
      (this as any).modifiedUsername = '' as string
      (this as any).validateCode = '' as string
      (this as any).validateCodeNew = '' as string
      (this as any).changeType = type
      if (type === 'USERNAME') {
        (this as any).headerMsg = '设置账号'
      } else if (type === 'MAIL') {
        (this as any).headerMsg = '修改邮箱'
      } else if (type === 'MOBILE') {
        (this as any).headerMsg = '修改手机号'
      } else if (type === 'PASSWORD') {
        (this as any).headerMsg = '修改密码'
      }
      (this as any).$refs.dialog.ejsRef.show()
    },
    onOpen(args: any) {
      args.preventFocus = true
    },
    // 获取旧手机验证码
    async getVerificationCode() {
      const res = (this as any).$api.enterprise.getVerificationCode()
      res.then((r: any) => {
        if (r.code === 200 && r.data) {
                    
        }
      })
    },
    // 获取新手机验证码
    async getVerificationCodeNew() {
      if ((this as any).isShowPhoneError) return

      const res = (this as any).$api.enterprise.getVerificationCodeNew({
        mobile: (this as any).modifiedMobile
      })
      res.then((r: any) => {
        if (r.code === 200 && r.data) {
                    
        }
      })
    },
    validPhone() {
      const phone = (this as any).modifiedMobile

      if (phoneRegex.test(phone)) {
        (this as any).isShowPhoneError = false
      } else {
        (this as any).isShowPhoneError = true
      }
    },
    validEmail() {
      const email = (this as any).modifiedMail

      if (emailRegex.test(email)) {
        (this as any).isShowEmailError = false
      } else {
        (this as any).isShowEmailError = true
      }
    }
  },
  watch: {
    account(val: any) {
      if (val) {
        const phone = (this as any).account.phone as string
        const email = (this as any).account.email as string;
        (this as any).account._phone = phone.substr(0, 3) + '****' + phone.substr(7)
        const len = email.split('@')[0].length as number
        (this as any).account._email = email.split('@')[0].substr(0, len - 4) + '****' + '@' + email.split('@')[1]
      }
    } 
  }
}

</script>
<style lang="scss" scoped>
.account{
    .title{
        font-size: 20px;
        font-weight: bold;
        height: 60px;
        line-height: 60px;
        padding: 0 20px;
        border-bottom: 2px solid #E8E8E8;
    }
    .body{
        margin: 30px 20px;
        .body-title{
            font-size: 14px;
            font-weight: bold;
            padding-left: 10px;
            border-left: 2px solid #00469C;
        }
        .detail-account{
            border: 1px solid #E8E8E8;
            padding: 0 20px 0 20px;
            margin: 10px 0;
            line-height: 120px;
            border-radius: 4px;
            .left{
                display: inline-block;
                .circle{
                    width: 2px;
                    height: 2px;
                    border-radius: 50%;
                    background: #6386C1;
                    display: inline-block;
                    vertical-align: middle;
                    margin: 0 10px;
                }
                label{
                    color: #6386C1;
                }
                .left-reject{
                    color: #9A9A9A;
                    
                }
                .left-reject-circle{
                    background: #9A9A9A;
                }
            }
            .account-body{
                line-height: 36px;
                vertical-align: middle;
                display: inline-block;
                width: calc(100% - 120px);
                .account-logo{
                    float: left;
                    margin: 10px 20px 0 0;
                    img{
                        height: 80px;
                        width: 80px;
                        border-radius: 50%;
                    }
                }
                .account-top{
                    font-weight: bold;
                    div{
                        display: inline-block;
                        margin: 0 10px;
                    }
                    .stationName{
                        padding: 0 5px;
                        border-radius: 2px;
                        font-weight: normal;
                        background: rgba(237,161,51,0.1);
                        color: #EDA133;
                        font-size: 12px;
                        height: 18px;
                        line-height: 18px;
                        display: inline-block;
                    }
                    .departmentName{
                        padding: 0 5px;
                        border-radius: 2px;
                        font-weight: normal;
                        background: rgba(99,134,193,0.1);
                        color: #6386C1;
                        font-size: 12px;
                        height: 18px;
                        line-height: 18px;
                        display: inline-block;
                    }
                }
                .account-detail{
                    max-width: 1000px;
                    div{
                        display: inline-flex;
                        margin-right: 20px;
                        width: 200px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }
                }
            }
        }
        .right{
            float: right;
            color: #6386C1;
            label{
                margin: 0 10px;
                cursor: pointer;
            }
        }
        .detail-account:hover{
            border-left: 6px solid #6386C1;
        }
        .account-basic{
            margin-bottom: 40px;
            .account-basic-content{
                height: 50px;
                line-height: 30px;
                margin: 15px;
                .account-left{
                    display: inline-block;
                    .account-left-title{
                        font-weight: bold;
                        .account-left-btn{
                            font-weight: normal;
                            border: 1px solid #D3DFF5;
                            border-radius: 2px;
                            color: #6386C1;
                            font-size: 10px;
                            display: inline-block;
                            margin-left: 10px;
                            padding: 0 5px;
                            line-height: 16px;
                        }
                    }
                    .account-left-tips{
                        color: #9A9A9A;
                    }
                }
                .account-right{
                    float: right;
                    color: #4F5B6D;
                    .account-right-change{
                        display: inline-block;
                        cursor: pointer;
                    }
                    .line{
                        display: inline-block;
                        width: 50px;
                        height: 4px;
                        border-radius: 2px;
                        background: #DBD9DC;
                        margin-right: 10px;
                    }
                    .line-end{
                        margin-right: 40px;
                    }
                    .line-active{
                        background: #F4746F;
                    }
                    .account-right-content{
                        display: inline-block;
                        margin-right: 40px;
                        span{
                            margin-right: 5px;
                        }
                        i{
                            cursor: pointer;
                        }
                    }
                }
            }
        }
    }
}
.dialog-change-account{
    padding: 15px;
    .content{
        margin-bottom: 15px;
        position: relative;
        .title{
            font-weight: bold;
            margin-bottom: 10px;
            
        }
        .red{
            color: #FF1F32;
            margin-right: 5px;
        }
        .tips{
            color: #FF1F32;
        }
        .phone--error {
          position: absolute;
          bottom: -15px;  
        }
        .verification-code-btn{
            cursor: pointer;
            width: 144px;
            height: 40px;
            color: #FFFFFF;
            background-color: #EDA133;
            position: absolute;
            text-align: center;
            line-height: 40px;
            top: 17px;
            right: 0px;
        }
    }
}
</style>
