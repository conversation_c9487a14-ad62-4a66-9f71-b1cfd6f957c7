<template>
    <div class="body">
        <div class="body-title">我加入的企业</div>
        <div class="btn-list">
            <button class="wait" @click="changeJoin('1')" :class="typeJoin === '1' ? 'active' : ''">已加入</button>
            <button class="over" @click="changeJoin('2')" :class="typeJoin === '2' ? 'active' : ''">审核中</button>
            <button class="over" @click="changeJoin('3')" :class="typeJoin === '3' ? 'active' : ''">被驳回</button>
            <div class="add" @click="addNewBusiness">
                <mt-icon name="icon_solid_add"></mt-icon>
                申请新企业
            </div>
        </div>

        <div>
            <div class="detail-business" v-for="item in businessAddList" :key="item.id">
                <div class="left">
                    <div class="business-title">
                        <div class="circle" :class="typeJoin === '3' ? 'circle-reject' : ''"></div>
                        <label class="business-type" v-if="typeJoin === '1'">已加入</label>
                        <label class="business-type" v-if="typeJoin === '2'">审核中</label>
                        <label class="business-type business-type-reject" v-if="typeJoin === '3'">被驳回</label>
                        <div class="circle" :class="typeJoin === '3' ? 'circle-reject' : ''"></div>
                    </div>
                    <div class="business-logo">
                        <img :src="'/api/file/user/file/viewPublicImageFile?id=' + item.logoFileId">
                    </div>
                    <label>{{item.name}}</label>
                </div>
                <div class="right" v-if="typeJoin === '1'">
                    <label class="orange" v-if="item.defaultTenantFlag">默认</label>
                    <label v-if="!item.defaultTenantFlag" @click="setDefaultTenant(item.tenantId, item.userId)">设为默认</label>
                </div>
            </div>
        </div>

        <!-- 申请新企业弹窗 -->
        <mt-dialog ref="dialogAddBusiness" css-class="dialog-form-flex" header="申请新企业" :buttons="buttonsAddBusiness" :open="onOpen">
            <div class="diolog">
                <div class="content">
                    <div class="title"><span class="red">*</span>企业名称</div>
                    <mt-input 
                        autocomplete="off"
                        v-if="isEditType === '' || isEditType === 'name'"
                        type="text" 
                        v-model="businessName" 
                        placeholder="请输入企业名称">
                    </mt-input>
                    <mt-select
                        v-if="isEditType === 'code'"
                        :dataSource="dataNameList"
                        :showClearButton="true"
                        :allowFiltering="true"
                        @select="isEditType = ''"
                        @change="changeName"
                        placeholder="请选择企业名称"
                    ></mt-select>
                </div>
                <div class="content">
                    <div class="title"><span class="red">*</span>企业身份代码</div>
                    <mt-input 
                        autocomplete="off"
                        v-if="isEditType === '' || isEditType === 'code'"
                        type="text" 
                        v-model="businessCode" 
                        placeholder="请输入企业身份代码">
                    </mt-input>
                    <mt-select
                        v-if="isEditType === 'name'"
                        :dataSource="dataCodeList"
                        :showClearButton="true"
                        :allowFiltering="true"
                        @change="changeCode"
                        placeholder="请选择企业身份代码"
                    ></mt-select>
                </div>
                <div class="content">
                    <div class="title"><span class="red">*</span>申请说明</div>
                    <mt-input autocomplete="off" type="text" v-model="applyRemark" placeholder="字数在50字以内"></mt-input>
                </div>
            </div>
        </mt-dialog>
    </div>
</template>

<script lang="ts">
export default {
  name: 'BusinessAdd',
  data() {
    return {
      typeJoin: '',
      businessAddList: [],
      // 申请新企业-企业名称
      businessName: '',
      // 申请新企业-企业编码
      businessCode: '',
      // 申请新企业-申请说明
      applyRemark: '',
      // 申请新企业-企业id，用于后端传参提交
      tenantId: '',
      // 申请新企业按钮
      buttonsAddBusiness: [
        {
          click: () => {
            (this as any).$refs.dialogAddBusiness.ejsRef.hide()
          },
          buttonModel: { content: '取消' }
        },
        {
          click: async () => {
            const res = (this as any).$api.enterprise.addNewBusiness({
              tenantId: (this as any).tenantId,
              applyRemark: (this as any).applyRemark
            })
            res.then((r: any) => {
              if (r.code === 200 && r.data) {
                (this as any).$refs.dialogAddBusiness.ejsRef.hide()
              }
            })
          },
          buttonModel: { isPrimary: 'true', content: '提交' }
        }
      ],
      // 申请新企业，正在编辑的内容
      isEditType: ''
    }
  },
  mounted() {
    (this as any).changeJoin('1')
  },
  watch: {
    businessName(val: any) {
      let timer: any
      timer = setTimeout(() => {
        (this as any).changeBusinessName()
      }, 700) as any
      (this as any).$once('hook:beforeDestroy', () => {
        clearInterval(timer)
        timer = null
      })
    },
    businessCode(val: any) {
      let timer: any
      timer = setTimeout(() => {
        (this as any).changeBusinessCode()
      }, 700) as any
      (this as any).$once('hook:beforeDestroy', () => {
        clearInterval(timer)
        timer = null
      })
    }
  },
  methods: {
    // 切换加入的企业
    changeJoin(type: string) {
      (this as any).businessAddList = []
      const params = {
        approvalStatus: 1
      }
      if (type === '1') {
        (this as any).typeJoin = '1'
        params.approvalStatus = 1
      } else if (type === '2') {
        (this as any).typeJoin = '2'
        params.approvalStatus = -1
      } else if (type === '3') {
        (this as any).typeJoin = '3'
        params.approvalStatus = 0
      }
      const res = (this as any).$api.enterprise.queryAddDetail(params)
      res.then((r: any) => {
        if (r.code === 200 && r.data) {
          (this as any).businessAddList = r.data
        }
      })
    },
    // 申请新企业
    addNewBusiness() {
      (this as any).isEditType = '' as string;
      (this as any).businessName = '' as string;
      (this as any).businessCode = '' as string;
      (this as any).applyRemark = '' as string;
      (this as any).$refs.dialogAddBusiness.ejsRef.show()
    },
    onOpen(args: any) {
      args.preventFocus = true
    },
    // 设置默认租户/企业
    setDefaultTenant(tenantId: any, userId: any) {
      const res = (this as any).$api.enterprise.setDefaultTenant({
        tenantId: tenantId,
        userId: userId
      })
      res.then((r: any) => {
        if (r.code === 200 && r.data) {
                    
        }
      })
    },
        
    // 企业名称修改
    async changeBusinessName() {
      if ((this as any).businessName === '') {
        return
      }
      const res = (this as any).$api.enterprise.getEnterpriseByCodeOrName({
        name: (this as any).businessName
      })
      res.then((r: any) => {
        if (r.code === 200 && r.data) {
          if (r.data && r.data.length > 1) {
            (this as any).isEditType = 'name' as string
            (this as any).dataCodeList = r.data.map((v: any) => {
              return {
                text: v.code,
                value: v.code
              }
            })
          } else if (r.data && r.data.length === 1) {
            (this as any).isEditType = '' as string
            if (r.data[0].code !== (this as any).businessCode) {
              (this as any).businessCode = r.data[0].code;
              (this as any).tenantId = r.data[0].id
            }
          } else {
            (this as any).businessCode = ''
          }
        }
      }).catch(() => {
        (this as any).businessCode = ''
      })
    },
    // 企业身份代码修改
    async changeBusinessCode() {
      if ((this as any).businessCode === '') {
        return 
      }
      const res = (this as any).$api.enterprise.getEnterpriseByCodeOrName({
        code: (this as any).businessCode
      })
      res.then((r: any) => {
        if (r.code === 200 && r.data) {
          if (r.data && r.data.length > 1) {
            (this as any).isEditType = 'code' as string
            (this as any).dataNameList = r.data.map((v: any) => {
              return {
                text: v.name,
                value: v.name
              }
            })
          } else if (r.data && r.data.length === 1) {
            (this as any).isEditType = '' as string
            if (r.data[0].name !== (this as any).businessName) {
              (this as any).businessName = r.data[0].name;
              (this as any).tenantId = r.data[0].id
            }
          } else {
            (this as any).businessName = ''
          }
        }
      }).catch(() => {
        (this as any).businessName = ''
      })
    },
    // 企业身份代码，select选择事件
    changeCode(val: any) {
      if (val.value && val.value !== (this as any).businessCode) {
        (this as any).isEditType = '' as string;
        (this as any).businessCode = val.value
      }
    },
    // 企业名称，select选择事件
    changeName(val: any) {
      if (val.value && val.value !== (this as any).businessName) {
        (this as any).isEditType = '' as string;
        (this as any).businessName = val.value
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.body{
    margin: 30px 20px;
    .body-title{
        font-size: 14px;
        font-weight: bold;
        padding-left: 10px;
        border-left: 2px solid #00469C;
    }
    .btn-list{
        margin: 20px 0;
        button{
            color: #9A9A9A;
            background: #F5F6F7;
            padding: 5px 10px;
            font-size: 12px;
            cursor: pointer;
            border: 1px solid #E8E8E8;
        }
        .wait{
            border-radius: 2px 0 0 2px;
        }
        .over{
            border-radius: 0 2px 2px 0;
        }
        .active{
            background: #6386C1;
            color: #FFFFFF;
        }
        .add{
            float: right;
            color: #4F5B6D;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            line-height: 28px;
        }
    }
    .detail-business{
        border: 1px solid #E8E8E8;
        padding: 0 20px 0 10px;
        margin: 10px 0;
        line-height: 54px;
        .left{
            display: inline-block;
            .business-title{
                float: left;
                .circle{
                    display: inline-block;
                    width: 2px;
                    height: 2px;
                    border-radius: 50%;
                    background: #6386C1;
                    vertical-align: middle;
                    margin: 0 10px;
                }
                .circle-reject{
                    background: #9A9A9A;
                }
                .business-type{
                    color: #6386C1;
                }
                .business-type-reject{
                    color: #9A9A9A;
                }
            }
            .business-logo{
                float: left;
                line-height: 0;
                margin: 5px 20px 0 0;
                img{ 
                  width: 100px;
                  max-height: 40px;
                }
            }
        }
        .right{
            float: right;
            color: #6386C1;
            label{
                margin: 0 10px;
                cursor: pointer;
            }
            .version-name{
                color: #9A9A9A;
            }
        }
    }
}
.diolog{
    padding: 15px;
    .content{
        margin-bottom: 15px;
        .title{
            font-weight: bold;
            margin-bottom: 10px;
            .red{
                color: #FF1F32;
                margin-right: 5px;
            }
        }
        .apply-icon{
            color: #6386C1;
            padding-left: 5px;
            cursor: pointer;
        }
        .phone{
            display: inline-block;
            margin-left: 20px;
            width: calc(100% - 170px);
        }
        .pop-version{
            margin: 10px 0 5px 0;
            color: #FF1F32;
        }
    }
    .name{
        display: inline-block;
        width: 200px;
        margin-right: 20px;
    }
}
.approve{
    .table{
        border: 1px solid #E8E8E8;
        margin-bottom: 15px;
        .table-title{
            height: 46px;
            line-height: 46px;
            font-size: 14px;
            font-weight: bold;
            color: #6386C1;
            padding: 0 20px;
            i{
                float: right;
                font-size: 8px;
                margin-top: 17px;
            }
        }
        .table-body{
            padding: 20px;
            border-top: 1px solid #E8E8E8;
            .table-label{
                margin: 0 20px 15px 0;
                label{
                    margin-left: 10px;
                }
                .registerCapital{
                    width: calc(100% - 80px);
                }
                .capitalCurrency{
                    float: right;
                    width: 60px;
                    text-align: center;
                }
                .time{
                    width: calc((100% - 80px)/2);
                    margin-right: 10px;
                }
                .long-term{
                    width: 60px;
                }
                .address{
                    width: calc(15% - 15px);
                    margin-right: 15px;
                }
                .address-detail{
                    width: calc(60% - 45px);
                }
            }
        }
    }
}
.version-detail{
    margin: 10px 0;
}
.orange{
    color: #EDA133;
}
</style>
