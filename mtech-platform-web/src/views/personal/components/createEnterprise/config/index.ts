export const enterpriseInfoColumnData = [
  // { width: '60', type: 'checkbox' },
  { field: 'fileType', headerText: '文件类型', width: 150 },
  { field: 'fileId', headerText: '附件上传', width: 500 },  
  { field: 'template', headerText: '模版', width: 150 },
  { field: 'fileRule', headerText: '上传文件规则' }
  // { field: 'remark', headerText: '备注' } 
]

export const invoiceInfoColumnData = [
  // { width: '60', type: 'checkbox' },
  { 
    field: 'invoiceTitle', 
    headerText: '企业名称'
    
  },
  { field: 'taxNo', headerText: '税号' },
  { field: 'registerAddress', headerText: '注册地址' },
  { field: 'bankName', headerText: '开户银行' },
  { field: 'bankAccount', headerText: '银行账号' },
  { field: 'phoneNo', headerText: '电话号码' }
]

export const invoiceInfoPageConfig = [
  {   
    toolbar: [ 
      'add'
    ],
    
    grid: {
      // selectionSettings: { checkboxOnly: true },
      columnData: [
        { width: '60', type: 'checkbox' },
        { 
          field: 'invoiceTitle', 
          headerText: '企业名称'          
        },
        { field: 'taxNo', headerText: '税号' },
        { field: 'registerAddress', headerText: '注册地址' },
        { field: 'bankName', headerText: '开户银行' },
        { field: 'bankAccount', headerText: '银行账号' },
        { field: 'phoneNo', headerText: '电话号码' }
      ],
      dataSource: []
    }
  }
]
