<template>
  <mt-dialog 
    ref="dialog"
    header="选择服务区域"   
    class="select-industry--dialog"
    :buttons="buttons"
    @close="hide">  

    <div class="search--wrap">
      <label class="">选择服务区域：</label>
      <mt-input 
        ref="input"
        class="search-input"
        cssClass="e-outline"
        v-model="searchText" 
        placeholder="请输入关键字进行过滤"
        @keyup.enter.native="searchTextByEnter"
        @change="searchTextFromTree" />
    </div>

    <mt-treeView  
      class="tree-view--template"
      ref="treeView" 
      :fields="sourceTreeData"
      :auto-check="true"
      :show-check-box="true"
      @nodeSelecting="nodeSelecting"
    ></mt-treeView> 

  </mt-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from '@mtech/vue-property-decorator'

@Component({ 
  components: {}
})
export default class ServiceAreaDialog extends Vue {
  @Prop()
  value !: boolean 

  buttons = [
    {
      click: this.hide,
      buttonModel: { content: '取消' }
    },
    {
      click: this.handleConfirm,
      buttonModel: { isPrimary: 'true', content: '确定' }
    }
  ]

  searchAreaList:any[] = [] // 查询出来的地区列表
  allAreaList: any[] = [] // 完整的地区列表

  sourceTreeData = {
    dataSource: [],
    id: 'areaCode',
    text: 'areaName', 
    parentID: 'parentCode'
  }

  searchText = ''

  get visible() {
    return this.value
  }

  set visible(v: boolean) {
    this.$emit('input', v)
  }  

  @Watch('visible') 
  onWatchVisible(val: boolean) {
    if (val) {
      this.show()
    } 
  }

  mounted() {
    this.getServiceArea()
  }

  hide() {
    this.visible = false
    const ref: any = this.$refs.dialog
    ref.ejsRef.hide()
  }

  show() {    
    const ref: any = this.$refs.dialog
    ref.ejsRef.show()   
  } 

  nodeSelecting(event: any) { 
    const { nodeData } = event
    if (!nodeData.parentID && !nodeData.hasChildren && !nodeData.selected) { // 省份级别
      this.getServiceArea(nodeData.id) 
    }
  }

  searchTextByEnter(event:any) { 
    (this.$refs.input as any).ejsInstances.focusOut()
  }

  searchTextFromTree() {
    const ref = this.$refs.treeView as any 
    ref.ejsInstances.removeNodes([...this.allAreaList, ...this.searchAreaList].map((v: any) => v.areaCode))

    if (this.searchText) {
      this.$api.common.getAreaListByCriteria({
        areaName: this.searchText
      }).then((res:any) => {
        if (res.code === 200 && res.data) {  
          // this.sourceTreeData = Object.assign({}, this.sourceTreeData, {
          //   dataSource: res.data
          // })
          ref.ejsInstances.addNodes(res.data)
          this.searchAreaList = res.data
        }
      }) 
    } else {
      ref.ejsInstances.addNodes(this.allAreaList)
      // this.sourceTreeData = Object.assign({}, this.sourceTreeData, {
      //   dataSource: this.allAreaList
      // })
    }
  }

  private handleConfirm() {
    const ref = this.$refs.treeView as any 
    const allCheckedNodes = ref.ejsInstances.getAllCheckedNodes()

    const treeDataList = allCheckedNodes.map((v: string) => {
      return ref.ejsInstances.getNode(v)
    })

    this.$emit('save', treeDataList)
    this.hide()
  }
 
  private async getServiceArea(code = '') { 
    const res = await this.$api.common.getAreaListByParent({
      parentCode: code
    })

    if (res.code === 200 && res.data) {
      const ref = this.$refs.treeView as any
      ref.ejsInstances.addNodes(res.data)
      this.allAreaList.push(...res.data)
    } 
  }
}
</script>

<style lang="scss" scoped> 
.search--wrap {
  margin: 20px ;
  display: flex;
  align-items: center;
  label {
    color: #3A3A3A;
  }
  .search-input {
    flex: 1;
  }

}
</style>
