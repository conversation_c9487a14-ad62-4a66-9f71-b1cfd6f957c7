<template>
  <tr class="enterprise-info--row">
    <td class="file-row file-type--row">
      <p class="title">
        {{ data.invoiceTitle }}
      </p>
      <div class="button--wrap">
        <span @click="editRowData" class="text-button">
          <i class="mt-icons mt-icon-icon_Editor"></i>
          编辑
        </span>

        <span  @click="deleteRowData" class="text-button">
          <i class="mt-icons mt-icon-icon_Delete"></i>
          删除
        </span> 
      </div>

    </td>
    <td class="file-row file-rule--row">
      {{data.taxNo}}
    </td>

    <td class="file-row file-template--row"> 
      {{data.registerAddress}} 
    </td>

    <td class="file-row file-id--row"> 
     {{data.bankName}}
    </td> 

    <td class="file-row file-id--row"> 
     {{data.bankAccount}}
    </td> 

    <td class="file-row file-id--row"> 
     {{data.phoneNo}}
    </td> 

  </tr>
</template>

<script lang="ts">
import { Component, Vue } from '@mtech/vue-property-decorator' 
import bus from './config/eventBus' 

@Component({ 
})
export default class InvocieInfoRow extends Vue { 
  data:any = {} 
  isShowError = false 

  mounted() {
    bus.$on('EnterpriseInfoRow-Error', this.showErrorTip)
  } 

  editRowData() {
    bus.$emit('InvoiceInfoRow-Edit', this.data)
  }

  deleteRowData() {
    bus.$emit('InvoiceInfoRow-Delete', this.data)
  }

  private showErrorTip(data: any) {
    if (data.index === this.data.index) {
      this.isShowError = !data.fileId
    }
  }
}
</script>

<style lang="scss" scoped> 
.file-row {
  padding: 10px;
}
.file-type--row {
  .title {
    color: #00469C;
  }
  .tile-error--tip {
    color: #E50011;
  }
}
.button--wrap{
  font-size: 12px;
  line-height: 24px;
  color: #6386C1;
  .text-button {
    margin-right: 12px;
    cursor: pointer;
  }
}

</style>
