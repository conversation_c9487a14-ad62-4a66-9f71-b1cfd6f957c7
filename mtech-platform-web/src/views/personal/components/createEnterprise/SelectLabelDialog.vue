<template>
  <mt-dialog 
    ref="dialog"
    header="添加标签"   
    class="select-industry--dialog"
    :buttons="buttons"
    @close="hide">  

    <div class="search--wrap">
      <!-- <label class="">选择主要产品/服务：</label> -->
      <mt-input 
        ref="input"
        class="search-input"
        cssClass="e-outline"
        v-model="searchText" 
        placeholder="请输入文字搜索标签"
        @keyup.enter.native="searchTextByEnter"
        @change="searchTextFromTree" />
    </div>

    <div class="selected-label--wrap">
      <mt-tag 
        v-for="tag in selectedLabelList" 
        :key="tag.itemCode"
        :showCloseIcon="true"
        class="tag"
        @close="closeServiceItem(index)">
        {{tag.name}}</mt-tag>
    </div>

    <mt-row class="label-content--wrap">
      <mt-col :span="12" class="label-recommend">
        <div class="label-content--subtitle label-recommend--title">推荐</div>

        <div class="label-recommend--list">
          <p class="label-recommend--item"
             v-for="recommend in recommendLabels" 
             :key="recommend.itemCode"
             @click="selectRecommend(recommend)">{{recommend.name}}</p>
        </div>
      </mt-col>

      <mt-col :span="12" class="label-list">
        <div class="label-content--subtitle label-list--title">添加标签</div>
        <div class="label-item--list">
          <span v-for="label in labelItemList" :key="label.itemCode" @click="selectLabel(label)">{{label.name}}</span>
        </div>
      </mt-col>

    </mt-row>

  </mt-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from '@mtech/vue-property-decorator'

@Component({ 
  components: {}
})
export default class SelectLabelDialog extends Vue {
  @Prop()
  value !: boolean 

  MAX_COUNT = 5

  buttons = [
    {
      click: this.hide,
      buttonModel: { content: '取消' }
    },
    {
      click: this.handleConfirm,
      buttonModel: { isPrimary: 'true', content: '确定' }
    }
  ]

  recommendLabels:any[] = [] // 推荐的标签分类
  labelItemList: any[] = [] // 选择的分类下的标签 
  selectedLabelList: any[] = []
  searchText = '' 

  get visible() {
    return this.value
  }

  set visible(v: boolean) {
    this.$emit('input', v)
  }  

  @Watch('visible') 
  onWatchVisible(val: boolean) {
    if (val) {
      this.show()
    } 
  }

  mounted() {
    this.getLabelData()
  }

  hide() {
    this.visible = false
    const ref: any = this.$refs.dialog
    ref.ejsRef.hide()
  }

  show() {    
    const ref: any = this.$refs.dialog
    ref.ejsRef.show()   
  } 

  selectRecommend(recommend: any) {
    this.labelItemList = recommend?.children || []
  }

  selectLabel(label:any) {
    if (!this.selectedLabelList.find(v => v.itemCode === label.itemCode)) {
      this.selectedLabelList.push(label)
    } 
  }

  searchTextByEnter(event:any) { 
    (this.$refs.input as any).ejsInstances.focusOut()
  }

  searchTextFromTree() {
    // const ref = this.$refs.treeView as any  

    this.$api.common.getDictItemTree({
      dictCode: 'EnterpriseLabel',
      nameLike: this.searchText
    }).then((res:any) => {
      if (res.code === 200) {
        this.recommendLabels = res.data || []
      }
    }) 
  }

  closeServiceItem(index: number) {
    this.selectedLabelList.splice(index, 1)
  }
 
  private handleConfirm() { 
    if (this.selectedLabelList.length >= this.MAX_COUNT) {  
      this.$toast({
        content: `最多添加${this.MAX_COUNT}个标签`,
        type: 'warning'
      }) 
      return
    } 
    this.$emit('save', this.selectedLabelList)
    this.hide()
  }
 
  private async getLabelData(text = '') { 
    const res = await this.$api.common.getDictItemTree({
      dictCode: 'EnterpriseLabel',
      nameLike: text
    })

    if (res.code === 200 && res.data) { 
      this.recommendLabels = res.data
    } 
  }
}
</script>

<style lang="scss" scoped> 
.search--wrap {
  margin: 40px 20px 20px;
}
.label-content--wrap {
  margin: 0 20px;
  height: calc(100% - 65px);
}
.selected-label--wrap {
  margin: 20px;
}
.label-recommend {
      padding-right: 20px;
    border-right: 1px dashed #E8E8E8;
    height: 100%;

  .label-recommend--title {
    color: #00469C;
  }
  .label-recommend--item {
    line-height: 24px;
    cursor: pointer;
  }
} 

.label-content--subtitle {
  margin-bottom: 20px;
}

.label-list {
    padding-left: 20px;

  .label-list--title {
    &::before {
      content: " ";
      margin-right: 10px;
      border-left: 4px solid #00469C;
      border-top-right-radius: 3px;
      border-bottom-right-radius: 3px;
    }
  }

  .label-item--list {
    >span {
      background: rgba(240,244,249,1);
      color: rgba(24,43,62,1);
      border-radius: 2px;
      padding: 5px 10px;
      margin-right: 10px;
      margin-bottom: 14px;
      cursor: pointer;
    }
  }
  
}

</style>
