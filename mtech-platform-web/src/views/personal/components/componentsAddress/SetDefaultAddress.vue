<template>
  <div class="set-default-address">   
    <!-- <mt-checkbox-group v-model="addressPurpose" >
      <mt-checkbox :content="{label: '收货地址',id: 'defaultDeliveryFlag'}" ></mt-checkbox>
      <mt-checkbox :content="{label: '收票地址',id: 'defaultTicketFlag'}" ></mt-checkbox>  
    </mt-checkbox-group>  -->

    <mt-checkbox label="收货地址" :disabled="!isEnabledDeliveryCheckbox" :checked="defaultDeliveryFlag" @change="changeDeliveryFlag" ></mt-checkbox>
    <mt-checkbox label="收票地址" :disabled="!isEnabledTicketCheckbox" :checked="defaultTicketFlag" @change="changeTicketFlag"></mt-checkbox>

    <div class="button--wrap"  >
      <mt-button type="text" plain style="margin-right: 12px;" @click="handleCancel">取消</mt-button>
      <mt-button type="primary" @click="handleConfirm">确定</mt-button> 
    </div>
  </div>
</template>
<script lang="ts"> 
import { Component, Vue } from '@mtech/vue-property-decorator'   
import bus from '@/utils/bus'

@Component({
  components: {    
  }
})
export default class SetDefaultAddress extends Vue {
  addressData: any = {} 

  defaultDeliveryFlag = false
  defaultTicketFlag = false

  get isEnabledDeliveryCheckbox() {
    return !!this.addressData.useForDelivery
  }

  get isEnabledTicketCheckbox() {
    return !!this.addressData.useForTicket
  }

  mounted() {
    bus.$on('dataBind', this.setAddressPurpose)
  }

  beforeDestroy() {
    bus.$off('dataBind', this.setAddressPurpose)
  }

  handleCancel() {
    bus.$emit('close', this.addressData.id)
  }

  private setAddressPurpose(data: any) {
    this.addressData = data
    this.defaultDeliveryFlag = !!data.defaultDeliveryFlag
    this.defaultTicketFlag = !!data.defaultTicketFlag
  }

  changeDeliveryFlag(event: any) { 
    this.defaultDeliveryFlag = event.checked
  }

  changeTicketFlag(event: any) { 
    this.defaultTicketFlag = event.checked
  }

  handleConfirm() { 
    const param = {
      id: this.addressData.id,
      defaultDeliveryFlag: +this.defaultDeliveryFlag,
      defaultTicketFlag: +this.defaultTicketFlag
    }
    this.$api.address.addressDefault(param).then((res: any) => {
      this.$toast({
        type: 'success',
        content: res.msg || '更新成功'
      })
      bus.$emit('confirm', param)
    }).catch((err: any) => {
      this.$toast({
        type: 'error',
        content: err.msg || '更新失败'
      })
    })
  } 
}
</script>
<style lang="scss" scoped>  
.set-default-address {
  background: #fff; 
  width: 200px;
  padding: 24px 12px 12px;
}

.button--wrap {
  text-align: right;
  margin-top: 24px;
}
</style>
