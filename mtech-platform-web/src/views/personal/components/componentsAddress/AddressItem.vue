<template>
  <div class="address-item">  
    <mt-form> 
      <mt-form-item label="别名："  labelWidth="100px" labelStyle="left">
        <div v-show="isShowEditBtn">
          <mt-icon class="address-item--icon icon-color" name="icon_solid_editsvg" @click.native="editAlias" />
          <span>{{address.alias}}</span>
          <span class="address-item--defaultname">{{defaultAddressName}}</span>
        </div>
        
        <div v-show="!isShowEditBtn" class="address-alias--wrap" >
          <mt-input v-model="newAlias" class="address-alias--input" maxlength="30" placeholder="请输入地址名称，建议名称： 公司，仓库等"></mt-input>
          <mt-button type="primary" @click="saveAlias">保存</mt-button>
        </div>
      </mt-form-item>

      <mt-form-item label="地址用途：" labelWidth="100px" labelStyle="left"> 
        <span style="margin-right: 12px;"> 
          <mt-icon v-if="address.useForDelivery" name="a-icon_MultipleChoice_on" class="icon-color"></mt-icon>
          <mt-icon v-else name="a-icon_MultipleChoice_off" class="icon-color"></mt-icon>
          <span style="margin-left: 12px;">收货地址</span>
        </span>

        <span> 
          <mt-icon v-if="address.useForTicket" name="a-icon_MultipleChoice_on" class="icon-color"></mt-icon>
          <mt-icon v-else name="a-icon_MultipleChoice_off" class="icon-color"></mt-icon>
          <span style="margin-left: 12px;">收票地址</span>
        </span>

        <!-- <mt-checkbox-group  >
          <mt-checkbox :checked="address.useForDelivery" label="收货地址" value="useForDelivery" :readonly="true"></mt-checkbox>
          <mt-checkbox :checked="address.useForTicket" label="收票地址" value="useForTicket" :readonly="true"></mt-checkbox>
        </mt-checkbox-group> -->
      </mt-form-item>

      <mt-row>
        <mt-col :span="12">
          <mt-form-item label="收货人：" labelWidth="100px" labelStyle="left">{{ address.name }}</mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item label="联系方式：" labelWidth="100px" labelStyle="left">{{ address.phone }}</mt-form-item>
        </mt-col>
      </mt-row> 

      <mt-form-item label="所在地区：" labelWidth="100px" labelStyle="left"> 
        {{ address.location }}
      </mt-form-item>
      <mt-form-item label="详细地址：" labelWidth="100px" labelStyle="left">
        {{ address.fullAddress }}
      </mt-form-item>
      <mt-row>
        <mt-col :span="12">
          <mt-form-item label="固定电话：" labelWidth="100px" labelStyle="left">
          {{ address.staticPhone || '暂无' }}
        </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item label="邮编：" labelWidth="100px" labelStyle="left">
            {{ address.postCode || '暂无' }}
          </mt-form-item>
        </mt-col>
      </mt-row>

      <mt-row>
        <mt-col :span="12">
          <mt-form-item label="紧急联系人：" labelWidth="100px" labelStyle="left">
            {{ address.emergencyPerson || '暂无' }}
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item label="紧急联系电话：" labelWidth="100px" labelStyle="left">
          {{ address.emergencyPhone || '暂无' }}
        </mt-form-item>
        </mt-col>
      </mt-row>

    </mt-form >

    <div class="button-wrap">
      <mt-tool-tip
        v-if="isShowDefaultAddressBtn"
        :target="`#${addressId}`" 
        ref="tooltipTitle"  
        :content="tooltipContent" 
        :beforeOpen="beforeOpen"
        position="BottomCenter"
        tipPointerPosition="Middle"
        opensOn="Click"
        class="address-item--btn"
        style="width: inherit;"
        @close="closeTooltip"
      >
        <mt-button :id="addressId" type="primary" @click="handleDefaultAddress">设为默认地址</mt-button>        
      </mt-tool-tip>
      <mt-button v-if="isShowDeleteBtn" class="address-item--btn" type="primary" @click="handleDelete">删除</mt-button>
      <mt-button  type="primary" @click="handleEdit">编辑</mt-button> 
    
    </div>
  </div>
</template>
<script lang="ts"> 
import { Component, Prop, Vue } from '@mtech/vue-property-decorator'  
import SetDefaultAddress from './SetDefaultAddress.vue'
import bus from '@/utils/bus'

@Component({
  components: {
    SetDefaultAddress
  }
})
export default class AddressItem extends Vue { 
  @Prop({
    default: {
      id: '',
      alias: '',
      useForDelivery: 0,
      useForTicket: 0,
      name: '', // 收货人
      phone: '', // 联系方式
      fullAddress: '', // 详细地址
      staticPhone: '', // 固定电话
      postCode: '', // 邮编
      emergencyPerson: '', // 紧急联系人
      emergencyPhone: '' // 紧急联系人电话
    }
  })
  address !: any
 
  newAlias = ''
  isShowEditBtn = true

  tooltipContent = function() {
    return {
      template: SetDefaultAddress
    }
  }

  get addressId() {
    return `default-address-${this.address.id}`
  }
 
  get isShowDefaultAddressBtn () {
    const { defaultDeliveryFlag, defaultTicketFlag, useForDelivery, useForTicket } = this.address
    if (defaultDeliveryFlag && defaultTicketFlag && useForDelivery && useForTicket) {
      return false
    } else if (defaultDeliveryFlag && useForDelivery) {
      return false
    } else if (defaultTicketFlag && useForTicket) {
      return false
    } else {
      return true
    }
  }

  get isShowDeleteBtn() {
    const { defaultDeliveryFlag, defaultTicketFlag } = this.address 
    return !(defaultDeliveryFlag || defaultTicketFlag)
  }

  get defaultAddressName() {
    const { defaultDeliveryFlag, defaultTicketFlag } = this.address 
 
    if (defaultDeliveryFlag && defaultTicketFlag) {
      return '默认地址（收货、收票）'
    } else if (defaultDeliveryFlag) {
      return '默认地址（收货）'
    } else if (defaultTicketFlag) {
      return '默认地址（收票）'
    } else {
      return ''
    }
  }
 
  created() {
    bus.$on('close', this.closeTooltip)
    bus.$on('confirm', this.confirmTooltip)
  }

  beforeDestroy() {
    bus.$off('close', this.closeTooltip)
    bus.$off('confirm', this.confirmTooltip)
  }

  handleDelete() {
    // if (this.isDefaultAddress) {
    //   this.$toast({
    //     type: 'warning',
    //     content: '默认地址不可删除'
    //   })
    //   return
    // }
    this.$dialog({
      data: {
        title: '删除',
        message: '删除后将不可恢复，您是否确认删除该地址？'
      },
      success: () => {
        this.deleteAddress()
      }
    })
  }

  handleEdit() {
    const param = Object.assign({}, this.address) 
    this.$emit('edit', param)
  }

  handleDefaultAddress() { 
  }

  beforeOpen(event: any) {
    const { element } = event
    element.style.backgroundColor = '#fff'
    bus.$emit('dataBind', this.address)
  }

  closeTooltip(id: string) { 
    const ref = this.$refs.tooltipTitle as any
    ref && ref.ejsRef.close()
  }

  confirmTooltip(data: any) {
    const { defaultDeliveryFlag, defaultTicketFlag, id } = data 
    if (id === this.address.id) {
      this.address.defaultDeliveryFlag = defaultDeliveryFlag
      this.address.defaultTicketFlag = defaultTicketFlag  
      this.$emit('refresh') 
      this.closeTooltip(data.id)
    } 
  }

  editAlias() {
    this.newAlias = this.address.alias
    this.isShowEditBtn = false
  }

  saveAlias() {
    this.$api.address.addressAlias({
      id: this.address.id,
      alias: this.newAlias
    }).then((res:any) => {
      this.address.alias = this.newAlias
      this.isShowEditBtn = true
    }).catch((err: any) => {
      this.$toast({
        type: 'error',
        content: err.msg || '更新失败'
      })
    })
  } 

  private deleteAddress() {
    this.$api.address.addressDelete({
      id: this.address.id
    }).then((res: any) => {
      this.$toast({
        type: 'success',
        content: res.msg || '删除成功'
      })
      this.$emit('delete', this.address.id)
    }).catch((err: any) => {
      this.$toast({
        type: 'error',
        content: err.msg || '删除失败'
      })
    })
  }
}
</script>
<style lang="scss" scoped> 
.address-item {
  border: 1px solid #E8E8E8;
  position: relative;
  padding: 24px 24px 24px 0;
  /deep/ .mt-form-item {
    height: 35px;
  }
  .button-wrap {
    position: absolute;
    bottom: 36px;
    right: 24px;
  }
  .address-item--btn {
    margin-right: 12px;
  }
  .address-item--icon {
    cursor: pointer;    
    margin-right: 12px;
  }
  .address-item--defaultname {
    color: #00469c;
    margin-left: 20px;
  }
  .address-alias--wrap {
    width: 100%;
    .address-alias--input {
      width: 300px;
      margin-right: 24px;
    }
  }
  .icon-color {
    color: #00469c;
  }
}

</style>
