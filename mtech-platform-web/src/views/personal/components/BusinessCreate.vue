<template>
    <div class="body">
        <div class="body-title">我创建的企业</div>
        <div class="btn-list">
            <button class="wait" @click="changeCreat('1')" :class="typeCreat === '1' ? 'active' : ''">已创建</button>
            <button class="over" @click="changeCreat('2')" :class="typeCreat === '2' ? 'active' : ''">审核中</button>
            <button class="over" @click="changeCreat('3')" :class="typeCreat === '3' ? 'active' : ''">被驳回</button>
            <div class="add" @click="createNewBusiness">
                <mt-icon name="icon_solid_add"></mt-icon>
                创建新企业
            </div>
        </div>

        <div>
            <div class="detail-business" v-for="item in businessCreateList" :key="item.id">
                <div class="left">
                    <div class="business-title">
                        <div class="circle" :class="typeCreat === '3' ? 'circle-reject' : ''"></div>
                        <label class="business-type" v-if="typeCreat === '1'">已创建</label>
                        <label class="business-type" v-if="typeCreat === '2'">审核中</label>
                        <label class="business-type business-type-reject" v-if="typeCreat === '3'">被驳回</label>
                        <div class="circle" :class="typeCreat === '3' ? 'circle-reject' : ''"></div>
                    </div>
                    <div class="business-logo">
                        <img :src="'/api/file/user/file/viewPublicImageFile?id=' + item.enterpriseLogoFileId">
                    </div>
                    <label>{{item.enterpriseName}}</label>
                </div>
                <div class="right" v-if="typeCreat === '1'">
                    <label class="version-name" v-if="item.versionStatus === 1">{{item.versionName}}</label>
                    <label class="version-name" v-if="item.versionStatus === -1">{{item.versionName}}（申请中）</label>
                    <label v-if="item.versionStatus === 0|| item.versionStatus === null" @click="applyOpen(item.id, item.enterpriseName)">申请开通</label>
                </div>
                <div class="right" v-if="typeCreat === '3'">
                    <label @click="showReject('create', item.id)">查看</label>
                </div>
            </div>
        </div>

        <!-- 查看驳回弹窗 -->
        <mt-dialog ref="dialogApprove" header="查看" :buttons="buttonsApprove" :open="onOpen"  >
            <div class="approve">
                <div class="content diolog">
                    <div class="title">审批意见</div>
                    <mt-input type="text" :readonly="true" v-model="approveData.rejectReason"></mt-input>
                </div> 
                <EnterpriseDetail v-if="rejectEnterpriseId" :enterpriseId="rejectEnterpriseId" @success="getEnterpriseDetail"  /> 
            </div>
        </mt-dialog>

        <!-- 申请开通弹窗 -->
        <mt-dialog ref="dialogApplyOpen" css-class="dialog-form-flex" header="申请开通权限" :buttons="buttonsApplyOpen" :open="onOpen">
            <div class="apply-open diolog">
                <div class="content">
                    <div class="title">企业名称</div>
                    <mt-input :readonly="true" :showClearButton="false" type="text" v-model="enterpriseName" placeholder="请输入企业名称"></mt-input>
                </div>
                <div class="content">
                    <div class="title">
                        <span class="red">*</span>申请权限
                        <mt-icon class="apply-icon" name="icon_solid_Information" @click.native="showVersionDetail"></mt-icon>
                    </div>
                    <mt-radio v-model="versionId" :dataSource="versionData"></mt-radio>
                    <div class="pop-version">
                        <mt-icon name="icon_solid_inform"></mt-icon>
                        提示：您当前申请的版本【{{getVersionName(versionId)}}】为【{{getVersionPrice(versionId) === '0' ? '免费版' : '付费版'}}】，我们将在2个工作日为您完成审核！
                    </div>
                    
                </div>
                <div class="content">
                    <div class="title"><span class="red">*</span>手机号</div>
                    <mt-select
                        :width="150"
                        :dataSource="phonePrefixList"
                        :value="globalRoaming"
                    ></mt-select>
                    <mt-input class="phone" type="text" v-model="phone" placeholder="请输入手机号" @blur="validPhone"></mt-input>
                    <p class="red phone--error" v-show="isShowPhoneError">请输入正确的手机号</p>
                </div>
                <div class="content name">
                    <div class="title"><span class="red">*</span>您的姓</div>
                    <mt-input type="text" v-model="firstName" placeholder="请输入您的姓"></mt-input>
                </div>
                <div class="content name">
                    <div class="title"><span class="red">*</span>您的名</div>
                    <mt-input type="text" v-model="lastName" placeholder="请输入您的名"></mt-input>
                </div>
                <div class="content">
                    <div class="title">申请备注</div>
                    <mt-input type="text" maxlength="200" :multiline="true" :rows="3" v-model="remark" placeholder="字数不超过200字"></mt-input>
                </div>
            </div>
        </mt-dialog>

        <!-- 查看版本详情 -->
        <mt-dialog ref="dialogVersion" css-class="dialog-form-flex" header="查看" :open="onOpen">
            <div class="version-detail">
                <mt-tabs
                :eTab="false"
                :dataSource="versionData"
                tabsWidth="700px"
                @handleSelectTab="handleVersionSelect"
                ></mt-tabs>

                <div>
                    <mt-treeView v-if="fileds.dataSource.length" :fields="fileds"></mt-treeView>
                </div>
            </div>
        </mt-dialog>
        
    </div>
</template>
<script lang="ts"> 
import EnterpriseDetail from '@/components/enterpriseDetail/index.vue'
import { phoneRegex } from '@/utils/validator'

export default {
  components: { 
    EnterpriseDetail
  },
  name: 'BusinessCreate',
  props: {
    refresh: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return { 
      // 申请权限-版本id
      isShowPhoneError: false,
      versionId: '',
      typeCreat: '',
      rejectEnterpriseId: '', // 我创建的企业-被驳回-当前查看的企业id
      businessCreateList: [],
      // 查看驳回弹窗数据
      approveData: {},
      visibleIndustry: false,
      visibleService: false,
      visibleBusinessProve: false,
      visibleInvoice: false, 
      phone: '',
      firstName: '',
      lastName: '',
      remark: '',
      enterpriseId: '',
      enterpriseName: '',
      // 手机号前缀
      globalRoaming: 1,
      // 查看驳回按钮
      buttonsApprove: [
        {
          click: (this as any).closeApproveDialog,
          buttonModel: { content: '关闭' }
        },
        {
          click: (this as any).approveRejectEnterprise,
          buttonModel: { isPrimary: 'true', content: '再次申请' }
        }
      ],
      // 申请开通按钮
      buttonsApplyOpen: [
        {
          click: () => {
            (this as any).enterpriseId = '' as string;
            (this as any).firstName = '' as string;
            (this as any).lastName = '' as string;
            (this as any).phone = '' as string;
            (this as any).remark = '' as string; 
            (this as any).globalRoaming = 1;
            (this as any).$refs.dialogApplyOpen.ejsRef.hide()
          },
          buttonModel: { content: '取消' }
        },
        {
          click: async () => {
            if ((this as any).isShowPhoneError) return

            const params = {
              enterpriseId: (this as any).enterpriseId,
              name: (this as any).firstName + (this as any).lastName,
              phone: (this as any).phone,
              remark: (this as any).remark,
              versionId: (this as any).versionId,
              globalRoaming: (this as any).globalRoaming
            }
            const res = (this as any).$api.enterprise.addEnterprise(params)
            res.then((r: any) => {
              if (r.code === 200 && r.data) {
                (this as any).enterpriseId = '' as string;
                (this as any).firstName = '' as string;
                (this as any).lastName = '' as string;
                (this as any).phone = '' as string;
                (this as any).remark = '' as string; 
                (this as any).globalRoaming = 1;
                // 重载创建企业列表
                (this as any).changeCreat('1');
                (this as any).$refs.dialogApplyOpen.ejsRef.hide()
              }
            })
          },
          buttonModel: { isPrimary: 'true', content: '提交' }
        }
      ],
      // 申请权限-可选项
      versionData: [],
      phonePrefixList: [
        {
          text: '中国大陆+86',
          value: 1
        },
        {
          text: '中国台湾地区+886',
          value: 2
        },
        {
          text: '中国香港+852',
          value: 3
        },
        {
          text: '中国澳门+853',
          value: 4
        }
      ],
      // 版本信息详情
      fileds: {
        dataSource: [],
        id: 'id',
        text: 'name',
        child: 'subChild'
      },
      dataNameList: [],
      dataCodeList: []
    }
  },
  mounted() {
    (this as any).changeCreat('1');
    // 初始化获取版本信息
    (this as any).getDropDownList()
  },
  watch: {
    refresh(val: boolean) {
      if (val) {
        const typeCreat = (this as any).typeCreat; 
        (this as any).changeCreat(typeCreat)
      }
    }
  },
  methods: {
    // 切换创建的企业
    changeCreat(type: string) {
      if (type === '1') {
        (this as any).typeCreat = '1'
        const res = (this as any).$api.enterprise.queryCreate()
        res.then((r: any) => {
          if (r.code === 200 && r.data) {
            (this as any).businessCreateList = r.data
          }
        })
      } else if (type === '2') {
        (this as any).typeCreat = '2'
        const res = (this as any).$api.enterprise.queryPending()
        res.then((r: any) => {
          if (r.code === 200 && r.data) {
            (this as any).businessCreateList = r.data
          }
        })
      } else if (type === '3') {
        (this as any).typeCreat = '3'
        const res = (this as any).$api.enterprise.queryReject()
        res.then((r: any) => {
          if (r.code === 200 && r.data) {
            (this as any).businessCreateList = r.data
          }
        })
      }
    },
    // 创建新企业
    createNewBusiness() { 
      (this as any).$emit('create', true)
    },  
    // 显示驳回弹窗
    async showReject(type: string, id: number) { 
      (this as any).rejectEnterpriseId = id;
      (this as any).visibleIndustry = false;
      (this as any).visibleService = false;
      (this as any).visibleBusinessProve = false;
      (this as any).visibleInvoice = false;
      (this as any).$refs.dialogApprove.ejsRef.show()
    },

    getEnterpriseDetail(data: any) { 
      (this as any).$set((this as any).approveData, 'rejectReason', data.rejectReason) 
    },

    closeApproveDialog() {
      (this as any).rejectEnterpriseId = undefined;
      (this as any).$refs.dialogApprove.ejsRef.hide()
    },

    approveRejectEnterprise() { 
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      const self: any = this
      self.$api.enterprise.submitEnterpriseRegister({
        enterpriseId: self.rejectEnterpriseId,
        reason: self.approveData.rejectReason
      }).then((res: any) => {
        if (res.code === 200) {
          self.$toast({
            content: res.msg ? res.msg : '操作成功',
            type: 'success'
          })
          this.closeApproveDialog()
          this.changeCreat('3')
        }
      })
    },

    onOpen(args: any) {
      args.preventFocus = true
    },
    // 我创建的-驳回弹窗
    showApproveDetail(num: number) {
      if (num === 1) {
        (this as any).visibleIndustry = !(this as any).visibleIndustry
      } else if (num === 2) {
        (this as any).visibleService = !(this as any).visibleService
      } else if (num === 3) {
        (this as any).visibleBusinessProve = !(this as any).visibleBusinessProve
      } else if (num === 4) {
        (this as any).visibleInvoice = !(this as any).visibleInvoice
      }
    },
    async getImgUrl(id: string) {
      (this as any).$api.file.downloadPublicFile(id)
    },
    // 申请开通
    applyOpen(id: any, name: any) {
      (this as any).isShowPhoneError = false;
      (this as any).enterpriseId = id;
      (this as any).enterpriseName = name;
      (this as any).$refs.dialogApplyOpen.ejsRef.show();
      (this as any).queryDefaultVersion(id)
    },
    // 初始化获取版本信息
    getDropDownList() {
      // 获取版本内容
      const res = (this as any).$api.enterprise.dropDownList()
      res.then((r: any) => {
        if (r.code === 200 && r.data) {
          if (r.data && r.data.length > 0) {
            r.data.forEach((v: any) => {
              v.label = v.name
              v.value = v.id
              //   v.isFree = v.isFree
              v.title = v.name
              v.content = v.id
            });
            (this as any).versionData = r.data; 
            (this as any).versionId = r.data[0].id
          }
        }
      })
    },
    // 获取queryDefaultVersion
    queryDefaultVersion(id: string) { 
      (this as any).$api.enterprise.queryDefaultVersion({
        enterpriseId: id
      }).then((r: any) => {
        if (r.code === 200 && r.data) { 
          (this as any).versionId = r.data
        }
      })
    },

    // 通过版本id获取版本name
    getVersionName(id: any): string {
      let returnValue = '' as string
      (this as any).versionData.map((v: any) => {
        if (v.value === id) {
          returnValue = v.label
        }
      })
      return returnValue
    },
    // 通过版本id获取版本price
    getVersionPrice(id: any): string {
      let returnValue = '' as string
      (this as any).versionData.map((v: any) => {
        if (v.value === id) {
          returnValue = v.isFree.toString()
        }
      })
      return returnValue
    },
    // 查看版本详情
    showVersionDetail() {
      (this as any).$refs.dialogVersion.ejsRef.show()
    },
    // 查看版本，切换tab页签
    async handleVersionSelect(item: number, i: any) {
      (this as any).$set((this as any).fileds, 'dataSource', [])
      const res = (this as any).$api.enterprise.getVersionDetail(i.id)
      res.then((r: any) => {
        if (r.code === 200 && r.data) {
          const list:Array<any> = r.data
          const result:Array<any> = []
          const obj:any = {}
          if (Array.isArray(list) && list.length > 0) {
            // 数组转树
            list.forEach((item: any) => {
              obj[item.permissionId] = item
            })
            list.forEach((item: any) => {
              item.id = item.permissionId
              item.name = item.permissionName
              const parent = obj[item.parentPermissionId]
              if (parent) {
                (parent.subChild || (parent.subChild = [])).push(item)
              } else {
                result.push(item)
              }
            });
            ((this as any) as any).$nextTick().then(() => {
              (this as any).$set((this as any).fileds, 'dataSource', [...result])
            })
          }
        }
      }).catch()
    },

    validPhone() {
      const phone = (this as any).phone

      if (phoneRegex.test(phone)) {
        (this as any).isShowPhoneError = false
      } else {
        (this as any).isShowPhoneError = true
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.body{
    margin: 30px 20px;
    .body-title{
        font-size: 14px;
        font-weight: bold;
        padding-left: 10px;
        border-left: 2px solid #00469C;
    }
    .btn-list{
        margin: 20px 0;
        button{
            color: #9A9A9A;
            background: #F5F6F7;
            padding: 5px 10px;
            font-size: 12px;
            cursor: pointer;
            border: 1px solid #E8E8E8;
        }
        .wait{
            border-radius: 2px 0 0 2px;
        }
        .over{
            border-radius: 0 2px 2px 0;
        }
        .active{
            background: #6386C1;
            color: #FFFFFF;
        }
        .add{
            float: right;
            color: #4F5B6D;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            line-height: 28px;
        }
    }
    .detail-business{
        border: 1px solid #E8E8E8;
        padding: 0 20px 0 10px;
        margin: 10px 0;
        line-height: 54px;
        .left{
            display: inline-block;
            .business-title{
                float: left;
                .circle{
                    display: inline-block;
                    width: 2px;
                    height: 2px;
                    border-radius: 50%;
                    background: #6386C1;
                    vertical-align: middle;
                    margin: 0 10px;
                }
                .circle-reject{
                    background: #9A9A9A;
                }
                .business-type{
                    color: #6386C1;
                }
                .business-type-reject{
                    color: #9A9A9A;
                }
            }
            .business-logo{
                float: left;
                line-height: 0;
                margin: 5px 20px 0 0;
                img{
                  width: 100px;
                  max-height: 40px;
                }
            }
        }
        .right{
            float: right;
            color: #6386C1;
            label{
                margin: 0 10px;
                cursor: pointer;
            }
            .version-name{
                color: #9A9A9A;
            }
        }
    }
}
.diolog{
    padding: 15px;
    .content{
        position: relative;
        margin-bottom: 15px;
        .title{
            font-weight: bold;
            margin-bottom: 10px;
            
        }
        .red{
            color: #FF1F32;
            margin-right: 5px;
        }
        .apply-icon{
            color: #6386C1;
            padding-left: 5px;
            cursor: pointer;
        }
        .phone{
            display: inline-block;
            margin-left: 20px;
            width: calc(100% - 170px);
        }
        .phone--error {
          position: absolute;
          bottom: -15px; 
          left: 170px;
        }
        .pop-version{
            margin: 10px 0 5px 0;
            color: #FF1F32;
        }
    }
    .name{
        display: inline-block;
        width: 200px;
        margin-right: 20px;
    }
}
.approve{
    .table{
        border: 1px solid #E8E8E8;
        margin-bottom: 15px;
        .table-title{
            height: 46px;
            line-height: 46px;
            font-size: 14px;
            font-weight: bold;
            color: #6386C1;
            padding: 0 20px;
            i{
                float: right;
                font-size: 8px;
                margin-top: 17px;
            }
        }
        .table-body{
            padding: 20px;
            border-top: 1px solid #E8E8E8;
            .table-label{
                margin: 0 20px 15px 0;
                label{
                    margin-left: 10px;
                }
                .registerCapital{
                    width: calc(100% - 80px);
                }
                .capitalCurrency{
                    float: right;
                    width: 60px;
                    text-align: center;
                }
                .time{
                    width: calc((100% - 80px)/2);
                    margin-right: 10px;
                }
                .long-term{
                    width: 60px;
                }
                .address{
                    width: calc(15% - 15px);
                    margin-right: 15px;
                }
                .address-detail{
                    width: calc(60% - 45px);
                }
            }
        }
    }
}
.version-detail{
    margin: 10px 0;
}
.orange{
    color: #EDA133;
}
</style>
