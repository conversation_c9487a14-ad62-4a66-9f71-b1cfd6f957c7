<template>
    <div class="invitation">
        <div class="title">
            我的邀请
        </div>
        <div class="content">
            <button class="wait" @click="change('wait')" :class="type === 'wait' ? 'active' : ''">待处理</button>
            <button class="over" @click="change('over')" :class="type === 'over' ? 'active' : ''">已处理</button>

            <div class="body">
                <div class="body-title">员工邀请加入</div>
                <div class="detail-invitation" v-for="item in invitationList" :key="item.id">
                    <div class="left" v-if="type === 'over'">
                        <div class="circle" :class="item.approvalStatus ? '' : 'left-reject-circle'"></div>
                        <label :class="item.approvalStatus ? '' : 'left-reject'">{{item.approvalStatus ? '已接受' : '已拒绝'}}</label>
                        <div class="circle" :class="item.approvalStatus ? '' : 'left-reject-circle'"></div>
                    </div>
                    <div class="invitation-body">
                        <div class="invitation-logo">
                            <img :src="'/api/file/user/file/viewPublicImageFile?id=' + item.logoFileId">
                        </div>
                        <div class="invitation-top">
                            员工编号：{{item.employeeCode}}
                            <div class="stationName" v-if="item.stationName">{{item.stationName}}</div>
                            <div class="departmentName" v-if="item.departmentName">{{item.departmentName}}</div>
                        </div>
                        <div class="invitation-detail">
                            <div>
                                <mt-icon name="icon_solid_Progress1"></mt-icon>
                                系统角色：{{item.rolesName}}
                            </div>
                            <div>
                                <mt-icon name="icon_solid_Rank1"></mt-icon>
                                公司：{{item.companyName}}
                            </div>
                            <div>
                                <mt-icon name="icon_outline_Radio"></mt-icon>
                                信息来源：{{item.entityName}}
                            </div>
                        </div>
                    </div>
                    <div class="right" v-if="type === 'wait'">
                        <label @click="operationClick('accept', item.employeeId)">接受</label>
                        <label @click="operationClick('refuse', item.employeeId)">拒绝</label>
                    </div>
                </div>
            </div>
        </div>

        <!-- 接受/拒绝弹窗 -->
        <mt-dialog 
            ref="dialogOperation" 
            css-class="dialog-form-flex" 
            header="提示" 
            width="500px"
            height="300px"
            :buttons="buttons" 
            :open="onOpen" >
            <div class="dialog-msg">
                是否确认<span>{{operationType === 'accept' ? '接受' : '拒绝' }}</span>该邀请！
            </div>
        </mt-dialog>
    </div>
</template>

<script lang="ts">
export default {
  data() {
    return {
      // 当前操作行id
      rowId: '',
      type: 'wait',
      invitationList: [],
      operationType: '',
      // 接受拒绝按钮
      buttons: [
        {
          click: () => {
            (this as any).rowId = '' as any
            (this as any).$refs.dialogOperation.ejsRef.hide()
          },
          buttonModel: { content: '取消' }
        },
        {
          click: () => {
            (this as any).operation()
          },
          buttonModel: { isPrimary: 'true', content: '确认' }
        }
      ]
    }
  },
  mounted() {
    (this as any).change('wait')
  },
  methods: {
    async change(type:string) {
      (this as any).type = type;
      (this as any).invitationList = []
      let res
      if (type === 'wait') {
        res = (this as any).$api.enterprise.companyInviteWait()
      } else if (type === 'over') {
        res = (this as any).$api.enterprise.companyInviteOver()
      }
      res.then((r: any) => {
        if (r.code === 200 && r.data) {
          (this as any).invitationList = r.data
        }
      })
    },
    // 接受/拒绝弹窗
    operationClick(type: any, id: any) {
      (this as any).operationType = type;
      (this as any).rowId = id;
      (this as any).$refs.dialogOperation.ejsRef.show()
    },
    async operation() {
      const params = {
        id: (this as any).rowId,
        approvalStatus: (this as any).operationType === 'accept' ? 1 : 0
      }
      const res = (this as any).$api.enterprise.companyOperation(params)
      res.then((r: any) => {
        if (r.code === 200 && r.data) {
          (this as any).$refs.dialogOperation.ejsRef.hide()
        }
      })
    },
    onOpen(args: any) {
      args.preventFocus = true
    }
  }
}
</script>

<style lang="scss" scoped>
.invitation{
    .title{
        font-size: 20px;
        font-weight: bold;
        height: 60px;
        line-height: 60px;
        padding: 0 20px;
        border-bottom: 2px solid #E8E8E8;
    }
    .content{
        padding: 34px 20px;
        button{
            color: #9A9A9A;
            background: #F5F6F7;
            padding: 5px 10px;
            font-size: 12px;
            cursor: pointer;
            border: 1px solid #E8E8E8;
        }
        .wait{
            border-radius: 2px 0 0 2px;
        }
        .over{
            border-radius: 0 2px 2px 0;
        }
        .active{
            background: #6386C1;
            color: #FFFFFF;
        }
        .body{
            margin: 30px 20px;
            .body-title{
                font-size: 14px;
                font-weight: bold;
                padding-left: 10px;
                border-left: 2px solid #00469C;
            }
            .detail-invitation{
                border: 1px solid #E8E8E8;
                padding: 0 20px 0 20px;
                margin: 10px 0;
                line-height: 120px;
                border-radius: 4px;
                .left{
                    display: inline-block;
                    .circle{
                        width: 2px;
                        height: 2px;
                        border-radius: 50%;
                        background: #6386C1;
                        display: inline-block;
                        vertical-align: middle;
                        margin: 0 10px;
                    }
                    label{
                        color: #6386C1;
                    }
                    .left-reject{
                        color: #9A9A9A;
                        
                    }
                    .left-reject-circle{
                        background: #9A9A9A;
                    }
                }
                .invitation-body{
                    line-height: 36px;
                    vertical-align: middle;
                    display: inline-block;
                    width: calc(100% - 120px);
                    .invitation-logo{
                        float: left;
                        margin: 10px 20px 0 0;
                        img{
                            min-height: 80px;
                        }
                    }
                    .invitation-top{
                        font-weight: bold;
                        div{
                            display: inline-block;
                            margin: 0 10px;
                        }
                        .stationName{
                            padding: 0 5px;
                            border-radius: 2px;
                            font-weight: normal;
                            background: rgba(237,161,51,0.1);
                            color: #EDA133;
                            font-size: 12px;
                            height: 18px;
                            line-height: 18px;
                            display: inline-block;
                        }
                        .departmentName{
                            padding: 0 5px;
                            border-radius: 2px;
                            font-weight: normal;
                            background: rgba(99,134,193,0.1);
                            color: #6386C1;
                            font-size: 12px;
                            height: 18px;
                            line-height: 18px;
                            display: inline-block;
                        }
                    }
                    .invitation-detail{
                        div{
                            display: inline-block;
                            margin-right: 100px;
                        }
                        i{
                            color: #B0C1DF;
                            font-size: 12px;
                        }
                    }
                }
            }
            .right{
                float: right;
                color: #6386C1;
                label{
                    margin: 0 10px;
                    cursor: pointer;
                }
            }
            .detail-invitation:hover{
                border-left: 6px solid #6386C1;
            }
        }
    }
}
.dialog-msg{
    text-align: center;
    font-size: 16px;
    vertical-align: middle;
    margin-top: 80px;
}
</style>
