<template>
  <div class="wrapper">  
    <CreateEnterpriseHeader v-show="currentStep === 0" @next="nextStep" @prev="prevStep" />
    <CreateEnterpriseMain v-show="currentStep === 1" :firstData="formData" :enterpriseId="formData.enterpriseId" @next="finishCreate" @prev="prevStep" />
  </div>
</template>

<script lang="ts">
import { Component, Vue } from '@mtech/vue-property-decorator' 
import CreateEnterpriseHeader from './createEnterpriseHeader/index.vue'
import CreateEnterpriseMain from './createEnterprise/index.vue'

@Component({
  components: {
    CreateEnterpriseHeader,
    CreateEnterpriseMain
  } 
})

export default class CreateNewEnterprise extends Vue {     
  currentStep = 0
  formData: any = {}

  nextStep (data: any) {
    if (this.currentStep === 0) {
      this.formData = data
    }
    
    this.currentStep++
  }

  prevStep () {
    if (this.currentStep === 0) {
      this.$emit('close')
      return
    }
    this.currentStep--
  }

  finishCreate() {
    this.$emit('close') 
  }
}
</script>

<style lang="scss" scoped>
  
</style>
