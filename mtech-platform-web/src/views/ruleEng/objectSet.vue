<template>
  <div>
    <mt-template-page
      :hiddenTabs="true"
      ref="mainTemplatePage"
      :templateConfig="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>
    <mt-dialog ref="addDialog" header="新增对象" :buttons="buttons" css-class="dialog-form-flex">
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="name" label="对象名称">
          <mt-input v-model="ruleForm.name" :show-clear-button="true" placeholder="对象名称"></mt-input>
        </mt-form-item>
        <mt-form-item prop="project" label="所属项目">
          <mt-select
            v-model="ruleForm.project"
            :data-source="projectSelect"
            :show-clear-button="true"
            placeholder="所属项目"
            :allow-filtering="true"
            :fields="{ text: 'projectName', value: 'id' }"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="modelDesc" label="对象描述">
          <mt-input v-model="ruleForm.modelDesc" :show-clear-button="true" placeholder="对象描述"></mt-input>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import { objectSetPageConfig } from './config'
export default {
  data() {
    return {
      projectSelect: [],
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: '取消' }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: '确定' }
        }
      ],
      ruleForm: {
        project: '',
        name: '',
        modelDesc: ''
      },
      rules: {
        project: [{ required: true, message: '请选择项目', trigger: 'blur' }],
        name: [{ required: true, message: '请填写对象名称', trigger: 'blur' }],
        modelDesc: [{ required: true, message: '请填写对象描述', trigger: 'blur' }]
      },
      params: { current: 1, size: 10 },
      pageConfig: objectSetPageConfig
    }
  },
  mounted() {
    this.getProjectList()
  },
  methods: {
    async getProjectList() {
      await this.$api.ruleEng.getProjectList().then((r) => {
        this.projectSelect = [...r.data]
      })
    },
    hide() {
      this.ruleForm = {
        project: '',
        name: '',
        modelDesc: ''
      }
      this.$refs.addDialog.ejsRef.hide()
    },
    save() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const params = {
            modelName: this.ruleForm.name,
            projectId: this.ruleForm.project,
            modelDesc: this.ruleForm.modelDesc
          }
          this.$api.ruleEng.addRuleModelList(params).then((r) => {
            this.refreshMain()
            this.$refs.addDialog.ejsRef.hide()
          })
        } else {
          return false
        }
      })
    },
    // 刷新main列表
    refreshMain() {
      this.$refs.mainTemplatePage.refreshCurrentGridData()
    },
    handleClickToolBar(e) {
      // 表格顶部 toolbar
      console.log(e)
      if (e.tabIndex === 0) {
        if (e.toolbar.id === 'Add') {
          this.$refs.addDialog.ejsRef.show()
        } else if (e.toolbar.id === 'Delete') {
          console.log(e.grid.getSelectedRecords())
          const arr = [...e.grid.getSelectedRecords()]
          if (arr.length === 0) {
            this.$toast({
              content: '请至少选择一条数据进行操作！',
              type: 'warning'
            })
          } else {
            const p = []
            arr.forEach((item) => {
              p.push(item.id)
            })
            this.deleteRuleModelList(p)
          }
        } else if (e.toolbar.id === 'Refresh') {
          this.refreshMain()
        }
      }
    },
    // 删除对象方法
    deleteRuleModelList(arr) {
      this.$dialog({
        data: {
          title: '删除',
          message: '是否确认删除？'
        },
        success: () => {
          this.$api.ruleEng.deleteRuleModelList({ ids: arr }).then((r) => {
            this.refreshMain()
          })
        }
      })
    },
    handleClickCellTool(a) {
      // 单元格 图标点击 tool
      console.log('use-handleClickCellTool', a)
      if (a.tool.id === 'edit') {
        const { id, modelName, createTime, updateTime } = a.data
        this.$router.push({ path: `/platform/ruleEng/objectDetails/${id}/${modelName}/${createTime}/${updateTime}` })
        // this.$router.push({
        //   path: 'objectDetails',
        //   params: a.data
        // })
      } else if (a.tool.id === 'delete') {
        this.deleteRuleModelList([a.data.id])
      }
    },
    handleClickCellTitle(a) {
      console.log('use-handleClickCellTitle', a)
      const { id, modelName, createTime, updateTime } = a.data
      this.$router.push({ path: `/platform/ruleEng/objectDetails/${id}/${modelName}/${createTime}/${updateTime}` })
      
      // this.$router.push({
      //   path: 'objectDetails',
      //   params: a.data
      // })
    }
  }
}
</script>
