<template>
  <div>
    <mt-template-page
      ref="mainTemplatePage"
      :templateConfig="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    >
    </mt-template-page>
    <mt-dialog ref="addDialog" :header="titleName" :buttons="buttons" css-class="dialog-form-flex">
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="project" label="所属项目">
          <mt-select
            v-model="ruleForm.project"
            :data-source="projectSelect"
            :show-clear-button="true"
            placeholder="所属项目"
            :allow-filtering="true"
            :fields="{ text: 'projectName', value: 'id' }"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="method" label="方法名">
          <mt-select
            v-model="ruleForm.method"
            :allow-filtering="true"
            :data-source="methodSelect"
            :show-clear-button="true"
            placeholder="方法名"
            :fields="{ text: 'methodName', value: 'id' }"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="rule" label="规则">
          <mt-select
            v-model="ruleForm.rule"
            :allow-filtering="true"
            :data-source="ruleSelect"
            :show-clear-button="true"
            placeholder="规则"
            :fields="{ text: 'ruleName', value: 'id' }"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      pageConfig,
      titleName: '增加',
      projectSelect: [],
      methodSelect: [],
      ruleSelect: [],
      getBindListParams: {
        projectId: '',
        methodId: '',
        ruleId: '',
        current: 1,
        size: 10
      },
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: '取消' }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: '确定' }
        }
      ],
      ruleForm: {
        project: '',
        method: '',
        rule: ''
      },
      rules: {
        project: [{ required: true, message: '请选择项目', trigger: 'blur' }],
        method: [{ required: true, message: '请选择方法', trigger: 'blur' }],
        rule: [{ required: true, message: '请选择规则', trigger: 'blur' }]
      }
    }
  },
  computed: {
    userInfo() {
      return this.$store.state.user.userInfo
    }
  },
  mounted() {
    // this.getRuleList()
    // this.refreshMain()
    this.getAllRule()
    this.getProjectList()
    this.getMethodList()
  },
  methods: {
    async getAllRule() {
      await this.$api.ruleEng.getAllRule().then((r) => {
        this.ruleSelect = [...r.data]
      })
    },
    // 刷新main列表
    refreshMain() {
      const func = this.$refs.mainTemplatePage
      func.refreshCurrentGridData()
    },
    hide() {
      this.ruleForm = {
        project: '',
        method: '',
        rule: ''
      }
      this.$refs.addDialog.ejsRef.hide()
    },
    save() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const params = {
            methodId: this.ruleForm.method,
            projectId: this.ruleForm.project,
            ruleId: this.ruleForm.rule
          }
          if (this.titleName === '增加') {
            this.$api.ruleEng.addRuleBind(params).then((r) => {
              this.refreshMain()
              this.hide()
            })
          } else {
            params.id = this.ruleForm.id
            this.$api.ruleEng.updataRuleBind(params).then((r) => {
              this.refreshMain()
              this.hide()
            })
          }
        } else {
          return false
        }
      })
    },
    async getProjectList() {
      await this.$api.ruleEng.getProjectList().then((r) => {
        this.projectSelect = [...r.data]
      })
    },
    async getMethodList() {
      await this.$api.ruleEng.getMethodList().then((r) => {
        this.methodSelect = [...r.data]
      })
    },
    // 表格顶部 toolbar
    handleClickToolBar(e) {
      console.log('use-handleClickToolBar', e)
      if (e.tabIndex === 0) {
        if (e.toolbar.id === 'Add') {
          // this.$refs.dialog.ejsRef.show()
          this.$router.push({
            name: 'editRule',
            params: { id: 1 }
          })
        } else if (e.toolbar.id === 'delete') {
          console.log(e.grid.getSelectedRecords())
          const arr = [...e.grid.getSelectedRecords()]
          if (arr.length === 0) {
            this.$toast({
              content: '请至少选择一条数据进行操作！',
              type: 'warning'
            })
          } else {
            const p = []
            arr.forEach((item) => {
              p.push(item.id)
            })
            this.deleteRule(p)
          }
        }
      } else if (e.tabIndex === 1) {
        console.log(e.grid.getSelectedRecords())
        if (e.toolbar.id === 'Add') {
          this.titleName = '增加'
          this.$refs.addDialog.ejsRef.show()
        } else if (e.toolbar.id === 'Delete') {
          const arr = [...e.grid.getSelectedRecords()]
          if (arr.length === 0) {
            this.$toast({
              content: '请至少选择一条数据进行操作！',
              type: 'warning'
            })
          } else {
            const p = []
            arr.forEach((item) => {
              p.push(item.id)
            })
            this.deleteRuleBind(p)
          }
        } else if (e.toolbar.id === 'Edit') {
          const arr = [...e.grid.getSelectedRecords()]
          if (arr.length === 1) {
            this.ruleForm.project = arr[0].projectId
            this.ruleForm.method = arr[0].methodId
            this.ruleForm.rule = arr[0].ruleId
            this.ruleForm.id = arr[0].id
            this.titleName = '编辑'
            this.$refs.addDialog.ejsRef.show()
          } else {
            this.$toast({
              content: '请至少选择一条数据进行操作！',
              type: 'warning'
            })
          }
        }
      }
    },
    // 单元格 图标点击 tool
    handleClickCellTool(a) {
      console.log('use-handleClickCellTool', a)
      if (a.tool.id === 'edit') {
        this.$router.push({
          name: 'editRule',
          params: a.data
        })
      } else if (a.tool.id === 'delete') {
        this.deleteRule([a.data.id])
      }
    },
    // 删除规则方法
    deleteRule(arr) {
      this.$dialog({
        data: {
          title: '删除',
          message: '是否确认删除？'
        },
        success: () => {
          this.$api.ruleEng.deleteRule({ ids: arr }).then((r) => {
            this.refreshMain()
          })
        }
      })
    },
    // 删除绑定关系
    deleteRuleBind(arr) {
      this.$dialog({
        data: {
          title: '删除',
          message: '是否确认删除？'
        },
        success: () => {
          this.$api.ruleEng.deleteRuleBind({ ids: arr }).then((r) => {
            this.refreshMain()
          })
        }
      })
    }
  }
}
</script>
