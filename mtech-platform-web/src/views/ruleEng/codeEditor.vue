<template>
  <Codemirror
    ref="myCm"
    :value="editorValue"
    :options="cmOptions"
    @changes="onCmCodeChanges"
    @blur="onCmBlur"
    @keydown.native="onKeyDown"
    @mousedown.native="onMouseDown"
    @paste.native="OnPaste"
  ></Codemirror>
</template>

<script>
import './codeEditorJs'
/* eslint-disable */
export default {
  props: ['cmTheme', 'cmMode', 'autoFormatJson', 'jsonIndentation'],
  // props: {
  //   cmTheme: {
  //     type: String
  //   },
  //   cmMode: {
  //     type: String
  //   },
  //   autoFormatJson: {
  //     type: Boolean
  //   },
  //   jsonIndentation: {
  //     type: Number
  //   }
  // },
  data() {
    return {
      editorValue: '',
      cmOptions: {
        theme: !this.cmTheme || this.cmTheme == 'default' ? 'blackboard' : this.cmTheme,
        mode: !this.cmMode || this.cmMode == 'default' ? 'application/json' : this.cmMode,
        lineWrapping: true,
        lineNumbers: true,
        autofocus: true,
        smartIndent: false,
        autocorrect: true,
        spellcheck: true,
        extraKeys: {
          'Tab': 'autocomplete',

          'Ctrl-Alt-L': () => {
            try {
              if (this.cmOptions.mode == 'application/json' && this.editorValue) {
                this.editorValue = this.formatStrInJson(this.editorValue)
              }
            } catch (e) {
              alert('格式化代码出错：' + e.toString())
            }
          }
        },

        lint: true,
        gutters: ['CodeMirror-lint-markers', 'CodeMirror-linenumbers', 'CodeMirror-foldgutter'],
        foldGutter: true,
        autoCloseBrackets: true,
        autoCloseTags: true,
        matchTags: { bothTags: true },
        matchBrackets: true,
        styleActiveLine: true,
        autoRefresh: true,
        highlightSelectionMatches: {
          minChars: 2,
          style: 'matchhighlight',
          showToken: true
        },
        styleSelectedText: true,
        enableAutoFormatJson: this.autoFormatJson == null ? true : this.autoFormatJson,
        defaultJsonIndentation: !this.jsonIndentation || typeof this.jsonIndentation != typeof 1 ? 2 : this.jsonIndentation
      },
      enableAutoFormatJson: this.autoFormatJson == null ? true : this.autoFormatJson,
      defaultJsonIndentation: !this.jsonIndentation || typeof this.jsonIndentation != typeof 1 ? 2 : this.jsonIndentation
    }
  },

  watch: {
    cmTheme() {
      try {
        let theme = this.cmTheme == 'default' ? 'blackboard' : this.cmTheme
        require('codemirror/theme/' + theme + '.css')
        this.cmOptions.theme = theme
        this.resetLint()
      } catch (e) {
        alert('切换编辑器主题出错：' + e.toString())
      }
    },
    cmMode() {
      this.$set(this.cmOptions, 'mode', this.cmMode)
      this.resetLint()
      this.resetFoldGutter()
    }
  },

  created() {
    try {
      if (!this.editorValue) {
        this.cmOptions.lint = false
        return
      }

      if (this.cmOptions.mode == 'application/json') {
        if (!this.enableAutoFormatJson) {
          return
        }
        this.editorValue = this.formatStrInJson(this.editorValue)
      }
    } catch (e) {
      // console.log('初始化codemirror出错：' + e)
      // alert("初始化codemirror出错：" + e);
    }
  },
  methods: {
    resetLint() {
      if (!this.$refs.myCm.codemirror.getValue()) {
        this.$nextTick(() => {
          this.$refs.myCm.codemirror.setOption('lint', false)
        })
        return
      }
      this.$refs.myCm.codemirror.setOption('lint', false)
      this.$nextTick(() => {
        this.$refs.myCm.codemirror.setOption('lint', true)
      })
    },
    resetFoldGutter() {
      this.$refs.myCm.codemirror.setOption('foldGutter', false)
      this.$nextTick(() => {
        this.$refs.myCm.codemirror.setOption('foldGutter', true)
      })
    },

    // 修改编辑框样式
    setStyle(style) {
      try {
        this.$nextTick(() => {
          let cm = this.$refs.myCm.$el.querySelector('.CodeMirror')
          if (cm) {
            cm.style.cssText = style
          } else {
            alert('未找到编辑器元素，修改编辑器样式失败')
          }
        })
      } catch (e) {
        alert('修改编辑器样式失败：' + e.toString())
      }
    },
    // 获取值
    getValue() {
      try {
        return this.$refs.myCm.codemirror.getValue()
      } catch (e) {
        let errorInfo = e.toString()
        alert('获取编辑框内容失败：' + errorInfo)
        return errorInfo
      }
    },
    // 修改值
    setValue(value) {
      try {
        if (typeof value != typeof '') {
          alert('修改编辑框内容失败：编辑宽内容只能为字符串')
          return
        }
        if (this.cmOptions.mode == 'application/json') {
          this.editorValue = this.formatStrInJson(value)
        } else {
          this.editorValue = value
        }
      } catch (e) {
        alert('修改编辑框内容失败：' + e.toString())
      }
    },
    // 在光标位置插入内容
    setLine(val) {
      try {
        let pos1 = this.$refs.myCm.codemirror.getCursor()
        let pos2 = {}
        pos2.line = pos1.line
        pos2.ch = pos1.ch
        this.$refs.myCm.codemirror.replaceRange(val, pos2)
      } catch (e) {
        alert('修改编辑框内容失败：' + e.toString())
      }
    },
    // 黏贴事件处理函数
    OnPaste() {
      if (this.cmOptions.mode == 'application/json') {
        try {
          this.editorValue = this.formatStrInJson(this.editorValue)
        } catch (e) {
          // 啥都不做
        }
      }
    },
    // 失去焦点时处理函数
    onCmBlur(cm, event) {
      try {
        let editorValue = cm.getValue()
        if (this.cmOptions.mode == 'application/json' && editorValue) {
          if (!this.enableAutoFormatJson) {
            return
          }
          this.editorValue = this.formatStrInJson(editorValue)
        }
      } catch (e) {
        // 啥也不做
      }
    },
    // 按下键盘事件处理函数
    onKeyDown(event) {
      const keyCode = event.keyCode || event.which || event.charCode
      const keyCombination = event.ctrlKey || event.altKey || event.metaKey
      if (!keyCombination && keyCode > 64 && keyCode < 123) {
        this.$refs.myCm.codemirror.showHint({ completeSingle: false })
      }
    },
    // 按下鼠标时事件处理函数
    onMouseDown() {
      this.$refs.myCm.codemirror.closeHint()
    },
    onCmCodeChanges(cm) {
      this.editorValue = cm.getValue()
      this.resetLint()
    },
    // 格式化字符串为json格式字符串
    formatStrInJson(strValue) {
      return JSON.stringify(JSON.parse(strValue), null, this.defaultJsonIndentation)
    }
  }
}
</script>
<style>
.CodeMirror-selected {
  background-color: blue !important;
}

.CodeMirror-selectedtext {
  color: white !important;
}

.cm-matchhighlight {
  background-color: #ccc;
}
</style>
