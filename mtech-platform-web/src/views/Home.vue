<template>
  <div class="home">  
    <!-- <layout v-if="isRouterAlive" /> -->
    <router-view  v-if="isRouterAlive"></router-view>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from '@mtech/vue-property-decorator'  
import Layout from '@/components/layout/layout.vue'

@Component({
  components: { 
    Layout
  }
})
export default class Home extends Vue {
  isRouterAlive = true
  created() { 
  }
}
</script>

<style>
.home {
  width: 100%;
  height: 100%;
}
</style>
