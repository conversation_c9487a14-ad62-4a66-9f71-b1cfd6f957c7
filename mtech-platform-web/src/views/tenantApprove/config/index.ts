import ColumnTimeTemplate from '../../../components/ColumnTimeTemplate.vue'

export const tenantPageConfig = [
  {  
    title: '待处理',
    useToolTemplate: false,
    toolbar: [  
    ],
    grid: {
      columnData: [{
        width: '40',
        type: 'checkbox'
      },    
      {
        field: 'createTime',
        headerText: '申请时间',
        template() {
          return {
            template: ColumnTimeTemplate
          }
        }
      },
      {
        field: 'enterpriseName', 
        headerText: '企业名称' 
      },  
      {
        field: 'enterpriseIdentityTypeName',
        headerText: '企业身份代码'
      },
      {
        field: 'enterpriseIdentityCode',
        headerText: '企业代码'
      },
      {
        field: 'versionName',
        headerText: '租户版本'
      },
      
      {
        field: 'approvalStatus',
        headerText: '状态',
        valueConverter: {
          type: 'map',
          fields: { text: 'label', value: 'status' },
          map: [
            { status: -1, label: '待审核', cssClass: ['status', 'status-unapproval'] },
            { status: 1, label: '审核通过', cssClass: ['status', 'status-success'] },
            { status: 0, label: '审核未通过', cssClass: ['status', 'status-failed'] }
          ]
        },
        cellTools: [ 
          {
            id: 'Approval',
            icon: 'icon_solid_Submit',
            title: '审核',
            visibleCondition: (data: { approvalStatus: number }) => { 
              return data.approvalStatus === -1
            } 
          } 
        ]
      }],
      dataSource: [],
      asyncConfig: {
        url: '/platform/admin/applyVersion/query',
        methods: 'post',
        defaultRules: [ 
          {
            condition: 'and',
            field: 'approvalStatus',
            operator: 'equal',
            type: 'int',
            value: -1  
          }
        ]
      }
    }
  }, 
  {
    title: '已处理',
    useToolTemplate: false,
    toolbar: [  
    ],
    grid: {
      columnData: [
        {
          width: '40',
          type: 'checkbox'
        },
        {
          field: 'createTime',
          headerText: '申请时间',
          template() {
            return {
              template: ColumnTimeTemplate
            }
          }
        },
        {
          field: 'enterpriseName', 
          headerText: '企业名称' 
        },  
        {
          field: 'enterpriseIdentityTypeName',
          headerText: '企业身份代码'
        },
        {
          field: 'enterpriseIdentityCode',
          headerText: '企业代码'
        },
        {
          field: 'versionName',
          headerText: '申请版本'
        },

        {
          field: 'approvalUsername',
          headerText: '审核人'
        },

        {
          field: 'approvalTime',
          headerText: '审核时间',
          template() {
            return {
              template: ColumnTimeTemplate
            }
          }
        },
        
        {
          field: 'approvalStatus',
          headerText: '状态',
          valueConverter: {
            type: 'map',
            fields: { text: 'label', value: 'status' },
            map: [
              { status: -1, label: '待审核', cssClass: ['status', 'status-unapproval'] },
              { status: 1, label: '审核通过', cssClass: ['status', 'status-success'] },
              { status: 0, label: '审核未通过', cssClass: ['status', 'status-failed'] }
            ]
          },
          cellTools: [  
            {
              id: 'View',
              icon: 'icon_solid_Submit',
              title: '查看' 
            }
          ]
        },

        {
          field: 'approvalReason',
          headerText: '审核意见'
        }
      ],
      dataSource: [],
      asyncConfig: {
        url: '/platform/admin/applyVersion/query',
        methods: 'post',
        defaultRules: [ 
          {
            condition: 'and',
            field: 'approvalStatus',
            operator: 'notequal',
            type: 'int',
            value: -1  
          }
        ]
      }
    }
  }
]
