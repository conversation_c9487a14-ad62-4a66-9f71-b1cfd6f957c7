<template> 
  <TreeTransferLayout 
    v-model="visible" 
    :id="formData.versionId" 
    :sourceField="sourceTreeField"
    :targentField ="treeField"
    :sourceDataList="sourceTreeDataSource"
    :targetDataList="targetTreeDataSource"
    :isApprove="!isView"
    :readonly="isView"
    @save="handleConfirm"
    @reject="handleReject"
   >
    <mt-form ref="form" class="content" :model="formData" :rules="formRules" > 
      <mt-row :gutter="50" >
        <mt-col :span="12">
          <mt-form-item label="企业名称" prop="enterpriseName" >
            <mt-input 
              v-model="formData.enterpriseName"  
              :readonly="true"
              :showClearButton="false"
              placeholder="企业名称" />
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item label="企业性质" prop="enterpriseTypeName" >
            <mt-input 
              v-model="formData.enterpriseTypeName"  
              :readonly="true"
              :showClearButton="false"
              placeholder="企业性质" />
          </mt-form-item>          
        </mt-col>
      </mt-row>

      <mt-row :gutter="50" >
        <mt-col :span="12">
          <mt-form-item label="类型" prop="businessType" >
            <mt-input 
              v-model="formData.businessTypeName"  
              :readonly="true"
              :showClearButton="false"
              placeholder="类型" />
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item label="租户编码" prop="tenantCode" >
            <mt-input 
              v-model="formData.tenantCode"   
              :readonly="isView"
              :showClearButton="false"
              placeholder="租户编码" />
          </mt-form-item>  
        </mt-col>
      </mt-row>

      <mt-row :gutter="50" >
        <mt-col :span="12">
          <mt-form-item label="开通时间" prop="range" >
            <mt-date-range-picker  
              v-model="formData.range" 
              separator="至" 
              placeholder="选择开通时间"
              :readonly="isView"
              :showClearButton="false"
              ></mt-date-range-picker> 
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item label="选择版本" prop="versionId" >
            <mt-select 
              v-model="formData.versionId"
              :dataSource="selectDataSource" 
              :readonly="isView"
              :showClearButton="false"
              placeholder="请选择版本"
              @change="changeVersion"
            ></mt-select>
          </mt-form-item>          
        </mt-col>
      </mt-row>

      <mt-row :gutter="50" > 
        <!-- <mt-col :span="12">
          <mt-form-item label="独立账户" prop="singleAccountFlag" >
            <mt-radio 
              v-model="formData.singleAccountFlag" 
              :readonly="isView"
              :dataSource="radioDataSource" ></mt-radio>
          </mt-form-item>
        </mt-col> -->
        <mt-col :span="12">
          <mt-form-item label="申请人姓名" prop="name" >
            <mt-input 
              v-model="formData.name"  
              :readonly="true"
              :showClearButton="false"
              placeholder="申请人姓名" /> 
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item label="申请人手机" prop="phone" >
            <mt-input 
              v-model="formData.phone"  
              :readonly="true"
              :showClearButton="false"
              placeholder="申请人手机" /> 
          </mt-form-item>
        </mt-col>
      </mt-row> 

      <mt-row :gutter="50" >         
        <mt-col :span="12">
          <mt-form-item label="申请备注" prop="remark" >
            <mt-input 
              v-model="formData.remark"  
              :readonly="true"
              :showClearButton="false"
              placeholder="申请备注" />  
          </mt-form-item>
        </mt-col>
      </mt-row> 

    </mt-form> 
  </TreeTransferLayout>
</template>

<script lang="ts">
import { Component, Vue, Prop } from '@mtech/vue-property-decorator'  
import TreeTransferLayout from '@/components/TreeTransferLayout.vue'   
import dayjs from 'dayjs'

@Component({
  components: { 
    TreeTransferLayout
  }
})
export default class TenantApproveDialog extends Vue {
  @Prop()
  value !: boolean 

  @Prop() 
  id !: string

  @Prop()
  isView !: boolean

  formData:any = {}

  formRules = {
    range: [
      { required: true, validator: this.validateRange, trigger: 'blur' }
    ],
    // singleAccountFlag: [
    //   { required: true, message: '请选择是否独立版本', trigger: 'blur' }
    // ],
    tenantCode: [
      { required: true, message: '请输入租户编码', trigger: 'blur' }
    ]
  }

  radioDataSource = [
    { label: '是', value: '1' },
    { label: '否', value: '0' }
  ]

  selectDataSource: any[] = [] 

  treeField = {
    id: 'permissionId',
    text: 'permissionName',
    parentID: 'parentPermissionId',  
    hasChildren: 'hasChildren'
  }

  sourceTreeField = {
    id: 'permissionId',
    text: 'permissionName',
    parentID: 'parentPermissionId',  
    hasChildren: 'hasChildren',
    applicationId: 'applicationId',
    applicationName: 'applicationName',
    permissionType: 'permissionType'
  }

  sourceTreeDataSource:any[] = []

  targetTreeDataSource:any[] = []
  currApplication: any = {}

  get visible() {
    if (this.value) {
      this.getApplyDetail(this.id, this.isView)
      this.getVersionList() 
    }
    return this.value
  }

  set visible(v: boolean) {
    this.$emit('input', v)
  } 

  changeVersion(event: any) {  
    this.getVersionPermission(event.value)  
  } 

  validateRange(rule:any, value:any, callback:any) { 
    const { range } = this.formData
    if (range && range[0] && range[1]) {
      callback()
    } else {
      callback(new Error('请选择开通时间'))
    } 
  }

  handleConfirm(auth: any[]) {
    if (this.isView) {
      this.visible = false
    } else {
      const approvalStatus = 1
      this.saveAuth(auth, approvalStatus)
    }
  }
  
  handleReject(auth: any[]) {
    const approvalStatus = 0
    this.saveAuth(auth, approvalStatus)
  }

  private saveAuth(auth: any[], approvalStatus: number) {
    if (approvalStatus === 0) {
      this.adminApplyVersionReject(auth, approvalStatus)
    } else {
      if (!auth || !auth.length) {
        this.$toast({
          content: '请先选择需要分配的权限',
          type: 'warning'
        }) 
        return
      }     
      const form:any = this.$refs.form
      form.validate((valid: boolean) => {
        if (valid) {  
          this.adminApplyVersionApproval(auth, approvalStatus)
        } 
      })
    }
  }

  private adminApplyVersionApproval(auth: any[], approvalStatus: number) {
    this.$api.tenant.adminApplyVersionApproval(this.formatParam(auth, approvalStatus)).then((res: { code: number; msg: any }) => {
      if (res.code === 200) {
        this.$toast({
          content: res.msg || '权限分配成功',
          type: 'success'
        }) 
        this.visible = false
        this.$emit('refresh')
      }
    }).catch((error: any) => {
      this.$toast({
        content: error.msg || '权限分配失败',
        type: 'error'
      }) 
    })
  }

  private adminApplyVersionReject(auth: any[], approvalStatus: number) {
    this.$api.tenant.adminApplyVersionReject(this.formatParam(auth, approvalStatus)).then((res: { code: number; msg: any }) => {
      if (res.code === 200) {
        this.$toast({
          content: res.msg || '已驳回',
          type: 'success'
        }) 
        this.visible = false
        this.$emit('refresh')
      }
    }).catch((error: any) => {
      this.$toast({
        content: error.msg || '驳回失败',
        type: 'error'
      }) 
    })
  }

  private formatParam(versions: any[], approvalStatus: number) {
    const params = { 
      // singleAccountFlag: Number(this.formData.singleAccountFlag),
      tenantCode: this.formData.tenantCode || undefined, 
      versionId: this.formData.versionId,
      validStartDate: this.formData.range[0] ? dayjs(this.formData.range[0]).format('YYYY-MM-DD') : undefined,
      validEndDate: this.formData.range[0] ? dayjs(this.formData.range[1]).format('YYYY-MM-DD') : undefined,
      selectedVersionList: versions,
      id: this.id,
      approvalStatus
    } 
    
    return params
  }

  // 获取详情
  private getApplyDetail(id: string, isView: boolean) {
    const api = this.$api.tenant.showDetailForHistory 

    // if (isView) {
    //   api = this.$api.tenant.showDetailForHistory 
    // } else {
    //   api = this.$api.tenant.showDetailForReview
    // }

    api(id).then((res: any) => {
      if (res.code === 200) {
        const businessType = res.data.businessType
        this.formData = Object.assign(
          {}, 
          res.data,
          {
            businessTypeName: businessType === 1 ? '供应商' : businessType === 2 ? '采购商' : businessType === 3 ? '供方，采方' : '',
            // singleAccountFlag: res.data.singleAccountFlag ? res.data.singleAccountFlag + '' : undefined,
            range: [res.data.validStartDate, res.data.validEndDate]  
          }
        )
        this.targetTreeDataSource = res.data.permissions || [] 
      }
    })
  }
 
  private getVersionList() {
    this.$api.tenant.versionDropdownList().then((res: any) => {
      if (res.code === 200) {
        this.selectDataSource = res.data?.map((v: { name: any; id: any }) => {
          return {
            text: v.name,
            value: v.id
          }
        })
      }
    })
  } 

  // 获取该版本下的所有权限
  private getVersionPermission(id: string) {
    this.$api.platmenu.versionPermissionFind({
      id
    }).then((res: { code: number; data: any[] }) => {
      if (res.code === 200) {
        this.sourceTreeDataSource = res?.data || []
      }
    })
  }
}

</script>

<style lang="scss"> 
</style>
