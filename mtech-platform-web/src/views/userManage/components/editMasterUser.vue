<template> 
  <mt-dialog 
    ref="dialog"
    header="修改"   
    :buttons="buttons"
    @close="hide"> 
    <mt-form ref="userForm" :model="userForm" :rules="rules">
        <mt-form-item prop="userName" label="用户名">
          <mt-input
            v-model="userForm.userName"
            :disabled="true"
            :showClearButton="true"
            type="text"
            placeholder="请输入用户名"
          ></mt-input>
        </mt-form-item>
        <!-- <mt-form-item prop="password" label="密码">
          <mt-input v-model="userForm.password" type="password" placeholder="请输入密码"></mt-input>
        </mt-form-item> -->
        <mt-form-item prop="nickName" label="昵称">
          <mt-input
            v-model="userForm.nickName"
            :disabled="false"
            :showClearButton="true"
            type="text"
            placeholder="请输入昵称"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="phone" label="手机号">
          <mt-input-number
            v-model="userForm.phone"
            :disabled="false"
            :showClearButton="true"
            type="text"
            placeholder="请输入手机号"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item prop="email" label="邮箱">
         <mt-input
            v-model="userForm.email"
            :disabled="false"
            :showClearButton="true"
            type="text"
            placeholder="请输入邮箱"
          ></mt-input>
        </mt-form-item>
      </mt-form>
      
  </mt-dialog> 
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from '@mtech/vue-property-decorator' 

@Component({
  components: { 
  }
})
export default class addMasterUser extends Vue {
  @Prop()
  value !: boolean 

  @Prop()
  itemEdit !: any

  buttons = [
    {
      click: this.hide,
      buttonModel: { content: '取消' }
    },
    {
      click: this.handleConfirm,
      buttonModel: { isPrimary: 'true', content: '确定' }
    }
  ] 

  userForm = {}

  rules = {
    userName: [
      { required: true, message: '请输入用户名', trigger: 'blur' }
    ],
    password: [
      { required: true, message: '请输入密码', trigger: 'blur' }
    ]
  }

  get visible() {
    return this.value
  }

  set visible(v: boolean) {
    this.$emit('input', v)
  }
 
  @Watch('visible') 
  onWatchVisible(val: boolean) {
    if (val) { 
      this.show()
    } 
  }

  @Watch('itemEdit') 
  onWatchItemEdit(val: any) {
    if (val) {
      this.userForm = { ...val }
    }
  }

  hide() {
    this.visible = false
    const ref: any = this.$refs.dialog
    ref.ejsRef.hide()
  }

  show() {   
    const ref: any = this.$refs.dialog
    ref.ejsRef.show()  
  }

  handleConfirm() {
    const ref:any = this.$refs.userForm
    ref.validate((valid:any) => { 
      if (valid) {
        this.$api.tenant.adminUpdateUser(this.userForm).then(() => {
          const parent:any = this.$parent 
          parent.refreshTempaltePage()
          this.hide()
        })
      }
    })  
  }
}

</script>

<style lang="scss" scoped> 
</style>
