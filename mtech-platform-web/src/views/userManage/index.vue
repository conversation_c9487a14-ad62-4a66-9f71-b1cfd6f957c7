<template>
  <div class="router-page--wrap">
    <mt-template-page
      ref="tempaltePageRef"
      :hiddenTabs="true"
      :templateConfig="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    > 
    </mt-template-page> 
 
  </div>
</template>
<script lang="ts">
import { Component, Vue } from '@mtech/vue-property-decorator' 
import { userPageConfig } from './config/index'

@Component({
  components: {  
  }
})
export default class PlatformPermission extends Vue {
  pageConfig: any[] = userPageConfig
  currentVersionData:any = {}
  isShowContentDialog = false 
  isShowMenuDialog = false
 
  handleClickToolBar(e: any) { 
    const { toolbar } = e
    if (toolbar.id === 'MenuConfig') {

    } else if (toolbar.id === 'Version') {
      this.isShowContentDialog = true
      this.currentVersionData = {}
    }
  }

  handleClickCellTool(e: any) {  
    const { data, tool } = e
    if (tool.id === 'edit') {
      this.isShowContentDialog = true
      this.currentVersionData = data
    } else if (tool.id === 'delete') {
      this.deleteVersion(data)
    } else if (tool.id === 'MenuConfig') {
      this.isShowMenuDialog = true
      this.currentVersionData = data
    }
  }

  handleClickCellTitle(e: any) { 
    if (e.field === 'no') {
      this.$router.push({
        path: '/platform/detail'
      })
    }
  }
  
  refreshTempaltePage() {
    const ref = this.$refs.tempaltePageRef as any
    ref.refreshCurrentGridData()
  }

  private deleteVersion(data: any) {
    this.$api.platmenu.versionDelete({  
      id: data.id
    }).then((res: any) => {
      if (res.code === 200) {
        this.$toast({
          content: res.msg || '版本删除成功',
          type: 'success'
        }) 
        this.refreshTempaltePage()
      }
    }).catch((error: any) => {
      this.$toast({
        content: error.msg || '版本删除失败',
        type: 'error'
      }) 
    })
  }
}
</script>
<style lang="scss" scoped> 
</style>
