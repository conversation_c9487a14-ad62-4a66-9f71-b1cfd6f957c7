import Vue from 'vue'
// import ColumnTimeTemplate from '../components/ColumnTimeTemplate.vue'

export const userPageConfig = [
  {  
    useToolTemplate: false,
    toolbar: [ 
      { id: 'View', icon: 'icon_solid_Createorder', title: '查看' } 
    ],
    grid: {
      columnData: [{
        width: '40',
        type: 'checkbox'
      },
      {
        field: 'no', 
        headerText: '序号' 
      }, 
      {
        field: 'name',
        headerText: '用户账号'
      },
      {
        field: 'updateUserName',
        headerText: '真实姓名'
      },
      {
        field: 'createTime',
        headerText: '联系电话' 
      },
      {
        field: 'updateTime',
        headerText: '用户类型' 
      },
      {
        field: 'remark',
        headerText: '所属公司（主）'
      },
      {
        field: 'remark',
        headerText: '公司类型'
      },
      {
        field: 'remark',
        headerText: '用户角色'
      },
      {
        field: 'remark',
        headerText: '创建人'
      },
      {
        field: 'remark',
        headerText: '申请时间'
      },
      {
        field: 'remark',
        headerText: '状态'
      }],
      dataSource: [],
      asyncConfig: {
        url: '/platform/admin/version/query-page',
        methods: 'post',
        params: { 
        },
        serializeList(list: any[]) { 
          return list.map(v => {
            v.updateUserName = v.updateUserName || v.createUserName 
            return v
          })
        }
      }
    }
  }
]

export const masterPageConfig = [
  {  
    useToolTemplate: false,
    toolbar: [ 
      { id: 'Add', icon: 'icon_solid_Createorder', title: '新增' },
      { id: 'Delete', icon: 'icon_solid_Createorder', title: '删除' }
    ],
    grid: {
      columnData: [
        { 
          width: '40',
          type: 'checkbox'
        },
        {
          field: 'userName', 
          headerText: '用户名',
          cellTools: [
            { id: 'edit', title: '编辑' }
          ] 
        }, 
        {
          field: 'nickName', 
          headerText: '昵称' 
        }, 
        {
          field: 'phone',
          headerText: '电话'
        },
        {
          field: 'email',
          headerText: '邮箱'
        }
      ],
      dataSource: [],
      asyncConfig: {
        url: '/iam/admin/account/users/page',
        methods: 'post',
        params: { 
        }
      }
    }
  }

]
