<template>
  <div class="tenant-manage router-page--wrap">
    <mt-template-page
      ref="tempaltePageRef"
      :hiddenTabs="true"
      :templateConfig="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool" 
    > 
    </mt-template-page> 

    <TenantAddDialog 
      v-model="isShowAddDialog" 
      @save="refreshTempaltePage"
     />
     <TenantEditDialog 
      v-model="isShowEditDialog"
      :itemEdit="itemEdit" 
      @save="refreshTempaltePage"
     />
  
  </div>
</template>
<script lang="ts">
import { Component, Vue } from '@mtech/vue-property-decorator'  
import { masterPageConfig } from './config/index'
import TenantAddDialog from './components/addMasterUser.vue'

import TenantEditDialog from './components/editMasterUser.vue'

@Component({
  components: { 
    TenantAddDialog,
    TenantEditDialog
  }
})
export default class MasterUserManage extends Vue {
  pageConfig: any[] = masterPageConfig

  isShowAddDialog = false  
  isShowEditDialog = false
  currTenantData :any = {} 
  itemEdit :any = {}
 
  handleClickToolBar(e: any) { 
    const { toolbar, grid } = e
    const selected = grid.getSelectedRecords()
    if (toolbar.id === 'Add') { 
      this.showAddTenantDialog()
    } 
    if (toolbar.id === 'Delete') {
      if (!selected.length) {
        this.$toast({
          content: '请选择一条数据',
          type: 'warning'
        })
        return 
      }
      this.deleteUser(selected)
    }
  }

  handleClickCellTool(e: any) { 
    const { tool, data } = e
    if (tool.id === 'edit') {
      this.showEditTenantDialog()
      this.itemEdit = data
    }
  }

  handleClickCellTitle(e: any) {
    console.log('use-handleClickCellTitle', e)
    if (e.field === 'column1') {
      // todo title click
    }
  }

  refreshTempaltePage() {
    const ref = this.$refs.tempaltePageRef as any
    ref.refreshCurrentGridData()
  }

  private showAddTenantDialog() {
    this.isShowAddDialog = true
  }
  
  private showEditTenantDialog() {
    this.isShowEditDialog = true
  }

  private deleteUser(data:any) {
    this.$dialog({
      data: {
        title: '删除',
        message: '是否确认删除？'
      },
      success: () => {
        const ids = data.map((i:any) => i.accountId)
        this.$api.tenant.adminDeleteUser({ ids }).then(() => {
          this.refreshTempaltePage()
        })
      }
    })  
  }
}
</script>
<style lang="scss" scoped > 
.tenant-manage { 
  .tenant-status--dot {
    content: ' ';
    width: 8px;
    height: 8px;
    margin-right: 8px;
    border-radius: 50%;
    display: inline-block;
  }
  /deep/ .status {
    border-radius: 3px;
    padding: 2px 3px;
    margin-bottom: 4px;
    display: inline-block;
  }

  /deep/ .status-cooperation {
    color: #6386c1;
    background: #eef2f9;
  }

  /deep/ .status-uncooperation {
    color: #9baac1;
    background: #f5f6f8;
  }
 
  /deep/  .status-valid {
    color: #6386c1;
    &::before {
      @extend .tenant-status--dot;
      background: #6386c1;
    }
  }

  /deep/ .status-unvalid {
    color: #9baac1;
    &::before {
      @extend .tenant-status--dot;
      background: #9baac1;
    }
  }
}

</style>
