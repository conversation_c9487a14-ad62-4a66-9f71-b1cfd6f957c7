// import service from './service'
import API from './api.config' 
import common from './modules/common' 
import detail from './modules/detail' 
import form from './modules/trendForm' 
import serviceManage from './modules/service' 
import platmenu from './modules/platmenu'
import tenant from './modules/tenant'
import enterprise from './modules/enterprise'
import file from './modules/file'
import ruleEng from './modules/ruleEng'
import employee from './modules/employee'
import address from './modules/address'
// 接口里传{noErrTips: true},则不显示错误tips

const axios = ($axios: any) => ({ 
  common: common($axios),
  detail: detail($axios), 
  form: form($axios),
  service: serviceManage($axios),
  platmenu: platmenu($axios),
  tenant: tenant($axios),
  enterprise: enterprise($axios),
  file: file($axios),
  ruleEng: ruleEng($axios),
  employee: employee($axios),
  address: address($axios)
}) 

export default axios(API)
