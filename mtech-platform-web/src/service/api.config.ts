/*
 * @Author: your name
 * @Date: 2021-08-11 16:53:53
 * @LastEditTime: 2021-08-11 18:14:56
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-sourcing-web\src\config\api.config.js
 */
// import { baseConfig, API } from '@mtech-common/utils'
import * as MAPI from '@mtech-common/http' 
import Vue from 'vue'  
const GAPI:any = MAPI
const baseCon:any = GAPI.baseConfig
const API:any = GAPI.API
baseCon.setDefault({ baseURL: '/api' })  

setTimeout(() => {
  baseCon.addNotify({
    success: (msg:any) => {
    // 可以使用$toast组件，msg当前版本下默认为接口response.msg
      Vue.prototype.$toast({
        content: msg,
        type: 'success'
      })
    },
    error: function(msg:any) {
      Vue.prototype.$toast({
        content: msg,
        type: 'error'
      })
    }
  })
}, 100) 

export default {
  $get: API.get,
  $post: API.post,
  $put: API.put,
  $delete: API.delete,
  $patch: API.patch
}
