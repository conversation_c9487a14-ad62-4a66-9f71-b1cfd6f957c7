import { utils } from '@mtech-common/utils'

export default ($axios: any) => ({
  // 国际化区号
  getParamList: (params: any) => {
    return $axios.$post('/master-data/common/countryOrRegion/getParamList', params)
  },
  // 查看系统用户菜单
  systemMenuTree: (params: string) => {
    return $axios.$get(`/permission/user/menu-user/system-tree?appGroup=${params}`)
  },
  // 用户信息
  getUserInfo: () => {
    return $axios.$get('/iam/common/account/userinfo')
  },
  // 获取菜单
  getUserMenu: () => {
    return $axios.$get(`/iam/tenant/user-permission-query/user-menu?appCode=${utils.getAppCode()}`)
  },
  // 退出登录
  logout: () => {
    return $axios.$get('/user/user/logout')
  },
  // 获取系统相关配置
  getApplication: () => {
    return $axios.$get('/platform/common/application/current')
  },
  // 获取系统相关的菜单配置
  menuTree: () => { 
    return $axios.$get('/iam/tenant/user-permission-query/user-menu')
  },

  // 条件查询国家或地区 
  getCountryList: (params: any) => { 
    return $axios.$post('/masterDataManagement/common/country/criteria-query', params)
  },
  // 查询所有国家或地区 
  getAllCountryList: (params: any) => { 
    return $axios.$post('/masterDataManagement/common/country/queryAll', params)
  },
  // 根据parentCode获取地址 
  getAreaListByParent: (params: any) => { 
    return $axios.$post('/masterDataManagement/common/area/selectByParentCode', params)
  },

  // 条件查询获取地址
  getAreaListByCriteria: (params: any) => { 
    return $axios.$post('/masterDataManagement/common/area/criteria-query', params)
  },

  // 根据字典类型代码获取字典明细树
  getDictItemTree: (params: any) => { 
    return $axios.$post('/masterDataManagement/common/dict-item/item-tree', params)
  },

  // 条件查询字典明细树
  getDictItemTreeByCriteria: (params: any) => { 
    return $axios.$post('/masterDataManagement/common/dict-item/criteria-query', params)
  },

  // 获取币种类型
  getCapitalCurrency(params: any) {
    return $axios.$get('/masterDataManagement/common/currency/queryActiveCurrency', params)
  }
 
})
