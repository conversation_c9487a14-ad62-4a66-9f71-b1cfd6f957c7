
export default ($axios: any) => ({
  // 获取根节点组织信息
  getorganizationRootNode(params: any) {
    return $axios.$get('/masterDataManagement/tenant/organization/root-node', params)
  },
  // 获取当前组织下公司列表
  getChildrenCompanyOrganization: (params: any) => {
    return $axios.$post('/masterDataManagement/tenant/organization/getChildrenCompanyOrganization', params)
  },

  // 获取当前组织下部门列表 
  getChildrenDepartmentOrganization: (params: any) => {
    return $axios.$post('/masterDataManagement/tenant/organization/getChildrenDepartmentOrganization', params)
  },

  // 获取当前组织下岗位列表 
  getChildrenStationOrganization: (params: any) => {
    return $axios.$post('/masterDataManagement/tenant/organization/getChildrenStationOrganization', params)
  },

  // 角色管理条件查询
  roleCriteriaQuery: (params: any) => {
    return $axios.$post('/iam/tenant/role/criteria-query', params)
  },

  // 邀请用户成为员工
  inviteUser2employee: (params: any) => {
    return $axios.$post('/masterDataManagement/tenant/employee/invite-user2employee', params)
  },

  // 根据员工id获取员工详情
  getEmployeeInfo: (params: any) => {
    return $axios.$get('/masterDataManagement/tenant/employee/employee-info', params)
  }
 
})
