export default ($axios: any) => ({
  // 主数据条件查询-应用切换数据源
  getApplicationQuery: (params: any) => {
    return $axios.$post('/masterDataManagement/admin/application/query', params)
  },
  // 查询页面元素树结构
  getListElementTree: (params: any) => {
    return $axios.$post('/iam/tenant/permission/list-element-tree', params)
  },
  // 查询页面元素树结构
  getListElementTreePlatform: (params: any) => {
    return $axios.$post('/iam/tenant/permission/list-element-tree-platform', params)
  },
  // 更新菜单节点基础信息
  updateElement: (params: any) => {
    return $axios.$put('/iam/tenant/permission/update-element', params, {
      useNotify: true
    })
  },
  // 获取后端api
  queryApi: (params: any) => {
    return $axios.$put('/iam/tenant/api/criteria-query', params)
  },
  // 根据字典类型代码获取字典明细
  getDictCode: (params: any) => {
    return $axios.$post('/masterDataManagement/common/dict-item/dict-code', params)
  },
  // 新增菜单
  addMenu: (params: any) => {
    return $axios.$post('/iam/tenant/permission-menu/add', params, {
      useNotify: true
    })
  },
  // 编辑保存菜单类
  updateMenu: (params: any) => {
    console.log('$axios', $axios)
    return $axios.$put('/iam/tenant/permission-menu/update', params, {
      useNotify: true
    })
  },
  // 新增列表opt
  addOpt: (params: any) => {
    return $axios.$post('/iam/tenant/permission/add-opt', params, {
      useNotify: true
    })
  },
  // 新增其他
  addOther: (params: any) => {
    return $axios.$post('/iam/tenant/permission/add-element', params, {
      useNotify: true
    })
  },
  // 更新其他页面元素
  updateOther: (params: any) => {
    return $axios.$put('/iam/tenant/permission/update-element', params, {
      useNotify: true
    })
  },
  // 更新opt和列表
  updateOpt: (params: any) => {
    return $axios.$put('/iam/tenant/permission/update-opt', params, {
      useNotify: true
    })
  },
  // 查询元数据表字段接口
  getMetadataTable: (params: any) => {
    return $axios.$post('/iam/tenant/metadata-table/paged-query', params)
  },
  // 删除菜单
  deleteBatch: (params: any) => {
    return $axios.$delete('/iam/tenant/permission/batch-delete', params, {
      useNotify: true
    })
  },
  // 菜单条件查询
  queryCriteria: (params: any) => {
    return $axios.$post('/iam/tenant/permission/criteria-query', params)
  },
  // 新增字段权限
  addPermission: (params: any) => {
    return $axios.$post('/iam/tenant/permission-field/batch-add', params, {
      useNotify: true
    })
  },
  // 删除字段权限
  deletePermission: (params: any) => {
    return $axios.$delete('/iam/tenant/permission-field/batch-delete', params, {
      useNotify: true
    })
  },
  // 编辑字段权限
  updataPermission: (params: any) => {
    return $axios.$put('/iam/tenant/permission-field/update', params, {
      useNotify: true
    })
  },
  // 新增数据权限
  addBatch: (params: any) => {
    return $axios.$post('/iam/tenant/permission-data-item/batch-add', params, {
      useNotify: true
    })
  },
  // 删除数据权限
  deleteDataBatch: (params: any) => {
    return $axios.$delete('/iam/tenant/permission-data-item/batch-delete', params, {
      useNotify: true
    })
  },
  // 删除数据权限
  updataDataBatch: (params: any) => {
    return $axios.$put('/iam/tenant/permission-data-item/update', params, {
      useNotify: true
    })
  }
})
