export default ($axios: any) => ({
  // 新增表单
  addForm: (params: any) => {
    return $axios.$post('/lowcodeWeb/tenant/form-design/add', params, {
      useNotify: true
    })
  },
  // 获取classify
  getClassify: (params: any) => {
    return $axios.$get(`/lowcodeWeb/tenant/form-design/classify/find?parentId=${params}`)
  },
  // 获取分类
  getFind: () => {
    return $axios.$get('/lowcodeWeb/tenant/form-design/classify/find')
  },
  // 获取表单
  getForm: (params: any) => {
    return $axios.$get(`/lowcodeWeb/tenant/form-design/get?id=${params.id}&version=${params.version}`)
  },
  // 删除删除
  deleteDesign: (params: any) => {
    return $axios.$delete('/lowcodeWeb/tenant/form-design/delete', params, {
      useNotify: true
    })
  },
  // 表单更新
  updataForm: (params: any) => {
    return $axios.$put('/lowcodeWeb/tenant/form-design/update', params, {
      useNotify: true
    })
  },
  // 获取关联业务类型
  getBusinessType: () => {
    return $axios.$post('/masterDataManagement/tenant/dict-item/item-tree', { dictCode: 'FORM_DESIGN_BUSINESS_TYPE' })
  },
  // 新增分类
  addClassify: (params: any) => {
    return $axios.$post('/lowcodeWeb/tenant/form-design/classify/add', params)
  },
  // 编辑分类
  updateClassify: (params: any) => {
    return $axios.$put('/lowcodeWeb/tenant/form-design/classify/update', params, {
      useNotify: true
    })
  },
  // 删除分类
  deleteClassify: (params: any) => {
    return $axios.$delete('/lowcodeWeb/tenant/form-design/classify/delete', params, {
      useNotify: true
    })
  },

  /**  模板接口 */

  // 新增模板
  addTemplate: (params: any) => {
    return $axios.$post('/lowcodeWeb/tenant/form-template/add', params, {
      useNotify: true
    })
  },
  // 模板更新
  updataTemplate: (params: any) => {
    return $axios.$put('/lowcodeWeb/tenant/form-template/update', params, {
      useNotify: true
    })
  },
  // 获取模板分页
  getTemplateList: (params: any) => {
    return $axios.$post('/lowcodeWeb/tenant/form-template/system/list', params)
  },
  // 获取我的模板分页
  getMyTemplateList: (params: any) => {
    return $axios.$post('/lowcodeWeb/tenant/form-template/list', params)
  },
  // 获取我的历史模板分页
  getMyHistoryTemplateList: (params: any) => {
    return $axios.$post('/lowcodeWeb/tenant/form-template/history/list', params)
  },
  // 获取模板
  getTemplate: (params: any) => {
    return $axios.$get(`/lowcodeWeb/tenant/form-template/get?id=${params.id}&version=${params.version}`)
  },
  // 删除模板
  deleteTemplate: (params: any) => {
    return $axios.$delete('/lowcodeWeb/tenant/form-template/delete', params, {
      useNotify: true
    })
  },
  // 获取模板分类
  getTemplateFind: () => {
    return $axios.$get('/lowcodeWeb/tenant/form-template/classify/find')
  },
  // 新增模板分类
  addTemplateClassify: (params: any) => {
    return $axios.$post('/lowcodeWeb/tenant/form-template/classify/add', params)
  },
  // 删除分类
  deleteTemplateClassify: (params: any) => {
    return $axios.$delete('/lowcodeWeb/tenant/form-template/classify/delete', params, {
      useNotify: true
    })
  },
  // 编辑模板分类
  updateTemplateClassify: (params: any) => {
    return $axios.$put('/lowcodeWeb/tenant/form-template/classify/update', params, {
      useNotify: true
    })
  },

  /**  字段接口 */

  // 新增字段
  addField: (params: any) => {
    return $axios.$post('/lowcodeWeb/tenant/field/add', params, {
      useNotify: true
    })
  },
  // 删除字段
  deleteField: (params: any) => {
    return $axios.$delete('/lowcodeWeb/tenant/field/delete', params, {
      useNotify: true
    })
  },
  // 编辑字段
  updateField: (params: any) => {
    return $axios.$post('/lowcodeWeb/tenant/field/update', params, {
      useNotify: true
    })
  },
  // 查询字段
  getField: (params: any) => {
    return $axios.$post('/lowcodeWeb/tenant/field/list', params)
  },

  /** 系统模板 */
  
  // 获取系统模板
  getSystemTemplate: (params: any) => {
    return $axios.$get(`/lowcodeWeb/tenant/form-template/get?id=${params.id}&version=${params.version}`)
  },
  // 获取系统模板详情
  getSystemTemplateX: (params: any) => {
    return $axios.$get(`/lowcodeWeb/tenant/form-template/system/get?id=${params.id}&version=${params.version}`)
  },
  // 获取系统模板分类
  getSystemTemplateFind: () => {
    return $axios.$get('/lowcodeWeb/tenant/form-template/classify/system/find')
  }
})
