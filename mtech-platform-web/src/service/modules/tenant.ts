export default ($axios: any) => ({ 
  // 分页查询
  tenantQueryPage: (params: any) => {
    return $axios.$post('/platform/admin/tenant/query-page', params)
  },

  // 版本列表(下拉框)
  versionDropdownList(params: any) {
    return $axios.$get('/platform/common/version/dropDownList', params)
  },

  // 权限分配
  tenantVersionAssign: (params: any) => {
    return $axios.$post('/platform/admin/tenant/assignVersion', params)
  },

  // 租户权限分配详情
  tenantVersionDetail: (params: any) => {
    return $axios.$post('/platform/admin/tenant/showAssignedDetail', params)
  },

  // 权限分配明细回显
  tenantVersionQueryDetail: (params: any) => {
    return $axios.$post('/platform/common/tenant-version/query/detail', params)
  },

  // 企业转租户
  tenantAdd: (params: any) => {
    return $axios.$post('/platform/admin/tenant/add', params)
  },

  // 启用-禁用租户
  tenantDisOrEnable: (params: any) => {
    return $axios.$post('/platform/admin/tenant/disOrEnable', params)
  },

  // 申请成为租户的用户列表 
  adminApplyVersionQuery(params: any) {
    return $axios.$post('/platform/admin/applyVersion/query', params)
  },

  // 明细查看(查看所要开通版本下有哪些权限)
  adminApplyVersionDetail(id: string) {
    return $axios.$post(`/platform/admin/applyVersion/detail/${id}`)
  },

  // 开通申请详情(已处理页签下弹框页内容填充)
  showDetailForHistory(id: string) {
    return $axios.$post(`/platform/admin/applyVersion/showDetailForHistory/${id}`)
  },
  // // 开通申请详情(待处理页签下弹框页内容填充)
  // showDetailForReview(id: string) {
  //   return $axios.$post(`/platform/admin/applyVersion/showDetailForReview/${id}`)
  // },

  // 租户审核 通过
  adminApplyVersionApproval(params: any) {
    return $axios.$post('/platform/admin/applyVersion/approval', params)
  },

  // 租户审核 拒绝
  adminApplyVersionReject(params: any) {
    return $axios.$post('/platform/admin/applyVersion/reject', params)
  },

  // 删除管理平台用户
  adminDeleteUser(params:any) {
    return $axios.$delete('/iam/admin/account/batch-delete', params)
  },

  // 添加管理平台用户
  adminAddUser(params:any) {
    return $axios.$post('/iam/admin/account/add', params)
  },

  // 更新管理平台用户
  adminUpdateUser(params:any) {
    return $axios.$put('/iam/admin/account/update', params)
  }
   
}) 
