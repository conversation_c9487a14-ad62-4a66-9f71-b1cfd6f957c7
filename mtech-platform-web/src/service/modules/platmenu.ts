export default ($axios: any) => ({

  // 分页查询版本
  versionPage: (params: any) => {
    return $axios.$post('/platform/admin/version/query-page', params)
  },

  // 添加版本
  versionAdd: (params: any) => {
    return $axios.$post('/platform/admin/version/add', params)
  },

  // 更新版本
  versionUpdate: (params: any) => {
    return $axios.$put('/platform/admin/version/update', params)
  },

  // 删除版本
  versionDelete: (params: any) => {
    return $axios.$delete(`/platform/admin/version/delete/${params.id}`)
  },

  // 为指定版本配置权限
  versionPermissionAdd: (params: any) => {
    return $axios.$post('/platform/admin/version/permission/add', params)
  },

  // 批量下发版本菜单
  versionDispatch: (params: any) => {
    return $axios.$post('/platform/admin/version/permission/dispatch', params)
  },

  // 删除权限记录
  versionPermissionDel: (params: any) => {
    return $axios.$delete('/platform/admin/version/permission/del', params)
  },

  // 查找指定版本下的权限明细 /api/platform/admin/version/permissionList/{versionId}
  versionPermissionFind: (params: any) => {
    return $axios.$post(`/platform/admin/version/permissionList/${params.id}`)
  },

  // 查询API分类接口
  queryApiCategryList: (params:any) => {
    return $axios.$post('/iam/tenant/api-category/criteria-query', params)
  },

  // 新增API接口
  addApiSave: (params:any) => {
    return $axios.$post('/iam/admin/api/add', params)
  },
  // 编辑API接口
  editApiSave: (params:any) => {
    return $axios.$put('/iam/admin/api/update', params)
  },

  // 版本配置导出权限菜单
  permissionExport: (params: any) => {
    return $axios.$post('/platform/admin/version/permission/export', params)
  },

  // 版本配置导入权限菜单
  permissionImport: (params: any) => {
    return $axios.$post('/platform/admin/version/permission/import', params)
  },

  // 查询接口
  queryOptApiList: (params: any) => {
    return $axios.$post('/iam/tenant/permission/queryOptApiList', params)
  },

  // 新增api接口
  addOptApiList: (params: any) => {
    return $axios.$post('/iam/tenant/permission/addOptApiList', params)
  },

  // 删除api接口
  deleteOptApiList: (params: any) => {
    return $axios.$post('/iam/tenant/permission/deleteOptApiList', params)
  }
})
