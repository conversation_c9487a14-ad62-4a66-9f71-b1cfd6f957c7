
/**
*   1. 文件名和依赖的mtechUI 主题名保持一致
*/
 
@import '@mtech-ui/mtech-ui/build/themes/default.scss';     // 引入mtechUI组件中的 default 主题样式
@import '@mtech/mtech-common-layout/build/themes/default.scss';  // @mtech/mtech-common-layout组件的基础变量
@import '@mtech/common-tree-view/build/themes/default.scss';  // @mtech/common-tree-view组件的基础变量
 



// :root {
//   --accent: #00469c;    // --accent 为mtechUI组件中提供的变量，如果项目中需要对其进行覆盖，可以在此处重写
//   --mpw-personal-address-border-color: var(--accent)    // 项目中自定义的变量, mpw 表示mtech-platform-web
// }
 
// :root {     // 重写 mtech-common-layout 中的变量
//   --mcl-left-nav-sidebar-bg-color: var(--grey-100);
// }