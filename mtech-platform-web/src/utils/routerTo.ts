/**
 * 跨应用路由跳转
 * @param {String} href url地址
 * @param {Object} stateObj 状态参数 可以通过history.state读取
 * @param {String} title 标题 暂时没有用
 */
function routerGo(href = '/', title = '', stateObj = {}) {
  window.history.pushState(stateObj, title, href)
}

/**
 * 路由监听
 * @param {String} routerPrefix 路由前缀
 */
function genActiveRule(routerPrefix: string) {
  // return (location) => location.pathname.startsWith(routerPrefix);
  return (location: { hash: string; }) => location.hash.startsWith(routerPrefix)
}
export {
  routerGo, // 跨应用路由跳转
  genActiveRule // 路由监听
}
