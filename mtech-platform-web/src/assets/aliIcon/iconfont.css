@font-face {
  font-family: "iconfont"; /* Project id 2691026 */
  src: url('iconfont.woff2?t=*************') format('woff2'),
       url('iconfont.woff?t=*************') format('woff'),
       url('iconfont.ttf?t=*************') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-icon_smooth_Purchaseorder:before {
  content: "\e689";
}

.icon-icon_smooth_Platformmanagement:before {
  content: "\e687";
}

.icon-icon_smooth_Workflow:before {
  content: "\e688";
}

.icon-icon_Application:before {
  content: "\e67f";
}

.icon-icon_Account:before {
  content: "\e680";
}

.icon-icon_Role:before {
  content: "\e681";
}

.icon-icon_Grouping:before {
  content: "\e682";
}

.icon-icon_Engine:before {
  content: "\e683";
}

.icon-icon_SystemSettings:before {
  content: "\e684";
}

.icon-icon_Bypassaccount:before {
  content: "\e685";
}

.icon-icon_Tenant:before {
  content: "\e686";
}

