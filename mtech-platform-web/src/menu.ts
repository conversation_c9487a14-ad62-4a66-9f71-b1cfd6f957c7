import routerPermission from '@mtech/router-permission'
import router from '@/router/index'
import $API from '@/service/index'
import store from '@/store/index'
const masterCode = 'PLATFORM_ADMIN'

export const permissionAsync = async () => {
  try {
    const res = await $API.common.getUserInfo()
    store.dispatch('user/setUserInfo', res.data)
  } catch (error) {}

  try {
    const res2 = await $API.common.getUserMenu()
    store.dispatch('menu/setMenu', res2.data)
    if ((store.state as any).user.userInfo.roleList.includes(masterCode)) {
      return
    }
    const permissionList: Iterable<any> | null | undefined = []
    recurssionRouter(permissionList, res2.data?.routesIn || [])
    routerPermission.routerPermissionInit({
      router,
      permissionList: [...new Set(permissionList)],
      unCheck: [],
      unPowerPath: '/platform/404',
      unLoginPath: ''
    })
  } catch (error) {
    routerPermission.routerPermissionInit({
      router,
      permissionList: [],
      unCheck: [],
      unPowerPath: '/platform/404',
      unLoginPath: ''
    })
  }
}

const recurssionRouter = (routesList: any, routerArry: any) => {
  routerArry.map((item: any) => {
    routesList.push(item.path)
    if (item.children && item.children.length) {
      recurssionRouter(routesList, item.children)
    }
  })
}
