<template>
  <div class="home">  
    <!-- <router-view   v-if="canReander" /> --> 
    <layout />
  </div>
</template>

<script lang="ts">
import { Component, Vue } from '@mtech/vue-property-decorator'  
// import Layout from '@/components/layout/index.vue'
import Layout from '@/components/layout/layout.vue'

@Component({
  components: { 
    Layout
  }
})
export default class App extends Vue {
  canReander = false

  created() { 
    console.log('object')
    this.$store.dispatch('menu/getMenu').then(() => { 
      this.canReander = true
    })
  }
}
</script>

<style lang="scss">
html,
body,
#app {
  width: 100%;
  height: 100%;
  background: #fff;
}
.home {
  flex: 1;
}
</style>
