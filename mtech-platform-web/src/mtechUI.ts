/* eslint-disable */
import Vue from 'vue'  
import MtechUI from '@mtech-ui/mtech-ui'
import MtCommonLayout from "@mtech/mtech-common-layout";
import MtCommonTree from '@mtech/common-tree-view' ;
import MtTemplatePage from "@mtech/common-template-page";

// import '@mtech-ui/base/build/esm/bundle.css'
// import "@mtech/side-menu/dist/index.css";
// import '@mtech/mtech-common-layout/lib/css/style.css'
import "@mtech/mtech-common-layout/build/esm/bundle.css";
import '@mtech/common-tree-view/build/esm/bundle.css';
import "@mtech/common-template-page/build/esm/bundle.css";
import "@mtech-form-design/deploy/build/esm/bundle.css";

Vue.use(MtechUI);
Vue.use(MtCommonLayout)
Vue.component("mt-template-page", MtTemplatePage);
Vue.component('mt-common-tree',MtCommonTree)
// Vue.component("mt-horizontal-list", MtHorizontalList);



 