
<template>
  <div class="address-select--wrap"> 
    <mt-select  
      v-if="isShowCountry"
      class="address-select--item"
      :allowFiltering="true"
      :fields="countryField"
      :dataSource="countryList"
      v-model="formData.countryCode"
      placeholder="请选择国家/地区"
      :change="handleCountryChange">
    </mt-select>

    <mt-select  
      v-if="isShowProvince"
      class="address-select--item"
      :allowFiltering="true"
      :fields="areaField"
      :dataSource="provinceList"
      v-model="formData.provinceCode"
      placeholder="请选择省"
      :change="handleProvinceChange">
    </mt-select>

    <mt-select  
      v-if="isShowCity"
      class="address-select--item"
      :allowFiltering="true"
      :fields="areaField"
      :dataSource="cityList"
      v-model="formData.cityCode"
      placeholder="请选择市"
      :change="handleCityChange" >
    </mt-select>

    <mt-select  
      v-if="isShowTown"
      class="address-select--item"
      :allowFiltering="true"
      :fields="areaField"
      :dataSource="townList"
      v-model="formData.townCode"
      placeholder="请选择区/县"
      :change="handleTownChange" >
    </mt-select>

    <mt-input 
      v-if="isShowDetail"
      class="address-input--item"
      v-model="formData.addressDetail" 
      placeholder="请输入明细地址"
      @change="handleDetailChange" /> 
      
  </div>
</template>

<script lang="ts">
/* eslint-disable no-unused-vars */
import { Component, Prop, Vue, Watch } from '@mtech/vue-property-decorator'  
enum EAreaLevel {
  country = 'country',
  province = 'province',
  city = 'city',
  town = 'town',
  detail = 'detail'
}

interface IAddressData {
  countryCode?: string,
  provinceCode?: string,
  cityCode?: string,
  townCode?: string,
  countryName?: string,
  provinceName?: string,
  cityName?: string,
  townName?: string,
  addressDetail?: string,
}

type AreaLevel = Array<EAreaLevel>

// 按序排列
const areaLevelList = [EAreaLevel.country, EAreaLevel.province, EAreaLevel.city, EAreaLevel.town, EAreaLevel.detail]

@Component({ 
})
export default class AddressSelect extends Vue { 
  @Prop() 
  address !: IAddressData

  @Prop({
    default: () => areaLevelList
  }) 
  areaLevel !: AreaLevel

  formData: IAddressData = {
    countryCode: 'CHN',
    countryName: '中国 内地',
    provinceCode: '',
    provinceName: '',
    cityCode: '',
    cityName: '',
    townCode: '',
    townName: '',
    addressDetail: '' 
  }

  countryList: any[] = []
  provinceList: any[] = []
  cityList: any[] = []
  townList: any[] = []

  countryField: any = {
    value: 'countryCode',
    text: 'shortName'
  }

  areaField: any = {
    value: 'areaCode',
    text: 'areaName'
  } 

  get isShowCountry() {
    return this.areaLevel && this.areaLevel.includes(EAreaLevel.country) 
  }

  get isShowProvince() {
    return this.areaLevel && this.areaLevel.includes(EAreaLevel.province) 
  }

  get isShowCity() {
    return this.areaLevel && this.areaLevel.includes(EAreaLevel.city) 
  }

  get isShowTown() {
    return this.areaLevel && this.areaLevel.includes(EAreaLevel.town) 
  }

  get isShowDetail() {
    return this.areaLevel && this.areaLevel.includes(EAreaLevel.detail) 
  }

  get minLevelName() { 
    let minLevel = '';

    [...areaLevelList].reverse().some(key => {
      if (key !== EAreaLevel.detail && this.areaLevel.includes(key)) {
        minLevel = key
        return true
      }
      return false
    })

    return minLevel
  }

  @Watch('address', { immediate: true })
  onWatchAddress(val: IAddressData) {
    if (val && Object.keys(val).length) {
      this.formData = Object.assign({}, this.formData, val)
      if (Object.values(this.formData).every(v => !v)) {
        this.formData.countryCode = 'CHN'
        this.formData.countryName = '中国 内地' 
      }
    }    
  }

  mounted() { 
    if (this.isShowCountry) {
      this.getCountryList()
    } else {
      this.getProvinceList()
    }
  }

  handleDetailChange(value: string) {
    if (this.isShowDetail) { 
      this.$set(this.formData, 'addressDetail', value || '') 
      this.emitChangeData(EAreaLevel.detail)
    }
  }

  handleTownChange(event: any = {}) {
    if (!this.isShowTown) return
    if (event.isInteracted === false) {
      return
    }
    const { itemData } = event   
    this.$set(this.formData, 'townCode', itemData?.areaCode || '')
    this.$set(this.formData, 'townName', itemData?.areaName || '') 
    this.emitChangeData(EAreaLevel.town) 
  }

  handleCityChange(event: any = {}) { 
    if (!this.isShowCity) {
      this.emitChangeData(EAreaLevel.city) 
      return
    }
    if (event.isInteracted === false) {
      return
    }
    const { itemData, e } = event   
    this.$set(this.formData, 'cityCode', itemData?.areaCode || '')
    this.$set(this.formData, 'cityName', itemData?.areaName || '') 
    this.townList = [] 
    if (itemData?.areaCode) {
      this.getTownList(itemData.areaCode)
    } 

    if (e) {
      this.handleTownChange({ itemData: {} })
    } else {
      this.handleTownChange({
        itemData: {
          areaCode: this.formData.townCode,
          areaName: this.formData.townName
        }
      })
    }  
  }

  handleProvinceChange(event: any = {}) {
    if (!this.isShowProvince) {
      this.emitChangeData(EAreaLevel.province) 
      return
    } 
    const { itemData, e } = event  
    this.$set(this.formData, 'provinceCode', itemData?.areaCode || '')
    this.$set(this.formData, 'provinceName', itemData?.areaName || '')  
    this.cityList = [] 
    if (itemData?.areaCode) {
      this.getCityList(itemData.areaCode)
    } 

    if (e) {
      this.handleCityChange({ itemData: {} })
    } else {
      this.handleCityChange({
        itemData: {
          areaCode: this.formData.cityCode,
          areaName: this.formData.cityName
        }
      })
    } 
  }

  handleCountryChange(event: any) { 
    if (!this.isShowCountry) {
      this.emitChangeData(EAreaLevel.country) 
      return
    }
    if (event.isInteracted === false) {
      return
    }
    const { itemData, e } = event  
    this.$set(this.formData, 'countryCode', itemData?.countryCode || '')
    this.$set(this.formData, 'countryName', itemData?.shortName || '') 
    this.provinceList = []
    if (this.formData.countryCode === 'CHN') {
      this.getProvinceList()
    } 
    this.handleProvinceChange()
  }

  private emitChangeData(level: EAreaLevel) {
    if (level === this.minLevelName || level === EAreaLevel.detail) {
      this.$emit('change', this.formData)
    }
  } 

  private async getCountryList() {
    const res = await this.$api.common.getAllCountryList()  
    this.countryList = (res?.data || []) 
  }

  private async getProvinceList() {
    const res = await this.$api.common.getAreaListByParent({
      parentCode: ''
    })
    this.provinceList = (res?.data || []) 
  }

  private async getCityList(code: string) {
    const res = await this.$api.common.getAreaListByParent({
      parentCode: code
    })
    this.cityList = (res?.data || []) 
  } 

  private async getTownList(code: string) {
    const res = await this.$api.common.getAreaListByParent({
      parentCode: code
    })
    this.townList = (res?.data || []) 
  }
}
</script>

<style lang="scss" scoped>  
.address-select--wrap {
  display: flex;
  .address-select--item {
    width: 20%;
    margin-right: 20px;
  }
  .address-input--item {
    width: 40%;
  }
}
</style>
