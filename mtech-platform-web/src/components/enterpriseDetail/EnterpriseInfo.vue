<template> 
  <div class="business-content"> 
    <!-- 工商信息 -->
    <mt-accordion :dataSource="enterpriseDataSource"></mt-accordion>

    <mt-form id="PLATFORM-ENTERPRISE-DETAIL-ENTERPRISE-INFO" style="display:none;" >  
      <mt-row :gutter="20">
        <mt-col :span="12">
          <mt-form-item label="企业资质"> 
            <div class="logo-image-wrap">
              <img :src="enterpriseProof.qualification.fileUrl">
            </div>
          </mt-form-item>  
        </mt-col>
        <mt-col :span="12">
          <mt-form-item label="企业授权函">
            <div class="logo-image-wrap">
              <img :src="enterpriseProof.auth.fileUrl">
            </div>
          </mt-form-item>  
        </mt-col>
      </mt-row>

      <mt-row :gutter="20" v-if="enterpriseProof.others.length">
        <mt-col :span="24">
          <mt-form-item label="其他"> 
            <div class="logo-image-wrap" v-for="item in enterpriseProof.others" :key="item.fileId">
              <img :src="item.fileUrl">
            </div>
          </mt-form-item>  
        </mt-col> 
      </mt-row>

    </mt-form> 
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from '@mtech/vue-property-decorator'  

@Component({
  components: {   
  }
})
export default class EnterpriseInfo extends Vue {
  @Prop() 
  formData !: any

  enterpriseDataSource:any[] = [
    {
      header: ' 企业证明',
      expanded: false,
      content: '#PLATFORM-ENTERPRISE-DETAIL-ENTERPRISE-INFO'
    }
  ]

  get enterpriseProof() {
    const { enterpriseInfoProofFileDTOList = [] } = this.formData
    const info:any = {
      qualification: {
        fileUrl: ''
      },
      auth: {
        fileUrl: ''
      },
      others: []
    }
    enterpriseInfoProofFileDTOList.forEach((item:any) => {
      if (item.fileType === 1) { // 企业资质
        info.qualification = item
      } else if (item.fileType === 2) { // 企业授权涵
        info.auth = item
      } else { // 服务区域
        info.others.push(item)
      }
    }) 

    return info
  }

  mounted() {
    // this.enterpriseDataSource[0].content = this.$refs.enterprise
  }
}
 
</script>

<style lang="scss" scoped>
.logo-image-wrap {
  width: 180px;
  height: 120px;
  text-align: center;
  background: #FBFCFD;
  border: 1px dashed #E8E8E8;

  img {
    max-height: 100%;
    max-width: 100%;
  }
}
</style>
