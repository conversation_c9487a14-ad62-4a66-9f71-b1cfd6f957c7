<template>
  <div class="mt-top-nav">
    <div class="nav-logo" :class="openM ? '' : 'nav-logo-close'">
      <!-- <svg-icon class="full-screen" icon-class="icon_Full_screen" :class="openM ? '' : 'nav-logo-close2'" @click="toggleScreen"></svg-icon> -->
      <img
        :class="openM ? '' : 'nav-logo-close2'"
        src="../../../assets/logo.png"
      />
      <svg-icon
        class="main-menu"
        icon-class="icon_ShrinkExpand"
        @click="tgSideBar"
      ></svg-icon>
    </div>
    <div class="nav-content">
      <div v-if="showMore" class="tabs-arrow" @click="toLeft"></div>
      <div ref="tagsP" class="option-content">
        <!-- <tags-view ref="tagsV" @getShowTag="getShowTag" @goRight="goRight"></tags-view> -->
      </div>
      <div
        v-if="showMore"
        class="tabs-arrow arrow-right"
        @click="toRight"
      ></div>
      <div class="public-content">
        <!-- <div class="option-item split">
          <mt-select
            id="appModule"
            :data-source="appModule.data"
            :width="appModule.width"
            popup-width="100px"
            float-label-type="Never"
            placeholder="切换应用"
            @select="changeApp"
          ></mt-select>
        </div> -->
        <div class="option-item split">
          <mt-select
            id="filter"
            :data-source="language.data"
            :width="language.width"
            popup-width="60px"
            float-label-type="Never"
            placeholder="CN"
          ></mt-select>
        </div>
        <div class="option-item split">
          <svg-icon class="option-svg" icon-class="message"></svg-icon>
        </div>
        <div class="option-item split">
          <svg-icon class="option-svg" icon-class="tips"></svg-icon>
          <div class="option-badge">5</div>
        </div>
        <div class="option-item">
          <span>
            <div class="option-name">{{ user.name }}</div>
          </span>
          <svg-icon
            class="option-svg"
            style="font-size: 45px"
            icon-class="users"
          ></svg-icon>
          <!-- <mt-select
            id="filter"
            css-class="option-dropList"
            :popup-width="options.popupWidth"
            :data-source="options.data"
            :width="options.width"
            float-label-type="Never"
            placeholder=""
            @change="handleSelect"
          ></mt-select> -->
          <ejs-menu
            :items="menuItems"
            :select="handleSelectMenu"
            :showItemOnClick="true"
          ></ejs-menu>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { logOut } from '@mtech-sso/single-sign-on' //, hasAuth, clearAuth
import { mapGetters } from 'vuex'
import screenfull from 'screenfull'
import bus from '@/utils/bus'
import { getOrigin } from '@/utils/env'
// import { source } from '@/apis/index'
// import TagsView from '../TagsView/index.vue'
export default {
  name: 'TopNav',
  components: {
    // TagsView
  },
  data() {
    return {
      scrollNum: 0,
      openM: true,
      showMore: false,
      transition: {
        display: false,
        list: ['新增报价', '新增代理商', '新增合同', '查看代理商']
      },
      user: {
        name: '姓名',
        role: '普通',
        photo: ''
      },
      appModule: {
        width: '100px',
        data: [
          {
            text: 'SRM',
            value: getOrigin('srm')
          },
          {
            text: '个人中心',
            value: getOrigin('uc')
          }
          // {
          //   text: '平台管理',
          //   value: getOrigin('platform')
          // }
        ]
      },
      language: {
        width: '50px',
        data: [
          {
            text: '中文',
            value: 'zh-CN'
          },
          {
            text: '英语',
            value: 'en'
          }
          // {
          //   text: '日语',
          //   value: 'ja'
          // }
        ]
      },
      options: {
        width: '0',
        popupWidth: '100px',
        data: ['个人信息', '修改密码', '清除缓存', '退出']
      },
      menuItems: [
        {
          text: '',
          iconCss: 'e-ddl-icon e-icons',
          items: [
            {
              text: '个人信息'
            },
            {
              text: '修改密码'
            },
            {
              text: '清除缓存'
            },
            {
              text: '切换模块',
              items: [{ text: 'SRM' }, { text: '个人中心' }]
            },
            {
              text: '切换租户',
              items: [
                { text: '恒力集团' },
                { text: '建信金融科技股份有限公司' }
              ]
            },
            {
              text: '退出'
            }
          ]
        }
      ]
    }
  },
  computed: {
    ...mapGetters(['sidebar'])
  },
  created() {
    this.$store.dispatch('user/setUserInfo')
    this.$store.dispatch('setApplication')
  },
  mounted() {
    bus.$on('changeIsOpen', (res) => {
      this.openM = res
    })
    // this.$http.get(source.queryUserInfo).then((r) => {
    //   // const { photo, nickName } = r.data;
    //   this.user.name = r.data.nickName
    //   this.user.photo = r.data.photo
    // })
  },
  methods: {
    goRight(a) {
      if (this.showMore) {
        const distance = this.$refs.tagsP.scrollLeft
        const scrollNum = this.$refs.tagsP.scrollLeft + a
        let step = (distance - scrollNum) / 50
        if (step < 0) step = -step
        this.moveSlow(distance, scrollNum, step, true)
      }
    },
    toRight() {
      // 往右滚动
      // this.$refs.tagsP.scrollLeft = this.scrollNum;
      const distance = this.$refs.tagsP.scrollLeft
      const scrollNum = distance + 200
      let step = (distance - scrollNum) / 50
      if (step < 0) step = -step
      this.moveSlow(distance, scrollNum, step, true)
    },
    toLeft() {
      // 左滚动
      const distance = this.$refs.tagsP.scrollLeft
      let scrollNum = distance - 200
      if (scrollNum < 0) {
        scrollNum = 0
      }
      let step = (distance - scrollNum) / 50
      if (step < 0) step = -step
      this.moveSlow(distance, scrollNum, step, false)
    },
    moveSlow(distance, total, step, LOR) {
      // 正向滚动 和 反向滚动
      if (LOR) {
        // 每隔1毫秒移动一小段距离，直到移动至目标至为止，反之亦然
        if (distance < total) {
          distance += step
          this.$refs.tagsP.scrollLeft = distance
          setTimeout(() => {
            this.moveSlow(distance, total, step, true)
          }, 1)
        } else {
          this.$refs.tagsP.scrollLeft = total
        }
      } else if (!LOR) {
        if (distance > total) {
          distance -= step
          this.$refs.tagsP.scrollLeft = distance
          setTimeout(() => {
            this.moveSlow(distance, total, step, false)
          }, 1)
        } else {
          this.$refs.tagsP.scrollLeft = total
        }
      }
    },
    getShowTag(a) {
      this.showMore = a
    },
    tgSideBar() {
      this.$emit('toggleSideBar')
      this.openM = !this.openM
    },
    toggleScreen() {
      screenfull.toggle()
    },
    toggleTransitionStatus() {
      this.transition.display = !this.transition.display
    },

    changeApp(value) {
      const url = value.itemData.value
      window.location.href = url
    },

    handleSelect(e) {
      console.log(e)
      if (e.itemData.value === '退出') {
        logOut()
      }
    },
    handleSelectMenu(args) {
      const { item } = args
      if (item.text === 'SRM') {
        const url = getOrigin('srm')
        window.location.href = url
      }
      if (item.text === '个人中心') {
        const url = getOrigin('uc')
        window.location.href = url
      }
      if (item.text === '退出') {
        logOut()
      }
    }
  }
}
</script>
<style lang="scss">
.mt-body .e-sidebar.e-left {
  border-right: none;
  box-shadow: 1px 0 0 0 rgb(237, 239, 243);
}
.mt-top-nav {
  .e-input-group {
    border: none !important;
    .e-input {
      text-align: center;
    }
    &::before {
      background: none;
    }
  }

  .e-menu-container,
  .e-menu-wrapper {
    background: transparent !important;

    .e-selected,
    .e-selected,
    .e-focused {
      background-color: #f5f6f9;
      color: #00469c;
    }

    .e-menu-item.e-menu-caret-icon,
    .e-menu-item.e-menu-caret-icon {
      padding-right: 0 !important;
      padding-left: 0 !important;
    }

    .e-menu-item .e-menu-icon {
      margin-right: 0 !important;
    }

    .e-menu-ite {
      padding: 0 !important;
    }

    .e-ddl-icon {
      line-height: 40px;
    }
    .e-ddl-icon::before {
      display: inline-block;
      width: 12px;
      height: 12px;
      font-size: 12px;
      content: "\e83d";
    }
    .e-caret {
      display: none;
    }
  }
}
</style>
<style lang="scss" scoped>
.mt-top-nav {
  height: 60px;
  background: linear-gradient(rgba(245, 245, 245, 1), rgba(245, 245, 245, 1)),
    linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 1));
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  min-width: 1024px;
  display: flex;
  z-index: 2;
  .tabs-arrow {
    // display: none;
    width: 20px;
    height: 30px;
    line-height: 30px;
    position: relative;
    cursor: pointer;
    border-radius: 4px 0 0 0;
    box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.1);
    &:after {
      content: "";
      position: absolute;
      width: 0;
      height: 0;
      border-top: 4px solid transparent;
      border-bottom: 4px solid transparent;
      border-left: 0;
      border-right: 4px solid #6e7a8d;
      left: 8px;
      top: 12px;
    }
    &.arrow-right {
      border-radius: 0 4px 0 0;
      &:after {
        border-top: 4px solid transparent;
        border-bottom: 4px solid transparent;
        border-left: 4px solid #6e7a8d;
        border-right: 0;
      }
    }
  }
  .nav-logo {
    transition: 0.5s;
    position: relative;
    width: 249px;
    height: 60px;
    line-height: 60px;
    padding: 0 20px;
    align-items: center;
    display: flex;
    flex-shrink: 0;

    img {
      height: 20px;
    }

    .full-screen {
      margin-right: 10px;
      color: #98aac3;
      cursor: pointer;

      &:hover {
        color: #00469c;
      }
    }

    .main-menu {
      color: #98aac3;
      cursor: pointer;
      position: absolute;
      right: 20px;
    }
  }

  .nav-content {
    display: flex;
    flex: 1;
    background: transparent;
    align-items: flex-end;
    justify-content: space-between;
    // padding: 0 10px 0 6px;
    .option-content {
      overflow-y: hidden;
      overflow-x: auto;
      // width: calc(100vw - 617px);
      min-width: 350px;
      height: calc(100% - 30px);
      &::-webkit-scrollbar {
        display: none;
      }
      &:hover {
        &::-webkit-scrollbar {
          display: block;
        }
      }
    }
    .public-content {
      // width: 350px;
      margin-right: 20px;
      height: 60px;
      align-items: center;
      display: flex;
    }
    .option-item {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 0 15px;
      // display: inline-block;
      text-align: center;
      position: relative;
      .option-name {
        max-width: 80px;
        height: 18px;
        line-height: 18px;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(35, 43, 57, 1);
        white-space: nowrap; //不换行
        overflow: hidden; //超出隐藏
        text-overflow: ellipsis; //变成...
      }
      .option-role {
        max-width: 80px;
        height: 16px;
        line-height: 16px;
        font-size: 12px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(152, 170, 195, 1);
        white-space: nowrap; //不换行
        overflow: hidden; //超出隐藏
        text-overflow: ellipsis; //变成...
      }
      .option-svg {
        font-size: 18px;
        color: #4d5b6f;
        margin-right: 5px;
      }
      .option-dropList {
        border-bottom: none;
        &::before {
          background: none;
        }
      }
      span {
        color: #4d5b6f;
      }

      &.split {
        position: relative;
        &::after {
          content: "";
          position: absolute;
          right: 0;
          border-right: 1px solid #e8e8e8;
          height: 60px;
          width: 1px;
        }
      }

      .option-badge {
        background: red;
        color: #fff;
        width: 12px;
        height: 12px;
        display: flex;
        font-size: 10px;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        position: absolute;
        left: 24px;
        top: -3px;
        opacity: 0.75;
      }

      .ejs-dropdown {
        position: absolute;
        left: 0;
        top: 25px;
      }
    }
    .option-item:last-child {
      padding-right: 0 !important;
    }
  }
  .nav-logo-close {
    width: 16px;
    transition: width 0.5s ease, visibility 0.5s;
    // border-right: 1px solid rgba(0, 0, 0, 0.12);
    box-shadow: 1px 0 0 0 rgba(237, 239, 243, 1);
  }
  .nav-logo-close2 {
    width: 0px;
    transition: width 0.5s ease, visibility 0.5s;
  }
  /deep/ .e-input-group {
    border: none !important;
    .e-input {
      text-align: center;
    }
    &::before {
      background: none;
    }
  }
}
</style>
