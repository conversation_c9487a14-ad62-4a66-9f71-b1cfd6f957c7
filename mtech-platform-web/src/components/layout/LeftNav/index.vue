<template>
  <mt-side-menu
    ref="sidebarInstance"
    :data-source="dataSource"
    :fields="fields"
    :target="target"
    :allow-switch="false"
    @menuSelect="menuSelect"
    @getIsOpen="getIsOpen"
  ></mt-side-menu>
</template>

<script>
import bus from '@/utils/bus.ts'
import { getOrigin } from '@/utils/env'

export default {
  props: {
    target: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      fields: {
        id: 'id',
        text: 'name',
        icon: 'icon',
        path: 'path',
        children: 'children',
        parentId: 'parentId'
      }
    }
  },
  computed: {
    dataSource() {
      let val = []
      if (this.$store.state && this.$store.state.menu && this.$store.state.menu.menu) {
        val = this.$store.state.menu.menu
      }
      if (val.length === 0) {
        val = [
          {
            id: '1',
            name: '首页',
            url: '/',
            parentId: '0'
          }
        ]
      }
      return val
    }
  },
  methods: {
    menuSelect(val) { 
      if (val && val.data) {
        let url = ''
        if (val.data.url) {
          url = val.data.url.trim()
        }
        if (url.indexOf('/#') === 0) {
          window.location.href = `${getOrigin('uc')}${url}`
        } else {
          this.$router.push({ path: url })
        }
      }
    },
    getIsOpen(e) {
      bus.$emit('changeIsOpen', e)
    },
    toggleMenuStyle() {
      this.$store.commit('app/TOGGLE_SIDEBAR')
      this.setSideBarOption()
    },
    setSideBarOption() {
      this.$refs.sidebarInstance.toggle()
    }
  }
}
</script>
<style lang="scss">
.mt-body .e-sidebar.e-left.e-open {
  background: #f5f5f5;
  box-shadow: 1px 0 0 0 rgb(237, 239, 243);
}
.mt-body .e-sidebar.e-left.e-close.e-dock {
  background: #f5f5f5;
  box-shadow: 1px 0 0 0 rgb(237, 239, 243);
}
.tree-control-wrapper[data-v-7d1bbcc1] .mt-tree-view .e-treeview > .e-list-parent.e-ul {
  overflow-y: hidden !important;
}
</style>
