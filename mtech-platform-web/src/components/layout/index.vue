<template>
  <div>
    <client-only>
      <top-nav @toggleSideBar="toggleSideBar"></top-nav>
    </client-only>
    <div class="mt-body">
      <client-only>
        <left-nav ref="leftMenu" target=".mt-body"></left-nav>
      </client-only>
      <div class="mt-container">
        <div class="mt-main-container">
          <micro-container
            v-show="isMicroRoute"
            container-class="mainMicroContainer"
          ></micro-container>
          <router-view />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { MicroContainer } from '@mtech-micro-frontend/vue-main'
import LeftNav from './LeftNav/index.vue'
import TopNav from './TopNav/index.vue'
// import TagsView from "./TagsView";

export default {
  name: 'Layout',
  data() {
    return {
      isMicroRoute: false
    }
  },
  components: {
    LeftNav,
    TopNav,
    // TagsView,
    MicroContainer
  },
  mounted() {
    this.checkIsMicroRoute(this.$route)
  },
  watch: {
    $route(route) {
      this.checkIsMicroRoute(route)
    }
  },
  computed: {
    ...mapGetters(['sidebar'])
  },
  methods: {
    checkIsMicroRoute(route) {
      if (route.path.startsWith('/platform')) {
        this.isMicroRoute = false
      } else {
        this.isMicroRoute = true
      }
    },
    toggleSideBar() {
      this.$refs.leftMenu.toggleMenuStyle()
    }
  }
}
</script>
<style lang="scss" scoped>
.mt-body {
  display: flex;
  margin-top: 60px;
  height: calc(100vh - 60px);

  .mt-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    // overflow: auto;
    position: relative;
    transition: width 0.4s;
    padding: 20px;
    height: 100%;
    background: linear-gradient(rgba(250, 250, 250, 1), rgba(250, 250, 250, 1)), linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 1));

    .mt-main-container {
      flex: 1;
      min-height: 0;
      // box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);

      > div {
        height: 100%;
        /deep/ .mainMicroContainer {
          height: 100%;
          > div {
            height: 100%;
          }
        }
      }
    }
  }
}
</style>
