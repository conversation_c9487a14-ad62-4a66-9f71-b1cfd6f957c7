<template>
  <div id="tags-view-tabs" ref="tags" class="tags-view-container">
    <NuxtLink
      v-for="(tag, i) in visitedViews"
      ref="tag"
      :key="tag.path"
      :class="isActive(tag) ? 'active' : ''"
      :to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }"
      tag="span"
      class="tags-view-item"
      @click.middle.native="!isAffix(tag) ? closeSelectedTag(tag) : ''"
    >
      <!-- @contextmenu.prevent.native="openMenu(tag, $event)" -->
      <div style="transform: skew(20deg)">
        {{ tag.title }}
      </div>
      <span v-if="!isAffix(tag)" style="transform: skew(20deg)" class="el-icon-close" @click.prevent.stop="closeSelectedTag(tag)" />
      <span v-if="isActive(tag)" class="right-deg"></span>

      <span v-else-if="i === visitedViews.length - 1" class="right-deg" style="background-color: #f0f0f0"></span>
    </NuxtLink>
    <ul v-show="visible" :style="{ left: left + 'px', top: top + 'px' }" class="contextmenu">
      <li @click="refreshSelectedTag(selectedTag)">Refresh</li>
      <li v-if="!isAffix(selectedTag)" @click="closeSelectedTag(selectedTag)">Close</li>
      <li @click="closeOthersTags">Close Others</li>
      <li @click="closeAllTags(selectedTag)">Close All</li>
    </ul>
  </div>
</template>

<script>
// let _this
// window.onresize = function () {
//   _this.openMore()
// }
export default {
  name: 'TagsView',
  data() {
    return {
      visible: false,
      moreList: [],
      top: 0,
      left: 0,
      selectedTag: {},
      affixTags: [],
      showMore: false
    }
  },
  computed: {
    visitedViews() {
      return this.$store.state.tagsView.visitedViews
    },
    routes() {
      return this.$store.state.permission.routes
    },
    vLength() {
      return this.$store.state.tagsView.visitedViews.length
    }
  },
  watch: {
    $route() {
      // this.addTags()
      this.moveToCurrentTag()
    },
    visible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    },
    visitedViews(v, a) {
      console.log(v, a)
      this.$nextTick(() => {
        if (this.isEllipsis(this.$refs.tags)) {
          this.$emit('getShowTag', true)
        } else {
          this.$emit('getShowTag', false)
        }
      })
    },

    vLength(v, a) {
      if (v > a) {
        this.$emit('goRight', this.$refs.tags.offsetWidth)
      }
    }
  },
  created() {
    // _this = this
  },
  mounted() {
    this.initTags()
    // this.addTags()
    this.openMore()
    // window.addEventListener('beforeunload', function () {
    //   sessionStorage.setItem('tagsView', JSON.stringify(_this.visitedViews))
    // })
  },
  beforeDestroy() {},
  methods: {
    isEllipsis(dom) {
      let flag = true
      if (dom && dom.scrollWidth) {
        flag = dom.scrollWidth > dom.offsetWidth
      }
      return flag
    },
    openMore() {
      this.$emit('getShowTag', this.isEllipsis(document.getElementById('tags-view-tabs')))
    },
    isActive(route) {
      return route.path === this.$route.path
    },
    isAffix(tag) {
      return tag.meta && tag.meta.affix
    },
    filterAffixTags(routes, basePath = '/') {
      let tags = []
      routes.forEach((route) => {
        if (route.meta && route.meta.affix) {
          const tagPath = basePath + route.path
          tags.push({
            fullPath: tagPath,
            path: tagPath,
            name: route.name,
            meta: { ...route.meta }
          })
        }
        if (route.children) {
          const tempTags = this.filterAffixTags(route.children, route.path)
          if (tempTags.length >= 1) {
            tags = [...tags, ...tempTags]
          }
        }
      })
      return tags
    },
    initTags() {
      // const affixTags = (this.affixTags = this.filterAffixTags(this.routes));
      // for (const tag of affixTags) {
      //   if (tag.name) {
      //     this.$store.dispatch("tagsView/addVisitedView", tag);
      //   }
      // }
    },
    addTags() {
      const { name } = this.$route
      if (name) {
        // this.$store.dispatch("tagsView/addView", this.$route);
      }
      return false
    },
    moveToCurrentTag() {
      this.$nextTick(() => {
        const tags = this.$refs.tag || {}
        for (const tag of tags) {
          if (tag.to.path === this.$route.path) {
            // when query is different then update
            if (tag.to.fullPath !== this.$route.fullPath) {
              this.$store.dispatch('tagsView/updateVisitedView', this.$route)
            }
            break
          }
        }
      })
    },
    refreshSelectedTag(view) {
      this.$store.dispatch('tagsView/delCachedView', view).then(() => {
        const { fullPath } = view
        this.$nextTick(() => {
          this.$router.replace({
            path: '/redirect' + fullPath
          })
        })
      })
    },
    closeSelectedTag(view) {
      this.$store.dispatch('tagsView/delView', view).then(({ visitedViews }) => {
        if (this.isActive(view)) {
          this.toLastView(visitedViews, view)
        }
      })
    },
    closeOthersTags() {
      this.$router.push(this.selectedTag)
      this.$store.dispatch('tagsView/delOthersViews', this.selectedTag).then(() => {
        this.moveToCurrentTag()
      })
    },
    closeAllTags(view) {
      this.$store.dispatch('tagsView/delAllViews').then(({ visitedViews }) => {
        if (this.affixTags.some((tag) => tag.path === view.path)) {
          return
        }
        this.toLastView(visitedViews, view)
      })
    },
    toLastView(visitedViews, view) {
      const latestView = visitedViews.slice(-1)[0]
      if (latestView) {
        this.$router.push(latestView.fullPath)
      } else {
        // now the default is to redirect to the home page if there is no tags-view,
        // you can adjust it according to your needs.
        if (view.name === 'Dashboard') {
          // to reload home page
          this.$router.replace({ path: '/redirect' + view.fullPath })
        } else {
          this.$router.push('/')
        }
      }
    },
    openMenu(tag, e) {
      const menuMinWidth = 105
      const offsetLeft = this.$el.getBoundingClientRect().left
      const offsetWidth = this.$el.offsetWidth
      const maxLeft = offsetWidth - menuMinWidth
      const left = e.clientX - offsetLeft

      if (left > maxLeft) {
        this.left = maxLeft
      } else {
        this.left = left
      }

      this.top = e.clientY - 35
      this.visible = true
      this.selectedTag = tag
    },
    closeMenu() {
      this.visible = false
    },
    handleScroll() {
      this.closeMenu()
    }
  }
}
</script>

<style lang="scss" scoped>
.tags-view-container {
  white-space: nowrap;
  .tags-view-item {
    margin: 0 2px 0 0;
    display: inline-block;
    position: relative;
    cursor: pointer;
    height: 30px;
    line-height: 30px;
    left: 7px;
    // border: 1px solid #e8e8e8;
    border-radius: 4px 4px 0 0;
    color: #9a9a9a;
    background: #f0f0f0;
    padding: 0 32px 0 16px;
    font-size: 14px;
    transform: skew(-20deg);
    border-bottom: none;
    z-index: 1;
    &.active {
      background-color: #fafafa;
      color: #00469c;
      font-weight: 600;
      border-right: none;
      z-index: 10;
    }
    .right-deg {
      background-color: #fafafa;
      position: absolute;
      transform: skew(35deg);
      height: 30px;
      width: 30px;
      top: 0;
      right: -11px;
      z-index: -1;
    }
    .el-icon-close {
      position: absolute;
      top: 8px;
      right: 16px;
      color: #98aac3;
      width: 8px;
      height: 8px;
      vertical-align: 2px;
      border-radius: 50%;
      text-align: center;
    }
  }
  .contextmenu {
    margin: 0;
    background: #fff;
    z-index: 3000;
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
    li {
      margin: 0;
      padding: 7px 16px;
      cursor: pointer;
      &:hover {
        background: #eee;
      }
    }
  }
}
</style>
