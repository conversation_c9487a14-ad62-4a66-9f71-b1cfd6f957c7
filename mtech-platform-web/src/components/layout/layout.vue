<template>
  <div class="layout">
    <mt-common-layout
      ref="commonLayout"
      @emitEvent="emitEvent"
      @changeTheme="changeTheme"
      :userInfo="userInfo"
      :routeInfo="routeInfo"
      :open-i18n="true"
      :open-theme="true"
      projectName="platform"
      :definedMenuSelect="true"
      :tags-class="tagsClass"
    >
      <div class="mt-container">
        <div class="mt-main-container">
          <micro-container
            v-show="isMicroRoute"
            container-class="mainMicroContainer"
          ></micro-container>
          <router-view></router-view>
        </div>
      </div>
    </mt-common-layout>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex'
import { MicroContainer } from '@mtech-micro-frontend/vue-main'
import { getOrigin } from '@/utils/env'
import routerPermission from '@mtech/router-permission'
import router from '@/router/index'
import $API from '@/service/index'
export default {
  components: {
    MicroContainer
  },
  data() {
    return {
      tagsClass: localStorage.getItem('mt-layout-theme')
        ? localStorage.getItem('mt-layout-theme') === 'default'
          ? 'nav-content'
          : 'nav-content-dark'
        : 'nav-content',
      isMicroRoute: false,
      // userInfo: {
      //   logoUrl: require('@/assets/<EMAIL>')
      // },
      
      // routeInfo: {
      //   routesIn: [],
      //   routesSupplier: [],
      //   allowSwitch: false
      // },
      fields: {
        id: 'id',
        text: 'name',
        icon: 'icon',
        path: 'path',
        children: 'children',
        parentId: 'parentId'
      },
      masterCode: 'PLATFORM_ADMIN'
    }
  },
  mounted() {
    this.checkIsMicroRoute(this.$route)
    window.onpopstate = function(event) {
    }
  },
  watch: {
    $route(route) {
      this.checkIsMicroRoute(route)
    }
  },
  computed: {
    ...mapGetters(['lang']),
    userInfo () {
      return { ...this.$store.state.user.userInfo, logoUrl: require('@/assets/<EMAIL>') }
    },
    routeInfo () {
      return this.$store.state.menu.menu
    }
    // routeInfo() {
    //   let val = []
    //   if (this.$store.state && this.$store.state.menu && this.$store.state.menu.menu) {
    //     val = this.$store.state.menu.menu
    //   }
    //   if (val.length === 0) {
    //     val = [
    //       {
    //         id: '1',
    //         name: '首页',
    //         url: '/',
    //         parentId: '0'
    //       }
    //     ]
    //   }
    //   console.log('val:', val)
    //   return {
    //     routesIn: val,
    //     routesSupplier: [],
    //     allowSwitch: false
    //   }
    // }
  },
  created () {
    // this.getUserInfo()
    // this.getUserMenu()
  },
  methods: {
    changeTheme(e) {
      if (e === 'default') {
        this.tagsClass = 'nav-content'
      } else if (e === 'dark') {
        this.tagsClass = 'nav-content-dark'
      }
    },
    ...mapMutations({
      setUserInfo: 'user/setUserInfo'
    }),
    getUserInfo() {
      $API.common.getUserInfo().then((r) => { 
        this.userInfo = r.data
        this.setUserInfo(r.data)
      })
    },
    getUserMenu() {
      $API.common.getUserMenu().then((r) => {
        this.routeInfo = Object.assign({}, this.routeInfo, {
          routesIn: r.data.routesIn,
          routesSupplier: r.data.routesSupplier
        })
        if (this.userInfo.roleList.includes(this.masterCode)) {
          return
        }
        const permissionList = []
        this.recurssionRouter(permissionList, (this.routeInfo?.routesIn || []))
        routerPermission.routerPermissionInit({
          router,
          permissionList: [...new Set(permissionList)],
          unCheck: [],
          unPowerPath: '/platform/404'
        })
      })
        .catch(() => {
          const permissionList = []
          this.recurssionRouter(permissionList, (this.routeInfo?.routesIn || []))
          routerPermission.routerPermissionInit({
            router,
            permissionList: [...new Set(permissionList)],
            unCheck: [],
            unPowerPath: '/platform/404'
          })
        })
    },
    checkIsMicroRoute(route) {
      if (route.path.startsWith('/platform')) {
        this.isMicroRoute = false
      } else {
        this.isMicroRoute = true
      }
    },
  
    emitEvent(eventName, eventParams) {
      if (eventName === 'menuSelect') {
        // 自定义点击事件
        if (eventParams && eventParams.data) {
          let url = ''
          if (eventParams.data.url) {
            url = eventParams.data.url.trim()
          }
          if (url.indexOf('/#') === 0) {
            window.location.href = `${getOrigin('uc')}${url}`
          } else {
            this.$router.push({ path: url })
          }
        }
        return
      }

      if (eventName === 'TOGGLE_SIDEBAR') {
        // 开关侧边栏
      }

      if (eventName === 'setLocal') {
        // 切换语言
        const lang = eventParams
        this.$store.dispatch('app/setLang', lang)
        this.$i18n.locale = lang
      }
    },
    recurssionRouter(routesList, routerArry) {
      routerArry.map((item) => {
        routesList.push(item.path)
        if (item.children && item.children.length) {
          this.recurssionRouter(routesList, item.children)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .mt-top-nav .nav-logo img {
  height: 40px !important;
}
.mt-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
  position: relative;
  transition: width 0.4s;
  padding: 0 20px;
  height: 100%; 
  background: linear-gradient(rgba(250, 250, 250, 1), rgba(250, 250, 250, 1)),
    linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 1));

  .mt-main-container {
    flex: 1;
    min-height: 0;
    > div {
      height: 100%;
      /deep/ .mainMicroContainer {
        height: 100%;
        > div {
          height: 100%;
        }
      }
    }
  }
}
</style>
