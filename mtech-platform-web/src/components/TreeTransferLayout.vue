<template>
  <mt-dialog ref="dialog" header="菜单分配" :buttons="dialogButtons" @close="hide">
    <div class="content--wrap">
      <slot></slot>

      <mt-row class="transfer-content--wrap" type="flex" justify="space-between">
        <mt-col class="content-box" :span="12">
          <div class="content-box--title">
            <slot name="sourceTitle"></slot>
          </div>

          <div class="search">
            <mt-input
              v-model="sourceSearch"
              placeholder="请输入关键字，多个关键字用空格分隔"
              :showClearButton="true"
              @input="handleSourceSearch"
            >
            </mt-input>
            <div
              class="search-actions"
              v-if="sourceSearch.trim() && filteredSourceTreeViewList.length > 0"
            >
              <mt-button size="small" type="primary" @click="selectAllFilteredItems"
                >选中并添加到已分配功能</mt-button
              >
            </div>
          </div>

          <div class="content-treeview">
            <div
              v-for="sourceTreeData in displaySourceTreeViewList"
              :key="sourceTreeData.applicationName"
            >
              <template v-if="sourceTreeData.dataSource.length">
                <p class="application-name">{{ sourceTreeData.applicationName }}</p>
                <mt-treeView
                  class="tree-view--template"
                  ref="treeViewSource"
                  :fields="sourceTreeData"
                  :nodeTemplate="nodeTemplate"
                  :loadOnDemand="false"
                  :auto-check="true"
                  :show-check-box="true"
                ></mt-treeView>
              </template>
            </div>
          </div>
        </mt-col>

        <div class="content-arrow">
          <div class="content-arrow--btn" @click="addSelectedTreeData">
            <mt-icon name="MT_Right_Arrow"></mt-icon>
          </div>
          <div class="content-arrow--btn" @click="deleteSelectedTreeData">
            <mt-icon name="MT_Left_Arrow"></mt-icon>
          </div>
        </div>

        <mt-col class="content-box" :span="12">
          <div class="content-box--title">
            <slot name="targetTitle"></slot>
          </div>

          <div class="content-treeview">
            <div v-for="targetTreeData in targetTreeviewList" :key="targetTreeData.applicationName">
              <!-- <template v-if="targetTreeData.dataSource.length"> -->
              <p class="application-name">{{ targetTreeData.applicationName }}</p>
              <mt-treeView
                class="tree-view--template"
                :ref="`treeViewTarget`"
                :nodeTemplate="nodeTemplate"
                :fields="targetTreeData"
                :auto-check="true"
                :show-check-box="true"
              ></mt-treeView>
              <!-- </template>              -->
            </div>
          </div>
        </mt-col>
      </mt-row>
    </div>
  </mt-dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from '@mtech/vue-property-decorator'

interface ITreeFieldAll {
  id: string
  text: string
  child: string
  parentID: string
  hasChildren: string
  applicationId: string
  applicationName: string
}

type ITreeField = Partial<ITreeFieldAll> & {
  id: string
  text: string
}

interface ITreeviewData extends ITreeField {
  dataSource: any[]
}

interface IParamPermission {
  applicationId: string
  applicationName: string
  parentPermissionId?: string
  permissionId: string
  permissionName: string
  permissionType: string
  permissionCode: string
  hasChildren?: boolean
  isChecked?: boolean | string
  versionId?: string
  businessType?: number
}

interface IApplicationData {
  applicationId: string
  applicationName: string
}

@Component({
  components: {}
})
export default class TreeTransferLayout extends Vue {
  @Prop()
  value!: boolean

  @Prop()
  id!: string

  @Prop()
  readonly!: boolean

  @Prop({
    required: true
  })
  sourceField!: ITreeField

  @Prop({
    required: true
  })
  targentField!: ITreeField

  @Prop()
  sourceDataTree!: any[]

  @Prop()
  sourceDataList!: any[]

  @Prop()
  targetDataList!: any[]

  @Prop()
  applicationData!: IApplicationData

  @Prop({
    default: false
  })
  isApprove!: boolean

  sourceTreeViewList: ITreeviewData[] = []
  filteredSourceTreeViewList: ITreeviewData[] = [] // 过滤后的源树数据
  sourceSearch: string = '' // 搜索关键字
  searchTimer: any = null // 防抖定时器
  isUpdating: boolean = false // 防止循环更新的标志

  targetTreeviewList: ITreeviewData[] = [] // 目标树的集合
  currApplication: IApplicationData = {} as IApplicationData
  parentIds: Array<string> = []
  buttons = [
    {
      click: this.hide,
      buttonModel: { content: '取消' }
    },
    {
      click: this.handleConfirm,
      buttonModel: { isPrimary: 'true', content: '确定' }
    }
  ]

  approveButtons = [
    {
      click: this.hide,
      buttonModel: { content: '取消' }
    },
    {
      click: this.handleReject,
      buttonModel: { content: '驳回' }
    },
    {
      click: this.handleConfirm,
      buttonModel: { isPrimary: 'true', content: '通过' }
    }
  ]

  nodeTemplate = function () {
    return {
      template: Vue.component('common', {
        template: `<div class="action-boxs">
                      <div>
                        <span>({{data.permissionCode}})</span>
                        <span>{{data.name || data.permissionName}}</span>
                        <span class="tree-node-style2" v-if="data.businessType == 1">采</span>
                        <span class="tree-node-style3" v-else-if="data.businessType == 2">供</span>
                      </div>
                    </div>`,

        data() {
          return { data: {} }
        }
      })
    }
  }

  get visible() {
    return this.value
  }

  set visible(v: boolean) {
    this.$emit('input', v)
  }

  get dialogButtons() {
    if (this.isApprove) {
      return this.approveButtons
    } else {
      return this.buttons
    }
  }

  // 显示的源树数据（根据搜索关键字过滤）
  get displaySourceTreeViewList() {
    if (!this.sourceSearch.trim()) {
      return this.sourceTreeViewList
    }
    return this.filteredSourceTreeViewList
  }

  get targetTreeViewData() {
    return {
      applicationId: 'applicationId',
      applicationName: 'applicationName',
      parentID: 'parentId',
      permissionType: 'permissionType',
      ...this.targentField,
      dataSource: []
    }
  }

  get sourceTreeViewData() {
    return {
      applicationId: 'applicationId',
      applicationName: 'applicationName',
      parentID: 'parentId',
      permissionType: 'permissionTypeCode',
      permissionCode: 'permissionCode',
      ...this.sourceField,
      dataSource: []
    }
  }

  @Watch('visible', { immediate: true })
  onWatchVisible(val: boolean) {
    if (val) {
      this.show()
    } else {
      this.hide()
    }
  }

  @Watch('sourceDataTree')
  onWatchSourceDataTree(val: any[]) {
    if (this.isUpdating) return
    this.isUpdating = true

    // 简单清理搜索状态
    this.sourceSearch = ''
    this.filteredSourceTreeViewList = []

    this.$nextTick(() => {
      this.checkSourceTreeByTarget(val, 1)
      this.isUpdating = false
    })
  }

  @Watch('sourceDataList')
  onWatchSourceDataList(val: any[]) {
    if (this.isUpdating) return
    this.isUpdating = true

    // 简单清理搜索状态
    this.sourceSearch = ''
    this.filteredSourceTreeViewList = []

    this.$nextTick(() => {
      this.checkSourceTreeByTarget(val, 2)
      this.isUpdating = false
    })
  }

  @Watch('targetDataList')
  onWatchTargetDataList(val: any[]) {
    if (this.isUpdating) return
    this.targetTreeviewList = this.initTreeviewList(
      val || [],
      this.targetTreeViewData as ITreeFieldAll,
      false
    )
  }

  @Watch('applicationData')
  onWatchApplicationData() {
    this.currApplication = this.applicationData
  }

  hide() {
    const ref: any = this.$refs.dialog
    ref?.ejsRef?.hide()
    this.visible = false
  }

  // 组件销毁时清理定时器
  beforeDestroy() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
      this.searchTimer = null
    }
  }

  show() {
    this.$nextTick(() => {
      const ref: any = this.$refs.dialog
      ref.ejsRef.show()
    })
  }

  // 处理搜索输入（带防抖）
  handleSourceSearch() {
    // 清除之前的定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }

    // 设置防抖延迟
    this.searchTimer = setTimeout(() => {
      this.performSearch()
    }, 300) // 300ms 防抖延迟
  }

  // 执行搜索
  performSearch() {
    if (!this.sourceSearch.trim()) {
      this.filteredSourceTreeViewList = []
      return
    }

    // 将搜索关键字按空格分割，支持多个编码过滤
    const keywords = this.sourceSearch
      .trim()
      .split(/\s+/)
      .filter((keyword) => keyword.length > 0)

    this.filteredSourceTreeViewList = this.sourceTreeViewList
      .map((treeData) => {
        const filteredDataSource = this.filterTreeData(treeData.dataSource, keywords)
        return {
          ...treeData,
          dataSource: filteredDataSource
        }
      })
      .filter((treeData) => treeData.dataSource.length > 0)
  }

  // 递归过滤树形数据（支持多个关键字）
  private filterTreeData(data: any[], keywords: string[]): any[] {
    const filtered: any[] = []

    for (const item of data) {
      // 检查当前节点是否匹配关键字
      const isMatch = this.isNodeMatch(item, keywords)

      // 递归过滤子节点
      let filteredChildren: any[] = []
      if (item.children && item.children.length > 0) {
        filteredChildren = this.filterTreeData(item.children, keywords)
      }

      // 如果当前节点匹配或有匹配的子节点，则包含此节点
      if (isMatch || filteredChildren.length > 0) {
        const newItem = { ...item }
        if (filteredChildren.length > 0) {
          newItem.children = filteredChildren
        }
        filtered.push(newItem)
      }
    }

    return filtered
  }

  // 检查节点是否匹配搜索关键字（支持多个关键字，用空格分隔）
  private isNodeMatch(node: any, keywords: string[]): boolean {
    // 获取节点的权限名称和编码
    const permissionName = (node.name || node.permissionName || '').toLowerCase()
    const permissionCode = (node.permissionCode || '').toLowerCase()

    // 合并权限名称和编码进行搜索
    const searchText = `${permissionName} ${permissionCode}`.trim()

    // 如果只有一个关键字，直接匹配
    if (keywords.length === 1) {
      const lowerKeyword = keywords[0].toLowerCase()
      return searchText.includes(lowerKeyword)
    }

    // 多个关键字时，支持两种匹配模式：
    // 1. 如果关键字中包含编码格式（如 M_02），则使用 AND 逻辑（精确匹配）
    // 2. 否则使用 OR 逻辑（任意匹配）
    // const hasCodePattern = keywords.some((keyword) => /[A-Z]_\d+/.test(keyword))

    // 宽松匹配模式：任意关键字匹配即可
    return keywords.some((keyword) => {
      const lowerKeyword = keyword.toLowerCase()
      return searchText.includes(lowerKeyword)
    })
    // if (hasCodePattern) {
    //   // 精确匹配模式：所有关键字都必须匹配
    //   return keywords.every(keyword => {
    //     const lowerKeyword = keyword.toLowerCase()
    //     return searchText.includes(lowerKeyword)
    //   })
    // } else {
    //   // 宽松匹配模式：任意关键字匹配即可
    //   return keywords.some(keyword => {
    //     const lowerKeyword = keyword.toLowerCase()
    //     return searchText.includes(lowerKeyword)
    //   })
    // }
  }

  // 选中所有过滤后的项目并添加到已分配功能列表
  selectAllFilteredItems() {
    if (this.readonly) return

    this.$nextTick(() => {
      const refs: any = this.$refs.treeViewSource
      if (!refs || !Array.isArray(refs)) return

      refs.forEach((ref: any, index: number) => {
        const ejsInstances = ref?.ejsInstances
        if (!ejsInstances) return

        // 获取当前树的所有节点ID
        const allNodeIds = this.getAllNodeIds(
          this.filteredSourceTreeViewList[index]?.dataSource || []
        )

        // 选中所有节点
        allNodeIds.forEach((nodeId) => {
          ejsInstances.checkAll([nodeId])
        })
      })

      // 选中完成后，增量添加到已分配功能列表
      this.$nextTick(() => {
        const result = this.addSelectedTreeDataIncremental()

        // 清空搜索框和过滤结果，回到初始状态
        this.sourceSearch = ''
        this.filteredSourceTreeViewList = []

        // 提示用户操作结果
        const { addedCount, skippedCount } = result
        let message = ''
        if (addedCount > 0 && skippedCount > 0) {
          message = `成功添加 ${addedCount} 项功能，跳过 ${skippedCount} 项重复功能`
        } else if (addedCount > 0) {
          message = `成功添加 ${addedCount} 项功能到已分配列表`
        } else if (skippedCount > 0) {
          message = `所选功能已存在，跳过 ${skippedCount} 项重复功能`
        } else {
          message = '未找到可添加的功能'
        }

        this.$toast({
          type: addedCount > 0 ? 'success' : 'info',
          content: message
        })
      })
    })
  }

  // 递归获取所有节点ID
  private getAllNodeIds(data: any[]): string[] {
    const ids: string[] = []

    for (const item of data) {
      const nodeId = item.id || item[this.sourceTreeViewData.id]
      if (nodeId) {
        ids.push(nodeId)
      }

      if (item.children && item.children.length > 0) {
        ids.push(...this.getAllNodeIds(item.children))
      }
    }

    return ids
  }

  // 将原始树中已选中的数据添加到被目标树中
  addSelectedTreeData() {
    if (this.readonly) return
    this.displaySourceTreeViewList.forEach((_sourceTreeData, index: number) => {
      const ref: any = this.$refs.treeViewSource
      const ejsInstances: any = ref[index]?.ejsInstances
      const treeData = ejsInstances.getTreeData()
      if (treeData.length === 0) {
        return
      }

      if (!this.applicationData) {
        this.currApplication = {
          applicationId: treeData[0][this.sourceTreeViewData.applicationId],
          applicationName: treeData[0][this.sourceTreeViewData.applicationName]
        }
      }

      const targetTreeIndex = this.targetTreeviewList.findIndex(
        (v) => v.applicationName === this.currApplication.applicationName
      )

      const newTreeData: ITreeviewData = Object.assign({}, this.targetTreeViewData, {
        applicationId: this.currApplication.applicationId,
        applicationName: this.currApplication.applicationName,
        dataSource: this.sourceDataTree
          ? this.createTargetTreeByTree(
              treeData,
              ejsInstances,
              this.currApplication.applicationId,
              this.currApplication.applicationName
            )
          : this.createTargetTreeByList(
              treeData,
              ejsInstances,
              this.currApplication.applicationId,
              this.currApplication.applicationName
            )
      })

      if (targetTreeIndex > -1) {
        this.targetTreeviewList.splice(targetTreeIndex, 1, newTreeData)
      } else {
        this.targetTreeviewList.push(newTreeData)
      }
    })
  }

  // 增量添加选中的数据到目标树中（避免重复）
  addSelectedTreeDataIncremental(): { addedCount: number; skippedCount: number } {
    if (this.readonly) return { addedCount: 0, skippedCount: 0 }

    let totalAddedCount = 0
    let totalSkippedCount = 0

    this.displaySourceTreeViewList.forEach((_sourceTreeData, index: number) => {
      const ref: any = this.$refs.treeViewSource
      const ejsInstances: any = ref[index]?.ejsInstances
      const treeData = ejsInstances.getTreeData()
      if (treeData.length === 0) {
        return
      }

      if (!this.applicationData) {
        this.currApplication = {
          applicationId: treeData[0][this.sourceTreeViewData.applicationId],
          applicationName: treeData[0][this.sourceTreeViewData.applicationName]
        }
      }

      const targetTreeIndex = this.targetTreeviewList.findIndex(
        (v) => v.applicationName === this.currApplication.applicationName
      )

      // 获取新选中的数据
      const newSelectedData = this.sourceDataTree
        ? this.createTargetTreeByTree(
            treeData,
            ejsInstances,
            this.currApplication.applicationId,
            this.currApplication.applicationName
          )
        : this.createTargetTreeByList(
            treeData,
            ejsInstances,
            this.currApplication.applicationId,
            this.currApplication.applicationName
          )

      if (targetTreeIndex > -1) {
        // 如果目标应用已存在，进行增量合并
        const existingData = this.targetTreeviewList[targetTreeIndex].dataSource
        const mergeResult = this.mergeTreeData(existingData, newSelectedData)

        this.targetTreeviewList[targetTreeIndex].dataSource = mergeResult.mergedData
        totalAddedCount += mergeResult.addedCount
        totalSkippedCount += mergeResult.skippedCount
      } else {
        // 如果目标应用不存在，直接添加
        const newTreeData: ITreeviewData = Object.assign({}, this.targetTreeViewData, {
          applicationId: this.currApplication.applicationId,
          applicationName: this.currApplication.applicationName,
          dataSource: newSelectedData
        })
        this.targetTreeviewList.push(newTreeData)
        totalAddedCount += newSelectedData.length
      }
    })

    return { addedCount: totalAddedCount, skippedCount: totalSkippedCount }
  }

  // 合并树形数据，避免重复
  private mergeTreeData(
    existingData: any[],
    newData: any[]
  ): { mergedData: any[]; addedCount: number; skippedCount: number } {
    const merged = [...existingData]
    const existingIds = new Set(existingData.map((item) => item.permissionId))
    let addedCount = 0
    let skippedCount = 0

    newData.forEach((newItem) => {
      if (!existingIds.has(newItem.permissionId)) {
        merged.push(newItem)
        existingIds.add(newItem.permissionId)
        addedCount++
      } else {
        skippedCount++
      }
    })

    return { mergedData: merged, addedCount, skippedCount }
  }

  // 将已选中的数据删除
  deleteSelectedTreeData() {
    if (this.readonly) return
    const list: ITreeviewData[] = []
    this.targetTreeviewList.forEach((v, index: number) => {
      const ref: any = this.$refs.treeViewTarget
      const ejsInstances: any = ref[index]?.ejsInstances

      const ids = ejsInstances.getAllCheckedNodes()
      ejsInstances.removeNodes(ids)

      if (ejsInstances.getTreeData().length) {
        list.push(v)
      }

      // v.dataSource = ejsInstances.getTreeData() // 维护一把dataSource
    })
    this.targetTreeviewList = [...list]
  }

  private handleConfirm() {
    const list = this.getSelectedVersionList()

    this.$emit('save', list)
  }

  private handleReject() {
    const list = this.getSelectedVersionList()

    this.$emit('reject', list)
  }

  private getSelectedVersionList() {
    const params: any[] = []
    this.targetTreeviewList.forEach((_treeView, index) => {
      const ref: any = this.$refs.treeViewTarget
      const ejsInstances: any = ref[index]?.ejsInstances
      const list = this.formatTargetTreeList(ejsInstances.getTreeData())
      params.push(...list)
    })
    return params
  }

  // 将树状数据转为列表，作为接口参数
  private formatTargetTreeList(source: IParamPermission[]) {
    const targetList: any = []
    const list: any = source.map((v) => {
      return {
        applicationId: v.applicationId,
        applicationName: v.applicationName,
        parentPermissionId: v?.parentPermissionId || '0',
        permissionId: v.permissionId,
        permissionName: v.permissionName,
        permissionType: v.permissionType,
        permissionCode: v.permissionCode,
        versionId: this.id
      }
    })
    targetList.push(...list)
    return targetList
  }

  // 将原始权限树（树状）中被选中的数据转换为列表
  private createTargetTreeByTree(
    sourceTreeData: any[],
    ejsInstances: any,
    applicationId: string,
    applicationName: string
  ) {
    const targetTreeDataList: IParamPermission[] = []

    sourceTreeData.forEach((v) => {
      // 判断该节点是否被选中或是否可能处于 mixed 状态
      if (v.isChecked || ejsInstances.getNode(v.id).isChecked === 'mixed') {
        const targetTreeNode: IParamPermission = {
          applicationId: applicationId,
          applicationName: applicationName,
          permissionId: v.id,
          permissionName: v.name,
          permissionType: v.permissionTypeId,
          permissionCode: v.permissionCode,
          hasChildren: !!v.children,
          isChecked: v.isChecked,
          businessType: v.businessType
        }

        if (v.parentId && v.parentId !== '0') {
          targetTreeNode.parentPermissionId = v.parentId
        }
        targetTreeDataList.push(targetTreeNode)

        if (v.children) {
          const list = this.createTargetTreeByTree(
            v.children,
            ejsInstances,
            applicationId,
            applicationName
          )
          targetTreeDataList.push(...list)
        }
      }
    })

    return targetTreeDataList
  }

  // 将原始权限树（列表）中被选中的数据转换为列表
  private createTargetTreeByList(
    sourceTreeData: any[],
    ejsInstances: any,
    applicationId: string,
    applicationName: string
  ) {
    const targetTreeDataList: IParamPermission[] = []

    sourceTreeData.forEach((v) => {
      const node = ejsInstances.getNode(v[this.sourceTreeViewData.id])
      if (v.isChecked || node.isChecked === 'mixed') {
        const targetTreeNode: IParamPermission = {
          applicationId: applicationId,
          applicationName: applicationName,
          permissionId: v[this.sourceTreeViewData.id],
          permissionName: v[this.sourceTreeViewData.text],
          parentPermissionId: v[this.sourceTreeViewData.parentID],
          permissionType: v[this.sourceTreeViewData.permissionType],
          permissionCode: v[this.sourceTreeViewData.permissionCode],
          hasChildren: node.hasChildren,
          isChecked: v.isChecked
        }

        targetTreeDataList.push(targetTreeNode)
      }
    })
    return targetTreeDataList
  }

  // 还原从接口中获取到的原始数和目标树
  private initTreeviewList(
    list: IParamPermission[],
    dataTemplate: ITreeFieldAll,
    isTarget = false
  ): ITreeviewData[] {
    const targetTreeviewMap: { [key: string]: IParamPermission[] } = {}

    list.reduce((total, curr: any) => {
      if (!curr[dataTemplate.applicationId]) return total
      curr[dataTemplate.hasChildren] = true
      curr.isChecked = curr.isChecked ?? isTarget
      if (curr[dataTemplate.parentID] === '0') {
        // 根节点
        delete curr[dataTemplate.parentID]
      }

      if (total[curr[dataTemplate.applicationId]]) {
        total[curr[dataTemplate.applicationId]].push(curr)
      } else {
        total[curr[dataTemplate.applicationId]] = [curr]
      }
      return total
    }, targetTreeviewMap)

    return Object.values(targetTreeviewMap).map((v) => {
      return Object.assign({}, dataTemplate, {
        dataSource: [...v],
        applicationId: v[0].applicationId,
        applicationName: v[0].applicationName
      })
    })
  }

  // 选中原始树中已被选中的数据
  private checkSourceTreeByTarget(data: any, type: any) {
    if (!data || data.length === 0) {
      this.sourceTreeViewList = []
      return
    }

    const Data_: any[] = Object.assign([], data) // 浅拷贝，避免深拷贝的性能问题

    // 获取目标树中已选中的权限ID
    const targetList: string[] = []
    this.parentIds = [] // 重置父级ID列表

    // 简化获取目标权限ID的逻辑
    this.targetTreeviewList.forEach((targetTree) => {
      if (targetTree.dataSource && Array.isArray(targetTree.dataSource)) {
        targetTree.dataSource.forEach((item: any) => {
          if (item.permissionId) {
            targetList.push(item.permissionId)
          }
          // 收集父级权限ID
          if (item.parentPermissionId && item.parentPermissionId !== '0') {
            this.parentIds.push(item.parentPermissionId)
          }
        })
      }
    })

    // 设置源树数据结构
    if (type === 1) {
      // 树形结构
      this.checkedSource(Data_, targetList)
      this.sourceTreeViewList = [
        {
          ...this.sourceField,
          dataSource: Data_
        }
      ]
    } else {
      // 列表结构
      this.checkedSource(Data_, targetList)
      this.sourceTreeViewList = this.initTreeviewList(
        Data_ || [],
        this.sourceTreeViewData as ITreeFieldAll
      )
    }
  }

  private checkedSource(data: any, targetList: any) {
    data.forEach((item: any) => {
      // 如果是父级节点不回显 不然 没有选择的子节点也会勾选上
      const align = this.sourceField.id
      if (targetList.includes(item[align]) && !this.parentIds.includes(item[align])) {
        item.isChecked = true
      } else {
        item.isChecked = false // 确保未选中的项目状态正确
      }
      if (item.children && item.children.length) {
        this.checkedSource(item.children, targetList)
      }
    })
  }
}
</script>

<style lang="scss" scoped>
.content--wrap {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 40px 22px 0;
}

.transfer-content--wrap {
  flex: 1;
  flex-wrap: nowrap;
  height: 100%;
}

.tree-view--template {
  /deep/ .mt-tree-view {
    display: block;
  }
  /deep/ .e-treeview .e-ul {
    overflow: hidden;
  }
}
.content-box {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  display: flex;
  flex-direction: column;

  .content-box--title {
    border-bottom: 1px solid #e8e8e8;
    padding: 0 20px;
    line-height: 40px;

    > span {
      font-size: 16px;
      color: #292929;
      font-weight: 500;
    }

    .application-select {
      float: right;
      width: 100px;
    }
  }
  .content-treeview {
    overflow: auto;
  }
  .application-name {
    line-height: 40px;
    padding: 0 30px;
    color: #292929;
    font-weight: 500;
  }
}

.content-arrow {
  margin: auto;

  .content-arrow--btn {
    height: 30px;
    width: 30px;
    border: 1px solid #e8e8e8;
    line-height: 30px;
    border-radius: 50%;
    text-align: center;
    color: #98aac3;
    margin: 10px;
    cursor: pointer;
  }
}

.search {
  padding: 10px 20px;
  border-bottom: 1px solid #e8e8e8;

  .search-actions {
    margin-top: 10px;
    text-align: center;
  }
}
</style>
