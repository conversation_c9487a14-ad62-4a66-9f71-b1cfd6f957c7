declare module '@mtech-ui/mtech-ui'  
declare module '@mtech-ui/base'

declare module '@mtech-common/utils' {
  import MAPI, { utils } from '@mtech-common/utils'
  export const utils = utils
  export default MAPI
} 
declare module '@mtech/mtech-common-layout' {
  import MtCommonLayout from '@mtech/mtech-common-layout'
  export default MtCommonLayout
} 
declare module '@digis/internationalization' {
  import 'vue-i18n'
  import indexDB from '@digis/internationalization'
  export default indexDB
}

declare module '@/components/Dialog/index.js'

declare module '@mtech/common-tree-view'
declare module '@mtech/side-menu'
declare module '@mtech/common-horizontal-list'  
declare module '@mtech-sso/single-sign-on'
declare module '@mtech/common-template-page'
declare module '@digis/multilingual-input'
declare module '*.vue' { 
  import Vue from 'vue' 
  export default Vue
}
