
// declare module '*.vue' {   
//   import Vue from 'vue'
//   export default Vue
// }

import Cookies from 'js-cookie'
// import Vue from 'vue'
import VueRouter, { Route } from 'vue-router'
import { Store } from 'vuex' 

declare module 'vue/types/vue' {
  interface Vue {
    $router: VueRouter;
    $route: Route;
    $store: Store<any>;
    // 以下是在main.ts中挂载到Vue.prototype上的变量
    $api: any;
    $cookies: Cookies,
    $tips: any,
    $load: any,
    $preview: any,
    $confirm: any,
    $toast: any,
    $dialog: any
  }
}
