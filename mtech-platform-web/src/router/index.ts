import Vue from 'vue'
import VueRouter, { RouteConfig } from 'vue-router'   
import Layout from '@/components/layout/layout.vue'

Vue.use(VueRouter)

const routerTemplate = {
  template: `
    <router-view></router-view>
  `
}
const routes2: Array<RouteConfig> = [ 
  {
    path: '/version', 
    name: 'version',
    component: () => import('@/views/platformMenuManage/version.vue') // 版本维护 
  }, 
  {
    path: 'version',
    name: 'version',
    meta: { name: '平台菜单' },
    component: () => import('@/views/platformMenuManage/version.vue') // 平台菜单
  },
  {
    path: 'templateSet',
    name: 'templateSet',
    meta: { menuPath: '/platform/templateSet', name: '模板配置' },
    component: () => import('@/views/trendForm/templateSet/index.vue') // 模板配置
  },
  {
    path: 'trendFormSet',
    name: 'trendFormSet',
    meta: { menuPath: '/platform/trendFormSet', name: '动态表单配置' },
    component: () => import('@/views/trendForm/trendFormSet/index.vue') // 动态表单配置
  },
  {
    // 规则设置
    path: 'ruleSet',
    name: 'ruleSet',
    meta: { menuPath: '/platform/ruleSet', name: '规则设置' },
    component: () => import('@/views/ruleEng/ruleSet.vue')
  },
  {
    path: 'editRule/:id',
    name: 'editRule',
    component: () => import('@/views/ruleEng/editRule.vue'),
    meta: {
      name: '编辑规则',
      parent: {
        path: '/editRule',
        meta: { name: '规则设置' }
      }
    }
  },
  {
    // 对象设置
    path: 'ruleEng/objectSet',
    name: 'objectSet',
    component: () => import('@/views/ruleEng/objectSet.vue'),
    meta: { name: '对象设置' }
  },
  {
    // 对象详情
    path: 'ruleEng/objectDetails/:id/:modelName/:createTime/:updateTime',
    name: 'objectDetails',
    component: () => import('@/views/ruleEng/objectDetails.vue'),
    meta: { name: '对象字段' }
  },
  {
    // 方法列表
    path: 'ruleEng/methodList',
    name: 'methodList',
    component: () => import('@/views/ruleEng/methodList.vue'),
    meta: { name: '方法列表' }
  },
  {
    // 项目列表
    path: 'ruleEng/projectList',
    name: 'projectList',
    component: () => import('@/views/ruleEng/projectList.vue'),
    meta: { name: '项目列表' }
  },
  {
    path: 'fieldSet',
    name: 'fieldSet',
    meta: { name: '字段配置' },
    component: () => import('@/views/trendForm/fieldSet/index.vue') // 字段配置
  },
  {
    path: 'menuconfig', 
    name: 'menuconfig',
    meta: { name: '版本维护' },
    component: () => import('@/views/platformMenuManage/platmenu.vue') // 版本维护 
  }, 
  // {
  //   path: 'permission',
  //   name: 'Permission',
  //   meta: { name: '平台权限管理' },
  //   component: () => import('@/views/platformPermission/index.vue') // 平台权限管理
  // },
  // {
  //   path: 'expert',
  //   name: 'Expert',
  //   meta: { name: '平台专家管理' },
  //   component: () => import('@/views/platformExpert/index.vue') // 平台专家管理
  // },

  {
    path: 'tenantManage',
    name: 'TenantManage',
    meta: { name: '租户管理' },
    component: () => import('@/views/tenantManage/index.vue') // 租户管理
  },

  {
    path: 'userManage',
    name: 'userManage',
    meta: { name: '用户管理' },
    component: () => import('@/views/user/index.vue') // 用户管理
  },

  {
    path: 'enterpriseManage',
    name: 'enterpriseManage',
    meta: { name: '企业管理' },
    component: () => import('@/views/enterpriseManage/index.vue') // 企业管理
  }, 
   
  {
    path: 'enterpriseApprove',
    name: 'EnterpriseApprove',
    meta: { name: '企业认证' },
    component: () => import('@/views/enterpriseApprove/index.vue') // 企业认证
  },
  {
    path: 'tenantApprove',
    name: 'TenantApprove',
    meta: { name: '租户开通审核' },
    component: () => import('@/views/tenantApprove/index.vue') // 租户开通审核
  }, 
  // {
  //   path: 'employeeInvitation',
  //   name: 'EmployeeInvitation',
  //   meta: { name: '员工邀请' },
  //   component: () => import('@/views/employeeInvitation/index.vue') // 员工邀请
  // },
  // {
  //   path: 'tenantApproveDetail',
  //   name: 'tenantApproveDetail',
  //   component: () => import('@/views/tenantApprove/detail.vue') // 公司详情
  // },
  // {
  //   path: 'personal',
  //   name: 'personal',
  //   component: () => import('@/views/personal/index.vue'), // 个人中心
  //   meta: { name: '个人中心', menuPath: '/platform/personal' }    
  // },
  {
    path: 'user',
    name: 'user',
    component: () => import('@/views/user/index.vue'), // 个人中心
    meta: { name: '用户管理' }    
  },
  {
    path: 'masterUser',
    name: 'masterUser',
    component: () => import('@/views/userManage/userMasterMange.vue'), // 个人中心
    meta: { name: '平台用户管理' }    
  },
  {
    path: '404',
    component: () => import('@/views/404/index.vue')
  }
  
]
const routes: Array<RouteConfig> = [
  {
    path: '/', 
    redirect: '/middlePlatform/personal',
    name: 'Home',
    component: Layout // 版本维护 
  },
  {
    path: '/masterdata*',
    name: 'masterdata',
    meta: { title: '角色管理' }
  },
  {
    path: '/middlePlatform/*',
    name: 'middlePlatform'
  },
  {
    path: '/platform/formEdit',
    name: 'formEdit',
    meta: { name: '动态表单新增' },
    component: () => import('@/views/trendForm/components/FormEdit.vue') // 动态表单新增
  },
  {
    path: '/platform/formTemplateEdit',
    name: 'formTemplateEdit',
    meta: { name: '模板新增' },
    component: () => import('@/views/trendForm/components/FormTemplateEdit.vue') // 模板新增
  },
  {
    path: '/platform',
    component: () => import('@/views/Home.vue'),
    children: routes2
  }
]
const router = new VueRouter({
  routes
}) 

export default router
