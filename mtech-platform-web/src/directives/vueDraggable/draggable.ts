import Vue, { DirectiveOptions, VNodeDirective, VNode } from 'vue'

export type HandleType = Vue | HTMLElement;
export type MouseOrTouchEvent = MouseEvent | TouchEvent;

export interface DirectiveBinding extends Readonly<VNodeDirective> {
  readonly modifiers: { [key: string]: boolean };
}

export interface Position {
  left: number;
  top: number;
}

export interface PositionDiff {
  x: number;
  y: number;
}

export interface MarginOptions {
  top?: number;
  right?: number;
  bottom?: number;
  left?: number;
}

export interface DraggableValue { 
  copy?: boolean;
  handle?: HandleType;
  onPositionChange?: (posDiff?: PositionDiff, pos?: Position, event?: MouseOrTouchEvent) => void;
  onDragEnd?: (posDiff?: PositionDiff, pos?: Position, event?: MouseOrTouchEvent) => void;
  onDragStart?: (posDiff?: PositionDiff, pos?: Position, event?: MouseOrTouchEvent) => void;
  resetInitialPos?: boolean;
  stopDragging?: boolean;
  boundingRect?: ClientRect;
  boundingElement?: HTMLElement;
  boundingRectMargin?: MarginOptions;
  initialPosition?: Position;
}

export interface DraggableState {
  initialPosition: Position;
  startDragPosition: Position;
  currentDragPosition: Position;
  initialMousePos?: Position;
}

enum ChangePositionType {
  Start = 1,
  End,
  Move
}

function extractHandle(handle: HandleType): HTMLElement {
  return (handle && (handle as Vue).$el as HTMLElement) || handle as HTMLElement
}

function getPosWithBoundaries(elementRect: ClientRect, boundingRect: ClientRect, left: number, top: number, boundingRectMargin: MarginOptions = {}): Position {
  const adjustedPos: Position = { left, top }
  const { height, width } = elementRect
  const topRect = top
  const bottomRect = top + height
  const leftRect = left
  const rightRect = left + width
  const marginTop = boundingRectMargin.top || 0
  const marginBottom = boundingRectMargin.bottom || 0
  const marginLeft = boundingRectMargin.left || 0
  const marginRight = boundingRectMargin.right || 0
  const topBoundary = boundingRect.top + marginTop
  const bottomBoundary = boundingRect.bottom - marginBottom
  const leftBoundary = boundingRect.left + marginLeft
  const rightBoundary = boundingRect.right - marginRight
  if (topRect < topBoundary) {
    adjustedPos.top = topBoundary
  } else if (bottomRect > bottomBoundary) {
    adjustedPos.top = bottomBoundary - height
  }
  if (leftRect < leftBoundary) {
    adjustedPos.left = leftBoundary
  } else if (rightRect > rightBoundary) {
    adjustedPos.left = rightBoundary - width
  }
  return adjustedPos
}

function initDragDom(el: HTMLElement) {
  const draggableId = Date.now() + Math.random() + ''
  el.dataset.draggableId = draggableId
}

function getMovedDom(el: HTMLElement, isCopy?: boolean): HTMLElement {
  if (!isCopy) {
    return el
  }
  let bakDom = findBakDom(el) as HTMLElement

  if (bakDom) return bakDom

  const parentDom = el.parentElement
  bakDom = parentDom?.appendChild(el.cloneNode(true)) as HTMLElement
  bakDom.dataset.draggableId = el.dataset.draggableId + '-bak' 

  return bakDom
}

function removeBakDom(el: HTMLElement) {
  const parentDom = el.parentElement

  const bakDom = findBakDom(el)

  bakDom && parentDom?.removeChild(bakDom) 
}

function findBakDom(el: HTMLElement) {
  const parentDom = el.parentElement

  const bakDom = parentDom?.querySelector(`[data-draggable-id="${el.dataset.draggableId}-bak"]`)

  return bakDom
}

export const Draggable: DirectiveOptions = {
  bind(el: HTMLElement, binding: DirectiveBinding, vnode: VNode, oldVnode: VNode) {
    Draggable.update && Draggable.update(el, binding, vnode, oldVnode)
  },

  update(el: HTMLElement, binding: DirectiveBinding, vnode: VNode, oldVnode: VNode) {
    if (binding.value && binding.value.stopDragging) {
      return
    }
    initDragDom(el)

    // if (binding.value?.copy) {
    //   el = originalEl.cloneNode(true) as HTMLElement
    // } 
    const handler = (binding.value && binding.value.handle && extractHandle(binding.value.handle)) || el
    if (binding && binding.value && binding.value.resetInitialPos) {
      initializeState()
      handlePositionChanged()
    }
    if (!handler.getAttribute('draggable')) {
      el.removeEventListener('mousedown', (el as any).listener)
      handler.addEventListener('mousedown', moveStart)
      el.removeEventListener('touchstart', (el as any).listener)
      handler.addEventListener('touchstart', moveStart, { passive: false })
      handler.setAttribute('draggable', 'true');
      (el as any).listener = moveStart
      initializeState()
      handlePositionChanged()
    }

    function move(event: MouseOrTouchEvent) {
      event.preventDefault()

      const stopDragging = binding.value && binding.value.stopDragging
      if (stopDragging) {
        return
      }
      
      let state = getState()
      if (!state.startDragPosition || !state.initialMousePos) {
        initializeState(event)
        state = getState()
      }

      const pos = getInitialMousePosition(event)
      if (!pos || !state.initialMousePos) return 
      const dx = pos.left - state.initialMousePos.left
      const dy = pos.top - state.initialMousePos.top

      let currentDragPosition = {
        left: state.startDragPosition.left + dx,
        top: state.startDragPosition.top + dy
      }

      const boundingRect = getBoundingRect()
      const elementRect = el.getBoundingClientRect()

      if (boundingRect && elementRect) {
        currentDragPosition = getPosWithBoundaries(
          elementRect,
          boundingRect,
          currentDragPosition.left,
          currentDragPosition.top,
          binding.value.boundingRectMargin
        )
      }

      setState({ currentDragPosition })
      updateElementStyle()
      handlePositionChanged(event)
    }

    function getBoundingRect(): ClientRect | undefined {
      if (!binding.value) {
        return
      }

      return (binding.value.boundingRect ||
        binding.value.boundingElement) &&
        binding.value.boundingElement.getBoundingClientRect()
    }

    function updateElementStyle(): void {
      const state = getState()
      if (!state.currentDragPosition) {
        return
      }

      const dom = getMovedDom(el, binding.value.copy)

      if (binding.value.copy) {
        dom.style.opacity = '0.5'
      }
      dom.style.touchAction = 'none'
      dom.style.position = 'fixed'
      dom.style.left = `${state.currentDragPosition.left}px`
      dom.style.top = `${state.currentDragPosition.top}px`
    }

    function moveEnd(event: MouseOrTouchEvent) {
      event.preventDefault()
      document.removeEventListener('mousemove', move)
      document.removeEventListener('mouseup', moveEnd)
      document.removeEventListener('touchmove', move)
      document.removeEventListener('touchend', moveEnd)

      const currentRectPosition = getRectPosition()
      setState({
        initialMousePos: undefined,
        startDragPosition: currentRectPosition,
        currentDragPosition: currentRectPosition
      })

      handlePositionChanged(event, ChangePositionType.End)

      if (!binding.value.copy) { 
        // removeBakDom(el)
        updateElementStyle()
      } 
    } 

    function moveStart(event: MouseOrTouchEvent) {
      setState({ initialMousePos: getInitialMousePosition(event) })
      handlePositionChanged(event, ChangePositionType.Start)
      document.addEventListener('mousemove', move)
      document.addEventListener('mouseup', moveEnd)
      document.addEventListener('touchmove', move)
      document.addEventListener('touchend', moveEnd)
    }

    function getInitialMousePosition(event?: MouseOrTouchEvent): Position | undefined {
      if (event instanceof MouseEvent) {
        return {
          left: event.clientX,
          top: event.clientY
        }
      }
      if (event instanceof TouchEvent) {
        const touch = event.changedTouches[event.changedTouches.length - 1]
        return {
          left: touch.clientX,
          top: touch.clientY
        }
      }
    }

    function getRectPosition(): Position | undefined {
      const clientRect = el.getBoundingClientRect()
      if (!clientRect.height || !clientRect.width) {
        return
      }
      return { left: clientRect.left, top: clientRect.top }
    }

    function initializeState(event?: MouseOrTouchEvent): void {
      const state = getState()
      const initialRectPositionFromBinding = binding && binding.value && binding.value.initialPosition
      const initialRectPositionFromState = state.initialPosition
      const startingDragPosition = getRectPosition()
      const initialPosition = initialRectPositionFromBinding || initialRectPositionFromState || startingDragPosition

      setState({
        initialPosition: initialPosition,
        startDragPosition: initialPosition,
        currentDragPosition: initialPosition,
        initialMousePos: getInitialMousePosition(event)
      })
      updateElementStyle()
    }

    function setState(partialState: Partial<DraggableState>) {
      const prevState = getState()
      const state = {
        ...prevState,
        ...partialState
      }
      handler.setAttribute('draggable-state', JSON.stringify(state))
    }

    function handlePositionChanged(event?: MouseOrTouchEvent, changePositionType?: ChangePositionType) {
      const state = getState()
      const posDiff: PositionDiff = { x: 0, y: 0 }
      if (state.currentDragPosition && state.startDragPosition) {
        posDiff.x = state.currentDragPosition.left - state.startDragPosition.left
        posDiff.y = state.currentDragPosition.top - state.startDragPosition.top
      }
      const currentPosition = state.currentDragPosition && { ...state.currentDragPosition }

      if (changePositionType === ChangePositionType.End) {
        binding.value && binding.value.onDragEnd && state && binding.value.onDragEnd(posDiff, currentPosition, event)
      } else if (changePositionType === ChangePositionType.Start) {
        binding.value && binding.value.onDragStart && state && binding.value.onDragStart(posDiff, currentPosition, event)
      } else {
        binding.value && binding.value.onPositionChange && state && binding.value.onPositionChange(posDiff, currentPosition, event)
      }
    }

    function getState(): DraggableState {
      return JSON.parse(handler.getAttribute('draggable-state') as string) || {}
    }
  }
}
