@import './common.scss';
// @import '../assets/aliIcon/iconfont.css';

//scrollBar-color
$scrollBar-track-color: rgb(235,235,235);
$scrollBar-thumb-color: rgb(210,210,210);
$scrollBar-thumb-hover-color: rgb(200,200,200);
$scrollBar-thumb-active-color: rgb(190,190,190);

.tree-node-style{
    height: 16px;
    border-radius: 2px;
    color: #FFFFFF;
    padding: 0 5px;
    font-size: 10px;
    font-family: PingFangSC;
    font-weight: normal;
}
.tree-node-style2{
    width: 14px;
    height: 14px;
    background: #EAEDF3;
    color: #6386C1;
    border-radius: 2px;
    font-size: 10px;
    font-family: PingFangSC;
    font-weight: 500;
    padding: 2px;
    margin-left:5px;
}
.tree-node-style3{
    width: 14px;
    height: 14px;
    background: #F8F0E5;
    color: #EDA133;
    border-radius: 2px;
    font-size: 10px;
    font-family: PingFangSC;
    font-weight: 500;
    padding: 2px;
    margin-left:5px;
}
.small-dialog {
    width: 500px!important;
    height: 300px!important;
    .e-dlg-header-content {
      // background: #fff;
      .e-icon-btn .e-btn-icon {
        color: #9DAABF;
      }
      .e-dlg-header {
        // color: #292929;
        display: flex;
        align-items: center;
        i {
          color: #EDA133;
        }
        .header-text {
          margin-left: 10px;
          color: #292929;
        }
      }
    }
  }

.dialog-form-flex .e-dlg-content {
  padding: 40px !important;
  .mt-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 390px;
      .mt-form-item__content {
        width: 100%;
      }
    }
  }
}

.isActiveClass{
  width: 44px;
  height: 20px;
  background: rgba(245,246,248,1);
  border-radius: 2px;
  padding: 3px;
  color: #9BAAC1;
}
.isActiveClassFalse{
  width: 32px;
  height: 20px;
  background: rgba(238,242,249,1);
  border-radius: 2px;
  padding: 3px;
  color: #6386C1;
}
  
//修改谷歌内核浏览器滚动条样式
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

::-webkit-scrollbar-track {
  background-color: $scrollBar-track-color;
}

::-webkit-scrollbar-thumb {
  background-color: $scrollBar-thumb-color;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: $scrollBar-thumb-hover-color;
}

::-webkit-scrollbar-thumb:active {
  background-color: $scrollBar-thumb-active-color;
}


.router-page--wrap {
  height: 100%;
}