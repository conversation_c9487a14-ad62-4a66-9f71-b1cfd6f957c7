import api from '@/service' 
import MenuData from '../mock/menu.json'

const state = {
  menu: [],
  menuExpand: []
}

const getters = {
  menu: (state: any) => state.menu,
  menuExpand: (state: any) => state.menuExpand
}

const mutations = {
  setMenu(state: any, payload: any[]) {
    state.menu = payload
  },
  setMenuExpand(state: any, payload: any[]) {
    state.menuExpand = payload
  }
}

const actions = {
  getMenu: async ({ commit }: any, payload: any[]) => {
    const menuExpend: any[] = []
    const menuData: any[] = []
    if (state.menu && state.menu.length <= 0) {
      try {
        // const { code, data } = await api.common.menuTree()
        const { code, data } = MenuData 
        if (code === 200 && data && data.length) {
        // 再存一个菜单数组，所有的路由，包括所有child
          expendMenu(data) 
          commit('setMenu', menuData)
          commit('setMenuExpand', menuExpend)
        }
      } catch (err) {
        return err
      }
    }

    // 获取展开的菜单
    function expendMenu(menu: any[]) { 
      menu.forEach((item: any) => {
        if (item.data && item.data.url && item.parentId === '0') {
          menuData.push({
            ...item,
            ...item.data
          })
          menuExpend.push(item.data.url)
        }
        if (item.children && item.children.length) {
          expendMenu(item.children)
        }
      })
    }
  },
  setMenu: ({ commit }: any, payload: any[]) => {
    commit('setMenu', payload)
  },
  setMenuExpand: ({ commit }: any, payload: any[]) => {
    commit('setMenuExpand', payload)
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
