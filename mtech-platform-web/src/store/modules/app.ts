import Cookies from 'js-cookie'

const state = {
  sidebar: {
    opened: Cookies.get('sidebarStatus') ? Number(Cookies.get('sidebarStatus')) : true,
    withoutAnimation: false
  },
  token: Cookies.get('token') || '',
  lang: Cookies.get('lang') || 'zh-CN'
}

const mutations = {
  TOGGLE_SIDEBAR: (state: { sidebar: { opened: boolean; withoutAnimation: boolean } }) => {
    state.sidebar.opened = !state.sidebar.opened
    state.sidebar.withoutAnimation = false
    if (state.sidebar.opened) {
      Cookies.set('sidebarStatus', '1')
    } else {
      Cookies.set('sidebarStatus', '0')
    }
  },
  CLOSE_SIDEBAR: (state: { sidebar: { opened: boolean; withoutAnimation: any } }, withoutAnimation: any) => {
    Cookies.set('sidebarStatus', '0')
    state.sidebar.opened = false
    state.sidebar.withoutAnimation = withoutAnimation
  },
  OPEN_SIDEBAR: (state: { sidebar: { opened: boolean; withoutAnimation: any } }, withoutAnimation: any) => {
    Cookies.set('sidebarStatus', '1')
    state.sidebar.opened = true
    state.sidebar.withoutAnimation = withoutAnimation
  },
  SET_TOKEN: (_state: any, payload: { token: any }) => {
    Cookies.set('token', payload.token)
  },
  REMOVE_TOKEN: () => {
    Cookies.set('token', '')
  },
  SET_LANG: (state: { lang: any }, playload: any) => {
    Cookies.set('lang', playload)
    state.lang = playload
  }
}

const actions = {
  setToken({ commit }:any) {
    commit('SET_TOKEN')
  },
  removeToken({ commit }:any) {
    commit('REMOVE_TOKEN')
  },
  toggleSideBar({ commit }:any) {
    commit('TOGGLE_SIDEBAR')
  },
  closeSideBar({ commit }:any, { withoutAnimation }:any) {
    commit('CLOSE_SIDEBAR', withoutAnimation)
  },
  setLang({ commit }: any, playload: any) {
    commit('SET_LANG', playload)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
