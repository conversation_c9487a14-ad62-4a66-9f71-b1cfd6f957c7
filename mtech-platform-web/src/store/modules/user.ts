const state = () => ({
  token: '',
  userInfo: {}
})
const getters = {
  token: (state: any) => state.token,
  userInfo: (state: any) => state.userInfo 
}

const mutations = {
  SET_TOKEN(state: any, payload: string) {
    state.token = payload
  },
  setUserInfo(state: any, payload: any) {
    state.userInfo = payload
  }
}

const actions = {
  loginAction({ commit }: any, token: any): void {
    commit('SET_TOKEN', token)
  },

  logoutAction({ commit }: any, app: any): void {
    commit('SET_TOKEN', '')
    commit('setUserInfo', {})
    app.$cookies.remove('token')
  },

  setUserInfo: ({ commit }: any, payload: any) => {
    commit('setUserInfo', payload)
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
