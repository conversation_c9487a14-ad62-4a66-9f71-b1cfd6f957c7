const state = () => ({
  commonTenantList: []
})

const getters = {
  commonTenantList: (state: any) => state.commonTenantList
}

const mutations = {
  // eslint-disable-next-line @typescript-eslint/ban-types
  setCommonTenantList(state: any, payload: object[]) {
    state.commonTenantList = payload
  }
}

const actions = {
  // eslint-disable-next-line @typescript-eslint/ban-types
  setCommonTenantList: ({ commit }: any, payload: object[]) => {
    commit('setCommonTenantList', payload)
  },

  clearCommonTenantList: ({ commit }: any) => {
    commit('setCommonTenantList', [])
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
