{"code": 200, "msg": "执行成功", "data": [{"id": "13023193473175596", "parentId": "0", "weight": 1, "name": "版本维护", "data": {"id": "13023193473175596", "menuName": "版本维护", "url": "/platform/version", "menuType": {"value": "page", "label": "页面"}, "pid": "0", "appGroup": "platform", "serviceUrls": [], "icon": "icon_smooth_Platformmanagement", "menuId": "13023193473175596", "tenantId": "0", "menuUrl": null, "appId": "0", "orderNum": 1}, "children": []}, {"id": "13023193473175597", "parentId": "0", "weight": 1, "name": "菜单配置", "data": {"id": "13023193473175597", "menuName": "菜单配置", "url": "/platform/detail", "menuType": {"value": "page", "label": "页面"}, "pid": "0", "appGroup": "platform", "serviceUrls": [], "icon": "icon_smooth_Platformmanagement", "menuId": "13023193473175597", "tenantId": "0", "menuUrl": null, "appId": "0", "orderNum": 1}, "children": []}, {"id": "13023193473175598", "parentId": "0", "weight": 1, "name": "模板配置", "data": {"id": "13023193473175598", "menuName": "模板配置", "url": "/platform/templateSet", "menuType": {"value": "page", "label": "页面"}, "pid": "0", "appGroup": "platform", "serviceUrls": [], "icon": "icon_smooth_Platformmanagement", "menuId": "13023193473175598", "tenantId": "0", "menuUrl": null, "appId": "0", "orderNum": 1}, "children": []}, {"id": "130231934731275599", "parentId": "0", "weight": 1, "name": "动态表单配置", "data": {"id": "130231934731275599", "menuName": "模板配置", "url": "/platform/trendFormSet", "menuType": {"value": "page", "label": "页面"}, "pid": "0", "appGroup": "platform", "serviceUrls": [], "icon": "icon_smooth_Platformmanagement", "menuId": "130231934731275599", "tenantId": "0", "menuUrl": null, "appId": "0", "orderNum": 1}, "children": []}, {"id": "130231934731275100", "parentId": "0", "weight": 1, "name": "字段配置", "data": {"id": "130231934731275100", "menuName": "模板配置", "url": "/platform/fieldSet", "menuType": {"value": "page", "label": "页面"}, "pid": "0", "appGroup": "platform", "serviceUrls": [], "icon": "icon_smooth_Platformmanagement", "menuId": "130231934731275100", "tenantId": "0", "menuUrl": null, "appId": "0", "orderNum": 1}, "children": []}, {"id": "*****************", "parentId": "0", "weight": 2, "name": "平台权限管理", "data": {"id": "*****************", "menuName": "平台权限管理", "url": "/platform/permission", "menuType": {"value": "page", "label": "页面"}, "pid": "0", "appGroup": "platform", "serviceUrls": [], "icon": "icon_Account", "menuId": "*****************", "tenantId": "0", "menuUrl": null, "appId": "0", "orderNum": 2}}, {"id": "10", "parentId": "0", "weight": 3, "name": "租户管理", "data": {"id": "10", "menuName": "租户管理", "url": "/platform/tenantManage", "menuType": {"value": "page", "label": "页面"}, "pid": "0", "appGroup": "platform", "serviceUrls": [], "icon": "icon_Application", "menuId": "10", "tenantId": "0", "menuUrl": null, "appId": "0", "orderNum": 3}}, {"id": "1013213", "parentId": "0", "weight": 3, "name": "用户管理", "data": {"id": "1013213", "menuName": "用户管理", "url": "/platform/userManage", "menuType": {"value": "page", "label": "页面"}, "pid": "0", "appGroup": "platform", "serviceUrls": [], "icon": "icon_Application", "menuId": "1013213", "tenantId": "0", "menuUrl": null, "appId": "0", "orderNum": 3}}, {"id": "**********", "parentId": "0", "weight": 3, "name": "企业管理", "data": {"id": "**********", "menuName": "企业管理", "url": "/platform/enterpriseManage", "menuType": {"value": "page", "label": "页面"}, "pid": "0", "appGroup": "platform", "serviceUrls": [], "icon": "icon_Application", "menuId": "**********", "tenantId": "0", "menuUrl": null, "appId": "0", "orderNum": 3}}, {"id": "10587900270268496", "parentId": "0", "weight": 4, "name": "企业认证", "data": {"id": "10587900270268496", "menuName": "企业认证", "url": "/platform/enterpriseApprove", "menuType": {"value": "page", "label": "页面"}, "pid": "0", "appGroup": "platform", "serviceUrls": [], "icon": "icon_Grouping", "menuId": "10587900270268496", "tenantId": "0", "menuUrl": null, "appId": "0", "orderNum": 4}}, {"id": "13083386978455574", "parentId": "0", "weight": 7, "name": "注册企业", "icon": "icon_smooth_Purchaseorder", "data": {"id": "", "menuName": "注册企业", "url": "/platform/#/createTenant", "menuType": {"value": "page", "label": "页面"}, "pid": "0", "appGroup": "platform", "serviceUrls": [], "icon": "icon_smooth_Purchaseorder", "menuId": "13083386978455500", "tenantId": "0", "menuUrl": null, "appId": "0", "orderNum": 7}}, {"id": "13083386978455574", "parentId": "0", "weight": 7, "name": "加入企业", "icon": "icon_Engine", "data": {"id": "13083386978455501", "menuName": "加入企业", "url": "/platform/#/addTenant", "menuType": {"value": "page", "label": "页面"}, "pid": "0", "appGroup": "platform", "serviceUrls": [], "icon": "icon_Engine", "menuId": "13083386978455501", "tenantId": "0", "menuUrl": null, "appId": "0", "orderNum": 7}}, {"id": "13083386978455574", "parentId": "0", "weight": 7, "name": "工作流模板", "icon": "icon_smooth_Workflow", "data": {"id": "13083386978455574", "menuName": "工作流模板", "url": "/platform/workflow", "menuType": {"value": "page", "label": "页面"}, "pid": "0", "appGroup": "platform", "serviceUrls": [], "icon": "icon_smooth_Workflow", "menuId": "13083386978455574", "tenantId": "0", "menuUrl": null, "appId": "0", "orderNum": 7}}, {"id": "13083386978455575", "parentId": "0", "weight": 7, "name": "工作流流程", "icon": "icon_smooth_Workflow", "data": {"id": "13083386978455575", "menuName": "工作流流程", "url": "/platform/flowinstance", "menuType": {"value": "page", "label": "页面"}, "pid": "0", "appGroup": "platform", "serviceUrls": [], "icon": "icon_smooth_Workflow", "menuId": "13083386978455575", "tenantId": "0", "menuUrl": null, "appId": "0", "orderNum": 7}}, {"id": "13083386978455576", "parentId": "0", "weight": 7, "name": "工作流服务", "icon": "icon_smooth_Workflow", "data": {"id": "13083386978455576", "menuName": "工作流服务", "url": "/platform/workflowserve", "menuType": {"value": "page", "label": "页面"}, "pid": "0", "appGroup": "platform", "serviceUrls": [], "icon": "icon_smooth_Workflow", "menuId": "13083386978455576", "tenantId": "0", "menuUrl": null, "appId": "0", "orderNum": 7}}, {"id": "13083386978455577", "parentId": "0", "weight": 7, "name": "接口管理", "icon": "icon_smooth_Workflow", "data": {"id": "13083386978455577", "menuName": "接口管理", "url": "/platform/workflowinterface", "menuType": {"value": "page", "label": "页面"}, "pid": "0", "appGroup": "platform", "serviceUrls": [], "icon": "icon_smooth_Workflow", "menuId": "13083386978455577", "tenantId": "0", "menuUrl": null, "appId": "0", "orderNum": 7}}, {"id": "13083386978455577", "parentId": "0", "weight": 7, "name": "个人中心", "icon": "icon_smooth_Workflow", "data": {"id": "13083386978455578", "menuName": "个人中心", "url": "/platform/personal", "menuType": {"value": "page", "label": "页面"}, "pid": "0", "appGroup": "platform", "serviceUrls": [], "icon": "icon_smooth_Workflow", "menuId": "13083386978455578", "tenantId": "0", "menuUrl": null, "appId": "0", "orderNum": 8}}]}