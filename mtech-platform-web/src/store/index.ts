/* eslint-disable @typescript-eslint/ban-types */

import Vue from 'vue'
import Vuex from 'vuex'
import user from './modules/user'
import router from './modules/router'
import tenant from './modules/tenant'
import menu from './modules/menu'
import tagsView from './modules/tagsView'
import app from './modules/app'

Vue.use(Vuex)

let domain = '.qeweb.com'
if (process.env.NODE_ENV === 'development') {
  domain = 'localhost'
}

const state = {
  application: {},
  countryList: [
    {
      id: 1,
      code: '0086',
      text: '中国 0086',
      value: 1,
      length: 11,
      maxLength: 11,
      minLength: 11
    }
  ],
  system: ''
}
const getters = {
  getApplication: (state: any) => state.application,
  getCountryList: (state: any) => state.countryList,
  system: (state: any) => state.userInfo,
  visitedViews: (state: any) => state.tagsView.visitedViews,
  cachedViews: (state: any) => state.tagsView.cachedViews,
  sidebar: (state: any) => state.app.sidebar,
  isPlatformAdmin: (state: any) => state.user.userInfo.tenantId === '-99'
}

const mutations = {
  setApplication(state: any, playload: object) {
    state.application = playload
  },
  setCountryList(state: any, playload: object[]) {
    state.countryList = playload
  },
  setSystem(state: any, payload: object) {
    state.system = payload
  }
}

const actions = {
  nuxtServerInit({ commit }: any, { app }: any) {
    const token: string = app.$cookies.get('token')
    if (token) {
      commit('user/SET_TOKEN', token)
    }
  },
  setApplication: ({ commit }: any, playload: object) => {
    commit('setApplication', playload)
  },
  setCountryList: ({ commit }: any, playload: object[]) => {
    commit('setCountryList', playload)
  },
  setSystem: ({ commit }: any, { system, app }: any) => {
    commit('setSystem', system)
    app.$cookies.set('system', JSON.stringify(system), { domain, path: '/' })
  }
}

export const modules = {
  user,
  router,
  tenant,
  menu,
  app,
  tagsView
}

export default new Vuex.Store({
  state,
  getters,
  mutations,
  actions,
  modules: modules
}) 
