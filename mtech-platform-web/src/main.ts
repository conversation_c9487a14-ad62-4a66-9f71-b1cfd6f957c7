import Vue from 'vue'
import App from './App.vue'
import './sso'
import router from './router'
import store from '@/store'
import api from '@/service'
import Cookies from 'js-cookie'
import './styles/index.scss'
import './mtechUI'

import '@/components/svgicon/index.js'
import '@/assets/aliIcon/iconfont.js' // 菜单图标
import { MtMicroMaster, MicroLoading } from '@mtech-micro-frontend/vue-main'
import MtMicroLoading from './components/micro-loading/index.vue'
import { routerGo, genActiveRule } from './utils/routerTo'
import * as Dialog from '@/components/Dialog/index.js'
import commonPermission from '@mtech/common-permission' 
import indexDB from '@digis/internationalization'
import { LoadableApp } from 'qiankun'
import { utils } from '@mtech-common/utils'
import { setLocal } from '@mtech-ui/base'
import MtMultilingualInput from '@digis/multilingual-input'
import { permissionAsync } from './menu'

Vue.use(MtMultilingualInput)
utils.setAppCode('platform')
setLocal('zh-CN')
 
const i18n:any = indexDB.digisI18n(Vue, 'srm')
const newObj:any = Dialog
Vue.prototype[newObj.NAME] = newObj.COMPONENT

Vue.config.productionTip = false
Vue.prototype.$api = api
Vue.prototype.$cookies = Cookies
// sso({})
const loader = MicroLoading(MtMicroLoading)
// commonPermission.init({ Vue: Vue, appCode: 'platform', businessType: '', permissionTypeCode: 'opt' })
Vue.use(commonPermission)
const msg = {
  data: store.state,
  fns: [
    routerGo,
    function addVisitedViews(route: any) {
      const { name } = route
      if (name) {
        store.dispatch('tagsView/addView', route)
      }
    }
  ]
}
permissionAsync().finally(() => {
  new Vue({
    router,
    store,
    i18n,
    render: (h) => h(App)
  }).$mount('#app')
})

/**
 * Step1 注册子应用
 */

const domain = document.domain.replace('platform', 'srm')
const master = new MtMicroMaster(
  [
    {
      name: 'masterData',
      entry:
        process.env.NODE_ENV === 'production'
          ? `//${domain}/masterData/`
          : '//localhost:8083',
      loader,
      activeRule: genActiveRule('#/masterdata'),
      // activeRule: '/masterdata',
      props: msg
    },
    {
      name: 'middlePlatform',
      entry:
        process.env.NODE_ENV === 'production'
          ? `//${domain}/middlePlatform/`
          : '//localhost:8085',
      loader,
      activeRule: genActiveRule('#/middlePlatform'),
      props: msg
    } 
  ],
  {
    beforeLoad: [
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      (app: any) => {
        app.props.entry = app.entry
        console.log('[LifeCycle] before load %c%s', 'color: green;', app.name)
      }
    ],
    beforeMount: [
      // (app:object) => { LoadableApp
      //   console.log(app)
      //   console.log('[LifeCycle] before mount %c%s', 'color: green;', app.name)
      // }
    ],
    afterUnmount: [
      // (app:object) => {
      //   console.log(
      //     '[LifeCycle] after unmount %c%s',
      //     'color: green;',
      //     app.name
      //   )
      // }
    ]
  }
)

master.addGlobalUncaughtErrorHandler((event) => {
  const { msg } = event
  if (msg && msg.includes('died in status LOADING_SOURCE_CODE')) {
    console.log('微应用加载失败，请检查应用是否可运行')
  }
})

/**
 * Step2 设置默认进入的子应用
 */
// master.setDefaultMountApp("/sourcing"); //todo 需要先启动'子应用'

/**
 * Step3 启动应用
 */
master.start({
  prefetch: true,
  sandbox: {
    experimentalStyleIsolation: false
  }
})

master.runAfterFirstMounted(() => {
  console.log('[MainApp] first app mounted')
})
