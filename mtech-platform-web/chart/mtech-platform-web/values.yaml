# Default values for choerodon-front.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  #repository: registry.choerodon.com.cn/choerodon/choerodon-front
  repository: tone.tcl.com/devops/docker/snapshot/operation-srm/mtech-platform-web
  pullPolicy: Always

imagePullSecret:
  enable: true
  name: tone-docker-secret

logs:
  parser: mtech-platform-web

service:
  enabled: true
  port: 80
  type: ClusterIP
  name: mtech-platform-web

env:
  open:
    
resources: 
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources,such as Minikube. If you do want to specify resources,uncomment the following
  # lines,adjust them as necessary,and remove the curly braces after 'resources:'.
  limits:
    # cpu: 100m
    # memory: 2Gi
  requests:
    # cpu: 100m
    # memory: 1Gi