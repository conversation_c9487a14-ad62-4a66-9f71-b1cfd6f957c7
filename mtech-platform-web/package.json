{"name": "mtech-platform-web", "version": "0.0.1", "private": true, "scripts": {"dev": "vue-cli-service serve", "serve": "vue-cli-service serve", "build": "vue-cli-service build", "prebuild": "npm run theme", "lint": "vue-cli-service lint", "buildAll": "npm-run-all theme build --continue-on-error", "dll": "vue-cli-service dll", "serveAll": "npm-run-all theme serve --continue-on-error", "theme": "node build/theme.js", "upgrade:mtech": "npx update-by-scope -t latest @mtech npm install", "prepare": "husky install", "lint-staged": "lint-staged"}, "dependencies": {"@digis/internationalization": "^1.1.2", "@digis/multilingual-input": "^1.1.27", "@mtech-common/http": "^0.18.2", "@mtech-common/utils": "^1.0.0", "@mtech-form-design/deploy": "^1.0.2", "@mtech-form-design/form-parser": "^1.0.1", "@mtech-micro-frontend/vue-main": "^1.0.0", "@mtech-sso/single-sign-on": "1.2.5-tcl.7", "@mtech-ui/base": "^1.10.10", "@mtech-ui/dialog": "^1.11.4", "@mtech-ui/mtech-ui": "^1.11.12", "@mtech/common-loading": "^0.1.46", "@mtech/common-permission": "^1.2.4", "@mtech/common-template-page": "^1.5.10", "@mtech/common-tree-view": "^0.1.58", "@mtech/mtech-common-layout": "1.6.0-tcl.2", "@mtech/router-permission": "^1.1.4", "@mtech/vue-property-decorator": "^9.1.2", "autoprefixer": "^9.8.8", "axios": "^0.21.1", "bpmn-js": "^8.7.3", "bpmn-moddle": "^7.1.2", "core-js": "^3.20.1", "css.escape": "^1.5.1", "currency.js": "^2.0.4", "dayjs": "^1.10.7", "diagram-js-direct-editing": "^1.6.3", "didi": "^6.0.0", "hammerjs": "^2.0.8", "ids": "^1.0.0", "iso8601-duration": "^1.3.0", "object-refs": "^0.3.0", "path-intersection": "^2.2.1", "postcss": "^8.4.11", "screenfull": "^5.1.0", "script-loader": "^0.7.2", "vue": "^2.6.11", "vue-class-component": "^8.0.0-rc.1", "vue-codemirror": "^4.0.6", "vue-router": "^3.2.0", "vuex": "^3.4.0", "vuex-class": "^0.3.2"}, "devDependencies": {"@commitlint/cli": "^17.0.1", "@commitlint/config-conventional": "^17.0.0", "@digis-vue-cli-preset/vue-cli-plugin-performance": "^1.1.3", "@mtech-micro-frontend/vue-cli-plugin-micro": "^1.0.0", "@mtech/eslint-config-vue": "0.0.4", "@types/js-cookie": "^2.2.7", "@typescript-eslint/eslint-plugin": "^4.27.0", "@typescript-eslint/parser": "^4.27.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "^4.5.13", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-standard": "^5.1.2", "@vue/eslint-config-typescript": "^7.0.0", "chalk": "^2.4.2", "eslint": "^6.8.0", "eslint-config-prettier": "^8.3.0", "eslint-loader": "^4.0.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "husky": "^8.0.3", "js-cookie": "^2.2.1", "less-loader": "^10.0.1", "lint-staged": "^13.1.4", "node-sass": "^4.12.0", "npm-run-all": "^4.1.5", "raw-loader": "^4.0.2", "sass-loader": "^8.0.2", "style-resources-loader": "^1.4.1", "svg-sprite-loader": "^6.0.9", "ts-loader": "^9.2.3", "ts-transformer-keys": "^0.4.3", "typescript": "^4.3.2", "vue-cli-plugin-style-resources-loader": "~0.1.5", "vue-template-compiler": "^2.6.11", "vuex-module-decorators": "^1.0.1", "webpack-fix-style-only-entries": "^0.6.1", "webpack-glob-entry": "^2.1.1"}, "mtMicro": {"type": "master"}, "lint-staged": {"src/**/*.{js,jsx,vue,ts,tsx}": ["eslint --fix"]}}