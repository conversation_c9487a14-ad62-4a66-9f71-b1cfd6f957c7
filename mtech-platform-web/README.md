# mtech-workflow

## Project setup

```
npm install
```

### Compiles and hot-reloads for development

```
npm run serve
```

### Compiles and minifies for production

```
npm run build
```

### Lints and fixes files

```
npm run lint
```

### Customize configuration

See [Configuration Reference](https://cli.vuejs.org/config/).

# BPMN 定制化开发步骤

# 新建 BPMN 实例 editor

1. 实例化 Bpmn 得到 editor
2. editor 放到 vuex

## 定制化 Palette

1. create palette data
2. render menu template
3. bind Drag Event to menu item
4. create node by editor's method in DragCallback

## 定制化 Property
