const path = require('path')

const resolve = (dir) => path.join(__dirname, dir)

const svgDir = resolve('src/assets/svgicons')

module.exports = {
  configureWebpack: {},

  chainWebpack: (config) => {
    config.resolve.alias
      .set('@', resolve('src'))
      .set('@assets', resolve('src/assets'))
      .end()
    config.module
      .rule('raw-loader')
      .test(/.(bpmn|xml)$/)
      .use('raw-loader')
      .loader('raw-loader')
      .end()
      .use('vue-loader')
      .loader('vue-loader')
      .end()
      .use('vue-markdown-loader')
      .loader('vue-markdown-loader/lib/markdown-compiler')
      .options({
        raw: true,
        preventExtract: true
      })
    config.plugin('html').tap((args) => {
      args[0].title = 'JDSRM平台站'
      return args
    })

    config.module.rule('svg').exclude.add(svgDir)
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(svgDir)
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({ symbolId: 'icon-[name]' })

    // 移除 prefetch 插件
    config.plugins.delete('prefetch')
    // 移除 preload 插件
    config.plugins.delete('preload')
  },

  // 基本路径
  publicPath: './',

  // 放置静态资源
  assetsDir: './',

  // html输出路径
  indexPath: 'index.html',

  // 文件名哈希
  filenameHashing: true,

  devServer: {
    port: 9010,
    clientLogLevel: 'warning',
    proxy: {
      // '/api/flow/tenant/flowService/queryBuilder': {
      //   target: 'http://10.14.241.210:8017',
      //   changeOrigin: true,
      //   pathRewrite: {
      //     '^/api/flow': ''
      //   }
      // },
      '/api': {
        // target: 'http://flow.dev.qeweb.com',
        // target: 'http://srm.dev.qeweb.com',
        // target: 'http://srm-dev-gw.eads.tcl.com',
        target: 'https://srm-uat-main.eads.tcl.com',
        // target: 'http://10.14.241.210:8017',
        changeOrigin: true,
        pathRewrite: {
          // '^/api': ''
        }
      }
    }
  },

  lintOnSave: false,
  runtimeCompiler: true,

  pluginOptions: {
    'style-resources-loader': {
      preProcessor: 'scss',
      patterns: [
        path.resolve(__dirname, 'src/themes/_mtechUI.scss')
      ]
    }
  }
}
