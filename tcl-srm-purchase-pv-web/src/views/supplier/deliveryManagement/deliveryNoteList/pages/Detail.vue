<!-- 明细 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item :label="$t('送货单号')" prop="deliveryCode">
          <mt-input
            v-model="searchFormModel.deliveryCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('送货单行号')" prop="deliveryLineNo">
          <mt-input
            v-model="searchFormModel.deliveryLineNo"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('状态')" prop="statusList">
          <mt-multi-select
            v-model="searchFormModel.statusList"
            :data-source="statusOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('签收状态')" prop="receiptStatus">
          <mt-select
            v-model="searchFormModel.receiptStatus"
            :data-source="receiptStatusOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('交货计划单号')" prop="deliveryPlanNo">
          <mt-input
            v-model="searchFormModel.deliveryPlanNo"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供应商')" prop="supplierCodeList">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCodeList"
            url="/masterDataManagement/tenant/supplier/paged-query-distinct-no-scope"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('工厂')" prop="factoryCodeList">
          <RemoteAutocomplete
            v-model="searchFormModel.factoryCodeList"
            :url="$API.masterData.getSiteListUrl"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('物料编码')" prop="materialCode">
          <RemoteAutocomplete
            v-model="searchFormModel.materialCode"
            :url="$API.masterData.getItemListUrlPage"
            :placeholder="$t('请选择')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :search-fields="['itemName', 'itemCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('规格型号')" prop="specificationModel">
          <mt-input
            v-model="searchFormModel.specificationModel"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('采购订单号')" prop="orderCode">
          <mt-input
            v-model="searchFormModel.orderCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('采购订单行号')" prop="orderLineNo">
          <mt-input
            v-model="searchFormModel.orderLineNo"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('送货单类型')" prop="deliveryType">
          <mt-select
            v-model="searchFormModel.deliveryType"
            :data-source="deliveryTypeOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('业务类型')" prop="businessType">
          <mt-select
            v-model="searchFormModel.businessType"
            :data-source="businessTypeOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('是否SRM签收')" prop="srmSignFlag">
          <mt-select
            v-model="searchFormModel.srmSignFlag"
            :data-source="srmSignFlagOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('入库仓库')" prop="warehouseCodeList">
          <RemoteAutocomplete
            v-model="searchFormModel.warehouseCodeList"
            url="/srm-purchase-pv/tenant/pv/warehouse/page/query"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'warehouseName', value: 'warehouseCode' }"
            params-key="fuzzyParam"
          />
        </mt-form-item>
        <mt-form-item :label="$t('收货人')" prop="receiver">
          <mt-input
            v-model="searchFormModel.receiver"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('收货联系方式')" prop="receiverContact">
          <mt-input
            v-model="searchFormModel.receiverContact"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('配送地址')" prop="deliveryAddress">
          <mt-input
            v-model="searchFormModel.deliveryAddress"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="firstReceiveTime" :label="$t('第一次入库时间')">
          <mt-date-range-picker
            v-model="searchFormModel.firstReceiveTime"
            @change="(e) => dateTimeChange(e, 'firstReceiveTime')"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="receiveTime" :label="$t('最后一次入库时间')">
          <mt-date-range-picker
            v-model="searchFormModel.receiveTime"
            @change="(e) => dateTimeChange(e, 'receiveTime')"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('取消人')" prop="canceler">
          <mt-input
            v-model="searchFormModel.canceler"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="cancelTime" :label="$t('取消时间')">
          <mt-date-range-picker
            v-model="searchFormModel.cancelTime"
            @change="(e) => dateTimeChange(e, 'cancelTime')"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('关闭人')" prop="closer">
          <mt-input
            v-model="searchFormModel.closer"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="closeTime" :label="$t('关闭时间')">
          <mt-date-range-picker
            v-model="searchFormModel.closeTime"
            @change="(e) => dateTimeChange(e, 'closeTime')"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('同步极光状态')" prop="syncJgStatus">
          <mt-select
            v-model="searchFormModel.syncJgStatus"
            :data-source="syncJgStatusOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('同步极光接口信息')" prop="syncJgMsg">
          <mt-input
            v-model="searchFormModel.syncJgMsg"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            @change="(e) => dateTimeChange(e, 'createTime')"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="scTableRef"
      row-id="id"
      grid-id="30d8cfab-cc4c-71a9-823b-456643140a3c"
      show-overflow
      keep-source
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :loading="item.loading"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import {
  detailColumnData,
  statusOptions,
  receiptStatusOptions,
  businessTypeOptions,
  deliveryTypeOptions,
  srmSignFlagOptions,
  syncJgStatusOptions
} from '../config/index'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  components: { CollapseSearch, ScTable, RemoteAutocomplete },
  data() {
    const startDate = dayjs().subtract(1, 'month').startOf('month')
    return {
      searchFormModel: {
        createTime: [new Date(startDate), new Date()],
        createTimeStart: this.getUnix(dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00')),
        createTimeEnd: this.getUnix(dayjs(new Date()).format('YYYY-MM-DD 23:59:59'))
      },
      searchFormRules: {},
      toolbar: [
        { code: 'copy', name: this.$t('复制送货单号'), status: 'info', loading: false },
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: detailColumnData,
      loading: false,
      tableData: [],

      statusOptions,
      receiptStatusOptions,
      businessTypeOptions,
      deliveryTypeOptions,
      srmSignFlagOptions,
      syncJgStatusOptions
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    dateTimeChange(e, field) {
      let startField = `${field}Start`
      let endField = `${field}End`

      if (['cancelTime', 'closeTime', 'firstReceiveTime', 'receiveTime'].includes(field)) {
        startField = `${field}S`
        endField = `${field}E`
      }

      if (e.startDate) {
        this.searchFormModel[startField] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[endField] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[startField] = null
        this.searchFormModel[endField] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      const startDate = dayjs().subtract(1, 'month').startOf('month')
      this.searchFormModel.createTime = [new Date(startDate), new Date()]
      this.searchFormModel.createTimeStart = this.getUnix(
        dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00')
      )
      this.searchFormModel.createTimeEnd = this.getUnix(
        dayjs(new Date()).format('YYYY-MM-DD 23:59:59')
      )
      this.handleSearch()
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.deliveryManagement
        .pageDetailDeliveryNoteListApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['copy']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'copy':
          this.handleCopy(selectedRecords)
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleCopy(selectedRecords) {
      const deliveryCodeList = selectedRecords.map((i) => {
        return i.deliveryCode
      })
      const str = [...new Set(deliveryCodeList)].join(' ')
      this.copyToClipboard(str)
    },
    copyToClipboard(str) {
      const el = document.createElement('textarea')
      el.value = str
      el.setAttribute('readonly', '')
      el.style.position = 'absolute'
      el.style.left = '-9999px'
      document.body.appendChild(el)
      el.select()
      document.execCommand('copy')
      document.body.removeChild(el)

      this.$toast({ content: this.$t('复制成功'), type: 'success' })
    },
    handleExport(e) {
      const dynamicHeaderMap = {}
      detailColumnData.forEach((item) => {
        if (item.field) {
          if (
            [
              'status',
              'receiptStatus',
              'businessType',
              'deliveryType',
              'shippingMethod',
              'srmSignFlag',
              'syncJgStatus'
            ].includes(item.field)
          ) {
            dynamicHeaderMap[item.field + 'Name'] = item.title
          } else if (item.field === 'warehouseCode') {
            dynamicHeaderMap[item.field] = item.title
            dynamicHeaderMap['warehouseName'] = this.$t('入库仓库名称')
          } else if (item.field === 'baseUnitCode') {
            dynamicHeaderMap[item.field] = item.title
            dynamicHeaderMap['baseUnitName'] = this.$t('单位名称')
          } else if (['firstReceiveTime', 'receiveTime'].includes(item.field)) {
            dynamicHeaderMap[item.field + 'Str'] = item.title
          } else {
            dynamicHeaderMap[item.field] = item.title
          }
        }
      })
      const params = {
        dynamicHeaderMap,
        pvDeliveryItemQueryReq: {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        }
      }
      this.$API.deliveryManagement
        .exportDeliveryNoteDetailApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>
