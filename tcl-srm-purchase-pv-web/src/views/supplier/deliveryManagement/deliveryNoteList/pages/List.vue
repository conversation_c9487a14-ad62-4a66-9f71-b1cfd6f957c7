<!-- 列表 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item :label="$t('送货单号')" prop="deliveryCode">
          <mt-input
            v-model="searchFormModel.deliveryCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('状态')" prop="statusList">
          <mt-multi-select
            v-model="searchFormModel.statusList"
            :data-source="statusOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('签收状态')" prop="receiptStatus">
          <mt-select
            v-model="searchFormModel.receiptStatus"
            :data-source="receiptStatusOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('业务类型')" prop="businessType">
          <mt-select
            v-model="searchFormModel.businessType"
            :data-source="businessTypeOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('工厂')" prop="factoryCodeList">
          <RemoteAutocomplete
            v-model="searchFormModel.factoryCodeList"
            :url="$API.masterData.getSiteListUrl"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('送货单类型')" prop="deliveryType">
          <mt-select
            v-model="searchFormModel.deliveryType"
            :data-source="deliveryTypeOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('是否SRM签收')" prop="srmSignFlag">
          <mt-select
            v-model="searchFormModel.srmSignFlag"
            :data-source="srmSignFlagOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('入库仓库')" prop="warehouseCodeList">
          <RemoteAutocomplete
            v-model="searchFormModel.warehouseCodeList"
            url="/srm-purchase-pv/tenant/pv/warehouse/page/query"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'warehouseName', value: 'warehouseCode' }"
            params-key="fuzzyParam"
          />
        </mt-form-item>
        <mt-form-item :label="$t('收货人')" prop="receiver">
          <mt-input
            v-model="searchFormModel.receiver"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('收货联系方式')" prop="receiverContact">
          <mt-input
            v-model="searchFormModel.receiverContact"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('配送地址')" prop="deliveryAddress">
          <mt-input
            v-model="searchFormModel.deliveryAddress"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('发货方式')" prop="shippingMethodList">
          <mt-multi-select
            v-model="searchFormModel.shippingMethodList"
            :data-source="shippingMethodOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('物流单号')" prop="logisticsNo">
          <mt-input
            v-model="searchFormModel.logisticsNo"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('物流公司名称')" prop="logisticsCompany">
          <mt-input
            v-model="searchFormModel.logisticsCompany"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('发件人姓名')" prop="senderName">
          <mt-input
            v-model="searchFormModel.senderName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('发件人手机号')" prop="senderPhone">
          <mt-input
            v-model="searchFormModel.senderPhone"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('车型')" prop="carModel">
          <mt-input
            v-model="searchFormModel.carModel"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('车牌号')" prop="licensePlate">
          <mt-input
            v-model="searchFormModel.licensePlate"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('司机姓名')" prop="driverName">
          <mt-input
            v-model="searchFormModel.driverName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('司机手机号')" prop="driverPhone">
          <mt-input
            v-model="searchFormModel.driverPhone"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="expectedArrivalTime" :label="$t('预计到货时间')">
          <mt-date-range-picker
            v-model="searchFormModel.expectedArrivalTime"
            @change="(e) => expectedArrivalTimeChange(e, 'expectedArrivalTime')"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('取消人')" prop="canceler">
          <mt-input
            v-model="searchFormModel.canceler"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="cancelTime" :label="$t('取消时间')">
          <mt-date-range-picker
            v-model="searchFormModel.cancelTime"
            @change="(e) => dateTimeChange(e, 'cancelTime')"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('关闭人')" prop="closer">
          <mt-input
            v-model="searchFormModel.closer"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="closeTime" :label="$t('关闭时间')">
          <mt-date-range-picker
            v-model="searchFormModel.closeTime"
            @change="(e) => dateTimeChange(e, 'closeTime')"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('同步极光状态')" prop="syncJgStatus">
          <mt-select
            v-model="searchFormModel.syncJgStatus"
            :data-source="syncJgStatusOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('同步极光接口信息')" prop="syncJgMsg">
          <mt-input
            v-model="searchFormModel.syncJgMsg"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('创建人')" prop="createUserName">
          <mt-input
            v-model="searchFormModel.createUserName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            @change="(e) => dateTimeChange(e, 'createTime')"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="scTableRef"
      row-id="id"
      grid-id="4aa69c4c-e03d-3100-1c12-832e32b44a77"
      show-overflow
      keep-source
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :loading="item.loading"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
      <template #codeDefault="{ row }">
        <div>
          <span style="cursor: pointer; color: #2783fe" @click="handleClick(row)">
            {{ row.deliveryCode }}
          </span>
        </div>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import {
  listColumnData,
  statusOptions,
  receiptStatusOptions,
  businessTypeOptions,
  deliveryTypeOptions,
  shippingMethodOptions,
  srmSignFlagOptions,
  syncJgStatusOptions
} from '../config/index'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  components: { CollapseSearch, ScTable, RemoteAutocomplete },
  data() {
    const startDate = dayjs().subtract(1, 'month').startOf('month')
    return {
      searchFormModel: {
        createTime: [new Date(startDate), new Date()],
        createTimeStart: this.getUnix(dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00')),
        createTimeEnd: this.getUnix(dayjs(new Date()).format('YYYY-MM-DD 23:59:59'))
      },
      searchFormRules: {},
      toolbar: [
        { code: 'copy', name: this.$t('复制送货单号'), status: 'info', loading: false },
        { code: 'cancel', name: this.$t('取消'), status: 'info', loading: false },
        // { code: 'upload', name: this.$t('上传签收单'), status: 'info', loading: false },
        { code: 'push', name: this.$t('同步极光系统'), status: 'info', loading: false },
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false },
        { code: 'print', name: this.$t('打印'), status: 'info', loading: false }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: listColumnData,
      loading: false,
      tableData: [],

      statusOptions,
      receiptStatusOptions,
      businessTypeOptions,
      deliveryTypeOptions,
      shippingMethodOptions,
      srmSignFlagOptions,
      syncJgStatusOptions
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    handleClick(row) {
      sessionStorage.setItem('supplyPlanSelectedRecords', JSON.stringify([row]))
      let type = [2].includes(row.status) ? 'edit' : 'check'
      this.$router.push({
        path: '/purchase-pv/delivery-management/supply-plan-detail',
        query: {
          type,
          source: row?.deliveryType === 9 ? 'plan' : 'order',
          timeStamp: new Date().getTime(),
          id: row.id
        }
      })
    },
    expectedArrivalTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'S'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[field + 'E'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[field + 'S'] = null
        this.searchFormModel[field + 'E'] = null
      }
    },
    dateTimeChange(e, field) {
      let startField = `${field}Start`
      let endField = `${field}End`

      if (['cancelTime', 'closeTime'].includes(field)) {
        startField = `${field}S`
        endField = `${field}E`
      }

      if (e.startDate) {
        this.searchFormModel[startField] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[endField] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[startField] = null
        this.searchFormModel[endField] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      const startDate = dayjs().subtract(1, 'month').startOf('month')
      this.searchFormModel.createTime = [new Date(startDate), new Date()]
      this.searchFormModel.createTimeStart = this.getUnix(
        dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00')
      )
      this.searchFormModel.createTimeEnd = this.getUnix(
        dayjs(new Date()).format('YYYY-MM-DD 23:59:59')
      )
      this.handleSearch()
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.deliveryManagement
        .pageDeliveryNoteListApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['copy', 'cancel', 'upload', 'push', 'print']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const ids = selectedRecords.map((record) => record.id)

      // 检查取消操作的条件
      if (['cancel'].includes(e.code)) {
        const invalidRecord = selectedRecords.find((record) => record.status !== 2)
        if (invalidRecord) {
          this.$toast({
            content: this.$t('仅支持操作送货中且没有产生入库的送货单！'),
            type: 'warning'
          })
          return
        }
      }
      // 检查上传签收单的条件
      if (['upload'].includes(e.code)) {
        const invalidRecord = selectedRecords.find((record) => record.status !== 2)
        if (invalidRecord) {
          this.$toast({
            content: this.$t('仅送货中状态的送货单支持上传签收单！'),
            type: 'warning'
          })
          return
        }
      }
      switch (e.code) {
        case 'copy':
          this.handleCopy(selectedRecords)
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        case 'cancel':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认取消？')
            },
            success: () => {
              this.handleCancel(ids)
            }
          })
          break
        case 'upload':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认上传签收单？')
            },
            success: () => {
              this.handleUpload(ids)
            }
          })
          break
        case 'push':
          this.handlePush(ids)
          break
        case 'print':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认打印？')
            },
            success: () => {
              this.handlePrint(ids)
            }
          })
          break
        default:
          break
      }
    },
    handleCopy(selectedRecords) {
      const deliveryCodeList = selectedRecords.map((i) => {
        return i.deliveryCode
      })
      const str = [...new Set(deliveryCodeList)].join(' ')
      this.copyToClipboard(str)
    },
    copyToClipboard(str) {
      const el = document.createElement('textarea')
      el.value = str
      el.setAttribute('readonly', '')
      el.style.position = 'absolute'
      el.style.left = '-9999px'
      document.body.appendChild(el)
      el.select()
      document.execCommand('copy')
      document.body.removeChild(el)

      this.$toast({ content: this.$t('复制成功'), type: 'success' })
    },
    handleExport(e) {
      const dynamicHeaderMap = {}
      listColumnData.forEach((item) => {
        if (item.field) {
          if (
            [
              'status',
              'receiptStatus',
              'businessType',
              'deliveryType',
              'shippingMethod',
              'srmSignFlag',
              'syncJgStatus'
            ].includes(item.field)
          ) {
            dynamicHeaderMap[item.field + 'Name'] = item.title
          } else if (item.field === 'warehouseCode') {
            dynamicHeaderMap[item.field] = item.title
            dynamicHeaderMap['warehouseName'] = this.$t('入库仓库名称')
          } else {
            dynamicHeaderMap[item.field] = item.title
          }
        }
      })
      const params = {
        dynamicHeaderMap,
        pvDeliveryQueryReq: {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        }
      }
      this.$API.deliveryManagement
        .exportDeliveryNoteListApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    },
    handleCancel(ids) {
      this.$API.deliveryManagement.cancelDeliveryNoteListApi({ ids }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('取消成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleUpload(ids) {
      this.$API.deliveryManagement.uploadDeliveryNoteListApi({ ids }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handlePush(ids) {
      this.$dialog({
        modal: () => import('../components/selectTypeDialog.vue'),
        data: {
          title: this.$t('选择操作类型')
        },
        success: (operationType) => {
          let params = {
            operationType,
            ids
          }
          this.$API.deliveryManagement.pushDeliveryNoteListApi(params).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('推送成功'), type: 'success' })
              this.handleSearch()
            }
          })
        }
      })
    },
    handlePrint(ids) {
      this.$API.deliveryManagement.printDeliveryNoteListApi({ ids }).then((res) => {
        const content = res.data
        this.pdfUrl = window.URL.createObjectURL(
          new Blob([content], { type: 'text/html;charset=utf-8' })
        )
        let date = new Date().getTime()
        let ifr = document.createElement('iframe')
        ifr.style.frameborder = 'no'
        ifr.style.display = 'none'
        ifr.style.pageBreakBefore = 'always'
        ifr.setAttribute('id', 'printPdf' + date)
        ifr.setAttribute('name', 'printPdf' + date)
        ifr.src = this.pdfUrl
        document.body.appendChild(ifr)
        this.doPrint('printPdf' + date)
        window.URL.revokeObjectURL(ifr.src)
      })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
      }, 100)
    }
  }
}
</script>
