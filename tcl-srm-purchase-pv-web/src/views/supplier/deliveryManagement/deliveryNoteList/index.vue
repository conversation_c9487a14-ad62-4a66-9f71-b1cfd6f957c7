<!-- 供方-发货管理-送货单列表 -->
<template>
  <div>
    <mt-tabs
      class="toggle-tab"
      :e-tab="false"
      :data-source="tabList"
      :selected-item="selectedItem"
      @handleSelectTab="handleSelectTab"
    />
    <div class="toggle-content">
      <List ref="listRef" v-show="tabIndex == 0" />
      <Detail ref="detailRef" v-show="tabIndex == 1" />
    </div>
  </div>
</template>

<script>
import List from './pages/List.vue'
import Detail from './pages/Detail.vue'
export default {
  components: { List, Detail },
  data() {
    return {
      tabList: [{ title: this.$t('送货单列表') }, { title: this.$t('送货单明细') }],
      selectedItem: 0,
      tabIndex: 0
    }
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
    }
  }
}
</script>
