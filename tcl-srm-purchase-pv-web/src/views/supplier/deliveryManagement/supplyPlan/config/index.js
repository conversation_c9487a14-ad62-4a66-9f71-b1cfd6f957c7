import { i18n } from '@/main.js'
import dayjs from 'dayjs'

export const statusOptions = [
  { text: i18n.t('发货中'), label: i18n.t('发货中'), value: 2 },
  { text: i18n.t('已完成'), label: i18n.t('已完成'), value: 3 },
  { text: i18n.t('已取消'), label: i18n.t('已取消'), value: 4 }
]

export const receiptStatusOptions = [
  { text: i18n.t('已签收'), label: i18n.t('已签收'), value: 'Y' },
  { text: i18n.t('未签收'), label: i18n.t('未签收'), value: 'N' }
]

export const businessTypeOptions = [
  { text: i18n.t('户用业务'), label: i18n.t('户用业务'), value: 1 },
  { text: i18n.t('商用业务'), label: i18n.t('商用业务'), value: 2 },
  { text: i18n.t('海外业务'), label: i18n.t('海外业务'), value: 3 }
]

export const urgentStatusOptions = [
  { text: i18n.t('未加急'), label: i18n.t('未加急'), value: 0 },
  { text: i18n.t('已加急'), label: i18n.t('已加急'), value: 1 }
]

export const deliveryTypeOptions = [
  { text: i18n.t('采购订单'), label: i18n.t('采购订单'), value: 1 },
  { text: i18n.t('交货计划'), label: i18n.t('交货计划'), value: 9 }
]

export const srmSignFlagOptions = [
  { text: i18n.t('是'), label: i18n.t('是'), value: 1 },
  { text: i18n.t('否'), label: i18n.t('否'), value: 0 }
]

export const shippingMethodOptions = [
  { text: i18n.t('自提'), label: i18n.t('自提'), value: 0 },
  { text: i18n.t('快递'), label: i18n.t('快递'), value: 1 },
  { text: i18n.t('厂家直送'), label: i18n.t('厂家直送'), value: 2 },
  { text: i18n.t('速必达配送'), label: i18n.t('速必达配送'), value: 3 }
]

export const syncJgStatusOptions = [
  { text: i18n.t('未同步'), label: i18n.t('未同步'), value: 0 },
  { text: i18n.t('同步中'), label: i18n.t('同步中'), value: 1 },
  { text: i18n.t('同步成功'), label: i18n.t('同步成功'), value: 2 },
  { text: i18n.t('同步失败'), label: i18n.t('同步失败'), value: 3 }
]

export const billTypeOptions = [
  { text: i18n.t('签收附件'), label: i18n.t('签收附件'), value: 'delivery_receipt' },
  { text: i18n.t('其他附件'), label: i18n.t('其他附件'), value: 'delivery_other' }
]

// 数据来源
export const dataSourceOptions = [
  { value: 1, text: i18n.t('手工新增') },
  { value: 0, text: i18n.t('仓库需求') }
]

export const deliveryScheduleColumnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'serialNumber',
    title: i18n.t('序列号'),
    minWidth: 180
  },
  {
    field: 'businessType',
    title: i18n.t('业务类型'),
    formatter: ({ cellValue }) => {
      let item = businessTypeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂'),
    minWidth: 220,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.siteName : ''
    }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    minWidth: 220,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.supplierName : ''
    }
  },
  {
    field: 'itemCode',
    title: i18n.t('物料'),
    minWidth: 220,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.itemName : ''
    }
  },
  {
    field: 'powerKw',
    title: i18n.t('功率(KW)'),
    minWidth: 120
  },
  {
    field: 'unitCode',
    title: i18n.t('基本单位'),
    minWidth: 120,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.unitName : ''
    }
  },
  {
    field: 'totalWarehouseDemand',
    title: i18n.t('仓库总需求'),
    minWidth: 120
  },
  {
    field: 'demandDate',
    title: i18n.t('需求日期'),
    minWidth: 130,
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD')
    }
  },
  {
    field: 'demandQuantity',
    title: i18n.t('需求数量'),
    minWidth: 120
  },
  {
    field: 'promisedQuantity',
    title: i18n.t('承诺数量'),
    minWidth: 120
  },
  {
    field: 'orderPreDeliveryQty',
    title: i18n.t('订单待发货数量'),
    minWidth: 140
  },
  {
    field: 'pendingShipmentQuantity',
    title: i18n.t('待发货数量'),
    minWidth: 120
  },
  {
    field: 'shipmentQuantity',
    title: i18n.t('本次发货数量'),
    minWidth: 120,
    editRender: {},
    slots: {
      edit: 'shipmentQuantityEdit'
    }
  },
  {
    field: 'categoryCode',
    title: i18n.t('品类'),
    minWidth: 120,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.categoryName : ''
    }
  },
  {
    field: 'largeCategoryName',
    title: i18n.t('大类')
  },
  {
    field: 'mediumCategoryName',
    title: i18n.t('中类')
  },
  {
    field: 'smallCategoryName',
    title: i18n.t('小类')
  },
  {
    field: 'dataSource',
    title: i18n.t('数据来源'),
    minWidth: 120,
    formatter: ({ cellValue }) => {
      let item = dataSourceOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'warehouseDemandCode',
    title: i18n.t('仓库需求编码'),
    minWidth: 120
  },
  {
    field: 'srmSignFlag',
    title: i18n.t('是否SRM签收'),
    minWidth: 130,
    formatter: ({ cellValue }) => {
      let item = srmSignFlagOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'warehouseCode',
    title: i18n.t('入库仓库'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.warehouseName : ''
    }
  },
  {
    field: 'consignee',
    title: i18n.t('收货人'),
    minWidth: 120
  },
  {
    field: 'contactPhone',
    title: i18n.t('收货联系方式'),
    minWidth: 120
  },
  {
    field: 'deliveryAddress',
    title: i18n.t('配送地址'),
    minWidth: 120
  }
]

export const purchaseOrderColumnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'orderCode',
    title: i18n.t('采购订单号')
  },
  {
    field: 'itemNo',
    title: i18n.t('采购订单行号')
  },
  {
    field: 'companyCode',
    title: i18n.t('公司编码')
  },
  {
    field: 'companyName',
    title: i18n.t('公司名称')
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂编码')
  },
  {
    field: 'siteName',
    title: i18n.t('工厂名称')
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称')
  },
  {
    field: 'specificationModel',
    title: i18n.t('规格型号')
  },
  {
    field: 'baseUnitCode',
    title: i18n.t('基本单位'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.baseUnitName : ''
    }
  },
  {
    field: 'quantity',
    title: i18n.t('订单数量')
  },
  {
    field: 'preDeliveryQty',
    title: i18n.t('待发货数量')
  },
  {
    field: 'deliveryQty',
    title: i18n.t('已发货数量')
  },
  {
    field: 'transitQty',
    title: i18n.t('在途数量')
  },
  {
    field: 'receiveQty',
    title: i18n.t('已收货数量')
  },
  {
    field: 'warehouseQty',
    title: i18n.t('已入库数量')
  },
  {
    field: 'requiredDeliveryDate',
    title: i18n.t('要求交期')
  },
  {
    field: 'orderDate',
    title: i18n.t('订单日期')
  },
  {
    field: 'categoryCode',
    title: i18n.t('品类'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.categoryName : ''
    }
  },
  {
    field: 'largeCategoryCode',
    title: i18n.t('大类'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.largeCategoryName : ''
    }
  },
  {
    field: 'mediumCategoryCode',
    title: i18n.t('中类'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.mediumCategoryName : ''
    }
  },
  {
    field: 'smallCategoryCode',
    title: i18n.t('小类'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.smallCategoryName : ''
    }
  },
  {
    field: 'businessType',
    title: i18n.t('业务类型'),
    formatter: ({ cellValue }) => {
      let item = businessTypeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'urgentStatus',
    title: i18n.t('加急状态'),
    formatter: ({ cellValue }) => {
      let item = urgentStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'urgentDate',
    title: i18n.t('加急日期')
  },
  {
    field: 'srmSignFlag',
    title: i18n.t('是否SRM签收'),
    formatter: ({ cellValue }) => {
      let item = srmSignFlagOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'warehouseCode',
    title: i18n.t('入库仓库'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.warehouseName : ''
    }
  },
  {
    field: 'consignee',
    title: i18n.t('收货人')
  },
  {
    field: 'contactPhone',
    title: i18n.t('收货联系方式')
  },
  {
    field: 'deliveryAddress',
    title: i18n.t('配送地址')
  },
  {
    field: 'purchaseRemark',
    title: i18n.t('采方备注')
  },
  {
    field: 'supplierRemark',
    title: i18n.t('供方备注')
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人')
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160
  }
]
