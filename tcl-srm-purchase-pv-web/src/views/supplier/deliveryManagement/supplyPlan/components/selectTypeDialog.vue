<template>
  <div>
    <mt-dialog
      ref="dialog"
      :buttons="buttons"
      :header="header"
      @beforeClose="cancel"
      width="360px"
      height="210px"
    >
      <div style="padding: 20px">
        <mt-form ref="formRef" :model="modelForm" :rules="formRules">
          <mt-form-item prop="billType" :label="$t('附件类型')" label-style="left">
            <vxe-select
              v-model="modelForm.billType"
              :options="billTypeOptions"
              :option-props="{ label: 'label', value: 'value' }"
              :placeholder="$t('请选择')"
              clearable
            />
          </mt-form-item>
        </mt-form>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import { billTypeOptions } from '../config/index'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      modelForm: {
        billType: 'delivery_receipt'
      },
      formRules: {
        billType: [
          {
            required: true,
            message: this.$t('请选择附件类型'),
            trigger: 'blur'
          }
        ]
      },
      billTypeOptions
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    confirm() {
      this.$refs.formRef.validate(async (valid) => {
        if (!valid) {
          return
        }
        this.$emit('confirm-function', this.modelForm.billType)
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style></style>
