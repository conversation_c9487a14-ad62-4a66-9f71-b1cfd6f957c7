<template>
  <div>
    <sc-table
      ref="scTableRef"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      keep-source
      :sortable="false"
      :is-show-right-btn="true"
      :is-show-refresh-bth="pageType !== 'create'"
      @refresh="getTableData"
      @edit-closed="editComplete"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
    <mt-page
      v-if="pageType !== 'create'"
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import ScTable from '@/components/ScTable/src/index'
import { getHeadersFileName, download } from '@/utils/utils'
import { statusOptions } from '../../snCodeInfoUpload/config/index'
export default {
  name: 'OverseasComponentTab',
  components: { ScTable },
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 100,
        pageSizes: [100, 200, 500, 1000]
      },
      editRules: {
        code: [{ required: true, message: this.$t('必填') }]
      },

      statusOptions
    }
  },
  computed: {
    pageType() {
      return this.$route.query?.type
    },
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'id',
          title: this.$t('ID')
        },
        {
          field: 'status',
          title: this.$t('状态'),
          formatter: ({ cellValue }) => {
            let item = statusOptions.find((item) => item.value === cellValue)
            return item ? item.text : ''
          }
        },
        {
          field: 'supplier',
          title: this.$t('SUPPLIER'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.supplier}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'serialNo',
          title: this.$t('SERIAL_NO'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.serialNo}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'testId',
          title: this.$t('TEST_ID'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.testId}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'testDate',
          title: this.$t('TEST_DATE'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.testDate}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'testTime',
          title: this.$t('TEST_TIME'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.testTime}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'power',
          title: this.$t('POWER'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input v-model={row.power} placeholder={this.$t('请输入')} transfer clearable />
              ]
            }
          }
        },
        {
          field: 'vmpMod',
          title: this.$t('VMP_MOD'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.vmpMod}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'vocMod',
          title: this.$t('VOC_MOD'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.vocMod}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'impMod',
          title: this.$t('IMP_MOD'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.impMod}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'iscMod',
          title: this.$t('ISC_MOD'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.iscMod}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'ff',
          title: this.$t('FF'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input v-model={row.ff} placeholder={this.$t('请输入')} transfer clearable />
              ]
            }
          }
        },
        {
          field: 'productType',
          title: this.$t('PRODUCT_TYPE'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.productType}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'class',
          title: this.$t('CLASS'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input v-model={row.class} placeholder={this.$t('请输入')} transfer clearable />
              ]
            }
          }
        },
        {
          field: 'palletNo',
          title: this.$t('PALLET_NO'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.palletNo}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'impLevel',
          title: this.$t('IMP_LEVEL'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.impLevel}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'cartonNo',
          title: this.$t('CARTON_NO'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.cartonNo}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'cellBin',
          title: this.$t('CELL_BIN'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.cellBin}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'cellColor',
          title: this.$t('CELL_COLOR'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.cellColor}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'glassType',
          title: this.$t('GLASS_TYPE'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.glassType}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'packingListNo',
          title: this.$t('PACKING_LIST_NO'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.packingListNo}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'customerName',
          title: this.$t('CUSTOMER_NAME'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.customerName}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'invoiceNo',
          title: this.$t('INVOICE_NO'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.invoiceNo}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'poNo',
          title: this.$t('PO_NO'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input v-model={row.poNo} placeholder={this.$t('请输入')} transfer clearable />
              ]
            }
          }
        },
        {
          field: 'partNo',
          title: this.$t('PART_NO'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.partNo}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'shipDate',
          title: this.$t('SHIP_DATE'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.shipDate}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'qrcodeWn',
          title: this.$t('QRCODE_WN'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.qrcodeWn}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'containerNo',
          title: this.$t('CONTAINER_NO'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.containerNo}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'remarks',
          title: this.$t('备注'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.remarks}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'submitJiguangFailureReason',
          title: this.$t('提交（极光）失败原因')
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          minWidth: 160
        },
        {
          field: 'updateUserName',
          title: this.$t('更新人')
        },
        {
          field: 'updateTime',
          title: this.$t('更新时间'),
          minWidth: 160
        }
      ]
    },
    editConfig() {
      return {
        enabled: ['create'].includes(this.pageType),
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    toolbar() {
      let btns = []
      if (['create'].includes(this.pageType)) {
        btns = [
          { code: 'import', name: this.$t('导入'), status: 'info', loading: false },
          { code: 'delete', name: this.$t('删除'), status: 'info', loading: false }
        ]
      } else if (['edit'].includes(this.pageType)) {
        btns = [
          { code: 'import', name: this.$t('导入'), status: 'info', loading: false },
          { code: 'export', name: this.$t('导出'), status: 'info', loading: false },
          { code: 'submit', name: this.$t('提交'), status: 'info', loading: false },
          { code: 'delete', name: this.$t('删除'), status: 'info', loading: false }
        ]
      } else {
        btns = [{ code: 'export', name: this.$t('导出'), status: 'info', loading: false }]
      }
      return btns
    }
  },
  mounted() {
    if (this.pageType !== 'create') {
      this.getTableData()
    }
  },
  methods: {
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    getTableData() {
      this.loading = true
      let params = {
        deliveryIdList: [this.$route.query?.id],
        businessType: 'HW_COMPONENT',
        createTimeStart: dayjs(
          dayjs(this.dataInfo?.createTime).format('YYYY-MM-DD 00:00:00')
        ).valueOf(),
        createTimeEnd: dayjs().valueOf(),
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        }
      }
      this.$API.deliveryManagement
        .pageAbroadSnCodeInfoUploadApi(params)
        .then((res) => {
          if (res.code === 200) {
            const { total = 0, records = [] } = res.data
            this.pageSettings.totalRecordsCount = Number(total)
            this.pageSettings.totalPages = Math.ceil(total / this.pageSettings.pageSize)
            this.tableData = records
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['delete', 'submit']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'import':
          this.handleImport()
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        case 'submit':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认提交？')
            },
            success: () => {
              this.handleSubmit(selectedRecords)
            }
          })
          break
        case 'delete':
          this.handleDelete(selectedRecords)
          break
        default:
          break
      }
    },
    handleDelete(selectedRecords) {
      if (['create'].includes(this.pageType)) {
        this.tableRef.removeCheckboxRow()
        const currentViewRecords = this.tableRef.getTableData().visibleData
        this.tableData = currentViewRecords
        this.$emit('updateDetail', currentViewRecords)
      } else {
        let ids = selectedRecords.map((v) => v.id)
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除？')
          },
          success: () => {
            this.$API.deliveryManagement.deleteAbroadSnCodeInfoUploadApi({ ids }).then((res) => {
              if (res.code === 200) {
                this.$toast({ content: this.$t('删除成功'), type: 'success' })
                this.tableRef.removeCheckboxRow()
              }
            })
          }
        })
      }
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        //1、 校验必填
        this.tableRef.validate([row]).then((valid) => {
          if (valid) {
            this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
            return
          }

          const currentViewRecords = this.tableRef.getTableData().visibleData
          this.$emit('updateDetail', currentViewRecords)
        })
      }
    },
    handleImport() {
      if (this.pageType === 'create') {
        this.$dialog({
          modal: () => import('@/components/uploadDialog/index.vue'),
          data: {
            title: this.$t('导入'),
            paramsKey: 'excel',
            importApi: this.$API.deliveryManagement.getImportDataAbroadSnCodeInfoUploadApi,
            downloadTemplateApi: this.$API.deliveryManagement.tempAbroadSnCodeInfoUploadApi
          },
          success: (res) => {
            if (res?.length !== 0) {
              res.forEach((item) => {
                this.tableData.push(item)
              })
              this.$emit('updateDetail', this.tableData)
            }
          }
        })
      } else {
        this.$dialog({
          modal: () => import('@/components/uploadDialog/index.vue'),
          data: {
            title: this.$t('导入'),
            paramsKey: 'excel',
            importApi: this.$API.deliveryManagement.importAbroadSnCodeInfoUploadApi,
            downloadTemplateApi: this.$API.deliveryManagement.tempAbroadSnCodeInfoUploadApi,
            asyncParams: {
              deliveryCode: this.dataInfo?.deliveryCode
            }
          },
          success: () => {
            this.getTableData()
          }
        })
      }
    },
    handleExport(e) {
      const dynamicHeaderMap = {}
      this.columns.forEach((item) => {
        if (item.field) {
          if (['status'].includes(item.field)) {
            dynamicHeaderMap[item.field + 'Name'] = item.title
          } else {
            dynamicHeaderMap[item.field] = item.title
          }
        }
      })
      const params = {
        dynamicHeaderMap,
        pvAbroadSnQueryReq: {
          deliveryIdList: [this.$route.query?.id],
          businessType: 'HW_COMPONENT',
          page: {
            current: 1,
            size: 100
          },
          createTimeStart: dayjs(
            dayjs(this.dataInfo?.createTime).format('YYYY-MM-DD 00:00:00')
          ).valueOf(),
          createTimeEnd: dayjs().valueOf()
        }
      }
      this.$API.deliveryManagement
        .exportAbroadNonSnCodeInfoUploadApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    },
    handleSubmit(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      this.$API.deliveryManagement.submitAbroadSnCodeInfoUploadApi({ ids }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          this.getTableData()
        }
      })
    }
  }
}
</script>

<style></style>
