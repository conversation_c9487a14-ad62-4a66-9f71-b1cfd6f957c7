<!-- 选择司机弹窗 -->
<template>
  <div>
    <mt-dialog
      ref="dialog"
      css-class="dialog-main"
      :buttons="buttons"
      :header="header"
      @beforeClose="cancel"
    >
      <div style="padding-top: 20px">
        <collapse-search
          class="toggle-container"
          :is-grid-display="true"
          @reset="handleReset"
          @search="handleSearch"
        >
          <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
            <mt-form-item :label="$t('司机姓名')" prop="driverName">
              <mt-input
                v-model="searchFormModel.driverName"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('司机手机号')" prop="driverPhone">
              <mt-input
                v-model="searchFormModel.driverPhone"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('身份证号')" prop="driverIdNo">
              <mt-input
                v-model="searchFormModel.driverIdNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
          </mt-form>
        </collapse-search>
        <sc-table
          ref="scTableRef"
          row-id="id"
          show-overflow
          keep-source
          :loading="loading"
          :columns="columns"
          :table-data="tableData"
          :fix-height="300"
          :is-show-refresh-bth="true"
          @radio-change="handleRadioChange"
        />
        <mt-page
          ref="pageRef"
          class="flex-keep custom-page"
          :page-settings="pageSettings"
          :total-pages="pageSettings.totalPages"
          @currentChange="handleCurrentChange"
          @sizeChange="handleSizeChange"
        />
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'

export default {
  name: 'DriverSelectDialog',
  components: { CollapseSearch, ScTable },
  data() {
    return {
      header: this.$t('选择司机'),
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      searchFormModel: {},
      searchFormRules: {},
      loading: false,
      tableData: [],
      selectedRow: null,
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 20,
        pageSizes: [10, 20, 50, 100]
      }
    }
  },
  computed: {
    columns() {
      return [
        {
          width: 50,
          type: 'radio',
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'name',
          title: this.$t('司机姓名'),
          minWidth: 120
        },
        {
          field: 'contact',
          title: this.$t('司机手机号'),
          minWidth: 140
        },
        {
          field: 'idCard',
          title: this.$t('身份证号'),
          minWidth: 180
        },
        {
          field: 'license',
          title: this.$t('车牌号'),
          minWidth: 120
        }
      ]
    }
  },
  mounted() {
    this.getTableData()

    if (this.$refs['dialog'] && this.$refs['dialog'].ejsRef) {
      this.$refs['dialog'].ejsRef.show()
    }
  },
  methods: {
    handleRadioChange({ row }) {
      this.selectedRow = row
    },

    handleReset() {
      this.searchFormModel = {}
      this.handleSearch()
    },

    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },

    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },

    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },

    async getTableData() {
      try {
        this.loading = true

        const params = {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          defaultRules: [
            {
              field: 'status',
              operator: 'equal',
              value: 1
            }
          ],
          ...this.searchFormModel
        }

        const res = await this.$API.deliveryManagement.getDriverListApi(params)

        if (res.code === 200) {
          const { total = 0, records = [] } = res.data
          this.pageSettings.totalRecordsCount = Number(total)
          this.pageSettings.totalPages = Math.ceil(total / this.pageSettings.pageSize)
          this.tableData = records
        }
      } catch (error) {
        console.error('获取司机列表失败:', error)
      } finally {
        this.loading = false
      }
    },
    confirm() {
      if (!this.selectedRow) {
        this.$toast({ content: this.$t('请选择一个司机'), type: 'warning' })
        return
      }

      this.$emit('confirm-function', this.selectedRow)

      this.$refs['dialog'].ejsRef.hide()
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
