<!-- 选择关联ASN/VMI入库单 -->
<template>
  <div>
    <mt-dialog
      ref="dialog"
      :buttons="buttons"
      :header="header"
      @beforeClose="cancel"
      :height="800"
      :width="1200"
    >
      <div style="padding: 20px 10px">
        <collapse-search
          class="toggle-container"
          :is-grid-display="true"
          @reset="handleReset"
          @search="handleSearch"
        >
          <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
            <mt-form-item
              v-for="(item, index) in searchFormItems"
              :key="index"
              :label="$t(item.label)"
              :prop="item.prop"
            >
              <component
                :is="item.component"
                v-model="searchFormModel[item.prop]"
                v-bind="item.props"
                @change="item.onChange && item.onChange($event, item.prop)"
              />
            </mt-form-item>
          </mt-form>
        </collapse-search>
        <sc-table
          ref="scTableRef"
          row-id="id"
          show-overflow
          keep-source
          :loading="loading"
          :columns="columns"
          :table-data="tableData"
          :fix-height="440"
          :is-show-refresh-bth="true"
          @refresh="handleSearch"
        >
        </sc-table>
        <mt-page
          ref="pageRef"
          class="flex-keep custom-page"
          :page-settings="pageSettings"
          :total-pages="pageSettings.totalPages"
          @currentChange="handleCurrentChange"
          @sizeChange="handleSizeChange"
        />
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { getDialogSearchFormItems } from '../config/searchForm'
import { getDialogTableColumns } from '../config/index'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => ({})
    },
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    },
    api: {
      type: Function,
      default: null
    },
    selectedCodes: {
      type: Array,
      default: () => []
    }
  },
  components: {
    CollapseSearch,
    ScTable
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      searchFormModel: {},
      searchFormRules: {},
      loading: false,
      tableData: [],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      selectedDataCodes: [] // 存储已选数据的codes
    }
  },
  computed: {
    // 优先使用独立传入的props，如果没有则使用modalData中的值
    effectiveTitle() {
      return this.title || (this.modalData && this.modalData.title) || ''
    },
    effectiveType() {
      return this.type || (this.modalData && this.modalData.type) || ''
    },
    effectiveSelectedCodes() {
      const codes = (this.modalData && this.modalData.selectedCodes) || this.selectedCodes || []
      // 确保返回纯数组，避免响应式对象问题
      return Array.isArray(codes) ? [...codes] : []
    },
    header() {
      return this.effectiveTitle
    },
    tableRef() {
      return this.$refs.scTableRef?.$refs.xGrid
    },
    searchFormItems() {
      const items = getDialogSearchFormItems({ type: this.effectiveType })
      // 为创建时间字段添加onChange事件处理
      const dateFields = ['createTime']
      dateFields.forEach((field) => {
        const item = items.find((item) => item.prop === field)
        if (item) {
          item.onChange = this.handleDateTimeChange
        }
      })
      return items
    },
    columns() {
      const columns = getDialogTableColumns(this.effectiveType)
      console.log('Dialog columns for type:', this.effectiveType, columns)
      return columns
    }
  },
  watch: {
    effectiveSelectedCodes: {
      handler(newVal) {
        // 确保是数组并复制数据
        if (Array.isArray(newVal)) {
          this.selectedDataCodes = [...newVal]
        } else {
          this.selectedDataCodes = []
        }

        if (this.tableData.length > 0) {
          this.$nextTick(() => {
            setTimeout(() => {
              this.setTableSelection()
            }, 100)
          })
        }
      },
      immediate: true
    }
  },
  mounted() {
    // 初始化默认创建时间
    this.initDefaultCreateTime()

    // 确保对话框显示
    if (this.$refs['dialog'] && this.$refs['dialog'].ejsRef) {
      this.$refs['dialog'].ejsRef.show()
    }
  },
  methods: {
    // 根据effectiveType设置默认创建时间
    getDefaultCreateTime() {
      // 统一默认最近1个月
      const startDate = dayjs().subtract(1, 'month').startOf('month')
      const endDate = dayjs()

      return {
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        startDateFormatted: startDate.format('YYYY-MM-DD 00:00:00'),
        endDateFormatted: endDate.format('YYYY-MM-DD 23:59:59')
      }
    },

    // 初始化默认创建时间
    initDefaultCreateTime() {
      const defaultTime = this.getDefaultCreateTime()

      // 根据effectiveType设置不同的字段名
      let startField = 'createTimeStart'
      let endField = 'createTimeEnd'

      if (this.effectiveType === 'vmi') {
        startField = 'createTimeS'
        endField = 'createTimeE'
      }

      this.searchFormModel.createTime = [defaultTime.startDate, defaultTime.endDate]
      this.searchFormModel[startField] = this.getUnix(defaultTime.startDateFormatted)
      this.searchFormModel[endField] = this.getUnix(defaultTime.endDateFormatted)

      this.handleSearch()
    },

    handleDateTimeChange(e, field) {
      let startField = `${field}Start`
      let endField = `${field}End`

      if (this.effectiveType === 'vmi') {
        startField = `${field}S`
        endField = `${field}E`
      }

      if (e.startDate) {
        this.searchFormModel[startField] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[endField] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[startField] = null
        this.searchFormModel[endField] = null
      }
    },

    getUnix(val) {
      return dayjs(val).valueOf()
    },

    handleReset() {
      this.searchFormModel = {}
      // 重置时根据effectiveType设置默认创建时间
      this.initDefaultCreateTime()
      this.handleSearch()
    },

    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },

    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },

    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },

    async getTableData() {
      // 确定要使用的API函数
      let apiFunction = this.api

      // 如果没有直接提供api函数，则根据type自动选择
      if (!apiFunction) {
        if (this.effectiveType === 'delivery') {
          apiFunction = this.$API.deliveryManagement.pageDeliveryNoteListApi
        } else if (this.effectiveType === 'vmi') {
          apiFunction = this.$API.supplierVmiManagement.pageInventoryManagementApi
        } else {
          // 默认使用送货单API
          apiFunction = this.$API.deliveryManagement.pageDeliveryNoteListApi
        }
      }

      // 检查API函数有效性
      if (typeof apiFunction !== 'function') {
        this.$toast({ content: this.$t('未提供数据请求服务'), type: 'error' })
        return
      }

      try {
        this.loading = true
        const params = {
          forecastDelivery: 0, // 未预约的送货单
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        }

        if (this.effectiveType === 'delivery') {
          // 为送货单类型添加额外的参数: 查询状态为发货中的数据
          params.statusList = [2]
        }

        if (this.effectiveType === 'vmi') {
          // 为vmi类型添加额外的参数: 查询状态为已发货的数据
          params.status = 2
        }

        const res = await apiFunction(params)

        if (res.code === 200) {
          const { total = 0, records = [] } = res.data
          this.pageSettings.totalRecordsCount = Number(total)
          this.pageSettings.totalPages = Math.ceil(total / this.pageSettings.pageSize)
          this.tableData = records

          // 数据加载完成后设置选中状态
          this.$nextTick(() => {
            // 延迟一点时间确保表格完全渲染
            setTimeout(() => {
              this.setTableSelection()
            }, 100)
          })
        }
      } catch (error) {
        console.error('获取表格数据失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 设置表格选中状态
    setTableSelection() {
      if (!this.tableRef || !this.selectedDataCodes.length) {
        console.log('Early return: no tableRef or no selectedDataCodes')
        return
      }

      // 根据已选数据设置表格选中状态
      this.tableData.forEach((row) => {
        // 根据类型确定字段名
        let rowCode = null

        if (this.effectiveType === 'vmi') {
          // vmi类型
          rowCode = row.stockInCode
        } else {
          // delivery类型
          rowCode = row.deliveryCode
        }

        if (rowCode && this.selectedDataCodes.includes(rowCode)) {
          try {
            this.tableRef.setCheckboxRow(row, true)
          } catch (error) {
            console.error('Error setting checkbox:', error)
          }
        }
      })
    },

    confirm() {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (selectedRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      // delivery类型下只能选择同一公司的数据
      if (this.effectiveType === 'delivery') {
        const firstRecord = selectedRecords[0]
        const hasDifferentCompany = selectedRecords.some(
          (record) => record.companyCode !== firstRecord.companyCode
        )
        if (hasDifferentCompany) {
          this.$toast({
            content: this.$t('只能选择同一公司的送货单'),
            type: 'warning'
          })
          return
        }
      }

      this.$emit('confirm-function', selectedRecords)

      this.$refs['dialog'].ejsRef.hide()
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style></style>
