<!-- 随车人员管理弹窗 -->
<template>
  <div>
    <mt-dialog
      ref="dialog"
      :buttons="buttons"
      :header="header"
      @beforeClose="cancel"
      :height="500"
      :width="900"
    >
      <div style="padding: 20px 10px">
        <sc-table
          ref="scTableRef"
          row-id="id"
          show-overflow
          keep-source
          :columns="columns"
          :table-data="tableData"
          :fix-height="300"
          :edit-config="editConfig"
          :edit-rules="editRules"
          @edit-closed="editComplete"
        >
          <template slot="custom-tools">
            <vxe-button
              v-for="item in toolbar"
              :key="item.code"
              :status="item.status"
              :icon="item.icon"
              :loading="item.loading"
              size="small"
              @click="handleClickToolBar(item)"
            >
              {{ item.name }}
            </vxe-button>
          </template>
        </sc-table>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import {
  validateAccompanyList,
  validateIdCardDetailed,
  handleIdCardInput,
  handlePhoneInput,
  validateIdCard,
  validatePhone,
  extractIdCardInfo,
  formatIdCardDisplay
} from '@/utils/validator'

export default {
  name: 'AccompanyDialog',
  components: { ScTable },
  props: {
    modalData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      header: this.$t('随车人员管理'),
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      tableData: [],
      editRules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          { min: 2, max: 20, message: '姓名长度应在2-20个字符之间', trigger: 'blur' }
        ],
        idNo: [
          { required: true, message: '请输入身份证号', trigger: 'blur' },
          { validator: validateIdCard, trigger: 'blur' }
        ],
        phone: [{ validator: validatePhone, trigger: 'blur' }]
      }
    }
  },
  computed: {
    accompanyList() {
      return this.modalData?.accompanyList || []
    },
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'name',
          title: this.$t('姓名'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.name}
                  placeholder={this.$t('请输入姓名')}
                  clearable
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'idNo',
          title: this.$t('身份证号'),
          minWidth: 200,
          editRender: {},
          slots: {
            default: ({ row }) => {
              // 默认状态显示脱敏的身份证号
              if (row.idNo) {
                const displayText = row._showFullIdCard
                  ? formatIdCardDisplay(row.idNo, false) // 不脱敏，格式化显示
                  : formatIdCardDisplay(row.idNo, true) // 脱敏显示

                return [
                  <div
                    style={{
                      display: 'flex',
                      'align-items': 'center',
                      'justify-content': 'space-between'
                    }}>
                    <span style={{ flex: 1, 'font-family': 'monospace' }}>{displayText}</span>
                    <div
                      class={`id-card-toggle-btn ${row._showFullIdCard ? 'showing' : 'hidden'}`}
                      title={row._showFullIdCard ? this.$t('点击隐藏完整身份证号') : this.$t('点击显示完整身份证号')}
                      onClick={() => this.toggleIdCardDisplay(row)}>
                      <vxe-icon
                        name={row._showFullIdCard ? 'eye-fill' : 'eye-fill-close'}
                      />
                    </div>
                  </div>
                ]
              }
              return [<span style={{ color: '#999' }}>-</span>]
            },
            edit: ({ row }) => {
              return [
                <vxe-input
                  value={row.idNo}
                  placeholder={this.$t('请输入18位身份证号')}
                  clearable
                  transfer
                  maxlength={18}
                  onInput={(e) => this.handleIdNoInput(e.value, row)}
                />
              ]
            }
          }
        },
        {
          field: 'phone',
          title: this.$t('手机号'),
          minWidth: 150,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.phone}
                  placeholder={this.$t('请输入11位手机号')}
                  clearable
                  transfer
                  maxlength={11}
                  onInput={(e) => this.handlePhoneInput(e.value, row)}
                />
              ]
            }
          }
        },
        {
          field: 'age',
          title: this.$t('年龄'),
          width: 80,
          slots: {
            default: ({ row }) => {
              if (row.idNo) {
                const info = extractIdCardInfo(row.idNo)
                return info ? `${info.age}岁` : '-'
              }
              return '-'
            }
          }
        },
        {
          field: 'gender',
          title: this.$t('性别'),
          width: 80,
          slots: {
            default: ({ row }) => {
              if (row.idNo) {
                const info = extractIdCardInfo(row.idNo)
                return info ? info.gender : '-'
              }
              return '-'
            }
          }
        }
      ]
    },
    editConfig() {
      return {
        enabled: true,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    toolbar() {
      let btns = [
        { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
        { code: 'delete', name: this.$t('删除'), status: 'info', loading: false },
        { code: 'import', name: this.$t('批量导入'), status: 'primary', loading: false },
        { code: 'template', name: this.$t('下载模板'), status: 'default', loading: false }
      ]
      return btns
    }
  },
  mounted() {
    // 延迟初始化，确保modalData已经传递
    this.$nextTick(() => {
      this.initTableData()
    })

    if (this.$refs['dialog'] && this.$refs['dialog'].ejsRef) {
      this.$refs['dialog'].ejsRef.show()
    }
  },
  watch: {
    // 监听modalData变化，确保数据正确回显
    modalData: {
      handler(newVal) {
        if (newVal && newVal.accompanyList) {
          this.$nextTick(() => {
            this.initTableData()
          })
        }
      },
      immediate: true,
      deep: true
    },
    // 监听accompanyList变化
    accompanyList: {
      handler() {
        this.$nextTick(() => {
          this.initTableData()
        })
      },
      immediate: true
    }
  },
  methods: {
    // 初始化表格数据
    initTableData() {
      const accompanyList = this.accompanyList || []

      const newTableData = accompanyList.map((item, index) => ({
        id: `accompany_${index}`,
        name: item.name || '',
        idNo: item.idNo || '',
        phone: item.phone || '',
        ...item
      }))

      // 使用Vue.set确保响应式更新
      this.$set(this, 'tableData', newTableData)

      // 如果表格已经初始化，则重新加载数据
      if (this.tableRef) {
        this.$nextTick(() => {
          // 尝试多种方式来更新表格数据
          if (this.tableRef.loadData) {
            this.tableRef.loadData(this.tableData)
          } else if (this.tableRef.reloadData) {
            this.tableRef.reloadData(this.tableData)
          } else if (this.tableRef.setData) {
            this.tableRef.setData(this.tableData)
          }
        })
      }
    },

    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['delete']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          this.handleDelete()
          break
        case 'import':
          this.handleImport()
          break
        case 'template':
          this.handleDownloadTemplate()
          break
        default:
          break
      }
    },
    handleAdd(item = {}) {
      // 生成唯一ID
      const newItem = {
        id: `accompany_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        name: '',
        idNo: '',
        phone: '',
        ...item
      }
      this.tableRef.insert([newItem])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.tableRef.setEditRow(currentViewRecords[0])
      })
    },

    handleDelete() {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请先选择要删除的随车人员'), type: 'warning' })
        return
      }

      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认删除选中的${selectedRecords.length}位随车人员？`),
          type: 'warning'
        },
        success: () => {
          this.tableRef.removeCheckboxRow()
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
        }
      })
    },

    editComplete() {
      // 编辑完成后的处理，可以在这里进行实时验证
      const currentViewRecords = this.tableRef.getTableData().visibleData
      console.log('编辑完成', currentViewRecords)
    },

    confirm() {
      // 验证数据
      const currentViewRecords = this.tableRef.getTableData().visibleData

      if (currentViewRecords.length === 0) {
        this.$toast({ content: this.$t('请至少添加一位随车人员'), type: 'warning' })
        return
      }

      // 使用增强的验证函数
      const validationResult = validateAccompanyList(currentViewRecords)
      if (!validationResult.valid) {
        // 显示详细的错误信息
        if (validationResult.details && validationResult.details.length > 0) {
          this.showValidationDetails(validationResult.details)
        } else {
          this.$toast({ content: this.$t(validationResult.message), type: 'warning' })
        }
        return
      }

      // 返回随车人员数据，包含提取的身份证信息
      const accompanyList = currentViewRecords.map(({ id: _id, ...rest }) => {
        const result = { ...rest }

        // 如果身份证号有效，添加提取的信息
        if (rest.idNo) {
          const info = extractIdCardInfo(rest.idNo)
          if (info) {
            result.age = info.age
            result.gender = info.gender
            result.birthDate = info.birthDate
          }
        }

        return result
      })

      console.log('confirm accompanyList', accompanyList)

      this.$emit('confirm-function', accompanyList)
      this.$refs['dialog'].ejsRef.hide()
    },

    cancel() {
      this.$emit('cancel-function')
    },

    // 身份证号输入处理
    handleIdNoInput(value, row) {
      console.log('handleIdNoInput 原始输入:', { value, type: typeof value, row: row.name })

      // 启用调试模式
      row.idNo = handleIdCardInput(value, false)

      console.log('handleIdNoInput 处理后:', { idNo: row.idNo, length: row.idNo?.length })

      // 实时验证身份证号
      if (row.idNo && row.idNo.length === 18) {
        const validation = validateIdCardDetailed(row.idNo)
        console.log('handleIdNoInput', row)
        console.log('validation', validation)
        if (!validation.valid) {
          this.$toast({
            content: validation.error,
            type: 'warning',
            duration: 3000
          })
        } else {
          // 身份证号有效，提取信息并显示
          const info = extractIdCardInfo(row.idNo)
          if (info) {
            this.$toast({
              content: `身份证验证通过：${info.gender}，${info.age}岁`,
              type: 'success',
              duration: 2000
            })
          }
        }

        // 触发表格重新渲染以更新年龄和性别显示
        this.$nextTick(() => {
          if (this.tableRef) {
            this.tableRef.updateData()
          }
        })
      }
    },

    // 手机号输入处理
    handlePhoneInput(value, row) {
      console.log('handlePhoneInput 原始输入:', { value, type: typeof value, row: row.name })

      // 启用调试模式
      row.phone = handlePhoneInput(value, false)

      console.log('handlePhoneInput 处理后:', { phone: row.phone, length: row.phone?.length })
    },

    // 切换身份证号显示状态
    toggleIdCardDisplay(row) {
      // 使用Vue.set确保响应式更新
      this.$set(row, '_showFullIdCard', !row._showFullIdCard)

      // 强制更新表格显示
      this.$nextTick(() => {
        if (this.tableRef) {
          this.tableRef.updateData()
        }
      })
    },

    // 显示详细的验证错误信息
    showValidationDetails(details) {
      if (!details || details.length === 0) return

      const errorList = details
        .map((detail) => `第${detail.index}行(${detail.name}): ${detail.errors.join('、')}`)
        .join('\n')

      this.$dialog({
        data: {
          title: '数据验证错误',
          message: errorList,
          type: 'warning'
        }
      })
    },

    // 批量导入
    handleImport() {
      // 创建文件输入元素
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = '.xlsx,.xls,.csv'
      input.onchange = (event) => {
        const file = event.target.files[0]
        if (!file) return

        this.parseImportFile(file)
      }
      input.click()
    },

    // 解析导入文件
    parseImportFile(file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          // 这里可以使用xlsx库来解析Excel文件
          // 为了简化，这里只处理CSV格式
          if (file.name.endsWith('.csv')) {
            this.parseCsvData(e.target.result)
          } else {
            this.$toast({ content: '暂时只支持CSV格式文件', type: 'warning' })
          }
        } catch (error) {
          this.$toast({ content: '文件解析失败：' + error.message, type: 'error' })
        }
      }

      if (file.name.endsWith('.csv')) {
        reader.readAsText(file, 'UTF-8')
      } else {
        reader.readAsBinaryString(file)
      }
    },

    // 解析CSV数据
    parseCsvData(csvText) {
      const lines = csvText.split('\n').filter((line) => line.trim())
      if (lines.length < 2) {
        this.$toast({ content: '文件格式错误，至少需要标题行和一行数据', type: 'warning' })
        return
      }

      // 跳过标题行
      const dataLines = lines.slice(1)
      const importedData = []

      dataLines.forEach((line, index) => {
        const columns = line.split(',').map((col) => col.trim().replace(/"/g, ''))
        if (columns.length >= 2) {
          const item = {
            id: `import_${Date.now()}_${index}`,
            name: columns[0] || '',
            idNo: columns[1] || '',
            phone: columns[2] || ''
          }

          // 验证导入的数据
          if (item.name && item.idNo) {
            importedData.push(item)
          }
        }
      })

      if (importedData.length === 0) {
        this.$toast({ content: '没有找到有效的数据行', type: 'warning' })
        return
      }

      // 验证导入的数据
      const validationResult = validateAccompanyList(importedData)
      if (!validationResult.valid) {
        this.$dialog({
          data: {
            title: '导入数据验证失败',
            message: `发现${validationResult.details.length}个错误，是否继续导入有效数据？`,
            type: 'warning'
          },
          success: () => {
            this.addImportedData(importedData.filter((item) => validateAccompanyList([item]).valid))
          }
        })
      } else {
        this.addImportedData(importedData)
      }
    },

    // 添加导入的数据
    addImportedData(data) {
      data.forEach((item) => {
        this.tableRef.insert([item])
      })
      this.$toast({ content: `成功导入${data.length}条记录`, type: 'success' })
    },

    // 下载模板
    handleDownloadTemplate() {
      const csvContent =
        '姓名,身份证号,手机号\n张三,11010519491231002X,13812345678\n李四,110105194912310021,15987654321'
      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', '随车人员导入模板.csv')
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      this.$toast({ content: '模板下载成功', type: 'success' })
    }
  }
}
</script>

<style scoped>
/* 身份证号切换按钮样式 */
.id-card-toggle-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  user-select: none;
  min-width: 24px;
  height: 24px;
}
</style>
