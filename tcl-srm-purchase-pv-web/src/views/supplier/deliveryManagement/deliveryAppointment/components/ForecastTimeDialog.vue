<!-- 选择预约时间弹窗 -->
<template>
  <div>
    <mt-dialog
      ref="dialog"
      :buttons="buttons"
      :header="header"
      @beforeClose="cancel"
      :height="600"
      :width="1000"
    >
      <div style="padding: 20px 10px">
        <collapse-search
          class="toggle-container"
          :is-grid-display="true"
          @reset="handleReset"
          @search="handleSearch"
        >
          <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
            <mt-form-item :label="$t('预约日期')" prop="forecastDate">
              <mt-date-picker
                v-model="searchFormModel.forecastDate"
                :placeholder="$t('请选择预约日期')"
                :min="new Date()"
                :allow-edit="false"
                :show-clear-button="false"
                @change="handleDateChange"
              />
            </mt-form-item>
          </mt-form>
        </collapse-search>
        <sc-table
          ref="scTableRef"
          row-id="id"
          show-overflow
          keep-source
          :loading="loading"
          :columns="columns"
          :table-data="tableData"
          :fix-height="300"
          :is-show-refresh-bth="true"
          @radio-change="handleRadioChange"
        />
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import dayjs from 'dayjs'

export default {
  name: 'ForecastTimeDialog',
  props: {
    modalData: {
      type: Object,
      default: () => ({})
    }
  },
  components: { CollapseSearch, ScTable },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      searchFormModel: {
        forecastDate: new Date()
      },
      loading: false,
      tableData: [],
      selectedRow: null
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    rowData() {
      return this.modalData.rowData || {}
    },
    columns() {
      return [
        {
          width: 50,
          type: 'radio',
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'appointmentTime',
          title: this.$t('时间段'),
          minWidth: 140
        },
        {
          field: 'isHave',
          title: this.$t('装车能力'),
          minWidth: 120,
          formatter: ({ cellValue }) => {
            // 确保数据类型一致性
            const normalizedValue = Number(cellValue)
            const abilityMap = {
              1: this.$t('有'),
              2: this.$t('无')
            }
            return abilityMap[normalizedValue] || '-'
          }
        },
        {
          field: 'warehouseCode',
          title: this.$t('库存地点'),
          minWidth: 200,
          formatter: ({ cellValue, row }) => {
            return cellValue ? `${cellValue}-${row.warehouse}` : ''
          }
        },
        {
          field: 'sendAddressCode',
          title: this.$t('送货地址'),
          minWidth: 200,
          formatter: ({ cellValue, row }) => {
            return cellValue ? `${cellValue}-${row.sendAddress}` : ''
          }
        }
      ]
    }
  },
  mounted() {
    this.getTableData()

    if (this.$refs['dialog'] && this.$refs['dialog'].ejsRef) {
      this.$refs['dialog'].ejsRef.show()
    }
  },
  methods: {
    handleRadioChange({ row }) {
      this.selectedRow = row
    },

    handleDateChange() {
      this.handleSearch()
    },

    handleReset() {
      this.searchFormModel = {
        forecastDate: new Date()
      }
      this.handleSearch()
    },

    handleSearch() {
      this.getTableData()
    },

    async getTableData() {
      try {
        this.loading = true

        const params = {
          timeInfo: this.searchFormModel.forecastDate
            ? dayjs(this.searchFormModel.forecastDate).format('YYYY-MM-DD')
            : dayjs().format('YYYY-MM-DD')
        }

        if (this.rowData?.forecastType === 1) {
          params.vmiOrderCode = this.rowData?.deliveryCodes.join(',')
        } else {
          params.deliveryCode = this.rowData?.deliveryCodes.join(',')
        }

        const res = await this.$API.deliveryManagement.getForecastTimeApi(params)

        if (res.code === 200) {
          this.tableData = res.data || []
        }
      } catch (error) {
        console.error('获取预约时间失败:', error)
      } finally {
        this.loading = false
      }
    },

    confirm() {
      if (!this.selectedRow) {
        this.$toast({ content: this.$t('请选择一个时间段'), type: 'warning' })
        return
      }

      // 拼接日期和时间段
      const appointmentTime = this.selectedRow.appointmentTime || ''
      const forecastDateStr = this.searchFormModel.forecastDate
        ? dayjs(this.searchFormModel.forecastDate).format('YYYY-MM-DD')
        : dayjs().format('YYYY-MM-DD')

      // 构建完整的返回数据
      const result = {
        ...this.selectedRow,
        appointmentTime: `${forecastDateStr} ${appointmentTime}` // 完整的预约时间
      }

      this.$emit('confirm-function', result)
      this.$refs['dialog'].ejsRef.hide()
    },

    cancel() {
      this.selectedRow = null
      this.$emit('cancel-function')
    }
  }
}
</script>
