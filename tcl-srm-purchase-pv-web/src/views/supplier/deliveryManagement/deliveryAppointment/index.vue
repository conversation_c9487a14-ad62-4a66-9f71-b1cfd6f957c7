<!-- 预约送货 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item
          v-for="(item, index) in searchFormItems"
          :key="index"
          :label="$t(item.label)"
          :prop="item.prop"
        >
          <component
            :is="item.component"
            v-model="searchFormModel[item.prop]"
            v-bind="item.props"
            @change="item.onChange && item.onChange($event, item.prop)"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="scTableRef"
      row-id="id"
      grid-id="b62206b6-eb33-40eb-b206-139729393a98"
      show-overflow
      keep-source
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
      @edit-closed="editComplete"
    >
      <template #custom-tools>
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          v-bind="item"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { getSearchFormItems } from './config/searchForm'
import { getHeadersFileName, download } from '@/utils/utils'
import mixin from './config/mixin'
import dayjs from 'dayjs'

// 操作类型常量
const OPERATION_TYPES = {
  ADD: 'add',
  CANCEL_EDIT: 'cancelEdit',
  SUBMIT: 'submit',
  UPGRADE: 'upgrade',
  CANCEL: 'cancel',
  IMPORT: 'import',
  EXPORT: 'export',
  PRINT: 'print'
}

// 操作配置
const OPERATION_CONFIG = {
  [OPERATION_TYPES.SUBMIT]: {
    title: '提示',
    message: '确认提交选中的数据？',
    api: 'submitDeliveryAppointmentApi',
    successMessage: '提交成功！',
    requireSelection: true,
    validate: (records) => {
      // 验证选中的记录是否都是可提交状态
      const invalidRecords = records.filter((record) => record.status !== 1)
      if (invalidRecords.length > 0) {
        return {
          valid: false,
          message: `存在${invalidRecords.length}条记录状态不是"未预约"，无法提交`
        }
      }
      return { valid: true }
    }
  },
  [OPERATION_TYPES.CANCEL]: {
    title: '提示',
    message: '确认取消选中的数据？',
    api: 'cancelDeliveryAppointmentApi',
    successMessage: '取消成功！',
    requireSelection: true,
    validate: (records) => {
      // 验证选中的记录是否都是可取消状态
      // 条件：(未预约 || 审批中 || 预约拒绝 || 预约成功) && 未入园
      const invalidRecords = records.filter((record) => {
        const validStatus = [1, 2, 4, 3].includes(record.status) // 未预约(1) || 审批中(2) || 预约拒绝(4) || 预约成功(3)
        const notEntered = record.intoStatus === 2 // 未入园(2)
        return !(validStatus && notEntered)
      })

      if (invalidRecords.length > 0) {
        return {
          valid: false,
          message: `只能取消未入园状态的预约单！`
        }
      }
      return { valid: true }
    }
  },
  [OPERATION_TYPES.PRINT]: {
    title: '提示',
    message: '确认打印选中的数据？',
    api: 'printDeliveryAppointmentApi',
    successMessage: '打印成功！',
    requireSelection: true,
    validate: (records) => {
      // 验证选中的记录是否都是可打印状态
      const invalidRecords = records.filter((record) => record.status !== 3)
      if (invalidRecords.length > 0) {
        return {
          valid: false,
          message: `存在${invalidRecords.length}条记录状态不是"预约成功"，无法打印`
        }
      }
      return { valid: true }
    }
  }
}

export default {
  name: 'DeliveryAppointment',
  components: { CollapseSearch, ScTable },
  mixins: [mixin],
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      loading: false,
      tableData: [],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      }
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef?.$refs.xGrid
    },
    searchFormItems() {
      const items = getSearchFormItems()
      // 为创建时间、更新时间、预约时间字段添加onChange事件处理
      const dateFields = ['createTime', 'updateTime', 'forecastTime']
      dateFields.forEach((field) => {
        const item = items.find((item) => item.prop === field)
        if (item) {
          item.onChange = this.handleDateTimeChange
        }
      })
      return items
    }
  },
  created() {
    this.getTableData()
  },

  methods: {
    handleDateTimeChange(e, field) {
      if (!e?.startDate) {
        this.searchFormModel[`${field}S`] = null
        this.searchFormModel[`${field}E`] = null
        return
      }

      const startDate = dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
      const endDate = dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')

      this.searchFormModel[`${field}S`] = this.getUnix(startDate)
      this.searchFormModel[`${field}E`] = this.getUnix(endDate)
    },

    getUnix(val) {
      return dayjs(val).valueOf()
    },

    handleReset() {
      this.searchFormModel = {}
      this.handleSearch()
    },

    async handleSearch() {
      this.currentPage = 1
      // 清除升级编辑状态
      this.clearUpgradeEditingRows()
      await this.getTableData()
    },

    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },

    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },

    async getTableData() {
      try {
        this.loading = true

        const params = {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        }

        const res = await this.$API.deliveryManagement.pageDeliveryAppointmentApi(params)

        if (res.code === 200) {
          const { total = 0, records = [] } = res.data
          this.pageSettings.totalRecordsCount = Number(total)
          this.pageSettings.totalPages = Math.ceil(total / this.pageSettings.pageSize)

          // 确保每条记录都有必要的数组字段，避免双击编辑时出现undefined错误
          this.tableData = records.map(record => {
            // 处理关联ASN/VMI入库单号的回显
            let deliveryCodes = []
            let deliveryCodeOptions = []
            let deliveryIds = []

            if (record.deliveryCode && typeof record.deliveryCode === 'string') {
              // 将后端的逗号分隔字符串转换为数组
              deliveryCodes = record.deliveryCode.split(',').filter(code => code.trim())
              deliveryCodeOptions = deliveryCodes.map(code => ({
                value: code,
                label: code
              }))
            }

            if (record.deliveryId && typeof record.deliveryId === 'string') {
              // 将后端的逗号分隔字符串转换为数组
              deliveryIds = record.deliveryId.split(',').filter(id => id.trim())
            }

            return {
              ...record,
              deliveryCodes,
              deliveryCodeOptions,
              deliveryIds,
              accompanyList: record.accompanyList || []
            }
          })
        }
      } catch (error) {
        console.error('获取表格数据失败:', error)
      } finally {
        this.loading = false
      }
    },

    async handleClickToolBar(item) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const operation = OPERATION_CONFIG[item.code]

      if (operation?.requireSelection && selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
        return
      }

      if (operation?.validate) {
        const validation = operation.validate(selectedRecords)
        if (!validation.valid) {
          this.$toast({ content: this.$t(validation.message), type: 'warning' })
          return
        }
      }

      const actionMap = {
        [OPERATION_TYPES.ADD]: () => this.handleAdd(),
        [OPERATION_TYPES.CANCEL_EDIT]: () => this.handleSearch(),
        [OPERATION_TYPES.SUBMIT]: () => this.handleOperate(selectedRecords, OPERATION_TYPES.SUBMIT),
        [OPERATION_TYPES.UPGRADE]: () =>
          this.handleUpgradeOperate(selectedRecords),
        [OPERATION_TYPES.CANCEL]: () => this.handleOperate(selectedRecords, OPERATION_TYPES.CANCEL),
        [OPERATION_TYPES.IMPORT]: () => this.handleImport(),
        [OPERATION_TYPES.EXPORT]: () => this.handleExport(item),
        [OPERATION_TYPES.PRINT]: () =>
          this.showConfirmDialog('打印', () => this.handlePrint(selectedRecords))
      }

      const action = actionMap[item.code]
      if (action) {
        const result = action()
        if (result && typeof result.then === 'function') {
          await result
        }
      }
    },

    showConfirmDialog(action, callback) {
      return this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认${action}？`)
        },
        success: callback
      })
    },

    async handleAdd() {
      const item = {
        status: 1,
        syncStatus: 0,
        intoStatus: 2,
        dataSource: 0,
        // 初始化数组字段，避免双击编辑时出现undefined错误
        deliveryCodes: [],
        deliveryCodeOptions: [],
        deliveryIds: [],
        accompanyList: []
      }

      try {
        await this.tableRef.insert([item])
        const currentViewRecords = this.tableRef.getTableData().visibleData
        this.tableRef.setEditRow(currentViewRecords[0])
      } catch (error) {
        console.error('新增行失败:', error)
        this.$toast({ content: this.$t('新增行失败'), type: 'error' })
      }
    },

    handleOperate(selectedRecords, type, extraParams = {}) {
      const operation = OPERATION_CONFIG[type]
      if (!operation) {
        this.$toast({ content: this.$t('不支持的操作类型'), type: 'error' })
        return
      }

      this.$dialog({
        data: {
          title: this.$t(operation.title),
          message: this.$t(operation.message),
          type: 'warning'
        },
        success: async () => {
          try {
            const params = {
              ids: selectedRecords.map((item) => item.id),
              ...extraParams
            }

            const res = await this.$API.deliveryManagement[operation.api](params.ids)

            if (res.code === 200) {
              this.$toast({
                content: this.$t(operation.successMessage),
                type: 'success'
              })
              await this.handleSearch()
            } else {
              throw new Error(res.message || this.$t(`${operation.title}失败`))
            }
          } catch (error) {
            console.error(`${operation.title}失败:`, error)
          }
        }
      })
    },

    // 处理预约升级操作 - 直接进入编辑状态
    handleUpgradeOperate(selectedRecords) {
      // 只能选择一行进行预约升级
      if (selectedRecords.length !== 1) {
        this.$toast({
          content: this.$t('请选择一行数据进行预约升级'),
          type: 'warning'
        })
        return
      }

      const row = selectedRecords[0]
      const rowId = row.id

      // 设置升级编辑状态，允许这行进入编辑模式
      this.setUpgradeEditingRows([rowId])

      // 立即进入编辑状态
      this.$nextTick(() => {
        if (this.tableRef) {
          this.tableRef.setEditRow(row)
        }
      })
    },

    async handleImport() {
      return new Promise((resolve) => {
        this.$dialog({
          modal: () => import('@/components/uploadDialog'),
          data: {
            title: this.$t('导入'),
            importApi: this.$API.deliveryManagement.importDeliveryAppointmentApi,
            downloadTemplateApi:
              this.$API.deliveryManagement.downloadDeliveryAppointmentTemplateApi,
            paramsKey: 'excel'
          },
          success: async () => {
            await this.handleSearch()
            resolve()
          },
          cancel: () => {
            resolve()
          }
        })
      })
    },

    async handleExport(item) {
      try {
        item.loading = true

        const params = {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        }

        const res = await this.$API.deliveryManagement.exportDeliveryAppointmentApi(params)
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      } catch (error) {
        console.error('导出失败:', error)
        this.$toast({ content: error || this.$t('导出失败'), type: 'error' })
      } finally {
        item.loading = false
      }
    },

    async handlePrint(selectedRecords) {
      // 验证选中记录
      if (selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
        return
      }

      // 验证预约类型一致性
      const forecastTypes = [...new Set(selectedRecords.map((item) => item.forecastType))]
      if (forecastTypes.length > 1) {
        this.$toast({ content: this.$t('请选择同一预约类型的预约单进行打印！'), type: 'warning' })
        return
      }

      try {
        // 构建请求参数
        const params = {
          ids: []
        }
        selectedRecords.forEach((item) => {
          let arr = item.deliveryId?.split(',') || []
          arr.forEach((aitem) => {
            params.ids.push(aitem)
          })
        })

        // 验证参数有效性
        if (params.ids.length === 0) {
          this.$toast({ content: this.$t('无有效的送货单ID！'), type: 'warning' })
          return
        }

        // 选择对应的API
        const api =
          selectedRecords[0].forecastType === 0
            ? this.$API.deliveryManagement.printDeliveryNoteListApi
            : this.$API.supplierVmiManagement.printInventoryManagementApi

        // 调用打印API
        const res = await api(params)

        // 验证响应
        if (!res || !res.data) {
          throw new Error(this.$t('打印数据获取失败'))
        }

        // 处理打印内容
        this.createPrintFrame(res.data)
      } catch (error) {
        console.error('打印失败:', error)
        this.$toast({
          content: error.message || this.$t('打印失败，请重试'),
          type: 'error'
        })
      }
    },

    /**
     * 创建打印框架
     * @param {*} content 打印内容
     */
    createPrintFrame(content) {
      const timestamp = Date.now()
      const frameId = `printPdf${timestamp}`
      let pdfUrl = null
      let iframe = null

      try {
        // 创建Blob URL
        pdfUrl = window.URL.createObjectURL(
          new Blob([content], { type: 'text/html;charset=utf-8' })
        )

        // 创建iframe元素
        iframe = this.createIframe(frameId, pdfUrl)

        // 添加到DOM
        document.body.appendChild(iframe)

        // 监听iframe加载完成事件
        this.setupPrintHandler(iframe, frameId, pdfUrl)

      } catch (error) {
        console.error('创建打印框架失败:', error)
        this.cleanupPrintFrame(iframe, pdfUrl)
        this.$toast({
          content: this.$t('打印准备失败，请重试'),
          type: 'error'
        })
      }
    },

    /**
     * 创建iframe元素
     * @param {string} frameId 框架ID
     * @param {string} pdfUrl PDF URL
     * @returns {HTMLIFrameElement} iframe元素
     */
    createIframe(frameId, pdfUrl) {
      const iframe = document.createElement('iframe')

      // 设置iframe属性
      Object.assign(iframe, {
        id: frameId,
        name: frameId,
        src: pdfUrl
      })

      // 设置样式
      Object.assign(iframe.style, {
        display: 'none',
        border: 'none',
        pageBreakBefore: 'always',
        width: '0',
        height: '0'
      })

      return iframe
    },

    /**
     * 设置打印处理器
     * @param {HTMLIFrameElement} iframe iframe元素
     * @param {string} frameId 框架ID
     * @param {string} pdfUrl PDF URL
     */
    setupPrintHandler(iframe, frameId, pdfUrl) {
      let isHandled = false

      const handlePrint = () => {
        if (isHandled) return
        isHandled = true

        try {
          this.doPrint(frameId)
        } catch (error) {
          console.error('打印执行失败:', error)
        } finally {
          // 延迟清理，确保打印完成
          setTimeout(() => {
            this.cleanupPrintFrame(iframe, pdfUrl)
          }, 2000)
        }
      }

      // 监听加载完成事件
      iframe.addEventListener('load', handlePrint, { once: true })

      // 备用超时处理
      setTimeout(() => {
        if (!isHandled) {
          console.warn('iframe加载超时，强制执行打印')
          handlePrint()
        }
      }, 5000)
    },

    /**
     * 清理打印框架资源
     * @param {HTMLElement} iframe iframe元素
     * @param {string} pdfUrl Blob URL
     */
    cleanupPrintFrame(iframe, pdfUrl) {
      if (!iframe && !pdfUrl) return

      try {
        // 清理iframe
        if (iframe) {
          // 移除事件监听器
          iframe.onload = null
          iframe.onerror = null

          // 从DOM中移除
          if (iframe.parentNode) {
            iframe.parentNode.removeChild(iframe)
          }
        }

        // 清理Blob URL
        if (pdfUrl) {
          window.URL.revokeObjectURL(pdfUrl)
        }

      } catch (error) {
        console.warn('清理打印资源时出错:', error)
      }
    },

    /**
     * 执行打印操作
     * @param {string} frameId iframe的ID
     */
    doPrint(frameId) {
      try {
        const iframe = document.getElementById(frameId)
        if (!iframe) {
          throw new Error(`打印框架未找到: ${frameId}`)
        }

        const contentWindow = iframe.contentWindow
        if (!contentWindow) {
          throw new Error('无法访问打印框架内容')
        }

        // 检查浏览器打印支持
        if (typeof contentWindow.print !== 'function') {
          throw new Error('当前浏览器不支持打印功能')
        }

        // 执行打印
        this.executePrint(contentWindow)

      } catch (error) {
        console.error('执行打印时出错:', error)
        this.$toast({
          content: error.message || this.$t('打印执行失败，请重试'),
          type: 'error'
        })
      }
    },

    /**
     * 执行实际的打印操作
     * @param {Window} contentWindow iframe的window对象
     */
    executePrint(contentWindow) {
      try {
        // 设置打印样式
        this.setPrintStyles(contentWindow.document)

        // 执行打印
        contentWindow.print()

        // 打印成功提示
        this.$toast({
          content: this.$t('打印任务已发送'),
          type: 'success'
        })

      } catch (error) {
        console.error('打印执行失败:', error)
        throw new Error('打印执行失败')
      }
    },

    /**
     * 设置打印样式
     * @param {Document} doc iframe文档对象
     */
    setPrintStyles(doc) {
      try {
        // 检查是否已存在打印样式
        if (doc.getElementById('print-styles')) {
          return
        }

        // 创建打印样式
        const style = doc.createElement('style')
        style.id = 'print-styles'
        style.textContent = `
          @media print {
            body { margin: 0; padding: 10px; }
            @page { margin: 1cm; size: A4; }
            .no-print { display: none !important; }
            table { page-break-inside: avoid; }
            tr { page-break-inside: avoid; }
          }
        `

        // 添加到文档头部
        const head = doc.head || doc.getElementsByTagName('head')[0]
        if (head) {
          head.appendChild(style)
        }

      } catch (error) {
        console.warn('设置打印样式失败:', error)
      }
    },

    async editComplete(args) {
      const { row } = args
      if (!args.$event) return

      // 检查是否是因为弹框相关操作导致的编辑完成事件
      const target = args.$event.target || args.$event.srcElement

      // 检查是否点击了弹框触发按钮
      const isDialogTrigger =
        target &&
        (target.classList.contains('vxe-icon') ||
          target.closest('.vxe-icon') ||
          target.getAttribute('name') === 'search' ||
          target.getAttribute('name') === 'edit')

      // 检查是否点击了弹框内的元素
      const isDialogElement =
        target &&
        (target.closest('.vxe-modal') ||
          target.closest('.vxe-modal--wrapper') ||
          target.closest('.e-dlg-container') ||
          target.closest('.el-dialog') ||
          target.closest('.el-dialog__wrapper') ||
          target.closest('.ant-modal') ||
          target.closest('.ant-modal-wrap') ||
          target.closest('[role="dialog"]') ||
          target.closest('.dialog') ||
          target.closest('.modal') ||
          // 检查是否是弹框的遮罩层
          target.classList.contains('vxe-modal--wrapper') ||
          target.classList.contains('el-dialog__wrapper') ||
          target.classList.contains('ant-modal-wrap') ||
          // 检查是否是弹框内的表单元素
          target.closest('.vxe-select') ||
          target.closest('.vxe-input') ||
          target.closest('.vxe-button') ||
          target.closest('.el-select') ||
          target.closest('.el-input') ||
          target.closest('.el-button'))

      // 如果是弹框相关的操作，不执行校验和保存逻辑
      if (isDialogTrigger || isDialogElement) {
        return
      }

      if (target && target.innerText === this.$t('取消编辑')) {
        // 清除该行的升级编辑状态
        this.clearUpgradeEditingRows([row.id])
        this.handleSearch()
        return
      }

      try {
        const valid = await this.tableRef.validate([row])
        if (valid) {
          this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
          return
        }

        const params = {
          ...row,
          id: row.id.includes('row_') ? null : row.id
        }

        params.deliveryCode = params?.deliveryCodes?.join(',')
        params.deliveryId = params?.deliveryIds?.join(',')

        delete params.deliveryCodes
        delete params.deliveryIds
        delete params.deliveryCodeOptions

        const api = this.$API.deliveryManagement.saveDeliveryAppointmentApi
        const res = await api(params)

        if (res.code === 200) {
          this.$toast({ content: this.$t('保存成功！'), type: 'success' })
          // 清除该行的升级编辑状态
          this.clearUpgradeEditingRows([row.id])
          await this.handleSearch()
        } else {
          throw new Error(res.message || this.$t('保存失败'))
        }
      } catch (error) {
        console.error('保存失败:', error)
        this.tableRef.setEditRow(row)
      }
    }
  }
}
</script>
