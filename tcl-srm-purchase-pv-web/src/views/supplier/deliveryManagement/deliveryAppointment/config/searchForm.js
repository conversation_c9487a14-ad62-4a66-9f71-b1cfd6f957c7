/**
 * 预约送货搜索表单配置
 */
import { i18n } from '@/main.js'
import { statusOptions, deliveryTypeOptions, onWayStatusOptions, dataSourceOptions } from './index'

export const getSearchFormItems = () => [
  {
    label: i18n.t('预约号'),
    prop: 'forecastCode',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('关联ASN/VMI入库单号'),
    prop: 'deliveryCode',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('预约状态'),
    prop: 'status',
    component: 'mt-select',
    props: {
      placeholder: i18n.t('请选择'),
      showClearButton: true,
      dataSource: statusOptions
    }
  },
  {
    label: i18n.t('在途状态'),
    prop: 'onWayStatus',
    component: 'mt-select',
    props: {
      placeholder: i18n.t('请选择'),
      showClearButton: true,
      dataSource: onWayStatusOptions
    }
  },
  {
    label: i18n.t('司机姓名'),
    prop: 'driverName',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('车牌号'),
    prop: 'carNo',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('司机身份证号'),
    prop: 'driverIdNo',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('司机手机号'),
    prop: 'driverPhone',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('预约时间'),
    prop: 'forecastTime',
    component: 'mt-date-range-picker',
    props: {
      placeholder: i18n.t('请选择'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('预约来源'),
    prop: 'dataSource',
    component: 'mt-select',
    props: {
      placeholder: i18n.t('请选择'),
      showClearButton: true,
      dataSource: dataSourceOptions
    }
  },
  {
    label: i18n.t('TMS预约单号'),
    prop: 'outerApptNo',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('库存地点'),
    prop: 'warehouseCode',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('送货地址'),
    prop: 'sendAddress',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('备注'),
    prop: 'remark',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  }
]

export const getDialogSearchFormItems = ({ type }) => {
  let searchFormItems = []

  if (type === 'delivery') {
    searchFormItems = [
      {
        label: i18n.t('送货单号'),
        prop: 'deliveryCode',
        component: 'mt-input',
        props: {
          placeholder: i18n.t('请输入'),
          showClearButton: true
        }
      },
      {
        label: i18n.t('送货单类型'),
        prop: 'deliveryType',
        component: 'mt-select',
        props: {
          placeholder: i18n.t('请选择'),
          showClearButton: true,
          dataSource: deliveryTypeOptions
        }
      },
      {
        label: i18n.t('创建时间'),
        prop: 'createTime',
        component: 'mt-date-range-picker',
        props: {
          placeholder: i18n.t('请选择')
        }
      }
    ]
  }
  if (type === 'vmi') {
    searchFormItems = [
      {
        label: i18n.t('SRM入库单号'),
        prop: 'stockInCode',
        component: 'mt-input',
        props: {
          placeholder: i18n.t('请输入'),
          showClearButton: true
        }
      },
      {
        label: i18n.t('工厂编码'),
        prop: 'siteCode',
        component: 'mt-input',
        props: {
          placeholder: i18n.t('请输入'),
          showClearButton: true
        }
      },
      {
        label: i18n.t('VMI仓编码'),
        prop: 'warehouseCode',
        component: 'mt-input',
        props: {
          placeholder: i18n.t('请输入'),
          showClearButton: true
        }
      },
      {
        label: i18n.t('创建时间'),
        prop: 'createTime',
        component: 'mt-date-range-picker',
        props: {
          placeholder: i18n.t('请选择')
        }
      }
    ]
  }

  return searchFormItems
}
