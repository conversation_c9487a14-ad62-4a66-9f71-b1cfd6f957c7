import {
  toolbar,
  forecastTypeOptions,
  statusOptions,
  onWayStatusOptions,
  syncStatusOptions,
  intoStatusOptions,
  deliveryMethodOptions
} from './index'
import VxeRemoteSearch from '@/components/VxeRemoteSearch/index.vue'
import { RegExpMap } from '@/utils/constant'
import { isValidPhone, getIdCardValidationError, getCarNoValidationError } from '@/utils/validator'

export default {
  components: {
    VxeRemoteSearch
  },
  data() {
    return {
      toolbar,
      forecastTypeOptions,
      statusOptions,
      onWayStatusOptions,
      syncStatusOptions,
      intoStatusOptions,
      deliveryMethodOptions,
      // 通过预约升级按钮进入编辑状态的行ID集合
      upgradeEditingRows: new Set(),
      editRules: {
        forecastType: [{ required: true, message: '请选择', trigger: 'blur' }],
        deliveryCodes: [{ required: true, message: '请选择', trigger: 'blur' }],
        driverName: [{ required: true, message: '请输入', trigger: 'blur' }],
        driverPhone: [
          { required: true, message: '请输入', trigger: 'blur' },
          {
            pattern: RegExpMap.phoneReg,
            message: '请输入正确的手机号码格式（11位数字，以1开头）',
            trigger: 'blur'
          }
        ],
        driverIdNo: [
          { required: true, message: '请输入', trigger: 'blur' },
          {
            pattern: RegExpMap.idCard18StrictReg,
            message: '请输入正确的身份证号码格式（18位）',
            trigger: 'blur'
          }
        ],
        carNo: [
          { required: true, message: '请输入', trigger: 'blur' },
          {
            pattern: RegExpMap.carNoReg,
            message: '请输入正确的车牌号码格式（如：京A12345）',
            trigger: 'blur'
          }
        ],
        quantity: [{ required: true, message: '请输入', trigger: 'blur' }],
        forecastTime: [{ required: true, message: '请选择', trigger: 'blur' }]
      },
      // 保存表格滚动位置
      savedScrollPosition: {
        scrollLeft: 0,
        scrollTop: 0
      }
    }
  },
  computed: {
    columns() {
      return [
        {
          width: 50,
          type: 'checkbox',
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'forecastCode',
          title: this.$t('预约号'),
          minWidth: 160
        },
        {
          field: 'forecastType',
          title: this.$t('预约类型'),
          minWidth: 140,
          editRender: {},
          slots: {
            default: ({ row }) => {
              let selectedItem = this.forecastTypeOptions.find((v) => v.value === row.forecastType)
              return [selectedItem ? <div>{selectedItem.label}</div> : null]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.forecastType}
                  options={this.forecastTypeOptions}
                  placeholder={this.$t('请选择')}
                  clearable
                  transfer
                  onChange={() => {
                    // 清空关联的ASN/VMI入库单号及带出的数据
                    row.deliveryCodes = []
                    row.deliveryCodeOptions = []
                    row.companyCode = null
                    row.companyName = null
                    row.supplierCode = null
                    row.supplierName = null
                    row.deliveryStatus = null
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'deliveryCodes',
          title: this.$t('关联ASN/VMI入库单号'),
          minWidth: 220,
          editRender: {},
          slots: {
            default: ({ row }) => {
              // 显示关联ASN/VMI入库单号，支持多个单号的显示
              const codes = row.deliveryCodes || []
              return [<div>{codes.join(', ')}</div>]
            },
            edit: ({ row }) => {
              return [
                <div style={{ display: 'flex', 'align-items': 'center' }}>
                  <vxe-select
                    v-model={row.deliveryCodes}
                    options={row.deliveryCodeOptions}
                    placeholder={
                      ![0, 1].includes(row?.forecastType)
                        ? this.$t('请先选择预约类型')
                        : (row.deliveryCodes && row.deliveryCodes.length)
                        ? this.$t('请选择')
                        : this.$t('请点击放大镜进行选择')
                    }
                    multiple
                    clearable
                    transfer
                  />
                  <vxe-icon
                    name='search'
                    style={{ cursor: 'pointer', padding: '0 5px' }}
                    onClick={() => this.deliveryCodeSelect(row)}
                  />
                </div>
              ]
            }
          }
        },
        {
          field: 'companyCode',
          title: this.$t('公司'),
          minWidth: 160,
          formatter: ({ cellValue, row }) => {
            return cellValue ? `${cellValue}-${row.companyName || ''}` : ''
          }
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商编码'),
          minWidth: 120
        },
        {
          field: 'supplierName',
          title: this.$t('供应商名称'),
          minWidth: 120
        },
        {
          field: 'status',
          title: this.$t('预约状态'),
          formatter: ({ cellValue }) => {
            const selectItem = statusOptions.find((item) => item.value === cellValue)
            return selectItem?.text
          }
        },
        {
          field: 'onWayStatus',
          title: this.$t('在途状态'),
          formatter: ({ cellValue }) => {
            const selectItem = onWayStatusOptions.find((item) => item.value === cellValue)
            return selectItem?.text
          }
        },
        {
          field: 'syncStatus',
          title: this.$t('同步状态'),
          formatter: ({ cellValue }) => {
            const selectItem = syncStatusOptions.find((item) => item.value === cellValue)
            return selectItem?.text
          }
        },
        {
          field: 'intoStatus',
          title: this.$t('入园状态'),
          formatter: ({ cellValue }) => {
            const selectItem = intoStatusOptions.find((item) => item.value === cellValue)
            return selectItem?.text
          }
        },
        {
          field: 'deliveryStatus',
          title: this.$t('交货方式'),
          formatter: ({ cellValue }) => {
            if (!cellValue) return ''
            const deliveryStatusList = cellValue.split(',')
            const deliveryStatusTextList = deliveryStatusList.map((value) => {
              const selectItem = deliveryMethodOptions.find((item) => item.value === Number(value))
              return selectItem?.text
            })
            return deliveryStatusTextList.join(',')
          }
        },
        {
          field: 'driverName',
          title: this.$t('司机姓名'),
          minWidth: 140,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <div style={{ display: 'flex', 'align-items': 'center' }}>
                  <vxe-input
                    v-model={row.driverName}
                    placeholder={this.$t('请输入')}
                    clearable
                    transfer
                  />
                  <vxe-icon
                    name='search'
                    style={{ cursor: 'pointer', padding: '0 5px' }}
                    onClick={() => this.selectDriver(row)}
                  />
                </div>
              ]
            }
          }
        },
        {
          field: 'driverPhone',
          title: this.$t('司机手机号'),
          minWidth: 140,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.driverPhone}
                  placeholder={this.$t('请输入11位手机号')}
                  clearable
                  transfer
                  maxlength={11}
                  onBlur={(e) => this.validateDriverPhone(e.value, row)}
                />
              ]
            }
          }
        },
        {
          field: 'driverIdNo',
          title: this.$t('司机身份证号'),
          minWidth: 200,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.driverIdNo}
                  placeholder={this.$t('请输入18位身份证号')}
                  clearable
                  transfer
                  maxlength={18}
                  onBlur={(e) => this.validateDriverIdNo(e.value, row)}
                />
              ]
            }
          }
        },
        {
          field: 'carNo',
          title: this.$t('车牌号'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.carNo}
                  placeholder={this.$t('请输入车牌号，如：京A12345')}
                  clearable
                  transfer
                  maxlength={8}
                  onBlur={(e) => this.validateCarNo(e.value, row)}
                />
              ]
            }
          }
        },
        {
          field: 'quantity',
          title: this.$t('件数'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.quantity}
                  placeholder={this.$t('请输入')}
                  min='1'
                  clearable
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'vehicleLogistics',
          title: this.$t('车辆物流'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              if (row?.forecastCode && row?.deliveryCode && row?.carNo) {
                return [
                  <span
                    style={{ color: '#6386c1', cursor: 'pointer' }}
                    onClick={() => this.handleViewLogistics(row)}>
                    {this.$t('查看物流')}
                  </span>
                ]
              }
              return [<span>-</span>]
            }
          }
        },
        // {
        //   field: 'subSiteCode',
        //   title: this.$t('分厂'),
        //   minWidth: 120
        // },
        // {
        //   field: 'gateNumber',
        //   title: this.$t('门岗号'),
        //   minWidth: 100
        // },
        {
          field: 'forecastTime',
          title: this.$t('预约时间'),
          minWidth: 160,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <div style={{ display: 'flex', 'align-items': 'center' }}>
                  <vxe-input
                    v-model={row.forecastTime}
                    placeholder={
                      ![0, 1].includes(row?.forecastType)
                        ? this.$t('请先选择预约类型')
                        : this.$t('请点击放大镜进行选择')
                    }
                    readonly
                    transfer
                  />
                  <vxe-icon
                    name='search'
                    style={{ cursor: 'pointer', padding: '0 5px' }}
                    onClick={() => this.selectForecastTime(row)}
                  />
                </div>
              ]
            }
          }
        },
        {
          field: 'dataSource',
          title: this.$t('预约来源'),
          minWidth: 120,
          formatter: ({ cellValue }) => {
            const sourceMap = {
              0: this.$t('SRM'),
              1: this.$t('TMS'),
              2: this.$t('其他')
            }
            return sourceMap[cellValue] || ''
          }
        },
        {
          field: 'outerApptNo',
          title: this.$t('TMS预约单号'),
          minWidth: 160
        },
        {
          field: 'warehouseCode',
          title: this.$t('库存地点'),
          minWidth: 160,
          slots: {
            default: ({ row }) => {
              return [
                <div>{row.warehouseCode ? `${row.warehouseCode}-${row.warehouse || ''}` : ''}</div>
              ]
            }
          }
        },
        {
          field: 'sendAddressCode',
          title: this.$t('送货地址'),
          minWidth: 160,
          slots: {
            default: ({ row }) => {
              return [
                <div>
                  {row.sendAddressCode ? `${row.sendAddressCode}-${row.sendAddress || ''}` : ''}
                </div>
              ]
            }
          }
        },
        {
          field: 'remark',
          title: this.$t('备注'),
          minWidth: 180,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.remark}
                  placeholder={this.$t('请输入')}
                  clearable
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'accompanyInfo',
          title: this.$t('随车信息'),
          minWidth: 180,
          editRender: {},
          slots: {
            default: ({ row }) => {
              const accompanyList = row?.accompanyList || []
              if (accompanyList.length === 0) {
                return [
                  <span
                    style={{ color: '#999', cursor: 'pointer' }}
                    onClick={() => this.editAccompanyInfo(row)}>
                    {this.$t('点击添加')}
                  </span>
                ]
              }

              // 显示人员数量和查看详情
              return [
                <div
                  style={{
                    display: 'flex',
                    'flex-direction': 'column',
                    'align-items': 'flex-start'
                  }}>
                  <span style={{ color: '#333', 'font-weight': 'bold', 'margin-bottom': '2px' }}>
                    {accompanyList.length}人
                  </span>
                  <span
                    style={{ color: '#6386c1', cursor: 'pointer', 'font-size': '12px' }}
                    onClick={() => this.editAccompanyInfo(row)}>
                    {this.$t('查看详情')}
                  </span>
                </div>
              ]
            },
            edit: ({ row }) => {
              const accompanyList = row?.accompanyList || []
              const displayText =
                accompanyList.length > 0 ? `${accompanyList.map((p) => p.name).join('、')}` : ''

              return [
                <div style={{ display: 'flex', 'align-items': 'center', width: '100%' }}>
                  <vxe-input
                    value={displayText}
                    placeholder={this.$t('请点击编辑进行添加')}
                    readonly
                    transfer
                    style={{ flex: 1 }}
                  />
                  <vxe-icon
                    name='edit'
                    style={{ cursor: 'pointer', padding: '0 5px' }}
                    onClick={() => this.editAccompanyInfo(row)}
                  />
                </div>
              ]
            }
          }
        },
        {
          field: 'accompanyNum',
          title: this.$t('随车人数'),
          minWidth: 150,
          slots: {
            default: ({ row }) => {
              const accompanyList = row?.accompanyList || []
              if (accompanyList.length === 0) {
                return [<div style={{ color: '#999' }}>0人</div>]
              }

              // 显示人数
              return [
                <div style={{ display: 'flex', 'flex-direction': 'column' }}>
                  <span style={{ 'font-weight': 'bold', color: '#333' }}>
                    {accompanyList.length}人
                  </span>
                </div>
              ]
            }
          }
        },
        {
          field: 'cancelPersonCode',
          title: this.$t('取消人'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              return [
                <div>
                  {row.cancelPersonCode
                    ? `${row.cancelPersonCode}-${row.cancelPersonName || ''}`
                    : ''}
                </div>
              ]
            }
          }
        },
        {
          field: 'cancelTime',
          title: this.$t('取消时间'),
          minWidth: 160
        },
        {
          field: 'checkInTimeStr',
          title: this.$t('司机签到时间'),
          minWidth: 160
        },
        {
          field: 'departTimeStr',
          title: this.$t('出车时间'),
          minWidth: 160
        },
        {
          field: 'enterParkTimeStr',
          title: this.$t('入园时间'),
          minWidth: 160
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          minWidth: 160
        },
        {
          field: 'updateTime',
          title: this.$t('更新时间'),
          minWidth: 160
        }
      ]
    },
    editConfig() {
      return {
        enabled: true,
        trigger: 'dblclick',
        mode: 'row',
        showStatus: true,
        beforeEditMethod: this.beforeEditMethod
      }
    }
  },
  methods: {
    // 编辑前检查方法 - 控制编辑权限
    beforeEditMethod({ row }) {
      const rowId = row.id
      const status = row.status

      // 审批中(2)、已取消(5)状态完全不可编辑
      if (status === 2 || status === 5) {
        const statusText = this.statusOptions.find(item => item.value === status)?.text || '未知状态'
        this.$toast({
          content: this.$t(`${statusText}状态的数据不可编辑`),
          type: 'warning'
        })
        return false
      }

      // 预约成功(3)状态只有通过升级按钮才可编辑
      if (status === 3 && !this.upgradeEditingRows.has(rowId)) {
        this.$toast({
          content: this.$t('预约成功状态的数据需要通过"预约升级"按钮进入编辑状态'),
          type: 'warning'
        })
        return false
      }

      return true
    },

    // 设置升级编辑状态
    setUpgradeEditingRows(rowIds) {
      rowIds.forEach(id => {
        this.upgradeEditingRows.add(id)
      })
    },

    // 清除升级编辑状态
    clearUpgradeEditingRows(rowIds = null) {
      if (rowIds) {
        rowIds.forEach(id => {
          this.upgradeEditingRows.delete(id)
        })
      } else {
        this.upgradeEditingRows.clear()
      }
    },

    // 校验司机手机号
    validateDriverPhone(value) {
      if (!value) return true // 空值由required规则处理

      if (!isValidPhone(value)) {
        this.$toast({
          content: this.$t('请输入正确的手机号码格式（11位数字，以1开头）'),
          type: 'warning'
        })
        return false
      }
      return true
    },

    // 校验司机身份证号
    validateDriverIdNo(value) {
      if (!value) return true // 空值由required规则处理

      const errorMessage = getIdCardValidationError(value)
      if (errorMessage) {
        this.$toast({
          content: this.$t(errorMessage),
          type: 'warning'
        })
        return false
      }

      return true
    },

    // 校验车牌号
    validateCarNo(value) {
      if (!value) return true // 空值由required规则处理

      const errorMessage = getCarNoValidationError(value, true)
      if (errorMessage) {
        this.$toast({
          content: this.$t(errorMessage),
          type: 'warning'
        })
        return false
      }

      return true
    },

    // 保存表格滚动位置
    saveTableScrollPosition() {
      if (this.tableRef) {
        try {
          const scrollContainer = this.tableRef.$el.querySelector('.vxe-table--body-wrapper')
          if (scrollContainer) {
            this.savedScrollPosition = {
              scrollLeft: scrollContainer.scrollLeft,
              scrollTop: scrollContainer.scrollTop
            }
            console.log('保存表格滚动位置:', this.savedScrollPosition)
          }
        } catch (e) {
          console.log('保存滚动位置失败:', e.message)
        }
      }
    },

    // 恢复表格滚动位置
    restoreTableScrollPosition() {
      if (this.tableRef && this.savedScrollPosition) {
        try {
          this.$nextTick(() => {
            const scrollContainer = this.tableRef.$el.querySelector('.vxe-table--body-wrapper')
            if (scrollContainer) {
              scrollContainer.scrollLeft = this.savedScrollPosition.scrollLeft
              scrollContainer.scrollTop = this.savedScrollPosition.scrollTop
              console.log('恢复表格滚动位置:', this.savedScrollPosition)
            }
          })
        } catch (e) {
          console.log('恢复滚动位置失败:', e.message)
        }
      }
    },

    // 重新激活编辑状态并保持滚动位置
    reactivateEditWithScrollPosition(row) {
      this.$nextTick(() => {
        if (this.tableRef) {
          // 先恢复滚动位置
          this.restoreTableScrollPosition()

          // 延迟一点再设置编辑状态，避免滚动位置被重置
          setTimeout(() => {
            this.tableRef.setEditRow(row)

            // 再次确保滚动位置正确
            setTimeout(() => {
              this.restoreTableScrollPosition()
            }, 50)
          }, 100)
        }
      })
    },
    deliveryCodeSelect(row) {
      if (![0, 1].includes(row?.forecastType)) {
        this.$toast({ content: this.$t('请先选择预约类型'), type: 'warning' })
        return
      }

      // 初始化数组（如果不存在）
      if (!row.deliveryCodes) {
        row.deliveryCodes = []
      }
      if (!row.deliveryCodeOptions) {
        row.deliveryCodeOptions = []
      }

      // 打开弹框前保存滚动位置
      this.saveTableScrollPosition()

      this.$dialog({
        modal: () => import('../components/SelectCodeDialog.vue'),
        data: {
          title: this.$t('选择关联ASN/VMI入库单'),
          type: row.forecastType === 0 ? 'delivery' : 'vmi',
          selectedCodes: row.deliveryCodes
        },
        success: (selectedRecords) => {
          console.log('selectedRecords:', selectedRecords)

          // 重置数组，以弹框最新确认的数据为准
          row.deliveryCodes = []
          row.deliveryCodeOptions = []

          // 用于收集其他字段的数据
          const deliveryIdSet = new Set()
          const deliveryTypeSet = new Set()

          if (selectedRecords && selectedRecords.length > 0) {
            selectedRecords.forEach((item) => {
              // 根据类型确定字段名
              let codeValue = null

              if (row.forecastType === 1) {
                // vmi类型
                codeValue = item.stockInCode

                deliveryTypeSet.add(5)
              } else {
                // delivery类型
                codeValue = item.deliveryCode

                if (item.deliveryType) {
                  if (item.deliveryType === 9) {
                    deliveryTypeSet.add(2)
                  } else {
                    deliveryTypeSet.add(item.deliveryType)
                  }
                }
              }

              console.log('Processing item:', item)
              console.log('Extracted codeValue:', codeValue)

              if (codeValue) {
                row.deliveryCodes.push(codeValue)
                row.deliveryCodeOptions.push({
                  value: codeValue,
                  label: codeValue
                })
              }

              deliveryIdSet.add(item.id)
            })

            if (deliveryIdSet.size > 0) {
              row.deliveryIds = Array.from(deliveryIdSet)
            }
            if (deliveryTypeSet.size > 0) {
              row.deliveryStatus = Array.from(deliveryTypeSet).join(',')
            }

            row.companyCode = selectedRecords[0].companyCode
            row.companyName = selectedRecords[0].companyName
            row.supplierCode = selectedRecords[0].supplierCode
            row.supplierName = selectedRecords[0].supplierName

            console.log('Updated other fields:', {
              deliveryIds: row.deliveryIds,
              companyCode: row.companyCode,
              companyName: row.companyName,
              supplierCode: row.supplierCode,
              supplierName: row.supplierName,
              deliveryStatus: row.deliveryStatus
            })
          }

          // 弹框关闭后重新激活行编辑状态并保持滚动位置
          this.reactivateEditWithScrollPosition(row)
        },
        close: () => {
          // 弹框取消后也要重新激活行编辑状态并保持滚动位置
          this.reactivateEditWithScrollPosition(row)
        }
      })
    },

    // 查看车辆物流
    async handleViewLogistics(row) {
      try {
        const params = {
          ztpno: row.deliveryCode?.toString(),
          busCode: row.forecastCode,
          busNum: row.carNo
        }

        const res = await this.$API.deliveryManagement.queryVehicleLogisticsApi(params)
        if (res.code === 200 && res.data?.mapURL) {
          window.open(res.data.mapURL)
        } else {
          this.$toast({ content: this.$t('暂无物流信息'), type: 'warning' })
        }
      } catch (error) {
        console.error('查看物流失败:', error)
        this.$toast({ content: error?.msg || this.$t('查看物流失败'), type: 'error' })
      }
    },

    // 选择司机
    selectDriver(row) {
      // 打开弹框前保存滚动位置
      this.saveTableScrollPosition()

      this.$dialog({
        modal: () => import('../components/DriverSelectDialog.vue'),
        data: {
          title: this.$t('选择司机')
        },
        success: (selectedDriver) => {
          if (selectedDriver) {
            row.driverName = selectedDriver.name
            row.driverPhone = selectedDriver.contact
            row.driverIdNo = selectedDriver.idCard
            row.carNo = selectedDriver.license
          }
          // 弹框关闭后重新激活行编辑状态并保持滚动位置
          this.reactivateEditWithScrollPosition(row)
        },
        close: () => {
          // 弹框取消后也要重新激活行编辑状态并保持滚动位置
          this.reactivateEditWithScrollPosition(row)
        }
      })
    },

    // 选择预约时间
    selectForecastTime(row) {
      console.log('row:', row)
      if (!row?.deliveryCodes || row.deliveryCodes?.length === 0) {
        this.$toast({ content: this.$t('请先选择关联ASN/VMI入库单'), type: 'warning' })
        return
      }

      // 打开弹框前保存滚动位置
      this.saveTableScrollPosition()

      this.$dialog({
        modal: () => import('../components/ForecastTimeDialog.vue'),
        data: {
          title: this.$t('选择预约时间'),
          rowData: row
        },
        success: (selectedTime) => {
          if (selectedTime) {
            // ForecastTimeDialog已经返回了完整的预约时间（日期 + 时间段）
            row.forecastTime = selectedTime.appointmentTime
            row.warehouseCode = selectedTime.warehouseCode
            row.warehouse = selectedTime.warehouse
            row.sendAddressCode = selectedTime.sendAddressCode
            row.sendAddress = selectedTime.sendAddress
          }
          // 弹框关闭后重新激活行编辑状态并保持滚动位置
          this.reactivateEditWithScrollPosition(row)
        },
        close: () => {
          // 弹框取消后也要重新激活行编辑状态并保持滚动位置
          this.reactivateEditWithScrollPosition(row)
        }
      })
    },

    // 编辑随车信息
    editAccompanyInfo(row) {
      // 打开弹框前保存滚动位置
      this.saveTableScrollPosition()

      this.$dialog({
        modal: () => import('../components/AccompanyDialog.vue'),
        data: {
          title: this.$t('随车人员管理'),
          accompanyList: row?.accompanyList || []
        },
        success: (accompanyList) => {
          console.log('随车信息保存成功:', { accompanyList, rowBefore: row.accompanyList })

          // 使用Vue.set确保响应式更新
          this.$set(row, 'accompanyList', accompanyList)

          console.log('随车信息更新后:', { rowAfter: row.accompanyList })

          // 显示成功提示
          this.$toast({
            content: this.$t(`随车人员信息已更新，共${accompanyList.length}人`),
            type: 'success'
          })
        }
      })
    }
  }
}
