import { i18n } from '@/main.js'

// 预约类型
export const forecastTypeOptions = [
  { value: 0, text: i18n.t('送货单预约'), label: i18n.t('送货单预约') },
  { value: 1, text: i18n.t('入库单预约'), label: i18n.t('入库单预约') }
]

// 预约状态
export const statusOptions = [
  { value: 1, text: i18n.t('未预约') },
  { value: 2, text: i18n.t('审批中') },
  { value: 3, text: i18n.t('预约成功') },
  { value: 4, text: i18n.t('预约拒绝') },
  { value: 5, text: i18n.t('已取消') }
]

// 在途状态
export const onWayStatusOptions = [
  { value: 0, text: i18n.t('未出发') },
  { value: 1, text: i18n.t('已入园') },
  { value: 2, text: i18n.t('已出发') },
  { value: 3, text: i18n.t('已报到') },
  { value: 4, text: i18n.t('已取消') },
  { value: 5, text: i18n.t('已关闭') },
  { value: 12, text: i18n.t('已离园') }
]

// 同步状态
export const syncStatusOptions = [
  { value: 0, text: i18n.t('未同步') },
  { value: 1, text: i18n.t('同步失败') },
  { value: 2, text: i18n.t('同步成功') }
]

// 入园状态
export const intoStatusOptions = [
  { value: 1, text: i18n.t('已入园') },
  { value: 2, text: i18n.t('未入园') }
]

// 交货方式
export const deliveryMethodOptions = [
  { value: 1, text: i18n.t('采购订单') },
  { value: 2, text: i18n.t('交货计划') },
  { value: 5, text: i18n.t('VMI') }
]

// 送货单类型
export const deliveryTypeOptions = [
  { value: 1, text: i18n.t('采购订单') },
  { value: 9, text: i18n.t('交货计划') }
]

// 送货单状态
export const deliveryStatusOptions = [
  { value: 2, text: i18n.t('发货中') },
  { value: 3, text: i18n.t('已完成') },
  { value: 4, text: i18n.t('已取消') },
  { value: 5, text: i18n.t('已关闭') }
]

// 入库单状态
export const stockInStatusOptions = [
  { value: 1, text: i18n.t('新建') },
  { value: 2, text: i18n.t('入库中') },
  { value: 3, text: i18n.t('已接收') },
  { value: 4, text: i18n.t('已取消') }
]

// 退货状态
export const returnStatusOptions = [
  { value: 1, text: i18n.t('未退货') },
  { value: 2, text: i18n.t('待供方确认') },
  { value: 3, text: i18n.t('待出库退货') },
  { value: 4, text: i18n.t('已退货') },
  { value: 5, text: i18n.t('供方已拒绝') },
  { value: 6, text: i18n.t('已取消') }
]

// 预约来源
export const dataSourceOptions = [
  { value: 0, text: i18n.t('SRM') },
  { value: 1, text: i18n.t('TMS') },
  { value: 2, text: i18n.t('其他') }
]

// 按钮
export const toolbar = [
  {
    code: 'add',
    name: i18n.t('新增'),
    status: 'info',
    loading: false
  },
  {
    code: 'cancelEdit',
    name: i18n.t('取消编辑'),
    status: 'info',
    loading: false
  },
  {
    code: 'submit',
    name: i18n.t('提交'),
    status: 'info',
    loading: false
  },
  {
    code: 'upgrade',
    name: i18n.t('预约升级'),
    status: 'info',
    loading: false
  },
  {
    code: 'cancel',
    name: i18n.t('取消'),
    status: 'info',
    loading: false
  },
  {
    code: 'import',
    name: i18n.t('导入'),
    status: 'info',
    loading: false
  },
  {
    code: 'export',
    name: i18n.t('导出'),
    status: 'info',
    loading: false
  },
  {
    code: 'print',
    name: i18n.t('打印送货单'),
    status: 'info',
    loading: false
  }
]

export const getDialogTableColumns = (type) => {
  let columns = []
  if (type === 'delivery') {
    columns = [
      {
        width: 50,
        type: 'checkbox',
        fixed: 'left',
        align: 'center'
      },
      {
        title: i18n.t('序号'),
        type: 'seq',
        width: 50,
        fixed: 'left',
        align: 'center'
      },
      {
        title: i18n.t('送货单号'),
        field: 'deliveryCode',
        minWidth: 160
      },
      {
        title: i18n.t('状态'),
        field: 'status',
        formatter: ({ row }) => {
          return deliveryStatusOptions.find((item) => item.value === row.status)?.text || ''
        }
      },
      {
        title: i18n.t('送货单类型'),
        field: 'deliveryType',
        minWidth: 120,
        formatter: ({ row }) => {
          return deliveryTypeOptions.find((item) => item.value === row.deliveryType)?.text || ''
        }
      },
      {
        title: i18n.t('公司编码'),
        field: 'companyCode'
      },
      {
        title: i18n.t('工厂编码'),
        field: 'siteCode'
      },
      {
        title: i18n.t('入库仓库'),
        field: 'warehouseCode'
      },
      {
        title: i18n.t('收货人'),
        field: 'receiver'
      },
      {
        title: i18n.t('收货联系方式'),
        field: 'receiverContact',
        minWidth: 120
      },
      {
        title: i18n.t('配送地址'),
        field: 'deliveryAddress',
        minWidth: 120
      }
    ]
  } else if (type === 'vmi') {
    columns = [
      {
        width: 50,
        type: 'checkbox',
        fixed: 'left',
        align: 'center'
      },
      {
        title: i18n.t('序号'),
        type: 'seq',
        width: 50,
        fixed: 'left',
        align: 'center'
      },
      {
        title: i18n.t('SRM入库单号'),
        field: 'stockInCode',
        minWidth: 160
      },
      {
        title: i18n.t('入库状态'),
        field: 'status',
        formatter: ({ row }) => {
          return stockInStatusOptions.find((item) => item.value === row.status)?.text || ''
        }
      },
      {
        title: i18n.t('退货状态'),
        field: 'returnStatus',
        formatter: ({ row }) => {
          return returnStatusOptions.find((item) => item.value === row.returnStatus)?.text || ''
        }
      },
      {
        title: i18n.t('工厂编码'),
        field: 'siteCode'
      },
      {
        title: i18n.t('工厂名称'),
        field: 'siteName'
      },
      {
        title: i18n.t('VMI仓编码'),
        field: 'warehouseCode',
        minWidth: 120
      },
      {
        title: i18n.t('VMI仓名称'),
        field: 'warehouseName',
        minWidth: 120
      },
      {
        title: i18n.t('供应商编码'),
        field: 'supplierCode',
        minWidth: 120
      },
      {
        title: i18n.t('供应商名称'),
        field: 'supplierName',
        minWidth: 120
      }
    ]
  }
  return columns
}
