import { i18n } from '@/main.js'

// 状态
export const statusList = [
  { value: 0, text: i18n.t('新建') },
  { value: 1, text: i18n.t('已修改') },
  { value: 2, text: i18n.t('已发布') },
  { value: 3, text: i18n.t('反馈满足') },
  { value: 4, text: i18n.t('反馈不满足') },
  { value: 5, text: i18n.t('已关闭') },
  { value: 6, text: i18n.t('已完成') }
]

// 业务类型
export const businessTypeList = [
  { value: 1, text: i18n.t('户用业务') },
  { value: 2, text: i18n.t('商用业务') },
  { value: 3, text: i18n.t('海外业务') }
]

// 数据来源
export const dataSourceList = [
  { value: 1, text: i18n.t('手工新增') },
  { value: 0, text: i18n.t('仓库需求') }
]

// 是否
export const yesOrNoList = [
  { value: 0, text: i18n.t('否') },
  { value: 1, text: i18n.t('是') }
]

// 按钮
export const toolbar = [
  {
    code: 'cancelEdit',
    name: i18n.t('取消编辑'),
    status: 'info',
    loading: false
  },
  {
    code: 'feedback',
    name: i18n.t('反馈'),
    status: 'info',
    loading: false
  },
  {
    code: 'import',
    name: i18n.t('导入'),
    status: 'info',
    loading: false
  },
  {
    code: 'export',
    name: i18n.t('导出'),
    status: 'info',
    loading: false
  }
]
