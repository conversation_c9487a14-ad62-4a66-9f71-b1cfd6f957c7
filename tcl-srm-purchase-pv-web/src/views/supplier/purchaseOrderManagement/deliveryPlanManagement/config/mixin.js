import { toolbar, statusList, businessTypeList, dataSourceList, yesOrNoList } from './index'
import dayjs from 'dayjs'

export default {
  data() {
    return {
      toolbar,
      statusList,
      businessTypeList,
      dataSourceList,
      yesOrNoList,
      editRules: {
        promisedQuantity: [{ required: true, message: this.$t('必填') }]
      }
    }
  },
  computed: {
    columns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'serialNumber',
          title: this.$t('序列号'),
          minWidth: 180
        },
        {
          field: 'status',
          title: this.$t('状态'),
          minWidth: 80,
          slots: {
            default: ({ row }) => {
              const selectItem = statusList.find((item) => item.value === row.status)
              return [<div>{selectItem?.text}</div>]
            }
          }
        },
        {
          field: 'businessType',
          title: this.$t('业务类型'),
          minWidth: 130,
          slots: {
            default: ({ row }) => {
              const selectItem = businessTypeList.find((item) => item.value === row.businessType)
              return [<div>{selectItem?.text}</div>]
            }
          }
        },
        {
          field: 'siteCode',
          title: this.$t('工厂'),
          minWidth: 220,
          slots: {
            default: ({ row }) => {
              return [row.siteCode ? <div>{row.siteCode + '-' + row.siteName}</div> : null]
            }
          }
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商'),
          minWidth: 220,
          slots: {
            default: ({ row }) => {
              return [
                row.supplierCode ? <span>{row.supplierCode + '-' + row.supplierName}</span> : null
              ]
            }
          }
        },
        {
          field: 'itemCode',
          title: this.$t('物料'),
          minWidth: 220,
          slots: {
            default: ({ row }) => {
              return [row.itemCode ? <span>{row.itemCode + '-' + row.itemName}</span> : null]
            }
          }
        },
        {
          field: 'categoryCode',
          title: this.$t('品类'),
          minWidth: 220,
          slots: {
            default: ({ row }) => {
              return [
                row.categoryCode ? <span>{row.categoryCode + '-' + row.categoryName}</span> : null
              ]
            }
          }
        },
        {
          field: 'powerKw',
          title: this.$t('功率(KW)'),
          minWidth: 120
        },
        {
          field: 'unitCode',
          title: this.$t('基本单位'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              return [row.unitCode ? <span>{row.unitCode + '-' + row.unitName}</span> : null]
            }
          }
        },
        {
          field: 'totalWarehouseDemand',
          title: this.$t('仓库总需求'),
          minWidth: 120
        },
        {
          field: 'demandDate',
          title: this.$t('需求日期'),
          minWidth: 130,
          slots: {
            default: ({ row }) => {
              return [
                row.demandDate ? <div>{dayjs(row.demandDate).format('YYYY-MM-DD')}</div> : null
              ]
            }
          }
        },
        {
          field: 'srmSignFlag',
          title: this.$t('是否SRM签收'),
          minWidth: 130,
          slots: {
            default: ({ row }) => {
              const selectItem = yesOrNoList.find((item) => item.value === row.srmSignFlag)
              return [<div>{selectItem?.text}</div>]
            }
          }
        },
        {
          field: 'warehouseCode',
          title: this.$t('入库仓库'),
          minWidth: 160,
          slots: {
            default: ({ row }) => {
              return [
                row.warehouseCode ? <div> {row.warehouseCode + '-' + row.warehouseName}</div> : null
              ]
            }
          }
        },
        {
          field: 'demandQuantity',
          title: this.$t('需求数量'),
          minWidth: 120
        },
        {
          field: 'promisedQuantity',
          title: this.$t('承诺数量'),
          minWidth: 130,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.promisedQuantity}
                  placeholder={this.$t('请输入')}
                  min={0}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'pendingShipmentQuantity',
          title: this.$t('待发货数量'),
          minWidth: 120
        },
        {
          field: 'inTransitQuantity',
          title: this.$t('在途数量'),
          minWidth: 100
        },
        {
          field: 'shippedQuantity',
          title: this.$t('已发货数量'),
          minWidth: 120
        },
        {
          field: 'inboundQuantity',
          title: this.$t('已入库数量'),
          minWidth: 120
        },
        {
          field: 'majorCategory',
          title: this.$t('大类'),
          minWidth: 140
        },
        {
          field: 'mediumCategory',
          title: this.$t('中类'),
          minWidth: 140
        },
        {
          field: 'minorCategory',
          title: this.$t('小类'),
          minWidth: 140
        },
        {
          field: 'dataSource',
          title: this.$t('数据来源'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = dataSourceList.find((item) => item.value === row.dataSource)
              return [<div>{selectItem?.text}</div>]
            }
          }
        },
        {
          field: 'warehouseDemandCode',
          title: this.$t('仓库需求编码'),
          minWidth: 120
        },
        {
          field: 'receiver',
          title: this.$t('收货人'),
          minWidth: 120
        },
        {
          field: 'contactInfo',
          title: this.$t('收货联系方式'),
          minWidth: 120
        },
        {
          field: 'deliveryAddress',
          title: this.$t('配送地址'),
          minWidth: 120
        },
        {
          field: 'buyerRemarks',
          title: this.$t('采方备注'),
          minWidth: 120
        },
        {
          field: 'supplierRemarks',
          title: this.$t('供方备注'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.supplierRemarks}
                  transfer
                  clearable
                  placeholder={this.$t('请输入')}
                />
              ]
            }
          }
        },
        {
          field: 'publishTime',
          title: this.$t('发布时间'),
          minWidth: 160
        },
        {
          field: 'feedbackTime',
          title: this.$t('反馈时间'),
          minWidth: 160
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          minWidth: 160
        },
        {
          field: 'updateUserName',
          title: this.$t('最后更新人'),
          minWidth: 120
        },
        {
          field: 'updateTime',
          title: this.$t('最后更新时间'),
          minWidth: 160
        }
      ]
    },
    editConfig() {
      return {
        enabled: true,
        trigger: 'dblclick',
        mode: 'row',
        showStatus: true,
        beforeEditMethod: this.beforeEditMethod
      }
    }
  },
  methods: {
    beforeEditMethod({ row }) {
      if (![2].includes(row.status)) {
        return false
      }
      return true
    },
    async editComplete(args) {
      const { row } = args
      if (!args.$event) return

      if (args.$event.srcElement.innerText === this.$t('取消编辑')) {
        await this.handleSearch()
        return
      }

      try {
        const valid = await this.tableRef.validate([row])
        if (valid) {
          this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
          return
        }

        const params = {
          ...row,
          id: row.id.includes('row_') ? null : row.id,
          demandDate: row.demandDate ? dayjs(row.demandDate).valueOf() : null
        }

        const res = await this.$API.supplierPurchaseOrderManagement.saveDeliveryPlanApi([params])
        if (res.code === 200) {
          this.$toast({ content: this.$t('保存成功！'), type: 'success' })
          await this.handleSearch()
        } else {
          throw new Error(res.message || this.$t('保存失败'))
        }
      } catch (error) {
        console.error('保存失败:', error)
        this.tableRef.setEditRow(row)
      }
    }
  }
}
