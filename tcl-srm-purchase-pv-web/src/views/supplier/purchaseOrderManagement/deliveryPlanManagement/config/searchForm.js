/**
 * 交货计划管理搜索表单配置
 */
import { i18n } from '@/main.js'
import { statusList, businessTypeList, yesOrNoList } from './index'

export const getSearchFormItems = () => [
  {
    label: i18n.t('序列号'),
    prop: 'serialNumber',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('状态'),
    prop: 'statusList',
    component: 'mt-select',
    props: {
      placeholder: i18n.t('请选择'),
      dataSource: statusList,
      showClearButton: true
    }
  },
  {
    label: i18n.t('业务类型'),
    prop: 'businessType',
    component: 'mt-select',
    props: {
      placeholder: i18n.t('请选择'),
      dataSource: businessTypeList,
      showClearButton: true
    }
  },
  {
    label: i18n.t('工厂编码'),
    prop: 'siteCode',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('物料编码'),
    prop: 'itemCode',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('物料名称'),
    prop: 'itemName',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('品类编码'),
    prop: 'categoryCode',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('品类名称'),
    prop: 'categoryName',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('功率'),
    prop: 'powerKw',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('需求日期'),
    prop: 'demandDate',
    component: 'mt-date-range-picker',
    props: {
      placeholder: i18n.t('请选择'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('是否SRM签收'),
    prop: 'srmSignFlag',
    component: 'mt-select',
    props: {
      placeholder: i18n.t('请选择'),
      dataSource: yesOrNoList,
      showClearButton: true
    }
  },
  {
    label: i18n.t('入库仓库'),
    prop: 'warehouseCodes',
    component: 'RemoteAutocomplete',
    props: {
      placeholder: i18n.t('请选择'),
      showClearButton: true,
      url: '/srm-purchase-pv/tenant/pv/warehouse/page/query',
      multiple: true,
      fields: { text: 'warehouseName', value: 'warehouseCode' },
      paramsKey: 'fuzzyParam'
    }
  },
  {
    label: i18n.t('仓库需求编码'),
    prop: 'warehouseDemandCode',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: i18n.t('创建人'),
    prop: 'createUserName',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: i18n.t('创建时间'),
    prop: 'createTime',
    component: 'mt-date-range-picker',
    props: { placeholder: '请选择' }
  },
  {
    label: i18n.t('更新人'),
    prop: 'updateUserName',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: i18n.t('更新时间'),
    prop: 'updateTime',
    component: 'mt-date-range-picker',
    props: { placeholder: '请选择' }
  }
]
