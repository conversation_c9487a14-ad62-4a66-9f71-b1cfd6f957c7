<!-- 供方-采购订单管理-交货计划管理 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item
          v-for="(item, index) in searchFormItems"
          :key="index"
          :label="$t(item.label)"
          :prop="item.prop"
        >
          <component
            :is="item.component"
            v-model="searchFormModel[item.prop]"
            v-bind="item.props"
            @change="item.onChange && item.onChange($event, item.prop)"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="scTableRef"
      row-id="id"
      grid-id="8d887c5b-7965-4535-ac96-5873cfd9238c"
      show-overflow
      keep-source
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
      @edit-closed="editComplete"
    >
      <template #custom-tools>
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          v-bind="item"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import { getSearchFormItems } from './config/searchForm'
import { getHeadersFileName, download } from '@/utils/utils'
import mixin from './config/mixin'
import dayjs from 'dayjs'

// 操作类型常量
const OPERATION_TYPES = {
  FEEDBACK: 'feedback',
  CANCEL_EDIT: 'cancelEdit',
  IMPORT: 'import',
  EXPORT: 'export'
}

// 操作配置
const OPERATION_CONFIG = {
  [OPERATION_TYPES.FEEDBACK]: {
    title: '反馈',
    message: '确认反馈选中的数据？',
    api: 'feedbackDeliveryPlanApi',
    successMessage: '反馈成功！',
    requireSelection: true
  }
}

export default {
  name: 'DeliveryPlanManagement',
  components: { CollapseSearch, ScTable, RemoteAutocomplete },
  mixins: [mixin],
  data() {
    const startDate = dayjs().subtract(1, 'month').startOf('month')
    return {
      loading: false,
      searchFormModel: {
        createTime: [new Date(startDate), new Date()],
        createTimeS: this.getUnix(dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00')),
        createTimeE: this.getUnix(dayjs(new Date()).format('YYYY-MM-DD 23:59:59'))
      },
      searchFormRules: {},
      tableData: [],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      }
    }
  },

  computed: {
    tableRef() {
      return this.$refs.scTableRef?.$refs.xGrid
    },
    searchFormItems() {
      const items = getSearchFormItems()
      // 为创建时间、更新时间、需求日期字段添加onChange事件处理
      const dateFields = ['createTime', 'updateTime', 'demandDate']
      dateFields.forEach((field) => {
        const item = items.find((item) => item.prop === field)
        if (item) {
          item.onChange = this.handleDateTimeChange
        }
      })
      return items
    }
  },

  created() {
    this.initData()
  },

  methods: {
    async initData() {
      await this.getTableData()
    },

    handleDateTimeChange(e, field) {
      if (!e?.startDate) {
        this.searchFormModel[`${field}S`] = null
        this.searchFormModel[`${field}E`] = null
        return
      }

      this.searchFormModel[`${field}S`] = this.getUnix(
        dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
      )
      this.searchFormModel[`${field}E`] = this.getUnix(
        dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
      )
    },

    getUnix(val) {
      return dayjs(val).valueOf()
    },

    handleReset() {
      this.searchFormModel = {}
      const startDate = dayjs().subtract(1, 'month').startOf('month')
      this.searchFormModel.createTime = [new Date(startDate), new Date()]
      this.searchFormModel.createTimeS = this.getUnix(
        dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00')
      )
      this.searchFormModel.createTimeE = this.getUnix(
        dayjs(new Date()).format('YYYY-MM-DD 23:59:59')
      )
      this.handleSearch()
    },

    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },

    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },

    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },

    async getTableData() {
      try {
        this.loading = true
        const params = {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        }

        const res = await this.$API.supplierPurchaseOrderManagement.pageDeliveryPlanApi(params)

        if (res.code === 200) {
          const { total = 0, records = [] } = res.data
          this.pageSettings.totalRecordsCount = Number(total)
          this.pageSettings.totalPages = Math.ceil(total / this.pageSettings.pageSize)
          this.tableData = records
        }
      } catch (error) {
        console.error('获取表格数据失败:', error)
        this.$toast({ content: error || this.$t('获取数据失败'), type: 'error' })
      } finally {
        this.loading = false
      }
    },

    async handleClickToolBar(item) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const operation = OPERATION_CONFIG[item.code]

      if (operation?.requireSelection && selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
        return
      }

      const actionMap = {
        [OPERATION_TYPES.CANCEL_EDIT]: () => this.handleSearch(),
        [OPERATION_TYPES.FEEDBACK]: () =>
          this.handleOperate(selectedRecords, OPERATION_TYPES.FEEDBACK),
        [OPERATION_TYPES.IMPORT]: () => this.handleImport(),
        [OPERATION_TYPES.EXPORT]: () => this.handleExport(item)
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },

    /**
     * 处理操作
     * @param {Array} selectedRecords - 选中的记录
     * @param {string} type - 操作类型
     * @param {Object} extraParams - 额外参数
     */
    handleOperate(selectedRecords, type, extraParams = {}) {
      const operation = OPERATION_CONFIG[type]
      if (!operation) {
        this.$toast({ content: this.$t('不支持的操作类型'), type: 'error' })
        return
      }

      this.$dialog({
        data: {
          title: this.$t(operation.title),
          message: this.$t(operation.message),
          type: 'warning'
        },
        success: async () => {
          try {
            const params = {
              ids: selectedRecords.map((item) => item.id),
              ...extraParams
            }

            const res = await this.$API.supplierPurchaseOrderManagement[operation.api](params)
            if (res.code === 200) {
              this.$toast({
                content: this.$t(operation.successMessage),
                type: 'success'
              })
              await this.handleSearch()
            } else {
              throw new Error(res.message || this.$t(`${operation.title}失败`))
            }
          } catch (error) {
            console.error(`${operation.title}失败:`, error)
            this.$toast({
              content: error?.message || this.$t(`${operation.title}失败`),
              type: 'error'
            })
          }
        }
      })
    },

    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.supplierPurchaseOrderManagement.importDeliveryPlanApi,
          downloadTemplateApi: this.$API.supplierPurchaseOrderManagement.getTemplateDeliveryPlanApi,
          paramsKey: 'excel',
          downloadTemplateParams: {
            page: {
              current: this.currentPage,
              size: this.pageSettings.pageSize
            },
            ...this.searchFormModel
          }
        },
        success: () => {
          this.handleSearch()
        }
      })
    },

    async handleExport(item) {
      try {
        item.loading = true
        const dynamicHeaderMap = {}
        const specialFields = ['status', 'businessType', 'srmSignFlag', 'dataSource']
        const codeNameMap = {
          siteCode: '工厂名称',
          supplierCode: '供应商名称',
          itemCode: '物料名称',
          categoryCode: '品类名称',
          unitCode: '单位名称',
          warehouseCode: '入库仓库名称'
        }

        this.columns.forEach(({ field, title }) => {
          if (!field) return

          if (specialFields.includes(field)) {
            dynamicHeaderMap[`${field}Name`] = title
          } else if (codeNameMap[field]) {
            dynamicHeaderMap[field] = title
            dynamicHeaderMap[`${field.replace('Code', '')}Name`] = this.$t(codeNameMap[field])
          } else {
            dynamicHeaderMap[field] = title
          }
        })

        const params = {
          dynamicHeaderMap,
          req: {
            page: {
              current: this.currentPage,
              size: this.pageSettings.pageSize
            },
            ...this.searchFormModel
          }
        }

        const res = await this.$API.supplierPurchaseOrderManagement.exportDeliveryPlanApi(params)
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      } catch (error) {
        console.error('导出失败:', error)
        this.$toast({ content: error?.message || this.$t('导出失败'), type: 'error' })
      } finally {
        item.loading = false
      }
    }
  }
}
</script>
