/**
 * VMI库存水位搜索表单配置
 */
import { i18n } from '@/main.js'

export const getSearchFormItems = () => [
  {
    label: '入库仓库',
    prop: 'warehouseCodes',
    component: 'RemoteAutocomplete',
    props: {
      placeholder: i18n.t('请选择'),
      showClearButton: true,
      url: '/srm-purchase-pv/tenant/pv/warehouse/page/query',
      multiple: true,
      fields: { text: 'warehouseName', value: 'warehouseCode' },
      paramsKey: 'fuzzyParam'
    }
  },
  {
    label: '品类名称',
    prop: 'catoryName',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: '物料编码',
    prop: 'productCode',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  }
]
