import { i18n } from '@/main.js'

export const toolbar = [
  {
    code: 'create',
    name: i18n.t('创建送货计划'),
    status: 'info',
    loading: false
  },
  {
    code: 'export',
    name: i18n.t('导出'),
    status: 'info',
    loading: false
  }
]

export const columns = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'province',
    title: i18n.t('省区'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.city : row.city
    }
  },
  {
    field: 'warehouseCode',
    title: i18n.t('仓库编码')
  },
  {
    field: 'warehouseName',
    title: i18n.t('仓库名称')
  },
  {
    field: 'organizationCode',
    title: i18n.t('工厂编码')
  },
  {
    field: 'organizationName',
    title: i18n.t('工厂名称')
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称')
  },
  {
    field: 'productCode',
    title: i18n.t('物料编码')
  },
  {
    field: 'productName',
    title: i18n.t('物料名称')
  },
  {
    field: 'catoryCode',
    title: i18n.t('品类'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.catoryName : row.catoryName
    }
  },
  {
    field: 'unit',
    title: i18n.t('单位')
  },
  {
    field: 'specificationModel',
    title: i18n.t('规格')
  },
  {
    field: 'planAvailableStock',
    title: i18n.t('VMI可用库存-计划')
  },
  {
    field: 'planOnwayStock',
    title: i18n.t('VMI在途库存-计划')
  },
  {
    field: 'availableStock',
    title: i18n.t('可用库存')
  },
  {
    field: 'allocationStock',
    title: i18n.t('占用库存')
  },
  // {
  //   field: 'onWayStock',
  //   title: i18n.t('在途库存')
  // },
  {
    field: 'buyerOnwayStock',
    title: i18n.t('采购在途库存')
  },
  {
    field: 'transferOnwayStock',
    title: i18n.t('调拨在途库存')
  },
  {
    field: 'purchaseUnshipmentNum',
    title: i18n.t('采购待发货数量')
  },
  {
    field: 'logisticsTime',
    title: i18n.t('物流时间')
  },
  {
    field: 'maxUnloadCap',
    title: i18n.t('仓库每日最大卸货能力')
  },
  {
    field: 'residualArea',
    title: i18n.t('剩余面积')
  },
  {
    field: 'onloadResidArea',
    title: i18n.t('剩余面积（含在途）')
  },
  {
    field: 'residAvailCap',
    title: i18n.t('剩余可用库容（MW）')
  },
  {
    field: 'onloadResidAvailCap',
    title: i18n.t('剩余可用库容（含在途-MW）')
  },
  {
    field: 'warehousePreAllocation',
    title: i18n.t('仓库预占')
  },
  {
    field: 'salePreAllocation',
    title: i18n.t('销售预占')
  }
]
