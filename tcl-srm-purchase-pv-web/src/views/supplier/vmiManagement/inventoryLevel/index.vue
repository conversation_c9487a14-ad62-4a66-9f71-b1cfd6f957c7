<!-- 供方-VMI管理-库存水位 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item
          v-for="(item, index) in searchFormItems"
          :key="index"
          :label="$t(item.label)"
          :prop="item.prop"
        >
          <component
            :is="item.component"
            v-model="searchFormModel[item.prop]"
            v-bind="item.props"
            @change="item.onChange && item.onChange($event, item.prop)"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="scTableRef"
      row-id="id"
      grid-id="427b681b-7a5b-c28a-35d9-b7ca7fd60e46"
      show-overflow
      keep-source
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template #custom-tools>
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          v-bind="item"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import { columns, toolbar } from './config/index'
import { getSearchFormItems } from './config/searchForm'
import { getHeadersFileName, download } from '@/utils/utils'

export default {
  name: 'SupplierInventoryLevel',
  components: { CollapseSearch, ScTable, RemoteAutocomplete },
  data() {
    return {
      loading: false,
      searchFormModel: {},
      searchFormRules: {},
      tableData: [],
      columns,
      toolbar,
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      }
    }
  },

  computed: {
    tableRef() {
      return this.$refs.scTableRef?.$refs.xGrid
    },
    searchFormItems() {
      const items = getSearchFormItems()
      return items
    }
  },

  created() {
    this.getTableData()
  },

  watch: {
    // 监听路由变化，处理提交成功后的数据刷新
    '$route.query.submitSuccess': {
      handler(newVal) {
        if (newVal === 'true') {
          // 延迟刷新，确保页面已完全加载
          this.$nextTick(() => {
            this.handleRefreshAfterSubmit()
          })
        }
      },
      immediate: true
    }
  },

  methods: {
    handleReset() {
      this.searchFormModel = {}
    },

    handleSearch() {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },

    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },

    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },

    async getTableData() {
      try {
        this.loading = true
        const params = {
          ...this.searchFormModel,
          pageNo: this.currentPage,
          pageSize: this.pageSettings.pageSize
        }

        const res = await this.$API.supplierVmiManagement.pageInventoryLevelApi(params)

        if ([200, '200'].includes(res.code)) {
          const { total = 0, data = [] } = res
          this.pageSettings.totalRecordsCount = Number(total)
          this.pageSettings.totalPages = Math.ceil(total / this.pageSettings.pageSize)
          this.tableData = data
        }
      } catch (error) {
        console.error('获取表格数据失败:', error)
        this.$toast({ content: error || this.$t('获取数据失败'), type: 'error' })
      } finally {
        this.loading = false
      }
    },

    async handleClickToolBar(item) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (selectedRecords.length === 0 && ['create'].includes(item.code)) {
        this.$toast({ content: this.$t('请选择要操作的行'), type: 'warning' })
        return
      }

      if (selectedRecords.length > 1 && ['create'].includes(item.code)) {
        this.$toast({ content: this.$t('只能选择一个'), type: 'warning' })
        return
      }

      const actionMap = {
        create: () => this.handleCreate(selectedRecords),
        export: () => this.handleExport(item)
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },

    handleCreate(selectedRecords) {
      const row = { ...selectedRecords[0], id: null }
      const query = {
        type: 'create',
        record: JSON.stringify(row),
        from: 'inventory-level', // 标识来源页面
        timeStamp: Date.now()
      }

      this.$router.push({
        path: '/purchase-pv/supplier-vmi-management/delivery-plan-create',
        query
      })
    },

    async handleExport(item) {
      try {
        item.loading = true
        const params = {
          ...this.searchFormModel,
          pageNo: this.currentPage,
          pageSize: this.pageSettings.pageSize
        }

        const res = await this.$API.supplierVmiManagement.exportInventoryLevelApi(params)
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      } catch (error) {
        console.error('导出失败:', error)
        this.$toast({ content: error || this.$t('导出失败'), type: 'error' })
      } finally {
        item.loading = false
      }
    },

    /**
     * 处理提交成功后的数据刷新
     */
    async handleRefreshAfterSubmit() {
      try {
        // 刷新表格数据
        await this.getTableData()

        // 清除URL中的submitSuccess参数，避免重复刷新
        const query = { ...this.$route.query }
        delete query.submitSuccess

        // 使用replace避免在历史记录中留下带有submitSuccess的URL
        this.$router.replace({
          path: this.$route.path,
          query
        })

        // 可选：显示刷新成功的提示
        console.log('库存水位数据已刷新')
      } catch (error) {
        console.error('刷新库存水位数据失败:', error)
      }
    }
  }
}
</script>
