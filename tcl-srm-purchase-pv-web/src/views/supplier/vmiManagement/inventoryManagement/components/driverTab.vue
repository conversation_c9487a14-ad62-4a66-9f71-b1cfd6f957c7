<template>
  <div class="driver-container">
    <mt-form ref="formRef" :model="formData" :rules="formRules">
      <mt-form-item :label="$t('司机姓名')" prop="driverName" label-style="top">
        <vxe-input
          v-model="formData.driverName"
          :placeholder="$t('请输入司机姓名')"
          clearable
          :disabled="!editable"
          @change="(e) => handleFieldChange('driverName', e)"
        />
      </mt-form-item>
      <mt-form-item :label="$t('联系方式')" prop="driverPhone" label-style="top">
        <vxe-input
          v-model="formData.driverPhone"
          :placeholder="$t('请输入联系方式')"
          clearable
          :disabled="!editable"
          @change="(e) => handleFieldChange('driverPhone', e)"
        />
      </mt-form-item>
      <mt-form-item :label="$t('车牌号')" prop="licensePlate" label-style="top">
        <vxe-input
          v-model="formData.licensePlate"
          :placeholder="$t('请输入车牌号')"
          clearable
          :disabled="!editable"
          @change="(e) => handleFieldChange('licensePlate', e)"
        />
      </mt-form-item>
      <mt-form-item :label="$t('车型')" prop="carModel" label-style="top">
        <vxe-input
          v-model="formData.carModel"
          :placeholder="$t('请输入车型')"
          clearable
          :disabled="!editable"
          @change="(e) => handleFieldChange('carModel', e)"
        />
      </mt-form-item>
    </mt-form>
  </div>
</template>

<script>
export default {
  name: 'DriverTab',
  props: {
    dataInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    // 手机号验证
    const validatePhone = (rule, value, callback) => {
      if (!value) {
        callback()
        return
      }
      if (!/^1[3-9]\d{9}$/.test(value)) {
        callback(new Error(this.$t('请输入正确的手机号')))
      } else {
        callback()
      }
    }
    // 车牌号验证
    const validateCarNo = (rule, value, callback) => {
      if (!value) {
        callback()
        return
      }
      if (
        !/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4,5}[A-Z0-9挂学警港澳]$/.test(
          value
        )
      ) {
        callback(new Error(this.$t('请输入正确的车牌号')))
      } else {
        callback()
      }
    }
    // 身份证号验证
    const validateIdCard = (rule, value, callback) => {
      if (!value) {
        callback()
        return
      }
      if (!/^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dX]$/.test(value)) {
        callback(new Error(this.$t('请输入正确的身份证号')))
      } else {
        callback()
      }
    }

    return {
      formData: {
        driverName: '',
        driverPhone: '',
        licensePlate: '',
        carModel: ''
      },
      formRules: {
        driverName: [
          { required: true, message: this.$t('请输入司机姓名'), trigger: ['blur', 'change'] },
          {
            min: 2,
            max: 20,
            message: this.$t('长度在 2 到 20 个字符'),
            trigger: ['blur', 'change']
          }
        ],
        driverPhone: [
          { required: true, message: this.$t('请输入联系方式'), trigger: ['blur', 'change'] },
          { validator: validatePhone, trigger: ['blur', 'change'] }
        ],
        licensePlate: [
          { required: false, message: this.$t('请输入车牌号'), trigger: ['blur', 'change'] },
          { validator: validateCarNo, trigger: ['blur', 'change'] }
        ]
      }
    }
  },
  computed: {
    pageType() {
      return this.$route.query?.type
    },
    editable() {
      return ['create', 'edit'].includes(this.pageType)
    }
  },
  watch: {
    formData: {
      handler(val) {
        this.$emit('updateDetail', val)
      },
      deep: true
    }
  },
  created() {
    if (this.pageType !== 'create') {
      this.initFormData()
    }
  },
  methods: {
    /**
     * 初始化表单数据
     */
    initFormData() {
      this.formData = {
        driverName: this.dataInfo.driverName || '',
        driverPhone: this.dataInfo.driverPhone || '',
        licensePlate: this.dataInfo.licensePlate || '',
        carModel: this.dataInfo.carModel || ''
      }
    },

    /**
     * 处理字段变化
     */
    handleFieldChange(field, value) {
      // 触发表单验证
      this.$refs.formRef.validateField(field, (errorMessage) => {
        console.log('验证结果：', errorMessage)
      })
    },

    /**
     * 表单验证
     */
    async validate() {
      try {
        const valid = await this.$refs.formRef.validate()
        if (!valid) {
          return false
        }
        return true
      } catch (error) {
        console.error('表单验证失败:', error)
        return false
      }
    },

    /**
     * 重置表单
     */
    resetForm() {
      this.$refs.formRef.resetFields()
      this.formData = {
        driverName: '',
        driverPhone: '',
        licensePlate: '',
        carModel: ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.driver-container {
  padding: 10px;
  .mt-form {
    width: 100%;
    box-sizing: border-box;
    display: grid;
    grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
    justify-content: space-between;
    grid-gap: 5px;
  }
  .vxe-input,
  .vxe-select,
  .vxe-pulldown {
    width: 100%;
    height: 34px;
    line-height: 34px;
  }
  /deep/.vxe-pulldown--panel {
    min-width: unset !important;
    width: 100%;
  }
  :deep(.mt-form-item) {
    .label {
      color: #606266;
      font-size: 14px;
    }
    .vxe-input--inner {
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      &:hover {
        border-color: #c0c4cc;
      }
      &:focus {
        border-color: #409eff;
      }
    }
  }
}
</style>
