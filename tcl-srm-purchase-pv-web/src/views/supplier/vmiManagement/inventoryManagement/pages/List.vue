<!-- 列表 -->
<template>
  <div class="inventory-management-detail">
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item
          v-for="(item, index) in searchFormItems"
          :key="index"
          :label="$t(item.label)"
          :prop="item.prop"
        >
          <component
            :is="item.component"
            v-model="searchFormModel[item.prop]"
            v-bind="item.props"
            @change="item.onChange && item.onChange($event, item.prop)"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="scTableRef"
      row-id="id"
      grid-id="19ff93fb-a80c-8578-d854-c90ac808af82"
      show-overflow
      keep-source
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template #custom-tools>
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          v-bind="item"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #stockInCodeDefault="{ row }">
        <div>
          <span style="cursor: pointer; color: #2783fe" @click="handleClick(row)">
            {{ row.stockInCode }}
          </span>
        </div>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { listColumns, listToolbar, statusOptions, returnStatusOptions } from '../config/index'
import { getSearchFormItems } from '../config/searchForm'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  name: 'InventoryManagementList',
  components: { CollapseSearch, ScTable },

  data() {
    const startDate = dayjs().subtract(1, 'month').startOf('month')
    return {
      loading: false,
      searchFormModel: {
        createTime: [new Date(startDate), new Date()],
        createTimeS: this.getUnix(dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00')),
        createTimeE: this.getUnix(dayjs(new Date()).format('YYYY-MM-DD 23:59:59'))
      },
      searchFormRules: {},
      tableData: [],
      columns: listColumns,
      toolbar: listToolbar,
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      statusOptions,
      returnStatusOptions,
      pdfUrl: ''
    }
  },

  computed: {
    tableRef() {
      return this.$refs.scTableRef?.$refs.xGrid
    },
    searchFormItems() {
      const items = getSearchFormItems({
        statusOptions: this.statusOptions,
        returnStatusOptions: this.returnStatusOptions
      })
      // 为创建时间字段添加onChange事件处理
      const dateFields = ['createTime']
      dateFields.forEach((field) => {
        const item = items.find((item) => item.prop === field)
        if (item) {
          item.onChange = this.handleDateTimeChange
        }
      })
      return items
    }
  },

  created() {
    this.initData()
  },

  methods: {
    async initData() {
      await this.getTableData()
    },

    handleDateTimeChange(e, field) {
      if (!e?.startDate) {
        this.searchFormModel[`${field}S`] = null
        this.searchFormModel[`${field}E`] = null
        return
      }

      this.searchFormModel[`${field}S`] = this.getUnix(
        dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
      )
      this.searchFormModel[`${field}E`] = this.getUnix(
        dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
      )
    },

    getUnix(val) {
      return dayjs(val).valueOf()
    },

    handleReset() {
      this.searchFormModel = {}
      const startDate = dayjs().subtract(1, 'month').startOf('month')
      this.searchFormModel.createTime = [new Date(startDate), new Date()]
      this.searchFormModel.createTimeS = this.getUnix(
        dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00')
      )
      this.searchFormModel.createTimeE = this.getUnix(
        dayjs(new Date()).format('YYYY-MM-DD 23:59:59')
      )
      this.handleSearch()
    },

    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },

    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },

    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },

    async getTableData() {
      try {
        this.loading = true
        const params = {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        }

        const res = await this.$API.supplierVmiManagement.pageInventoryManagementApi(params)

        if (res.code === 200) {
          const { total = 0, records = [] } = res.data
          this.pageSettings.totalRecordsCount = Number(total)
          this.pageSettings.totalPages = Math.ceil(total / this.pageSettings.pageSize)
          this.tableData = records
        }
      } catch (error) {
        console.error('获取表格数据失败:', error)
        this.$toast({ content: error || this.$t('获取数据失败'), type: 'error' })
      } finally {
        this.loading = false
      }
    },

    handleClick(row) {
      const query = {
        id: row.id,
        type: [1].includes(row.status) ? 'edit' : 'view',
        source: 'inventory',
        timeStamp: Date.now()
      }
      sessionStorage.setItem('deliveryPlanSelectedRecords', JSON.stringify([row]))
      this.$router.push({
        path: '/purchase-pv/supplier-vmi-management/inventory-management-detail',
        query
      })
    },

    async handleClickToolBar(item) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (
        selectedRecords.length === 0 &&
        ['delete', 'submit', 'cancel', 'close', 'print'].includes(item.code)
      ) {
        this.$toast({ content: this.$t('请选择要操作的行'), type: 'warning' })
        return
      }

      const actionMap = {
        add: () => this.handleAdd(),
        delete: () => this.showConfirmDialog('删除', () => this.handleDelete(selectedRecords)),
        submit: () => this.showConfirmDialog('提交', () => this.handleSubmit(selectedRecords)),
        cancel: () => this.showConfirmDialog('取消', () => this.handleCancel(selectedRecords)),
        close: () => this.handleClose(selectedRecords),
        export: () => this.handleExport(item),
        print: () => this.showConfirmDialog('打印', () => this.handlePrint(selectedRecords))
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },

    showConfirmDialog(action, callback) {
      return this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认${action}？`)
        },
        success: callback
      })
    },

    handleAdd() {
      // 实现创建VMI入库单逻辑
      this.$router.push({
        path: '/purchase-pv/supplier-vmi-management/delivery-plan',
        query: {
          timeStamp: dayjs().valueOf()
        }
      })
    },

    handleDelete(selectedRecords) {
      let params = {
        ids: selectedRecords.map((item) => item.id)
      }
      this.$API.supplierVmiManagement.deleteInventoryManagementApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },

    handleSubmit(selectedRecords) {
      let params = {
        ids: selectedRecords.map((item) => item.id)
      }
      this.$API.supplierVmiManagement.submitInventoryManagementApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleCancel(selectedRecords) {
      let params = {
        ids: selectedRecords.map((item) => item.id)
      }
      this.$API.supplierVmiManagement.cancelInventoryManagementApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('取消成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },

    handleClose(selectedRecords) {
      let ids = selectedRecords.map((item) => item.id)
      this.$dialog({
        modal: () => import('../components/closeDialog.vue'),
        data: {
          title: this.$t('关闭原因')
        },
        success: (closeReason) => {
          let params = {
            closeReason,
            ids
          }
          this.$API.supplierVmiManagement.closeInventoryManagementApi(params).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('关闭成功'), type: 'success' })
              this.handleSearch()
            }
          })
        }
      })
    },

    async handleExport(item) {
      try {
        item.loading = true
        const params = {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        }

        const res = await this.$API.supplierVmiManagement.exportInventoryManagementApi(params)
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      } catch (error) {
        console.error('导出失败:', error)
        this.$toast({ content: error || this.$t('导出失败'), type: 'error' })
      } finally {
        item.loading = false
      }
    },

    async handlePrint(selectedRecords) {
      try {
        let params = { ids: selectedRecords.map((item) => item.id) }
        const res = await this.$API.supplierVmiManagement.printInventoryManagementApi(params)
        const content = res.data
        this.pdfUrl = window.URL.createObjectURL(
          new Blob([content], { type: 'text/html;charset=utf-8' })
        )
        let date = new Date().getTime()
        let ifr = document.createElement('iframe')
        ifr.style.frameborder = 'no'
        ifr.style.display = 'none'
        ifr.style.pageBreakBefore = 'always'
        ifr.setAttribute('id', 'printPdf' + date)
        ifr.setAttribute('name', 'printPdf' + date)
        ifr.src = this.pdfUrl
        document.body.appendChild(ifr)
        this.doPrint('printPdf' + date)
        window.URL.revokeObjectURL(ifr.src)
      } catch (error) {
        console.error('打印失败:', error)
        this.$toast({ content: error || this.$t('打印失败'), type: 'error' })
      }
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
      }, 100)
    }
  }
}
</script>
