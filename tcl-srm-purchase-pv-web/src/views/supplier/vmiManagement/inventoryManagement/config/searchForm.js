/**
 * VMI入库管理搜索表单配置
 */

export const getSearchFormItems = ({ statusOptions, returnStatusOptions }) => [
  {
    label: 'SRM入库单号',
    prop: 'stockInCode',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '工厂编码',
    prop: 'siteCode',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: 'VMI仓编码',
    prop: 'warehouseCode',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '入库状态',
    prop: 'status',
    component: 'mt-select',
    props: {
      placeholder: '请选择',
      dataSource: statusOptions,
      showClearButton: true
    }
  },
  {
    label: '退货状态',
    prop: 'returnStatus',
    component: 'mt-select',
    props: {
      placeholder: '请选择',
      dataSource: returnStatusOptions,
      showClearButton: true
    }
  },
  {
    label: '创建人',
    prop: 'createUserName',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '创建时间',
    prop: 'createTime',
    component: 'mt-date-range-picker',
    props: { placeholder: '请选择' }
  }
]

export const getDetailSearchFormItems = ({ statusOptions }) => [
  {
    label: 'SRM入库单号',
    prop: 'stockInCode',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '送货计划单号',
    prop: 'deliveryPlanNo',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '工厂编码',
    prop: 'siteCode',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '物料编码',
    prop: 'itemCodeStr',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: 'VMI仓编码',
    prop: 'warehouseCode',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '入库状态',
    prop: 'status',
    component: 'mt-select',
    props: {
      placeholder: '请选择',
      dataSource: statusOptions,
      showClearButton: true
    }
  },
  {
    label: '创建人',
    prop: 'createUserName',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '创建时间',
    prop: 'createTime',
    component: 'mt-date-range-picker',
    props: { placeholder: '请选择' }
  }
]
