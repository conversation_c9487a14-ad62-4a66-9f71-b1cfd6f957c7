<!-- 创建送货计划 -->
<template>
  <div class="postpone-full-height">
    <div class="top-container toggle-container" :class="[!isExpand && 'top-hidden']">
      <div class="top-content">
        <div class="top-info">
          <div class="top-title">
            <span>{{ $t('仓库编码') }}：{{ formData.warehouseCode || '-' }}</span>
            <span>{{ $t('仓库名称') }}：{{ formData.warehouseName || '-' }}</span>
            <span>{{ $t('省区') }}：{{ `${formData.province}-${formData.city}` || '-' }}</span>
            <span
              >{{ $t('采购计划负责人') }}：{{
                `${formData.buyerPersonCode}-${formData.buyerPersonName}` || '-'
              }}
            </span>
          </div>
          <div class="top-btns">
            <vxe-button
              v-for="(item, index) in detailToolbar"
              v-show="!item.isHidden"
              :key="index"
              :icon="item.icon"
              :status="item.status"
              :disabled="item.disabled"
              size="small"
              @click="handleClickToolBar(item)"
              >{{ item.name }}</vxe-button
            >
          </div>
        </div>
        <div style="padding-bottom: 10px">
          <span>{{ $t('工厂') }}：{{ `${formData.siteCode}-${formData.siteName}` || '-' }} </span>
        </div>
        <div class="top-table">
          <sc-table
            ref="scTableRef"
            :loading="loading"
            :columns="columns"
            :table-data="tableData"
            :fix-height="130"
            keep-source
            :sortable="false"
            :is-show-right-btn="false"
            :is-show-column-config="false"
          >
          </sc-table>
        </div>
      </div>
      <div class="top-toggle-btn">
        <div @click="handleToggle"><i class="vxe-icon-arrow-up" /></div>
      </div>
    </div>
    <div class="body-container">
      <mt-tabs
        ref="tabsRef"
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabList"
        :halt-select="false"
        @handleSelectTab="(index) => handleTabChange(index)"
        style="background-color: #fff"
      />
      <keep-alive :include="keepArr">
        <component
          ref="mainContent"
          style="height: calc(100% - 50px)"
          :is="activeComponent"
          :data-info="formData"
          :table-data="tableData"
          @updateDetail="updateDetail"
        />
      </keep-alive>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import ScTable from '@/components/ScTable/src/index'
import errorHandler from '@/mixins/errorHandler'

export default {
  name: 'DeliveryPlanDetail',
  components: { ScTable },
  mixins: [errorHandler],
  data() {
    return {
      isExpand: true,
      formData: {},
      activeTabIndex: 0,
      keepArr: ['ItemTab'],
      loading: false,
      tableData: [],
      itemDataList: [] // 物料明细
    }
  },
  computed: {
    pageType() {
      return this.$route.query?.type || 'create'
    },
    columns() {
      return [
        {
          field: 'planAvailableStock',
          title: this.$t('VMI可用库存-计划')
        },
        {
          field: 'planOnwayStock',
          title: this.$t('VMI在途库存-计划')
        },
        {
          headerAlign: 'center',
          title: this.$t('当前VMI库存数据（MW）'),
          children: [
            {
              field: 'availableStock',
              title: this.$t('可用库存')
            },
            {
              field: 'allocationStock',
              title: this.$t('占用库存')
            },
            {
              field: 'buyerOnwayStock',
              title: this.$t('采购在途库存'),
              minWidth: 120
            },
            {
              field: 'transferOnwayStock',
              title: this.$t('调拨在途库存'),
              minWidth: 120
            }
          ]
        },
        {
          field: 'logisticsTime',
          title: this.$t('物流时间')
        },
        {
          field: 'maxUnloadCap',
          title: this.$t('仓库每日最大卸货能力')
        },
        {
          field: 'residualArea',
          title: this.$t('剩余面积')
        },
        {
          field: 'onloadResidArea',
          title: this.$t('剩余面积（含在途）')
        },
        {
          field: 'residAvailCap',
          title: this.$t('剩余可用库容（MW）')
        },
        {
          field: 'onloadResidAvailCap',
          title: this.$t('剩余可用库容（含在途-MW）')
        },
        {
          field: 'purchaseUnshipmentNum',
          title: i18n.t('采购待发货数量')
        }
      ]
    },
    editable() {
      return ['create', 'edit'].includes(this.pageType)
    },
    detailToolbar() {
      return [
        {
          code: 'back',
          name: this.$t('返回')
        },
        {
          code: 'save',
          name: this.$t('保存'),
          status: 'primary',
          isHidden: !this.editable
        },
        {
          code: 'submit',
          name: this.$t('保存并提交'),
          status: 'primary',
          isHidden: !this.editable
        }
      ]
    },
    tabList() {
      return [{ title: this.$t('物料明细'), compName: 'ItemTab' }]
    },
    activeComponent() {
      let comp = ''
      switch (this.activeTabIndex) {
        case 0:
          comp = () => import('./components/itemTab.vue')
          break
        default:
          break
      }
      return comp
    }
  },
  created() {
    this.initData()
  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      try {
        this.formData = JSON.parse(this.$route.query?.record || '{}')
        this.formData.siteCode = this.formData?.siteCode || this.formData?.organizationCode
        this.formData.siteName = this.formData?.siteName || this.formData?.organizationName
        this.tableData = [
          {
            planAvailableStock: this.formData?.planAvailableStock,
            planOnwayStock: this.formData?.planOnwayStock,
            availableStock: this.formData?.availableStock,
            allocationStock: this.formData?.allocationStock,
            onWayStock: this.formData?.onWayStock,
            buyerOnwayStock: this.formData?.buyerOnwayStock,
            transferOnwayStock: this.formData?.transferOnwayStock,
            logisticsTime: this.formData?.logisticsTime,
            maxUnloadCap: this.formData?.maxUnloadCap,
            residualArea: this.formData?.residualArea,
            onloadResidArea: this.formData?.onloadResidArea,
            residAvailCap: this.formData?.residAvailCap,
            onloadResidAvailCap: this.formData?.onloadResidAvailCap,
            purchaseUnshipmentNum: this.formData?.purchaseUnshipmentNum
          }
        ]

        if (this.pageType !== 'create') {
          this.formData.id = this.$route.query?.id
        }

        this.getBuyerPerson()
      } catch (error) {
        this.handleApiError(error, '初始化数据失败')
      }
    },

    /**
     * 获取采购员信息
     */
    async getBuyerPerson() {
      try {
        const params = {
          warehouseCode: this.formData.warehouseCode,
          status: 0,
          page: {
            current: 1,
            size: 10
          }
        }
        const res = await this.$API.vmiManagement.pagePlanManagerApi(params)
        if (res.code === 200 && res.data.records.length === 1) {
          this.$set(this.formData, 'buyerPersonCode', res.data.records[0].plannerCode)
          this.$set(this.formData, 'buyerPersonName', res.data.records[0].plannerName)
        }
      } catch (error) {
        this.handleApiError(error, '获取采购员信息失败')
      }
    },

    /**
     * 更新明细数据
     * @param {Array} data - 明细数据
     */
    updateDetail(data) {
      switch (this.activeTabIndex) {
        case 0:
          this.itemDataList = data
          break
        default:
          break
      }
    },

    /**
     * 处理工具栏点击事件
     * @param {Object} item - 工具栏项
     */
    async handleClickToolBar(item) {
      const actionMap = {
        back: () => this.handleBack(),
        save: () => this.handleSave('save'),
        submit: () => this.showConfirmDialog('保存并提交', () => this.handleSubmit())
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },

    /**
     * 保存数据
     */
    async handleSave(type) {
      try {
        if (this.itemDataList.length === 0) {
          this.handleBusinessError('请新增物料明细')
          return
        }

        // 保存前先创建送货计划
        if (!this.formData.id) {
          await this.createDeliveryPlan()
        }

        const params = {
          deliveryPlanId: this.formData.id,
          addOrUpdateDtoList: this.itemDataList.map((item) => ({
            ...item,
            id: item.id?.includes('row_') ? null : item.id,
            planDeliveryTime: dayjs(item.planDeliveryTime).valueOf()
          }))
        }

        const res = await this.$API.supplierVmiManagement.saveDetailDeliveryPlanApi(params)
        if (res.code === 200) {
          this.$toast({ content: this.$t('保存成功'), type: 'success' })

          if (type === 'save') {
            this.$router.push({
              path: '/purchase-pv/supplier-vmi-management/delivery-plan-create',
              query: {
                type: 'edit',
                id: this.formData.id,
                record: this.$route.query?.record,
                timeStamp: Date.now()
              }
            })
          }
        }
      } catch (error) {
        this.handleApiError(error, '保存失败')
      }
    },

    /**
     * 创建送货计划
     */
    async createDeliveryPlan() {
      try {
        const params = { ...this.formData }
        params.id = params.id?.includes('row_') ? null : params.id
        const res = await this.$API.supplierVmiManagement.createDeliveryPlanApi(params)
        if (res.code === 200) {
          this.formData.id = res.data
        }
      } catch (error) {
        this.handleApiError(error, '创建送货计划失败')
        throw error
      }
    },

    /**
     * 提交数据
     */
    async handleSubmit() {
      try {
        // 提交前先保存
        await this.handleSave('submit')

        if (!this.formData.id) {
          return
        }

        const params = { ids: [this.formData.id] }
        const res = await this.$API.supplierVmiManagement.submitDeliveryPlanApi(params)
        if (res.code === 200) {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })

          // 优化后的跳转逻辑：直接跳转到送货计划列表页面
          this.handleNavigateAfterSubmit()
        }
      } catch (error) {
        this.handleApiError(error, '提交失败')
      }
    },

    /**
     * 提交成功后的导航处理
     */
    handleNavigateAfterSubmit() {
      try {
        // 获取来源页面信息
        const fromPage = this.$route.query?.from || 'delivery-plan'
        const returnPath = this.getReturnPath(fromPage)

        // 使用 replace 避免在浏览器历史中留下当前页面
        this.$router.replace({
          path: returnPath,
          query: {
            timeStamp: Date.now(),
            // 传递提交成功的标识，用于目标页面刷新数据
            submitSuccess: 'true'
          }
        })
      } catch (error) {
        console.error('导航失败:', error)
        // 降级处理：直接跳转到送货计划列表
        this.$router.replace({
          path: '/purchase-pv/supplier-vmi-management/delivery-plan',
          query: { timeStamp: Date.now() }
        })
      }
    },

    /**
     * 根据来源页面确定返回路径
     * @param {string} fromPage - 来源页面标识
     * @returns {string} 返回路径
     */
    getReturnPath(fromPage) {
      const pathMap = {
        'inventory-level': '/purchase-pv/supplier-vmi-management/inventory-level',
        'delivery-plan': '/purchase-pv/supplier-vmi-management/delivery-plan'
      }

      return pathMap[fromPage] || '/purchase-pv/supplier-vmi-management/delivery-plan'
    },

    /**
     * 返回上一页
     */
    handleBack() {
      // 优化返回逻辑：考虑是否有未保存的更改
      if (this.hasUnsavedChanges()) {
        this.showConfirmDialog('有未保存的更改，确定要离开吗？', () => {
          this.navigateBack()
        })
      } else {
        this.navigateBack()
      }
    },

    /**
     * 检查是否有未保存的更改
     * @returns {boolean}
     */
    hasUnsavedChanges() {
      // 这里可以添加具体的检查逻辑
      // 比如对比当前数据与初始数据
      return false
    },

    /**
     * 执行返回导航
     */
    navigateBack() {
      // 优先使用浏览器历史记录返回
      if (window.history.length > 1) {
        this.$router.go(-1)
      } else {
        // 如果没有历史记录，跳转到默认页面
        const fromPage = this.$route.query?.from || 'delivery-plan'
        const returnPath = this.getReturnPath(fromPage)
        this.$router.replace({ path: returnPath })
      }
    },

    /**
     * 展开/收缩
     */
    handleToggle() {
      this.isExpand = !this.isExpand
    }
  }
}
</script>

<style scoped lang="scss">
.postpone-full-height {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 8px 8px 0;
  background: #fff;
}
.body-container {
  height: calc(100% - 80px);
}
.top-container {
  flex: 1;
  width: 100%;
  .top-content {
    padding: 8px 8px 0 8px;
    background-color: #f9f9f9;
    border-radius: 8px;
    .top-info {
      display: flex;
      justify-content: space-between;
      .title {
        font-size: 20px;
        font-weight: 700;
        color: #292929;
        span {
          margin-right: 15px;
        }
        .sub-title {
          font-size: 15px;
          color: #777;
        }
      }
      .top-title {
        display: flex;
        padding-top: 10px;
        span {
          margin-right: 35px;
        }
      }
      .top-btns {
        text-align: right;
        margin-bottom: 10px;
      }
    }
    .top-table {
      padding-bottom: 8px;
    }
  }
  .top-toggle-btn {
    width: 100%;
    div {
      width: 80px;
      height: 15px;
      line-height: 15px;
      margin: auto;
      text-align: center;
      background-color: #4a556b;
      color: #fff;
      font-weight: bold;
      border-radius: 0 0 6px 6px;
    }
  }
}

.top-hidden {
  .top-table {
    display: none;
  }
  .top-toggle-btn {
    div {
      transform: rotate(180deg);
      border-radius: 6px 6px 0 0;
    }
  }
}

.my-dropdown2 {
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 4px 0;
  max-height: 200px;
  border-radius: 4px;
  border: 1px solid #dadce0;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}
.list-item2 {
  line-height: 30px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 0 4px;

  .isSelected {
    font-weight: 700;
    color: #409eff;
  }
}
.list-item2:hover {
  background-color: #f5f7fa;
}
.empty-tip {
  width: 100%;
  line-height: 30px;
  padding: 0 4px;
  text-align: center;
  color: #c0c4cc;
}

::v-deep {
  .mt-form-item {
    margin-bottom: 15px;
    .label {
      margin: 0 10px 5px 0;
    }
  }
  .mt-tabs-container {
    background-color: #fff;
  }

  .mt-tabs-container ul.tab-container li.tab-item2.active {
    border-bottom: 2px solid #409eff;
  }
  .mt-tabs-container ul.tab-container li.tab-item2.active,
  .mt-tabs-container ul.tab-container li.tab-item2:hover {
    color: #31374e;
  }

  .vxe-textarea--inner {
    min-height: 34px;
    height: 100%;
  }
}
</style>
