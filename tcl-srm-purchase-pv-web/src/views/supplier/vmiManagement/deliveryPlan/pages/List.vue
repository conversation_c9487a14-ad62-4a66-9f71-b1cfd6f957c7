<!-- 列表 -->
<template>
  <div class="delivery-plan-list">
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item
          v-for="(item, index) in searchFormItems"
          :key="index"
          :label="$t(item.label)"
          :prop="item.prop"
        >
          <component
            :is="item.component"
            v-model="searchFormModel[item.prop]"
            v-bind="item.props"
            @change="item.onChange && item.onChange($event, item.prop)"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="scTableRef"
      row-id="id"
      grid-id="b9f875fa-6947-4529-8ec3-185257b4e8a4"
      show-overflow
      keep-source
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template #custom-tools>
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          v-bind="item"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #deliveryPlanCodeDefault="{ row }">
        <div style="cursor: pointer; color: #2783fe" @click="handleClick(row)">
          {{ row.deliveryPlanCode }}
        </div>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { listColumns, listToolbar, statusOptions } from '../config/index'
import { getHeadersFileName, download } from '@/utils/utils'

export default {
  name: 'SupplierDeliveryPlanList',
  components: { CollapseSearch, ScTable },

  data() {
    const startDate = dayjs().subtract(1, 'month').startOf('month')
    return {
      loading: false,
      searchFormModel: {
        createTime: [new Date(startDate), new Date()],
        createTimeS: this.getUnix(dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00')),
        createTimeE: this.getUnix(dayjs(new Date()).format('YYYY-MM-DD 23:59:59'))
      },
      searchFormRules: {},
      tableData: [],
      columns: listColumns,
      toolbar: listToolbar,
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      statusOptions
    }
  },

  computed: {
    tableRef() {
      return this.$refs.scTableRef?.$refs.xGrid
    },
    searchFormItems() {
      return [
        {
          label: this.$t('送货计划单号'),
          prop: 'deliveryPlanCode',
          component: 'mt-input',
          props: { placeholder: this.$t('请输入'), showClearButton: true }
        },
        {
          label: this.$t('省'),
          prop: 'province',
          component: 'mt-input',
          props: { placeholder: this.$t('请输入'), showClearButton: true }
        },
        {
          label: this.$t('区'),
          prop: 'city',
          component: 'mt-input',
          props: { placeholder: this.$t('请输入'), showClearButton: true }
        },
        {
          label: this.$t('仓库编码'),
          prop: 'warehouseCode',
          component: 'mt-input',
          props: { placeholder: this.$t('请输入'), showClearButton: true }
        },
        {
          label: this.$t('仓库名称'),
          prop: 'warehouseName',
          component: 'mt-input',
          props: { placeholder: this.$t('请输入'), showClearButton: true }
        },
        {
          label: this.$t('仓库地址'),
          prop: 'warehouseAddr',
          component: 'mt-input',
          props: { placeholder: this.$t('请输入'), showClearButton: true }
        },
        {
          label: this.$t('状态'),
          prop: 'statusList',
          component: 'mt-multi-select',
          props: {
            placeholder: this.$t('请选择'),
            dataSource: this.statusOptions,
            showClearButton: true
          }
        },
        {
          label: this.$t('创建人'),
          prop: 'createUserName',
          component: 'mt-input',
          props: { placeholder: this.$t('请输入'), showClearButton: true }
        },
        {
          label: this.$t('创建时间'),
          prop: 'createTime',
          component: 'mt-date-range-picker',
          props: { placeholder: this.$t('请选择') },
          onChange: (e) => this.dateTimeChange(e, 'createTime')
        }
      ]
    }
  },

  created() {
    this.initData()
  },

  watch: {
    // 监听路由变化，处理提交成功后的数据刷新
    '$route.query.submitSuccess': {
      handler(newVal) {
        if (newVal === 'true') {
          // 延迟刷新，确保页面已完全加载
          this.$nextTick(() => {
            this.handleRefreshAfterSubmit()
          })
        }
      },
      immediate: true
    }
  },

  methods: {
    async initData() {
      await this.getTableData()
    },

    dateTimeChange(e, field) {
      if (!e?.startDate) {
        this.searchFormModel[`${field}S`] = null
        this.searchFormModel[`${field}E`] = null
        return
      }

      this.searchFormModel[`${field}S`] = this.getUnix(
        dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
      )
      this.searchFormModel[`${field}E`] = this.getUnix(
        dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
      )
    },

    getUnix(val) {
      return dayjs(val).valueOf()
    },

    handleReset() {
      this.searchFormModel = {}
      const startDate = dayjs().subtract(1, 'month').startOf('month')
      this.searchFormModel.createTime = [new Date(startDate), new Date()]
      this.searchFormModel.createTimeS = this.getUnix(
        dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00')
      )
      this.searchFormModel.createTimeE = this.getUnix(
        dayjs(new Date()).format('YYYY-MM-DD 23:59:59')
      )
      this.handleSearch()
    },

    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },

    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },

    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },

    async getTableData() {
      try {
        this.loading = true
        const params = {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        }

        const res = await this.$API.supplierVmiManagement.pageDeliveryPlanApi(params)

        if (res.code === 200) {
          const { total = 0, records = [] } = res.data
          this.pageSettings.totalRecordsCount = Number(total)
          this.pageSettings.totalPages = Math.ceil(total / this.pageSettings.pageSize)
          this.tableData = records
        }
      } catch (error) {
        console.error('获取表格数据失败:', error)
        this.$toast({ content: error || this.$t('获取数据失败'), type: 'error' })
      } finally {
        this.loading = false
      }
    },

    handleClick(row) {
      this.$router.push({
        path: '/purchase-pv/supplier-vmi-management/delivery-plan-create',
        query: {
          type: [0, 3].includes(row.status) ? 'edit' : 'view',
          id: row.id,
          record: JSON.stringify(row),
          timeStamp: Date.now()
        }
      })
    },

    async handleClickToolBar(item) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (
        selectedRecords.length === 0 &&
        ['submit', 'delete', 'cancel', 'create'].includes(item.code)
      ) {
        this.$toast({ content: this.$t('请选择要操作的行'), type: 'warning' })
        return
      }

      if (selectedRecords.length > 1 && ['create'].includes(item.code)) {
        this.$toast({ content: this.$t('只能选择一行进行操作'), type: 'warning' })
        return
      }

      const actionMap = {
        add: this.handleAdd,
        submit: () => this.showConfirmDialog('提交', () => this.handleSubmit(selectedRecords)),
        delete: () => this.showConfirmDialog('删除', () => this.handleDelete(selectedRecords)),
        cancel: () => this.showConfirmDialog('取消', () => this.handleCancel(selectedRecords)),
        create: () => this.handleCreate(selectedRecords),
        export: () => this.handleExport(item)
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },

    showConfirmDialog(action, callback) {
      return this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认${action}选中的送货计划？`)
        },
        success: callback
      })
    },

    handleAdd() {
      this.$router.push({
        path: '/purchase-pv/supplier-vmi-management/inventory-level',
        query: {
          timeStamp: Date.now()
        }
      })
    },

    async handleSubmit(selectedRecords) {
      const params = {
        ids: selectedRecords.map((item) => item.id)
      }

      const res = await this.$API.supplierVmiManagement.submitDeliveryPlanApi(params)
      if (res.code === 200) {
        this.$toast({ content: this.$t('提交成功'), type: 'success' })
        this.handleSearch()
      }
    },

    async handleDelete(selectedRecords) {
      const params = {
        ids: selectedRecords.map((item) => item.id)
      }

      const res = await this.$API.supplierVmiManagement.deleteDeliveryPlanApi(params)
      if (res.code === 200) {
        this.$toast({ content: this.$t('删除成功'), type: 'success' })
        this.handleSearch()
      }
    },

    async handleCancel(selectedRecords) {
      const params = {
        ids: selectedRecords.map((item) => item.id)
      }

      const res = await this.$API.supplierVmiManagement.cancelDeliveryPlanApi(params)
      if (res.code === 200) {
        this.$toast({ content: this.$t('取消成功'), type: 'success' })
        this.handleSearch()
      }
    },

    handleCreate(selectedRecords) {
      if (selectedRecords[0].status !== 2) {
        this.$toast({ content: this.$t('只能选择【待发货】状态的数据进行操作'), type: 'warning' })
        return
      }
      const row = { ...selectedRecords[0], id: null }
      const query = {
        type: 'create',
        record: JSON.stringify(row),
        timeStamp: Date.now()
      }
      // 跳转创建VMI入库单页面
      this.$router.push({
        path: '/purchase-pv/supplier-vmi-management/inventory-management-detail',
        query
      })
    },

    async handleExport(item) {
      try {
        item.loading = true
        const params = {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        }

        const res = await this.$API.supplierVmiManagement.exportDeliveryPlanApi(params)
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      } catch (error) {
        console.error('导出失败:', error)
        this.$toast({ content: error || this.$t('导出失败'), type: 'error' })
      } finally {
        item.loading = false
      }
    },

    /**
     * 处理提交成功后的数据刷新
     */
    async handleRefreshAfterSubmit() {
      try {
        // 刷新表格数据
        await this.getTableData()

        // 清除URL中的submitSuccess参数，避免重复刷新
        const query = { ...this.$route.query }
        delete query.submitSuccess

        // 使用replace避免在历史记录中留下带有submitSuccess的URL
        this.$router.replace({
          path: this.$route.path,
          query
        })

        // 可选：显示刷新成功的提示
        console.log('数据已刷新')
      } catch (error) {
        console.error('刷新数据失败:', error)
      }
    }
  }
}
</script>
