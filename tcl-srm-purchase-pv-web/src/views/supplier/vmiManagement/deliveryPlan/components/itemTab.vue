<template>
  <div>
    <sc-table
      ref="scTableRef"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      keep-source
      :sortable="false"
      :is-show-right-btn="true"
      :is-show-refresh-bth="pageType !== 'create'"
      @refresh="getTableData"
      @edit-closed="editComplete"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
    <mt-page
      v-if="pageType !== 'create'"
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import VxeRemoteSearch from '@/components/VxeRemoteSearch/index.vue'
import errorHandler from '@/mixins/errorHandler'

export default {
  name: 'ItemTab',
  components: { ScTable, VxeRemoteSearch },
  mixins: [errorHandler],
  props: {
    dataInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 100,
        pageSizes: [100, 200, 500, 1000]
      },
      editRules: {
        itemCode: [{ required: true, message: this.$t('必填') }],
        planDeliveryTime: [{ required: true, message: this.$t('必填') }],
        planDeliveryQty: [{ required: true, message: this.$t('必填') }]
      }
    }
  },
  computed: {
    pageType() {
      return this.$route.query?.type || 'create'
    },
    editable() {
      return ['create', 'edit'].includes(this.pageType)
    },
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'itemCode',
          title: this.$t('物料编码'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.itemCode}
                  fields={{ text: 'itemName', value: 'itemCode' }}
                  placeholder={this.$t('请选择')}
                  request-info={{
                    urlPre: 'masterData',
                    url: 'getItemPage',
                    searchFields: ['itemCode', 'itemName'],
                    params: {
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  onChange={(e) => {
                    if (!e?.checked) {
                      row.itemName = e?.itemName || null
                      row.specificationModel = e?.itemModelName || null
                      row.unitCode = e?.baseMeasureUnitCode || null
                      row.unit = e?.baseMeasureUnitName || null
                      row.catoryCode = e?.categoryResponse?.categoryCode || null
                      row.catoryName = e?.categoryResponse?.categoryName || null
                    }
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'itemName',
          title: this.$t('物料名称')
        },
        {
          field: 'catoryCode',
          title: this.$t('品类'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.catoryName : ''
          }
        },
        {
          field: 'specificationModel',
          title: this.$t('规格型号')
        },
        {
          field: 'unitCode',
          title: this.$t('单位'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.unit : ''
          }
        },
        {
          field: 'planDeliveryTime',
          title: this.$t('计划送货日期'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              const dateDisabled = (params) => {
                // 验证参数
                if (!params || !params.date || !(params.date instanceof Date)) {
                  return true
                }

                const { date } = params
                // 获取UTC今天0点
                const today = new Date()
                const todayUTC = Date.UTC(today.getFullYear(), today.getMonth(), today.getDate())

                // 计算UTC时间下允许的最大日期(T+3天)
                const maxDateUTC = todayUTC + 3 * 24 * 60 * 60 * 1000

                // 获取输入日期的UTC时间
                const dateUTC = Date.UTC(date.getFullYear(), date.getMonth(), date.getDate())

                // 返回是否在允许范围外(不在[T, T+3]区间内)
                return dateUTC < todayUTC || dateUTC > maxDateUTC
              }
              return [
                <vxe-input
                  type={'date'}
                  v-model={row.planDeliveryTime}
                  placeholder={this.$t('请选择')}
                  transfer
                  clearable
                  label-format='yyyy-MM-dd'
                  value-format='yyyy-MM-dd'
                  disabled-method={dateDisabled}
                />
              ]
            }
          }
        },
        {
          field: 'planDeliveryQty',
          title: this.$t('计划送货量'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='integer'
                  v-model={row.planDeliveryQty}
                  placeholder={this.$t('请输入')}
                  min='0'
                  transfer
                  clearable
                />
              ]
            }
          }
        }
      ]
    },
    editConfig() {
      return {
        enabled: this.editable,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    toolbar() {
      let btns = []
      if (this.editable) {
        btns = [
          { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
          { code: 'delete', name: this.$t('删除'), status: 'info', loading: false }
        ]
        // 只有编辑模式才显示导入按钮
        if (this.pageType === 'edit') {
          btns.push({ code: 'import', name: this.$t('导入'), status: 'info', loading: false })
        }
      }
      return btns
    }
  },
  mounted() {
    if (this.pageType === 'create') {
      this.tableData = []
    } else {
      this.getTableData()
    }
  },
  methods: {
    /**
     * 处理页码变化
     * @param {number} currentPage - 当前页码
     */
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },

    /**
     * 处理每页条数变化
     * @param {number} pageSize - 每页条数
     */
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },

    /**
     * 获取表格数据
     */
    async getTableData() {
      try {
        this.loading = true
        const params = {
          deliveryPlanIds: [this.$route.query?.id],
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          }
        }
        const res = await this.$API.supplierVmiManagement.pageDeliveryPlanDetailApi(params)
        if (res.code === 200) {
          const { total = 0, records = [] } = res.data
          this.pageSettings.totalRecordsCount = Number(total)
          this.pageSettings.totalPages = Math.ceil(total / this.pageSettings.pageSize)
          this.tableData = records
          this.$emit('updateDetail', this.tableData)
        }
      } catch (error) {
        this.handleApiError(error, '获取表格数据失败')
      } finally {
        this.loading = false
      }
    },

    /**
     * 处理工具栏点击事件
     * @param {Object} e - 工具栏项
     */
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['delete']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.handleBusinessError('请先选择一行')
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          this.handleDelete()
          break
        case 'import':
          this.handleImport()
          break
        default:
          break
      }
    },

    /**
     * 处理新增
     */
    async handleAdd() {
      const item = {
        itemCode: this.dataInfo.productCode
      }

      // 如果有物料编码，根据物料编码带出其他字段
      if (this.dataInfo.productCode) {
        try {
          const itemInfo = await this.getItemInfo(this.dataInfo.productCode)
          if (itemInfo) {
            item.itemName = itemInfo.itemName || null
            item.specificationModel = itemInfo.itemModelName || null
            item.unitCode = itemInfo.baseMeasureUnitCode || null
            item.unit = itemInfo.baseMeasureUnitName || null
            item.catoryCode = itemInfo.categoryResponse?.categoryCode || null
            item.catoryName = itemInfo.categoryResponse?.categoryName || null
          }
        } catch (error) {
          console.warn('获取物料信息失败:', error)
        }
      }

      this.tableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.tableRef.setEditRow(currentViewRecords[0])
      })
    },

    /**
     * 根据物料编码获取物料信息
     * @param {string} itemCode - 物料编码
     * @returns {Object|null} 物料信息
     */
    async getItemInfo(itemCode) {
      try {
        const params = {
          page: {
            current: 1,
            size: 10
          },
          rules: [
            {
              field: 'itemCode',
              label: '',
              operator: 'contains',
              type: 'string',
              value: itemCode
            }
          ]
        }

        const res = await this.$API.masterData.getItemPage(params)
        if (res.code === 200 && res.data?.records?.length > 0) {
          return res.data.records[0]
        }
        return null
      } catch (error) {
        console.error('获取物料信息失败:', error)
        return null
      }
    },

    /**
     * 处理删除
     */
    handleDelete() {
      this.tableRef.removeCheckboxRow()
      const currentViewRecords = this.tableRef.getTableData().visibleData
      this.$emit('updateDetail', currentViewRecords)
    },

    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog/index.vue'),
        data: {
          title: this.$t('导入'),
          paramsKey: 'excel',
          importApi: this.$API.supplierVmiManagement.importInventoryManagementApi,
          downloadTemplateApi:
            this.$API.supplierVmiManagement.downloadInventoryManagementTemplateApi,
          downloadTemplateParams: {
            deliveryPlanIds: [this.$route.query?.id],
            page: {
              current: 1,
              size: 10
            }
          },
          asyncParams: {
            deliveryPlanId: this.$route.query?.id
          }
        },
        success: () => {
          this.getTableData()
        }
      })
    },

    /**
     * 编辑完成
     * @param {Object} args - 编辑参数
     */
    editComplete(args) {
      if (args.$event) {
        const currentViewRecords = this.tableRef.getTableData().visibleData
        this.$emit('updateDetail', currentViewRecords)
      }
    }
  }
}
</script>

<style></style>
