import { i18n } from '@/main.js'

export const statusOptions = [
  { value: 2, text: i18n.t('待供方确认') },
  { value: 3, text: i18n.t('待出库退货') },
  { value: 4, text: i18n.t('已退货') },
  { value: 5, text: i18n.t('供方已拒绝') },
  { value: 6, text: i18n.t('已取消') }
]

export const returnTypeOptions = [
  { value: 0, text: i18n.t('正常退货'), label: i18n.t('正常退货') },
  { value: 1, text: i18n.t('货损'), label: i18n.t('货损') }
]

export const warehouseTypeOptions = [
  { value: 'HY', text: i18n.t('户用RDC仓'), label: i18n.t('户用RDC仓') },
  { value: 'SY', text: i18n.t('商用仓'), label: i18n.t('商用仓') },
  { value: 'HYYW', text: i18n.t('户用运维RDC仓'), label: i18n.t('户用运维RDC仓') },
  { value: 'HW', text: i18n.t('海外仓'), label: i18n.t('海外仓') },
  { value: 'VMI', text: i18n.t('VMI仓库'), label: i18n.t('VMI仓库') }
]

export const syncJgStatusOptions = [
  { text: i18n.t('未同步'), value: 0 },
  { text: i18n.t('同步中'), value: 1 },
  { text: i18n.t('同步成功'), value: 2 },
  { text: i18n.t('同步失败'), value: 3 }
]

export const listToolbar = [
  {
    code: 'confirm',
    name: i18n.t('确认退货'),
    status: 'info',
    loading: false
  },
  {
    code: 'reject',
    name: i18n.t('驳回'),
    status: 'info',
    loading: false
  },
  {
    code: 'cancel',
    name: i18n.t('取消'),
    status: 'info',
    loading: false
  },
  {
    code: 'export',
    name: i18n.t('导出'),
    status: 'info',
    loading: false
  }
]

export const detailToolbar = [
  {
    code: 'export',
    name: i18n.t('导出'),
    status: 'info',
    loading: false
  }
]

export const listColumns = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'returnCode',
    title: i18n.t('SRM退货单号'),
    minWidth: 180,
    slots: {
      default: 'returnCodeDefault'
    }
  },
  {
    field: 'returnStatus',
    title: i18n.t('退货状态'),
    formatter: ({ cellValue }) =>
      statusOptions.find((item) => item.value === cellValue)?.text ?? '-'
  },
  {
    field: 'stockInCode',
    title: i18n.t('入库单号'),
    minWidth: 180
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂编码')
  },
  {
    field: 'siteName',
    title: i18n.t('工厂名称')
  },
  {
    field: 'warehouseType',
    title: i18n.t('仓库类型'),
    formatter: ({ cellValue }) =>
      warehouseTypeOptions.find((item) => item.value === cellValue)?.text ?? '-'
  },
  {
    field: 'warehouseCode',
    title: i18n.t('入库仓库'),
    minWidth: 120,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.warehouseName : ''
    }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码'),
    minWidth: 120
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    minWidth: 120
  },
  {
    field: 'returnType',
    title: i18n.t('退货类型'),
    formatter: ({ cellValue }) => {
      return returnTypeOptions.find((item) => item.value === cellValue)?.text ?? '-'
    }
  },
  {
    field: 'returnAddress',
    title: i18n.t('退货地址')
  },
  {
    field: 'returnPerson',
    title: i18n.t('退货人')
  },
  {
    field: 'returnPersonPhone',
    title: i18n.t('退货手机号')
  },
  {
    field: 'syncJgStatus',
    title: i18n.t('极光同步状态'),
    minWidth: 140,
    formatter: ({ cellValue }) =>
      syncJgStatusOptions.find((item) => item.value === cellValue)?.text ?? '-'
  },
  {
    field: 'syncJgMsg',
    title: i18n.t('极光同步信息')
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  }
]

export const detailColumns = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'returnCode',
    title: i18n.t('SRM退货单号'),
    minWidth: 180
  },
  {
    field: 'lineNo',
    title: i18n.t('退货单行号'),
    minWidth: 120
  },
  {
    field: 'returnStatus',
    title: i18n.t('退货状态'),
    formatter: ({ cellValue }) =>
      statusOptions.find((item) => item.value === cellValue)?.text ?? '-'
  },
  {
    field: 'stockInCode',
    title: i18n.t('入库单号'),
    minWidth: 180
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂编码')
  },
  {
    field: 'siteName',
    title: i18n.t('工厂名称')
  },
  {
    field: 'warehouseCode',
    title: i18n.t('入库仓库'),
    minWidth: 120,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.warehouseName : ''
    }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码'),
    minWidth: 120
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    minWidth: 120
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码'),
    minWidth: 120
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称'),
    minWidth: 120
  },
  {
    field: 'baseUnitCode',
    title: i18n.t('单位'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.baseUnitName : ''
    }
  },
  {
    field: 'categoryCode',
    title: i18n.t('品类编码'),
    minWidth: 120
  },
  {
    field: 'categoryName',
    title: i18n.t('品类名称'),
    minWidth: 120
  },
  {
    field: 'receivedQuantity',
    title: i18n.t('签收数量')
  },
  {
    field: 'returnQuantity',
    title: i18n.t('退货数量')
  },
  {
    field: 'actualReturnQuantity',
    title: i18n.t('实退数量')
  },
  {
    field: 'itemRemark',
    title: i18n.t('行备注')
  },
  {
    field: 'returnTime',
    title: i18n.t('退货时间'),
    minWidth: 160
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人')
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160
  }
]
