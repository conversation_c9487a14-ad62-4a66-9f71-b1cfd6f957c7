<!-- 物料明细 -->
<template>
  <div>
    <sc-table
      ref="scTableRef"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      keep-source
      :sortable="false"
      :is-show-right-btn="true"
      :is-show-refresh-bth="pageType !== 'create'"
      @refresh="getTableData"
      @edit-closed="editComplete"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'

export default {
  name: 'ItemTab',
  components: { ScTable },
  props: {
    dataInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      editRules: {
        checkCount: [
          { required: true, message: this.$t('请输入实退数量') },
          { type: 'number', message: this.$t('实退数量必须为数字') },
          { validator: this.validateCheckCount, message: this.$t('实退数量不能大于退货数量') }
        ]
      }
    }
  },
  computed: {
    pageType() {
      return this.$route.query?.type || 'confirm'
    },
    editable() {
      return ['create', 'edit'].includes(this.pageType)
    },
    tableRef() {
      return this.$refs.scTableRef?.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'itemCode',
          title: this.$t('物料编码')
        },
        {
          field: 'itemName',
          title: this.$t('物料名称')
        },
        {
          field: 'baseUnitCode',
          title: this.$t('单位'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.baseUnitName : ''
          }
        },
        {
          field: 'categoryCode',
          title: this.$t('品类'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.categoryName : ''
          }
        },
        {
          field: 'receivedQuantity',
          title: this.$t('签收数量')
        },
        {
          field: 'returnQuantity',
          title: this.$t('退货数量'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='integer'
                  v-model={row.returnQuantity}
                  placeholder={this.$t('请输入')}
                  min='1'
                  max={row.receivedQuantity}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'itemRemark',
          title: this.$t('行备注'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.itemRemark}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        }
      ]
    },
    editConfig() {
      return {
        enabled: false,
        trigger: 'click',
        mode: 'cell',
        showStatus: true
      }
    },
    toolbar() {
      let btns = []
      return btns
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    /**
     * 处理工具栏按钮点击
     */
    handleClickToolBar() {},

    /**
     * 处理编辑完成
     */
    editComplete(args) {
      if (args.$event) {
        // 保存数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        this.$emit('updateDetail', currentViewRecords)
      }
    },

    /**
     * 获取表格数据
     */
    async getTableData() {
      try {
        this.loading = true
        let params = {
          ids: [this.$route.query?.id]
        }
        const res = await this.$API.supplierVmiManagement.detailReturnManagementDetailApi(params)
        if (res.code === 200) {
          this.tableData = res.data.map((item) => {
            return {
              stockInItemId: item.id,
              returnQuantity:
                this.pageType === 'create' ? item.receivedQuantity : item.returnQuantity,
              receivedQuantity: item.receivedQuantity,
              itemCode: item.itemCode,
              itemName: item.itemName,
              baseUnitCode: item.baseUnitCode,
              baseUnitName: item.baseUnitName,
              categoryCode: item.categoryCode,
              categoryName: item.categoryName,
              itemRemark: item.itemRemark
            }
          })
          this.$emit('updateDetail', this.tableData)
        } else {
          this.$toast({ content: res.msg || this.$t('获取数据失败'), type: 'warning' })
        }
      } catch (error) {
        console.error('获取表格数据失败:', error)
        this.$toast({ content: this.$t('获取数据失败'), type: 'error' })
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.sc-table {
  :deep(.vxe-table) {
    .vxe-body--row {
      &.row--editing {
        background-color: #f5f7fa;
      }
    }
  }
}
</style>
