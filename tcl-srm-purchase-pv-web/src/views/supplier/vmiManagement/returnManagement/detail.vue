<!-- 确认退货单 -->
<template>
  <div class="full-height">
    <div class="top-container toggle-container" :class="[!isExpand && 'top-hidden']">
      <div class="top-content">
        <div class="top-info">
          <div class="top-title">
            <span>{{ $t('VMI退货单号') }}：{{ formData.returnCode || '-' }}</span>
            <span>{{ $t('创建人') }}：{{ formData.createUserName || '-' }}</span>
            <span>{{ $t('创建时间') }}：{{ formData.createTime || '-' }}</span>
          </div>
          <div class="top-btns">
            <vxe-button
              v-for="(item, index) in detailToolbar"
              v-show="!item.isHidden"
              :key="index"
              :icon="item.icon"
              :status="item.status"
              :disabled="item.disabled"
              size="small"
              @click="handleClickToolBar(item)"
              >{{ item.name }}</vxe-button
            >
          </div>
        </div>
        <div class="top-form">
          <mt-form ref="formRef" :model="formData" :rules="formRules">
            <mt-form-item
              v-for="(item, key) in filteredFormItems"
              :key="key"
              :label="item.label"
              :prop="item.prop"
              label-style="top"
            >
              <component
                :is="item.component"
                v-model="formData[item.prop]"
                v-bind="getComponentProps(item)"
                @change="(e) => handleFieldChange(item.prop, e)"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </div>
      <div class="top-toggle-btn">
        <div @click="handleToggle"><i class="vxe-icon-arrow-up" /></div>
      </div>
    </div>
    <div class="body-container">
      <mt-tabs
        ref="tabsRef"
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabList"
        :halt-select="false"
        @handleSelectTab="(index) => handleTabChange(index)"
        style="background-color: #fff"
      />
      <keep-alive :include="keepArr">
        <component
          ref="mainContent"
          style="height: calc(100% - 50px)"
          :is="activeComponent"
          :data-info="formData"
          :table-data="tableData"
          @updateDetail="updateDetail"
        />
      </keep-alive>
    </div>
  </div>
</template>

<script>
import { returnTypeOptions, warehouseTypeOptions } from './config'

export default {
  name: 'ReturnManagementDetail',
  data() {
    return {
      isExpand: true,
      formData: {},
      activeTabIndex: 0,
      keepArr: ['ItemTab', 'SnTab'],
      tableData: [],
      itemList: [],
      snList: [],
      loading: false,
      returnTypeOptions,
      warehouseTypeOptions
    }
  },
  computed: {
    pageType() {
      return this.$route.query?.type || 'view'
    },
    editable() {
      return this.pageType === 'confirm'
    },
    detailToolbar() {
      return [
        {
          code: 'back',
          name: this.$t('返回')
        },
        {
          code: 'confirm',
          name: this.$t('确认退货'),
          status: 'primary',
          isHidden: !this.editable
        },
        {
          code: 'reject',
          name: this.$t('驳回'),
          status: 'primary',
          isHidden: !this.editable
        }
      ]
    },
    formRules() {
      return {}
    },
    filteredFormItems() {
      return this.formItems.filter((item) => !item.show || item.show(this.formData))
    },
    formItems() {
      return [
        {
          label: this.$t('退货类型'),
          prop: 'returnType',
          component: 'vxe-select',
          options: this.returnTypeOptions,
          props: {
            disabled: true
          }
        },
        {
          label: this.$t('工厂编码'),
          prop: 'siteCode',
          component: 'vxe-input',
          props: {
            disabled: true
          }
        },
        {
          label: this.$t('工厂名称'),
          prop: 'siteName',
          component: 'vxe-input',
          props: {
            disabled: true
          }
        },
        {
          label: this.$t('入库单号'),
          prop: 'stockInCode',
          component: 'vxe-input',
          props: {
            disabled: true
          }
        },
        {
          label: this.$t('VMI仓类型'),
          prop: 'warehouseType',
          component: 'vxe-select',
          options: this.warehouseTypeOptions,
          props: {
            disabled: true
          }
        },
        {
          label: this.$t('VMI仓编码'),
          prop: 'warehouseCode',
          component: 'vxe-input',
          props: {
            disabled: true
          }
        },
        {
          label: this.$t('VMI仓名称'),
          prop: 'warehouseName',
          component: 'vxe-input',
          props: {
            disabled: true
          }
        },
        {
          label: this.$t('供应商编码'),
          prop: 'supplierCode',
          component: 'vxe-input',
          props: {
            disabled: true
          }
        },
        {
          label: this.$t('供应商名称'),
          prop: 'supplierName',
          component: 'vxe-input',
          props: {
            disabled: true
          }
        },
        {
          label: this.$t('退货地址'),
          prop: 'returnAddress',
          component: 'vxe-input',
          props: {
            disabled: true
          }
        },
        {
          label: this.$t('退货人'),
          prop: 'returnPerson',
          component: 'vxe-input',
          props: {
            disabled: true
          }
        },
        {
          label: this.$t('退货人手机号'),
          prop: 'returnPersonPhone',
          component: 'vxe-input',
          props: {
            disabled: true
          }
        }
      ]
    },
    tabList() {
      return [
        { title: this.$t('物料明细'), compName: 'ItemTab' },
        { title: this.$t('SN码'), compName: 'snTab' }
      ]
    },
    activeComponent() {
      let comp = ''
      switch (this.activeTabIndex) {
        case 0:
          comp = () => import('./components/itemTab.vue')
          break
        case 1:
          comp = () => import('./components/snTab.vue')
          break
        default:
          break
      }
      return comp
    }
  },
  methods: {
    /**
     * 初始化页面数据
     */
    initPage() {
      let selectedRecords = sessionStorage.getItem('inventorySelectedRecords')
        ? JSON.parse(sessionStorage.getItem('inventorySelectedRecords'))
        : []
      if (selectedRecords?.length !== 0) {
        const record = selectedRecords[0]
        this.$set(this.formData, 'siteCode', record.siteCode)
        this.$set(this.formData, 'siteName', record.siteName)
        this.$set(this.formData, 'stockInId', record.id)
        this.$set(this.formData, 'stockInCode', record.stockInCode)
        this.$set(this.formData, 'warehouseType', record.warehouseType || 'VMI')
        this.$set(this.formData, 'warehouseCode', record.warehouseCode)
        this.$set(this.formData, 'warehouseName', record.warehouseName)
        this.$set(this.formData, 'supplierTenantId', record.supplierTenantId)
        this.$set(this.formData, 'supplierCode', record.supplierCode)
        this.$set(this.formData, 'supplierName', record.supplierName)
        this.$set(this.formData, 'returnStatus', record.returnStatus)
        if (this.pageType !== 'create') {
          this.$set(this.formData, 'id', record.id)
          this.$set(this.formData, 'stockInId', record.stockInId)
          this.$set(this.formData, 'returnCode', record.returnCode)
          this.$set(this.formData, 'returnType', record.returnType)
          this.$set(this.formData, 'returnAddress', record.returnAddress)
          this.$set(this.formData, 'returnPerson', record.returnPerson)
          this.$set(this.formData, 'returnPersonPhone', record.returnPersonPhone)
          this.$set(this.formData, 'createUserName', record.createUserName)
          this.$set(this.formData, 'createTime', record.createTime)
          this.$set(this.formData, 'status', record.status)
        }
      }
    },

    /**
     * 处理标签页切换
     */
    handleTabChange(index) {
      this.$refs.tabsRef.activeTab = index
      this.activeTabIndex = index
    },

    /**
     * 获取组件属性
     */
    getComponentProps(item) {
      const baseProps = {
        disabled: !this.editable || item.props?.disabled
      }

      // 合并基础属性和表单项特定属性
      const props = {
        ...baseProps,
        ...item.props
      }

      // 根据组件类型添加特定属性
      switch (item.component) {
        case 'vxe-input':
          props.type = item.type || 'text'
          break
        case 'vxe-select':
          props.options = item.options || []
          break
        default:
          break
      }

      return props
    },

    /**
     * 处理字段变化
     */
    handleFieldChange(field, value) {
      // 触发表单验证
      this.$refs.formRef?.validateField(field)
    },

    /**
     * 更新详情数据
     */
    updateDetail(data) {
      switch (this.activeTabIndex) {
        case 0:
          this.itemList = data
          break
        case 1:
          this.snList = data
          break
        default:
          break
      }
    },

    /**
     * 返回上一页
     */
    handleBack() {
      this.$router.go(-1)
    },

    /**
     * 展开/收缩
     */
    handleToggle() {
      this.isExpand = !this.isExpand
    },

    /**
     * 处理工具栏按钮点击
     */
    async handleClickToolBar(item) {
      const actionMap = {
        back: () => this.handleBack(),
        confirm: () => this.showConfirmDialog('退货', () => this.handleConfirm()),
        reject: () => this.handleReject()
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },

    showConfirmDialog(action, callback) {
      return this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认${action}？`)
        },
        success: callback
      })
    },

    /**
     * 确认退货
     */
    async handleConfirm() {
      let params = {
        ids: [this.formData.id]
      }
      this.$API.supplierVmiManagement.confirmReturnManagementApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('确认成功'), type: 'success' })
          this.handleBack()
        }
      })
    },

    /**
     * 退回退货单
     */
    async handleReject() {
      this.$dialog({
        data: {
          title: this.$t('驳回')
        },
        modal: () => import('./components/RejectDialog.vue'),
        success: async (rejectReason) => {
          let params = {
            ids: [this.formData.id],
            rejectReason
          }
          let api = this.$API.supplierVmiManagement.rejectReturnManagementApi
          const res = await api(params)
          if (res.code === 200) {
            this.$toast({ content: this.$t('驳回成功'), type: 'success' })
            this.handleBack()
          }
        }
      })
    }
  },
  created() {
    this.initPage()
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 8px 8px 0;
  background: #fff;
}
.top-container {
  flex: 1;
  width: 100%;
  .top-content {
    padding: 8px 8px 0 8px;
    background-color: #f9f9f9;
    border-radius: 8px;
    .top-info {
      display: flex;
      justify-content: space-between;
      .title {
        font-size: 20px;
        font-weight: 700;
        color: #292929;
        span {
          margin-right: 15px;
        }
        .sub-title {
          font-size: 15px;
          color: #777;
        }
      }
      .top-title {
        display: flex;
        padding-top: 10px;
        span {
          margin-right: 35px;
        }
      }
      .top-btns {
        text-align: right;
        margin-bottom: 10px;
      }
    }
    .top-form {
      .mt-form {
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
        justify-content: space-between;
        grid-gap: 5px;
      }
      .vxe-input,
      .vxe-select,
      .vxe-pulldown {
        width: 100%;
        height: 34px;
        line-height: 34px;
      }
      /deep/.vxe-pulldown--panel {
        min-width: unset !important;
        width: 100%;
      }
    }
  }
  .top-toggle-btn {
    width: 100%;
    div {
      width: 80px;
      height: 15px;
      line-height: 15px;
      margin: auto;
      text-align: center;
      background-color: #4a556b;
      color: #fff;
      font-weight: bold;
      border-radius: 0 0 6px 6px;
      cursor: pointer;
      &:hover {
        background-color: #5a657b;
      }
    }
  }
}
.top-hidden {
  .top-form {
    display: none;
  }
  .top-toggle-btn {
    div {
      transform: rotate(180deg);
      border-radius: 6px 6px 0 0;
    }
  }
}
.body-container {
  height: calc(100% - 80px);
}
::v-deep {
  .mt-form-item {
    margin-bottom: 15px;
    .label {
      margin: 0 10px 5px 0;
    }
  }
  .mt-tabs-container {
    background-color: #fff;
  }

  .mt-tabs-container ul.tab-container li.tab-item2.active {
    border-bottom: 2px solid #409eff;
  }
  .mt-tabs-container ul.tab-container li.tab-item2.active,
  .mt-tabs-container ul.tab-container li.tab-item2:hover {
    color: #31374e;
  }

  .vxe-textarea--inner {
    min-height: 34px;
    height: 100%;
  }
}
</style>
