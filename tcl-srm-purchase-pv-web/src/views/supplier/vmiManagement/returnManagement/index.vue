<!-- 供方-VMI管理-退货管理 -->
<template>
  <div class="supplier-vmi-return-management">
    <mt-tabs
      class="toggle-tab"
      :e-tab="false"
      :data-source="tabList"
      :selected-item="selectedItem"
      @handleSelectTab="handleSelectTab"
    />
    <div class="toggle-content">
      <List v-show="tabIndex === 0" ref="listRef" />
      <Detail v-show="tabIndex === 1" ref="detailRef" />
    </div>
  </div>
</template>

<script>
import List from './pages/List.vue'
import Detail from './pages/Detail.vue'

export default {
  name: 'SupplierVmiReturnManagement',
  components: {
    List,
    Detail
  },
  data() {
    return {
      tabList: [{ title: this.$t('列表') }, { title: this.$t('明细') }],
      selectedItem: 0,
      tabIndex: 0
    }
  },
  methods: {
    handleSelectTab(index) {
      this.tabIndex = index
    }
  }
}
</script>

<style lang="scss" scoped>
.supplier-vmi-return-management {
  .toggle-content {
    min-height: 400px;
  }
}
</style>
