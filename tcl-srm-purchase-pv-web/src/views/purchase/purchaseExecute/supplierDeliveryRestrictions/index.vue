<template>
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.status"
            css-class="rule-element"
            :data-source="statusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="siteCode" :label="$t('工厂编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.siteCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="siteName" :label="$t('工厂名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.siteName"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.supplierCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.supplierName"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.itemCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.itemName"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="warehouseCode" :label="$t('入库仓库编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.warehouseCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="warehouseName" :label="$t('入库仓库名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.warehouseName"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="remarks" :label="$t('备注')" label-style="top">
          <mt-input
            v-model="searchFormModel.remarks"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :allow-edit="false"
            :placeholder="$t('请选择')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'createTime')"
          />
        </mt-form-item>
        <mt-form-item prop="updateTime" :label="$t('最后更新时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.updateTime"
            :allow-edit="false"
            :placeholder="$t('请选择')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'updateTime')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="scTableRef"
      grid-id="3cba0768-2d85-1f06-2c01-01fc6c7ad24d"
      :loading="loading"
      :edit-config="editConfig"
      :edit-rules="editRules"
      keep-source
      :sortable="true"
      :is-show-refresh-bth="true"
      :columns="listColumns"
      :table-data="tableData"
      @refresh="handleSearch"
      @edit-closed="editComplete"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import VxeRemoteSearch from '@/components/VxeRemoteSearch/index.vue'
import { listToolbar, statusList } from './config/index'
import XEUtils from 'xe-utils'
import dayjs from 'dayjs'

export default {
  components: {
    ScTable,
    CollapseSearch,
    VxeRemoteSearch
  },
  mixins: [mixin],
  data() {
    return {
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 20,
        pageSizes: [20, 50, 200, 500, 1000, 2000]
      },
      pageInfo: {
        size: 20,
        current: 1
      },
      editRules: {
        siteCode: [{ required: true, message: this.$t('必填') }],
        supplierCode: [{ required: true, message: this.$t('必填') }],
        warehouseCode: [{ required: true, message: this.$t('必填') }]
      },
      statusList,
      toolbar: [],
      searchFormModel: {},
      tableData: [],
      loading: false,
      type: 'list',
      uploadFileList: [], // 上传的附件
      factoryList: [], // 上传的附件
      isEdit: false
    }
  },
  computed: {
    editConfig() {
      return {
        enabled: true,
        trigger: 'dblclick',
        mode: 'row',
        showStatus: true
      }
    },
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    },
    // 列配置
    listColumns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        // {
        //   width: 70,
        //   field: 'index',
        //   title: this.$t('序号')
        // },
        {
          field: 'status',
          title: this.$t('状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = statusList.find((item) => item.value === row.status)
              const name = selectItem?.text
              return [<div>{name}</div>]
            }
          }
        },
        {
          field: 'siteCode',
          title: this.$t('工厂'),
          editRender: {},
          minWidth: 180,
          slots: {
            default: ({ row }) => {
              return [row.siteCode ? <div>{row.siteCode + '-' + row.siteName}</div> : null]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.siteCode}
                  options={this.factoryList}
                  placeholder={this.$t('请选择')}
                  clearable
                  filterable
                  transfer
                  onChange={() => {
                    let selectedItem = this.factoryList.find((v) => v.value === row.siteCode)
                    row.siteName = selectedItem?.siteName
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商'),
          editRender: {},
          minWidth: 180,
          slots: {
            default: ({ row }) => {
              return [
                row.supplierCode ? <div> {row.supplierCode + '-' + row.supplierName}</div> : null
              ]
            },
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.supplierCode}
                  placeholder={this.$t('请选择')}
                  fields={{ text: 'supplierName', value: 'supplierCode' }}
                  request-info={{
                    urlPre: 'masterData',
                    url: 'supplierDistinctPagedQueryNoScope',
                    searchFields: ['supplierCode', 'supplierName'],
                    params: {
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  onChange={(e) => {
                    row.supplierName = e?.supplierName || null
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'itemCode',
          title: this.$t('物料'),
          minWidth: 180,
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [row.itemCode ? <div> {row.itemCode + '-' + row.itemName}</div> : null]
            },
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.itemCode}
                  fields={{ text: 'itemName', value: 'itemCode' }}
                  placeholder={this.$t('请选择')}
                  request-info={{
                    urlPre: 'masterData',
                    url: 'getItemPage',
                    searchFields: ['itemCode', 'itemName'],
                    params: {
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  onChange={(e) => {
                    row.itemName = e?.itemName || null
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'warehouseCode',
          title: this.$t('入库仓库'),
          editRender: {},
          minWidth: 180,
          slots: {
            default: ({ row }) => {
              return [
                row.warehouseCode ? <div> {row.warehouseCode + '-' + row.warehouseName}</div> : null
              ]
            },
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.warehouseCode}
                  disabled={
                    this.editableType === 2 ||
                    this.editableType === 3 ||
                    this.isSup ||
                    row.deliveryCompletedFlag === 1 ||
                    row.closeStatus === 1
                  }
                  placeholder={this.$t('请选择')}
                  fields={{ text: 'warehouseName', value: 'warehouseCode' }}
                  request-info={{
                    urlPre: 'purchaseOrderMgt',
                    url: 'queryPvWarehouseList',
                    searchKey: 'fuzzyParam',
                    params: {
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  onChange={(e) => {
                    row.warehouseName = e?.warehouseName || null
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'remarks',
          title: this.$t('备注'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.remarks}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          minWidth: 120
        },
        {
          field: 'updateUserName',
          title: this.$t('最后修改人'),
          minWidth: 130
        },
        {
          field: 'updateTime',
          title: this.$t('最后修改时间'),
          minWidth: 140
        }
      ]
    }
  },
  mounted() {
    if (!this.$route.path.includes('-sup')) {
      this.toolbar = listToolbar
    } else {
      this.toolbar = [{ code: 'export', name: this.$t('导出'), status: 'info' }]
    }
    this.getFactoryList()
    this.handleSearch()
  },
  activated() {
    this.handleSearch()
  },
  methods: {
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        if (args.$event.srcElement.innerText === this.$t('取消编辑')) {
          this.handleSearch()
          return
        }
        // 远程数据才有$event属性
        //1、 校验必填
        this.tableRef.validate([row]).then((valid) => {
          if (valid) {
            this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
            return
          }
          const params = {
            ...row,
            id: row.id.includes('row_') ? null : row.id,
            itemCode: row.itemCode || '',
            itemName: row.itemName || ''
          }
          this.$API.regionalDeliveryPlanMgt.saveSupplierPlanLimitList(params).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t(`保存成功！`), type: 'success' })
              this.handleSearch()
            }
          })
        })
      }
    },
    getFactoryList() {
      this.$API.masterData.getSiteListByPermission().then((res) => {
        this.factoryList = res.data.map((i) => {
          return {
            ...i,
            label: `${i.siteCode}-${i.siteName}`,
            value: i.siteCode
          }
        })
      })
    },
    // 日期格式化
    handleDateChange(e, field) {
      const startField = field + 'Start'
      const endField = field + 'End'
      if (e.startDate) {
        const startDate = XEUtils.toDateString(e.startDate, 'yyyy-MM-dd') + ' 00:00:00'
        const endDate = XEUtils.toDateString(e.endDate, 'yyyy-MM-dd') + ' 23:59:59'
        this.searchFormModel[startField] = XEUtils.timestamp(startDate)
        this.searchFormModel[endField] = XEUtils.timestamp(endDate)
      } else {
        this.searchFormModel[startField] = null
        this.searchFormModel[endField] = null
      }
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    // 查询
    async handleSearch(type) {
      // 解决修改查询条件后，查询结果有数据但未正常显示的问题（type: pageChange/refresh）
      if (!type) {
        this.pageInfo.current = 1
        this.$refs.pageRef.jumpNum = 1
        this.$refs.pageRef.currentPage = 1
      }
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      delete params.submitTime
      this.loading = true
      const res = await this.$API.regionalDeliveryPlanMgt
        .querySupplierPlanLimitList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.tableData?.forEach((item, index) => {
          item.index = index + 1
        }) || []
        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
        await this.tableRef?.clearEdit()
        this.isEdit = false
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (!['add', 'import', 'cancel'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      const ids = []
      for (let index = 0; index < selectedRecords.length; index++) {
        const item = selectedRecords[index]
        if (e.code === 'deleteSupplierPlanLimitList' && ![0].includes(item.status)) {
          this.$toast({ content: this.$t('仅支持删除失效状态的数据！'), type: 'warning' })
          return
        }
        if (e.code === 'enableSupplierPlanLimitList' && ![0].includes(item.status)) {
          this.$toast({ content: this.$t('仅支持生效失效状态的数据！'), type: 'warning' })
          return
        }
        if (e.code === 'disableSupplierPlanLimitList' && ![1].includes(item.status)) {
          this.$toast({ content: this.$t('仅支持失效生效状态的数据！'), type: 'warning' })
          return
        }
        ids.push(item.id)
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'cancel':
          // this.handleSearch()
          break
        // case 'edit':
        //   this.handleAdd('edit', selectedRecords)
        //   break
        case 'deleteSupplierPlanLimitList': // 删除
          this.handleOperate(ids, e.code)
          break
        case 'enableSupplierPlanLimitList': // 生效
          this.handleOperate(ids, e.code)
          break
        case 'disableSupplierPlanLimitList': // 失效
          this.handleOperate(ids, e.code)
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          this.handleExport()
          break
        default:
          break
      }
    },
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.regionalDeliveryPlanMgt.importSupplierPlanLimitList,
          downloadTemplateApi: this.$API.regionalDeliveryPlanMgt.getSupplierPlanLimitImportTemplate,
          paramsKey: 'excel'
          // asyncParams: { id: this.$route.query.id }
        },
        success: () => {
          this.handleSearch()
        }
      })
    },
    handleExport() {
      const params = { page: { size: 9999, current: 1 }, ...this.searchFormModel } // 筛选条件
      this.$API.regionalDeliveryPlanMgt.exportSupplierPlanLimitList(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 新增
    handleAdd(item = {}) {
      if (this.isEdit) {
        return false
      }
      this.tableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.tableRef.setEditRow(currentViewRecords[0])
        this.isEdit = true
      })
    },
    // 删除、提交
    handleOperate(ids, type, extraParams = {}) {
      const tipMap = {
        deleteSupplierPlanLimitList: '删除',
        enableSupplierPlanLimitList: '生效',
        disableSupplierPlanLimitList: '失效'
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认${tipMap[type]}选中的数据？`)
        },
        success: async () => {
          const res = await this.$API.regionalDeliveryPlanMgt[type]({ ids, ...extraParams })
          if (res.code === 200) {
            this.$toast({ content: this.$t(`${tipMap[type]}成功！`), type: 'success' })
            this.handleSearch()
          }
        }
      })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.handleSearch('pageChange')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.handleSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
