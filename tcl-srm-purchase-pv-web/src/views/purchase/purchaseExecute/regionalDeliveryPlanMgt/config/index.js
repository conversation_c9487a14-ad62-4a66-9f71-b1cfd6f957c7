import { i18n } from '@/main'

// 状态
export const statusList = [
  { value: 0, text: i18n.t('新建') },
  { value: 1, text: i18n.t('已修改') },
  { value: 2, text: i18n.t('已发布') },
  { value: 3, text: i18n.t('反馈满足') },
  { value: 4, text: i18n.t('反馈不满足') },
  { value: 5, text: i18n.t('已关闭') },
  { value: 6, text: i18n.t('已完成') }
]

// 业务类型
export const businessTypeList = [
  { value: 1, text: i18n.t('户用业务') },
  { value: 2, text: i18n.t('商用业务') },
  { value: 3, text: i18n.t('海外业务') }
]

// 数据来源
export const dataSourceList = [
  { value: 1, text: i18n.t('手工新增') },
  { value: 0, text: i18n.t('仓库需求') }
]

// 订单状态
export const orderStatusList = [
  { value: 0, text: i18n.t('草稿') },
  { value: 1, text: i18n.t('待供应商确认') },
  { value: 2, text: i18n.t('供应商已拒绝') },
  { value: 3, text: i18n.t('待采购确认') },
  { value: 4, text: i18n.t('采购已拒绝') },
  { value: 5, text: i18n.t('双方已确认') },
  { value: 6, text: i18n.t('已完成') },
  { value: 7, text: i18n.t('已关闭') }
]

// 审批状态
export const approvalStatusList = [
  { value: 0, text: i18n.t('未审批') },
  { value: 1, text: i18n.t('审批中') },
  { value: 2, text: i18n.t('审批通过') },
  { value: 3, text: i18n.t('审批拒绝') },
  { value: 4, text: i18n.t('无需审批') }
]

// 分配状态
export const allocationStatusList = [
  { value: 0, text: i18n.t('未分配') },
  { value: 1, text: i18n.t('已分配') }
]

// 分配状态
export const allocationStateList = [
  { value: 0, text: i18n.t('分配成功') },
  { value: 1, text: i18n.t('分配失败') }
]
// 是否
export const yesOrNoList = [
  { value: 0, text: i18n.t('否') },
  { value: 1, text: i18n.t('是') }
]

// 同步极光状态
export const syncJgStatusOptions = [
  { text: i18n.t('未同步'), value: 0 },
  { text: i18n.t('同步中'), value: 1 },
  { text: i18n.t('同步成功'), value: 2 },
  { text: i18n.t('同步失败'), value: 3 }
]

// 列表试图-操作按钮
export const listToolbar = [
  { code: 'add', name: i18n.t('新增'), status: 'info', permission: ['O_02_1829'] },
  { code: 'cancel', name: i18n.t('取消编辑'), status: 'info', permission: ['O_02_1830'] },
  { code: 'deleteList', name: i18n.t('删除'), status: 'info', permission: ['O_02_1831'] },
  { code: 'import', name: i18n.t('导入'), status: 'info', permission: ['O_02_1832'] },
  { code: 'export', name: i18n.t('导出'), status: 'info', permission: ['O_02_1833'] },
  { code: 'autoAssign', name: i18n.t('自动分配供应商'), status: 'info', permission: ['O_02_1834'] }
]

// 明细视图-操作按钮
export const detailToolbar = [
  { code: 'add', name: i18n.t('新增'), status: 'info', loading: false, permission: ['O_02_1835'] },
  {
    code: 'cancel',
    name: i18n.t('取消编辑'),
    status: 'info',
    loading: false,
    permission: ['O_02_1836']
  },
  {
    code: 'delete',
    name: i18n.t('删除'),
    status: 'info',
    loading: false,
    permission: ['O_02_1837']
  },
  {
    code: 'publish',
    name: i18n.t('发布'),
    status: 'info',
    loading: false,
    permission: ['O_02_1840']
  },
  {
    code: 'close',
    name: i18n.t('关闭'),
    status: 'info',
    loading: false,
    permission: ['O_02_1841']
  },
  {
    code: 'import',
    name: i18n.t('导入'),
    status: 'info',
    loading: false,
    permission: ['O_02_1838']
  },
  {
    code: 'export',
    name: i18n.t('导出'),
    status: 'info',
    loading: false,
    permission: ['O_02_1839']
  },
  {
    code: 'sync',
    name: i18n.t('同步极光'),
    status: 'info',
    loading: false,
    permission: ['O_02_1851']
  }
]
