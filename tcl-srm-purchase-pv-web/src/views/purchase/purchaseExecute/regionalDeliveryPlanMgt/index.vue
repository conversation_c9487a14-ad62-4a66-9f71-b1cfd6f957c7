<template>
  <div class="full-height vertical-flex-box">
    <div class="body-container">
      <mt-tabs
        ref="mtTabsRef"
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabList"
        :halt-select="false"
        @handleSelectTab="(index, item) => handleTabChange(index)"
      />
      <keep-alive>
        <component ref="mainContent" style="height: calc(100% - 50px)" :is="activeComponent" />
      </keep-alive>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      tabList: [
        { title: this.$t('仓库需求上载'), compName: 'list' },
        { title: this.$t('交货计划管理'), compName: 'detail' }
      ],
      activeTabIndex: 0
    }
  },
  computed: {
    activeComponent() {
      let comp = ''
      switch (this.activeTabIndex) {
        case 0:
          // 仓库需求上载
          comp = () => import('./pages/list.vue')
          break
        case 1:
          // 交货计划管理
          comp = () => import('./pages/detail.vue')
          break

        default:
          return
      }
      return comp
    }
  },
  methods: {
    // 切换tab页签
    handleTabChange(index) {
      this.$refs.mtTabsRef.activeTab = index
      this.activeTabIndex = index
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
::v-deep .body-container {
  .mt-tabs-container {
    background-color: #fff;
    margin-left: 8px;
  }

  .mt-tabs-container ul.tab-container li.tab-item2.active {
    border-bottom: 2px solid #409eff;
  }
  .mt-tabs-container ul.tab-container li.tab-item2.active,
  .mt-tabs-container ul.tab-container li.tab-item2:hover {
    color: #31374e;
  }
}
</style>
