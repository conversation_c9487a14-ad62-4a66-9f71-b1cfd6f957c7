<template>
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="areaGoodsPlanCode" :label="$t('仓库需求编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.areaGoodsPlanCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="allocationStatus" :label="$t('分配状态')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.allocationStatus"
            css-class="rule-element"
            :data-source="allocationStatusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="siteCode" :label="$t('工厂')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.siteCode"
            :url="$API.masterData.getSiteListUrl"
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          />
        </mt-form-item>
        <mt-form-item prop="warehouseCode" :label="$t('入库仓库')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.warehouseCode"
            url="/srm-purchase-pv/tenant/pv/warehouse/page/query"
            :placeholder="$t('请选择')"
            :fields="{ text: 'warehouseName', value: 'warehouseCode' }"
            params-key="fuzzyParam"
          />
        </mt-form-item>
        <mt-form-item prop="categoryCode" :label="$t('品类编码')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.categoryCode"
            url="/masterDataManagement/tenant/category/paged-query"
            :placeholder="$t('请选择')"
            :fields="{ text: 'categoryName', value: 'categoryCode' }"
            :search-fields="['categoryName', 'categoryCode']"
          />
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.itemCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.itemName"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="demandDate" :label="$t('需求日期')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.demandDate"
            :allow-edit="false"
            :placeholder="$t('请选择')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'demandDate')"
          />
        </mt-form-item>

        <mt-form-item prop="remark" :label="$t('备注')" label-style="top">
          <mt-input
            v-model="searchFormModel.remark"
            :show-clear-button="true"
            :placeholder="$t('请输入采方备注')"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :allow-edit="false"
            :placeholder="$t('请选择')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'createTime')"
          />
        </mt-form-item>
        <mt-form-item prop="updateTime" :label="$t('最后修改时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.updateTime"
            :allow-edit="false"
            :placeholder="$t('请选择')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'updateTime')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="scTableRef"
      grid-id="b3ecbe4e-d0d1-40f1-a690-860c05864434"
      :loading="loading"
      :edit-config="editConfig"
      :edit-rules="editRules"
      keep-source
      :sortable="true"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      @refresh="handleSearch"
      @edit-closed="editComplete"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          v-permission="item.permission"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './../config/mixin'
import RemoteAutocomplete from '@/components/RemoteAutocomplete/index.vue'
import VxeRemoteSearch from '@/components/VxeRemoteSearch/index.vue'
import { listToolbar, allocationStatusList } from './../config/index'
import XEUtils from 'xe-utils'
import dayjs from 'dayjs'

// 常量定义
const DEFAULT_PAGE_SIZE = 20
const PAGE_SIZES = [20, 50, 200, 500, 1000, 2000]

// 操作类型映射
const OPERATION_MAP = {
  deleteList: {
    title: '删除',
    message: '确认删除选中的数据？',
    api: 'deleteList'
  },
  autoAssign: {
    title: '自动分配',
    message: '确认自动分配供应商？',
    api: 'autoAssign'
  }
}

export default {
  components: {
    ScTable,
    CollapseSearch,
    RemoteAutocomplete,
    VxeRemoteSearch
  },
  mixins: [mixin],
  data() {
    return {
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: DEFAULT_PAGE_SIZE,
        pageSizes: PAGE_SIZES
      },
      pageInfo: {
        size: DEFAULT_PAGE_SIZE,
        current: 1
      },
      editRules: {
        siteCode: [{ required: true, message: this.$t('必填') }],
        categoryCode: [{ required: true, message: this.$t('必填') }],
        warehouseCode: [{ required: true, message: this.$t('必填') }],
        requiredNum: [{ required: true, message: this.$t('必填') }],
        requiredDate: [{ required: true, message: this.$t('必填') }]
      },
      allocationStatusList,
      toolbar: [],
      searchFormModel: {},
      tableData: [],
      loading: false,
      type: 'list',
      uploadFileList: [],
      factoryList: [],
      isEdit: false
    }
  },
  computed: {
    editConfig() {
      return {
        enabled:
          window.elementPermissionSet.includes('O_02_1829') ||
          window.elementPermissionSet.includes('O_02_1830'),
        trigger: 'dblclick',
        mode: 'row',
        showStatus: true
      }
    },
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'areaGoodsPlanCode',
          title: this.$t('仓库需求编码'),
          minWidth: 180
        },
        {
          field: 'allocationStatus',
          title: this.$t('分配状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = allocationStatusList.find(
                (item) => item.value === row.allocationStatus
              )
              return [<div>{selectItem?.text}</div>]
            }
          }
        },
        {
          field: 'siteCode',
          title: this.$t('工厂'),
          minWidth: 170,
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [row.siteCode ? <div>{row.siteCode + '-' + row.siteName}</div> : null]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.siteCode}
                  options={this.factoryList}
                  placeholder={this.$t('请选择')}
                  clearable
                  filterable
                  transfer
                  onChange={() => {
                    const selectedItem = this.factoryList.find((v) => v.value === row.siteCode)
                    row.siteName = selectedItem?.siteName
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'warehouseCode',
          title: this.$t('入库仓库'),
          minWidth: 180,
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [
                row.warehouseCode ? <div>{row.warehouseCode + '-' + row.warehouseName}</div> : null
              ]
            },
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.warehouseCode}
                  disabled={
                    this.editableType === 2 ||
                    this.editableType === 3 ||
                    this.isSup ||
                    row.deliveryCompletedFlag === 1 ||
                    row.closeStatus === 1
                  }
                  placeholder={this.$t('请选择')}
                  fields={{ text: 'warehouseName', value: 'warehouseCode' }}
                  request-info={{
                    urlPre: 'purchaseOrderMgt',
                    url: 'queryPvWarehouseList',
                    searchKey: 'fuzzyParam',
                    params: {
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  onChange={(e) => {
                    row.warehouseName = e?.warehouseName || null
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'categoryCode',
          title: this.$t('品类'),
          editRender: {},
          minWidth: 180,
          slots: {
            default: ({ row }) => {
              return [
                row.categoryCode ? <div>{row.categoryCode + '-' + row.categoryName}</div> : null
              ]
            },
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.categoryCode}
                  placeholder={this.$t('请选择')}
                  fields={{ text: 'categoryName', value: 'categoryCode' }}
                  request-info={{
                    urlPre: 'masterData',
                    url: 'pageCategoryApi',
                    searchFields: ['categoryCode', 'categoryName'],
                    params: {
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  onChange={(e) => {
                    row.categoryName = e?.categoryName || null
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'itemCode',
          title: this.$t('物料编码'),
          editRender: {},
          minWidth: 180,
          slots: {
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.itemCode}
                  fields={{ text: 'itemName', value: 'itemCode' }}
                  placeholder={this.$t('请选择')}
                  request-info={{
                    urlPre: 'masterData',
                    url: 'getItemPage',
                    searchFields: ['itemCode', 'itemName'],
                    params: {
                      defaultRules: [
                        {
                          field: 'organizationCode',
                          operator: 'equal',
                          value: row.siteCode
                        }
                      ],
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  link-field-value={row.siteCode}
                  disabled={row.siteCode ? false : true}
                  onChange={(e) => {
                    row.itemName = e?.itemName || null
                    row.categoryCode = e?.categoryCode || null
                    row.categoryName = e?.categoryName || null
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'itemName',
          title: this.$t('物料名称'),
          minWidth: 180
        },
        {
          field: 'power',
          title: this.$t('功率(KW)'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.power}
                  placeholder={this.$t('请输入')}
                  min='0'
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'quantity',
          title: this.$t('需求数量'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.quantity}
                  placeholder={this.$t('请输入')}
                  min='0'
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'demandDate',
          title: this.$t('需求日期'),
          minWidth: 140,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              const dateDisabled = (params) => {
                const { date } = params
                return date < new Date()
              }
              return [
                <vxe-input
                  type='date'
                  v-model={row.demandDate}
                  placeholder={this.$t('请选择')}
                  transfer
                  clearable
                  label-format='yyyy-MM-dd'
                  value-format='yyyy-MM-dd'
                  disabled-method={dateDisabled}
                />
              ]
            }
          }
        },
        {
          field: 'remark',
          title: this.$t('备注'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.remark}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          minWidth: 120
        },
        {
          field: 'updateUserName',
          title: this.$t('最后修改人'),
          minWidth: 130
        },
        {
          field: 'updateTime',
          title: this.$t('最后修改时间'),
          minWidth: 140
        }
      ]
    }
  },
  created() {
    this.initializeToolbar()
  },
  mounted() {
    this.getFactoryList()
    this.handleSearch()
  },
  activated() {
    this.handleSearch()
  },
  methods: {
    initializeToolbar() {
      this.toolbar = this.$route.path?.includes('-sup')
        ? [{ code: 'export', name: this.$t('导出'), status: 'info' }]
        : listToolbar
    },
    async editComplete(args) {
      const { row } = args
      if (!args.$event) return

      if (args.$event.srcElement.innerText === this.$t('取消编辑')) {
        await this.handleSearch()
        return
      }

      try {
        const valid = await this.tableRef.validate([row])
        if (valid) {
          this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
          return
        }

        if (!row.itemCode && !row.power) {
          this.$toast({ content: this.$t('物料编码和功率必须填一个'), type: 'warning' })
          return
        }

        const params = {
          ...row,
          id: row.id.includes('row_') ? null : row.id,
          demandDate: row.demandDate ? dayjs(row.demandDate).valueOf() : null
        }

        const res = await this.$API.regionalDeliveryPlanMgt.saveList(params)
        if (res.code === 200) {
          this.$toast({ content: this.$t('保存成功！'), type: 'success' })
          await this.handleSearch()
        } else {
          throw new Error(res.message || this.$t('保存失败'))
        }
      } catch (error) {
        console.error('保存失败:', error)
      }
    },
    async getFactoryList() {
      try {
        const res = await this.$API.masterData.getSiteListByPermission()
        this.factoryList = res.data.map((i) => ({
          ...i,
          label: `${i.siteCode}-${i.siteName}`,
          value: i.siteCode
        }))
      } catch (error) {
        console.error('获取工厂列表失败:', error)
      }
    },
    handleDateChange(e, field) {
      const startField = field + 'Start'
      const endField = field + 'End'

      if (e.startDate) {
        const startDate = XEUtils.toDateString(e.startDate, 'yyyy-MM-dd') + ' 00:00:00'
        const endDate = XEUtils.toDateString(e.endDate, 'yyyy-MM-dd') + ' 23:59:59'
        this.searchFormModel[startField] = XEUtils.timestamp(startDate)
        this.searchFormModel[endField] = XEUtils.timestamp(endDate)
      } else {
        this.searchFormModel[startField] = null
        this.searchFormModel[endField] = null
      }
    },
    handleReset() {
      this.searchFormModel = {}
      this.handleSearch()
    },
    async handleSearch(type) {
      if (!type) {
        this.resetPagination()
      }

      try {
        this.loading = true
        const params = {
          page: this.pageInfo,
          ...this.searchFormModel
        }
        delete params.submitTime

        const res = await this.$API.regionalDeliveryPlanMgt.queryList(params)
        if (res.code === 200) {
          await this.updateTableData(res.data)
        } else {
          throw new Error(res.msg || this.$t('查询失败'))
        }
      } catch (error) {
        console.error('查询失败:', error)
      } finally {
        this.loading = false
      }
    },
    resetPagination() {
      this.pageInfo.current = 1
      this.$refs.pageRef.jumpNum = 1
      this.$refs.pageRef.currentPage = 1
    },
    async updateTableData(data) {
      this.tableData = (data?.records || []).map((item, index) => ({
        ...item,
        index: index + 1
      }))
      this.pageSettings.totalPages = Math.ceil(Number(data.total) / this.pageSettings.pageSize)
      this.pageSettings.totalRecordsCount = Number(data?.total)

      await this.tableRef?.clearEdit()
      this.isEdit = false
    },
    async handleClickToolBar(item) {
      const selectedRecords = this.tableRef.getCheckboxRecords()

      if (
        selectedRecords.length === 0 &&
        ['deleteList', 'assign', 'autoAssign'].includes(item.code)
      ) {
        this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
        return
      }

      const actionMap = {
        add: this.handleAdd,
        cancel: this.handleSearch,
        deleteList: () => this.handleOperate(selectedRecords, 'deleteList'),
        autoAssign: () => this.handleOperate(selectedRecords, 'autoAssign'),
        import: this.handleImport,
        export: this.handleExport
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },
    handleAdd(item = {}) {
      if (this.isEdit) {
        this.$toast({ content: this.$t('请先完成当前编辑'), type: 'warning' })
        return false
      }

      this.tableRef.insert([item])
      this.$nextTick(() => {
        const currentViewRecords = this.tableRef.getTableData().visibleData
        this.tableRef.setEditRow(currentViewRecords[0])
        this.isEdit = true
      })
    },
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.regionalDeliveryPlanMgt.importList,
          downloadTemplateApi: this.$API.regionalDeliveryPlanMgt.getImportTemplate,
          paramsKey: 'excel'
        },
        success: () => {
          this.handleSearch()
        }
      })
    },
    async handleExport() {
      try {
        const params = {
          page: { size: 9999, current: 1 },
          ...this.searchFormModel
        }
        const res = await this.$API.regionalDeliveryPlanMgt.exportList(params)
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      } catch (error) {
        console.error('导出失败:', error)
      }
    },
    handleOperate(selectedRecords, type, extraParams = {}) {
      const operation = OPERATION_MAP[type]
      if (!operation) {
        this.$toast({ content: this.$t('不支持的操作类型'), type: 'error' })
        return
      }

      this.$dialog({
        data: {
          title: this.$t(operation.title),
          message: this.$t(operation.message),
          type: 'warning'
        },
        success: async () => {
          try {
            const params = {
              ids: selectedRecords.map((item) => item.id),
              ...extraParams
            }

            const res = await this.$API.regionalDeliveryPlanMgt[operation.api](params)
            if (res.code === 200) {
              this.$toast({
                content: this.$t(`${operation.title}成功！`),
                type: 'success'
              })
              await this.handleSearch()
            } else {
              throw new Error(res.msg || this.$t(`${operation.title}失败`))
            }
          } catch (error) {
            console.error(`${operation.title}失败:`, error)
          }
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.handleSearch('pageChange')
    },
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.handleSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
