<template>
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="serialNumber" :label="$t('序列号')" label-style="top">
          <mt-input
            v-model="searchFormModel.serialNumber"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="statusList" :label="$t('状态')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.statusList"
            css-class="rule-element"
            :data-source="statusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="businessType" :label="$t('业务类型')" label-style="top">
          <mt-select
            v-model="searchFormModel.businessType"
            :data-source="businessTypeList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="siteCodeList" :label="$t('工厂')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.siteCodeList"
            :url="$API.masterData.getSiteListUrl"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          />
        </mt-form-item>
        <mt-form-item prop="supplierCodeList" :label="$t('供应商')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCodeList"
            url="/masterDataManagement/tenant/supplier/paged-query-distinct-no-scope"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          />
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.itemCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.itemName"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="categoryCode" :label="$t('品类编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.categoryCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="categoryName" :label="$t('品类名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.categoryName"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="powerKw" :label="$t('功率')" label-style="top">
          <mt-input
            v-model="searchFormModel.powerKw"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="demandDate" :label="$t('需求日期')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.demandDate"
            :allow-edit="false"
            :placeholder="$t('请选择')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'demandDate')"
          />
        </mt-form-item>
        <mt-form-item prop="srmSignFlag" :label="$t('是否SRM签收')" label-style="top">
          <mt-select
            v-model="searchFormModel.srmSignFlag"
            :data-source="yesOrNoList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="warehouseCode" :label="$t('入库仓库')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.warehouseCode"
            url="/srm-purchase-pv/tenant/pv/warehouse/page/query"
            :placeholder="$t('请选择')"
            :fields="{ text: 'warehouseName', value: 'warehouseCode' }"
            params-key="fuzzyParam"
          />
        </mt-form-item>
        <mt-form-item prop="dataSource" :label="$t('数据来源')" label-style="top">
          <mt-select
            v-model="searchFormModel.dataSource"
            :data-source="dataSourceList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="warehouseDemandCode" :label="$t('仓库需求编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.warehouseDemandCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="receiver" :label="$t('收货人')" label-style="top">
          <mt-input
            v-model="searchFormModel.receiver"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="contactInfo" :label="$t('联系方式')" label-style="top">
          <mt-input
            v-model="searchFormModel.contactInfo"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="deliveryAddress" :label="$t('收货地址')" label-style="top">
          <mt-input
            v-model="searchFormModel.deliveryAddress"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
          <mt-input
            v-model="searchFormModel.createUserName"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :allow-edit="false"
            :placeholder="$t('请选择创建时间')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'createTime')"
          />
        </mt-form-item>
        <mt-form-item prop="updateUserName" :label="$t('最后更新人')" label-style="top">
          <mt-input
            v-model="searchFormModel.updateUserName"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="updateTime" :label="$t('最后更新时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.updateTime"
            :allow-edit="false"
            :placeholder="$t('请选择创建时间')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'updateTime')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="scTableRef"
      grid-id="f3a51f50-31a5-41f7-8e42-3a0c62a512e8"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      @refresh="handleSearch"
      @edit-closed="editComplete"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          v-permission="item.permission"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import RemoteAutocomplete from '@/components/RemoteAutocomplete/index.vue'
import VxeRemoteSearch from '@/components/VxeRemoteSearch/index.vue'
import mixin from './../config/mixin'
import {
  detailToolbar,
  statusList,
  businessTypeList,
  dataSourceList,
  yesOrNoList,
  syncJgStatusOptions
} from './../config/index'
import XEUtils from 'xe-utils'
import dayjs from 'dayjs'

// 常量定义
const DEFAULT_PAGE_SIZE = 20
const PAGE_SIZES = [20, 50, 200, 500, 1000, 2000]

// 操作类型映射
const OPERATION_MAP = {
  delete: {
    title: '删除',
    message: '确认删除选中的数据？',
    api: 'deleteDeliveryPlanApi'
  },
  publish: {
    title: '发布',
    message: '确认发布选中的数据？',
    api: 'publishDeliveryPlanApi'
  },
  close: {
    title: '关闭',
    message: '确认关闭选中的数据？',
    api: 'closeDeliveryPlanApi'
  },
  sync: {
    title: '同步极光',
    message: '确认同步选中的数据？',
    api: 'syncDeliveryPlanApi'
  }
}

export default {
  name: 'RegionalDeliveryPlanDetail',
  components: {
    ScTable,
    CollapseSearch,
    RemoteAutocomplete,
    VxeRemoteSearch
  },
  mixins: [mixin],
  data() {
    const startDate = dayjs().subtract(1, 'month').startOf('month')
    return {
      searchFormModel: {
        createTime: [new Date(startDate), new Date()],
        createTimeS: this.getUnix(dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00')),
        createTimeE: this.getUnix(dayjs(new Date()).format('YYYY-MM-DD 23:59:59'))
      },
      tableData: [],
      loading: false,
      toolbar: [],
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: DEFAULT_PAGE_SIZE,
        pageSizes: PAGE_SIZES
      },
      pageInfo: {
        size: DEFAULT_PAGE_SIZE,
        current: 1
      },
      isAdd: false,
      isEdit: false,
      statusList,
      businessTypeList,
      dataSourceList,
      factoryList: [],
      yesOrNoList,
      syncJgStatusOptions,
      editRules: {
        businessType: [{ required: true, message: this.$t('必填') }],
        siteCode: [{ required: true, message: this.$t('必填') }],
        supplierCode: [{ required: true, message: this.$t('必填') }],
        itemCode: [{ required: true, message: this.$t('必填') }],
        demandDate: [{ required: true, message: this.$t('必填') }],
        warehouseCode: [{ required: true, message: this.$t('必填') }],
        demandQuantity: [{ required: true, message: this.$t('必填') }]
      }
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef?.$refs.xGrid
    },
    columns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'serialNumber',
          title: this.$t('序列号'),
          minWidth: 180
        },
        {
          field: 'status',
          title: this.$t('状态'),
          minWidth: 80,
          slots: {
            default: ({ row }) => {
              const selectItem = statusList.find((item) => item.value === row.status)
              return [<div>{selectItem?.text}</div>]
            }
          }
        },
        {
          field: 'businessType',
          title: this.$t('业务类型'),
          minWidth: 130,
          editRender: {},
          slots: {
            default: ({ row }) => {
              const selectItem = businessTypeList.find((item) => item.value === row.businessType)
              return [<div>{selectItem?.text}</div>]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.businessType}
                  options={this.businessTypeList}
                  option-props={{ label: 'text', value: 'value' }}
                  placeholder={this.$t('请选择')}
                  clearable
                  filterable
                  transfer
                  onChange={() => {
                    const selectedItem = this.businessTypeList.find(
                      (v) => v.value === row.businessType
                    )
                    row.businessTypeName = selectedItem?.text
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'siteCode',
          title: this.$t('工厂'),
          minWidth: 220,
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [row.siteCode ? <div>{row.siteCode + '-' + row.siteName}</div> : null]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.siteCode}
                  options={this.factoryList}
                  placeholder={this.$t('请选择')}
                  clearable
                  filterable
                  transfer
                  disabled={row.dataOrigin === 2}
                  onChange={() => {
                    const selectedItem = this.factoryList.find((v) => v.value === row.siteCode)
                    row.siteName = selectedItem?.siteName
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商'),
          minWidth: 220,
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [
                row.supplierCode ? <span>{row.supplierCode + '-' + row.supplierName}</span> : null
              ]
            },
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.supplierCode}
                  placeholder={this.$t('请选择')}
                  fields={{ text: 'supplierName', value: 'supplierCode' }}
                  request-info={{
                    urlPre: 'masterData',
                    url: 'supplierDistinctPagedQueryNoScope',
                    searchFields: ['supplierCode', 'supplierName'],
                    params: {
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  onChange={(e) => {
                    row.supplierTenantId = e?.supplierTenantId || null
                    row.supplierName = e?.supplierName || null
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'itemCode',
          title: this.$t('物料'),
          minWidth: 220,
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [row.itemCode ? <span>{row.itemCode + '-' + row.itemName}</span> : null]
            },
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.itemCode}
                  fields={{ text: 'itemName', value: 'itemCode' }}
                  placeholder={this.$t('请选择')}
                  request-info={{
                    urlPre: 'masterData',
                    url: 'getItemPage',
                    searchFields: ['itemCode', 'itemName'],
                    params: {
                      defaultRules: [
                        {
                          field: 'organizationCode',
                          operator: 'equal',
                          value: row.siteCode
                        }
                      ],
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  link-field-value={row.siteCode}
                  disabled={row.siteCode ? false : true}
                  onChange={(e) => {
                    if (!e?.checked) {
                      row.itemName = e?.itemName || null
                      row.unitCode = e?.baseMeasureUnitCode || null
                      row.unitName = e?.baseMeasureUnitName || null
                      row.categoryCode = e?.categoryResponse?.categoryCode || null
                      row.categoryName = e?.categoryResponse?.categoryName || null
                      row.majorCategory = e?.bigCategName || null
                      row.mediumCategory = e?.medCategName || null
                      row.minorCategory = e?.smallCategName || null
                    }
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'categoryCode',
          title: this.$t('品类'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              return [
                row.categoryCode ? <div>{row.categoryCode + '-' + row.categoryName}</div> : null
              ]
            }
          }
        },
        {
          field: 'powerKw',
          title: this.$t('功率(KW)'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.powerKw}
                  placeholder={this.$t('请输入')}
                  min='0'
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'unitCode',
          title: this.$t('基本单位'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              return [row.unitCode ? <span>{row.unitCode + '-' + row.unitName}</span> : null]
            }
          }
        },
        {
          field: 'totalWarehouseDemand',
          title: this.$t('仓库总需求'),
          minWidth: 120
        },
        {
          field: 'demandDate',
          title: this.$t('需求日期'),
          minWidth: 130,
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [
                row.demandDate ? <div>{dayjs(row.demandDate).format('YYYY-MM-DD')}</div> : null
              ]
            },
            edit: ({ row }) => {
              const dateDisabled = (params) => {
                const { date } = params
                return date < dayjs().startOf('day')
              }
              return [
                <vxe-input
                  type={'date'}
                  v-model={row.demandDate}
                  placeholder={this.$t('请选择')}
                  transfer
                  clearable
                  label-format='yyyy-MM-dd'
                  value-format='yyyy-MM-dd'
                  disabled-method={dateDisabled}
                />
              ]
            }
          }
        },
        {
          field: 'warehouseCode',
          title: this.$t('入库仓库'),
          minWidth: 160,
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [
                row.warehouseCode ? <div> {row.warehouseCode + '-' + row.warehouseName}</div> : null
              ]
            },
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.warehouseCode}
                  placeholder={this.$t('请选择')}
                  fields={{ text: 'warehouseName', value: 'warehouseCode' }}
                  request-info={{
                    urlPre: 'demandManagement',
                    url: 'queryPvWarehouseList',
                    searchKey: 'fuzzyParam',
                    params: {
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  onChange={(e) => {
                    if (!e?.checked) {
                      row.warehouseName = e?.warehouseName || null
                      row.receiver = e?.contactor || null
                      row.contactInfo = e?.contactPhone || null
                      row.deliveryAddress = e?.addr || null
                      row.srmSignFlag = e?.wmsStoryFlag === '0' ? 1 : 0
                    }
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'srmSignFlag',
          title: this.$t('是否SRM签收'),
          minWidth: 130,
          slots: {
            default: ({ row }) => {
              const selectItem = yesOrNoList.find((item) => item.value === row.srmSignFlag)
              return [<div>{selectItem?.text}</div>]
            }
          }
        },
        {
          field: 'demandQuantity',
          title: this.$t('需求数量'),
          minWidth: 130,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.demandQuantity}
                  placeholder={this.$t('请输入')}
                  min={row.dataOrigin === 2 ? 0 : 1}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'promisedQuantity',
          title: this.$t('承诺数量'),
          minWidth: 120
        },
        {
          field: 'pendingShipmentQuantity',
          title: this.$t('待发货数量'),
          minWidth: 120
        },
        {
          field: 'inTransitQuantity',
          title: this.$t('在途数量'),
          minWidth: 100
        },
        {
          field: 'shippedQuantity',
          title: this.$t('已发货数量'),
          minWidth: 120
        },
        {
          field: 'inboundQuantity',
          title: this.$t('已入库数量'),
          minWidth: 120
        },
        {
          field: 'majorCategory',
          title: this.$t('大类'),
          minWidth: 140
        },
        {
          field: 'mediumCategory',
          title: this.$t('中类'),
          minWidth: 140
        },
        {
          field: 'minorCategory',
          title: this.$t('小类'),
          minWidth: 140
        },
        {
          field: 'dataSource',
          title: this.$t('数据来源'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = dataSourceList.find((item) => item.value === row.dataSource)
              return [<div>{selectItem?.text}</div>]
            }
          }
        },
        {
          field: 'warehouseDemandCode',
          title: this.$t('仓库需求编码'),
          minWidth: 120
        },
        {
          field: 'receiver',
          title: this.$t('收货人'),
          minWidth: 120
        },
        {
          field: 'contactInfo',
          title: this.$t('收货联系方式'),
          minWidth: 120
        },
        {
          field: 'deliveryAddress',
          title: this.$t('配送地址'),
          minWidth: 120
        },
        {
          field: 'buyerRemarks',
          title: this.$t('采方备注'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.buyerRemarks}
                  transfer
                  clearable
                  placeholder={this.$t('请输入')}
                />
              ]
            }
          }
        },
        {
          field: 'supplierRemarks',
          title: this.$t('供方备注'),
          minWidth: 120
        },
        {
          field: 'publishTime',
          title: this.$t('发布时间'),
          minWidth: 160
        },
        {
          field: 'feedbackTime',
          title: this.$t('反馈时间'),
          minWidth: 160
        },
        {
          field: 'syncJgStatus',
          title: this.$t('同步极光状态'),
          minWidth: 120,
          formatter: ({ cellValue }) => {
            let item = syncJgStatusOptions.find((item) => item.value === cellValue)
            return item ? item.text : ''
          }
        },
        {
          field: 'syncJgMsg',
          title: this.$t('同步极光信息'),
          minWidth: 120
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          minWidth: 160
        },
        {
          field: 'updateUserName',
          title: this.$t('最后更新人'),
          minWidth: 120
        },
        {
          field: 'updateTime',
          title: this.$t('最后更新时间'),
          minWidth: 160
        }
      ]
    },
    editConfig() {
      return {
        enabled:
          window.elementPermissionSet.includes('O_02_1835') ||
          window.elementPermissionSet.includes('O_02_1836'),
        trigger: 'dblclick',
        mode: 'row',
        showStatus: true,
        beforeEditMethod: this.beforeEditMethod
      }
    }
  },
  created() {
    this.initializeToolbar()
  },
  mounted() {
    this.getFactoryList()
    this.handleSearch()
  },
  activated() {
    this.handleSearch()
  },
  methods: {
    beforeEditMethod({ row }) {
      if (![0, 1, 4].includes(row.status)) {
        return false
      }
      return true
    },
    initializeToolbar() {
      this.toolbar = this.$router.path?.includes('-sup')
        ? [{ code: 'export', name: this.$t('导出'), status: 'info', loading: false }]
        : detailToolbar
    },
    async getFactoryList() {
      try {
        const res = await this.$API.masterData.getSiteListByPermission()
        this.factoryList = res.data.map((i) => ({
          ...i,
          label: `${i.siteCode}-${i.siteName}`,
          value: i.siteCode
        }))
      } catch (error) {
        console.error('获取工厂列表失败:', error)
      }
    },
    async editComplete(args) {
      const { row } = args
      if (!args.$event) return

      if (args.$event.srcElement.innerText === this.$t('取消编辑')) {
        await this.handleSearch()
        return
      }

      try {
        const valid = await this.tableRef.validate([row])
        if (valid) {
          this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
          return
        }

        const params = {
          ...row,
          id: row.id.includes('row_') ? null : row.id,
          demandDate: row.demandDate ? dayjs(row.demandDate).valueOf() : null
        }

        let api = this.isAdd
          ? this.$API.regionalDeliveryPlanMgt.addDeliveryPlanApi
          : this.$API.regionalDeliveryPlanMgt.updateDeliveryPlanApi

        const res = await api([params])
        if (res.code === 200) {
          this.$toast({ content: this.$t('保存成功！'), type: 'success' })
          await this.handleSearch()
        } else {
          throw new Error(res.msg || this.$t('保存失败'))
        }
      } catch (error) {
        console.error('保存失败:', error)
        this.tableRef.setEditRow(row)
      }
    },
    handleDateChange(e, field) {
      const startField = field + 'S'
      const endField = field + 'E'

      if (e.startDate) {
        const startDate = XEUtils.toDateString(e.startDate, 'yyyy-MM-dd') + ' 00:00:00'
        const endDate = XEUtils.toDateString(e.endDate, 'yyyy-MM-dd') + ' 23:59:59'
        this.searchFormModel[startField] = XEUtils.timestamp(startDate)
        this.searchFormModel[endField] = XEUtils.timestamp(endDate)
      } else {
        this.searchFormModel[startField] = null
        this.searchFormModel[endField] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleReset() {
      this.searchFormModel = {}
      const startDate = dayjs().subtract(1, 'month').startOf('month')
      this.searchFormModel.createTime = [new Date(startDate), new Date()]
      this.searchFormModel.createTimeS = this.getUnix(
        dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00')
      )
      this.searchFormModel.createTimeE = this.getUnix(
        dayjs(new Date()).format('YYYY-MM-DD 23:59:59')
      )
      this.handleSearch()
    },
    async handleSearch(type) {
      if (!type) {
        this.resetPagination()
      }

      try {
        this.loading = true
        const params = {
          page: this.pageInfo,
          ...this.searchFormModel
        }
        delete params.submitTime

        const res = await this.$API.regionalDeliveryPlanMgt.pageDeliveryPlanApi(params)
        if (res.code === 200) {
          await this.updateTableData(res.data)
        } else {
          throw new Error(res.msg || this.$t('查询失败'))
        }
      } catch (error) {
        console.error('查询失败:', error)
      } finally {
        this.loading = false
      }
    },
    resetPagination() {
      this.pageInfo.current = 1
      this.$refs.pageRef.jumpNum = 1
      this.$refs.pageRef.currentPage = 1
    },
    async updateTableData(data) {
      this.tableData = (data?.records || []).map((item, index) => ({
        ...item,
        index: index + 1
      }))
      this.pageSettings.totalPages = Math.ceil(Number(data.total) / this.pageSettings.pageSize)
      this.pageSettings.totalRecordsCount = Number(data?.total)

      await this.tableRef?.clearEdit()
      this.isAdd = false
      this.isEdit = false
    },
    async handleClickToolBar(item) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (selectedRecords.length === 0 && ['delete', 'publish', 'close', 'sync'].includes(item.code)) {
        this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
        return
      }

      const actionMap = {
        add: this.handleAdd,
        cancel: this.handleSearch,
        delete: () => this.handleOperate(selectedRecords, 'delete'),
        publish: () => this.handleOperate(selectedRecords, 'publish'),
        close: () => this.handleOperate(selectedRecords, 'close'),
        import: this.handleImport,
        export: () => this.handleExport(item),
        sync: () => this.handleOperate(selectedRecords, 'sync')
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },
    handleAdd() {
      if (this.isEdit) {
        this.$toast({ content: this.$t('请先完成当前编辑'), type: 'warning' })
        return false
      }
      const item = {
        status: 0
      }
      this.tableRef.insert([item])
      this.$nextTick(() => {
        const currentViewRecords = this.tableRef.getTableData().visibleData
        this.tableRef.setEditRow(currentViewRecords[0])
        this.isAdd = true
        this.isEdit = true
      })
    },
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.regionalDeliveryPlanMgt.importDeliveryPlanApi,
          downloadTemplateApi: this.$API.regionalDeliveryPlanMgt.getDeliveryPlanImportTemplateApi,
          paramsKey: 'excel',
          downloadTemplateParams: {
            page: this.pageInfo,
            ...this.searchFormModel
          }
        },
        success: () => {
          this.handleSearch()
        }
      })
    },
    async handleExport(item) {
      try {
        item.loading = true
        const dynamicHeaderMap = {}
        const specialFields = ['status', 'businessType', 'srmSignFlag', 'dataSource']
        const codeNameMap = {
          siteCode: '工厂名称',
          supplierCode: '供应商名称',
          itemCode: '物料名称',
          categoryCode: '品类名称',
          unitCode: '单位名称',
          warehouseCode: '入库仓库名称'
        }

        this.columns.forEach(({ field, title }) => {
          if (!field) return

          if (specialFields.includes(field)) {
            dynamicHeaderMap[`${field}Name`] = title
          } else if (codeNameMap[field]) {
            dynamicHeaderMap[field] = title
            dynamicHeaderMap[`${field.replace('Code', '')}Name`] = this.$t(codeNameMap[field])
          } else {
            dynamicHeaderMap[field] = title
          }
        })

        const params = {
          dynamicHeaderMap,
          req: {
            page: {
              current: this.pageSettings.current,
              size: this.pageSettings.pageSize
            },
            ...this.searchFormModel
          }
        }

        const res = await this.$API.regionalDeliveryPlanMgt.exportDeliveryPlanApi(params)
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      } catch (error) {
        console.error('导出失败:', error)
      } finally {
        item.loading = false
      }
    },
    handleOperate(selectedRecords, type, extraParams = {}) {
      const operation = OPERATION_MAP[type]
      if (!operation) {
        this.$toast({ content: this.$t('不支持的操作类型'), type: 'error' })
        return
      }

      this.$dialog({
        data: {
          title: this.$t(operation.title),
          message: this.$t(operation.message),
          type: 'warning'
        },
        success: async () => {
          try {
            const params = {
              ids: selectedRecords.map((item) => item.id),
              ...extraParams
            }

            const res = await this.$API.regionalDeliveryPlanMgt[operation.api](params)
            if (res.code === 200) {
              this.$toast({
                content: this.$t(`${operation.title}成功！`),
                type: 'success'
              })
              await this.handleSearch()
            } else {
              throw new Error(res.msg || this.$t(`${operation.title}失败`))
            }
          } catch (error) {
            console.error(`${operation.title}失败:`, error)
          }
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.handleSearch('pageChange')
    },
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.handleSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
