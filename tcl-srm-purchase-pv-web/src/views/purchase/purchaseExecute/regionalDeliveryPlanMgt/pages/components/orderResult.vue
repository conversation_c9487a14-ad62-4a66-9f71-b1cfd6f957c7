<template>
  <mt-dialog
    ref="dialog"
    height="80%"
    width="70%"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <!-- <mt-template-page ref="templateRef" :template-config="pageConfig" /> -->
      <template>
        <div style="width: 100%">
          <!-- 自定义查询条件 -->
          <!-- <collapse-search
            class="toggle-container"
            :is-grid-display="true"
            :default-expand="true"
            @reset="handleReset"
            @search="handleSearch"
          >
            <mt-form ref="searchFormRef" :model="searchFormModel">
              <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
                <mt-input
                  v-model="searchFormModel.itemCode"
                  :show-clear-button="true"
                  :placeholder="$t('请输入物料编码')"
                />
              </mt-form-item>
              <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
                <mt-input
                  v-model="searchFormModel.itemName"
                  :show-clear-button="true"
                  :placeholder="$t('请输入物料名称')"
                />
              </mt-form-item>
            </mt-form>
          </collapse-search> -->
          <!-- 表格 -->
          <sc-table
            ref="sctableRef"
            grid-id="d95e0241-5ae4-5e5f-d130-224d66ef540d"
            :loading="loading"
            :is-show-refresh-bth="true"
            :fix-height="300"
            :columns="listColumns"
            :table-data="tableData"
            @refresh="handleSearch"
          >
            <template slot="custom-tools">
              <vxe-button
                v-for="item in toolbar"
                :key="item.code"
                :status="item.status"
                :icon="item.icon"
                size="small"
                @click="handleClickToolBar(item)"
                >{{ item.name }}</vxe-button
              >
            </template>
          </sc-table>
          <!-- 分页 -->
          <!-- <mt-page
            ref="pageRef"
            class="flex-keep custom-page"
            :page-settings="pageSettings"
            :total-pages="pageSettings.totalPages"
            @currentChange="handleCurrentChange"
            @sizeChange="handleSizeChange"
          /> -->
        </div>
      </template>
    </div>
  </mt-dialog>
</template>

<script>
// import CollapseSearch from '@/components/collapseSearch'n
import ScTable from '@/components/ScTable/src/index'
import { allocationStateList } from '../../config'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  components: {
    ScTable
    // CollapseSearch
  },
  data() {
    return {
      // pageSettings: {
      //   current: 1,
      //   pageCount: 5,
      //   totalPages: 0,
      //   totalRecordsCount: 0,
      //   pageSize: 20,
      //   pageSizes: [20, 50, 200, 500, 1000, 2000]
      // },
      // pageInfo: {
      //   size: 20,
      //   current: 1
      // },
      toolbar: [{ code: 'export', name: this.$t('导出'), status: 'info' }],
      searchFormModel: {},
      tableData: [],
      loading: false,
      data: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    // 列配置
    listColumns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'allocationStatus',
          title: this.$t('分配状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = allocationStateList.find(
                (item) => item.value === row.allocationStatus
              )
              const name = selectItem?.text
              return [<div>{name}</div>]
            }
          }
        },
        {
          minWidth: 150,
          field: 'allocationDes',
          title: this.$t('分配失败原因')
        },
        {
          minWidth: 120,
          field: 'areaGoodsPlanCode',
          title: this.$t('区域计划编码')
        },
        {
          minWidth: 150,
          field: 'warehouseCode',
          title: this.$t('入库仓库'),
          slots: {
            default: ({ row }) => {
              return [
                row.warehouseCode ? <div> {row.warehouseCode + '-' + row.warehouseName}</div> : null
              ]
            }
          }
        },
        {
          width: '150',
          field: 'itemCode',
          title: this.$t('物料编码')
        },
        { width: '150', field: 'itemName', title: this.$t('物料名称') },
        {
          field: 'orderPreDeliveryQuantity',
          title: this.$t('订单待发货数量'),
          minWidth: 140
        },
        {
          field: 'quantity',
          title: this.$t('需求数量'),
          minWidth: 120
        },
        {
          field: 'allocationQuantity',
          title: this.$t('分配数量'),
          minWidth: 120
        },
        {
          field: 'demandDate',
          title: this.$t('需求日期'),
          minWidth: 150
        },
        {
          field: 'orderCode',
          title: this.$t('采购订单号'),
          minWidth: 120
        },
        {
          field: 'orderItemNo',
          title: this.$t('采购订单行号'),
          minWidth: 120
        },
        {
          minWidth: 150,
          field: 'supplierCode',
          title: this.$t('供应商编码')
        },
        {
          minWidth: 150,
          field: 'supplierName',
          title: this.$t('供应商名称')
        },
        {
          minWidth: 150,
          field: 'categoryCode',
          title: this.$t('品类编码')
        },
        {
          minWidth: 150,
          field: 'categoryName',
          title: this.$t('品类名称')
        }
      ]
    },
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.handleSearch()
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    // 点击按钮栏
    handleClickToolBar(e) {
      if (e.code === 'export') {
        // 导出咯
        const params = { ids: this.modalData.selectedRecords?.map((item) => item.id) || [] } // 筛选条件
        this.$API.regionalDeliveryPlanMgt.exportAreaGoodsPlanAllocationList(params).then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
      }
    },
    confirm() {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (selectedRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      } else {
        this.$emit('confirm-function', selectedRecords)
      }
    },
    cancel() {
      this.$emit('cancel-function')
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    // 查询
    async handleSearch() {
      // 解决修改查询条件后，查询结果有数据但未正常显示的问题（type: pageChange/refresh）
      // if (!type) {
      //   this.pageInfo.current = 1
      //   this.$refs.pageRef.jumpNum = 1
      //   this.$refs.pageRef.currentPage = 1
      // }
      const params = {
        // page: this.pageInfo,
        ids: this.modalData.selectedRecords?.map((item) => item.id) || []
      }
      this.loading = true
      const res = await this.$API.regionalDeliveryPlanMgt
        .areaGoodsPlanAllocationList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data || []
        this.tableData?.forEach((item, index) => {
          item.index = index + 1
        }) || []
        // this.pageSettings.totalPages = Math.ceil(
        //   Number(res.data.total) / this.pageSettings.pageSize
        // )
        // this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    }
    // 切换page
    // handleCurrentChange(currentPage) {
    //   this.pageInfo.current = currentPage
    //   this.$refs.pageRef.jumpNum = currentPage
    //   this.handleSearch('pageChange')
    // },
    // // 切换pageSize
    // handleSizeChange(pageSize) {
    //   this.pageSettings.pageSize = pageSize
    //   this.pageInfo = {
    //     size: pageSize,
    //     current: 1
    //   }
    //   this.handleSearch()
    // }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
