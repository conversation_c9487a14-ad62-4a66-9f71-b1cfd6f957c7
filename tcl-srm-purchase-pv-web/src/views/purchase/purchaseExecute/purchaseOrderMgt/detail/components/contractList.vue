<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <!-- <mt-template-page ref="templateRef" :template-config="pageConfig" /> -->
      <template>
        <div style="width: 100%">
          <!-- 自定义查询条件 -->
          <collapse-search
            class="toggle-container"
            :is-grid-display="true"
            :default-expand="true"
            @reset="handleReset"
            @search="handleSearch"
          >
            <mt-form ref="searchFormRef" :model="searchFormModel">
              <mt-form-item prop="contractCode" :label="$t('合同编号')" label-style="top">
                <mt-input
                  v-model="searchFormModel.contractCode"
                  :show-clear-button="true"
                  :placeholder="$t('请输入')"
                />
              </mt-form-item>
              <mt-form-item prop="contractStatus" :label="$t('合同状态')" label-style="top">
                <mt-multi-select
                  v-model="searchFormModel.contractStatus"
                  css-class="rule-element"
                  :data-source="contractStatusList"
                  :fields="{ text: 'text', value: 'value' }"
                  :show-clear-button="true"
                  :allow-filtering="true"
                  filter-type="Contains"
                  :placeholder="$t('请选择')"
                />
              </mt-form-item>
              <mt-form-item prop="contractName" :label="$t('合同名称')" label-style="top">
                <mt-input
                  v-model="searchFormModel.contractName"
                  :show-clear-button="true"
                  :placeholder="$t('请输入')"
                />
              </mt-form-item>
              <mt-form-item prop="supplierCodeStr" :label="$t('供应商编码')" label-style="top">
                <mt-input
                  v-model="searchFormModel.supplierCodeStr"
                  :show-clear-button="true"
                  :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
                />
              </mt-form-item>
              <mt-form-item prop="contractType" :label="$t('合同类型')" label-style="top">
                <mt-multi-select
                  v-model="searchFormModel.contractType"
                  css-class="rule-element"
                  :data-source="contractTypeList"
                  :fields="{ text: 'text', value: 'value' }"
                  :show-clear-button="true"
                  :allow-filtering="true"
                  filter-type="Contains"
                  :placeholder="$t('请选择')"
                />
              </mt-form-item>
              <mt-form-item prop="signDate" :label="$t('签订日期')" label-style="top">
                <mt-date-range-picker
                  v-model="searchFormModel.signDate"
                  :allow-edit="false"
                  :placeholder="$t('请选择')"
                  :open-on-focus="true"
                  @change="(e) => handleDateChange(e, 'signDate')"
                />
              </mt-form-item>
            </mt-form>
          </collapse-search>
          <!-- 表格 -->
          <sc-table
            ref="scTableRef"
            grid-id="44cc9508-b908-cb15-d8fc-ca71bdcfcda5"
            :loading="loading"
            :is-show-refresh-bth="true"
            :columns="listColumns"
            :fix-height="294"
            :table-data="tableData"
            @refresh="handleSearch"
          >
            <template slot="custom-tools">
              <vxe-button
                v-for="item in toolbar"
                :key="item.code"
                :status="item.status"
                :icon="item.icon"
                size="small"
                @click="handleClickToolBar(item)"
                >{{ item.name }}</vxe-button
              >
            </template>
          </sc-table>
          <!-- 分页 -->
          <mt-page
            ref="pageRef"
            class="flex-keep custom-page"
            :page-settings="pageSettings"
            :total-pages="pageSettings.totalPages"
            @currentChange="handleCurrentChange"
            @sizeChange="handleSizeChange"
          />
        </div>
      </template>
    </div>
  </mt-dialog>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { contractStatusList, contractTypeList } from './config/item'
import { businessTypeList } from './../../config/index'
import XEUtils from 'xe-utils'
export default {
  components: {
    ScTable,
    CollapseSearch
  },
  data() {
    return {
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 20,
        pageSizes: [20, 50, 200, 500, 1000, 2000]
      },
      pageInfo: {
        size: 20,
        current: 1
      },
      toolbar: [],
      searchFormModel: {},
      contractStatusList,
      contractTypeList,
      tableData: [],
      loading: false,
      data: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],

      businessTypeList
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    },
    // 列配置
    listColumns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        // {
        //   width: 70,
        //   field: 'index',
        //   title: this.$t('序号')
        // },
        {
          field: 'contractCode',
          title: this.$t('合同编号'),
          minWidth: 180
          // slots: {
          //   default: ({ row, column }) => {
          //     return [
          //       <a on-click={() => this.handleClickCellTitle(row, column)}>{row.contractCode}</a>
          //     ]
          //   }
          // }
        },
        {
          field: 'contractName',
          title: this.$t('合同名称'),
          minWidth: 140
        },
        {
          field: 'contractStatus',
          title: this.$t('合同状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = contractStatusList.find(
                (item) => item.value === row.contractStatus
              )
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'businessType',
          title: this.$t('业务类型'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = businessTypeList.find((item) => item.value === row.businessType)
              const businessTypeName = selectItem?.text
              return [<div>{businessTypeName}</div>]
            }
          }
        },
        {
          field: 'companyCode',
          title: this.$t('公司'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [
                row.companyCode ? <span>{row.companyCode + '-' + row.companyName}</span> : null
              ]
            }
          }
        },
        {
          field: 'purchaseOrgCode',
          title: this.$t('采购组织'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [
                row.purchaseOrgCode ? (
                  <span>{row.purchaseOrgCode + '-' + row.purchaseOrgName}</span>
                ) : null
              ]
            }
          }
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商编码'),
          minWidth: 140
        },
        {
          field: 'supplierName',
          title: this.$t('供应商名称'),
          minWidth: 140
        },
        {
          field: 'contractType',
          title: this.$t('合同类型'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = contractTypeList.find((item) => item.value === row.contractType)
              const name = selectItem?.text
              return [<div>{name}</div>]
            }
          }
        },
        {
          field: 'currencyCode',
          title: this.$t('币种'),
          minWidth: 200,
          slots: {
            default: ({ row }) => {
              return [<span>{row.currencyCode + '-' + row.currencyName}</span>]
            }
          }
        },
        {
          field: 'categoryCode',
          title: this.$t('品类'),
          minWidth: 200,
          slots: {
            default: ({ row }) => {
              return [<span>{row.categoryCode + '-' + row.categoryName}</span>]
            }
          }
        },
        {
          field: 'signDate',
          title: this.$t('签订日期'),
          minWidth: 140
        },
        {
          field: 'contractStartValid',
          title: this.$t('合同生效时间'),
          minWidth: 140
        },
        {
          field: 'contractEndValid',
          title: this.$t('合同终止时间'),
          minWidth: 140
        },
        {
          field: 'untaxedContractTotal',
          title: this.$t('合同总金额（未税）'),
          minWidth: 180
        },
        {
          field: 'taxedContractTotal',
          title: this.$t('合同总金额（含税）'),
          minWidth: 180
        }
      ]
    },
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.handleSearch()
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    // 日期格式化
    handleDateChange(e, field) {
      const startField = field + 'Start'
      const endField = field + 'End'
      if (e.startDate) {
        const startDate = XEUtils.toDateString(e.startDate, 'yyyy-MM-dd') + ' 00:00:00'
        const endDate = XEUtils.toDateString(e.endDate, 'yyyy-MM-dd') + ' 23:59:59'
        this.searchFormModel[startField] = XEUtils.timestamp(startDate)
        this.searchFormModel[endField] = XEUtils.timestamp(endDate)
      } else {
        this.searchFormModel[startField] = null
        this.searchFormModel[endField] = null
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      if (e.code === 'export') {
        // 导出咯
      }
    },
    confirm() {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (selectedRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      // 校验选中合同的一致性
      const firstRecord = selectedRecords[0]
      const checkFields = [
        'businessType',
        'companyCode',
        'purchaseOrgCode',
        'supplierCode',
        'currencyCode',
        'categoryCode'
      ]
      const fieldNames = {
        businessType: '业务类型',
        companyCode: '公司',
        purchaseOrgCode: '采购组织',
        supplierCode: '供应商',
        currencyCode: '币种',
        categoryCode: '品类'
      }

      for (const field of checkFields) {
        const hasDifferent = selectedRecords.some((record) => record[field] !== firstRecord[field])
        if (hasDifferent) {
          this.$toast({
            content: this.$t(`选中的合同${fieldNames[field]}不一致，请重新选择`),
            type: 'warning'
          })
          return
        }
      }

      this.$emit('confirm-function', selectedRecords)
    },
    cancel() {
      this.$emit('cancel-function')
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    // 查询
    async handleSearch(type) {
      // 解决修改查询条件后，查询结果有数据但未正常显示的问题（type: pageChange/refresh）
      if (!type) {
        this.pageInfo.current = 1
        this.$refs.pageRef.jumpNum = 1
        this.$refs.pageRef.currentPage = 1
      }
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel,
        contractStatus:
          this.searchFormModel.contractStatus && this.searchFormModel.contractStatus.length
            ? this.searchFormModel.contractStatus
            : [2, 5],
        status: [1],
        contractEndValidStart: XEUtils.timestamp(
          XEUtils.toDateString(new Date(), 'yyyy-MM-dd') + ' 23:59:59'
        )
      }
      delete params.submitTime
      this.loading = true
      const res = await this.$API.contract
        .queryContractList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.tableData?.forEach((item, index) => {
          item.index = index + 1
        }) || []
        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.handleSearch('pageChange')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.handleSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
