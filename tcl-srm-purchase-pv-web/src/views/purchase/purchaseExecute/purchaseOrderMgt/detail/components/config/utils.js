const fieldMap = {
  'biddingItemDTO.bidTaxRateName': 'taxItemName',
  baseUnitName: 'unitName',
  categoryName: 'categoryName',
  currencyName: 'currencyName',
  siteName: 'siteName',
  taxName: 'taxItemName',
  purGroupCode: 'groupCode',
  purUnitName: 'purUnitName',
  taxRateName: 'taxItemName',
  'annualLogisticsTrunkItem.logisticsMethodName': 'dictCode'
}

export const addArrTextField = (arr, code, name) => {
  arr.forEach((e) => {
    e.__text = `${e[code]} - ${e[name]}`
  })
  return arr
}
/**
 * 数据设置
 * @param {} params
 * @returns
 */
const setData = (params, data) => {
  const rowData = Object.assign({}, params.node.data, data)
  params.node.setData(rowData)
}
/**
 * 数据联动
 * @param {} params
 * @returns
 */
const linkUpdate = (mapData, params) => {
  const field = params.column.colId
  if (!Object.keys(mapData).includes(field)) return
  const { row } = getOptions(params)
  let obj = {}
  for (const linkField in mapData[field]) {
    obj[linkField] = row[mapData[field][linkField]]
  }
  // 设置数据
  setData(params, obj)
}
export const getOptions = (params) => {
  const field = params.column.colId
  const value = params.value
  let dataSource = params.colDef?.cellEditorParams?.editConfig?.props?.dataSource
    ? params.colDef?.cellEditorParams?.editConfig?.props?.dataSource
    : params.colDef?.cellEditorParams?.editConfig?.dataSource
  dataSource = dataSource ? dataSource : []
  const findField = fieldMap[field]
  let row = null
  if (dataSource?.length) {
    row = dataSource.find((e) => e[findField] === value)
  }
  return {
    field,
    value,
    row
  }
}
/**
 * 单位联动
 * unitName => unitId unitCode 单位联动
 */
export const inputUnit = (params) => {
  const unitMap = {
    baseUnitName: {
      baseUnitId: 'id',
      baseUnitCode: 'unitCode'
    }
  }
  linkUpdate(unitMap, params)
}
/**
 * 工厂联动
 * siteName => siteId siteCode 工厂联动
 */
export const inputSite = (params) => {
  const siteMap = {
    siteName: {
      siteCode: 'siteCode'
    }
  }
  linkUpdate(siteMap, params)
}
/**
 * 工厂联动
 * siteName => siteId siteCode 工厂联动
 */
export const inputTax = (params) => {
  const taxMap = {
    taxName: {
      taxCode: 'taxItemCode'
    }
  }
  linkUpdate(taxMap, params)
}
/**
 * 币种名称 => 币种编码联动编辑（暂不设置双向联动）
 */
export const inputCurrency = (params) => {
  const currencyMap = {
    currencyName: {
      currencyCode: 'currencyCode'
    }
  }
  linkUpdate(currencyMap, params)
}
