import { i18n } from '@/main'
export const whetherMapList = [
  { label: i18n.t('否'), text: i18n.t('否'), value: 0 },
  { label: i18n.t('是'), text: i18n.t('是'), value: 1 }
]
// 价格单位 - 非采
export const priceUnitList = [
  { label: i18n.t('元'), text: i18n.t('元'), value: '1' },
  { label: i18n.t('万元'), text: i18n.t('万元'), value: '0.0001' }
]
// 合同类型
export const contractTypeList = [
  { value: 1, text: i18n.t('年度框采合同') },
  { value: 2, text: i18n.t('技术协议') },
  { value: 3, text: i18n.t('廉洁协议') },
  { value: 4, text: i18n.t('质量协议') },
  { value: 5, text: i18n.t('补充协议') },
  { value: 6, text: i18n.t('采购订单') }
]

// 合同状态
export const contractStatusList = [
  { value: 2, text: i18n.t('审批通过') },
  { value: 5, text: i18n.t('已归档') }
]

//详情 - 物料明细 - 操作按钮
export const itemToolbar = (status) => [
  {
    id: 'select',
    title: i18n.t('批量选择物料'),
    hide: [2, 5].includes(status) // 审批通过、已归档状态隐藏
  },
  {
    id: 'add',
    title: i18n.t('新增'),
    hide: [2, 5].includes(status) // 审批通过、已归档状态隐藏
  },
  {
    id: 'delete',
    title: i18n.t('删除'),
    hide: [2, 5].includes(status) // 审批通过、已归档状态隐藏
  },
  {
    id: 'import',
    title: i18n.t('导入'),
    hide: [2, 5].includes(status) // 审批通过、已归档状态隐藏
  },
  {
    id: 'export',
    title: i18n.t('导出')
  }
]
