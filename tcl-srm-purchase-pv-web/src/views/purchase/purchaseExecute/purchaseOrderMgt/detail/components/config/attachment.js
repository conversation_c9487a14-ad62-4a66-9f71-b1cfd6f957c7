import { i18n } from '@/main.js'

export const toolBar = (editable) => [
  {
    id: 'download',
    title: i18n.t('下载')
  },
  {
    id: 'upload',
    title: i18n.t('上传'),
    hide: !editable // 审批通过、已归档状态隐藏
    // hide:
    //   (!$route.path?.includes('-sup') && !editable) ||
    //   ($route.path?.includes('-sup') && $route.query.type === 'detail') // 审批通过、已归档状态隐藏
  },
  {
    id: 'delete',
    title: i18n.t('删除'),
    hide: !editable // 审批通过、已归档状态隐藏
    // hide:
    //   (!$route.path?.includes('-sup') && !editable) ||
    //   ($route.path?.includes('-sup') && $route.query.type === 'detail') // 审批通过、已归档状态隐藏
  }
]

export const columnData = [
  { option: 'checkboxSelection', width: 55 },
  {
    field: 'fileName',
    headerName: i18n.t('文件名称'),
    cellStyle: () => {
      return {
        color: '#2783fe',
        cursor: 'pointer'
      }
    }
  },
  {
    field: 'fileType',
    headerName: i18n.t('文件类型')
  },
  {
    field: 'fileSize',
    headerName: i18n.t('文件大小'),
    valueFormatter: (params) => {
      return `${Number(((params.data.fileSize / 1024) * 100) / 100).toFixed(2)}KB`
    }
  },
  {
    field: 'createUserName',
    headerName: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerName: i18n.t('创建时间')
  }
]
