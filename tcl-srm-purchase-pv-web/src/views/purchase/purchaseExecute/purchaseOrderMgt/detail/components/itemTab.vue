<template>
  <div>
    <sc-table
      ref="sctableRef"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      keep-source
      :sortable="false"
      :is-show-right-btn="false"
      :is-show-refresh-bth="false"
      @edit-closed="editComplete"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          v-show="!item.isHidden"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import VxeRemoteSearch from '@/components/VxeRemoteSearch/index.vue'
import { whetherMapList, priceUnitList } from './config/item'
import {
  closeStatusList,
  syncOaStatusList,
  warehouseStatusList,
  receiveStatusList,
  deliveryStatusList,
  urgentStatusList
} from '../../config/index'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  name: 'ItemTab',
  // eslint-disable-next-line vue/no-unused-components
  components: { ScTable, VxeRemoteSearch },
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    },
    factoryList: {
      type: Array,
      default: () => []
    },
    editableType: {
      type: Number,
      default: 0
    },
    isSup: {
      type: Boolean,
      default: false
    },
    editable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      taxRateNameList: [],
      purchaseGroupList: [],
      whetherMapList,
      priceUnitList
    }
  },
  computed: {
    pageType() {
      return this.$route.query?.type
    },
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'itemNo',
          title: this.$t('行号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'siteCode',
          title: this.$t('工厂'),
          minWidth: 180,
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [row.siteCode ? <div>{row.siteCode + '-' + row.siteName}</div> : null]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.siteCode}
                  options={this.factoryList}
                  placeholder={this.$t('请选择')}
                  disabled={
                    this.isSup ||
                    row.deliveryCompletedFlag === 1 ||
                    row.closeStatus === 1 ||
                    this.editableType === 2 ||
                    this.editableType === 3
                  }
                  filterable
                  transfer
                  onChange={() => {
                    let selectedItem = this.factoryList.find((v) => v.value === row.siteCode)
                    row.siteName = selectedItem?.siteName
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'itemCode',
          title: this.$t('物料编码'),
          minWidth: 180,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.itemCode}
                  disabled={
                    this.editableType === 1 ||
                    this.editableType === 2 ||
                    this.editableType === 3 ||
                    this.isSup ||
                    row.deliveryCompletedFlag === 1 ||
                    row.closeStatus === 1 ||
                    row.siteCode ? false : true
                  }
                  fields={{ text: 'itemName', value: 'itemCode' }}
                  placeholder={this.$t('请选择')}
                  request-info={{
                    urlPre: 'masterData',
                    url: 'getItemPage',
                    searchFields: ['itemCode', 'itemName'],
                    params: {
                      defaultRules: [
                        {
                          field: 'organizationCode',
                          operator: 'equal',
                          value: row.siteCode
                        }
                      ],
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  link-field-value={row.siteCode}
                  onChange={(e) => {
                    row.itemCode = e?.itemCode || null
                    row.itemName = e?.itemName || null
                    row.specificationModel = e?.itemDescription || null
                    row.categoryCode = e?.categoryCode || null
                    row.categoryName = e?.categoryName || null
                    row.baseUnitCode = e?.baseMeasureUnitCode || null
                    row.baseUnitName = e?.baseMeasureUnitName || null
                    if (e?.categoryResponse) {
                      row.largeCategoryCode = e?.categoryResponse?.businessTypeCode || null
                      row.largeCategoryName = e?.categoryResponse?.businessTypeName || null
                      row.mediumCategoryCode = e?.categoryResponse?.categoryTypeCode || null
                      row.mediumCategoryName = e?.categoryResponse?.categoryTypeName || null
                      row.smallCategoryCode = e?.categoryResponse?.categoryCode || null
                      row.smallCategoryName = e?.categoryResponse?.categoryName || null
                    }
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'itemName',
          title: this.$t('物料名称'),
          minWidth: 180
        },
        {
          field: 'brand',
          title: this.$t('品牌'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.brand}
                  disabled={
                    this.isSup ||
                    row.deliveryCompletedFlag === 1 ||
                    row.closeStatus === 1 ||
                    this.editableType === 2 ||
                    this.editableType === 3
                  }
                  placeholder={this.$t('请输入')}
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'specificationModel',
          title: this.$t('规格型号'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.specificationModel}
                  disabled={
                    this.isSup ||
                    row.deliveryCompletedFlag === 1 ||
                    row.closeStatus === 1 ||
                    this.editableType === 2 ||
                    this.editableType === 3
                  }
                  placeholder={this.$t('请输入')}
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'quantity',
          title: this.$t('订单数量'),
          minWidth: 150,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.quantity}
                  disabled={this.isSup || row.deliveryCompletedFlag === 1 || row.closeStatus === 1}
                  placeholder={this.$t('请输入')}
                  min={row.deliveryQty ? row.deliveryQty : '0'}
                  transfer
                  onChange={() => {
                    if (row.taxedPrice && row.taxRate) {
                      // 未税单价
                      row.untaxedPrice =
                        (Number(row.taxedPrice) / (1 + Number(row.taxRate))).toFixed(2) || 0
                    }
                    if (row.taxRate && row.taxedPrice) {
                      row.untaxedPriceTotal =
                        (Number(row.untaxedPrice) * Number(row.quantity)).toFixed(2) || 0
                      row.taxedPriceTotal =
                        (Number(row.taxedPrice) * Number(row.quantity)).toFixed(2) || 0
                    }
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'preDeliveryQty',
          title: this.$t('待发货数量')
        },
        {
          field: 'transitQty',
          title: this.$t('在途数量')
        },
        {
          field: 'warehouseQty',
          title: this.$t('已入库数量')
        },
        {
          field: 'deliveryQty',
          title: this.$t('已发货数量')
        },
        {
          field: 'receiveQty',
          title: this.$t('已收货数量')
        },
        {
          field: 'taxCode',
          title: this.$t('税率(%)'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [row.taxCode ? <div>{row.taxCode + '-' + row.taxName}</div> : null]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.taxCode}
                  disabled={
                    this.isSup ||
                    row.deliveryCompletedFlag === 1 ||
                    row.closeStatus === 1 ||
                    this.editableType === 3
                  }
                  options={this.taxRateNameList}
                  placeholder={this.$t('请选择')}
                  filterable
                  transfer
                  onChange={() => {
                    let selectedItem = this.taxRateNameList.find((v) => v.value === row.taxCode)
                    row.taxName = selectedItem?.taxItemName
                    row.taxRate = selectedItem?.taxRate
                    if (row.taxedPrice) {
                      row.untaxedPrice =
                        (Number(row.taxedPrice) / (1 + Number(row.taxRate))).toFixed(2) || 0
                    }
                    if (row.taxedPrice && row.quantity) {
                      row.untaxedPriceTotal =
                        (Number(row.untaxedPrice) * Number(row.quantity)).toFixed(2) || 0
                      row.taxedPriceTotal =
                        (Number(row.taxedPrice) * Number(row.quantity)).toFixed(2) || 0
                    }
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'untaxedPrice',
          title: this.$t('不含税单价')
        },
        {
          field: 'taxedPrice',
          title: this.$t('含税单价'),
          minWidth: 150,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.taxedPrice}
                  disabled={
                    this.editableType === 1 ||
                    this.editableType === 2 ||
                    this.editableType === 3 ||
                    this.isSup ||
                    row.deliveryCompletedFlag === 1 ||
                    row.closeStatus === 1 ||
                    row.deliveryQty > 0
                  }
                  placeholder={this.$t('请输入')}
                  min='0'
                  transfer
                  onChange={() => {
                    if (row.taxRate) {
                      // 未税单价
                      row.untaxedPrice =
                        (Number(row.taxedPrice) / (1 + Number(row.taxRate))).toFixed(2) || 0
                    }
                    if (row.taxRate && row.quantity) {
                      row.untaxedPriceTotal =
                        (Number(row.untaxedPrice) * Number(row.quantity)).toFixed(2) || 0
                      row.taxedPriceTotal =
                        (Number(row.taxedPrice) * Number(row.quantity)).toFixed(2) || 0
                    }
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'untaxedPriceTotal',
          title: this.$t('不含税总价')
        },
        {
          field: 'taxedPriceTotal',
          title: this.$t('含税总价')
        },
        {
          field: 'categoryCode',
          title: this.$t('品类'),
          slots: {
            default: ({ row }) => {
              return [
                row.categoryCode ? <div> {row.categoryCode + '-' + row.categoryName}</div> : null
              ]
            }
          }
        },
        {
          field: 'largeCategoryCode',
          title: this.$t('大类编码')
        },
        {
          field: 'mediumCategoryCode',
          title: this.$t('中类编码')
        },
        {
          field: 'smallCategoryCode',
          title: this.$t('小类编码')
        },
        {
          field: 'urgentStatus',
          title: this.$t('加急状态'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              let selectedItem = urgentStatusList.find((v) => v.value === row.urgentStatus)
              return [selectedItem ? <div>{selectedItem.text}</div> : null]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.urgentStatus}
                  disabled={
                    this.isSup ||
                    row.deliveryCompletedFlag === 1 ||
                    row.closeStatus === 1 ||
                    this.editableType === 2 ||
                    this.editableType === 3
                  }
                  options={urgentStatusList}
                  placeholder={this.$t('请选择')}
                  filterable
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'urgentDate',
          title: this.$t('加急日期')
        },
        {
          field: 'closeStatus',
          title: this.$t('关闭状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = closeStatusList.find((item) => item.value === row.closeStatus)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'requiredDeliveryDate',
          title: this.$t('要求交期'),
          minWidth: 150,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              const dateDisabled = (params) => {
                const { date } = params
                return date < new Date()
              }
              return [
                <vxe-input
                  type={'date'}
                  v-model={row.requiredDeliveryDate}
                  placeholder={this.$t('请选择')}
                  transfer
                  disabled={this.isSup || row.deliveryCompletedFlag === 1 || row.closeStatus === 1}
                  label-format='yyyy-MM-dd'
                  value-format='yyyy-MM-dd'
                  disabled-method={dateDisabled}
                />
              ]
            }
          }
        },
        // {
        //   field: 'purchaseGroupCode',
        //   title: this.$t('采购组'),
        //   minWidth: 120,
        //   editRender: {},
        //   slots: {
        //     default: ({ row }) => {
        //       return [
        //         row.purchaseGroupCode ? (
        //           <span>{row.purchaseGroupCode + '-' + row.purchaseGroupName}</span>
        //         ) : null
        //       ]
        //     },
        //     edit: ({ row }) => {
        //       return [
        //         <vxe-select
        //           v-model={row.purchaseGroupCode}
        //           disabled={
        //             this.isSup ||
        //             row.deliveryCompletedFlag === 1 ||
        //             row.closeStatus === 1 ||
        //             this.editableType === 2 ||
        //             this.editableType === 3
        //           }
        //           options={this.purchaseGroupList}
        //           placeholder={this.$t('请选择')}
        //           filterable
        //           transfer
        //           onChange={() => {
        //             let selectedItem = this.purchaseGroupList.find(
        //               (v) => v.value === row.purchaseGroupCode
        //             )
        //             row.purchaseGroupName = selectedItem?.groupName
        //           }}
        //         />
        //       ]
        //     }
        //   }
        // },
        {
          field: 'baseUnitCode',
          title: this.$t('基本单位'),
          slots: {
            default: ({ row }) => {
              return [
                row.baseUnitCode ? <div>{row.baseUnitCode + '-' + row.baseUnitName}</div> : null
              ]
            }
          }
        },
        {
          field: 'srmSignFlag',
          title: this.$t('是否SRM签收'),
          minWidth: 180,
          slots: {
            default: ({ row }) => {
              let selectedItem = this.whetherMapList.find((v) => v.value === row.srmSignFlag)
              return [selectedItem ? <div>{selectedItem.label}</div> : null]
            }
          }
        },
        {
          field: 'warehouseCode',
          title: this.$t('入库仓库'),
          minWidth: 180,
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [
                row.warehouseCode ? <div> {row.warehouseCode + '-' + row.warehouseName}</div> : null
              ]
            },
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.warehouseCode}
                  disabled={this.isSup || row.deliveryCompletedFlag === 1 || row.closeStatus === 1}
                  placeholder={this.$t('请选择')}
                  fields={{ text: 'warehouseName', value: 'warehouseCode' }}
                  request-info={{
                    urlPre: 'purchaseOrderMgt',
                    url: 'queryPvWarehouseList',
                    searchKey: 'fuzzyParam',
                    params: {
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  onChange={(e) => {
                    row.warehouseName = e?.warehouseName || null
                    row.consignee = e?.contactor || null
                    row.contactPhone = e?.contactPhone || null
                    row.deliveryAddress = e?.addr || null
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'consignee',
          title: this.$t('收货人'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.consignee}
                  disabled={this.isSup || row.deliveryCompletedFlag === 1 || row.closeStatus === 1}
                  placeholder={this.$t('请输入')}
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'contactPhone',
          title: this.$t('联系方式'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.contactPhone}
                  disabled={this.isSup || row.deliveryCompletedFlag === 1 || row.closeStatus === 1}
                  placeholder={this.$t('请输入')}
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'deliveryAddress',
          title: this.$t('收货地址'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.deliveryAddress}
                  disabled={this.isSup || row.deliveryCompletedFlag === 1 || row.closeStatus === 1}
                  placeholder={this.$t('请输入')}
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'promiseTime',
          title: this.$t('承诺日期'),
          minWidth: 150,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              const dateDisabled = (params) => {
                const { date } = params
                return date < new Date()
              }
              return [
                <vxe-input
                  type={'date'}
                  v-model={row.promiseTime}
                  disabled={
                    (this.isSup && this.dataInfo.orderStatus !== 1) ||
                    row.deliveryCompletedFlag === 1 ||
                    row.closeStatus === 1 ||
                    this.editableType === 2 ||
                    this.editableType === 3
                  }
                  placeholder={this.$t('请选择')}
                  transfer
                  label-format='yyyy-MM-dd'
                  value-format='yyyy-MM-dd'
                  disabled-method={dateDisabled}
                />
              ]
            }
          }
        },
        {
          field: 'promiseQty',
          title: this.$t('承诺数量'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.promiseQty}
                  disabled={
                    (this.isSup && this.dataInfo.orderStatus !== 1) ||
                    row.deliveryCompletedFlag === 1 ||
                    row.closeStatus === 1 ||
                    this.editableType === 2 ||
                    this.editableType === 3
                  }
                  placeholder={this.$t('请输入')}
                  min='0'
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'supplierRemark',
          title: this.$t('供方反馈备注'),
          minWidth: 180,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.supplierRemark}
                  disabled={!this.isSup || row.deliveryCompletedFlag === 1 || row.closeStatus === 1}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'deliveryStatus',
          title: this.$t('发货状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = deliveryStatusList.find(
                (item) => item.value === row.deliveryStatus
              )
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'receiveStatus',
          title: this.$t('收货状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = receiveStatusList.find((item) => item.value === row.receiveStatus)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'warehouseStatus',
          title: this.$t('入库状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = warehouseStatusList.find(
                (item) => item.value === row.warehouseStatus
              )
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'sourceCode',
          title: this.$t('来源单号'),
          minWidth: 140,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.sourceCode}
                  disabled={
                    this.editableType === 2 ||
                    this.editableType === 3 ||
                    this.isSup ||
                    row.deliveryCompletedFlag === 1 ||
                    row.closeStatus === 1
                  }
                  placeholder={this.$t('请输入')}
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'sourceLineNo',
          title: this.$t('来源单行号'),
          minWidth: 140,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.sourceLineNo}
                  disabled={
                    this.editableType === 2 ||
                    this.editableType === 3 ||
                    this.isSup ||
                    row.deliveryCompletedFlag === 1 ||
                    row.closeStatus === 1
                  }
                  placeholder={this.$t('请输入')}
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'projectName',
          title: this.$t('项目名称'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.projectName}
                  disabled={
                    this.editableType === 2 ||
                    this.editableType === 3 ||
                    this.isSup ||
                    row.deliveryCompletedFlag === 1 ||
                    row.closeStatus === 1
                  }
                  placeholder={this.$t('请输入')}
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'projectManager',
          title: this.$t('项目经理'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.projectManager}
                  disabled={
                    this.editableType === 2 ||
                    this.editableType === 3 ||
                    this.isSup ||
                    row.deliveryCompletedFlag === 1 ||
                    row.closeStatus === 1
                  }
                  placeholder={this.$t('请输入')}
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'deliveryCompletedFlag',
          title: this.$t('交货已完成标识'),
          minWidth: 150,
          slots: {
            default: ({ row }) => {
              const selectItem = this.whetherMapList.find(
                (item) => item.value === row.deliveryCompletedFlag
              )
              const name = selectItem?.text
              return [<div>{name}</div>]
            }
          }
        },
        {
          field: 'syncErpStatus',
          title: this.$t('同步极光状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = syncOaStatusList.find((item) => item.value === row.syncErpStatus)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'syncErpDesc',
          title: this.$t('同步极光接口返回'),
          minWidth: 140
        },
        {
          field: 'syncOaStatus',
          title: this.$t('同步OA状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = syncOaStatusList.find((item) => item.value === row.syncOaStatus)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'syncOaDesc',
          title: this.$t('同步OA接口返回'),
          minWidth: 140
        },
        {
          field: 'purchaseRemark',
          title: this.$t('采方备注'),
          minWidth: 180,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.purchaseRemark}
                  disabled={this.isSup || row.deliveryCompletedFlag === 1 || row.closeStatus === 1}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        }
      ]
    },
    editConfig() {
      return {
        enabled: this.editable || this.isSup,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    editRules() {
      return {
        siteCode: [{ required: true, message: this.$t('必填') }],
        itemCode: [{ required: true, message: this.$t('必填') }],
        quantity: [{ required: true, message: this.$t('必填') }],
        taxCode: [{ required: true, message: this.$t('必填') }],
        taxedPrice: [{ required: true, message: this.$t('必填') }],
        requiredDeliveryDate: [{ required: true, message: this.$t('必填') }],
        baseUnitCode: [{ required: true, message: this.$t('必填') }],
        warehouseCode: [
          { required: this.dataInfo?.deliveryDriverType !== 2, message: this.$t('必填') }
        ]
      }
    },
    toolbar() {
      let btns = [{ code: 'export', name: this.$t('导出'), status: 'info', loading: false }]
      if (!this.$route.path?.includes('-sup')) {
        btns = [
          // {
          //   code: 'select',
          //   name: this.$t('批量选择物料'),
          //   status: 'info',
          //   loading: false
          // },
          // {
          //   code: 'execute',
          //   name: this.$t('执行交货已完成'),
          //   status: 'info',
          //   loading: false,
          //   isHidden: this.isSup || !this.editable
          // },
          {
            code: 'close',
            name: this.$t('关闭'),
            status: 'info',
            loading: false,
            isHidden: this.editableType === 2 || this.isSup || !this.editable
          },
          {
            code: 'add',
            name: this.$t('新增'),
            status: 'info',
            loading: false,
            isHidden: this.editableType === 2 || this.isSup || !this.editable
          },
          {
            code: 'delete',
            name: this.$t('删除'),
            status: 'info',
            loading: false,
            isHidden:
              this.editableType === 1 || this.editableType === 2 || this.isSup || !this.editable
          },
          {
            code: 'import',
            name: this.$t('导入'),
            status: 'info',
            loading: false,
            isHidden: this.isSup || !this.editable
          },
          { code: 'export', name: this.$t('导出'), status: 'info', loading: false }
        ]
      }
      return btns
    }
  },
  watch: {
    'dataInfo.detailList': {
      handler() {
        this.tableData = this.dataInfo?.detailList || []
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.getTaxList()
    this.getPurchaseGroupList()
    if (this.pageType !== 'add') {
      this.initTableData()
    }
  },
  methods: {
    async getTaxList() {
      const res = await this.$API.masterData.queryAllTaxItem()
      this.taxRateNameList =
        res?.data?.map((i) => {
          return {
            ...i,
            label: i.taxItemCode + '-' + i.taxItemName,
            value: i.taxItemCode
          }
        }) || []
    },
    async getPurchaseGroupList() {
      const res = await this.$API.masterData.getbussinessGroup()
      this.purchaseGroupList =
        res?.data?.map((i) => {
          return {
            ...i,
            label: i.groupCode + '-' + i.groupName,
            value: i.groupCode
          }
        }) || []
    },
    // 初始化获取数据
    async initTableData() {
      this.tableData = this.dataInfo?.detailList || []
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['delete', 'execute', 'close']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const ids = []
      for (let index = 0; index < selectedRecords.length; index++) {
        const item = selectedRecords[index]
        if (e.code === 'execute' && ![1].includes(item.supplierConfirmStatus)) {
          this.$toast({
            content: this.$t('仅支持供应商已确认状态的数据！'),
            type: 'warning'
          })
          return
        }
        ids.push(item.id)
      }
      switch (e.code) {
        // case 'select':
        //   this.handleSelect()
        //   break
        case 'execute':
          this.handleExecute(e, ids)
          break
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          this.handleDelete()
          break
        case 'close':
          this.handleClose(e, ids)
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleExecute(e, ids) {
      // 执行交货已完成
      e.loading = true
      this.$API.purchaseOrderMgt
        .executeDeliveryFinish({ ids })
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('操作成功！'), type: 'success' })
            this.$emit('updateItemList')
          }
        })
        .finally(() => {
          e.loading = false
        })
    },
    handleClose(e, ids) {
      // 执行交货已完成
      e.loading = true
      this.$API.purchaseOrderMgt
        .closeDetail({ ids })
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('操作成功！'), type: 'success' })
            this.$emit('updateItemList')
          }
        })
        .finally(() => {
          e.loading = false
        })
    },
    handleSelect() {
      this.$dialog({
        modal: () => import('./selectItemGrid'),
        data: {
          title: this.$t('选择物料')
        },
        success: (data) => {
          if (data.length) {
            data.forEach((item) => {
              this.handleAdd({
                itemCode: item.itemCode,
                itemName: item.itemName
              })
            })
          }
        }
      })
    },
    handleAdd(item = {}) {
      this.tableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.tableRef.setEditRow(currentViewRecords[0])
      })
    },
    editComplete(args) {
      // const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        //1、 校验必填
        // this.tableRef.validate([row]).then((valid) => {
        //   if (valid) {
        //     this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
        //     return
        //   }
        //
        // })
        const currentViewRecords = this.tableRef.getTableData().visibleData
        this.$emit('itemDataChange', currentViewRecords)
      }
    },
    handleDelete() {
      this.tableRef.removeCheckboxRow()
      const currentViewRecords = this.tableRef.getTableData().visibleData
      this.$emit('itemDataChange', currentViewRecords)
    },
    handleImport() {
      const currentViewRecords = this.tableRef.getTableData().visibleData
      if (currentViewRecords.some((i) => i.id?.includes('row_'))) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t(`存在未保存的行数据将会被覆盖，是否继续导入？`)
          },
          success: async () => {
            this.importAction()
          }
        })
        return
      }
      this.importAction()
    },
    importAction() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.purchaseOrderMgt.importDetail,
          downloadTemplateApi: this.$API.purchaseOrderMgt.getImportTemplate,
          paramsKey: 'excel',
          asyncParams: { id: this.$route.query.id },
          downloadTemplateParams: { id: this.$route.query.id }
        },
        success: () => {
          this.$emit('updateItemList')
        }
      })
    },
    handleExport(e) {
      const params = { id: this.$route.query.id }
      e.loading = true
      this.$API.purchaseOrderMgt
        .exportDetailList(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>
