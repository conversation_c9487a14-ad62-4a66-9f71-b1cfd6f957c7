<template>
  <div class="flex-full-height">
    <CustomAgGrid
      ref="CustomAgGrid"
      :toolbar="toolbar"
      :columns="columns"
      :suppress-row-transform="true"
      :row-data="tableData"
      :animate-rows="false"
      :is-pagination="false"
      :get-row-id="getRowId"
      :row-class-rules="rowClassRules"
      :params-type="2"
      @handleClickToolbar="handleClickToolbar"
      @onGridReady="onGridReady"
      @row-selected="onRowSelected"
      @cell-value-changed="cellValueChanged"
      @cell-clicked="onCellClicked"
    >
    </CustomAgGrid>
    <!-- 附件弹窗 -->
    <uploader-dialog ref="uploaderDialog" @confirm="uploaderDialogOnConfirm"></uploader-dialog>
  </div>
</template>

<script>
import UploaderDialog from '@/components/Upload/uploaderDialog'
import CustomAgGrid from '@/components/CustomAgGrid'
import { columnData, toolBar } from './config/attachment'
import { download } from '@/utils/utils'
export default {
  name: 'AttachmentTab',
  components: {
    CustomAgGrid,
    UploaderDialog
  },
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    },
    editable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableData: [],
      columns: columnData,
      toolbar: []
    }
  },
  computed: {
    rowClassRules() {
      return {
        errorBg: function (params) {
          return params.data.errorMsg
        }
      }
    }
  },
  watch: {
    'dataInfo.orderStatus': {
      handler(v) {
        if (v || v === 0) {
          this.toolbar = toolBar(this.editable, this.$route)
        }
      },
      deep: true,
      immediate: true
    },
    'dataInfo.fileList': {
      handler() {
        this.tableData =
          this.dataInfo?.fileList?.map((i) => {
            return {
              ...i,
              customId: i.id
            }
          }) || []
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    if (this.$route.query.type !== 'add') {
      this.initTableData()
    }
  },
  methods: {
    /**
     * ---------------------------ag相关---------------------------
     */
    // ag - 初始监听
    onGridReady(params) {
      this.agGrid = params
    },
    // ag - 监听select选择框
    onRowSelected() {},
    // ag - 编辑框监听
    cellValueChanged(params) {
      if (params.oldValue === params.newValue) return
    },
    onCellClicked(params) {
      const { colDef, data } = params
      if (colDef.field === 'fileName') {
        this.handleExport(data.fileId, data.fileName)
      }
    },
    // ag - 获取rowId
    getRowId(params) {
      return params.data.id ? params.data.id : params.data?.customId
    },
    /**
     * ---------------------------初始化---------------------------
     */
    // 初始化获取数据
    async initTableData() {
      this.tableData =
        this.dataInfo?.fileList?.map((i) => {
          return {
            ...i,
            customId: i.id
          }
        }) || []
    },
    // btn - 刷新
    refresh() {
      this.initTableData()
    },
    /**
     * ---------------------------事件操作---------------------------
     */
    // 点击按钮栏
    handleClickToolbar(e) {
      const selectedRows = this.agGrid.api.getSelectedRows()
      if (e?.toolbar?.id !== 'upload' && !selectedRows.length) {
        this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
        return
      }
      switch (e?.toolbar?.id) {
        case 'delete':
          this.handleDelete(selectedRows)
          break
        case 'upload':
          console.log('导入')
          const dialogParams = {
            // fileData: cloneDeep(e.value),
            isView: false,
            title: this.$t('附件')
          }
          this.$refs.uploaderDialog.dialogInit(dialogParams)
          // this.handleImport()
          break
        case 'download':
          selectedRows.forEach((i) => {
            this.handleExport(i.fileId, i.fileName)
          })
          break
        default:
          break
      }
    },
    // 附件弹框文件点击确认
    uploaderDialogOnConfirm(args) {
      console.log('附件弹窗文件点击确认', args)
      const { fileList } = args
      fileList?.forEach((i) => {
        const obj = {
          customId: i.id,
          fileId: i.id, //附件id
          fileName: i.fileName, //附件名称
          fileType: i.fileType, //附件类型
          fileSize: i.fileSize, //文件大小
          url: i.url //文件路径
        }
        this.tableData.push(obj)
      })
      this.$emit('itemDataChange', this.tableData)
    },
    // 删除
    handleDelete(list) {
      if (list.length !== 1) {
        this.$toast({ content: this.$t('请仅选择一条数据！'), type: 'warning' })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: async () => {
          const deleteCustomIds = list.map((item) => item.customId)
          if (deleteCustomIds[0]) {
            const res = await this.$API.purchaseOrderMgt.deleteFile({
              id: list[0].id
            })
            if (res.code === 200) {
              this.tableData = this.tableData.filter(
                (item) => !deleteCustomIds.includes(item.customId)
              )
              this.$emit('itemDataChange', this.tableData)
            }
          } else {
            this.tableData = this.tableData.filter(
              (item) => !deleteCustomIds.includes(item.customId)
            )
            this.$emit('itemDataChange', this.tableData)
          }
        }
      })
    },
    // 导出
    async handleExport(id, fileName) {
      this.$API.fileService
        .downloadPrivateFile({
          id
        })
        .then((res) => {
          download({ fileName, blob: res.data })
        })
    }
  }
}
</script>
<style lang="scss"></style>
<style lang="scss" scoped>
.mt-pagertemplate {
  margin-bottom: 0px !important;
}
</style>
