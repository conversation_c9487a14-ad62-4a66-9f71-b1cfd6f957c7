import { i18n } from '@/main'
// 订单状态
export const orderStatusList = [
  { value: 0, text: i18n.t('草稿') },
  { value: 1, text: i18n.t('待供应商确认') },
  { value: 2, text: i18n.t('供应商已拒绝') },
  { value: 3, text: i18n.t('待采购确认') },
  { value: 4, text: i18n.t('采购已拒绝') },
  { value: 5, text: i18n.t('双方已确认') },
  { value: 6, text: i18n.t('已完成') },
  { value: 7, text: i18n.t('已关闭') },
  { value: 8, text: i18n.t('未确认') },
  { value: 9, text: i18n.t('已确认') }
]
// 订单状态
export const orderStatusSupList = [
  { value: 1, text: i18n.t('待供应商确认') },
  { value: 2, text: i18n.t('供应商已拒绝') },
  { value: 3, text: i18n.t('待采购确认') },
  { value: 4, text: i18n.t('采购已拒绝') },
  { value: 5, text: i18n.t('双方已确认') },
  { value: 6, text: i18n.t('已完成') },
  { value: 7, text: i18n.t('已关闭') },
  { value: 9, text: i18n.t('已确认') }
]
// 业务类型
export const businessTypeList = [
  { value: 1, text: i18n.t('户用业务') },
  { value: 2, text: i18n.t('商用业务') },
  { value: 3, text: i18n.t('海外业务') }
]
// 发货驱动类型
export const deliveryDriverTypeList = [
  { value: 1, text: i18n.t('采购订单') },
  { value: 2, text: i18n.t('交货计划') },
  { value: 3, text: i18n.t('VMI采购'), disabled: true }
]
// 发布状态
export const publishStatusList = [
  { value: 0, text: i18n.t('未发布') },
  { value: 1, text: i18n.t('已发布') }
]
// 是否用印签署
export const sealSignFlagList = [
  { value: 0, text: i18n.t('否') },
  { value: 1, text: i18n.t('是') }
]
// 加急状态
export const urgentStatusList = [
  { value: 0, text: i18n.t('未加急'), label: i18n.t('未加急') },
  { value: 1, text: i18n.t('已加急'), label: i18n.t('已加急') }
]
// 关闭状态
export const closeStatusList = [
  { value: 0, text: i18n.t('未关闭') },
  { value: 1, text: i18n.t('已关闭') }
]
// 发货状态
export const deliveryStatusList = [
  { value: 0, text: i18n.t('未发货') },
  { value: 1, text: i18n.t('部分发货') },
  { value: 2, text: i18n.t('全部发货') }
]
// 收货状态
export const receiveStatusList = [
  { value: 0, text: i18n.t('未收货') },
  { value: 1, text: i18n.t('部分收货') },
  { value: 2, text: i18n.t('全部收货') }
]
// 入库状态
export const warehouseStatusList = [
  { value: 0, text: i18n.t('未入库') },
  { value: 1, text: i18n.t('部分入库') },
  { value: 2, text: i18n.t('全部入库') }
]
// 订单来源
export const dataSourceList = [
  { value: 0, text: i18n.t('手工新增') },
  { value: 1, text: i18n.t('外部SAP') },
  { value: 2, text: i18n.t('合同转换') },
  { value: 3, text: i18n.t('采购申请转换') },
  { value: 4, text: i18n.t('商城申请转换') },
  { value: 5, text: i18n.t('VMI出库记录转化') }
]
// 审批状态
export const approvalStatusList = [
  { value: 0, text: i18n.t('未审批') },
  { value: 1, text: i18n.t('审批中') },
  { value: 2, text: i18n.t('审批通过') },
  { value: 3, text: i18n.t('审批拒绝') },
  { value: 4, text: i18n.t('无需审批') }
]
// 签署状态
export const signStatusList = [
  { value: 0, text: i18n.t('未签署') },
  { value: 1, text: i18n.t('无需签署') },
  { value: 2, text: i18n.t('签署中') },
  { value: 3, text: i18n.t('已完成签署') }
]

// 同步OA状态
export const syncOaStatusList = [
  { value: 0, text: i18n.t('未同步') },
  { value: 1, text: i18n.t('同步成功') },
  { value: 2, text: i18n.t('同步失败') }
]
// 是否
export const yesOrNoList = [
  { value: 0, text: i18n.t('否') },
  { value: 1, text: i18n.t('是') }
]
// 操作符
export const symbolOptions = [
  { value: 'gt', text: '>' },
  { value: 'ge', text: '>=' },
  { value: 'eq', text: '=' },
  { value: 'ne', text: '!=' },
  { value: 'lt', text: '<' },
  { value: 'le', text: '<=' }
]

// 列表试图-操作按钮
export const listToolbar = [
  { code: 'add', name: i18n.t('新增'), status: 'info' },
  { code: 'edit', name: i18n.t('编辑'), status: 'info' },
  { code: 'deleteList', name: i18n.t('删除'), status: 'info' },
  { code: 'publishList', name: i18n.t('发布'), status: 'info' },
  { code: 'confirmList', name: i18n.t('确认'), status: 'info' },
  { code: 'submitOaList', name: i18n.t('提交OA审批'), status: 'info' },
  { code: 'checkOaList', name: i18n.t('查看OA审批'), status: 'info' },
  // { code: 'closeDetailList', name: i18n.t('关闭'), status: 'info' },
  { code: 'syncList', name: i18n.t('同步极光系统'), status: 'info' },
  { code: 'export', name: i18n.t('导出'), status: 'info' },
  { code: 'downloadSealFile', name: i18n.t('下载用印文件'), status: 'info' }
]

// 明细视图-操作按钮
export const detailToolbar = [
  { code: 'closeDetailList', name: i18n.t('关闭'), status: 'info' },
  { code: 'viewOA', name: i18n.t('查看OA审批'), status: 'info' },
  { code: 'export', name: i18n.t('导出'), status: 'info' },
  { code: 'execute', name: i18n.t('执行交货已完成'), status: 'info' }
]
