<template>
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="quotaApplicationNo" :label="$t('配额申请单号')" label-style="top">
          <mt-input
            v-model="searchFormModel.quotaApplicationNo"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.status"
            css-class="rule-element"
            :data-source="statusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="siteCode" :label="$t('工厂代码')" label-style="top">
          <mt-input
            v-model="searchFormModel.siteCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="siteName" :label="$t('工厂名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.siteName"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="categoryCode" :label="$t('品类编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.categoryCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="categoryName" :label="$t('品类名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.categoryName"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.itemCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.itemName"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.supplierCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.supplierName"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="effectiveDate" :label="$t('有效日期')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.effectiveDate"
            :allow-edit="false"
            :placeholder="$t('请选择')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'effectiveDate')"
          />
        </mt-form-item>
        <mt-form-item prop="expirationDate" :label="$t('失效日期')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.expirationDate"
            :allow-edit="false"
            :placeholder="$t('请选择')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'expirationDate')"
          />
        </mt-form-item>
        <!-- <mt-form-item prop="specificationModel" :label="$t('规格型号')" label-style="top">
          <mt-input
            v-model="searchFormModel.specificationModel"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item> -->
        <mt-form-item prop="remarks" :label="$t('备注')" label-style="top">
          <mt-input
            v-model="searchFormModel.remarks"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :allow-edit="false"
            :placeholder="$t('请选择')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'createTime')"
          />
        </mt-form-item>
        <mt-form-item prop="updateTime" :label="$t('最后更新时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.updateTime"
            :allow-edit="false"
            :placeholder="$t('请选择')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'updateTime')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      grid-id="bfb847ee-73b5-6c0b-bd5b-8771cfe0a072"
      :loading="loading"
      :edit-config="editConfig"
      :edit-rules="editRules"
      keep-source
      :sortable="true"
      :is-show-refresh-bth="true"
      :columns="listColumns"
      :table-data="tableData"
      @refresh="handleSearch"
      @edit-closed="editComplete"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import VxeRemoteSearch from '@/components/VxeRemoteSearch/index.vue'
import { listToolbar, statusList } from './config/index'
import XEUtils from 'xe-utils'

export default {
  components: {
    ScTable,
    CollapseSearch,
    VxeRemoteSearch
  },
  mixins: [mixin],
  data() {
    return {
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 20,
        pageSizes: [20, 50, 200, 500, 1000, 2000]
      },
      pageInfo: {
        size: 20,
        current: 1
      },
      editRules: {
        siteCode: [{ required: true, message: this.$t('必填') }],
        categoryCode: [{ required: true, message: this.$t('必填') }],
        itemCode: [{ required: true, message: this.$t('必填') }],
        supplierCode: [{ required: true, message: this.$t('必填') }],
        powerKw: [{ required: true, message: this.$t('必填') }],
        quotationRatio: [{ required: true, message: this.$t('必填') }],
        validityDate: [{ required: true, message: this.$t('必填') }],
        expireDate: [{ required: true, message: this.$t('必填') }],
        remark: [{ required: true, message: this.$t('必填') }]
      },
      statusList,
      toolbar: [],
      searchFormModel: {},
      tableData: [],
      loading: false,
      type: 'list',
      uploadFileList: [], // 上传的附件
      factoryList: [], // 上传的附件
      isEdit: false
    }
  },
  computed: {
    editConfig() {
      return {
        enabled: false,
        trigger: 'dblclick',
        mode: 'row',
        showStatus: true
      }
    },
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    // 列配置
    listColumns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        // {
        //   width: 70,
        //   field: 'index',
        //   title: this.$t('序号')
        // },
        {
          field: 'quotaApplicationNo',
          title: this.$t('配额申请单号'),
          minWidth: 180
        },
        {
          field: 'status',
          title: this.$t('状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = statusList.find((item) => item.value === row.status)
              const name = selectItem?.text
              return [<div>{name}</div>]
            }
          }
        },
        {
          field: 'siteCode',
          title: this.$t('工厂代码'),
          minWidth: 140,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.siteCode}
                  options={this.factoryList}
                  placeholder={this.$t('请选择')}
                  clearable
                  filterable
                  transfer
                  onChange={() => {
                    let selectedItem = this.factoryList.find((v) => v.value === row.siteCode)
                    row.siteName = selectedItem?.siteName
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'siteName',
          title: this.$t('工厂名称'),
          minWidth: 140
        },
        {
          field: 'categoryCode',
          title: this.$t('品类编码'),
          minWidth: 140,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.categoryCode}
                  placeholder={this.$t('请选择')}
                  fields={{ text: 'categoryName', value: 'categoryCode' }}
                  request-info={{
                    urlPre: 'masterData',
                    url: 'pageCategoryApi',
                    searchFields: ['categoryCode', 'categoryName'],
                    params: {
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  onChange={(e) => {
                    row.categoryName = e?.categoryName || null
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'categoryName',
          title: this.$t('品类名称'),
          minWidth: 140
        },
        {
          field: 'itemCode',
          title: this.$t('物料编码'),
          minWidth: 140,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.itemCode}
                  fields={{ text: 'itemName', value: 'itemCode' }}
                  placeholder={this.$t('请选择')}
                  request-info={{
                    urlPre: 'masterData',
                    url: 'getItemPage',
                    searchFields: ['itemCode', 'itemName'],
                    params: {
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  onChange={(e) => {
                    row.itemName = e?.itemName || null
                    row.itemDescription = e?.itemDescription || null
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'itemName',
          title: this.$t('物料名称'),
          minWidth: 140
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商代码'),
          minWidth: 140,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.supplierCode}
                  placeholder={this.$t('请选择')}
                  fields={{ text: 'supplierName', value: 'supplierCode' }}
                  request-info={{
                    urlPre: 'masterData',
                    url: 'supplierDistinctPagedQueryNoScope',
                    searchFields: ['supplierCode', 'supplierName'],
                    params: {
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  onChange={(e) => {
                    row.supplierName = e?.supplierName || null
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'supplierName',
          title: this.$t('供应商名称'),
          minWidth: 140
        },
        {
          field: 'powerKw',
          title: this.$t('功率(KW)'),
          minWidth: 130,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.powerKw}
                  placeholder={this.$t('请输入')}
                  min='0'
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'quotaRatio',
          title: this.$t('配额比'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.quotaRatio}
                  placeholder={this.$t('请输入')}
                  min='0'
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'effectiveDate',
          title: this.$t('有效日期'),
          minWidth: 130,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              const dateDisabled = (params) => {
                const { date } = params
                return date < new Date()
              }
              return [
                <vxe-input
                  type={'date'}
                  v-model={row.effectiveDate}
                  placeholder={this.$t('请选择')}
                  transfer
                  clearable
                  label-format='yyyy-MM-dd'
                  value-format='yyyy-MM-dd'
                  disabled-method={dateDisabled}
                />
              ]
            }
          }
        },
        {
          field: 'expirationDate',
          title: this.$t('失效日期'),
          minWidth: 130,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              const dateDisabled = (params) => {
                const { date } = params
                return date < new Date()
              }
              return [
                <vxe-input
                  type={'date'}
                  v-model={row.expirationDate}
                  placeholder={this.$t('请选择')}
                  transfer
                  clearable
                  label-format='yyyy-MM-dd'
                  value-format='yyyy-MM-dd'
                  disabled-method={dateDisabled}
                />
              ]
            }
          }
        },
        {
          field: 'specificationModel',
          title: this.$t('规格型号'),
          minWidth: 150
        },
        {
          field: 'remarks',
          title: this.$t('备注'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.remarks}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          minWidth: 120
        },
        {
          field: 'updateUserName',
          title: this.$t('最后修改人'),
          minWidth: 130
        },
        {
          field: 'updateTime',
          title: this.$t('最后修改时间'),
          minWidth: 140
        }
      ]
    }
  },
  mounted() {
    if (!this.$route.path.includes('-sup')) {
      this.toolbar = listToolbar
    } else {
      this.toolbar = [{ code: 'export', name: this.$t('导出'), status: 'info' }]
    }
    this.getFactoryList()
    this.handleSearch()
  },
  activated() {
    this.handleSearch()
  },
  methods: {
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        if (args.$event.srcElement.innerText === this.$t('取消编辑')) {
          this.handleSearch()
          return
        }
        // 远程数据才有$event属性
        //1、 校验必填
        this.tableRef.validate([row]).then((valid) => {
          if (valid) {
            this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
            return
          }
          const params = {
            ...row,
            id: row.id.includes('row_') ? null : row.id
          }
          this.$API.quotaManagement.saveSupplierPlanLimitList(params).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t(`保存成功！`), type: 'success' })
              this.handleSearch()
            }
          })
        })
      }
    },
    getFactoryList() {
      this.$API.masterData.getSiteListByPermission().then((res) => {
        this.factoryList = res.data.map((i) => {
          return {
            ...i,
            label: `${i.siteCode}-${i.siteName}`,
            value: i.siteCode
          }
        })
      })
    },
    // 选择公司
    handleCompanyChange(e) {
      console.log('test lint')
      this.$set(this.searchFormModel, 'purchaseOrgCode', null)
      if (e.itemData.id) {
        this.getPurchaseOrgList(e.itemData.id)
      } else {
        this.purchaseOrgList = []
      }
    },
    // 选择定价对象
    handleFixPriceObjectChange(e) {
      this.$set(
        this.searchFormModel,
        'fixPriceObjectType',
        e.value ? e.itemData.sourcingObjType : null
      )
    },
    // 日期格式化
    handleDateChange(e, field) {
      const startField = field + 'Start'
      const endField = field + 'End'
      if (e.startDate) {
        const startDate = XEUtils.toDateString(e.startDate, 'yyyy-MM-dd') + ' 00:00:00'
        const endDate = XEUtils.toDateString(e.endDate, 'yyyy-MM-dd') + ' 23:59:59'
        this.searchFormModel[startField] = XEUtils.timestamp(startDate)
        this.searchFormModel[endField] = XEUtils.timestamp(endDate)
      } else {
        this.searchFormModel[startField] = null
        this.searchFormModel[endField] = null
      }
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    // 查询
    async handleSearch(type) {
      // 解决修改查询条件后，查询结果有数据但未正常显示的问题（type: pageChange/refresh）
      if (!type) {
        this.pageInfo.current = 1
        this.$refs.pageRef.jumpNum = 1
        this.$refs.pageRef.currentPage = 1
      }
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      delete params.submitTime
      this.loading = true
      const res = await this.$API.quotaManagement
        .queryList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.tableData?.forEach((item, index) => {
          item.index = index + 1
        }) || []
        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
        await this.tableRef?.clearEdit()
        this.isEdit = false
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (!['add', 'import', 'export'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      const ids = []
      for (let index = 0; index < selectedRecords.length; index++) {
        const item = selectedRecords[index]
        if (e.code === 'delete' && !['0'].includes(item.contractStatus)) {
          // 支持批量删除，仅支持删除草稿状态的数据
          this.$toast({ content: this.$t('仅支持删除草稿状态的数据！'), type: 'warning' })
          return
        }
        if (e.code === 'enable' && ['1'].includes(item.contractStatus)) {
          // 失效的均变为有效状态
          this.$toast({
            content: this.$t('仅支持生效草稿及失效状态的数据！'),
            type: 'warning'
          })
          return
        }
        if (e.code === 'disable' && !['1'].includes(item.contractStatus)) {
          // 有效的均变为失效状态，失效的数据不能再使用
          this.$toast({
            content: this.$t('仅支持失效生效状态的数据！'),
            type: 'warning'
          })
          return
        }
        ids.push(item.id)
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        // case 'edit':
        //   this.handleAdd('edit', selectedRecords)
        //   break
        case 'deleteList': // 删除
          this.handleOperate(ids, e.code)
          break
        case 'enableList': // 生效
          this.handleOperate(ids, e.code)
          break
        case 'disableList': // 失效
          this.handleOperate(ids, e.code)
          break
        case 'export':
          this.handleExport()
          break
        case 'import':
          this.handleImport()
          break
        default:
          break
      }
    },
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.quotaManagement.importList,
          downloadTemplateApi: this.$API.quotaManagement.getImportTemplate,
          paramsKey: 'excel',
          asyncParams: { id: this.$route.query.id },
          downloadTemplateParams: { id: this.$route.query.id }
        },
        success: () => {
          this.handleSearch()
        }
      })
    },
    handleExport() {
      const params = { page: { size: 9999, current: 1 }, ...this.searchFormModel } // 筛选条件
      this.$API.quotaManagement.exportList(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 点击单元格
    handleClickCellTitle(row, column) {
      if (column.field === 'contractCode') {
        this.$router.push({
          name: 'purchase-coordination-pv-detail',
          query: {
            type: 'edit',
            id: row.id,
            refreshId: Date.now()
          }
        })
      }
    },
    // 新增
    handleAdd(item = {}) {
      if (this.isEdit) {
        return false
      }
      this.tableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.tableRef.setEditRow(currentViewRecords[0])
        this.isEdit = true
      })
    },
    // 删除、提交
    handleOperate(ids, type, extraParams = {}) {
      const tipMap = {
        deleteList: this.$t('删除'),
        enableList: this.$t('生效'),
        disableList: this.$t('失效')
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认${tipMap[type]}选中的数据？`)
        },
        success: async () => {
          const res = await this.$API.quotaManagement[type]({ ids, ...extraParams })
          if (res.code === 200) {
            this.$toast({ content: this.$t(`${tipMap[type]}成功！`), type: 'success' })
            this.handleSearch()
          }
        }
      })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.handleSearch('pageChange')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.handleSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
