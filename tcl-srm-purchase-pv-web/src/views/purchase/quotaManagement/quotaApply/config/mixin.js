const addArrTextField = (arr, code, name) => {
  arr.forEach((e) => {
    e.__text = `${e[code]} - ${e[name]}`
  })
  return arr
}
/**
 * 公共接口查询
 */
export default {
  data() {
    return {
      companyList: [],
      purchaseOrgList: [],
      factoryList: [],
      sourcingExpandList: [],
      purchaserList: [],
      fixPriceObjectTypeList: [] // 定价对象
    }
  },
  mounted() {
    if (this.$route.query.type !== 'edit') {
      this.getCompanyList()
    }
  },
  methods: {
    // 获取公司下拉列表
    async getCompanyList(isInit) {
      const res = await this.$API.masterData.permissionCompanyList()
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.companyList = res.data

        if (isInit) {
          const selectItem = this.companyList?.find(
            (item) => item.orgCode === this.dataForm?.companyCode
          )
          selectItem && this.getPurchaseOrgList(selectItem.id, true)
          !this.dataForm.companyId && (this.dataForm.companyId = selectItem.id)
        }
      }
    },
    // 根据所选公司获取采购组织下拉列表
    getPurchaseOrgList(companyId, isInit) {
      if (!companyId) {
        this.purchaseOrgList = []
        return
      }
      this.$API.masterData
        .permissionOrgList({
          orgId: companyId
        })
        .then((res) => {
          res.data.forEach((item) => {
            item.text = item.organizationCode + '-' + item.organizationName
          })
          this.purchaseOrgList = res.data

          if (isInit) {
            const selectItem = this.purchaseOrgList?.find(
              (item) => item.organizationCode === this.dataForm?.purchaseOrgCode
            )
            selectItem && this.getFactoryListByCompanyAndPur(companyId, selectItem.id, true)
          }
        })
    },
    // 获取采购组织下拉列表
    getPermissionPurchaseOrgList() {
      this.$API.masterData.permissionOrgList().then((res) => {
        res.data.forEach((item) => {
          item.text = item.organizationCode + '-' + item.organizationName
        })
        this.purchaseOrgList = res.data
      })
    },
    // 根据公司id、采购组织id获取工厂下拉列表
    async getFactoryListByCompanyAndPur(companyId, purOrgId) {
      const res = await this.$API.masterData.permissionSiteList({
        companyId,
        buOrgId: purOrgId
      })
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
          item.label = item.orgCode + '-' + item.orgName
          item.value = item.orgCode
          item.siteCode = item.orgCode
          item.siteName = item.orgName
        })
        this.factoryList = addArrTextField(res.data, 'siteCode', 'siteName')
      }
    },
    // 获取采购员下拉列表
    async getPurchaserList(searchText) {
      const res = await this.$API.masterData.getCurrentTenantEmployees({ fuzzyName: searchText })
      if (res.code === 200) {
        const tmp = []
        res.data.forEach((item) => {
          tmp.push({
            ...item,
            text: `${item.employeeCode}-${item.employeeName}`
          })
        })
        this.purchaserList = tmp

        // if (this.dataForm && tmp?.length) {
        //   const { employeeName, employeeCode } = tmp[0]
        //   this.$set(this.dataForm, 'purchaserUserName', employeeName)
        //   this.$set(this.dataForm, 'purchaseUserCode', employeeCode)
        // }
      }
    }
  }
}
