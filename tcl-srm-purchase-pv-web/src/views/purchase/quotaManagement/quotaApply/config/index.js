import { i18n } from '@/main'
// 分配状态
export const statusList = [
  { value: '0', text: i18n.t('草稿') },
  { value: '1', text: i18n.t('有效') },
  { value: '2', text: i18n.t('失效') }
]
// 是否
export const yesOrNoList = [
  { value: 0, text: i18n.t('否') },
  { value: 1, text: i18n.t('是') }
]

// 列表试图-操作按钮
export const listToolbar = [
  // { code: 'add', name: i18n.t('新增'), status: 'info' },
  // { code: 'edit', name: i18n.t('编辑'), status: 'info' },
  { code: 'deleteList', name: i18n.t('删除'), status: 'info' },
  { code: 'enableList', name: i18n.t('生效'), status: 'info' },
  { code: 'disableList', name: i18n.t('失效'), status: 'info' },
  { code: 'import', name: i18n.t('导入'), status: 'info' },
  { code: 'export', name: i18n.t('导出'), status: 'info' }
]
