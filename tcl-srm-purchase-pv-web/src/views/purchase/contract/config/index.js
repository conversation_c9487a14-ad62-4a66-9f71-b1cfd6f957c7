import { i18n } from '@/main'
// 合同类型
export const contractTypeList = [
  { value: 1, text: i18n.t('年度框采合同') },
  { value: 2, text: i18n.t('技术协议') },
  { value: 3, text: i18n.t('廉洁协议') },
  { value: 4, text: i18n.t('质量协议') },
  { value: 5, text: i18n.t('补充协议') },
  { value: 6, text: i18n.t('采购订单') }
]
// 业务类型
export const businessTypeList = [
  { value: 1, text: i18n.t('户用业务') },
  { value: 2, text: i18n.t('商用业务') },
  { value: 3, text: i18n.t('海外业务') }
]
// 数据来源
export const dataSourceList = [
  { value: 0, text: i18n.t('其他') },
  { value: 1, text: i18n.t('手工新增') },
  { value: 2, text: i18n.t('寻源单') }
]
// 审批状态
export const approvalStatusList = [
  { value: 0, text: i18n.t('未审批') },
  { value: 1, text: i18n.t('审批中') },
  { value: 2, text: i18n.t('审批通过') },
  { value: 3, text: i18n.t('审批拒绝') },
  { value: 4, text: i18n.t('无需审批') }
]
// 合同状态
export const contractStatusList = [
  { value: 0, text: i18n.t('草稿') },
  { value: 1, text: i18n.t('审批中') },
  { value: 2, text: i18n.t('审批通过') },
  { value: 3, text: i18n.t('审批拒绝') },
  { value: 4, text: i18n.t('无需审批') },
  { value: 5, text: i18n.t('已归档') }
]
// 生效状态
export const statusList = [
  { value: 0, text: i18n.t('未生效') },
  { value: 1, text: i18n.t('已生效') },
  { value: 2, text: i18n.t('已作废') },
  { value: 3, text: i18n.t('已失效') }
]
// 是否失效原合同
export const relationContractInvalidFlagList = [
  { value: 0, text: i18n.t('否') },
  { value: 1, text: i18n.t('是') }
]
// 同步OA状态
export const syncOaStatusList = [
  { value: 0, text: i18n.t('未同步') },
  { value: 1, text: i18n.t('同步成功') },
  { value: 2, text: i18n.t('同步失败') }
]

// 列表试图-操作按钮
export const listToolbar = [
  { code: 'add', name: i18n.t('新增'), status: 'info' },
  { code: 'edit', name: i18n.t('编辑'), status: 'info' },
  { code: 'deleteContract', name: i18n.t('删除'), status: 'info' },
  { code: 'submit', name: i18n.t('提交OA审批'), status: 'info' },
  { code: 'viewOA', name: i18n.t('查看OA审批'), status: 'info' },
  { code: 'batchAbolish', name: i18n.t('作废'), status: 'info' },
  { code: 'archived', name: i18n.t('归档'), status: 'info' },
  { code: 'transferContract', name: i18n.t('批量转办'), status: 'info' },
  { code: 'export', name: i18n.t('导出'), status: 'info' }
]

// 明细视图-操作按钮
export const detailToolbar = [
  { code: 'transfer', name: i18n.t('转采购订单'), status: 'info' },
  { code: 'export', name: i18n.t('导出'), status: 'info' }
]
