<template>
  <mt-dialog ref="dialog" size="small" :buttons="buttons" :header="header">
    <div class="dialog-content">
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="materialCode" :label="$t('采购负责人')">
          <debounce-filter-select
            v-model="searchFormModel.purId"
            :request="getCurrentEmployees"
            :data-source="personnelDataSource"
            @change="inputPricing($event, 'purId')"
            :show-clear-button="false"
            :fields="{ text: 'text', value: 'uid' }"
            :placeholder="$t('请选择采购员')"
          ></debounce-filter-select>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import debounceFilterSelect from '@/components/debounceFilterSelect'
export default {
  components: {
    debounceFilterSelect
  },
  data() {
    return {
      searchFormModel: {
        personnel: []
      },
      personnelDataSource: [],
      data: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      selectedData: {}
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.modalData.isShow ? [] : this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.initData()
  },
  methods: {
    initData() {
      this.getCurrentEmployees({ text: '' })
    },
    confirm() {
      this.$emit('confirm-function', this.selectedData)
    },
    inputPricing(e) {
      if (e.itemData) {
        this.selectedData = e.itemData
      }
    },
    // 获取 用户列表
    getCurrentEmployees(e) {
      const { text: fuzzyName } = e
      this.personnelDataSource = []
      this.$API.masterData.getCurrentTenantEmployees({ fuzzyName }).then((res) => {
        const tmp = []
        res.data.forEach((item) => {
          tmp.push({
            ...item,
            text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`
          })
        })
        this.personnelDataSource = tmp
        if (fuzzyName == '') {
          this.searchFormModel.purId = this.personnelDataSource[0].uid
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
