<template>
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="contractCode" :label="$t('合同编号')" label-style="top">
          <mt-input
            v-model="searchFormModel.contractCode"
            :show-clear-button="true"
            :placeholder="$t('请输入合同编号')"
          />
        </mt-form-item>
        <mt-form-item prop="contractName" :label="$t('合同名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.contractName"
            :show-clear-button="true"
            :placeholder="$t('请输入合同名称')"
          />
        </mt-form-item>
        <mt-form-item prop="contractStatus" :label="$t('合同状态')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.contractStatus"
            css-class="rule-element"
            :data-source="contractStatusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择合同状态')"
          />
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('生效状态')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.status"
            css-class="rule-element"
            :data-source="statusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择生效状态')"
          />
        </mt-form-item>
        <mt-form-item prop="contractType" :label="$t('合同类型')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.contractType"
            css-class="rule-element"
            :data-source="contractTypeList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择合同类型')"
          />
        </mt-form-item>
        <mt-form-item prop="businessType" :label="$t('业务类型')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.businessType"
            css-class="rule-element"
            :data-source="businessTypeList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择业务类型')"
          />
        </mt-form-item>
        <mt-form-item prop="purchaseUserCode" :label="$t('采购负责人编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.purchaseUserCode"
            :show-clear-button="true"
            :placeholder="$t('请输入采购负责人编码')"
          />
        </mt-form-item>
        <mt-form-item prop="purchaseUserName" :label="$t('采购负责人名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.purchaseUserName"
            :show-clear-button="true"
            :placeholder="$t('请输入采购负责人名称')"
          />
        </mt-form-item>
        <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.companyCode"
            css-class="rule-element"
            :data-source="companyList"
            :fields="{ text: 'text', value: 'orgCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择公司')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierCodeStr" :label="$t('供应商编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.supplierCodeStr"
            :show-clear-button="true"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
          />
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.itemCode"
            :show-clear-button="true"
            :placeholder="$t('请输入物料编码')"
          />
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.itemName"
            :show-clear-button="true"
            :placeholder="$t('请输入物料名称')"
          />
        </mt-form-item>
        <mt-form-item prop="specificationModel" :label="$t('规格型号')" label-style="top">
          <mt-input
            v-model="searchFormModel.specificationModel"
            :show-clear-button="true"
            :placeholder="$t('请输入规格型号')"
          />
        </mt-form-item>
        <mt-form-item prop="unitCode" :label="$t('单位编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.unitCode"
            :show-clear-button="true"
            :placeholder="$t('请输入单位编码')"
          />
        </mt-form-item>
        <mt-form-item prop="unitName" :label="$t('单位名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.unitName"
            :show-clear-button="true"
            :placeholder="$t('请输入单位名称')"
          />
        </mt-form-item>
        <mt-form-item prop="purchaseApplyCode" :label="$t('采购申请单号')" label-style="top">
          <mt-input
            v-model="searchFormModel.purchaseApplyCode"
            :show-clear-button="true"
            :placeholder="$t('请输入采购申请单号')"
          />
        </mt-form-item>
        <mt-form-item prop="sourceCode" :label="$t('寻源单号')" label-style="top">
          <mt-input
            v-model="searchFormModel.sourceCode"
            :show-clear-button="true"
            :placeholder="$t('请输入寻源单号')"
          />
        </mt-form-item>
        <mt-form-item prop="brand" :label="$t('品牌')" label-style="top">
          <mt-input
            v-model="searchFormModel.brand"
            :show-clear-button="true"
            :placeholder="$t('请输入品牌')"
          />
        </mt-form-item>
        <mt-form-item prop="projectName" :label="$t('项目名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.projectName"
            :show-clear-button="true"
            :placeholder="$t('请输入项目名称')"
          />
        </mt-form-item>
        <mt-form-item prop="projectManager" :label="$t('项目经理')" label-style="top">
          <mt-input
            v-model="searchFormModel.projectManager"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="demandDate" :label="$t('要求交期')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.demandDate"
            :allow-edit="false"
            :placeholder="$t('请选择要求交期')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'demandDate')"
          />
        </mt-form-item>
        <mt-form-item prop="siteCode" :label="$t('工厂代码')" label-style="top">
          <mt-input
            v-model="searchFormModel.siteCode"
            :show-clear-button="true"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
          />
        </mt-form-item>
        <mt-form-item prop="rdcFlag" :label="$t('是否入RDC仓')">
          <mt-select
            v-model="searchFormModel.rdcFlag"
            :data-source="relationContractInvalidFlagList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择是否入RDC仓')"
          />
        </mt-form-item>
        <mt-form-item prop="warehouseCode" :label="$t('入库仓库编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.warehouseCode"
            :show-clear-button="true"
            :placeholder="$t('请输入入库仓库编码')"
          />
        </mt-form-item>
        <mt-form-item prop="warehouseName" :label="$t('入库仓库名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.warehouseName"
            :show-clear-button="true"
            :placeholder="$t('请输入入库仓库名称')"
          />
        </mt-form-item>
        <mt-form-item prop="receiveUserCode" :label="$t('收货人编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.receiveUserCode"
            :show-clear-button="true"
            :placeholder="$t('请输入收货人编码')"
          />
        </mt-form-item>
        <mt-form-item prop="receiveUserName" :label="$t('收货人名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.receiveUserName"
            :show-clear-button="true"
            :placeholder="$t('请输入收货人名称')"
          />
        </mt-form-item>
        <mt-form-item prop="contactPhone" :label="$t('联系方式')" label-style="top">
          <mt-input
            v-model="searchFormModel.contactPhone"
            :show-clear-button="true"
            :placeholder="$t('请输入联系方式')"
          />
        </mt-form-item>
        <mt-form-item prop="receiveAddress" :label="$t('收货地址')" label-style="top">
          <mt-input
            v-model="searchFormModel.receiveAddress"
            :show-clear-button="true"
            :placeholder="$t('请输入联系方式')"
          />
        </mt-form-item>
        <mt-form-item prop="categoryCode" :label="$t('品类编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.categoryCode"
            :show-clear-button="true"
            :placeholder="$t('请输入品类编码')"
          />
        </mt-form-item>
        <mt-form-item prop="categoryName" :label="$t('品类名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.categoryName"
            :show-clear-button="true"
            :placeholder="$t('请输入品类名称')"
          />
        </mt-form-item>
        <mt-form-item prop="largeCategoryCode" :label="$t('大类编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.largeCategoryCode"
            :show-clear-button="true"
            :placeholder="$t('请输入大类编码')"
          />
        </mt-form-item>
        <mt-form-item prop="largeCategoryName" :label="$t('大类名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.largeCategoryName"
            :show-clear-button="true"
            :placeholder="$t('请输入大类名称')"
          />
        </mt-form-item>
        <mt-form-item prop="mediumCategoryCode" :label="$t('中类编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.mediumCategoryCode"
            :show-clear-button="true"
            :placeholder="$t('请输入中类编码')"
          />
        </mt-form-item>
        <mt-form-item prop="mediumCategoryName" :label="$t('中类名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.mediumCategoryName"
            :show-clear-button="true"
            :placeholder="$t('请输入中类名称')"
          />
        </mt-form-item>
        <mt-form-item prop="smallCategoryCode" :label="$t('小类编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.smallCategoryCode"
            :show-clear-button="true"
            :placeholder="$t('请输入小类编码')"
          />
        </mt-form-item>
        <mt-form-item prop="smallCategoryName" :label="$t('小类名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.smallCategoryName"
            :show-clear-button="true"
            :placeholder="$t('请输入小类名称')"
          />
        </mt-form-item>
        <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
          <mt-input
            v-model="searchFormModel.createUserName"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :allow-edit="false"
            :placeholder="$t('请选择创建时间')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'createTime')"
          />
        </mt-form-item>
        <mt-form-item prop="updateUserName" :label="$t('最后更新人')" label-style="top">
          <mt-input
            v-model="searchFormModel.updateUserName"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="updateTime" :label="$t('最后更新时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.updateTime"
            :allow-edit="false"
            :placeholder="$t('请选择创建时间')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'updateTime')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="scTableRef"
      grid-id="3477089c-f517-c152-d719-7eb4d9839ae4"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="listColumns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './../config/mixin'
import {
  detailToolbar,
  contractTypeList,
  businessTypeList,
  contractStatusList,
  statusList,
  relationContractInvalidFlagList,
  syncOaStatusList
} from './../config/index'
import XEUtils from 'xe-utils'

export default {
  components: {
    ScTable,
    CollapseSearch
  },
  mixins: [mixin],
  data() {
    return {
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 20,
        pageSizes: [20, 50, 200, 500, 1000, 2000]
      },
      pageInfo: {
        size: 20,
        current: 1
      },
      contractTypeList,
      businessTypeList,
      contractStatusList,
      statusList,
      relationContractInvalidFlagList,
      syncOaStatusList,
      toolbar: [],
      searchFormModel: {},
      tableData: [],
      loading: false
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    },
    // 列配置
    listColumns() {
      return [
        {
          width: 50,
          type: 'checkbox',
          fixed: 'left',
          align: 'center'
        },
        {
          width: 70,
          field: 'index',
          title: this.$t('行号')
        },
        {
          field: 'contractCode',
          title: this.$t('合同编号'),
          minWidth: 180,
          slots: {
            default: ({ row, column }) => {
              return [
                <a on-click={() => this.handleClickCellTitle(row, column)}>{row.contractCode}</a>
              ]
            }
          }
        },
        {
          field: 'contractName',
          title: this.$t('合同名称'),
          minWidth: 140
        },
        {
          field: 'contractStatus',
          title: this.$t('合同状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = contractStatusList.find(
                (item) => item.value === row.contractStatus
              )
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'status',
          title: this.$t('生效状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = statusList.find((item) => item.value === row.status)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'contractType',
          title: this.$t('合同类型'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = contractTypeList.find((item) => item.value === row.contractType)
              const name = selectItem?.text
              return [<div>{name}</div>]
            }
          }
        },
        {
          field: 'businessType',
          title: this.$t('业务类型'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = businessTypeList.find((item) => item.value === row.businessType)
              const name = selectItem?.text
              return [<div>{name}</div>]
            }
          }
        },
        {
          field: 'purchaseUserName',
          title: this.$t('采购负责人')
        },
        {
          field: 'companyCode',
          title: this.$t('公司'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [
                row.companyCode ? <span>{row.companyCode + '-' + row.companyName}</span> : null
              ]
            }
          }
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商编码'),
          minWidth: 140
        },
        {
          field: 'supplierName',
          title: this.$t('供应商名称'),
          minWidth: 140
        },
        {
          field: 'categoryCode',
          title: this.$t('品类编码'),
          minWidth: 100
        },
        {
          field: 'categoryName',
          title: this.$t('品类名称'),
          minWidth: 140
        },
        // 是否要查询区域start
        {
          field: 'itemCode',
          title: this.$t('物料编码'),
          minWidth: 140
        },
        {
          field: 'itemName',
          title: this.$t('物料名称'),
          minWidth: 140
        },
        {
          field: 'specificationModel',
          title: this.$t('规格型号'),
          minWidth: 140
        },
        {
          field: 'power',
          title: this.$t('功率（kW）'),
          minWidth: 140
        },
        {
          field: 'unitName',
          title: this.$t('单位'),
          minWidth: 140
        },
        {
          field: 'purchaseApplyCode',
          title: this.$t('采购申请单号'),
          minWidth: 140
        },
        {
          field: 'purchaseApplyItemNo',
          title: this.$t('采购申请单行号'),
          minWidth: 140
        },
        {
          field: 'sourceCode',
          title: this.$t('寻源单号'),
          minWidth: 140
        },
        {
          field: 'sourceItemNo',
          title: this.$t('寻源单行号'),
          minWidth: 140
        },
        {
          field: 'brand',
          title: this.$t('品牌'),
          minWidth: 140
        },
        {
          field: 'projectName',
          title: this.$t('项目名称'),
          minWidth: 140
        },
        {
          field: 'projectManager',
          title: this.$t('项目经理'),
          minWidth: 140
        },
        {
          field: 'demandQuantity',
          title: this.$t('需求数量'),
          minWidth: 140
        },
        {
          field: 'demandDate',
          title: this.$t('要求交期'),
          minWidth: 140
        },
        // 是否要查询区域end
        {
          field: 'relationContract',
          title: this.$t('关联合同'),
          minWidth: 200
        },
        {
          field: 'relationContractInvalidFlag',
          title: this.$t('是否失效原合同'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = relationContractInvalidFlagList.find(
                (item) => item.value === row.relationContractInvalidFlag
              )
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'signDate',
          title: this.$t('签订日期'),
          minWidth: 140
        },
        {
          field: 'contractStartValid',
          title: this.$t('合同生效时间'),
          minWidth: 140
        },
        {
          field: 'contractEndValid',
          title: this.$t('合同终止时间'),
          minWidth: 140
        },
        {
          field: 'currencyCode',
          title: this.$t('币种'),
          minWidth: 200,
          slots: {
            default: ({ row }) => {
              return [
                row.currencyCode ? <span>{row.currencyCode + '-' + row.currencyName}</span> : null
              ]
            }
          }
        },
        {
          field: 'untaxedContractTotal',
          title: this.$t('合同总金额（未税）'),
          minWidth: 140
        },
        {
          field: 'taxedContractTotal',
          title: this.$t('合同总金额（含税）'),
          minWidth: 140
        },

        // 是否要查询区域start
        {
          field: 'siteCode',
          title: this.$t('工厂代码'),
          minWidth: 140
        },
        {
          field: 'siteName',
          title: this.$t('工厂名称'),
          minWidth: 140
        },
        {
          field: 'priceUnit',
          title: this.$t('价格单位'),
          minWidth: 140
        },
        {
          field: 'baseUnitName',
          title: this.$t('基本单位'),
          minWidth: 140
        },
        {
          field: 'untaxedPriceUnit',
          title: this.$t('不含税单价'),
          minWidth: 140
        },
        {
          field: 'untaxedPriceTotal',
          title: this.$t('不含税总价'),
          minWidth: 140
        },
        {
          field: 'taxedPriceUnit',
          title: this.$t('含税单价'),
          minWidth: 140
        },
        {
          field: 'taxedPriceTotal',
          title: this.$t('含税总价'),
          minWidth: 140
        },
        {
          field: 'priceStartTime',
          title: this.$t('价格开始时间'),
          minWidth: 140
        },
        {
          field: 'priceEndTime',
          title: this.$t('价格结束时间'),
          minWidth: 140
        },
        {
          field: 'rdcFlag',
          title: this.$t('是否入RDC仓'),
          minWidth: 140
        },
        {
          field: 'warehouseCode',
          title: this.$t('入库仓库'),
          minWidth: 140
        },
        {
          field: 'receiveUserName',
          title: this.$t('收货人'),
          minWidth: 140
        },
        {
          field: 'contactPhone',
          title: this.$t('收货联系方式'),
          minWidth: 140
        },
        {
          field: 'receiveAddress',
          title: this.$t('收货地址'),
          minWidth: 140
        },
        {
          field: 'categoryName',
          title: this.$t('品类'),
          minWidth: 140
        },
        {
          field: 'largeCategoryName',
          title: this.$t('大类'),
          minWidth: 140
        },
        {
          field: 'mediumCategoryName',
          title: this.$t('中类'),
          minWidth: 140
        },
        {
          field: 'smallCategoryName',
          title: this.$t('小类'),
          minWidth: 140
        },

        // 是否要查询区域end
        {
          field: 'supplierContactor',
          title: this.$t('供应商联系人'),
          minWidth: 140
        },
        {
          field: 'supplierContactPhone',
          title: this.$t('供应商联系电话'),
          minWidth: 140
        },
        {
          field: 'supplierContactMail',
          title: this.$t('供应商邮箱'),
          minWidth: 140
        },
        {
          field: 'supplierContactMail2',
          title: this.$t('供应商备用邮箱'),
          minWidth: 140
        },
        {
          field: 'syncOaStatus',
          title: this.$t('同步OA状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = syncOaStatusList.find((item) => item.value === row.syncOaStatus)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'syncOaResult',
          title: this.$t('同步OA接口返回'),
          minWidth: 140
        },
        {
          field: 'purRemark',
          title: this.$t('采方备注'),
          minWidth: 140
        }
      ]
    }
  },
  mounted() {
    if (!this.$router.path?.includes('-sup')) {
      this.toolbar = detailToolbar
    } else {
      this.toolbar = [{ code: 'export', name: this.$t('导出'), status: 'info' }]
    }
    this.handleSearch()
  },
  activated() {
    this.handleSearch()
  },
  methods: {
    // 选择公司
    handleCompanyChange(e) {
      console.log('test lint')
      this.$set(this.searchFormModel, 'purchaseOrgCode', null)
      if (e.itemData.id) {
        this.getPurchaseOrgList(e.itemData.id)
      } else {
        this.purchaseOrgList = []
      }
    },
    // 日期格式化
    handleDateChange(e, field) {
      const startField = field + 'Start'
      const endField = field + 'End'
      if (e.startDate) {
        const startDate = XEUtils.toDateString(e.startDate, 'yyyy-MM-dd') + ' 00:00:00'
        const endDate = XEUtils.toDateString(e.endDate, 'yyyy-MM-dd') + ' 23:59:59'
        this.searchFormModel[startField] = XEUtils.timestamp(startDate)
        this.searchFormModel[endField] = XEUtils.timestamp(endDate)
      } else {
        this.searchFormModel[startField] = null
        this.searchFormModel[endField] = null
      }
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    // 查询
    async handleSearch(type) {
      // 解决修改查询条件后，查询结果有数据但未正常显示的问题（type: pageChange/refresh）
      if (!type) {
        this.pageInfo.current = 1
        this.$refs.pageRef.jumpNum = 1
        this.$refs.pageRef.currentPage = 1
      }
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      delete params.submitTime
      this.loading = true
      const res = await this.$API.contract
        .queryContractDetailList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.tableData?.forEach((item, index) => {
          item.index = index + 1
        }) || []
        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (!['export'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      const ids = []
      const firstItem = selectedRecords[0]
      for (let index = 0; index < selectedRecords.length; index++) {
        const item = selectedRecords[index]
        if (
          e.code === 'transfer' &&
          (firstItem.companyCode !== item.companyCode ||
            firstItem.supplierCode !== item.supplierCode ||
            firstItem.businessType !== item.businessType)
        ) {
          this.$toast({
            content: this.$t('请选择业务类型、供应商、公司相同的数据操作！'),
            type: 'warning'
          })
          return
        }
        ids.push(item.id)
      }
      switch (e.code) {
        case 'transfer': // 批量转办
          this.handleOperate(ids)
          break
        case 'export':
          this.handleExport()
          break
        default:
          break
      }
    },
    handleExport() {
      const params = { page: { size: 9999, current: 1 }, ...this.searchFormModel } // 筛选条件
      this.$API.contract.exportContractDetailList(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 点击单元格
    handleClickCellTitle(row, column) {
      if (column.field === 'contractCode') {
        this.$router.push({
          name: this.$route.path.includes('-sup') ? 'contract-sup-detail' : 'contract-detail',
          query: {
            type: 'detail',
            id: row.contractId,
            refreshId: Date.now()
          }
        })
      }
    },
    // 转采购订单
    handleOperate(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认将选中的数据转采购订单？`)
        },
        success: async () => {
          const res = await this.$API.contract.contractTransferOrder({
            ids
          })
          if (res.code === 200) {
            this.$router.push({
              name: 'purchase-coordination-pv-detail',
              query: {
                type: 'edit',
                ids,
                orderSource: 2,
                refreshId: Date.now()
              }
            })
          }
        }
      })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.handleSearch('pageChange')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.handleSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
