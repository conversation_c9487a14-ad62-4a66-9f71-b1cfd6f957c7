<template>
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="contractCode" :label="$t('合同编号')" label-style="top">
          <mt-input
            v-model="searchFormModel.contractCode"
            :show-clear-button="true"
            :placeholder="$t('请输入合同编号')"
          />
        </mt-form-item>
        <mt-form-item prop="contractName" :label="$t('合同名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.contractName"
            :show-clear-button="true"
            :placeholder="$t('请输入合同名称')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierCodeStr" :label="$t('供应商编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.supplierCodeStr"
            :show-clear-button="true"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
          />
        </mt-form-item>
        <mt-form-item prop="contractType" :label="$t('合同类型')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.contractType"
            css-class="rule-element"
            :data-source="contractTypeList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择合同类型')"
          />
        </mt-form-item>
        <mt-form-item prop="contractStatus" :label="$t('合同状态')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.contractStatus"
            css-class="rule-element"
            :data-source="contractStatusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择合同状态')"
          />
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('生效状态')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.status"
            css-class="rule-element"
            :data-source="statusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择生效状态')"
          />
        </mt-form-item>
        <mt-form-item prop="approvalStatus" :label="$t('审批状态')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.approvalStatus"
            css-class="rule-element"
            :data-source="approvalStatusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择审批状态')"
          />
        </mt-form-item>
        <mt-form-item prop="businessType" :label="$t('业务类型')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.businessType"
            css-class="rule-element"
            :data-source="businessTypeList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择业务类型')"
          />
        </mt-form-item>
        <mt-form-item prop="purchaseUserCode" :label="$t('采购负责人编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.purchaseUserCode"
            :show-clear-button="true"
            :placeholder="$t('请输入采购负责人编码')"
          />
        </mt-form-item>
        <mt-form-item prop="purchaseUserName" :label="$t('采购负责人名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.purchaseUserName"
            :show-clear-button="true"
            :placeholder="$t('请输入采购负责人名称')"
          />
        </mt-form-item>
        <mt-form-item prop="dataSource" :label="$t('数据来源')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.dataSource"
            css-class="rule-element"
            :data-source="dataSourceList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择数据来源')"
          />
        </mt-form-item>
        <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.companyCode"
            css-class="rule-element"
            :data-source="companyList"
            :fields="{ text: 'text', value: 'orgCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择公司')"
          />
        </mt-form-item>
        <mt-form-item prop="purchaseOrgCode" :label="$t('采购组织编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.purchaseOrgCode"
            :show-clear-button="true"
            :placeholder="$t('请输入采购组织编码')"
          />
        </mt-form-item>
        <mt-form-item prop="purchaseOrgName" :label="$t('采购组织名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.purchaseOrgName"
            :show-clear-button="true"
            :placeholder="$t('请输入采购组织名称')"
          />
        </mt-form-item>
        <mt-form-item prop="relationContract" :label="$t('关联合同')" label-style="top">
          <mt-input
            v-model="searchFormModel.relationContract"
            :show-clear-button="true"
            :placeholder="$t('请输入关联合同')"
          />
        </mt-form-item>
        <mt-form-item prop="relationContractInvalidFlag" :label="$t('是否失效原合同')">
          <mt-select
            v-model="searchFormModel.relationContractInvalidFlag"
            :data-source="relationContractInvalidFlagList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择是否失效原合同')"
          />
        </mt-form-item>
        <mt-form-item prop="signDate" :label="$t('签订日期')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.signDate"
            :allow-edit="false"
            :placeholder="$t('请选择签订日期')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'signDate')"
          />
        </mt-form-item>
        <mt-form-item prop="currencyCode" :label="$t('币种编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.currencyCode"
            :show-clear-button="true"
            :placeholder="$t('请输入币种编码')"
          />
        </mt-form-item>
        <mt-form-item prop="currencyName" :label="$t('币种名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.currencyName"
            :show-clear-button="true"
            :placeholder="$t('请输入币种名称')"
          />
        </mt-form-item>
        <mt-form-item prop="contractStartValid" :label="$t('合同生效时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.contractStartValid"
            :allow-edit="false"
            :placeholder="$t('请选择合同生效时间')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'contractStartValid')"
          />
        </mt-form-item>
        <mt-form-item prop="contractEndValid" :label="$t('合同终止时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.contractEndValid"
            :allow-edit="false"
            :placeholder="$t('请选择合同终止时间')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'contractEndValid')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierContactor" :label="$t('供应商联系人')" label-style="top">
          <mt-input
            v-model="searchFormModel.supplierContactor"
            :show-clear-button="true"
            :placeholder="$t('请输入供应商联系人')"
          />
        </mt-form-item>
        <mt-form-item prop="syncOaStatus" :label="$t('同步OA状态')">
          <mt-multi-select
            v-model="searchFormModel.syncOaStatus"
            :data-source="syncOaStatusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择同步OA状态')"
          />
        </mt-form-item>
        <mt-form-item prop="syncOaResult" :label="$t('同步OA接口返回')" label-style="top">
          <mt-input
            v-model="searchFormModel.syncOaResult"
            :show-clear-button="true"
            :placeholder="$t('请输入同步OA接口返回')"
          />
        </mt-form-item>
        <mt-form-item prop="purRemark" :label="$t('采方备注')" label-style="top">
          <mt-input
            v-model="searchFormModel.purRemark"
            :show-clear-button="true"
            :placeholder="$t('请输入采方备注')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      grid-id="abe1b045-5677-a732-c2b9-861764bee562"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="listColumns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
    <!-- 附件弹框 -->
    <uploader-dialog
      @change="change"
      @confirm-function="confirm"
      @cancel="cancel"
      ref="uploaderDialog"
    ></uploader-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { download, getHeadersFileName } from '@/utils/utils'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './../config/mixin'
import {
  listToolbar,
  contractTypeList,
  businessTypeList,
  dataSourceList,
  approvalStatusList,
  contractStatusList,
  statusList,
  relationContractInvalidFlagList,
  syncOaStatusList
} from './../config/index'
import XEUtils from 'xe-utils'

export default {
  components: {
    UploaderDialog: () => import('@/components/Upload/uploaderDialog'), //优化dialog初始加载问题
    ScTable,
    CollapseSearch
  },
  mixins: [mixin],
  data() {
    return {
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 20,
        pageSizes: [20, 50, 200, 500, 1000, 2000]
      },
      pageInfo: {
        size: 20,
        current: 1
      },
      contractTypeList,
      businessTypeList,
      dataSourceList,
      approvalStatusList,
      contractStatusList,
      statusList,
      relationContractInvalidFlagList,
      syncOaStatusList,
      toolbar: [],
      searchFormModel: {},
      tableData: [],
      loading: false,
      type: 'list',
      uploadFileList: [] // 上传的附件
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    // 列配置
    listColumns() {
      return [
        {
          width: 50,
          type: 'checkbox',
          fixed: 'left',
          align: 'center'
        },
        // {
        //   width: 70,
        //   field: 'index',
        //   title: this.$t('序号')
        // },
        {
          field: 'contractCode',
          title: this.$t('合同编号'),
          minWidth: 180,
          slots: {
            default: ({ row, column }) => {
              return [
                <a on-click={() => this.handleClickCellTitle(row, column)}>{row.contractCode}</a>
              ]
            }
          }
        },
        {
          field: 'contractName',
          title: this.$t('合同名称'),
          minWidth: 140
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商编码'),
          minWidth: 140
        },
        {
          field: 'supplierName',
          title: this.$t('供应商名称'),
          minWidth: 140
        },
        {
          field: 'categoryCode',
          title: this.$t('品类编码'),
          minWidth: 100
        },
        {
          field: 'categoryName',
          title: this.$t('品类名称'),
          minWidth: 140
        },
        {
          field: 'contractType',
          title: this.$t('合同类型'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = contractTypeList.find((item) => item.value === row.contractType)
              const name = selectItem?.text
              return [<div>{name}</div>]
            }
          }
        },
        {
          field: 'businessType',
          title: this.$t('业务类型'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = businessTypeList.find((item) => item.value === row.businessType)
              const name = selectItem?.text
              return [<div>{name}</div>]
            }
          }
        },
        {
          field: 'purchaseUserName',
          title: this.$t('采购负责人'),
          minWidth: 150
        },
        {
          field: 'dataSource',
          title: this.$t('数据来源'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = dataSourceList.find((item) => item.value === row.dataSource)
              const name = selectItem?.text
              return [<div>{name}</div>]
            }
          }
        },
        {
          field: 'contractStatus',
          title: this.$t('合同状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = contractStatusList.find(
                (item) => item.value === row.contractStatus
              )
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'status',
          title: this.$t('生效状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = statusList.find((item) => item.value === row.status)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'approvalStatus',
          title: this.$t('审批状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = approvalStatusList.find(
                (item) => item.value === row.approvalStatus
              )
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'relationContract',
          title: this.$t('关联合同'),
          minWidth: 200
        },
        {
          field: 'relationContractInvalidFlag',
          title: this.$t('是否失效原合同'),
          minWidth: 160,
          slots: {
            default: ({ row }) => {
              const selectItem = relationContractInvalidFlagList.find(
                (item) => item.value === row.relationContractInvalidFlag
              )
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'companyCode',
          title: this.$t('公司'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [
                row.companyCode ? <span>{row.companyCode + '-' + row.companyName}</span> : null
              ]
            }
          }
        },
        {
          field: 'purchaseOrgCode',
          title: this.$t('采购组织'),
          minWidth: 200,
          slots: {
            default: ({ row }) => {
              return [
                row.purchaseOrgCode ? (
                  <span>{row.purchaseOrgCode + '-' + row.purchaseOrgName}</span>
                ) : null
              ]
            }
          }
        },
        {
          field: 'signDate',
          title: this.$t('签订日期'),
          minWidth: 140
        },
        {
          field: 'contractStartValid',
          title: this.$t('合同生效时间'),
          minWidth: 140
        },
        {
          field: 'contractEndValid',
          title: this.$t('合同终止时间'),
          minWidth: 140
        },
        {
          field: 'currencyCode',
          title: this.$t('币种'),
          minWidth: 200,
          slots: {
            default: ({ row }) => {
              return [
                row.currencyCode ? <span>{row.currencyCode + '-' + row.currencyName}</span> : null
              ]
            }
          }
        },
        {
          field: 'untaxedContractTotal',
          title: this.$t('合同总金额（未税）'),
          minWidth: 180
        },
        {
          field: 'taxedContractTotal',
          title: this.$t('合同总金额（含税）'),
          minWidth: 180
        },
        {
          field: 'supplierContactor',
          title: this.$t('供应商联系人'),
          minWidth: 140
        },
        {
          field: 'supplierContactPhone',
          title: this.$t('供应商联系电话'),
          minWidth: 140
        },
        {
          field: 'supplierContactMail',
          title: this.$t('供应商邮箱'),
          minWidth: 140
        },
        {
          field: 'supplierContactMail2',
          title: this.$t('供应商备用邮箱'),
          minWidth: 140
        },
        {
          field: 'syncOaStatus',
          title: this.$t('同步OA状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = syncOaStatusList.find((item) => item.value === row.syncOaStatus)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'syncOaResult',
          title: this.$t('同步OA接口返回'),
          minWidth: 140
        },
        // {
        //   field: 'createUserName',
        //   title: this.$t('创建人')
        // },
        // {
        //   field: 'createTime',
        //   title: this.$t('创建时间'),
        //   minWidth: 140
        // },
        // {
        //   field: 'lastUpdateUserName',
        //   title: this.$t('最后修改人')
        // },
        // {
        //   field: 'lastUpdateTime',
        //   title: this.$t('最后修改时间'),
        //   minWidth: 140
        // },
        {
          field: 'purRemark',
          title: this.$t('采方备注'),
          minWidth: 140
        }
        // {
        //   field: 'oaApproveLink',
        //   title: this.$t('OA申请单查看'),
        //   minWidth: 120,
        //   slots: {
        //     default: ({ row, column }) => {
        //       return [
        //         <div>
        //           <a
        //             v-show={row.oaApproveLink}
        //             on-click={() => this.handleClickCellTitle(row, column)}>
        //             {this.$t('查看')}
        //           </a>
        //           <span v-show={!row.oaApproveLink}>-</span>
        //         </div>
        //       ]
        //     }
        //   }
        // },
      ]
    }
  },
  mounted() {
    if (!this.$route.path.includes('-sup')) {
      this.toolbar = listToolbar
    } else {
      this.toolbar = [{ code: 'export', name: this.$t('导出'), status: 'info' }]
    }
    this.handleSearch()
  },
  activated() {
    this.handleSearch()
  },
  methods: {
    // 选择公司
    handleCompanyChange(e) {
      console.log('test lint')
      this.$set(this.searchFormModel, 'purchaseOrgCode', null)
      if (e.itemData.id) {
        this.getPurchaseOrgList(e.itemData.id)
      } else {
        this.purchaseOrgList = []
      }
    },
    // 选择定价对象
    handleFixPriceObjectChange(e) {
      this.$set(
        this.searchFormModel,
        'fixPriceObjectType',
        e.value ? e.itemData.sourcingObjType : null
      )
    },
    // 日期格式化
    handleDateChange(e, field) {
      const startField = field + 'Start'
      const endField = field + 'End'
      if (e.startDate) {
        const startDate = XEUtils.toDateString(e.startDate, 'yyyy-MM-dd') + ' 00:00:00'
        const endDate = XEUtils.toDateString(e.endDate, 'yyyy-MM-dd') + ' 23:59:59'
        this.searchFormModel[startField] = XEUtils.timestamp(startDate)
        this.searchFormModel[endField] = XEUtils.timestamp(endDate)
      } else {
        this.searchFormModel[startField] = null
        this.searchFormModel[endField] = null
      }
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    // 查询
    async handleSearch(type) {
      // 解决修改查询条件后，查询结果有数据但未正常显示的问题（type: pageChange/refresh）
      if (!type) {
        this.pageInfo.current = 1
        this.$refs.pageRef.jumpNum = 1
        this.$refs.pageRef.currentPage = 1
      }
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      delete params.submitTime
      this.loading = true
      const res = await this.$API.contract
        .queryContractList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.tableData?.forEach((item, index) => {
          item.index = index + 1
        }) || []
        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (!['add', 'edit', 'export'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      if (['submit', 'viewOA', 'archived'].includes(e.code)) {
        if (selectedRecords.length !== 1) {
          this.$toast({ content: this.$t('请仅选择一条数据！'), type: 'warning' })
          return
        }
      }
      const ids = []
      for (let index = 0; index < selectedRecords.length; index++) {
        const item = selectedRecords[index]
        if (e.code === 'deleteContract' && ![0, 3].includes(item.contractStatus)) {
          this.$toast({ content: this.$t('仅支持删除草稿、审批拒绝状态的数据！'), type: 'warning' })
          return
        }
        if (e.code === 'batchAbolish' && ![1, 3].includes(item.contractStatus)) {
          this.$toast({
            content: this.$t('仅支持作废审批中和审批拒绝状态的数据！'),
            type: 'warning'
          })
          return
        }
        if (
          e.code === 'archived' &&
          (![2].includes(item.contractStatus) || ![1].includes(item.status))
        ) {
          this.$toast({
            content: this.$t('仅支持合同状态为审批通过且生效状态为已生效状态的数据！'),
            type: 'warning'
          })
          return
        }
        ids.push(item.id)
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'edit':
          this.handleAdd('edit', selectedRecords)
          break
        case 'deleteContract': // 删除
          this.handleOperate(ids, e.code)
          break
        case 'submit': // 提交
          this.handleOperate(selectedRecords, e.code, { id: selectedRecords[0].id })
          break
        case 'viewOA': // 查看OA审批
          if (![1, 2, 3].includes(selectedRecords[0].contractStatus)) {
            this.$toast({
              content: this.$t(
                '仅支持状态为审批中、审批通过和审批拒绝的单据跳转至OA系统查看审批！'
              ),
              type: 'warning'
            })
            return
          }
          if (selectedRecords[0].oaUrl) {
            window.open(selectedRecords[0].oaUrl)
          } else {
            this.$toast({ content: this.$t('未提交OA审批'), type: 'warning' })
          }
          break
        case 'batchAbolish': // 作废
          // 若关联的订单存在未完结的，则需弹窗提示“合同XXX存在未完结的采购订单，请完成采购订单或关闭采购订单后才能作废合同！”，否则弹窗提示“作废后新的采购订单将无法再使用该合同，您确定要作废吗？”。
          this.handleOperate(ids, e.code)
          break
        case 'archived': // 归档
          this.$refs.uploaderDialog?.dialogInit({
            title: this.$t('归档')
          })
          break
        case 'transferContract': // 批量转办
          this.$dialog({
            modal: () => import('./components/purchaseUser'),
            data: {
              title: this.$t('批量转办')
            },
            success: (info) => {
              this.handleOperate(ids, e.code, {
                idList: ids,
                purchaseUserCode: info.employeeCode,
                purchaseUserName: info.employeeName
              })
            }
          })
          break
        case 'export':
          this.handleExport()
          break
        default:
          break
      }
    },
    // dialog - hander - change
    change(data) {
      this.uploadFileList = data
    },
    cancel() {
      this.uploadFileList = []
    },
    // dialog - hander - confirm
    confirm() {
      let _uploadFileList = cloneDeep(this.uploadFileList)
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const params = {
        id: selectedRecords[0].id, //主键ID
        fileList: [
          {
            fileId: 152255888520, //附件id
            fileName: 'xxxx', //附件名称
            fileType: 'xxxx', //附件类型
            fileSize: 10025, //文件大小
            url: 'xxxx' //文件路径
          }
        ]
      }
      _uploadFileList.forEach((file) => {
        params.fileList.push({
          fileId: file.id,
          fileName: file.fileName,
          fileType: file.fileType,
          fileSize: file.fileSize,
          url: file.url
        })
      })
      this.$API.contract.archivedContract(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t(`操作成功！`), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleExport() {
      const params = { page: { size: 9999, current: 1 }, ...this.searchFormModel } // 筛选条件
      this.$API.contract.exportContractList(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 点击单元格
    handleClickCellTitle(row, column) {
      if (column.field === 'contractCode') {
        this.$router.push({
          name: this.$route.path.includes('-sup') ? 'contract-sup-detail' : 'contract-detail',
          query: {
            type: 'detail',
            id: row.id,
            refreshId: Date.now()
          }
        })
      } else if (column.field === 'oaApproveLink') {
        window.open(row.oaApproveLink)
      }
    },
    // 新增/编辑
    handleAdd(type, selectedRecords) {
      if (type === 'edit') {
        if (selectedRecords.length !== 1) {
          this.$toast({ content: this.$t('请仅选择一条数据！'), type: 'warning' })
          return
        }
        if (![0, 3].includes(selectedRecords[0].contractStatus)) {
          this.$toast({ content: this.$t('仅支持编辑草稿和审批拒绝状态的单据！'), type: 'warning' })
          return
        }
        this.$router.push({
          name: 'contract-detail',
          query: {
            type: 'edit',
            id: selectedRecords[0].id,
            refreshId: Date.now()
          }
        })
        return
      }
      this.$router.push({
        name: 'contract-detail',
        query: {
          type: 'create',
          refreshId: Date.now()
        }
      })
    },
    // 删除、提交
    handleOperate(ids, type, extraParams = {}) {
      const tipMap = {
        deleteContract: this.$t('删除'),
        submit: this.$t('提交'),
        batchAbolish: this.$t('作废'),
        transferContract: this.$t('批量转办')
      }
      const params = { ...extraParams }
      if (type !== 'submit') {
        params.ids = ids
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认${tipMap[type]}选中的数据？`)
        },
        success: async () => {
          const res = await this.$API.contract[type](params)
          if (res.code === 200) {
            this.$toast({ content: this.$t(`${tipMap[type]}成功！`), type: 'success' })
            this.handleSearch()
          }
        }
      })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.handleSearch('pageChange')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.handleSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
