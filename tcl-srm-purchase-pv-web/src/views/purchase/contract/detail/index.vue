<template>
  <div class="postpone-full-height">
    <div class="top-container toggle-container" :class="[!isExpand && 'top-hidden']">
      <div class="top-content">
        <div class="top-info">
          <div class="title">
            <div>
              <!-- <span>{{ dataForm.rfxCode }}</span> -->
              <span class="sub-title">{{
                `${$t('合同状态')}: ${getContractStatusText(dataForm.contractStatus) || ''}`
              }}</span>
              <span class="sub-title">{{
                `${$t('合同编号')}: ${dataForm.contractCode || ''}`
              }}</span>
              <!-- <span class="sub-title">{{
                `${$t('创建人')}: ${dataForm.purchaserName || ''}`
              }}</span>
              <span class="sub-title">{{ `${$t('创建时间')}: ${dataForm.createTime || ''}` }}</span> -->
            </div>
          </div>
          <div class="top-btns">
            <vxe-button
              v-for="(item, index) in detailToolbar"
              v-show="!item.isHidden"
              :key="index"
              :icon="item.icon"
              :status="item.status"
              :disabled="item.disabled"
              size="small"
              @click="handleClickToolBar(item)"
              >{{ item.name }}</vxe-button
            >
          </div>
        </div>
        <div class="top-form">
          <mt-form ref="dataFormRef" :model="dataForm" :rules="formRules">
            <mt-form-item prop="contractName" :label="$t('合同名称')" label-style="top">
              <vxe-input
                v-model="dataForm.contractName"
                clearable
                :disabled="!editable"
                :placeholder="$t('请输入合同名称')"
              />
            </mt-form-item>
            <mt-form-item prop="businessType" :label="$t('业务类型')" label-style="top">
              <vxe-select
                v-model="dataForm.businessType"
                :options="businessTypeList"
                :option-props="{ label: 'text', value: 'value' }"
                clearable
                filterable
                :disabled="!editable"
                :placeholder="$t('请选择业务类型')"
              />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
              <vxe-select
                v-model="dataForm.companyCode"
                :options="companyList"
                :option-props="{ label: 'text', value: 'orgCode' }"
                clearable
                filterable
                :disabled="!editable"
                :placeholder="$t('请选择公司')"
                @change="(e) => handleValueChange('company', e)"
              />
            </mt-form-item>
            <mt-form-item prop="purchaseOrgCode" :label="$t('采购组织')">
              <vxe-select
                v-model="dataForm.purchaseOrgCode"
                :options="purchaseOrgList"
                :option-props="{ label: 'text', value: 'organizationCode' }"
                clearable
                filterable
                :disabled="!editable || !dataForm.companyCode"
                :placeholder="$t('请选择采购组织')"
                @change="(e) => handleValueChange('purchaseOrg', e)"
              />
            </mt-form-item>
            <mt-form-item prop="purchaseUserName" :label="$t('采购负责人')" label-style="top">
              <vxe-pulldown ref="pulldownRef" destroy-on-close>
                <template #default>
                  <vxe-input
                    :value="
                      dataForm.purchaseUserCode
                        ? `${dataForm.purchaseUserCode}-${dataForm.purchaseUserName}`
                        : ''
                    "
                    suffix-icon="vxe-icon-caret-down"
                    readonly
                    :disabled="!editable"
                    :placeholder="$t('请选择采购负责人')"
                    @click="handlePulldown"
                  />
                </template>
                <template #dropdown>
                  <vxe-input
                    ref="purchaserSearcInputRef"
                    v-model="purchaserSearcValue"
                    :prefix-icon="'vxe-icon-search'"
                    clearable
                    :placeholder="$t('搜索')"
                    @input="handlePulldownSearchInput"
                  />
                  <vxe-list class="my-dropdown2" :data="purchaserList" auto-resize height="auto">
                    <template #default="{ items }">
                      <div v-if="items.length">
                        <div
                          class="list-item2"
                          v-for="item in items"
                          :key="item.employeeId"
                          @click="handlePulldownItemSelected(item)"
                        >
                          <span
                            :class="{
                              isSelected: item.employeeCode === dataForm.purchaserUserCode
                            }"
                            >{{ item.text }}</span
                          >
                        </div>
                      </div>
                      <div v-else class="empty-tip">
                        {{ $t('暂无数据') }}
                      </div>
                    </template>
                  </vxe-list>
                </template>
              </vxe-pulldown>
            </mt-form-item>
            <!-- <mt-form-item prop="categoryCode" :label="$t('品类')" label-style="top">
              <VxeRemoteSearch
                v-model="dataForm.categoryCode"
                :placeholder="$t('请选择')"
                :fields="{ text: 'categoryName', value: 'categoryCode' }"
                :request-info="{
                  urlPre: 'masterData',
                  url: 'pageCategoryApi',
                  searchFields: ['categoryCode', 'categoryName'],
                  params: {
                    page: {
                      current: 1,
                      size: 50
                    }
                  },
                  recordsPosition: 'data.records'
                }"
                on-change="(e) => {
                    dataForm.categoryName = e?.categoryName || null
                  }"
              />
            </mt-form-item> -->
            <mt-form-item prop="categoryCode" :label="$t('品类')" label-style="top">
              <vxe-pulldown ref="pulldownCategoryRef" destroy-on-close>
                <template #default>
                  <vxe-input
                    :value="
                      dataForm.categoryCode
                        ? `${dataForm.categoryCode}-${dataForm.categoryName}`
                        : ''
                    "
                    suffix-icon="vxe-icon-caret-down"
                    readonly
                    :disabled="!editable"
                    :placeholder="$t('请选择品类')"
                    @click="handlePulldownCategory"
                  />
                </template>
                <template #dropdown>
                  <vxe-input
                    ref="categorySearcInputRef"
                    v-model="categorySearcValue"
                    :prefix-icon="'vxe-icon-search'"
                    clearable
                    :placeholder="$t('搜索')"
                    @input="handlePulldownSearchInputCategory"
                  />
                  <vxe-list class="my-dropdown2" :data="categoryList" auto-resize height="auto">
                    <template #default="{ items }">
                      <div v-if="items.length">
                        <div
                          class="list-item2"
                          v-for="item in items"
                          :key="item.id"
                          @click="handlePulldownItemSelectedCategory(item)"
                        >
                          <span
                            :class="{
                              isSelected: item.categoryCode === dataForm.categoryCode
                            }"
                            >{{ item.text }}</span
                          >
                        </div>
                      </div>
                      <div v-else class="empty-tip">
                        {{ $t('暂无数据') }}
                      </div>
                    </template>
                  </vxe-list>
                </template>
              </vxe-pulldown>
            </mt-form-item>
            <mt-form-item prop="contractType" :label="$t('合同类型')" label-style="top">
              <vxe-select
                v-model="dataForm.contractType"
                :options="contractTypeList"
                :option-props="{ label: 'text', value: 'value' }"
                clearable
                filterable
                :disabled="!editable"
                :placeholder="$t('请选择合同类型')"
              />
            </mt-form-item>
            <mt-form-item prop="currencyCode" :label="$t('币种')" label-style="top">
              <vxe-select
                v-model="dataForm.currencyCode"
                :options="currencyList"
                :option-props="{ label: 'text', value: 'currencyCode' }"
                clearable
                filterable
                :disabled="!editable"
                :placeholder="$t('请选择币种')"
                @change="(e) => handleValueChange('currency', e)"
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('生效状态')" label-style="top">
              <vxe-select
                v-model="dataForm.status"
                :options="statusList"
                :option-props="{ label: 'text', value: 'value' }"
                clearable
                disabled
                filterable
                :placeholder="$t('请选择生效状态')"
              />
            </mt-form-item>
            <mt-form-item prop="approvalStatus" :label="$t('审批状态')" label-style="top">
              <vxe-select
                v-model="dataForm.approvalStatus"
                :options="approvalStatusList"
                :option-props="{ label: 'text', value: 'value' }"
                clearable
                disabled
                filterable
                :placeholder="$t('请选择审批状态')"
              />
            </mt-form-item>

            <mt-form-item prop="relationContract" :label="$t('关联合同')" label-style="top">
              <vxe-input
                v-model="dataForm.relationContract"
                clearable
                :disabled="!editable"
                readonly
                :placeholder="$t('请输入关联合同')"
                suffix-icon="vxe-icon-search"
                @suffix-click="choseContractCode"
              />
            </mt-form-item>
            <mt-form-item
              prop="relationContractInvalidFlag"
              :label="$t('是否失效原合同')"
              label-style="top"
            >
              <vxe-select
                v-model="dataForm.relationContractInvalidFlag"
                :options="relationContractInvalidFlagList"
                :option-props="{ label: 'text', value: 'value' }"
                clearable
                :disabled="!editable"
                filterable
                :placeholder="$t('请选择审批状态')"
              />
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('供应商')" label-style="top">
              <vxe-pulldown ref="pulldownSupplierRef" destroy-on-close>
                <template #default>
                  <vxe-input
                    :value="
                      dataForm.supplierCode
                        ? `${dataForm.supplierCode}-${dataForm.supplierName}`
                        : ''
                    "
                    suffix-icon="vxe-icon-caret-down"
                    readonly
                    :disabled="!editable"
                    :placeholder="$t('请选择供应商')"
                    @click="handlePulldownSupplier"
                  />
                </template>
                <template #dropdown>
                  <vxe-input
                    ref="supplierSearcInputRef"
                    v-model="supplierSearcValue"
                    :prefix-icon="'vxe-icon-search'"
                    clearable
                    :placeholder="$t('搜索')"
                    @input="handlePulldownSearchInputSupplier"
                  />
                  <vxe-list class="my-dropdown2" :data="supplierList" auto-resize height="auto">
                    <template #default="{ items }">
                      <div v-if="items.length">
                        <div
                          class="list-item2"
                          v-for="item in items"
                          :key="item.id"
                          @click="handlePulldownItemSelectedSupplier(item)"
                        >
                          <span
                            :class="{
                              isSelected: item.supplierCode === dataForm.supplierCode
                            }"
                            >{{ item.text }}</span
                          >
                        </div>
                      </div>
                      <div v-else class="empty-tip">
                        {{ $t('暂无数据') }}
                      </div>
                    </template>
                  </vxe-list>
                </template>
              </vxe-pulldown>
            </mt-form-item>
            <mt-form-item prop="signDate" :label="$t('签订日期')" label-style="top">
              <vxe-input
                v-model="dataForm.signDate"
                type="date"
                label-format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                :disabled="!editable"
                clearable
                :placeholder="$t('请选择签订日期')"
              />
            </mt-form-item>
            <mt-form-item prop="contractStartValid" :label="$t('合同生效时间')" label-style="top">
              <vxe-input
                v-model="dataForm.contractStartValid"
                type="date"
                label-format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                :disabled="!editable"
                clearable
                :placeholder="$t('请选择合同生效时间')"
                @change="contractStartValidChange"
              />
            </mt-form-item>
            <mt-form-item prop="contractEndValid" :label="$t('合同终止时间')" label-style="top">
              <vxe-input
                v-model="dataForm.contractEndValid"
                type="date"
                label-format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                :disabled="!editable"
                :disabled-method="endDateDisabled"
                clearable
                :placeholder="$t('请选择合同终止时间')"
              />
            </mt-form-item>

            <mt-form-item
              prop="untaxedContractTotal"
              :label="$t('合同总金额（未税）')"
              label-style="top"
            >
              <vxe-input
                v-model="dataForm.untaxedContractTotal"
                clearable
                :disabled="!editable"
                :placeholder="$t('请输入')"
              />
            </mt-form-item>

            <mt-form-item
              prop="taxedContractTotal"
              :label="$t('合同总金额（含税）')"
              label-style="top"
            >
              <vxe-input
                v-model="dataForm.taxedContractTotal"
                clearable
                :disabled="!editable"
                :placeholder="$t('请输入')"
              />
            </mt-form-item>
            <mt-form-item prop="supplierContactor" :label="$t('供应商联系人')" label-style="top">
              <vxe-input
                v-model="dataForm.supplierContactor"
                clearable
                :disabled="!editable"
                :placeholder="$t('请输入供应商联系人')"
              />
            </mt-form-item>

            <mt-form-item
              prop="supplierContactPhone"
              :label="$t('供应商联系电话')"
              label-style="top"
            >
              <vxe-input
                v-model="dataForm.supplierContactPhone"
                clearable
                :disabled="!editable"
                :placeholder="$t('请输入供应商联系电话')"
              />
            </mt-form-item>
            <mt-form-item prop="supplierContactMail" :label="$t('供应商邮箱')" label-style="top">
              <vxe-input
                v-model="dataForm.supplierContactMail"
                clearable
                :disabled="!editable"
                :placeholder="$t('请输入供应商邮箱')"
              />
            </mt-form-item>

            <mt-form-item
              prop="supplierContactMail2"
              :label="$t('供应商备用邮箱')"
              label-style="top"
            >
              <vxe-input
                v-model="dataForm.supplierContactMail2"
                clearable
                :disabled="!editable"
                :placeholder="$t('请输入供应商备用邮箱')"
              />
            </mt-form-item>
            <mt-form-item prop="syncOaStatus" :label="$t('同步OA状态')" label-style="top">
              <vxe-select
                v-model="dataForm.syncOaStatus"
                :options="syncOaStatusList"
                :option-props="{ label: 'text', value: 'value' }"
                disabled
              />
            </mt-form-item>

            <mt-form-item prop="syncOaResult" :label="$t('同步OA接口返回')" label-style="top">
              <vxe-input v-model="dataForm.syncOaResult" clearable disabled />
            </mt-form-item>
            <mt-form-item prop="purRemark" :label="$t('采方备注')" label-style="top">
              <vxe-textarea
                v-model="dataForm.purRemark"
                clearable
                :disabled="!editable"
                :rows="1"
                :placeholder="$t('请输入采方备注')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </div>
      <div class="top-toggle-btn">
        <div @click="handleToggle"><i class="vxe-icon-arrow-up" /></div>
      </div>
    </div>
    <div class="body-container" v-show="$route.query.type !== 'create'">
      <mt-tabs
        ref="mtTabsRef"
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabList"
        :halt-select="false"
        @handleSelectTab="(index, item) => handleTabChange(index)"
        style="background-color: #fff"
      />
      <keep-alive :include="keepArr">
        <component
          ref="mainContent"
          style="height: calc(100% - 50px)"
          :is="activeComponent"
          :data-info="dataForm"
          :factory-list="factoryList"
          :detail-info="dataForm"
          @updateDetail="init"
          @updateItemList="updateItemList"
          @itemDataChange="itemDataChange"
        />
      </keep-alive>
    </div>
  </div>
</template>

<script>
import VxeRemoteSearch from '@/components/VxeRemoteSearch/index.vue'
import {
  Input as VxeInput,
  Button as VxeButton,
  Select as VxeSelect,
  Textarea as VxeTextarea,
  Pulldown as VxePulldown
} from 'vxe-table'
import mixin from './../config/mixin'
import {
  businessTypeList,
  contractTypeList,
  approvalStatusList,
  contractStatusList,
  syncOaStatusList,
  statusList,
  relationContractInvalidFlagList
} from './../config/index'
import debounce from 'lodash.debounce'
import dayjs from 'dayjs'

export default {
  name: 'ExpertRating',
  components: {
    VxeInput,
    VxeButton,
    VxeSelect,
    VxeTextarea,
    VxePulldown,
    VxeRemoteSearch
  },
  mixins: [mixin],
  data() {
    return {
      businessTypeList,
      contractTypeList,
      approvalStatusList,
      contractStatusList,
      syncOaStatusList,
      relationContractInvalidFlagList,
      currencyList: [],
      supplierList: [],
      categoryList: [],
      itemDetailAddDtoList: [],
      fileRequestList: [],
      statusList,
      dataForm: {
        purchaseUserCode: JSON.parse(sessionStorage.getItem('userInfo'))?.employeeCode || null,
        purchaseUserName: JSON.parse(sessionStorage.getItem('userInfo'))?.accountName || null
      },
      activeTabIndex: 0,
      isExpand: true,
      dataList: [[], []],
      type: 'detail',
      isInit: true,
      purchaserSearcValue: null,
      supplierSearcValue: null,
      categorySearcValue: null,
      keepArr: ['ItemTab', 'PurchaseOrderTab', 'AttachmentTab']
    }
  },
  computed: {
    editable() {
      return (
        (this.$route.query.type !== 'detail' || ![0, 3].includes(this.dataForm.contractStatus)) &&
        !this.$route.path?.includes('-sup')
      )
    },
    tabList() {
      const tabs = [
        { title: this.$t('物料明细'), compName: 'ItemTab' },
        // { title: this.$t('关联采购订单'), compName: 'PurchaseOrderTab' },
        { title: this.$t('合同附件'), compName: 'AttachmentTab' }
      ]
      return tabs
    },
    activeComponent() {
      let comp = ''
      switch (this.activeTabIndex) {
        case 0:
          // 物料明细
          comp = () => import('./components/itemTab.vue')
          break
        // case 1:
        //   // 关联采购订单
        //   comp = () => import('./components/purchaseOrderTab.vue')
        //   break
        case 1:
          // 合同附件
          comp = () => import('./components/attachment.vue')
          break

        default:
          return
      }
      return comp
    },
    formRules() {
      return {
        contractName: [
          {
            required: true,
            message: this.$t('请输入合同名称'),
            trigger: 'blur'
          }
        ],
        businessType: [{ required: false, message: this.$t('请选择业务类型'), trigger: 'blur' }],
        companyCode: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        purchaseOrgCode: [{ required: true, message: this.$t('请选择采购组织'), trigger: 'blur' }],
        contractType: [{ required: true, message: this.$t('请选择合同类型'), trigger: 'blur' }],
        currencyCode: [{ required: true, message: this.$t('请选择币种'), trigger: 'blur' }],
        purchaseUserName: [
          { required: true, message: this.$t('请选择采购负责人'), trigger: 'blur' }
        ],
        categoryCode: [{ required: true, message: this.$t('请选择品类'), trigger: 'blur' }],
        relationContract: [
          {
            required: !!this.dataForm.relationContractInvalidFlag,
            message: this.$t('请选择关联合同'),
            trigger: 'blur'
          }
        ],
        supplierCode: [{ required: true, message: this.$t('请选择供应商'), trigger: 'blur' }],
        signDate: [{ required: true, message: this.$t('请选择签订日期'), trigger: 'blur' }],
        contractStartValid: [
          { required: true, message: this.$t('请选择合同生效时间'), trigger: 'blur' }
        ],
        contractEndValid: [
          { required: true, message: this.$t('请选择合同终止时间'), trigger: 'blur' }
        ]
      }
    },
    detailToolbar() {
      return [
        {
          code: 'back',
          name: this.$t('返回')
        },
        // {
        //   code: 'viewOA',
        //   name: this.$t('OA审批进度'),
        //   status: '',
        //   isHidden: !this.dataForm?.oaApproveLink
        // },
        {
          code: 'save',
          name: this.$t('保存'),
          isHidden:
            (this.$route.query.type !== 'create' && this.$route.query.type !== 'edit') ||
            [2, 5].includes(this.dataForm.contractStatus) ||
            this.$route.path.includes('-sup'),
          status: 'primary'
        }
        // {
        //   code: 'submit',
        //   name: this.$t('提交'),
        //   isHidden:
        //     this.$route.query.type === 'create' ||
        //     (this.$route.query.type === 'edit' && ![2, 5].includes(this.dataForm.contractStatus)),
        //   status: 'primary'
        // }
      ]
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    choseContractCode() {
      if (this.isSup || !this.editable) {
        return
      }
      this.$dialog({
        modal: () => import('./components/contractList'),
        data: {
          title: this.$t('选择合同')
        },
        success: (row) => {
          this.dataForm.relationContract = row?.contractCode
        }
      })
    },
    contractStartValidChange() {
      if (
        dayjs(this.dataForm.contractStartValid).valueOf() >
        dayjs(this.dataForm.contractEndValid).valueOf()
      ) {
        this.dataForm.contractEndValid = this.dataForm.contractStartValid
      }
    },
    endDateDisabled(params) {
      const { date } = params
      if (this.dataForm.contractStartValid) {
        return date < dayjs(this.dataForm.contractStartValid) || date < new Date()
      }
      return date < new Date()
    },
    // 远程搜索查询-展开面板
    handlePulldown() {
      this.$refs.pulldownRef?.showPanel()
      this.$nextTick(() => {
        this.$refs.purchaserSearcInputRef?.focus()
      })
    },
    // 远程搜索查询-查询
    handlePulldownSearchInput: debounce(function (e) {
      this.getPurchaserList(e?.value)
    }, 500),
    // 远程搜索查询-选中
    handlePulldownItemSelected(item) {
      if (this.$refs.pulldownRef) {
        this.$set(this.dataForm, 'purchaseUserCode', item.employeeCode)
        this.$set(this.dataForm, 'purchaseUserName', item.employeeName)
        this.$refs.pulldownRef.hidePanel()
        this.purchaserSearcValue = null
      }
    },
    // 远程搜索查询-展开面板
    handlePulldownSupplier() {
      this.$refs.pulldownSupplierRef?.showPanel()
      this.$nextTick(() => {
        this.$refs.SupplierSearcInputRef?.focus()
      })
    },
    // 远程搜索查询-查询
    handlePulldownSearchInputSupplier: debounce(function (e) {
      this.getSupplierList(e?.value)
    }, 500),
    // 远程搜索查询-选中
    handlePulldownItemSelectedSupplier(item) {
      if (this.$refs.pulldownSupplierRef) {
        this.$set(this.dataForm, 'supplierCode', item.supplierCode)
        this.$set(this.dataForm, 'supplierName', item.supplierName)
        this.$refs.pulldownSupplierRef.hidePanel()
        this.supplierSearcValue = null
      }
    },
    // 远程搜索查询-展开面板
    handlePulldownCategory() {
      this.$refs.pulldownCategoryRef?.showPanel()
      this.$nextTick(() => {
        this.$refs.categorySearcInputRef?.focus()
      })
    },
    // 远程搜索查询-查询
    handlePulldownSearchInputCategory: debounce(function (e) {
      this.getCategoryList(e?.value)
    }, 500),
    // 远程搜索查询-选中
    handlePulldownItemSelectedCategory(item) {
      if (this.$refs.pulldownCategoryRef) {
        this.$set(this.dataForm, 'categoryCode', item.categoryCode)
        this.$set(this.dataForm, 'categoryName', item.categoryName)
        this.$refs.pulldownCategoryRef.hidePanel()
        this.categorySearcValue = null
      }
    },
    getContractStatusText() {
      const item = contractStatusList.find((item) => item.value === this.dataForm.contractStatus)
      return item?.text || ''
    },
    // 初始化
    init() {
      if (this.$route.query.type !== 'create') {
        this.getHeaderInfo()
        this.isExpand = false
      } else {
        this.getPurchaserList()
        // 设置默认值
        this.dataForm = {
          purchaseUserCode: JSON.parse(sessionStorage.getItem('userInfo'))?.employeeCode || null,
          purchaseUserName: JSON.parse(sessionStorage.getItem('userInfo'))?.accountName || null
        }
      }
      // 获取币种
      this.getCurrencyList()
      // 获取供应商
      this.getSupplierList()
      // 获取品类
      this.getCategoryList()
    },
    async getCurrencyList() {
      const res = await this.$API.masterData.queryAllCurrency()
      this.currencyList = res.data.map((i) => {
        return {
          text: i.currencyCode + '-' + i.currencyName,
          ...i
        }
      })
    },
    async getCategoryList(searchText) {
      const params = {
        page: {
          current: 1,
          size: 50
        }
      }
      if (searchText) {
        params.rules = [
          {
            condition: 'or',
            label: '',
            field: 'categoryName',
            type: 'string',
            operator: 'contains',
            value: searchText
          },
          {
            condition: 'or',
            label: '',
            field: 'categoryCode',
            type: 'string',
            operator: 'contains',
            value: searchText
          }
        ]
      }
      const res = await this.$API.masterData.pageCategoryApi(params)
      this.categoryList = res.data.records.map((i) => {
        return {
          text: i.categoryCode + '-' + i.categoryName,
          ...i
        }
      })
    },
    async getSupplierList(searchText) {
      const params = {
        page: {
          current: 1,
          size: 50
        }
      }
      // if (isNaN(searchText)) {
      //   params.supplierName = searchText
      // } else {
      //   params.supplierCode = searchText
      // }
      if (searchText) {
        // params.supplierName = searchText
        params.rules = [
          {
            condition: 'or',
            label: '',
            field: 'supplierName',
            type: 'string',
            operator: 'contains',
            value: searchText
          },
          {
            condition: 'or',
            label: '',
            field: 'supplierCode',
            type: 'string',
            operator: 'contains',
            value: searchText
          }
        ]
      }
      const res = await this.$API.masterData.supplierDistinctPagedQueryNoScope(params)
      this.supplierList = res.data.records.map((i) => {
        return {
          text: i.supplierCode + '-' + i.supplierName,
          ...i
        }
      })
    },

    // 监听定价明细数据
    itemDataChange(data) {
      switch (this.activeTabIndex) {
        case 0:
          this.itemDetailAddDtoList = data
          break
        case 1:
          this.pvFileRequestList = data
          break
        default:
          break
      }
    },
    async updateItemList() {
      const res = await this.$API.contract.getContractDetail({
        id: this.$route.query.id
      })
      this.$set(this.dataForm, 'detailList', res.data.detailList)
      this.itemDetailAddDtoList = this.dataForm.detailList
    },
    // 获取头部基础信息
    async getHeaderInfo() {
      const res = await this.$API.contract.getContractDetail({
        id: this.$route.query.id
      })
      if (res.code === 200) {
        this.dataForm = { ...res.data }
        this.itemDetailAddDtoList = this.dataForm.detailList
        this.pvFileRequestList = this.dataForm.fileList
        this.getCompanyList(true)
        this.getPurchaserList(res.data?.purchaserName)
        this.getSupplierList(res.data?.supplierName)
      }
    },

    // 下拉列表选中值修改
    handleValueChange(prefix, e) {
      const { value } = e
      switch (prefix) {
        // 选择公司
        case 'company':
          this.$set(this.dataForm, 'purchaseOrgCode', null)
          // this.$set(this.dataForm, 'factoryCode', null)

          if (value) {
            const selectedItem = this.companyList.find((item) => item.orgCode === value)
            this.dataForm.companyId = selectedItem ? selectedItem.id : null
            this.dataForm.companyName = selectedItem ? selectedItem.orgName : null

            this.getPurchaseOrgList(selectedItem?.id)
          } else {
            this.dataForm.companyId = null
            this.dataForm.companyName = null
          }
          break
        // 选择采购组织
        case 'purchaseOrg':
          // this.$set(this.dataForm, 'factoryCode', null)
          if (value) {
            const selectedItem = this.purchaseOrgList.find(
              (item) => item.organizationCode === value
            )
            this.dataForm.purchaseOrgId = selectedItem ? selectedItem.id : null
            this.dataForm.purchaseOrgName = selectedItem ? selectedItem.organizationName : null

            const { companyId, purchaseOrgId } = this.dataForm
            this.getFactoryListByCompanyAndPur(companyId, purchaseOrgId)
          } else {
            this.dataForm.purchaseOrgId = null
            this.dataForm.purchaseOrgName = null

            // this.dataForm.factoryCode = null
            // this.dataForm.factoryName = null
          }
          break
        // 选择币种
        case 'currency':
          if (value) {
            const selectedItem = this.currencyList.find((item) => item.currencyCode === value)
            this.dataForm.currencyName = selectedItem ? selectedItem.currencyName : null
          } else {
            this.dataForm.currencyName = null
          }
          break
        // 选择采购负责人
        case 'purchaseUser':
          if (e) {
            this.dataForm.purchaseUserName = e ? e.employeeName : null
          } else {
            this.dataForm.purchaseUserName = null
          }
          break
        // 选择供应商
        case 'supplier':
          if (value) {
            const selectedItem = this.supplierList.find((item) => item.supplierCode === value)
            this.dataForm.supplierName = selectedItem ? selectedItem.supplierName : null
          } else {
            this.dataForm.supplierName = null
          }
          break
        // 选择工厂
        // case 'factory':
        //   if (value) {
        //     const selectedItem = this.factoryList.find((item) => item.orgCode === value)
        //     this.dataForm.factoryName = selectedItem ? selectedItem.orgName : null
        //   } else {
        //     this.dataForm.factoryName = null
        //   }
        //   break
        default:
          break
      }
    },
    // 点击工具栏按钮
    handleClickToolBar(e) {
      switch (e.code) {
        case 'back':
          this.handleBack()
          break
        case 'save':
          this.handleSave()
          break
        case 'submit':
          this.handleSubmit(e.code)
          break
        case 'viewOA':
          window.open(this.dataForm?.oaApproveLink)
          break
        default:
          break
      }
    },
    // 切换tab页签
    handleTabChange(index) {
      this.$refs.mtTabsRef.activeTab = index
      this.activeTabIndex = index
    },
    // 返回
    handleBack() {
      this.$router.go(-1)
    },
    // 处理提交数据
    getSubmitData() {
      let params = {}
      this.$refs.dataFormRef.validate(async (valid) => {
        if (!valid) {
          //展开头部表单数据，显示报错信息
          this.isExpand = true
          return
        }
        params = {
          ...this.dataForm,
          detailList: this.itemDetailAddDtoList,
          fileList: this.pvFileRequestList
        }
      })
      return params
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    // 处理保存数据----不需要校验必填
    getSaveData() {
      let params = {}
      this.$refs.dataFormRef.validate(async (valid) => {
        if (!valid) {
          //展开头部表单数据，显示报错信息
          this.isExpand = true
          return
        }
        params = {
          ...this.dataForm,
          signDate: this.dataForm.signDate ? this.getUnix(dayjs(this.dataForm.signDate)) : null,
          contractStartValid: this.dataForm.contractStartValid
            ? this.getUnix(dayjs(this.dataForm.contractStartValid))
            : null,
          contractEndValid: this.dataForm.contractEndValid
            ? this.getUnix(dayjs(this.dataForm.contractEndValid))
            : null,
          detailList:
            this.itemDetailAddDtoList.map((i) => {
              return {
                ...i,
                demandDate: i.demandDate ? this.getUnix(dayjs(i.demandDate)) : null,
                priceStartTime: i.priceStartTime ? this.getUnix(dayjs(i.priceStartTime)) : null,
                priceEndTime: i.priceEndTime ? this.getUnix(dayjs(i.priceEndTime)) : null,
                // id: i.id ? (i.id.includes('row_') ? null : i.id) : null
                id: i.id.includes('row_') || !i.id ? null : i.id
              }
            }) || [],
          fileList: this.pvFileRequestList || []
        }
      })
      return params
    },
    // 保存
    async handleSave() {
      let params = this.getSaveData()
      if (Object.entries(params).length === 0) return
      this.$store.commit('startLoading')
      const res = await this.$API.contract.saveContractDetail(params)
      this.$store.commit('endLoading')
      if (res.code === 200) {
        this.$toast({ content: this.$t('操作成功！'), type: 'success' })
        if (this.$route.query.type === 'create') {
          this.$router.replace({
            name: 'contract'
            // query: {
            //   type: 'edit',
            //   id: res.data
            // }
          })
        } else {
          this.getHeaderInfo()
          this.$refs.mainContent?.initTableData()
          // if (this.activeComponent?.name !== 'AttachmentTab') {
          //   this.$refs.mainContent?.initTableData()
          // }
        }
      }
    },

    // 提交
    async handleSubmit(type) {
      let params = this.getSubmitData(type)
      if (Object.entries(params).length === 0) return
      this.$store.commit('startLoading')
      const res = await this.$API.postponePricing.submitPostpone(params)
      this.$store.commit('endLoading')
      if (res.code === 200) {
        this.$toast({ content: this.$t('操作成功！'), type: 'success' })
        this.getHeaderInfo()
        if (this.activeComponent?.name !== 'AttachmentTab') {
          this.$refs.mainContent?.initTableData()
        }
      }
    },
    // 弹框展示
    async showDialog(message, cssClass) {
      return new Promise((resolve) => {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t(message),
            cssClass
          },
          success: resolve
        })
      })
    },
    // 展开/收缩
    handleToggle() {
      this.isExpand = !this.isExpand
    }
  }
}
</script>

<style scoped lang="scss">
.postpone-full-height {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 8px 8px 0;
  background: #fff;
}
.body-container {
  height: calc(100% - 80px);
}
.top-container {
  flex: 1;
  width: 100%;
  .top-content {
    padding: 8px 8px 0 8px;
    background-color: #f9f9f9;
    border-radius: 8px;
    .top-info {
      display: flex;
      justify-content: space-between;
      padding-bottom: 8px;
      .title {
        font-size: 20px;
        font-weight: 700;
        color: #292929;
        padding-right: 12px;
        span {
          margin-right: 15px;
        }
        .sub-title {
          font-size: 15px;
          color: #777;
        }
      }
      .top-btns {
        text-align: right;
        margin-bottom: 10px;
        white-space: nowrap;
      }
    }
    .top-form {
      .mt-form {
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
        justify-content: space-between;
        grid-gap: 5px;
      }
      .vxe-input,
      .vxe-select,
      .vxe-pulldown {
        width: 100%;
        height: 34px;
        line-height: 34px;
      }
      /deep/.vxe-pulldown--panel {
        min-width: unset !important;
        width: 100%;
      }
    }
  }
  .top-toggle-btn {
    width: 100%;
    div {
      width: 80px;
      height: 15px;
      line-height: 15px;
      margin: auto;
      text-align: center;
      background-color: #4a556b;
      color: #fff;
      font-weight: bold;
      border-radius: 0 0 6px 6px;
    }
  }
}

.top-hidden {
  .top-form {
    display: none;
  }
  .top-toggle-btn {
    div {
      transform: rotate(180deg);
      border-radius: 6px 6px 0 0;
    }
  }
}

.my-dropdown2 {
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 4px 0;
  max-height: 200px;
  border-radius: 4px;
  border: 1px solid #dadce0;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}
.list-item2 {
  line-height: 30px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 0 4px;

  .isSelected {
    font-weight: 700;
    color: #409eff;
  }
}
.list-item2:hover {
  background-color: #f5f7fa;
}
.empty-tip {
  width: 100%;
  line-height: 30px;
  padding: 0 4px;
  text-align: center;
  color: #c0c4cc;
}

::v-deep {
  .mt-form-item {
    margin-bottom: 15px;
    .label {
      margin: 0 10px 5px 0;
    }
  }
  .mt-tabs-container {
    background-color: #fff;
  }

  .mt-tabs-container ul.tab-container li.tab-item2.active {
    border-bottom: 2px solid #409eff;
  }
  .mt-tabs-container ul.tab-container li.tab-item2.active,
  .mt-tabs-container ul.tab-container li.tab-item2:hover {
    color: #31374e;
  }

  .vxe-textarea--inner {
    min-height: 34px;
    height: 100%;
  }
}
</style>
