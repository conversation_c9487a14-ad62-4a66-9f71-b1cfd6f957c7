<template>
  <div class="flex-full-height">
    <CustomAgGrid
      ref="CustomAgGrid"
      :toolbar="toolbar"
      :columns="columns"
      :suppress-row-transform="true"
      :row-data="tableData"
      :animate-rows="false"
      :is-pagination="false"
      :get-row-id="getRowId"
      :row-class-rules="rowClassRules"
      :params-type="2"
      @handleClickToolbar="handleClickToolbar"
      @onGridReady="onGridReady"
      @refresh="refresh"
    >
    </CustomAgGrid>
  </div>
</template>

<script>
import CustomAgGrid from '@/components/CustomAgGrid'
import { purchaseOrderColumns, purchaseOrderToolbar } from './config/purchaseOrder'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  name: 'ItemTab',
  components: {
    CustomAgGrid
  },
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tableData: [],
      columns: purchaseOrderColumns,
      toolbar: []
    }
  },
  computed: {
    editConfig() {
      return {
        enabled:
          this.$route.query.id &&
          ![2, 5].includes(this.dataInfo.contractStatus) &&
          !this.$route.path.includes('-sup'),
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    rowClassRules() {
      return {
        errorBg: function (params) {
          return params.data.errorMsg
        }
      }
    }
  },
  watch: {
    'dataInfo.status': {
      handler(v) {
        if (v || v === 0) {
          this.toolbar = purchaseOrderToolbar()
        }
      },
      deep: true,
      immediate: true
    },
    'dataInfo.purchaseOrderList': {
      handler() {
        this.tableData = this.dataInfo?.purchaseOrderList || []
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    if (this.$route.query.type !== 'create') {
      this.initTableData()
    }
  },
  methods: {
    /**
     * ---------------------------ag相关---------------------------
     */
    // ag - 初始监听
    onGridReady(params) {
      this.agGrid = params
    },
    // ag - 获取rowId
    getRowId(params) {
      return params.data.id ? params.data.id : params.data?.customId
    },
    /**
     * ---------------------------初始化---------------------------
     */
    // 初始化获取数据
    async initTableData() {
      // this.tableData = this.dataInfo?.purchaseOrderList || []
      const params = {
        page: { size: 9999, current: 1 },
        contractCode: this.dataInfo.contractCode
      }
      const res = await this.$API.purchaseOrderMgt.queryDetailList(params)
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.tableData?.forEach((item, index) => {
          item.index = index + 1
        }) || []
      }
    },
    // btn - 刷新
    refresh() {
      this.initTableData()
    },
    /**
     * ---------------------------事件操作---------------------------
     */
    // 点击按钮栏
    handleClickToolbar(e) {
      switch (e?.toolbar?.id) {
        case 'export':
          this.handleExport()
          break
        default:
          break
      }
    },
    // 导出
    async handleExport() {
      const params = {
        id: this.$route.query.id
      }
      const res = await this.$API.contract.exportPurchaseOrder(params)
      if (res.data) {
        this.$toast({
          type: 'success',
          content: this.$t('正在导出，请稍后！')
        })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      }
    }
  }
}
</script>
<style lang="scss"></style>
<style lang="scss" scoped>
.mt-pagertemplate {
  margin-bottom: 0px !important;
}
</style>
