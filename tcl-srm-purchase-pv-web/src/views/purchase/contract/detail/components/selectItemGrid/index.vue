<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <!-- <mt-template-page ref="templateRef" :template-config="pageConfig" /> -->
      <template>
        <div style="width: 100%">
          <!-- 自定义查询条件 -->
          <collapse-search
            class="toggle-container"
            :is-grid-display="true"
            :default-expand="true"
            @reset="handleReset"
            @search="handleSearch"
          >
            <mt-form ref="searchFormRef" :model="searchFormModel">
              <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
                <mt-input
                  v-model="searchFormModel.itemCode"
                  :show-clear-button="true"
                  :placeholder="$t('请输入物料编码')"
                />
              </mt-form-item>
              <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
                <mt-input
                  v-model="searchFormModel.itemName"
                  :show-clear-button="true"
                  :placeholder="$t('请输入物料名称')"
                />
              </mt-form-item>
            </mt-form>
          </collapse-search>
          <!-- 表格 -->
          <sc-table
            ref="sctableRef"
            grid-id="703ce927-994a-4b92-80e8-9716507487f0"
            :loading="loading"
            :is-show-refresh-bth="true"
            :columns="listColumns"
            :table-data="tableData"
            @refresh="handleSearch"
          >
          </sc-table>
          <!-- 分页 -->
          <mt-page
            ref="pageRef"
            class="flex-keep custom-page"
            :page-settings="pageSettings"
            :total-pages="pageSettings.totalPages"
            @currentChange="handleCurrentChange"
            @sizeChange="handleSizeChange"
          />
        </div>
      </template>
    </div>
  </mt-dialog>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
export default {
  components: {
    ScTable,
    CollapseSearch
  },
  data() {
    return {
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 20,
        pageSizes: [20, 50, 200, 500, 1000, 2000]
      },
      pageInfo: {
        size: 20,
        current: 1
      },
      searchFormModel: {},
      tableData: [],
      loading: false,
      data: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
      // pageConfig: [
      //   {
      //     useToolTemplate: false,
      //     toolbar: [],
      //     gridId: '406d62ee-44dd-4529-85c4-cc84c542b461',
      //     grid: {
      //       allowFiltering: true,
      //       columnData,
      //       allowSorting: false,
      //       allowSelection: true,
      //       selectionSettings: {
      //         type: 'Multiple',
      //         mode: 'Row'
      //       },
      //       recordDoubleClick: this.recordDoubleClick,
      //       asyncConfig: {
      //         url: this.$API.masterData.getItemListUrlPage
      //         // url: this.$API.masterData.getItemListUrls,
      //         // params: {
      //         //   orgId: this.modalData.organizationId,
      //         // },
      //       }
      //     }
      //   }
      // ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    // 列配置
    listColumns() {
      return [
        {
          width: 50,
          // type: 'checkbox'
          type: 'radio'
        },
        {
          width: '150',
          field: 'itemCode',
          title: this.$t('物料编码')
        },
        { width: '150', field: 'itemName', title: this.$t('物料名称') },
        {
          width: '150',
          field: 'categoryResponse.categoryCode',
          title: this.$t('品类编码')
        },
        {
          width: '150',
          field: 'categoryResponse.categoryName',
          title: this.$t('品类名称')
        },
        { width: '150', field: 'itemDescription', title: this.$t('规格型号') },
        { width: '150', field: 'oldItemCode', title: this.$t('旧物料编码') }
      ]
    },
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.handleSearch()
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    confirm() {
      // const selectedRecords = this.tableRef.getCheckboxRecords()
      const selectedRecords = this.tableRef.getRadioRecord()
      // if (selectedRecords.length <= 0) {
      if (!selectedRecords) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      } else {
        this.$emit('confirm-function', selectedRecords)
      }
    },
    cancel() {
      this.$emit('cancel-function')
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    // 查询
    async handleSearch(type) {
      // 解决修改查询条件后，查询结果有数据但未正常显示的问题（type: pageChange/refresh）
      if (!type) {
        this.pageInfo.current = 1
        this.$refs.pageRef.jumpNum = 1
        this.$refs.pageRef.currentPage = 1
      }
      const rules = []
      if (this.searchFormModel.itemCode) {
        rules.push({
          field: 'itemCode',
          label: '',
          operator: 'contains',
          type: 'string',
          value: this.searchFormModel.itemCode
        })
      }
      if (this.searchFormModel.itemName) {
        rules.push({
          field: 'itemName',
          label: '',
          operator: 'contains',
          type: 'string',
          value: this.searchFormModel.itemName
        })
      }
      const params = {
        page: this.pageInfo,
        condition: 'and',
        rules
      }
      this.loading = true
      const res = await this.$API.masterData
        .getItemListUrlList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.tableData?.forEach((item, index) => {
          item.index = index + 1
        }) || []
        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.handleSearch('pageChange')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.handleSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
