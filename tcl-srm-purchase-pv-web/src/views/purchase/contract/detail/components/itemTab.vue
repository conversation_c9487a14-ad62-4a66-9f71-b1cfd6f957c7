<template>
  <div>
    <sc-table
      ref="sctableRef"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      keep-source
      :sortable="false"
      :is-show-right-btn="false"
      :is-show-refresh-bth="false"
      @edit-closed="editComplete"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import VxeRemoteSearch from '@/components/VxeRemoteSearch/index.vue'
import { whetherMapList, priceUnitList } from './config/item'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  name: 'ItemTab',
  components: { ScTable, VxeRemoteSearch },
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    },
    factoryList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      editRules: {
        itemCode: [{ required: true, message: this.$t('此项必填') }],
        siteCode: [{ required: true, message: this.$t('此项必填') }],
        taxedPriceUnit: [{ required: true, message: this.$t('此项必填') }],
        taxCode: [{ required: true, message: this.$t('此项必填') }],
        untaxedPriceUnit: [{ required: true, message: this.$t('此项必填') }],
        priceStartTime: [{ required: true, message: this.$t('此项必填') }],
        priceEndTime: [{ required: true, message: this.$t('此项必填') }]
      },
      taxRateNameList: [],
      unitNameList: [],
      whetherMapList,
      priceUnitList
    }
  },
  watch: {
    'dataInfo.detailList': {
      handler() {
        if (this.pageType !== 'create') {
          this.initTableData()
        }
      },
      deep: true
    }
  },
  computed: {
    pageType() {
      return this.$route.query?.type
    },
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('行号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'purchaseApplyCode',
          title: this.$t('采购申请单号')
        },
        {
          field: 'purchaseApplyItemNo',
          title: this.$t('采购申请单行号'),
          minWidth: 150
        },
        {
          field: 'sourceCode',
          title: this.$t('寻源单号')
        },
        {
          field: 'sourceItemNo',
          title: this.$t('寻源单行号')
        },
        {
          field: 'siteCode',
          title: this.$t('工厂'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [row.siteCode ? <div>{row.siteCode + '-' + row.siteName}</div> : null]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.siteCode}
                  options={this.factoryList}
                  placeholder={this.$t('请选择')}
                  clearable
                  filterable
                  transfer
                  onChange={() => {
                    let selectedItem = this.factoryList.find((v) => v.value === row.siteCode)
                    row.siteName = selectedItem?.siteName
                    if (row.siteCode && row.itemCode) {
                      this.setPriceInfo(row)
                    }
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'itemCode',
          title: this.$t('物料编码'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.itemCode}
                  fields={{ text: 'itemName', value: 'itemCode' }}
                  placeholder={this.$t('请选择')}
                  request-info={{
                    urlPre: 'masterData',
                    url: 'getItemPage',
                    searchFields: ['itemCode', 'itemName'],
                    params: {
                      defaultRules: [
                        {
                          field: 'organizationCode',
                          operator: 'equal',
                          value: row.siteCode
                        }
                      ],
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  link-field-value={row.siteCode}
                  disabled={row.siteCode ? false : true}
                  onChange={(e) => {
                    this.setItemInfo(e, row)
                    if (row.siteCode && row.itemCode) {
                      this.setPriceInfo(row)
                    }
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'itemName',
          title: this.$t('物料名称')
        },
        {
          field: 'brand',
          title: this.$t('品牌'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input v-model={row.brand} placeholder={this.$t('请输入')} transfer clearable />
              ]
            }
          }
        },
        {
          field: 'specificationModel',
          title: this.$t('规格型号'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.specificationModel}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'power',
          title: this.$t(`功率(KW)`),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.power}
                  placeholder={this.$t('请输入')}
                  min='0'
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'projectName',
          title: this.$t('项目名称'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.projectName}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'projectManager',
          title: this.$t('项目经理'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.projectManager}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'demandQuantity',
          title: this.$t('需求数量'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.demandQuantity}
                  placeholder={this.$t('请输入')}
                  min='0'
                  transfer
                  onChange={() => {
                    if (row.taxedPriceUnit && row.taxRate) {
                      // 未税单价
                      row.untaxedPriceUnit =
                        (Number(row.taxedPriceUnit) / (1 + Number(row.taxRate))).toFixed(2) || 0
                    }
                    if (row.taxRate && row.taxedPriceUnit) {
                      row.untaxedPriceTotal =
                        (Number(row.untaxedPriceUnit) * Number(row.demandQuantity)).toFixed(2) || 0
                      row.taxedPriceTotal =
                        (Number(row.taxedPriceUnit) * Number(row.demandQuantity)).toFixed(2) || 0
                    }
                    row.creatableQuantity = row.demandQuantity
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'creatableQuantity',
          title: this.$t('可创建数量')
        },
        {
          field: 'demandDate',
          title: this.$t('要求交期'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              const dateDisabled = (params) => {
                const { date } = params
                return date < new Date()
              }
              return [
                <vxe-input
                  type={'date'}
                  v-model={row.demandDate}
                  placeholder={this.$t('请选择')}
                  transfer
                  clearable
                  label-format='yyyy-MM-dd'
                  value-format='yyyy-MM-dd'
                  disabled-method={dateDisabled}
                />
              ]
            }
          }
        },
        {
          field: 'priceUnit',
          title: this.$t('价格单位'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              let selectedItem = this.priceUnitList.find((v) => v.value === row.priceUnit)
              return [selectedItem ? <div>{selectedItem.label}</div> : null]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.priceUnit}
                  options={this.priceUnitList}
                  placeholder={this.$t('请选择')}
                  clearable
                  filterable
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'baseUnitCode',
          title: this.$t('基本单位'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [
                row.baseUnitCode ? <div>{row.baseUnitCode + '-' + row.baseUnitName}</div> : null
              ]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.baseUnitCode}
                  options={this.unitNameList}
                  placeholder={this.$t('请选择')}
                  clearable
                  filterable
                  transfer
                  onChange={() => {
                    let selectedItem = this.unitNameList.find((v) => v.value === row.baseUnitCode)
                    row.baseUnitName = selectedItem?.unitName
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'taxCode',
          title: this.$t('税率(%)'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [row.taxCode ? <div>{row.taxCode + '-' + row.taxName}</div> : null]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.taxCode}
                  options={this.taxRateNameList}
                  placeholder={this.$t('请选择')}
                  clearable
                  filterable
                  transfer
                  onChange={() => {
                    let selectedItem = this.taxRateNameList.find((v) => v.value === row.taxCode)
                    row.taxName = selectedItem?.taxItemName
                    row.taxRate = selectedItem?.taxRate
                    if (row.taxedPriceUnit) {
                      row.untaxedPriceUnit =
                        (Number(row.taxedPriceUnit) / (1 + Number(row.taxRate))).toFixed(2) || 0
                    }
                    if (row.taxedPriceUnit && row.demandQuantity) {
                      row.untaxedPriceTotal =
                        (Number(row.untaxedPriceUnit) * Number(row.demandQuantity)).toFixed(2) || 0
                      row.taxedPriceTotal =
                        (Number(row.taxedPriceUnit) * Number(row.demandQuantity)).toFixed(2) || 0
                    }
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'untaxedPriceUnit',
          title: this.$t('不含税单价'),
          minWidth: 150
        },
        {
          field: 'untaxedPriceTotal',
          title: this.$t('不含税总价'),
          minWidth: 150
        },
        {
          field: 'taxedPriceUnit',
          title: this.$t('含税单价'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.taxedPriceUnit}
                  placeholder={this.$t('请输入')}
                  min='0'
                  transfer
                  onChange={() => {
                    if (row.taxRate) {
                      // 未税单价
                      row.untaxedPriceUnit =
                        (Number(row.taxedPriceUnit) / (1 + Number(row.taxRate))).toFixed(2) || 0
                    }
                    if (row.taxRate && row.demandQuantity) {
                      row.untaxedPriceTotal =
                        (Number(row.untaxedPriceUnit) * Number(row.demandQuantity)).toFixed(2) || 0
                      row.taxedPriceTotal =
                        (Number(row.taxedPriceUnit) * Number(row.demandQuantity)).toFixed(2) || 0
                    }
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'taxedPriceTotal',
          title: this.$t('含税总价')
        },
        {
          field: 'priceStartTime',
          title: this.$t('价格开始时间'),
          minWidth: 180,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              const dateDisabled = (params) => {
                const { date } = params
                return date < new Date()
              }
              return [
                <vxe-input
                  type={'date'}
                  v-model={row.priceStartTime}
                  placeholder={this.$t('请选择')}
                  transfer
                  clearable
                  label-format='yyyy-MM-dd'
                  value-format='yyyy-MM-dd'
                  disabled-method={dateDisabled}
                />
              ]
            }
          }
        },
        {
          field: 'priceEndTime',
          title: this.$t('价格结束时间'),
          minWidth: 180,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              const dateDisabled = (params) => {
                const { date } = params
                return date < new Date()
              }
              return [
                <vxe-input
                  type={'date'}
                  v-model={row.priceEndTime}
                  placeholder={this.$t('请选择')}
                  transfer
                  clearable
                  label-format='yyyy-MM-dd'
                  value-format='yyyy-MM-dd'
                  disabled-method={dateDisabled}
                />
              ]
            }
          }
        },
        {
          field: 'rdcFlag',
          title: this.$t('是否入RDC仓'),
          minWidth: 180,
          editRender: {},
          slots: {
            default: ({ row }) => {
              let selectedItem = this.whetherMapList.find((v) => v.value === row.rdcFlag)
              return [selectedItem ? <div>{selectedItem.label}</div> : null]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.rdcFlag}
                  options={this.whetherMapList}
                  placeholder={this.$t('请选择')}
                  clearable
                  filterable
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'warehouseCode',
          title: this.$t('入库仓库'),
          editRender: {},
          minWidth: 180,
          slots: {
            default: ({ row }) => {
              return [
                row.warehouseCode ? <div> {row.warehouseCode + '-' + row.warehouseName}</div> : null
              ]
            },
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.warehouseCode}
                  disabled={
                    this.editableType === 2 ||
                    this.editableType === 3 ||
                    this.isSup ||
                    row.deliveryCompletedFlag === 1 ||
                    row.closeStatus === 1
                  }
                  placeholder={this.$t('请选择')}
                  fields={{ text: 'warehouseName', value: 'warehouseCode' }}
                  request-info={{
                    urlPre: 'purchaseOrderMgt',
                    url: 'queryPvWarehouseList',
                    searchKey: 'fuzzyParam',
                    params: {
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  onChange={(e) => {
                    row.warehouseName = e?.warehouseName || null
                    row.receiveUserCode = e?.contactor || null
                    row.contactPhone = e?.contactPhone || null
                    row.receiveAddress = e?.addr || null
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'receiveUserCode',
          title: this.$t('收货人'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [
                // row.receiveUserCode ? (
                //   <div> {row.receiveUserCode + '-' + row.receiveUserName}</div>
                // ) : null
                row.receiveUserCode
              ]
            },
            edit: ({ row }) => {
              // return [
              //   <VxeRemoteSearch
              //     v-model={row.receiveUserCode}
              //     placeholder={this.$t('请选择')}
              //     fields={{ text: 'employeeName', value: 'employeeCode' }}
              //     request-info={{
              //       urlPre: 'masterData',
              //       url: 'getUserPageList',
              //       searchFields: ['employeeCode', 'employeeName'],
              //       params: {
              //         page: {
              //           current: 1,
              //           size: 50
              //         }
              //       },
              //       recordsPosition: 'data.records'
              //     }}
              //     onChange={(e) => {
              //       row.receiveUserName = e?.employeeName || null
              //     }}
              //   />
              // ]
              return [
                <vxe-input
                  v-model={row.receiveUserCode}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'contactPhone',
          title: this.$t('联系方式'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.contactPhone}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'receiveAddress',
          title: this.$t('收货地址'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.receiveAddress}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'categoryCode',
          title: this.$t('品类'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [
                row.categoryCode ? <div> {row.categoryCode + '-' + row.categoryName}</div> : null
              ]
            },
            edit: ({ row }) => {
              return [
                row.categoryCode ? <div> {row.categoryCode + '-' + row.categoryName}</div> : null
              ]
            }
          }
        },

        {
          field: 'largeCategoryCode',
          title: this.$t('大类编码'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.largeCategoryCode}
                  placeholder={this.$t('请输入')}
                  disabled
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'mediumCategoryCode',
          title: this.$t('中类编码'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.mediumCategoryCode}
                  placeholder={this.$t('请输入')}
                  disabled
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'smallCategoryCode',
          title: this.$t('小类编码'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.smallCategoryCode}
                  placeholder={this.$t('请输入')}
                  disabled
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'remark',
          title: this.$t('备注'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.remark}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        }
      ]
    },
    editConfig() {
      return {
        enabled:
          this.pageType !== 'detail' &&
          [0, 3].includes(this.dataInfo.contractStatus) &&
          !this.$route.path.includes('-sup'),
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    toolbar() {
      let btns = [{ code: 'export', name: this.$t('导出'), status: 'info', loading: false }]
      if (
        this.pageType !== 'detail' &&
        [0, 3].includes(this.dataInfo.contractStatus) &&
        !this.$route.path.includes('-sup')
      ) {
        btns = [
          // {
          //   code: 'select',
          //   name: this.$t('批量选择物料'),
          //   status: 'info',
          //   loading: false
          // },
          { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
          { code: 'delete', name: this.$t('删除'), status: 'info', loading: false },
          { code: 'import', name: this.$t('导入'), status: 'info', loading: false },
          { code: 'export', name: this.$t('导出'), status: 'info', loading: false }
        ]
      }
      return btns
    }
  },
  created() {
    this.getUnitList()
    this.getTaxList()
    if (this.pageType !== 'create') {
      this.initTableData()
    }
  },
  methods: {
    async setPriceInfo(row) {
      const params = {
        currencyCode: this.dataInfo.currencyCode,
        itemCode: row.itemCode,
        siteCode: row.siteCode,
        supplierCode: this.dataInfo.supplierCode
      }
      const res = await this.$API.contract.queryPvPrice(params)
      const { code, data } = res
      if (code === 200 && data) {
        row.priceUnit = data?.priceUnit || null
        row.taxRate = data?.taxRate || null
        row.taxCode = data?.taxCode || null
        row.taxName = data?.taxName || null
        row.untaxedPriceUnit = data?.untaxedUnitPrice || null
        row.priceStartTime = data?.validStartTime || null
        row.priceEndTime = data?.validEndTime || null
      }
    },
    setItemInfo(e, row) {
      row.itemCode = e?.itemCode || null
      row.itemName = e?.itemName || null
      row.categoryCode = e?.categoryCode || null
      row.categoryName = e?.categoryName || null
      row.baseUnitCode = e?.baseMeasureUnitCode || null
      row.baseUnitName = e?.baseMeasureUnitName || null
      if (e?.categoryResponse) {
        row.largeCategoryCode = e?.categoryResponse?.businessTypeCode || null
        row.largeCategoryName = e?.categoryResponse?.businessTypeName || null
        row.mediumCategoryCode = e?.categoryResponse?.categoryTypeCode || null
        row.mediumCategoryName = e?.categoryResponse?.categoryTypeName || null
        row.smallCategoryCode = e?.categoryResponse?.categoryCode || null
        row.smallCategoryName = e?.categoryResponse?.categoryName || null
      }
    },
    async getTaxList() {
      const res = await this.$API.masterData.queryAllTaxItem()
      this.taxRateNameList =
        res?.data?.map((i) => {
          return {
            ...i,
            label: i.taxItemCode + '-' + i.taxItemName,
            value: i.taxItemCode
          }
        }) || []
    },
    async getUnitList() {
      const res = await this.$API.masterData.pagedQueryUnit()
      let _res = res?.data.records || []
      let s = []
      let o = []
      _res.forEach((item) => {
        item.label = `${item.unitCode}-${item.unitName}`
        item.value = item.unitCode
        if (item.unitCode === 'G' || item.unitCode === 'KG') {
          s.push(item)
        } else {
          o.push(item)
        }
      })
      let n = [...s, ...o]
      this.unitNameList = n || []
    },
    // 初始化获取数据
    async initTableData() {
      this.tableData =
        this.dataInfo?.detailList?.map((i) => {
          return {
            ...i,
            priceUnit: i.priceUnit ? String(i.priceUnit) : ''
          }
        }) || []
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['delete', 'select']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'select':
          this.handleSelect(selectedRecords)
          break
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          this.handleDelete()
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleSelect(selectedRecords) {
      this.$dialog({
        modal: () => import('./selectItemGrid'),
        data: {
          title: this.$t('选择物料')
        },
        success: (data) => {
          selectedRecords.forEach((row) => {
            this.setItemInfo(data, row)
          })
          // if (data.length) {
          //   data.forEach((item) => {
          //     this.handleAdd({
          //       itemCode: item.itemCode,
          //       itemName: item.itemName
          //     })
          //   })
          // }
        }
      })
    },
    handleAdd(item = {}) {
      this.tableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.tableRef.setEditRow(currentViewRecords[0])
      })
    },
    editComplete(args) {
      // const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        //1、 校验必填
        //this.tableRef.validate([row]).then((valid) => {
        //  if (valid) {
        //    this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
        //    return
        //  }
        //
        //  const currentViewRecords = this.tableRef.getTableData().visibleData
        //  this.$emit('itemDataChange', currentViewRecords)
        //})
        const currentViewRecords = this.tableRef.getTableData().visibleData
        this.$emit('itemDataChange', currentViewRecords)
      }
    },
    handleDelete() {
      this.tableRef.removeCheckboxRow()
      const currentViewRecords = this.tableRef.getTableData().visibleData
      this.$emit('itemDataChange', currentViewRecords)
    },
    handleImport() {
      const currentViewRecords = this.tableRef.getTableData().visibleData
      if (currentViewRecords.some((i) => i.id?.includes('row_'))) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t(`存在未保存的行数据将会被覆盖，是否继续导入？`)
          },
          success: async () => {
            this.importAction()
          }
        })
        return
      }
      this.importAction()
    },
    importAction() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.contract.importContractItemDetail,
          downloadTemplateApi: this.$API.contract.getImportTemplate,
          paramsKey: 'excel',
          asyncParams: { id: this.$route.query.id },
          downloadTemplateParams: { id: this.$route.query.id }
        },
        success: () => {
          this.$emit('updateItemList')
        }
      })
    },
    handleExport(e) {
      const params = { id: this.$route.query.id }
      e.loading = true
      this.$API.contract
        .exportContractItemDetail(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>
