import { i18n } from '@/main'
// 订单状态
export const orderStatusList = [
  { value: 0, text: i18n.t('草稿') },
  { value: 1, text: i18n.t('待供应商确认') },
  { value: 2, text: i18n.t('供应商已拒绝') },
  { value: 3, text: i18n.t('待采购确认') },
  { value: 4, text: i18n.t('采购已拒绝') },
  { value: 5, text: i18n.t('双方已确认') },
  { value: 6, text: i18n.t('已完成') },
  { value: 7, text: i18n.t('已关闭') }
]
// 业务类型
export const businessTypeList = [
  { value: 1, text: i18n.t('户用业务') },
  { value: 2, text: i18n.t('商用业务') },
  { value: 3, text: i18n.t('海外业务') }
]
// 加急状态
export const urgentStatusList = [
  { value: 0, text: i18n.t('未加急'), label: i18n.t('未加急') },
  { value: 1, text: i18n.t('已加急'), label: i18n.t('已加急') }
]
// 发货状态
export const deliveryStatusList = [
  { value: 0, text: i18n.t('未发货') },
  { value: 1, text: i18n.t('部分发货') },
  { value: 2, text: i18n.t('全部发货') }
]
// 收货状态
export const receiveStatusList = [
  { value: 0, text: i18n.t('未收货') },
  { value: 1, text: i18n.t('部分收货') },
  { value: 2, text: i18n.t('全部收货') }
]
// 入库状态
export const warehouseStatusList = [
  { value: 0, text: i18n.t('未入库') },
  { value: 1, text: i18n.t('部分入库') },
  { value: 2, text: i18n.t('全部入库') }
]
const getMapValue = (list, value) => {
  return list.find((item) => item.value === value).text
}
//详情 - 物料明细 - 列表字段
export const purchaseOrderColumns = [
  { option: 'checkboxSelection', width: 55 },
  {
    field: 'lineNo',
    headerName: i18n.t('行号'),
    width: 70,
    valueFormatter: function (params) {
      return params.node.rowIndex + 1 // 使用rowIndex同样可以显示正确的行号
    }
  },
  {
    field: 'orderCode',
    headerName: i18n.t('采购订单号')
  },
  {
    field: 'itemNo',
    headerName: i18n.t('采购订单行号')
  },
  {
    field: 'orderStatus',
    headerName: i18n.t('订单状态'),
    valueFormatter: function (params) {
      return getMapValue(orderStatusList, params.data.orderStatus)
    }
  },
  {
    field: 'itemCode',
    headerName: i18n.t('物料编码'),
    width: 155
  },
  {
    field: 'itemName',
    headerName: i18n.t('物料名称')
  },
  {
    field: 'specificationModel',
    headerName: i18n.t('规格型号')
  },
  {
    field: 'requiredDeliveryDate',
    headerName: i18n.t('要求交期')
  },
  {
    field: 'quantity',
    headerName: i18n.t('订单数量')
  },
  {
    field: 'preDeliveryQty',
    headerName: i18n.t('待发货数量')
  },
  {
    field: 'transitQty',
    headerName: i18n.t('在途数量')
  },
  {
    field: 'warehouseQty',
    headerName: i18n.t('已入库数量')
  },
  {
    field: 'taxRate',
    headerName: i18n.t('税率(%)')
  },
  {
    field: 'untaxedPrice',
    headerName: i18n.t('不含税单价')
  },
  {
    field: 'untaxedPriceTotal',
    headerName: i18n.t('不含税总价')
  },
  {
    field: 'taxedPrice',
    headerName: i18n.t('含税单价')
  },
  {
    field: 'taxedPriceTotal',
    headerName: i18n.t('含税总价')
  },
  {
    field: 'businessType',
    headerName: i18n.t('业务类型'),
    valueFormatter: function (params) {
      return getMapValue(businessTypeList, params.data.businessType)
    }
  },
  {
    field: 'orderDate',
    headerName: i18n.t('订单日期')
  },
  {
    field: 'companyCode',
    headerName: i18n.t('公司编码'),
    valueFormatter: (params) => {
      return params.data.companyCode
        ? params.data.companyCode + '-' + params.data.companyName
        : null
    }
  },
  {
    field: 'purchaseOrgCode',
    headerName: i18n.t('采购组织编码'),
    valueFormatter: (params) => {
      return params.data.purchaseOrgCode
        ? params.data.purchaseOrgCode + '-' + params.data.purchaseOrgName
        : null
    }
  },
  {
    field: 'supplierCode',
    headerName: i18n.t('供应商编码'),
    valueFormatter: (params) => {
      return params.data.supplierCode
        ? params.data.supplierCode + '-' + params.data.supplierName
        : null
    }
  },
  {
    field: 'urgentStatus',
    headerName: i18n.t('加急状态'),
    valueFormatter: function (params) {
      return getMapValue(urgentStatusList, params.data.urgentStatus)
    }
  },
  {
    field: 'urgentDate',
    headerName: i18n.t('加急日期')
  },
  {
    field: 'deliveryStatus',
    headerName: i18n.t('发货状态'),
    valueFormatter: function (params) {
      return getMapValue(deliveryStatusList, params.data.deliveryStatus)
    }
  },
  {
    field: 'receiveStatus',
    headerName: i18n.t('收货状态'),
    valueFormatter: function (params) {
      return getMapValue(receiveStatusList, params.data.receiveStatus)
    }
  },
  {
    field: 'warehouseStatus',
    headerName: i18n.t('入库状态'),
    valueFormatter: function (params) {
      return getMapValue(warehouseStatusList, params.data.warehouseStatus)
    }
  },
  {
    field: 'currencyCode',
    headerName: i18n.t('币种编码'),
    valueFormatter: (params) => {
      return params.data.currencyCode
        ? params.data.currencyCode + '-' + params.data.currencyName
        : null
    }
  },
  {
    field: 'paymentType',
    headerName: i18n.t('付款方式'),
    valueFormatter: (params) => {
      return params.data.paymentType
        ? params.data.paymentType + '-' + params.data.paymentTypeName
        : null
    }
  },
  {
    field: 'promiseTime',
    headerName: i18n.t('承诺日期')
  },
  {
    field: 'promiseQty',
    headerName: i18n.t('承诺数量')
  },
  {
    field: 'categoryCode',
    headerName: i18n.t('品类编码'),
    valueFormatter: (params) => {
      return params.data.categoryCode
        ? params.data.categoryCode + '-' + params.data.categoryName
        : null
    }
  },
  {
    field: 'largeCategoryName',
    headerName: i18n.t('大类编码'),
    valueFormatter: (params) => {
      return params.data.largeCategoryCode
        ? params.data.largeCategoryCode + '-' + params.data.largeCategoryName
        : null
    }
  },
  {
    field: 'mediumCategoryCode',
    headerName: i18n.t('中类编码'),
    valueFormatter: (params) => {
      return params.data.mediumCategoryCode
        ? params.data.mediumCategoryCode + '-' + params.data.mediumCategoryName
        : null
    }
  },
  {
    field: 'smallCategoryCode',
    headerName: i18n.t('小类编码'),
    valueFormatter: (params) => {
      return params.data.smallCategoryCode
        ? params.data.smallCategoryCode + '-' + params.data.smallCategoryName
        : null
    }
  }
]

//详情 - 物料明细 - 操作按钮
export const purchaseOrderToolbar = () => [
  {
    id: 'export',
    title: i18n.t('导出')
  }
]
