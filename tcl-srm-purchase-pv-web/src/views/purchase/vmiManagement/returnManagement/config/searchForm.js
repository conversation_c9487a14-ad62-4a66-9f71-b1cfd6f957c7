/**
 * VMI退货管理搜索表单配置
 */

export const getSearchFormItems = ({ statusOptions, returnTypeOptions }) => [
  {
    label: 'SRM退货单号',
    prop: 'returnCode',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '退货状态',
    prop: 'returnStatus',
    component: 'mt-select',
    props: {
      placeholder: '请选择',
      dataSource: statusOptions,
      showClearButton: true
    }
  },
  {
    label: '入库单号',
    prop: 'stockInCode',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '工厂编码',
    prop: 'siteCode',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '入库仓库编码',
    prop: 'warehouseCode',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '供应商编码',
    prop: 'supplierCode',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '退货类型',
    prop: 'returnType',
    component: 'mt-select',
    props: {
      placeholder: '请选择',
      dataSource: returnTypeOptions,
      showClearButton: true
    }
  },
  {
    label: '退货地址',
    prop: 'returnAddress',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '退货人',
    prop: 'returnPerson',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '退货人手机号',
    prop: 'returnPersonPhone',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '创建人',
    prop: 'createUserName',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '创建时间',
    prop: 'createTime',
    component: 'mt-date-range-picker',
    props: { placeholder: '请选择' }
  }
]

export const getDetailSearchFormItems = ({ statusOptions }) => [
  {
    label: 'SRM退货单号',
    prop: 'returnCode',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '退货状态',
    prop: 'returnStatus',
    component: 'mt-select',
    props: {
      placeholder: '请选择',
      dataSource: statusOptions,
      showClearButton: true
    }
  },
  {
    label: '入库单号',
    prop: 'stockInCode',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '工厂编码',
    prop: 'siteCode',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '入库仓库编码',
    prop: 'warehouseCode',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '供应商编码',
    prop: 'supplierCode',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '物料编码',
    prop: 'itemCode',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '物料名称',
    prop: 'itemName',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '品类编码',
    prop: 'categoryCode',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '创建人',
    prop: 'createUserName',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '创建时间',
    prop: 'createTime',
    component: 'mt-date-range-picker',
    props: { placeholder: '请选择' }
  }
]
