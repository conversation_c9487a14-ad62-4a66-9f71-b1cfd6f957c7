<template>
  <div>
    <mt-dialog
      ref="dialog"
      :buttons="buttons"
      :header="header"
      @beforeClose="cancel"
      width="360px"
      height="180px"
    >
      <div style="padding: 20px">
        <mt-form ref="formRef" :model="modelForm" :rules="formRules">
          <mt-form-item prop="operationType" :label="$t('操作类型')" label-style="left">
            <mt-select
              v-model="modelForm.operationType"
              :data-source="operationTypeOptions"
              :fields="{ text: 'text', value: 'value' }"
              :placeholder="$t('请选择')"
              :allow-filtering="true"
              filter-type="Contains"
              :show-clear-button="true"
            />
          </mt-form-item>
        </mt-form>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import { operationTypeOptions } from '../config/index'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      modelForm: {
        operationType: ''
      },
      formRules: {
        operationType: [
          {
            required: true,
            message: this.$t('请选择操作类型'),
            trigger: 'blur'
          }
        ]
      },
      operationTypeOptions
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    confirm() {
      this.$refs.formRef.validate(async (valid) => {
        if (!valid) {
          return
        }
        this.$emit('confirm-function', this.modelForm.operationType)
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style></style>
