/**
 * 出库记录搜索表单配置
 */
import { i18n } from '@/main.js'

export const getSearchFormItems = () => [
  {
    label: i18n.t('极光单号'),
    prop: 'jgOrderCode',
    component: 'mt-input',
    props: { placeholder: i18n.t('请输入'), showClearButton: true }
  },
  {
    label: i18n.t('省'),
    prop: 'province',
    component: 'mt-input',
    props: { placeholder: i18n.t('请输入'), showClearButton: true }
  },
  {
    label: i18n.t('区'),
    prop: 'city',
    component: 'mt-input',
    props: { placeholder: i18n.t('请输入'), showClearButton: true }
  },
  {
    label: i18n.t('仓库编码'),
    prop: 'warehouseCodeList',
    component: 'RemoteAutocomplete',
    props: {
      placeholder: i18n.t('请选择'),
      url: '/srm-purchase-pv/tenant/pv/warehouse/page/query',
      multiple: true,
      fields: { text: 'warehouseName', value: 'warehouseCode' },
      paramsKey: 'fuzzyParam',
      showClearButton: true
    }
  },
  {
    label: i18n.t('供应商编码'),
    prop: 'supplierCodeList',
    component: 'RemoteAutocomplete',
    props: {
      placeholder: i18n.t('请选择'),
      url: '/masterDataManagement/tenant/supplier/paged-query-distinct-no-scope',
      multiple: true,
      fields: { text: 'supplierName', value: 'supplierCode' },
      searchFields: ['supplierName', 'supplierCode'],
      showClearButton: true
    }
  },
  {
    label: i18n.t('品类名称'),
    prop: 'catoryName',
    component: 'mt-input',
    props: { placeholder: i18n.t('请输入'), showClearButton: true }
  },
  {
    label: i18n.t('物料编码'),
    prop: 'itemCodeStr',
    component: 'mt-input',
    props: { placeholder: i18n.t('支持粘贴多个精准及单个右模糊'), showClearButton: true }
  },
  {
    label: i18n.t('单位名称'),
    prop: 'unitName',
    component: 'mt-input',
    props: { placeholder: i18n.t('请输入'), showClearButton: true }
  },
  {
    label: i18n.t('规格'),
    prop: 'specificationModel',
    component: 'mt-input',
    props: { placeholder: i18n.t('请输入'), showClearButton: true }
  },
  {
    label: i18n.t('出库时间'),
    prop: 'outTime',
    component: 'mt-date-range-picker',
    props: { placeholder: i18n.t('请选择') }
  },
  {
    label: i18n.t('是否转订单'),
    prop: 'toOrderFlag',
    component: 'mt-select',
    props: {
      placeholder: i18n.t('请选择'),
      showClearButton: true,
      dataSource: [
        { text: i18n.t('是'), value: 'Y' },
        { text: i18n.t('否'), value: 'N' }
      ]
    }
  },
  {
    label: i18n.t('创建人'),
    prop: 'createUser',
    component: 'mt-input',
    props: { placeholder: i18n.t('请输入'), showClearButton: true }
  },
  {
    label: i18n.t('创建时间'),
    prop: 'createDate',
    component: 'mt-date-range-picker',
    props: { placeholder: i18n.t('请选择') }
  }
]
