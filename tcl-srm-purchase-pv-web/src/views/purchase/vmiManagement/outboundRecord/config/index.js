import { i18n } from '@/main.js'

export const toolbar = [
  {
    code: 'getPrice',
    name: i18n.t('获取价格'),
    status: 'info',
    loading: false,
    permission: ['O_02_1877']
  },
  {
    code: 'transferOrder',
    name: i18n.t('转订单'),
    status: 'info',
    loading: false,
    permission: ['O_02_1878']
  },
  {
    code: 'export',
    name: i18n.t('导出'),
    status: 'info',
    loading: false,
    permission: ['O_02_1879']
  }
]

export const columns = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  // {
  //   field: 'outCode',
  //   title: i18n.t('出库单号'),
  //   minWidth: 120
  // },
  {
    field: 'jgOrderCode',
    title: i18n.t('极光单号'),
    minWidth: 120
  },
  // {
  //   field: 'outLineCode',
  //   title: i18n.t('出库单行号'),
  //   minWidth: 120
  // },
  {
    field: 'province',
    title: i18n.t('省区'),
    formatter: ({ cellValue, row }) => {
      return row.city ? cellValue + '-' + row.city : cellValue
    }
  },
  {
    field: 'businessType',
    title: i18n.t('仓库类型')
  },
  {
    field: 'warehouseCode',
    title: i18n.t('仓库编码')
  },
  {
    field: 'warehouseName',
    title: i18n.t('仓库名称')
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码'),
    minWidth: 120
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    minWidth: 120
  },
  {
    field: 'catoryCode',
    title: i18n.t('品类'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.catoryName : row.catoryName
    }
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称')
  },
  {
    field: 'unitCode',
    title: i18n.t('单位'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.unitName : row.unitName
    }
  },
  {
    field: 'specificationModel',
    title: i18n.t('规格')
  },
  {
    field: 'outQty',
    title: i18n.t('出库数量')
  },
  {
    field: 'taxCode',
    title: i18n.t('税率编码')
  },
  {
    field: 'taxName',
    title: i18n.t('税率名称')
  },
  {
    field: 'taxRate',
    title: i18n.t('税率')
  },
  {
    field: 'untaxedPrice',
    title: i18n.t('不含税单价'),
    minWidth: 120
  },
  {
    field: 'taxedPrice',
    title: i18n.t('含税单价')
  },
  {
    field: 'untaxedPriceTotal',
    title: i18n.t('不含税总价'),
    minWidth: 120
  },
  {
    field: 'taxedPriceTotal',
    title: i18n.t('含税总价')
  },
  {
    field: 'outTime',
    title: i18n.t('出库时间'),
    minWidth: 160
  },
  {
    field: 'toOrderFlag',
    title: i18n.t('是否转订单'),
    minWidth: 120,
    formatter: ({ cellValue }) => {
      return cellValue === 'Y' ? i18n.t('是') : cellValue === 'N' ? i18n.t('否') : '-'
    }
  },
  {
    field: 'orderCode',
    title: i18n.t('采购订单号'),
    minWidth: 120
  },
  {
    field: 'toOrderMsg',
    title: i18n.t('转订单消息'),
    minWidth: 120
  },
  {
    field: 'createUser',
    title: i18n.t('创建人')
  },
  {
    field: 'createDate',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'pushTime',
    title: i18n.t('推送时间'),
    minWidth: 160
  }
]
