<!-- 领料出库记录 -->
<template>
  <div class="outbound-record">
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <template v-for="(item, index) in searchFormItems">
          <mt-form-item :key="index" :label="$t(item.label)" :prop="item.prop">
            <component
              :is="item.component"
              v-model="searchFormModel[item.prop]"
              v-bind="item.props"
              @change="item.onChange && item.onChange($event, item.prop)"
            />
          </mt-form-item>
        </template>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="scTableRef"
      row-id="id"
      grid-id="79a0ac59-990a-6e59-a5aa-c63310e406df"
      show-overflow
      keep-source
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template #custom-tools>
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          v-bind="item"
          v-permission="item.permission"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import { columns, toolbar } from './config/index'
import { getSearchFormItems } from './config/searchForm'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  name: 'OutboundRecord',
  components: { CollapseSearch, ScTable, RemoteAutocomplete },

  data() {
    return {
      loading: false,
      searchFormModel: {},
      searchFormRules: {},
      tableData: [],
      columns,
      toolbar,
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      }
    }
  },

  computed: {
    tableRef() {
      return this.$refs.scTableRef?.$refs.xGrid
    },
    searchFormItems() {
      const items = getSearchFormItems()
      // 为创建时间、出库时间字段添加onChange事件处理
      const dateFields = ['createDate', 'outTime']
      dateFields.forEach((field) => {
        const item = items.find((item) => item.prop === field)
        if (item) {
          item.onChange = this.handleDateTimeChange
        }
      })
      return items
    }
  },

  created() {
    this.initData()
  },

  methods: {
    async initData() {
      await this.getTableData()
    },

    handleDateTimeChange(e, field) {
      if (!e?.startDate) {
        this.searchFormModel[`${field}Start`] = null
        this.searchFormModel[`${field}End`] = null
        return
      }

      this.searchFormModel[`${field}Start`] = this.getUnix(
        dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
      )
      this.searchFormModel[`${field}End`] = this.getUnix(
        dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
      )
    },

    getUnix(val) {
      return dayjs(val).valueOf()
    },

    handleReset() {
      this.searchFormModel = {}
      this.handleSearch()
    },

    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },

    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },

    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },

    async getTableData() {
      try {
        this.loading = true
        const params = {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        }

        const res = await this.$API.vmiManagement.pageOutboundRecordApi(params)

        if (res.code === 200) {
          const { total = 0, records = [] } = res.data
          this.pageSettings.totalRecordsCount = Number(total)
          this.pageSettings.totalPages = Math.ceil(total / this.pageSettings.pageSize)
          this.tableData = records
        }
      } catch (error) {
        console.error('获取表格数据失败:', error)
        this.$toast({ content: error || this.$t('获取数据失败'), type: 'error' })
      } finally {
        this.loading = false
      }
    },

    async handleClickToolBar(item) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (selectedRecords.length <= 0 && ['getPrice', 'transferOrder'].includes(item.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const actionMap = {
        getPrice: () =>
          this.showConfirmDialog('获取价格', () => this.handleGetPrice(selectedRecords)),
        transferOrder: () =>
          this.showConfirmDialog('转订单', () => this.handleTransferOrder(selectedRecords)),
        export: () => this.showConfirmDialog('导出', () => this.handleExport(item))
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },

    showConfirmDialog(action, callback) {
      return this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认${action}？`)
        },
        success: () => {
          if (typeof callback === 'function') {
            callback()
          }
        }
      })
    },

    async handleGetPrice(selectedRecords) {
      const params = {
        ids: selectedRecords.map((item) => item.id)
      }
      const res = await this.$API.vmiManagement.getPriceOutboundRecordApi(params)
      if (res.code === 200) {
        this.$toast({ content: this.$t('获取价格成功'), type: 'success' })
        this.handleSearch()
      } else {
        this.$toast({ content: res.message || this.$t('获取价格失败'), type: 'error' })
      }
    },

    async handleTransferOrder(selectedRecords) {
      const params = selectedRecords.map((item) => item.jgOrderCode)
      const res = await this.$API.vmiManagement.transferOrderOutboundRecordApi(params)
      if (res.code === 200) {
        this.$toast({ content: this.$t('转订单成功'), type: 'success' })
        this.handleSearch()
      } else {
        this.$toast({ content: res.message || this.$t('转订单失败'), type: 'error' })
      }
    },

    async handleExport(item) {
      try {
        item.loading = true
        const params = {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        }

        const res = await this.$API.vmiManagement.exportOutboundRecordApi(params)
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      } catch (error) {
        console.error('导出失败:', error)
        this.$toast({ content: error || this.$t('导出失败'), type: 'error' })
      } finally {
        item.loading = false
      }
    }
  }
}
</script>
