<!-- 明细 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item
          v-for="(item, index) in searchFormItems"
          :key="index"
          :label="$t(item.label)"
          :prop="item.prop"
        >
          <component
            :is="item.component"
            v-model="searchFormModel[item.prop]"
            v-bind="item.props"
            @change="item.onChange && item.onChange($event, item.prop)"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="scTableRef"
      row-id="id"
      grid-id="8d280919-7542-4bee-a9e8-cae7627f41d4"
      show-overflow
      keep-source
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template #custom-tools>
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          v-bind="item"
          v-permission="item.permission"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import { detailColumns, detailToolbar, statusOptions } from '../config/index'
import { getHeadersFileName, download } from '@/utils/utils'

export default {
  name: 'DeliveryPlanDetail',
  components: { CollapseSearch, ScTable, RemoteAutocomplete },

  data() {
    return {
      loading: false,
      searchFormModel: {},
      searchFormRules: {},
      tableData: [],
      columns: detailColumns,
      toolbar: detailToolbar,
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      statusOptions
    }
  },

  computed: {
    tableRef() {
      return this.$refs.scTableRef?.$refs.xGrid
    },
    searchFormItems() {
      return [
        {
          label: this.$t('送货计划单号'),
          prop: 'deliveryPlanCode',
          component: 'mt-input',
          props: { placeholder: this.$t('请输入'), showClearButton: true }
        },
        {
          label: this.$t('省'),
          prop: 'province',
          component: 'mt-input',
          props: { placeholder: this.$t('请输入'), showClearButton: true }
        },
        {
          label: this.$t('区'),
          prop: 'city',
          component: 'mt-input',
          props: { placeholder: this.$t('请输入'), showClearButton: true }
        },
        {
          label: this.$t('仓库编码'),
          prop: 'warehouseCode',
          component: 'mt-input',
          props: { placeholder: this.$t('请输入'), showClearButton: true }
        },
        {
          label: this.$t('状态'),
          prop: 'statusList',
          component: 'mt-multi-select',
          props: {
            placeholder: this.$t('请选择'),
            dataSource: this.statusOptions,
            showClearButton: true
          }
        },
        {
          label: this.$t('仓库名称'),
          prop: 'warehouseName',
          component: 'mt-input',
          props: { placeholder: this.$t('请输入'), showClearButton: true }
        },
        {
          label: this.$t('仓库地址'),
          prop: 'warehouseAddr',
          component: 'mt-input',
          props: { placeholder: this.$t('请输入'), showClearButton: true }
        },
        {
          label: this.$t('供应商'),
          prop: 'supplierCodeList',
          component: 'RemoteAutocomplete',
          props: {
            placeholder: this.$t('请选择'),
            showClearButton: true,
            url: '/masterDataManagement/tenant/supplier/paged-query-distinct-no-scope',
            multiple: true,
            fields: { text: 'supplierName', value: 'supplierCode' },
            searchFields: ['supplierName', 'supplierCode']
          }
        },
        {
          label: this.$t('品类'),
          prop: 'catoryCodeList',
          component: 'RemoteAutocomplete',
          props: {
            placeholder: this.$t('请选择'),
            showClearButton: true,
            url: '/masterDataManagement/tenant/category/paged-query',
            multiple: true,
            fields: { text: 'categoryName', value: 'categoryCode' },
            searchFields: ['categoryName', 'categoryCode']
          }
        },
        {
          label: this.$t('物料编码'),
          prop: 'itemCodeStr',
          component: 'mt-input',
          props: { placeholder: this.$t('请输入'), showClearButton: true }
        },
        {
          label: this.$t('规格'),
          prop: 'specificationModel',
          component: 'mt-input',
          props: { placeholder: this.$t('请输入'), showClearButton: true }
        },
        {
          label: this.$t('创建人'),
          prop: 'createUserName',
          component: 'mt-input',
          props: { placeholder: this.$t('请输入'), showClearButton: true }
        },
        {
          label: this.$t('创建时间'),
          prop: 'createTime',
          component: 'mt-date-range-picker',
          props: { placeholder: this.$t('请选择') },
          onChange: (e) => this.dateTimeChange(e, 'createTime')
        }
      ]
    }
  },

  created() {
    this.initData()
  },

  methods: {
    async initData() {
      await this.getTableData()
    },

    dateTimeChange(e, field) {
      if (!e?.startDate) {
        this.searchFormModel[`${field}S`] = null
        this.searchFormModel[`${field}E`] = null
        return
      }

      this.searchFormModel[`${field}S`] = this.getUnix(
        dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
      )
      this.searchFormModel[`${field}E`] = this.getUnix(
        dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
      )
    },

    getUnix(val) {
      return dayjs(val).valueOf()
    },

    handleReset() {
      this.searchFormModel = {}
      this.handleSearch()
    },

    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },

    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },

    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },

    async getTableData() {
      try {
        this.loading = true
        const params = {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        }

        const res = await this.$API.vmiManagement.pageDeliveryPlanDetailApi(params)

        if (res.code === 200) {
          const { total = 0, records = [] } = res.data
          this.pageSettings.totalRecordsCount = Number(total)
          this.pageSettings.totalPages = Math.ceil(total / this.pageSettings.pageSize)
          this.tableData = records
        }
      } catch (error) {
        console.error('获取表格数据失败:', error)
        this.$toast({ content: error || this.$t('获取数据失败'), type: 'error' })
      } finally {
        this.loading = false
      }
    },

    async handleClickToolBar(item) {
      const actionMap = {
        export: () => this.handleExport(item)
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },

    async handleExport(item) {
      try {
        item.loading = true
        const params = {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        }

        const res = await this.$API.vmiManagement.exportDeliveryPlanDetailApi(params)
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      } catch (error) {
        console.error('导出失败:', error)
        this.$toast({ content: error || this.$t('导出失败'), type: 'error' })
      } finally {
        item.loading = false
      }
    }
  }
}
</script>
