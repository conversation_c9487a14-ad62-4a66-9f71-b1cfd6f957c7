<!-- 列表 -->
<template>
  <div class="delivery-plan-list">
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item
          v-for="(item, index) in searchFormItems"
          :key="index"
          :label="$t(item.label)"
          :prop="item.prop"
        >
          <component
            :is="item.component"
            v-model="searchFormModel[item.prop]"
            v-bind="item.props"
            @change="item.onChange && item.onChange($event, item.prop)"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="scTableRef"
      row-id="id"
      grid-id="3243637a-9351-41ac-53f3-635795cd256c"
      show-overflow
      keep-source
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template #custom-tools>
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          v-bind="item"
          v-permission="item.permission"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #deliveryPlanCodeDefault="{ row }">
        <div style="cursor: pointer; color: #2783fe" @click="handleClick(row)">
          {{ row.deliveryPlanCode }}
        </div>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import { listColumns, listToolbar, statusOptions } from '../config/index'
import { getHeadersFileName, download } from '@/utils/utils'

export default {
  name: 'DeliveryPlanList',
  components: { CollapseSearch, ScTable, RemoteAutocomplete },

  data() {
    const startDate = dayjs().subtract(1, 'month').startOf('month')
    return {
      loading: false,
      searchFormModel: {
        createTime: [new Date(startDate), new Date()],
        createTimeS: this.getUnix(dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00')),
        createTimeE: this.getUnix(dayjs(new Date()).format('YYYY-MM-DD 23:59:59'))
      },
      searchFormRules: {},
      tableData: [],
      columns: listColumns,
      toolbar: listToolbar,
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      statusOptions
    }
  },

  computed: {
    tableRef() {
      return this.$refs.scTableRef?.$refs.xGrid
    },
    searchFormItems() {
      return [
        {
          label: this.$t('送货计划单号'),
          prop: 'deliveryPlanCode',
          component: 'mt-input',
          props: { placeholder: this.$t('请输入'), showClearButton: true }
        },
        {
          label: this.$t('省'),
          prop: 'province',
          component: 'mt-input',
          props: { placeholder: this.$t('请输入'), showClearButton: true }
        },
        {
          label: this.$t('区'),
          prop: 'city',
          component: 'mt-input',
          props: { placeholder: this.$t('请输入'), showClearButton: true }
        },
        {
          label: this.$t('仓库编码'),
          prop: 'warehouseCode',
          component: 'mt-input',
          props: { placeholder: this.$t('请输入'), showClearButton: true }
        },
        {
          label: this.$t('仓库名称'),
          prop: 'warehouseName',
          component: 'mt-input',
          props: { placeholder: this.$t('请输入'), showClearButton: true }
        },
        {
          label: this.$t('仓库地址'),
          prop: 'warehouseAddr',
          component: 'mt-input',
          props: { placeholder: this.$t('请输入'), showClearButton: true }
        },
        {
          label: this.$t('供应商'),
          prop: 'supplierCodeList',
          component: 'RemoteAutocomplete',
          props: {
            placeholder: this.$t('请选择'),
            showClearButton: true,
            url: '/masterDataManagement/tenant/supplier/paged-query-distinct-no-scope',
            multiple: true,
            fields: { text: 'supplierName', value: 'supplierCode' },
            searchFields: ['supplierName', 'supplierCode']
          }
        },
        {
          label: this.$t('状态'),
          prop: 'statusList',
          component: 'mt-multi-select',
          props: {
            placeholder: this.$t('请选择'),
            dataSource: this.statusOptions,
            showClearButton: true
          }
        },
        {
          label: this.$t('创建人'),
          prop: 'createUserName',
          component: 'mt-input',
          props: { placeholder: this.$t('请输入'), showClearButton: true }
        },
        {
          label: this.$t('创建时间'),
          prop: 'createTime',
          component: 'mt-date-range-picker',
          props: { placeholder: this.$t('请选择') },
          onChange: (e) => this.dateTimeChange(e, 'createTime')
        }
      ]
    }
  },

  created() {
    this.initData()
  },

  methods: {
    async initData() {
      await this.getTableData()
    },

    dateTimeChange(e, field) {
      if (!e?.startDate) {
        this.searchFormModel[`${field}S`] = null
        this.searchFormModel[`${field}E`] = null
        return
      }

      this.searchFormModel[`${field}S`] = this.getUnix(
        dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
      )
      this.searchFormModel[`${field}E`] = this.getUnix(
        dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
      )
    },

    getUnix(val) {
      return dayjs(val).valueOf()
    },

    handleReset() {
      this.searchFormModel = {}
      const startDate = dayjs().subtract(1, 'month').startOf('month')
      this.searchFormModel.createTime = [new Date(startDate), new Date()]
      this.searchFormModel.createTimeS = this.getUnix(
        dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00')
      )
      this.searchFormModel.createTimeE = this.getUnix(
        dayjs(new Date()).format('YYYY-MM-DD 23:59:59')
      )
      this.handleSearch()
    },

    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },

    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },

    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },

    async getTableData() {
      try {
        this.loading = true
        const params = {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        }

        const res = await this.$API.vmiManagement.pageDeliveryPlanApi(params)

        if (res.code === 200) {
          const { total = 0, records = [] } = res.data
          this.pageSettings.totalRecordsCount = Number(total)
          this.pageSettings.totalPages = Math.ceil(total / this.pageSettings.pageSize)
          this.tableData = records
        }
      } catch (error) {
        console.error('获取表格数据失败:', error)
        this.$toast({ content: error || this.$t('获取数据失败'), type: 'error' })
      } finally {
        this.loading = false
      }
    },

    handleClick(row) {
      this.$router.push({
        path: '/purchase-pv/vmi-management/delivery-plan-detail',
        query: {
          type: 'view',
          id: row.id,
          record: JSON.stringify(row),
          timeStamp: Date.now()
        }
      })
    },

    async handleClickToolBar(item) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (selectedRecords.length === 0 && ['pass', 'reject', 'delete'].includes(item.code)) {
        this.$toast({ content: this.$t('请选择要操作的行'), type: 'warning' })
        return
      }

      const actionMap = {
        pass: () => this.showConfirmDialog('通过', () => this.handlePass(selectedRecords)),
        reject: () => this.handleReject(selectedRecords),
        delete: () => this.showConfirmDialog('删除', () => this.handleDelete(selectedRecords)),
        export: () => this.handleExport(item)
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },

    showConfirmDialog(action, callback) {
      return this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认${action}选中的送货计划？`)
        },
        success: callback
      })
    },

    async handleExport(item) {
      try {
        item.loading = true
        const params = {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        }

        const res = await this.$API.vmiManagement.exportDeliveryPlanApi(params)
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      } catch (error) {
        console.error('导出失败:', error)
        this.$toast({ content: error || this.$t('导出失败'), type: 'error' })
      } finally {
        item.loading = false
      }
    },

    async handlePass(selectedRecords) {
      const params = { ids: selectedRecords.map((item) => item.id) }
      const res = await this.$API.vmiManagement.passDeliveryPlanApi(params)
      if (res.code === 200) {
        this.$toast({ content: this.$t('通过成功'), type: 'success' })
        this.handleSearch()
      }
    },
    async handleReject(selectedRecords) {
      this.$dialog({
        data: {
          title: this.$t('驳回')
        },
        modal: () => import('../components/RejectDialog.vue'),
        success: async (rejectReason) => {
          let params = {
            ids: selectedRecords.map((item) => item.id),
            rejectReason
          }
          let api = this.$API.vmiManagement.rejectDeliveryPlanApi
          const res = await api(params)
          if (res.code === 200) {
            this.$toast({ content: this.$t('驳回成功'), type: 'success' })
            this.handleSearch()
          }
        }
      })
    },

    async handleDelete(selectedRecords) {
      const params = { ids: selectedRecords.map((item) => item.id) }
      const res = await this.$API.vmiManagement.deleteDeliveryPlanApi(params)
      if (res.code === 200) {
        this.$toast({ content: this.$t('删除成功'), type: 'success' })
        this.handleSearch()
      }
    }
  }
}
</script>
