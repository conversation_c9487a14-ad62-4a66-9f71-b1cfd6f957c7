import { i18n } from '@/main.js'

export const statusOptions = [
  {
    value: 0,
    text: i18n.t('草稿')
  },
  {
    value: 1,
    text: i18n.t('待审核')
  },
  {
    value: 2,
    text: i18n.t('待发货')
  },
  {
    value: 3,
    text: i18n.t('已驳回')
  },
  {
    value: 4,
    text: i18n.t('已发货')
  },
  {
    value: 5,
    text: i18n.t('已取消')
  }
]

export const listToolbar = [
  {
    code: 'pass',
    name: i18n.t('通过'),
    status: 'info',
    loading: false,
    permission: ['O_02_1865']
  },
  {
    code: 'reject',
    name: i18n.t('驳回'),
    status: 'info',
    loading: false,
    permission: ['O_02_1866']
  },
  {
    code: 'delete',
    name: i18n.t('删除'),
    status: 'info',
    loading: false,
    permission: ['O_02_1867']
  },
  {
    code: 'export',
    name: i18n.t('导出'),
    status: 'info',
    loading: false,
    permission: ['O_02_1868']
  }
]

export const detailToolbar = [
  {
    code: 'export',
    name: i18n.t('导出'),
    status: 'info',
    loading: false,
    permission: ['O_02_1869']
  }
]

export const listColumns = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'deliveryPlanCode',
    title: i18n.t('送货计划单号'),
    minWidth: 180,
    slots: {
      default: 'deliveryPlanCodeDefault'
    }
  },
  {
    field: 'province',
    title: i18n.t('省区'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.city : row.city
    }
  },
  {
    field: 'warehouseCode',
    title: i18n.t('仓库编码')
  },
  {
    field: 'warehouseName',
    title: i18n.t('仓库名称')
  },
  {
    field: 'warehouseAddr',
    title: i18n.t('仓库地址')
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂编码')
  },
  {
    field: 'siteName',
    title: i18n.t('工厂名称')
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称')
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    formatter: ({ cellValue }) => {
      let text = statusOptions.find((item) => item.value === cellValue)?.text || ''
      return text
    }
  },
  {
    field: 'rejectReason',
    title: i18n.t('驳回原因')
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间')
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人')
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间')
  }
]

export const detailColumns = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'deliveryPlanCode',
    title: i18n.t('送货计划单号'),
    minWidth: 150
  },
  {
    field: 'province',
    title: i18n.t('省区'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.city : row.city
    }
  },
  {
    field: 'warehouseType',
    title: i18n.t('仓库类型')
  },
  {
    field: 'warehouseCode',
    title: i18n.t('仓库编码')
  },
  {
    field: 'warehouseName',
    title: i18n.t('仓库名称')
  },
  {
    field: 'warehouseAddr',
    title: i18n.t('仓库地址')
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂编码')
  },
  {
    field: 'siteName',
    title: i18n.t('工厂名称')
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称')
  },
  {
    field: 'catoryCode',
    title: i18n.t('品类'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.catoryName : row.catoryName
    }
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称')
  },
  {
    field: 'unit',
    title: i18n.t('单位')
  },
  {
    field: 'specificationModel',
    title: i18n.t('规格')
  },
  {
    field: 'planDeliveryTime',
    title: i18n.t('计划送货日期')
  },
  {
    field: 'planDeliveryQty',
    title: i18n.t('计划送货量（MW）')
  },
  // {
  //   field: 'pendingShipmentQty',
  //   title: i18n.t('待发货数量')
  // },
  {
    field: 'status',
    title: i18n.t('状态'),
    formatter: ({ cellValue }) => {
      let text = statusOptions.find((item) => item.value === cellValue)?.text || ''
      return text
    }
  },
  {
    field: 'salePreAllocation',
    title: i18n.t('销售预占')
  },
  {
    field: 'warehousePreAllocation',
    title: i18n.t('仓库预占')
  },
  {
    field: 'planAvailableStock',
    title: i18n.t('VMI可用库存-计划')
  },
  {
    field: 'planOnwayStock',
    title: i18n.t('VMI在途库存-计划')
  },
  {
    field: 'availableStock',
    title: i18n.t('可用库存')
  },
  {
    field: 'allocationStock',
    title: i18n.t('占用库存')
  },
  {
    field: 'onWayStock',
    title: i18n.t('在途库存')
  },
  {
    field: 'buyerOnwayStock',
    title: i18n.t('采购在途库存')
  },
  {
    field: 'transferOnwayStock',
    title: i18n.t('调拨在途库存')
  },
  {
    field: 'logisticsTime',
    title: i18n.t('物流时间')
  },
  {
    field: 'maxUnloadCap',
    title: i18n.t('仓库每日最大卸货能力')
  },
  {
    field: 'residualArea',
    title: i18n.t('剩余面积')
  },
  {
    field: 'onloadResidArea',
    title: i18n.t('剩余面积（含在途）')
  },
  {
    field: 'residAvailCap',
    title: i18n.t('剩余可用库存')
  },
  {
    field: 'onloadResidAvailCap',
    title: i18n.t('剩余可用库存（含在途）')
  },
  {
    field: 'purchaseUnshipmentNum',
    title: i18n.t('采购待发货数量')
  }
]
