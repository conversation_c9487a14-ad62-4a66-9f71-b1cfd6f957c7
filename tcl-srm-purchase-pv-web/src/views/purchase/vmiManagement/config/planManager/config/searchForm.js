/**
 * 采购计划负责人搜索表单配置
 */
import { i18n } from '@/main.js'
import { statusOptions } from './index'

export const getSearchFormItems = () => [
  {
    label: i18n.t('省'),
    prop: 'province',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('区'),
    prop: 'city',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('仓库编码'),
    prop: 'warehouseCode',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('仓库名称'),
    prop: 'warehouseName',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('计划审核人'),
    prop: 'plannerCode',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('计划审核人名称'),
    prop: 'plannerName',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('电话'),
    prop: 'phoneNum',
    component: 'mt-input',
    props: {
      placeholder: i18n.t('请输入'),
      showClearButton: true
    }
  },
  {
    label: i18n.t('状态'),
    prop: 'status',
    component: 'mt-select',
    props: {
      placeholder: i18n.t('请选择'),
      showClearButton: true,
      dataSource: statusOptions
    }
  },
  {
    label: i18n.t('创建人'),
    prop: 'createUserName',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: i18n.t('创建时间'),
    prop: 'createTime',
    component: 'mt-date-range-picker',
    props: { placeholder: '请选择' }
  },
  {
    label: i18n.t('更新人'),
    prop: 'updateUserName',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: i18n.t('更新时间'),
    prop: 'updateTime',
    component: 'mt-date-range-picker',
    props: { placeholder: '请选择' }
  }
]
