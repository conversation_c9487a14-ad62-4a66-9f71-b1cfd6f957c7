import { toolbar, statusOptions } from './index'
import VxeRemoteSearch from '@/components/VxeRemoteSearch/index.vue'

export default {
  components: {
    VxeRemoteSearch
  },
  data() {
    return {
      toolbar,
      statusOptions,
      editRules: {
        warehouseCode: [{ required: true, message: '请选择', trigger: 'blur' }],
        plannerCode: [{ required: true, message: '请选择', trigger: 'blur' }]
      }
    }
  },
  computed: {
    columns() {
      return [
        {
          width: 50,
          type: 'checkbox',
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'province',
          title: this.$t('省区'),
          slots: {
            default: ({ row }) => {
              return [row.province ? <div>{row.province + '-' + row.city}</div> : null]
            }
          }
        },
        {
          field: 'warehouseType',
          title: this.$t('仓库类型')
        },
        {
          field: 'warehouseCode',
          title: this.$t('仓库编码'),
          minWidth: 140,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.warehouseCode}
                  placeholder={this.$t('请选择')}
                  fields={{ text: 'warehouseName', value: 'warehouseCode' }}
                  request-info={{
                    urlPre: 'demandManagement',
                    url: 'queryPvWarehouseList',
                    searchKey: 'fuzzyParam',
                    params: {
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  onChange={(e) => {
                    if (!e?.checked) {
                      row.warehouseName = e?.warehouseName || null
                      row.province = e?.province || null
                      row.city = e?.city || null
                    }
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'warehouseName',
          title: this.$t('仓库名称')
        },
        {
          field: 'plannerCode',
          title: this.$t('计划审核人'),
          minWidth: 140,
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [row.plannerCode ? <div>{row.plannerCode + '-' + row.plannerName}</div> : null]
            },
            edit: ({ row }) => {
              return (
                <VxeRemoteSearch
                  v-model={row.plannerCode}
                  placeholder={this.$t('请选择')}
                  fields={{ text: 'employeeName', value: 'externalCode' }}
                  request-info={{
                    urlPre: 'masterData',
                    url: 'getUserPageList',
                    recordsPosition: 'data.records',
                    searchFields: ['employeeName', 'externalCode'],
                    params: {
                      page: {
                        current: 1,
                        size: 50
                      }
                    }
                  }}
                  onChange={(e) => {
                    if (!e?.checked) {
                      row.plannerName = e?.employeeName || null
                      row.email = e?.email || null
                      row.phoneNum = e?.phoneNum || null
                    }
                  }}
                />
              )
            }
          }
        },
        {
          field: 'email',
          title: this.$t('邮箱'),
          minWidth: 120
        },
        {
          field: 'phoneNum',
          title: this.$t('电话'),
          minWidth: 120
        },
        {
          field: 'status',
          title: this.$t('状态'),
          minWidth: 80,
          slots: {
            default: ({ row }) => {
              const selectItem = statusOptions.find((item) => item.value === row.status)
              return [<div>{selectItem?.text}</div>]
            }
          }
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          minWidth: 160
        },
        {
          field: 'updateUserName',
          title: this.$t('最后更新人'),
          minWidth: 120
        },
        {
          field: 'updateTime',
          title: this.$t('最后更新时间'),
          minWidth: 160
        }
      ]
    },
    editConfig() {
      return {
        enabled: window.elementPermissionSet.includes('O_02_1861'),
        trigger: 'dblclick',
        mode: 'row',
        showStatus: true
      }
    }
  },
  methods: {
    async editComplete(args) {
      const { row } = args
      if (!args.$event) return

      if (args.$event.srcElement.innerText === this.$t('取消编辑')) {
        await this.handleSearch()
        return
      }

      try {
        const valid = await this.tableRef.validate([row])
        if (valid) {
          this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
          return
        }

        const params = {
          ...row,
          id: row.id.includes('row_') ? null : row.id
        }

        const api = params?.id
          ? this.$API.vmiManagement.updatePlanManagerApi
          : this.$API.vmiManagement.addPlanManagerApi

        const res = await api([params])
        if (res.code === 200) {
          this.$toast({ content: this.$t('保存成功！'), type: 'success' })
          await this.handleSearch()
        } else {
          throw new Error(res.message || this.$t('保存失败'))
        }
      } catch (error) {
        console.error('保存失败:', error)
        this.tableRef.setEditRow(row)
      }
    }
  }
}
