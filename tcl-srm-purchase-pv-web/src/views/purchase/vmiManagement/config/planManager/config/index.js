import { i18n } from '@/main.js'

// 状态
export const statusOptions = [
  { value: 0, text: i18n.t('生效') },
  { value: 1, text: i18n.t('失效') }
]

// 按钮
export const toolbar = [
  {
    code: 'add',
    name: i18n.t('新增'),
    status: 'info',
    loading: false,
    permission: ['O_02_1860']
  },
  {
    code: 'cancelEdit',
    name: i18n.t('取消编辑'),
    status: 'info',
    loading: false,
    permission: ['O_02_1861']
  },
  {
    code: 'delete',
    name: i18n.t('删除'),
    status: 'info',
    loading: false,
    permission: ['O_02_1862']
  },
  {
    code: 'enable',
    name: i18n.t('生效'),
    status: 'info',
    loading: false,
    permission: ['O_02_1863']
  },
  {
    code: 'disable',
    name: i18n.t('失效'),
    status: 'info',
    loading: false,
    permission: ['O_02_1864']
  }
]
