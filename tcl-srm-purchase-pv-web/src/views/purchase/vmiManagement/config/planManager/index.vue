<!-- 采购计划负责人 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item
          v-for="(item, index) in searchFormItems"
          :key="index"
          :label="$t(item.label)"
          :prop="item.prop"
        >
          <component
            :is="item.component"
            v-model="searchFormModel[item.prop]"
            v-bind="item.props"
            @change="item.onChange && item.onChange($event, item.prop)"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="scTableRef"
      row-id="id"
      grid-id="3d559826-fb47-4f0a-82b4-a57ed99bef78"
      show-overflow
      keep-source
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
      @edit-closed="editComplete"
    >
      <template #custom-tools>
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          v-bind="item"
          v-permission="item.permission"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { getSearchFormItems } from './config/searchForm'
import mixin from './config/mixin'
import dayjs from 'dayjs'

// 操作类型常量
const OPERATION_TYPES = {
  ADD: 'add',
  CANCEL_EDIT: 'cancelEdit',
  DELETE: 'delete',
  ENABLE: 'enable',
  DISABLE: 'disable'
}

// 操作配置
const OPERATION_CONFIG = {
  [OPERATION_TYPES.DELETE]: {
    title: '提示',
    message: '确认删除选中的数据？',
    api: 'deletePlanManagerApi',
    successMessage: '删除成功！',
    requireSelection: true
  },
  [OPERATION_TYPES.ENABLE]: {
    title: '提示',
    message: '确认启用选中的数据？',
    api: 'enablePlanManagerApi',
    successMessage: '生效成功！',
    requireSelection: true
  },
  [OPERATION_TYPES.DISABLE]: {
    title: '提示',
    message: '确认禁用选中的数据？',
    api: 'disablePlanManagerApi',
    successMessage: '失效成功！',
    requireSelection: true
  }
}

export default {
  name: 'PlanManager',
  components: { CollapseSearch, ScTable },
  mixins: [mixin],
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      loading: false,
      tableData: [],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      }
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef?.$refs.xGrid
    },
    searchFormItems() {
      const items = getSearchFormItems()
      // 为创建时间、更新时间字段添加onChange事件处理
      const dateFields = ['createTime', 'updateTime']
      dateFields.forEach((field) => {
        const item = items.find((item) => item.prop === field)
        if (item) {
          item.onChange = this.handleDateTimeChange
        }
      })
      return items
    }
  },

  created() {
    this.initData()
  },

  methods: {
    async initData() {
      await this.getTableData()
    },

    handleDateTimeChange(e, field) {
      if (!e?.startDate) {
        this.searchFormModel[`${field}S`] = null
        this.searchFormModel[`${field}E`] = null
        return
      }

      this.searchFormModel[`${field}S`] = this.getUnix(
        dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
      )
      this.searchFormModel[`${field}E`] = this.getUnix(
        dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
      )
    },

    getUnix(val) {
      return dayjs(val).valueOf()
    },

    handleReset() {
      this.searchFormModel = {}
      this.handleSearch()
    },

    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },

    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },

    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },

    async getTableData() {
      try {
        this.loading = true
        const params = {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        }

        const res = await this.$API.vmiManagement.pagePlanManagerApi(params)

        if (res.code === 200) {
          const { total = 0, records = [] } = res.data
          this.pageSettings.totalRecordsCount = Number(total)
          this.pageSettings.totalPages = Math.ceil(total / this.pageSettings.pageSize)
          this.tableData = records
        }
      } catch (error) {
        console.error('获取表格数据失败:', error)
        this.$toast({ content: error || this.$t('获取数据失败'), type: 'error' })
      } finally {
        this.loading = false
      }
    },

    async handleClickToolBar(item) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const operation = OPERATION_CONFIG[item.code]

      if (operation?.requireSelection && selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
        return
      }

      const actionMap = {
        [OPERATION_TYPES.ADD]: () => this.handleAdd(),
        [OPERATION_TYPES.CANCEL_EDIT]: () => this.handleSearch(),
        [OPERATION_TYPES.DELETE]: () => this.handleOperate(selectedRecords, OPERATION_TYPES.DELETE),
        [OPERATION_TYPES.ENABLE]: () => this.handleOperate(selectedRecords, OPERATION_TYPES.ENABLE),
        [OPERATION_TYPES.DISABLE]: () =>
          this.handleOperate(selectedRecords, OPERATION_TYPES.DISABLE)
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },

    handleAdd() {
      const item = {
        warehouseType: 'VMI'
      }
      this.tableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.tableRef.setEditRow(currentViewRecords[0])
      })
    },

    /**
     * 处理操作
     * @param {Array} selectedRecords - 选中的记录
     * @param {string} type - 操作类型
     * @param {Object} extraParams - 额外参数
     */
    handleOperate(selectedRecords, type, extraParams = {}) {
      const operation = OPERATION_CONFIG[type]
      if (!operation) {
        this.$toast({ content: this.$t('不支持的操作类型'), type: 'error' })
        return
      }

      this.$dialog({
        data: {
          title: this.$t(operation.title),
          message: this.$t(operation.message),
          type: 'warning'
        },
        success: async () => {
          try {
            const params = {
              ids: selectedRecords.map((item) => item.id),
              ...extraParams
            }

            const res = await this.$API.vmiManagement[operation.api](params)
            if (res.code === 200) {
              this.$toast({
                content: this.$t(operation.successMessage),
                type: 'success'
              })
              await this.handleSearch()
            } else {
              throw new Error(res || this.$t(`${operation.title}失败`))
            }
          } catch (error) {
            console.error(`${operation.title}失败:`, error)
            this.$toast({
              content: error || this.$t(`${operation.title}失败`),
              type: 'error'
            })
          }
        }
      })
    }
  }
}
</script>
