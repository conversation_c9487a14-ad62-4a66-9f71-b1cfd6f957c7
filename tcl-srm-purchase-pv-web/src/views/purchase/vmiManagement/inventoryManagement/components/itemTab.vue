<template>
  <div>
    <sc-table
      ref="scTableRef"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      keep-source
      :sortable="false"
      :is-show-right-btn="true"
      :is-show-refresh-bth="pageType !== 'create'"
      @refresh="getTableData"
      @edit-closed="editComplete"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'

export default {
  name: 'ItemTab',
  components: { ScTable },
  props: {
    dataInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      editRules: {
        shippedQuantity: [{ required: true, message: this.$t('必填') }]
      }
    }
  },
  computed: {
    pageType() {
      return this.$route.query?.type
    },
    editable() {
      return ['create', 'edit'].includes(this.pageType)
    },
    tableRef() {
      return this.$refs.scTableRef?.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'lineNo',
          title: this.$t('行号'),
          width: 80
        },
        {
          field: 'deliveryPlanNo',
          title: this.$t('送货计划单号')
        },
        {
          field: 'itemCode',
          title: this.$t('物料编码')
        },
        {
          field: 'itemName',
          title: this.$t('物料名称')
        },
        {
          field: 'shippedQuantity',
          title: this.$t('发货数量'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='integer'
                  v-model={row.shippedQuantity}
                  placeholder={this.$t('请输入')}
                  min='1'
                  max={row.planDeliveryQty}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'receivedQuantity',
          title: this.$t('收货数量')
        },
        {
          field: 'receiveTime',
          title: this.$t('收货时间'),
          minWidth: 160
        },
        {
          field: 'baseUnitCode',
          title: this.$t('单位'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.baseUnitName : ''
          }
        },
        {
          field: 'categoryCode',
          title: this.$t('品类'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.categoryName : ''
          }
        },
        {
          field: 'itemRemark',
          title: this.$t('行备注'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.itemRemark}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        }
      ]
    },
    editConfig() {
      return {
        enabled: this.editable,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    toolbar() {
      let btns = []
      if (this.editable) {
        btns = [{ code: 'delete', name: this.$t('删除'), status: 'info', loading: false }]
      }
      return btns
    }
  },
  mounted() {
    if (this.pageType === 'create') {
      let data = []
      data = sessionStorage.getItem('deliveryPlanSelectedRecords')
        ? JSON.parse(sessionStorage.getItem('deliveryPlanSelectedRecords'))
        : []
      this.tableData = data.map((item) => {
        return {
          planId: item.id,
          deliveryPlanNo: item.deliveryPlanCode,
          supplierTenantId: item.supplierTenantId,
          supplierCode: item.supplierCode,
          supplierName: item.supplierName,
          itemCode: item.itemCode,
          itemName: item.itemName,
          baseUnitCode: item.unitCode,
          baseUnitName: item.unit,
          categoryCode: item.catoryCode,
          categoryName: item.catoryName,
          planDeliveryQty: Number(item.planDeliveryQty),
          shippedQuantity: Number(item.planDeliveryQty)
        }
      })
      this.$emit('updateDetail', this.tableData)
    } else {
      this.getTableData()
    }
  },
  methods: {
    /**
     * 获取表格数据
     */
    async getTableData() {
      try {
        this.loading = true
        let params = {
          ids: [this.$route.query?.id]
        }
        const res = await this.$API.supplierVmiManagement.detailInventoryManagementApi(params)
        if (res.code === 200) {
          this.tableData = res.data
          this.$emit('updateDetail', this.tableData)
        } else {
          this.$toast({ content: res.msg || this.$t('获取数据失败'), type: 'warning' })
        }
      } catch (error) {
        console.error('获取表格数据失败:', error)
        this.$toast({ content: this.$t('获取数据失败'), type: 'error' })
      } finally {
        this.loading = false
      }
    },

    /**
     * 处理工具栏按钮点击
     */
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['delete']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      switch (e.code) {
        case 'delete':
          this.handleDelete()
          break
        default:
          break
      }
    },

    /**
     * 处理删除
     */
    handleDelete() {
      this.tableRef.removeCheckboxRow()
      const currentViewRecords = this.tableRef.getTableData().visibleData
      this.$emit('updateDetail', currentViewRecords)
    },

    /**
     * 处理编辑完成
     */
    editComplete(args) {
      if (args.$event) {
        // 保存数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        this.$emit('updateDetail', currentViewRecords)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.sc-table {
  :deep(.vxe-table) {
    .vxe-body--row {
      &.row--editing {
        background-color: #f5f7fa;
      }
    }
  }
}
</style>
