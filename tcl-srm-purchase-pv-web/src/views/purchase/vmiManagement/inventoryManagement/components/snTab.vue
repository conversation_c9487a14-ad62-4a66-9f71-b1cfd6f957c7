<template>
  <div>
    <sc-table
      ref="scTableRef"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      keep-source
      :sortable="false"
      :is-show-right-btn="true"
      :is-show-refresh-bth="pageType !== 'create'"
      @refresh="getTableData"
      @edit-closed="editComplete"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
    <mt-page
      v-if="pageType !== 'create'"
      ref="pageRef"
      class="custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import ScTable from '@/components/ScTable/src/index'
import VxeRemoteSearch from '@/components/VxeRemoteSearch/index.vue'
import { getHeadersFileName, download } from '@/utils/utils'

export default {
  name: 'SnTab',
  components: { ScTable, VxeRemoteSearch },
  props: {
    dataInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 100,
        pageSizes: [100, 200, 500, 1000]
      },
      editRules: {
        materialCode: [{ required: true, message: this.$t('必填') }],
        snCode: [{ required: true, message: this.$t('必填') }]
      },
      statusOptions: [
        { label: this.$t('草稿'), value: 0 },
        { label: this.$t('提交成功'), value: 1 },
        { label: this.$t('提交失败'), value: 2 }
      ],
      businessTypeOptions: [
        { label: this.$t('户用业务'), value: 'HY' }
        // { label: this.$t('商用业务'), value: 'SY' }
      ]
    }
  },
  computed: {
    pageType() {
      return this.$route.query?.type
    },
    tableRef() {
      return this.$refs.scTableRef?.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'status',
          title: this.$t('状态'),
          formatter: ({ cellValue }) => {
            let item = this.statusOptions.find((item) => item.value === cellValue)
            return item ? item.label : ''
          }
        },
        {
          field: 'businessType',
          title: this.$t('业务类型'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              let selectedItem = this.businessTypeOptions.find((v) => v.value === row.businessType)
              return [selectedItem ? <div>{selectedItem.label}</div> : null]
            }
          }
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商编码'),
          minWidth: 120
        },
        {
          field: 'supplierName',
          title: this.$t('供应商名称'),
          minWidth: 120
        },
        {
          field: 'materialCode',
          title: this.$t('物料编码'),
          minWidth: 120
        },
        {
          field: 'materialName',
          title: this.$t('物料名称')
        },
        {
          field: 'snCode',
          title: this.$t('SN码'),
          minWidth: 140
        },
        {
          field: 'remarks',
          title: this.$t('备注'),
          minWidth: 120
        },
        {
          field: 'deliveryCode',
          title: this.$t('送货单号')
        },
        {
          field: 'submitJiguangFailureReason',
          title: this.$t('提交（极光）失败原因')
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          minWidth: 160
        },
        {
          field: 'updateUserName',
          title: this.$t('更新人')
        },
        {
          field: 'updateTime',
          title: this.$t('更新时间'),
          minWidth: 160
        }
      ]
    },
    editConfig() {
      return {
        enabled: false,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    toolbar() {
      let btns = [{ code: 'export', name: this.$t('导出'), status: 'info', loading: false }]
      return btns
    }
  },
  mounted() {
    if (this.pageType !== 'create') {
      this.getTableData()
    }
  },
  methods: {
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      try {
        this.loading = true
        let params = {
          deliveryIdList: [this.$route.query?.id],
          businessType: 'HY',
          createTimeStart: dayjs(
            dayjs(this.dataInfo?.createTime).format('YYYY-MM-DD 00:00:00')
          ).valueOf(),
          createTimeEnd: dayjs().valueOf(),
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          }
        }
        const res = await this.$API.receivingManagement.pageCommercialSnCodeInfoUploadApi(params)
        if (res.code === 200) {
          const { total = 0, records = [] } = res.data
          this.pageSettings.totalRecordsCount = Number(total)
          this.pageSettings.totalPages = Math.ceil(total / this.pageSettings.pageSize)
          this.tableData = records.map((item) => {
            return {
              ...item,
              businessType: 'HY'
            }
          })
        } else {
          this.$toast({ content: res.msg || this.$t('获取数据失败'), type: 'warning' })
        }
      } catch (error) {
        console.error('获取表格数据失败:', error)
        this.$toast({ content: this.$t('获取数据失败'), type: 'error' })
      } finally {
        this.loading = false
      }
    },
    async handleClickToolBar(e) {
      switch (e.code) {
        case 'export':
          e.loading = true
          await this.handleExport(e)
          break
        default:
          break
      }
    },
    editComplete(args) {
      if (args.$event) {
        const currentViewRecords = this.tableRef.getTableData().visibleData
        this.$emit('updateDetail', currentViewRecords)
      }
    },
    async handleExport(e) {
      try {
        const dynamicHeaderMap = {}
        this.columns.forEach((item) => {
          if (item.field) {
            if (['status'].includes(item.field)) {
              dynamicHeaderMap[item.field + 'Name'] = item.title
            } else {
              dynamicHeaderMap[item.field] = item.title
            }
          }
        })
        const params = {
          dynamicHeaderMap,
          pvCommercialSnQueryReq: {
            deliveryIdList: [this.$route.query?.id],
            businessType: 'HY',
            page: {
              current: 1,
              size: 5000
            },
            createTimeStart: dayjs(
              dayjs(this.dataInfo?.createTime).format('YYYY-MM-DD 00:00:00')
            ).valueOf(),
            createTimeEnd: dayjs().valueOf()
          }
        }
        const res = await this.$API.receivingManagement.exportCommercialSnCodeInfoUploadApi(params)
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      } catch (error) {
        console.error('导出失败:', error)
        this.$toast({ content: this.$t('导出失败'), type: 'error' })
      } finally {
        e.loading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.flex-keep {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
.custom-page {
  :deep(.mt-pagination) {
    margin: 0;
  }
}
</style>
