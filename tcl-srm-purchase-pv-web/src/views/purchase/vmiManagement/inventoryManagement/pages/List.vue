<!-- 列表 -->
<template>
  <div class="inventory-management-detail">
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item
          v-for="(item, index) in searchFormItems"
          :key="index"
          :label="$t(item.label)"
          :prop="item.prop"
        >
          <component
            :is="item.component"
            v-model="searchFormModel[item.prop]"
            v-bind="item.props"
            @change="item.onChange && item.onChange($event, item.prop)"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="scTableRef"
      row-id="id"
      grid-id="43c6e693-3464-0a2b-85ff-1d9cb77714fe"
      show-overflow
      keep-source
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template #custom-tools>
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          v-bind="item"
          v-permission="item.permission"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #stockInCodeDefault="{ row }">
        <div>
          <span style="cursor: pointer; color: #2783fe" @click="handleClick(row)">
            {{ row.stockInCode }}
          </span>
        </div>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import { listColumns, listToolbar, statusOptions, returnStatusOptions } from '../config/index'
import { getSearchFormItems } from '../config/searchForm'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  name: 'InventoryManagementList',
  components: { CollapseSearch, ScTable, RemoteAutocomplete },

  data() {
    const startDate = dayjs().subtract(1, 'month').startOf('month')
    return {
      loading: false,
      searchFormModel: {
        createTime: [new Date(startDate), new Date()],
        createTimeS: this.getUnix(dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00')),
        createTimeE: this.getUnix(dayjs(new Date()).format('YYYY-MM-DD 23:59:59'))
      },
      searchFormRules: {},
      tableData: [],
      columns: listColumns,
      toolbar: listToolbar,
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      statusOptions,
      returnStatusOptions,
      pdfUrl: ''
    }
  },

  computed: {
    tableRef() {
      return this.$refs.scTableRef?.$refs.xGrid
    },
    searchFormItems() {
      const items = getSearchFormItems({
        statusOptions: this.statusOptions,
        returnStatusOptions: this.returnStatusOptions,
        siteListUrl: this.$API.masterData.getSiteListUrl
      })
      // 为创建时间字段添加onChange事件处理
      const dateFields = ['createTime']
      dateFields.forEach((field) => {
        const item = items.find((item) => item.prop === field)
        if (item) {
          item.onChange = this.handleDateTimeChange
        }
      })
      return items
    }
  },

  created() {
    this.initData()
  },

  methods: {
    async initData() {
      await this.getTableData()
    },

    handleDateTimeChange(e, field) {
      if (!e?.startDate) {
        this.searchFormModel[`${field}S`] = null
        this.searchFormModel[`${field}E`] = null
        return
      }

      this.searchFormModel[`${field}S`] = this.getUnix(
        dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
      )
      this.searchFormModel[`${field}E`] = this.getUnix(
        dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
      )
    },

    getUnix(val) {
      return dayjs(val).valueOf()
    },

    handleReset() {
      this.searchFormModel = {}
      const startDate = dayjs().subtract(1, 'month').startOf('month')
      this.searchFormModel.createTime = [new Date(startDate), new Date()]
      this.searchFormModel.createTimeS = this.getUnix(
        dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00')
      )
      this.searchFormModel.createTimeE = this.getUnix(
        dayjs(new Date()).format('YYYY-MM-DD 23:59:59')
      )
      this.handleSearch()
    },

    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },

    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },

    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },

    async getTableData() {
      try {
        this.loading = true
        const params = {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        }

        const res = await this.$API.vmiManagement.pageInventoryManagementApi(params)

        if (res.code === 200) {
          const { total = 0, records = [] } = res.data
          this.pageSettings.totalRecordsCount = Number(total)
          this.pageSettings.totalPages = Math.ceil(total / this.pageSettings.pageSize)
          this.tableData = records
        }
      } catch (error) {
        console.error('获取表格数据失败:', error)
        this.$toast({ content: error || this.$t('获取数据失败'), type: 'error' })
      } finally {
        this.loading = false
      }
    },

    handleClick(row) {
      const query = {
        id: row.id,
        type: 'view',
        timeStamp: Date.now()
      }
      sessionStorage.setItem('deliveryPlanSelectedRecords', JSON.stringify([row]))
      this.$router.push({
        path: '/purchase-pv/vmi-management/inventory-management-detail',
        query
      })
    },

    async handleClickToolBar(item) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (
        selectedRecords.length === 0 &&
        ['cancel', 'close', 'print', 'sync', 'create'].includes(item.code)
      ) {
        this.$toast({ content: this.$t('请选择要操作的行'), type: 'warning' })
        return
      }

      if (selectedRecords.length > 1 && ['create'].includes(item.code)) {
        this.$toast({ content: this.$t('只能选择一行进行操作'), type: 'warning' })
        return
      }

      const actionMap = {
        cancel: () => this.showConfirmDialog('取消', () => this.handleCancel(selectedRecords)),
        close: () => this.handleClose(selectedRecords),
        export: () => this.handleExport(item),
        print: () => this.showConfirmDialog('打印', () => this.handlePrint(selectedRecords)),
        sync: () => this.handleSync(selectedRecords),
        create: () => this.handleCreate(selectedRecords)
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },

    showConfirmDialog(action, callback) {
      return this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认${action}？`)
        },
        success: callback
      })
    },

    handleCancel(selectedRecords) {
      let params = { ids: selectedRecords.map((item) => item.id) }
      this.$API.vmiManagement.cancelInventoryManagementApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('取消成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },

    handleClose(selectedRecords) {
      let ids = selectedRecords.map((item) => item.id)
      this.$dialog({
        modal: () => import('../components/closeDialog.vue'),
        data: {
          title: this.$t('关闭原因')
        },
        success: (closeReason) => {
          let params = {
            closeReason,
            ids
          }
          this.$API.vmiManagement.closeInventoryManagementApi(params).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('关闭成功'), type: 'success' })
              this.handleSearch()
            }
          })
        }
      })
    },

    async handleExport(item) {
      try {
        item.loading = true
        const params = {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        }

        const res = await this.$API.vmiManagement.exportInventoryManagementApi(params)
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      } catch (error) {
        console.error('导出失败:', error)
        this.$toast({ content: error || this.$t('导出失败'), type: 'error' })
      } finally {
        item.loading = false
      }
    },

    async handlePrint(selectedRecords) {
      try {
        let params = { ids: selectedRecords.map((item) => item.id) }
        const res = await this.$API.vmiManagement.printInventoryManagementApi(params)
        const content = res.data
        this.pdfUrl = window.URL.createObjectURL(
          new Blob([content], { type: 'text/html;charset=utf-8' })
        )
        let date = new Date().getTime()
        let ifr = document.createElement('iframe')
        ifr.style.frameborder = 'no'
        ifr.style.display = 'none'
        ifr.style.pageBreakBefore = 'always'
        ifr.setAttribute('id', 'printPdf' + date)
        ifr.setAttribute('name', 'printPdf' + date)
        ifr.src = this.pdfUrl
        document.body.appendChild(ifr)
        this.doPrint('printPdf' + date)
        window.URL.revokeObjectURL(ifr.src)
      } catch (error) {
        console.error('打印失败:', error)
        this.$toast({ content: error || this.$t('打印失败'), type: 'error' })
      }
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
      }, 100)
    },

    handleSync(selectedRecords) {
      let ids = selectedRecords.map((item) => item.id)
      this.$dialog({
        modal: () => import('../components/selectTypeDialog.vue'),
        data: {
          title: this.$t('选择操作类型')
        },
        success: (operationType) => {
          let params = {
            operationType,
            ids
          }
          this.$API.vmiManagement.syncInventoryManagementApi(params).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('同步成功'), type: 'success' })
              this.handleSearch()
            }
          })
        }
      })
    },

    handleCreate(selectedRecords) {
      if (
        !(selectedRecords[0].status === 3 && [1, 5, 6].includes(selectedRecords[0].returnStatus))
      ) {
        this.$toast({
          content: this.$t(
            '只能选择【已接收】且退货状态为【未退货】【供方已拒绝】【已取消】的数据进行操作'
          ),
          type: 'warning'
        })
        return
      }
      sessionStorage.setItem('inventorySelectedRecords', JSON.stringify(selectedRecords))

      // 跳转创建VMI退货单页面
      this.$router.push({
        path: '/purchase-pv/vmi-management/return-management-detail',
        query: {
          type: 'create',
          timeStamp: dayjs().valueOf()
        }
      })
    }
  }
}
</script>
