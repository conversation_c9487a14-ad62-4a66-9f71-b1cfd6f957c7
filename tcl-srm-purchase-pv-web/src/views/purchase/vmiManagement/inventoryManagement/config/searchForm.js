/**
 * VMI入库管理搜索表单配置
 */

export const getSearchFormItems = ({ statusOptions, returnStatusOptions, siteListUrl }) => [
  {
    label: 'SRM入库单号',
    prop: 'stockInCode',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '工厂编码',
    prop: 'siteCodeList',
    component: 'RemoteAutocomplete',
    props: {
      placeholder: i18n.t('请选择'),
      showClearButton: true,
      url: siteListUrl,
      multiple: true,
      fields: { text: 'siteName', value: 'siteCode' },
      searchFields: ['siteName', 'siteCode']
    }
  },
  {
    label: '供应商编码',
    prop: 'supplierCode',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: 'VMI仓编码',
    prop: 'warehouseCode',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '入库状态',
    prop: 'status',
    component: 'mt-select',
    props: {
      placeholder: '请选择',
      dataSource: statusOptions,
      showClearButton: true
    }
  },
  {
    label: '退货状态',
    prop: 'returnStatus',
    component: 'mt-select',
    props: {
      placeholder: '请选择',
      dataSource: returnStatusOptions,
      showClearButton: true
    }
  },
  {
    label: '创建人',
    prop: 'createUserName',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '创建时间',
    prop: 'createTime',
    component: 'mt-date-range-picker',
    props: { placeholder: '请选择' }
  }
]

export const getDetailSearchFormItems = ({ statusOptions, siteListUrl }) => [
  {
    label: 'SRM入库单号',
    prop: 'stockInCode',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '送货计划单号',
    prop: 'deliveryPlanNo',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '工厂编码',
    prop: 'siteCodeList',
    component: 'RemoteAutocomplete',
    props: {
      placeholder: i18n.t('请选择'),
      showClearButton: true,
      url: siteListUrl,
      multiple: true,
      fields: { text: 'siteName', value: 'siteCode' },
      searchFields: ['siteName', 'siteCode']
    }
  },
  {
    label: '供应商编码',
    prop: 'supplierCode',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '物料编码',
    prop: 'itemCodeStr',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: 'VMI仓编码',
    prop: 'warehouseCode',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '入库状态',
    prop: 'status',
    component: 'mt-select',
    props: {
      placeholder: '请选择',
      dataSource: statusOptions,
      showClearButton: true
    }
  },
  {
    label: '创建人',
    prop: 'createUserName',
    component: 'mt-input',
    props: { placeholder: '请输入', showClearButton: true }
  },
  {
    label: '创建时间',
    prop: 'createTime',
    component: 'mt-date-range-picker',
    props: { placeholder: '请选择' }
  }
]
