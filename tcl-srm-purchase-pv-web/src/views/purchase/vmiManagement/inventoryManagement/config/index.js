import { i18n } from '@/main.js'
import dayjs from 'dayjs'

// 状态
export const statusOptions = [
  { value: 1, text: i18n.t('新建') },
  { value: 2, text: i18n.t('入库中') },
  { value: 3, text: i18n.t('已接收') },
  { value: 4, text: i18n.t('已取消') },
  { value: 5, text: i18n.t('已关闭') }
]

// 退货状态
export const returnStatusOptions = [
  { value: 1, text: i18n.t('未退货') },
  { value: 2, text: i18n.t('待供方确认') },
  { value: 3, text: i18n.t('待出库退货') },
  { value: 4, text: i18n.t('已退货') },
  { value: 5, text: i18n.t('供方已拒绝') },
  { value: 6, text: i18n.t('已取消') }
]

// 仓库类型
export const warehouseTypeOptions = [
  { value: 'HY', text: i18n.t('户用RDC仓'), label: i18n.t('户用RDC仓') },
  { value: 'SY', text: i18n.t('商用仓'), label: i18n.t('商用仓') },
  { value: 'HYYW', text: i18n.t('户用运维RDC仓'), label: i18n.t('户用运维RDC仓') },
  { value: 'HW', text: i18n.t('海外仓'), label: i18n.t('海外仓') },
  { value: 'VMI', text: i18n.t('VMI仓库'), label: i18n.t('VMI仓库') }
]

export const syncJgStatusOptions = [
  { text: i18n.t('未同步'), value: 0 },
  { text: i18n.t('同步中'), value: 1 },
  { text: i18n.t('同步成功'), value: 2 },
  { text: i18n.t('同步失败'), value: 3 }
]

export const shippingMethodOptions = [
  { text: i18n.t('自提'), label: i18n.t('自提'), value: 0 },
  { text: i18n.t('快递'), label: i18n.t('快递'), value: 1 },
  { text: i18n.t('厂家直送'), label: i18n.t('厂家直送'), value: 2 },
  { text: i18n.t('速必达配送'), label: i18n.t('速必达配送'), value: 3 }
]

export const operationTypeOptions = [
  { text: i18n.t('新增'), label: i18n.t('新增'), value: 1 },
  { text: i18n.t('取消'), label: i18n.t('取消'), value: 2 },
  { text: i18n.t('关闭'), label: i18n.t('关闭'), value: 4 }
]

export const listToolbar = [
  {
    code: 'cancel',
    name: i18n.t('取消'),
    status: 'info',
    loading: false,
    permission: ['O_02_1870']
  },
  {
    code: 'close',
    name: i18n.t('关闭'),
    status: 'info',
    loading: false,
    permission: ['O_02_1871']
  },
  {
    code: 'export',
    name: i18n.t('导出'),
    status: 'info',
    loading: false,
    permission: ['O_02_1872']
  },
  {
    code: 'print',
    name: i18n.t('打印'),
    status: 'info',
    loading: false,
    permission: ['O_02_1873']
  },
  {
    code: 'sync',
    name: i18n.t('同步极光'),
    status: 'info',
    loading: false,
    permission: ['O_02_1874']
  },
  {
    code: 'create',
    name: i18n.t('创建退货单'),
    status: 'info',
    loading: false,
    permission: ['O_02_1875']
  }
]

export const detailToolbar = [
  {
    code: 'export',
    name: i18n.t('导出'),
    status: 'info',
    loading: false,
    permission: ['O_02_1876']
  }
]

export const listColumns = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'stockInCode',
    title: i18n.t('SRM入库单号'),
    minWidth: 180,
    slots: {
      default: 'stockInCodeDefault'
    }
  },
  {
    field: 'status',
    title: i18n.t('入库状态'),
    formatter: ({ cellValue }) =>
      statusOptions.find((item) => item.value === cellValue)?.text ?? '-'
  },
  {
    field: 'returnStatus',
    title: i18n.t('退货状态'),
    formatter: ({ cellValue }) =>
      returnStatusOptions.find((item) => item.value === cellValue)?.text ?? '-'
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂编码')
  },
  {
    field: 'siteName',
    title: i18n.t('工厂名称')
  },

  {
    field: 'warehouseCode',
    title: i18n.t('VMI仓编码'),
    minWidth: 120
  },
  {
    field: 'warehouseName',
    title: i18n.t('VMI仓名称'),
    minWidth: 120
  },
  {
    field: 'warehouseType',
    title: i18n.t('仓库类型'),
    formatter: ({ cellValue }) =>
      warehouseTypeOptions.find((item) => item.value === cellValue)?.text ?? '-'
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码'),
    minWidth: 120
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    minWidth: 120
  },
  {
    field: 'deliveryAddress',
    title: i18n.t('送货地址')
  },
  {
    field: 'closeReason',
    title: i18n.t('关闭原因')
  },
  {
    field: 'syncJgStatus',
    title: i18n.t('极光同步状态'),
    minWidth: 140,
    formatter: ({ cellValue }) =>
      syncJgStatusOptions.find((item) => item.value === cellValue)?.text ?? '-'
  },
  {
    field: 'syncJgMsg',
    title: i18n.t('极光同步信息'),
    minWidth: 140
  },
  {
    field: 'purchaseUnshipmentNum',
    title: i18n.t('采购待发货数量')
  },
  {
    field: 'shipmentRemark',
    title: i18n.t('发货备注')
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  }
]

export const detailColumns = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'stockInCode',
    title: i18n.t('VMI入库单号'),
    minWidth: 150
  },
  {
    field: 'lineNo',
    title: i18n.t('行号')
  },
  {
    field: 'companyCode',
    title: i18n.t('公司'),
    minWidth: 150,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.companyName : ''
    }
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂'),
    minWidth: 150,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.siteName : ''
    }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    minWidth: 150,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.supplierName : ''
    }
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码'),
    minWidth: 120
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称'),
    minWidth: 150
  },
  {
    field: 'baseUnitCode',
    title: i18n.t('单位'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.baseUnitName : '-'
    }
  },
  {
    field: 'requiredQuantity',
    title: i18n.t('需求数量')
  },
  {
    field: 'shippedQuantity',
    title: i18n.t('发货数量')
  },
  {
    field: 'receivedQuantity',
    title: i18n.t('签收数量')
  },
  {
    field: 'returnQuantity',
    title: i18n.t('退回数量')
  },
  {
    field: 'warehouseCode',
    title: i18n.t('入库仓库'),
    minWidth: 150,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.warehouseName : '-'
    }
  },
  {
    field: 'categoryCode',
    title: i18n.t('品类'),
    minWidth: 150,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.categoryName : '-'
    }
  },
  {
    field: 'deliveryPlanNo',
    title: i18n.t('送货计划单号'),
    minWidth: 150
  },
  {
    field: 'shippingMethod',
    title: i18n.t('发货方式'),
    formatter: ({ cellValue }) =>
      shippingMethodOptions.find((item) => item.value === cellValue)?.text ?? '-'
  },
  {
    field: 'senderName',
    title: i18n.t('发件人姓名'),
    minWidth: 120
  },
  {
    field: 'senderPhone',
    title: i18n.t('发件人手机号'),
    minWidth: 120
  },
  {
    field: 'receiver',
    title: i18n.t('收货人')
  },
  {
    field: 'receiverContact',
    title: i18n.t('收货联系方式'),
    minWidth: 120
  },
  {
    field: 'deliveryAddress',
    title: i18n.t('配送地址')
  },
  {
    field: 'logisticsCompany',
    title: i18n.t('物流公司')
  },
  {
    field: 'logisticsNo',
    title: i18n.t('物流单号')
  },
  {
    field: 'carModel',
    title: i18n.t('车型')
  },
  {
    field: 'licensePlate',
    title: i18n.t('车牌号')
  },
  {
    field: 'syncJgStatus',
    title: i18n.t('同步极光状态'),
    minWidth: 140,
    formatter: ({ cellValue }) =>
      syncJgStatusOptions.find((item) => item.value === cellValue)?.text ?? '-'
  },
  {
    field: 'syncJgMsg',
    title: i18n.t('同步极光信息'),
    minWidth: 140
  },
  {
    field: 'itemRemark',
    title: i18n.t('行备注')
  },
  {
    field: 'canceller',
    title: i18n.t('取消人')
  },
  {
    field: 'cancelTime',
    title: i18n.t('取消时间'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      return cellValue ? dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss') : '-'
    }
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人')
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160
  }
]
