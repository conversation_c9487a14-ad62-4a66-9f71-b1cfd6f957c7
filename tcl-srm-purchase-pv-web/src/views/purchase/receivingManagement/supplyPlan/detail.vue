<!-- 创建送货单 -->
<template>
  <div class="postpone-full-height">
    <div class="top-container toggle-container" :class="[!isExpand && 'top-hidden']">
      <div class="top-content">
        <div class="top-info">
          <div class="top-title">
            <span>{{ $t('状态') }}：{{ formData.status | statusFormat }}</span>
            <span>{{ $t('送货单号') }}：{{ formData.deliveryCode || '-' }}</span>
            <span>{{ $t('创建人') }}：{{ formData.createUserName || '-' }}</span>
            <span>{{ $t('创建时间') }}：{{ formData.createTime || '-' }}</span>
          </div>
          <div class="top-btns">
            <vxe-button
              v-for="(item, index) in detailToolbar"
              v-show="!item.isHidden"
              :key="index"
              :icon="item.icon"
              :status="item.status"
              :disabled="item.disabled"
              size="small"
              @click="handleClickToolBar(item)"
              >{{ item.name }}</vxe-button
            >
          </div>
        </div>
        <div class="top-form">
          <mt-form ref="formRef" :model="formData" :rules="formRules">
            <mt-form-item prop="businessType" :label="$t('业务类型')" label-style="top">
              <vxe-select
                v-model="formData.businessType"
                :options="businessTypeOptions"
                :option-props="{ label: 'label', value: 'value' }"
                :placeholder="$t('请选择')"
                :disabled="true"
                clearable
              />
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('供应商编码')" label-style="top">
              <vxe-input v-model="formData.supplierCode" clearable :disabled="true" />
            </mt-form-item>
            <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
              <vxe-input v-model="formData.supplierName" clearable :disabled="true" />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('公司编码')" label-style="top">
              <vxe-input v-model="formData.companyCode" clearable :disabled="true" />
            </mt-form-item>
            <mt-form-item prop="companyName" :label="$t('公司名称')" label-style="top">
              <vxe-input v-model="formData.companyName" clearable :disabled="true" />
            </mt-form-item>
            <mt-form-item prop="siteCode" :label="$t('工厂编码')" label-style="top">
              <vxe-input v-model="formData.siteCode" clearable :disabled="true" />
            </mt-form-item>
            <mt-form-item prop="siteName" :label="$t('工厂名称')" label-style="top">
              <vxe-input v-model="formData.siteName" clearable :disabled="true" />
            </mt-form-item>
            <mt-form-item prop="srmSignFlag" :label="$t('是否SRM签收')" label-style="top">
              <vxe-select
                v-model="formData.srmSignFlag"
                :options="srmSignFlagOptions"
                :option-props="{ label: 'label', value: 'value' }"
                :placeholder="$t('请选择')"
                :disabled="true"
                clearable
              />
            </mt-form-item>
            <mt-form-item prop="warehouseName" :label="$t('入库仓库')" label-style="top">
              <vxe-input v-model="formData.warehouseName" clearable :disabled="true" />
            </mt-form-item>
            <mt-form-item prop="deliveryType" :label="$t('送货单类型')" label-style="top">
              <vxe-select
                v-model="formData.deliveryType"
                :options="deliveryTypeOptions"
                :option-props="{ label: 'label', value: 'value' }"
                :placeholder="$t('请选择')"
                :disabled="true"
                clearable
              />
            </mt-form-item>
            <mt-form-item prop="shippingMethod" :label="$t('发货方式')" label-style="top">
              <vxe-select
                v-model="formData.shippingMethod"
                :options="shippingMethodOptions"
                :option-props="{ label: 'label', value: 'value' }"
                :placeholder="$t('请选择')"
                :disabled="!editable"
                clearable
              />
            </mt-form-item>
            <mt-form-item
              v-if="formData.shippingMethod === 1"
              prop="logisticsNo"
              :label="$t('物流单号')"
              label-style="top"
            >
              <vxe-input
                v-model="formData.logisticsNo"
                :placeholder="$t('请输入')"
                clearable
                :disabled="!editable"
              />
            </mt-form-item>
            <mt-form-item
              v-if="formData.shippingMethod === 1"
              prop="logisticsCompany"
              :label="$t('物流公司名称')"
              label-style="top"
            >
              <vxe-input
                v-model="formData.logisticsCompany"
                :placeholder="$t('请输入')"
                clearable
                :disabled="!editable"
              />
            </mt-form-item>
            <mt-form-item
              v-if="formData.shippingMethod === 1"
              prop="senderName"
              :label="$t('发件人姓名')"
              label-style="top"
            >
              <vxe-input
                v-model="formData.senderName"
                :placeholder="$t('请输入')"
                clearable
                :disabled="!editable"
              />
            </mt-form-item>
            <mt-form-item
              v-if="formData.shippingMethod === 1"
              prop="senderPhone"
              :label="$t('发件人手机号')"
              label-style="top"
            >
              <vxe-input
                v-model="formData.senderPhone"
                :placeholder="$t('请输入')"
                clearable
                :disabled="!editable"
              />
            </mt-form-item>
            <mt-form-item
              v-if="formData.shippingMethod === 2"
              prop="carModel"
              :label="$t('车型')"
              label-style="top"
            >
              <vxe-input
                v-model="formData.carModel"
                :placeholder="$t('请输入')"
                clearable
                :disabled="!editable"
              />
            </mt-form-item>
            <mt-form-item
              v-if="formData.shippingMethod === 2"
              prop="licensePlate"
              :label="$t('车牌号')"
              label-style="top"
            >
              <vxe-input
                v-model="formData.licensePlate"
                :placeholder="$t('请输入')"
                clearable
                :disabled="!editable"
              />
            </mt-form-item>
            <mt-form-item
              v-if="formData.shippingMethod === 2"
              prop="driverName"
              :label="$t('司机姓名')"
              label-style="top"
            >
              <vxe-input
                v-model="formData.driverName"
                :placeholder="$t('请输入')"
                clearable
                :disabled="!editable"
              />
            </mt-form-item>
            <mt-form-item
              v-if="formData.shippingMethod === 2"
              prop="driverPhone"
              :label="$t('司机手机号')"
              label-style="top"
            >
              <vxe-input
                v-model="formData.driverPhone"
                :placeholder="$t('请输入')"
                clearable
                :disabled="!editable"
              />
            </mt-form-item>
            <mt-form-item prop="sendTime" :label="$t('发货日期')" label-style="top">
              <vxe-input
                type="date"
                v-model="formData.sendTime"
                :placeholder="$t('请选择')"
                label-format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                clearable
                :disabled="!editable"
              />
            </mt-form-item>
            <mt-form-item prop="expectedArrivalTime" :label="$t('预计到货时间')" label-style="top">
              <vxe-input
                type="date"
                v-model="formData.expectedArrivalTime"
                :placeholder="$t('请选择')"
                :disabled-method="dateDisabled"
                label-format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                clearable
                :disabled="!editable"
              />
            </mt-form-item>
            <mt-form-item prop="consignee" :label="$t('收货人')" label-style="top">
              <vxe-input v-model="formData.consignee" clearable :disabled="true" />
            </mt-form-item>
            <mt-form-item prop="contactPhone" :label="$t('收货联系方式')" label-style="top">
              <vxe-input v-model="formData.contactPhone" clearable :disabled="true" />
            </mt-form-item>
            <mt-form-item prop="deliveryAddress" :label="$t('配送地址')" label-style="top">
              <vxe-input v-model="formData.deliveryAddress" clearable :disabled="true" />
            </mt-form-item>
            <mt-form-item prop="shipmentRemark" :label="$t('发货备注')" label-style="top">
              <vxe-input
                v-model="formData.shipmentRemark"
                :placeholder="$t('请输入')"
                clearable
                :disabled="!editable"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </div>
      <div class="top-toggle-btn">
        <div @click="handleToggle"><i class="vxe-icon-arrow-up" /></div>
      </div>
    </div>
    <div class="body-container">
      <mt-tabs
        ref="tabsRef"
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabList"
        :halt-select="false"
        @handleSelectTab="(index) => handleTabChange(index)"
        style="background-color: #fff"
      />
      <keep-alive :include="keepArr">
        <component
          ref="mainContent"
          style="height: calc(100% - 50px)"
          :is="activeComponent"
          :data-info="formData"
          :table-data="tableData"
          @updateDetail="updateDetail"
        />
      </keep-alive>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import {
  statusOptions,
  businessTypeOptions,
  srmSignFlagOptions,
  shippingMethodOptions,
  deliveryTypeOptions
} from './config'

export default {
  data() {
    const dateDisabled = (params) => {
      const { date } = params
      return date < dayjs().startOf('day')
    }
    return {
      isExpand: true,
      formData: {
        businessType: 1
      },
      activeTabIndex: 0,
      keepArr: [
        'ItemTab',
        'AttachmentTab',
        'CommercialTab',
        'OverseasComponentTab',
        'OverseasNonComponentTab'
      ],

      businessTypeOptions,
      srmSignFlagOptions,
      shippingMethodOptions,
      deliveryTypeOptions,

      companyOptions: [],
      buyerOrgOptions: [],
      currencyOptions: [],

      purchaseApplyDetailDtoList: [],
      pvFileRequestList: [],

      dateDisabled,

      tableData: [],
      deliveryItemDtoList: [], // 物料明细tab数据
      commercialSnList: [], // 商用SN码tab数据
      abroadComponentSnList: [], // 海外组件SN码tab数据
      abroadNonComponentSnList: [], // 海外非组件SN码tab数据
      fileDtoList: [] // 附件tab数据
    }
  },
  computed: {
    pageType() {
      return this.$route.query?.type || 'create'
    },
    sourceType() {
      return this.$route.query?.source || 'plan'
    },
    editable() {
      return this.pageType === 'create' || [0, 3].includes(this.formData.status)
    },
    formRules() {
      return {
        shippingMethod: [
          {
            required: true,
            message: this.$t('请选择发货方式'),
            trigger: 'blur'
          }
        ],
        logisticsNo: [
          {
            required: true,
            message: this.$t('请输入物流单号'),
            trigger: 'blur'
          }
        ],
        logisticsCompany: [
          {
            required: true,
            message: this.$t('请输入物流公司名称'),
            trigger: 'blur'
          }
        ],
        senderName: [
          {
            required: true,
            message: this.$t('请输入发件人姓名'),
            trigger: 'blur'
          }
        ],
        senderPhone: [
          {
            required: true,
            message: this.$t('请输入发件人手机号'),
            trigger: 'blur'
          }
        ],
        carModel: [
          {
            required: true,
            message: this.$t('请输入车型'),
            trigger: 'blur'
          }
        ],
        licensePlate: [
          {
            required: true,
            message: this.$t('请输入车牌号'),
            trigger: 'blur'
          }
        ],
        driverName: [
          {
            required: true,
            message: this.$t('请输入司机姓名'),
            trigger: 'blur'
          }
        ],
        driverPhone: [
          {
            required: true,
            message: this.$t('请输入司机手机号'),
            trigger: 'blur'
          }
        ],
        sendTime: [
          {
            required: true,
            message: this.$t('请选择发货时间'),
            trigger: 'blur'
          }
        ],
        expectedArrivalTime: [
          {
            required: true,
            message: this.$t('请选择预计到货时间'),
            trigger: 'blur'
          }
        ]
      }
    },
    detailToolbar() {
      return [
        {
          code: 'back',
          name: this.$t('返回')
        },
        {
          code: 'submit',
          name: this.$t('提交'),
          status: 'primary',
          isHidden: !this.editable
        },
        {
          code: 'confirm',
          name: this.$t('签收确认'),
          status: 'primary',
          isHidden: !this.$route.query?.showConfirmBtn
        }
      ]
    },
    tabList() {
      let tabs = [
        { title: this.$t('物料明细'), compName: 'ItemTab', isShow: true },
        {
          title: this.$t('户用SN码'),
          compName: 'CommercialTab',
          isShow: [1].includes(this.formData.businessType)
        },
        {
          title: this.$t('商用SN码'),
          compName: 'CommercialTab',
          isShow: [2].includes(this.formData.businessType)
        },
        {
          title: this.$t('海外组件SN码'),
          compName: 'OverseasComponentTab',
          isShow: this.formData.businessType === 3
        },
        {
          title: this.$t('海外非组件SN码'),
          compName: 'OverseasNonComponentTab',
          isShow: this.formData.businessType === 3
        },
        { title: this.$t('附件'), compName: 'AttachmentTab', isShow: true }
      ]
      return tabs.filter((v) => v.isShow)
    },
    activeComponent() {
      let comp = ''
      switch (this.activeTabIndex) {
        case 0:
          // 物料明细
          comp = () => import('./components/itemTab.vue')
          break
        case 1:
          if ([1, 2].includes(this.formData.businessType)) {
            // 商用SN码
            comp = () => import('./components/commercialTab.vue')
          } else if (this.formData.businessType === 3) {
            // 海外组件SN码
            comp = () => import('./components/overseasComponentTab.vue')
          } else {
            // 相关附件
            comp = () => import('./components/attachmentTab.vue')
          }
          break
        case 2:
          if (this.formData.businessType === 3) {
            // 海外非组件SN码
            comp = () => import('./components/overseasNonComponentTab.vue')
          } else {
            // 相关附件
            comp = () => import('./components/attachmentTab.vue')
          }
          break
        case 3:
          comp = () => import('./components/attachmentTab.vue')
          break
        default:
          return
      }
      return comp
    }
  },
  filters: {
    statusFormat(value) {
      let text = statusOptions.find((v) => v.value === value)?.text || ''
      return text
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      let selectedRecords = sessionStorage.getItem('supplyPlanSelectedRecords')
        ? JSON.parse(sessionStorage.getItem('supplyPlanSelectedRecords'))
        : []
      let itemTabData = sessionStorage.getItem('itemTabData')
        ? JSON.parse(sessionStorage.getItem('itemTabData'))
        : []
      if (selectedRecords?.length !== 0) {
        this.formData.businessType = selectedRecords[0].businessType
        this.formData.supplierTenantId = selectedRecords[0].supplierTenantId
        this.formData.supplierCode = selectedRecords[0].supplierCode
        this.formData.supplierName = selectedRecords[0].supplierName
        this.formData.companyCode =
          this.sourceType === 'plan' && this.pageType === 'create'
            ? itemTabData[0].companyCode
            : selectedRecords[0].companyCode
        this.formData.companyName =
          this.sourceType === 'plan' && this.pageType === 'create'
            ? itemTabData[0].companyName
            : selectedRecords[0].companyName
        this.formData.siteCode = selectedRecords[0].siteCode
        this.formData.siteName = selectedRecords[0].siteName
        this.formData.srmSignFlag = selectedRecords[0].srmSignFlag
        this.formData.warehouseCode = selectedRecords[0].warehouseCode
        this.formData.warehouseName = selectedRecords[0].warehouseName
        this.formData.consignee = selectedRecords[0].consignee
        this.formData.contactPhone = selectedRecords[0].contactPhone
        this.formData.deliveryAddress = selectedRecords[0].deliveryAddress
        this.formData.deliveryType =
          selectedRecords[0]?.deliveryType ?? (this.sourceType === 'plan' ? 9 : 1)
        if (this.pageType !== 'create') {
          this.formData.status = selectedRecords[0].status
          this.formData.deliveryCode = selectedRecords[0].deliveryCode
          this.formData.createUserName = selectedRecords[0].createUserName
          this.formData.createTime = selectedRecords[0].createTime
          this.formData.shippingMethod = selectedRecords[0].shippingMethod
          this.formData.logisticsNo = selectedRecords[0].logisticsNo
          this.formData.logisticsCompany = selectedRecords[0].logisticsCompany
          this.formData.senderName = selectedRecords[0].senderName
          this.formData.senderPhone = selectedRecords[0].senderPhone
          this.formData.carModel = selectedRecords[0].carModel
          this.formData.licensePlate = selectedRecords[0].licensePlate
          this.formData.driverName = selectedRecords[0].driverName
          this.formData.driverPhone = selectedRecords[0].driverPhone
          this.formData.sendTime = selectedRecords[0].sendTime
          this.formData.expectedArrivalTime = selectedRecords[0].expectedArrivalTime
          this.formData.shipmentRemark = selectedRecords[0].shipmentRemark
        }
      }
    },
    updateDetail(data) {
      switch (this.activeTabIndex) {
        case 0:
          this.deliveryItemDtoList = data
          break
        case 1:
          if (this.formData.businessType === 3) {
            this.abroadComponentSnList = data
          } else {
            this.commercialSnList = data
          }
          break
        case 2:
          if (this.formData.businessType === 3) {
            this.abroadNonComponentSnList = data
          } else {
            this.fileDtoList = data
          }
          break
        case 3:
          this.fileDtoList = data
          break
        default:
          break
      }
    },
    handleTabChange(index) {
      this.$refs.tabsRef.activeTab = index
      this.activeTabIndex = index
    },
    handleClickToolBar(e) {
      switch (e.code) {
        case 'back':
          this.handleBack()
          break
        case 'submit':
          this.handleSubmit()
          break
        case 'confirm':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认签收？')
            },
            success: () => {
              this.handleConfirm()
            }
          })
          break
        default:
          break
      }
    },
    async handleSubmit() {
      let params = this.getParams()
      if (Object.entries(params).length === 0) return
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认提交？')
        },
        success: async () => {
          this.$store.commit('startLoading')
          const api =
            this.sourceType === 'plan'
              ? this.$API.receivingManagement.createPlanSupplyPlanApi
              : this.$API.receivingManagement.createOrderSupplyPlanApi
          const res = await api(params).finally(() => {
            this.$store.commit('endLoading')
          })
          if (res.code === 200) {
            this.$toast({ content: this.$t('提交成功！'), type: 'success' })
            this.handleBack()
          }
        }
      })
    },
    getParams() {
      let params = {}
      this.$refs.formRef.validate(async (valid) => {
        if (!valid) {
          //展开头部表单数据，显示报错信息
          this.isExpand = true
          return
        }
        if (this.pageType === 'create') {
          params = {
            ...this.formData,
            sendTime: dayjs(this.formData.sendTime).valueOf(),
            expectedArrivalTime: dayjs(this.formData.expectedArrivalTime).valueOf(),
            deliveryItemDtoList: this.deliveryItemDtoList.map((item) => {
              return {
                ...item,
                requiredDeliveryDate: dayjs(item.requiredDeliveryDate).valueOf(),
                orderDetailId: item?.orderDetailId || item.id
              }
            }),
            commercialSnList: this.commercialSnList.map((item) => {
              return {
                ...item,
                id: item.id.includes('row_') ? null : item.id
              }
            }),
            abroadComponentSnList: this.abroadComponentSnList.map((item) => {
              return {
                ...item,
                id: item.id.includes('row_') ? null : item.id
              }
            }),
            abroadNonComponentSnList: this.abroadNonComponentSnList.map((item) => {
              return {
                ...item,
                id: item.id.includes('row_') ? null : item.id
              }
            }),
            fileDtoList: this.fileDtoList
          }
        }
        if (
          params.deliveryItemDtoList?.length &&
          params.deliveryItemDtoList.some((v) => !v.deliveryQty)
        ) {
          this.$toast({ content: this.$t('请填写发货数量'), type: 'warning' })
          this.$refs.tabsRef.activeTab = 0
          this.activeTabIndex = 0
          params = {}
        }
      })
      return params
    },
    handleConfirm() {
      this.$API.receivingManagement
        .confirmDeliveryNoteListApi({ ids: [this.$route.query?.id] })
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('确认成功'), type: 'success' })
            this.handleBack()
          }
        })
    },
    handleBack() {
      this.$router.go(-1)
    },
    // 展开/收缩
    handleToggle() {
      this.isExpand = !this.isExpand
    }
  }
}
</script>

<style scoped lang="scss">
.postpone-full-height {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 8px 8px 0;
  background: #fff;
}
.body-container {
  height: calc(100% - 80px);
}
.top-container {
  flex: 1;
  width: 100%;
  .top-content {
    padding: 8px 8px 0 8px;
    background-color: #f9f9f9;
    border-radius: 8px;
    .top-info {
      display: flex;
      justify-content: space-between;
      .title {
        font-size: 20px;
        font-weight: 700;
        color: #292929;
        span {
          margin-right: 15px;
        }
        .sub-title {
          font-size: 15px;
          color: #777;
        }
      }
      .top-title {
        display: flex;
        padding-top: 10px;
        span {
          margin-right: 35px;
        }
      }
      .top-btns {
        text-align: right;
        margin-bottom: 10px;
      }
    }
    .top-form {
      .mt-form {
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
        justify-content: space-between;
        grid-gap: 5px;
      }
      .vxe-input,
      .vxe-select,
      .vxe-pulldown {
        width: 100%;
        height: 34px;
        line-height: 34px;
      }
      /deep/.vxe-pulldown--panel {
        min-width: unset !important;
        width: 100%;
      }
      .full-width {
        grid-column-start: 1;
        grid-column-end: 6;
      }
    }
  }
  .top-toggle-btn {
    width: 100%;
    div {
      width: 80px;
      height: 15px;
      line-height: 15px;
      margin: auto;
      text-align: center;
      background-color: #4a556b;
      color: #fff;
      font-weight: bold;
      border-radius: 0 0 6px 6px;
    }
  }
}

.top-hidden {
  .top-form {
    display: none;
  }
  .top-toggle-btn {
    div {
      transform: rotate(180deg);
      border-radius: 6px 6px 0 0;
    }
  }
}

.my-dropdown2 {
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 4px 0;
  max-height: 200px;
  border-radius: 4px;
  border: 1px solid #dadce0;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}
.list-item2 {
  line-height: 30px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 0 4px;

  .isSelected {
    font-weight: 700;
    color: #409eff;
  }
}
.list-item2:hover {
  background-color: #f5f7fa;
}
.empty-tip {
  width: 100%;
  line-height: 30px;
  padding: 0 4px;
  text-align: center;
  color: #c0c4cc;
}

::v-deep {
  .mt-form-item {
    margin-bottom: 15px;
    .label {
      margin: 0 10px 5px 0;
    }
  }
  .mt-tabs-container {
    background-color: #fff;
  }

  .mt-tabs-container ul.tab-container li.tab-item2.active {
    border-bottom: 2px solid #409eff;
  }
  .mt-tabs-container ul.tab-container li.tab-item2.active,
  .mt-tabs-container ul.tab-container li.tab-item2:hover {
    color: #31374e;
  }

  .vxe-textarea--inner {
    min-height: 34px;
    height: 100%;
  }
}
</style>
