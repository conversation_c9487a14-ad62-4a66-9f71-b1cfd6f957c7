<!-- 采购订单 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item :label="$t('采购订单号')" prop="orderCode">
          <mt-input
            v-model="searchFormModel.orderCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供应商')" prop="supplierCodeList">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCodeList"
            url="/masterDataManagement/tenant/supplier/paged-query-distinct-no-scope"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('公司')" prop="companyCode">
          <RemoteAutocomplete
            v-model="searchFormModel.companyCode"
            url="/masterDataManagement/tenant/organization/specified-level-paged-query"
            :placeholder="$t('请选择')"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            :params="{
              organizationLevelCodes: ['ORG01', 'ORG02']
            }"
          />
        </mt-form-item>
        <mt-form-item :label="$t('工厂')" prop="siteCodeList">
          <RemoteAutocomplete
            v-model="searchFormModel.siteCodeList"
            :url="$API.masterData.getSiteListUrl"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          />
        </mt-form-item>
        <mt-form-item prop="itemCodeList" :label="$t('物料')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.itemCodeList"
            :url="$API.masterData.getItemListUrlPage"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :search-fields="['itemName', 'itemCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('规格型号')" prop="specificationModel">
          <mt-input
            v-model="searchFormModel.specificationModel"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="requiredDeliveryDate" :label="$t('要求交期')">
          <mt-date-range-picker
            v-model="searchFormModel.requiredDeliveryDate"
            @change="(e) => dateTimeChange(e, 'requiredDeliveryDate')"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="orderDate" :label="$t('订单日期')">
          <mt-date-range-picker
            v-model="searchFormModel.orderDate"
            @change="(e) => dateTimeChange(e, 'orderDate')"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="categoryCode" :label="$t('品类')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.categoryCode"
            url="/masterDataManagement/tenant/category/paged-query"
            :placeholder="$t('请选择')"
            :fields="{ text: 'categoryName', value: 'categoryCode' }"
            :search-fields="['categoryName', 'categoryCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('大类编码')" prop="largeCategoryCode">
          <mt-input
            v-model="searchFormModel.largeCategoryCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('中类编码')" prop="mediumCategoryCode">
          <mt-input
            v-model="searchFormModel.mediumCategoryCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('小类编码')" prop="smallCategoryCode">
          <mt-input
            v-model="searchFormModel.smallCategoryCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('业务类型')" prop="businessType">
          <mt-select
            v-model="searchFormModel.businessType"
            :data-source="businessTypeOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('加急状态')" prop="urgentStatus">
          <mt-multi-select
            v-model="searchFormModel.urgentStatus"
            :data-source="urgentStatusOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="urgentDate" :label="$t('加急日期')">
          <mt-date-range-picker
            v-model="searchFormModel.urgentDate"
            @change="(e) => dateTimeChange(e, 'urgentDate')"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('是否SRM签收')" prop="srmSignFlag">
          <mt-select
            v-model="searchFormModel.srmSignFlag"
            :data-source="srmSignFlagOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('入库仓库编码')" prop="warehouseCode">
          <mt-input
            v-model="searchFormModel.warehouseCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('收货人')" prop="consignee">
          <mt-input
            v-model="searchFormModel.consignee"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('收货联系方式')" prop="contactPhone">
          <mt-input
            v-model="searchFormModel.contactPhone"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('配送地址')" prop="deliveryAddress">
          <mt-input
            v-model="searchFormModel.deliveryAddress"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('采方备注')" prop="purchaseRemark">
          <mt-input
            v-model="searchFormModel.purchaseRemark"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供方备注')" prop="supplierRemark">
          <mt-input
            v-model="searchFormModel.supplierRemark"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            @change="(e) => dateTimeChange(e, 'createTime')"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="updateTime" :label="$t('更新时间')">
          <mt-date-range-picker
            v-model="searchFormModel.updateTime"
            @change="(e) => dateTimeChange(e, 'updateTime')"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      row-id="id"
      grid-id="944bea5d-f28b-6ee3-e5ab-4aa318302733"
      show-overflow
      keep-source
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :loading="item.loading"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import {
  purchaseOrderColumnData,
  statusOptions,
  businessTypeOptions,
  urgentStatusOptions,
  srmSignFlagOptions
} from '../config/index'
export default {
  components: { CollapseSearch, ScTable, RemoteAutocomplete },
  data() {
    const startDate = dayjs().subtract(1, 'month').startOf('month')
    return {
      searchFormModel: {
        createTime: [new Date(startDate), new Date()],
        createTimeStart: this.getUnix(dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00')),
        createTimeEnd: this.getUnix(dayjs(new Date()).format('YYYY-MM-DD 23:59:59'))
      },
      searchFormRules: {},
      toolbar: [{ code: 'create', name: this.$t('创建签收单'), status: 'info', loading: false }],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: purchaseOrderColumnData,
      loading: false,
      tableData: [],

      statusOptions,
      businessTypeOptions,
      urgentStatusOptions,
      srmSignFlagOptions
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    dateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[field + 'End'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      const startDate = dayjs().subtract(1, 'month').startOf('month')
      this.searchFormModel.createTime = [new Date(startDate), new Date()]
      this.searchFormModel.createTimeStart = this.getUnix(
        dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00')
      )
      this.searchFormModel.createTimeEnd = this.getUnix(
        dayjs(new Date()).format('YYYY-MM-DD 23:59:59')
      )
      this.handleSearch()
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.receivingManagement
        .pagePurchaseOrderApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['create']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'create':
          this.handleCreate(selectedRecords)
          break
        default:
          break
      }
    },
    handleCreate(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      this.$API.receivingManagement.checkOrderSupplyPlanApi({ ids }).then((res) => {
        if (res.code === 200) {
          if (res.data) {
            sessionStorage.setItem('supplyPlanSelectedRecords', JSON.stringify(selectedRecords))
            this.$router.push({
              path: '/purchase-pv/receiving-management/supply-plan-detail',
              query: {
                type: 'create',
                source: 'order',
                timeStamp: new Date().getTime()
              }
            })
          }
        }
      })
    }
  }
}
</script>
