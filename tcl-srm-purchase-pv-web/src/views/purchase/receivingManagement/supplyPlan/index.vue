<!-- 采方-收货管理-供货计划 -->
<template>
  <div>
    <mt-tabs
      class="toggle-tab"
      :e-tab="false"
      :data-source="tabList"
      :selected-item="selectedItem"
      @handleSelectTab="handleSelectTab"
    />
    <div class="toggle-content">
      <DeliverySchedule ref="listRef" v-show="tabIndex == 0" />
      <PurchaseOrder ref="detailRef" v-show="tabIndex == 1" />
    </div>
  </div>
</template>

<script>
import DeliverySchedule from './pages/DeliverySchedule.vue'
import PurchaseOrder from './pages/PurchaseOrder.vue'
export default {
  components: { DeliverySchedule, PurchaseOrder },
  data() {
    return {
      tabList: [{ title: this.$t('交货计划') }, { title: this.$t('采购订单') }],
      selectedItem: 0,
      tabIndex: 0
    }
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
    }
  }
}
</script>
