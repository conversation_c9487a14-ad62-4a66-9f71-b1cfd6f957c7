<template>
  <div>
    <sc-table
      ref="scTableRef"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      keep-source
      :sortable="false"
      :is-show-right-btn="true"
      :is-show-refresh-bth="pageType !== 'create'"
      @refresh="getFileList"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #fileNameDefault="{ row }">
        <span style="color: #2783fe; cursor: pointer" @click="handlePreview(row)">
          {{ row.fileName }}
        </span>
      </template>
    </sc-table>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { download } from '@/utils/utils'
import { billTypeOptions } from '../config'
export default {
  name: 'AttachmentTab',
  components: { ScTable },
  data() {
    return {
      loading: false,
      tableData: [],
      billType: null
    }
  },
  computed: {
    pageType() {
      return this.$route.query?.type
    },
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'billType',
          title: this.$t('附件类型'),
          formatter: ({ cellValue }) => {
            let item = billTypeOptions.find((item) => item.value === cellValue)
            return item ? item.text : ''
          }
        },
        {
          field: 'fileName',
          title: this.$t('文件名'),
          slots: {
            default: 'fileNameDefault'
          }
        },
        {
          field: 'fileType',
          title: this.$t('文件类型')
        },
        {
          field: 'fileSize',
          title: this.$t('文件大小')
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间')
        }
      ]
    },
    toolbar() {
      let btns = []
      if (['create'].includes(this.pageType)) {
        btns = [
          { code: 'upload', name: this.$t('上传'), status: 'info', loading: false },
          { code: 'download', name: this.$t('下载'), status: 'info', loading: false },
          { code: 'delete', name: this.$t('删除'), status: 'info', loading: false }
        ]
      }
      if (['edit'].includes(this.pageType)) {
        btns = [
          { code: 'upload', name: this.$t('上传'), status: 'info', loading: false },
          { code: 'download', name: this.$t('下载'), status: 'info', loading: false },
          { code: 'delete', name: this.$t('删除'), status: 'info', loading: false },
          { code: 'save', name: this.$t('保存'), status: 'info', loading: false }
        ]
      }
      return btns
    }
  },
  created() {
    if (this.pageType !== 'create') {
      this.getFileList()
    }
  },
  methods: {
    getFileList() {
      this.loading = true
      let params = {
        ids: [this.$route.query?.id]
      }
      this.$API.fileService
        .getFileListByIdApi(params)
        .then((res) => {
          if (res.code === 200) {
            this.tableData = res.data
            this.$emit('updateDetail', this.tableData)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['delete', 'download']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (selectedRecords.length > 1 && e.code === 'download') {
        this.$toast({ content: this.$t('只能选择一行进行下载操作'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'upload':
          this.handleSelect()
          break
        case 'download':
          this.handleDownload(selectedRecords[0])
          break
        case 'delete':
          this.handleDelete()
          break
        case 'save':
          this.handleSave()
          break
        default:
          break
      }
    },
    handleSelect() {
      this.$dialog({
        modal: () => import('./selectTypeDialog.vue'),
        data: {
          title: this.$t('选择附件类型')
        },
        success: (billType) => {
          this.billType = billType
          this.$dialog({
            modal: () => import('@/components/Upload/index.vue'),
            data: {
              title: this.$t('导入')
            },
            success: (res) => {
              res.fileId = res.id
              res.fileUrl = res.url
              this.handleUpload(res)
            }
          })
        }
      })
    },
    handleUpload(res) {
      this.tableData.push({
        ...res,
        billType: this.billType
      })
      this.$emit('updateDetail', this.tableData)
    },
    handlePreview(row) {
      let params = {
        id: row?.fileId || row.id,
        useType: 2
      }
      this.$API.fileService.getMtPreview(params).then((res) => {
        window.open(res.data)
      })
    },
    handleDownload(row) {
      this.$loading()
      this.$API.fileService.downloadPrivateFile({ id: row?.fileId || row.id }).then((res) => {
        this.$hloading()
        download({
          fileName: row.fileName,
          blob: new Blob([res.data])
        })
      })
    },
    handleDelete() {
      this.tableRef.removeCheckboxRow()
      const currentViewRecords = this.tableRef.getTableData().visibleData
      this.tableData = currentViewRecords
      this.$emit('updateDetail', currentViewRecords)
    },
    handleSave() {
      let data = this.tableData.map((item) => {
        return {
          ...item,
          dataId: this.$route.query?.id
        }
      })
      this.$API.fileService.saveFileListApi(data).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('保存成功'), type: 'success' })
          this.getFileList()
        }
      })
    }
  }
}
</script>
