<template>
  <div>
    <sc-table
      ref="scTableRef"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      keep-source
      :sortable="false"
      :is-show-right-btn="true"
      :is-show-refresh-bth="pageType !== 'create'"
      @refresh="getTableData"
      @edit-closed="editComplete"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
    <mt-page
      v-if="pageType !== 'create'"
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import ScTable from '@/components/ScTable/src/index'
import VxeRemoteSearch from '@/components/VxeRemoteSearch/index.vue'
import { getHeadersFileName, download } from '@/utils/utils'
import { statusOptions } from '../../snCodeInfoUpload/config/index'
export default {
  name: 'CommercialTab',
  // eslint-disable-next-line vue/no-unused-components
  components: { ScTable, VxeRemoteSearch },
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 100,
        pageSizes: [100, 200, 500, 1000]
      },
      editRules: {
        supplierCode: [{ required: true, message: this.$t('必填') }]
      },
      statusOptions,
      businessTypeOptions: [
        { label: this.$t('户用业务'), value: 'HY' },
        { label: this.$t('商用业务'), value: 'SY' }
      ]
    }
  },
  computed: {
    pageType() {
      return this.$route.query?.type
    },
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'id',
          title: this.$t('ID')
        },
        {
          field: 'status',
          title: this.$t('状态'),
          formatter: ({ cellValue }) => {
            let item = statusOptions.find((item) => item.value === cellValue)
            return item ? item.text : ''
          }
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商编码'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-remote-search
                  v-model={row.supplierCode}
                  placeholder={this.$t('请选择')}
                  fields={{ text: 'supplierName', value: 'supplierCode' }}
                  request-info={{
                    urlPre: 'masterData',
                    url: 'supplierDistinctPagedQueryNoScope',
                    searchFields: ['supplierCode', 'supplierName'],
                    params: {
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  onChange={(e) => {
                    row.supplierName = e?.supplierName || null
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'supplierName',
          title: this.$t('供应商名称'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.supplierName}
                  placeholder={this.$t('请选择供应商编码')}
                  transfer
                  disabled
                />
              ]
            }
          }
        },
        {
          field: 'materialCode',
          title: this.$t('物料编码'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.materialCode}
                  fields={{ text: 'itemName', value: 'itemCode' }}
                  placeholder={this.$t('请选择')}
                  request-info={{
                    urlPre: 'masterData',
                    url: 'getItemPage',
                    searchFields: ['itemCode', 'itemName'],
                    params: {
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  onChange={(e) => {
                    row.materialName = e?.itemName || null
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'materialName',
          title: this.$t('物料名称'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.materialName}
                  placeholder={this.$t('请选择物料编码')}
                  transfer
                  disabled
                />
              ]
            }
          }
        },
        {
          field: 'snCode',
          title: this.$t('SN码'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.snCode}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'businessType',
          title: this.$t('业务类型'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              let selectedItem = this.businessTypeOptions.find((v) => v.value === row.businessType)
              return [selectedItem ? <div>{selectedItem.label}</div> : null]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.businessType}
                  options={this.businessTypeOptions}
                  placeholder={this.$t('请选择')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'remarks',
          title: this.$t('备注'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.remarks}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'submitJiguangFailureReason',
          title: this.$t('提交（极光）失败原因')
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          minWidth: 160
        },
        {
          field: 'updateUserName',
          title: this.$t('更新人')
        },
        {
          field: 'updateTime',
          title: this.$t('更新时间'),
          minWidth: 160
        }
      ]
    },
    editConfig() {
      return {
        enabled: ['create'].includes(this.pageType),
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    toolbar() {
      let btns = []
      if (['create'].includes(this.pageType)) {
        btns = [
          { code: 'import', name: this.$t('导入'), status: 'info', loading: false },
          { code: 'delete', name: this.$t('删除'), status: 'info', loading: false }
        ]
      } else if (['edit'].includes(this.pageType)) {
        btns = [
          { code: 'import', name: this.$t('导入'), status: 'info', loading: false },
          { code: 'export', name: this.$t('导出'), status: 'info', loading: false },
          { code: 'submit', name: this.$t('提交'), status: 'info', loading: false },
          { code: 'delete', name: this.$t('删除'), status: 'info', loading: false }
        ]
      } else {
        btns = [{ code: 'export', name: this.$t('导出'), status: 'info', loading: false }]
      }
      return btns
    }
  },
  mounted() {
    if (this.pageType !== 'create') {
      this.getTableData()
    }
  },
  methods: {
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    getTableData() {
      this.loading = true
      let params = {
        deliveryIdList: [this.$route.query?.id],
        businessType: this.dataInfo?.businessType === 1 ? 'HY' : 'SY',
        createTimeStart: dayjs(
          dayjs(this.dataInfo?.createTime).format('YYYY-MM-DD 00:00:00')
        ).valueOf(),
        createTimeEnd: dayjs().valueOf(),
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        }
      }
      this.$API.receivingManagement
        .pageCommercialSnCodeInfoUploadApi(params)
        .then((res) => {
          if (res.code === 200) {
            const { total = 0, records = [] } = res.data
            this.pageSettings.totalRecordsCount = Number(total)
            this.pageSettings.totalPages = Math.ceil(total / this.pageSettings.pageSize)
            this.tableData = records
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['delete', 'submit']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'import':
          this.handleImport()
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        case 'submit':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认提交？')
            },
            success: () => {
              this.handleSubmit(selectedRecords)
            }
          })
          break
        case 'delete':
          this.handleDelete(selectedRecords)
          break
        default:
          break
      }
    },
    handleDelete(selectedRecords) {
      if (['create'].includes(this.pageType)) {
        this.tableRef.removeCheckboxRow()
        const currentViewRecords = this.tableRef.getTableData().visibleData
        this.tableData = currentViewRecords
        this.$emit('updateDetail', currentViewRecords)
      } else {
        let ids = selectedRecords.map((v) => v.id)
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除？')
          },
          success: () => {
            this.$API.receivingManagement
              .deleteCommercialSnCodeInfoUploadApi({ ids })
              .then((res) => {
                if (res.code === 200) {
                  this.$toast({ content: this.$t('删除成功'), type: 'success' })
                  this.tableRef.removeCheckboxRow()
                }
              })
          }
        })
      }
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        //1、 校验必填
        this.tableRef.validate([row]).then((valid) => {
          if (valid) {
            this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
            return
          }

          const currentViewRecords = this.tableRef.getTableData().visibleData
          this.$emit('updateDetail', currentViewRecords)
        })
      }
    },
    handleImport() {
      if (this.pageType === 'create') {
        this.$dialog({
          modal: () => import('@/components/uploadDialog/index.vue'),
          data: {
            title: this.$t('导入'),
            paramsKey: 'excel',
            importApi: this.$API.receivingManagement.getImportDataCommercialSnCodeInfoUploadApi,
            downloadTemplateApi: this.$API.receivingManagement.tempCommercialSnCodeInfoUploadApi
          },
          success: (res) => {
            if (res?.length !== 0) {
              res.forEach((item) => {
                this.tableData.push(item)
              })
              this.$emit('updateDetail', this.tableData)
            }
          }
        })
      } else {
        this.$dialog({
          modal: () => import('@/components/uploadDialog/index.vue'),
          data: {
            title: this.$t('导入'),
            paramsKey: 'excel',
            importApi:
              this.dataInfo?.businessType === 1
                ? this.$API.receivingManagement.importHouseholdSnCodeInfoUploadApi
                : this.$API.receivingManagement.importCommercialSnCodeInfoUploadApi,
            downloadTemplateApi: this.$API.receivingManagement.tempCommercialSnCodeInfoUploadApi,
            asyncParams: {
              deliveryCode: this.dataInfo?.deliveryCode
            }
          },
          success: () => {
            this.getTableData()
          }
        })
      }
    },
    handleExport(e) {
      const dynamicHeaderMap = {}
      this.columns.forEach((item) => {
        if (item.field) {
          if (['status'].includes(item.field)) {
            dynamicHeaderMap[item.field + 'Name'] = item.title
          } else {
            dynamicHeaderMap[item.field] = item.title
          }
        }
      })
      const params = {
        dynamicHeaderMap,
        pvCommercialSnQueryReq: {
          deliveryIdList: [this.$route.query?.id],
          businessType: this.dataInfo?.businessType === 1 ? 'HY' : 'SY',
          page: {
            current: 1,
            size: 100
          },
          createTimeStart: dayjs(
            dayjs(this.dataInfo?.createTime).format('YYYY-MM-DD 00:00:00')
          ).valueOf(),
          createTimeEnd: dayjs().valueOf()
        }
      }
      this.$API.receivingManagement
        .exportCommercialSnCodeInfoUploadApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    },
    handleSubmit(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      this.$API.receivingManagement.submitCommercialSnCodeInfoUploadApi({ ids }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          this.getTableData()
        }
      })
    }
  }
}
</script>

<style></style>
