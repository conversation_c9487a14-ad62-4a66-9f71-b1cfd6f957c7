<template>
  <div>
    <sc-table
      ref="scTableRef"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      keep-source
      :sortable="false"
      :is-show-right-btn="true"
      :is-show-refresh-bth="pageType !== 'create'"
      @refresh="getTableData"
      @edit-closed="editComplete"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import ScTable from '@/components/ScTable/src/index'
export default {
  name: 'ItemTab',
  components: { ScTable },
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      editRules: {
        deliveryQty: [{ required: true, message: this.$t('必填') }]
      }
    }
  },
  computed: {
    pageType() {
      return this.$route.query?.type
    },
    sourceType() {
      return this.$route.query?.source || 'plan'
    },
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'deliveryPlanNo',
          title: this.$t('交货计划单号')
        },
        {
          field: 'itemCode',
          title: this.$t('物料编码')
        },
        {
          field: 'itemName',
          title: this.$t('物料名称')
        },
        {
          field: 'specificationModel',
          title: this.$t('规格型号')
        },
        {
          field: 'baseUnitCode',
          title: this.$t('单位'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.baseUnitName : ''
          }
        },
        {
          field: 'quantity',
          title: this.$t('订单数量')
        },
        {
          field: 'preDeliveryQty',
          title: this.$t('待发货数量')
        },
        {
          field: 'deliveryQty',
          title: this.$t('发货数量'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.deliveryQty}
                  placeholder={this.$t('请输入')}
                  min='1'
                  max={row.preDeliveryQty}
                  transfer
                  clearable
                  disabled={this.sourceType === 'plan'}
                />
              ]
            }
          }
        },
        {
          field: 'receivedQuantity',
          title: this.$t('签收数量')
        },
        {
          field: 'returnQuantity',
          title: this.$t('退回数量')
        },
        {
          field: 'orderCode',
          title: this.$t('采购订单号')
        },
        {
          field: 'itemNo',
          title: this.$t('采购订单行号')
        },
        {
          field: 'requiredDeliveryDate',
          title: this.$t('要求交期')
        },
        {
          field: 'categoryCode',
          title: this.$t('品类'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.categoryName : ''
          }
        },
        {
          field: 'largeCategoryCode',
          title: this.$t('大类'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.largeCategoryName : ''
          }
        },
        {
          field: 'mediumCategoryCode',
          title: this.$t('中类'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.mediumCategoryName : ''
          }
        },
        {
          field: 'smallCategoryCode',
          title: this.$t('小类'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.smallCategoryName : ''
          }
        },
        {
          field: 'itemRemark',
          title: this.$t('行备注'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.itemRemark}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        }
      ]
    },
    editConfig() {
      return {
        enabled: ['create'].includes(this.pageType),
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    toolbar() {
      let btns = []
      if (['create'].includes(this.pageType)) {
        btns = [{ code: 'delete', name: this.$t('删除'), status: 'info', loading: false }]
      }
      return btns
    }
  },
  mounted() {
    if (this.pageType === 'create') {
      let data = []
      if (this.sourceType === 'plan') {
        data = sessionStorage.getItem('itemTabData')
          ? JSON.parse(sessionStorage.getItem('itemTabData'))
          : []
        data.forEach((item) => {
          item.requiredDeliveryDate = dayjs(Number(item.requiredDeliveryDate)).format('YYYY-MM-DD')
          item.deliveryPlanNo = item.serialNumber
        })
      }
      if (this.sourceType === 'order') {
        data = sessionStorage.getItem('supplyPlanSelectedRecords')
          ? JSON.parse(sessionStorage.getItem('supplyPlanSelectedRecords'))
          : []
      }
      this.tableData = data
      this.$emit('updateDetail', data)
    } else {
      this.getTableData()
    }
  },
  methods: {
    getTableData() {
      this.loading = true
      let params = {
        ids: [this.$route.query?.id]
      }
      this.$API.receivingManagement
        .getDataByDeliveryIdsDeliveryNoteListApi(params)
        .then((res) => {
          if (res.code === 200) {
            this.tableData = res.data.map((item) => {
              return {
                ...item,
                quantity: item.orderQuantity,
                preDeliveryQty: item.requiredQuantity,
                deliveryQty: item.shippedQuantity,
                itemNo: item.orderLineNo
              }
            })
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['delete']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'delete':
          this.handleDelete()
          break
        default:
          break
      }
    },
    handleDelete() {
      this.tableRef.removeCheckboxRow()
      const currentViewRecords = this.tableRef.getTableData().visibleData
      this.$emit('updateDetail', currentViewRecords)
    },
    editComplete(args) {
      // const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        //1、 校验必填
        // this.tableRef.validate([row]).then((valid) => {
        //   if (valid) {
        //     this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
        //     return
        //   }
        // })
        // 2、保存数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        this.$emit('updateDetail', currentViewRecords)
      }
    }
  }
}
</script>

<style></style>
