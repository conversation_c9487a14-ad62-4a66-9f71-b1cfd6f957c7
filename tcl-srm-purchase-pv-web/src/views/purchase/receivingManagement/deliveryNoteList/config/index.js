import { i18n } from '@/main.js'
import dayjs from 'dayjs'

export const statusOptions = [
  { text: i18n.t('发货中'), label: i18n.t('发货中'), value: 2 },
  { text: i18n.t('已完成'), label: i18n.t('已完成'), value: 3 },
  { text: i18n.t('已取消'), label: i18n.t('已取消'), value: 4 },
  { text: i18n.t('已关闭'), label: i18n.t('已关闭'), value: 5 }
]

export const receiptStatusOptions = [
  { text: i18n.t('已签收'), label: i18n.t('已签收'), value: 'Y' },
  { text: i18n.t('未签收'), label: i18n.t('未签收'), value: 'N' },
  { text: i18n.t('签收待确认'), label: i18n.t('签收待确认'), value: 'P' }
]

export const businessTypeOptions = [
  { text: i18n.t('户用业务'), label: i18n.t('户用业务'), value: 1 },
  { text: i18n.t('商用业务'), label: i18n.t('商用业务'), value: 2 },
  { text: i18n.t('海外业务'), label: i18n.t('海外业务'), value: 3 }
]

export const deliveryTypeOptions = [
  { text: i18n.t('采购订单'), label: i18n.t('采购订单'), value: 1 },
  { text: i18n.t('交货计划'), label: i18n.t('交货计划'), value: 9 }
]

export const shippingMethodOptions = [
  { text: i18n.t('自提'), label: i18n.t('自提'), value: 0 },
  { text: i18n.t('快递'), label: i18n.t('快递'), value: 1 },
  { text: i18n.t('厂家直送'), label: i18n.t('厂家直送'), value: 2 },
  { text: i18n.t('速必达配送'), label: i18n.t('速必达配送'), value: 3 }
]

export const syncJgStatusOptions = [
  { text: i18n.t('未同步'), label: i18n.t('未同步'), value: 0 },
  { text: i18n.t('同步中'), label: i18n.t('同步中'), value: 1 },
  { text: i18n.t('同步成功'), label: i18n.t('同步成功'), value: 2 },
  { text: i18n.t('同步失败'), label: i18n.t('同步失败'), value: 3 }
]

export const srmSignFlagOptions = [
  { text: i18n.t('是'), label: i18n.t('是'), value: 1 },
  { text: i18n.t('否'), label: i18n.t('否'), value: 0 }
]

export const operationTypeOptions = [
  { text: i18n.t('新增'), label: i18n.t('新增'), value: 1 },
  { text: i18n.t('取消'), label: i18n.t('取消'), value: 2 },
  { text: i18n.t('签收入库'), label: i18n.t('签收入库'), value: 3 },
  { text: i18n.t('关闭'), label: i18n.t('关闭'), value: 4 }
]

export const listColumnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'deliveryCode',
    title: i18n.t('送货单号'),
    minWidth: 150,
    slots: {
      default: 'codeDefault'
    }
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'receiptStatus',
    title: i18n.t('签收状态'),
    formatter: ({ cellValue }) => {
      let item = receiptStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'businessType',
    title: i18n.t('业务类型'),
    formatter: ({ cellValue }) => {
      let item = businessTypeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称')
  },
  {
    field: 'companyCode',
    title: i18n.t('公司编码')
  },
  {
    field: 'companyName',
    title: i18n.t('公司名称')
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂编码')
  },
  {
    field: 'siteName',
    title: i18n.t('工厂名称')
  },
  {
    field: 'deliveryType',
    title: i18n.t('送货单类型'),
    formatter: ({ cellValue }) => {
      let item = deliveryTypeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'srmSignFlag',
    title: i18n.t('是否SRM签收'),
    formatter: ({ cellValue }) => {
      let item = srmSignFlagOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'warehouseCode',
    title: i18n.t('入库仓库'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.warehouseName : ''
    }
  },
  {
    field: 'receiver',
    title: i18n.t('收货人')
  },
  {
    field: 'receiverContact',
    title: i18n.t('收货联系方式')
  },
  {
    field: 'deliveryAddress',
    title: i18n.t('配送地址')
  },
  {
    field: 'shippingMethod',
    title: i18n.t('发货方式'),
    formatter: ({ cellValue }) => {
      let item = shippingMethodOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'logisticsNo',
    title: i18n.t('物流单号')
  },
  {
    field: 'logisticsCompany',
    title: i18n.t('物流公司名称')
  },
  {
    field: 'senderName',
    title: i18n.t('发件人姓名')
  },
  {
    field: 'senderPhone',
    title: i18n.t('发件人手机号')
  },
  {
    field: 'carModel',
    title: i18n.t('车型')
  },
  {
    field: 'licensePlate',
    title: i18n.t('车牌号')
  },
  {
    field: 'driverName',
    title: i18n.t('司机姓名')
  },
  {
    field: 'driverPhone',
    title: i18n.t('司机手机号')
  },
  {
    field: 'sendTime',
    title: i18n.t('发货日期')
  },
  {
    field: 'expectedArrivalTime',
    title: i18n.t('预计到货时间')
  },
  {
    field: 'shipmentRemark',
    title: i18n.t('发货备注')
  },
  {
    field: 'canceler',
    title: i18n.t('取消人')
  },
  {
    field: 'cancelTime',
    title: i18n.t('取消时间')
  },
  {
    field: 'closer',
    title: i18n.t('关闭人')
  },
  {
    field: 'closeTime',
    title: i18n.t('关闭时间')
  },
  {
    field: 'syncJgStatus',
    title: i18n.t('同步极光状态'),
    formatter: ({ cellValue }) => {
      let item = syncJgStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'syncJgMsg',
    title: i18n.t('同步极光接口信息')
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人')
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160
  }
]

export const detailColumnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'deliveryCode',
    title: i18n.t('送货单号'),
    minWidth: 150
  },
  {
    field: 'deliveryLineNo',
    title: i18n.t('送货单行号')
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'receiptStatus',
    title: i18n.t('签收状态'),
    formatter: ({ cellValue }) => {
      let item = receiptStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'deliveryPlanNo',
    title: i18n.t('交货计划单号')
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称')
  },
  {
    field: 'companyCode',
    title: i18n.t('公司编码')
  },
  {
    field: 'companyName',
    title: i18n.t('公司名称')
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂编码')
  },
  {
    field: 'siteName',
    title: i18n.t('工厂名称')
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称')
  },
  {
    field: 'specificationModel',
    title: i18n.t('规格型号')
  },
  {
    field: 'baseUnitCode',
    title: i18n.t('单位'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.baseUnitName : ''
    }
  },
  {
    field: 'orderQuantity',
    title: i18n.t('订单数量')
  },
  {
    field: 'requiredQuantity',
    title: i18n.t('需求数量')
  },
  {
    field: 'shippedQuantity',
    title: i18n.t('发货数量')
  },
  {
    field: 'receivedQuantity',
    title: i18n.t('签收数量')
  },
  {
    field: 'returnQuantity',
    title: i18n.t('退回数量')
  },
  {
    field: 'orderCode',
    title: i18n.t('采购订单号')
  },
  {
    field: 'orderLineNo',
    title: i18n.t('采购订单行号')
  },
  {
    field: 'deliveryType',
    title: i18n.t('送货单类型'),
    formatter: ({ cellValue }) => {
      let item = deliveryTypeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'requiredDeliveryDate',
    title: i18n.t('要求交期')
  },
  {
    field: 'categoryCode',
    title: i18n.t('品类'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.categoryName : ''
    }
  },
  {
    field: 'largeCategoryName',
    title: i18n.t('大类')
  },
  {
    field: 'mediumCategoryName',
    title: i18n.t('中类')
  },
  {
    field: 'smallCategoryName',
    title: i18n.t('小类')
  },
  {
    field: 'businessType',
    title: i18n.t('业务类型'),
    formatter: ({ cellValue }) => {
      let item = businessTypeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'srmSignFlag',
    title: i18n.t('是否SRM签收'),
    formatter: ({ cellValue }) => {
      let item = srmSignFlagOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'warehouseCode',
    title: i18n.t('入库仓库'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.warehouseName : ''
    }
  },
  {
    field: 'receiver',
    title: i18n.t('收货人')
  },
  {
    field: 'receiverContact',
    title: i18n.t('收货联系方式')
  },
  {
    field: 'deliveryAddress',
    title: i18n.t('配送地址')
  },
  {
    field: 'logisticsNo',
    title: i18n.t('物流单号')
  },
  {
    field: 'logisticsCompany',
    title: i18n.t('物流公司名称')
  },
  {
    field: 'senderName',
    title: i18n.t('发件人姓名')
  },
  {
    field: 'senderPhone',
    title: i18n.t('发件人手机号')
  },
  {
    field: 'carModel',
    title: i18n.t('车型')
  },
  {
    field: 'licensePlate',
    title: i18n.t('车牌号')
  },
  {
    field: 'driverName',
    title: i18n.t('司机姓名')
  },
  {
    field: 'driverPhone',
    title: i18n.t('司机手机号')
  },
  {
    field: 'sendTime',
    title: i18n.t('发货日期')
  },
  {
    field: 'expectedArrivalTime',
    title: i18n.t('预计到货时间')
  },
  {
    field: 'firstReceiveTime',
    title: i18n.t('第一次入库时间'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      return cellValue && cellValue !== '0'
        ? dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
        : ''
    }
  },
  {
    field: 'receiveTime',
    title: i18n.t('最后一次入库时间'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      return cellValue && cellValue !== '0'
        ? dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
        : ''
    }
  },
  {
    field: 'shipmentRemark',
    title: i18n.t('发货备注')
  },
  {
    field: 'canceler',
    title: i18n.t('取消人')
  },
  {
    field: 'cancelTime',
    title: i18n.t('取消时间')
  },
  {
    field: 'closer',
    title: i18n.t('关闭人')
  },
  {
    field: 'closureTime',
    title: i18n.t('关闭时间')
  },
  {
    field: 'syncJgStatus',
    title: i18n.t('同步极光状态'),
    formatter: ({ cellValue }) => {
      let item = syncJgStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'syncJgMsg',
    title: i18n.t('同步极光接口信息')
  },
  {
    field: 'headerRemark',
    title: i18n.t('头备注')
  },
  {
    field: 'itemRemark',
    title: i18n.t('行备注')
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人')
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160
  }
]
