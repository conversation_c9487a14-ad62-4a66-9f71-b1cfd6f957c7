<!-- 采方-收货管理-SN码信息上载 -->
<template>
  <div>
    <mt-tabs
      class="toggle-tab"
      :e-tab="false"
      :data-source="tabList"
      :selected-item="selectedItem"
      @handleSelectTab="handleSelectTab"
    />
    <div class="toggle-content">
      <Household ref="householdRef" v-show="tabIndex == 0" />
      <Commercial ref="commercialRef" v-show="tabIndex == 1" />
      <OverseasComponent ref="overseasComponentRef" v-show="tabIndex == 2" />
      <OverseasNonComponent ref="overseasNonComponentRef" v-show="tabIndex == 3" />
    </div>
  </div>
</template>

<script>
import Household from './pages/Household.vue'
import Commercial from './pages/Commercial.vue'
import OverseasComponent from './pages/OverseasComponent.vue'
import OverseasNonComponent from './pages/OverseasNonComponent.vue'
export default {
  components: { Household, Commercial, OverseasComponent, OverseasNonComponent },
  data() {
    return {
      tabList: [
        { title: this.$t('户用业务') },
        { title: this.$t('商用业务') },
        { title: this.$t('海外组件业务') },
        { title: this.$t('海外非组件业务') }
      ],
      selectedItem: 0,
      tabIndex: 0
    }
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
    }
  }
}
</script>
