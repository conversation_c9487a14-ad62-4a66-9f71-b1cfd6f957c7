import { i18n } from '@/main.js'

export const statusOptions = [
  { text: i18n.t('草稿'), label: i18n.t('草稿'), value: 0 },
  { text: i18n.t('提交成功'), label: i18n.t('提交成功'), value: 1 },
  { text: i18n.t('提交失败'), label: i18n.t('提交失败'), value: 2 }
]

export const householdColumnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'id',
    title: i18n.t('ID')
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'deliveryCode',
    title: i18n.t('送货单号')
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称')
  },
  {
    field: 'materialCode',
    title: i18n.t('物料编码')
  },
  {
    field: 'materialName',
    title: i18n.t('物料名称')
  },
  {
    field: 'snCode',
    title: i18n.t('SN码')
  },
  {
    field: 'submitJiguangFailureReason',
    title: i18n.t('提交（极光）失败原因')
  },
  {
    field: 'remarks',
    title: i18n.t('备注')
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人')
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160
  }
]

export const commercialColumnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'id',
    title: i18n.t('ID')
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'deliveryCode',
    title: i18n.t('送货单号')
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称')
  },
  {
    field: 'materialCode',
    title: i18n.t('物料编码')
  },
  {
    field: 'materialName',
    title: i18n.t('物料名称')
  },
  {
    field: 'snCode',
    title: i18n.t('SN码')
  },
  {
    field: 'submitJiguangFailureReason',
    title: i18n.t('提交（极光）失败原因')
  },
  {
    field: 'remarks',
    title: i18n.t('备注')
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人')
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160
  }
]

export const overseasComponentColumnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'id',
    title: i18n.t('ID')
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'deliveryCode',
    title: i18n.t('送货单号')
  },
  {
    field: 'supplier',
    title: i18n.t('SUPPLIER')
  },
  {
    field: 'serialNo',
    title: i18n.t('SERIAL_NO')
  },
  {
    field: 'testId',
    title: i18n.t('TEST_ID')
  },
  {
    field: 'testDate',
    title: i18n.t('TEST_DATE')
  },
  {
    field: 'testTime',
    title: i18n.t('TEST_TIME')
  },
  {
    field: 'power',
    title: i18n.t('POWER')
  },
  {
    field: 'vmpMod',
    title: i18n.t('VMP_MOD')
  },
  {
    field: 'vocMod',
    title: i18n.t('VOC_MOD')
  },
  {
    field: 'impMod',
    title: i18n.t('IMP_MOD')
  },
  {
    field: 'iscMod',
    title: i18n.t('ISC_MOD')
  },
  {
    field: 'ff',
    title: i18n.t('FF')
  },
  {
    field: 'productType',
    title: i18n.t('PRODUCT_TYPE')
  },
  {
    field: 'class',
    title: i18n.t('CLASS')
  },
  {
    field: 'palletNo',
    title: i18n.t('PALLET_NO')
  },
  {
    field: 'impLevel',
    title: i18n.t('IMP_LEVEL')
  },
  {
    field: 'cartonNo',
    title: i18n.t('CARTON_NO')
  },
  {
    field: 'cellBin',
    title: i18n.t('CELL_BIN')
  },
  {
    field: 'cellColor',
    title: i18n.t('CELL_COLOR')
  },
  {
    field: 'glassType',
    title: i18n.t('GLASS_TYPE')
  },
  {
    field: 'packingListNo',
    title: i18n.t('PACKING_LIST_NO')
  },
  {
    field: 'customerName',
    title: i18n.t('CUSTOMER_NAME')
  },
  {
    field: 'invoiceNo',
    title: i18n.t('INVOICE_NO')
  },
  {
    field: 'poNo',
    title: i18n.t('PO_NO')
  },
  {
    field: 'partNo',
    title: i18n.t('PART_NO')
  },
  {
    field: 'shipDate',
    title: i18n.t('SHIP_DATE')
  },
  {
    field: 'qrcodeWn',
    title: i18n.t('QRCODE_WN')
  },
  {
    field: 'containerNo',
    title: i18n.t('CONTAINER_NO')
  },
  {
    field: 'submitJiguangFailureReason',
    title: i18n.t('提交（极光）失败原因')
  },
  {
    field: 'remarks',
    title: i18n.t('备注')
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人')
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160
  }
]

export const overseasNonComponentColumnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'id',
    title: i18n.t('ID')
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'deliveryCode',
    title: i18n.t('送货单号')
  },
  {
    field: 'supplier',
    title: i18n.t('SUPPLIER')
  },
  {
    field: 'serialNo',
    title: i18n.t('SERIAL_NO')
  },
  {
    field: 'testId',
    title: i18n.t('TEST_ID')
  },
  {
    field: 'testDate',
    title: i18n.t('TEST_DATE')
  },
  {
    field: 'testTime',
    title: i18n.t('TEST_TIME')
  },
  {
    field: 'power',
    title: i18n.t('POWER')
  },
  {
    field: 'vmpMod',
    title: i18n.t('VMP_MOD')
  },
  {
    field: 'vocMod',
    title: i18n.t('VOC_MOD')
  },
  {
    field: 'impMod',
    title: i18n.t('IMP_MOD')
  },
  {
    field: 'iscMod',
    title: i18n.t('ISC_MOD')
  },
  {
    field: 'ff',
    title: i18n.t('FF')
  },
  {
    field: 'productType',
    title: i18n.t('PRODUCT_TYPE')
  },
  {
    field: 'class',
    title: i18n.t('CLASS')
  },
  {
    field: 'palletNo',
    title: i18n.t('PALLET_NO')
  },
  {
    field: 'impLevel',
    title: i18n.t('IMP_LEVEL')
  },
  {
    field: 'cartonNo',
    title: i18n.t('CARTON_NO')
  },
  {
    field: 'cellBin',
    title: i18n.t('CELL_BIN')
  },
  {
    field: 'cellColor',
    title: i18n.t('CELL_COLOR')
  },
  {
    field: 'glassType',
    title: i18n.t('GLASS_TYPE')
  },
  {
    field: 'packingListNo',
    title: i18n.t('PACKING_LIST_NO')
  },
  {
    field: 'customerName',
    title: i18n.t('CUSTOMER_NAME')
  },
  {
    field: 'invoiceNo',
    title: i18n.t('INVOICE_NO')
  },
  {
    field: 'poNo',
    title: i18n.t('PO_NO')
  },
  {
    field: 'partNo',
    title: i18n.t('PART_NO')
  },
  {
    field: 'shipDate',
    title: i18n.t('SHIP_DATE')
  },
  {
    field: 'qrcodeWn',
    title: i18n.t('QRCODE_WN')
  },
  {
    field: 'containerNo',
    title: i18n.t('CONTAINER_NO')
  },
  {
    field: 'submitJiguangFailureReason',
    title: i18n.t('提交（极光）失败原因')
  },
  {
    field: 'remarks',
    title: i18n.t('备注')
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人')
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160
  }
]
