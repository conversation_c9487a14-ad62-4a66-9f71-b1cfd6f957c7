import { i18n } from '@/main'
// 状态
export const statusList = [
  { value: 0, text: i18n.t('草稿') },
  { value: 1, text: i18n.t('有效') },
  { value: 2, text: i18n.t('失效') }
]
// 维度策略
export const dimensionList = [
  { value: 0, text: i18n.t('工厂'), label: i18n.t('工厂') },
  { value: 1, text: i18n.t('工厂+品类'), label: i18n.t('工厂+品类') },
  { value: 2, text: i18n.t('工厂+品类+物料'), label: i18n.t('工厂+品类+物料') }
]
// 是否
export const yesOrNoList = [
  { value: 0, text: i18n.t('否') },
  { value: 1, text: i18n.t('是') }
]

// 列表试图-操作按钮
export const listToolbar = [
  { code: 'add', name: i18n.t('新增'), status: 'info' },
  // { code: 'edit', name: i18n.t('编辑'), status: 'info' },
  { code: 'delete', name: i18n.t('删除'), status: 'info' },
  { code: 'enable', name: i18n.t('生效'), status: 'info' },
  { code: 'disable', name: i18n.t('失效'), status: 'info' },
  { code: 'import', name: i18n.t('导入'), status: 'info' },
  { code: 'export', name: i18n.t('导出'), status: 'info' }
]
