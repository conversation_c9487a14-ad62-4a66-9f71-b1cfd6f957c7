<!-- 采购申请管理 -->
<template>
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item prop="applyNo" :label="$t('采购申请单号')" label-style="top">
          <mt-input
            v-model="searchFormModel.applyNo"
            :show-clear-button="true"
            :placeholder="$t('请输入采购申请单号')"
          />
        </mt-form-item>
        <mt-form-item prop="statusList" :label="$t('申请单状态')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.statusList"
            :data-source="statusOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="businessTypeList" :label="$t('业务类型')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.businessTypeList"
            :data-source="businessTypeOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="title" :label="$t('标题')" label-style="top">
          <mt-input
            v-model="searchFormModel.title"
            :show-clear-button="true"
            :placeholder="$t('支持模糊查询')"
          />
        </mt-form-item>
        <mt-form-item prop="companyCode" :label="$t('公司')">
          <RemoteAutocomplete
            v-model="searchFormModel.companyCode"
            url="/masterDataManagement/tenant/organization/specified-level-paged-query"
            :placeholder="$t('请选择')"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            :params="{
              organizationLevelCodes: ['ORG01', 'ORG02']
            }"
          />
        </mt-form-item>
        <mt-form-item prop="buyerOrgCode" :label="$t('采购组织')" label-style="top">
          <mt-input
            v-model="searchFormModel.buyerOrgCode"
            :show-clear-button="true"
            :placeholder="$t('请输入采购组织')"
          />
        </mt-form-item>
        <mt-form-item prop="applyDept" :label="$t('申请部门')" label-style="top">
          <mt-input
            v-model="searchFormModel.applyDept"
            :show-clear-button="true"
            :placeholder="$t('支持模糊查询')"
          />
        </mt-form-item>
        <mt-form-item prop="applyUser" :label="$t('申请人')" label-style="top">
          <mt-input
            v-model="searchFormModel.applyUser"
            :show-clear-button="true"
            :placeholder="$t('支持模糊查询')"
          />
        </mt-form-item>
        <mt-form-item prop="dataSourceList" :label="$t('来源类型')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.dataSourceList"
            :data-source="dataSourceOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="currencyCode" :label="$t('币种')" label-style="top">
          <mt-input
            v-model="searchFormModel.currencyCode"
            :show-clear-button="true"
            :placeholder="$t('请输入币种')"
          />
        </mt-form-item>
        <mt-form-item prop="projectName" :label="$t('项目名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.projectName"
            :show-clear-button="true"
            :placeholder="$t('支持模糊查询')"
          />
        </mt-form-item>
        <mt-form-item prop="projectManager" :label="$t('项目经理')" label-style="top">
          <mt-input
            v-model="searchFormModel.projectManager"
            :show-clear-button="true"
            :placeholder="$t('支持模糊查询')"
          />
        </mt-form-item>
        <mt-form-item prop="syncOaStatusList" :label="$t('同步OA状态')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.syncOaStatusList"
            :data-source="oaStatusOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')" label-style="top">
          <mt-input
            v-model="searchFormModel.remark"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('创建人')" prop="createUserName">
          <mt-input
            v-model="searchFormModel.createUserName"
            :placeholder="$t('支持模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            @change="(e) => dateChange(e, 'createTime')"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('更新人')" prop="updateUserName">
          <mt-input
            v-model="searchFormModel.updateUserName"
            :placeholder="$t('支持模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="updateTime" :label="$t('更新时间')">
          <mt-date-range-picker
            v-model="searchFormModel.updateTime"
            @change="(e) => dateChange(e, 'updateTime')"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="scTableRef"
      grid-id="444d0c47-54c8-4330-8ae7-84296b8ab2fa"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
      <template #codeDefault="{ row }">
        <div>
          <span style="cursor: pointer; color: #2783fe" @click="handleClick(row)">
            {{ row.applyNo }}
          </span>
        </div>
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import { getHeadersFileName, download } from '@/utils/utils'
import {
  columnData,
  statusOptions,
  oaStatusOptions,
  businessTypeOptions,
  dataSourceOptions
} from './config'
export default {
  components: {
    CollapseSearch,
    ScTable,
    RemoteAutocomplete
  },
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      toolbar: [
        {
          code: 'add',
          name: this.$t('新增'),
          status: 'info',
          loading: false
        },
        {
          code: 'delete',
          name: this.$t('删除'),
          status: 'info',
          loading: false
        },
        {
          code: 'submitOA',
          name: this.$t('提交OA审批'),
          status: 'info',
          loading: false
        },
        {
          code: 'checkOA',
          name: this.$t('查看OA审批'),
          status: 'info',
          loading: false
        },
        {
          code: 'close',
          name: this.$t('关闭'),
          status: 'info',
          loading: false
        },
        {
          code: 'export',
          name: this.$t('导出'),
          status: 'info',
          loading: false
        }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      statusOptions,
      oaStatusOptions,
      businessTypeOptions,
      dataSourceOptions
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    handleClick(row) {
      let type = [0, 3].includes(row.status) ? 'edit' : 'check'
      this.$router.push({
        name: 'procurement-request-management-detail',
        query: {
          type,
          timeStamp: new Date().getTime(),
          id: row.id
        }
      })
    },
    dateChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'S'] = this.getUnix(dayjs(e.startDate))
        this.searchFormModel[field + 'E'] = this.getUnix(dayjs(e.endDate))
      } else {
        this.searchFormModel[field + 'S'] = null
        this.searchFormModel[field + 'E'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          this.currentPage = 1
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.demandManagement
        .pageProcurementRequestManagementApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['edit', 'delete', 'submitOA', 'checkOA', 'close']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (selectedRecords.length > 1 && e.code === 'edit') {
        this.$toast({ content: this.$t('只能选择一行进行编辑操作'), type: 'warning' })
        return
      }
      if (selectedRecords.length > 1 && e.code === 'checkOA') {
        this.$toast({ content: this.$t('只能选择一行进行查看OA审批操作'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'edit':
          this.handleEdit(selectedRecords[0])
          break
        case 'delete':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除？')
            },
            success: () => {
              this.handleDelete(selectedRecords)
            }
          })
          break
        case 'submitOA':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认提交OA审批？')
            },
            success: () => {
              this.handleSubmitOA(selectedRecords)
            }
          })
          break
        case 'checkOA':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认查看OA审批？')
            },
            success: () => {
              this.handleCheckOA(selectedRecords)
            }
          })
          break
        case 'close':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认关闭？')
            },
            success: () => {
              this.handleClose(selectedRecords)
            }
          })
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleAdd() {
      this.$router.push({
        name: 'procurement-request-management-detail',
        query: {
          type: 'add',
          timeStamp: new Date().getTime()
        }
      })
    },
    handleEdit(row) {
      this.$router.push({
        name: 'procurement-request-management-detail',
        query: {
          type: 'edit',
          timeStamp: new Date().getTime(),
          id: row.id
        }
      })
    },
    handleDelete(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      this.$API.demandManagement.deleteProcurementRequestManagementApi({ ids }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleSubmitOA(selectedRecords) {
      let params = {
        ids: selectedRecords.map((v) => v.id)
      }
      this.$API.demandManagement.submitOAProcurementRequestManagementApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleCheckOA(selectedRecords) {
      let url = selectedRecords[0].oaUrl
      if (url) {
        window.open(url)
      } else {
        this.$toast({ content: this.$t('未提交OA审批'), type: 'warning' })
      }
    },
    handleClose(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      this.$API.demandManagement.closeProcurementRequestManagementApi({ ids }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('关闭成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleExport(e) {
      const dynamicHeaderMap = {}
      columnData.forEach((item) => {
        if (item.field) {
          if (['status', 'businessType', 'dataSource'].includes(item.field)) {
            dynamicHeaderMap[item.field + 'Name'] = item.title
          } else {
            dynamicHeaderMap[item.field] = item.title
          }
        }
      })
      const params = {
        dynamicHeaderMap,
        purchaseApplyQueryReq: {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        }
      }
      this.$API.demandManagement
        .exportProcurementRequestManagementApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
