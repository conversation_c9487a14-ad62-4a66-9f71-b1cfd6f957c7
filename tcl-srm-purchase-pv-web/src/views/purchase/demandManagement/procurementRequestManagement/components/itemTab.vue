<template>
  <div>
    <sc-table
      ref="scTableRef"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      keep-source
      :sortable="false"
      :is-show-right-btn="false"
      :is-show-refresh-bth="false"
      @edit-closed="editComplete"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import ScTable from '@/components/ScTable/src/index'
import VxeRemoteSearch from '@/components/VxeRemoteSearch/index.vue'
import { deliveryMethodOptions, purchaseMethodOptions, isRdcWarehouseOptions } from '../config'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  name: 'ItemTab',
  // eslint-disable-next-line vue/no-unused-components
  components: { ScTable, VxeRemoteSearch },
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    },
    refreshKey: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      editRules: {
        siteCode: [{ required: true, message: this.$t('必填') }],
        itemName: [{ required: true, message: this.$t('必填') }],
        unitCode: [{ required: true, message: this.$t('必填') }],
        requiredQuantity: [{ required: true, message: this.$t('必填') }],
        purchaseMethod: [{ required: true, message: this.$t('必填') }],
        categoryCode: [{ required: true, message: this.$t('必填') }]
      },

      siteOptions: [],
      deliveryMethodOptions,
      purchaseMethodOptions,
      isRdcWarehouseOptions
    }
  },
  watch: {
    refreshKey(val) {
      if (val) {
        if (this.pageType !== 'add') {
          this.getTableData()
        }
      }
    }
  },
  computed: {
    pageType() {
      return this.$route.query?.type
    },
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'siteCode',
          title: this.$t('工厂'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [row.siteCode ? <div>{row.siteCode + '-' + row.siteName}</div> : null]
            },
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.siteCode}
                  placeholder={this.$t('请选择')}
                  fields={{ text: 'siteName', value: 'siteCode' }}
                  request-info={{
                    urlPre: 'masterData',
                    url: 'getCompanySite',
                    params: {
                      organizationId: this.dataInfo?.companyId
                    },
                    recordsPosition: 'data'
                  }}
                  onChange={(e) => {
                    row.siteName = e?.siteName || null
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'brand',
          title: this.$t('品牌'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input v-model={row.brand} placeholder={this.$t('请输入')} transfer clearable />
              ]
            }
          }
        },
        {
          field: 'itemCode',
          title: this.$t('物料编码'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.itemCode}
                  fields={{ text: 'itemName', value: 'itemCode' }}
                  placeholder={this.$t('请选择')}
                  request-info={{
                    urlPre: 'masterData',
                    url: 'getItemPage',
                    searchFields: ['itemCode', 'itemName'],
                    params: {
                      defaultRules: [
                        {
                          field: 'organizationCode',
                          operator: 'equal',
                          value: row.siteCode
                        }
                      ],
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  link-field-value={row.siteCode}
                  disabled={row.siteCode ? false : true}
                  onChange={(e) => {
                    if (!e?.checked) {
                      row.itemName = e?.itemName || null
                      row.specification = e?.itemDescription || null
                      row.unitCode = e?.baseMeasureUnitCode || null
                      row.unit = e?.baseMeasureUnitName || null
                      row.categoryCode = e?.categoryResponse?.categoryCode || null
                      row.categoryName = e?.categoryResponse?.categoryName || null
                      row.bigCategCode = e?.bigCategCode || null
                      row.bigCategName = e?.bigCategName || null
                      row.medCategCode = e?.medCategCode || null
                      row.medCategName = e?.medCategName || null
                      row.smallCategCode = e?.smallCategCode || null
                      row.smallCategName = e?.smallCategName || null
                    }
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'itemName',
          title: this.$t('物料名称'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.itemName}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'tempMaterialCode',
          title: this.$t('临时物料编码'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.tempMaterialCode} transfer disabled />]
            }
          }
        },
        // {
        //   field: 'specification',
        //   title: this.$t('规格型号'),
        //   editRender: {},
        //   slots: {
        //     edit: ({ row }) => {
        //       return [
        //         <vxe-input
        //           v-model={row.specification}
        //           placeholder={this.$t('请输入')}
        //           transfer
        //           clearable
        //         />
        //       ]
        //     }
        //   }
        // },
        {
          field: 'unitCode',
          title: this.$t('单位'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [row.unitCode ? <div> {row.unitCode + '-' + row.unit}</div> : null]
            },
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.unitCode}
                  placeholder={this.$t('请选择')}
                  fields={{ text: 'unitName', value: 'unitCode' }}
                  request-info={{
                    urlPre: 'masterData',
                    url: 'pagedQueryUnit',
                    searchFields: ['unitCode', 'unitName'],
                    params: {
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  link-field-value={row.itemCode}
                  fill-value={row.unitCode}
                  onChange={(e) => {
                    row.unit = e?.unitName || null
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'requiredQuantity',
          title: this.$t('需求数量'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.requiredQuantity}
                  placeholder={this.$t('请输入')}
                  min='1'
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'orderQty',
          title: this.$t('已转订单数量'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.orderQty} placeholder={this.$t('请输入')} disabled />]
            }
          }
        },
        {
          field: 'requiredDeliveryDate',
          title: this.$t('要求交期'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              const dateDisabled = (params) => {
                const { date } = params
                return date < new Date()
              }
              return [
                <vxe-input
                  type={'date'}
                  v-model={row.requiredDeliveryDate}
                  placeholder={this.$t('请选择')}
                  transfer
                  clearable
                  label-format='yyyy-MM-dd'
                  value-format='yyyy-MM-dd'
                  disabled-method={dateDisabled}
                />
              ]
            }
          }
        },
        {
          field: 'powerKw',
          title: this.$t('功率(KW)'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.powerKw}
                  placeholder={this.$t('请输入')}
                  min='0'
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'capacity',
          title: this.$t('容量'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.capacity} transfer disabled />]
            }
          }
        },
        {
          field: 'specialRequirements',
          title: this.$t('特殊要求'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.specialRequirements}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'deliveryMethod',
          title: this.$t('配送方式'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              let selectedItem = this.deliveryMethodOptions.find(
                (v) => v.value === row.deliveryMethod
              )
              return [selectedItem ? <div>{selectedItem.label}</div> : null]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.deliveryMethod}
                  options={this.deliveryMethodOptions}
                  placeholder={this.$t('请选择')}
                  clearable
                  filterable
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'deliveryPriority',
          title: this.$t('发货优先级'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.deliveryPriority}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'purchaseMethod',
          title: this.$t('采购方式'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              let selectedItem = this.purchaseMethodOptions.find(
                (v) => v.value === row.purchaseMethod
              )
              return [selectedItem ? <div>{selectedItem.label}</div> : null]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.purchaseMethod}
                  options={this.purchaseMethodOptions}
                  placeholder={this.$t('请选择')}
                  filterable
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'categoryCode',
          title: this.$t('品类'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [
                row.categoryCode ? <div> {row.categoryCode + '-' + row.categoryName}</div> : null
              ]
            },
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.categoryCode}
                  placeholder={this.$t('请选择')}
                  fields={{ text: 'categoryName', value: 'categoryCode' }}
                  request-info={{
                    urlPre: 'masterData',
                    url: 'pageCategoryApi',
                    searchFields: ['categoryCode', 'categoryName'],
                    params: {
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  link-field-value={row.itemCode}
                  fill-value={row.categoryCode}
                  onChange={(e) => {
                    row.categoryName = e?.categoryName || null
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'bigCategCode',
          title: this.$t('大类编码'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.bigCategCode}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'bigCategName',
          title: this.$t('大类名称'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.bigCategName}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'medCategCode',
          title: this.$t('中类编码'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.medCategCode}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'medCategName',
          title: this.$t('中类名称'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.medCategName}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'smallCategCode',
          title: this.$t('小类编码'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.smallCategCode}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'smallCategName',
          title: this.$t('小类名称'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.smallCategName}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'suggestedSupplierCode',
          title: this.$t('建议供应商'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [
                row.suggestedSupplierCode ? (
                  <div> {row.suggestedSupplierCode + '-' + row.suggestedSupplierName}</div>
                ) : null
              ]
            },
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.suggestedSupplierCode}
                  placeholder={this.$t('请选择')}
                  fields={{ text: 'supplierName', value: 'supplierCode' }}
                  request-info={{
                    urlPre: 'masterData',
                    url: 'supplierDistinctPagedQueryNoScope',
                    searchFields: ['supplierCode', 'supplierName'],
                    params: {
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  onChange={(e) => {
                    row.suggestedSupplierName = e?.supplierName || null
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'isRdcWarehouse',
          title: this.$t('是否入RDC仓'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              let selectedItem = this.isRdcWarehouseOptions.find(
                (v) => v.value === row.isRdcWarehouse
              )
              return [selectedItem ? <div>{selectedItem.label}</div> : null]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.isRdcWarehouse}
                  options={this.isRdcWarehouseOptions}
                  placeholder={this.$t('请选择')}
                  clearable
                  filterable
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'warehouseCode',
          title: this.$t('入库仓库'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [
                row.warehouseCode ? <div> {row.warehouseCode + '-' + row.warehouse}</div> : null
              ]
            },
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.warehouseCode}
                  placeholder={this.$t('请选择')}
                  fields={{ text: 'warehouseName', value: 'warehouseCode' }}
                  request-info={{
                    urlPre: 'demandManagement',
                    url: 'queryPvWarehouseList',
                    searchKey: 'fuzzyParam',
                    params: {
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  onChange={(e) => {
                    if (!e?.checked) {
                      row.warehouse = e?.warehouseName || null
                      row.receiverName = e?.contactor || null
                      row.receiverPhone = e?.contactPhone || null
                      row.deliveryAddress = e?.addr || null
                    }
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'receiverName',
          title: this.$t('收货人'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.receiverName}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'receiverPhone',
          title: this.$t('收货联系方式'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.receiverPhone}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'deliveryAddress',
          title: this.$t('收货地址'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.deliveryAddress}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'remarks',
          title: this.$t('备注'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.remarks}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        }
      ]
    },
    editConfig() {
      return {
        enabled: this.pageType !== 'check',
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    toolbar() {
      let btns = []
      if (['add', 'edit'].includes(this.pageType)) {
        btns = [
          { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
          { code: 'delete', name: this.$t('删除'), status: 'info', loading: false },
          { code: 'import', name: this.$t('导入'), status: 'info', loading: false },
          { code: 'export', name: this.$t('导出'), status: 'info', loading: false }
        ]
      }
      return btns
    }
  },
  created() {
    if (this.pageType !== 'add') {
      this.getTableData()
    }
  },
  methods: {
    getTableData() {
      let params = {
        applyId: this.$route.query?.id,
        page: {
          current: 1,
          size: 50
        }
      }
      this.$API.demandManagement.detailPageProcurementRequestManagementApi(params).then((res) => {
        if (res.code === 200) {
          this.tableData = res.data.records.map((item) => {
            return {
              ...item,
              requiredDeliveryDate:
                item.requiredDeliveryDate && item.requiredDeliveryDate !== '0'
                  ? dayjs(Number(item.requiredDeliveryDate)).format('YYYY-MM-DD')
                  : null
            }
          })
          this.$emit('updateDetail', this.tableData)
        }
      })
    },
    getSiteOptions() {
      let params = { organizationId: this.dataInfo?.companyId }
      if (!params.organizationId) {
        return
      }
      this.$API.masterData.getCompanySite(params).then((res) => {
        if (res.code === 200) {
          this.siteOptions = res.data.map((item) => {
            return {
              label: item.siteCode + '-' + item.siteName,
              value: item.siteCode,
              ...item
            }
          })
        }
      })
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['delete']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.code === 'import' && !this.dataInfo?.id) {
        this.$toast({ content: this.$t('请先保存数据再进行导入操作'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          this.handleDelete(selectedRecords)
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleAdd() {
      if (!this.dataInfo?.companyId) {
        this.$toast({ content: this.$t('请先选择公司'), type: 'warning' })
        return
      }
      const item = {
        purchaseMethod: 0
      }
      this.tableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.tableRef.setEditRow(currentViewRecords[0])
      })
    },
    editComplete(args) {
      // const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        // 1、 校验必填
        // this.tableRef.validate([row]).then((valid) => {
        //  if (valid) {
        //    this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
        //    return
        //  }
        // })
        // 2、保存数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        this.$emit('updateDetail', currentViewRecords)
      }
    },
    handleDelete(selectedRecords) {
      if (this.pageType === 'edit') {
        let ids = []
        selectedRecords.forEach((v) => {
          if (!v.id?.includes('row_')) {
            ids.push(v.id)
          }
        })
        if (ids.length !== 0) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除？')
            },
            success: () => {
              this.$API.demandManagement
                .deleteDetailProcurementRequestManagementApi({ ids })
                .then((res) => {
                  if (res.code === 200) {
                    this.$toast({ content: this.$t('删除成功'), type: 'success' })

                    this.tableRef.removeCheckboxRow()
                    const currentViewRecords = this.tableRef.getTableData().visibleData
                    this.$emit('updateDetail', currentViewRecords)
                  }
                })
            }
          })
        } else {
          this.tableRef.removeCheckboxRow()
          const currentViewRecords = this.tableRef.getTableData().visibleData
          this.$emit('updateDetail', currentViewRecords)
        }
      } else {
        this.tableRef.removeCheckboxRow()
        const currentViewRecords = this.tableRef.getTableData().visibleData
        this.$emit('updateDetail', currentViewRecords)
      }
    },
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          downloadTemplateApi: this.$API.demandManagement.tempDetailProcurementRequestManagementApi,
          importApi: this.$API.demandManagement.importDetailProcurementRequestManagementApi,
          paramsKey: 'excel',
          asyncParams: {
            applyId: this.$route.query?.id
          }
        },
        success: () => {
          this.getTableData()
        }
      })
    },
    handleExport(e) {
      const dynamicHeaderMap = {}
      this.columns.forEach((item) => {
        if (item.field) {
          dynamicHeaderMap[item.field] = item.title
        }
      })
      const params = {
        dynamicHeaderMap,
        purchaseApplyDetailQueryReq: {
          applyId: this.$route.query?.id,
          page: {
            current: 1,
            size: 1000
          }
        }
      }
      e.loading = true
      this.$API.demandManagement
        .exportDetailProcurementRequestManagementApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>
