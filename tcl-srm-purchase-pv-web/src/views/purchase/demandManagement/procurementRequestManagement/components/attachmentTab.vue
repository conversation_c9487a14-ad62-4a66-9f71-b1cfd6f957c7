<template>
  <div>
    <sc-table
      ref="sctableRef"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      keep-source
      :sortable="false"
      :is-show-right-btn="false"
      :is-show-refresh-bth="false"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #fileNameDefault="{ row }">
        <span style="color: #2783fe; cursor: pointer" @click="handlePreview(row)">
          {{ row.fileName }}
        </span>
      </template>
    </sc-table>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { download } from '@/utils/utils'
export default {
  name: 'AttachmentTab',
  components: { ScTable },
  data() {
    return {
      loading: false,
      tableData: []
    }
  },
  computed: {
    pageType() {
      return this.$route.query?.type
    },
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'fileName',
          title: this.$t('文件名'),
          slots: {
            default: 'fileNameDefault'
          }
        },
        {
          field: 'fileType',
          title: this.$t('文件类型')
        },
        {
          field: 'fileSize',
          title: this.$t('文件大小')
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间')
        }
      ]
    },
    toolbar() {
      let btns = []
      if (['add', 'edit'].includes(this.pageType)) {
        btns = [
          { code: 'upload', name: this.$t('上传'), status: 'info', loading: false },
          { code: 'download', name: this.$t('下载'), status: 'info', loading: false },
          { code: 'delete', name: this.$t('删除'), status: 'info', loading: false }
        ]
      }
      return btns
    }
  },
  created() {
    if (this.pageType !== 'add') {
      this.getFileList()
    }
  },
  methods: {
    getFileList() {
      let params = {
        ids: [this.$route.query?.id]
      }
      this.$API.demandManagement.fileProcurementRequestManagementApi(params).then((res) => {
        if (res.code === 200) {
          this.tableData = res.data
          this.$emit('updateDetail', this.tableData)
        }
      })
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['delete', 'download']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (selectedRecords.length > 1 && e.code === 'download') {
        this.$toast({ content: this.$t('只能选择一行进行下载操作'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'upload':
          this.$dialog({
            modal: () => import('@/components/Upload/index.vue'),
            data: {
              title: this.$t('导入')
            },
            success: (res) => {
              res.fileId = res.id
              res.fileUrl = res.url
              this.handleUpload(res)
            }
          })
          break
        case 'download':
          e.loading = true
          this.handleDownload(selectedRecords[0], e)
          break
        case 'delete':
          this.handleDelete()
          break
        default:
          break
      }
    },
    handleUpload(res) {
      this.tableData.push(res)
      this.$emit('updateDetail', this.tableData)
    },
    handlePreview(row) {
      let params = {
        id: row?.fileId || row.id,
        useType: 2
      }
      this.$API.fileService.getMtPreview(params).then((res) => {
        window.open(res.data)
      })
    },
    handleDownload(row) {
      this.$loading()
      this.$API.fileService
        .downloadPrivateFile({ id: row?.fileId || row.id })
        .then((res) => {
          this.$hloading()
          download({
            fileName: row.fileName,
            blob: new Blob([res.data])
          })
        })
        .finally(() => {
          this.$hloading()
        })
    },
    handleDelete() {
      this.tableRef.removeCheckboxRow()
      const currentViewRecords = this.tableRef.getTableData().visibleData
      this.$emit('updateDetail', currentViewRecords)
    }
  }
}
</script>
