import { i18n } from '@/main.js'

export const statusOptions = [
  { text: i18n.t('草稿'), label: i18n.t('草稿'), value: 0 },
  { text: i18n.t('审批中'), label: i18n.t('审批中'), value: 1 },
  { text: i18n.t('审批通过'), label: i18n.t('审批通过'), value: 2 },
  { text: i18n.t('审批拒绝'), label: i18n.t('审批拒绝'), value: 3 },
  { text: i18n.t('已完成'), label: i18n.t('已完成'), value: 4 },
  { text: i18n.t('部分退回'), label: i18n.t('部分退回'), value: 5 },
  { text: i18n.t('全部退回'), label: i18n.t('全部退回'), value: 6 },
  { text: i18n.t('已关闭'), label: i18n.t('已关闭'), value: 7 }
]

export const oaStatusOptions = [
  { text: i18n.t('未同步'), label: i18n.t('未同步'), value: 0 },
  { text: i18n.t('同步中'), label: i18n.t('同步中'), value: 1 },
  { text: i18n.t('同步成功'), label: i18n.t('同步成功'), value: 2 },
  { text: i18n.t('同步失败'), label: i18n.t('同步失败'), value: 3 }
]

export const businessTypeOptions = [
  { text: i18n.t('户用业务'), label: i18n.t('户用业务'), value: 1 },
  { text: i18n.t('商用业务'), label: i18n.t('商用业务'), value: 2 },
  { text: i18n.t('海外业务'), label: i18n.t('海外业务'), value: 3 }
]

export const dataSourceOptions = [
  { text: i18n.t('手工创建'), label: i18n.t('手工创建'), value: 0 },
  { text: i18n.t('OA'), label: i18n.t('OA'), value: 1 },
  { text: i18n.t('商城'), label: i18n.t('商城'), value: 2 },
  { text: i18n.t('其他'), label: i18n.t('其他'), value: 3 }
]

export const deliveryMethodOptions = [
  { text: i18n.t('自提'), label: i18n.t('自提'), value: 0 },
  { text: i18n.t('快递'), label: i18n.t('快递'), value: 1 },
  { text: i18n.t('厂家直送'), label: i18n.t('厂家直送'), value: 2 },
  { text: i18n.t('速必达配送'), label: i18n.t('速必达配送'), value: 3 }
]

export const purchaseMethodOptions = [
  { text: i18n.t('标准'), label: i18n.t('标准'), value: 0 },
  { text: i18n.t('寄售'), label: i18n.t('寄售'), value: 1 },
  { text: i18n.t('委外加工'), label: i18n.t('委外加工'), value: 2 },
  { text: i18n.t('其他，默认标准'), label: i18n.t('其他，默认标准'), value: 3 }
]

export const isRdcWarehouseOptions = [
  { text: i18n.t('否'), label: i18n.t('否'), value: 0 },
  { text: i18n.t('是'), label: i18n.t('是'), value: 1 }
]

export const maxLongCarOptions = [
  { text: i18n.t('17米5'), label: i18n.t('17米5'), value: 17.5 },
  { text: i18n.t('13米5'), label: i18n.t('13米5'), value: 13.5 },
  { text: i18n.t('9米6'), label: i18n.t('9米6'), value: 9.6 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'applyNo',
    title: i18n.t('采购申请单号'),
    minWidth: 140,
    slots: {
      default: 'codeDefault'
    }
  },
  {
    field: 'status',
    title: i18n.t('申请单状态'),
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    },
    minWidth: 110
  },
  {
    field: 'businessType',
    title: i18n.t('业务类型'),
    formatter: ({ cellValue }) => {
      let item = businessTypeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'title',
    title: i18n.t('标题')
  },
  {
    field: 'companyCode',
    title: i18n.t('公司代码')
  },
  {
    field: 'companyName',
    title: i18n.t('公司名称')
  },
  {
    field: 'buyerOrgCode',
    title: i18n.t('采购组织'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.buyerOrgName : ''
    }
  },
  {
    field: 'applyDept',
    title: i18n.t('申请部门')
  },
  {
    field: 'applyUser',
    title: i18n.t('申请人')
  },
  {
    field: 'dataSource',
    title: i18n.t('来源类型'),
    formatter: ({ cellValue }) => {
      let item = dataSourceOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'currencyCode',
    title: i18n.t('币种'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.currencyName : ''
    }
  },
  {
    field: 'projectName',
    title: i18n.t('项目名称')
  },
  {
    field: 'projectManager',
    title: i18n.t('项目经理')
  },
  {
    field: 'maxLongCar',
    title: i18n.t('可通过最长车型米'),
    formatter: ({ cellValue }) => {
      let item = maxLongCarOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'onloadCarNum',
    title: i18n.t('一日可卸货车数')
  },
  {
    field: 'syncOaStatus',
    title: i18n.t('同步OA状态')
  },
  {
    field: 'syncOaStatusReturn',
    title: i18n.t('同步OA接口返回')
  },
  {
    field: 'remark',
    title: i18n.t('备注')
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人')
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160
  }
]
