<!-- 采购申请管理-详情 -->
<template>
  <div class="postpone-full-height">
    <div class="top-container toggle-container" :class="[!isExpand && 'top-hidden']">
      <div class="top-content">
        <div class="top-info">
          <div class="top-title">
            <span>{{ $t('状态') }}：{{ formData.status | statusFormat }}</span>
            <span>{{ $t('采购申请单号') }}：{{ formData.applyNo || '-' }}</span>
            <span>{{ $t('创建人') }}：{{ formData.createUserName || '-' }}</span>
            <span>{{ $t('创建时间') }}：{{ formData.createTime || '-' }}</span>
            <span>{{ $t('更新人') }}：{{ formData.updateUserName || '-' }}</span>
            <span>{{ $t('更新时间') }}：{{ formData.updateTime || '-' }}</span>
          </div>
          <div class="top-btns">
            <vxe-button
              v-for="(item, index) in detailToolbar"
              v-show="!item.isHidden"
              :key="index"
              :icon="item.icon"
              :status="item.status"
              :disabled="item.disabled"
              size="small"
              @click="handleClickToolBar(item)"
              >{{ item.name }}</vxe-button
            >
          </div>
        </div>
        <div class="top-form">
          <mt-form ref="formRef" :model="formData" :rules="formRules">
            <mt-form-item prop="title" :label="$t('标题')" label-style="top">
              <vxe-input
                v-model="formData.title"
                :placeholder="$t('请输入标题')"
                clearable
                :disabled="!editable"
                maxlength="30"
              />
            </mt-form-item>
            <mt-form-item prop="businessType" :label="$t('业务类型')" label-style="top">
              <vxe-select
                v-model="formData.businessType"
                :options="businessTypeOptions"
                :option-props="{ label: 'label', value: 'value' }"
                :placeholder="$t('请选择')"
                :disabled="!editable"
                clearable
              />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
              <vxe-select
                v-model="formData.companyCode"
                :options="companyOptions"
                :option-props="{ label: 'label', value: 'value' }"
                :placeholder="$t('请选择')"
                :disabled="!editable"
                clearable
                filterable
                @change="companyChange"
              />
            </mt-form-item>
            <mt-form-item prop="buyerOrgCode" :label="$t('采购组织')" label-style="top">
              <vxe-select
                v-model="formData.buyerOrgCode"
                :options="buyerOrgOptions"
                :option-props="{ label: 'label', value: 'value' }"
                :placeholder="formData.companyCode ? $t('请选择') : $t('请先选择公司')"
                :disabled="!editable"
                clearable
                filterable
                @change="buyerOrgChange"
              />
            </mt-form-item>
            <mt-form-item prop="applyUserCode" :label="$t('申请人')" label-style="top">
              <VxeRemoteSearch
                v-if="showApplyUser"
                v-model="formData.applyUserCode"
                :placeholder="$t('请选择')"
                :fields="{ text: 'employeeName', value: 'accountName' }"
                :request-info="{
                  urlPre: 'masterData',
                  url: 'getCurrentTenantEmployees',
                  searchKey: 'fuzzyName',
                  recordsPosition: 'data'
                }"
                @change="applyUserChange"
              />
            </mt-form-item>
            <mt-form-item prop="applyDept" :label="$t('申请部门')" label-style="top">
              <vxe-input
                v-model="formData.applyDept"
                :placeholder="$t('请输入')"
                clearable
                :disabled="!editable"
              />
            </mt-form-item>
            <mt-form-item prop="currencyCode" :label="$t('币种')" label-style="top">
              <vxe-select
                v-model="formData.currencyCode"
                :options="currencyOptions"
                :option-props="{ label: 'label', value: 'value' }"
                :placeholder="$t('请选择')"
                :disabled="!editable"
                clearable
                filterable
                @change="currencyChange"
              />
            </mt-form-item>
            <mt-form-item prop="projectName" :label="$t('项目名称')" label-style="top">
              <vxe-input
                v-model="formData.projectName"
                :placeholder="$t('请输入')"
                clearable
                :disabled="!editable"
              />
            </mt-form-item>
            <mt-form-item prop="projectManager" :label="$t('项目经理')" label-style="top">
              <vxe-input
                v-model="formData.projectManager"
                :placeholder="$t('请输入')"
                clearable
                :disabled="!editable"
              />
            </mt-form-item>
            <mt-form-item prop="maxLongCar" :label="$t('可通行最长车型')" label-style="top">
              <vxe-select
                v-model="formData.maxLongCar"
                :options="maxLongCarOptions"
                :option-props="{ label: 'label', value: 'value' }"
                :placeholder="$t('请选择')"
                :disabled="!editable"
                clearable
              />
            </mt-form-item>
            <mt-form-item prop="onloadCarNum" :label="$t('一日可卸货车数')" label-style="top">
              <vxe-input
                type="number"
                v-model="formData.onloadCarNum"
                :placeholder="$t('请输入')"
                min="0"
                clearable
                :disabled="!editable"
              />
            </mt-form-item>
            <mt-form-item prop="dataSource" :label="$t('来源类型')" label-style="top">
              <vxe-select
                v-model="formData.dataSource"
                :options="dataSourceOptions"
                :option-props="{ label: 'label', value: 'value' }"
                :placeholder="$t('请选择')"
                :disabled="true"
                clearable
              />
            </mt-form-item>
            <mt-form-item prop="sourceNo" :label="$t('来源单号')" label-style="top">
              <vxe-input v-model="formData.sourceNo" :disabled="true" />
            </mt-form-item>
            <mt-form-item prop="remark" :label="$t('备注')" label-style="top" class="full-width">
              <vxe-input
                v-model="formData.remark"
                :placeholder="$t('请输入')"
                clearable
                :disabled="!editable"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </div>
      <div class="top-toggle-btn">
        <div @click="handleToggle"><i class="vxe-icon-arrow-up" /></div>
      </div>
    </div>
    <div class="body-container">
      <mt-tabs
        ref="tabsRef"
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabList"
        :halt-select="false"
        @handleSelectTab="(index) => handleTabChange(index)"
        style="background-color: #fff"
      />
      <keep-alive :include="keepArr">
        <component
          ref="mainContent"
          style="height: calc(100% - 50px)"
          :is="activeComponent"
          :refresh-key="refreshKey"
          :data-info="formData"
          :detail-info="formData"
          @updateDetail="updateDetail"
        />
      </keep-alive>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import VxeRemoteSearch from '@/components/VxeRemoteSearch/index.vue'
import { statusOptions, businessTypeOptions, dataSourceOptions, maxLongCarOptions } from './config'

export default {
  name: 'ProcurementRequestDetail',
  components: {
    VxeRemoteSearch
  },
  data() {
    return {
      isExpand: true,
      formData: {},
      activeTabIndex: 0,
      keepArr: ['ItemTab', 'AttachmentTab'],
      businessTypeOptions,
      companyOptions: [],
      buyerOrgOptions: [],
      currencyOptions: [],
      dataSourceOptions,
      maxLongCarOptions,
      purchaseApplyDetailDtoList: [],
      pvFileRequestList: [],
      showApplyUser: false,
      firstInit: true,
      refreshKey: 0
    }
  },
  computed: {
    pageType() {
      return this.$route.query?.type || 'add'
    },
    editable() {
      return this.pageType === 'add' || [0, 3].includes(this.formData.status)
    },
    formRules() {
      return {
        title: [{ required: true, message: this.$t('请输入标题'), trigger: 'blur' }],
        businessType: [{ required: true, message: this.$t('请选择业务类型'), trigger: 'blur' }],
        companyCode: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        buyerOrgCode: [{ required: true, message: this.$t('请选择采购组织'), trigger: 'blur' }],
        applyDept: [{ required: true, message: this.$t('请输入申请部门'), trigger: 'blur' }],
        applyUserCode: [{ required: true, message: this.$t('请选择申请人'), trigger: 'blur' }],
        currencyCode: [{ required: true, message: this.$t('请选择币种'), trigger: 'blur' }],
        dataSource: [{ required: true, message: this.$t('请选择来源类型'), trigger: 'blur' }]
      }
    },
    detailToolbar() {
      return [
        { code: 'back', name: this.$t('返回') },
        { code: 'save', name: this.$t('保存'), status: 'primary', isHidden: !this.editable },
        { code: 'submit', name: this.$t('提交OA审批'), status: 'primary', isHidden: !this.editable }
      ]
    },
    tabList() {
      return [
        { title: this.$t('物料明细'), compName: 'ItemTab' },
        { title: this.$t('相关附件'), compName: 'AttachmentTab' }
      ]
    },
    activeComponent() {
      const componentMap = {
        0: () => import('./components/itemTab.vue'),
        1: () => import('./components/attachmentTab.vue')
      }
      return componentMap[this.activeTabIndex]
    }
  },
  filters: {
    statusFormat(value) {
      let text = statusOptions.find((v) => v.value === value)?.text || ''
      return text
    }
  },
  created() {
    this.initData()
  },
  methods: {
    async initData() {
      try {
        await this.getCompanyOptions()
        await this.getCurrencyOptions()

        if (this.pageType === 'add') {
          this.initAddForm()
        } else {
          await this.getDetail()
        }
      } catch (error) {
        console.error('初始化数据失败:', error)
        this.$toast({ content: this.$t('初始化数据失败'), type: 'error' })
      }
    },
    initAddForm() {
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      this.formData = {
        companyId: null,
        status: 0,
        applyUserCode: userInfo?.externalCode,
        applyUser: userInfo?.username,
        applyDept: null,
        dataSource: 0,
        buyerOrgCode: null,
        buyerOrgName: null
      }
      this.showApplyUser = true
    },
    async getDetail() {
      try {
        const params = { ids: [this.$route.query?.id] }
        const res = await this.$API.demandManagement.detailProcurementRequestManagementApi(params)
        if (res.code === 200 && res.data?.length) {
          this.showApplyUser = true
          this.formData = res.data[0]
          await this.getBuyerOrgOptions(this.formData.companyId)
        }
      } catch (error) {
        console.error('获取详情失败:', error)
        this.$toast({ content: this.$t('获取详情失败'), type: 'error' })
      }
    },
    applyUserChange(e) {
      if (!this.firstInit || this.pageType === 'add') {
        this.formData.applyUser = e?.employeeName
        this.formData.applyDept = e?.departmentOrgName
      } else {
        this.firstInit = false
      }
    },
    currencyChange(e) {
      const { value } = e
      if (value) {
        const selectedItem = this.currencyOptions.find((item) => item.value === value)
        this.formData.currencyName = selectedItem ? selectedItem.currencyName : null
      } else {
        this.formData.currencyName = null
      }
    },
    getCurrencyOptions() {
      this.$API.masterData.queryAllCurrency().then((res) => {
        if (res.code === 200) {
          this.currencyOptions = res.data.map((item) => {
            return {
              label: item.currencyCode + '-' + item.currencyName,
              value: item.currencyCode,
              ...item
            }
          })
        }
      })
    },
    buyerOrgChange(e) {
      const { value } = e
      if (value) {
        const selectedItem = this.buyerOrgOptions.find((item) => item.value === value)
        this.formData.buyerOrgName = selectedItem ? selectedItem.organizationName : null
      } else {
        this.formData.buyerOrgName = null
      }
    },
    getBuyerOrgOptions(companyId) {
      if (!companyId) {
        this.buyerOrgOptions = []
        return
      }
      this.$API.masterData
        .permissionOrgList({
          orgId: companyId
        })
        .then((res) => {
          if (res.code === 200) {
            this.buyerOrgOptions = res.data.map((item) => {
              return {
                label: item.organizationCode + '-' + item.organizationName,
                value: item.organizationCode,
                ...item
              }
            })
          }
        })
    },
    companyChange(e) {
      const { value } = e
      if (value) {
        const selectedItem = this.companyOptions.find((item) => item.value === value)
        this.formData.companyId = selectedItem ? selectedItem.id : null
        this.formData.companyName = selectedItem ? selectedItem.orgName : null
        this.formData.buyerOrgCode = null
        this.formData.buyerOrgName = null
        this.getBuyerOrgOptions(selectedItem?.id)
      } else {
        this.formData.companyId = null
        this.formData.companyName = null
        this.buyerOrgOptions = []
      }
    },
    getCompanyOptions() {
      this.$API.masterData.permissionCompanyList().then((res) => {
        if (res.code === 200) {
          this.companyOptions = res.data.map((item) => {
            return {
              label: item.orgCode + '-' + item.orgName,
              value: item.orgCode,
              ...item
            }
          })
        }
      })
    },
    updateDetail(data) {
      switch (this.activeTabIndex) {
        case 0:
          this.purchaseApplyDetailDtoList = data
          break
        case 1:
          this.pvFileRequestList = data
          break
        default:
          break
      }
    },
    handleTabChange(index) {
      this.$refs.tabsRef.activeTab = index
      this.activeTabIndex = index
    },
    handleClickToolBar(e) {
      switch (e.code) {
        case 'back':
          this.handleBack()
          break
        case 'save':
          this.handleSave()
          break
        case 'submit':
          this.handleSubmit()
          break
        default:
          break
      }
    },
    handleBack() {
      // this.$router.go(-1)
      this.$router.replace({
        path: '/purchase-pv/demand-management/procurement-request-management',
        query: {
          timeStamp: new Date().getTime()
        }
      })
    },
    async handleSave() {
      let params = this.getParams()
      if (Object.entries(params).length === 0) return
      this.$store.commit('startLoading')
      const api =
        this.pageType === 'add'
          ? this.$API.demandManagement.addProcurementRequestManagementApi
          : this.$API.demandManagement.updateProcurementRequestManagementApi
      const res = await api(params).finally(() => {
        this.$store.commit('endLoading')
      })
      if (res.code === 200) {
        this.$toast({ content: this.$t('保存成功！'), type: 'success' })
        if (this.pageType === 'add') {
          this.$router.replace({
            name: 'procurement-request-management-detail',
            query: {
              type: 'edit',
              id: res.data?.length ? res.data[0] : null
            }
          })
        } else {
          // 编辑保存完获取最新数据
          this.refreshKey++
        }
      }
    },
    getParams() {
      let params = {}
      this.$refs.formRef.validate(async (valid) => {
        if (!valid) {
          //展开头部表单数据，显示报错信息
          this.isExpand = true
          return
        }
        if (this.pageType === 'add') {
          let purchaseApplyDetailAddDtoList = this.purchaseApplyDetailDtoList.map((item) => {
            return {
              ...item,
              requiredDeliveryDate: item.requiredDeliveryDate
                ? dayjs(item.requiredDeliveryDate).valueOf()
                : null,
              id: item.id.includes('row_') ? null : item.id,
              itemCode: item?.itemCode || '',
              itemName: item?.itemName || '',
              currencyCode: this.formData?.currencyCode || '',
              currencyName: this.formData?.currencyName || ''
            }
          })
          let pvFileRequestList = this.pvFileRequestList
          params = {
            purchaseApplyAddDtoList: [
              {
                ...this.formData,
                purchaseApplyDetailAddDtoList,
                pvFileRequestList
              }
            ]
          }
        } else {
          let purchaseApplyDetailUpdateDtos = this.purchaseApplyDetailDtoList.map((item) => {
            return {
              ...item,
              requiredDeliveryDate: item.requiredDeliveryDate
                ? dayjs(item.requiredDeliveryDate).valueOf()
                : null,
              id: item.id.includes('row_') ? null : item.id,
              itemCode: item?.itemCode || '',
              itemName: item?.itemName || '',
              currencyCode: this.formData?.currencyCode || '',
              currencyName: this.formData?.currencyName || ''
            }
          })
          let pvFileRequestList = this.pvFileRequestList
          params = {
            purchaseApplyUpdateDtoList: [
              {
                ...this.formData,
                purchaseApplyDetailUpdateDtos,
                pvFileRequestList,
                maxLongCar: this.formData?.maxLongCar || '' // 后端要求为空时传空字符串
              }
            ]
          }
        }
      })
      return params
    },
    async handleSubmit() {
      await this.handleSave()

      if (!this.formData?.id) {
        return
      }
      let params = {
        ids: [this.formData?.id]
      }
      this.$store.commit('startLoading')
      const api = this.$API.demandManagement.submitOAProcurementRequestManagementApi
      const res = await api(params).finally(() => {
        this.$store.commit('endLoading')
      })
      if (res.code === 200) {
        this.$toast({ content: this.$t('提交成功！'), type: 'success' })
        this.handleBack()
      }
    },
    // 展开/收缩
    handleToggle() {
      this.isExpand = !this.isExpand
    }
  }
}
</script>

<style scoped lang="scss">
.postpone-full-height {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 8px 8px 0;
  background: #fff;
}
.body-container {
  height: calc(100% - 80px);
}
.top-container {
  flex: 1;
  width: 100%;
  .top-content {
    padding: 8px 8px 0 8px;
    background-color: #f9f9f9;
    border-radius: 8px;
    .top-info {
      display: flex;
      justify-content: space-between;
      .title {
        font-size: 20px;
        font-weight: 700;
        color: #292929;
        span {
          margin-right: 15px;
        }
        .sub-title {
          font-size: 15px;
          color: #777;
        }
      }
      .top-title {
        display: flex;
        padding-top: 10px;
        span {
          margin-right: 35px;
        }
      }
      .top-btns {
        text-align: right;
        margin-bottom: 10px;
      }
    }
    .top-form {
      .mt-form {
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
        justify-content: space-between;
        grid-gap: 5px;
      }
      .vxe-input,
      .vxe-select,
      .vxe-pulldown {
        width: 100%;
        height: 34px;
        line-height: 34px;
      }
      /deep/.vxe-pulldown--panel {
        min-width: unset !important;
        width: 100%;
      }
      .full-width {
        grid-column-start: 1;
        grid-column-end: 6;
      }
    }
  }
  .top-toggle-btn {
    width: 100%;
    div {
      width: 80px;
      height: 15px;
      line-height: 15px;
      margin: auto;
      text-align: center;
      background-color: #4a556b;
      color: #fff;
      font-weight: bold;
      border-radius: 0 0 6px 6px;
    }
  }
}

.top-hidden {
  .top-form {
    display: none;
  }
  .top-toggle-btn {
    div {
      transform: rotate(180deg);
      border-radius: 6px 6px 0 0;
    }
  }
}

.my-dropdown2 {
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 4px 0;
  max-height: 200px;
  border-radius: 4px;
  border: 1px solid #dadce0;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}
.list-item2 {
  line-height: 30px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 0 4px;

  .isSelected {
    font-weight: 700;
    color: #409eff;
  }
}
.list-item2:hover {
  background-color: #f5f7fa;
}
.empty-tip {
  width: 100%;
  line-height: 30px;
  padding: 0 4px;
  text-align: center;
  color: #c0c4cc;
}

::v-deep {
  .mt-form-item {
    margin-bottom: 15px;
    .label {
      margin: 0 10px 5px 0;
    }
  }
  .mt-tabs-container {
    background-color: #fff;
  }

  .mt-tabs-container ul.tab-container li.tab-item2.active {
    border-bottom: 2px solid #409eff;
  }
  .mt-tabs-container ul.tab-container li.tab-item2.active,
  .mt-tabs-container ul.tab-container li.tab-item2:hover {
    color: #31374e;
  }

  .vxe-textarea--inner {
    min-height: 34px;
    height: 100%;
  }
}
</style>
