import { i18n } from '@/main.js'
import dayjs from 'dayjs'

export const statusOptions = [
  { text: i18n.t('新建'), label: i18n.t('新建'), value: 0 },
  { text: i18n.t('待寻源'), label: i18n.t('待寻源'), value: 1 },
  { text: i18n.t('已建询价'), label: i18n.t('已建询价'), value: 2 },
  { text: i18n.t('已建竞价'), label: i18n.t('已建竞价'), value: 3 },
  { text: i18n.t('已建招标'), label: i18n.t('已建招标'), value: 4 },
  { text: i18n.t('询价完成'), label: i18n.t('询价完成'), value: 5 },
  { text: i18n.t('竞价完成'), label: i18n.t('竞价完成'), value: 6 },
  { text: i18n.t('招标完成'), label: i18n.t('招标完成'), value: 7 },
  { text: i18n.t('已退回'), label: i18n.t('已退回'), value: 8 },
  { text: i18n.t('部分转订单'), label: i18n.t('部分转订单'), value: 9 },
  { text: i18n.t('全部转订单'), label: i18n.t('全部转订单'), value: 10 },
  { text: i18n.t('已关闭'), label: i18n.t('已关闭'), value: 11 }
]

export const dataSourceOptions = [
  { text: i18n.t('手工创建'), label: i18n.t('手工创建'), value: 0 },
  { text: i18n.t('OA'), label: i18n.t('OA'), value: 1 },
  { text: i18n.t('商城'), label: i18n.t('商城'), value: 2 },
  { text: i18n.t('其他'), label: i18n.t('其他'), value: 3 }
]

export const businessTypeOptions = [
  { text: i18n.t('户用业务'), label: i18n.t('户用业务'), value: 1 },
  { text: i18n.t('商用业务'), label: i18n.t('商用业务'), value: 2 },
  { text: i18n.t('海外业务'), label: i18n.t('海外业务'), value: 3 }
]

export const deliveryMethodOptions = [
  { text: i18n.t('自提'), label: i18n.t('自提'), value: 0 },
  { text: i18n.t('快递'), label: i18n.t('快递'), value: 1 },
  { text: i18n.t('厂家直送'), label: i18n.t('厂家直送'), value: 2 },
  { text: i18n.t('速必达配送'), label: i18n.t('速必达配送'), value: 3 }
]

export const purchaseMethodOptions = [
  { text: i18n.t('标准'), label: i18n.t('标准'), value: 0 },
  { text: i18n.t('寄售'), label: i18n.t('寄售'), value: 1 },
  { text: i18n.t('委外加工'), label: i18n.t('委外加工'), value: 2 },
  { text: i18n.t('其他，默认标准'), label: i18n.t('其他，默认标准'), value: 3 }
]

export const isRdcWarehouseOptions = [
  { text: i18n.t('否'), label: i18n.t('否'), value: 0 },
  { text: i18n.t('是'), label: i18n.t('是'), value: 1 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'applyNo',
    title: i18n.t('采购申请单号')
  },
  {
    field: 'lineNo',
    title: i18n.t('采购申请单行号')
  },
  {
    field: 'lineStatus',
    title: i18n.t('申请单行状态'),
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'dataSource',
    title: i18n.t('来源类型'),
    formatter: ({ cellValue }) => {
      let item = dataSourceOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'businessType',
    title: i18n.t('业务类型'),
    formatter: ({ cellValue }) => {
      let item = businessTypeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'companyCode',
    title: i18n.t('公司代码')
  },
  {
    field: 'companyName',
    title: i18n.t('公司名称')
  },
  {
    field: 'buyerOrgCode',
    title: i18n.t('采购组织'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.buyerOrgName : ''
    }
  },
  {
    field: 'applyDept',
    title: i18n.t('申请部门')
  },
  {
    field: 'applyUser',
    title: i18n.t('申请人')
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂代码')
  },
  {
    field: 'siteName',
    title: i18n.t('工厂名称')
  },
  {
    field: 'brand',
    title: i18n.t('品牌')
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称')
  },
  {
    field: 'tempMaterialCode',
    title: i18n.t('临时物料编码')
  },
  // {
  //   field: 'specification',
  //   title: i18n.t('规格型号')
  // },
  {
    field: 'unit',
    title: i18n.t('单位')
  },
  {
    field: 'requiredQuantity',
    title: i18n.t('需求数量')
  },
  {
    field: 'orderQty',
    title: i18n.t('已转订单数量')
  },
  {
    field: 'requiredDeliveryDateStr',
    title: i18n.t('要求交期')
  },
  {
    field: 'powerKw',
    title: i18n.t('功率(KW)')
  },
  {
    field: 'capacity',
    title: i18n.t('容量')
  },
  {
    field: 'specialRequirements',
    title: i18n.t('特殊要求')
  },
  {
    field: 'deliveryMethod',
    title: i18n.t('配送方式'),
    formatter: ({ cellValue }) => {
      let item = deliveryMethodOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'deliveryPriority',
    title: i18n.t('发货优先级')
  },
  {
    field: 'categoryCode',
    title: i18n.t('品类'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.categoryName : ''
    }
  },
  {
    field: 'bigCategCode',
    title: i18n.t('大类'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.bigCategName : ''
    }
  },
  {
    field: 'medCategCode',
    title: i18n.t('中类'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.medCategName : ''
    }
  },
  {
    field: 'smallCategCode',
    title: i18n.t('小类'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.smallCategName : ''
    }
  },
  {
    field: 'ext24',
    title: i18n.t('采购组')
  },
  {
    field: 'ext25',
    title: i18n.t('物料组')
  },
  {
    field: 'ext26',
    title: i18n.t('最早交付时间')
  },
  {
    field: 'ext27',
    title: i18n.t('最晚交付时间')
  },
  {
    field: 'projectName',
    title: i18n.t('项目名称')
  },
  {
    field: 'projectManager',
    title: i18n.t('项目经理')
  },
  {
    field: 'purchaseMethod',
    title: i18n.t('采购方式'),
    formatter: ({ cellValue }) => {
      let item = purchaseMethodOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'title',
    title: i18n.t('标题')
  },
  {
    field: 'currencyCode',
    title: i18n.t('币种'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.currencyName : ''
    }
  },
  {
    field: 'suggestedSupplierCode',
    title: i18n.t('建议供应商'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.suggestedSupplierName : ''
    }
  },
  {
    field: 'isRdcWarehouse',
    title: i18n.t('是否入RCD仓'),
    formatter: ({ cellValue }) => {
      let item = isRdcWarehouseOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'warehouse',
    title: i18n.t('入库仓库')
  },
  {
    field: 'receiverName',
    title: i18n.t('收货人')
  },
  {
    field: 'receiverPhone',
    title: i18n.t('收货联系方式')
  },
  {
    field: 'deliveryAddress',
    title: i18n.t('收货地址')
  },
  {
    field: 'remarks',
    title: i18n.t('备注')
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人')
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      let time = ''
      if (cellValue && cellValue != '0') {
        time = dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
      }
      return time
    }
  }
]
