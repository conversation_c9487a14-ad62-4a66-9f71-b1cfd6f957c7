<template>
  <div>
    <sc-table
      ref="scTableRef"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      keep-source
      :sortable="false"
      :is-show-right-btn="true"
      :is-show-refresh-bth="false"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import ScTable from '@/components/ScTable/src/index'
import { getHeadersFileName, download } from '@/utils/utils'
import { businessTypeOptions } from './config'
export default {
  components: { ScTable },
  data() {
    return {
      toolbar: [
        { code: 'create', name: this.$t('生成订单'), status: 'info', loading: false },
        // { code: 'export', name: this.$t('导出'), status: 'info', loading: false },
        { code: 'back', name: this.$t('返回'), status: 'info', loading: false }
      ],
      loading: false,
      tableData: [],
      editRules: {
        toOrderQty: [{ required: true, message: this.$t('必填') }],
        deliveryPlace: [{ required: true, message: this.$t('必填') }]
      },

      businessTypeOptions
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'applyNo',
          title: this.$t('采购申请单号')
        },
        {
          field: 'lineNo',
          title: this.$t('采购申请单行号')
        },
        {
          field: 'itemCode',
          title: this.$t('物料编码')
        },
        {
          field: 'itemName',
          title: this.$t('物料名称')
        },
        {
          field: 'preOrderQty',
          title: this.$t('可转订单数量')
        },
        {
          field: 'toOrderQty',
          title: this.$t('转订单数量'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.toOrderQty}
                  placeholder={this.$t('请输入')}
                  min='1'
                  max={row.preOrderQty}
                  transfer
                  clearable
                  onChange={(e) => {
                    row.quotaPercent = row.toOrderQty / Number(row.preOrderQty)
                    row.taxedTotalPrice = row.toOrderQty * Number(row.taxedUnitPrice)
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'quotaPercent',
          title: this.$t('配额比例'),
          formatter: ({ cellValue }) => {
            return cellValue ? (cellValue * 100).toFixed(2) + '%' : ''
          }
        },
        {
          field: 'deliveryPlace',
          title: this.$t('直送地'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.deliveryPlace}
                  options={row.priceRecordList}
                  option-props={{ label: 'deliveryPlace', value: 'deliveryPlace' }}
                  placeholder={this.$t('请选择')}
                  transfer
                  clearable
                  onChange={(e) => {
                    let selectedItem = row.priceRecordList.find((v) => v.deliveryPlace === e.value)
                    row.taxRate = selectedItem.taxRate
                    row.taxedUnitPrice = selectedItem?.taxedUnitPrice || null
                    row.untaxedUnitPrice = selectedItem?.untaxedUnitPrice || null
                    row.validStartTime = selectedItem?.validStartTime || null
                    row.validEndTime = selectedItem?.validEndTime || null
                    row.unitCode = selectedItem?.unitCode || null
                    row.unit = selectedItem?.unitName || null
                    row.companyCode = selectedItem?.companyCode || null
                    row.companyName = selectedItem?.companyName || null
                    row.paymentCondition = selectedItem?.paymentCondition || null
                    row.minPurchaseQuantity = selectedItem.minPurchaseQuantity
                    row.minPackageQuantity = selectedItem.minPackageQuantity
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'taxCode',
          title: this.$t('税码'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.taxName : ''
          }
        },
        {
          field: 'taxRate',
          title: this.$t('税率')
        },
        {
          field: 'taxedUnitPrice',
          title: this.$t('含税单价')
        },
        {
          field: 'taxedTotalPrice',
          title: this.$t('含税总价')
        },
        // {
        //   field: 'untaxedUnitPrice',
        //   title: this.$t('未税单价')
        // },
        {
          field: 'supplierCode',
          title: this.$t('供应商编码')
        },
        {
          field: 'supplierName',
          title: this.$t('供应商名称')
        },
        {
          field: 'requiredDeliveryDate',
          title: this.$t('要求交期'),
          formatter: ({ cellValue }) => {
            return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD')
          }
        },
        {
          field: 'validStartTime',
          title: this.$t('价格开始时间')
        },
        {
          field: 'validEndTime',
          title: this.$t('价格结束时间')
        },
        {
          field: 'siteCode',
          title: this.$t('工厂代码')
        },
        {
          field: 'siteName',
          title: this.$t('工厂名称')
        },
        {
          field: 'categoryCode',
          title: this.$t('品类'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.categoryName : ''
          }
        },
        {
          field: 'businessType',
          title: this.$t('业务类型'),
          formatter: ({ cellValue }) => {
            let item = this.businessTypeOptions.find((item) => item.value === cellValue)
            return item ? item.text : ''
          }
        },
        {
          field: 'unitCode',
          title: this.$t('基本单位'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.unit : ''
          }
        },
        {
          field: 'companyCode',
          title: this.$t('公司代码')
        },
        {
          field: 'companyName',
          title: this.$t('公司名称')
        },
        {
          field: 'buyerOrgCode',
          title: this.$t('采购组织'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.buyerOrgName : ''
          }
        },
        {
          field: 'currencyCode',
          title: this.$t('币种'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.currencyName : ''
          }
        },
        {
          field: 'projectName',
          title: this.$t('项目名称')
        },
        {
          field: 'projectManager',
          title: this.$t('项目经理')
        },
        {
          field: 'warehouseCode',
          title: this.$t('入库仓库'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.warehouse : ''
          }
        },
        {
          field: 'receiverName',
          title: this.$t('收货人')
        },
        {
          field: 'receiverPhone',
          title: this.$t('联系方式')
        },
        {
          field: 'deliveryAddress',
          title: this.$t('配送地址')
        },
        {
          field: 'paymentCondition',
          title: this.$t('付款条件')
        },
        {
          field: 'minPurchaseQuantity',
          title: this.$t('最小订单量')
        },
        {
          field: 'minPackageQuantity',
          title: this.$t('最小包装量')
        }
      ]
    },
    editConfig() {
      return {
        enabled: true,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    getTableData() {
      let data = JSON.parse(sessionStorage.getItem('demandPoolData'))
      this.tableData = data.map((item) => {
        if (item?.priceRecordList.length === 1) {
          item.deliveryPlace = item.priceRecordList[0].deliveryPlace
          item.taxRate = item.priceRecordList[0].taxRate
          item.taxCode = item.priceRecordList[0]?.taxCode || null
          item.taxName = item.priceRecordList[0]?.taxName || null
          item.taxedUnitPrice = item.priceRecordList[0]?.taxedUnitPrice || null
          item.untaxedUnitPrice = item.priceRecordList[0]?.untaxedUnitPrice || null
          item.validStartTime = item.priceRecordList[0]?.validStartTime || null
          item.validEndTime = item.priceRecordList[0]?.validEndTime || null
          item.unitCode = item.priceRecordList[0]?.unitCode || null
          item.unit = item.priceRecordList[0]?.unitName || null
          item.companyCode = item.priceRecordList[0]?.companyCode || null
          item.companyName = item.priceRecordList[0]?.companyName || null
          item.paymentCondition = item.priceRecordList[0]?.paymentCondition || null
          item.minPurchaseQuantity = item.priceRecordList[0].minPurchaseQuantity
          item.minPackageQuantity = item.priceRecordList[0].minPackageQuantity
        }
        return {
          ...item
        }
      })
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['create']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.code === 'create' && selectedRecords.some((v) => !v.deliveryPlace)) {
        this.$toast({ content: this.$t('请选择直送地'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'create':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('是否确定生成订单？')
            },
            success: () => {
              this.handleCreate(selectedRecords)
            }
          })
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        case 'back':
          this.handleBack()
          break
        default:
          break
      }
    },
    handleCreate(selectedRecords) {
      this.$API.demandManagement.createOrderDemandPoolApi(selectedRecords).then((res) => {
        if (res.code === 200) {
          if (res.data) {
            let data = JSON.parse(res.data)
            let total = data.length
            let str = data.join(',')
            this.$toast({
              content: this.$t(`转订单成功，已转${total}单，单号为${str}`),
              type: 'success'
            })
          }
          this.handleBack()
        }
      })
    },
    handleExport(e) {
      let params = {}
      this.$API.demandManagement
        .exportApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    },
    handleBack() {
      this.$router.push({
        path: '/purchase-pv/demand-management/demand-pool',
        query: {
          timeStamp: new Date().getTime()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
