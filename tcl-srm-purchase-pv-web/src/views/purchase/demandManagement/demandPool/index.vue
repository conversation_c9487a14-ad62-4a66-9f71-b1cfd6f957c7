<!-- 需求池 -->
<template>
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item prop="applyNo" :label="$t('采购申请单号')" label-style="top">
          <mt-input
            v-model="searchFormModel.applyNo"
            :show-clear-button="true"
            :placeholder="$t('请输入采购申请单号')"
          />
        </mt-form-item>
        <mt-form-item prop="lineNo" :label="$t('采购申请单行号')" label-style="top">
          <mt-input
            v-model="searchFormModel.lineNo"
            :show-clear-button="true"
            :placeholder="$t('请输入采购申请单行号')"
          />
        </mt-form-item>
        <mt-form-item prop="lineStatusList" :label="$t('申请单行状态')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.lineStatusList"
            :data-source="statusOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="dataSourceList" :label="$t('来源类型')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.dataSourceList"
            :data-source="dataSourceOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="businessTypeList" :label="$t('业务类型')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.businessTypeList"
            :data-source="businessTypeOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.companyCode"
            url="/masterDataManagement/tenant/organization/specified-level-paged-query"
            :placeholder="$t('请选择')"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            :params="{
              organizationLevelCodes: ['ORG01', 'ORG02']
            }"
          />
        </mt-form-item>
        <mt-form-item prop="buyerOrgCode" :label="$t('采购组织')" label-style="top">
          <mt-input
            v-model="searchFormModel.buyerOrgCode"
            :show-clear-button="true"
            :placeholder="$t('请输入采购组织')"
          />
        </mt-form-item>
        <mt-form-item prop="applyDept" :label="$t('申请部门')" label-style="top">
          <mt-input
            v-model="searchFormModel.applyDept"
            :show-clear-button="true"
            :placeholder="$t('请输入申请部门')"
          />
        </mt-form-item>
        <mt-form-item prop="applyUser" :label="$t('申请人')" label-style="top">
          <mt-input
            v-model="searchFormModel.applyUser"
            :show-clear-button="true"
            :placeholder="$t('请输入申请人')"
          />
        </mt-form-item>
        <mt-form-item prop="siteCodeList" :label="$t('工厂')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.siteCodeList"
            :url="$API.masterData.getSiteListUrl"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          />
        </mt-form-item>
        <mt-form-item prop="brand" :label="$t('品牌')" label-style="top">
          <mt-input
            v-model="searchFormModel.brand"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.itemCode"
            :url="$API.masterData.getItemListUrlPage"
            :placeholder="$t('请选择')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :search-fields="['itemName', 'itemCode']"
          />
        </mt-form-item>
        <mt-form-item prop="tempMaterialCode" :label="$t('临时物料编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.tempMaterialCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="specification" :label="$t('规格型号')" label-style="top">
          <mt-input
            v-model="searchFormModel.specification"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="unit" :label="$t('单位')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.unit"
            :url="$API.masterData.pagedQueryUnitUrl"
            :placeholder="$t('请选择')"
            :fields="{ text: 'unitName', value: 'unitCode' }"
            :search-fields="['unitName', 'unitCode']"
          />
        </mt-form-item>
        <mt-form-item prop="requiredDeliveryDate" :label="$t('要求交期')">
          <mt-date-range-picker
            v-model="searchFormModel.requiredDeliveryDate"
            @change="(e) => dateChange(e, 'requiredDeliveryDate')"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="deliveryMethodList" :label="$t('配送方式')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.deliveryMethodList"
            :data-source="deliveryMethodOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="deliveryPriority" :label="$t('发货优先级')" label-style="top">
          <mt-input
            v-model="searchFormModel.deliveryPriority"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="categoryCode" :label="$t('品类')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.categoryCode"
            url="/masterDataManagement/tenant/category/paged-query"
            :placeholder="$t('请选择')"
            :fields="{ text: 'categoryName', value: 'categoryCode' }"
            :search-fields="['categoryName', 'categoryCode']"
          />
        </mt-form-item>
        <mt-form-item prop="bigCategCode" :label="$t('大类')" label-style="top">
          <mt-input
            v-model="searchFormModel.bigCategCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="medCategCode" :label="$t('中类')" label-style="top">
          <mt-input
            v-model="searchFormModel.medCategCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="smallCategCode" :label="$t('小类')" label-style="top">
          <mt-input
            v-model="searchFormModel.smallCategCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="purchaseMethodList" :label="$t('采购方式')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.purchaseMethodList"
            :data-source="purchaseMethodOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="currencyCode" :label="$t('币种编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.currencyCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="suggestedSupplierCode" :label="$t('建议供应商编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.suggestedSupplierCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="isRdcWarehouse" :label="$t('是否入RDC仓')" label-style="top">
          <mt-select
            v-model="searchFormModel.isRdcWarehouse"
            :data-source="isRdcWarehouseOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="warehouse" :label="$t('入库仓库')" label-style="top">
          <mt-input
            v-model="searchFormModel.warehouse"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="receiverName" :label="$t('收货人')" label-style="top">
          <mt-input
            v-model="searchFormModel.receiverName"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="receiverPhone" :label="$t('收货联系方式')" label-style="top">
          <mt-input
            v-model="searchFormModel.receiverPhone"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="deliveryAddress" :label="$t('收货地址')" label-style="top">
          <mt-input
            v-model="searchFormModel.deliveryAddress"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')" label-style="top">
          <mt-input
            v-model="searchFormModel.remark"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('创建人')" prop="createUserName">
          <mt-input
            v-model="searchFormModel.createUserName"
            :placeholder="$t('支持模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            @change="(e) => dateChange(e, 'createTime')"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('更新人')" prop="updateUserName">
          <mt-input
            v-model="searchFormModel.updateUserName"
            :placeholder="$t('支持模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="updateTime" :label="$t('更新时间')">
          <mt-date-range-picker
            v-model="searchFormModel.updateTime"
            @change="(e) => dateChange(e, 'updateTime')"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="scTableRef"
      grid-id="6dbb4b6c-f1f2-4cd2-8e8d-cd3e54d78a60"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          v-permission="item.permission"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import {
  columnData,
  statusOptions,
  dataSourceOptions,
  businessTypeOptions,
  deliveryMethodOptions,
  purchaseMethodOptions,
  isRdcWarehouseOptions
} from './config'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  components: {
    CollapseSearch,
    ScTable,
    RemoteAutocomplete
  },
  data() {
    const startDate = dayjs().subtract(1, 'month').startOf('month')
    return {
      searchFormModel: {
        createTime: [new Date(startDate), new Date()],
        createTimeS: this.getUnix(dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00')),
        createTimeE: this.getUnix(dayjs(new Date()).format('YYYY-MM-DD 23:59:59'))
      },
      searchFormRules: {},
      toolbar: [
        {
          code: 'transferInquiry',
          name: this.$t('转询价'),
          status: 'info',
          loading: false,
          permission: ['O_02_1846']
        },
        {
          code: 'transferBidding',
          name: this.$t('转招标'),
          status: 'info',
          loading: false,
          permission: ['O_02_1847']
        },
        {
          code: 'transferOrder',
          name: this.$t('转订单'),
          status: 'info',
          loading: false,
          permission: ['O_02_1848']
        },
        {
          code: 'batchReturn',
          name: this.$t('批量退回'),
          status: 'info',
          loading: false,
          permission: ['O_02_1849']
        },
        // {
        //   code: 'maintainItemCode',
        //   name: this.$t('维护物料编码'),
        //   status: 'info',
        //   loading: false
        // },
        {
          code: 'export',
          name: this.$t('导出'),
          status: 'info',
          loading: false,
          permission: ['O_02_1850']
        }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      statusOptions,
      dataSourceOptions,
      businessTypeOptions,
      deliveryMethodOptions,
      purchaseMethodOptions,
      isRdcWarehouseOptions
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    dateChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'S'] = this.getUnix(dayjs(e.startDate))
        this.searchFormModel[field + 'E'] = this.getUnix(dayjs(e.endDate))
      } else {
        this.searchFormModel[field + 'S'] = null
        this.searchFormModel[field + 'E'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      const startDate = dayjs().subtract(1, 'month').startOf('month')
      this.searchFormModel.createTime = [new Date(startDate), new Date()]
      this.searchFormModel.createTimeS = this.getUnix(
        dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00')
      )
      this.searchFormModel.createTimeE = this.getUnix(
        dayjs(new Date()).format('YYYY-MM-DD 23:59:59')
      )
      this.handleSearch()
    },
    handleSearch() {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          this.currentPage = 1
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.demandManagement
        .pageDemandPoolApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['transferInquiry', 'transferBidding', 'transferOrder', 'batchReturn']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (
        selectedRecords.some((v) => v.lineStatus !== 1) &&
        ['transferInquiry', 'transferBidding'].includes(e.code)
      ) {
        this.$toast({ content: this.$t('只能选择【待寻源】状态的数据进行操作'), type: 'warning' })
        return
      }
      if (
        selectedRecords.some((v) => ![1, 2, 3, 4, 5, 6, 7, 9].includes(v.lineStatus)) &&
        ['transferOrder'].includes(e.code)
      ) {
        this.$toast({
          content: this.$t(
            '请选择【待寻源】【已建询价】【已建竞价】【已建招标】【询价完成】【竞价完成】【招标完成】【部分转订单】状态的数据进行操作'
          ),
          type: 'warning'
        })
        return
      }
      switch (e.code) {
        case 'transferInquiry':
          this.handleCreate(selectedRecords, 'rfq')
          break
        case 'transferBidding':
          this.handleCreate(selectedRecords, 'invite_bids')
          break
        case 'transferOrder':
          this.handleTransferOrder(selectedRecords)
          break
        case 'maintainItemCode':
          this.$toast({ content: this.$t('功能开发中'), type: 'warning' })
          break
        case 'batchReturn':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认退回？')
            },
            success: () => {
              this.handleReturn(selectedRecords)
            }
          })
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleCreate(selectedRecords, source) {
      let ids = selectedRecords.map((v) => v.id)
      let _companyCodes = new Set(selectedRecords.map((v) => v.companyCode))
      let _buyerOrgCodes = new Set(selectedRecords.map((v) => v.buyerOrgCode))
      let _siteCodes = new Set(selectedRecords.map((v) => v.siteCode))
      let _applyDepts = new Set(selectedRecords.map((v) => v.applyDept))
      let _companyCode = selectedRecords[0].companyCode
      let _buyerOrgCode = selectedRecords[0].buyerOrgCode
      let _siteCode = selectedRecords[0].siteCode
      if (_companyCodes.size > 1) {
        this.$toast({
          content: this.$t('请选择同一公司'),
          type: 'warning'
        })
        return
      }
      if (_buyerOrgCodes.size > 1) {
        this.$toast({
          content: this.$t('请选择同一采购组织'),
          type: 'warning'
        })
        return
      }
      if (_siteCodes.size > 1) {
        this.$toast({
          content: this.$t('请选择同一工厂'),
          type: 'warning'
        })
        return
      }
      if (_applyDepts.size > 1) {
        this.$toast({
          content: this.$t('请选择同一部门'),
          type: 'warning'
        })
        return
      }
      this.$router.push(
        `/sourcing/bid-hall/create?source=${source}&key=${this.$utils.randomString()}&pageType=gf&ids=${JSON.stringify(
          ids
        )}&companyCode=${_companyCode}&purOrgCode=${_buyerOrgCode}&siteCode=${_siteCode}`
      )
    },
    handleTransferOrder(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      this.$API.demandManagement.toOrderDemandPoolApi({ ids }).then((res) => {
        if (res.code === 200) {
          sessionStorage.setItem('demandPoolData', JSON.stringify(res.data))
          this.$router.push({
            path: '/purchase-pv/demand-management/demand-pool-create',
            query: {
              timeStamp: new Date().getTime()
            }
          })
        }
      })
    },
    handleReturn(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      this.$API.demandManagement.returnDemandPoolApi({ ids }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleExport(e) {
      const dynamicHeaderMap = {}
      columnData.forEach((item) => {
        if (item.field) {
          if (
            [
              'lineStatus',
              'businessType',
              'dataSource',
              'deliveryMethod',
              'purchaseMethod',
              'isRdcWarehouse'
            ].includes(item.field)
          ) {
            dynamicHeaderMap[item.field + 'Name'] = item.title
          } else if (item.field === 'categoryCode') {
            dynamicHeaderMap[item.field] = item.title
            dynamicHeaderMap['categoryName'] = this.$t('品类名称')
          } else {
            dynamicHeaderMap[item.field] = item.title
          }
        }
      })
      const params = {
        dynamicHeaderMap,
        purchaseDemandPoolQueryReq: {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        }
      }
      this.$API.demandManagement
        .exportDemandPoolApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
