import { i18n } from '@/main'
import { formatTime } from '@/utils/utils'
// 是否
const whetherMap = {
  0: i18n.t('否'),
  1: i18n.t('是')
}
// 阶梯类型
const stepQuoteTypeMap = {
  '-1': i18n.t('无梯度'),
  0: i18n.t('数量累计阶梯'),
  3: i18n.t('数量逐层阶梯')
}
// 报价属性
const quoteAttrMap = {
  mailing_price: i18n.t('寄售价'),
  standard_price: i18n.t('标准价'),
  outsource: i18n.t('委外价')
}
// 报价生效方式
const priceEffectiveMethodMap = {
  in_warehouse: i18n.t('按照入库'),
  out_warehouse: i18n.t('按出库'),
  order_date: i18n.t('按订单日期')
}

//详情 - 物料明细 - 列表字段
export const itemColumns = [
  { option: 'checkboxSelection', width: 55 },
  {
    field: 'materialCode',
    headerName: i18n.t('物料编码'),
    width: 155
  },
  {
    field: 'materialName',
    headerName: i18n.t('物料描述')
  },
  // {
  //   field: 'supplierCode',
  //   headerName: i18n.t('供应商编码'),
  //   width: 102
  // },
  {
    field: 'supplierName',
    headerName: i18n.t('供应商名称'),
    width: 170,
    valueFormatter: (params) => {
      return params.data.supplierCode + '-' + params.data.supplierName
    }
  },
  {
    field: 'stepQuote',
    headerName: i18n.t('是否阶梯报价'),
    width: 118,
    valueFormatter: (params) => {
      return whetherMap[params.value]
    }
  },
  {
    field: 'stageType',
    headerName: i18n.t('阶梯类型'),
    width: 110,
    valueFormatter: (params) => {
      return stepQuoteTypeMap[params.value]
    }
  },
  {
    field: 'stepValue',
    headerName: i18n.t('阶梯数量'),
    width: 90
  },
  {
    field: 'unitPriceUntaxed',
    headerName: i18n.t('单价（未税）'),
    width: 115
  },
  {
    field: 'unitPriceTaxed',
    headerName: i18n.t('单价（含税）'),
    width: 115
  },
  {
    field: 'historyUntaxedUnitPrice',
    headerName: i18n.t('历史价格'),
    width: 90
  },
  {
    field: 'declinePercent',
    headerName: i18n.t('降幅%'),
    width: 90
  },
  {
    field: 'priceUnitName',
    headerName: i18n.t('价格单位'),
    width: 90
  },
  {
    field: 'directDeliverAddr',
    headerName: i18n.t('直送地'),
    width: 76
  },

  {
    field: 'currencyName',
    headerName: i18n.t('币种名称'),
    width: 170,
    valueFormatter: (params) => {
      return (params.data.currencyCode || '') + '-' + params.data.currencyName
    }
  },
  {
    field: 'taxRateName',
    headerName: i18n.t('税率名称'),
    width: 170,
    valueFormatter: (params) => {
      return params.data.taxRateCode + '-' + params.data.taxRateName
    }
  },
  {
    field: 'taxRateValue',
    headerName: i18n.t('税率值'),
    width: 90
  },
  {
    field: 'quoteAttr',
    headerName: i18n.t('报价属性'),
    width: 90,
    valueFormatter: (params) => {
      return quoteAttrMap[params.value]
    }
  },
  {
    field: 'priceEffectiveMethod',
    headerName: i18n.t('报价生效方式'),
    width: 115,
    valueFormatter: (params) => {
      return priceEffectiveMethodMap[params.value]
    }
  },
  {
    field: 'minPackageQty',
    headerName: i18n.t('最小包装量'),
    width: 105
  },
  {
    field: 'minPurchaseQty',
    headerName: i18n.t('最小采购量'),
    width: 105
  },
  {
    field: 'leadTime',
    headerName: i18n.t('L/T'),
    width: 96
  },
  {
    field: 'unconditionalLeadTime',
    headerName: i18n.t('无条件L/T'),
    width: 96
  },
  {
    field: 'validStartTime',
    headerName: i18n.t('生效日期'),
    width: 130,
    valueFormatter: (params) => {
      return params.value ? formatTime(new Date(params.value), 'Y-mm-dd') : ''
    }
  },
  {
    field: 'validEndTime',
    headerName: i18n.t('失效日期'),
    width: 130,
    valueFormatter: (params) => {
      return params.value ? formatTime(new Date(params.value), 'Y-mm-dd') : ''
    }
  },
  {
    field: 'errorMsg',
    headerName: i18n.t('错误信息'),
    width: 300,
    tooltipValueGetter: (params) => {
      return params.value
    }
  },
  {
    field: 'validEndDate',
    headerName: i18n.t('更新后失效日期'),
    width: 132,
    pinned: 'right',
    option: 'customEdit',
    editable: true,
    editConfig: {
      type: 'date',
      props: {
        format: 'yyyy-MM-dd',
        'show-clear-button': true
      }
    }
  }
]

//详情 - 物料明细 - 操作按钮
export const itemToolbar = (status) => [
  {
    id: 'add',
    title: i18n.t('新增'),
    hide: ![0, 3, 7, 8].includes(status) //草稿、审批驳回
  },
  {
    id: 'save',
    title: i18n.t('保存'),
    hide: ![0, 3, 7, 8].includes(status) //草稿、审批驳回、审批废弃、审批撤回
  },
  {
    id: 'delete',
    title: i18n.t('删除'),
    hide: ![0, 3, 7, 8].includes(status) //草稿、审批驳回、审批废弃、审批撤回
  },
  {
    id: 'import',
    title: i18n.t('导入'),
    hide: ![0, 3, 7, 8].includes(status) //草稿、审批驳回、审批废弃、审批撤回
  },
  {
    id: 'export',
    title: i18n.t('导出')
  },
  {
    id: 'setting',
    title: i18n.t('失效时间设置'),
    hide: ![0, 3, 7, 8].includes(status) //草稿、审批驳回、审批废弃、审批撤回
  }
]
