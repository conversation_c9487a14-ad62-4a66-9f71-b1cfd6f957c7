import { i18n } from '@/main.js'
import { formatTime } from '@/utils/utils'
// 阶梯类型
const stepQuoteTypeMap = {
  '-1': i18n.t('无梯度'),
  0: i18n.t('数量累计阶梯'),
  3: i18n.t('数量逐层阶梯')
}
// 是否
const whetherMap = {
  0: i18n.t('否'),
  1: i18n.t('是')
}
export const columnData = [
  {
    type: 'checkbox',
    width: '50'
  },
  {
    width: '160',
    field: 'purchaseOrgName',
    headerText: i18n.t('采购组织'),
    valueAccessor: (field, data) => {
      return data.purchaseOrgCode + '-' + data.purchaseOrgName
    }
  },
  {
    width: '160',
    field: 'factoryName',
    headerText: i18n.t('工厂'),
    valueAccessor: (field, data) => {
      return data.factoryCode + '-' + data.factoryName
    }
  },
  {
    width: '140',
    field: 'materialCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '150',
    field: 'materialName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '160',
    field: 'supplierName',
    headerText: i18n.t('供应商'),
    valueAccessor: (field, data) => {
      return data.supplierCode + '-' + data.supplierName
    }
  },
  {
    width: '·20',
    field: 'stepQuote',
    headerText: i18n.t('是否阶梯报价'),
    valueAccessor: (field, data) => {
      return whetherMap[data[field]]
    }
  },
  {
    width: '150',
    field: 'stageType',
    headerText: i18n.t('阶梯类型'),
    valueAccessor: (field, data) => {
      return stepQuoteTypeMap[data[field]]
    }
  },
  {
    width: '120',
    field: 'stepValue',
    headerText: i18n.t('阶梯数量')
  },
  {
    width: '150',
    field: 'unitPriceUntaxed',
    headerText: i18n.t('单价（未税）')
  },
  { width: '150', field: 'priceUnitName', headerText: i18n.t('价格单位') },

  { width: '150', field: 'directDeliverAddr', headerText: i18n.t('直送地') },
  {
    width: '150',
    field: 'validStartTime',
    headerText: i18n.t('价格有效期从'),
    valueAccessor: (field, data) => {
      return data[field] ? formatTime(new Date(data[field]), 'Y-mm-dd') : ''
    }
  },
  {
    width: '150',
    field: 'validEndTime',
    headerText: i18n.t('价格有效期至'),
    valueAccessor: (field, data) => {
      return data[field] ? formatTime(new Date(data[field]), 'Y-mm-dd') : ''
    }
  },
  {
    field: 'sourceCode',
    headerText: i18n.t('来源单号'),
    width: '200'
  }
]
