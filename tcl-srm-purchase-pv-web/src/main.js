import Vue from 'vue'
// import "@digis/component-props-state";
import App from './App.vue'
import Router from 'vue-router'
import store from './store'
import routes from './router'
import API_PROMISE from '@/apis'
import '@mtech-micro-frontend/vue-cli-plugin-micro/public-path.js'
import { setLocal } from '@mtech-ui/base'
import $utils from '@/utils/utils'
import { $http } from '@/utils/httpClient'
import { sso } from '@mtech-sso/single-sign-on' //, hasAuth, clearAuth
import { Theme } from '@mtech-common/utils'
// import { JReferFormula } from '@digis/j-antdv'
// import '@digis/j-antdv/lib/style/j-index-css.css'
// import permission from '@/permission'
import md5 from 'md5'
import getDictionary from '@/utils/dictionary'
import 'ag-grid-community/dist/styles/ag-grid.css'
import 'ag-grid-community/dist/styles/ag-theme-alpine.css'
// 初始化ui组件
import './mtechUi'

import '@/icons'
import { LicenseManager } from '@ag-grid-enterprise/core'
LicenseManager.setLicenseKey(
  'CompanyName=TCL KING ELECRTICAL APPLIANCES(HUIZHOU) CO.LTD,LicensedGroup=DP-NA Teams,LicenseType=MultipleApplications,LicensedConcurrentDeveloperCount=1,LicensedProductionInstancesCount=0,AssetReference=AG-028797,ExpiryDate=30_May_2023_[v2]_MTY4NTQwMTIwMDAwMA==35611a53b51030476f74a3d9096db921'
)
Vue.use(Router)
import Bus from '@/utils/bus.js'
Vue.prototype.$bus = Bus
Vue.prototype.$utils = $utils
Vue.prototype.$http = $http
Vue.config.productionTip = false
Vue.prototype.$store = store
Vue.prototype.$API = API_PROMISE
// Vue.prototype.$permission = permission
Vue.prototype.$md5 = md5

// 指令
import '@/directive/insert-toggle-class'
import waves from '@/directive/waves'

Vue.use(waves)

import { baseConfig } from '@mtech-common/http'
baseConfig.setDefault({ baseURL: '/api' })
baseConfig.addNotify({
  success: function (msg) {
    // 可以使用$toast组件，msg当前版本下默认为接口response.msg
    Vue.prototype.$toast({
      content: msg,
      type: 'success'
    })
  },
  error: function (msg) {
    console.warn('应用层报错：', msg)
    // 请求静态模板时不再抛出错误提示
    if (msg) {
      Vue.prototype.$toast({
        content: msg,
        type: 'error'
      })
    }
    Vue.prototype.$store.commit('endLoading')
  }
})

getDictionary()
// setLocal("zh-CN");
const internationlization = localStorage.getItem('internationlization')
if (internationlization === 'zh' || internationlization === 'zh-CH') {
  setLocal('zh-CN')
}

// 国际化
import indexDB from '@digis/internationalization'
const i18n = indexDB.digisI18n(Vue, 'sourcing') //第二个参数是当前使用的应用code
export { i18n, md5 }

let router = null
let instance = null
function theme(props) {
  const { entry, container } = props

  const changeTheme = new Theme()

  const themeName = localStorage.getItem('mt-layout-theme') || 'default'

  changeTheme.add(themeName, container, entry)
}

function render(props = {}) {
  const { container, routerMode = 'hash', defaultPath } = props
  router = new Router({
    routes,
    mode: routerMode
  })
  router.beforeEach((to, from, next) => {
    next()
  })

  indexDB.layoutCreatLanguage().finally(() => {
    instance = new Vue({
      router,
      store,
      i18n,
      render: (h) => h(App)
    }).$mount(container ? container.querySelector('#purchasePvApp') : '#purchasePvApp')
  })

  if (defaultPath) {
    router.push(defaultPath)
  }
}

if (!window.__POWERED_BY_QIANKUN__) {
  sso()
  render()
  theme({
    entry: location.origin,
    container: document.head
  })
}

function storeTest(props) {
  props.onGlobalStateChange &&
    props.onGlobalStateChange(
      (value, prev) => console.log(`[onGlobalStateChange - ${props.name}]:`, value, prev),
      true
    )
  props.setGlobalState &&
    props.setGlobalState({
      ignore: props.name,
      user: {
        name: props.name
      }
    })
}

//single-spa的生命周期函数
export async function bootstrap({ fns = [] } = {}) {
  Array.isArray(fns) &&
    fns.map((i) => {
      Vue.prototype[i.name] = i
    })
  console.log('%c ', 'color: green;', 'app bootstraped')
}

export async function mount(props) {
  // 在这里挂载vue
  storeTest(props)
  render(props)
  theme(props)
  console.log('%c ', 'color: green;', 'app mount', props)
}

export async function unmount() {
  // 在这里unmount实例的vue
  instance.$destroy()
  instance.$el.innerHTML = ''
  instance = null
  router = null
  console.log('%c ', 'color: green;', 'app unmount')
}
