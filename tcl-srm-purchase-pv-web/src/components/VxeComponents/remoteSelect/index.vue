<template>
  <vxe-pulldown ref="pulldownRef" destroy-on-close>
    <template #default>
      <vxe-input v-model="value" v-show="false" />
      <vxe-input
        v-model="showValue"
        suffix-icon="vxe-icon-caret-down"
        :disabled="disabled"
        readonly
        :placeholder="$t('请输入')"
        @click="handlePulldown"
        :title="showValue"
      />
    </template>
    <template #dropdown>
      <vxe-input
        ref="serachInputRef"
        :style="{ width: listWidth ? listWidth + 'px' : '100%' }"
        v-model="searchValue"
        :prefix-icon="'vxe-icon-search'"
        clearable
        :placeholder="$t('搜索')"
        @input="handleSearch"
      />
      <vxe-list
        class="dropdown-list"
        :style="{ width: listWidth ? listWidth + 'px' : '100%' }"
        :data="list"
        auto-resize
        height="auto"
      >
        <template #default="{ items }">
          <div v-if="items.length">
            <div
              class="dropdown-list-item"
              v-for="item in items"
              :key="item[fields.value]"
              @click="handleSelected(item)"
            >
              <span :class="{ isSelected: item[fields.value] === value }">{{ item.label }}</span>
            </div>
          </div>
          <div v-else class="empty-tip">
            {{ $t('暂无数据') }}
          </div>
        </template>
      </vxe-list>
    </template>
  </vxe-pulldown>
</template>

<script>
import { API } from '@mtech-common/http'
import { cloneDeep, debounce } from 'lodash'
import { Pulldown as VxePulldown, Input as VxeInput, List as VxeList } from 'vxe-table'
export default {
  name: 'RemoteSelect',
  components: {
    VxeInput,
    VxePulldown,
    VxeList
  },
  model: {
    prop: 'modelVal',
    event: 'syncChange'
  },
  props: {
    modelVal: {
      type: [String, Number, Array],
      default: null
    },
    defaultSearchText: {
      type: [String],
      default: null
    },
    dataSource: {
      type: Array,
      default: () => {
        return []
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    // 数据源匹配对象
    fields: {
      type: Object,
      default: () => {
        return {
          text: 'text',
          value: 'value'
        }
      }
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 请求url
    url: {
      type: String,
      default: ''
    },
    // 请求方法
    method: {
      type: String,
      default: 'post'
    },
    // 请求传入的参数
    params: {
      type: [Object, Array],
      default: () => null
    },
    // 输入框查询的key值
    paramsKey: {
      type: String,
      default: 'fuzzyParam'
    },
    // 通过rules查询时候的查询字段
    searchFields: {
      type: Array,
      default: () => null
    },
    recordsPosition: {
      type: String,
      default: 'data.records'
    },
    // 额外的rules params
    ruleParams: {
      type: Array,
      default: () => {
        return []
      }
    },
    placeholder: {
      type: String,
      default: null
    },
    clearable: {
      type: Boolean,
      default: true
    },
    dataLimit: {
      // 查询条数
      type: Number,
      default: 50
    },
    // 初始化value值是否加载完成（处理value值是通过接口拿到的）
    isLoaded: {
      type: Boolean,
      default: true
    },
    listWidth: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      searchValue: '', // 输入框查询条件
      options: [], // 数据源
      list: [],
      // dataLimit: 20, //查询条数
      page: 1, //当前查询页数
      total: 0, //总条数
      checkedList: [], //多选缓存当前list
      isFill: false, //数据回填标识，初始value回填时判断
      observe: null,
      queryText: null, //当前下拉列表的查询条件
      isInitDataLoaded: false // 初始下拉数据是否加载
    }
  },
  computed: {
    value: {
      get() {
        return this.modelVal
      },
      set(val) {
        this.$emit('syncChange', val)
      }
    },
    showValue() {
      const _find = this.list.find((item) => item[this.fields.value] === this.value) || ''
      return this.generateLabel(_find)
    }
  },
  watch: {
    dataSource: {
      handler(val) {
        if (val) {
          this.options = this.arrSet([...val])
        }
      },
      immediate: true
    },
    options: {
      handler(val) {
        this.$emit('getOptions', val)
      }
    },
    isLoaded: {
      handler(newValue) {
        if (newValue) {
          this.remoteMethod({ text: this.defaultSearchText || '' })
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {},
  methods: {
    // 展开搜索框面板
    handlePulldown() {
      this.$refs.pulldownRef?.showPanel()
      this.$nextTick(() => {
        this.$refs.purchaserSearcInputRef?.focus()
      })
    },
    // 搜索查询
    handleSearch: debounce(function (e) {
      this.remoteMethod({ text: e?.value }, 'filter')
    }, 500),
    // 选中
    handleSelected(item) {
      this.value = item[this.fields.value]
      if (!this.$refs.pulldownRef) return
      this.$emit('change', item)
      this.$refs.pulldownRef.hidePanel()
      this.searchValue = null
    },

    init() {
      // // 初始modelValue非空，则回填select数据
      // this.modelVal && (this.isFill = true)
      // this.modelVal?.length === 0 && (this.isFill = false)
      // if (this.dataSource.length === 0) this.remoteMethod(this.modelVal || '', 'init')
    },
    remoteMethod(e = { text: '' }, type) {
      if (type === 'filter' && e.text === this.queryText) return
      if (type === 'filter') {
        this.page = 1
      }
      // 非url接口请求，直接赋值
      if (!this.url) {
        this.options = []
        return
      }
      /**
       * 目前有两种形式的传参
       * 1.通过paramsKey入参 default
       * 2.通过rules入参 需传递searchFields: ['supplierCode', 'supplierName'],
       */
      const params = this.getParams(e)
      this.isFill = false //初始话选然后置为false
      API[this.method](this.url, params || {}).then((response) => {
        if (response.code === 200 && response.data) {
          const res = cloneDeep(response)
          this.isInitDataLoaded = true
          const _dataList = this.deepGet(res, this.recordsPosition || 'data.records')
          const _list = this.arrSet(_dataList)
          this.list = cloneDeep(_list)
        }
      })
    },
    // 级联获取对象数据，如获取data.records数据
    deepGet(obj, keys, defaultVal) {
      return keys.split(/\./).reduce((o, j) => (o || {})[j], obj) || defaultVal
    },
    // 去重 & 拼接label - value
    arrSet(arr) {
      let obj = {}
      const res = arr.reduce((setArr, item) => {
        let _field = item[this.fields.value]
        let _label = this.generateLabel(item)
        if (!obj[_field]) {
          obj[_field] = true
          item.label = _label //拼接codeValue
          setArr.push(item)
        } else {
          let _findIndex = setArr.findIndex((i) => i[_field] == _field)
          setArr[_findIndex] = {
            ...setArr[_findIndex],
            ...item,
            label: _label //拼接codeValue
          }
        }
        return setArr
      }, [])
      return res
    }, // 生成label (目前支持code-value形式)
    generateLabel(data) {
      if (!data) return ''
      let _text = this.fields.text.split('-')
      let _label = ''
      if (_text?.length > 1) {
        _label = _text.map((item) => data[item]).join('-')
      } else {
        _label = data[this.fields.value] + ' - ' + data[this.fields.text]
      }
      return _label
    },
    // 获取入参条件
    getParams(e) {
      // 1.通过rules查询
      if (this.searchFields) {
        let searchRules = this.getSearchRules(e.text)
        return {
          condition: 'and',
          page: { current: this.page, pages: 1, size: this.dataLimit },
          rules: [...searchRules, ...this.ruleParams] //拼接额外的ruleParams
        }
      }
      // 2.通过paramsKey查询数据
      return {
        ...this.params,
        page: { current: this.page, pages: 1, size: this.dataLimit },
        [this.paramsKey]: this.isFill ? this.modelVal : e.text
        // ...searchRules[0],
      }
    },
    // 获取rule入参
    getSearchRules(value) {
      let searchFields = this.searchFields
      let rules = []
      if (this.isFill) {
        // 初始数据回填时候根据value定义的字段拼接值进行查询
        rules.push({
          condition: 'or',
          label: '',
          field: this.fields.value, //根据交互的字段精准查询
          type: 'string',
          operator: 'in',
          value: this.modelVal
        })
      } else if (searchFields) {
        // 拼接查询字段
        searchFields.forEach((item) => {
          rules.push({
            condition: 'or',
            label: '',
            field: item,
            type: 'string',
            operator: 'contains',
            value: value || ''
          })
        })
      }
      // 合并rules params并返回
      return [
        {
          condition: 'and',
          rules
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.dropdown-list {
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 4px 0;
  max-height: 200px;
  border-radius: 4px;
  border: 1px solid #dadce0;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.dropdown-list-item {
  line-height: 30px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 0 4px;
  .isSelected {
    font-weight: 700;
    color: #409eff;
  }
}
.dropdown-list-item:hover {
  background-color: #f5f7fa;
}
.empty-tip {
  width: 100%;
  line-height: 30px;
  padding: 0 4px;
  text-align: center;
  color: #c0c4cc;
}
::v-deep {
  .vxe-pulldown {
    width: 100%;
    height: 34px;
    line-height: 34px;
  }
  .vxe-input {
    width: 100%;
  }
  .vxe-pulldown--panel {
    min-width: unset !important;
    width: 100%;
  }
}
</style>
