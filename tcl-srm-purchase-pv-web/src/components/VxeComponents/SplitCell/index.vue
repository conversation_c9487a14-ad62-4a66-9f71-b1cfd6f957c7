<template>
  <!-- 自定单元格拆分 -->
  <div class="split-cell-container">
    <div v-for="(item, index) in valueList" :key="index">
      <div class="cell-item" @click="handleClick(index)">
        <a v-if="type === 'operation'">{{ operationName || item }}</a>
        <span v-else>{{ item }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => []
    },
    // 单元格类型
    type: {
      type: String,
      default: ''
    },
    // 操作列名称
    operationName: {
      type: String,
      default: ''
    }
  },
  watch: {
    list: {
      handler(val) {
        this.valueList = val || []
      },
      deep: true
    }
  },
  data() {
    return {
      valueList: []
    }
  },
  computed: {},
  mounted() {
    this.valueList = this.list || []
  },
  methods: {
    handleClick(index) {
      this.$emit('click', index)
    }
  }
}
</script>

<style lang="scss" scoped>
.split-cell-container {
  .cell-item {
    width: 100%;
    height: 32px;
    line-height: 32px;
    border-bottom: 1px solid #e6f0e5;
    padding: 0 10px;
  }
  &:last-child {
    border-bottom: none;
  }
}
</style>
