export default {
  name: 'FreeEditor',
  props: {
    scope: {
      type: Object,
      default: () => {
        return {}
      }
    },
    editor: {
      type: Function,
      default: () => {}
    },
    field: {
      type: String,
      default: ''
    }
  },
  watch: {
    'scope.row': {
      handler(obj) {
        this.$emit('setValue', obj[this.field])
      },
      deep: true
    }
  },
  render: function (h) {
    return this?.editor ? this?.editor(h, this?.scope) : ''
  }
}
