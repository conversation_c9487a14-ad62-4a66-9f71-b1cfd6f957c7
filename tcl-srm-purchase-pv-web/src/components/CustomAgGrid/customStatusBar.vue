<template>
  <div>
    <sc-pagination
      :page-size.sync="pageSize"
      :page-count="pageCount"
      :current-page="page"
      @current-change="change"
    />
  </div>
</template>

<script>
import ScPagination from '@/components/sc-pagination'
export default {
  components: {
    ScPagination
  },
  data() {
    return {
      page: 1,
      pageSize: 10,
      pageCount: 0,
      pageParams: null
    }
  },
  watch: {
    pageSize() {
      this.getPagingData()
    }
  },
  methods: {
    change(page) {
      this.page = page
      this.getPagingData()
    },
    // 获取分页数据
    async getPagingData(params) {
      const _params = params || this.pageParams
      if (params) this.pageParams = params
      let postData = this.pageParams.data || {}

      postData = Object.assign({}, _params.data, {
        page: this.page - 1,
        size: this.pageSize
      })
      const res = await this.$fetch[_params.method || 'get'](_params.url, postData)
      // 处理分页逻辑
      if (res.code === 0) {
        this.pageCount = res.data.totalPages // 总页数
        this.params.api.setRowData(res.data.content)
        return res
      }
    }
  }
}
</script>
