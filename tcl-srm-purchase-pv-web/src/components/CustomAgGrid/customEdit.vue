<template>
  <div style="padding-left: 8px">
    <mt-select
      v-if="editConfig.type && editConfig.type === 'select'"
      ref="selectRef"
      v-model="value"
      v-bind="editProps"
    ></mt-select>
    <mt-multi-select
      v-if="editConfig.type && editConfig.type === 'multiSelect'"
      ref="multiSelect"
      v-model="value"
      v-bind="editProps"
    ></mt-multi-select>
    <mt-input-number
      v-else-if="editConfig.type && editConfig.type === 'number'"
      v-model="value"
      v-bind="editProps"
    />
    <mt-date-picker
      v-else-if="editConfig.type && (editConfig.type === 'date' || editConfig.type === 'dateMonth')"
      v-model="value"
      v-bind="editProps"
      :open-on-focus="true"
    ></mt-date-picker>
    <mt-date-time-picker
      v-else-if="editConfig.type && editConfig.type === 'datetime'"
      v-model="value"
      v-bind="editProps"
      :open-on-focus="true"
    ></mt-date-time-picker>
    <SelectSearch
      v-else-if="editConfig.type && editConfig.type === 'selectSearch'"
      ref="selectRef"
      v-model="value"
      v-bind="editProps"
      :params="params"
    ></SelectSearch>
    <CellSelectSearch
      v-else-if="editConfig.type && editConfig.type === 'cellSelectSearch'"
      ref="selectRef"
      v-model="value"
      v-bind="editProps"
      :params="params"
    ></CellSelectSearch>
    <CellRemoteSearch
      v-else-if="editConfig.type && editConfig.type === 'cellRemoteSelect'"
      ref="selectRef"
      v-model="value"
      v-bind="editProps"
      :params="params"
    ></CellRemoteSearch>
    <CellCost
      v-else-if="editConfig.type && editConfig.type === 'cellCost'"
      ref="selectRef"
      v-model="value"
      v-bind="editProps"
      :params="params"
    ></CellCost>
    <CellDesc
      v-else-if="editConfig.type && editConfig.type === 'cellDesc'"
      ref="selectRef"
      v-model="value"
      v-bind="editProps"
      :params="params"
    ></CellDesc>
    <mt-input v-else v-model="value"></mt-input>
    <FreeEditor
      v-if="editConfig.type && typeof editConfig.type === 'function'"
      ref="inputVal"
      :scope="scope"
      :editor="params.editor"
      :field="params.field"
      @setValue="setValue"
    />
  </div>
</template>

<script>
import FreeEditor from './editor'
import SelectSearch from '../AgCellComponents/selectSearch'
import CellSelectSearch from '../AgCellComponents/cellSelectSearch'
import CellRemoteSearch from '../AgCellComponents/cellRemoteSelect'
import CellCost from '../AgCellComponents/cellCost'
import CellDesc from '../AgCellComponents/cellDesc/index.vue'
export default {
  components: { SelectSearch, CellSelectSearch, CellRemoteSearch, CellCost, FreeEditor, CellDesc },
  data() {
    return {
      value: null
    }
  },
  computed: {
    editConfig() {
      return this.params?.editConfig || {}
    },
    editProps() {
      return this.editConfig?.props ? { ...this.editConfig.props } : { ...this.editConfig }
    }
  },
  beforeMount() {
    this.scope = { row: this.params.data, $index: this.params.rowIndex }
  },
  mounted() {
    // 初始化this.value, 非常重要，如果没有此操作，在结束编辑状态时ag-grid维护的数据源将丢失原来对应的单元格数据
    this.value = this.params.value
  },
  methods: {
    // 此方法很重要，在结束编辑状态时ag-grid会自动调用该方法将值传到grid的数据源
    getValue() {
      if (this.editConfig?.type === 'number' && this.editProps?.precision) {
        if (!this.value && this.value !== 0) return ''
        let _value = Number(this.value)
        return parseFloat(
          (
            Math.round((_value + Number.EPSILON) * Math.pow(10, this.editProps?.precision)) /
            Math.pow(10, this.editProps?.precision)
          ).toFixed(this.editProps?.precision)
        )
      }
      if (this.editConfig?.type === 'multiSelect') {
        // 多选框不会直接失去焦点不会触发失焦，手动触发
        this.$refs.multiSelect.ejsRef.focusOut()
      }
      return this.value
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-input__inner {
  border: none;
}
</style>
