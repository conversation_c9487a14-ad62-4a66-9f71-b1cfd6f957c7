export default {
  name: '<PERSON><PERSON><PERSON>',
  functional: true,
  props: {
    scope: {
      type: Object,
      default: () => {
        return {}
      }
    },
    render: {
      type: Function,
      default: () => {}
    }
  },
  mounted() {
    this.formatData()
  },
  methods: {
    formatData() {
      let index = 1
      this.templateData = this.value.replace(/\$\.(number)\{(.*?)\}/g, (match, p1, p2) => {
        this.$set(this.inputsData, `input${index}`, p2)
        let replaceStr = `<mt-input-number v-model="{this.aa}"/>`
        index++
        return replaceStr
      })
    }
  },
  render: (h, ctx) => {
    return ctx?.props.render ? ctx?.props.render(h, ctx.props.scope) : ''
  }
}
