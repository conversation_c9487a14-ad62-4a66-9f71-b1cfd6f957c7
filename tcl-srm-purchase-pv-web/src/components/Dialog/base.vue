<template>
  <transition name="fade" @after-leave="afterLeave" @after-enter="afterEnter">
    <div class="component-dialog" v-if="visible">
      <component
        :is="getModal"
        :modal-data="data"
        @cancel-function="cancel"
        @confirm-function="confirm"
      ></component>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'Compose',
  props: {
    modal: {
      type: Function,
      default: () =>
        import(/* webpackChunkName: "components/dialog/small-dialog" */ './modals/small-dialog')
    },
    data: {
      type: Object,
      default: () => {
        return {}
      }
    },
    success: {
      type: Function,
      default: () => {}
    },
    close: {
      type: Function,
      default: () => {}
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    getModal() {
      return this.modal
    }
  },
  data() {
    return {
      // visible: false,
    }
  },
  mounted() {},
  methods: {
    confirm(data) {
      this.$emit('confirm', data, this.success)
    },
    cancel() {
      console.log('base--cancel')
      this.$emit('close', this.close())
    },
    afterLeave() {
      this.$emit('closed')
    },
    afterEnter() {}
  }
}
</script>
