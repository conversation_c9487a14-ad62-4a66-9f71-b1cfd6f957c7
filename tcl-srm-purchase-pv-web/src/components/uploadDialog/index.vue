<template>
  <mt-dialog
    z-index="9999"
    ref="dialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="import-process">
      <div v-if="downloadTemplateApi" class="download">
        <i>1</i>
        <span @click="importTemplate">{{ $t('模板下载') }}</span>
      </div>
      <ul v-if="downloadTemplateApi">
        <li v-for="index in 4" :key="index"></li>
      </ul>
      <div :class="headerFlag ? 'import' : 'choose'">
        <i>{{ downloadTemplateApi ? 2 : 1 }}</i>
        <span>{{ $t('选择文件') }}</span>
      </div>
      <ul>
        <li v-for="index in 4" :key="index"></li>
      </ul>
      <div :class="headerFlag ? 'choose' : 'import'">
        <i>{{ downloadTemplateApi ? 3 : 2 }}</i>
        <span>{{ $t('导入文件') }}</span>
      </div>
    </div>
    <div class="uploader-box">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-form-item prop="file" class="form-item">
          <div class="cell-upload">
            <div class="to-upload">
              <input type="file" ref="file" class="upload-input" @change="chooseFiles" />
              <div class="upload-box">
                <div class="plus-icon"></div>
                <div class="right-state">
                  <div class="plus-txt">{{ $t('请拖拽文件或点击上传') }}</div>
                  <div class="warn-text">
                    {{ $t('文件最大不可超过50M， 文件格式仅支持.xls .xlsx .doc') }}
                  </div>
                </div>
              </div>
            </div>
            <div class="has-file" v-if="!!uploadInfo.fileName">
              <div class="left-info">
                <div class="file-title">
                  <span class="text-ellipsis">{{ uploadInfo.fileName }}</span>
                  <span>{{ uploadInfo.fileSize }} kb</span>
                </div>
              </div>
              <mt-icon
                name="icon_Close_2"
                class="close-icon"
                @click.native="handleRemove"
              ></mt-icon>
            </div>
          </div>
        </mt-form-item>
        <div v-if="this.downloadTemplateApi" class="template-to-import">
          {{ $t('为了保证数据导入顺利，推荐您下载使用') }}
          <span class="import-the-template" @click="importTemplate">{{ $t('导入模板') }}</span
          >,{{ $t('并按照规范示例录入数据') }}
        </div>
        <div class="specification">
          •{{ $t('如果导入的数据中有不合规数据，将直接自动下载一个excel文件到本地供您参考') }}
          <br />•
          {{ $t('上传的 Excel 表符合以下规范:') }}
          <br />• {{ $t('文件大小不超过20MB，这里是规范限制文案') }} <br />•
          {{ $t('仅支持 （*.xls 和 *.xlsx）文件') }} <br />•
          {{ $t('请确保您需要导入的sheet表头中不包含空的单元格，否则该sheet页数据系统将不做导入') }}
        </div>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils'
// import { download, getHeadersFileName } from '@/utils/utils'
let fileData = null
export default {
  data() {
    return {
      formInfo: {
        remark: ''
      },
      rules: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          // click: debounce(this.confirm, 1000),
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('导入') }
        }
      ],
      uploadInfo: {}, // 上传后信息
      headerFlag: false,
      isUploading: false
    }
  },
  props: {
    // 所有需要传入的 props 均在 computed 下面做了拆分
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    // 标题
    header() {
      return this.modalData.title
    },
    // 预留传参对象
    applyInfo() {
      return this.modalData.applyInfo
    },
    // 导入接口url
    importApi() {
      return this.modalData.importApi
    },
    // 下载模板url
    downloadTemplateApi() {
      return this.modalData.downloadTemplateApi
    },
    // 上传的文件formdata中对应的key名
    paramsKey() {
      if (this.modalData.paramsKey) {
        return this.modalData.paramsKey
      }
      return 'file'
    },
    // 上传的接口需要额外附加的参数
    asyncParams() {
      return this.modalData.asyncParams
    },
    // 下载模板的接口需要额外附加的参数
    downloadTemplateParams() {
      return this.modalData.downloadTemplateParams
    }
  },
  mounted() {
    this.show()
  },
  methods: {
    chooseFiles(data) {
      console.log(this.$t('点击上传'), data)
      this.$loading()
      let { files } = data.target
      let params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (files.length < 1) {
        this.$hloading()
        this.headerFlag = false
        // 您未选择需要上传的文件
        return
      }
      let _data = new FormData(),
        isOutOfRange = false
      for (let i = 0; i < files.length; i++) {
        _data.append(this.paramsKey, files[i])
        if (files[i].size > params.limit * 1024) {
          isOutOfRange = true

          break
        }
      }
      if (isOutOfRange) {
        this.$hloading()
        this.$toast({
          content: params.msg
        })
        return
      }
      fileData = _data
      this.$refs.file.value = ''
      let file = fileData.get(this.paramsKey)
      this.uploadInfo = {
        fileName: file.name,
        fileSize: file.size
      }
      this.headerFlag = true
      this.$hloading()
      this.$toast({ content: this.$t('操作成功'), type: 'success' })
    },

    // 移除文件
    handleRemove() {
      this.uploadInfo = {}
      fileData = null
      this.headerFlag = false
      console.log('点击了remove-file', this.data)
    },
    importTemplate() {
      console.log('为保证数据下载使用顺利,推荐您下载使用导入模块')
      if (this.downloadTemplateApi) {
        let params = {}
        if (this.downloadTemplateParams) {
          params = this.downloadTemplateParams
        }
        this.downloadTemplateApi(params).then((res) => {
          const { data } = res
          utils.download({
            // fileName: decodeURI(headers['content-disposition'].split('=')[1]),
            fileName: utils.getHeadersFileName(res),
            blob: data
          })
        })
      }
    },
    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm() {
      this.$store.commit('startLoading')
      if (utils.isEmpty(this.uploadInfo)) {
        this.$toast({ content: this.$t('请选择文件上传！'), type: 'warning' })
        this.$store.commit('endLoading')
        return
      }
      // 加isUploading为了防止沙雕光速点击及接口响应时间长导致的重复提交
      if (this.importApi && !this.isUploading) {
        if (this.asyncParams) {
          // 如果传了参数就一一append进formdata对象
          const paramsKeys = Object.keys(this.asyncParams)
          paramsKeys.forEach((i) => {
            if (!fileData.get(i)) {
              fileData.append(i, this.asyncParams[i])
            }
          })
        }
        this.isUploading = !this.isUploading
        this.importApi(fileData)
          .then((res) => {
            const { data, code } = res
            if (data.size == 0 || code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('confirm-function', data)
            } else {
              let _this = this
              if (data.type === 'application/json') {
                const reader = new FileReader() //创建一个FileReader实例
                reader.readAsText(data, 'utf-8') //读取文件,结果用字符串形式表示
                reader.onload = function () {
                  //读取完成后,**获取reader.result**
                  const { msg } = JSON.parse(reader.result)
                  if (msg === 'success') {
                    _this.$toast({ content: _this.$t('操作成功'), type: 'success' })
                    _this.$emit('confirm-function', JSON.parse(reader.result))
                    return
                  }
                  _this.$toast({
                    content: msg || _this.$t('上传失败，错误详情请查看文件'),
                    type: 'warning'
                  })
                }
                return
              }
              if (data.msg === 'success') {
                _this.$toast({ content: this.$t('操作成功'), type: 'success' })
                _this.$emit('confirm-function', data)
                return
              }
              this.$toast({
                content: data.msg || this.$t('上传失败，错误详情请查看文件'),
                type: 'warning'
              })
              utils.download({
                fileName: utils.getHeadersFileName(res),
                blob: data
              })
            }
          })
          .catch((error) => {
            this.$toast({ content: error.msg || this.$t('上传失败'), type: 'warning' })
          })
          .finally(() => {
            this.isUploading = !this.isUploading
            this.$store.commit('endLoading')
          })
      }
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
//选择文件 导入文件
.import-process {
  width: 385px;
  height: 24px;
  margin: 45px auto 0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  ul {
    width: 40px;
    height: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    li {
      width: 4px;
      height: 4px;
      border-radius: 50%;
      background-color: #6386c1;
    }
  }

  .choose {
    width: 99px;
    height: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    i {
      display: block;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background-color: #6386c1;
      text-align: center;
      line-height: 24px;
      color: #fff;
      font-size: 14px;
      font-style: normal;
    }
    span {
      display: block;
      width: 71px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(0, 70, 156, 1);
      border-bottom: 2px solid #6386c1;
    }
  }
  .import {
    width: 99px;
    height: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    i {
      display: block;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      border: 1px solid #6386c1;
      text-align: center;
      line-height: 24px;
      color: #6386c1;
      font-size: 14px;
      font-style: normal;
    }
    span {
      display: block;
      width: 71px;
      height: 24px;
      text-align: center;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 500;
      color: black;
      line-height: 24px;
    }
  }
  .download {
    width: 90px;
    height: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    i {
      display: block;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      border: 1px solid red;
      text-align: center;
      line-height: 24px;
      color: red;
      font-size: 14px;
      font-style: normal;
    }
    span {
      cursor: pointer;
      display: block;
      width: 65px;
      height: 24px;
      text-align: center;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 500;
      color: red;
      line-height: 24px;
    }
    span:hover {
      font-weight: bold;
    }
  }
}
//主体
.uploader-box {
  width: 100%;
  margin: 50px auto 0;
  .form-box {
    width: 100%;
    .form-item {
      width: 820px;
      margin: 0 auto;
      .cell-upload {
        position: relative;
        width: 820px;
        height: 250px;
        background: rgba(251, 252, 253, 1);
        border: 1px solid rgba(232, 232, 232, 1);
        margin: 0 auto;
        .has-file {
          font-size: 16px;
          font-weight: bold;
          display: flex;
          align-items: center;
          .close-icon {
            cursor: pointer;
            margin-left: 10px;
          }
        }
        //覆盖的选择文件框
        .upload-input {
          width: 100%;
          height: 75%;
          background: rgba(251, 252, 253, 1);
          border: 1px solid rgba(232, 232, 232, 1);
          box-sizing: border-box;
          position: absolute;
          z-index: 2;
          top: 0;
          background: transparent;
          opacity: 0;
        }
        //添加文档和说明文字区域
        .upload-box {
          width: 100%;
          height: 100%;
          //十字架
          .plus-icon {
            width: 60px;
            height: 60px;
            position: relative;
            margin: 50px auto 0;
            border: 1px dashed #000;
            &::before {
              content: ' ';
              display: inline-block;
              width: 60px;
              height: 2px;
              background: #98aac3;
              position: absolute;
              top: 50%;
              left: -1px;
            }

            &::after {
              content: ' ';
              display: inline-block;
              width: 2px;
              height: 60px;
              background: #98aac3;
              position: absolute;
              top: -1px;
              left: 50%;
            }
          }
          //文字
          .right-state {
            text-align: center;

            .plus-txt {
              margin: 20px auto 0;
              width: 270px;
              height: 24px;
              font-size: 24px;
              font-family: PingFangSC;
              font-weight: normal;
              color: rgba(152, 170, 195, 1);
            }
            .warn-text {
              margin: 16px auto 0;
              font-size: 12px;
              width: 369px;
              height: 21px;
              font-size: 14px;
              font-family: PingFangSC;
              font-weight: normal;
              color: rgba(241, 62, 62, 1);
            }
          }
        }
      }
    }
  }
  //导入模板规范
  .template-to-import {
    width: 566px;
    height: 24px;
    font-size: 16px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(41, 41, 41, 1);
    margin-top: 10px;
    margin-left: 15px;
    .import-the-template {
      cursor: pointer;
      color: #fe3131;
      font-weight: bold;
      &:hover {
        border-bottom: 2px solid #fe3131;
      }
    }
  }
  .specification {
    width: 639px;
    height: 98px;
    margin-top: 31px;
    margin-left: 15px;
    font-size: 16px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(154, 154, 154, 1);
  }
}
</style>
