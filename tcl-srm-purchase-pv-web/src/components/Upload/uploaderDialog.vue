<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="title"
    :buttons="buttons"
    :open="onOpen"
    :close="init"
  >
    <div>
      <upload-file
        v-if="viewFileData.length > 0 || !isView"
        :is-view="isView"
        :upload-text="uploadText"
        :view-file-data="viewFileData"
        ref="uploader"
        @change="fileChange"
        @removeOne="removeOne"
        @removeAll="removeAll"
      ></upload-file>
      <div class="empty" v-else>
        <svg-icon icon-class="empty-data"></svg-icon>
        <div>{{ $t('没有数据') }}</div>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import UploadFile from '@/components/Upload/uploader'

export default {
  components: {
    UploadFile
  },
  data() {
    return {
      uploadText: this.$t('请拖拽文件或点击上传'),
      title: this.$t('附件上传'),
      isView: false,
      required: true,
      fieldName: 'fileList', // 返回的字段名称
      uploadData: [], // 上传完成的文件数据
      viewFileData: [], // 查看状态的数据
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  methods: {
    init() {
      this.$refs.uploader.init()
    },
    fileChange(data) {
      this.uploadData = data
      this.$emit('change', data)
    },
    /**
     * fileData: 文件信息;
     * isView：是否为查看状态;
     * required：是否必须;
     * title: 标题;
     */
    dialogInit(entryInfo) {
      const { fileData, isView, required, title, fieldName, uploadText } = entryInfo
      this.viewFileData = fileData || []
      this.isView = isView || false // 默认 上传状态
      this.required = required != null ? required : true // 默认 必须
      this.title = title || this.$t('附件上传') // 默认 "附件上传"
      this.fieldName = fieldName || 'fileList'
      this.uploadText = uploadText || this.$t('请拖拽文件或点击上传')
      if (this.isView) {
        // 查看状态
        this.buttons = [
          {
            click: this.handleClose,
            buttonModel: { isPrimary: 'true', content: this.$t('确定') }
          }
        ]
      } else {
        // 编辑状态
        this.buttons = [
          {
            click: this.handleClose,
            buttonModel: { content: this.$t('取消') }
          },
          {
            click: this.confirm,
            buttonModel: { isPrimary: 'true', content: this.$t('确定') }
          }
        ]
      }
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    confirm() {
      if (
        !this.isView &&
        this.required &&
        this.uploadData.length == 0 &&
        this.viewFileData.length == 0
      ) {
        this.$toast({ content: this.$t('请上传附件'), type: 'warning' })
        return
      }
      this.$emit('confirm', {
        [this.fieldName]: this.uploadData, // 指定字段名称的上传数据
        viewFileData: this.viewFileData, // 查看状态的数据
        uploadData: this.uploadData // 上传的附件
      })
      this.handleClose()
    },
    removeOne(args) {
      this.$emit('removeOne', args)
    },
    removeAll(args) {
      this.$emit('removeAll', args)
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
<style lang="scss" scoped>
.empty {
  display: flex;
  justify-content: center;
  flex-direction: column;
  min-height: 200px;
  align-items: center;
  color: #a6a6a6;
}
</style>
