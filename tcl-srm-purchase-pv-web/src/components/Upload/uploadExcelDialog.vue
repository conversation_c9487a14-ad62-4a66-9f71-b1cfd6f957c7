<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog upload-excel-dialog"
    :header="$t('上传')"
    :buttons="buttons"
    @close="handleClose"
    :loading="isLoading"
  >
    <div class="tips" v-show="!requestUrls.noDown">
      <span>{{ $t('请使用批量导入模板') }}</span>
      <span class="downs" @click="downloadTemplate">
        <mt-icon name="icon_list_download"></mt-icon>
        {{ $t('下载模板') }}</span
      >
    </div>
    <div>
      <upload-file
        ref="uploader"
        :is-single-file="true"
        :file-key="fileKey"
        @change="fileChange"
      ></upload-file>
      <div v-if="isShowError">
        {{ $t('文件内容存在错误，下载请点击')
        }}<span class="downs" @click="downloadResult">{{ $t('此处') }}</span>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import UploadFile from '@/components/Upload/cellUploadExCel.vue'
import Vue from 'vue'
import { i18n } from '@/main.js'
export default {
  components: {
    UploadFile
  },
  props: {
    fileKey: {
      type: String,
      default: 'importFile'
    },
    // 下载模板的组合参数
    downTemplateParams: {
      type: Object,
      default: () => {}
    },
    // 上传参数
    uploadParams: {
      type: Object,
      default: () => {}
    },
    /**
     * 请求地址：
     * requestUrls:
     *   templateUrlPre: purchaseRequest
     *   templateUrl, //模板地址（例如 downloadItemTemplate）；
     *   uploadUrl    //上传地址（例如 uploadRequestItem）
     */
    requestUrls: {
      type: Object,
      default: () => {}
    },
    downTemplateName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isShowError: false,
      resultExcel: null,
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      uploadData: null,
      fileLength: 0,
      isLoading: false
    }
  },
  methods: {
    handleClose() {
      this.uploadData = []
      this.isShowError = false
      this.$emit('closeUploadExcel')
    },
    fileChange(files, data) {
      console.log('fileChange--', files, data)
      this.fileLength = files.length
      this.uploadData = data
      if (!data) {
        this.isShowError = false
        this.resultExcel = null
      }
    },
    // 下载模板
    downloadTemplate() {
      console.error(this.requestUrls, 'this.downTemplateParams123')
      this.$API[this.requestUrls.templateUrlPre]
        [this.requestUrls.templateUrl](this.downTemplateParams)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          // const fileName = this.downTemplateName;
          download({ fileName: `${fileName}`, blob: res.data })
        })
    },

    // 下载上传结果（失败结果）
    downloadResult() {
      const fileName = getHeadersFileName(this.resultExcel)
      download({
        fileName: `${fileName}`,
        blob: this.resultExcel.data
      })
    },

    confirm() {
      if (!this.fileLength) {
        this.$toast({ content: this.$t('请选择上传文件!'), type: 'error' })
        return
      }
      if (this.uploadParams != null) {
        Object.keys(this.uploadParams).forEach((item) => {
          this.uploadData.append(item, this.uploadParams[item])
        })
      }
      let params = {
        rfxId: this.requestUrls.rfxId,
        data: this.uploadData
      }
      if (this.requestUrls.pointNo) {
        params = {
          pointNo: this.requestUrls.pointNo,
          data: this.uploadData
        }
      }
      if (this.requestUrls.pointId) {
        params = {
          pointId: this.requestUrls.pointId,
          data: this.uploadData
        }
      }
      if (this.requestUrls.itemCode) {
        params = {
          itemCode: this.requestUrls.itemCode,
          itemName: this.requestUrls.itemName,
          data: this.uploadData
        }
      }
      if (this.requestUrls.costModelId) {
        params = {
          costModelId: this.requestUrls.costModelId,
          data: this.uploadData
        }
      }
      if (this.requestUrls.id) {
        params = {
          id: this.requestUrls.id,
          data: this.uploadData
        }
      }
      this.isLoading = true
      let _routes = ['base-price', 'average-price', 'strike-price']
      if (this.$route && this.$route.name && _routes.includes(this.$route.name)) {
        this.$API[this.requestUrls.templateUrlPre]
          [this.requestUrls.uploadUrl](this.uploadData)
          .then((res) => {
            this.isLoading = false
            let _this = this
            if (res.data?.type === 'application/json') {
              const reader = new FileReader()
              reader.readAsText(res.data, 'utf-8')
              reader.onload = function () {
                try {
                  const readerRes = reader.result
                  const resObj = JSON.parse(readerRes)
                  if (resObj?.code == 200) {
                    _this.$emit('upExcelConfirm', resObj)
                    Vue.prototype.$toast({
                      content: '上传成功',
                      type: 'success'
                    })
                  } else {
                    Vue.prototype.$toast({
                      content: resObj.msg,
                      type: 'error'
                    })
                  }
                } catch (error) {
                  Vue.prototype.$toast({
                    content: i18n.t('响应数据格式错误，解析失败'),
                    type: 'error'
                  })
                  console.warn(error)
                }
              }
            } else {
              _this.$toast({ content: '导入错误，请重试！', type: 'warning' })
              this.isShowError = true
              this.resultExcel = res
            }
          })
      } else {
        this.$API[this.requestUrls.templateUrlPre]
          [this.requestUrls.uploadUrl](params)
          .then((res) => {
            this.isLoading = false
            let _this = this
            if (res.data?.type === 'application/json') {
              const reader = new FileReader()
              reader.readAsText(res.data, 'utf-8')
              reader.onload = function () {
                try {
                  const readerRes = reader.result
                  const resObj = JSON.parse(readerRes)
                  if (Array.isArray(resObj)) {
                    _this.$emit('upExcelConfirm', resObj)
                    return
                  }
                  if (resObj?.code == 200) {
                    _this.$emit('upExcelConfirm', resObj)
                    Vue.prototype.$toast({
                      content: '上传成功',
                      type: 'success'
                    })
                  } else {
                    Vue.prototype.$toast({
                      content: resObj.msg,
                      type: 'error'
                    })
                  }
                } catch (error) {
                  Vue.prototype.$toast({
                    content: i18n.t('响应数据格式错误，解析失败'),
                    type: 'error'
                  })
                  console.warn(error)
                }
              }
            } else {
              _this.$toast({ content: '导入错误，请重试！', type: 'warning' })
              this.isShowError = true
              this.resultExcel = res
            }
          })
      }
    }
  }
}
</script>

<style lang="scss">
.upload-excel-dialog {
  .tips {
    margin-bottom: 15px;
  }
  .downs {
    margin-left: 10px;
    color: #005ca9;
    cursor: pointer;
  }
}
</style>
