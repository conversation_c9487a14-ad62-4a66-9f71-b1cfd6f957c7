<template>
  <mt-select
    ref="filterSelectRef"
    @change="changeEmit"
    @focus="focusEmit"
    :value="privateValue"
    :allow-filtering="true"
    :filtering="doGetDataSource"
    :data-source="dataSource"
    :float-label-type="floatLabelType"
    :placeholder="placeholder"
    :show-clear-button="showClearButton"
    :disabled="disabled"
    :fields="fields"
    :value-template="valueTemplate"
    :open-dispatch-change="openDispatchChange"
    :width="width"
    :popup-width="popupWidth"
  ></mt-select>
</template>

<script>
import Vue from 'vue'
import { utils } from '@mtech-common/utils'
const valueVue = Vue.component('valueTemplate', {
  template: `<div class="user-value-template mt-flex-direction-column">
    <div class="user-name">{{data.name||data.employeeName}}</div>
  </div>`,
  data() {
    return { data: {} }
  }
})
export default {
  data() {
    return {
      doGetDataSource: () => {},
      privateValue: null,
      valueTemplate: function () {
        return { template: valueVue }
      }
    }
  },
  mounted() {
    this.doGetDataSource = utils.debounce(this.request, 1000)
  },
  props: {
    // debounce 执行的请求函数
    request: {
      type: Function,
      default: () => {
        return () => {}
      }
    },
    openDispatchChange: {
      type: Boolean,
      default: true
    },
    // 值
    value: {
      // eslint-disable-next-line
      type: String | Number | Boolean,
      default: () => {
        return null
      }
    },
    // 占位符
    placeholder: {
      type: String,
      default: () => {
        return ''
      }
    },
    // 指定是显示还是隐藏文本框中的清除图标
    showClearButton: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    disabled: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    // placeholder的浮动行为
    floatLabelType: {
      type: String,
      default: () => {
        return 'Never'
      }
    },
    // 字段配置
    fields: {
      type: Object,
      default: () => {
        return {
          // 映射列表中每个列表项的文本
          text: 'text',
          // 映射列表中每个列表项的value
          value: 'value',
          // 映射列表中每个列表项的icon
          iconCss: 'iconCss',
          // 映射列表中分组
          groupBy: 'groupBy'
        }
      }
    },
    dataSource: {
      type: Array,
      default: () => {
        return []
      }
    },
    width: {
      type: [Number, String],
      default: () => {
        return 400
      }
    },
    popupWidth: {
      type: String,
      default: () => {
        return '100%'
      }
    }
  },
  watch: {
    value: {
      handler(value) {
        this.privateValue = value
      },
      immediate: true
    }
  },
  methods: {
    changeEmit(e) {
      console.log('抛出---1', e)
      this.privateValue = e.value
      this.$emit('input', this.privateValue)
      this.$emit('change', e)
    },
    focusEmit(e) {
      this.$emit('focus', e)
    }
  }
}
</script>
<style lang="scss" scoped></style>
