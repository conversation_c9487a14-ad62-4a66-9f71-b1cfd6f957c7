<template>
  <div class="cell-file">
    <div v-if="handleable">
      <span class="cursor" v-if="value.length" @click="upload"
        >{{ $t('查看') }}({{ value.length }})</span
      >
      <span class="cursor" v-else @click="upload">{{ $t('点击上传') }}</span>
    </div>
    <div v-else>
      <span class="cursor" v-if="value.length" @click="view"
        >{{ $t('查看') }}({{ value.length }})</span
      >
      <span v-else>{{ $t('暂无附件') }}</span>
    </div>
    <!-- 附件弹框 -->
    <uploader-dialog @change="change" @confirm="confirm" ref="uploaderDialog"></uploader-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { i18n } from '@/main.js'
// 草稿或审批拒绝状态才能再次上传
export default {
  components: {
    UploaderDialog: () => import('./uploaderDialog') //优化dialog初始加载问题
  },
  data() {
    return {
      dialogShow: false,
      uploadFileList: [] // 上传的附件
    }
  },
  computed: {
    field() {
      // 字段
      return this.params.column.colId
    },
    value() {
      // 值
      let _value = this.params.data[this.field]
      return _value ? (Array.isArray(_value) ? _value : JSON.parse(_value)) : []
    },
    handleable() {
      // 是否可编辑
      return this.params.colDef?.cellRendererParams?.handleable
    },
    isTransform() {
      // 是否需要转换数据
      return this.params.colDef?.cellRendererParams?.isTransform
    }
  },
  mounted() {
    this.uploadFileList = cloneDeep(this.value)
  },
  methods: {
    // handler - 查看
    view() {
      this.showUploadDialog('view')
    },
    // handler - 上传
    upload() {
      this.showUploadDialog('upload')
    },
    // dialog - show
    showUploadDialog() {
      const dialogParams = {
        fileData: cloneDeep(this.value),
        isView: this.handleable ? false : true, //是否可上传
        required: false, // 是否必须
        title: i18n.t('附件')
      }
      this.$refs.uploaderDialog?.dialogInit(dialogParams)
    },

    // dialog - hander - change
    change(data) {
      if (!this.handleable) return
      this.uploadFileList = data
    },
    // dialog - hander - confirm
    confirm() {
      if (!this.handleable) return
      let _uploadFileList = cloneDeep(this.uploadFileList)
      if (this.isTransform) {
        _uploadFileList.forEach((file) => {
          if (!file.syFileId) {
            file.sysFileId = file.id
            delete file.id
          }
        })
        this.params.node.setDataValue(this.field, _uploadFileList)
        return
      }
      this.params.node.setDataValue(this.field, JSON.stringify(_uploadFileList))
    }
  }
}
</script>

<style lang="scss" scoped>
.cell-file span {
  color: #00469c;
  &.cursor {
    cursor: pointer;
  }
}
</style>
