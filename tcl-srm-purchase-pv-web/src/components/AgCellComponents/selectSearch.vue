<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <mt-input
        :id="fieldName"
        v-model="value"
        disabled
        :width="130"
        :placeholder="headerText"
      ></mt-input>
      <mt-icon
        style="width: 20px"
        name="icon_list_refuse"
        @click.native="handleClear"
        v-if="allowEditing"
      ></mt-icon>
      <mt-icon
        v-if="allowEditing"
        style="width: 20px"
        name="icon_input_search"
        @click.native="showDialog"
      ></mt-icon>
    </div>
  </div>
</template>

<script>
import { getValueByPath } from '@/utils/obj'
import { cloneDeep } from 'lodash'
const fieldMap = {
  itemId: 'id',
  itemName: 'itemName',
  spec: 'itemDescription', // 规格描述
  material: 'materialTypeName', // 材质
  unitName: 'baseMeasureUnitName', // 单位
  categoryId: 'categoryResponse.categoryId', //品类名称
  categoryCode: 'categoryResponse.categoryCode', //品类名称
  categoryName: 'categoryResponse.categoryName', //品类名称
  priceUnitName: 'purchaseUnitName' //采购单位
}

export default {
  model: {
    prop: 'modelVal',
    event: 'syncChange'
  },
  props: {
    modelVal: {
      type: [String, Object, Number],
      default: null
    },
    params: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      headerText: '',
      allowEditing: true
    }
  },
  computed: {
    headerInfo() {
      // 头相关信息
      return this.params.colDef?.cellEditorParams?.editConfig?.props || {}
    },
    fieldName() {
      return this.params.colDef?.fieldCode || this.params.colDef?.field
    },
    value: {
      get() {
        return this.modelVal
      },
      set(val) {
        this.$emit('syncChange', val)
      }
    }
  },
  mounted() {},
  methods: {
    // btn - clear
    handleClear() {
      this.value = ''
      Object.entries(fieldMap).map(([field]) => {
        field !== 'priceUnitName' && this.params.node.setDataValue(field, null) // *
      })
    },
    // btn - show
    showDialog() {
      if (this.fieldName === 'itemCode') {
        this.showDialogItemCode()
      } else {
        this.showDialogReferItemCode()
      }
    },
    // showDialog - 物料
    showDialogItemCode() {
      const { organizationId, siteCode, categoryCode, rfxItemId } = this.params.data
      if (!organizationId || !siteCode) {
        this.$toast({
          content: this.$t('当前未选择工厂，不可以选择物料'),
          type: 'warning'
        })
        return
      }
      // this.$dialog({
      //   modal: () => import('COMPONENTS/NormalEdit/tempSelectItemCode/selectGrid'),
      //   data: {
      //     title: this.$t('选择物料'),
      //     rfxId: this.headerInfo.rfxId,
      //     siteParam: { organizationId, siteCode },
      //     sourcingType: this.headerInfo.sourcingType,
      //     source: this.headerInfo.source,
      //     categoryCode: categoryCode
      //   },
      //   success: (data) => {
      //     this.value = data[0].itemCode
      //     Object.entries(fieldMap).map(([field, key]) => {
      //       this.params.node.setDataValue(field, getValueByPath(data[0], key))
      //     })
      //     this.updateItemCode(data[0], rfxItemId)
      //   }
      // })
    },
    // showDialog - 参考物料编码
    showDialogReferItemCode() {
      if (this.params.data?.itemExtMap?.referChannel !== 2) {
        this.$toast({
          content: '仅价格来源为价格记录，可选择参考物料',
          type: 'warning'
        })
        return
      }
      this.showSelect = false
      let stepQuoteType = -1
      if (this.params.data?.stepQuoteType || this.params.data?.stepQuoteType === 0) {
        stepQuoteType = this.params.data?.stepQuoteType
      }
      // this.$dialog({
      //   modal: () =>
      //     import('COMPONENTS/NormalEdit/selectReferItem/components/selectGrid/index.vue'),
      //   data: {
      //     title: this.$t('选择参考物料'),
      //     stepQuoteType,
      //     companyCode: this.headerInfo.companyCode,
      //     isAgGrid: true
      //   },
      //   success: async (data) => {
      //     this.value = this.fieldName === 'referItemCode' ? data[0].itemCode : data[0].itemName
      //     const itemExtMap = {
      //       referItemCode: data[0]['itemCode'],
      //       referItemName: data[0]['itemName']
      //     }
      //     let stageList = {
      //       // extStageSaveRequests:'',
      //       // itemStageList:'',
      //       // stepQuoteName:''
      //     }
      //     if (this.headerInfo.sourcingObjType === 'module_out_buy') {
      //       const referItemExtFields = [
      //         { field: 'refScreenCode', valueField: 'screenCode' },
      //         { field: 'refScreenPrice', valueField: 'screenPrice' },
      //         { field: 'refTconCode', valueField: 'tconCode' },
      //         { field: 'refTconPrice', valueField: 'tconPrice' },
      //         { field: 'refTotalPrice', valueField: 'totalPrice' }
      //       ]
      //       referItemExtFields.forEach((item) => {
      //         itemExtMap[item.field] = data[0][item.valueField]
      //       })
      //     }
      //     if (this.params.data?.stepQuoteType !== -1) {
      //       const referList = await this.getPriceReferList(data)
      //       stageList.extStageSaveRequests = cloneDeep(referList)
      //       const arr = ['power_panel', 'module_out_going']
      //       if (
      //         arr.includes(this.headerInfo.sourcingObjType) &&
      //         this.params.data?.stepQuote === 1
      //       ) {
      //         const itemStageList = []
      //         referList.forEach((item) => {
      //           itemStageList.push({ startValue: item.startValue, remark: '' })
      //         })
      //         stageList.itemStageList = itemStageList
      //         stageList.stepQuoteName = JSON.stringify(itemStageList)
      //       }
      //     }
      //     let rowData = this.params.data
      //     rowData.itemExtMap = Object.assign({}, rowData.itemExtMap, itemExtMap)
      //     Object.assign(rowData, stageList)
      //     this.params.node.setData(rowData)
      //   },
      //   close: () => {}
      // })
    },
    // 获取参考价格记录
    async getPriceReferList(data) {
      const params = {
        page: { current: 1, size: 999 },
        type: 2,
        defaultRules: [],
        rules: [
          {
            label: this.$t('公司编码'),
            field: 'companyCode',
            type: 'string',
            operator: 'equal',
            value: this.headerInfo.companyCode
          },
          {
            label: this.$t('阶梯类型'),
            field: 'stageType',
            type: 'string',
            operator: 'equal',
            value: data[0].stepQuoteType
          },
          {
            label: this.$t('价格分类'),
            field: 'priceClassify',
            type: 'number',
            operator: 'equal',
            value: 0
          },
          {
            label: this.$t('分组编码'),
            field: 'priceGroupCode',
            type: 'string',
            operator: 'equal',
            value: data[0].priceGroupCode
          }
        ]
      }
      const res = await this.$API.rfxList.getPriceReferList(params)
      const arr = []
      res.data.records.map((item) => {
        const { itemCode, itemName, referChannel, untaxedUnitPrice, stepValue, deliveryPlace } =
          item
        arr.push({
          referItemName: itemName,
          referItemCode: itemCode,
          referChannel,
          referItemUnitPriceUntaxed: untaxedUnitPrice,
          deliveryPlace,
          startValue: stepValue
        })
      })
      return arr
    },
    // confirm - 确认更新物料信息
    updateItemCode(data, rfxItemId) {
      let _params = {
        rfxId: this.headerInfo.rfxId,
        rfxItemId: rfxItemId,
        itemId: data.id,
        itemCode: data.itemCode,
        itemName: data.itemName,
        unitCode: data.unitCode || data.baseMeasureUnitCode,
        unitName: data.unitName || data.baseMeasureUnitName
      }
      this.$API.rfxExt.updateItemCodeByRfxItemId(_params).then(() => {
        this.$bus.$emit(`refreshPricingTab`)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
</style>
