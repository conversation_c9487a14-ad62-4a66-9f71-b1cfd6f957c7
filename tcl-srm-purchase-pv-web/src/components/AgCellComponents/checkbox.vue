<template>
  <mt-checkbox
    class="checkbox-item"
    :checked="value"
    @change="change"
    :disabled="!editable"
  ></mt-checkbox>
</template>

<script>
export default {
  computed: {
    field() {
      // 字段
      return this.params.column.colId
    },
    value() {
      // 值
      return this.params.data[this.field] === 'Y'
    },
    editable() {
      return this.params.colDef.editable
    }
  },
  methods: {
    change(e) {
      let _value = e.checked ? 'Y' : 'N'
      this.params.node.setDataValue(this.field, _value)
    }
  }
}
</script>
