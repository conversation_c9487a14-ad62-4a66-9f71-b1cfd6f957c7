<template>
  <CodeRener v-model="value" :params="params" @change="change"></CodeRener>
</template>
<script>
import CodeRener from './index'
export default {
  components: { CodeRener },
  model: {
    prop: 'modelVal',
    event: 'syncChange'
  },
  props: {
    modelVal: {
      type: [String, Object, Number],
      default: null
    }
  },
  data() {
    return {}
  },
  computed: {
    params() {
      return this.$attrs?.params || {}
    },
    value: {
      get() {
        return this.modelVal
      },
      set(val) {
        this.$emit('syncChange', val)
      }
    }
  },
  methods: {
    change(v) {
      this.value = v
    }
  }
}
</script>
<style lang="scss">
.desc-item {
  & > div {
    display: inline-block;
  }
}
</style>
