<template>
  <!-- 成本模型下拉组件 -->
  <div>
    <mt-select
      v-model="value"
      v-bind="$attrs"
      :allow-filtering="true"
      :data-source="dataSource"
      :filtering="filtering"
      :fields="{ text: 'costModelName', value: 'costModelName' }"
      @select="handleSelectChange"
      popup-width="180px"
    ></mt-select>
  </div>
</template>

<script>
import { cloneDeep, debounce } from 'lodash'
export default {
  model: {
    prop: 'modelVal',
    event: 'syncChange'
  },
  props: {
    modelVal: {
      type: [String, Object, Number],
      default: null
    }
  },
  data() {
    return {
      dataSelectList: [],
      dataSource: []
    }
  },
  computed: {
    value: {
      get() {
        return this.modelVal
      },
      set(val) {
        this.$emit('syncChange', val)
      }
    },
    params() {
      return this.$attrs?.params || {}
    },
    paramsInfo() {
      // props 传递的参数
      return this.params.colDef?.cellEditorParams?.editConfig?.props || {}
    },
    detailInfo() {
      return this.paramsInfo?.detailInfo || {}
    }
  },
  async mounted() {
    await this.initData()
  },
  methods: {
    // 获取成本模型下拉数据
    async initData(e = { text: '' }) {
      let params = {
        rfxId: this.paramsInfo.rfxId,
        categoryCode: this.params.data.categoryCode || '',
        companyCodeList: [this.detailInfo.companyCode || ''],
        relCode: this.params.data.itemCode || this.params.data.categoryCode,
        relationType: this.detailInfo.sourcingObjType === 'cost_factor' ? 2 : 1, // 品类:0、物料:1、行情因子:2
        page: { current: 1, size: 20 }
      }
      const res = await this.$API.costModel[
        !this.paramsInfo?.isKT ? 'queryRelCostModel' : 'queryRelKtCostModel'
      ](params).catch(() => {})
      if (res.code === 200) {
        this.dataSource = cloneDeep(res.data?.records)
        this.$nextTick(() => {
          if (e.updateData && typeof e.updateData == 'function') {
            e.updateData(this.dataSource)
          }
        })
      }
    },
    filtering: debounce(function (e) {
      this.initData(e)
    }, 1000),
    getOptions(val) {
      this.dataSelectList = cloneDeep(val)
    },
    // select 监听
    handleSelectChange(e) {
      const data = this.dataSource.find((item) => item.costModelCode === e.itemData.costModelCode)
      const rowData = Object.assign({}, this.params?.node?.data, {
        costModelId: data.id,
        costModelCode: data.costModelCode,
        costModelName: data.costModelName,
        costModelVersionCode: data.versionCode
      })
      this.params.node.setData(rowData)
    }
  }
}
</script>
