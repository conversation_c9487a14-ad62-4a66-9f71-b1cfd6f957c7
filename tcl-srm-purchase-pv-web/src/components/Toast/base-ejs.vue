<template>
  <transition name="fade">
    <ejs-toast
      ref="toastRef"
      :position="position"
      :show-close-button="showCloseButton"
      :time-out="timeout"
      @beforeClose="destroyedToast"
      :style="toastContainerStyle"
    ></ejs-toast>
  </transition>
</template>

<script>
const lang = localStorage.getItem('internationlization')
const changeLang = (lang) => {
  let _langMap = {
    zh: '提示',
    en: 'hint',
    vn: 'gợi ý',
    esp: 'sugerencia'
  }
  return _langMap[lang || 'zh']
}

export default {
  name: 'Toast',
  props: {
    title: {
      type: String,
      default: changeLang(lang)
    },
    content: {
      type: String,
      default: ''
    },
    cssClass: {
      type: String,
      default: 'e-toast-warning'
    },
    icon: {
      type: String,
      default: 'e-warning toast-icons'
    },
    type: { type: String, default: 'warning' },
    position: {
      type: Object,
      default: () => {
        return { X: 'Right', Y: 'Bottom' }
      }
    },
    showCloseButton: {
      type: Boolean,
      default: true
    },
    timeout: {
      type: Number,
      default: 3000
    },
    verticalOffset: {
      type: Number,
      default: 10
    }
  },
  data() {
    return {
      style: {
        warning: {
          cssClass: 'e-toast-warning',
          icon: 'e-warning toast-icons'
        },
        success: {
          cssClass: 'e-toast-success',
          icon: 'e-success toast-icons'
        },
        info: {
          cssClass: 'e-toast-info',
          icon: 'e-info toast-icons'
        },
        error: {
          cssClass: 'e-toast-danger',
          icon: 'e-error toast-icons'
        }
      }
    }
  },
  computed: {
    toastContainerStyle() {
      return this.position.Y === 'Bottom'
        ? {
            bottom: `${this.verticalOffset}px`
          }
        : {
            top: `${this.verticalOffset}px`
          }
    }
  },
  mounted() {
    this.$refs.toastRef.show({
      title: this.title,
      content: this.content,
      cssClass: this.style[this.type]['cssClass'],
      icon: this.style[this.type]['icon']
    })
  },
  methods: {
    destroyedToast() {
      this.$emit('close')
    }
  },
  beforeDestroy() {
    this.$emit('close')
  }
}
</script>

<style lang="scss" scoped></style>
