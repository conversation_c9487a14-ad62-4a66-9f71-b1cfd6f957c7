/**
 * 错误处理 mixin
 * 提供统一的错误处理机制
 */
export default {
  methods: {
    /**
     * 处理 API 请求错误
     * @param {Error} error - 错误对象
     * @param {string} message - 错误提示信息
     */
    handleApiError(error, message) {
      console.error(message, error)
    },

    /**
     * 处理业务逻辑错误
     * @param {string} message - 错误提示信息
     * @param {string} type - 提示类型，默认为 warning
     */
    handleBusinessError(message, type = 'warning') {
      this.$toast({
        content: this.$t(message),
        type
      })
    },

    /**
     * 显示确认对话框
     * @param {string} action - 操作名称
     * @param {Function} callback - 确认后的回调函数
     */
    showConfirmDialog(action, callback) {
      return this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认${action}？`)
        },
        success: () => {
          if (typeof callback === 'function') {
            callback()
          }
        }
      })
    }
  }
}
