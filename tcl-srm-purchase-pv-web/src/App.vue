<template>
  <div id="purchasePvApp" v-if="show">
    <mt-loading v-show="loading"></mt-loading>
    <keep-alive>
      <router-view :key="$route.fullPath"></router-view>
    </keep-alive>
    <!-- <router-view v-if="!isKeepAlive($router)"></router-view> -->
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: 'App',
  provide() {
    return {
      reload: () => {
        this.reload()
      }
    }
  },
  data() {
    return {
      show: true
    }
  },
  created() {
    this.$nextTick(() => {
      this.getUserInfo()
    })
  },
  computed: {
    ...mapState(['loading'])
  },
  methods: {
    getUserInfo() {
      // 获取当前登录的用户
      this.$API.iamService.queryUserInfo().then((res) => {
        if (res?.data) {
          sessionStorage.setItem('userInfo', JSON.stringify(res.data))
          this.$store.commit('updateUserInfo', res.data)
        }
      })
    },
    reload() {
      this.show = false
      this.$nextTick(() => {
        this.show = true
      })
    }
    // isKeepAlive(route) {
    //   console.log("router--change", route);
    //   let flag = false;
    //   if (
    //     route.currentRoute &&
    //     route.currentRoute.meta &&
    //     route.currentRoute.meta.keepAlive
    //   ) {
    //     flag = true;
    //   }
    //   return flag;
    // },
  }
}
</script>

<style>
#purchasePvApp {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
