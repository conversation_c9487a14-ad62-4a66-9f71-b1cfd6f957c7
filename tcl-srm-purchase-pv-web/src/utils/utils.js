const utils = {}

//通过selectRef获取数据，并赋值其他字段。常用场景：form提交时，使用下拉框数据的其他字段
utils.assignDataFromRef = (formObject, key, ref, fields) => {
  const assignData = (_select, _form, _fields) => {
    for (let i in _fields) {
      // formField: sourceField
      _form[i] = _select[fields[i]]
    }
  }
  if (formObject[key]) {
    //根据当前选择的key，获取整个key对应的Select对象
    let _select = ref?.getDataByValue(formObject[key])
    if (typeof fields === 'string') {
      //只赋值一个字段
      formObject[fields] = _select[fields]
    } else if (Array.isArray(fields)) {
      fields.forEach((e) => {
        if (typeof e === 'string') {
          //formField与sourceField一致时
          formObject[e] = _select[e]
        } else {
          assignData(_select, formObject, e)
        }
      })
    } else {
      assignData(_select, formObject, fields)
    }
  }
}
//通过selectRef获取数据，并赋值其他字段。常用场景：form提交时，使用下拉框数据的其他字段
utils.assignDataFromRefs = (formObject, fields) => {
  //判断fields是不是一个数组，如果是一个数组返回true，否则返回false
  if (Array.isArray(fields)) {
    fields.forEach((e) => {
      utils.assignDataFromRef(formObject, e['key'], e['ref'], e['fields'])
    })
  }
}
utils.formatTime = (date, fmt = 'Y-m-d') => {
  if (!date) {
    return
  }
  let ret
  // YYYY/mm/dd HH:MM:SS
  const opt = {
    'Y+': date.getFullYear().toString(),
    'm+': (date.getMonth() + 1).toString(),
    'd+': date.getDate().toString(),
    'H+': date.getHours().toString(),
    'M+': date.getMinutes().toString(),
    'S+': date.getSeconds().toString()
  }
  for (let k in opt) {
    ret = new RegExp('(' + k + ')').exec(fmt)
    if (ret) {
      fmt = fmt.replace(ret[1], ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, '0'))
    }
  }
  return fmt
}

export const formatTime = utils.formatTime

utils.dataFormat = (value, format = 2) => {
  var minus = false
  if (isNaN(value)) return '-'
  if (value == null || !value) return '-'
  if (value.toString().indexOf('-') > -1) {
    value = value.toString().substring(1)
    minus = true
  }

  let s = parseFloat((value + '').replace(/[^\d\.-]/g, '')).toFixed(format) + '' // eslint-disable-line
  let l = s.split('.')[0].split('').reverse(),
    r = s.split('.')[1]
  let f = ''
  for (let i = 0; i < l.length; i++) {
    f += l[i] + ((i + 1) % 3 == 0 && i + 1 != l.length ? ',' : '')
  }
  if (minus) return '-' + f.split('').reverse().join('') + (format > 0 ? '.' + r : '')
  return f.split('').reverse().join('') + (format > 0 ? '.' + r : '')
}

import Vue from 'vue'
// 通过 a 标签下载文件
export const download = (data) => {
  const { fileName, blob } = data
  if (!blob) {
    return
  }

  if (blob?.type === 'application/json') {
    const reader = new FileReader()
    reader.readAsText(blob, 'utf-8')
    reader.onload = function () {
      const readerRes = reader.result
      const resObj = JSON.parse(readerRes)
      Vue.prototype.$toast({
        content: resObj.msg,
        type: 'error'
      })
    }

    return
  }
  const a = document.createElement('a')
  a.href = URL.createObjectURL(blob)
  a.style.display = 'none'
  a.download = fileName
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
}

utils.download = download
utils.randomString = (e = 20) => {
  var t = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789',
    a = t.length,
    n = ''
  for (let i = 0; i < e; i++) {
    n += t.charAt(Math.floor(Math.random() * a))
  }
  return n
}

utils.toQueryParams = (params) => {
  const keys = Object.keys(params)
  let queryParams = []

  keys.forEach((k) => {
    queryParams.push(`${k}=${params[k]}`)
  })

  return queryParams.join('&')
}

utils.toChinesNum = (num) => {
  let changeNum = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'] //changeNum[0] = "零"
  let unit = ['', '十', '百', '千', '万']
  num = parseInt(num)
  let getWan = (temp) => {
    let strArr = temp.toString().split('').reverse()
    let newNum = ''
    for (var i = 0; i < strArr.length; i++) {
      newNum =
        (i == 0 && strArr[i] == 0
          ? ''
          : i > 0 && strArr[i] == 0 && strArr[i - 1] == 0
          ? ''
          : changeNum[strArr[i]] + (strArr[i] == 0 ? unit[0] : unit[i])) + newNum
    }
    return newNum
  }
  let overWan = Math.floor(num / 10000)
  let noWan = num % 10000
  if (noWan.toString().length < 4) noWan = '0' + noWan
  let _res = overWan ? getWan(overWan) + '万' + getWan(noWan) : getWan(num)
  if (_res.length == 3 && _res.split('')[0] == '一') {
    return _res.substring(1)
  }
  return _res
}
/**
 * @description: formatRules，序列化后端返回的表单校验规则
 * @param
    {
      "Name": {
      "required": [true, "请输入"],
      "email": [true, "请输入正确的email"],
      "url": [true, "请输入正确的url"],
      "date": [true, "请输入正确的date"],
      "dateIso": [true, "请输入正确的dateIso"],
      "number": [true, "请输入number"],
      "maxLength": [2, "最大长度2"],
      "minLength": [2, "最小长度2"],
      "rangeLength": [[1, 5], "长度在1-5个之间"],
      "range": [[1, 5], "1-5之间的数字"],
      "max": [5, "小于5的数字"],
      "min": [5, "大于5的数字"],
      "regex": ["^[A-z]+$", "字母"]
      }
    }
 * @return
  {
    Name: [
      {
        required: true,
        message: "请输入",
        trigger: "blur",
      },
      {
        type: "email",
        message: "请输入正确的email",
        trigger: "blur",
      },
      {
        type: "url",
        message: "请输入正确的url",
        trigger: "blur",
      },
      {
        type: "date",
        message: "请输入正确的date",
        trigger: "blur",
      },
      {
        type: "dateIso",
        message: "请输入正确的dateIso",
        trigger: "blur",
      },
      {
        type: "number",
        message: "请输入number",
        trigger: "blur",
      },
      {
        maxLength: 2,
        message: "最大长度2",
        trigger: "blur",
      },
      {
        minLength: 2,
        message: "最小长度2",
        trigger: "blur",
      },
      {
        rangeLength: [1, 5],
        message: "长度在1-5个之间",
        trigger: "blur",
      },
      {
        range: [1, 5],
        message: "1-5之间的数字",
        trigger: "blur",
      },
      {
        max: 5,
        message: "小于5的数字",
        trigger: "blur",
      },
      {
        min: 5,
        message: "大于5的数字",
        trigger: "blur",
      },
      {
        regex: "^[A-z]+$",
        message: "字母",
        trigger: "blur",
      },
    ],
  }
 */
utils.formatRules = (rules) => {
  if (Object.prototype.toString.call(rules) != '[object Object]') {
    return {}
  }
  let res = {}
  for (var i in rules) {
    let _oneRule = []
    for (var j in rules[i]) {
      if (typeof rules[i][j][0] == 'boolean' && j != 'required') {
        _oneRule.push({
          type: j,
          message: rules[i][j][1],
          trigger: 'blur'
        })
      } else {
        _oneRule.push({
          [j]: rules[i][j][0],
          message: rules[i][j][1],
          trigger: 'blur'
        })
      }
    }
    res[i] = _oneRule
  }
  return res
}
/**
 * 获取请求头中的文件名称
 * @param data data: { headers: content-disposition: "<文件名信息>" }
 * @returns String
 */
export const getHeadersFileName = (data) => {
  const contentDisposition = data?.headers['content-disposition'] || ''
  const prefix = "attachment;filename*=utf-8'zh_cn'"
  const prefix1 = 'attachment; filename='
  const prefix2 = 'attachment;filename='
  let fileName = contentDisposition
  if (contentDisposition.indexOf(prefix) === 0) {
    fileName = decodeURI(contentDisposition.slice(prefix.length))
  } else if (contentDisposition.indexOf(prefix1) === 0) {
    fileName = decodeURI(contentDisposition.slice(prefix1.length))
  } else if (contentDisposition.indexOf(prefix2) === 0) {
    fileName = decodeURI(contentDisposition.slice(prefix2.length))
  }

  return fileName
}

utils.getHeadersFileName = getHeadersFileName

const isString = (obj) => Object.prototype.toString.call(obj) === '[object String]'
const isEmpty = (obj) => {
  /* eslint-disable */
  if (obj == null) return true
  if (Array.isArray(obj) || isString(obj)) return obj.length === 0
  for (let key in obj) {
    if (obj.hasOwnProperty(key)) return false
  }
  return true
}

utils.isEmpty = isEmpty

export const getPurchasePv = (code) => {
  const dict = JSON.parse(sessionStorage.getItem('purchasePvDictionary')) || {}
  if (code && !isEmpty(dict)) {
    return dict[code]
  } else {
    return []
  }
}

utils.getPurchasePv = getPurchasePv

export default utils
/**
 * Remove class from element
 * @param {Array} list  要遍历的数组
 * @param {string | number} val 要寻找的值
 * @param {string} labelKey 对象label名，默认'label'
 * @param {string | number} valKey 对象value名，默认'value'
 * @param {string} equalFlag 用于标识强判等，'strong'表示'==='，'weak'表示'=='，默认'strong'（为'weak'时，主要解决value值为number类型，过滤器显示的列表值与表中数据不一致处理不生效的问题）
 */
export function valueMap(list, val, labelKey = 'text', valKey = 'value', equalFlag = 'strong') {
  if (!Array.isArray(list)) return null
  let item = null
  if (equalFlag === 'weak') {
    item = list.find((el) => val == el[valKey])
  } else {
    item = list.find((el) => val === el[valKey])
  }
  if (item) {
    return item[labelKey]
  } else {
    return null
  }
}

export function arrValues(list, valKey = 'value') {
  if (!Array.isArray(list)) return null
  const arr = list.map((item) => item[valKey])
  return arr
}

// 显示 code+name
export const codeNameColumn = (args) => {
  const { firstKey, secondKey, thirdKey, fourthKey, cssClass } = args
  const template = () => {
    return {
      template: Vue.component('codeNameColumnComponent', {
        template: `
          <div style="display: flex; align-items: center; height: 100%;">
            <div :class="['text-ellipsis', cssClass]">{{theColumnData}}</div>
          </div>
        `,
        data: function () {
          return {
            data: {},
            firstKey,
            secondKey,
            thirdKey,
            fourthKey,
            cssClass,
            theColumnData: ''
          }
        },
        watch: {
          data: {
            handler(newVal) {
              // 设置数据格子的值
              this.setTheColumnData(newVal)
            },
            immediate: true
          }
        },
        mounted() {
          // 设置数据格子的值
          this.setTheColumnData(this.data)
        },
        beforeDestroy() {},
        methods: {
          // 设置数据格子的值
          setTheColumnData(data) {
            if (data[firstKey] && data[secondKey] && data[thirdKey] && data[fourthKey]) {
              // firstKey-secondKey-thirdKey-fourthKey
              this.theColumnData = `${data[firstKey]}-${data[secondKey]}-${data[thirdKey]}-${data[fourthKey]}`
            } else if (data[firstKey] && data[secondKey] && data[thirdKey]) {
              // firstKey-secondKey-thirdKey
              this.theColumnData = `${data[firstKey]}-${data[secondKey]}-${data[thirdKey]}`
            } else if (data[firstKey] && data[secondKey]) {
              // firstKey-secondKey
              this.theColumnData = `${data[firstKey]}-${data[secondKey]}`
            } else if (data[firstKey]) {
              // firstKey
              this.theColumnData = data[firstKey]
            } else if (data[secondKey]) {
              // secondKey
              this.theColumnData = data[secondKey]
            } else {
              // ""
              this.theColumnData = ''
            }
          }
        }
      })
    }
  }
  return template
}
