/**
 * @param {Object} target
 * @param {String} key
 * @returns
 */
export const getValueByPath = function (target, key) {
  if (!target || typeof target !== 'object' || typeof key !== 'string') {
    console.warn('getValueByPath', { target, key })
    return
  }
  let result = target
  for (const k of key.split('.')) {
    result = result[k]
    if (result === undefined || result === null) {
      return result
    }
  }
  return result
}

/**
 *
 * @param {Object} target
 * @param {String} key
 * @param {*} value
 * @returns
 */
export const setValueByPath = function (target, key, value) {
  if (!target || typeof target !== 'object' || typeof key !== 'string') {
    console.warn('setValueByPath', { target, key, value })
    return
  }
  let result = target
  const keys = key.split('.')
  for (let i = 0; i < keys.length; i++) {
    if (result[keys[i]] === undefined || result[keys[i]] === null) {
      result[keys[i]] = {}
    }
    if (i === keys.length - 1) {
      result[keys[i]] = value
    } else {
      result = result[keys[i]]
    }
  }
  return target
}

/**
 *
 * @param {date} stringify
 * @returns
 */
export const getEndDate = function (date) {
  let _end = ''
  let currentYear = new Date(date).getFullYear()
  // let dateTime = new Date(date).getTime()
  let judgeTime = new Date(currentYear + '-07-01').getTime()
  if (date < judgeTime) {
    _end = currentYear + '-12-31' // 当年12-31
  } else {
    _end = currentYear + 1 + '-12-31' // 次年12-31
  }

  return new Date(_end).getTime()
}
/**
 *
 * @param {diffTime} number
 * @returns
 */
export const getTimeList = function (diffTime) {
  let _date = new Date()
  _date.setMonth(_date.getMonth() - diffTime)
  _date.setDate(1)
  return [_date, new Date()]
}
