import Vue from 'vue'
const codeList = [
  { code: 'QUOTA_STRATEGY_LW_WD', type: 'string' }, // 例外维度
  { code: 'QUOTA_BIAS_TYPE', type: 'number' }, // 物控反馈偏差类型
  { code: 'QUOTA_ITEM_ADJUST_TYPE', type: 'string' }, // 调整类型
  { code: 'QUOTA_STRATEGY_RANKING_TYPE', type: 'string' }, // 排名类型
  { code: 'QUOTA_BIAS_STATUS', type: 'number' }, // 配额偏差状态
  { code: 'QUOTA_STRATEGY_PELW_TYPE', type: 'string' }, // 例外类型
  { code: 'QUOTA_STRATEGY_RANKING_PRIORITY', type: 'string' }, // 优先级
  { code: 'QUOTA_STRATEGY_SHORT_STATUS', type: 'number' }, // 配额状态（无需审批）
  { code: 'QUOTA_STRATEGY_XY_TYPE', type: 'string' }, // 单据类型
  { code: 'QUOTA_ITEM_CUSTOMIZATION_FLAG', type: 'string' }, // 是否自制配额
  { code: 'QUOTA_STRATEGY_STATUS', type: 'number' }, // 配额单据状态
  { code: 'QUOTA_ITEM_COMMIT_STATUS', type: 'number' }, // 确认状态
  { code: 'QUOTA_STRATEGY_CONSTRAINT_TYPE', type: 'string' }, // 限制类型（限制配额）
  { code: 'QUOTA_STRATEGY_CONSTRAINT_SYMBOL', type: 'string' }, // 限制符号（限制配额）
  { code: 'QUOTA_STRATEGY_CONSTRAINT_LEVEL', type: 'string' }, // 限制等级
  { code: 'QUOTA_STRATEGY_XZPEYXJ_TYPE', type: 'string' }, // 限制类型（优先级设置及放行供应商类型）
  { code: 'QUOTA_ITEM_UN_COMMIT_TYPE', type: 'number' }, // 未自动筛选原因
  { code: 'QUOTA_ITEM_SUBMIT_STATUS', type: 'number' }, // 提交审批状态
  { code: 'QUOTA_STRATEGY_FX_WD', type: 'string' }, // 放行维度
  { code: 'QUOTA_STRATEGY_PRIORITY_IS_DISTRIBUTE', type: 'number' }, // 是否可再分配
  { code: 'SPECIAL PUR TYPE', type: 'string' }, // 特殊采购/报价属性
  { code: 'PROCUREMENT TYPE', type: 'string' }, // 	采购类型
  { code: 'payMethod', type: 'string' }, // 	付款类型
  { code: 'PaymentType', type: 'string' } // 	付款方式
]
export default function () {
  Vue.prototype.$API.masterData.getNumDictListAllByCode(codeList).then((res) => {
    const { code, data } = res
    if (code === 200) {
      sessionStorage.setItem('purchasePvDictionary', JSON.stringify(data))
    }
  })
}
