/**
 *  select 组件 filtering 事件修复
 * 中文输入后鼠标点击会改变输入框的值, 但是并不会触发 filtering
 * (B20220607480172 输入cny后鼠标点击后不搜索，快捷键搜索)
 * @param {*} fn
 * @returns
 */
export function useFiltering(fn) {
  return function (e) {
    const that = this
    e.baseEventArgs.target.oninput = (ev) => {
      if (ev.target.value !== e.text) {
        e.text = ev.target.value
        that.trigger('filtering', e)
      }
    }
    fn.call(this, e)
  }
}

/**
 * 创建通用本地过滤方法
 * @param {string} field
 * @returns
 */
export function createFiltering(field) {
  return useFiltering(function (e) {
    if (!e.text) {
      e.updateData(this.dataSource)
    } else {
      e.updateData(this.dataSource.filter((row) => row[field].indexOf(e.text) > -1))
    }
  })
}
