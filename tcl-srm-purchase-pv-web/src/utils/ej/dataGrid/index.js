/**
 * DataGrid 组件相关工具方法
 */

// 行内编辑
// const editInstance = createEditInstance();
// const columnData = [
//   {
//     field: "datetime",
//     headerText: "datetime",
//     edit: editInstance.create({
//       getEditConfig: ({ column, rowData }) => ({
//         // 根据配置生成组件, 除了 type 其他字段都作为属性传入创建的组件
//         type: "mt-date-time-picker", // 表单类型 | 组件名 | tagName
//         format: "yyyy-MM-dd HH:mm:ss",
//       }),
//     }),
//   },
// ];

// 监听 input 事件
// editInstance.onInput((ctx, { field }) => {
//   ctx.rowData; // 获取最新的行数据

//   ctx.getValueByField(field); // 获取 field 字段的最新值
//   ctx.setValueByField(field, "newValue"); // 设置 field 字段值, 并更新行内编辑组件

//   ctx.getOptions(field); // 获取 field 字段组件的配置
//   ctx.setOptions(field, {
//     min: new Date(),
//   }); // 更新 field 字段组件的配置, 并更新行内编辑组件
// });

// 使用自定义组件
// editInstance.component("myComponent", DateTimePicker); // 使用时: {type: "myComponent"}

import Vue from 'vue'
import { i18n } from '@/main.js'
import { getValueByPath, setValueByPath } from '@/utils/obj'
export * as EditConfig from './editConfig'
export * as Formatter from './formatter'

/**
 * 组件别名, 兼容 FormGenerator
 */
export const COMPONENT_ALIAS = Object.freeze({
  date: 'mt-date-picker',
  datetime: 'mt-date-time-picker',
  select: 'mt-select',
  number: 'mt-input-number',
  multiSelect: 'mt-multi-select',
  text: 'mt-input'
})

/**
 * 让组件开放几个兼容 eJComponent 的 api
 */
export const eJComponentFactory = function ({
  tag = 'text',
  options,
  components = {},
  componentAlias = COMPONENT_ALIAS,
  onInput,
  onChange,
  valueConvert,
  column
}) {
  let tagName
  if (components[tag]) {
    tagName = tag
  }
  if (!tagName) {
    tagName = componentAlias[tag] ?? tag
  }

  const vueConstructor = Vue.extend({
    template: `<component is="${tagName}" @change="onChange" v-bind="options" @input="onInput" />`,
    components,
    data: function () {
      return {
        options
      }
    },
    methods: {
      onInput(v) {
        this.setValue(v)
        onInput && onInput(v)
      },
      onChange(v) {
        onChange && onChange(v)
      },
      getValue() {
        return this.options.value
      },
      setValue(v) {
        this.options.value = v
      },
      setOptions(data) {
        Object.assign(this.options, data)
        this.$forceUpdate()
      }
    }
  })
  const instance = new vueConstructor({
    i18n
  })

  return {
    instance,
    appendTo: (el) => {
      instance.vm = instance.$mount()
      el.style.display = 'none'
      el.parentElement.insertBefore(instance.vm.$el, el)
      el.ej2_instances = null
    },
    destroy: () => {
      instance.vm.$destroy()
    },
    get value() {
      let val = instance.vm.getValue()
      if (typeof valueConvert === 'function') {
        val = valueConvert(val, {
          options: instance.options,
          column
        })
      }
      return val
    },
    set value(v) {
      instance.vm.setValue(v)
    }
  }
}

/**
 * edit 配置扩展, 用于单元格级生成表单配置
 */
export function createEditInstance() {
  const KEY_FIELD_EL = Symbol('field'),
    KEY_COMPONENTS = Symbol('components'),
    KEY_COMPONENT_ALIAS = Symbol('componentAlias'),
    KEY_ROW_DATA = Symbol('rowData'),
    KEY_ON_INPUT = Symbol('onInput'),
    KEY_ON_CHANGE = Symbol('onChange')

  const ctx = {
    [KEY_FIELD_EL]: {},
    [KEY_COMPONENTS]: {},
    [KEY_COMPONENT_ALIAS]: {
      ...COMPONENT_ALIAS
    },
    [KEY_ROW_DATA]: null,
    [KEY_ON_INPUT]: new Set(),
    [KEY_ON_CHANGE]: new Set(),

    /**
     * 获取最新的行数据
     */
    get rowData() {
      return ctx[KEY_ROW_DATA]
    },

    /**
     * 获取行数据
     * @param {string} field
     * @returns
     */
    getValueByField(field) {
      return getValueByPath(ctx[KEY_ROW_DATA], field)
    },

    /**
     * 设置行数据并更新组件
     * @param {string} field
     * @param {*} value
     */
    setValueByField(field, value) {
      setValueByPath(ctx[KEY_ROW_DATA], field, value)
      if (ctx[KEY_FIELD_EL]?.[field]) {
        ctx[KEY_FIELD_EL][field].value = value
      }
    },

    /**
     * 更新组件配置
     * @param {string} field
     * @param {object} options
     */
    setOptions(field, options) {
      ctx[KEY_FIELD_EL][field]?.instance.setOptions(options)
    },

    /**
     * 获取组件配置
     * @param {string} field
     */
    getOptions(field) {
      return ctx[KEY_FIELD_EL][field]?.instance?.options || {}
    },

    /**
     * 更新所有组件配置, 主要用于整行禁用启用
     * @param {*} options
     */
    setOptionsAll(options) {
      Object.values(ctx[KEY_FIELD_EL]).forEach((el) => {
        el?.instance.setOptions(options)
      })
    },

    /**
     * 生成 edit 配置
     * @param {function} getEditConfig 用于单元格级生成表单配置, 空则读取 columnData 下 editConfig
     * @param {function} valueConvert 值转换
     * @param {function} onInput
     * @param {function} onChange
     * @returns
     */
    create: function ({ getEditConfig, valueConvert, onInput, onChange } = {}) {
      let createEl // create 事件占位组件
      let writeEl // 真正生成的表单组件
      let cellEditConfig
      let mField

      return {
        create: (args) => {
          ctx[KEY_ROW_DATA] = args.data
          createEl = document.createElement('input')
          return createEl
        },
        read: () => {
          const val = writeEl.value
          return val
        },
        write: (arg) => {
          const { rowData, column, row } = arg
          const { editConfig, field } = column
          arg.row.editInstance = ctx
          cellEditConfig = editConfig
          if (typeof getEditConfig === 'function') {
            cellEditConfig = getEditConfig({ column, rowData })
          }

          writeEl = eJComponentFactory({
            valueConvert,
            tag: cellEditConfig.type,
            options: {
              value: getValueByPath(rowData, field),
              ...cellEditConfig,
              type: undefined
            },
            components: ctx[KEY_COMPONENTS],
            componentAlias: ctx[KEY_COMPONENT_ALIAS],
            onChange: (arg) => {
              const callArg = [
                ctx,
                {
                  rowData,
                  field,
                  row
                },
                arg
              ]

              // 字段级
              typeof onChange === 'function' && onChange(...callArg)
              // 全局
              ctx[KEY_ON_CHANGE].forEach((fn) => fn(...callArg))
            },
            onInput: (value) => {
              const oldValue = getValueByPath(ctx[KEY_ROW_DATA], field, value)
              setValueByPath(ctx[KEY_ROW_DATA], field, value)
              const callArg = [
                ctx,
                {
                  value,
                  oldValue,
                  rowData,
                  row,
                  field
                }
              ]

              // 字段级
              typeof onInput === 'function' && onInput(...callArg)
              // 全局
              ctx[KEY_ON_INPUT].forEach((fn) => fn(...callArg))
            },
            column
          })

          mField = field
          ctx[KEY_FIELD_EL][field] = writeEl

          writeEl.appendTo(createEl)
        },
        destroy: () => {
          writeEl.destroy()
          writeEl = null
          delete ctx[KEY_FIELD_EL][mField]
        }
      }
    },

    /**
     * 使用组件
     * @param {string} name
     * @param {*} component
     */
    component: function (name, component) {
      ctx[KEY_COMPONENTS][name] = component
      return ctx
    },

    /**
     * 组件别名
     * @param {string} name
     * @param {string} alias
     */
    componentAlias: function (name, alias) {
      ctx[KEY_COMPONENT_ALIAS][name] = alias
      return ctx
    },

    /**
     * input 事件触发回调
     * @param {function} fn (ctx, { value, oldValue, field }) => {};
     * @returns
     */
    onInput: function (fn) {
      if (typeof fn === 'function') {
        ctx[KEY_ON_INPUT].add(fn)
      }
      return ctx
    },
    /**
     * change 事件触发回调
     * @param {function} fn (ctx, { value, field }, event) => {};
     * @returns
     */
    onChange: function (fn) {
      if (typeof fn === 'function') {
        ctx[KEY_ON_CHANGE].add(fn)
      }
      return ctx
    }
  }
  return ctx
}
