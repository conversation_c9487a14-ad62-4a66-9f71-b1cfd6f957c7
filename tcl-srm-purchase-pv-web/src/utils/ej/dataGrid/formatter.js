import { getValueByPath } from '@/utils/obj'
import { dateUtils } from '@mtech-common/utils'

/**
 * 通用的 select 组件 formatter
 * @deprecated 仅适用于旧版本的配置 (editConfig 被挂载到 column 时)
 */
export const fmtSelect = function ({ field, editConfig }, item) {
  const cellVal = getValueByPath(item, field)
  if (!editConfig || typeof editConfig !== 'object') {
    return cellVal
  }
  const fields = editConfig.props?.fields || editConfig.fields
  const dataSource = editConfig.props?.dataSource || editConfig.dataSource || []
  const { value = 'value', text = 'text' } = fields
  return dataSource.find((e) => e[value] === cellVal)?.[text] ?? cellVal
}

/**
 * datetime 组件 formatter
 */
export function createFmtDatetime(fmt = 'YYYY-MM-DD HH:mm:ss') {
  return function ({ field }, item) {
    const cellVal = getValueByPath(item, field)
    if (typeof cellVal === 'object' && cellVal !== null) {
      return dateUtils(cellVal).format(fmt)
    } else if (/^\d{13}$|^\d{14}$|^\d{15}$/.test(cellVal)) {
      return dateUtils(new Date(Number(cellVal))).format(fmt)
    } else if (cellVal && typeof cellVal === 'string' && cellVal != '0') {
      return dateUtils(new Date(cellVal)).format(fmt)
    } else if (!cellVal || cellVal == '0') {
      return null
    }
    return cellVal
  }
}

/**
 * 通用的 datetime 组件 formatter
 */
export const fmtDatetime = createFmtDatetime('YYYY-MM-DD HH:mm:ss')
