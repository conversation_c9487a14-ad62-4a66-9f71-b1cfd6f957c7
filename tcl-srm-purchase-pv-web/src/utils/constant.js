// 阶梯报价时需要阶梯展示的价格字段（暂时列举这些，后续根据功能添加）mt_supplier_bidding_item
// 单价(未税)、单价（含税）、历史价格、上次报价未税、上次报价含税、降幅%、降幅、
// 是否成本模型报价、成本模型id、成本模型合并、成本分析成本测算不合并
export const stepPriceField = [
  'biddingItemDTO.untaxedUnitPrice', //单价(未税)
  'biddingItemDTO.taxedUnitPrice', //单价（含税）
  'biddingItemDTO.bidHistoryPrice', //历史价格
  'biddingItemDTO.lastQuoteUntaxed', //上次报价未税
  'biddingItemDTO.lastQuoteTaxed', //上次报价含税
  'biddingItemDTO.declinePercent', //降幅%
  // 'biddingItemDTO.costModelQuote', //是否成本模型报价、
  // 'biddingItemDTO.costModelId', //成本模型id
  'biddingItemDTO.costModelName', //成本模型
  'biddingItemDTO.transportCost', // 运费
  'biddingItemDTO.processCost', // 加工费
  'biddingItemDTO.processPartyMaterialCost', //加工费材料费
  ''
]

export const stepPriceFieldArr = [
  'untaxedUnitPrice', //单价(未税)
  'taxedUnitPrice', //单价（含税）
  'bidHistoryPrice', //历史价格
  'lastQuoteUntaxed', //上次报价未税
  'lastQuoteTaxed', //上次报价含税
  'declinePercent', //降幅%
  // 'costModelQuote', //是否成本模型报价、
  // 'costModelId', //成本模型id
  'costModelName', //成本模型
  'transportCost', // 运费
  'processCost', // 加工费
  'processPartyMaterialCost' //加工费材料费
]

export const stepPriceFieldNoPrefix = [
  'untaxedUnitPrice', //单价(未税)
  'taxedUnitPrice', //单价（含税）
  'bidHistoryPrice', //历史价格
  'lastQuoteUntaxed', //上次报价未税
  'lastQuoteTaxed', //上次报价含税
  'declinePercent', //降幅%
  // 'costModelQuote', //是否成本模型报价、
  // 'costModelId', //成本模型id
  'costModelName', //成本模型
  'transportCost', // 运费
  'processCost', // 加工费
  'processPartyMaterialCost' //加工费材料费
]

// 不合并的field - no prefix
export const notMergeFieldnoPrefix = [
  ...stepPriceFieldNoPrefix,
  'stepNum',
  'itemExtMap.referItemUnitPriceUntaxed'
]

// 不合并的field - 报价
export const stepNotMergeField = [
  'stepNum', //阶梯数量
  'biddingItemDTO.untaxedUnitPrice', //单价(未税)
  'biddingItemDTO.taxedUnitPrice', //单价（含税）
  'biddingItemDTO.untaxedTotalPrice', //总价（未税）
  'biddingItemDTO.taxedTotalPrice', //总价（含税）
  'biddingItemDTO.bidHistoryPrice', //历史价格
  'biddingItemDTO.lastQuoteUntaxed', //上次报价未税
  'biddingItemDTO.lastQuoteTaxed', //上次报价含税
  'biddingItemDTO.declinePercent', //降幅%
  'biddingItemDTO.transportCost', // 运费
  'biddingItemDTO.processCost', // 加工费
  'biddingItemDTO.processPartyMaterialCost', //加工费材料费
  'itemExtMap.referItemUnitPriceUntaxed', // 参考物料单价（未税）
  'itemExtMap.referItemUnitPriceTaxed', // 参考物料单价（含税）
  'costAnalysis', //成本分析
  'costModelName', //成本模型
  'costEstimation', //成本测算
  'costModelEstimatePrice' //测算价格
]

export const stepNotMergeFieldNoPrefix = [
  'stepNum', //阶梯数量
  'untaxedUnitPrice', //单价(未税)
  'taxedUnitPrice', //单价（含税）
  'untaxedTotalPrice', //总价（含税）
  'taxedTotalPrice', //总价（含税）
  'bidHistoryPrice', //历史价格
  'lastQuoteUntaxed', //上次报价未税
  'lastQuoteTaxed', //上次报价含税
  'declinePercent', //降幅%
  'transportCost', // 运费
  'processCost', // 加工费
  'processPartyMaterialCost', //加工费材料费
  'referItemUnitPriceUntaxed', // 参考物料单价（未税）
  'referItemUnitPriceTaxed', // 参考物料单价（含税）
  'costAnalysis', //成本分析
  'costModelName', //成本模型
  'costEstimation', //成本测算
  'costModelEstimatePrice' //测算价格
]

// 正则表达式
export const RegExpMap = {
  /**
   * 身份证号码（废弃）
   * 支持15位和18位身份证号，18位支持最后一位为X
   */
  idCardReg: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,

  /**
   * 18位身份证号码（严格验证）
   * 严格按照身份证号码规则验证，包括地区码、出生日期等
   */
  idCard18StrictReg: /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dX]$/i,

  /**
   * 手机号码
   * 中国大陆手机号正则表达式，支持13-19开头的11位号码
   */
  phoneReg: /^1[3-9]\d{9}$/,

  /**
   * 姓名验证
   * 支持中文、英文、少数民族姓名中的·
   */
  nameReg: /^[\u4e00-\u9fa5a-zA-Z·]{2,20}$/,

  /**
   * 车牌号码
   * 支持普通车牌号（7位）
   */
  carNoReg: /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/,

  /**
   * 新能源车牌号码
   * 支持新能源车牌号（8位）
   */
  newEnergyCarNoReg: /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领]{1}[A-Z]{1}[DF]{1}[A-HJ-NP-Z0-9]{5}$/,

  /**
   * 邮箱地址
   * 标准邮箱格式验证
   */
  emailReg: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,

  /**
   * 固定电话
   * 支持区号+号码格式，如：010-********
   */
  telReg: /^0\d{2,3}-?\d{7,8}$/,

  /**
   * 邮政编码
   * 中国邮政编码6位数字
   */
  postalCodeReg: /^[1-9]\d{5}$/,

  /**
   * 统一社会信用代码
   * 18位统一社会信用代码
   */
  creditCodeReg: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/,

  /**
   * 银行卡号
   * 支持13-19位银行卡号
   */
  bankCardReg: /^[1-9]\d{12,18}$/
}

// 身份证地区码映射（部分常用地区）
export const IdCardRegionMap = {
  '110000': '北京市',
  '110100': '北京市市辖区',
  '110101': '北京市东城区',
  '110102': '北京市西城区',
  '110105': '北京市朝阳区',
  '110106': '北京市丰台区',
  '110107': '北京市石景山区',
  '110108': '北京市海淀区',
  '110109': '北京市门头沟区',
  '110111': '北京市房山区',
  '110112': '北京市通州区',
  '110113': '北京市顺义区',
  '110114': '北京市昌平区',
  '110115': '北京市大兴区',
  '110116': '北京市怀柔区',
  '110117': '北京市平谷区',
  '110118': '北京市密云区',
  '110119': '北京市延庆区',

  '120000': '天津市',
  '120100': '天津市市辖区',
  '120101': '天津市和平区',
  '120102': '天津市河东区',
  '120103': '天津市河西区',
  '120104': '天津市南开区',
  '120105': '天津市河北区',
  '120106': '天津市红桥区',
  '120110': '天津市东丽区',
  '120111': '天津市西青区',
  '120112': '天津市津南区',
  '120113': '天津市北辰区',
  '120114': '天津市武清区',
  '120115': '天津市宝坻区',
  '120116': '天津市滨海新区',
  '120117': '天津市宁河区',
  '120118': '天津市静海区',
  '120119': '天津市蓟州区',

  '310000': '上海市',
  '310100': '上海市市辖区',
  '310101': '上海市黄浦区',
  '310104': '上海市徐汇区',
  '310105': '上海市长宁区',
  '310106': '上海市静安区',
  '310107': '上海市普陀区',
  '310109': '上海市虹口区',
  '310110': '上海市杨浦区',
  '310112': '上海市闵行区',
  '310113': '上海市宝山区',
  '310114': '上海市嘉定区',
  '310115': '上海市浦东新区',
  '310116': '上海市金山区',
  '310117': '上海市松江区',
  '310118': '上海市青浦区',
  '310120': '上海市奉贤区',
  '310151': '上海市崇明区',

  '440000': '广东省',
  '440100': '广州市',
  '440300': '深圳市',
  '440400': '珠海市',
  '440500': '汕头市',
  '440600': '佛山市',
  '440700': '江门市',
  '440800': '湛江市',
  '440900': '茂名市',
  '441200': '肇庆市',
  '441300': '惠州市',
  '441400': '梅州市',
  '441500': '汕尾市',
  '441600': '河源市',
  '441700': '阳江市',
  '441800': '清远市',
  '441900': '东莞市',
  '442000': '中山市',
  '445100': '潮州市',
  '445200': '揭阳市',
  '445300': '云浮市'
}
