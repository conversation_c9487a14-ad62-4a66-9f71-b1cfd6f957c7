/**
 * 验证工具函数
 * 提供常用的数据验证功能
 */

import { RegExpMap } from './constant'

/**
 * 身份证号格式验证
 * @param {string} idCard 身份证号
 * @returns {boolean} 是否有效
 */
export const isValidIdCard = (idCard) => {
  if (!idCard) return false

  // 去除空格并转换为大写
  const cleanIdCard = idCard.toString().trim().toUpperCase()

  // 长度检查
  if (cleanIdCard.length !== 18) {
    return false
  }

  // 基础格式验证
  if (!RegExpMap.idCard18StrictReg.test(cleanIdCard)) {
    return false
  }

  // 验证出生日期
  if (!validateIdCardBirthDate(cleanIdCard)) {
    return false
  }

  // 验证校验位
  return validateIdCardChecksum(cleanIdCard)
}

/**
 * 验证身份证号中的出生日期
 * @param {string} idCard 身份证号
 * @returns {boolean} 出生日期是否有效
 */
export const validateIdCardBirthDate = (idCard) => {
  try {
    // 提取出生日期部分 (第7-14位)
    const birthDateStr = idCard.substring(6, 14)
    const year = parseInt(birthDateStr.substring(0, 4))
    const month = parseInt(birthDateStr.substring(4, 6))
    const day = parseInt(birthDateStr.substring(6, 8))

    // 年份范围检查 (1900-当前年份)
    const currentYear = new Date().getFullYear()
    if (year < 1900 || year > currentYear) {
      return false
    }

    // 月份检查
    if (month < 1 || month > 12) {
      return false
    }

    // 日期检查
    if (day < 1 || day > 31) {
      return false
    }

    // 使用Date对象验证日期的有效性
    const date = new Date(year, month - 1, day)
    return date.getFullYear() === year &&
           date.getMonth() === month - 1 &&
           date.getDate() === day
  } catch (error) {
    return false
  }
}

/**
 * 身份证校验位验证
 * @param {string} idCard 身份证号
 * @returns {boolean} 校验位是否正确
 */
export const validateIdCardChecksum = (idCard) => {
  try {
    // 加权因子
    const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    // 校验码对应表
    const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']

    let sum = 0
    // 计算前17位的加权和
    for (let i = 0; i < 17; i++) {
      const digit = parseInt(idCard.charAt(i))
      if (isNaN(digit)) {
        return false
      }
      sum += digit * weights[i]
    }

    // 计算校验位
    const remainder = sum % 11
    const checkCode = checkCodes[remainder]
    const actualCheckCode = idCard.charAt(17).toUpperCase()

    return checkCode === actualCheckCode
  } catch (error) {
    return false
  }
}

/**
 * 手机号格式验证
 * @param {string} phone 手机号
 * @returns {boolean} 是否有效
 */
export const isValidPhone = (phone) => {
  if (!phone) return true // 手机号通常不是必填的
  return RegExpMap.phoneReg.test(phone.toString().trim())
}

/**
 * 车牌号格式验证
 * @param {string} carNo 车牌号
 * @param {boolean} supportNewEnergy 是否支持新能源车牌，默认true
 * @returns {boolean} 是否有效
 */
export const isValidCarNo = (carNo, supportNewEnergy = true) => {
  if (!carNo) return true // 车牌号通常不是必填的

  const upperCarNo = carNo.toString().trim().toUpperCase()

  // 普通车牌号校验
  const isNormalCarNo = RegExpMap.carNoReg.test(upperCarNo)

  // 新能源车牌号校验
  const isNewEnergyCarNo = supportNewEnergy && RegExpMap.newEnergyCarNoReg.test(upperCarNo)

  return isNormalCarNo || isNewEnergyCarNo
}

/**
 * 姓名格式验证
 * @param {string} name 姓名
 * @returns {boolean} 是否有效
 */
export const isValidName = (name) => {
  if (!name) return false
  return RegExpMap.nameReg.test(name)
}

/**
 * 获取身份证验证详细错误信息
 * @param {string} idCard 身份证号
 * @returns {string} 错误信息，空字符串表示验证通过
 */
export const getIdCardValidationError = (idCard) => {
  if (!idCard) {
    return '请输入身份证号'
  }

  const cleanIdCard = idCard.toString().trim().toUpperCase()

  if (cleanIdCard.length !== 18) {
    return '身份证号应为18位'
  }

  if (!RegExpMap.idCard18StrictReg.test(cleanIdCard)) {
    return '身份证号格式不正确'
  }

  if (!validateIdCardBirthDate(cleanIdCard)) {
    return '身份证号中的出生日期无效'
  }

  if (!validateIdCardChecksum(cleanIdCard)) {
    return '身份证号校验位错误'
  }

  return ''
}

/**
 * 身份证号验证器（用于表单验证）
 * @param {object} _rule 验证规则（未使用，但保持接口一致性）
 * @param {string} value 值
 * @param {function} callback 回调函数
 */
export const validateIdCard = (_rule, value, callback) => {
  if (!value) {
    callback()
    return
  }

  const errorMessage = getIdCardValidationError(value)
  if (errorMessage) {
    callback(new Error(errorMessage))
  } else {
    callback()
  }
}

/**
 * 手机号验证器（用于表单验证）
 * @param {object} _rule 验证规则（未使用，但保持接口一致性）
 * @param {string} value 值
 * @param {function} callback 回调函数
 */
export const validatePhone = (_rule, value, callback) => {
  if (!value) {
    callback()
    return
  }
  if (!isValidPhone(value)) {
    callback(new Error('请输入正确的手机号格式'))
  } else {
    callback()
  }
}

/**
 * 姓名验证器（用于表单验证）
 * @param {object} _rule 验证规则（未使用，但保持接口一致性）
 * @param {string} value 值
 * @param {function} callback 回调函数
 */
export const validateName = (_rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入姓名'))
    return
  }
  if (!isValidName(value)) {
    callback(new Error('姓名格式不正确，应为2-20个中文、英文字符'))
  } else {
    callback()
  }
}

/**
 * 车牌号验证器（用于表单验证）
 * @param {object} _rule 验证规则（未使用，但保持接口一致性）
 * @param {string} value 值
 * @param {function} callback 回调函数
 */
export const validateCarNo = (_rule, value, callback) => {
  if (!value) {
    callback()
    return
  }
  if (!isValidCarNo(value)) {
    callback(new Error('请输入正确的车牌号格式（如：京A12345 或 京AD12345）'))
  } else {
    callback()
  }
}

/**
 * 处理身份证号输入
 * @param {string} value 输入值
 * @param {boolean} debug 是否开启调试模式
 * @returns {string} 处理后的值
 */
export const handleIdCardInput = (value, debug = false) => {
  if (debug) {
    console.log('handleIdCardInput 输入:', { value, type: typeof value, length: value?.length })
  }

  // 处理各种输入类型
  if (value === null || value === undefined) {
    if (debug) console.log('handleIdCardInput: 输入为null或undefined，返回空字符串')
    return ''
  }

  // 转换为字符串
  let stringValue = value
  if (typeof value === 'number') {
    stringValue = value.toString()
    if (debug) console.log('handleIdCardInput: 数字转字符串:', stringValue)
  } else if (typeof value !== 'string') {
    if (debug) console.log('handleIdCardInput: 非字符串类型，返回空字符串')
    return ''
  }

  // 处理步骤
  const step1 = stringValue.trim()
  if (debug) console.log('handleIdCardInput step1 (trim):', step1)

  const step2 = step1.toUpperCase()
  if (debug) console.log('handleIdCardInput step2 (toUpperCase):', step2)

  const step3 = step2.replace(/[^0-9X]/g, '')
  if (debug) console.log('handleIdCardInput step3 (replace):', step3)

  const step4 = step3.substring(0, 18)
  if (debug) console.log('handleIdCardInput step4 (substring):', step4)

  if (debug) {
    console.log('handleIdCardInput 最终结果:', {
      input: value,
      output: step4,
      inputLength: value?.length,
      outputLength: step4.length
    })
  }

  return step4
}

/**
 * 格式化身份证号显示（脱敏）
 * @param {string} idCard 身份证号
 * @param {boolean} mask 是否脱敏，默认true
 * @returns {string} 格式化后的身份证号
 */
export const formatIdCardDisplay = (idCard, mask = true) => {
  if (!idCard) return ''

  const cleanIdCard = idCard.toString().trim()
  if (cleanIdCard.length !== 18) return cleanIdCard

  if (mask) {
    // 脱敏显示：显示前6位和后4位，中间用*代替
    return `${cleanIdCard.substring(0, 6)}********${cleanIdCard.substring(14)}`
  } else {
    // 格式化显示：添加空格分隔
    return `${cleanIdCard.substring(0, 6)} ${cleanIdCard.substring(6, 14)} ${cleanIdCard.substring(14)}`
  }
}

/**
 * 从身份证号提取信息
 * @param {string} idCard 身份证号
 * @returns {object} 提取的信息
 */
export const extractIdCardInfo = (idCard) => {
  if (!isValidIdCard(idCard)) {
    return null
  }

  const cleanIdCard = idCard.toString().trim().toUpperCase()

  // 提取出生日期
  const birthYear = parseInt(cleanIdCard.substring(6, 10))
  const birthMonth = parseInt(cleanIdCard.substring(10, 12))
  const birthDay = parseInt(cleanIdCard.substring(12, 14))
  const birthDate = new Date(birthYear, birthMonth - 1, birthDay)

  // 计算年龄
  const today = new Date()
  let age = today.getFullYear() - birthYear
  if (today.getMonth() < birthMonth - 1 ||
      (today.getMonth() === birthMonth - 1 && today.getDate() < birthDay)) {
    age--
  }

  // 判断性别（第17位，奇数为男，偶数为女）
  const genderCode = parseInt(cleanIdCard.charAt(16))
  const gender = genderCode % 2 === 1 ? '男' : '女'

  // 提取地区码
  const regionCode = cleanIdCard.substring(0, 6)

  return {
    birthDate,
    birthYear,
    birthMonth,
    birthDay,
    age,
    gender,
    regionCode
  }
}

/**
 * 处理手机号输入
 * @param {string} value 输入值
 * @param {boolean} debug 是否开启调试模式
 * @returns {string} 处理后的值
 */
export const handlePhoneInput = (value, debug = false) => {
  if (debug) {
    console.log('handlePhoneInput 输入:', { value, type: typeof value, length: value?.length })
  }

  // 处理各种输入类型
  if (value === null || value === undefined) {
    if (debug) console.log('handlePhoneInput: 输入为null或undefined，返回空字符串')
    return ''
  }

  // 转换为字符串
  let stringValue = value
  if (typeof value === 'number') {
    stringValue = value.toString()
    if (debug) console.log('handlePhoneInput: 数字转字符串:', stringValue)
  } else if (typeof value !== 'string') {
    if (debug) console.log('handlePhoneInput: 非字符串类型，返回空字符串')
    return ''
  }

  // 只保留数字，限制11位
  const processed = stringValue.replace(/\D/g, '').substring(0, 11)

  if (debug) {
    console.log('handlePhoneInput 最终结果:', {
      input: value,
      output: processed,
      inputLength: value?.length,
      outputLength: processed.length
    })
  }

  return processed
}

/**
 * 处理车牌号输入
 * @param {string} value 输入值
 * @param {boolean} debug 是否开启调试模式
 * @returns {string} 处理后的值
 */
export const handleCarNoInput = (value, debug = false) => {
  if (debug) {
    console.log('handleCarNoInput 输入:', { value, type: typeof value, length: value?.length })
  }

  // 处理各种输入类型
  if (value === null || value === undefined) {
    if (debug) console.log('handleCarNoInput: 输入为null或undefined，返回空字符串')
    return ''
  }

  // 转换为字符串
  let stringValue = value
  if (typeof value === 'number') {
    stringValue = value.toString()
    if (debug) console.log('handleCarNoInput: 数字转字符串:', stringValue)
  } else if (typeof value !== 'string') {
    if (debug) console.log('handleCarNoInput: 非字符串类型，返回空字符串')
    return ''
  }

  // 处理步骤
  const step1 = stringValue.trim()
  if (debug) console.log('handleCarNoInput step1 (trim):', step1)

  const step2 = step1.toUpperCase()
  if (debug) console.log('handleCarNoInput step2 (toUpperCase):', step2)

  // 只保留中文、英文字母、数字，限制8位
  const step3 = step2.replace(/[^\u4e00-\u9fa5A-Z0-9]/g, '')
  if (debug) console.log('handleCarNoInput step3 (replace):', step3)

  const step4 = step3.substring(0, 8)
  if (debug) console.log('handleCarNoInput step4 (substring):', step4)

  if (debug) {
    console.log('handleCarNoInput 最终结果:', {
      input: value,
      output: step4,
      inputLength: value?.length,
      outputLength: step4.length
    })
  }

  return step4
}

/**
 * 获取车牌号验证详细错误信息
 * @param {string} carNo 车牌号
 * @param {boolean} supportNewEnergy 是否支持新能源车牌
 * @returns {string} 错误信息，空字符串表示验证通过
 */
export const getCarNoValidationError = (carNo, supportNewEnergy = true) => {
  if (!carNo) {
    return '请输入车牌号'
  }

  const cleanCarNo = carNo.toString().trim().toUpperCase()

  // 长度检查
  if (cleanCarNo.length < 7 || cleanCarNo.length > 8) {
    return `车牌号长度错误，当前${cleanCarNo.length}位，应为7位或8位`
  }

  // 格式检查
  const isNormalCarNo = RegExpMap.carNoReg.test(cleanCarNo)
  const isNewEnergyCarNo = supportNewEnergy && RegExpMap.newEnergyCarNoReg.test(cleanCarNo)

  if (!isNormalCarNo && !isNewEnergyCarNo) {
    if (cleanCarNo.length === 7) {
      return '普通车牌号格式不正确，应为：省份简称+字母+4位数字/字母+特殊字符'
    } else if (cleanCarNo.length === 8) {
      return '新能源车牌号格式不正确，应为：省份简称+字母+D/F+5位数字/字母'
    } else {
      return '车牌号格式不正确'
    }
  }

  return ''
}

/**
 * 检查数组中是否有重复的身份证号
 * @param {Array} list 包含身份证号的对象数组
 * @param {string} field 身份证号字段名，默认为'idNo'
 * @returns {Array} 重复的身份证号数组
 */
export const checkDuplicateIdCards = (list, field = 'idNo') => {
  const idNos = list.map((item) => item[field]).filter(Boolean)
  return idNos.filter((idNo, index) => idNos.indexOf(idNo) !== index)
}

/**
 * 详细验证身份证号并返回具体错误信息
 * @param {string} idCard 身份证号
 * @returns {object} 验证结果 { valid: boolean, error: string, suggestion: string }
 */
export const validateIdCardDetailed = (idCard) => {
  if (!idCard) {
    return {
      valid: false,
      error: '身份证号不能为空',
      suggestion: '请输入18位身份证号'
    }
  }

  const cleanIdCard = idCard.toString().trim().toUpperCase()

  // 长度检查
  if (cleanIdCard.length !== 18) {
    return {
      valid: false,
      error: `身份证号长度错误，当前${cleanIdCard.length}位`,
      suggestion: '身份证号应为18位，请检查是否有遗漏或多余字符'
    }
  }

  // 格式检查
  if (!RegExpMap.idCard18StrictReg.test(cleanIdCard)) {
    return {
      valid: false,
      error: '身份证号格式不正确',
      suggestion: '身份证号应由17位数字和1位校验码（数字或X）组成'
    }
  }

  // 出生日期检查
  if (!validateIdCardBirthDate(cleanIdCard)) {
    const birthDateStr = cleanIdCard.substring(6, 14)
    return {
      valid: false,
      error: `出生日期无效：${birthDateStr}`,
      suggestion: '请检查身份证号中的出生日期部分是否正确'
    }
  }

  // 校验位检查
  if (!validateIdCardChecksum(cleanIdCard)) {
    const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']

    let sum = 0
    for (let i = 0; i < 17; i++) {
      sum += parseInt(cleanIdCard.charAt(i)) * weights[i]
    }
    const correctCheckCode = checkCodes[sum % 11]
    const actualCheckCode = cleanIdCard.charAt(17)

    return {
      valid: false,
      error: `校验位错误，应为${correctCheckCode}，实际为${actualCheckCode}`,
      suggestion: `请将身份证号最后一位修改为${correctCheckCode}`
    }
  }

  return { valid: true, error: '', suggestion: '' }
}

/**
 * 验证随车人员信息（增强版）
 * @param {Array} accompanyList 随车人员列表
 * @returns {object} 验证结果 { valid: boolean, message: string, details: Array }
 */
export const validateAccompanyList = (accompanyList) => {
  if (!Array.isArray(accompanyList) || accompanyList.length === 0) {
    return { valid: true, message: '', details: [] }
  }

  const details = []

  // 逐行验证
  accompanyList.forEach((row, index) => {
    const rowErrors = []

    // 验证姓名
    if (!row.name || !row.name.trim()) {
      rowErrors.push('姓名不能为空')
    } else if (!isValidName(row.name)) {
      rowErrors.push('姓名格式不正确')
    }

    // 验证身份证号
    if (!row.idNo || !row.idNo.trim()) {
      rowErrors.push('身份证号不能为空')
    } else {
      const idValidation = validateIdCardDetailed(row.idNo)
      if (!idValidation.valid) {
        rowErrors.push(idValidation.error)
      }
    }

    // 验证手机号（可选）
    if (row.phone && row.phone.trim() && !isValidPhone(row.phone)) {
      rowErrors.push('手机号格式不正确')
    }

    if (rowErrors.length > 0) {
      details.push({
        index: index + 1,
        name: row.name || '未填写',
        errors: rowErrors
      })
    }
  })

  // 检查身份证号重复
  const duplicateIdNos = checkDuplicateIdCards(accompanyList)
  if (duplicateIdNos.length > 0) {
    duplicateIdNos.forEach(idNo => {
      const duplicateIndexes = accompanyList
        .map((item, index) => item.idNo === idNo ? index + 1 : null)
        .filter(index => index !== null)

      details.push({
        index: duplicateIndexes.join('、'),
        name: '重复身份证号',
        errors: [`身份证号${idNo}重复出现在第${duplicateIndexes.join('、')}行`]
      })
    })
  }

  if (details.length > 0) {
    const errorMessage = details.map(detail =>
      `第${detail.index}行(${detail.name}): ${detail.errors.join('、')}`
    ).join('；')

    return {
      valid: false,
      message: errorMessage,
      details
    }
  }

  return { valid: true, message: '', details: [] }
}

