const ctx = '@@clickoutsideContext'

export default {
  bind(el, binding, vnode) {
    const ele = el
    const documentHandler = (e) => {
      if (!el.contains(e.target)) return
      let gridEl = null
      ele.__vue__.$children?.forEach((e) => {
        let _classList = e.$el?.classList?.length ? [...e.$el?.classList] : []
        if (_classList.includes('grid-container') || _classList.includes('ag-theme-alpine')) {
          gridEl = e.$el
        }
      })
      if (!gridEl || !vnode.context || gridEl?.contains(e.target)) {
        return false
      }
      // 调用指令回调
      if (binding.expression) {
        vnode.context[el[ctx].methodName](e)
      } else {
        el[ctx].bindingFn(e)
      }
    }
    // 将方法添加到ele
    ele[ctx] = {
      documentHandler,
      methodName: binding.expression,
      bindingFn: binding.value
    }
    document.addEventListener('click', documentHandler) // 为document绑定事件
  },
  update(el, binding) {
    const ele = el
    ele[ctx].methodName = binding.expression
    ele[ctx].bindingFn = binding.value
  },
  unbind(el) {
    document.removeEventListener('click', el[ctx].documentHandler) // 解绑
    delete el[ctx]
  }
}
