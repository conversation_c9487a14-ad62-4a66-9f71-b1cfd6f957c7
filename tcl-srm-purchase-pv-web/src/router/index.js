let routes = []
const requireComponent = require.context('./modules', true, /\.js$/)
requireComponent.keys().forEach((f) => {
  const config = requireComponent(f)
  let _module = config.default
  if (Array.isArray(_module)) {
    routes = routes.concat(_module)
  }
})

export default [
  {
    path: '/purchase-pv',
    component: () => import(/* webpackChunkName: "router/public" */ 'ROUTER_PUBLIC/index.vue'),
    children: routes
  }
]
