/** 收货管理 */
const Router = [
  {
    path: '/purchase-pv/receiving-management',
    name: 'receiving-management',
    component: () => import('ROUTER_PUBLIC/index.vue'),
    meta: {
      title: '收货管理'
    },
    children: [
      {
        path: 'sn-code-info-upload', // SN码信息上载
        name: 'sn-code-info-upload',
        component: () => import('@/views/purchase/receivingManagement/snCodeInfoUpload/index.vue')
      },
      {
        path: 'delivery-note-list', // 送货单列表
        name: 'delivery-note-list',
        component: () => import('@/views/purchase/receivingManagement/deliveryNoteList/index.vue')
      },
      {
        path: 'supply-plan', // 供货计划
        name: 'supply-plan',
        component: () => import('@/views/purchase/receivingManagement/supplyPlan/index.vue')
      },
      {
        path: 'supply-plan-detail', // 供货计划-创建送货单
        name: 'supply-plan-detail',
        component: () => import('@/views/purchase/receivingManagement/supplyPlan/detail.vue')
      }
    ]
  }
]

export default Router
