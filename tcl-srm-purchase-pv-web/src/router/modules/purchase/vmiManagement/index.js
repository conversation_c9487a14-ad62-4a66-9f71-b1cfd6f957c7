/** 采方-VMI管理 */
const Router = [
  {
    path: '/purchase-pv/vmi-management',
    name: 'vmi-management',
    component: () => import('ROUTER_PUBLIC/index.vue'),
    meta: {
      title: '采方-VMI管理'
    },
    children: [
      {
        path: 'inventory-level', // 库存水位
        name: 'inventory-level',
        component: () => import('@/views/purchase/vmiManagement/inventoryLevel/index.vue')
      },
      {
        path: 'config/plan-manager', // 采购计划负责人
        name: 'config/plan-manager',
        component: () => import('@/views/purchase/vmiManagement/config/planManager/index.vue')
      },
      {
        path: 'delivery-plan', // 送货计划
        name: 'delivery-plan',
        component: () => import('@/views/purchase/vmiManagement/deliveryPlan/index.vue')
      },
      {
        path: 'delivery-plan-detail', // 送货计划明细
        name: 'delivery-plan-detail',
        component: () => import('@/views/purchase/vmiManagement/deliveryPlan/detail.vue')
      },
      {
        path: 'inventory-management', // 入库管理
        name: 'inventory-management',
        component: () => import('@/views/purchase/vmiManagement/inventoryManagement/index.vue')
      },
      {
        path: 'inventory-management-detail', // 入库单明细
        name: 'inventory-management-detail',
        component: () => import('@/views/purchase/vmiManagement/inventoryManagement/detail.vue')
      },
      {
        path: 'return-management-detail', // 创建退货单
        name: 'return-management-detail',
        component: () => import('@/views/purchase/vmiManagement/returnManagement/detail.vue')
      },
      {
        path: 'outbound-record', // 出库记录
        name: 'outbound-record',
        component: () => import('@/views/purchase/vmiManagement/outboundRecord/index.vue')
      },
      {
        path: 'return-management', // 退货管理
        name: 'return-management',
        component: () => import('@/views/purchase/vmiManagement/returnManagement/index.vue')
      }
    ]
  }
]

export default Router
