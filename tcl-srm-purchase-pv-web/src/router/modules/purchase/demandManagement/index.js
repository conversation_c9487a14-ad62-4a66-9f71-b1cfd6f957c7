/** 需求管理 */
const Router = [
  {
    path: '/purchase-pv/demand-management',
    name: 'demand-management',
    component: () => import('ROUTER_PUBLIC/index.vue'),
    meta: {
      title: '需求管理'
    },
    children: [
      {
        path: 'procurement-request-management', // 采购申请管理
        name: 'procurement-request-management',
        component: () =>
          import('@/views/purchase/demandManagement/procurementRequestManagement/index.vue')
      },
      {
        path: 'procurement-request-management-detail', // 采购申请管理详情
        name: 'procurement-request-management-detail',
        component: () =>
          import('@/views/purchase/demandManagement/procurementRequestManagement/detail.vue')
      },
      {
        path: 'demand-pool', // 需求池
        name: 'demand-pool',
        component: () => import('@/views/purchase/demandManagement/demandPool/index.vue')
      },
      {
        path: 'order-transfer-config', // 转订单策略配置
        name: 'order-transfer-config',
        component: () => import('@/views/purchase/demandManagement/orderTransferConfig/index.vue')
      },
      {
        path: 'demand-pool-create', // 需求池转订单
        name: 'demand-pool-create',
        component: () => import('@/views/purchase/demandManagement/demandPool/create.vue')
      }
    ]
  }
]

export default Router
