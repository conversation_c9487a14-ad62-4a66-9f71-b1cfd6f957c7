/** 光伏采购执行**/
const Router = [
  {
    path: '/purchase-pv/purchase-coordination',
    name: 'contract',
    component: () => import('ROUTER_PUBLIC/index.vue'),
    meta: {
      title: 'contract'
    },
    children: [
      {
        path: '/purchase-pv/purchase-coordination', // 采购订单管理
        name: 'purchase-coordination-pv',
        component: () => import('@/views/purchase/purchaseExecute/purchaseOrderMgt/index.vue')
      },
      {
        path: '/purchase-pv/purchase-coordination-detail', // 采购订单管理详情
        name: 'purchase-coordination-pv-detail',
        component: () =>
          import('@/views/purchase/purchaseExecute/purchaseOrderMgt/detail/index.vue')
      },
      {
        path: '/purchase-pv/purchase-coordination-sup', // 采购订单管理 - 供方
        name: 'purchase-coordination-pv-sup',
        component: () => import('@/views/purchase/purchaseExecute/purchaseOrderMgt/index.vue')
      },
      {
        path: '/purchase-pv/purchase-coordination-detail-sup', // 采购订单管理详情 - 供方
        name: 'purchase-coordination-pv-detail-sup',
        component: () =>
          import('@/views/purchase/purchaseExecute/purchaseOrderMgt/detail/index.vue')
      },
      {
        path: '/purchase-pv/regionalDeliveryPlanMgt', // 区域交货计划管理
        name: 'regional-delivery-plan-pv',
        component: () =>
          import('@/views/purchase/purchaseExecute/regionalDeliveryPlanMgt/index.vue')
      },
      {
        path: '/purchase-pv/supplierDeliveryRestrictions', // 供应商与区域交货限制
        name: 'supplier-delivery-restrictions-pv',
        component: () =>
          import('@/views/purchase/purchaseExecute/supplierDeliveryRestrictions/index.vue')
      }
    ]
  }
]

export default Router
