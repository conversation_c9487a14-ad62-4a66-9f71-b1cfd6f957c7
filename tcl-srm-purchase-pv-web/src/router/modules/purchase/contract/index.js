/** 光伏执行测试文件**/
const Router = [
  {
    path: '/purchase-pv/contract',
    name: 'contract',
    component: () => import('ROUTER_PUBLIC/index.vue'),
    meta: {
      title: 'contract'
    },
    children: [
      {
        path: '/purchase-pv/contract', // 合同管理
        name: 'contract',
        component: () => import('@/views/purchase/contract/index.vue')
      },
      {
        path: '/purchase-pv/contract-detail', // 合同管理详情
        name: 'contract-detail',
        component: () => import('@/views/purchase/contract/detail/index.vue')
      },
      {
        path: '/purchase-pv/contract-sup', // 合同管理-供方
        name: 'contract-sup',
        component: () => import('@/views/purchase/contract/index.vue')
      },
      {
        path: '/purchase-pv/contract-sup-detail', // 合同管理详情-供方
        name: 'contract-sup-detail',
        component: () => import('@/views/purchase/contract/detail/index.vue')
      }
    ]
  }
]

export default Router
