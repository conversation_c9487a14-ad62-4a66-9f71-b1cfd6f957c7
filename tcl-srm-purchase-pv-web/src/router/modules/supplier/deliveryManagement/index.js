/** 发货管理 */
const Router = [
  {
    path: '/purchase-pv/delivery-management',
    name: 'delivery-management',
    component: () => import('ROUTER_PUBLIC/index.vue'),
    meta: {
      title: '发货管理'
    },
    children: [
      {
        path: 'sn-code-info-upload', // SN码信息上载
        name: 'sn-code-info-upload',
        component: () => import('@/views/supplier/deliveryManagement/snCodeInfoUpload/index.vue')
      },
      {
        path: 'delivery-note-list', // 送货单列表
        name: 'delivery-note-list',
        component: () => import('@/views/supplier/deliveryManagement/deliveryNoteList/index.vue')
      },
      {
        path: 'supply-plan', // 供货计划
        name: 'supply-plan',
        component: () => import('@/views/supplier/deliveryManagement/supplyPlan/index.vue')
      },
      {
        path: 'supply-plan-detail', // 供货计划-创建送货单
        name: 'supply-plan-detail',
        component: () => import('@/views/supplier/deliveryManagement/supplyPlan/detail.vue')
      },
      {
        path: 'delivery-appointment', // 预约送货
        name: 'delivery-appointment',
        component: () => import('@/views/supplier/deliveryManagement/deliveryAppointment/index.vue')
      }
    ]
  }
]

export default Router
