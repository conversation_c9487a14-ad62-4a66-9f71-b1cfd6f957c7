/** 供方-VMI管理 */
const Router = [
  {
    path: '/purchase-pv/supplier-vmi-management',
    name: 'supplier-vmi-management',
    component: () => import('ROUTER_PUBLIC/index.vue'),
    meta: {
      title: '供方-VMI管理'
    },
    children: [
      {
        path: 'inventory-level', // 库存水位
        name: 'inventory-level',
        component: () => import('@/views/supplier/vmiManagement/inventoryLevel/index.vue')
      },
      {
        path: 'delivery-plan-create', // 创建送货计划
        name: 'delivery-plan-create',
        component: () => import('@/views/supplier/vmiManagement/deliveryPlan/detail.vue')
      },
      {
        path: 'delivery-plan', // 送货计划
        name: 'delivery-plan',
        component: () => import('@/views/supplier/vmiManagement/deliveryPlan/index.vue')
      },
      {
        path: 'inventory-management-detail', // 创建入库单
        name: 'inventory-management-detail',
        component: () => import('@/views/supplier/vmiManagement/inventoryManagement/detail.vue')
      },
      {
        path: 'inventory-management', // 入库管理
        name: 'inventory-management',
        component: () => import('@/views/supplier/vmiManagement/inventoryManagement/index.vue')
      },
      {
        path: 'outbound-record', // 出库记录
        name: 'outbound-record',
        component: () => import('@/views/supplier/vmiManagement/outboundRecord/index.vue')
      },
      {
        path: 'return-management', // 退货管理
        name: 'return-management',
        component: () => import('@/views/supplier/vmiManagement/returnManagement/index.vue')
      },
      {
        path: 'return-management-detail', // 确认退货单
        name: 'return-management-detail',
        component: () => import('@/views/supplier/vmiManagement/returnManagement/detail.vue')
      }
    ]
  }
]

export default Router
