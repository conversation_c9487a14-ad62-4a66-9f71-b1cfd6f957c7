import { API } from '@mtech-common/http'

const NAME = 'supplierVmiManagement'

const APIS = {
  // 供方-VMI管理-库存水位-分页查询
  pageInventoryLevelApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/supplier/pull/jg`, data)
  },
  // 供方-VMI管理-库存水位-导出
  exportInventoryLevelApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/export`, data, {
      responseType: 'blob'
    })
  },
  // 供方-VMI管理-创建送货计划
  createDeliveryPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/delivery/plan/add`, data)
  },
  // 供方-VMI管理-创建送货计划明细
  saveDetailDeliveryPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/delivery/plan/detail/addAndUpdate`, data)
  },
  // 供方-VMI管理-送货计划-分页查询
  pageDeliveryPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/delivery/plan/supplier/page/query`, data)
  },
  // 供方-VMI管理-送货计划-提交
  submitDeliveryPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/delivery/plan/batch/submit`, data)
  },
  // 供方-VMI管理-送货计划-删除
  deleteDeliveryPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/delivery/plan/batch/delete`, data)
  },
  // 供方-VMI管理-送货计划-导出
  exportDeliveryPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/delivery/plan/supplier/export`, data, {
      responseType: 'blob'
    })
  },
  // 供方-VMI管理-送货计划-取消
  cancelDeliveryPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/delivery/plan/batch/cancel`, data)
  },
  // 供方-VMI管理-送货计划-明细
  pageDeliveryPlanDetailApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/delivery/plan/detail/supplier/page/query`, data)
  },
  // 供方-VMI管理-送货计划-明细-导出
  exportDeliveryPlanDetailApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/delivery/plan/detail/supplier/export`, data, {
      responseType: 'blob'
    })
  },
  // 供方-VMI管理-创建入库单-根据主单查询
  detailInventoryManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/in/item/list/stockInIds`, data)
  },
  // 供方-VMI管理-创建入库单-前置校验（废弃）
  checkInventoryManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/in/supplier/create/check`, data)
  },
  // 供方-VMI管理-创建入库单-合并单据
  mergeInventoryManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/in/item/merge/item`, data)
  },
  // 供方-VMI管理-创建入库单-物料明细-导入模板
  downloadInventoryManagementTemplateApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/delivery/plan/detail/template/export`, data, {
      responseType: 'blob'
    })
  },
  // 供方-VMI管理-创建入库单-物料明细-导入
  importInventoryManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/delivery/plan/detail/supplier/import`, data, {
      responseType: 'blob'
    })
  },
  // 供方-VMI管理-创建入库单-保存主单
  createInventoryManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/in/addAndUpdate`, data)
  },
  // 供方-VMI管理-创建入库单-保存明细
  saveInventoryManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/in/item/addAndUpdate`, data)
  },
  // 供方-VMI管理-入库管理-分页查询
  pageInventoryManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/in/supplier/page/query`, data)
  },
  // 供方-VMI管理-入库管理-根据id查询
  getInventoryManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/in/list/ids`, data)
  },
  // 供方-VMI管理-入库管理-删除
  deleteInventoryManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/in/batch/delete`, data)
  },
  // 供方-VMI管理-入库管理-提交
  submitInventoryManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/in/batch/submit`, data)
  },
  // 供方-VMI管理-入库管理-取消
  cancelInventoryManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/in/batch/cancel`, data)
  },
  // 供方-VMI管理-入库管理-关闭
  closeInventoryManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/in/batch/close`, data)
  },
  // 供方-VMI管理-入库管理-导出
  exportInventoryManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/in/supplier/export`, data, {
      responseType: 'blob'
    })
  },
  // 供方-VMI管理-入库管理-打印
  printInventoryManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/in/batch/print`, data, {
      responseType: 'blob'
    })
  },
  // 供方-VMI管理-入库管理-详情-分页查询
  pageInventoryManagementDetailApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/in/item/supplier/page/query`, data)
  },
  // 供方-VMI管理-入库管理-详情-导出
  exportInventoryManagementDetailApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/in/item/supplier/export`, data, {
      responseType: 'blob'
    })
  },
  // 供方-VMI管理-出库记录-分页查询
  pageOutboundRecordApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/out/record/supplier/page/query`, data)
  },
  // 供方-VMI管理-出库记录-导出
  exportOutboundRecordApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/out/record/supplier/export`, data, {
      responseType: 'blob'
    })
  },
  // 供方-VMI管理-退货管理-分页查询
  pageReturnManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/return/order/supplier/page/query`, data)
  },
  // 供方-VMI管理-退货管理-取消
  cancelReturnManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/return/order/batch/cancel`, data)
  },
  // 供方-VMI管理-退货管理-确认
  confirmReturnManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/return/order/batch/pass`, data)
  },
  // 供方-VMI管理-退货管理-驳回
  rejectReturnManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/return/order/batch/rejected`, data)
  },
  // 供方-VMI管理-退货管理-导出
  exportReturnManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/return/order/supplier/export`, data, {
      responseType: 'blob'
    })
  },
  // 供方-VMI管理-退货管理-明细-分页查询
  pageReturnManagementDetailApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/return/order/item/supplier/page/query`, data)
  },
  // 供方-VMI管理-退货管理-明细-根据主单id查询
  detailReturnManagementDetailApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/return/order/item/list/returnOrderIds`, data)
  },
  // 供方-VMI管理-退货管理-明细-导出
  exportReturnManagementDetailApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/return/order/item/supplier/export`, data, {
      responseType: 'blob'
    })
  }
}

export default {
  NAME,
  APIS
}
