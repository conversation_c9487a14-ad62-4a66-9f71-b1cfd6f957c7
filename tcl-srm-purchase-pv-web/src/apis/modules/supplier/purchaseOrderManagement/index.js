import { API } from '@mtech-common/http'

const NAME = 'supplierPurchaseOrderManagement'

const APIS = {
  // 供方-交货计划管理-分页查询
  pageDeliveryPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/plan/supplier/page`, data)
  },
  // 供方-交货计划管理-保存
  saveDeliveryPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/plan/batch/supplier/update`, data)
  },
  // 供方-交货计划管理-反馈
  feedbackDeliveryPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/plan/batch/feedback`, data)
  },
  // 供方-交货计划管理-导出
  exportDeliveryPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/plan/supplier/export/reImport`, data, {
      responseType: 'blob'
    })
  },
  // 供方-交货计划管理-导入
  importDeliveryPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/plan/supplier/import`, data, {
      responseType: 'blob'
    })
  },
  // 供方-交货计划管理-获取导入模板
  getTemplateDeliveryPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/plan/supplier/export/template`, data, {
      responseType: 'blob'
    })
  }
}

export default { NAME, APIS }
