import { API } from '@mtech-common/http'

const NAME = 'deliveryManagement'

const APIS = {
  // 供方-发货管理-SN码信息上载-户用/商用业务-分页查询
  pageCommercialSnCodeInfoUploadApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/commercial/sn/supplier/page/query`, data)
  },
  // 供方-发货管理-SN码信息上载-户用业务-导入
  importHouseholdSnCodeInfoUploadApi: (data = {}) => {
    return API.post(
      `/srm-purchase-pv/tenant/pv/delivery/commercial/sn/user/supplier/excel/import`,
      data,
      {
        responseType: 'blob'
      }
    )
  },
  // 供方-发货管理-SN码信息上载-商用业务-获取导入数据
  getImportDataCommercialSnCodeInfoUploadApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/commercial/sn/get/excel/data`, data)
  },
  // 供方-发货管理-SN码信息上载-商用业务-导入
  importCommercialSnCodeInfoUploadApi: (data = {}) => {
    return API.post(
      `/srm-purchase-pv/tenant/pv/delivery/commercial/sn/supplier/excel/import`,
      data,
      {
        responseType: 'blob'
      }
    )
  },
  // 供方-发货管理-SN码信息上载-户用/商用业务-导入模板下载
  tempCommercialSnCodeInfoUploadApi: (data = {}) => {
    return API.get(`/srm-purchase-pv/tenant/pv/delivery/commercial/sn/template/export`, data, {
      responseType: 'blob'
    })
  },
  // 供方-发货管理-SN码信息上载-户用/商用业务-导出
  exportCommercialSnCodeInfoUploadApi: (data = {}) => {
    return API.post(
      `/srm-purchase-pv/tenant/pv/delivery/commercial/sn/supplier/excel/export`,
      data,
      {
        responseType: 'blob'
      }
    )
  },
  // 供方-发货管理-SN码信息上载-户用/商用业务-批量提交
  submitCommercialSnCodeInfoUploadApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/commercial/sn/batch/submit`, data)
  },
  // 供方-发货管理-SN码信息上载-户用/商用业务-批量删除
  deleteCommercialSnCodeInfoUploadApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/commercial/sn/batch/delete`, data)
  },
  // 供方-发货管理-SN码信息上载-海外/非组件业务-分页查询
  pageAbroadSnCodeInfoUploadApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/abroad/sn/supplier/page/query`, data)
  },
  // 供方-发货管理-SN码信息上载-海外业务-导入
  importAbroadSnCodeInfoUploadApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/abroad/sn/component/excel/import`, data, {
      responseType: 'blob'
    })
  },
  // 供方-发货管理-SN码信息上载-海外业务-导入模板下载
  tempAbroadSnCodeInfoUploadApi: (data = {}) => {
    return API.get(
      `/srm-purchase-pv/tenant/pv/delivery/abroad/sn/component/template/export`,
      data,
      {
        responseType: 'blob'
      }
    )
  },
  // 供方-发货管理-SN码信息上载-海外业务-获取导入数据
  getImportDataAbroadSnCodeInfoUploadApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/abroad/sn/get/excel/data`, data)
  },
  // 供方-发货管理-SN码信息上载-非组件业务-导入
  importAbroadNonSnCodeInfoUploadApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/abroad/sn/excel/import`, data, {
      responseType: 'blob'
    })
  },
  // 供方-发货管理-SN码信息上载-非组件业务-导入模板下载
  tempAbroadNonSnCodeInfoUploadApi: (data = {}) => {
    return API.get(`/srm-purchase-pv/tenant/pv/delivery/abroad/sn/template/export`, data, {
      responseType: 'blob'
    })
  },
  // 供方-发货管理-SN码信息上载-海外业务-导出
  exportAbroadNonSnCodeInfoUploadApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/abroad/sn/supplier/excel/export`, data, {
      responseType: 'blob'
    })
  },
  // 供方-发货管理-SN码信息上载-海外/非组件业务-批量提交
  submitAbroadSnCodeInfoUploadApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/abroad/sn/batch/submit`, data)
  },
  // 供方-发货管理-SN码信息上载-海外/非组件业务-批量删除
  deleteAbroadSnCodeInfoUploadApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/abroad/sn/batch/delete`, data)
  },

  // 供方-发货管理-送货单列表-分页查询
  pageDeliveryNoteListApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/supplier/page/query`, data)
  },
  // 供方-发货管理-送货单明细-分页查询
  pageDetailDeliveryNoteListApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/item/supplier/page/query`, data)
  },
  // 供方-发货管理-送货单列表-取消
  cancelDeliveryNoteListApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/batch/cancel`, data)
  },
  // 供方-发货管理-送货单列表-上传签收单
  uploadDeliveryNoteListApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/supplier/upload/sign`, data)
  },
  // 供方-发货管理-送货单列表-批量推送极光
  pushDeliveryNoteListApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/batch/push/jg`, data)
  },
  // 供方-发货管理-送货单列表-导出
  exportDeliveryNoteListApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/supplier/excel/export`, data, {
      responseType: 'blob'
    })
  },
  // 供方-发货管理-送货单列表-打印
  printDeliveryNoteListApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/batch/print`, data, {
      responseType: 'blob'
    })
  },
  // 供方-发货管理-送货单明细-导出
  exportDeliveryNoteDetailApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/item/supplier/excel/export`, data, {
      responseType: 'blob'
    })
  },
  // 供方-发货管理-送货单列表-根据主单ID批量查询
  getDataByDeliveryIdsDeliveryNoteListApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/item/list/deliveryIds`, data)
  },

  // 供方-发货管理-供货计划-交货计划分页查询
  pageDeliveryScheduleApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/supply/plan/supplier/page/query`, data)
  },
  // 供方-发货管理-供货计划-采购订单分页查询
  pagePurchaseOrderApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/supply/order/supplier/page/query`, data)
  },
  // 供方-发货管理-供货计划-采购订单-创建送货单前置校验
  checkOrderSupplyPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/supply/order/supplier/create/check`, data)
  },
  // 供方-发货管理-供货计划-采购订单-创建送货单
  createOrderSupplyPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/supply/order/supplier/batch/create`, data)
  },
  // 供方-发货管理-供货计划-交货计划-创建送货单前置校验（废弃）
  checkPlanSupplyPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/supply/plan/supplier/create/check`, data)
  },
  // 供方-发货管理-供货计划-交货计划-预创建送货单
  preCreatePlanSupplyPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv//tenant/pv/supply/plan/supplier/pre/create`, data)
  },
  // 供方-发货管理-供货计划-交货计划-创建送货单
  createPlanSupplyPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/supply/plan/supplier/batch/create`, data)
  },
  // 供方-发货管理-预约送货-分页查询
  pageDeliveryAppointmentApi: (data = {}) => {
    return API.post(
      `/srm-purchase-execute/tenant/supplierForecastDelivery/query-tv?BU_CODE=GF`,
      data
    )
  },
  // 供方-发货管理-预约送货-保存
  saveDeliveryAppointmentApi: (data = {}) => {
    return API.post(`/srm-purchase-execute/tenant/supplierForecastDelivery/save?BU_CODE=GF`, data)
  },
  // 供方-发货管理-预约送货-提交
  submitDeliveryAppointmentApi: (data = {}) => {
    return API.post(`/srm-purchase-execute/tenant/supplierForecastDelivery/submit?BU_CODE=GF`, data)
  },
  // 供方-发货管理-预约送货-升级
  upgradeDeliveryAppointmentApi: (data = {}) => {
    return API.post(`/srm-purchase-execute/tenant/supplierForecastDelivery/level?BU_CODE=GF`, data)
  },
  // 供方-发货管理-预约送货-取消
  cancelDeliveryAppointmentApi: (data = {}) => {
    return API.post(`/srm-purchase-execute/tenant/supplierForecastDelivery/cancel?BU_CODE=GF`, data)
  },
  // 供方-发货管理-预约送货-导入
  importDeliveryAppointmentApi: (data = {}) => {
    return API.post(
      `/srm-purchase-execute/tenant/delivery/forecast/supplier/import?BU_CODE=GF`,
      data,
      {
        responseType: 'blob'
      }
    )
  },
  // 供方-发货管理-预约送货-导入模板下载
  downloadDeliveryAppointmentTemplateApi: (data = {}) => {
    return API.post(
      `/srm-purchase-execute/tenant/delivery/forecast/supplier/exportTemplate?BU_CODE=GF`,
      data,
      {
        responseType: 'blob'
      }
    )
  },
  // 供方-发货管理-预约送货-导出
  exportDeliveryAppointmentApi: (data = {}) => {
    return API.post(
      `/srm-purchase-execute/tenant/supplierForecastDelivery/pv/export?BU_CODE=GF`,
      data,
      {
        responseType: 'blob'
      }
    )
  },
  // 供方-发货管理-预约送货-打印
  printDeliveryAppointmentApi: (data = {}) => {
    return API.post(
      `/srm-purchase-execute/tenant/delivery/forecast/supplier/print?BU_CODE=GF`,
      data,
      {
        responseType: 'blob'
      }
    )
  },
  // 供方-发货管理-预约送货-查看车辆物流
  queryVehicleLogisticsApi: (data = {}) => {
    return API.post(
      `/srm-purchase-execute/tenant/supplierForecastDelivery/forecast/getTmsCarPositionUrl?BU_CODE=GF`,
      data
    )
  },
  // 供方-发货管理-预约送货-获取司机列表
  getDriverListApi: (data = {}) => {
    return API.post(`/srm-purchase-execute/tenant/supplierDriver/query?BU_CODE=GF`, data)
  },
  // 供方-发货管理-预约送货-获取预约时间段
  getForecastTimeApi: (data = {}) => {
    return API.post(
      `/srm-purchase-execute/tenant/supplierForecastDelivery/forecast/timeWithVmi?BU_CODE=GF`,
      data
    )
  }
}

export default {
  NAME,
  APIS
}
