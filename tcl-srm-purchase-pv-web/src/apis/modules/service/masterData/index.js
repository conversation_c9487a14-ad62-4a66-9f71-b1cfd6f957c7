import { API } from '@mtech-common/http'
import { PROXY_MASTER_DATA } from 'CONFIG/proxy.config'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'masterData'
const DEFAULTPARAM = {
  condition: '',
  page: {
    current: 1,
    size: 1000
  },
  pageFlag: false
}
const defaultCompanyParams = {
  organizationLevelCodes: ['ORG02'],
  orgType: 'ORG001PRO',
  includeItself: true,
  organizationIds: []
}
const APIS = {
  /**
   * @description: 获取指定组织下指定组织层级节点列表
   * @param {
   * organizationLevelCodes: [],//指定层级编码列表 公司：ORG02，部门：ORG03，岗位：ORG04，工厂：ORG06
   * organzationIds: []//指定组织id列表
   * }
   * @return {}
   */
  findSpecifiedChildrenLevelOrgs: (data = defaultCompanyParams) => {
    return API.post(`${PROXY_MASTER_DATA}/tenant/organization/findSpecifiedChildrenLevelOrgs`, data)
  },

  // 获取指定组织下员工
  getOrganizationEmployees: (data) => {
    return API.post(`${PROXY_MASTER_DATA}/tenant/organization/getOrganizationEmployees`, data)
  },

  /*
    租户级-业务组织接口
  */
  // 根据业务组织类型代码查询   采购组织列表：BUORG002ADM
  purchaseOraginaze: (data = {}) =>
    API.post(
      `${PROXY_MASTER_DATA}/tenant/business-organization/query-by-orgcode?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      data
    ),

  /**租户级-公司接口
   * @description 模糊查询 获取公司编码列表   companyCode: "",companyName: "",
   * @param {Object} data
   */
  getCompanyList: (data = {}) => API.post(`${PROXY_MASTER_DATA}/tenant/company/fuzzy-query`, data),

  // // 分页查询
  // getCompanyPageList: (data = {}) =>
  //   API.post(`${PROXY_MASTER_DATA}/tenant/company/paged-query`, data),

  // 租户级 - 业务组织 - 获取当前租户下的公司的业务组织
  getOrganizateByOrgId: (data = {}) =>
    API.get(
      `${PROXY_MASTER_DATA}/business-organization/getBusinessOrganizationByOrgId?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      data
    ),

  /** 租户级-部门接口
   * @description 模糊查询  获取部门列表  departmentName: "",
   * @param {Object} data
   */
  getDepartmentList: (data = {}) =>
    API.put(`${PROXY_MASTER_DATA}/tenant/department/fuzzy-query`, data),

  /*
    租户级-雇员接口   模糊查询
  */
  getUserListByTenantId: (data) =>
    API.get(`${PROXY_MASTER_DATA}/tenant/employee/fuzzy-query`, data),

  /*
    租户级-用户接口   模糊查询
  */
  getUserList: (data) => API.post(`${PROXY_MASTER_DATA}/tenant/user/criteria-query`, data),

  getDetail: (data) => API.post(`${PROXY_SOURCING}/tenant/expert/apply/query/detail`, data),

  getfuzzy: (data) => API.post(`${PROXY_SOURCING}/tenant/expert/apply/query/fuzzy`, data),

  // 雇员接口 分页查询
  getUserPageList: (data) => API.post(`${PROXY_MASTER_DATA}/tenant/employee/paged-query`, data),

  // 租户级-雇员接口-当前租户下员工列表
  getCurrentTenantEmployees: (data = {}) =>
    API.get(`${PROXY_MASTER_DATA}/tenant/employee/currentTenantEmployees`, data),

  /*
    获取公司下工厂
  */
  getCompanySite: (data = {}) =>
    API.get(
      `${PROXY_MASTER_DATA}/tenant/site/getSiteInfo?BU_CODE=${localStorage.getItem('currentBu')}`,
      data
    ),

  /*
   租户级-组织机构接口
  */
  getStatedLimitTree: (data = {}) => {
    //获取指定范围树结构数据
    //公司：ORG02，部门：ORG03，岗位：ORG04，员工：ORG05
    return API.post(`${PROXY_MASTER_DATA}/tenant/organization/getStatedLimitTree`, data)
  },

  /**
   * @description: 获取指定部门下员工列表
   * @param {}
   * @return {}
   */ findEmployeesInDeparments: (data = {}) => {
    return API.post(`${PROXY_MASTER_DATA}/tenant/organization/findEmployeesInDeparments`, data)
  },

  /*
    租户级-供应商信息接口
  */
  /**
   * @description:条件查询
   * @param {*}
   * @return {supplierCode、supplierName、id}
   */
  getSupplierList: (data = { supplierCode: '' }) =>
    API.post(`${PROXY_MASTER_DATA}/tenant/supplier/criteria-query`, data),
  getItemListUrls: `${PROXY_MASTER_DATA}/tenant/supplier/paged-query`,

  supplierPagedQuery: (data = {}) =>
    API.post(`${PROXY_MASTER_DATA}/tenant/supplier/paged-query`, data),

  supplierDistinctPagedQuery: (data = {}) =>
    API.post(`${PROXY_MASTER_DATA}/tenant/supplier/paged-query-distinct`, data),
  supplierDistinctPagedQueryNoScope: (data = {}) =>
    API.post(`${PROXY_MASTER_DATA}/tenant/supplier/paged-query-distinct-no-scope`, data),

  /*
   租户级-供应商编码获取供应商信息
  */
  getItemFindByCode: (data = {}) =>
    API.post(`${PROXY_MASTER_DATA}/tenant/supplier/findByCode`, data),

  /*
   租户级-品项接口
 */
  getItemList: (data = {}) =>
    API.post(
      `${PROXY_MASTER_DATA}/tenant/item/criteria-query?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      data
    ),
  getItemListUrl: `${PROXY_MASTER_DATA}/tenant/item/criteria-query?BU_CODE=${localStorage.getItem(
    'currentBu'
  )}`,
  getItemListUrlPage: `${PROXY_MASTER_DATA}/tenant/item/paged-query?BU_CODE=${localStorage.getItem(
    'currentBu'
  )}`,
  getItemListPageBySite: `${PROXY_MASTER_DATA}/tenant/item/paged-query-in-site?BU_CODE=${localStorage.getItem(
    'currentBu'
  )}`,
  getItemListPageBySitePcb: `${PROXY_MASTER_DATA}/tenant/item/paged-query?BU_CODE=${localStorage.getItem(
    'currentBu'
  )}`,

  getItemListUrlList: (data = {}) =>
    API.post(
      `${PROXY_MASTER_DATA}/tenant/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
      data
    ),
  /*
   租户级-品类接口
 */
  /**
   * @description: 条件查询
   * @param {*}
   * @return {categoryCode、categoryName、id}
   */
  getCategoryList: (data = {}) =>
    API.post(`${PROXY_MASTER_DATA}/tenant/category/criteria-query`, data),

  // 获取主品类树特定层级数据--认证场景定义
  getCategoryListByLevel: (data = {}) => {
    return API.post(`${PROXY_MASTER_DATA}/tenant/category/product-list`, data)
  },
  /**
   * 分页查询
   */
  categoryPagedQuery: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/permission/queryCategorys`, data),

  getCategoryListUrl: `${PROXY_MASTER_DATA}/tenant/category/criteria-query`,
  getCategoryPagedUrl: `${PROXY_MASTER_DATA}/tenant/category/paged-query`,

  // 丢失：品项历史价格: 租户级 - 货源信息接口
  getHistoryPrice: (data) =>
    API.get(`${PROXY_MASTER_DATA}/tenant/supply/source/item/history-price`, data),

  /**
   * @description: 根据条件获取货源信息
   * @param {}
   * @return {}
   */ getSupplySourceInfo: (data = {}) => {
    return API.post(`${PROXY_MASTER_DATA}/tenant/supply/source/infos`, data)
  },

  /** 租户级-SKU管理 */
  getSKUList: (data = DEFAULTPARAM) => API.post(`${PROXY_MASTER_DATA}/tenant/sku/page-query`, data),
  getSKUListUrl: `${PROXY_MASTER_DATA}/tenant/sku/page-query`,

  /*
    租户级-货币接口
  */
  /**
   * @description: 查询所有 币种
   * @param {*}
   * @return {currencyCode、currencyName、id}
   */
  queryAllCurrency: (data = {}) => {
    return API.post(`${PROXY_MASTER_DATA}/tenant/currency/queryAll`, data)
  },

  /*
    租户级-单位接口
  */
  /**
   * @description: 分页查询
   * @param {*}
   * @return {? ? ?}
   */
  pagedQueryUnitUrl: `${PROXY_MASTER_DATA}/tenant/unit/paged-query?BU_CODE=${localStorage.getItem(
    'currentBu'
  )}`,
  pagedQueryUnit: (data = DEFAULTPARAM) => {
    return API.post(
      `${PROXY_MASTER_DATA}/tenant/unit/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
      data
    )
  },

  /*
    租户级-税目税率接口
  */
  /**
   * @description: 查询所有
   * @param {*}
   * @return {taxItemCode、taxItemName、id}
   */
  queryAllTaxItem: (data = DEFAULTPARAM) => {
    return API.post(
      `${PROXY_MASTER_DATA}/tenant/tax-item/queryAll?BU_CODE=${localStorage.getItem('currentBu')}`,
      data
    )
  },

  /*
    租户级-地点接口
  */
  /**
   * @description:分页查询
   * @param {*}
   * @return {siteCode、siteName、id}
   */
  getSiteList: (data = DEFAULTPARAM) =>
    API.post(
      `${PROXY_MASTER_DATA}/tenant/site/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
      data
    ),

  getSiteListUrl: `${PROXY_MASTER_DATA}/tenant/site/paged-query?BU_CODE=${localStorage.getItem(
    'currentBu'
  )}`,

  //根据业务组织id获取指定类型组织节点
  findOrgByBuOrgIdAndOrgLevelTypeCode: (data = {}) =>
    API.get(
      `${PROXY_MASTER_DATA}/tenant/business-org-org-rel/findOrgByBuOrgIdAndOrgLevelTypeCode`,
      data
    ),

  //通过物料名称查询工厂
  criteriaQuery: (data = DEFAULTPARAM) =>
    API.post(
      `${PROXY_MASTER_DATA}/tenant/item-org-rel/criteria-query?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      data
    ),

  /*
   租户级-库位接口
 */
  //分页查询
  getLocationList: (data = {}) =>
    API.post(`${PROXY_MASTER_DATA}/tenant/location/paged-query`, data),

  /**
   * @description: 根据条件获取货组织机构
   * @param {}
   * @return {}
   */
  getOrgListByCid: (data) => {
    return API.get(
      `/masterDataManagement/tenant/business-organization/getBusinessOrganizationByOrgId?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      data
    )
  },
  //获取扩展
  // findBusinessOrgInfoByOrg: (data) => {
  //   return API.get(
  //     "/masterDataManagement/tenant/business-organization/findBusinessOrgInfoByOrg?BU_CODE=${localStorage.getItem('currentBu')}",
  //     data
  //   );
  // },

  // 租户级-成本中心接口-条件查询
  postCostCenterCriteriaQuery: (data = {}) =>
    API.post(
      `${PROXY_MASTER_DATA}/tenant/cost-center/criteria-query?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      data
    ),

  /*
    租户级-字典相关接口
  */
  /**
   * @description: 根据字典类型编码获取字典详情
   * 推荐合同类型:contractType  业务类型:businessType 项目策略:policyType
   * @param {*}
   * @return {itemCode、itemName、id}
   */
  dictionaryGetList: (data = {}) =>
    API.post(`${PROXY_MASTER_DATA}/tenant/dict-item/dict-code`, data),
  getDictionaryListURL: `${PROXY_MASTER_DATA}/tenant/dict-item/dict-code`,
  checkItemCode: (data = {}) => API.post(`${PROXY_SOURCING}/tenant/rfxItem/checkItemCode`, data),
  dictionaryGetTreeList: (data = {}) =>
    API.post(`${PROXY_MASTER_DATA}/tenant/dict-item/item-tree`, data),

  /**
   * @returns 供方字典接口
   */
  getSupplierDictionary: (data = {}) =>
    API.post(`${PROXY_MASTER_DATA}/tenant/supplier/dict-item/dict-code`, data),
  getSupplierDictionarys: (data = {}) =>
    API.post(
      `${PROXY_MASTER_DATA}/tenant/supplier/dict-item/getByDictCodes
    `,
      data
    ),
  /*
    添加数据权限后，使用以下接口
  */
  //获取允许操作的业务类型
  dictionaryGetBusinessType: () => API.get(`${PROXY_SOURCING}/tenant/permission/businessType`, {}),
  //获取允许操作的公司信息
  permissionCompanyList: (data) => {
    return API.get(`${PROXY_SOURCING}/tenant/permission/company`, data)
  },
  //获取允许操作的采购组织信息
  permissionOrgList: ({ orgId }) => {
    return API.get(
      `${PROXY_SOURCING}/tenant/permission/purOrgs?BU_CODE=${localStorage.getItem('currentBu')}`,
      {
        companyId: orgId
      }
    )
  },
  //获取允许操作的工厂信息
  permissionSiteList: ({ buOrgId, companyId }) =>
    API.get(
      `${PROXY_SOURCING}/tenant/permission/sites?BU_CODE=${localStorage.getItem('currentBu')}`,
      {
        purOrgId: buOrgId,
        companyId
      }
    ),

  // 组合物料查询
  packItemRelPagedQuery: (data = {}) =>
    API.post(`${PROXY_MASTER_DATA}/tenant/pack_item_rel/paged-query`, data),

  //查询采购组
  getbussinessGroup: (data = {}) =>
    API.post(
      `${PROXY_MASTER_DATA}/tenant/business-group/criteria-query?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      data
    ),

  // 获取需要的字典存入sessionStorage
  getNumDictListAllByCode: (data = {}) => {
    return API.post(`${PROXY_MASTER_DATA}/tenant/dict-item/getByDictCodes`, data)
  },

  // 根据工厂和物料获取采购组
  getPurGroup: (data = {}) => {
    return API.post(`${PROXY_MASTER_DATA}/tenant/item-purchasing/detail`, data)
  },

  // 根据公司和供应商获取品类
  queryCateByCompSup: (data = {}) => {
    return API.post(`${PROXY_MASTER_DATA}/tenant/supply/source/query-category`, data)
  },

  /**
   * 租户级-供应商主数据查询
   */
  // 供应商主数据查询
  getStandardSupplierInfoListUrl: `${PROXY_SOURCING}/tenant/mdm/supplier/paged-query`,
  getStandardSupplierInfoList: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/mdm/supplier/paged-query`, data)
  },

  /**
   * 租户级-根据权限查询工厂列表
   */
  getSiteListByPermission: (data = {}) => {
    return API.get(
      `${PROXY_MASTER_DATA}/tenant/site/findByPermission?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      data
    )
  },
  // 汇率接口
  getExchange: (data = {}) =>
    API.post(
      `${PROXY_MASTER_DATA}/tenant/currency-exchange/paged-query?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      data
    ),
  // 固定汇率 - 获取币种
  getCurrencyDataAll: (data = {}) =>
    API.get(`${PROXY_MASTER_DATA}/tenant/currency/queryActiveCurrency`, data),
  // 固定汇率 - 保存
  exchangeSave: (data = {}) =>
    API.post(`${PROXY_MASTER_DATA}/tenant/currency/exchange/vn/save`, data),
  // 固定汇率 - 启用 禁用
  exchangeStatusUpdate: (data = {}) =>
    API.post(`${PROXY_MASTER_DATA}/tenant/currency/exchange/vn/enable`, data),
  // 固定汇率 - 删除
  exchangeDelete: (data = {}) =>
    API.post(`${PROXY_MASTER_DATA}/tenant/currency/exchange/vn/delete`, data),

  // 获取公司
  getCompanyApi: (data = {}) =>
    API.post(`${PROXY_MASTER_DATA}/tenant/organization/specified-level-paged-query`, data),

  // 获取品项 分页查询
  getItemPage: (data = {}) =>
    API.post(
      `${PROXY_MASTER_DATA}/tenant/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
      data
    ),

  // 获取品类（外部物料组）- 条件查询
  pageCategoryApi: (data = {}) => API.post(`${PROXY_MASTER_DATA}/tenant/category/paged-query`, data)
}

export default {
  NAME,
  APIS
}
