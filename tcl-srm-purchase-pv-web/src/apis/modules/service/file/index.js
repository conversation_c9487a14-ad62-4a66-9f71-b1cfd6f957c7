import { API } from '@mtech-common/http'
import { PROXY_FILE } from 'CONFIG/proxy.config'
const NAME = 'fileService'
const APIS = {
  uploadPublicFile: (data = {}) => {
    //文件上传-公开类型
    return API.post(`${PROXY_FILE}/user/file/uploadPublic?useType=1`, data)
  },
  uploadPublicByUrl: (data = {}) => {
    //文件上传URL--公开类型
    return API.post(`${PROXY_FILE}/user/file/uploadPublicByUrl`, data)
  },
  downloadPublicFile: (params = {}) => {
    //文件下载-公开文件
    return API.get(`${PROXY_FILE}/user/file/downloadPublicFile`, params)
  },
  deletePublicFile: (data = {}) => {
    //文件删除--公开文件
    return API.get(`${PROXY_FILE}/user/file/deletePublicFile`, data)
  },
  uploadPrivateFileFullAPI: `/api${PROXY_FILE}/user/file/uploadPrivate?useType=2`,
  uploadPrivateFile: (data = {}) => {
    //文件上传-私密类型
    return API.post(`${PROXY_FILE}/user/file/uploadPrivate?useType=2`, data)
  },
  uploadPrivateByUrl: (data = {}) => {
    //文件上传URL--私密类型
    return API.post(`${PROXY_FILE}/user/file/uploadPrivateByUrl`, data)
  },
  // downloadPrivateFile: (params = {}) => {
  //   //文件下载-私密文件
  //   return API.get(`${PROXY_FILE}/user/file/downloadPrivateFile`, params);
  // },
  //文件下载-私密文件
  downloadPrivateFile: (data = {}) =>
    API.get(`${PROXY_FILE}/user/file/downloadPrivateFile?useType=2`, data, {
      responseType: 'blob'
    }),
  deletePrivateFile: (data = {}) => {
    //文件删除--私密文件
    return API.get(`${PROXY_FILE}/user/file/deletePrivateFile`, data)
  },

  // 文件预览
  getMtPreview: (data = {}) => API.get(`${PROXY_FILE}/user/file/mtPreview`, data),

  // 光伏-根据业务数据ID查询文件
  getFileListByIdApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/file/query/dataId`, data)
  },
  // 光伏-批量保存附件记录
  saveFileListApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/file/batch/save`, data)
  }
}

export default {
  NAME,
  APIS
}
