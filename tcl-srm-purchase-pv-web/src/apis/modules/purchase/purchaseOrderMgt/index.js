/********** 成本因子联动定价 ***********/

import { API } from '@mtech-common/http'
import { PROXY_PURCHASEPV } from 'CONFIG/proxy.config'

const NAME = 'purchaseOrderMgt'
const APIS = {
  // 查询列表-查询
  queryList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/order/queryPage`, data)
  },
  // 查询列表-导出
  exportList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/order/export`, data, {
      responseType: 'blob'
    })
  },
  // 采方/供方-列表视图-删除
  deleteList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/order/delete`, data)
  },
  // 采方/供方-列表视图-发布
  publishList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/order/publish`, data)
  },
  // 采方-列表视图-确认
  confirmList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/order/vmi/batch/confirm`, data)
  },
  // 采方-列表视图-提交OA审批
  submitOaList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/order/batch/submitOA`, data)
  },
  // 采方-列表视图-查看OA审批
  checkOaList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/order/checkOA`, data)
  },
  // 采方-列表视图-同步极光系统
  syncList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/order/sync`, data)
  },
  // 采方-列表视图-下载用印文件
  downloadSealFile: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/order/download/word`, data, {
      responseType: 'blob'
    })
  },
  // 供方-列表视图-批量确认
  batchConfirmList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/order/batchConfirm`, data)
  },
  // 采方/供方-明细视图-查询分页
  queryDetailList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/orderDetail/queryPage`, data)
  },
  // 采方/供方-仓库列表-查询分页
  queryPvWarehouseList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pv/warehouse/page/query`, data)
  },
  // 采方-明细视图-关闭
  closeDetailList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/orderDetail/close`, data)
  },
  // 采方/供方-明细视图-导出
  exportOrderDetailList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/orderDetail/export`, data, {
      responseType: 'blob'
    })
  },
  // 详情页-获取抬头信息
  getDetailHeader: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/order/detail/getHeader`, data)
  },
  // 详情页-获取明细列表
  getDetailList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/order/detail/getDetailList`, data)
  },
  // 详情页-获取附件列表
  getFileList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/order/detail/getFileList`, data)
  },
  // 详情页-详情页-保存
  saveDetail: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/order/detail/save`, data)
  },
  // 详情页-详情页-采方发布
  publishDetail: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/order/detail/publish`, data)
  },
  // 详情页-详情页-供方提交
  feedbackDetail: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/order/detail/feedback`, data)
  },
  // 采方-详情页-物料明细-获取导入模板
  getImportTemplate: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/order/detail/getImportTemplate`, data, {
      responseType: 'blob'
    })
  },
  // 采方-详情页-物料明细-导入
  importDetail: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/order/detail/importDetail`, data, {
      responseType: 'blob'
    })
  },
  // 采方-详情页-物料明细-导出
  exportDetailList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/order/detail/exportDetailList`, data, {
      responseType: 'blob'
    })
  },
  // 详情页-详情页-物料明细-执行交货已完成
  executeDeliveryFinish: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/order/detail/executeDeliveryFinish`, data)
  },
  // 详情页-详情页-物料明细-关闭
  closeDetail: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/order/detail/close`, data)
  },
  // 详情页-详情页-附件列表-删除
  deleteFile: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/order/detail/deleteFile`, data)
  }
}

export default {
  NAME,
  APIS
}
