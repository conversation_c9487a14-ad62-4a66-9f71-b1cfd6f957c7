import { API } from '@mtech-common/http'

const NAME = 'vmiManagement'

const APIS = {
  // 采方-VMI管理-库存水位-分页查询
  pageInventoryLevelApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/buyer/pull/jg`, data)
  },
  // 采方-VMI管理-库存水位-导出
  exportInventoryLevelApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/export`, data, {
      responseType: 'blob'
    })
  },
  // 采方-VMI管理-采购计划负责人-分页查询
  pagePlanManagerApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/buyer/planner/page/query`, data)
  },
  // 采方-VMI管理-采购计划负责人-新增
  addPlanManagerApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/buyer/planner/batch/add`, data)
  },
  // 采方-VMI管理-采购计划负责人-修改
  updatePlanManagerApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/buyer/planner/batch/update`, data)
  },
  // 采方-VMI管理-采购计划负责人-删除
  deletePlanManagerApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/buyer/planner/batch/del`, data)
  },
  // 采方-VMI管理-采购计划负责人-生效
  enablePlanManagerApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/buyer/planner/batch/take`, data)
  },
  // 采方-VMI管理-采购计划负责人-失效
  disablePlanManagerApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/buyer/planner/batch/lose`, data)
  },
  // 采方-VMI管理-送货计划-分页查询
  pageDeliveryPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/delivery/plan/buyer/page/query`, data)
  },
  // 采方-VMI管理-送货计划-通过
  passDeliveryPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/delivery/plan/batch/pass`, data)
  },
  // 采方-VMI管理-送货计划-拒绝
  rejectDeliveryPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/delivery/plan/batch/rejected`, data)
  },
  // 采方-VMI管理-送货计划-删除
  deleteDeliveryPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/delivery/plan/batch/delete`, data)
  },
  // 采方-VMI管理-送货计划-导出
  exportDeliveryPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/delivery/plan/buyer/export`, data, {
      responseType: 'blob'
    })
  },
  // 采方-VMI管理-送货计划-明细-分页查询
  pageDeliveryPlanDetailApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/delivery/plan/detail/buyer/page/query`, data)
  },
  // 采方-VMI管理-送货计划-明细-导出
  exportDeliveryPlanDetailApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/delivery/plan/detail/buyer/export`, data, {
      responseType: 'blob'
    })
  },
  // 采方-VMI管理-入库管理-分页查询
  pageInventoryManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/in/buyer/page/query`, data)
  },
  // 采方-VMI管理-入库管理-取消
  cancelInventoryManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/in/batch/cancel`, data)
  },
  // 采方-VMI管理-入库管理-关闭
  closeInventoryManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/in/batch/close`, data)
  },
  // 采方-VMI管理-入库管理-导出
  exportInventoryManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/in/buyuer/export`, data, {
      responseType: 'blob'
    })
  },
  // 采方-VMI管理-入库管理-打印
  printInventoryManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/in/batch/print`, data, {
      responseType: 'blob'
    })
  },
  // 采方-VMI管理-入库管理-同步极光
  syncInventoryManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/in/batch/push/jg`, data)
  },
  // 采方-VMI管理-入库管理-详情-分页查询
  pageInventoryManagementDetailApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/in/item/buyer/page/query`, data)
  },
  // 供方-VMI管理-入库管理-根据主单查询
  detailInventoryManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/in/item/list/stockInIds`, data)
  },
  // 采方-VMI管理-入库管理-详情-导出
  exportInventoryManagementDetailApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/stock/in/item/buyuer/export`, data, {
      responseType: 'blob'
    })
  },
  // 采方-VMI管理-出库记录-分页查询
  pageOutboundRecordApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/out/record/buyer/page/query`, data)
  },
  // 采方-VMI管理-出库记录-获取价格
  getPriceOutboundRecordApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/out/record/batch/update/price`, data)
  },
  // 采方-VMI管理-出库记录-转订单
  transferOrderOutboundRecordApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/out/record/batch/to/order`, data)
  },
  // 采方-VMI管理-出库记录-导出
  exportOutboundRecordApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/out/record/buyer/export`, data, {
      responseType: 'blob'
    })
  },
  // 采方-VMI管理-退货管理-分页查询
  pageReturnManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/return/order/buyer/page/query`, data)
  },
  // 采方-VMI管理-退货管理-取消
  cancelReturnManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/return/order/batch/cancel`, data)
  },
  // 采方-VMI管理-退货管理-导出
  exportReturnManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/return/order/buyer/export`, data, {
      responseType: 'blob'
    })
  },
  // 采方-VMI管理-退货管理-同步极光
  syncReturnManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/return/order/batch/push/jg`, data)
  },
  // 采方-VMI管理-退货管理-创建退货单
  createReturnManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/return/order/buyer/page/submit`, data)
  },
  // 采方-VMI管理-退货管理-明细-分页查询
  pageReturnManagementDetailApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/return/order/item/buyer/page/query`, data)
  },
  // 采方-VMI管理-退货管理-明细-根据主单id查询
  detailReturnManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/return/order/item/list/returnOrderIds`, data)
  },
  // 采方-VMI管理-退货管理-明细-导出
  exportReturnManagementDetailApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/vmi/return/order/item/buyer/export`, data, {
      responseType: 'blob'
    })
  }
}

export default {
  NAME,
  APIS
}
