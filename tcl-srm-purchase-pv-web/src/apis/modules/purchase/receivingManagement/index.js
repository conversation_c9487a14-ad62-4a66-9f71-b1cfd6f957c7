import { API } from '@mtech-common/http'

const NAME = 'receivingManagement'

const APIS = {
  // 采方-收货管理-SN码信息上载-户用/商用业务-分页查询
  pageCommercialSnCodeInfoUploadApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/commercial/sn/buyer/page/query`, data)
  },
  // 采方-收货管理-SN码信息上载-户用业务-导入
  importHouseholdSnCodeInfoUploadApi: (data = {}) => {
    return API.post(
      `/srm-purchase-pv/tenant/pv/delivery/commercial/sn/user/buyer/excel/import`,
      data,
      {
        responseType: 'blob'
      }
    )
  },
  // 采方-收货管理-SN码信息上载-商用业务-获取导入数据
  getImportDataCommercialSnCodeInfoUploadApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/commercial/sn/get/excel/data`, data)
  },
  // 采方-收货管理-SN码信息上载-商用业务-导入
  importCommercialSnCodeInfoUploadApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/commercial/sn/buyer/excel/import`, data, {
      responseType: 'blob'
    })
  },
  // 采方-收货管理-SN码信息上载-户用/商用业务-导入模板下载
  tempCommercialSnCodeInfoUploadApi: (data = {}) => {
    return API.get(`/srm-purchase-pv/tenant/pv/delivery/commercial/sn/template/export`, data, {
      responseType: 'blob'
    })
  },
  // 采方-收货管理-SN码信息上载-户用/商用业务-导出
  exportCommercialSnCodeInfoUploadApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/commercial/sn/buyer/excel/export`, data, {
      responseType: 'blob'
    })
  },
  // 采方-收货管理-SN码信息上载-户用/商用业务-批量提交
  submitCommercialSnCodeInfoUploadApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/commercial/sn/batch/submit`, data)
  },
  // 采方-收货管理-SN码信息上载-户用/商用业务-批量删除
  deleteCommercialSnCodeInfoUploadApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/commercial/sn/batch/delete`, data)
  },
  // 采方-收货管理-SN码信息上载-海外/非组件业务-分页查询
  pageAbroadSnCodeInfoUploadApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/abroad/sn/buyer/page/query`, data)
  },
  // 采方-收货管理-SN码信息上载-海外业务-导入
  importAbroadSnCodeInfoUploadApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/abroad/sn/component/excel/import`, data, {
      responseType: 'blob'
    })
  },
  // 采方-收货管理-SN码信息上载-海外业务-导入模板下载
  tempAbroadSnCodeInfoUploadApi: (data = {}) => {
    return API.get(
      `/srm-purchase-pv/tenant/pv/delivery/abroad/sn/component/template/export`,
      data,
      {
        responseType: 'blob'
      }
    )
  },
  // 采方-收货管理-SN码信息上载-海外业务-获取导入数据
  getImportDataAbroadSnCodeInfoUploadApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/abroad/sn/get/excel/data`, data)
  },
  // 采方-收货管理-SN码信息上载-非组件业务-导入
  importAbroadNonSnCodeInfoUploadApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/abroad/sn/excel/import`, data, {
      responseType: 'blob'
    })
  },
  // 采方-收货管理-SN码信息上载-非组件业务-导入模板下载
  tempAbroadNonSnCodeInfoUploadApi: (data = {}) => {
    return API.get(`/srm-purchase-pv/tenant/pv/delivery/abroad/sn/template/export`, data, {
      responseType: 'blob'
    })
  },
  // 采方-收货管理-SN码信息上载-海外业务-导出
  exportAbroadNonSnCodeInfoUploadApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/abroad/sn/buyer/excel/export`, data, {
      responseType: 'blob'
    })
  },
  // 采方-收货管理-SN码信息上载-海外/非组件业务-批量提交
  submitAbroadSnCodeInfoUploadApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/abroad/sn/batch/submit`, data)
  },
  // 采方-收货管理-SN码信息上载-海外/非组件业务-批量删除
  deleteAbroadSnCodeInfoUploadApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/abroad/sn/batch/delete`, data)
  },

  // 采方-收货管理-送货单列表-分页查询
  pageDeliveryNoteListApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/buyer/page/query`, data)
  },
  // 采方-收货管理-送货单明细-分页查询
  pageDetailDeliveryNoteListApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/item/buyer/page/query`, data)
  },
  // 采方-收货管理-送货单列表-签收确认
  confirmDeliveryNoteListApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/batch/sign`, data)
  },
  // 采方-收货管理-送货单列表-取消
  cancelDeliveryNoteListApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/batch/cancel`, data)
  },
  // 采方-收货管理-送货单列表-关闭
  closeDeliveryNoteListApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/batch/close`, data)
  },
  // 采方-收货管理-送货单列表-批量推送极光
  pushDeliveryNoteListApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/batch/push/jg`, data)
  },
  // 采方-收货管理-送货单列表-导出
  exportDeliveryNoteListApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/buyer/excel/export`, data, {
      responseType: 'blob'
    })
  },
  // 采方-收货管理-送货单列表-打印
  printDeliveryNoteListApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/batch/print`, data, {
      responseType: 'blob'
    })
  },
  // 采方-收货管理-送货单明细-导出
  exportDeliveryNoteDetailApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/item/buyer/excel/export`, data, {
      responseType: 'blob'
    })
  },
  // 采方-收货管理-送货单列表-根据主单ID批量查询
  getDataByDeliveryIdsDeliveryNoteListApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/delivery/item/list/deliveryIds`, data)
  },

  // 采方-收货管理-供货计划-交货计划分页查询
  pageDeliveryScheduleApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/supply/plan/buyer/page/query`, data)
  },
  // 采方-收货管理-供货计划-采购订单分页查询
  pagePurchaseOrderApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/supply/order/buyer/page/query`, data)
  },
  // 采方-收货管理-供货计划-采购订单-创建送货单前置校验
  checkOrderSupplyPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/supply/order/buyer/create/check`, data)
  },
  // 采方-收货管理-供货计划-采购订单-创建送货单
  createOrderSupplyPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/supply/order/buyer/batch/create`, data)
  },
  // 采方-收货管理-供货计划-交货计划-创建送货单前置校验（废弃）
  checkPlanSupplyPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/supply/plan/buyer/create/check`, data)
  },
  // 采方-收货管理-供货计划-交货计划-预创建送货单
  preCreatePlanSupplyPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv//tenant/pv/supply/plan/buyer/pre/create`, data)
  },
  // 采方-收货管理-供货计划-交货计划-创建送货单
  createPlanSupplyPlanApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/supply/plan/buyer/batch/create`, data)
  }
}

export default {
  NAME,
  APIS
}
