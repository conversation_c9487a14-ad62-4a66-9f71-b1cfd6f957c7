import { API } from '@mtech-common/http'

const NAME = 'demandManagement'

const APIS = {
  // 采购申请管理-分页查询
  pageProcurementRequestManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/purchase/apply/page/query`, data)
  },
  // 采购申请管理-新增
  addProcurementRequestManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/purchase/apply/batch/insert`, data)
  },
  // 采购申请管理-修改
  updateProcurementRequestManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/purchase/apply/batch/update`, data)
  },
  // 采购申请管理-删除
  deleteProcurementRequestManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/purchase/apply/batch/delete`, data)
  },
  // 采购申请管理-提交OA审批
  submitOAProcurementRequestManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/purchase/apply/submit`, data)
  },
  // 采购申请管理-关闭
  closeProcurementRequestManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/purchase/apply/batch/close`, data)
  },
  // 采购申请管理-导出
  exportProcurementRequestManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/purchase/apply/excel/export`, data, {
      responseType: 'blob'
    })
  },
  // 采购申请管理-明细删除
  deleteDetailProcurementRequestManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/purchase/apply/detail/batch/delete`, data)
  },
  // 采购申请管理-明细导入
  importDetailProcurementRequestManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/purchase/apply/detail/excel/import`, data, {
      responseType: 'blob'
    })
  },
  // 采购申请管理-明细导入模板下载
  tempDetailProcurementRequestManagementApi: (data = {}) => {
    return API.get(`/srm-purchase-pv/tenant/pv/purchase/apply/detail/template/export`, data, {
      responseType: 'blob'
    })
  },
  // 采购申请管理-明细导出
  exportDetailProcurementRequestManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/purchase/apply/detail/excel/export`, data, {
      responseType: 'blob'
    })
  },
  // 采购申请管理-根据ID查询
  detailProcurementRequestManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/purchase/apply/query/ids`, data)
  },
  // 采购申请管理-根据主单ID分页查询
  detailPageProcurementRequestManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/purchase/apply/detail/applyId/page/query`, data)
  },
  // 采购申请管理-根据ID查询附件
  fileProcurementRequestManagementApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/file/query/dataId`, data)
  },
  // 采购申请管理-物料明细-入库仓库
  queryPvWarehouseList: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/warehouse/page/query`, data)
  },
  // 需求池-分页查询
  pageDemandPoolApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/purchase/apply/detail/demand/page/query`, data)
  },
  // 需求池-批量退回
  returnDemandPoolApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/purchase/apply/detail/batch/return`, data)
  },
  // 需求池-转寻源
  toSourcingDemandPoolApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/purchase/apply/toSourcing`, data)
  },
  // 需求池-转订单
  toOrderDemandPoolApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/purchase/apply/detail/batch/to/order`, data)
  },
  // 需求池-生成订单
  createOrderDemandPoolApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/purchase/apply/detail/batch/generator/order`, data)
  },
  // 需求池-导出
  exportDemandPoolApi: (data = {}) => {
    return API.post(`/srm-purchase-pv/tenant/pv/purchase/apply/detail/demand/excel/export`, data, {
      responseType: 'blob'
    })
  }
}

export default {
  NAME,
  APIS
}
