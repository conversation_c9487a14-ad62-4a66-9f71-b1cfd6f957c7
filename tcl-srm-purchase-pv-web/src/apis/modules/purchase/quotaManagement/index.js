/********** 区域交货计划, 供应商计划交货限制 ***********/

import { API } from '@mtech-common/http'
import { PROXY_PURCHASEPV } from 'CONFIG/proxy.config'

const NAME = 'quotaManagement'
const APIS = {
  // 特殊配额维护-分页列表
  queryList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pvSpecialQuota/queryPage`, data)
  },
  // 特殊配额维护-导出
  exportList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pvSpecialQuota/export`, data, {
      responseType: 'blob'
    })
  },
  // 特殊配额维护-删除
  deleteList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pvSpecialQuota/delete`, data)
  },
  // 特殊配额维护-生效
  enableList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pvSpecialQuota/enable`, data)
  },
  // 特殊配额维护-失效
  disableList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pvSpecialQuota/disable`, data)
  },
  // 区域交货计划-获取导入模板
  getImportTemplate: (data = {}) => {
    return API.get(`${PROXY_PURCHASEPV}/tenant/pvSpecialQuota/getImportTemplate`, data, {
      responseType: 'blob'
    })
  },
  // 区域交货计划-导入
  importList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pvSpecialQuota/import`, data, {
      responseType: 'blob'
    })
  }
}

export default {
  NAME,
  APIS
}
