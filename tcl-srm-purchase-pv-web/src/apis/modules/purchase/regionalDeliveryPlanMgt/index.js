/********** 区域交货计划, 供应商计划交货限制 ***********/

import { API } from '@mtech-common/http'
import { PROXY_PURCHASEPV } from 'CONFIG/proxy.config'

const NAME = 'regionalDeliveryPlanMgt'
const APIS = {
  // 区域交货计划-查询
  queryList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/areaGoodsPlan/queryPage`, data)
  },
  // 区域交货计划-保存
  saveList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/areaGoodsPlan/save`, data)
  },
  // 区域交货计划-删除
  deleteList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/areaGoodsPlan/delete`, data)
  },
  // 区域交货计划-导出
  exportList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/areaGoodsPlan/export`, data, {
      responseType: 'blob'
    })
  },
  // 区域交货计划-获取导入模板
  getImportTemplate: (data = {}) => {
    return API.get(`${PROXY_PURCHASEPV}/tenant/areaGoodsPlan/getImportTemplate`, data, {
      responseType: 'blob'
    })
  },
  // 区域交货计划-导入
  importList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/areaGoodsPlan/import`, data, {
      responseType: 'blob'
    })
  },
  // 区域交货计划-自动分配供应商
  autoAssign: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/areaGoodsPlan/to/delivery/plan`, data)
  },
  // 区域分配订单-分页列表
  queryDetailList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/areaGoodsPlanAllocationOrder/queryPage`, data)
  },
  // 区域分配订单-关闭
  closeDetailList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/areaGoodsPlanAllocationOrder/close`, data)
  },
  // 区域分配订单-导出
  exportDetailList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/areaGoodsPlanAllocationOrder/export`, data, {
      responseType: 'blob'
    })
  },
  // 区域交货计划-分配订单弹窗-列表
  areaGoodsPlanAllocationList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/areaGoodsPlan/allocation/list`, data)
  },
  // 区域交货计划-分配订单弹窗-确定
  confirmAreaGoodsPlanAllocationList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/areaGoodsPlan/allocation/confirm`, data)
  },
  // 区域分配订单-分配订单弹窗-导出
  exportAreaGoodsPlanAllocationList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/areaGoodsPlan/allocation/export`, data, {
      responseType: 'blob'
    })
  },
  // 供应商计划交货限制-分页列表
  querySupplierPlanLimitList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/supplierPlanLimit/queryPage`, data)
  },
  // 区域交货计划-获取导入模板
  getSupplierPlanLimitImportTemplate: (data = {}) => {
    return API.get(`${PROXY_PURCHASEPV}/tenant/supplierPlanLimit/getImportTemplate`, data, {
      responseType: 'blob'
    })
  },
  // 区域交货计划-导入
  importSupplierPlanLimitList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/supplierPlanLimit/import`, data, {
      responseType: 'blob'
    })
  },
  // 供应商计划交货限制-导出
  exportSupplierPlanLimitList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/supplierPlanLimit/export`, data, {
      responseType: 'blob'
    })
  },
  // 供应商计划交货限制-保存
  saveSupplierPlanLimitList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/supplierPlanLimit/save`, data)
  },
  // 供应商计划交货限制-删除
  deleteSupplierPlanLimitList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/supplierPlanLimit/delete`, data)
  },
  // 供应商计划交货限制-生效
  enableSupplierPlanLimitList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/supplierPlanLimit/enable`, data)
  },
  // 供应商计划交货限制-失效
  disableSupplierPlanLimitList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/supplierPlanLimit/disable`, data)
  },
  // 交货计划管理-分页查询
  pageDeliveryPlanApi: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pv/delivery/plan/buyer/page`, data)
  },
  // 交货计划管理-新增
  addDeliveryPlanApi: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pv/delivery/plan/batch/add`, data)
  },
  // 交货计划管理-修改
  updateDeliveryPlanApi: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pv/delivery/plan/batch/buyer/update`, data)
  },
  // 交货计划管理-删除
  deleteDeliveryPlanApi: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pv/delivery/plan/batch/remove`, data)
  },
  // 交货计划管理-发布
  publishDeliveryPlanApi: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pv/delivery/plan/batch/publish`, data)
  },
  // 交货计划管理-关闭
  closeDeliveryPlanApi: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pv/delivery/plan/batch/close`, data)
  },
  // 交货计划管理-同步极光
  syncDeliveryPlanApi: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pv/delivery/plan/batch/push/jg`, data)
  },
  // 交货计划管理-导出
  exportDeliveryPlanApi: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pv/delivery/plan/buyer/export/reImport`, data, {
      responseType: 'blob'
    })
  },
  // 交货计划管理-导入
  importDeliveryPlanApi: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pv/delivery/plan/buyer/import`, data, {
      responseType: 'blob'
    })
  },
  // 交货计划管理-导入模板
  getDeliveryPlanImportTemplateApi: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pv/delivery/plan/buyer/export/template`, data, {
      responseType: 'blob'
    })
  }
}

export default {
  NAME,
  APIS
}
