/********** 成本因子联动定价 ***********/

import { API } from '@mtech-common/http'
import { PROXY_PURCHASEPV } from 'CONFIG/proxy.config'

const NAME = 'contract'
const APIS = {
  // 查询列表-查询
  queryContractList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pv/contract/queryPage`, data)
  },
  // 查询列表-导出
  exportContractList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pv/contract/export`, data, {
      responseType: 'blob'
    })
  },
  // 查询列表-批量删除
  deleteContract: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pv/contract/delete`, data)
  },
  // 查询列表-批量作废
  batchAbolish: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pv/contract/batchAbolish`, data)
  },
  // 查询列表-归档
  archivedContract: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pv/contract/archived`, data)
  },
  // 查询列表-批量转办
  transferContract: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pv/contract/transfer`, data)
  },
  // 查询列表-批量提交OA
  submit: (data = {}) => {
    return API.get(`${PROXY_PURCHASEPV}/tenant/pv/contract/submit`, data)
  },
  // 查询明细视图列表-查询
  queryContractDetailList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pv/contractDetail/queryPage`, data)
  },
  // 查询明细视图列表-合同转订单
  contractTransferOrder: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pv/contractDetail/transferOrder`, data)
  },
  // 查询明细视图列表-导出
  exportContractDetailList: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pv/contractDetail/export`, data, {
      responseType: 'blob'
    })
  },
  // 合同管理详情页
  getContractDetail: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pv/contract/detail`, data)
  },
  // 光伏价格记录查询
  queryPvPrice: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/feign/price/pv/query`, data)
  },
  // 合同管理详情新增/编辑界面抬头按钮逻辑-保存
  saveContractDetail: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pv/contract/save`, data)
  },
  // 合同管理详情新增/编辑界面物料明细按钮逻辑-下载导入模板
  getImportTemplate: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pv/contract/detail/getImportTemplate`, data, {
      responseType: 'blob'
    })
  },
  // 详情新增/编辑界面物料明细按钮逻辑-导入
  importContractItemDetail: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pv/contract/detail/import`, data, {
      responseType: 'blob'
    })
  },
  // 详情新增/编辑界面物料明细按钮逻辑- 导出
  exportContractItemDetail: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pv/contract/detail/exportDetail`, data, {
      responseType: 'blob'
    })
  },
  // 详情查看界面关联采购订单按钮逻辑- 导出
  exportPurchaseOrder: (data = {}) => {
    return API.post(`${PROXY_PURCHASEPV}/tenant/pv/contract/detail/exportPurchaseOrder`, data, {
      responseType: 'blob'
    })
  }
}

export default {
  NAME,
  APIS
}
