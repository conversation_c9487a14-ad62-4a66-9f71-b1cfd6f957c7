'use strict'
const path = require('path')
const proxy = require('./src/config/proxy.config')
// const DictionaryPlugin = require('@digis/dictionary-plugin') //需要导出‘国际化’字段库时，再解注释
// const BundleAnalyzerPlugin =
//   require("webpack-bundle-analyzer").BundleAnalyzerPlugin;

function resolve(dir) {
  return path.join(__dirname, dir)
}
const name = 'mtech-srm-main-web'

const port = process.env.port || process.env.npm_config_port || 8087 // dev port
// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
  publicPath: './',

  outputDir: 'dist',
  assetsDir: 'static',

  // lintOnSave: process.env.NODE_ENV === "development",
  lintOnSave: false,

  productionSourceMap: false,

  devServer: {
    port: port,
    open: true,
    overlay: {
      warnings: false,
      errors: true
    },
    clientLogLevel: 'info',
    proxy: proxy.PROXY_CONFIG
  },

  configureWebpack: {
    devtool: process.env.CONFIGURE_WEBPACK_DEVTOOL,
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    plugins: [
      // new DictionaryPlugin() // new DictionaryPlugin({NODE_ENV:'production'}) 执行的插件的环境 默认为'production'
    ],
    name: name,
    resolve: {
      alias: {
        '@': resolve('src'),
        CONFIG: resolve('src/config'),
        COMPONENTS: resolve('src/components'),
        ASSETS: resolve('src/assets'),
        STORE: resolve('src/store'),
        UTILS: resolve('src/utils'),
        MIXINS: resolve('src/mixins'),
        ROUTER_PUBLIC: resolve('src/views/public'),
        ROUTER_PURCHASE: resolve('src/views/purchase'),
        ROUTER_PURCHASE_SOR: resolve('src/views/purchase/sor'),
        ROUTER_PURCHASE_RFX: resolve('src/views/purchase/rfx'),
        ROUTER_PURCHASE_BIDDING: resolve('src/views/purchase/bidding'),
        ROUTER_PURCHASE_PRICE_BIDDING: resolve('src/views/purchase/priceBidding'),
        ROUTER_PURCHASE_PRICE: resolve('src/views/purchase/price'),
        ROUTER_PURCHASE_ASSESSMANAGE: resolve('src/views/purchase/assessManage'),
        ROUTER_PURCHASE_POINT: resolve('src/views/purchase/fixedPoint'),
        ROUTER_PURCHASE_SETTINGS: resolve('src/views/purchase/settings'),
        ROUTER_SUPPLY: resolve('src/views/supply'),
        ROUTER_SUPPLY_QB: resolve('src/views/supply/quotationBiddingOld'),
        ROUTER_SUPPLY_QUOTATION: resolve('src/views/supply/quotationBidding'),
        ROUTER_SUPPLY_PRICE: resolve('src/views/supply/price'),
        ROUTER_SUPPLY_PRICECONFIRM: resolve('src/views/supply/priceConfirm'),
        ROUTER_SUPPLY_ASSESSMANAGE: resolve('src/views/supply/assessFeedback'),
        ROUTER_SUPPLY_QM: resolve('src/views/supply/quotaManagement'),
        ROUTER_COMMON: resolve('src/views/common'),
        ROUTER_COMMON_SETTINGS: resolve('src/views/common/settings'),
        vue$: 'vue/dist/vue.esm.js'
        // vue$: 'vue-source/dist/vue.js'
      }
    }
  },

  chainWebpack(config) {
    // fix HMR un-useful
    config.resolve.symlinks(true)

    // it can improve the speed of the first screen, it is recommended to turn on preload
    config.plugin('preload').tap(() => [
      {
        rel: 'preload',
        // to ignore runtime.js
        // https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/cli-service/lib/config/app.js#L171
        fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
        include: 'initial'
      }
    ])

    // if (
    //   process.env.NODE_ENV === "development" &&
    //   !process.env.DISABLE_BUNDLE_ANALYZER_PLUGIN
    // ) {
    //   config.plugin("BundleAnalyzerPlugin").use(new BundleAnalyzerPlugin());
    // }

    // when there are many pages, it will cause too many meaningless requests
    config.plugins.delete('prefetch')

    // set svg-sprite-loader
    config.module.rule('svg').exclude.add(resolve('src/icons')).end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()
  },

  css: {
    requireModuleExtension: true, // 是否开启支持‘foo.module.css’样式
    extract: true, // 是否使用css分离插件 ExtractTextPlugin 抽离css
    sourceMap: process.env.NODE_ENV !== 'production' // 是否在构建样式地图，false将提高构建速度
  },

  pluginOptions: {
    'style-resources-loader': {
      preProcessor: 'sass',
      patterns: [path.resolve(__dirname, 'src/themes/_mtechUI.scss')]
    }
  }
}
