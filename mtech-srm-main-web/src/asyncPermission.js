import router from './router'
import store from './store'

router.beforeEach((to, from, next) => {
  const whiteRoutes = ['/login', '/404']
  if (whiteRoutes.includes(to.path)) {
    next()
  } else {
    // 判断是否有从后台请求的菜单, 在切换用户角色的时候主动清除了routes
    const hasRoutes = store.getters.routes && store.getters.routes.length > 0
    if (hasRoutes) {
      if (!store.getters.routes.some((item) => item.path === to.path)) {
        next(`/404`)
      }
      next()
    } else {
      next()
    }
  }
})
