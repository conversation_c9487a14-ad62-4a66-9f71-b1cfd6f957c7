import Vue from 'vue'

import { utils } from '@mtech-common/utils'
utils.setAppCode('srm')
import commonPermission from '@mtech/common-permission'
Vue.use(commonPermission)
//引入样式
// import "@mtech-ui/mtech-ui/build/themes/default.css";
// import "@mtech-ui/base/build/esm/bundle.css";
import '@mtech/mtech-common-layout/build/esm/bundle.css'
import '@mtech/common-loading/build/esm/bundle.css'
// import '@mtech/common-template-page/build/esm/bundle.css'

import GlobalToast from '@mtech-ui/global-toast'
// import MtCommonLayout from '@mtech/mtech-common-layout'
// import MtTemplatePage from '@mtech/common-template-page'
// import MtCommonUploader from '@mtech/mtech-common-uploader'
// import MtCalendar from '@mtech-ui/calendar'
// import MtMultilingualInput from '@digis/multilingual-input'
// import '@digis/multilingual-input/build/esm/bundle.css'

// Vue.use(MtMultilingualInput)
// Vue.component('mt-template-page', MtTemplatePage)
Vue.use(GlobalToast)
// Vue.use(MtCommonLayout)
// Vue.use(MtCommonUploader)
// Vue.component('mt-calendar', MtCalendar)
// svg
import './icons/iconfont'

import MtTabs from '@mtech-ui/tabs'
Vue.use(MtTabs)
