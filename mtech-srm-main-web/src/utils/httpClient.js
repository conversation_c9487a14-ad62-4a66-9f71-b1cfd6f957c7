import Vue from 'vue'
import { API, baseConfig } from '@mtech-common/utils'
baseConfig.setDefault({ baseURL: '/api' })

baseConfig.setTypeOfCode({
  200: { type: 'success', msg: '操作成功' },
  302: { type: 'success', msg: '未登录' },
  400: { type: 'error', msg: '账号异常，登录超时，请重新登录' },
  500: { type: 'error', msg: '系统异常，请稍后再试' },
  503: { type: 'error', msg: '数据异常，操作失败' }
})

baseConfig.addNotify({
  success: function (msg) {
    // 可以使用$toast组件，msg当前版本下默认为接口response.msg
    alert(`成功：${msg}`)
  },
  warning: function (msg) {
    alert(`警告：${msg}`)
  },
  error: function (msg) {
    alert(`错误：${msg}`)
  }
})

Vue.prototype.$http = API
