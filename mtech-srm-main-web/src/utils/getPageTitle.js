export const getPageTitle = (i18n, to) => {
  const routeMap = JSON.parse(sessionStorage.getItem('routeMap') || '{}')
  let resTitle = routeMap[to.path]
  if (resTitle) {
    // 要先做好国际化再存储起来以便在index.html中使用
    resTitle = `SRM-${i18n.t(resTitle)}`
    sessionStorage.setItem('lastDocumentTitle', resTitle)
  } else {
    const specilePages = ['/middlePlatform/dashboard', '/main/workbench']
    if (specilePages.includes(to.path)) {
      switch (to.path) {
        case '/middlePlatform/dashboard':
          resTitle = `SRM-${i18n.t('首页')}`
          break
        case '/main/workbench':
          resTitle = `SRM-${i18n.t('工作台')}`
          break
      }
    } else {
      const lastTitle = sessionStorage.getItem('lastDocumentTitle')
      resTitle = lastTitle ? lastTitle : `SRM-${i18n.t('首页')}`
    }
  }
  return resTitle
}
