import sensors from '../../../public/track-lib/crumb.es6.min.js'
import VueRouter from 'vue-router'

window.sensors = sensors
// 判断是测试环境还是生产环境
const isTestEnv = location.host.includes('eads') || location.host.includes('localhost')
let appId
let appToken
appId = isTestEnv ? 54 : 56
appToken = isTestEnv
  ? 'OTdjMjFhMWExYzFlNGY2MmI0N2EzYzkyMTlhZDg3NjM='
  : 'MmFiOWIwMWMzOTJhNDkzZjk5NTMwYjQ1ZjNjYjAyNWQ='
const serverUrl = `https://crumb.tclo2o.cn/api/collect/sdk/upload?appId=${appId}&appToken=${appToken}`
// console.log(serverUrl)
sensors.init({
  server_url: serverUrl,
  is_track_single_page: true, // 单页面配置，默认开启，若页面中有锚点设计，需要将该配置删除，否则触发锚点会多触发 $pageview 事件
  use_client_time: true,
  send_type: 'beacon',
  batch_send: true, // 开启批量发送
  heatmap: {
    // 是否开启点击图，default 表示开启，自动采集 $WebClick 事件，可以设置 'not_collect' 表示关闭。
    clickmap: 'default',
    collect_tags: {
      span: true
    },
    // 是否开启触达图，not_collect 表示关闭，不会自动采集 $WebStay 事件，可以设置 'default' 表示开启。
    scroll_notice_map: 'not_collect',
    has_session_id: true // 是否需要会话sessionId
  },
  // show_log: NODE_ENV !== 'production'
  show_log: false
})
// 注册公共属性
sensors.registerPage({
  application_name: 'SRM系统',
  $is_login_id: false,
  $user_agent: navigator.userAgent
})
sensors.quick('autoTrack') // 用于采集 $pageview 事件。
sensors.quick('watchSinglePage', VueRouter)

export default sensors
