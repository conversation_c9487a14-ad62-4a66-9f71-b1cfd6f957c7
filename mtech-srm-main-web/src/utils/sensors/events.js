import sensors from '@/utils/sensors'
import EVENT_NAMES from './events-name'

/**
 * 创建事件追踪器
 * @param {string} event                             - 事件名称
 * @param {Object} propsDescMap                      - 事件属性定义模型描述对象
 * @return {Function}                                - 对外提供的方法
 */
const newTracker = (event, propsDescMap) => {
  return (props) => {
    const finalProps = Object.assign(propsDescMap || {}, props || {})
    sensors.track(event, finalProps)
  }
}

/**
 * @description: 用户登录
 * @param {string} user_name 用户名
 * @param {boolean} is_success 是否登录成功
 */
export const trackSignIn = newTracker(EVENT_NAMES.SIGN_IN, {
  user_name: String,
  is_success: Boolean
})

/**
 * @description: 查询
 * @param {string} search_content 查询内容
 * @param {number} search_load_duration 查询加载时长
 * @param {number} search_count 查询数据条数
 */
export const trackSearch = newTracker(EVENT_NAMES.SEARCH, {
  search_content: String,
  search_load_duration: Number,
  search_count: Number
})

/**
 * @description: 页面加载
 * @param {date} enter_page_time 页面进入时间
 * @param {number} load_duration 页面加载时长
 * @param {string} return_error 报错提示
 */
export const trackPageLoad = newTracker(EVENT_NAMES.PAGELOAD, {
  enter_page_time: Date,
  load_duration: Number,
  return_error: String
})

export const sensorLogin = (id) => {
  sensors.login(id)
}

export const sensorLogout = () => {
  sensors.logout()
}

export const setLoginId = (b) => {
  sensors.registerPage({
    $is_login_id: b
  })
}

export const setProfile = (params) => {
  sensors.setProfile(params)
}

export const createSessionId = () => {
  sensors.creatSessionId()
  console.log(sensors.creatSessionId)
}
