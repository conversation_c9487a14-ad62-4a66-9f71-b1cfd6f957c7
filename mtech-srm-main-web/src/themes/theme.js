/*
 * @Author: your name
 * @Date: 2022-02-25 17:21:54
 * @LastEditTime: 2022-02-25 18:22:17
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-common\packages\utils\src\theme.js
 */
import cssVars from 'css-vars-ponyfill' // css var 的垫片

export default class Theme {
  constructor(id = '') {
    this.styleId = id
    this.isIE = this.checkIsIE()
  }

  change(theme) {
    const linkList = this.getLinkList()
    Array.from(linkList).forEach((link) => {
      let _url = link.href
      let url = new URL(_url)
      let _origin = url.origin

      if (!_url.includes('localhost')) {
        _origin = _origin + '/index'
      }
      const href = this.getLinkHref(_origin, theme)
      const newLink = this.createLink(href, theme)
      newLink.onload = this.deleteLink(_url)
      link.parentElement?.appendChild(newLink)
    })

    if (this.isIE) {
      setTimeout(() => {
        cssVars({
          watch: true
        })
      })
    }
  }

  add(theme, container, origin) {
    const href = this.getLinkHref(origin, theme)
    const link = this.createLink(href, theme)

    container.appendChild(link)
  }

  getLinkHref(origin, theme) {
    const href = `${origin}/themes/${theme}.css`

    return href
  }

  createLink(href, theme) {
    const link = document.createElement('link')
    link.setAttribute('rel', 'stylesheet')
    link.setAttribute('type', 'text/css')
    link.setAttribute('href', href)
    link.dataset.mtTheme = theme

    return link
  }

  deleteLink(href) {
    return () => {
      const linkList = this.getLinkList()
      const oldLink = Array.from(linkList).find((v) => v.href === href)
      if (oldLink) {
        oldLink.remove()
      }
    }
  }

  getLinkList() {
    const linkList = document.querySelectorAll('link[data-mt-theme]')
    return linkList
  }

  checkIsIE() {
    // TOOD: IE浏览器 的判断
    return false
  }
}
