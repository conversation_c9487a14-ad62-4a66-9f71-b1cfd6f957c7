import Vue from 'vue'
import './config.js'
import './sso.js'
import store from './store'
import router from './router'
import api from '@/apis'
import App from './App.vue'
import '@/icons'
import '@/main.scss'
import { routerGo, genActiveRule } from '@/utils/routerTo'
import { MtMicroMaster } from '@mtech-micro-frontend/vue-main'
// import MtMicroLoading from '@/components/micro-loading'
import { setLocal } from '@mtech-ui/base'
import indexDB from '@digis/internationalization'
import { getPageTitle } from '@/utils/getPageTitle'

import './mtechUi'
// import './asyncPermission' // 权限控制

//埋点
import '@/utils/sensors'

export const i18n = indexDB.digisI18n(Vue, 'srm')
window.i18n = i18n
Vue.config.productionTip = false
Vue.prototype.$api = api
Vue.prototype.$bus = new Vue()

const internationlization = localStorage.getItem('internationlization')
if (internationlization === 'zh' || internationlization === 'zh-CH') {
  setLocal('zh-CN')
}

// 设置默认时区
let timeZone = 0 - new Date().getTimezoneOffset() / 60
const localTimeZone = timeZone > 0 ? 'GMT+' + timeZone : 'GMT' + timeZone
localStorage.setItem('timeZone', localTimeZone)

router.beforeEach((to, from, next) => {
  document.title = getPageTitle(i18n, to)
  next()
})

// 拉到代码
// function theme(props) {
//   debugger;
//   const { entry, container } = props;

//   const changeTheme = new ChangeTheme();
//   const themeName = localStorage.getItem("mt-layout-theme") || "default";
//   changeTheme.add(themeName, container, entry);
// }
// theme({
//   entry: location.origin,
//   container: document.head,
// });
indexDB
  .layoutCreatLanguage()
  .then(() => {
    init()
  })
  .catch((err) => {
    console.log('indexDB_err', err)
    init()
  })

function init() {
  new Vue({
    router,
    store,
    i18n,
    render: (h) => h(App)
  }).$mount('#app')
  // const loader = MicroLoading(MtMicroLoading)

  let msg = {
    data: store.state,
    fns: [
      routerGo,
      function addVisitedViews(route) {
        const { name } = route
        if (name) {
          store.dispatch('tagsView/addView', route)
        }
      }
    ]
  }

  if (sessionStorage.getItem('tagsView')) {
    store.dispatch('tagsView/updataVisitedView', sessionStorage.getItem('tagsView'))
  }

  /**
   * Step1 注册子应用
   */
  const master = new MtMicroMaster(
    [
      {
        name: 'sourcing',
        entry:
          process.env.NODE_ENV === 'production'
            ? `//${document.domain}/sourcing/`
            : '//localhost:8081',
        // loader,
        activeRule: genActiveRule('#/sourcing'),
        // activeRule: "/sourcing",
        props: msg
      },
      {
        name: 'masterData',
        entry:
          process.env.NODE_ENV === 'production'
            ? `//${document.domain}/masterData/`
            : '//localhost:8083',
        // loader,
        activeRule: genActiveRule('#/masterdata'),
        // activeRule: "/sourcing",
        props: msg
      },
      {
        name: 'supplier',
        entry:
          process.env.NODE_ENV === 'production'
            ? `//${document.domain}/supplier/`
            : '//localhost:8082',
        // loader,
        activeRule: genActiveRule('#/supplier'),
        props: msg
      },
      {
        name: 'purchase-execute',
        entry:
          process.env.NODE_ENV === 'production'
            ? `//${document.domain}/purchase-execute/`
            : '//localhost:8084',
        // loader,
        activeRule: genActiveRule('#/purchase-execute'),
        props: msg
      },
      {
        name: 'middlePlatform',
        entry:
          process.env.NODE_ENV === 'production'
            ? `//${document.domain}/middlePlatform/`
            : '//localhost:8085',
        // loader,
        activeRule: genActiveRule('#/middlePlatform'),
        props: msg
      },
      {
        name: 'purchase-pv',
        entry:
          process.env.NODE_ENV === 'production'
            ? `//${document.domain}/purchase-pv/`
            : '//localhost:8087',
        // loader,
        activeRule: genActiveRule('#/purchase-pv'),
        props: msg
      }
    ],
    {
      beforeLoad: [
        (app) => {
          app.props.entry = app.entry
          console.log('[LifeCycle] before load %c%s', 'color: green;', app.name)
          console.log('[LifeCycle] before load %c%s', 'color: green;', app)
        }
      ],
      beforeMount: [
        (app) => {
          console.log(app)
          console.log('[LifeCycle] before mount %c%s', 'color: green;', app.name)
        }
      ],
      afterMount: [
        (app) => {
          console.log(app)
          console.log('[LifeCycle] after mount %c%s', 'color: green;', app.name)
        }
      ],
      afterUnmount: [
        (app) => {
          console.log('[LifeCycle] after unmount %c%s', 'color: green;', app.name)
          console.log(app)
        }
      ]
    }
  )

  master.addGlobalUncaughtErrorHandler((event) => {
    const { msg } = event
    if (msg && msg.includes('died in status LOADING_SOURCE_CODE')) {
      console.log('微应用加载失败，请检查应用是否可运行')
    }
  })

  /**
   * Step2 设置默认进入的子应用
   */
  // master.setDefaultMountApp("/sourcing"); //todo 需要先启动'子应用'

  /**
   * Step3 启动应用
   */
  master.start({
    prefetch: location.hostname.includes('main') ? 'all' : false,
    sandbox: {
      experimentalStyleIsolation: false
    }
  })

  master.runAfterFirstMounted(() => {
    console.log('[MainApp] first app mounted')
  })
}
