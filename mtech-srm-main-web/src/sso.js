import { sso } from '@mtech-sso/single-sign-on' //, hasAuth, clearAuth
sso({
  autoLogin: true,
  whiteList: new Set([]),
  authSuccessHandler: () => {
    // console.log("====================================");
    console.log('认证成功')
    // console.log("====================================");
  },
  // option
  authFailureHandler: (code, message) => {
    // console.log("====================================");
    console.log('认证失败', code, message)
    // console.log("====================================");
  }
  // loginWebUrl: 'http://localhost:9013/login'
})
