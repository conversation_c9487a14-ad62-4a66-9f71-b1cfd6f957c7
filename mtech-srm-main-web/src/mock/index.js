/**
 * 子路由下要配置rootId，如：rootId: "01",这样才有选中高亮的样式
 */
const publicRoutes = [
  {
    id: '07',
    name: '个人工作台',
    icon: 'icon_smooth_SupplierManagement',
    path: '/main/dashboard'
  },
  {
    id: '05',
    name: '主数据管理',
    icon: 'icon_smooth_Purchasingmeeting',
    children: [
      {
        id: '05-01',
        name: '物料主数据',
        parentId: '05',
        rootId: '05',
        children: [
          {
            id: '05-01-01',
            name: '物料主数据',
            rootId: '05'
          },
          {
            id: '05-01-02',
            name: '物料组织关系配置',
            rootId: '05'
          }
        ]
      },
      {
        id: '06',
        name: '组织用户管理',
        rootId: '05',
        children: [
          {
            id: '06-01',
            name: '组织管理',
            parentId: '06',
            rootId: '05',
            path: '/masterdata/org'
          },
          {
            id: '06-02',
            name: '企业管理',
            parentId: '06',
            rootId: '05',
            path: '/masterdata/company'
          },
          {
            id: '06-03',
            name: '员工管理',
            parentId: '06',
            rootId: '05',
            path: '/masterdata/staff'
          },
          {
            id: '06-04',
            name: '岗位管理',
            parentId: '06',
            rootId: '05',
            path: '/masterdata/station'
          }
        ]
      },
      {
        id: '05-03',
        name: '基础主数据',
        parentId: '05',
        rootId: '05',
        children: [
          {
            id: '05-03-01',
            name: '国家管理',
            rootId: '05',
            path: '/masterdata/base-management/set-country'
          },
          {
            id: '05-03-02',
            name: '地区管理',
            rootId: '05',
            path: '/masterdata/base-management/set-area'
          },
          {
            id: '05-03-03',
            name: '区域管理',
            rootId: '05',
            path: '/masterdata/base-management/set-region'
          },
          {
            id: '05-03-04',
            name: '货币管理',
            rootId: '05',
            path: '/masterdata/base-management/set-currency'
          },
          {
            id: '05-03-05',
            name: '汇率管理',
            rootId: '05',
            path: '/masterdata/base-management/set-exchange-rate'
          },
          {
            id: '05-03-06',
            name: '税种管理',
            rootId: '05',
            path: '/masterdata/base-management/set-tax-category'
          },
          {
            id: '05-03-07',
            name: '税率管理',
            rootId: '05',
            path: '/masterdata/base-management/set-tax-item'
          },
          {
            id: '05-03-08',
            name: '语言管理',
            rootId: '05',
            path: '/masterdata/base-management/set-language'
          },
          {
            id: '05-03-09',
            name: '时区管理',
            rootId: '05',
            path: '/masterdata/base-management/set-time-zone'
          },
          {
            id: '05-03-010',
            name: '单位管理',
            rootId: '05',
            path: '/masterdata/base-management/set-unit'
          },
          {
            id: '05-03-011',
            name: '字典管理',
            rootId: '05',
            path: '/masterdata/base-management/set-dictionary'
          }
          // {
          //   id: "05-03-012",
          //   name: "字典明细管理",
          //   rootId: "05",
          //   path: "/masterdata/base-management/set-dictionary-detail",
          // },
        ]
      },
      {
        id: '05-04',
        name: '系统配置',
        parentId: '05',
        rootId: '05',
        children: [
          {
            id: '05-04-01',
            name: '序列号生成器',
            rootId: '05',
            path: '/masterdata/system-setting/code-generator'
          }
        ]
      }
    ]
  },
  {
    id: '04',
    name: '后台设置',
    icon: 'icon_smooth_SystemSettings_1',
    children: [
      {
        id: '04-01',
        name: '角色管理',
        rootId: '04',
        path: '/masterdata/role-management'
      },
      {
        id: '04-02',
        name: '数据权限',
        parentId: '04',
        rootId: '04',
        path: '/masterdata/data-authority'
      }
    ]
  }
]

const routesIn = [
  ...publicRoutes,
  {
    id: '01',
    name: '寻源管理',
    icon: 'icon_smooth_StrategicSourcing',
    children: [
      {
        id: '01-01',
        name: '寻源需求管理',
        parentId: '01',
        rootId: '01',
        children: [
          {
            id: '01-01-01',
            name: '需求汇总管理',
            path: '/sourcing/require-summary',
            parentId: '01-01',
            rootId: '01'
          },
          {
            id: '01-01-02',
            name: '我的需求管理',
            path: '/sourcing/require-mine',
            parentId: '01-01',
            rootId: '01'
          }
        ]
      },
      {
        id: '02-03',
        name: '寻源项目管理',
        rootId: '01',
        children: [
          // {
          // id: "02-03-01",
          // name: "项目管理",
          // rootId: "01",
          // },
          {
            id: '02-03-02',
            name: '询标大厅',
            rootId: '01',
            path: '/sourcing/bid-hall'
          },
          {
            id: '02-03-03',
            name: '核价管理',
            rootId: '01'
          },
          {
            id: '02-03-04',
            name: '评标管理',
            rootId: '01'
          },
          {
            id: '02-03-05',
            name: '定点管理',
            rootId: '01'
          }
        ]
      },
      {
        id: '02-01',
        name: '项目配置',
        rootId: '01',
        children: [
          {
            id: '02-01-01',
            name: '策略地图',
            path: '/sourcing/strategy-maps',
            rootId: '01'
          },
          {
            id: '01-01-03-01',
            name: '需求分组规则配置',
            path: '/sourcing/require-config-group',
            parentId: '01-01-03',
            rootId: '01'
          },
          {
            id: '01-01-03-02',
            name: '需求分配策略配置',
            path: '/sourcing/require-config-strategy',
            parentId: '01-01-03',
            rootId: '01'
          },
          // {
          // id: "01-01-03-03",
          // name: "模块功能流程配置",
          // path: "/sourcing/require-config-module",
          // parentId: "01-01-03",
          // rootId: "01",
          // },
          {
            id: '02-01-02',
            name: '成本模型',
            path: '/sourcing/cost-collocation',
            rootId: '01'
          },
          {
            id: '02-01-03',
            name: '专家管理',
            rootId: '01'
          },
          {
            id: '02-01-04',
            name: '业务类型字段配置',
            path: '/sourcing/business-config',
            rootId: '01'
          },
          {
            id: '01-02',
            name: '业务类型维护',
            path: '/sourcing/require-business-type',
            parentId: '01',
            rootId: '01'
          }
        ]
      },
      {
        id: '02-02',
        name: '模板配置',
        rootId: '01',
        children: [
          {
            id: '02-02-01',
            name: '供应商招募模板',
            rootId: '01'
          },
          {
            id: '02-02-02',
            name: '中标公告模板',
            rootId: '01'
          }
        ]
      },
      {
        id: '02-06',
        name: '会议管理',
        rootId: '01',
        children: [
          {
            id: '02-06-01',
            name: '会议议题',
            rootId: '01'
          },
          {
            id: '02-06-02',
            name: '会议管理',
            rootId: '01'
          }
        ]
      }
    ]
  },
  {
    id: '03',
    name: '供应商管理',
    icon: 'icon_smooth_SupplierQuality',
    path: '',
    children: [
      {
        id: '03-01',
        name: '供应商邀请管理',
        path: '/supplier/supplierInvitaion',
        parentId: '03',
        rootId: '03'
      },
      {
        id: '03-02',
        name: '供应商准入',
        parentId: '03',
        rootId: '03',
        children: [
          {
            id: '03-02-01',
            name: '集团准入供应商',
            path: '/supplier/supplierAccess',
            parentId: '03-02',
            rootId: '03'
          },
          {
            id: '03-02-02',
            name: '公司准入供应商',
            path: '/supplier/supplierAccessForCompany',
            parentId: '03-02',
            rootId: '03'
          }
        ]
      },
      {
        id: '03-03',
        name: '调查表管理',
        parentId: '03',
        rootId: '03',
        children: [
          {
            id: '03-03-01',
            name: '调查表填写',
            path: '/supplier/questionnaire-write',
            parentId: '03-03',
            rootId: '03'
          },
          {
            id: '03-03-02',
            name: '调查表审批',
            path: '/supplier/questionnaire-approve',
            parentId: '03-03',
            rootId: '03'
          }
        ]
      },
      {
        id: '03-04',
        name: '模块配置',
        parentId: '03',
        rootId: '03',
        children: [
          {
            id: '03-04-01',
            name: '阶段配置管理',
            path: '/supplier/stage-config',
            parentId: '03-04',
            rootId: '03'
          }
        ]
      }
    ]
  },

  {
    id: '08',
    name: '采购执行',
    icon: 'icon_smooth_Requirementschedule',
    children: [
      {
        id: '08-01',
        name: '采购申请',
        path: '/purchase-execute/purchase-request',
        parentId: '08',
        rootId: '08'
      },
      {
        id: '08-02',
        name: '采购订单',
        path: '/purchase-execute/purchase-order',
        parentId: '08',
        rootId: '08'
      }
    ]
  }
]

const routesSupplier = [
  ...publicRoutes,
  {
    id: '01',
    name: '客户准入管理',
    icon: 'icon_smooth_Customermanagement',
    path: '',
    children: [
      {
        id: '01-01',
        name: '客户邀约管理',
        icon: '',
        path: '/supplier/customer-invitation',
        parentId: '01',
        rootId: '01'
      },
      {
        id: '01-02',
        name: '客户准入管理',
        icon: '',
        path: '/supplier/customer-access',
        parentId: '01',
        rootId: '01'
      }
    ]
  },
  {
    id: '02',
    name: '客户寻源管理',
    icon: 'icon_smooth_Customermanagement',
    path: '',
    children: [
      {
        id: '02-04',
        name: '报价投标管理',
        rootId: '02',
        children: [
          {
            id: '02-04-01',
            name: '报价投标',
            path: '/sourcing/offer-bid',
            rootId: '02'
          }
        ]
      }
    ]
  }
]

module.exports = {
  routesIn,
  routesSupplier
}
