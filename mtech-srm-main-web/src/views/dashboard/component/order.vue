<template>
  <div class="announcement-list">
    <div class="announcement-list-content">
      <div class="list-item-header">
        <div class="list-item-title">
          <img src="../../../assets/订单.png" class="dontlist-icon" />

          订单
        </div>
      </div>
      <div class="list-item-bottom">
        <div
          style="width: 50%"
          v-for="(item, index) in OrderList"
          :key="index"
          @click="linkToDetail(item)"
        >
          <div v-if="index === 0">
            <img src="../../../assets/新订单.png" class="dontlist-icon" />
          </div>
          <div v-if="index === 1">
            <img src="../../../assets/订单更新.png" class="dontlist-icon" />
          </div>
          <div class="list-item-bottom-new">
            {{ item.taskBusinessName }}
          </div>
          <div :class="[item.totalCount > 0 ? 'list-item-bottom-new-red' : 'list-item-bottom-new']">
            ({{ item.totalCount }})
          </div>
        </div>
        <!-- <div style="width: 50%">
          <div>
            <img src="../../../assets/订单更新.png" class="dontlist-icon" />
          </div>
          <div class="list-item-bottom-new">订单更新</div>
          <div class="list-item-bottom-new">()</div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    // eslint-disable-next-line vue/prop-name-casing
    OrderList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {}
  },
  methods: {
    linkToDetail(args) {
      console.log('详情', args)
      this.$router.push({
        path: `/${args.urlRefFront}`,
        query: { from: 'mytodo' }
      })
      sessionStorage.setItem('todoDetail', JSON.stringify(args.requestParam))
    }
  }
}
</script>

<style lang="scss" scoped>
.announcement-list {
  // max-width: calc(50% - 16px);
  width: 100%;
  border-radius: 10px;
  overflow: hidden;
  background: #fff;
  // padding: 0 24px 4px;
  .list-item-bottom {
    text-align: center;
    display: flex;
    .list-item-bottom-new {
      margin-top: 5px;
      cursor: pointer;
    }
    .list-item-bottom-new-red {
      margin-top: 5px;
      color: red;
      cursor: pointer;
    }
  }
  .list-item {
    &-content {
      padding: 0 24px 4px;
    }
    &-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 24px;
    }
    &-title {
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      padding: 16px 0;
    }
    &-more {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 12px;
      color: #3678fe;
      cursor: pointer;
      user-select: none;
    }
  }
  .list-item-content {
    overflow: hidden;
  }
  .list-item-area {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    cursor: pointer;
    .sort-number {
      font-family: Helvetica;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
    }
    .clock-img {
      display: block;
      min-width: 40px;
      max-width: 40px;
      height: 40px;
      background: #eef2fd;
      border-radius: 8px;
      margin: 0 8px;
    }
    .list-detail {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      width: calc(100% - 65px);
      .list-title {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 14px;
        color: #333333;
      }
      .list-time {
        font-size: 12px;
        color: #999999;
      }
    }
  }
  .list-no-data {
    min-height: 198px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #d8d8d8;
  }
}
</style>
