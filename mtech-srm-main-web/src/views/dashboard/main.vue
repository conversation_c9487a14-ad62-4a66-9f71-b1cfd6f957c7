<template>
  <div v-if="isShow" class="dashboard-container">
    <div v-if="false" class="system-analys">
      <span class="system-analys-title">{{ $t('数据系统分析') }}</span>
      <div class="system-analys-area">
        <div class="system-analys-content">
          <div v-for="item in 7" :key="item" class="system-analys-content__item">
            <img src="./../../assets/icon_done.png" class="dontlist-icon" />
            <div>
              <span class="system-analys-content__num">23000</span>
              <span class="system-analys-content__text">{{ $t('创建送货单') }}</span>
            </div>
            <img src="./../../assets/icon_delete.png" class="delete-icon" />
          </div>
        </div>
        <span class="system-analys-line" />
        <div class="system-analys-addarea">
          <img src="./../../assets/icon_add.png" />
          <span>{{ $t('添加') }}</span>
        </div>
      </div>
    </div>
    <div v-if="userInfo && userInfo.tenantId !== '10000'">
      <div style="width: 100%" class="dashboard-list-item-parent">
        <div style="width: 70%" class="dashboard-list-one">
          <div class="dashboard-list-one-item">
            <order class="paddingBo" :order-list="orderList" style="width: 35%"></order>
            <delivery class="paddingBo" :todo-list="deliverList" style="width: 63%"></delivery>
          </div>
          <div class="dashboard-list-one-item-two">
            <quality class="paddingBo" :quality-list="qualityList" style="width: 35%"></quality>
            <out-sour-cing
              class="paddingBo"
              :todo-list="outList"
              style="width: 63%"
            ></out-sour-cing>
          </div>
        </div>
        <announcement-list
          style="width: 30%; float: right"
          :announcement-list="announcementList"
          class="dashboard-list-item"
        />
      </div>
      <div style="width: 100%" class="dashboard-list-item-parent-two">
        <div style="width: 70%" class="dashboard-list-one">
          <div class="dashboard-list-one-item">
            <row-line :todo-list="drawingList" class="paddingBo" style="width: 100%"></row-line>
          </div>
          <div class="dashboard-list-one-item-two">
            <row-line :todo-list="predictList" class="paddingBo" style="width: 100%"></row-line>
          </div>
          <div class="dashboard-list-one-item-two">
            <row-line :todo-list="steelList" class="paddingBo" style="width: 100%"></row-line>
          </div>
          <div class="dashboard-list-one-item-two">
            <row-line :todo-list="sourceList" class="paddingBo" style="width: 100%"></row-line>
          </div>
          <div class="dashboard-list-one-item-two">
            <row-line
              :todo-list="reconcilicationList"
              class="paddingBo"
              style="width: 100%"
            ></row-line>
          </div>
        </div>

        <platform-list-item
          :todo-list="warningList"
          style="width: 30%; float: right"
          class="dashboard-list-item"
        />
      </div>
    </div>
    <div v-else>
      <div style="width: 100%" class="dashboard-list-item-parent">
        <announcement-list
          style="width: 30%; float: right"
          :announcement-list="announcementList"
          class="dashboard-list-item"
        />
        <div style="width: 70%; margin-left: 20px" class="dashboard-list-one">
          <div class="dashboard-list-one-item">
            <deliveryBuy
              class="paddingBo"
              :todo-list="deliverList"
              style="width: 100%"
            ></deliveryBuy>
          </div>
          <div class="dashboard-list-one-item-two">
            <out-sour-cing
              class="paddingBo"
              :todo-list="outList"
              style="width: 100%"
            ></out-sour-cing>
          </div>
          <div class="dashboard-list-one-item-two">
            <!-- 配额调整通知 -->
            <quotaBuy
              class="paddingBo"
              :quota-buy-list="quotaBuyList"
              style="width: 100%"
            ></quotaBuy>
          </div>
        </div>
      </div>
      <div style="width: 100%" class="dashboard-list-item-parent-two">
        <platform-list-item
          :todo-list="warningList"
          style="width: 30%; float: right"
          class="dashboard-list-item"
        />
      </div>
    </div>

    <!-- dashboard-list-item <div class="dashboard-list" style="width: 30%; float: right"> -->
    <!-- <announcement-list :announcement-list="announcementList" class="dashboard-list-item" /> -->
    <!-- <platform-list-item :todo-list="todoList" class="dashboard-list-item" /> -->
    <!-- <to-do-list
        v-if="userInfo.tenantId !== '10000'"
        :todo-new-list="todoNewList"
        class="dashboard-list-item"
      /> -->

    <!-- <platform-list-item :todo-list="warningList" class="dashboard-list-item" /> -->
    <!-- </div> -->
    <!-- <div >
      <to-do-list :todo-new-list="todoNewList" class="dashboard-list-item" />
    </div> -->
    <div v-if="false" class="data-download">
      <div class="data-download-header">{{ $t('资料下载') }}</div>
      <mt-data-grid :column-data="columnData" :data-source="fileData"></mt-data-grid>
    </div>
  </div>
  <div v-else></div>
</template>

<script>
import PlatformListItem from './component/platform-list-item.vue'
import AnnouncementList from './component/announcement-list.vue'
// import ToDoList from './component/todo-list.vue'
import order from './component/order.vue'
import delivery from './component/delivery.vue'
import deliveryBuy from './component/deliveryBuy.vue'
import quotaBuy from './component/quotaBuy.vue'
import outSourCing from './component/delivery.vue'
import quality from './component/quality.vue'
import rowLine from './component/rowLine.vue'
import { fileData } from './mock/index'
import { downloadColumns } from './config/columns'
export default {
  components: {
    PlatformListItem,
    AnnouncementList,
    // ToDoList,
    deliveryBuy,
    rowLine,
    order,
    delivery,
    outSourCing,
    quality,
    quotaBuy
  },
  data() {
    return {
      todoList: [],
      warningList: [],
      userInfo: null,
      deliverList: [],
      quotaBuyList: [{}, {}],
      columnData: [],
      qualityList: [],
      drawingList: [],
      predictList: [],
      steelList: [],
      outList: [],
      announcementList: [],
      todoNewList: [],
      sourceList: [],
      reconcilicationList: [],
      orderList: [],
      fileData: [],
      hasTodo: false,
      isPurchase: false, // 判断是是否为采方页面
      isShow: false // 是否有权限查看页面
    }
  },
  created() {
    // this.announcementList = announcementList
    this.columnData = downloadColumns
    this.fileData = fileData
  },
  watch: {
    $route: {
      handler: 'setIsPurchase',
      immediate: true
    }
  },
  methods: {
    setIsPurchase() {
      // 判断当前路由是否配了菜单权限, 没有权限就不显示内容
      setTimeout(() => {
        // 加定时器解决watch执行时sessionstorage还没比set时就get导致的报错
        const routeInfo = JSON.parse(sessionStorage.getItem('routeInfo'))
        this.userInfo = sessionStorage.getItem('userInfo')
          ? JSON.parse(sessionStorage.getItem('userInfo'))
          : null
        if (routeInfo) {
          const routeList = [...routeInfo.routesIn, ...routeInfo.routesSupplier]
          if (routeList && routeList.some((i) => i.path == this.$route.path)) {
            this.isShow = true
          }
        }
      }, 2000)
      // 判断当前地址是否为采购方
      this.isPurchase = this.$route.path === '/main/dashboard'
      this.getAnnouncementList({ page: { size: 10, current: 1 } }).then((res) => {
        const { code, data } = res
        if (code === 200) {
          data.records.forEach((item) => {
            item.releaseTime = item.releaseTime.substr(0, 11)
          })
          this.announcementList = data.records
        }
      })
      this.getTodoList({ current: 1, size: 10 })
        .then((res) => {
          const { code, data } = res
          console.log(res, 'res')
          if (code === 200) {
            this.todoList = [data[0], data[1]]
            this.warningList = [data[2], data[3]]
          }
        })
        .catch(() => {
          this.todoList = [
            {
              todoTab: 0,
              count: '0',
              record: []
            },
            {
              todoTab: 1,
              count: '0',
              record: []
            }
          ]
          this.warningList = [
            {
              todoTab: 2,
              count: '0',
              record: []
            },
            {
              todoTab: 3,
              count: '0',
              record: []
            }
          ]
        })
      if (this.isPurchase) {
        this.getNewBuyTodoList({
          page: { size: 10, current: 1 }
        }).then((res) => {
          this.deliverList = res.data[0].record
          this.outList = res.data[1].record
        })
        this.getNewBuyQuotaList({
          page: { current: 1, size: 10 }
        })
      } else {
        // new 9.28 新待办
        this.getNewSupTodoList({
          page: { size: 10, current: 1 }
        }).then((res) => {
          this.orderList = res.data[0].record
          this.deliverList = res.data[1].record
          this.outList = res.data[4].record
          this.qualityList = res.data[2].record
          this.drawingList = res.data[8]
          this.predictList = res.data[3]
          this.steelList = res.data[5]
          this.sourceList = res.data[6]
          this.reconcilicationList = res.data[7]
          console.log(this.orderList)
        })
      }
    },
    // 获取配额调整通知信息
    getNewBuyQuotaList(param) {
      this.$api.source.getQuotaWaitList(param).then((res) => {
        const _obj = {
          taskGroupName: this.$t('配额管理'),
          totalCount: res.data ?? 0,
          taskBusinessName: this.$t('配额调整通知'),
          urlRefFront: '/sourcing/quota-calc/quota-adjustment-notice'
        }
        this.quotaBuyList[0] = _obj
      })
    },
    // new 待办
    getNewSupTodoList(params) {
      return this.$api.source.getNewSupTodoList(params)
    },
    // new 采方待办
    getNewBuyTodoList(params) {
      return this.$api.source.getBuyTodoList(params)
    },
    // 获取待办通知列表
    getTodoList(params) {
      // 判断是否为采方，调用采方接口
      if (this.isPurchase) {
        return this.$api.source.getPurTodoList(params)
      }
      return this.$api.source.getSupTodoList(params)
    },
    getBuyTodoList(params) {
      // 判断是否为采方，调用采方接口
      if (this.isPurchase) {
        return this.$api.source.getBuyTodoList(params)
      }
    },
    // 获取系统公告列表
    getAnnouncementList(params) {
      if (this.isPurchase) {
        return this.$api.source.getPurAnnouncement(params)
      }
      return this.$api.source.getSupAnnouncement(params)
    }
  }
}
</script>

<style>
.mt-icon-icon_ppt {
  color: rgb(237, 86, 51);
}
.mt-icon-icon_word {
  color: rgb(99, 134, 193);
}
.mt-icon-icon_pdf {
  color: rgb(218, 62, 26);
}
.mt-icon-icon_excel {
  color: rgb(138, 204, 64);
}
</style>

<style lang="scss" scoped>
.dashboard-container {
  padding-top: 16px;
  .dashboard-list-item-parent {
    display: inline-block;
    display: flex;

    .dashboard-list-one {
      padding-right: 20px;
      .dashboard-list-one-item {
        .paddingBo {
          padding-bottom: 20px;
        }
        display: flex;
        // padding-top: 10px;
        justify-content: space-between;
      }
      .dashboard-list-one-item-two {
        .paddingBo {
          padding-bottom: 20px;
        }
        margin-top: 28px;

        display: flex;
        // padding-top: 10px;
        justify-content: space-between;
      }
    }
  }
  .dashboard-list-item-parent-two {
    display: inline-block;
    display: flex;
    margin-top: 15px;
    .dashboard-list-one {
      padding-right: 20px;
      .dashboard-list-one-item {
        .paddingBo {
          padding-bottom: 20px;
        }

        display: flex;

        justify-content: space-between;
      }
      .dashboard-list-one-item-two {
        .paddingBo {
          padding-bottom: 20px;
        }

        margin-top: 28px;
        display: flex;
        // padding-top: 10px;
        justify-content: space-between;
      }
    }
  }
  .system-analys {
    background: #fff;
    padding: 0 24px 24px;
    border-radius: 8px;
    &-title {
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      padding: 18px 0;
      display: block;
      border-bottom: 1px dashed #e9e9e9;
    }
    &-area {
      display: flex;
      align-items: center;
    }
    &-line {
      margin: 0 30px;
      width: 1px;
      height: 39px;
      background: #e9e9e9;
    }
    &-addarea {
      text-align: center;
      cursor: pointer;
      img {
        width: 36px;
        margin: 3px 0 10px;
      }
      span {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        white-space: nowrap;
      }
    }
    &-content {
      display: flex;
      overflow-x: auto;
      padding-top: 16px;
      &__item {
        min-width: 190px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        padding: 16px 24px;
        border-radius: 8px;
        background: url('./../../assets/bg_donglist.png') no-repeat;
        background-size: cover;
        margin-right: 24px;
        position: relative;
        &:last-child {
          margin-right: 0;
        }
        .dontlist-icon {
          width: 24px;
          margin-right: 24px;
        }
        .delete-icon {
          cursor: pointer;
          position: absolute;
          width: 8px;
          height: 8px;
          top: 6px;
          right: 6px;
          display: none;
        }
        &:hover {
          .delete-icon {
            display: block;
          }
        }
        div {
          display: flex;
          flex-direction: column;
          span {
            white-space: nowrap;
          }
          .system-analys-content__num {
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            font-size: 18px;
            color: #333333;
          }
          .system-analys-content__text {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 14px;
            color: #666666;
          }
        }
      }
    }
  }
  .dashboard-list {
    display: flex;
    // justify-content: space-between;
    flex-wrap: wrap;
    padding: 16px 0;
    &-item {
      // margin-right: 16px;
      width: 100%;
      &:first-child {
        margin-left: 0;
      }
      &:last-child {
        margin-right: 0;
      }
    }
  }
  .data-download {
    background: #fff;
    border-radius: 8px;
    padding: 0 24px 32px;
    &-header {
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      border-bottom: 1px dashed #e9e9e9;
      padding: 16px 0;
      margin-bottom: 16px;
    }
  }
}
</style>
