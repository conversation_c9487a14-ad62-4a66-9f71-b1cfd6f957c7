<template>
  <div class="page-content">
    <div class="tips">
      <img src="@/assets//404.png" alt="404" />

      <div class="right-wrap">
        <p>抱歉，你访问的页面不存在</p>
        <button class="home-btn" type="primary" @click="goHomePage">{{ $t('返回首页') }}</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
  methods: {
    goHomePage() {
      this.$router.push('/')
    }
  }
}
</script>

<style lang="scss" scoped>
.page-content {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  .home-btn {
    margin-top: 20px;
    background: lightblue;
    border: 1px solid #8b8bbd;
    border-radius: 4px;
    color: #333;
    padding: 4px 8px;
  }
  .tips {
    width: 630px;
    margin: auto;
    margin-top: 25vh;
    display: flex;
    align-items: center;
    justify-content: space-between;

    img {
      width: 300px;
      height: 180px;
    }

    .right-wrap {
      p {
        color: #808695;
        font-size: 20px;
        line-height: 28px;
      }

      .el-button {
        margin-top: 20px;
      }
    }
  }

  .bottom {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 30px;
    margin: auto;
    text-align: center;

    a {
      color: #808695;
      font-size: 15px;
      text-decoration: none;
      margin-right: 30px;

      &:last-of-type {
        margin-right: 0;
      }
    }
  }
}

@media only screen and (max-width: 500px) {
  .page-content {
    .tips {
      width: 100%;
      margin-top: 10vh;
      display: block;

      img {
        width: 230px;
        display: block;
        margin: auto;
      }

      .right-wrap {
        text-align: center;

        p {
          font-size: 18px;
          margin-top: 40px;
        }
      }
    }
  }
}
</style>
