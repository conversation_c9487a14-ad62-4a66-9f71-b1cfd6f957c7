<template>
  <div style="height: 100%">
    <!-- <mt-template-page
      ref="templetePage"
      :template-config="componentConfig"
      @handleClickCellTool="handleClickCellTitle"
    ></mt-template-page> -->
    <DialogForm ref="DialogForm"></DialogForm>
  </div>
</template>
<script>
import DialogForm from './DialogForm.vue'
import { componentConfig } from './config'
export default {
  components: {
    DialogForm
  },
  data() {
    return {
      componentConfig
    }
  },
  methods: {
    // 刷新main列表
    refreshMain() {
      this.$refs.templetePage.refreshCurrentGridData()
    },
    handleClickCellTitle(e) {
      console.log(e)
      if (e.tool.id === 'look') {
        this.$api.source.queryUserJoinTenant(`${e.data.id}`).then((r) => {
          this.$refs.DialogForm.type = 'look'
          this.$refs.DialogForm.ruleForm = { ...r.data }
          this.$refs.DialogForm.look()
          this.$refs.DialogForm.show()
        })
      } else if (e.tool.id === 'publish') {
        this.$api.source.queryUserJoinTenant(`${e.data.id}`).then((r) => {
          this.$refs.DialogForm.type = 'pushlish'
          this.$refs.DialogForm.ruleForm = { ...r.data }
          this.$refs.DialogForm.reset()
          this.$refs.DialogForm.show()
        })
      }
    }
  }
}
</script>
