import { i18n } from '@/main.js'
export const componentConfig = [
  {
    toolbar: [],
    grid: {
      columnData: [
        {
          type: 'checkbox',
          width: 50
        },
        {
          field: 'accountName',
          width: '150',
          headerText: i18n.t('申请人账号')
        },
        {
          field: 'userName',
          width: '150',
          headerText: i18n.t('姓名'),
          valueConverter: {
            type: 'placeholder'
          }
        },
        {
          field: 'mobile',
          width: '150',
          headerText: i18n.t('手机号'),
          valueConverter: {
            type: 'placeholder'
          }
        },
        {
          field: 'tenantName',
          width: '150',
          headerText: i18n.t('申入公司'),
          valueConverter: {
            type: 'placeholder'
          }
        },
        {
          field: 'createTime',
          width: '150',
          headerText: i18n.t('创建时间'),
          valueConverter: {
            type: 'data'
          }
        },
        {
          field: 'approvalStatus',
          width: '150',
          headerText: i18n.t('状态'),
          valueConverter: {
            type: 'map',
            map: [
              { id: -1, title: i18n.t('待审核'), cssClass: 'isActiveClass' },
              {
                id: 0,
                title: i18n.t('审核未通过'),
                cssClass: 'isActiveClassFalse'
              },
              {
                id: 1,
                title: i18n.t('审核通过'),
                cssClass: 'isActiveClassFalse'
              }
            ],
            fields: { text: 'title', value: 'id' }
          },
          cellTools: [
            {
              id: 'publish',
              icon: 'icon_Share_2',
              title: i18n.t('审核'),
              visibleCondition: (data) => {
                return data.approvalStatus === -1
              }
            },
            {
              id: 'look',
              icon: 'icon_Share_2',
              title: i18n.t('查看'),
              visibleCondition: (data) => {
                return data.approvalStatus !== -1
              }
            }
          ]
        }
      ],
      asyncConfig: {
        recordsPosition: 'data.records',
        url: '/usercenter/admin/user-join-tenant/query-page'
      }
    }
  }
]
