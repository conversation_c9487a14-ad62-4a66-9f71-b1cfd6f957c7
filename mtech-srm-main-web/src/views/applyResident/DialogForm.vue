<template>
  <div class="demo-block">
    <mt-dialog
      ref="dialog"
      css-class="dialog-form-flex"
      :header="$t('申请详情')"
      :close="close"
      :buttons="buttons"
    >
      <div class="dialog-header">
        <img :src="ruleForm.photo" alt="" />
        <div>
          <span style="color: #292929; font-size: 18px; font-weight: 500">{{
            ruleForm.userName || $t('姓名')
          }}</span>
          <span>
            <i class="mt-icons mt-icon-platform_icon_Account apply-icon"></i
            >{{ $t('账户名称') }}：{{ ruleForm.accountName }}</span
          >
        </div>
        <div>
          <span>
            <i class="mt-icons mt-icon-platform_icon_tell apply-icon"></i>{{ $t('手机') }}：{{
              ruleForm.mobile || $t('未知')
            }}</span
          >
          <span>
            <i class="mt-icons mt-icon-icon_mail apply-icon"></i>{{ $t('邮箱') }}：{{
              ruleForm.email
            }}</span
          >
          <span>
            <i class="mt-icons mt-icon-platform_icon_user apply-icon"></i>{{ $t('申入租户') }}:{{
              ruleForm.tenantName || $t('未知')
            }}</span
          >
        </div>
      </div>
      <div style="color: #292929; font-size: 14px; font-weight: 500; margin-bottom: 20px">
        <span
          style="
            width: 2px;
            height: 10px;
            margin-right: 10px;
            display: inline-block;
            background: rgba(0, 70, 156, 1);
            border-radius: 1px;
          "
        ></span
        >{{ $t('员工分配') }}
      </div>
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="companyId" :label="$t('公司')">
          <mt-select
            :disabled="disabled"
            v-model="ruleForm.companyId"
            :change="companyChange"
            :data-source="companyArr"
            :fields="{ text: 'orgName', value: 'id' }"
            :show-clear-button="true"
            :placeholder="$t('请选择公司')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="organizationId" :label="$t('部门')">
          <mt-select
            :disabled="disabled"
            v-model="ruleForm.organizationId"
            :change="departmentChange"
            :data-source="departmentArr"
            :fields="{ text: 'orgName', value: 'id' }"
            :show-clear-button="true"
            :placeholder="$t('请选择部门')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="stationOrganizationId" :label="$t('岗位')">
          <mt-select
            :disabled="disabled"
            v-model="ruleForm.stationOrganizationId"
            :data-source="stationArr"
            :fields="{ text: 'orgName', value: 'id' }"
            :show-clear-button="true"
            :placeholder="$t('请选择岗位')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('审批结果')" v-if="ruleForm.approvalStatus !== -1">
          {{ ruleForm.approvalStatus == 0 ? $t('未通过') : $t('通过') }}
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import MtDialog from '@mtech-ui/dialog'
import MtForm from '@mtech-ui/form'
import MtFormItem from '@mtech-ui/form-item'
import { mapGetters } from 'vuex'

export default {
  components: {
    MtDialog,
    MtForm,
    MtFormItem
  },
  data() {
    return {
      stationOrganizationId: '',
      ruleForm: {},
      rules: {
        companyId: [
          { required: true, message: this.$t('请选择公司'), trigger: 'blur' },
          {
            required: true,
            pattern: /^\S{2,}$/,
            message: this.$t('请选择公司'),
            trigger: 'blur'
          }
        ],
        organizationId: [
          { required: true, message: this.$t('请选择部门'), trigger: 'blur' },
          {
            required: true,
            pattern: /^\S{2,}$/,
            message: this.$t('请选择部门'),
            trigger: 'blur'
          }
        ],
        stationOrganizationId: [
          { required: true, message: this.$t('请选择岗位'), trigger: 'blur' },
          {
            required: true,
            pattern: /^\S{2,}$/,
            message: this.$t('请选择岗位'),
            trigger: 'blur'
          }
        ]
      },
      companyArr: [],
      departmentArr: [],
      stationArr: [],
      type: 'pushlish'
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    disabled() {
      const a = this.ruleForm.approvalStatus == -1 ? false : true
      return a
    },
    buttons() {
      let a = [{}]
      if (this.ruleForm.approvalStatus == -1) {
        a = [
          {
            click: this.hide,
            buttonModel: { content: this.$t('驳回') }
          },
          {
            click: this.save,
            buttonModel: {
              isPrimary: 'true',
              content: this.$t('审核通过')
            }
          }
        ]
      }
      return a
    }
  },
  mounted() {
    this.getChildrenCompanyOrganization()
  },
  methods: {
    close() {
      this.reset()
    },
    reset() {
      this.companyArr = []
      this.departmentArr = []
      this.stationArr = []
      this.getChildrenCompanyOrganization()
    },
    look() {
      this.getChildrenCompanyOrganization()
      this.getChildrenDepartmentOrganization(this.ruleForm.companyId)
      this.getChildrenStationOrganization(this.ruleForm.organizationId)
    },
    show() {
      this.$refs.dialog.ejsRef.show()
    },
    hide() {
      this.$api.source
        .userApproval({
          approvalStatus: 0,
          id: this.ruleForm.id,
          companyId: 0,
          organizationId: 0,
          stationOrganizationId: 0
        })
        .then((r) => {
          if (r.code == 200) {
            this.$toast({ content: r.msg, type: 'success' })
            this.$refs.dialog.ejsRef.hide()
            this.$parent.refreshMain()
          }
        })
    },
    save() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$api.source
            .userApproval({
              approvalStatus: 1,
              id: this.ruleForm.id,
              companyId: this.ruleForm.companyId,
              organizationId: this.ruleForm.organizationId,
              stationOrganizationId: this.ruleForm.stationOrganizationId
            })
            .then((r) => {
              if (r.code == 200) {
                this.$toast({ content: r.msg, type: 'success' })
                this.$refs.dialog.ejsRef.hide()
                this.$parent.refreshMain()
              }
            })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 选择公司
    getChildrenCompanyOrganization() {
      this.$api.source
        .getChildrenCompanyOrganization({
          tenantId: this.userInfo.tenantId
        })
        .then((r) => {
          this.companyArr = [...r.data]
        })
    },
    companyChange(e) {
      console.log('触发公司change')
      this.departmentArr = []
      if (e.value && this.type != 'look') {
        this.getChildrenDepartmentOrganization(e.value)
      }
    },
    // 选择部门
    getChildrenDepartmentOrganization(e) {
      if (e == '0' && e) {
        this.reset()
        return
      }
      this.$api.source
        .getChildrenDepartmentOrganization({
          organizationId: e
        })
        .then((r) => {
          this.departmentArr = [...r.data]
        })
    },
    departmentChange(e) {
      // this.stationArr = [];
      if (e.value && this.type != 'look') {
        this.getChildrenStationOrganization(e.value)
      }
    },
    // 选择岗位
    getChildrenStationOrganization(e) {
      if (e == '0' && e) {
        return
      }
      this.$api.source
        .getChildrenStationOrganization({
          organizationId: e
        })
        .then((r) => {
          this.stationArr = [...r.data]
          this.ruleForm = { ...this.ruleForm }
        })
    }
  }
}
</script>

<style lang="scss">
.dialog-form-flex .e-dlg-content {
  padding: 40px !important;
  .mt-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 390px;
      .mt-form-item__content {
        width: 100%;
      }
    }
  }
  .dialog-header {
    margin-bottom: 20px;
    img {
      float: left;
      height: 60px;
      width: 60px;
      border-radius: 50%;
    }
    height: 60px;
    width: 100%;
    color: rgba(154, 154, 154, 1);
    font-size: 14px;
    .apply-icon {
      color: #b0c1df;
      font-size: 14px;
      margin: 0 5px 0 0;
    }
    div {
      height: 30px;
      line-height: 30px;
    }
    span {
      margin-left: 40px;
    }
  }
}
</style>
