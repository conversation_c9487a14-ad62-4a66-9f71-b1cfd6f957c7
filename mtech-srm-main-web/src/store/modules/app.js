import Cookies from 'js-cookie'

const state = {
  sidebar: {
    opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
    withoutAnimation: false
  },
  token: Cookies.get('token') || '',
  lang: Cookies.get('lang') || 'zh-CN',
  userInfo: {},
  routes: [], // 从后台获取的菜单
  buVal: 'TV',
  buList: []
}

const mutations = {
  TOGGLE_SIDEBAR: (state) => {
    state.sidebar.opened = !state.sidebar.opened
    state.sidebar.withoutAnimation = false
    if (state.sidebar.opened) {
      Cookies.set('sidebarStatus', 1)
    } else {
      Cookies.set('sidebarStatus', 0)
    }
  },
  CLOSE_SIDEBAR: (state, withoutAnimation) => {
    Cookies.set('sidebarStatus', 0)
    state.sidebar.opened = false
    state.sidebar.withoutAnimation = withoutAnimation
  },
  OPEN_SIDEBAR: (state, withoutAnimation) => {
    Cookies.set('sidebarStatus', 1)
    state.sidebar.opened = true
    state.sidebar.withoutAnimation = withoutAnimation
  },
  SET_TOKEN: (state, payload) => {
    Cookies.set('token', payload.token)
  },
  SET_USERINFO: (state, payload) => {
    state.userInfo = payload
  },
  REMOVE_TOKEN: () => {
    Cookies.set('token', '')
  },
  SET_LANG: (state, playload) => {
    Cookies.set('lang', playload)
    state.lang = playload
  },
  SET_ROUTES: (state, data) => {
    state.routes = data
  },
  REMOVE_ROUTES: (state) => {
    state.routes = []
  },
  SET_BU: (state, val) => {
    state.buVal = val
  },
  SET_BU_LIST: (state, list) => {
    state.buList = list
  }
}

const actions = {
  setToken({ commit }) {
    commit('SET_TOKEN')
  },
  removeToken({ commit }) {
    commit('REMOVE_TOKEN')
  },
  toggleSideBar({ commit }) {
    commit('TOGGLE_SIDEBAR')
  },
  closeSideBar({ commit }, { withoutAnimation }) {
    commit('CLOSE_SIDEBAR', withoutAnimation)
  },
  setLang({ commit }, playload) {
    commit('SET_LANG', playload)
  },
  setUserInfo({ commit }, playload) {
    commit('SET_USERINFO', playload)
  },
  setRoutes({ commit }, playload) {
    commit('SET_ROUTES', playload)
  },
  removeRoutes({ commit }) {
    commit('REMOVE_ROUTES')
  },
  setBu({ commit }, playload) {
    commit('SET_BU', playload)
  },
  setBuList({ commit }, playload) {
    commit('SET_BU_LIST', playload)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
