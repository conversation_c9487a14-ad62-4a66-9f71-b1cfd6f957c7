import * as MAPI from '@mtech-common/http'
import Vue from 'vue'
const GAPI = MAPI
const baseCon = GAPI.baseConfig
const API = GAPI.API
baseCon.setDefault({ baseURL: '/api' })

baseCon.addRequestTransform((config) => {
  return config
})

setTimeout(() => {
  baseCon.addNotify({
    success: (msg) => {
      // 可以使用$toast组件，msg当前版本下默认为接口response.msg
      Vue.prototype.$toast({
        content: msg,
        type: 'success'
      })
    },
    error: (msg) => {
      // 可以使用$toast组件，msg当前版本下默认为接口response.msg
      Vue.prototype.$toast({
        content: msg,
        type: 'error'
      })
    }
  })
}, 100)

export default {
  $get: API.get,
  $post: API.post,
  $put: API.put,
  $delete: API.delete
}
