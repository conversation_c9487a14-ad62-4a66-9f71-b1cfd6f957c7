// export default {
//   queryUserInfo: "/iam/common/account/userinfo", //查询用户信息
//   queryUserMenu: "/iam/tenant/user-permission-query/user-menu", //查询用户信息
//   queryUserJoinTenant: "/usercenter/admin/user-join-tenant/detail", //查询入驻申请明细
//   userApproval: "/usercenter/admin/user-join-tenant/approval", //审核入驻申请
//   getChildrenCompanyOrganization:
//     "/masterDataManagement/tenant/organization/getChildrenCompanyOrganization", //获取当前组织下公司列表
//   getChildrenDepartmentOrganization:
//     "/masterDataManagement/tenant/organization/getChildrenDepartmentOrganization", //获取当前组织下部门列表
//   getChildrenStationOrganization:
//     "/masterDataManagement/tenant/organization/getChildrenStationOrganization", //获取当前组织下岗位列表
// };
const MESSAGE = '/message' // 请求接口的前缀
export default ($axios) => ({
  queryUserInfo: (params) => {
    return $axios.$get('/iam/common/account/userinfo', params)
  },
  queryUserMenu: (params) => {
    return $axios.$get('/iam/tenant/user-permission-query/user-menu', params.code, {
      headers: { business_menu: params.buVal }
    })
  },
  queryUserJoinTenant: (params) => {
    return $axios.$post(`/usercenter/admin/user-join-tenant/detail/${params}`)
  },
  userApproval: (params) => {
    return $axios.$post('/usercenter/admin/user-join-tenant/approval', params)
  },
  getChildrenCompanyOrganization: (params) => {
    return $axios.$post(
      '/masterDataManagement/tenant/organization/getChildrenCompanyOrganization',
      params
    )
  },
  getChildrenDepartmentOrganization: (params) => {
    return $axios.$post(
      '/masterDataManagement/tenant/organization/getChildrenDepartmentOrganization',
      params
    )
  },
  getChildrenStationOrganization: (params) => {
    return $axios.$post(
      '/masterDataManagement/tenant/organization/getChildrenStationOrganization',
      params
    )
  },
  //未读消息
  inmailUnReadCount: (params) => {
    return $axios.$get(`${MESSAGE}/user/inmail/unReadCount`, params)
  },
  //我的站内消息
  inmailMy: (params) => {
    return $axios.$get(`${MESSAGE}/user/inmail/my/`, params)
  },
  //我的站内消息
  mustReadMsg: () => {
    return $axios.$get(`${MESSAGE}/user/inmail/my/inmail`)
  },
  //设置单条消息已读
  inmailSetRead: (params) => {
    return $axios.$post(`${MESSAGE}/user/inmail/setRead`, params)
  },
  //必读消息已读
  msgAreadyRead: (params) => {
    return $axios.$post(`${MESSAGE}/user/inmail/setAll/read`, params)
  },
  //获取首页系统公告-采方
  getPurAnnouncement: (params) => {
    return $axios.$post(`${MESSAGE}/tenant/notice/for/queryPur`, params)
  },
  //获取首页系统公告-供方
  getSupAnnouncement: (params) => {
    return $axios.$post(`${MESSAGE}/tenant/notice/supplier/querySup`, params)
  },
  //获取首页待办事项 -采方
  getBuyTodoList: (params) => {
    return $axios.$post(`/srm-purchase-execute/tenant/buyerWorkCenterTask/query`, params)
  },
  //获取首页待办列表 -采方
  getPurTodoList: (params) => {
    return $axios.$post(`${MESSAGE}/user/todoHeader/queryMainList`, params)
  },
  // 配额管理 - 获取首页待办列表 - 采方
  getQuotaWaitList: (params) => {
    return $axios.$post('/price/tenant/price/change/record/queryCount', params)
  },

  //获取首页待办列表-供方
  getSupTodoList: (params) => {
    return $axios.$post(`${MESSAGE}/user/supplierTodoHeader/queryMainList`, params)
  },
  //获取首页待办列表-供方 new
  getNewSupTodoList: (params) => {
    return $axios.$post(`/srm-purchase-execute/tenant/supplierWorkCenterTask/query`, params)
  },
  getBusinessList: (params) => {
    return $axios.$get('/iam/tenant/user-permission-query/user-business', params)
  }
})
