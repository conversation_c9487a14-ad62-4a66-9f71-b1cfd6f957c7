import Vue from 'vue'
import Router from 'vue-router'
// import Layout from '@/components/layout/index.vue'
import Layout from '@/layout/index.vue'

import mainRoute from './main'

// 解决点击同一个路游报错的问题
const originalPush = Router.prototype.push
//修改原型对象中的push方法
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err)
}

Vue.use(Router)

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/',
    component: Layout,
    redirect: '/main/dashboard'
  },
  {
    path: '/sourcing*',
    name: 'sourcing',
    meta: { title: '寻源项目' }
  },
  {
    path: '/supplier*',
    name: 'supplier',
    meta: { title: '供应商管理' }
  },
  {
    path: '/masterdata*',
    name: 'masterdata',
    meta: { title: '角色管理' }
  },
  {
    path: '/purchase-execute*',
    name: 'purchase-execute',
    meta: { title: '采购执行' }
  },
  {
    path: '/purchase-pv*',
    name: 'purchase-pv*',
    meta: { title: '光伏采购' }
  },
  {
    path: '/middlePlatform*',
    name: 'middlePlatform'
  },
  {
    path: '/mall*',
    name: 'mall',
    meta: { title: '采购商城' }
  },
  {
    path: '/item*',
    name: 'item',
    meta: { title: '商品管理' }
  },
  {
    path: '/main',
    component: () => import('@/views/public.vue'),
    children: [...mainRoute]
  },
  {
    path: '/404',
    component: () => import('@/views/errorPage/404'),
    hidden: true
  }
]

const createRouter = () =>
  new Router({
    routes: constantRoutes,
    // mode: 'history',
    // base: '/',
    linkActiveClass: 'active-link',
    linkExactActiveClass: 'exact-active-link',
    scrollBehavior(to, from, savedPosition) {
      if (savedPosition) {
        return savedPosition
      } else {
        return { x: 0, y: 0 }
      }
    },
    fallback: true
  })

const router = createRouter()

export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher
}

export default router
