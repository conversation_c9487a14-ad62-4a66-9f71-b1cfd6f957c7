<template>
  <div id="app">
    <mt-layout />
    <!-- <mt-dialog
      ref="toast"
      header="提示"
      :buttons="buttons"
      :open="onOpen"
      :width="500"
      :height="200"
      size="small"
      :show-close-icon="false"
    >
      <div class="fontSize">该账号未分配权限,请联系管理员分配权限!</div>
    </mt-dialog> -->
  </div>
</template>

<script>
import MtLayout from '@/layout/index.vue'
// import MtLayout from '@/components/layout/index.vue'
// import MtDialog from '@mtech-ui/dialog'
// import Theme from './themes/theme.js'

export default {
  name: 'App',
  components: {
    MtLayout
    // MtDialog
  },
  data() {
    return {
      url: '',
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: '确定' }
        }
      ]
    }
  },
  mounted: function () {
    console.log('eslint test')
    document.getElementById('firstpage-spinner').style.display = 'none'
    const configScript = document.createElement('script')
    configScript.innerHTML = `
      window.difyChatbotConfig = {
        token: 'udFXJDzDieuxzeUv',
        baseUrl: 'https://srm-ai.eads.tcl.com',
        draggable: true,
        dynamicScript: true
      }
    `
    document.head.appendChild(configScript)
    const chatbotScript = document.createElement('script')
    chatbotScript.src = 'https://srm-ai.eads.tcl.com/embed.min.js'
    chatbotScript.id = 'udFXJDzDieuxzeUv'
    chatbotScript.defer = true
    // document.head.appendChild(chatbotScript)
  },
  watch: {
    // 监听userInfo中值
    '$store.state.app.userInfo'(newVal) {
      if (
        newVal.applicationList.length == 1 &&
        newVal.applicationList[0].applicationCode == 'user'
      ) {
        this.$refs.toast.ejsRef.show()
        this.url = newVal.applicationList[0].url
      }
    }
  },
  methods: {
    hide() {
      this.$refs.toast.ejsRef.hide()
      location.href = this.url
    },
    onOpen: function (args) {
      args.preventFocus = true
    }
  }
}
</script>
<style lang="scss">
#dify-chatbot-bubble-button {
  background-color: #1c64f2 !important;
}
#dify-chatbot-bubble-window {
  width: 80rem !important;
  height: 80vh !important;
}
.fontSize {
  font-size: 15px;
  padding-top: 43px;
}
</style>
