$white: rgb(255, 255, 255);
// background-colors
$background-color: rgb(244, 244, 244); // body background color
$head-bg-color: rgb(255, 255, 255); // head background color
$left-bg-color: rgb(22, 27, 31);

// color
$light-blue: #31AFC8;
$dark-gray: rgb(88, 94, 108);
$green: rgb(51, 181, 147);
$pink: rgb(248, 118, 120);
$gray: rgb(209, 209, 209);


//scrollBar-color
$scrollBar-track-color: rgb(235,235,235);
$scrollBar-thumb-color: rgb(210,210,210);
$scrollBar-thumb-hover-color: rgb(200,200,200);
$scrollBar-thumb-active-color: rgb(190,190,190);
//left-background


// fonts
$primary-font-family: 'Microsoft YaHei', 'Arial', serif; // SimHei  // SimSun // Microsoft YaHei      // "Helvetica Neue", Helvetica, Arial, sans-serif; font-family
$primary-font-color: rgb(88, 94, 107); //primary text color //基本文字颜色
$main-font-color: rgb(66, 66, 66); // 主要文字颜色 lighter
$link-font-color: rgb(12, 168, 242); // clickable text color
$nav-font-color: rgb(178, 202, 216); //nav font color
$active-font-color: rgb(101, 157, 237); //selected font color

//width,height
$main-width: 1280px;
$menu-width: 200px;
$head-height: 60px;
$nav-height: 45px;
$foot-height: 35px;

//border
$border-color: rgb(216,221,227);
$border: 1px solid $border-color;

//border-radius
$border-radius-default: 2px;
$border-radius-small: 1px;
$border-radius-medium: 3px;
$border-radius-large: 4px;

// padding-top,padding-bottom
$padding-default-vertical: 3px;
$padding-small-vertical: 2px;
$padding-medium-vertical: 5px;
$padding-large-vertical: 8px;

// padding-left,padding-right
$padding-default-horizontal: 8px;
$padding-small-horizontal: 10px;
$padding-medium-horizontal: 10px;
$padding-large-horizontal: 30px;

//font-size
$font-size-default: 14px;
$font-size-small: 14px;
$font-size-medium: 15px;
$font-size-large: 16px;

// 禁止点击
$cursor-disabled: not-allowed;
//total-money color
$total-money-color: rgb(217, 86, 82);



