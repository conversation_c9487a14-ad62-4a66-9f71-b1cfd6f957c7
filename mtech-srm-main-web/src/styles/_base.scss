.mt-flex {
    display: flex;
    position: relative;
}

.mt-flex-direction-column {
    display: flex;
    flex-direction: column;
    position: relative;
}
.mt-loading{
  background: none;
}
.size-for-10px {
    font-size: 12px;
    transform: scale(0.83);
}

.list-item-active {
    border-left: none;
    position: relative;
    &:before {
        content: "";
        height: 100%;
        width: 0;
        position: absolute;
        border-left: 2px solid #00469c;
        left: 0;
        top: 0;
        animation: list-item-active-animation 0.2s ease;
    }
    @keyframes list-item-active-animation {
        0% {
            top: 50%;
            height: 0;
        }
        100% {
            top: 0;
            height: 100%;
        }
    }
}

.top-left-arrow-tag {
    padding-left: 5px;
    position: relative;
    &:before {
        content: "";
        height: 0;
        width: 0;
        border-right: 6px solid transparent;
        border-top: 6px solid #eda133;
        position: absolute;
        left: 0;
        top: 0;
    }
}
.cell-checkbox-container{
  display:flex;
  justify-content: center;
  align-items: center;
}
.checkbox {
    display: inline-block;
    position: relative;
    height: 100%;
    label {
        width: 16px;
        height: 16px;
        background: #fff;
        border: 1px solid #E8E8E8;
        cursor: pointer;
        border-radius: 2px;
        @include position(absolute, 0 null 0 0);
        @include center(y);
    }
    input[type=checkbox]:checked+label {
        background: #00469C;
        border: 1px solid #02469C;
        &:after {
            opacity: 1;
            content: '';
            @include arrow(10px, 6px, 2px, #ffffff, -45deg);
            position: absolute;
            top: 3px;
            left: 2px;
        }
    }
}

.mt-margin-left {
    margin-left: 10px;
}

.mt-margin-top {
    margin-top: 10px;
}

.svg-option-item {
    cursor: pointer;
    padding: 0;
    display: inline-block;
    text-align: center;
    position: relative;
    min-width: 60px;
    .svg-icon {
        font-size: 18px;
        color: #979797;
        margin-right: 5px;
    }
    span {
        color: #4f5b6d;
    }
    /deep/ .e-input-group {
        border: none !important;
        .e-input {
            text-align: center;
        }
    }
}
.modal {
  background: rgba(32, 28, 43, 0.8);
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 2;
  .modal-dialog {
    width: 600px;
    max-height: 80vh;
    background: rgb(255, 255, 255);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    position: relative;
    left: calc(50vw - 300px);
    // top: 50px;
    top: 50%;
    transform: translateY(-50%);

    .modal-margin {
      margin: 5px 0;
    }
    .modal-header {
      border-bottom: none;
      padding: 18px;
      border-radius: 1px 1px 0 0;
      // line-height: 30px;
      line-height: 1;
      background-color: #005ca9;
      position: relative;
      color: rgb(255, 255, 255);
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        display: block;
        font-size: 18px;
        font-weight: 400;
        overflow: hidden;
        text-overflow: ellipsis;
        user-select: none;
        white-space: nowrap;
      }
      .close {
        color: rgb(255, 255, 255);
        font-size: 15px;
        font-weight: bold;
        &:after{
          display: block;
          content: "+";
          transform: rotate(45deg);
          font-size: 22px;
        }
      }
    }
    .modal-content {
      // margin: 15px;
      padding: 15px;
      max-height: calc(80vh - 155px);
      overflow-y: auto;
      .form-control {
        margin-left: 4px;
      }
    }
    .modal-footer {
      // padding: 15px;
      // border-top: 1px solid rgb(216, 221, 227);
      padding: 5px;
      background: #FAFAFA;
      .button-group {
        // text-align: center;
        text-align: right;
        .mt-button {
          // margin: 0 10px;
          &:first-child button {
            color: #9BAAC1;
          }
          &:last-child button {
            color: #0043A8;
          }
        }
      }
    }
  }
  .modal-mask {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 5px;
    display: flex;
    justify-content: center;
    align-items: center;

    .svg-icon {
      font-size: 60px;
      animation: circle 2s linear infinite;

      @keyframes circle {
        0% {
          transform: rotate(0);
        }
        to {
          transform: rotate(1turn);
        }
      }
    }
    span {
      color: #fff;
      margin-top: 20px;
    }
  }
  .modal-dialog-small {
    width: 350px;
    left: calc(50vw - 175px);
    .modal-content {
      padding: 20px;
    }
  }
  .modal-dialog-large {
    width: 800px;
    left: calc(50vw - 400px);
  }
}

.slider-panel-container {
  background: rgba(0, 0, 0, 0.2);
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 2;

  .slider-modal {
    width: 800px;
    height: 100vh;
    position: fixed;
    top: 0;
    right: 0;
    background: #fafafa;
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px 0 0 8px;
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.06);

    &:before {
      content: "";
      cursor: pointer;
      width: 12px;
      height: 60px;
      background: #f3f3f3;
      border-radius: 8px 0 0 8px;
      position: absolute;
      left: -12px;
      top: calc(50% - 30px);
    }

    &:after {
      content: "";
      width: 0;
      height: 0;
      cursor: pointer;
      border-width: 4px 0px 4px 4px;
      border-style: solid;
      border-color: transparent transparent transparent #6c7a8f;
      position: absolute;
      left: -8px;
      top: calc(50% - 4px);
    }

    .slider-header {
      height: 60px;
      background: #f3f3f3;
      padding: 10px 20px 10px 30px;
      justify-content: space-between;
      align-items: center;

      .slider-title {
        font-size: 16px;
        font-weight: 500;
        color: #292929;
      }

      .slider-close {
        cursor: pointer;
        font-size: 30px;
        color: #4d5b6f;
        transform: rotate(45deg);
      }
    }

    .slider-content {
      flex: 1;
      padding: 20px;
      .rule-group {
        margin-top: 30px;

        &:first-of-type {
          margin-top: 0;
        }

        .group-header {
          font-size: 14px;
          color: #292929;
          display: inline-block;
          padding-left: 10px;
          position: relative;

          &:before {
            content: "";
            position: absolute;
            width: 3px;
            height: 12px;
            background: #eda133;
            border-radius: 2px 0 0 2px;
            left: 0;
            top: 4px;
          }
        }

        .group-description {
          padding-left: 10px;
          font-size: 12px;
          display: block;
          color: #9a9a9a;
          margin-top: 6px;
        }

        .group-content {
          padding: 20px 10px 0 10px;
        }
      }
    }

    .slider-footer {
      height: 60px;
      background: #ffffff;
      border-radius: 0 0 0 8px;
      box-shadow: inset 0 1px 0 0 #e8e8e8;
      align-items: center;
      flex-direction: row-reverse;

      span {
        margin: 0 30px;
        font-size: 14px;
        color: #0043a8;
        font-weight: 500;
      }
    }
  }
}
// 普通弹窗
.dialog-form {
  width: 900px!important;
  max-height: 90%!important;
  border-radius: 8px;
  overflow: hidden;
  .e-dlg-header-content {
    padding: 14px 20px;
  }

  &.small-dialog {
    width: 500px!important;
    .e-dlg-header-content {
      background: #fff;
      .e-icon-btn .e-btn-icon {
        color: #9DAABF;
      }
      .e-dlg-header {
        color: #292929;
        display: flex;
        align-items: center;
        i {
          color: #EDA133;
        }
        .header-text {
          margin-left: 10px;
          color: #292929;
        }
      }
    }
  }
}
