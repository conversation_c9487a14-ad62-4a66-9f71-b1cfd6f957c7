// breakpoints

$S: 320px;
$M: 768px;
$L: 1280px;

// media queries

@mixin MQ($canvas) {
  @if $canvas == S {
    @media only screen and (min-width: $S) {
      @content;
    }
  } @else if $canvas == M {
    @media only screen and (min-width: $M) {
      @content;
    }
  } @else if $canvas == L {
    @media only screen and (min-width: $L) {
      @content;
    }
  }
}

// super light grid - it works with the .cd-container class

@mixin column($percentage, $float-direction:left) {
  width: 100% * $percentage;
  float: $float-direction;
}

.columns {
  width: 100%;
  .column-07 {
    @include column(.07)
  }
  .column-1 {
    @include column(.1);
  }
  .column-13-right {
    @include column(.13, right);
    text-align: right;
  }
  .column-15 {
    @include column(.15);
  }
  .column-2 {
    @include column(.2);
  }
  .column-22 {
    @include column(.22);
  }
  .column-25 {
    @include column(.25);
  }
  .column-25-right {
    @include column(.25, right);
    text-align: right;
  }
  .column-3 {
    @include column(.3);
  }
  .column-3-right {
    @include column(.3, right);
    text-align: right;
  }
  .column-33 {
    @include column(.3333);
  }
  .column-4 {
    @include column(.4);
  }
  .column-49 {
    @include column(.49);
  }
  .column-49-right {
    @include column(.49, right);
  }
  .column-5 {
    @include column(.5);
  }
  .column-5-right {
    @include column(.5, right);
  }
  .column-6 {
    @include column(.6);
  }
  .column-65 {
    @include column(.65);
  }
  .column-7 {
    @include column(.7);
  }
  .column-73-right {
    @include column(.73, right);
  }
  .column-74-right {
    @include column(.74, right);
  }
  .column-8 {
    @include column(.8);
  }
  .column-40 {
    @include column(.4);
  }
  .column-58-right {
    @include column(.58, right);
  }
  &:after {
    content: "";
    display: table;
    clear: both;
  }
}
