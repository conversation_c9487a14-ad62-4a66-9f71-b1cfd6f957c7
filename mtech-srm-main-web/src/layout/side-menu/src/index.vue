<template>
  <!-- 菜单 -->
  <mt-side-bar
    id="sidebar-menu"
    ref="sidebarInstance"
    :target="target"
    :width="width"
    :dock-size="dockSize"
    :enable-dock="true"
    :enable-gestures="false"
    :is-open="true"
    @close="close"
    @open="open"
  >
    <div v-if="allowSearch || allowSwitch" class="option-wrap">
      <!-- 搜索 -->
      <template v-if="allowSearch" name="fade" mode="out-in">
        <div v-if="isOpen">
          <div class="search-container">
            <i class="mt-icons mt-icon-MT_Search icon-search"></i>
            <ejs-autocomplete
              css-class="auto-complete-component"
              :data-source="searchData"
              :fields="searchFields"
              :placeholder="$t('请输入关键字')"
              @select="searchSelect"
            ></ejs-autocomplete>
          </div>
        </div>
        <div v-if="!isOpen" class="slider-icon-container">
          <i class="mt-icons mt-icon-MT_Search" @click="openMenu"></i>
        </div>
      </template>
      <!-- 采供切换 -->
      <template v-if="allowSwitch && false" name="fade">
        <div class="switch-container" v-if="isOpen">
          <div
            :class="['switch', isSwitchIn && 'switch-active']"
            ref="switchIn"
            @click="switchToggle('buyer')"
          >
            <i class="mt-icons mt-icon-icon_tab_off switch-transform-icon"></i>
            <span>{{ $t('采购') }}</span>
          </div>
          <div class="switch switch-icon-box" :title="$t('转换')">
            <i class="mt-icons mt-icon-Sorting-m icon-toogle" @click="switchToggle"></i>
          </div>

          <div
            :class="['switch', !isSwitchIn && 'switch-active']"
            ref="switchOut"
            @click="switchToggle('supplier')"
          >
            <i class="mt-icons mt-icon-icon_tab_off"></i>
            <span>{{ $t('供应') }}</span>
          </div>
        </div>
        <div class="slider-icon-container" v-else>
          <i class="mt-icons mt-icon-Sorting-m icon-toggle-big" @click="toggle"></i>
        </div>
      </template>
    </div>

    <div class="tree-control-wrapper">
      <keep-alive>
        <mt-treeView
          id="treeview"
          ref="treeviewInstance"
          css-class="tree-view-component"
          v-on="$listeners"
          :show-line="false"
          v-bind="$attrs"
          :fields="treeFields"
          :node-template="TreeNodeTemplate"
          :selected-nodes="selectedNodes"
          :expanded-nodes="expandedNodes"
          :enable-persistence="true"
          @nodeSelected="nodeSelected"
          @nodeExpanded="nodeExpanded"
          @nodeCollapsed="nodeCollapsed"
        ></mt-treeView>
      </keep-alive>
    </div>
    <!-- <div class="menu-control-wrapper">
      <ejs-menu
        id="menubar"
        ref="menubar"
        css-class="menu-bar-component"
        v-on="$listeners"
        :fields="menuFields"
        :items="dataSource"
        orientation="Vertical"
        :template="MenuNodeTemplate"
        @select="menuSelect"
      ></ejs-menu>
    </div> -->
  </mt-side-bar>
</template>

<script>
import Vue from 'vue'

import { MenuPlugin } from '@syncfusion/ej2-vue-navigations'
import { AutoCompletePlugin } from '@syncfusion/ej2-vue-dropdowns'
// import MtechUI from '@mtech-ui/mtech-ui'
import MtTreeView from '@mtech-ui/tree-view'
import MtSideBar from '@mtech-ui/side-bar'

import SvgIcon from './components/SvgIcon'
Vue.component('svg-icon', SvgIcon)

Vue.use(MenuPlugin)
Vue.use(AutoCompletePlugin)
// Vue.use(MtechUI)

import TreeNodeTemplate from './components/sideMenu/treeNodeTemplate.vue'
import MenuNodeTemplate from './components/sideMenu/menuNodeTemplate.vue'
import findAllParent from './utils/findAllParent'

export default {
  name: 'MtSideMenu',
  components: {
    MtTreeView,
    MtSideBar
  },
  provide() {
    return {
      mtSideMenu: this
    }
  },
  props: {
    target: {
      type: String,
      require: false,
      default: null
    },
    fields: {
      type: Object,
      default: () => {
        return {
          id: 'id',
          text: 'text',
          icon: 'icon',
          path: 'path',
          children: 'children',
          parentId: 'parentId',
          hasChildren: 'hasChildren'
        }
      }
    },
    dataSource: {
      type: Array,
      default: () => []
    },
    allowSearch: {
      type: Boolean,
      default: true
    },
    allowSwitch: {
      type: Boolean,
      default: false
    },
    allowSession: {
      type: Boolean,
      default: true
    },
    switchIcon: {
      type: String,
      default: 'switch-in'
    },
    width: {
      type: Number,
      default: 220
    },
    dockSize: {
      type: Number,
      default: 56
    }
  },
  data() {
    return {
      isOpen: true,
      isSwitchIn: true,
      selectedNodes: [],
      expandedNodes: [],
      selectMenu: {},
      treeFields2: {}
    }
  },
  computed: {
    sidebarWidth() {
      return this.isOpen ? this.width : this.dockSize
    },
    fieldsVal() {
      let val = {
        id: 'id',
        text: 'text',
        count: 'count',
        icon: 'icon',
        path: 'path',
        children: 'children',
        parentId: 'parentId',
        rootId: 'rootId',
        hasChildren: 'hasChildren'
      }
      const { id, text, icon, path, children, parentId, hasChildren } = this.fields
      id && (val.id = id)
      text && (val.text = text)
      icon && (val.icon = icon)
      path && (val.path = path)
      children && (val.children = children)
      parentId && (val.parentId = parentId)
      hasChildren && (val.hasChildren = hasChildren)
      val = { ...val }
      return val
    },
    treeFields() {
      const { dataSource: sourceTreeList, fieldsVal } = this
      const dataSource = this.recurrence(sourceTreeList)
      const { id, text, children, parentId, hasChildren } = fieldsVal
      dataSource.forEach((item) => {
        delete item[parentId]
      })
      return {
        dataSource,
        id,
        text,
        child: children,
        parentID: parentId,
        hasChildren
      }
    },
    menuFields() {
      const { id, text, path, parentId, children } = this.fieldsVal
      return {
        itemId: id,
        text,
        parentId,
        url: path,
        children
      }
    },
    searchFields() {
      return {
        text: this.fieldsVal.text
      }
    },
    searchData() {
      return this.treeToArray(this.dataSource)
    }
  },
  watch: {
    isOpen(v) {
      this.$emit('getIsOpen', v)
    },
    $route(newRoute) {
      if (newRoute.path === '/404') {
        sessionStorage.removeItem('currentSideMenu')
        this.selectedNodes = []
        this.$forceUpdate()
      }
    }
  },
  methods: {
    beforeOpen(e) {
      const { fieldsVal } = this
      const { children, parentId } = fieldsVal
      let showA = false
      this.treeFields2 = {
        dataSource: e.items,
        ...fieldsVal,
        child: children,
        parentID: parentId
      }
      e.items.forEach((item) => {
        if (item.children) {
          showA = true
        }
      })
      // let a = e.element;
      if (showA) {
        e.element.children.forEach((item) => {
          item.style.height = '0'
          item.style.overflow = 'hidden'
          item.style.transition = '0.01s'
        })
        setTimeout(() => {
          e.element.appendChild(this.$refs.treeviewInstance2.$el)
        }, 200)
      }
    },
    onClose() {
      return false
    },
    TreeNodeTemplate() {
      return {
        template: TreeNodeTemplate
      }
    },
    MenuNodeTemplate() {
      return {
        template: MenuNodeTemplate
      }
    },
    openMenu() {
      this.toggle()
    },
    toggle() {
      this.isOpen = !this.isOpen
      this.$refs.sidebarInstance.toggle()
      this.$emit('openMenu', true)
    },
    handleUrl(url) {
      var obj = {}
      var arrNew = url.split('?')[1].split('&')
      for (let i = 0; i < arrNew.length; i++) {
        var key = arrNew[i].split('=')[0]
        var value = arrNew[i].split('=')[1]
        //在对象中添加属性
        obj[key] = value
      }
      return obj
    },
    nodeSelected(e) {
      console.log('nodeSelected-e', e)
      let init = e.isInteracted
      this.$emit('setSideClickMenu', e.isInteracted)
      // sessionStorage.setItem('menuInfo', JSON.stringify(e.nodeData))
      const { nodeData } = e
      const { id: idKey } = this.fieldsVal
      const node = this.searchData.filter((item) => item[idKey] === nodeData[idKey])
      if (node && node.length) {
        this.selectMenu = node[0]
        this.$emit('menuSelect', node[0])
        // this.$refs.treeviewInstance.$refs.ejsRef.nodeTemplate().template.methods.redirectTo()
        if (this.selectMenu.url) {
          window.open(
            this.selectMenu.url,
            this.selectMenu.urlOpenType || '_blank',
            this.selectMenu.urlData
          )
        } else if (this.selectMenu.path && init) {
          if (this.selectMenu.path.indexOf('?') > -1) {
            this.$router.push({
              path: this.selectMenu.path.split('?')[0],
              query: this.handleUrl(this.selectMenu.path)
            })
          } else {
            this.$router.push(this.selectMenu.path)
          }
          const role = this.isSwitchIn ? 'buyer' : 'supplier'
          this.selectMenu.toggle = role
          sessionStorage.setItem('currentSideMenu', JSON.stringify(this.selectMenu))
        }
      }
      node[0].rootId ? this.handleAddBg(node[0].rootId) : ''
    },
    handleAddBg(rootId) {
      const _levelEleList = document.getElementById('treeview').getElementsByClassName('e-level-1')
      const arr = Array.from(_levelEleList)
      console.log('_levelEleList', _levelEleList, rootId)
      arr.forEach((item) => {
        console.log('data-uid', item.getAttribute('data-uid'))
        if (item.getAttribute('data-uid') == rootId) {
          item.classList.add('expandedLevel1')
        } else if (item.classList.contains('expandedLevel1')) {
          item.classList.remove('expandedLevel1')
        }
      })
    },

    menuSelect(e) {
      const { item } = e
      const { id: idKey } = this.fieldsVal
      const node = this.searchData.filter((data) => data[idKey] === item[idKey])
      if (node && node.length) {
        this.selectMenu = node[0]
        this.$emit('menuSelect', node[0])
      }
    },
    switchToggle(status) {
      let role = status
      console.log('switchToggle1', status)
      if (status === 'buyer') {
        if (!this.isSwitchIn) {
          this.isSwitchIn = true
          this.$emit('roleSwitch', role)
        }
      } else if (status === 'supplier') {
        if (this.isSwitchIn) {
          this.isSwitchIn = false
          this.$emit('roleSwitch', role)
        }
      } else {
        this.isSwitchIn = !this.isSwitchIn
        role = this.isSwitchIn ? 'buyer' : 'supplier'
        this.$emit('roleSwitch', role)
      }
    },
    // 将树转换成数组的形式,传入tree数据，返回array数据
    treeToArray(tree) {
      const { children } = this.fieldsVal
      const arr = []
      const expanded = (datas) => {
        if (datas && datas.length > 0) {
          datas.forEach((data) => {
            arr.push(data)
            data[children] && expanded(data[children])
          })
        }
      }
      expanded(tree)
      // 埋点》》
      if (arr.length) {
        const routeList = JSON.parse(JSON.stringify(arr))
        const routeMap = {}
        routeList.forEach((item) => {
          if (item.children) delete item.children
          if (item.path) routeMap[item.path] = item.name || 'SRM 系统'
        })
        sessionStorage.setItem('routeMap', JSON.stringify(routeMap))
      }
      // 《《埋点
      return arr
    },

    searchSelect(e) {
      console.log('sidemenu-e', e)
      const { itemData } = e
      const { id, parentId, path } = this.fieldsVal
      const idVal = itemData[id]
      const parentIdVal = itemData[parentId]
      const pathVal = itemData[path]

      this.$nextTick(() => {
        if (parentIdVal) {
          const parentNodes = findAllParent(
            { id: idVal, parentId: parentIdVal },
            this.dataSource,
            this.fieldsVal
          )
          parentNodes.forEach((node) => {
            this.expandedNodes.push(node.id)
          })
        }
        this.selectedNodes = []
        this.selectedNodes.push(idVal)
        pathVal && this.$router && this.$router.push({ path: pathVal })
        this.$refs.treeviewInstance.$refs.ejsRef.ensureVisible(itemData.id)
      })
    },
    closeId() {
      this.selectedNodes = []
      this.$refs.treeviewInstance.$refs.ejsRef.collapseAll()
    },
    chooseId() {
      const itemData = JSON.parse(sessionStorage.getItem('currentSideMenu')) || {}
      const id = itemData.id
      const parentIdVal = itemData.parentId
      this.$nextTick(() => {
        if (parentIdVal) {
          const parentNodes = findAllParent(
            { id: id, parentId: parentIdVal },
            this.dataSource,
            this.fieldsVal
          )
          this.expandedNodes = []
          parentNodes.forEach((node) => {
            this.expandedNodes.push(node.id)
          })
        }
        this.selectedNodes = []
        this.selectedNodes.push(id)
        // setTimeout(() => {
        // this.$refs.treeviewInstance.$refs.ejsRef.ensureVisible(itemData.id)
        // },100)
      })
    },
    close() {
      this.isOpen = false
    },
    open() {
      this.isOpen = true
    },
    nodeExpanded() {
      // console.log('节点展开了', nodeEle)
      // if (nodeEle.node.classList.contains("e-level-1")) {
      //   nodeEle.node.classList.add("expandedLevel1");
      // }
    },
    nodeCollapsed() {
      // console.log('节点收缩了', nodeEle)
      // if (nodeEle.node.classList.contains("expandedLevel1")) {
      //   nodeEle.node.classList.remove("expandedLevel1");
      // }
    },
    recurrence(data) {
      const array = []
      data.map((i) => {
        if ((i.menuType && i.menuType === 0) || !i.menuType) {
          array.push(i)
        }
        if (i.children && i.children.length) {
          i.children = this.recurrence(i.children)
        }
      })
      return array
    }
  }
}
</script>

<style lang="scss">
// @import 'node_modules/@syncfusion/ej2-inputs/styles/material.scss';
// @import 'node_modules/@syncfusion/ej2-vue-navigations/styles/material.scss';
.mtech-common-layout {
  #treeview > .e-treeview > .e-ul {
    padding: 0 !important;
  }
  .expandedLevel1 {
    background: var(--msm-expandedLevel1-bg-color);
    position: relative;
    &::before {
      position: absolute;
      content: '';
      left: 0;
      width: 3px;
      height: 100%;
      background: var(--msm-before-color);
      border-radius: 0 100px 100px 0;
    }
    // .e-icons {
    //   color: var(--msm-before-color);
    // }
  }
}

.auto-complete-component.e-input-group.e-control-wrapper.e-ddl {
  // border-color: var(--msm-background-color) !important;
  border-bottom: 0;
}
.auto-complete-component.e-input-group.e-control-wrapper.e-ddl {
  font-size: 14px;
  margin-bottom: 0;
}

.menu-bar-component {
  background-color: var(--msm-background-color);

  .e-menu-item {
    background: var(--msm-background-color) !important;
    &.e-selected,
    &.e-focused {
      background-color: var(--msm-expandedLevel1-bg-color) !important;
      &:before {
        position: absolute;
        content: '';
        left: 0;
        width: 3px;
        height: 100%;
        background: var(--msm-before-color);
        border-radius: 0 100px 100px 0;
      }
    }
    // &.e-menu-caret-icon {
    padding: 0 !important;
    display: flex !important;
    align-items: center;
    justify-content: center;
    // }
  }
}
.menu-bar-component.e-menu-wrapper ul.e-vertical {
  min-width: 56px;
  width: 56px;
}
.menu-bar-component.e-menu-wrapper ul .e-menu-item .e-caret {
  display: none;
}
#sidebar-menu.e-dock.e-close .e-menu-wrapper ul.e-vertical .node-content {
  display: none;
  color: var(--mcl-top-select-color);
}
.menu-control-wrapper .e-menu-wrapper {
  background-color: var(--msm-background-color) !important;
}

.e-menu-wrapper .e-ul,
.e-menu-container .e-ul {
  padding: 0 !important;
}
.e-menu-wrapper .e-ul .e-menu-item.e-focused,
.e-menu-container .e-ul .e-menu-item.e-focused {
  color: var(--msm-active-color) !important;
}

#treeview .e-treeview .e-list-item.e-active > .e-text-content .nodetext {
  font-weight: 400;
}
#treeview .e-treeview .e-list-item {
  padding: 0;
}
#treeview .e-treeview .e-text-content {
  border: none;
}

#treeview .e-treeview .e-icon-collapsible::before,
.e-treeview .e-icon-expandable::before {
  transform: scale(0.85);
}
</style>
<style lang="scss" scoped>
v-deep #sidebar-menu {
  overflow-x: hidden;
  // background: transparent;
  // background: linear-gradient(rgba(245, 245, 245, 1), rgba(245, 245, 245, 1)),
  // linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 1));
  background: var(--msm-background-color);
  transition: width 0.5s ease, visibility 0.5s;
  box-shadow: 1px 0 0 0 var(--msm-box-shadow-color);
  border-right: none;
}
.option-wrap {
  margin-bottom: 8px;
  .search-container {
    margin: 0 12px;
    // padding: 8px 0;
    display: flex;
    align-items: center;
    animation: slider-open 0.4s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    height: 30px;
    .icon-search {
      margin: 4px 8px 0 8px;
    }
  }
  .search-icon {
    margin-right: 10px;
    font-size: 24px;
    position: relative;
    bottom: 3px;
  }

  .input {
    animation: slider-open-input 0.4s ease;
    background-color: transparent;
    border: none;
    flex: 1;
  }
  .slider-icon-container {
    width: 56px;
    height: 46px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 22px;
    cursor: pointer;
    position: relative;
    // border-bottom: 1px solid rgba(0, 21, 41, 0.08);
    animation: slider-close 0.4s ease;

    .mt-icons {
      font-size: 16px;
      color: var(--msm-icon-color);
    }
    &:hover {
      .svg-icon {
        color: var(--msm-active-color);
      }
    }
    .icon-toggle-big {
      display: inline-block;
      transform: rotate(90deg);
      color: #9a9a9a;
    }
    &:after {
      content: '';
      width: 16px;
      height: 0;
      border-top: 1px solid rgba(0, 0, 0, 0.06);
      position: absolute;
      bottom: 0;
      left: 20px;
    }
  }
  .switch-container {
    position: relative;
    height: 50px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(0, 21, 41, 0.08);
    animation: slider-open 0.4s ease;
    padding: 0 10px;
    .switch {
      font-weight: 400;
      color: var(--msm-icon-color);
      font-size: 14px;
      width: 33.3%;
      text-align: center;
      cursor: pointer;
      span {
        color: #292929;
      }
      &:hover {
        .switch-icon,
        span {
          color: var(--msm-active-color);
        }
      }
      .mt-icon-icon_tab_off {
        margin-right: 5px;
        font-size: 14px;
      }
    }
    .switch-transform-icon {
      display: inline-block;
      transform: rotate(180deg);
    }
    .switch-transform-icon,
    .mt-icon-icon_tab_off {
      font-size: 14px;
    }
    .icon-toogle {
      display: inline-block;
      transform: rotate(90deg);
    }
    .switch-active {
      // font-size: 16px;
      font-weight: 500;
      color: var(--msm-active-color);
      span {
        color: var(--msm-active-color);
      }
    }
    .switch-out {
      transform: rotate(180deg);
    }
  }
}

.mt-tree-view {
  width: 100%;
}

.e-sidebar.e-left {
  border: none;
}

#sidebar-menu.e-sidebar.e-close .tree-control-wrapper {
  display: none;
}
#sidebar-menu.e-sidebar.e-open .tree-control-wrapper {
  display: block;
}
#sidebar-menu.e-sidebar.e-close .menu-control-wrapper {
  display: block;
}
#sidebar-menu.e-sidebar.e-open .menu-control-wrapper {
  display: none;
}

@keyframes slider-open {
  0% {
    width: 56px;
    opacity: 0;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
    width: 250px;
  }
}
@keyframes slider-open-input {
  0% {
    opacity: 0;
  }
  80% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes slider-close {
  0% {
    width: 250px;
  }
  100% {
    width: 56px;
  }
}

//修改谷歌内核浏览器滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background-color: var(--msm-background-color);
}

::-webkit-scrollbar-thumb {
  background-color: var(--msm-scrollbar-bg-color);
  border-radius: 12px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--msm-scrollbar-bg-color-hover);
}

::-webkit-scrollbar-thumb:active {
  background-color: var(--msm-scrollbar-bg-color-active);
}
::v-deep .e-treeview {
  > .e-list-parent.e-ul {
    padding: 0 !important;
  }
  .e-list-text {
    width: calc(100% - 8px);
    padding: 0;
  }
  .e-level-3,
  .e-level-4 {
    .e-list-text {
      width: 100%;
      padding: 0;
    }
  }
}
.tree-control-wrapper .mt-tree-view .e-treeview {
  .e-fullrow {
    z-index: -1;
  }
  .e-list-item {
    padding: 0;
    &.e-hover {
      .e-fullrow {
        border: unset;
        background: rgba(245, 246, 249, 0.6);
      }
      > .e-text-content .node-content {
        color: var(--msm-active-color);
      }
    }
    &.e-active {
      .e-fullrow {
        background: #f5f6f9;
        border: unset;
      }
      > .e-text-content .nodetext {
        color: var(--msm-active-color);
      }
    }
  }
  // 悬浮的左侧
  > .e-list-parent > .e-list-item {
    &.e-has-child {
      &.e-active {
        position: relative;
        &::before {
          position: absolute;
          content: '';
          left: 0;
          width: 3px;
          height: 100%;
          background: var(--msm-before-color);
          border-radius: 0 100px 100px 0;
        }
      }
    }
  }

  .expandedLevel1 {
    background: var(--msm-expandedLevel1-bg-color);
    position: relative;
    &::before {
      position: absolute;
      content: '';
      left: 0;
      width: 3px;
      height: 100%;
      background: var(--msm-before-color);
      border-radius: 0 100px 100px 0;
    }
    // .e-icons {
    //   color: var(--msm-before-color);
    // }
  }

  .e-list-item[aria-expanded='true'] {
    > .e-text-content .e-icons {
      color: var(--msm-before-color);
    }
  }
  .e-level-2,
  .e-level-3,
  .e-level-4 {
    .e-text-content {
      padding: 0 0 0 16px;
    }
  }
}
</style>
<style lang="scss">
.ivu-layout-sider {
  input::-webkit-input-placeholder {
    color: rgba(255, 255, 255, 0.5) !important;
    font-size: 12px !important;
  }
  input::-moz-placeholder {
    /* Mozilla Firefox 19+ */
    color: rgba(255, 255, 255, 0.5) !important;
    font-size: 12px !important;
  }
  input:-moz-placeholder {
    /* Mozilla Firefox */
    color: rgba(255, 255, 255, 0.5) !important;
    font-size: 12px !important;
  }
}
</style>
