const fieldsDefault = {
  id: 'id',
  children: 'children',
  parentID: 'parentId'
}

export function findAllParent(node, tree, fields, parentNodes = [], index = 0) {
  if (fields === undefined) {
    fields = fieldsDefault
  }
  const { parentID: parentIdKey } = fields
  if (!node || node[parentIdKey] === 0) {
    return
  }
  findParent(node, parentNodes, tree)
  const parentNode = parentNodes[index]
  findAllParent(parentNode, tree, fields, parentNodes, ++index)
  return parentNodes
}

function findParent(node, parentNodes, tree, fields) {
  if (fields === undefined) {
    fields = fieldsDefault
  }
  const { id: idKey, parentID: parentIdKey, children: childrenKey } = fields
  for (let i = 0; i < tree.length; i++) {
    const item = tree[i]
    if (item[idKey] === node[parentIdKey]) {
      parentNodes.push(item)
      return
    }
    if (item[childrenKey] && item[childrenKey].length > 0) {
      findParent(node, parentNodes, item[childrenKey])
    }
  }
}

export default findAllParent
