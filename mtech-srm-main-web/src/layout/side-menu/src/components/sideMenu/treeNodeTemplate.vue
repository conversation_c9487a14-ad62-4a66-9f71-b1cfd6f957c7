<template>
  <div class="tree-node" :data-rootId="rootId">
    <div class="nodetext">
      <span v-if="icon" class="node-icon">
        <svg-icon :icon-class="icon"></svg-icon>
      </span>
      <span class="node-content" :title="name">{{ name }}</span>
    </div>
    <div v-if="count" class="nodebadge">
      <span class="treeCount">{{ count }}</span>
    </div>
  </div>
</template>

<script>
export default {
  inject: {
    mtSideMenu: {
      default: ''
    }
  },
  data() {
    return {
      data: {}
    }
  },
  computed: {
    fields() {
      return this.mtSideMenu ? this.mtSideMenu.fieldsVal : ''
    },
    icon() {
      const { icon } = this.fields
      return icon ? this.data[icon] : ''
    },
    name() {
      const { text } = this.fields
      return text ? this.data[text] : ''
    },
    count() {
      const { count } = this.fields
      return count ? this.data[count] : ''
    },
    rootId() {
      const { rootId } = this.fields
      return rootId ? this.data[rootId] : ''
    },

    path() {
      const { path } = this.fields
      return path ? this.data[path] : ''
    },
    selectMenu() {
      return this.mtSideMenu ? this.mtSideMenu.selectMenu : ''
    },
    allowSession() {
      return this.mtSideMenu ? this.mtSideMenu.allowSession : ''
    }
  },
  mounted() {},
  methods: {
    redirectTo() {
      const { path } = this
      console.log('path', this)
      if (this.data.url) {
        window.open(this.data.url, this.data.urlOpenType || '_blank', this.data.urlData)
      } else if (path) {
        this.$router && this.$router.push({ path })
        this.allowSession &&
          this.selectMenu &&
          sessionStorage.setItem('currentSideMenu', JSON.stringify(this.selectMenu))
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .nodetext {
    display: flex;
    align-items: center;
    width: 100%;
  }
  .node-icon {
    padding-right: 10px;
    font-size: 16px;
  }
  .node-content {
    font-size: 14px;
  }
  .icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
  }
  .nodebadge {
    .treeCount {
      min-width: 24px;
      text-align: center;
      line-height: 14px;
      background: var(--msm-treeCount-bg-color);
      border-radius: 7px;
      color: var(--msm-white-color);
      display: inline-block;
    }
  }
}
</style>
