<template>
  <div class="menu-node" @click="redirectTo">
    <span v-if="icon" class="node-icon">
      <svg-icon :icon-class="icon"></svg-icon>
      <!-- <i :class="['mt-icons', icon]"></i> -->
    </span>
    <div class="node-content">{{ name }}</div>
  </div>
</template>

<script>
export default {
  inject: {
    mtSideMenu: {
      default: ''
    }
  },
  data() {
    return {
      data: {}
    }
  },
  computed: {
    fields() {
      return this.mtSideMenu ? this.mtSideMenu.fieldsVal : ''
    },
    icon() {
      const { icon } = this.fields
      return icon ? this.data[icon] : ''
    },
    name() {
      const { text } = this.fields
      return text ? this.data[text] : ''
    },
    path() {
      const { path } = this.fields
      return path ? this.data[path] : ''
    },
    selectMenu() {
      return this.mtSideMenu ? this.mtSideMenu.selectMenu : ''
    },
    allowSession() {
      return this.mtSideMenu ? this.mtSideMenu.allowSession : ''
    }
  },
  mounted() {
    console.log(this.data)
  },
  methods: {
    redirectTo() {
      const { path } = this
      console.log('object', path)
      if (path) {
        this.$router && this.$router.push({ path })
        setTimeout(() => {
          this.allowSession &&
            this.selectMenu &&
            sessionStorage.setItem('currentSideMenu', JSON.stringify(this.selectMenu))
        }, 200)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.menu-node {
  display: flex;
  align-items: center;
  .node-icon {
    // padding-right: 5px;
    font-size: 16px;
  }
  .icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
  }
  .node-content {
    font-size: 14px;
  }
}
</style>
