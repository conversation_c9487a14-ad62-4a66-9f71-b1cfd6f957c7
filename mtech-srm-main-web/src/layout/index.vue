<template>
  <div class="layout">
    <common-layout
      class="mtCommonLayout"
      ref="commonLayout"
      @emitEvent="emitEvent"
      @changeTheme="changeTheme"
      :user-info="userInfo"
      :route-info="routeInfo"
      :open-i18n="true"
      :open-theme="false"
      :tags-class="tagsClass"
      :remind="remind"
      @clickMessage="clickMessage"
      @clickInform="clickInform"
      :defined-menu-select="true"
      project-name="main"
    >
      <div class="mt-container">
        <div class="mt-main-container">
          <micro-container
            v-show="isMicroRoute"
            container-class="mainMicroContainer"
          ></micro-container>
          <router-view></router-view>
        </div>
      </div>
      <!-- 消息提示 -->
      <div class="messageDialog" v-if="messageDialog">
        <nav>
          <div class="navMain">
            <!--  (·-)待办 -->
            <div @click="pendingClick">
              <img v-if="iconSwitch" src="../assets/icon_card_quest (1).png" alt="" />
              <img v-if="!iconSwitch" src="../assets/icon_card_quest.png" alt="" />
            </div>
            <span class="solid"></span>
            <!-- 消息提示 -->
            <div @click="messageClick">
              <img v-if="!iconSwitch" src="../assets/icon_card_info.png" alt="" />
              <img v-if="iconSwitch" src="../assets/icon_card_info (1).png" alt="" />
            </div>
          </div>
        </nav>
        <main>
          <div class="mainDiv">
            <div class="noMessage" v-if="allMessages.length <= 0">
              <img src="../assets/暂无数据.png" alt="" />
              <p>{{ $t('暂无消息') }}</p>
            </div>
            <div v-else class="existingMessage">
              <message
                v-show="messageBacklog"
                @clickMessageRead="clickMessageRead"
                :all-messages="allMessages"
              ></message>
              <pending-messages v-show="!messageBacklog"></pending-messages>
            </div>
          </div>
        </main>
        <footer>
          <div class="oerationButton">
            <div @click="allReadClick">{{ $t('全部已读') }}</div>
            <div @click="goMessageCenterClick">{{ $t('前往消息中心') }}</div>
          </div>
        </footer>
      </div>
    </common-layout>
    <div class="maskLayer" v-if="messageDialog" @click="maskLayerClick"></div>
    <div class="must-read-msg" v-if="mustReadMsgDialog">
      <div class="content-box">
        <div class="header">{{ $t('重要信息通知') }}</div>
        <div class="msg-list">
          <ul>
            <li v-for="item in mustReadMsgList" :key="item.id">{{ item.content }}</li>
          </ul>
        </div>
        <p class="notice">
          {{
            $t(
              '注：如果您是非打单发货人员，请务必通知到贵司打单发货人员核对更新的叫料计划，避免给贵司带来停产考核的损失！'
            )
          }}
        </p>
        <div class="footer">
          <span class="confirm-btn" @click="areadyReadClick">{{ $t('查看详细叫料计划') }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import actions from '@/utils/actions'
import { mapGetters } from 'vuex'
import { MicroContainer } from '@mtech-micro-frontend/vue-main'
import CommonLayout from './mtech-common-layout/src/views/index'
import { utils } from '@mtech-common/utils'
import { getToken } from '@mtech-sso/single-sign-on'
import { cloneDeep } from 'lodash'
import {
  trackSignIn,
  setProfile,
  sensorLogin,
  setLoginId,
  createSessionId
} from '@/utils/sensors/events'

export default {
  data() {
    return {
      tagsClass: localStorage.getItem('mt-layout-theme')
        ? localStorage.getItem('mt-layout-theme') === 'default'
          ? 'nav-content'
          : 'nav-content-dark'
        : 'nav-content',

      isMicroRoute: false,
      userInfo: {
        logoUrl: 'menu_logo.png'
      },
      routeInfo: {
        routesIn: [],
        routesSupplier: [],
        allowSwitch: false
      },

      fields: {
        id: 'id',
        text: 'name',
        icon: 'icon',
        path: 'path',
        children: 'children',
        parentId: 'parentId'
      },
      //铃铛 消息 是否显示
      remind: {
        message: { index: 0, show: false }, // index表示角标数，为0时不显示；show表示是否显示该栏
        inform: { index: 0, show: true } // index表示角标数，为0时不显示；show表示是否显示该栏
      },
      allMessages: [], //我的所有消息
      iconSwitch: true, //图标显示开关
      //消息弹框是否隐藏
      messageDialog: false,
      // 显示消息/待办
      messageBacklog: true,
      // 是否有采购商城前台权限
      isShowMall: false,
      token: '',
      mustReadMsgDialog: false,
      mustReadMsgList: []
    }
  },
  components: {
    CommonLayout,
    MicroContainer,
    // 新消息
    message: () =>
      import(
        /* webpackChunkName: "src/components/layout/components/newMassage.vue" */ './components/message.vue'
      ),
    // 待办消息
    pendingMessages: () =>
      import(
        /* webpackChunkName: "src/components/layout/components/pendingMessages.vue" */ './components/pendingMessages.vue'
      )
  },
  mounted() {
    // 初始化时立即获取一次必读消息
    this.getMustReadMsgList()
    this.loopInnerMsg()
    this.initialCallInterface() //初始调用接口
    this.checkIsMicroRoute(this.$route)
    this.addEvent(document, 'visibilitychange', this.visibleHandle)
  },
  watch: {
    $route(route) {
      this.checkIsMicroRoute(route)
    }
  },
  computed: {
    ...mapGetters(['lang'])
  },
  async created() {
    localStorage.setItem('mt-layout-theme', 'dark')
    try {
      await this.getUserMenu()
    } catch (error) {
      console.log(error)
    }
    await this.getUserInfo()
    this.token = getToken()
  },
  beforeDestroy() {
    clearInterval(window.interValId)
  },
  methods: {
    // 循环调取接口查询站内待办必读消息
    loopInnerMsg() {
      if (window.interValId) window.interValId = null
      window.interValId = setInterval(() => {
        this.getMustReadMsgList()
      }, 300000)
    },
    getMustReadMsgList() {
      this.$api.source.mustReadMsg().then((res) => {
        if (res.code === 200) {
          this.mustReadMsgList = res.data || []
          if (this.mustReadMsgList.length) {
            this.mustReadMsgDialog = true
          }
        }
      })
    },
    //初始调用接口
    initialCallInterface() {
      //未读消息
      this.$api.source.inmailUnReadCount().then((res) => {
        console.log(res.data)
        this.remind.inform.index = res.data
      })
      //我的全部消息
      const parameter = {
        page: {
          current: 1,
          size: 100
        }
      }
      this.$api.source.inmailMy(parameter).then((res) => {
        this.allMessages = res.data.records
        console.log(res, '我的所有消息')
      })
    },
    //完成读取消息后
    clickMessageRead() {
      this.initialCallInterface()
    },
    //点击待办
    pendingClick() {
      this.messageBacklog = false
      this.iconSwitch = false
    },
    //点击消息
    messageClick() {
      this.messageBacklog = true
      this.iconSwitch = true
    },
    //---头部---点击消息框
    clickMessage(e) {
      console.log(e)
    },
    //--头部--点击按铃
    clickInform() {
      this.messageDialog = !this.messageDialog
    },
    //点击全部已读
    allReadClick() {
      console.log('全部已读')
    },
    // 修改逻辑为跳转到叫料计划（空调）
    areadyReadClick() {
      const ids = this.mustReadMsgList.map((item) => item.id)
      this.$api.source
        .msgAreadyRead(ids)
        .then((res) => {
          if (res.code === 200) {
            this.mustReadMsgDialog = false
            const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
            const _path =
              userInfo.tenantId == '10000'
                ? '/purchase-execute/purchase-jit-kt'
                : '/purchase-execute/purchase-jit-supplier'
            this.$router.push({ path: _path })
          }
        })
        .finally(() => {
          this.mustReadMsgDialog = false
        })
    },
    //点击前往消息中心
    goMessageCenterClick() {
      this.messageDialog = false
      console.log('前往消息中心')
      this.$router.push(`/main/notice`)
    },
    maskLayerClick() {
      this.messageDialog = false
    },
    changeTheme(e) {
      console.log('qihuan', e)
      if (e === 'default') {
        this.tagsClass = 'nav-content'
      } else if (e === 'dark') {
        this.tagsClass = 'nav-content-dark'
      }
    },
    getUserInfo() {
      this.$api.source.queryUserInfo().then((res) => {
        if (res.data.tenantId != '10000' && res.data.tenantId != '-99') {
          this.$refs.commonLayout.$refs.sidebarInstance.switchToggle('supplier')
        }
        this.userInfo = res.data
        this.userInfo.nickName = res.data.realName
        this.userInfo.logoUrl = require('@/assets/<EMAIL>')
        let cloneUserInfo = cloneDeep(this.userInfo)
        this.userInfo.username =
          res.data.tenantId === '10000' || res.data.tenantId === '-99'
            ? res.data.username
            : res.data.enterpriseName
        this.$store.dispatch('app/setUserInfo', cloneUserInfo)
        sessionStorage.setItem('userInfo', JSON.stringify(cloneUserInfo))
        // 埋点
        trackSignIn({
          user_name: res.code == 200 ? res.data.loginName : '',
          is_success: res.code == 200 ? 1 : 0
        })
        if (res.code === 200) {
          // 埋点>>>
          const { username, employeeId, employeeCode } = res.data
          const userId = employeeCode || employeeId
          const bu = this.$store.state.app.buVal
          const buWhiteList = ['BD', 'KT', 'TV']
          if (buWhiteList.includes(bu)) {
            sensorLogin(userId + ',' + bu)
          } else {
            sensorLogin(userId)
          }
          setLoginId(employeeId)
          setProfile({
            $name: username
          })
          createSessionId()
          // 埋点<<<
          this.employeeId = employeeId
        }
      })
    },
    async getUserMenu() {
      const buRes = await this.$api.source.getBusinessList({})
      if (buRes.code === 200 && Array.isArray(buRes.data)) {
        this.$store.dispatch('app/setBuList', buRes.data)
      }
      const buVal = buRes.data?.length ? buRes.data[0]['value'] : 'TV'
      localStorage.setItem('currentBu', buVal)
      this.$store.dispatch('app/setBu', buVal)
      const res = await this.$api.source.queryUserMenu({
        code: { appCode: utils.getAppCode() },
        buVal
      })
      let routesIn = []
      let routesSupplier = []
      if (res?.data?.routesIn && res.data.routesIn.length > 0) {
        res.data.routesIn.forEach((v) => {
          routesIn.push(v)
        })
      }
      if (res?.data?.routesSupplier && res.data.routesSupplier.length > 0) {
        res.data.routesSupplier.forEach((v) => {
          routesSupplier.push(v)
        })
      }
      this.routeInfo = Object.assign({}, this.routeInfo, {
        routesIn: routesIn,
        routesSupplier: routesSupplier
      })
      sessionStorage.setItem('routeInfo', JSON.stringify(this.routeInfo))
      // const allRoutes = routesIn.concat(routesSupplier)
      // const storeRoutes = this.flatRoutes(allRoutes)
      // this.$store.dispatch('app/setRoutes', storeRoutes)
      // 如果当前菜单路由是无权限路由则重定向到404页面
      // if (!storeRoutes.some((item) => item.path === this.$route.path)) {
      //   this.$route.push('/404')
      // }
    },
    flatRoutes(arr) {
      if (!Array.isArray(arr)) return []
      return [].concat(...arr.map((item) => [].concat(item, ...this.flatRoutes(item.children))))
    },
    checkIsMicroRoute(route) {
      if (route.path.startsWith('/main')) {
        this.isMicroRoute = false
      } else {
        this.isMicroRoute = true
      }
    },

    emitEvent(eventName, eventParams) {
      console.log(eventName, eventParams)
      if (eventName === 'TOGGLE_SIDEBAR') {
        // 开关侧边栏
      }

      if (eventName === 'setLocal') {
        // 切换语言
        const lang = eventParams
        this.$store.dispatch('app/setLang', lang)
        this.$i18n.locale = lang
        actions.setGlobalState({ lang })
      }
    },
    addEvent(domNode, eventType, handlerFunction) {
      if (window.addEventListener) {
        domNode.addEventListener(eventType, handlerFunction)
      } else {
        domNode.attachEvent(`on${eventType}`, handlerFunction)
      }
    },
    visibleHandle() {
      if (document.visibilityState === 'visible') {
        const token = getToken()
        if (token !== this.token) {
          window.location.reload()
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.layout {
  min-width: 1200px;
}
/deep/.mtCommonLayout {
  .option-name {
    max-width: none !important;
  }
}
.mt-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
  position: relative;
  transition: width 0.4s;
  padding: 0 16px;
  height: 100%;
  background: linear-gradient(rgba(250, 250, 250, 1), rgba(250, 250, 250, 1)),
    linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 1));
  .mt-main-container {
    flex: 1;
    min-height: 0;
    // box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);

    > div {
      height: 100%;
      /deep/ .mainMicroContainer {
        height: 100%;
        > div {
          height: 100%;
        }
      }
    }
  }
}
// 弹框
.messageDialog {
  width: 360px;
  height: 306px;
  max-height: 442px;
  min-height: 306px;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 10000;
  border-radius: 8px;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.12);
  background: rgba(255, 255, 255, 1);
  nav {
    width: 100%;
    height: 60px;
    padding: 0 16px;
    box-sizing: border-box;
    .navMain {
      width: 100%;
      height: 60px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid rgba(232, 232, 232, 1);
      div {
        width: 50%;
        height: 100%;
        // border: 1px solid #000;
        display: flex;
        justify-content: space-around;
        align-items: center;
      }
      .solid {
        display: block;
        width: 1px;
        height: 40px;
        background-color: rgba(232, 232, 232, 1);
      }
    }
  }
  main {
    width: 100%;
    height: 184px;
    overflow-y: scroll;
    .mainDiv {
      width: 100%;
      .noMessage {
        width: 80px;
        height: 110px;
        margin: 30px auto 44px;
        img {
          width: 80px;
          height: 80px;
          border: 1px dotted #000;
        }
        p {
          margin-top: 12px;
          width: 80px;
          height: 14px;
          text-align: center;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(154, 154, 154, 1);
        }
      }
      .existingMessage {
        width: 100%;
      }
    }
  }
  footer {
    width: 100%;
    height: 62px;
    position: absolute;
    left: 0;
    bottom: 0;
    padding: 0 16px;
    box-sizing: border-box;
    .oerationButton {
      width: 100%;
      height: 62px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-top: 1px solid rgba(232, 232, 232, 1);
      div {
        height: 30px;
        line-height: 30px;
        padding: 0 10px;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
      }
      div:hover {
        cursor: pointer;
      }
    }
  }
}
//遮罩层
.maskLayer {
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: transparent;
}
.must-read-msg {
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  .content-box {
    width: 1000px;
    height: 500px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    // border: 3px solid rgba(0, 0, 0, 0.6);
    border-radius: 12px;
    padding: 0 12px;
    word-break: break-all;
    white-space: normal;
    background: #fff;
    box-shadow: 0 0 2px #fff;
    .header {
      height: 32px;
      text-align: center;
      line-height: 32px;
      font-size: 22px;
      font-weight: 600;
      color: #333;
      margin-top: 12px;
    }
    .msg-list {
      height: 336px;
      overflow: auto;
      padding-bottom: 40px;
      ul {
        list-style: inside;
      }
      li {
        margin-top: 18px;
        font-size: 18px;
        line-height: 24px;
      }
    }
    .notice {
      color: red;
      font-size: 18px;
      line-height: 22px;
    }
    .footer {
      height: 48px;
      width: calc(100% - 24px);
      position: absolute;
      bottom: 0;
      z-index: 10;
      margin-top: 16px;
      text-align: right;
      background: #fff;
      .confirm-btn {
        margin: 8px 24px 12px 0;
        font-size: 14px;
        letter-spacing: 2px;
        cursor: pointer;
        text-align: center;
        background-color: #4e5a70;
        padding: 0;
        border: none;
        width: 100%;
        color: #fff;
        padding: 8px 12px;
        border-radius: 4px;
      }
    }
  }
}
</style>
