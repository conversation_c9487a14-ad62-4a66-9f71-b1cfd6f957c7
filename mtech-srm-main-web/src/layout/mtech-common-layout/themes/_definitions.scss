:root  {  
    --mcl-white-color:var(--accent-font);
    // topNav  
    --mcl-left-nav-sidebar-bg-color: var(--grey-100); // 自定义颜色变量
    --mcl-top-nav-background-color:var(--mcl-top-nav-background-color);
    --mcl-top-nav-tabs-arrow-box-shadow:rgba(0, 0, 0, 0.1);
    --mcl-top-nav-tabs-arrow-border-right:#6e7a8d;
    --mcl-top-nav-option-name-color:#232b39;
    --mcl-top-nav-e-left-color:#edeff3;
    --mcl-top-nav-e-focused-bg-color:var(--mcl-top-nav-e-focused-bg-color);
    // tag contextmenu
    --mcl-tags-bg-color:#f0f0f0;
    --mcl-tags-color:var(--grey-500);
    --mcl-tags-icon-close-color:#98aac3;
    --mcl-contextmenu-color:var(--grey-200);
    --mcl-contextmenu-bg-color:var(--grey-white);
    // base
    --mcl-base-left-nav-box-background-color:var(--grey-100);
    --mcl-base-left-nav-box-box-shadow:#edeff3;
    --mcl-base-active-color:var(--accent);
    --mcl-base-right-deg-bg-color:var(-mcl-base-right-deg-bg-color);
    --mcl-base-text-color:rgba($base-font, 0.87);
    --mcl-base-box-shadow:rgba(0, 0, 0, 0.3);
  }