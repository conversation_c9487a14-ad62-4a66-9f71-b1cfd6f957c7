body {
    margin: 0;
    box-sizing: border-box;
}

div {
    box-sizing: border-box;
}

li {
    text-decoration: none;
}

.ivu-layout {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-flex: 1;
    -ms-flex: auto;
    flex: auto;
    background: #f5f7f9
}

.ivu-layout.ivu-layout-has-sider {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row
}

.ivu-layout.ivu-layout-has-sider>.ivu-layout, .ivu-layout.ivu-layout-has-sider>.ivu-layout-content {
    overflow-x: hidden
}

.ivu-layout-footer, .ivu-layout-header {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto
}

.ivu-layout-header {
    /* background: linear-gradient(rgba(245, 245, 245, 1), rgba(245, 245, 245, 1)), linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 1));
    ; */
    padding: 0 50px;
    height: 50px;
    line-height: 54px
}

.ivu-layout-sider {
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
    position: relative;
    /* background: linear-gradient(rgba(245, 245, 245, 1), rgba(245, 245, 245, 1)), linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 1));
    ; */
    min-width: 0
}

.ivu-layout-sider-children {
    height: 100%;
    padding-top: .1px;
    margin-top: -.1px
}

.ivu-layout-sider-has-trigger {
    padding-bottom: 48px
}

.ivu-layout-sider-trigger {
    position: fixed;
    bottom: 0;
    text-align: center;
    cursor: pointer;
    height: 48px;
    line-height: 48px;
    color: #fff;
    /* background: linear-gradient(rgba(245, 245, 245, 1), rgba(245, 245, 245, 1)), linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 1));
    ; */
    z-index: 1;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ivu-layout-sider-trigger .ivu-icon {
    font-size: 16px
}

.ivu-layout-sider-trigger>* {
    -webkit-transition: all .2s;
    transition: all .2s
}

.ivu-layout-sider-trigger-collapsed .ivu-layout-sider-trigger-icon {
    -webkit-transform: rotateZ(180deg);
    -ms-transform: rotate(180deg);
    transform: rotateZ(180deg)
}

.ivu-layout-sider-zero-width>* {
    overflow: hidden
}

.ivu-layout-sider-zero-width-trigger {
    position: absolute;
    top: 64px;
    right: -36px;
    text-align: center;
    width: 36px;
    height: 42px;
    line-height: 42px;
    background: linear-gradient(rgba(245, 245, 245, 1), rgba(245, 245, 245, 1)), linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 1));
    ;
    color: #fff;
    font-size: 18px;
    border-radius: 0 6px 6px 0;
    cursor: pointer;
    -webkit-transition: background .3s ease;
    transition: background .3s ease
}

.ivu-layout-sider-zero-width-trigger:hover {
    background: #626b7d
}

.ivu-layout-sider-zero-width-trigger.ivu-layout-sider-zero-width-trigger-left {
    right: 0;
    left: -36px;
    border-radius: 6px 0 0 6px
}

.ivu-layout-footer {
    background: #f5f7f9;
    padding: 24px 50px;
    color: linear-gradient(rgba(245, 245, 245, 1), rgba(245, 245, 245, 1)), linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 1));
    ;
    font-size: 14px
}

.ivu-layout-content {
    -webkit-box-flex: 1;
    -ms-flex: auto;
    flex: auto;
    height: calc(100vh - 50px);
    background: linear-gradient(rgba(250, 250, 250, 1), rgba(250, 250, 250, 1)), linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 1));
}