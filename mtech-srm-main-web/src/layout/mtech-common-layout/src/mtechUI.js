import Vue from 'vue'
// import MtechUI from '@mtech-ui/mtech-ui'
import MtSelect from '@mtech-ui/select'
// import MtIcon from '@mtech-ui/icon'
import store from './store/index'
// import { MenuPlugin } from '@syncfusion/ej2-vue-navigations'
// import { AutoCompletePlugin } from '@syncfusion/ej2-vue-dropdowns'

// import '@mtech-ui/base/build/themes/default.scss'
// import '@mtech-ui/base/build/esm/bundle.css'
import '../themes/dark.scss'
import '@mtech/side-menu/build/esm/bundle.css'
import './assets/iconfont.js'
import { API, baseConfig } from '@mtech-common/http'

Vue.use(store)
Vue.use(MtSelect)
// Vue.use(MtIcon)
// Vue.use(MenuPlugin)
// Vue.use(AutoCompletePlugin)
// Vue.use(MtechUI)
Vue.prototype.API = API
baseConfig.setDefault({ baseURL: '/api' })
