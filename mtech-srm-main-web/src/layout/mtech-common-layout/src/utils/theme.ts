import cssVars from 'css-vars-ponyfill' // css var 的垫片

type TThemeType = 'default' | 'dark'

export default class ChangeTheme {
  styleId = ''
  private isIE = false

  constructor(id: string) {
    this.styleId = id
    this.isIE = this.checkIsIE()
  }

  change(theme: TThemeType) {
    this.getInitLinkFile(theme)
    if (this.isIE) {
      setTimeout(() => {
        cssVars({
          watch: true
        })
      })
    }
  }

  private async getInitLinkFile(theme: TThemeType) {
    const href = `${location.origin}/themes/${theme}.css`
    
    this.createLink(href) 
  }

  private createLink (href: string) {  
    const link = document.createElement('link')
    link.onload = this.deleteLink(href)
    link.setAttribute('rel', 'stylesheet')
    link.setAttribute('type', 'text/css')
    link.setAttribute('href', href)

    document.head.appendChild(link) 
  }

  private deleteLink(href:string) { 
    return () => {
      const linkList = document.querySelectorAll('link')  
      const themeDir = `${location.origin}/themes`
    
      for (let i = 0; i < linkList.length; i++) {
        if (linkList[i].href !== href && linkList[i].href.startsWith(themeDir)) {
          linkList[i].remove() 
        }
      } 
    }
  }

  private checkIsIE() {
    // TOOD: IE浏览器 的判断
    return false
  }
  
  private getFile(url: string, isBlob?: boolean): Promise<string> {
    return new Promise((resolve, reject) => {
      const client = new XMLHttpRequest()
      client.responseType = isBlob ? 'blob' : ''
      client.onreadystatechange = () => {
        if (client.readyState !== 4) {
          return
        }
        if (client.status === 200) { 
          resolve(client.response)
        } else {
          reject(new Error(client.statusText))
        }
      }
      client.open('GET', url)
      client.send()
    })
  }

  /**
   * 创建新的<style>
   * @param cssText 模板样式字符串
   * @returns 
   */
  private createNewStyle(cssText: string): HTMLStyleElement {
    let style = document.querySelector('#' + this.styleId) as HTMLStyleElement

    if (!style) {
      style = document.createElement('style')
      style.id = this.styleId
      style.innerHTML = cssText
      document.head.appendChild(style)
    } else {
      style.innerHTML = cssText
    }

    return style
  }
}
