<template>
  <div id="tags-view-tabs" ref="tags" :class="tagsViewClass">
    <vuedraggable class="vuedraggable">
      <div
        v-for="(tag, i) in visitedViews"
        ref="tag"
        :key="tag.path"
        :class="isActive(tag) ? 'active' : ''"
        tag="span"
        class="tags-view-item"
        :style="{ 'padding-right': isShowCloseIcon(tag) ? '32px' : '16px' }"
        @contextmenu.prevent="openMenu(tag, $event)"
        @click="routerTo(tag)"
      >
        <div class="text-class">
          {{ tag.title }}
        </div>
        <span
          v-if="isShowCloseIcon(tag)"
          class="mt-icons mt-icon-Close_F text-class"
          @click.prevent.stop="closeSelectedTag(tag)"
        />

        <span v-if="isActive(tag)" class="right-deg active"></span>

        <span v-else-if="i === visitedViews.length - 1" class="right-deg"></span>
      </div>
    </vuedraggable>
    <ul v-show="visible" :style="{ left: left + 'px', top: top + 'px' }" class="contextmenu">
      <!-- <li @click="refreshSelectedTag(selectedTag)">刷新页面</li> -->
      <!-- <li v-if="!isAffix(selectedTag)" @click="closeSelectedTag(selectedTag)">关闭当前</li> -->
      <li @click="closeOthersTags">{{ $t('关闭其他') }}</li>
      <li @click="closeAllTags()">{{ $t('关闭所有') }}</li>
    </ul>
  </div>
</template>

<script>
import vuedraggable from 'vuedraggable'
import store from '../../store/index'

export default {
  name: 'TagsView',
  components: { vuedraggable },
  data() {
    return {
      isPurchase: location.host.includes('-main') || location.host.includes('localhost'), // 是否为采方
      visible: false,
      moreList: [],
      top: 0,
      left: 0,
      selectedTag: {},
      affixTags: [],
      showMore: false,
      activePath: '',
      clickMenuTag: false,
      isGoBack: false
    }
  },
  props: {
    projectName: {
      type: String,
      default: 'main'
    },
    clickMenu: {
      type: Boolean,
      default: false
    },
    routeInfo: {
      type: Object,
      default: () => {}
    },
    tagsClass: {
      type: String,
      default: 'nav-content'
    }
  },
  computed: {
    visitedViews() {
      return store.state.tagsView.visitedViews
    },
    routes() {
      return store.state.permission.routes
    },
    vLength() {
      return store.state.tagsView.visitedViews.length
    },
    tagsViewClass() {
      let className = ''
      this.tagsClass === 'nav-content'
        ? (className = 'tags-view-container')
        : (className = 'tags-view-container-dark')
      return className
    }
  },
  watch: {
    $route() {
      this.addTags()
      // this.getId(!this.clickMenu,this.$route.path)
      this.getId(true, this.$route.path)
      setTimeout(() => {
        this.visitedViews.forEach((e) => {
          if (this.isActive(e)) {
            const ele1 = this.getTreeItem(this.routeInfo.routesIn, this.$route.path)
            const ele2 = this.getTreeItem(this.routeInfo.routesSupplier, this.$route.path)
            if (!ele1 && !ele2) {
              let a = true
              this.visitedViews.forEach((e2) => {
                if (e2.childRoute && JSON.stringify(e2.childRoute) == JSON.stringify(this.$route)) {
                  a = false
                }
              })
              if (a) {
                if (e.childRoute) {
                  if (e.lineRoute && e.lineRoute.length > 0) {
                    console.log('this.isGoBack', this.isGoBack)
                    if (this.isGoBack) {
                      e.lineRoute.pop()
                    } else {
                      e.lineRoute.push(e.childRoute)
                    }
                  } else {
                    if (!this.isGoBack) {
                      e.lineRoute = []
                      e.lineRoute.push(e.childRoute)
                    }
                  }
                }
                e.childRoute = this.$route
                console.log('e.lineRoute', e.lineRoute)
              }
            } else if (e.childRoute) {
              delete e.childRoute
              delete e.lineRoute
            }
          }
        })
      }, 0)
    },
    clickMenu: {
      handler(v) {
        console.log('$clickMenu', v)
        this.clickMenuTag = v
      },
      deep: true
    },
    visitedViews() {
      this.$nextTick(() => {
        if (this.isEllipsis(this.$refs.tags)) {
          this.$emit('getShowTag', true)
        } else {
          this.$emit('getShowTag', false)
        }
      })
    },
    vLength(v, a) {
      if (v > a) {
        this.$emit('goRight', this.$refs.tags.offsetWidth)
      }
    },
    routeInfo() {
      this.getId(true, this.$route.path)
    },
    visible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    }
  },
  created() {
    let _this = this
    window.onpopstate = function (e) {
      _this.isGoBack = e.isTrusted
      console.log('I am PopState', e.isTrusted)
    }
  },
  mounted() {
    this.openMore()
    this.$router.beforeEach(function (to, from, next) {
      const domArr = document.getElementsByClassName('e-tooltip-wrap')
      const arr = Array.from(domArr)
      if (arr.length > 0) {
        arr[0].style.display = 'none'
      }
      next()
    })
  },
  methods: {
    isShowCloseIcon(tag) {
      // 首页跟工作台的tagsview不能关闭
      const wihteList = ['/middlePlatform/dashboard', '/middlePlatform/workbench']
      return !wihteList.includes(tag.path)
    },
    fireKeyEvent(el, evtType, keyCode) {
      var doc = el.ownerDocument,
        win = doc.defaultView || doc.parentWindow,
        evtObj
      if (doc.createEvent) {
        if (win.KeyEvent) {
          evtObj = doc.createEvent('KeyEvents')
          evtObj.initKeyEvent(evtType, true, true, win, false, false, false, false, keyCode, 0)
        } else {
          evtObj = doc.createEvent('UIEvents')
          Object.defineProperty(evtObj, 'keyCode', {
            get: function () {
              return this.keyCodeVal
            }
          })
          Object.defineProperty(evtObj, 'which', {
            get: function () {
              return this.keyCodeVal
            }
          })
          evtObj.initUIEvent(evtType, true, true, win, 1)
          evtObj.keyCodeVal = keyCode
          if (evtObj.keyCode !== keyCode) {
            console.log('keyCode ' + evtObj.keyCode + ' 和 (' + evtObj.which + ') 不匹配')
          }
        }
        el.dispatchEvent(evtObj)
      } else if (doc.createEventObject) {
        evtObj = doc.createEventObject()
        evtObj.keyCode = keyCode
        el.fireEvent('on' + evtType, evtObj)
      }
    },
    handleUrl(url) {
      var obj = {}
      var arrNew = url.split('?')[1].split('&')
      for (let i = 0; i < arrNew.length; i++) {
        var key = arrNew[i].split('=')[0]
        var value = arrNew[i].split('=')[1]
        //在对象中添加属性
        obj[key] = value
      }
      return obj
    },
    routerTo(tag) {
      this.visitedViews.forEach((e) => {
        if (e.path == tag.path) {
          console.log('routerto', e, tag)
          if (e.childRoute) {
            if (tag.path.indexOf('?') > -1) {
              this.$router.push({ path: tag.path.split('?')[0], query: this.handleUrl(tag.path) })
              if (e.lineRoute) {
                e.lineRoute.forEach((l) => {
                  this.$router.push({
                    path: l.fullPath.split('?')[0],
                    query: this.handleUrl(l.fullPath)
                  })
                })
              }
              this.$router.push({
                path: e.childRoute.fullPath.split('?')[0],
                query: this.handleUrl(e.childRoute.fullPath)
              })
            } else {
              this.$router.push(tag.path)
              if (e.lineRoute) {
                e.lineRoute.forEach((l) => {
                  this.$router.push({
                    path: l.fullPath.split('?')[0],
                    query: this.handleUrl(l.fullPath)
                  })
                })
              }
              this.$router.push(e.childRoute.fullPath)
            }
            this.getId(true, e.path)
          } else {
            if (tag.path.indexOf('?') <= -1) {
              this.$router.push(tag.path)
            }
            this.getId(true, e.path)
          }
        }
      })
      // this.getId(true,e.path)
    },
    // 通过路由的menuPath来获取当前菜单的id和parentId
    getId(bool, path) {
      if (path === '/' || path === '/404') {
        sessionStorage.removeItem('currentSideMenu')
        this.$emit('closeMenu')
        return
      }
      const ele = this.getTreeItem(this.routeInfo.routesIn, path)
      if (ele) {
        sessionStorage.setItem(
          'currentSideMenu',
          `{"id":"${ele.id}","parentId":"${ele.parentId}","name":"${ele.name}","toggle":"buyer"}`
        )
        // this.$emit('switchToggle', 'buyer')
        this.$emit('chooseMenu', bool)
      } else {
        const ele2 = this.getTreeItem(this.routeInfo.routesSupplier, path)
        if (ele2) {
          sessionStorage.setItem(
            'currentSideMenu',
            `{"id":"${ele2.id}","parentId":"${ele2.parentId}","name":"${ele2.name}","toggle":"supplier"}`
          )
          // this.$emit('switchToggle', 'supplier')
          this.$emit('chooseMenu', bool)
        } else if (sessionStorage.getItem('currentSideMenu')) {
          const ele3 = JSON.parse(sessionStorage.getItem('currentSideMenu'))
          if (ele3) {
            // this.$emit('switchToggle', ele3.toggle)
            this.$emit('chooseMenu', bool)
          }
        }
      }
    },

    getTreeItem(tree, path) {
      const flat = function (arr) {
        if (!Array.isArray(arr)) return []

        return [].concat(...arr.map((item) => [].concat(item, ...flat(item.children))))
      }
      return flat(tree).find((item) => item.path === path)
    },
    // 是否超出
    isEllipsis(dom) {
      let flag = true
      if (dom && dom.scrollWidth) {
        flag = dom.scrollWidth > dom.offsetWidth
      }
      return flag
    },
    // 通知上層展示箭頭
    openMore() {
      this.$emit('getShowTag', this.isEllipsis(document.getElementById('tags-view-tabs')))
    },
    isActive(route) {
      if (route.path === this.$route.path) {
        this.activePath = route.path
        return true
      } else if (route.childRoute && route.childRoute.fullPath === this.$route.fullPath) {
        this.activePath = route.path
        return true
      } else if (`#${route.path}` === decodeURIComponent(window.location.hash)) {
        this.activePath = route.path
        return true
      } else {
        if (this.$route.path === '/404') {
          return
        }
        for (let i = 0; i < this.visitedViews.length; i++) {
          if (this.visitedViews[i].path === this.$route.path) {
            return false
          }
        }
        return this.activePath === route.path
      }
    },
    addTags() {
      // const { name, path } = this.$route;
      if (!JSON.parse(sessionStorage.getItem('currentSideMenu'))) {
        return
      }
      const { name, path } = JSON.parse(sessionStorage.getItem('currentSideMenu'))
      if (this.projectName === 'main') {
        if (path === '/main/dashboard') {
          store.dispatch('tagsView/addView', this.$route)
        }
        return
      }
      if (name && this.projectName != 'main') {
        store.dispatch('tagsView/addView', this.$route)
      }
      return false
    },

    moveToCurrentTag() {
      const tags = this.$refs.tag
      if (!tags || tags.length === 0) return
      this.$nextTick(() => {
        for (const tag of tags) {
          if (tag.to.path === this.$route.path) {
            // when query is different then update
            if (tag.to.fullPath !== this.$route.fullPath) {
              store.dispatch('tagsView/updateVisitedView', this.$route)
            }
            break
          }
        }
      })
    },
    refreshSelectedTag(view) {
      store.dispatch('tagsView/delCachedView', view).then(() => {
        const { fullPath } = view
        this.$nextTick(() => {
          this.$router.replace({
            path: '/redirect' + fullPath
          })
        })
      })
    },
    closeSelectedTag(view) {
      store.dispatch('tagsView/delView', view).then(({ visitedViews }) => {
        console.log('visitedViews', visitedViews)
        if (visitedViews.length === 0) {
          // this.$router.push('/')
          this.getId(true, '/')
        }
        console.log('view1', view)
        if (this.isActive(view)) {
          this.toLastView(visitedViews, view)
        }
      })
    },
    closeOthersTags() {
      this.$router.push(this.selectedTag)
      store.dispatch('tagsView/delOthersViews', this.selectedTag).then(() => {
        this.moveToCurrentTag()
      })
    },
    closeAllTags() {
      const view = this.selectedTag
      this.getId(true, '/')
      store.dispatch('tagsView/delAllViews').then(({ visitedViews }) => {
        if (this.affixTags.some((tag) => tag.path === view.path)) {
          return
        }
        // this.getId(true,'/')
        this.toLastView(visitedViews, view)
      })
    },
    openMenu(tag, e) {
      const menuMinWidth = 105
      const offsetWidth = this.$el.offsetWidth // container width
      const maxLeft = offsetWidth - menuMinWidth // left boundary
      const left = e.clientX // 15: margin right

      if (left > maxLeft) {
        this.left = maxLeft
      } else {
        this.left = left
      }

      this.top = e.clientY
      this.visible = true
      this.selectedTag = tag
    },
    toLastView(visitedViews) {
      const latestView = visitedViews.slice(-1)[0]
      console.log('latestView', latestView)
      if (latestView) {
        this.$router.push(latestView.path)
        this.getId(true, this.$route.path)
      } else {
        // now the default is to redirect to the home page if there is no tags-view,
        // you can adjust it according to your needs.
        this.isPurchase ? this.$router.push('/') : this.$router.push('/middlePlatform/workbench')
        this.getId(true, this.$route.path)
      }
    },
    closeMenu() {
      this.visible = false
    }
  }
}
</script>

<style lang="scss">
.tags-view-container {
  display: flex;
  white-space: nowrap;
  .vuedraggable {
    height: 100px;
  }
  .text-class {
    transform: skew(20deg);
  }
  .tags-view-item {
    margin: 0 2px 0 0;
    display: inline-block;
    position: relative;
    cursor: pointer;
    height: 30px;
    line-height: 30px;
    left: 7px;
    border-radius: 4px 4px 0 0;
    color: var(--mcl-tags-color);
    background: var(--mcl-tags-bg-color);
    padding: 0 32px 0 16px;
    font-size: 14px;
    transform: skew(-20deg);
    border-bottom: none;
    z-index: 1;
    &.active {
      background-color: var(--mcl-base-right-deg-bg-color);
      color: var(--mcl-base-active-color);
      font-weight: 600;
      border-right: none;
      z-index: 10;
    }
    .right-deg {
      background-color: var(--mcl-tags-bg-color);
      position: absolute;
      transform: skew(35deg);
      height: 30px;
      width: 30px;
      top: 0;
      right: -11px;
      z-index: -1;
      &.active {
        background-color: var(--mcl-base-right-deg-bg-color);
      }
    }
    .el-icon-close,
    .mt-icon-Close_F {
      position: absolute;
      top: 8px;
      right: 16px;
      color: var(--mcl-tags-icon-close-color);
      width: 8px;
      height: 8px;
      font-size: 12px;
      vertical-align: 2px;
      border-radius: 50%;
      text-align: center;
      line-height: 18px;
    }
  }
  .contextmenu {
    margin: 0;
    background: var(--mcl-contextmenu-bg-color);
    z-index: 3000;
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    color: var(--mcl-base-text-color);
    box-shadow: 2px 2px 3px 0 var(--mcl-base-box-shadow);
    li {
      margin: 0;
      padding: 7px 16px;
      cursor: pointer;
      line-height: 20px;
      &:hover {
        background: var(--mcl-contextmenu-color);
      }
    }
  }
}
.tags-view-container-dark {
  display: flex;
  white-space: nowrap;
  align-items: center;
  height: 35px;
  .vuedraggable {
    // height: 100px;
  }
  .text-class {
    color: #333333;
  }
  .tags-view-item {
    margin: 0 2px 0 0;
    display: inline-block;
    position: relative;
    cursor: pointer;
    height: 30px;
    line-height: 30px;
    left: 7px;
    border-radius: 6px;
    color: var(--mcl-tags-color);
    background: var(--mcl-tags-bg-color);
    padding: 0 32px 0 16px;
    font-size: 14px;
    border-bottom: none;
    z-index: 1;
    &.active {
      background-color: var(--mcl-base-right-deg-bg-color);
      // font-weight: 600;
      border-right: none;
      z-index: 10;
      .text-class {
        color: var(--mcl-left-select-color);
      }
    }
    .right-deg {
      display: none;
    }
    .el-icon-close,
    .mt-icon-Close_F {
      position: absolute;
      top: 8px;
      right: 16px;
      color: var(--mcl-tags-icon-close-color);
      width: 8px;
      height: 8px;
      font-size: 12px;
      vertical-align: 2px;
      border-radius: 50%;
      text-align: center;
      line-height: 18px;
    }
  }
  .contextmenu {
    margin: 0;
    background: var(--mcl-contextmenu-bg-color);
    z-index: 3000;
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    color: var(--mcl-base-text-color);
    box-shadow: 2px 2px 3px 0 var(--mcl-base-box-shadow);
    li {
      margin: 0;
      padding: 7px 16px;
      cursor: pointer;
      line-height: 20px;
      &:hover {
        background: var(--mcl-contextmenu-color);
      }
    }
  }
}
</style>
