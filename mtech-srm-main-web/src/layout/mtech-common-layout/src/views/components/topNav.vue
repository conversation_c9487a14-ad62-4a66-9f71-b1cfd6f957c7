<template>
  <div class="mt-top-nav">
    <div :class="{ 'nav-logo': true, 'nav-logo-close': !openM }" @click="returnIndex">
      <img class="logo-img" src="@/assets/logo_tcl.png" style="height: 24px" />
      <!-- <img class="logo-img" :src="user.logoUrl" /> -->
      <span class="project-name" :style="{ color: isDarkTheme ? '#fff' : '#999' }">SRM</span>
      <!-- <i
        class="mt-icons mt-icon-a-icon_Shrinkexpand main-menu"
        @click="tgSideBar"
        v-if="leftNav"
      ></i> -->
    </div>
    <div :class="tagsClass">
      <p class="nav-title" data-text="成为全球领先的智能科技公司">成为全球领先的智能科技公司</p>
      <p class="nav-slogan" data-text="当责 | 创新 | 卓越">当责 | 创新 | 卓越</p>
      <!-- <div class="nav-tip-container">
        <p class="nav-tip">
          6+1泛智屏630版本上线倒计时 <span>{{ countDown() }}</span> 天
        </p>
      </div> -->
      <div v-if="showMore" class="tabs-arrow" @click="toLeft"></div>
      <div class="option-content" ref="tagsP">
        <slot></slot>
      </div>
      <div v-if="tagsClass === 'nav-content-dark'" class="closeTag" @click="closeTags">
        <span v-if="visitedViews.length != 0 && isPurchase">{{ $t('关闭全部页签') }}</span>
      </div>
      <div v-if="showMore" class="tabs-arrow arrow-right" @click="toRight"></div>
      <div class="public-content">
        <!-- 全屏 -->
        <div class="option-item split">
          <i class="mt-icons mt-icon-a-icon_Fullscreen full-screen" @click="toggleScreen"></i>
        </div>
        <!-- bu切换 -->
        <div class="option-item split" v-if="isPurchase">
          <mt-select
            v-model="setBuVal"
            :data-source="buList"
            :width="120"
            popup-width="150px"
            float-label-type="Never"
            :open-dispatch-change="false"
            @change="buChange"
          ></mt-select>
        </div>
        <!-- 时区切换 -->
        <div class="option-item split">
          <mt-select
            v-model="currentTimeZone"
            :data-source="timeZoneList"
            :width="120"
            popup-width="120px"
            float-label-type="Never"
            :open-dispatch-change="false"
            :readonly="true"
            @change="timeZoneChange"
          ></mt-select>
        </div>
        <!-- 主题切换 -->
        <div class="option-item split" v-if="openTheme">
          <mt-select
            :data-source="theme.data"
            :width="theme.width"
            popup-width="150px"
            float-label-type="Never"
            :value="themeValue"
            :fields="{ text: 'name', value: 'code' }"
            :open-dispatch-change="false"
            @change="themeChange"
          ></mt-select>
        </div>
        <!-- 语言切换 -->
        <div class="option-item split" v-if="openI18n">
          <mt-select
            :data-source="language.data"
            :width="langWidth(languageValue)"
            popup-width="150px"
            float-label-type="Never"
            :value="languageValue"
            :fields="{ text: 'name', value: 'code' }"
            @change="langChange"
          ></mt-select>
        </div>
        <!-- APP下载 -->
        <div class="option-item split">
          <span>
            <div class="option-name">APP</div>
          </span>
          <ejs-menu :items="appMenuItems" :select="appSelect" :show-item-on-click="true"></ejs-menu>
        </div>
        <!-- 消息 -->
        <div class="option-item split" @click="clickMessage" v-if="remind.message.show">
          <i class="mt-icons mt-icon-icon_solid_message option-svg"></i>
          <div class="option-badge" v-show="remind.message.index != 0">
            <span :class="{ dot: Number(remind.message.index) > 99 }">{{
              Number(remind.message.index) > 99 ? '...' : remind.message.index
            }}</span>
          </div>
        </div>
        <div class="option-item split" @click="clickInform" v-if="remind.inform.show">
          <!-- <i class="mt-icons mt-icon-icon_solid_inform option-svg"></i> -->
          <img src="../../assets/news.png" alt="" />
          <div class="option-badge" v-show="remind.inform.index != 0">
            <span :class="{ dot: Number(remind.inform.index) > 99 }">{{
              Number(remind.inform.index) > 99 ? '...' : remind.inform.index
            }}</span>
          </div>
        </div>
        <!-- 用户 -->
        <div class="option-item">
          <img v-if="!user.photo" class="avatar" src="../../assets/users.png" alt="" />
          <img v-else class="option-svg" style="font-size: 45px" :src="user.photo" />

          <span>
            <div class="option-name">{{ user.username }}</div>
          </span>
          <ejs-menu
            :items="menuItems"
            :select="handleSelectMenu"
            :show-item-on-click="true"
          ></ejs-menu>
        </div>
      </div>
    </div>
    <mt-dialog ref="dialog" :header="header" :height="380" :width="350" @close="handleClose">
      <div style="padding: 20px; display: flex; align-items: center; justify-content: center">
        <img v-if="showQRCode" :src="imgUrl" :height="250" :width="250" />
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import { MenuPlugin } from '@syncfusion/ej2-vue-navigations'
import { logOut } from '@mtech-sso/single-sign-on' //, hasAuth, clearAuth
import { mapGetters } from 'vuex'
import screenfull from 'screenfull'
// import TagsView from './tagsView.vue'
import { utils } from '@mtech-common/utils'
import Theme from '@/themes/theme'
import store from '../../store/index'
import { sensorLogout } from '@/utils/sensors/events'
import MtDialog from '@mtech-ui/dialog'
Vue.use(MenuPlugin)

export default {
  name: 'TopNav',
  props: {
    userInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    remind: {
      type: Object,
      default: () => {
        return {
          message: { index: 0, show: false },
          inform: { index: 0, show: false }
        }
      }
    },
    openI18n: {
      type: Boolean,
      default: false
    },
    openTheme: {
      type: Boolean,
      default: false
    },
    leftNav: {
      type: Boolean,
      default: false
    },
    tagsClass: {
      type: String,
      default: 'nav-content'
    }
  },
  components: {
    MtDialog
  },
  data() {
    return {
      isPurchase: location.host.includes('-main') || location.host.includes('localhost'), // 是否为采方
      languageValue: localStorage.getItem('internationlization') ?? 'zh',
      scrollNum: 0,
      openM: true,
      showMore: false,
      // transition: {
      //   display: false,
      //   list: ['新增报价', '新增代理商', '新增合同', '查看代理商']
      // },
      user: {
        name: this.$t('姓名'),
        role: this.$t('普通'),
        photo: ''
      },
      language: {
        width: '80px',
        data: [
          {
            name: this.$t('中文'),
            code: 'zh-CN'
          },
          {
            name: this.$t('英语'),
            code: 'en'
          }
        ]
      },
      themeValue: localStorage.getItem('mt-layout-theme')
        ? localStorage.getItem('mt-layout-theme')
        : 'default',
      // options: {
      //   width: "0",
      //   popupWidth: "100px",
      //   data: [
      //     // "个人信息",
      //     // "修改密码",
      //     // "清除缓存",
      //     "切换模块",
      //     // "切换租户",
      //     "退出"
      //   ]
      // },
      menuItems: [],
      timeZoneList: [
        { text: this.$t('中国时区'), value: 'GMT+8' },
        { text: this.$t('越南时区'), value: 'GMT+7' },
        { text: this.$t('墨西哥时区'), value: 'GMT-6' }
      ],

      appMenuItems: [
        {
          text: '',
          iconCss: 'e-ddl-icon e-icons',
          items: [
            {
              text: this.$t('安卓下载')
            },
            {
              text: this.$t('iOS下载')
            },
            {
              text: this.$t('H5访问')
            }
          ]
        }
      ],
      header: '',
      currentIndex: 0,
      showQRCode: false
    }
  },
  watch: {
    userInfo: {
      handler(v) {
        this.init(v)
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    ...mapGetters(['sidebar', 'lang']),
    visitedViews() {
      return store.state.tagsView.visitedViews
    },
    theme() {
      return {
        width: '60px',
        data: [
          {
            name: this.$t('默认'),
            code: 'default'
          },
          {
            name: this.$t('黑色'),
            code: 'dark'
          }
        ]
      }
    },
    isDarkTheme() {
      return this.tagsClass === 'nav-content-dark'
    },
    setBuVal: {
      get() {
        return this.$store.state.app.buVal
      },
      set(val) {
        console.log(val)
      }
    },
    buList() {
      return this.$store.state.app.buList
    },
    currentTimeZone() {
      const localTimeZone = localStorage.getItem('timeZone') || 'GMT+8'
      return localTimeZone
    },
    imgUrl() {
      let urlList = [
        '/api/purchase/app/public/pc/getQrcode',
        '/api/purchase/app/public/pc/getAppleQrcode',
        '/api/purchase/app/public/pc/getH5Qrcode'
      ]
      let url = urlList[this.currentIndex]
      let origin = window.location.origin
      if (origin.includes('/index')) {
        let prefix = origin.split('/index')[0]
        url = prefix + url
      }
      return url
    }
  },
  mounted() {
    console.log('treeNodeTemplate:', this.user)
    if (this.$route.query.menuClose) {
      this.tgSideBar()
    }
    if (this.openI18n) {
      this.getLanguage()
    }
    this.themeChange({ value: this.themeValue })
  },
  methods: {
    appSelect(args) {
      const { item } = args
      if (item.text === this.$t('安卓下载')) {
        this.header = this.$t('安卓下载')
        this.currentIndex = 0
        this.handleShow()
      }
      if (item.text === this.$t('iOS下载')) {
        this.header = this.$t('iOS下载')
        this.currentIndex = 1
        this.handleShow()
      }
      if (item.text === this.$t('H5访问')) {
        this.header = this.$t('H5访问')
        this.currentIndex = 2
        this.handleShow()
      }
    },
    handleShow() {
      this.$refs.dialog.ejsRef.show()
      this.showQRCode = true
    },
    handleClose() {
      this.showQRCode = false
    },
    countDown() {
      return parseInt((new Date('2023-06-30') - new Date()) / 1000 / 60 / 60 / 24) - 1
    },
    returnIndex() {
      let _appInfo = this.userInfo.applicationList.find((e) => e.applicationCode === 'srm')
      if (_appInfo?.url) {
        window.location.href = _appInfo.url
      }
    },
    closeTags() {
      this.$emit('closeTags')
    },
    getLanguage() {
      this.API.get('/i18n/common/lang/list').then((r) => {
        this.language.data = r.data
      })
    },
    /**
     * 初始化 获取userInfo数据
     */
    init(userInfo) {
      this.user = Object.assign({}, this.user, userInfo)
      const a = []
      let b = []
      this.user.applicationList?.forEach((e) => {
        a.push({
          text: e.applicationName,
          url: e.url,
          code: e.applicationCode
        })
      })
      if (userInfo.myMenuList) {
        b = [
          {
            text: '',
            iconCss: 'e-ddl-icon e-icons',
            items: [
              ...userInfo.myMenuList,
              {
                text: this.$t('切换应用'),
                items: a
              },
              {
                text: this.$t('退出')
              }
            ]
          }
        ]
      } else {
        b = [
          {
            text: '',
            iconCss: 'e-ddl-icon e-icons',
            items: [
              {
                text: this.$t('切换应用'),
                items: a
              },
              {
                text: this.$t('退出')
              }
            ]
          }
        ]
      }
      // this.menuItems = b
      this.$set(this, 'menuItems', b)
    },

    changeIsOpen(res) {
      console.log('openM', res)
      this.openM = res
    },

    changeApp(value) {
      const url = value.itemData.value
      window.location.href = url
    },

    goRight(a) {
      if (this.showMore) {
        const distance = this.$refs.tagsP.scrollLeft
        const scrollNum = this.$refs.tagsP.scrollLeft + a
        let step = (distance - scrollNum) / 50
        if (step < 0) step = -step
        this.moveSlow(distance, scrollNum, step, true)
      }
    },
    toRight() {
      // 往右滚动
      const distance = this.$refs.tagsP.scrollLeft
      const scrollNum = distance + 200
      let step = (distance - scrollNum) / 50
      if (step < 0) step = -step
      this.moveSlow(distance, scrollNum, step, true)
    },
    toLeft() {
      // 左滚动
      const distance = this.$refs.tagsP.scrollLeft
      let scrollNum = distance - 200
      if (scrollNum < 0) {
        scrollNum = 0
      }
      let step = (distance - scrollNum) / 50
      if (step < 0) step = -step
      this.moveSlow(distance, scrollNum, step, false)
    },
    moveSlow(distance, total, step, LOR) {
      // 正向滚动 和 反向滚动
      if (LOR) {
        // 每隔1毫秒移动一小段距离，直到移动至目标至为止，反之亦然
        if (distance < total) {
          distance += step
          this.$refs.tagsP.scrollLeft = distance
          setTimeout(() => {
            this.moveSlow(distance, total, step, true)
          }, 1)
        } else {
          this.$refs.tagsP.scrollLeft = total
        }
      } else if (!LOR) {
        if (distance > total) {
          distance -= step
          this.$refs.tagsP.scrollLeft = distance
          setTimeout(() => {
            this.moveSlow(distance, total, step, false)
          }, 1)
        } else {
          this.$refs.tagsP.scrollLeft = total
        }
      }
    },
    getShowTag(a) {
      this.showMore = a
    },
    tgSideBar() {
      this.$emit('toggleSideBar')
      this.openM = !this.openM
    },
    toggleScreen() {
      screenfull.toggle()
    },
    // toggleTransitionStatus() {
    //   this.transition.display = !this.transition.display
    // },
    langChange(event) {
      if (event.isInteracted) {
        const lang = event.value
        this.$emit('setLocal', lang)
        localStorage.setItem('internationlization', lang)
        this.closeTags()
        location.reload()
      }
    },
    clickMessage() {
      this.$emit('clickMessage')
    },
    clickInform() {
      this.$emit('clickInform')
    },
    themeChange(event) {
      const lang = event.value
      const themeInstance = new Theme()
      localStorage.setItem('mt-layout-theme', lang)
      themeInstance.change(event.value)
      this.$emit('changeTheme', lang)
      // this.changeTagsClass('nav-content-dark')
    },
    async buChange(e) {
      const buVal = e.value || 'TV'
      // 后端没有提供单独保存用户选择的接口，而是通过user-menu接口来保存用户选择，所有切换bu后立马调用一次改接口
      const res = await this.$api.source.queryUserMenu({
        code: { appCode: utils.getAppCode() },
        buVal
      })
      if (res.code === 200) {
        this.$router.push('/')
        location.reload()
      }
    },
    timeZoneChange(e) {
      const timeZoneVal = e.value || 'GMT+8'
      localStorage.setItem('timeZone', timeZoneVal)
      location.reload()
    },
    changeTagsClass(e) {
      this.tagsClass = e
    },
    handleSelectMenu(args) {
      const { item } = args
      this.$emit('handleSelectMenu', item)
      // if (item.text === "SRM") {
      //   let url = getOrigin("srm");
      //   window.location.href = url;
      // }
      // if (item.text === "个人中心") {
      //   let url = getOrigin("uc");
      //   window.location.href = url;
      // }
      // if (item.text === "平台站") {
      //   let url = getOrigin("platform");
      //   window.location.href = url;
      // }
      if (item.text === '退出' || item.text === 'sign out') {
        logOut()
        this.$store.dispatch('app/removeRoutes')
        sensorLogout()
      }
    },
    langWidth(val) {
      let res
      switch (val) {
        case 'vn':
          res = 90
          break
        default:
          res = 80
          break
      }
      return res
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .e-menu-wrapper ul.e-menu .e-menu-item .e-menu-icon {
  color: var(--mcl-top-select-color) !important;
}
</style>
<style lang="scss">
.mt-body .e-sidebar.e-left {
  border-right: none;
  box-shadow: 1px 0 0 0 var(--mcl-top-nav-e-left-color);
}

.mt-top-nav {
  .e-input-group {
    border: none !important;
    .e-input {
      text-align: center;
    }
    &::before {
      background: none;
    }
  }

  .e-menu-container,
  .e-menu-wrapper {
    background: transparent !important;

    .e-selected,
    .e-selected,
    .e-focused {
      background-color: var(--mcl-top-nav-e-focused-bg-color) !important;
      color: var(--mcl-top-select-color);
    }

    .e-menu-item.e-menu-caret-icon,
    .e-menu-item.e-menu-caret-icon {
      padding-right: 0 !important;
      padding-left: 0 !important;
    }

    .e-menu-item .e-menu-icon {
      margin-right: 0 !important;
    }

    .e-menu-ite {
      padding: 0 !important;
    }

    .e-ddl-icon {
      line-height: 40px;
    }
    .e-ddl-icon::before {
      display: inline-block;
      width: 12px;
      height: 12px;
      font-size: 12px;
      content: '\e83d';
    }
    .e-caret {
      display: none;
    }
  }
}
</style>
<style lang="scss" scoped>
::v-deep .e-ddl.e-input-group .e-dropdownlist {
  font-size: 14px !important;
  color: var(--mcl-top-select-color) !important;
}
::v-deep .e-input-group.e-control-wrapper .e-input + .e-input-group-icon {
  color: var(--mcl-top-select-color);
}
::v-deep .e-input-group.e-control-wrapper:not(.e-disabled) .e-input-group-icon:hover {
  color: var(--mcl-top-select-color);
}
::v-deep .e-ddl.e-input-group.e-control-wrapper .e-ddl-icon::before {
  font-size: 12px;
  content: '\e969';
}
::v-deep .option-item .e-input-group.e-control-wrapper {
  margin-bottom: 0px;
}
.mt-top-nav {
  height: 50px;
  // background: linear-gradient(rgba(245, 245, 245, 1), rgba(245, 245, 245, 1)),
  //   linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 1));
  background: var(--mcl-top-nav-background-color);
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  min-width: 1200px;
  display: flex;
  z-index: 2;
  .tabs-arrow {
    width: 20px;
    height: 30px;
    line-height: 30px;
    position: relative;
    cursor: pointer;
    border-radius: 4px 0 0 0;
    // box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.1);
    box-shadow: 0 0 2px 0 var(--mcl-top-nav-tabs-arrow-box-shadow);
    &:after {
      content: '';
      position: absolute;
      width: 0;
      height: 0;
      border-top: 4px solid transparent;
      border-bottom: 4px solid transparent;
      border-left: 0;
      border-right: 4px solid var(--mcl-top-nav-tabs-arrow-border-right);
      left: 8px;
      top: 12px;
    }
    &.arrow-right {
      border-radius: 0 4px 0 0;
      &:after {
        border-top: 4px solid transparent;
        border-bottom: 4px solid transparent;
        border-left: 4px solid var(--mcl-top-nav-tabs-arrow-border-right);
        border-right: 0;
      }
    }
  }
  .nav-logo {
    transition: 0.5s;
    position: relative;
    width: 220px;
    height: 50px;
    line-height: 50px;
    padding: 0 12px;
    align-items: center;
    display: flex;
    flex-shrink: 0;
    cursor: pointer;
    .logo-img {
      height: 30px;
    }
    .project-name {
      color: #fff;
      font-size: 20px;
      font-weight: 600;
      margin-left: 12px;
    }
    .main-menu {
      color: var(--mcl-tags-icon-close-color);
      cursor: pointer;
      position: absolute;
      right: 20px;
    }
  }
  .nav-title {
    position: absolute;
    left: 110px;
    top: -2px;
    font-size: 16px;
    letter-spacing: 1px;
    font-weight: 500;
    color: #fff;
    margin-left: 100px;
    margin-bottom: 0;
  }
  @keyframes shine {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }
  @keyframes letterspacing {
    0% {
      letter-spacing: -12px;
      filter: blur(6px);
    }
    25% {
      filter: blur(3px);
    }
    50% {
      filter: blur(1px);
    }
    100% {
      letter-spacing: 1px;
      filter: blur(0px);
    }
  }
  .nav-slogan {
    position: absolute;
    left: 380px;
    top: -2px;
    color: transparent;
    font-size: 18px;
    font-weight: 500;
    margin-left: 100px;
    background-color: #605546;
    background-clip: text;
    &::after {
      content: attr(data-text);
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-image: linear-gradient(
        120deg,
        transparent 0%,
        transparent 6rem,
        white 11rem,
        transparent 11.15rem,
        transparent 15rem,
        rgba(255, 255, 255, 0.3) 20rem,
        transparent 25rem,
        transparent 27rem,
        rgba(255, 255, 255, 0.6) 32rem,
        white 33rem,
        rgba(255, 255, 255, 0.3) 33.15rem,
        transparent 38rem,
        transparent 40rem,
        rgba(255, 255, 255, 0.3) 45rem,
        transparent 50rem,
        transparent 100%
      );
      background-clip: text;
      background-size: 150% 100%;
      background-repeat: no-repeat;
      animation: shine 3s infinite linear;
    }
  }
  .nav-tip-container {
    height: 50px;
    width: 600px;
    position: absolute;
    left: 550px;
    top: 0px;
    display: flex;
    color: #fff;
    filter: contrast(20);
    background: transparent;
    overflow: hidden;
    .nav-tip {
      // font-family: Righteous;
      font-weight: 600;
      color: white;
      font-size: 16px;
      text-transform: uppercase;
      line-height: 1;
      animation: letterspacing 3s infinite alternate ease-in-out;
      display: block;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate3d(-50%, -50%, 0);
      letter-spacing: -2.2px;
      span {
        color: red;
        font-size: 18px;
        font-weight: 800;
      }
    }
  }
  .nav-content-dark {
    display: flex;
    flex: 1;
    background: transparent;
    align-items: flex-end;
    justify-content: space-between;
    .tabs-arrow {
      display: none;
    }
    .closeTag {
      position: relative;
      bottom: -35px;
      background: #f0f0f0;
      text-align: center;
      line-height: 35px;
      color: #958e8e;
      cursor: pointer;
      font-size: 12px;
      padding: 0 40px 0 20px;
      // width: 132px;
      height: 35px;
      text-shadow: 0 0 10px #0ebeff, 0 0 20px #0ebeff, 0 0 50px #0ebeff, 0 0 100px #0ebeff,
        0 0 200px #0ebeff;
    }
    .option-content {
      overflow-y: hidden;
      overflow-x: auto;
      height: 35px;
      flex: 1;
      width: 100px;
      position: relative;
      bottom: -35px;
      margin-left: 1px;
      background: #f0f0f0;
      &::-webkit-scrollbar {
        display: none;
        height: 8px;
      }
      &:hover {
        &::-webkit-scrollbar {
          display: block;
          height: 8px;
        }
      }
      .tags-view-container {
        .tags-view-item {
          margin: 0 2px 0 0;
          display: inline-block;
          position: relative;
          cursor: pointer;
          height: 30px;
          line-height: 30px;
          left: 7px;
          border-radius: 4px 4px 0 0;
          color: var(--mcl-tags-color);
          background: var(--mcl-tags-bg-color);
          padding: 0 32px 0 16px;
          font-size: 14px;
          border-bottom: none;
          z-index: 1;
        }
      }
    }
    .toggle-open {
      left: -162px;
    }
    .public-content {
      margin-right: 20px;
      height: 50px;
      align-items: center;
      display: flex;
      position: absolute;
      right: 0;
    }
    .option-item {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 0 15px;
      // display: inline-block;
      text-align: center;
      position: relative;
      .option-name {
        max-width: 80px;
        height: 18px;
        line-height: 18px;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 500;
        color: var(--mcl-top-select-color);
        white-space: nowrap; //不换行
        overflow: hidden; //超出隐藏
        text-overflow: ellipsis; //变成...
      }
      .option-role {
        max-width: 80px;
        height: 16px;
        line-height: 16px;
        font-size: 12px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(152, 170, 195, 1);
        white-space: nowrap; //不换行
        overflow: hidden; //超出隐藏
        text-overflow: ellipsis; //变成...
      }
      .option-svg {
        font-size: 18px;
        color: var(--mcl-top-nav-option-item-color);
        margin-right: 5px;
      }
      .option-dropList {
        border-bottom: none;
        &::before {
          background: none;
        }
      }
      span {
        color: var(--mcl-top-nav-option-item-color);
      }

      &.split {
        position: relative;
        &::after {
          content: '';
          position: absolute;
          right: 0;
          border-right: 1px solid var(--mcl-top-nav-after);
          height: 50px;
          width: 1px;
        }
      }

      .option-badge {
        background: red;
        width: 14px;
        height: 14px;
        display: flex;

        align-items: center;
        justify-content: center;
        border-radius: 50%;
        position: absolute;
        left: 33px;
        top: -3px;
        span {
          color: var(--mcl-white-color);
          font-size: 10px;
          transform: scale(0.7);
          font-weight: 700;
          &.dot {
            margin-top: -4px;
          }
        }
      }

      .ejs-dropdown {
        position: absolute;
        left: 0;
        top: 25px;
      }

      .avatar {
        width: 30px;
        height: 30px;
        border-radius: 50%;
      }
    }

    .option-item:last-child {
      padding-right: 0 !important;
    }
  }
  .nav-content {
    display: flex;
    flex: 1;
    background: transparent;
    align-items: flex-end;
    justify-content: space-between;
    // padding: 0 10px 0 6px;
    .option-content {
      overflow-y: hidden;
      overflow-x: auto;
      width: calc(100vw - 750px);
      min-width: 350px;
      height: calc(100% - 9px);
      flex: 1;
      &::-webkit-scrollbar {
        display: none;
        height: 10px;
      }
      &:hover {
        &::-webkit-scrollbar {
          display: block;
          height: 10px;
        }
      }
    }
    .public-content {
      // width: 350px;
      margin-right: 20px;
      height: 50px;
      align-items: center;
      display: flex;
    }
    .option-item {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 0 15px;
      // display: inline-block;
      text-align: center;
      position: relative;
      .option-name {
        max-width: 80px;
        height: 18px;
        line-height: 18px;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 500;
        color: var(--mcl-top-select-color);
        white-space: nowrap; //不换行
        overflow: hidden; //超出隐藏
        text-overflow: ellipsis; //变成...
      }
      .option-role {
        max-width: 80px;
        height: 16px;
        line-height: 16px;
        font-size: 12px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(152, 170, 195, 1);
        white-space: nowrap; //不换行
        overflow: hidden; //超出隐藏
        text-overflow: ellipsis; //变成...
      }
      .option-svg {
        font-size: 18px;
        color: var(--mcl-top-nav-option-item-color);
        margin-right: 5px;
      }
      .option-dropList {
        border-bottom: none;
        &::before {
          background: none;
        }
      }
      span {
        color: var(--mcl-top-nav-option-item-color);
      }

      &.split {
        position: relative;
        &::after {
          content: '';
          position: absolute;
          right: 0;
          border-right: 1px solid var(--mcl-top-nav-after);
          height: 50px;
          width: 1px;
        }
      }

      .option-badge {
        background: red;
        width: 14px;
        height: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        position: absolute;
        left: 33px;
        top: -3px;
        span {
          color: var(--mcl-white-color);
          font-size: 10px;
          transform: scale(0.7);
          font-weight: 700;
          &.dot {
            margin-top: -4px;
          }
        }
      }

      .ejs-dropdown {
        position: absolute;
        left: 0;
        top: 25px;
      }

      .avatar {
        width: 30px;
        height: 30px;
        border-radius: 50%;
      }
    }

    .option-item:last-child {
      padding-right: 0 !important;
    }
  }
  .full-screen {
    color: var(--mcl-tags-icon-close-color);
    &:hover {
      color: var(--mcl-base-active-color);
    }
  }
  .nav-logo-close {
    width: 56px;
    // transition: width 0.5s ease, visibility 0.5s;
    // box-shadow: 1px 0 0 0 rgba(237, 239, 243, 1);
  }
  .nav-logo-close2 {
    width: 0px;
    transition: width 0.5s ease, visibility 0.5s;
  }
  .nav-logo-hide {
    display: none;
  }
}
</style>
