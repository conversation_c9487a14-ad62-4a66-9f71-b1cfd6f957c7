<template>
  <div class="mtech-common-layout">
    <Layout>
      <Sider
        :style="{
          position: 'fixed',
          height: '100vh',
          left: 0,
          overflow: 'auto',
          width: '100%',
          paddingTop: '50px',
          zIndex: 11
        }"
        v-if="leftNav"
        ref="sider"
        hide-trigger
        collapsible
        :width="220"
        :collapsed-width="56"
        v-model="isCollapsed"
        @toggleSideBar="toggleSideBar"
      >
        <div class="left-nav-box">
          <mt-side-menu
            ref="sidebarInstance"
            :data-source="dataSource"
            :fields="fields"
            target=".left-nav-box"
            :allow-switch="allowSwitch"
            @openMenu="openMenu"
            @setSideClickMenu="setSideClickMenu"
            @menuSelect="menuSelect"
            @getIsOpen="getIsOpen"
            @roleSwitch="onMenuSwitch"
          ></mt-side-menu>
        </div>
      </Sider>
      <Layout>
        <Header
          :style="{
            position: 'fixed',
            width: '100%',
            height: '50px',
            zIndex: 12
          }"
        >
          <TopNav
            :user-info="userInfo"
            :remind="remind"
            :open-i18n="openI18n"
            :open-theme="openTheme"
            :left-nav="leftNav"
            :tags-class="tagsClass"
            @setLocal="setLocal"
            @changeTheme="changeTheme"
            @clickInform="clickInform"
            @clickMessage="clickMessage"
            @toggleSideBar="toggleSideBar"
            @handleSelectMenu="handleSelectMenu"
            @closeTags="closeTags"
            ref="topNav"
          >
            <TagsView
              ref="tagsV"
              :project-name="projectName"
              :route-info="routeInfo"
              :tags-class="tagsClass"
              :click-menu="clickMenu"
              @getShowTag="getShowTag"
              @goRight="goRight"
              @chooseMenu="chooseMenu"
              @closeMenu="closeMenu"
              @switchToggle="switchToggle"
              @collapseAll="collapseAll"
            ></TagsView>
          </TopNav>
        </Header>
        <Content
          :style="{ paddingTop: tagsClass == 'nav-content' ? '' : '35px', margin: contentMargin }"
          class="e-mian-content"
        >
          <slot></slot>
        </Content>
      </Layout>
    </Layout>
  </div>
</template>

<script>
import '../mtechUI.js'
import '../assets/base.css'

import Layout from './layout/layout.vue'
import Header from './layout/header.vue'
import Sider from './layout/sider.vue'
import Content from './layout/content.vue'
import TopNav from './components/topNav.vue'
// umd 格式文件 用require
import MtSideMenu from '@/layout/side-menu/src/index.vue'
import TagsView from './components/tagsView.vue'
import store from '../store/index'

export default {
  components: {
    Layout,
    Header,
    Sider,
    Content,
    TopNav,
    MtSideMenu,
    TagsView
  },
  props: {
    userInfo: {
      type: Object,
      default: () => {}
    },
    remind: {
      type: Object,
      default: () => {}
    },
    openI18n: {
      type: Boolean,
      default: false
    },
    openTheme: {
      type: Boolean,
      default: false
    },
    leftNav: {
      type: Boolean,
      default: true
    },
    routeInfo: {
      type: Object,
      default: () => {}
    },
    definedMenuSelect: {
      type: Boolean,
      default: false
    },
    projectName: {
      type: String,
      default: ''
    },
    tagsClass: {
      type: String,
      default: 'nav-content'
    }
  },
  data() {
    return {
      isCollapsed: false,
      allowSwitch: false,
      clickMenu: false,
      contentMargin: '50px 0 0 220px',
      dataSource: [],
      routesSupplier: [],
      updataCount: 0,
      fields: {
        id: 'id',
        text: 'name',
        icon: 'icon',
        path: 'path',
        children: 'children',
        parentId: 'parentId'
      }
    }
  },
  watch: {
    isCollapsed: {
      handler(val) {
        this.leftNav
          ? (this.contentMargin = val ? '50px 0 0 56px' : '50px 0 0 220px')
          : (this.contentMargin = '50px 0 0 0')
      },
      immediate: true
    },
    routeInfo(obj) {
      if (this.leftNav) {
        this.leftNavInit(obj)
      }
    },
    dataSource: {
      handler() {
        if (this.$refs.sidebarInstance) {
          this.$refs.sidebarInstance.chooseId()
        }
      },
      immediate: true
    }
  },
  updated() {
    if (this.updataCount < 10) {
      this.chooseMenu(true)
    }
  },
  mounted() {
    this.leftNavInit(this.routeInfo)
  },
  created() {
    let tagsView = []
    store.commit('tagsView/addProjectName', this.projectName)
    try {
      tagsView = sessionStorage.getItem('tagsView')
      if (tagsView) {
        store.dispatch('tagsView/updataVisitedView', tagsView)
      }
    } catch (error) {
      console.log(error)
    }
  },
  methods: {
    closeTags() {
      this?.$refs?.tagsV.closeAllTags()
    },
    toggleSideBar() {
      this?.$refs?.sidebarInstance.toggle()
      this?.$refs?.sider.toggleCollapse()
    },
    switchToggle(e) {
      this?.$refs?.sidebarInstance.switchToggle(e)
    },
    collapseAll() {
      this?.$refs?.sidebarInstance.$refs?.treeviewInstance?.ejsRef.collapseAll()
    },
    leftNavInit(routeInfo) {
      const { routesIn, routesSupplier, allowSwitch } = routeInfo
      if (sessionStorage.getItem('currentSideMenu')) {
        const ele = JSON.parse(sessionStorage.getItem('currentSideMenu'))
        if (ele.toggle === 'buyer') {
          this.dataSource = routesIn
        } else {
          this.dataSource = routesSupplier
        }
      } else {
        this.dataSource = routesIn
      }
      this.routesSupplier = routesSupplier
      this.allowSwitch = allowSwitch
      console.log('leftNavInit-routesIn', routesIn)
      if (routesIn === null || routesIn.length === 0) {
        this.$refs.sidebarInstance.isSwitchIn = false
        this.allowSwitch = false
        this.onMenuSwitch('supplier')
      } else if (routesSupplier === null || routesSupplier.length === 0) {
        this.allowSwitch = false
      } else {
        this.$refs.sidebarInstance.isSwitchIn = true
        this.onMenuSwitch('buyer')
        this.allowSwitch = true
      }
    },
    setSideClickMenu(e) {
      console.log('setSideClickMenu', e)
      this.clickMenu = e
    },
    getShowTag(e) {
      this?.$refs.topNav.getShowTag(e)
    },
    goRight(e) {
      this?.$refs.topNav.goRight(e)
    },
    openMenu() {
      this?.$refs?.sider.toggleCollapse()
    },
    handleSelectMenu(e) {
      this.$emit('handleSelectMenu', e)
    },
    chooseMenu(bool) {
      console.log('chooseMenu', bool)
      if (this.$refs.sidebarInstance && bool) {
        this.$refs.sidebarInstance.chooseId()
        this.updataCount++
      }
    },
    closeMenu() {
      if (this.$refs.sidebarInstance) {
        this.$refs.sidebarInstance.closeId()
      }
    },
    setLocal(lang = 'zh-CN') {
      this.$emit('emitEvent', 'setLocal', lang)
    },
    changeTheme(theme = 'default') {
      this.$emit('changeTheme', theme)
    },
    clickMessage() {
      console.log('clickMessage')
      this.$emit('clickMessage')
    },
    clickInform() {
      console.log('clickInform')
      this.$emit('clickInform')
    },
    // side menu 添加菜单
    menuSelect(e) {
      // console.log("tagsView/addView",e);
      // 自定义点击跳转事件
      this.updataCount = 10
      if (this.definedMenuSelect) {
        store.dispatch('tagsView/addView', e)
        this.$emit('emitEvent', 'menuSelect', e)
        return
      }
      if (this.projectName === 'main') {
        store.dispatch('tagsView/addView', e)
      }
    },

    getIsOpen(e) {
      this?.$refs?.topNav.changeIsOpen(e)
    },

    onMenuSwitch(type) {
      const { routesIn, routesSupplier } = this.routeInfo
      this.dataSource = routesIn
      this.routesSupplier = routesSupplier
      console.log('onMenuSwitch', type)
      if (type === 'supplier') {
        this.dataSource = routesSupplier
      } else if (type === 'buyer') {
        this.dataSource = routesIn
      }
    }
  }
}
</script>

<style lang="scss">
.mtech-common-layout {
  color: #232b39;
  font-size: 14px;
  width: 100%;
  height: 100%;

  .e-mian-content {
    overflow: auto;
    position: relative;
    transition: width 0.4s;
  }

  .left-nav-box {
    position: relative;
    height: 100%;
    overflow-y: auto;
    .e-sidebar.e-left.e-open {
      background: transparent;
      box-shadow: 1px 0 0 0 var(--mcl-base-left-nav-box-box-shadow) !important;
    }
    .e-sidebar.e-left.e-close.e-dock {
      background: var(--mcl-top-nav-background-color);
      box-shadow: 1px 0 0 0 var(--mcl-base-left-nav-box-box-shadow);
    }

    .tree-control-wrapper .mt-tree-view .e-treeview > .e-list-parent.e-ul {
      overflow-y: hidden !important;
    }
    .e-treeview .e-list-item .e-ul {
      padding: 0 0 0 16px;
    }
  }
}
</style>
<style lang="scss" scoped>
.left-nav-box {
  ::v-deep .tree-node .node-content {
    color: var(--mcl-top-select-color);
    display: block;
    width: 100%;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  ::v-deep .e-input-group.e-control-wrapper:not(.e-float-icon-left):not(.e-float-input)::before,
  ::v-deep .e-input-group.e-control-wrapper:not(.e-float-icon-left):not(.e-float-input)::after {
    background: var(--mcl-active-color);
  }
  ::v-deep .e-input-group.e-control-wrapper input.e-input {
    border-color: #e8e8e8;
  }
  ::v-deep .mt-icon-MT_Search:before {
    font-size: 20px;
    color: #9a9a9a;
  }
  ::v-deep .e-active .tree-node .node-content {
    color: var(--mcl-left-select-color);
  }
  ::v-deep .e-treeview .e-list-item.e-active > .e-fullrow {
    background: var(--mcl-left-nav-e-focused-bg-color);
    opacity: var(--mcl-left-nav-e-focused-bg-opacity);
    border: none;
  }
  ::v-deep .e-treeview .e-list-item.e-hover > .e-fullrow {
    background: var(--mcl-left-nav-e-focused-bg-color);
    opacity: var(--mcl-left-nav-e-focused-bg-opacity);
    border: none;
  }
  ::v-deep .option-wrap .search-container .icon-search {
    position: absolute;
    &::before {
      font-size: 14px;
      color: var(--mcl-serch-icon);
    }
  }
  ::v-deep .e-control .e-input-group.e-control-wrapper input.e-input {
    color: var(--mcl-top-select-color);
    padding-left: 25px;
  }
  ::v-deep .option-wrap .switch-container .switch {
    color: var(--mcl-no-active-color);
  }
  ::v-deep .option-wrap .switch-container .switch span {
    color: var(--mcl-no-active-color);
  }
  ::v-deep .option-wrap .switch-container .switch-active {
    color: var(--mcl-active-color);
  }
  ::v-deep .option-wrap .switch-container .switch-active span {
    color: var(--mcl-active-color);
  }
}
</style>
