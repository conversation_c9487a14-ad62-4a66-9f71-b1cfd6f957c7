<!--消息-->
<template>
  <div class="main">
    <div class="eachMessage" v-for="item in messages" @click="clickSingleData(item)" :key="item.id">
      <i :class="[!item.read ? 'newMessageI' : 'readMessageI']"></i>
      <p :class="[!item.read ? 'newMessageP' : 'readMessageP']">
        {{ item.content }}
      </p>
      <span :class="[!item.read ? 'newMessageSpan' : 'readMessageSpan']">{{
        item.createTime
      }}</span>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    allMessages: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      messages: [] //所有消息
    }
  },
  components: {},
  mounted() {
    this.messages = this.allMessages
  },
  watch: {
    allMessages() {
      this.watchAllMessages()
    }
  },
  computed: {},
  methods: {
    //点击消息调用已读
    clickSingleData(item) {
      console.log(item)
      if (item.read === false) {
        let parameter = {
          id: item.id
        }
        this.$api.source.inmailSetRead(parameter).then((res) => {
          console.log(res, this.$t('消息已读'))
          this.$emit('clickMessageRead')
          this.$toast({
            content: this.$t('消息已读'),
            type: 'success'
          })
        })
      }
    },
    //监听变化以后
    watchAllMessages() {
      this.messages = this.allMessages
    }
  }
}
</script>

<style lang="scss" scoped>
.main {
  margin: 0;
  width: 100%;
  .eachMessage {
    padding: 16px;
    box-sizing: border-box;
    position: relative;
    // 新消息红点
    .newMessageI {
      display: block;
      width: 4px;
      height: 4px;
      background-color: #ed5633;
      position: absolute;
      left: 5px;
      top: 20px;
    }
    //已读消息红点
    .readMessageI {
      display: none;
    }
    //新消息文本
    .newMessageP {
      text-align: left;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(41, 41, 41, 1);
      margin-bottom: 16px;
    }
    //已读消息文本
    .readMessageP {
      text-align: left;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
      margin-bottom: 16px;
    }
    //新消息时间显示
    .newMessageSpan {
      width: 119px;
      height: 12px;
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(99, 134, 193, 1);
      margin-left: 16px;
    }
    //已读消息时间显示
    .readMessageSpan {
      width: 119px;
      height: 12px;
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
      margin-left: 16px;
    }
  }
  .eachMessage:hover {
    cursor: pointer;
  }
}
</style>
