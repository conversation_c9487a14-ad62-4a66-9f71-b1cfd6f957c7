FROM tone.tcl.com/devops/docker/release/nginx:1.20.2

# FROM registry.getech.cn/poros/frontbase:1.0.7

RUN rm /etc/nginx/conf.d/default.conf
# RUN mkdir /usr/share/nginx/html/h5

COPY dist/ /usr/share/nginx/html/
# ADD dist /usr/local/openresty/nginx/html
# COPY h5_dist/ /usr/share/nginx/html/h5

COPY docker/default.conf /etc/nginx/conf.d/

RUN chmod -R 777 /usr/share/nginx/html
CMD ["nginx", "-g", "daemon off;"]

EXPOSE 80
