server {
    listen       80;
    server_name  localhost;
    # 永久重定向到新域名
    
    access_log  /var/log/nginx/access.log  main;

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        try_files $uri /index.html;
        add_header Cache-Control no-cache;
        add_header Pragma no-cache;
        add_header Expires 0;
    }

    gzip on;  #开启压缩
    gzip_min_length 1k;   #设置压缩最小单位，小于不压缩
    #gzip_disable "msie6";

    # gzip_vary on;
    # gzip_proxied any;
    gzip_comp_level 4;  #压缩比
    gzip_buffers 4 16k;  
    gzip_http_version 1.1;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
}
