{"name": "mtech-srm-main-web", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "upgrade:mtech-ui": "npx update-by-scope -t latest @mtech npm install", "theme": "node build/theme.js", "serveAll": "npm-run-all theme serve --continue-on-error", "buildAll": "npm-run-all theme build --continue-on-error", "build": "vue-cli-service build", "build:report": "vue-cli-service build --report", "prebuild": "npm run theme", "dictionary": "node build/dict.js", "lint": "vue-cli-service lint", "prepare": "husky install", "lint-staged": "lint-staged"}, "dependencies": {"@digis/component-props-state": "^1.2.6", "@digis/dictionary-editor": "^1.1.18", "@digis/dictionary-plugin": "^1.1.10", "@digis/internationalization": "^1.1.22", "@mtech-common/http": "^1.0.5", "@mtech-common/utils": "^1.0.0", "@mtech-micro-frontend/vue-main": "^1.0.0", "@mtech-sso/single-sign-on": "^1.2.5-tcl.7", "@mtech-ui/base": "^1.10.10", "@mtech-ui/button": "^1.10.10", "@mtech-ui/dialog": "^1.11.4", "@mtech-ui/drop-down-tree": "^1.10.10", "@mtech-ui/form": "^1.10.10", "@mtech-ui/form-item": "^1.10.10", "@mtech-ui/global-toast": "^1.11.14", "@mtech-ui/select": "^1.10.10", "@mtech-ui/side-bar": "^1.10.10", "@mtech-ui/tree-view": "^1.10.10", "@mtech/common-loading": "^0.2.1", "@mtech/common-permission": "^1.2.5", "@mtech/mtech-common-layout": "^1.6.0-tcl.7", "@mtech/mtech-common-uploader": "^1.3.34", "@mtech/side-menu": "^1.7.2", "@syncfusion/ej2-vue-navigations": "19.2.62", "@syncfusion/ej2-vue-notifications": "19.2.60", "core-js": "^3.21.0", "lodash": "^4.17.0", "vue": "^2.6.11", "vue-class-component": "^8.0.0-rc.1", "vue-router": "^3.5.1"}, "devDependencies": {"@babel/parser": "^7.17.0", "@commitlint/cli": "^17.0.1", "@commitlint/config-conventional": "^17.0.0", "@digis-vue-cli-preset/vue-cli-plugin-performance": "^1.1.5", "@mtech-micro-frontend/vue-cli-plugin-micro": "1.0.0", "@mtech/eslint-config-vue": "0.0.4", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "~4.5.0", "axios": "^0.21.1", "babel-eslint": "^10.1.0", "babel-plugin-dynamic-import-node": "^2.3.3", "eslint": "^6.7.2", "html-webpack-plugin": "^5.3.1", "husky": "^8.0.3", "js-cookie": "^2.2.1", "lint-staged": "^13.1.4", "mini-css-extract-plugin": "^0.9.0", "node-sass": "^4.14.1", "prerender-spa-plugin": "^3.4.0", "sass-loader": "^8.0.0", "screenfull": "^5.1.0", "svg-sprite-loader": "^6.0.4", "vue-i18n": "^8.27.0", "vue-template-compiler": "^2.6.11", "vuex": "^3.6.2", "webpack": "^4.0.0", "webpack-bundle-analyzer": "^4.5.0", "webpack-fix-style-only-entries": "^0.6.1", "webpack-glob-entry": "^2.1.1"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "mtMicro": {"type": "master"}, "lint-staged": {"src/**/*.{js,jsx,vue,ts,tsx}": ["eslint --fix"]}}