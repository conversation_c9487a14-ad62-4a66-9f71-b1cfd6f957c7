'use strict'
const path = require('path')
const proxyConfig = require('./src/config/proxy.config')
// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin
const DictionaryPlugin = require('@digis/dictionary-plugin')
// var PrerenderSPAPlugin = require('prerender-spa-plugin')
// const Renderer = PrerenderSPAPlugin.PuppeteerRenderer

function resolve(dir) {
  return path.join(__dirname, dir)
}

const name = 'mtech-srm-main-web'

const port = process.env.port || process.env.npm_config_port || 9012 // dev port

// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
  publicPath: './',
  outputDir: 'dist',
  assetsDir: 'static',
  lintOnSave: false,
  productionSourceMap: false,

  devServer: {
    port: port,
    open: true,
    overlay: {
      warnings: false,
      errors: true
    },
    clientLogLevel: 'info',
    proxy: proxyConfig
  },

  configureWebpack: {
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    name: name,
    resolve: {
      alias: {
        '@': resolve('src'),
        vue$: 'vue/dist/vue.esm.js'
      }
    },
    plugins: [
      new DictionaryPlugin() // new DictionaryPlugin({NODE_ENV:'production'}) 执行的插件的环境 默认为'production'
      // new PrerenderSPAPlugin({
      //   staticDir: path.join(__dirname, 'dist'),
      //   routes: ['/', '/main/dashboard'],
      //   renderer: new Renderer({
      //     inject: {
      //       foo: 'bar'
      //     },
      //     maxConcurrentRoutes: 5,
      //     headless: false,
      //     // renderAfterDocumentEvent: 'render-event'
      //     renderAfterTime: 500
      //   })
      // })
    ]
    // optimization: {
    //   usedExports: true
    // }
  },

  chainWebpack() {
    // if (process.env.NODE_ENV === "development") {
    //   config.plugin("BundleAnalyzerPlugin").use(new BundleAnalyzerPlugin());
    // }
  },

  pluginOptions: {
    'style-resources-loader': {
      preProcessor: 'scss',
      patterns: [path.resolve(__dirname, 'src/themes/_mtechUI.scss')]
    }
  }
}
