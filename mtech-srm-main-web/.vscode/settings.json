{
  // 编辑器字体大小
  "editor.fontSize": 15,
  // 这些文件将不会显示在工作空间中
  // 在使用搜索功能时，将这些文件夹/文件排除在外
  "search.exclude": {
    "**/node_modules": true,
    "**/bower_components": true,
    "**/target": true,
    "**/logs": true
  },
  "files.associations": {
    "*.vue": "vue",
    "*.wpy": "vue",
    "*.wxml": "html",
    "*.wxss": "css",
    "*.cjson": "jsonc",
    "*.wxs": "javascript",
    ".prettierrc": "jsonc"
  },
  "files.exclude": {
    "**/.git": true,
    "**/.svn": true,
    "**/.hg": true,
    "**/CVS": true,
    "**/.DS_Store": true,
    "**/*.js": {
      "when": "$(basename).ts" //ts编译后生成的js文件将不会显示在工作空中
    },
    "**/node_modules": false
  },
  // vscode文件图标主题
  "workbench.iconTheme": "vscode-icons",
  // 是否显示空格和tab符
  "editor.renderControlCharacters": true,
  "editor.renderWhitespace": "none",
  //===============================
  // "[vue]": {
  //   "editor.defaultFormatter": "octref.vetur",
  // },
  // "[vue]": {
  //     "editor.defaultFormatter": "esbenp.prettier-vscode"
  // },
  // vscode默认启用了根据文件类型自动设置tabsize的选项
  "editor.detectIndentation": false,
  // 重新设定tabsize
  "editor.tabSize": 2,
  // #每次保存的时候自动格式化
  "editor.formatOnSave": true,
  // eslint配置项，保存时自动修复错误
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  // 添加 vue 支持
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "html"
  ],
  //  #让函数(名)和后面的括号之间加个空格
  "javascript.format.insertSpaceBeforeFunctionParenthesis": true,
  //打开文件不覆盖
  "workbench.editor.enablePreview": false,
  // 设置不同文件使用的格式化配置
  "[html]": {
    // "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  "vetur.format.defaultFormatter.css": "prettier",
  "vetur.format.defaultFormatter.postcss": "prettier",
  "vetur.format.defaultFormatter.scss": "prettier",
  "vetur.format.defaultFormatter.less": "prettier",
  "vetur.format.defaultFormatter.stylus": "stylus-supremacy",
  "vetur.format.defaultFormatter.ts": "prettier",
  // vetur 缩进是4
  "vetur.format.options.tabSize": 4,
  "vetur.format.options.useTabs": false,
  "vetur.validation.script": false,
  "javascript.updateImportsOnFileMove.enabled": "never",
  "vetur.format.defaultFormatterOptions": {
    "prettier": {
      // "trailingComma": "es5", // 多行时，尽可能打印尾随的逗号
      "tabWidth": 2, // 会忽略vetur的tabSize配置
      "useTabs": false, // 是否利用tab替代空格
      "semi": false, // 句尾是否加;
      "singleQuote": true, // 使用单引号而不是双引号
      "arrowParens": "avoid", // allow paren-less arrow functions 箭头函数的参数使用圆括号
      // 末尾不添加逗号
      "trailingComma": false,
      "wrapAttributes": false,
      "sortAttributes": false
    },
    "js-beautify-html": {
      //属性强制折行对齐
      // "wrap_attributes": "aligned-multiple",
      "wrap_attributes": "auto",
      "end_with_newline": 0,
      "semi": false,
      "singleQuote": true,
      // 末尾不添加逗号
      "trailingComma": false
    }
  },
  "eslint.options": {
    "overrideConfigFile": "./`.eslintrc`.js"
  },
  "[typescript]": {
    "editor.defaultFormatter": "vscode.typescript-language-features"
  },
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  // #vue组件中html代码格式化样式
  "prettier.useEditorConfig": false,
  "prettier.vueIndentScriptAndStyle": false,
  "prettier.semi": false, // 句尾添加分号
  "prettier.singleQuote": true,
  "prettier.bracketSpacing": true,
  "prettier.tabWidth": 2,
  "prettier.useTabs": false,
  "prettier.htmlWhitespaceSensitivity": "strict",
  "prettier.trailingComma": "none",
  // "prettier.endOfLine": "crlf",
  "prettier.jsxSingleQuote": true,
  "prettier.jsxBracketSameLine": true,
  "prettier.printWidth": 100,
  "i18n-ally.localesPaths": [
    "src/locale",
    "src/locale/lang"
  ] // 每行字符
}
//#prettier参数说明 https://blog.csdn.net/weixin_44808483/article/details/118113753