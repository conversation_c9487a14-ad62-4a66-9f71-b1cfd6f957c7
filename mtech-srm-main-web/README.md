# mtech-srm-main-web

## Project setup

```
npm install --loglevel verbose --unsafe-perm=true --allow-root --registry https://srm-nexus.sct.tcl.com/repository/jd-npm-pub/
```

### Compiles and hot-reloads for development

```
npm run dev
```

### Compiles and minifies for production

```
npm run build-
```

### Lints and fixes files test

```
npm run lint
```

### Customize configuration 1

See [ Configuration Reference ](https://cli.vuejs.org/config/).
