var sd={modules:{}},_=sd._={};!function(){var J={function:!0,object:!0},B=J[typeof window]&&window||this;var e=B.JSON,t=B.JSON3,r=!1,s=function e(t,d){t=t||B.Object(),d=d||B.Object();var c=t.Number||B.Number,l=t.String||B.String,r=t.Object||B.Object,g=t.Date||B.Date,s=t.SyntaxError||B.SyntaxError,h=t.TypeError||B.TypeError,p=t.Math||B.Math,t=t.JSON||B.JSON;"object"==typeof t&&t&&(d.stringify=t.stringify,d.parse=t.parse);var m,r=r.prototype,v=r.toString,o=r.hasOwnProperty;function y(e,t){try{e()}catch(e){t&&t()}}var u,b,w,S,_,k,a,f,j,i,n,O,P,N,D,C,A,$,x,E,T,I,U,R=new g(-0xc782b5b800cec);function H(e){return null!=H[e]?H[e]:("bug-string-char-index"==e?t=!1:"json"==e?t=H("json-stringify")&&H("date-serialization")&&H("json-parse"):"date-serialization"==e?(t=H("json-stringify")&&R)&&(a=d.stringify,y(function(){t='"-271821-04-20T00:00:00.000Z"'==a(new g(-864e13))&&'"+275760-09-13T00:00:00.000Z"'==a(new g(864e13))&&'"-000001-01-01T00:00:00.000Z"'==a(new g(-621987552e5))&&'"1969-12-31T23:59:59.999Z"'==a(new g(-1))})):(s='{"a":[1,true,false,null,"\\u0000\\b\\n\\f\\r\\t"]}',"json-stringify"==e&&((i="function"==typeof(a=d.stringify))&&((r=function(){return 1}).toJSON=r,y(function(){i="0"===a(0)&&"0"===a(new c)&&'""'==a(new l)&&a(v)===m&&a(m)===m&&a()===m&&"1"===a(r)&&"[1]"==a([r])&&"[null]"==a([m])&&"null"==a(null)&&"[null,null,null]"==a([m,v,null])&&a({a:[r,!0,!1,null,"\0\b\n\f\r\t"]})==s&&"1"===a(null,r)&&"[\n 1,\n 2\n]"==a([1,2],null,1)},function(){i=!1})),t=i),"json-parse"==e&&("function"==typeof(n=d.parse)&&y(function(){0!==n("0")||n(!1)||(r=n(s),(o=5==r.a.length&&1===r.a[0])&&(y(function(){o=!n('"\t"')}),o&&y(function(){o=1!==n("01")}),o&&y(function(){o=1!==n("1.")})))},function(){o=!1}),t=o)),H[e]=!!t);var t,r,s,a,i,n,o}function L(e){return j(this)}return y(function(){R=-109252==R.getUTCFullYear()&&0==R.getUTCMonth()&&1==R.getUTCDate()&&10==R.getUTCHours()&&37==R.getUTCMinutes()&&6==R.getUTCSeconds()&&708==R.getUTCMilliseconds()}),H["bug-string-char-index"]=H["date-serialization"]=H.json=H["json-stringify"]=H["json-parse"]=null,H("json")||(u="[object Function]",b="[object Number]",w="[object String]",S="[object Array]",_=H("bug-string-char-index"),k=function(e,t){var r,n,s,a=0;for(s in(r=function(){this.valueOf=0}).prototype.valueOf=0,n=new r)o.call(n,s)&&a++;return n=null,(k=a?function(e,t){var r,s,a=v.call(e)==u;for(r in e)a&&"prototype"==r||!o.call(e,r)||(s="constructor"===r)||t(r);(s||o.call(e,r="constructor"))&&t(r)}:(n=["valueOf","toString","toLocaleString","propertyIsEnumerable","isPrototypeOf","hasOwnProperty","constructor"],function(e,t){var r,s,a=v.call(e)==u,i=!a&&"function"!=typeof e.constructor&&J[typeof e.hasOwnProperty]&&e.hasOwnProperty||o;for(r in e)a&&"prototype"==r||!i.call(e,r)||t(r);for(s=n.length;r=n[--s];)i.call(e,r)&&t(r)}))(e,t)},H("json-stringify")||H("date-serialization")||(a={92:"\\\\",34:'\\"',8:"\\b",12:"\\f",10:"\\n",13:"\\r",9:"\\t"},f=function(e,t){return("000000"+(t||0)).slice(-e)},j=function(e){var t,r,s,a,i,n,o,d,c,l,u,_;return _=R?function(e){t=e.getUTCFullYear(),r=e.getUTCMonth(),s=e.getUTCDate(),i=e.getUTCHours(),n=e.getUTCMinutes(),o=e.getUTCSeconds(),d=e.getUTCMilliseconds()}:(c=p.floor,l=[0,31,59,90,120,151,181,212,243,273,304,334],u=function(e,t){return l[t]+365*(e-1970)+c((e-1969+(t=+(1<t)))/4)-c((e-1901+t)/100)+c((e-1601+t)/400)},function(e){for(s=c(e/864e5),t=c(s/365.2425)+1970-1;u(t+1,0)<=s;t++);for(r=c((s-u(t,0))/30.42);u(t,r+1)<=s;r++);s=1+s-u(t,r),i=c((a=(e%864e5+864e5)%864e5)/36e5)%24,n=c(a/6e4)%60,o=c(a/1e3)%60,d=a%1e3}),(j=function(e){return-1/0<e&&e<1/0?(_(e),e=(0<t&&t<1e4?f(4,t):(t<0?"-":"+")+f(6,t<0?-t:t))+"-"+f(2,r+1)+"-"+f(2,s)+"T"+f(2,i)+":"+f(2,n)+":"+f(2,o)+"."+f(3,d)+"Z",t=r=s=i=n=o=d=null):e=null,e})(e)},H("json-stringify")&&!H("date-serialization")?(i=d.stringify,d.stringify=function(e,t,r){var s=g.prototype.toJSON;return g.prototype.toJSON=L,r=i(e,t,r),g.prototype.toJSON=s,r}):(n=function(e){var t=e.charCodeAt(0);return(e=a[t])||"\\u00"+f(2,t.toString(16))},O=/[\x00-\x1f\x22\x5c]/g,P=function(e){return O.lastIndex=0,'"'+(O.test(e)?e.replace(O,n):e)+'"'},N=function(e,t,r,s,a,i,n){var o,d,c,l,u,_,p,f;if(y(function(){o=t[e]}),"object"==typeof o&&o&&(o.getUTCFullYear&&"[object Date]"==v.call(o)&&o.toJSON===g.prototype.toJSON?o=j(o):"function"==typeof o.toJSON&&(o=o.toJSON(e))),(o=r?r.call(t,e,o):o)==m)return o===m?o:"null";switch((d="object"==(p=typeof o)?v.call(o):d)||p){case"boolean":case"[object Boolean]":return""+o;case"number":case b:return-1/0<o&&o<1/0?""+o:"null";case"string":case w:return P(""+o)}if("object"==typeof o){for(_=n.length;_--;)if(n[_]===o)throw h();if(n.push(o),c=[],p=i,i+=a,d==S){for(u=0,_=o.length;u<_;u++)l=N(u,o,r,s,a,i,n),c.push(l===m?"null":l);f=c.length?a?"[\n"+i+c.join(",\n"+i)+"\n"+p+"]":"["+c.join(",")+"]":"[]"}else k(s||o,function(e){var t=N(e,o,r,s,a,i,n);t!==m&&c.push(P(e)+":"+(a?" ":"")+t)}),f=c.length?a?"{\n"+i+c.join(",\n"+i)+"\n"+p+"}":"{"+c.join(",")+"}":"{}";return n.pop(),f}},d.stringify=function(e,t,r){var s,a,i;if(J[typeof t]&&t)if((i=v.call(t))==u)a=t;else if(i==S)for(var n,o={},d=0,c=t.length;d<c;)n=t[d++],"[object String]"!=(i=v.call(n))&&"[object Number]"!=i||(o[n]=1);if(r)if((i=v.call(r))==b){if(0<(r-=r%1))for(10<r&&(r=10),s="";s.length<r;)s+=" "}else i==w&&(s=10<r.length?r.slice(0,10):r);return N("",((n={})[""]=e,n),a,o,s,"",[])})),H("json-parse")||(D=l.fromCharCode,C={92:"\\",34:'"',47:"/",98:"\b",116:"\t",110:"\n",102:"\f",114:"\r"},x=function(){throw A=$=null,s()},E=function(){for(var e,t,r,s,a,i=$,n=i.length;A<n;)switch(a=i.charCodeAt(A)){case 9:case 10:case 13:case 32:A++;break;case 123:case 125:case 91:case 93:case 58:case 44:return e=_?i[0|A]||"":i[A],A++,e;case 34:for(e="@",A++;A<n;)if((a=i.charCodeAt(A))<32)x();else if(92==a)switch(a=i.charCodeAt(++A)){case 92:case 34:case 47:case 98:case 116:case 110:case 102:case 114:e+=C[a],A++;break;case 117:for(t=++A,r=A+4;A<r;A++)((a=i.charCodeAt(A))<48||57<a)&&(a<97||102<a)&&(a<65||70<a)&&x();e+=D("0x"+i.slice(t,A));break;default:x()}else{if(34==a)break;for(a=i.charCodeAt(A),t=A;32<=a&&92!=a&&34!=a;)a=i.charCodeAt(++A);e+=i.slice(t,A)}if(34==i.charCodeAt(A))return A++,e;x();default:if(t=A,45==a&&(s=!0,a=i.charCodeAt(++A)),48<=a&&a<=57){for(48!=a||(a=i.charCodeAt(A+1))<48||57<a||x(),s=!1;A<n&&48<=(a=i.charCodeAt(A))&&a<=57;A++);if(46==i.charCodeAt(A)){for(r=++A;r<n&&48<=(a=i.charCodeAt(r))&&a<=57;r++);r==A&&x(),A=r}if(101==(a=i.charCodeAt(A))||69==a){for(43!=(a=i.charCodeAt(++A))&&45!=a||A++,r=A;r<n&&48<=(a=i.charCodeAt(r))&&a<=57;r++);r==A&&x(),A=r}return+i.slice(t,A)}s&&x();var o=i.slice(A,A+4);if("true"==o)return A+=4,!0;if("fals"==o&&101==i.charCodeAt(A+4))return A+=5,!1;if("null"==o)return A+=4,null;x()}return"$"},T=function(e){var t,r;if("$"==e&&x(),"string"==typeof e){if("@"==(_?e[0]||"":e[0]))return e.slice(1);if("["==e){for(t=[];"]"!=(e=E());)r?","==e&&"]"!=(e=E())||x():r=!0,","==e&&x(),t.push(T(e));return t}if("{"==e){for(t={};"}"!=(e=E());)r?","==e&&"}"!=(e=E())||x():r=!0,","!=e&&"string"==typeof e&&"@"==(_?e[0]||"":e[0])&&":"==E()||x(),t[e.slice(1)]=T(E());return t}x()}return e},I=function(e,t,r){(r=U(e,t,r))===m?delete e[t]:e[t]=r},U=function(e,t,r){var s,a=e[t];if("object"==typeof a&&a)if(v.call(a)==S)for(s=a.length;s--;)I(v,k,a);else k(a,function(e){I(a,e,r)});return r.call(e,t,a)},d.parse=function(e,t){var r;return A=0,$=""+e,r=T(E()),"$"!=E()&&x(),A=$=null,t&&v.call(t)==u?U(((e={})[""]=r,e),"",t):r})),d.runInContext=e,d}(B,B.JSON3={noConflict:function(){return r||(r=!0,B.JSON=e,B.JSON3=t,e=t=null),s}});B.JSON={parse:s.parse,stringify:s.stringify}}.call(void 0),function(t){if(t.atob)try{t.atob(" ")}catch(e){t.atob=(r.original=s=t.atob,r)}else{var o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",n=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/;t.btoa=function(e){for(var t,r,s,a="",i=0,n=(e=""+e).length%3;i<e.length;)(255<(t=e.charCodeAt(i++))||255<(r=e.charCodeAt(i++))||255<(s=e.charCodeAt(i++)))&&sd.log("Failed to execute 'btoa' on 'Window': The string to be encoded contains characters outside of the Latin1 range."),a+=(o[0|(t=t<<16|r<<8|s)>>18&63]||"")+(o[0|t>>12&63]||"")+(o[0|t>>6&63]||"")+(o[0|63&t]||"");return n?a.slice(0,n-3)+"===".substring(n):a},t.atob=function(e){n.test(e=(""+e).replace(/[\t\n\f\r ]+/g,""))||sd.log("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded."),e+="==".slice(2-(3&e.length));for(var t,r,s,a="",i=0;i<e.length;)t=o.indexOf(e[0|i++]||"")<<18|o.indexOf(e[0|i++]||"")<<12|(r=o.indexOf(e[0|i++]||""))<<6|(s=o.indexOf(e[0|i++]||"")),a+=64==r?String.fromCharCode(t>>16&255):64==s?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return a}}function r(e){return s((""+e).replace(/[\t\n\f\r ]+/g,""))}var s}(window),String.prototype.replaceAll||(String.prototype.replaceAll=function(e,t){return"[object regexp]"==Object.prototype.toString.call(e).toLowerCase()?this.replace(e,t):this.replace(RegExp(e,"g"),t)}),function(){var t,r,s,e=Array.prototype,a=Object.prototype,i=e.slice,n=a.toString,o=a.hasOwnProperty,d=e.forEach,e=Array.isArray,c={},l=_.each=function(e,t,r){if(null==e)return!1;if(d&&e.forEach===d)e.forEach(t,r);else if(_.isArray(e)&&e.length===+e.length){for(var s=0,a=e.length;s<a;s++)if(s in e&&t.call(r,e[s],s,e)===c)return!1}else for(var i in e)if(o.call(e,i)&&t.call(r,e[i],i,e)===c)return!1};function u(){for(var e=+new Date,t=0;e==+new Date;)t++;return e.toString(16)+t.toString(16)}_.map=function(e,s){var a=[];return null==e?a:Array.prototype.map&&e.map===Array.prototype.map?e.map(s):(l(e,function(e,t,r){a.push(s(e,t,r))}),a)},_.extend=function(r){return l(i.call(arguments,1),function(e){for(var t in e)o.call(e,t)&&void 0!==e[t]&&(r[t]=e[t])}),r},_.extend2Lev=function(r){return l(i.call(arguments,1),function(e){for(var t in e)void 0!==e[t]&&(_.isObject(e[t])&&_.isObject(r[t])?_.extend(r[t],e[t]):r[t]=e[t])}),r},_.coverExtend=function(r){return l(i.call(arguments,1),function(e){for(var t in e)void 0!==e[t]&&void 0===r[t]&&(r[t]=e[t])}),r},_.isArray=e||function(e){return"[object Array]"===n.call(e)},_.isFunction=function(e){if(!e)return!1;try{return/^\s*\bfunction\b/.test(e)}catch(e){return!1}},_.isArguments=function(e){return!(!e||!o.call(e,"callee"))},_.toArray=function(e){return e?e.toArray?e.toArray():_.isArray(e)||_.isArguments(e)?i.call(e):_.values(e):[]},_.values=function(e){var t=[];return null==e||l(e,function(e){t[t.length]=e}),t},_.indexOf=function(e,t){var r=e.indexOf;if(r)return r.call(e,t);for(var s=0;s<e.length;s++)if(t===e[s])return s;return-1},_.hasAttributes=function(e,t){if("string"==typeof t)return _.hasAttribute(e,t);if(_.isArray(t)){for(var r=!1,s=0;s<t.length;s++)if(_.hasAttribute(e,t[s])){r=!0;break}return r}},_.hasAttribute=function(e,t){return e.hasAttribute?e.hasAttribute(t):!(!e.attributes[t]||!e.attributes[t].specified)},_.filter=function(e,t,r){var s=Object.prototype.hasOwnProperty;if(e.filter)return e.filter(t);for(var a,i=[],n=0;n<e.length;n++)s.call(e,n)&&(t.call(r,a=e[n],n,e)&&i.push(a));return i},_.inherit=function(e,t){return e.prototype=new t,(e.prototype.constructor=e).superclass=t.prototype,e},_.trim=function(e){return e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")},_.isObject=function(e){return null!=e&&"[object Object]"==n.call(e)},_.isEmptyObject=function(e){if(_.isObject(e)){for(var t in e)if(o.call(e,t))return!1;return!0}return!1},_.isUndefined=function(e){return void 0===e},_.isString=function(e){return"[object String]"==n.call(e)},_.isDate=function(e){return"[object Date]"==n.call(e)},_.isBoolean=function(e){return"[object Boolean]"==n.call(e)},_.isNumber=function(e){return"[object Number]"==n.call(e)&&/[\d\.]+/.test(""+e)},_.isElement=function(e){return!(!e||1!==e.nodeType)},_.isJSONString=function(e){try{JSON.parse(e)}catch(e){return!1}return!0},_.safeJSONParse=function(e){var t=null;try{t=JSON.parse(e)}catch(e){return!1}return t},_.decodeURIComponent=function(t){var r=t;try{r=decodeURIComponent(t)}catch(e){r=t}return r},_.decodeURI=function(t){var r=t;try{r=decodeURI(t)}catch(e){r=t}return r},_.isDecodeURI=function(e,t){return e?_.decodeURI(t):t},_.encodeDates=function(r){return _.each(r,function(e,t){_.isDate(e)?r[t]=_.formatDate(e):_.isObject(e)&&(r[t]=_.encodeDates(e))}),r},_.mediaQueriesSupported=function(){return void 0!==window.matchMedia||void 0!==window.msMatchMedia},_.getScreenOrientation=function(){var e=screen.msOrientation||screen.mozOrientation||(screen.orientation||{}).type,t="未取到值";return e?t=~e.indexOf("landscape")?"landscape":"portrait":_.mediaQueriesSupported()&&((e=window.matchMedia||window.msMatchMedia)("(orientation: landscape)").matches?t="landscape":e("(orientation: portrait)").matches&&(t="portrait")),t},_.now=Date.now||function(){return(new Date).getTime()},_.throttle=function(r,s,a){var i,n,o,d=null,c=0;a=a||{};function l(){c=!1===a.leading?0:_.now(),d=null,o=r.apply(i,n),d||(i=n=null)}return function(){var e=_.now(),t=s-(e-(c=!c&&!1===a.leading?e:c));return i=this,n=arguments,t<=0||s<t?(d&&(clearTimeout(d),d=null),c=e,o=r.apply(i,n),d||(i=n=null)):d||!1===a.trailing||(d=setTimeout(l,t)),o}},_.hashCode=function(e){if("string"!=typeof e)return 0;var t=0;if(0==e.length)return t;for(var r=0;r<e.length;r++)t=(t<<5)-t+e.charCodeAt(r),t&=t;return t},_.formatDate=function(e){function t(e){return e<10?"0"+e:e}return e.getFullYear()+"-"+t(1+e.getMonth())+"-"+t(e.getDate())+" "+t(e.getHours())+":"+t(e.getMinutes())+":"+t(e.getSeconds())+"."+t(e.getMilliseconds())},_.getRandomBasic=(t=(new Date).getTime(),function(e){return Math.ceil((t=(9301*t+49297)%233280)/233280*e)}),_.getRandom=function(){if("function"==typeof Uint32Array){var e="";if("undefined"!=typeof crypto?e=crypto:"undefined"!=typeof msCrypto&&(e=msCrypto),_.isObject(e)&&e.getRandomValues){var t=new Uint32Array(1);return e.getRandomValues(t)[0]/4294967296}}return _.getRandomBasic(1e19)/1e19},_.searchObjDate=function(r){_.isObject(r)&&_.each(r,function(e,t){_.isObject(e)?_.searchObjDate(r[t]):_.isDate(e)&&(r[t]=_.formatDate(e))})},_.searchZZAppStyle=function(e){void 0!==e.properties.$project&&(e.project=e.properties.$project,delete e.properties.$project),void 0!==e.properties.$token&&(e.token=e.properties.$token,delete e.properties.$token)},_.formatJsonString=function(t){try{return JSON.stringify(t,null,"  ")}catch(e){return JSON.stringify(t)}},_.formatString=function(e,t){return _.isNumber(t)&&t<e.length?(sd.log("字符串长度超过限制，已经做截取--"+e),e.slice(0,t)):e},_.searchObjString=function(r){var s=["$element_selector","$element_path"];_.isObject(r)&&_.each(r,function(e,t){_.isObject(e)?_.searchObjString(r[t]):_.isString(e)&&(r[t]=_.formatString(e,~_.indexOf(s,t)?1024:sd.para.max_string_length))})},_.parseSuperProperties=function(e){var r=e.properties,s=JSON.parse(JSON.stringify(e));_.isObject(r)&&(_.each(r,function(e,t){if(_.isFunction(e))try{r[t]=e(s),_.isFunction(r[t])&&(sd.log("您的属性- "+t+" 格式不满足要求，我们已经将其删除"),delete r[t])}catch(e){delete r[t],sd.log("您的属性- "+t+" 抛出了异常，我们已经将其删除")}}),_.strip_sa_properties(r))},_.filterReservedProperties=function(r){_.isObject(r)&&_.each(["distinct_id","user_id","id","date","datetime","event","events","first_id","original_id","device_id","properties","second_id","time","users"],function(e,t){e in r&&(t<3?(delete r[e],sd.log("您的属性- "+e+"是保留字段，我们已经将其删除")):sd.log("您的属性- "+e+"是保留字段，请避免其作为属性名"))})},_.searchConfigData=function(e){if("object"==typeof e&&e.$option){var t=e.$option;return delete e.$option,t}return{}},_.unique=function(e){for(var t,r=[],s={},a=0;a<e.length;a++)(t=e[a])in s||(s[t]=!0,r.push(t));return r},_.strip_sa_properties=function(e){return _.isObject(e)&&_.each(e,function(t,r){var s;_.isArray(t)&&(s=[],_.each(t,function(e){_.isString(e)?s.push(e):sd.log("您的数据-",r,t,"的数组里的值必须是字符串,已经将其删除")}),e[r]=s),_.isString(t)||_.isNumber(t)||_.isDate(t)||_.isBoolean(t)||_.isArray(t)||_.isFunction(t)||"$option"===r||(sd.log("您的数据-",r,t,"-格式不满足要求，我们已经将其删除"),delete e[r])}),e},_.strip_empty_properties=function(e){var r={};return _.each(e,function(e,t){null!=e&&(r[t]=e)}),r},_.base64Encode=function(e){return btoa(encodeURIComponent(e).replace(/%([0-9A-F]{2})/g,function(e,t){return String.fromCharCode("0x"+t)}))},_.base64Decode=function(e){e=_.map(atob(e).split(""),function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)});return decodeURIComponent(e.join(""))},_.UUID=function(){var e=(e=""+screen.height*screen.width)&&/\d{5,}/.test(e)?e.toString(16):(""+31242*_.getRandom()).replace(".","").slice(0,8),e=u()+"-"+_.getRandom().toString(16).replace(".","")+"-"+function(){var e,t,r=navigator.userAgent,a=[],s=0;function i(e,t){for(var r=0,s=0;s<t.length;s++)r|=a[s]<<8*s;return e^r}for(e=0;e<r.length;e++)t=r.charCodeAt(e),a.unshift(255&t),a.length<4||(s=i(s,a),a=[]);return(s=0<a.length?i(s,a):s).toString(16)}()+"-"+e+"-"+u();return e||(_.getRandom()+(""+_.getRandom())+_.getRandom()).slice(2,15)},_.getQueryParam=function(e,t){t=t.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]"),e=_.decodeURIComponent(e);e=RegExp("[\\?&]"+t+"=([^&#]*)").exec(e);return null===e||e&&"string"!=typeof e[1]&&e[1].length?"":_.decodeURIComponent(e[1])},_.urlParse=function(e){function t(e){this._fields={Username:4,Password:5,Port:7,Protocol:2,Host:6,Path:8,URL:0,QueryString:9,Fragment:10},this._values={},this._regex=null,this._regex=/^((\w+):\/\/)?((\w+):?(\w+)?@)?([^\/\?:]+):?(\d+)?(\/?[^\?#]+)?\??([^#]+)?#?(\w*)/,void 0!==e&&this._parse(e)}return t.prototype.setUrl=function(e){this._parse(e)},t.prototype._initValues=function(){for(var e in this._fields)this._values[e]=""},t.prototype.addQueryString=function(e){if("object"!=typeof e)return!1;var t,r=this._values.QueryString||"";for(t in e)r=RegExp(t+"[^&]+").test(r)?r.replace(RegExp(t+"[^&]+"),t+"="+e[t]):"&"===r.slice(-1)?r+t+"="+e[t]:""===r?t+"="+e[t]:r+"&"+t+"="+e[t];this._values.QueryString=r},t.prototype.getUrl=function(){var e="";return e+=this._values.Origin,e+=this._values.Port?":"+this._values.Port:"",e+=this._values.Path,e+=this._values.QueryString?"?"+this._values.QueryString:"",e+=this._values.Fragment?"#"+this._values.Fragment:""},t.prototype.getUrl=function(){var e="";return e+=this._values.Origin,e+=this._values.Port?":"+this._values.Port:"",e+=this._values.Path,e+=this._values.QueryString?"?"+this._values.QueryString:""},t.prototype._parse=function(e){this._initValues();var t,r=this._regex.exec(e);for(t in r||sd.log("DPURLParser::_parse -> Invalid URL"),this._fields)void 0!==r[this._fields[t]]&&(this._values[t]=r[this._fields[t]]);this._values.Hostname=this._values.Host.replace(/:\d+$/,""),this._values.Origin=this._values.Protocol+"://"+this._values.Hostname},new t(e)},_.addEvent=function(){function o(e){return e&&(e.preventDefault=o.preventDefault,e.stopPropagation=o.stopPropagation,e._getPath=o._getPath),e}o._getPath=function(){var r=this;return this.path||this.composedPath&&this.composedPath()||function(){try{var e=r.target,t=[e];if(null===e||null===e.parentElement)return[];for(;null!==e.parentElement;)t.unshift(e=e.parentElement);return t}catch(e){return[]}}()},o.preventDefault=function(){this.returnValue=!1},o.stopPropagation=function(){this.cancelBubble=!0};!function(e,t,r){var s,a,i,n=!(!_.isObject(sd.para.heatmap)||!sd.para.heatmap.useCapture);_.isObject(sd.para.heatmap)&&void 0===sd.para.heatmap.useCapture&&"click"===t&&(n=!0),e&&e.addEventListener?e.addEventListener(t,function(e){e._getPath=o._getPath,r.call(this,e)},n):e[t="on"+t]=(a=r,i=(s=e)[t],function(e){if(e=e||o(window.event)){e.target=e.srcElement;var t,r=!0;return"function"==typeof i&&(t=i(e)),e=a.call(s,e),r=!1===t||!1===e?!1:r}})}.apply(null,arguments)},_.addHashEvent=function(e){var t="pushState"in window.history?"popstate":"hashchange";_.addEvent(window,t,e)},_.addSinglePageEvent=function(e){var t=location.href,r=window.history.pushState,s=window.history.replaceState;window.history.pushState=function(){r.apply(window.history,arguments),e(t),t=location.href},window.history.replaceState=function(){s.apply(window.history,arguments),e(t),t=location.href},_.addEvent(window,r?"popstate":"hashchange",function(){e(t),t=location.href})},_.cookie={get:function(e){for(var t=e+"=",r=document.cookie.split(";"),s=0;s<r.length;s++){for(var a=r[s];" "==(a[0]||"");)a=a.substring(1,a.length);if(!a.indexOf(t))return _.decodeURIComponent(a.substring(t.length,a.length))}return null},set:function(e,t,r,s){var a="",i="",n="";function o(e){return!!e&&e.replaceAll(/\r\n/g,"")}r=null==r?73e3:r,(s=void 0===s?sd.para.cross_subdomain:s)&&(a=(d="url解析失败"===(d=_.getCurrentDomain(location.href))?"":d)?"; domain="+d:""),0!==r&&((c=new Date).setTime("s"===(""+r).slice(-1)?c.getTime()+1e3*(""+(""+r).slice(0,-1)):c.getTime()+24*r*60*60*1e3),i="; expires="+c.toGMTString()),sd.para.is_secure_cookie&&(n="; secure");var d="",r="",c="";e&&(d=o(e)),t&&(r=o(t)),a&&(c=o(a)),d&&r&&(document.cookie=d+"="+encodeURIComponent(r)+i+"; path=/"+c+n)},encrypt:function(e){return"data:enc;"+_.rot13obfs(e)},decrypt:function(e){return e=e.substring(9),e=_.rot13defs(e)},resolveValue:function(e){return e=_.isString(e)&&!e.indexOf("data:enc;")?_.cookie.decrypt(e):e},remove:function(e,t){t=void 0===t?sd.para.cross_subdomain:t,_.cookie.set(e,"",-1,t)},getCookieName:function(e,t){var r="";if(t=t||location.href,!1===sd.para.cross_subdomain){try{r=_.URL(t).hostname}catch(e){sd.log(e)}r="string"==typeof r&&""!==r?"sajssdk_2015_"+e+"_"+r.replace(/\./g,"_"):"sajssdk_2015_root_"+e}else r="sajssdk_2015_cross_"+e;return r},getNewUser:function(){return null!==this.get("sensorsdata_is_new_user")||null!==this.get(this.getCookieName("new_user"))}},_.getElementContent=function(e,t){var r="",s="";return e.textContent?r=_.trim(e.textContent):e.innerText&&(r=_.trim(e.innerText)),s=(r=r&&r.replace(/[\r\n]/g," ").replace(/[ ]+/g," ").substring(0,255))||"","input"!==t&&"INPUT"!==t||("button"===e.type||"submit"===e.type||sd.para.heatmap&&"function"==typeof sd.para.heatmap.collect_input&&sd.para.heatmap.collect_input(e))&&(s=e.value||""),s},_.getEleInfo=function(e){if(!e.target)return!1;var t=e.target,r=t.tagName.toLowerCase(),e={};return e.$element_type=r,e.$element_name=t.getAttribute("name"),e.$element_id=t.getAttribute("id"),e.$element_class_name="string"==typeof t.className?t.className:null,e.$element_target_url=t.getAttribute("href"),e.$element_content=_.getElementContent(t,r),(e=_.strip_empty_properties(e)).$url=_.isDecodeURI(sd.para.url_is_decode,location.href),e.$url_path=location.pathname,e.$title=document.title,e.$viewport_width=window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth||0,e},_.localStorage={get:function(e){return window.localStorage.getItem(e)},parse:function(e){var t;try{t=JSON.parse(_.localStorage.get(e))||null}catch(e){sd.log(e)}return t},set:function(e,t){window.localStorage.setItem(e,t)},remove:function(e){window.localStorage.removeItem(e)},isSupport:function(){var t=!0;try{var e="__sensorsdatasupport__",r="testIsSupportStorage";_.localStorage.set(e,r),_.localStorage.get(e)!==r&&(t=!1),_.localStorage.remove(e)}catch(e){t=!1}return t}},_.sessionStorage={isSupport:function(){var t=!0,e="__sensorsdatasupport__",r="testIsSupportStorage";try{t=!(!sessionStorage||!sessionStorage.setItem)&&(sessionStorage.setItem(e,r),sessionStorage.removeItem(e,r),!0)}catch(e){t=!1}return t}},_.isSupportCors=function(){return void 0!==window.XMLHttpRequest&&("withCredentials"in new XMLHttpRequest||"undefined"!=typeof XDomainRequest)},_.xhr=function(e){if(e)return void 0!==window.XMLHttpRequest&&"withCredentials"in new XMLHttpRequest?new XMLHttpRequest:"undefined"!=typeof XDomainRequest?new XDomainRequest:null;if(void 0!==window.XMLHttpRequest)return new XMLHttpRequest;if(window.ActiveXObject)try{return new ActiveXObject("Msxml2.XMLHTTP")}catch(e){try{return new ActiveXObject("Microsoft.XMLHTTP")}catch(e){sd.log(e)}}},_.ajax=function(e){function t(e){if(!e)return"";try{return JSON.parse(e)}catch(e){return{}}}e.timeout=e.timeout||2e4,e.credentials=void 0===e.credentials||e.credentials;var r=_.xhr(e.cors);if(!r)return!1;e.type||(e.type=e.data?"POST":"GET"),e=_.extend({success:function(){},error:function(){}},e),sd.debug.protocol.ajax(e.url);var s,a=e.success,i=e.error;e.success=function(e){a(e),s&&(clearTimeout(s),s=null)},e.error=function(e){i(e),s&&(clearTimeout(s),s=null)},s=setTimeout(function(){!function(){try{_.isObject(r)&&r.abort&&r.abort()}catch(e){sd.log(e)}s&&(clearTimeout(s),s=null,e.error&&e.error(),r.onreadystatechange=null,r.onload=null,r.onerror=null)}()},e.timeout),"undefined"!=typeof XDomainRequest&&r instanceof XDomainRequest&&(r.onload=function(){e.success&&e.success(t(r.responseText)),r.onreadystatechange=null,r.onload=null,r.onerror=null},r.onerror=function(){e.error&&e.error(t(r.responseText),r.status),r.onreadystatechange=null,r.onerror=null,r.onload=null}),r.onreadystatechange=function(){try{4==r.readyState&&(200<=r.status&&r.status<300||304==r.status?e.success(t(r.responseText)):e.error(t(r.responseText),r.status),r.onreadystatechange=null,r.onload=null)}catch(e){r.onreadystatechange=null,r.onload=null}},r.open(e.type,e.url,!0);try{e.credentials&&(r.withCredentials=!0),_.isObject(e.header)&&_.each(e.header,function(e,t){r.setRequestHeader&&r.setRequestHeader(t,e)}),e.data&&(e.cors||r.setRequestHeader&&r.setRequestHeader("X-Requested-With","XMLHttpRequest"),"application/json"===e.contentType?r.setRequestHeader&&r.setRequestHeader("Content-type","application/json; charset=UTF-8"):r.setRequestHeader&&r.setRequestHeader("Content-type","application/x-www-form-urlencoded"))}catch(e){sd.log(e)}r.send(e.data||null)},_.loadScript=function(e){e=_.extend({success:function(){},error:function(){},appendCall:function(e){document.getElementsByTagName("head")[0].appendChild(e)}},e);var t=null;"css"===e.type&&((t=document.createElement("link")).rel="stylesheet",t.href=e.url),"js"===e.type&&((t=document.createElement("script")).async="async",t.setAttribute("charset","UTF-8"),t.src=e.url,t.type="text/javascript"),t.onload=t.onreadystatechange=function(){this.readyState&&"loaded"!==this.readyState&&"complete"!==this.readyState||(e.success(),t.onload=t.onreadystatechange=null)},t.onerror=function(){e.error(),t.onerror=null},e.appendCall(t)},_.getHostname=function(e,t){t&&"string"==typeof t||(t="hostname解析异常");var r=null;try{r=_.URL(e).hostname}catch(e){sd.log("getHostname传入的url参数不合法！")}return r||t},_.getQueryParamsFromUrl=function(e){var t={},e=e.split("?")[1]||"";return t=e?_.getURLSearchParams("?"+e):t},_.getURLSearchParams=function(e){function t(e){return decodeURIComponent(e)}for(var r={},s=(e=e||"").substring(1).split("&"),a=0;a<s.length;a++){var i,n=s[a].indexOf("=");-1!=n&&(i=s[a].substring(0,n),n=s[a].substring(1+n),i=t(i),n=t(n),r[i]=n)}return r},_.URL=function(e){var t,r,s={};return"function"==typeof window.URL&&function(){try{return"http://modernizr.com/"===new URL("http://modernizr.com/").href}catch(e){return!1}}()?(s=new URL(e)).searchParams||(s.searchParams=(r=_.getURLSearchParams(s.search),{get:function(e){return r[e]}})):(!1==/^https?:\/\/.+/.test(e)&&sd.log("Invalid URL"),e=_.urlParse(e),s.hash="",s.host=e._values.Host?e._values.Host+(e._values.Port?":"+e._values.Port:""):"",s.href=e._values.URL,s.password=e._values.Password,s.pathname=e._values.Path,s.port=e._values.Port,s.search=e._values.QueryString?"?"+e._values.QueryString:"",s.username=e._values.Username,s.hostname=e._values.Hostname,s.protocol=e._values.Protocol?e._values.Protocol+":":"",s.origin=e._values.Origin?e._values.Origin+(e._values.Port?":"+e._values.Port:""):"",s.searchParams=(t=_.getURLSearchParams("?"+e._values.QueryString),{get:function(e){return t[e]}})),s},_.getCurrentDomain=function(e){var t=sd.para.current_domain;switch(typeof t){case"function":var r=t();return""!==r&&_.trim(r)&&~r.indexOf(".")?r:"url解析失败";case"string":return""!==t&&_.trim(t)&&~t.indexOf(".")?t:"url解析失败";default:r=_.getCookieTopLevelDomain();return""===e||""===r?"url解析失败":r}},_.getCookieTopLevelDomain=function(e){e=e||location.hostname;var t=e||!1;if(!t)return"";var r=t.split(".");if(_.isArray(r)&&2<=r.length&&!/^(\d+\.)+\d+$/.test(t))for(var s="."+r.splice(r.length-1,1);0<r.length;)if(s="."+r.splice(r.length-1,1)+s,document.cookie="sensorsdata_domain_test=true; path=/; domain="+s,~document.cookie.indexOf("sensorsdata_domain_test=true")){var a=new Date;return a.setTime(a.getTime()-1e3),document.cookie="sensorsdata_domain_test=true; expires="+a.toGMTString()+"; path=/; domain="+s,s}return""},_.isReferralTraffic=function(e){return""===(e=e||document.referrer)||_.getCookieTopLevelDomain(_.getHostname(e))!==_.getCookieTopLevelDomain()},_.ry=function(e){return new _.ry.init(e)},_.ry.init=function(e){this.ele=e},_.ry.init.prototype={addClass:function(e){return~(" "+this.ele.className+" ").indexOf(" "+e+" ")||(this.ele.className=this.ele.className+(""===this.ele.className?"":" ")+e),this},removeClass:function(e){var t=" "+this.ele.className+" ";return~t.indexOf(" "+e+" ")&&(this.ele.className=t.replace(" "+e+" "," ").slice(1,-1)),this},hasClass:function(e){return!!~(" "+this.ele.className+" ").indexOf(" "+e+" ")},attr:function(e,t){return"string"==typeof e&&_.isUndefined(t)?this.ele.getAttribute(e):("string"==typeof e&&this.ele.setAttribute(e,t=""+t),this)},offset:function(){var e=this.ele.getBoundingClientRect();if(e.width||e.height){var t=this.ele.ownerDocument.documentElement;return{top:e.top+window.pageYOffset-t.clientTop,left:e.left+window.pageXOffset-t.clientLeft}}return{top:0,left:0}},getSize:function(){if(!window.getComputedStyle)return{width:this.ele.offsetWidth,height:this.ele.offsetHeight};try{var e=this.ele.getBoundingClientRect();return{width:e.width,height:e.height}}catch(e){return{width:0,height:0}}},getStyle:function(e){return this.ele.currentStyle?this.ele.currentStyle[e]:this.ele.ownerDocument.defaultView.getComputedStyle(this.ele,null).getPropertyValue(e)},wrap:function(e){e=document.createElement(e);return this.ele.parentNode.insertBefore(e,this.ele),e.appendChild(this.ele),_.ry(e)},getCssStyle:function(e){var t=this.ele.style.getPropertyValue(e);if(t)return t;var r=null;if(!(r="function"==typeof window.getMatchedCSSRules?getMatchedCSSRules(this.ele):r)||!_.isArray(r))return null;for(var s=r.length-1;0<=s;s--)if(t=r[s].style.getPropertyValue(e))return t},sibling:function(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e},next:function(){return this.sibling(this.ele,"nextSibling")},prev:function(e){return this.sibling(this.ele,"previousSibling")},siblings:function(e){return this.siblings((this.ele.parentNode||{}).firstChild,this.ele)},children:function(e){return this.siblings(this.ele.firstChild)},parent:function(){var e=(e=this.ele.parentNode)&&11!==e.nodeType?e:null;return _.ry(e)},previousElementSibling:function(){var e=this.ele;if("previousElementSibling"in document.documentElement)return _.ry(e.previousElementSibling);for(;e=e.previousSibling;)if(1===e.nodeType)return _.ry(e);return _.ry(null)},getSameTypeSiblings:function(){for(var e=this.ele,t=e.parentNode,r=e.tagName.toLowerCase(),s=[],a=0;a<t.children.length;a++){var i=t.children[a];1===i.nodeType&&i.tagName.toLowerCase()==r&&s.push(t.children[a])}return s}},_.strToUnicode=function(e){if("string"!=typeof e)return sd.log("转换unicode错误",e),e;for(var t="",r=0;r<e.length;r++)t+="\\"+e.charCodeAt(r).toString(16);return t},_.getReferrer=function(e){return"string"!=typeof(e=e||document.referrer)?"取值异常_referrer异常_"+e:"string"==typeof(e=(e=!e.indexOf("https://www.baidu.com/")?e.split("?")[0]:e).slice(0,sd.para.max_referrer_string_length))?e:""},_.getKeywordFromReferrer=function(e){e=e||document.referrer;var t=sd.para.source_type.keyword;if(document&&"string"==typeof e){if(e.indexOf("http"))return""===e?"未取到值_直接打开":"未取到值_非http的url";var r=_.getReferSearchEngine(e),s=_.getQueryParamsFromUrl(e);if(_.isEmptyObject(s))return"未取到值";var a;for(i in t)if(r===i&&"object"==typeof s)if(_.isArray(a=t[i]))for(var i=0;i<a.length;i++){var n=s[a[i]];if(n)return n}else if(s[a])return s[a];return"未取到值"}return"取值异常_referrer异常_"+e},_.getWxAdIdFromUrl=function(e){var t=_.getQueryParam(e,"gdt_vid"),r=_.getQueryParam(e,"hash_key"),s=_.getQueryParam(e,"callbacks"),e={click_id:"",hash_key:"",callbacks:""};return _.isString(t)&&t.length&&(e.click_id=16==t.length||18==t.length?t:"参数解析不合法",_.isString(r)&&r.length&&(e.hash_key=r),_.isString(s)&&s.length&&(e.callbacks=s)),e},_.getReferSearchEngine=function(e){var t=_.getHostname(e);if(!t||"hostname解析异常"===t)return"";var r,s={baidu:[/^.*\.baidu\.com$/],bing:[/^.*\.bing\.com$/],google:[/^www\.google\.com$/,/^www\.google\.com\.[a-z]{2}$/,/^www\.google\.[a-z]{2}$/],sm:[/^m\.sm\.cn$/],so:[/^.+\.so\.com$/],sogou:[/^.*\.sogou\.com$/],yahoo:[/^.*\.yahoo\.com$/]};for(r in s)for(var a=s[r],i=0,n=a.length;i<n;i++)if(a[i].test(t))return r;return"未知搜索引擎"},_.getSourceFromReferrer=function(){function e(e,t){for(var r=0;r<e.length;r++)if(~t.split("?")[0].indexOf(e[r]))return 1}var t="("+sd.para.source_type.utm.join("|")+")\\=[^&]+",r=sd.para.source_type.search,s=sd.para.source_type.social,a=document.referrer||"",i=_.info.pageProp.url;if(i){t=i.match(RegExp(t));return t&&t[0]?"付费广告流量":e(r,a)?"自然搜索流量":e(s,a)?"社交网站流量":""===a?"直接流量":"引荐流量"}return"获取url异常"},_.info={initPage:function(){var e=_.getReferrer(),t=location.href,r=_.getCurrentDomain(t);r||sd.debug.jssdkDebug("url_domain异常_"+t+"_"+r),this.pageProp={referrer:e,referrer_host:e?_.getHostname(e):"",url:t,url_host:_.getHostname(t,"url_host取值异常"),url_domain:r}},pageProp:{},campaignParams:function(){var t,e=sd.source_channel_standard.split(" "),r={};return _.isArray(sd.para.source_channel)&&0<sd.para.source_channel.length&&(e=e.concat(sd.para.source_channel),e=_.unique(e)),_.each(e,function(e){(t=_.getQueryParam(location.href,e)).length&&(r[e]=t)}),r},campaignParamsStandard:function(s,a){s=s||"",a=a||"";var e=_.info.campaignParams(),i={},n={};return _.each(e,function(e,t,r){~(" "+sd.source_channel_standard+" ").indexOf(" "+t+" ")?i[s+t]=r[t]:n[a+t]=r[t]}),{$utms:i,otherUtms:n}},properties:function(){return{$timezone_offset:(new Date).getTimezoneOffset(),$screen_height:+(""+screen.height)||0,$screen_width:+(""+screen.width)||0,$lib:"js",$lib_version:""+sd.lib_version}},currentProps:{},register:function(e){_.extend(_.info.currentProps,e)}},_.autoExeQueue=function(){return{items:[],enqueue:function(e){this.items.push(e),this.start()},dequeue:function(){return this.items.shift()},getCurrentItem:function(){return this.items[0]},isRun:!1,start:function(){0<this.items.length&&!this.isRun&&(this.isRun=!0,this.getCurrentItem().start())},close:function(){this.dequeue(),this.isRun=!1,this.start()}}},_.trackLink=function(e,s,a){var i=(e=e||{}).event?e.target||e.event.target:e.ele?e.ele:null;return a=a||{},!(!i||"object"!=typeof i)&&(!i.href||/^javascript/.test(i.href)||i.target||i.download||i.onclick?(sd.track(s,a),!1):(e.event&&t(e.event),void(e.ele&&_.addEvent(e.ele,"click",function(e){t(e)}))));function t(e){e.stopPropagation(),e.preventDefault();var t=!1;function r(){t||(t=!0,location.href=i.href)}setTimeout(r,1e3),sd.track(s,a,r)}},_.eventEmitter=function(){this._events=[],this.pendingEvents=[]},_.eventEmitter.prototype={emit:function(t){var r=[].slice.call(arguments,1);_.each(this._events,function(e){e.type===t&&e.callback.apply(e.context,r)})},on:function(e,t,r){"function"==typeof t&&this._events.push({type:e,callback:t,context:r||this})},tempAdd:function(e,t){t&&e&&(this.pendingEvents.push({type:e,data:t}),20<this.pendingEvents.length&&this.pendingEvents.shift())},isReady:function(){var t=this;this.tempAdd=this.emit,0!==this.pendingEvents.length&&(_.each(this.pendingEvents,function(e){t.emit(e.type,e.data)}),this.pendingEvents=[])}},_.rot13obfs=function(e,t){t="number"==typeof t?t:13;for(var r=(e=""+e).split(""),s=0,a=r.length;s<a;s++)r[s].charCodeAt(0)<126&&(r[s]=String.fromCharCode((r[s].charCodeAt(0)+t)%126));return r.join("")},_.rot13defs=function(e){e=""+e;return _.rot13obfs(e,113)},_.urlSafeBase64=(r={"+":"-","/":"_","=":"."},s={"-":"+",_:"/",".":"="},{encode:function(e){return e.replace(/[+/=]/g,function(e){return r[e]})},decode:function(e){return e.replace(/[-_.]/g,function(e){return s[e]})},trim:function(e){return e.replace(/[.=]{1,2}$/,"")},isBase64:function(e){return/^[A-Za-z0-9+/]*[=]{0,2}$/.test(e)},isUrlSafeBase64:function(e){return/^[A-Za-z0-9_-]*[.]{0,2}$/.test(e)}}),_.setCssStyle=function(t){var r=document.createElement("style");r.type="text/css";try{r.appendChild(document.createTextNode(t))}catch(e){r.styleSheet.cssText=t}var e=document.getElementsByTagName("head")[0],t=document.getElementsByTagName("script")[0];e?e.children.length?e.insertBefore(r,e.children[0]):e.appendChild(r):t.parentNode.insertBefore(r,t)},_.isIOS=function(){return!!navigator.userAgent.match(/iPhone|iPad|iPod/i)},_.getIOSVersion=function(){try{var e=navigator.appVersion.match(/OS (\d+)[._](\d+)[._]?(\d+)?/);return e&&e[1]?Number.parseInt(e[1],10):""}catch(e){return""}},_.getUA=function(){var e,t={},r=navigator.userAgent.toLowerCase();return(e=r.match(/opera.([\d.]+)/))?t.opera=+(""+e[1].split(".")[0]):(e=r.match(/msie ([\d.]+)/))?t.ie=+(""+e[1].split(".")[0]):(e=r.match(/edge.([\d.]+)/))?t.edge=+(""+e[1].split(".")[0]):(e=r.match(/firefox\/([\d.]+)/))?t.firefox=+(""+e[1].split(".")[0]):(e=r.match(/chrome\/([\d.]+)/))?t.chrome=+(""+e[1].split(".")[0]):(e=r.match(/version\/([\d.]+).*safari/))&&(t.safari=+(""+e[1].match(/^\d*.\d*/))),t},_.getDomBySelector=function(e){if(!_.isString(e))return null;var a=e.split(">"),e=null;return(e=function e(t){var r,s=a.shift();if(!s)return t;try{r=function(e,t){var r;if("body"==(e=_.trim(e)))return document.getElementsByTagName("body")[0];if(e.indexOf("#")){if(~e.indexOf(":nth-of-type")){var s=e.split(":nth-of-type");if(!s[0]||!s[1])return null;var a=s[0];if(!(s=s[1].match(/\(([0-9]+)\)/))||!s[1])return null;var i=+(""+s[1]);if(!(_.isElement(t)&&t.children&&0<t.children.length))return null;for(var n=t.children,o=0;o<n.length;o++)if(_.isElement(n[o])){var d=n[o].tagName.toLowerCase();if(d===a&&0==--i){r=n[o];break}}if(0<i)return null}}else e=e.slice(1),r=document.getElementById(e);return r||null}(s,t)}catch(e){sd.log(e)}return r&&_.isElement(r)?e(r):null}())&&_.isElement(e)?e:null},_.jsonp=function(t){if(!_.isObject(t)||!_.isString(t.callbackName))return sd.log("JSONP 请求缺少 callbackName"),!1;t.success=_.isFunction(t.success)?t.success:function(){},t.error=_.isFunction(t.error)?t.error:function(){},t.data=t.data||"";var r,s=document.createElement("script"),a=document.getElementsByTagName("head")[0],i=null,n=!1;a.appendChild(s),_.isNumber(t.timeout)&&(i=setTimeout(function(){return!n&&(t.error("timeout"),window[t.callbackName]=function(){sd.log("call jsonp error")},i=null,a.removeChild(s),void(n=!0))},t.timeout)),window[t.callbackName]=function(){clearTimeout(i),i=null,t.success.apply(null,arguments),window[t.callbackName]=function(){sd.log("call jsonp error")},a.removeChild(s)},~t.url.indexOf("?")?t.url+="&callbackName="+t.callbackName:t.url+="?callbackName="+t.callbackName,_.isObject(t.data)&&(r=[],_.each(t.data,function(e,t){r.push(t+"="+e)}),t.data=r.join("&"),t.url+="&"+t.data),s.onerror=function(e){if(n)return!1;window[t.callbackName]=function(){sd.log("call jsonp error")},clearTimeout(i),i=null,a.removeChild(s),t.error(e),n=!0},s.src=t.url},_.listenPageState=function(e){({visibleHandle:_.isFunction(e.visible)?e.visible:function(){},hiddenHandler:_.isFunction(e.hidden)?e.hidden:function(){},visibilityChange:null,hidden:null,isSupport:function(){return void 0!==document[this.hidden]},init:function(){void 0!==document.hidden?(this.hidden="hidden",this.visibilityChange="visibilitychange"):void 0!==document.mozHidden?(this.hidden="mozHidden",this.visibilityChange="mozvisibilitychange"):void 0!==document.msHidden?(this.hidden="msHidden",this.visibilityChange="msvisibilitychange"):void 0!==document.webkitHidden&&(this.hidden="webkitHidden",this.visibilityChange="webkitvisibilitychange"),this.listen()},listen:function(){var e;this.isSupport()?(e=this,document.addEventListener(e.visibilityChange,function(){document[e.hidden]?e.hiddenHandler():e.visibleHandle()},1)):document.addEventListener?(window.addEventListener("focus",this.visibleHandle,1),window.addEventListener("blur",this.hiddenHandler,1)):(document.attachEvent("onfocusin",this.visibleHandle),document.attachEvent("onfocusout",this.hiddenHandler))}}).init()},_.isSupportBeaconSend=function(){var e=_.getUA(),t=!1,r=navigator.userAgent.toLowerCase();return/Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent)?(r=(r.match(/os [\d._]*/gi)+"").replace(/[^0-9|_.]/gi,"").replace(/_/gi,".").split("."),void 0===e.safari&&(e.safari=r[0]),r[0]&&r[0]<13?(41<e.chrome||30<e.firefox||25<e.opera||12<e.safari)&&(t=!0):(41<e.chrome||30<e.firefox||25<e.opera||11.3<e.safari)&&(t=!0)):(38<e.chrome||13<e.edge||30<e.firefox||25<e.opera||11<e.safari)&&(t=!0),t},_.secCheck={isHttpUrl:function(e){if("string"!=typeof e)return!1;return!1!=/^https?:\/\/.+/.test(e)||(sd.log("Invalid URL"),!1)},removeScriptProtocol:function(e){if("string"!=typeof e)return"";for(var t=/^\s*javascript/i;t.test(e);)e=e.replace(t,"");return e}}}(),sd.para_default={preset_properties:{latest_utm:!0,latest_traffic_source_type:!0,latest_search_keyword:!0,latest_referrer:!0,latest_referrer_host:!1,latest_landing_page:!1,latest_wx_ad_click_id:void 0,url:!0,title:!0},encrypt_cookie:!1,img_use_crossorigin:!1,name:"sa",max_referrer_string_length:200,max_string_length:500,cross_subdomain:!0,show_log:!0,is_debug:!1,debug_mode:!1,debug_mode_upload:!1,session_time:0,use_client_time:!1,source_channel:[],send_type:"image",vtrack_ignore:{},auto_init:!0,is_track_single_page:!1,is_single_page:!1,batch_send:!1,source_type:{},callback_timeout:200,datasend_timeout:3e3,queue_timeout:300,is_track_device_id:!1,ignore_oom:!0,app_js_bridge:!1,url_is_decode:!1,single_page_info:{},has_session_id:!0,session_id_expire_time:18e5},sd.addReferrerHost=function(e){var t="取值异常";_.isObject(e.properties)&&(e.properties.$first_referrer&&(e.properties.$first_referrer_host=_.getHostname(e.properties.$first_referrer,t)),"track"!==e.type&&"track_signup"!==e.type||("$referrer"in e.properties&&(e.properties.$referrer_host=""===e.properties.$referrer?"":_.getHostname(e.properties.$referrer,t)),sd.para.preset_properties.latest_referrer&&sd.para.preset_properties.latest_referrer_host&&(e.properties.$latest_referrer_host=""===e.properties.$latest_referrer?"":_.getHostname(e.properties.$latest_referrer,t))))},sd.addPropsHook=function(e){sd.para.preset_properties&&sd.para.preset_properties.url&&("track"===e.type||"track_signup"===e.type)&&void 0===e.properties.$url&&(e.properties.$url=_.isDecodeURI(sd.para.url_is_decode,window.location.href)),sd.para.preset_properties&&sd.para.preset_properties.title&&("track"===e.type||"track_signup"===e.type)&&void 0===e.properties.$title&&(e.properties.$title=document.title)},sd.initPara=function(e){sd.para=e||sd.para||{};var t,r={};if(_.isObject(sd.para.is_track_latest))for(var s in sd.para.is_track_latest)r["latest_"+s]=sd.para.is_track_latest[s];for(t in sd.para.preset_properties=_.extend({},sd.para_default.preset_properties,r,sd.para.preset_properties||{}),sd.para_default)void 0===sd.para[t]&&(sd.para[t]=sd.para_default[t]);"string"==typeof sd.para.server_url&&(sd.para.server_url=_.trim(sd.para.server_url),sd.para.server_url&&("://"===sd.para.server_url.slice(0,3)?sd.para.server_url=location.protocol.slice(0,-1)+sd.para.server_url:"//"===sd.para.server_url.slice(0,2)?sd.para.server_url=location.protocol+sd.para.server_url:"http"!==sd.para.server_url.slice(0,4)&&(sd.para.server_url=""))),"string"!=typeof sd.para.web_url||"://"!==sd.para.web_url.slice(0,3)&&"//"!==sd.para.web_url.slice(0,2)||("://"===sd.para.web_url.slice(0,3)?sd.para.web_url=location.protocol.slice(0,-1)+sd.para.web_url:sd.para.web_url=location.protocol+sd.para.web_url),"image"!==sd.para.send_type&&"ajax"!==sd.para.send_type&&"beacon"!==sd.para.send_type&&(sd.para.send_type="image"),sd.debug.protocol.serverUrl(),sd.bridge.initPara(),sd.bridge.initState();var a={datasend_timeout:6e3,send_interval:6e3};_.localStorage.isSupport()&&_.isSupportCors()&&"object"==typeof localStorage?!0===sd.para.batch_send?(sd.para.batch_send=_.extend({},a),sd.para.use_client_time=!0):"object"==typeof sd.para.batch_send&&(sd.para.use_client_time=!0,sd.para.batch_send=_.extend({},a,sd.para.batch_send)):sd.para.batch_send=!1;var i=["utm_source","utm_medium","utm_campaign","utm_content","utm_term"],n=["www.baidu.","m.baidu.","m.sm.cn","so.com","sogou.com","youdao.com","google.","yahoo.com/","bing.com/","ask.com/"],e=["weibo.com","renren.com","kaixin001.com","douban.com","qzone.qq.com","zhihu.com","tieba.baidu.com","weixin.qq.com"],a={baidu:["wd","word","kw","keyword"],google:"q",bing:"q",yahoo:"p",sogou:["query","keyword"],so:"q",sm:"q"};"object"==typeof sd.para.source_type&&(sd.para.source_type.utm=_.isArray(sd.para.source_type.utm)?sd.para.source_type.utm.concat(i):i,sd.para.source_type.search=_.isArray(sd.para.source_type.search)?sd.para.source_type.search.concat(n):n,sd.para.source_type.social=_.isArray(sd.para.source_type.social)?sd.para.source_type.social.concat(e):e,sd.para.source_type.keyword=_.isObject(sd.para.source_type.keyword)?_.extend(a,sd.para.source_type.keyword):a);e=["mark","/mark","strong","b","em","i","u","abbr","ins","del","s","sup"];if(sd.para.heatmap&&!_.isObject(sd.para.heatmap)&&(sd.para.heatmap={}),_.isObject(sd.para.heatmap)&&(sd.para.heatmap.clickmap=sd.para.heatmap.clickmap||"default",sd.para.heatmap.scroll_notice_map=sd.para.heatmap.scroll_notice_map||"default",sd.para.heatmap.scroll_delay_time=sd.para.heatmap.scroll_delay_time||4e3,sd.para.heatmap.scroll_event_duration=sd.para.heatmap.scroll_event_duration||18e3,sd.para.heatmap.renderRefreshTime=sd.para.heatmap.renderRefreshTime||1e3,sd.para.heatmap.loadTimeout=sd.para.heatmap.loadTimeout||1e3,!1!==sd.para.heatmap.get_vtrack_config&&(sd.para.heatmap.get_vtrack_config=!0),(a=_.isArray(sd.para.heatmap.track_attr)?_.filter(sd.para.heatmap.track_attr,function(e){return e&&"string"==typeof e}):[]).push("data-sensors-click"),sd.para.heatmap.track_attr=a,_.isObject(sd.para.heatmap.collect_tags)?!0===sd.para.heatmap.collect_tags.div?sd.para.heatmap.collect_tags.div={ignore_tags:e,max_level:1}:_.isObject(sd.para.heatmap.collect_tags.div)?(sd.para.heatmap.collect_tags.div.ignore_tags?_.isArray(sd.para.heatmap.collect_tags.div.ignore_tags)||(sd.log("ignore_tags 参数必须是数组格式"),sd.para.heatmap.collect_tags.div.ignore_tags=e):sd.para.heatmap.collect_tags.div.ignore_tags=e,sd.para.heatmap.collect_tags.div.max_level&&(~_.indexOf([1,2,3],sd.para.heatmap.collect_tags.div.max_level)||(sd.para.heatmap.collect_tags.div.max_level=1))):sd.para.heatmap.collect_tags.div=!1:sd.para.heatmap.collect_tags={div:!1}),_.isArray(sd.para.server_url)&&sd.para.server_url.length)for(t=0;t<sd.para.server_url.length;t++)/sa\.gif[^\/]*$/.test(sd.para.server_url[t])||(sd.para.server_url[t]=sd.para.server_url[t].replace(/\/sa$/,"/sa.gif").replace(/(\/sa)(\?[^\/]+)$/,"/sa.gif$2"));else/sa\.gif[^\/]*$/.test(sd.para.server_url)||"string"!=typeof sd.para.server_url||(sd.para.server_url=sd.para.server_url.replace(/\/sa$/,"/sa.gif").replace(/(\/sa)(\?[^\/]+)$/,"/sa.gif$2"));"string"==typeof sd.para.server_url&&(sd.para.debug_mode_url=sd.para.debug_mode_url||sd.para.server_url.replace("sa.gif","debug")),sd.para.noCache=!0===sd.para.noCache?"?"+(new Date).getTime():"",sd.para.datasend_timeout<sd.para.callback_timeout&&(sd.para.datasend_timeout=sd.para.callback_timeout),sd.para.queue_timeout<sd.para.callback_timeout&&(sd.para.queue_timeout=sd.para.callback_timeout),sd.para.datasend_timeout<sd.para.queue_timeout&&(sd.para.datasend_timeout=sd.para.queue_timeout),sd.para.has_session_id&&sd.creatSessionId()},sd.readyState={state:0,historyState:[],stateType:{1:"1-init未开始",2:"2-init开始",3:"3-store完成"},getState:function(){return this.historyState.join("\n")},setState:function(e){""+e in this.stateType&&(this.state=e),this.historyState.push(this.stateType[e])}},sd.setPreConfig=function(e){sd.para=e.para,sd._q=e._q},sd.setInitVar=function(){sd._t=sd._t||+new Date,sd.lib_version="1.18.2",sd.is_first_visitor=!1,sd.source_channel_standard="utm_source utm_medium utm_campaign utm_content utm_term"},sd.log=function(){if((_.sessionStorage.isSupport()&&"true"===sessionStorage.getItem("sensorsdata_jssdk_debug")||sd.para.show_log)&&(!_.isObject(arguments[0])||!0!==sd.para.show_log&&"string"!==sd.para.show_log&&!1!==sd.para.show_log||(arguments[0]=_.formatJsonString(arguments[0])),"object"==typeof console&&console.log))try{return console.log.apply(console,arguments)}catch(e){console.log(arguments[0])}},sd.enableLocalLog=function(){if(_.sessionStorage.isSupport())try{sessionStorage.setItem("sensorsdata_jssdk_debug","true")}catch(e){sd.log("enableLocalLog error: "+e.message)}},sd.disableLocalLog=function(){_.sessionStorage.isSupport()&&sessionStorage.removeItem("sensorsdata_jssdk_debug")};var commonWays={setOnlineState:function(e){!0===e&&_.isObject(sd.para.jsapp)&&"function"==typeof sd.para.jsapp.getData?(sd.para.jsapp.isOnline=!0,e=sd.para.jsapp.getData(),_.isArray(e)&&0<e.length&&_.each(e,function(e){_.isJSONString(e)&&sd.sendState.pushSend(JSON.parse(e))})):sd.para.jsapp.isOnline=!1},autoTrackIsUsed:!(sd.debug={distinct_id:function(){},jssdkDebug:function(){},_sendDebug:function(e){sd.track("_sensorsdata2019_debug",{_jssdk_debug_info:e})},apph5:function(e){var t="app_h5打通失败-",r=e.output,s=e.step,e=e.data||"";"all"!==r&&"console"!==r||sd.log({1:t+"use_app_track为false",2:t+"Android或者iOS，没有暴露相应方法",3.1:t+"Android校验server_url失败",3.2:t+"iOS校验server_url失败",4.1:t+"H5 校验 iOS server_url 失败",4.2:t+"H5 校验 Android server_url 失败"}[s]),("all"===r||"code"===r)&&_.isObject(sd.para.is_debug)&&sd.para.is_debug.apph5&&(e.type&&"profile"===e.type.slice(0,7)||(e.properties._jssdk_debug_info="apph5-"+s))},defineMode:function(e){var t={1:{title:"当前页面无法进行可视化全埋点",message:"App SDK 与 Web JS SDK 没有进行打通，请联系贵方技术人员修正 App SDK 的配置，详细信息请查看文档。",link_text:"配置文档",link_url:"https://manual.sensorsdata.cn/sa/latest/tech_sdk_client_link-1573913.html"},2:{title:"当前页面无法进行可视化全埋点",message:"App SDK 与 Web JS SDK 没有进行打通，请联系贵方技术人员修正 Web JS SDK 的配置，详细信息请查看文档。",link_text:"配置文档",link_url:"https://manual.sensorsdata.cn/sa/latest/tech_sdk_client_link-1573913.html"},3:{title:"当前页面无法进行可视化全埋点",message:"Web JS SDK 没有开启全埋点配置，请联系贵方工作人员修正 SDK 的配置，详细信息请查看文档。",link_text:"配置文档",link_url:"https://manual.sensorsdata.cn/sa/latest/tech_sdk_client_web_all-1573964.html"},4:{title:"当前页面无法进行可视化全埋点",message:"Web JS SDK 配置的数据校验地址与 App SDK 配置的数据校验地址不一致，请联系贵方工作人员修正 SDK 的配置，详细信息请查看文档。",link_text:"配置文档",link_url:"https://manual.sensorsdata.cn/sa/latest/tech_sdk_client_link-1573913.html"}};return!(!e||!t[e])&&t[e]},protocol:{protocolIsSame:function(e,t){try{if(_.URL(e).protocol!==_.URL(t).protocol)return!1}catch(e){return sd.log("不支持 _.URL 方法"),!1}return!0},serverUrl:function(){_.isString(sd.para.server_url)&&""!==sd.para.server_url&&!this.protocolIsSame(sd.para.server_url,location.href)&&sd.log("SDK 检测到您的数据发送地址和当前页面地址的协议不一致，建议您修改成一致的协议。\n因为：1、https 下面发送 http 的图片请求会失败。2、http 页面使用 https + ajax 方式发数据，在 ie9 及以下会丢失数据。")},ajax:function(e){if(e===sd.para.server_url)return!1;_.isString(e)&&""!==e&&!this.protocolIsSame(e,location.href)&&sd.log("SDK 检测到您的数据发送地址和当前页面地址的协议不一致，建议您修改成一致的协议。因为 http 页面使用 https + ajax 方式发数据，在 ie9 及以下会丢失数据。")}}}),isReady:function(e){e()},getUtm:function(){return _.info.campaignParams()},getStayTime:function(){return(new Date-sd._t)/1e3},setProfileLocal:function(e){if(!_.localStorage.isSupport())return sd.setProfile(e),!1;if(!_.isObject(e)||_.isEmptyObject(e))return!1;var t=_.localStorage.parse("sensorsdata_2015_jssdk_profile"),r=!1;if(_.isObject(t)&&!_.isEmptyObject(t)){for(var s in e)!(s in t&&t[s]!==e[s])&&s in t||(t[s]=e[s],r=!0);r&&(_.localStorage.set("sensorsdata_2015_jssdk_profile",JSON.stringify(t)),sd.setProfile(e))}else _.localStorage.set("sensorsdata_2015_jssdk_profile",JSON.stringify(e)),sd.setProfile(e)},setInitReferrer:function(){var e=_.getReferrer();sd.setOnceProfile({_init_referrer:e,_init_referrer_host:_.info.pageProp.referrer_host})},setSessionReferrer:function(){var e=_.getReferrer();store.setSessionPropsOnce({_session_referrer:e,_session_referrer_host:_.info.pageProp.referrer_host})},setDefaultAttr:function(){_.info.register({_current_url:location.href,_referrer:_.getReferrer(),_referring_host:_.info.pageProp.referrer_host})},trackHeatMap:function(e,t,r){var s,a,i;"object"==typeof e&&e.tagName&&(s=e.tagName.toLowerCase(),a=e.parentNode.tagName.toLowerCase(),i=sd.para.heatmap&&sd.para.heatmap.track_attr?sd.para.heatmap.track_attr:["data-sensors-click"],"button"==s||"a"==s||"a"==a||"button"==a||"input"==s||"textarea"==s||_.hasAttributes(e,i)||heatmap.start(null,e,s,t,r))},trackAllHeatMap:function(e,t,r){var s;"object"==typeof e&&e.tagName&&(s=e.tagName.toLowerCase(),heatmap.start(null,e,s,t,r))},autoTrackSinglePage:function(e,t){var r;function s(){var e=_.info.campaignParams(),s={};return _.each(e,function(e,t,r){~(" "+sd.source_channel_standard+" ").indexOf(" "+t+" ")?s["$"+t]=r[t]:s[t]=r[t]}),s}function a(e,t){console.log("wwwwwwww"),sd.track("$pageview",_.extend({$referrer:_.isDecodeURI(sd.para.url_is_decode,r),$url:_.isDecodeURI(sd.para.url_is_decode,location.href),$url_path:location.pathname,$title:document.title},e,s()),t),r=location.href}r=this.autoTrackIsUsed?_.info.pageProp.url:_.info.pageProp.referrer,e=_.isObject(e)?e:{},e=_.isObject(e)?e:{},sd.is_first_visitor&&!e.not_set_profile&&(sd.setOnceProfile(_.extend({$first_visit_time:new Date,$first_referrer:_.isDecodeURI(sd.para.url_is_decode,_.getReferrer()),$first_browser_language:navigator.language||"取值异常",$first_browser_charset:"string"==typeof document.charset?document.charset.toUpperCase():"取值异常",$first_traffic_source_type:_.getSourceFromReferrer(),$first_search_keyword:_.getKeywordFromReferrer()},s())),sd.is_first_visitor=!1),e.not_set_profile&&delete e.not_set_profile,a(e,t),this.autoTrackSinglePage=a},autoTrackWithoutProfile:function(e,t){e=_.isObject(e)?e:{},this.autoTrack(_.extend(e,{not_set_profile:!0}),t)},autoTrack:function(t,r){t=_.isObject(t)?t:{};var e=_.info.campaignParams(),s={};_.each(e,function(e,t,r){~(" "+sd.source_channel_standard+" ").indexOf(" "+t+" ")?s["$"+t]=r[t]:s[t]=r[t]}),sd.is_first_visitor&&!t.not_set_profile&&(sd.setOnceProfile(_.extend({$first_visit_time:new Date,$first_referrer:_.isDecodeURI(sd.para.url_is_decode,_.getReferrer()),$first_browser_language:navigator.language||"取值异常",$first_browser_charset:"string"==typeof document.charset?document.charset.toUpperCase():"取值异常",$first_traffic_source_type:_.getSourceFromReferrer(),$first_search_keyword:_.getKeywordFromReferrer()},s)),sd.is_first_visitor=!1),t.not_set_profile&&delete t.not_set_profile;var a=location.href;sd.para.is_single_page&&(console.log("hahahahah"),_.addHashEvent(function(){var e=_.getReferrer(a);sd.track("$pageview",_.extend({$referrer:_.isDecodeURI(sd.para.url_is_decode,e),$url:_.isDecodeURI(sd.para.url_is_decode,location.href),$url_path:location.pathname,$title:document.title},s,t),r),a=location.href})),sd.track("$pageview",_.extend({$referrer:_.isDecodeURI(sd.para.url_is_decode,_.getReferrer()),$url:_.isDecodeURI(sd.para.url_is_decode,location.href),$url_path:location.pathname,$title:document.title},s,t),r);var{loadTime:e}=sd.para.single_page_info;e&&sd.track("$PageLoad",_.extend({$referrer:_.isDecodeURI(sd.para.url_is_decode,_.getReferrer()),$url:_.isDecodeURI(sd.para.url_is_decode,location.href),$url_path:location.pathname,$title:document.title,$load_duration:e,$return_error:e?"成功":"失败"},s,t),r),this.autoTrackIsUsed=!0},getAnonymousID:function(){return _.isEmptyObject(sd.store._state)?"请先初始化SDK":sd.store._state._first_id||sd.store._state.first_id||sd.store._state._distinct_id||sd.store._state.distinct_id},setPlugin:function(e){if(!_.isObject(e))return!1;_.each(e,function(e,t){_.isFunction(e)&&(_.isObject(window.SensorsDataWebJSSDKPlugin)&&window.SensorsDataWebJSSDKPlugin[t]?e(window.SensorsDataWebJSSDKPlugin[t]):sd.log(t+"没有获取到,请查阅文档，调整"+t+"的引入顺序！"))})},useModulePlugin:function(){sd.use.apply(sd,arguments)},useAppPlugin:function(){this.setPlugin.apply(this,arguments)},watchSinglePage:function(e=null){if(e){const t=e.prototype.beforeEach,r=e.prototype.afterEach;e.prototype.beforeEach=function(s){var a=this;return t.call(this,function(e,t,r){return sd.para.single_page_info.loadBeginTime=(new Date).getTime(),s.call(a,e,t,r)})},e.prototype.afterEach=function(i){var n=this;return r.call(this,function(e,t,r){sd.para.single_page_info.loadEndTime=(new Date).getTime();var{loadBeginTime:s,loadEndTime:a}=sd.para.single_page_info;return sd.para.single_page_info.loadTime=(a-s)/1e3,sd.para.isFisrt||(sd.para.isFisrt=!0,sd.track("$PageLoad",_.extend({$referrer:_.isDecodeURI(sd.para.url_is_decode,_.getReferrer()),$url:_.isDecodeURI(sd.para.url_is_decode,location.href),$url_path:location.pathname,$title:document.title,$load_duration:sd.para.single_page_info.loadTime,$return_error:sd.para.single_page_info.loadTime?"成功":"失败"}))),i.call(n,e,t,r)})}}}};function BatchSend(){this.sendingData=0,this.sendingItemKeys=[]}sd.quick=function(){var e=Array.prototype.slice.call(arguments),t=e[0],r=e.slice(1);if(console.log("arg",e),"string"==typeof t&&commonWays[t])return commonWays[t].apply(commonWays,r);"function"==typeof t?t.apply(sd,r):sd.log("quick方法中没有这个功能"+e[0])},sd.use=function(e,t){return _.isString(e)?_.isObject(window.SensorsDataWebJSSDKPlugin)&&_.isObject(window.SensorsDataWebJSSDKPlugin[e])&&_.isFunction(window.SensorsDataWebJSSDKPlugin[e].init)?(window.SensorsDataWebJSSDKPlugin[e].init(sd,t),window.SensorsDataWebJSSDKPlugin[e]):_.isObject(sd.modules)&&_.isObject(sd.modules[e])&&_.isFunction(sd.modules[e].init)?(sd.modules[e].init(sd,t),sd.modules[e]):void sd.log(e+"没有获取到,请查阅文档，调整"+e+"的引入顺序！"):(sd.log("use插件名称必须是字符串！"),!1)},sd.track=function(e,t,r){saEvent.check({event:e,properties:t})&&saEvent.send({type:"track",event:e,properties:t},r)},sd.trackLink=function(e,t,r){"object"==typeof e&&e.tagName?_.trackLink({ele:e},t,r):"object"==typeof e&&e.target&&e.event&&_.trackLink(e,t,r)},sd.trackLinks=function(s,a,i){return i=i||{},!(!s||"object"!=typeof s)&&(!(!s.href||/^javascript/.test(s.href)||s.target)&&void _.addEvent(s,"click",function(e){e.preventDefault();var t=!1;function r(){t||(t=!0,location.href=s.href)}setTimeout(r,1e3),sd.track(a,i,r)}))},sd.setProfile=function(e,t){saEvent.check({propertiesMust:e})&&saEvent.send({type:"profile_set",properties:e},t)},sd.setOnceProfile=function(e,t){saEvent.check({propertiesMust:e})&&saEvent.send({type:"profile_set_once",properties:e},t)},sd.appendProfile=function(r,e){saEvent.check({propertiesMust:r})&&(_.each(r,function(e,t){_.isString(e)?r[t]=[e]:_.isArray(e)?r[t]=e:(delete r[t],sd.log("appendProfile属性的值必须是字符串或者数组"))}),_.isEmptyObject(r)||saEvent.send({type:"profile_append",properties:r},e))},sd.incrementProfile=function(e,t){var r=e;_.isString(e)&&((e={})[r]=1),saEvent.check({propertiesMust:e})&&(!function(e){for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t)&&!/-*\d+/.test(""+e[t]))return;return 1}(e)?sd.log("profile_increment的值只能是数字"):saEvent.send({type:"profile_increment",properties:e},t))},sd.deleteProfile=function(e){saEvent.send({type:"profile_delete"},e),store.set("distinct_id",_.UUID()),store.set("first_id","")},sd.unsetProfile=function(e,t){var r=e,s={};_.isString(e)&&(e=[]).push(r),_.isArray(e)?(_.each(e,function(e){_.isString(e)?s[e]=!0:sd.log("profile_unset给的数组里面的值必须时string,已经过滤掉",e)}),saEvent.send({type:"profile_unset",properties:s},t)):sd.log("profile_unset的参数是数组")},sd.identify=function(e,t){"number"==typeof e&&(e=""+e);var r,s=store.getFirstId();void 0===e?(r=_.UUID(),store.set(s?"first_id":"distinct_id",r)):saEvent.check({distinct_id:e})?!0===t?store.set(s?"first_id":"distinct_id",e):store.change(s?"first_id":"distinct_id",e):sd.log("identify的参数必须是字符串")},sd.trackSignup=function(e,t,r,s){var a;saEvent.check({distinct_id:e,event:t,properties:r})&&(a=store.getFirstId()||store.getDistinctId(),store.set("distinct_id",e),saEvent.send({original_id:a,distinct_id:e,type:"track_signup",event:t,properties:r},s))},sd.registerPage=function(e){saEvent.check({properties:e})?_.extend(_.info.currentProps,e):sd.log("register输入的参数有误")},sd.clearAllRegister=function(e){store.clearAllProps(e)},sd.clearPageRegister=function(e){if(_.isArray(e)&&0<e.length)for(var t=0;t<e.length;t++)_.isString(e[t])&&e[t]in _.info.currentProps&&delete _.info.currentProps[e[t]];else if(!0===e)for(var t in _.info.currentProps)delete _.info.currentProps[t]},sd.register=function(e){saEvent.check({properties:e})?store.setProps(e):sd.log("register输入的参数有误")},sd.registerOnce=function(e){saEvent.check({properties:e})?store.setPropsOnce(e):sd.log("registerOnce输入的参数有误")},sd.registerSession=function(e){saEvent.check({properties:e})?store.setSessionProps(e):sd.log("registerSession输入的参数有误")},sd.registerSessionOnce=function(e){saEvent.check({properties:e})?store.setSessionPropsOnce(e):sd.log("registerSessionOnce输入的参数有误")},sd.login=function(e,t){var r,s;saEvent.check({distinct_id:e="number"==typeof e?""+e:e})?(r=store.getFirstId(),e!==(s=store.getDistinctId())?(r||store.set("first_id",s),sd.trackSignup(e,"$SignUp",{},t)):t&&t()):(sd.log("login的参数必须是字符串"),t&&t())},sd.logout=function(e){var t=store.getFirstId();t?(store.set("first_id",""),!0===e?(e=_.UUID(),store.set("distinct_id",e)):store.set("distinct_id",t)):sd.log("没有first_id，logout失败")},sd.getPresetProperties=function(){var e,s,t={$is_first_day:_.cookie.getNewUser(),$referrer:_.isDecodeURI(sd.para.url_is_decode,_.info.pageProp.referrer)||"",$referrer_host:_.info.pageProp.referrer?_.getHostname(_.info.pageProp.referrer):"",$url:_.isDecodeURI(sd.para.url_is_decode,location.href),$url_path:location.pathname,$title:document.title||"",_distinct_id:store.getDistinctId()},t=_.extend({},_.info.properties(),sd.store.getProps(),(e=_.info.campaignParams(),s={},_.each(e,function(e,t,r){~(" "+sd.source_channel_standard+" ").indexOf(" "+t+" ")?s["$"+t]=r[t]:s[t]=r[t]}),s),t);return sd.para.preset_properties.latest_referrer&&sd.para.preset_properties.latest_referrer_host&&(t.$latest_referrer_host=""===t.$latest_referrer?"":_.getHostname(t.$latest_referrer)),t},sd.detectMode=function(){function e(e){var t,r=sd.bridge.initDefineBridgeInfo();function s(){var e=[];r.touch_app_bridge||e.push(sd.debug.defineMode("1")),_.isObject(sd.para.app_js_bridge)||(e.push(sd.debug.defineMode("2")),r.verify_success=!1),_.isObject(sd.para.heatmap)&&"default"==sd.para.heatmap.clickmap||e.push(sd.debug.defineMode("3")),"fail"===r.verify_success&&e.push(sd.debug.defineMode("4"));e={callType:"app_alert",data:e};SensorsData_App_Visual_Bridge&&SensorsData_App_Visual_Bridge.sensorsdata_visualized_alert_info?SensorsData_App_Visual_Bridge.sensorsdata_visualized_alert_info(JSON.stringify(e)):window.webkit&&window.webkit.messageHandlers&&window.webkit.messageHandlers.sensorsdataNativeTracker&&window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage&&window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage(JSON.stringify(e))}_.isObject(window.SensorsData_App_Visual_Bridge)&&window.SensorsData_App_Visual_Bridge.sensorsdata_visualized_mode&&(!0===window.SensorsData_App_Visual_Bridge.sensorsdata_visualized_mode||window.SensorsData_App_Visual_Bridge.sensorsdata_visualized_mode())&&(_.isObject(sd.para.heatmap)&&"default"==sd.para.heatmap.clickmap&&_.isObject(sd.para.app_js_bridge)&&"success"===r.verify_success?e?sa_jssdk_app_define_mode(sd,e):(t=location.protocol,t=~_.indexOf(["http:","https:"],t)?t:"https:",_.loadScript({success:function(){setTimeout(function(){"undefined"!=typeof sa_jssdk_app_define_mode&&sa_jssdk_app_define_mode(sd,e)},0)},error:function(){},type:"js",url:t+"//static.sensorsdata.cn/sdk/"+sd.lib_version+"/vapph5define.min.js"})):s())}var t={searchKeywordMatch:location.search.match(/sa-request-id=([^&#]+)/),isSeachHasKeyword:function(){var e=this.searchKeywordMatch;return!!(e&&e[0]&&e[1])&&("string"==typeof sessionStorage.getItem("sensors-visual-mode")&&sessionStorage.removeItem("sensors-visual-mode"),!0)},hasKeywordHandle:function(){var e=this.searchKeywordMatch,t=location.search.match(/sa-request-type=([^&#]+)/),r=location.search.match(/sa-request-url=([^&#]+)/);heatmap.setNotice(r),_.sessionStorage.isSupport()&&(r&&r[0]&&r[1]&&sessionStorage.setItem("sensors_heatmap_url",decodeURIComponent(r[1])),sessionStorage.setItem("sensors_heatmap_id",e[1]),t&&t[0]&&t[1]?"1"===t[1]||"2"===t[1]||"3"===t[1]?(t=t[1],sessionStorage.setItem("sensors_heatmap_type",t)):t=null:t=null!==sessionStorage.getItem("sensors_heatmap_type")?sessionStorage.getItem("sensors_heatmap_type"):null),this.isReady(e[1],t)},isReady:function(e,t,r){sd.para.heatmap_url?_.loadScript({success:function(){setTimeout(function(){"undefined"!=typeof sa_jssdk_heatmap_render&&(sa_jssdk_heatmap_render(sd,e,t,r),"object"==typeof console&&"function"==typeof console.log&&(sd.heatmap_version&&sd.heatmap_version===sd.lib_version||console.log("heatmap.js与sensorsdata.js版本号不一致，可能存在风险!")))},0)},error:function(){},type:"js",url:sd.para.heatmap_url}):sd.log("没有指定heatmap_url的路径")},isStorageHasKeyword:function(){return _.sessionStorage.isSupport()&&"string"==typeof sessionStorage.getItem("sensors_heatmap_id")},storageHasKeywordHandle:function(){heatmap.setNotice(),t.isReady(sessionStorage.getItem("sensors_heatmap_id"),sessionStorage.getItem("sensors_heatmap_type"),location.href)}},s={isStorageHasKeyword:function(){return _.sessionStorage.isSupport()&&"string"==typeof sessionStorage.getItem("sensors-visual-mode")},isSearchHasKeyword:function(){return!!location.search.match(/sa-visual-mode=true/)&&("string"==typeof sessionStorage.getItem("sensors_heatmap_id")&&sessionStorage.removeItem("sensors_heatmap_id"),!0)},loadVtrack:function(){_.loadScript({success:function(){},error:function(){},type:"js",url:sd.para.vtrack_url||location.protocol+"//static.sensorsdata.cn/sdk/"+sd.lib_version+"/vtrack.min.js"})},messageListener:function(e){if("sa-fe"!==e.data.source)return!1;var t,r;"v-track-mode"===e.data.type&&(e.data.data&&e.data.data.isVtrack&&(_.sessionStorage.isSupport()&&sessionStorage.setItem("sensors-visual-mode","true"),e.data.data.userURL&&location.search.match(/sa-visual-mode=true/)?(t=_.secCheck.isHttpUrl(r=e.data.data.userURL)?_.secCheck.removeScriptProtocol(r):(sd.log("可视化模式检测 URL 失败"),!1))&&(window.location.href=t):s.loadVtrack()),window.removeEventListener("message",s.messageListener,!1))},removeMessageHandle:function(){window.removeEventListener&&window.removeEventListener("message",s.messageListener,!1)},verifyVtrackMode:function(){window.addEventListener&&window.addEventListener("message",s.messageListener,!1),s.postMessage()},postMessage:function(){window.parent&&window.parent.postMessage&&window.parent.postMessage({source:"sa-web-sdk",type:"v-is-vtrack",data:{sdkversion:"1.18.2"}},"*")},notifyUser:function(){var t=function(e){if("sa-fe"!==e.data.source)return!1;"v-track-mode"===e.data.type&&(e.data.data&&e.data.data.isVtrack&&alert("当前版本不支持，请升级部署神策数据治理"),window.removeEventListener("message",t,!1))};window.addEventListener&&window.addEventListener("message",t,!1),s.postMessage()}};function r(){sd.readyState.setState(3),new sd.JSBridge({type:"visualized",app_call_js:function(){"undefined"!=typeof sa_jssdk_app_define_mode?e(!0):e(!1)}}),e(!1),sd.bridge.app_js_bridge_v1(),_.info.initPage(),sd.para.is_track_single_page&&_.addSinglePageEvent(function(t){function e(e){e=e||{},t!==location.href&&(_.info.pageProp.referrer=t,t=_.isDecodeURI(sd.para.url_is_decode,t),sd.quick("autoTrack",_.extend({$url:_.isDecodeURI(sd.para.url_is_decode,location.href),$referrer:t},e)))}var r;"boolean"==typeof sd.para.is_track_single_page?e():"function"==typeof sd.para.is_track_single_page&&(r=sd.para.is_track_single_page(),_.isObject(r)?e(r):!0===r&&e())}),sd.para.batch_send&&(_.addEvent(window,"onpagehide"in window?"pagehide":"unload",function(e){sd.batchSend.clearPendingStatus()}),sd.batchSend.batchInterval()),sd.store.init(),sd.vtrackcollect.init(),sd.readyState.setState(4),sd._q&&_.isArray(sd._q)&&0<sd._q.length&&_.each(sd._q,function(e){sd[e[0]].apply(sd,Array.prototype.slice.call(e[1]))}),_.isObject(sd.para.heatmap)&&(heatmap.initHeatmap(),heatmap.initScrollmap())}sd.para.heatmap&&sd.para.heatmap.collect_tags&&_.isObject(sd.para.heatmap.collect_tags)&&_.each(sd.para.heatmap.collect_tags,function(e,t){"div"!==t&&e&&sd.heatmap.otherTags.push(t)}),t.isSeachHasKeyword()?t.hasKeywordHandle():window.parent!==self&&s.isSearchHasKeyword()?s.verifyVtrackMode():t.isStorageHasKeyword()?t.storageHasKeywordHandle():window.parent!==self&&s.isStorageHasKeyword()?s.verifyVtrackMode():(r(),s.notifyUser())},sd.removeSessionId=function(){sd.session_id=""},sd.creatSessionId=function(){sd.create_sessionid_time=(new Date).getTime(),sd.session_id=_.UUID()},BatchSend.prototype={add:function(e){_.isObject(e)&&(this.writeStore(e),"track_signup"!==e.type&&"$pageview"!==e.event||this.sendStrategy())},clearPendingStatus:function(){this.sendingItemKeys.length&&this.removePendingItems(this.sendingItemKeys)},remove:function(e){0<this.sendingData&&--this.sendingData,_.isArray(e)&&0<e.length&&_.each(e,function(e){_.localStorage.remove(e)})},send:function(e){var t,r=this;_.isString(sd.para.server_url)&&""!==sd.para.server_url||_.isArray(sd.para.server_url)&&sd.para.server_url.length?(t=_.isArray(sd.para.server_url)?sd.para.server_url[0]:sd.para.server_url,_.ajax({url:t,type:"POST",data:"data_list="+encodeURIComponent(_.base64Encode(JSON.stringify(e.vals))),credentials:!1,timeout:sd.para.batch_send.datasend_timeout,cors:!0,success:function(){r.remove(e.keys),r.removePendingItems(e.keys)},error:function(){0<r.sendingData&&--r.sendingData,r.removePendingItems(e.keys)}})):sd.log("当前 server_url 为空或不正确，只在控制台打印日志，network 中不会发数据，请配置正确的 server_url！")},appendPendingItems:function(e){if(!1!==_.isArray(e)){this.sendingItemKeys=_.unique(this.sendingItemKeys.concat(e));try{var t=this.getPendingItems(),r=_.unique(t.concat(e));localStorage.setItem("sawebjssdk-sendingitems",JSON.stringify(r))}catch(e){}}},removePendingItems:function(t){if(!1!==_.isArray(t)){this.sendingItemKeys.length&&(this.sendingItemKeys=_.filter(this.sendingItemKeys,function(e){return!~_.indexOf(t,e)}));try{var e=this.getPendingItems(),r=_.filter(e,function(e){return!~_.indexOf(t,e)});localStorage.setItem("sawebjssdk-sendingitems",JSON.stringify(r))}catch(e){}}},getPendingItems:function(){var e=[];try{var t=localStorage.getItem("sawebjssdk-sendingitems");t&&(e=JSON.parse(t))}catch(e){}return e},sendPrepare:function(e){this.appendPendingItems(e.keys);var t=e.vals;0<t.length&&this.send({keys:e.keys,vals:t})},sendStrategy:function(){if(!1===document.hasFocus())return!1;var e=this.readStore();0<e.keys.length&&0===this.sendingData&&(this.sendingData=1,this.sendPrepare(e))},batchInterval:function(){var e=this;setInterval(function(){e.sendStrategy()},sd.para.batch_send.send_interval)},readStore:function(){for(var e=[],t=[],r=null,s=(new Date).getTime(),a=localStorage.length,i=this.getPendingItems(),n=0;n<a;n++){var o=localStorage.key(n);!o.indexOf("sawebjssdk-")&&/^sawebjssdk\-\d+$/.test(o)&&(i.length&&~_.indexOf(i,o)||((r=localStorage.getItem(o))?(r=_.safeJSONParse(r))&&_.isObject(r)?(r._flush_time=s,e.push(o),t.push(r)):(localStorage.removeItem(o),sd.log("localStorage-数据parse异常"+r)):(localStorage.removeItem(o),sd.log("localStorage-数据取值异常"+r))))}return{keys:e,vals:t}},writeStore:function(e){var t=(""+_.getRandom()).slice(2,5)+(""+_.getRandom()).slice(2,5)+(""+(new Date).getTime()).slice(3);localStorage.setItem("sawebjssdk-"+t,JSON.stringify(e))}},sd.batchSend=new BatchSend;var dataSend={getSendUrl:function(e,t){var r=_.base64Encode(t),t="crc="+_.hashCode(r);return~e.indexOf("?")?e+"&data="+encodeURIComponent(r)+"&ext="+encodeURIComponent(t):e+"?data="+encodeURIComponent(r)+"&ext="+encodeURIComponent(t)},getSendData:function(e){var t=_.base64Encode(e),e="crc="+_.hashCode(t);return"data="+encodeURIComponent(t)+"&ext="+encodeURIComponent(e)},getInstance:function(e){var t=this.getSendType(e),e=new this[t](e),r=e.start;return e.start=function(){_.isObject(sd.para.is_debug)&&sd.para.is_debug.storage&&sd.store.requests&&sd.store.requests.push({name:this.server_url,initiatorType:this.img?"img":"xmlhttprequest",entryType:"resource",requestData:this.data});var e=this;r.apply(this,arguments),setTimeout(function(){e.isEnd(!0)},sd.para.callback_timeout)},e.end=function(){this.callback&&this.callback();var e=this;setTimeout(function(){e.lastClear&&e.lastClear()},sd.para.datasend_timeout-sd.para.callback_timeout)},e.isEnd=function(e){var t;this.received||(this.received=!0,this.end(),t=this,e&&0<sd.para.queue_timeout-sd.para.callback_timeout?setTimeout(function(){t.close()},sd.para.queue_timeout-sd.para.callback_timeout):t.close())},e},getRealtimeInstance:function(e){var t=this.getSendType(e),e=new this[t](e),r=e.start;return e.start=function(){var e=this;r.apply(this,arguments),setTimeout(function(){e.isEnd(!0)},sd.para.callback_timeout)},e.end=function(){this.callback&&this.callback();var e=this;setTimeout(function(){e.lastClear&&e.lastClear()},sd.para.datasend_timeout-sd.para.callback_timeout)},e.isEnd=function(e){this.received||(this.received=!0,this.end())},e},getSendType:function(e){var t=["image","ajax","beacon"],r=t[0];return r="ajax"===(r="beacon"===(r=(e.config&&~_.indexOf(t,e.config.send_type)?e.config:sd.para).send_type)&&!1===_.isSupportBeaconSend()?"image":r)&&!1===_.isSupportCors()?"image":r},image:function(e){this.callback=e.callback,this.img=document.createElement("img"),this.img.width=1,this.img.height=1,sd.para.img_use_crossorigin&&(this.img.crossOrigin="anonymous"),this.data=e.data,this.server_url=dataSend.getSendUrl(e.server_url,e.data)}};dataSend.image.prototype.start=function(){var e=this;sd.para.ignore_oom&&(this.img.onload=function(){this.onload=null,this.onerror=null,this.onabort=null,e.isEnd()},this.img.onerror=function(){this.onload=null,this.onerror=null,this.onabort=null,e.isEnd()},this.img.onabort=function(){this.onload=null,this.onerror=null,this.onabort=null,e.isEnd()}),this.img.src=this.server_url},dataSend.image.prototype.lastClear=function(){this.img.src=""},dataSend.ajax=function(e){this.callback=e.callback,this.server_url=e.server_url,this.data=dataSend.getSendData(e.data)},dataSend.ajax.prototype.start=function(){var e=this;_.ajax({url:this.server_url,type:"POST",data:this.data,credentials:!1,timeout:sd.para.datasend_timeout,cors:!0,success:function(){e.isEnd()},error:function(){e.isEnd()}})},dataSend.beacon=function(e){this.callback=e.callback,this.server_url=e.server_url,this.data=dataSend.getSendData(e.data)},dataSend.beacon.prototype.start=function(){var e=this;"object"==typeof navigator&&"function"==typeof navigator.sendBeacon&&navigator.sendBeacon(this.server_url,this.data),setTimeout(function(){e.isEnd()},40)};var sendState={};sd.sendState=sendState,sd.events=new _.eventEmitter,sendState.queue=_.autoExeQueue(),sendState.getSendCall=function(e,t,r){if(sd.is_heatmap_render_mode)return!1;if(sd.readyState.state<3)return sd.log("初始化没有完成"),!1;e._track_id=+(""+((""+_.getRandom()).slice(2,5)+(""+_.getRandom()).slice(2,4)+(""+(new Date).getTime()).slice(-4))),sd.para.use_client_time&&(e._flush_time=(new Date).getTime());var s=e;e=JSON.stringify(e);t={data:s,config:t,callback:r};if(sd.events.tempAdd("send",s),!sd.para.app_js_bridge&&sd.para.batch_send&&localStorage.length<200)return sd.log(s),sd.batchSend.add(t.data),!1;sd.bridge.dataSend(t,this,r),sd.log(s)},sendState.prepareServerUrl=function(e){if("object"==typeof e.config&&e.config.server_url)this.sendCall(e,e.config.server_url,e.callback);else if(_.isArray(sd.para.server_url)&&sd.para.server_url.length)for(var t=0;t<sd.para.server_url.length;t++)this.sendCall(e,sd.para.server_url[t]);else"string"==typeof sd.para.server_url&&""!==sd.para.server_url?this.sendCall(e,sd.para.server_url,e.callback):sd.log("当前 server_url 为空或不正确，只在控制台打印日志，network 中不会发数据，请配置正确的 server_url！")},sendState.sendCall=function(e,t,r){e={server_url:t,data:JSON.stringify(e.data),callback:r,config:e.config};_.isObject(sd.para.jsapp)&&!sd.para.jsapp.isOnline&&"function"==typeof sd.para.jsapp.setData?(delete e.callback,e=JSON.stringify(e),sd.para.jsapp.setData(e)):sd.para.use_client_time?this.realtimeSend(e):this.pushSend(e)},sendState.pushSend=function(e){var e=dataSend.getInstance(e),t=this;e.close=function(){t.queue.close()},this.queue.enqueue(e)},sendState.realtimeSend=function(e){dataSend.getRealtimeInstance(e).start()};var saEvent={};(sd.saEvent=saEvent).checkOption={regChecks:{regName:/^((?!^distinct_id$|^original_id$|^time$|^properties$|^id$|^first_id$|^second_id$|^users$|^events$|^event$|^user_id$|^date$|^datetime$)[a-zA-Z_$][a-zA-Z\d_$]{0,99})$/i},checkPropertiesKey:function(e){var r=this,s=!0;return _.each(e,function(e,t){r.regChecks.regName.test(t)||(s=!1)}),s},check:function(e,t){return"string"==typeof this[e]?this[this[e]](t):_.isFunction(this[e])?this[e](t):void 0},str:function(e){return _.isString(e)||sd.log("请检查参数格式,必须是字符串"),!0},properties:function(e){return _.strip_sa_properties(e),e&&(_.isObject(e)?this.checkPropertiesKey(e)||sd.log("properties 里的自定义属性名需要是合法的变量名，不能以数字开头，且只包含：大小写字母、数字、下划线，自定义属性不能以 $ 开头"):sd.log("properties可以没有，但有的话必须是对象")),!0},propertiesMust:function(e){return _.strip_sa_properties(e),void 0===e||!_.isObject(e)||_.isEmptyObject(e)?sd.log("properties必须是对象且有值"):this.checkPropertiesKey(e)||sd.log("properties 里的自定义属性名需要是合法的变量名，不能以数字开头，且只包含：大小写字母、数字、下划线，自定义属性不能以 $ 开头"),!0},event:function(e){return _.isString(e)&&this.regChecks.regName.test(e)||sd.log("请检查参数格式，eventName 必须是字符串，且需是合法的变量名，即不能以数字开头，且只包含：大小写字母、数字、下划线和 $,其中以 $ 开头的表明是系统的保留字段，自定义事件名请不要以 $ 开头"),!0},test_id:"str",group_id:"str",distinct_id:function(e){return!(!_.isString(e)||!/^.{1,255}$/.test(e))||(sd.log("distinct_id必须是不能为空，且小于255位的字符串"),!1)}},saEvent.check=function(e){for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t)&&!this.checkOption.check(t,e[t]))return!1;return!0},saEvent.send=function(e,t){sd.para.has_session_id&&sd.session_id&&((new Date).getTime()-sd.create_sessionid_time<sd.para.session_id_expire_time||(sd.create_sessionid_time=(new Date).getTime(),sd.session_id=_.UUID()));var r={distinct_id:store.getDistinctId(),lib:{$lib:"js",$lib_method:"code",$lib_version:""+sd.lib_version},properties:{},session_id:sd.session_id||""};_.isObject(e)&&_.isObject(e.properties)&&!_.isEmptyObject(e.properties)&&(e.properties.$lib_detail&&(r.lib.$lib_detail=e.properties.$lib_detail,delete e.properties.$lib_detail),e.properties.$lib_method&&(r.lib.$lib_method=e.properties.$lib_method,delete e.properties.$lib_method)),_.extend(r,sd.store.getUnionId(),e),_.isObject(e.properties)&&!_.isEmptyObject(e.properties)&&_.extend(r.properties,e.properties),e.type&&"profile"===e.type.slice(0,7)||(r.properties=_.extend({},_.info.properties(),store.getProps(),store.getSessionProps(),_.info.currentProps,r.properties),sd.para.preset_properties.latest_referrer&&!_.isString(r.properties.$latest_referrer)&&(r.properties.$latest_referrer="取值异常"),sd.para.preset_properties.latest_search_keyword&&!_.isString(r.properties.$latest_search_keyword)&&(r.properties.$latest_search_keyword="取值异常"),sd.para.preset_properties.latest_traffic_source_type&&!_.isString(r.properties.$latest_traffic_source_type)&&(r.properties.$latest_traffic_source_type="取值异常"),sd.para.preset_properties.latest_landing_page&&!_.isString(r.properties.$latest_landing_page)&&(r.properties.$latest_landing_page="取值异常"),"not_collect"===sd.para.preset_properties.latest_wx_ad_click_id?(delete r.properties._latest_wx_ad_click_id,delete r.properties._latest_wx_ad_hash_key,delete r.properties._latest_wx_ad_callbacks):sd.para.preset_properties.latest_wx_ad_click_id&&!_.isString(r.properties._latest_wx_ad_click_id)&&(r.properties._latest_wx_ad_click_id="取值异常",r.properties._latest_wx_ad_hash_key="取值异常",r.properties._latest_wx_ad_callbacks="取值异常"),_.isString(r.properties._latest_wx_ad_click_id)&&(r.properties.$url=_.isDecodeURI(sd.para.url_is_decode,window.location.href))),r.properties.$time&&_.isDate(r.properties.$time)?(r.time=+r.properties.$time,delete r.properties.$time):sd.para.use_client_time&&(r.time=+new Date);e=sd.vtrackcollect.customProp.getVtrackProps(JSON.parse(JSON.stringify(r)));_.isObject(e)&&!_.isEmptyObject(e)&&(r.properties=_.extend(r.properties,e)),_.parseSuperProperties(r),_.filterReservedProperties(r.properties),_.searchObjDate(r),_.searchObjString(r),_.searchZZAppStyle(r);e=_.searchConfigData(r.properties);saNewUser.checkIsAddSign(r),saNewUser.checkIsFirstTime(r),sd.addReferrerHost(r),sd.addPropsHook(r),!0===sd.para.debug_mode?(sd.log(r),this.debugPath(JSON.stringify(r),t)):sd.sendState.getSendCall(r,e,t)},saEvent.debugPath=function(e,t){var r=e,s="",s=~sd.para.debug_mode_url.indexOf("?")?sd.para.debug_mode_url+"&data="+encodeURIComponent(_.base64Encode(e)):sd.para.debug_mode_url+"?data="+encodeURIComponent(_.base64Encode(e));_.ajax({url:s,type:"GET",cors:!0,header:{"Dry-Run":""+sd.para.debug_mode_upload},success:function(e){!0===_.isEmptyObject(e)?alert("debug数据发送成功"+r):alert("debug失败 错误原因"+JSON.stringify(e))}})};var store=sd.store={requests:[],_sessionState:{},_state:{distinct_id:"",first_id:"",props:{}},getProps:function(){return this._state.props||{}},getSessionProps:function(){return this._sessionState},getDistinctId:function(){return this._state._distinct_id||this._state.distinct_id},getUnionId:function(){var e={},t=this._state._first_id||this._state.first_id,r=this._state._distinct_id||this._state.distinct_id;return t&&r?(e.login_id=r,e.anonymous_id=t):e.anonymous_id=r,e},getFirstId:function(){return this._state._first_id||this._state.first_id},toState:function(e){var t=null;if(null!=e&&_.isJSONString(e))if(t=JSON.parse(e),this._state=_.extend(t),t.distinct_id){if("object"==typeof t.props){for(var r in t.props)"string"==typeof t.props[r]&&(t.props[r]=t.props[r].slice(0,sd.para.max_referrer_string_length));this.save()}}else this.set("distinct_id",_.UUID()),sd.debug.distinct_id("1",e);else this.set("distinct_id",_.UUID()),sd.debug.distinct_id("2",e)},initSessionState:function(){var e=_.cookie.get("sensorsdata2015session"),t=null;null!==e&&"object"==typeof(t=JSON.parse(e))&&(this._sessionState=t||{})},setOnce:function(e,t){e in this._state||this.set(e,t)},set:function(e,t){this._state=this._state||{};var r=this._state.distinct_id;this._state[e]=t,"first_id"===e?delete this._state._first_id:"distinct_id"===e&&delete this._state._distinct_id,this.save(),"distinct_id"===e&&r&&sd.events.tempAdd("changeDistinctId",t)},change:function(e,t){this._state["_"+e]=t},setSessionProps:function(e){var t=this._sessionState;_.extend(t,e),this.sessionSave(t)},setSessionPropsOnce:function(e){var t=this._sessionState;_.coverExtend(t,e),this.sessionSave(t)},setProps:function(e,t){var r,s={};for(r in s=t?e:_.extend(this._state.props||{},e))"string"==typeof s[r]&&(s[r]=s[r].slice(0,sd.para.max_referrer_string_length));this.set("props",s)},setPropsOnce:function(e){var t=this._state.props||{};_.coverExtend(t,e),this.set("props",t)},clearAllProps:function(e){if(this._sessionState={},_.isArray(e)&&0<e.length)for(var t=0;t<e.length;t++)_.isString(e[t])&&!~e[t].indexOf("latest_")&&e[t]in this._state.props&&delete this._state.props[e[t]];else for(var t in this._state.props)1!=t.indexOf("latest_")&&delete this._state.props[t];this.sessionSave({}),this.save()},sessionSave:function(e){this._sessionState=e,_.cookie.set("sensorsdata2015session",JSON.stringify(this._sessionState),0)},save:function(){var e=JSON.parse(JSON.stringify(this._state));delete e._first_id,delete e._distinct_id;e=JSON.stringify(e);sd.para.encrypt_cookie&&(e=_.cookie.encrypt(e)),_.cookie.set(this.getCookieName(),e,73e3,sd.para.cross_subdomain)},getCookieName:function(){var e="";if(!1===sd.para.cross_subdomain){try{e=_.URL(location.href).hostname}catch(e){sd.log(e)}e="string"==typeof e&&""!==e?"sa_jssdk_2015_"+e.replace(/\./g,"_"):"sa_jssdk_2015_root"}else e="sensorsdata2015jssdkcross";return e},init:function(){this.initSessionState();var e=_.UUID(),t=_.cookie.get(this.getCookieName());null===(t=_.cookie.resolveValue(t))?(sd.is_first_visitor=!0,this.set("distinct_id",e)):(_.isJSONString(t)&&JSON.parse(t).distinct_id||(sd.is_first_visitor=!0),this.toState(t)),saNewUser.setDeviceId(e),saNewUser.storeInitCheck(),saNewUser.checkIsFirstLatest()}},saNewUser={checkIsAddSign:function(e){"track"===e.type&&(_.cookie.getNewUser()?e.properties.$is_first_day=!0:e.properties.$is_first_day=!1)},is_first_visit_time:!1,checkIsFirstTime:function(e){"track"===e.type&&"$pageview"===e.event&&(this.is_first_visit_time?this.is_first_visit_time=!(e.properties.$is_first_time=!0):e.properties.$is_first_time=!1)},setDeviceId:function(e){var t=null,r=_.cookie.get("sensorsdata2015jssdkcross"),s={},t=(t=null!=(r=_.cookie.resolveValue(r))&&_.isJSONString(r)&&(s=JSON.parse(r)).$device_id?s.$device_id:t)||e;!0===sd.para.cross_subdomain?store.set("$device_id",t):(s.$device_id=t,s=JSON.stringify(s),sd.para.encrypt_cookie&&(s=_.cookie.encrypt(s)),_.cookie.set("sensorsdata2015jssdkcross",s,null,!0)),sd.para.is_track_device_id&&(_.info.currentProps.$device_id=t)},storeInitCheck:function(){var e,t,r;sd.is_first_visitor?(e=23-(r=new Date).getHours(),t=59-r.getMinutes(),r=59-r.getSeconds(),_.cookie.set(_.cookie.getCookieName("new_user"),"1",3600*e+60*t+r+"s"),this.is_first_visit_time=!0):(_.cookie.getNewUser()||(this.checkIsAddSign=function(e){"track"===e.type&&(e.properties.$is_first_day=!1)}),this.checkIsFirstTime=function(e){"track"===e.type&&"$pageview"===e.event&&(e.properties.$is_first_time=!1)})},checkIsFirstLatest:function(){for(var a=_.info.pageProp.url_domain,e=["$utm_source","$utm_medium","$utm_campaign","$utm_content","$utm_term"],t=store.getProps(),r=0;r<5;r++)e[r]in t&&delete t[e[r]];store.setProps(t,!0);var s,i,n={};""===a&&(a="url解析失败"),_.each(sd.para.preset_properties,function(e,t){if(!~t.indexOf("latest_"))return!1;if(t=t.slice(7),e){if("wx_ad_click_id"===t&&"not_collect"===e)return!1;if("utm"!==t&&"url解析失败"===a)"wx_ad_click_id"===t?(n._latest_wx_ad_click_id="url的domain解析失败",n._latest_wx_ad_hash_key="url的domain解析失败",n._latest_wx_ad_callbacks="url的domain解析失败"):n["$latest_"+t]="url的domain解析失败";else if(_.isReferralTraffic(document.referrer))switch(t){case"traffic_source_type":n.$latest_traffic_source_type=_.getSourceFromReferrer();break;case"referrer":n.$latest_referrer=_.isDecodeURI(sd.para.url_is_decode,_.info.pageProp.referrer);break;case"search_keyword":n.$latest_search_keyword=_.getKeywordFromReferrer();break;case"landing_page":n.$latest_landing_page=_.isDecodeURI(sd.para.url_is_decode,location.href);break;case"wx_ad_click_id":var r=_.getWxAdIdFromUrl(location.href);n._latest_wx_ad_click_id=r.click_id,n._latest_wx_ad_hash_key=r.hash_key,n._latest_wx_ad_callbacks=r.callbacks}}else if("utm"===t&&sd.store._state.props)for(var s in sd.store._state.props)s.indexOf("$latest_utm")&&(s.indexOf("_latest_")||~s.indexOf("_latest_wx_ad_"))||delete sd.store._state.props[s];else sd.store._state.props&&"$latest_"+t in sd.store._state.props?delete sd.store._state.props["$latest_"+t]:"wx_ad_click_id"==t&&sd.store._state.props&&!1===e&&_.each(["_latest_wx_ad_click_id","_latest_wx_ad_hash_key","_latest_wx_ad_callbacks"],function(e){e in sd.store._state.props&&delete sd.store._state.props[e]})}),sd.register(n),sd.para.preset_properties.latest_utm&&(i=(s=_.info.campaignParamsStandard("$latest_","_latest_")).otherUtms,_.isEmptyObject(s=s.$utms)||sd.register(s),_.isEmptyObject(i)||sd.register(i))}};sd.bridge={is_verify_success:!1,initPara:function(){var e={is_send:!0,white_list:[],is_mui:!1};"object"==typeof sd.para.app_js_bridge?sd.para.app_js_bridge=_.extend({},e,sd.para.app_js_bridge):!0===sd.para.use_app_track||!0===sd.para.app_js_bridge||"only"===sd.para.use_app_track?(!1!==sd.para.use_app_track_is_send&&"only"!==sd.para.use_app_track||(e.is_send=!1),sd.para.app_js_bridge=_.extend({},e)):"mui"===sd.para.use_app_track&&(e.is_mui=!0,sd.para.app_js_bridge=_.extend({},e)),!1===sd.para.app_js_bridge.is_send&&sd.log("设置了 is_send:false,如果打通失败，数据将被丢弃！")},initState:function(){function e(e){function t(e){var t={hostname:"",project:""};try{t.hostname=_.URL(e).hostname,t.project=_.URL(e).searchParams.get("project")||"default"}catch(e){sd.log(e)}return t}var r=t(e),e=t(sd.para.server_url);if(r.hostname===e.hostname&&r.project===e.project)return 1;if(0<sd.para.app_js_bridge.white_list.length)for(var s=0;s<sd.para.app_js_bridge.white_list.length;s++){var a=t(sd.para.app_js_bridge.white_list[s]);if(a.hostname===r.hostname&&a.project===r.project)return 1}}var t;_.isObject(sd.para.app_js_bridge)&&!sd.para.app_js_bridge.is_mui&&(window.webkit&&window.webkit.messageHandlers&&window.webkit.messageHandlers.sensorsdataNativeTracker&&_.isObject(window.SensorsData_iOS_JS_Bridge)&&window.SensorsData_iOS_JS_Bridge.sensorsdata_app_server_url?e(window.SensorsData_iOS_JS_Bridge.sensorsdata_app_server_url)&&(sd.bridge.is_verify_success=!0):_.isObject(window.SensorsData_APP_New_H5_Bridge)&&window.SensorsData_APP_New_H5_Bridge.sensorsdata_get_server_url&&window.SensorsData_APP_New_H5_Bridge.sensorsdata_track&&((t=window.SensorsData_APP_New_H5_Bridge.sensorsdata_get_server_url())&&e(t)&&(sd.bridge.is_verify_success=!0)))},initDefineBridgeInfo:function(){var e={touch_app_bridge:!0,verify_success:!1};return window.webkit&&window.webkit.messageHandlers&&window.webkit.messageHandlers.sensorsdataNativeTracker&&window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage&&_.isObject(window.SensorsData_iOS_JS_Bridge)&&window.SensorsData_iOS_JS_Bridge.sensorsdata_app_server_url||_.isObject(window.SensorsData_APP_New_H5_Bridge)&&window.SensorsData_APP_New_H5_Bridge.sensorsdata_get_server_url&&window.SensorsData_APP_New_H5_Bridge.sensorsdata_track?e.verify_success=sd.bridge.is_verify_success?"success":"fail":"object"==typeof SensorsData_APP_JS_Bridge&&(SensorsData_APP_JS_Bridge.sensorsdata_verify&&SensorsData_APP_JS_Bridge.sensorsdata_visual_verify||SensorsData_APP_JS_Bridge.sensorsdata_track)?!SensorsData_APP_JS_Bridge.sensorsdata_verify||!SensorsData_APP_JS_Bridge.sensorsdata_visual_verify||SensorsData_APP_JS_Bridge.sensorsdata_visual_verify(JSON.stringify({server_url:sd.para.server_url}))?e.verify_success="success":e.verify_success="fail":!/sensors-verify/.test(navigator.userAgent)&&!/sa-sdk-ios/.test(navigator.userAgent)||window.MSStream?e.touch_app_bridge=!1:sd.bridge.iOS_UA_bridge()?e.verify_success="success":e.verify_success="fail",e},iOS_UA_bridge:function(){if(/sensors-verify/.test(navigator.userAgent)){if((e=navigator.userAgent.match(/sensors-verify\/([^\s]+)/))&&e[0]&&"string"==typeof e[1]&&2===e[1].split("?").length){var e=e[1].split("?"),t=null,r=null;try{t=_.URL(sd.para.server_url).hostname,r=_.URL(sd.para.server_url).searchParams.get("project")||"default"}catch(e){sd.log(e)}return!(!t||t!==e[0]||!r||r!==e[1])}return!1}return/sa-sdk-ios/.test(navigator.userAgent)},dataSend:function(e,t,r){var s,a,i,n=e.data;_.isObject(sd.para.app_js_bridge)&&!sd.para.app_js_bridge.is_mui?window.webkit&&window.webkit.messageHandlers&&window.webkit.messageHandlers.sensorsdataNativeTracker&&window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage&&_.isObject(window.SensorsData_iOS_JS_Bridge)&&window.SensorsData_iOS_JS_Bridge.sensorsdata_app_server_url?sd.bridge.is_verify_success?(window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage(JSON.stringify({callType:"app_h5_track",data:_.extend({server_url:sd.para.server_url},n)})),"function"==typeof r&&r()):sd.para.app_js_bridge.is_send?(sd.debug.apph5({data:n,step:"4.1",output:"all"}),t.prepareServerUrl(e)):"function"==typeof r&&r():_.isObject(window.SensorsData_APP_New_H5_Bridge)&&window.SensorsData_APP_New_H5_Bridge.sensorsdata_get_server_url&&window.SensorsData_APP_New_H5_Bridge.sensorsdata_track?sd.bridge.is_verify_success?(SensorsData_APP_New_H5_Bridge.sensorsdata_track(JSON.stringify(_.extend({server_url:sd.para.server_url},n))),"function"==typeof r&&r()):sd.para.app_js_bridge.is_send?(sd.debug.apph5({data:n,step:"4.2",output:"all"}),t.prepareServerUrl(e)):"function"==typeof r&&r():"object"==typeof SensorsData_APP_JS_Bridge&&(SensorsData_APP_JS_Bridge.sensorsdata_verify||SensorsData_APP_JS_Bridge.sensorsdata_track)?SensorsData_APP_JS_Bridge.sensorsdata_verify?!SensorsData_APP_JS_Bridge.sensorsdata_verify(JSON.stringify(_.extend({server_url:sd.para.server_url},n)))&&sd.para.app_js_bridge.is_send?(sd.debug.apph5({data:n,step:"3.1",output:"all"}),t.prepareServerUrl(e)):"function"==typeof r&&r():(SensorsData_APP_JS_Bridge.sensorsdata_track(JSON.stringify(_.extend({server_url:sd.para.server_url},n))),"function"==typeof r&&r()):!/sensors-verify/.test(navigator.userAgent)&&!/sa-sdk-ios/.test(navigator.userAgent)||window.MSStream?_.isObject(sd.para.app_js_bridge)&&!0===sd.para.app_js_bridge.is_send?(sd.debug.apph5({data:n,step:"2",output:"all"}),t.prepareServerUrl(e)):"function"==typeof r&&r():(s=null,sd.bridge.iOS_UA_bridge()?(s=document.createElement("iframe"),i=n,i=(i=JSON.stringify(_.extend({server_url:sd.para.server_url},n))).replaceAll(/\r\n/g,""),a="sensorsanalytics://trackEvent?event="+(i=encodeURIComponent(i)),s.setAttribute("src",a),document.documentElement.appendChild(s),s.parentNode.removeChild(s),s=null,"function"==typeof r&&r()):sd.para.app_js_bridge.is_send?(sd.debug.apph5({data:n,step:"3.2",output:"all"}),t.prepareServerUrl(e)):"function"==typeof r&&r()):_.isObject(sd.para.app_js_bridge)&&sd.para.app_js_bridge.is_mui?_.isObject(window.plus)&&window.plus.SDAnalytics&&window.plus.SDAnalytics.trackH5Event?(window.plus.SDAnalytics.trackH5Event(data),"function"==typeof r&&r()):_.isObject(sd.para.app_js_bridge)&&!0===sd.para.app_js_bridge.is_send?t.prepareServerUrl(e):"function"==typeof r&&r():(sd.debug.apph5({data:n,step:"1",output:"code"}),t.prepareServerUrl(e))},app_js_bridge_v1:function(){var r=null,s=null;window.sensorsdata_app_js_bridge_call_js=function(e){r=e,_.isJSONString(r)&&(r=JSON.parse(r)),s&&(s(r),r=s=null)},sd.getAppStatus=function(e){var t;if(/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream&&((t=document.createElement("iframe")).setAttribute("src","sensorsanalytics://getAppInfo"),document.documentElement.appendChild(t),t.parentNode.removeChild(t),t=null),"object"==typeof window.SensorsData_APP_JS_Bridge&&window.SensorsData_APP_JS_Bridge.sensorsdata_call_app&&(r=SensorsData_APP_JS_Bridge.sensorsdata_call_app(),_.isJSONString(r)&&(r=JSON.parse(r))),!e)return r;null===r?s=e:(e(r),r=null)}},supportAppCallJs:function(){window.sensorsdata_app_call_js=function(e,t){e in window.sensorsdata_app_call_js.modules&&window.sensorsdata_app_call_js.modules[e](t)},window.sensorsdata_app_call_js.modules={}}},sd.JSBridge=function(e){this.list={},this.type=e.type,this.app_call_js=_.isFunction(e.app_call_js)?e.app_call_js:function(){},this.init()},sd.JSBridge.prototype.init=function(){var t=this;window.sensorsdata_app_call_js.modules[this.type]||(window.sensorsdata_app_call_js.modules[this.type]=function(e){t.app_call_js(e)})},sd.JSBridge.prototype.jsCallApp=function(e){e={callType:this.type,data:e};if(window.webkit&&window.webkit.messageHandlers&&window.webkit.messageHandlers.sensorsdataNativeTracker&&window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage)window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage(JSON.stringify(e));else{if(!_.isObject(window.SensorsData_APP_New_H5_Bridge)||!window.SensorsData_APP_New_H5_Bridge.sensorsdata_js_call_app)return sd.log("数据发往App失败，App没有暴露bridge"),!1;window.SensorsData_APP_New_H5_Bridge.sensorsdata_js_call_app(JSON.stringify(e))}},sd.JSBridge.prototype.hasAppBridge=function(){return window.webkit&&window.webkit.messageHandlers&&window.webkit.messageHandlers.sensorsdataNativeTracker&&window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage?"ios":_.isObject(window.SensorsData_APP_New_H5_Bridge)&&window.SensorsData_APP_New_H5_Bridge.sensorsdata_js_call_app?"android":(sd.log("App端bridge未暴露"),!1)},sd.JSBridge.prototype.requestToApp=function(e){var t=this,r=_.isObject(e.data)?e.data:{};_.isFunction(e.callback)||(e.callback=function(){}),_.isObject(e.timeout)&&_.isNumber(e.timeout.time)&&(_.isFunction(e.timeout.callback)||(e.timeout.callback=function(){}),e.timer=setTimeout(function(){e.timeout.callback(),delete t.list[s]},e.timeout.time));var s=(new Date).getTime().toString(16)+"-"+(""+_.getRandom()).replace(".","").slice(1,8);this.list[s]=e;r={callType:this.type,data:r};if(r.data.message_id=s,window.webkit&&window.webkit.messageHandlers&&window.webkit.messageHandlers.sensorsdataNativeTracker&&window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage)window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage(JSON.stringify(r));else{if(!_.isObject(window.SensorsData_APP_New_H5_Bridge)||!window.SensorsData_APP_New_H5_Bridge.sensorsdata_js_call_app)return sd.log("数据发往App失败，App没有暴露bridge"),!1;window.SensorsData_APP_New_H5_Bridge.sensorsdata_js_call_app(JSON.stringify(r))}},sd.JSBridge.prototype.double=function(e){var t;!e.message_id||(t=this.list[e.message_id])&&(t.timer&&clearTimeout(t.timer),t.callback(e),delete this.list[e.message_id])};var heatmap=sd.heatmap={otherTags:[],getTargetElement:function(e,t){var r=this,s=e;if("object"!=typeof s)return null;if("string"!=typeof s.tagName)return null;var a=s.tagName.toLowerCase();if("body"==a.toLowerCase()||"html"==a.toLowerCase())return null;if(!s||!s.parentNode||!s.parentNode.children)return null;var i=s.parentNode,n=r.hasElement(t.originalEvent||t),e=sd.para.heatmap.track_attr,t=r.otherTags;if("a"===a||"button"===a||"input"===a||"textarea"===a||_.hasAttributes(s,e))return s;if(~_.indexOf(t,a))return s;if("button"==i.tagName.toLowerCase()||"a"==i.tagName.toLowerCase()||_.hasAttributes(i,e))return i;if("area"===a&&"map"==i.tagName.toLowerCase()&&_.ry(i).prev().tagName&&"img"==_.ry(i).prev().tagName.toLowerCase())return _.ry(i).prev();if(n)return n;if("div"===a&&sd.para.heatmap.collect_tags.div&&r.isDivLevelValid(s))return 1<(sd.para.heatmap&&sd.para.heatmap.collect_tags&&sd.para.heatmap.collect_tags.div&&sd.para.heatmap.collect_tags.div.max_level||1)||r.isCollectableDiv(s)?s:null;if(r.isStyleTag(a)&&sd.para.heatmap.collect_tags.div){s=r.getCollectableParent(s);if(s&&r.isDivLevelValid(s))return s}return null},getDivLevels:function(e,t){var t=heatmap.getElementPath(e,!0,t).split(" > "),r=0;return _.each(t,function(e){"div"===e&&r++}),r},isDivLevelValid:function(e){for(var t=sd.para.heatmap&&sd.para.heatmap.collect_tags&&sd.para.heatmap.collect_tags.div&&sd.para.heatmap.collect_tags.div.max_level||1,r=e.getElementsByTagName("div"),s=r.length-1;0<=s;s--)if(heatmap.getDivLevels(r[s],e)>t)return!1;return!0},getElementPath:function(e,t,r){for(var s=[];e.parentNode;){if(e.id&&!t&&/^[A-Za-z][-A-Za-z0-9_:.]*$/.test(e.id)){s.unshift(e.tagName.toLowerCase()+"#"+e.id);break}if(r&&e===r){s.unshift(e.tagName.toLowerCase());break}if(e===document.body){s.unshift("body");break}s.unshift(e.tagName.toLowerCase()),e=e.parentNode}return s.join(" > ")},getClosestLi:function(e){return function(e,t){for(;e&&e!==document&&1===e.nodeType;e=e.parentNode)if(e.tagName.toLowerCase()===t)return e;return null}(e,"li")},getElementPosition:function(e,t,r){var s=sd.heatmap.getClosestLi(e);if(!s)return null;var a=e.tagName.toLowerCase(),i=s.getElementsByTagName(a),n=i.length,o=[];if(1<n){for(var d=0;d<n;d++)sd.heatmap.getElementPath(i[d],r)===t&&o.push(i[d]);if(1<o.length)return _.indexOf(o,e)}return function(e){if(e.tagName.toLowerCase(),!e.parentNode)return"";if(1===_.ry(e).getSameTypeSiblings().length)return 0;for(var t=0,r=e;_.ry(r).previousElementSibling().ele;r=_.ry(r).previousElementSibling().ele,t++);return t}(s)},setNotice:function(e){sd.is_heatmap_render_mode=!0,sd.para.heatmap||(sd.errorMsg="您SDK没有配置开启点击图，可能没有数据！"),e&&e[0]&&e[1]&&"http:"===e[1].slice(0,5)&&"https:"===location.protocol&&(sd.errorMsg="您的当前页面是https的地址，神策分析环境也必须是https！"),sd.para.heatmap_url||(sd.para.heatmap_url=location.protocol+"//static.sensorsdata.cn/sdk/"+sd.lib_version+"/heatmap.min.js")},getDomIndex:function(e){if(!e.parentNode)return-1;for(var t=0,r=e.tagName,s=e.parentNode.children,a=0;a<s.length;a++)if(s[a].tagName===r){if(e===s[a])return t;t++}return-1},selector:function(e,t){var r=e.parentNode&&9==e.parentNode.nodeType?-1:this.getDomIndex(e);return e.getAttribute&&e.getAttribute("id")&&/^[A-Za-z][-A-Za-z0-9_:.]*$/.test(e.getAttribute("id"))&&(!sd.para.heatmap||sd.para.heatmap&&"not_use_id"!==sd.para.heatmap.element_selector)&&!t?"#"+e.getAttribute("id"):e.tagName.toLowerCase()+(~r?":nth-of-type("+(r+1)+")":"")},getDomSelector:function(e,t,r){if(!e||!e.parentNode||!e.parentNode.children)return!1;t=t&&t.join?t:[];var s=e.nodeName.toLowerCase();return e&&"body"!=s&&1==e.nodeType?(t.unshift(this.selector(e,r)),e.getAttribute&&e.getAttribute("id")&&/^[A-Za-z][-A-Za-z0-9_:.]*$/.test(e.getAttribute("id"))&&sd.para.heatmap&&"not_use_id"!==sd.para.heatmap.element_selector&&!r?t.join(" > "):this.getDomSelector(e.parentNode,t,r)):(t.unshift("body"),t.join(" > "))},na:function(){var e=document.documentElement.scrollLeft||window.pageXOffset;return parseInt(isNaN(e)?0:e,10)},i:function(){var t=0;try{t=o.documentElement&&o.documentElement.scrollTop||m.pageYOffset,t=isNaN(t)?0:t}catch(e){t=0}return parseInt(t,10)},getBrowserWidth:function(){var e=window.innerWidth||document.body.clientWidth;return isNaN(e)?0:parseInt(e,10)},getBrowserHeight:function(){var e=window.innerHeight||document.body.clientHeight;return isNaN(e)?0:parseInt(e,10)},getScrollWidth:function(){var e=parseInt(document.body.scrollWidth,10);return isNaN(e)?0:e},W:function(e){var t=parseInt(+e.clientX+ +this.na(),10),e=parseInt(+e.clientY+ +this.i(),10);return{x:isNaN(t)?0:t,y:isNaN(e)?0:e}},getEleDetail:function(e){var t=this.getDomSelector(e),r=_.getEleInfo({target:e});r.$element_selector=t||"",r.$element_path=sd.heatmap.getElementPath(e,sd.para.heatmap&&"not_use_id"===sd.para.heatmap.element_selector);e=sd.heatmap.getElementPosition(e,r.$element_path,sd.para.heatmap&&"not_use_id"===sd.para.heatmap.element_selector);return _.isNumber(e)&&(r.$element_position=e),r},start:function(e,t,r,s,a){var i=_.isObject(s)?s:{},n=_.isFunction(a)?a:_.isFunction(s)?s:void 0;if(sd.para.heatmap&&sd.para.heatmap.collect_element&&!sd.para.heatmap.collect_element(t))return!1;a=this.getEleDetail(t);sd.para.heatmap&&sd.para.heatmap.custom_property&&(s=sd.para.heatmap.custom_property(t),_.isObject(s)&&(a=_.extend(a,s))),a=_.extend(a,i),"a"===r&&sd.para.heatmap&&!0===sd.para.heatmap.isTrackLink?_.trackLink({event:e,target:t},"$WebClick",a):sd.track("$WebClick",a,n)},hasElement:function(e){var t=e._getPath?e._getPath():heatmap.getElementPath(e.target,!0).split(" > ");if(_.isArray(t)&&0<t.length)for(var r=0;r<t.length;r++)if(t[r]&&t[r].tagName&&"a"==t[r].tagName.toLowerCase())return t[r];return!1},isStyleTag:function(e,t){return!~_.indexOf(["a","div","input","button","textarea"],e)&&(!t||sd.para.heatmap&&sd.para.heatmap.collect_tags&&sd.para.heatmap.collect_tags.div?!!(_.isObject(sd.para.heatmap)&&_.isObject(sd.para.heatmap.collect_tags)&&_.isObject(sd.para.heatmap.collect_tags.div)&&~_.indexOf(sd.para.heatmap.collect_tags.div.ignore_tags,e)):!!~_.indexOf(["mark","/mark","strong","b","em","i","u","abbr","ins","del","s","sup"],e))},isCollectableDiv:function(e,t){try{if(0===e.children.length)return!0;for(var r=0;r<e.children.length;r++)if(1===e.children[r].nodeType){var s=e.children[r].tagName.toLowerCase();if(!("div"==s&&1<(sd.para&&sd.para.heatmap&&sd.para.heatmap.collect_tags&&sd.para.heatmap.collect_tags.div&&sd.para.heatmap.collect_tags.div.max_level)||this.isStyleTag(s,t)))return!1;if(!this.isCollectableDiv(e.children[r],t))return!1}return!0}catch(e){sd.log(e)}return!1},getCollectableParent:function(e,t){try{var r=e.parentNode,s=r?r.tagName.toLowerCase():"";if("body"==s)return!1;if(s&&"div"==s&&(1<(sd.para&&sd.para.heatmap&&sd.para.heatmap.collect_tags&&sd.para.heatmap.collect_tags.div&&sd.para.heatmap.collect_tags.div.max_level)||this.isCollectableDiv(r,t)))return r;if(r&&this.isStyleTag(s,t))return this.getCollectableParent(r,t)}catch(e){sd.log(e)}return!1},initScrollmap:function(){if(!_.isObject(sd.para.heatmap)||"default"!==sd.para.heatmap.scroll_notice_map)return!1;function e(){return!(sd.para.scrollmap&&_.isFunction(sd.para.scrollmap.collect_url)&&!sd.para.scrollmap.collect_url())}var t,r,s=(t={timeout:1e3,func:function(e,t){var r=document.documentElement&&document.documentElement.scrollTop||window.pageYOffset||document.body.scrollTop||0,s=new Date,a=s-this.current_time;(sd.para.heatmap.scroll_delay_time<a&&r-e.$viewport_position!=0||t)&&(e.$url=_.isDecodeURI(sd.para.url_is_decode,location.href),e.$title=document.title,e.$url_path=location.pathname,e.event_duration=Math.min(sd.para.heatmap.scroll_event_duration,parseInt(a)/1e3),sd.track("$WebStay",e)),this.current_time=s}},(r={}).timeout=t.timeout||1e3,r.func=t.func,r.hasInit=!1,r.inter=null,r.main=function(e,t){this.func(e,t),this.inter=null},r.go=function(e){var t={};this.inter||(t.$viewport_position=document.documentElement&&document.documentElement.scrollTop||window.pageYOffset||document.body.scrollTop||0,t.$viewport_position=Math.round(t.$viewport_position)||0,t.$viewport_height=window.innerHeight||document.documentElement.clientHeight||document.body.clientHeight||0,t.$viewport_width=window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth||0,e?r.main(t,!0):this.inter=setTimeout(function(){r.main(t)},this.timeout))},r);s.current_time=new Date,_.addEvent(window,"scroll",function(){return!!e()&&void s.go()}),_.addEvent(window,"unload",function(){return!!e()&&void s.go("notime")})},initHeatmap:function(){var a=this;return!(!_.isObject(sd.para.heatmap)||"default"!==sd.para.heatmap.clickmap)&&(!(_.isFunction(sd.para.heatmap.collect_url)&&!sd.para.heatmap.collect_url())&&(sd.para.heatmap.collect_elements="all"===sd.para.heatmap.collect_elements?"all":"interact",void _.addEvent(document,"click","all"===sd.para.heatmap.collect_elements?function(e){var t=e||window.event;if(!t)return!1;var r=t.target||t.srcElement;if("object"!=typeof r)return!1;if("string"!=typeof r.tagName)return!1;var s=r.tagName.toLowerCase();if("body"==s||"html"==s)return!1;if(!r||!r.parentNode||!r.parentNode.children)return!1;e=r.parentNode.tagName.toLowerCase();"a"==e||"button"==e?a.start(t,r.parentNode,e):a.start(t,r,s)}:function(e){var t=e||window.event;if(!t)return!1;var r=t.target||t.srcElement,e=sd.heatmap.getTargetElement(r,e);e?a.start(t,e,e.tagName.toLowerCase()):_.isElement(r)&&"div"==r.tagName.toLowerCase()&&_.isObject(sd.para.heatmap)&&sd.para.heatmap.get_vtrack_config&&0<sd.unlimitedDiv.events.length&&sd.unlimitedDiv.isTargetEle(r)&&a.start(t,r,r.tagName.toLowerCase(),{$lib_method:"vtrack"})})))}};sd.unlimitedDiv={events:[],init:function(){this.filterWebClickEvents()},filterWebClickEvents:function(e){this.events=sd.vtrackcollect.getAssignConfigs(function(e){return!(!_.isObject(e)||!0!==e.event.unlimited_div||"webclick"!==e.event_type)},e)},isTargetEle:function(e){var t=sd.heatmap.getEleDetail(e);if(!_.isObject(t)||!_.isString(t.$element_path))return!1;for(var r=0;r<this.events.length;r++)if(_.isObject(this.events[r])&&_.isObject(this.events[r].event)&&sd.vtrackcollect.configIsMatch(t,this.events[r].event))return!0;return!1}},sd.customProp={events:[],configSwitch:!1,collectAble:function(){return this.configSwitch&&_.isObject(sd.para.heatmap)&&sd.para.heatmap.get_vtrack_config},updateEvents:function(){this.events=sd.vtrackcollect.getAssignConfigs(function(e){return!!(_.isObject(e)&&_.isArray(e.properties)&&0<e.properties.length)}),this.configSwitch=!!this.events.length},getVtrackProps:function(e){var t={};return this.collectAble()?"$WebClick"===e.event?this.clickCustomPropMaker(e,this.events):t:{}},clickCustomPropMaker:function(t,e,r){var s=this,r=r||this.filterConfig(t,e,sd.vtrackcollect.url_info.page_url),a={};return r.length?(_.each(r,function(e){_.isArray(e.properties)&&0<e.properties.length&&_.each(e.properties,function(e){e=s.getProp(e,t);_.isObject(e)&&_.extend(a,e)})}),a):{}},getProp:function(t,e){if(!_.isObject(t))return!1;if(!_.isString(t.name)||t.name.length<=0)return sd.log("----vtrackcustom----属性名不合法,属性抛弃",t.name),!1;var r,s,a={};if("content"!==t.method)return sd.log("----vtrackcustom----属性不支持此获取方式",t.name,t.method),!1;if(_.isString(t.element_selector)&&0<t.element_selector.length)n=_.getDomBySelector(t.element_selector);else{if(!_.isString(t.list_selector))return sd.log("----vtrackcustom----属性配置异常，属性抛弃",t.name),!1;var i=_.getDomBySelector(e.properties.$element_selector);if(!i)return sd.log("----vtrackcustom----点击元素获取异常，属性抛弃",t.name),!1;var i=sd.heatmap.getClosestLi(i),n=this.getPropElInLi(i,t.list_selector)}if(!n||!_.isElement(n))return sd.log("----vtrackcustom----属性元素获取失败，属性抛弃",t.name),!1;if("input"==n.tagName.toLowerCase()?r=n.value||"":"select"==n.tagName.toLowerCase()?_.isNumber(i=n.selectedIndex)&&_.isElement(n[i])&&(r=sd._.getElementContent(n[i],"select")):r=_.getElementContent(n,n.tagName.toLowerCase()),t.regular){try{s=RegExp(t.regular).exec(r)}catch(e){return sd.log("----vtrackcustom----正则处理失败，属性抛弃",t.name),!1}if(null===s)return sd.log("----vtrackcustom----属性规则处理，未匹配到结果,属性抛弃",t.name),!1;if(!_.isArray(s)||!_.isString(s[0]))return sd.log("----vtrackcustom----正则处理异常，属性抛弃",t.name,s),!1;r=s[0]}if("STRING"===t.type)a[t.name]=r;else if("NUMBER"===t.type){if(r.length<1)return sd.log("----vtrackcustom----未获取到数字内容，属性抛弃",t.name,r),!1;if(isNaN(+(""+r)))return sd.log("----vtrackcustom----数字类型属性转换失败，属性抛弃",t.name,r),!1;a[t.name]=+(""+r)}return a},getPropElInLi:function(e,t){if(!(e&&_.isElement(e)&&_.isString(t)))return null;if("li"!=e.tagName.toLowerCase())return null;var r=sd.heatmap.getDomSelector(e);if(r){e=_.getDomBySelector(r+t);return e||null}return sd.log("----vtrackcustom---获取同级属性元素失败，selector信息异常",r,t),null},filterConfig:function(r,e,s){var a=[];if(!s){var t=sd.vtrackcollect.initUrl();if(!t)return[];s=t.page_url}return"$WebClick"===r.event&&_.each(e,function(e,t){_.isObject(e)&&"webclick"===e.event_type&&_.isObject(e.event)&&e.event.url_host===s.host&&e.event.url_path===s.pathname&&sd.vtrackcollect.configIsMatch(r.properties,e.event)&&a.push(e)}),a}},sd.vtrackcollect={unlimitedDiv:sd.unlimitedDiv,config:{},storageEnable:!0,storage_name:"webjssdkvtrackcollect",para:{session_time:18e5,timeout:5e3,update_interval:18e5},url_info:{},timer:null,update_time:null,customProp:sd.customProp,initUrl:function(){var e,t,r,s={server_url:{project:"",host:""},page_url:{host:"",pathname:""},api_url:""};if(!_.isString(sd.para.server_url))return sd.log("----vtrackcollect---server_url必须为字符串"),!1;try{e=_.URL(sd.para.server_url),s.server_url.project=e.searchParams.get("project")||"default",s.server_url.host=e.host}catch(e){return sd.log("----vtrackcollect---server_url解析异常",e),!1}try{t=_.URL(location.href),s.page_url.host=t.hostname,s.page_url.pathname=t.pathname}catch(e){return sd.log("----vtrackcollect---页面地址解析异常",e),!1}try{(r=new _.urlParse(sd.para.server_url))._values.Path="/config/visualized/Web.conf",s.api_url=r.getUrl()}catch(e){return sd.log("----vtrackcollect---API地址解析异常",e),!1}return this.url_info=s},init:function(){return _.isObject(sd.para.heatmap)&&sd.para.heatmap.get_vtrack_config?(_.localStorage.isSupport()||(this.storageEnable=!1),this.initUrl()?(this.storageEnable?(e=_.localStorage.parse(this.storage_name),_.isObject(e)&&_.isObject(e.data)&&this.serverUrlIsSame(e.serverUrl)?(this.config=e.data,this.update_time=e.updateTime,this.updateConfig(e.data),e=(new Date).getTime()-this.update_time,_.isNumber(e)&&0<e&&e<this.para.session_time?this.setNextFetch(this.para.update_interval-e):this.getConfigFromServer()):this.getConfigFromServer()):this.getConfigFromServer(),void this.pageStateListenner()):(sd.log("----vtrackcustom----初始化失败，url信息解析失败"),!1)):(sd.log("----vtrackcustom----初始化失败，get_vtrack_config开关未开启"),!1);var e},serverUrlIsSame:function(e){return!!_.isObject(e)&&(e.host===this.url_info.server_url.host&&e.project===this.url_info.server_url.project)},getConfigFromServer:function(){var s=this;this.sendRequest(function(e,t){s.update_time=(new Date).getTime();var r={};200===e?t&&_.isObject(t)&&"Web"===t.os&&s.updateConfig(r=t):205===e?s.updateConfig(r):304===e?r=s.config:(sd.log("----vtrackcustom----数据异常",e),s.updateConfig(r)),s.updateStorage(r),s.setNextFetch()},function(e){s.update_time=(new Date).getTime(),sd.log("----vtrackcustom----配置拉取失败",e),s.setNextFetch()})},setNextFetch:function(e){var t=this;this.timer&&(clearTimeout(this.timer),this.timer=null),this.timer=setTimeout(function(){t.getConfigFromServer()},e=e||this.para.update_interval)},pageStateListenner:function(){var t=this;_.listenPageState({visible:function(){var e=(new Date).getTime()-t.update_time;_.isNumber(e)&&0<e&&e<t.para.update_interval?t.setNextFetch(t.para.update_interval-e):t.getConfigFromServer()},hidden:function(){t.timer&&(clearTimeout(t.timer),t.timer=null)}})},updateConfig:function(e){if(!_.isObject(e))return!1;this.config=e,this.customProp.updateEvents(),this.unlimitedDiv.init(e)},updateStorage:function(e){if(!this.storageEnable)return!1;if(!_.isObject(e))return!1;if(this.url_info.server_url)r=this.url_info.server_url;else{var t=sd.vtrackcollect.initUrl();if(!t)return!1;r=t.server_url}var r={updateTime:(new Date).getTime(),data:e,serverUrl:r};_.localStorage.set(this.storage_name,JSON.stringify(r))},sendRequest:function(r,t){var e={app_id:this.url_info.page_url.host};this.config.version&&(e.v=this.config.version),_.jsonp({url:this.url_info.api_url,callbackName:"saJSSDKVtrackCollectConfig",data:e,timeout:this.para.timeout,success:function(e,t){r(e,t)},error:function(e){t(e)}})},getAssignConfigs:function(t,e){if(!this.url_info.server_url&&!this.initUrl())return[];var r=this,s=[];return(e=e||this.config).events=e.events||e.eventList,_.isObject(e)&&_.isArray(e.events)&&0<e.events.length?(_.each(e.events,function(e){_.isObject(e)&&_.isObject(e.event)&&e.event.url_host===r.url_info.page_url.host&&e.event.url_path===r.url_info.page_url.pathname&&t(e)&&s.push(e)}),s):[]},isDiv:function(e){if(e.element_path){e=e.element_path.split(">");if("div"!==_.trim(e.pop()).slice(0,3))return!1}return!0},configIsMatch:function(e,t){if(!t.element_path)return!1;if(t.limit_element_content&&t.element_content!==e.$element_content)return!1;if(t.limit_element_position&&t.element_position!==""+e.$element_position)return!1;if(void 0!==e.$element_position){if(t.element_path!==e.$element_path)return!1}else if(sd.vtrackcollect.isDiv({element_path:t.element_path})){if(!~e.$element_path.indexOf(t.element_path))return!1}else if(t.element_path!==e.$element_path)return!1;return!0}},sd.init=function(e){if(sd.readyState&&sd.readyState.state&&2<=sd.readyState.state)return!1;sd.setInitVar(),sd.readyState.setState(2),sd.initPara(e),sd.bridge.supportAppCallJs(),sd.detectMode(),sd._.isIOS()&&sd._.getIOSVersion()&&sd._.getIOSVersion()<13&&(sd.para.heatmap&&sd.para.heatmap.collect_tags&&sd.para.heatmap.collect_tags.div&&sd._.setCssStyle("div, [data-sensors-click] { cursor: pointer; -webkit-tap-highlight-color: rgba(0,0,0,0); }"),sd.para.heatmap&&sd.para.heatmap.track_attr&&sd._.setCssStyle("["+sd.para.heatmap.track_attr.join("], [")+"] { cursor: pointer; -webkit-tap-highlight-color: rgba(0,0,0,0); }"))};var methods=["getAppStatus","track","quick","register","registerPage","registerOnce","trackSignup","setProfile","setOnceProfile","appendProfile","incrementProfile","deleteProfile","unsetProfile","identify","login","logout","trackLink","clearAllRegister","clearPageRegister"];_.each(methods,function(e){var t=sd[e];sd[e]=function(){if(sd.readyState.state<3)return _.isArray(sd._q)||(sd._q=[]),sd._q.push([e,arguments]),!1;if(sd.readyState.getState())return t.apply(sd,arguments);try{console.error("请先初始化神策JS SDK")}catch(e){sd.log(e)}}}),"string"==typeof window.sensorsDataAnalytic201505?(sd.setPreConfig(window[sensorsDataAnalytic201505]),window[sensorsDataAnalytic201505]=sd,(window.sensorsDataAnalytic201505=sd).init()):void 0===window.sensorsDataAnalytic201505?window.sensorsDataAnalytic201505=sd:sd=window.sensorsDataAnalytic201505;var sd$1=sd;export{sd$1 as default};
