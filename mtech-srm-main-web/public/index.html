<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
    <!-- <link href="themes/dark.css" rel="stylesheet"> -->
    <!-- <link href="material.css" rel="stylesheet" type="text/css" /> -->
    <script>
      const lastTitle = sessionStorage.getItem('lastDocumentTitle')
      resTitle = lastTitle ? lastTitle : 'SRM'
      document.title = resTitle
    </script>
    <link
      rel="stylesheet"
      type="text/css"
      href="themes/default.css"
      data-mt-theme="default"
    />
    <title>SRM</title>
    <div id="firstpage-spinner">
      <div class="rect1"></div>
      <div class="rect2"></div>
      <div class="rect3"></div>
      <div class="rect4"></div>
      <div class="rect5"></div>
    </div>
    <style>
      #firstpage-spinner {
        margin: 100px auto;
        width: 80px;
        height: 60px;
        text-align: center;
        font-size: 10px;
      }

      #firstpage-spinner > div {
        background-color: #00469c;
        height: 100%;
        width: 6px;
        margin-left: 4px;
        display: inline-block;
        -webkit-animation: stretchDelay 1.2s infinite ease-in-out;
        animation: stretchDelay 1.2s infinite ease-in-out;
      }

      #firstpage-spinner .rect2 {
        -webkit-animation-delay: -1.1s;
        animation-delay: -1.1s;
      }

      #firstpage-spinner .rect3 {
        -webkit-animation-delay: -1s;
        animation-delay: -1s;
      }

      #firstpage-spinner .rect4 {
        -webkit-animation-delay: -0.9s;
        animation-delay: -0.9s;
      }

      #firstpage-spinner .rect5 {
        -webkit-animation-delay: -0.8s;
        animation-delay: -0.8s;
      }

      @-webkit-keyframes stretchDelay {
        0%,
        40%,
        100% {
          -webkit-transform: scaleY(0.4);
        }
        20% {
          -webkit-transform: scaleY(1);
        }
      }

      @keyframes stretchDelay {
        0%,
        40%,
        100% {
          transform: scaleY(0.4);
          -webkit-transform: scaleY(0.4);
        }
        20% {
          transform: scaleY(1);
          -webkit-transform: scaleY(1);
        }
      }
    </style>
    <!-- <script src="vue.min.js" type="text/javascript"></script> -->
    <!-- <script src="ej2-vue.min.js" type="text/javascript"></script> -->
  </head>

  <body>
    <noscript>
      <strong
        >We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work
        properly without JavaScript enabled. Please enable it to
        continue.</strong
      >
    </noscript>

    <div id="app">
    </div>
    <!-- built files will be auto injected -->
  </body>
</html>
