## srm-sourcing寻源项目目录结构
### 仓库分支：develop
### 整理时间：2021-11-02
```bash
mtech-srm-sourcing-web                                //
├─ .editorconfig                                      //vscode EditorConfig插件
├─ .eslintignore                                      //eslintignore文件
├─ .eslintrc.js                                       //eslintrc配置文件
├─ .npmrc                                             //npm私服仓库配置
├─ babel.config.js                                    //babel.config
├─ package-lock.json                                  //package-lock
├─ package.json                                       //package
├─ README.md                                          //README
├─ src                                                //
│  ├─ apis                                            //
│  │  ├─ index.js                                     //API入口文件(已配置递归读取modules文件夹)
│  │  └─ modules                                      //
│  │     ├─ purchase                                  //采方
│  │     │  ├─ common                                 //
│  │     │  │  └─ index.js                            //采方-公用
│  │     │  ├─ fixedPoint                             //
│  │     │  │  └─ index.js                            //采方-定点管理
│  │     │  ├─ por                                    //采方-寻源需求管理
│  │     │  │  ├─ porHeader.js                        //采方-寻源需求管理-寻源需求汇总
│  │     │  │  └─ porMine.js                          //采方-寻源需求管理-我的需求管理
│  │     │  ├─ price                                  //
│  │     │  │  └─ index.js                            //采方-价格服务
│  │     │  ├─ rfx                                    //采方-询比价管理（询价大厅）
│  │     │  │  ├─ inquiryBidding.js                   //采方-询比价管理-询价招标
│  │     │  │  ├─ rfxDetail.js                        //采方-询比价管理-详情相关
│  │     │  │  ├─ rfxExt.js                           //采方-询比价管理-其他
│  │     │  │  ├─ rfxFiles.js                         //采方-询比价管理-文件相关
│  │     │  │  ├─ rfxList.js                          //采方-询比价管理-列表页
│  │     │  │  ├─ rfxRequireDetail.js                 //采方-询比价管理-采购明细
│  │     │  │  ├─ rfxSchedule.js                      //采方-询比价管理-任务计划
│  │     │  │  ├─ rfxSupplier.js                      //采方-询比价管理-候选供方
│  │     │  │  └─ tabComparePrice.js                  //采方-询比价管理-比价
│  │     │  └─ settings                               //采方-配置相关
│  │     │     ├─ approvalConfig.js                   //采方-审批流配置
│  │     │     ├─ businessConfig.js                   //采方-业务类型字段配置
│  │     │     ├─ moduleConfig.js                     //采方-模型配置
│  │     │     ├─ scheduleConfig.js                   //采方-任务计划相关配置
│  │     │     └─ strategyConfig.js                   //采方-寻源策略相关(策略地图)
│  │     ├─ service                                   //公共服务API
│  │     │  ├─ file                                   //
│  │     │  │  └─ index.js                            //文件相关
│  │     │  ├─ formDesign                             //
│  │     │  │  └─ index.js                            //自定义表单相关
│  │     │  ├─ masterData                             //
│  │     │  │  └─ index.js                            //主数据相关
│  │     │  └─ workFlow                               //
│  │     │     └─ index.js                            //工作流相关
│  │     └─ supply                                    //供方
│  │        └─ quotationBidding                       //供方-客户寻源管理-报价投标管理
│  │           ├─ index.js                            //供方-客户寻源管理-报价投标管理-列表
│  │           └─ supQBFiles.js                       //供方-客户寻源管理-报价投标管理-文件相关
│  ├─ App.vue                                         //
│  ├─ assets                                          //
│  │  ├─ images                                       //
│  │  │  ├─ biding-open-allowed.png                   //采方-询比价管理-询价招标-允许开标
│  │  │  ├─ biding-open-not-allowed.png               //采方-询比价管理-询价招标-不允许开标
│  │  │  └─ evdetailbanner.jpg                        //供方-客户寻源管理-报价投标管理-报价Tab页-头部文件
│  │  └─ logo.png                                     //
│  ├─ components                                      //
│  │  ├─ Dialog                                       //公用Dialog组件
│  │  │  ├─ base.vue                                  //
│  │  │  ├─ index.js                                  //
│  │  │  └─ modals                                    //
│  │  │     └─ small-dialog.vue                       //
│  │  ├─ micro-loading                                //loading组件
│  │  │  └─ index.vue                                 //
│  │  ├─ SourcingProject                              //采方-询比价管理-询价大厅需要的组件
│  │  │  ├─ panelTitle.vue                            //面板title
│  │  │  ├─ selectRound.vue                           //切换当前轮次
│  │  │  └─ timerClock.vue                            //倒计时
│  │  ├─ StrategyFlow                                 //采方-策略地图配置-组件抽离(工作流)
│  │  │  ├─ config                                    //
│  │  │  │  └─ index.js                               //
│  │  │  ├─ index.vue                                 //
│  │  │  └─ SimpleRule.vue                            //
│  │  ├─ SvgIcon                                      //公用-svg-icon
│  │  │  └─ index.vue                                 //
│  │  ├─ Toast                                        //公用-信息提示Toast
│  │  │  ├─ base-ejs.vue                              //
│  │  │  ├─ base.vue                                  //
│  │  │  └─ index.js                                  //
│  │  ├─ Tooltip                                      //公用-信息提示Tooltip
│  │  │  ├─ base.vue                                  //
│  │  │  └─ index.js                                  //
│  │  └─ Upload                                       //公用-上传文件弹框(临时解决方案)
│  │     └─ index.vue                                 //
│  ├─ config                                          //
│  │  └─ proxy.config.js                              //代理配置
│  ├─ icons                                           //
│  │  ├─ index.js                                     //
│  │  ├─ svg                                          //
│  │  │  ├─ icon_rank_1.svg                           //采方-询比价管理-询价大厅-排名需要的icon
│  │  │  ├─ icon_rank_2.svg                           //采方-询比价管理-询价大厅-排名需要的icon
│  │  │  └─ icon_rank_3.svg                           //采方-询比价管理-询价大厅-排名需要的icon
│  │  └─ svgo.yml                                     //
│  ├─ main.js                                         //入口文件
│  ├─ mtechUi.js                                      //
│  ├─ router                                          //
│  │  ├─ index.js                                     //路由入口文件(已配置递归读取modules文件夹)
│  │  └─ modules                                      //
│  │     ├─ common                                    //公用配置-不区分采方、供方的路由
│  │     │  └─ settings                               //
│  │     │     └─ business.js                         //
│  │     ├─ purchase                                  //采方路由
│  │     │  ├─ coastModel                             //采方-成本模型(静态页面)
│  │     │  │  └─ index.js                            //
│  │     │  ├─ costCollocation                        //采方-成本配置(静态页面)
│  │     │  │  └─ index.js                            //
│  │     │  ├─ expert                                 //采方-专家管理(静态页面)
│  │     │  │  └─ index.js                            //
│  │     │  ├─ fixedPoint                             //采方-定点管理
│  │     │  │  └─ index.js                            //
│  │     │  ├─ price                                  //采方-价格服务
│  │     │  │  └─ index.js                            //
│  │     │  ├─ rfx                                    //采方-询比价管理（询价大厅）
│  │     │  │  └─ index.js                            //
│  │     │  ├─ settings                               //采方-配置类路由
│  │     │  │  ├─ rfx.js                              //采方-配置类路由-rfx相关
│  │     │  │  └─ sor.js                              //采方-配置类路由-sor相关
│  │     │  └─ sor                                    //采方-寻源需求管理
│  │     │     └─ index.js                            //
│  │     └─ supply                                    //供方路由
│  │        └─ quotationBidding                       //供方-客户寻源管理-报价投标管理
│  │           └─ index.js                            //
│  ├─ store                                           //vuex相关配置
│  │  ├─ actions                                      //
│  │  │  └─ index.js                                  //
│  │  ├─ getters                                      //
│  │  │  └─ index.js                                  //
│  │  ├─ index.js                                     //
│  │  ├─ mutations                                    //
│  │  │  └─ index.js                                  //
│  │  └─ state                                        //
│  │     └─ index.js                                  //
│  ├─ styles                                          //样式文件(暂未整理太多公用样式)
│  │  ├─ index.scss                                   //样式文件(目前都是各个页面单独处理)
│  │  ├─ pages                                        //
│  │  │  ├─ _dialog.scss                              //
│  │  │  └─ _pages.scss                               //
│  │  ├─ _base.scss                                   //
│  │  └─ _reset.scss                                  //
│  ├─ utils                                           //工具类文件
│  │  ├─ bus.js                                       //
│  │  ├─ httpClient.js                                //【弃用】集成的axios，现在使用@mtech-common/http
│  │  └─ utils.js                                     //公共方法
│  └─ views                                           //视图层-文件目录
│     ├─ common                                       //common 公共目录，不区分采方、供方
│     │  └─ settings                                  //
│     │     └─ business                               //公共：业务类型字段配置
│     │        ├─ detail                              //公共：业务类型字段配置-详情页
│     │        │  ├─ index.vue                        //详情页-入口文件
│     │        │  └─ tabs                             //
│     │        │     ├─ rfxStepsConfig                //详情页-RFX阶段配置
│     │        │     │  ├─ config                     //
│     │        │     │  │  └─ index.js                //
│     │        │     │  └─ index.vue                  //
│     │        │     ├─ sourceConfig                  //详情页-寻源需求配置
│     │        │     │  ├─ components                 //
│     │        │     │  │  └─ addTagDialog.vue        //【弹框】自定义添加表单-表单名
│     │        │     │  ├─ config                     //
│     │        │     │  │  └─ index.js                //
│     │        │     │  └─ index.vue                  //
│     │        │     └─ supplierOfferConfig           //详情页-供应商报价单
│     │        │        ├─ config                     //
│     │        │        │  └─ index.js                //
│     │        │        └─ index.vue                  //
│     │        └─ list                                //公共：业务类型字段配置-列表页
│     │           ├─ config                           //
│     │           │  └─ index.js                      //
│     │           └─ index.vue                        //
│     ├─ purchase                                     //purchase-采方
│     │  ├─ coastModel                                //采方：成本模型(静态页面)
│     │  │  ├─ config                                 //
│     │  │  │  └─ index.js                            //
│     │  │  ├─ index.vue                              //
│     │  │  └─ treeViewGrid.vue                       //
│     │  ├─ costCollocation                           //采方：成本配置(静态页面)
│     │  │  ├─ components                             //
│     │  │  │  ├─ costItem.vue                        //
│     │  │  │  └─ relatedMaterials.vue                //
│     │  │  ├─ config                                 //
│     │  │  │  └─ index.js                            //
│     │  │  ├─ detail.vue                             //
│     │  │  └─ index.vue                              //
│     │  ├─ expert                                    //采方：专家管理(静态页面)
│     │  │  ├─ config                                 //
│     │  │  │  └─ index.js                            //
│     │  │  ├─ detail                                 //
│     │  │  │  ├─ config                              //
│     │  │  │  │  └─ index.js                         //
│     │  │  │  └─ index.vue                           //
│     │  │  └─ index.vue                              //
│     │  ├─ fixedPoint                                //采方：定点管理
│     │  │  ├─ detail                                 //采方：定点管理-定点详情
│     │  │  │  ├─ config                              //
│     │  │  │  │  └─ index.js                         //
│     │  │  │  ├─ edit.vue                            //定点详情-编辑
│     │  │  │  └─ index.vue                           //定点详情-新建
│     │  │  ├─ list                                   //采方：定点管理-定点管理
│     │  │  │  ├─ components                          //
│     │  │  │  │  └─ closePoint.vue                   //【弹框】关闭RFX
│     │  │  │  ├─ config                              //
│     │  │  │  │  └─ index.js                         //
│     │  │  │  └─ index.vue                           //
│     │  │  └─ recommend                              //采方：定点管理-定点推荐
│     │  │     ├─ components                          //
│     │  │     │  └─ backPointRecommend.vue           //【弹框】退回比价
│     │  │     ├─ config                              //
│     │  │     │  └─ index.js                         //
│     │  │     └─ index.vue                           //
│     │  ├─ price                                     //采方：价格服务
│     │  │  └─ list                                   //
│     │  │     ├─ components                          //
│     │  │     │  └─ addPrice                         //采方：价格服务-新增操作
│     │  │     │     ├─ components                    //
│     │  │     │     │  └─ addStage.vue               //【侧拉框】添加、编辑阶梯报价
│     │  │     │     ├─ config                        //
│     │  │     │     │  └─ index.js                   //
│     │  │     │     └─ index.vue                     //
│     │  │     ├─ config                              //
│     │  │     │  └─ index.js                         //
│     │  │     └─ index.vue                           //
│     │  ├─ rfx                                       //采方：询比价管理-询价大厅
│     │  │  ├─ detail                                 //采方：询价大厅(详情页)
│     │  │  │  ├─ components                          //
│     │  │  │  │  ├─ processKanban.vue                //询价大厅(详情页)-流程看板
│     │  │  │  │  ├─ timerClock.vue                   //询价大厅(详情页)-倒计时
│     │  │  │  │  └─ topInfo.vue                      //询价大厅(详情页)-顶部详情
│     │  │  │  ├─ index.vue                           //询价大厅(详情页)-入口文件
│     │  │  │  └─ tabs                                //
│     │  │  │     ├─ bidding                          //询价大厅(详情页)-竞价
│     │  │  │     │  └─ index.vue                     //
│     │  │  │     ├─ candidateSupplier                //询价大厅(详情页)-候选供方
│     │  │  │     │  ├─ components                    //
│     │  │  │     │  │  ├─ dialogSource.vue           //【弹框】货源清单使用
│     │  │  │     │  │  ├─ dialogSupplier.vue         //【弹框】供应商清单使用
│     │  │  │     │  │  ├─ sourceList.vue             //候选供方-货源清单
│     │  │  │     │  │  └─ supplierList.vue           //候选供方-供应商清单
│     │  │  │     │  ├─ config                        //
│     │  │  │     │  │  └─ index.js                   //
│     │  │  │     │  └─ index.vue                     //
│     │  │  │     ├─ checkPricing                     //询价大厅(详情页)-核价
│     │  │  │     │  ├─ config                        //
│     │  │  │     │  │  └─ index.js                   //
│     │  │  │     │  └─ index.vue                     //
│     │  │  │     ├─ comparePrice                     //询价大厅(详情页)-比价
│     │  │  │     │  ├─ components                    //
│     │  │  │     │  │  └─ backComparePrice.vue       //【弹框】撤回报价历史
│     │  │  │     │  ├─ config                        //
│     │  │  │     │  │  └─ index.js                   //
│     │  │  │     │  └─ index.vue                     //
│     │  │  │     ├─ description                      //询价大厅(详情页)-描述说明
│     │  │  │     │  └─ index.vue                     //
│     │  │  │     ├─ documents                        //询价大厅(详情页)-相关文件
│     │  │  │     │  ├─ config                        //
│     │  │  │     │  │  └─ index.js                   //
│     │  │  │     │  └─ index.vue                     //
│     │  │  │     ├─ evaluationBidding                //询价大厅(详情页)-评标(静态页面)
│     │  │  │     │  └─ index.vue                     //
│     │  │  │     ├─ hall                             //询价大厅(详情页)-大厅
│     │  │  │     │  ├─ components                    //
│     │  │  │     │  │  ├─ bidding.vue                //大厅面板-实时竞价
│     │  │  │     │  │  ├─ progress.vue               //大厅面板-进度汇总
│     │  │  │     │  │  ├─ ranking.vue                //大厅面板-实时排名
│     │  │  │     │  │  └─ supply.vue                 //大厅面板-供应商管理
│     │  │  │     │  ├─ config                        //
│     │  │  │     │  │  └─ index.js                   //
│     │  │  │     │  └─ index.vue                     //
│     │  │  │     ├─ inquiryBidding                   //询价大厅(详情页)-询价招标
│     │  │  │     │  ├─ components                    //
│     │  │  │     │  │  ├─ offerHistory               //【弹框】报价历史
│     │  │  │     │  │  │  ├─ config                  //
│     │  │  │     │  │  │  │  └─ index.js             //
│     │  │  │     │  │  │  └─ index.vue               //【弹框】阶梯报价
│     │  │  │     │  │  ├─ offerStages                //
│     │  │  │     │  │  │  ├─ config                  //
│     │  │  │     │  │  │  │  └─ index.js             //
│     │  │  │     │  │  │  └─ index.vue               //
│     │  │  │     │  │  └─ rejectQuote.vue            //【弹框】退回报价
│     │  │  │     │  ├─ config                        //
│     │  │  │     │  │  ├─ index.js                   //
│     │  │  │     │  │  └─ mixin.js                   //
│     │  │  │     │  ├─ index.vue                     //
│     │  │  │     │  ├─ offerDetails                  //询价招标-报价详情
│     │  │  │     │  │  ├─ components                 //
│     │  │  │     │  │  │  └─ abateDialog.vue         //【弹框】还价操作
│     │  │  │     │  │  ├─ index.vue                  //
│     │  │  │     │  │  ├─ listView                   //询价招标-报价详情-列表模式
│     │  │  │     │  │  │  ├─ config                  //
│     │  │  │     │  │  │  │  └─ index.js             //
│     │  │  │     │  │  │  └─ index.vue               //
│     │  │  │     │  │  ├─ packageView                //询价招标-报价详情-卡片模式-物料
│     │  │  │     │  │  │  ├─ config                  //
│     │  │  │     │  │  │  │  └─ index.js             //
│     │  │  │     │  │  │  └─ index.vue               //
│     │  │  │     │  │  └─ supplyView                 //询价招标-报价详情-卡片模式-供应商
│     │  │  │     │  │     ├─ config                  //
│     │  │  │     │  │     │  └─ index.js             //
│     │  │  │     │  │     └─ index.vue               //
│     │  │  │     │  └─ offerHistory                  //询价招标-接收历史
│     │  │  │     │     ├─ config                     //
│     │  │  │     │     │  └─ index.js                //
│     │  │  │     │     ├─ index.vue                  //
│     │  │  │     │     ├─ listView                   //询价招标-接收历史-列表模式
│     │  │  │     │     │  ├─ config                  //
│     │  │  │     │     │  │  └─ index.js             //
│     │  │  │     │     │  └─ index.vue               //
│     │  │  │     │     ├─ packageView                //询价招标-接收历史-卡片模式-物料
│     │  │  │     │     │  ├─ config                  //
│     │  │  │     │     │  │  └─ index.js             //
│     │  │  │     │     │  └─ index.vue               //
│     │  │  │     │     └─ supplyView                 //询价招标-接收历史-卡片模式-供应商
│     │  │  │     │        ├─ config                  //
│     │  │  │     │        │  └─ index.js             //
│     │  │  │     │        └─ index.vue               //
│     │  │  │     ├─ logs                             //询价大厅(详情页)-操作日志
│     │  │  │     │  ├─ config                        //
│     │  │  │     │  │  └─ index.js                   //
│     │  │  │     │  └─ index.vue                     //
│     │  │  │     ├─ purchaseDetail                   //询价大厅(详情页)-采购明细
│     │  │  │     │  ├─ components                    //
│     │  │  │     │  │  └─ chooseDetail               //【弹框】选择需求明细
│     │  │  │     │  │     ├─ config                  //
│     │  │  │     │  │     │  └─ index.js             //
│     │  │  │     │  │     └─ index.vue               //
│     │  │  │     │  └─ index.vue                     //
│     │  │  │     ├─ schedule                         //询价大厅(详情页)-任务计划
│     │  │  │     │  ├─ components                    //
│     │  │  │     │  │  ├─ backwardPlanDialog.vue     //【弹框】倒推生成计划
│     │  │  │     │  │  ├─ forwardPlanDialog.vue      //【弹框】正推生成计划
│     │  │  │     │  │  ├─ phaseDialog.vue            //【弹框】新增、编辑阶段
│     │  │  │     │  │  └─ planDialog.vue             //【弹框】新增、编辑任务
│     │  │  │     │  ├─ index.vue                     //
│     │  │  │     │  ├─ ScheduleCard.vue              //任务卡片-抽离组件
│     │  │  │     │  ├─ ScheduleContainer.vue         //任务计划-列表模式
│     │  │  │     │  └─ ScheduleGantt.vue             //任务计划-甘特图模式
│     │  │  │     ├─ strategy                         //询价大厅(详情页)-策略报告
│     │  │  │     │  └─ index.vue                     //
│     │  │  │     └─ teams                            //询价大厅(详情页)-寻源团队
│     │  │  │        ├─ components                    //
│     │  │  │        │  └─ createTeam.vue             //【弹框】新增、编辑团队
│     │  │  │        ├─ config                        //
│     │  │  │        │  └─ index.js                   //
│     │  │  │        └─ index.vue                     //
│     │  │  └─ list                                   //采方：询价大厅(列表页)
│     │  │     ├─ config                              //
│     │  │     │  └─ index.js                         //
│     │  │     └─ index.vue                           //
│     │  ├─ settings                                  //采方：配置类
│     │  │  ├─ rfx                                    //采方：配置类-rfx相关
│     │  │  │  ├─ approval                            //采方：审批流配置
│     │  │  │  │  ├─ components                       //【弹框】新增审批流配置
│     │  │  │  │  │  ├─ approvalDialog.vue            //【弹框】编辑审批流配置
│     │  │  │  │  │  └─ editApprovalDialog.vue        //
│     │  │  │  │  ├─ config                           //
│     │  │  │  │  │  └─ index.js                      //
│     │  │  │  │  └─ index.vue                        //
│     │  │  │  ├─ fixedPoint                          //采方：定点相关配置(路由名称：寻源策略配置)
│     │  │  │  │  └─ index.vue                        //
│     │  │  │  └─ strategy                            //采方：策略地图配置
│     │  │  │     ├─ detail                           //
│     │  │  │     │  ├─ components                    //【弹框】新增、编辑条目
│     │  │  │     │  │  ├─ itemDialog.vue             //【弹框】保存报错时，提示框
│     │  │  │     │  │  └─ strategyError.vue          //
│     │  │  │     │  ├─ config                        //
│     │  │  │     │  │  └─ index.js                   //
│     │  │  │     │  └─ index.vue                     //策略地图配置-详情
│     │  │  │     └─ list                             //策略地图配置-列表
│     │  │  │        ├─ config                        //
│     │  │  │        │  └─ index.js                   //
│     │  │  │        └─ index.vue                     //
│     │  │  └─ sor                                    //采方：配置类-sor相关
│     │  │     ├─ grouping                            //采方：需求分组规则
│     │  │     │  ├─ components                       //
│     │  │     │  │  └─ groupConfig.vue               //【侧拉框】新增、编辑分组规则
│     │  │     │  ├─ config                           //
│     │  │     │  │  └─ index.js                      //
│     │  │     │  └─ index.vue                        //
│     │  │     └─ distribution                        //采方：需求分配策略
│     │  │        ├─ components                       //
│     │  │        │  └─ strategyConfig.vue            //【侧拉框】新增、编辑分配策略
│     │  │        ├─ config                           //
│     │  │        │  └─ index.js                      //
│     │  │        └─ index.vue                        //
│     │  └─ sor                                       //采方-寻源需求管理
│     │     ├─ components                             //
│     │     │  └─ historyPrice                        //【弹框】价格历史
│     │     │     ├─ config                           //
│     │     │     │  └─ index.js                      //
│     │     │     └─ index.vue                        //
│     │     ├─ config                                 //
│     │     │  └─ mixin.js                            //
│     │     ├─ detail                                 //寻源需求-单据详情
│     │     │  ├─ components                          //
│     │     │  │  ├─ approval.vue                     //
│     │     │  │  └─ createSorDetail.vue              //
│     │     │  ├─ config                              //
│     │     │  │  └─ index.js                         //
│     │     │  └─ index.vue                           //
│     │     ├─ grouping                               //寻源需求-寻源分组建议-配置页
│     │     │  ├─ components                          //
│     │     │  │  ├─ sourceList.vue                   //
│     │     │  │  └─ sourceRight.vue                  //
│     │     │  ├─ config                              //
│     │     │  │  └─ index.js                         //
│     │     │  └─ index.vue                           //
│     │     ├─ header                                 //寻源需求管理-寻源需求汇总
│     │     │  ├─ components                          //
│     │     │  ├─ config                              //
│     │     │  │  └─ index.js                         //
│     │     │  └─ index.vue                           //
│     │     └─ mine                                   //寻源需求管理-我的需求管理
│     │        ├─ components                          //
│     │        │  ├─ createRequirement.vue            //【弹框】创建寻源需求
│     │        │  ├─ createSourceProject.vue          //【弹框】创建寻源项目
│     │        │  ├─ sourceGrouping.vue               //【弹框】寻源分组建议
│     │        │  └─ transmitRequirement.vue          //【弹框】转发
│     │        ├─ config                              //
│     │        │  └─ index.js                         //
│     │        └─ index.vue                           //
│     └─ supply                                       //supply 供方
│        └─ quotationBidding                          //供方-客户寻源管理-报价投标管理
│           ├─ detail                                 //供方-客户寻源管理-报价投标管理-详情页
│           │  ├─ components                          //
│           │  │  ├─ tabHall                          //
│           │  │  │  ├─ timeBid.vue                   //
│           │  │  │  └─ timeRank.vue                  //
│           │  │  └─ topInfo.vue                      //详情页-顶部详情
│           │  ├─ index.vue                           //详情页
│           │  ├─ mock                                //
│           │  │  ├─ index.js                         //
│           │  │  └─ tabHall.js                       //
│           │  └─ pages                               //
│           │     ├─ bidEvaluation.vue                //
│           │     ├─ components                       //
│           │     │  └─ planInfo.vue                  //
│           │     ├─ config                           //
│           │     │  └─ index.js                      //
│           │     ├─ documents                        //详情页-相关文件
│           │     │  ├─ config                        //
│           │     │  │  └─ index.js                   //
│           │     │  └─ index.vue                     //
│           │     ├─ tabExplain.vue                   //详情页-说明
│           │     ├─ tabHall.vue                      //详情页-大厅
│           │     └─ tabQuotedPrice.vue               //详情页-报价
│           └─ list                                   //供方-客户寻源管理-报价投标管理-列表页
│              ├─ config                              //
│              │  └─ index.js                         //
│              └─ index.vue                           //列表页
└─ vue.config.js                                      //

```