{
  "include": [
    "./src/**/*"
  ],
  "exclude": [
    "node_modules"
  ],
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "COMPONENTS/*": [
        "src/components/*"
      ],
      "@": [
        "src"
      ],
      "CONFIG": [
        "src/config"
      ],
      "COMPONENTS": [
        "src/components"
      ],
      "ASSETS": [
        "src/assets"
      ],
      "STORE": [
        "src/store"
      ],
      "UTILS": [
        "src/utils"
      ],
      "ROUTER_PUBLIC": [
        "src/views/public"
      ],
      "ROUTER_PURCHASE": [
        "src/views/purchase"
      ],
      "ROUTER_PURCHASE_SOR": [
        "src/views/purchase/sor"
      ],
      "ROUTER_PURCHASE_RFX": [
        "src/views/purchase/rfx"
      ],
      "ROUTER_PURCHASE_PRICE": [
        "src/views/purchase/price"
      ],
      "ROUTER_PURCHASE_ASSESSMANAGE": [
        "src/views/purchase/assessManage"
      ],
      "ROUTER_PURCHASE_POINT": [
        "src/views/purchase/fixedPoint"
      ],
      "ROUTER_PURCHASE_SETTINGS": [
        "src/views/purchase/settings"
      ],
      "ROUTER_SUPPLY": [
        "src/views/supply"
      ],
      "ROUTER_SUPPLY_QB": [
        "src/views/supply/quotationBiddingOld"
      ],
      "ROUTER_SUPPLY_QUOTATION": [
        "src/views/supply/quotationBidding"
      ],
      "ROUTER_SUPPLY_PRICE": [
        "src/views/supply/price"
      ],
      "ROUTER_SUPPLY_ASSESSMANAGE": [
        "src/views/supply/assessFeedback"
      ],
      "ROUTER_SUPPLY_PRICECONFIRM": [
        "src/views/supply/priceConfirm"
      ],
      "ROUTER_SUPPLY_QM": [
        "src/views/supply/quotaManagement"
      ],
      "ROUTER_COMMON": [
        "src/views/common"
      ],
      "ROUTER_COMMON_SETTINGS": [
        "src/views/common/settings"
      ],
    }
  }
}
