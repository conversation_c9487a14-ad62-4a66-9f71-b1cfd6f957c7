{"name": "mtech-srm-sourcing-web", "version": "0.0.1", "private": true, "scripts": {"build": "vue-cli-service build", "prebuild": "npm run theme", "lint": "vue-cli-service lint", "buildAll": "npm-run-all theme build --continue-on-error", "predev": "npm run theme", "dev": "vue-cli-service serve", "serve": "vue-cli-service serve", "serve:dev": "cross-env VUE_PROXY_TARGET=http://srm-dev-gw.eads.tcl.com port=8081 vue-cli-service serve", "serve:test": "cross-env VUE_PROXY_TARGET=http://srm-test-gw.eads.tcl.com port=8082 vue-cli-service serve", "serve:sit": "cross-env VUE_PROXY_TARGET=http://srm-sit-gw.eads.tcl.com port=8083 vue-cli-service serve", "serve:uat": "cross-env VUE_PROXY_TARGET=http://srm-uat-gw.eads.tcl.com port=8084 vue-cli-service serve", "dll": "vue-cli-service dll", "serveAll": "npm-run-all theme serve --continue-on-error", "theme": "node build/theme.js", "dictionary": "node build/dict.js", "upgrade:mtech-ui": "npx update-by-scope -t latest @mtech-ui npm install", "build:report": "vue-cli-service build --report --report-json", "translate": "node translate.js", "prepare": "husky install", "lint-staged": "lint-staged"}, "dependencies": {"@ag-grid-community/vue": "^27.3.0", "@ag-grid-enterprise/all-modules": "^27.3.0", "@digis/component-props-state": "^1.2.6", "@digis/dictionary-plugin": "^1.1.11", "@digis/digis-bpmn-editor": "^0.2.6", "@digis/internationalization": "^1.1.22", "@digis/j-antdv": "^1.2.25", "@mtech-common/http": "^1.0.5", "@mtech-common/utils": "^1.0.0", "@mtech-form-design/deploy": "^1.0.0", "@mtech-form-design/form-parser": "^1.0.0", "@mtech-micro-frontend/vue-cli-plugin-micro": "^1.0.0", "@mtech-sso/single-sign-on": "^1.2.5-tcl.7", "@mtech-ui/data-grid": "^1.11.30", "@mtech-ui/input-number": "^1.11.3", "@mtech-ui/mtech-ui": "^1.11.14", "@mtech-ui/multi-select": "^1.11.27", "@mtech-ui/page": "^1.11.14", "@mtech-ui/select": "^1.11.27", "@mtech-ui/tabs": "1.8.4", "@mtech/common-detail-page": "^0.2.1", "@mtech/common-loading": "^0.2.1", "@mtech/common-permission": "^1.2.4", "@mtech/common-rule-config": "^0.2.1", "@mtech/common-template-page": "^1.8.21", "@mtech/common-tree-view": "^1.1.6", "@mtech/eslint-config-vue": "0.0.4", "@mtech/mtech-common-uploader": "^1.8.4", "ag-grid-community": "^27.3.0", "ag-grid-enterprise": "^27.3.0", "ag-grid-vue": "^27.3.0", "crypto": "^1.0.1", "decimal.js": "^10.3.1", "echarts": "^5.3.0", "encryptlong": "^3.1.4", "jsrsasign": "^10.5.23", "uuid": "^8.3.2", "vue": "^2.6.11", "vue-class-component": "^7.2.6", "vue-cli-plugin-style-resources-loader": "~0.1.5", "vue-draggable-resizable": "^2.3.0", "vue-property-decorator": "^9.1.2", "vue-router": "^3.5.1", "vue-template-compiler": "^2.6.11", "vue-virtual-scroll-list": "^2.3.3", "vuex": "^3.6.2", "vuex-persistedstate": "^4.0.0", "vxe-table": "^3.6.13", "xe-utils": "^3.5.11"}, "devDependencies": {"@commitlint/cli": "^17.0.1", "@commitlint/config-conventional": "^17.0.0", "@digis-vue-cli-preset/vue-cli-plugin-performance": "^1.1.5", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "^4.5.13", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "babel-plugin-import": "^1.13.3", "base-64": "^1.0.0", "chalk": "^2.4.2", "core-js": "^3.6.5", "cross-env": "^7.0.3", "eslint": "^6.8.0", "husky": "^8.0.3", "js-cookie": "^2.2.1", "lint-staged": "^13.1.4", "lodash": "^4.17.21", "md5": "^2.3.0", "node-sass": "^4.14.1", "npm-run-all": "^4.1.5", "sass-loader": "^8.0.0", "sortablejs": "^1.14.0", "style-resources-loader": "^1.4.1", "svg-sprite-loader": "^6.0.5", "webpack-bundle-analyzer": "^4.5.0", "webpack-fix-style-only-entries": "^0.6.1", "webpack-glob-entry": "^2.1.1"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "mtMicro": {"type": "slave"}, "lint-staged": {"src/**/*.{js,jsx,vue,ts,tsx}": ["eslint --fix"]}}