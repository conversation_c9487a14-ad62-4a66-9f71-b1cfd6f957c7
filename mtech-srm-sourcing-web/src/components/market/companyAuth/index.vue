// 授权公司弹出框
<template>
  <mt-dialog ref="dialog" :header="header" :buttons="buttons" width="1000" @beforeClose="cancel">
    <div class="dialog-content">
      <div class="table-container">
        <div class="table-title">
          {{ $t(`待授权公司（总计`) }}
          {{ unAuthList.length }}
          {{ $t(`家公司）`) }}
        </div>
        <div class="table-content">
          <mt-template-page ref="unAuthRef" :template-config="unAuthConfig" />
        </div>
      </div>
      <div class="cross-container">
        <mt-tooltip
          opens-on="Custom"
          target=".cross-right"
          :content="authToolTip.msg"
          ref="tooltipForAuth"
        >
          <div
            :class="[
              'cross',
              'cross-right',
              {
                'cross-disabled': !authToolTip.enableMove
              }
            ]"
            @click="handleAuth"
            @mouseenter="openTipsForAuth"
            @mouseleave="closeTipsForAuth"
          ></div>
        </mt-tooltip>
        <mt-tooltip
          opens-on="Custom"
          target=".cross-left"
          :content="unAuthToolTip.msg"
          ref="tooltipForUnAuth"
        >
          <div
            :class="[
              'cross',
              'cross-left',
              {
                'cross-disabled': !unAuthToolTip.enableMove
              }
            ]"
            @click="handleUnAuth"
            @mouseenter="openTipsForUnAuth"
            @mouseleave="closeTipsForUnAuth"
          ></div>
        </mt-tooltip>
      </div>
      <div class="table-container">
        <div class="table-title">
          {{ $t(`已授权公司（总计`) }}
          {{ authList.length }}
          {{ $t(`家公司）`) }}
        </div>
        <div class="table-content">
          <mt-template-page ref="authRef" :template-config="authConfig" />
        </div>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { utils } from '@mtech-common/utils'
import { unAuthConfig, authConfig } from './config'
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      factorInfo: {
        id: null
      },
      authToolTip: {
        msg: '',
        enableMove: false
      },
      unAuthToolTip: {
        msg: '',
        enableMove: false
      },
      allList: [], //全部公司列表
      unAuthList: [], //未授权公司列表
      authList: [], //当前授权公司列表
      originList: [], //初始获取的已授权公司列表
      unAuthConfig: unAuthConfig(this.serializeAuthToolTip),
      authConfig: authConfig(this.serializeUnAuthToolTip)
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.$t(`公司信息（总计`) + this?.allList?.length + this.$t(`家公司）`)
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.factorInfo = { ...this.modalData.data }
    }
    this.getCompanyList()
  },
  methods: {
    //获取全部公司列表
    getCompanyList() {
      // 获取公司编码、公司名称  {companyCode、companyName、id}
      this.$API.masterData.findSpecifiedChildrenLevelOrgs().then((res) => {
        res.data.forEach((e) => {
          e.companyId = e.id
          e.companyCode = e.orgCode
          e.companyName = e.orgName
        })
        this.allList = res.data
        this.getAuthList()
      })
    },
    //获取当前授权列表
    getAuthList() {
      if (this.factorInfo.id) {
        const DEFAULTPARAM = {
          condition: '',
          page: {
            current: 1,
            size: 1000
          },
          pageFlag: false,
          defaultRules: [
            {
              label: '因子ID',
              field: this.modalData.queryId,
              type: 'number',
              operator: 'equal',
              value: this.factorInfo.id
            }
          ]
        }
        this.modalData.getAuthList(DEFAULTPARAM).then((res) => {
          this.originList = utils.cloneDeep(res.data.records)
          this.authList = utils.cloneDeep(res.data.records)
          this.$set(this.authConfig[0].grid, 'dataSource', this.authList)
          this.initUnAuthConfig()
        })
      } else if (this.modalData.localList) {
        this.originList = utils.cloneDeep(this.modalData.localList)
        this.authList = utils.cloneDeep(this.modalData.localList)
        this.$set(this.authConfig[0].grid, 'dataSource', this.authList)
        this.initUnAuthConfig()
      }
    },
    //根据全部数据、已授权数据，得到待授权列表数据
    initUnAuthConfig() {
      let _auth = utils.cloneDeep(this.authList)
      let _all = utils.cloneDeep(this.allList)
      let _authCodes = [],
        _unAutnList = []
      _auth.forEach((e) => {
        _authCodes.push(e.companyCode)
      })
      _all.forEach((e) => {
        if (_authCodes.indexOf(e.companyCode) < 0) {
          _unAutnList.push(e)
        }
      })
      this.unAuthList = _unAutnList
      this.$set(this.unAuthConfig[0].grid, 'dataSource', this.unAuthList)
    },
    serializeAuthToolTip() {
      let _currentTabRef = this.$refs.unAuthRef.getCurrentTabRef()
      let _selectRecords = _currentTabRef?.grid?.getSelectedRecords() ?? []
      if (_selectRecords.length < 1) {
        this.authToolTip = {
          msg: this.$t("'待授权'列表中，无勾选数据."),
          enableMove: false
        }
      } else {
        this.authToolTip = {
          msg: this.$t("点击，将'待授权'中选中的数据，移至已授权列表."),
          enableMove: true
        }
      }
    },
    openTipsForAuth(args) {
      this.serializeAuthToolTip()
      this.$refs.tooltipForAuth.open(args.target)
    },
    closeTipsForAuth(args) {
      this.$refs.tooltipForAuth.close(args.target)
    },
    //授权操作
    handleAuth() {
      let _currentTabRef = this.$refs.unAuthRef.getCurrentTabRef()
      let _selectRecords = _currentTabRef?.grid?.getSelectedRecords() ?? []
      if (!_selectRecords.length) {
        this.$toast({
          content: this.$t('当前未勾选需要授权的数据'),
          type: 'warning'
        })
        return
      }
      let _selectCodes = [],
        _unAuthResult = [],
        _authList = utils.cloneDeep(this.authList),
        _unAutnList = utils.cloneDeep(this.unAuthList)
      _selectRecords.forEach((e) => {
        _selectCodes.push(e.companyCode)
        _authList.push(e)
      })
      _unAutnList.forEach((e) => {
        if (_selectCodes.indexOf(e.companyCode) < 0) {
          _unAuthResult.push(e)
        }
      })
      this.$nextTick(() => {
        this.authList = _authList
        this.$set(this.authConfig[0].grid, 'dataSource', this.authList)
        this.unAuthList = _unAuthResult
        this.$set(this.unAuthConfig[0].grid, 'dataSource', this.unAuthList)
      })
    },
    serializeUnAuthToolTip() {
      let _currentTabRef = this.$refs.authRef.getCurrentTabRef()
      let _selectRecords = _currentTabRef?.grid?.getSelectedRecords() ?? []
      if (_selectRecords.length < 1) {
        this.unAuthToolTip = {
          msg: this.$t("'已授权'列表中，无勾选数据."),
          enableMove: false
        }
      } else {
        this.unAuthToolTip = {
          msg: this.$t("点击，将'已授权'中选中的数据，移至待授权列表."),
          enableMove: true
        }
      }
    },
    openTipsForUnAuth(args) {
      this.serializeUnAuthToolTip()
      this.$refs.tooltipForUnAuth.open(args.target)
    },
    closeTipsForUnAuth(args) {
      this.$refs.tooltipForUnAuth.close(args.target)
    },
    //取消授权操作
    handleUnAuth() {
      let _currentTabRef = this.$refs.authRef.getCurrentTabRef()
      let _selectRecords = _currentTabRef?.grid?.getSelectedRecords() ?? []
      if (!_selectRecords.length) {
        this.$toast({
          content: this.$t('当前未勾选取消授权的数据'),
          type: 'warning'
        })
        return
      }
      let _selectCodes = [],
        _authResult = [],
        _authList = utils.cloneDeep(this.authList),
        _unAutnList = utils.cloneDeep(this.unAuthList)
      _selectRecords.forEach((e) => {
        _selectCodes.push(e.companyCode)
        _unAutnList.push(e)
      })
      _authList.forEach((e) => {
        if (_selectCodes.indexOf(e.companyCode) < 0) {
          _authResult.push(e)
        }
      })
      this.$nextTick(() => {
        this.authList = _authResult
        this.$set(this.authConfig[0].grid, 'dataSource', this.authList)
        this.unAuthList = _unAutnList
        this.$set(this.unAuthConfig[0].grid, 'dataSource', this.unAuthList)
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    confirm() {
      let _deleteAuth = [],
        _saveAuth = [],
        _originList = utils.cloneDeep(this.originList),
        _authList = utils.cloneDeep(this.authList)
      _originList.forEach((e) => {
        let _filter = _authList.filter((f) => {
          f.companyCode === e.companyCode
        })
        if (_filter.length < 1) {
          //origin中的数据，在_authList列表未找到，标记为已删除数据
          _deleteAuth.push({
            companyCode: e.companyCode, //公司编码
            companyId: e.companyId, //公司id
            companyName: e.companyName //公司名称
          })
        }
      })
      _authList.forEach((e) => {
        _saveAuth.push({
          companyCode: e.companyCode, //公司编码
          companyId: e.companyId, //公司id
          companyName: e.companyName //公司名称
        })
      })
      let _result = {
        companyRelList: _saveAuth,
        removeCompanyRelList: _deleteAuth
      }
      this.$emit('confirm-function', _result)
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  padding: 20px 0 0 0;
  .table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    .table-title {
      flex-shrink: 0;
      font-size: 16px;
      color: #292929;
      display: inline-block;
      padding-left: 13px;
      position: relative;
      margin-bottom: 20px;

      &:before {
        content: '';
        position: absolute;
        width: 3px;
        height: 14px;
        background: #6386c1;
        border-radius: 0 2px 2px 0;
        left: 0;
        top: 2px;
      }
    }
    .table-content {
      flex: 1;
      border-bottom: 1px solid #e8e8e8;
    }
  }
  .cross-container {
    width: 60px;
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    .mt-tooptip {
      width: auto;
      margin-top: 30px;
      &:first-of-type {
        margin-top: 0;
      }
      .cross {
        background: #fbfbfb;
        height: 30px;
        width: 30px;
        border-radius: 50%;
        position: relative;
        border: 2px solid #6386c1;
        &:after {
          content: '';
          width: 10px;
          height: 10px;
          background: 0 0;
          border-left: none;
          border-top: none;
          border-right: 2px solid #6386c1;
          border-bottom: 2px solid #6386c1;
          position: absolute;
          top: 8px;
          left: 6px;
          transform: rotate(-45deg);
        }
        &.cross-left {
          &:after {
            border-left: 2px solid #6386c1;
            border-top: 2px solid #6386c1;
            border-right: none;
            border-bottom: none;
            left: 10px;
          }
        }
        &.cross-disabled {
          border-width: 1px;
          border-color: #9daabf;
          &:after {
            border-width: 1px;
            border-color: #9daabf;
          }
        }
      }
    }
  }
}
</style>
