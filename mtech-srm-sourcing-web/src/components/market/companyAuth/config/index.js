import { i18n } from '@/main.js'
const columnData = [
  {
    field: 'companyCode',
    headerText: i18n.t('公司编码'),
    width: 120
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称')
  }
]

export const unAuthConfig = (setToolTipFunction) => [
  {
    grid: {
      allowFiltering: true,
      allowPaging: false,
      lineSelection: true,
      rowSelected: setToolTipFunction,
      rowDeselected: setToolTipFunction,
      columnData,
      dataSource: []
    }
  }
]

export const authConfig = (setToolTipFunction) => [
  {
    grid: {
      allowFiltering: true,
      allowPaging: false,
      lineSelection: true,
      rowSelected: setToolTipFunction,
      rowDeselected: setToolTipFunction,
      columnData,
      dataSource: []
    }
  }
]
