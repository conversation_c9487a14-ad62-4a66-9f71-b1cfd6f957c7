// 行情因子-操作记录
<template>
  <mt-dialog ref="dialog" :header="header" :buttons="buttons" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-template-page ref="templateRef" :template-config="pageConfig" />
    </div>
  </mt-dialog>
</template>

<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('关闭') }
        }
      ],
      pageConfig,
      factorInfo: {
        id: null
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.factorInfo = { ...this.modalData.data }
      this.resetAsyncConfigParams()
    }
  },
  methods: {
    //列表参数重新赋值
    resetAsyncConfigParams() {
      let params = { dataId: this.factorInfo.id }
      this.$set(this.pageConfig[0].grid, 'asyncConfig', {
        url: this.$API.marketFactor.getOperatorList,
        queryBuilderWrap: 'queryBuilderDTO',
        params
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  padding: 20px 0 0 0;
}
</style>
