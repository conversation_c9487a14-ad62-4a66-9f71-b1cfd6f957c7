import { i18n } from '@/main.js'
const columnData = [
  {
    field: 'createUserName',
    headerText: i18n.t('操作人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('操作日期')
  },
  {
    field: 'operateType',
    headerText: i18n.t('动作')
  },
  {
    field: 'content',
    headerText: i18n.t('说明')
  }
]

export const pageConfig = [
  {
    grid: { allowFiltering: true, columnData, dataSource: [] }
  }
]
