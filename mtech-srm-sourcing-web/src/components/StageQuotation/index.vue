<template>
  <div>
    <h3 style="font-weight: bolder; padding: 16px 0">{{ $t('阶梯维护：') }}</h3>
    <mt-select
      ref="stageTypeRef"
      @change="changeStageType"
      float-label-type="Never"
      :index="0"
      :data-source="stageTypeList"
      :placeholder="$t('请选择阶梯类型')"
    />
    <mt-form ref="dialogRef">
      <mt-form-item :label="$t('含税价')" v-show="stageType == -1">
        <mt-input-number
          :min="0"
          :max="999999999999"
          :precision="4"
          v-model="taxedUnitPriceValue"
          :placeholder="$t('输入含税价')"
        ></mt-input-number>
      </mt-form-item>
      <mt-form-item :label="$t('未含税价')" v-show="stageType == -1">
        <mt-input-number
          disabled
          :show-clear-button="false"
          float-label-type="Never"
          v-model="untaxedUnitPrice"
          :placeholder="$t('未含税价-自动计算')"
        ></mt-input-number>
      </mt-form-item>
      <template v-if="showMaterialInfo === 0">
        <mt-form-item :label="$t('规划量')" v-show="stageType == -1">
          <mt-input-number
            :show-clear-button="false"
            float-label-type="Never"
            v-model="noStepObject.planQuantity"
            @change="planQuantityChange"
            :placeholder="$t('规划量')"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item :label="$t('实际分摊量')" v-show="stageType == -1">
          <mt-input-number
            :show-clear-button="false"
            float-label-type="Never"
            v-model="noStepObject.shareQuantity"
            @change="shareQuantityChange"
            :placeholder="$t('实际分摊量')"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item :label="$t('规划分摊价（含税）')" v-show="stageType == -1">
          <mt-input-number
            :show-clear-button="false"
            float-label-type="Never"
            v-model="planSharePriceTaxed"
            :placeholder="$t('规划分摊价（含税）')"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item :label="$t('规划分摊价（未税）')" v-show="stageType == -1">
          <mt-input-number
            disabled
            :show-clear-button="false"
            float-label-type="Never"
            v-model="planSharePriceUntaxed"
            :placeholder="$t('规划分摊价（未税）')"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item :label="$t('实际分摊价（含税）')" v-show="stageType == -1">
          <mt-input-number
            :show-clear-button="false"
            float-label-type="Never"
            v-model="realSharePriceTaxed"
            :placeholder="$t('实际分摊价（含税）')"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item :label="$t('实际分摊价（未税）')" v-show="stageType == -1">
          <mt-input-number
            disabled
            :show-clear-button="false"
            float-label-type="Never"
            v-model="realSharePriceUntaxed"
            :placeholder="$t('实际分摊价（未税）')"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item :label="$t('分摊后单价（含税）')" v-show="stageType == -1">
          <mt-input-number
            :show-clear-button="false"
            float-label-type="Never"
            v-model="sharePriceTaxed"
            :placeholder="$t('分摊后单价（含税）')"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item :label="$t('分摊后单价（未税）')" v-show="stageType == -1">
          <mt-input-number
            disabled
            :show-clear-button="false"
            float-label-type="Never"
            v-model="sharePriceUntaxed"
            :placeholder="$t('分摊后单价（未税）')"
          ></mt-input-number>
        </mt-form-item>
      </template>
    </mt-form>
    <mt-template-page
      :template-config="templateConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      v-if="stageType >= 0"
    />
  </div>
</template>

<script>
export default {
  name: 'StageQuotationIndex',
  props: {
    taxRate: {
      type: Object,
      required: true
    },
    stageList: {
      type: Array,
      required: true
    },
    priceType: {
      type: String,
      require: true,
      default: () => {
        return null
      }
    }
  },
  model: {
    prop: 'stageList',
    event: 'change'
  },
  data() {
    return {
      stageTypeList: [
        { value: -1, text: this.$t('无阶梯') },
        { value: 0, text: this.$t('按数量累计') },
        { value: 1, text: this.$t('按时间') },
        { value: 2, text: this.$t('按金额') }
      ],
      stageType: -1,
      templateConfig: [
        {
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [['Add']]
          },
          grid: {
            allowFiltering: true,
            height: 90,
            allowPaging: false,
            columnData: [
              {
                field: 'stageRange',
                headerText: this.$t('数量'),
                cellTools: [
                  { id: 'Edit', icon: 'icon_Editor', title: this.$t('编辑') },
                  {
                    id: 'Delete',
                    icon: 'icon_solid_Delete',
                    title: this.$t('删除')
                  }
                ]
              },
              {
                field: 'taxedUnitPrice',
                headerText: this.$t('含税价')
              },
              {
                field: 'untaxedUnitPrice',
                headerText: this.$t('未含税')
              },
              {
                field: 'discountRate',
                headerText: this.$t('折扣')
              }
            ],
            dataSource: []
          }
        }
      ],
      taxedUnitPriceValue: 0,
      planSharePriceTaxed: 0, // 规划分摊价（含税）
      realSharePriceTaxed: 0, // 实际分摊价（含税）
      sharePriceTaxed: 0, // 分摊后单价（含税）
      noStepObject: {}
    }
  },
  watch: {
    stageList: {
      immediate: true,
      handler(value) {
        this.templateConfig[0].grid.dataSource = value
      }
    }
  },
  methods: {
    changeStageType(ej2EventObject) {
      this.stageType = ej2EventObject.itemData.value
      this.templateConfig[0].grid.columnData[0].headerText = ej2EventObject.itemData.text
      this.$emit('change', [])
    },
    handleClickToolBar(ej2EventObject) {
      if (ej2EventObject.toolbar.id === 'Add') {
        this.handleAddStage()
      }
    },
    handleClickCellTool(ej2EventObject) {
      if (ej2EventObject.tool.id === 'Edit') {
        this.handleEditStage(ej2EventObject)
      } else if (ej2EventObject.tool.id === 'Delete') {
        this.handleDeleteStage(ej2EventObject)
      }
    },
    handleEditStage(ej2EventObject) {
      this.$dialog({
        modal: () => import('./dialog.vue'),
        data: {
          taxRate: this.taxRate,
          formData: ej2EventObject.componentData,
          stageType: this.stageType
        },
        success: (res) => {
          const list = [...this.templateConfig[0].grid.dataSource]
          list[res.formItemIndex] = {
            stageType: this.stageType,
            stageFrom: res.start,
            stageTo: res.end,
            remark: res.remark,
            taxId: this.taxRate.taxId,
            stageRange: `${res.start} - ${res.end}`,
            taxedUnitPrice: res.taxedUnitPrice,
            untaxedUnitPrice: res.untaxedUnitPrice,
            discountRate: res.discountRate
          }
          this.$emit('change', list)
        }
      })
    },
    //删除阶梯报价
    handleDeleteStage(ej2EventObject) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const list = [...this.templateConfig[0].grid.dataSource]
          list.splice(ej2EventObject.componentData.index, 1)
          this.$emit('change', list)
        }
      })
    },

    handleAddStage() {
      if (this.taxRate.value === null || this.taxRate.value === '') {
        return this.$toast({ type: 'warning', content: this.$t('未选择税率') })
      }
      this.$dialog({
        modal: () => import('./dialog.vue'),
        data: {
          stageType: this.stageType,
          taxRate: this.taxRate
        },
        success: (res) => {
          const list = [...this.templateConfig[0].grid.dataSource]
          list.push({
            stageType: this.stageType,
            stageFrom: res.start,
            stageTo: res.end,
            remark: res.remark,
            taxId: this.taxRate.taxId,
            stageRange: `${res.start} - ${res.end}`,
            taxedUnitPrice: res.taxedUnitPrice,
            untaxedUnitPrice: res.untaxedUnitPrice,
            discountRate: res.discountRate
          })
          this.$emit('change', list)
        }
      })
    },
    planQuantityChange(e) {
      this.$emit('stage', {
        planQuantity: e
      })
    },
    shareQuantityChange(e) {
      this.$emit('stage', {
        shareQuantity: e
      })
    }
  },
  computed: {
    //未税单价
    untaxedUnitPrice() {
      if (Number(this.taxedUnitPriceValue) === 0) return '0'
      const uValue = (Number(this.taxedUnitPriceValue) / (1 + this.taxRate.value * 1)).toFixed(4)
      this.$emit('stage', {
        taxedUnitPrice: this.taxedUnitPriceValue,
        untaxedUnitPrice: uValue
      })
      return uValue
    },
    planSharePriceUntaxed() {
      if (Number(this.planSharePriceTaxed) === 0) return '0'
      const uValue = (Number(this.planSharePriceTaxed) / (1 + this.taxRate.value * 1)).toFixed(4)
      this.$emit('stage', {
        planSharePriceTaxed: this.planSharePriceTaxed,
        planSharePriceUntaxed: uValue
      })
      return uValue
    },
    realSharePriceUntaxed() {
      if (Number(this.realSharePriceTaxed) === 0) return '0'
      const uValue = (Number(this.realSharePriceTaxed) / (1 + this.taxRate.value * 1)).toFixed(4)
      this.$emit('stage', {
        realSharePriceTaxed: this.realSharePriceTaxed,
        realSharePriceUntaxed: uValue
      })
      return uValue
    },
    sharePriceUntaxed() {
      if (Number(this.sharePriceTaxed) === 0) return '0'
      const uValue = (Number(this.sharePriceTaxed) / (1 + this.taxRate.value * 1)).toFixed(4)
      this.$emit('stage', {
        sharePriceTaxed: this.sharePriceTaxed,
        sharePriceUntaxed: uValue
      })
      return uValue
    },
    showMaterialInfo() {
      return this.priceType
    }
  }
}
</script>

<style scoped></style>
