<template>
  <!-- 阶梯报价-设置弹框 -->
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="dialogTitle"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <p class="tax-rate">
        {{ $t('当前选择适用税率：') }}
        {{ modalData.taxRate.text }}
        {{ $t('，税率数值：') }}
        {{ modalData.taxRate.value }}
      </p>
      <!-- 0数量 1时间 2金额 -->
      <mt-form ref="dialogRef" :model="formObject">
        <mt-form-item :label="$t('起始')" v-if="modalData.stageType !== 1">
          <mt-input-number :min="0" v-model="formObject.start" :placeholder="$t('请输入起始值')" />
        </mt-form-item>
        <mt-form-item :label="$t('结束(留空为 ∞)')" v-if="modalData.stageType !== 1">
          <mt-input-number
            :min="0"
            v-model="formObject.end"
            :placeholder="$t('请输入结束值，留空为∞')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('起始时间')" v-if="modalData.stageType === 1">
          <mt-date-time-picker v-model="formObject.start" :placeholder="$t('请选择起始时间')" />
        </mt-form-item>
        <mt-form-item :label="$t('结束时间(留空为 ∞)')" v-if="modalData.stageType === 1">
          <mt-date-time-picker
            v-model="formObject.end"
            :placeholder="$t('请选择结束时间，留空为∞')"
          />
        </mt-form-item>
        <mt-form-item prop="taxedUnitPrice" :label="$t('含税价')">
          <mt-input-number
            :min="0"
            v-model="formObject.taxedUnitPrice"
            :placeholder="$t('输入含税价')"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item :label="$t('未含税价')">
          <mt-input-number
            disabled
            :show-clear-button="false"
            float-label-type="Never"
            v-model="untaxedUnitPrice"
            :placeholder="$t('未含税价-自动计算')"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item prop="discountRate" :label="$t('折扣')">
          <mt-input-number
            :min="0"
            :max="1"
            float-label-type="Never"
            v-model="formObject.discountRate"
            :placeholder="$t('折扣')"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')">
          <mt-input
            float-label-type="Never"
            v-model="formObject.remark"
            :placeholder="$t('备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { DifferentialPricingTypeMap } from '@/constants'
import utils from '@/utils/utils'
export default {
  name: 'AddStageModal',
  data() {
    return {
      type: 'NEW',
      formItemIndex: null,
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formObject: {
        start: null,
        end: null,
        remark: '',
        taxedUnitPrice: 0, //含税单价
        discountRate: 0 //折扣率
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    //未税单价
    untaxedUnitPrice() {
      if (Number(this.formObject.taxedUnitPrice) === 0) return '0'
      return (
        Number(this.formObject.taxedUnitPrice) /
        (1 + this.modalData.taxRate.value * 1)
      ).toFixed(4)
    },
    dialogTitle() {
      return `${this.type === 'EDIT' ? this.$t('编辑') : this.$t('新增')}【${
        DifferentialPricingTypeMap[this.modalData.stageType]
      }】${this.$t('阶梯')}`
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData.formData) {
      let { stageFrom, stageTo, taxedUnitPrice, discountRate } = this.modalData.formData
      this.type = 'EDIT'
      this.formObject.start = stageFrom
      this.formObject.end = stageTo
      this.formObject.taxedUnitPrice = taxedUnitPrice
      this.formObject.discountRate = discountRate
    }
  },
  methods: {
    /** @return {boolean} */
    validateFormData() {
      //0数量 1时间 2金额
      const { start, end, discountRate } = this.formObject
      const startAssert = this.modalData.stageType === 1 ? !!start : start !== 0
      return (
        discountRate < 1 &&
        discountRate >= 0 &&
        startAssert &&
        (end === null || end === '∞' || end * 1 > start * 1)
      )
    },
    confirm() {
      if (!this.validateFormData()) {
        return this.$toast({
          content: this.$t('表单填写不正确'),
          type: 'warning'
        })
      }
      const res = {
        untaxedUnitPrice: Number(this.untaxedUnitPrice),
        discountRate: Number(this.formObject.discountRate),
        taxedUnitPrice: Number(this.formObject.taxedUnitPrice),
        remark: this.formObject.remark
      }
      if (this.type === 'EDIT') {
        res.formItemIndex = this.modalData.formData.index
      }
      if (this.modalData.stageType === 1) {
        res.start = utils.formatTime(new Date(this.formObject.start), 'YYYY-mm-dd HH:MM:SS')
        res.end = [null, '∞'].includes(this.formObject.end)
          ? '∞'
          : utils.formatTime(new Date(this.formObject.end), 'YYYY-mm-dd HH:MM:SS')
      } else {
        res.start = Number(this.formObject.start)
        res.end = this.formObject.end === null ? '∞' : Number(this.formObject.end)
      }

      this.$emit('confirm-function', res)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 0;
  font-size: 16px;
  .tax-rate {
    margin-bottom: 10px;
    background-color: #eef2f6;
    padding: 5px;
    border: 1px solid #e9e9eb;
    border-radius: 4px;
  }
}
</style>
