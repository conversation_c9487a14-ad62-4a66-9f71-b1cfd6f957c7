<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <mt-input
        :id="fieldName"
        v-model="data[fieldName]"
        disabled
        :placeholder="headerTxt"
      ></mt-input>
      <mt-icon
        style="width: 20px"
        name="icon_list_refuse"
        @click.native="handleClear"
        v-if="allowEditing"
      ></mt-icon>
      <mt-icon
        v-if="allowEditing"
        style="width: 20px"
        name="icon_input_search"
        @click.native="showDialog"
      ></mt-icon>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    objtype: {
      type: String,
      default: ''
    },
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      data: {},
      fieldName: 'itemCode',
      headerTxt: '',
      allowEditing: true
    }
  },
  mounted() {
    // 初始回填input值
    this.value && this.$set(this.data, 'itemCode', this.value)
  },
  methods: {
    handleClear() {
      this.$set(this.data, 'itemCode', null)
      this.$emit('change', this.data)
      this.$emit('input', this.data.itemCode)
    },
    showDialog() {
      // if (
      //   sessionStorage.getItem("organizationId") == "" ||
      //   sessionStorage.getItem("organizationId") == "undefined"
      // ) {
      //   this.$toast({
      //     content: this.$t("当前未选择工厂，不可以选择物料"),
      //     type: "warning",
      //   });
      //   return;
      // }
      let objType = sessionStorage.getItem('sourcingObjType')
      console.log('objType', objType)
      this.$dialog({
        modal: () => import('./components/selectGrid'),
        data: {
          title: this.$t('选择PCB'),
          objType: objType
        },
        success: (data) => {
          this.$set(this.data, 'itemCode', data[0]['itemCode'])
          this.$emit('change', this.data)
          this.$emit('input', this.data.itemCode)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;
  /deep/ .mt-input {
    width: 100%;
  }
  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
</style>
