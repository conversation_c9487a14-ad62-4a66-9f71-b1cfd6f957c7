<template>
  <div
    :class="['cell-changed', 'cell-changed-disabled' && !data.column.allowEditing]"
    id="cell-changed"
  >
    <mt-input
      :id="data.column.field"
      style="display: none"
      :value="data[data.column.field]"
    ></mt-input>
    <mt-input v-model="data[data.column.field]" :disabled="!data.column.allowEditing"></mt-input>
  </div>
</template>

<script>
import { setValueByPath } from '@/utils/obj'

export default {
  data() {
    return {
      data: {},
      fieldName: ''
    }
  },
  mounted() {
    this.fieldName = this.data.column.field
    this.$bus.$on(`${this.fieldName}Change`, (txt) => {
      setValueByPath(this.data, this.fieldName, txt)
      this.$forceUpdate() //更新
    })
  },
  beforeDestroy() {
    this.$bus.$off(`${this.fieldName}Change`)
  }
}
</script>

<style lang="scss" scoped>
#cell-changed /deep/ .e-input.e-disabled {
  height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
  padding-left: 10px !important;
  background: #f5f5f5 !important;
}
</style>
