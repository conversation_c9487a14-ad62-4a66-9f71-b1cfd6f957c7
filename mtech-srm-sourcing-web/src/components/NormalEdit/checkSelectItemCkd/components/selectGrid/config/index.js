import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '0',
    field: 'purOrgCode',
    headerText: i18n.t('采购组织编码')
  },
  {
    width: '150',
    field: 'purOrgName',
    headerText: i18n.t('采购组织'),
    valueAccessor: (field, data) => {
      return data.purOrgCode + '-' + data.purOrgName
    }
  },
  {
    width: '0',
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '150',
    field: 'siteName',
    headerText: i18n.t('工厂'),
    valueAccessor: (field, data) => {
      return data.siteCode + '-' + data.siteName
    }
  },
  {
    width: '0',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '150',
    field: 'supplierName',
    headerText: i18n.t('供应商'),
    valueAccessor: (field, data) => {
      return data.supplierCode + '-' + data.supplierName
    }
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '150',
    field: 'categoryName',
    headerText: i18n.t('品类')
  },
  {
    width: '120',
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    width: '120',
    field: 'currencyCode',
    headerText: i18n.t('币种编码')
  },
  {
    width: '120',
    field: 'priceUnit',
    headerText: i18n.t('价格单位')
  },
  {
    width: '150',
    field: 'untaxedUnitPrice',
    headerText: i18n.t('单价（未税）')
  },
  {
    width: '150',
    field: 'exchangeUntaxedUnitPrice',
    headerText: i18n.t('换算后单价（未税）')
  }
]
