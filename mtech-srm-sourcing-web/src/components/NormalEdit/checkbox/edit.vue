<template>
  <mt-checkbox class="checkbox-item" :checked="value" @change="change"></mt-checkbox>
</template>

<script>
export default {
  props: {
    editInstance: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      data: {},
      value: ''
    }
  },

  computed: {},
  mounted() {
    this.value = this.editInstance.rowData?.vmi === 'N' ? false : true
  },
  methods: {
    change(e) {
      let _value = e.checked ? 'Y' : 'N'
      this.editInstance.setValueByField('vmi', _value)
    }
  }
}
</script>
