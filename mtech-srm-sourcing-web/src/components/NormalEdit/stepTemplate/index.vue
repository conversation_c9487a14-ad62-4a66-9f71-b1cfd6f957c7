<template>
  <div class="viewBox">
    <div>阶梯报价（{{ dataSource.length }}）</div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      data: {},
      dataSource: []
    }
  },
  computed: {
    field() {
      const _fieldArr = this.data?.column?.field.split('biddingItemDTO.')
      return _fieldArr.length === 2
        ? _fieldArr[1]
        : _fieldArr[0] === 'stepNum' // stepNum特殊处理，取值startValue
        ? 'startValue'
        : _fieldArr[0]
    },
    fieldView() {
      return this.data.stepQuoteType == 0 && this.field === 'startValue'
        ? '暂无阶梯报价方案'
        : this.data[this.field]
    }
  },
  filters: {
    fieldView(val) {
      if (val && val == -999) return '******'
      if (val || val == 0) return val
      return '-'
    }
  },
  mounted() {
    // 采供方取值不通
    this.dataSource = this.data.itemStages
      ? [...this.data.itemStages]
      : this.data.itemStageList
      ? [...this.data.itemStageList]
      : []
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.viewBox {
  width: calc(100% + 20px);
  margin-left: -10px;
  margin-right: -10px;
  &-btn {
    width: 100%;
    display: flex;
    justify-content: center;
    margin: 3px 0;
    div {
      color: rgba(0, 70, 156, 1);
    }
  }
}
/deep/ .mt-data-grid {
  .e-grid {
    th.e-headercell,
    td.e-rowcell {
      font-size: 8px !important;
      height: 30px;
    }
  }
}
/deep/ .e-headertext {
  font-size: 8px !important;
}
/deep/ .e-headercelldiv {
  height: 12px !important;
  line-height: 12px !important;
  padding: 0 !important;
}
</style>
