<template>
  <!-- 结构阶梯格式编辑 -->
  <div class="cellBox">
    <div class="cellBox-btn">
      <div @click="showDialog">{{ data.stepQuoteName | listNumFormat }}</div>
    </div>
  </div>
</template>

<script>
import { i18n } from '@/main.js'
export default {
  data() {
    return {
      data: {},
      dataSource: [],
      defaultDataSource: [],
      sourcingObjType: ''
    }
  },
  filters: {
    listNumFormat(value) {
      if (value && value.length > 0) {
        return i18n.t('编辑阶梯报价方案')
      } else {
        return i18n.t('添加阶梯报价方案')
      }
    }
  },
  mounted() {
    // 避免新增询价单后，通过路由无法拿到sourcingObjType
    const param = JSON.parse(sessionStorage.getItem('referItemParam'))
    this.sourcingObjType = param?.sourcingObjType

    this.getDefaultDataSource()
    this.$nextTick(() => {
      if (this.data.stepQuoteName && this.data.stepQuoteName.length) {
        if (typeof this.data.stepQuoteName[0] === 'string') {
          this.dataSource = JSON.parse(this.data.stepQuoteName)
        } else {
          this.dataSource = this.data.stepQuoteName
        }
      }
    })
    this.$bus.$on('changeStepQuote', (data) => {
      this.data.stepQuote = data
      this.initStepNum()
    })
    this.$bus.$on('changeStepQuoteType', (data) => {
      this.data.stepQuoteType = data
    })
    // 根据参考物料带出阶梯报价信息，为了不退出编辑状态即可看到最新的值（data中的数据需要退出编辑状态后再进入才能看到更新）
    this.$bus.$on('extStageSaveRequestsChange', (list) => {
      const arr = ['power_panel', 'module_out_going']
      if (arr.includes(this.sourcingObjType) && this.data.stepQuote === 1) {
        const itemStageList = []
        list.forEach((item) => {
          itemStageList.push({ startValue: item.startValue, remark: '' })
        })
        this.$set(this.data, 'itemStageList', itemStageList)
        this.$set(this.data, 'stepQuoteName', JSON.stringify(itemStageList))
      }
    })
  },
  methods: {
    // 阶梯数量默认值
    async getDefaultDataSource() {
      const sourcingObjTypeList = [
        'artist_component', // 美工件
        'structure_component', // 结构件
        'power_panel', // 电源板外发
        'module_out_going' // 整机外发
      ]
      if (!sourcingObjTypeList.includes(this.sourcingObjType)) {
        return
      }
      const res = await this.$API.masterData.dictionaryGetList({
        dictCode: this.sourcingObjType
      })
      this.defaultDataSource = []
      res.code === 200 &&
        res.data.map((item) => {
          this.defaultDataSource.push({
            startValue: item.itemCode,
            remark: ''
          })
        })
    },
    // 外发电源板 - 初始化阶梯数量
    initStepNum() {
      const arr = [
        'power_panel', // 外发电源板
        'module_out_going' // 整机外发
      ]
      if (!arr.includes(this.sourcingObjType)) {
        return
      }
      if (this.data.stepQuote === 1) {
        this.dataSource = this.defaultDataSource
      } else {
        this.dataSource = []
      }
      this.$bus.$emit('itemStageListChange', this.dataSource)
      this.$set(this.data, 'stepQuoteName', JSON.stringify(this.dataSource))
    },
    showDialog() {
      if (this.data.stepQuoteType == '' && this.data.stepQuoteType !== 0) {
        this.$toast({
          content: this.$t('请先选择阶梯报价类型'),
          type: 'warning'
        })
        return
      }
      if (this.data.stepQuote !== 1) {
        this.$toast({
          content: this.$t('请先选择是阶梯报价'),
          type: 'warning'
        })
        return
      }
      let arr = [...this.defaultDataSource]
      if (this.data.stepQuoteName && this.data.stepQuoteName.length) {
        arr =
          typeof this.data.stepQuoteName[0] === 'string'
            ? JSON.parse(this.data.stepQuoteName)
            : this.data.stepQuoteName
      }
      this.$dialog({
        modal: () => import('./components/stepDialog.vue'),
        data: {
          title: '阶梯数量',
          dataSource: arr
        },
        success: (data) => {
          this.$set(this.data, 'stepQuoteName', JSON.stringify(data))
          this.dataSource = JSON.parse(this.data.stepQuoteName)
        }
      })
    }
  }
}
</script>
<style lang="scss">
.cellBox {
  width: calc(100% + 20px);
  margin-left: -10px;
  margin-right: -10px;
  &-btn {
    width: 100%;
    display: flex;
    justify-content: center;
    margin: 3px 0;
    div {
      color: rgba(0, 70, 156, 1);
    }
  }
  p {
    border-left: 1px solid #e0e0e0;
    border-right: 1px solid #e0e0e0;
    border-bottom: 1px solid #e0e0e0;
    line-height: 30px;
    text-align: center;
    margin-bottom: 0 !important;
  }
}
/deep/ .mt-data-grid {
  .e-grid {
    th.e-headercell,
    td.e-rowcell {
      font-size: 8px !important;
      height: 30px;
    }
  }
}
/deep/ .e-headertext {
  font-size: 8px !important;
}
/deep/ .e-headercelldiv {
  height: 12px !important;
  line-height: 12px !important;
  padding: 0 !important;
}
</style>
