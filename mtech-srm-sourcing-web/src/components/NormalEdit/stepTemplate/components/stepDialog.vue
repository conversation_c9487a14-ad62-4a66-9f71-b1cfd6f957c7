<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
      />
    </div>
  </mt-dialog>
</template>

<script>
import { columnData } from './config'
import cloneDeep from 'lodash/cloneDeep'
export default {
  data() {
    return {
      data: {},
      defaultDataSource: [
        { startValue: 1, remark: '' },
        { startValue: 500, remark: '' },
        { startValue: 2000, remark: '' },
        { startValue: 3000, remark: '' }
      ],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [['Add', 'Delete']]
          },
          gridId: '289f0b7c-bc65-4592-a14b-587b8d6bb109',
          grid: {
            columnData: columnData,
            dataSource: [],
            allowFiltering: false,
            allowPaging: false,
            allowSorting: false,
            editSettings: {
              allowAdding: true,
              allowEditing: true,
              allowDeleting: true,
              allowEditOnDblClick: true,
              newRowPosition: 'Bottom'
            }
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    showLeft() {
      return false
    },
    dataSource() {
      // 默认为1-500-2000-3000
      const _dataSource = this.modalData.dataSource
        ? this.modalData.dataSource
        : [...this.defaultDataSource]
      return cloneDeep(_dataSource)
    }
  },
  watch: {
    dataSource: {
      handler(val) {
        this.$set(this.pageConfig[0].grid, 'dataSource', val)
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    // 结束编辑状态
    endEdit() {
      let _current = this.$refs.templateRef.getCurrentTabRef()
      _current?.grid.endEdit()
      _current?.grid.hideSpinner()
    },

    confirm() {
      this.endEdit()
      let dataSource = this.$refs.templateRef.getCurrentTabRef().grid.getCurrentViewRecords()
      for (let i = 0; i < dataSource.length; i++) {
        let _currentNum = dataSource[i].startValue
          ? Number(dataSource[i].startValue)
          : dataSource[i].startValue
        if (!_currentNum && _currentNum !== 0) {
          this.$toast({
            content: this.$t('阶梯数量不能为空'),
            type: 'warning'
          })
          return
        }
        if (i > 0 && _currentNum <= Number(dataSource[i - 1].startValue)) {
          this.$toast({
            content: this.$t('阶梯数量不能小于等于上一行值'),
            type: 'warning'
          })
          return
        }
      }

      this.$bus.$emit('itemStageListChange', dataSource)
      this.$emit('confirm-function', dataSource)
    },
    cancel() {
      this.$emit('cancel-function')
    },
    handleClickToolBar(e) {
      // this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      if (e.toolbar.id == 'Add') {
        let _currentTabRef = this.$refs.templateRef.getCurrentTabRef()
        let _selectRecords = _currentTabRef?.grid?.getCurrentViewRecords() ?? []
        this.pageConfig[0].grid.dataSource.push({
          startValue: '',
          remark: ''
        })
        // 未知原因新增下一行时上一行的结束值与说明丢失
        if (_selectRecords.length > 0) {
          let startValue = _selectRecords[_selectRecords.length - 1].startValue
          let remark = _selectRecords[_selectRecords.length - 1].remark
          let _index = this.pageConfig[0].grid.dataSource.length - 2
          this.pageConfig[0].grid.dataSource[_index].startValue = startValue
          this.pageConfig[0].grid.dataSource[_index].remark = remark
        }
        return
      }
      if (e.toolbar.id == 'Delete') {
        let _selectGridRecords = e.gridRef.getMtechGridRecords()
        if (_selectGridRecords?.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        this.handleBatchDelete(_selectGridRecords)
      }
    },
    //删除
    handleBatchDelete(_records) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          let _currentTabRef = this.$refs.templateRef.getCurrentTabRef()
          let _selectRecords = _currentTabRef?.grid?.getCurrentViewRecords() ?? []
          _records.forEach((x) => {
            _selectRecords.forEach((v, i) => {
              if (JSON.stringify(x) === JSON.stringify(v)) {
                _selectRecords.splice(i, 1)
              }
            })
          })
          this.$set(this.pageConfig[0].grid, 'dataSource', _selectRecords)
        }
      })
    },
    changeTab(tab) {
      this.tabIndex = tab.index
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
