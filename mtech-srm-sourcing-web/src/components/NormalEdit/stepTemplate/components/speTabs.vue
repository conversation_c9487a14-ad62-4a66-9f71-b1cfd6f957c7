<template>
  <div class="my-tabs">
    <div class="tabs-bar">
      <div
        class="tabs-tab"
        v-for="(tab, i) in tabList"
        :key="i"
        :class="[tabIndex == tab.index ? 'tabs-active' : '']"
        @click="changeTab(tab)"
      >
        {{ tab.title }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Mytabs',
  props: {
    tabList: {
      type: Array,
      default: () => {
        return []
      }
    },
    tabIndex: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {}
  },
  methods: {
    changeTab: function (tab) {
      this.$emit('changeTab', tab)
    }
  }
}
</script>

<style lang="scss" scoped>
.my-tabs {
  font-size: 14px;
  color: #444;
  height: 100%;
}
.tabs-bar {
  border-right: 2px solid #eee;
  padding: 5px 0;
  height: 100%;
  width: 150px;
  overflow-y: scroll;
  margin-right: 5px;
}
.tabs-bar::-webkit-scrollbar {
  display: block;
}
.tabs-tab {
  min-height: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  cursor: pointer;
  width: 100%;
  padding: 5px;
  margin-bottom: 5px;
}
.tabs-active {
  background: rgb(97, 140, 196);
  color: white;
  border-radius: 2px;
}
</style>
