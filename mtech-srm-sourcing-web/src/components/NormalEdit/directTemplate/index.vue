<template>
  <div class="cellBox">
    <mt-input
      id="biddingItemStageStrName"
      style="display: none"
      :value="data.biddingItemStageStrName"
    ></mt-input>
    <div style="margin-top: 3px" v-show="this.grid.dataSource && this.grid.dataSource.length > 0">
      <mt-data-grid v-bind="grid" height="100px" />
    </div>
    <div class="cellBox-btn" v-show="busInfo">
      <div @click="showDialog">
        {{ data.biddingItemStageStrName | listNumFormat }}
      </div>
    </div>
  </div>
</template>

<script>
import { i18n } from '@/main.js'
export default {
  data() {
    return {
      data: {},
      grid: {
        allowFiltering: false,
        columnData: [
          {
            field: 'startValue',
            headerText: this.$t('起始值(≥)')
          },
          {
            field: 'endValue',
            headerText: this.$t('结束值(<)')
          },
          {
            field: 'untaxedUnitPrice',
            headerText: this.$t('单价(未税)')
          }
        ],
        dataSource: []
      },
      busInfo: true
    }
  },
  filters: {
    listNumFormat(value) {
      if (value && value.length > 0) {
        return i18n.t('编辑阶梯报价方案')
        // return JSON.parse(value).length;
      } else {
        return i18n.t('添加阶梯报价方案')
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.data.biddingItemStageStrName && this.data.biddingItemStageStrName.length) {
        this.grid.dataSource = JSON.parse(this.data.biddingItemStageStrName)
      }
    })
    this.$bus.$on('changeStepQuote', (data) => {
      this.data.stepQuote = data
    })
    this.$bus.$on('changeStepQuoteType', (data) => {
      this.data.stepQuoteType = data
    })
    this.$bus.$on('pointBiddingItemStageChange', (data) => {
      this.busInfo = true
      this.grid.dataSource = data
    })
    this.$bus.$on('clearPriceReferRfxCodeAndLineNo', () => {
      this.busInfo = false
      this.grid.dataSource = []
    })
  },
  methods: {
    showDialog() {
      if (this.data.stepQuoteType == '') {
        this.$toast({
          content: this.$t('请先选择阶梯报价类型'),
          type: 'warning'
        })
        return
      }
      if (this.data.stepQuote !== 1) {
        this.$toast({
          content: this.$t('请先选择是阶梯报价'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        modal: () => import('./components/editConstructionDialog'),
        data: {
          title: '阶梯报价定义',
          source: this.$route.query.source,
          rowData: this.data
        },
        success: (data) => {
          this.$set(this.data, 'biddingItemStageStrName', JSON.stringify(data))
          this.grid.dataSource = JSON.parse(this.data.biddingItemStageStrName)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.cellBox {
  width: 100%;
  padding-top: 3px;
  &-btn {
    width: 100%;
    display: flex;
    justify-content: center;
    margin: 3px 0;
    div {
      color: rgba(0, 70, 156, 1);
    }
  }
}
/deep/ .mt-data-grid {
  .e-grid {
    th.e-headercell,
    td.e-rowcell {
      font-size: 8px !important;
      height: 30px;
    }
  }
}
/deep/ .e-headertext {
  font-size: 8px !important;
}
/deep/ .e-headercelldiv {
  height: 12px !important;
  line-height: 12px !important;
  padding: 0 !important;
}
</style>
