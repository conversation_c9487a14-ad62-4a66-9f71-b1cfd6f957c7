import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'costFactorSpecification',
    headerText: i18n.t('规格'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'costFactorBrand',
    headerText: i18n.t('品牌'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'costFactorProperty',
    headerText: i18n.t('属性'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'companyName',
    headerText: i18n.t('单位'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'costFactorGroup',
    headerText: i18n.t('组别'),
    cssClass: 'click'
  }
]
