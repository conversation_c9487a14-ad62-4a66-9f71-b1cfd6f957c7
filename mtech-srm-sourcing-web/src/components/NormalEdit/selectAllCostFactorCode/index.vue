<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <mt-input
        :id="fieldName"
        v-model="data[fieldName]"
        disabled
        :width="130"
        :placeholder="headerTxt"
      ></mt-input>
      <mt-icon
        style="width: 20px"
        name="icon_list_refuse"
        @click.native="handleClear"
        v-if="allowEditing"
      ></mt-icon>
      <mt-icon
        v-if="allowEditing"
        style="width: 20px"
        name="icon_input_search"
        @click.native="showDialog"
      ></mt-icon>
    </div>
  </div>
</template>

<script>
import { getValueByPath } from '@/utils/obj'

export default {
  data() {
    return {
      data: {},
      fieldName: '',
      headerTxt: '',
      allowEditing: true
    }
  },
  mounted() {
    this.fieldName = this.data.column.field
    this.allowEditing = this.data.column.allowEditing === false ? false : true
    if (!this.allowEditing) return
  },
  methods: {
    handleClear() {
      this.$set(this.data, this.fieldName, null)
      const fieldMap = {
        itemId: 'id',
        itemName: 'itemName',
        spec: 'itemDescription', // 规格描述
        material: 'materialTypeName', // 材质
        unitName: 'baseMeasureUnitName', // 单位
        categoryId: 'categoryResponse.id', // 品类ID
        categoryCode: 'categoryResponse.categoryCode', // 品类编码
        categoryName: 'categoryResponse.categoryName' //品类名称
      }
      Object.entries(fieldMap).map(([field]) => {
        this.$bus.$emit(`${field}Change`, null)
      })
    },
    showDialog() {
      // if (
      //   sessionStorage.getItem("organizationId") == "" ||
      //   sessionStorage.getItem("organizationId") == "undefined" ||
      //   !sessionStorage.getItem("organizationId")
      // ) {
      //   this.$toast({
      //     content: this.$t("当前未选择工厂，不可以选择物料"),
      //     type: "warning",
      //   });
      //   return;
      // }
      this.$dialog({
        modal: () => import('./components/selectGrid'),
        data: {
          title: this.$t('选择物料')
          // organizationId: sessionStorage.getItem("organizationId"),
        },
        success: (data) => {
          this.$set(this.data, this.fieldName, data[0]['itemCode'])
          const fieldMap = {
            itemId: 'id',
            itemName: 'itemName',
            itemCode: 'itemCode',
            costFactorName: 'itemName',
            spec: 'itemDescription', // 规格描述
            material: 'materialTypeName', // 材质
            unitName: 'baseMeasureUnitName', // 单位
            categoryId: 'categoryResponse.id', //品类名称
            categoryCode: 'categoryResponse.categoryCode', //品类名称
            categoryName: 'categoryResponse.categoryName', //品类名称
            // purUnitName: "purchaseUnitName", //采购单位
            priceUnitName: 'purchaseUnitName'
          }
          if (this.fieldName == 'costFactorCode') {
            delete fieldMap.itemName
          }
          Object.entries(fieldMap).map(([field, key]) => {
            this.$bus.$emit(`${field}Change`, getValueByPath(data[0], key))
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
</style>
