<template>
  <div class="cell-upload" :id="'cell-upload-' + data.index">
    <mt-input id="supplierDrawing" style="display: none" :value="data.supplierDrawing"></mt-input>
    <div @click="showFileBaseInfo" class="cell-operable-title">
      {{ data.supplierDrawing | listNumFormat }}
    </div>

    <!-- 需求附件弹窗 -->
    <uploader-dialog @change="fileChange" @confirm="setFile" ref="uploaderDialog"></uploader-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { i18n } from '@/main.js'
// 草稿或审批拒绝状态才能再次上传
export default {
  components: {
    UploaderDialog: () => import('COMPONENTS/NormalEdit/Upload/uploaderDialog.vue')
  },
  data() {
    return {
      data: {
        // supplierDrawing: {},
      },
      headerStatus: null, // 0-草稿，1. 待审批，2. 审批通过，3. 审批拒绝，4. 关闭
      uploadFileList: [] // 上传的附件(初始值赋值之前上传过的)
    }
  },
  filters: {
    byteToKB: (value) => Math.floor((value / 1024) * 100) / 100,
    listNumFormat(value) {
      if (value && value.length > 0) {
        return `${i18n.t('查看')}(${JSON.parse(value).length})`
        // return JSON.parse(value).length;
      } else {
        return i18n.t('点击上传')
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.data.supplierDrawing && this.data.supplierDrawing.length) {
        this.uploadFileList = JSON.parse(this.data.supplierDrawing)
      }
    })
  },
  methods: {
    showFileBaseInfo() {
      const dialogParams = {
        fileData: cloneDeep(this.uploadFileList),
        isView: false, //是否可上传
        required: false, // 是否必须
        title: i18n.t('附件')
      }
      this.$refs.uploaderDialog.dialogInit(dialogParams)
    },

    // 行附件弹窗内文件变动
    fileChange(data) {
      this.uploadFileList = data
    },
    // 点击行附件上传的确认按钮
    setFile() {
      this.$set(this.data, 'supplierDrawing', JSON.stringify(this.uploadFileList))
      this.$parent.$emit('fileUpload', {
        field: 'supplierDrawing',
        value: JSON.stringify(this.uploadFileList),
        data: this.data
      })
      // this.$parent.$emit("confirm-function", this.data.supplierDrawing);
    }
  }
}
</script>

<style scoped>
.cell-operable-title {
  display: inline-block;
  padding: 10px;

  color: #00469c;
  font-size: 14px;
  cursor: pointer;
}
</style>
