<template>
  <div>
    <mt-input v-if="type" disabled :id="fieldName" v-model="brand"></mt-input>
    <mt-select
      v-else
      :id="fieldName"
      :allow-filtering="true"
      :data-source="dataSource"
      :fields="{ text: 'brand', value: 'brand' }"
      :value="brandName"
      @select="handleSelectChange"
    ></mt-select>
  </div>
</template>

<script>
export default {
  props: {
    dataSource: {
      type: Array,
      default: () => []
    },
    editInstance: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      brand: '',
      brandName: '',
      fieldName: 'biddingItemDTO.brandName'
    }
  },
  computed: {
    type() {
      // 如果采方填了品牌，供方禁用
      return this.brand ? true : false
    }
  },
  mounted() {
    this.brand = this.editInstance.getValueByField('brand')
    this.brandName = this.editInstance.getValueByField('biddingItemDTO.brandName')
  },
  methods: {
    handleSelectChange(e) {
      let value = e.itemData?.brand
      this.editInstance.setValueByField(this.fieldName, value)
    }
  }
}
</script>
