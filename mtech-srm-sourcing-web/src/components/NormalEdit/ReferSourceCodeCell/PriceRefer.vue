<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-template-page ref="templateRef" :template-config="pageConfig" />
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: [],
          gridId: this.$permission.gridId.purchase.priceRefer,
          grid: {
            allowFiltering: true,
            allowSelection: true,
            selectionSettings: {
              type: 'Multiple',
              mode: 'Row'
            },
            columnData: [
              {
                field: 'referSourceCode',
                headerText: this.$t('来源单号'),
                width: 200
              },
              {
                field: 'itemCode',
                headerText: this.$t('物料编码'),
                width: 200
              },
              {
                field: 'itemName',
                headerText: this.$t('物料名称'),
                width: 200
              },
              {
                field: 'spec',
                headerText: this.$t('规格'),
                width: 200
              },
              {
                field: 'taxedUnitPrice',
                headerText: this.$t('单价(含税)'),
                width: 200
              },
              {
                field: 'untaxedUnitPrice',
                headerText: this.$t('单价(不含税)'),
                width: 200
              },
              {
                field: 'bidTaxRateCode',
                headerText: this.$t('税率编码'),
                width: 200
              },
              {
                field: 'bidTaxRateName',
                headerText: this.$t('税率名称'),
                width: 200
              },
              {
                field: 'bidTaxRateValue',
                headerText: this.$t('税率'),
                width: 200
              },
              {
                field: 'supplierCode',
                headerText: this.$t('供应商编码'),
                width: 200
              },
              {
                field: 'supplierName',
                headerText: this.$t('供应商名称'),
                width: 200
              }
            ],
            allowSorting: false,
            recordDoubleClick: this.recordDoubleClick,
            asyncConfig: {
              url: this.$API.rfxList.priceReferList,
              params: { type: 2 },
              defaultRules: [],
              rules: [
                {
                  label: this.$t('公司编码'),
                  field: 'companyCode',
                  type: 'string',
                  operator: 'equal',
                  value: this.modalData.companyCode
                },
                {
                  label: this.$t('阶梯类型'),
                  field: 'stageType',
                  type: 'string',
                  operator: 'equal',
                  value: this.modalData.stepQuoteType
                },
                {
                  label: this.$t('价格分类'),
                  field: 'priceClassify',
                  type: 'number',
                  operator: 'equal',
                  value: 0
                }
              ]
            }
          }
        }
      ],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: true, content: this.$t('确定') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  created() {
    // const editInstance = this.modalData.editInstance;
    // this.pageConfig[0].grid.asyncConfig.defaultRules = [
    //   {
    //     field: "referChannel",
    //     type: "string",
    //     operator: "equal",
    //     value: editInstance.getValueByField("itemExtMap.referChannel") + "",
    //   },
    //   {
    //     field: "itemCode",
    //     type: "string",
    //     operator: "equal",
    //     value: editInstance.getValueByField("itemCode"),
    //   },
    // ];
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    cancel() {
      this.$emit('cancel-function')
    },
    confirm() {
      const records = this.$refs.templateRef.getCurrentTabRef().grid.getSelectedRecords()
      if (records.length === 0) {
        this.cancel()
        return
      }
      const item = records[0]
      this.$emit('confirm-function', item)
    },
    recordDoubleClick(e) {
      this.$emit('confirm-function', e.rowData)
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  padding: 20px 0 0 0;
}
</style>
