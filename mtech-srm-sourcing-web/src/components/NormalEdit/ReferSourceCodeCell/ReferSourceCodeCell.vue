<template>
  <div class="refer-source-code">
    <mt-input :value="value" disabled :width="130"></mt-input>
    <mt-icon v-if="!disabled" style="width: 20px" name="icon_list_refuse" @click.native="clean" />
    <mt-icon
      v-if="!disabled"
      style="width: 20px"
      name="icon_input_search"
      @click.native="priceRefer"
    />
  </div>
</template>

<script>
// ****************原本CKD（combination）来源单号的选择器，目前改造为通过选择参考物料编码，经过确认只有CKD（combination）使用
export default {
  props: {
    value: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    editInstance: {
      type: Object,
      default: null
    },
    sourcingObjType: {
      type: String,
      default: ''
    },
    companyCode: {
      type: String,
      default: ''
    }
  },
  methods: {
    priceRefer() {
      let stepQuoteType = -1
      if (this.stepQuoteType || this.stepQuoteType === 0) {
        stepQuoteType = this.stepQuoteType
      }
      this.$dialog({
        modal: () => import('./PriceRefer'),
        data: {
          title: this.$t('选择物料'),
          editInstance: this.editInstance,
          stepQuoteType,
          companyCode: this.companyCode
        },
        success: (item) => {
          const editInstance = this.editInstance
          // rfx_item_ext referItemUnitPriceUntaxed 参考物料单价（未税）
          // rfx_item_ext referItemUnitPriceTaxed 参考物料单价（含税）
          editInstance.setValueByField(
            'itemExtMap.referItemUnitPriceUntaxed',
            item.untaxedUnitPrice
          )
          editInstance.setValueByField('itemExtMap.referItemUnitPriceTaxed', item.taxedUnitPrice)
          // mt_supplier_rfx_item_ext referItemRateCode 参考税率编码  itemExtMap
          // mt_supplier_rfx_item_ext referItemRateValue 参考税率值
          // mt_supplier_rfx_item_ext referItemRateName 参考税率名称
          editInstance.setValueByField('itemExtMap.referItemRateCode', item.bidTaxRateCode)
          editInstance.setValueByField('itemExtMap.referItemRateName', item.bidTaxRateName)

          editInstance.setValueByField('itemExtMap.referItemRateValue', item.bidTaxRateValue)
          // rfx_item_ext referItemSupplierCode 参考物料供应商编码
          // rfx_item_ext referItemSupplierName 参考物料供应商名称
          editInstance.setValueByField('itemExtMap.referItemSupplierId', item.supplierId)
          editInstance.setValueByField('itemExtMap.referItemSupplierCode', item.supplierCode)
          editInstance.setValueByField('itemExtMap.referItemSupplierName', item.supplierName)
          editInstance.setValueByField('itemExtMap.referItemCode', item.itemCode)
          editInstance.setValueByField('itemExtMap.referItemName', item.itemName)
          editInstance.setValueByField('itemExtMap.referItemSpec', item.spec)
          // editInstance.setValueByField('itemExtMap.referSourceCode', item.referSourceCode)
          this.$emit('input', item.itemCode)
        }
      })
    },
    clean() {
      const ctx = this.editInstance
      ctx.setValueByField('itemExtMap.referItemUnitPriceUntaxed', null)
      ctx.setValueByField('itemExtMap.referItemUnitPriceTaxed', null)
      ctx.setValueByField('itemExtMap.referItemRateCode', null)
      ctx.setValueByField('itemExtMap.referItemRateName', null)
      ctx.setValueByField('itemExtMap.referItemRateValue', null)
      ctx.setValueByField('itemExtMap.referItemSupplierId', null)
      ctx.setValueByField('itemExtMap.referItemSupplierCode', null)
      ctx.setValueByField('itemExtMap.referItemSupplierName', null)
      ctx.setValueByField('itemExtMap.referItemCode', null)
      ctx.setValueByField('itemExtMap.referItemName', null)
      ctx.setValueByField('itemExtMap.referItemSpec', null)
      // ctx.setValueByField('itemExtMap.referSourceCode', null)
      this.$emit('input', null)
    }
  }
}
</script>

<style lang="scss">
.refer-source-code {
  display: flex;
  align-items: center;

  i {
    cursor: pointer;
    margin-left: 10px;
  }
}
</style>
