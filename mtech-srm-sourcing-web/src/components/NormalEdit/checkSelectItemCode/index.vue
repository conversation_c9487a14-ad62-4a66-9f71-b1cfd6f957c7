<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <mt-select
        v-if="showSelect"
        :id="fieldName"
        :allow-filtering="true"
        :data-source="itemsDataSources"
        :filtering="onFiltering"
        :fields="{ text: 'itemCode', value: 'itemCode' }"
        :value="value"
        :width="130"
        @select="handleSelectChange"
        :placeholder="headerTxt"
        popup-width="180px"
      ></mt-select>
      <mt-input
        :id="fieldName"
        :value="value"
        disabled
        :width="130"
        :placeholder="headerTxt"
        v-show="!showSelect"
      ></mt-input>
      <mt-icon
        style="width: 20px"
        name="icon_list_refuse"
        @click.native="handleClear"
        v-if="allowEditing"
      ></mt-icon>
      <mt-icon
        v-if="allowEditing"
        style="width: 20px"
        name="icon_input_search"
        @click.native="showDialog"
      ></mt-icon>
    </div>
  </div>
</template>

<script>
import { getValueByPath } from '@/utils/obj'
import debounce from 'lodash.debounce'
export default {
  props: [
    // eslint-disable-next-line vue/require-prop-types
    'field',
    // eslint-disable-next-line vue/require-prop-types
    'value',
    // eslint-disable-next-line vue/require-prop-types
    'rfxId',
    // eslint-disable-next-line vue/require-prop-types
    'submitTableData',
    // eslint-disable-next-line vue/require-prop-types
    'sourcingType',
    // eslint-disable-next-line vue/require-prop-types
    'sourcingObjType',
    // eslint-disable-next-line vue/require-prop-types
    'source',
    // eslint-disable-next-line vue/require-prop-types
    'companyCode',
    // eslint-disable-next-line vue/require-prop-types
    'priceClassification'
  ],
  data() {
    return {
      // data: {},
      fieldName: '',
      headerTxt: '',
      allowEditing: true,
      itemsDataSources: [],
      showSelect: true,
      notCheckFieldList: ['screenCode', 'tconCode'] // 不校验物料信息的field
    }
  },
  computed: {
    // 校验选择的物料信息
    checkSelectItem() {
      return (
        this.sourcingObjType !== 'module_out_buy' || !this.notCheckFieldList.includes(this.field)
      )
    }
  },
  async mounted() {
    this.showSelect = false
    this.fieldName = this.field
    await this.getDataSource()
    if (!this.allowEditing) return
  },
  methods: {
    async getDataSource() {
      let _param = JSON.parse(sessionStorage.getItem('siteParam'))
      const funcName =
        this.sourcingObjType === 'serial_item' ? 'queryItemsTwoData' : 'queryItemsData'
      if (_param?.organizationId) {
        await this.$API.comparativePrice[funcName]({
          page: { current: 1, size: 20 },
          organizationId: _param.organizationId,
          siteCode: _param.siteCode,
          rfxId: this.rfxId,
          defaultRules: [
            {
              label: '品类编码',
              field: 'itemCode',
              type: 'string',
              operator: 'contains',
              value: this.value || ''
            }
          ]
        })
          .then((res) => {
            this.itemsDataSources.length = 0
            res.data.records.forEach((item) => {
              item.text = `(${item.itemCode})${item.itemName}`
              this.itemsDataSources.push(item)
            })
            this.showSelect = true
          })
          .catch(() => {
            this.showSelect = true
          })
      } else {
        this.showSelect = true
      }
    },
    onFiltering: debounce(function (e) {
      let { text } = e
      let _param = JSON.parse(sessionStorage.getItem('siteParam'))
      const funcName =
        this.sourcingObjType === 'serial_item' ? 'queryItemsTwoData' : 'queryItemsData'
      if (_param?.organizationId) {
        this.$API.comparativePrice[funcName]({
          page: { current: 1, size: 20 },
          organizationId: _param.organizationId,
          siteCode: _param.siteCode,
          rfxId: this.rfxId,
          defaultRules: [
            {
              label: '品类编码',
              field: 'itemCode',
              type: 'string',
              operator: 'contains',
              value: text
            }
          ]
        }).then((res) => {
          let _records = []
          res.data.records.forEach((item) => {
            item.text = `(${item.itemCode})${item.itemName}`
            _records.push(item)
          })
          e.updateData(_records)
        })
      } else {
        this.$toast({
          content: this.$t('当前未选择工厂，不可以选择物料'),
          type: 'warning'
        })
      }
    }, 1000),
    handleSelectChange(e) {
      if (e.e !== null) {
        let _records = [e.itemData]
        if (this.sourcingObjType == 'serial_item') {
          if (this.sourcingType == 'new_products') {
            if (this.priceClassification == 'predict_price') {
              if (_records[0].predictPrice == 1 || _records[0].executePrice == 1) {
                this.$toast({
                  content: this.$t('请选择没有执行价和暂估价记录的物料'),
                  type: 'warning'
                })
                console.log()
                return
              }
            } else if (this.priceClassification == 'srm_price') {
              if (_records[0].srmPrice == 1) {
                this.$toast({
                  content: this.$t('请选择没有srm价格记录的物料'),
                  type: 'warning'
                })
                return
              }
            } else if (this.priceClassification == 'execute_price') {
              if (_records[0].executePrice == 1) {
                this.$toast({
                  content: this.$t('请选择没有执行价记录的物料'),
                  type: 'warning'
                })
                return
              }
            } else if (this.priceClassification == 'basic_price') {
              if (_records[0].basicPrice == 1) {
                this.$toast({
                  content: this.$t('请选择没有基价记录的物料'),
                  type: 'warning'
                })
                return
              }
            } else {
              if (_records[0].isPriceRecord == 1) {
                this.$toast({
                  content: this.$t('请选择没有价格记录的物料'),
                  type: 'warning'
                })
                return
              }
            }
          } else if (this.sourcingType == 'second_inquiry' || this.sourcingType == 'exist') {
            if (_records[0].isPriceRecord == 0) {
              this.$toast({
                content: this.$t('请选择有价格记录的物料'),
                type: 'warning'
              })
              return
            }
          }
          let params = {
            rfxId: this.rfxId,
            itemCodeList: [_records[0].itemCode]
          }

          if (this.hasItemCode(_records[0].itemCode)) {
            return
          }

          if (!this.checkSelectItem) {
            this.confirmEvent(_records)
            return
          }
          this.$API.masterData.checkItemCode(params).then((res) => {
            if (res?.data?.length) {
              this.$dialog({
                data: {
                  title: this.$t('提示'),
                  message: this.$t(`进行中的单据：{0}，已存在物料编码：{1},是否继续添加?`, {
                    0: res.data[0].rfxCode,
                    1: res.data[0].itemCode
                  })
                },
                success: () => {
                  this.confirmEvent(_records)
                }
              })
            } else {
              this.confirmEvent(_records)
            }
          })
          return
        }

        if (this.sourcingType == 'new_products' && this.checkSelectItem) {
          if (this.priceClassification == 'predict_price') {
            if (_records[0].predictPrice == 1 || _records[0].executePrice == 1) {
              this.$toast({
                content: this.$t('请选择没有执行价和暂估价记录的物料'),
                type: 'warning'
              })
              return
            }
          } else if (this.priceClassification == 'srm_price') {
            if (_records[0].srmPrice == 1) {
              this.$toast({
                content: this.$t('请选择没有srm价格记录的物料'),
                type: 'warning'
              })
              return
            }
          } else if (this.priceClassification == 'execute_price') {
            if (_records[0].executePrice == 1) {
              this.$toast({
                content: this.$t('请选择没有执行价记录的物料'),
                type: 'warning'
              })
              return
            }
          } else if (this.priceClassification == 'basic_price') {
            if (_records[0].basicPrice == 1) {
              this.$toast({
                content: this.$t('请选择没有基价记录的物料'),
                type: 'warning'
              })
              return
            }
          } else {
            if (_records[0].isPriceRecord == 1) {
              this.$toast({
                content: this.$t('请选择没有价格记录的物料'),
                type: 'warning'
              })
              return
            }
          }
        } else if (
          (this.sourcingType == 'second_inquiry' || this.sourcingType == 'exist') &&
          this.checkSelectItem
        ) {
          if (_records[0].isPriceRecord == 0 && this.checkSelectItem) {
            this.$toast({
              content: this.$t('请选择有价格记录的物料'),
              type: 'warning'
            })
            return
          }
        }

        let params = {
          rfxId: this.rfxId,
          itemCodeList: [_records[0].itemCode]
        }

        if (this.hasItemCode(_records[0].itemCode)) {
          return
        }

        if (!this.checkSelectItem) {
          this.confirmEvent(_records)
          return
        }
        this.$API.masterData.checkItemCode(params).then((res) => {
          if (res?.data?.length) {
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: this.$t(`进行中的单据：{0}，已存在物料编码：{1},是否继续添加?`, {
                  0: res.data[0].rfxCode,
                  1: res.data[0].itemCode
                })
              },
              success: () => {
                this.confirmEvent(_records)
              }
            })
          } else {
            this.confirmEvent(_records)
          }
        })
      }
    },
    hasItemCode(itemCode) {
      if (this.checkItemCode && this.submitTableData().find((row) => row.itemCode === itemCode)) {
        this.$toast({
          content: this.$t('物料已存在,不可重复添加'),
          type: 'warning'
        })
        return true
      }
      return false
    },
    confirmEvent(_records) {
      let itemGroupId = []
      _records.forEach((e) => {
        itemGroupId = e.id
      })
      let param = {
        id: itemGroupId
      }
      this.$API.quotaConfig.basicDetail(param).then((res) => {
        if (res.data) {
          const fieldMap = {
            purUnitName: 'purchaseUnitName'
          }
          Object.entries(fieldMap).map(([field, key]) => {
            this.$bus.$emit(`${field}Change`, res.data[key])
          })
        }
      })

      this.confirm123(_records)
    },
    confirm123(data) {
      this.$emit('input', data[0]['itemCode'])
      let fieldMap = {
        itemId: 'id',
        itemCode: 'itemCode',
        itemName: 'itemName',
        spec: 'itemDescription', // 规格描述
        material: 'materialTypeName', // 材质
        unitName: 'baseMeasureUnitName', // 单位
        unitCode: 'baseMeasureUnitCode',
        priceUnitName: 'purchaseUnitName',
        priceUnitCode: 'purchaseUnitCode'
      }
      if (data[0].categoryResponse && !['screenCode', 'tconCode'].includes(this.field)) {
        this.$bus.$emit(`categoryResponseChange`, data[0].categoryResponse)
      } else {
        fieldMap = {
          itemId: 'id',
          itemCode: 'itemCode',
          itemName: 'itemName',
          spec: 'itemDescription', // 规格描述
          material: 'materialTypeName', // 材质
          unitName: 'baseMeasureUnitName', // 单位
          unitCode: 'baseMeasureUnitCode',
          priceUnitName: 'purchaseUnitName',
          priceUnitCode: 'purchaseUnitCode'
          // categoryCode: "categoryCode",
          // categoryName: "categoryName",
        }
      }

      // 屏编码、tcon编码不修改其他信息
      if (['screenCode', 'tconCode'].includes(this.field)) {
        this.$bus.$emit(`${this.field}Change`, getValueByPath(data[0], 'itemCode'))
        return
      }

      Object.entries(fieldMap).map(([field, key]) => {
        this.$bus.$emit(`${field}Change`, getValueByPath(data[0], key))
      })
      let categoryResponse = {}
      if (data[0].categoryCode) {
        categoryResponse = {
          categoryName: data[0].categoryName,
          categoryCode: data[0].categoryCode,
          categoryId: data[0].categoryId
        }
      }

      if (data[0].categoryResponse == null) {
        categoryResponse = {
          categoryName: '',
          categoryCode: '',
          categoryId: ''
        }
      } else {
        categoryResponse = {
          categoryName: data[0].categoryResponse.categoryName,
          categoryCode: data[0].categoryResponse.categoryCode,
          categoryId: data[0].categoryResponse.id
        }
      }

      this.$bus.$emit(`categoryResponseChange`, categoryResponse)
    },
    handleClear() {
      this.$emit('input', null)
      const fieldMap = {
        itemId: 'id',
        itemCode: 'itemCode',
        itemName: 'itemName',
        spec: 'itemDescription', // 规格描述
        material: 'materialTypeName', // 材质
        unitName: 'baseMeasureUnitName', // 单位
        unitCode: 'baseMeasureUnitCode'
      }

      // 屏编码、tcon编码不修改其他信息
      if (['screenCode', 'tconCode'].includes(this.field)) {
        this.$bus.$emit(`${this.field}Change`, null)
        return
      }

      this.$bus.$emit(`categoryResponseChange`, null)

      Object.entries(fieldMap).map(([field]) => {
        // this.$bus.$emit(`${field}Change`, null);
        if (
          field === 'itemName' ||
          field === 'itemCode' ||
          field === 'spec' ||
          field === 'material' ||
          field === 'unitName' ||
          field === 'unitCode' ||
          field === 'categoryCode' ||
          field === 'categoryName'
        ) {
          this.$bus.$emit(`${field}Change`, '')
        } else {
          this.$bus.$emit(`${field}Change`, null)
        }
      })
    },
    showDialog() {
      if (sessionStorage.getItem('siteParam') == null && this.sourcingObjType !== 'cost_factor') {
        this.$toast({
          content: this.$t('当前未选择工厂，不可以选择物料'),
          type: 'warning'
        })
        return
      }
      this.showSelect = false
      this.$dialog({
        modal: () => import('./components/selectGrid'),
        data: {
          title: this.$t('选择物料'),
          rfxId: this.rfxId,
          itemCode: this.value,
          submitTableData: this.submitTableData,
          siteParam: JSON.parse(sessionStorage.getItem('siteParam')),
          sourcingType: this.sourcingType,
          sourcingObjType: this.sourcingObjType,
          source: this.source,
          companyCode: this.companyCode,
          priceClassification: this.priceClassification,
          checkSelectItem: this.checkSelectItem
        },
        success: (data) => {
          this.$emit('input', data[0]['itemCode'])
          let fieldMap = {
            itemId: 'id',
            itemCode: 'itemCode',
            itemName: 'itemName',
            spec: 'itemDescription', // 规格描述
            material: 'materialTypeName', // 材质
            unitName: 'baseMeasureUnitName', // 单位
            unitCode: 'baseMeasureUnitCode',
            priceUnitName: 'purchaseUnitName',
            priceUnitCode: 'purchaseUnitCode'
          }
          if (data[0].categoryResponse && !['screenCode', 'tconCode'].includes(this.field)) {
            this.$bus.$emit(`categoryResponseChange`, data[0].categoryResponse)
          } else {
            fieldMap = {
              itemId: 'id',
              itemCode: 'itemCode',
              itemName: 'itemName',
              spec: 'itemDescription', // 规格描述
              material: 'materialTypeName', // 材质
              unitName: 'baseMeasureUnitName', // 单位
              unitCode: 'baseMeasureUnitCode',
              priceUnitName: 'purchaseUnitName',
              priceUnitCode: 'purchaseUnitCode'
              // categoryCode: "categoryCode",
              // categoryName: "categoryName",
            }
          }

          // 屏编码、tcon编码不修改其他信息
          if (['screenCode', 'tconCode'].includes(this.field)) {
            this.$bus.$emit(`${this.field}Change`, getValueByPath(data[0], 'itemCode'))
            this.value = data[0]['itemCode']
            this.getDataSource()
            return
          }

          Object.entries(fieldMap).map(([field, key]) => {
            this.$bus.$emit(`${field}Change`, getValueByPath(data[0], key))
          })
          let categoryResponse = {}
          if (data[0].categoryCode) {
            categoryResponse = {
              categoryName: data[0].categoryName,
              categoryCode: data[0].categoryCode,
              categoryId: data[0].categoryId
            }
          }
          this.$bus.$emit(`categoryResponseChange`, categoryResponse)
          this.value = data[0]['itemCode']
          this.getDataSource()
          // this.showSelect = true
        },
        close: () => {
          // this.value =
          // this.getDataSource()
          this.showSelect = true
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    // justify-content: space-between;

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
.input {
  display: none;
}
</style>
