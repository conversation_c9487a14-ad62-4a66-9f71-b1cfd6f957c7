<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfigTwo"
        v-if="this.modalData.sourcingObjType == 'serial_item'"
      />
      <mt-template-page ref="templateRef" :template-config="pageConfig" v-else />
    </div>
  </mt-dialog>
</template>

<script>
import { utils } from '@mtech-common/utils'
import { columnData, extColumnData, costFactorColumnData } from './config'
import { columnDataTwo } from './configTwo'
export default {
  data() {
    return {
      selectRowsCache: [],
      data: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: [],
          gridId:
            this.$permission.gridId['purchase'][this.modalData.source]['detail']['tabs'][
              'dialog_checkSelectItemCode'
            ],
          grid: {
            allowFiltering: true,
            ignoreFields: ['isPriceRecord'],
            columnData: columnData,
            allowSorting: false,
            allowSelection: true,
            selectionSettings: {
              type: 'Single',
              checkboxOnly: false
            },
            rowSelected: this.rowSelecting,
            rowDataBound: (args) => {
              if (
                this.modalData.sourcingType == '' ||
                this.modalData.sourcingType == null ||
                this.modalData.sourcingType == undefined
              ) {
                return
              } else if (this.modalData.sourcingType == 'new_products') {
                if (this.modalData?.priceClassification == 'predict_price') {
                  if (args.data.predictPrice == 1 || args.data.executePrice == 1) {
                    args.row.classList.add('backgroundRed')
                  }
                } else if (this.modalData?.priceClassification == 'srm_price') {
                  if (args.data.srmPrice == 1) {
                    args.row.classList.add('backgroundRed')
                  }
                } else if (this.modalData?.priceClassification == 'execute_price') {
                  if (args.data.executePrice == 1) {
                    args.row.classList.add('backgroundRed')
                  }
                } else if (this.modalData?.priceClassification == 'basic_price') {
                  if (args.data.basicPrice == 1) {
                    args.row.classList.add('backgroundRed')
                  }
                } else {
                  if (args.data.isPriceRecord == 1) {
                    args.row.classList.add('backgroundRed')
                  }
                }
              } else if (
                this.modalData.sourcingType == 'second_inquiry' ||
                this.modalData.sourcingType == 'exist'
              ) {
                if (args.data.isPriceRecord == 0) {
                  args.row.classList.add('backgroundRed')
                }
              }
            },
            recordDoubleClick: this.recordDoubleClick,
            asyncConfig: {
              // url: this.$API.masterData.getItemListUrlPage,
              // url: this.$API.comparativePrice.queryItems,
              url: null,
              params: {
                organizationId: this.modalData.siteParam.organizationId,
                siteCode: this.modalData.siteParam.siteCode,
                rfxId: this.modalData.rfxId
              },
              afterAsyncData: () => {
                this.selectRowsCache = []
              },
              serializeList: (list) => {
                list.forEach((item) => {
                  if (item.categoryResponse == null) {
                    item.categoryCode = ''
                    item.categoryName = ''
                    item.categoryId = ''
                  } else {
                    item.categoryCode = item.categoryResponse.categoryCode
                    item.categoryName = item.categoryResponse.categoryName
                    item.categoryId = item.categoryResponse.id
                  }
                })
                return list
              }
            }
          }
        }
      ],
      pageConfigTwo: [
        {
          useToolTemplate: false,
          toolbar: [],
          gridId:
            this.$permission.gridId['purchase'][this.modalData.source]['detail']['tabs'][
              'dialog_checkSelectItemCode'
            ],
          grid: {
            allowFiltering: true,
            ignoreFields: ['isPriceRecord'],
            columnData: columnDataTwo,
            allowSorting: false,
            allowSelection: true,
            selectionSettings: {
              type: 'Single',
              checkboxOnly: false
            },
            rowDataBound: (args) => {
              if (
                this.modalData.sourcingType == '' ||
                this.modalData.sourcingType == null ||
                this.modalData.sourcingType == undefined
              ) {
                return
              } else if (this.modalData.sourcingType == 'new_products') {
                if (this.modalData?.priceClassification == 'predict_price') {
                  if (args.data.predictPrice == 1 || args.data.executePrice == 1) {
                    args.row.classList.add('backgroundRed')
                  }
                } else if (this.modalData?.priceClassification == 'srm_price') {
                  if (args.data.srmPrice == 1) {
                    args.row.classList.add('backgroundRed')
                  }
                } else if (this.modalData?.priceClassification == 'execute_price') {
                  if (args.data.executePrice == 1) {
                    args.row.classList.add('backgroundRed')
                  }
                } else if (this.modalData?.priceClassification == 'basic_price') {
                  if (args.data.basicPrice == 1) {
                    args.row.classList.add('backgroundRed')
                  }
                } else {
                  if (args.data.isPriceRecord == 1) {
                    args.row.classList.add('backgroundRed')
                  }
                }
              } else if (
                this.modalData.sourcingType == 'second_inquiry' ||
                this.modalData.sourcingType == 'exist'
              ) {
                if (args.data.isPriceRecord == 0) {
                  args.row.classList.add('backgroundRed')
                }
              }
            },
            recordDoubleClick: this.recordDoubleClick,
            asyncConfig: {
              url: this.$API.comparativePrice.queryItemsTwo,
              params: {
                page: { current: 1, size: 20 },
                organizationId: this.modalData.siteParam.organizationId,
                rfxId: this.modalData.rfxId,
                siteCode: this.modalData.siteParam.siteCode
              },
              serializeList: (list) => {
                list.forEach((item) => {
                  // item.categoryCode = item.categoryCode
                  // item.categoryName = item.categoryName
                  item.categoryId = item.id
                })
                return list
              }
            }
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.getUrl()
    this.$refs['dialog'].ejsRef.show()
    let newCols
    console.log('this.modalData--', this.modalData)
    if (this.modalData?.sourcingObjType == 'cost_factor') {
      newCols = costFactorColumnData
    } else if (this.modalData?.source == 'rfq') {
      if (this.modalData?.sourcingObjType == 'module_out_buy' && !this.modalData.checkSelectItem) {
        let temp = [...columnData]
        temp.pop()
        newCols = [...temp]
      } else {
        newCols = columnData.concat(extColumnData)
      }
    } else {
      newCols = columnData
    }
    if (this.modalData.multiple) {
      let columnDataArr = utils.cloneDeep(newCols)
      columnDataArr.unshift({
        width: '50',
        type: 'checkbox'
      })
      this.$set(this.pageConfig[0].grid, 'columnData', columnDataArr)
      this.$set(this.pageConfig[0].grid, 'recordDoubleClick', null)
    } else {
      this.$set(this.pageConfig[0].grid, 'columnData', newCols)
    }
  },
  methods: {
    getUrl() {
      if (this.modalData?.sourcingObjType == 'cost_factor') {
        this.$set(this.pageConfig[0].grid.asyncConfig, 'url', this.$API.comparativePrice.costFactor)
      } else {
        this.$set(this.pageConfig[0].grid.asyncConfig, 'url', this.$API.comparativePrice.queryItems)
      }
    },
    hasItemCode(itemCode) {
      if (
        this.modalData.checkSelectItem &&
        this.modalData.submitTableData().find((row) => row.itemCode === itemCode)
      ) {
        this.$toast({
          content: this.$t('物料已存在,不可重复添加'),
          type: 'warning'
        })
        return true
      }
      return false
    },
    rowSelecting(e) {
      if (e.data.length == 0 || e.isHeaderCheckboxClicked == undefined || e.target == null) {
        return
      }
      if (!this.modalData.sourcingType) {
        this.selectRowsCache = []
        return
      }
      let grid = this.$refs.templateRef.getCurrentTabRef().grid
      if (e.isHeaderCheckboxClicked) {
        let data = utils.cloneDeep(e.data)
        let rows = []
        for (let index = 0; index < data.length; index++) {
          if (this.modalData.sourcingType == 'new_products') {
            if (data[index].isPriceRecord == 0) {
              rows.push(index)
            }
          } else if (
            this.modalData.sourcingType == 'second_inquiry' ||
            this.modalData.sourcingType == 'exist'
          ) {
            if (data[index].isPriceRecord == 1) {
              rows.push(index)
            }
          }
        }
        this.$nextTick(() => {
          if (JSON.stringify(rows.sort()) == JSON.stringify(this.selectRowsCache.sort())) {
            this.selectRowsCache = []
            grid.selectRows([])
          } else {
            this.selectRowsCache = rows
            grid.selectRows(rows)
          }
        })
        return
      }
      if (this.modalData.sourcingType == 'new_products') {
        if (this.modalData?.priceClassification == 'predict_price') {
          if (e.data.predictPrice == 1 || e.data.executePrice == 1) {
            this.$toast({
              content: this.$t('请选择没有执行价和暂估价记录的物料'),
              type: 'warning'
            })

            this.$nextTick(() => {
              let rowSelected = utils.cloneDeep(e.rowIndexes)
              rowSelected.splice(e.rowIndexes.indexOf(e.rowIndex), 1)
              this.selectRowsCache = rowSelected
              grid.selectRows(rowSelected)
            })
          } else {
            this.selectRowsCache = e.rowIndexes
          }
        } else if (this.modalData?.priceClassification == 'srm_price') {
          if (e.data.srmPrice == 1) {
            this.$toast({
              content: this.$t('请选择没有srm价格记录的物料'),
              type: 'warning'
            })

            this.$nextTick(() => {
              let rowSelected = utils.cloneDeep(e.rowIndexes)
              rowSelected.splice(e.rowIndexes.indexOf(e.rowIndex), 1)
              this.selectRowsCache = rowSelected
              grid.selectRows(rowSelected)
            })
          } else {
            this.selectRowsCache = e.rowIndexes
          }
        } else if (this.modalData?.priceClassification == 'execute_price') {
          if (e.data.executePrice == 1) {
            this.$toast({
              content: this.$t('请选择没有执行价记录的物料'),
              type: 'warning'
            })

            this.$nextTick(() => {
              let rowSelected = utils.cloneDeep(e.rowIndexes)
              rowSelected.splice(e.rowIndexes.indexOf(e.rowIndex), 1)
              this.selectRowsCache = rowSelected
              let records = this.$refs.templateRef.getCurrentTabRef().gridRef.getMtechGridRecords()
              records = records.filter((item) => {
                return item.itemCode !== e.data.itemCode
              })
              this.$refs.templateRef.getCurrentTabRef().gridRef.selectIdRecords = records
              grid.selectRows(rowSelected)
            })
          } else {
            this.selectRowsCache = e.rowIndexes
          }
        } else if (this.modalData?.priceClassification == 'basic_price') {
          if (e.data.basicPrice == 1) {
            this.$toast({
              content: this.$t('请选择没有基价记录的物料'),
              type: 'warning'
            })

            this.$nextTick(() => {
              let rowSelected = utils.cloneDeep(e.rowIndexes)
              rowSelected.splice(e.rowIndexes.indexOf(e.rowIndex), 1)
              this.selectRowsCache = rowSelected
              grid.selectRows(rowSelected)
            })
          } else {
            this.selectRowsCache = e.rowIndexes
          }
        } else {
          if (e.data.isPriceRecord == 1) {
            this.$toast({
              content: this.$t('请选择没有价格记录的物料'),
              type: 'warning'
            })

            this.$nextTick(() => {
              let rowSelected = utils.cloneDeep(e.rowIndexes)
              rowSelected.splice(e.rowIndexes.indexOf(e.rowIndex), 1)
              this.selectRowsCache = rowSelected
              grid.selectRows(rowSelected)
            })
          } else {
            this.selectRowsCache = e.rowIndexes
          }
        }
      } else if (
        this.modalData.sourcingType == 'second_inquiry' ||
        this.modalData.sourcingType == 'exist'
      ) {
        if (e.data.isPriceRecord == 0) {
          this.$toast({
            content: this.$t('请选择有价格记录的物料'),
            type: 'warning'
          })

          this.$nextTick(() => {
            let rowSelected = utils.cloneDeep(e.rowIndexes)
            rowSelected.splice(e.rowIndexes.indexOf(e.rowIndex), 1)
            this.selectRowsCache = rowSelected
            grid.selectRows(rowSelected)
          })
        } else {
          this.selectRowsCache = e.rowIndexes
        }
      }
    },
    recordDoubleClick(e) {
      if (this.modalData.sourcingType == 'new_products' && this.modalData.checkSelectItem) {
        if (this.modalData?.priceClassification == 'predict_price') {
          if (e.rowData.predictPrice == 1 || e.rowData.executePrice == 1) {
            this.$toast({
              content: this.$t('请选择没有执行价和暂估价记录的物料'),
              type: 'warning'
            })
            return
          }
        } else if (this.modalData?.priceClassification == 'srm_price') {
          if (e.rowData.srmPrice == 1) {
            this.$toast({
              content: this.$t('请选择没有srm价格记录的物料'),
              type: 'warning'
            })
            return
          }
        } else if (this.modalData?.priceClassification == 'execute_price') {
          if (e.rowData.executePrice == 1) {
            this.$toast({
              content: this.$t('请选择没有执行价记录的物料'),
              type: 'warning'
            })
            return
          }
        } else if (this.modalData?.priceClassification == 'basic_price') {
          if (e.rowData.basicPrice == 1) {
            this.$toast({
              content: this.$t('请选择没有基价记录的物料'),
              type: 'warning'
            })
            return
          }
        } else {
          if (e.rowData.isPriceRecord == 1) {
            this.$toast({
              content: this.$t('请选择没有价格记录的物料'),
              type: 'warning'
            })
            return
          }
        }
      } else if (
        (this.modalData.sourcingType == 'second_inquiry' ||
          this.modalData.sourcingType == 'exist') &&
        this.modalData.checkSelectItem
      ) {
        if (e.rowData.isPriceRecord == 0 && this.modalData.checkSelectItem) {
          this.$toast({
            content: this.$t('请选择有价格记录的物料'),
            type: 'warning'
          })
          return
        }
      }
      let params = {
        rfxId: this.modalData.rfxId,
        itemCodeList: [e.rowData.itemCode]
      }

      if (this.hasItemCode(e.rowData.itemCode)) {
        return
      }

      if (!this.modalData.checkSelectItem) {
        this.confirmEvent([e.rowData])
        return
      }
      this.$API.masterData.checkItemCode(params).then((res) => {
        if (res?.data?.length) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t(`进行中的单据：{0}，已存在物料编码：{1},是否继续添加?`, {
                0: res.data[0].rfxCode,
                1: res.data[0].itemCode
              })
            },
            success: () => {
              this.confirmEvent([e.rowData])
            }
          })
        } else {
          this.confirmEvent([e.rowData])
        }
      })
    },
    confirm() {
      let _records = []
      if (this.modalData.multiple) {
        _records = this.$refs.templateRef.getCurrentTabRef().gridRef.getMtechGridRecords()
      } else {
        _records = this.$refs.templateRef.getCurrentTabRef().grid.getSelectedRecords()
      }
      if (_records.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (this.modalData.sourcingObjType == 'serial_item' && this.modalData.checkSelectItem) {
        if (this.modalData.sourcingType == 'new_products') {
          if (this.modalData?.priceClassification == 'predict_price') {
            if (_records[0].predictPrice == 1 || _records[0].executePrice == 1) {
              this.$toast({
                content: this.$t('请选择没有执行价和暂估价记录的物料'),
                type: 'warning'
              })
              return
            }
          } else if (this.modalData?.priceClassification == 'srm_price') {
            if (_records[0].srmPrice == 1) {
              this.$toast({
                content: this.$t('请选择没有srm价格记录的物料'),
                type: 'warning'
              })
              return
            }
          } else if (this.modalData?.priceClassification == 'execute_price') {
            if (_records[0].executePrice == 1) {
              this.$toast({
                content: this.$t('请选择没有执行价记录的物料'),
                type: 'warning'
              })
              return
            }
          } else if (this.modalData?.priceClassification == 'basic_price') {
            if (_records[0].basicPrice == 1) {
              this.$toast({
                content: this.$t('请选择没有基价记录的物料'),
                type: 'warning'
              })
              return
            }
          } else {
            if (_records[0].isPriceRecord == 1) {
              this.$toast({
                content: this.$t('请选择没有价格记录的物料'),
                type: 'warning'
              })
              return
            }
          }
        } else if (
          this.modalData.sourcingType == 'second_inquiry' ||
          this.modalData.sourcingType == 'exist'
        ) {
          if (_records[0].isPriceRecord == 0 && this.modalData.checkSelectItem) {
            this.$toast({
              content: this.$t('请选择有价格记录的物料'),
              type: 'warning'
            })
            return
          }
        }
        let params = {
          rfxId: this.modalData.rfxId,
          itemCodeList: [_records[0].itemCode]
        }

        if (this.hasItemCode(_records[0].itemCode)) {
          return
        }

        if (!this.modalData.checkSelectItem) {
          this.confirmEvent(_records)
          return
        }
        this.$API.masterData.checkItemCode(params).then((res) => {
          if (res?.data?.length) {
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: this.$t(`进行中的单据：{0}，已存在物料编码：{1},是否继续添加?`, {
                  0: res.data[0].rfxCode,
                  1: res.data[0].itemCode
                })
              },
              success: () => {
                this.confirmEvent(_records)
              }
            })
          } else {
            this.confirmEvent(_records)
          }
        })
        return
      }
      if (this.modalData.multiple) {
        let itemCodeList = []
        let ids = []
        for (let item of _records) {
          itemCodeList.push(item.itemCode)
          ids.push(item.id)
        }

        this.$API.quotaConfig.basicDetails({ ids: ids }).then((res) => {
          if (res.data) {
            let basicDetailsMap = {}
            for (let item of res.data) {
              basicDetailsMap[item.itemId] = item
            }
            for (let item of _records) {
              item.purUnitId = basicDetailsMap[item.id]
                ? basicDetailsMap[item.id].purchaseUnitId
                : ''
              item.purUnitCode = basicDetailsMap[item.id]
                ? basicDetailsMap[item.id].purchaseUnitCode
                : ''
              item.purUnitName = basicDetailsMap[item.id]
                ? basicDetailsMap[item.id].purchaseUnitName
                : ''
            }
            let params = {
              rfxId: this.modalData.rfxId,
              itemCodeList: itemCodeList
            }
            this.$API.masterData.checkItemCode(params).then((res) => {
              if (res?.data?.length) {
                let rfxStr = []
                let itemCodeStr = []
                for (let item of res.data) {
                  rfxStr.push(item.rfxCode)
                  itemCodeStr.push(item.itemCode)
                }
                this.$dialog({
                  data: {
                    title: this.$t('提示'),
                    message: this.$t(`进行中的单据：{0}，已存在物料编码：{1},是否继续添加?`, {
                      0: rfxStr.join(','),
                      1: itemCodeStr.join(',')
                    })
                  },
                  success: () => {
                    this.$emit('confirm-function', _records)
                  }
                })
              } else {
                this.$emit('confirm-function', _records)
              }
            })
          }
        })
      } else {
        if (this.modalData.sourcingType == 'new_products' && this.modalData.checkSelectItem) {
          if (this.modalData?.priceClassification == 'predict_price') {
            if (_records[0].predictPrice == 1 || _records[0].executePrice == 1) {
              this.$toast({
                content: this.$t('请选择没有执行价和暂估价记录的物料'),
                type: 'warning'
              })
              return
            }
          } else if (this.modalData?.priceClassification == 'srm_price') {
            if (_records[0].srmPrice == 1) {
              this.$toast({
                content: this.$t('请选择没有srm价格记录的物料'),
                type: 'warning'
              })
              return
            }
          } else if (this.modalData?.priceClassification == 'execute_price') {
            if (_records[0].executePrice == 1) {
              this.$toast({
                content: this.$t('请选择没有执行价记录的物料'),
                type: 'warning'
              })
              return
            }
          } else if (this.modalData?.priceClassification == 'basic_price') {
            if (_records[0].basicPrice == 1) {
              this.$toast({
                content: this.$t('请选择没有基价记录的物料'),
                type: 'warning'
              })
              return
            }
          } else {
            if (_records[0].isPriceRecord == 1) {
              this.$toast({
                content: this.$t('请选择没有价格记录的物料'),
                type: 'warning'
              })
              return
            }
          }
        } else if (
          (this.modalData.sourcingType == 'second_inquiry' ||
            this.modalData.sourcingType == 'exist') &&
          this.modalData.checkSelectItem
        ) {
          if (_records[0].isPriceRecord == 0) {
            this.$toast({
              content: this.$t('请选择有价格记录的物料'),
              type: 'warning'
            })
            return
          }
        }

        let params = {
          rfxId: this.modalData.rfxId,
          itemCodeList: [_records[0].itemCode]
        }

        if (this.hasItemCode(_records[0].itemCode)) {
          return
        }

        if (!this.modalData.checkSelectItem) {
          this.confirmEvent(_records)
          return
        }
        this.$API.masterData.checkItemCode(params).then((res) => {
          if (res?.data?.length) {
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: this.$t(`进行中的单据：{0}，已存在物料编码：{1},是否继续添加?`, {
                  0: res.data[0].rfxCode,
                  1: res.data[0].itemCode
                })
              },
              success: () => {
                this.confirmEvent(_records)
              }
            })
          } else {
            this.confirmEvent(_records)
          }
        })
      }
    },
    confirmEvent(_records) {
      let itemGroupId = []
      _records.forEach((e) => {
        itemGroupId = e.id
      })
      let param = {
        id: itemGroupId
      }
      this.$API.quotaConfig.basicDetail(param).then((res) => {
        if (res.data) {
          const fieldMap = {
            purUnitName: 'purchaseUnitName'
          }
          Object.entries(fieldMap).map(([field, key]) => {
            this.$bus.$emit(`${field}Change`, res.data[key])
          })
        }
      })

      this.$emit('confirm-function', _records)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
