import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    cssClass: 'click',
    searchOptions: { maxQueryValueLength: 10000 }
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'itemGroupName',
    headerText: i18n.t('物料组名称'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'itemDescription',
    headerText: i18n.t('规格型号'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'oldItemCode',
    headerText: i18n.t('旧物料编号'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'manufacturerName',
    headerText: i18n.t('制造商'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'isPriceRecord',
    ignore: true,
    allowFiltering: false,
    headerText: i18n.t('是否有价格记录'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  }
]
export const extColumnData = [
  {
    width: '150',
    field: 'basicPrice',
    ignore: true,
    allowFiltering: false,
    headerText: i18n.t('是否有基价'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    width: '150',
    field: 'srmPrice',
    ignore: true,
    allowFiltering: false,
    headerText: i18n.t('是否有SRM价'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    width: '150',
    field: 'predictPrice',
    ignore: true,
    allowFiltering: false,
    headerText: i18n.t('是否有暂估价'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    width: '150',
    field: 'executePrice',
    ignore: true,
    allowFiltering: false,
    headerText: i18n.t('是否有执行价'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  }
]

export const costFactorColumnData = [
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'costFactorSpecification',
    headerText: i18n.t('规格'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'costFactorBrand',
    headerText: i18n.t('品牌'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'costFactorProperty',
    headerText: i18n.t('属性'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'baseMeasureUnitName',
    headerText: i18n.t('单位'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'costFactorGroup',
    headerText: i18n.t('组别'),
    cssClass: 'click'
  }
]
