import { i18n } from '@/main.js'
export const columnDataTwo = [
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    cssClass: 'click',
    searchOptions: { maxQueryValueLength: 10000 }
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'isPriceRecord',
    headerText: i18n.t('是否有价格记录'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    width: '150',
    field: 'basicPrice',
    allowFiltering: false,
    headerText: i18n.t('是否有基价'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    width: '150',
    field: 'srmPrice',
    allowFiltering: false,
    headerText: i18n.t('是否有SRM价'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    width: '150',
    field: 'predictPrice',
    allowFiltering: false,
    headerText: i18n.t('是否有暂估价'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    width: '150',
    field: 'executePrice',
    allowFiltering: false,
    headerText: i18n.t('是否有执行价'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  }
]
