<template>
  <mt-input
    v-bind="$attrs"
    v-model="displayValue"
    @input="input"
    @change="change"
    @keyup.native="keyup"
  />
</template>

<script>
export default {
  props: {
    value: {
      type: [String],
      default: () => null
    }
  },
  data() {
    return {
      displayValue: null
    }
  },
  methods: {
    change() {
      this.$emit('change', this.displayValue)
    },
    input() {
      this.$emit('input', this.displayValue)
    },
    keyup(e) {
      let _value = e.target.value
      if (_value) {
        // 因为模具编码可能会有“-”等字符所有先去掉校验--lbj-modify-2023.04.03
        // _value = _value.replace(/[\W]/g, "");
        if (_value.length && _value.length > 50) {
          _value = _value.substring(0, 50)
        }
        this.displayValue = _value
      } else {
        this.displayValue = null
      }
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(value) {
        this.displayValue = value
      }
    }
  }
}
</script>
