import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '150',
    field: 'attributeCode',
    headerText: i18n.t('属性编号'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'attributeName',
    headerText: i18n.t('属性名称'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'attributeTypeCode',
    headerText: i18n.t('类型'),
    valueConverter: {
      type: 'map',
      map: { 0: '实际定额', 1: '条件基价' }
    },
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'parentAttributeName',
    headerText: i18n.t('父类'),
    cssClass: 'click'
  }
]
