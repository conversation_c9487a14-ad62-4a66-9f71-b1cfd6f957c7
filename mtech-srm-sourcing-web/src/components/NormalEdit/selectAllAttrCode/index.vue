<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <mt-input
        :id="fieldName"
        v-model="data[fieldName]"
        disabled
        :width="130"
        :placeholder="headerTxt"
      ></mt-input>
      <mt-icon
        style="width: 20px"
        name="icon_list_refuse"
        @click.native="handleClear"
        v-if="allowEditing"
      ></mt-icon>
      <mt-icon
        v-if="allowEditing"
        style="width: 20px"
        name="icon_input_search"
        @click.native="showDialog"
      ></mt-icon>
    </div>
  </div>
</template>

<script>
import { getValueByPath } from '@/utils/obj'

export default {
  data() {
    return {
      data: {},
      fieldName: '',
      headerTxt: '',
      allowEditing: true
    }
  },
  mounted() {
    this.fieldName = this.data.column.field
    this.allowEditing = this.data.column.allowEditing === false ? false : true
    if (!this.allowEditing) return
  },
  methods: {
    handleClear() {
      this.$set(this.data, this.fieldName, null)
      const fieldMap = {
        itemId: 'id',
        propertyCode: 'attributeCode',
        propertyName: 'attributeName',
        parentCode: 'parentAttributeCode',
        parentName: 'parentAttributeName'
      }
      Object.entries(fieldMap).map(([field]) => {
        this.$bus.$emit(`${field}Change`, null)
      })
    },
    showDialog() {
      this.$dialog({
        modal: () => import('./components/selectGrid'),
        data: {
          title: this.$t('选择物料')
          // organizationId: sessionStorage.getItem("organizationId"),
        },
        success: (data) => {
          let fieldMap
          if (this.$route.name == 'purchase-quotaConfig-detail') {
            this.$set(this.data, this.fieldName, data[0]['attributeCode'])
            fieldMap = {
              itemId: 'id',
              propertyCode: 'attributeCode',
              propertyName: 'attributeName',
              quotaName: 'attributeName',
              parentCode: 'parentAttributeCode',
              parentName: 'parentAttributeName',
              quotaType: 'attributeTypeCode'
            }
          } else {
            this.$set(this.data, this.fieldName, data[0]['attributeCode'])
            fieldMap = {
              itemId: 'id',
              propertyCode: 'attributeCode',
              propertyName: 'attributeName',
              quotaName: 'attributeName',
              parentCode: 'parentAttributeCode',
              parentName: 'parentAttributeName'
            }
          }
          Object.entries(fieldMap).map(([field, key]) => {
            this.$bus.$emit(`${field}Change`, getValueByPath(data[0], key))
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
</style>
