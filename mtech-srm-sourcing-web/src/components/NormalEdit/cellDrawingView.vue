<template>
  <div class="cell-upload" :id="'cell-upload-' + data.index">
    <mt-input id="drawingUrl" style="display: none" :value="data.itemCode"></mt-input>
    <div
      @click.self="showDrawing"
      :class="['cell-operable-title', { active: isAvtive(data.drawingUrl) }]"
    >
      {{ data.drawingUrl | listNumFormat }}
    </div>
  </div>
</template>

<script>
import { i18n } from '@/main.js'
export default {
  data() {
    return {
      data: {
        // drawing: {},
      },
      fieldName: ''
    }
  },
  filters: {
    listNumFormat(value) {
      if (value && value.length > 0) {
        return i18n.t('图纸查看')
      } else {
        return i18n.t('图纸查看')
      }
    }
  },
  computed: {
    isAvtive() {
      return (value) => {
        return value && value.length > 0
      }
    }
  },
  mounted() {
    this.fieldName = this.data.column.field
    this.$bus.$on('drawingUrlChange', (txt) => {
      console.log('drawingUrlChange--', txt)
      if (txt) {
        this.data.itemCode = txt
      }
    })
  },
  methods: {
    showDrawing() {
      if (!this.data.itemCode) {
        this.$toast({
          content: this.$t('请先选择物料'),
          type: 'warning'
        })
        return
      }
      this.getUrl(this.data.itemCode)
    },
    getUrl(txt) {
      let _queryParam = { itemCode: txt }
      this.$API.rfxRequireDetail.getDrawingUrl(_queryParam).then((res) => {
        if (res.code == 200) {
          window.open(res.data)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.cell-operable-title {
  display: inline-block;
  padding: 10px;

  color: #00469c;
  font-size: 14px;
  cursor: pointer;
}
</style>
