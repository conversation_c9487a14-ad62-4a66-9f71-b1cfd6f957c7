<template>
  <mt-input v-bind="$attrs" v-model="displayValue" @input="input" />
</template>

<script>
export default {
  props: {
    value: {
      type: [String],
      default: () => null
    },
    field: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      displayValue: null
    }
  },
  methods: {
    input() {
      this.$emit('input', this.displayValue)
    }
  },

  watch: {
    value: {
      immediate: true,
      handler(value) {
        this.displayValue = value
      }
    }
  }
}
</script>
