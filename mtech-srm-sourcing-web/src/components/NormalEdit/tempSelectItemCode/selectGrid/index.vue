<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-template-page ref="templateRef" :template-config="pageConfig" />
    </div>
  </mt-dialog>
</template>

<script>
import { columnData } from './config'
export default {
  data() {
    return {
      data: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: [],
          gridId:
            this.$permission.gridId['purchase'][this.modalData.source]['detail']['tabs']['pricing'][
              'dialog_tempItem'
            ],
          grid: {
            allowFiltering: true,
            ignoreFields: ['isPriceRecord'],
            columnData: columnData,
            allowSorting: false,
            allowSelection: true,
            selectionSettings: {
              type: 'Multiple',
              mode: 'Row'
            },
            rowDataBound: (args) => {
              if (
                this.modalData.sourcingType == '' ||
                this.modalData.sourcingType == null ||
                this.modalData.sourcingType == undefined
              ) {
                return
              } else if (this.modalData.sourcingType == 'new_products') {
                if (args.data.isPriceRecord == 1) {
                  args.row.classList.add('backgroundRed')
                }
              } else if (
                this.modalData.sourcingType == 'second_inquiry' ||
                this.modalData.sourcingType == 'exist'
              ) {
                if (args.data.isPriceRecord == 0) {
                  args.row.classList.add('backgroundRed')
                }
              }
            },
            recordDoubleClick: this.recordDoubleClick,
            asyncConfig: {
              // url: this.$API.masterData.getItemListUrlPage,
              url: this.$API.comparativePrice.queryItems,
              defaultRules: [
                {
                  label: '品类编码',
                  field: 'categoryResponse.categoryCode',
                  type: 'string',
                  operator: 'equal',
                  value: this.modalData.categoryCode
                }
              ],
              params: {
                organizationId: this.modalData.siteParam.organizationId,
                siteCode: this.modalData.siteParam.siteCode,
                rfxId: this.modalData.rfxId
              }
            }
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    recordDoubleClick(e) {
      if (this.modalData.sourcingType == 'new_products') {
        if (e.rowData.isPriceRecord == 1) {
          this.$toast({
            content: this.$t('请选择没有价格记录的物料'),
            type: 'warning'
          })
          return
        }
      } else if (
        this.modalData.sourcingType == 'second_inquiry' ||
        this.modalData.sourcingType == 'exist'
      ) {
        if (e.rowData.isPriceRecord == 0) {
          this.$toast({
            content: this.$t('请选择有价格记录的物料'),
            type: 'warning'
          })
          return
        }
      }
      this.confirmEvent([e.rowData])
    },
    confirm() {
      let _records = this.$refs.templateRef.getCurrentTabRef().grid.getSelectedRecords()
      if (_records.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      } else {
        if (this.modalData.sourcingType == 'new_products') {
          if (_records[0].isPriceRecord == 1) {
            this.$toast({
              content: this.$t('请选择没有价格记录的物料'),
              type: 'warning'
            })
            return
          }
        } else if (
          this.modalData.sourcingType == 'second_inquiry' ||
          this.modalData.sourcingType == 'exist'
        ) {
          if (_records[0].isPriceRecord == 0) {
            this.$toast({
              content: this.$t('请选择有价格记录的物料'),
              type: 'warning'
            })
            return
          }
        }
        this.confirmEvent(_records)
      }
    },
    confirmEvent(_records) {
      let itemGroupId = []
      _records.forEach((e) => {
        itemGroupId = e.id
      })
      let param = {
        id: itemGroupId
      }
      this.$API.quotaConfig.basicDetail(param).then((res) => {
        if (res.data) {
          const fieldMap = {
            purUnitName: 'purchaseUnitName'
          }
          Object.entries(fieldMap).map(([field, key]) => {
            this.$bus.$emit(`${field}Change`, res.data[key])
          })
        }
      })
      this.$emit('confirm-function', _records)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
