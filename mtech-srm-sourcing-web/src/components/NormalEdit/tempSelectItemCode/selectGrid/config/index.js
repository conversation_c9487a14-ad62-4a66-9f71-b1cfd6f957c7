import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'categoryResponse.categoryCode',
    headerText: i18n.t('品类编码'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'categoryResponse.categoryName',
    headerText: i18n.t('品类名称'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'itemDescription',
    headerText: i18n.t('规格型号'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'oldItemCode',
    headerText: i18n.t('旧物料编号'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'manufacturerName',
    headerText: i18n.t('制造商'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'isPriceRecord',
    ignore: true,
    allowFiltering: false,
    headerText: i18n.t('是否有价格记录'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  }
]
