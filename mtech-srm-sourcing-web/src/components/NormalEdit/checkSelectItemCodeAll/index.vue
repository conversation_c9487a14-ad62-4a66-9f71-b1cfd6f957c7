<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <mt-input :id="fieldName" :value="value" disabled :placeholder="headerTxt"></mt-input>
      <mt-icon
        style="width: 20px"
        name="icon_list_refuse"
        @click.native="handleClear"
        v-if="allowEditing"
      ></mt-icon>
      <mt-icon
        v-if="allowEditing"
        style="width: 20px"
        name="icon_input_search"
        @click.native="showDialog"
      ></mt-icon>
    </div>
  </div>
</template>

<script>
import { getValueByPath } from '@/utils/obj'

export default {
  props: [
    // eslint-disable-next-line vue/require-prop-types
    'field',
    // eslint-disable-next-line vue/require-prop-types
    'value',
    // eslint-disable-next-line vue/require-prop-types
    'rfxId',
    // eslint-disable-next-line vue/require-prop-types
    'submitTableData',
    // eslint-disable-next-line vue/require-prop-types
    'sourcingType',
    // eslint-disable-next-line vue/require-prop-types
    'source',
    // eslint-disable-next-line vue/require-prop-types
    'sourcingObjType'
  ],
  data() {
    return {
      // data: {},
      fieldName: '',
      headerTxt: '',
      allowEditing: true,
      title: this.$t('选择物料')
    }
  },
  mounted() {
    if (this.sourcingObjType === 'cost_factor') {
      this.title = this.$t('选择成本因子')
    } else if (
      this.sourcingObjType === 'quota_no' ||
      this.sourcingObjType === 'conditions_base_price' ||
      this.sourcingObjType === 'item_quota_no' ||
      this.sourcingObjType === 'cost_factor_quota_no'
    ) {
      this.title = this.$t('选择属性大类')
    } else {
      this.title = this.$t('选择物料')
    }
    this.fieldName = this.field
    if (!this.allowEditing) return
  },
  methods: {
    handleClear() {
      this.$emit('input', null)
      const fieldMap = {
        itemId: 'id',
        itemCode: 'itemCode',
        itemName: 'itemName',
        spec: 'itemDescription', // 规格描述
        material: 'materialTypeName', // 材质
        unitName: 'baseMeasureUnitName', // 单位
        categoryId: 'categoryResponse.id', // 品类ID
        categoryCode: 'categoryResponse.categoryCode', // 品类编码
        categoryName: 'categoryResponse.categoryName' //品类名称
      }
      this.$bus.$emit(`categoryResponseChange`, null)

      Object.entries(fieldMap).map(([field]) => {
        // this.$bus.$emit(`${field}Change`, null);
        if (
          field === 'itemName' ||
          field === 'itemCode' ||
          field === 'spec' ||
          field === 'material' ||
          field === 'unitName' ||
          field === 'categoryCode' ||
          field === 'categoryName'
        ) {
          this.$bus.$emit(`${field}Change`, ' ')
        }
      })
    },
    showDialog() {
      this.$dialog({
        modal: () => import('./components/selectSKUOrCategory/index.vue'),
        data: {
          title: this.title,
          sourcingObjType: this.sourcingObjType
        },
        success: (data) => {
          let fieldMap
          if (
            this.sourcingObjType == 'quota_no' ||
            this.sourcingObjType == 'item_quota_no' ||
            this.sourcingObjType == 'cost_factor_quota_no'
          ) {
            this.$emit('input', data[0]['attributeCode'])
            fieldMap = {
              itemCode: 'attributeCode',
              itemName: 'attributeName'
            }
          } else if (this.sourcingObjType == 'conditions_base_price') {
            this.$emit('input', data[0]['parentAttributeCode'])
            fieldMap = {
              itemCode: 'parentAttributeCode',
              itemName: 'parentAttributeName'
            }
          } else {
            this.$emit('input', data[0]['itemCode'])
            fieldMap = {
              itemId: 'id',
              itemCode: 'itemCode',
              itemName: 'itemName',
              spec: 'itemDescription', // 规格描述
              material: 'materialTypeName', // 材质
              unitName: 'baseMeasureUnitName', // 单位
              categoryId: 'categoryResponse.id', //品类名称
              categoryCode: 'categoryResponse.categoryCode', //品类名称
              categoryName: 'categoryResponse.categoryName', //品类名称
              priceUnitName: 'purchaseUnitName'
            }
            data?.[0]?.categoryResponse &&
              this.$bus.$emit(`categoryResponseChange`, data[0].categoryResponse)
          }

          Object.entries(fieldMap).map(([field, key]) => {
            this.$bus.$emit(`${field}Change`, getValueByPath(data[0], key))
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    // justify-content: space-between;

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
    }
    .mt-input {
      width: 100%;
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
</style>
