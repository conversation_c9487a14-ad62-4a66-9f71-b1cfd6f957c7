<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :style="{ width: '940px' }"
    :header="header"
    @beforeClose="cancel"
  >
    <div id="forecast-manage-table-container">
      <mt-template-page ref="table" :template-config="pageConfig" />
    </div>
  </mt-dialog>
</template>

<script>
import { costFactor, categoryTemplateConfig, attriList, parentAttriList } from './config'
export default {
  name: 'SelectSKUOrCategory',
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  computed: {
    pageConfig() {
      let _tempConfig
      if (this.modalData.sourcingObjType === 'cost_factor') {
        _tempConfig = costFactor
      } else if (
        this.modalData.sourcingObjType === 'quota_no' ||
        this.modalData.sourcingObjType === 'item_quota_no' ||
        this.modalData.sourcingObjType === 'cost_factor_quota_no'
      ) {
        _tempConfig = attriList
      } else if (this.modalData.sourcingObjType === 'base_price') {
        _tempConfig = categoryTemplateConfig
      } else if (this.modalData.sourcingObjType === 'conditions_base_price') {
        _tempConfig = parentAttriList
      } else {
        _tempConfig = categoryTemplateConfig
      }
      return _tempConfig
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    confirm() {
      this.$emit(
        'confirm-function',
        this.$refs.table.getCurrentUsefulRef().ejsRef.getSelectedRecords()
      )
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped></style>
