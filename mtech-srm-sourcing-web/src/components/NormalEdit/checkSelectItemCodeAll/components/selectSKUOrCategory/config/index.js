import { i18n } from '@/main.js'
//按照数量设置阶梯
export const quantityStageConfig = (cellSaveEvent) => [
  {
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [['Add']]
    },
    grid: {
      allowFiltering: true,
      allowPaging: false,
      allowEditting: true,
      editSettings: {
        allowEditing: true,
        mode: 'Batch'
      },
      cellSave: cellSaveEvent,
      columnData: [
        // {
        //   width: "50",
        //   type: "checkbox",
        // },
        {
          field: 'stageRange',
          headerText: i18n.t('数量'),
          cssClass: '',
          allowEditing: false,
          cellTools: [
            { id: 'Edit', icon: 'icon_Editor', title: i18n.t('编辑') },
            { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') }
          ]
        },
        {
          field: 'taxedUnitPrice',
          headerText: i18n.t('含税价'),
          allowEditing: true
        },
        {
          field: 'untaxedUnitPrice',
          headerText: i18n.t('未含税'),
          allowEditing: false
        },
        {
          field: 'discountRate',
          headerText: i18n.t('折扣'),
          allowEditing: true
        }
      ],
      dataSource: []
    }
  }
]
//按照时间设置阶梯
export const timeStageConfig = (cellSaveEvent) => [
  {
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [['Add']]
    },
    grid: {
      allowFiltering: true,
      allowPaging: false,
      allowEditting: true,
      editSettings: {
        allowEditing: true,
        mode: 'Batch'
      },
      cellSave: cellSaveEvent,
      columnData: [
        // {
        //   width: "50",
        //   type: "checkbox",
        // },
        {
          field: 'stageRange',
          headerText: i18n.t('时间'),
          cssClass: '',
          allowEditing: false,
          cellTools: [
            { id: 'Edit', icon: 'icon_Editor', title: i18n.t('编辑') },
            { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') }
          ]
        },
        {
          field: 'taxedUnitPrice',
          headerText: i18n.t('含税价'),
          allowEditing: true
        },
        {
          field: 'untaxedUnitPrice',
          headerText: i18n.t('未含税'),
          allowEditing: false
        },
        {
          field: 'discountRate',
          headerText: i18n.t('折扣'),
          allowEditing: true
        }
      ],
      dataSource: []
    }
  }
]
//按照金额设置阶梯
export const amountStageConfig = (cellSaveEvent) => [
  {
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [['Add']]
    },
    grid: {
      allowFiltering: true,
      allowPaging: false,
      allowEditting: true,
      editSettings: {
        allowEditing: true,
        mode: 'Batch'
      },
      cellSave: cellSaveEvent,
      columnData: [
        // {
        //   width: "50",
        //   type: "checkbox",
        // },
        {
          field: 'stageRange',
          headerText: i18n.t('金额'),
          cssClass: '',
          allowEditing: false,
          cellTools: [
            { id: 'Edit', icon: 'icon_Editor', title: i18n.t('编辑') },
            { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') }
          ]
        },
        {
          field: 'taxedUnitPrice',
          headerText: i18n.t('含税价'),
          allowEditing: true
        },
        {
          field: 'untaxedUnitPrice',
          headerText: i18n.t('未含税'),
          allowEditing: false
        },
        {
          field: 'discountRate',
          headerText: i18n.t('折扣'),
          allowEditing: true
        }
      ],
      dataSource: []
    }
  }
]

import masterData from '@/apis/modules/service/masterData'

export const SKUTemplateConfig = [
  {
    useToolTemplate: false,
    toolbar: [],
    grid: {
      allowFiltering: true,
      height: 260,
      allowSelection: true,
      selectionSettings: {
        checkboxOnly: false
      },
      columnData: [
        {
          width: '150',
          field: 'itemId',
          headerText: i18n.t('SKU编号')
        },
        { width: '150', field: 'name', headerText: i18n.t('SKU名称') },
        {
          width: '150',
          field: 'itemCode',
          headerText: i18n.t('商品SPU/物料编号')
        },
        {
          width: '150',
          field: 'itemName',
          headerText: i18n.t('商品SPU/物料名称')
        },
        {
          width: '150',
          field: 'headImgId',
          headerText: i18n.t('SKU图片')
        },
        {
          width: '150',
          field: 'specificationModel',
          headerText: i18n.t('规格型号')
        },
        {
          width: '150',
          field: 'manufacturerName',
          headerText: i18n.t('制造商')
        },
        {
          width: '150',
          field: 'statusDescription',
          headerText: i18n.t('状态')
        }
      ],
      // dataSource: [],
      asyncConfig: {
        url: masterData.APIS.getSKUListUrl
      }
    }
  }
]
export const categoryTemplateConfig = [
  {
    useToolTemplate: false,
    toolbar: [],
    grid: {
      allowFiltering: true,
      height: 260,
      allowSelection: true,
      selectionSettings: {
        checkboxOnly: false
      },
      columnData: [
        {
          width: '150',
          field: 'itemCode',
          headerText: i18n.t('物料编号')
        },
        { width: '150', field: 'itemName', headerText: i18n.t('物料名称') },
        {
          width: '150',
          field: 'categoryResponse.categoryCode',
          headerText: i18n.t('品类')
        },
        {
          width: '150',
          field: 'itemDescription',
          headerText: i18n.t('规格型号')
        },
        {
          width: '150',
          field: 'oldItemCode',
          headerText: i18n.t('旧物料编号')
        },
        {
          width: '150',
          field: 'manufacturerName',
          headerText: i18n.t('制造商')
        }
      ],
      // dataSource: [],
      asyncConfig: {
        url: masterData.APIS.getItemListUrlPage
        // recordsPosition: 'data'
      }
    }
  }
]

import comparativePrice from '@/apis/modules/purchase/comparativePrice'
export const costFactor = [
  {
    useToolTemplate: false,
    toolbar: [],
    grid: {
      allowFiltering: true,
      height: 260,
      allowSelection: true,
      selectionSettings: {
        checkboxOnly: false
      },
      columnData: [
        {
          width: '150',
          field: 'itemCode',
          headerText: i18n.t('成本因子编码'),
          cssClass: 'click'
        },
        {
          width: '150',
          field: 'itemName',
          headerText: i18n.t('成本因子名称'),
          cssClass: 'click'
        },
        {
          width: '150',
          field: 'costFactorSpecification',
          headerText: i18n.t('规格'),
          cssClass: 'click'
        },
        {
          width: '150',
          field: 'costFactorBrand',
          headerText: i18n.t('品牌'),
          cssClass: 'click'
        },
        {
          width: '150',
          field: 'costFactorProperty',
          headerText: i18n.t('属性'),
          cssClass: 'click'
        },
        {
          width: '150',
          field: 'companyName',
          headerText: i18n.t('单位'),
          cssClass: 'click'
        },
        {
          width: '150',
          field: 'costFactorGroup',
          headerText: i18n.t('组别'),
          cssClass: 'click'
        }
      ],
      // dataSource: [],
      asyncConfig: {
        url: comparativePrice.APIS.costFactor,
        defaultRules: [
          {
            label: '是否生效',
            field: 'statusId',
            type: 'string',
            operator: 'equal',
            value: '1'
          }
        ],
        params: {
          onlyCurrentLevel: 0
        }
      }
    }
  }
]

export const attriList = [
  {
    useToolTemplate: false,
    toolbar: [],
    grid: {
      allowFiltering: true,
      height: 260,
      allowSelection: true,
      selectionSettings: {
        checkboxOnly: false
      },
      columnData: [
        {
          width: '150',
          field: 'attributeCode',
          headerText: i18n.t('属性编号'),
          cssClass: 'click'
        },
        {
          width: '150',
          field: 'attributeName',
          headerText: i18n.t('属性名称'),
          cssClass: 'click'
        },
        {
          width: '150',
          field: 'attributeTypeCode',
          headerText: i18n.t('类型'),
          valueConverter: {
            type: 'map',
            map: { 0: '实际定额', 1: '条件基价' }
          },
          cssClass: 'click'
        },
        {
          width: '150',
          field: 'parentAttributeName',
          headerText: i18n.t('父类'),
          cssClass: 'click'
        }
      ],
      asyncConfig: {
        url: comparativePrice.APIS.costFactor,
        defaultRules: [
          {
            label: '是否生效',
            field: 'statusId',
            type: 'string',
            operator: 'equal',
            value: '1'
          }
        ],
        params: {
          onlyCurrentLevel: 1
        }
      }
    }
  }
]

export const parentAttriList = [
  {
    useToolTemplate: false,
    toolbar: [],
    grid: {
      allowFiltering: true,
      height: 260,
      allowSelection: true,
      selectionSettings: {
        checkboxOnly: false
      },
      columnData: [
        {
          width: '150',
          field: 'attributeCode',
          headerText: i18n.t('属性编号'),
          cssClass: 'click'
        },
        {
          width: '150',
          field: 'attributeName',
          headerText: i18n.t('属性名称'),
          cssClass: 'click'
        },
        {
          width: '150',
          field: 'parentAttributeCode',
          headerText: i18n.t('父类编码'),
          cssClass: 'click'
        },
        {
          width: '150',
          field: 'parentAttributeName',
          headerText: i18n.t('父类'),
          cssClass: 'click'
        }
      ],
      asyncConfig: {
        url: comparativePrice.APIS.costFactor,
        params: {
          onlyCurrentLevel: 1
        },
        defaultRules: [
          {
            field: 'attributeTypeCode',
            type: 'number',
            operator: 'equal',
            value: '1'
          },
          {
            label: '是否生效',
            field: 'statusId',
            type: 'string',
            operator: 'equal',
            value: '1'
          }
        ]
      }
    }
  }
]
