<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-template-page ref="templateRef" :template-config="pageConfig" />
    </div>
  </mt-dialog>
</template>

<script>
import { columnData } from './config'
export default {
  data() {
    return {
      data: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: [],
          gridId: '848b9627-23ea-45c4-beb9-0abfaa3adcde',
          grid: {
            allowFiltering: true,
            columnData,
            allowSorting: false,
            allowSelection: true,
            selectionSettings: {
              type: 'Multiple',
              mode: 'Row'
            },
            recordDoubleClick: this.recordDoubleClick,
            asyncConfig: {
              url: `/masterDataManagement/tenant/unit/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`
            }
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    recordDoubleClick(e) {
      console.log('double', e)
      this.confirmEvent([e.rowData])
    },
    confirm() {
      let _records = this.$refs.templateRef.getCurrentTabRef().gridRef.getMtechGridRecords()
      if (_records.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      } else {
        // if()
        console.log('router', this.$router)
        this.confirmEvent(_records)
      }
    },
    confirmEvent(_records) {
      let itemGroupId = []
      _records.forEach((e) => {
        itemGroupId = e.id
      })
      let param = {
        id: itemGroupId
      }
      this.$API.quotaConfig.basicDetail(param).then((res) => {
        if (res.data) {
          const fieldMap = {
            purUnitName: 'purchaseUnitName'
          }
          Object.entries(fieldMap).map(([field, key]) => {
            this.$bus.$emit(`${field}Change`, res.data[key])
          })
        }
      })
      this.$emit('confirm-function', _records)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
