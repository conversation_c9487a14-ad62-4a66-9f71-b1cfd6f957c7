<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <mt-select
        v-if="showSelect"
        :id="fieldName"
        :allow-filtering="true"
        :data-source="itemsDataSources"
        :filtering="onFiltering"
        :fields="{ text: 'itemCode', value: 'itemCode' }"
        :value="value"
        :width="130"
        :disabled="!allowEditing"
        @select="handleSelectChange"
        :placeholder="headerTxt"
        popup-width="180px"
      ></mt-select>
      <mt-input
        :id="fieldName"
        v-model="data[fieldName]"
        disabled
        :width="130"
        :placeholder="headerTxt"
        v-show="!showSelect"
      ></mt-input>
      <mt-icon
        style="width: 20px"
        name="icon_list_refuse"
        @click.native="handleClear"
        v-if="allowEditing"
      ></mt-icon>
      <mt-icon
        v-if="allowEditing"
        style="width: 20px"
        name="icon_input_search"
        @click.native="showDialog"
      ></mt-icon>
    </div>
  </div>
</template>

<script>
import { getValueByPath } from '@/utils/obj'
import debounce from 'lodash.debounce'
export default {
  props: {
    field: {
      type: String,
      default: ''
    },
    dataInfo: {
      type: Object,
      default: () => null
    },
    moduleType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      data: {},
      fieldName: '',
      headerTxt: '',
      allowEditing: true,
      itemsDataSources: [],
      showSelect: true,
      value: ''
    }
  },
  async mounted() {
    this.showSelect = false
    this.fieldName = this.data.column?.field || this.field
    if (this.dataInfo) {
      this.data = { ...this.dataInfo }
    }
    this.value = this.data.itemCode

    await this.getDataSource()
    this.allowEditing = this.data.column?.allowEditing === false ? false : true
    if (!this.allowEditing) return
    this.$bus.$on(`${this.fieldName}Clear`, () => {
      this.handleClear()
    })
  },
  methods: {
    async getDataSource() {
      let _organizationId = sessionStorage.getItem('organizationId')
      if (!_organizationId) {
        console.error('在请求物料数据时没有获得 organizationId')
        return
      }
      let _siteCode = sessionStorage.getItem('pricingSiteCode')
      await this.$API.comparativePrice
        .queryItemsData({
          page: { current: 1, size: 20 },
          organizationId: _organizationId,
          siteCode: _siteCode,
          rfxId: '0',
          rules: [
            {
              label: '品类编码',
              field: 'itemCode',
              type: 'string',
              operator: 'contains',
              value: this.value
            }
          ]
        })
        .then((res) => {
          this.itemsDataSources.length = 0
          res.data.records.forEach((item) => {
            item.text = `(${item.itemCode})${item.itemName}`
            this.itemsDataSources.push(item)
          })
          this.showSelect = true
        })
        .catch(() => {
          this.showSelect = true
        })
    },
    onFiltering: debounce(function (e) {
      let { text } = e
      let _organizationId = sessionStorage.getItem('organizationId')
      let _siteCode = sessionStorage.getItem('pricingSiteCode')
      if (!!_organizationId && !!_siteCode && !!text) {
        this.$API.comparativePrice
          .queryItemsData({
            page: { current: 1, size: 20 },
            organizationId: _organizationId,
            siteCode: _siteCode,
            rfxId: '0',
            rules: [
              {
                label: '品类编码',
                field: 'itemCode',
                type: 'string',
                operator: 'contains',
                value: text
              }
            ]
          })
          .then((res) => {
            let _records = []
            res.data.records.forEach((item) => {
              item.text = `(${item.itemCode})${item.itemName}`
              _records.push(item)
            })
            e.updateData(_records)
          })
      } else {
        this.$toast({
          content: this.$t('当前未选择工厂，不可以选择物料'),
          type: 'warning'
        })
      }
    }, 1000),
    handleSelectChange(e) {
      if (e.e !== null) {
        if (this.moduleType === 'zjdj') {
          this.$bus.$emit('itemCodeChange', e.itemData)
        } else {
          let _records = [e.itemData]
          this.confirmEvent(_records)
        }
      }
    },
    confirmEvent(_records) {
      let itemGroupId = []
      _records.forEach((e) => {
        itemGroupId = e.id
      })
      let param = {
        id: itemGroupId
      }
      this.$API.quotaConfig.basicDetail(param).then((res) => {
        if (res.data) {
          const fieldMap = {
            purUnitName: 'purchaseUnitName'
          }
          Object.entries(fieldMap).map(([field, key]) => {
            this.$bus.$emit(`${field}Change`, res.data[key])
          })
        }
      })

      this.confirm123(_records)
    },
    confirm123(data) {
      this.$set(this.data, 'itemCode', data[0]['itemCode'])
      const fieldMap = {
        itemId: 'id',
        itemCode: 'itemCode',
        itemName: 'itemName',
        spec: 'itemDescription', // 规格描述
        material: 'materialTypeName', // 材质
        unitCode: 'baseMeasureUnitCode',
        unitName: 'baseMeasureUnitName', // 单位
        categoryId: 'categoryResponse.id', //品类名称
        categoryCode: 'categoryResponse.categoryCode', //品类名称
        categoryName: 'categoryResponse.categoryName', //品类名称
        // purUnitName: "purchaseUnitName", //采购单位
        priceUnitName: 'purchaseUnitName'
      }
      Object.entries(fieldMap).map(([field, key]) => {
        if (
          this.$route.name === 'contract-price-detail' &&
          key === 'itemName' &&
          getValueByPath(data[0], 'itemText')
        ) {
          this.$bus.$emit(`${field}Change`, getValueByPath(data[0], 'itemText'))
        } else {
          if (this.$route.name === 'direct-pricing-detail-tv' && key === 'itemCode') {
            let _supplierCode = sessionStorage.getItem('pricingSupplierCode')
            if (_supplierCode) {
              this.$bus.$emit(`purUnitNameChange`, data[0])
            }
          }
          this.$bus.$emit(`${field}Change`, getValueByPath(data[0], key))
        }
      })
      this.value = data[0]['itemCode']
    },
    handleClear() {
      this.$set(this.data, 'itemCode', null)
      this.value = null
      const fieldMap = {
        itemId: 'id',
        itemCode: 'itemCode',
        itemName: 'itemName',
        spec: 'itemDescription', // 规格描述
        material: 'materialTypeName', // 材质
        unitCode: 'baseMeasureUnitCode',
        unitName: 'baseMeasureUnitName', // 单位
        categoryId: 'categoryResponse.id', // 品类ID
        categoryCode: 'categoryResponse.categoryCode', // 品类编码
        categoryName: 'categoryResponse.categoryName' //品类名称
      }
      Object.entries(fieldMap).map(([field]) => {
        this.$bus.$emit(`${field}Change`, null)
      })
    },
    showDialog() {
      if (
        sessionStorage.getItem('organizationId') == '' ||
        sessionStorage.getItem('organizationId') == 'undefined' ||
        !sessionStorage.getItem('organizationId')
      ) {
        this.$toast({
          content: this.$t('当前未选择工厂，不可以选择物料'),
          type: 'warning'
        })
        return
      }
      this.showSelect = false
      this.$dialog({
        modal: () => import('./components/selectGrid'),
        data: {
          title: this.$t('选择物料'),
          organizationId: sessionStorage.getItem('organizationId'),
          siteCode: sessionStorage.getItem('pricingSiteCode')
        },
        success: (data) => {
          this.$set(this.data, 'itemCode', data[0]['itemCode'])
          const fieldMap = {
            itemId: 'id',
            itemCode: 'itemCode',
            itemName: 'itemName',
            spec: 'itemDescription', // 规格描述
            material: 'materialTypeName', // 材质
            unitCode: 'baseMeasureUnitCode',
            unitName: 'baseMeasureUnitName', // 单位
            categoryId: 'categoryResponse.id', //品类名称
            categoryCode: 'categoryResponse.categoryCode', //品类名称
            categoryName: 'categoryResponse.categoryName' //品类名称
            // purUnitName: "purchaseUnitName", //采购单位
            // priceUnitName: 'purchaseUnitName'
          }
          Object.entries(fieldMap).map(([field, key]) => {
            if (
              this.$route?.name === 'contract-price-detail' &&
              key === 'itemName' &&
              getValueByPath(data[0], 'itemText')
            ) {
              this.$bus.$emit(`${field}Change`, getValueByPath(data[0], 'itemText'))
            } else {
              if (this.$route?.name === 'direct-pricing-detail-tv' && key === 'itemCode') {
                let _supplierCode = sessionStorage.getItem('pricingSupplierCode')
                if (_supplierCode) {
                  this.$bus.$emit(`purUnitNameChange`, data[0])
                }
              }
              this.$bus.$emit(`${field}Change`, getValueByPath(data[0], key))
            }
          })
          this.value = data[0]['itemCode']
          this.getDataSource()
        },
        close: () => {
          this.showSelect = true
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
</style>
