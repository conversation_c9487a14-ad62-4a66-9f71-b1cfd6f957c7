import { i18n } from '@/main.js'
// 部品编码、部品名称、模具编码、模具类型、规划量、实际分摊量
export const columnData = [
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('部品编码')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('部品名称')
  },
  {
    width: '150',
    field: 'dieCode',
    headerText: i18n.t('模具编码')
  },
  {
    width: '150',
    field: 'dieType',
    headerText: i18n.t('模具类型'),
    ignore: true,
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('基础模具'),
        2: i18n.t('复制模具'),
        3: i18n.t('基础模改模'),
        4: i18n.t('复制模改模')
      }
    }
  },
  {
    width: '150',
    field: 'planQuantity',
    ignore: true,
    headerText: i18n.t('规划量')
  },
  {
    width: '150',
    ignore: true,
    field: 'shareQuantity',
    headerText: i18n.t('实际分摊）')
  }
]
