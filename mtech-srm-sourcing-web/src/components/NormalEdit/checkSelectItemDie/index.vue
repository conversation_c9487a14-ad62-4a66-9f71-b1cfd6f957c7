<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <mt-input
        :id="fieldName"
        :value="displayValue"
        :disabled="isDisabled"
        @input="input"
        @keyup.native="keyup"
        :placeholder="headerTxt"
        class="field-input"
      ></mt-input>
      <mt-icon
        v-if="isDisplay"
        class="field-icon"
        name="icon_list_refuse"
        @click.native="handleClear"
      ></mt-icon>
      <mt-icon
        v-if="isDisplay"
        class="field-icon"
        name="icon_input_search"
        @click.native="showDialog"
      ></mt-icon>
    </div>
  </div>
</template>

<script>
export default {
  // eslint-disable-next-line vue/require-prop-types
  props: ['field', 'value', 'rfxId', 'source', 'editInstance'],
  data() {
    return {
      // data: {},
      inputDisabled: true,
      fieldName: '',
      headerTxt: '',
      allowEditing: true,
      displayValue: null
    }
  },
  computed: {
    isDisabled() {
      return this.inputDisabled
    },
    isDisplay() {
      return this.allowEditing && this.inputDisabled
    }
  },
  mounted() {
    this.fieldName = this.field
    if (!this.allowEditing) return
    this.$bus.$on('itemExtMap.referChannelChange', (e = 0) => {
      this.inputDisabled = e > 0
      // 获取最新一条数据
      if (e > 0) {
        this.getNewItemDie()
      }
    })
    //0手工定价  2价格记录
    if ([2].includes(this?.editInstance?.rowData?.itemExtMap?.referChannel)) {
      this.inputDisabled = true
    } else {
      this.inputDisabled = false
    }
  },
  beforeDestroy() {
    this.$bus.$off('itemExtMap.referChannelChange')
  },
  methods: {
    input() {
      // this.$emit("input", { dieFormalCode: this.displayValue });
    },
    keyup(e) {
      let _value = e.target.value
      if (_value) {
        // 因为模具编码可能会有“-”等字符所有先去掉校验--lbj-modify-2023.04.03
        // _value = _value.replace(/[\W]/g, '')
        if (_value.length && _value.length > 50) {
          _value = _value.substring(0, 50)
        }
        this.displayValue = _value
      } else {
        this.displayValue = ''
      }
      this.$emit('input', this.displayValue)
    },
    handleClear() {
      this.$emit('input', null)
    },
    showDialog() {
      this.$dialog({
        modal: () => import('./components/selectGrid'),
        data: {
          title: this.$t('选择模具'),
          rfxId: this.rfxId,
          itemParam: JSON.parse(sessionStorage.getItem('dieItemParam')),
          source: this.source
        },
        success: (data) => {
          this.$emit('input', data[0])
        }
      })
    },
    // 获取模具编码最新一条记录信息
    getNewItemDie() {
      let params = {
        itemCodes: [this?.editInstance?.rowData?.itemCode],
        dieType: this?.editInstance?.rowData?.itemDieResponse?.dieType,
        page: { current: 1, size: 10 }
      }
      this.$API.rfxExt.queryMoldByItemCode(params).then((res) => {
        if (res.code === 200) {
          const _obj = res.data.find(
            (item) => item.dieType === this?.editInstance?.rowData?.itemDieResponse?.dieType
          )
          if (_obj?.dieCode) {
            _obj.dieFormalCode = _obj.dieCode
          }
          this.$emit('input', { ..._obj })
        }
      })
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(value) {
        this.displayValue = value
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;

  .in-cell {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .field-input {
      flex: 1;
    }

    .field-icon {
      width: 20px;
      flex-shrink: 0;
      margin-left: 10px;
      cursor: pointer;
    }

    > .mt-icons {
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
</style>
