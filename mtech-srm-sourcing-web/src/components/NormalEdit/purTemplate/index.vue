<template>
  <div class="cellBox">
    <mt-input id="stepQuoteName" style="display: none" :value="data.stepQuoteName"></mt-input>
    <div style="margin-top: 3px">
      <mt-data-grid v-bind="grid" height="100px" />
    </div>
    <div class="cellBox-btn">
      <div @click="showDialog">{{ data.stepQuoteName | listNumFormat }}</div>
    </div>
  </div>
</template>

<script>
import { i18n } from '@/main.js'
export default {
  // eslint-disable-next-line vue/require-prop-types
  props: ['source', 'list', 'rowData'],
  data() {
    return {
      data: {},
      grid: {
        allowFiltering: false,
        columnData: [
          {
            field: 'startValue',
            headerText: this.$t('起始值(≥)')
          },
          {
            field: 'endValue',
            headerText: this.$t('结束值(<)')
          },
          {
            field: 'remark',
            headerText: this.$t('说明')
          },
          {
            field: 'untaxedUnitPrice',
            headerText: this.$t('单价(未税)')
          }
        ],
        dataSource: []
      }
    }
  },
  filters: {
    listNumFormat(value) {
      if (value && value.length > 0) {
        return i18n.t('编辑阶梯报价方案')
        // return JSON.parse(value).length;
      } else {
        return i18n.t('编辑阶梯报价方案')
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.rowData.itemStages && this.rowData.itemStages.length) {
        this.grid.dataSource = this.rowData.itemStages
      }
    })
  },
  methods: {
    showDialog() {
      console.error(this.list, 'this.list123')
      this.$bus.$emit('specEndEdit')
      setTimeout(() => {
        this.$dialog({
          modal: () => import('./components/editConstructionDialog'),
          data: {
            title: i18n.t('阶梯报价定义'),
            source: this.source,
            rowData: this.rowData,
            list: this.list
          },
          success: (data) => {
            this.$set(this.data, 'stepQuoteName', JSON.stringify(data))
            this.grid.dataSource = JSON.parse(this.data.stepQuoteName)
          }
        })
      }, 200)
    }
  }
}
</script>

<style lang="scss" scoped>
.cellBox {
  width: 100%;
  padding-top: 3px;
  &-btn {
    width: 100%;
    display: flex;
    justify-content: center;
    margin: 3px 0;
    div {
      color: rgba(0, 70, 156, 1);
    }
  }
}
/deep/ .mt-data-grid {
  .e-grid {
    th.e-headercell,
    td.e-rowcell {
      font-size: 8px !important;
      height: 30px;
    }
  }
}
/deep/ .e-headertext {
  font-size: 8px !important;
}
/deep/ .e-headercelldiv {
  height: 12px !important;
  line-height: 12px !important;
  padding: 0 !important;
}
</style>
