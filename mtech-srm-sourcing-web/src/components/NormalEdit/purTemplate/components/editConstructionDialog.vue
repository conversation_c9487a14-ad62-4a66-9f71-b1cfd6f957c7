<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <spe-tabs :tab-list="tabSource" :tab-index="tabIndex" @changeTab="changeTab"> </spe-tabs>
      <mt-template-page ref="templateRef" :template-config="pageConfig" />
    </div>
  </mt-dialog>
</template>

<script>
import { cloneDeep } from 'lodash'
import { columnData } from './config'
import speTabs from './speTabs.vue'
export default {
  components: { speTabs },
  data() {
    return {
      data: {},
      tabSource: [],
      tabIndex: 0,
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.next,
          buttonModel: { isPrimary: 'true', content: this.$t('确定并下一条') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false //代表不使用组件中的toolbar配置，使用当前项的toolbar
          },
          gridId:
            this.$permission.gridId['purchase'][this.modalData.source]['detail']['tabs'][
              'dialog_editConstructionDialog'
            ],
          grid: {
            columnData: columnData,
            dataSource: [],
            allowFiltering: false,
            allowPaging: false,
            allowSorting: false,
            editSettings: {
              allowAdding: true,
              allowEditing: true,
              allowDeleting: true,
              allowEditOnDblClick: true
            }
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    showLeft() {
      return false
    }
  },
  mounted() {
    if (this.modalData) {
      this.tabSource = cloneDeep(this.modalData.list)
      let _index = 0
      this.tabSource.forEach((x, i) => {
        if (!x.itemName) {
          _index = i + 1
          this.$toast({
            content: this.$t(`第${_index}条数据的物料名称不能为空`),
            type: 'warning'
          })
          return
        }
        if (x.itemCode === this.modalData.rowData.itemCode) {
          this.tabIndex = i
          this.pageConfig[0].grid.dataSource = this.modalData.rowData.itemStages
        }
      })
      if (_index === 0) {
        this.$refs['dialog'].ejsRef.show()
      }
    }
  },
  methods: {
    next() {
      let _currentTabRef = this.$refs.templateRef.getCurrentTabRef()
      let _selectRecords = _currentTabRef?.grid?.getCurrentViewRecords() ?? []
      if (_selectRecords.length < 1) return
      if (this.tabIndex == this.tabSource.length - 1) return
      this.tabSource[this.tabIndex].itemStages = _selectRecords
      this.tabIndex += 1
      this.pageConfig[0].grid.dataSource = this.tabSource[this.tabIndex].itemStages
    },
    // 结束编辑状态
    endEdit() {
      let _current = this.$refs.templateRef.getCurrentTabRef()
      _current?.grid.endEdit()
      _current?.grid.hideSpinner()
    },
    confirm() {
      this.endEdit()
      let dataSource = this.$refs.templateRef.getCurrentTabRef().grid.getCurrentViewRecords()
      let flag = false
      this.tabSource[this.tabIndex].itemStages = dataSource
      this.tabSource.forEach((x) => {
        if (x.itemStages[0].untaxedUnitPrice) {
          x.biddingItemDTO.untaxedUnitPrice = x.itemStages[0].untaxedUnitPrice
        }
        if (x.itemStages.list > 0) {
          x.itemStages.forEach((v) => {
            if (!v.untaxedUnitPrice || v.untaxedUnitPrice === 0) {
              flag = true
            }
          })
        }
      })
      if (flag) {
        this.$toast({
          content: this.$t('单价未税不能为空或者0'),
          type: 'warning'
        })
        return
      }
      this.$emit('confirm-function', dataSource)
      this.$bus.$emit('allPriceChange', this.tabSource)
      this.tabSource.forEach((x) => {
        if (x.itemCode == this.modalData.rowData.itemCode) {
          this.$bus.$emit('changeShowInfo', x)
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },

    changeTab(index) {
      this.endEdit()
      let _currentTabRef = this.$refs.templateRef.getCurrentTabRef()
      let _selectRecords = _currentTabRef?.grid?.getCurrentViewRecords() ?? []
      this.tabSource[this.tabIndex].itemStages = _selectRecords
      this.tabIndex = index
      this.pageConfig[0].grid.dataSource = this.tabSource[this.tabIndex].itemStages
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
