import { i18n } from '@/main.js'
import { createEditInstance } from '@/utils/ej/dataGrid'
const editInstance = createEditInstance()
export const columnData = [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    field: 'startValue',
    headerText: i18n.t('起始值(≥)'),
    width: 100,
    allowEditing: false
  },
  {
    field: 'endValue',
    width: 100,
    headerText: i18n.t('结束值(<)'),
    allowEditing: false
  },
  {
    field: 'remark',
    headerText: i18n.t('说明'),
    allowEditing: false
  },
  {
    field: 'untaxedUnitPrice',
    width: 100,
    headerText: i18n.t('单价(未税)'),
    allowEditing: true,
    edit: editInstance.create({
      getEditConfig: () => ({
        type: 'number',
        min: 0,
        precision: 2
      })
    })
  }
]
