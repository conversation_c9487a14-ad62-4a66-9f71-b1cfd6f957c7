<template>
  <div>
    <mt-input id="stepQuoteName" style="display: none" :value="data.stepQuoteName"></mt-input>
    <div v-show="this.grid.dataSource.length == 0" class="cellBox-btn">暂无阶梯报价方案</div>
    <div style="margin-top: 5px" v-show="this.grid.dataSource && this.grid.dataSource.length > 0">
      <mt-data-grid v-bind="grid" height="100px" />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      data: {},
      grid: {
        allowFiltering: false,
        columnData: [
          {
            field: 'startValue',
            headerText: this.$t('起始值(≥)')
          },
          {
            field: 'endValue',
            headerText: this.$t('结束值(<)')
          },
          {
            field: 'remark',
            headerText: this.$t('说明')
          },
          {
            field: 'untaxedUnitPrice',
            headerText: this.$t('单价(未税)')
          }
        ],
        dataSource: []
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      console.error(this.data, 12345)
      if (this.data.itemStages && this.data.itemStages.length) {
        this.grid.dataSource = this.data.itemStages
      }
    })
    this.$bus.$on('changeShowInfo', (data) => {
      this.grid.dataSource = data
    })
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.cellBox {
  width: 100%;
  padding-top: 3px;
  &-btn {
    width: 100%;
    display: flex;
    justify-content: center;
    margin: 3px 0;
    div {
      color: rgba(0, 70, 156, 1);
    }
  }
}
/deep/ .mt-data-grid {
  .e-grid {
    th.e-headercell,
    td.e-rowcell {
      font-size: 8px !important;
      height: 30px;
    }
  }
}
/deep/ .e-headertext {
  font-size: 8px !important;
}
/deep/ .e-headercelldiv {
  height: 12px !important;
  line-height: 12px !important;
  padding: 0 !important;
}
</style>
