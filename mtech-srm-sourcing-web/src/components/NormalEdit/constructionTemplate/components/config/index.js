import { i18n } from '@/main.js'
import { createEditInstance } from '@/utils/ej/dataGrid'
const editInstance = createEditInstance()
const MAX_SAFE_INTEGER = 9999999999
export const columnData = [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    field: 'startValue',
    headerText: i18n.t('起始值(≥)'),
    allowEditing: false
  },
  {
    field: 'endValue',
    headerText: i18n.t('结束值(<)'),
    allowEditing: true,
    edit: editInstance.create({
      getEditConfig: () => ({
        type: 'number',
        min: 0,
        precision: 2,
        max: MAX_SAFE_INTEGER
      })
    })
  },
  {
    field: 'remark',
    headerText: i18n.t('说明'),
    allowEditing: true,
    edit: editInstance.create({
      getEditConfig: () => ({
        type: 'text',
        maxlength: 20
      })
    })
  }
]
