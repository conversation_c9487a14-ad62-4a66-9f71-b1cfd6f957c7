<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
      />
    </div>
  </mt-dialog>
</template>

<script>
import { columnData } from './config'
export default {
  data() {
    return {
      data: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [['Add', 'Delete']]
          },
          grid: {
            columnData: columnData,
            dataSource: [],
            allowFiltering: false,
            allowPaging: false,
            allowSorting: false,
            editSettings: {
              allowAdding: true,
              allowEditing: true,
              allowDeleting: true,
              allowEditOnDblClick: true
            }
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    showLeft() {
      return false
    }
  },
  mounted() {
    if (this.modalData.rowData && this.modalData.rowData.stepQuoteName) {
      this.pageConfig[0].grid.dataSource = JSON.parse(this.modalData.rowData.stepQuoteName) || []
    }
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    // 结束编辑状态
    endEdit() {
      let _current = this.$refs.templateRef.getCurrentTabRef()
      _current?.grid.endEdit()
      _current?.grid.hideSpinner()
    },
    confirm() {
      this.endEdit()
      let dataSource = this.$refs.templateRef.getCurrentTabRef().grid.getCurrentViewRecords()
      let flag = false
      let priceFlag2 = false
      dataSource.forEach((x, i) => {
        if (x.endValue && x.endValue >= 0 && x.endValue <= x.startValue) flag = true
        if (i >= 1 && x.startValue < dataSource[i - 1].endValue) priceFlag2 = true
      })
      if (flag) {
        this.$toast({
          content: this.$t('起始值不能大于结束值'),
          type: 'warning'
        })
        return
      }
      if (priceFlag2) {
        this.$toast({
          content: this.$t('起始值不能小于上一行结束值'),
          type: 'warning'
        })
        return
      }
      this.$bus.$emit('itemStageListChange', dataSource)
      this.$emit('confirm-function', dataSource)
    },
    cancel() {
      this.$emit('cancel-function')
    },
    handleClickToolBar(e) {
      if (e.toolbar.id == 'Add') {
        let _currentTabRef = this.$refs.templateRef.getCurrentTabRef()
        let _selectRecords = _currentTabRef?.grid?.getCurrentViewRecords() ?? []
        if (_selectRecords.length == 0) {
          this.pageConfig[0].grid.dataSource.push({
            startValue: 0,
            endValue: '',
            remark: ''
          })
        } else {
          console.error(_selectRecords)
          let startValue = _selectRecords[_selectRecords.length - 1].endValue
          let remark = _selectRecords[_selectRecords.length - 1].remark
          if (startValue === 0) {
            this.$toast({
              content: this.$t('结束值不应该为0'),
              type: 'warning'
            })
          }
          if (!startValue) {
            this.$toast({
              content: this.$t('结束值不能为空'),
              type: 'warning'
            })
            return
          }
          if (startValue) {
            this.pageConfig[0].grid.dataSource.push({
              startValue,
              endValue: '',
              remark: ''
            })
          }
          // 未知原因新增下一行时上一行的结束值与说明丢失
          let _index = this.pageConfig[0].grid.dataSource.length - 2
          this.pageConfig[0].grid.dataSource[_index].endValue = startValue
          this.pageConfig[0].grid.dataSource[_index].remark = remark
        }
      } else if (e.toolbar.id == 'Delete') {
        let _selectGridRecords = e.gridRef.getMtechGridRecords()
        if (_selectGridRecords?.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        this.handleBatchDelete(_selectGridRecords)
      }
    },
    //删除
    handleBatchDelete(_records) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          let _currentTabRef = this.$refs.templateRef.getCurrentTabRef()
          let _selectRecords = _currentTabRef?.grid?.getCurrentViewRecords() ?? []
          _records.forEach((x) => {
            _selectRecords.forEach((v, i) => {
              if (JSON.stringify(x) === JSON.stringify(v)) {
                _selectRecords.splice(i, 1)
              }
            })
          })
          this.$set(this.pageConfig[0].grid, 'dataSource', _selectRecords)
        }
      })
    },
    changeTab(tab) {
      this.tabIndex = tab.index
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
