<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-template-page ref="templateRef" :template-config="pageConfig" />
    </div>
  </mt-dialog>
</template>

<script>
import { columnData, columnDataNoStage } from './config'
export default {
  data() {
    return {
      data: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.modalData.isShow ? [] : this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    if (this.modalData.route.name == 'contract-price-detail') {
      this.pageConfig = [
        {
          useToolTemplate: false,
          toolbar: [],
          grid: {
            allowFiltering: true,
            columnData: columnDataNoStage,
            allowSorting: false,
            allowSelection: true,
            selectionSettings: {
              type: 'Multiple',
              mode: 'Row'
            },
            recordDoubleClick: this.recordDoubleClick,
            asyncConfig: {
              url: this.$API.priceService.getReferList,
              params: { type: 1 },
              defaultRules: [
                {
                  field: 'record.itemCode',
                  type: 'string',
                  operator: 'equal',
                  value: this.modalData.itemCode
                }
              ]
            }
          }
        }
      ]
    } else if (
      this.modalData.route.name == 'fixed-point' &&
      this.modalData.route.query.decidePriceType == 5
    ) {
      this.pageConfig = [
        {
          useToolTemplate: false,
          toolbar: [],
          grid: {
            allowFiltering: true,
            columnData: columnDataNoStage,
            allowSorting: false,
            allowSelection: true,
            selectionSettings: {
              type: 'Multiple',
              mode: 'Row'
            },
            recordDoubleClick: this.recordDoubleClick,
            asyncConfig: {
              url: this.$API.priceService.getReferList,
              params: { type: 2 },
              defaultRules: [
                {
                  label: '物料编码',
                  field: 'record.itemCode',
                  type: 'string',
                  operator: 'equal',
                  value: this.modalData.data.itemCode
                },
                {
                  label: '价格类型',
                  field: 'record.priceValueType',
                  type: 'string',
                  operator: 'equal',
                  value: '4'
                }
              ]
            }
          }
        }
      ]
      this.buttons = [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('确定') }
        }
      ]
    } else {
      this.pageConfig = [
        {
          useToolTemplate: false,
          toolbar: [],
          grid: {
            allowFiltering: true,
            columnData: columnData,
            allowSorting: false,
            allowSelection: true,
            selectionSettings: {
              type: 'Multiple',
              mode: 'Row'
            },
            recordDoubleClick: this.recordDoubleClick,
            asyncConfig: {
              url: this.$API.priceService.getReferList,
              params: { type: 1 }
            }
          }
        }
      ]
    }
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    recordDoubleClick(e) {
      console.log('double', e)
      this.confirmEvent([e.rowData])
    },
    confirm() {
      let _records = this.$refs.templateRef.getCurrentTabRef().gridRef.getMtechGridRecords()
      if (_records.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      } else {
        this.$emit('confirm-function', _records)
      }
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
/deep/ .mt-data-grid {
  .e-grid {
    th.e-headercell,
    td.e-rowcell {
      font-size: 8px !important;
      height: 30px;
    }
  }
}
/deep/ .e-headertext {
  font-size: 8px !important;
}
/deep/ .e-headercelldiv {
  height: 12px !important;
  line-height: 12px !important;
  padding: 0 !important;
}
</style>
