import { i18n } from '@/main.js'
import Vue from 'vue'
export const columnData = [
  {
    width: '150',
    field: 'companyName',
    headerText: i18n.t('公司')
  },
  {
    width: '150',
    field: 'siteName',
    headerText: i18n.t('工厂')
  },
  {
    width: '150',
    field: 'purOrgName',
    headerText: i18n.t('采购组织')
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '400',
    field: 'pointBiddingItemStage',
    headerText: i18n.t('阶梯价格'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div class="dialog-content">
          <mt-data-grid v-bind="grid" height="100px" />
        </div>`,
          data() {
            return {
              data: {},
              grid: {
                allowFiltering: false,
                columnData: [
                  {
                    field: 'startValue',
                    headerText: this.$t('起始值(≥)')
                  },
                  {
                    field: 'endValue',
                    headerText: this.$t('结束值(<)')
                  },
                  {
                    field: 'remark',
                    headerText: this.$t('说明')
                  },
                  {
                    field: 'untaxedUnitPrice',
                    headerText: this.$t('单价(未税)')
                  }
                ],
                dataSource: []
              }
            }
          },
          methods: {},
          created() {
            this.grid.dataSource = this.data.pointBiddingItemStage
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  { width: '150', field: 'supplierCode', headerText: i18n.t('供应商名称') },
  { width: '150', field: 'taxedUnitPrice', headerText: i18n.t('单价（含税）') },
  {
    width: '150',
    field: 'untaxedUnitPrice',
    headerText: i18n.t('单价（不含税）')
  },
  { width: '150', field: 'bidTaxRateName', headerText: i18n.t('税率') },
  { width: '150', field: 'itemGroupName', headerText: i18n.t('品项组名称') }
]

export const columnDataNoStage = [
  {
    width: '150',
    field: 'companyName',
    headerText: i18n.t('公司')
  },
  {
    width: '150',
    field: 'siteName',
    headerText: i18n.t('工厂')
  },
  {
    width: '150',
    field: 'purOrgName',
    headerText: i18n.t('采购组织')
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  { width: '150', field: 'supplierCode', headerText: i18n.t('供应商名称') },
  { width: '150', field: 'taxedUnitPrice', headerText: i18n.t('单价（含税）') },
  {
    width: '150',
    field: 'untaxedUnitPrice',
    headerText: i18n.t('单价（不含税）')
  },
  { width: '150', field: 'bidTaxRateName', headerText: i18n.t('税率') },
  { width: '150', field: 'itemGroupName', headerText: i18n.t('品项组名称') }
]
