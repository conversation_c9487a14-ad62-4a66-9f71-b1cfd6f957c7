<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <!-- <mt-input
        :id="fieldName"
        v-model="data[fieldName]"
        disabled
        :width="130"
        :placeholder="headerTxt"
        @click="showDialog(true)"
      ></mt-input> -->
      <!-- <mt-icon
        style="width: 20px"
        name="icon_list_refuse"
        @click.native="handleClear"
        v-if="allowEditing"
      ></mt-icon> -->
      <span class="optionBtn" style="cursor: pointer" @click="showDialog">{{ $t('查看') }}</span>
      <!-- <mt-icon
        v-if="allowEditing"
        style="width: 20px"
        name="icon_input_search"
        @click.native="showDialog"
      ></mt-icon> -->
    </div>
  </div>
</template>

<script>
import { getValueByPath } from '@/utils/obj'
export default {
  props: {
    field: {
      type: String,
      default: ''
    },
    dataInfo: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      data: {},
      fieldName: '',
      headerTxt: '',
      allowEditing: true
    }
  },
  mounted() {
    this.fieldName = this.data.column?.field || this.field
    if (this.dataInfo) {
      this.data = { ...this.dataInfo }
    }
    this.allowEditing = this.data.column?.allowEditing === false ? false : true
    this.$bus.$on('changeReferChannel', (val) => {
      if (val == 0) {
        this.allowEditing = false
      } else {
        this.allowEditing = true
      }
    })
    if (!this.allowEditing) return
  },
  methods: {
    handleClear() {
      this.$set(this.data, 'priceReferRfxCodeAndLineNo', null)
      this.$bus.$emit('clearPriceReferRfxCodeAndLineNo')
    },
    showDialog(isShow) {
      let _itemCode = sessionStorage.getItem('currentPriceItemCode')
      if (!_itemCode && this.$route.name == 'contract-price-detail') {
        this.$toast({
          content: this.$t('请先选择物料'),
          type: 'warning'
        })
      } else {
        this.$dialog({
          modal: () => import('./components/selectGrid'),
          data: {
            title: this.$t('价格记录'),
            organizationId: sessionStorage.getItem('organizationId'),
            itemCode: _itemCode,
            isShow,
            route: this.$route,
            data: this.data
          },
          success: (data) => {
            this.$set(this.data, 'referSourceCode', data[0]['referSourceCode'])
            const fieldMap = {
              supplierCode: 'supplierCode',
              pointBiddingItemStage: 'pointBiddingItemStage',
              supplierId: 'supplierId',
              supplierName: 'supplierName',
              taxedUnitPrice: 'taxedUnitPrice',
              untaxedUnitPrice: 'untaxedUnitPrice',
              deliveryPlace: 'deliveryPlace',
              quoteAttribute: 'quoteAttribute',
              quoteMode: 'quoteMode',
              referLineNo: 'referLineNo',
              bidTaxRateCode: 'bidTaxRateCode',
              bidTaxRateName: 'bidTaxRateName',
              bidTaxRateValue: 'bidTaxRateValue'
            }
            Object.entries(fieldMap).map(([field, key]) => {
              this.$bus.$emit(`${field}Change`, getValueByPath(data[0], key))
            })
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
.optionBtn {
  color: #6386c1;
  cursor: pointer;
  margin-right: 10px;
}
</style>
