<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <mt-select
        v-if="showSelect"
        :id="fieldName"
        :allow-filtering="true"
        :data-source="itemsDataSources"
        :filtering="onFiltering"
        :fields="{ text: 'itemCode', value: 'itemCode' }"
        :value="value"
        :width="130"
        @select="handleSelectChange"
        :placeholder="headerTxt"
        popup-width="150px"
      ></mt-select>
      <mt-input
        v-show="!showSelect"
        :id="fieldName"
        v-model="data[fieldName]"
        disabled
        :width="130"
        :placeholder="headerTxt"
      ></mt-input>
      <mt-icon
        style="width: 20px"
        name="icon_list_refuse"
        @click.native="handleClear"
        v-if="allowEditing"
      ></mt-icon>
      <mt-icon
        v-if="allowEditing"
        style="width: 20px"
        name="icon_input_search"
        @click.native="showDialog"
      ></mt-icon>
    </div>
  </div>
</template>

<script>
import { getValueByPath } from '@/utils/obj'
import debounce from 'lodash.debounce'
export default {
  data() {
    return {
      data: {},
      fieldName: '',
      headerTxt: '',
      allowEditing: true,
      itemsDataSources: [],
      showSelect: true
    }
  },
  async mounted() {
    this.showSelect = false
    this.fieldName = this.data.column.field
    this.value = this.data.itemCode
    await this.getDataSource()
    this.allowEditing = this.data.column.allowEditing === false ? false : true
    if (!this.allowEditing) return
  },
  methods: {
    async getDataSource() {
      await this.$API.masterData
        .getItemListUrlList({
          page: { current: 1, size: 20 },
          defaultRules: [
            {
              label: '品类编码',
              field: 'itemCode',
              type: 'string',
              operator: 'contains',
              value: this.value
            }
          ]
        })
        .then((res) => {
          this.showSelect = true
          this.itemsDataSources.length = 0
          res.data.records.forEach((item) => {
            item.text = `(${item.itemCode})${item.itemName}`
            this.itemsDataSources.push(item)
          })
        })
        .catch(() => {
          this.showSelect = true
        })
    },
    onFiltering: debounce(function (e) {
      let { text } = e
      this.$API.masterData
        .getItemListUrlList({
          page: { current: 1, size: 20 },
          defaultRules: [
            {
              label: '品类编码',
              field: 'itemCode',
              type: 'string',
              operator: 'contains',
              value: text
            }
          ]
        })
        .then((res) => {
          let _records = []
          res.data.records.forEach((item) => {
            item.text = `(${item.itemCode})${item.itemName}`
            _records.push(item)
          })
          e.updateData(_records)
        })
    }, 1000),
    handleSelectChange(e) {
      if (e.e !== null) {
        let _records = [e.itemData]
        this.confirmEvent(_records)
      }
    },
    confirmEvent(_records) {
      let itemGroupId = []
      _records.forEach((e) => {
        itemGroupId = e.id
      })
      let param = {
        id: itemGroupId
      }
      this.$API.quotaConfig.basicDetail(param).then((res) => {
        if (res.data) {
          const fieldMap = {
            purUnitName: 'purchaseUnitName'
          }
          Object.entries(fieldMap).map(([field, key]) => {
            this.$bus.$emit(`${field}Change`, res.data[key])
          })
        }
      })

      this.confirm123(_records)
    },
    confirm123(data) {
      this.$set(this.data, 'itemCode', data[0]['itemCode'])
      const fieldMap = {
        itemId: 'id',
        itemName: 'itemName',
        spec: 'itemDescription', // 规格描述
        material: 'materialTypeName', // 材质
        unitName: 'baseMeasureUnitName', // 单位
        categoryId: 'categoryResponse.id', //品类名称
        categoryCode: 'categoryResponse.categoryCode', //品类名称
        categoryName: 'categoryResponse.categoryName', //品类名称
        // purUnitName: "purchaseUnitName", //采购单位
        priceUnitName: 'purchaseUnitName'
      }
      Object.entries(fieldMap).map(([field, key]) => {
        this.$bus.$emit(`${field}Change`, getValueByPath(data[0], key))
      })
      this.value = data[0]['itemCode']
    },
    handleClear() {
      this.$set(this.data, 'itemCode', null)
      this.value = null
      const fieldMap = {
        itemId: 'id',
        itemName: 'itemName',
        spec: 'itemDescription', // 规格描述
        material: 'materialTypeName', // 材质
        unitName: 'baseMeasureUnitName', // 单位
        categoryId: 'categoryResponse.id', // 品类ID
        categoryCode: 'categoryResponse.categoryCode', // 品类编码
        categoryName: 'categoryResponse.categoryName' //品类名称
      }
      Object.entries(fieldMap).map(([field]) => {
        this.$bus.$emit(`${field}Change`, null)
      })
    },
    showDialog() {
      // if (
      //   sessionStorage.getItem("organizationId") == "" ||
      //   sessionStorage.getItem("organizationId") == "undefined" ||
      //   !sessionStorage.getItem("organizationId")
      // ) {
      //   this.$toast({
      //     content: this.$t("当前未选择工厂，不可以选择物料"),
      //     type: "warning",
      //   });
      //   return;
      // }
      this.showSelect = false
      this.$dialog({
        modal: () => import('./components/selectGrid'),
        data: {
          title: this.$t('选择物料')
          // organizationId: sessionStorage.getItem("organizationId"),
        },
        success: (data) => {
          this.$set(this.data, 'itemCode', data[0]['itemCode'])
          const fieldMap = {
            itemId: 'id',
            itemName: 'itemName',
            spec: 'itemDescription', // 规格描述
            material: 'materialTypeName', // 材质
            unitName: 'baseMeasureUnitName', // 单位
            categoryId: 'categoryResponse.id', //品类名称
            categoryCode: 'categoryResponse.categoryCode', //品类名称
            categoryName: 'categoryResponse.categoryName', //品类名称
            // purUnitName: "purchaseUnitName", //采购单位
            priceUnitName: 'purchaseUnitName'
          }
          Object.entries(fieldMap).map(([field, key]) => {
            this.$bus.$emit(`${field}Change`, getValueByPath(data[0], key))
          })
          this.value = data[0]['itemCode']
          this.getDataSource()
        },
        close: () => {
          this.showSelect = true
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
</style>
