<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <mt-select
        v-if="showSelect"
        :id="fieldName"
        :allow-filtering="true"
        :data-source="itemsDataSources"
        :filtering="onFiltering"
        :fields="{ text: 'itemCode', value: 'itemCode' }"
        :value="showValue"
        :width="130"
        @select="handleSelectChange"
        :placeholder="headerTxt"
        popup-width="150px"
      ></mt-select>
      <mt-input
        :id="fieldName"
        :value="showValue"
        disabled
        :width="130"
        :placeholder="headerTxt"
        v-show="!showSelect"
      ></mt-input>
      <mt-icon
        style="width: 20px"
        name="icon_list_refuse"
        @click.native="handleClear"
        v-if="allowEditing"
      ></mt-icon>
      <mt-icon
        v-if="allowEditing"
        style="width: 20px"
        name="icon_input_search"
        @click.native="showDialog"
      ></mt-icon>
    </div>
  </div>
</template>

<script>
import debounce from 'lodash.debounce'

export default {
  props: {
    field: {
      type: String,
      default: ''
    },
    value: {
      type: String,
      default: ''
    },
    sourcingObjType: {
      type: String,
      default: ''
    },
    companyCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showValue: '',
      fieldName: '',
      headerTxt: '',
      allowEditing: true,
      itemsDataSources: [],
      showSelect: true
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.showValue = newVal
      },
      deep: true,
      immediate: true
    }
  },
  async mounted() {
    this.showSelect = false
    this.fieldName = this.field
    await this.getDataSource()
    if (!this.allowEditing) return
  },
  methods: {
    async getDataSource() {},
    onFiltering: debounce(function () {}, 1000),
    handleSelectChange() {},
    confirmEvent() {},
    // 清除
    handleClear() {
      this.$bus.$emit(`referItemNameChange`, null)
    },
    // 选择
    showDialog() {
      const param = JSON.parse(sessionStorage.getItem('referItemParam'))
      if (param.referChannel != 2) {
        this.$toast({
          content: '仅价格来源为价格记录，可选择参考物料',
          type: 'warning'
        })
        return
      }
      this.showSelect = false
      let stepQuoteType = -1
      if (param.stepQuoteType || param.stepQuoteType === 0) {
        stepQuoteType = param.stepQuoteType
      }
      this.$dialog({
        modal: () => import('./components/selectGrid'),
        data: {
          title: this.$t('选择参考物料'),
          stepQuoteType,
          companyCode: this.companyCode
        },
        success: (data) => {
          this.$bus.$emit(`referItemNameChange`, data[0])
          if (this.field === 'referItemCode') {
            this.showValue = data[0]['itemCode']
          } else {
            this.showValue = data[0]['itemName']
          }
        },
        close: () => {}
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    // justify-content: space-between;

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
.input {
  display: none;
}
</style>
