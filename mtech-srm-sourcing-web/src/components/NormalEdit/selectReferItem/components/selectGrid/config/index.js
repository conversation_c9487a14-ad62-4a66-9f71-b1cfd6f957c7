import { i18n } from '@/main.js'
import { Formatter } from '@/utils/ej/dataGrid/index'

export const columnData = [
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'stepValue',
    headerText: i18n.t('阶梯数量')
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('单价（未税）')
  },
  {
    width: '150',
    field: 'companyName',
    headerText: i18n.t('公司')
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },

  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织')
  },
  { field: 'deliveryPlace', headerText: i18n.t('直送地') },
  { field: 'priceUnitName', headerText: i18n.t('价格单位') },
  {
    field: 'quoteEffectiveStartDate',
    headerText: i18n.t('生效日期'),
    formatter: Formatter.createFmtDatetime('YYYY-MM-DD')
  },
  {
    field: 'quoteEffectiveEndDate',
    headerText: i18n.t('失效日期'),
    formatter: Formatter.createFmtDatetime('YYYY-MM-DD')
  },
  {
    field: 'referSourceCode',
    headerText: i18n.t('来源单号'),
    searchOptions: {
      renameField: 'sourceCode',
      operator: 'equal'
    }
  }
]

export const rules = (that) => {
  return [
    {
      label: i18n.t('公司编码'),
      field: 'companyCode',
      type: 'string',
      operator: 'equal',
      value: that.modalData.companyCode
    },
    {
      label: i18n.t('阶梯类型'),
      field: 'stageType',
      type: 'string',
      operator: 'equal',
      value: that.modalData.stepQuoteType
    },
    {
      label: i18n.t('价格分类'),
      field: 'priceClassify',
      type: 'number',
      operator: 'equal',
      value: 0
    }
  ]
}
