<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-template-page ref="templateRef" :template-config="pageConfig" />
    </div>
  </mt-dialog>
</template>

<script>
import { columnData, rules } from './config'

export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      data: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: [],
          grid: {
            allowFiltering: true,
            columnData,
            allowSorting: false,
            allowSelection: true,
            selectionSettings: {
              type: 'Multiple',
              mode: 'Row'
            },
            recordDoubleClick: this.recordDoubleClick,
            asyncConfig: {
              url: this.$API.rfxList.priceReferList,
              params: { type: 2 },
              defaultRules: [],
              rules: rules(this),
              serializeList: this.serializeList
            },
            queryCellInfo: this.queryCellInfo
          }
        }
      ]
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    recordDoubleClick(e) {
      this.confirmEvent([e.rowData])
    },
    confirm() {
      let _records = this.$refs.templateRef.getCurrentTabRef().gridRef.getMtechGridRecords()
      if (_records.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      } else {
        this.confirmEvent(_records)
      }
    },
    confirmEvent(_records) {
      this.$emit('confirm-function', _records)
      if (this.modalData.isAgGrid) return // aggrid改造不走以下逻辑
      if (this.modalData.stepQuoteType === -1) {
        return
      }
      const params = {
        page: { current: 1, size: 999 },
        type: 2,
        defaultRules: [],
        rules: [
          ...rules(this),
          {
            label: this.$t('分组编码'),
            field: 'priceGroupCode',
            type: 'string',
            operator: 'equal',
            value: _records[0].priceGroupCode
          }
        ]
      }
      this.$API.rfxList.getPriceReferList(params).then((res) => {
        const arr = []
        res.data.records.map((item) => {
          const { itemCode, itemName, referChannel, untaxedUnitPrice, stepValue, deliveryPlace } =
            item
          arr.push({
            referItemName: itemName,
            referItemCode: itemCode,
            referChannel,
            referItemUnitPriceUntaxed: untaxedUnitPrice,
            deliveryPlace,
            startValue: stepValue
          })
        })
        this.$bus.$emit('extStageSaveRequestsChange', arr)
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    serializeList(list) {
      // 非阶梯不分组
      if (this.modalData.stepQuoteType === -1) {
        return list
      }
      // 对数据进行分组
      const kindList = []
      const tempList = []
      const resList = []
      list.forEach((item) => {
        let groupByKey = item.priceGroupCode
        if (!kindList.includes(groupByKey)) {
          kindList.push(groupByKey)
          tempList.push([])
        }
        const i = kindList.indexOf(groupByKey)
        tempList[i].push(item)
      })
      tempList.forEach((item) => {
        item[0].rowSpan = item.length
        resList.push(...item)
      })
      return resList
    },
    queryCellInfo(args) {
      // 不合并的单元格
      const arr = ['stepValue', 'taxedUnitPrice', 'untaxedUnitPrice']
      if (args.data.rowSpan && !arr.includes(args.column.field)) {
        args.rowSpan = args.data.rowSpan
      }
      this.modalData.stepQuoteType !== -1 && (args.cell.style.borderLeft = '1px solid #e0e0e0')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
