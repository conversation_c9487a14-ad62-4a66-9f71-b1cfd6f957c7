<template>
  <c-loading :is-show="loading"></c-loading>
</template>

<script>
import CLoading from '@mtech/common-loading'

export default {
  data() {
    return {
      loading: true
    }
  },
  components: {
    CLoading
  },
  methods: {}
}
</script>
<style lang="scss">
.mt-loading {
  // background: none !important;
  position: absolute !important;
  z-index: 3;
  width: 100%;
  background: rgba(255, 255, 255, 0.6) !important;
}
</style>
