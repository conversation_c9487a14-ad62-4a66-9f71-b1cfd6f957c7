<template>
  <mt-dialog
    ref="dialog"
    css-class="mcu-create-proj-dialog"
    class="display-block"
    :header="title"
    :buttons="buttons"
    :open="onOpen"
    :close="init"
  >
    <div>
      <upload-file
        :view-file-data="viewFileData"
        :save-url="saveUrl"
        v-bind="$attrs"
        v-on="$listeners"
        ref="uploader"
        @change="fileChange"
      ></upload-file>
    </div>
  </mt-dialog>
</template>

<script>
import UploadFile from './uploader.vue'
export default {
  props: {
    saveUrl: {
      type: String,
      default: ''
    }
  },
  name: 'UploaderDialog',
  components: {
    UploadFile
  },
  data() {
    return {
      title: this.$t('附件上传'),
      isView: false,
      required: true,
      uploadData: [], // 上传完成的文件数据
      viewFileData: [], // 查看状态的数据
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  methods: {
    init() {
      this.$refs.uploader.init()
    },
    fileChange(data) {
      this.uploadData = data
      this.$emit('change', data)
    },
    /**
     * fileData: 文件信息;
     * isView：是否为查看状态;
     * required：是否必须;
     * title: 标题;
     */
    dialogInit(entryInfo) {
      const { fileData, isView, required, title } = entryInfo
      this.viewFileData = fileData || []
      this.isView = isView || false // 默认 上传状态
      this.required = required != null ? required : true // 默认 必须
      this.title = title || this.$t('附件上传') // 默认 "附件上传"
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    confirm() {
      if (
        !this.isView &&
        this.required &&
        this.uploadData.length == 0 &&
        this.viewFileData.length == 0
      ) {
        this.$toast({ content: this.$t('请上传附件'), type: 'warning' })
        return
      }
      this.$emit('confirm')
      this.handleClose()
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
<style lang="scss">
.mcu-create-proj-dialog {
  .e-dlg-content {
    padding: 40px !important;
    .mt-form {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .mt-form-item {
        width: 390px;
        .mt-form-item__content {
          width: 100%;
        }
      }
    }
  }

  .e-footer-content {
    line-height: 40px;

    .e-btn {
      vertical-align: initial;
      margin-left: 0px !important;
      margin-right: 36px;

      &:last-child {
        margin-right: 44px;
      }
    }
  }
}
</style>
