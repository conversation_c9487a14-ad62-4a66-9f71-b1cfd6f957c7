import Vue from 'vue'

// 通过 a 标签下载文件
export const download = (data) => {
  const { fileName, blob } = data

  if (!blob) {
    return
  }

  if (blob?.type === 'application/json') {
    const reader = new FileReader()
    reader.readAsText(blob, 'utf-8')
    reader.onload = function () {
      const readerRes = reader.result
      const resObj = JSON.parse(readerRes)
      Vue.prototype.$toast({
        content: resObj.msg,
        type: 'error'
      })
    }

    return
  }

  const a = document.createElement('a')
  a.href = URL.createObjectURL(blob)
  a.style.display = 'none'
  a.download = fileName
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
}
