<template>
  <!-- 文本输入框 -->
  <div v-html="templateData"></div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      templateData: null,
      uploadFileList: [] // 上传的附件
    }
  },
  computed: {
    field() {
      // 字段
      return this.params.column.colId
    },
    value() {
      // 值
      return this.params.data[this.field] || ''
    }
  },
  mounted() {
    this.formatData()
  },
  methods: {
    formatData() {
      this.templateData = this.value.replace(/(@number|@input)\{(.*?)\}/g, (match, p1, p2) => {
        return p2
          ? `<span style='font-weight:bold;padding:0 4px;'>${p2}</span>`
          : `<span style="border-bottom: 1px solid rgba(0,0,0,0.42);display:inline-block;width:80px;"></span>`
      })
    }
  }
}
</script>
