<template>
  <!-- 用作单元格点击跳转，后续扩展 -->
  <div class="supplier-wrap">
    <div class="cell-link">
      <span class="link" @click="handleClick" v-if="isClick"> 查看详细报价 </span>
      <span v-else>{{ text }}</span>
    </div>
    <mt-dialog ref="diffDialog" class="diffDialog" :header="header" width="1000">
      <div class="diff-dialog-content">
        <CustomAgGrid
          ref="CustomAgGrid"
          :toolbar="toolbar"
          :columns="columns"
          :suppress-row-transform="true"
          :row-data="tableData"
          :animate-rows="false"
          :params-type="2"
          :row-height="40"
          @onGridReady="onGridReady"
          @refresh="refresh"
          @search="search"
        >
        </CustomAgGrid>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import CustomAgGrid from '@/components/CustomAgGrid'
import { cloneDeep } from 'lodash'
export default {
  data() {
    return {
      header: this.$t('查看详细报价'),
      buttons: [{}],
      currentDescMap: {
        ZLPFONE: this.$t('供方参与过三新物料(创新型)技术开发或提供三新物料关键技术(含专利授权)')
      },
      tableData: [],
      toolbar: [],
      supplierSortColumns: [],
      field: 'currentDescText'
    }
  },
  components: {
    CustomAgGrid
  },
  computed: {
    queryParams() {
      // 字段
      return this.params.colDef?.cellRendererParams || {}
    },
    text() {
      return this.params.data.ratingDimensionCode === 'technical_bonus_points'
        ? this.currentDescMap[this.params.data['scoreStandardCode']]
        : this.params.data[this.field]
    },
    isClick() {
      return this.params.data['ratingDimensionCode'] === 'cost'
    },
    columns() {
      return [
        {
          field: 'supplierBaseInfo',
          headerName: '',
          children: [
            {
              field: 'lineNo',
              headerName: this.$t('行号'),
              width: 65
            },
            {
              field: 'itemCode',
              headerName: this.$t('物料编码'),
              width: 110
            },
            {
              field: 'itemName',
              headerName: this.$t('物料名称'),
              width: 280
            },
            {
              field: 'demandQty',
              headerName: this.$t('需求数量'),
              width: 120
            },
            {
              field: 'minPrice',
              headerName: this.$t('最低分'),
              width: 120
            }
          ]
        },
        {
          field: 'supplierSort',
          headerName: this.$t('供应商排名'),
          width: 120,
          children: this.supplierSortColumns
        }
      ]
    }
  },
  methods: {
    onGridReady(params) {
      this.agGrid = params
    },
    handleClick() {
      this.initTableData()

      this.$refs['diffDialog'].ejsRef.show()
    },
    cancel() {
      this.$emit('cancel-function')
    },
    // 初始化渲染表格
    async initTableData() {
      let res = await this.$API.publicSourcing.querySupplierQuoteList({
        rfxId: this.$route.query?.rfxId,
        supplierTenantId: this.$route.query.sId
      })

      if (res.code === 200) {
        this.tableData = res.data?.map((item, index) => {
          item.lineNo = index + 1
          item.supplierList.forEach((supplier) => {
            item[supplier.supplierCode] =
              supplier.untaxedUnitPrice === -999 ? '***' : supplier.untaxedUnitPrice
          })
          return item
        })
        let _columns = []
        this.tableData[0].supplierList.forEach((item) => {
          _columns.push({
            field: item.supplierCode,
            headerName: item.supplierName
          })
        })
        this.supplierSortColumns = _columns
        this.cacheTableData = cloneDeep(this.tableData)
      }
    },
    refresh() {
      this.tableData = cloneDeep(this.cacheTableData)
    },
    search() {}
  }
}
</script>
<style lang="scss">
.diff-dialog-content {
  padding: 15px 0;
  height: 545px;
  .grid-container {
    height: 100%;
  }
}
</style>
<style lang="scss" scoped>
.cell-link span.link {
  color: #00469c;
  cursor: pointer;
}
.hoverClass:hover {
  text-decoration: underline;
}
</style>
