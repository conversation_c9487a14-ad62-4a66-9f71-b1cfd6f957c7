<template>
  <!-- 结构阶梯格式编辑 -->
  <div class="cellBox">
    <div class="cellBox-btn">
      <div @click="showDialog">{{ value | listNumFormat }}</div>
    </div>
  </div>
</template>

<script>
import { i18n } from '@/main.js'
import { cloneDeep } from 'lodash'
export default {
  data() {
    return {
      dataSource: [],
      defaultDataSource: []
    }
  },
  computed: {
    queryParams() {
      // 字段
      return this.params.colDef?.cellRendererParams || {}
    },
    value() {
      return this.params.data?.itemStageList || []
    }
  },
  filters: {
    listNumFormat(value) {
      if (value && value.length > 0) {
        return i18n.t(`阶梯报价(${value.length})`)
      } else {
        return i18n.t('添加阶梯报价方案')
      }
    }
  },
  methods: {
    showDialog() {
      if (!this.params.data.stepQuoteType && this.params.data.stepQuoteType !== 0) {
        this.$toast({
          content: this.$t('请先选择阶梯报价类型'),
          type: 'warning'
        })
        return
      }
      if (this.params.data.stepQuote !== 1) {
        this.$toast({
          content: this.$t('请先选择是否阶梯报价'),
          type: 'warning'
        })
        return
      }
      let arr = [...this.defaultDataSource]
      if (this.params.data?.stepQuoteName && this.params.data?.stepQuoteName?.length) {
        arr =
          typeof this.params.data?.stepQuoteName[0] === 'string'
            ? JSON.parse(this.params.data?.stepQuoteName)
            : this.params.data?.stepQuoteName
      }
      this.$dialog({
        modal: () => import('@/components/NormalEdit/stepTemplate/components/stepDialog.vue'),
        data: {
          title: '阶梯数量',
          dataSource: arr
        },
        success: (data) => {
          // 更新行数据
          let stepData = {
            stepQuoteName: JSON.stringify(data),
            itemStageList: cloneDeep(data)
          }
          const rowData = Object.assign({}, this.params.node.data, stepData)
          this.params.node.setData(rowData)
        }
      })
    }
  }
}
</script>
<style lang="scss">
.cellBox {
  width: calc(100% + 20px);
  margin-left: -10px;
  margin-right: -10px;
  &-btn {
    width: 100%;
    display: flex;
    justify-content: center;
    margin: 3px 0;
    div {
      color: rgba(0, 70, 156, 1);
    }
  }
  p {
    border-left: 1px solid #e0e0e0;
    border-right: 1px solid #e0e0e0;
    border-bottom: 1px solid #e0e0e0;
    line-height: 30px;
    text-align: center;
    margin-bottom: 0 !important;
  }
}
/deep/ .mt-data-grid {
  .e-grid {
    th.e-headercell,
    td.e-rowcell {
      font-size: 8px !important;
      height: 30px;
    }
  }
}
/deep/ .e-headertext {
  font-size: 8px !important;
}
/deep/ .e-headercelldiv {
  height: 12px !important;
  line-height: 12px !important;
  padding: 0 !important;
}
</style>
