<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <mt-select
        v-show="showSelect"
        :id="fieldName"
        :allow-filtering="true"
        :data-source="dataSource"
        :filtering="filtering"
        :fields="{ text: 'itemCode', value: 'itemCode' }"
        v-model="value"
        :width="130"
        @select="handleSelectChange"
        :placeholder="headerTxt"
        popup-width="180px"
      ></mt-select>
      <mt-input
        :id="fieldName"
        v-model="value"
        disabled
        :width="130"
        :placeholder="headerTxt"
        v-show="!showSelect"
      ></mt-input>
      <mt-icon
        style="width: 20px"
        name="icon_list_refuse"
        @click.native="handleClear"
        v-if="allowEditing"
      ></mt-icon>
      <mt-icon
        v-if="allowEditing"
        style="width: 20px"
        name="icon_input_search"
        @click.native="showDialog"
      ></mt-icon>
    </div>
  </div>
</template>

<script>
import { getValueByPath } from '@/utils/obj'
import { debounce, cloneDeep } from 'lodash'
export default {
  model: {
    prop: 'modelVal',
    event: 'syncChange'
  },
  props: {
    modelVal: {
      type: [String, Object, Number],
      default: null
    },
    params: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      headerTxt: '',
      allowEditing: true,
      dataSource: [],
      showSelect: true
    }
  },
  computed: {
    paramsInfo() {
      // props 传递的参数
      return this.params.colDef?.cellEditorParams?.editConfig?.props || {}
    },
    detailInfo() {
      return this.paramsInfo?.detailInfo || {}
    },
    fieldName() {
      return this.params.colDef?.fieldCode
    },
    value: {
      get() {
        return this.modelVal
      },
      set(val) {
        this.$emit('syncChange', val)
      }
    }
  },
  async mounted() {
    this.showSelect = false
    await this.getDataSource()
    if (!this.allowEditing) return
  },
  methods: {
    // 获取数据源(物料)
    async getDataSource(e) {
      let value = e ? e.text : this.value
      const { rfxId } = this.paramsInfo
      const funcName =
        this.detailInfo?.sourcingObjType === 'serial_item' ? 'queryItemsTwoData' : 'queryItemsData'
      const _siteId = this.params.data?.siteId || this.detailInfo.siteId
      if (!_siteId) {
        this.$toast({
          content: this.$t('当前未选择工厂，不可以选择物料'),
          type: 'warning'
        })
        this.showSelect = true
        return
      }
      await this.$API.comparativePrice[funcName]({
        page: { current: 1, size: 20 },
        organizationId: _siteId,
        siteCode: this.params.data?.siteCode,
        rfxId,
        defaultRules: [
          {
            label: '物料编码',
            field: 'itemCode',
            type: 'string',
            operator: 'contains',
            value
          }
        ]
      })
        .then((res) => {
          let _records = []
          res.data.records.forEach((item) => {
            item.text = `(${item.itemCode})${item.itemName}`
            _records.push(item)
          })
          if (e) {
            // 更新
            e.updateData(_records)
          } else {
            // 初始化
            this.dataSource = cloneDeep(_records)
          }

          this.showSelect = true
        })
        .catch(() => {
          this.showSelect = true
        })
    },
    // 过滤
    filtering: debounce(function (e) {
      this.getDataSource(e)
    }, 1000),
    // 下拉选择事件监听
    handleSelectChange(e) {
      if (!e.e) return
      let _records = [e.itemData]
      let _data = _records[0]
      let valid = this.validData(_data) //数据校验
      if (!valid) return

      let params = {
        rfxId: this.paramsInfo.rfxId,
        itemCodeList: [_data.itemCode]
      }
      // 接口物料重复校验
      this.$API.masterData.checkItemCode(params).then((res) => {
        if (res?.data?.length) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t(
                `进行中的单据：${res.data[0].rfxCode} ，已存在物料编码：${res.data[0].itemCode},是否继续添加?`
              )
            },
            success: () => {
              this.confirmEvent(_records)
            }
          })
        } else {
          this.confirmEvent(_records)
        }
      })
    },
    // 数据校验
    validData(data) {
      // 1.整机模组外购 中 screenCoe tconCode 不作物料得数据校验
      if (
        this.detailInfo.sourcingObjType === 'module_out_buy' &&
        ['screenCode', 'tconCode'].includes(this.fieldName)
      ) {
        this.confirmEvent(data)
        return
      }
      // 2.二次、已有：如果不存在价格记录报错
      if (
        ['second_inquiry', 'exist'].includes(this.detailInfo.sourcingType) &&
        data.isPriceRecord === 0
      ) {
        this.$toast({
          content: this.$t('请选择有价格记录的物料'),
          type: 'warning'
        })
        return
      }
      /**
       * 3.新品：
       * 0:没有价格类型：校验价格记录
       * 1.暂估价：校验暂估价 和 执行价
       * 2.srm价格：校验srm价格
       * 3.执行价：校验执行价
       * 4.基价：校验基价
       *  */
      const priceMap = {
        '': {
          validField: ['isPriceRecord'],
          validText: this.$t('请选择没有价格记录的物料')
        },
        predict_price: {
          validField: ['predictPrice', 'executePrice'],
          validText: this.$t('请选择没有执行价和暂估价记录的物料')
        },
        srm_price: {
          validField: ['srmPrice'],
          validText: this.$t('请选择没有执行价和暂估价记录的物料')
        },
        execute_price: {
          validField: ['executePrice'],
          validText: this.$t('请选择没有执行价和暂估价记录的物料')
        },
        basic_price: {
          validField: ['basicPrice'],
          validText: this.$t('请选择没有执行价和暂估价记录的物料')
        }
      }
      if (this.detailInfo.sourcingType === 'new_products') {
        let priceClassification = this.detailInfo.priceClassification || ''
        for (let i = 0; i < priceMap[priceClassification].length; i++) {
          if (data[priceMap[priceClassification][i]] === 1) {
            this.$toast({
              content: priceMap['validText'],
              type: 'warning'
            })
            return
          }
        }
      }
      // 4.物物料重复校验
      if (this.hasItemCode(data.itemCode)) return
      return true
    },
    // 物料重复校验-前端
    hasItemCode(itemCode) {
      if (this.paramsInfo.tableData.find((row) => row.itemCode === itemCode)) {
        this.$toast({
          content: this.$t('物料已存在,不可重复添加'),
          type: 'warning'
        })
        return true
      }
      return false
    },
    // 执行数据联动
    confirmEvent(_records) {
      let itemGroupId = []
      _records.forEach((e) => {
        itemGroupId = e.id
      })
      let param = {
        id: itemGroupId
      }
      // 此接口经确认暂无数据，并不能执行，有可能是废弃功能 TODO:确认使用场景并完善（是否带出code和id）
      this.$API.quotaConfig.basicDetail(param).then((res) => {
        if (res.data) {
          const fieldMap = {
            purUnitName: 'purchaseUnitName'
          }
          Object.entries(fieldMap).map(([field, key]) => {
            this.params.node.setDataValue(field, res.data[key])
          })
        }
      })
      // 其他数据联动
      this.dataChange(_records[0])
    },
    // 基础数据联动
    async dataChange(data) {
      const _itemCode = data ? data['itemCode'] : ''
      this.value = _itemCode
      // 屏编码、tcon编码不修改其他信息
      if (['screenCode', 'tconCode'].includes(this.fieldName)) {
        return
      }

      // 采购组联动
      // await this.getPurGroup()
      // 物料编码及联动
      let fieldMap = {
        itemId: 'id',
        itemName: 'itemName',
        spec: 'itemDescription', // 规格描述
        material: 'materialTypeName', // 材质
        unitName: 'baseMeasureUnitName', // 单位
        unitCode: 'baseMeasureUnitCode',
        priceUnitCode: 'purchaseUnitCode'
      }
      // 如果页面配置了价格单位，则不用根据物料编码联动，由页面逻辑自动带默认值
      const priceUnitName = this.params.columnApi.getColumn('priceUnitName')
      if (!priceUnitName) {
        fieldMap.priceUnitName = 'purchaseUnitName'
      }
      let basicData = {}
      // 基础信息设置
      Object.entries(fieldMap).map(([field, key]) => {
        basicData[field] = data ? getValueByPath(data, key) : ''
      })

      // 品类信息设置
      let categoryResponse = {
        categoryName: '',
        categoryCode: '',
        categoryId: ''
      }
      if (data) {
        // 优先取categoryResponse
        if ('categoryResponse' in data && data.categoryResponse !== null) {
          categoryResponse = {
            categoryName: data.categoryResponse.categoryName,
            categoryCode: data.categoryResponse.categoryCode,
            categoryId: data.categoryResponse.id
          }
        } else if (data.categoryCode) {
          categoryResponse = {
            categoryName: data.categoryName,
            categoryCode: data.categoryCode,
            categoryId: data.id
          }
        }
      }
      // 采购信息设置
      let purGroupData = await this.getPurGroup({
        itemCode: _itemCode,
        siteCode: this.params.data?.siteCode
      })

      // smt pcb版号同步
      let itemExtMap = this.params.node.data.itemExtMap || {}
      if (this.detailInfo.sourcingObjType === 'smt') {
        let res = await this.getComponentInfo({
          companyCode: this.detailInfo.companyCode,
          componentCode: this.params.data?.itemCode
        })
        itemExtMap.pcbCode = res.pcbCode
      }
      // 同步改变drawingUrlChange
      // this.$bus.$emit('drawingUrlChange', _itemCode)
      // 更新行数据
      const paramsData = Object.assign({}, this.params.node.data, { itemExtMap })
      const rowData = Object.assign({}, paramsData, basicData, categoryResponse, purGroupData)
      this.params.node.setData(rowData)
    },
    // 获取采购组信息
    async getPurGroup(data) {
      let params = {
        itemCode: data.itemCode,
        organizationCode: data.siteCode
      }
      const res = await this.$API.masterData.getPurGroup(params)
      return {
        purGroupName: res.data?.purchaseGroupName,
        purGroupCode: res.data?.purchaseGroupCode
      }
    },
    // 获取pcb版本号
    async getComponentInfo(data) {
      let params = {
        companyCode: data.companyCode,
        componentCode: data.componentCode
      }
      const res = await this.$API.smtComponentItem.querySmtComponent(params)
      return {
        pcbCode: res.data?.itemCode
      }
    },
    // 清除数据
    handleClear() {
      this.dataChange()
    },
    showDialog() {
      const _siteId = this.params.data?.siteId || this.detailInfo.siteId
      if (!_siteId) {
        this.$toast({
          content: this.$t('当前未选择工厂，不可以选择物料'),
          type: 'warning'
        })
        return
      }
      const { rfxId, source, tableData } = this.paramsInfo
      this.showSelect = false
      this.$dialog({
        modal: () =>
          import('COMPONENTS/NormalEdit/checkSelectItemCode/components/selectGrid/index.vue'),
        data: {
          title: this.$t('选择物料'),
          rfxId,
          submitTableData: () => {
            return tableData
          },
          siteParam: {
            organizationId: _siteId,
            siteCode: this.params.data?.siteCode
          },
          sourcingType: this.detailInfo.sourcingType,
          sourcingObjType: this.detailInfo.sourcingObjType,
          source,
          companyCode: this.detailInfo.companyCode,
          priceClassification: this.detailInfo.priceClassification,
          checkSelectItem:
            this.detailInfo.sourcingObjType === 'module_out_buy' &&
            ['screenCode', 'tconCode'].includes(this.fieldName)
              ? false
              : true
        },
        success: (data) => {
          this.dataChange(data[0])
        },
        close: () => {
          this.showSelect = true
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;
  .mt-select,
  .mt-input {
    // height: 30px;
    span {
      height: 30px;
      margin-top: 0px;
      margin-bottom: 0px;
    }
  }
  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    // justify-content: space-between;

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
    }
    /deep/ .e-input.e-disabled {
      height: 30px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
.input {
  display: none;
}
</style>
