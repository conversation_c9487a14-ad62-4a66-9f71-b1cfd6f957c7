<template>
  <div class="cell-file">
    <div>
      <span class="cursor" v-if="value.length" @click="showFiledVolist"
        >{{ $t('柜量明细') }}({{ value.length }})</span
      >
    </div>
    <!-- 柜量明细 -->
    <mt-dialog
      ref="filedVolistRef"
      css-class="column-setting"
      :buttons="buttons"
      :header="$t('柜量明细')"
    >
      <mt-template-page ref="templateRef" class="template-height" :template-config="pageConfig">
      </mt-template-page>
    </mt-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { i18n } from '@/main.js'
// 草稿或审批拒绝状态才能再次上传
export default {
  components: {},
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.cancel,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 使用组件中的toolbar配置
          toolbar: [],
          grid: {
            columnData: [
              {
                width: '150',
                field: 'fieldCode',
                headerText: this.$t('月份')
              },
              {
                width: '150',
                field: 'fieldData',
                headerText: this.$t('柜量')
              }
            ],
            dataSource: [],
            allowPaging: false,
            asyncConfig: {}
          }
        }
      ]
    }
  },
  computed: {
    field() {
      // 字段
      return this.params.column.colId
    },
    position() {
      return this.params.colDef?.cellRendererParams?.position || 'seaItemResponse'
    },
    value() {
      // 值
      let _value = this.params.data[this.position][this.field]
      return _value || []
    },
    handleable() {
      // 是否可编辑
      return this.params.colDef?.cellRendererParams?.handleable
    }
  },
  mounted() {
    this.uploadFileList = cloneDeep(this.value)
  },
  methods: {
    // 显示柜量明细
    showFiledVolist() {
      this.$refs.filedVolistRef.ejsRef.show()
      this.$set(this.pageConfig[0].grid, 'dataSource', this.value || [])
    },
    cancel() {
      this.$refs.filedVolistRef.ejsRef.hide()
    },
    // handler - 查看
    view() {
      this.showUploadDialog('view')
    },
    // handler - 上传
    upload() {
      this.showUploadDialog('upload')
    },
    // dialog - show
    showUploadDialog() {
      const dialogParams = {
        fileData: cloneDeep(this.value),
        isView: this.handleable ? false : true, //是否可上传
        required: false, // 是否必须
        title: i18n.t('附件')
      }
      this.$refs.uploaderDialog?.dialogInit(dialogParams)
    },

    // dialog - hander - change
    change(data) {
      if (!this.handleable) return
      this.uploadFileList = data
    },
    // dialog - hander - confirm
    confirm() {
      if (!this.handleable) return
      let _uploadFileList = cloneDeep(this.uploadFileList)
      if (this.isTransform) {
        _uploadFileList.forEach((file) => {
          if (!file.syFileId) {
            file.sysFileId = file.id
            delete file.id
          }
        })
        this.params.node.setDataValue(this.field, _uploadFileList)
        return
      }
      this.params.node.setDataValue(this.field, JSON.stringify(_uploadFileList))
    }
  }
}
</script>

<style lang="scss" scoped>
.cell-file span {
  color: #00469c;
  &.cursor {
    cursor: pointer;
  }
}
</style>
