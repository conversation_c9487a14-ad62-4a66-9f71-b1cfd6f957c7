<template>
  <div>
    <RemoteAutocomplete
      v-model="value"
      v-bind="$attrs"
      @change="handleSelectChange"
      @getOptions="getOptions"
    ></RemoteAutocomplete>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
export default {
  components: {
    RemoteAutocomplete
  },
  model: {
    prop: 'modelVal',
    event: 'syncChange'
  },
  props: {
    modelVal: {
      type: [String, Object, Number],
      default: null
    }
  },
  data() {
    return {
      dataSelectList: []
    }
  },
  computed: {
    value: {
      get() {
        return this.modelVal
      },
      set(val) {
        this.$emit('syncChange', val)
      }
    },
    params() {
      return this.$attrs?.cellParams || {}
    }
  },
  async mounted() {},
  methods: {
    getOptions(val) {
      this.dataSelectList = cloneDeep(val)
    },
    // select 监听
    handleSelectChange(e) {
      let fieldCode = this.params.colDef?.fieldCode
      let rowData = {}
      // let data = this.params?.node?.data || {}
      if (fieldCode === 'categoryName') {
        const categoryData = this.dataSelectList.find(
          (item) => item.categoryName === e.itemData.categoryName
        )
        rowData = Object.assign({}, this.params?.node?.data, {
          categoryId: categoryData.id,
          categoryCode: categoryData.categoryCode
        })
      }
      if (fieldCode === 'internationalSupplierName') {
        const internationalSupplierData = this.dataSelectList.find(
          (item) => item.supplierName === e.itemData.supplierName
        )
        rowData = Object.assign({}, this.params?.node?.data, {
          internationalSupplierCode: internationalSupplierData.supplierCode
        })
      }
      if (fieldCode === 'domesticSupplierName') {
        const domesticSupplierData = this.dataSelectList.find(
          (item) => item.supplierName === e.itemData.supplierName
        )
        rowData = Object.assign({}, this.params?.node?.data, {
          domesticSupplierCode: domesticSupplierData.supplierCode
        })
      }
      // if (fieldCode === 'countryName') {
      //   const countryData = this.dataSelectList.find(
      //     (item) => item.countryCode === e.itemData.countryCode
      //   )
      //   const itemExtMap = Object.assign({}, data?.itemExtMap, {
      //     countryCode: countryData.countryCode
      //   })
      //   rowData = Object.assign({}, this.params?.node?.data)
      //   rowData.itemExtMap = itemExtMap
      // }
      if (fieldCode === 'paymentCondition') {
        rowData = Object.assign({}, this.params?.node?.data, {})
      }
      this.params.node.setData(rowData)
    }
  }
}
</script>
