import debounce from 'lodash/debounce'
export default {
  name: 'CellDesc',
  computed: {
    value() {
      // 字段
      return this.params.value
    }
  },
  props: {
    params: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      renderData: null,
      inputData: {}
    }
  },
  mounted() {
    this.renderData = this.formatData(this.value)
    this.tempalateValue = this.value
  },
  methods: {
    formatData(str) {
      let regex = /(@\w+{([^}]*)})|([^@]+)/g
      let result = []
      let match
      let index = 0
      while ((match = regex.exec(str)) !== null) {
        if (match[1]) {
          let type = match[1].split('{')[0].substring(1)
          let value = match[1].match(/{(.*?)}/)[1]
          let field = `input${index}`
          result.push({ type: type, value: value, field })
          this.inputData[field] = value
          index++
        } else {
          result.push({ type: 'text', value: match[0] })
        }
      }
      return result
    },

    toText(str, data) {
      let arr = Object.values(data)
      let index = 0
      return str.replace(/(?<=\{)[^}]*(?=\})/g, () => {
        let val = arr[index]
        index++
        return val
      })
    },
    handeleInput: debounce(function (val, field) {
      this.inputData[field] = val
      this.$emit('change', this.toText(this.value, this.inputData))
    }, 200)
  },
  render() {
    return (
      <div class='desc-item'>
        {this.renderData?.length &&
          this.renderData?.map((item) => {
            if (item.type === 'text' && item.value) {
              return item.value
            }
            if (item.type === 'number') {
              const field = item.field
              return (
                <mt-input-number
                  v-model={this.inputData[field]}
                  onInput={(e) => this.handeleInput(e, field)}
                />
              )
            }
            if (item.type === 'input') {
              const field = item.field
              return (
                <mt-input
                  value={this.inputData[field]}
                  onInput={(e) => this.handeleInput(e, field)}
                />
              )
            }
          })}
      </div>
    )
  }
}
