<template>
  <!-- 用作单元格点击跳转，后续扩展 -->
  <div class="cell-link">
    <span :class="{ hoverClass: queryParams.handleable }" @click="handleClick">{{ text }}</span>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
  computed: {
    queryParams() {
      // 字段
      return this.params.colDef?.cellRendererParams || {}
    },
    field() {
      return this.params.colDef?.field
    },
    text() {
      // 显示文字（可扩展）
      let _text = ''
      switch (this.field) {
        case 'costAnalysis':
          _text = this.params.data.costModelQuote ? this.$t('成本分析') : '' // 是否成本模型为是时显示
          break
        case 'costModelName':
          _text = this.params.data.costModelName ? this.params.data.costModelName : '' // 是否成本模型为是时显示
          break
        case 'costEstimation':
          _text = this.params.data.costModelQuote ? this.$t('成本测算') : '' // 是否成本模型为是时显示
          break
        case 'itemExtMap.drawingUrl':
          _text = this.$t('图纸查看') // 图纸查看
          break
        default:
          _text = ''
      }
      return _text
    }
  },
  methods: {
    // handler - 点击跳转
    handleClick() {
      // 如果是图纸 不走以下逻辑
      if (this.field === 'itemExtMap.drawingUrl') {
        this.showDrawing()
        return
      }
      if (!this.params.data?.costModelQuote) return
      if (!this.queryParams?.handleable) return

      if (this.queryParams.isAutoSave) {
        this.params.context.componentParent.handleSave(true)
        this.confirm()
        return
      }
      this.confirm()
    },
    // 图纸协同 - 跳转
    showDrawing() {
      if (!this.params.data?.itemCode) {
        this.$toast({
          content: this.$t('请先选择物料'),
          type: 'warning'
        })
        return
      }
      this.getUrl()
    },
    // 图纸协同 - 获取链接
    getUrl() {
      let _queryParam = { itemCode: this.params.data?.itemCode }
      this.$API.rfxRequireDetail.getDrawingUrl(_queryParam).then((res) => {
        if (res.code == 200) {
          window.open(res.data)
        }
      })
    },
    confirm() {
      if (this.field === 'costAnalysis' && this.queryParams?.type === 'pur') {
        // 采方成本分析
        this.$router.push({
          name: 'purchase-cost',
          query: {
            rfxId: this.queryParams?.rfxId,
            biddingItemId: this.params.data?.id,
            costModelId: this.params.data?.costModelId,
            refreshId: Date.now()
          }
        })
        return
      }
      if (this.field === 'costAnalysis') {
        // 供方成本分析
        // 成本分析
        this.$router.push({
          name: 'offer-purchase-cost',
          query: {
            abateFlag: this.queryParams?.abateFlag,
            rfxId: this.queryParams?.rfxId,
            rfxItemId: this.params.data?.rfxItemId,
            refreshId: Date.now()
          }
        })
        return
      }
      // costModelName、costEstimation  成本模型、成本测算
      if (!this.params.data?.id) {
        this.$toast({
          content: this.$t('请保存当前数据后操作！'),
          type: 'warning'
        })
      }
      this.$router.push({
        name: 'calculation-purchase-cost',
        query: {
          rfxId: this.$route.query.rfxId,
          rfxItemId: this.params.data?.id,
          type: this.field === 'costModelName' ? 'costModel' : 'calculation',
          refreshId: Date.now()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.cell-link span {
  color: #00469c;
  cursor: pointer;
}
.hoverClass:hover {
  text-decoration: underline;
}
</style>
