<template>
  <mt-dialog
    ref="columnSettingRef"
    css-class="column-selction"
    :target="getDialogTarget"
    :height="400"
    :width="400"
    :enable-resize="false"
    @close="cancel"
    @overlayClick="cancel"
    :footer-template="footerTemplate"
  >
    <div class="title-box">
      <span class="title-span">{{ $t('字段') }}</span>
      <span>{{ $t('已选字段') }}</span>
    </div>
    <div class="dialog-content">
      <div class="source-columns">
        <div class="search-container">
          <mt-checkbox
            class="checkbox-item"
            :checked="checkboxVal"
            :label="$t('全选')"
            @change="handleChange"
          ></mt-checkbox>
          <!-- <span class="placeholder-span">{{$t('搜索字段')}}</span>
          <mt-multi-select
            :width="158"
            cssClass="column-selction-search-multi-select"
            :dataSource="getMutiContainerSelectFields"
            :fields="{ text: 'headerText', value: 'field' }"
            v-model="selectFields"
            :showClearButton="false"
            :allowFiltering="true"
            :enableSelectionOrder="false"
            :closePopupOnSelect="false"
            @select="selectResourceMulti"
            @removed="removedResourceMulti"
            :filtering="filteringResource"
            :filterBarPlaceholder="$t('搜索字段')"
          ></mt-multi-select> -->
        </div>
        <div class="source-content">
          <!-- <mt-listbox
            ref="sourceListRef"
            :dataSource="resourceFields"
            :fields="{ text: 'headerText', value: 'field' }"
            :selectionSettings="selectionSettings"
            :itemTemplate="itemTemplate"
            v-model="selectFields"
            @change="changeSetting"
          /> -->
          <mt-listbox
            ref="sourceListRef"
            :data-source="resourceFields"
            :fields="{ text: 'headerText', value: 'field' }"
            :selection-settings="selectionSettings"
            v-model="selectFields"
            @change="changeSetting"
          />
        </div>
      </div>
      <div class="result-columns">
        <!-- <div class="search-container">
          <span class="placeholder-span">{{$t('搜索字段')}}</span>
          <mt-multi-select
            :width="158"
            cssClass="column-selction-search-multi-select"
            :dataSource="getMutiContainerVisibleFields"
            :fields="{ text: 'headerText', value: 'field' }"
            v-model="selectFields"
            :showClearButton="false"
            :allowFiltering="true"
            :enableSelectionOrder="false"
            :closePopupOnSelect="false"
            @select="selectResourceMulti"
            @removed="removedResourceMulti"
            :filtering="filteringResource"
            :filterBarPlaceholder="$t('搜索字段')"
          ></mt-multi-select>
        </div> -->
        <div class="result-content" id="rule-container">
          <div
            :class="['result-item']"
            v-for="(item, index) in visiblefields"
            :key="index"
            :title="item.headerText"
          >
            <div class="item-handler">
              <span>{{ index + 1 }}</span>
              <div class="handler"></div>
            </div>
            <div class="item-content">{{ item.headerText }}</div>
            <div class="item-remove" @click="removeItem(item)">+</div>
          </div>
        </div>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { utils } from '@mtech-common/utils'
import Sortable from 'sortablejs'
// import MtIcon from '@mtech-ui/icon'
// import MtInput from '@mtech-ui/input'
import MtDialog from '@mtech-ui/dialog'
// import MtMultiSelect from '@mtech-ui/multi-select'
import MtListbox from '@mtech-ui/listbox'
// import MtTooltip from '@mtech-ui/tooltip'
import Vue from 'vue'
export default {
  name: 'PluginColumnSelection',
  // components: { MtListbox, MtDialog, MtInput, MtIcon, MtMultiSelect },
  components: { MtListbox, MtDialog },
  props: {
    visibleColumns: {
      type: Array,
      default: () => []
    },
    resourceColumns: {
      type: Array,
      default: () => []
    },
    ignoreFields: {
      type: Array,
      default: () => {
        return []
      }
    },
    stickyFields: {
      type: Array,
      default: () => {
        return []
      }
    },
    selectionType: {
      type: String,
      default: 'search'
    },
    dialogTarget: {
      type: String,
      default: 'common-template-page'
    },
    // maxFields: {
    //   type: Number,
    //   default: 12
    // },
    // limitMaxFields: {
    //   type: Boolean,
    //   default: false
    // },
    dialogPosition: {
      type: Object,
      default: () => {
        return {
          X: 0,
          Y: 0
        }
      }
    }
  },
  data() {
    const _this = this
    return {
      stickFields: [],
      checkboxVal: false,
      selectionSettings: {
        mode: 'Single',
        showCheckbox: true
        // showSelectAll: true,
      },
      visiblefields: [],
      resourceFields: [],
      selectedResources: ['OrderID_2', 'OrderID_3'],
      footerTemplate: function () {
        return {
          template: Vue.component('dialog-footer', {
            template: ` <div class="dialog-footer">
                          <div class="footer-btns">
                            <span :class="['btn', {'disable-btn': btnIsDisabled}]" @click="dialogReset">{{$t('重置')}}</span>
                            <span :class="['btn', 'last-btn', {'disable-btn': btnIsDisabled}]" @click="dialogConfirm">{{$t('确定')}}</span>
                          </div>
                        </div>`,
            data() {
              return {
                data: {}
              }
            },
            computed: {
              btnIsDisabled() {
                return _this?.visiblefields?.length < 1
              }
              // isLimitMaxFields() {
              //   return _this?.limitMaxFields
              // },
              // getMaxFields() {
              //   return _this?.maxFields ?? 0
              // }
            },
            methods: {
              dialogReset() {
                _this.dialogReset()
              },
              dialogConfirm() {
                _this.confirm()
              }
            }
          })
        }
      }
      // itemTemplate: function () {
      //   return {
      //     template: Vue.component('select-item', {
      //       template: `<mt-tooltip :content="getItemContent">
      //                   <span :title="data.headerText">{{data.headerText}}</span>
      //                 </mt-tooltip>`,
      //       data() {
      //         return { data: {} }
      //       },
      //       components: { MtTooltip },
      //       computed: {
      //         getItemContent() {
      //           if (_this?.limitMaxFields) {
      //             return _this?.visiblefields?.length >= _this?.maxFields
      //               ? `${this.$t('最多选择')} ${_this?.maxFields ?? 0} ${this.$t('个')}`
      //               : this.data.headerText
      //           } else {
      //             return this.data.headerText
      //           }
      //         }
      //       }
      //     })
      //   }
      // }
    }
  },
  watch: {
    visibleColumns: {
      handler(n) {
        if (n) {
          let _visiblefields = n.filter((e) => !!e.allowReordering)
          const isQuickSearch = ['pluginQuickSearch', 'plugin-quick-search'].includes(
            this.$parent.$options._componentTag
          )
          if (isQuickSearch) {
            this.visiblefields = n
          } else {
            this.visiblefields = _visiblefields.filter((e) => !e?.sticky)
          }
          const _cols = []
          // _columns = []
          n.forEach((item) => {
            if (isQuickSearch || !!item.allowReordering) {
              _cols.push(item.field)
            }
            // let _stickyField = false
            // if (this.stickyFields.indexOf(item.field) > -1) {
            //   //在stickyFields中，设置了固定字段
            //   _stickyField = true
            //   item.sticky = true
            // }
            // if (typeof item?.sticky === 'boolean' && item?.sticky) {
            //   //在单列中中，设置了固定字段
            //   _stickyField = true
            // }
            // if (_stickyField) {
            //   _columns.push(item)
            // }
          })
          this.selectFields = _cols
          // this.stickyfields = _columns
        }
      },
      immediate: true,
      deep: true
    },
    resourceColumns: {
      handler(n) {
        if (n) {
          const _resource = utils.cloneDeep(n)
          const _columns = []
          this.stickFields.length = 0
          _resource.forEach((item) => {
            let _ignoreField = false
            if (this.ignoreFields.indexOf(item.field) > -1 && this.selectionType === 'search') {
              // 在ignoreFields中，设置了忽略此字段
              _ignoreField = true
            }
            if (
              typeof item?.ignore === 'boolean' &&
              item?.ignore &&
              this.selectionType === 'search'
            ) {
              // 在单列中中，设置了忽略此字段
              _ignoreField = true
            }
            if (
              typeof item?.sticky === 'boolean' &&
              item?.sticky &&
              this.selectionType !== 'search'
            ) {
              // 在单列中中，设置了固定此字段
              this.stickFields.push(item)
              _ignoreField = true
            }
            if (!_ignoreField) {
              _columns.push(item)
            }
          })
          this.resourceFields = _columns
          this.resetStickyFields()
        }
      },
      immediate: true,
      deep: true
    }
  },
  computed: {
    getButtons() {
      if (this.visiblefields.length) {
        return [
          {
            click: this.cancel,
            buttonModel: { content: this.$t('重置') }
          },
          {
            click: this.confirm,
            buttonModel: { isPrimary: 'true', content: this.$t('确定') }
          }
        ]
      } else {
        return [
          {
            click: this.cancel,
            buttonModel: { content: this.$t('重置') }
          },
          {
            buttonModel: { disabled: 'true', content: this.$t('确定') }
          }
        ]
      }
    },
    getDialogTarget() {
      return `.${this.dialogTarget}`
    },
    getMutiContainerVisibleFields() {
      return this.visiblefields.filter((e) => !e?.sticky && !!e.allowReordering)
    },
    getMutiContainerSelectFields() {
      return this.resourceFields.filter((e) => !e?.sticky)
    }
  },
  mounted() {
    this.$refs.columnSettingRef.ejsRef.show()
    this.createdListBox()
    const _this = this
    const _checkDom = setInterval(() => {
      if (document.getElementById('rule-container')) {
        clearInterval(_checkDom)
        Sortable.create(document.getElementById('rule-container'), {
          handle: '.item-handler',
          filter: '.sticky-item',
          onEnd: ({ oldIndex, newIndex }) => {
            const _rules = utils.cloneDeep(_this.visiblefields)
            const targetRow = _rules.splice(oldIndex, 1)[0]
            _rules.splice(newIndex, 0, targetRow)
            _this.visiblefields = []
            _this.$nextTick(function () {
              _this.visiblefields = _rules
            })
          }
        })
      } else {
      }
    }, 1000)
  },
  methods: {
    handleChange(val) {
      if (val.checked) {
        this.$refs.sourceListRef.ejsRef.selectAll()
      } else {
        this.dialogReset()
      }
    },
    createdListBox() {
      const _resource = utils.cloneDeep(this.resourceColumns)
      const stickColumns = []
      _resource.forEach((item) => {
        let _stickyField = false
        if (this.stickyFields.indexOf(item.field) > -1) {
          // 在stickyFields中，设置了固定字段
          _stickyField = true
          item.sticky = true
        }
        if (typeof item?.sticky === 'boolean' && item?.sticky) {
          // 在单列中中，设置了固定字段
          _stickyField = true
        }
        if (_stickyField) {
          stickColumns.push(item.headerText)
        }
      })
      // this.$refs.sourceListRef.ejsRef.enableItems(stickColumns, false)
    },
    changeSetting(e) {
      this.selectFields = e.value
      const filterAttr = (arr, attr) => {
        const r = arr.reduce((prev, element) => {
          if (!prev.find((el) => el[attr] === element[attr])) {
            prev.push(element)
          }
          return prev
        }, [])
        return r
      }
      // const _stickyfields = utils.cloneDeep(this.stickyfields)
      // const _merge = filterAttr(_stickyfields.concat(e.items), 'field')
      const _merge = filterAttr(e.items, 'field')
      // if (this?.limitMaxFields) {
      //   if (_merge.length > this.maxFields) {
      //     const _resource = _merge.slice(0, this.maxFields)
      //     const _cols = []
      //     _resource.forEach(e => {
      //       _cols.push(e.field)
      //     })
      //     this.selectFields = _cols
      //     this.visiblefields = _resource
      //   } else {
      //     this.visiblefields = _merge
      //   }
      // } else {
      this.visiblefields = _merge
      // }
    },
    // 搜索框 勾选
    selectResourceMulti(e) {
      const { itemData, name } = e
      if (name === 'select') {
        const _findIndex = this.visiblefields.findIndex((e) => e.field === itemData.field)
        if (_findIndex < 0) {
          this.visiblefields.push(itemData)
          this.selectFields.push(itemData.field)
          this.$refs.sourceListRef.ejsRef.selectItems([itemData.headerText], true)
        }
      }
    },
    // 搜索框 移除
    removedResourceMulti(e) {
      const { itemData, name } = e
      if (name === 'removed') {
        this.removeItem(itemData)
      }
    },
    filteringResource(e) {
      if (typeof e.text === 'string' && e.text) {
        e.updateData(this.visiblefields.filter((f) => f?.headerText.indexOf(e.text) > -1))
      } else {
        e.updateData(this.visiblefields)
      }
    },
    removeItem(item) {
      let _stickyField = false
      if (this.stickyFields.indexOf(item.field) > -1) {
        // 在stickyFields中，设置了固定字段
        _stickyField = true
        item.sticky = true
      }
      if (typeof item?.sticky === 'boolean' && item?.sticky) {
        // 在单列中中，设置了固定字段
        _stickyField = true
      }
      if (_stickyField) {
        // 如果是固定字段，不执行移除
        return
      }
      const _visibleList = utils.cloneDeep(this.visiblefields)
      const _findIndex = _visibleList.findIndex((e) => e.field === item.field)
      const _removeItem = _visibleList.splice(_findIndex, 1)
      this.visiblefields = _visibleList
      this.$nextTick(() => {
        this.$refs.sourceListRef.ejsRef.selectItems([_removeItem[0].headerText], false)
      })
    },
    // resetColumnSetting() {
    //   let _resource = utils.cloneDeep(this.resourceColumns)
    //   this.visiblefields = _resource
    //   let _cols = []
    //   _resource.forEach(e => {
    //     _cols.push(e.field)
    //   })
    //   this.selectFields = _cols
    // },
    cancel() {
      // this.$refs.columnSettingRef.ejsRef.hide()
      this.$emit('cancelColumnsDialog')
    },
    resetStickyFields() {
      const _resource = utils.cloneDeep(this.resourceFields)
      const _columns = []
      _resource.forEach((item) => {
        let _stickyField = false
        if (this.stickyFields.indexOf(item.field) > -1) {
          // 在stickyFields中，设置了固定字段
          _stickyField = true
          item.sticky = true
        }
        if (typeof item?.sticky === 'boolean' && item?.sticky) {
          // 在单列中中，设置了固定字段
          _stickyField = true
        }
        if (_stickyField) {
          _columns.push(item)
        }
      })
      this.stickyfields = _columns
      const _visibles = utils.cloneDeep(this.visibleColumns)
      // if (_visibles.length < 1) {
      //   this.visiblefields = _columns
      // }
    },
    dialogReset() {
      // const _resource = utils.cloneDeep(this.stickyfields)
      // this.visiblefields = _resource
      this.visiblefields = []
      const _cols = []
      // _resource.forEach(e => {
      //   _cols.push(e.field)
      // })
      this.selectFields = _cols
      // this.selectFields.length = 0
    },
    confirm() {
      this.$refs.columnSettingRef.ejsRef.hide()
      const _visible = this.stickFields.map((e) => e.field)
      const _hiddenFields = []
      const _hiddenColumns = []
      this.visiblefields.forEach((e) => {
        _visible.push(e.field)
      })
      this.resourceFields.forEach((e) => {
        if (_visible.indexOf(e.field) < 0) {
          _hiddenFields.push(e.field)
          _hiddenColumns.push(e)
        }
      })
      if (_visible.length) {
        const _emitValue = {
          visibleColumns: this.stickFields.concat(this.visiblefields),
          visibleFields: _visible,
          hiddenColumns: _hiddenColumns,
          hiddenFields: _hiddenFields,
          resourceFields: this.resourceFields
        }
        this.$emit('confirmColumnsDialog', { type: this.selectionType, value: _emitValue })
      }
    }
  }
}
</script>

<style lang="scss">
// @import '../themes/dark.scss';
.column-selction-search-multi-select {
  border: none !important;
  &:before,
  &:after {
    display: none !important;
  }
  .e-delim-view {
    display: none !important;
  }
}
</style>
<style lang="scss" scoped>
.title-box {
  height: 35px;
  line-height: 35px;
  .title-span {
    width: 202px;
    display: inline-block;
    float: left;
    text-indent: 2px;
  }
}
.dialog-content {
  width: 100%;
  height: calc(100% - 35px);
  display: flex;
  justify-content: space-between;
  padding: 0px 2px 2px 2px;

  .source-columns,
  .result-columns {
    display: flex;
    flex-direction: column;
    width: 160px;
    flex-shrink: 0;
    border: 1px solid var(--common-pcs-border-color);
    border-radius: 2px;
    overflow-y: auto;
    overflow-x: hidden;
    .search-container {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      // padding: 0 10px;
      padding: 0;
      position: relative;
      color: var(--common-pcs-search-container-color);
      height: 40px;
      border-bottom: 1px solid var(--common-pcs-border-color);

      .checkbox-item {
        margin-left: 20px;
      }
      .placeholder-span {
        position: absolute;
        padding-left: 20px;
      }
      ::v-deep.search-columns {
        margin: 0;
        border: none !important;
        padding: 0 0 0 10px;

        &::before,
        &::after {
          background: transparent !important;
        }
      }
    }
    .source-content,
    .result-content {
      display: flex;
      flex-direction: column;
      flex: 1;
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
  .source-columns {
    ::v-deep .e-listbox-wrapper {
      border: none;
    }
    ::v-deep .e-list-item {
      height: 30px;
      line-height: 30px;
      overflow: hidden;
      white-space: nowrap;
      // text-overflow: ellipsis;
      padding: 0 20px;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 400;
      color: rgba(41, 41, 41, 1);
      &.e-disabled {
        cursor: not-allowed;
        .e-frame {
          background-color: var(--common-pcs-disable-color);
          border-color: transparent;
          color: var(--common-pcs-list-item-disabled-color);
          position: relative;
          &::before {
            content: '';
            width: 9px;
            height: 5px;
            border: 2px solid var(--common-pcs-list-item-disabled-color);
            transform: rotate(-45deg);
            background: transparent;
            border-top: none;
            border-right: none;
            position: absolute;
            top: 2px;
            left: 1px;
          }
        }
        span {
          color: var(--common-pcs-disable-color);
        }
      }

      .mt-tooptip {
        display: inline-block;
      }
    }
  }
  .result-columns {
    .result-content {
      padding: 0 6px;
      .result-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 30px;
        line-height: 30px;
        .item-handler {
          cursor: move;
          display: flex;
          align-items: center;
          color: var(--common-pcs-item-handler-color);
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(99, 134, 193, 1);
          .handler {
            margin-left: 7px;
            width: 13px;
            height: 10px;
            border-top: 1px solid var(--common-pcs-item-handler-color);
            border-bottom: 1px solid var(--common-pcs-item-handler-color);
            box-sizing: border-box;
            position: relative;
            &::after {
              content: '';
              border-top: 1px solid var(--common-pcs-item-handler-color);
              width: 13px;
              position: absolute;
              top: 4px;
            }
          }
        }
        .item-content {
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: normal;
          color: var(--common-pcs-column-title-color);
          flex: 1;
          padding: 0 10px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          padding-right: 10px;
        }
        .item-remove {
          cursor: pointer;
          color: var(--common-pcs-item-remove-color);
          font-size: 20px;
          font-weight: 100;
          transform: rotate(45deg);
          &:hover {
            color: var(--common-pcs-item-remove-hover-color);
          }
        }

        &.sticky-item {
          cursor: not-allowed;
          color: var(--common-pcs-sticky-item-color);
          .item-handler,
          .item-content,
          .item-remove {
            cursor: not-allowed;
            color: var(--common-pcs-sticky-item-color);
            &:hover {
              color: var(--common-pcs-sticky-item-color);
            }
          }
        }
      }
    }
  }

  //修改谷歌内核浏览器滚动条样式
  ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }
  ::-webkit-scrollbar-track {
    border-radius: 2px;
    background-color: var(--scroll-bar-track-color);
  }

  ::-webkit-scrollbar-thumb {
    background-color: var(--scroll-bar-thumb-color);
    border-radius: 2px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: var(--scroll-bar-thumb-hover-color);
  }

  ::-webkit-scrollbar-thumb:active {
    background-color: var(--scroll-bar-thumb-active-color);
  }
}
</style>
