<template>
  <mt-loading :is-show="isShow" css-class="template"
    ><div class="loadingImg">
      <img src="../assets/loading.png" alt="" />
    </div>
  </mt-loading>
</template>

<script>
import MtLoading from '@mtech-ui/loading'

export default {
  props: {
    isShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false
    }
  },
  components: {
    MtLoading
  },
  methods: {}
}
</script>
<style lang="scss" scoped>
// @import '../themes/dark.scss';
.loadingImg {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;

  img {
    height: 150px;
  }
}
</style>
