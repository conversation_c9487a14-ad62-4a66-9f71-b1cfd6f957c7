const isServer = typeof window === 'undefined'

/* istanbul ignore next */
const resizeHandler = function (entries) {
  for (let entry of entries) {
    const listeners = entry.target.__resizeListeners__ || []
    if (listeners.length) {
      listeners.forEach((fn) => {
        fn()
      })
    }
  }
}

/* istanbul ignore next */
const addResizeListener = function (element, fn) {
  if (isServer) return
  if (!element.__resizeListeners__) {
    element.__resizeListeners__ = []
    element.__ro__ = new ResizeObserver(resizeHandler)
    element.__ro__.observe(element)
  }
  element.__resizeListeners__.push(fn)
}

/* istanbul ignore next */
const _removeResizeListener = function (element, fn) {
  if (!element || !element.__resizeListeners__) return
  element.__resizeListeners__.splice(element.__resizeListeners__.indexOf(fn), 1)
  if (!element.__resizeListeners__.length) {
    element.__ro__.disconnect()
  }
}

/**
 * How to use
 * <ag-grid-vue v-adaptive="{bottomOffset: 30, gridHeightCfg }">...</ag-grid-vue>
 * bottomOffset: 60(default)   // The height of the table from the bottom of the page.
 */

const doResize = (el, gridHeightCfg, bottomOffset) => {
  // js计算的尺寸都要进行分辨率detectZoom转换
  if (!el) return
  const radio = document.body.style.zoom || 1
  const dialogDom = el.closest('.e-dialog')
  if (dialogDom) {
    bottomOffset = 150
    const containerHeight = dialogDom.offsetHeight
    const innerHeight = parseInt(containerHeight / radio)
    const clientRectTop = el.getBoundingClientRect().top - dialogDom.getBoundingClientRect().top
    let height = innerHeight - clientRectTop - bottomOffset
    height = height < 100 ? 100 : height
    gridHeightCfg.height = height
  } else {
    const innerHeight = parseInt(window.innerHeight / radio)
    let height = innerHeight - el.getBoundingClientRect().top - bottomOffset
    height = height < 200 ? 200 : height
    gridHeightCfg.height = height
  }
}

export const adaptiveInit = (el, gridHeightCfg, bottomOffset) => {
  el.resizeListener = () => {
    doResize(el, gridHeightCfg, bottomOffset)
  }
  // parameter 1 is must be "Element" type
  setTimeout(() => {
    const templatePageDom = el.closest('.common-template-page')
    const templatePageToggleDom = templatePageDom.querySelector('.toggle-search-bar')
    const collapseSearchDom = document.querySelector('.collapse-search-area')
    const toggleDom = templatePageToggleDom || collapseSearchDom
    if (toggleDom) {
      addResizeListener(toggleDom, el.resizeListener)
    }
  }, 120)

  const dialogDom = el.closest('.e-dialog')
  if (dialogDom) addResizeListener(dialogDom, el.resizeListener)
  addResizeListener(window.document.body, el.resizeListener)
}
export const adaptive = (el, gridHeightCfg, bottomOffset) => {
  doResize(el, gridHeightCfg, bottomOffset)
}
export const removeResizeListener = (el) => {
  _removeResizeListener(window.document.body, el.resizeListener)
  const dialogDom = el.closest('.e-dialog')
  if (dialogDom) _removeResizeListener(dialogDom, el.resizeListener)
}
