<template>
  <div
    :class="[
      'common-template-page',
      'mt-flex-direction-column',
      dialogTargetClass,
      { 'template-hidden-tabs': paddingTop }
    ]"
  >
    <common-loading :is-show="useLoading && loadingStatus"></common-loading>
    <tabs
      ref="tabsRef"
      v-if="showTabs"
      v-bind="tabsConfig"
      v-on="$listeners"
      v-permission="permissionObj"
      :tab-id="getRandomTabId"
      :selected-item="currentTabIndex"
      @handleSelectTab="componentSelectTab"
    />
    <!-- tab下面的公共过滤 -->
    <slot ref="slotFilterRef" name="slot-filter"></slot>
    <div v-if="activeContent" class="repeat-template mt-flex-direction-column">
      <div v-permission="[activeContent.permissionCode || 'ignore-element']" class="template-wrap">
        <template>
          <!-- 单个tab下的过滤 -->
          <slot :ref="'slotFilterRef-' + orgTabIndex" :name="'slot-filter-' + orgTabIndex"></slot>
          <query
            ref="queryRef"
            v-if="activeContent.useQueryBuilder"
            v-show="activeContent.showQueryBuilder"
            v-bind="renderGridConfig(activeContent)"
            v-on="$listeners"
            @handleQuerySearch="handleQuerySearch($event, currentTabIndex)"
            @handleQueryReset="handleQueryReset($event, currentTabIndex)"
          />
          <pluginQuickSearch
            ref="searchRef"
            v-if="activeContent.useQuickSearch || activeContent.isUseCustomSearch"
            v-show="activeContent.showQuickSearch || activeContent.isUseCustomSearch"
            :column-data="searchColumnData"
            :allow-filtering-fields="getAllowFilteringFields(activeContent)"
            :max-query-value-length="maxQueryValueLength"
            :search-reset-with-a-p-i="activeContent.searchResetWithAPI"
            :save-selected-record-with-search="activeContent.saveSelectedRecordWithSearch"
            :resource-column-data="renderGridColumnData(activeContent)"
            :grid-id="activeContent.gridId"
            :default-search-templates="activeContent.defaultSearchTemplates"
            :is-use-custom-search="activeContent.isUseCustomSearch"
            :search-form-model="activeContent.searchFormModel"
            :is-custom-search-handle="activeContent.isCustomSearchHandle"
            :custom-match-rules="activeContent.customMatchRules"
            :custom-search-expand-min-rows="activeContent.customSearchExpandMinRows"
            :tabindex="currentTabIndex"
            v-on="$listeners"
            @handleQuickSearch="handleQuickSearch($event, currentTabIndex)"
            @handleQuickReset="handleQuickReset($event, currentTabIndex)"
            @handleSaveMemory="handleSaveMemory($event, currentTabIndex)"
          >
            <template v-slot:custom-search-form="{ searchFormModel }">
              <slot name="quick-search-form" v-bind="{ searchFormModel }"></slot>
            </template>
          </pluginQuickSearch>
          <toolbar
            ref="toolbarRef"
            v-if="activeContent.useToolBar"
            :toolbar-config="activeContent.toolbarConfig"
            :show-query-builder="activeContent.showQueryBuilder"
            :show-quick-search="activeContent.showQuickSearch"
            :is-use-custom-search="activeContent.isUseCustomSearch"
            :is-custom-search-rules="activeContent.isCustomSearchRules"
            :use-tool-template="activeContent.useToolTemplate"
            :button-quantity="activeContent.buttonQuantity"
            :use-combination-selection="false"
            :use-right-tool-bar-rule="activeContent.useRightToolBarRule"
            v-on="$listeners"
            @componentClickToolBar="componentClickToolBar($event, currentTabIndex)"
            @componentClickToolTips="componentClickToolTips($event, currentTabIndex)"
            @componentClickToolMarker="componentClickToolMarker($event, currentTabIndex)"
          />
          <data-grid
            :ref="'pageGridRef-' + currentTabIndex"
            :tabindex="currentTabIndex"
            v-if="activeContent.useGrid"
            v-bind="activeContent.gridConfig"
            v-on="$listeners"
            @handleToggleLoading="toggleLoading"
            @handleQuerySearch="handleQuerySearch($event, currentTabIndex)"
            @currentChange="componentChangePageIndex($event, currentTabIndex)"
            @sizeChange="componentChangePageSize($event, currentTabIndex)"
            @componentClickCellTool="componentClickCellTool($event, currentTabIndex)"
            @componentClickCellTitle="componentClickCellTitle($event, currentTabIndex)"
            @handleSaveMemory="handleSaveMemory($event, currentTabIndex)"
          />
          <tree-grid
            :ref="'pageGridRef-' + currentTabIndex"
            v-if="activeContent.useTreeGrid"
            v-bind="activeContent.treeGridConfig"
            v-on="$listeners"
            @handleToggleLoading="toggleLoading"
            @handleQuerySearch="handleQuerySearch($event, currentTabIndex)"
            @currentChange="componentChangePageIndex($event, currentTabIndex)"
            @sizeChange="componentChangePageSize($event, currentTabIndex)"
            @componentClickCellTool="componentClickCellTool($event, currentTabIndex)"
            @componentClickCellTitle="componentClickCellTitle($event, currentTabIndex)"
            @handleSaveMemory="handleSaveMemory($event, currentTabIndex)"
          />
          <slot :ref="'slotRef-' + orgTabIndex" :name="'slot-' + orgTabIndex"></slot>
        </template>
      </div>
    </div>
    <plugin-column-setting
      v-if="showColumnSetting"
      :visible-columns="visibleColumns"
      :resource-columns="resourceColumns"
      :dialog-target="dialogTargetClass"
      @confirmColumnsDialog="handleConfirmColumnSetting"
      @cancelColumnsDialog="handleCancelColumnsSetting"
    />
    <plugin-column-selection
      v-if="showColumnSelection"
      :visible-columns="visibleColumns"
      :resource-columns="resourceColumns"
      :dialog-position="dialogPosition"
      :limit-max-fields="limitMaxFields"
      :selection-type="selectionType"
      :dialog-target="dialogTargetClass"
      @confirmColumnsDialog="handleConfirmColumnSelection"
      @cancelColumnsDialog="handleCancelColumnsSelection"
    />
    <!-- <plugin-combination-selection
      v-if="showCombinationSelection"
      :column-data="resourceColumns"
      :allow-filtering-fields="allowFilteringFields"
      :ignore-fields="ignoreFields"
      :dialog-position="dialogPosition"
      :max-query-value-length="maxQueryValueLength"
      :dialog-target="dialogTargetClass"
      @handleQuickSearch="handleQuickSearch($event)"
      @handleQuickReset="handleQuickReset($event)"
      @confirmColumnsDialog="handleConfirmCombinationSelection"
      @cancelColumnsDialog="handleCancelCombinationSelection"
    /> -->
  </div>
</template>

<script>
import { cloneDeep, isEqual } from 'lodash'
import { utils } from '@mtech-common/utils'
import MtTabs from '@mtech-ui/tabs'
import PluginToolBar from './pluginToolbar/src/main.vue'
import pluginDataGrid from './pluginDataGrid/src/main.vue'
import pluginTreeGrid from './pluginTreeGrid/src/main.vue'
import pluginQueryBuilder from './pluginQueryBuilder/src/main.vue'
import pluginQuickSearch from './pluginQuickSearch/src/main.vue'
import pluginColumnSetting from './pluginColumnSetting/src/main.vue'
import pluginColumnSelection from './pluginColumnSelection/src/main.vue'
// import pluginCombinationSelection from './pluginCombinationSelection/src/main.vue'
import commonLoading from './common-loading/src/main.vue'
// import './pluginToolbar/build/esm/bundle.css'
// import '@mtech/plugin-data-grid/build/esm/bundle.css'
// import '@mtech/plugin-tree-grid/build/esm/bundle.css'
// import '@mtech/common-loading/build/esm/bundle.css'
// import '@mtech/plugin-query-builder/build/esm/bundle.css'
// import '@mtech/plugin-quick-search/build/esm/bundle.css'
// import '@mtech/plugin-column-setting/build/esm/bundle.css'
// import '@mtech/plugin-column-selection/build/esm/bundle.css'
// import '@mtech/plugin-combination-selection/build/esm/bundle.css'
import { API } from '@mtech-common/http'

const saveUserMemoryUrl = '/lowcodeWeb/tenant/user-memory/save'
const getUserMemoryUrl = '/lowcodeWeb/tenant/user-memory/get'
const Query = {
  inheritAttrs: false,
  components: {
    pluginQueryBuilder
  },
  name: 'Query',
  render(h) {
    return h('plugin-query-builder', {
      props: {
        ...this.$props
      },
      attrs: {
        ...this.$attrs
      },
      on: {
        ...this.$listeners
      }
    })
  }
}
const Tabs = {
  inheritAttrs: false,
  components: {
    MtTabs
  },
  name: 'Tabs',
  render(h) {
    return h('mt-tabs', {
      props: {
        ...this.$props
      },
      attrs: {
        ...this.$attrs
      },
      on: {
        ...this.$listeners
      }
    })
  }
}
const Toolbar = {
  inheritAttrs: false,
  components: {
    PluginToolBar
  },
  name: 'Toolbar',
  props: {
    toolbarConfig: {
      type: Array,
      default() {
        return []
      }
    },
    showQueryBuilder: {
      type: Boolean,
      default: false
    },
    showQuickSearch: {
      type: Boolean,
      default: false
    },
    useToolTemplate: {
      type: Boolean,
      default: true
    },
    buttonQuantity: {
      type: Number,
      default: 5
    },
    useCombinationSelection: {
      type: Boolean,
      default: true
    },
    useRightToolBarRule: {
      type: Boolean,
      default: false
    },
    isUseCustomSearch: {
      type: Boolean,
      default: false
    },
    isCustomSearchRules: {
      type: Boolean,
      default: false
    },
    // 自定义查询条件默认展开的最小行数，0-展开所有
    customSearchExpandMinRows: {
      type: Number,
      default: 0
    }
  },
  render(h) {
    return h('plugin-tool-bar', {
      props: {
        ...this.$props
      },
      attrs: {
        toolbarConfig: this.toolbarConfig,
        showQueryBuilder: this.showQueryBuilder,
        showQuickSearch: this.showQuickSearch,
        useToolTemplate: this.useToolTemplate,
        buttonQuantity: this.buttonQuantity,
        useCombinationSelection: this.useCombinationSelection,
        useRightToolBarRule: this.useRightToolBarRule
      },
      on: {
        ...this.$listeners
      },
      ref: 'toolbar'
    })
  }
}
const DataGrid = {
  inheritAttrs: false,
  components: {
    pluginDataGrid
  },
  name: 'DataGrid',
  data() {
    return {}
  },
  render(h) {
    return h('plugin-data-grid', {
      props: {
        ...this.$props
      },
      attrs: {
        ...this.$attrs
      },
      on: {
        ...this.$listeners
      },
      ref: 'pluginGridRef'
    })
  }
}
const TreeGrid = {
  components: {
    pluginTreeGrid
  },
  name: 'TreeGrid',
  data() {
    return {}
  },
  render(h) {
    return h('plugin-tree-grid', {
      props: {
        ...this.$props
      },
      attrs: {
        ...this.$attrs
      },
      on: {
        ...this.$listeners
      },
      ref: 'pluginGridRef'
    })
  }
}
export default {
  inheritAttrs: false,
  name: 'CommonTemplatePage',
  props: {
    gridId: {
      type: String,
      default: null
    },
    activatedRefresh: {
      type: Boolean,
      default: true
    },
    defaultSearchTemplates: {
      type: Array,
      default() {
        return []
      }
    },
    useToolTemplate: {
      type: Boolean,
      default: true
    },
    hiddenTabs: {
      type: Boolean,
      default: false
    },
    paddingTop: {
      type: Boolean,
      default: false
    },
    useLoading: {
      type: Boolean,
      default: true
    },
    templateConfig: {
      type: Array,
      required: true,
      default() {
        return []
      }
    },
    tabConfig: {
      type: Object,
      default() {
        return {
          eTab: false,
          titleCanChange: true
        }
      }
    },
    toolbarConfig: {
      type: [Object, Array],
      default() {
        return ['resetSearch', 'quickSearch', 'combinationSearch', 'Refresh', 'Setting']
        // return ['quickSearch', 'combinationSearch', 'Refresh', 'Setting']
      }
    },
    gridConfig: {
      type: Object,
      default() {
        return {}
      }
    },
    treeGridConfig: {
      type: Object,
      default() {
        return {}
      }
    },
    currentTab: {
      type: Number,
      default: 0
    },
    useColumnChooser: {
      type: Boolean,
      default: false
    },
    maxQueryValueLength: {
      type: Number,
      default: 50
    },
    freezeOperationColumn: {
      type: Boolean,
      default: false
    },
    buttonQuantity: {
      type: Number,
      default: 5
    },
    useCombinationSelection: {
      type: Boolean,
      default: true
    },
    useRightToolBarRule: {
      type: Boolean,
      default: false
    },
    permissionObj: {
      type: Object,
      default() {
        return {}
      }
    },
    searchResetWithAPI: {
      type: Boolean,
      default: true
    },
    saveSelectedRecordWithSearch: {
      type: Boolean,
      default: true
    }
  },
  components: {
    Query,
    pluginQuickSearch,
    // Search,
    commonLoading,
    Tabs,
    Toolbar,
    DataGrid,
    TreeGrid,
    pluginColumnSetting,
    pluginColumnSelection
    // pluginCombinationSelection
  },
  data() {
    return {
      useTabs: true,
      loadingStatus: false,
      currentTabIndex: this.currentTab,
      contentConfig: [],
      tabsConfig: {},
      visibleColumns: [],
      resourceColumns: [],
      showColumnSetting: false, // 列设置弹框--old
      showColumnSelection: false, // 列设置弹框--new
      showCombinationSelection: false, // 高级查询
      ignoreFields: [],
      allowFilteringFields: [],
      limitMaxFields: false,
      selectionType: 'search',
      searchColumnData: [],
      dialogPosition: {
        X: 0,
        Y: 0
      },
      dialogTargetClass: `dialog-target-${parseInt(Math.random(10000) * 10000)}`
    }
  },
  mounted() {
    this.getTabsIndex()
  },
  activated() {
    if (this.contentConfig[this.currentTabIndex]?.activatedRefresh) {
      this.refreshCurrentGridData()
    }
  },
  watch: {
    cloneTemplateConfig: {
      async handler(newVal, oldVal) {
        if (newVal && !isEqual(newVal, oldVal)) {
          const currentTab = newVal[this.currentTabIndex]
          const oldCurrentTab = oldVal ? oldVal[this.currentTabIndex] : undefined
          // 是否存在当前tab的grid的dataSource或者asyncConfig配置
          const _exitGridCfg = currentTab?.grid?.dataSource || currentTab?.grid?.asyncConfig
          // 是否存在当前tab的treeGrid的dataSource或者asyncConfig配置
          const _exitTreeGridCfg =
            currentTab?.treeGrid?.dataSource || currentTab?.treeGrid?.asyncConfig
          const exitDataOrAsyncCfg = _exitGridCfg || _exitTreeGridCfg
          // 是否存在当前tab的grid或者treeGrid的columnData配置
          const exitGridCols =
            currentTab?.grid?.columnData?.length > 0 || currentTab?.treeGrid?.columnData?.length > 0
          // 新旧值的gridId是否一致
          const isGridIdDiff = currentTab?.gridId !== oldCurrentTab?.gridId
          // 首次转入值，适配那种只用template-page组件的tabs功能的场景
          const isImmediateRender = !oldVal && !!newVal
          const shouldSerializeAttrs =
            isImmediateRender || ((exitDataOrAsyncCfg || isGridIdDiff) && exitGridCols)
          if (shouldSerializeAttrs) {
            await this.serializeComponentAttrs(currentTab?.gridId)
            const gridMemory = JSON.parse(sessionStorage.getItem(currentTab?.gridId) || '{}')
            if (gridMemory && gridMemory.visibleCols) {
              const visibleFields = gridMemory.visibleCols.map((col) => col.field)
              this.initShowColumns(visibleFields)
            }
            if (gridMemory?.savedSearchItem) {
              this.searchColumnData = gridMemory.savedSearchItem
            } else if (currentTab?.grid?.defaultSearchItem) {
              this.searchColumnData = currentTab.grid.defaultSearchItem
            } else {
              this.searchColumnData = []
            }
            if (this.$refs.searchRef && Array.isArray(this.$refs.searchRef.$children)) {
              const searchTemplate = gridMemory?.searchTemplates || []
              this.$refs.searchRef.$children[0].searchTemplates = searchTemplate
            }
            const hasSearchColumnData =
              this.searchColumnData?.length > 0 ||
              !!gridMemory?.defaultTemplate ||
              this.contentConfig[this.currentTabIndex]?.defaultSearchTemplates?.length > 0
            if (hasSearchColumnData) {
              this.$set(this.contentConfig[this.currentTabIndex], 'showQuickSearch', true)
            }
          } else {
            // this.serializeComponentAttrs('')
            // 只用template-page组件来做页签切换功能的场景
            if (newVal.every((item) => !item.grid && item.title)) {
              const _tabDataSource = this.templateConfig.map((tabItem) =>
                this.serializeTabParams(tabItem)
              )
              this.tabsConfig = this.serializeTabConfig(_tabDataSource)
            }
          }
        }
      },
      immediate: true,
      deep: true
    },
    currentTab: {
      handler(n) {
        this.currentTabIndex = n
      },
      immediate: true
    }
  },
  computed: {
    activeContent() {
      return this.contentConfig[this.currentTabIndex]
    },
    // currentTabIndex与原始的tab index 不一定一致, 开发者在外面配置slot时是写死的原始tab index
    orgTabIndex() {
      return this.contentConfig.findIndex((item) => item.id === this.activeContent.id)
    },
    cloneTemplateConfig() {
      return utils.cloneDeep(this.templateConfig)
    },
    renderGridConfig() {
      return (item) => {
        return item?.gridConfig
      }
    },
    getAllowFilteringFields() {
      return (item) => {
        return (
          item?.gridConfig?.allowFilteringFields ?? item?.treeGridConfig?.allowFilteringFields ?? []
        )
      }
    },
    renderGridColumnData() {
      return (item) => {
        const _columns = item?.gridConfig?.columnData ?? item?.treeGridConfig?.columnData ?? []
        if (Array.isArray(_columns) && _columns.length) {
          const _resource = []
          for (let i = 0; i < _columns.length; i++) {
            if (typeof _columns[i].visible === 'boolean' && !_columns[i].visible) {
              // 列配置时，设置了visible=false
              continue
            }
            if (_columns[i].field && _columns[i].headerText) {
              _resource.push(_columns[i])
            }
          }
          return _resource
        } else {
          return []
        }
      }
    },
    showTabs() {
      if (this.hiddenTabs) {
        return false
      } else {
        return this.useTabs
      }
    },
    getRandomTabId() {
      return `tab-id-${parseInt(Math.random(100000) * 10000)}`
    }
  },
  methods: {
    initShowColumns(visibleFields) {
      this.$nextTick(() => {
        const index = this.currentTabIndex
        const _config = utils.cloneDeep(this.contentConfig[index])
        const _columns = _config?.useGrid
          ? utils.cloneDeep(_config?.gridConfig?.columnData)
          : utils.cloneDeep(_config?.treeGridConfig?.columnData)
        const _visible = new Array(visibleFields.length)
        const _hidden = []
        const _fixed = []
        _columns.forEach((c, i) => {
          if (visibleFields.includes(c.field)) {
            c.visible = true
            _visible[visibleFields.indexOf(c.field)] = c
          } else if (i === 0 && ['checkbox', 'checkBox'].includes(c.type)) {
            // 第一列为 checkbox  单独处理
            c.visible = true
            _fixed.push(c)
          } else {
            c.visible = false
            _hidden.push(c)
          }
        })
        let _usefulCols = _visible.filter((e) => Object.hasOwnProperty.call(e, 'visible'))
        if (_config.useGrid) {
          this.$set(
            this.contentConfig[index].gridConfig,
            'columnData',
            _fixed.concat(_usefulCols, _hidden)
          )
        } else {
          this.$set(
            this.contentConfig[index].treeGridConfig,
            'columnData',
            _fixed.concat(_usefulCols, _hidden)
          )
        }
      })
    },
    getTabsIndex() {
      const a = Array.from(document.querySelectorAll('li'))
      for (let i = 0; i < a.length; i++) {
        if (a[i].getAttribute('permissioncode')) {
          for (let j = 0; j < this.contentConfig.length; j++) {
            if (a[i].getAttribute('permissioncode') == this.contentConfig[j].permissionCode) {
              this.currentTabIndex = j
              return
            }
          }
        }
      }
    },
    // 处理templateConfig产出contentConfig
    async serializeComponentAttrs(_gridId) {
      let _gridMemory = {}
      if (_gridId) {
        const res = await API.get(getUserMemoryUrl, { gridId: _gridId })
        if (res.code === 200) _gridMemory = res.data?.gridMemory
        sessionStorage.setItem(_gridId, JSON.stringify(_gridMemory || {}))
      }
      const _tabDataSource = this.templateConfig.map((tabItem) => this.serializeTabParams(tabItem))
      const _compConfig = utils.cloneDeep(this.templateConfig)
      const _contentConfig = _compConfig.map((_comp) => {
        const {
          isUseCustomSearch = false, // 配置中使用自定义快捷查询
          isCustomSearchRules = false, // 配置中使用自定义快捷查询入参规则
          searchFormModel = {}, // 自定义快捷查询的form model
          isCustomSearchHandle = false, // 配置中使用了自定义点击“查询”按钮事件
          customMatchRules = {}, // 自定义查询匹配规则
          useToolTemplate = false, // 默认为false
          customSearchExpandMinRows = 0 // 自定义快捷查询查默认展开的所有行
        } = _comp

        const hasFilterToolBar = this.serializeToolBarConfig(_comp)?.some((f) =>
          f.includes('Filter')
        )
        const hasQuickSearchToolBar = this.serializeToolBarConfig(_comp)?.some((f) =>
          f.includes('quickSearch')
        )
        const useGridColumnChooser = Object(_comp)?.useColumnChooser ?? this.useColumnChooser

        return {
          id: parseInt(Math.random(100000) * 10000),
          showQueryBuilder: false,
          showQuickSearch: false,
          useQueryBuilder: hasFilterToolBar && (!!_comp?.grid || !!_comp?.treeGrid),
          useQuickSearch: hasQuickSearchToolBar && (!!_comp?.grid || !!_comp?.treeGrid),
          useToolBar: Object.prototype.hasOwnProperty.call(_comp, 'toolbar'),
          useGrid: Object.prototype.hasOwnProperty.call(_comp, 'grid'),
          useTreeGrid: Object.prototype.hasOwnProperty.call(_comp, 'treeGrid'),
          isUseCustomSearch,
          isCustomSearchRules,
          searchFormModel,
          isCustomSearchHandle,
          customMatchRules,
          useToolTemplate,
          customSearchExpandMinRows,
          useRightToolBarRule: Object(_comp)?.useRightToolBarRule ?? this.useRightToolBarRule,
          buttonQuantity: Object(_comp)?.buttonQuantity ?? this.buttonQuantity,
          useCombinationSelection:
            Object(_comp)?.useCombinationSelection ?? this.useCombinationSelection,
          useColumnChooser: useGridColumnChooser,
          activatedRefresh: Object(_comp)?.activatedRefresh ?? this.activatedRefresh,
          freezeOperationColumn: Object(_comp)?.freezeOperationColumn ?? this.freezeOperationColumn,
          defaultSearchTemplates: Object(_comp)?.defaultSearchTemplates,
          searchResetWithAPI: Object(_comp)?.searchResetWithAPI,
          saveSelectedRecordWithSearch: Object(_comp)?.saveSelectedRecordWithSearch,
          permissionCode: Object(_comp)?.permissionCode,
          gridId: Object(_comp)?.gridId,
          ...(_comp?.toolbar ? { toolbarConfig: this.serializeToolBarConfig(_comp) } : {}),
          ...(_comp?.grid
            ? {
                gridConfig: utils.merge({}, this.serializeGridConfig(_comp), {
                  useToolTemplate: this.override(_comp, 'useToolTemplate', this.useToolTemplate),
                  freezeOperationColumn: this.override(
                    _comp,
                    'freezeOperationColumn',
                    this.freezeOperationColumn
                  ),
                  allowFilteringFields: this.override(
                    _comp,
                    'allowFilteringFields',
                    this.allowFilteringFields
                  ),
                  ignoreFields: this.override(_comp, 'ignoreFields', this.ignoreFields),
                  isUseCustomEditor: _comp?.isUseCustomEditor,
                  isUseCustomSearch,
                  isCustomSearchRules,
                  searchFormModel,
                  customMatchRules,
                  contentConfig: this.contentConfig
                })
              }
            : {}),
          ...(_comp?.treeGrid
            ? {
                treeGridConfig: utils.merge({}, this.serializeTreeGridConfig(_comp), {
                  useToolTemplate: this.override(_comp, 'useToolTemplate', this.useToolTemplate),
                  freezeOperationColumn: this.override(
                    _comp,
                    'freezeOperationColumn',
                    this.freezeOperationColumn
                  ),
                  allowFilteringFields: this.override(
                    _comp,
                    'allowFilteringFields',
                    this.allowFilteringFields
                  ),
                  ignoreFields: this.override(_comp, 'ignoreFields', this.ignoreFields)
                })
              }
            : {})
        }
      })
      this.tabsConfig = this.serializeTabConfig(_tabDataSource)
      this.contentConfig = _contentConfig
      if (_gridId) {
        const currentTabIndex = this.currentTabIndex
        const currentTemplate = _gridMemory?.defaultTemplate
          ? _gridMemory.searchTemplates?.find((t) => t.templateName === _gridMemory.defaultTemplate)
          : this.contentConfig[currentTabIndex]?.defaultSearchTemplates?.[0]
        const _isGrid = this.contentConfig[this.currentTabIndex]?.useGrid
        const asyncConfig = _isGrid
          ? this.templateConfig[currentTabIndex]?.grid?.asyncConfig
          : this.templateConfig[currentTabIndex]?.treeGrid?.asyncConfig
        if (currentTemplate) {
          this.$set(asyncConfig, 'condition', currentTemplate.searchRule.rules.condition)
          this.$set(asyncConfig, 'rules', currentTemplate.searchRule.rules.rules)
        }
      }
    },
    override(obj, key, initVal) {
      if (!obj) return null
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        return obj.key
      } else {
        return initVal
      }
    },
    // 格式化tab参数，向下兼容title
    serializeTabParams(_config) {
      if (Object.prototype.hasOwnProperty.call(_config, 'tab')) {
        // 配置中存在tab
        return _config.tab
      } else if (Object.prototype.hasOwnProperty.call(_config, 'title')) {
        // 配置中存在title，向下兼容
        return {
          title: _config.title,
          'data-permission': _config.dataPermission,
          permissionCode: _config.permissionCode
        }
      } else {
        if (this?.templateConfig?.length === 1) {
          this.useTabs = false
        }
        return { title: this.$t('未定义Tab') }
      }
    },
    // 序列化tab参数
    serializeTabConfig(_tabDataSource) {
      const _baseConfig = utils.cloneDeep(this.tabConfig)
      return utils.merge({}, _baseConfig, {
        dataSource: _tabDataSource
      })
    },
    // 序列化toolbar参数
    serializeToolBarConfig(_comp) {
      const _toolbar = _comp.toolbar
      if (!_toolbar) return null
      const _baseConfig = utils.cloneDeep(this.toolbarConfig)
      let _toolbarList = []
      const _useBaseConfig = _comp.useBaseConfig
      if (typeof _useBaseConfig === 'boolean' && !_useBaseConfig && Array.isArray(_toolbar)) {
        /**
         * 最新版本中，设置useBaseConfig=false的使用方式
         *  useBaseConfig:false
         *  toolbar: [[{ id: "Export",icon: "icon_solid_edit",title: "导出" }], ['Filter', 'Refresh']],
         *
         */
        _toolbarList = _toolbar
      } else if (Array.isArray(_toolbar)) {
        // toolbar 设置数组
        _toolbarList.push(_toolbar)
        if (Array.isArray(_baseConfig)) {
          _toolbarList.push(_baseConfig)
        } else {
          /**
           * 单独设置 tools:['Filter', 'Setting']     不常用
           *  toolbar: [{ id: "Export",icon: "icon_solid_edit",title: "导出" }],
              tools: [, ['Filter', 'Refresh']],
           *
           */
          if (Object.prototype.hasOwnProperty.call(_baseConfig, 'tools')) {
            _toolbarList.push(_baseConfig.tools)
          }
        }
      } else {
        // 最初版本中，设置useBaseConfig=false  toolbar设置为对象
        if (
          Object.prototype.hasOwnProperty.call(_toolbar, 'useBaseConfig') &&
          _toolbar.useBaseConfig
        ) {
          /**
           *  toolbar: {
                useBaseConfig: true,//代表不使用组件中的toolbar配置，使用当前项的toolbar
                tools: [[{ id: "Export",icon: "icon_solid_edit",title: "导出" }], ['Filter', 'Refresh']],
              },
           *
           */
          if (Array.isArray(_baseConfig)) {
            _toolbarList.push(_toolbar.tools)
            _toolbarList.push(_baseConfig)
          } else {
            if (Object.prototype.hasOwnProperty.call(_toolbar, 'tools')) {
              _toolbarList = _toolbar.tools
            }
          }
        } else {
          /**
          * toolbar: {
              useBaseConfig: false,//代表不使用组件中的toolbar配置，使用当前项的toolbar
              tools: [[{ id: "Export",icon: "icon_solid_edit",title: "导出" }], ['Filter', 'Refresh']],
            },
          *
          */
          if (Object.prototype.hasOwnProperty.call(_toolbar, 'tools')) {
            _toolbarList = _toolbar.tools
          }
        }
      }
      _toolbarList.forEach((t) => {
        if (Array.isArray(t) && t.length) {
          if (t.indexOf('Filter') > -1) {
            t.splice(t.indexOf('Filter'), 1, 'resetSearch', 'quickSearch', 'combinationSearch')
            // t.splice(t.indexOf('Filter'), 1, 'quickSearch', 'combinationSearch')
          }
        }
      })
      // 修复toolbar非标准传参（[[],[]]或[[]]）场合的显示问题
      if (Array.isArray(_toolbarList[0]) && Array.isArray(_toolbarList[0][0])) {
        _toolbarList[0] = cloneDeep(_toolbarList[0][0])
      }
      return _toolbarList
    },
    // 序列化grid参数
    serializeGridConfig(_comp) {
      const _baseConfig = utils.cloneDeep(this.gridConfig)
      return utils.merge({}, _baseConfig, _comp.grid)
    },
    // 序列化treeGrid参数
    serializeTreeGridConfig(_comp) {
      const _baseConfig = utils.cloneDeep(this.treeGridConfig)
      return utils.merge({}, _baseConfig, _comp.treeGrid)
    },
    getCurrentTabRef() {
      return this.getCurrentUsefulRef(this.currentTabIndex).res
    },
    // 根据index，获取当前对应的对象链。。
    getCurrentUsefulRef(index = this.currentTabIndex) {
      let res = { tabIndex: index }
      const _config = utils.cloneDeep(this.contentConfig[index])
      let pageRef = null // 当前组件中的ref
      let pluginRef = null // 当前组件，使用pluginDataGrid的ref
      let gridRef = null // plugin中，对应的mtech组件的ref
      let ejsRef = null // 根据mtech的ref获取对应的ejs对象
      let searchRef = null // 快捷查询ref
      if (_config.useGrid || _config.useTreeGrid) {
        pageRef = this.$refs[`pageGridRef-${index}`]
        pluginRef = pageRef?.$refs['pluginGridRef']
        gridRef = pluginRef?.$refs['gridRef']
        ejsRef = gridRef?.$refs['ejsRef']
        searchRef = gridRef?.$refs['searchRef']
        if (_config.useGrid) {
          res = utils.merge({}, res, { grid: ejsRef, gridRef: gridRef, searchRef })
        } else {
          res = utils.merge({}, res, { treeGrid: ejsRef, gridRef: gridRef, searchRef })
        }
      }
      return { res, pageRef, pluginRef, gridRef, ejsRef, searchRef }
    },
    // 组件顶部toolbar的点击事件，添加当前tabIndex、grid标记，抛出
    componentClickToolBar(args, index) {
      function getOffsetSum(elem) {
        var top = 0
        var left = 0
        while (elem) {
          top = top + parseInt(elem.offsetTop)
          left = left + parseInt(elem.offsetLeft)
          elem = elem.offsetParent
        }
        return { offsetTop: top, offsetLeft: left }
      }
      const { event, toolbar: e } = args
      const _target = event?.target
      const _offSet = getOffsetSum(_target)
      const { offsetTop, offsetLeft } = _offSet
      const selfWidth = _target.clientWidth
      const selfHeight = _target.clientHeight
      const documentHeight = document.body.clientHeight
      const documentWidth = document.body.clientWidth
      let X = 0
      let Y = 0
      if (documentWidth - offsetLeft - selfWidth > 400) {
        X = offsetLeft - 10
      } else {
        X = offsetLeft - 400 + selfWidth + 10
      }
      if (documentHeight - offsetTop - selfHeight > 400) {
        Y = selfHeight + offsetTop + 10
      } else {
        Y = offsetTop - 10 - 400
      }
      this.dialogPosition = { X, Y }
      if (e.id == 'ResetSearch') {
        // 对于resetsearch操作，组件内部处理，直接调用handleQueryReset 数据重置
        this.handleQueryReset(e, index)
        return
      }
      if (e.id == 'QuickSearch') {
        // 对于快捷搜索操作，组件内部处理，不抛出事件
        const _config = this.contentConfig[index]
        if (_config.showQuickSearch) {
          this.$set(this.contentConfig[index], 'showQuickSearch', false)
          this.$nextTick(() => {
            this.handleQueryReset(e, index)
          })
          if (_config?.gridId) {
            let _gridMemory = JSON.parse(sessionStorage.getItem(_config.gridId) || '{}')
            delete _gridMemory.savedSearchItem
            sessionStorage.setItem(_config.gridId, JSON.stringify(_gridMemory))
            API.post(saveUserMemoryUrl, { gridId: _config.gridId, gridMemory: _gridMemory })
          }
        } else {
          this.limitMaxFields = true
          this.handleColumnSelection(e, index, 'search')
        }
        return
      }
      if (e.id === 'CombinationSearch') {
        // 对于高级搜索操作，组件内部处理，不抛出事件
        this.handleCombinationSelection(e, index)
        return
      }
      if (e.id == 'Filter') {
        // 对于Filter操作，组件内部处理，控制queryBuilder显隐，不抛出事件
        this.$set(
          this.contentConfig[index],
          'showQueryBuilder',
          !this.contentConfig[index].showQueryBuilder
        )
        return
      }
      if (e.id == 'Refresh') {
        // 对于Refresh操作，组件内部处理，直接调用handleQueryReset 数据重置
        this.refreshCurrentGridData()
        return
      }
      if (e.id == 'Export') {
        // 对于Export操作，组件内部处理，直接调用grid.excelExport();
        this.exportCurrentGridData(e, index)
        return
      }
      if (e.id == 'Setting') {
        // 对于Setting操作，组件内部处理
        const _config = utils.cloneDeep(this.contentConfig[index])
        if (_config.useColumnChooser) {
          // 早期使用Grid自带的openColumnChooser，如果配置useColumnChooser=true，仍然使用这个方式
          this.handleColumnChooser(e, index)
        } else {
          // 当前使用UI设计稿
          // this.handleColumnSetting(e, index)
          this.limitMaxFields = false
          this.handleColumnSelection(e, index, 'setting')
        }
        return
      }
      this.$emit(
        'handleClickToolBar',
        utils.merge({}, { toolbar: e }, this.getCurrentUsefulRef(index).res)
      )
    },
    // 组件顶部toolbar tips的点击事件，添加当前tabIndex、grid标记，抛出
    componentClickToolTips(e, index) {
      this.$emit(
        'handleClickToolTips',
        utils.merge({}, { toolbar: e, tips: e?.tips }, this.getCurrentUsefulRef(index).res)
      )
    },
    // 组件顶部toolbar marker的点击事件，添加当前tabIndex、grid标记，抛出
    componentClickToolMarker(e, index) {
      this.$emit(
        'handleClickToolMarker',
        utils.merge({}, { toolbar: e, marker: e?.marker }, this.getCurrentUsefulRef(index).res)
      )
    },
    // 单元格内的按钮，点击事件，添加当前tabIndex、grid标记，抛出
    componentClickCellTool(e, index) {
      this.$emit('handleClickCellTool', utils.merge({}, e, this.getCurrentUsefulRef(index).res))
    },
    // 单元格内的title，点击事件，添加当前tabIndex、grid标记，抛出
    componentClickCellTitle(e, index) {
      this.$emit('handleClickCellTitle', utils.merge({}, e, this.getCurrentUsefulRef(index).res))
    },
    // 表格切换页码
    componentChangePageIndex(e, index) {
      const _config = utils.cloneDeep(this.contentConfig[index])
      let __config = null
      if (_config.useGrid) {
        __config = _config.gridConfig?.asyncConfig
      } else if (_config.useTreeGrid) {
        __config = _config.treeGridConfig
      }
      if (!__config?.url) {
        // 存在aync配置，并且存在url，在组件内部执行页面切换，事件不抛出。不存在url，抛出事件
        if (!isNaN(e)) {
          this.$emit(
            'handleGridCurrentChange',
            utils.merge({}, { currentPage: e }, this.getCurrentUsefulRef(index).res)
          )
        }
      }
    },
    // 表格切换每页显示条数
    componentChangePageSize(e, index) {
      const _config = utils.cloneDeep(this.contentConfig[index])
      let __config = null
      if (_config.useGrid) {
        __config = _config.gridConfig?.asyncConfig
      } else if (_config.useTreeGrid) {
        __config = _config.treeGridConfig
      }
      if (!__config?.url) {
        // 存在aync配置，并且存在url，在组件内部执行页面切换，事件不抛出。不存在url，抛出事件
        this.$emit(
          'handleGridSizeChange',
          utils.merge({}, { currentPage: e }, this.getCurrentUsefulRef(index).res)
        )
      }
    },
    handleSaveMemory(e, index = this.currentTabIndex) {
      if (this.contentConfig[index]?.gridId) {
        let _gridId = this.contentConfig[index].gridId
        let _gridMemory = JSON.parse(
          sessionStorage.getItem(this.contentConfig[index].gridId) || '{}'
        )
        _gridMemory = {
          ..._gridMemory,
          ...e
        }
        if (e?.visibleCols) {
          _gridMemory.visibleCols = e.visibleCols.filter((x) => x.field !== 'lineIndexComponent')
        }
        sessionStorage.setItem(_gridId, JSON.stringify(_gridMemory))
        API.post(saveUserMemoryUrl, { gridId: _gridId, gridMemory: _gridMemory })
      }
    },
    // quickSearch中的过滤
    handleQuickSearch(e, index = this.currentTabIndex) {
      const { form, rules } = e
      const _config = utils.cloneDeep(this.contentConfig[index])
      const _gridConfig = _config?.gridConfig
      const _treeGridConfig = _config?.treeGridConfig
      if (_gridConfig?.asyncConfig?.url || _treeGridConfig?.asyncConfig?.url) {
        // 存在aync配置，并且存在url，异步获取数据，传入规则，执行异步请求
        const _currentRef = this.getCurrentUsefulRef(index)
        _currentRef.pluginRef.loadGridDataUseQueryBuilder(rules, 'handle')
      } else {
        // 直接使用dataSource，操作表格数据的场景
        this.$emit(
          'handleClickToolBar',
          utils.merge(
            {},
            { toolbar: { id: 'filterDataByLocal', title: this.$t('筛选数据') }, rules, form },
            this.getCurrentUsefulRef(index).res
          )
        )
      }
    },
    // quickSearch中的重置
    handleQuickReset(e, index = this.currentTabIndex) {
      const _config = utils.cloneDeep(this.contentConfig[index])
      const _gridConfig = _config?.gridConfig
      const _treeGridConfig = _config?.treeGridConfig
      let _tempConfig = this.contentConfig[index]
      // if (_tempConfig.isUseCustomSearch) {
      //   this.$emit('handleCustomReset')
      // }
      let _contentObject = _tempConfig.useGrid ? _tempConfig.gridConfig : _tempConfig.treeGridConfig
      if (_gridConfig?.asyncConfig?.url || _treeGridConfig?.asyncConfig?.url) {
        // 存在aync配置，并且存在url，异步获取数据，重置规则，执行异步请求
        delete _contentObject.asyncConfig.condition
        delete _contentObject.asyncConfig.rules
        const _currentRef = this.getCurrentUsefulRef(index)
        _currentRef.pluginRef.loadGridDataUseQueryBuilder()
      } else {
        // 直接使用dataSource，操作表格数据的场景
        this.$emit(
          'handleClickToolBar',
          utils.merge(
            {},
            { toolbar: { id: 'resetDataByLocal', title: this.$t('重置数据') } },
            this.getCurrentUsefulRef(index).res
          )
        )
      }
    },
    // queryBuilder中的过滤
    handleQuerySearch(e, index) {
      const _config = utils.cloneDeep(this.contentConfig[index])
      const _gridConfig = _config?.gridConfig
      const _treeGridConfig = _config?.treeGridConfig
      if (_gridConfig?.asyncConfig?.url || _treeGridConfig?.asyncConfig?.url) {
        // 存在aync配置，并且存在url，异步获取数据，传入规则，执行异步请求
        const _currentRef = this.getCurrentUsefulRef(index)
        _currentRef.pluginRef.loadGridDataUseQueryBuilder(e)
      } else {
        // 直接使用dataSource，操作表格数据的场景
        this.$emit(
          'handleClickToolBar',
          utils.merge(
            {},
            { toolbar: { id: 'filterDataByLocal', title: this.$t('筛选数据') }, rules: e },
            this.getCurrentUsefulRef(index).res
          )
        )
      }
    },
    // queryBuilder中的重置
    handleQueryReset(e, index) {
      const _config = utils.cloneDeep(this.contentConfig[index])
      const _gridConfig = _config?.gridConfig
      const _treeGridConfig = _config?.treeGridConfig
      const _currentRef = this.getCurrentUsefulRef(index)
      if (this.$refs.searchRef) {
        this.$set(this.$refs.searchRef.$children[0], 'currentSearchTemplate', '')
      }
      let _tempConfig = this.contentConfig[index]
      let _contentObject = _tempConfig.useGrid ? _tempConfig.gridConfig : _tempConfig.treeGridConfig
      if (_gridConfig?.asyncConfig?.url || _treeGridConfig?.asyncConfig?.url) {
        // 存在aync配置，并且存在url，异步获取数据，重置规则，执行异步请求
        delete _contentObject.asyncConfig.condition
        delete _contentObject.asyncConfig.rules
        _currentRef.pluginRef.loadGridDataUseQueryBuilder()
      } else {
        // 直接使用dataSource，操作表格数据的场景
        this.$emit(
          'handleClickToolBar',
          utils.merge(
            {},
            { toolbar: { id: 'resetDataByLocal', title: this.$t('重置数据') } },
            this.getCurrentUsefulRef(index).res
          )
        )
      }
      _currentRef.ejsRef.clearFiltering()
    },
    // 刷新当前Tab下表格数据
    refreshCurrentGridData(data) {
      let isClearAll = true
      data ? (isClearAll = data.isClearAll) : (isClearAll = true)
      const index = this.currentTabIndex
      const _config = utils.cloneDeep(this.contentConfig[index])
      const _gridConfig = _config?.gridConfig
      const _treeGridConfig = _config?.treeGridConfig
      if (_gridConfig?.asyncConfig?.url || _treeGridConfig?.asyncConfig?.url) {
        // 存在aync配置，并且存在url，异步获取数据，重置规则，执行异步请求
        const _currentRef = this.getCurrentUsefulRef()
        _currentRef.pluginRef.refreshGridData()
      } else {
        // 直接使用dataSource，刷新表格数据的场景
        this.$emit(
          'handleClickToolBar',
          utils.merge(
            {},
            { toolbar: { id: 'refreshDataByLocal', title: this.$t('刷新数据') } },
            this.getCurrentUsefulRef(index).res
          )
        )
      }
      if (isClearAll) {
        this.getCurrentUsefulRef(index).res?.gridRef?.clearAll()
      }
    },
    // 重新获取当前Tab下表格数据
    reloadCurrentGridData() {
      const index = this.currentTabIndex
      const _config = utils.cloneDeep(this.contentConfig[index])
      const _gridConfig = _config?.gridConfig
      const _treeGridConfig = _config?.treeGridConfig
      if (_gridConfig?.asyncConfig?.url || _treeGridConfig?.asyncConfig?.url) {
        // 存在aync配置，并且存在url，异步获取数据，重置规则，执行异步请求
        const _currentRef = this.getCurrentUsefulRef()
        _currentRef.pluginRef.reloadGridData()
      }
    },
    // 刷新当前Tab下表格数据
    refreshGridData() {
      this.refreshCurrentGridData()
    },
    // 重新获取当前Tab下表格数据
    reloadGridData() {
      this.reloadCurrentGridData()
    },
    // 获取异步请求的请求参数
    getAsyncParams() {
      const index = this.currentTabIndex
      const _config = utils.cloneDeep(this.contentConfig[index])
      const _gridConfig = _config?.gridConfig
      if (_gridConfig?.asyncConfig?.url) {
        // 存在aync配置，并且存在url，异步获取数据，重置规则，执行异步请求
        const _currentRef = this.getCurrentUsefulRef()
        return _currentRef?.pluginRef?.asyncParams ?? {}
      } else {
        // 直接使用dataSource，获取筛选规则
        return {} // todo 使用本地数据时，返回用户定义的queryBuilder校验规则
      }
    },
    // 导出当前Tab下表格数据
    exportCurrentGridData(e, index) {
      const _currentRef = this.getCurrentUsefulRef(index)
      const excelExportProperties = {
        fileName: `${this.$t('导出表格数据')}.xlsx`
      }
      _currentRef.ejsRef.excelExport(excelExportProperties)
    },
    toggleLoading(e) {
      this.loadingStatus = e
    },
    // 列设置、列选择，暂时使用表格组件中默认的openColumnChooser方法
    handleColumnChooser(e, index) {
      const _currentRef = this.getCurrentUsefulRef(index)
      _currentRef.ejsRef.openColumnChooser()
    },
    // tab切换
    async componentSelectTab(e) {
      if (this.currentTabIndex === e) {
        return
      } else {
        this.currentTabIndex = e
      }
      let _config = utils.cloneDeep(this.contentConfig[e])
      let _currentConfig = this.templateConfig[e]
      let _gridObject = _config.useGrid ? _currentConfig?.grid : _currentConfig?.treeGrid
      if (
        !!_gridObject &&
        Object.hasOwnProperty.call(_gridObject, 'asyncConfig') &&
        !!_gridObject.asyncConfig
      ) {
        let _useLoading = true
        if (Object.prototype.hasOwnProperty.call(_gridObject.asyncConfig, 'loading')) {
          // 配置中单独配置了loading
          _useLoading = _gridObject._asyncConfig.loading
        }
        this.toggleLoading(_useLoading)
      }
      if (this.contentConfig[e]?.gridId) {
        let _gridId = this.contentConfig[e].gridId
        await API.get(getUserMemoryUrl, { gridId: _gridId })
          .then((res) => {
            if (res?.code === 200 && res?.data?.gridMemory) {
              sessionStorage.setItem(_gridId, JSON.stringify(res.data.gridMemory))
            } else {
              sessionStorage.setItem(_gridId, JSON.stringify({}))
            }
          })
          .catch(() => {
            sessionStorage.setItem(_gridId, JSON.stringify({}))
          })
        let _gridMemory = JSON.parse(sessionStorage.getItem(_gridId) || '{}')
        let _tempConfig = this.contentConfig[e]
        let _contentObject = _tempConfig.useGrid
          ? _tempConfig.gridConfig
          : _tempConfig.treeGridConfig
        if (_gridMemory?.defaultTemplate) {
          let _currentTemplate = _gridMemory.searchTemplates.find(
            (e) => e.templateName === _gridMemory.defaultTemplate
          )
          this.$set(
            _contentObject.asyncConfig,
            'condition',
            _currentTemplate.searchRule.rules.condition
          )
          this.$set(_contentObject.asyncConfig, 'rules', _currentTemplate.searchRule.rules.rules)
        }
        let _columns = _gridObject?.columnData
        if (!!_gridMemory.visibleCols && !_columns.length < _gridMemory.visibleCols.length) {
          let _visibleFields = _gridMemory.visibleCols.map((e) => e.field)
          this.initShowColumns(_visibleFields)
        }
        if (_gridMemory?.savedSearchItem) {
          this.searchColumnData = _gridMemory.savedSearchItem
        } else if (_gridObject?.defaultSearchItem) {
          this.searchColumnData = _gridObject.defaultSearchItem
        } else {
          this.searchColumnData = []
        }
        if (
          this?.searchColumnData.length > 0 ||
          !!_gridMemory.defaultTemplate ||
          this?.contentConfig[e]?.defaultSearchTemplates?.length > 0
        ) {
          this.$set(this.contentConfig[e], 'showQuickSearch', true)
        }
      }
    },
    // 快捷搜索、列设置，使用弹框
    handleColumnSelection(e, index, type = 'search') {
      const _config = utils.cloneDeep(this.contentConfig[index])
      if (_config.useGrid || _config.useTreeGrid) {
        const _currentConfig = this.templateConfig[index]
        if (type === 'setting') {
          const _currentRef = this.getCurrentUsefulRef(index)
          const _showColumns = _currentRef.ejsRef.getVisibleColumns()
          if (Array.isArray(_showColumns) && _showColumns.length) {
            const _visible = []
            _showColumns.forEach((c) => {
              if (c.field && c.headerText) {
                _visible.push({
                  field: c.field,
                  headerText: c.headerText,
                  ignore: !!c.ignore,
                  sticky: !!c.sticky,
                  allowReordering: !!c.allowReordering
                })
              }
            })
            if (_visible.length) {
              this.visibleColumns = _visible
            }
          }
        } else {
          // 搜索状态下，不设置默认的‘显示列’，组件中优先处理‘sticky’列
          this.visibleColumns = []
        }
        const _gridObject = _config.useGrid ? _currentConfig.grid : _currentConfig.treeGrid
        const _columns = _gridObject?.columnData
        if (Array.isArray(_columns) && _columns.length) {
          const _resource = []
          for (let i = 0; i < _columns.length; i++) {
            if (typeof _columns[i].visible === 'boolean' && !_columns[i].visible) {
              // 列配置时，设置了visible=false
              continue
            }
            if (_columns[i].field && _columns[i].headerText) {
              _resource.push(_columns[i])
            }
          }
          if (_resource.length) {
            this.resourceColumns = _resource
            this.selectionType = type
            this.showColumnSelection = true
          }
        }
      }
    },
    // 高级搜索弹框，打开前数据准备
    handleCombinationSelection(e, index) {
      const _config = utils.cloneDeep(this.contentConfig[index])
      if (_config.useGrid || _config.useTreeGrid) {
        const _currentConfig = this.templateConfig[index]
        const _gridObject = _config.useGrid ? _currentConfig.grid : _currentConfig.treeGrid
        const _columns = _gridObject?.columnData
        if (Array.isArray(_columns) && _columns.length) {
          const _resource = []
          for (let i = 0; i < _columns.length; i++) {
            if (typeof _columns[i].visible === 'boolean' && !_columns[i].visible) {
              // 列配置时，设置了visible=false
              continue
            }
            if (_columns[i].field && _columns[i].headerText) {
              _resource.push(_columns[i])
            }
          }
          if (_resource.length) {
            ;(this.resourceColumns = _resource),
              (this.allowFilteringFields = _gridObject.allowFilteringFields),
              (this.ignoreFields = _gridObject.ignoreFields),
              (this.showCombinationSelection = true)
          }
        }
      }
    },
    // 高级查询弹框-确认操作
    handleConfirmCombinationSelection() {
      this.showCombinationSelection = false
    },
    // 高级查询弹框-取消操作
    handleCancelCombinationSelection() {
      this.showCombinationSelection = false
    },
    // 列设置弹框-确认操作
    handleConfirmColumnSelection(e) {
      this.showColumnSelection = false
      const { type, value } = e
      if (type === 'search') {
        const _visibleColumns = value.visibleColumns
        // 对于quickSearch操作，组件内部处理，控制queryBuilder显隐，不抛出事件
        const index = this.currentTabIndex
        this.searchColumnData = _visibleColumns
        if (this.contentConfig[index]?.gridId) {
          let _gridId = this.contentConfig[index].gridId
          let _gridMemory = JSON.parse(sessionStorage.getItem(_gridId) || '{}')
          _gridMemory.savedSearchItem = this.searchColumnData
          sessionStorage.setItem(_gridId, JSON.stringify(_gridMemory))
          API.post(saveUserMemoryUrl, { gridId: _gridId, gridMemory: _gridMemory })
        }
        if (this.$refs.searchRef) this.$refs.searchRef.$children[0].basicExpand = true
        this.$set(this.contentConfig[index], 'showQuickSearch', true)
      } else if (type === 'setting') {
        const { visibleFields } = value
        const index = this.currentTabIndex
        const _config = utils.cloneDeep(this.contentConfig[index])
        const _columns = _config.useGrid
          ? utils.cloneDeep(_config?.gridConfig?.columnData)
          : utils.cloneDeep(_config?.treeGridConfig?.columnData)
        const _visible = new Array(visibleFields.length)
        const _hidden = []
        const _fixed = []
        _columns.forEach((c, i) => {
          if (visibleFields.indexOf(c.field) > -1) {
            c.visible = true
            _visible[visibleFields.indexOf(c.field)] = c
          } else if (i === 0 && ['checkbox', 'checkBox'].includes(c.type)) {
            // 第一列为 checkbox  单独处理
            c.visible = true
            _fixed.push(c)
          } else {
            c.visible = false
            _hidden.push(c)
          }
        })
        if (this.contentConfig[index]?.gridId) {
          let _gridId = this.contentConfig[index].gridId
          let _gridMemory = JSON.parse(sessionStorage.getItem(_gridId) || '{}')
          _gridMemory.visibleCols = _visible
          _gridMemory.hiddenCols = _hidden
          sessionStorage.setItem(_gridId, JSON.stringify(_gridMemory))
          API.post(saveUserMemoryUrl, { gridId: _gridId, gridMemory: _gridMemory })
        }
        if (_config.useGrid) {
          this.$set(
            this.contentConfig[index].gridConfig,
            'columnData',
            _fixed.concat(_visible, _hidden)
          )
        } else {
          this.$set(
            this.contentConfig[index].treeGridConfig,
            'columnData',
            _fixed.concat(_visible, _hidden)
          )
        }
      }
    },
    // 列设置弹框-取消操作
    handleCancelColumnsSelection() {
      this.showColumnSelection = false
    },
    // 列设置、列选择，使用弹框  当前弃用
    handleColumnSetting(e, index) {
      const _config = utils.cloneDeep(this.contentConfig[index])
      if (_config.useGrid || _config.useTreeGrid) {
        const _currentConfig = this.templateConfig[index]
        const _gridObject = _config.useGrid ? _currentConfig.grid : _currentConfig.treeGrid
        const _currentRef = this.getCurrentUsefulRef(index)
        const _showColumns = _currentRef.ejsRef.getVisibleColumns()
        const _columns = _gridObject?.columnData
        if (Array.isArray(_showColumns) && _showColumns.length) {
          const _cols = []
          _showColumns.forEach((c) => {
            if (c.field && c.headerText) {
              _cols.push({ field: c.field, headerText: c.headerText })
            }
          })
          if (_cols.length) {
            this.visibleColumns = _cols
          }
        }
        if (Array.isArray(_columns) && _columns.length) {
          const _cols = []
          _columns.forEach((c) => {
            if (typeof c.visible === 'boolean' && !c.visible) {
              // 列配置时，设置了visible=false
              return
            }
            if (c.field && c.headerText) {
              _cols.push({ field: c.field, headerText: c.headerText })
            }
          })
          if (_cols.length) {
            this.resourceColumns = _cols
            this.showColumnSetting = true
          }
        }
      }
    },
    // 列设置弹框-确认操作
    handleConfirmColumnSetting(e) {
      const { visible, hidden } = e
      this.showColumnSetting = false
      const _currentRef = this.getCurrentUsefulRef()
      if (Array.isArray(visible) && visible.length) {
        _currentRef.ejsRef.showColumns(visible, 'field')
      }
      if (Array.isArray(hidden) && hidden.length) {
        _currentRef.ejsRef.hideColumns(hidden, 'field')
      }
    },
    // 列设置弹框-取消操作
    handleCancelColumnsSetting() {
      this.showColumnSetting = false
    }
  }
}
</script>
<style lang="scss" scoped>
.mt-flex {
  display: flex;
  position: relative;
}
.mt-flex-direction-column {
  display: flex;
  flex-direction: column;
  position: relative;
}
.common-template-page {
  width: 100%;
  height: 100%;
  background: var(--common-tp-bg-fa);
  padding: 0;
  box-sizing: border-box;
  overflow: auto;

  .mt-loading {
    position: absolute;
    width: 100%;
    height: 100%;
    ::v-deep .loadingImg {
      height: 100%;
    }
  }

  .mt-tabs {
    flex-shrink: 0;
  }
  &.template-hidden-tabs {
    padding-top: 20px;
  }
  .repeat-template {
    flex-grow: 1;
    // overflow: hidden;
    flex: 1;
    background: var(--common-tp-bg-ff);
    .template-wrap {
      height: 100%;
      flex-grow: 1;
      display: flex;
      flex: 1;
      flex-direction: column;
      .template-height {
        flex: 1;
      }
      .grid-container {
        flex: 1;
        height: auto;
        overflow: hidden;
        ::v-deep .mt-data-grid {
          .e-content {
            .e-table {
              border-bottom: 1px solid #e0e0e0;
            }
          }
          .e-grid {
            .e-gridcontent {
              height: calc(100% - 44px);
            }
          }
        }
      }

      .quick-search {
        .mt-form {
          .mt-form-item {
            .mt-form-item-topLabel {
              .j-select-wrapper {
                // width: 100% !important;
                .j-select {
                  // width: 100% !important;
                  .ant-select-selection {
                    // width: 100% !important;
                    border-bottom: 1px solid rgba(0, 0, 0, 0.42);
                  }
                }
              }
            }
          }
        }
      }
    }
    // .mt-loading {
    //   position: absolute;
    //   width: 100%;
    //   height: 100%;
    //   ::v-deep .loadingImg {
    //     height: 100%;
    //   }
    // }
  }
  // .mt-loading {
  //   position: fixed;
  //   z-index: 2;
  //   background: var(--common-tp-loading) !important;
  // }
  ::v-deep.e-dlg-container {
    .e-dlg-overlay {
      background: transparent;
    }
    .column-selction {
      border-radius: 4px;
      box-shadow: 0 0 6px 0 var(--common-tp-column-selction-box-shadow);

      div {
        box-sizing: border-box;
      }
      .e-dlg-header-content {
        display: none;
      }
      .dialog-footer {
        width: 100%;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: end;
        padding: 0 10px;
        .footer-label {
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: normal;
          color: var(--common-tp-column-footer-label-color);
        }
        .footer-btns {
          display: flex;
          .btn {
            padding: 0 10px;
            cursor: pointer;
            display: flex;
            align-items: center;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: 500;
            color: var(--common-tp-column-footer-btns-color);
            &.last-btn {
              margin-left: 20px;
            }
            &.disable-btn {
              color: var(--common-tp-footer-disable-color);
              cursor: not-allowed;
            }
          }
        }
      }
    }
    .column-setting {
      .e-dlg-content {
        padding: 0;
        display: flex;
      }
      .dialog-content {
        padding: 20px 30px;
        flex: 1;
        .setting-option {
          margin-bottom: 20px;
          flex-shrink: 0;
          .toolbar-item {
            min-width: auto;
            line-height: 1;
            cursor: pointer;
            padding: 0;
            margin-left: 20px;
            display: flex;
            align-items: center;
            position: relative;
            .mt-icons {
              font-size: 14px;
              color: var(--common-tp-span-color);
              margin-right: 5px;
            }

            span {
              word-break: keep-all;
              font-size: 14px;
              color: var(--common-tp-span-color);
              font-weight: normal;
            }
            &:first-of-type {
              margin-left: 0;
            }
            &:hover {
              .mt-icons,
              span {
                color: var(--common-tp-span-hover-color);
              }
            }
          }
        }
        .setting-content {
          flex: 1;
          .source-columns {
            flex: 1;
            border: 1px solid var(--common-tp-border-color);
            border-radius: 4px;
            .mt-listbox {
              .e-listbox-wrapper {
                border: none;
              }
              .e-selectall-parent {
                height: 40px;
              }
              .e-list-item {
                height: 50px;
                line-height: 50px;
              }
            }
          }
          .result-columns {
            flex: 1;
            margin-left: 30px;
            border: 1px solid var(--common-tp-border-color);
            border-radius: 4px;
            .result-title {
              padding-left: 20px;
              height: 40px;
              line-height: 40px;
              font-size: 16px;
              font-family: PingFangSC;
              font-weight: 500;
              color: var(--common-tp-title-color);
              border-bottom: 1px solid var(--common-tp-border-color);
              flex-shrink: 0;
            }
            .result-content {
              flex: 1;
              padding: 0 20px;
              .result-item {
                border-radius: 4px;
                box-shadow: 0 2px 6px 0 var(--common-tp-result-item-box-shadow);
                margin-top: 10px;
                height: 40px;
                line-height: 40px;
                .item-handler {
                  background: var(--common-tp-column-handler-bg);
                  position: relative;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  border-radius: 4px 0 0 4px;
                  cursor: move;
                  &:before {
                    content: '';
                    height: 16px;
                    border-left: 1px solid var(--common-tp-handle-before-color);
                    margin-left: 3px;
                  }
                  &:after {
                    content: '';
                    height: 16px;
                    border-left: 1px solid var(--common-tp-handle-before-color);
                    margin: 0 3px;
                  }

                  &:hover {
                    background: var(--common-tp-column-handler-hover-bg);
                  }
                }
                .item-content {
                  font-size: 14px;
                  font-family: PingFangSC;
                  font-weight: 500;
                  color: var(--common-tp-column-color);
                  flex: 1;
                  padding-left: 15px;
                }
              }
            }
          }
        }
      }
    }
    .combination-selction {
      .dialog-content {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        padding: 20px 2px 2px 2px;
        div {
          box-sizing: border-box;
        }

        .rule-item-container {
          width: 100%;
          display: flex;
          flex-direction: column;
          // max-height: 450px;
          // overflow-y: auto;
          // overflow-x: hidden;
          .rule-item {
            display: flex;
            flex-wrap: nowrap;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            height: 40px;
            .item {
              margin-right: 10px;
              ::v-deep.rule-element {
                &:before,
                &:after {
                  display: none;
                }
              }
            }
            .condition,
            .operator {
              width: 75px;
              flex-shrink: 0;
            }
            .data-source {
              width: 120px;
              flex-shrink: 0;
              &-first {
                width: 200px;
              }
            }
            .data-value {
              flex: 1;
              .j-select-wrapper {
                // width: 100% !important;
                .j-select {
                  // width: 100% !important;
                  .ant-select-selection {
                    // width: 100% !important;
                    border-bottom: 1px solid rgba(0, 0, 0, 0.42);
                  }
                }
              }
            }
            .remove {
              width: 16px;
              flex-shrink: 0;
              margin-right: 0;
              .mt-icons {
                color: var(--common-tp-column-remove-color);
                font-weight: 400;
                font-size: 14px;
                cursor: pointer;
                &:hover {
                  color: var(--common-tp-column-handler-hover-bg);
                }
              }
            }
          }
        }
        .add-rule-item {
          display: flex;
          height: 30px;
          width: 140px;
          align-items: center;
          cursor: pointer;
          .mt-icons {
            color: var(--common-tp-column-handler-hover-bg);
            font-weight: 400;
            font-size: 18px;
          }
          .label {
            margin-left: 10px;
            font-family: PingFangSC;
            font-weight: normal;
            color: var(--common-tp-add-rule-label-color);
            font-weight: 400;
            font-size: 14px;
          }
          &:hover {
            .icon,
            .label {
              font-weight: 500;
            }
          }
        }

        //修改谷歌内核浏览器滚动条样式
        ::-webkit-scrollbar {
          width: 10px;
          height: 10px;
        }
        ::-webkit-scrollbar-track {
          border-radius: 2px;
          background-color: var(--scroll-bar-track-color);
        }

        ::-webkit-scrollbar-thumb {
          background-color: var(--scroll-bar-thumb-color);
          border-radius: 2px;
        }

        ::-webkit-scrollbar-thumb:hover {
          background-color: var(--scroll-bar-thumb-hover-color);
        }

        ::-webkit-scrollbar-thumb:active {
          background-color: var(--scroll-bar-thumb-active-color);
        }
      }
    }
  }
}
</style>
