<template>
  <div class="common-rule-config mt-flex-direction-column" :id="'ruleContainer-' + randomId">
    <div
      :class="['mt-flex', 'rule-item-container', { 'simple-container': type == 'simple' }]"
      :name="'item-container-' + index"
      v-for="(rule, index) in ruleData"
      :key="index"
    >
      <div class="rule-item rule-item-handler"></div>
      <div class="rule-item rule-link split" v-if="type == 'link'" :ref="'leftLink-' + index">
        <mt-select
          v-model="rule.groupLeft"
          css-class="rule-element"
          :data-source="groupLeftList"
        ></mt-select>
      </div>
      <div class="rule-item rule-data-source split" :ref="'dataSource-' + index">
        <mt-select
          :ref="'dataSourceRef-' + index"
          css-class="rule-element"
          v-model="rule.dataSource"
          :data-source="queryDataSource"
          @change="handleSelectField(index)"
          :fields="queryFields"
        ></mt-select>
      </div>
      <div
        :class="['rule-item', 'rule-symbol', 'split', { 'simple-rule-symbol': type == 'simple' }]"
        :ref="'symbol-' + index"
      >
        <mt-select
          v-model="rule.symbol"
          css-class="rule-element"
          :data-source="getSymbolList"
        ></mt-select>
      </div>
      <div
        :class="[
          'rule-item',
          'rule-data-value',
          'split',
          { 'simple-data-value': type == 'simple' }
        ]"
        :ref="'dataValue-' + index"
      >
        <mt-input
          v-if="showElementByType(rule, 'text')"
          css-class="rule-element"
          v-model="rule.dataValue"
          :show-clear-button="false"
          type="text"
        ></mt-input>
        <mt-select
          v-if="showElementByType(rule, 'select')"
          css-class="rule-element"
          v-model="rule.dataValue"
          :data-source="getElementDataSource(rule)"
          :fields="getElementDataFields(rule)"
        ></mt-select>
        <mt-multi-select
          v-if="showElementByType(rule, 'multi-select')"
          css-class="rule-element"
          v-model="rule.dataValue"
          :data-source="getElementDataSource(rule)"
          :fields="getElementDataFields(rule)"
        ></mt-multi-select>
        <mt-date-picker
          v-if="showElementByType(rule, 'date')"
          css-class="rule-element"
          :open-on-focus="true"
          :show-clear-button="false"
          v-model="rule.dataValue"
        ></mt-date-picker>
        <mt-time-picker
          v-if="showElementByType(rule, 'time')"
          css-class="rule-element"
          :open-on-focus="true"
          :show-clear-button="false"
          v-model="rule.dataValue"
        ></mt-time-picker>
        <mt-date-time-picker
          v-if="showElementByType(rule, 'datetime')"
          css-class="rule-element"
          :open-on-focus="true"
          :show-clear-button="false"
          v-model="rule.dataValue"
        ></mt-date-time-picker>
        <mt-drop-down-tree
          v-if="showElementByType(rule, 'drop-down-tree')"
          css-class="rule-element"
          id="tree"
          v-model="rule.dataValue"
          :fields="getElementTreeDataFields(rule)"
          :show-clear-button="false"
          @input="vaulechange"
        ></mt-drop-down-tree>
      </div>
      <div class="rule-item rule-link split" v-if="type == 'link'" :ref="'rightLink-' + index">
        <mt-select
          v-model="rule.groupRight"
          css-class="rule-element"
          :data-source="groupRightList"
        ></mt-select>
      </div>
      <div
        :class="[
          'rule-item',
          'rule-link',
          'last-link',
          { 'last-item': index === ruleData.length - 1 }
        ]"
        v-if="type == 'link'"
      >
        <mt-select
          v-model="rule.groupLink"
          css-class="rule-element"
          :data-source="groupLinkList"
        ></mt-select>
      </div>
      <div class="rule-item rule-icons">
        <img
          @click="addRuleItem(index)"
          src="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNjI2Mjg0NjE5NTcwIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjUyNDAiIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPjxkZWZzPjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+PC9zdHlsZT48L2RlZnM+PHBhdGggZD0iTTUxMS45OTkwNCAwYzY5LjExOTg3IDAgMTM2LjQ0Nzc0NCAxMy43NTk5NzQgMTk4LjkxMTYyNyA0MC4wNjM5MjVhMzEuOTk5OTQgMzEuOTk5OTQgMCAxIDEtMjQuOTU5OTUzIDU4Ljk0Mzg4OSA0NDcuOTk5MTYgNDQ3Ljk5OTE2IDAgMSAwIDIzOS43NDM1NSAyNDAuODMxNTQ5IDMxLjk5OTk0IDMxLjk5OTk0IDAgMSAxIDU5LjEzNTg4OS0yNC41NzU5NTRBNTExLjk5OTA0IDUxMS45OTkwNCAwIDEgMSA1MTEuOTk5MDQgMHogbTAgMTkxLjk5OTY0YTMxLjk5OTk0IDMxLjk5OTk0IDAgMCAxIDMxLjk5OTk0IDMxLjk5OTk0djI1NS45MzU1MmwyNTUuOTk5NTIgMC4wNjRhMzEuOTk5OTQgMzEuOTk5OTQgMCAxIDEgMCA2My45OTk4OGwtMjU1Ljk5OTUyLTAuMDY0Vjc5OS45OTg1YTMxLjk5OTk0IDMxLjk5OTk0IDAgMSAxLTYzLjk5OTg4IDBWNTQzLjkzNDk4bC0yNTUuOTk5NTIgMC4wNjRhMzEuOTk5OTQgMzEuOTk5OTQgMCAwIDEgMC02My45OTk4OGwyNTUuOTk5NTItMC4wNjRWMjIzLjk5OTU4QTMxLjk5OTk0IDMxLjk5OTk0IDAgMCAxIDUxMS45OTkwNCAxOTEuOTk5NjR6IiBmaWxsPSIjNjM4NkMxIiBwLWlkPSI1MjQxIj48L3BhdGg+PC9zdmc+"
          alt=""
        />
        <img
          @click="removeRuleItem(index)"
          v-if="ruleData.length > 1"
          src="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNjI2Mjg0Njc1MDEwIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjU0NDQiIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMzIiIGhlaWdodD0iMzIiPjxkZWZzPjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+PC9zdHlsZT48L2RlZnM+PHBhdGggZD0iTTUxMS45OTkwNCAwYzY5LjExOTg3IDAgMTM2LjQ0Nzc0NCAxMy43NTk5NzQgMTk4LjkxMTYyNyA0MC4wNjM5MjVhMzEuOTk5OTQgMzEuOTk5OTQgMCAxIDEtMjQuOTU5OTUzIDU4Ljk0Mzg4OSA0NDcuOTk5MTYgNDQ3Ljk5OTE2IDAgMSAwIDIzOS43NDM1NSAyNDAuODMxNTQ5IDMxLjk5OTk0IDMxLjk5OTk0IDAgMSAxIDU5LjEzNTg4OS0yNC41NzU5NTRBNTExLjk5OTA0IDUxMS45OTkwNCAwIDEgMSA1MTEuOTk5MDQgMHogbTI4Ny45OTk0NiA0NzkuOTk5MWEzMS45OTk5NCAzMS45OTk5NCAwIDEgMSAwIDYzLjk5OTg4aC01NzUuOTk4OTJhMzEuOTk5OTQgMzEuOTk5OTQgMCAwIDEgMC02My45OTk4OGg1NzUuOTk4OTJ6IiBmaWxsPSIjRUQ1NjMzIiBwLWlkPSI1NDQ1Ij48L3BhdGg+PC9zdmc+"
          alt=""
        />
        <!-- <span class="iconfont icon-add-rule"
              @click="addRuleItem(index)"></span>
        <span class="iconfont icon-remove-rule"
              @click="removeRuleItem(index)"
              v-if="ruleData.length>1"></span> -->
      </div>
    </div>
  </div>
</template>

<script>
import { utils } from '@mtech-common/utils'
import MtInput from '@mtech-ui/input'
import MtSelect from '@mtech-ui/select'
import MtMultiSelect from '@mtech-ui/multi-select'
import MtDatePicker from '@mtech-ui/date-picker'
import MtTimePicker from '@mtech-ui/time-picker'
import MtDateTimePicker from '@mtech-ui/date-time-picker'
import MtDropDownTree from '@mtech-ui/drop-down-tree'
import MtSvgIcon from './components/MtSvgIcon'
import * as ToolTip from './components/Tooltip'
import Vue from 'vue'
import MtTooltip from '@mtech-ui/tooltip'
import Sortable from 'sortablejs'
Vue.prototype[ToolTip.NAME] = ToolTip.COMPONENT

export default {
  props: {
    type: {
      type: String,
      default: 'link'
    },
    value: {
      type: Array,
      default: () => {
        return []
      }
    },
    queryFields: {
      type: Object,
      default: () => {
        return { text: 'text', value: 'value' }
      }
    },
    queryDataSource: {
      type: Array,
      default: () => {
        return []
      }
    },
    symbolList: {
      type: Array,
      default: () => {
        return [
          { text: '>', value: '>' },
          { text: '≥', value: '>=' },
          { text: '<', value: '<' },
          { text: '≤', value: '<=' },
          { text: '=', value: '==' },
          // { text: '不等于', value: '!=' },
          { text: this.$t('包含'), value: 'contains' },
          { text: this.$t('不包含'), value: 'not contains' }
          // { text: '集合成员', value: 'memberof' },
          // { text: '不是集合成员', value: 'not memberof' },
          // { text: '正则匹配', value: 'matches' },
          // { text: '正则不匹配', value: 'not matches' },
        ]
      }
    }
  },
  components: {
    MtSvgIcon,
    MtSelect,
    MtInput,
    MtDatePicker,
    MtTimePicker,
    MtDateTimePicker,
    MtMultiSelect,
    MtDropDownTree,
    MtTooltip
  },
  data() {
    return {
      ruleData: this.value,
      randomId: parseInt(Math.random(10000) * 10000),
      groupLeftList: [
        { text: '', value: '' },
        { text: '(', value: '(' }
      ],
      groupRightList: [
        { text: '', value: '' },
        { text: ')', value: ')' }
      ],
      groupLinkList: [
        { text: this.$t('或'), value: 'or' },
        { text: this.$t('且'), value: 'and' }
      ]
      // symbolList: ['相同', '=', '≥', '>', '<', '≤', '空', '非空', '包含', '不包含']
      // symbolList: [
      //   { text: '大于', value: '>' },
      //   { text: '不小于', value: '>=' },
      //   { text: '小于', value: '<' },
      //   { text: '不大于', value: '<=' },
      //   { text: '等于', value: '==' },
      //   { text: '不等于', value: '!=' },
      //   { text: '包含', value: 'contains' },
      //   { text: '不包含', value: 'not contains' },
      //   { text: '集合成员', value: 'memberof' },
      //   { text: '不是集合成员', value: 'not memberof' },
      //   { text: '正则匹配', value: 'matches' },
      //   { text: '正则不匹配', value: 'not matches' },
      // ]
    }
  },
  mounted() {
    const _this = this
    const _checkDom = setInterval(() => {
      if (document.getElementById(`ruleContainer-${_this.randomId}`)) {
        clearInterval(_checkDom)
        Sortable.create(document.getElementById(`ruleContainer-${_this.randomId}`), {
          handle: '.rule-item-handler',
          onEnd: ({ oldIndex, newIndex }) => {
            const _rules = utils.cloneDeep(_this.ruleData)
            const targetRow = _rules.splice(oldIndex, 1)[0]
            _rules.splice(newIndex, 0, targetRow)
            _this.ruleData = []
            _this.$nextTick(function () {
              _this.ruleData = _rules
            })
          }
        })
      } else {
      }
    }, 1000)
  },
  computed: {
    // ruleData: {
    //   get() {
    //     return this.value;
    //   },
    //   set(){}
    // },
    getSymbolList() {
      if (this.type === 'link') {
        return this.symbolList
      } else {
        return [{ text: this.$t('相同'), value: '=' }]
      }
    },
    getDataSourceObject() {
      return (_rule) => {
        const _dataSource = _rule.dataSource
        const _find = this.queryDataSource.filter((e) => {
          return e[this.queryFields.value] === _dataSource
        })
        return _find.length ? _find[0] : {}
      }
    },
    showElementByType() {
      return (rule, type) => {
        const _dataSourceObject = this.getDataSourceObject(rule)
        if (_dataSourceObject && Object.prototype.hasOwnProperty.call(_dataSourceObject, 'type')) {
          return _dataSourceObject.type === type
        } else {
          return type === 'text'
        }
      }
    },
    getElementDataSource() {
      return (rule) => {
        const _dataSourceObject = this.getDataSourceObject(rule)
        if (
          _dataSourceObject &&
          Object.prototype.hasOwnProperty.call(_dataSourceObject, 'source')
        ) {
          return _dataSourceObject.source
        } else {
          return []
        }
      }
    },
    getElementDataFields() {
      return (rule) => {
        const _dataSourceObject = this.getDataSourceObject(rule)
        if (
          _dataSourceObject &&
          Object.prototype.hasOwnProperty.call(_dataSourceObject, 'fields')
        ) {
          return _dataSourceObject.fields
        } else {
          if (
            _dataSourceObject &&
            Object.prototype.hasOwnProperty.call(_dataSourceObject, 'source')
          ) {
            const _source = _dataSourceObject.source
            if (_source.length) {
              if (typeof _source[0] === 'string') {
                return { text: null, value: null }
              } else {
                return { text: 'text', value: 'value' }
              }
            } else {
              return { text: null, value: null }
            }
          } else {
            return { text: null, value: null }
          }
        }
      }
    },
    getElementTreeDataFields() {
      return (rule) => {
        const _dataSourceObject = this.getDataSourceObject(rule)
        if (
          _dataSourceObject &&
          Object.prototype.hasOwnProperty.call(_dataSourceObject, 'fields')
        ) {
          return _dataSourceObject.fields
        } else {
          return { value: 'value', text: 'text', dataSource: [], child: 'child' }
        }
      }
    }
  },
  methods: {
    vaulechange(value, text) {},
    addRuleItem(index) {
      const _rules = utils.cloneDeep(this.ruleData)

      let _checkLink = true
      if (this.type == 'link') {
        _checkLink = this.checkGroupLink(_rules)
      }
      const _checkDataSource = this.checkRuleData(_rules)
      if (_checkLink && _checkDataSource) {
        // 当前规则校验通过，执行添加操作
        const targetRow = {
          groupLeft: '',
          dataSource: '',
          dataValue: '',
          symbol: '=',
          groupRight: '',
          groupLink: 'or'
        }
        _rules.splice(index + 1, 0, targetRow)
        this.ruleData = []
        this.$nextTick(function () {
          this.ruleData = _rules
        })
      }
    },
    removeRuleItem(index) {
      const _rules = utils.cloneDeep(this.ruleData)
      _rules.splice(index, 1)
      this.ruleData = []
      this.$nextTick(function () {
        this.ruleData = _rules
      })
    },
    getValidResult() {
      const _rules = utils.cloneDeep(this.ruleData)
      for (const i in _rules) {
        const _rule = _rules[i]
        if (_rule.dataValue instanceof Date) {
          const _object = this.getDataSourceObject(_rule)
          let _format = 'YYYY-MM-DD'
          if (_object.type === 'time') {
            _format = 'YYYY-MM-DD HH:mm:ss'
          }
          _rule.dataValue = this.dateFormat(_rule.dataValue, _format)
        }
        if (this.type == 'simple') {
          delete _rule.groupLeft
          delete _rule.groupRight
          delete _rule.groupLink
        } else if (+i === _rules.length - 1) {
          _rule.groupLink = ''
        }
      }
      let _checkLink = true
      if (this.type == 'link') {
        _checkLink = this.checkGroupLink(_rules)
      }

      const _checkDataSource = this.checkRuleData(_rules)
      return new Promise((resolve, reject) => {
        if (_checkLink && _checkDataSource) {
          resolve({ rules: _rules })
        } else {
          reject({ rules: _rules })
        }
      })
    },
    checkRuleData(_rules) {
      // 校验当前值，包括dataSource、symbol、dataValue
      let _checkResult = true
      for (const i in _rules) {
        const _rule = _rules[i]
        const _dataSource = _rule.dataSource
        if (!_dataSource || !_dataSource.trim()) {
          // dataSource为空，校验不通过
          _checkResult = false
          this.handleTooltip(i, 'dataSource')
          break
        }

        const _symbol = _rule.symbol
        if (!_symbol || !_symbol.trim()) {
          // symbol为空，校验不通过
          _checkResult = false
          this.handleTooltip(i, 'symbol')
          break
        }

        if (this.type === 'link') {
          // 简约模式下，不校验value值
          const _dataValue = _rule.dataValue
          if (!_dataValue || !_dataValue.trim()) {
            // dataValue为空，校验不通过
            _checkResult = false
            this.handleTooltip(i, 'dataValue')
            break
          }
        }
      }
      return _checkResult
    },
    checkGroupLink(_rules) {
      // 校验括号是否匹配，type='simple'时，忽略
      let _leftLinkIndex = 0
      let _rightLinkIndex = 0
      const _leftLinkArray = []
      const _rightLinkArray = []
      for (const i in _rules) {
        const _rule = _rules[i]
        if (_rule.groupLeft && _rule.groupLeft === '(') {
          _leftLinkIndex = i
          _leftLinkArray.push(i)
        }
        if (_rule.groupRight && _rule.groupRight === ')') {
          _rightLinkIndex = i
          _rightLinkArray.push(i)
        }
      }
      let _checkResult = true
      const _leftLinkCount = _leftLinkArray.length
      const _rightLinkCount = _rightLinkArray.length
      if (_leftLinkCount === _rightLinkCount) {
        if (_leftLinkCount < 1) {
          // 没有'()'， 校验通过
          _checkResult = true
        } else {
          let _check = true
          for (const i in _leftLinkArray) {
            if (_leftLinkArray[i] > _rightLinkArray[i]) {
              _check = false
              break
            }
          }
          if (_check) {
            // ()数量相等，且()依次匹配， 校验通过
            _checkResult = true
          } else {
            // ()数量相等，但是存在()不匹配的， 校验不通过
            this.handleTooltip(_rightLinkArray[0], 'rightLink')
            _checkResult = false
          }
        }
      } else {
        // 有'()' 但是数量不等， 校验不通过
        let _tipIndex = _leftLinkIndex
        let _tipGroup = 'leftLink'
        if (_rightLinkCount > _leftLinkCount) {
          _tipIndex = _rightLinkIndex
          _tipGroup = 'rightLink'
        }
        this.handleTooltip(_tipIndex, _tipGroup)
        _checkResult = false
      }
      return _checkResult
    },
    handleTooltip(index, link, msg = this.$t('规则配置不规范')) {
      const _el = this.$refs[`${link}-${index}`][0]
      this.$ruleTooltip({
        target: _el,
        coordinate: {
          offsetTop: _el?.offsetTop,
          offsetLeft: _el?.offsetLeft,
          offsetWidth: _el?.offsetWidth,
          offsetHeight: _el?.offsetHeight,
          clientWidth: _el?.clientWidth,
          clientHeight: _el?.clientHeight
        },
        content: msg
      })
    },
    handleSelectField(index) {
      setTimeout(() => {
        const _rule = this.ruleData[index]
        const _dataSourceRef = this.$refs['dataSourceRef-' + index]
        const _selectRef = _dataSourceRef[0].$refs.ejsRef
        const _dataSource = _rule.dataSource
        this.ruleData[index].dataValue = ''
      }, 0)
    },
    dateFormat(rawDate, format) {
      if (!rawDate) return ''
      var _date = new Date(rawDate)
      var __Y = _date.getFullYear()
      var __M = _date.getMonth() + 1
      var __D = _date.getDate()
      var __H = _date.getHours()
      var __m = _date.getMinutes()
      var __s = _date.getSeconds()
      /* add possible pre 0 */
      var preM = __M > 9 ? __M.toString() : '0' + __M.toString()
      var preD = __D > 9 ? __D.toString() : '0' + __D.toString()
      var preH = __H > 9 ? __H.toString() : '0' + __H.toString()
      var prem = __m > 9 ? __m.toString() : '0' + __m.toString()
      var pres = __s > 9 ? __s.toString() : '0' + __s.toString()
      var formatted
      switch (format) {
        case 'YYYY-MM-DD HH:mm:ss':
          formatted = __Y + '-' + preM + '-' + preD + ' ' + preH + ':' + prem + ':' + pres
          break
        case 'YYYY-MM-DD':
          formatted = __Y + '-' + preM + '-' + preD
          break
        case 'YYYY/MM/DD HH:mm:ss':
          formatted = __Y + '/' + preM + '/' + preD + ' ' + preH + ':' + prem + ':' + pres
          break
        case 'YYYY/MM/DD':
          formatted = __Y + '/' + preM + '/' + preD
          break
        default:
          formatted = __Y + '-' + preM + '-' + preD
          break
      }
      return formatted
    }
  },
  watch: {
    value: {
      handler(n, o) {
        this.ruleData = n
      },
      deep: true
    }
  }
}
</script>
<style lang="scss" scoped>
.mt-flex {
  display: flex;
  position: relative;
}

.mt-flex-direction-column {
  display: flex;
  flex-direction: column;
  position: relative;
}

.common-rule-config {
  background: #ffffff;

  .rule-item-container {
    background: #ffffff;
    height: 40px;
    width: 670px;
    border-radius: 4px;
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.06);
    margin-top: 10px;

    &:first-of-type {
      margin-top: 0;
    }

    .rule-item {
      display: flex;
      position: relative;
      justify-content: center;
      align-items: center;

      &.split:after {
        content: '';
        height: 20px;
        border-left: 1px solid #e8e8e8;
        position: relative;
        margin: 0 10px;
      }

      .rule-element {
        border: none;
        border-color: transparent !important;
        &:before,
        &:after {
          display: none;
        }
        .e-float-line {
          display: none;
        }
        .e-control {
          color: #292929;
          border: none;
          border-color: transparent !important;
        }
        &.e-date-wrapper,
        &.e-time-wrapper {
          .e-icons {
            display: none;
          }
        }
        .e-ddt-icon,
        .e-ddl-icon {
          &:before {
            content: '';
            position: absolute;
            width: 0;
            height: 0;
            border-top: 4px solid #292929;
            border-bottom: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            left: 50%;
            top: 50%;
          }
        }
      }
    }

    .rule-item-handler {
      background: #dedede;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 4px 0 0 4px;
      cursor: move;
      &:before {
        content: '';
        height: 16px;
        border-left: 1px solid #ffffff;
        margin-left: 3px;
      }
      &:after {
        content: '';
        height: 16px;
        border-left: 1px solid #ffffff;
        margin: 0 3px;
      }

      &:hover {
        background: #6386c1;
      }
    }

    .rule-link {
      .select-container {
        width: 50px;
        .rule-element {
          .e-control {
            text-align: right;
            color: #9a9a9a;
          }
          .e-ddl-icon {
            &:before {
              border-top-color: #9a9a9a;
            }
          }
        }
      }
      &.last-link {
        margin-right: 10px;
        .rule-element {
          .e-control {
            color: #eda133;
          }
          .e-ddl-icon {
            &:before {
              border-top-color: #eda133;
            }
          }
        }
      }
      &.last-item {
        visibility: hidden;
      }
    }

    .rule-symbol {
      .select-container {
        width: 65px;
        .rule-element {
          .e-control {
            text-align: center;
            color: #00469c;
          }
          .e-ddl-icon {
            &:before {
              border-top-color: #00469c;
            }
          }
        }
      }
      &:after {
        visibility: hidden;
      }
    }

    .rule-data-source {
      .select-container {
        width: 100px;
      }
    }

    .rule-data-value {
      min-width: 200px;
      flex: 1;
      &.simple-data-value {
        visibility: hidden;
        &:after {
          visibility: hidden;
        }
      }
    }

    .rule-icons {
      position: absolute;
      top: 10px;
      width: 50px;
      right: -70px;
      justify-content: space-between;
      img {
        height: 20px;
        width: 20px;
        cursor: pointer;
      }
      // .iconfont {
      //   cursor: pointer;
      //   font-size: 20px;
      // }
      // .icon-add-rule {
      //   color: #6386c1;
      // }

      // .icon-remove-rule {
      //   color: #ff4949;
      // }
    }
  }

  .simple-container {
    .rule-data-source {
      .select-container {
        width: 240px;
        .e-control {
          padding-left: 20px;
        }
      }
    }
    .rule-data-value {
      &::after {
        visibility: hidden;
      }
    }
  }
}
//scrollBar-color
$scrollBar-track-color: #ffffff;
$scrollBar-thumb-color: #d8d8d8;
$scrollBar-thumb-hover-color: rgb(200, 200, 200);
$scrollBar-thumb-active-color: rgb(190, 190, 190);
//修改谷歌内核浏览器滚动条样式
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
::-webkit-scrollbar-track {
  border-radius: 2px;
  background-color: $scrollBar-track-color;
}

::-webkit-scrollbar-thumb {
  background-color: $scrollBar-thumb-color;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: $scrollBar-thumb-hover-color;
}

::-webkit-scrollbar-thumb:active {
  background-color: $scrollBar-thumb-hover-color;
}
</style>
