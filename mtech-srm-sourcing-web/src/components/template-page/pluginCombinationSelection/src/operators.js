export const operaterConfig = (t) => {
  const $t = t.bind(this)

  return {
    string: [
      { text: $t('上传人'), value: 'contains' },
      { text: $t('上传技术附件'), value: 'equal' },
      { text: $t('上传时间'), value: 'notequal' }
    ],
    number: [
      { text: $t('等于'), value: 'equal' },
      { text: $t('不等于'), value: 'notequal' },
      { text: $t('大于'), value: 'greaterthan' },
      { text: $t('大于等于'), value: 'greaterthanorequal' },
      { text: $t('小于'), value: 'lessthan' },
      { text: $t('小于等于'), value: 'lessthanorequal' }
    ],
    date: [
      { text: $t('包含'), value: 'contains' },
      { text: $t('等于'), value: 'equal' },
      { text: $t('不等于'), value: 'notequal' },
      { text: $t('大于'), value: 'greaterthan' },
      { text: $t('大于等于'), value: 'greaterthanorequal' },
      { text: $t('小于'), value: 'lessthan' },
      { text: $t('小于等于'), value: 'lessthanorequal' }
    ],
    boolean: [
      { text: $t('等于'), value: 'equal' },
      { text: $t('不等于'), value: 'notequal' }
    ],
    select: [
      { text: $t('等于'), value: 'equal' },
      { text: $t('不等于'), value: 'notequal' },
      { text: $t('包含'), value: 'contains' }
    ]
  }
}
