import { tempRowData } from './tempData.js'
export default {
  name: 'FreeEditor',
  props: {
    scoped: {
      type: Object,
      default: () => {
        return {}
      }
    },
    dataSource: {
      type: Array,
      default: () => {
        return []
      }
    },
    fieldKey: {
      type: String,
      default: ''
    },
    editorRender: {
      type: Function,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      isChange: false
    }
  },
  watch: {
    scoped: {
      handler(obj) {
        for (const key in obj) {
          if (Object.hasOwnProperty.call(obj, key) && key && key !== 'undefined') {
            tempRowData[key] = obj[key]
          }
        }
      },
      deep: true
    }
  },
  render: function (h) {
    return this.editorRender ? this.editorRender(h, this.scoped, this.dataSource) : ''
  }
}
