<template>
  <div>
    <mt-select
      v-if="editorParams.type === 'select'"
      :disabled="editorParams.disabled"
      :fields="editorParams.fields"
      v-model="scoped[fieldKey]"
      float-label-type="Never"
      :allow-filtering="true"
      filter-type="Contains"
      :show-clear-button="editorParams.showClearButton"
      :data-source="editorParams.dataSource"
      :placeholder="editorParams.placeholder || $t('请选择')"
    ></mt-select>
    <mt-date-picker
      v-else-if="editorParams.type === 'datePicker'"
      :open-on-focus="true"
      :show-clear-button="editorParams.showClearButton"
      :allow-edit="false"
      :disabled="editorParams.disabled"
      :format="editorParams.format || 'yyyy-MM-dd'"
      :value-format="editorParams.valueFormat || 'yyyy-MM-dd'"
      v-model="scoped[fieldKey]"
      :placeholder="editorParams.placeholder || $t('请选择')"
    ></mt-date-picker>
    <mt-time-picker
      v-else-if="editorParams.type === 'timePicker'"
      :open-on-focus="true"
      :show-clear-button="editorParams.showClearButton"
      :allow-edit="false"
      :disabled="editorParams.disabled"
      :format="editorParams.format || 'HH:mm:ss'"
      :value-format="editorParams.valueFormat || 'HH:mm:ss'"
      v-model="scoped[fieldKey]"
      :placeholder="editorParams.placeholder || $t('请选择')"
    ></mt-time-picker>
    <mt-date-time-picker
      v-else-if="editorParams.type === 'dateTimePicker'"
      :open-on-focus="true"
      :show-clear-button="editorParams.showClearButtonn"
      :allow-edit="false"
      :disabled="editorParams.disabled"
      :format="editorParams.format || 'yyyy-MM-dd HH:mm:ss'"
      :value-format="editorParams.valueFormat || 'yyyy-MM-dd HH:mm:ss'"
      v-model="scoped[fieldKey]"
      :placeholder="editorParams.placeholder || $t('请选择')"
    ></mt-date-time-picker>
    <mt-input v-else type="text" :disabled="editorParams.disabled" v-model="scoped[fieldKey]" />
  </div>
</template>
<script>
import { tempRowData } from './tempData.js'

export default {
  name: 'ProvidedEditor',
  props: {
    scoped: {
      type: Object,
      default: () => {
        return {}
      }
    },
    dataSource: {
      type: Array,
      default: () => {
        return []
      }
    },
    fieldKey: {
      type: String,
      default: ''
    },
    parentInstance: {
      type: Object,
      default: () => {
        return {}
      }
    },
    editorParams: {
      type: Object,
      default: () => {
        return {}
      }
    },
    testProp: {
      type: Object,
      default: () => {
        return {}
      }
    },
    ejsRef: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      isChange: false
    }
  },
  watch: {
    scoped: {
      handler(obj) {
        for (const key in obj) {
          if (Object.hasOwnProperty.call(obj, key) && key && key !== 'undefined') {
            tempRowData[key] = obj[key]
          }
        }
      },
      deep: true
    }
  }
}
</script>
