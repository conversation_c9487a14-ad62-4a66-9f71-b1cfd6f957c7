<template>
  <mt-dialog
    ref="dialog"
    css-class="small-dialog upload-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="uipload-box" @drop="fileDrop">
      <div id="drop" class="droparea">
        <div class="click-upbox" id="browse">
          <div class="plus-icon"></div>
          <div class="plus-txt">{{ $t('请拖拽文件或点击上传') }}</div>
          <input type="file" class="upload-input" @change="chooseFiles" />
        </div>
        <div class="warn-text">
          <span
            >{{
              $t('注：文件最大不可超过80M；支持类型：')
            }}.xls,.xlsx,.doc,.docx,.jpg,.jpeg,.gif,.rar,.zip,.txt,.pdf,.tiff,.tif,.png,.ico,.pcx,.jfif,.pgp,.svgz,.svg,.webp,.bmp,.pjpeg,.avif,.xbm,.7z,.stream,.ppt,.pptx,.dwg,.stp,.prt；</span
          >
        </div>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        }
        // {
        //   click: this.confirm,
        //   buttonModel: { isPrimary: "true", content:  this.$t("上传") },
        // },
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        width: '500px',
        buttons: this.buttons,
        cssClass: ('small-dialog ' + this.modalData.cssClass).trim()
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    // 兼容火狐拖拽问题
    fileDrop(e) {
      e.preventDefault()
      e.stopPropagation()
      this.chooseFiles(e)
    },
    chooseFiles(event) {
      let _files = event.type === 'drop' ? event.dataTransfer.files : event.target.files
      let params = {
        type: 'array',
        limit: 80 * 1024,
        msg: this.$t('单个文件，限制80M')
      }
      if (_files.length < 1) {
        this.$toast({
          type: 'warning',
          content: this.$t('您未选择需要上传的文件.')
        })
        return
      }
      let _data = new FormData(),
        isOutOfRange = false
      for (let i = 0; i < _files.length; i++) {
        _data.append('UploadFiles', _files[i])
        if (_files[i].size > params.limit * 1024) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$toast({ type: 'warning', content: params.msg })
        return
      }
      this.$store.commit('startLoading')
      this.$API.fileService.uploadPrivateFile(_data).then((res) => {
        this.$store.commit('endLoading')
        this.$emit('confirm-function', res?.data)
      })
    },
    // confirm() {
    //   this.$emit("cancel-function");
    // },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .mt-upload-file {
  display: none;
}
/deep/ .e-dlg-content {
  padding: 10px;
  overflow: hidden;
}
.upload-dialog {
  .e-upload {
    float: none !important;
    border: none !important;
  }
  .e-file-select-wrap {
    display: none !important;
  }
  .e-upload-files {
    background: rgba(250, 250, 250, 1);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 4px;
    padding: 20px;
    margin-top: 20px;

    .e-upload-file-list {
      min-height: 16px !important;
      height: 16px !important;
      line-height: 16px !important;
      border-bottom: none !important;
    }

    .up-loadcontainer {
      .wrapper {
        display: flex;
        align-items: center;
      }
      .file-name {
        width: 80px !important;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-top: 0;
        padding-top: 0;
        color: #292929;
      }
      .upload-size {
        color: #9a9a9a !important;
        padding-right: 10px;
      }
      .upload-status {
        color: #0043a8 !important;
      }
      .upload-success {
        color: #6f982b !important;
      }
      .upload-failed {
        color: #ed5633 !important;
      }
    }
  }

  .uipload-box {
    width: 100%;
    .droparea {
      background: rgba(251, 252, 253, 1);
      border: 1px dashed rgba(232, 232, 232, 1);
      border-radius: 4px;
      margin: 0 auto;
      padding: 19px 0;
      .click-upbox {
        cursor: pointer;
        position: relative;
        .upload-input {
          height: 215%;
          width: 100%;
          position: absolute;
          z-index: 2;
          top: 0;
          background: transparent;
          opacity: 0;
        }
        .plus-icon {
          width: 40px;
          height: 40px;
          margin: 0 auto;
          position: relative;
          &::after {
            content: ' ';
            display: inline-block;
            width: 2px;
            height: 40px;
            background: rgba(232, 232, 232, 1);
            border-radius: 100px;
            position: absolute;
            left: 50%;
            top: 0;
            transform: translateX(-50%);
          }
          &::before {
            content: ' ';
            display: inline-block;
            width: 40px;
            height: 2px;
            background: rgba(232, 232, 232, 1);
            border-radius: 100px;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
          }
        }
        .plus-txt {
          width: 100%;
          text-align: center;
          margin-top: 10px;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(152, 170, 195, 1);
        }
      }
      .warn-text {
        width: 100%;
        text-align: center;
        padding: 0 10px;
        margin: 0 auto;
        margin-top: 10px;
        font-size: 12px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(237, 86, 51, 1);
        word-break: break-all;
      }
    }
  }
}
</style>
