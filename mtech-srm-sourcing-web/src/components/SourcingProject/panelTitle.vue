<template>
  <div class="title-name">
    <div class="left-name">
      <mt-icon :name="iconName"></mt-icon>
      <span class="name">{{ name }}</span>
      <span class="timer" v-if="timer">{{ timer }}</span>
    </div>
    <div class="right-box" v-if="hasSelect">
      <mt-select
        :width="150"
        v-model="thingId"
        :data-source="dataArr"
        :fields="{ value: 'id', text: 'packageName' }"
        css-class="strategy-element"
        :show-clear-button="true"
        :allow-filtering="true"
        placeholder="请选择物料"
        @select="handleSelect"
      ></mt-select>
    </div>
    <!-- <div class="right-link" @click="handleSelectTab">
      <mt-icon name="icon_Packup"></mt-icon>
    </div> -->
  </div>
</template>

<script>
export default {
  props: {
    iconName: {
      type: String,
      default: 'icon_enquiry_Suppliers'
    },
    name: {
      type: String,
      default: '供应商管理'
    },
    timer: {
      type: String,
      default: ''
    },
    tabIndex: {
      type: Number,
      default: 0
    },
    hasSelect: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      thingId: '',
      dataArr: []
    }
  },
  mounted() {
    if (this.hasSelect) this.getPackage()
  },
  methods: {
    handleSelect(e) {
      this.$emit('wuliaoSelect', e.itemData.id)
    },
    // 查看跳转到“询价招标”
    handleSelectTab() {
      this.$bus.$emit('changeTab', this.tabIndex)
    },
    // 获取物料列表---（目前只是在询价大厅-实时排名处使用）
    getPackage() {
      this.$API.rfxExt.getPackageById({ rfxId: this.$route.query.rfxId }).then((res) => {
        this.dataArr = res.data
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.title-name {
  width: 100%;
  height: 60px;
  flex-shrink: 0;
  background: rgba(255, 255, 255, 1);
  border-radius: 6px 6px 0 0;
  box-shadow: 0 0 5px 0 rgba(137, 120, 120, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 10;

  .left-name {
    padding-left: 20px;
    display: flex;
    align-items: center;

    .mt-icons {
      color: #6386c1;
    }
    .name {
      font-size: 20px;
      margin-left: 10px;
      margin-right: 10px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(41, 41, 41, 1);
    }

    .timer {
      font-size: 12px;
      font-family: DINAlternate;
      font-weight: bold;
      color: rgba(154, 154, 154, 1);
    }
  }

  .right-box {
    padding-right: 30px;
  }
  .right-link {
    cursor: pointer;
    padding-right: 15px;
    .mt-icons {
      transform: rotate(-90deg) scale(0.5);
      display: inline-block;
      color: #9baac1;
      font-size: 12px;
    }
  }

  /deep/ .strategy-element {
    border: unset !important;
    border-bottom: 1px solid #e8e8e8 !important;
    position: relative;
    top: 4px;
    &:before,
    &:after {
      display: none;
    }
    .e-float-line {
      display: none;
    }
    .e-control {
      color: #292929;
      border: none;
      border-color: transparent !important;
    }
  }
}
</style>
