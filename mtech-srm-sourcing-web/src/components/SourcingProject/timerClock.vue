<template>
  <div
    :class="{
      'timer-clock-container': true,
      'direction-row': directionRow,
      'no-padding': noPadding
    }"
  >
    <div class="top-wrap">
      <div class="lefts">
        <div class="top-title">
          <div class="breathe-btn breathe-btn-1" :class="[status === 2 ? 'breathing' : '']"></div>
          <div class="breathe-btn breathe-btn-2" :class="[status === 2 ? 'breathing' : '']"></div>
        </div>
        <span class="top-end" style="font-size: 15px">{{ timerString }}</span>
      </div>
      <div class="right" v-if="days > 0">{{ days }}{{ $t('天') }}</div>
    </div>
    <div class="clock">
      <div :class="['digital', `digital_${hour[0]}`]">
        <span class="c1"></span>
        <span class="c2"></span>
        <span class="c3"></span>
        <span class="l1"></span>
        <span class="l2"></span>
        <span class="r1"></span>
        <span class="r2"></span>
      </div>
      <div :class="['digital', `digital_${hour[1]}`]">
        <span class="c1"></span>
        <span class="c2"></span>
        <span class="c3"></span>
        <span class="l1"></span>
        <span class="l2"></span>
        <span class="r1"></span>
        <span class="r2"></span>
      </div>
      <div class="gap"></div>
      <div :class="['digital', `digital_${minute[0]}`]">
        <span class="c1"></span>
        <span class="c2"></span>
        <span class="c3"></span>
        <span class="l1"></span>
        <span class="l2"></span>
        <span class="r1"></span>
        <span class="r2"></span>
      </div>
      <div :class="['digital', `digital_${minute[1]}`]">
        <span class="c1"></span>
        <span class="c2"></span>
        <span class="c3"></span>
        <span class="l1"></span>
        <span class="l2"></span>
        <span class="r1"></span>
        <span class="r2"></span>
      </div>
      <div class="gap"></div>
      <div :class="['digital', `digital_${second[0]}`]">
        <span class="c1"></span>
        <span class="c2"></span>
        <span class="c3"></span>
        <span class="l1"></span>
        <span class="l2"></span>
        <span class="r1"></span>
        <span class="r2"></span>
      </div>
      <div :class="['digital', `digital_${second[1]}`]">
        <span class="c1"></span>
        <span class="c2"></span>
        <span class="c3"></span>
        <span class="l1"></span>
        <span class="l2"></span>
        <span class="r1"></span>
        <span class="r2"></span>
      </div>
    </div>
  </div>
</template>

<script>
// import Decimal from "decimal.js";
export default {
  inject: ['reload'],
  props: {
    directionRow: {
      type: Boolean,
      default: false
    },
    noPadding: {
      type: Boolean,
      default: false
    },
    startTime: {
      type: Number,
      default: 0
    },
    endTime: {
      type: Number,
      default: 0
    },
    timerString: {
      type: String,
      default: ''
    },
    serverTime: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      timer: null,
      hour: [0, 0],
      minute: [0, 0],
      second: [0, 0],
      days: 0,
      status: -1,
      isHidden: false,
      differenceTime: 0
    }
  },
  watch: {
    // startTime() {
    //   if (this.timer) {
    //     clearInterval(this.timer);
    //     this.countDown();
    //   }
    // },
    // endTime() {
    //   if (this.timer) {
    //     clearInterval(this.timer);
    //     this.countDown();
    //   }
    // },
    serverTime() {
      if (this.timer) {
        this.judgeDifference()
        clearInterval(this.timer)
        this.countDown()
      }
    }
  },
  computed: {
    statusString() {
      return [this.$t('报价已结束'), this.$t('报价未开始'), this.$t('报价进行中')][this.status]
    }
  },
  created() {
    this.judgeDifference()
    this.countDown()
    // document.addEventListener("visibilitychange", () => {
    //   if (document.visibilityState == "hidden") {
    //     const now = Date.now();
    //     const diff = Math.floor((this.endTime - now) / 1000);
    //     if (diff > 0) {
    //       this.isHidden = true;
    //     }
    //   } else if (document.visibilityState == "visible") {
    //     this.isHidden = false;
    //   }
    // });
  },
  mounted() {
    if (!Number.isInteger(this.startTime) || !Number.isInteger(this.endTime)) {
      throw new Error('endTime and startTime must be integer')
    }
    if (this.startTime === 0 || this.endTime === 0) {
      return
    }
    const now = new Date().getTime()
    if (this.startTime >= this.endTime) {
      throw new Error('endTime must be greater than startTime')
    }
    if (this.endTime < now) {
      this.status = 0
      return
    }
    if (this.startTime > now) {
      this.status = 1
    }
    // this.countDown();
  },
  methods: {
    judgeDifference() {
      this.differenceTime = this.serverTime - Date.now()
    },
    formatTime(seconds) {
      const t = seconds
      this.days = Math.floor(t / 86400)
      const h = Math.floor((t - this.days * 86400) / 3600)
      // 优化超过一天的倒计时分钟和秒取值
      const m = Math.floor((t - h * 3600 - this.days * 86400) / 60)
      let s = t - h * 3600 - m * 60 - this.days * 86400
      this.hour = h.toString().padStart(2, '0').split('')
      this.minute = m.toString().padStart(2, '0').split('')
      this.second = s.toString().padStart(2, '0').split('')
    },
    countDown() {
      this.timer = setInterval(() => {
        const now = Date.now() + this.differenceTime
        if (this.startTime > now) {
          this.status = 1
        }
        const diff = (this.endTime - now) / 1000
        if (diff < 0 && diff > -1) {
          clearInterval(this.timer)
          this.reload()
        } else if (diff <= -1) {
          this.status = 1
          this.hour = [0, 0]
          this.minute = [0, 0]
          this.second = [0, 0]
        } else {
          this.status = 2
          let _diff = Math.floor(diff)
          this.formatTime(_diff)
        }
      }, 500)
    }
  },
  beforeDestroy() {
    clearInterval(this.timer)
    this.timer = null
  },
  deactivated() {
    // 未激活清除计时器，会导致切换tab页签后，计时器停止，所以仅在销毁时清除
    // clearInterval(this.timer);
    // this.timer = null;
  }
}
</script>

<style lang="scss" scoped>
.timer-clock-container {
  padding: 20px 0px 3px 20px;
  height: 100px;
  display: flex;
  flex-direction: column;

  &.no-padding {
    padding: 0 0 0 0;
    height: auto;
  }

  &.direction-row {
    flex-direction: row;
  }

  .top-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(99, 134, 193, 1);
    margin-bottom: 5px;
    .lefts {
      display: inline-flex;
      align-items: center;
      .top-title {
        width: 20px;
        height: 20px;
        margin-right: 10px;
        position: relative;
        .breathe-btn {
          border-radius: 50%;
          position: absolute;
          background: rgba(99, 134, 193, 1);

          &-1 {
            width: 10px;
            height: 10px;
            left: 5px;
            top: 5px;
          }
          &.breathing {
            animation: breathe1 2s ease-in-out infinite alternate;
          }
          &-2 {
            width: 20px;
            height: 20px;
            left: 0;
            top: 0;
          }
        }
        @keyframes breathe1 {
          0% {
            opacity: 0.3;
            box-shadow: 0 0 20px 0 rgba(99, 134, 193, 1);
          }
          50% {
            opacity: 0.5;
            transform: scale(0.7);
            box-shadow: 0 0 20px 0 rgba(99, 134, 193, 1);
          }
          100% {
            opacity: 0.3;
            box-shadow: 0 0 20px 0 rgba(99, 134, 193, 1);
          }
        }
        @keyframes breathe2 {
          0% {
            opacity: 0.5;
            box-shadow: 0 0 20px 0 rgba(99, 134, 193, 1);
          }
          50% {
            opacity: 0.8;
            transform: scale(0.7);
            box-shadow: 0 0 20px 0 rgba(99, 134, 193, 1);
          }
          100% {
            opacity: 0.5;
            box-shadow: 0 0 20px 0 rgba(99, 134, 193, 1);
          }
        }
      }
    }
  }
  .clock {
    display: flex;
    justify-content: space-around;
    align-items: center;
    color: #00469c;
    width: 200px;

    .digital {
      position: relative;
      width: 20px;
      height: 45px;
      flex-shrink: 0;
    }
    span {
      position: absolute;
    }
    .c1,
    .c2,
    .c3 {
      height: 0;
      width: 15px;
      left: 1px;
      border-left: 2px solid transparent;
      border-right: 2px solid transparent;
    }
    .c1:before,
    .c1:after,
    .c2:before,
    .c2:after,
    .c3:before,
    .c3:after {
      content: '';
      height: 0;
      width: 15px;
      border-left: 2px solid transparent;
      border-right: 2px solid transparent;
    }
    .c1:before,
    .c2:before,
    .c3:before {
      position: absolute;
      top: 0;
      left: 0;
      border-bottom: 2px solid currentColor;
    }
    .c1:after,
    .c2:after,
    .c3:after {
      position: absolute;
      top: 1px;
      border-top: 2px solid currentColor;
    }

    .l1,
    .l2 {
      height: 15px;
      width: 0;
      left: 0;
      border-top: 2px solid transparent;
      border-bottom: 2px solid transparent;
    }

    .r1,
    .r2 {
      height: 15px;
      width: 0;
      right: 0;
      border-top: 2px solid transparent;
      border-bottom: 2px solid transparent;
    }

    .l1,
    .r1 {
      top: 1px;
    }
    .l2,
    .r2 {
      top: 3px;
    }
    .c1 {
      top: 0;
    }
    .c2 {
      top: 17px;
    }
    .c3 {
      bottom: 10px; // 要在范围里
    }

    .l1::before,
    .l1::after,
    .l2::before,
    .l2::after,
    .r1::before,
    .r1::after,
    .r2::before,
    .r2::after {
      content: '';
      width: 0;
      height: 15px;
      position: absolute;
      border-top: 2px solid transparent;
      border-bottom: 2px solid transparent;
      box-sizing: border-box;
    }
    .l1:before,
    .r1:before {
      top: 0;
      left: 0;
      border-right: 2px solid currentColor;
    }
    .l2:before,
    .r2:before {
      top: 15px;
      left: 0;
      border-right: 2px solid currentColor;
    }

    .l1:after,
    .r1:after {
      top: 0;
      left: 1px;
      border-left: 2px solid currentColor;
    }

    .l2:after,
    .r2:after {
      top: 15px;
      left: 1px;
      border-left: 2px solid currentColor;
    }

    .r1:before,
    .r2:before {
      right: 1px;
      left: auto;
    }

    .r1:after,
    .r2:after {
      right: 0;
      left: auto;
    }

    .digital_1 .c1,
    .digital_1 .c2,
    .digital_1 .c3,
    .digital_1 .l1,
    .digital_1 .l2,
    .digital_2 .l1,
    .digital_2 .r2,
    .digital_3 .l1,
    .digital_3 .l2,
    .digital_4 .c1,
    .digital_4 .c3,
    .digital_4 .l2,
    .digital_5 .l2,
    .digital_5 .r1,
    .digital_6 .r1,
    .digital_7 .c2,
    .digital_7 .c3,
    .digital_7 .l1,
    .digital_7 .l2,
    .digital_9 .l2,
    .digital_0 .c2 {
      animation: changeDigital 200ms 0ms 1 ease-in forwards;
    }

    @keyframes changeDigital {
      form {
        opacity: 1;
      }
      to {
        opacity: 0.1;
      }
    }

    .gap {
      width: 9px;
      height: 52px;
      text-align: center;
      position: relative;

      &::before {
        content: '';
        width: 4px;
        height: 4px;
        position: absolute;
        top: 16px;
        transform: rotate(45deg);
        background: #00469c;
      }
      &::after {
        content: '';
        width: 4px;
        height: 4px;
        position: absolute;
        bottom: 16px;
        transform: rotate(45deg);
        background: #00469c;
      }
    }
  }
}
</style>
