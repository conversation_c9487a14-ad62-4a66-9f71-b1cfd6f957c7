<template>
  <div class="select-round">
    <div class="strategy-common-label">{{ $t('当前轮次:') }}</div>
    <div class="strategy-common-container">
      <mt-select
        css-class="strategy-element"
        v-model="roundId"
        :data-source="turnsList"
        :fields="{ value: 'id', text: 'label' }"
        @select="handleSelect"
      ></mt-select>
    </div>
  </div>
</template>

<script>
import utils from '@/utils/utils'
export default {
  data() {
    return {
      roundId: null,
      turnsList: [], // 轮次列表
      currentTurnInfo: null // 当前轮次信息
    }
  },
  mounted() {
    this.getTurns()
    this.getCurrentTurnsInfo()
  },
  methods: {
    getTurns() {
      this.$API.rfxDetail
        .getTurnList({
          rfxId: this.$route.query.rfxId
        })
        .then((res) => {
          this.turnsList = res.data.map((item) => {
            return {
              ...item,
              label: this.$t('第') + utils.toChinesNum(item.roundNo) + this.$t('轮')
            }
          })
        })
    },

    getCurrentTurnsInfo() {
      this.$API.rfxDetail
        .getCurrentTurn({
          rfxId: this.$route.query.rfxId
        })
        .then((res) => {
          this.currentTurnInfo = res.data
          this.roundId = res.data.id
          this.$emit('roundSelect', res.data.id, res.data)
        })
    },

    handleSelect(e) {
      this.$emit('roundSelect', e.itemData.id, e.itemData)
    }
  }
}
</script>

<style lang="scss" scoped>
.select-round {
  display: inline-block;
  .strategy-common-label {
    width: 70px;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: #465b73;
    text-align: right;
    display: inline-block;
    margin-right: 20px;
    position: relative;
  }
  .strategy-common-container {
    width: 120px;
    display: inline-block;
    height: 40px;
    background: #fafafa;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    padding: 0 20px;
  }
  /deep/ .strategy-element {
    border: none;
    border-color: transparent !important;
    position: relative;
    top: 4px;
    &:before,
    &:after {
      display: none;
    }
    .e-float-line {
      display: none;
    }
    .e-control {
      color: #292929;
      border: none;
      border-color: transparent !important;
    }
  }
}
</style>
