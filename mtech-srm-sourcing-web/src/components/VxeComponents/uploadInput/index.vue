<template>
  <div class="upload-file">
    <vxe-input
      :class="{ disabledBg: disabled }"
      v-model="showValue"
      :suffix-icon="'vxe-icon-upload'"
      readonly
      :placeholder="$t(placeholder)"
      @click="handleClick"
    />
    <!-- 附件弹框 -->
    <uploader-dialog @change="change" @confirm="confirm" ref="uploaderDialog"></uploader-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { i18n } from '@/main.js'
import { Input as VxeInput } from 'vxe-table'

// 草稿或审批拒绝状态才能再次上传
export default {
  components: {
    UploaderDialog: () => import('@/components/AgCellComponents/uploaderDialog'), //优化dialog初始加载问题
    VxeInput
  },
  model: {
    prop: 'modelVal',
    event: 'syncChange'
  },
  props: {
    modelVal: {
      type: [String, Number, Array],
      default: null
    },
    disabled: {
      type: <PERSON>olean,
      default: false
    },
    placeholder: {
      type: String,
      default: () => {
        return '请上传文件'
      }
    },
    isImage: {
      type: Boolean,
      default: false
    },
    saveUrl: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogShow: false,
      uploadFileList: [] // 上传的附件
    }
  },
  computed: {
    value: {
      get() {
        return this.modelVal || []
      },
      set(val) {
        this.$emit('syncChange', val)
      }
    },
    showValue() {
      return this.value?.length ? this.$t('文件个数') + this.value?.length : ''
    }
  },
  mounted() {
    this.uploadFileList = cloneDeep(this.value)
  },
  methods: {
    handleClick() {
      this.showUploadDialog('upload')
    },
    // handler - 查看
    view() {
      this.showUploadDialog('view')
    },
    // handler - 上传
    upload() {
      this.showUploadDialog('upload')
    },
    // dialog - show
    showUploadDialog() {
      let dialogParams = {
        fileData: cloneDeep(this.value),
        isView: this.disabled, //是否可上传
        required: false, // 是否必须
        title: i18n.t('附件'),
        saveUrl: this.saveUrl ? this.saveUrl : '/file/user/file/uploadPrivate?useType=2'
      }
      if (this.isImage) {
        dialogParams.accept = ['.png', '.jpg']
        dialogParams.isSingleFile = true
      }
      this.$refs.uploaderDialog?.dialogInit(dialogParams)
    },

    // dialog - hander - change
    change(data) {
      if (this.disabled) return
      this.uploadFileList = data
    },
    // dialog - hander - confirm
    confirm() {
      let _uploadFileList = cloneDeep(this.uploadFileList)
      _uploadFileList.forEach((file) => {
        if (!file.syFileId) {
          file.sysFileId = file.id
          delete file.id
        }
      })
      this.value = _uploadFileList
      this.$refs.uploaderDialog.cancel()
    }
  }
}
</script>
<style lang="scss">
.disabledBg .vxe-input--inner {
  color: #bfbfbf;
  background-color: #f3f3f3 !important;
}
</style>
<style lang="scss" scoped>
::v-deep {
  .vxe-input {
    width: 100%;
  }
}
</style>
