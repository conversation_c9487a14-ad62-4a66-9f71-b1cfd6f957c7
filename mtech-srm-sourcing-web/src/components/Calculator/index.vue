<template>
  <div class="calculator-container">
    <mt-loading v-show="loading"></mt-loading>
    <p class="flex" style="margin: 16px 0">
      <mt-select
        :data-source="sortedItemList"
        :placeholder="$t('选择项目')"
        @change="selectFactor"
      ></mt-select>
      <mt-select
        v-if="showFuncSelect"
        :style="{ 'margin-left': '16px' }"
        :data-source="integrationFormula"
        :fields="{ text: 'name', value: 'expression' }"
        @change="changeIntegrationFormula"
        :placeholder="$t('选择函数')"
      ></mt-select>
    </p>
    <mt-input
      :multiline="true"
      :rows="3"
      ref="inputRef"
      type="text"
      v-model="inputDisplay"
      :placeholder="$t('请输入公式')"
    ></mt-input>
    <p style="margin-bottom: 16px" class="flex operators" @click="clickOperator">
      <mt-button>+</mt-button>
      <mt-button>-</mt-button>
      <mt-button>*</mt-button>
      <mt-button>/</mt-button>
      <mt-button>(</mt-button>
      <mt-button>)</mt-button>
      <mt-button>{{ $t('删除') }}</mt-button>
    </p>
    <p style="text-align: right">
      <mt-button is-primary @click="confirm">{{ $t('确定') }}</mt-button>
    </p>
  </div>
</template>

<script>
// import * as presetFormula from "./presetFormula";
export default {
  name: 'Calculator',
  props: {
    itemList: {
      type: Array,
      required: true
    },
    calVal: {
      type: String,
      default: ''
    },
    // 是否显示函数下拉选择框
    showFuncSelect: {
      type: Boolean,
      default: true
    },
    // 是否对值集排序
    isSortItemList: {
      type: Boolean,
      default: true
    },
    // 是否校验公式
    isValidate: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      inputDisplay: '',
      loading: false,
      integrationFormula: []
    }
  },
  computed: {
    sortedItemList() {
      if (this.isSortItemList) {
        return [...this.itemList].sort((a, b) => b.text.length - a.text.length)
      }
      return this.itemList
    }
  },
  mounted() {
    if (this.calVal) {
      this.inputDisplay = this.calVal
    }
    this.textarea = this.$refs.inputRef.$el.querySelector('textarea')
    this.getFormulaList()
  },
  methods: {
    getFormulaList() {
      this.loading = true
      this.$API.analysis
        .getFormulaList()
        .then((res) => {
          this.integrationFormula = res.data
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    clickOperator(event) {
      if (event.target.tagName === 'BUTTON') {
        const operator = event.target.innerText
        if (operator === this.$t('删除')) {
          this.deleteChar()
        } else {
          this.insertChar(operator)
        }
        this.textarea.focus()
      }
    },
    insertChar(text) {
      const { selectionStart, selectionEnd, value } = this.textarea
      this.textarea.value = value.slice(0, selectionStart) + text + value.slice(selectionEnd)
      this.textarea.selectionStart = selectionEnd + text.length
      this.textarea.selectionEnd = selectionEnd + text.length
    },
    deleteChar() {
      const { selectionStart, selectionEnd, value } = this.textarea
      this.textarea.value = value.slice(0, selectionStart - 1) + value.slice(selectionEnd)
      this.textarea.selectionStart = selectionStart - 1
      this.textarea.selectionEnd = selectionEnd - 1
    },
    confirm() {
      if (!this.textarea.value.trim().length) return
      // let evalStr = this.textarea.value;
      let formula = this.textarea.value
      // let haveAllFactor = true;
      // this.sortedItemList.forEach((item) => {
      //   haveAllFactor = evalStr.includes(item.text);
      //   evalStr = evalStr.replaceAll(item.text, "(3)");
      //   formula = formula.replaceAll(item.text, item.value);
      // });
      // this.integrationFormula.forEach((item) => {
      //   evalStr = evalStr.replaceAll(item.value, item.formula);
      //   formula = formula.replaceAll(item.value, item.formula);
      // });
      // if (!haveAllFactor) return this.$emit("error", "公式须使用全部的因子");

      const factorMap = {}
      this.sortedItemList.forEach((item) => {
        factorMap[item.text] = parseInt(item?.value)
      })

      if (this.isValidate) {
        this.loading = true
        this.$API.analysis
          .checkFormula(formula, factorMap)
          .then(() => {
            this.loading = false
            this.$emit('success', formula)
          })
          .catch((e) => {
            this.loading = false
            this.$emit('error', e.message)
          })
      } else {
        this.$emit('success', formula)
      }

      // try {
      //   eslint-disable-next-line
      //   const { factorial } = presetFormula;
      //   eval(evalStr);
      //   this.$emit("success", formula);
      // } catch (e) {
      //   this.$emit("error", e.message);
      // }
    },
    selectFactor(object) {
      const { selectionStart, selectionEnd, value } = this.textarea
      this.textarea.value =
        value.slice(0, selectionStart) + object.itemData.text + value.slice(selectionEnd)
      this.textarea.selectionStart = selectionEnd + object.itemData.text.length
      this.textarea.selectionEnd = selectionEnd + object.itemData.text.length
      this.textarea.focus()
    },
    changeIntegrationFormula(object) {
      const { value } = this.textarea
      this.textarea.value = object.itemData.expression + '(' + value + ')'
      this.textarea.focus()
    }
  }
}
</script>

<style lang="scss" scoped>
.calculator-container {
  box-shadow: #fff;
  border-radius: 4px;
  background-color: #fff;

  .flex {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .operators /deep/.mt-button > button {
    font-weight: 500;
    font-size: 16px;
  }
}
</style>
