<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <mt-input
        v-if="mode === 'input'"
        v-model="text"
        :placeholder="`请输入${title}`"
        css-class="e-small"
        @change="handleInputChange"
      ></mt-input>
      <mt-select
        v-if="mode === 'select'"
        ref="supplierRef"
        v-model="text"
        :disabled="disabled"
        :allow-filtering="true"
        float-label-type="Never"
        :data-source="dataSourceList"
        :fields="{ text: 'textAndValue', value: config.value }"
        @change="handleSelectChange"
        :filtering="filteringList"
        :placeholder="`请选择${title}`"
      ></mt-select>
      <mt-icon
        style="width: 20px; right: 18px"
        name="icon_list_refuse"
        @click.native="handleClear"
        v-if="!disabled"
      ></mt-icon>
      <mt-icon
        v-if="!disabled"
        style="width: 20px"
        name="icon_input_search"
        @click.native="showDialog"
      ></mt-icon>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import { throttle } from 'lodash'
export default {
  props: {
    mode: {
      type: String,
      required: false,
      default: 'select'
    },
    config: {
      type: Object,
      required: true,
      default: () => {
        return {
          pageConfig: [],
          placehold: '请选择',
          text: 'text',
          value: 'value'
        }
      }
    },
    defaultValue: {
      type: String,
      required: false,
      default: ''
    },
    disabled: {
      type: Boolean,
      required: false,
      default: false
    }
  },
  data() {
    return {
      dataSourceList: [], // 下拉列表
      text: '',
      isShowDialog: false
    }
  },
  mounted() {},
  computed: {
    title() {
      // 设置弹窗title
      let columnData = this.config.pageConfig[0]['grid']['columnData']
      let column = columnData.filter((i) => i.field === this.config.text)
      return column[0]['headerText']
    }
  },
  watch: {
    defaultValue: {
      immediate: true,
      handler(value) {
        if (value) {
          this.dataSourceList = [
            {
              textAndValue: value,
              [this.config.value]: value
            }
          ]
          this.text = value
          this.filteringList({ text: this.text })
        } else {
          this.text = ''
          this.dataSourceList = []
        }
      }
    }
  },
  methods: {
    handleInputChange(e) {
      this.$emit('change', e)
    },
    handleSelectChange(e) {
      let _data = e.itemData
      const fieldValue = this.config.pageConfig[0]['grid']['columnData'][0]['field']
      if (_data && _data[fieldValue] !== this.text) {
        this.$emit('change', _data)
      }
    },
    getParams(text) {
      let params = {
        page: {
          current: 1,
          size: 100
        }
      }
      const rules = []
      // const keys = [this.config.text, this.config.value]
      // const values = this.text
      let columnData = this.config.pageConfig[0]['grid']['columnData']
      for (let i = 0; i < columnData.length; i++) {
        // const element = values[i]
        // if (element || element === 0) {
        //   }
        if (i < 2) {
          const field = columnData[i]?.field
          let obj = {
            field,
            label: '',
            operator: columnData[i].operator || (field.includes('Code') ? 'equal' : 'contains'),
            type: 'string',
            value: text
          }
          rules.push(obj)
        }
      }
      if (rules.length) {
        params.condition = 'or'
        params.rules = rules
      }
      if (
        this.config.pageConfig[0]['grid']['asyncConfig'] &&
        this.config.pageConfig[0]['grid']['asyncConfig']['params']
      ) {
        params = {
          ...params,
          ...this.config.pageConfig[0]['grid']['asyncConfig']['params']
        }
      }
      return params
    },
    filteringList: throttle(function (e) {
      let { text } = e
      if (text) {
        const params = this.getParams(text)
        axios
          .post(`/api${this.config.pageConfig[0]['grid']['asyncConfig']['url']}`, params)
          .then((res) => {
            const { code, data, msg } = res.data
            const fieldValue = this.config.pageConfig[0]['grid']['columnData'][0]['field']
            const fieldText = this.config.pageConfig[0]['grid']['columnData'][1]['field']
            if (code === 200) {
              // this.dataSourceList = data?.records || []
              let records = data?.records || []
              this.dataSourceList = records.map((i) => {
                return {
                  ...i,
                  textAndValue: `${i[fieldValue]}-${i[fieldText]}`
                }
              })
            } else {
              this.$toast({ content: msg, type: 'warning' })
            }
          })
          .catch(() => {
            this.$toast({ content: '系统异常，请稍后再试', type: 'warning' })
          })
      }
    }, 300),
    handleClear() {
      const obj = {}
      obj[this.config.text] = ''
      obj[this.config.value] = ''
      this.$emit('change', obj)
      this.text = null
    },
    showDialog() {
      if (this.disabled) {
        return false
      }
      this.$dialog({
        modal: () => import('./templateDialog.vue'),
        data: {
          title: this.title + '选择',
          pageConfig: this.config.pageConfig
        },
        success: (data) => {
          // 设置输入框显示的值
          this.text = data[this.config.value]
          const fieldValue = this.config.pageConfig[0]['grid']['columnData'][0]['field']
          const fieldText = this.config.pageConfig[0]['grid']['columnData'][1]['field']
          this.dataSourceList = [
            { ...data, textAndValue: `${data[fieldValue]}-${data[fieldText]}` }
          ]
          this.$emit('change', data)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;

    /deep/ .mt-select {
      .e-input-group-icon,
      .e-ddl-icon,
      .e-search-icon {
        margin-right: 40px;
      }
    }

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
      position: absolute;
      top: 8px;
      right: 0;
    }
    // /deep/ .mt-input {
    //   width: 100%;
    //   min-width: 130px;
    // }
    // /deep/ .e-input.e-disabled {
    //   height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
    //   padding-left: 10px !important;
    //   background: #f5f5f5 !important;
    // }
  }
}
</style>
