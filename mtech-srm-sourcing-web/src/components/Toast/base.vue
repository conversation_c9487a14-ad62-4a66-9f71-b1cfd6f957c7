<template>
  <transition name="fade" @after-leave="destroyedToast">
    <mt-toast
      ref="toastRef"
      :position="position"
      :show-close-button="showCloseButton"
      :time-out="timeOut"
    ></mt-toast>
  </transition>
</template>

<script>
export default {
  name: 'Toast',
  props: {
    title: {
      type: String,
      default: '提示'
    },
    content: {
      type: String,
      default: ''
    },
    cssClass: {
      type: String,
      default: 'e-toast-warning'
    },
    icon: {
      type: String,
      default: 'e-warning toast-icons'
    },
    type: { type: String, default: 'warning' },
    position: {
      type: Object,
      default: () => {
        return { X: 'Right', Y: 'Bottom' }
      }
    },
    showCloseButton: {
      type: Boolean,
      default: true
    },
    timeOut: {
      type: Number,
      default: 500000
    }
  },
  data() {
    return {
      style: {
        warning: {
          cssClass: 'e-toast-warning',
          icon: 'e-warning toast-icons'
        },
        success: {
          cssClass: 'e-toast-success',
          icon: 'e-success toast-icons'
        },
        info: {
          cssClass: 'e-toast-info',
          icon: 'e-info toast-icons'
        },
        error: {
          cssClass: 'e-toast-danger',
          icon: 'e-error toast-icons'
        }
      }
    }
  },
  computed: {},
  mounted() {
    this.$refs.toastRef.ejsRef.show({
      title: this.title,
      content: this.content,
      cssClass: this.style[this.type]['cssClass'],
      icon: this.style[this.type]['icon']
    })
  },
  methods: {
    destroyedToast() {
      this.$emit('closed')
    }
  },
  beforeDestroy() {
    this.$emit('closed')
  }
}
</script>

<style lang="scss" scoped></style>
