<template>
  <div class="table-tool-bar">
    <div class="custom-bar">
      <slot name="custom-tools"></slot>
    </div>
    <div class="provided-bar" v-if="isShowRightBtn">
      <slot name="provided-tools">
        <div class="button-group">
          <vxe-button
            style="margin-right: 10px"
            v-show="isShowRefreshBth"
            size="small"
            @click="handleRefresh"
            >{{ $t('刷新') }}</vxe-button
          >
          <vxe-pulldown ref="pulldownRef" :transfer="true" @hide-panel="handleVisibleChange">
            <template #default>
              <vxe-button status="primary" size="small" @click="showPulldown">{{
                $t('设置')
              }}</vxe-button>
            </template>
            <template #dropdown>
              <div class="all-check-area">
                <div class="custom-a-menu-item">
                  <template>
                    <vxe-checkbox
                      v-model="allCheck"
                      :content="$t('全选')"
                      @change="allCheckedChange"
                    ></vxe-checkbox>
                  </template>
                </div>
              </div>
              <vxe-list height="auto" class="col-config-pulldown" :data="cols" auto-resize>
                <template #default="{ items }">
                  <draggable
                    :value="cols"
                    filter=".forbid"
                    class="board-column-content"
                    @end="dragEnd"
                  >
                    <div
                      :class="{
                        forbid: item.fixed,
                        'custom-a-menu-item': item.isShow ? true : false
                      }"
                      v-for="item in items"
                      :key="item.dataIndex"
                    >
                      <template v-if="item.isShow">
                        <vxe-checkbox
                          v-model="item.visible"
                          :disabled="!!item.disabled"
                          @change="(e) => checkedChange(e.checked, item)"
                        ></vxe-checkbox>
                        <span class="col-title">{{
                          item.title || item.headerTitle
                            ? item.title || item.headerTitle
                            : item.dataIndex
                        }}</span>
                      </template>
                    </div>
                  </draggable>
                </template>
              </vxe-list>
            </template>
          </vxe-pulldown>
        </div>
      </slot>
    </div>
  </div>
</template>
<script>
import draggable from 'vuedraggable'
import cloneDeep from 'lodash/cloneDeep'

export default {
  name: 'ColumnConfig',
  components: {
    draggable
  },
  props: {
    columns: {
      type: Array,
      default() {
        return []
      }
    },
    isShowRefreshBth: {
      type: Boolean,
      default: false
    },
    isShowRightBtn: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      cols: [],
      allCheck: false
    }
  },
  watch: {
    columns: {
      handler(list) {
        this.cols = cloneDeep(list)
        this.cols.forEach((item) => {
          this.$set(item, 'visible', item.visible === false ? false : true)
          // 未配置field的列不显示、设置了ignore为true的列不显示
          item.isShow = item.field && !item.ignore
        })
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleRefresh() {
      this.$emit('refresh')
    },
    allCheckedChange(e) {
      this.cols.forEach((item) => {
        if (item.field && item.title) {
          item.visible = e.value
        }
      })
      const list = this.cols.filter((item) => item.visible)
      this.$emit('changeColsVisible', list)
    },
    checkedChange(state, column) {
      const isLastone = this.cols.filter((item) => item.visible).length === 1
      if (isLastone && !state) {
        column.visible = true
        return
      }
      column.visible = state
      const list = this.cols.filter((item) => item.visible)
      this.$emit('changeColsVisible', list)
    },
    dragEnd({ newIndex, oldIndex }) {
      let currRow = this.cols.splice(oldIndex, 1)[0]
      this.cols.splice(newIndex, 0, currRow)
      this.$emit('changeColsVisible', this.cols)
    },
    showPulldown() {
      const $pulldown = this.$refs.pulldownRef
      if ($pulldown) {
        $pulldown.togglePanel()
      }
    },
    handleVisibleChange() {
      // 关闭的时候保存列设置信息
      this.$emit('columnsSequenceChange', this.cols)
    }
  }
}
</script>
<style lang="scss" scoped>
.table-tool-bar {
  height: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .custom-bar {
    height: 100%;
    line-height: 40px;
    margin-left: 8px;
  }
  .provided-bar {
    height: 100%;
    line-height: 40px;
    margin-right: 12px;
  }
}
.all-check-area {
  padding: 8px 16px !important;
  border-bottom: 1px solid #e8e8e8;
}
.col-config-pulldown {
  min-width: 200px;
  padding-bottom: 12px;
  box-shadow: 0 6px 6px 0 rgba(0, 0, 0, 0.06);
  .board-column-content {
    padding: 0 16px 8px 16px !important;
    .custom-a-menu-item {
      height: 30px;
      line-height: 30px;
    }
    .col-title {
      margin-left: 4px;
      cursor: move;
    }
    .forbid > span {
      cursor: not-allowed;
    }
  }
  .custom-a-menu-item:hover {
    background: rgba(90, 146, 173, 0.2);
  }
  ::v-deep {
    .vxe-list--virtual-wrapper,
    .vxe-list--body {
      max-height: 300px !important;
    }
  }
}
</style>
