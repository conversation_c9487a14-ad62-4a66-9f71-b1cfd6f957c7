<template>
  <div class="icon-button" :class="{ disabled: disabled }" :type="type" @click="handleClick">
    <!-- <mt-icon v-if="icon" :name="icon" /> -->
    <span class="icon-button-text">{{ text }}</span>
  </div>
</template>

<script>
export default {
  props: {
    icon: {
      type: String,
      default: ''
    },
    text: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'default'
    }
  },
  methods: {
    handleClick() {
      if (this.disabled) return
      this.$emit('click')
    }
  }
}
</script>

<style lang="scss" scoped>
.disabled {
  cursor: not-allowed !important;
  color: #bfbfbf !important;
  box-shadow: none !important;
  border-color: #ddd !important;
}
.icon-button {
  display: flex;
  align-items: center;
  margin-right: 10px;
  // background: #19a2d5;
  border: 1px solid #2783fe;
  color: #4a556b;
  background: #fff;
  padding: 6px 16px;
  border-radius: 5px;
  box-shadow: 1px 1px 4px 0 #aaa;
  cursor: pointer;
  &-text {
    // margin-left: 4px;
    font-size: 14px;
    font-weight: 400;
    line-height: 1;
    word-break: keep-all;
  }
  &[type='info'] {
    background: #fff;
    color: #4a556b;
    border: 1px solid #4a556b;
  }
  &[type='primary'] {
    background: #4a556b;
    color: #fff;
    border: 1px solid #4a556b;
  }
}
</style>
