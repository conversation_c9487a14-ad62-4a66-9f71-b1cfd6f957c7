<template>
  <mt-dialog
    ref="dialog"
    :header="modalData.dialigInfo.labelName"
    :buttons="buttons"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <!-- 自定义查询条件 -->
      <collapse-search
        class="toggle-container"
        :is-grid-display="true"
        :default-expand="false"
        @reset="handleReset"
        @search="handleSearch"
      >
        <mt-form ref="searchFormRef" :model="searchFormModel">
          <mt-form-item prop="code" :label="modalData.dialigInfo.labelName" label-style="top">
            <mt-input
              v-model="searchFormModel.code"
              :show-clear-button="true"
              :placeholder="$t(`请输入${modalData.dialigInfo.labelName}`)"
            />
          </mt-form-item>
        </mt-form>
      </collapse-search>
      <!-- 数据table -->
      <ScTable ref="sctableRef" :columns="columns" :table-data="tableData" />
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'
import CollapseSearch from '@/components/collapseSearch'

export default {
  components: { CollapseSearch, ScTable },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      searchFormModel: {},
      tableData: [],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    columns() {
      const columns = [
        { type: 'checkbox', width: 50 },
        {
          field: 'value',
          title: this.modalData.dialigInfo.labelName
        }
      ]
      return columns
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.init()
  },
  methods: {
    init() {
      this.tableData = this.modalData.dialigInfo.tableData
      const selectedItems = this.modalData.dialigInfo.selectedItems
      this.$nextTick(() => {
        this.tableData.forEach((row) => {
          const isSelected = selectedItems.find((val) => val == row.value)
          isSelected && this.$refs.sctableRef.$refs.xGrid.setCheckboxRow(row, true)
        })
      })
    },
    // 查询
    handleSearch() {
      const dataList = this.modalData.dialigInfo.tableData
      this.tableData = dataList.filter((row) => row.value?.includes(this.searchFormModel.code))
      this.$refs.sctableRef.$refs.xGrid.setAllCheckboxRow(false)
    },
    // 重置
    handleReset() {
      this.$set(this.searchFormModel, 'code', null)
      this.tableData = this.modalData.dialigInfo.tableData
      this.$refs.sctableRef.$refs.xGrid.setAllCheckboxRow(false)
    },
    // 确认
    confirm() {
      const selectedRecords = this.$refs.sctableRef.$refs.xGrid.getCheckboxRecords(true)
      if (selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请至少选择一行数据'), type: 'warning' })
        return
      }
      this.$emit('confirm-function', selectedRecords)
    },
    // 取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding: 5px 0 0 0;
  height: 100%;
  width: 100%;
  .collapse-search-area {
    padding: 0;
  }
  .mt-pagertemplate {
    margin: 10px 0 0 0;
  }
}
</style>
