<template>
  <!-- 自定义选择框：可通过输入自动生成选项 -->
  <div class="custom-select-container">
    <div class="in-cell">
      <vxe-pulldown ref="pulldownRef" v-model="pulldownShow" destroy-on-close transfer>
        <template #default>
          <vxe-input
            ref="inputRef"
            v-model="text"
            :placeholder="placeholder"
            autocomplete="off"
            @input="handleInput"
            @focus="handleToggle"
            @prefix-click="showDialog"
          />
        </template>
        <template #dropdown>
          <div class="my-dropdown">
            <vxe-list class="my-dropdown2" :data="dataSourceList" auto-resize height="auto">
              <template #default="{ items }">
                <div v-if="items.length">
                  <div class="list-item2" v-for="item in items" :key="item.value">
                    <vxe-checkbox
                      v-model="item.isChecked"
                      size="medium"
                      :checked-value="true"
                      @change="handleCheck"
                    >
                    </vxe-checkbox>
                    <span :class="{ isSelected: isSelected(item) }">{{ item.text }}</span>
                  </div>
                </div>
                <div v-else class="empty-tip">
                  {{ $t('暂无数据') }}
                </div>
              </template>
            </vxe-list>
          </div>
        </template>
      </vxe-pulldown>
      <mt-icon
        v-if="!disabled"
        style="width: 20px; right: 18px"
        name="icon_list_refuse"
        @click.native="handleClear"
      />
      <mt-icon
        v-if="!disabled"
        style="width: 20px"
        name="icon_input_search"
        @click.native="showDialog"
      />
    </div>
  </div>
</template>

<script>
// import MtListbox from '@mtech-ui/listbox'
import {
  Pulldown as VxePulldown,
  Input as VxeInput,
  List as VxeList,
  Checkbox as VxeCheckbox
} from 'vxe-table'
import debounce from 'lodash.debounce'

export default {
  components: { VxeList, VxePulldown, VxeInput, VxeCheckbox },
  model: {
    prop: 'value',
    event: 'syncValue'
  },
  props: {
    value: {
      type: Array,
      default: () => []
    },
    labelName: {
      type: String,
      default: '编码'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: ''
    }
  },
  watch: {
    value: {
      handler(val) {
        if (!val) {
          this.text = null
          this.selectedList = []
        }
      },
      deep: true
    }
  },
  filters: {
    isChecked(value) {
      return this.selectedList?.includes(value)
    }
  },
  data() {
    return {
      text: '',
      selectedList: [],
      dataSourceList: [], // 下拉列表
      selectionSettings: {
        mode: 'Single',
        showCheckbox: true,
        showSelectAll: true
      },
      showSelect: false,
      pulldownShow: true
    }
  },
  computed: {},
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.text = this.value?.join(' ') || null
      this.selectedList = this.value || []
      // 去重
      const arr = [...new Set(this.value)]
      // 生成选项
      const result = arr.map((val) => {
        return { text: val, value: val, isChecked: false }
      })
      this.dataSourceList = result
    },
    handleToggle() {
      this.$refs.pulldownRef?.showPanel()
    },
    // 清除
    handleClear() {
      this.handleSelect({ value: [] })
      this.$refs.pulldownRef?.showPanel()
    },
    // 输入
    handleInput: debounce(function (e) {
      let str = e.value
      if (!str) {
        this.handleSelect({ value: [] })
        return
      }
      let tempArr = []
      tempArr = str.split(' ')
      // 去除重复的元素
      const arr = [...new Set(tempArr)]
      // 生成选项
      const result = arr.map((val) => {
        return { text: val, value: val, isChecked: true }
      })
      this.dataSourceList = result
      // 默认全选
      this.handleSelect({ value: arr })
    }, 800),
    // checkbox监听
    handleCheck() {
      let arr = this.dataSourceList.filter((option) => option.isChecked).map((item) => item.value)
      this.handleSelect({ value: arr })
    },
    // 选择
    handleSelect(e) {
      this.selectedList = e.value
      this.text = this.selectedList.join(' ')
      this.$emit('syncValue', this.selectedList)
      this.$emit('change', this.selectedList)
      this.updateDataSource()
    },
    // 点击弹窗
    showDialog() {
      this.$dialog({
        modal: () => import('./components/detailDialog.vue'),
        data: {
          dialigInfo: {
            labelName: this.labelName,
            tableData: this.dataSourceList,
            selectedItems: this.selectedList
          }
        },
        success: (list) => {
          const selectedItems = []
          list.forEach((item) => selectedItems.push(item.value))
          this.handleSelect({ value: selectedItems })
        }
      })
    },
    isSelected(option) {
      return this.selectedList?.some((item) => item === option.value)
    },
    // 根据selectedList更新dataSourceList
    updateDataSource() {
      this.dataSourceList.forEach((item) => {
        item.isChecked = this.selectedList.includes(item.value)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.vxe-pulldown {
  width: 100%;
}
.custom-select-container {
  display: flex;
  .in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    margin-bottom: 4px;
  }
}

.my-dropdown {
  position: absolute;
  width: 100%;
  max-height: 300px;
  overflow-x: hidden;
  overflow-y: auto;
  z-index: 99;
  background-color: #fff;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
}

::v-deep {
  .e-filter-parent {
    box-shadow: none;
  }
  .e-list-item {
    line-height: 22px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    padding: 0 4px;
  }
  .vxe-input {
    height: 22px;
    line-height: 22px;
    width: 100%;
    .vxe-input--inner {
      border: none;
      border-radius: 0;
      background-color: inherit;
      border-bottom: 1px solid rgba(0, 0, 0, 0.42);
      padding: 0;
      box-sizing: unset;
    }
  }
  .vxe-input:not(.is--disabled).is--active .vxe-input--inner {
    border: none;
    border-bottom: 2px solid #000;
  }
}
.my-dropdown2 {
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 4px 0;
  max-height: 200px;
  border-radius: 4px;
  border: 1px solid #dadce0;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}
.list-item2 {
  line-height: 30px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 0 4px;

  .isSelected {
    font-weight: 700;
    color: #409eff;
  }
}
.list-item2:hover {
  background-color: #f5f7fa;
}
.empty-tip {
  width: 100%;
  line-height: 30px;
  padding: 0 4px;
  text-align: center;
  color: #c0c4cc;
}
</style>
<style lang="scss">
@import 'vxe-table/styles/variable.scss';
@import 'vxe-table/styles/input.scss';
@import 'vxe-table/styles/pulldown.scss';
</style>
