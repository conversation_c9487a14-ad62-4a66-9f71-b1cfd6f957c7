<template>
  <div>
    <div class="rank" @click.stop="dialogShow">{{ $t('排名') }}</div>
    <mt-dialog ref="dialog" :header="header">
      <div class="dialog-content">
        <mt-template-page ref="templateRef" :template-config="pageConfig" />
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import { supplierPageConfig } from './config'
export default {
  data() {
    return {
      header: this.$t('排名'),
      pageConfig: supplierPageConfig
    }
  },
  methods: {
    dialogShow() {
      this.$refs['dialog'].ejsRef.show()
      this.getSupplierRankData()
    },
    // 获取列表数据
    getSupplierRankData() {
      let params = {
        rfxId: this.$route?.query?.rfxId,
        rfxItemId: this.data?.rfxItemId
      }
      this.$API.rfxStatistic.rank(params).then((res) => {
        if (res.code === 200) {
          const _res = res.data
          this.$set(this.pageConfig[0].grid, 'dataSource', _res)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
.rank {
  cursor: pointer;
  color: #00469c;
}
</style>
