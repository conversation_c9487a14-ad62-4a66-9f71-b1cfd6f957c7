<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div style="padding-top: 18px">
      <mt-form ref="form" :model="approveForm" :rules="formRules">
        <mt-form-item style="width: 100%" :label="labelName" prop="inputContent">
          <mt-input type="text" :multiline="true" :rows="10" v-model="approveForm.inputContent" />
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
export default {
  props: {
    // 父组件传值 数据集合
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确认') }
        },
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        }
      ],
      approveForm: {
        inputContent: ''
      }
    }
  },
  computed: {
    header() {
      let title = this.$t('失效提示')
      if (this.modalData.title) {
        title = this.modalData.title
      }
      return title
    },
    formRules() {
      let labelName = this.$t('失效原因')
      if (this.modalData.labelName) {
        labelName = this.modalData.labelName
      }
      let required = true
      if (this.modalData.required === false) {
        required = this.modalData.required
      }
      return {
        inputContent: [{ required, message: `请输入${labelName}`, trigger: 'blur' }]
      }
    },
    labelName() {
      let labelName = this.$t('失效原因')
      if (this.modalData.labelName) {
        labelName = this.modalData.labelName
      }
      return labelName
    }
  },
  methods: {
    save() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('confirm-function', this.approveForm) // 确认
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.inputContent) {
      this.$set(this.approveForm, 'inputContent', this.modalData.inputContent)
    }
  }
}
</script>
<style lang="scss">
.dialog-content {
  padding: 32px 6px 0 6px !important;
}
/deep/ .mt-date-picker > .e-input-group {
  line-height: 32px;
}
/deep/ .icon-style {
  margin: -14px 0 0 42px;
  position: absolute;
}
/deep/ #describe {
  width: 100%;
}
</style>
