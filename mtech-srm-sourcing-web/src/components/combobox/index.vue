<template>
  <div class="combobox">
    <ejs-combobox
      id="combobox"
      ref="comboboxRef"
      v-bind="$attrs"
      v-model="value"
      :data-source="dataSource"
      :fields="fields"
      @change="handleChange"
      @blur="handleBlur"
    ></ejs-combobox>
  </div>
</template>

<script>
import Vue from 'vue'
import { ComboBoxPlugin } from '@syncfusion/ej2-vue-dropdowns'
Vue.use(ComboBoxPlugin)
export default {
  name: 'Combobox',
  model: {
    prop: 'modelVal',
    event: 'syncChange'
  },
  props: {
    type: {
      type: String,
      default: ''
    },
    modelVal: {
      type: [String, Number],
      required: false,
      default: ''
    },
    dataSource: {
      type: Array,
      required: true,
      default: () => {
        return []
      }
    },
    fields: {
      type: Object,
      required: false,
      default: () => {
        return {
          text: 'text',
          value: 'value',
          iconCss: 'iconCss'
        }
      }
    }
  },
  data() {
    return {
      ejsValue: ''
    }
  },
  computed: {
    value: {
      get() {
        let newVal = ''
        if (this.type === 'number') {
          newVal = !this.modelVal || isNaN(this.modelVal) ? '' : Number(this.modelVal)
        } else {
          newVal = this.modelVal
        }
        return newVal
      },
      set(val) {
        this.$emit('syncChange', val)
      }
    }
  },
  methods: {
    /** events */
    handleChange(event) {
      this.$emit('change', event)
    },
    handleBlur() {
      this.$emit('blur', this.value)
    }
  }
}
</script>
