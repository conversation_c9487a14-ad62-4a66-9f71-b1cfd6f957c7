<template>
  <div class="grid-container" style="flex: 1; display: flex; flex-direction: column">
    <!-- 自定义查询区 -->
    <custom-search
      v-if="searchConfig.length"
      :params-type="paramsType"
      :search-config="searchConfig"
      @search="search"
    >
    </custom-search>
    <!-- 自定义工具栏 -->
    <custom-toolbar
      :toolbar="toolbar"
      :columns="columnsConfig"
      :selected-columns="selectedColumns"
      @clickToolbar="handleClickToolbar"
      @refresh="refresh"
      @visibleColumnsChanged="visibleColumnsChanged"
      @saveConfig="columnInfoChange"
    ></custom-toolbar>
    <!-- 自定义头部区域 -->
    <slot name="header"></slot>
    <!-- aggrid表格 -->
    <ag-grid-vue
      class="ag-theme-alpine"
      :row-data="rowData"
      :style="{ width: width }"
      :header-height="headerHeight"
      :row-height="rowHeight"
      :suppress-menu-hide="true"
      :modules="modules"
      :column-defs="columnsConfig"
      :default-col-def="defColCfg"
      :enable-range-selection="true"
      :row-selection="rowSelection"
      :status-bar="statusBarCfg"
      :suppress-agg-func-in-header="true"
      :suppress-row-click-selection="true"
      :single-click-edit="isSingleClickEdit"
      :debounce-vertical-scrollbar="true"
      :stop-editing-when-cells-lose-focus="isStopEditingOnBlur"
      :process-data-from-clipboard="processDataFromClipboard"
      :process-cell-for-clipboard="processCellForClipboard"
      :excel-styles="excelStyles"
      :suppress-scroll-on-new-data="isScrollNewData"
      v-bind="$attrs"
      :get-main-menu-items="getMainMenuItems"
      :locale-text="localeText"
      v-on="$listeners"
      @grid-ready="gridReady"
      @grid-size-changed="gridSizeChanged"
      @cell-value-changed="cellValueChanged"
      @rowDataChanged="rowDataChanged"
      @displayedColumnsChanged="displayedColumnsChanged"
      @filter-changed="onFilterChanged"
    />
    <!-- :side-bar="sideBar" -->
    <!-- 分页 -->
    <!-- <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    /> -->
  </div>
</template>

<script>
import { AllModules } from '@ag-grid-enterprise/all-modules'
import { AgGridVue } from 'ag-grid-vue'
// import { AgGridVue } from '@ag-grid-community/vue'
import CustomRender from './customRender.vue'
import CustomEdit from './customEdit'
import CustomHeader from './customHeader.vue'
import CustomToolbar from './customToolbar.vue'
import CustomSearch from './customSearch.vue'
import { valueMap } from '@/utils/utils'
import { recombineCols } from './tools'
import { cloneDeep, merge } from 'lodash'
import { API } from '@mtech-common/http'
export default {
  name: 'App',
  components: {
    AgGridVue,
    CustomToolbar,
    CustomSearch: CustomSearch,
    // eslint-disable-next-line
    CustomRender,
    // eslint-disable-next-line
    CustomEdit,
    // eslint-disable-next-line
    agColumnHeader: CustomHeader
  },
  props: {
    gridId: {
      type: String,
      default: null
    },
    rowData: {
      type: Array,
      default: null
    },
    total: {
      type: Number,
      default: 0
    },
    /** 宽度 */
    width: {
      type: String,
      default: '100%'
    },
    /** 高度 */
    height: {
      type: String,
      default: '500px'
    },
    /** 列配置 */
    columns: {
      type: Array,
      default: () => []
    },
    /** 工具栏配置 */
    toolbar: {
      type: Array,
      default: () => []
    },
    /** 是否在鼠标点击到表格外面的时候结束编辑状态，注意：编辑单元格时如果是自定义的含弹窗的组件也会被判断为鼠标点击了表格之外，
     * 比如自定义下拉组件，日历组件等，一定要将改属性设为false
     */
    isStopEditingOnBlur: {
      type: Boolean,
      default: true
    },
    isSingleClickEdit: {
      type: Boolean,
      default: true
    },
    /** 默认列配置, 以merge方式合并 */
    defaultColConfig: {
      type: Object,
      default: () => {}
    },
    /** 行选择：single - 单选；multiple - 多选 */
    rowSelection: {
      type: String,
      default: 'multiple'
    },
    /** 表格底部汇总栏 */
    statusBar: {
      type: Object,
      default: null
    },
    /** 侧边栏 */
    sideBar: {
      type: Object,
      default() {
        return {
          toolPanels: [
            {
              id: 'columns',
              labelDefault: 'Columns',
              labelKey: 'columns',
              iconKey: 'columns',
              toolPanel: 'agColumnsToolPanel',
              toolPanelParams: {
                suppressValues: true,
                suppressPivots: true,
                suppressPivotMode: true
              }
            },
            {
              id: 'filters',
              labelDefault: 'Filters',
              labelKey: 'filters',
              iconKey: 'filter',
              toolPanel: 'agFiltersToolPanel'
            }
          ]
        }
      }
    },
    // /** 表格顶部分组（group）栏是否显示 */
    // rowPanelShow: {
    //   type: String,
    //   default: 'always'
    // },
    /** 用户编辑数据 */
    editData: {
      type: Object,
      default: null
    },
    /** 是否开启自适应高度，默认开启 */
    isAdaptiveHeight: {
      type: Boolean,
      default: true
    },
    /** 是否开启默认宽度 */
    isDefaultWidth: {
      type: Boolean,
      default: true
    },
    /** 是否开启宽度平铺 */
    isColumnsFit: {
      type: Boolean,
      default: true
    },
    /** 是否开启分页 */
    isPagination: {
      type: Boolean,
      default: false
    },
    /** excel 模板style*/
    excelStyles: {
      type: Array,
      default() {
        return [
          {
            id: 'stringType',
            dataType: 'String'
          }
        ]
      }
    },
    /** 是否编辑rowData刷新数据不滚动*/
    isScrollNewData: {
      type: Boolean,
      default: true
    },
    /** 搜索栏入参类型*/
    paramsType: {
      type: Number,
      default: () => 1
    },
    /** 搜索栏配置*/
    searchConfig: {
      type: Array,
      default: () => []
    },
    /**行高 */
    rowHeight: {
      type: Number,
      default: 32
    },
    /** 显示底部*/
    showStatusbar: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      modules: AllModules,
      gridApi: null,
      gridColumnApi: null,
      headerHeight: 40,
      pasteSelectColList: [], // 从excel复制粘贴内容到下拉选择列时做处理
      localeText: {
        //此处后续需要翻译处理
        noRowsToShow: this.$t('无记录可显示'),
        totalAndFilteredRows: this.$t('条数'),
        totalRows: this.$t('总条数')
      },
      saveUserMemoryUrl: '/lowcodeWeb/tenant/user-memory/save',
      getUserMemoryUrl: '/lowcodeWeb/tenant/user-memory/get',
      memoryInfo: null,
      selectedColumns: [],
      pageInfo: {
        size: 1000,
        current: 1
      },
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 1000,
        pageSizes: [20, 50, 200, 500, 1000, 2000]
      }
    }
  },
  computed: {
    commonColCfg() {
      // 公共列配置，会议开发者传入的列配置进行merge
      return {
        filter: true,
        resizable: true,
        sortable: true,
        width: this.isDefaultWidth ? window.defColWidth : '', // 如果针对有dictionary的列会导致初始时候不会自动适应
        menuTabs: ['filterMenuTab', 'generalMenuTab'],
        headerComponentParams: {
          menuIcon: 'icon-icon_shaixuan_16_def-1', // 标题菜单icon
          unsortIcon: 'icon-icon_paixu_16_def' // 默认排序icon
        }
      }
    },
    columnsConfig() {
      // eslint-disable-next-line
      this.pasteSelectColList = JSON.parse(
        JSON.stringify(this.columns.filter((item) => item.pasteBehavior === 'translate') || [])
      )
      this.formateFilterList()
      let _columns = cloneDeep(this.columns)
      return recombineCols(_columns, this)
    },
    defColCfg() {
      return merge(this.commonColCfg, this.defaultColConfig)
    },
    cellEditData() {
      return this.editData || {}
    },
    statusBarCfg() {
      return {
        statusPanels:
          this.isPagination || !this.showStatusbar
            ? [
                // { statusPanel: 'agTotalRowCountComponent', align: 'left' },
                // { statusPanel: 'CustomStatusBar' }
              ]
            : [
                { statusPanel: 'agTotalAndFilteredRowCountComponent', align: 'left' },
                { statusPanel: 'agTotalRowCountComponent', align: 'center' },
                { statusPanel: 'agFilteredRowCountComponent' },
                { statusPanel: 'agSelectedRowCountComponent' },
                { statusPanel: 'agAggregationComponent' }
              ]
      }
    }
  },
  watch: {
    total: {
      handler(val) {
        if (val) {
          // this.pageSettings.totalPages = Math.ceil(Number(val) / this.pageSettings.pageSize)
          // this.pageSettings.totalRecordsCount = Number(val)
        }
      },
      immediate: true
    }
  },
  async mounted() {
    if (this.gridId) await this.getMemoryInfo()
  },
  methods: {
    handleClickToolbar(args) {
      this.$emit('handleClickToolbar', args)
    },
    gridReady(params) {
      this.gridApi = params.api
      this.gridColumnApi = params.columnApi
      // this.gridColumnApi.autoSizeAllColumns() // 当在列数表较少的表格实例中使用this.gridApi.sizeColumnsToFit()覆盖autoSizeAllColumns()的效果
      this.gridApi.expandAll()
      /** 其他用户可以通过这个事件执行自己想要在grid-ready钩子回调里进行的操作 */
      this.$emit('onGridReady', params)
    },
    // 监听rowData是否有更新，也即从新重后台获取数据，如果有更新数据则清楚所有过滤设置
    rowDataChanged(params) {
      params.api?.setFilterModel(null)
      this.$emit('onRowDataChanged', params)
    },
    // 设置列显示隐藏
    visibleColumnsChanged(columns) {
      const _hidden = []
      this.columnsConfig.forEach((item) => {
        if (!columns.includes(item.field)) {
          _hidden.push(item.field)
        }
      })
      this.gridColumnApi.setColumnsVisible(columns, true)
      this.gridColumnApi.setColumnsVisible(_hidden, false)
    },
    displayedColumnsChanged() {
      let _columns = this.gridColumnApi?.getAllDisplayedColumns() || []
      this.selectedColumns = _columns.map((item) => item.colId)
    },
    // 获取记忆信息
    async getMemoryInfo() {
      if (!this.gridId) {
        return
      }
      const res = await API.get(this.getUserMemoryUrl, { gridId: this.gridId })
      if (res.code === 200) {
        this.memoryInfo = res.data?.gridMemory || {}
      }
    },
    // 保存记忆信息
    async savetMemoryInfo(gridMemory) {
      if (!this.gridId) {
        return
      }
      const res = await API.post(this.saveUserMemoryUrl, { gridId: this.gridId, gridMemory })
      if (res.code === 200) {
        this.memoryInfo = res.data?.gridMemory || {}
      }
    },
    // 列信息变化 - 保存
    columnInfoChange() {
      const allColumns = this.gridColumnApi.getAllColumns()?.map((item) => {
        return {
          actualWidth: item.actualWidth,
          colId: item.colId,
          visible: item.visible
        }
      })
      let gridMemry = {
        gridId: this.gridId,
        cols: allColumns
      }
      this.savetMemoryInfo(gridMemry)
    },
    // 列宽变化 - 保存
    columnsWidthChange() {},
    // 拼接保存接口提交数据
    mergeGridMemory() {},
    // 处理右边滚动列左边框无法对其齐头部左边框的问题
    handleRightColumnWidth() {
      setTimeout(() => {
        const containerTableHeight = this.$el.querySelector(
          '.ag-center-cols-container'
        ).offsetHeight
        const viewableHeight = this.$el.querySelector('.ag-body-viewport').offsetHeight
        const pinned_right = this.$el.querySelector('.ag-pinned-right-header')
        const pinned_right_children_wid = this.$el.querySelector(
          '.ag-pinned-right-header>.ag-header-row-column'
        ).offsetWidth
        // 表格右边有固定列并且行数据高度大于表格高度造成的滚动条去做宽度处理
        pinned_right_children_wid &&
          containerTableHeight > viewableHeight &&
          (pinned_right.style.minWidth = pinned_right.style.width =
            pinned_right_children_wid + 8 + 'px')
      }, 300)
    },
    gridSizeChanged(params) {
      // 还原滚动条位置
      if (this.$route.meta.keepAlive) {
        this.$el.querySelector('.ag-body-horizontal-scroll-viewport').scrollLeft =
          this.$route.meta?.left || 0
        this.$route.meta?.rowIndex &&
          this.gridApi.ensureIndexVisible(this.$route.meta.rowIndex, 'top')
      }
      this.$emit('onGridSizeChanged', params)
    },
    cellValueChanged(params) {
      // 记录更新数据
      if (params.data.id && this.editData) {
        this.$set(this.cellEditData, params.data.id, params.data)
        this.$emit('update:editData', this.cellEditData)
      }
      this.$emit('cellValueChanged', params)
    },
    // 处理excel粘贴最后一行多余问题（如果针对aggrid自带表格中 复制一列，最后一行是空行的情况，目前给处理了）
    processDataFromClipboard(params) {
      if (!Array.isArray(params.data)) return null
      const _len = params.data.length
      const _lastItem = params.data[_len - 1]
      let _data = []
      if (Array.isArray(_lastItem) && _lastItem.length === 1 && !_lastItem[0]) {
        _data = params.data.slice(0, -1)
      } else {
        _data = params.data
      }
      this.$emit('processDataFromClipboard', _data)
      return _data
    },
    processCellForClipboard(params) {
      let _colDef = params.column.getColDef()
      if (_colDef.valueFormatter) {
        return _colDef.valueFormatter({
          ...params,
          data: params.node?.data,
          colDef: _colDef
        })
      }
      return params.value
    },
    // 解决过滤器显示的列表值与表中数据不一致的问题
    formateFilterList() {
      this.columns.map((item) => {
        // 设置option方式实现下拉选择列
        if (item.dictionary) {
          item.filterParams = {
            valueFormatter: (params) => {
              return valueMap(item.dictionary, params.value, 'label', 'value', 'weak')
            }
          }
        }
        // 自定义editor方式实现下拉选择列
        if (item.cellEditorParams && item.cellEditorParams.dictionary) {
          item.filterParams = {
            valueFormatter: (params) => {
              return valueMap(
                item.cellEditorParams.dictionary,
                params.value,
                'label',
                'value',
                'weak'
              )
            }
          }
        }
      })
    },
    // 只保留固定列选项
    getMainMenuItems(params) {
      const findItem = params.defaultItems.find((item) => item === 'pinSubMenu')
      return [findItem]
    },
    getRowData() {
      // 针对applyTransaction调用
      const rowData = []
      this.gridApi.forEachNode(function (node) {
        rowData.push(node.data)
      })
      return rowData
    },
    /**
     * Common Delete Async Function
     * @param {String} url 删除接口连接
     * @param {String} method 删除接口请求方式 默认delete
     * @param {string} type 提交数据类型 1=>传整行数据 2=>自定义数据 默认传需要删除额id
     * @param {Array} data 自定义提交的数据(设置type=2时生效)
     * @returns {Promise} rowData
     */
    async deleteRowData(params) {
      const rowSelections = this.gridApi.getSelectedRows()
      const addRows = [] // 删除新增数据
      const backRows = [] // 删除数据库已有数据
      rowSelections.map((item) => {
        // 处理数据
        item.id && backRows.push(item)
        !item.id && addRows.push(item)
      })

      // 1.如果只有新增数据的情况直接删除
      if (addRows.length && !backRows.length) {
        this.gridApi.applyTransaction({ remove: addRows })
        return this.getRowData()
      }

      // 2.存在删除数据库数据
      let postData = []
      if (params.type === 2) {
        postData = params.data.length > 0 ? params.data : []
      } else {
        postData = backRows.map((item) => (params.type === 1 ? item : item.id))
      }
      await this.$confirm(this.$t('common.deleteTips'), this.$t('common.tips'), {
        confirmButtonText: this.$t('buttonTxt.confirm'),
        cancelButtonText: this.$t('buttonTxt.cancel'),
        type: 'warning'
      })
      const res = await this.$fetch[params.method || 'delete'](params.url, postData)
      if (res.code === 0) {
        this.gridApi.applyTransaction({ remove: rowSelections })
        this.$message.success(this.$t('common.operateSuccessful'))
        return this.getRowData()
      }
    },
    // 获取过滤条件对象，在父组件用ref调用返回
    getFilterParams() {
      const paramsObj = {}
      const filterObj = this.gridApi.getFilterModel()
      Object.keys(filterObj).forEach((key) => {
        paramsObj[key] = filterObj[key].values.join(',')
      })
      return paramsObj
    },
    // 处理筛选时先清空所有选项然后逐条选中时出现序号重复的问题
    onFilterChanged(params) {
      this.gridApi.redrawRows()
      this.handleRightColumnWidth(params)
      this.$emit('onFilterChanged', params)
    },
    // 刷新表格数据
    refresh() {
      this.$emit('refresh')
    },
    // 查询数据
    search(params) {
      this.$emit('search', params)
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.$emit('search', this.pageInfo)
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.$emit('search', this.pageInfo)
    }
  }
}
</script>
<style lang="scss">
.ag-theme-alpine {
  flex: 1;
  input[type='checkbox'] {
    visibility: visible !important;
  }
  .ag-root-wrapper,
  .ag-header,
  .ag-floating-bottom,
  .ag-status-bar {
    border-color: rgb(224, 224, 224);
  }
}
</style>
