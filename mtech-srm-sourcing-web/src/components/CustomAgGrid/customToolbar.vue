<template>
  <div class="custom-toolbar-wrapper">
    <div class="left-toolbar toolbar-flex">
      <div class="toolbar-btn-group">
        <div
          class="toolbar-btn-item"
          type="default"
          v-for="(tool, index) in toolbarConfig"
          :key="index"
          @click="handleClickToolbar($event, tool)"
        >
          <!-- <mt-icon v-if="tool.icon" :name="tool.icon" /> -->
          <span class="span">{{ tool.title }}</span>
        </div>
      </div>
    </div>
    <div class="right-toolbar toolbar-flex">
      <div class="toolbar-btn-group">
        <div class="toolbar-btn-item" @click.stop="refresh">
          <!-- <mt-icon name="icon_solid_Refresh" /> -->
          <span class="span">{{ $t('刷新') }}</span>
        </div>
        <!-- TODO -->
        <div class="setting-wrap">
          <div class="toolbar-btn-item" type="primary" @click="setting">
            <!-- <mt-icon name="icon_solid_Settingup" /> -->
            <span class="span">{{ $t('设置') }}</span>
          </div>
          <transition name="fade">
            <mt-listbox
              ref="settingListbox"
              v-if="isShowListBox"
              v-model="selectedColumnList"
              class="setting-listbox"
              :allow-drag-and-drop="true"
              :data-source="columnList"
              :fields="{ text: 'headerName', value: 'field' }"
              :selection-settings="selectionSettings"
              @change="selectChange"
            ></mt-listbox>
          </transition>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { cloneDeep } from 'lodash'
import MtListbox from '@mtech-ui/listbox'
export default {
  name: 'ColumnConfig',
  components: {
    MtListbox
  },
  props: {
    columns: {
      type: Array,
      default() {
        return []
      }
    },
    selectedColumns: {
      type: Array,
      default() {
        return []
      }
    },
    toolbar: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isShowListBox: false,
      selectionSettings: {
        mode: 'Single',
        showCheckbox: true,
        showSelectAll: true
      },
      columnList: [],
      selectedColumnList: []
    }
  },
  computed: {
    toolbarConfig() {
      return this.toolbar.filter((item) => item?.hide !== true)
    }
  },
  watch: {
    columns: {
      handler(list) {
        this.columnList = cloneDeep(list)
      },
      immediate: true,
      deep: true
    },
    selectedColumns: {
      handler(list) {
        this.selectedColumnList = cloneDeep(list)
      },
      immediate: true,
      deep: true
    },
    isShowListBox(val) {
      // 列表消失时候隐藏
      if (!val) {
        this.saveConfig()
      }
    }
  },
  mounted() {
    document.addEventListener('click', this.clickOutside)
  },
  methods: {
    handleClickToolbar(e, temp) {
      this.$emit('clickToolbar', { event: e, toolbar: temp })
    },
    // 操作 - 刷新
    refresh() {
      this.$emit('refresh')
    },
    // 操作 - 设置
    setting() {
      this.isShowListBox = !this.isShowListBox
    },
    // 监听clickOutside
    clickOutside(e) {
      let _dom = document.querySelector('.setting-wrap')
      if (_dom && !_dom?.contains(e.target)) {
        this.isShowListBox = false
      }
    },
    // 监听表格勾选事件
    selectChange(e) {
      this.$emit('visibleColumnsChanged', e.value)
    },
    // 保存列信息
    saveConfig() {
      this.$emit('saveConfig')
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-toolbar-wrapper {
  display: flex;
  justify-content: space-between;
  padding: 0 20px 5px;
  align-items: center;
}
.toolbar-flex {
  display: flex;
  position: relative;
  .toolbar-btn-group {
    display: flex;
    .toolbar-btn-item {
      align-items: center;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #2783fe;
      cursor: pointer;
      display: flex;
      line-height: 1;
      margin-left: 10px;
      min-width: auto;
      padding: 5px 16px;
      position: relative;
      span {
        color: #4a556b;
        font-size: 14px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        word-break: keep-all;
      }
      &[type='primary'] {
        background: #4a556b;
        border: none;
        span {
          color: #ffffff;
        }
      }
      &[type='info'] {
        border: 1px solid #4a556b;
        span {
          color: #4a556b;
        }
      }
    }
  }
  .setting-wrap {
    position: relative;
    .setting-listbox {
      position: absolute;
      z-index: 999;
      min-width: 200px;
      max-height: 300px;
      right: 0px;
      overflow-y: scroll;
    }
  }
}
</style>
