<template>
  <div>
    <FreeRender v-if="params" :scope="scope" :render="params.render" />
  </div>
</template>
<script>
import FreeRender from './render.js'
export default {
  components: {
    FreeRender
  },
  data() {
    return {
      cellValue: null,
      scope: null
    }
  },
  beforeMount() {
    // this.params contains the cell & row information and is made available to this component at creation time
    // see ICellRendererParams below for more details
    this.cellValue = this.getValueToDisplay(this.params)
    this.scope = { row: this.params.data, $index: this.params.rowIndex }
  },
  methods: {
    // gets called whenever the user gets the cell to refresh
    refresh(params) {
      // set value into cell again
      this.cellValue = this.getValueToDisplay(params)
    },
    getValueToDisplay(params) {
      return params.valueFormatted ? params.valueFormatted : params.value
    }
  }
}
</script>
