import { valueMap, formatTime } from '@/utils/utils'
// 此处代码后续改造
export function recombineCols(list, t) {
  return list.map((column) => {
    let conf = null
    let cols = t.memoryInfo?.cols || []
    // 匹配缓存中的而数据
    let item = { ...column }
    let find = cols.find((memory) => memory.colId === column.field)
    if (find) {
      item = {
        ...item,
        width: find.actualWidth,
        hide: !find.visible
      }
    }
    // headerName 转义
    const _headerName = item.headerName
    item.headerName = _headerName && t.$te(_headerName) ? t.$t(_headerName) : _headerName
    item.children = item?.children ? recombineCols(item.children, t) : item.children
    switch (item.option) {
      // 配置复选框选择列
      case 'checkboxSelection':
        conf = {
          headerName: item.headerName,
          field: '',
          menuTabs: false,
          sortable: false,
          width: item.width || 50,
          pinned: item.pinned || 'left',
          suppressSizeToFit: true,
          headerCheckboxSelection: true,
          headerCheckboxSelectionFilteredOnly: true,
          checkboxSelection: true,
          cellStyle: { 'text-align': 'center' },
          ...item,
          showDisabledCheckboxes: true
        }
        break
      // 配置序号列
      case 'index':
        conf = {
          headerName: item.headerName,
          field: '',
          menuTabs: false,
          sortable: false,
          width: item.width || 50,
          pinned: item.pinned || 'left',
          suppressSizeToFit: true,
          valueFormatter: (params) => {
            return params.node.rowIndex + 1
          }
        }
        break
      // // 下拉选择， 需要配置option: 'select' 和 传入下拉数据dictionary: [...]
      // case 'select': {
      //   const { editConfig } = item
      //   let _dataSource = editConfig?.props ? editConfig?.props?.dataSource : editConfig?.dataSource
      //   delete item.option // 删除多余的属性，不然ag-grid会在控制台打印warn信息
      //   delete item.pasteBehavior
      //   conf = {
      //     ...item,
      //     suppressSizeToFit: item.width,
      //     cellEditor: 'agSelectCellEditor',
      //     cellEditorParams: { values: arrValues(_dataSource) }
      //   }
      //   // 下拉框值转换
      //   if (!item.valueFormatter && _dataSource && editConfig?.type === 'select') {
      //     conf.valueFormatter = (params) => {
      //       return valueMap(_dataSource, params.value)
      //     }
      //   }
      //   break
      // }
      // 自定义操作列渲染内容和交互，模仿了element-ui返回的scope包含row对象和$index
      case 'customOption': {
        if (typeof item.render !== 'function') {
          console.error('the render is not funciton')
          return item
        }
        const render = item.render
        delete item.option
        delete item.render
        conf = {
          ...item,
          menuTabs: false,
          sortable: false,
          cellRendererFramework: 'CustomRender',
          cellRendererParams: { render }
        }
        break
      }
      // 自定义Editor
      case 'customEdit': {
        if (
          item.editable &&
          item.editConfig &&
          ![
            'select',
            'multiSelect',
            'input',
            'number',
            'date',
            'dateMonth',
            'datetime',
            'selectSearch',
            'cellSelectSearch',
            'cellRemoteSelect',
            'cellCost',
            'cellDesc'
          ].includes(item.editConfig?.type)
        ) {
          console.error('the editor is neither funciton nor provied components')
        }
        const {
          editor,
          editType,
          regExpression,
          isToNumber,
          maxlength,
          field,
          dictionary,
          editorDef,
          editConfig
        } = item
        const {
          filterable,
          clearable,
          multiple,
          url,
          method,
          dateType,
          dateOptions,
          titleData,
          forKey
        } = editorDef || {}
        const render = item.render
        delete item.render
        delete item.editor
        delete item.option
        delete item.regExpression
        delete item.isToNumber
        delete item.editType
        delete item.maxlength
        delete item.pasteBehavior
        delete item.editConfig
        if (item.dictionary) delete item.dictionary
        if (item.editorDef) delete item.editorDef
        if (item.customEdit) delete item.customEdit
        conf = {
          ...item,
          cellEditor: 'CustomEdit',
          cellEditorParams: {
            editConfig,
            editor, // 以下暂不需要，待改造
            regExpression, // el-input params
            isToNumber, // el-input params
            maxlength, // el-input params
            editType, // el-input params
            field, // 自定义render函数params
            dictionary, // 下拉组件params
            filterable, // 下拉组件params
            clearable, // 下拉组件params
            multiple, // 下拉组件params
            url, // 下拉组件params
            method, // 下拉组件params
            dateType, // 日历组件params
            dateOptions, // 日历组件params
            titleData, // 下拉组件（Table）params
            forKey // 下拉组件（Table）params
          }
        }
        let _dataSource = editConfig?.props ? editConfig?.props?.dataSource : editConfig?.dataSource
        let _fields = editConfig?.props ? editConfig?.props?.fields : editConfig?.fields
        _fields = _fields || { value: 'value', text: 'text' }
        // 下拉框值转换
        if (!item.valueFormatter && _dataSource && editConfig?.type === 'select') {
          conf.valueFormatter = (params) => {
            return valueMap(_dataSource, params.value, _fields.text, _fields.value)
          }
        }
        // 日期值转换
        if (!item.valueFormatter && editConfig?.type === 'date') {
          conf.valueFormatter = (params) => {
            if (params.value == 0) return ''
            return params.value && !isNaN(Number(params.value))
              ? formatTime(new Date(Number(params.value)), 'Y-mm-dd')
              : params.value
              ? formatTime(new Date(params.value), 'Y-mm-dd')
              : ''
          }
        }
        // 时间值转换
        if (!item.valueFormatter && editConfig?.type === 'datetime') {
          conf.valueFormatter = (params) => {
            if (params.value == 0) return ''
            return params.value && !isNaN(Number(params.value))
              ? formatTime(new Date(Number(params.value)), 'Y-mm-dd HH:MM:SS')
              : params.value
          }
        }

        if (render) {
          conf = Object.assign({}, conf, {
            cellRendererFramework: 'CustomRender',
            cellRendererParams: { render }
          })
        }

        break
      }
      // 默认
      default:
        conf = {
          ...item,
          suppressSizeToFit: item.width // 如果开发者设置了width就禁止fit
        }
        if (!item.valueGetter && item.cellEditorParams?.dictionary) {
          conf.valueGetter = (params) => {
            return valueMap(item.cellEditorParams.dictionary, params.data[item.field])
          }
        }
        break
    }
    return conf
  })
}
