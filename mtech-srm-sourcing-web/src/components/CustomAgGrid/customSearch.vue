<template>
  <div class="vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      :is-grid-display="true"
      default-max-height="75"
      @reset="handleCustomReset"
      @search="handleCustomSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <template v-for="formItem in searchConfigAfterFilter">
          <mt-form-item
            :prop="searchFormModel[formItem.field]"
            :label="$t(formItem.label)"
            :key="formItem.field"
            v-if="formItem.type === 'select'"
            label-style="top"
          >
            <mt-select
              v-model="searchFormModel[formItem.field]"
              :show-clear-button="true"
              v-bind="formItem.props"
              @keyup.enter.native="handleCustomSearch"
            />
          </mt-form-item>
          <mt-form-item
            :prop="searchFormModel[formItem.field]"
            :label="$t(formItem.label)"
            :key="formItem.field"
            v-else-if="formItem.type === 'dateRange'"
            label-style="top"
          >
            <mt-date-range-picker
              v-model="searchFormModel[formItem.field]"
              :show-clear-button="true"
              v-bind="formItem.props"
              @keyup.enter.native="handleCustomSearch"
            ></mt-date-range-picker>
          </mt-form-item>
          <mt-form-item
            v-else
            :prop="searchFormModel[formItem.field]"
            :label="$t(formItem.label)"
            :key="formItem.field"
            label-style="top"
          >
            <mt-input
              v-model="searchFormModel[formItem.field]"
              :show-clear-button="true"
              v-bind="formItem.props"
              @keyup.enter.native="handleCustomSearch"
            />
          </mt-form-item>
        </template>
      </mt-form>
    </collapse-search>
  </div>
</template>

<script>
import CollapseSearch from './collapseSearch'

export default {
  components: {
    CollapseSearch
  },
  props: {
    searchConfig: {
      type: Array,
      default: () => []
    },
    paramsType: {
      type: Number,
      default: 1 // 1 => JD入参方式，返回rules , 2=>平铺方式，新接口方式
    }
  },
  computed: {
    searchConfigAfterFilter() {
      // 过滤
      return this.searchConfig.filter((item) => item?.hide !== true)
    }
  },
  data() {
    return {
      searchFormModel: {},
      copySearchFormModel: null
    }
  },
  mounted() {
    // 复制searchFormModel,重置使用
    this.copySearchFormModel = { ...this.searchFormModel }
  },
  watch: {},
  methods: {
    // btn - 重置 (此处查询和列表刷新功能基本相同)
    handleCustomReset() {
      this.searchFormModel = { ...this.copySearchFormModel }
      this.handleCustomSearch()
    },
    // btn - 查询
    handleCustomSearch() {
      let _params =
        this.paramsType === 2 ? this.searchFormModel : this.megerPrams(this.searchFormModel)
      this.$emit('search', _params)
    },
    // 查询 - 拼接参数（拼接成rule格式模式返回）
    megerPrams(params) {
      let rules = []
      for (let i in params) {
        let _find = this.searchConfigAfterFilter.find((item) => item.field === i)
        let _item = {}
        if (_find) {
          _item = {
            field: _find.field,
            label: _find.label,
            operator: _find?.operator ? _find?.operator : this.getOperatorByType(_find.type),
            type: 'string',
            value: params[i]
          }
        }
        rules.push(_item)
      }
      return {
        condition: 'and',
        rules
      }
    },
    // 查询 - 根据类型获取查询条件
    getOperatorByType(type) {
      let _operator = ''
      switch (type) {
        case 'select':
          _operator = 'equal'
          break
        case 'dateRange':
          _operator = 'between'
          break
        default:
          _operator = 'contains'
      }
      return _operator
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-select-selection {
  background-color: transparent !important;
}
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  .forecast-type-item {
    &:first-child {
      .vxe-cell-border {
        border-top: none;
      }
    }
    .vxe-cell-border {
      border-top: solid #e6e9ed 1px;
    }
  }
  .dynamic-column-item {
    height: 114px;
    .vxe-cell-border {
      border-top: solid #e6e9ed 1px;
      &:first-child {
        border-top: none;
      }
    }
  }
}
</style>

<style>
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
</style>
