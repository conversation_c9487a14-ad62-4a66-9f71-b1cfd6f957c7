<template>
  <div class="coutomHeaderContainer">
    <div class="required" v-if="required">*</div>
    <div v-if="doubleDisplayName.length > 1" class="customHeaderLabel">
      {{ doubleDisplayName[0] }}<br />{{ doubleDisplayName[1] }}
    </div>
    <div v-else class="customHeaderLabel">{{ params.displayName }}</div>
    <div class="tips-wrapper" v-if="fieldDesc">
      <svg-icon
        class="tips"
        icon-class="icon-tip"
        @mouseover="showTooltip"
        @mouseleave="hideTooltip"
      />
    </div>
    <div
      v-if="params.enableMenu && params.column.colDef.filter"
      :class="['customHeaderIcon', isFilterActive ? 'filter-active' : '']"
    >
      <!-- <div ref="menuButton" class="menuIcon" @click="onMenuClicked($event)">
        <i class="iconfont" :class="params.menuIcon" />
      </div> -->
      <div v-if="params.enableSorting">
        <div v-if="count === 1" class="sortIcon" @click="onSortRequested($event)">
          <i class="sort">
            <svg-icon class="sortIconActive" icon-class="up" />
            <svg-icon class="sortIconActive" icon-class="down" />
          </i>
        </div>
        <div v-if="count === 2" class="sortIcon" @click="onSortRequested($event)">
          <i class="sort">
            <svg-icon class="sortIconActive" icon-class="up-blue" />
            <svg-icon class="sortIconActive" icon-class="down" />
          </i>
        </div>
        <div v-if="count === 3" class="sortIcon" @click="onSortRequested($event)">
          <i class="sort">
            <svg-icon class="sortIconActive" icon-class="up" />
            <svg-icon class="sortIconActive" icon-class="down-blue" />
          </i>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      ascSort: false,
      descSort: false,
      noSort: true,
      count: 1,
      isFilterActive: false
    }
  },
  computed: {
    required() {
      return this.params.column.colDef?.required
    },
    doubleDisplayName() {
      return this.params.displayName.split('_,')
    },
    fieldDesc() {
      return this.params?.column?.colDef?.fieldDesc || ''
    }
  },
  mounted() {
    this.params.column.addEventListener('filterChanged', this.onFilterChanged)
  },
  beforeDestroy() {
    this.params.column.removeEventListener('filterChanged', this.onFilterChanged)
  },
  methods: {
    showTooltip(event) {
      if (!this.tooltipElement) {
        this.tooltipElement = document.createElement('div')
        this.tooltipElement.className = 'customToolTip'
        this.tooltipElement.textContent = this.fieldDesc
        this.tooltipElement.style.left = `${event.clientX + 2}px`
        this.tooltipElement.style.top = `${event.clientY + 2}px`
        document.body.appendChild(this.tooltipElement)
      }
    },
    hideTooltip() {
      if (this.tooltipElement) {
        document.body.removeChild(this.tooltipElement)
        this.tooltipElement = null
      }
    },
    // 点击菜单icon 显示过滤菜单
    onMenuClicked() {
      this.params.showColumnMenu(this.$refs.menuButton)
    },
    onFilterChanged() {
      this.isFilterActive = this.params.column.filterActive
    },
    // 点击排序icon 执行排序操作 count为1、2、3分别表示排序前、升序、降序，点击icon执行顺序为：排序前(1)->升序(2)->降序(3)->排序前(1)
    onSortRequested(event) {
      let order = ''
      let flag = this.count
      switch (flag) {
        case 1:
          order = 'asc'
          this.count = 2
          break
        case 2:
          order = 'desc'
          this.count = 3
          break
        case 3:
          order = ''
          this.count = 1
          break
        default:
          break
      }
      this.params.setSort(order, event.shiftKey)
      this.params.api.redrawRows()
    }
  }
}
</script>
<style>
.customToolTip {
  position: fixed;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.06);
  color: #eee;
  padding: 6px 8px;
  text-align: left;
  z-index: 9999;
  font-size: 14px;
}
</style>
<style lang="scss" scoped>
.coutomHeaderContainer {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  .required {
    position: absolute;
    left: 8px;
    color: red;
    margin-right: 3px;
  }
  .customHeaderLabel {
    overflow: hidden;
    word-break: break-all;
    white-space: break-spaces;
    text-overflow: -o-ellipsis-lastline;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .customHeaderIcon {
    position: absolute;
    right: 6px;
    background-color: var(--ag-header-background-color, #f5f6f7);
    display: flex;
    margin: 0 0 0 2px;
    .menuIcon {
      margin: 0;
    }
    .sort {
      display: flex;
      flex-direction: column;
      .sortIconActive {
        &:first-child {
          height: 10px;
        }
        /deep/ .svg-icon {
          width: 8px;
          height: 8px;
        }
      }
    }
    opacity: 0;
  }
  &:hover {
    .customHeaderIcon {
      opacity: 1;
    }
  }
  .filter-active {
    opacity: 1;
  }
}
.tips-wrapper {
  .tips {
    margin-left: 4px;
    margin-top: 4px;
    /deep/ .svg-icon {
      width: 16px;
      height: 16px;
    }
  }
}
</style>
