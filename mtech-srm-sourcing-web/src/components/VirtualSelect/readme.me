# virtual select
> 使用虚拟列表解决大量数据select组件卡顿问题

## API

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
| --- | --- | --- | --- | --- |
|value/v-model|绑定值|Object|-|-|
|filter-type|搜索类型|String|'remote'\|'local'|'remote'|
|remote-method|远程加载数据方法|Function|-|-|
|row-key|渲染列表的key|String|-|-|
|row-name|选中值后，select input 里回显的值|String|-|-|
|search-keys|搜索字段组合|String[]|-|-|
|display-keys|下拉列表显示的内容组合|String[]|-|-|
|min-keywords-length|最少关键字长度（触发搜索行为）|Number|-|3|

## 方法

|事件名称|说明|回调参数|
|---|---|---|---|
|change|选中值发生变化时触发|选中值|

## 其他说明

- 如果是remote形式，要保证函数的返回值结构是这样的`{rawItem: your data, [rowKey]: xxx, __virtualListExtra__: {displayKeys, matchField: oneValueInSearchKeys}}`
- [ ] 删除上述结构里的`displayKeys`
- 匹配值是根据searchKeys的顺序来的，匹配到之后会在下拉列表里以badge的形式展现，如果只有一个key，则badge不显示。
- [ ] Keyboard Accessibility
