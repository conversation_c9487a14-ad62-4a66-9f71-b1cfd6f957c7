<template>
  <div
    class="virtual-select-container"
    ref="virtual-select-container"
    tabindex="0"
    :style="{ width: $attrs.width + 'px' }"
  >
    <div class="e-input-group" style="border: none">
      <!-- :placeholder="`请输入 ${searchKeys.join(
          ' 或 '
        )} 搜索，至少 ${minKeywordsLength} 个字符`" -->
      <ejs-textbox
        :float-label-type="$attrs['float-label-type'] || 'Auto'"
        show-clear-button="true"
        disabled
        @focus="handleInputFocus"
        ref="input-box"
        :value="inputValue"
        @input="handleInput"
        @blur="handleInputBlur"
      ></ejs-textbox>
      <span
        class="e-icons e-input-group-icon e-select-blur"
        :class="isFocus ? 'isFocus' : ''"
      ></span>
    </div>

    <div
      class="virtual-select-list-container"
      :style="{ top: listPosition.top }"
      v-show="showSelectList"
    >
      <VirtualList
        v-show="filtedData.length > 0"
        :data-sources="filtedData"
        :estimate-size="filtedData.length"
        class="virtual-list"
        :data-key="rowKey"
        :data-component="ListItemComponent"
      />
      <p class="no-match" v-show="filtedData.length === 0">
        {{ $t('暂无数据') }}
      </p>
    </div>
  </div>
</template>

<script>
import debounce from 'lodash.debounce'
import Vue from 'vue'
import VirtualList from 'vue-virtual-scroll-list'

const ListItemComponent = Vue.component('virtual-list-item', {
  name: 'VirtualListItemInner',
  props: { source: { type: Object, default: () => {} } },
  methods: {
    handleClick() {
      let parent = this.$parent
      let name = parent.$options.name
      while (parent && (!name || name !== 'VirtualSelect')) {
        parent = parent.$parent
        if (parent) name = parent.$options.name
      }
      parent.$emit('select-virtual-list-item', this.source.rawItem)
    }
  },
  render() {
    const { source, handleClick } = this
    if (source.__virtualListExtra__.NoDataMessage) {
      return <div>{source.__virtualListExtra__.NoDataMessage}</div>
    }
    const str = source.__virtualListExtra__.displayKeys
      .map((field) => source.rawItem[field])
      .join(' | ')
    return (
      <div class='virtual-list-item-inner' onClick={handleClick} title={str}>
        <p style='display: flex; align-items: center; justify-content: space-between;'>
          <span class='virtual-item-text'>{str}</span>
          {source.__virtualListExtra__.displayKeys.length > 1 && (
            <span class='virtual-item-badge'>{source.__virtualListExtra__.matchField}</span>
          )}
        </p>
      </div>
    )
  }
})

export default {
  name: 'VirtualSelect',
  components: { VirtualList },
  model: {
    prop: 'selectedValue',
    event: 'change'
  },
  props: {
    selectedValue: {
      type: Object,
      default: () => {}
    },
    filterType: {
      // 'remote'|'local'
      type: String,
      default: 'remote'
    },
    searchKeys: {
      type: Array,
      required: true
    },
    rowKey: {
      type: String,
      default: ''
    },
    rowName: {
      type: String,
      default: ''
    },
    displayKeys: {
      type: Array,
      default: () => []
    },
    // placeholder: {
    //   type: String,
    //   default: '请输入关键字'
    // },
    minKeywordsLength: {
      type: Number,
      default: 3
    },
    remoteMethod: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      dataSource: [],
      showSelectList: false,
      inputValue: '',
      keywords: '',
      loading: false,
      listPosition: {
        top: 0
      },
      isFocus: false,
      ListItemComponent
    }
  },
  mounted() {
    if (this.filterType === 'local') {
      // 如果是本地过滤，组件加载后就去远端拿全部数据
      this.loading = true
      this.remoteMethod().then((res) => {
        this.dataSource = res
        this.loading = false
      })
    }

    this.$on('select-virtual-list-item', (data) => {
      this.$emit('change', data)
      this.inputValue = data[this.rowName]
      this.showSelectList = false
    })

    document.addEventListener('click', this.clickOutside)
  },

  watch: {
    selectedValue: {
      immediate: true,
      handler() {
        this.inputValue = this.selectedValue ? this.selectedValue[this.rowName] : ''
      }
    }
  },
  methods: {
    /** @param {Event} event */
    clickOutside(event) {
      if (!event.composedPath().includes(this.$refs['virtual-select-container'])) {
        this.showSelectList = false
        this.isFocus = false
      }
    },
    handleInputBlur() {
      this.isFocus = false
    },
    handleInput: debounce(function (object) {
      this.isFocus = true
      this.listPosition.top = this.$refs['input-box'].$el.offsetHeight + 1 + 'px'
      if (object.value.length >= this.minKeywordsLength) {
        this.keywords = object.value
        if (this.filterType === 'remote') {
          this.loading = true
          this.remoteMethod(this.keywords).then((data) => {
            this.isFocus && (this.showSelectList = true)
            this.loading = false
            this.dataSource = data
          })
        } else {
          // 等待触发computed的计算
          this.loading = true
          this.$nextTick(() => {
            this.showSelectList = true
            this.loading = false
          })
        }
      } else {
        this.showSelectList = false
      }
      this.inputValue = object.value
    }, 200),
    handleInputFocus(object) {
      this.isFocus = true
      this.listPosition.top = this.$refs['input-box'].$el.offsetHeight + 1 + 'px'
      if (object.value < this.minKeywordsLength) return
      this.keywords = object.value
      if (this.filterType === 'remote') {
        this.loading = true
        this.remoteMethod(this.keywords).then((data) => {
          this.isFocus && (this.showSelectList = true)
          this.loading = false
          this.dataSource = data
        })
      } else {
        // 等待触发computed的计算
        this.loading = true
        this.$nextTick(() => {
          this.showSelectList = true
          this.loading = false
        })
      }
    }
  },
  computed: {
    filtedData() {
      if (!this.keywords.length) return []
      if (this.filterType === 'remote') {
        return this.dataSource
      } else {
        const result = []
        this.dataSource.forEach((item) => {
          let _item // 内部过滤用的，没有匹配到属性报警
          for (let field of this.searchKeys) {
            if (item[field]) {
              _item = item
              if (typeof item[field] === 'string') {
                if (item[field].toLowerCase().includes(this.keywords)) {
                  result.push({
                    rawItem: item,
                    id: item.id,
                    __virtualListExtra__: {
                      matchField: field,
                      displayKeys: this.displayKeys
                    }
                  })
                  break
                }
              } else {
                throw Error(
                  `${this.$t('数据子项')}${JSON.stringify(item)} ${this.$t(
                    '匹配到的属性不是字符串类型'
                  )}`
                )
              }
            }
          }
          if (!_item) {
            console.warn(this.$t('数据子项'), JSON.stringify(item), this.$t('找不到想要匹配的字段'))
          }
        })
        return result
      }
    }
  },
  beforeDestroy() {
    document.removeEventListener('click', this.clickOutside)
  }
}
</script>

<style scoped lang="scss">
.virtual-select-container {
  position: relative;
  .virtual-select-list-container {
    width: 100%;
    max-height: 400px;
    overflow-y: auto;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 0 rgba(0, 0, 0, 0.14),
      0 3px 14px 0 rgba(0, 0, 0, 0.12);
    position: absolute;
    top: 0;
    z-index: 2147483647;
    .no-match {
      padding: 8px;
      background-color: #fff;
    }
    /deep/.virtual-list-item-inner {
      cursor: pointer;
      overflow: hidden;
      position: relative;
      text-overflow: ellipsis;
      vertical-align: middle;
      white-space: nowrap;
      width: 100%;
      background-color: #fff;
      border-bottom: 0;
      border-color: #fff;
      color: rgba(0, 0, 0, 0.87);
      font-family: inherit;
      font-size: 13px;
      line-height: 36px;
      min-height: 36px;
      padding-right: 16px;
      text-indent: 16px;
      &:hover {
        background-color: rgb(238, 238, 238);
        border-color: transparent;
        color: rgba(0, 0, 0, 0.87);
      }
      .virtual-item-text {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .virtual-item-badge {
        background-color: #3f51b5;
        color: #fff;
        padding: 3px 4px;
        text-indent: 0;
        font-size: 12px;
        line-height: 1;
        border-radius: 3px;
      }
    }
  }
}
</style>

<style>
.e-select-blur {
  transition: all 0.5s;
  transform: rotate(0deg);
}
.e-select-blur:before {
  content: '\e82e';
}
.isFocus.e-select-blur {
  transform: rotate(180deg);
}
.e-select-loading:before {
  content: '\e99d';
}
</style>
