<template>
  <div class="common-rule-config mt-flex-direction-column">
    <div
      :class="['mt-flex', 'rule-item-container']"
      :name="'item-container-' + index"
      v-for="(rule, index) in ruleData"
      :key="index"
    >
      <div class="rule-item rule-item-handler"></div>
      <div :class="['rule-item', 'rule-link', 'last-link']">
        <mt-select
          v-model="rule.groupLink"
          css-class="rule-element"
          :data-source="groupLinkList"
        ></mt-select>
      </div>
      <div
        :class="['rule-item', 'rule-data-source', { split: showElementByType(rule, 'text') }]"
        :ref="'dataSource-' + index"
        :title="getDataSourceTitle(rule, index)"
      >
        <!--

          @mouseenter="handleTooltip(index, 'dataSource')" -->
        <mt-select
          :ref="'dataSourceRef-' + index"
          css-class="rule-element"
          v-model="rule.dataSource"
          :data-source="queryDataSource"
          :fields="queryFields"
          :item-template="selectItemTemplate"
        ></mt-select>
      </div>
      <div :class="['rule-item', 'rule-data-value']" :ref="'dataValue-' + index">
        <mt-input
          v-if="showElementByType(rule, 'text')"
          css-class="rule-element"
          v-model="rule.dataValue"
          :show-clear-button="false"
          type="text"
        ></mt-input>
      </div>
      <div class="rule-item rule-icons">
        <img
          v-if="ruleData.length < queryDataSource.length"
          src="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNjI2Mjg0NjE5NTcwIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjUyNDAiIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPjxkZWZzPjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+PC9zdHlsZT48L2RlZnM+PHBhdGggZD0iTTUxMS45OTkwNCAwYzY5LjExOTg3IDAgMTM2LjQ0Nzc0NCAxMy43NTk5NzQgMTk4LjkxMTYyNyA0MC4wNjM5MjVhMzEuOTk5OTQgMzEuOTk5OTQgMCAxIDEtMjQuOTU5OTUzIDU4Ljk0Mzg4OSA0NDcuOTk5MTYgNDQ3Ljk5OTE2IDAgMSAwIDIzOS43NDM1NSAyNDAuODMxNTQ5IDMxLjk5OTk0IDMxLjk5OTk0IDAgMSAxIDU5LjEzNTg4OS0yNC41NzU5NTRBNTExLjk5OTA0IDUxMS45OTkwNCAwIDEgMSA1MTEuOTk5MDQgMHogbTAgMTkxLjk5OTY0YTMxLjk5OTk0IDMxLjk5OTk0IDAgMCAxIDMxLjk5OTk0IDMxLjk5OTk0djI1NS45MzU1MmwyNTUuOTk5NTIgMC4wNjRhMzEuOTk5OTQgMzEuOTk5OTQgMCAxIDEgMCA2My45OTk4OGwtMjU1Ljk5OTUyLTAuMDY0Vjc5OS45OTg1YTMxLjk5OTk0IDMxLjk5OTk0IDAgMSAxLTYzLjk5OTg4IDBWNTQzLjkzNDk4bC0yNTUuOTk5NTIgMC4wNjRhMzEuOTk5OTQgMzEuOTk5OTQgMCAwIDEgMC02My45OTk4OGwyNTUuOTk5NTItMC4wNjRWMjIzLjk5OTU4QTMxLjk5OTk0IDMxLjk5OTk0IDAgMCAxIDUxMS45OTkwNCAxOTEuOTk5NjR6IiBmaWxsPSIjNjM4NkMxIiBwLWlkPSI1MjQxIj48L3BhdGg+PC9zdmc+"
          @click="addRuleItem(index)"
        />
        <img
          v-if="ruleData.length > 1"
          src="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNjI2Mjg0Njc1MDEwIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjU0NDQiIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMzIiIGhlaWdodD0iMzIiPjxkZWZzPjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+PC9zdHlsZT48L2RlZnM+PHBhdGggZD0iTTUxMS45OTkwNCAwYzY5LjExOTg3IDAgMTM2LjQ0Nzc0NCAxMy43NTk5NzQgMTk4LjkxMTYyNyA0MC4wNjM5MjVhMzEuOTk5OTQgMzEuOTk5OTQgMCAxIDEtMjQuOTU5OTUzIDU4Ljk0Mzg4OSA0NDcuOTk5MTYgNDQ3Ljk5OTE2IDAgMSAwIDIzOS43NDM1NSAyNDAuODMxNTQ5IDMxLjk5OTk0IDMxLjk5OTk0IDAgMSAxIDU5LjEzNTg4OS0yNC41NzU5NTRBNTExLjk5OTA0IDUxMS45OTkwNCAwIDEgMSA1MTEuOTk5MDQgMHogbTI4Ny45OTk0NiA0NzkuOTk5MWEzMS45OTk5NCAzMS45OTk5NCAwIDEgMSAwIDYzLjk5OTg4aC01NzUuOTk4OTJhMzEuOTk5OTQgMzEuOTk5OTQgMCAwIDEgMC02My45OTk4OGg1NzUuOTk4OTJ6IiBmaWxsPSIjRUQ1NjMzIiBwLWlkPSI1NDQ1Ij48L3BhdGg+PC9zdmc+"
          @click="removeRuleItem(index)"
        />
      </div>
    </div>
  </div>
</template>
<script>
import Vue from 'vue'
export default {
  name: 'SimpleRule',
  props: {
    value: {
      type: Array,
      default: () => {
        return []
      }
    },
    queryFields: {
      type: Object,
      default: () => {
        return { text: 'text', value: 'value' }
      }
    },
    queryDataSource: {
      type: Array,
      default: () => {
        return []
      }
    },
    groupLinkList: {
      type: Array,
      default: () => {
        return [
          { text: this.$t('或'), value: 'or' },
          { text: this.$t('且'), value: 'and' }
        ]
      }
    }
  },
  data() {
    return {
      ruleData: this.value,
      // 因文字太多，给选项添加title
      selectItemTemplate: function () {
        return {
          template: Vue.component('select-item', {
            template: `<div :title="data.text">{{data.text}}</div>`,
            data() {
              return { data: {} }
            }
          })
        }
      }
    }
  },
  computed: {
    getDataSourceObject() {
      return (_rule) => {
        let _dataSource = _rule['dataSource']
        let _find = this.queryDataSource.filter((e) => {
          return e[this.queryFields['value']] === _dataSource
        })
        return _find.length ? _find[0] : {}
      }
    },
    showElementByType() {
      return (rule, type) => {
        let _dataSourceObject = this.getDataSourceObject(rule)
        if (_dataSourceObject && Object.prototype.hasOwnProperty.call(_dataSourceObject, 'type')) {
          return _dataSourceObject['type'] === type
        } else {
          return type === 'text'
        }
      }
    },
    getDataSourceTitle() {
      return (rule, index) => {
        if (this.$refs[`dataSourceRef-${index}`]) {
          let _select = this.$refs[`dataSourceRef-${index}`][0]?.ejsRef?.getDataByValue(
            rule.dataSource
          )
          return _select?.text
        } else {
          return ''
        }
      }
    }
  },
  mounted() {},
  methods: {
    addRuleItem(index) {
      let _rules = JSON.parse(JSON.stringify(this.ruleData))

      let _checkDataSource = this.checkRuleData(_rules)
      if (_checkDataSource) {
        // 当前规则校验通过，执行添加操作
        const targetRow = {
          groupLeft: '',
          dataSource: '',
          dataValue: '',
          symbol: '=',
          groupRight: '',
          groupLink: 'or'
        }
        _rules.splice(index + 1, 0, targetRow)
        this.ruleData = []
        this.$nextTick(function () {
          this.ruleData = _rules
        })
      }
    },
    removeRuleItem(index) {
      let _rules = JSON.parse(JSON.stringify(this.ruleData))
      _rules.splice(index, 1)
      this.ruleData = []
      this.$nextTick(function () {
        this.ruleData = _rules
      })
    },
    getValidResultByPromise() {
      let _rules = JSON.parse(JSON.stringify(this.ruleData))
      let _checkDataSource = this.checkRuleData(_rules)
      return new Promise((resolve, reject) => {
        if (_checkDataSource) {
          resolve({ rules: _rules })
        } else {
          reject({ rules: _rules })
        }
      })
    },
    getValidResult() {
      let _rules = JSON.parse(JSON.stringify(this.ruleData))
      let _checkDataSource = this.checkRuleData(_rules)
      return {
        rules: _rules,
        ruleIsOk: _checkDataSource
      }
    },
    checkRuleData(_rules) {
      //校验当前值，包括dataSource、symbol、dataValue
      let _checkResult = true
      let _sourceMap = {}
      for (let i in _rules) {
        let _rule = _rules[i]
        let _dataSource = _rule['dataSource']
        if (!_dataSource) {
          //dataSource为空，校验不通过
          _checkResult = false
          this.handleTooltip(i, 'dataSource')
          break
        } else {
          if (_sourceMap[_dataSource]) {
            _checkResult = false
            this.handleTooltip(i, 'dataSource', this.$t('条件值重复'))
            break
          } else {
            _sourceMap[_dataSource] = _dataSource
          }
          if (_dataSource == 3) {
            let _dataValue = _rule['dataValue']
            let _p = /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/
            if (!_p.test(_dataValue)) {
              _checkResult = false
              this.handleTooltip(i, 'dataValue', this.$t('数据不规范.'))
              break
            }
          }
        }
      }
      return _checkResult
    },
    handleTooltip(index, link, msg = this.$t('规则配置不规范')) {
      let _el = this.$refs[`${link}-${index}`][0]
      this.$ruleTooltip({
        target: _el,
        coordinate: {
          offsetTop: _el.offsetTop,
          offsetLeft: _el.offsetLeft,
          offsetWidth: _el.offsetWidth,
          offsetHeight: _el.offsetHeight,
          clientWidth: _el.clientWidth,
          clientHeight: _el.clientHeight
        },
        content: msg
      })
    },
    handleSelectField(index) {
      setTimeout(() => {
        this.ruleData[index]['dataValue'] = ''
      }, 0)
    }
  },
  watch: {
    value: {
      handler(n) {
        this.ruleData = n
      },
      deep: true
    }
  }
}
</script>
<style lang="scss" scoped>
.common-rule-config {
  background: transparent;
  margin-top: 6px;

  .rule-item-container {
    background: #ffffff;
    height: 40px;
    width: 170px;
    border-radius: 4px;
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.06);
    margin-top: 10px;

    &:first-of-type {
      margin-top: 0;
    }

    .rule-item {
      display: flex;
      position: relative;
      justify-content: center;
      align-items: center;

      &.split:after {
        content: '';
        height: 20px;
        border-left: 1px solid #e8e8e8;
        position: relative;
        margin: 0 10px;
      }

      .rule-element {
        border: none;
        border-color: transparent !important;
        &:before,
        &:after {
          display: none;
        }
        .e-float-line {
          display: none;
        }
        .e-control {
          color: #292929;
          border: none;
          border-color: transparent !important;
        }
        &.e-date-wrapper,
        &.e-time-wrapper {
          .e-icons {
            display: none;
          }
        }
        .e-ddt-icon,
        .e-ddl-icon {
          &:before {
            content: '';
            position: absolute;
            width: 0;
            height: 0;
            border-top: 4px solid #292929;
            border-bottom: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            left: 50%;
            top: 50%;
          }
        }
      }
    }

    .rule-item-handler {
      background: #dedede;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 4px 0 0 4px;
      cursor: move;
      &:before {
        content: '';
        height: 16px;
        border-left: 1px solid #ffffff;
        margin-left: 3px;
      }
      &:after {
        content: '';
        height: 16px;
        border-left: 1px solid #ffffff;
        margin: 0 3px;
      }

      &:hover {
        background: #6386c1;
      }
    }

    .rule-link {
      .select-container {
        width: 44px;
        .rule-element {
          .e-control {
            text-align: right;
            color: #9a9a9a;
          }
          .e-ddl-icon {
            &:before {
              border-top-color: #9a9a9a;
            }
          }
        }
      }
      &.last-link {
        // margin-right: 10px;
        margin-right: 0;
        .rule-element {
          .e-control {
            color: #eda133;
          }
          .e-ddl-icon {
            &:before {
              border-top-color: #eda133;
            }
          }
        }
      }
      &.last-item {
        visibility: hidden;
      }
    }

    .rule-data-source {
      .select-container {
        width: 80px;
      }
    }

    .rule-data-value {
      min-width: 20px;
      flex: 1;
      &.simple-data-value {
        visibility: hidden;
        &:after {
          visibility: hidden;
        }
      }
    }

    .rule-icons {
      position: absolute;
      top: 10px;
      width: 50px;
      right: -60px;
      justify-content: space-between;
      img {
        height: 20px;
        width: 20px;
        cursor: pointer;
      }
    }
  }

  .simple-container {
    .rule-data-source {
      .select-container {
        width: 240px;
        .e-control {
          padding-left: 20px;
        }
      }
    }
    .rule-data-value {
      &::after {
        visibility: hidden;
      }
    }
  }
}
</style>
