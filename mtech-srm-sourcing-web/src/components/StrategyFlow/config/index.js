import { i18n } from '@/main.js'
export const ruleItemMap = {
  minSupplier: i18n.t('供应商数量至少'),
  maxSupplier: i18n.t('供应商数量至多'),
  quotationRound: i18n.t('允许报价轮数'),
  extQuotationRound: i18n.t('轮次'),
  quotationNextCondition: i18n.t('报价触发下一轮条件流程'),
  nextRfxRule: i18n.t('自动规则'),
  addSupplier: i18n.t('增加供应商'), // un-use
  eliminateSupplier: i18n.t('淘汰供应商'), // un-use
  enableSupplier: i18n.t('禁用/启用供应商'),
  quotationType: i18n.t('按寻源行/成本模型报价'), // un-use
  quotationNum: i18n.t('每轮报价次数'), // un-use
  extQuotationNum: i18n.t('指定'), // un-use
  quotationRange: i18n.t('报价范围'),
  quotationForm: i18n.t('报价方式'),
  openQuotation: i18n.t('报价时公开'),
  counterOffer: i18n.t('允许还价'),
  quotationEndRule: i18n.t('报价结束'),
  sealedPrice: i18n.t('是否密封价格'),
  bidOpenTime: i18n.t('开标时间'),
  bidOpenTimeValue: i18n.t('设置开标时间'),
  bidOpenPwd: i18n.t('开标密码'),
  bidEvaluationType: i18n.t('评标方式'),
  sliderValue: i18n.t('评分权重'),
  biddingStrategy: i18n.t('决标策略'),
  extQuotationStartTime: i18n.t('报价开始'),
  extQuotationEndTime: i18n.t('报价结束'),
  extBidOpenTime: i18n.t('设置开标时间'),
  extBidOpenUser: i18n.t('设置开标人'),
  extEvaluationBusinessUser: i18n.t('商务评标人'),
  extEvaluationTechUser: i18n.t('技术评标人')
}
export const flowPointsMap = [
  {
    taskId: '1',
    taskType: 'SUPPLIER_JOIN',
    taskKey: '2a3d9166-fc19-11eb-96d2-0242ac130002',
    taskName: i18n.t('供应商报名'),
    flowType: 0,
    flowTask: 0,
    rules: []
  },
  {
    taskId: '2',
    taskType: 'SUPPLIER_AUDIT',
    taskKey: '2a4d2304-fc19-11eb-96d2-0242ac130002',
    taskName: i18n.t('供应商评审'),
    flowType: 0,
    flowTask: 0,
    rules: []
  },
  {
    taskId: '3',
    taskType: 'SUPPLIER_PAY_DEPOSIT',
    taskKey: '2a5bb543-fc19-11eb-96d2-0242ac130002',
    taskName: i18n.t('缴纳保证金'),
    flowType: 0,
    flowTask: 0,
    rules: []
  },
  {
    taskId: '4',
    taskType: 'PUBLISH_PRICE',
    taskKey: '2a6a1f4c-fc19-11eb-96d2-0242ac130002',
    taskName: i18n.t('发布报价'),
    flowType: 0,
    flowTask: 0,
    rules: []
  },
  {
    taskId: '5',
    taskType: 'BUSINESS_BID_PRICE',
    taskKey: '2a7901e9-fc19-11eb-96d2-0242ac130002',
    taskName: i18n.t('商务报价'),
    flowType: 0,
    flowTask: 0,
    rules: [
      'quotationForm', //报价方式
      'openQuotation', //报价时公开
      'quotationEndRule' //报价结束
    ]
  },
  {
    taskId: '6',
    taskType: 'BUSINESS_OPEN_BID',
    taskKey: '2a880b18-fc19-11eb-96d2-0242ac130002',
    taskName: i18n.t('商务开标'),
    flowType: 0,
    flowTask: 0,
    rules: [
      'sealedPrice', //是否密封价格
      'bidOpenTime', //开标时间
      'bidOpenTimeValue', //设置开标时间
      'bidOpenPwd' //开标密码
    ]
  },
  {
    taskId: '7',
    taskType: 'COMPARE_PRICE',
    taskKey: '2a96ae75-fc19-11eb-96d2-0242ac130002',
    taskName: i18n.t('商务比价'),
    flowType: 0,
    flowTask: 0,
    rules: [
      'counterOffer' //允许还价
    ]
  },
  {
    taskId: '8',
    taskType: 'AUDIT_PRICE',
    taskKey: '2aa5560e-fc19-11eb-96d2-0242ac130002',
    taskName: i18n.t('核价'),
    flowType: 0,
    flowTask: 0,
    rules: []
  },
  {
    taskId: '9',
    taskType: 'EVALUATION_BID',
    taskKey: '2ab449a0-fc19-11eb-96d2-0242ac130002',
    taskName: i18n.t('评标'),
    flowType: 0,
    flowTask: 0,
    rules: [
      'bidEvaluationType', //评标方式
      'sliderValue' //评分权重
    ]
  },
  {
    taskId: '10',
    taskType: 'DECIDE_PRICE',
    taskKey: '2ac329d7-fc19-11eb-96d2-0242ac130002',
    taskName: i18n.t('定点'),
    flowType: 0,
    flowTask: 0,
    rules: [
      'biddingStrategy' //决标策略
    ]
  },
  {
    taskId: '11',
    taskType: 'START',
    taskKey: '060f7e23-009e-11ec-96d2-0242ac130002',
    taskName: i18n.t('启动点'),
    flowType: 0,
    flowTask: 0,
    rules: []
  },
  {
    taskId: '12',
    taskType: 'END',
    taskKey: '061f3819-009e-11ec-96d2-0242ac130002',
    taskName: i18n.t('结束点'),
    flowType: 0,
    flowTask: 0,
    rules: []
  },
  {
    taskId: '13',
    taskType: 'PUBLISH_TENDER',
    taskKey: '2844deec-0558-11ec-96d2-0242ac130002',
    taskName: i18n.t('发布招标'),
    flowType: 0,
    flowTask: 0,
    rules: [
      'quotationRound', //允许报价轮数
      'extQuotationRound', //轮次
      'quotationNextCondition', //报价触发下一轮条件流程
      'nextRfxRule', //自动规则
      'enableSupplier', //禁用/启用供应商
      'quotationRange', //报价范围
      'minSupplier', //供应商数量至少
      'maxSupplier' //供应商数量至多
    ]
  },
  {
    taskId: '14',
    taskType: 'ACCEPT_PRICE',
    taskKey: '28559c4d-0558-11ec-96d2-0242ac130002',
    taskName: i18n.t('接收报价'),
    flowType: 0,
    flowTask: 0,
    rules: []
  }
]
