<template>
  <div class="strategy-content mt-flex-direction-column">
    <MtechBpmnEditor
      ref="bpmnEditorRef"
      :class="[{ 'bpmn-editor-disabled': !strategyEnabled }]"
      :id="getCurrentFlowId"
      @select="selectFlowPoint"
    >
    </MtechBpmnEditor>
  </div>
</template>
<script>
import MtechBpmnEditor from '@digis/digis-bpmn-editor'
import '@digis/digis-bpmn-editor/lib/css/style.css'
import { ruleItemMap } from './config'
import { v1 as uuidv1 } from 'uuid'
export default {
  name: 'StrategyFlow',
  components: {
    MtechBpmnEditor
  },
  props: {
    strategyConfig: {
      type: Object,
      default: () => {}
    },
    strategyModel: {
      type: Object,
      default: () => {}
    },
    strategyEnabled: {
      type: Boolean,
      default: true
    },
    strategyMode: {
      //用于配置追加配置规则，模式：Config(配置页)、RFX(Tab使用页)
      type: String,
      default: 'Config'
    },
    strategyUseModel: {
      type: Object,
      default: () => {}
    },
    strategyType: {
      //类型:Config(配置页)、RFX(Tab使用页)
      type: String,
      default: 'Config'
    }
  },
  data() {
    return {
      ruleItemMap,
      flowPointsMap: [],
      currentSelectPoint: null,
      modelName: this.$t('寻源招标流程'),
      flowModel: this.strategyModel,
      flowUseModel: this.strategyUseModel,
      selectMaps: {},
      leftNavList: [
        this.$t('基本配置'),
        this.$t('招投标配置'),
        this.$t('开标配置'),
        this.$t('评标配置'),
        this.$t('决标配置')
      ],
      //评标配置
      bidEvaluationConfig: {
        bidEvaluationType: 1, //评标方式
        businessWeight: 0, //	商务权重
        techWeight: 0 //	技术权重
      },
      //开标配置
      bidOpening: {
        bidOpenPwd: false, //开标密码 true 选择 false 不选择
        bidOpenTime: false, //	开标时间 true 选择 false 不勾选
        bidOpenTimeValue: 0, //开标时间勾选值
        sealedPrice: false //是否密封价格 true 是 false 否
      },
      //	决标策略配置
      biddingStrategyConfig: {
        biddingStrategy: 1 //	决标策略
      },
      //	跨轮次询报价配置
      crossRoundQuotationConfig: {
        addSupplier: 1, //增加供应商
        eliminateSupplier: 1, //淘汰供应商
        enableSupplier: 1, //	禁用/启用供应商
        quotationNextCondition: 1, //报价轮触发下一轮条件 1 手动 2自动 3手动+自动
        quotationRound: -1, //允许报价轮数 -1:不限制 >=0 指定轮次
        quotationRule: '' //自动规则 a*b#1
      },
      //	轮次内询报价配置
      innerRoundQuotationConfig: {
        counterOffer: false, //允许还价 true 允许 false 不允许
        openQuotation: 1, //报价时公开
        quotationEnd: '1', //报价结束 1&2 1|2
        quotationForm: 1, //	报价方式
        quotationNum: -1, //每轮报价次数 -1 不限制 >=0 指定次数
        quotationRange: 1, //	报价范围
        quotationType: 1 //按寻源行/成本模型报价 1 寻源行 2 成本模型
      },
      //立项策略配置
      projectStrategyConfig: {
        maxSupplier: 0, //	供应商数量至多
        minSupplier: 0 //供应商数量至少
      },
      //工作流配置
      workFlowConfig: {
        flowDesc: '', //	工作流描述
        flowId: '', //
        flowName: '' //	工作流名称
      },
      //扩展参数：如指定轮次时
      extConfig: {
        quotationRound: 0, //允许报价轮数 -1:不限制 >=0 指定轮次
        quotationNum: 0 //每轮报价次数 -1 不限制 >=0 指定次数
      },
      //扩展参数：用于RFX大厅策略报告状态下的参数
      extStrategyConfig: {
        extQuotationStartTime: null, //"报价开始",
        extQuotationEndTime: null, //"报价结束",
        extBidOpenTime: null, //"设置开标时间",
        extBidOpenUser: null, //"设置开标人",
        extEvaluationBusinessUser: null, //"商务评标人",
        extEvaluationTechUser: null //"技术评标人",
      },
      //允许报价轮数 列表
      quotationRoundList: [
        { text: this.$t('不限制'), value: -1 },
        { text: this.$t('指定轮次'), value: 0 }
      ],
      //每轮报价次数 列表
      eachQuotationNumList: [
        { text: this.$t('不限制'), value: -1 },
        { text: this.$t('指定'), value: 0 }
      ],
      //是否允许还价
      counterOfferList: [
        { text: this.$t('允许'), value: true },
        { text: this.$t('不允许'), value: false }
      ],
      //是否密封价格
      sealedPriceList: [
        { text: this.$t('是'), value: true },
        { text: this.$t('否'), value: false }
      ],
      //是否设置开标时间
      bidOpenTimeList: [
        { text: this.$t('设置'), value: true },
        { text: this.$t('不设置'), value: false }
      ],
      //是否开标密码
      bidOpenPwdList: [
        { text: this.$t('设置'), value: true },
        { text: this.$t('不设置'), value: false }
      ],
      sliderValue: 30,
      workFlowDataList: [],
      doNextRfxList: [
        { text: this.$t('手工'), value: 1 },
        { text: this.$t('自动'), value: 2 },
        { text: this.$t('手工+自动'), value: 3 }
      ],
      nextRfxRule: [
        {
          groupLeft: '(',
          dataSource: '',
          dataValue: '',
          symbol: '=',
          groupRight: '',
          groupLink: 'or'
        }
      ],
      nextRfxDataSource: [
        { text: this.$t('至少三条有效报价'), value: '1', type: null },
        { text: this.$t('最低报价未达到预算价'), value: '2', type: null },
        { text: this.$t('有效报价少于'), value: '3', type: 'text' }
      ],
      quotationEndRule: [
        {
          groupLeft: '(',
          dataSource: '1',
          dataValue: '',
          symbol: '=',
          groupRight: '',
          groupLink: 'or'
        }
      ],
      quotationEndRuleDataSource: [
        { text: this.$t('手工'), value: '1', type: null },
        { text: this.$t('按时间'), value: '2', type: null }
      ]
    }
  },
  computed: {
    getCurrentFlowId() {
      return this.flowModel?.processDefId
        ? this.flowModel?.processDefId
        : this.flowUseModel?.processDefId
    },
    getSelectDataSource() {
      return (key) => {
        let _map = this.selectMaps[key]
        let _res = []
        for (let i in _map) {
          _res.push({ text: _map[i], value: +i })
        }
        return _res
      }
    },
    getBidEvaluationSliderValue() {
      //获取技术权重
      return this.bidEvaluationConfig.techWeight
    },
    showFlowPoint() {
      return (key) => {
        if (this.currentSelectPoint && key) {
          let _find = this.flowPointsMap.find((e) => e.taskType === this.currentSelectPoint)
          let _rules = _find?.rules
          if (Array.isArray(_rules) && _rules.length > 0) {
            return _rules.indexOf(key) > -1
          } else {
            return false
          }
        } else {
          return false
        }
      }
    }
  },
  mounted() {
    this.getStrategyMapsSelectData()
    this.getFlowTaskList()
  },
  methods: {
    selectFlowPoint(e) {
      this.currentSelectPoint = e
    },
    async getSaveEditorInfo() {
      let _modelData = await this.$refs.bpmnEditorRef.getXML()
      let _query = {
        category: 0,
        data: _modelData,
        // key: uuidv1(),
        // key: `flow_${new Date().getTime()}`,
        //key: `flow_${uuidv1().replace(/-/g, "_")}`,
        key: `flow_${uuidv1().replace(/-/g, '_')}`.substring(0, 20),
        name: this.modelName,
        // tenantId: "",
        type: 'ProcessModel'
      }
      return new Promise((resolve) => {
        this.$API.workFlowService.addTemplatePage(_query).then((res) => {
          resolve(res.data)
        })
      })
    },
    async getPublishEditorInfo() {
      let _modelData = await this.$refs.bpmnEditorRef.getXML()
      let _query = {
        data: _modelData,
        name: this.modelName,
        id: this.flowModel.processDefId
      }
      return new Promise((resolve) => {
        this.$API.workFlowService.publishTemplatePage(_query).then((res) => {
          resolve(res.data)
        })
      })
    },
    async getUsePublishEditorInfo() {
      let _modelData = await this.$refs.bpmnEditorRef.getXML()
      let _query = {
        data: _modelData,
        name: this.modelName,
        id: this.flowUseModel.processDefId
      }
      return new Promise((resolve) => {
        this.$API.workFlowService.publishTemplatePage(_query).then((res) => {
          resolve(res.data)
        })
      })
    },
    //获取所有Task节点列表
    getFlowTaskList() {
      this.$API.commonConfig.getBaseTasks().then((res) => {
        if (Array.isArray(res.data)) {
          this.flowPointsMap = this.serializePointData(res.data)
        } else {
          this.flowPointsMap = []
        }
      })
    },
    serializePointData(_list) {
      _list.forEach((e) => {
        let _rules = null
        switch (e.taskType) {
          case 'BUSINESS_BID_PRICE': //商务报价
            _rules = [
              'quotationForm', //报价方式
              'openQuotation', //报价时公开
              'quotationEndRule' //报价结束
            ]
            if (this.strategyMode === 'RFX') {
              _rules = _rules.concat([
                'extQuotationStartTime', //报价开始
                'extQuotationEndTime' //报价结束
              ])
            }
            break
          case 'BUSINESS_OPEN_BID': //商务开标
            _rules = [
              'sealedPrice', //是否密封价格
              'bidOpenTime', //开标时间
              'bidOpenTimeValue', //设置开标时间
              'bidOpenPwd' //开标密码
            ]
            if (this.strategyMode === 'RFX') {
              _rules = _rules.concat([
                'extBidOpenTime', //设置开标时间
                'extBidOpenUser' //设置开标人
              ])
            }
            break
          case 'COMPARE_PRICE': //商务比价
            _rules = [
              'counterOffer' //允许还价
            ]
            break
          case 'EVALUATION_BID': //评标
            _rules = [
              'bidEvaluationType', //评标方式
              'sliderValue' //评分权重
            ]
            if (this.strategyMode === 'RFX') {
              _rules = _rules.concat([
                'extEvaluationBusinessUser', //商务评标人
                'extEvaluationTechUser' //技术评标人
              ])
            }
            break
          case 'DECIDE_PRICE': //定点
            _rules = [
              'biddingStrategy' //决标策略
            ]
            break
          case 'PUBLISH_TENDER': //发布招标
            _rules = [
              'quotationRound', //允许报价轮数
              'extQuotationRound', //轮次
              'quotationNextCondition', //报价触发下一轮条件流程
              'nextRfxRule', //自动规则
              'enableSupplier', //禁用/启用供应商
              'quotationRange', //报价范围
              'minSupplier', //供应商数量至少
              'maxSupplier' //供应商数量至多
            ]
            break
          default:
            _rules = []
            break
        }
        e.rules = _rules
      })
      return _list
    },
    serializeRuleData(_rule) {
      let _res = []
      if (_rule && _rule.trim()) {
        let _orRulesList = _rule.split('|')

        for (let i in _orRulesList) {
          let _andRuleList = _orRulesList[i].split('&')
          let _tempRes = []
          for (let j in _andRuleList) {
            let _singleRule = _andRuleList[j]
            let _d = '',
              _v = ''

            if (_singleRule.indexOf('#') > -1) {
              let _value = _singleRule.split('#')
              _d = _value[0] //dataSource
              _v = _value[1] //value
            } else {
              _d = _singleRule
            }
            _tempRes.push({
              groupLeft: '',
              dataSource: _d,
              dataValue: _v,
              symbol: '=',
              groupRight: '',
              groupLink: 'and'
            })
          }
          _tempRes[0]['groupLink'] = 'or'
          _res = _res.concat(_tempRes)
        }
      }
      //序列化的数组，有数据，则直接使用。无数据，则返回一条空数据
      return _res.length
        ? _res
        : [
            {
              groupLeft: '(',
              dataSource: '',
              dataValue: '',
              symbol: '=',
              groupRight: '',
              groupLink: 'or'
            }
          ]
    },
    checkQuotationEndRule() {
      let e = this.$refs.quotationEndRuleRef.getValidResult()
      if (e.ruleIsOk) {
        let { rules } = e,
          res = []
        for (let i in rules) {
          let _d = rules[i]['dataSource']
          let _l = rules[i]['groupLink'] == 'or' ? '|' : '&'
          let _temp = i < 1 ? _d : `${_l}${_d}`
          res.push(_temp)
        }
        this.$set(this.innerRoundQuotationConfig, 'quotationEnd', res.join(''))
      } else {
        this.$toast({
          content: this.$t('报价结束规则，设置有误。'),
          type: 'warning'
        })
      }
      return e.ruleIsOk
    },
    checkQuotationRule() {
      let e = this.$refs.quotationRuleRef.getValidResult()
      if (e.ruleIsOk) {
        let { rules } = e,
          res = []
        for (let i in rules) {
          let _d = rules[i]['dataSource']
          let _v = rules[i]['dataValue']
          let _l = rules[i]['groupLink'] == 'or' ? '|' : '&'
          let _value = _d == 3 ? `${_d}#${_v}` : _d
          let _temp = i < 1 ? _value : `${_l}${_value}`
          res.push(_temp)
        }
        this.$set(this.crossRoundQuotationConfig, 'quotationRule', res.join(''))
      } else {
        this.$toast({
          content: this.$t('报价触发下一轮条件流程，自动规则，设置有误。'),
          type: 'warning'
        })
      }
      return e.ruleIsOk
    },
    getStrategyMapsSelectData() {
      this.$API.strategyConfig.getRfxStrategySelectData().then((res) => {
        this.selectMaps = { ...res.data }
      })
    },
    getModelInfo() {
      if (this.strategyType === 'RFX') {
        //使用策略
        //新增时，rfxStrategyModel中无ID，需要先保存  发布   id key
        return new Promise((resolve) => {
          if (!this.flowUseModel?.processDefId) {
            //无ID  新的模型
            this.getSaveEditorInfo()
              .then((e) => {
                this.flowUseModel = {
                  processDefId: e.id,
                  processDefKey: e.key
                }
                return this.getUsePublishEditorInfo()
              })
              .then((e) => {
                this.flowUseModel = {
                  processDefId: e.id,
                  processDefKey: e.key
                }
                resolve(this.flowUseModel)
              })
          } else {
            this.getUsePublishEditorInfo().then((e) => {
              this.flowUseModel = { processDefId: e.id, processDefKey: e.key }
              resolve(this.flowUseModel)
            })
          }
        })
      } else {
        //配置策略
        //新增时，无ID，需要先保存  发布   id key
        return new Promise((resolve) => {
          if (!this.flowModel?.processDefId) {
            //无ID  新的模型
            this.getSaveEditorInfo()
              .then((e) => {
                this.flowModel = { processDefId: e.id, processDefKey: e.key }
                return this.getPublishEditorInfo()
              })
              .then((e) => {
                this.flowModel = { processDefId: e.id, processDefKey: e.key }
                resolve(this.flowModel)
              })
          } else {
            this.getPublishEditorInfo().then((e) => {
              this.flowModel = { processDefId: e.id, processDefKey: e.key }
              resolve(this.flowModel)
            })
          }
        })
      }
    },
    getStrategyConfigData() {
      if (!this.checkQuotationEndRule()) {
        return
      }
      if (this.crossRoundQuotationConfig?.quotationNextCondition > 1) {
        //设置了自动规则(报价触发下一轮条件流程)
        if (!this.checkQuotationRule()) {
          return
        }
      }
      let _crossRoundQuotationConfig = { ...this.crossRoundQuotationConfig }
      if (_crossRoundQuotationConfig?.quotationRound > -1) {
        //指定轮次 使用extConfig中的数据
        _crossRoundQuotationConfig.quotationRound = this.extConfig.quotationRound
      }
      let _innerRoundQuotationConfig = { ...this.innerRoundQuotationConfig }
      if (_innerRoundQuotationConfig.quotationNum > -1) {
        //指定轮次 使用extConfig中的数据
        _innerRoundQuotationConfig.quotationNum = this.extConfig.quotationNum
      }
      let strategyConfigInfo = {
        bidEvaluationConfig: this.bidEvaluationConfig, //评标配置
        bidOpening: this.bidOpening, //开标配置
        biddingStrategyConfig: this.biddingStrategyConfig, //	决标策略配置
        crossRoundQuotationConfig: _crossRoundQuotationConfig, //	跨轮次询报价配置
        innerRoundQuotationConfig: _innerRoundQuotationConfig, //	轮次内询报价配置
        projectStrategyConfig: this.projectStrategyConfig, //立项策略配置
        workFlowConfig: this.workFlowConfig //工作流配置
      }
      return strategyConfigInfo
    },
    handleWorkFlowListChange(e) {
      let _item = e.itemData
      this.workFlowConfig.flowDesc = _item.key
      this.workFlowConfig.flowName = _item.name
      this.workFlowConfig.flowId = _item.id
    },
    changedSlider(e) {
      this.bidEvaluationConfig.techWeight = e.value
      this.bidEvaluationConfig.businessWeight = 100 - e.value
    },
    redirectToFlowWebsite() {
      const env = location.host.split('.')[1]
      let url = '//workflow.'
      if (env === 'dev') {
        url += 'dev.qeweb.com'
      } else if (env === 'demo') {
        url += 'demo.qeweb.com'
      } else {
        url += 'dev.qeweb.com'
      }

      url = `${url}/#/?id=${this.workFlowConfig.flowId}`
      window.open(url)
    }
  },
  watch: {
    strategyModel: {
      handler(n) {
        this.flowModel = n
      },
      deep: true
    },
    strategyUseModel: {
      handler(n) {
        this.flowUseModel = n
      },
      deep: true
    },
    /**
     * 代码回退 -> 93d322afa7206a876ee37ec0c77ed692e2725247
     */
    strategyConfig: {
      handler(n) {
        let {
          bidEvaluationConfig,
          bidOpening,
          biddingStrategyConfig,
          crossRoundQuotationConfig,
          innerRoundQuotationConfig,
          projectStrategyConfig,
          workFlowConfig
        } = n
        let _crossRoundQuotationConfig = JSON.parse(JSON.stringify(crossRoundQuotationConfig))
        if (_crossRoundQuotationConfig?.quotationRound > -1) {
          //指定轮次 初始化extConfig中的数据
          this.$set(this.extConfig, 'quotationRound', _crossRoundQuotationConfig.quotationRound)
          crossRoundQuotationConfig.quotationRound = 0
        } else {
          this.$set(this.extConfig, 'quotationRound', 0)
        }

        if (_crossRoundQuotationConfig?.quotationNextCondition > 1) {
          //设置了自动规则，反序列化
          let _rule = _crossRoundQuotationConfig.quotationRule
          this.$set(this, 'nextRfxRule', this.serializeRuleData(_rule))
        }

        let _innerRoundQuotationConfig = JSON.parse(JSON.stringify(innerRoundQuotationConfig))
        if (_innerRoundQuotationConfig?.quotationNum > -1) {
          //指定轮次 初始化extConfig中的数据
          this.$set(this.extConfig, 'quotationNum', _innerRoundQuotationConfig.quotationNum)
          innerRoundQuotationConfig.quotationNum = 0
        } else {
          this.$set(this.extConfig, 'quotationNum', 0)
        }

        if (_innerRoundQuotationConfig?.quotationEnd) {
          //设置了结束报价规则-反序列化
          let _rule = _innerRoundQuotationConfig.quotationEnd
          this.$set(this, 'quotationEndRule', this.serializeRuleData(_rule))
        }
        this.bidEvaluationConfig = bidEvaluationConfig
        this.bidOpening = bidOpening
        this.biddingStrategyConfig = biddingStrategyConfig
        this.crossRoundQuotationConfig = crossRoundQuotationConfig
        this.innerRoundQuotationConfig = innerRoundQuotationConfig
        this.projectStrategyConfig = projectStrategyConfig
        this.workFlowConfig = workFlowConfig
      },
      deep: true
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .bpmn-editor-disabled {
  //.layout-header,
  .layout-content__left,
  .djs-context-pad {
    display: none;
  }
  .layout-header {
    .module-item--group {
      &:first-of-type {
        display: none;
      }
    }
  }
}
/deep/ .mt-tabs {
  display: none;
}
// /deep/ .mt-tabs {
//   width: 100%;
//   height: 100%;
//   align-items: baseline;
//   position: relative;
//   .e-tab {
//     height: 100% !important;
//     overflow-y: auto;
//   }
// }
// /deep/ .mt-form {
//   padding-top: 20px;
// }
// /deep/ .strategy-element {
//   border: none;
//   border-color: transparent !important;
//   position: relative;
//   top: 4px;
//   height: 40px;
//   background: #fafafa;
//   padding: 0 20px;
//   display: flex;
//   align-items: center;
//   top: 4px;
//   &:before,
//   &:after {
//     display: none;
//   }
//   .e-float-line {
//     display: none;
//   }
//   .e-control {
//     color: #292929;
//     border: none;
//     border-color: transparent !important;
//   }
//   .e-spin-down {
//     position: absolute;
//     right: -5px;
//     top: 9px;
//   }
//   .e-spin-up {
//     position: absolute;
//     right: -1px;
//     top: -4px;
//   }
//   .e-ddl-icon,
//   .e-date-icon {
//     position: absolute;
//     right: 15px;
//     top: calc(50% - 13px);
//   }
// }
// .rules-container {
//   padding: 20px;

//   .strategy-score-slider {
//     width: 100%;
//     position: relative;
//     margin-top: 15px;
//     .slider-value {
//       font-size: 12px;
//       color: #9daabf;
//       position: absolute;
//       top: -5px;
//       &.right-value {
//         right: 0;
//       }
//     }
//     /deep/ .e-slider-track {
//       height: 6px;
//       background: rgba(250, 250, 250, 1);
//       border-radius: 4px;
//       box-shadow: inset 0 1px 2px 0 rgba(232, 232, 232, 1);
//     }
//     /deep/ .e-handle {
//       width: 24px;
//       height: 24px;
//       background: radial-gradient(
//         circle at 41.61% 37.86%,
//         rgb(243, 186, 103) 0%,
//         rgb(237, 161, 51) 100%
//       );
//       border: 2px solid rgba(0, 0, 0, 0.1);
//       top: calc(50% - 12px);
//       &.e-large-thumb-size {
//         transform: scale(1);
//       }
//     }
//   }
//   .user-item {
//     width: 270px;
//     height: 80px;
//     border-radius: 4px;
//     background: #fafafa;
//     margin-bottom: 20px;
//     margin-right: 20px;
//     border-radius: 4px;
//     border: 1px solid #e8e8e8;
//     position: relative;
//     .mt-icons {
//       color: #ff0020;
//       position: absolute;
//       right: -8px;
//       top: -9px;
//       font-size: 16px;
//       cursor: pointer;
//     }
//     .user-element-container {
//       border: none;
//       position: relative;
//       &:first-of-type {
//         &:after {
//           content: "";
//           width: 230px;
//           position: absolute;
//           left: 20px;
//           bottom: 0;
//           border-top: 1px solid #e8e8e8;
//         }
//       }
//       /deep/ .user-value-template {
//         .user-name {
//           font-size: 14px;
//           font-family: PingFangSC;
//           font-weight: normal;
//           color: rgba(41, 41, 41, 1);
//         }
//         .user-desc {
//           font-size: 12px;
//           font-family: PingFangSC;
//           font-weight: normal;
//           color: rgba(154, 154, 154, 1);
//         }
//       }
//     }
//   }
// }
</style>
