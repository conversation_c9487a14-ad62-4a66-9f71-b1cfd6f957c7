/**
*   1. 自定义变量的名称要注意命名规范，做到见名知意。强制要求是前缀必须是所属项目的缩写,缩写的名字可有项目负责人确定，例如 mpw 表示 mtech-platform-web
*
*/

// @import '/node_modules/@mtech-ui/base/build/themes/default.scss';   // UI 库中的基础变量
// @import '/node_modules/@mtech-ui/button/build/themes/default.scss';     // UI 库中Button组件的基础变量
// @import '/node_modules/@mtech-ui/mtech-ui/build/themes/default.scss';     // 引入mtechUI组件中的 default 主题样式
// // 引入mtechUI组件中的 default 主题样式
// :root {
//   --accent: #00469c;    // --accent 为mtechUI组件中提供的变量，如果项目中需要对其进行覆盖，可以在此处重写
//   --mpw-personal-address-border-color: var(--accent);    // 项目中自定义的变量
//   --mpw-base-font-primary-color: #{rgba($base-font,0.87)};  // 混用 scss 变量的写法
// }

// :root {     // 重写 mtech-common-layout 中的变量
//   --mcl-left-nav-sidebar-bg-color: var(--grey-100);
// }
