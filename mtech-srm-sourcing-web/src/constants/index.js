// import Vue from "vue";
// // 国际化
// import indexDB from "@digis/internationalization";
// const i18n = indexDB.digisI18n(Vue, "sourcing"); //第二个参数是当前使用的应用code
let i18n = require('@/main.js').i18n
console.log('i18n，值', i18n)
if (!i18n) {
  i18n = {
    t: (e) => e
  }
}
export let PriceTypeMap = [i18n.t('到物料'), i18n.t('到SKU'), i18n.t('到品类'), i18n.t('到供应商')]

export const PriceTypeList = PriceTypeMap.map((type, index) => ({
  text: type,
  value: index
}))

export const DifferentialPricingTypeMap = [i18n.t('数量累计'), i18n.t('时间'), i18n.t('金额')]

export const DifferentialPricingTypeList = DifferentialPricingTypeMap.map((type, index) => ({
  text: type,
  value: index
}))

export const ApproveStatusMap = {
  canceled: { code: -3, label: i18n.t('取消审批') },
  rejected: { code: -2, label: i18n.t('驳回') },
  closed: { code: -1, label: i18n.t('关闭') },
  draft: { code: 0, label: i18n.t('草稿') },
  pending: { code: 1, label: i18n.t('待审批') },
  approved: { code: 2, label: i18n.t('审批通过') },
  control: { code: 3, label: i18n.t('控制') }
}

export const ApproveStatusCodeMap = {
  '-3': i18n.t('取消审批'),
  '-2': i18n.t('驳回'),
  '-1': i18n.t('关闭'),
  0: i18n.t('草稿'),
  1: i18n.t('待审批'),
  2: i18n.t('审批通过'),
  3: i18n.t('控制')
}

//询价模型  列表
export const sourcingObjTypeList = [
  { text: i18n.t('通用物料'), value: 'common' },
  { text: i18n.t('组合结构'), value: 'combination' },
  // { text: i18n.t("CKD"), value: "ckd" },
  // { text: i18n.t("部品+模具"), value: "item_mould" },
  { text: i18n.t('父子结构'), value: 'HIERARCHY' },
  { text: i18n.t('海运'), value: 'sea_transport' },
  { text: i18n.t('空运'), value: 'air_transport' },
  { text: i18n.t('铁路'), value: 'railway_transport' },
  { text: i18n.t('陆运'), value: 'land_transport' },
  { text: i18n.t('拖车'), value: 'trailer_transport' },
  { text: i18n.t('外发业务-电源板'), value: 'power_panel' },
  { text: i18n.t('外发业务-SMT'), value: 'smt' },
  { text: i18n.t('外发业务-整机模组外购'), value: 'module_out_buy' },
  { text: i18n.t('外发业务-整机模组外发'), value: 'module_out_going' },
  { text: i18n.t('成本因子'), value: 'cost_factor' },
  { text: i18n.t('系列物料'), value: 'serial_item' },
  { text: i18n.t('结构件'), value: 'structure_component' },
  { text: i18n.t('美工件'), value: 'artist_component' },
  { text: i18n.t('单独模具'), value: 'single_module' },
  { text: i18n.t('海外CKD'), value: 'COMMON_OVERSEA_CKD' },
  { text: i18n.t('ODMIN整机采购'), value: 'odmin' },
  { text: i18n.t('海运（年约需求）'), value: 'sea_transport_annual' }
]

export const TEC_BID_ING = 38
export const WAIT_TEC_OPEN_BID = 39
export const EVALUATION_TEC_BID_ING = 40
export const BUSINESS_BID_ING = 41
export const WAIT_BUSINESS_OPEN_BID = 42
export const EVALUATION_BUSINESS_BID_ING = 43

export const transferStatusList = {
  0: i18n.t('立项准备'),
  1: i18n.t('待发布'),
  3: i18n.t('待报名'),
  5: i18n.t('待报价'),
  6: i18n.t('待开标'),
  7: i18n.t('待评标'),
  8: i18n.t('待核价'),
  9: i18n.t('待比价'),
  10: i18n.t('待提交定点'),
  15: i18n.t('待缴纳保证金'),
  20: i18n.t('已提交定点'),
  25: i18n.t('待接收报价'),
  30: i18n.t('待供应商评审'),
  31: i18n.t('报价中'),
  32: i18n.t('议价中'),
  33: i18n.t('定价中'),
  34: i18n.t('定标中'),
  35: i18n.t('报名中'),
  36: i18n.t('投标中'),
  37: i18n.t('评标中'),
  //毛正文修改
  44: i18n.t('开标核价中'),
  45: i18n.t('财务核价中'),
  46: i18n.t('核价目标价中'),
  [TEC_BID_ING]: i18n.t('技术投标中'),
  [WAIT_TEC_OPEN_BID]: i18n.t('待技术开标'),
  [EVALUATION_TEC_BID_ING]: i18n.t('技术评标中'),
  [BUSINESS_BID_ING]: i18n.t('商务投标中'),
  [WAIT_BUSINESS_OPEN_BID]: i18n.t('待商务开标'),
  [EVALUATION_BUSINESS_BID_ING]: i18n.t('商务评标中'),
  100: i18n.t('已完成')
}

export const rfxDocStatusList = {
  '-5': i18n.t('审批废弃'),
  '-4': i18n.t('已取消'),
  '-2': i18n.t('撤回'),
  '-1': i18n.t('关闭'),
  0: i18n.t('草稿'),
  1: i18n.t('报价中'),
  2: i18n.t('放弃报价'),
  3: i18n.t('超时未报价'),
  4: i18n.t('开标核价中'),
  5: i18n.t('财务核价中'),
  6: i18n.t('目标价核价中'),
  7: i18n.t('议价'),
  8: i18n.t('定价'),
  9: i18n.t('定标审批中'),
  10: i18n.t('驳回至定价'),
  11: i18n.t('驳回至重新询价'),
  12: i18n.t('SAP同步中'),
  13: i18n.t('推送SAP失败'),
  14: i18n.t('已完成'),
  30: i18n.t('已全部报价')
}
export const bidDocStatusList = {
  '-5': i18n.t('审批废弃'),
  '-3': i18n.t('流标'),
  '-1': i18n.t('关闭'),
  0: i18n.t('草稿'),
  7: i18n.t('议价'),
  8: i18n.t('定价'),
  9: i18n.t('定标审批中'),
  14: i18n.t('已完成'),
  16: i18n.t('应标中'),
  17: i18n.t('立项驳回'),
  18: i18n.t('技术投标中'),
  19: i18n.t('待技术开标'),
  20: i18n.t('技术评标中'),
  21: i18n.t('商务投标中'),
  22: i18n.t('待商务开标'),
  23: i18n.t('商务评标中'),
  24: i18n.t('定标中'),
  25: i18n.t('资质审查'),
  26: i18n.t('立项审批中'),
  27: i18n.t('投标中'),
  28: i18n.t('评标中'),
  29: i18n.t('待开标'),
  30: i18n.t('已全部报价')
}
export const approveStatusList = {
  '-4': i18n.t('审批废弃'),
  '-3': i18n.t('取消审批'),
  '-2': i18n.t('立项审批驳回'),
  '-1': i18n.t('关闭'),
  0: i18n.t('草稿'),
  1: i18n.t('立项待审核'),
  2: i18n.t('立项审批通过'),
  3: i18n.t('控制'),
  4: i18n.t('晋级商务标审批通过'),
  5: i18n.t('晋级商务标审批待审核'),
  6: i18n.t('晋级商务标审批驳回'),
  7: i18n.t('定标审批通过'),
  8: i18n.t('定标待审核'),
  9: i18n.t('定标审批驳回'),
  10: i18n.t('审批废弃')
}

export const SUMMARY_STATUS = {
  0: i18n.t('待报价'),
  1: i18n.t('部分报价'),
  2: i18n.t('全部报价'),
  3: i18n.t('资格预审驳回'),
  10: i18n.t('待提交资格预审'),
  11: i18n.t('资格预审中'),
  12: i18n.t('资格预审通过'),
  13: i18n.t('资格预审驳回'),
  20: i18n.t('待投技术标'),
  21: i18n.t('已投部分技术标'),
  22: i18n.t('已投技术标'),
  30: i18n.t('未入围'),
  31: i18n.t('已入围'),
  40: i18n.t('待投商务标'),
  41: i18n.t('已投部分商务标'),
  42: i18n.t('已投商务标'),
  50: i18n.t('待议价'),
  51: i18n.t('已议价'),
  60: i18n.t('未中标'),
  61: i18n.t('部分中标'),
  62: i18n.t('全部中标')
}

// export const DRAFT = 0; // 草稿
// export const IN_PROGRESS = 1; // 进行中
// export const PAUSE = 2; // 暂停
// export const NEW_ROUND = 4; // 新轮次
// export const FINISH = 8; // 已完成
// export const CLOSE = -1; // 关闭
// export const CANCEL = -2; // 撤回
// export const FAIL_BID = -8; // 流标

export const RFX_STATUS = {
  0: i18n.t('草稿'),
  1: i18n.t('进行中'),
  2: i18n.t('暂停'),
  3: i18n.t('关闭'),
  4: i18n.t('新轮次'),
  8: i18n.t('已完成'),
  10: i18n.t('已放弃'),
  '-1': i18n.t('关闭'),
  '-2': i18n.t('撤回'),
  '-8': i18n.t('流标')
}

//专家角色  列表
export const expertRuleList = [
  { text: i18n.t('技术专家'), value: 'tech_expert' },
  { text: i18n.t('商务专家'), value: 'bus_expert' },
  // { text: i18n.t("技术&商务专家"), value: "tech_bus_expert" },
  { text: i18n.t('开标人'), value: 'open_bid_peo' },
  { text: i18n.t('需求人'), value: 'demander' },
  { text: i18n.t('采购员'), value: 'buyer' },
  { text: i18n.t('研发人员'), value: 'research_peo' }
]

// 表格 column 的主数据选择 searchOptions 的配置 默认配置
// searchable: true, // 默认 可搜索
// 可多选字段：行政公司，业务公司，工厂，物料，状态，供应商，客户，工作中心，库存地点
export const searchOptionsList = {
  // 时间区间选择
  timeRange: {
    elementType: 'date-range',
    operator: 'between',
    serializeValue: (e) => {
      let obj = e.map((x) => Number(new Date(x.toString())))
      obj[1] = obj[1] + Number(86399000)

      //自定义搜索值，规则
      return obj
    }
  },
  // 日期范围选择 00:00:00 - 23:59:59
  dateRange: {
    elementType: 'date-range',
    operator: 'between',
    serializeValue: (e) => {
      //自定义搜索值，规则
      return e.map((x, i) => {
        if (i === 1) {
          return Number(new Date(x.toString().replace('00:00:00', '23:59:59')))
        }
        return Number(new Date(x.toString()))
      })
    }
  },
  // 单个时间的值：时间戳
  timeStamp: {
    elementType: 'date',
    serializeValue: (e) => {
      //自定义搜索值，规则
      return e.map((x) => Number(new Date(x.toString())))
    }
  },
  // 单个时间的值：非时间戳
  timeOnly: {
    elementType: 'datetime',
    dateFormat: 'YYYY-mm-dd HH:MM:SS'
  },
  // 单位
  unit: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'unit', // 货币
    fields: { text: 'title', value: 'unitCode' },
    inputAble: false
  },
  // 货币
  money: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'money', // 货币
    fields: { text: 'title', value: 'currencyCode' }
  },
  // 币种
  moneyType: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'money', // 货币
    renameField: 'currencyId',
    fields: { text: 'title', value: 'id' }
  },
  // 税率
  taxRate: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'taxRate', // 货币
    fields: { text: 'title', value: 'taxTypeCode' }, // taxTypeCode taxRate
    operator: 'equal'
  },
  // 税率
  saleTaxRate: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'taxRate', // 货币
    fields: { text: 'title', value: 'taxRate' }, // taxTypeCode taxRate
    operator: 'equal'
  },
  // 成本中心
  costCenter: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'costCenter', // 成本中心
    fields: { text: 'title', value: 'costCenterCode' }
  },
  // 利润中心
  profitCenter: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'profitCenter', // 利润中心
    fields: { text: 'title', value: 'profitCenterCode' }
  },
  // 行政公司
  administrativeCompany: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'administrativeCompany', // 行政公司
    fields: { text: 'title', value: 'orgCode' },
    params: {
      orgType: 'ORG001ADM',
      organizationLevelCodes: ['ORG01', 'ORG02']
    },
    multiple: true,
    operator: 'in'
  },
  // 业务公司
  businessCompany: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'administrativeCompany', // 业务公司
    fields: { text: 'title', value: 'orgCode' },
    params: {
      organizationLevelCodes: ['ORG01', 'ORG02']
    },
    multiple: true,
    operator: 'in'
  },
  // 部门
  department: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'department', // 部门
    fields: { text: 'title', value: 'departmentCode' }
  },
  // 岗位
  job: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'job', // 岗位
    fields: { text: 'title', value: 'stationCode' }
  },
  // 员工
  staff: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'staff', // 员工
    fields: { text: 'title', value: 'employeeCode' }
  },
  // 工厂
  factoryAddress: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'factoryAddress', // 工厂
    fields: { text: 'title', value: 'siteCode' },
    multiple: true,
    operator: 'in'
  },
  // VMI 仓
  vmiWarehouse: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'vmiWarehouse', // Vmi仓
    fields: { text: 'title', value: 'vmiWarehouseCode' },
    multiple: true,
    operator: 'in'
  },
  // 库存地点
  stockAddress: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'stockAddress', // 库存地点
    fields: { text: 'title', value: 'locationCode' },
    multiple: true,
    operator: 'in'
  },
  stockAddressName: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'stockAddress', // 库存地点
    fields: { text: 'title', value: 'locationName' },
    multiple: true,
    operator: 'in',
    extend: {
      mode: 'normal',
      selectType: 'stockAddress',
      name: '库存地点/编码',
      method: 'post',
      url: 'masterDataManagement/tenant/location/paged-query',
      searchFields: ['id', 'locationCode', 'locationDescription', 'locationName'],
      recordsPosition: 'data.records',
      input: (item) => `${item.locationCode} ${item.locationName}`,
      title: (item) => `${item.locationCode} ${item.locationName}`,
      title2: (item) => `${item.companyOrgName || ''}`, //TODO  缺公司1
      rulesAbled: true,
      statusAbled: false
    }
  },

  // 工作中心
  workCenter: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'workCenter', // 工作中心
    fields: { text: 'title', value: 'workCenterCode' },
    multiple: true,
    operator: 'in'
  },
  // 业务组
  businessGroup: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'businessGroup', // 业务组
    fields: { text: 'title', value: 'groupCode' }
  },
  // 业务组 多选
  businessGroupIn: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'businessGroup', // 业务组
    fields: { text: 'title', value: 'groupCode' },
    multiple: true,
    operator: 'in'
  },
  // 业务组织
  businessGroupUnit: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'businessGroupUnit', // 业务组织
    fields: { text: 'title', value: 'organizationCode' }
  },
  // 国家
  country: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'country', // 国家
    fields: { text: 'title', value: 'countryCode' }
  },
  // 收货地址
  receivingAddress: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'receivingAddress', // 收货地址
    fields: { text: 'title', value: 'TODO' }
  },
  // 供应商
  supplier: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    selectType: 'supplier', // 供应商
    fields: { text: 'title', value: 'supplierCode' },
    multiple: true,
    operator: 'in'
  },
  // sku
  sku: {
    elementType: 'masterdata-selects',
    type: 'dialog', // 弹框选择
    dialogType: 'sku', // sku
    operator: 'in'
  },
  // 物料
  material: {
    elementType: 'masterdata-selects',
    type: 'select', // 弹框选择
    selectType: 'material', // 物料
    multiple: true,
    operator: 'in',
    fields: { text: 'title', value: 'itemCode' }
  },
  // 货源关系
  supply: {
    elementType: 'masterdata-selects',
    type: 'dialog', // 弹框选择
    dialogType: 'supply', // 货源关系
    operator: 'in'
  },
  // 品类
  category: {
    elementType: 'masterdata-selects',
    type: 'dialog', // 弹框选择
    dialogType: 'category', // 品类
    operator: 'in'
  },
  // 品类 建议用这个
  categoryCode: {
    elementType: 'masterdata-selects',
    type: 'select',
    multiple: true,
    operator: 'in',
    fields: { text: 'title', value: 'categoryCode' },
    extend: {
      mode: 'normal',
      name: '品类',
      url: 'masterDataManagement/tenant/category/paged-query',
      method: 'post',
      searchFields: ['categoryCode', 'categoryName'],
      input: (item) => `${item.categoryCode} ${item.categoryName}`,
      title: (item) => `${item.categoryCode} ${item.categoryName}`,
      recordsPosition: 'data.records',
      rulesAbled: true,
      statusAbled: true
    }
  },
  // 字典 OrderType 订单类型
  dictOrderType: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    multiple: true,
    operator: 'in',
    fields: { text: 'title', value: 'itemCode' },
    extend: {
      mode: 'normal',
      method: 'post',
      url: 'masterDataManagement/tenant/dict-item/dict-code',
      searchUrl: '',
      searchFields: [],
      input: (item) => `${item.itemCode} ${item.itemName}`,
      title: (item) => `${item.itemCode} ${item.itemName}`,
      params: {
        dictCode: 'OrderType' // 字典类型编码
      },
      recordsPosition: 'data'
    }
  },
  // 字典 payMethod 付款方式
  dictPaymentMode: {
    elementType: 'masterdata-selects',
    type: 'select', // 下拉选择
    multiple: true,
    operator: 'in',
    fields: { text: 'title', value: 'itemCode' },
    extend: {
      mode: 'normal',
      method: 'post',
      url: 'masterDataManagement/tenant/dict-item/dict-code',
      searchUrl: '',
      searchFields: [],
      input: (item) => `${item.itemCode} ${item.itemName}`,
      title: (item) => `${item.itemCode} ${item.itemName}`,
      params: {
        dictCode: 'payMethod' // 字典类型编码
      },
      recordsPosition: 'data'
    }
  }
}
