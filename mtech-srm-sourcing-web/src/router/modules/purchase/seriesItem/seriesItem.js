/** 采方：系列物料 **/

const Router = [
  // {
  //   path: "series-item-formula-definition",
  //   name: "series-item-formula-definition", //系列物料公式定义
  //   component: () =>
  //     import(
  //       "ROUTER_PURCHASE_SETTINGS/seriesItem/seriesItemFormulaDefinition/list/index.vue"
  //     ),
  //   meta: {
  //     title: "series-item-formula-definition",
  //     keepAlive: true,
  //   },
  // },
  {
    path: 'series-item-price-management',
    name: 'series-item-price-management', //系列物料价格管理
    component: () => import('ROUTER_PURCHASE/seriesItem/seriesItemPriceManagement/list/index.vue'),
    meta: {
      title: 'series-item-price-management',
      keepAlive: true
    }
  },
  // {
  //   path: "series-item-price-management",
  //   name: "series-item-price-management", //系列物料价格管理
  //   component: () =>
  //     import(
  //       "ROUTER_PURCHASE/seriesItem/seriesItemPriceManagement/list/index.vue"
  //     ),
  //   meta: {
  //     title: "series-item-price-management",
  //     keepAlive: true,
  //   },
  // },
  {
    path: 'series-item-pricing',
    name: 'series-item-pricing', //系列物料定价
    component: () => import('ROUTER_PURCHASE/seriesItem/seriesItemPricing/list/index.vue'),
    meta: {
      title: 'series-item-pricing',
      keepAlive: true
    }
  },
  {
    path: 'series-item-pricing-detail',
    name: 'series-item-pricing-detail', //系列物料定价详情
    component: () => import('ROUTER_PURCHASE/seriesItem/seriesItemPricing/detail/index.vue'),
    meta: {
      title: 'series-item-pricing',
      keepAlive: true
    }
  }
]

export default Router
