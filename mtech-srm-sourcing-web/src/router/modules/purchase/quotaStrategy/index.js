/** 采方：配额管理 **/

const Router = [
  // 基础策略
  {
    path: 'quota-strategy',
    name: 'quota-strategy',
    component: () => import(/* webpackChunkName: "router/public" */ 'ROUTER_PUBLIC/index.vue'),
    meta: {
      title: 'quota-strategy',
      keepAlive: false
    },
    children: [
      {
        path: 'default', // 预设配额
        name: 'quota-strategy-default',
        component: () => import('@/views/purchase/quotaStrategy/default/index.vue')
      },
      {
        path: 'default-detail', // 预设配额 - 详情
        name: 'quota-strategy-default-detail',
        component: () => import('@/views/purchase/quotaStrategy/default/detail.vue')
      },
      {
        path: 'constraint', // 限制配额
        name: 'quota-strategy-constraint',
        component: () => import('@/views/purchase/quotaStrategy/constraint/index.vue')
      },
      {
        path: 'constraint-detail', // 限制配额 - 详情
        name: 'quota-strategy-constraint-detail',
        component: () => import('@/views/purchase/quotaStrategy/constraint/detail.vue')
      },
      {
        path: 'price-difference', // 价差标准
        name: 'quota-strategy-difference',
        component: () => import('@/views/purchase/quotaStrategy/priceDifference/index.vue')
      },
      {
        path: 'price-difference-detail', // 价差标准 - 详情
        name: 'quota-strategy-difference-detail',
        component: () => import('@/views/purchase/quotaStrategy/priceDifference/detail.vue')
      },
      {
        path: 'customization', // 自制配额
        name: 'quota-strategy-customization',
        component: () => import('@/views/purchase/quotaStrategy/customization/index.vue')
      },
      {
        path: 'exclude', // 配额例外
        name: 'quota-strategy-exclude',
        component: () => import('@/views/purchase/quotaStrategy/exclude/index.vue')
      },
      {
        path: 'exclude-detail', // 配额例外详情
        name: 'quota-strategy-exclude-detail',
        component: () => import('@/views/purchase/quotaStrategy/exclude/detail/index.vue')
      },
      {
        path: 'constraint-exclude', // 放行配额
        name: 'quota-strategy-constraint-exclude',
        component: () => import('@/views/purchase/quotaStrategy/constraintExclude/index.vue')
      },
      {
        path: 'constraint-exclude-detail', // 放行配额详情
        name: 'quota-strategy-constraint-exclude-detail',
        component: () => import('@/views/purchase/quotaStrategy/constraintExclude/detail/index.vue')
      },
      {
        path: 'priority', // 限制配额优先级配置
        name: 'quota-strategy-priority',
        component: () => import('@/views/purchase/quotaStrategy/priority/index.vue')
      },
      {
        path: 'agreement', // 协议配额
        name: 'quota-strategy-agreement',
        component: () => import('@/views/purchase/quotaStrategy/agreement/index.vue')
      },
      {
        path: 'agreement-item', // 协议配额 - 详情
        name: 'quota-strategy-agreement-item',
        component: () => import('@/views/purchase/quotaStrategy/agreement/detail.vue')
      },
      {
        path: 'agreement-detail', // 协议配额明细
        name: 'quota-strategy-agreement-detail',
        component: () => import('@/views/purchase/quotaStrategy/agreementDetail/index.vue')
      },
      {
        path: 'ranking', // 综合排名优先级配置
        name: 'quota-strategy-ranking',
        component: () => import('@/views/purchase/quotaStrategy/ranking/index.vue')
      }
    ]
  }
  // 配额制定与调整
  // {}
]

export default Router
