/** 模具制造审批表**/

const Router = [
  {
    path: 'purchase-mouldManagement-applicationForm', //模具制造审批表
    name: 'purchase-mouldManagement-applicationForm',
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/mouldManagement/applicationForm/index" */ '@/views/purchase/mouldManagement/applicationForm/index.vue'
      ),
    meta: {
      title: 'purchase-mouldManagement-applicationForm'
    }
  },
  {
    path: 'purchase-mouldManagement-applicationForm-detail', //模具制造审批表详情
    name: 'purchase-mouldManagement-applicationForm-detail',
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/mouldManagement/applicationForm/detail/index" */ '@/views/purchase/mouldManagement/applicationForm/detail/index.vue'
      ),
    meta: {
      title: 'purchase-mouldManagement-applicationForm-detail'
    }
  }
]

export default Router
