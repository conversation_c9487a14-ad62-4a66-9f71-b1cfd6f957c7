/** 采方：价格服务**/

const Router = [
  {
    path: 'purchase-price-service', // 菜单：价格服务列表
    name: 'purchase-price-service',
    // path: "purchase-price-list", // 菜单：价格服务列表
    // name: "purchase-price-list",
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/price/list" */ 'ROUTER_PURCHASE_PRICE/list/index.vue'
      ),
    meta: {
      title: 'purchase-price-service'
    }
  },
  {
    path: 'purchase-price-search', // 菜单：价格查询列表
    name: 'purchase-price-search',
    component: () => import('ROUTER_PURCHASE_PRICE/search/index.vue'),
    meta: {
      title: 'purchase-price-search'
    }
  },
  {
    path: 'purchase-price-trace', // 菜单：价格追溯列表
    name: 'purchase-price-trace',
    component: () => import('ROUTER_PURCHASE_PRICE/trace/index.vue'),
    meta: {
      title: 'purchase-price-trace'
    }
  },
  {
    path: 'pur-valuation-management', // 菜单：白电估价列表
    name: 'pur-valuation-management',
    component: () => import('ROUTER_PURCHASE_PRICE/purValuationManagement/index.vue'),
    meta: {
      title: 'pur-valuation-management'
    }
  },
  {
    path: 'pur-valuation-management-detail', // 菜单：白电估价详情
    name: 'pur-valuation-management-detail',
    component: () => import('ROUTER_PURCHASE_PRICE/purValuationManagement/detail/index.vue'),
    meta: {
      title: 'pur-valuation-management-detail'
    }
  },
  {
    path: 'pur-lcd-screen-sale-agreement', // 菜单：液晶屏销售协议
    name: 'pur-lcd-screen-sale-agreement',
    component: () => import('ROUTER_PURCHASE_PRICE/lcdScreenSaleAgreement/index.vue'),
    meta: {
      title: 'pur-lcd-screen-sale-agreement'
    }
  },
  {
    path: 'offer-outsourcing-inquiry', // 菜单：屏编码对照表
    name: 'offer-outsourcing-inquiry',
    component: () => import('ROUTER_PURCHASE/offerOutsourcingInquiry/index.vue'),
    meta: {
      title: 'offer-outsourcing-inquiry',
      keepAlive: true
    }
  },
  {
    path: 'pur-price-record', // 菜单：价格记录-新
    name: 'pur-price-record',
    component: () => import('ROUTER_PURCHASE_PRICE/priceRecord/index.vue'),
    meta: {
      title: 'pur-price-record',
      keepAlive: true
    }
  },
  {
    path: 'pur-price-record-logistics', // 菜单：物流价格记录 - 年约需求
    name: 'pur-price-record-logistics',
    component: () => import('ROUTER_PURCHASE_PRICE/logisticsPriceRecord/index.vue'),
    meta: {
      title: 'pur-price-record-logistics',
      keepAlive: true
    }
  },
  {
    path: 'manpower-outsourcing-pricing', // 菜单：人力外包定价
    name: 'manpower-outsourcing-pricing',
    component: () => import('ROUTER_PURCHASE_PRICE/manpowerOutsourcingPricing/index.vue'),
    meta: {
      title: 'manpower-outsourcing-pricing',
      keepAlive: true
    }
  },
  {
    path: 'manpower-outsourcing-pricing-detail', // 菜单：人力外包定价-明细
    name: 'manpower-outsourcing-pricing-detail',
    component: () =>
      import('ROUTER_PURCHASE_PRICE/manpowerOutsourcingPricing/tabs/pricing/detail.vue'),
    meta: {
      title: 'manpower-outsourcing-pricing-detail',
      keepAlive: true
    }
  },
  {
    path: 'pur-price-record-difference', // 菜单：价格记录差异
    name: 'pur-price-record-difference',
    component: () => import('ROUTER_PURCHASE_PRICE/priceRecordDifference/index.vue'),
    meta: {
      title: 'pur-price-record-difference',
      keepAlive: true
    }
  },
  {
    path: 'bom-file-management', // 菜单：bom 文件管理
    name: 'bom-file-management',
    component: () => import('ROUTER_PURCHASE_PRICE/bomFileManage/index.vue'),
    meta: {
      title: 'bom-file-management'
    }
  },
  {
    path: 'logistics-pricing', // 菜单：物流直接定价 - 列表页
    name: 'logistics-pricing',
    component: () => import('ROUTER_PURCHASE_PRICE/logisticsPricing/index.vue')
  },
  {
    path: 'logistics-pricing-detail', // 菜单：物流直接定价 - 详情页
    name: 'logistics-pricing-detail',
    component: () => import('ROUTER_PURCHASE_PRICE/logisticsPricing/detail.vue')
  }
]

export default Router
