/** 采方：定点管理 **/

const Router = [
  {
    path: 'fixed-point-list', //定点推荐
    name: 'fixed-point-list',
    // path: "purchase-fixed-point-recommend", //定点推荐
    // name: "purchase-fixed-point-recommend",
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/fixedPoint/recommend" */ 'ROUTER_PURCHASE_POINT/recommend/index.vue'
      ),
    meta: {
      title: 'fixed-point-list'
    }
  },
  {
    path: 'fixed-point-management', //定点管理
    name: 'fixed-point-management',
    // path: "purchase-fixed-point-management", //定点管理
    // name: "purchase-fixed-point-management",
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/fixedPoint/list" */ 'ROUTER_PURCHASE_POINT/list/index.vue'
      ),
    meta: {
      title: 'fixed-point-management'
    }
  },
  {
    path: 'fixed-point-detail', //定点详情
    name: 'fixed-point-detail',
    // path: "purchase-fixed-point-detail", //定点详情
    // name: "purchase-fixed-point-detail",
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/fixedPoint/detail-add" */ 'ROUTER_PURCHASE_POINT/detail/index.vue'
      ),
    meta: {
      title: 'fixed-point-detail'
    }
  },
  {
    path: 'fixed-point-detail-edit', //定点详情--修改
    name: 'fixed-point-detail-edit',
    // path: "purchase-fixed-point-detail-edit", //定点详情--修改
    // name: "purchase-fixed-point-detail-edit",
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/fixedPoint/detail-edit" */ 'ROUTER_PURCHASE_POINT/detail/edit.vue'
      ),
    meta: {
      title: 'fixed-point-detail-edit'
    }
  }
]

export default Router
