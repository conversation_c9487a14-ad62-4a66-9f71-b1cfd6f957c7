/** 定价管理**/

const Router = [
  {
    path: 'pricingManagement-nuclearPrice', //核价定价
    name: 'pricingManagement-nuclearPrice',
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/pricingManagement/nuclearPrice/index" */ '@/views/purchase/pricingManagement/nuclearPrice/index.vue'
      ),
    meta: {
      title: 'pricingManagement-nuclearPrice'
    }
  },
  {
    path: 'pricingManagement-nuclearPrice-detail', //核价定价-详情
    name: 'pricingManagement-nuclearPrice-detail',
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/pricingManagement/nuclearPrice/detail/index" */ '@/views/purchase/pricingManagement/nuclearPrice/detail/index.vue'
      ),
    meta: {
      title: 'pricingManagement-nuclearPrice-detail'
    }
  }
]

export default Router
