/** 采方：配额管理 **/

const Router = [
  // 配额制定与调整
  {
    path: 'quota-calc',
    name: 'quota-calc',
    component: () => import(/* webpackChunkName: "router/public" */ 'ROUTER_PUBLIC/index.vue'),
    meta: {
      title: 'quota-calc',
      keepAlive: false
    },
    children: [
      {
        path: 'item-scope-init', // 物料配额筛选确认
        name: 'item-scope-init',
        component: () => import('@/views/purchase/quotaCalc/itemScopeInit/index.vue')
      },
      {
        path: 'item-quota-commit', // 物料配额计算确认
        name: 'item-quota-commit',
        component: () => import('@/views/purchase/quotaCalc/itemQuotaCommit/index.vue')
      },
      {
        path: 'item-quota-submmit', // 物料配额制定审批
        name: 'item-quota-submmit',
        component: () => import('@/views/purchase/quotaCalc/itemQuotaSubmmit/index.vue')
      },
      {
        path: 'quota-detail', // 配额明细查询
        name: 'quota-detail',
        component: () => import('@/views/purchase/quotaCalc/quotaDetail/index.vue')
      },
      {
        path: 'item-quota-submmit-detail', // 物料配额制定审批详细
        name: 'item-quota-submmit-detail',
        component: () => import('@/views/purchase/quotaCalc/itemQuotaSubmmit/detail/index.vue')
      },
      {
        path: 'quota-apply', // 配额申请
        name: 'quota-apply',
        component: () => import('@/views/purchase/quotaCalc/quotaApply/index.vue')
      },
      {
        path: 'quota-apply-detail', // 配额申请
        name: 'quota-apply-detail',
        component: () => import('@/views/purchase/quotaCalc/quotaApplyDetail/index.vue')
      },
      {
        path: 'quota-bias-list', // 配额执行偏差清单
        name: 'quota-bias-list',
        component: () => import('@/views/purchase/quotaCalc/quotaBias/quotaBiasList/index.vue')
      },
      {
        path: 'quota-bias-feedback', // 配额执行偏差反馈
        name: 'quota-bias-feedback',
        component: () => import('@/views/purchase/quotaCalc/quotaBias/index.vue')
      },
      {
        path: 'quota-bias-examine', // 配额执行偏差审核
        name: 'quota-bias-examine',
        component: () => import('@/views/purchase/quotaCalc/quotaBias/index.vue')
      },
      {
        path: 'quota-apply-record', // 配额调整申请备案
        name: 'quota-apply-record',
        component: () => import('@/views/purchase/quotaCalc/quotaApplyRecord/index.vue'),
        meta: {
          title: 'quota-apply-record',
          keepAlive: true
        }
      },
      {
        path: 'quota-apply-record-detail', // 配额调整申请备案详情
        name: 'quota-apply-record-detail',
        component: () => import('@/views/purchase/quotaCalc/quotaApplyRecord/detail/index.vue')
      },
      {
        path: 'quota-adjustment-notice', // 配额调整通知
        name: 'quota-adjustment-notice',
        component: () => import('@/views/purchase/quotaCalc/quotaModulationUpdateList/index.vue')
      }
    ]
  }
]

export default Router
