/** 采方：rfx配置相关 **/

const Router = [
  {
    path: 'approval-config', // 审批流配置-列表
    name: 'approval-config',
    // path: "purchase-settings-rfx-approval", // 审批流配置-列表
    // name: "purchase-settings-rfx-approval",
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/settings/rfx/approval" */ 'ROUTER_PURCHASE_SETTINGS/rfx/approval/index.vue'
      ),
    meta: {
      title: 'approval-config'
    }
  },
  // 任务计划模板-列表页
  {
    path: 'schedule-template',
    name: 'schedule-template',
    // path: "purchase-settings-sor-planDetail",
    // name: "purchase-settings-sor-planDetail",
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/settings/rfx/scheduleTemplate" */ 'ROUTER_PURCHASE_SETTINGS/rfx/scheduleTemplate/list/index.vue'
      ),
    meta: {
      title: 'schedule-template'
    }
  },
  // 任务计划模板-详情页
  {
    path: 'schedule-template-detail',
    name: 'schedule-template-detail',
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/settings/rfx/scheduleTemplate/detail" */ 'ROUTER_PURCHASE_SETTINGS/rfx/scheduleTemplate/detail/index.vue'
      ),
    meta: {
      title: 'schedule-template-detail'
    }
  },
  {
    path: 'sourcing-global-strategy', // 寻源策略配置
    name: 'sourcing-global-strategy',
    // path: "purchase-settings-rfx-fixed-point", // 寻源策略配置
    // name: "purchase-settings-rfx-fixed-point",
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/settings/rfx/fixedPoint" */ 'ROUTER_PURCHASE_SETTINGS/rfx/fixedPoint/index.vue'
      ),
    meta: {
      title: 'sourcing-global-strategy'
    }
  },
  {
    path: 'strategy-maps', // 策略地图-列表
    name: 'strategy-maps',
    // path: "purchase-settings-rfx-strategy-list", //  策略地图-列表
    // name: "purchase-settings-rfx-strategy-list",
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/settings/rfx/strategy/list" */ 'ROUTER_PURCHASE_SETTINGS/rfx/strategy/list/index.vue'
      ),
    meta: {
      title: 'strategy-maps',
      keepAlive: true
    }
  },
  {
    path: 'strategy-map-detail-flow', // 策略地图-详情
    name: 'strategy-map-detail-flow',
    // path: "purchase-settings-rfx-strategy-detail", // 策略地图-详情
    // name: "purchase-settings-rfx-strategy-detail",
    component: () => import('ROUTER_PURCHASE_SETTINGS/rfx/strategy/detail/index.vue'),
    meta: {
      title: 'strategy-map-detail-flow',
      keepAlive: true
    }
  }
]

export default Router
