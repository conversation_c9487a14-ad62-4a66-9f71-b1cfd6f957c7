/** 采方：成本模型 **/

const Router = [
  {
    path: 'purchase-settings-cost', //成本模型列表
    name: 'purchase-settings-cost',
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/settings/cost/list/index" */ 'ROUTER_PURCHASE_SETTINGS/cost/list/index.vue'
      ),
    meta: {
      title: 'purchase-settings-cost',
      keepAlive: true
    }
  },
  {
    path: 'purchase-settings-cost-detail', //成本模型-详情页
    name: 'purchase-settings-cost-detail',
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/settings/cost/detail/index" */ 'ROUTER_PURCHASE_SETTINGS/cost/detail/index.vue'
      ),
    meta: {
      title: 'purchase-settings-cost-detail',
      keepAlive: true
    }
  }
]

export default Router
