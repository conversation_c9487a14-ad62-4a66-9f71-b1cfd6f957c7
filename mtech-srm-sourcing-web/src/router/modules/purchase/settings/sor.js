/** 采方：SOR配置相关 **/

const Router = [
  {
    path: 'require-config-group', // 需求分组规则配置
    name: 'require-config-group',
    // path: "purchase-settings-sor-grouping", // 需求分组规则配置
    // name: "purchase-settings-sor-grouping",
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/settings/sor/grouping" */ 'ROUTER_PURCHASE_SETTINGS/sor/grouping/index.vue'
      ),
    meta: {
      title: 'require-config-group'
    }
  },
  {
    path: 'require-config-strategy', // 需求分配策略配置
    name: 'require-config-strategy',
    // path: "purchase-settings-sor-distribution", // 需求分配策略配置
    // name: "purchase-settings-sor-distribution",
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/settings/sor/distribution" */ 'ROUTER_PURCHASE_SETTINGS/sor/distribution/index.vue'
      ),
    meta: {
      title: 'require-config-strategy'
    }
  }
]

export default Router
