/** 采方：行情因子 **/

const Router = [
  {
    path: 'purchase-settings-factor',
    name: 'purchase-settings-factor', //行情因子列表
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/settings/market/factor" */ 'ROUTER_PURCHASE_SETTINGS/market/factor/list/index.vue'
      ),
    meta: {
      title: 'purchase-settings-factor',
      keepAlive: true
    }
  },
  {
    path: 'purchase-settings-factor-detail',
    name: 'purchase-settings-factor-detail', //行情因子列表
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/settings/market/factor" */ 'ROUTER_PURCHASE_SETTINGS/market/factor/detail/index.vue'
      ),
    meta: {
      title: 'purchase-settings-factor-detail',
      keepAlive: true
    }
  }
]

export default Router
