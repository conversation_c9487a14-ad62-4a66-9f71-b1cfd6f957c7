/** 采方：询比价-RFX **/

const Router = [
  {
    path: 'bid-hall',
    name: 'bid-hall',
    component: () => import(/* webpackChunkName: "router/public" */ 'ROUTER_PUBLIC/index.vue'),
    meta: {
      title: 'bid-hall',
      keepAlive: false
    },
    children: [
      {
        path: '', // 询价大厅列表
        meta: {
          keepAlive: true
        },
        component: () =>
          import(
            /* webpackChunkName: "router/purchase/rfx/list" */ 'ROUTER_PURCHASE_RFX/list/index.vue'
          )
      },
      {
        path: 'create', // 创建单据
        name: 'create',
        meta: {
          keepAlive: false
        },
        component: () =>
          import(
            /* webpackChunkName: "router/purchase/rfx/list" */ 'ROUTER_PURCHASE_RFX/list/components/createRFX.vue'
          )
      },
      {
        path: 'hall-detail',
        meta: {
          keepAlive: false
        },
        hidden: true,
        component: () => import(/* webpackChunkName: "router/public" */ 'ROUTER_PUBLIC/index.vue'),
        children: [
          {
            name: 'hall-detail',
            path: '', // 询价大厅-详情
            meta: {
              keepAlive: false
            },
            component: () =>
              import(
                /* webpackChunkName: "router/purchase/rfx/detail" */ 'ROUTER_PURCHASE_RFX/detail/index.vue'
              )
          }
        ]
      },
      {
        path: 'purchase-cost', // 成本模型-采方询价
        name: 'purchase-cost',
        meta: {
          keepAlive: false
        },
        component: () =>
          import(
            /* webpackChunkName: "router/purchase/rfx/list" */ 'ROUTER_PURCHASE_RFX/detail/purchaseCost/index.vue'
          )
      },
      {
        path: 'calculation-purchase-cost', // 成本模型-成本测算
        name: 'calculation-purchase-cost',
        meta: {
          keepAlive: false
        },
        component: () =>
          import(
            /* webpackChunkName: "router/purchase/rfx/list" */ 'ROUTER_PURCHASE_RFX/detail/calculation/index.vue'
          )
      },
      {
        // 临时路由，评分规则
        path: 'rating-rules',
        name: 'rating-rules',
        component: () => import('ROUTER_PURCHASE_RFX/detail/tabs/ratingRules/index.vue')
      },
      {
        // 临时路由，技术标管理
        path: 'technical-bid',
        name: 'technical-bid',
        component: () => import('ROUTER_PURCHASE_RFX/detail/tabs/technicalBid/list/index.vue')
      },
      {
        // 临时路由，商务标管理
        path: 'commercial-bid',
        name: 'commercial-bid',
        component: () => import('ROUTER_PURCHASE_RFX/detail/tabs/commercialBid/list/index.vue')
      },
      {
        // 专家评分列表
        path: 'expert-rating',
        name: 'expert-rating',
        component: () => import('ROUTER_PURCHASE/experts/expertRating/list/index.vue')
      },
      {
        // 评分确认列表
        path: 'expert-rating-confirm',
        name: 'expert-rating-confirm',
        component: () => import('ROUTER_PURCHASE/experts/expertRating/list/index.vue')
      },
      {
        // 临时路由，商务标
        path: 'expert-rate',
        name: 'expert-rate',
        component: () => import('ROUTER_PURCHASE/experts/expertRating/detail/rate.vue')
      },
      {
        // 临时路由，技术标
        path: 'expert-ratexpert',
        name: 'expert-ratexpert',
        component: () =>
          import('ROUTER_PURCHASE/experts/expertRating/components/list/aggregationDialog.vue')
      },
      {
        // 临时路由，专家评分汇总
        path: 'expert-rate-aggregation',
        name: 'expert-rate-aggregation',
        component: () => import('ROUTER_PURCHASE/experts/expertRating/expertPricing/list/index.vue')
      },
      // 中标公示
      {
        path: 'rfx-notice-list',
        name: 'rfx-notice-list',
        component: () =>
          import(
            /* webpackChunkName: "router/purchase/rfx/rfx-notice-list" */ 'ROUTER_PURCHASE_RFX/rfxNotice/list/index.vue'
          )
      }
    ]
  },
  //特殊下载
  {
    path: 'download',
    name: 'download',
    component: () => import('ROUTER_COMMON/download.vue')
  },
  // 成本分析
  {
    path: 'cost-analysis',
    name: 'cost-analysis',
    component: () => import('ROUTER_COMMON/components/costAnalysis/index.vue')
  }
]

export default Router
