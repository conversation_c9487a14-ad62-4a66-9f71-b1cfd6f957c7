/** 采方：评分模板**/

const Router = [
  {
    path: 'scoringTemplate-list', // 评分模板列表
    name: 'scoringTemplate-list',
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/scoringTemplate/list" */ 'ROUTER_PURCHASE/scoringTemplate/list/index.vue'
      ),
    meta: {
      title: 'scoringTemplate-list'
    }
  },
  {
    path: 'scoringTemplate-gradingRules', // 寻源项目配置管理-评分细项
    name: 'scoringTemplate-gradingRules',
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/scoringTemplate/gradingRules" */ 'ROUTER_PURCHASE/scoringTemplate/gradingRules/index.vue'
      ),
    meta: {
      title: 'scoringTemplate-gradingRules'
    }
  },
  {
    path: 'scoringTemplate-details', // 寻源项目配置管理-评分细则
    name: 'scoringTemplate-details',
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/scoringTemplate/details" */ 'ROUTER_PURCHASE/scoringTemplate/details/index.vue'
      ),
    meta: {
      title: 'scoringTemplate-details'
    }
  }
]

export default Router
