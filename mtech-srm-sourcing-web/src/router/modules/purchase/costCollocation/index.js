/** 采方：成本配置**/

const Router = [
  {
    path: 'cost-collocation', // 成本 配置  列表
    name: 'cost-collocation',
    // path: "purchase-cost-collocation", // 成本 配置  列表
    // name: "purchase-cost-collocation",
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/costCollocation" */ 'ROUTER_PURCHASE/costCollocation/index.vue'
      ),
    meta: {
      title: 'cost-collocation'
    }
  },
  {
    path: 'cost-collocation-detail', // 成本 配置  详情
    name: 'cost-collocation-detail',
    // path: "purchase-cost-collocation-detail", // 成本 配置  详情
    // name: "purchase-cost-collocation-detail",
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/costCollocation/detail" */ 'ROUTER_PURCHASE/costCollocation/detail.vue'
      ),
    meta: {
      title: 'cost-collocation-detail'
    }
  }
]

export default Router
