/** 采方：组件功能说明 **/

const Router = [
  {
    path: 'template-page',
    name: 'template-page',
    component: () => import(/* webpackChunkName: "router/public" */ 'ROUTER_PUBLIC/index.vue'),
    meta: {
      title: 'common-template-page',
      keepAlive: false
    },
    children: [
      {
        path: 'custom-search',
        name: 'custom-search',
        component: () =>
          import('@/views/purchase/componentsDoc/template-page/customQuickSearch/index.vue')
      },
      {
        path: 'custom-editor',
        name: 'custom-editor',
        component: () =>
          import('@/views/purchase/componentsDoc/template-page/customEditor/index.vue')
      },
      {
        path: 'cell-edit',
        name: 'cell-edit',
        component: () =>
          import('@/views/purchase/componentsDoc/template-page/crwdEditorQrender/index.vue')
      },
      {
        path: 'virtual-scroll',
        name: 'virtual-scroll',
        component: () =>
          import('@/views/purchase/componentsDoc/template-page/virtualScroll/index.vue')
      },
      {
        path: 'virtual-scroll-page',
        name: 'virtual-scroll-page',
        component: () =>
          import('@/views/purchase/componentsDoc/template-page/virtualScrollPagination/index.vue')
      }
    ]
  },
  {
    path: 'vxe-table',
    name: 'vxe-table',
    component: () => import(/* webpackChunkName: "router/public" */ 'ROUTER_PUBLIC/index.vue'),
    meta: {
      title: 'vxe-table',
      keepAlive: false
    },
    children: [
      {
        path: 'merge-cell',
        name: 'merge-cell',
        component: () => import('@/views/purchase/componentsDoc/vxe-table/mergeCells/index.vue')
      }
    ]
  }
]

export default Router
