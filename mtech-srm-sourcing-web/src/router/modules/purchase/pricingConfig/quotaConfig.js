/** 核价配置**/

const Router = [
  {
    path: 'purchase-quotaConfig', //定额配置
    name: 'purchase-quotaConfig',
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/quotaConfig/index" */ '@/views/purchase/quotaConfig/index.vue'
      ),
    meta: {
      title: 'purchase-quotaConfig'
    }
  },
  {
    path: 'purchase-quotaConfig-detail', //定额配置-详情
    name: 'purchase-quotaConfig-detail',
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/quotaConfig/components/index" */ '@/views/purchase/quotaConfig/components/index.vue'
      ),
    meta: {
      title: 'purchase-quotaConfig-detail'
    }
  },
  {
    path: 'quotaConfig-detail', //定额配置
    name: 'quotaConfig-detail',
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/quotaConfig/detail" */ '@/views/purchase/quotaConfig/detail.vue'
      ),
    meta: {
      title: 'quotaConfig-detail'
    }
  }
]

export default Router
