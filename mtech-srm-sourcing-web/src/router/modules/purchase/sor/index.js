/** 采方：寻源需求**/

const Router = [
  {
    path: 'require-summary', // 需求汇总管理
    name: 'require-summary',
    // path: "purchase-sor-header", // 需求汇总管理
    // name: "purchase-sor-header",
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/sor/header" */ 'ROUTER_PURCHASE_SOR/header/index.vue'
      ),
    meta: {
      title: 'require-summary'
    }
  },
  {
    path: 'require-mine', // 我的需求管理
    name: 'require-mine',
    // path: "purchase-sor-mine", // 我的需求管理
    // name: "purchase-sor-mine",
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/sor/mine" */ 'ROUTER_PURCHASE_SOR/mine/index.vue'
      ),
    meta: {
      title: 'require-mine'
    }
  },
  {
    path: 'require-detail', // 需求单据详情
    name: 'require-detail',
    // path: "purchase-sor-detail", // 需求单据详情
    // name: "purchase-sor-detail",
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/sor/detail" */ 'ROUTER_PURCHASE_SOR/detail/index.vue'
      ),
    meta: {
      title: 'require-detail'
    }
  },
  {
    path: 'source-require-page', //寻源需求配置页面
    name: 'source-require-page',
    // path: "purchase-sor-grouping", //寻源需求配置页面
    // name: "purchase-sor-grouping",
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/sor/grouping" */ 'ROUTER_PURCHASE_SOR/grouping/index.vue'
      ),
    meta: {
      title: 'sourcing source-require-page'
    }
  }
]

export default Router
