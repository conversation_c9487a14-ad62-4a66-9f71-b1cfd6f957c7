/** 采方：定点推荐**/

const Router = [
  {
    path: 'fixed-point-recommendations', // 菜单：
    name: 'fixed-point-recommendations',
    // path: "purchase-price-list", // 菜单：
    // name: "purchase-price-list",
    component: () => import('@/views/purchase/recommendFixedPointOld/index.vue'),
    meta: {
      title: 'fixed-point-recommendations',
      keepAlive: true
    }
  },
  {
    path: 'direct-pricing-kt', // 菜单：
    name: 'direct-pricing-kt',
    // path: "purchase-price-list", // 菜单：
    // name: "purchase-price-list",
    component: () => import('@/views/purchase/recommendFixedPointOld/index.vue'),
    meta: {
      title: 'direct-pricing-kt',
      keepAlive: true
    }
  },
  {
    // 临时路由，直接定价明细
    path: 'fixed-point',
    name: 'fixed-point',
    component: () => import('ROUTER_PURCHASE/recommendFixedPoint/components/index.vue')
  },
  {
    // 临时路由，直接定价明细
    path: 'direct-pricing-detail-tv',
    name: 'direct-pricing-detail-tv',
    component: () => import('ROUTER_PURCHASE/recommendFixedPointOld/components/index.vue')
  },
  {
    path: 'base-price', // 菜单：
    name: 'base-price',
    component: () => import('@/views/purchase/recommendFixedPoint/basePrice.vue'),
    meta: {
      title: 'base-price',
      keepAlive: true
    }
  },
  {
    path: 'base-price-detail', // 菜单：
    name: 'base-price-detail',
    component: () => import('@/views/purchase/recommendFixedPoint/basePriceDetail.vue'),
    meta: {
      title: 'base-price-detail',
      keepAlive: true
    }
  },
  {
    path: 'average-price', // 菜单：
    name: 'average-price',
    component: () => import('@/views/purchase/recommendFixedPoint/averagePrice.vue'),
    meta: {
      title: 'average-price',
      keepAlive: true
    }
  },
  {
    path: 'average-price-detail', // 菜单：
    name: 'average-price-detail',
    component: () => import('@/views/purchase/recommendFixedPoint/averagePriceDetail.vue'),
    meta: {
      title: 'average-price-detail',
      keepAlive: true
    }
  },
  {
    path: 'strike-price', // 菜单：
    name: 'strike-price',
    component: () => import('@/views/purchase/recommendFixedPoint/strikePrice.vue'),
    meta: {
      title: 'strike-price',
      keepAlive: true
    }
  },
  {
    path: 'contract-price', // 菜单：价格合同
    name: 'contract-price',
    component: () => import('@/views/purchase/recommendFixedPoint/contractPrice.vue'),
    meta: {
      title: 'contract-price',
      keepAlive: true
    }
  },
  {
    // 临时路由，直接定价明细
    path: 'contract-price-detail',
    name: 'contract-price-detail',
    component: () => import('ROUTER_PURCHASE/recommendFixedPoint/components/contractDetail.vue')
  },
  {
    path: 'contract-prices-detail', // 菜单：价格合同查询
    name: 'contract-prices-detail',
    component: () => import('@/views/purchase/recommendFixedPoint/contractPriceDetail.vue'),
    meta: {
      title: 'base-price-detail',
      keepAlive: true
    }
  },
  {
    path: 'contract-price-cost-model', // 成本模型-执行价
    name: 'contract-price-cost-model',
    meta: {
      keepAlive: false
    },
    component: () => import('ROUTER_PURCHASE/recommendFixedPoint/components/costModel.vue')
  },
  {
    path: 'storage-not-priced', // 入库未定价
    name: 'storage-not-priced',
    component: () => import('@/views/purchase/recommendFixedPoint/storageNotPriced.vue')
  },
  {
    path: 'cost-calculation', // 成本测算
    name: 'cost-calculation',
    component: () => import('@/views/purchase/recommendFixedPoint/costCalculation')
  },
  {
    path: 'cost-calculation-detail', // 成本测算详情
    name: 'cost-calculation-detail',
    component: () => import('@/views/purchase/recommendFixedPoint/costCalculation/detail')
  },
  {
    path: 'calculation', // 测算页面
    name: 'calculation',
    component: () => import('@/views/purchase/recommendFixedPoint/calculation')
  }
]

export default Router
