/**
 * 资质审查
 */

const Router = [
  {
    path: 'examine',
    name: 'examine',
    component: () => import(/* webpackChunkName: "router/public" */ 'ROUTER_PUBLIC/index.vue'),
    meta: {
      title: 'examine',
      keepAlive: false
    },
    children: [
      {
        path: 'list',
        name: 'examine-list',
        component: () => import('@/views/purchase/examine/list')
      },
      // 资质审查汇总页
      {
        path: 'rfx-list',
        name: 'examine-rfx-list',
        component: () => import('@/views/purchase/examine/rfxList')
      },
      {
        path: 'qualification',
        name: 'examine-qualification',
        component: () => import('@/views/purchase/examine/qualification/index.vue')
      }
    ]
  }
]

export default Router
