// 配额管理
const Router = [
  {
    path: 'purchase-quota-apply', // 菜单：配额申请
    name: 'purchase-quota-apply',
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/quota" */ 'ROUTER_PURCHASE/quota/apply/list/index.vue'
      ),
    meta: {
      title: 'purchase-quota-apply'
    }
  },
  {
    path: 'purchase-quota-apply-detail', // 菜单：配额申请
    name: 'purchase-quota-apply-detail',
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/quota" */ 'ROUTER_PURCHASE/quota/apply/detail/index.vue'
      ),
    meta: {
      title: 'purchase-quota-apply-detail'
    }
  },
  {
    path: 'purchase-quota-detail', // 菜单：配额申请
    name: 'purchase-quota-detail',

    component: () =>
      import(
        /* webpackChunkName: "router/purchase/quota" */ 'ROUTER_PURCHASE/quota/detail/index.vue'
      ),
    meta: {
      title: 'purchase-quota-detail'
    }
  }
]

export default Router
