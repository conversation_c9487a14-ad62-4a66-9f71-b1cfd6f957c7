/** 采/供：公开寻源**/
const Router = [
  {
    path: 'public-sourcing',
    name: 'public-sourcing',
    component: () => import('ROUTER_PUBLIC/index.vue'),
    meta: {
      title: 'public-sourcing'
    },
    children: [
      // 采方 - 公开寻源发起
      {
        path: '/sourcing/public-sourcing-initiated',
        name: 'public-sourcing-initiated',
        component: () => import('ROUTER_PURCHASE/publicSourcing/initiated/index.vue')
      },
      // 采方 - 公开寻源发起详情
      {
        path: '/sourcing/public-sourcing-initiated/detail',
        name: 'public-sourcing-initiated-detail',
        component: () => import('ROUTER_PURCHASE/publicSourcing/initiated/detail/index.vue')
      },
      // 采方 - 评分页面
      {
        path: '/sourcing/public-sourcing-score',
        name: 'public-sourcing-score',
        component: () => import('ROUTER_PURCHASE/publicSourcing/score/index.vue')
      },
      // 供方 - 公开寻源
      {
        path: '/sourcing/public-sourcing',
        name: 'public-sourcing',
        component: () => import('ROUTER_SUPPLY/publicSourcing/index.vue')
      },
      // 供方 - 公开寻源详情
      {
        path: '/sourcing/public-sourcing-detail',
        name: 'public-sourcing-detail',
        component: () => import('ROUTER_SUPPLY/publicSourcing/detail')
      },
      // 供方 - 公开寻源结果
      {
        path: '/sourcing/public-sourcing-result',
        name: 'public-sourcing-result',
        component: () => import('ROUTER_SUPPLY/publicSourcing/result')
      }
    ]
  }
]

export default Router
