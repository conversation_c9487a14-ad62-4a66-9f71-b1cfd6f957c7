/** 采方：专家管理**/

const Router = [
  {
    path: 'purchase-expert',
    name: 'purchase-expert',
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/experts/expert/list" */ 'ROUTER_PURCHASE/experts/expert/list/index.vue'
      ),
    meta: {
      title: 'Platform-expert-management',
      keepAlive: true
    }
  },
  {
    path: 'purchase-expert-detail',
    name: 'purchase-expert-detail',
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/experts/expert/detail" */ 'ROUTER_PURCHASE/experts/expert/detail/index.vue'
      ),
    meta: {
      title: 'Platform-expert-detail',
      keepAlive: true
    }
  },

  // 专家信息管理列表
  {
    path: 'purchase-expertMessage',
    name: 'purchase-expertMessage',
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/experts/expertMessage/list" */ 'ROUTER_PURCHASE/experts/expertMessage/list/index.vue'
      ),
    meta: {
      title: 'purchase-expertMessage',
      keepAlive: true
    }
  },

  // 专家管理信息详情
  {
    path: 'purchase-expertMessage-detail',
    name: 'purchase-expertMessage-detail',
    component: () =>
      import(
        /* webpackChunkName: "router/purchase/expert/expertMessage/detail" */ 'ROUTER_PURCHASE/experts/expertMessage/detail/index.vue'
      ),
    meta: {
      title: 'purchase-expertMessage-detail'
    }
  }
]

export default Router
