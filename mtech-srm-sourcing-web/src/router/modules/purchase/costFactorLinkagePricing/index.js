/** 采方：成本因子联动定价**/

const Router = [
  {
    path: 'cost-factor-linkage-pricing',
    name: 'cost-factor-linkage-pricing',
    component: () => import('ROUTER_PUBLIC/index.vue'),
    meta: {
      title: 'cost-factor-linkage-pricing'
    },
    children: [
      // 列表
      {
        path: '',
        name: 'cost-factor-linkage-pricing-list',
        component: () => import('ROUTER_PURCHASE/costFactorLinkagePricing/index.vue')
      },
      // 明细
      {
        path: 'detail',
        name: 'cost-factor-linkage-pricing-detail',
        component: () => import('ROUTER_PURCHASE/costFactorLinkagePricing/detail.vue')
      },
      // 成本分析
      {
        path: 'cost-analysis',
        name: 'cost-factor-linkage-pricing-cost-analysis',
        component: () => import('ROUTER_PURCHASE/costFactorLinkagePricing/costAnalysis/index.vue')
      }
    ]
  }
]

export default Router
