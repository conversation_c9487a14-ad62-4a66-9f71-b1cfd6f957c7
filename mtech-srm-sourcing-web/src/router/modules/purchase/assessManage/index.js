/** 采方：考核管理**/

const Router = [
  {
    path: 'purchase-assessmanage-type', // 菜单：考核类型设置
    name: 'purchase-assessmanage-type',
    component: () => import('ROUTER_PURCHASE_ASSESSMANAGE/assessType/index.vue'),
    meta: {
      title: 'purchase-assessmanage-type'
    }
  },
  {
    path: 'purchase-assessmanage-index', // 菜单：考核指标设置
    name: 'purchase-assessmanage-index',
    component: () => import('ROUTER_PURCHASE_ASSESSMANAGE/assessIndex/index.vue'),
    meta: {
      title: 'purchase-assessmanage-index'
    }
  },
  {
    path: 'purchase-assessmanage-dataPrepare', // 菜单：数据准备
    name: 'purchase-assessmanage-dataPrepare',
    component: () => import('ROUTER_PURCHASE_ASSESSMANAGE/dataPrepare/index.vue'),
    meta: {
      title: 'purchase-assessmanage-dataPrepare'
    }
  },
  {
    path: 'purchase-assessmanage-agreementTemplate', // 菜单：协议书模板
    name: 'purchase-assessmanage-agreementTemplate',
    component: () => import('ROUTER_PURCHASE_ASSESSMANAGE/agreementTemplate/index.vue'),
    meta: {
      title: 'purchase-assessmanage-agreementTemplate'
    }
  },
  {
    path: 'purchase-assessmanage-assessList', // 菜单：考核单管理
    name: 'purchase-assessmanage-assessList',
    component: () => import('ROUTER_PURCHASE_ASSESSMANAGE/assessList/index.vue'),
    meta: {
      title: 'purchase-assessmanage-assessList'
    }
  },
  {
    path: 'purchase-assessmanage-assessListDetail', // 菜单：考核单详情
    name: 'purchase-assessmanage-assessListDetail',
    component: () => import('ROUTER_PURCHASE_ASSESSMANAGE/assessList/detail.vue'),
    meta: {
      title: 'purchase-assessmanage-assessListDetail'
    }
  },
  {
    path: 'purchase-assessmanage-appealDeal', // 菜单：申诉处理
    name: 'purchase-assessmanage-appealDeal',
    component: () => import('ROUTER_PURCHASE_ASSESSMANAGE/appealDeal/index.vue'),
    meta: {
      title: 'purchase-assessmanage-appealDeal'
    }
  },
  {
    path: 'purchase-assessmanage-appealDealDetail', // 菜单：考核单详情
    name: 'purchase-assessmanage-appealDealDetail',
    component: () => import('ROUTER_PURCHASE_ASSESSMANAGE/appealDeal/detail.vue'),
    meta: {
      title: 'purchase-assessmanage-appealDealDetail'
    }
  }
]

export default Router
