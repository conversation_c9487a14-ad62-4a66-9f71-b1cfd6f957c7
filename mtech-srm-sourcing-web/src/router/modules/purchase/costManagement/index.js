/** 采方：成本模型管理**/

const Router = [
  // 成本因子内部定价
  {
    path: 'cost-factor-internal-pricing',
    name: 'cost-factor-internal-pricing',
    component: () => import('ROUTER_PUBLIC/index.vue'),
    meta: {
      title: 'cost-factor-internal-pricing'
    },
    children: [
      // 成本因子内部定价-列表
      {
        path: '',
        name: 'cost-factor-internal-pricing-list',
        component: () =>
          import('ROUTER_PURCHASE/costManagement/costFactorInternalPricing/index.vue')
      },
      // 成本因子内部定价-明细
      {
        path: 'detail',
        name: 'cost-factor-internal-pricing-detail',
        component: () =>
          import('ROUTER_PURCHASE/costManagement/costFactorInternalPricing/detail.vue')
      }
    ]
  },
  // 成本因子价格记录
  {
    path: 'cost-factor-price-record',
    name: 'cost-factor-price-record',
    component: () => import('ROUTER_PURCHASE/costManagement/costFactorPriceRecord/index.vue'),
    meta: {
      title: 'cost-factor-price-record'
    }
  },
  // 联动物料维护
  {
    path: 'linkage-material-maintenance',
    name: 'linkage-material-maintenance',
    component: () => import('ROUTER_PURCHASE/costManagement/linkageMaterialMaintenance/index.vue'),
    meta: {
      title: 'linkage-material-maintenance'
    }
  },
  // 成本分析报表
  {
    path: 'cost-analysis-report',
    name: 'cost-analysis-report',
    component: () => import('ROUTER_PURCHASE/costManagement/costAnalysisReport/index.vue'),
    meta: {
      title: 'cost-analysis-report'
    }
  }
]

export default Router
