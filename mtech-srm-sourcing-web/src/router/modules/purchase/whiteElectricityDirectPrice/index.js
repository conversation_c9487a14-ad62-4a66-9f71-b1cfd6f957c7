/** 采方：白电定点推荐**/

const Router = [
  {
    path: 'white-electricity-direct-price', // 菜单：
    name: 'white-electricity-direct-price',
    component: () => import('@/views/purchase/whiteElectricityDirectPrice/index.vue'),
    meta: {
      title: 'white-electricity-direct-price',
      keepAlive: true
    }
  },
  {
    // 临时路由，直接定价明细
    path: 'direct-price',
    name: 'direct-price',
    component: () => import('ROUTER_PURCHASE/whiteElectricityDirectPrice/components/index.vue')
  },
  {
    // 临时路由，直接定价明细
    path: 'direct-price-detail',
    name: 'direct-price-detail',
    component: () =>
      import('ROUTER_PURCHASE/whiteElectricityDirectPrice/components/indexDetail.vue')
  }
]

export default Router
