/** 采方：招投标-Bidding **/

const Router = [
  {
    path: 'purchase-bidding',
    name: 'purchase-bidding',
    component: () => import(/* webpackChunkName: "router/public" */ 'ROUTER_PUBLIC/index.vue'),
    meta: {
      title: 'bidding-hall',
      keepAlive: true
    },
    children: [
      {
        path: '', // 招投标大厅列表
        component: () =>
          import(
            /* webpackChunkName: "router/purchase/bidding/list" */ 'ROUTER_PURCHASE_BIDDING/list/index.vue'
          )
      }
    ]
  }
]

export default Router
