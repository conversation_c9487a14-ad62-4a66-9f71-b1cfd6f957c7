/** 采方：招投标-Bidding **/

const Router = [
  {
    path: 'purchase-price-bidding',
    name: 'purchase-price-bidding',
    component: () => import(/* webpackChunkName: "router/public" */ 'ROUTER_PUBLIC/index.vue'),
    meta: {
      title: 'price-bidding-hall',
      keepAlive: true
    },
    children: [
      {
        path: '', // 竞价大厅列表
        component: () =>
          import(
            /* webpackChunkName: "router/purchase/bidding/list" */ 'ROUTER_PURCHASE_PRICE_BIDDING/list/index.vue'
          )
      }
    ]
  }
]

export default Router
