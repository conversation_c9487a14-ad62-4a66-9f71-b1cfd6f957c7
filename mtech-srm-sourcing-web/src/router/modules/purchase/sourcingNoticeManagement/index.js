/** 采/供：公开寻源**/
const Router = [
  {
    path: 'sourcing-notice-management',
    name: 'sourcing-notice-management',
    component: () => import('ROUTER_PUBLIC/index.vue'),
    meta: {
      title: 'sourcing-notice-management'
    },
    children: [
      // 采方 - 寻源公告列表
      {
        path: '/sourcing/sourcing-notice',
        name: 'sourcing-notice',
        component: () => import('ROUTER_PURCHASE/sourcingNoticeManagement/sourcingNotice/index.vue')
      },
      // 采方 - 寻源公告详情
      {
        path: '/sourcing/sourcing-notice-edit',
        name: 'sourcing-notice-edit',
        component: () => import('ROUTER_PURCHASE/sourcingNoticeManagement/sourcingNotice/edit.vue')
      },
      // 采方 - 寻源公告详情
      {
        path: '/sourcing/bid-notice',
        name: 'bid-notice',
        component: () => import('ROUTER_PURCHASE/sourcingNoticeManagement/bidNotice/index.vue')
      }
    ]
  }
]

export default Router
