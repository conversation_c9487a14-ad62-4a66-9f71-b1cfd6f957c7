/** 公共配置： 业务类型字段配置 **/

const Router = [
  {
    path: 'business-config', // 业务字段配置
    name: 'business-config',
    // path: "common-settings-business-list", // 业务字段配置
    // name: "common-settings-business-list",
    component: () =>
      import(
        /* webpackChunkName: "router/common/settings/business/list" */ 'ROUTER_COMMON_SETTINGS/business/list/index.vue'
      ),
    meta: {
      title: 'business-config',
      keepAlive: true
    }
  },
  {
    path: 'business-item-config', // 业务字段明细配置
    name: 'business-item-config',
    component: () =>
      import(
        /* webpackChunkName: "router/common/settings/business/list" */ 'ROUTER_COMMON_SETTINGS/businessItem/index.vue'
      ),
    meta: {
      title: 'business-item-config',
      keepAlive: true
    }
  },
  {
    path: 'business-config-detail', // 业务字段配置-详情
    name: 'business-config-detail',
    // path: "common-settings-business-detail", // 业务字段配置-详情
    // name: "common-settings-business-detail",
    component: () =>
      import(
        /* webpackChunkName: "router/common/settings/business/detail" */ 'ROUTER_COMMON_SETTINGS/business/detail/index.vue'
      ),
    meta: {
      title: 'business-config-detail',
      keepAlive: true
    }
  },
  {
    path: 'structure-config', // 业务字段配置
    name: 'structure-config',
    component: () =>
      import(
        /* webpackChunkName: "router/common/settings/structure/list" */ 'ROUTER_COMMON_SETTINGS/business/list/index.vue'
      ),
    meta: {
      title: 'structure-config',
      keepAlive: true
    }
  },
  {
    path: 'structure-config-detail', // 业务字段配置-详情
    name: 'structure-config-detail',
    component: () =>
      import(
        /* webpackChunkName: "router/common/settings/structure/detail" */ 'ROUTER_COMMON_SETTINGS/business/detail/index.vue'
      ),
    meta: {
      title: 'structure-config-detail',
      keepAlive: true
    }
  },
  {
    path: 'smt-component-item', // smt需求模板小板
    name: 'smt-component-item',
    component: () =>
      import(
        /* webpackChunkName: "router/common/settings/structure/detail" */ 'ROUTER_COMMON_SETTINGS/smtComponentItem/index.vue'
      ),
    meta: {
      title: 'smt-component-item',
      keepAlive: true
    }
  },
  {
    path: 'fixed-exchange-rate', // 固定汇率维护
    name: 'fixed-exchange-rate',
    component: () =>
      import(
        /* webpackChunkName: "router/common/settings/structure/detail" */ 'ROUTER_COMMON_SETTINGS/fixedExchangeRate/index.vue'
      ),
    meta: {
      title: 'fixed-exchange-rate',
      keepAlive: true
    }
  }
]

export default Router
