/** 供方-寻源协同-报价 **/

const Router = [
  {
    path: 'price-confirm', // 报价投标-列表
    name: 'price-confirm',
    component: () =>
      import(
        /* webpackChunkName: "./src/router/modules/supply/quotationBidding/index.js" */ 'ROUTER_SUPPLY_PRICECONFIRM/list/index.vue'
      ),
    meta: {
      title: 'price-confirm'
    }
  },
  {
    path: 'price-confirm-detail', // 报价投标-列表
    name: 'price-confirm-detail',
    component: () =>
      import(
        /* webpackChunkName: "./src/router/modules/supply/quotationBidding/index.js" */ 'ROUTER_SUPPLY_PRICECONFIRM/detail/index.vue'
      ),
    meta: {
      title: 'price-confirm-detail'
    }
  }
]

export default Router
