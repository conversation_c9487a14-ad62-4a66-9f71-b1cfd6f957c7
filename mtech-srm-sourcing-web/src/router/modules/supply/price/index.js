/** 采方：价格服务**/

const Router = [
  {
    path: 'supply-price-service', // 菜单：价格服务列表
    name: 'supply-price-service',
    // path: "supply-price-list", // 菜单：价格服务列表
    // name: "supply-price-list",
    component: () =>
      import(
        /* webpackChunkName: "router/supply/price/list" */ 'ROUTER_SUPPLY_PRICE/list/index.vue'
      ),
    meta: {
      title: 'supply-price-service'
    }
  },
  {
    path: 'supply-price-record', // 菜单：价格服务列表
    name: 'supply-price-record',
    component: () => import('ROUTER_SUPPLY_PRICE/priceRecord/index.vue'),
    meta: {
      title: 'supply-price-record',
      keepAlive: true
    }
  },
  {
    path: 'sup-manpower-outsourcing-pricing', // 菜单：供方-人力外包定价
    name: 'sup-manpower-outsourcing-pricing',
    component: () => import('ROUTER_SUPPLY_PRICE/manpowerOutsourcingPricing/index.vue'),
    meta: {
      title: 'sup-manpower-outsourcing-pricing',
      keepAlive: true
    }
  },
  {
    path: 'sup-manpower-outsourcing-pricing-detail', // 菜单：供方-人力外包定价-明细
    name: 'sup-manpower-outsourcing-pricing-detail',
    component: () => import('ROUTER_SUPPLY_PRICE/manpowerOutsourcingPricing/detail.vue'),
    meta: {
      title: 'sup-manpower-outsourcing-pricing-detail',
      keepAlive: true
    }
  }
]

export default Router
