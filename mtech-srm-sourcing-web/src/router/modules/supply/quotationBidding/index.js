/** 供方-寻源协同-报价 **/

const Router = [
  {
    path: 'offer-bid', // 报价投标-列表
    name: 'offer-bid',
    component: () =>
      import(
        /* webpackChunkName: "./src/router/modules/supply/quotationBidding/index.js" */ 'ROUTER_SUPPLY_QUOTATION/list/index.vue'
      ),
    meta: {
      title: 'offer-bid'
    }
  },
  {
    path: 'offer-bid-information', // 信息记录-列表
    name: 'offer-bid-information',
    component: () =>
      import(
        /* webpackChunkName: "./src/router/modules/supply/quotationBidding/index.js" */ 'ROUTER_SUPPLY_QUOTATION/information/index.vue'
      ),
    meta: {
      title: 'offer-bid-information'
    }
  },
  {
    path: 'offer-bid-part-in', // 报价投标-详情
    name: 'offer-bid-part-in',
    component: () =>
      import(
        /* webpackChunkName: "./src/router/modules/supply/quotationBidding/detail/index.js" */ 'ROUTER_SUPPLY_QUOTATION/detail/index.vue'
      ),
    meta: {
      title: 'offer-bid-part-in'
    }
  },
  {
    path: 'offer-purchase-cost', // 供方-报价-成本模型
    name: 'offer-purchase-cost',
    component: () =>
      import(
        /* webpackChunkName: "./src/router/modules/supply/quotationBidding/detail/index.js" */ 'ROUTER_SUPPLY_QUOTATION/detail/purchaseCost/index.vue'
      ),
    meta: {
      title: 'offer-purchase-cost'
    }
  },
  //=========我的投标
  {
    path: 'IBid-list', // 我的投标-列表
    name: 'IBid-list',
    component: () =>
      import(
        /* webpackChunkName: "./src/router/modules/supply/quotationBidding/IBid/list/index.js" */ 'ROUTER_SUPPLY_QUOTATION/IBid/index.vue'
      ),
    meta: {
      title: 'IBid-list'
    }
  },
  //=========我的竞价
  {
    path: 'IBidding-lists', // 我的投标-列表
    name: 'IBidding-lists',
    component: () =>
      import(
        /* webpackChunkName: "./src/router/modules/supply/quotationBidding/IBid/list/index.js" */ 'ROUTER_SUPPLY_QUOTATION/IBidding/index.vue'
      ),
    meta: {
      title: 'IBidding-lists'
    }
  },
  // 联动定价
  {
    path: 'linkage-pricing-confirm',
    name: 'linkage-pricing-confirm',
    component: () => import('ROUTER_PUBLIC/index.vue'),
    meta: {
      title: 'cost-factor-linkage-pricing'
    },
    children: [
      // 列表
      {
        path: '',
        name: 'linkage-pricing-confirm',
        component: () => import('ROUTER_SUPPLY_QUOTATION/costFactorLinkagePricingConfirm/index.vue')
      },
      // 明细
      {
        path: 'detail',
        name: 'linkage-pricing-confirm-detail',
        component: () =>
          import('ROUTER_SUPPLY_QUOTATION/costFactorLinkagePricingConfirm/detail.vue')
      },
      // 成本分析
      {
        path: 'cost-analysis',
        name: 'linkage-pricing-confirm-cost-analysis',
        component: () =>
          import('ROUTER_SUPPLY_QUOTATION/costFactorLinkagePricingConfirm/costAnalysis/index.vue')
      }
    ]
  }
]

export default Router
