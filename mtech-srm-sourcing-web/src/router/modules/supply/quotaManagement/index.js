/** 配额管理：配额申请**/

const Router = [
  {
    path: 'supply-QM-quotaApplication-list', //配额申请：列表
    name: 'supply-QM-quotaApplication-list',
    component: () =>
      import(
        /* webpackChunkName: "router/supply/quotaManagement/quotaApplication/list" */ 'ROUTER_SUPPLY_QM/quotaApplication/list/index.vue'
      ),
    meta: {
      title: 'supply-QM-quotaApplication-list'
    }
  },
  {
    path: 'supply-QM-quotaApplication-detail', //配额申请：详情
    name: 'supply-QM-quotaApplication-detail',
    component: () =>
      import(
        /* webpackChunkName: "router/supply/quotaManagement/quotaApplication/detail" */ 'ROUTER_SUPPLY_QM/quotaApplication/detail/index.vue'
      ),
    meta: {
      title: 'supply-QM-quotaApplication-detail'
    }
  },
  {
    path: 'supply-QM-quotaDetail-list', //配额明细：列表
    name: 'supply-QM-quotaDetail-list',
    component: () =>
      import(
        /* webpackChunkName: "router/supply/quotaManagement/quotaDetail/list" */ 'ROUTER_SUPPLY_QM/quotaDetail/list/index.vue'
      ),
    meta: {
      title: 'supply-QM-quotaDetail-list'
    }
  },
  {
    path: 'supply-QM-quotaDetail-detail', //配额明细：详情
    name: 'supply-QM-quotaDetail-detail',
    component: () =>
      import(
        /* webpackChunkName: "router/supply/quotaManagement/quotaDetail/detail" */ 'ROUTER_SUPPLY_QM/quotaApplication/detail/index.vue'
      ),
    meta: {
      title: 'supply-QM-quotaDetail-detail'
    }
  }
]

export default Router
