import Vue from 'vue'

//引入样式
import './styles/index.scss'
// import "@mtech-ui/base/build/esm/bundle.css";
import '@mtech-form-design/deploy/build/esm/bundle.css'
import '@mtech/common-tree-view/build/esm/bundle.css'
import '@mtech/common-detail-page/build/esm/bundle.css'
import '@mtech/common-rule-config/build/esm/bundle.css'
import '@mtech/common-loading/build/esm/bundle.css'
import '@mtech/common-template-page/build/esm/bundle.css'

import loading from './components/loading'
Vue.use(loading)

//mtech-ui组件
import MtDialog from '@mtech-ui/dialog'
Vue.use(MtDialog)
import MtButton from '@mtech-ui/button'
Vue.use(MtButton)
import MtForm from '@mtech-ui/form'
Vue.use(MtForm)
import MtRow from '@mtech-ui/row'
Vue.use(MtRow)
import MtCol from '@mtech-ui/col'
Vue.use(MtCol)
import MtFormItem from '@mtech-ui/form-item'
Vue.use(MtFormItem)
import MtInput from '@mtech-ui/input'
Vue.use(MtInput)
import MtDataGrid from '@mtech-ui/data-grid'
Vue.use(MtDataGrid)
import MtSelect from '@mtech-ui/select'
Vue.use(MtSelect)
import MtTabs from '@mtech-ui/tabs'
Vue.use(MtTabs)
import MtSlider from '@mtech-ui/slider'
Vue.use(MtSlider)
import MtTooltip from '@mtech-ui/tooltip'
Vue.use(MtTooltip)
import MtToast from '@mtech-ui/toast'
Vue.use(MtToast)
import MtIcon from '@mtech-ui/icon'
Vue.use(MtIcon)
import MtCheckbox from '@mtech-ui/checkbox'
Vue.use(MtCheckbox)
import MtCheckboxGroup from '@mtech-ui/checkbox-group'
Vue.use(MtCheckboxGroup)
import MtDateTangePicker from '@mtech-ui/date-range-picker'
Vue.use(MtDateTangePicker)
import MtDateTimePicker from '@mtech-ui/date-time-picker'
Vue.use(MtDateTimePicker)
import MtTimePicker from '@mtech-ui/time-picker'
Vue.use(MtTimePicker)
import MtTreeView from '@mtech-ui/tree-view'
Vue.use(MtTreeView)
import MtDatePicker from '@mtech-ui/date-picker'
Vue.use(MtDatePicker)
import MtInputNumber from '@mtech-ui/input-number'
Vue.use(MtInputNumber)
import MtDropDownTree from '@mtech-ui/drop-down-tree'
Vue.use(MtDropDownTree)
import MtMultiSelect from '@mtech-ui/multi-select'
Vue.use(MtMultiSelect)

import MtCommonUploader from '@mtech/mtech-common-uploader'
Vue.use(MtCommonUploader)
import Parser from '@mtech-form-design/form-parser'
Vue.component('mt-parser', Parser)

import mtTreeGrid from '@mtech-ui/tree-grid'
Vue.component('mt-tree-grid', mtTreeGrid)

//$dialog
import * as Dialog from 'COMPONENTS/Dialog'
Vue.component('mt-col', MtCol)
Vue.component('mt-row', MtRow)
Vue.component('mt-dialog', MtDialog)
Vue.component('mt-button', MtButton)
Vue.component('mt-form', MtForm)
Vue.component('mt-form-item', MtFormItem)
Vue.component('mt-input', MtInput)
Vue.component('mt-data-grid', MtDataGrid)
Vue.component('mt-select', MtSelect)
Vue.component('mt-tabs', MtTabs)
Vue.component('mt-tooltip', MtTooltip)
Vue.component('mt-toast', MtToast)
Vue.component('mt-date-picker', MtDatePicker)
Vue.component('mt-tree-view', MtTreeView)
Vue.component('mt-icon', MtIcon)
Vue.prototype.$dialog = Dialog['COMPONENT']

//$toast
import * as Toast from 'COMPONENTS/Toast'
Vue.prototype.$toast = Toast['COMPONENT']

//$tooltip
import * as ToolTip from 'COMPONENTS/Tooltip'
Vue.prototype.$tooltip = ToolTip['COMPONENT']

//common-template-page组件
import MtTemplatePage from '@mtech/common-template-page'
// import MtTemplatePage from '@/components/template-page'
Vue.component('mt-template-page', MtTemplatePage)

//common-tree-view组件
import MtCommonTree from '@mtech/common-tree-view'
Vue.component('mt-common-tree', MtCommonTree)

//common-rule-config组件
import MtRuleConfig from '@mtech/common-rule-config'
Vue.component('mt-rule-config', MtRuleConfig)

import MtMicroLoading from 'COMPONENTS/micro-loading'
Vue.component('mt-loading', MtMicroLoading)

// 设置appCode，上线后需删除
import { utils } from '@mtech-common/utils'
utils.setAppCode('srm')

import commonPermission from '@mtech/common-permission'
Vue.use(commonPermission)
