<!--供方-寻源协同-报价 主页面 -->
<template>
  <div id="app" class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickCellTool="handleClickCellTool"
    >
      <costFactorLinkagePricingConfirm slot="slot-2" />
    </mt-template-page>
  </div>
</template>

<script>
import { pageConfig } from './config'
import costFactorLinkagePricingConfirm from '../costFactorLinkagePricingConfirm/index'
export default {
  components: { costFactorLinkagePricingConfirm },
  data() {
    return {
      pageConfig: pageConfig(this.$API.quotationList.listQueryFile)
    }
  },
  mounted() {},
  methods: {
    //点击是否参与
    handleClickCellTool(e) {
      if (e.tool.id == 'join') {
        this.handleJoinEvent(e)
      }
    },
    //点击参与
    handleJoinEvent(e) {
      if (e.tool.id == 'join') {
        let parameter = {
          rfxCode: e.data.rfxCode,
          rfxId: e.data.rfxId,
          supplierId: e.data.supplierId,
          biddingMode: e.data.biddingMode
        }
        this.$API.quotationList.tenderSync(parameter).then((res) => {
          if (res.code == 200) {
            this.$router.push({
              path: 'offer-bid-part-in',
              query: {
                source: e.data.sourcingMode,
                companyCode: e.data.companyCode,
                rfxId: e.data.rfxId,
                biddingMode: e.data.biddingMode,
                key: this.$utils.randomString()
              }
            })
          }
        })
      }
    },
    //点击编码
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
      if (e.field == 'rfxCode') {
        let parameter = {
          rfxCode: e.data.rfxCode,
          rfxId: e.data.rfxId,
          supplierId: e.data.supplierId,
          biddingMode: e.data.biddingMode
        }
        this.$API.quotationList.tenderSync(parameter).then((res) => {
          if (res.code == 200) {
            this.$router.push({
              path: 'offer-bid-part-in',
              query: {
                source: e.data.sourcingMode,
                companyCode: e.data.companyCode,
                rfxId: e.data.rfxId,
                biddingMode: e.data.biddingMode,
                key: this.$utils.randomString()
              }
            })
          }
        })
      }
    }
  },
  deactivated() {
    const domArr = document.getElementsByClassName('e-tooltip-wrap')
    const arr = Array.from(domArr)
    if (arr.length > 0) {
      arr[0].style.display = 'none'
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  /deep/.e-headercelldiv {
    padding: 0 0.6em 0 0.6em !important;
  }
}
</style>
<style>
.OfferBid-status-class {
  margin-bottom: 5px;
}
.OfferBid-status0 {
  width: 56px;
  height: 20px;
  background: #f4f4f4;
  border-radius: 2px;
  padding: 4px;
  color: #9a9a9a;
}
.OfferBid-status1 {
  width: 56px;
  height: 20px;
  background: #eef2f9;
  border-radius: 2px;
  padding: 4px;
  color: #6386c1;
}
.OfferBid-status2 {
  width: 56px;
  height: 20px;
  background: #eef2f9;
  border-radius: 2px;
  padding: 4px;
  color: #6386c1;
}
</style>
<style>
.user-content {
  color: red;
}
body,
html {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
}
#app {
  padding: 0;
  height: 100%;
  width: 100%;
}
.test-btn {
  position: fixed;
  bottom: 10px;
  z-index: 2;
  left: 50px;
}
</style>
