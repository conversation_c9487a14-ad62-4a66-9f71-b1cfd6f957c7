import { i18n, permission } from '@/main.js'
import { transferStatusList, RFX_STATUS, SUMMARY_STATUS } from '@/constants'

// 公共列
const commonColumnData = [
  {
    width: '120',
    field: 'transferStatus',
    headerText: i18n.t('当前阶段'),
    // formatter: function ({ field }, item) {
    //   const cellVal = item[field];
    //   return transferStatusList[cellVal] ?? cellVal;
    // },
    valueConverter: {
      type: 'map',
      map: Object.keys(transferStatusList).map(function (i) {
        return {
          text: transferStatusList[i],
          value: +i,
          cssClass: 'title-#6386c1'
        }
      })
    }
  },
  {
    width: '120',
    field: 'status',
    headerText: i18n.t('单据状态'),
    // formatter: function ({ field }, item) {
    //   const cellVal = item[field];
    //   return RFX_STATUS[cellVal] ?? cellVal;
    // },
    valueConverter: {
      type: 'map',
      map: Object.keys(RFX_STATUS).map(function (i) {
        return {
          text: RFX_STATUS[i],
          value: +i,
          cssClass: 'title-#6386c1'
        }
      })
    }
  }
]
// 待报名列表
const todoColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    width: '160',
    field: 'rfxCode',
    headerText: i18n.t('RFX单号'),
    cssClass: 'field-content'
  },
  {
    width: '160',
    field: 'rfxName',
    headerText: i18n.t('标题')
  },
  {
    width: '160',
    field: 'sourcingObj',
    headerText: i18n.t('询价对象')
  },
  ...commonColumnData,
  {
    width: '200',
    field: 'companyName',
    headerText: i18n.t('客户')
  },
  {
    width: '160',
    field: 'bidEndTime',
    headerText: i18n.t('报价截止时间')
    //参与状态
    //应标截止时间
    // 当前轮次   roundNo
    //报价开始时间 bidStartTime
    //报价结束时间 bidEndTime
    //开标时间
    //议价截止时间
  },
  {
    width: '120',
    field: 'sourcingDirection',
    headerText: i18n.t('询价方向'),
    valueConverter: {
      type: 'map',
      map: {
        forward: i18n.t('递增'),
        reverse: i18n.t('递减'),
        unlimited: i18n.t('无限制')
      }
    }
  },
  {
    width: '120',
    field: 'biddingMode',
    headerText: i18n.t('招标方式'),
    valueConverter: {
      type: 'map',
      map: {
        open: i18n.t('公开'),
        target: i18n.t('邀请')
        // supplierOpen: i18n.t('合作伙伴公开')
      }
    }
  },
  {
    width: '120',
    field: 'purExecutorName',
    headerText: i18n.t('采购员')
  },
  {
    width: '1',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '1',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  }
  //创建时间   createTime
  //备注  remark
]
// 已报名列表
const joinColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    width: '160',
    field: 'rfxCode',
    headerText: i18n.t('RFX单号'),
    cssClass: 'field-content'
  },
  {
    width: '160',
    field: 'rfxName',
    headerText: i18n.t('标题')
  },
  {
    width: '160',
    field: 'sourcingObj',
    headerText: i18n.t('询价对象')
  },
  ...commonColumnData,
  {
    width: '120',
    field: 'summaryStatus',
    headerText: i18n.t('报价状态'),
    formatter: function ({ field }, item) {
      const cellVal = item[field]
      return SUMMARY_STATUS[cellVal] ?? cellVal
    }
  },
  {
    width: '150',
    field: 'companyName',
    headerText: i18n.t('客户')
  },
  {
    width: '150',
    field: 'bidEndTime',
    headerText: i18n.t('报价截止时间') //报名截止时间
    // 资格预审截止时间
    // 报价截止时间
  },
  {
    width: '100',
    field: 'sourcingDirection',
    headerText: i18n.t('询价方向'),
    valueConverter: {
      type: 'map',
      map: {
        forward: i18n.t('递增'),
        reverse: i18n.t('递减'),
        unlimited: i18n.t('无限制')
      }
    }
  },
  {
    width: '120',
    field: 'biddingMode',
    headerText: i18n.t('招标方式'),
    valueConverter: {
      type: 'map',
      map: {
        open: i18n.t('公开'),
        target: i18n.t('邀请')
        // supplierOpen: i18n.t('合作伙伴公开')
      }
    }
  },
  {
    width: '100',
    field: 'purExecutorName',
    headerText: i18n.t('采购员')
  },
  {
    width: '1',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '1',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  }
  // 议价截止时间
  // 创建时间
  //备注
]

export const pageConfig = (url) => [
  {
    title: i18n.t('待报名'),
    toolbar: [],
    gridId: permission.gridId['supply']['rfq']['list']['unJoined'],
    useToolTemplate: false,
    grid: {
      allowFiltering: true,
      allowSorting: false,
      useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
      frozenColumns: 2,
      columnData: todoColumnData,
      asyncConfig: {
        url,
        params: { status: 0, sourcingMode: 'rfq' },
        // queryBuilderWrap 非正常传参,放在参数子级里面
        queryBuilderWrap: 'queryBuilderDTO'
      }
    }
  },

  {
    title: i18n.t('已报名'),
    toolbar: [],
    gridId: permission.gridId['supply']['rfq']['list']['joined'],
    useToolTemplate: false,
    grid: {
      allowFiltering: true,
      allowSorting: false,
      useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
      frozenColumns: 2,
      columnData: joinColumnData,
      // dataSource:[],
      asyncConfig: {
        url,
        params: { status: 1, sourcingMode: 'rfq' },
        queryBuilderWrap: 'queryBuilderDTO'
      }
    }
  },
  {
    title: i18n.t('联动定价确认')
  }
]
