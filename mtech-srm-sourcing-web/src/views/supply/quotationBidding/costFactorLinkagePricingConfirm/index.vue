<template>
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="rfxCode" :label="$t('定价单号')" label-style="top">
          <mt-input
            v-model="searchFormModel.rfxCode"
            :show-clear-button="true"
            :placeholder="$t('请输入定价单号')"
          />
        </mt-form-item>
        <mt-form-item prop="rfxName" :label="$t('定价单名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.rfxName"
            :show-clear-button="true"
            :placeholder="$t('请输入定价单名称')"
          />
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('确认状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.status"
            :data-source="statusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :placeholder="$t('请选择确认状态')"
          />
        </mt-form-item>
        <mt-form-item prop="publishTime" :label="$t('发布时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.publishTime"
            :allow-edit="false"
            :placeholder="$t('请选择发布时间')"
            :open-on-focus="true"
            @change="(e) => handleDateChange('publishTime', e)"
          />
        </mt-form-item>
        <mt-form-item prop="submitTime" :label="$t('确认时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.submitTime"
            :allow-edit="false"
            :placeholder="$t('请选择确认时间')"
            :open-on-focus="true"
            @change="(e) => handleDateChange('submitTime', e)"
          />
        </mt-form-item>
        <mt-form-item prop="endTime" :label="$t('确认截止时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.endTime"
            :allow-edit="false"
            :placeholder="$t('请选择确认截止时间')"
            :open-on-focus="true"
            @change="(e) => handleDateChange('endTime', e)"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      grid-id="ab75b2da-9d68-4ab0-bb67-5afff6398dfe"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      @refresh="handleSearch"
    />
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import { statusList } from './config/index'
import XEUtils from 'xe-utils'

export default {
  components: {
    ScTable,
    CollapseSearch
  },
  mixins: [mixin],
  data() {
    return {
      statusList,
      searchFormModel: {},
      tableData: [],
      loading: false,
      type: 'list'
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  mounted() {
    this.handleSearch()
  },
  activated() {
    this.handleSearch()
  },
  methods: {
    // 日期格式化
    handleDateChange(prefix, e) {
      if (e.startDate) {
        const startDate = XEUtils.toDateString(e.startDate, 'yyyy-MM-dd') + ' 00:00:00'
        const endDate = XEUtils.toDateString(e.endDate, 'yyyy-MM-dd') + ' 23:59:59'
        this.searchFormModel[prefix + 'Start'] = startDate
        this.searchFormModel[prefix + 'End'] = endDate
      } else {
        this.searchFormModel[prefix + 'Start'] = null
        this.searchFormModel[prefix + 'End'] = null
      }
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    // 查询
    async handleSearch(type) {
      // 解决修改查询条件后，查询结果有数据但未正常显示的问题（type: pageChange/refresh）
      if (!type) {
        this.pageInfo.current = 1
        this.$refs.pageRef.jumpNum = 1
        this.$refs.pageRef.currentPage = 1
      }

      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      delete params.endTime
      delete params.publishTime

      this.loading = true
      const res = await this.$API.costFactorLinkagePricingConfirm
        .queryCflpcList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []

        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击单元格
    handleClickCellTitle(row, column) {
      switch (column.field) {
        case 'rfxCode':
          this.$router.push({
            name: 'linkage-pricing-confirm-detail',
            query: {
              id: row.id,
              refreshId: Date.now()
            }
          })
          break
        default:
          break
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
