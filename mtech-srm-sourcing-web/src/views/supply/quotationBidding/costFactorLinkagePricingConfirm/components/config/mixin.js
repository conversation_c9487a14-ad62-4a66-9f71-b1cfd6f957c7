import XEUtils from 'xe-utils'
import SplitCell from '@/components/VxeComponents/SplitCell/index.vue'

export default {
  components: { SplitCell },
  data() {
    return {
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 20,
        pageSizes: [20, 50, 200, 500, 1000, 2000]
      },
      pageInfo: {
        size: 20,
        current: 1
      },
      tableData: [],
      loading: false
    }
  },
  computed: {
    toolbar() {
      switch (this.type) {
        case 'costFactor':
        case 'material':
          return [
            {
              code: 'export',
              name: this.$t('导出'),
              status: 'info',
              isHidden: !this.$route.query.id
            }
          ]
        default:
          return []
      }
    },
    tableRef() {
      return this.$refs.detailSctableRef.$refs.xGrid
    },
    columns() {
      switch (this.type) {
        case 'costFactor':
          return [
            {
              width: 50,
              type: 'checkbox'
            },
            {
              field: 'costFactorCode',
              title: this.$t('成本因子编码'),
              minWidth: 150
            },
            {
              field: 'costFactorName',
              title: this.$t('成本因子名称'),
              minWidth: 150
            },
            {
              field: 'costFactorSpec',
              title: this.$t('规格')
            },
            {
              field: 'costFactorBrand',
              title: this.$t('品牌')
            },
            {
              field: 'basicMeasureUnitName',
              title: this.$t('单位'),
              slots: {
                default: ({ row }) => {
                  return [
                    <span>
                      {(row.basicMeasureUnitCode || '') + '-' + (row.basicMeasureUnitName || '')}
                    </span>
                  ]
                }
              }
            },
            {
              field: 'costFactorAttr',
              title: this.$t('属性大类')
            },
            {
              field: 'costFactorGroup',
              title: this.$t('属性中类')
            },
            {
              field: 'historyUntaxedUnitPrice',
              title: this.$t('历史单价（未税）'),
              minWidth: 140
            },
            {
              field: 'historyTaxedUnitPrice',
              title: this.$t('历史单价（含税）'),
              minWidth: 140
            },
            {
              field: 'unitPriceUntaxed',
              title: this.$t('单价（未税）')
            },
            {
              field: 'unitPriceTaxed',
              title: this.$t('单价（含税）')
            },
            {
              field: 'supplierCode',
              title: this.$t('供应商编码')
            },
            {
              field: 'supplierName',
              title: this.$t('供应商名称'),
              minWidth: 150
            },
            {
              field: 'status',
              title: this.$t('是否生效'),
              slots: {
                default: ({ row }) => {
                  const statusrMap = {
                    0: this.$t('草稿'),
                    1: this.$t('生效'),
                    2: this.$t('失效')
                  }
                  return [<span>{row.status ? statusrMap[row.status] : '-'}</span>]
                }
              }
            },
            {
              field: 'priceValidStartDate',
              title: this.$t('生效日期'),
              minWidth: 140,
              slots: {
                default: ({ row }) => {
                  const priceValidStartDate = XEUtils.toDateString(
                    row.priceValidStartDate,
                    'yyyy-MM-dd'
                  )
                  return [<span>{priceValidStartDate}</span>]
                }
              }
            },
            {
              field: 'priceValidEndDate',
              title: this.$t('失效日期'),
              minWidth: 140,
              slots: {
                default: ({ row }) => {
                  const priceValidEndDate = XEUtils.toDateString(
                    row.priceValidEndDate,
                    'yyyy-MM-dd'
                  )
                  return [<span>{priceValidEndDate}</span>]
                }
              }
            },
            {
              field: 'remark',
              title: this.$t('备注'),
              minWidth: 180
            }
          ]
        case 'material':
          return [
            {
              width: 50,
              type: 'checkbox'
            },
            {
              field: 'itemNo',
              title: this.$t('行号'),
              width: 50
            },
            {
              field: 'costFactorCode',
              title: this.$t('成本因子编码'),
              slots: {
                default: ({ row }) => {
                  const arr = []
                  row.costFactorList?.forEach((item) => {
                    arr.push(item.costFactorCode)
                  })
                  return [<span>{arr.join(',')}</span>]
                }
              }
            },
            {
              field: 'costFactorName',
              title: this.$t('成本因子名称'),
              minWidth: 150,
              slots: {
                default: ({ row }) => {
                  const arr = []
                  row.costFactorList?.forEach((item) => {
                    arr.push(item.costFactorName)
                  })
                  return [<span>{arr.join(',')}</span>]
                }
              }
            },
            {
              field: 'supplierCode',
              title: this.$t('供应商编码')
            },
            {
              field: 'supplierName',
              title: this.$t('供应商名称'),
              minWidth: 180
            },
            {
              field: 'factoryCode',
              title: this.$t('工厂编码')
            },
            {
              field: 'factoryName',
              title: this.$t('工厂名称'),
              minWidth: 150
            },
            {
              field: 'materialCode',
              title: this.$t('物料编码'),
              minWidth: 150
            },
            {
              field: 'materialName',
              title: this.$t('物料名称'),
              minWidth: 150
            },
            {
              field: 'categoryCode',
              title: this.$t('品类编码')
            },
            {
              field: 'categoryName',
              title: this.$t('品类名称')
            },
            {
              field: 'historyUntaxedUnitPrice',
              title: this.$t('历史单价（未税）'),
              minWidth: 140,
              slots: {
                default: ({ row }) => {
                  return [<SplitCell list={row.historyUntaxedUnitPriceList} />]
                }
              }
            },
            {
              field: 'historyTaxedUnitPrice',
              title: this.$t('历史单价（含税）'),
              minWidth: 140,
              slots: {
                default: ({ row }) => {
                  return [<SplitCell list={row.historyTaxedUnitPriceList} />]
                }
              }
            },
            {
              field: 'unitPriceUntaxed',
              title: this.$t('单价（未税）'),
              slots: {
                default: ({ row }) => {
                  return [<SplitCell list={row.unitPriceUntaxedList} />]
                }
              }
            },
            {
              field: 'unitPriceTaxed',
              title: this.$t('单价（含税）'),
              slots: {
                default: ({ row }) => {
                  return [<SplitCell list={row.unitPriceTaxedList} />]
                }
              }
            },
            {
              field: 'stepValue',
              title: this.$t('阶梯数量'),
              slots: {
                default: ({ row }) => {
                  return [<SplitCell list={row.stepValueList} />]
                }
              }
            },
            {
              field: 'stageType',
              title: this.$t('阶梯类型'),
              slots: {
                default: ({ row }) => {
                  const stageTypeMap = {
                    '-1': this.$t('无阶梯'),
                    0: this.$t('按数量'),
                    1: this.$t('按时间'),
                    2: this.$t('按金额'),
                    3: this.$t('数量逐层')
                  }
                  return [<span>{stageTypeMap[row.stageType]}</span>]
                }
              }
            },
            {
              field: 'directDeliverAddr',
              title: this.$t('直送地')
            },
            {
              field: 'costModelName',
              title: this.$t('成本模型名称'),
              minWidth: 130
            },
            {
              field: 'currencyCode',
              title: this.$t('币种编码')
            },
            {
              field: 'currencyName',
              title: this.$t('币种名称')
            },
            {
              field: 'basicUnit',
              title: this.$t('基本单位'),
              slots: {
                default: ({ row }) => {
                  return [<span>{(row.basicUnit || '') + '-' + (row.basicUnitName || '')}</span>]
                }
              }
            },
            {
              field: 'purchaseUnitName',
              title: this.$t('订单单位'),
              slots: {
                default: ({ row }) => {
                  return [
                    <span>{(row.purchaseUnitCode || '') + '-' + (row.purchaseUnitName || '')}</span>
                  ]
                }
              }
            },
            {
              field: 'priceUnitName',
              title: this.$t('价格单位')
            },
            {
              field: 'taxRateCode',
              title: this.$t('税率编码')
            },
            {
              field: 'taxRateName',
              title: this.$t('税率名称')
            },
            {
              field: 'taxRate',
              title: this.$t('税率')
            },
            {
              field: 'quoteAttr',
              title: this.$t('报价属性'),
              slots: {
                default: ({ row }) => {
                  const quoteAttrMap = {
                    mailing_price: this.$t('寄售价'),
                    standard_price: this.$t('标准价')
                  }

                  return [<span>{row.quoteAttr ? quoteAttrMap[row.quoteAttr] : '-'}</span>]
                }
              }
            },
            {
              field: 'priceEffectiveMethod',
              title: this.$t('报价生效方式'),
              slots: {
                default: ({ row }) => {
                  const methodMap = {
                    in_warehouse: this.$t('按照入库'),
                    out_warehouse: this.$t('按出库'),
                    order_date: this.$t('按订单日期')
                  }
                  return [
                    <span>
                      {row.priceEffectiveMethod ? methodMap[row.priceEffectiveMethod] : '-'}
                    </span>
                  ]
                }
              }
            },
            {
              field: 'minPackageQty',
              title: this.$t('最小包装量')
            },
            {
              field: 'minPurchaseQty',
              title: this.$t('最小采购量')
            },
            {
              field: 'leadTime',
              title: this.$t('L/T')
            },
            {
              field: 'unconditionalLeadTime',
              title: this.$t('无条件L/T')
            },
            {
              field: 'priceValidStartDate',
              title: this.$t('生效日期'),
              slots: {
                default: ({ row }) => {
                  const date = XEUtils.toDateString(row.priceValidStartDate, 'yyyy-MM-dd')
                  return [<span>{date}</span>]
                }
              }
            },
            {
              field: 'priceValidEndDate',
              title: this.$t('失效日期'),
              slots: {
                default: ({ row }) => {
                  const date = XEUtils.toDateString(row.priceValidEndDate, 'yyyy-MM-dd')
                  return [<span>{date}</span>]
                }
              }
            },
            {
              field: 'status',
              title: this.$t('确认状态'),
              slots: {
                default: ({ row }) => {
                  const statusMap = {
                    0: this.$t('待确认'),
                    1: this.$t('已确认'),
                    2: this.$t('已驳回')
                  }
                  return [
                    <span>{row.status || row.status === 0 ? statusMap[row.status] : '-'}</span>
                  ]
                }
              }
            },
            {
              field: 'priceRecordCode',
              title: this.$t('成本分析'),
              minWidth: 140,
              fixed: 'right',
              slots: {
                default: ({ row }) => {
                  return [
                    <SplitCell
                      list={row.priceRecordCodeList}
                      type='operation'
                      operationName={this.$t('成本分析')}
                      on-click={(index) => this.handleCostAnalysis(row, index)}
                    />
                  ]
                }
              }
            }
          ]
        default:
          return []
      }
    }
  },
  mounted() {},
  methods: {
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.getTableData()
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.getTableData()
    }
  }
}
