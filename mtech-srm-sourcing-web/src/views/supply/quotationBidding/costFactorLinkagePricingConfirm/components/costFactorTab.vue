<template>
  <div>
    <sc-table
      ref="detailSctableRef"
      row-id="id"
      grid-id="2323d439-a600-4821-b96a-43cc13997a3b"
      :keep-source="true"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      :loading="loading"
      @refresh="getTableData"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          v-show="!item.isHidden"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :disabled="item.disabled"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import { download, getHeadersFileName } from '@/utils/utils'
// import XEUtils from 'xe-utils'
// import { getEndDate } from '@/utils/obj'

export default {
  components: {
    ScTable
  },
  mixins: [mixin],
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      type: 'costFactor'
    }
  },
  computed: {},
  mounted() {
    this.getTableData()
  },
  methods: {
    async getTableData() {
      const params = {
        rfxId: this.$route.query.id,
        page: this.pageInfo
      }
      this.loading = true
      const res = await this.$API.costFactorLinkagePricingConfirm
        .queryCflpcCfDetailList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []

        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const { code } = e
      const selectedRecords = this.tableRef.getCheckboxRecords()
      switch (code) {
        case 'add':
          this.handleAdd()
          break
        case 'save':
          this.handleSave()
          break
        case 'delete':
          this.handleDelete(selectedRecords)
          break
        case 'export':
          this.handleExport()
          break
        default:
          break
      }
    },
    // 导出
    async handleExport() {
      const includeColumnFiledNames = []
      const tableColumns = this.tableRef.getColumns()
      tableColumns.forEach((col) => col.field && includeColumnFiledNames.push(col.field))

      const params = {
        rfxId: this.$route.query.id,
        page: {
          size: this.pageSettings.pageSize,
          current: this.pageSettings.current
        },
        includeColumnFiledNames
      }
      const res = await this.$API.costFactorLinkagePricingConfirm.exportCflpcCfDetailList(params)
      if (res.data) {
        this.$toast({
          type: 'success',
          content: this.$t('正在导出，请稍后！')
        })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 0px 8px;
  background: #fff;
}
</style>
