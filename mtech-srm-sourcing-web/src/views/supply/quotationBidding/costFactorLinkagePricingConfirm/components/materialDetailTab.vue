<template>
  <div>
    <sc-table
      ref="detailSctableRef"
      row-id="id"
      grid-id="3f33ca8c-e551-4c29-a86d-7325f474a931"
      :keep-source="true"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      :loading="loading"
      :cell-style="handleCellStyle"
      @refresh="getTableData"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          v-show="!item.isHidden"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :disabled="item.disabled"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {
    ScTable
  },
  mixins: [mixin],
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      type: 'material',
      // 不合并的单元格：历史单价含税/未税、单价含税/未税、阶梯数量
      unmergeList: [
        'id',
        'historyUntaxedUnitPrice',
        'historyTaxedUnitPrice',
        'unitPriceUntaxed',
        'unitPriceTaxed',
        'stepValue',
        'priceRecordCode'
      ]
    }
  },
  computed: {},
  mounted() {
    this.getTableData()
  },
  methods: {
    async getTableData() {
      const params = {
        rfxId: this.$route.query.id,
        page: this.pageInfo
      }
      this.loading = true
      const res = await this.$API.costFactorLinkagePricingConfirm
        .queryCflpcMdDetailList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = this.groupData(res.data?.records || [])

        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 对数据进行分组
    groupData(list) {
      const groupKeyList = []
      const tempList = []
      const resList = []
      list.forEach((item) => {
        const { itemGroupId, id } = item
        const groupKey = itemGroupId || id
        if (!groupKeyList.includes(groupKey)) {
          groupKeyList.push(groupKey)
          tempList.push([])
        }
        const i = groupKeyList.indexOf(groupKey)
        tempList[i].push(item)
      })
      tempList.forEach((item) => {
        const obj = {}
        item.forEach((t) => {
          this.unmergeList.forEach((key) => {
            if (!obj[key + 'List']) {
              obj[key + 'List'] = []
            }
            obj[key + 'List'].push(t[key])
          })
        })
        resList.push({ ...item[0], ...obj, rowSpan: item.length })
      })
      return resList
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      switch (e.code) {
        case 'export':
          this.handleExport()
          break
        default:
          break
      }
    },
    // 导出
    async handleExport() {
      const includeColumnFiledNames = []
      const tableColumns = this.tableRef.getColumns()
      tableColumns.forEach((col) => col.field && includeColumnFiledNames.push(col.field))

      const params = {
        rfxId: this.$route.query.id,
        page: {
          size: this.pageSettings.pageSize,
          current: this.pageSettings.current
        },
        includeColumnFiledNames
      }
      const res = await this.$API.costFactorLinkagePricingConfirm.exportCflpcMdDetailList(params)
      if (res.data) {
        this.$toast({
          type: 'success',
          content: this.$t('正在导出，请稍后！')
        })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      }
    },
    // 成本分析
    handleCostAnalysis(row, index) {
      this.$router.push({
        name: 'linkage-pricing-confirm-cost-analysis',
        query: {
          rfxId: this.$route.query.id,
          rfxItemId: row.idList[index],
          priceRecordCode: row.priceRecordCodeList[index],
          refreshId: Date.now()
        }
      })
    },
    // 单元格样式
    handleCellStyle(e) {
      const { row, column } = e
      const height = row?.rowSpan ? row.rowSpan * 32 : 32
      let padding = ''
      if (!this.unmergeList.includes(column.field)) {
        padding = '0 10px'
      }
      return { padding, height: `${height}px` }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 0px 8px;
  background: #fff;
}
::v-deep {
  .vxe-table--render-default .vxe-body--column.col--ellipsis:not(.col--actived) > .vxe-cell {
    max-height: unset !important;
    padding: 0;
  }
}
</style>
