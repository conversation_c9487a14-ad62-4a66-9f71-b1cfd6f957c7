import cloneDeep from 'lodash/cloneDeep'
import XEUtils from 'xe-utils'

export default {
  data() {
    return {}
  },
  computed: {
    defaultColumns() {
      return [
        {
          field: 'nodeName',
          title: this.$t('成本构成'),
          fixed: 'left',
          treeNode: true,
          minWidth: 200
        },
        {
          type: 'seq',
          title: this.$t('排序'),
          width: 80,
          slots: {
            default: ({ row, seq }) => {
              row.sortCode = seq
              return [<span>{seq}</span>]
            }
          }
        },
        {
          field: 'calculationFormulaSpec',
          title: this.$t('计算公式'),
          minWidth: 200
        },
        {
          field: 'result',
          title: this.$t('成本'),
          minWidth: 120,
          slots: {
            default: ({ row, level }) => {
              let tip = ''
              if (level === 0) {
                tip = this.$t('合计：')
              } else if (row.itemList?.length) {
                tip = this.$t('小计：')
              }
              return [
                <div>
                  <span>
                    <span style='color:#606266'>{tip}</span>
                    {row.result}
                  </span>
                </div>
              ]
            }
          }
        },
        {
          field: 'percent',
          title: this.$t('占销价百分比（%）'),
          minWidth: 140,
          slots: {
            default: ({ row, level }) => {
              let tip = ''
              if (level === 0) {
                tip = this.$t('合计：')
              } else if (row.itemList?.length) {
                tip = this.$t('小计：')
              }
              return [
                <div>
                  <span>
                    {tip}
                    {row.percent}
                  </span>
                </div>
              ]
            }
          }
        },
        {
          field: 'remark',
          title: this.$t('备注'),
          minWidth: 120
        },
        {
          field: 'errorMsg',
          title: this.$t('提示信息'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              return [<span style='color: red'>{row.errorMsg}</span>]
            }
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    // 处理动态配置信息
    formateColumns(obj) {
      const { extra_formula, form, header, classifyCode } = obj
      const typeMap = {
        0: 'number',
        1: 'text',
        2: 'select'
      }
      // 成本模板分类
      const cmtClassifyCode = classifyCode[0]?.classifyCode

      // 附加动态表单信息
      this.extraFormItems = []
      this.extraFormula = extra_formula || []
      extra_formula?.forEach((item) => {
        const {
          id,
          columnCode,
          columnName,
          columnType,
          columnAlias,
          valueSet,
          calculationFormula,
          calculationFormulaSpec
        } = item
        this.extraFormItems.push({
          id,
          type: typeMap[item.columnType],
          fieldCode: columnCode,
          fieldName: columnName,
          columnAlias,
          columnType,
          valueSet,
          calculationFormula,
          calculationFormulaSpec,
          readonly: true
        })
      })

      // 通用动态表单信息
      this.commonFormItems = []
      form?.forEach((item) => {
        this.commonFormItems.push({
          type: typeMap[item.columnType],
          fieldCode: item.columnCode,
          fieldName: item.columnName,
          columnAlias: item.columnAlias,
          columnType: item.columnType,
          valueSet: item.valueSet,
          readonly: true
        })
      })

      // 动态列信息
      this.dynamicColumns = []
      const defaultColumns = cloneDeep(this.defaultColumns)
      // 外发类型，不显示成本列
      if (cmtClassifyCode === 'out_going') {
        defaultColumns.splice(3, 1)
      }
      header?.forEach((item) => {
        this.dynamicColumns.push({
          field: item.columnCode,
          title: item.columnName,
          columnAlias: item.columnAlias,
          columnType: item.columnType,
          minWidth: item.columnName.length > 6 ? 120 : 0,
          slots: {
            default: ({ row, level }) => {
              const tip =
                level === 0 ? this.$t('合计：') : row.itemList?.length ? this.$t('小计：') : ''
              let template = [<span>{row[item.columnCode]}</span>]
              // 外发类型，非末级金额列，显示为“合计/小计XXXXX”
              if (cmtClassifyCode === 'out_going' && item.columnCode === 'amount') {
                template = [
                  <span>
                    {tip}
                    {row[item.columnCode]}
                  </span>
                ]
              }
              return template
            }
          }
        })
      })
      defaultColumns.splice(2, 0, ...this.dynamicColumns)
      this.columns = defaultColumns
    },
    // 序列化基础信息表单
    serializeTempForm(prefix, data) {
      const {
        id,
        materialCode,
        supplierCode,
        counterName,
        costModelCode,
        costModelName,
        costModelVersionCode,
        stepValue,
        directDeliverAddr,
        status
      } = data
      this[prefix + 'DefaultId'] = id
      const arr = [materialCode, supplierCode, counterName]
      const tempArr = arr.filter((item) => {
        if (item) {
          return item
        }
      })
      this.tempForm[prefix + 'HistoryInfo'] = tempArr.join('+')
      this.tempForm = {
        ...this.tempForm,
        costModelCode,
        costModelName,
        costModelVersionCode,
        stepValue,
        deliveryPlace: directDeliverAddr,
        status
      }
    },
    // 序列化表单
    serializeFormData(dataList, type) {
      const formData = {}
      if (type === 'common') {
        dataList.forEach((item) => {
          const { columnType, columnCode } = item
          if (columnType === 0) {
            item[columnCode] = item.dataValue
          } else if (columnType === 1) {
            item[columnCode] = item.stringValue
          } else {
            const key = Object.keys(item.jsonValue || {})?.find((k) => k?.includes('Code'))
            item[columnCode] = key ? item.jsonValue[key] : null
          }
          formData[item.fieldCode] = item[columnCode]
        })
      } else {
        dataList.forEach((item) => {
          formData[item.fieldCode] = item.result
        })
      }
      return formData
    },
    // 序列化列表
    serializeItemList(treeList) {
      const resList = treeList.map((item) => {
        item.itemValueList?.forEach((v) => {
          if (v.columnType === 0) {
            item[v.columnCode] = v.dataValue
          } else if (v.columnType === 1) {
            item[v.columnCode] = v.stringValue
          } else {
            const key = Object.keys(v.jsonValue || {})?.find((k) => k?.includes('Code'))
            item[v.columnCode] = key ? v.jsonValue[key] : null
            item[v.columnCode + 'Json'] = key ? v.jsonValue : null
          }
        })
        return {
          ...item,
          itemList: item.itemList ? this.serializeItemList(item.itemList) : []
        }
      })
      return resList
    },
    // 左右两个Table一起滚动
    handleScroll(type, e) {
      const { isX, isY, scrollLeft, scrollTop } = e
      const prefix = type === 'left' ? 'right' : 'left'
      if (this[prefix + 'Expand']) {
        const tableRef = this.$refs[prefix + 'TableRef']?.$refs?.xGrid
        isX && tableRef?.scrollTo(scrollLeft, null)
        isY && tableRef?.scrollTo(null, scrollTop)
      }
    },
    // 控制左右两部分内容展开、折叠
    handleToggle(key) {
      this[key] = !this[key]
      this.$refs.leftTableRef.handleResize(0)
      this.$refs.rightTableRef.handleResize(1)
    },
    // 单元格样式
    handleCellStyle(type, e) {
      const { row, column } = e
      const tableData = type === 'left' ? this.rightTableData : this.leftTableData
      const tempRow = XEUtils.findTree(tableData, (item) => item.id === row.id, {
        children: 'itemList'
      })?.item

      const fieldList = ['result']
      this.dynamicColumns?.forEach((item) => item.columnType === 0 && fieldList.push(item.field))
      if (
        fieldList.includes(column.field) &&
        tempRow &&
        (tempRow[column.field] || tempRow[column.field] == 0)
      ) {
        if (row[column.field] > tempRow[column.field]) {
          return { color: 'red' }
        }
        if (row[column.field] < tempRow[column.field]) {
          return { color: '#06d006' }
        }
      }
      return null
    }
  }
}
