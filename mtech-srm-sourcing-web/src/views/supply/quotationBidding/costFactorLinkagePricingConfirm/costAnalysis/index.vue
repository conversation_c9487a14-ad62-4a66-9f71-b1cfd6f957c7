<template>
  <div class="full-height">
    <div class="top-container">
      <div class="top-info">
        <div class="left">
          <div class="title">
            {{ tempForm.costModelCode }}
            <span class="sub-title" :style="tempForm.costModelCode && 'margin-left: 20px;'">{{
              tempForm.costModelName
            }}</span>
          </div>
          <div class="info">
            <div class="item">{{ $t('版本号：') }}{{ tempForm.costModelVersionCode || '--' }}</div>
            <div class="item">{{ $t('阶梯数量：') }}{{ tempForm.stepValue || '--' }}</div>
            <div class="item">{{ $t('直送地：') }}{{ tempForm.deliveryPlace || '--' }}</div>
            <div class="item">{{ $t('业务方：采购方') }}</div>
          </div>
        </div>
        <div class="right">
          <vxe-button size="small" @click="hanldeExport">{{ $t('导出') }}</vxe-button>
          <vxe-button size="small" @click="$router.go(-1)">{{ $t('返回') }}</vxe-button>
        </div>
      </div>
    </div>
    <div
      class="body-container"
      :class="[!leftExpand && 'left-hidden', !rightExpand && 'right-hidden']"
    >
      <!-- 左侧内容 -->
      <div class="letf container">
        <div class="content">
          <div class="body">
            <mt-form ref="leftTempFormRef" :model="tempForm">
              <mt-row :gutter="10">
                <mt-col :span="12">
                  <mt-form-item prop="leftCurShowCode" :label="$t('当前显示')" label-style="left">
                    <vxe-input v-model="tempForm.leftCurShowName" readonly />
                  </mt-form-item>
                </mt-col>
                <mt-col :span="12">
                  <mt-form-item prop="leftCounterName" :label="$t('报价次数')" label-style="left">
                    <vxe-input v-model="tempForm.leftCounterName" readonly />
                  </mt-form-item>
                </mt-col>
              </mt-row>
            </mt-form>
          </div>
          <div class="form-info" v-if="extraFormItems.length || commonFormItems.length">
            <div class="title" @click="handleToggle('leftFormInfoShow')">
              <i class="vxe-icon-caret-down" v-show="!leftFormInfoShow" />
              <i class="vxe-icon-caret-up" v-show="leftFormInfoShow" />
              <span>{{ $t('报价信息') }}</span>
            </div>
            <div class="body" v-show="leftFormInfoShow">
              <custom-form :data="leftExtraFormData" :form-items="extraFormItems" type="extra" />
              <div class="tip" v-if="extraFormItems.length">{{ priceTipInfo }}</div>
              <custom-form :data="leftCommonFormData" :form-items="commonFormItems" type="common" />
            </div>
          </div>
          <div class="table-info" v-if="leftExpand">
            <div class="title" @click="handleToggle('leftTableShow')">
              <i class="vxe-icon-caret-down" v-show="!leftTableShow" />
              <i class="vxe-icon-caret-up" v-show="leftTableShow" />
              <span>{{ $t('其他信息') }}</span>
            </div>
            <div class="body" v-show="leftTableShow">
              <sc-table
                ref="leftTableRef"
                row-id="id"
                :keep-source="true"
                :tree-config="treeConfig"
                :is-show-right-btn="false"
                :loading="loading"
                :columns="columns"
                :table-data="leftTableData"
                :cell-style="(e) => handleCellStyle('left', e)"
                @scroll="(e) => handleScroll('left', e)"
              />
            </div>
          </div>
        </div>
        <div v-show="rightExpand" class="operate left-operate" @click="handleToggle('leftExpand')">
          <i class="vxe-icon-arrow-left" />
        </div>
      </div>
      <!-- 右侧内容 -->
      <div class="right container">
        <div v-show="leftExpand" class="operate right-operate" @click="handleToggle('rightExpand')">
          <i class="vxe-icon-arrow-right" />
        </div>
        <div class="content" v-if="rightExpand">
          <div class="body">
            <mt-form ref="rightTempFormRef" :model="tempForm">
              <mt-row :gutter="10">
                <mt-col :span="12">
                  <mt-form-item prop="rightCurShowCode" :label="$t('当前显示')" label-style="left">
                    <vxe-input v-model="tempForm.rightCurShowName" readonly />
                  </mt-form-item>
                </mt-col>
                <mt-col :span="12">
                  <mt-form-item prop="rightHistory" :label="$t('历史报价')" label-style="left">
                    <vxe-input v-model="tempForm.rightHistory" readonly />
                  </mt-form-item>
                </mt-col>
              </mt-row>
            </mt-form>
          </div>
          <div class="form-info" v-if="extraFormItems.length || commonFormItems.length">
            <div class="title" @click="handleToggle('rightFormInfoShow')">
              <i class="vxe-icon-caret-down" v-show="!rightFormInfoShow" />
              <i class="vxe-icon-caret-up" v-show="rightFormInfoShow" />
              <span>{{ $t('报价信息') }}</span>
            </div>
            <div class="body" v-show="rightFormInfoShow">
              <custom-form :data="rightExtraFormData" :form-items="extraFormItems" type="extra" />
              <div class="tip" v-if="extraFormItems.length">{{ priceTipInfo }}</div>
              <custom-form
                :data="rightCommonFormData"
                :form-items="commonFormItems"
                type="common"
              />
            </div>
          </div>
          <div class="table-info">
            <div class="title" @click="handleToggle('rightTableShow')">
              <i class="vxe-icon-caret-down" v-show="!rightTableShow" />
              <i class="vxe-icon-caret-up" v-show="rightTableShow" />
              <span>{{ $t('其他信息') }}</span>
            </div>
            <div class="body" v-show="rightTableShow">
              <sc-table
                ref="rightTableRef"
                row-id="id"
                :keep-source="true"
                :tree-config="treeConfig"
                :is-show-right-btn="false"
                :loading="loading"
                :columns="columns"
                :table-data="rightTableData"
                :cell-style="(e) => handleCellStyle('right', e)"
                @scroll="(e) => handleScroll('right', e)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'
import CustomForm from './components/customForm.vue'
import mixin from './config/mixin'
import selectMixin from './config/selectMixin.js'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: { ScTable, CustomForm },
  mixins: [mixin, selectMixin],
  data() {
    return {
      treeConfig: {
        children: 'itemList',
        expandAll: true
      },
      columns: [],
      dynamicColumns: [],
      leftTableData: [],
      rightTableData: [],
      loading: false,
      leftExpand: true,
      rightExpand: true,
      extraFormItems: [],
      extraFormula: [],
      leftExtraFormData: {},
      rightExtraFormData: {},
      commonFormItems: [],
      leftCommonFormData: {},
      rightCommonFormData: {},
      tempForm: {
        leftCurShowName: this.$t('成本分析'),
        rightCurShowName: this.$t('成本分析'),
        rightHistory: this.$t('最近1次')
      },
      leftFormInfoShow: true,
      leftTableShow: true,
      rightFormInfoShow: true,
      rightTableShow: true
    }
  },
  computed: {
    leftTableRef() {
      return this.$refs.leftTableRef.$refs.xGrid
    },
    rightTableRef() {
      return this.$refs.rightTableRef.$refs.xGrid
    },
    priceTipInfo() {
      const arr = []
      this.extraFormula.forEach((item) => {
        const { columnName, calculationFormulaSpec } = item
        const spec =
          !calculationFormulaSpec && calculationFormulaSpec !== 0 ? '' : calculationFormulaSpec
        arr.push(columnName + '=' + spec)
      })
      return arr.join(';')
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.getCostModelDetailColumns()
      this.getAnalysInfo()
    },
    // 获取配置列、表单信息
    async getCostModelDetailColumns() {
      const { rfxId, rfxItemId, priceRecordCode } = this.$route.query
      const params = {
        rfxId,
        rfxItemId,
        priceRecordCode
      }
      const res = await this.$API.costFactorLinkagePricingConfirm.queryCflpcCostAnalysisColumns(
        params
      )
      if (res.code === 200 && res.data) {
        this.formateColumns(res.data)
        this.$refs.leftTableRef.handleResize()
        this.$refs.rightTableRef.handleResize()
      }
    },
    // 查询默认成本分析报价信息
    async getAnalysInfo() {
      const { rfxId, rfxItemId, priceRecordCode } = this.$route.query
      const params = {
        rfxId,
        rfxItemId,
        priceRecordCode
      }
      this.loading = true
      const res = await this.$API.costFactorLinkagePricingConfirm
        .queryCflpcCostAnalysisData(params)
        .catch(() => {
          this.loading = false
        })
      this.loading = false
      if (res.code === 200) {
        const {
          costModelId,
          costModelCode,
          costModelName,
          versionCode,
          stepValue,
          directDeliverAddr,
          counterName
        } = res.data?.current

        this.tempForm = {
          ...this.tempForm,
          costModelId,
          costModelCode,
          costModelName,
          versionCode,
          stepValue,
          directDeliverAddr,
          leftCounterName: counterName
        }
        this.serializeDataList('left', res.data?.current || {})
        this.serializeDataList('right', res.data?.history || {})
      }
    },
    // 处理列表数据
    serializeDataList(prefix, res = {}) {
      this[prefix + 'ExtraFormData'] = this.serializeFormData(res.extraList || [], 'extra')
      this[prefix + 'CommonFormData'] = this.serializeFormData(res?.formValueList || [], 'common')
      this[prefix + 'TableData'] = this.serializeItemList(res?.itemList || [])
      this.$nextTick(() => this[prefix + 'TableRef'].setAllTreeExpand(true))
    },
    // 导出
    async hanldeExport() {
      const { rfxId, rfxItemId, priceRecordCode } = this.$route.query
      const params = {
        rfxId,
        rfxItemId,
        priceRecordCode
      }
      const res = await this.$API.costFactorLinkagePricingConfirm.exportCflpcCostAnalysis(params)
      if (res.data) {
        this.$toast({ type: 'success', content: this.$t('正在导出，请稍后！') })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;

  .top-container {
    height: 80px;
    background-color: #fff;
    padding: 10px;
  }
  .body-container {
    display: flex;
    justify-content: space-between;
    height: calc(100% - 80px);
  }
}
.top-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 10px;
  height: 100%;
  background-color: rgba(99, 134, 193, 0.08);
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  box-shadow: 0 0 10px 0 hsla(0, 7%, 50%, 0.06);

  .left {
    .title {
      height: 28px;
      line-height: 28px;
      font-size: 20px;
      font-weight: 600;
      .sub-title {
        color: #9a9a9a;
        font-size: 16px;
        font-weight: 500;
      }
    }
    .info {
      display: flex;
      height: 22px;
      line-height: 22px;
      .item {
        margin-right: 20px;
      }
    }
  }
}
.container {
  display: flex;
  height: 100%;
  width: 100%;
  background-color: #fff;

  .title {
    height: 35px;
    line-height: 35px;
    font-weight: bold;
    color: #fff;
    background-color: #3c435e;
    i {
      margin: auto 8px;
    }
  }
  .body {
    padding: 10px 15px;
    .tip {
      line-height: 22px;
      color: red;
      font-weight: bold;
    }
  }

  .content {
    width: calc(100% - 20px);
    border: 1px solid #e8e8e8;
    margin-bottom: 10px;
    .form-info {
      margin-bottom: 5px;
      .mt-form {
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 4 - 10px));
        justify-content: space-between;
        grid-gap: 5px;
      }
    }
  }
  .operate {
    margin: auto;
    width: 15px;
    height: 80px;
    line-height: 80px;
    border: 1px solid #e8e8e8;
    color: #3c435e;
    font-weight: bold;
    text-align: center;
  }
  .left-operate {
    border-radius: 0 8px 8px 0;
    margin-right: 5px;
  }
  .right-operate {
    border-radius: 8px 0 0 8px;
    margin-left: 5px;
  }
}
.left-hidden {
  .container:first-child {
    width: 20px;
    overflow: hidden;
    .content {
      display: none;
    }
    .left-operate {
      transform: rotate(180deg);
      border-radius: 8px 0 0 8px;
    }
  }
  .container:last-child {
    .content {
      width: 100%;
    }
    .operate {
      display: none;
    }
  }
}
.right-hidden {
  .container:first-child {
    .content {
      width: 100%;
    }
    .operate {
      display: none;
    }
  }
  .container:last-child {
    width: 20px;
    overflow: hidden;
    .content {
      display: none;
    }
    .right-operate {
      transform: rotate(180deg);
      border-radius: 0 8px 8px 0;
    }
  }
}
::v-deep {
  .table-tool-bar {
    height: 0;
  }
  .mt-form-item {
    margin-bottom: 0;
  }
  .vxe-input {
    width: 100%;
    height: 28px;
    line-height: 28px;
  }
}
</style>
