<template>
  <div class="full-height">
    <div class="top-container toggle-container" :class="[!isExpand && 'top-hidden']">
      <div class="top-content">
        <div class="top-info">
          <div class="title">
            <div v-show="!isExpand">
              <span>{{ dataForm.rfxCode }}</span>
              <span class="sub-title">{{ dataForm.rfxName }}</span>
            </div>
          </div>
          <div class="top-btns">
            <vxe-button
              v-for="(item, index) in detailToolbar"
              v-show="!item.isHidden"
              :key="index"
              :icon="item.icon"
              :status="item.status"
              :disabled="item.disabled"
              size="small"
              @click="handleClickToolBar(item)"
              >{{ item.name }}</vxe-button
            >
          </div>
        </div>

        <div class="top-form">
          <mt-form ref="dataFormRef" :model="dataForm">
            <mt-form-item prop="rfxCode" :label="$t('定价单号')" label-style="top">
              <vxe-input v-model="dataForm.rfxCode" disabled />
            </mt-form-item>
            <mt-form-item prop="rfxName" :label="$t('定价单名称')" label-style="top">
              <vxe-input v-model="dataForm.rfxName" clearable disabled />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
              <vxe-input v-model="dataForm.company" clearable disabled />
            </mt-form-item>
            <mt-form-item prop="purchaseOrgCode" :label="$t('采购组织')">
              <vxe-input v-model="dataForm.purchaseOrg" clearable disabled />
            </mt-form-item>
            <mt-form-item prop="factoryCode" :label="$t('工厂')" label-style="top">
              <vxe-input v-model="dataForm.factory" clearable disabled />
            </mt-form-item>
            <mt-form-item prop="docTypeCode" :label="$t('定价单类型')" label-style="top">
              <vxe-select
                v-model="dataForm.docTypeCode"
                :options="orderTypeList"
                :option-props="{ label: 'text', value: 'value' }"
                disabled
              />
            </mt-form-item>
            <mt-form-item prop="statu" :label="$t('确认状态')" label-style="top">
              <vxe-select
                v-model="dataForm.status"
                :options="statusList"
                :option-props="{ label: 'text', value: 'value' }"
                disabled
              />
            </mt-form-item>
            <mt-form-item prop="publishTime" :label="$t('发布时间')" label-style="top">
              <vxe-input v-model="dataForm.publishTime" type="datetime" clearable disabled />
            </mt-form-item>
            <mt-form-item
              v-show="dataForm.status !== 0"
              prop="submitTime"
              :label="$t('确认时间')"
              label-style="top"
            >
              <vxe-input v-model="dataForm.submitTime" type="datetime" clearable disabled />
            </mt-form-item>
            <mt-form-item prop="endTime" :label="$t('确认截止时间')" label-style="top">
              <vxe-input v-model="dataForm.endTime" type="datetime" clearable disabled />
            </mt-form-item>
          </mt-form>
        </div>
      </div>
      <div class="top-toggle-btn">
        <div @click="handleToggle"><i class="vxe-icon-arrow-up" /></div>
      </div>
    </div>
    <div class="body-container">
      <mt-tabs
        ref="mtTabsRef"
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabList"
        :halt-select="false"
        @handleSelectTab="(index, item) => handleTabChange(index)"
        style="background-color: #fff"
      />
      <keep-alive>
        <component ref="mainContent" :is="activeComponent" :data-info="dataForm" />
      </keep-alive>
    </div>
  </div>
</template>

<script>
import {
  Input as VxeInput,
  Button as VxeButton,
  Select as VxeSelect,
  Textarea as VxeTextarea
} from 'vxe-table'
import mixin from './config/mixin'
import { orderTypeList, statusList } from './config/index'

export default {
  name: 'ExpertRating',
  components: {
    VxeInput,
    VxeButton,
    VxeSelect,
    VxeTextarea
  },
  mixins: [mixin],
  data() {
    return {
      type: 'detail',
      orderTypeList,
      statusList,
      dataForm: {},
      tabList: [
        { title: this.$t('成本因子') },
        { title: this.$t('物料明细') },
        { title: this.$t('附件') }
      ],
      activeTabIndex: 0,
      isExpand: false
    }
  },
  computed: {
    activeComponent() {
      let comp = ''
      switch (this.activeTabIndex) {
        case 0:
          // 成本因子
          comp = () => import('./components/costFactorTab.vue')
          break
        case 1:
          // 物料明细
          comp = () => import('./components/materialDetailTab.vue')
          break
        case 2:
          // 附件
          comp = () => import('./components/attachmentTab.vue')
          break
        default:
          return
      }
      return comp
    }
  },
  mounted() {
    this.getHeaderInfo()
  },
  methods: {
    // 获取头部基础信息
    async getHeaderInfo() {
      const res = await this.$API.costFactorLinkagePricingConfirm.queryCflpcHeaderInfo({
        id: this.$route.query.id
      })
      if (res.code === 200) {
        const {
          companyCode,
          companyName,
          purchaseOrgCode,
          purchaseOrgName,
          factoryCode,
          factoryName
        } = res.data
        this.dataForm = {
          ...res.data,
          company: companyCode + '-' + companyName,
          purchaseOrg: purchaseOrgCode + '-' + purchaseOrgName,
          factory: factoryCode + '-' + factoryName
        }
      }
    },
    // 点击工具栏按钮
    handleClickToolBar(e) {
      switch (e.code) {
        case 'back':
          this.handleBack()
          break
        case 'confirm':
        case 'reject':
          this.handleOperate(e.code)
          break
        case 'export':
          this.handleExport()
          break
        default:
          break
      }
    },
    // 切换tab页签
    handleTabChange(index) {
      this.$refs.mtTabsRef.activeTab = index
      this.activeTabIndex = index
    },
    // 返回
    handleBack() {
      this.$router.go(-1)
    },
    // 确认、驳回
    async handleOperate(type) {
      // 确认联动定价，必须上传报价附件
      if (type === 'confirm') {
        const supQuoteFileList = await this.getSupQuoteFileList()
        if (supQuoteFileList.length === 0) {
          this.$toast({ content: this.$t('报价附件不能为空！'), type: 'warning' })
          return
        }
      }
      const tipMap = {
        confirm: this.$t('通过'),
        reject: this.$t('驳回')
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确定${tipMap[type]}？`)
        },
        success: async () => {
          const params = {
            id: this.$route.query.id
          }
          const res = await this.$API.costFactorLinkagePricingConfirm[type + 'Cflpc'](params)
          if (res.code === 200) {
            this.getHeaderInfo()
            this.$toast({ content: this.$t('操作成功！'), type: 'success' })
          }
        }
      })
    },
    // 报价单导出
    async handleExport() {
      const params = {
        rfxId: this.$route.query.id,
        page: { current: 1, size: 10000 }
      }
      this.$store.commit('startLoading')
      const res = await this.$API.costFactorLinkagePricingConfirm.exportCflpc(params)
      if (res.data instanceof Blob) {
        const buffer = res.data
        if (buffer.type === 'application/json') {
          try {
            const json = JSON.parse(await buffer.text())
            if (json.msg) {
              this.$toast({ content: json.msg, type: 'warning' })
            }
          } catch (error) {
            console.warn(error)
          }
        } else if (buffer.type === 'application/pdf') {
          const url = URL.createObjectURL(buffer)
          window.open(url)
        }
      }
      this.$store.commit('endLoading')
    },
    // 展开/收缩
    handleToggle() {
      this.isExpand = !this.isExpand
    },
    // 获取报价附件列表
    async getSupQuoteFileList() {
      let supQuoteFileList = []
      // 获取报价附件节点信息
      const res = await this.$API.costFactorLinkagePricingConfirm.getFileNodeList({
        docId: this.dataForm.rfxId
      })
      if (res.code === 200) {
        const recursiveQuery = function (children) {
          for (let item of children) {
            if (item.nodeCode === 'sup_quote_file') {
              return item
            }
            if (Array.isArray(item.fileNodeResponseList) && item.fileNodeResponseList.length > 0) {
              return recursiveQuery(item.fileNodeResponseList)
            }
          }
        }
        const supQuoteFileNode = recursiveQuery(res.data)
        const params = {
          docId: this.dataForm.rfxId,
          parentId: supQuoteFileNode.id
        }
        const res2 = await this.$API.costFactorLinkagePricingConfirm.getFileList(params)
        if (res2.code === 200) {
          supQuoteFileList = res2.data || []
        }
      }
      return supQuoteFileList
    }
  }
}
</script>

<style scoped lang="scss">
.full-height {
  height: 100%;
  padding: 8px;
  background: #fff;
}

.top-container {
  width: 100%;
  .top-content {
    padding: 8px 8px 0 8px;
    background-color: #f9f9f9;
    border-radius: 8px;
    .top-info {
      display: flex;
      justify-content: space-between;
      .title {
        font-size: 20px;
        font-weight: 700;
        color: #292929;
        span {
          margin-right: 15px;
        }
        .sub-title {
          font-size: 15px;
          color: #777;
        }
      }
      .top-btns {
        text-align: right;
        margin-bottom: 10px;
      }
    }
    .top-form {
      .mt-form {
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
        justify-content: space-between;
        grid-gap: 5px;
      }
      .vxe-input,
      .vxe-select {
        width: 100%;
        height: 32px;
        line-height: 32px;
      }
    }
  }
  .top-toggle-btn {
    width: 100%;
    div {
      width: 80px;
      height: 15px;
      line-height: 15px;
      margin: auto;
      text-align: center;
      background-color: #4a556b;
      color: #fff;
      font-weight: bold;
      border-radius: 0 0 6px 6px;
    }
  }
}

.top-hidden {
  .top-form {
    display: none;
  }
  .top-toggle-btn {
    div {
      transform: rotate(180deg);
      border-radius: 6px 6px 0 0;
    }
  }
}

::v-deep {
  .mt-form-item {
    margin-bottom: 15px;
    .label {
      margin: 0 10px 5px 0;
    }
  }
  .mt-tabs-container {
    background-color: #fff;
  }

  .mt-tabs-container ul.tab-container li.tab-item2.active {
    border-bottom: 2px solid #409eff;
  }
  .mt-tabs-container ul.tab-container li.tab-item2.active,
  .mt-tabs-container ul.tab-container li.tab-item2:hover {
    color: #31374e;
  }
}
</style>
