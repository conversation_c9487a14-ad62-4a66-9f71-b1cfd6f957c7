import { statusList } from './index'

export default {
  data() {
    return {
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 20,
        pageSizes: [20, 50, 200, 500, 1000, 2000]
      },
      pageInfo: {
        size: 20,
        current: 1
      },
      companyList: [],
      purchaseOrgList: [],
      factoryList: [],
      sourcingExpandList: []
    }
  },
  computed: {
    detailToolbar() {
      const toolbar = [
        {
          code: 'confirm',
          name: this.$t('确认'),
          status: '',
          isHidden: this.dataForm.status !== 0
        },
        {
          code: 'reject',
          name: this.$t('驳回'),
          status: '',
          isHidden: this.dataForm.status !== 0
        },
        {
          code: 'export',
          name: this.$t('报价单导出'),
          status: ''
        },
        {
          code: 'back',
          name: this.$t('返回'),
          status: 'primary'
        }
      ]
      return toolbar
    },
    columns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'rfxCode',
          title: this.$t('定价单号'),
          minWidth: 140,
          slots: {
            default: ({ row, column }) => {
              return [<a on-click={() => this.handleClickCellTitle(row, column)}>{row.rfxCode}</a>]
            }
          }
        },
        {
          field: 'rfxName',
          title: this.$t('定价单名称'),
          minWidth: 140
        },
        {
          field: 'docTypeName',
          title: this.$t('定价类型'),
          minWidth: 120
        },
        {
          field: 'status',
          title: this.$t('确认状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = statusList.find((item) => item.value === row.status)
              const statusName = selectItem?.text || ''
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'companyCode',
          title: this.$t('公司'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [<span>{row.companyCode + '-' + row.companyName}</span>]
            }
          }
        },
        {
          field: 'publishTime',
          title: this.$t('发布时间'),
          minWidth: 140
        },
        {
          field: 'submitTime',
          title: this.$t('确认时间'),
          minWidth: 140
        },
        {
          field: 'endTime',
          title: this.$t('确认截止时间'),
          minWidth: 140
        }
      ]
    }
  },
  mounted() {},
  methods: {
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.handleSearch('pageChange')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.handleSearch()
    }
  }
}
