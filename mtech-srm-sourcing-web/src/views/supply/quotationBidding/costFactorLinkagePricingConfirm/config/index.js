import { i18n } from '@/main'

// 状态列表
export const statusList = [
  { value: 0, text: i18n.t('待确认') },
  { value: 1, text: i18n.t('已确认') },
  { value: 2, text: i18n.t('已驳回') }
]
// 价格分类列表
export const priceClassifyList = [
  { value: '1', text: i18n.t('基价') },
  { value: '2', text: i18n.t('SRM价格') },
  { value: '3', text: i18n.t('暂估价格') },
  { value: '4', text: i18n.t('执行价格') }
]
// 定价单类型
export const orderTypeList = [{ value: '0', text: i18n.t('因子联动部品询报价') }]
// 单据来源
export const sourceList = [{ value: '0', text: i18n.t('手动创建') }]

// 查询列表-操作按钮
export const listToolbar = [
  { code: 'add', name: i18n.t('新增'), status: 'info' },
  { code: 'delete', name: i18n.t('删除'), status: 'info' },
  { code: 'submit', name: i18n.t('提交'), status: 'info' }
]
