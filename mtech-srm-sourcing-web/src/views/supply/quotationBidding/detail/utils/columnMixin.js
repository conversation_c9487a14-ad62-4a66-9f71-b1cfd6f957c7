import Vue from 'vue'
import { columnData as logisticsColumnData } from '@/views/common/columnData/rfxItemLogistics'
import { columnData as biddingItemLogisticsColumnData } from '@/views/common/columnData/biddingItemLogistics'
import { getFields as getSBIFields } from '@/views/common/columnData/supplierBiddingItem'
import { columnData as itemExtMapColumnData } from '@/views/common/columnData/itemExtMap'
import { columnData as itemDieMapColumnData } from '@/views/common/columnData/biddingItemDie'
import { createEditInstance } from '@/utils/ej/dataGrid/index'
// import { mergeDataSource } from "@/utils/ej/dataGrid/utils";
import cellFileView from 'COMPONENTS/NormalEdit/cellFileView' // 单元格附件查看
import cellFileViewSupply from 'COMPONENTS/NormalEdit/cellFileViewSupply' // 单元格上传
import cellUploadSupply from 'COMPONENTS/NormalEdit/cellUploadSupply' // 单元格上传
import inputSelectBrand from 'COMPONENTS/NormalEdit/inputSelectBrand'

import { Decimal32 } from '../utils/utils'
import { PRICE_FRACTION_DIGITS } from '@/constants/editConfig'
import {
  inputCurrency,
  inputPrice,
  inputCompleteVehiclePrice,
  inputSeaTransportFeeTotal,
  inputAirTransport,
  inputBidTaxRate,
  inputQuote
} from './gridInputHandlers'
import DateTimePicker from '@/components/DateTimePicker'
import { useEdit } from './column'
import * as combination from './combination'
// 子表自读字段
const childGridReadonlyFields = [
  // 单价（未税）
  // "mt_supplier_bidding_item.untaxedUnitPrice",
  // 单价（含税）
  // "mt_supplier_bidding_item.taxedUnitPrice",

  // // 规划分摊价（未税）
  // "mt_supplier_bidding_item.planSharePriceUntaxed",
  // // 规划分摊价（含税）
  // "mt_supplier_bidding_item.planSharePriceTaxed",

  // // 实际分摊价（未税）
  // "mt_supplier_bidding_item.realSharePriceUntaxed",
  // // 实际分摊价（含税）
  // "mt_supplier_bidding_item.realSharePriceTaxed",

  //上次规划分摊价（未税）
  'mt_supplier_bidding_item.lastPlanSharePriceUntaxed',
  //上次规划分摊价（含税）
  'mt_supplier_bidding_item.lastPlanSharePriceTaxed',

  //上次实际分摊价（未税）
  'mt_supplier_bidding_item.lastRealSharePriceUntaxed',
  //上次实际分摊价（含税）
  'mt_supplier_bidding_item.lastRealSharePriceTaxed',

  // 上次分摊后单价（未税）
  'mt_supplier_bidding_item.lastSharePriceUntaxed',
  // 上次分摊后单价（含税）
  'mt_supplier_bidding_item.lastSharePriceTaxed',

  // 上次分摊费用（未税)
  'mt_supplier_bidding_item.lastShareCostUntaxed',
  // 上次分摊费用（含税）
  'mt_supplier_bidding_item.lastShareCostTaxed',

  //分摊后单价（未税）
  'mt_supplier_bidding_item.sharePriceUntaxed',
  //分摊后单价（含税）
  'mt_supplier_bidding_item.sharePriceTaxed',

  //历史价格
  'mt_supplier_bidding_item.bidHistoryPrice',
  //汇总价格
  'mt_supplier_bidding_item.totalPrice'
]

export default {
  data() {
    return {
      dictItems: [], // dict-item 接口统一维护字段
      currencyList: [], // 货币名称
      taxList: [],
      purUnitList: [], // 采购单位
      parentRequired: [],
      childRequired: []
    }
  },
  methods: {
    async initTaxList() {
      const res = await this.$API.pur
        .getRates({
          rfxId: this.rfxId
        })
        .catch(() => {})
      if (res) {
        this.taxList = res.data
      }
    },
    // 币种编码
    async initCurrencyList() {
      const res = await this.$API.pur
        .getCurrencys({
          rfxId: this.rfxId
        })
        .catch(() => {})
      if (res) {
        this.currencyList = res.data
      }
    },
    // 采购单位
    async initPurUnitList() {
      const res = await this.$API.pur
        .getPurUnits({
          rfxId: this.rfxId
        })
        .catch(() => {})
      if (res) {
        this.purUnitList = res.data
      }
    },
    // 初始化字典数据
    async initDictItems() {
      const tasks = [
        'TradeClause', // 贸易条款 tradeClauseNameData
        'DELIVERY_PLACE', // 直送地 deliveryPlaceData
        'TransportMode', // 物流方式 shippingMethodNameData
        'START-PORT', // 起始港
        'DESTINATION-PORT' // 目的港
      ].map((dictCode) => this.$API.masterData.dictionaryGetList({ dictCode }))
      const result = await Promise.all(tasks).catch((err) => {
        console.error(err)
      })
      if (!result) {
        return
      }
      this.dictItems = result.map((e) => e.data).flat()
    },
    onGridInput(...args) {
      // 供方税率选择
      inputBidTaxRate(...args)
      // 币种编码
      inputCurrency(...args)
      inputQuote(...args)
      if (this.quotedPriceData && combination.isCombination(this.quotedPriceData)) {
        // ckd 跳过
        combination.inputPrice.call(this, ...args)
      } else {
        // 价格计算
        inputPrice(...args)
        // 海运
        inputSeaTransportFeeTotal(...args)
        // 陆运总费计算
        inputCompleteVehiclePrice(...args)
        // 航空
        inputAirTransport(this, ...args)
      }

      const [ctx, { field, rowData }] = args
      const arr = [
        'biddingItemDTO.transportCost',
        'biddingItemDTO.processCost',
        'biddingItemDTO.processPartyMaterialCost',
        'biddingItemDTO.bidTaxRateValue'
      ]
      if (field === 'biddingItemDTO.bidPurUnitName') {
        // 同步更新转换率
        if (Array.isArray(this.fieldDefines)) {
          // 存在配置
          const hasPurUnitCode = this.fieldDefines.find(({ field }) => {
            return ['biddingItemDTO.bidPurUnitCode', 'purUnitCode'].includes(field)
          })
          const hasUnitCode = this.fieldDefines.find(({ field }) => {
            return ['unitCode'].includes(field)
          })
          const hasBidConversionRate = this.fieldDefines.find(({ field }) => {
            return ['biddingItemDTO.bidConversionRate'].includes(field)
          })
          if (hasPurUnitCode && hasUnitCode && hasBidConversionRate) {
            // 存在字段
            const purUnitCode =
              ctx.getValueByField('biddingItemDTO.bidPurUnitCode') ||
              ctx.getValueByField('purUnitCode')
            const unitCode = ctx.getValueByField('unitCode')
            if (purUnitCode === unitCode) {
              // 符合更新条件
              ctx.setValueByField('biddingItemDTO.bidConversionRate', 1)
            }
          }
        }
      } else if (
        field === 'biddingItemDTO.minPurQuantity' ||
        field === 'biddingItemDTO.minPackageQuantity'
      ) {
        if (
          ctx.rowData.biddingItemDTO.minPurQuantity != undefined &&
          ctx.rowData.biddingItemDTO.minPurQuantity != null &&
          ctx.rowData.biddingItemDTO.minPurQuantity != '' &&
          ctx.rowData.biddingItemDTO.minPackageQuantity != undefined &&
          ctx.rowData.biddingItemDTO.minPackageQuantity != null &&
          ctx.rowData.biddingItemDTO.minPackageQuantity != ''
        ) {
          setTimeout(() => {
            let adviseMinPurQuantity = ctx.rowData.biddingItemDTO.minPurQuantity
            let adviseMinPackageQuantity = ctx.rowData.biddingItemDTO.minPackageQuantity
            let number = adviseMinPurQuantity / adviseMinPackageQuantity
            if (Math.floor(number) !== number) {
              this.$bus.$emit(`contentDialog`)
              console.log('input')
            }
          }, 1500)
        }
      } else if (field === 'biddingItemDTO.bidCurrencyCode') {
        if (['JPY', 'VND'].includes(ctx.rowData.biddingItemDTO.bidCurrencyCode)) {
          ctx.setValueByField('biddingItemDTO.untaxedUnitPrice', 0)
          ctx.setOptions('biddingItemDTO.untaxedUnitPrice', {
            precision: '0'
          })
          ctx.setOptions('biddingItemDTO.taxedUnitPrice', {
            precision: '0'
          })
        }
      } else if (
        this.quotedPriceData.sourcingObjType === 'module_out_going' &&
        arr.includes(field)
      ) {
        // 整机模组外发，单价未税=运费+加工费+加工费材料费
        const { transportCost, processCost, processPartyMaterialCost, bidTaxRateValue } =
          rowData.biddingItemDTO
        // 单价（未税）
        let untaxedUnitPrice = transportCost + processCost + processPartyMaterialCost
        // 单价（含税）
        let taxedUnitPrice = 0
        const rate = new Decimal32(bidTaxRateValue || 0).add(1).toString()
        if (bidTaxRateValue >= 0) {
          taxedUnitPrice = new Decimal32(untaxedUnitPrice)
            .mul(rate)
            .toFixed(this.ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS)
        }
        ctx.setValueByField('biddingItemDTO.untaxedUnitPrice', untaxedUnitPrice)
        ctx.setValueByField('biddingItemDTO.taxedUnitPrice', taxedUnitPrice)
      }
      // 潜在供应商 一行同步多行（初次进来都没值时候）
      let _fixField = [
        // 合格供应商禁用编辑字段
        'biddingItemDTO.bidCurrencyCode',
        'biddingItemDTO.bidCurrencyName',
        'biddingItemDTO.quoteAttribute',
        'biddingItemDTO.quoteMode',
        'biddingItemDTO.bidTaxRateCode',
        'biddingItemDTO.bidTaxRateName',
        'biddingItemDTO.bidTaxRateValue'
      ]
      if (
        ['potential_qualified', 'category_potential'].includes(
          this.quotedPriceData.supplierRange
        ) &&
        _fixField.includes(field)
      ) {
        // 同步其它行数据
        this.$bus.$emit(`syncOtherLine`, { rowData, field })
      }
    },
    onGridChange(...args) {
      const [ctx, { field }, event] = args
      if (field === 'biddingItemDTO.bidPurUnitName') {
        if (event.itemData) {
          ctx.setValueByField('biddingItemDTO.bidPurUnitCode', event.itemData.unitCode)
        } else {
          ctx.setValueByField('biddingItemDTO.bidPurUnitCode', '')
        }
      } else if (
        field === 'biddingItemDTO.minPurQuantity' ||
        field === 'biddingItemDTO.minPackageQuantity'
      ) {
        if (
          ctx.rowData.biddingItemDTO.minPurQuantity != undefined &&
          ctx.rowData.biddingItemDTO.minPurQuantity != null &&
          ctx.rowData.biddingItemDTO.minPurQuantity != '' &&
          ctx.rowData.biddingItemDTO.minPackageQuantity != undefined &&
          ctx.rowData.biddingItemDTO.minPackageQuantity != null &&
          ctx.rowData.biddingItemDTO.minPackageQuantity != ''
        ) {
          let adviseMinPurQuantity = ctx.rowData.biddingItemDTO.minPurQuantity
          let adviseMinPackageQuantity = ctx.rowData.biddingItemDTO.minPackageQuantity
          let number = adviseMinPurQuantity / adviseMinPackageQuantity
          if (Math.floor(number) !== number) {
            this.$bus.$emit(`contentDialog`)
            console.log('change')
          }
        }
      }
    },
    defineGridColumns(fieldDefines, isChild = false) {
      let _requiredFields = []
      if (!Array.isArray(fieldDefines) || fieldDefines.length < 1) {
        return []
      }
      let _columnData = []
      //渲染表头数组
      _columnData = [
        // {
        //   type: "checkbox",
        //   width: "50",
        //   showInColumnChooser: false,
        //   allowEditing: false,
        // },
      ]
      const rfxItemLogisticsColumn = logisticsColumnData({
        prefix: 'rfxItemLogisticsDTO.',
        dictItems: this.dictItems,
        handleSelectChange: () => {}
      })
      const biddingItemLogisticsColumn = biddingItemLogisticsColumnData({
        prefix: 'biddingItemLogisticsDTO.',
        dictItems: this.dictItems,
        currencyList: this.currencyList,
        handleSelectChange: () => {}
      })
      // 生成编辑器实例并注册 input 和 change 事件
      const editInstance = createEditInstance()
        .component('datetime', DateTimePicker)
        .component('inputSelectBrand', inputSelectBrand)
        .onInput(this.onGridInput)
        .onChange(this.onGridChange)
      const sBIFields = getSBIFields({
        currencyList: this.currencyList,
        taxList: this.taxList,
        purUnitList: this.purUnitList,
        editInstance,
        quotedPriceData: this.quotedPriceData
      })
      if (sBIFields.bidPurUnitName?.editConfig) {
        const dataSource = this.purUnitList
        sBIFields.bidPurUnitName.editConfig = {
          type: 'select',
          valueConvert: (val, { options, column }) => {
            const dataSource = options.dataSource || []
            const row = dataSource.find((e) => e.id === val || e.unitName === val)
            const purUnitName = row?.unitName || ''
            editInstance.setValueByField(column.field, purUnitName)
            return purUnitName
          },
          props: {
            ...sBIFields.bidPurUnitName.editConfig.props,
            fields: { value: 'id', text: '__text' },
            created: () => {
              let row
              const bidPurUnitCode = editInstance.getValueByField('biddingItemDTO.bidPurUnitCode')
              const bidPurUnitName = editInstance.getValueByField('biddingItemDTO.bidPurUnitName')
              if (bidPurUnitCode) {
                row = dataSource.find((e) => e.unitCode === bidPurUnitCode)
              } else if (bidPurUnitName) {
                row = dataSource.find((e) => e.unitName === bidPurUnitName)
              }
              if (row) {
                setTimeout(() => {
                  editInstance.setValueByField('biddingItemDTO.bidPurUnitName', row.id)
                }, 0)
              }
            }
          }
        }
      }

      const itemExtMapColumn = itemExtMapColumnData({
        prefix: 'itemExtMap.',
        dictItems: this.dictItems,
        handleSelectChange: () => {},
        moduleType: 'supplier'
      })

      const itemDieMapColumn = itemDieMapColumnData({
        prefix: 'rfxItemDieDTO.',
        handleSelectChange: () => {}
      })

      const tbNameMap = {
        mt_supplier_bidding_item: {
          key: 'biddingItemDTO'
        },
        mt_supplier_rfx_item_ext: {
          key: 'itemExtMap'
        },
        mt_supplier_rfx_item_logistics: {
          key: 'rfxItemLogisticsDTO'
        },
        mt_supplier_bidding_item_logistics: {
          key: 'biddingItemLogisticsDTO'
        },
        mt_supplier_rfx_item_die: {
          key: 'rfxItemDieDTO'
        }
      }
      // 隐藏的字段
      const filterFields = (field) => {
        const isShow = ![
          'mt_supplier_rfx_item.currencyCode', // 币种
          'mt_supplier_rfx_item.currencyName',
          'mt_supplier_rfx_item.taxRateCode', // 税率
          'mt_supplier_rfx_item.taxRateName',
          'mt_supplier_rfx_item.taxRateValue',
          'mt_supplier_rfx_item.purUnitCode', // 采购单位
          'mt_supplier_rfx_item.purUnitName',
          'mt_supplier_rfx_item.conversionRate' // 转换率
        ].includes(`${field.tableName}.${field.fieldCode}`)
        !isShow && console.log('隐藏字段', field)
        return isShow
      }
      // 动态字段接口中的字段属性tableName要匹配得上才能push进submitField数组继而才能保存和提交到后台
      fieldDefines.filter(filterFields).forEach((item) => {
        let name = ''
        //submitStatus == 1 不能编辑  == 0 可以编辑

        if (this.quotedPriceData.submitStatus == 1) {
          // console.log("可以编辑的item", item);
          //判断可以编辑
          if (item.tableName == 'mt_supplier_bidding_item') {
            // 如果是必填 0-非必填；1-必填；2-无需配置
            if (item.required == 1) {
              //必填数组
              _requiredFields.push(item)
              //设置必填-表头
              item.headerTemplate = () => {
                return {
                  template: Vue.component('requiredCell', {
                    template: `
                    <div class="headers">
                      <span style="color: red">*</span>
                      <span class="e-headertext">{{fieldName}}</span>
                    </div>
                  `,
                    data() {
                      return {
                        data: {},
                        fieldName: ''
                      }
                    },
                    mounted() {
                      this.fieldName = item.fieldName
                    }
                  })
                }
              }
            }
            name = tbNameMap[item.tableName].key
            let headerText = item.fieldName
            if (
              // isChild &&
              !combination.isCombination(this.quotedPriceData) &&
              childGridReadonlyFields.includes(`${item.tableName}.${item.fieldCode}`.trim())
            ) {
              _columnData.push({
                field: `${name}.${item.fieldCode}`,
                headerText: item.fieldName,
                headerTemplate: item?.headerTemplate,
                queryType: 'string',
                allowEditing: false,
                tableName: item.tableName
              })
            } else {
              _columnData.push(
                useEdit({
                  row: {
                    field: `${name}.${item.fieldCode}`,
                    headerText,
                    headerTemplate: item?.headerTemplate,
                    queryType: 'string',
                    allowEditing: true,
                    tableName: 'mt_supplier_bidding_item'
                  },
                  editInstance,
                  config: sBIFields?.[item.fieldCode],
                  disabled: true
                })
              )
            }

            //需要维护的数组
            this.submitField.push(item)
          } else if (item.tableName == 'mt_supplier_rfx_item_ext') {
            name = tbNameMap[item.tableName].key
            const field = `${name}.${item.fieldCode}`
            const defColumn = itemExtMapColumn.find((e) => e.field === field)

            // _columnData.push({
            //   field,
            //   headerText: item.fieldName,
            //   headerTemplate: item?.headerTemplate,
            //   editConfig: defColumn?.editConfig,
            //   formatter: defColumn?.formatter,
            //   queryType: "string",
            //   allowEditing: false,
            //   tableName: item.tableName,
            // });
            _columnData.push(
              useEdit({
                row: {
                  field: `${name}.${item.fieldCode}`,
                  headerText: item.fieldName,
                  headerTemplate: item?.headerTemplate,
                  queryType: 'string',
                  allowEditing: false,
                  tableName: item.tableName
                },
                editInstance,
                config: {
                  formatter: defColumn?.formatter,
                  editConfig: {
                    type: defColumn?.editConfig?.type,
                    props: { disabled: true, ...defColumn?.editConfig }
                  }
                }
              })
            )
          } else if (item.tableName == 'mt_supplier_rfx_item_die') {
            name = tbNameMap[item.tableName].key
            const field = `${name}.${item.fieldCode}`

            const defColumn = itemDieMapColumn.find((e) => e.field === field)
            _columnData.push(
              useEdit({
                row: {
                  field: `${name}.${item.fieldCode}`,
                  headerText: item.fieldName,
                  headerTemplate: item?.headerTemplate,
                  queryType: 'string',
                  allowEditing: false,
                  tableName: item.tableName
                },
                editInstance,
                config: {
                  formatter: defColumn?.formatter,
                  editConfig: {
                    type: defColumn?.editConfig?.type,
                    props: { disabled: true, ...defColumn?.editConfig }
                  }
                }
              })
            )
          } else if (item.tableName === 'mt_supplier_rfx_item_logistics') {
            // 物流信息
            name = tbNameMap[item.tableName].key
            const field = `${name}.${item.fieldCode}`
            const defColumn = rfxItemLogisticsColumn.find((e) => e.field === field)
            if (!defColumn) {
              console.warn('没有定义的列:' + field)
            }
            _columnData.push({
              field,
              headerText: item.fieldName,
              headerTemplate: item?.headerTemplate,
              editConfig: defColumn?.editConfig,
              formatter: defColumn?.formatter,
              queryType: 'string',
              allowEditing: false,
              tableName: item.tableName
            })
          } else if (item.tableName === 'mt_supplier_bidding_item_logistics') {
            // 物流信息 可编辑
            name = tbNameMap[item.tableName].key
            const field = `${name}.${item.fieldCode}`
            const defColumn = biddingItemLogisticsColumn.find((e) => e.field === field)
            if (!defColumn) {
              console.warn('没有定义的列:' + field)
            }
            _columnData.push({
              field,
              headerText: item.fieldName,
              headerTemplate: item?.headerTemplate,
              editConfig: defColumn?.editConfig,
              formatter: defColumn?.formatter,
              queryType: 'string',
              allowEditing: false,
              tableName: item.tableName,
              edit: defColumn?.editConfig && editInstance.create()
            })
          } else if (item.tableName === 'rfx_file') {
            _columnData.push({
              field: item.fieldCode,
              headerText: item.fieldName,
              headerTemplate: item?.headerTemplate,
              template: function () {
                return {
                  template: item.fieldCode === 'supplierDrawing' ? cellFileViewSupply : cellFileView
                }
              },
              editTemplate: function () {
                return {
                  template: item.fieldCode === 'supplierDrawing' ? cellUploadSupply : cellFileView
                }
              },
              width: 150,
              allowEditing: false
            })
            if (item.fieldCode === 'supplierDrawing') {
              this.submitField.push(item)
            }
          } else {
            if (item.fieldCode == 'selfPurchasing') {
              _columnData.push({
                field: item.fieldCode,
                headerText: item.fieldName,
                headerTemplate: item?.headerTemplate,
                allowEditing: false,
                valueConverter: {
                  type: 'map',
                  map: { 0: this.$t('否'), 1: this.$t('是') }
                },
                edit: editInstance.create({
                  getEditConfig: () => ({
                    type: 'select',
                    'show-clear-button': true,
                    fields: { value: 'value', text: 'text' },
                    dataSource: [
                      { text: this.$t('是'), value: 1 },
                      { text: this.$t('否'), value: 0 }
                    ],
                    readonly: true,
                    disabled: true
                  })
                })
              })
            } else {
              _columnData.push({
                field: item.fieldCode,
                headerText: item.fieldName,
                headerTemplate: item?.headerTemplate,
                queryType: 'string',
                allowEditing: false
              })
            }
          }
        }
        if (this.quotedPriceData.submitStatus == 0) {
          //可以编辑
          if (item.tableName == 'mt_supplier_bidding_item') {
            name = tbNameMap[item.tableName].key
            // 如果是必填 0-非必填；1-必填；2-无需配置
            if (item.required == 1) {
              //必填数组
              _requiredFields.push(item)
              //设置必填-表头
              item.headerTemplate = () => {
                return {
                  template: Vue.component('requiredCell', {
                    template: `
                    <div class="headers">
                      <span style="color: red">*</span>
                      <span class="e-headertext">{{fieldName}}</span>
                    </div>
                  `,
                    data() {
                      return {
                        data: {},
                        fieldName: ''
                      }
                    },
                    mounted() {
                      this.fieldName = item.fieldName
                    }
                  })
                }
              }
            }
            let headerText = item.fieldName
            if (
              // isChild &&
              !combination.isCombination(this.quotedPriceData) &&
              childGridReadonlyFields.includes(`${item.tableName}.${item.fieldCode}`.trim())
            ) {
              _columnData.push({
                field: `${name}.${item.fieldCode}`,
                headerText: item.fieldName,
                headerTemplate: item?.headerTemplate,
                queryType: 'string',
                allowEditing: false,
                tableName: item.tableName
              })
            } else {
              _columnData.push(
                useEdit({
                  row: {
                    field: `${name}.${item.fieldCode}`,
                    headerText,
                    headerTemplate: item?.headerTemplate,
                    queryType: 'string',
                    allowEditing: true,
                    tableName: 'mt_supplier_bidding_item'
                  },
                  editInstance,
                  config: sBIFields?.[item.fieldCode]
                })
              )
            }
            //需要维护的数组
            this.submitField.push(item)
          } else if (item.tableName == 'mt_supplier_rfx_item_ext') {
            name = tbNameMap[item.tableName].key
            const field = `${name}.${item.fieldCode}`

            const defColumn = itemExtMapColumn.find((e) => e.field === field)
            // _columnData.push({
            //   field,
            //   headerText: item.fieldName,
            //   headerTemplate: item?.headerTemplate,
            //   editConfig: defColumn?.editConfig,
            //   formatter: defColumn?.formatter,
            //   queryType: "string",
            //   allowEditing: false,
            //   tableName: item.tableName,
            // });
            _columnData.push(
              useEdit({
                row: {
                  field: `${name}.${item.fieldCode}`,
                  headerText: item.fieldName,
                  headerTemplate: item?.headerTemplate,
                  queryType: 'string',
                  allowEditing: false,
                  tableName: item.tableName
                },
                editInstance,
                config: {
                  formatter: defColumn?.formatter,
                  editConfig: {
                    type: defColumn?.editConfig?.type,
                    props: { disabled: true, ...defColumn?.editConfig }
                  }
                }
              })
            )
          } else if (item.tableName == 'mt_supplier_rfx_item_die') {
            name = tbNameMap[item.tableName].key
            const field = `${name}.${item.fieldCode}`
            const defColumn = itemDieMapColumn.find((e) => e.field === field)
            _columnData.push(
              useEdit({
                row: {
                  field: `${name}.${item.fieldCode}`,
                  headerText: item.fieldName,
                  headerTemplate: item?.headerTemplate,
                  queryType: 'string',
                  allowEditing: false,
                  tableName: item.tableName
                },
                editInstance,
                config: {
                  formatter: defColumn?.formatter,
                  editConfig: {
                    type: defColumn?.editConfig?.type,
                    props: { disabled: true, ...defColumn?.editConfig }
                  }
                }
              })
            )
          } else if (item.tableName === 'mt_supplier_rfx_item_logistics') {
            // 物流信息
            name = tbNameMap[item.tableName].key
            const defColumn = rfxItemLogisticsColumn.find(
              (e) => e.field === `${name}.${item.fieldCode}`
            )
            _columnData.push({
              field: `${name}.${item.fieldCode}`,
              headerText: item.fieldName,
              headerTemplate: item?.headerTemplate,
              editConfig: defColumn?.editConfig,
              formatter: defColumn?.formatter,
              queryType: 'string',
              allowEditing: false,
              tableName: item.tableName
            })
          } else if (item.tableName === 'mt_supplier_bidding_item_logistics') {
            name = tbNameMap[item.tableName].key
            const field = `${name}.${item.fieldCode}`
            const defColumn = biddingItemLogisticsColumn.find((e) => e.field === field)
            _columnData.push({
              field,
              headerText: item.fieldName,
              headerTemplate: item?.headerTemplate,
              editConfig: defColumn?.editConfig,
              formatter: defColumn?.formatter,
              queryType: 'string',
              allowEditing: false,
              tableName: item.tableName,
              edit: defColumn?.editConfig && editInstance.create()
            })
          } else if (item.tableName === 'rfx_file') {
            _columnData.push({
              field: item.fieldCode,
              headerText: item.fieldName,
              headerTemplate: item?.headerTemplate,
              template: function () {
                return {
                  template: item.fieldCode === 'supplierDrawing' ? cellFileViewSupply : cellFileView
                }
              },
              editTemplate: function () {
                return {
                  template: item.fieldCode === 'supplierDrawing' ? cellUploadSupply : cellFileView
                }
              },
              width: 150,
              allowEditing: false
            })
            if (item.fieldCode === 'supplierDrawing') {
              this.submitField.push(item)
            }
          } else {
            if (item.fieldCode == 'selfPurchasing') {
              _columnData.push({
                field: item.fieldCode,
                headerText: item.fieldName,
                headerTemplate: item?.headerTemplate,
                allowEditing: false,
                valueConverter: {
                  type: 'map',
                  map: { 0: this.$t('否'), 1: this.$t('是') }
                },
                edit: editInstance.create({
                  getEditConfig: () => ({
                    type: 'select',
                    'show-clear-button': true,
                    fields: { value: 'value', text: 'text' },
                    dataSource: [
                      { text: this.$t('是'), value: 1 },
                      { text: this.$t('否'), value: 0 }
                    ],
                    readonly: true,
                    disabled: true
                  })
                })
              })
            } else {
              _columnData.push({
                field: item.fieldCode,
                headerText: item.fieldName,
                headerTemplate: item?.headerTemplate,
                queryType: 'string',
                allowEditing: false
              })
            }
          }
        }
      })
      if (isChild) {
        this.childRequired = _requiredFields
      } else {
        this.parentRequired = _requiredFields
      }
      return combination.defineGridColumnsAfter.call(this, _columnData, isChild)
    },
    //供方，列字段复杂，暂时未整理到‘采购明细Tab’页
    defineModuleColumns(item) {
      item.parentColumns = []
      item.childColumns = []
      if (Array.isArray(item?.parentFields) && item?.parentFields?.length) {
        item.parentColumns = this.defineGridColumns(item.parentFields)
      }
      if (Array.isArray(item?.childFields) && item?.childFields?.length) {
        item.childColumns = this.defineGridColumns(item.childFields, true)
      }
    }
  }
}
