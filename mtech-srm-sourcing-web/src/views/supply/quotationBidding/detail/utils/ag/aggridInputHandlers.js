import Vue from 'vue'
import { isNullOrUndefined } from '@/utils/is'
import { PRICE_FRACTION_DIGITS } from '@/constants/editConfig'
import { Decimal32, isSafeNumber, microTask } from '../utils'
const fieldMap = {
  'biddingItemDTO.bidTaxRateName': 'taxItemName',
  'biddingItemDTO.bidPurUnitName': 'unitName',
  'biddingItemDTO.bidCurrencyName': 'currencyName',
  'biddingItemDTO.shipModeName': 'dictName',
  'biddingItemDTO.supplyTypeName': 'dictName'
}
export const getOptions = (params) => {
  const field = params.column.colId
  const value = params.value
  const config =
    params.colDef?.cellEditorParams?.editConfig?.props ||
    params.colDef?.cellEditorParams?.editConfig
  const dataSource = config?.dataSource || []
  const findField = fieldMap[field]
  let row = null
  if (dataSource?.length) {
    row = dataSource.find((e) => e[findField] === value)
  }
  return {
    field,
    value,
    row
  }
}
/**
 * 币种名称 => 币种编码联动编辑（暂不设置双向联动）
 */
export const inputCurrency = (params) => {
  const currencyMap = {
    // 总计费用币种编码
    // 'biddingItemLogisticsDTO.totalFeeCurrencyCode': {
    //   fieldsValue: 'currencyCode',
    //   link: {
    //     field: 'biddingItemLogisticsDTO.totalFeeCurrencyName',
    //     fieldsValue: 'currencyName'
    //   }
    // },
    'biddingItemLogisticsDTO.totalFeeCurrencyName': {
      fieldsValue: 'currencyName',
      link: {
        field: 'biddingItemLogisticsDTO.totalFeeCurrencyCode',
        fieldsValue: 'currencyCode'
      }
    },
    // 航空运费币种编码
    // 'biddingItemLogisticsDTO.airFeeCurrencyCode': {
    //   fieldsValue: 'currencyCode',
    //   link: {
    //     field: 'biddingItemLogisticsDTO.airFeeCurrencyName',
    //     fieldsValue: 'currencyName'
    //   }
    // },
    'biddingItemLogisticsDTO.airFeeCurrencyName': {
      fieldsValue: 'currencyName',
      link: {
        field: 'biddingItemLogisticsDTO.airFeeCurrencyCode',
        fieldsValue: 'currencyCode'
      }
    },

    // 运费币种编码
    // 'biddingItemLogisticsDTO.transportFeeCurrencyCode': {
    //   fieldsValue: 'currencyCode',
    //   link: {
    //     field: 'biddingItemLogisticsDTO.transportFeeCurrencyName',
    //     fieldsValue: 'currencyName'
    //   }
    // },
    'biddingItemLogisticsDTO.transportFeeCurrencyName': {
      fieldsValue: 'currencyName',
      link: {
        field: 'biddingItemLogisticsDTO.transportFeeCurrencyCode',
        fieldsValue: 'currencyCode'
      }
    },

    // 起运港杂费币种编码
    // 'biddingItemLogisticsDTO.startPortFeeCode': {
    //   fieldsValue: 'currencyCode',
    //   link: {
    //     field: 'biddingItemLogisticsDTO.startPortFeeName',
    //     fieldsValue: 'currencyName'
    //   }
    // },
    'biddingItemLogisticsDTO.startPortFeeName': {
      fieldsValue: 'currencyName',
      link: {
        field: 'biddingItemLogisticsDTO.startPortFeeCode',
        fieldsValue: 'currencyCode'
      }
    },

    // 目的港杂费币种编码
    // 'biddingItemLogisticsDTO.endPortFeeCode': {
    //   fieldsValue: 'currencyCode',
    //   link: {
    //     field: 'biddingItemLogisticsDTO.endPortFeeName',
    //     fieldsValue: 'currencyName'
    //   }
    // },
    'biddingItemLogisticsDTO.endPortFeeName': {
      fieldsValue: 'currencyName',
      link: {
        field: 'biddingItemLogisticsDTO.endPortFeeCode',
        fieldsValue: 'currencyCode'
      }
    },

    // 代理费币种编码
    // 'biddingItemLogisticsDTO.agencyFeeCurrencyCode': {
    //   fieldsValue: 'currencyCode',
    //   link: {
    //     field: 'biddingItemLogisticsDTO.agencyFeeCurrencyName',
    //     fieldsValue: 'currencyName'
    //   }
    // },
    'biddingItemLogisticsDTO.agencyFeeCurrencyName': {
      fieldsValue: 'currencyName',
      link: {
        field: 'biddingItemLogisticsDTO.agencyFeeCurrencyCode',
        fieldsValue: 'currencyCode'
      }
    },

    // 'biddingItemDTO.bidCurrencyCode': {
    //   fieldsValue: 'currencyCode',
    //   link: {
    //     field: 'biddingItemDTO.bidCurrencyName',
    //     fieldsValue: 'currencyName'
    //   }
    // },
    'biddingItemDTO.bidCurrencyName': {
      fieldsValue: 'currencyName',
      link: {
        field: 'biddingItemDTO.bidCurrencyCode',
        fieldsValue: 'currencyCode'
      }
    }
  }
  const field = params.column.colId
  const value = params.value
  if (Object.keys(currencyMap).includes(field)) {
    const currencyMapItem = currencyMap[field]
    const config =
      params.colDef?.cellEditorParams?.editConfig?.props ||
      params.colDef?.cellEditorParams?.editConfig
    const dataSource = config?.dataSource || []
    const fieldsValue = currencyMap[field]['fieldsValue']
    const currency = dataSource.find((e) => e[fieldsValue] === value)
    if (currency) {
      params.node.setDataValue(
        currencyMapItem.link.field,
        currency[currencyMapItem.link.fieldsValue]
      )
    }
  }
}
/**
 * 订单单位名称联动 => 订单单位编码 和 单位编码一致 则转换率为1
 */
export const inputBidPurUnit = (params, grid, columns) => {
  const field = params.column.colId
  if (field !== 'biddingItemDTO.bidPurUnitName') return
  // 设置订单单位编码
  const { row } = getOptions(params)
  params.node.setDataValue('biddingItemDTO.bidPurUnitCode', row.unitCode)
  // 设置转换率
  if (!judgeUnitExist(columns)) return
  if (judgeUnitConsistent(params.data)) {
    // 符合更新条件
    params.node.setDataValue('biddingItemDTO.bidConversionRate', 1)
  }
}

/**
 * 出货模式联动
 */
export const inputShipMode = (params) => {
  const field = params.column.colId
  if (field !== 'biddingItemDTO.shipModeName') return
  // 设置出货模式编码
  const { row } = getOptions(params)
  params.node.setDataValue('biddingItemDTO.shipModeCode', row.dictCode)
}

/**
 * 付款条件联动
 */
export const inputSupplyType = (params) => {
  const field = params.column.colId
  if (field !== 'biddingItemDTO.supplyTypeName') return
  // 设置付款条件编码
  const { row } = getOptions(params)
  params.node.setDataValue('biddingItemDTO.supplyTypeCode', row.dictCode)
}

/**
 * 校验订单单位编码 和 单位编码 是否存在 存在返回ture
 */
export const judgeUnitExist = (columns) => {
  const cols = columns.map((item) => item.field)
  const flag =
    (cols.includes('biddingItemDTO.bidPurUnitCode') || cols.includes('purUnitCode')) &&
    cols.includes('unitCode') &&
    cols.includes('biddingItemDTO.bidConversionRate')
  return flag
}
/**
 * 校验订单单位编码 和 单位编码是否一致 (一致返回true)
 */
export const judgeUnitConsistent = (row) => {
  const purUnitCode = row.biddingItemDTO?.bidPurUnitCode || row.purUnitCode
  const unitCode = row.unitCode
  return purUnitCode === unitCode
}
/**
 * 报价有效期从联动 => 报价有效期止
 */
export const inputEffectiveDate = (params, quotedPriceData) => {
  if (
    quotedPriceData?.sourcingScenarios === 'LOGISTICS' ||
    quotedPriceData?.sourcingScenarios === 'sea_transport_annual' ||
    quotedPriceData?.sourcingScenarios === 'railway_transport_annual' ||
    quotedPriceData?.sourcingScenarios === 'trunk_transport_annual' ||
    ['0602'].includes(quotedPriceData?.companyCode)
  )
    return
  const field = params.column.colId
  if (field !== 'biddingItemDTO.quoteEffectiveStartDate') return
  let _startDate = params.data.biddingItemDTO.quoteEffectiveStartDate
  let _endDate = getEndDate(_startDate)
  _startDate && params.node.setDataValue('biddingItemDTO.quoteEffectiveEndDate', _endDate)
}
/**
 * 报价方式、报价属性联动编辑
 */
export const inputQuote = (params) => {
  if (params.oldValue === params.newValue) return
  const quoteMap = {
    // 报价方式
    'biddingItemDTO.quoteMode': {
      fieldsValue: 'quoteMode',
      link: {
        field: 'biddingItemDTO.quoteAttribute',
        fieldsValue: 'quoteAttribute'
      }
    },
    // 报价属性
    'biddingItemDTO.quoteAttribute': {
      fieldsValue: 'quoteAttribute',
      link: {
        field: 'biddingItemDTO.quoteMode',
        fieldsValue: 'quoteMode'
      }
    }
  }
  const field = params.column.colId
  const value = params.value
  if (Object.keys(quoteMap).includes(field)) {
    const quoteMapItem = quoteMap[field]
    const config =
      params.colDef?.cellEditorParams?.editConfig?.props ||
      params.colDef?.cellEditorParams?.editConfig
    const dataSource = config?.dataSource || []
    const quote = dataSource.find((e) => e.value === value)
    // 标准价 <=> 按入库  寄售价 <=> 按出库
    if (quote.value == 'standard_price') {
      params.node.setDataValue(quoteMapItem.link.field, 'in_warehouse')
    } else if (quote.value == 'mailing_price') {
      params.node.setDataValue(quoteMapItem.link.field, 'out_warehouse')
    } else if (quote.value == 'in_warehouse') {
      params.node.setDataValue(quoteMapItem.link.field, 'standard_price')
    } else if (quote.value == 'out_warehouse') {
      params.node.setDataValue(quoteMapItem.link.field, 'mailing_price')
    }
  }
}
/**
 * 供方税率选择 （只做税率名称 -> 税率编码、供方税率值的联动）
 */
export const inputBidTaxRate = (params, grid, columns) => {
  const field = params.column.colId
  // const rowId = params.data.addId
  if (!['biddingItemDTO.bidTaxRateName'].includes(field)) return
  const { row } = getOptions(params)
  // 设置供方税率编码、税率值
  params.node.setDataValue('biddingItemDTO.bidTaxRateCode', row.taxItemCode)
  params.node.setDataValue('biddingItemDTO.bidTaxRateValue', row.taxRate)
  const _ktFlag = sessionStorage.getItem('purDetailKtFlag')
  // const sourcingObjType = sessionStorage.getItem('purDetailKtFlag') // 另作整理
  if (_ktFlag && _ktFlag == 1) {
    // 根据含税算未税
    inputPrice(params, grid, columns, 'biddingItemDTO.taxedUnitPrice')
  } else {
    //根据未税算含税
    inputPrice(params, grid, columns, 'biddingItemDTO.untaxedUnitPrice')
  }
}
/**
 * 价格计算（逻辑待优化、模具和整机模组外发逻辑待整理）
 * grid: aggirdApi
 * columns: 列信息
 * params: 单元格信息
 * colId：触发字段
 */
export const inputPrice = (params, grid, columns, colId) => {
  // 存在colId时是税率选择时候触发
  const rowId = params.data.addId
  const field = colId || params.column.colId
  const costIdArr = colId ? colId.split('.') : []
  const oldValue = colId ? params.data[costIdArr[0]][costIdArr[1]] : params.oldValue
  const newValue = colId ? params.data[costIdArr[0]][costIdArr[1]] : params.newValue
  const rowNode = grid.api.getRowNode(rowId)
  const rowData = rowNode.data
  const cols = columns.map((item) => item.field)
  // biddingItemDTO.taxedTotalPrice	总价（含税）
  // biddingItemDTO.untaxedTotalPrice	总价（未税）
  // biddingItemDTO.taxedUnitPrice	单价（含税）
  // biddingItemDTO.untaxedUnitPrice	单价（未税）
  // biddingItemDTO.bidTaxRateValue 税率值
  // requireQuantity 需求数量 mt_supplier_rfx_item_ext
  let ktFlag = 0
  let _ktFlag = sessionStorage.getItem('purDetailKtFlag')
  if (_ktFlag && _ktFlag == 1) {
    ktFlag = 1
  }
  const checkNumbers = []
  const listenFields = ktFlag
    ? [
        //含税算未税(修改价格单位后续场景整理)
        'biddingItemDTO.taxedUnitPrice',
        'biddingItemDTO.priceUnitName'
      ]
    : [
        //未税算含税
        'biddingItemDTO.untaxedUnitPrice',
        'biddingItemDTO.priceUnitName'
      ]
  if (!rowData?.biddingItemDTO || !listenFields.includes(field)) return

  if (!isSafeNumber(newValue)) {
    newValue !== oldValue &&
      microTask.then(() => {
        // 还原输入
        rowNode.setDataValue(field, oldValue)
      })
    return
  }

  // 价格单位
  let priceUnitName = rowData.biddingItemDTO.priceUnitName
  priceUnitName =
    isNullOrUndefined(priceUnitName) || Number(priceUnitName) === 0 ? 1 : Number(priceUnitName)
  // 单价（未税）
  let untaxedUnitPrice = rowData.biddingItemDTO.untaxedUnitPrice || '0'
  // 单价（含税）
  let taxedUnitPrice = rowData.biddingItemDTO.taxedUnitPrice || '0'

  // 税率值
  const bidTaxRateValue = getBidTaxRateValue(grid, {
    bidTaxRateName: rowData.biddingItemDTO.bidTaxRateName,
    bidTaxRateCode: rowData.biddingItemDTO.bidTaxRateCode,
    bidTaxRateValue: rowData.biddingItemDTO.bidTaxRateValue || '0'
  })
  const rate = new Decimal32(bidTaxRateValue).add(1).toString()
  if (Number(bidTaxRateValue) >= 0) {
    // 修改单价未税
    if (field === 'biddingItemDTO.untaxedUnitPrice') {
      taxedUnitPrice = new Decimal32(untaxedUnitPrice)
        .mul(rate)
        .toFixed(ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS)
      rowNode.setDataValue('biddingItemDTO.taxedUnitPrice', taxedUnitPrice)

      //以下为实际分摊价格，计算   shareQuantity实际分摊数量
      const shareQuantity = rowData.rfxItemDieDTO?.shareQuantity
      if (
        cols.includes('biddingItemDTO.realSharePriceTaxed') &&
        cols.includes('biddingItemDTO.realSharePriceUntaxed')
      ) {
        if (shareQuantity > 0) {
          let _taxed = new Decimal32(taxedUnitPrice / shareQuantity).toFixed(
            ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS
          )
          let _untaxed = new Decimal32(untaxedUnitPrice / shareQuantity).toFixed(
            ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS
          )
          rowNode.setDataValue('biddingItemDTO.realSharePriceTaxed', _taxed)
          rowNode.setDataValue('biddingItemDTO.realSharePriceUntaxed', _untaxed)
        } else {
          rowNode.setDataValue('biddingItemDTO.realSharePriceTaxed', 0)
          rowNode.setDataValue('biddingItemDTO.realSharePriceUntaxed', 0)
        }
      }

      //以下为规划分摊价格，计算   planQuantity实际分摊数量
      const planQuantity = rowData.rfxItemDieDTO?.planQuantity
      if (
        cols.includes('biddingItemDTO.planSharePriceTaxed') &&
        cols.includes('biddingItemDTO.planSharePriceUntaxed')
      ) {
        if (planQuantity > 0) {
          let _taxed = new Decimal32(taxedUnitPrice / planQuantity).toFixed(
            ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS
          )
          let _untaxed = new Decimal32(untaxedUnitPrice / planQuantity).toFixed(
            ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS
          )
          rowNode.setDataValue('biddingItemDTO.planSharePriceTaxed', _taxed)
          rowNode.setDataValue('biddingItemDTO.planSharePriceUntaxed', _untaxed)
        } else {
          rowNode.setDataValue('biddingItemDTO.planSharePriceTaxed', 0)
          rowNode.setDataValue('biddingItemDTO.planSharePriceUntaxed', 0)
        }
      }
    } else if (field === 'biddingItemDTO.taxedUnitPrice') {
      // 修改单价含税
      untaxedUnitPrice = new Decimal32(taxedUnitPrice)
        .div(rate)
        .toFixed(ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS)
      rowNode.setDataValue('biddingItemDTO.untaxedUnitPrice', untaxedUnitPrice)
    }
  } else {
    // 修改单价未税
    if (field === 'biddingItemDTO.untaxedUnitPrice') {
      rowNode.setDataValue('biddingItemDTO.taxedUnitPrice', 0)
    } else if (field === 'biddingItemDTO.taxedUnitPrice') {
      // 修改单价含税
      rowNode.setDataValue('biddingItemDTO.untaxedUnitPrice', 0)
    }
    if (
      cols.includes('biddingItemDTO.realSharePriceTaxed') &&
      cols.includes('biddingItemDTO.realSharePriceUntaxed') &&
      cols.includes('biddingItemDTO.planSharePriceTaxed') &&
      cols.includes('biddingItemDTO.planSharePriceUntaxed')
    ) {
      //以下为实际分摊价格，计算
      rowNode.setDataValue('biddingItemDTO.realSharePriceTaxed', 0)
      rowNode.setDataValue('biddingItemDTO.realSharePriceUntaxed', 0)
      //以下为规划分摊价格，计算
      rowNode.setDataValue('biddingItemDTO.planSharePriceTaxed', 0)
      rowNode.setDataValue('biddingItemDTO.planSharePriceUntaxed', 0)
    }
  }

  // 数量
  let requireQuantity = rowData?.itemExtMap?.requireQuantity
  if (requireQuantity > 0) {
    // 单价（未税）- 真 单价 / 价格单位 * 需求数量
    const untaxedUnitPriceReal = new Decimal32(untaxedUnitPrice)
      .div(priceUnitName)
      .toFixed(ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS)
    // 单价（含税）- 真 单价 / 价格单位 * 需求数量
    const taxedUnitPriceReal = new Decimal32(taxedUnitPrice)
      .div(priceUnitName)
      .toFixed(ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS)

    if (untaxedUnitPrice) {
      const untaxedTotalPrice = new Decimal32(requireQuantity)
        .mul(untaxedUnitPriceReal)
        .toFixed(ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS)
      rowNode.setDataValue('biddingItemDTO.untaxedTotalPrice', untaxedTotalPrice)
      checkNumbers.push(untaxedTotalPrice)
    }
    if (taxedUnitPrice) {
      const taxedTotalPrice = new Decimal32(requireQuantity)
        .mul(taxedUnitPriceReal)
        .toFixed(ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS)
      rowNode.setDataValue('biddingItemDTO.taxedTotalPrice', taxedTotalPrice)
      checkNumbers.push(taxedTotalPrice)
    }
  }

  if (oldValue !== newValue) {
    if (checkNumbers.find((val) => !isSafeNumber(val))) {
      microTask.then(() => {
        // 还原输入
        rowNode.setDataValue(field, oldValue)
        // // 重新计算
        // inputPrice(ctx, { field, oldValue, value: oldValue })
      })
    }
  }
}
/**
 * 最小采购量 <=> 最小包装量 整数联动校验
 */
export const inputQuantity = (params) => {
  const field = params.column.colId
  if (!['biddingItemDTO.minPurQuantity', 'biddingItemDTO.minPackageQuantity'].includes(field))
    return
  const minPurQuantity = params.data.biddingItemDTO.minPurQuantity
  const minPackageQuantity = params.data.biddingItemDTO.minPackageQuantity
  if (!minPurQuantity || !minPackageQuantity) return
  if (judgePurAndQuantiry(minPurQuantity, minPackageQuantity)) {
    Vue.prototype.$toast({ content: '最小采购量需是最小包装量的整数倍', type: 'warning' })
  }
}
/**
 * 判断最小采购量和最小包装量
 */
export const judgePurAndQuantiry = (minPurQuantity, minPackageQuantity) => {
  if (!minPurQuantity || !minPackageQuantity) return false
  let _num = minPurQuantity / minPackageQuantity
  return Math.floor(_num) !== _num ? true : false
}
/**
 * 获取税率值 biddingItemDTO.bidTaxRateValue
 * @returns 税率值
 */
function getBidTaxRateValue(grid, bidInfo) {
  const { bidTaxRateCode, bidTaxRateName, bidTaxRateValue } = bidInfo
  const columnInfo = grid.columnApi.getColumn('biddingItemDTO.bidTaxRateName')
  if (bidTaxRateCode) {
    // 通过税率编码获取(此处组装与列信息保持一致)
    const { row } = getOptions({
      ...columnInfo,
      column: { colId: 'biddingItemDTO.bidTaxRateName' },
      value: bidTaxRateName
    })
    return row?.taxRate
  }
  return bidTaxRateValue
}
/**
 * 获取生效日期止
 * @returns date
 */
function getEndDate(date) {
  let _end = ''
  let currentYear = new Date(date).getFullYear()
  let judgeTime = new Date(currentYear + '-07-01').getTime()
  if (date < judgeTime) {
    _end = currentYear + '-12-31' // 当年12-31
  } else {
    _end = currentYear + 1 + '-12-31' // 次年12-31
  }

  return new Date(_end).getTime()
}
