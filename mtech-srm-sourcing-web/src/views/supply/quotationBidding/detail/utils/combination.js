import { isNullOrUndefined } from '@/utils/is'
import { getValueByPath } from '@/utils/obj'
import { PRICE_FRACTION_DIGITS } from '@/constants/editConfig'
import { Decimal32, isSafeNumber, microTask } from './utils'

export function isCombination(quotedPriceData) {
  return quotedPriceData.sourcingObjType === 'combination'
}
export function isHierarchy(quotedPriceData) {
  return quotedPriceData.sourcingObjType === 'HIERARCHY'
}

/**
 * 获取税率值 biddingItemDTO.bidTaxRateValue
 * @returns 税率值
 */
function getBidTaxRateValue(ctx) {
  const getValue = ctx.getValueByField
  const bidTaxRateCode = getValue('biddingItemDTO.bidTaxRateCode')
  const bidTaxRateValue = getValue('biddingItemDTO.bidTaxRateValue') || '0'
  if (bidTaxRateCode) {
    // 通过税率编码获取
    const { dataSource, fields } = ctx.getOptions('biddingItemDTO.bidTaxRateCode')
    if (Array.isArray(dataSource) && typeof fields === 'object') {
      const row = dataSource.find((e) => e[fields?.value || 'value'] === bidTaxRateCode)
      return row.taxRate
    }
  }
  return bidTaxRateValue
}

// 获取父行
async function getParentRow(rfxItemKey) {
  const actionObj = await getActionObj.call(this)
  return actionObj.find((e) => e.rfxItemKey === rfxItemKey)
}

async function getActionObj() {
  return await this.$refs.purchaseDetailRef.getActionObj()
}

/**
 * 价格计算
 */
export async function inputPrice(ctx, { field, oldValue, value }) {
  const getValue = ctx.getValueByField
  const setValue = ctx.setValueByField
  let _ktFlag = sessionStorage.getItem('purDetailKtFlag')
  const checkNumbers = []
  const listenFields = ['biddingItemDTO.untaxedUnitPrice', 'biddingItemDTO.priceUnitName']
  if (getValue('biddingItemDTO') && listenFields.includes(field)) {
    if (!isSafeNumber(value)) {
      value !== oldValue &&
        microTask.then(() => {
          // 还原输入
          setValue(field, oldValue)
        })
      return
    }
  }
  // 单价（未税）
  const untaxedUnitPrice = getValue('biddingItemDTO.untaxedUnitPrice') || '0'
  // 税率值
  const bidTaxRateValue = getBidTaxRateValue(ctx)
  const rate = new Decimal32(bidTaxRateValue).add(1).toString()
  // 单价（含税）
  const taxedUnitPrice = new Decimal32(untaxedUnitPrice)
    .mul(rate)
    .toFixed(_ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS)
  setValue('biddingItemDTO.taxedUnitPrice', taxedUnitPrice)
  // 价格单位
  let priceUnitName = getValue('biddingItemDTO.priceUnitName')
  priceUnitName =
    isNullOrUndefined(priceUnitName) || Number(priceUnitName) === 0 ? 1 : Number(priceUnitName)
  // 真 单价（未税）
  const untaxedUnitPriceReal = new Decimal32(untaxedUnitPrice)
    .div(priceUnitName)
    .toFixed(_ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS)
  // 真 单价（含税）
  const taxedUnitPriceReal = new Decimal32(taxedUnitPrice)
    .div(priceUnitName)
    .toFixed(_ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS)
  // 需求数量
  // let requireQuantity = getValue("itemExtMap.requireQuantity") || 0;
  const parentRow = await getParentRow.call(this, getValue('parentRfxItemKey'))
  if (!parentRow) {
    return
  }
  const childRequireQuantity = getValue('itemExtMap.requireQuantity') || 0
  // 含税单价  * 子件需求数量 = 含税总价
  const taxedTotalPrice = new Decimal32(taxedUnitPriceReal)
    .mul(childRequireQuantity)
    .toFixed(_ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS)
  // 未税单价  * 子件需求数量 = 未税总价
  const untaxedTotalPrice = new Decimal32(untaxedUnitPriceReal)
    .mul(childRequireQuantity)
    .toFixed(_ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS)
  checkNumbers.push(taxedTotalPrice)
  checkNumbers.push(untaxedTotalPrice)
  setValue('biddingItemDTO.taxedTotalPrice', taxedTotalPrice)
  setValue('biddingItemDTO.untaxedTotalPrice', untaxedTotalPrice)

  if (oldValue !== value) {
    if (checkNumbers.find((val) => !isSafeNumber(val))) {
      microTask.then(() => {
        // 还原输入
        setValue(field, oldValue)
        // 重新计算
        inputPrice.call(this, ctx, { field, oldValue, value: oldValue })
      })
    }
  }
}

export function defineGridColumnsAfter(columnData, isChild) {
  if (isHierarchy && isChild) {
    columnData.forEach((row) => {
      // 单价含税 taxedUnitPrice
      if (row?.field === 'biddingItemDTO.untaxedUnitPrice') {
        row.editConfig.disbled = true
        row.editConfig.readonly = true
        row.allowEditing = false
      }
    })
    return columnData
  }
  if (!isCombination(this.quotedPriceData)) {
    return columnData
  }
  if (!isChild) {
    columnData.forEach((row) => {
      // 单价含税 taxedUnitPrice 单价未税 untaxedUnitPrice 总价含税 taxedTotalPrice 总价未税  untaxedTotalPrice
      if (
        row.field &&
        ['taxedUnitPrice', 'untaxedUnitPrice', 'taxedTotalPrice', 'untaxedTotalPrice'].find(
          (field) => row.field.includes(field)
        )
      ) {
        delete row.edit
        row.allowEditing = false
      }
    })
  }
  return columnData
}

export function actionCompleteAfter(e, tag = 'parent') {
  let _ktFlag = sessionStorage.getItem('purDetailKtFlag')
  if (!isCombination(this.quotedPriceData)) {
    return
  }

  if (e.requestType == 'save') {
    if (tag !== 'parent') {
      this.actionObj.forEach((row1) => {
        // 单价（未税）
        let pUntaxedUnitPrice = new Decimal32(0)
        // 单价（含税）
        let pTaxedUnitPrice = new Decimal32(0)
        // 总价（未税）
        let pUntaxedTotalPrice = new Decimal32(0)
        // 总价（含税）
        let pTaxedTotalPrice = new Decimal32(0)

        if (Array.isArray(row1.childItems)) {
          row1.childItems.forEach((row2) => {
            const singleQuantity = getValueByPath(row2, 'itemExtMap.singleQuantity') || 0
            // 单价（未税）
            const untaxedUnitPrice = getValueByPath(row2, 'biddingItemDTO.untaxedUnitPrice') || 0
            // 单价（含税）
            const taxedUnitPrice = getValueByPath(row2, 'biddingItemDTO.taxedUnitPrice') || 0
            const taxedTotalPrice = getValueByPath(row2, 'biddingItemDTO.taxedTotalPrice') || 0
            const untaxedTotalPrice = getValueByPath(row2, 'biddingItemDTO.untaxedTotalPrice') || 0

            // 未税单价 = 所有子件单机用量 * 未税单价之和
            pUntaxedUnitPrice = pUntaxedUnitPrice.add(
              new Decimal32(singleQuantity).mul(untaxedUnitPrice)
            )
            // 含税单价 = 所有子件单机用量 * 含税单价之和
            pTaxedUnitPrice = pTaxedUnitPrice.add(new Decimal32(singleQuantity).mul(taxedUnitPrice))
            // 未税总价 子件未税总价之和
            pUntaxedTotalPrice = pUntaxedTotalPrice.add(new Decimal32(untaxedTotalPrice))
            // 含税总价 子件含税总价之和
            pTaxedTotalPrice = pTaxedTotalPrice.add(new Decimal32(taxedTotalPrice))
          })
        }
        row1.biddingItemDTO.taxedUnitPrice = pTaxedUnitPrice.toFixed(
          _ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS
        )
        row1.biddingItemDTO.untaxedUnitPrice = pUntaxedUnitPrice.toFixed(
          _ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS
        )
        row1.biddingItemDTO.taxedTotalPrice = pTaxedTotalPrice.toFixed(
          _ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS
        )
        row1.biddingItemDTO.untaxedTotalPrice = pUntaxedTotalPrice.toFixed(
          _ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS
        )
        this.actionObj.forEach((row) => {
          if (row.rfxItemId === row1.rfxItemId) {
            Object.assign(row.biddingItemDTO, {
              taxedUnitPrice: row1.biddingItemDTO.taxedUnitPrice,
              untaxedUnitPrice: row1.biddingItemDTO.untaxedUnitPrice,
              taxedTotalPrice: row1.biddingItemDTO.taxedTotalPrice,
              untaxedTotalPrice: row1.biddingItemDTO.untaxedTotalPrice
            })
          }
        })
      })
      this.$refs.templateRef.getCurrentTabRef()?.grid.refresh()
    }
  }
}
