export const useEdit = function ({
  row,
  editInstance,
  config
  // disabled = false,
}) {
  if (config?.editConfig) {
    const result = {
      ...row,
      formatter: config.formatter,
      edit: config.edit,
      editConfig: {
        type: config.editConfig.type,
        ...config.editConfig.props
      }
    }
    if (!result.edit) {
      result.edit = editInstance.create({
        valueConvert: config.editConfig.valueConvert
      })
    }
    // if (disabled && result.editConfig) {
    //   Object.assign(result.editConfig, {
    //     disabled: true,
    //     readonly: true,
    //   });
    // }
    return result
  }
  return row
}
