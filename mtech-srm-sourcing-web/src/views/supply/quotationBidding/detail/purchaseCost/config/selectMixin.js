export default {
  data() {
    return {}
  },
  computed: {},
  mounted() {},
  methods: {
    // 获取值集的fields信息
    getJsonValueFields(valueSet) {
      let fields = {}
      const valueSetTypeArr = [
        'conditions_base_price',
        'quota_no',
        'item_quota_no',
        'cost_factor_quota_no'
      ]
      if (valueSet === 'unit') {
        fields = {
          nameCode: 'unitName',
          valueCode: 'unitCode'
        }
      } else if (valueSet === 'cost_factor') {
        fields = {
          nameCode: 'costFactorName',
          valueCode: 'costFactorCode'
        }
      } else if (valueSetTypeArr.includes(valueSet)) {
        fields = {
          nameCode: 'attributeName',
          valueCode: 'attributeCode'
        }
      } else {
        fields = {
          nameCode: 'itemName',
          valueCode: 'itemCode'
        }
      }
      return fields
    }
  }
}
