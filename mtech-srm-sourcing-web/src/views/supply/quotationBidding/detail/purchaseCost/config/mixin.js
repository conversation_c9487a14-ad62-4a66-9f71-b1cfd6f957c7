import cloneDeep from 'lodash/cloneDeep'

export default {
  data() {
    return {}
  },
  computed: {
    defaultColumns() {
      return [
        {
          field: 'nodeName',
          title: this.$t('成本构成'),
          fixed: 'left',
          minWidth: 200,
          treeNode: true
        },
        {
          type: 'seq',
          title: this.$t('排序'),
          width: 80,
          slots: {
            default: ({ row, seq }) => {
              row.sortCode = seq
              return [<span>{seq}</span>]
            }
          }
        },
        {
          field: 'calculationFormulaSpec',
          title: this.$t('计算公式'),
          minWidth: 200
        },
        {
          field: 'result',
          title: this.$t('成本'),
          minWidth: 180,
          slots: {
            default: ({ row, level }) => {
              let tip = ''
              if (level === 0) {
                tip = this.$t('合计：')
              } else if (row.itemList?.length) {
                tip = this.$t('小计：')
              }
              return [
                <div>
                  <span>
                    {tip}
                    {row.result}
                  </span>
                </div>
              ]
            }
          }
        },
        {
          field: 'percent',
          title: this.$t('占销价百分比（%）'),
          minWidth: 180,
          slots: {
            default: ({ row, level }) => {
              let tip = ''
              if (level === 0) {
                tip = this.$t('合计：')
              } else if (row.itemList?.length) {
                tip = this.$t('小计：')
              }
              return [
                <div>
                  <span>
                    {tip}
                    {row.percent}
                  </span>
                </div>
              ]
            }
          }
        },
        {
          field: 'remark',
          title: this.$t('备注'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.remark} clearable />]
            }
          }
        },
        {
          field: 'errorMsg',
          title: this.$t('提示信息'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              return [<span style='color: red'>{row.errorMsg}</span>]
            }
          }
        }
      ]
    },
    toolbar() {
      return [
        {
          code: 'save',
          name: this.$t('保存'),
          status: 'info',
          disabled: this.tempForm.status !== 0 || this.isEditing
        },
        {
          code: 'import',
          name: this.$t('导入'),
          status: 'info',
          disabled: this.tempForm.status !== 0 || this.isEditing
        },
        {
          code: 'export',
          name: this.$t('导出'),
          status: 'info',
          disabled: !this.resData.id
        }
      ]
    }
  },
  mounted() {},
  methods: {
    // 处理动态配置信息
    formateColumns(obj) {
      const { extra_formula, form, header, classifyCode } = obj
      const typeMap = {
        0: 'number',
        1: 'text',
        2: 'select'
      }
      // 成本模板分类
      const cmtClassifyCode = classifyCode[0]?.classifyCode

      // 附加动态表单信息
      this.extraFormItems = []
      this.extraFormula = extra_formula || []
      extra_formula?.forEach((item) => {
        const {
          id,
          columnCode,
          columnName,
          columnType,
          columnAlias,
          valueSet,
          calculationFormula,
          calculationFormulaSpec
        } = item
        this.extraFormItems.push({
          id,
          type: typeMap[item.columnType],
          fieldCode: columnCode,
          fieldName: columnName,
          columnAlias,
          columnType,
          valueSet,
          calculationFormula,
          calculationFormulaSpec,
          readonly: true
        })
      })

      // 通用动态表单信息
      this.commonFormItems = []
      form?.forEach((item) => {
        this.commonFormItems.push({
          type: typeMap[item.columnType],
          fieldCode: item.columnCode,
          fieldName: item.columnName,
          columnAlias: item.columnAlias,
          columnType: item.columnType,
          valueSet: item.valueSet
        })
      })

      // 动态列信息
      this.dynamicColumns = []
      const defaultColumns = cloneDeep(this.defaultColumns)
      // 外发类型，不显示成本列
      if (cmtClassifyCode === 'out_going') {
        defaultColumns.splice(-2, 1)
      }
      header?.forEach((item) => {
        this.dynamicColumns.push({
          field: item.columnCode,
          title: item.columnName,
          columnAlias: item.columnAlias,
          columnType: item.columnType,
          minWidth: 150,
          editRender: {
            enabled: item.columnCode !== 'priceUnit' //价格单位不可编辑
          },
          slots: {
            header: () => {
              return [
                <vxe-tooltip content={item.columnCode} enterable>
                  <span>{item.columnName}</span>
                </vxe-tooltip>
              ]
            },
            default: ({ row, level }) => {
              const tip =
                level === 0 ? this.$t('合计：') : row.itemList?.length ? this.$t('小计：') : ''
              let template = [<span>{row[item.columnCode]}</span>]
              // 外发类型，非末级金额列，显示为“合计/小计XXXXX”
              if (cmtClassifyCode === 'out_going' && item.columnCode === 'amount') {
                template = [
                  <span>
                    {tip}
                    {row[item.columnCode]}
                  </span>
                ]
              }
              return template
            },
            edit: ({ row, column, level }) => {
              let template = [<span>{row[item.columnCode]}</span>]

              // 成本因子，自动带出规格、品牌、单位、单价
              // const costFactorCol = header?.find((t) => t.columnCode === 'costFactor')
              // const arr = ['costFactorSpec', 'costFactorBrand', 'unitName', 'price']
              // const colDisabled = costFactorCol && arr.includes(item.columnCode)

              // 成本构成为‘其他/它XXX’、costFactor的isInput=1，成本因子可选择、可输入
              const isInput =
                item.valueSet === 'cost_factor' &&
                ([this.$t('其他'), this.$t('其它')].includes(row.nodeName?.substr(0, 2)) ||
                  row.costFactorIsInput === 1)

              // 仅末级可编辑动态列，且单元格标识xxxRo为false
              if (!row.itemList?.length && !row[item.columnCode + 'Ro']) {
                switch (typeMap[item.columnType]) {
                  case 'select':
                    template = [
                      <div style='display: flex'>
                        <vxe-input
                          v-model={row[item.columnCode]}
                          clearable
                          readonly={!isInput}
                          type='search'
                          on-clear={() => this.handleClearItem(row, column, item.valueSet)}
                          on-search-click={() => this.handleSelectItem(row, column, item.valueSet)}
                          on-input={({ value }) => {
                            if (item.valueSet === 'cost_factor') {
                              row[item.columnCode + 'Json'] = value
                                ? {
                                    costFactorCode: value,
                                    costFactorName: value
                                  }
                                : null
                            }
                          }}
                        />
                      </div>
                    ]
                    break
                  case 'number':
                  case 'text':
                    template = [
                      <vxe-input
                        v-model={row[item.columnCode]}
                        type={typeMap[item.columnType]}
                        min='0'
                        clearable
                      />
                    ]
                    break
                  default:
                    template = [<span>{row[item.columnCode]}</span>]
                    break
                }
              }
              // 外发类型，非末级金额列，显示为“合计/小计XXXXX”
              else if (cmtClassifyCode === 'out_going' && item.columnCode === 'amount') {
                const tip =
                  level === 0 ? this.$t('合计：') : row.itemList?.length ? this.$t('小计：') : ''
                template = [
                  <span>
                    {tip}
                    {row[item.columnCode]}
                  </span>
                ]
              }
              return template
            }
          }
        })
      })
      defaultColumns.splice(2, 0, ...this.dynamicColumns)
      this.columns = defaultColumns
    },
    // 处理成本测算参数
    formatFormValueList(type) {
      const formValueList = []
      if (type === 'extra') {
        this.extraFormItems.forEach((item) => {
          const { id, fieldCode, fieldName, calculationFormula, columnAlias, columnType } = item
          formValueList.push({
            id,
            fieldCode,
            fieldName,
            columnCode: fieldCode,
            columnName: fieldName,
            columnAlias,
            columnType,
            calculationFormula,
            result: this.extraFormData[fieldCode]
          })
        })
      } else {
        this.commonFormItems.forEach((item) => {
          const { fieldCode, fieldName, columnAlias, columnType } = item
          formValueList.push({
            fieldCode,
            fieldName,
            columnCode: fieldCode,
            columnName: fieldName,
            columnAlias,
            columnType,
            dataValue: columnType === 0 ? this.commonFormData[fieldCode] : null,
            stringValue: columnType === 1 ? this.commonFormData[fieldCode] : null,
            jsonValue: columnType === 2 ? this.commonFormData[fieldCode + 'Json'] : null
          })
        })
      }
      return formValueList
    },
    // 清除动态列（选择弹窗）
    handleClearItem(row, column, valueSet) {
      row[column.field] = null
      row[column.field + 'Json'] = {}
      row.isUpdate = true

      // 成本因子，带出规格、品牌、单位、单价
      if (
        valueSet === 'cost_factor' &&
        ![this.$t('其他'), this.$t('其它')].includes(row.nodeName?.substr(0, 2)) &&
        row.costFactorIsInput !== 1
      ) {
        const tempList = ['costFactorSpec', 'costFactorBrand', 'unitName', 'price']
        tempList.forEach((field) => {
          row[field] = null
          if (field === 'unitName') {
            row.unitNameJson = {}
          }
        })
      }
      this.handleUpdateRow(row)
    },
    // 选择动态列（选择弹窗）
    handleSelectItem(row, column, valueSet) {
      let comp = null
      switch (valueSet) {
        case 'cost_factor':
          comp = import('@/views/common/components/dialog/costFactorPriceDialog.vue')
          break
        case 'unit':
          comp = import('../dialog/unitDiaolog.vue')
          break
        default:
          comp = import('@/views/common/components/dialog/itemCodeDialog.vue')
          break
      }
      this.$dialog({
        modal: () => comp,
        data: {
          title: column.title,
          type: 'Supplier',
          rfxId: this.$route.query.rfxId,
          valueSet,
          nodeCode: row?.nodeCode
        },
        success: (data) => {
          const oldCellValue = row[column.field]
          const fields = this.getJsonValueFields(valueSet)
          const newValue = data[fields.valueCode]
          const jsonValue = {}
          jsonValue[fields.valueCode] = data[fields.valueCode]
          jsonValue[fields.nameCode] = data[fields.nameCode]

          if (newValue !== oldCellValue) {
            row[column.field] = newValue
            row[column.field + 'Json'] = jsonValue
            row.isUpdate = true

            // 成本因子，带出规格、品牌、单位、单价
            if (valueSet === 'cost_factor') {
              const tempList = [
                { field: 'costFactorSpec', valueKey: 'costFactorSpec' },
                { field: 'costFactorBrand', valueKey: 'costFactorBrand' },
                { field: 'unitName', valueKey: 'basicMeasureUnitCode' },
                { field: 'price', valueKey: 'unitPriceUntaxed' }
              ]
              tempList.forEach((item) => {
                row[item.field] = data[item.valueKey]
                if (item.field === 'unitName') {
                  row.unitNameJson = {
                    unitCode: data.basicMeasureUnitCode,
                    unitName: data.basicMeasureUnitName
                  }
                }
              })
            }

            this.handleUpdateRow(row)
          }
        },
        close: () => {
          this.leftTableRef.setEditRow(row)
        }
      })
    },
    // 递归处理，成本测算参数
    formatItemList(treeList) {
      const resList = treeList.map((item) => {
        const itemValueList = []
        this.dynamicColumns.forEach((c) => {
          const { field, title, columnAlias, columnType } = c
          let dataValue = null
          if (columnType === 0 && (item[field] || item[field] === '0')) {
            dataValue = Number(item[field])
          }
          itemValueList.push({
            columnCode: field,
            columnName: title,
            columnAlias,
            columnType,
            dataValue,
            stringValue: columnType === 1 ? item[field] : null,
            jsonValue: columnType === 2 ? item[field + 'Json'] : null,
            ro: item[field + 'Ro']
          })
        })
        return {
          ...item,
          itemList: item.itemList ? this.formatItemList(item.itemList) : [],
          itemValueList
        }
      })
      return resList
    },
    // 序列化，成本测算结果-表单
    serializeFormData(dataList, type) {
      const formData = {}
      if (type === 'common') {
        dataList.forEach((item) => {
          const { columnType } = item
          let _value = null
          if (columnType === 0) {
            _value = item.dataValue
          } else if (columnType === 1) {
            _value = item.stringValue
          } else {
            const key = Object.keys(item.jsonValue || {})?.find((k) => k?.includes('Code'))
            _value = key ? item.jsonValue[key] : null
          }
          formData[item.columnCode] = _value
        })
      } else {
        dataList.forEach((item) => {
          formData[item.fieldCode] = item.result
        })
      }
      return formData
    },
    // 序列化，成本测算结果-列表
    serializeItemList(treeList) {
      const resList = treeList.map((item) => {
        item.itemValueList?.forEach((v) => {
          if (v.columnType === 0) {
            item[v.columnCode] = v.dataValue
          } else if (v.columnType === 1) {
            item[v.columnCode] = v.stringValue
          } else {
            const key = Object.keys(v.jsonValue || {})?.find((k) => k?.includes('Code'))
            item[v.columnCode] = key ? v.jsonValue[key] : null
            item[v.columnCode + 'Json'] = key ? v.jsonValue : null
          }
          item[v.columnCode + 'Ro'] = v.ro
          item[v.columnCode + 'IsInput'] = v.isInput
        })
        return {
          ...item,
          itemList: item.itemList ? this.serializeItemList(item.itemList) : []
        }
      })
      return resList
    },
    // 编辑行
    async handleUpdateRow(curRow) {
      const tableData = [...this.leftTableData]
      this.updateTreeNode(tableData, curRow.id, curRow)
      this.leftTableData = [...tableData]

      await this.leftTableRef?.setEditRow(curRow) // 设置行为编辑状态
    },
    // 数组（树结构）-- 修改
    updateTreeNode(treeList, id, obj) {
      if (!treeList || !treeList.length) {
        return
      }
      for (let i = 0; i < treeList.length; i++) {
        if (treeList[i].id == id) {
          treeList[i] = obj
          break
        }
        this.updateTreeNode(treeList[i].itemList, id, obj)
      }
    }
  }
}
