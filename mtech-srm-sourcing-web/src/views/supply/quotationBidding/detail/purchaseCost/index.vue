<template>
  <div class="full-height">
    <div class="top-container">
      <div class="top-info">
        <div class="left">
          <div class="title">
            <span :style="tempForm.costModelCode && 'margin-right: 20px;'">{{
              tempForm.costModelCode
            }}</span>
            <span class="sub-title" :style="tempForm.costModelName && 'margin-right: 20px;'">{{
              tempForm.costModelName
            }}</span>
            <span class="status">{{ status }}</span>
          </div>
          <div class="info">
            <div class="item">{{ $t('版本号：') }}{{ tempForm.costModelVersionCode || '--' }}</div>
            <div class="item">{{ $t('物料编码：') }}{{ tempForm.materialCode || '--' }}</div>
            <div class="item">{{ $t('临时物编：') }}{{ tempForm.tempMaterialCode || '--' }}</div>
            <div class="item">{{ $t('阶梯数量：') }}{{ tempForm.stepValue || '--' }}</div>
            <div class="item">{{ $t('直送地：') }}{{ tempForm.deliveryPlace || '--' }}</div>
            <div class="item">{{ $t('业务方：供应方') }}</div>
          </div>
        </div>
        <div class="right">
          <vxe-button size="small" @click="handleExport(null)">{{ $t('导出') }}</vxe-button>
          <vxe-button size="small" @click="handleBack">{{ $t('返回') }}</vxe-button>
        </div>
      </div>
    </div>

    <div
      class="body-container"
      :class="[!leftExpand && 'left-hidden', !rightExpand && 'right-hidden']"
    >
      <!-- 左侧内容 -->
      <div class="letf container">
        <div class="content">
          <div class="body">
            <mt-form ref="tempFormRef" :model="tempForm">
              <mt-row :gutter="20">
                <mt-col :span="10">
                  <mt-form-item prop="counterName" :label="$t('报价次数')" label-style="left">
                    <vxe-input
                      v-model="tempForm.counterName"
                      type="text"
                      clearable
                      :disabled="true"
                      :placeholder="$t('报价次数')"
                    />
                  </mt-form-item>
                </mt-col>
                <mt-col :span="14" style="text-align: right; line-height: 28px">
                  <vxe-button
                    v-for="item in toolbar"
                    :key="item.code"
                    :status="item.status"
                    :icon="item.icon"
                    :disabled="item.disabled"
                    size="small"
                    @click="handleClickToolBar(item)"
                    >{{ item.name }}</vxe-button
                  >
                </mt-col>
              </mt-row>
            </mt-form>
          </div>
          <div class="form-info" v-if="extraFormItems.length || commonFormItems.length">
            <div class="title" @click="handleToggle('leftFormInfoShow')">
              <i class="vxe-icon-caret-down" v-show="!leftFormInfoShow" />
              <i class="vxe-icon-caret-up" v-show="leftFormInfoShow" />
              <span>{{ $t('报价信息') }}</span>
            </div>
            <div class="body" v-show="leftFormInfoShow">
              <custom-form :data="extraFormData" :form-items="extraFormItems" type="extra" />
              <div class="tip" v-if="extraFormItems.length">{{ priceTipInfo }}</div>
              <custom-form
                :data="commonFormData"
                :form-items="commonFormItems"
                type="common"
                :readonly="tempForm.status === 1"
                @change="(e) => handleFormValueChange('common', e)"
              />
            </div>
          </div>
          <div class="table-info" v-if="leftExpand">
            <div class="title" @click="handleToggle('leftTableShow')">
              <i class="vxe-icon-caret-down" v-show="!leftTableShow" />
              <i class="vxe-icon-caret-up" v-show="leftTableShow" />
              <span>{{ $t('其他信息') }}</span>
            </div>
            <div class="body" v-show="leftTableShow">
              <sc-table
                ref="leftTableRef"
                row-id="id"
                :keep-source="true"
                :tree-config="treeConfig"
                :edit-config="editConfig"
                :is-show-right-btn="false"
                :loading="leftLoading"
                :columns="columns"
                :table-data="leftTableData"
                :cell-style="(e) => handleCellStyle('left', e)"
                @scroll="(e) => handleScroll('left', e)"
                @edit-actived="isEditing = true"
                @edit-closed="handleCalculate"
              />
            </div>
          </div>
        </div>
        <div v-show="rightExpand" class="operate left-operate" @click="handleToggle('leftExpand')">
          <i class="vxe-icon-arrow-left" />
        </div>
      </div>
      <!-- 右侧内容 -->
      <div class="right container">
        <div v-show="leftExpand" class="operate right-operate" @click="handleToggle('rightExpand')">
          <i class="vxe-icon-arrow-right" />
        </div>
        <div class="content" v-if="rightExpand">
          <div class="body">
            <mt-form ref="tempFormRef" :model="tempForm">
              <mt-row :gutter="10">
                <mt-col :span="10">
                  <mt-form-item prop="curCount" :label="$t('本单报价')" label-style="left">
                    <vxe-select
                      v-model="tempForm.curCount"
                      :options="countList || []"
                      :option-props="{ label: 'text', value: 'value' }"
                      clearable
                      :placeholder="$t('请选择本单报价')"
                      @change="(e) => handleCountChange('', e)"
                    />
                  </mt-form-item>
                </mt-col>
                <mt-col :span="10">
                  <mt-form-item prop="historyCount" :label="$t('历史报价')" label-style="left">
                    <vxe-select
                      v-model="tempForm.historyCount"
                      :options="historyCountList || []"
                      :option-props="{ label: 'text', value: 'value' }"
                      clearable
                      :placeholder="$t('请选择历史报价')"
                      @change="(e) => handleCountChange('history', e)"
                    />
                  </mt-form-item>
                </mt-col>
                <mt-col :span="4" style="text-align: right; line-height: 28px">
                  <vxe-button
                    size="small"
                    :disabled="
                      (!tempForm.curCount && tempForm.curCount !== 0) ||
                      (tempForm.historyCount && tempForm.historyCount !== 0)
                    "
                    @click="handleExport('right')"
                    >{{ this.$t('导出') }}</vxe-button
                  >
                </mt-col>
              </mt-row>
            </mt-form>
          </div>
          <div class="form-info" v-if="extraFormItems.length || commonFormItems.length">
            <div class="title" @click="handleToggle('rightFormInfoShow')">
              <i class="vxe-icon-caret-down" v-show="!rightFormInfoShow" />
              <i class="vxe-icon-caret-up" v-show="rightFormInfoShow" />
              <span>{{ $t('报价信息') }}</span>
            </div>
            <div class="body" v-show="rightFormInfoShow">
              <custom-form :data="rightExtraFormData" :form-items="extraFormItems" type="extra" />
              <div class="tip" v-if="extraFormItems.length">{{ priceTipInfo }}</div>
              <custom-form
                :data="rightCommonFormData"
                :form-items="commonFormItems"
                :readonly="true"
                type="common"
              />
            </div>
          </div>
          <div class="table-info">
            <div class="title" @click="handleToggle('rightTableShow')">
              <i class="vxe-icon-caret-down" v-show="!rightTableShow" />
              <i class="vxe-icon-caret-up" v-show="rightTableShow" />
              <span>{{ $t('其他信息') }}</span>
            </div>
            <div class="body" v-show="rightTableShow">
              <sc-table
                ref="rightTableRef"
                row-id="id"
                :keep-source="true"
                :tree-config="treeConfig"
                :is-show-right-btn="false"
                :loading="rightLoading"
                :columns="columns"
                :table-data="rightTableData"
                :cell-style="(e) => handleCellStyle('right', e)"
                @scroll="(e) => handleScroll('right', e)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :request-urls="requestUrls"
      file-key="file"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    />
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'
import CustomForm from './components/customForm.vue'
import mixin from './config/mixin'
import selectMixin from './config/selectMixin.js'
import XEUtils from 'xe-utils'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {
    ScTable,
    CustomForm,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  mixins: [mixin, selectMixin],
  data() {
    return {
      treeConfig: {
        children: 'itemList',
        expandAll: true
      },
      columns: [],
      dynamicColumns: [],
      leftTableData: [],
      rightTableData: [],
      leftLoading: false,
      rightLoading: false,
      leftExpand: true,
      rightExpand: true,
      extraFormItems: [],
      extraFormula: [],
      extraFormData: {},
      commonFormItems: [],
      commonFormData: {},
      rightExtraFormData: {},
      rightCommonFormData: {},
      tempForm: {},
      countList: [],
      priceList: [],
      historyCountList: [],
      historyPriceList: [], //历史报价
      resData: {},
      estimateRes: {},
      requestUrls: {},
      isContentChange: false,
      isEditing: false,
      leftFormInfoShow: true,
      leftTableShow: true,
      rightFormInfoShow: true,
      rightTableShow: true
    }
  },
  computed: {
    editConfig() {
      return {
        enabled: this.tempForm.status !== 1, // 非已提交状态，启用编辑
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    leftTableRef() {
      return this.$refs.leftTableRef.$refs.xGrid
    },
    rightTableRef() {
      return this.$refs.rightTableRef.$refs.xGrid
    },
    priceTipInfo() {
      const arr = []
      this.extraFormula.forEach((item) => {
        const { columnName, calculationFormulaSpec } = item
        const spec =
          !calculationFormulaSpec && calculationFormulaSpec !== 0 ? '' : calculationFormulaSpec
        arr.push(columnName + '=' + spec)
      })
      return arr.join(';')
    },
    status() {
      switch (this.tempForm.status) {
        case 0:
          return this.$t('未提交')
        case 1:
          return this.$t('已提交')
        default:
          return this.$t('暂无状态')
      }
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.getCostModelDetailColumns()
      this.getCostModelList()
      this.getCostModelPriceList()
      this.getCostModelPriceList('history')
    },
    // 表单数据修改
    handleFormValueChange(type, formData) {
      this[type + 'FormData'] = formData
      if (type === 'common') {
        this.handleCalculate({})
      }
      this.isContentChange = true
    },
    // 选择报价
    handleCountChange(type, e) {
      if (e.value || e.value === 0) {
        if (type === 'history') {
          this.$set(this.tempForm, 'curCount', null)
          const data = this.historyPriceList[e.value]
          this.$dialog({
            modal: () => import('./dialog/coverDialog.vue'),
            data: {
              title: this.$t('提示')
            },
            success: () => {
              this.updateData(data, true)
            },
            close: () => {
              this.updateData(data, false)
            }
          })
        } else {
          this.$set(this.tempForm, 'historyCount', null)
          const data = this.priceList[e.value]
          this.updateData(data, false)
        }
      }
    },
    updateData(data, flag) {
      if (flag) {
        this.isContentChange = true
        this.extraFormData = this.serializeFormData(data?.extraList || [])
        this.commonFormData = this.serializeFormData(data?.formValueList || [], 'common')
        this.leftTableData = this.serializeItemList(data?.itemList || [])
        this.$nextTick(() => this.leftTableRef.setAllTreeExpand(true))
      }
      this.rightExtraFormData = this.serializeFormData(data?.extraList || [])
      this.rightCommonFormData = this.serializeFormData(data?.formValueList || [], 'common')
      this.rightTableData = this.serializeItemList(data?.itemList || [])
      this.$nextTick(() => this.rightTableRef.setAllTreeExpand(true))
    },
    // 点击工具栏按钮
    handleClickToolBar(e) {
      switch (e.code) {
        case 'save':
          this.handleSaveCostModel()
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          this.handleExport('left')
          break
        default:
          break
      }
    },
    // 返回
    handleBack() {
      if (this.isContentChange) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('有未保存的记录，是否确认返回？')
          },
          success: () => {
            this.$router.go(-1)
          }
        })
      } else {
        this.$router.go(-1)
      }
    },
    // 获取配置列、表单信息
    async getCostModelDetailColumns() {
      const params = {
        rfxHeaderId: this.$route.query.rfxId,
        rfxItemId: this.$route.query.rfxItemId
      }
      const res = await this.$API.supplierBiddingCost.queryColumn(params)
      if (res.code === 200 && res.data) {
        this.formateColumns(res.data)
        this.$refs.leftTableRef.handleResize()
        this.$refs.rightTableRef.handleResize()
      }
    },
    // 查询成本模型列表
    async getCostModelList() {
      const params = {
        rfxHeaderId: this.$route.query.rfxId,
        rfxItemId: this.$route.query.rfxItemId
      }
      this.leftLoading = true
      const res = await this.$API.supplierBiddingCost.queryBidItemCostModel(params).catch(() => {
        this.leftLoading = false
      })
      this.leftLoading = false
      if (res.code === 200) {
        this.resData = res.data || {}
        const {
          costModelCode,
          costModelVersionCode,
          costModelName,
          counterName,
          status,
          directDeliverAddr,
          stepValue,
          materialCode,
          tempMaterialCode
        } = res.data
        this.tempForm = {
          ...this.tempForm,
          costModelCode,
          costModelVersionCode,
          costModelName,
          counterName,
          status,
          stepValue,
          deliveryPlace: directDeliverAddr,
          materialCode,
          tempMaterialCode
        }
        this.extraFormData = this.serializeFormData(res.data?.extraList || [], 'extra')
        this.commonFormData = this.serializeFormData(res.data?.formValueList || [], 'common')
        this.leftTableData = this.serializeItemList(res.data?.itemList || [])
        this.$nextTick(() => this.leftTableRef.setAllTreeExpand(true))

        this.isContentChange = false
      }
    },
    // 获取本单报价、历史报价列表
    async getCostModelPriceList(type) {
      const temp = type === 'history' ? 'History' : ''
      const params = {
        rfxHeaderId: this.$route.query.rfxId,
        rfxItemId: this.$route.query.rfxItemId
      }
      this.rightLoading = true
      const res = await this.$API.supplierBiddingCost['query' + temp + 'CostModelPriceList'](
        params
      ).catch(() => {
        this.rightLoading = false
      })
      this.rightLoading = false
      if (res.code === 200) {
        if (type === 'history') {
          this.historyPriceList = res.data || []
          this.historyCountList = []
          for (let i = 0; i < res.data.length; i++) {
            this.historyCountList.push({
              text: this.$t(`最近${i + 1}次：${res.data[i].result}`),
              value: i
            })
          }
        } else {
          this.priceList = res.data || []
          this.countList = []
          for (let i = 0; i < res.data.length; i++) {
            this.countList.push({
              text: this.$t(`${res.data[i].counterName}：${res.data[i].result}`),
              value: i
            })
          }
        }
      }
    },
    // 保存成本模型信息
    async handleSaveCostModel() {
      const { rfxId, rfxItemId, abateFlag } = this.$route.query
      const params = {
        ...this.resData,
        ...this.estimateRes,
        rfxHeaderId: rfxId,
        rfxItemId,
        abateFlag,
        extraList: this.formatFormValueList('extra'),
        formValueList: this.formatFormValueList('common'),
        itemList: this.formatItemList(this.leftTableData)
      }
      const res = await this.$API.supplierBiddingCost.saveCostModelPrice(params)
      if (res.code === 200) {
        this.$toast({ content: this.$t('保存成功'), type: 'success' })
        this.getCostModelList()
        this.getCostModelPriceList()
        this.getCostModelPriceList('history')
      }
    },
    // 成本测算
    async handleCalculate(e) {
      if (e) {
        const { row } = e
        let isRowUpdate = row ? this.leftTableRef.isUpdateByRow(row) : true
        // 解决弹窗编辑单元格，更新之后isUpdateByRow方法获取为false的问题，但实际值更新了（用row的isUpdate属性标识行是否被更新）
        if (row && row.isUpdate) {
          isRowUpdate = true
        }
        this.isContentChange = isRowUpdate
        // 行未更新，不进行成本测算
        if (!isRowUpdate) {
          this.isEditing = false
          return
        }
      }

      const { costModelCode, versionCode } = this.resData
      const { rfxId, rfxItemId, abateFlag } = this.$route.query
      const params = {
        id: this.resData.id,
        rfxHeaderId: rfxId,
        rfxItemId,
        costModelCode,
        versionCode,
        abateFlag,
        extraList: this.formatFormValueList('extra'),
        formValueList: this.formatFormValueList('common'),
        itemList: this.formatItemList(this.leftTableData)
      }
      this.leftLoading = true
      const res = await this.$API.supplierBiddingCost.costEstimate(params).catch(() => {
        this.leftLoading = false
        this.isEditing = false
      })
      this.leftLoading = false
      this.isEditing = false

      if (res?.code === 200) {
        const expandRows = this.leftTableRef.getTreeExpandRecords()
        this.estimateRes = res.data
        this.extraFormData = this.serializeFormData(res.data?.extraList || [])
        this.commonFormData = this.serializeFormData(res.data?.formValueList || [], 'common')
        this.leftTableData = this.serializeItemList(res.data?.itemList || [])

        // 仅展开原本展开的行
        expandRows.forEach((row) => {
          const node = XEUtils.findTree(this.leftTableData, (r) => r.id === row.id, {
            children: 'itemList'
          })?.item
          if (node) {
            this.$nextTick(() => this.leftTableRef.setTreeExpand(node, true))
          }
        })
      }
    },
    // 导入
    handleImport() {
      this.requestUrls = {
        templateUrlPre: 'supplierBiddingCost',
        uploadUrl: 'importCostModel',
        id: this.resData.id,
        noDown: true
      }
      this.showUploadExcel(true)
    },
    // 导出
    async handleExport(type) {
      if (type) {
        let id = this.resData.id
        if (type === 'right') {
          const historyItem = this.historyPriceList[this.tempForm.historyCount]
          historyItem && (id = historyItem.id)
          const curItem = this.priceList[this.tempForm.curCount]
          curItem && (id = curItem.id)
        }
        const res = await this.$API.supplierBiddingCost.exportCostModel({ id })
        if (res.data) {
          this.$toast({ type: 'success', content: this.$t('正在导出，请稍后！') })
          let fileName = getHeadersFileName(res)
          download({ fileName: fileName, blob: res.data })
        }
      } else {
        const params = {
          rfxHeaderId: this.$route.query.rfxId,
          rfxItemId: this.$route.query.rfxItemId
        }
        const res = await this.$API.supplierBiddingCost.exportCostModelContrast(params)
        if (res.data) {
          this.$toast({ type: 'success', content: this.$t('正在导出，请稍后！') })
          let fileName = getHeadersFileName(res)
          download({ fileName: fileName, blob: res.data })
        }
      }
    },
    // 展示/不展示 上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = [] // 清空数据
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 导入成功
    upExcelConfirm(res) {
      this.showUploadExcel(false)
      if (res?.code === 200) {
        this.leftTableData = this.serializeItemList(res.data?.itemList || [])
        this.$nextTick(() => this.leftTableRef.setAllTreeExpand(true))

        // 成本测算
        this.handleCalculate()
      }
    },
    // 左右两个Table一起滚动
    handleScroll(type, e) {
      const { isX, isY, scrollLeft, scrollTop } = e
      const prefix = type === 'left' ? 'right' : 'left'
      if (this[prefix + 'Expand']) {
        const tableRef = this.$refs[prefix + 'TableRef']?.$refs?.xGrid
        isX && tableRef?.scrollTo(scrollLeft, null)
        isY && tableRef?.scrollTo(null, scrollTop)
      }
    },
    // 控制左右两部分内容展开、折叠
    handleToggle(key) {
      this[key] = !this[key]
      this.$refs.leftTableRef.handleResize(0)
      this.$refs.rightTableRef.handleResize(1)
    },
    // 单元格样式
    handleCellStyle(type, e) {
      const { row, column } = e
      const tableData = type === 'left' ? this.rightTableData : this.leftTableData
      const tempRow = XEUtils.findTree(tableData, (item) => item.id === row.id, {
        children: 'itemList'
      })?.item

      const fieldList = []
      this.dynamicColumns?.forEach((item) => item.columnType === 0 && fieldList.push(item.field))
      if (
        fieldList.includes(column.field) &&
        tempRow &&
        (tempRow[column.field] || tempRow[column.field] == 0)
      ) {
        if (row[column.field] > tempRow[column.field]) {
          return { color: 'red' }
        }
        if (row[column.field] < tempRow[column.field]) {
          return { color: '#06d006' }
        }
      }
      return null
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;

  .top-container {
    height: 80px;
    background-color: #fff;
    padding: 10px;
  }
  .body-container {
    display: flex;
    justify-content: space-between;
    height: calc(100% - 80px);
  }
}
.top-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 10px;
  height: 100%;
  background-color: rgba(99, 134, 193, 0.08);
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  box-shadow: 0 0 10px 0 hsla(0, 7%, 50%, 0.06);

  .left {
    .title {
      height: 28px;
      line-height: 28px;
      font-size: 20px;
      font-weight: 600;

      .sub-title {
        color: #9a9a9a;
        font-size: 16px;
        font-weight: 500;
      }
      .status {
        display: inline-block;
        padding: 0 5px;
        height: 24px;
        line-height: 22px;
        text-align: center;
        background: #e8ecf5;
        border-radius: 2px;
        font-size: 12px;
        color: rgba(99, 134, 193, 1);
        font-weight: 500;
      }
    }
    .info {
      display: flex;
      height: 22px;
      line-height: 22px;
      .item {
        margin-right: 20px;
      }
    }
  }
}
.container {
  display: flex;
  height: 100%;
  width: 100%;
  background-color: #fff;

  .title {
    height: 35px;
    line-height: 35px;
    font-weight: bold;
    color: #fff;
    background-color: #3c435e;
    i {
      margin: auto 8px;
    }
  }
  .body {
    padding: 10px 15px;
    .tip {
      line-height: 22px;
      color: red;
      font-weight: bold;
    }
  }

  .content {
    width: calc(100% - 20px);
    border: 1px solid #e8e8e8;
    margin-bottom: 10px;
    .form-info {
      margin-bottom: 5px;
      .mt-form {
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 4 - 10px));
        justify-content: space-between;
        grid-gap: 5px;
      }
    }
  }
  .operate {
    margin: auto;
    width: 15px;
    height: 80px;
    line-height: 80px;
    border: 1px solid #e8e8e8;
    color: #3c435e;
    font-weight: bold;
    text-align: center;
  }
  .left-operate {
    border-radius: 0 8px 8px 0;
    margin-right: 5px;
  }
  .right-operate {
    border-radius: 8px 0 0 8px;
    margin-left: 5px;
  }
}
.left-hidden {
  .container:first-child {
    width: 20px;
    overflow: hidden;
    .content {
      display: none;
    }
    .left-operate {
      transform: rotate(180deg);
      border-radius: 8px 0 0 8px;
    }
  }
  .container:last-child {
    .content {
      width: 100%;
    }
    .operate {
      display: none;
    }
  }
}
.right-hidden {
  .container:first-child {
    .content {
      width: 100%;
    }
    .operate {
      display: none;
    }
  }
  .container:last-child {
    width: 20px;
    overflow: hidden;
    .content {
      display: none;
    }
    .right-operate {
      transform: rotate(180deg);
      border-radius: 0 8px 8px 0;
    }
  }
}
::v-deep {
  .table-tool-bar {
    height: 0;
  }
  .mt-form-item {
    margin-bottom: 0;
  }
  .vxe-input,
  .vxe-select {
    width: 100%;
    height: 30px;
    line-height: 30px;
  }
}
</style>
