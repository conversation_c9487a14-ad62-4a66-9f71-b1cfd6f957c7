<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <!-- 自定义查询条件 -->
      <collapse-search
        class="toggle-container"
        :default-expand="false"
        @reset="handleReset"
        @search="handleSearch"
      >
        <mt-form ref="searchFormRef" :model="searchFormModel">
          <mt-form-item prop="unitCode" :label="$t('单位编码')" label-style="top">
            <mt-input
              v-model="searchFormModel.unitCode"
              :show-clear-button="true"
              :placeholder="$t('请输入单位编码')"
            />
          </mt-form-item>
          <mt-form-item prop="unitName" :label="$t('单位名称')" label-style="top">
            <mt-input
              v-model="searchFormModel.unitName"
              :show-clear-button="true"
              :placeholder="$t('请输入单位名称')"
            />
          </mt-form-item>
        </mt-form>
      </collapse-search>
      <!-- 表格 -->
      <sc-table
        ref="sctableRef"
        :loading="loading"
        :is-show-refresh-bth="true"
        :columns="columns"
        :table-data="tableData"
        @refresh="handleSearch"
        @cell-dblclick="handleDbclick"
      />
      <!-- 分页 -->
      <mt-page
        ref="pageRef"
        class="flex-keep custom-page"
        :page-settings="pageSettings"
        :total-pages="pageSettings.totalPages"
        @currentChange="handleCurrentChange"
        @sizeChange="handleSizeChange"
      />
    </div>
  </mt-dialog>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'

export default {
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  components: { ScTable, CollapseSearch },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      searchFormModel: {},
      loading: false,
      tableData: [],
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalRecordsCount: 0,
        totalPages: 1,
        pageSize: 20,
        pageSizes: [20, 50, 100, 500, 1000, 2000]
      },
      pageInfo: {
        size: 20,
        current: 1
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    columns() {
      const defaultColumns = [
        {
          field: 'unitCode',
          title: this.$t('单位编码')
        },
        {
          field: 'unitName',
          title: this.$t('单位名称')
        }
      ]
      return defaultColumns
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.handleSearch()
  },
  methods: {
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    // 查询
    async handleSearch(type) {
      // 解决修改查询条件后，查询结果有数据但未正常显示的问题（type: pageChange/refresh）
      if (!type) {
        this.pageInfo.current = 1
        this.$refs.pageRef.jumpNum = 1
        this.$refs.pageRef.currentPage = 1
      }

      const params = {
        rfxId: this.modalData.rfxId,
        page: this.pageInfo,
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.supplierBiddingCost
        .queryUnitList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []

        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.handleSearch('pageChange')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.handleSearch()
    },
    // 双击行
    handleDbclick(e) {
      this.$emit('confirm-function', e.row)
    },
    confirm() {
      const currow = this.$refs.sctableRef.$refs.xGrid.getCurrentRecord()
      this.$emit('confirm-function', currow)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 5px 0 0 0;
  height: 100%;
  width: 100%;
  .collapse-search-area {
    padding: 0;
  }
  .mt-pagertemplate {
    margin: 10px 0 0 0;
  }
}
</style>
