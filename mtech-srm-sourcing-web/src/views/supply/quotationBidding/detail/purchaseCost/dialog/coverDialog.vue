<template>
  <mt-dialog
    ref="dialog"
    :header="header"
    :buttons="buttons"
    @beforeClose="cancel"
    height="300"
    width="500"
  >
    <div class="dialog-content" style="font-size: 16px; padding-top: 18px">
      {{ $t('是否将历史记录覆盖到本次报价？') }}
    </div>
  </mt-dialog>
</template>

<script>
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('否') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('是') }
        }
      ]
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    confirm() {
      this.$emit('confirm-function')
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
