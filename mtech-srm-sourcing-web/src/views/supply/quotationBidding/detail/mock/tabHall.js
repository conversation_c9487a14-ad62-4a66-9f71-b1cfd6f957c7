import { i18n } from '@/main.js'
import Vue from 'vue'
export const supplyColumnData = [
  {
    field: 'field1',
    headerText: i18n.t('编号')
  },
  {
    field: 'field2',
    headerText: i18n.t('名称')
  },
  {
    field: 'field3',
    headerText: i18n.t('保障金'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div class="action-boxs"><span class="pay-text isPay" v-if="data.field3 == 1">{{ $t('已缴纳') }}</span><span class="pay-text unPay" v-else>{{ $t('未缴纳') }}</span></div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'field4',
    headerText: i18n.t('审核'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div class="action-boxs"><mt-icon name="icon_V" class="checked" v-if="data.field4 == 1" src=""></mt-icon><mt-icon name="icon_X" class="uncheck" v-else></mt-icon></div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'field5',
    headerText: i18n.t('阶段')
  },
  {
    field: 'field6',
    headerText: i18n.t('状态'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div class="action-boxs"><span class="status-text isStatus" v-if="data.field3 == 1">{{ $t('已报价') }}</span><span class="status-text unStatus" v-else>{{ $t('未报价') }}</span></div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'field7',
    headerText: i18n.t('报价次数')
  }
]

export const supplyGridData = [
  {
    field1: '00001',
    field2: '供应商名称1',
    field3: 1,
    field4: 1,
    field5: i18n.t('潜在'),
    field6: 0,
    field7: 1
  },
  {
    field1: '00001',
    field2: '供应商名称1',
    field3: 1,
    field4: 0,
    field5: i18n.t('潜在'),
    field6: 1,
    field7: 1
  },
  {
    field1: '00001',
    field2: '供应商名称1',
    field3: 0,
    field4: 1,
    field5: i18n.t('潜在'),
    field6: 1,
    field7: 1
  },
  {
    field1: '00001',
    field2: '供应商名称1',
    field3: 0,
    field4: i18n.t('是'),
    field5: i18n.t('潜在'),
    field6: 0,
    field7: 1
  },
  {
    field1: '00001',
    field2: '供应商名称1',
    field3: 1,
    field4: 0,
    field5: i18n.t('潜在'),
    field6: 0,
    field7: 1
  },
  {
    field1: '00001',
    field2: '供应商名称1',
    field3: 1,
    field4: 1,
    field5: i18n.t('潜在'),
    field6: 1,
    field7: 1
  }
]

export const rankList = [
  {
    name: i18n.t(
      '甲骨文甲骨文甲骨文甲骨文甲骨文甲骨文甲骨文甲骨文甲骨文甲骨文甲骨文甲骨文甲骨文甲骨文甲骨文甲骨文甲骨文甲骨文甲骨文甲骨文甲骨文甲骨文甲骨文甲骨文'
    ),
    price: '32134000',
    times: 2,
    status: 'up'
  },
  {
    name: i18n.t('神州数码'),
    price: '32134000',
    times: 2,
    status: 'down'
  },
  {
    name: i18n.t('新中大软件'),
    price: '32134000',
    times: 2,
    status: 'hori'
  },
  {
    name: 'SAGE',
    price: '32134000',
    times: 2,
    status: 'up'
  },
  {
    name: i18n.t('全碟'),
    price: '32134000',
    times: 2,
    status: 'up'
  },
  {
    name: 'SAP',
    price: '32134000',
    times: 2,
    status: 'up'
  },
  {
    name: i18n.t('新中大软件'),
    price: '32134000',
    times: 2,
    status: 'hori'
  },
  {
    name: 'SAGE',
    price: '32134000',
    times: 2,
    status: 'up'
  },
  {
    name: i18n.t('全碟'),
    price: '32134000',
    times: 2,
    status: 'up'
  },
  {
    name: 'SAP',
    price: '32134000',
    times: 2,
    status: 'up'
  }
]

export const matierialList = [
  {
    name: '物料名称1',
    price: '3000',
    company: i18n.t('上海欧兴科技'),
    status: 'up'
  },
  {
    name: '物料名称1',
    price: '3000',
    company: i18n.t('上海欧兴科技有限公司'),
    status: 'down'
  },
  {
    name: '物料名称1',
    price: '3000',
    company: i18n.t('上海欧兴科技'),
    status: 'hori'
  },
  {
    name: '物料名称1',
    price: '3000',
    company: i18n.t('上海欧兴科技'),
    status: 'up'
  },
  {
    name: '物料名称1',
    price: '3000',
    company: i18n.t('上海欧兴科技'),
    status: 'up'
  }
]

export const tagList = [i18n.t('一般采购'), i18n.t('框架协议')]
