export const moduleData = {
  code: 200,
  msg: '执行成功',
  data: {
    moduleItems: [
      {
        moduleId: '131576415015497731',
        moduleName: '采购明细',
        moduleType: 14,
        moduleKey: 'b3ba260c-dfd0-11eb-96d2-0242ac130002',
        formType: 0,
        formTemplateId: '',
        formVersion: null,
        parentModuleKey: '',
        containField: 1,
        fieldDefines: [
          {
            recordId: null,
            fieldId: '144683062867374158',
            fieldKey: '57607875-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'projectEndTime',
            fieldGroup: '需求信息',
            fieldName: '项目截止时间',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: 'null'
          },
          {
            recordId: null,
            fieldId: '144683062804459530',
            fieldKey: '3361d694-edf7-11eb-96d2-0242ac130002',
            fieldCode: 'checkUserExt',
            fieldGroup: '人员信息',
            fieldName: '验收人扩展字段',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062808653851',
            fieldKey: '5858a8cd-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'contractInterval',
            fieldGroup: '合同要求',
            fieldName: '合同周期',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062812848146',
            fieldKey: '585fae16-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'contractUnit',
            fieldGroup: '合同要求',
            fieldName: '时间单位',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062817042464',
            fieldKey: '58670083-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'histroyApplyCount',
            fieldGroup: '参考信息',
            fieldName: '历史申购次数',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062821236792',
            fieldKey: '586e5456-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'histroyApplyQuantity',
            fieldGroup: '参考信息',
            fieldName: '历史申购量',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062825431118',
            fieldKey: '5875f48c-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'histroyApplyPrice',
            fieldGroup: '参考信息',
            fieldName: '历史申购金额',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062833819733',
            fieldKey: '587d4930-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'referenceCurrency',
            fieldGroup: '参考信息',
            fieldName: '币种',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062838014042',
            fieldKey: '5884e970-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'checkDept',
            fieldGroup: '人员信息',
            fieldName: '验收部门',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062842208314',
            fieldKey: '588c3c21-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'checkUser',
            fieldGroup: '人员信息',
            fieldName: '验收人',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062846402603',
            fieldKey: '579383f8-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'expectBidPeriodEnd',
            fieldGroup: '需求信息',
            fieldName: '预测采购周期止',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062850596869',
            fieldKey: '5743dbd3-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'projectCode',
            fieldGroup: '需求信息',
            fieldName: '项目代码',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062854791216',
            fieldKey: '574ae166-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'projectName',
            fieldGroup: '需求信息',
            fieldName: '项目名称',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062858985480',
            fieldKey: '575234b1-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'relationProjectId',
            fieldGroup: '需求信息',
            fieldName: '关联项目',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062863179861',
            fieldKey: '57594ccd-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'projectStartTime',
            fieldGroup: '需求信息',
            fieldName: '项目开始时间',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062800265315',
            fieldKey: '584a50a7-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'relationProductName',
            fieldGroup: '物料及收获需求',
            fieldName: '涉及产品名称',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062871568390',
            fieldKey: '5767df22-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'sole',
            fieldGroup: '需求信息',
            fieldName: '是否独家',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062875762694',
            fieldKey: '576f320d-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'soleType',
            fieldGroup: '需求信息',
            fieldName: '独家类型',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062879957044',
            fieldKey: '577686e5-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'soleResource',
            fieldGroup: '需求信息',
            fieldName: '独家货源',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062884151301',
            fieldKey: '578c2f41-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'expectBidPeriodStart',
            fieldGroup: '需求信息',
            fieldName: '预测采购周期起',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062888345682',
            fieldKey: '58011354-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'logisticsWay',
            fieldGroup: '物料及收获需求',
            fieldName: '物流方式',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062892539943',
            fieldKey: '57a22a25-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'budgetCode',
            fieldGroup: '需求信息',
            fieldName: '预算编号',
            fieldType: 0,
            fixed: 1,
            required: 0,
            fieldData: null,
            tableField: 1,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062900928567',
            fieldKey: '57bf628b-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'budgetSubject',
            fieldGroup: '需求信息',
            fieldName: '预算科目',
            fieldType: 0,
            fixed: 1,
            required: 0,
            fieldData: null,
            tableField: 1,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062905122855',
            fieldKey: '57c6c785-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'budgetSubjectTotalPrice',
            fieldGroup: '需求信息',
            fieldName: '科目总额',
            fieldType: 0,
            fixed: 1,
            required: 0,
            fieldData: null,
            tableField: 1,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062909317207',
            fieldKey: '57dcc173-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'budgetCurrency',
            fieldGroup: '需求信息',
            fieldName: '币种',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 1,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062913511467',
            fieldKey: '57e414f9-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'entrySubject',
            fieldGroup: '财务信息',
            fieldName: '入账科目',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062917705818',
            fieldKey: '57eb0590-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'reduction',
            fieldGroup: '财务信息',
            fieldName: '是否抵扣',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062921900080',
            fieldKey: '57f26d85-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'payer',
            fieldGroup: '财务信息',
            fieldName: '付款方',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062926094427',
            fieldKey: '57f9bf5b-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'tradeTerms',
            fieldGroup: '物料及收获需求',
            fieldName: '贸易条款',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062741545055',
            fieldKey: '57ce1c13-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'budgetPriceIncludeTax',
            fieldGroup: '需求信息',
            fieldName: '预算单价（含税）',
            fieldType: 0,
            fixed: 1,
            required: 0,
            fieldData: null,
            tableField: 1,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062670241843',
            fieldKey: '56f2eb02-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'itemCode',
            fieldGroup: '物料信息',
            fieldName: '品项编码',
            fieldType: 0,
            fixed: 1,
            required: 1,
            fieldData: null,
            tableField: 1,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062678630424',
            fieldKey: '56fa3d2b-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'itemName',
            fieldGroup: '物料信息',
            fieldName: '品项名称',
            fieldType: 0,
            fixed: 1,
            required: 1,
            fieldData: null,
            tableField: 1,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062682824741',
            fieldKey: '5701a320-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'skuCode',
            fieldGroup: '物料信息',
            fieldName: 'SKU编码',
            fieldType: 0,
            fixed: 1,
            required: 1,
            fieldData: null,
            tableField: 1,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062687019083',
            fieldKey: '570944d2-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'skuName',
            fieldGroup: '物料信息',
            fieldName: 'SKU名称',
            fieldType: 0,
            fixed: 1,
            required: 1,
            fieldData: null,
            tableField: 1,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062691213357',
            fieldKey: '5717afd4-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'categoryCode',
            fieldGroup: '物料信息',
            fieldName: '品类编码',
            fieldType: 0,
            fixed: 1,
            required: 1,
            fieldData: null,
            tableField: 1,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062695407710',
            fieldKey: '571f3d4b-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'categoryName',
            fieldGroup: '物料信息',
            fieldName: '品类名称',
            fieldType: 0,
            fixed: 1,
            required: 1,
            fieldData: null,
            tableField: 1,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062703796264',
            fieldKey: '571096b3-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'spec',
            fieldGroup: '物料信息',
            fieldName: '规格描述',
            fieldType: 0,
            fixed: 1,
            required: 1,
            fieldData: null,
            tableField: 1,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062707990587',
            fieldKey: '572690f8-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'siteName',
            fieldGroup: '物料信息',
            fieldName: '地点/工厂',
            fieldType: 0,
            fixed: 1,
            required: 0,
            fieldData: null,
            tableField: 1,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062712184865',
            fieldKey: '572dcfd8-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'unitName',
            fieldGroup: '物料信息',
            fieldName: '基本单位',
            fieldType: 0,
            fixed: 1,
            required: 0,
            fieldData: null,
            tableField: 1,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062716379235',
            fieldKey: '573536ce-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'currencyName',
            fieldGroup: '物料信息',
            fieldName: '币种',
            fieldType: 0,
            fixed: 1,
            required: 0,
            fieldData: null,
            tableField: 1,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062720573452',
            fieldKey: '577dd88e-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'quantity',
            fieldGroup: '需求信息',
            fieldName: '申请数量',
            fieldType: 0,
            fixed: 1,
            required: 0,
            fieldData: null,
            tableField: 1,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062724767765',
            fieldKey: '5784dd8c-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'bidQuantity',
            fieldGroup: '需求信息',
            fieldName: '预测采购量',
            fieldType: 0,
            fixed: 1,
            required: 0,
            fieldData: null,
            tableField: 1,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062733156435',
            fieldKey: '57a97c0d-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'budgetPrice',
            fieldGroup: '需求信息',
            fieldName: '预算单价（未税）',
            fieldType: 0,
            fixed: 1,
            required: 0,
            fieldData: null,
            tableField: 1,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062737350755',
            fieldKey: '57b0bbbc-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'budgetTotalPrice',
            fieldGroup: '需求信息',
            fieldName: '预算总价（未税）',
            fieldType: 0,
            fixed: 1,
            required: 0,
            fieldData: null,
            tableField: 1,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062661853266',
            fieldKey: '56ebab79-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'lineNo',
            fieldGroup: '物料信息',
            fieldName: '行号',
            fieldType: 0,
            fixed: 1,
            required: 1,
            fieldData: null,
            tableField: 1,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062745739328',
            fieldKey: '57d570f5-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'budgetTotalPriceIncludeTax',
            fieldGroup: '需求信息',
            fieldName: '预算总价（含税）',
            fieldType: 0,
            fixed: 1,
            required: 0,
            fieldData: null,
            tableField: 1,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062749933572',
            fieldKey: '579ad624-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'requiredDeliveryDate',
            fieldGroup: '需求信息',
            fieldName: '要求交期',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 1,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062754127916',
            fieldKey: '573c86fd-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'bidUnitName',
            fieldGroup: '物料信息',
            fieldName: '采购单位',
            fieldType: 0,
            fixed: 1,
            required: 0,
            fieldData: null,
            tableField: 1,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062758322238',
            fieldKey: '57b8221d-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'budgetRate',
            fieldGroup: '需求信息',
            fieldName: '税率（%）',
            fieldType: 0,
            fixed: 1,
            required: 0,
            fieldData: null,
            tableField: 1,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062762516558',
            fieldKey: '58519144-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'contractType',
            fieldGroup: '合同要求',
            fieldName: '合同类型',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062766710868',
            fieldKey: '5808b40c-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'logisticsPattern',
            fieldGroup: '物料及收获需求',
            fieldName: '物流模式',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062770905175',
            fieldKey: '58100860-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'deliveryGoods',
            fieldGroup: '物料及收获需求',
            fieldName: '收货地点',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062775099398',
            fieldKey: '58175935-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'deliveryAddress',
            fieldGroup: '物料及收获需求',
            fieldName: '收货地址',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062779293740',
            fieldKey: '581eaebd-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'inspectionReq',
            fieldGroup: '物料及收获需求',
            fieldName: '检验要求',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062783488037',
            fieldKey: '58260076-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'SourceCertificationReq',
            fieldGroup: '物料及收获需求',
            fieldName: '货源认证要求',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062787682361',
            fieldKey: '582d2cb1-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'packWay',
            fieldGroup: '物料及收获需求',
            fieldName: '包装方式',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062791876683',
            fieldKey: '583492d6-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'packingSize',
            fieldGroup: '物料及收获需求',
            fieldName: '包装规格',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          },
          {
            recordId: null,
            fieldId: '144683062796070936',
            fieldKey: '583bbe3e-da5c-11eb-96d2-0242ac130002',
            fieldCode: 'packDescribe',
            fieldGroup: '物料及收获需求',
            fieldName: '包装说明',
            fieldType: 0,
            fixed: 0,
            required: 0,
            fieldData: null,
            tableField: 0,
            customer: 0,
            sortValue: null
          }
        ]
      },
      {
        moduleId: '131576415040663645',
        moduleName: '相关文件',
        moduleType: 2,
        moduleKey: 'b3cb5ed2-dfd0-11eb-96d2-0242ac130002',
        formType: 0,
        formTemplateId: '',
        formVersion: null,
        parentModuleKey: '',
        containField: 0,
        fieldDefines: null
      },
      {
        moduleId: '131576415116161104',
        moduleName: '说明',
        moduleType: 3,
        moduleKey: 'b3dc5092-dfd0-11eb-96d2-0242ac130002',
        formType: 0,
        formTemplateId: '',
        formVersion: null,
        parentModuleKey: '',
        containField: 0,
        fieldDefines: null
      }
    ]
  },
  errorStackTrace: null
}

export const detail = {
  code: 200,
  msg: '执行成功',
  data: {
    tenderId: '155100979537182809',
    rfxId: '155099601045618767',
    rfxCode: 'FPX500001202201131502',
    rfxName: '寻源数据017',
    roundId: '155099601079173127',
    roundNo: 1,
    purCompanyId: 0,
    purCompanyCode: null,
    purCompanyName: '',
    customerId: 0,
    customerCode: 0,
    customerName: '',
    bidStartTime: '1640966400000',
    currentTime: '1642556858979',
    bidEndTime: '1642435200000',
    publishTime: null,
    desc: null,
    rounds: [
      {
        tenderId: '155100979537182809',
        roundId: '155099601079173127',
        roundNo: 1
      }
    ]
  },
  errorStackTrace: null
}
export const purchaseDetails = {
  code: 200,
  msg: '执行成功',
  data: {
    records: [
      {
        fieldDataList: [],
        recordId: '155099602366824483',
        docId: '155099601045618767',
        version: null,
        tenantId: '1460789085316702210',
        tenderItemId: '155101150429904982',
        rfxId: '155099601045618767',
        rfxCode: 'FPX500001202201131502',
        purTenantId: '1456440628761219073',
        sourceId: '0',
        sourceHeaderId: '0',
        sourceHeaderCode: '',
        sourceHeaderName: '',
        sourceType: 0,
        businessTypeId: '1456500141976576001',
        businessTypeCode: 'BT001',
        businessTypeName: '一般业务类型',
        rfxItemId: '155099602366824483',
        lineNo: 20,
        itemSiteId: '0',
        itemId: '1476127025444487169',
        itemCode: 'FOSUN-MA-DRAGS-000002',
        itemName: '阿莫西林胶囊',
        skuId: '1480465997822578689',
        skuCode: 'GMA-SKU-001',
        skuName: '大包装感冒灵',
        spec: '药品',
        categoryId: '1476721661484945410',
        categoryCode: '***********',
        categoryName: '中药-中成药',
        siteId: '1476123318258565122',
        siteCode: 'FOSUN-SUZHOU-SITE-20211229',
        siteName: '复星智造苏州工厂',
        stockSite: '',
        reqDescription: '',
        quantity: 1000,
        bidQuantity: 1000,
        requiredDeliveryDate: null,
        unitId: '0',
        unitName: '千米',
        bidUnitId: '',
        bidUnitName: '厘米',
        currencyId: '1460058661154414594',
        currencyName: '人民币',
        purGroupId: '0',
        purGroupCode: '',
        purGroupName: '',
        remark: '',
        packageId: '155101150392156253',
        tenderId: '155100979537182809',
        packageType: 0,
        budgetCode: 0,
        budgetPrice: 0,
        budgetTotalPrice: 0,
        budgetRate: 0,
        budgetSubjectTotalPrice: 0,
        budgetSubject: '',
        budgetPriceIncludeTax: 0,
        budgetTotalPriceIncludeTax: 0,
        fieldVersion: null
      },
      {
        fieldDataList: [],
        recordId: '155099602358435900',
        docId: '155099601045618767',
        version: null,
        tenantId: '1460789085316702210',
        tenderItemId: '155101150350213199',
        rfxId: '155099601045618767',
        rfxCode: 'FPX500001202201131502',
        purTenantId: '1456440628761219073',
        sourceId: '0',
        sourceHeaderId: '0',
        sourceHeaderCode: '',
        sourceHeaderName: '',
        sourceType: 0,
        businessTypeId: '1456500141976576001',
        businessTypeCode: 'BT001',
        businessTypeName: '一般业务类型',
        rfxItemId: '155099602358435900',
        lineNo: 10,
        itemSiteId: '0',
        itemId: '1476126445389021185',
        itemCode: 'FOSUN-MA-DRGAS-00001',
        itemName: '感冒灵颗粒',
        skuId: '1480465997822578689',
        skuCode: 'GMA-SKU-001',
        skuName: '大包装感冒灵',
        spec: '新能源',
        categoryId: '1476721661484945410',
        categoryCode: '***********',
        categoryName: '中药-中成药',
        siteId: '1476123318258565122',
        siteCode: 'FOSUN-SUZHOU-SITE-20211229',
        siteName: '复星智造苏州工厂',
        stockSite: '',
        reqDescription: '',
        quantity: 1000,
        bidQuantity: 1000,
        requiredDeliveryDate: null,
        unitId: '0',
        unitName: '毫米',
        bidUnitId: '',
        bidUnitName: '毫米',
        currencyId: '1460058661154414594',
        currencyName: '人民币',
        purGroupId: '0',
        purGroupCode: '',
        purGroupName: '',
        remark: '',
        packageId: '155101150312464389',
        tenderId: '155100979537182809',
        packageType: 0,
        budgetCode: 0,
        budgetPrice: 0,
        budgetTotalPrice: 0,
        budgetRate: 0,
        budgetSubjectTotalPrice: 0,
        budgetSubject: '',
        budgetPriceIncludeTax: 0,
        budgetTotalPriceIncludeTax: 0,
        fieldVersion: null
      }
    ],
    total: '2',
    size: '10',
    current: '1',
    orders: [
      {
        column: 'create_time',
        asc: false
      }
    ],
    optimizeCountSql: true,
    hitCount: false,
    countId: null,
    maxLimit: null,
    searchCount: true,
    pages: '1'
  },
  errorStackTrace: null
}
