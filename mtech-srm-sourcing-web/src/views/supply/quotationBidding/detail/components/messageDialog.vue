<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    :width="400"
    :height="300"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form class="dialog-mt-form" ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item :label="$t('报价错误')">
          <mt-radio
            v-model="formObject.isError"
            :data-source="[
              {
                label: this.$t('是'),
                value: true
              }
            ]"
          ></mt-radio>
        </mt-form-item>
        <mt-form-item prop="msgDetail" :label="$t('实际价格')">
          <mt-input
            v-model="formObject.msgDetail"
            :placeholder="$t('请输入')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
      <div style="color: red">注：报价错误留言仅有一次填写机会，请谨慎确认</div>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      //v-model获取的值
      formObject: {
        isError: true,
        msgDetail: ''
      },
      //必填项
      formRules: {
        msgDetail: [
          {
            required: true,
            message: this.$t('请输入实际价格'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    //点击确认
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          this.$emit('confirm-function', this.formObject)
        }
      })
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    // padding: 0;
    .dialog-content {
      padding: 20px 0;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 100%;
        }
      }
    }
  }
}
</style>
