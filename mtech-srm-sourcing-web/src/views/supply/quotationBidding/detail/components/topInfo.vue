<template>
  <div>
    <div class="top-info">
      <div
        class="timer-clock"
        v-if="showTimer && quotedPriceData.status === 1 && quotedPriceData.transferStatus !== 43"
      >
        <timer-clock
          :start-time="timerStartTime"
          :end-time="timerEndTime"
          :timer-string="timerString"
          :server-time="timerServerTime"
          v-show="
            showTimer && quotedPriceData.status === 1 && quotedPriceData.transferStatus !== 43
          "
        ></timer-clock>
      </div>
      <div class="lf-wrap">
        <div class="name-wrap">
          <div class="first-line">
            <span class="code">{{ (quotedPriceData && quotedPriceData.rfxCode) || '-' }}</span>
            <!-- <span class="tags tags-1" v-if="detailInfo && detailInfo.businessTypeName"
              >{{ status[0] || '-' }}
            </span> -->
          </div>
          <div class="second-line">
            <div :title="quotedPriceData.rfxName">
              {{ quotedPriceData.rfxName || '' }}
            </div>
            <div>{{ $t('采购组织') }}：{{ quotedPriceData.purOrgName || '-' }}</div>
            <div>{{ $t('执行人') }}：{{ quotedPriceData.purExecutorName || '-' }}</div>
          </div>
        </div>
        <div class="btns-wrap">
          <mt-button @click.native="$router.back()">{{ $t('返回') }}</mt-button>
          <mt-button
            v-if="
              showAptitude &&
              quotedPriceData.bidStatus !== 3 &&
              examineStatus !== null &&
              ![1, 2].includes(examineStatus) &&
              quotedPriceData.joinStatus == 1
            "
            style="margin-left: 20px"
            @click.native="aptitude"
            >{{ $t('提交资质审查') }}</mt-button
          >
          <!-- <mt-button
                    @click.native="waiveButton"
                    v-if="
                      quotedPriceData.status == 1 &&
                      quotedPriceData.joinStatus == 1
                    "
                    >{{ $t("放弃") }}</mt-button
                  > -->
          <mt-button
            @click.native="submitButton"
            v-if="
              !showAptitude &&
              quotedPriceData.status == 1 &&
              quotedPriceData.joinStatus == 1 &&
              (quotedPriceData.submitStatus !== 1 || newPrice === 1) &&
              isRfqOrEndBidding &&
              !showQuotedPrice
            "
            >{{ $t('提交') }}</mt-button
          >
          <mt-button
            v-if="
              showSupplierTenderExport &&
              quotedPriceData.status == 1 &&
              quotedPriceData.joinStatus == 1 &&
              quotedPriceData.sourcingScenarios !== 'LOGISTICS'
            "
            @click.native="supplierTenderExport"
            >{{ $t('报价单导出') }}
          </mt-button>
          <!-- 38, "技术投标中"   41, "商务投标中"(注：非采的商务投标不显示) -->
          <mt-button
            v-if="
              (quotedPriceData.transferStatus == 38 ||
                (quotedPriceData.transferStatus == 41 && quotedPriceData.rfxGeneralType !== 2)) &&
              quotedPriceData.status == 1 &&
              quotedPriceData.joinStatus == 1 &&
              needTecBid == 1
            "
            @click.native="uploadTechFiles"
            >{{ getTechFileTxt }}
          </mt-button>
          <mt-button
            @click.native="Participate(1)"
            v-if="quotedPriceData.status == 1 && quotedPriceData.joinStatus == 0"
            >{{ $t('不参与') }}</mt-button
          >
          <mt-button
            @click.native="Participate(0)"
            v-if="quotedPriceData.status == 1 && quotedPriceData.joinStatus == 0"
            >{{ $t('参与') }}</mt-button
          >
          <mt-button
            v-if="showQuotedPrice && quotedPriceData.sourcingScenarios !== 'trunk_transport_annual'"
            @click.native="quotedPrice"
            icon-css="mt-icons mt-icon-icon_card_punish"
            >{{ $t('报价') }}</mt-button
          >
          <mt-button v-if="showQuotedMsg" @click="quotedMsg">{{ $t('报价留言') }}</mt-button>
          <mt-button
            v-if="
              showQuotedPrice &&
              quotedPriceData.sourcingScenarios === 'trunk_transport_annual' &&
              submitStatus
            "
            @click.native="declineQuotedPrice"
            icon-css="mt-icons mt-icon-icon_card_punish"
            >{{ $t('按照幅度报价') }}</mt-button
          >
        </div>
      </div>
    </div>
    <mt-dialog
      ref="dialog"
      css-class="create-proj-dialog"
      :buttons="buttons"
      :header="$t('按幅度报价')"
      size="small"
      @beforeClose="cancel"
    >
      <div class="dialog-content">
        <mt-form ref="dialogRef" :model="formObject">
          <mt-form-item prop="decline" :label="$t('降幅（%）')" label-style="left">
            <mt-input-number
              :min="0"
              :max="100"
              v-model="decline"
              precision="2"
              :placeholder="$t('请输入降幅%')"
            ></mt-input-number>
          </mt-form-item>
        </mt-form>
        <p style="color: red">
          {{ $t('注：按照单价的降价比例填写，需降10%，则填写10') }}
        </p>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import { utils } from '@mtech-common/utils'
import TimerClock from 'COMPONENTS/SourcingProject/timerClock.vue'

export default {
  components: {
    TimerClock
  },
  props: {
    quotedPriceData: {
      type: Object,
      required: true,
      defaut: () => {
        return {}
      }
    },
    strategyConfig: {
      type: Array,
      required: true,
      defaut: () => {
        return []
      }
    },
    moduleType: {
      type: [String, Number],
      default: () => null
    },
    rfxCountdown: {
      type: Object,
      default: () => ({})
    },
    newPrice: {
      type: Number,
      default: 0
    },
    examineStatus: {
      type: Number,
      default: null
    }
  },
  computed: {
    needTecBid() {
      // 是否需要技术标
      return this.strategyConfig[0]?.needTecBid || 0
    },
    showSupplierTenderExport() {
      return this.quotedPriceData && ['rfq'].includes(this.quotedPriceData.sourcingMode)
    },
    getTechFileTxt() {
      return this.techFileList.length
        ? this.$t('上传技术附件({0})', { 0: this.techFileList.length })
        : this.$t('上传技术附件')
    },
    showAptitude() {
      // 待提交资格预审,资格预审中,资格预审驳回  展示按钮
      let statusList = [10, 11, 13]
      return (
        this.moduleType == 24 &&
        statusList.includes(this.quotedPriceData.summaryStatus) &&
        this.quotedPriceData.joinStatus != 0
      )
    },
    isRfqOrEndBidding() {
      if (this.$route.query.source == 'rfq') {
        //供应商报价，不添加额外的逻辑判断
        return true
      } else {
        //应标时间截止后，再显示该按钮
        return this.quotedPriceData.transferStatus !== 35
      }
    },
    isBidding() {
      return this.$route.query.source == 'bidding_price'
    },
    showQuotedPrice() {
      return (
        this.moduleType == 4 &&
        this.quotedPriceData.joinStatus == 1 &&
        this.$route.query.source == 'bidding_price' &&
        [31, 41].includes(Number(this.quotedPriceData.transferStatus)) &&
        this.quotedPriceData.status == 1
      )
    },
    submitStatus() {
      return this.quotedPriceData?.submitStatus === 1
    }
  },
  data() {
    return {
      showTimer: true,
      timerString: '',
      timerStartTime: 0,
      timerEndTime: 0,
      timerServerTime: 0,
      status: [this.$t('草稿'), this.$t('进行中'), this.$t('已暂停'), this.$t('已关闭')], //单据状态 0:草稿 1:进行中 2:已暂停 3:已关闭
      techFileList: [],
      showQuotedMsg: false,
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      decline: ''
    }
  },
  watch: {
    quotedPriceData() {
      this.initData()
    },
    rfxCountdown() {
      this.getCurrentTurnsInfo()
    }
  },
  mounted() {
    // this.getCurrentTurnsInfo();
    // this.getTechFileList();
  },
  methods: {
    getMessageBoardMyself() {
      this.$API.supplyQdetail
        .getMessageBoardMyselfApi({ rfxId: this.quotedPriceData.rfxId })
        .then((res) => {
          if (res.code === 200) {
            this.showQuotedMsg = !res.data?.msgDetail
          }
        })
    },
    quotedMsg() {
      this.$dialog({
        data: {
          title: this.$t('供方报价留言')
        },
        modal: () => import('./messageDialog.vue'),
        success: (form) => {
          let params = {
            rfxCode: this.quotedPriceData.rfxCode,
            msgDetail: form.msgDetail
          }
          this.$API.supplyQdetail.supplierSaveMessageApi(params).then((res) => {
            if (res.code === 200) {
              this.showQuotedMsg = false
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
            }
          })
        }
      })
    },
    initData() {
      this.getCurrentTurnsInfo()
      this.getTechFileList()
      if (['BTTCL001', 'BTTCL002', 'BTTCL003'].includes(this.quotedPriceData.businessTypeCode)) {
        this.getMessageBoardMyself()
      }
    },
    getCurrentTurnsInfo() {
      if (this.rfxCountdown && this.rfxCountdown?.countDown) {
        this.timerStartTime = new Date().getTime()
        this.timerEndTime = +this.rfxCountdown?.countDown
        this.timerServerTime = +this.rfxCountdown?.currentServerTime
        this.timerString = this.rfxCountdown?.title
        this.showTimer = true
      } else {
        this.showTimer = false
      }
    },
    getTechFileList() {
      this.$API.RfxSupRelConfig.queryTecFileByRfxId({
        docId: this.quotedPriceData.rfxId
      }).then((res) => {
        if (Array.isArray(res?.data)) {
          this.techFileList = res.data
        } else {
          this.techFileList = []
        }
      })
    },
    uploadTechFiles() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "components/Upload/uploaderDialog" */ 'COMPONENTS/Upload/uploaderDialog.vue'
          ),
        data: {
          fileData: utils.cloneDeep(this.techFileList),
          isView: false, // 是否为预览
          required: false, // 是否必须
          title: this.$t('技术附件上传')
        },
        success: (res) => {
          this.handleUploadFiles(res)
        }
      })
    },
    //执行上传文件
    handleUploadFiles(_fileList) {
      let _saveFiles = []
      _fileList.forEach((f) => {
        if (!f?.sysFileId) {
          //如果文件没有sysFileId 说明是新上传文件
          f.docId = this.quotedPriceData.rfxId
          f.sysFileId = f.id
          delete f.id
          _saveFiles.push(f)
        } else {
          _saveFiles.push(f)
        }
      })
      // if (_saveFiles.length < 1) return;
      this.$API.RfxSupRelConfig.uploadTechFiles(_saveFiles).then(() => {
        this.$toast({
          content: this.$t('上传成功'),
          type: 'success'
        })
        this.getTechFileList()
      })
    },
    //放弃
    waiveButton() {
      // console.log("放弃");
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认放弃操作？')
        },
        success: () => {
          this.$emit('mainWaiveButton')
        }
      })
    },
    //提交
    submitButton() {
      // console.log("提交");
      this.$emit('mainSubmitButton')
    },
    //报价单导出
    supplierTenderExport() {
      this.$emit('supplierTenderExport')
    },
    // handleSelectTab(e, item) {
    //   console.log(e, item);
    //   this.$bus.$emit("changeTab", e, item);
    // },

    //点击按钮是否参与
    Participate(index) {
      let number = index + 1
      let parameter = {
        biddingMode: this.quotedPriceData.biddingMode,
        joinStatus: number, //参与 1 不参与 2
        rfxId: this.quotedPriceData.rfxId,
        supplierId: this.quotedPriceData.supplierId
      }
      // 非采
      if (this.quotedPriceData.rfxGeneralType === 2 && number === 2) {
        this.$dialog({
          modal: () => import('./refuseDialog.vue'),
          data: {
            title: this.$t('不参与原因')
          },
          success: (data) => {
            parameter.refusedReason = data
            this.submitData(parameter, number)
          }
        })
      } else {
        // 通采
        let _statusMap = [this.$t('确认参与本次招标项目？'), this.$t('确认不参与本次招标项目？')]
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: _statusMap[index]
          },
          success: () => {
            this.submitData(parameter, number)
          }
        })
      }
    },
    submitData(parameter, number) {
      this.$API.supplyQdetail.tenderJoin(parameter).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.$emit('mainparticipateButton')
          this.$bus.$emit('procurementRefreshPage')
          this.initData()
          if (number == 1) {
            this.$bus.$emit('participate')
          }
        }
      })
    },
    // 提交资质审核
    aptitude() {
      this.$emit(`aptitudeSubmit`)
    },
    // 报价
    quotedPrice() {
      this.$emit(`quotedPrice`)
    },
    declineQuotedPrice() {
      this.$refs['dialog'].ejsRef.show()
    },
    confirm() {
      const params = {
        abateFlag: false,
        decline: this.decline,
        rfxId: this.$route.query?.rfxId
      }
      this.$API.supplyQdetail.declineQuotation(params).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.$refs['dialog'].ejsRef.hide()
          this.$emit('declineQuotedPrice')
        }
      })
      this.$refs['dialog'].ejsRef.hide()
    },
    cancel() {
      this.$refs['dialog'].ejsRef.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.top-info {
  min-height: 100px;
  flex-wrap: nowrap;
  display: flex;
  justify-content: space-between;
  background: rgba(245, 248, 251, 1);

  .lf-wrap {
    flex: 1;
    transition: all 2s ease-in-out;
    background: linear-gradient(rgba(99, 134, 193, 0.06), rgba(99, 134, 193, 0.06)),
      linear-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 1));
    padding: 20px 20px 3px 20px;
    display: flex;
    line-height: 1;

    .name-wrap {
      flex: 1;
      .first-line {
        display: flex;
        align-items: center;
        .code {
          font-size: 20px;
          font-family: DINAlternate;
          font-weight: bold;
          color: rgba(41, 41, 41, 1);
        }
        .tags {
          font-size: 12px;
          font-family: PingFangSC;
          font-weight: 500;
          padding: 4px;
          border-radius: 2px;
          margin-left: 10px;

          &-1 {
            color: rgba(237, 161, 51, 1);
            background: rgba(237, 161, 51, 0.1);
          }
          &-2 {
            color: #6386c1;
            background: rgba(99, 134, 193, 0.1);
          }
        }
      }

      .second-line {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        margin-top: 15px;
        div {
          margin: 10px 10px 0 0;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          @extend .text-ellipsis;

          &:not(:first-of-type) {
            color: rgba(157, 170, 191, 1);
          }
          &:first-of-type {
            max-width: 250px;
          }
        }
      }
    }

    .btns-wrap {
      max-width: 300px;
      flex-shrink: 0;
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-end;
      /deep/ .mt-button {
        margin-right: 0;
        height: 30px;
        button {
          background: transparent;
          border-radius: 4px;
          box-shadow: unset;
          padding: 6px 12px 4px;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(0, 70, 156, 1);
        }
      }
    }
  }
  .timer-clock {
    width: 225px;
    flex-shrink: 0;
  }
}

.top-form-generator {
  background: rgba(245, 248, 251, 1);
  // padding-bottom: 10px;
}

.top-shrink {
  color: #9bb0cb;
  display: flex;
  justify-content: center;
}
</style>
