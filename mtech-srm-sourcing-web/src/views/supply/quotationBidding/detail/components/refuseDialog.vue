<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <p>*{{ $t('不参与原因：') }}</p>
      <mt-input :multiline="true" v-model="refuseReason"></mt-input>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      refuseReason: null,
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    confirm() {
      if (!this.refuseReason) {
        this.$toast({ content: this.$t('请先填写不参与的原因'), type: 'warning' })
        return
      } else {
        this.$emit('confirm-function', this.refuseReason)
      }
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  height: 300px;
  width: 100%;
  p {
    margin-bottom: 20px;
  }
}
</style>
