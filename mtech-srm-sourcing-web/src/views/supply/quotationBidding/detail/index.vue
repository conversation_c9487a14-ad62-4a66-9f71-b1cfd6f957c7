<template>
  <!-- 大厅详情页面 -->
  <div class="full-height hall-detail">
    <!-- 头部 -->
    <top-info-view
      :rfx-countdown="rfxCountdown"
      :quoted-price-data="quotedPriceData"
      :strategy-config="strategyConfig"
      :module-type="moduleType"
      @mainparticipateButton="mainparticipateButton"
      @mainSubmitButton="judgeSave"
      @mainWaiveButton="mainWaiveButton"
      @supplierTenderExport="supplierTenderExport"
      @aptitudeSubmit="aptitudeSubmit"
      @quotedPrice="quotedPrice"
      @declineQuotedPrice="declineQuotedPrice"
      :new-price="newPrice"
      :examine-status="examineStatus"
    ></top-info-view>
    <div class="roundBox" v-if="showRound">
      <MtIcon name="icon_solid_inform" />
      {{ roundInfo }}
    </div>
    <!-- tabs切换 -->
    <mt-tabs
      ref="tabs"
      :tab-id="$utils.randomString()"
      :e-tab="false"
      :data-source="tabSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <div class="middle-wrap">
      <!-- 1. 大厅 -->
      <tab-hall
        class="tab-hall"
        v-if="moduleType == 4 && !isRealTime"
        :quoted-price-data="quotedPriceData"
        tab-from="sup"
      ></tab-hall>
      <real-time-hall
        :kt-flag="ktFlag"
        :is-logistics-trunk="isLogisticsTrunk"
        class="tab-hall"
        v-if="moduleType == 4 && isRealTime"
        tab-from="sup"
        ref="realTimeHall"
        :quoted-price-data="quotedPriceData"
        :strategy-config="strategyConfig"
      >
      </real-time-hall>
      <!-- 2. 采购明细 -->
      <component
        :is="getPurchaseDetailComponent"
        ref="purchaseDetailRef"
        :kt-flag="ktFlag"
        :key="purchaseDetailKey"
        v-show="moduleType == 14"
        v-if="strategyConfig.length > 0 && this.tabSource.length"
        :is-show="moduleType == 14"
        :submit-field="submitField"
        :parent-required="parentRequired"
        :child-required="childRequired"
        :field-defines="getModulesFields(14, 'parentColumns')"
        :child-fields="getModulesFields(14, 'childColumns')"
        :field-structures="getModulesFields(14, 'structures')"
        :quoted-price-data="quotedPriceData"
        :strategy-config="strategyConfig"
        :rfx-countdown="rfxCountdown"
        @mainparticipateButton="mainparticipateButton"
        :currency-list="currencyList"
        get-data-url="getSupplierTenderList"
        :tax-list="taxList"
        :pur-unit-list="purUnitList"
        :new-price.sync="newPrice"
      ></component>
      <!-- 3. 相关文件 -->
      <tab-documents
        v-show="moduleType == 2"
        :quoted-price-data="quotedPriceData"
        :new-price.sync="newPrice"
      ></tab-documents>
      <!-- 4. 说明 -->
      <tab-explain v-show="moduleType == 3" :remark="quotedPriceData.desc"></tab-explain>
      <!-- 5. 项目计划 -->
      <!-- <tab-project-plan
          v-else-if="tabIndex == 4"
        ></tab-project-plan> -->
      <!-- 5. 报价 -->
      <tab-quoted-price
        :quoted-price-data="quotedPriceData"
        v-show="moduleType == -2"
      ></tab-quoted-price>
      <!-- 资质审查 -->
      <tab-review-qualification
        v-if="moduleType == 24"
        ref="qualification"
        :quoted-price-data="quotedPriceData"
        :examine-status.sync="examineStatus"
      ></tab-review-qualification>
      <!-- 基础信息 -->
      <basicInfo v-if="moduleType == 999" :detail-info="quotedPriceData" />
    </div>
    <!-- 策略报告 -->
    <strategy v-if="moduleType == 15" :detail-info="quotedPriceData" />
  </div>
</template>

<script>
import { utils } from '@mtech-common/utils'
import { TEC_BID_ING } from '@/constants'
import MixIn from './utils/columnMixin.js'
import moduleMixIn from 'ROUTER_PURCHASE_RFX/detail/config/mixin.js'
import { download, getHeadersFileName } from '@/utils/utils'
// 默认看板小，中间内容大
export default {
  inject: ['reload'],
  mixins: [MixIn, moduleMixIn],
  components: {
    //顶部详情
    topInfoView: () => import('./components/topInfo.vue'),
    //大厅
    tabHall: () =>
      import(/* webpackChunkName: "router/common/tabs/hall" */ 'ROUTER_COMMON/tabs/hall'),
    //实时竞价大厅
    realTimeHall: () =>
      import(/* webpackChunkName: "router/common/tabs/hall" */ 'ROUTER_COMMON/tabs/realTimeHall'),
    //相关文件
    tabDocuments: () =>
      import(
        /* webpackChunkName: "router/supply/quotationBidding/detail/tabs/documents" */ './pages/documents/index.vue'
      ),
    // 说明
    tabExplain: () =>
      import(
        /* webpackChunkName: "router/supply/quotationBidding/detail/tabs/tabExplain" */ './pages/tabExplain.vue'
      ),
    //报价
    tabQuotedPrice: () =>
      import(
        /* webpackChunkName: "router/supply/quotationBidding/detail/tabs/tabQuotedPrice" */ './pages/tabQuotedPrice.vue'
      ),
    //采购明细
    // tabPurchaseDetail: () => import('./pages/purchaseDetails/index.vue'),
    //资质审查
    tabReviewQualification: () => import('./pages/reviewQualification/index.vue'),
    // 策略报告
    strategy: () => import('./pages/strategy/strategy.vue'),
    // 基础信息
    basicInfo: () => import('./pages/basicInfo/index.vue')
  },
  data() {
    return {
      ktFlag: 0,
      //主数据
      quotedPriceData: {},
      isKanbanBigger: false, // 左边看板更大
      moduleType: -1, // 当前选中的tab
      tabSource: [],
      submitField: [], //可以编辑数组
      listData: null,
      rfxCountdown: null, // 倒计时信息
      roundInfo: '',
      newPrice: 0, // 重新报价 0 否 1
      timer: null,
      examineStatus: null,
      strategyConfig: [],
      purchaseDetailKey: new Date().getTime()
      // isLogisticsTrunk: false
    }
  },
  async mounted() {
    await this.getKtFlag()
    this.$bus.$on('changeTab', (e, item) => {
      console.log(item)
      this.moduleType = item.moduleType
    })
    await this.initDictItems()
    await Promise.all([
      this.initTaxList(),
      this.initCurrencyList(),
      this.initPurUnitList(),
      this.initRfxCountdown()
    ]).catch(() => {})
    await this.getData(this.rfxId, this.biddingMode)

    this.getTabs(this.rfxId, this.biddingMode)
    this.$bus.$on(`updateRound`, (info) => {
      if (info) {
        this.roundInfo = info
      }
    })
    this.$bus.$on(`updateTime`, () => {
      this.initRfxCountdown()
    })
    this.$API.rfxSupRel
      .getSupRelBidList({
        id: this.$route.query.rfxId
      })
      .then((res) => {
        res?.data && (this.examineStatus = res.data.examineStatus)
      })
    this.timer = setInterval(() => {
      // (34,"定标中"), (20, "已提交定点"),(0, "立项准备"),(100, "已完成");(40, "技术评标中"),(43, "商务评标中"),
      let statusArr = [34, 20, 0, 100, 40, 43]
      if (
        !statusArr.includes(this.quotedPriceData?.transferStatus) &&
        this.detailInfo?.status == 1
      ) {
        this.initRfxCountdown()
      }
    }, 10000)
    // 根据rfxId查询策略信息
    await this.initStrategyConfigInfo()
  },
  beforeDestroy() {
    this.$bus.$off('updateTime')
    clearInterval(this.timer)
    this.timer = null
  },
  deactivated() {
    this.$bus.$off('procurementRefreshPage')
    clearInterval(this.timer)
    this.timer = null
  },
  computed: {
    rfxId() {
      return this.$route.query.rfxId
    },
    biddingMode() {
      return this.$route.query.biddingMode
    },
    isRealTime() {
      return this.$route.query.source == 'bidding_price'
    },
    showRound() {
      return this.$route.query.source == 'bidding_price' && this.moduleType == 4 && this.roundInfo
    },
    getModulesFields() {
      return (moduleType, key) => {
        let keyMap = {
          parentColumns: 'parentFields',
          childColumns: 'childFields',
          structures: 'structures'
        }
        // TODO:所有场景尚未改造完成，兼容处理
        let _key =
          this.quotedPriceData.sourcingScenarios === 'LOGISTICS' ||
          // this.quotedPriceData.sourcingScenarios === 'COMMON' || //切换333 切换时候删除COMMON
          this.quotedPriceData.sourcingScenarios === 'odmin' ||
          this.quotedPriceData.sourcingScenarios === 'smt' ||
          this.quotedPriceData.sourcingScenarios === 'sea_transport_annual' ||
          this.quotedPriceData.sourcingScenarios === 'railway_transport_annual' ||
          this.quotedPriceData.sourcingScenarios === 'trunk_transport_annual'
            ? keyMap[key]
            : key
        let item = this.tabSource.find((e) => e.moduleType === moduleType)
        return item && item[_key] ? item[_key] : []
      }
    },
    isLogisticsTrunk() {
      return this.quotedPriceData.sourcingScenarios === 'trunk_transport_annual'
    }
  },
  methods: {
    getPurchaseDetailComponent() {
      if (this.quotedPriceData.sourcingScenarios === 'STAGE') {
        return import('./pages/purchaseDetails/modules/step/index.vue')
      } else if (this.quotedPriceData.sourcingScenarios === 'LOGISTICS') {
        return import('./pages/purchaseDetails/modules/logistics/index.vue')
      } else if (
        ['HIERARCHY_CKD', 'HIERARCHY_MOLD', 'cost_factor'].includes(
          this.quotedPriceData.sourcingScenarios
        )
      ) {
        return import('./pages/purchaseDetails/index.vue')
        // } else if (['odmin', 'smt', 'COMMON'].includes(this.quotedPriceData.sourcingScenarios)) {
      } else if (
        [
          'odmin',
          'smt',
          'sea_transport_annual',
          'railway_transport_annual',
          'trunk_transport_annual'
        ].includes(this.quotedPriceData.sourcingScenarios)
      ) {
        //切换333 切换时候删除COMMON
        return import('./pages/purchaseDetails/modules/general/index.vue')
      }
      return import('./pages/purchaseDetails/index.vue')
    },
    async getKtFlag() {
      await this.$API.rfxList
        .supplierGetKtFlag({ companyCode: this.$route.query.companyCode })
        .then((res) => {
          if (res.code == 200) {
            this.ktFlag = res.data
            sessionStorage.setItem('purDetailKtFlag', res.data)
          }
        })
    },
    /**
     * 根据rfxId查询策略信息
     */
    async initStrategyConfigInfo() {
      await this.$API.supplierTender
        .getStrategys({
          sourcingMode: this.$route.query.source,
          rfxId: this.$route.query.rfxId
        })
        .then((res) => {
          this.strategyConfig = res?.data
        })
        .catch(() => {})
    },
    // 初始化倒计时信息
    async initRfxCountdown() {
      if (!this.rfxId) return
      const res = await this.$API.supplyQdetail
        .getRfxCountdown({
          rfxId: this.rfxId
        })
        .catch(() => {})
      if (res?.data) {
        this.rfxCountdown = res.data
      }
    },
    async initTaxList() {
      const res = await this.$API.pur
        .getRates({
          rfxId: this.rfxId
        })
        .catch(() => {})
      if (res) {
        this.taxList = res.data
      }
    },
    // 币种编码
    async initCurrencyList() {
      const res = await this.$API.pur
        .getCurrencys({
          rfxId: this.rfxId
        })
        .catch(() => {})
      if (res) {
        this.currencyList = res.data
      }
    },
    // 采购单位
    async initPurUnitList() {
      const res = await this.$API.pur
        .getPurUnits({
          rfxId: this.rfxId
        })
        .catch(() => {})
      if (res) {
        this.purUnitList = res.data
      }
    },
    // 初始化字典数据
    async initDictItems() {
      const tasks = [
        'TradeClause', // 贸易条款 tradeClauseNameData
        'DELIVERY_PLACE', // 直送地 deliveryPlaceData
        'TransportMode', // 物流方式 shippingMethodNameData
        'START-PORT', // 起始港
        'DESTINATION-PORT' // 目的港
      ].map((dictCode) => this.$API.masterData.dictionaryGetList({ dictCode }))
      const result = await Promise.all(tasks).catch((err) => {
        console.error(err)
      })
      if (!result) {
        return
      }
      this.dictItems = result.map((e) => e.data).flat()
    },
    handleSelectTab(e, item) {
      console.error(e, '=======')
      this.moduleType = item.moduleType
      if (item.moduleType == '14') {
        this.purchaseDetailKey = new Date().getTime()
      }
    },

    //$emit点击参与
    mainparticipateButton(flag) {
      this.getData(this.rfxId, this.biddingMode)
      flag !== 'getDataOnly' && this.getTabs(this.rfxId, this.biddingMode)
    },
    // 报价单导出
    async supplierTenderExport() {
      if (
        ['sea_transport_annual', 'railway_transport_annual', 'trunk_transport_annual'].includes(
          this.quotedPriceData.sourcingScenarios
        )
      ) {
        // 海运物流报价单导出
        await this.handleLogisticsExport()
        return
      }
      // 31为报价中 32为议价中
      // 报价阶段取当前报价，议价阶段取议价
      this.$store.commit('startLoading')
      const transferStatus = this.quotedPriceData?.transferStatus
      let buffer = await this.$API.supplierTender
        .export({
          rfxId: this.rfxId,
          // 报价标识 0 当前报价 1 历史报价 2 议价
          tabType: transferStatus === 31 ? 0 : transferStatus === 32 ? 2 : 1,
          queryBuilderDTO: {
            page: {
              current: 1,
              size: 200
            }
          }
        })
        .catch(() => {})
      buffer?.data && (buffer = buffer.data) // "@mtech-common/http" 1.0.0 版本被挂载到 data, 0.15.1 版本是直接返回
      if (buffer instanceof Blob) {
        if (buffer.type === 'application/json') {
          try {
            const json = JSON.parse(await buffer.text())
            if (json.msg) {
              this.$toast({ content: json.msg, type: 'warning' })
            }
          } catch (error) {
            console.warn(error)
          }
        } else if (buffer.type === 'application/pdf') {
          const url = URL.createObjectURL(buffer)
          window.open(url)
        }
      }
      this.$store.commit('endLoading')
    },
    // 物流报价单导出
    async handleLogisticsExport() {
      this.$store.commit('startLoading')
      const transferStatus = this.quotedPriceData?.transferStatus
      await this.$API.supplierTender
        .logisticsExport({
          rfxId: this.$route.query?.rfxId,
          // 报价标识 0 当前报价 1 历史报价 2 议价
          tabType: transferStatus === 31 ? 0 : transferStatus === 32 ? 2 : 1,
          sourcingObjType: 'sea_transport_annual',
          queryBuilderDTO: {
            page: {
              current: 1,
              size: 200
            }
          }
        })
        .then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .catch(() => {})
    },
    //$emit点击提交
    judgeSave() {
      if (this.moduleType == 14) {
        // let newList =
        //   this.$refs.purchaseDetailRef.$refs.templateRef
        //     .getCurrentTabRef()
        //     .grid?.getCurrentViewRecords() ?? [];
        // if (
        //   JSON.stringify(this.$refs.purchaseDetailRef.oldList) !==
        //   JSON.stringify(newList)
        // ) {
        //   this.judgeDialog(true);
        // } else {
        //   this.judgeDialog();
        // }
        this.judgeDialog() //采购明细Tab，保存校验
      } else if (this.moduleType == 24) {
        let newList =
          this.$refs.qualification.$refs.templateRef
            .getCurrentTabRef()
            .grid?.getCurrentViewRecords() ?? []
        if (JSON.stringify(this.$refs.qualification.oldList) !== JSON.stringify(newList)) {
          this.judgeDialog(true)
        } else {
          this.judgeDialog()
        }
      } else {
        this.mainSubmitButton()
      }
    },
    judgeDialog(isNotSave) {
      if (isNotSave) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t(`当前表格内容未保存，是否继续？`)
          },
          success: () => {
            this.mainSubmitButton()
          }
        })
      } else {
        this.mainSubmitButton()
      }
    },
    // 处理选中的数据
    handleSelectData(_selectGridRecords) {
      if (_selectGridRecords?.length < 0) return []

      if (_selectGridRecords[0]?.itemGroupId === '0' || !_selectGridRecords[0]?.stepNum)
        return _selectGridRecords
      let _current = this.$refs.purchaseDetailRef.$refs.templateRef.getCurrentTabRef()
      let _dataSource = _current.grid.dataSource
      let _checkList = _selectGridRecords.map((item) => item.itemGroupId)
      let _itemGroupIdList = Array.from(new Set(_checkList))
      let list = []
      _dataSource.forEach((item) => {
        if (_itemGroupIdList.includes(item.itemGroupId)) {
          list.push(item)
        }
      })
      return list
    },
    mainSubmitButton() {
      // 切换333 切换时候删除COMMON
      if (
        // ['STAGE', 'LOGISTICS', 'odmin', 'smt', 'COMMON'].includes(
        [
          'STAGE',
          'LOGISTICS',
          'odmin',
          'smt',
          'sea_transport_annual',
          'railway_transport_annual',
          'trunk_transport_annual'
        ].includes(this.quotedPriceData.sourcingScenarios)
      ) {
        //阶梯提交
        this.stepMainSubmitButton()
      } else {
        this.generalMainSubmitButton()
      }
    },
    async generalMainSubmitButton() {
      this.$refs.purchaseDetailRef.endEdit()
      let actionObj = await this.$refs.purchaseDetailRef.getActionObj()
      let originObj = utils.cloneDeep(actionObj)
      let _priceControl = this.strategyConfig[0]['priceControl']
      if (_priceControl) {
        //如果设置了报价规则，才执行。未设置报价规则，跳过
        let _current = this.$refs.purchaseDetailRef.$refs.templateRef.getCurrentTabRef()
        let _selectGridRecords = []
        _current.gridRef.dataSource.forEach((item) => {
          if (item.customChecked) {
            _selectGridRecords.push(item)
          }
        })
        _selectGridRecords = this.handleSelectData(_selectGridRecords)
        if (this.$refs.purchaseDetailRef.moduleType === 2) {
          //议价Tab，必须执行勾选数据
          if (_selectGridRecords.length < 1) {
            this.$toast({
              content: this.$t('请勾选您需要提交报价的行'),
              type: 'warning'
            })
            return
          } else {
            originObj = utils.cloneDeep(_selectGridRecords)
            actionObj = this.$refs.purchaseDetailRef.serializeSaveParams(
              utils.cloneDeep(_selectGridRecords)
            )
          }
        } else if (this.$refs.purchaseDetailRef.moduleType === 0) {
          //无限制   首次整单报价(非首次，使用勾选数据)
          if (
            _priceControl === 'unlimited' ||
            (_priceControl === 'first_all' && this.quotedPriceData.firstPrice == 0)
          ) {
            //firstPrice 1 首次 0 非首次
            if (_selectGridRecords.length < 1) {
              this.$toast({
                content:
                  _priceControl === 'unlimited'
                    ? this.$t('本次寻源不限制报价行数，请勾选您需要提交报价的行')
                    : this.$t('本次报价无限制，您可以报任意多行，请勾选您需要提交报价的行'),
                type: 'warning'
              })
              return
            } else {
              originObj = utils.cloneDeep(_selectGridRecords)
              actionObj = this.$refs.purchaseDetailRef.serializeSaveParams(
                utils.cloneDeep(_selectGridRecords)
              )
            }
          } else {
            originObj = utils.cloneDeep(actionObj)
            actionObj = this.$refs.purchaseDetailRef.serializeSaveParams(utils.cloneDeep(actionObj))
          }
        }
      } else {
        // 如果未配置过‘报价规则’，报价限制走无限制
        // let _current =
        //   this.$refs.purchaseDetailRef.$refs.templateRef.getCurrentTabRef();
        // let _selectGridRecords = _current.gridRef.getMtechGridRecords();
        // if (_selectGridRecords.length < 1) {
        //   this.$toast({
        //     content: this.$t("请勾选您需要提交报价的行"),
        //     type: "warning",
        //   });
        //   return;
        // } else {
        //   actionObj = this.$refs.purchaseDetailRef.serializeSaveParams(
        //     utils.cloneDeep(_selectGridRecords)
        //   );
        // }
        originObj = utils.cloneDeep(actionObj)
        actionObj = this.$refs.purchaseDetailRef.serializeSaveParams(utils.cloneDeep(actionObj))
      }

      const transferStatus = this.quotedPriceData?.transferStatus
      // 31 报价中, 32 议价中, 41 商务投标中  这几个状态才校验必填
      if ([31, 32, 41].includes(transferStatus)) {
        let { checked, checkedMsg } = this.$refs.purchaseDetailRef.checkRequiredFields(originObj)
        if (!checked) {
          this.$toast({
            content: `采购明细Tab，${checkedMsg}`,
            type: 'error'
          })
          return
        }
      }
      const newPrice = this.newPrice
      let tabsNumber = this.$refs.purchaseDetailRef.moduleType
      if (this.quotedPriceData.bidStatus != 3) {
        if (actionObj.length > 0 || this.quotedPriceData.transferStatus === TEC_BID_ING) {
          let rfxId = this.quotedPriceData.rfxId
          let tenantId = this.quotedPriceData.tenantId
          let arr = []
          //统一arr数组length长度
          for (let i = 0; i < actionObj.length; i++) {
            arr.push({})
          }
          //arr对象内添加code
          for (let i = 0; i < arr.length; i++) {
            for (let j = 0; j < this.submitField.length; j++) {
              let FieldCode = this.submitField[j].fieldCode
              arr[i][FieldCode] = actionObj[i][FieldCode]
              arr[i][FieldCode] = actionObj[i]['biddingItemDTO'][FieldCode]
            }
          }

          for (let i = 0; i < arr.length; i++) {
            arr[i].biddingId = actionObj[i]['biddingItemDTO'].biddingId
              ? actionObj[i]['biddingItemDTO'].biddingId
              : null
            arr[i].biddingItemId = actionObj[i]['biddingItemDTO'].biddingItemId
              ? actionObj[i]['biddingItemDTO'].biddingItemId
              : null
            arr[i].rfxItemId = actionObj[i].rfxItemId ? actionObj[i].rfxItemId : null
            arr[i].itemStages = actionObj[i].itemStages ? actionObj[i].itemStages : null
          }
          let param1 = {}
          if (tabsNumber == 0 || tabsNumber == 1) {
            param1 = {
              abateFlag: false,
              bidPriceItems: arr,
              rfxId: rfxId,
              submitStatus: 1,
              tenantId: tenantId
            }
          } else if (tabsNumber == 2) {
            param1 = {
              abateFlag: true,
              bidPriceItems: arr,
              rfxId: rfxId,
              submitStatus: 1,
              tenantId: tenantId,
              newPrice
            }
          }
          if (this.quotedPriceData.rfxGeneralType === 2 && param1.submitStatus === 1) {
            const res = await this.$API.supplyQdetail.getPriceFloatMsg(param1)
            if (res.code === 200 && res.data.quoteFloatFlag) {
              this.$dialog({
                data: {
                  title: this.$t('提示'),
                  message: this.$t('当前报价与上次报价浮动达到10%，是否确定提交报价？')
                },
                success: () => {
                  this.$API.supplyQdetail.priceSave(param1).then((res) => {
                    if (res.code == 200) {
                      this.$toast({
                        content: this.$t('操作成功'),
                        type: 'success'
                      })
                      this.reload()
                    }
                  })
                }
              })
              return
            }
          }
          this.$API.supplyQdetail.priceSave(param1).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
              this.reload()
            }
          })
        } else {
          this.$toast({
            content: this.$t('没有编辑数据不能保存'),
            type: 'warning'
          })
          return
        }
      } else {
        this.$toast({ content: this.$t('已放弃不能提交'), type: 'warning' })
      }
    },
    async stepMainSubmitButton() {
      this.$refs.purchaseDetailRef.handleSubmit()
    },
    //$emit点击放弃
    mainWaiveButton() {
      this.$refs.purchaseDetailRef.endEdit()
      let rfxId = this.quotedPriceData.rfxId

      let param1 = {
        abateFlag: false,
        rfxId: rfxId,
        submitStatus: 2
      }
      console.log(param1)
      this.$API.supplyQdetail.priceSave(param1).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          // this.getData(this.rfxId, this.biddingMode);
          // this.$refs.purchaseDetailRef.$refs.templateRef.refreshCurrentGridData();
          this.$router.go(0)
        }
      })
    },

    // 获取详情
    async getData(rfxId = '', biddingMode = '') {
      const res = await this.$API.supplyQdetail.tenderDetail(rfxId, biddingMode).catch(() => {})
      if (res) {
        this.quotedPriceData = res.data
      }
    },
    // 获取tabs数据
    // mt_supplier_rfx_item_ext
    // itemExtMap
    getTabs(rfxId = '', biddingMode = '') {
      this.$API.supplyQdetail.getTabs(rfxId, biddingMode).then((res) => {
        this.listData = res
        this.processData()
        this.$refs.tabs.activeTab = 0
      })
    },

    processData() {
      this.tabSource = [
        {
          title: this.$t('基础信息'),
          moduleType: 999
        }
      ]
      let listData = this.listData
      if (listData.data && listData.data.moduleItems) {
        listData.data.moduleItems.forEach((e) => {
          if (![19].includes(e.moduleType)) {
            // 处理采购明细中的动态列
            this.defineModuleFields(e)
            // 目前所有流程尚未改造完，改造完成取消下面方法
            if (
              this.quotedPriceData.sourcingScenarios !== 'LOGISTICS' &&
              // this.quotedPriceData.sourcingScenarios !== 'COMMON' && //切换333 切换时候删除COMMON
              this.quotedPriceData.sourcingScenarios !== 'odmin' &&
              this.quotedPriceData.sourcingScenarios !== 'smt' &&
              this.quotedPriceData.sourcingScenarios !== 'sea_transport_annual' &&
              this.quotedPriceData.sourcingScenarios !== 'railway_transport_annual'
            ) {
              this.defineModuleColumns(e)
            }
            this.tabSource.push({
              title: this.$t(e.moduleName),
              ...e
            })
          }
        })
      }
      if (
        !this.tabSource.find((e) => e.moduleType === 4) &&
        ['invite_bids', 'bidding_price'].includes(this.quotedPriceData.sourcingMode)
      ) {
        this.tabSource.splice(0, 0, {
          title: this.$t('大厅'),
          moduleType: 4
        })
      }

      // 资质审查
      if (this.quotedPriceData.examineOpen !== 1) {
        this.tabSource = this.tabSource.filter((e) => e.moduleType !== 24)
      }
      this.moduleType = this.tabSource[0].moduleType
      // 待资质审查默认进资质审查tab 或者保证金驳回
      if (this.quotedPriceData.summaryStatus == 10 || this.quotedPriceData?.moneyStatus === 8) {
        this.tabSource.forEach((v, i) => {
          if (v.moduleType == 24) {
            this.moduleType = 24
            this.$nextTick(() => {
              this.$refs.tabs.activeTab = i
            })
          }
        })
      } else {
        // 非资质审查，默认进入采购明细tab
        if (this.$route.query.source !== 'rfq') {
          return
        }
        this.tabSource.forEach((v, i) => {
          if (v.moduleType == 14) {
            this.moduleType = 14
            this.$nextTick(() => {
              this.$refs.tabs.activeTab = i
            })
          }
        })
      }
      console.log('当前详情页，Tab数据', this.tabSource)
    },
    aptitudeSubmit() {
      this.$refs.qualification.handSubmitEvent()
    },
    quotedPrice() {
      this.$refs.realTimeHall.quotedPrice()
    },
    declineQuotedPrice() {
      this.reload()
    }
  }
}
</script>

<style lang="scss" scoped>
// 主内容部分，按1440宽计算的
$mainMinWidth: 1224px; // 再小顶部的按钮那边就会换行了
$mainMinHeight: 668px;
.mt-tabs {
  background-color: transparent;
  /deep/.mt-tabs-container {
    background-color: transparent;
  }
}
.hall-detail {
  width: 100%;
  padding: 20px 0 0 0;
  display: flex;
  flex-direction: column;
  min-width: 1200px;
}
.middle-wrap {
  width: 100%;
  min-width: $mainMinWidth;
  flex: 1;
  padding: 0px 0px 10px 10px;
  .tab-hall {
    height: 100%;
    min-height: $mainMinHeight;
  }
}

/deep/ .e-table .e-editcell .mt-input-number input {
  width: 100% !important;

  &[readonly] {
    padding-right: 0 !important;
  }
}
.roundBox {
  width: 100%;
  padding: 10px;
  background: rgba(237, 161, 51, 1);
  border-radius: 4px;
  color: white;
}
</style>
