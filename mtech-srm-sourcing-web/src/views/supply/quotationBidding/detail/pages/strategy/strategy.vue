<template>
  <div class="full-height">
    <div class="round-container" v-for="(strategyConfigRow, index) in strategysInfo" :key="index">
      <!-- headerForm -->
      <strategyForm
        v-if="showTagCurrentRound && index === 0"
        :ref="'headerForm' + index"
        :strategy-config="strategyConfigRow"
        :rfx-strategy-detail-items="getRfxStrategyDetailItems(strategyConfigRow, true)"
        :form-field-config="strategyConfig"
      />
      <strategyForm
        :ref="'appForm' + index"
        :strategy-config="strategyConfigRow"
        :form-field-config="strategyConfig"
        :rfx-strategy-detail-items="getRfxStrategyDetailItems(strategyConfigRow, false)"
      >
        <span class="tag-current-round" v-if="showTagCurrentRound">
          {{ $t(`第${strategyConfigRow.roundNo || '1'}轮`) }}
        </span>
      </strategyForm>
    </div>
  </div>
</template>

<script>
import { HEADER_FIELD, getStrategyConfig } from 'ROUTER_PURCHASE_RFX/config/strategyConfig'

export default {
  components: {
    strategyForm: () => import('./components/strategyForm.vue')
  },
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      loading: false,
      strategysInfo: [],
      strategyConfig: getStrategyConfig()
    }
  },
  computed: {
    showTagCurrentRound() {
      const result =
        this.strategysInfo.length > 0 &&
        this.getRfxStrategyDetailItems(this.strategysInfo[0], true).find(
          (e) => e.strategyCode === 'roundCount'
        )
      return result
    }
  },
  async mounted() {
    this.loading = true
    this.$store.commit('startLoading')
    // 根据rfxId查询策略信息
    await this.initStrategyConfigInfo()
    this.$store.commit('endLoading')
  },
  methods: {
    /**
     * 根据rfxId查询策略信息
     */
    async initStrategyConfigInfo() {
      const reqFn = this.$API.supplierTender.getStrategys
      const strategyConfigRes = await reqFn({
        sourcingMode: this.$route.query.source,
        rfxId: this.$route.query.rfxId
      }).catch(() => {})
      if (Array.isArray(strategyConfigRes?.data)) {
        this.strategysInfo = this.strategyConverter(strategyConfigRes.data)
      }
    },
    // 获取可用的表单配置
    getRfxStrategyDetailItems(strategy, isHeader) {
      if (!Array.isArray(strategy.strategyConfigDetails)) {
        return []
      }
      const showTagCurrentRound = strategy.strategyConfigDetails.find(
        (e) => e.strategyCode === 'roundCount'
      )
      let result = strategy.strategyConfigDetails.map((e) => {
        e.editEnable = 0
        return e
      })

      if (showTagCurrentRound) {
        result = result.filter(
          (e) =>
            e.enableStatus &&
            e.supplierVisible &&
            (isHeader
              ? HEADER_FIELD.includes(e.strategyCode)
              : !HEADER_FIELD.includes(e.strategyCode))
        )
      }
      return result
    },
    // 统一转化为 string 类型, 因为 defaultValue 也是 string
    strategyConverter(list) {
      if (list && Array.isArray(list)) {
        const timeFields = this.getTimeFields()
        list.forEach((obj) => {
          for (const key of Object.keys(obj)) {
            // 后端空的时间戳会返回 0 而不是 null
            if (timeFields.includes(key) && Number(obj[key]) === 0) {
              obj[key] = null
            }
            if (typeof obj[key] === 'number') {
              obj[key] = '' + obj[key]
            }
          }
        })
      }
      return list
    },
    // 轮次相关字段
    getRoundFields() {
      return Object.keys(getStrategyConfig()).filter((field) => !HEADER_FIELD.includes(field))
    },
    // 获取时间字段
    getTimeFields() {
      return Object.keys(this.strategyConfig).filter(
        (e) => this.strategyConfig[e].type === 'datetime' && this.strategyConfig[e]['time-stamp']
      )
    }
  }
}
</script>

<style scoped lang="scss">
.toolbar {
  padding: 20px;
  display: flex;
  .svg-option-item:hover {
    color: #707b8b;
    color: var(--plugin-tb-tool-item-hover-color);
  }
}
.round-container {
  background: #fff;
  border: 1px solid #e8e8e8;

  .tag-current-round {
    background-color: #6386c1;
    border-radius: 10px;
    padding: 5px 20px;
    color: #fff;
    display: inline-block;
    margin-bottom: 20px;
  }

  /deep/ .strategy-form:not(:first-child) {
    border-top: solid 1px rgb(232, 232, 232);
  }

  /deep/ .e-input-group.e-error {
    border-bottom-color: rgba(0, 0, 0, 0.42);
    &::before,
    &::after {
      background: transparent !important;
    }
  }
}
</style>
