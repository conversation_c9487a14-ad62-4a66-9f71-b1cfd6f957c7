<template>
  <div class="evaluation-container">
    <!-- 顶部banner -->
    <div class="banner-line mt-flex">
      <div class="left-part mt-flex">
        <div class="label-title">{{ $t('当前轮次：') }}</div>
        <div class="label-select">
          <mt-select
            :width="200"
            css-class="e-outline"
            float-label-type="Never"
            :data-source="evaluationArr"
            :show-clear-button="false"
            :value="evStep"
            @change="changeEV"
            :placeholder="$t('请选择阶段')"
          ></mt-select>
        </div>
      </div>
      <div class="right-part">
        <mt-button css-class="e-flat" @click="upQuote" v-if="enableEdit"
          ><i class="mt-icons mt-icon-icon_Auction"></i> {{ $t('提交报价') }}</mt-button
        >
        <mt-button css-class="e-flat" @click="save" v-if="enableEdit"
          ><i class="mt-icons mt-icon-icon_solid_Save"></i> {{ $t('保存') }}</mt-button
        >
        <mt-button css-class="e-flat" class="error-i" @click="goBack"
          ><i class="mt-icons mt-icon-icon_solid_close"></i> {{ $t('关闭') }}</mt-button
        >
      </div>
    </div>
    <!-- 底部内容 -->
    <div class="bt-detail mt-flex">
      <div class="ev-item-box">
        <!-- 左侧的单个item -->
        <div
          class="ev-item"
          :class="{ active: productId === i }"
          @click="selectProduct(i)"
          v-for="(item, i) in bidItems"
          :key="i"
        >
          <div class="tp-line mt-flex">
            <div class="title-left flex1">{{ $t('品类名称') }}-{{ item.categoryName }}</div>
            <div class="title-right flex1">{{ item.lineNo }}</div>
          </div>
          <div class="tp-bottom mt-flex">
            <div class="title-left flex1">{{ item.itemName }}</div>
            <div class="title-right flex1"></div>
          </div>
        </div>
      </div>
      <div style="width: 10px; background: rgba(232, 232, 232, 1)"></div>
      <div class="ev-detail">
        <div class="detail-items">
          <plan-info ref="palnInfo" :bid-items="bidItems" :bid-index="productId"></plan-info>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import utils from '@/utils/utils'
import moduleData from '../mock/tabQuotedPrice'
export default {
  name: 'TabQuotedPrice',
  components: {
    planInfo: () =>
      import(
        /* webpackChunkName: "router/supply/quotationBidding/detail/components/planInfo" */ './components/planInfo.vue'
      )
  },
  props: {
    quotedPriceData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      tabId: 0,
      productId: 0,
      bidItems: [],
      modalData: {},
      ladderList: [],
      enableEdit: true
    }
  },
  computed: {
    evaluationArr() {
      let arr = []
      if (Array.isArray(this.quotedPriceData?.rounds)) {
        this.quotedPriceData.rounds.forEach((e) => {
          arr.push({
            text: `第${utils.toChinesNum(e.roundNo)}轮`,
            value: e.roundId
          })
        })
      }
      return arr
    },
    evStep() {
      return this.quotedPriceData.roundId
    },
    biddingId() {
      return this.quotedPriceData.biddingId
    }
  },
  mounted() {
    this.getBidItems(this.evStep, this.biddingId)
  },
  methods: {
    // 获取轮次对应物料信息  roundId = "", biddingId = ""
    getBidItems() {
      const r = moduleData
      // this.$API.quotationBidding.getBidItems(roundId, biddingId).then((r) => {
      this.bidItems = r.data
      // this.getLatestDetail();
      // });
    },
    // 获取物料详情
    // getLatestDetail() {
    //   this.$API.quotationBidding
    //     .getLatestDetail(this.bidItems[this.productId])
    //     .then((r) => {
    //       this.modalData = r.data.priceDetail;
    //       this.ladderList = r.data.itemStages;
    //       this.enableEdit = r.data.priceDetail.enableEdit;
    //     });
    // },
    selectProduct(id) {
      this.productId = id
      this.modalData = {}
      this.ladderList = []
      // this.enableEdit = false;
    },
    changeEV(va) {
      this.getBidItems(va.value, va.biddingId)
      // this.getLatestDetail();
    },
    handleSelectTab() {},
    goBack() {
      this.$router.go(-1)
    },
    upQuote() {
      this.$refs.palnInfo.bidPriceSave(1)
    },
    save() {
      this.$refs.palnInfo.bidPriceSave(0)
    }
  }
}
</script>

<style lang="scss" scoped>
.flex1 {
  flex: 1;
}

.evaluation-container {
  width: 100%;
  min-height: 100%;
  background: #fff;
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 4px;

  .banner-line {
    height: 50px;
    line-height: 50px;
    background: rgba(250, 250, 250, 1);
    border-radius: 4px 4px 0 0;
    border-bottom: 1px solid rgba(232, 232, 232, 1);
    justify-content: space-between;
    justify-content: space-between;

    .left-part {
      padding: 0 20px;
      width: 357px;
      align-items: center;
    }
    .right-part {
      padding: 0 20px;
      /deep/ .mt-button {
        vertical-align: middle;
      }
      .tab-item {
        cursor: pointer;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(154, 154, 154, 1);
        padding: 0 20px;
      }
      .active {
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        position: relative;

        &:after {
          content: ' ';
          display: inline-block;
          width: calc(100% - 56px);
          height: 2px;
          background: rgba(99, 134, 193, 1);
          border: 1px solid rgba(0, 70, 156, 0.1);
          border-radius: 4px;
          text-align: center;
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          top: 42px;
        }
      }
    }
  }

  .bt-detail {
    width: 100%;

    .ev-item-box {
      width: 357px;
      box-sizing: border-box;
      padding: 20px;

      .ev-item {
        padding: 10px 20px;
        background: rgba(255, 255, 255, 1);
        border: 1px solid rgba(232, 232, 232, 1);
        border-radius: 4px;
        box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
        margin-bottom: 20px;
        cursor: pointer;

        .tp-line {
          justify-content: space-between;

          .title-left {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            height: 14px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: 600;
            color: rgba(35, 43, 57, 1);
            text-align: left;
          }
          .title-right {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            height: 14px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: normal;
            color: #6386c1;
            text-align: right;
          }
        }
        .tp-bottom {
          margin-top: 6px;
          justify-content: space-between;
          .title-left {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            height: 12px;
            font-size: 12px;
            font-family: PingFangSC;
            font-weight: 600;
            color: #9a9a9a;
            text-align: left;
          }
          .title-right {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            height: 12px;
            font-size: 12px;
            font-family: PingFangSC;
            font-weight: normal;
            color: #9a9a9a;
            text-align: right;
          }
        }
      }

      .active {
        background: rgba(245, 246, 249, 1);
        border: 1px solid rgba(232, 232, 232, 1);
        border-radius: 4px;
        box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
        position: relative;
        &::before {
          content: ' ';
          display: inline-block;
          width: 3px;
          height: 100%;
          position: absolute;
          left: 0;
          top: 0;
          background: rgba(99, 134, 193, 1);
          border-radius: 4px 0 0 4px;
        }
      }
    }
    .ev-detail {
      flex: 1;
      //   border-left: 10px solid rgba(232, 232, 232, 1);
      overflow-x: auto;
      padding: 20px;

      .detail-banner {
        border-radius: 4px 4px 0 0;
        padding: 20px;
        background: url(../../../../../assets/images/evdetailbanner.jpg) center no-repeat;
        background-size: 100% 100%;
        border: 1px solid rgba(232, 232, 232, 1);
        // min-width: 1120px;
        .title-banner {
          height: 20px;
          font-size: 20px;
          font-family: PingFangSC;
          font-weight: 600;
          color: rgba(41, 41, 41, 1);
        }
        .detail-info {
          height: 14px;
          margin-top: 10px;
          font-size: 14px;
          line-height: 14px;
          color: rgba(157, 170, 191, 1);

          .info-item {
            white-space: nowrap;
          }
        }
      }
    }
  }
}
</style>

<style lang="scss">
.detail-item {
  .mt-tabs-container {
    width: 100%;
    .tab-wrap {
      padding: 0 !important;
    }
  }
}
.error-i .e-flat {
  color: #d81e06;
}
.tab-item {
  .prop-box {
    text-align: center;
    .rank-name {
      height: 16px;
      font-size: 16px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(154, 154, 154, 1);
    }
    .rank-desc {
      height: 12px;
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(41, 41, 41, 1);
      margin-top: 8px;
      display: flex;
      justify-content: center;

      .type-txt {
        width: 12px;
        height: 12px;
        background: rgba(237, 161, 51, 0.1);
        border-radius: 1px;
        font-size: 8px;
        font-family: PingFangSC;
        font-weight: 500;
        margin-left: 6px;
        display: inline-block;
        i {
          transform: scale(0.8);
        }
      }
      .type-1 {
        color: rgba(99, 134, 193, 1);
        background: rgba(99, 134, 193, 0.3);
        border-radius: 1px;
      }
      .type-2 {
        background: rgba(237, 161, 51, 0.3);
        color: rgba(237, 161, 51, 1);
      }
    }
  }
}
</style>
