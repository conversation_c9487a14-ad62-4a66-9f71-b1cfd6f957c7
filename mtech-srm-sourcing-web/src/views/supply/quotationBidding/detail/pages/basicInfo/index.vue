<template>
  <div class="top-form-generator">
    <!-- 你好 -->
    <FormGenerator ref="form" :field-defines="fieldDefines" :form-field-config="formFieldConfig" />
  </div>
</template>

<script>
import FormGenerator from 'ROUTER_PURCHASE_RFX/detail/components/FormGenerator.vue'

export default {
  components: {
    FormGenerator
  },
  props: {
    moduleType: {
      type: Number,
      default: -1
    },
    detailInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      formFieldConfig: {},
      fieldDefines: [],
      headerFieldDefines: []
    }
  },
  mounted() {
    this.getTopConfigFields()
  },
  methods: {
    async getTopConfigFields() {
      await this.$API.supplyQdetail
        .getTabs(this.$route.query.rfxId, this.$route.query.biddingMode)
        .then((res) => {
          if (Array.isArray(res?.data?.moduleItems)) {
            res.data.moduleItems.forEach((e) => {
              if (e.moduleType == 19 && e.fieldDefines) {
                this.headerFieldDefines = e.fieldDefines
              }
            })
          }
        })
      this.formFieldConfig = {
        //标题
        rfxName: {
          defaultValue: () => this.detailInfo.rfxName,
          type: 'text',
          readonly: true
        },
        //寻源方式
        sourcingMode: {
          type: 'select',
          defaultValue: () => this.detailInfo.sourcingMode,
          readonly: true,
          dataSource: [
            { text: this.$t('询报价'), value: 'rfq' },
            { text: this.$t('招投标'), value: 'invite_bids' },
            { text: this.$t('竞价'), value: 'bidding_price' }
          ]
        },
        //寻源对象
        sourcingObj: {
          type: 'text',
          defaultValue: () => this.detailInfo.sourcingObj,
          readonly: true
        },
        //询价类型
        sourcingType: {
          type: 'select',
          readonly: true,
          dataSource: [
            { text: this.$t('新品'), value: 'new_products' },
            { text: this.$t('二次'), value: 'second_inquiry' },
            { text: this.$t('已有'), value: 'exist' }
          ],
          defaultValue: () => this.detailInfo.sourcingType
        },
        //公司名称
        companyName: {
          type: 'text',
          defaultValue: () => this.detailInfo.companyName,
          readonly: true
        },
        // 币种 currencyName currencyName
        currencyName: {
          type: 'select',
          readonly: true,
          handler: (ej2EventObject) => {
            Object.assign(this.formData, ej2EventObject.itemData.data)
          },
          // 供方头部使用headerCurrencyCode
          defaultValue: () => this.detailInfo.headerCurrencyCode,
          api: this.$API.masterData.queryAllCurrency().then((res) => {
            return res.data.map((item) => ({
              text: item.currencyName,
              value: item.currencyCode,
              data: {
                currencyName: item.currencyName,
                currencyCode: item.currencyCode
              }
            }))
          })
        },
        //采购组织  purOrgName  purOrgCode  purOrgId
        purOrgName: {
          type: 'text',
          defaultValue: () => this.detailInfo.purOrgName,
          readonly: true
        },
        //业务类型
        businessTypeName: {
          type: 'text',
          defaultValue: () => this.detailInfo.businessTypeName,
          readonly: true
        },
        //采购员  purExecutorName  purExecutorId
        purExecutorName: {
          type: 'text',
          defaultValue: () => this.detailInfo.purExecutorName,
          readonly: true
        },
        //备注
        remark: {
          col: 1,
          readonly: true,
          defaultValue: () => this.detailInfo.remark,
          type: 'text'
        },
        //需求部门  deptName  deptCode deptId
        deptName: {
          type: 'text',
          defaultValue: () => this.detailInfo.deptName,
          readonly: true
        },
        //工厂 siteName siteId  siteCode
        siteName: {
          type: 'text',
          defaultValue: () => this.detailInfo.siteName,
          readonly: true
        }
      }
      this.fieldDefines = this.headerFieldDefines
      this.fieldDefines.sort((a, b) => b.sortValue - a.sortValue)
    }
  }
}
</script>

<style lang="scss" scoped>
.top-form-generator {
  background: #fff;
  border: 1px solid #e8e8e8;
  padding: 20px;
}
</style>
