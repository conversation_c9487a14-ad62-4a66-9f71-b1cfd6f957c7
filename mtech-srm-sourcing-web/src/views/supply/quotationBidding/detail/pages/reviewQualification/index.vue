<template>
  <div class="review-qualification">
    <div class="margin-top">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="scoreDetailName" :label="$t('是否需要保证金')">
          <mt-select
            disabled
            v-model="formObject.needEarnestMoney"
            float-label-type="Never"
            :data-source="seletArr"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="scoreDetailCode" :label="$t('保证金')">
          <mt-input
            v-model="formObject.earnestMoney"
            float-label-type="Never"
            :disabled="true"
            :placeholder="$t('请输入金额')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
      <div class="buttons">
        <span>{{ $t('保证金') }}</span>
        <mt-button @click="handUploadFile">{{ getUploadFileTxt }}</mt-button>
      </div>
    </div>
    <div class="review-table">
      <mt-template-page
        v-if="!isOnlyEarnest"
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
        @actionComplete="actionComplete"
        @handleChange="handleChange"
      />
    </div>
  </div>
</template>
<script>
import { pageConfig } from './config'
import { utils } from '@mtech-common/utils'
import { isEqual } from 'lodash'
export default {
  props: {
    quotedPriceData: {
      type: Object,
      default: () => ({})
    },
    examineStatus: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      pageConfig: pageConfig.call(this, this.$route?.query?.source),
      formObject: {
        needEarnestMoney: 0,
        earnestMoney: '',
        remark: '',
        examineCode: '',
        examineId: 0,
        examineStatus: 0,
        moneyStatus: 0,
        supplierCode: '',
        supplierId: 0,
        supplierName: ''
      },
      headInfoObject: {},
      examineId: '',
      supplierName: '',
      supplierId: '',
      supplierCode: '',
      formRules: {
        scoreDetailName: [
          {
            required: true,
            message: this.$t('请选择是否需要保证金'),
            trigger: 'blur'
          }
        ]
      },
      seletArr: [
        { text: this.$t('否'), value: 0 },
        { text: this.$t('是'), value: 1 }
      ],
      sourcingFileList: [],
      selectRows: [],
      oldList: [],
      allowSave: true
    }
  },
  mounted() {
    this.getRfxSupRelList()
    if (this.quotedPriceData.status == 1 && this.quotedPriceData.joinStatus !== 1) {
      this.pageConfig[0].grid.editSettings.allowEditing = false
    }
    this.$bus.$on('participate', () => {
      this.pageConfig[0].grid.editSettings.allowEditing = true
    })
  },
  computed: {
    getUploadFileTxt() {
      return this.sourcingFileList.length
        ? this.$t(`保证金附件(${this.sourcingFileList.length})`)
        : this.$t('保证金附件')
    },
    isOnlyEarnest() {
      return this.headInfoObject?.onlyEnableEarnest
    }
  },
  methods: {
    actionComplete(args) {
      if (args.requestType == 'save') {
        if (!isEqual(args.data, args.previousData)) {
          this.intergrateEditData(args.data, args.rowIndex) // 整合编辑数据
        }
      }
    },
    handleChange(e) {
      this.intergrateEditData(e.rowData, e.index)
    },
    intergrateEditData(rowData, rowIndex) {
      let _currentTabRef = this.$refs.templateRef.getCurrentTabRef()
      let dataSource = _currentTabRef?.grid?.dataSource ?? []
      this.$set(dataSource, rowIndex, rowData)
    },
    handleClickToolBar(e) {
      this.selectRows = e.gridRef.getMtechGridRecords()
      if (e.toolbar.id == 'Save') {
        if (this.quotedPriceData.joinStatus !== 1) {
          this.$toast({
            content: this.$t('请先参与'),
            type: 'warning'
          })
          return
        }
        this.allowSave = false
        // e.grid.endEdit()
        let dataSource = e.grid.dataSource
        this.handSaveEvent(dataSource)
      }
    },
    handSaveEvent(_selectRecords) {
      if (_selectRecords.length) {
        _selectRecords.forEach((_data) => {
          // 修复文件类型,从string改成对象
          if (_data['drawing'] && typeof _data['drawing'] == 'string') {
            _data['drawing'] = JSON.parse(_data['drawing'])
            if (_data['drawing'].length) {
              _data['drawing'].forEach((f) => {
                if (!f?.sysFileId) {
                  f.sysFileId = f.id
                  delete f.id
                }
              })
            }
          } else if (!_data['drawing']) {
            _data['drawing'] = []
          }
          _data.sourcingFileSaveDTOList = _data['drawing']
          delete _data.drawing
          delete _data.column
          delete _data.sourcingFileResponseList
        })
      }
      this.$API.rfxSupRel
        .getRfxSaveAdd(_selectRecords)
        .then(() => {
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
        })
        .finally(() => {
          this.allowSave = true
          // this.$refs.templateRef.refreshCurrentGridData();
        })
    },
    handUploadFile() {
      if (this.quotedPriceData.status == 1 && this.quotedPriceData.joinStatus !== 1) {
        this.$toast({
          content: this.$t('请先参与'),
          type: 'warning'
        })
        return
      }
      let isView = true
      // 前端根据examineStatus控制下(只有-1和0状态才能进行修改)
      if ([-1, 0].includes(this.formObject.examineStatus)) {
        isView = false
      }
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "components/Upload/uploaderDialog" */ 'COMPONENTS/Upload/uploaderDialog.vue'
          ),
        data: {
          fileData: utils.cloneDeep(this.sourcingFileList),
          isView, // 是否为预览
          required: false, // 是否必须
          title: this.$t('保证金附件')
        },
        success: (res) => {
          this.handleUploadFiles(res)
        }
      })
    },
    //执行上传文件
    handleUploadFiles(_fileList) {
      let _saveFiles = []
      _fileList.forEach((f) => {
        if (!f?.sysFileId) {
          //如果文件没有sysFileId 说明是新上传文件
          f.docId = this.$route.query.rfxId
          f.sysFileId = f.id
          delete f.id
          _saveFiles.push(f)
        } else {
          _saveFiles.push(f)
        }
      })
      let _saveParams = {
        ...this.headInfoObject,
        sourcingFileSaveList: _saveFiles
      }
      this.$API.RfxSupRelConfig.getTenderFileAdd(_saveParams).then(() => {
        this.$toast({
          content: this.$t('上传成功'),
          type: 'success'
        })
        this.getRfxSupRelList()
      })
    },
    getRfxSupRelList() {
      let params = {
        id: this.$route.query.rfxId
      }
      this.$API.rfxSupRel.getSupRelBidList(params).then((res) => {
        console.error(res, 'res')
        this.headInfoObject = res.data
        this.formObject.needEarnestMoney = res.data.needEarnestMoney
        this.formObject.earnestMoney = res.data.earnestMoney
        this.formObject.remark = res.data.remark
        this.formObject.examineStatus = res.data.examineStatus
        this.$emit('update:examineStatus', res.data.examineStatus)
        this.getRfxRelSupList(res.data)
        this.examineId = res.data.examineId
        this.supplierCode = res.data.supplierCode
        this.supplierName = res.data.supplierName
        this.supplierId = res.data.supplierId
        this.sourcingFileList = res.data.sourcingFileResponseList || []
        // this.examineItemId = res.data.examineItemId;
      })
    },
    getRfxRelSupList(value) {
      this.$set(this.pageConfig[0].grid, 'asyncConfig', {
        url: this.$API.rfxSupRel.getrfxCainder,
        queryBuilderWrap: 'queryBuilderDTO',
        params: {
          examineId: value.examineId,
          supplierId: value.supplierId,
          rfxId: this.$route.query.rfxId
        },
        serializeList: (list) => {
          list.forEach((e) => {
            e.drawing = e.sourcingFileResponseList
              ? JSON.stringify(e.sourcingFileResponseList)
              : null //单独处理附件字段
          })
          this.oldList = utils.cloneDeep(list)
          return list
        }
      })
    },
    //提交
    handSubmitEvent() {
      // 判断是否必传保证金附件
      if (this.formObject.needEarnestMoney == 1 && this.sourcingFileList.length == 0) {
        this.$toast({
          content: this.$t('保证金附件必传'),
          type: 'warning'
        })
        return
      }

      let params = {
        examineId: this.examineId,
        supplierId: this.supplierId,
        rfxId: this.$route.query.rfxId
      }
      this.$API.rfxSupRel.getRfxSaveSubmit(params).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          setTimeout(() => {
            this.$router.go(0)
          }, 300)
        }
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },
    //备注
    strategyRemark(e) {
      if (e.length >= 500) {
        this.$toast({
          content: this.$t('字数不得超过500字'),
          type: 'warning'
        })
      } else {
        this.formObject.remark = e
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.review-qualification {
  padding: 10px;
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
  //保证金头部
  .margin-top {
    width: 950px;
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // 保证金
    .mt-form {
      width: 840px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .mt-form-item {
        width: 400px;
        height: 54px;
      }
    }
    .buttons {
      width: 70px;
      font-size: 14px;
      margin-top: -10px;
      span {
        display: block;
        height: 14px;
        line-height: 14px;
        font-weight: 700;
        margin-bottom: 5px;
      }
      /deep/ .mt-button {
        margin-right: 0;
        button {
          width: 76px;
          height: 34px;
          background: transparent;
          //border: 1px solid rgba(0, 70, 156, 0.1);
          border-radius: 4px;
          box-shadow: unset;
          padding: 0;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(0, 70, 156, 1);
          text-align: left;
        }
      }
    }
    //备注
    .cai-note {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 20px;
      span {
        width: 70px;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: normal;
      }
      .strategy-remark {
        width: 94%;
        font-size: 14px;
      }
    }
  }
  //列表
  .review-table {
    width: 100%;
    height: 100%;
    flex: 1;
  }
}
</style>
