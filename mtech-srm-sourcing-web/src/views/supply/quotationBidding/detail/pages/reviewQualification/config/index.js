import { i18n, permission } from '@/main.js'
import { getValueByPath } from '@/utils/obj'
import { createEditInstance } from '@/utils/ej/dataGrid/index'
import Vue from 'vue'
import cellUpload from 'COMPONENTS/NormalEdit/cellUpload' // 单元格上传
// import cellFileView from 'COMPONENTS/NormalEdit/cellFileView' // 单元格附件查看

const columnData = () => {
  const editInstance = createEditInstance()

  return [
    {
      type: 'checkbox',
      width: '60',
      allowEditing: false
    },
    {
      field: 'examineItemCode',
      headerText: i18n.t('审查代码'),
      allowEditing: false
    },
    {
      field: 'examineItemName',
      headerText: i18n.t('审查项目名称'),
      allowEditing: false
    },
    {
      field: 'examineItemSpec',
      headerText: i18n.t('审查项目说明'),
      allowEditing: false
    },
    {
      field: 'purOpinion',
      headerText: i18n.t('供应商回复'),
      allowEditing: false,
      template: function () {
        return {
          template: Vue.component('actionOption', {
            template: `<mt-input v-model="purOpinion" @blur="handleChange"></mt-input>`,
            data() {
              return { purOpinion: '' }
            },
            created() {
              this.purOpinion = this.data.purOpinion
            },
            methods: {
              handleChange(value) {
                this.data.purOpinion = value
                this.$parent.$emit('handleChange', {
                  rowData: this.data,
                  index: this.data.index,
                  key: 'purOpinion',
                  value: value
                })
              }
            }
          })
        }
      }
    },
    {
      field: 'supplierReply',
      headerText: i18n.t('采方审查意见'),
      allowEditing: false
    },
    {
      field: 'drawing',
      headerText: i18n.t('附件'),
      allowEditing: false,
      template: function () {
        return {
          template: cellUpload
        }
      }
      // editTemplate: () => {
      //   return {
      //     template: cellUpload
      //   }
      // }
    },
    {
      field: 'needFile',
      headerText: i18n.t('是否必须上传附件'),
      allowEditing: false,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'select',
          readonly: true,
          dataSource: [
            { text: i18n.t('否'), value: 0 },
            { text: i18n.t('是'), value: 1 }
          ]
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        switch (Number(cellVal)) {
          case 0:
            return i18n.t('否')
          case 1:
            return i18n.t('是')
          default:
            return cellVal
        }
      }
      // valueConverter: {
      //   type: "map",
      //   map: { 0: i18n.t("否"), 1: "是" },
      // },
    }
  ]
}
export const pageConfig = function (source) {
  return [
    {
      toolbar: [
        // { id: "Submit", icon: "icon_solid_Createorder", title: i18n.t("提交") },
        // { id: "Delete", icon: "icon_solid_Delete", title: i18n.t("删除") },
        {
          id: 'Save',
          icon: 'icon_solid_edit ',
          title: i18n.t('保存'),
          visibleCondition: () => {
            return this.allowSave
          }
        }
      ],
      useToolTemplate: false,
      gridId: permission.gridId['supply'][source]['tabs']['qualification'],
      grid: {
        allowFiltering: true,
        columnData: columnData(),
        dataSource: [{ examineCode: 'examineCode' }],
        editSettings: {
          allowAdding: false,
          allowEditing: false,
          allowDeleting: false,
          mode: 'Normal', // 默认normal模式
          allowEditOnDblClick: false,
          showConfirmDialog: false,
          showDeleteConfirmDialog: false
        }
      }
    }
  ]
}
