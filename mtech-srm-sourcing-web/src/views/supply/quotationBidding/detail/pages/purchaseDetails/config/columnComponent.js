import Vue from 'vue'
import { rowDataTemp, factoryNameOptions, businessGroupCode, editLinkDataParams } from './variable'
import {
  StatusClass,
  StatusText,
  Status,
  ForecastDataTypeCell,
  ComponentType,
  ComponentChangeType
} from './constant'

// 预测管理 Component 集合
const ForecastColumnComponent = {
  // 文本 新增行时带出的数据，不可编辑
  changedInput: (args) => {
    const { dataKey, type } = args
    const template = () => {
      return {
        template: Vue.component('changedInputComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content" v-if="data.id && type !== ComponentType.mustEdit"><span>{{data[dataKey]}}</span></div>
            <div class="field-content" v-else>
              <mt-input
                :value="data[dataKey]"
                disabled
              ></mt-input>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              ComponentChangeType,
              type,
              ComponentType
            }
          },
          beforeDestroy() {
            this.$bus.$off('predictManageComponentChange')
          },
          mounted() {
            // 监听变化
            this.onComponentChange()
          },
          methods: {
            // 监听变化
            onComponentChange() {
              // 监听被变化
              this.$bus.$on(`predictManageComponentChange`, (e) => {
                const { requestKey, modifiedKey, changeType, value } = e
                const requestKeyList = [
                  'itemName', // 物料名称
                  'factoryName', // 工厂
                  'supplierName' // 供应商名称
                ]
                if (
                  requestKeyList.includes(requestKey) &&
                  changeType === ComponentChangeType.code &&
                  modifiedKey === dataKey
                ) {
                  // 发布事件的数据修改了，关联的code修改
                  this.data[dataKey] = value
                  // 编辑行时改变rowDataTemp中当前的值
                  rowDataTemp[rowDataTemp.length - 1][dataKey] = value
                } else if (
                  (requestKey === 'itemName' || requestKey === 'factoryName') &&
                  changeType === ComponentChangeType.link &&
                  modifiedKey === dataKey &&
                  dataKey === 'planGroup'
                ) {
                  // 计划组收到变更请求
                  const factoryCode = rowDataTemp[rowDataTemp.length - 1].factoryCode // 工厂Code
                  const itemCode = rowDataTemp[rowDataTemp.length - 1].itemCode // 物料Code
                  if (factoryCode && itemCode && businessGroupCode.plan) {
                    // 选择了 工厂、物料，而且获得了计划组的业务组类型code
                    const params = {
                      orgCode: factoryCode, // 组织Code
                      categoryCode: itemCode, // 末级节点品类Code
                      groupTypeCode: businessGroupCode.plan // 业务员类型编码
                    }
                    this.postCriteriaQueryBusinessGroup({
                      params,
                      businessKey: 'plan'
                    })
                  }
                } else if (
                  (requestKey === 'itemName' || requestKey === 'factoryName') &&
                  changeType === ComponentChangeType.link &&
                  modifiedKey === dataKey &&
                  dataKey === 'buyerOrgName'
                ) {
                  // 采购组收到变更请求
                  const factoryCode = rowDataTemp[rowDataTemp.length - 1].factoryCode // 工厂Code
                  const itemCode = rowDataTemp[rowDataTemp.length - 1].itemCode // 物料Code
                  if (factoryCode && itemCode && businessGroupCode.purchase) {
                    // 选择了 工厂、物料，而且获得了采购组的业务组类型code
                    const params = {
                      orgCode: factoryCode, // 组织code
                      categoryCode: itemCode, // 末级节点品类code
                      groupTypeCode: businessGroupCode.purchase // 业务员类型编码
                    }
                    this.postCriteriaQueryBusinessGroup({
                      params,
                      businessKey: 'purchase'
                    })
                  }
                }
              })
            },
            // 查询采购组或计划组，并设置值
            postCriteriaQueryBusinessGroup(args) {
              const { params, businessKey } = args
              this.$API.masterData.postCriteriaQueryBusinessGroup(params).then((res) => {
                const list = res?.data || []
                if (businessKey === 'plan' && list.length > 0) {
                  // 计划组
                  const groupName = list[0]?.groupName // 业务组名称，产品说获取第一个
                  const id = list[0]?.id // 业务组id，产品说获取第一个

                  this.data[dataKey] = groupName
                  rowDataTemp[rowDataTemp.length - 1][dataKey] = list[0]?.groupName
                  rowDataTemp[rowDataTemp.length - 1].planGroupId = id // 计划组id
                } else if (businessKey === 'purchase' && list.length > 0) {
                  // 采购组
                  const groupName = list[0]?.groupName // 业务组名称，产品说获取第一个
                  const id = list[0]?.id // 业务组id，产品说获取第一个

                  this.data[dataKey] = groupName
                  rowDataTemp[rowDataTemp.length - 1][dataKey] = groupName
                  rowDataTemp[rowDataTemp.length - 1].buyerOrgId = id // 采购组id
                }
              })
            }
          }
        })
      }
    }
    return template
  },
  // 编辑行时的 文本数据
  input: (args) => {
    const { dataKey, showClearBtn, alwaysEdit } = args
    const template = () => {
      return {
        template: Vue.component('inputComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content" v-if="notEdit"><span>{{data[dataKey]}}</span></div>
            <div class="field-content" v-else>
                <mt-input
                  v-model="data[dataKey]"
                  :show-clear-button="showClearBtn"
                  :disabled="isDisabledEdit()"
                  @input="onInput"
                ></mt-input>
              </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              showClearBtn,
              notEdit: true // 初始不可编辑
            }
          },
          mounted() {
            const isRowAdd = !this.data.id // id没有时为增加行
            if (alwaysEdit) {
              // 总是可编辑
              this.notEdit = false
            } else if (isRowAdd) {
              // 增加行时可编辑
              this.notEdit = false
            }
          },
          methods: {
            onInput(e) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e
            },
            // 编辑的条件
            isDisabledEdit() {
              let disabled = false
              // 条件
              const condition = [
                Status.pendingFeedback // 待反馈
              ]
              if (condition.includes(this.data?.status) && dataKey === 'purRemark') {
                // 采方备注的不可编辑条件
                disabled = true
              }

              return disabled
            }
          }
        })
      }
    }
    return template
  },
  // 编辑行时的 文本数据
  number: (args) => {
    const { dataKey, showClearBtn, alwaysEdit } = args
    const template = () => {
      return {
        template: Vue.component('numberComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content" v-if="notEdit"><span>{{data[dataKey]}}</span></div>
            <div class="field-content" v-else>
              <mt-input-number
                v-model="data[dataKey]"
                :show-spin-button="false"
                :show-clear-button="showClearBtn"
                @input="onInput"
              ></mt-input-number>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              showClearBtn,
              notEdit: true // 初始不可编辑
            }
          },
          mounted() {
            const isRowAdd = !this.data.id // id没有时为增加行
            if (alwaysEdit) {
              // 总是可编辑
              this.notEdit = false
            } else if (isRowAdd) {
              // 增加行时可编辑
              this.notEdit = false
            }
          },
          methods: {
            onInput(e) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e
            }
          }
        })
      }
    }
    return template
  },
  // 下拉框 新增行时可编辑
  select: (args) => {
    const { selectOptions, dataKey, allowFiltering, showClearBtn, type } = args
    const template = () => {
      return {
        template: Vue.component('selectComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content" v-if="data.id && type !== ComponentType.mustEdit"><span>{{data[dataKey]}}</span></div>
            <div class="field-content" v-else>
              <mt-select
                @change="selectChange"
                v-model="data[dataKey]"
                :allow-filtering="allowFiltering"
                :data-source="options"
                :show-clear-button="showClearBtn"
                :placeholder="$t('请选择')"
              ></mt-select>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              selectOptions,
              options: [],
              dataKey,
              allowFiltering,
              showClearBtn,
              ComponentChangeType,
              type,
              ComponentType
            }
          },
          beforeDestroy() {
            this.$bus.$off('predictManageComponentChange')
          },
          mounted() {
            // 初始化页面加载时得到的值
            this.options = this.formatOptionsValueToText(selectOptions)
            // 监听变化
            this.onComponentChange()
          },
          methods: {
            // 监听变化
            onComponentChange() {
              this.$bus.$on(`predictManageComponentChange`, (e) => {
                const { requestKey, modifiedKey, changeType, value } = e
                const requestKeyList = [
                  'itemName' // 物料名称
                ]
                if (
                  requestKeyList.includes(requestKey) &&
                  changeType === ComponentChangeType.link &&
                  modifiedKey === dataKey &&
                  dataKey === 'factoryName'
                ) {
                  this.data[dataKey] = null
                  // 更新工厂的数据源
                  this.getFactoryList(value)
                  // 不在页面显示的值改变
                  this.updateNotTable(undefined)
                  // 触发改变code，清空工厂选择的值
                  this.triggerCodeChange(undefined)
                }
              })
            },
            // 修改中的值
            selectChange(e) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e.value
              const selectValue = this.textToValue({
                options: this.selectOptions,
                text: e.value
              })
              // 不在页面显示的值改变
              this.updateNotTable(selectValue)
              // 触发改变code
              this.triggerCodeChange(selectValue)
              // 触发改变相关的值
              this.triggerLinkChange(selectValue)
            },
            // 不在页面显示的值改变
            updateNotTable(selectValue) {
              // 编辑行时改变rowDataTemp中当前的值
              if (dataKey === 'companyName') {
                // 公司
                rowDataTemp[rowDataTemp.length - 1].companyId = selectValue
              } else if (dataKey === 'itemName') {
                if (!selectValue) {
                  rowDataTemp[rowDataTemp.length - 1].itemId = selectValue
                  return
                }
                // 物料
                for (let i = 0; i < this.options.length; i++) {
                  if (this.options[i].itemCode === selectValue) {
                    rowDataTemp[rowDataTemp.length - 1].itemId = this.options[i].id // 物料id
                    break
                  }
                }
              } else if (dataKey === 'factoryName') {
                if (!selectValue) {
                  rowDataTemp[rowDataTemp.length - 1].factoryId = selectValue
                  return
                }
                // 工厂
                for (let i = 0; i < this.options.length; i++) {
                  if (this.options[i].organizationCode === selectValue) {
                    rowDataTemp[rowDataTemp.length - 1].factoryId = this.options[i].id // 工厂id
                    break
                  }
                }
              }
            },
            // 触发改变相关的值
            triggerLinkChange(selectValue) {
              // 关联的key
              const linkKey = {
                itemName: ['factoryName'], // “物料名称”修改，计划组、采购组、工厂将改变，但是工厂还没被改变前，不应该改变计划组、采购组，因为改变物料，工厂将修改数据源
                factoryName: ['planGroup', 'buyerOrgName'] // “工厂”修改，计划组、采购组改变
              }
              if (linkKey[dataKey]) {
                linkKey[dataKey].forEach((itemLinkKey) => {
                  const args = {
                    requestKey: dataKey, // 触发请求的key
                    modifiedKey: itemLinkKey, // 要被修改的key
                    changeType: ComponentChangeType.link, // 修改类型
                    value: selectValue // 发出的值
                  }
                  this.$bus.$emit('predictManageComponentChange', args)
                })
              }
            },
            // 触发改变code
            triggerCodeChange(selectValue) {
              // 要被修改的code
              const changeCode = {
                itemName: 'itemCode', // “物料名称”，物料编码改变
                factoryName: 'factoryCode', // “工厂”，工厂代码改变
                supplierName: 'supplierCode' // “供应商名称”，供应商编码改变
              }
              if (changeCode[dataKey]) {
                const args = {
                  requestKey: dataKey, // 触发请求的key
                  modifiedKey: changeCode[dataKey], // 要被修改的key
                  changeType: ComponentChangeType.code, // 修改类型
                  value: selectValue // 发出的值
                }
                this.$bus.$emit('predictManageComponentChange', args)
              }
            },
            // 使下拉选择的值转为text
            formatOptionsValueToText(options) {
              const list = []
              if (options.length) {
                options.forEach((item) => {
                  list.push({ ...item, text: item.text, value: item.text })
                })
              }

              return list
            },
            // 将选择的text转为value
            textToValue(args) {
              const { options, text } = args
              let value = undefined
              if (options.length && options.length > 0) {
                for (let i = 0; i < options.length; i++) {
                  if (options[i] && options[i].text === text) {
                    value = options[i].value
                    break
                  }
                }
              }

              return value
            },
            // 获取并更新工厂的数据源
            getFactoryList(itemCode) {
              if (editLinkDataParams.itemCode === itemCode) {
                return
              }
              factoryNameOptions.length = 0
              this.selectOptions.length = 0
              const params = {
                itemCode // 	物料编码
              }
              editLinkDataParams.itemCode = itemCode
              this.$API.masterData.getFactoryList(params).then((res) => {
                const list = res?.data || []
                list.forEach((item) => {
                  factoryNameOptions.push({
                    ...item,
                    text: item.organizationName,
                    value: item.organizationCode
                  })
                })
                this.selectOptions = factoryNameOptions
                this.options = this.formatOptionsValueToText(factoryNameOptions)
              })
            }
          }
        })
      }
    }
    return template
  },
  // 编辑时的 checkBox
  empty: () => {
    return {
      template: Vue.component('emptyComponent', {
        template: `<div></div>`,
        data: function () {
          return {
            data: {}
          }
        }
      })
    }
  },
  /**
   * 预测数据
   *
   * timeInfo => 预测时间;
   * forecastNum => D1预测;
   * orderNum => D2订单;
   * total => D;
   * buyerNum => P;
   * supplierNum => C;
   * planGroupNum => F;
   */
  forecast: (args) => {
    const { colIndex, type, dynamicTitle } = args
    const template = () => {
      return {
        template: Vue.component('forecastComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column" v-if="data[dynamicTitle[colIndex]]">
            <!-- D1(预测)行 view -->
            <div class="forecast-item" v-if="(type === ComponentType.view || data.id) && type !== ComponentType.mustEdit">{{data[dynamicTitle[colIndex]].forecastNum}}</div>
            <!-- D1(预测)行 edit -->
            <div v-if="(type === ComponentType.edit && !data.id) || type === ComponentType.mustEdit">
              <mt-input-number
                :show-clear-button="false"
                :show-spin-button="false"
                v-model="data[dynamicTitle[colIndex]].forecastNum"
                @change="changeForecastNum"
                placeholder="">
              </mt-input-number>
            </div>
            <!-- D2(订单)行 view -->
            <div class="forecast-item" v-if="(type === ComponentType.view || data.id) && type !== ComponentType.mustEdit">{{data[dynamicTitle[colIndex]].orderNum}}</div>
            <!-- D2(订单)行 edit -->
            <div v-if="(type === ComponentType.edit && !data.id) || type === ComponentType.mustEdit">
              <mt-input-number
                :show-clear-button="false"
                :show-spin-button="false"
                v-model="data[dynamicTitle[colIndex]].orderNum"
                @change="changeOrderNum"
                placeholder="">
              </mt-input-number>
            </div>
            <!-- D行 view -->
            <div class="forecast-item" v-if="(type === ComponentType.view || data.id) && type !== ComponentType.mustEdit">{{data[dynamicTitle[colIndex]].total}}</div>
            <!-- D行 edit -->
            <div v-if="(type === ComponentType.edit && !data.id) || type === ComponentType.mustEdit">
              <mt-input-number
                disabled
                :show-clear-button="false"
                :show-spin-button="false"
                v-model="data[dynamicTitle[colIndex]].total"
                @change="changeTotal"
                placeholder="">
              </mt-input-number>
            </div>
            <!-- P行 view -->
            <div
              :class="['forecast-item', isBuyerNumHighlight() && 'forecast-highlight']"
              v-show="type === ComponentType.view"
              >{{data[dynamicTitle[colIndex]].buyerNum}}</div>
            <!-- P行 edit -->
            <div v-if="type === ComponentType.edit || type === ComponentType.mustEdit">
              <mt-input-number
                :show-clear-button="false"
                :show-spin-button="false"
                :disabled="isDisabledBuyerNumEdit()"
                v-model="data[dynamicTitle[colIndex]].buyerNum"
                @change="changeBuyerNum"
                placeholder="">
              </mt-input-number>
            </div>
            <!-- C行 仅可查看 -->
            <div class="forecast-item">{{data[dynamicTitle[colIndex]].supplierNum}}</div>
            <!-- Gap行 仅可查看 -->
            <div class="forecast-item">{{gap}}</div>
            <!-- F行 view -->
            <div class="forecast-item"
              v-show="type === ComponentType.view"
              >{{data[dynamicTitle[colIndex]].planGroupNum}}</div>
            <!-- F行 edit -->
            <div v-if="type === ComponentType.edit">
              <mt-input-number
                :show-clear-button="false"
                :show-spin-button="false"
                :disabled="isDisabledPlanGroupNumEdit()"
                v-model="data[dynamicTitle[colIndex]].planGroupNum"
                @change="changePlanGroupNum"
                placeholder="">
              </mt-input-number>
            </div>
          </div>
          <div class="grid-edit-column mt-flex-direction-column" v-else>
            <div v-for="(cellItem, index) in cellItems" :key="index" class="forecast-item"></div>
          </div>`,
          data: function () {
            return {
              data: {},
              colIndex,
              dynamicTitle,
              ComponentType,
              type,
              gap: '',
              cellItems: ForecastDataTypeCell
            }
          },
          mounted() {
            // 计算 Gap 行
            this.calculateGap()
          },
          methods: {
            // 计算 Gap 行
            calculateGap() {
              const supplierNum = this.data[dynamicTitle[colIndex]]?.supplierNum || 0 // C行
              const buyerNum = this.data[dynamicTitle[colIndex]]?.buyerNum || 0 // P行
              if (supplierNum >= 0 || buyerNum >= 0) {
                this.gap = supplierNum - buyerNum // Gap 差异 = C行 - P行
              }
            },
            // change D1预测 forecastNum
            changeForecastNum(value) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dynamicTitle[colIndex]].forecastNum =
                Number(value)
              // 关联D行
              const orderNum = Number(this.data[dynamicTitle[colIndex]].orderNum)
              const total = orderNum + Number(value)
              this.data[dynamicTitle[colIndex]].total = total
              rowDataTemp[rowDataTemp.length - 1][dynamicTitle[colIndex]].total = total
            },
            // change D2订单 orderNum
            changeOrderNum(value) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dynamicTitle[colIndex]].orderNum = Number(value)
              // 关联D行
              const forecastNum = Number(this.data[dynamicTitle[colIndex]].forecastNum)
              const total = forecastNum + Number(value)
              this.data[dynamicTitle[colIndex]].total = total
              rowDataTemp[rowDataTemp.length - 1][dynamicTitle[colIndex]].total = total
            },
            // change D total
            changeTotal(value) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dynamicTitle[colIndex]].total = Number(value)
              // 计算 Gap 行
              this.calculateGap()
            },
            // change P buyerNum
            changeBuyerNum(value) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dynamicTitle[colIndex]].buyerNum = Number(value)
              // 计算 Gap 行
              this.calculateGap()
            },
            // P 行可编辑的条件
            isDisabledBuyerNumEdit() {
              let disabled = true
              // 条件
              const condition = [
                Status.feedbackAbnormal, // 反馈异常
                Status.feedbackNormal, // 反馈异常
                Status.modified, // 已修改
                Status.new // 新增
              ]
              if (condition.includes(this.data?.status)) {
                disabled = false
              }

              return disabled
            },
            // change F planGroupNum
            changePlanGroupNum(value) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dynamicTitle[colIndex]].planGroupNum =
                Number(value)
            },
            // F 行可编辑的条件
            isDisabledPlanGroupNumEdit() {
              let disabled = true
              // 条件
              const condition = [
                Status.new, // 新建
                Status.modified, // 待反馈
                Status.pendingFeedback, // 待反馈
                Status.feedbackNormal, // 反馈正常
                Status.feedbackAbnormal, // 反馈异常
                Status.confirmed // 已确认
              ]
              if (condition.includes(this.data?.status)) {
                disabled = false
              }

              return disabled
            },
            // P 行不等于 D 行时高亮
            isBuyerNumHighlight() {
              let isHighlight = false
              const buyerNum = this.data[dynamicTitle[colIndex]].buyerNum // P
              const total = this.data[dynamicTitle[colIndex]].total // D

              if (buyerNum != total) {
                isHighlight = true
              }

              return isHighlight
            }
          }
        })
      }
    }
    return template
  },
  // 不可编辑的类型 D1(预测) D2(订单) D P C F
  type: () => {
    return {
      template: Vue.component('typeComponent', {
        template: `<div class="grid-edit-column mt-flex-direction-column forecast-type-box"><div class="forecast-item" v-for="(cellItem, index) in cellItems" :key="index">{{cellItem.title}}</div></div>`,
        data: function () {
          return {
            data: {},
            cellItems: ForecastDataTypeCell
          }
        }
      })
    }
  },
  // 不可编辑的文字
  text: (args) => {
    const { dataKey } = args
    const template = () => {
      return {
        template: Vue.component('textComponent', {
          template: `<div class="grid-edit-column mt-flex-direction-column"><div class="field-content"><span>{{data[dataKey]}}</span></div></div>`,
          data: function () {
            return { data: {}, dataKey }
          }
        })
      }
    }
    return template
  },
  // 不可编辑的错误文字
  errorText: (args) => {
    const { dataKey } = args
    const template = () => {
      return {
        template: Vue.component('textComponent', {
          template: `<div class="grid-edit-column mt-flex-direction-column"><div class="field-content"><span class="errorText">{{data[dataKey]}}</span></div></div>`,
          data: function () {
            return { data: {}, dataKey }
          }
        })
      }
    }
    return template
  },
  // 版本
  versionText: () => {
    return {
      template: Vue.component('textComponent', {
        template: `<div class="grid-edit-column mt-flex-direction-column"><div class="field-content"><span>{{version}}</span></div></div>`,
        data: function () {
          return { data: {}, version: '' }
        },
        mounted() {
          if (this.data.syncVersion && this.data.publishVersion >= 0) {
            this.version = `${this.data.syncVersion}.${this.data.publishVersion}`
          } else if (this.data.syncVersion) {
            this.version = this.data.syncVersion
          }
        }
      })
    }
  },
  // 状态
  status: (args) => {
    const { type, cellTools } = args
    const template = () => {
      return {
        // 按钮：发布，取消发布，删除，确认
        template: Vue.component('statusComponent', {
          template: `<div class="grid-edit-column mt-flex-direction-column"
          >
            <div class="field-content"><span :class="[StatusClass[data.status]]">{{data.status | dataFormat}}</span></div>
            <div class="column-tool mt-flex invite-btn" v-if="type === ComponentType.view">
              <div v-for="(cellTool, index) in cellTools"
                :key="index" :id="cellTool.id"
                class="template-svg"
                v-if="!cellTool.visibleCondition || cellTool.visibleCondition(data)"
                @click.stop="clickCellTool({tool:cellTool, data})"
                ><MtIcon :name="cellTool.icon" />&nbsp<span class="icon-title">{{cellTool.title}}</span>
              </div>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              type,
              StatusClass,
              StatusText,
              cellTools,
              ComponentType
            }
          },
          filters: {
            // 值转换
            dataFormat(value) {
              let result = value
              if (StatusText[value]) {
                result = StatusText[value]
              }
              return result
            }
          },
          methods: {
            clickCellTool(data) {
              this.$parent.$emit('handleClickCellTool', data)
            }
          }
        })
      }
    }
    return template
  }
}

export default ForecastColumnComponent
