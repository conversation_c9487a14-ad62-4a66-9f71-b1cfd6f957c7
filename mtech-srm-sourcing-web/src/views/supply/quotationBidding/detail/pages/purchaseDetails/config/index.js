import { i18n } from '@/main.js'
export const toolbar = [
  { id: 'save', icon: 'icon_solid_edit', title: i18n.t('保存') },
  { id: 'quote', icon: 'icon_solid_edit', title: i18n.t('重新报价') },
  {
    id: 'upload',
    icon: 'icon_solid_Createproject',
    title: i18n.t('导入')
  }
]

export const toolbarNoImport = [
  { id: 'save', icon: 'icon_solid_edit', title: i18n.t('保存') },
  { id: 'quote', icon: 'icon_solid_edit', title: i18n.t('重新报价') }
]

export const disabledToolbar = [
  {
    id: 'save',
    icon: 'icon_solid_edit',
    title: i18n.t('保存'),
    visibleCondition: () => false
  },
  {
    id: 'quote',
    icon: 'icon_solid_edit',
    title: i18n.t('重新报价'),
    visibleCondition: () => false
  }
]

export const editColumnBefore = [
  {
    width: '50',
    type: 'checkbox',
    allowEditing: false,
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'addId', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    headerText: i18n.t('addId主键'),
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true, // 一定要有主键，不然已有的行会被修改掉。。
    allowEditing: false
  }
]
