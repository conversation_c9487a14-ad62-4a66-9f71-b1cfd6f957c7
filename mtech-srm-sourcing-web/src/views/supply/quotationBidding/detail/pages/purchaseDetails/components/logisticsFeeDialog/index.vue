<template>
  <mt-dialog
    css-class="logisticsFeeDialog"
    ref="dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
    width="1450"
    height="800"
  >
    <div class="dialog-content" style="padding-top: 16px">
      <mt-form ref="editForm" :model="formData" :rules="formRules">
        <mt-row :gutter="20" v-for="(config, configIndex) in itemConfig" :key="configIndex">
          <mt-col :span="24">
            <p class="form-title">
              {{ config.fieldGroup }}
            </p>
          </mt-col>
          <mt-col v-for="(item, index) in config.fields" :span="6" :key="index">
            <mt-form-item :prop="item.fieldCode" :label="item.fieldName">
              <!-- 数值输入框 -->
              <mt-input-number
                v-if="item.editConfig && item.editConfig.type === 'number'"
                v-model="formData[item.fieldCode]"
                :placeholder="$t('请输入')"
                v-bind="item.editConfig.props"
              />
              <!-- 下拉选择框 -->
              <mt-select
                v-else-if="item.editConfig && item.editConfig.type === 'select'"
                v-model="formData[item.fieldCode]"
                :placeholder="$t('请选择')"
                v-bind="item.editConfig.props"
                @change="(e) => handleValueChange(item, e)"
              />
              <!-- 日期选择框 -->
              <mt-date-picker
                v-else-if="item.editConfig && item.editConfig.type === 'date'"
                v-model="formData[item.fieldCode]"
                v-bind="item.editConfig.props"
              />
              <!-- 时间选择框 -->
              <mt-date-time-picker
                v-else-if="item.editConfig && item.editConfig.type === 'datetime'"
                v-model="formData[item.fieldCode]"
                v-bind="item.editConfig.props"
              />
              <!-- 文本输入框 -->
              <mt-input
                v-else
                type="text"
                v-model="formData[item.fieldCode]"
                :show-clear-button="true"
                v-bind="item.editConfig.props"
                :placeholder="$t('请输入')"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { cloneDeep } from 'lodash'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      itemConfig: [], // 配置数据（处理后）
      formRules: {}, // 校验规则
      formData: {
        // eta: '',
        // etd: ''
      }, //表单数据
      currencyConfig: {},
      sumConfig: [], //总价信息
      buttons: [
        {
          click: this.calculate,
          buttonModel: { content: this.$t('计算') }
        },
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  computed: {
    logisticsItemConfig() {
      return this.modalData.logisticsItemConfig
    },
    header() {
      return this.modalData.title
    },
    biddingItemLogisticsDTO() {
      return this.modalData.biddingItemLogisticsDTO
    },
    biddingItemDTO() {
      return this.modalData.biddingItemDTO
    },
    itemDTO() {
      return { ...this.biddingItemLogisticsDTO, ...this.biddingItemDTO }
    }
  },
  mounted() {
    this.init()
  },

  methods: {
    init() {
      this.initFom()
      this.$refs['dialog'].ejsRef.show()
    },
    // 初始化 - formItem数据
    initFom() {
      let _formData = {}
      let codeReg = /[a-zA-Z].*CurrencyCode$|[a-zA-Z].*PortFeeCode$/
      let requiredReg = /[a-zA-Z].*CurrencyName$|[a-zA-Z].*PortFeeName$|et[a|d]$/
      this.itemConfig = cloneDeep(this.logisticsItemConfig)
      this.itemConfig.forEach((config) => {
        let _fields = []
        config.fields.forEach((item) => {
          // 初始化默认信息
          item.editConfig = item?.editConfig || { type: 'input', props: {} }
          // 如果是code，则隐藏，利用币种名称来选择
          if (!codeReg.test(item.fieldCode)) {
            _fields.push(item)
            this.formRules[item.fieldCode] = [
              {
                required: requiredReg.test(item.fieldCode),
                trigger: 'blur',
                message: this.getMessage(item?.editConfig?.type)
              }
            ]
          } else {
            // 整理币种联动字段，便于写入币种code信息
            let _currencyName = item.fieldCode.replace(/Code$/, 'Name')
            this.currencyConfig[_currencyName] = item.fieldCode
          }
          // 整理汇总字段，便于写入总价信息
          if (item.fieldGroupCode === 'sum') {
            this.sumConfig.push(item.fieldCode)
          }
          // 填充默认值
          _formData[item.fieldCode] = this.itemDTO[item.fieldCode]
        })
        this.formData = Object.assign({}, _formData)
        config.fields = [..._fields]
      })
    },
    // 修改值
    handleValueChange(item, e) {
      const { fieldCode } = item
      let _currencyCode = this.currencyConfig[fieldCode] // 对应币种code
      this.$set(this.formData, _currencyCode, e.itemData?.currencyCode || '')
    },
    // 弹框 - 计算
    async calculate() {
      let params = {
        rfxItemId: this.modalData.rfxItemId,
        rfxId: this.modalData.rfxId,
        biddingItemLogisticsDTO: {
          ...this.formData
        }
      }
      const res = await this.$API.supplyQdetail.getLogisticsPrice(params)
      if (res.code === 200) {
        let _obj = {}
        this.sumConfig.forEach((item) => {
          _obj[item] = res.data[item]
        })
        this.formData = Object.assign({}, this.formData, { ..._obj })
      }
    },
    // 弹框 - 确认
    async confirm() {
      console.log('%c调试', 'font-weight:bold;color:blue', this.formData)
      // 确认之前优先计算
      // await this.calculate()

      this.$refs.editForm.validate((val) => {
        if (val) {
          // 数据回填至表格
          const _formData = cloneDeep(this.formData)
          const { quoteEffectiveEndDate, quoteEffectiveStartDate } = _formData //其他字段暂不作逻辑区分
          delete _formData.quoteEffectiveStartDate
          delete _formData.quoteEffectiveEndDate
          this.$emit('confirm-function', {
            // 区分物流明细 | 报价明细
            biddingItemLogisticsDTO: this.formData,
            biddingItemDTO: { quoteEffectiveEndDate, quoteEffectiveStartDate }
          })
        }
      })
    },
    // 弹框 - 取消
    cancel() {
      this.$emit('cancel-function')
    },
    // utils - 根据不同字段类型确认不同提示语言
    getMessage(type) {
      let message = ''
      switch (type) {
        case 'select':
          message = this.$t('请选择')
          break
        case 'datetime':
        case 'date':
          message = this.$t('选择一个日期')
          break
        case 'text':
        case 'number':
        default:
          message = this.$t('请输入')
          break
      }
      return message
    }
  }
}
</script>
<style lang="scss">
.logisticsFeeDialog {
  .mt-input-number input {
    height: 26px;
    &[disabled] {
      background: #fafafa;
      background-image: none;
      background-position: initial;
      background-repeat: no-repeat;
      background-size: 0;
      border-color: rgba(0, 0, 0, 0.06);
      color: rgba(0, 0, 0, 0.38);
    }
  }
  .mt-input-number .step-box .one-icon {
    height: 10px;
    .icon-p {
      margin-top: -2px;
    }
  }
  .e-input-group input.e-input {
    min-height: 25px !important;
  }
}
</style>
<style lang="scss" scoped>
.form-title {
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 4px;
}
</style>
