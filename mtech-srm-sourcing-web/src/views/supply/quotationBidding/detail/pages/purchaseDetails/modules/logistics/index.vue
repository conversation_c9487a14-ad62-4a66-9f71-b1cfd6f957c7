<!--采购明细-->
<template>
  <div class="flex-full-height">
    <div class="tabs-wrap">
      <mt-tabs
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabSource"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>
      <div class="bid-tips" v-if="bidTips" :style="{ left: tabSource.length * 158 + 'px' }">
        <mt-icon name="icon_card_info"></mt-icon>{{ $t(bidTips) }}
      </div>
    </div>
    <!-- 议价、当前报价、历史报价公用 -->
    <CustomAgGrid
      ref="CustomAgGrid"
      v-clickoutside="clickoutside"
      :search-config="searchConfig"
      :toolbar="toolbar"
      :columns="columns"
      :suppress-row-transform="true"
      :row-data="tableData"
      :animate-rows="false"
      :is-stop-editing-on-blur="isStopEditingOnBlur"
      :get-row-id="getRowId"
      :context="context"
      @cell-value-changed="cellValueChanged"
      @handleClickToolbar="handleClickToolbar"
      @onGridReady="onGridReady"
      @row-selected="onRowSelected"
      @refresh="refresh"
      @search="search"
      @cellDoubleClicked="cellDoubleClicked"
    >
      <div slot="header" class="aggregate-container" v-if="moduleType == 0 || moduleType == 2">
        <span>{{ $t('汇总') }}</span>
        <div class="amount">
          <div>
            {{ $t('上次总金额（未税）：') }}<span>{{ lastTotal }}</span>
          </div>
          <div>
            {{ $t('本次总金额（未税）：') }}<span>{{ currentTotal }}</span>
          </div>
        </div>
      </div>
    </CustomAgGrid>
    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadInitBalanceRef"
      :down-template-name="downTemplateName"
      :request-urls="requestUrls"
      :down-template-params="downTemplateParams"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import CustomAgGrid from '@/components/CustomAgGrid'
import cellFile from '@/components/AgCellComponents/cellFile'
import cellLink from '@/components/AgCellComponents/cellLink'
import mixin from '../../config/mixin'

export default {
  mixins: [mixin],
  watch: {},
  components: {
    CustomAgGrid,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default,
    // eslint-disable-next-line
    cellFile, // 单元格文件查看
    // eslint-disable-next-line
    cellLink // 单元格点击跳转
  },
  data() {
    return {
      logisticsItemConfig: []
    }
  },
  computed: {},
  create() {},
  mounted() {},
  methods: {
    // 初始化 - toolbar
    initToolbar() {
      if (this.moduleType === 1) {
        // 历史报价 不显示
        this.toolbar = []
        return
      }
      let _toolbar = [
        {
          id: 'Save',
          icon: 'icon_solid_Save',
          title: this.$t('保存')
        },
        {
          id: 'ReQuote',
          icon: 'icon_solid_edit',
          title: this.$t('重新报价')
        },
        {
          id: 'Import',
          icon: 'icon_solid_Import',
          title: this.$t('导入')
        },
        {
          id: 'Export',
          icon: 'icon_solid_Import',
          title: this.$t('导出报价单')
        }
      ]
      this.toolbar = this.status === 1 && this.joinStatus === 1 ? _toolbar : []
    },
    // 获取物流弹框数据
    async getLogisticsItemConfig() {
      let res = await this.$API.supplyQdetail.getLogisticsConfig({ rfxId: this.$route.query.rfxId })
      if (res.code === 200) {
        this.logisticsItemConfig = [...res.data]
      }
    },
    // 设置物流弹框属性 (获取配置表中配置的属性)
    setLogisticsItemAttr(tableName, fieldCode, column) {
      if (!['mt_supplier_bidding_item_logistics', 'mt_supplier_bidding_item'].includes(tableName))
        return
      this.logisticsItemConfig.forEach((item) => {
        let _fields = [...item.fields]
        let _find = _fields.find((field) => field.fieldCode === fieldCode)
        if (_find) {
          Object.assign(_find, column)
        }
      })
    },
    cellDoubleClicked(e) {
      // 如果非空运、海运或者表格不可编辑的话，点击无效
      if (
        !this.getGridEditAble() ||
        !['sea_transport', 'air_transport'].includes(this.quotedPriceData.sourcingObjType)
      )
        return
      const { data, node } = e
      this.$dialog({
        modal: () => import('../../components/logisticsFeeDialog'),
        data: {
          title: this.$t('报价明细'),
          logisticsItemConfig: this.logisticsItemConfig,
          rfxId: this.$route.query?.rfxId,
          rfxItemId: data.rfxItemId,
          biddingItemLogisticsDTO: data.biddingItemLogisticsDTO,
          biddingItemDTO: data.biddingItemDTO
        },
        success: (res) => {
          data.biddingItemLogisticsDTO = {
            ...data.biddingItemLogisticsDTO,
            ...res.biddingItemLogisticsDTO
          }
          data.biddingItemDTO = { ...data.biddingItemDTO, ...res.biddingItemDTO }
          node.setData(data)
          // 设置数据后直接保存
          this.handleSave()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.normal-class {
  font: #000;
}
/deep/.bg-grey {
  background: #dedede;
}
/deep/.bg-orange {
  background: #fdf5ea;
}

/deep/ .e-rowcell input[readonly] {
  background: #f5f5f5;
  border-bottom: none;
}
.full-height {
  display: flex;
  flex-direction: column;
  .content {
    flex: 1;
  }
  .tabs-wrap {
    position: relative;
    .bid-tips {
      position: absolute;
      // left: 230px;
      z-index: 1;
      top: 15px;
      color: #4d5b6f;
    }
  }
  /deep/.toolbar-container {
    position: relative;
    top: -10px;
  }
  .aggregate-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fafafa;
    width: 100%;
    padding: 5px 20px;
    border: 1px solid #babfc7;
    border-bottom: none;
    span {
      color: #9a9a9a;
    }
    .amount {
      display: flex;
      color: #9a9a9a;
      span {
        color: #00469c;
      }
      div {
        &:last-of-type {
          margin-left: 40px;
        }
      }
    }
  }
  /deep/.e-spinner-pane {
    display: none;
  }
  /deep/.e-grid .e-detailrowcollapse:not(.e-editedbatchcell):not(.e-updatedtd) {
    vertical-align: middle;
  }
  /deep/ .e-grid .e-detailrowexpand:not(.e-editedbatchcell):not(.e-updatedtd) {
    vertical-align: middle;
  }
  /deep/ .e-table {
    border-left: 1px solid #e0e0e0;
    .e-emptyrow {
      td {
        border: 1px solid #e0e0e0;
        border-top: none;
      }
    }
  }
  /deep/ .e-detailindentcell {
    border-right: none;
  }
  /deep/ .e-detailcell {
    .toolbar-container {
      height: 30px;
    }
  }
}
</style>
