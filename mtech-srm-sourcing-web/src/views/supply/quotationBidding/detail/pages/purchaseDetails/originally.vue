<!--采购明细-->
<template>
  <div class="full-height">
    <div>
      <mt-tabs
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabSource"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>
    </div>

    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :edit-settings="editSettings"
      :query-cell-info="customiseCell"
      @handleClickToolBar="handleClickToolBar"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    />
    <!--   @handleClickCellTool="handleClickCellTool" -->
  </div>
</template>

<script>
export default {
  props: {
    fieldDefines: {
      type: Array,
      default: () => {
        return []
      }
    },
    submitField: {
      type: Array,
      default: () => {
        return []
      }
    },
    quotedPriceData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    detailInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      pageConfig: [
        {
          toolbar: [{ id: 'save', icon: 'icon_solid_edit', title: this.$t('保存') }],
          useToolTemplate: false,
          grid: {
            allowFiltering: true,
            editSettings: {
              allowEditing: true
            },
            columnData: this.fieldDefines,
            frozenColumns: 1,
            asyncConfig: {
              url: this.$API.supplyQdetail.tenderItems,
              params: {
                rfxId: this.$route.query.rfxId,
                tabType: 0
              },
              queryBuilderWrap: 'queryBuilderDTO'
            },
            queryCellInfo: this.customiseCell,
            class: 'pe-edit-grid custom-toolbar-grid'
          }
        }
      ],
      editSettings: {
        allowEditing: true
        // allowAdding: true,
        // allowDeleting: true,
        // mode: "Normal", // 选择默认模式，双击整行可以进行编辑
        // showConfirmDialog: false,
        // showDeleteConfirmDialog: false,
        // newRowPosition: "Bottom",
      },
      disabled: false,
      actionCompletes: {},
      //切换tabs页
      tabSource: [
        { title: this.$t('当前报价'), moduleType: 0 },
        { title: this.$t('历史报价'), moduleType: 1 }
      ]
    }
  },
  mounted() {
    if (this.quotedPriceData.bidStatus == 3) {
      this.disabled = true
    }
    // if (this.getDataUrl != "getRFXItem") {
    //   this.$set(this.pageConfig[0], "toolbar", []);
    // }
    // this.resetAsyncConfigParams();
    // this.getData();
    // this.purchaseDetails();
  },
  methods: {
    // 点击跳转
    handleSelectTab(e) {
      if (e != 0) {
        this.$set(this.pageConfig[0], 'toolbar', [])
      } else {
        this.$set(this.pageConfig[0], 'toolbar', [
          { id: 'save', icon: 'icon_solid_edit', title: this.$t('保存') }
        ])
      }
    },
    //点击头部
    handleClickToolBar(e) {
      console.log(e.toolbar.id)
      if (e.toolbar.id == 'save') {
        this.handleClickSaves()
      }
    },
    // 不可修改的单元格，修改背景色
    customiseCell(args) {
      if (!args.column.allowEditing) {
        args.cell.classList.add('bg-grey')
      } else {
        args.cell.classList.add('bg-orange')
      }
    },
    //行内进入事件
    actionBegin() {},
    //行内离开事件
    actionComplete(e) {
      this.actionCompletes = e.data
    },
    //保存
    handleClickSaves() {
      if (this.actionCompletes) {
        let rfxId = this.quotedPriceData.rfxId
        let rfxItemId = this.quotedPriceData.rfxItemId
        let bidStatus = this.quotedPriceData.bidStatus
        let dataObj = [this.actionCompletes]
        let arr = [{}]
        console.log(rfxItemId)
        for (let i = 0; i < this.submitField.length; i++) {
          let a = this.submitField[i].fieldCode
          arr[0][a] = dataObj[0][a]
        }
        let flag = false
        for (let key in arr[0]) {
          if (arr[0][key] == '') {
            flag = true
            break
          }
        }
        if (flag == false) {
          console.log(rfxId)
          var param1 = {
            rfxId: rfxId,
            bidStatus: bidStatus,
            bidPriceItems: arr
          }
          this.$API.supplyQdetail.priceSave(param1).then((res) => {
            console.log(res)
          })
        }
      } else {
        this.$toast({ content: this.$t('请输入必填项'), type: 'warning' })
        return
      }
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/.bg-grey {
  background: #dedede;
}
/deep/.bg-orange {
  background: #fdf5ea;
}
</style>
