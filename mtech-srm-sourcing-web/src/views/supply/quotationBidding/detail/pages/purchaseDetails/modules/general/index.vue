<!--采购明细-->
<template>
  <div class="flex-full-height">
    <div class="tabs-wrap">
      <mt-tabs
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabSource"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>
      <div class="bid-tips" v-if="bidTips" :style="{ left: tabSource.length * 158 + 'px' }">
        <mt-icon name="icon_card_info"></mt-icon>{{ $t(bidTips) }}
      </div>
    </div>
    <!-- 议价、当前报价、历史报价公用 -->
    <CustomAgGrid
      ref="CustomAgGrid"
      v-clickoutside="clickoutside"
      :search-config="searchConfig"
      :toolbar="toolbar"
      :columns="columns"
      :suppress-row-transform="true"
      :row-data="tableData"
      :animate-rows="false"
      :is-stop-editing-on-blur="isStopEditingOnBlur"
      :get-row-id="getRowId"
      :context="context"
      @cell-value-changed="cellValueChanged"
      @handleClickToolbar="handleClickToolbar"
      @onGridReady="onGridReady"
      @row-selected="onRowSelected"
      @refresh="refresh"
      @search="search"
    >
      <div slot="header" class="aggregate-container" v-if="moduleType == 0 || moduleType == 2">
        <span>{{ $t('汇总') }}</span>
        <div class="amount">
          <div>
            {{ $t('上次总金额（未税）：') }}<span>{{ lastTotal }}</span>
          </div>
          <div>
            {{ $t('本次总金额（未税）：') }}<span>{{ currentTotal }}</span>
          </div>
        </div>
      </div>
    </CustomAgGrid>
    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadInitBalanceRef"
      :down-template-name="downTemplateName"
      :request-urls="requestUrls"
      :down-template-params="downTemplateParams"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import CustomAgGrid from '@/components/CustomAgGrid'
import cellFile from '@/components/AgCellComponents/cellFile'
import cellLink from '@/components/AgCellComponents/cellLink'
import mixin from '../../config/mixin'

export default {
  mixins: [mixin],
  watch: {},
  components: {
    CustomAgGrid,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default,
    // eslint-disable-next-line
    cellFile, // 单元格文件查看
    // eslint-disable-next-line
    cellLink // 单元格点击跳转
  },
  data() {
    return {
      logisticsItemConfig: []
    }
  },
  computed: {},
  create() {},
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
.normal-class {
  font: #000;
}
/deep/.bg-grey {
  background: #dedede;
}
/deep/.bg-orange {
  background: #fdf5ea;
}

/deep/ .e-rowcell input[readonly] {
  background: #f5f5f5;
  border-bottom: none;
}
.full-height {
  display: flex;
  flex-direction: column;
  .content {
    flex: 1;
  }
  .tabs-wrap {
    position: relative;
    .bid-tips {
      position: absolute;
      // left: 230px;
      z-index: 1;
      top: 15px;
      color: #4d5b6f;
    }
  }
  /deep/.toolbar-container {
    position: relative;
    top: -10px;
  }
  .aggregate-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fafafa;
    width: 100%;
    padding: 5px 20px;
    border: 1px solid #babfc7;
    border-bottom: none;
    span {
      color: #9a9a9a;
    }
    .amount {
      display: flex;
      color: #9a9a9a;
      span {
        color: #00469c;
      }
      div {
        &:last-of-type {
          margin-left: 40px;
        }
      }
    }
  }
  /deep/.e-spinner-pane {
    display: none;
  }
  /deep/.e-grid .e-detailrowcollapse:not(.e-editedbatchcell):not(.e-updatedtd) {
    vertical-align: middle;
  }
  /deep/ .e-grid .e-detailrowexpand:not(.e-editedbatchcell):not(.e-updatedtd) {
    vertical-align: middle;
  }
  /deep/ .e-table {
    border-left: 1px solid #e0e0e0;
    .e-emptyrow {
      td {
        border: 1px solid #e0e0e0;
        border-top: none;
      }
    }
  }
  /deep/ .e-detailindentcell {
    border-right: none;
  }
  /deep/ .e-detailcell {
    .toolbar-container {
      height: 30px;
    }
  }
}
</style>
