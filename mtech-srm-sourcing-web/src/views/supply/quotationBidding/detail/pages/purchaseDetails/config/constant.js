import { i18n } from '@/main.js'
export const ConstDynamicTitleStr = 'title_' // 动态表格数据的 key 标识

// 业务组类型数字字典编码
export const BusinessGroupTypeDictCode = 'BUSINESSGROUP'

// 分页的高度
export const PagerHeight = 60
// toolBar 的高度
export const ToolbarHeight = 50
// 表头的高度
export const TheadHeight = 42

// 表格组件类型
export const ComponentType = {
  view: 'view',
  edit: 'edit',
  mustEdit: 'mustEdit'
}

// 预测数据类型cell
export const ForecastDataTypeCell = [
  {
    title: 'D1(预测)'
  },
  {
    title: 'D2(订单)'
  },
  {
    title: 'D(原始需求）'
  },
  {
    title: 'P(需求量）'
  },
  {
    title: 'C(承诺量）'
  },
  {
    title: 'Gap(差异）'
  },
  {
    title: 'F'
  }
]

// 表格编辑 的 RequestType
export const RequestType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add'
}

// 表格编辑 的 action
export const ActionType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  edit: 'edit'
}

// 行编辑触发的 change 事件类型
export const ComponentChangeType = {
  code: 'code', // 例：下拉框选择“供应商”，供应商编码（code）要被修改
  link: 'link' // 例：计划组：根据物料+工厂+计划组的业务组类型 code 带出
}

// 预测类型对应后端在表头增加的标志
export const ForecastType = {
  day: 'D',
  week: 'W',
  month: 'M'
}

// 动态行初始化数据
export const DynamicItemInit = {
  timeInfo: '', // 预测时间
  forecastNum: '', // D1预测
  orderNum: '', // D2订单
  total: '', // D
  buyerNum: '', // P
  supplierNum: '', // C
  planGroupNum: '0' // F
}

// 新增行固定数据
export const NewRowData = {
  thePrimaryKey: 'add' + Math.random().toString(36).substring(3, 8),
  status: 0, // 状态 0 新建、1 已修改、2 待反馈、3 反馈正常、4 反馈异常、5 已确认
  itemName: '', // 物料名称
  itemCode: '', // 物料编码
  factoryName: '', // 工厂
  factoryCode: '', // 工厂代码
  planGroup: '', // 计划组
  buyerOrgName: '', // 采购组
  productRel: '', // 机型/机芯
  companyName: '', // 公司
  mrpArea: '', // 计划区域
  percentage: '', // 配额
  supplierName: '', // 供应商名称
  supplierCode: '', // 供应商编码
  manufacturer: '', // 制造商名称
  rawMaterialManufacturer: '', // 关键原材厂商
  rawMaterialOrigin: '', // 关键原材产地
  packagingManufacturer: '', // 包装厂商
  packagingOrigin: '', // 包装产地
  specialUse: '', // 制造商专用状况
  undeliveredOrderQty: '', // 未交PO
  storageQty: '', // 已入库数量
  purRemark: '' // 采方备注
}

// toolbar 按钮配置
export const Toolbar = [
  {
    id: 'ForecastAdd',
    prefixIcon: 'mt-icons mt-icon-icon_solid_Createproject',
    text: i18n.t('新增'),
    tooltipText: i18n.t('新增'),
    icon: 'icon_solid_Createproject',
    title: i18n.t('新增')
  },
  {
    id: 'ForecastDelete',
    prefixIcon: 'mt-icons mt-icon-icon_table_delete',
    text: i18n.t('删除'),
    tooltipText: i18n.t('删除'),
    icon: 'icon_table_delete',
    title: i18n.t('删除')
  },
  {
    id: 'ForecastPublish',
    prefixIcon: 'mt-icons mt-icon-icon_solid_Release',
    text: i18n.t('发布'),
    tooltipText: i18n.t('发布'),
    icon: 'icon_solid_Release',
    title: i18n.t('发布')
  },
  {
    id: 'ForecastConfirm',
    prefixIcon: 'mt-icons mt-icon-icon_table_batchacceptance',
    text: i18n.t('确认'),
    tooltipText: i18n.t('确认'),
    icon: 'icon_table_batchacceptance',
    title: i18n.t('确认')
  },
  {
    id: 'ForecastImport',
    prefixIcon: 'mt-icons mt-icon-icon_solid_Import',
    text: i18n.t('导入'),
    tooltipText: i18n.t('导入'),
    icon: 'icon_solid_Import',
    title: i18n.t('导入')
  },
  {
    id: 'ForecastExport',
    prefixIcon: 'mt-icons mt-icon-icon_solid_Import',
    text: i18n.t('导出'),
    tooltipText: i18n.t('导出'),
    icon: 'icon_solid_Import',
    title: i18n.t('导出')
  }
]

// 弹框表格行按钮
export const DialogCellTools = [
  {
    id: 'ForecastDelete',
    icon: '', // icon_solid_Delete
    title: i18n.t('删除'),
    visibleCondition: (data) => {
      return data.status === Status.new // 状态：新建
    }
  }
]

// 表格行按钮
export const CellTools = [
  {
    id: 'ForecastPublish',
    icon: '', // icon_solid_Release
    title: i18n.t('发布'),
    visibleCondition: (data) => {
      return data.status === Status.new || data.status === Status.modified // 状态：新建 || 已修改
    }
  },
  {
    id: 'ForecastCancelPublish',
    icon: '', // icon_solid_Delete
    title: i18n.t('取消发布'),
    visibleCondition: (data) => {
      return data.status === Status.pendingFeedback // 状态：待反馈
    }
  },
  {
    id: 'ForecastDelete',
    icon: '', // icon_solid_Delete
    title: i18n.t('删除'),
    visibleCondition: (data) => {
      return data.status === Status.new || data.status === Status.modified // 状态：新建 || 已修改
    }
  },
  {
    id: 'ForecastConfirm',
    icon: '', // icon_solid_Delete
    title: i18n.t('确认'),
    visibleCondition: (data) => {
      return data.status === Status.feedbackAbnormal || data.status === Status.feedbackNormal // 状态：新建 || 已修改
    }
  }
]

// 预测表格/预测弹框表格 EditSettings
export const EditSettings = {
  allowEditing: true,
  allowAdding: true,
  allowDeleting: true,
  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Top'
} // 编辑设置

// 预测模板 2：短周期；1：中周期；0：长周期；
export const ForecastTemplate = {
  long: 0, // 长周期
  medium: 1, // 中周期
  short: 2 // 短周期
}
// 预测模板 text
export const ForecastTemplateText = {
  [ForecastTemplate.long]: i18n.t('长周期'),
  [ForecastTemplate.medium]: i18n.t('中周期'),
  [ForecastTemplate.short]: i18n.t('短周期')
}
// 预测模板 Options
export const ForecastTemplateOptions = [
  {
    text: ForecastTemplateText[ForecastTemplate.long],
    value: ForecastTemplate.long
  },
  {
    text: ForecastTemplateText[ForecastTemplate.medium],
    value: ForecastTemplate.medium
  },
  {
    text: ForecastTemplateText[ForecastTemplate.short],
    value: ForecastTemplate.short
  }
]

// 状态 0 新建、1 已修改、2 待反馈、3 反馈正常、4 反馈异常、5 已确认
export const Status = {
  new: 0, // 新建
  modified: 1, // 已修改
  pendingFeedback: 2, // 待反馈 不可编辑
  feedbackNormal: 3, // 反馈正常
  feedbackAbnormal: 4, // 反馈异常
  confirmed: 5 // 已确认 不可编辑
}
// 状态 text
export const StatusText = {
  [Status.new]: i18n.t('新建'),
  [Status.modified]: i18n.t('已修改'),
  [Status.pendingFeedback]: i18n.t('待反馈'),
  [Status.feedbackNormal]: i18n.t('反馈正常'),
  [Status.feedbackAbnormal]: i18n.t('反馈异常'),
  [Status.confirmed]: i18n.t('已确认')
}
// 状态 class
export const StatusClass = {
  [Status.new]: 'col-active',
  [Status.modified]: 'col-active',
  [Status.pendingFeedback]: 'col-active', // col-published
  [Status.feedbackNormal]: 'col-active', // col-normal
  [Status.feedbackAbnormal]: 'col-active', // col-abnormal
  [Status.confirmed]: 'col-active' // col-inactive
}

// 预测表格列数据
export const ForecastColumnData = [
  {
    fieldCode: 'checkBox' // 不可编辑
  },
  {
    fieldCode: 'thePrimaryKey'
  },
  {
    fieldCode: 'serialNumber', // 前端定义,不可编辑
    fieldName: i18n.t('序号')
  },
  {
    fieldCode: 'syncVersion', // 自动生成,不可编辑 大版本， publishVersion 小版本
    fieldName: i18n.t('版本')
  },
  {
    fieldCode: 'status', // 自动生成，不可编辑
    fieldName: i18n.t('状态') // 0 新建、1 已修改、2 待反馈、3 反馈正常、4 反馈异常、5 已确认
  },
  {
    fieldCode: 'itemName', // 主数据中的物料，新增状态时可编辑，下拉框，必须
    fieldName: i18n.t('物料名称')
  },
  {
    fieldCode: 'itemCode', // 新增状态时“物料选择”带出，不可编辑
    fieldName: i18n.t('物料编码')
  },
  {
    fieldCode: 'factoryName', // 物料带出对应的主数据中的工厂，新增状态时可编辑，下拉框模糊搜索，必须
    fieldName: i18n.t('工厂')
  },
  {
    fieldCode: 'factoryCode', // 新增状态时“工厂选择”带出，不可编辑
    fieldName: i18n.t('工厂代码')
  },
  {
    fieldCode: 'supplierName', // 主数据供应商表，新增状态时可编辑，下拉框模糊搜索
    fieldName: i18n.t('供应商名称')
  },
  {
    fieldCode: 'supplierCode', // “供应商选择”带出，不可编辑
    fieldName: i18n.t('供应商编码')
  },
  {
    fieldCode: 'planGroup', // 根据物料+工厂+计划组的业务组类型code带出，不可编辑，必须
    fieldName: i18n.t('计划组')
  },
  {
    fieldCode: 'buyerOrgName', // 根据物料+工厂+采购组的业务组类型code带出，不可编辑，必须
    fieldName: i18n.t('采购组')
  },
  {
    fieldCode: 'companyName', // 通过登录用户信息获取当前租户的公司列表，新增状态时可编辑，下拉框
    fieldName: i18n.t('公司')
  },
  {
    fieldCode: 'productRel', // 物料所属的机型/机芯，新增状态时可编辑，文本框
    fieldName: i18n.t('机型/机芯')
  },
  {
    fieldCode: 'mrpArea', // 预测报表行的计划区域，新增状态时可编辑，文本框
    fieldName: i18n.t('计划区域')
  },
  {
    fieldCode: 'percentage', // 预测数据对应供应商的配额，新增状态时可编辑，文本框
    fieldName: i18n.t('配额')
  },
  {
    fieldCode: 'manufacturer', // 新增状态时可编辑，文本框
    fieldName: i18n.t('制造商名称')
  },
  {
    fieldCode: 'rawMaterialManufacturer', // 新增状态时可编辑，文本框
    fieldName: i18n.t('关键原材厂商')
  },
  {
    fieldCode: 'rawMaterialOrigin', // 新增状态时可编辑，文本框
    fieldName: i18n.t('关键原材产地')
  },
  {
    fieldCode: 'packagingManufacturer', // 新增状态时可编辑，文本框
    fieldName: i18n.t('包装厂商')
  },
  {
    fieldCode: 'packagingOrigin', // 新增状态时可编辑，文本框
    fieldName: i18n.t('包装产地')
  },
  {
    fieldCode: 'specialUse', // 新增状态时可编辑，文本框
    fieldName: i18n.t('制造商专用状况')
  },
  {
    fieldCode: 'undeliveredOrderQty', // 新增状态时可编辑，数字
    fieldName: i18n.t('未交PO')
  },
  {
    fieldCode: 'storageQty', // 新增状态时可编辑，数字
    fieldName: i18n.t('已入库数量')
  },
  {
    fieldCode: 'purRemark', // 计划组可填写备注，新增状态时可编辑，文本框
    fieldName: i18n.t('采方备注')
  },
  {
    fieldCode: 'supRemark', // 不可编辑
    fieldName: i18n.t('供应商备注')
  },
  {
    fieldCode: 'remainFeedbackTime', // 按照配置后，发布给供应商后显示，不可编辑
    fieldName: i18n.t('剩余反馈时间（H）')
  },
  {
    fieldCode: 'forecastDataType', // 前端写死 D1(预测); D2(订单); D;C;P;F;
    fieldName: i18n.t('类型')
  }
]

// 预测弹框表格列数据
export const ForecastDialogColumnData = [
  // {
  //   fieldCode: "isError",
  //   fieldName: i18n.t("是否异常"),
  // },
  {
    fieldCode: 'errorContent',
    fieldName: i18n.t('错误信息')
  }
]

// 预测弹框表格列不显示的列数据
export const ForecastDialogColumnDataDoNotShow = [
  'syncVersion', // 版本
  'remainFeedbackTime', // 剩余反馈时间
  'supRemark' // 供应商备注
]
