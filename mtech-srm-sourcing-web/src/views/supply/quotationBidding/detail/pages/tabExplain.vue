<template>
  <div id="app">
    <mt-rich-text-editor
      css-class="rich-editor"
      :readonly="true"
      :value="remark"
      :enable-html-encode="true"
    >
    </mt-rich-text-editor>
    <div style="margin-top: 20px">
      <ScTable ref="sctableRef" :columns="columns" :table-data="tableData" :height="400" />
    </div>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'
import Vue from 'vue'
import MtRichTextEditor from '@mtech-ui/rich-text-editor'
Vue.use(MtRichTextEditor)
import { i18n } from '@/main.js'

export default {
  name: 'App',
  components: { ScTable },
  props: {
    remark: {
      type: String,
      default: i18n.t('暂无数据')
    }
  },
  data() {
    return {
      tableData: []
    }
  },
  computed: {
    columns() {
      return [
        {
          field: 'supplierName',
          title: this.$t('供应商')
        },
        {
          field: 'content',
          title: this.$t('留言内容')
        },
        {
          field: 'msgDetail',
          title: this.$t('实际价格')
        }
      ]
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    getTableData() {
      this.$API.supplyQdetail
        .getMessageBoardMyselfApi({ rfxId: this.$route.query.rfxId })
        .then((res) => {
          if (res.code == 200) {
            this.tableData = [
              {
                content: this.$t('报价错误'),
                ...res.data
              }
            ]
          }
        })
    }
  }
}
</script>
<style>
.OfferBid-status0 {
  width: 56px;
  height: 20px;
  background: #f4f4f4;
  border-radius: 2px;
  padding: 4px;
  color: #9a9a9a;
}
.OfferBid-status2 {
  width: 56px;
  height: 20px;
  background: #eef2f9;
  border-radius: 2px;
  padding: 4px;
  color: #6386c1;
}
</style>
<style>
.user-content {
  color: red;
}
body,
html {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
}
#app {
  padding: 0;
  height: 100%;
  width: 100%;
}
.test-btn {
  position: fixed;
  bottom: 10px;
  z-index: 2;
  left: 50px;
}
</style>
