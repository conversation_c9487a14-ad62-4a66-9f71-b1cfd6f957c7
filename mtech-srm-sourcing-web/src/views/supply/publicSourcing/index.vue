<template>
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.status"
            css-class="rule-element"
            :data-source="statusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择状态')"
          />
        </mt-form-item>
        <mt-form-item prop="rfxCode" :label="$t('单据号')" label-style="top">
          <mt-input
            v-model="searchFormModel.rfxCode"
            :show-clear-button="true"
            :placeholder="$t('请输入单据号')"
          />
        </mt-form-item>
        <mt-form-item prop="rfxName" :label="$t('单据名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.rfxName"
            :show-clear-button="true"
            :placeholder="$t('请输入单据名称')"
          />
        </mt-form-item>

        <mt-form-item prop="companyCode" :label="$t('客户')" label-style="top">
          <mt-input
            v-model="searchFormModel.companyCode"
            :show-clear-button="true"
            :placeholder="$t('请输入客户')"
          />
        </mt-form-item>
        <mt-form-item prop="categoryCode" :label="$t('品类')" label-style="top">
          <mt-input
            v-model="searchFormModel.categoryCode"
            :show-clear-button="true"
            :placeholder="$t('请输入品类')"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('发布时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :allow-edit="false"
            :placeholder="$t('请选择发布时间')"
            :open-on-focus="true"
            @change="handleDateChange"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      grid-id="f976dd8f-d024-4a67-8bab-21c7f624b585"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="listColumns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import { getTimeList } from '@/utils/obj'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import XEUtils from 'xe-utils'

export default {
  components: {
    ScTable,
    CollapseSearch
  },
  data() {
    return {
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 20,
        pageSizes: [20, 50, 200, 500, 1000, 2000]
      },
      pageInfo: {
        size: 20,
        current: 1
      },
      statusList: [
        { value: 0, text: this.$t('已发布') },
        { value: 1, text: this.$t('已参与') },
        { value: 2, text: this.$t('已提交') },
        { value: 3, text: this.$t('已驳回') },
        { value: 4, text: this.$t('已完成') }
      ],
      toolbar: [{ code: 'participate', name: this.$t('参与'), status: 'info' }],
      searchFormModel: {
        createTime: getTimeList(3)
      },
      tableData: [],
      loading: false,
      type: 'list'
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    // 列配置
    listColumns() {
      return [
        {
          width: 50,
          type: 'checkbox',
          fixed: 'left'
        },
        {
          width: 50,
          field: 'index',
          title: this.$t('序号')
        },
        {
          field: 'status',
          title: this.$t('状态'),
          minWidth: 80,
          slots: {
            default: ({ row }) => {
              const selectItem = this.statusList.find((item) => item.value === row.status)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'rfxCode',
          title: this.$t('单据号'),
          minWidth: 150,
          slots: {
            default: ({ row, column }) => {
              return [<a on-click={() => this.handleClickCellTitle(row, column)}>{row.rfxCode}</a>]
            }
          }
        },
        {
          field: 'rfxName',
          title: this.$t('单据名称'),
          minWidth: 140
        },
        {
          field: 'companyCode',
          title: this.$t('客户'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [<span>{row.companyCode + '-' + row.companyName}</span>]
            }
          }
        },
        {
          field: 'categoryCode',
          title: this.$t('品类'),
          minWidth: 150,
          slots: {
            default: ({ row }) => {
              return [<span>{row.categoryCode + '-' + row.categoryName}</span>]
            }
          }
        },
        {
          field: 'deliveryPlaceName',
          title: this.$t('交货地点'),
          minWidth: 120
        },
        {
          field: 'deadlineDate',
          title: this.$t('截止日期'),
          minWidth: 140
        },
        {
          field: 'expectSupplierQty',
          title: this.$t('期望寻源数量'),
          minWidth: 100
        },
        {
          field: 'remark',
          title: this.$t('备注'),
          minWidth: 140
        },
        {
          field: 'createTime',
          title: this.$t('发布时间'),
          minWidth: 140
        },
        {
          field: 'remainingDateStr',
          title: this.$t('剩余时间'),
          minWidth: 140
        },
        {
          field: 'hanlder',
          title: this.$t('操作'),
          minWidth: 90,
          fixed: 'right',
          slots: {
            default: ({ row, column }) => {
              return [
                <div>
                  <a on-click={() => this.handleClickCellTitle(row, column)}>
                    {this.$t('结果查询')}
                  </a>
                </div>
              ]
            }
          }
        }
      ]
    }
  },
  mounted() {
    this.handleSearch()
  },
  methods: {
    // 日期格式化
    handleDateChange(e) {
      if (e.startDate) {
        const startDate = XEUtils.toDateString(e.startDate, 'yyyy-MM-dd') + ' 00:00:00'
        const endDate = XEUtils.toDateString(e.endDate, 'yyyy-MM-dd') + ' 23:59:59'
        this.searchFormModel['createStartTime'] = XEUtils.timestamp(startDate)
        this.searchFormModel['createEndTime'] = XEUtils.timestamp(endDate)
      } else {
        this.searchFormModel['createStartTime'] = null
        this.searchFormModel['createEndTime'] = null
      }
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    // 查询
    async handleSearch(type) {
      // 解决修改查询条件后，查询结果有数据但未正常显示的问题（type: pageChange/refresh）
      if (!type) {
        this.pageInfo.current = 1
        this.$refs.pageRef.jumpNum = 1
        this.$refs.pageRef.currentPage = 1
      }
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      delete params.submitTime
      this.loading = true
      const res = await this.$API.publicSourcing
        .queryPublicList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.tableData?.forEach((item, index) => {
          item.index = index + 1
        }) || []
        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (['participate'].includes(e.code)) {
        if (selectedRecords?.length !== 1) {
          this.$toast({ content: this.$t('请选择一条数据！'), type: 'warning' })
          return
        }
      }
      // 参与
      this.handleParticipate(selectedRecords)
    },
    // 点击单元格
    handleClickCellTitle(row, column) {
      if (column.field === 'rfxCode') {
        this.$router.push({
          name: 'public-sourcing-detail',
          query: {
            rfxId: row.rfxId,
            refreshId: Date.now()
          }
        })
      } else if (column.field === 'hanlder') {
        if (row.status !== 4) {
          this.$toast({ content: this.$t('只能查看已完成状态的数据！'), type: 'warning' })
          return
        }
        this.$router.push({
          name: 'public-sourcing-result',
          query: {
            rfxId: row.rfxId,
            refreshId: Date.now()
          }
        })
      }
    },
    // 参与
    async handleParticipate(list) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认参与？`)
        },
        success: async () => {
          const ids = []
          list.forEach((item) => ids.push(item.id))
          const selectedRecords = this.tableRef.getCheckboxRecords()
          const res = await this.$API.publicSourcing.joinPublicList({
            rfxId: selectedRecords[0]?.rfxId
          })
          if (res.code === 200) {
            if (res.data?.registrationTaskFinished === 0) {
              this.confirmParticipate(res.msg)
            } else {
              this.$toast({ content: this.$t(`参与成功`), type: 'success' })
              this.handleSearch()
            }
          }
        }
      })
    },
    // 确认参与
    confirmParticipate(msg) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: msg
        },
        success: async () => {
          this.$router.push({
            path: `/supplier/sup/access`
          })
        }
      })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.handleSearch('pageChange')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.handleSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
