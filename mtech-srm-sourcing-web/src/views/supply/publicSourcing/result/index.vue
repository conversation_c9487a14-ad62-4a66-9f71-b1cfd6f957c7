<template>
  <div class="publicSourcing-full-height">
    <div class="top-container">
      <div class="top-content">
        <div class="top-info">
          <div class="title">
            <div>
              <span>{{ rfxCode }}</span>
              <span class="sub-title">{{ rfxName }}</span>
            </div>
          </div>
          <div class="top-btns">
            <vxe-button
              v-for="(item, index) in detailToolbar"
              v-show="!item.isHidden"
              :key="index"
              :icon="item.icon"
              :status="item.status"
              :disabled="item.disabled"
              size="small"
              @click="handleClickToolBar(item)"
              >{{ item.name }}</vxe-button
            >
          </div>
        </div>
      </div>
    </div>
    <div class="body-container">
      <collapse :title="$t('入围结果')">
        <div slot="content" style="display: flex; height: 500px">
          <CustomAgGrid
            ref="CustomAgGrid"
            height="300px"
            :columns="columns"
            :suppress-row-transform="true"
            :row-data="tableData"
            :animate-rows="false"
            :params-type="2"
            @onGridReady="onGridReady"
            @refresh="refresh"
            @search="search"
          >
          </CustomAgGrid>
        </div>
      </collapse>
    </div>
  </div>
</template>

<script>
import CustomAgGrid from '@/components/CustomAgGrid'
import { Button as VxeButton } from 'vxe-table'
import collapse from '../detail/components/collapse'
import { columns } from './config'
import { cloneDeep } from 'lodash'
export default {
  name: 'PublicSourcingResult',
  components: {
    CustomAgGrid,
    VxeButton,
    collapse
  },
  data() {
    return {
      rfxCode: '',
      rfxName: '',
      columns,
      tableData: [],
      detailToolbar: [
        {
          code: 'back',
          name: this.$t('返回'),
          status: 'primary'
        }
      ]
    }
  },
  created() {
    this.intTableData()
  },

  methods: {
    // 获取详情页数据
    async intTableData() {
      const res = await this.$API.publicSourcing.querySourcingResultList({
        rfxId: this.$route.query?.rfxId
      })
      if (res.code !== 200) return
      this.tableData = cloneDeep(res.data)
    },
    // 点击工具栏按钮
    handleClickToolBar() {
      // 返回
      this.$router.go(-1)
    },
    onGridReady() {},
    refresh() {
      this.intTableData()
    },
    search() {}
  }
}
</script>

<style scoped lang="scss">
.publicSourcing-full-height {
  padding: 8px 8px 0;
  background: #fff;
  height: calc(100% - 20px);
  display: flex;
  flex-direction: column;
}
.publicSourcing-full-height.isBasic {
  height: auto;
}
.body-container {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  margin-top: 16px;
}
.top-container {
  height: 40px;
  width: 100%;
  .top-content {
    padding: 8px 8px 0 8px;
    // background-color: #f9f9f9;
    border-radius: 8px;
    .top-info {
      display: flex;
      justify-content: space-between;
      .title {
        font-size: 20px;
        font-weight: 700;
        color: #292929;
        span {
          margin-right: 15px;
        }
        .sub-title {
          font-size: 15px;
          color: #777;
        }
      }
      .top-btns {
        text-align: right;
        margin-bottom: 10px;
      }
    }
  }
}
</style>
