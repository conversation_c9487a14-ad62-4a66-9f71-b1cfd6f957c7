import { i18n } from '@/main.js'
const supplierStatus = {
  0: i18n.t('待入围'),
  1: i18n.t('已入围'),
  2: i18n.t('已淘汰')
}

export const columns = [
  {
    field: 'lineNo',
    headerName: i18n.t('行号'),
    width: 65
  },
  {
    field: 'rfxCode',
    headerName: i18n.t('单据号')
  },
  {
    field: 'rfxName',
    headerName: i18n.t('单据名称')
  },
  {
    field: 'supplierCode',
    headerName: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerName: i18n.t('供应商名称')
  },
  {
    field: 'categoryCode',
    headerName: i18n.t('品类'),
    valueformatter: (params) => {
      return params.data?.categoryCode
        ? params.data.categoryCode + '-' + params.data.categoryName
        : params.data.categoryName
    }
  },
  {
    field: 'currencyCode',
    headerName: i18n.t('币种')
  },
  {
    field: 'totalPrice',
    headerName: i18n.t('初步报价')
  },
  {
    field: 'biddingScore',
    headerName: i18n.t('报价得分')
  },
  {
    field: 'otherScore',
    headerName: i18n.t('其它得分')
  },
  {
    field: 'totalScore',
    headerName: i18n.t('总分')
  },
  {
    field: 'reason',
    headerName: i18n.t('原因说明')
  },
  {
    field: 'supplierStatus',
    headerName: i18n.t('入围状态'),
    valueFormatter: (params) => {
      return supplierStatus[params.value]
    }
  }
]
