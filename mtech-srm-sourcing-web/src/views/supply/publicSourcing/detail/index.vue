<template>
  <div class="publicSourcing-full-height" :class="{ isBasic: activeTabIndex === 0 }">
    <div class="top-container">
      <div class="top-content">
        <div class="top-info">
          <div class="title">
            <div>
              <span>{{ rfxCode }}</span>
              <span class="sub-title" :title="rfxName">{{ rfxName }}</span>
            </div>
          </div>
          <div class="top-btns">
            <vxe-button
              v-for="(item, index) in detailToolbar"
              v-show="!item.isHidden"
              :key="index"
              :icon="item.icon"
              :status="item.status"
              :disabled="item.disabled"
              size="small"
              @click="handleClickToolBar(item)"
              >{{ item.name }}</vxe-button
            >
          </div>
        </div>
      </div>
    </div>
    <div class="body-container">
      <mt-tabs
        ref="mtTabsRef"
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabList"
        :halt-select="false"
        @handleSelectTab="(index, item) => handleTabChange(index)"
        style="background-color: #fff"
      />
      <baseInfoTab
        ref="baseInfoRef"
        :detail-info="detailInfo"
        :status="status"
        :editable="editable"
        v-show="activeTabIndex === 0"
      ></baseInfoTab>
      <requireTab ref="requireRef" v-if="activeTabIndex === 1" :editable="editable"></requireTab>
      <cooperationTab
        ref="cooperationRef"
        v-if="activeTabIndex === 2"
        :editable="editable"
      ></cooperationTab>
      <supplierInfoTab
        ref="supplierInfoRef"
        v-if="activeTabIndex === 3"
        :editable="editable"
      ></supplierInfoTab>
      <attachmentTab
        ref="attachmentRef"
        v-if="activeTabIndex === 4"
        :editable="editable"
      ></attachmentTab>
    </div>
  </div>
</template>

<script>
import { Button as VxeButton } from 'vxe-table'
import baseInfoTab from './tabs/baseInfoTab' // 寻源基本信息
import requireTab from './tabs/requireTab' // 寻源要求
import cooperationTab from './tabs/cooperationTab' // 合作承诺
import supplierInfoTab from './tabs/supplierInfoTab' // 供应商附件
import attachmentTab from './tabs/attachmentTab' // 附件
import { cloneDeep } from 'lodash'
export default {
  name: 'InitiatedDetail',
  inject: ['reload'],
  components: {
    VxeButton,
    baseInfoTab,
    requireTab,
    attachmentTab,
    cooperationTab,
    supplierInfoTab
  },
  data() {
    return {
      rfxCode: '',
      rfxName: '',
      status: 0,
      detailInfo: {},
      activeTabIndex: 0,
      dataList: [[], []],
      itemLength: 0,
      type: 'detail',
      isInit: true,
      purchaserSearcValue: null,
      keepArr: ['baseInfoTab', 'requireTab', 'logTab', 'attachmentTab']
    }
  },
  computed: {
    detailToolbar() {
      const toolbar = [
        {
          code: 'back',
          name: this.$t('返回'),
          status: 'primary'
        },
        {
          code: 'participate',
          name: this.$t('参与'),
          status: '',
          isHidden: this.status !== 0 // 不是已发布的单隐藏
        },
        {
          code: 'save',
          name: this.$t('保存'),
          status: '',
          isHidden: ![1, 3].includes(this.status) // 不是已参与、已驳回的单
        },
        {
          code: 'submit',
          name: this.status === 1 ? this.$t('提交报名') : this.$t('提交'),
          status: '',
          isHidden: ![1, 3].includes(this.status) // 不是已参与、已驳回的单
        }
      ]
      return toolbar
    },

    tabList() {
      const tabs = [
        { title: this.$t('寻源基本信息'), compName: 'baseInfoTab' },
        { title: this.$t('寻源要求'), compName: 'requireTab' },
        { title: this.$t('合作承诺'), compName: 'cooperationTab' },
        { title: this.$t('企业基本信息'), compName: 'supplierInfoTab' },
        { title: this.$t('附件'), compName: 'AttachmentTab' }
      ]
      return tabs
    },
    editable() {
      return [1, 3].includes(this.status) // 已参与、已驳回的单可以编辑
    }
  },
  mounted() {},
  created() {
    this.init()
  },
  activted() {
    this.init()
  },
  methods: {
    // 初始化
    init() {
      this.queryBaseInfo()
    },
    // 获取详情页数据
    async queryBaseInfo() {
      const res = await this.$API.publicSourcing.queryPublicDetail({ id: this.$route.query?.rfxId })
      if (res.code !== 200) return
      this.detailInfo = cloneDeep(res.data)
      const { rfxCode, rfxName, status } = res.data
      // 渲染基本信息
      this.rfxCode = rfxCode
      this.rfxName = rfxName
      this.status = status // 单据状态
    },
    // 点击工具栏按钮
    handleClickToolBar(e) {
      switch (e.code) {
        case 'back':
          this.handleBack()
          break
        case 'participate':
          this.handleParticipate()
          break
        case 'save':
          this.handleSave(this.activeTabIndex, true)
          break
        case 'submit':
          this.handleSubmit(e.code)
          break
        default:
          break
      }
    },
    // 切换tab页签
    handleTabChange(index) {
      let fromIndex = this.$refs.mtTabsRef.activeTab
      this.editable && this.handleSave(fromIndex)
      this.$refs.mtTabsRef.activeTab = index
      this.activeTabIndex = index
    },
    // 返回
    handleBack() {
      this.$router.push({
        name: 'public-sourcing'
      })
    },
    // 参与
    async handleParticipate() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: `<span style="color:red;">${this.$t(
            `参与请确保所填内容及上传附件真实准确，一旦发现弄虚作假，取消此次参与资格！！`
          )}</span>`
        },
        success: async () => {
          const res = await this.$API.publicSourcing.joinPublicList({
            rfxId: this.$route.query?.rfxId
          })
          if (res.code === 200) {
            if (res.data?.registrationTaskFinished === 0) {
              this.confirmParticipate(res.msg)
            } else {
              this.$toast({ content: this.$t(`参与成功`), type: 'success' })
              this.reload()
            }
          }
        }
      })
    },
    // 确认参与
    confirmParticipate(msg) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: msg
        },
        success: async () => {
          this.$router.push({
            path: `/supplier/sup/access`
          })
        }
      })
    },
    // 保存
    async handleSave(currentTabIndex, currentPage) {
      switch (currentTabIndex) {
        case 1:
          this.$refs.requireRef.handleSave(currentPage)
          break
        case 2:
          this.$refs.cooperationRef.handleSave(currentPage)
          break
        case 3:
          this.$refs.supplierInfoRef.handleSave(currentPage)
          break
        default:
          this.$refs.baseInfoRef.handleSave(currentPage)
      }
    },
    // 提交
    async handleSubmit() {
      await this.handleSave(this.activeTabIndex)
      const res = await this.$API.publicSourcing.submitSupplierInfo({
        rfxId: this.$route.query?.rfxId
      })
      if (res.code === 200) {
        this.$toast({ content: this.$t(`提交成功`), type: 'success' })
        this.reload()
      }
    },

    // 弹框展示
    async showDialog(message, cssClass) {
      return new Promise((resolve) => {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t(message),
            cssClass
          },
          success: resolve
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.publicSourcing-full-height {
  padding: 8px 8px 0;
  background: #fff;
  height: calc(100% - 20px);
  display: flex;
  flex-direction: column;
}
.publicSourcing-full-height.isBasic {
  height: auto;
}
.body-container {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}
.top-container {
  height: 40px;
  width: 100%;
  .top-content {
    padding: 8px 8px 0 8px;
    // background-color: #f9f9f9;
    border-radius: 8px;
    .top-info {
      display: flex;
      justify-content: space-between;
      .title {
        max-width: 80%;
        font-size: 20px;
        font-weight: 700;
        color: #292929;
        span {
          margin-right: 15px;
        }
        .sub-title {
          display: inline-block;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 75%;
          font-size: 15px;
          color: #777;
        }
      }
      .top-btns {
        text-align: right;
        margin-bottom: 10px;
      }
    }
    .top-form {
      .mt-form {
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
        justify-content: space-between;
        grid-gap: 5px;
      }
      .vxe-input,
      .vxe-select,
      .vxe-pulldown {
        width: 100%;
        height: 34px;
        line-height: 34px;
      }
      /deep/.vxe-pulldown--panel {
        min-width: unset !important;
        width: 100%;
      }
    }
  }
  .top-toggle-btn {
    width: 100%;
    div {
      width: 80px;
      height: 15px;
      line-height: 15px;
      margin: auto;
      text-align: center;
      background-color: #4a556b;
      color: #fff;
      font-weight: bold;
      border-radius: 0 0 6px 6px;
    }
  }
}

.top-hidden {
  .top-form {
    display: none;
  }
  .top-toggle-btn {
    div {
      transform: rotate(180deg);
      border-radius: 6px 6px 0 0;
    }
  }
}

.my-dropdown2 {
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 4px 0;
  max-height: 200px;
  border-radius: 4px;
  border: 1px solid #dadce0;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}
.list-item2 {
  line-height: 30px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 0 4px;

  .isSelected {
    font-weight: 700;
    color: #409eff;
  }
}
.list-item2:hover {
  background-color: #f5f7fa;
}
.empty-tip {
  width: 100%;
  line-height: 30px;
  padding: 0 4px;
  text-align: center;
  color: #c0c4cc;
}

::v-deep {
  .mt-form-item {
    margin-bottom: 15px;
    .label {
      margin: 0 10px 5px 0;
    }
  }
  .mt-tabs-container {
    background-color: #fff;
  }

  .mt-tabs-container ul.tab-container li.tab-item2.active {
    border-bottom: 2px solid #409eff;
  }
  .mt-tabs-container ul.tab-container li.tab-item2.active,
  .mt-tabs-container ul.tab-container li.tab-item2:hover {
    color: #31374e;
  }

  .vxe-textarea--inner {
    min-height: 34px;
    height: 100%;
  }
}
</style>
