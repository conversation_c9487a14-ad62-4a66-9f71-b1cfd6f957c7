<template>
  <div class="collapse-panel">
    <div class="collapse-title">
      <div class="collapse-title-wrapper" @click="handleToggle">
        <i :class="isShow ? 'vxe-icon-arrow-down' : 'vxe-icon-arrow-up'"></i>
        <span>{{ title }}</span>
      </div>
    </div>
    <div class="collapse-content" v-show="isShow"><slot name="content"></slot></div>
  </div>
</template>
<script>
export default {
  name: 'CollapsePanel',
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  watch: {},
  data() {
    return {
      isShow: true
    }
  },
  mounted() {},
  methods: {
    handleToggle() {
      this.isShow = !this.isShow
    }
  }
}
</script>
<style lang="scss">
.collapse-panel {
  margin-bottom: 16px;
  .collapse-title {
    border-bottom: 1px solid #d7d8da;
    padding-bottom: 5px;
    .collapse-title-wrapper {
      display: inline-block;
      span {
        margin-left: 5px;
        font-size: 16px;
        font-weight: bold;
      }
    }
  }
  .collapse-content {
    padding-top: 12px;
  }
}
</style>
