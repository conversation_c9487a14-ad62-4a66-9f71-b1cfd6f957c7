<template>
  <div class="supplierInfoWrapper">
    <!-- 基本信息 -->
    <collapse :title="$t('企业基本信息')">
      <div slot="content" class="form-content">
        <mt-form ref="dataFormRef" :model="dataForm" :rules="formRules">
          <mt-form-item prop="enterpriseName" :label="$t('公司名称')" label-style="top">
            <vxe-input v-model="dataForm.enterpriseName" disabled />
          </mt-form-item>
          <mt-form-item prop="establishTime" :label="$t('成立时间')" label-style="top">
            <vxe-input v-model="dataForm.establishTime" disabled />
          </mt-form-item>
          <mt-form-item prop="registerDate" :label="$t('注册日期')" label-style="top">
            <vxe-input v-model="dataForm.registerDate" disabled />
          </mt-form-item>
          <mt-form-item
            prop="companyIdentityCode"
            :label="$t('统一社会信用代码')"
            label-style="top"
          >
            <vxe-input v-model="dataForm.companyIdentityCode" disabled />
          </mt-form-item>
          <mt-form-item
            prop="registerCapital"
            :label="$t('注册资金（万元人名币）')"
            label-style="top"
          >
            <vxe-input v-model="dataForm.registerCapital" disabled />
          </mt-form-item>
          <mt-form-item prop="companyRegisterAddr" :label="$t('注册地址')" label-style="top">
            <vxe-input v-model="dataForm.companyRegisterAddr" disabled />
          </mt-form-item>
          <mt-form-item prop="companyLegalperson" :label="$t('法人代表')" label-style="top">
            <vxe-input v-model="dataForm.companyLegalperson" disabled />
          </mt-form-item>
          <mt-form-item prop="employeeNum" :label="$t('员工人数')" label-style="top">
            <vxe-input
              type="integer"
              maxlength="8"
              min="0"
              v-model="dataForm.employeeNum"
              :disabled="!editable"
              clearable
              :placeholder="$t('请输入员工人数')"
            />
          </mt-form-item>
          <mt-form-item
            prop="technicianRatio"
            :label="$t('技术人员占比（不含操作工）')"
            label-style="top"
          >
            <vxe-input
              type="number"
              maxlength="8"
              v-model="dataForm.technicianRatio"
              :disabled="!editable"
              min="0"
              max="100"
              clearable
              :placeholder="$t('请输入技术人员占比')"
              @blur="ratioBlur('technicianRatio')"
            >
              <template #suffix>
                <span>%</span>
              </template>
            </vxe-input>
          </mt-form-item>
          <mt-form-item prop="operatorRatio" :label="$t('操作工占比')" label-style="top">
            <vxe-input
              type="number"
              maxlength="8"
              min="0"
              max="100"
              v-model="dataForm.operatorRatio"
              :disabled="!editable"
              clearable
              :placeholder="$t('请输入操作工占比')"
              @blur="ratioBlur('operatorRatio')"
            >
              <template #suffix>
                <span>%</span>
              </template>
            </vxe-input>
          </mt-form-item>
          <mt-form-item prop="customerGroup" :label="$t('现有客户群体')" label-style="top">
            <vxe-select
              v-model="dataForm.customerGroup"
              :disabled="!editable"
              :options="dictItems.PUBLIC_SOURCING_CUSTOMER_GROUP"
              :option-props="{ label: 'dictName', value: 'dictCode' }"
              :placeholder="$t('请选择现有客户群体')"
            />
          </mt-form-item>
          <mt-form-item prop="enterpriseFiles" :label="$t('发票资料证明')" label-style="top">
            <UploadInput
              ref="uploader"
              v-model="dataForm.enterpriseFiles"
              :disabled="!editable"
            ></UploadInput>
          </mt-form-item>
          <mt-form-item
            prop="customersCooperation"
            :label="$t('主要客户及合作情况')"
            label-style="top"
          >
            <vxe-textarea
              v-model="dataForm.customersCooperation"
              :disabled="!editable"
              clearable
              :rows="1"
              :placeholder="$t('请输入主要客户及合作情况')"
            />
          </mt-form-item>
          <mt-form-item prop="scopeBiz" :label="$t('业务范围能力')" label-style="top">
            <vxe-textarea
              v-model="dataForm.scopeBiz"
              :disabled="!editable"
              clearable
              :rows="1"
              :placeholder="$t('请输入业务范围能力')"
            />
          </mt-form-item>
        </mt-form>
      </div>
    </collapse>
  </div>
</template>

<script>
import { Input as VxeInput, Textarea as VxeTextarea, Select as VxeSelect } from 'vxe-table'
import UploadInput from '@/components/VxeComponents/uploadInput'
import collapse from '../../components/collapse'
export default {
  name: 'BaseInfoTab',
  inject: ['reload'],
  components: {
    VxeInput,
    VxeTextarea,
    VxeSelect,
    UploadInput,
    collapse
  },
  props: {
    detailInfo: {
      type: Object,
      default: () => null
    },
    editable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      dataForm: {}, // 企业基本信息
      dictItems: {} // 字典数据
    }
  },
  computed: {
    // 基本信息校验规则
    formRules() {
      return {
        employeeNum: [{ required: true, message: this.$t('请输入员工人数'), trigger: 'blur' }],
        technicianRatio: [
          { required: true, message: this.$t('请输入技术人员占比'), trigger: 'blur' }
        ],
        operatorRatio: [{ required: true, message: this.$t('请输入操作工占比'), trigger: 'blur' }],
        customerGroup: [
          { required: true, message: this.$t('请输入现有客户群体'), trigger: 'blur' }
        ],
        enterpriseFiles: [
          { required: true, message: this.$t('请上传最近半年与该客户的发票样件'), trigger: 'blur' }
        ],
        customersCooperation: [
          { required: true, message: this.$t('请输入主要客户及合作情况'), trigger: 'blur' }
        ],
        scopeBiz: [{ required: true, message: this.$t('请输入业务范围能力'), trigger: 'blur' }]
      }
    }
  },
  mounted() {
    // 字典数据
    this.initDictItems()
    this.initData()
  },
  methods: {
    // 初始化数据
    async initData() {
      await this.$API.publicSourcing
        .querySupplierEnterprise({ rfxId: this.$route.query.rfxId })
        .then((res) => {
          if (res.code === 200) {
            this.renderData(res.data)
          }
        })
    },
    // 渲染页面数据
    renderData(data) {
      const {
        id,
        enterpriseName,
        establishTime,
        registerDate,
        companyIdentityCode,
        registerCapital,
        companyRegisterAddr,
        companyLegalperson,
        employeeNum,
        technicianRatio,
        operatorRatio,
        customerGroup,
        enterpriseFiles,
        customersCooperation,
        scopeBiz
      } = data
      // 渲染基本信息
      this.dataForm = {
        id,
        enterpriseName,
        establishTime,
        registerDate,
        companyIdentityCode,
        registerCapital,
        companyRegisterAddr,
        companyLegalperson,
        employeeNum,
        technicianRatio,
        operatorRatio,
        customerGroup,
        enterpriseFiles,
        customersCooperation,
        scopeBiz
      }
    },
    // 初始化 - 字典数据
    async initDictItems() {
      let codeList = [
        { code: 'PUBLIC_SOURCING_CUSTOMER_GROUP', type: 'string' } // 寻源原因
      ]
      await this.$API.masterData.getSupplierDictionarys(codeList).then((res) => {
        if (res.code === 200) {
          this.dictItems = res.data
        }
      })
    },
    // 保存当前信息页
    async handleSave(isCurrentPage) {
      let _submitData = {
        supplierEnterprise: { ...this.dataForm },
        rfxId: this.$route.query.rfxId
      }
      let res = await this.$API.publicSourcing.saveSupplierInfo(_submitData)
      if (isCurrentPage && res.code === 200) {
        this.$toast({ content: this.$t('保存成功！'), type: 'success' })
      }
    },
    // 技术员工占比 和 操作员工占比失去焦点校验
    ratioBlur(type) {
      let _technicianRatio = this.dataForm.technicianRatio * 100
      let _operatorRatio = this.dataForm.operatorRatio * 100
      if ((_technicianRatio + _operatorRatio) / 100 > 100) {
        this.$set(this.dataForm, type, 0)
        this.$toast({
          content: this.$t('技术人员占比与操作工占比之和不能操过100'),
          type: 'warning'
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.form-content {
  .mt-form {
    width: 100%;
    box-sizing: border-box;
    display: grid;
    grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
    justify-content: space-between;
    grid-gap: 5px;
  }
  .vxe-input,
  .vxe-select,
  .vxe-pulldown {
    width: 100%;
    height: 34px;
    line-height: 34px;
  }
  /deep/.vxe-pulldown--panel {
    min-width: unset !important;
    width: 100%;
  }
}
::v-deep {
  .mt-form-item {
    margin-bottom: 15px;
    .label {
      margin: 0 10px 5px 0;
    }
  }
  .vxe-textarea--inner {
    min-height: 34px;
    height: 100%;
  }
}
</style>
