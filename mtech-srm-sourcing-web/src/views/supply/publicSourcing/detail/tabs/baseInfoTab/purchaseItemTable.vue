<template>
  <!-- 需求采购明细 -->
  <div class="purchaseItem">
    <CustomAgGrid
      ref="CustomAgGrid"
      :toolbar="toolbar"
      :columns="columns"
      :suppress-row-transform="true"
      :row-data="tableData"
      :animate-rows="false"
      :params-type="2"
      @onGridReady="onGridReady"
      @refresh="refresh"
      @search="search"
    >
    </CustomAgGrid>
  </div>
</template>
<script>
import CustomAgGrid from '@/components/CustomAgGrid'
import { columns } from './config/purchaseItemTable'
import cellFile from '@/components/AgCellComponents/cellFile'
import { v4 as uuidv4 } from 'uuid'
export default {
  name: 'BaseInfoTab',
  components: {
    CustomAgGrid,
    // eslint-disable-next-line vue/no-unused-components
    cellFile
  },
  props: {
    // 初始化数据
    dataSource: {
      type: Array,
      default: () => []
    },
    editable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      toolbar: [],
      tableData: [],
      agGrid: null,
      columns: null
    }
  },
  watch: {
    dataSource: {
      handler(val) {
        val.length && this.initTableData()
      }
    },
    editable: {
      handler() {
        this.initColumns()
      },
      immediate: true
    }
  },
  mounted() {},
  methods: {
    initColumns() {
      this.columns = columns.map((item) => {
        let _item = { ...item }
        if (item.field === 'sourcingFiles') {
          item.cellRenderer = 'cellFile'
          item.cellRendererParams = {
            handleable: false
          }
          item.editable = false
        }
        //如果页面不可编辑，清空编辑选项
        if (!this.editable) {
          _item.editable = false
          _item.cellClass = ''
        }
        return { ..._item }
      })
    },
    // 初始化渲染表格
    initTableData() {
      this.tableData = this.dataSource.map((item, index) => {
        item.lineNo = index + 1
        item.rowId = uuidv4()
        return item
      })
    },

    onGridReady(params) {
      this.agGrid = params
    },
    refresh() {},
    search() {},
    // 表格 - 工具 - 获取表格数据
    getRowData() {
      const rowData = []
      this.agGrid.api.forEachNode(function (node) {
        rowData.push(node.data)
      })
      return rowData
    }
  }
}
</script>
<style lang="scss" scoped>
.purchaseItem {
  height: 300px;
  .grid-container {
    height: 100%;
  }
}
</style>
