<template>
  <div class="flex-full-height">
    <!-- 基本信息 -->
    <collapse :title="$t('基本信息')">
      <div slot="content" class="form-content">
        <mt-form ref="dataFormRef" :model="dataForm">
          <mt-form-item prop="rfxCode" :label="$t('寻源单号')" label-style="top">
            <vxe-input v-model="dataForm.rfxCode" disabled />
          </mt-form-item>
          <mt-form-item prop="rfxName" :label="$t('寻源标题')" label-style="top">
            <vxe-input v-model="dataForm.rfxName" disabled />
          </mt-form-item>
          <mt-form-item prop="sourceMethodName" :label="$t('寻源方式')" label-style="top">
            <vxe-input v-model="dataForm.sourceMethodName" disabled />
          </mt-form-item>
          <mt-form-item prop="deadlineDate" :label="$t('报名截止日期')" label-style="top">
            <vxe-input v-model="dataForm.deadlineDate" disabled />
          </mt-form-item>
          <mt-form-item prop="category" :label="$t('品类')" label-style="top">
            <vxe-input v-model="dataForm.category" disabled />
          </mt-form-item>
          <mt-form-item prop="company" :label="$t('需求公司')" label-style="top">
            <vxe-input v-model="dataForm.company" disabled />
          </mt-form-item>
          <mt-form-item prop="payMethodName" :label="$t('付款方式')" label-style="top">
            <vxe-input v-model="dataForm.payMethodName" disabled />
          </mt-form-item>
          <mt-form-item prop="paymentTermsName" :label="$t('付款条件')" label-style="top">
            <vxe-input v-model="dataForm.paymentTermsName" disabled />
          </mt-form-item>
          <mt-form-item prop="deliveryPlaceName" :label="$t('交货地址')" label-style="top">
            <vxe-input v-model="dataForm.deliveryPlaceName" disabled />
          </mt-form-item>
          <mt-form-item prop="cycleTime" :label="$t('交货周期（天）')" label-style="top">
            <vxe-input v-model="dataForm.cycleTime" disabled />
          </mt-form-item>
          <mt-form-item prop="expectSupplierQty" :label="$t('期望寻源数量')" label-style="top">
            <vxe-input v-model="dataForm.expectSupplierQty" disabled />
          </mt-form-item>
          <mt-form-item
            v-if="status === 3"
            prop="rejectReason"
            :label="$t('驳回原因')"
            label-style="top"
          >
            <vxe-input v-model="dataForm.rejectReason" disabled />
          </mt-form-item>
        </mt-form>
      </div>
    </collapse>
    <!-- 采购需求明细 -->
    <collapse :title="$t('采购需求明细')">
      <div slot="content">
        <PurchaseItem
          :basic-info="dataForm"
          :data-source="publicSourcingItemData"
          :editable="editable"
          ref="purchaseItemRef"
        ></PurchaseItem>
      </div>
    </collapse>
    <!-- 联系方式 -->
    <collapse :title="$t('联系方式')">
      <div slot="content" class="form-content">
        <mt-form ref="contactDataFormRef" :model="dataForm">
          <mt-form-item prop="contactUserName" :label="$t('联系人姓名')" label-style="top">
            <vxe-input v-model="dataForm.contactUserName" disabled />
          </mt-form-item>
          <mt-form-item prop="contactMobile" :label="$t('手机号')" label-style="top">
            <vxe-input v-model="dataForm.contactMobile" disabled />
          </mt-form-item>
          <mt-form-item prop="contactUserEmail" :label="$t('邮箱')" label-style="top">
            <vxe-input v-model="dataForm.contactUserEmail" disabled />
          </mt-form-item>
        </mt-form>
      </div>
    </collapse>
  </div>
</template>

<script>
import { Input as VxeInput } from 'vxe-table'
import collapse from '../../components/collapse'
import PurchaseItem from './purchaseItemTable'
import { cloneDeep, isEmpty } from 'lodash'
export default {
  name: 'BaseInfoTab',
  inject: ['reload'],
  components: {
    VxeInput,
    collapse,
    PurchaseItem
  },
  props: {
    detailInfo: {
      type: Object,
      default: () => null
    },
    status: {
      type: [String, Number],
      default: () => 0
    },
    editable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dataForm: {}, // 基本信息表单数据
      publicSourcingItemData: null, // 采购需求明细数据
      sourceMethodList: {
        open: this.$t('公开'),
        target: this.$t('邀请')
      }
    }
  },

  watch: {
    detailInfo: {
      handler(v) {
        // 渲染接口请求基本数据
        if (!isEmpty(v)) {
          this.initBaseData(v)
        }
      },
      deep: true
    }
  },

  mounted() {},

  methods: {
    initBaseData(data) {
      const {
        rfxCode,
        rfxName,
        sourceMethod,
        deadlineDate,
        categoryCode,
        categoryName,
        companyCode,
        companyName,
        payMethodCode,
        payMethodName,
        paymentTermsCode,
        paymentTermsName,
        deliveryPlaceName,
        cycleTime,
        expectSupplierQty,
        rejectReason,
        contactUserId,
        contactUserName,
        genderCode,
        contactMobile,
        contactUserEmail,
        supplierBiddingItems
      } = data
      // 渲染基本信息
      this.dataForm = {
        rfxCode,
        rfxName,
        sourceMethod,
        sourceMethodName: this.sourceMethodList[sourceMethod],
        deadlineDate,
        categoryCode,
        categoryName,
        category: categoryCode + ' - ' + categoryName,
        companyCode,
        companyName,
        company: companyCode + ' - ' + companyName,
        payMethodCode,
        payMethodName,
        paymentTermsCode,
        paymentTermsName,
        deliveryPlaceName,
        cycleTime,
        expectSupplierQty,
        rejectReason,
        contactUserId,
        contactUserName,
        genderCode,
        contactMobile,
        contactUserEmail
      }
      // 采购需求明细
      this.publicSourcingItemData = cloneDeep(supplierBiddingItems)
    },
    // 保存当前信息页
    async handleSave(isCurrentPage) {
      let _submitData = this.getSubmitData()
      let res = await this.$API.publicSourcing.saveSupplierInfo(_submitData)
      if (isCurrentPage && res.code === 200) {
        this.$toast({ content: this.$t('保存成功！'), type: 'success' })
      }
      return res
    },
    // 获取信息
    getSubmitData() {
      let purchaseItemData = this.$refs.purchaseItemRef.tableData
      let params = {
        supplierBiddingItems: cloneDeep(purchaseItemData),
        rfxId: this.$route.query?.rfxId
      }
      return params
    }
  }
}
</script>

<style scoped lang="scss">
.form-content {
  .mt-form {
    width: 100%;
    box-sizing: border-box;
    display: grid;
    grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
    justify-content: space-between;
    grid-gap: 5px;
  }
  .vxe-input,
  .vxe-select,
  .vxe-pulldown {
    width: 100%;
    height: 34px;
    line-height: 34px;
  }
  /deep/.vxe-pulldown--panel {
    min-width: unset !important;
    width: 100%;
  }
}

::v-deep {
  .mt-form-item {
    margin-bottom: 15px;
    .label {
      margin: 0 10px 5px 0;
    }
  }
  .mt-tabs-container {
    background-color: #fff;
  }

  .mt-tabs-container ul.tab-container li.tab-item2.active {
    border-bottom: 2px solid #409eff;
  }
  .mt-tabs-container ul.tab-container li.tab-item2.active,
  .mt-tabs-container ul.tab-container li.tab-item2:hover {
    color: #31374e;
  }

  .vxe-textarea--inner {
    min-height: 34px;
    height: 100%;
  }
}
</style>
