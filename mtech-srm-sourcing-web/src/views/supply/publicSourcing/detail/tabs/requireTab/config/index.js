import { i18n } from '@/main.js'
const requireTypeMap = {
  CXZG: i18n.t('产/销资格'),
  JYNX: i18n.t('经营年限'),
  ZLTX: i18n.t('质量体系'),
  HPZG: i18n.t('环评资格'),
  XSJE: i18n.t('销售金额'),
  CNYQ: i18n.t('产能要求'),
  PZYQ: i18n.t('品质要求'),
  JSYQ: i18n.t('技术要求'),
  QTYQ: i18n.t('其它要求')
}
export const columns = [
  {
    field: 'lineNo',
    headerName: i18n.t('行号'),
    width: 65
  },
  {
    field: 'requireType',
    headerName: i18n.t('需求项目'),
    width: 100,
    valueFormatter: (params) => {
      return requireTypeMap[params.value]
    }
  },
  {
    field: 'requireTypeDesc',
    headerName: i18n.t('要求说明'),
    width: 680
  },
  {
    field: 'sourcingFiles',
    headerName: i18n.t('参考附件'),
    width: 120
  },
  {
    field: 'supplierReplyOpinion',
    headerName: i18n.t('供应商回复说明'),
    required: true,
    editable: (params) => {
      return (
        params.data.supplierReplyOpinion?.indexOf('@input') >= 0 ||
        params.data.supplierReplyOpinion?.indexOf('@number') >= 0
      )
    },
    option: 'customEdit',
    width: 220,
    editConfig: {
      type: 'cellDesc',
      props: {}
    },
    cellClass: (params) => {
      return params.data.supplierReplyOpinion?.indexOf('@input') === -1 &&
        params.data.supplierReplyOpinion?.indexOf('@number') === -1
        ? 'singleCellDisable'
        : ''
    }
  },
  {
    field: 'supplierSourcingFiles',
    headerName: i18n.t('证明文件'),
    width: 120,
    required: true
  }
]
