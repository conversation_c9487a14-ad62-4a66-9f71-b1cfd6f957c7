<template>
  <!-- 合作承诺 -->
  <div class="cooperationWrapper">
    <CustomAgGrid
      ref="CustomAgGrid"
      :columns="columns"
      :suppress-row-transform="true"
      :row-data="tableData"
      :animate-rows="false"
      :params-type="2"
      :row-height="40"
      :show-statusbar="false"
      @onGridReady="onGridReady"
      @cell-value-changed="cellValueChanged"
      @refresh="refresh"
    >
    </CustomAgGrid>
  </div>
</template>
<script>
import CustomAgGrid from '@/components/CustomAgGrid'
import { columns } from './config/cooperationTable'
import cellFile from '@/components/AgCellComponents/cellFile'
import cellDescView from '@/components/AgCellComponents/cellDescView'
import { v4 as uuidv4 } from 'uuid'
import { cloneDeep } from 'lodash'
export default {
  name: 'CooperationTable',
  inject: ['reload'],
  components: {
    CustomAgGrid,
    // eslint-disable-next-line vue/no-unused-components
    cellFile,
    // eslint-disable-next-line vue/no-unused-components
    cellDescView
  },
  props: {
    // 初始化数据
    dataSource: {
      type: Array,
      default: () => []
    },
    // 是否可编辑
    editable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      tableData: [],
      cacheTableData: [],
      agGrid: null
    }
  },
  watch: {
    dataSource: {
      handler(val) {
        val.length && this.initTableData()
      }
    },
    editable: {
      handler() {
        this.initColumns()
      },
      immediate: true
    }
  },
  computed: {},
  mounted() {},
  methods: {
    initColumns() {
      this.columns = columns.map((item) => {
        let _item = cloneDeep(item)
        if (['sourcingFiles', 'referenceAttachment'].includes(item.field)) {
          _item.cellRenderer = 'cellFile'
          _item.editable = false
          _item.cellRendererParams = {
            handleable:
              _item.field === 'referenceAttachment' || !this.editable ? false : this.editable,
            isTransform: true // 该字段判断是否需要转换源数据（不影响询报价）
          }
        }
        //如果页面不可编辑，清空编辑选项
        if (!this.editable) {
          _item.editable = false
          if (['reportingMatters', 'value'].includes(_item.field)) {
            _item.cellClass = ''
          }
        }
        return { ..._item }
      })
    },
    // 初始化渲染表格
    initTableData() {
      this.tableData = this.dataSource.map((item) => {
        item.rowId = uuidv4()
        return item
      })
      this.cacheTableData = cloneDeep(this.tableData)
    },

    // 保存数据
    async handleSave() {
      let _tableData = this.getRowData()
      let res = await this.$API.publicSourcing.saveRequireList({
        requireConfigDTOS: _tableData,
        rfxId: this.$route.query?.id
      })
      if (res.code === 200) {
        this.$toast({
          content: this.$t('保存成功'),
          type: 'success'
        })
        this.initTableData()
      }
    },
    onGridReady(params) {
      this.agGrid = params
    },

    cellValueChanged(params) {
      // 报备结果联动
      if (
        params.colDef.field === 'value' &&
        ['has_resigned_employees', 'has_rel_company'].includes(params.data.reportingMattersCode)
      ) {
        this.$emit('cellValueChanged', {
          value: params.value == '1',
          field: params.data.reportingMattersCode
        })
      }
    },
    // 表格 - 工具 - 获取表格数据
    getRowData() {
      const rowData = []
      this.agGrid.api.forEachNode(function (node) {
        rowData.push(node.data)
      })
      return rowData
    },
    refresh() {
      this.tableData = cloneDeep(this.cacheTableData)
      let isShowTclFormer
      let isShowAffiliatEnterprise
      this.tableData?.forEach((item) => {
        if (item.reportingMattersCode === 'has_resigned_employees' && item.value === 1) {
          isShowTclFormer = true
        }
        if (item.reportingMattersCode === 'has_rel_company' && item.value === 1) {
          isShowAffiliatEnterprise = true
        }
      })
      this.$emit('refresh', { isShowTclFormer, isShowAffiliatEnterprise })
    }
  }
}
</script>
<style lang="scss">
.cooperationWrapper {
  height: 300px;
  .grid-container {
    height: 100%;
  }
  .ag-theme-alpine .ag-cell-inline-editing {
    height: 40px !important;
  }
  .mt-input-number {
    width: 100px;
  }
  .mt-input-number .step-box .one-icon {
    display: none;
  }
  .mt-input-number input[type='number'] {
    padding-left: 10px;
    padding-right: 10px;
  }
  .mt-input-number .icon-clear {
    margin-top: 6px;
    right: 10px;
  }
  .mt-input {
    width: 100%;
  }
}
</style>
