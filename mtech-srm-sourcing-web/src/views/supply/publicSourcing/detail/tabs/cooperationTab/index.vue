<template>
  <div class="flex-full-height">
    <!-- 合作承诺 -->
    <collapse :title="$t('合作承诺')">
      <div slot="content">
        <cooperation
          ref="cooperationRef"
          :editable="editable"
          :data-source="commitmentDTOList"
          @cellValueChanged="cellValueChanged"
          @refresh="refresh"
        ></cooperation>
      </div>
    </collapse>
    <!-- 股东、高管及对口TCL业务人员是TCL离职人员申报信息 -->
    <collapse
      :title="$t('股东、高管及对口TCL业务人员是TCL离职人员申报信息')"
      v-show="isShowTclFormer"
    >
      <div slot="content">
        <tclFormer
          :data-source="supplierEmployees"
          :editable="editable"
          ref="tclFormerRef"
        ></tclFormer>
      </div>
    </collapse>
    <!-- 关联企业信息 -->
    <collapse :title="$t('关联企业信息')" v-show="isShowAffiliatEnterprise">
      <div slot="content">
        <affiliatEnterprise
          :data-source="supplierCompanies"
          :editable="editable"
          ref="affiliatEnterpriseRef"
        ></affiliatEnterprise>
      </div>
    </collapse>
  </div>
</template>

<script>
import collapse from '../../components/collapse'
import cooperation from './cooperationTable' //合作承诺
import tclFormer from './tclFormerTable' // tcl离职人员
import affiliatEnterprise from './affiliatEnterpriseTable' // 关联企业信息
import { cloneDeep } from 'lodash'
export default {
  name: 'BaseInfoTab',
  inject: ['reload'],
  components: {
    collapse,
    cooperation,
    tclFormer,
    affiliatEnterprise
  },
  props: {
    detailInfo: {
      type: Object,
      default: () => null
    },
    editable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      commitmentDTOList: null,
      supplierEmployees: null,
      supplierCompanies: null,
      isShowTclFormer: false,
      isShowAffiliatEnterprise: false
    }
  },
  watch: {},
  mounted() {
    this.initData()
  },
  methods: {
    cellValueChanged(e) {
      let { value, field } = e
      if (field === 'has_resigned_employees') {
        this.isShowTclFormer = value
      } else if (field === 'has_rel_company') {
        this.isShowAffiliatEnterprise = value
      }
    },
    // 初始化数据
    async initData() {
      await this.$API.publicSourcing
        .querySupplierCommitments({ rfxId: this.$route.query.rfxId })
        .then((res) => {
          if (res.code === 200) {
            this.renderData(res.data || {})
          }
        })
    },
    renderData(data) {
      const { commitmentDTOList, supplierCompanies, supplierEmployees } = data
      commitmentDTOList?.forEach((item) => {
        if (item.reportingMattersCode === 'has_resigned_employees' && item.value === 1) {
          this.isShowTclFormer = true
        }
        if (item.reportingMattersCode === 'has_rel_company' && item.value === 1) {
          this.isShowAffiliatEnterprise = true
        }
      })
      // 合作承诺
      this.commitmentDTOList = cloneDeep(commitmentDTOList)
      // 申报信息
      this.supplierEmployees = cloneDeep(supplierEmployees)
      // 关联企业信息
      this.supplierCompanies = cloneDeep(supplierCompanies)
    },
    // dialog - cancel
    cancel() {
      this.$refs.dialog.ejsRef.hide()
    },
    // dialog - confirm
    confirm() {
      // 保存数据
      this.handleSave()
      this.$refs.dialog.ejsRef.hide()
    },
    // 保存当前信息页
    async handleSave(isCurrentPage) {
      let _submitData = this.getSubmitData()
      let res = await this.$API.publicSourcing.saveSupplierInfo(_submitData)
      if (isCurrentPage && res.code === 200) {
        this.$toast({ content: this.$t('保存成功！'), type: 'success' })
      }
      return res
    },
    // 获取信息
    getSubmitData() {
      let commitmentDTOList = this.$refs.cooperationRef.tableData
      let supplierEmployees = this.$refs?.tclFormerRef?.tableData || null
      let supplierCompanies = this.$refs?.affiliatEnterpriseRef?.tableData || null
      let params = {
        commitmentDTOList,
        supplierEmployees,
        supplierCompanies,
        rfxId: this.$route.query?.rfxId
      }
      return params
    },
    refresh(data) {
      const { isShowTclFormer, isShowAffiliatEnterprise } = data
      this.isShowTclFormer = isShowTclFormer
      this.isShowAffiliatEnterprise = isShowAffiliatEnterprise
    }
  }
}
</script>

<style scoped lang="scss">
.form-content {
  .mt-form {
    width: 100%;
    box-sizing: border-box;
    display: grid;
    grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
    justify-content: space-between;
    grid-gap: 5px;
  }
  .vxe-input,
  .vxe-select,
  .vxe-pulldown {
    width: 100%;
    height: 34px;
    line-height: 34px;
  }
  /deep/.vxe-pulldown--panel {
    min-width: unset !important;
    width: 100%;
  }
}

::v-deep {
  .mt-form-item {
    margin-bottom: 15px;
    .label {
      margin: 0 10px 5px 0;
    }
  }
  .mt-tabs-container {
    background-color: #fff;
  }

  .mt-tabs-container ul.tab-container li.tab-item2.active {
    border-bottom: 2px solid #409eff;
  }
  .mt-tabs-container ul.tab-container li.tab-item2.active,
  .mt-tabs-container ul.tab-container li.tab-item2:hover {
    color: #31374e;
  }

  .vxe-textarea--inner {
    min-height: 34px;
    height: 100%;
  }
}
</style>
