import { i18n } from '@/main.js'

export const toolbar = [
  { id: 'Add', icon: '', title: i18n.t('新增') },
  { id: 'Delete', icon: '', title: i18n.t('删除') }
]

export const columns = [
  { option: 'checkboxSelection', width: 55 },
  {
    field: 'companyName',
    headerName: i18n.t('关联企业名称'),
    editable: true
  },
  {
    field: 'businessesLicense',
    headerName: i18n.t('关联企业统一社会信用代码'),
    editable: true
  },
  {
    field: 'associateRelation',
    headerName: i18n.t('关联关系'),
    editable: true
  },
  {
    field: 'relationDesc',
    headerName: i18n.t('详细关系说明'),
    editable: true
  },
  {
    field: 'isBlacklist',
    headerName: i18n.t('是否黑名单'),
    width: 150,
    editable: true,
    option: 'customEdit',
    editConfig: {
      type: 'select',
      props: {
        'show-clear-button': true,
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { value: 0, text: i18n.t('否') },
          { value: 1, text: i18n.t('是') }
        ]
      }
    }
  }
]
