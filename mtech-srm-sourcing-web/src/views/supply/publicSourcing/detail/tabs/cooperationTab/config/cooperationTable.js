import { i18n } from '@/main.js'
export const whetherMapList = [
  { text: i18n.t('否'), value: '0' },
  { text: i18n.t('是'), value: '1' }
]
export const whetherMap = {
  0: i18n.t('否'),
  1: i18n.t('是')
}

export const columns = [
  {
    field: 'reportingMatters',
    headerName: i18n.t('企业合规报备事项'),
    option: 'customEdit',
    cellClass: 'singleCellDisable',
    width: 400
  },
  {
    field: 'value',
    headerName: i18n.t('报备结果'),
    editable: true,
    option: 'customEdit',
    width: 200,
    editConfig: {
      type: 'select',
      props: {
        'show-clear-button': true,
        dataSource: whetherMapList
      }
    },
    cellClass: 'cellEditable',
    valueFormatter: (params) => {
      return whetherMap[params.value]
    }
  },
  {
    field: 'commitmentMatters',
    headerName: i18n.t('承诺事项'),
    width: 400,
    rowSpan: (params) => {
      return params.data.reportingMattersCode === 'is_blacklist' ? 4 : 1
    },
    cellClass: (params) => {
      return params.data.reportingMattersCode === 'is_blacklist' ? 'mergeCell' : ''
    }
  },
  {
    field: 'sourcingFiles',
    headerName: i18n.t('附件'),
    required: true,
    width: 200,
    rowSpan: (params) => {
      return params.data.reportingMattersCode === 'is_blacklist' ? 4 : 1
    },
    cellClass: (params) => {
      return params.data.reportingMattersCode === 'is_blacklist' ? 'mergeCell' : ''
    }
  },
  {
    field: 'referenceAttachment',
    headerName: i18n.t('参考附件'),
    width: 200,
    rowSpan: (params) => {
      return params.data.reportingMattersCode === 'is_blacklist' ? 4 : 1
    },
    cellClass: (params) => {
      return params.data.reportingMattersCode === 'is_blacklist' ? 'mergeCell' : ''
    }
  }
]
