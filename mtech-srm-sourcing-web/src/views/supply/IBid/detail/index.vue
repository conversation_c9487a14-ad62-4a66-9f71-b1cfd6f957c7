<template>
  <!-- 大厅详情页面 -->
  <div class="full-height hall-detail">
    <!-- 流程看板 -->
    <!-- <div
      :class="['progress-kanban', isKanbanBigger && 'progress-kanban-bigger']"
    >
      <process-kanban :is-kanban-bigger="isKanbanBigger"></process-kanban>

      <div class="shrink" @click="isKanbanBigger = !isKanbanBigger">
        <mt-icon v-show="isKanbanBigger" name="icon_arrow_left"></mt-icon>
        <mt-icon v-show="!isKanbanBigger" name="icon_arrow_right"></mt-icon>
      </div>
    </div> -->

    <!-- 中间内容 -->
    <div :class="['main-context', isKanbanBigger && 'main-context-small']">
      <!-- 头部 -->
      <top-info-view
        :quoted-price-data="quotedPriceData"
        :tab-source="tabSource"
        @mainparticipateButton="mainparticipateButton"
        @mainSubmitButton="mainSubmitButton"
        @mainWaiveButton="mainWaiveButton"
        @supplierTenderExport="supplierTenderExport"
      ></top-info-view>
      <!-- tabs切换 -->
      <mt-tabs
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabSource"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>
      <div class="middle-wrap">
        <!-- 1. 大厅 -->
        <tab-hall class="tab-hall" v-if="moduleType == 4"></tab-hall>

        <!-- 2. 采购明细 -->
        <tab-purchase-detail
          ref="purchaseDetailRef"
          v-else-if="moduleType == 14"
          :submit-field="submitField"
          :mandatory-arr="mandatoryArr"
          :field-defines="fieldDefines"
          :quoted-price-data="quotedPriceData"
          @mainparticipateButton="mainparticipateButton"
          @monitorRequotation="monitorRequotation"
          get-data-url="getSupplierTenderList"
        ></tab-purchase-detail>
        <!-- 3. 相关文件 -->
        <tab-documents
          v-else-if="moduleType == 2"
          :quoted-price-data="quotedPriceData"
        ></tab-documents>
        <!-- 4. 说明 -->
        <tab-explain v-else-if="moduleType == 3" :remark="quotedPriceData.desc"></tab-explain>
        <!-- 5. 项目计划 -->
        <!-- <tab-project-plan
          v-else-if="tabIndex == 4"
        ></tab-project-plan> -->
        <!-- 5. 报价 -->
        <tab-quoted-price
          :quoted-price-data="quotedPriceData"
          v-else-if="moduleType == -2"
        ></tab-quoted-price>
        <!-- 资质审查 -->
        <tab-review-qualification
          :quoted-price-data="quotedPriceData"
          v-else-if="moduleType == 24"
        ></tab-review-qualification>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { Formatter } from '@/utils/ej/dataGrid'

// import fixedPoint from "src/apis/modules/purchase/fixedPoint";
// 默认看板小，中间内容大
export default {
  props: {
    clickSave: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  components: {
    //顶部详情
    topInfoView: () => import('./components/topInfo.vue'),
    //大厅
    tabHall: () =>
      import(
        /* webpackChunkName: "router/supply/quotationBidding/detail/tabs/tabHall" */ './pages/tabHall.vue'
      ),
    //相关文件
    tabDocuments: () =>
      import(
        /* webpackChunkName: "router/supply/quotationBidding/detail/tabs/documents" */ './pages/documents/index.vue'
      ),
    // 说明
    tabExplain: () =>
      import(
        /* webpackChunkName: "router/supply/quotationBidding/detail/tabs/tabExplain" */ './pages/tabExplain.vue'
      ),
    //报价
    tabQuotedPrice: () =>
      import(
        /* webpackChunkName: "router/supply/quotationBidding/detail/tabs/tabQuotedPrice" */ './pages/tabQuotedPrice.vue'
      ),
    //采购明细
    tabPurchaseDetail: () =>
      import(
        /* webpackChunkName: "router/supply/IBid/detail/pages/purchaseDetails" */ './pages/purchaseDetails/index.vue'
      ),
    //资质审查
    tabReviewQualification: () =>
      import(
        /* webpackChunkName: "router/supply/IBid/detail/pages/reviewQualification " */ './pages/reviewQualification/index.vue'
      )
  },
  data() {
    return {
      //主数据
      quotedPriceData: {},
      isKanbanBigger: false, // 左边看板更大
      moduleType: 2, // 当前选中的tab
      tabSource: [],
      fieldDefines: [],
      submitField: [], //可以编辑数组
      mandatoryArr: [] //必填数组
    }
  },
  mounted() {
    this.$bus.$on('changeTab', (e, item) => {
      console.log(item)
      this.moduleType = item.moduleType
      this.fieldDefines = item.fieldDefines
    })
    this.getData(this.rfxId, this.bidingMode)
    this.getTabs(this.rfxId, this.bidingMode)
  },
  computed: {
    rfxId() {
      return this.$route.query.rfxId
    },
    bidingMode() {
      return this.$route.query.bidingMode
    }
  },
  methods: {
    handleSelectTab(e, item) {
      this.moduleType = item.moduleType
    },

    //$emit点击参与
    mainparticipateButton() {
      this.getData(this.rfxId, this.bidingMode)
      // console.log(this.quotedPriceData);
    },
    // 报价单导出
    async supplierTenderExport() {
      // 31为报价中 32为议价中
      // 报价阶段取当前报价，议价阶段取议价
      this.$store.commit('startLoading')
      const transferStatus = this.quotedPriceData?.transferStatus
      let buffer = await this.$API.supplierTender
        .export({
          rfxId: this.rfxId,
          // 报价标识 0 当前报价 1 历史报价 2 议价
          tabType: transferStatus === 31 ? 0 : transferStatus === 32 ? 2 : 1,
          queryBuilderDTO: {
            page: {
              current: 1,
              size: 200
            }
          }
        })
        .catch(() => {})
      buffer?.data && (buffer = buffer.data) // "@mtech-common/http" 1.0.0 版本被挂载到 data, 0.15.1 版本是直接返回
      if (buffer instanceof Blob) {
        if (buffer.type === 'application/json') {
          try {
            const json = JSON.parse(await buffer.text())
            if (json.msg) {
              this.$toast({ content: json.msg, type: 'warning' })
            }
          } catch (error) {
            console.warn(error)
          }
        } else if (buffer.type === 'application/pdf') {
          const url = URL.createObjectURL(buffer)
          window.open(url)
        }
      }
      this.$store.commit('endLoading')
    },
    //$emit点击提交
    mainSubmitButton() {
      this.$refs.purchaseDetailRef.endEdit()
      let actionObj = this.$refs.purchaseDetailRef.actionObj
      let tabsNumber = this.$refs.purchaseDetailRef.tabsNumber

      if (this.quotedPriceData.bidStatus != 3) {
        if (actionObj.length > 0) {
          let rfxId = this.quotedPriceData.rfxId
          let tenantId = this.quotedPriceData.tenantId
          let arr = []
          let mandatoryArr = []
          //统一arr数组length长度
          for (let i = 0; i < actionObj.length; i++) {
            arr.push({})
            mandatoryArr.push({})
          }
          //arr对象内添加code
          for (let i = 0; i < arr.length; i++) {
            for (let j = 0; j < this.submitField.length; j++) {
              let FieldCode = this.submitField[j].fieldCode
              arr[i][FieldCode] = actionObj[i][FieldCode]
              arr[i][FieldCode] = actionObj[i]['biddingItemDTO'][FieldCode]
            }
          }

          //mandatoryArr对象内添加code
          for (let i = 0; i < mandatoryArr.length; i++) {
            //2
            for (let j = 0; j < this.mandatoryArr.length; j++) {
              //必填 18个字段
              let FieldCode = this.mandatoryArr[j].fieldCode
              mandatoryArr[i][FieldCode] = actionObj[i][FieldCode]
              mandatoryArr[i][FieldCode] = actionObj[i]['biddingItemDTO'][FieldCode]
            }
          }
          //开关
          let flag = true
          for (let i = 0; i < mandatoryArr.length; i++) {
            for (let key in mandatoryArr[i]) {
              if (mandatoryArr[i][key] == undefined || mandatoryArr[i][key] == null) {
                flag = false
                break
              }
            }
          }
          if (flag) {
            for (let i = 0; i < arr.length; i++) {
              arr[i].biddingId = actionObj[i]['biddingItemDTO'].biddingId
                ? actionObj[i]['biddingItemDTO'].biddingId
                : null
              arr[i].biddingItemId = actionObj[i]['biddingItemDTO'].biddingItemId
                ? actionObj[i]['biddingItemDTO'].biddingItemId
                : null
              arr[i].rfxItemId = actionObj[i].rfxItemId ? actionObj[i].rfxItemId : null
              if (arr[i]['quoteEffectiveEndDate']) {
                arr[i]['quoteEffectiveEndDate'] = this.$utils.formatTime(
                  arr[i]['quoteEffectiveEndDate']
                )
              }
              if (arr[i]['quoteEffectiveStartDate']) {
                arr[i]['quoteEffectiveStartDate'] = this.$utils.formatTime(
                  arr[i]['quoteEffectiveStartDate']
                )
              }
            }
            let param1 = {}
            if (tabsNumber == 0) {
              param1 = {
                abateFlag: false,
                bidPriceItems: arr,
                rfxId: rfxId,
                submitStatus: 1,
                tenantId: tenantId
              }
            }
            if (tabsNumber == 2) {
              param1 = {
                abateFlag: true,
                bidPriceItems: arr,
                rfxId: rfxId,
                submitStatus: 1,
                tenantId: tenantId
              }
            }
            this.$API.supplyQdetail.priceSave(param1).then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('操作成功'),
                  type: 'success'
                })
                this.getData(this.rfxId, this.bidingMode)
                this.$refs.purchaseDetailRef.$refs.templateRef.refreshCurrentGridData()
                this.$router.go(-1)
              }
            })
          } else {
            this.$toast({ content: this.$t('请输入必填项'), type: 'warning' })
            return
          }
        } else {
          this.$toast({
            content: this.$t('没有编辑数据不能保存'),
            type: 'warning'
          })
          return
        }
      } else {
        this.$toast({ content: this.$t('已放弃不能提交'), type: 'warning' })
      }
    },
    //$emit点击放弃
    mainWaiveButton() {
      this.$refs.purchaseDetailRef.endEdit()
      let rfxId = this.quotedPriceData.rfxId

      let param1 = {
        abateFlag: false,
        rfxId: rfxId,
        submitStatus: 2
      }
      console.log(param1)
      this.$API.supplyQdetail.priceSave(param1).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.getData(this.rfxId, this.bidingMode)
          this.$refs.purchaseDetailRef.$refs.templateRef.refreshCurrentGridData()
          this.$router.go(-1)
        }
      })
    },

    //监听点击重新报价
    monitorRequotation() {
      this.getData(this.rfxId, this.bidingMode)
    },
    // 获取详情
    getData(rfxId = '', bidingMode = '') {
      this.$API.supplyQdetail.tenderDetail(rfxId, bidingMode).then((r) => {
        if (r.code == 200) {
          this.quotedPriceData = r.data
        }
      })
    },
    // 获取tabs数据
    // mt_supplier_rfx_item_ext
    // itemExtMap
    getTabs(rfxId = '', bidingMode = '') {
      // let r = moduleData;
      this.$API.supplyQdetail.getTabs(rfxId, bidingMode).then((r) => {
        this.tabSource = [
          // { title: this.$t("大厅"), moduleType: 4 },
          // { title: this.$t("资质审查"), moduleType: 1 },
        ]
        if (r.data && r.data.moduleItems) {
          r.data.moduleItems.forEach((e) => {
            // 处理采购明细的表头
            let _columnData = []
            if (e.moduleType == 14 && e.fieldDefines) {
              //渲染表头数组
              _columnData = [
                {
                  type: 'checkbox',
                  width: '50',
                  showInColumnChooser: false
                }
              ]
              // console.log("submitStatus", this.quotedPriceData);
              e.fieldDefines.forEach((item) => {
                let name = ''
                //submitStatus == 1 不能编辑  == 0 可以编辑
                console.log(this.quotedPriceData.submitStatus)
                if (this.quotedPriceData.submitStatus == 1) {
                  // console.log("可以编辑的item", item);
                  //判断可以编辑
                  if (item.tableName == 'mt_supplier_bidding_item') {
                    // 如果是必填 0-非必填；1-必填；2-无需配置
                    if (item.required == 1) {
                      //必填数组
                      this.mandatoryArr.push(item)
                      //设置必填-表头
                      item.headerTemplate = () => {
                        return {
                          template: Vue.component('requiredCell', {
                            template: `
                        <div class="headers">
                          <span style="color: red">*</span>
                          <span class="e-headertext">{{fieldName}}</span>
                        </div>
                      `,
                            data() {
                              return {
                                data: {},
                                fieldName: ''
                              }
                            },
                            mounted() {
                              this.fieldName = item.fieldName
                            }
                          })
                        }
                      }
                    }
                    name = 'biddingItemDTO'
                    // 时间
                    if (
                      item.fieldCode == 'quoteEffectiveEndDate' ||
                      item.fieldCode == 'quoteEffectiveStartDate'
                    ) {
                      _columnData.push({
                        field: `${name}.${item.fieldCode}`,
                        headerText: item.fieldName,
                        queryType: 'string',
                        headerTemplate: item?.headerTemplate,
                        allowEditing: false,
                        editType: 'datepickeredit',
                        type: 'date',
                        format: 'yyyy-MM-dd',
                        edit: {
                          params: {
                            format: 'yyyy-MM-dd'
                          }
                        },
                        formatter: Formatter.createFmtDatetime('YYYY-MM-DD')
                      })
                    } else {
                      _columnData.push({
                        field: `${name}.${item.fieldCode}`,
                        headerText: item.fieldName,
                        headerTemplate: item?.headerTemplate,
                        queryType: 'string',
                        allowEditing: false
                      })
                    }

                    //需要维护的数组
                    this.submitField.push(item)
                  } else if (item.tableName == 'mt_supplier_rfx_item_ext') {
                    name = 'itemExtMap'
                    _columnData.push({
                      field: `${name}.${item.fieldCode}`,
                      headerText: item.fieldName,
                      headerTemplate: item?.headerTemplate,
                      queryType: 'string',
                      allowEditing: false
                    })
                  } else {
                    _columnData.push({
                      field: item.fieldCode,
                      headerText: item.fieldName,
                      headerTemplate: item?.headerTemplate,
                      queryType: 'string',
                      allowEditing: false
                    })
                  }
                }
                if (this.quotedPriceData.submitStatus == 0) {
                  //判断可以编辑
                  if (item.tableName == 'mt_supplier_bidding_item') {
                    // 如果是必填 0-非必填；1-必填；2-无需配置
                    if (item.required == 1) {
                      //必填数组
                      this.mandatoryArr.push(item)
                      //设置必填-表头
                      item.headerTemplate = () => {
                        return {
                          template: Vue.component('requiredCell', {
                            template: `
                        <div class="headers">
                          <span style="color: red">*</span>
                          <span class="e-headertext">{{fieldName}}</span>
                        </div>
                      `,
                            data() {
                              return {
                                data: {},
                                fieldName: ''
                              }
                            },
                            mounted() {
                              this.fieldName = item.fieldName
                            }
                          })
                        }
                      }
                    }
                    // console.log(item);
                    name = 'biddingItemDTO'
                    // 时间
                    if (
                      item.fieldCode == 'quoteEffectiveEndDate' ||
                      item.fieldCode == 'quoteEffectiveStartDate'
                    ) {
                      _columnData.push({
                        field: `${name}.${item.fieldCode}`,
                        headerText: item.fieldName,
                        queryType: 'string',
                        headerTemplate: item?.headerTemplate,
                        allowEditing: true,
                        editType: 'datepickeredit',
                        type: 'date',
                        format: 'yyyy-MM-dd'
                      })
                    } else {
                      _columnData.push({
                        field: `${name}.${item.fieldCode}`,
                        headerText: item.fieldName,
                        headerTemplate: item?.headerTemplate,
                        queryType: 'string',
                        allowEditing: true
                      })
                    }

                    //需要维护的数组
                    this.submitField.push(item)
                  } else if (item.tableName == 'mt_supplier_rfx_item_ext') {
                    name = 'itemExtMap'
                    _columnData.push({
                      field: `${name}.${item.fieldCode}`,
                      headerText: item.fieldName,
                      headerTemplate: item?.headerTemplate,
                      queryType: 'string',
                      allowEditing: false
                    })
                  } else {
                    _columnData.push({
                      field: item.fieldCode,
                      headerText: item.fieldName,
                      headerTemplate: item?.headerTemplate,
                      queryType: 'string',
                      allowEditing: false
                    })
                  }
                }
              })
            }

            this.tabSource.push({
              title: e.moduleName,
              moduleType: e.moduleType,
              fieldDefines: e.fieldDefines ? _columnData : null
            })
          })
        }
        // console.log(this.mandatoryArr);
        // console.log(this.tabSource);
        this.moduleType = this.tabSource[0].moduleType
        if (this.moduleType == 14) {
          this.fieldDefines = this.tabSource[0].fieldDefines
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// 主内容部分，按1440宽计算的
$mainMinWidth: 1224px; // 再小顶部的按钮那边就会换行了
$mainMinHeight: 668px;
.hall-detail {
  width: 100%;
  padding: 20px 0 0 0;
  display: flex;

  .progress-kanban {
    width: 96px;
    height: 100%;
    position: relative;
    transition: all 0.5s ease-in-out;
    flex-shrink: 0;
    background: #fff;
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px 0 0 0;

    &-bigger {
      width: 250px;
    }

    .shrink {
      cursor: pointer;
      width: 12px;
      height: 60px;
      background: rgba(243, 243, 243, 1);
      border-radius: 8px 0 0 8px;
      position: absolute;
      right: 0;
      top: calc((100% - 60px) / 2);
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .mt-icons {
        color: #6c7a8f;
        font-size: 12px;
        transform: scale(0.8);
      }
    }
  }

  .main-context {
    flex: 1;
    transition: all 0.5s ease-in-out;
    overflow-x: auto;
    overflow-y: hidden;
    display: flex;
    flex-direction: column;

    .top-info {
      min-width: $mainMinWidth;
      height: 120px;
      background: #f5f8fb;
      border: 1px solid #e8e8e8;
      border-left: 0;
      border-radius: 0 8px 0 0;
      flex-shrink: 0;
      display: flex;
    }

    .middle-wrap {
      width: 100%;
      min-width: $mainMinWidth;
      flex: 1;
      padding: 10px 0 10px 10px;
      overflow-y: auto;
      .saveButton {
        margin: 20px;
      }
      .tab-hall {
        height: 100%;
        min-height: $mainMinHeight;
      }
    }
  }
}
</style>
