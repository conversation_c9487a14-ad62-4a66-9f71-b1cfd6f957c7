import { i18n } from '@/main.js'
export const tabSource = [
  // statusList 如果hallStatus在这个状态array中的就显示
  {
    title: i18n.t('大厅')
    // componentName: hall
  },
  {
    title: i18n.t('采购明细')
  },
  {
    title: i18n.t('相关文件')
  },
  {
    title: i18n.t('说明')
  },
  {
    title: i18n.t('项目计划')
  }
]
export const tabSource2 = [
  // statusList 如果hallStatus在这个状态array中的就显示
  {
    title: i18n.t('大厅')
    // componentName: hall
  },
  {
    title: i18n.t('采购明细')
  },
  {
    title: i18n.t('相关文件')
  },
  {
    title: i18n.t('说明')
  },
  {
    title: i18n.t('报价')
  }
]
export const dataSource = [
  {
    title: 'tab1',
    titleCanChange: true
  },
  {
    title: 'tab1',
    content: '1'
  },
  {
    title: 'tab1',
    content: '1'
  },
  {
    title: 'tab1',
    content: '1'
  },
  {
    title: 'tab1',
    content: '1'
  },
  {
    title: 'tab1',
    content: '1'
  },
  {
    title: 'tab1',
    content: '1'
  }
]
