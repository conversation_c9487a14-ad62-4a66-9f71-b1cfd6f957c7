<template>
  <div class="review-qualification">
    <div class="marginTop">
      <div class="formMain">
        <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
          <mt-form-item prop="scoreDetailName" :label="$t('是否需要保证金')">
            <mt-select
              disabled
              v-model="formObject.needEarnestMoney"
              float-label-type="Never"
              :data-source="seletArr"
              :placeholder="$t('是')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="scoreDetailCode" :label="$t('保证金')">
            <mt-input
              v-model="formObject.earnestMoney"
              float-label-type="Never"
              :disabled="true"
              :placeholder="$t('请输入金额')"
            ></mt-input>
          </mt-form-item>
          <mt-button>{{ $t('保证金附件') }}</mt-button>
          <mt-button @click="getAddSave">{{ $t('提交资质审查') }}</mt-button>
          <mt-button @click="getSaveList">{{ $t('保存') }}</mt-button>
        </mt-form>
      </div>
      <div class="cai-note">
        <span>{{ $t('备注') }}:</span
        ><mt-input
          class="strategy-remark"
          float-label-type="Never"
          v-model="formObject.remark"
          type="text"
          @change="strategyRemark"
          :placeholder="$t('字数不超过500字')"
        />
      </div>
    </div>
    <div class="review-table">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
      />
    </div>
  </div>
</template>
<script>
import { toolbar, columnData } from './config'
export default {
  data() {
    return {
      pageConfig: [
        {
          toolbar,
          useToolTemplate: false,
          grid: {
            allowFiltering: true,
            columnData: columnData,
            dataSource: [],
            editSettings: {
              allowAdding: false,
              allowEditing: true,
              allowDeleting: false,
              mode: 'Normal', // 默认normal模式
              allowEditOnDblClick: true,
              showConfirmDialog: false,
              showDeleteConfirmDialog: false
            }
          }
        }
      ],
      formObject: {
        needEarnestMoney: 0,
        earnestMoney: '',
        remark: '',
        examineCode: '',
        examineId: 0,
        examineStatus: 0,
        moneyStatus: 0,
        supplierCode: '',
        supplierId: 0,
        supplierName: ''
      },
      examineId: '',
      supplierName: '',
      supplierId: '',
      supplierCode: '',
      formRules: {
        scoreDetailName: [
          {
            required: true,
            message: this.$t('请选择是否需要保证金'),
            trigger: 'blur'
          }
        ]
      },
      seletArr: [
        { text: this.$t('是'), value: 1 },
        { text: this.$t('否'), value: 0 }
      ]
    }
  },
  props: {
    quotedPriceData: {
      type: Object,
      default: () => {}
    }
  },
  mounted() {
    this.getRfxSupRelList()
    // this.getRfxRelSupList();
  },
  methods: {
    handleClickToolBar(e) {
      if (e.toolbar.id == 'Save') {
        this.handSave()
      }
    },
    handSave() {
      let _currentTabRef = this.$refs.templateRef.getCurrentTabRef()
      let _selectRecords = _currentTabRef?.grid?.getSelectedRecords()
      let page = _selectRecords
      if (_selectRecords.length) {
        _selectRecords.forEach((e) => {
          this.id = e.id
          this.supplierTenantId = e.supplierTenantId
          this.stageId = e.stageId
          this.supplierCode = e.supplierCode
          this.supplierName = e.supplierName
          this.supplierId = e.supplierId
        })
      }

      this.$API.rfxSupRel.getRfxSaveAdd(page).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    getRfxSupRelList() {
      let params = {
        id: this.$route.query.rfxId
      }
      this.$API.rfxSupRel.getSupRelBidList(params).then((res) => {
        const { needEarnestMoneys, earnestMoney, remark, examineId, supplierCode, supplierName } =
          res.data
        this.formObject.needEarnestMoney = needEarnestMoneys
        this.formObject.earnestMoney = earnestMoney
        this.formObject.remark = remark
        this.getRfxRelSupList(res.data)
        this.examineId = examineId
        this.supplierCode = supplierCode
        this.supplierName = supplierName
        this.supplierId = this.quotedPriceData.supplierId
        // this.examineItemId = res.data.examineItemId;
      })
    },
    getSaveList() {
      let params = {
        rfxId: this.$route.query.rfxId,
        ...this.formObject
      }
      this.$API.rfxSupRel.getRfxSave([params]).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    getRfxRelSupList(value) {
      this.$set(this.pageConfig[0].grid, 'asyncConfig', {
        url: this.$API.rfxSupRel.getrfxCainder,
        queryBuilderWrap: 'queryBuilderDTO',
        params: {
          examineId: value?.examineId,
          supplierId: this.quotedPriceData.supplierId,
          rfxId: this.$route.query.rfxId
        }
      })
    },
    //提交
    getAddSave() {
      let params = {
        examineId: this.examineId,
        supplierId: this.quotedPriceData?.supplierId,
        rfxId: this.$route.query.rfxId
      }
      this.$API.rfxSupRel.getRfxSaveSubmit(params).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //备注
    strategyRemark(e) {
      if (e.length >= 500) {
        this.$toast({
          content: this.$t('字数不得超过500字'),
          type: 'warning'
        })
      } else {
        this.formObject.remark = e
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.review-qualification {
  padding: 10px;
  //保证金头部
  .marginTop {
    width: 100%;
    .formMain {
      // 保证金
      .mt-form {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .mt-form-item {
          width: 400px;
        }
      }
    }

    //备注
    .cai-note {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 20px;
      span {
        width: 70px;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: normal;
      }
      .strategy-remark {
        width: 94%;
        // border: 1px solid #d7d7d7;
        // outline: #e8e8e8;
        font-size: 14px;
      }
    }
  }
  //列表
  .review-table {
    width: 100%;
    margin-top: 20px;
    height: 400px;
  }
}
</style>
