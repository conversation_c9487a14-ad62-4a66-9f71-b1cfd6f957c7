/*
 * @Author: your name
 * @Date: 2022-03-11 15:48:38
 * @LastEditTime: 2022-03-12 16:23:25
 * @LastEditors: your name
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-sourcing-web\src\views\supply\IBid\detail\pages\reviewQualification\config\index.js
 */
// import Vue from "vue";
import { i18n } from '@/main.js'
export const toolbar = [
  // { id: "Add", icon: "icon_solid_Createorder", title: "新增" },
  // { id: "Delete", icon: "icon_solid_Delete", title: "删除" },
  { id: 'Save', icon: 'icon_solid_edit ', title: i18n.t('保存') }
]
export const columnData = [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    field: 'examineCode',
    headerText: i18n.t('审查代码')
  },
  {
    field: 'examineItemName',
    headerText: i18n.t('审查项目名称')
  },
  {
    field: 'examineItemSpec',
    headerText: i18n.t('审查项目说明')
  },
  {
    field: 'purOpinion',
    headerText: i18n.t('供应商回复'),
    allowEditing: true
  },

  {
    field: 'supplierReply',
    headerText: i18n.t('采方审查意见')
  },
  {
    field: 'sourcingFileResponseList',
    headerText: i18n.t('附件')
    // template: function () {
    //   return {
    //     template: Vue.component("actionOption", {
    //       template: `<div><div v-if="data && data.sourcingFileResponseList" v-for="(item, index) in data.sourcingFileResponseList">{{item.fileName}}</div></div>`,
    //       data() {
    //         return { data: {} };
    //       },
    //       mounted() {},
    //       computed: {},
    //     }),
    //   };
    // },
  }
  // {
  //   field: "needFile",
  //   headerText: i18n.t("是否必须上传附件"), FIXME API 没有返回这个字段
  // },
]
export const pageConfig = [
  {
    toolbar,
    useToolTemplate: false,
    grid: { allowFiltering: true, columnData: columnData, dataSource: [] }
  }
]
