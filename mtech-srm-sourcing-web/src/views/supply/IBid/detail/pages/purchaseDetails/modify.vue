<!--采购明细-->
<template>
  <div class="full-height">
    <div>
      <mt-tabs
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabSource"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>
    </div>

    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :edit-settings="editSettings"
      :query-cell-info="customiseCell"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
      @handleClickToolBar="handleClickToolBar"
    />
    <!--   @handleClickCellTool="handleClickCellTool" -->
  </div>
</template>

<script>
export default {
  props: {
    fieldDefines: {
      type: Array,
      default: () => {
        return []
      }
    },
    submitField: {
      type: Array,
      default: () => {
        return []
      }
    },
    quotedPriceData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    detailInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      pageConfig: [
        {
          toolbar: [{ id: 'save', icon: 'icon_solid_edit', title: this.$t('保存') }],
          useToolTemplate: false,
          grid: {
            allowFiltering: true,
            editSettings: {
              allowEditing: true
            },
            columnData: this.fieldDefines,
            frozenColumns: 1,

            queryCellInfo: this.customiseCell,
            class: 'pe-edit-grid custom-toolbar-grid'
          }
        }
      ],
      editSettings: {
        allowEditing: true
        // allowAdding: true,
        // allowDeleting: true,
        // mode: "Normal", // 选择默认模式，双击整行可以进行编辑
        // showConfirmDialog: false,
        // showDeleteConfirmDialog: false,
        // newRowPosition: "Bottom",
      },
      disabled: false,
      // actionCompletes: {},
      //切换tabs页
      tabSource: [
        { title: this.$t('当前报价'), moduleType: 0 },
        { title: this.$t('历史报价'), moduleType: 1 }
      ],
      submitTableData: []
    }
  },
  mounted() {
    if (this.quotedPriceData.bidStatus == 3) {
      this.disabled = true
    }
    // this.purchaseDetails();
    // console.log(this.pageConfig[0].grid.columnData);
    // if (this.getDataUrl != "getRFXItem") {
    //   this.$set(this.pageConfig[0], "toolbar", []);
    // }
    this.resetAsyncConfigParams()
    // this.getData();
  },
  methods: {
    // 点击跳转
    handleSelectTab(e) {
      if (e != 0) {
        this.$set(this.pageConfig[0], 'toolbar', [])
      } else {
        this.$set(this.pageConfig[0], 'toolbar', [
          { id: 'save', icon: 'icon_solid_edit', title: this.$t('保存') }
        ])
      }
    },
    //列表数据
    // purchaseDetails() {
    //   console.log(this.fieldDefines);
    //   // this.pageConfig[0].grid.columnData.forEach((item) => {
    //   //   if (item.field == "quoteEffectiveEndDate") {
    //   //     console.log(item);
    //   //   }
    //   // });
    // },
    //点击头部

    handleClickToolBar(e) {
      console.log(e.toolbar.id)
      if (e.toolbar.id == 'save') {
        this.handleClickSaves()
      }
    },
    // 不可修改的单元格，修改背景色
    customiseCell(args) {
      if (!args.column.allowEditing) {
        args.cell.classList.add('bg-grey')
      } else {
        args.cell.classList.add('bg-orange')
      }
    },
    //行内进入事件
    actionBegin(e) {
      console.log('============actionBegin', e)
    },
    //行内离开事件
    actionComplete(args) {
      console.log('============actionComplete', args)
      if (!args.action) {
        console.log(args.requestType)
        return
      }
      if (args.requestType == 'save') {
        this.intergrateEditData(args.data)
      }
      // this.actionCompletes = args.data;
    },
    //整合编辑数据，更新到 提交数据里
    intergrateEditData(rowData) {
      let _name = rowData.rfxItemId
      let _index = this.submitTableData.findIndex((item) => rowData[_name] == item[_name])
      if (_index > -1) {
        this.$set(this.submitTableData, _index, rowData)
      } else {
        this.submitTableData.push(rowData)
      }
      console.log('intergrateEditData:', this.submitTableData.priceUnitCode)
    },
    //保存
    handleClickSaves() {
      // if (!isEqual(args.data, args.previousData)) {
      //   this.intergrateEditData(args.data); // 整合编辑数据
      // }
      // if (this.actionCompletes) {
      let rfxId = this.quotedPriceData.rfxId
      //   let rfxItemId = this.quotedPriceData.rfxItemId;
      let bidStatus = this.quotedPriceData.bidStatus
      //   let dataObj = [this.actionCompletes];
      //   let arr = [{}];
      //   console.log(rfxItemId);
      //   for (let i = 0; i < this.submitField.length; i++) {
      //     let a = this.submitField[i].fieldCode;
      //     arr[0][a] = dataObj[0][a];
      //   }
      //   let flag = false;
      //   for (let key in arr[0]) {
      //     if (arr[0][key] == "") {
      //       flag = true;
      //       break;
      //     }
      //   }
      //   if (flag == false) {
      //     // console.log(rfxId);
      var param1 = {
        rfxId: rfxId,
        bidStatus: bidStatus,
        bidPriceItems: this.submitTableData
      }
      this.$API.supplyQdetail.priceSave(param1).then((res) => {
        console.log(res)
      })
    },
    // } else {
    //   this.$toast({ content: this.$t("请输入必填项"), type: "warning" });
    //   return;
    // }
    //列表参数重新赋值
    resetAsyncConfigParams() {
      // 大厅中的采购明细
      this.$set(this.pageConfig[0].grid, 'asyncConfig', {
        url: this.$API.supplyQdetail.tenderItems,
        params: {
          rfxId: this.$route.query.rfxId,
          tabType: 0
        },
        queryBuilderWrap: 'queryBuilderDTO',
        serializeList: (list) => {
          console.log(list)
          list.forEach((e) => {
            e.addId = e.id
          })
          this.submitTableData = list
          return list
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/.bg-grey {
  background: #dedede;
}
/deep/.bg-orange {
  background: #fdf5ea;
}
</style>
