// 物料下拉数据
export const itemNameOptions = []

// 工厂下拉数据
export const factoryNameOptions = []

// 供应商下拉数据
export const supplierNameOptions = []

// 公司下拉数据
export const companyNameOptions = []

// 预测表格数据
export const forecastDataSource = []

// 预测弹框 表格数据
export const forecastDialogDataSource = []

// 预测弹框 表格头数据
export const forecastDialogColumnData = [
  {
    allowEditing: true,
    field: 'checkBox',
    fieldCode: 'checkBox',
    showInColumnChooser: false,
    type: 'checkbox',
    width: '50'
  },
  {
    width: '150',
    field: 'thePrimaryKey', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true,
    allowEditing: false
  }
]

// 预测表格编辑行数据容器
export const rowDataTemp = []

// 预测表格头数据
export const forecastColumnData = [
  {
    allowEditing: true,
    field: 'checkBox',
    fieldCode: 'checkBox',
    showInColumnChooser: false,
    type: 'checkbox',
    width: '50'
  },
  {
    width: '150',
    field: 'thePrimaryKey', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true,
    allowEditing: false
  }
]

// 预测动态表头
export const dynamicTitle = []

// 弹框预测动态表头
export const dynamicTitleDialog = []

// 业务组类型编码
export const businessGroupCode = {
  purchase: '', // 采购组
  plan: '' // 计划组
}

// 当重新编辑错误数据时，不重新获取关联数据的数据源，保留已用于请求的数据
export const editLinkDataParams = {
  itemCode: '' // 物料，获取工厂数据源的请求数据
}
