<!--采购明细-->
<template>
  <div class="full-height">
    <div>
      <mt-tabs
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabSource"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>
    </div>

    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :edit-settings="editSettings"
      :query-cell-info="customiseCell"
      @handleClickToolBar="handleClickToolBar"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    />
    <!--   @handleClickCellTool="handleClickCellTool" -->
  </div>
</template>

<script>
// import { purchaseDetails } from "../../mock/module.js";
// import {  } from "./config/columnComponent.js";
export default {
  props: {
    fieldDefines: {
      type: Array,
      default: () => {
        return []
      }
    },
    //可编辑字段
    submitField: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 必填字段
    mandatoryArr: {
      type: Array,
      default: () => {
        return []
      }
    },
    quotedPriceData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    detailInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  watch: {
    quotedPriceData() {
      this.watchQuotedPriceData()
    }
  },
  data() {
    return {
      pageConfig: [],
      editSettings: {
        allowEditing: true
        // allowAdding: true,
        // allowDeleting: true,
        // mode: "Normal", // 选择默认模式，双击整行可以进行编辑
        // showConfirmDialog: false,
        // showDeleteConfirmDialog: false,
        // newRowPosition: "Bottom",
      },
      // disabled: false,
      // actionCompletes: {},
      //维护数组
      actionObj: [],
      //切换tabs页
      tabSource: [
        { title: this.$t('当前报价'), moduleType: 0 },
        { title: this.$t('历史报价'), moduleType: 1 },
        { title: this.$t('议价'), moduleType: 2 }
      ],
      tabsNumber: 0
    }
  },
  beforeDestroy() {
    this.$bus.$off('procurementRefreshPage')
  },
  mounted() {
    this.ListInterface()
    this.pageConfig = [
      {
        toolbar: [],
        useToolTemplate: false,
        grid: {
          allowFiltering: true,
          editSettings: {
            allowEditing: true
          },
          columnData: this.fieldDefines,
          frozenColumns: 1,
          dataSource: [],
          // asyncConfig: {
          //   url: this.$API.supplyQdetail.tenderItems,
          //   params: {
          //     rfxId: this.$route.query.rfxId,
          //     tabType: 0,
          //   },
          //   queryBuilderWrap: "queryBuilderDTO",
          // },
          queryCellInfo: this.customiseCell,
          class: 'pe-edit-grid custom-toolbar-grid'
        }
      }
    ]
    if (this.quotedPriceData.status == 1) {
      if (this.quotedPriceData.joinStatus == 0) {
        this.$set(this.pageConfig[0], 'toolbar', [])
        this.$set(this.pageConfig[0].grid.editSettings, 'allowEditing', false)
        this.$set(this.pageConfig[0].grid, 'queryCellInfo', null)
        this.$set(this.pageConfig[0].grid, 'class', '')
      } else if (this.quotedPriceData.joinStatus == 1) {
        this.$set(this.pageConfig[0], 'toolbar', [
          { id: 'save', icon: 'icon_solid_edit', title: this.$t('保存') },
          { id: 'quote', icon: 'icon_solid_edit', title: this.$t('重新报价') }
        ])
        this.$set(this.pageConfig[0].grid.editSettings, 'allowEditing', true)
        this.$set(this.pageConfig[0].grid, 'queryCellInfo', this.customiseCell)
        this.$set(this.pageConfig[0].grid, 'class', 'pe-edit-grid custom-toolbar-grid')
      }
    }
    this.refreshPage()
    // if (this.getDataUrl != "getRFXItem") {
    //   this.$set(this.pageConfig[0], "toolbar", []);
    // }
    // this.resetAsyncConfigParams();
    // this.getData();
    // this.purchaseDetails();
  },
  methods: {
    //初始调用接口
    ListInterface() {
      let param = {
        rfxId: this.$route.query.rfxId,
        tabType: 0,
        queryBuilderDTO: { page: { current: 1, size: 10 } }
      }
      this.$API.supplyQdetail.tenderItems(param).then((res) => {
        // console.log(res.data.records);
        this.$set(this.pageConfig[0].grid, 'dataSource', res.data.records)
      })
    },
    //监听参与状态改变
    watchQuotedPriceData() {
      if (this.quotedPriceData.status == 1) {
        if (this.quotedPriceData.joinStatus == 0) {
          this.$set(this.pageConfig[0], 'toolbar', [])
          this.$set(this.pageConfig[0].grid.editSettings, 'allowEditing', false)
          this.$set(this.pageConfig[0].grid, 'queryCellInfo', null)
          this.$set(this.pageConfig[0].grid, 'class', '')
        } else if (this.quotedPriceData.joinStatus == 1) {
          // if (e == 0 || e == 2) {
          this.$set(this.pageConfig[0], 'toolbar', [
            { id: 'save', icon: 'icon_solid_edit', title: this.$t('保存') },
            {
              id: 'quote',
              icon: 'icon_solid_edit',
              title: this.$t('重新报价')
            }
          ])
          this.$set(this.pageConfig[0].grid.editSettings, 'allowEditing', true)
          this.$set(this.pageConfig[0].grid, 'queryCellInfo', this.customiseCell)
          this.$set(this.pageConfig[0].grid, 'class', 'pe-edit-grid custom-toolbar-grid')
          // }
        }
      }
    },
    // 点击跳转
    handleSelectTab(e) {
      this.tabsNumber = e
      if (this.quotedPriceData.status == 1) {
        if (this.quotedPriceData.joinStatus == 0) {
          this.$set(this.pageConfig[0], 'toolbar', [])
          this.$set(this.pageConfig[0].grid.editSettings, 'allowEditing', false)
          this.$set(this.pageConfig[0].grid, 'queryCellInfo', null)
          this.$set(this.pageConfig[0].grid, 'class', '')
        } else if (this.quotedPriceData.joinStatus == 1) {
          if (e == 0 || e == 2) {
            this.$set(this.pageConfig[0], 'toolbar', [
              { id: 'save', icon: 'icon_solid_edit', title: this.$t('保存') },
              {
                id: 'quote',
                icon: 'icon_solid_edit',
                title: this.$t('重新报价')
              }
            ])
            this.$set(this.pageConfig[0].grid.editSettings, 'allowEditing', true)
            this.$set(this.pageConfig[0].grid, 'queryCellInfo', this.customiseCell)
            this.$set(this.pageConfig[0].grid, 'class', 'pe-edit-grid custom-toolbar-grid')
          } else if (e == 1) {
            this.$set(this.pageConfig[0], 'toolbar', [])
            this.$set(this.pageConfig[0].grid.editSettings, 'allowEditing', false)
            this.$set(this.pageConfig[0].grid, 'queryCellInfo', null)
            this.$set(this.pageConfig[0].grid, 'class', '')
          }
        }
      }
      this.ListInterface()
    },
    //点击头部
    handleClickToolBar(e) {
      if (e.toolbar.id == 'save') {
        this.endEdit()
        this.handleClickSaves()
      }
      //重新报价
      if (e.toolbar.id == 'quote') {
        this.endEdit()
        this.handleClickQuote()
      }
    },
    // 不可修改的单元格，修改背景色
    customiseCell(args) {
      if (!args.column.allowEditing) {
        args.cell.classList.add('bg-grey')
      } else {
        args.cell.classList.add('bg-orange')
      }
    },
    //行内进入事件
    actionBegin(e) {
      console.log('进入事件', e)
    },
    //行内离开事件
    actionComplete(e) {
      if (e.requestType == 'save') {
        if (this.actionObj.length == 0) {
          this.actionObj.push(e.data)
        } else {
          let hasSave = false
          for (let i = 0; i < this.actionObj.length; i++) {
            if (this.actionObj[i].rfxItemId == e.data.rfxItemId) {
              this.actionObj[i] = e.data
              hasSave = true
              break
            }
          }
          if (!hasSave) {
            this.actionObj.push(e.data)
          }
        }
        // console.log( e.data);
        // console.log(this.mandatoryArr);
      }

      // this.actionCompletes = e.data;
    },
    // 保存
    handleClickSaves() {
      if (this.actionObj.length > 0) {
        let rfxId = this.quotedPriceData.rfxId
        let tenantId = this.quotedPriceData.tenantId
        let arr = []
        let mandatoryArr = []
        //统一arr数组length长度
        for (let i = 0; i < this.actionObj.length; i++) {
          arr.push({})
          mandatoryArr.push({})
        }
        //arr对象内添加code
        for (let i = 0; i < arr.length; i++) {
          for (let j = 0; j < this.submitField.length; j++) {
            let FieldCode = this.submitField[j].fieldCode

            arr[i][FieldCode] = this.actionObj[i][FieldCode]
            arr[i][FieldCode] = this.actionObj[i]['biddingItemDTO'][FieldCode]
          }
        }
        //mandatoryArr对象内添加code
        for (let i = 0; i < mandatoryArr.length; i++) {
          //2
          for (let j = 0; j < this.mandatoryArr.length; j++) {
            //必填 18个字段
            let FieldCode = this.mandatoryArr[j].fieldCode
            mandatoryArr[i][FieldCode] = this.actionObj[i][FieldCode]
            mandatoryArr[i][FieldCode] = this.actionObj[i]['biddingItemDTO'][FieldCode]
          }
        }
        // console.log("this.actionObj[i][FieldCode]", this.actionObj);
        // console.log(" mandatoryArr[i][FieldCode]", mandatoryArr);
        //开关
        let flag = true
        // console.log(this.mandatoryArr);
        for (let i = 0; i < mandatoryArr.length; i++) {
          for (let key in mandatoryArr[i]) {
            if (mandatoryArr[i][key] == undefined || mandatoryArr[i][key] == null) {
              flag = false
              break
            }
          }
        }
        if (flag) {
          // console.log(
          //   this.actionObj[0]["biddingItemDTO"].biddingId,
          //   this.actionObj[0]["biddingItemDTO"].biddingItemId
          // );
          for (let i = 0; i < arr.length; i++) {
            arr[i].biddingId = this.actionObj[i]['biddingItemDTO'].biddingId
              ? this.actionObj[i]['biddingItemDTO'].biddingId
              : null
            arr[i].biddingItemId = this.actionObj[i]['biddingItemDTO'].biddingItemId
              ? this.actionObj[i]['biddingItemDTO'].biddingItemId
              : null
            arr[i].rfxItemId = this.actionObj[i].rfxItemId ? this.actionObj[i].rfxItemId : null
            // console.log(
            //   arr[i].biddingId,
            //   arr[i].biddingItemId,
            //   arr[i].rfxItemId
            // );
            if (arr[i]['quoteEffectiveEndDate']) {
              arr[i]['quoteEffectiveEndDate'] = this.$utils.formatTime(
                arr[i]['quoteEffectiveEndDate']
              )
            }
            if (arr[i]['quoteEffectiveStartDate']) {
              arr[i]['quoteEffectiveStartDate'] = this.$utils.formatTime(
                arr[i]['quoteEffectiveStartDate']
              )
            }
          }
          let param1 = {}
          if (this.tabsNumber == 0) {
            param1 = {
              abateFlag: false,
              bidPriceItems: arr,
              rfxId: rfxId,
              submitStatus: 0,
              tenantId: tenantId
            }
          }
          if (this.tabsNumber == 2) {
            param1 = {
              abateFlag: true,
              bidPriceItems: arr,
              rfxId: rfxId,
              submitStatus: 0,
              tenantId: tenantId
            }
          }
          this.$API.supplyQdetail.priceSave(param1).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
              this.$emit('mainparticipateButton')
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        } else {
          this.$toast({ content: this.$t('请输入必填项'), type: 'warning' })
          return
        }
        console.log('所有可以编辑的字段', this.submitField)
      } else {
        this.$toast({
          content: this.$t('没有编辑数据不能保存'),
          type: 'warning'
        })
        return
      }
    },
    // 重新报价
    handleClickQuote() {
      // console.log(this.pageConfig.grid.columnData);

      if (this.actionObj.length > 0) {
        // console.log(this.actionObj);
        for (let i = 0; i < this.actionObj.length; i++) {
          this.actionObj[i]['biddingItemDTO'].biddingId = 0
          this.actionObj[i]['biddingItemDTO'].biddingItemId = 0
          this.actionObj[i]['biddingItemDTO'].submitStatus = 0
        }
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        this.$emit('monitorRequotation')
      } else {
        this.$toast({
          content: this.$t('没有编辑数据不能保存'),
          type: 'warning'
        })
        return
      }
    },
    // 结束编辑状态
    endEdit() {
      let _current = this.$refs.templateRef.getCurrentTabRef()
      _current?.grid.endEdit()
    },
    //刷新操作
    refreshPage() {
      this.$bus.$on(`procurementRefreshPage`, () => {
        this.$refs.templateRef.refreshCurrentGridData()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/.bg-grey {
  background: #dedede;
}
/deep/.bg-orange {
  background: #fdf5ea;
}
</style>
