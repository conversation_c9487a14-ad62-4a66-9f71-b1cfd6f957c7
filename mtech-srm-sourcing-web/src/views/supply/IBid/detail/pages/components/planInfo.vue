<template>
  <div class="info-content">
    <div class="price-info-panel mt-flex-direction-column">
      <div class="info-header mt-flex-direction-column">
        <span class="info-title">{{ bidItemsData.itemCode || $t('物料编码') }}</span>
        <div class="info-desc mt-flex">
          <span class="main-desc">{{ bidItemsData.itemName || $t('物料名称') }}</span>
          <div class="desc-item">
            <mt-icon name="icon_Department"></mt-icon>
            <span class="desc-title">{{ $t('采购数量') }}</span>
            <span class="desc-value">{{ bidItemsData.bidQuantity }}</span>
          </div>
          <div class="desc-item">
            <mt-icon name="icon_Coin"></mt-icon>
            <span class="desc-title">{{ $t('采购工厂/地点') }}</span>
            <span class="desc-value">{{ bidItemsData.siteName }}</span>
          </div>
          <div class="desc-item">
            <mt-icon name="icon_Coin"></mt-icon>
            <span class="desc-title">{{ $t('采购预估价') }}</span>
            <span class="desc-value"
              >{{ bidItemsData.budgetPrice }}/{{ bidItemsData.bidUnitName }}</span
            >
          </div>
        </div>
      </div>
      <div class="info-detail">
        <div class="detail-title">{{ $t('报价信息') }}</div>
        <!-- <div class="option-item" style="float: right">
          <mt-checkbox
            class="group-item"
            :value="giveUp"
            :label="$t('标记放弃')"
            @change="giveUpChange"
          ></mt-checkbox>
        </div> -->
        <div class="detail-fields mt-flex">
          <div class="field-item" v-for="(item, index) in fieldColumn" :key="index">
            <div class="field-title">
              <span v-if="item.rule" style="color: #d81e06">*</span>{{ item.headerText }}
            </div>
            <mt-input
              v-if="item.type === 'number'"
              class="input-item"
              style="width: 250px"
              :show-spin-button="false"
              css-class="e-outline"
              type="number"
              v-model="priceDetail[item.field]"
              :placeholder="$t('请输入数字')"
            ></mt-input>
            <mt-input
              v-else-if="item.type === 'untaxedUnitPrice'"
              class="input-item"
              style="width: 250px"
              :show-spin-button="false"
              @input="formUntaxedUnitPrice"
              css-class="e-outline"
              type="number"
              v-model="priceDetail[item.field]"
              :placeholder="$t('请输入未税单价')"
            ></mt-input>
            <mt-input
              v-else-if="item.type === 'taxedUnitPrice'"
              class="input-item"
              style="width: 250px"
              @input="formTaxedUnitPrice"
              :show-spin-button="false"
              css-class="e-outline"
              type="number"
              v-model="priceDetail[item.field]"
              :placeholder="$t('请输入含税单价')"
            ></mt-input>
            <mt-select
              v-else-if="item.type === 'select'"
              class="input-item"
              style="width: 250px"
              :show-spin-button="false"
              :data-source="item.dataSource"
              css-class="e-outline"
              type="text"
              :change="formSelectChange"
              :fields="item.fields"
              v-model="priceDetail[item.field]"
              :placeholder="$t('请选择税率')"
            ></mt-select>
            <mt-date-time-picker
              v-else-if="item.type === 'date'"
              class="input-item"
              style="width: 250px"
              :show-spin-button="false"
              :allow-edit="false"
              css-class="e-outline"
              type="text"
              v-model="priceDetail[item.field]"
              :placeholder="$t('选择日期和时间')"
            ></mt-date-time-picker>
            <div v-else class="upload-item">
              <div
                class="upload"
                v-if="!checkHasFiles(item.field)"
                @click="uploadFiles(item.field)"
              >
                {{ $t('上传文件') }}
              </div>
              <div v-if="checkHasFiles(item.field)" class="content">
                {{ priceDetail[item.field][0]['fileName'] }}
              </div>
              <mt-icon
                @click.native="clearFileContent(item.field)"
                v-if="checkHasFiles(item.field)"
                name="icon_solid_delete_2"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="detail-tabs-container mt-flex">
      <div class="left-panel">
        <mt-tabs
          :tab-id="$utils.randomString()"
          :e-tab="false"
          :data-source="tableTabs"
          @handleSelectTab="handleSelectTableTab"
        ></mt-tabs>
      </div>
    </div>
    <div class="price-list">
      <mt-template-page v-if="listTab === 1" :template-config="priceListConfig" />
    </div>
    <StageQuotation
      v-if="listTab === 0"
      v-model="ladderList"
      :tax-rate="{
        text: taxItem.taxItemName,
        value: taxItem.taxRate,
        taxId: taxItem.taxId
      }"
    />
  </div>
</template>

<script>
import { dialogDetailColumnData, getDialogListGridData } from '../config'
import StageQuotation from 'COMPONENTS/StageQuotation'
export default {
  name: 'PlanInfo',
  components: { StageQuotation },
  data() {
    return {
      taxItem: {},
      valueStrategy: '1',
      right_panel_switch: true,
      activeTab: 0,
      listTab: 0,
      dialog: {},
      giveUp: false,
      modalData: {},
      ladderList: [],
      underIndex: '',
      tableTabs: [
        {
          title: this.$t('阶梯报价')
        }
        // {
        //   title: this.$t("明细报价"),
        // },
      ],
      fieldColumn: [
        {
          type: 'untaxedUnitPrice',
          field: 'untaxedUnitPrice',
          headerText: this.$t('未税单价')
        },
        {
          type: 'select',
          field: 'taxId',
          headerText: this.$t('税率'),
          dataSource: [],
          fields: { text: 'taxItemName', value: 'taxId' },
          rulesText: this.$t('不能为空')
        },
        {
          type: 'taxedUnitPrice',
          field: 'taxedUnitPrice',
          headerText: this.$t('含税单价'),
          rulesText: this.$t('不能为空')
        },
        {
          type: 'date',
          field: 'validStartTime',
          headerText: this.$t('报价有效期从')
        },
        {
          type: 'date',
          field: 'validEndTime',
          headerText: this.$t('报价有效期止')
        },
        {
          type: 'number',
          field: 'supplyQuantity',
          headerText: this.$t('可供数量'),
          rulesText: this.$t('不能为空')
        },
        {
          type: 'date',
          field: 'deliveryCommitmentDate',
          headerText: this.$t('承诺交货日期')
        },
        {
          type: 'date',
          field: 'deliveryPreTime',
          headerText: this.$t('供货提前期')
        },
        {
          type: 'number',
          field: 'minPurchaseQuantity',
          headerText: this.$t('最小采购量')
        },
        {
          type: 'number',
          field: 'minPackageQuantity',
          headerText: this.$t('最小包装数量')
        },
        {
          type: 'uploader',
          field: 'businessFileList',
          headerText: this.$t('商务附件')
        },
        {
          type: 'uploader',
          field: 'tecFileList',
          headerText: this.$t('技术附件')
        }
      ],
      priceListConfig: [
        {
          useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
          toolbar: [[{ id: 'Add', icon: 'icon_solid_add', title: this.$t('新增') }]],
          grid: {
            allowFiltering: true,
            allowPaging: false,
            columnData: dialogDetailColumnData,
            dataSource: getDialogListGridData(3)
          }
        }
      ]
    }
  },
  props: {
    bidItems: {
      type: Array,
      default: () => {
        return []
      }
    },
    bidIndex: {
      type: Number,
      default: 0
    }
  },
  computed: {
    bidItemsData() {
      return this.bidItems[this.bidIndex] || {}
    },
    priceDetail() {
      return this.modalData
    },
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    formTaxRate() {
      let taxRate = null
      this.selectData.forEach((e) => {
        if (e.id === this.priceDetail.taxId) {
          taxRate = e.taxRate
        }
      })
      return taxRate
    },
    newTaxRate() {
      let taxRate = null
      this.selectData.forEach((e) => {
        if (e.id === this.ruleForm.id) {
          taxRate = e.taxRate
        }
      })
      return taxRate
    },
    ruleFormTaxRate() {
      let taxRate
      this.selectData.forEach((e) => {
        if (e.id === this.ruleForm.id) {
          taxRate = e.taxRate
        }
      })
      return taxRate
    },
    checkHasFiles() {
      return (field) => {
        if (Array.isArray(this.modalData[field])) {
          return this.modalData[field].length
        } else {
          return false
        }
      }
    }
  },
  watch: {
    bidItemsData: {
      handler() {
        this.getLatestDetail()
      },
      deep: true
    }
  },
  mounted() {
    // this.getAllTaxItem();
    this.getLatestDetail()
  },
  methods: {
    // 表单未税单价input方法
    formUntaxedUnitPrice(e) {
      if (this.formTaxRate != null) {
        let a = e * ((100 + this.formTaxRate) / 100)
        this.priceDetail.taxedUnitPrice = a.toFixed(4)
      }
    },
    // 表单框含税单价input方法
    formTaxedUnitPrice(e) {
      if (this.formTaxRate != null) {
        let a = e / ((100 + this.formTaxRate) / 100)
        this.priceDetail.untaxedUnitPrice = a.toFixed(4)
      }
    },
    // 表单框税率
    formSelectChange(e) {
      this.taxItem = e.itemData
      let taxRate = e.itemData.taxRate
      if (this.priceDetail.untaxedUnitPrice) {
        let a = this.priceDetail.untaxedUnitPrice * ((100 + taxRate) / 100)
        this.priceDetail.taxedUnitPrice = a.toFixed(4)
      } else if (this.priceDetail.taxedUnitPrice) {
        let b = this.priceDetail.taxedUnitPrice / ((100 + taxRate) / 100)
        this.priceDetail.untaxedUnitPrice = b.toFixed(4)
      }
    },
    giveUpChange(e) {
      this.giveUp = e.checked
    },
    // 获取物料详情
    getLatestDetail() {
      console.log('bidItemsData', this.bidItemsData)
      if (this.bidItemsData && this.bidItemsData.tenderId) {
        if (this.bidItemsData.priceDetail) {
          this.modalData = this.bidItemsData.priceDetail
          if (!this.bidItemsData.priceDetail?.businessFileList) {
            this.modalData.businessFileList = []
          }
          if (!this.bidItemsData.priceDetail?.tecFileList) {
            this.modalData.tecFileList = []
          }
          this.$parent.enableEdit = this.bidItemsData.priceDetail.enableEdit
        } else {
          this.bidItemsData.priceDetail = {
            stageFrom: '',
            stageTo: '',
            taxId: '',
            taxedUnitPrice: '',
            untaxedUnitPrice: '',
            remark: '',
            id: '',
            enableEdit: true
          }
          this.modalData = this.bidItemsData.priceDetail
          this.modalData.businessFileList = []
          this.modalData.tecFileList = []
          this.$parent.enableEdit = true
        }
      }
    },
    // 获取税率
    // getAllTaxItem() {
    //   this.$API.masterData.queryAllTaxItem().then((r) => {
    //     this.fieldColumn.forEach((e) => {
    //       if (e.field == "taxId") {
    //         let a = r.data;
    //         a.forEach((p) => {
    //           p.taxId = p.id;
    //         });
    //         e.dataSource = a;
    //         this.selectData = a;
    //       }
    //     });
    //   });
    // },
    handleSelectTab(e) {
      this.activeTab = e
    },
    handleSelectTableTab(e) {
      this.listTab = e
    },
    bidPriceSave(submitStatus) {
      let hasError = false
      let bidPriceItems = this.bidItems.map((item, index) => {
        if (item.priceDetail) {
          if (index === this.bidIndex) {
            item.priceDetail.itemStages = this.ladderList
          }
          item.priceDetail.packageId = item.packageId
          if (
            !item.priceDetail.validEndTime ||
            !item.priceDetail.validStartTime ||
            !item.priceDetail.deliveryCommitmentDate ||
            !item.priceDetail.deliveryPreTime
          ) {
            hasError = true
            this.$toast({
              type: 'warning',
              content: this.$t('请选择日期')
            })
          }
          if (
            item.priceDetail.validEndTime <= item.priceDetail.validStartTime ||
            item.priceDetail.deliveryPreTime <= item.priceDetail.deliveryCommitmentDate
          ) {
            hasError = true
            this.$toast({
              type: 'warning',
              content: this.$t('结束时间必须大于开始时间')
            })
          }
          if (item.priceDetail.supplyQuantity < item.priceDetail.minPurchaseQuantity) {
            hasError = true
            this.$toast({
              type: 'warning',
              content: this.$t('采购数量不能大于可供货数量')
            })
          }
        }
        return item.priceDetail
      })
      if (hasError) return
      let params = {}
      // params.packageId = this.bidItemsData.packageId;
      params.roundId = this.bidItemsData.roundId
      params.tenderId = this.bidItemsData.tenderId
      params.biddingId = this.bidItemsData.biddingId
      // params.stageType = this.bidItemsData.valueStrategy;
      // params.abandon = this.giveUp ? 1 : 0;
      params.submitStatus = submitStatus
      params.bidPriceItems = bidPriceItems
      //投标价格保存
      // this.$API.quotationBidding.bidPriceSave(params).then(() => {
      //   this.$toast({
      //     content: "操作成功！",
      //     type: "success",
      //   });
      //   this.$parent.getBidItems(params.roundId);
      // });
    },
    uploadFiles(field) {
      let _text = field === 'businessFileList' ? this.$t('商务附件') : this.$t('技术附件')
      this.$dialog({
        modal: () =>
          import(/* webpackChunkName: "components/upload" */ 'COMPONENTS/Upload/index.vue'),
        data: {
          title: `上传${_text}`
        },
        success: (data) => {
          let { id, fileName, fileSize, fileType, url } = data
          let _data = {
            fileName: fileName,
            fileSize: fileSize,
            fileType: fileType,
            sysFileId: id,
            url: url
          }
          if (Array.isArray(this.modalData[field])) {
            this.modalData[field].push(_data)
          }
          let _modalData = { ...this.modalData }
          this.modalData = {}
          this.$set(this, 'modalData', _modalData)
        }
      })
    },
    //文件移除
    clearFileContent(field) {
      if (Array.isArray(this.modalData[field])) {
        this.modalData[field] = []
      }
      let _modalData = { ...this.modalData }
      this.modalData = {}
      this.$set(this, 'modalData', _modalData)
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .e-upload {
  border: none;
}
/deep/ .e-file-select-wrap {
  padding: 0;
}
.info-content {
  /deep/ .mt-tabs-container {
    height: 68px;
    background: #ffffff;
    .tabs-arrow {
      display: none;
    }
    .tab-wrap {
      padding: 0;
      .tab-item {
        padding: 6px 10px;
        span {
          line-height: 1;
        }
      }
    }
  }
  .price-info-panel {
    min-width: 860px;
    height: 393px;
    background: #ffffff;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    margin-bottom: 20px;
    .info-header {
      height: 84px;
      background: #ffffff;
      border-bottom: 1px solid #e8e8e8;
      background: url(../../../../../../assets/images/evdetailbanner.jpg) center no-repeat;
      background-size: 100% 100%;
      border-radius: 4px 4px 0 0;
      padding: 20px;
      justify-content: space-between;
      .info-title {
        font-size: 20px;
        font-family: DINAlternate;
        font-weight: bold;
        color: rgba(41, 41, 41, 1);
      }
      .info-desc {
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: normal;

        .main-desc {
          color: rgba(41, 41, 41, 1);
        }
        .desc-item {
          color: rgba(157, 170, 191, 1);
          margin-left: 30px;
          .mt-icons {
            position: relative;
            top: -2px;
            margin-right: 5px;
          }
          .desc-title {
            &:after {
              content: '：';
            }
          }
        }
      }
    }
    .info-detail {
      flex: 1;
      padding: 20px;
      .detail-title {
        font-size: 16px;
        color: #292929;
        display: inline-block;
        padding-left: 13px;
        position: relative;

        &:before {
          content: '';
          position: absolute;
          width: 3px;
          height: 14px;
          background: #6386c1;
          border-radius: 0 2px 2px 0;
          left: 0;
          top: 2px;
        }
      }
      .detail-fields {
        flex-wrap: wrap;
        .field-item {
          width: 33%;
          margin-top: 20px;
          display: flex;
          align-items: center;
          .field-title {
            width: 105px;
            text-align: right;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: 500;
            color: rgba(70, 91, 115, 1);
            flex-shrink: 0;
            &:after {
              content: '：';
            }
          }
          .field-value {
            flex: 1;
            margin-left: 10px;
            width: 90px;
            text-align: left;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: normal;
            color: rgba(35, 43, 57, 1);
          }
          .upload-item {
            width: 250px;
            overflow: hidden;
            background: rgba(250, 250, 250, 1);
            color: #8e8e8e;
            border-radius: 5px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            .upload {
              width: 100%;
              height: 100%;
              word-wrap: nowrap;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
            }
            .content {
              width: 100%;
              height: 100%;
              word-wrap: nowrap;
              display: flex;
              align-items: center;
              justify-content: center;
            }
            .mt-icons {
              color: #ff0020;
              position: absolute;
              right: 0;
              top: 0;
              font-size: 16px;
              cursor: pointer;
            }
          }
        }
      }
    }
  }
  .detail-tabs-container {
    box-shadow: inset 0 -1px 0 0 rgba(237, 239, 243, 1);
    align-items: center;
    justify-content: space-between;
    padding-bottom: 20px;
    .right-panel,
    .left-panel {
      .option-item {
        display: flex;
        align-items: center;
        .strategy-common-label {
          width: 70px;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          color: #465b73;
          text-align: right;
          display: inline-block;
          margin-right: 20px;
          position: relative;
        }
        .strategy-common-container {
          width: 120px;
          display: inline-block;
          height: 34px;
          background: #fafafa;
          border: 1px solid #e8e8e8;
          border-radius: 4px;
          padding: 0 20px;
        }
        /deep/ .strategy-element {
          border: none;
          border-color: transparent !important;
          position: relative;
          top: 0;
          &:before,
          &:after {
            display: none;
          }
          .e-float-line {
            display: none;
          }
          .e-control {
            color: #292929;
            border: none;
            border-color: transparent !important;
          }
        }
      }
    }
  }
}
</style>
