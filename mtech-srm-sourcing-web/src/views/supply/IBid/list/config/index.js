import { i18n } from '@/main.js'
//  参与状态
const map = [
  {
    status: 0,
    label: i18n.t('未参与'),
    cssClass: ['OfferBid-status0']
  },
  {
    status: 1,
    label: i18n.t('参与'),
    cssClass: ['OfferBid-status1']
  }
]
// 已报名状态
const bidStatus = [
  // 报价状态 0 是否报价 1 部分报价 2 全部报价 3 已放弃
  {
    status: 0,
    label: i18n.t('未报价'),
    cssClass: ['OfferBid-status0']
  },
  {
    status: 1,
    label: i18n.t('部分报价'),
    cssClass: ['OfferBid-status1']
  },
  {
    status: 2,
    label: i18n.t('全部报价'),
    cssClass: ['OfferBid-status2']
  },
  {
    status: 3,
    label: i18n.t('已放弃'),
    cssClass: ['OfferBid-status0']
  }
]
// 待报名列表
const todoColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'rfxCode',
    headerText: i18n.t('RFX单号'),
    cssClass: 'field-content',
    width: '200'
  },
  {
    field: 'rfxName',
    headerText: i18n.t('标题')
  },
  {
    field: 'sourcingObj',
    headerText: i18n.t('询价对象')
  },
  {
    field: 'joinStatus',
    headerText: i18n.t('状态'),
    cssClass: 'OfferBid-status-class',
    // cellTools: [
    //   {
    //     id: "join",
    //     icon: "icon_solid_add",
    //     title: i18n.t("参与"),
    //     visibleCondition: (data) => {
    //       return data.joinStatus == 0;
    //     },
    //   },
    // ],
    valueConverter: {
      type: 'map',
      map: map,
      fields: { text: 'label', value: 'status' }
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('客户')
  },
  {
    field: 'bidEndTime',
    headerText: i18n.t('报价截止时间')
    //参与状态
    //应标截止时间
    // 当前轮次   roundNo
    //报价开始时间 bidStartTime
    //报价结束时间 bidEndTime
    //开标时间
    //议价截止时间
  },
  {
    field: 'sourcingDirection',
    headerText: i18n.t('询价方向'),
    valueConverter: {
      type: 'map',
      map: {
        forward: i18n.t('递增'),
        reverse: i18n.t('递减'),
        unlimited: i18n.t('无限制')
      }
    }
  },
  {
    field: 'biddingMode',
    headerText: i18n.t('询价方式'),
    valueConverter: {
      type: 'map',
      map: { open: i18n.t('公开'), target: i18n.t('邀请') }
    }
  },
  {
    field: 'purExecutorName',
    headerText: i18n.t('采购员')
  }
  //创建时间   createTime
  //备注  remark
]
// 已报名列表
const joinColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'rfxCode',
    headerText: i18n.t('RFX单号'),
    cssClass: 'field-content',
    width: '200'
  },
  {
    field: 'rfxName',
    headerText: i18n.t('标题')
  },
  {
    field: 'sourcingObj',
    headerText: i18n.t('询价对象')
  },
  {
    field: 'bidStatus',
    headerText: i18n.t('状态'),
    // cssClass: "OfferBid-status-class",
    // cellTools: [
    //   {
    //     id: "join",
    //     icon: "icon_solid_add",
    //     title: i18n.t("报价"),
    //     visibleCondition: (data) => {
    //       return data.bidStatus == 0;
    //     },
    //   },
    // ],
    valueConverter: {
      type: 'map',
      map: bidStatus,
      fields: { text: 'label', value: 'status' }
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('客户')
  },
  {
    field: 'bidEndTime',
    headerText: i18n.t('报价截止时间') //报名截止时间
    // 资格预审截止时间
    // 报价截止时间
  },
  {
    field: 'sourcingDirection',
    headerText: i18n.t('询价方向'),
    valueConverter: {
      type: 'map',
      map: {
        forward: i18n.t('递增'),
        reverse: i18n.t('递减'),
        unlimited: i18n.t('无限制')
      }
    }
  },
  {
    field: 'biddingMode',
    headerText: i18n.t('询价方式'),
    valueConverter: {
      type: 'map',
      map: { open: i18n.t('公开'), target: i18n.t('邀请') }
    }
  },
  {
    field: 'purExecutorName',
    headerText: i18n.t('采购员')
  }
  // 议价截止时间
  // 创建时间
  //备注
]

export const pageConfig = (url) => [
  {
    title: i18n.t('待报名'),
    toolbar: [],
    grid: {
      allowFiltering: true,
      useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
      frozenColumns: 2,
      columnData: todoColumnData,
      asyncConfig: {
        url,
        params: { status: 0, sourcingMode: 'invite_bids' },
        // queryBuilderWrap 非正常传参,放在参数子级里面
        queryBuilderWrap: 'queryBuilderDTO'
      }
    }
  },

  {
    title: i18n.t('已报名'),
    toolbar: [],
    grid: {
      allowFiltering: true,
      useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
      frozenColumns: 2,
      columnData: joinColumnData,
      // dataSource:[],
      asyncConfig: {
        url,
        params: { status: 1, sourcingMode: 'invite_bids' },
        queryBuilderWrap: 'queryBuilderDTO'
      }
      // dataSource: [
      //   {
      //     rfxCode: "RFXdanhao1",
      //     rfxName: "这里是标题",
      //     inquiryObjects: "询价对象1",
      //     status: 0,
      //     customerName: "客户",
      //     priceEndTime: "报价截止时间",
      //     inquiryDirection: "询价方向",
      //     inquiryWay: "询价方式",
      //     buyer: "采购员",
      //     tenderId: "144208458134695937",
      //     rfxId: "144208005070172214",
      //     roundId: "144208005086949463",
      //   },
      //   {
      //     rfxCode: "RFXdanhao2",
      //     rfxName: "这里是标题",
      //     inquiryObjects: "询价对象2",
      //     status: 0,
      //     customerName: "客户",
      //     priceEndTime: "报价截止时间",
      //     inquiryDirection: "询价方向",
      //     inquiryWay: "询价方式",
      //     buyer: "采购员",
      //     tenderId: "144158196003090496",
      //     rfxId: "144155052623900747",
      //     roundId: "144155052640677956",
      //   },
      // ],
    }
  }
  // {
  //   title: "已完成",
  //   toolbar: [],
  //   grid: { allowFiltering: true,
  //     frozenColumns: 2,
  //     columnData: joinedColumnData,
  //     asyncConfig: {
  //       url,
  //       params: { status: 2 },
  //       queryBuilderWrap: "queryBuilderDTO",
  //     },
  //   },
  // },
]
