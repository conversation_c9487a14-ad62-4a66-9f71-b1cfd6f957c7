<template>
  <div id="app">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
export default {
  components: {},
  data() {
    return {
      pageConfig: pageConfig(this.$API.quotationBidding.getOfferBidList)
    }
  },
  mounted() {},
  methods: {
    handleClickCellTool(e) {
      if (e.tool.id == 'join') {
        this.handleJoinEvent(e)
      } else {
        this.$router.push({
          path: 'offer-bid-part-in',
          query: { id: e.data.tenderId, rfxId: e.data.rfxId }
        })
      }
    },
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
      if (e.tabIndex != 0) {
        this.$router.push({
          path: 'offer-bid-part-in',
          query: { id: e.data.tenderId, rfxId: e.data.rfxId }
        })
      }
    },
    handleJoinEvent(e) {
      let data = {
        rfxId: e.data.rfxId,
        supplierId: e.data.supplierId,
        tenderId: e.data.tenderId
      }
      this.$dialog({
        data: {
          title: this.$t('参加'),
          message: this.$t('是否确认参加？')
        },
        success: () => {
          this.$API.quotationBidding.tenderJoin(data).then(() => {
            this.$router.push({
              path: 'offer-bid-part-in',
              query: { id: e.data.tenderId, rfxId: e.data.rfxId }
            })
          })
        }
      })
    }
  }
}
</script>
<style>
.OfferBid-status-class {
  margin-bottom: 5px;
}
.OfferBid-status0 {
  width: 56px;
  height: 20px;
  background: #f4f4f4;
  border-radius: 2px;
  padding: 4px;
  color: #9a9a9a;
}
.OfferBid-status1 {
  width: 56px;
  height: 20px;
  background: #eef2f9;
  border-radius: 2px;
  padding: 4px;
  color: #6386c1;
}
.OfferBid-status2 {
  width: 56px;
  height: 20px;
  background: #eef2f9;
  border-radius: 2px;
  padding: 4px;
  color: #6386c1;
}
</style>
<style>
.user-content {
  color: red;
}
body,
html {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
}
#app {
  padding: 0;
  height: 100%;
  width: 100%;
}
.test-btn {
  position: fixed;
  bottom: 10px;
  z-index: 2;
  left: 50px;
}
</style>
