import { i18n } from '@/main.js'
const map = [
  {
    status: 0,
    label: i18n.t('未参与'),
    cssClass: ['OfferBid-status0']
  },
  {
    status: 1,
    label: i18n.t('待开启报价'),
    cssClass: ['OfferBid-status1']
  },
  {
    status: 2,
    label: i18n.t('报价中'),
    cssClass: ['OfferBid-status2']
  },
  {
    status: 3,
    label: i18n.t('还价'),
    cssClass: ['OfferBid-status1']
  },
  {
    status: 4,
    label: i18n.t('待公布结果'),
    cssClass: ['OfferBid-status1']
  },
  {
    status: 5,
    label: i18n.t('已公布结果'),
    cssClass: ['OfferBid-status1']
  },
  {
    status: 6,
    label: i18n.t('继续报价'),
    cssClass: ['OfferBid-status1']
  }
]
const cellTools = [
  {
    id: 'join',
    icon: 'icon_solid_add',
    title: i18n.t('参加'),
    visibleCondition: (data) => {
      return data.status == 0
    }
  },
  {
    id: 'offerBid1',
    icon: 'icon_solid_add',
    title: i18n.t('报价'),
    visibleCondition: (data) => {
      return data.status == 2
    }
  },
  {
    id: 'offerBid2',
    icon: 'icon_solid_add',
    title: i18n.t('还价报价'),
    visibleCondition: (data) => {
      return data.status == 3
    }
  },
  {
    id: 'offerBid3',
    icon: 'icon_solid_add',
    title: i18n.t('继续报价'),
    visibleCondition: (data) => {
      return data.status == 6
    }
  },
  {
    id: 'result',
    icon: 'icon_solid_add',
    title: i18n.t('查看结果'),
    visibleCondition: (data) => {
      return data.status == 4
    }
  }
]
const todoColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'rfxCode',
    headerText: i18n.t('RFX单号'),
    cssClass: 'field-content',
    width: '200'
  },
  {
    field: 'rfxName',
    headerText: i18n.t('询价单标题')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    cssClass: 'OfferBid-status-class',
    cellTools: [
      {
        id: 'join',
        // icon: "icon_solid_add",
        title: i18n.t('参加'),
        visibleCondition: (data) => {
          return data.status == 0
        }
      }
    ],

    valueConverter: {
      type: 'map',
      map: map,
      fields: { text: 'label', value: 'status' }
    }
  },
  {
    field: 'roundNumber',
    headerText: i18n.t('询价次数')
  },
  {
    field: 'priceTimes',
    headerText: i18n.t('报价次数')
  },
  {
    field: 'priceStartTime',
    headerText: i18n.t('开始时间')
  },
  {
    field: 'priceEndTime',
    headerText: i18n.t('报价截止时间')
  },
  {
    field: 'customerName',
    headerText: i18n.t('客户')
  },
  {
    field: 'sourcingRange',
    headerText: i18n.t('寻源方式'),

    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('指定供应商'),
        2: i18n.t('公开招募')
      }
    }
  },
  {
    field: 'createUser',
    headerText: i18n.t('创建人')
  }
]
const joinColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'rfxCode',
    headerText: i18n.t('RFX单号'),
    cssClass: 'field-content',
    width: '200'
  },
  {
    field: 'rfxName',
    headerText: i18n.t('询价单标题')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    cssClass: 'OfferBid-status-class',

    cellTools: cellTools,
    valueConverter: {
      type: 'map',
      map: map,
      fields: { text: 'label', value: 'status' }
    }
  },
  {
    field: 'roundNumber',
    headerText: i18n.t('询价次数')
  },
  {
    field: 'priceTimes',
    headerText: i18n.t('报价次数')
  },
  {
    field: 'priceStartTime',
    headerText: i18n.t('开始时间')
  },
  {
    field: 'priceEndTime',
    headerText: i18n.t('报价截止时间')
  },
  {
    field: 'customerName',
    headerText: i18n.t('客户')
  },
  {
    field: 'sourcingRange',
    headerText: i18n.t('寻源方式'),

    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('指定供应商'),
        2: i18n.t('公开招募')
      }
    }
  },
  {
    field: 'createUser',
    headerText: i18n.t('创建人')
  }
]
const joinedColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'rfxCode',
    headerText: i18n.t('RFX单号'),
    cssClass: 'field-content',
    width: '200'
  },
  {
    field: 'rfxName',
    headerText: i18n.t('询价单标题')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    cssClass: 'OfferBid-status-class',

    cellTools: cellTools,
    valueConverter: {
      type: 'map',
      map: map,
      fields: { text: 'label', value: 'status' }
    }
  },
  {
    field: 'roundNumber',
    headerText: i18n.t('询价次数')
  },
  {
    field: 'priceTimes',
    headerText: i18n.t('报价次数')
  },
  {
    field: 'priceStartTime',
    headerText: i18n.t('开始时间')
  },
  {
    field: 'priceEndTime',
    headerText: i18n.t('报价截止时间')
  },
  {
    field: 'customerName',
    headerText: i18n.t('客户')
  },
  {
    field: 'customerName',
    headerText: i18n.t('公司')
  },
  {
    field: 'sourcingRange',
    headerText: i18n.t('寻源方式'),

    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('指定供应商'),
        2: i18n.t('公开招募')
      }
    }
  },
  {
    field: 'createUser',
    headerText: i18n.t('创建人')
  }
]
export const pageConfig = (url) => [
  {
    title: i18n.t('待报名'),
    toolbar: [],
    grid: {
      allowFiltering: true,
      frozenColumns: 2,
      columnData: todoColumnData,
      asyncConfig: {
        url,
        params: { status: 0 },
        queryBuilderWrap: 'queryBuilderDTO'
      }
    }
  },
  {
    title: i18n.t('参加未完成'),
    toolbar: [],
    grid: {
      allowFiltering: true,
      frozenColumns: 2,
      columnData: joinColumnData,
      asyncConfig: {
        url,
        params: { status: 1 },
        queryBuilderWrap: 'queryBuilderDTO'
      }
    }
  },
  {
    title: i18n.t('已完成'),
    toolbar: [],
    grid: {
      allowFiltering: true,
      frozenColumns: 2,
      columnData: joinedColumnData,
      asyncConfig: {
        url,
        params: { status: 2 },
        queryBuilderWrap: 'queryBuilderDTO'
      }
    }
  }
]
