<template>
  <div class="full-height r-d-container mt-flex">
    <div class="tree-view--wrap">
      <div class="trew-node--add">
        <div class="node-title">{{ $t('层级') }}</div>
      </div>
      <mt-common-tree
        v-if="treeViewData.dataSource.length"
        ref="treeView"
        class="tree-view--template"
        :un-button="true"
        :fields="treeViewData"
        :selected-nodes="selectedNodes"
        :expanded-nodes="expandedNodes"
        @nodeSelected="nodeSelected"
      ></mt-common-tree>
    </div>
    <div class="table-container">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
      />
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { rdToolBar, rdColumnData } from './config'
export default {
  data() {
    return {
      selectedNodes: [],
      expandedNodes: [],
      treeViewData: {
        //相关文件-目录结构-原数据
        nodeTemplate: function () {
          return {
            template: Vue.component('common', {
              template: `<div class="action-boxs">
                            <div>{{mtData.nodeName}}</div>
                          </div>`,
              data() {
                return { data: {} }
              },
              props: {
                mtData: {
                  // eslint-disable-line
                  // 拿到数据
                  type: Object,
                  default: () => {}
                }
              }
            })
          }
        },
        dataSource: [],
        id: 'id',
        text: 'nodeName',
        child: 'fileNodeResponseList'
      },
      pageConfig: [
        {
          toolbar: rdToolBar,
          grid: {
            allowFiltering: true,
            allowPaging: false,
            columnData: rdColumnData,
            dataSource: []
          }
        }
      ]
    }
  },

  mounted() {
    this.getFileFolderData()
  },

  methods: {
    getFileFolderData() {
      let _params = {
        docId: this.$route.query.rfxId
      }
      this.$API.supQBFiles.querySupFileNodeByRfxId(_params).then((res) => {
        this.$set(this.treeViewData, 'dataSource', res.data)
        if (Array.isArray(res.data) && res.data.length) {
          this.selectNodeId = res.data[0]['id']
          this.$set(this.pageConfig[0].grid, 'asyncConfig', {
            url: this.$API.supQBFiles.querySupFileByRfxId,
            params: {
              docId: this.$route.query.rfxId,
              parentId: this.selectNodeId
            },
            methods: 'get',
            recordsPosition: 'data'
          })
          this.selectedNodes = [res.data[0]['id']]
          this.expandedNodes = [res.data[0]['id']]
        }
      })
    },
    nodeSelected(event) {
      console.log('nodeSelected---', event)
      if (event?.nodeData?.id) {
        this.selectNodeId = event?.nodeData?.id
        this.$set(this.pageConfig[0].grid, 'asyncConfig', {
          url: this.$API.supQBFiles.querySupFileByRfxId,
          params: {
            docId: this.$route.query.rfxId,
            parentId: this.selectNodeId
          },
          methods: 'get',
          recordsPosition: 'data'
        })
      }
    },
    handleClickToolBar(e) {
      console.log('use-handleClickToolBar', e)
      if (e.toolbar.id === 'Add') {
        this.$dialog({
          modal: () =>
            import(/* webpackChunkName: "components/upload" */ 'COMPONENTS/Upload/index.vue'),
          data: {
            title: this.$t('上传')
          },
          success: (data) => {
            this.handleUploadFiles(data)
          }
        })
      } else if (e.toolbar.id == 'pull') {
        let _selectRows = e.grid.getSelectedRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        } else {
          let _url = _selectRows[0]['url']
          window.open(_url)
          // window.open(e.data.remoteUrl);
          // this.$API.fileService.downloadPrivateFile({
          //   id: _selectRows[0]["sysFileId"],
          // });
        }
      } else if (e.toolbar.id == 'delete') {
        let _selectRows = e.grid.getSelectedRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        } else {
          this.handleBatchDelete(_selectRows)
        }
      }
    },
    //批量删除操作
    handleBatchDelete(_selectGridRecords) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      this.handleDelete(_selectIds)
    },
    //删除文件
    handleDelete(ids) {
      let _params = {
        idList: ids
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.rfxFiles.deleteSourcingFileById(_params).then(() => {
            this.$store.commit('endLoading')
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    //单元格按钮，点击
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      if (e.tool.id == 'download') {
        window.open(e.data.url)
        // window.open(e.data.remoteUrl);
        // this.$API.fileService.downloadPrivateFile({ id: e.data.sysFileId });
      } else if (e.tool.id == 'delete') {
        this.handleDelete([e.data.id])
      }
    },
    //单元格title文字点击，文件名点击预览
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
      if (e.field == 'fileName') {
        this.$API.fileService.getMtPreview().then((res) => {
          window.open(`${res.data}/onlinePreview?url=${e.data.url}`)
        })
      }
    },
    //执行上传文件
    handleUploadFiles(data) {
      let { id, fileName, fileSize, fileType, url, remoteUrl, sysName } = data
      let _params = {
        docId: this.$route.query.rfxId,
        parentId: this.selectNodeId,
        fileName: fileName,
        fileSize: fileSize,
        fileType: fileType,
        remoteUrl: remoteUrl,
        sysFileId: id,
        sysName: sysName,
        url: url
        // docType: "",
        // id: 0,
        // nodeId: 0,
        // nodeName: "",
        // nodeType: 0,
        // sortValue: 0,
      }
      this.$API.quotationBidding.saveSupplierHeaderFile(_params).then(() => {
        this.$toast({
          content: this.$t('上传成功'),
          type: 'success'
        })
        this.$refs.templateRef.refreshCurrentGridData()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.r-d-container {
  width: 100%;
  margin-top: 20px;
  margin-left: 10px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  .tree-view--wrap {
    min-width: 300px;
    background: #fff;
    box-shadow: inset -1px 0 0 0 rgba(232, 232, 232, 1);
    .trew-node--add {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      height: 50px;
      padding: 0 20px;
      color: #4f5b6d;
      box-shadow: inset 0 -1px 0 0 rgba(232, 232, 232, 1);
      .node-title {
        font-size: 14px;
        color: #292929;
        display: inline-block;
        position: relative;
        padding-left: 10px;

        &:before {
          content: '';
          position: absolute;
          width: 3px;
          height: 14px;
          background: #6386c1;
          border-radius: 0 2px 2px 0;
          left: 0;
          top: 0;
        }
      }
    }
    .tree-view--template {
      width: 100%;
      padding-top: 10px;
      padding-left: 20px;
    }
  }
}
</style>
