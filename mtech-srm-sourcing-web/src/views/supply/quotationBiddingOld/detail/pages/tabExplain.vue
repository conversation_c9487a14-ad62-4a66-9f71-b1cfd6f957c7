<template>
  <div id="app">
    <div>{{ remark }}</div>
  </div>
</template>

<script>
export default {
  name: 'App',
  components: {},
  props: {
    remark: {
      type: String,
      default: this.$t('暂无说明')
    }
  },
  data() {
    return {}
  }
}
</script>
<style>
.OfferBid-status0 {
  width: 56px;
  height: 20px;
  background: #f4f4f4;
  border-radius: 2px;
  padding: 4px;
  color: #9a9a9a;
}
.OfferBid-status2 {
  width: 56px;
  height: 20px;
  background: #eef2f9;
  border-radius: 2px;
  padding: 4px;
  color: #6386c1;
}
</style>
<style>
.user-content {
  color: red;
}
body,
html {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
}
#app {
  padding: 0;
  height: 100%;
  width: 100%;
}
.test-btn {
  position: fixed;
  bottom: 10px;
  z-index: 2;
  left: 50px;
}
</style>
