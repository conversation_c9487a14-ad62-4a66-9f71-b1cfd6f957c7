<template>
  <div class="hall-view">
    <div class="own-line">
      <!-- <supply-manage></supply-manage> -->
      <time-bid></time-bid>
      <time-rank></time-rank>
    </div>
  </div>
</template>

<script>
export default {
  components: {
    timeBid: () =>
      import(
        /* webpackChunkName: "router/supply/quotationBidding/detail/components/tabHall/timeBid" */ '../components/tabHall/timeBid.vue'
      ),
    timeRank: () =>
      import(
        /* webpackChunkName: "router/supply/quotationBidding/detail/components/tabHall/timeRank" */ '../components/tabHall/timeRank.vue'
      )
  }
}
</script>

<style lang="scss" scoped>
.hall-view {
  .own-line {
    width: 100%;
    height: calc(100%);
    display: flex;
  }
  .supply-manage,
  .progress-summary {
    flex: 2;
    height: 100%;
    overflow: hidden;
    margin-right: 10px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 6px;
    box-shadow: 0 0 8px 0 rgba(137, 120, 120, 0.06);
    display: flex;
    flex-direction: column;
  }

  .time-bid,
  .time-rank {
    // width: 454px;
    flex: 1;
    height: 100%;
    overflow: hidden;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 6px;
    box-shadow: 0 0 8px 0 rgba(137, 120, 120, 0.06);
    display: flex;
    flex-direction: column;
  }
  .time-bid {
    flex: 2;
    margin-right: 10px;
  }
}
</style>
