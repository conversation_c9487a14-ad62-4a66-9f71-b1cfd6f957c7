<template>
  <div class="evaluation-container">
    <!-- 顶部banner -->
    <div class="banner-line mt-flex">
      <div class="left-part mt-flex">
        <div class="label-title">{{ $t('当前轮次') }}：</div>
        <div class="label-select">
          <mt-select
            :width="200"
            css-class="e-outline"
            float-label-type="Never"
            :data-source="evaluationArr"
            :show-clear-button="true"
            :value="evStep"
            @change="changeEV"
            :placeholder="$t('请选择阶段')"
          ></mt-select>
        </div>
      </div>
      <div class="right-part mt-flex">
        <div class="tab-item" :class="{ active: tabId === 0 }" @click="changTab(0)">
          {{ $t('所有评标') }}
        </div>
        <div class="tab-item" :class="{ active: tabId === 1 }" @click="changTab(1)">
          {{ $t('我的评标') }}
        </div>
      </div>
    </div>
    <!-- 底部内容 -->
    <div class="bt-detail mt-flex">
      <div class="ev-item-box">
        <!-- 左侧的单个item -->
        <div class="ev-item" :class="{ active: productId === 0 }" @click="selectProduct(0)">
          <div class="tp-line mt-flex">
            <div class="title-left flex1">10-10006152</div>
            <div class="title-right flex1">{{ $t('产品名称') }}</div>
          </div>
          <div class="tp-bottom mt-flex">
            <div class="title-left flex1">{{ $t('描述') }}</div>
            <div class="title-right flex1">0001</div>
          </div>
        </div>

        <div class="ev-item" :class="{ active: productId === 1 }" @click="selectProduct(1)">
          <div class="tp-line mt-flex">
            <div class="title-left flex1">10-10006152</div>
            <div class="title-right flex1">{{ $t('产品名称') }}</div>
          </div>
          <div class="tp-bottom mt-flex">
            <div class="title-left flex1">{{ $t('描述') }}</div>
            <div class="title-right flex1">0001</div>
          </div>
        </div>
      </div>
      <div class="ev-detail">
        <!-- tab内容 -->
        <div class="detail-banner">
          <div class="title-banner">{{ $t('低合金板') }}_Q345-B_10</div>
          <div class="detail-info mt-flex">
            <div class="flex1 info-item">
              <mt-icon name="MT_Description"></mt-icon>
              {{ $t('需求数量：') }}4kg
            </div>
            <div class="flex1 info-item">
              <mt-icon name="MT_Description"></mt-icon>
              {{ $t('交付日期：') }}2015-02-28
            </div>
            <div class="flex1 info-item">
              <mt-icon name="MT_Description"></mt-icon>
              {{ $t('最低报价：') }}¥ 442.00
            </div>
            <div class="flex1 info-item">
              <mt-icon name="MT_Description"></mt-icon>
              {{ $t('最初报价：') }}¥ 442.00 / 117KG
            </div>
            <div class="flex1 info-item">
              <mt-icon name="MT_Description"></mt-icon>
              {{ $t('报价总计：') }}¥ 1442.00
            </div>
          </div>
        </div>

        <div class="detail-items">
          <!-- 循环单元 -->
          <div class="detail-item mt-flex">
            <div class="detail-infos detail-infos-0">
              <div class="between">
                <div class="ra-index">
                  <svg-icon :icon-class="`icon_rank_1`"></svg-icon>
                  <!-- <span v-else>{{ index + 1 }}</span> -->
                </div>
                <div class="names">{{ $t('芈娜魅旗舰店') }}</div>
              </div>

              <div class="between">
                <div class="price-box">
                  <div class="tp-price">¥<span class="price-inner">42.00</span></div>
                  <div class="bt-des">{{ $t('最低投标价') }}</div>
                </div>
                <div class="rank-num">99</div>
              </div>
            </div>
            <div class="detail-slider">
              <!-- 换张晓如新加的滑动组件 -->
              <mt-tabs
                :tab-id="$utils.randomString()"
                :e-tab="false"
                :data-source="tabSource"
                :tabs-solt="true"
                @handleSelectTab="handleSelectTab"
              >
                <template #templateContent="{ props }">
                  <div class="prop-box">
                    <div class="rank-name">{{ props.rank }}</div>
                    <div class="rank-desc mt-flex">
                      <span class="name-txt">{{ props.name }}</span>
                      <span class="type-txt type-1">
                        <i>{{ props.type }}</i></span
                      >
                    </div>
                  </div>
                </template>
              </mt-tabs>
            </div>
          </div>

          <div class="detail-item mt-flex">
            <div class="detail-infos detail-infos-1">
              <div class="between">
                <div class="ra-index">
                  <svg-icon :icon-class="`icon_rank_2`"></svg-icon>
                  <!-- <span v-else>{{ index + 1 }}</span> -->
                </div>
                <div class="names">{{ $t('芈娜魅旗舰店') }}</div>
              </div>

              <div class="between">
                <div class="price-box">
                  <div class="tp-price">¥<span class="price-inner">42.00</span></div>
                  <div class="bt-des">{{ $t('最低投标价') }}</div>
                </div>
                <div class="rank-num">99</div>
              </div>
            </div>
            <div class="detail-slider">
              <!-- 换张晓如新加的滑动组件 -->
              <mt-tabs
                :tab-id="$utils.randomString()"
                :e-tab="false"
                :data-source="tabSource"
                :tabs-solt="true"
                @handleSelectTab="handleSelectTab"
              >
                <template #templateContent="{ props }">
                  <div class="prop-box">
                    <div class="rank-name">{{ props.rank }}</div>
                    <div class="rank-desc mt-flex">
                      <span class="name-txt">{{ props.name }}</span>
                      <span class="type-txt type-1">
                        <i>{{ props.type }}</i></span
                      >
                    </div>
                  </div>
                </template>
              </mt-tabs>
            </div>
          </div>

          <div class="detail-item mt-flex">
            <div class="detail-infos detail-infos-2">
              <div class="between">
                <div class="ra-index">
                  <svg-icon :icon-class="`icon_rank_3`"></svg-icon>
                  <!-- <span v-else>{{ index + 1 }}</span> -->
                </div>
                <div class="names">{{ $t('芈娜魅旗舰店') }}</div>
              </div>

              <div class="between">
                <div class="price-box">
                  <div class="tp-price">¥<span class="price-inner">42.00</span></div>
                  <div class="bt-des">{{ $t('最低投标价') }}</div>
                </div>
                <div class="rank-num">99</div>
              </div>
            </div>
            <div class="detail-slider">
              <!-- 换张晓如新加的滑动组件 -->
              <mt-tabs
                :tab-id="$utils.randomString()"
                :e-tab="false"
                :data-source="tabSource"
                :tabs-solt="true"
                @handleSelectTab="handleSelectTab"
              >
                <template #templateContent="{ props }">
                  <div class="prop-box">
                    <div class="rank-name">{{ props.rank }}</div>
                    <div class="rank-desc mt-flex">
                      <span class="name-txt">{{ props.name }}</span>
                      <span class="type-txt type-1">
                        <i>{{ props.type }}</i></span
                      >
                    </div>
                  </div>
                </template>
              </mt-tabs>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tabId: 0,
      productId: 0,
      evStep: 1,
      evaluationArr: [
        { text: this.$t('第一轮'), value: 1 },
        { text: this.$t('第二轮'), value: 2 },
        { text: this.$t('第三轮'), value: 3 }
      ],
      tabSource: [
        // statusList 如果hallStatus在这个状态array中的就显示
        {
          title: '',
          rank: 99,
          name: this.$t('宋军'),
          type: this.$t('商')
        },
        {
          title: '',
          rank: 66,
          name: this.$t('张杰'),
          type: this.$t('技')
        }
      ]
    }
  },
  methods: {
    changTab(id) {
      this.tabId = id
    },
    selectProduct(id) {
      this.productId = id
    },
    changeEV() {},
    handleSelectTab() {}
  }
}
</script>

<style lang="scss" scoped>
.flex1 {
  flex: 1;
}

.evaluation-container {
  width: 100%;
  min-height: 100%;
  background: #fff;
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 4px;

  .banner-line {
    height: 50px;
    line-height: 50px;
    background: rgba(250, 250, 250, 1);
    border-radius: 4px 4px 0 0;
    border-bottom: 1px solid rgba(232, 232, 232, 1);

    .left-part {
      padding: 0 20px;
      width: 357px;
      align-items: center;
    }
    .right-part {
      padding: 0 20px;
      flex: 1;

      .tab-item {
        cursor: pointer;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(154, 154, 154, 1);
        padding: 0 20px;
      }
      .active {
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        position: relative;

        &:after {
          content: ' ';
          display: inline-block;
          width: calc(100% - 56px);
          height: 2px;
          background: rgba(99, 134, 193, 1);
          border: 1px solid rgba(0, 70, 156, 0.1);
          border-radius: 4px;
          text-align: center;
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          top: 42px;
        }
      }
    }
  }

  .bt-detail {
    width: 100%;

    .ev-item-box {
      width: 357px;
      box-sizing: border-box;
      padding: 20px;

      .ev-item {
        padding: 10px 20px;
        background: rgba(255, 255, 255, 1);
        border: 1px solid rgba(232, 232, 232, 1);
        border-radius: 4px;
        box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
        margin-bottom: 20px;
        cursor: pointer;

        .tp-line {
          justify-content: space-between;

          .title-left {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            height: 14px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: 600;
            color: rgba(35, 43, 57, 1);
            text-align: left;
          }
          .title-right {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            height: 14px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: normal;
            color: rgba(35, 43, 57, 1);
            text-align: right;
          }
        }
        .tp-bottom {
          margin-top: 6px;
          justify-content: space-between;
          .title-left {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            height: 12px;
            font-size: 12px;
            font-family: PingFangSC;
            font-weight: 600;
            color: #9a9a9a;
            text-align: left;
          }
          .title-right {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            height: 12px;
            font-size: 12px;
            font-family: PingFangSC;
            font-weight: normal;
            color: #6386c1;
            text-align: right;
          }
        }
      }

      .active {
        background: rgba(245, 246, 249, 1);
        border: 1px solid rgba(232, 232, 232, 1);
        border-radius: 4px;
        box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
        position: relative;
        &::before {
          content: ' ';
          display: inline-block;
          width: 3px;
          height: 100%;
          position: absolute;
          left: 0;
          top: 0;
          background: rgba(99, 134, 193, 1);
          border-radius: 4px 0 0 4px;
        }
      }
    }
    .ev-detail {
      flex: 1;
      border-left: 10px solid rgba(232, 232, 232, 1);
      overflow-x: auto;
      padding: 20px;

      .detail-banner {
        border-radius: 4px 4px 0 0;
        padding: 20px;
        background: url(../../../../../assets/images/evdetailbanner.jpg) center no-repeat;
        background-size: 100% 100%;
        border: 1px solid rgba(232, 232, 232, 1);
        min-width: 1120px;
        .title-banner {
          height: 20px;
          font-size: 20px;
          font-family: PingFangSC;
          font-weight: 600;
          color: rgba(41, 41, 41, 1);
        }
        .detail-info {
          height: 14px;
          margin-top: 10px;
          font-size: 14px;
          line-height: 14px;
          color: rgba(157, 170, 191, 1);

          .info-item {
            white-space: nowrap;
          }
        }
      }

      .detail-items {
        min-width: 1120px;
        box-sizing: border-box;
        padding: 20px;
        border-left: 1px solid rgba(232, 232, 232, 1);
        border-right: 1px solid rgba(232, 232, 232, 1);

        .detail-item {
          height: 68px;
          background: rgba(255, 255, 255, 1);
          border-radius: 4px 0 0 4px;
          margin-bottom: 20px;
          .detail-infos {
            width: 60%;
            display: flex;
            justify-content: space-between;
            padding: 0 0 0 20px;

            .between {
              justify-content: space-between;
              display: flex;
              align-items: center;
            }

            .ra-index {
              width: 26px;
              text-align: center;
              margin-right: 10px;
              /deep/ .svg-icon {
                width: 26px;
                height: 28px;
              }
            }

            .names {
              font-size: 18px;

              font-family: PingFangSC;
              font-weight: 500;
              color: rgba(41, 41, 41, 1);
            }
            .price-box {
              display: flex;
              flex-direction: column;
              justify-content: center;
              .tp-price {
                height: 24px;
                font-size: 24px;
                font-family: PingFangSC;
                font-weight: 500;
                color: rgba(99, 134, 193, 1);
                .price-inner {
                  padding-left: 6px;
                }
              }
              .bt-des {
                height: 12px;
                font-size: 12px;
                font-family: PingFangSC;
                font-weight: normal;
                color: rgba(154, 154, 154, 1);
                margin-top: 8px;
              }
            }
            .rank-num {
              font-size: 60px;
              font-family: DINAlternate;
              font-weight: bold;
              color: rgba(99, 134, 193, 0.4);
              padding-left: 30px;
              padding-right: 30px;
            }
          }
          .detail-infos-0 {
            background: linear-gradient(
              90deg,
              rgba(238, 185, 31, 0.1) 0%,
              rgba(238, 185, 31, 0) 100%
            );
          }
          .detail-infos-1 {
            background: linear-gradient(
              90deg,
              rgba(125, 124, 148, 0.1) 0%,
              rgba(126, 125, 149, 0) 100%
            );
          }
          .detail-infos-2 {
            background: linear-gradient(
              90deg,
              rgba(194, 138, 100, 0.1) 0%,
              rgba(194, 138, 100, 0) 100%
            );
          }
          .detail-slider {
            width: 40%;

            & /deep/ .mt-tabs {
              width: 100%;
              height: 100%;
              .tabs-container {
                width: 100%;
                height: 100%;
              }
            }
          }
        }
      }
    }
  }
}
</style>

<style lang="scss">
.detail-item {
  .mt-tabs-container {
    width: 100%;
    .tab-wrap {
      padding: 0 !important;
    }
  }
}
.tab-item {
  .prop-box {
    text-align: center;
    .rank-name {
      height: 16px;
      font-size: 16px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(154, 154, 154, 1);
    }
    .rank-desc {
      height: 12px;
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(41, 41, 41, 1);
      margin-top: 8px;
      display: flex;
      justify-content: center;

      .type-txt {
        width: 12px;
        height: 12px;
        background: rgba(237, 161, 51, 0.1);
        border-radius: 1px;
        font-size: 8px;
        font-family: PingFangSC;
        font-weight: 500;
        margin-left: 6px;
        display: inline-block;
        i {
          transform: scale(0.8);
        }
      }
      .type-1 {
        color: rgba(99, 134, 193, 1);
        background: rgba(99, 134, 193, 0.3);
        border-radius: 1px;
      }
      .type-2 {
        background: rgba(237, 161, 51, 0.3);
        color: rgba(237, 161, 51, 1);
      }
    }
  }
}
</style>
