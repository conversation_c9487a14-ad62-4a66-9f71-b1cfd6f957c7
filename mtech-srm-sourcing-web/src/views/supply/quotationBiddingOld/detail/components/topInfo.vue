<template>
  <div class="top-info">
    <div class="lf-wrap">
      <div class="detail-info">
        <div class="name-wrap">
          <div class="first-line">
            <span class="code">{{ (detailInfo && detailInfo.rfxCode) || '-' }}</span>
            <span class="tags tags-1" v-if="detailInfo && detailInfo.businessTypeName">{{
              (detailInfo && detailInfo.businessTypeName) || '-'
            }}</span>
            <span class="tags tags-2" v-if="detailInfo && detailInfo.recContractTypeName">{{
              (detailInfo && detailInfo.recContractTypeName) || '-'
            }}</span>
          </div>

          <div class="second-line">
            <div class="cai-name">
              {{ (detailInfo && detailInfo.rfxName) || '' }}
            </div>
            <ul class="labels">
              <li>
                <mt-icon name="icon_Department"></mt-icon>
                <span
                  >{{ $t('客户名称') }}：{{ (detailInfo && detailInfo.purCompanyName) || '-' }}
                </span>
              </li>
              <!-- <li>
                <mt-icon name="icon_Company"></mt-icon>
                <span
                  >工厂/地点：{{
                    (detailInfo && detailInfo.companyName) || "-"
                  }}</span
                >
              </li> -->
              <li>
                <mt-icon name="icon_outline_Datetimeselection"></mt-icon>
                <span
                  >{{ $t('发布时间') }}：{{ (detailInfo && detailInfo.publishTime) || '-' }}</span
                >
              </li>
            </ul>
          </div>
        </div>

        <!-- <div class="btns-wrap">
          <mt-button>{{ $t('提交立项') }}</mt-button>
        </div> -->
      </div>

      <mt-tabs
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabSource"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>
    </div>

    <div class="rg-wrap">
      <div class="top-wrap">
        <div class="lefts">
          <div class="top-title">
            <div class="breathe-btn breathe-btn-1"></div>
            <div class="breathe-btn breathe-btn-2"></div>
          </div>
          <span class="top-end">{{ $t('报价结束') }}</span>
        </div>
        <div class="right" v-if="leftDay > 0">{{ leftDay }}{{ $t('天') }}</div>
      </div>
      <timer-clock :left-time="leftTime"></timer-clock>
    </div>
  </div>
</template>

<script>
import { tagList } from '../mock/tabHall'
import { dataSource } from '../mock/index'
export default {
  components: {
    timerClock: () =>
      import(
        /* webpackChunkName: "components/sourcing-project/timerClock" */ 'COMPONENTS/SourcingProject/timerClock.vue'
      )
  },
  props: {
    detailInfo: {
      type: Object,
      required: true,
      defaut: () => {
        return {}
      }
    },
    tabSource: {
      type: Array,
      required: false,
      default: () => []
    }
  },
  watch: {
    detailInfo() {
      this.getCurrentTurnsInfo()
    }
  },
  data() {
    return {
      tagList,
      dataSource,
      leftDay: 1,
      leftTime: 0 // 剩余秒
    }
  },
  methods: {
    handleSelectTab(e, item) {
      this.$bus.$emit('changeTab', e, item)
    },
    getCurrentTurnsInfo() {
      let _nowTime = this.detailInfo.currentTime
      let _endTime = this.detailInfo.bidEndTime
      let _leftSeconds = parseInt((_endTime - _nowTime) / 1000)
      this.leftDay = parseInt(_leftSeconds / 24 / 60 / 60)
      this.leftTime = _leftSeconds - this.leftDay * 24 * 60 * 60
    }
  }
}
</script>

<style lang="scss" scoped>
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.top-info {
  width: 100%;
  height: 120px;
  display: flex;
  background: rgba(245, 248, 251, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 0 8px 0 0;

  .lf-wrap {
    flex: 1;
    overflow-x: hidden;
    height: 120px;
    background: linear-gradient(rgba(99, 134, 193, 0.06), rgba(99, 134, 193, 0.06)),
      linear-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 1));
    border-right: 1px solid rgba(232, 232, 232, 1);

    .detail-info {
      padding: 20px 20px 3px 20px;
      display: flex;
      line-height: 1;

      .name-wrap {
        flex: 1;

        .first-line {
          display: flex;
          align-items: center;
          .code {
            font-size: 20px;
            font-family: DINAlternate;
            font-weight: bold;
            color: rgba(41, 41, 41, 1);
          }
          .tags {
            font-size: 12px;
            font-family: PingFangSC;
            font-weight: 500;
            padding: 4px;
            border-radius: 2px;
            margin-left: 10px;

            &-1 {
              color: rgba(237, 161, 51, 1);
              background: rgba(237, 161, 51, 0.1);
            }
            &-2 {
              color: #6386c1;
              background: rgba(99, 134, 193, 0.1);
            }
          }
        }

        .second-line {
          display: flex;
          align-items: center;
          margin-top: 10px;
          .cai-name {
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: normal;
            color: rgba(41, 41, 41, 1);
            @extend .text-ellipsis;
          }
          ul {
            display: flex;
            li {
              margin-left: 30px;
              font-size: 14px;
              font-family: PingFangSC;
              font-weight: normal;
              color: rgba(157, 170, 191, 1);

              .mt-icons {
                font-size: 12px;
              }
              span {
                vertical-align: text-bottom;
                margin-left: 4px;
                @extend .text-ellipsis;
              }
            }
          }
        }
      }

      .btns-wrap {
        /deep/ .mt-button {
          margin-right: 20px;
          button {
            width: 76px;
            height: 34px;
            background: rgba(255, 255, 255, 1);
            border: 1px solid rgba(0, 70, 156, 0.1);
            border-radius: 4px;
            box-shadow: unset;
            padding: 0;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: 500;
            color: rgba(0, 70, 156, 1);
          }
        }
      }
    }

    /deep/ .mt-tabs {
      width: 100%;

      .mt-tabs-container {
        background: transparent;
      }
    }
  }
  .rg-wrap {
    width: 305px;
    height: 100%;
    padding: 20px;

    .top-wrap {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      margin-bottom: 10px;
      .lefts {
        display: inline-flex;
        align-items: center;
        .top-title {
          width: 20px;
          height: 20px;
          margin-right: 10px;
          position: relative;
          .breathe-btn {
            border-radius: 50%;
            position: absolute;
            background: rgba(99, 134, 193, 1);

            &-1 {
              width: 10px;
              height: 10px;
              left: 5px;
              top: 5px;
              animation: breathe1 2s ease-in-out infinite alternate;
            }
            &-2 {
              width: 20px;
              height: 20px;
              left: 0;
              top: 0;
              animation: breathe2 2s ease-in-out infinite alternate;
            }
          }
          @keyframes breathe1 {
            0% {
              opacity: 0.3;
              box-shadow: 0 0 20px 0 rgba(99, 134, 193, 1);
            }
            50% {
              opacity: 0.5;
              transform: scale(0.7);
              box-shadow: 0 0 20px 0 rgba(99, 134, 193, 1);
            }
            100% {
              opacity: 0.3;
              box-shadow: 0 0 20px 0 rgba(99, 134, 193, 1);
            }
          }
          @keyframes breathe2 {
            0% {
              opacity: 0.5;
              box-shadow: 0 0 20px 0 rgba(99, 134, 193, 1);
            }
            50% {
              opacity: 0.8;
              transform: scale(0.7);
              box-shadow: 0 0 20px 0 rgba(99, 134, 193, 1);
            }
            100% {
              opacity: 0.5;
              box-shadow: 0 0 20px 0 rgba(99, 134, 193, 1);
            }
          }
        }
      }
    }
  }
}
</style>
