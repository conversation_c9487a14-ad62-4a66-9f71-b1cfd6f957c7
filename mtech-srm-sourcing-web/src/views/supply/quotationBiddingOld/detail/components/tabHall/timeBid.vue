<template>
  <div class="time-bid">
    <title-name
      icon-name="icon_solid_Graph"
      :name="$t('实时竞价')"
      :tab-index="0"
      :timer="statistsicDate"
      @click.native="changeChart"
    ></title-name>

    <div class="chart-box">
      <div class="left-chart">
        <div class="titles" v-if="matierialList && matierialList[currentIndex]">
          {{ matierialList[currentIndex].purOrgName || '' }}
          <div class="price" :title="matierialList[currentIndex].fieldData">
            ¥ {{ matierialList[currentIndex].fieldData }}
          </div>
        </div>
        <mt-chart
          ref="lineChart"
          :series-data-source="seriesDataSource"
          :primary-x-axis="primaryXAxis"
          :primary-y-axis="primaryYAxis"
          :zoom-settings="zoom"
          align="center"
          :tooltip="tooltip"
          :chart-area="chartArea"
          :width="chartSize + '%'"
          :height="chartSize + '%'"
          :legend-settings="legendSettings"
        >
        </mt-chart>
      </div>

      <ul class="right-list">
        <li
          v-for="(item, index) in matierialList"
          :key="index"
          :class="[index == currentIndex && 'active-li']"
          @click="changeWuliao(index)"
        >
          <div class="title-box">
            <div class="name" :title="item.itemName">{{ item.itemName }}</div>
          </div>

          <div class="price" :title="item.fieldData">¥ {{ item.fieldData }}</div>

          <div class="company" :title="item.categoryName">
            {{ item.categoryName }}
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import MtChart from '@mtech-ui/chart'
Vue.use(MtChart)
import utils from '@/utils/utils'
export default {
  components: {
    titleName: () =>
      import(
        /* webpackChunkName: "components/sourcing-project/panel-title" */ 'COMPONENTS/SourcingProject/panelTitle.vue'
      )
  },
  data() {
    return {
      utils,
      currentIndex: 0,
      statistsicDate: '', // 时间
      matierialList: [],
      supplierNowPriceParentDTOListMap: new Map(),
      zoom: {
        //缩放
        enableMouseWheelZooming: true,
        enableSelectionZooming: true,
        enablePan: true,
        enablePinchZooming: true,
        enableScrollbar: true
      },
      primaryXAxis: {
        valueType: 'Category',
        interval: 1,
        majorGridLines: { width: 1 },
        labelIntersectAction: 'Rotate90'
      },
      primaryYAxis: {
        labelFormat: '{value}'
        // lineStyle: { width: 1 },
        // majorTickLines: { width: 0 },
        // minorTickLines: { width: 0 }
      },
      tooltip: {
        enable: true
      },
      chartArea: {
        border: {
          width: 0
        }
      },
      legendSettings: { visible: true },
      seriesDataSource: [],
      chartSize: 100,
      chartColors: ['#6386C1', '#EDA133', '#54BF00', '#ED5633', '#9963FD']
    }
  },
  mounted() {
    this.$bus.$on('changeKanban', this.changeChart)
    this.getData()
  },

  methods: {
    getData() {
      let params = {
        rfxId: this.$route.query.rfxId
      }
      this.$API.quotationBidding.getRFXPriceInfo(params).then((res) => {
        this.statistsicDate = res.data.statistsicDate
        this.matierialList = res.data.rfxPurPackageDTOList || []
        this.setData()
      })
    },

    setData() {
      let _seriesDataSource = []
      let _priceList = this.matierialList[this.currentIndex]
        ? this.matierialList[this.currentIndex]['supplierNowPriceDTOList']
        : undefined
      if (!Array.isArray(_priceList) || _priceList.length < 1) {
        this.seriesDataSource = [{ dataSource: [] }]
        return
      }
      _priceList.forEach((item, index) => {
        let _oneline = {
          type: 'Spline',
          xName: 'XValue',
          yName: 'YValue',
          name: item[0] && item[0].supplierName,
          fill: this.chartColors[index % 5],
          width: 2,
          dataSource: [],
          marker: {
            visible: true,
            height: 10,
            width: 10
          }
        }
        item.forEach((pointItem) => {
          _oneline.dataSource.push({
            XValue: utils.formatTime(new Date(pointItem.bidTime), 'mm-dd HH:MM'), // "07-21 15:30"
            YValue: pointItem.taxedUnitPrice // 15623
          })
        })
        _seriesDataSource.push(_oneline)
      })
      this.seriesDataSource = _seriesDataSource
    },

    changeWuliao(index) {
      this.currentIndex = index
      this.setData()
    },

    changeChart() {
      var myEvent = new Event('resize')
      window.dispatchEvent(myEvent)
    }
  }
}
</script>

<style lang="scss" scoped>
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.wihe100 {
  width: 100%;
  height: 100%;
}

.chart-box {
  width: 100%;
  height: 100%;
  display: flex;
  overflow: hidden;

  .left-chart {
    flex: 1;
    height: 100%;
    position: relative;
    .titles {
      padding: 10px;
      font-size: 12px;
      background: #fff;
      border-radius: 1px;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.06);
      white-space: nowrap;
      position: absolute;
      z-index: 1;
      left: 14%;
      top: 4%;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 2px;
        height: 100%;
        background: rgba(99, 134, 193, 0.5);
        border-radius: 1px 0 0 1px;
      }
    }
    /deep/ .mt-chart {
      @extend .wihe100;
      .e-chart {
        @extend .wihe100;
      }
    }
  }

  .right-list {
    width: 146px;
    height: 100%;
    padding: 10px 20px;
    overflow-y: auto;
    overflow-x: hidden;
    li {
      width: 106px;
      margin-bottom: 20px;
      padding: 6px 10px;
      background: rgba(255, 255, 255, 1);
      border-radius: 4px;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.06);
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      cursor: pointer;

      &:last-child {
        margin-bottom: 0;
      }

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 2px;
        height: 100%;
        background: #b1b1b1;
        border-radius: 4px 0 0 4px;
      }

      &.active-li {
        .price {
          color: rgba(99, 134, 193, 1);
        }
        &::before {
          background: rgba(99, 134, 193, 1);
        }
      }

      .title-box {
        display: flex;
        justify-content: space-between;
        .name {
          font-size: 12px;
          @extend .text-ellipsis;
        }
        .mt-icons {
          font-size: 12px;
          transform: scale(0.5);
        }
        .icon_grey {
          width: 8px;
          height: 2px;
          background: rgba(154, 154, 154, 0.5);
          border-radius: 1px;
        }
      }

      .price {
        margin: 6px 0;
        font-size: 16px;
        font-family: DINAlternate;
        font-weight: bold;
        color: #9a9a9a;
        @extend .text-ellipsis;
      }

      .company {
        font-size: 12px;
        @extend .text-ellipsis;
      }
    }
  }
}
</style>
