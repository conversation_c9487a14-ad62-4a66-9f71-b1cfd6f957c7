<template>
  <!-- 大厅详情页面 -->
  <div class="full-height hall-detail">
    <!-- 流程看板 -->
    <!-- <div
      :class="['progress-kanban', isKanbanBigger && 'progress-kanban-bigger']"
    >
      <process-kanban :is-kanban-bigger="isKanbanBigger"></process-kanban>

      <div class="shrink" @click="isKanbanBigger = !isKanbanBigger">
        <mt-icon v-show="isKanbanBigger" name="icon_arrow_left"></mt-icon>
        <mt-icon v-show="!isKanbanBigger" name="icon_arrow_right"></mt-icon>
      </div>
    </div> -->

    <!-- 中间内容 -->
    <div :class="['main-context', isKanbanBigger && 'main-context-small']">
      <top-info-view :detail-info="quotedPriceData" :tab-source="tabSource"></top-info-view>

      <div class="middle-wrap">
        <!-- 1. 大厅 -->
        <tab-hall class="tab-hall" v-if="moduleType == 4"></tab-hall>

        <!-- 2. 采购明细 -->
        <tab-purchase-detail
          v-else-if="moduleType == 14"
          get-data-url="getSupplierTenderList"
          :field-defines="fieldDefines"
        ></tab-purchase-detail>
        <!-- 3. 相关文件 -->
        <tab-documents v-else-if="moduleType == 2"></tab-documents>
        <!-- 4. 说明 -->
        <tab-explain v-else-if="moduleType == 3"></tab-explain>
        <!-- 5. 项目计划 -->
        <!-- <tab-project-plan
          v-else-if="tabIndex == 4"
        ></tab-project-plan> -->
        <!-- 5. 报价 -->
        <tab-quoted-price
          :quoted-price-data="quotedPriceData"
          v-else-if="moduleType == -2"
        ></tab-quoted-price>
      </div>
    </div>
  </div>
</template>

<script>
// 默认看板小，中间内容大
export default {
  components: {
    //顶部详情
    topInfoView: () => import('./components/topInfo.vue'),
    //大厅
    tabHall: () =>
      import(
        /* webpackChunkName: "router/supply/quotationBidding/detail/tabs/tabHall" */ './pages/tabHall.vue'
      ),
    //相关文件
    tabDocuments: () =>
      import(
        /* webpackChunkName: "router/supply/quotationBidding/detail/tabs/documents" */ './pages/documents/index.vue'
      ),
    //说明
    tabExplain: () =>
      import(
        /* webpackChunkName: "router/supply/quotationBidding/detail/tabs/tabExplain" */ './pages/tabExplain.vue'
      ),
    //报价
    tabQuotedPrice: () =>
      import(
        /* webpackChunkName: "router/supply/quotationBidding/detail/tabs/tabQuotedPrice" */ './pages/tabQuotedPrice.vue'
      ),
    //采购明细
    tabPurchaseDetail: () => import('ROUTER_PURCHASE_RFX/detail/tabs/purchaseDetail/index.vue')
  },
  data() {
    return {
      isKanbanBigger: false, // 左边看板更大
      hallStatus: 1, // 大厅状态
      moduleType: -1, // 当前选中的tab
      tabSource: [],
      quotedPriceData: {},
      remark: '',
      fieldDefines: []
    }
  },
  mounted() {
    this.$bus.$on('changeTab', (e, item) => {
      this.moduleType = item.moduleType
      this.fieldDefines = item.fieldDefines
    })
    this.getData()
    this.getTabs()
  },
  computed: {
    tenderId() {
      return this.$route.query.id
    }
  },
  methods: {
    // 获取详情
    getData() {
      this.$API.quotationBidding.getOfferBidDetails(this.tenderId).then((r) => {
        this.quotedPriceData = r.data
        this.remark = r.data.desc
      })
    },
    // 获取tabs数据
    getTabs() {
      this.$API.quotationBidding.getTabs(this.tenderId).then((r) => {
        this.tabSource = [{ title: this.$t('大厅'), moduleType: 4 }]
        if (r.data && r.data.moduleItems) {
          r.data.moduleItems.forEach((e) => {
            // 处理采购明细的表头
            let _columnData = []
            if (e.moduleType == 14 && e.fieldDefines) {
              _columnData = [
                {
                  type: 'checkbox',
                  width: '50',
                  showInColumnChooser: false
                }
              ]
              e.fieldDefines.forEach((item) => {
                _columnData.push({
                  field: item.fieldCode,
                  headerText: item.fieldName,
                  queryType: 'string'
                })
              })
            }
            this.tabSource.push({
              title: e.moduleName,
              moduleType: e.moduleType,
              fieldDefines: e.fieldDefines ? _columnData : null
            })
          })
        }
        this.tabSource.push({ title: this.$t('报价'), moduleType: -2 })
        this.moduleType = this.tabSource[0].moduleType
        if (this.moduleType == 14) {
          this.fieldDefines = this.tabSource[0].fieldDefines
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// 主内容部分，按1440宽计算的
$mainMinWidth: 1224px; // 再小顶部的按钮那边就会换行了
$mainMinHeight: 668px;
.hall-detail {
  width: 100%;
  padding: 20px 0 0 0;
  display: flex;

  .progress-kanban {
    width: 96px;
    height: 100%;
    position: relative;
    transition: all 0.5s ease-in-out;
    flex-shrink: 0;
    background: #fff;
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px 0 0 0;

    &-bigger {
      width: 250px;
    }

    .shrink {
      cursor: pointer;
      width: 12px;
      height: 60px;
      background: rgba(243, 243, 243, 1);
      border-radius: 8px 0 0 8px;
      position: absolute;
      right: 0;
      top: calc((100% - 60px) / 2);
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .mt-icons {
        color: #6c7a8f;
        font-size: 12px;
        transform: scale(0.8);
      }
    }
  }

  .main-context {
    flex: 1;
    transition: all 0.5s ease-in-out;
    overflow-x: auto;
    overflow-y: hidden;
    display: flex;
    flex-direction: column;

    .top-info {
      min-width: $mainMinWidth;
      height: 120px;
      background: #f5f8fb;
      border: 1px solid #e8e8e8;
      border-left: 0;
      border-radius: 0 8px 0 0;
      flex-shrink: 0;
      display: flex;
    }

    .middle-wrap {
      width: 100%;
      min-width: $mainMinWidth;
      flex: 1;
      padding: 10px 0 10px 10px;
      overflow-y: auto;
      .tab-hall {
        height: 100%;
        min-height: $mainMinHeight;
      }
    }
  }
}
</style>
