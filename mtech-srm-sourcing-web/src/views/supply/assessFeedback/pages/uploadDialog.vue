<template>
  <mt-dialog
    ref="dialog"
    css-class="bule-bg"
    :width="900"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="uploader-box">
      <mt-form ref="formInfo" class="form-box" :model="formInfo" :rules="rules">
        <mt-row>
          <mt-col :span="24">
            <mt-form-item prop="file" class="form-item">
              <div class="cell-upload">
                <div class="to-upload">
                  <!-- multiple="multiple" -->
                  <input type="file" class="upload-input" @change="chooseFiles" />
                  <div class="upload-box" v-show="!uploadInfo.fileName">
                    <div class="plus-icon"></div>
                    <div class="right-state">
                      <div class="plus-txt">
                        {{ $t('请拖拽文件或点击上传') }}
                      </div>
                      <div class="warn-text">
                        {{ $t('注：文件最大不可超过50M') }}， <br />{{
                          $t(
                            '文件格式仅支持（.pdf .jpg.bmp .gif .ico .pcx .jpeg .tif .png .txt .xls .xlsx .doc.docx .zip .7z .rar）'
                          )
                        }}
                      </div>
                    </div>
                  </div>
                </div>

                <div class="has-file" v-if="!!uploadInfo.fileName">
                  <div class="left-info">
                    <div class="file-title">
                      <span class="text-ellipsis">{{ uploadInfo.fileName }}</span>
                      <span>{{ uploadInfo.fileSize }} kb</span>
                    </div>
                  </div>
                  <mt-icon
                    name="icon_Close_2"
                    class="close-icon"
                    @click.native="handleRemove"
                  ></mt-icon>
                </div>
              </div>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-form-item class="form-item" :label="$t('描述')" label-style="top" prop="remark">
          <mt-input
            v-model="formInfo.remark"
            :multiline="true"
            maxlength="200"
            :placeholder="$t('请输入')"
            float-label-type="Never"
            width="820"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
let fileData = null
export default {
  data() {
    return {
      // allowFileType: ["xls", "xlsx"],
      uploadUser: '',
      formInfo: {
        remark: ''
      },
      rules: {
        // remark: [
        //   { required: true, message: this.$t("请输入"), trigger: "blur" },
        // ],
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      uploadInfo: {} // 上传后信息
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    isEdit() {
      return this.modalData.isEdit
    },
    info() {
      return this.modalData.info
    },
    index() {
      return this.modalData.index
    }
  },
  mounted() {
    console.log('this.modalData', this.modalData)
    this.initData()
    this.show()
  },
  methods: {
    initData() {
      this.getUserInfoDetail()
      if (this.isEdit) {
        this.formInfo.remark = this.info.remark
        this.uploadInfo = { ...this.info }
      }
    },
    getUserInfoDetail() {
      this.$API.iamService.getUserDetail().then((res) => {
        this.uploadUser = res.data
      })
    },
    chooseFiles(data) {
      this.$store.commit('startLoading')
      let { files } = data.target
      let params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (files.length < 1) {
        this.$store.commit('endLoading')
        // 您未选择需要上传的文件
        return
      }
      // console.log("files", files);
      // let _tempInfo = files[0].name.split(".");
      // if (
      //   _tempInfo.length < 2 ||
      //   !this.allowFileType.includes(_tempInfo[_tempInfo.length - 1])
      // ) {
      //   this.$toast({
      //     content: this.$t("文件格式仅支持xls .xlsx"),
      //     type: "warning",
      //   });
      //   this.$store.commit("endLoading");
      //   return;
      // }
      let _data = new FormData(),
        isOutOfRange = false
      for (let i = 0; i < files.length; i++) {
        _data.append('UploadFiles', files[i])
        if (files[i].size > params.limit * 1024) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$store.commit('endLoading')
        this.$toast({
          content: params.msg
        })
        return
      }
      _data.append('useType', 1) // (1 admin 平台管理 2 tenant 租户id 3 user 用户id)
      fileData = _data
      this.uploadFile()
    },

    // 上传图片
    uploadFile() {
      this.$API.fileService
        .uploadPublicFile(fileData)
        .then((res) => {
          const { code, data } = res
          this.$store.commit('endLoading')
          if (code == 200) {
            this.uploadInfo = {
              ...data,
              fileId: data.id,
              uploadUserName: this.uploadUser.username,
              uploadUserId: this.uploadUser.uid
            }
            // this.$toast({ content: this.$t("操作成功"), type: "success" });
          } else {
            this.uploadInfo = {}
            this.$toast({
              content: data.msg,
              type: 'warning'
            })
          }
        })
        .catch((error) => {
          this.uploadInfo = {}
          this.$store.commit('endLoading')
          this.$toast({
            content: error.msg,
            type: 'warning'
          })
        })
    },

    // 移除文件
    handleRemove() {
      this.uploadInfo = {}
      fileData = null
    },

    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    confirm() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          if (!this.uploadInfo) {
            this.$toast({
              content: this.$t('请选择文件上传！'),
              type: 'warning'
            })
            return
          }
          this.$emit('confirm-function', {
            ...this.uploadInfo,
            fileUrl: this.uploadInfo.url,
            remark: this.formInfo.remark,
            uploadTime: this?.uploadInfo?.uploadTime
              ? this.uploadInfo.uploadTime
              : Number(new Date(this.uploadInfo.createTime))
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss">
.uploader-box {
  .mt-form-item__content {
    flex: 1;
  }
}
</style>
<style lang="scss" scoped>
.uploader-box {
  padding: 40px 20px 0;
}

.cell-upload {
  margin-top: 15px;
  padding: 10px 20px;
  background: rgba(251, 252, 253, 1);
  border: 1px dashed rgba(232, 232, 232, 1);
  position: relative;

  .upload-input {
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 2;
    top: 0;
    background: transparent;
    opacity: 0;
  }

  .upload-box {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;

    .plus-icon {
      width: 40px;
      height: 40px;
      position: relative;
      margin-right: 30px;

      &::before {
        content: ' ';
        display: inline-block;
        width: 40px;
        height: 2px;
        background: #98aac3;
        position: absolute;
        top: 50%;
        left: 0;
      }

      &::after {
        content: ' ';
        display: inline-block;
        width: 2px;
        height: 40px;
        background: #98aac3;
        position: absolute;
        top: 0;
        left: 50%;
      }
    }
    .right-state {
      .plus-txt {
        font-size: 20px;
        font-weight: normal;
        color: rgba(155, 170, 193, 1);
      }
      .warn-text {
        font-size: 12px;
        font-weight: normal;
        color: rgba(237, 86, 51, 1);
      }
    }
  }

  .has-file {
    width: 100%;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 99;

    .left-info {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .file-title {
        width: 80%;
        font-size: 16px;
        color: rgba(152, 170, 195, 1);

        .text-ellipsis {
          width: 100%;
          display: inline-block;
          vertical-align: middle;
        }
      }
      div:last-child {
        width: 80%;
        font-size: 12px;

        font-weight: normal;
        color: rgba(154, 154, 154, 1);
        transform: scale(0.9);
        transform-origin: left;
      }
    }

    .close-icon {
      cursor: pointer;
      color: #98aac3;
    }
  }
}
</style>
