<template>
  <div>
    <div
      slot="slot-filter"
      :class="['top-filter', !supplierFeedbackExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('供方反馈') }}</div>
      <div class="sort-box" @click="supplierFeedbackExpand = !supplierFeedbackExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="supplierFeedbackExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !supplierAttachmentExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('供方附件') }}</div>
      <div class="sort-box" @click="supplierAttachmentExpand = !supplierAttachmentExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="supplierAttachmentExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  props: {},
  data() {
    return {
      supplierFeedbackExpand: true,
      supplierAttachmentExpand: true
    }
  },
  created() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
.top-filter {
  background: #fff;
  padding: 20px 20px 20px 0;
  font-weight: 500;

  .accordion-main {
    margin-right: 20px;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 25px;
  }

  .left-status {
    margin: 0px 20px 20px 0px;
    clear: both;
  }

  .titles {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
  }
  &-small {
    height: 50px;
    overflow: hidden;
  }
}
.accordion-title {
  float: left;
  font-size: 14px;
  margin-left: 20px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  text-indent: 10px;
  border-left: 5px solid #00469c;
  margin-bottom: 20px;
  border-radius: 2px 0 0 2px;
}

.sort-box {
  position: relative;
  cursor: pointer;
  width: 5px;
  float: right;
  margin: 15px 15px 0px 0px;

  .mt-icons {
    font-size: 12px;
    transform: scale(0.5);
    color: rgba(0, 70, 156, 1);
    margin-top: -15px;
    position: absolute;
    top: 0;
    &:nth-child(2) {
      top: 6px;
    }
  }
}
</style>
