<template>
  <div>
    <div
      slot="slot-filter"
      :class="['top-filter', !supplierFeedbackExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('供方反馈') }}</div>
      <div class="sort-box" @click="supplierFeedbackExpand = !supplierFeedbackExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="supplierFeedbackExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="form-box">
        <mt-form
          ref="feedbackFormInfo"
          :model="feedbackFormInfo"
          :rules="rules"
          :auto-complete="false"
        >
          <mt-form-item
            class="form-item"
            :label="$t('是否申诉')"
            label-style="top"
            prop="appealFlag"
          >
            <mt-select
              v-model="feedbackFormInfo.appealFlag"
              :data-source="booleanList"
              :show-clear-button="true"
              :allow-filtering="true"
              :placeholder="$t('请选择')"
              @change="changeAppeal"
              width="300"
              :disabled="queryType == 11 || queryType == 12"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('申诉内容')"
            label-style="top"
            prop="appealContext"
            v-if="feedbackFormInfo.appealFlag"
          >
            <mt-select
              :disabled="queryType == 11 || queryType == 12"
              v-model="feedbackFormInfo.appealContext"
              :data-source="claimTypeList"
              :placeholder="$t('请选择')"
              width="300"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            class="form-item full-width"
            :label="$t('反馈意见')"
            label-style="top"
            prop="appealSuggestion"
          >
            <mt-input
              v-model="feedbackFormInfo.appealSuggestion"
              :multiline="true"
              :rows="2"
              maxlength="200"
              float-label-type="Never"
              :placeholder="$t('字数不超过200字')"
              :disabled="queryType == 11 || queryType == 12"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !supplierAttachmentExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('供方附件') }}</div>
      <div class="sort-box" @click="supplierAttachmentExpand = !supplierAttachmentExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="supplierAttachmentExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="table-box">
        <mt-template-page
          ref="supAttachmentRef"
          :template-config="supAttachmentConfig"
          @handleClickToolBar="handleClickSupAttachmentToolBar"
        ></mt-template-page>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !basicExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('基本信息') }}</div>
      <div class="sort-box" @click="basicExpand = !basicExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="basicExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="form-box">
        <mt-form ref="formInfo" :model="basicInfo" :rules="rules" :auto-complete="false">
          <mt-form-item
            class="form-item"
            :label="$t('反馈截止时间')"
            label-style="top"
            prop="feedbackEndTime"
          >
            <mt-date-time-picker
              :disabled="true"
              :width="300"
              v-model="basicInfo.feedbackEndTime"
              :placeholder="$t('选择日期和时间')"
            ></mt-date-time-picker>
          </mt-form-item>
          <mt-form-item class="form-item" :label="$t('币种')" label-style="top" prop="currencyName">
            <mt-input :disabled="true" v-model="basicInfo.currencyName" width="300"></mt-input>
          </mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('索赔总额')"
            label-style="top"
            prop="claimTotalAmount"
          >
            <mt-input :disabled="true" :width="300" v-model="basicInfo.claimTotalAmount"></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
      <div class="table-box">
        <mt-template-page
          ref="templateRef"
          :template-config="assessmentIndexConfig"
        ></mt-template-page>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !basicExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('原因说明') }}</div>
      <div class="sort-box" @click="basicExpand = !basicExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="basicExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="table-box">
        <mt-input
          ref="editorRef"
          v-model="basicInfo.reasonDesc"
          :multiline="true"
          :rows="2"
          :disabled="true"
        ></mt-input>
      </div>
    </div>
    <div
      slot="slot-filter"
      :class="['top-filter', !basicExpand && 'top-filter-small']"
      class="top-filter"
    >
      <div class="accordion-title">{{ $t('附件') }}</div>
      <div class="sort-box" @click="basicExpand = !basicExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="basicExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <div class="table-box">
        <mt-template-page
          ref="purAttachmentRef"
          :template-config="purchaseAttachmentConfig"
        ></mt-template-page>
      </div>
    </div>
  </div>
</template>
<script>
import { cloneDeep } from 'lodash'
import { assessmentIndexColumn, attachmentColumn, editSettings } from '../config/index'
export default {
  components: {},
  computed: {
    queryId() {
      return this.$route.query.id
    },
    queryType() {
      return this.$route.query.type
    }
  },
  props: {
    info: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  created() {
    if (this.queryType == 11 || this.queryType == 12) {
      delete this.supAttachmentConfig[0].toolbar
    }
  },
  mounted() {
    console.log('mounted=', this.info)
    this.basicInfo = {
      ...this.info,
      feedbackEndTime: new Date(Number(this.info.feedbackEndTime))
    }
    this.feedbackFormInfo = { ...this.info.claimAppeal }
    // this.info.standDetailList.forEach(
    //   (e) =>
    //     (e.happenTime = utils.formatTime(
    //       new Date(Number(e.happenTime)),
    //       "YYYY-mm-dd HH:MM"
    //     ))
    // );
    this.info.costCenterList.forEach((e) => (e.costCenterAddId = this.costCenterAddId++))
    this.assessmentIndexConfig[0].grid.dataSource = this.info.standDetailList
    this.purchaseAttachmentConfig[0].grid.dataSource = this.info.attachmentList
    // this.info.claimAppealAttachmentList.forEach(
    //   (e) =>
    //     (e.uploadTime = utils.formatTime(
    //       new Date(Number(e.uploadTime)),
    //       "YYYY-mm-dd HH:MM"
    //     ))
    // );
    this.supAttachmentConfig[0].grid.dataSource = this.info.claimAppealAttachmentList
  },
  data() {
    return {
      currencyList: [],
      claimTypeList: [
        { text: this.$t('申诉考核金额'), value: 0 },
        { text: this.$t('申诉考核指标'), value: 1 },
        { text: this.$t('非我公司责任'), value: 2 },
        { text: this.$t('其他'), value: 3 }
      ],
      templateText: '',
      isPerPublish: true,
      booleanList: [
        { text: this.$t('否'), value: false },
        { text: this.$t('是'), value: true }
      ],
      feedbackFormInfo: {
        appealFlag: false
      },
      basicInfo: {},
      rules: {
        appealContext: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      basicExpand: true,
      supplierAttachmentExpand: true,
      supplierFeedbackExpand: true,
      purchaseAttachmentConfig: [
        {
          grid: {
            allowEditing: true,
            editSettings: editSettings,
            allowPaging: false,
            lineIndex: 1,
            columnData: attachmentColumn,
            height: 200,
            dataSource: []
          }
        }
      ],
      supAttachmentConfig: [
        {
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [['Add', 'Edit', 'Delete'], []]
          },
          useToolTemplate: false,
          grid: {
            allowEditing: true,
            editSettings: editSettings,
            allowPaging: false,
            lineIndex: 1,
            columnData: attachmentColumn,
            height: 200,
            dataSource: []
          }
        }
      ],
      assessmentIndexConfig: [
        {
          grid: {
            allowPaging: false,
            lineIndex: 1,
            columnData: assessmentIndexColumn,
            height: 200,
            dataSource: []
          }
        }
      ]
    }
  },
  methods: {
    changeAppeal() {},
    handleClickSupAttachmentToolBar(e) {
      let sltList = e.grid.getSelectedRecords()
      let selectedRowIndexes = e.grid.getSelectedRowIndexes()
      if ((!sltList || sltList.length <= 0) && e.toolbar.id != 'Add') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        this.$dialog({
          modal: () => import('./uploadDialog.vue'),
          data: {
            fileData: [],
            isView: false, // 是否为预览
            required: false, // 是否必须
            title: this.$t('新增供方附件')
          },
          success: (res) => {
            this.handleUploadFiles(res)
          }
        })
      } else if (e.toolbar.id == 'Edit') {
        if (sltList.length > 1) {
          this.$toast({
            content: this.$t('不能同时编辑多行'),
            type: 'warning'
          })
          return
        }
        this.$dialog({
          modal: () => import('./uploadDialog.vue'),
          data: {
            title: this.$t('编辑供方附件'),
            isEdit: true,
            info: {
              ...sltList[0],
              fileName: sltList[0].attachmentName,
              fileSize: sltList[0].attachmentSize,
              url: sltList[0].attachmentUrl,
              createTime: sltList[0].uploadTime
            },
            index: selectedRowIndexes[0]
          },
          success: (data) => {
            let _tempData = cloneDeep(this.supAttachmentConfig[0].grid.dataSource)
            _tempData[selectedRowIndexes[0]] = data
            this.$set(this.supAttachmentConfig[0].grid, 'dataSource', _tempData)
            this.$refs.supAttachmentRef.refreshCurrentGridData()
          }
        })
      } else if (e.toolbar.id == 'Delete') {
        let _tempData = cloneDeep(this.supAttachmentConfig[0].grid.dataSource)
        let _fileIds = sltList.map((x) => x.id)
        let _newData = _tempData.filter((element) => !_fileIds.includes(element.id))
        this.$set(this.supAttachmentConfig[0].grid, 'dataSource', _newData)
      }
    },
    handleUploadFiles(data) {
      console.log('handleUploadFiles=', data)
      let _tempData = {
        ...data,
        attachmentId: data.id,
        attachmentName: data.fileName,
        attachmentSize: data.fileSize,
        attachmentUrl: data.url
      }
      this.supAttachmentConfig[0].grid.dataSource.push(_tempData)
    }
  }
}
</script>
<style lang="scss" scoped>
.top-filter {
  background: #fff;
  padding: 20px 20px 20px 0;
  font-weight: 500;

  .accordion-main {
    margin-right: 20px;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 25px;
  }

  .left-status {
    margin: 0px 20px 20px 0px;
    clear: both;
  }

  .titles {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
  }
  &-small {
    height: 50px;
    overflow: hidden;
  }
}
.accordion-title {
  float: left;
  font-size: 14px;
  margin-left: 20px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  text-indent: 10px;
  border-left: 5px solid #00469c;
  margin-bottom: 20px;
  border-radius: 2px 0 0 2px;
}

.sort-box {
  position: relative;
  cursor: pointer;
  width: 5px;
  float: right;
  margin: 15px 15px 0px 0px;

  .mt-icons {
    font-size: 12px;
    transform: scale(0.5);
    color: rgba(0, 70, 156, 1);
    margin-top: -15px;
    position: absolute;
    top: 0;
    &:nth-child(2) {
      top: 6px;
    }
  }
}
.form-box {
  width: 100%;
  display: flex;
  .mt-form {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: left;
    margin: 10px 0 10px 28px;
    .mt-form-item {
      margin-right: 20px;
    }
  }
}
.full-width {
  width: 100%;
}
.table-box {
  margin: 0 20px;
}
/deep/ .mt-rich-text-editor {
  margin: 30px 10px 0 10px;
  .e-toolbar-wrapper {
    height: 42px !important;
  }
  .e-toolbar-items {
    margin-left: -59px;
  }
  .e-rte-content {
    height: 300px !important;
  }
}
</style>
