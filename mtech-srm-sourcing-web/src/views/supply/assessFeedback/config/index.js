import { i18n } from '@/main.js'
import utils from '@/utils/utils'
export const editSettings = {
  allowAdding: true,
  allowEditing: true,
  allowDeleting: true,
  mode: 'Normal', // 默认normal模式
  allowEditOnDblClick: true,
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Top'
}
export const attachmentColumn = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'attachmentName',
    headerText: i18n.t('附件名称')
  },
  {
    field: 'attachmentSize',
    headerText: i18n.t('附件大小')
  },
  {
    field: 'uploadUserName',
    headerText: i18n.t('上传人')
  },
  {
    field: 'uploadTime',
    headerText: i18n.t('上传时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formatTime(e)
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              return e.substr(0, 10)
            } else {
              let val = parseInt(e)
              return utils.formatTime(new Date(val))
            }
          } else if (typeof e == 'number') {
            return utils.formatTime(new Date(e))
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
export const firstCols = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'claimCode',
    headerText: i18n.t('考核单编码'),
    cellTools: []
  },
  {
    field: 'claimTypeName',
    headerText: i18n.t('考核维度')
  },
  {
    field: 'companyName',
    headerText: i18n.t('所属公司')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        '-1': i18n.t('已取消'),
        0: i18n.t('新建'),
        1: i18n.t('已提交'),
        3: i18n.t('审批拒绝'),
        10: i18n.t('待反馈'),
        11: i18n.t('已反馈'),
        12: i18n.t('已确认'),
        13: i18n.t('申诉处理审批中'),
        14: i18n.t('申诉处理审批拒绝'),
        15: i18n.t('已改判'),
        16: i18n.t('不改判'),
        17: i18n.t('已付款')
      }
    }
  },
  {
    field: 'offlineEnsure',
    headerText: i18n.t('供应商是否签字确认'),
    valueConverter: {
      type: 'map',
      map: { false: i18n.t('否'), true: i18n.t('是') }
    }
  },
  {
    field: 'claimTotalAmount',
    headerText: i18n.t('索赔总额')
  },
  {
    field: 'claimMonth',
    headerText: i18n.t('考核月份')
  },
  {
    field: 'itemName',
    headerText: i18n.t('考核品类')
  },
  {
    field: 'feedbackEndTime',
    headerText: i18n.t('要求反馈日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formatTime(e)
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              return e.substr(0, 10)
            } else {
              let val = parseInt(e)
              return utils.formatTime(new Date(val))
            }
          } else if (typeof e == 'number') {
            return utils.formatTime(new Date(e))
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    field: 'realFeedbackTime',
    headerText: i18n.t('实际反馈日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formatTime(e)
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              return e.substr(0, 10)
            } else {
              let val = parseInt(e)
              return utils.formatTime(new Date(val))
            }
          } else if (typeof e == 'number') {
            return utils.formatTime(new Date(e))
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  }
]
export const appealCols = [
  {
    field: 'currencyName',
    headerText: i18n.t('是否申诉')
  },
  {
    field: 'currencyName',
    headerText: i18n.t('申诉结果')
  }
]
export const lastCols = [
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formatTime(e)
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              return e.substr(0, 10)
            } else {
              let val = parseInt(e)
              return utils.formatTime(new Date(val))
            }
          } else if (typeof e == 'number') {
            return utils.formatTime(new Date(e))
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    field: 'currencyName',
    headerText: i18n.t('备注')
  }
]
export const assessmentIndexColumn = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂')
  },
  {
    field: 'standCode',
    headerText: i18n.t('考核指标代码')
  },
  {
    field: 'standName',
    headerText: i18n.t('考核指标描述')
  },
  {
    field: 'standDesc',
    headerText: i18n.t('考核指标说明')
  },
  {
    field: 'happenTime',
    headerText: i18n.t('发生时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          if (e == 0) {
            return (e = '')
          } else if (typeof e == 'object') {
            return utils.formatTime(e)
          } else if (typeof e == 'string') {
            if (e.indexOf('T') != -1) {
              return e.substr(0, 10)
            } else {
              let val = parseInt(e)
              return utils.formatTime(new Date(val))
            }
          } else if (typeof e == 'number') {
            return utils.formatTime(new Date(e))
          } else {
            return e
          }
        } else {
          return e
        }
      }
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'unitName',
    headerText: i18n.t('单位')
  },
  {
    field: 'quantity',
    headerText: i18n.t('数量')
  },
  {
    field: 'unitPrice',
    headerText: i18n.t('单价')
  },
  {
    field: 'claimDesc',
    headerText: i18n.t('考核说明')
  },
  {
    field: 'taxInclusiveName',
    headerText: i18n.t('是否含税')
  },
  {
    field: 'taxTypeName',
    headerText: i18n.t('税率')
  },
  {
    field: 'taxAmount',
    headerText: i18n.t('税额')
  },
  {
    field: 'untaxedPrice',
    headerText: i18n.t('不含税金额')
  },
  {
    field: 'taxedPrice',
    headerText: i18n.t('含税金额')
  },
  {
    field: 'refRes',
    headerText: i18n.t('联带物品')
  },
  {
    field: 'refResUnitName',
    headerText: i18n.t('单位')
  },
  {
    field: 'refResQuantity',
    headerText: i18n.t('联带数量')
  },
  {
    field: 'refAmount',
    headerText: i18n.t('联带金额')
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
