<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import { firstCols, appealCols, lastCols } from './config/index'
export default {
  data() {
    return {
      pageConfig: [
        {
          title: this.$t('待反馈'),
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'feedback',
                  icon: 'icon_table_new',
                  title: this.$t('反馈考核单')
                },
                {
                  id: 'print',
                  icon: 'icon_table_new',
                  title: this.$t('打印')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          useToolTemplate: false,
          grid: {
            columnData: firstCols.concat(lastCols),
            asyncConfig: {
              url: '/analysis/tenant/supplierClaim/pageClaim',
              params: { type: 0 }
            }
          }
        },
        {
          title: this.$t('考核历史'),
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [[], ['Filter', 'Refresh', 'Setting']]
          },
          useToolTemplate: false,
          grid: {
            columnData: firstCols.concat(appealCols, lastCols),
            asyncConfig: {
              url: '/analysis/tenant/supplierClaim/pageClaim',
              params: { type: 1 }
            }
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(item) {
      let records = item.data ? [item.data] : item.grid.getSelectedRecords()
      if (records.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (item.toolbar.id == 'print') {
        this.$API.assessManage.printClaim().then(() => {})
      }
      if (item.toolbar.id == 'feedback') {
        if (records.length > 1) {
          this.$toast({ content: this.$t('只能编辑一行'), type: 'warning' })
          return
        }
        this.$router.push({
          name: 'purchase-assessmanage-assessFeedbackDetail',
          query: {
            type: records[0].status,
            id: records[0].id
          }
        })
      }
    },
    handleClickCellTool() {},
    /**
     * 点击标题跳转
     */
    handleClickCellTitle(e) {
      console.log(e)
      let { data } = e
      this.$router.push({
        name: 'purchase-assessmanage-assessFeedbackDetail',
        query: {
          type: data.status,
          id: data.id
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
