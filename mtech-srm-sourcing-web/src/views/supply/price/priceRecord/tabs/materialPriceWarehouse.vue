<template>
  <div class="full-height">
    <mt-local-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="priceValueType" :label="$t('价格类别')" label-style="top">
              <mt-select
                v-model="searchFormModel.priceValueType"
                :data-source="priceCategoryList"
                :show-clear-button="true"
                :placeholder="$t('请选择价格类别')"
              />
            </mt-form-item>
            <mt-form-item prop="orderNo" :label="$t('采购订单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.orderNo"
                :show-clear-button="true"
                :placeholder="$t('请输入采购订单号')"
              />
            </mt-form-item>
            <mt-form-item prop="materialCodeList" :label="$t('物料编码')" label-style="top">
              <custom-select
                v-model="searchFormModel.materialCodeList"
                :label-name="$t('物料编码')"
                :placeholder="$t('请输入物料编码')"
              />
            </mt-form-item>
            <mt-form-item prop="categoryCodeList" :label="$t('品类编码')" label-style="top">
              <custom-select
                v-model="searchFormModel.categoryCodeList"
                :label-name="$t('品类编码')"
                :placeholder="$t('请输入品类编码')"
              />
            </mt-form-item>
            <mt-form-item prop="companyCodeList" :label="$t('公司编码')" label-style="top">
              <custom-select
                v-model="searchFormModel.companyCodeList"
                :label-name="$t('公司编码')"
                :placeholder="$t('请输入公司编码')"
              />
            </mt-form-item>
            <mt-form-item prop="factoryCodeList" :label="$t('工厂编码')" label-style="top">
              <custom-select
                v-model="searchFormModel.factoryCodeList"
                :label-name="$t('工厂编码')"
                :placeholder="$t('请输入工厂编码')"
              />
            </mt-form-item>
            <mt-form-item prop="purOrgCodeList" :label="$t('采购组织编码')" label-style="top">
              <custom-select
                v-model="searchFormModel.purOrgCodeList"
                :label-name="$t('采购组织编码')"
                :placeholder="$t('请输入采购组织编码')"
              />
            </mt-form-item>
            <mt-form-item prop="sourceCodeList" :label="$t('来源单号')" label-style="top">
              <custom-select
                v-model="searchFormModel.sourceCodeList"
                :label-name="$t('来源单号')"
                :placeholder="$t('请输入来源单号')"
              />
            </mt-form-item>
            <mt-form-item
              prop="purchaseInfoRecordNo"
              :label="$t('外发信息记录编号')"
              label-style="top"
            >
              <mt-input
                v-model="searchFormModel.purchaseInfoRecordNo"
                :show-clear-button="true"
                :placeholder="$t('请输入外发信息记录编号')"
              />
            </mt-form-item>
            <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
              <mt-date-range-picker
                v-model="tempForm.createTime"
                :open-on-focus="true"
                :allow-edit="false"
                time-stamp
                format="yyyy-MM-dd"
                :placeholder="$t('请选择创建时间')"
                @change="(e) => handleDateRangeChange('createTime', e)"
              />
            </mt-form-item>
            <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.itemName"
                :show-clear-button="true"
                :placeholder="$t('请输入物料名称')"
              />
            </mt-form-item>

            <mt-form-item prop="categoryName" :label="$t('品类名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.categoryName"
                :show-clear-button="true"
                :placeholder="$t('请输入品类名称')"
              />
            </mt-form-item>
            <mt-form-item prop="companyName" :label="$t('公司名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.companyName"
                :show-clear-button="true"
                :placeholder="$t('请输入公司名称')"
              />
            </mt-form-item>
            <mt-form-item prop="siteName" :label="$t('工厂名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.siteName"
                :show-clear-button="true"
                :placeholder="$t('请输入工厂名称')"
              />
            </mt-form-item>
            <mt-form-item prop="syncStatus" :label="$t('同步状态')" label-style="top">
              <mt-select
                v-model="searchFormModel.syncStatus"
                :data-source="syncStatusList"
                :show-clear-button="true"
                :placeholder="$t('请选择同步状态')"
              />
            </mt-form-item>
            <mt-form-item prop="syncMsg" :label="$t('同步信息')" label-style="top">
              <mt-input
                v-model="searchFormModel.syncMsg"
                :show-clear-button="true"
                :placeholder="$t('请输入同步信息')"
              />
            </mt-form-item>
            <mt-form-item prop="syncFinishTime" :label="$t('同步SAP完成时间')" label-style="top">
              <mt-date-range-picker
                v-model="tempForm.syncFinishTime"
                :open-on-focus="true"
                :allow-edit="false"
                time-stamp
                format="yyyy-MM-dd"
                :placeholder="$t('请选择同步SAP完成时间')"
                @change="(e) => handleDateRangeChange('syncFinishTime', e)"
              />
            </mt-form-item>
            <mt-form-item prop="deliveryPlace" :label="$t('直送地')" label-style="top">
              <mt-input
                v-model="searchFormModel.deliveryPlace"
                :show-clear-button="true"
                :placeholder="$t('请输入直送地')"
              />
            </mt-form-item>
            <mt-form-item prop="validStartTime" :label="$t('生效日期')" label-style="top">
              <mt-date-picker
                v-model="tempForm.validStartTime"
                :open-on-focus="true"
                :allow-edit="false"
                time-stamp
                format="yyyy-MM-dd"
                :placeholder="$t('请选择生效日期')"
                @change="(e) => handleDateChange('validStartTime', e)"
              />
            </mt-form-item>
            <mt-form-item prop="validEndTime" :label="$t('失效日期')" label-style="top">
              <mt-date-picker
                v-model="tempForm.validEndTime"
                :open-on-focus="true"
                :allow-edit="false"
                time-stamp
                format="yyyy-MM-dd"
                :placeholder="$t('请选择失效日期')"
                @change="(e) => handleDateChange('validEndTime', e)"
              />
            </mt-form-item>
            <mt-form-item prop="priceRecordCode" :label="$t('价格编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.priceRecordCode"
                :show-clear-button="true"
                :placeholder="$t('请输入价格编码')"
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-select
                v-model="searchFormModel.status"
                :data-source="statusList"
                :show-clear-button="true"
                :placeholder="$t('请选择状态')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-local-template-page>
  </div>
</template>

<script>
// 引入本地的emplate-page组件
import MtLocalTemplatePage from '@/components/template-page'
import CustomSelect from '@/components/customSelect'
import { materialPriceWarehousePageConfig } from './config/index'
import { download, getHeadersFileName } from '@/utils/utils'
import mixin from './config/mixin'

export default {
  components: {
    MtLocalTemplatePage,
    CustomSelect
  },
  mixins: [mixin],
  data() {
    return {
      pageConfig: materialPriceWarehousePageConfig
    }
  },
  mounted() {},
  methods: {
    // 点击按钮工具栏
    handleClickToolBar(e) {
      if (e.toolbar.id === 'download') {
        this.handleExport()
      }
    },
    // 导出
    handleExport() {
      const visibleColumns =
        JSON.parse(sessionStorage.getItem(this.pageConfig[0]?.gridId))?.visibleCols || []
      const includeColumnFiledNames = []
      visibleColumns?.forEach((column) => {
        includeColumnFiledNames.push(column.field)
      })
      const params = {
        page: { current: 1, size: 10000 },
        includeColumnFiledNames,
        priceTypeList: [0, 1, 2, 3, 4],
        ...this.searchFormModel
      }
      this.$API.supplierPrice.exportPriceRecord(params).then((res) => {
        this.$toast({ type: 'success', content: this.$t('正在导出，请稍后！') })
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  .hidden {
    display: none;
  }
  /deep/.e-headercelldiv {
    padding: 0 0.6em 0 0.6em !important;
  }
}
</style>
