import { syncStatusList, priceCategoryList, priceTypeList, statusList } from './index'

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      tempForm: {},
      syncStatusList,
      priceCategoryList,
      priceTypeList,
      statusList
    }
  },
  mounted() {},
  methods: {
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    // 日期格式化
    handleDateChange(key, val) {
      this.searchFormModel[key] = val?.valueOf()
    },
    // 日期范围格式化
    handleDateRangeChange(key, e) {
      const { startDate, endDate } = e
      this.searchFormModel[key + 'Start'] = startDate ? startDate.valueOf() : null
      this.searchFormModel[key + 'End'] = endDate ? endDate.valueOf() + 86400000 : null
    }
  }
}
