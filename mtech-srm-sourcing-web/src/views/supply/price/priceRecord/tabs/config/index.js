import { i18n } from '@/main.js'
import historyes from '@/views/purchase/price/list/components/history/index.vue'
import priceHistory from '@/views/purchase/price/list/components/priceHistory/index.vue'

// 同步状态
export const syncStatusList = [
  { value: 0, text: i18n.t('无需同步'), cssClass: 'title-#9baac1' },
  { value: 1, text: i18n.t('未同步'), cssClass: 'title-#6386c1' },
  { value: 2, text: i18n.t('同步中'), cssClass: 'title-#6386c1' },
  { value: 3, text: i18n.t('同步成功'), cssClass: 'title-#6386c1' },
  { value: 4, text: i18n.t('同步失败'), cssClass: 'title-#9baac1' }
]
// 报价属性
export const quoteAttributeList = [
  { value: 'mailing_price', text: i18n.t('寄售价'), cssClass: '' },
  { value: 'standard_price', text: i18n.t('标准价'), cssClass: '' },
  { value: 'outsource', text: i18n.t('委外价'), cssClass: '' }
]
// 价格生效方式
export const quoteModeList = [
  { value: 'in_warehouse', text: i18n.t('按照入库'), cssClass: '' },
  { value: 'out_warehouse', text: i18n.t('按出库'), cssClass: '' },
  { value: 'order_date', text: i18n.t('按订单日期'), cssClass: '' }
]
// 价格类别
export const priceCategoryList = [
  { value: 1, text: i18n.t('基价'), cssClass: '' },
  { value: 2, text: i18n.t('SRM价'), cssClass: '' },
  { value: 3, text: i18n.t('暂估价格'), cssClass: '' },
  { value: 4, text: i18n.t('执行价'), cssClass: '' }
]
// 价格类型
export const priceTypeList = [
  { value: 0, text: i18n.t('到物料'), cssClass: '' },
  { value: 1, text: i18n.t('到SKU'), cssClass: '' },
  { value: 2, text: i18n.t('到品类'), cssClass: '' },
  { value: 3, text: i18n.t('到供应商'), cssClass: '' },
  { value: 4, text: i18n.t('业务单号'), cssClass: '' },
  { value: 5, text: i18n.t('成本因子'), cssClass: '' },
  { value: 6, text: i18n.t('物流'), cssClass: '' },
  { value: 8, text: i18n.t('模具'), cssClass: '' },
  { value: 10, text: i18n.t('结构阶梯'), cssClass: '' },
  { value: 11, text: i18n.t('美工阶梯'), cssClass: '' }
]
// 状态
export const statusList = [
  { value: 0, text: i18n.t('草稿'), cssClass: '' },
  { value: 1, text: i18n.t('启用'), cssClass: '' },
  { value: 2, text: i18n.t('禁用'), cssClass: '' },
  { value: 3, text: i18n.t('未合格'), cssClass: '' }
]

// 物料价格库
const materialPriceWarehouseColumnData = [
  {
    field: 'id',
    width: 0,
    isPrimaryKey: true
  },
  {
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: syncStatusList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'syncMsg',
    headerText: i18n.t('同步信息')
  },
  {
    field: 'orderNo',
    headerText: i18n.t('采购订单号')
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('含税单价')
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('未税单价')
  },
  {
    field: 'priceUnit',
    headerText: i18n.t('价格单位')
  },
  {
    field: 'priceValueType',
    headerText: i18n.t('价格类别'),
    valueConverter: {
      type: 'map',
      map: priceCategoryList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'priceType',
    headerText: i18n.t('价格类型'),
    valueConverter: {
      type: 'map',
      map: priceTypeList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司'),
    width: '200',
    valueAccessor: (field, data) => {
      if (Array.isArray(data.companyList) && data.companyList.length) {
        return data.companyList[0]['companyCode'] + '-' + data.companyList[0]['companyName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: '200',
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.siteList[0]['siteCode'] + '-' + data.siteList[0]['siteName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织'),
    ignore: true,
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.siteList[0]['purOrgCode'] + '-' + data.siteList[0]['purOrgName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'quoteAttribute',
    headerText: i18n.t('报价属性'),
    valueConverter: {
      type: 'map',
      map: quoteAttributeList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'quoteMode',
    headerText: i18n.t('价格生效方式'),
    valueConverter: {
      type: 'map',
      map: quoteModeList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'deliveryPlace',
    headerText: i18n.t('直送地')
  },
  {
    field: 'unitName',
    headerText: i18n.t('单位')
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种'),
    valueAccessor: (field, row) => {
      return row.currencyCode ? row.currencyCode + '-' + row.currencyName : ''
    }
  },
  {
    field: 'taxRate',
    headerText: i18n.t('税率')
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('生效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('失效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'priceRecordCode',
    headerText: i18n.t('价格编码')
  },
  {
    field: 'sourceCode',
    headerText: i18n.t('来源单号')
  },
  {
    field: 'purchaseInfoRecordNo',
    headerText: i18n.t('外发信息记录编号')
  },
  {
    field: 'minPackageQuantity',
    headerText: i18n.t('最小包装数量')
  },
  {
    field: 'minPurchaseQuantity',
    headerText: i18n.t('最小采购量')
  },
  {
    field: 'leadTime',
    headerText: 'L/T'
  },
  {
    field: 'unconditionalLeadTime',
    headerText: i18n.t('无条件L/T')
  },
  {
    field: 'paymentCondition',
    headerText: i18n.t('付款条件')
  },
  {
    field: 'historyRecord',
    headerText: i18n.t('历史记录'),
    template: function () {
      return {
        template: historyes
      }
    }
  },
  {
    field: 'priceRecordId',
    headerText: i18n.t('历史价格趋势'),
    template: function () {
      return {
        template: priceHistory
      }
    }
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: statusList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'purchaseAgentName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    field: 'sourceCreateTime',
    headerText: i18n.t('单据创建时间')
  },
  {
    field: 'oaApprovalFinishTime',
    headerText: i18n.t('OA审批完成时间')
  },
  {
    field: 'syncFinishTime',
    headerText: i18n.t('同步SAP完成时间')
  },
  {
    field: 'priceValidStartTime',
    headerText: i18n.t('初始有效开始时间')
  },
  {
    field: 'priceValidEndTime',
    headerText: i18n.t('初始有效结束时间')
  }
]
export const materialPriceWarehousePageConfig = [
  {
    title: i18n.t('物料价格库'),
    gridId: 'a4cf97d0-e9fe-4365-a49f-cbfb732a626e',
    activatedRefresh: false,
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    useToolTemplate: false,
    toolbar: {
      useBaseConfig: false,
      tools: [
        [{ id: 'download', icon: 'icon_solid_export', title: i18n.t('导出') }],
        ['Refresh', 'Setting']
      ]
    },
    grid: {
      virtualPageSize: 30,
      enableVirtualization: true,
      pageSettings: {
        currentPage: 1,
        pageSize: 20,
        pageSizes: [20, 50, 100, 200, 1000],
        totalRecordsCount: 0
      },
      allowFiltering: true,
      showSelected: false,
      columnData: materialPriceWarehouseColumnData,
      asyncConfig: {
        url: '/price/tenant/priceRecord/new/supplier/pageQuery'
      }
    }
  }
]
