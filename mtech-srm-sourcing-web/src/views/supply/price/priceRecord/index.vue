<template>
  <div>
    <mt-tabs
      ref="mtTabsRef"
      :tab-id="$utils.randomString()"
      :e-tab="false"
      :data-source="tabList"
      :halt-select="false"
      @handleSelectTab="handleTabChange"
    />
    <keep-alive>
      <component ref="mainContent" :is="activeComponent" />
    </keep-alive>
  </div>
</template>

<script>
export default {
  data() {
    return {
      activeTabIndex: 0
    }
  },
  computed: {
    tabList() {
      const tabs = [
        {
          title: this.$t('物料价格库')
        }
      ]
      return tabs
    },
    activeComponent() {
      let comp = ''
      switch (this.activeTabIndex) {
        case 0:
          // 物料价格库
          comp = () => import('./tabs/materialPriceWarehouse.vue')
          break
        default:
          break
      }
      return comp
    }
  },
  methods: {
    // 切换tab页签
    handleTabChange(index) {
      this.$refs.mtTabsRef.activeTab = index
      this.activeTabIndex = index
    }
  }
}
</script>
<style lang="scss" scoped></style>
