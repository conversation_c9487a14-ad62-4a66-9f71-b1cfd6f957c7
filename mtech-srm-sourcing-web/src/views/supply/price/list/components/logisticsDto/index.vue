<template>
  <mt-dialog
    ref="dialog"
    class="aaa"
    css-class="price-details-dialog-container"
    :buttons="buttons"
    :enable-resize="false"
    :header="modalData.title"
    @beforeClose="cancel"
  >
    <mt-template-page :hidden-tabs="true" :template-config="templateConfig" />
  </mt-dialog>
</template>
<script>
import { columnDataDialogThc, columnDataDialogQua } from '../../config/index'
export default {
  name: 'PriceDetailsDialog',
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('确定') }
        }
      ],
      templateConfig: [
        {
          grid: {
            allowFiltering: true,
            allowPaging: false,
            columnData: [],
            dataSource: []
          }
        }
      ]
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    if (this.modalData.data.type == 1) {
      this.templateConfig[0].grid.columnData = columnDataDialogThc
    } else {
      this.templateConfig[0].grid.columnData = columnDataDialogQua
    }
    this.templateConfig[0].grid.dataSource = this.modalData.data.data
  },
  methods: {
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.price-details-dialog-container {
  &.e-dialog .e-dlg-content {
    padding: 40px;
  }
}
</style>
