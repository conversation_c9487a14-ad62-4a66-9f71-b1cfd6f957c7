<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :close="cancel"
    :buttons="button"
    :header="header"
  >
    <div class="logic">
      <mt-chart
        v-if="seriesDataSource.length > 0"
        :series-data-source="seriesDataSource"
        :title="title"
        :primary-x-axis="primaryXAxis"
        :primary-y-axis="primaryYAxis"
        :zoom-settings="zoom"
        align="center"
        :tooltip="tooltip"
        :chart-area="chartArea"
        width="800px"
        :legend-settings="legendSettings"
      >
      </mt-chart>
    </div>
  </mt-dialog>
</template>
<script>
import Vue from 'vue'
import MtChart from '@mtech-ui/chart'
Vue.use(MtChart)
import utils from '@/utils/utils'
export default {
  data() {
    return {
      button: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('确定') }
        }
      ],
      title: this.$t('历史价格趋势图'),
      zoom: {
        //缩放
        enableMouseWheelZooming: true,
        enableSelectionZooming: true,
        enablePan: true,
        enablePinchZooming: true,
        enableScrollbar: true
      },
      primaryXAxis: {
        valueType: 'Category',
        interval: 1,
        majorGridLines: { width: 1 },
        labelIntersectAction: 'Rotate90'
      },
      primaryYAxis: {
        labelFormat: '{value}'
      },
      tooltip: {
        enable: true
      },
      chartArea: {
        border: {
          width: 0
        }
      },
      legendSettings: { visible: true },

      seriesDataSource: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.getRondomColor()
  },
  methods: {
    cancel() {
      this.$emit('confirm-function')
    },
    getParamByPriceType() {
      const {
        priceType,
        bizCode,
        categoryCode,
        categoryId,
        itemCode,
        itemId,
        logisticsEndPlace,
        logisticsStartPlace,
        logisticsType,
        marketFactorCode,
        marketFactorId,
        skuCode,
        skuId,
        supplierCode,
        supplierId
      } = this.modalData.data
      return [
        { itemCode, itemId, priceType },
        { skuCode, skuId, priceType },
        { categoryCode, categoryId, priceType },
        { supplierCode, supplierId, priceType },
        { bizCode },
        { marketFactorCode, marketFactorId, priceType },
        { logisticsEndPlace, logisticsStartPlace, logisticsType, priceType }
      ]
    },
    async init() {
      const param = this.getParamByPriceType()[this.modalData?.data?.priceType]
      const res = await this.$API.priceService.getLineRecords(param)
      const _result = res.data.map((i) => {
        return {
          type: 'Spline',
          xName: 'date',
          yName: 'taxedUnitPrice',
          name: i.supplierName,
          fill: this.getRondomColor(),
          width: 2,
          dataSource: i.trendList.map((v) => {
            return {
              date: utils.formatTime(new Date(v.date), 'YYYY-mm-dd'),
              taxedUnitPrice: v.taxedUnitPrice
            }
          }),
          marker: {
            visible: true,
            height: 10,
            width: 10
          }
        }
      })
      this.seriesDataSource = _result
    },
    getRondomColor() {
      return `#${Math.floor(Math.random() * 0xffffff).toString(16)}`
    }
  },
  created() {
    this.init()
  }
}
</script>
<style lang="scss" scoped>
.logic {
  width: 100%;
}
</style>
