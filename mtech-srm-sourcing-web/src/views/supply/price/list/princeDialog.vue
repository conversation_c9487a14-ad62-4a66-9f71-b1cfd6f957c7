<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <i class="mt-icons icon_Hiddenpassword" />
    <div class="dialog-panel stages-panel">
      <div class="stages-list">
        <mt-template-page ref="quantityStageRef" :template-config="amountStageConfig" />
      </div>
    </div>
  </mt-dialog>
</template>
<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('确定') }
        }
      ],
      amountStageConfig: [
        {
          grid: {
            allowFiltering: true,
            allowPaging: false,
            columnData: [
              {
                field: 'stageFrom',
                headerText: this.$t('阶梯开始')
              },
              {
                field: 'stageTo',
                headerText: this.$t('阶梯结束')
              },
              {
                field: 'untaxedUnitPrice',
                headerText: this.$t('未税单价')
              },
              {
                field: 'taxRate',
                headerText: this.$t('税率')
              },
              {
                field: '',
                headerText: this.$t('是否含运费')
              },
              {
                field: '',
                headerText: this.$t('运费')
              }
            ],
            dataSource: [{}]
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        width: '500px',
        buttons: this.buttons,
        cssClass: ('small-dialog ' + this.modalData.cssClass).trim()
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.amountStageConfig[0].grid.dataSource = this.modalData.stageList
  },
  methods: {
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
