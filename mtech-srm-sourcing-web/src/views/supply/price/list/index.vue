<template>
  <div class="full-height">
    <input type="file" accept=".xlsx" hidden ref="excel-uploader" />
    <mt-template-page :template-config="pageConfigOuter">
      <mt-template-page
        ref="templateRefWL"
        slot="slot-0"
        :hidden-tabs="true"
        :padding-top="true"
        :template-config="pageConfig"
        @handleClickToolBar="(e) => handleClickToolBar(e, 'WL')"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
      />
      <mt-template-page
        ref="templateRef"
        slot="slot-1"
        :padding-top="true"
        :template-config="logisticsConfig"
      >
        <mt-template-page
          ref="templateRefHY"
          slot="slot-0"
          :hidden-tabs="true"
          :padding-top="true"
          :template-config="pageConfigLoFnBase(1, $t('海运'))"
          :handle-click-cell-title="handleClickCellTitleLogis"
          @handleClickToolBar="(e) => handleClickToolBar(e, 'HY', 1)"
        />
        <mt-template-page
          ref="templateRefKY"
          slot="slot-1"
          :hidden-tabs="true"
          :padding-top="true"
          :template-config="pageConfigLoFnBase(2, $t('空运'))"
          :handle-click-cell-title="handleClickCellTitleLogis"
          @handleClickToolBar="(e) => handleClickToolBar(e, 'KY', 2)"
        />
        <mt-template-page
          ref="templateRefTL"
          slot="slot-2"
          :hidden-tabs="true"
          :padding-top="true"
          :template-config="pageConfigLoFnBase(3, $t('铁路'))"
          :handle-click-cell-title="handleClickCellTitleLogis"
          @handleClickToolBar="(e) => handleClickToolBar(e, 'TL', 3)"
        />
        <mt-template-page
          ref="templateRefLY"
          slot="slot-3"
          :hidden-tabs="true"
          :padding-top="true"
          :template-config="pageConfigLoFn(4, $t('陆运'))"
          :handle-click-cell-title="handleClickCellTitleLogis"
          @handleClickToolBar="(e) => handleClickToolBar(e, 'LY', 4)"
        />
        <mt-template-page
          ref="templateRefTC"
          slot="slot-4"
          :hidden-tabs="true"
          :padding-top="true"
          :template-config="pageConfigLoFn(5, $t('拖车'))"
          :handle-click-cell-title="handleClickCellTitleLogis"
          @handleClickToolBar="(e) => handleClickToolBar(e, 'TC', 5)"
        />
      </mt-template-page>
    </mt-template-page>
  </div>
</template>

<script>
import {
  toolbar,
  // toolbarLogic,
  columnData,
  // columnDataLogic,
  pageConfigOuter,
  logisticsConfig,
  pageConfigLoFn
} from './config'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  data() {
    return {
      pageConfigOuter,
      pageConfig: [
        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          toolbar,
          gridId: this.$permission.gridId['supply']['price']['category'],
          grid: {
            allowFiltering: true,
            columnData,
            asyncConfig: {
              url: this.$API.supplierPrice.getSupplierRecords
            }
          }
        }
      ],
      pageConfigLoFn,
      logisticsConfig,
      buttons: [
        {
          click: this.btnConfirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  mounted() {
    this.$refs['excel-uploader'].addEventListener('change', (event) => {
      let file = event.target.files[0]
      if (file) {
        const formData = new FormData()
        formData.append('file', file)
        this.$API.priceService.importExcel(formData).then(() => {
          this.$toast({ type: 'success', content: this.$t('导入成功') })
          this.$refs.templateRef.refreshCurrentGridData()
        })
      }
    })
  },
  methods: {
    //表格按钮-点击事件
    handleClickToolBar(e, type, index) {
      console.log('1111-----', e)
      if (e.toolbar.id === 'download') {
        this.handleExport(type, index)
      }
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      if (e.tool.id == 'start') {
        //启用操作
        this.handleUpdateConfigStatus([e.data.id], 1)
      } else if (e.tool.id == 'stop') {
        //停用操作
        this.handleUpdateConfigStatus([e.data.id], 2)
      } else if (e.tool.id == 'check') {
        this.handleCheckPrice(e.data.stageList, e.data.taxRate)
      }
    },
    handleUpdateConfigStatus(ids, status) {
      //enableStatus状态 0 未启用 1 启用 2 禁用
      let _statusMap = [this.$t('草稿'), this.$t('启用'), this.$t('禁用')]
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: `${this.$t('确认更新状态为')}${_statusMap[status]}？`
        },
        success: () => {}
      })
    },
    //单元格标题点击操作
    handleClickCellTitle(ejsEventObject) {
      console.log('use-handleClickCellTitle', ejsEventObject)
      if (ejsEventObject.field === 'priceRecordCode') {
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "router/purchase/price/list/components/addPrice" */ './components/details/index.vue'
            ),
          data: {
            title: this.$t('价格条款明细'),
            data: ejsEventObject.data
          }
        })
      }
    },
    handleAddPrice() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/price/list/components/addPrice" */ './components/addPrice/index.vue'
          ),
        data: {
          title: this.$t('新增价格记录')
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // handleAddCbPrice() {
    //   this.$dialog({
    //     modal: () => import("./components/addCbPrice/index"),
    //     data: {
    //       title: this.$t("新增成本因子"),
    //     },
    //     success: () => {
    //       this.$refs.templateRef1.refreshCurrentGridData();
    //     },
    //   });
    // },
    handleCheckPrice(stageList, taxRate) {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/price/list/princeDialog" */ './princeDialog.vue'
          ),
        data: {
          title: this.$t('阶梯价格'),
          stageList: stageList.map((item) => {
            item.taxRate = taxRate
            return item
          })
        },
        success: () => {
          // this.$refs.templateRef.refreshCurrentGridData();
        }
      })
    },
    pageConfigLoFnBase(i, str) {
      return pageConfigLoFn(i, str)
    },
    btnConfirm() {
      this.$refs.dialogPrice.ejsRef.hide()
    },
    // 物料价格库-导出
    handleExport(type, index) {
      const typeMap = {
        WL: this.$t('物料价格库'),
        HY: this.$t('海运'),
        KY: this.$t('空运'),
        TL: this.$t('铁路'),
        LY: this.$t('陆运'),
        TC: this.$t('拖车')
      }
      const pageConfig = type === 'WL' ? this.pageConfig : pageConfigLoFn(index, typeMap[type])

      // 获取动态列信息
      const visibleColumns =
        JSON.parse(sessionStorage.getItem(pageConfig[0]?.gridId))?.visibleCols ||
        pageConfig[0].grid.columnData
      const includeColumnFiledNames = []
      visibleColumns?.forEach((column) => {
        includeColumnFiledNames.push(column.field)
      })

      // 查询参数
      const asyncParms = pageConfig[0].grid.asyncConfig.params || {}

      // 获取查询条件
      // 先要点击“查询”按钮才能取得queryBuilderRules
      const queryBuilderRules =
        this.$refs['templateRef' + type].getCurrentUsefulRef().pluginRef.queryBuilderRules || {}

      // 默认查询条件
      let defaultRules = []
      switch (type) {
        case 'WL':
          defaultRules = [
            {
              label: '',
              field: 'priceType',
              type: 'string',
              operator: 'in',
              value: [0, 1, 2, 3, 4]
            }
          ]
          break
        default:
          break
      }
      const params = {
        page: { current: 1, size: 10000 },
        includeColumnFiledNames,
        defaultRules,
        ...asyncParms,
        ...queryBuilderRules
      }
      this.$API.supplierPrice.exportPriceRecord(params).then((res) => {
        this.$toast({ type: 'success', content: this.$t('正在导出，请稍后！') })
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
