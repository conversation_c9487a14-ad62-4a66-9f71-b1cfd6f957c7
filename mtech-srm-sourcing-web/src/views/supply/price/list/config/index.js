import { i18n, permission } from '@/main.js'
import historyes from '../components/history/index.vue'
import priceHistory from '../components/priceHistory/index.vue'
import APIS from '@/apis/modules/supply/price/index'
import Vue from 'vue'
import specifiCation from '../components/logisticsDto/specifiCation'
function formatDateTime(inputTime) {
  var date = new Date(inputTime)
  var y = date.getFullYear()
  var m = date.getMonth() + 1
  m = m < 10 ? '0' + m : m
  var d = date.getDate()
  d = d < 10 ? '0' + d : d
  return y + '-' + m + '-' + d
}
export const toolbar = [
  // { id: "Add", icon: "icon_solid_Createorder", title: i18n.t("新增") },
  // { id: "Edit", icon: "icon_solid_edit", title: i18n.t("修改") },
  // { id: "Delete", icon: "icon_solid_Delete", title: i18n.t("删除") },
  // { id: "ImportEXCEL", icon: "icon_solid_upload", title: i18n.t("导入Excel") },
  { id: 'download', icon: 'icon_solid_Download', title: i18n.t('导出') }
]
export const toolbarCost = [
  // { id: "Add", icon: "icon_solid_Createorder", title: i18n.t("新增" )},
]
export const toolbarLogic = []

export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  // {
  //   field: "priceRecordCode",
  //   headerText: i18n.t("历史记录"),
  //   template: function () {
  //     return {
  //       template: historyes,
  //     };
  //   },
  // },
  // {
  //   field: "priceRecordId",
  //   headerText: i18n.t("历史价格趋势"),
  //   template: function () {
  //     return {
  //       template: priceHistory,
  //     };
  //   },
  // },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('草稿'), 1: i18n.t('启用'), 2: i18n.t('禁用') }
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    width: '200'
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    width: '200'
  },
  // {
  //   field: 'dieType',
  //   headerText: i18n.t('模具类型'),
  //   valueConverter: {
  //     type: 'map',
  //     map: {
  //       1: i18n.t('基础模具'),
  //       2: i18n.t('复制模具'),
  //       3: i18n.t('基础模改模'),
  //       4: i18n.t('复制模改模')
  //     }
  //   },
  //   width: '200'
  // },
  // {
  //   field: 'dieCode',
  //   headerText: i18n.t('模具编码'),
  //   width: '200'
  // },
  {
    field: 'parentItemCode',
    headerText: i18n.t('父级物料编码'),
    width: '200'
  },
  {
    field: 'parentItemName',
    headerText: i18n.t('父级物料名称'),
    width: '200'
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'companyList',
    headerText: i18n.t('公司名称'),
    width: '200',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['companyName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂/地点'),
    width: '200',
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.siteList[0]['siteName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织'),
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.siteList[0]['purOrgName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'quoteAttribute',
    headerText: i18n.t('报价属性'),
    valueConverter: {
      type: 'map',
      map: {
        mailing_price: i18n.t('寄售价'),
        standard_price: i18n.t('标准价')
      }
    }
  },
  {
    field: 'quoteMode',
    headerText: i18n.t('价格生效方式'),
    valueConverter: {
      type: 'map',
      map: {
        in_warehouse: i18n.t('按照入库'),
        out_warehouse: i18n.t('按出库'),
        order_date: i18n.t('按订单日期')
      }
    }
  },
  {
    field: 'deliveryPlace',
    headerText: i18n.t('直送地')
  },
  {
    field: 'unitName',
    headerText: i18n.t('单位')
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('含税单价'),
    searchOptions: {
      serializeValue: (e) => {
        //自定义搜索值，规则
        return +e
      },
      operator: 'equal',
      type: 'number'
    }
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('未税单价'),
    searchOptions: {
      serializeValue: (e) => {
        //自定义搜索值，规则
        return +e
      },
      operator: 'equal',
      type: 'number'
    }
  },
  {
    field: 'planQuantity',
    headerText: i18n.t('规划量')
  },
  {
    field: 'shareQuantity',
    headerText: i18n.t('实际分摊量')
  },
  {
    field: 'planSharePriceTaxed',
    headerText: i18n.t('规划分摊价（含税）')
  },
  {
    field: 'planSharePriceUntaxed',
    headerText: i18n.t(' 规划分摊价（未税）')
  },
  {
    field: 'realSharePriceTaxed',
    headerText: i18n.t('实际分摊价（含税）')
  },
  {
    field: 'realSharePriceUntaxed',
    headerText: i18n.t('实际分摊价（未税）')
  },
  {
    field: 'sharePriceTaxed',
    headerText: i18n.t('分摊后单价（含税）')
  },
  {
    field: 'sharePriceUntaxed',
    headerText: i18n.t('分摊后单价（未税）')
  },
  {
    field: 'priceRecordCode',
    headerText: i18n.t('价格编码'),
    width: 200,
    cssClass: 'field-content'
  },
  {
    field: 'priceValueType',
    headerText: i18n.t('价格类别'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('基价'),
        2: i18n.t('SRM价'),
        3: i18n.t('暂估价格'),
        4: i18n.t('执行价')
      }
    }
  },
  {
    field: 'priceRecordId',
    headerText: i18n.t('价格单位')
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种')
  },
  {
    field: 'sourceId',
    headerText: i18n.t('来源单号')
  },

  {
    field: 'validStartTime',
    headerText: i18n.t('生效日期'),
    type: 'date',
    format: 'yyyy-MM-dd',
    searchOptions: {
      serializeValue: (e) => {
        //自定义搜索值，规则
        return new Date(e).valueOf()
      }
    }
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('失效日期'),
    type: 'date',
    format: 'yyyy-MM-dd',
    searchOptions: {
      serializeValue: (e) => {
        //自定义搜索值，规则
        return new Date(e).valueOf()
      }
    }
  },
  {
    field: 'dieT1',
    headerText: i18n.t('T1时间'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (data) {
          return formatDateTime(parseInt(data))
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'minPackageQuantity',
    headerText: i18n.t('最小包装数量')
  },
  {
    field: 'minPurchaseQuantity',
    headerText: i18n.t('最小采购量')
  },

  {
    field: 'leadTime',
    headerText: 'L/T'
  },
  {
    field: 'unconditionalLeadTime',
    headerText: i18n.t('无条件L/T')
  },
  {
    field: 'stageType',
    headerText: i18n.t('阶梯类型'),
    valueConverter: {
      type: 'map',
      map: {
        '-1': i18n.t('无阶梯'),
        0: i18n.t('数量累计'),
        1: i18n.t('时间'),
        2: i18n.t('金额')
      }
    }
  },
  // {
  //   field: 'stageList',
  //   headerText: i18n.t('阶梯价格'),
  //   width: 500,
  //   template: function () {
  //     return {
  //       template: stageComponent
  //     }
  //   }
  // },
  {
    field: 'taxRate',
    headerText: i18n.t('税率')
  },
  {
    field: 'paymentCondition',
    headerText: i18n.t('付款条件')
  },
  {
    field: 'purchaseAgentName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期')
  }
]

export const columnDataCb = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'priceRecordCode',
    headerText: i18n.t('历史记录'),
    template: function () {
      return {
        template: historyes
      }
    }
  },
  {
    field: 'priceRecordId',
    headerText: i18n.t('历史价格趋势'),
    template: function () {
      return {
        template: priceHistory
      }
    }
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('草稿'), 1: i18n.t('启用'), 2: i18n.t('禁用') }
    }
  },
  {
    field: 'marketFactorCode',
    headerText: i18n.t('成本因子编码')
  },
  {
    field: 'marketFactorName',
    headerText: i18n.t('成本因子名称')
  },
  {
    field: 'marketFactorBrand',
    headerText: i18n.t('品牌')
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('单价(含税)')
  },
  {
    field: 'taxRate',
    headerText: i18n.t('税率')
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'companyList',
    headerText: i18n.t('公司名称'),
    width: '200',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['companyName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂/地点'),
    width: '200',
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.siteList[0]['siteName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织'),
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.siteList[0]['purOrgName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'sourceId',
    headerText: i18n.t('来源单号')
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('生效日期'),
    type: 'date',
    format: 'yyyy-MM-dd',
    searchOptions: {
      serializeValue: (e) => {
        //自定义搜索值，规则
        return new Date(e).valueOf()
      }
    }
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('失效日期'),
    type: 'date',
    format: 'yyyy-MM-dd',
    searchOptions: {
      serializeValue: (e) => {
        //自定义搜索值，规则
        return new Date(e).valueOf()
      }
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期')
  }
]

export const columnDataLogic = [
  {
    field: 'companyList',
    headerText: i18n.t('公司名称'),
    width: '200',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['companyName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'purchaseAgentName',
    headerText: i18n.t('采购员')
  },
  {
    field: 'logisticsType',
    headerText: i18n.t('运输方式')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  }
]

export const pageConfigOuter = [
  {
    title: i18n.t('物料价格库')
  },
  {
    title: i18n.t('物流价格记录')
  }
]

export const logisticsConfig = [
  {
    title: i18n.t('海运'),
    id: 1
  },
  {
    title: i18n.t('空运'),
    id: 2
  },
  {
    title: i18n.t('铁路'),
    id: 3
  },
  {
    title: i18n.t('陆运'),
    id: 4
  },
  {
    title: i18n.t('拖车'),
    id: 5
  }
]
const columnDataLo = {}
// 托运
columnDataLo['5'] = [
  {
    type: 'checkbox',
    width: '50'
  },
  {
    field: 'companyList',
    headerText: i18n.t('公司名称'),
    width: '200',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['companyName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'purchaseAgentName',
    headerText: i18n.t('采购员')
  },
  {
    field: 'logisticsDto.transportType',
    headerText: i18n.t('运输方式'),
    width: '200'
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    width: '200'
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    width: '200'
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    width: '200'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: '200'
  },
  {
    field: 'siteList',
    headerText: i18n.t('采购组织'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['purOrgName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'logisticsDto.startPlace',
    headerText: i18n.t('发运地'),
    width: '200'
  },
  {
    field: 'logisticsDto.endPlace',
    headerText: i18n.t('目的地'),
    width: '200'
  },
  {
    field: 'logisticsDto.trailerItem',
    headerText: i18n.t('项目'),
    width: '300'
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('运费'),
    width: '200'
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('报价有效期从'),
    width: '200'
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('报价有效期至'),
    width: '200'
  },
  {
    field: 'remark',
    headerText: i18n.t('费用备注'),
    width: '200',
    template: () => {
      return {
        template: Vue.component('priceMark', {
          template: `
          <div style="padding:5px">
              <p v-if="data.doc">doc费用：{{data.doc}}</p>
              <p v-if="data.seal">seal费用：{{data.seal}}</p>
              <p v-if="data.eir">eir费用：{{data.eir}}</p>
              <p>其他费用：{{data.otherFee}}</p>
          </div>`
        })
      }
    }
  }
]

// 陆运
columnDataLo['4'] = [
  {
    type: 'checkbox',
    width: '50'
  },
  {
    field: 'companyList',
    headerText: i18n.t('公司名称'),
    width: '200',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['companyName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'purchaseAgentName',
    headerText: i18n.t('采购员')
  },
  {
    field: 'logisticsDto.transportType',
    headerText: i18n.t('运输方式'),
    width: '200'
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    width: '200'
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    width: '200'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: '200'
  },
  {
    field: 'siteList',
    headerText: i18n.t('采购组织'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['purOrgName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'logisticsDto.startPlace',
    headerText: i18n.t('始发地'),
    width: '200'
  },
  {
    field: 'logisticsDto.endPlace',
    headerText: i18n.t('目的地'),
    width: '200'
  },
  {
    field: 'logisticsDto.carType',
    headerText: i18n.t('载重车型'),
    width: '200'
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种'),
    width: '200'
  },
  {
    field: 'logisticsDto.landTransportMode',
    headerText: i18n.t('陆运模式'),
    width: '200'
  },
  {
    field: 'logisticsDto.transportEffective',
    headerText: i18n.t('运输时效'),
    width: '200'
  },
  {
    field: 'logisticsDto.loadingUnloadingMode',
    headerText: i18n.t('装卸方式'),
    width: '200'
  },
  {
    field: 'logisticsDto.completeVehiclePrice',
    headerText: i18n.t('整车总价（假设1天压夜）'),
    width: '200'
  },
  {
    field: 'remark',
    headerText: i18n.t('费用备注'),
    width: '200',
    template: () => {
      return {
        template: Vue.component('priceMark', {
          template: `
          <div  style="padding:5px">
              <p v-if="data.doc">doc费用：{{data.doc}}</p>
              <p v-if="data.seal">seal费用：{{data.seal}}</p>
              <p v-if="data.eir">eir费用：{{data.eir}}</p>
              <p>其他费用：{{data.otherFee}}</p>
          </div>`
        })
      }
    }
  }
]
// 铁路
columnDataLo['3'] = [
  {
    type: 'checkbox',
    width: '50'
  },
  {
    field: 'companyList',
    headerText: i18n.t('公司名称'),
    width: '200',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['companyName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'purchaseAgentName',
    headerText: i18n.t('采购员')
  },
  {
    field: 'logisticsDto.transportType',
    headerText: i18n.t('运输方式')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    width: '200'
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    width: '200'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: '200'
  },
  {
    field: 'siteList',
    headerText: i18n.t('采购组织'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['purOrgName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'logisticsDto.startPlace',
    headerText: i18n.t('始发地')
  },
  {
    field: 'logisticsDto.endPlace',
    headerText: i18n.t('目的地')
  },
  {
    field: 'logisticsDto.serviceMode',
    headerText: i18n.t('服务模式'),
    width: '200'
  },
  {
    field: 'logisticsDto.loadingAddress',
    headerText: i18n.t('装货地'),
    width: '200'
  },
  {
    field: 'logisticsDto.expectLoadingTime',
    headerText: i18n.t('期望装货时间'),
    width: '200'
  },
  {
    field: 'logisticsDto.logisticsTerm',
    headerText: i18n.t('贸易条款'),
    width: '200'
  },
  {
    field: 'logisticsDto.lastDeliveryTime',
    headerText: i18n.t('最晚发运时间'),
    width: '200'
  },
  {
    field: 'logisticsDto.expectArriveTime',
    headerText: i18n.t('期望到达时间'),
    width: '200'
  },
  {
    field: 'logisticsDto.specification',
    headerText: i18n.t('集中箱费用'),
    template: () => {
      return specifiCation
    }
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种')
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('报价有效期从'),
    width: '200'
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('报价有效期至'),
    width: '200'
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
// 空运
columnDataLo['2'] = [
  {
    type: 'checkbox',
    width: '50'
  },
  {
    field: 'companyList',
    headerText: i18n.t('公司名称'),
    width: '200',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['companyName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'purchaseAgentName',
    headerText: i18n.t('采购员')
  },
  {
    field: 'logisticsDto.transportType',
    headerText: i18n.t('运输方式')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    width: '200'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: '200'
  },
  {
    field: 'siteList',
    headerText: i18n.t('采购组织'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['purOrgName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'logisticsDto.startPort',
    headerText: i18n.t('起运港'),
    width: '200'
  },
  {
    field: 'logisticsDto.endPort',
    headerText: i18n.t('目的港'),
    width: '200'
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品名'),
    width: '200'
  },
  {
    field: 'logisticsDto.goodsFinishTime',
    headerText: i18n.t('货好时间')
  },
  {
    field: 'logisticsDto.logisticsTerm',
    headerText: i18n.t('贸易条款'),
    width: '200'
  },
  {
    field: 'logisticsDto.declareMode',
    headerText: i18n.t('报关方式'),
    width: '200'
  },
  {
    field: 'logisticsDto.arriveAddress',
    headerText: i18n.t('送货地址'),
    width: '200'
  },
  {
    field: 'logisticsDto.specSize',
    headerText: i18n.t('货品规格（尺寸）'),
    width: '200'
  },
  {
    field: 'logisticsDto.specGrossWeight',
    headerText: i18n.t('货品规格（毛重（KG））'),
    width: '300'
  },
  {
    field: 'logisticsDto.specVolume',
    headerText: i18n.t('货品规格（体积（CBM）'),
    width: '300'
  },
  {
    field: 'logisticsDto.specVolumeWeight',
    headerText: i18n.t('货品规格（体积重（KG））'),
    width: '300'
  },
  {
    field: 'logisticsDto.startAirport',
    headerText: i18n.t('起运机场'),
    width: '200'
  },
  {
    field: 'logisticsDto.eta',
    headerText: 'ETA',
    width: '200'
  },
  {
    field: 'logisticsDto.etd',
    headerText: 'ETD',
    width: '200'
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('总计费用'),
    width: '200'
  },
  {
    field: 'remark',
    headerText: i18n.t('费用备注'),
    width: '200',
    template: () => {
      return {
        template: Vue.component('priceMark', {
          template: `
          <div style="padding:5px">
              <p v-if="data.doc">doc费用：{{data.doc}}</p>
              <p v-if="data.seal">seal费用：{{data.seal}}</p>
              <p v-if="data.eir">eir费用：{{data.eir}}</p>
              <p>其他费用：{{data.otherFee}}</p>
          </div>`
        })
      }
    }
  }
]
// 海运
columnDataLo['1'] = [
  {
    type: 'checkbox',
    width: '50'
  },
  {
    field: 'companyList',
    headerText: i18n.t('公司名称'),
    width: '200',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['companyName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'purchaseAgentName',
    headerText: i18n.t('采购员')
  },
  {
    field: 'logisticsDto.transportType',
    headerText: i18n.t('运输方式')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: '200'
  },
  {
    field: 'siteList',
    headerText: i18n.t('采购组织'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['purOrgName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'logisticsDto.startPort',
    headerText: i18n.t('起运港')
  },
  {
    field: 'logisticsDto.endPort',
    headerText: i18n.t('目的港')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品名')
  },
  {
    field: 'logisticsDto.goodsFinishTime',
    headerText: i18n.t('货好时间')
  },
  {
    field: 'logisticsDto.logisticsTerm',
    headerText: i18n.t('贸易条款')
  },
  {
    field: 'logisticsDto.specification',
    headerText: i18n.t('集中箱费用'),
    template: () => {
      return {
        template: specifiCation
      }
    }
  },
  {
    field: 'logisticsDto.eta',
    headerText: i18n.t('船期（ETA）'),
    width: '200'
  },

  {
    field: 'logisticsDto.etd',
    headerText: i18n.t('船期（ETD）'),
    width: '200'
  },

  {
    field: 'logisticsDto.seaTransportFeeTotal',
    headerText: 'USD',
    width: '200'
  },

  {
    field: 'taxedUnitPrice',
    headerText: 'RMB'
  },

  {
    field: 'remark',
    headerText: i18n.t('费用备注'),
    width: '200',
    template: () => {
      return {
        template: Vue.component('priceMark', {
          template: `
          <div style="padding:5px">
              <p v-if="data.logisticsDto.doc">DOC费用：{{data.logisticsDto.doc}}</p>
              <p v-if="data.logisticsDto.seal">SEAL费用：{{data.logisticsDto.seal}}</p>
              <p v-if="data.logisticsDto.eir">EIR费用：{{data.logisticsDto.eir}}</p>
              <p>其他费用：{{data.otherFee}}</p>
          </div>`
        })
      }
    }
  }
]

export const columnDataDialogThc = [
  {
    field: 'specification',
    headerText: i18n.t('规格')
  },
  {
    field: 'requiredQuantity',
    headerText: i18n.t('需求数量')
  },
  {
    field: 'price',
    headerText: i18n.t('海运费')
  },
  {
    field: 'thc',
    headerText: 'THC'
  }
]

export const columnDataDialogQua = [
  {
    field: 'specification',
    headerText: i18n.t('规格')
  },
  {
    field: 'requiredQuantity',
    headerText: i18n.t('需求数量')
  },
  {
    field: 'containerQuantity',
    headerText: i18n.t('可提供柜量')
  },
  {
    field: 'price',
    headerText: i18n.t('报价单价')
  }
]
const gridIdMap = {}
gridIdMap['1'] = permission.gridId['supply']['price']['logistics_sea'] //海运
gridIdMap['2'] = permission.gridId['supply']['price']['logistics_air'] //空运
gridIdMap['3'] = permission.gridId['supply']['price']['logistics_railway'] //铁路
gridIdMap['4'] = permission.gridId['supply']['price']['logistics_land'] //陆运
gridIdMap['5'] = permission.gridId['supply']['price']['logistics_trailer'] //拖车
export const pageConfigLoFn = (i, str) => {
  console.log(columnDataLo[i])
  return [
    {
      useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
      toolbar: {
        useBaseConfig: false,
        tools: [[], ['Refresh']]
      },
      gridId: gridIdMap[i],
      grid: {
        allowFiltering: true,
        columnData: columnDataLo[i],
        asyncConfig: {
          url: APIS.APIS.getSupplierlogis,
          params: {
            logisticsType: str
          }
        }
      }
    }
  ]
}
