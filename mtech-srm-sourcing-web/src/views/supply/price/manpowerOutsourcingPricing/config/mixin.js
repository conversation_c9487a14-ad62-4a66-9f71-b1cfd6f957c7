export default {
  data() {
    return {
      companyList: [],
      purchaseOrgList: [],
      siteList: [],
      expandList: [],
      purchaserList: []
    }
  },
  mounted() {
    this.getCompanyList()
    this.type !== 'add' && this.getPurchaseOrgList()
    this.type === 'add' && this.getPurchaserList({ text: '' })
  },
  methods: {
    // 获取公司下拉列表
    async getCompanyList() {
      const res = await this.$API.masterData.permissionCompanyList()
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.companyList = res.data || []
      }
    },
    // 获取采购组织下拉列表
    async getPurchaseOrgList() {
      const res = await this.$API.masterData.purchaseOraginaze({
        organizationTypeCode: 'BUORG002ADM'
      })
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.organizationCode + '-' + item.organizationName
        })
        this.purchaseOrgList = res.data || []
      }
    },
    // 根据公司id获取采购组织下拉列表
    async getPurchaseOrgListByCompanyId(companyId) {
      if (!companyId) {
        this.purchaseOrgList = []
        return
      }
      const res = await this.$API.masterData.permissionOrgList({
        orgId: companyId
      })
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.organizationCode + '-' + item.organizationName
        })
        this.purchaseOrgList = res.data
      }
    },
    // 根据公司i采购组织id获取工厂下拉列表
    async getSiteListById(companyId, purOrgId) {
      if (!companyId || !purOrgId) {
        this.siteList = []
        return
      }
      const res = await this.$API.masterData.permissionSiteList({
        companyId,
        buOrgId: purOrgId,
        orgLevelTypeCode: 'ORG06'
      })
      if (res.code === 200) {
        res.data?.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.siteList = res.data || []
      }
    },
    // 获取扩展下拉列表
    async getExpandList(siteCode, purOrgCode) {
      if (!siteCode) {
        this.expandList = []
        return
      }
      const res = await this.$API.rfxDetail.purAllOrgWithSite({ fuzzyParam: siteCode, purOrgCode })
      if (res.code === 200) {
        const dataSource = []
        res.data.forEach((v) => {
          v.siteOrgs?.forEach((x) => {
            const {
              companyId,
              companyCode,
              companyName,
              id,
              businessOrganizationCode,
              businessOrganizationName
            } = v
            dataSource.push({
              text:
                companyCode +
                '-' +
                businessOrganizationCode +
                '-' +
                businessOrganizationName +
                '+' +
                x.orgCode +
                '-' +
                x.orgName,
              value: companyCode + '+' + businessOrganizationCode + '+' + x.orgCode,
              data: {
                companyId,
                companyCode,
                companyName,
                purOrgId: id,
                purOrgCode: businessOrganizationCode,
                purOrgName: businessOrganizationName,
                siteCode: x.orgCode,
                siteId: x.id,
                siteName: x.orgName
              }
            })
          })
        })
        this.expandList = dataSource
      }
    },
    // 获取采购员下拉列表
    async getPurchaserList(e) {
      const { text: fuzzyName } = e
      const res = await this.$API.masterData.getCurrentTenantEmployees({ fuzzyName })
      if (res.code === 200) {
        res.data?.forEach((item) => {
          item.text = `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`
        })
        this.purchaserList = res.data || []
        if (fuzzyName == '' && this.purchaserList.length) {
          this.formData.purId = this.purchaserList[0].employeeId
        }
      }
    }
  }
}
