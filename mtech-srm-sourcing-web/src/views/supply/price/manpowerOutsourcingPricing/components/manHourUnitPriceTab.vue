<template>
  <sc-table
    ref="sctableRef"
    grid-id="8912f326-d89e-469d-b057-b594fe8f717e"
    :is-show-refresh-bth="true"
    :columns="columns"
    :table-data="tableData"
    :loading="loading"
    @refresh="getTableData"
  />
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'

export default {
  name: 'MaterialDetailTab',
  components: {
    ScTable
  },
  mixins: [mixin],
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      type: 'unitPrice'
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    async getTableData() {
      this.loading = true
      const res = await this.$API.manpowerOutsourcing
        .queryManHourUnitPriceList({ hroPointId: this.$route.query.id })
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data || []
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 0px 8px;
  background: #fff;
}
</style>
