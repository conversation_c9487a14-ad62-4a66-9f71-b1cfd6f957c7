<template>
  <sc-table
    ref="sctableRef"
    grid-id="225dfcce-8b40-4b40-adf7-c5250e37e0cc"
    :is-show-refresh-bth="true"
    :columns="columns"
    :table-data="tableData"
    :loading="loading"
    @refresh="getTableData"
  />
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'

export default {
  name: 'MaterialDetailTab',
  components: {
    ScTable
  },
  mixins: [mixin],
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      type: 'clearModel'
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    async getTableData() {
      this.loading = true
      const res = await this.$API.manpowerOutsourcing
        .queryPricingClearModelList({ hroPointId: this.$route.query.id })
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data || []
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 0px 8px;
  background: #fff;
}
</style>
