import { i18n } from '@/main.js'
export default {
  data() {
    return {
      tableData: [],
      loading: false,
      quoteAttributeList: [
        { value: 'mailing_price', text: i18n.t('寄售价') },
        { value: 'standard_price', text: i18n.t('标准价') },
        { value: 'outsource', text: i18n.t('委外价') }
      ],
      priceTypeList: [],
      priceUnitList: [
        { value: 0.0001, text: i18n.t('万元') },
        { value: 1, text: i18n.t('元') }
      ]
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    toolbar() {
      switch (this.type) {
        case 'unitPrice':
          return [
            {
              code: 'add',
              name: this.$t('新增'),
              status: 'info',
              isHidden: ![0, 1, 5].includes(this.dataInfo.status)
            },
            {
              code: 'delete',
              name: this.$t('删除'),
              status: 'info',
              isHidden: ![0, 1, 5].includes(this.dataInfo.status)
            }
          ]
        case 'clearModel':
          return [
            {
              code: 'add',
              name: this.$t('新增'),
              status: 'info',
              isHidden: ![0, 1, 5].includes(this.dataInfo.status)
            },
            {
              code: 'save',
              name: this.$t('保存'),
              status: 'info',
              isHidden: ![0, 1, 5].includes(this.dataInfo.status)
            },
            {
              code: 'delete',
              name: this.$t('删除'),
              status: 'info',
              isHidden: ![0, 1, 5].includes(this.dataInfo.status)
            }
          ]
        default:
          return []
      }
    },
    columns() {
      switch (this.type) {
        case 'unitPrice':
          return [
            {
              field: 'itemCode',
              title: this.$t('物料编码'),
              minWidth: 150
            },
            {
              field: 'itemName',
              title: this.$t('物料名称')
            },
            {
              field: 'supplierCode',
              title: this.$t('供应商编码')
            },
            {
              field: 'supplierName',
              title: this.$t('供应商名称')
            },
            {
              field: 'siteCode',
              title: this.$t('工厂编码')
            },
            {
              field: 'siteName',
              title: this.$t('工厂名称')
            },
            {
              field: 'taxedUnitPrice',
              title: this.$t('单价（含税）')
            },
            {
              field: 'untaxedUnitPrice',
              title: this.$t('单价（未税）')
            },
            {
              field: 'unitCode',
              title: this.$t('基本单位'),
              slots: {
                default: ({ row }) => {
                  return [<span>{(row.unitCode || '') + '-' + (row.unitName || '')}</span>]
                }
              }
            },
            {
              field: 'priceUnit',
              title: this.$t('价格单位'),
              slots: {
                default: ({ row }) => {
                  const selectItem = this.priceUnitList.find((item) => item.value == row.priceUnit)
                  return [<span>{selectItem?.text || ''}</span>]
                }
              }
            },
            {
              field: 'sourceCode',
              title: this.$t('来源单号')
            },
            {
              field: 'validStartTime',
              title: this.$t('生效日期'),
              minWidth: 140,
              slots: {
                default: ({ row }) => {
                  const validStartTime = row.validStartTime?.split(' ')[0]
                  return [<span>{validStartTime}</span>]
                }
              }
            },
            {
              field: 'validEndTime',
              title: this.$t('失效日期'),
              minWidth: 140,
              slots: {
                default: ({ row }) => {
                  const validEndTime = row.validEndTime?.split(' ')[0]
                  return [<span>{validEndTime}</span>]
                }
              }
            }
          ]
        case 'clearModel':
          return [
            {
              field: 'calcFormula',
              title: this.$t('计算公式')
            },
            {
              field: 'taxedUnitPrice',
              title: this.$t('单价（含税）')
            },
            {
              field: 'untaxedUnitPrice',
              title: this.$t('单价（未税）')
            },
            {
              field: 'supplierCode',
              title: this.$t('供应商编码')
            },
            {
              field: 'supplierName',
              title: this.$t('供应商名称')
            },
            {
              field: 'siteCode',
              title: this.$t('工厂编码')
            },
            {
              field: 'siteName',
              title: this.$t('工厂名称')
            },
            {
              field: 'itemCode',
              title: this.$t('物料编码'),
              minWidth: 150
            },
            {
              field: 'itemName',
              title: this.$t('物料名称')
            },
            {
              field: 'historyPrice',
              title: this.$t('历史价格')
            },
            {
              field: 'priceUnit',
              title: this.$t('价格单位'),
              slots: {
                default: ({ row }) => {
                  const selectItem = this.priceUnitList.find((item) => item.value == row.priceUnit)
                  return [<span>{selectItem?.text || ''}</span>]
                }
              }
            },
            {
              field: 'quoteAttribute',
              title: this.$t('报价属性'),
              slots: {
                default: ({ row }) => {
                  const selectItem = this.quoteAttributeList.find(
                    (item) => item.value === row.quoteAttribute
                  )
                  return [<span>{selectItem?.text || ''}</span>]
                }
              }
            },
            {
              field: 'currencyCode',
              title: this.$t('币种'),
              slots: {
                default: ({ row }) => {
                  return [<span>{(row.currencyCode || '') + '-' + (row.currencyName || '')}</span>]
                }
              }
            },
            {
              field: 'validStartTime',
              title: this.$t('生效日期'),
              minWidth: 140,
              slots: {
                default: ({ row }) => {
                  const validStartTime = row.validStartTime?.split(' ')[0]
                  return [<span>{validStartTime}</span>]
                }
              }
            },
            {
              field: 'validEndTime',
              title: this.$t('失效日期'),
              minWidth: 140,
              slots: {
                default: ({ row }) => {
                  const validEndTime = row.validEndTime?.split(' ')[0]
                  return [<span>{validEndTime}</span>]
                }
              }
            },
            {
              field: 'taxCode',
              title: this.$t('税率'),
              slots: {
                default: ({ row }) => {
                  return [<span>{(row.taxCode || '') + '-' + (row.taxName || '')}</span>]
                }
              }
            },
            {
              field: 'taxRate',
              title: this.$t('税率值')
            },
            {
              field: 'remark',
              title: this.$t('备注')
            },

            {
              field: 'moduleHourOutput',
              title: this.$t('模组时产')
            },
            {
              field: 'cbuHourOutput',
              title: this.$t('整机时产')
            },
            {
              field: 'perCapitaHourOutput',
              title: this.$t('人均时产')
            },
            {
              field: 'domainRentalFee',
              title: this.$t('场地租赁费')
            },
            {
              field: 'equipmentRentalFee',
              title: this.$t('设备租赁费')
            },
            {
              field: 'moduleDomainRentalFee',
              title: this.$t('模组场地租赁费')
            },
            {
              field: 'moduleEquipmentRentalFee',
              title: this.$t('模组设备租赁费')
            },
            {
              field: 'cbuDomainRentalFee',
              title: this.$t('整机场地租赁费')
            },
            {
              field: 'cbuEquipmentRentalFee',
              title: this.$t('整机设备租赁费')
            },
            {
              field: 'priceType',
              title: this.$t('价格类型')
            }
          ]
        default:
          return []
      }
    }
  },
  mounted() {
    this.type === 'clearModel' && this.getPriceTypelist()
  },
  methods: {
    async getPriceTypelist() {
      const res = await this.$API.masterData.dictionaryGetList({ dictCode: 'PRICETYPE' })
      if (res.code === 200) {
        this.priceTypeList = res.data || []
      }
    }
  }
}
