import { i18n, permission } from '@/main.js'

function formatDateTime(inputTime) {
  var date = new Date(inputTime)
  var y = date.getFullYear()
  var m = date.getMonth() + 1
  m = m < 10 ? '0' + m : m
  var d = date.getDate()
  d = d < 10 ? '0' + d : d
  var h = date.getHours()
  h = h < 10 ? '0' + h : h
  var minute = date.getMinutes()
  var second = date.getSeconds()
  minute = minute < 10 ? '0' + minute : minute
  second = second < 10 ? '0' + second : second
  return y + '-' + m + '-' + d + ' ' + h + ':' + minute + ':' + second
}

// 待报名列表
const todoColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'pointNo',
    width: 200,
    headerText: i18n.t('定价单号'),
    cssClass: 'field-content',
    searchOptions: { renameField: 'h.pointNo' }
  },
  {
    field: 'title',
    width: 200,
    headerText: i18n.t('调价单标题'),
    searchOptions: { renameField: 'h.title' }
  },
  {
    field: 'decidePriceType',
    headerText: i18n.t('定价类型'),
    searchOptions: { renameField: 'h.decidePriceType' },
    valueConverter: {
      type: 'map',
      map: {
        6: i18n.t('白电直接定价')
      }
    }
  },
  {
    field: 'status',
    width: 200,
    headerText: i18n.t('供方确认状态'),
    searchOptions: { renameField: 'h.status' },
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('待确认'),
        1: i18n.t('已驳回'),
        2: i18n.t('已确认')
      }
    }
  },
  {
    field: 'companyName',
    width: 200,
    headerText: i18n.t('公司'),
    searchOptions: { renameField: 'h.companyName' }
  },
  {
    field: 'createUserName',
    width: 200,
    headerText: i18n.t('创建人'),
    searchOptions: { renameField: 'h.createUserName' }
  },
  {
    field: 'createTime',
    width: 200,
    headerText: i18n.t('发布时间'),
    searchOptions: { renameField: 'h.createTime' },
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (data) {
          return formatDateTime(parseInt(data))
        } else {
          return '-'
        }
      }
    }
    //参与状态
    //应标截止时间
    // 当前轮次   roundNo
    //报价开始时间 bidStartTime
    //报价结束时间 bidEndTime
    //开标时间
    //议价截止时间
  }
  //创建时间   createTime
  //备注  remark
]
export const pageConfig = (url, confirmUrl) => [
  {
    title: i18n.t('待确认'),
    toolbar: [],
    gridId: permission.gridId.supply.priceConfirm,
    // gridId: permission.gridId["supply"]["rfq"]["list"]["unJoined"],
    grid: {
      allowFiltering: true,
      allowSorting: false,
      useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
      frozenColumns: 2,
      columnData: todoColumnData,
      asyncConfig: {
        url,
        params: { condition: '' }
        // defaultRules: [
        //   {
        //     label: "定价单id",
        //     field: "point_id",
        //     type: "string",
        //     operator: "equal",
        //     value: "241390687859359820",
        //   },
        // ],
        // queryBuilderWrap 非正常传参,放在参数子级里面
        // queryBuilderWrap: "queryBuilderDTO",
      }
    }
  },
  {
    title: i18n.t('已确认'),
    toolbar: [],
    gridId: permission.gridId.supply.priceConfirm,
    // gridId: permission.gridId["supply"]["rfq"]["list"]["unJoined"],
    grid: {
      allowFiltering: true,
      allowSorting: false,
      useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
      frozenColumns: 2,
      columnData: todoColumnData,
      asyncConfig: {
        url: confirmUrl,
        params: { condition: '' }
        // defaultRules: [
        //   {
        //     label: "定价单id",
        //     field: "point_id",
        //     type: "string",
        //     operator: "equal",
        //     value: "241390687859359820",
        //   },
        // ],
        // queryBuilderWrap 非正常传参,放在参数子级里面
        // queryBuilderWrap: "queryBuilderDTO",
      }
    }
  }
]
