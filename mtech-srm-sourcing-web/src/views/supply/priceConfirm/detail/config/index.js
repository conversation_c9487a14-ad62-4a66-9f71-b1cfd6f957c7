import { i18n, permission } from '@/main.js'
import { createEditInstance, Formatter } from '@/utils/ej/dataGrid/index'
import Vue from 'vue'
const editInstance = createEditInstance()
const columnDataTechnologyRate = [
  {
    width: '60',
    type: 'checkbox',
    allowEditing: false
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    allowEditing: false
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂编码'),
    allowEditing: false
  },
  // {
  //   field: "siteId",
  //   headerText: i18n.t("工厂Id"),
  //   allowEditing: false,
  //   width: 1,
  // },
  {
    field: 'deliveryPlace',
    headerText: i18n.t('直送地'),
    allowEditing: false
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    width: 200,
    allowEditing: false
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    allowEditing: false,
    width: 200
  },
  // {
  //   field: "itemId",
  //   headerText: i18n.t("物料Id"),
  //   width: 1,
  //   allowEditing: false,
  // },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    allowEditing: false
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    allowEditing: false
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商'),
    allowEditing: false
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    allowEditing: false
  },
  // {
  //   field: "supplierId",
  //   headerText: i18n.t("供应商ID"),
  //   allowEditing: false,
  //   width: 1,
  // },

  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('单价(含税)'),
    allowEditing: false
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('单价(未税)'),
    allowEditing: false
  },
  {
    field: 'priceUnitName',
    headerText: i18n.t('价格单位'),
    allowEditing: false
  },
  {
    field: 'historyPriceNum',
    headerText: i18n.t('历史价格'),
    allowEditing: false
  },
  {
    field: 'quoteMode',
    headerText: i18n.t('报价方式'),
    allowEditing: false,
    edit: editInstance.create({
      getEditConfig: () => ({
        type: 'select',
        fields: { text: 'text', value: 'value' },
        disabled: true,
        dataSource: [
          { text: i18n.t('按照入库'), value: 'in_warehouse' },
          { text: i18n.t('按出库'), value: 'out_warehouse' },
          { text: i18n.t('按订单日期'), value: 'order_date' }
        ]
      })
    }),
    formatter: ({ field }, item) => {
      const cellVal = item[field]
      switch (cellVal) {
        case 'in_warehouse':
          return i18n.t('按照入库')
        case 'out_warehouse':
          return i18n.t('按出库')
        case 'order_date':
          return i18n.t('按订单日期')
        default:
          return cellVal
      }
    }
  },
  {
    field: 'quoteAttribute',
    headerText: i18n.t('报价属性'),
    allowEditing: false,
    edit: editInstance.create({
      getEditConfig: () => ({
        type: 'select',
        fields: { text: 'text', value: 'value' },
        disabled: true,
        dataSource: [
          { text: i18n.t('标准价'), value: 'standard_price' },
          { text: i18n.t('寄售价'), value: 'mailing_price' },
          { text: i18n.t('委外价'), value: 'outsource' }
        ]
      })
    }),
    formatter: ({ field }, item) => {
      const cellVal = item[field]
      switch (cellVal) {
        case 'standard_price':
          return i18n.t('标准价')
        case 'mailing_price':
          return i18n.t('寄售价')
        case 'outsource':
          return i18n.t('委外价')
        default:
          return cellVal
      }
    }
  },

  {
    field: 'bidCurrencyName',
    headerText: i18n.t('币种'),
    allowEditing: false,
    width: 250
  },
  {
    field: 'bidCurrencyCode',
    headerText: i18n.t('币种编码'),
    allowEditing: false
  },
  {
    field: 'bidTaxRateName',
    headerText: i18n.t('税率名称'),
    allowEditing: false
  },
  {
    field: 'bidTaxRateCode',
    headerText: i18n.t('税率编码'),
    allowEditing: false
  },
  {
    field: 'bidTaxRateValue',
    headerText: i18n.t('税率值'),
    allowEditing: false
  },
  {
    field: 'unitName',
    headerText: i18n.t('基本单位'),
    allowEditing: false
  },
  {
    field: 'purUnitName',
    headerText: i18n.t('订单单位'),
    allowEditing: false
  },
  {
    field: 'conversionRate',
    headerText: i18n.t('转换率'),
    allowEditing: false
  },
  {
    field: 'minPurQuantity',
    headerText: i18n.t('最小采购量/MOQ'),
    allowEditing: false
  },
  {
    field: 'minPackageQuantity',
    headerText: i18n.t('最小包装量/MPQ'),
    allowEditing: false
  },

  {
    field: 'leadTime',
    headerText: i18n.t('L/T'),
    allowEditing: false
  },
  {
    field: 'unconditionalLeadTime',
    headerText: i18n.t('无条件L/T'),
    allowEditing: false
  },
  {
    field: 'effectiveStartDate',
    headerText: i18n.t('生效日期'),
    formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
    allowEditing: false,
    editType: 'datepickeredit',
    type: 'date',
    edit: {
      params: {
        format: 'yyyy-MM-dd'
      }
    },
    valueAccessor: (field, data) => {
      data['effectiveStartDate'] = Number(data['effectiveStartDate'])
      return Number(data['effectiveStartDate'])
    }
  },
  {
    field: 'effectiveEndDate',
    allowEditing: false,
    headerText: i18n.t('失效日期'),
    formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
    editType: 'datepickeredit',
    type: 'date',
    edit: {
      params: {
        format: 'yyyy-MM-dd'
      }
    },
    valueAccessor: (field, data) => {
      data['effectiveEndDate'] = Number(data['effectiveEndDate'])
      return Number(data['effectiveEndDate'])
    }
  },
  // 字段未定义
  {
    field: 'supplierResponse',
    headerText: i18n.t('供方意见'),
    allowEditing: true
  },
  {
    field: 'supplierConfirmStatus',
    headerText: i18n.t('供应商确认状态'),
    allowEditing: false,
    edit: editInstance.create({
      getEditConfig: () => ({
        type: 'select',
        fields: { text: 'text', value: 'value' },
        disabled: true,
        dataSource: [
          { text: i18n.t('未确认'), value: '0' },
          { text: i18n.t('已驳回'), value: '1' },
          { text: i18n.t('已确认'), value: '2' }
        ]
      })
    }),
    formatter: ({ field }, item) => {
      const cellVal = item[field]
      switch (Number(cellVal)) {
        case 0:
          return i18n.t('未确认')
        case 1:
          return i18n.t('已驳回')
        case 2:
          return i18n.t('已确认')
        default:
          return cellVal
      }
    }
  }
]

export const rdToolBar = [
  { id: 'Add', icon: 'icon_solid_upload', title: i18n.t('上传') },
  { id: 'pull', icon: 'icon_solid_Download', title: i18n.t('下载') },
  { id: 'delete', icon: 'icon_solid_Delete', title: i18n.t('删除') }
]
const iconSetting = {
  '.ppt': 'mt-icon-icon_ppt',
  '.docx': 'mt-icon-icon_word',
  '.pdf': 'mt-icon-icon_pdf',
  '.xls': 'mt-icon-icon_excel',
  '.png': 'mt-icon-icon_File1'
}
export const rdColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'fileName',
    cssClass: 'field-content',
    headerText: i18n.t('文件名称')
    // cellTools: [
    //   { id: "download", icon: "icon_solid_Download", title: i18n.t("下载") },
    //   { id: "delete", icon: "icon_solid_Delete", title: i18n.t("删除") },
    // ],
  },
  {
    field: 'fileSize',
    headerText: i18n.t('文件大小')
  },
  {
    field: 'fileType',
    headerText: i18n.t('文件类型'),
    template: function () {
      return {
        template: Vue.component('fileTypeOption', {
          template: `<div><i :class="['mt-icons', icon]"></i><span style="margin-left: 5px">{{data.fileType}}</span></div>`,
          data() {
            return { data: { data: {} } }
          },
          computed: {
            icon() {
              const { fileType } = this.data
              return fileType ? iconSetting[fileType] : ''
            }
          }
        })
      }
    }
    // template: function () {
    //   return {
    //     template: Vue.component("actionOption", {
    //       template: `<div class="action-boxs">
    //               <template v-if="data.fileType == '.xls'">
    //                 <mt-icon name="Pdf_Export" class="checked"></mt-icon>
    //                 <span>ppt</span>
    //               </template>
    //               <template v-if="data.fileType == '.docx'">
    //                 <mt-icon name="CSV_Export" class="checked"></mt-icon>
    //                 <span>ppt</span>
    //               </template>
    //               <template v-if="data.fileType == '.jpg'">
    //                 <mt-icon name="Excel_Export" class="checked"></mt-icon>
    //                 <span>ppt</span>
    //               </template>
    //               <template v-if="data.fileType == '.png'">
    //                 <mt-icon name="MT_Daterange" class="checked"></mt-icon>
    //                 <span>ppt</span>
    //               </template>
    //               <template v-if="data.fileType == '.pdf'">
    //                 <mt-icon name="Pdf_Export" class="checked"></mt-icon>
    //                 <span>ppt</span>
    //               </template>
    //           </div>`,
    //     }),
    //   };
    // },
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  }
]

export const pageConfig = (url, id, _this) => [
  {
    title: i18n.t('物料明细'),
    useToolTemplate: false,
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [
        [{ id: 'export', icon: 'icon_solid_export', title: i18n.t('导出') }],
        ['Filter', 'Refresh']
      ]
    },
    gridId: permission.gridId.supply.priceConfirmDetail.grid1,
    // permission.gridId["purchase"]["expertRating"]["detail"]["tech"]["score"],
    grid: {
      allowFiltering: true,
      editSettings: {
        allowAdding: false,
        allowEditing: true,
        allowDeleting: false,
        mode: 'Normal',
        allowEditOnDblClick: true,
        showConfirmDialog: false,
        showDeleteConfirmDialog: true,
        newRowPosition: 'Top'
      },
      lineIndex: true,
      actionComplete: _this.actionComplete,
      columnData: columnDataTechnologyRate,
      asyncConfig: {
        url,
        // params: { condition: "" },
        // queryBuilderWrap 非正常传参,放在参数子级里面
        // queryBuilderWrap: "queryBuilderDTO",
        defaultRules: [
          {
            label: '定价单id',
            field: 'point_id',
            type: 'string',
            operator: 'equal',
            value: id
          }
        ]
      }
      // dataSource: [{ expertLevel: i18n.t("打分") }],
    }
  },
  {
    title: i18n.t('附件'),
    // useToolTemplate: false,
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [rdToolBar, ['Refresh', 'setting']]
    },
    gridId: permission.gridId.supply.priceConfirmDetail.grid2,
    //   permission.gridId["purchase"]["expertRating"]["detail"]["tech"][
    //     "category"
    //   ],
    grid: {
      allowFiltering: true,
      lineIndex: true,
      columnData: rdColumnData,
      allowPaging: false,
      asyncConfig: {
        recordsPosition: 'data',
        url: '/sourcing/tenant/whitePoint/supplier/queryFileByPointId',
        params: { pointId: id },
        methods: 'get'
      }
    }
  }
]
