<template>
  <!-- @beforeClose="cancel" -->
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    height="900"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <div class="dialog-box">
        <div class="expert_box">
          <p>{{ $t('评分供应商') }}：</p>
          <mt-select
            :width="200"
            float-label-type="Never"
            :data-source="modalData.supplierArr"
            :fields="{
              text: 'supplierName',
              value: 'supplierCode'
            }"
            :value="modalData.supplierCode"
            @change="changeSupplier"
            :show-clear-button="true"
          ></mt-select>
        </div>
      </div>
      <div class="dialog-box">
        <div class="expert_box" style="width: 100%">
          <div style="width: 110px">
            <p style="padding-left: 15px">{{ $t('综合评价') }}：</p>
            <p class="lastRemark" v-show="lastRemark != null" @click="quoteLastRemark">
              {{ $t('引用上次评价') }}
            </p>
          </div>
          <div style="width: 100%">
            <mt-input
              :multiline="true"
              :rows="3"
              style="width: 100%"
              maxlength="1000"
              v-model="formObject.remark"
              float-label-type="Never"
              :placeholder="$t('请输入1000字以内')"
            ></mt-input>
          </div>
        </div>
      </div>
      <div class="box_height">
        <div>{{ $t('供应商方案附件') }}</div>
        <mt-template-page
          style="margin-top: 10px"
          ref="templateRef1"
          :template-config="pageConfigFileList"
          @handleClickCellTool="handleClickCellTool"
          @handleClickCellTitle="handleClickCellTitle"
        />
      </div>
      <div>
        <div>{{ $t('评分区') }}</div>
        <div class="box">
          <div class="op-item mt-flex" @click="handleSaveBtn">
            <i class="mt-icons mt-icon-icon_table_new"></i>
            {{ $t('保存') }}
          </div>
          <div class="expert-input">
            <div class="expert_box">
              <p>{{ $t('得分') }}：</p>
              <mt-input
                width="150"
                disabled
                v-model="formObject.totalScore"
                float-label-type="Never"
              ></mt-input>
            </div>
          </div>
        </div>
        <mt-template-page class="table" ref="templateRef" :template-config="pageConfig" />
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { columnData } from './config/index'
import { pageConfigFileList } from './config/experts'
import { download } from '@/utils/utils'
export default {
  data() {
    return {
      lastRemark: null,
      pageConfigFileList: pageConfigFileList(
        this.$API.rfxExpert.getSkillFileListUrl,
        this.modalData
      ),
      pageConfig: [
        {
          useToolTemplate: false,
          grid: {
            allowFiltering: true,
            // lineIndex: true,
            columnData: columnData,
            height: 200,
            allowPaging: false,
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Bottom'
            },
            asyncConfig: {
              url: this.$API.rfxExpert.getSaveRfxTexpert,
              recordsPosition: 'data.bidScoreSupplierDetailDTOIPage.records',
              serializeList: (list) => {
                list.forEach((item) => {
                  if (item.score == null) {
                    item.score = item.defaultScore
                  }
                })
                return list
              },
              afterAsyncData: this.updateTabTitle,
              page: {
                current: 1,
                size: 1000
              },
              queryBuilderWrap: 'requestParams',
              params: {
                rfxCode: this.modalData.rfxCode,
                bidType: 0,
                supplierCode: this.modalData.supplierCode
              }
            }
          }
        }
      ],
      formObject: {
        totalScore: '',
        remark: ''
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.formObject = { ...this.modalData.data }
    }
  },
  methods: {
    quoteLastRemark() {
      this.formObject.remark = this.lastRemark
    },
    changeSupplier(obj) {
      this.modalData.supplierCode = obj.itemData.supplierCode
      this.pageConfigFileList = pageConfigFileList(
        this.$API.rfxExpert.getSkillFileListUrl,
        this.modalData
      )
      this.$set(
        this.pageConfig[0].grid.asyncConfig.params,
        'supplierCode',
        this.modalData.supplierCode
      )
    },
    updateTabTitle(res) {
      this.formObject.remark = res.data.remark
      this.formObject.totalScore = res.data.totalScore
      this.lastRemark = res.data.lastRemark
    },
    //单元格title文字点击，文件名点击预览
    handleClickCellTitle(e) {
      if (e.field == 'fileName') {
        let params = {
          id: e?.data?.sysFileId || e?.data?.id,
          useType: 2
        }
        this.$API.fileService.getMtPreview(params).then((res) => {
          window.open(res.data)
        })
      }
    },
    //单元格按钮，点击
    handleClickCellTool(e) {
      if (e.tool.id == 'download') {
        this.$API.fileService.downloadPrivateFile({ id: e.data.sysFileId }).then((res) => {
          download({
            fileName: e.data.fileName,
            blob: new Blob([res.data])
          })
        })
      }
    },
    handleSaveBtn() {
      this.endEdit()
      setTimeout(() => {
        this.saveDataGrid()
      }, 100)
    },
    endEdit() {
      let _currentTabRef = this.$refs.templateRef.getCurrentTabRef()
      _currentTabRef?.grid?.endEdit()
    },
    saveDataGrid() {
      let _currentTabRef = this.$refs.templateRef.getCurrentTabRef()
      let _selectRecords = _currentTabRef?.grid?.getCurrentViewRecords() ?? []
      if (_selectRecords.length) {
        for (let item of _selectRecords) {
          if (item.score < item.minScore || item.score > item.maxScore) {
            this.$toast({
              content: this.$t('得分分值不应低于最低分或高于最高分'),
              type: 'warning'
            })
            return
          }
        }
        let params = {
          rfxCode: this.modalData.rfxCode,
          bidType: 0,
          remark: this.formObject.remark,
          supplierCode: this.modalData.supplierCode,
          bidScoreSupplierDetailDTOS: _selectRecords
        }

        this.$API.rfxExpert.getRfxAddSave(params).then((res) => {
          if (res.code == 200) {
            this.$emit('confirm-function')
          }
        })
      }
    },

    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
// .box_height {
//   // height: 100%;
// }
.lastRemark {
  font-size: 14px;
  color: #00469c;
  cursor: pointer;
}
.dialog-box {
  display: flex;
  align-content: center;
  justify-content: space-between;
  .expert_box {
    display: flex;
    align-content: center;
    justify-content: center;
  }
}
.box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  // .table {
  //   height: 200px;
  // }
}
.expert-input {
  display: flex;
  align-items: center;
  .expert_box {
    display: flex;
    align-content: center;
    justify-content: center;
  }
}
.dialog-content {
  font-size: 16px;
  height: 100%;
  /deep/ .div-auth {
    background: #fafafa;
    height: 35px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    color: #0f0f0f;
    font-size: 12px;
    cursor: pointer;
    border-radius: 5px;
  }
}
.op-item {
  cursor: pointer;
  color: #4f5b6d;
  align-items: center;
  margin-right: 20px;
  align-items: center;
}

// .repeat-template
//   .grid-container
//   .mt-data-grid
//   .e-control
//   .e-gridcontent
//   .e-content
//   .e-table {
//   height: 20px;
// }
</style>
