import { i18n } from '@/main.js'
const columnData = [
  {
    field: 'fileName',
    headerText: i18n.t('文件名称'),
    cssClass: 'field-content',
    cellTools: [{ id: 'download', icon: 'icon_solid_Download', title: i18n.t('下载') }]
  },
  {
    field: 'fileDetailInfo',
    headerText: i18n.t('备注')
  }
]
export const pageConfigFileList = (url, modalData) => [
  {
    useToolTemplate: false,
    grid: {
      allowFiltering: true,
      lineIndex: true,
      columnData: columnData,
      height: 200,
      allowPaging: false,
      dataSource: [],
      asyncConfig: {
        url: url,
        recordsPosition: 'data',
        params: {
          rfxCode: modalData.rfxCode,
          bidType: 0,
          supplierCode: modalData.supplierCode
        }
      }
    }
  }
]
