<template>
  <div class="full-height mt-flex-direction-column">
    <div class="miam-container">
      <div class="operate-bar">
        <div class="operate-content">
          <p class="operate-input">{{ scheduleTemplateInfo.pointNo }}</p>
        </div>
        <div class="btns-wrap">
          <mt-button @click.native="accept" v-if="$route.query.status == 0">{{
            $t('确认')
          }}</mt-button>
          <mt-button @click.native="reject" v-if="$route.query.status == 0">{{
            $t('驳回')
          }}</mt-button>
          <mt-button @click.native="comeBack">{{ $t('返回') }}</mt-button>
        </div>
      </div>
      <!-- 顶部主要信息 -->
      <div class="mian-info">
        <mt-form ref="ruleForm" :model="forObject">
          <mt-form-item ref="pointNo" prop="id" :label="$t('定价单编码')">
            <mt-input
              width="300"
              float-label-type="Never"
              disabled
              v-model="forObject.pointNo"
            ></mt-input>
          </mt-form-item>
          <mt-form-item ref="title" prop="id" :label="$t('定价单标题')">
            <mt-input
              width="300"
              float-label-type="Never"
              disabled
              v-model="forObject.title"
            ></mt-input>
          </mt-form-item>
          <mt-form-item ref="companyName" prop="id" :label="$t('公司')">
            <mt-input
              width="300"
              float-label-type="Never"
              disabled
              v-model="forObject.companyName"
              :placeholder="$t('请输入公司')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item ref="purOrgName" prop="id" :label="$t('采购组织')">
            <mt-input
              :width="300"
              disabled
              v-model="forObject.purOrgName"
              :show-clear-button="true"
            ></mt-input>
          </mt-form-item>
          <mt-form-item ref="purName" prop="id" :label="$t('采购员')">
            <mt-input
              width="300"
              float-label-type="Never"
              disabled
              v-model="forObject.purName"
            ></mt-input>
          </mt-form-item>
          <mt-form-item ref="createTime" prop="id" :label="$t('创建时间')">
            <mt-date-time-picker
              width="300"
              float-label-type="Never"
              disabled
              v-model="forObject.createTime"
              time-stamp
              format="yyyy-MM-dd HH:mm:ss"
            ></mt-date-time-picker>
          </mt-form-item>
        </mt-form>
      </div>
    </div>
    <div class="relation-ships">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTitle="handleClickCellTitle"
      />
    </div>
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import { pageConfig } from './config'
export default {
  name: 'ExpertRating',
  data() {
    return {
      rejectReason: '',
      pageConfig: pageConfig(
        this.$API.priceConfirm.queryItemByPage,
        this.$route.query.pointId,
        this
      ),
      forObject: {
        createTime: '', //开标时间
        purOrgName: '', //寻源方式
        pointNo: '',
        title: '',
        companyName: '',
        purName: ''
      },
      // sourcingModeArr: [
      //   { text: this.$t("询报价"), value: "rfq" },
      //   // { text: this.$t("直接定价"), value: "direct_pricing" },
      //   { text: this.$t("招投标"), value: "invite_bids" },
      //   { text: this.$t("竞价"), value: "bidding_price" },
      // ],
      scheduleTemplateInfo: {}
    }
  },
  mounted() {
    this.scheduleTemplateInfo = localStorage?.scheduleTemplateInfo
      ? JSON.parse(localStorage.scheduleTemplateInfo)
      : {}
    this.pageInit()
    if (this.$route.query.status != 0) {
      this.pageConfig[1].toolbar = [
        { id: 'pull', icon: 'icon_solid_Download', title: this.$t('下载') }
      ]
      this.pageConfig[0].grid.editSettings.allowEditing = false
    }
  },
  methods: {
    actionComplete(e) {
      console.log('====', e)
      if (e.requestType == 'save') {
        this.$API.priceConfirm
          .saveResponse([
            {
              id: e.data.id,
              supplierResponse: e.data.supplierResponse
            }
          ])
          .then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
          })
          .catch(() => {
            this.$refs.templateRef.refreshCurrentGridData()
          })
      }
    },
    reject() {
      this.$API.priceConfirm.reject({ id: this.$route.query.pointId }).then(() => {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        this.$router.go(-1)
      })
    },
    accept() {
      this.$API.priceConfirm.accept({ id: this.$route.query.pointId }).then(() => {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        this.$router.go(-1)
      })
    },
    pageInit() {
      this.$API.priceConfirm.viewDetailById({ id: this.$route.query.pointId }).then((r) => {
        this.forObject = { ...r.data }
      })
    },
    comeBack() {
      this.$router.go(-1)
    },
    handleClickToolBar(e) {
      console.log('use-handleClickToolBar', e)
      if (e.toolbar.id === 'Add') {
        this.$dialog({
          modal: () =>
            import(/* webpackChunkName: "components/upload" */ 'COMPONENTS/Upload/index.vue'),
          data: {
            title: this.$t('上传')
          },
          success: (data) => {
            this.handleUploadFiles(data)
          }
        })
      } else if (e.toolbar.id == 'pull') {
        let _selectRows = e.gridRef.getMtechGridRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        } else {
          // let _url = _selectRows[0]["url"];
          // window.open(_url);
          // window.open(e.data.remoteUrl);
          _selectRows.forEach((ele) => {
            this.$API.fileService
              .downloadPrivateFile({
                id: ele['sysFileId']
              })
              .then((res) => {
                download({
                  fileName: ele.fileName,
                  blob: new Blob([res.data])
                })
              })
          })
        }
      } else if (e.toolbar.id == 'delete') {
        let _selectRows = e.gridRef.getMtechGridRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        } else {
          this.handleBatchDelete(_selectRows)
        }
      } else if (e.toolbar.id == 'export') {
        let params = {
          page: { current: 1, size: 1000 },
          defaultRules: [
            {
              label: this.$t('定价单id'),
              field: 'point_id',
              type: 'string',
              operator: 'equal',
              value: this.$route.query.pointId
            }
          ]
        }
        this.$API.priceConfirm.exportItemSup(params).then((res) => {
          this.$toast({
            type: 'success',
            content: this.$t('正在导出，请稍后！')
          })
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
      }
    },
    //批量删除操作
    handleBatchDelete(_selectGridRecords) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      this.handleDelete(_selectIds)
    },
    //删除文件
    handleDelete(ids) {
      let _params = {
        fileIdList: ids,
        tenantType: 2,
        pointId: this.$route.query.pointId
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.priceConfirm.deleteFile(_params).then(() => {
            this.$store.commit('endLoading')
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    //单元格按钮，点击
    // handleClickCellTool(e) {
    //   console.log("use-handleClickCellTool", e);
    //   if (e.tool.id == "download") {
    //     window.open(e.data.url);
    //     // window.open(e.data.remoteUrl);
    //     // this.$API.fileService.downloadPrivateFile({ id: e.data.sysFileId });
    //   } else if (e.tool.id == "delete") {
    //     this.handleDelete([e.data.id]);
    //   }
    // },
    //单元格title文字点击，文件名点击预览
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
      if (e.field == 'fileName') {
        let params = {
          id: e?.data?.sysFileId || e?.data?.id,
          useType: 2
        }
        this.$API.fileService.getMtPreview(params).then((res) => {
          window.open(res.data)
        })
      }
    },
    //执行上传文件
    handleUploadFiles(data) {
      let { id, fileName, fileSize, fileType, url, remoteUrl, sysName } = data
      let _params = {
        docId: this.$route.query.pointId,
        tenantType: 2,
        fileName: fileName,
        fileSize: fileSize,
        fileType: fileType,
        remoteUrl: remoteUrl,
        sysFileId: id,
        sysName: sysName,
        url: url
        // docType: "",
        // id: 0,
        // nodeId: 0,
        // nodeName: "",
        // nodeType: 0,
        // sortValue: 0,
      }
      this.$API.priceConfirm.saveFile(_params).then(() => {
        this.$toast({
          content: this.$t('上传成功'),
          type: 'success'
        })
        this.$refs.templateRef.refreshCurrentGridData()
      })
    }
  }
}
</script>

<style scoped lang="scss">
.operate-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.mt-form {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  .grid_flex {
    width: 679px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.operate-bar {
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #4f5b6d;
  font-size: 14px;
  padding: 20px;
  .btns-wrap {
    /deep/ .mt-button {
      margin-right: 0;
      button {
        width: 76px;
        height: 34px;
        background: transparent;
        //border: 1px solid rgba(0, 70, 156, 0.1);
        border-radius: 4px;
        box-shadow: unset;
        padding: 0;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
      }
    }
  }
}
.operate-bars {
  width: 20px;
  height: 20px;
  background: rgba(0, 70, 156, 0.1);
  border: 1px solid rgba(0, 70, 156, 0.9);
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;

  i {
    width: 6px;
    height: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: rotate(180deg);
  }
}
.miam-container {
  background: #fff;
  // padding: 0 20px;
  min-width: 1300px;
  .mian-info {
    background: rgba(255, 255, 255, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    padding: 20px;
    min-width: 1300px;
    .normal-title {
      width: 100%;
      height: 14px;
      line-height: 14px;
      font-size: 14px;
      color: #292929;
      font-family: PingFangSC;
      font-weight: 500;

      &:before {
        content: ' ';
        display: inline-block;
        vertical-align: middle;
        width: 2px;
        height: 10px;
        background: rgba(0, 70, 156, 1);
        border-radius: 1px;
        margin-right: 10px;
      }
    }

    .input-item {
      margin-top: 20px;
      padding-right: 50px;
      .label-txt {
        height: 40px;
        width: 130px;
        font-size: 14px;
        // line-height: 40px;
        color: #292929;
      }
      .label-value {
        height: 40px;
        width: 130px;
        font-size: 14px;
        // line-height: 40px;
        color: #35404e;
      }
      .select-container {
        height: 40px;
      }
      .e-label {
        color: #35404e;
      }
      .label-text {
        color: #35404e;
      }
    }
    .input-item /deep/ .normal-width {
      width: 240px;
    }
    .input-item /deep/ .e-radio + label .e-label {
      color: #35404e;
    }
  }
}
.relation-ships {
  flex: 1;
  background: rgba(255, 255, 255, 1);
  min-width: 1300px;
}
</style>
