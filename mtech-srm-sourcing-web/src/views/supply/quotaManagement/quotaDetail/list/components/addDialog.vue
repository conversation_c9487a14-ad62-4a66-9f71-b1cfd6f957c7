// 计划清单-新增计划弹窗
<template>
  <mt-dialog
    ref="dialog"
    :header="dialogTitle"
    css-class="create-proj-dialog"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <div class="dialog-content">
      <mt-form ref="ruleForm" :model="formObject" :rules="rules">
        <div class="flex">
          <mt-form-item prop="planName" :label="$t('模板名称')">
            <mt-input
              v-model="formObject.planName"
              :show-clear-button="true"
              :multiline="false"
              :placeholder="$t('请输入模板名称')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="analysisType" :label="$t('分析类型')">
            <mt-select
              v-model="formObject.analysisType"
              float-label-type="Never"
              :allow-filtering="true"
              :data-source="analysisList"
              :placeholder="$t('请选择分析类型')"
              @change="analysisTypeChange"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="approachesTo" :label="$t('计算方式')">
            <mt-select
              v-model="formObject.approachesTo"
              float-label-type="Never"
              :allow-filtering="true"
              :data-source="approachesToList"
              :placeholder="$t('权重式计算')"
              @change="approachesToChange"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="fullMarks" :label="$t('满分')">
            <mt-select
              v-model="formObject.fullMarks"
              float-label-type="Never"
              :allow-filtering="true"
              :data-source="planList"
              placeholder="100"
              @change="planChange"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="retainDecimal" :label="$t('保留小数')">
            <mt-select
              v-model="formObject.retainDecimal"
              float-label-type="Never"
              :allow-filtering="true"
              :data-source="planLoopList"
              placeholder="0"
              @change="planLoopChange"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="describe" :label="$t('描述')">
            <mt-input
              v-model="formObject.describe"
              :show-clear-button="true"
              :multiline="false"
              :placeholder="$t('请输入')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="organization" :label="$t('组织')">
            <mt-select
              v-model="formObject.organization"
              float-label-type="Never"
              :allow-filtering="true"
              :data-source="orgList"
              :placeholder="$t('账户具备数据权限的组织选择器 单选')"
              @change="orgChange"
            ></mt-select>
          </mt-form-item>
        </div>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        },
        {
          click: this.confirmEnter,
          buttonModel: { isPrimary: 'true', content: this.$t('保存并进入') }
        }
      ],
      dialogTitle: '',
      orgList: [], // 组织
      analysisList: [], // 分析类型
      approachesToList: [], // 数据范围
      planList: [], // 计划策略
      planLoopList: [], // 计划周期
      supplierTypeList: [],
      formObject: {
        planName: null,
        organization: null,
        analysisType: null,
        approachesTo: null,
        fullMarks: null,
        retainDecimal: null
      },
      rules: {
        planName: [
          {
            required: true,
            message: this.$t('请输入模板名称'),
            trigger: 'blur'
          }
        ],
        analysisType: [
          {
            required: true,
            message: this.$t('请选择分析类型'),
            trigger: 'blur'
          }
        ],
        approachesTo: [{ required: true, message: this.$t('权重式计算'), trigger: 'blur' }],
        fullMarks: [{ required: true, message: this.$t('100分'), trigger: 'blur' }],
        retainDecimal: [{ required: true, message: this.$t('保留0位小数'), trigger: 'blur' }],
        organization: [
          {
            required: true,
            message: this.$t('账户具备数据权限的组织选择器 单选'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  mounted() {
    // if (this.dialogData.dialogType === "add") {
    this.dialogTitle = this.$t('新增')
    // } else {
    //   this.dialogTitle = "编辑计划";
    // }
    this.$refs['dialog'].ejsRef.show()
    // console.log(this.dialogData);
    // console.log(this.dialogData);
  },
  methods: {
    isShow() {},
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    // 计划策略改变
    planChange() {},
    // 计划周期改变
    planLoopChange() {},
    // 有效期改变
    effectTimeChange() {},
    // 重复评分改变
    scoreChange() {},
    // 重复分析改变
    analysisChange() {},
    // 分析类型改变
    analysisTypeChange() {},
    // 数据范围改变
    approachesToChange() {},
    // 组织改变
    orgChange() {},
    handleClose() {
      // this.$emit("handleAddDialogShow", false);
      this.$emit('cancel-function')
    },
    save() {
      console.log('保存')
    },
    confirmEnter() {
      console.log('保存并进入')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 32px 6px 0 6px !important;
}
.mt-form-item-topLabel {
  width: 100%;
}
.mt-form {
  width: 100%;
  .flex {
    width: 95%;
    height: 100%;
    margin: 0 auto;
    .mt-form-item {
      width: 100%;
      display: block;
    }
  }
}
</style>
