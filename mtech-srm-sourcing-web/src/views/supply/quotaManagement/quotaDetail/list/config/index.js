import { i18n } from '@/main.js'
const map = [
  {
    status: 0,
    label: i18n.t('草稿'),
    cssClass: ['title-#FF370A']
  },
  {
    status: 1,
    label: i18n.t('审批中'),
    cssClass: ['OfferBid-status1']
  },
  {
    status: 2,
    label: i18n.t('驳回'),
    cssClass: ['OfferBid-status2']
  },
  {
    status: 3,
    label: i18n.t('通过'),
    cssClass: ['OfferBid-status1']
  },
  {
    status: 4,
    label: i18n.t('取消'),
    cssClass: ['OfferBid-status1']
  }
]
const quotaApplicationList = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'quotaProtocolCode',
    headerText: i18n.t('配额协议编码'),
    cssClass: 'field-content'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: map,
      fields: { text: 'label', value: 'status' }
    },
    cellTools: [
      {
        id: 'Cancel',
        // icon: "icon_solid_Cancel",
        title: i18n.t('取消'),
        visibleCondition: (data) => {
          return data['status'] == 1
        }
      }
    ]
  },
  {
    field: 'quotaProtocolDescription',
    headerText: i18n.t('配额协议描述')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    field: 'createWay',
    headerText: i18n.t('创建方式')
  }
]
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'Cancel', icon: 'icon_solid_Cancel', title: i18n.t('取消') },
  { id: 'Submit', icon: 'icon_solid_Submit', title: i18n.t('提交') }
]
export const pageConfig = () => [
  {
    toolbar,
    grid: {
      allowFiltering: true,
      useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
      columnData: quotaApplicationList,
      asyncConfig: {},
      dataSource: []
    }
  }
]
