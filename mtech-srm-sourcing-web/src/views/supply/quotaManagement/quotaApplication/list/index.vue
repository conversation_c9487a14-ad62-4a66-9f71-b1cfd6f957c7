<!--  配额管理-配额申请-列表  -->
<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>
<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      // pageConfig: pageConfig(this.$API.costModel.getModelList),
      pageConfig: pageConfig()
    }
  },
  methods: {
    //表格头部按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.grid.getSelectedRecords()
      console.log('use-handleClickToolBar', e, _selectGridRecords)
      if (
        _selectGridRecords.length <= 0 &&
        (e.toolbar.id == 'Cancel' || e.toolbar.id == 'Submit')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      if (e.toolbar.id == 'Add') {
        //新增
        this.handleAddCost()
      } else if (e.toolbar.id == 'Cancel') {
        //取消
        this.handleBatchCancel(_selectGridRecords)
      } else if (e.toolbar.id == 'Submit') {
        //提交
        this.handleBatchSubmit(_selectGridRecords)
      }
    },

    //点击取消
    handleBatchCancel() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认执行取消操作？`)
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        }
      })
    },

    // 提交
    handleBatchSubmit(_selectGridRecords) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认执行提交操作？`)
        },
        success: () => {
          let idList = []
          _selectGridRecords.map((item) => {
            idList.push(item.id)
          })
          // this.$API.costModel
          //   .commitById({
          //     idList,
          //   })
          //   .then((res) => {
          //     if (res.code == 200) {
          this.$refs.templateRef.refreshCurrentGridData()
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          // }
          // });
        }
      })
    },
    //配额管理-配额申请-新增
    handleAddCost() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/settings/cost/list/components/costDialog" */ './components/addDialog.vue'
          ),
        data: {
          title: this.$t('新增配额申请')
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //单元格标题
    handleClickCellTitle(e) {
      // console.log("use-handleClickCellTitle", e);
      if (e.field == 'quotaProtocolCode') {
        localStorage.quotaList = JSON.stringify(e.data)
        this.$router.push({
          name: `supply-QM-quotaApplication-detail`,
          query: {
            configId: e.data.id
          }
        })
      }
    },
    //行内取消
    handleClickCellTool(e) {
      // console.log(e.tool);
      if (e.tool.id == 'Cancel') {
        console.log('点击了行内取消')
      }
    }
  }
}
</script>
