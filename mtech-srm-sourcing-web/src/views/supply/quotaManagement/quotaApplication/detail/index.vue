<template>
  <div class="supplier-box mt-flex">
    <div class="miam-container">
      <div class="operate-bar">
        <div class="op-item mt-flex" @click="submitForm">
          <i class="mt-icons mt-icon-icon_table_new"></i>
          {{ $t('返回') }}
        </div>

        <div class="op-item mt-flex">
          <i class="mt-icons mt-icon-Delete"></i>
          {{ $t('提交') }}
        </div>
        <div class="op-item mt-flex" @click="expertSave">
          <i class="mt-icons mt-icon-icon_table_new"></i>
          {{ $t('保存') }}
        </div>
      </div>
      <!-- 顶部主要信息 -->
      <div class="mian-info">
        <mt-form ref="ruleForm">
          <!-- :model="ruleForm" :rules="formRules" -->
          <mt-form-item ref="formItemarea" prop="applycode" :label="$t('申请单编号：')">
            <!--   :css-class="cssClassObj['applycode']"
              v-model="ruleForm.applycode"
              disabled -->
            <mt-input width="300" type="text" :placeholder="$t('请输入申请单编号')"></mt-input>
          </mt-form-item>
          <mt-form-item ref="formItemFullname" prop="expertLevel" :label="$t('专家级别：')">
            <!--     :data-source="exLevelArr"
              v-model="ruleForm.expertLevel"
              :css-class="cssClassObj['expertLevel']"
               -->
            <mt-select
              :width="300"
              :show-clear-button="true"
              :placeholder="$t('请选择专家级别')"
            ></mt-select>
          </mt-form-item>
        </mt-form>
      </div>
      <div class="full-height">
        <mt-template-page
          ref="templateRef"
          :padding-top="true"
          :template-config="pageConfig"
          @handleClickToolBar="handleClickToolBar"
          @handleClickCellTool="handleClickCellTool"
          @handleClickCellTitle="handleClickCellTitle"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      tabIndex: 0,
      pageConfig: pageConfig()
    }
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
    },
    submitForm() {
      this.$router.go(-1)
    },
    selectTab(tabId) {
      this.tabId = tabId
    },
    expertSave() {
      // this.$refs.ruleForm.validate((valid) => {
      //   if (valid) {
      //     let params = { ...this.ruleForm };
      //     if (!this.editStatus) {
      //       delete params.id;
      //     }
      //     this.$API.expert.expertsave(params).then((res) => {
      //       if (res.code == 200) {
      //         this.$emit("confirm-function");
      //         this.$toast({
      //           content: res.msg,
      //           type: "success",
      //         });
      //       }
      //     });
      //     console.log(params);
      //   }
      // });
    }
  },
  // props: {
  //   modalData: {
  //     type: Object,
  //     default: () => {
  //       return {};
  //     },
  //   },
  // },
  computed: {
    // propsData() {
    //   return {
    //     ...this.modalData,
    //   };
    // },
  },
  mounted() {
    // if (this.modalData || this.modalData.data) {
    //   this.ruleForm = { ...this.modalData.data };
    // }
  }
}
</script>

<style lang="scss">
.supplier-box {
  .mt-form-item-label {
    display: flex !important;
    justify-content: space-between !important;
  }
}
</style>
<style lang="scss" scoped>
.mt-form {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  .grid_flex {
    width: 679px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.supplier-box {
  position: absolute;
  width: 100%;
  min-height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: #fff;
  overflow: auto;
  -webkit-overflow-scrolling: scroll;

  .flex1 {
    flex: 1;
  }

  .miam-container {
    flex: 1;
    background: #fff;
    padding: 0 20px;

    .operate-bar {
      height: 60px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      color: #4f5b6d;
      font-size: 14px;

      i {
        margin-right: 4px;
        color: #4f5b6d;
      }
      .op-item {
        cursor: pointer;
        color: #4f5b6d;
        align-items: center;
        margin-right: 20px;
        align-items: center;
      }
    }

    .flex-d-c {
      flex-direction: column;
    }

    .mian-info {
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(232, 232, 232, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      padding: 20px;

      .normal-title {
        width: 100%;
        height: 14px;
        line-height: 14px;
        font-size: 14px;
        color: #292929;
        font-family: PingFangSC;
        font-weight: 500;

        // &amp;:before {
        //   content: " ";
        //   display: inline-block;
        //   vertical-align: middle;
        //   width: 2px;
        //   height: 10px;
        //   background: rgba(0, 70, 156, 1);
        //   border-radius: 1px;
        //   margin-right: 10px;
        // }
      }

      .flex-d-c {
        flex-direction: column;
      }

      .input-item {
        margin-top: 20px;
        padding-right: 50px;
        .label-txt {
          height: 40px;
          width: 130px;
          font-size: 14px;
          // line-height: 40px;
          color: #292929;
        }
        .label-value {
          height: 40px;
          width: 130px;
          font-size: 14px;
          // line-height: 40px;
          color: #35404e;
        }
        .select-container {
          height: 40px;
        }
        .e-label {
          color: #35404e;
        }
        .label-text {
          color: #35404e;
        }
      }
      .input-item /deep/ .normal-width {
        width: 240px;
      }
      .input-item /deep/ .e-radio + label .e-label {
        color: #35404e;
      }
    }

    .relation-ships {
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(232, 232, 232, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      padding: 20px;
      margin-top: 20px;

      .tab-box {
        width: 100%;
        height: 40px;
        border-bottom: 1px solid #e8e8e8;
        position: relative;

        .tab-item {
          font-size: 14px;
          height: 40px;
          line-height: 40px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(40, 41, 41, 1);
          padding: 0 38px;
          cursor: pointer;
        }
        .active {
          font-size: 16px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(0, 70, 156, 1);
          border-bottom: 4px solid #00469c;
        }

        .right-btn {
          height: 40px;
          position: absolute;
          right: 0;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          transform: all 0.6s ease-in;
          i {
            margin-right: 4px;
            color: #4f5b6d;
          }
          .op-item {
            color: #4f5b6d;
            align-items: center;
            margin-right: 20px;
            align-items: center;
            cursor: pointer;
          }
          .add-new {
            i {
              color: #6386c1;
            }
            color: #6386c1;
          }
        }
      }
      .tab-content {
        .grid-search {
          height: 60px;
          line-height: 60px;
          justify-content: flex-end;
          .search-box {
            .label-txt {
              font-size: 14px;
              font-family: PingFangSC;
              font-weight: normal;
            }
          }
        }
        /deep/ .common-template-page .page-grid-container {
          padding: 0 !important;
        }
      }
    }
  }

  .grid-content {
    padding-top: 20px;
  }
}
</style>
