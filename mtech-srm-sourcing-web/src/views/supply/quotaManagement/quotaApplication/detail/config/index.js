import { i18n } from '@/main.js'
const map = [
  {
    status: 0,
    label: i18n.t('草稿'),
    cssClass: ['title-#FF370A']
  },
  {
    status: 1,
    label: i18n.t('审批中'),
    cssClass: ['OfferBid-status1']
  },
  {
    status: 2,
    label: i18n.t('驳回'),
    cssClass: ['OfferBid-status2']
  },
  {
    status: 3,
    label: i18n.t('通过'),
    cssClass: ['OfferBid-status1']
  },
  {
    status: 4,
    label: i18n.t('取消'),
    cssClass: ['OfferBid-status1']
  }
]
const quotaApplicationList = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: map,
      fields: { text: 'label', value: 'status' }
    },
    cellTools: [
      {
        id: 'Cancel',
        // icon: "icon_solid_Cancel",
        title: i18n.t('取消'),
        visibleCondition: (data) => {
          return data['status'] == 1
        }
      }
    ]
  },
  {
    field: ' factory',
    headerText: i18n.t('工厂')
  },
  {
    field: ' supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: ' supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: ' effectiveDate',
    headerText: i18n.t('生效日期')
  },
  {
    field: ' failureDate',
    headerText: i18n.t('失效日期')
  },
  {
    field: 'minSplitAmount',
    headerText: i18n.t('最小起拆量')
  },
  {
    field: 'unit',
    headerText: i18n.t('单位')
  },

  {
    field: 'quotasThan',
    headerText: i18n.t('配额比(%)')
  },
  {
    field: 'minPackaging',
    headerText: i18n.t('最小包装量')
  },
  {
    field: 'minProcurement',
    headerText: i18n.t('最小采购量')
  }
]
const toolbar = [
  { id: 'Add', icon: 'icon_table_new', title: i18n.t('新增') },
  { id: 'Cancel', icon: 'icon_solid_Cancel', title: i18n.t('取消') },
  { id: 'Submit', icon: 'icon_solid_Submit', title: i18n.t('提交') }
]
export const pageConfig = () => [
  {
    toolbar,
    grid: {
      allowFiltering: true,
      useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
      columnData: quotaApplicationList,
      asyncConfig: {},
      dataSource: []
    }
  }
]
