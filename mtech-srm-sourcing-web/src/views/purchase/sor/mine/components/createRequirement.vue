// 创建寻源需求弹窗
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="modalData.title"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="createRequireRef" :model="createRequireForm" :rules="formRules">
        <mt-form-item prop="porName" :label="$t('单据名称')">
          <mt-input
            v-model="createRequireForm.porName"
            float-label-type="Never"
            :placeholder="$t('请输入单据名称')"
          ></mt-input>
        </mt-form-item>
        <!-- <mt-form-item :label="$t('单据编码')">
          <mt-input
            disabled
            :show-clear-button="false"
            float-label-type="Never"
            :placeholder="$t('保存后自动生成')"
          ></mt-input>
        </mt-form-item> -->

        <mt-form-item prop="companyCode" :label="$t('公司编码')">
          <mt-select
            v-model="createRequireForm.companyCode"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="modalData.companyList"
            :fields="{ text: 'companyCode', value: 'companyCode' }"
            :placeholder="$t('请选择公司编码')"
            @change="handleSelectChange($event, 'companyCode', 'companyId')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="companyId" :label="$t('公司名称')">
          <mt-select
            v-model="createRequireForm.companyId"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="modalData.companyList"
            :fields="{ text: 'companyName', value: 'id' }"
            :placeholder="$t('请选择公司名称')"
            @change="handleSelectChange($event, 'companyId', 'companyCode')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="deptId" :label="$t('申请部门')">
          <mt-select
            v-model="createRequireForm.deptId"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="modalData.departmentList"
            :fields="{ text: 'departmentName', value: 'organizationId' }"
            :placeholder="$t('请选择申请部门')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="purId" :label="$t('申请人')">
          <mt-select
            v-model="createRequireForm.purId"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="modalData.tenantIdList"
            :fields="{ text: 'employeeName', value: 'id' }"
            :placeholder="$t('请选择申请人')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="applyTime" :label="$t('申请时间')">
          <mt-date-picker
            v-model="createRequireForm.applyTime"
            float-label-type="Never"
            :allow-filtering="true"
            :open-on-focus="true"
            format="yyyy-MM-dd HH:mm"
            :placeholder="$t('请选择申请时间')"
          ></mt-date-picker>
        </mt-form-item>

        <mt-form-item prop="businessTypeId" :label="$t('业务类型')">
          <mt-select
            v-model="createRequireForm.businessTypeId"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="modalData.businessTypeList"
            :fields="{ text: 'itemName', value: 'id' }"
            :placeholder="$t('请选择业务类型')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="remark" :label="$t('备注')">
          <mt-input
            v-model="createRequireForm.remark"
            float-label-type="Never"
            :placeholder="$t('请输入备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import utils from '@/utils/utils'
export default {
  name: 'CreateRequirementDialog',
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      createRequireForm: {
        porName: '',
        companyId: '',
        companyCode: '',
        deptId: '',
        purId: '',
        applyTime: new Date(),
        businessTypeId: '',
        businessTypeCode: '',
        remark: ''
      },
      formRules: {
        porName: [
          {
            required: true,
            message: this.$t('请输入单据名称'),
            trigger: 'blur'
          },
          {
            min: 1,
            max: 30,
            message: this.$t('单据名称长度在1~30个字符'),
            trigger: 'blur'
          }
        ],
        companyCode: [
          {
            required: true,
            message: this.$t('请选择公司代码'),
            trigger: 'blur'
          }
        ],
        companyId: [
          {
            required: true,
            message: this.$t('请选择公司名称'),
            trigger: 'blur'
          }
        ],
        deptId: [
          {
            required: true,
            message: this.$t('请选择申请部门'),
            trigger: 'blur'
          }
        ],
        purId: [{ required: true, message: this.$t('请选择申请人'), trigger: 'blur' }],
        applyTime: [
          {
            required: true,
            message: this.$t('请选择申请时间'),
            trigger: 'blur'
          }
        ],
        businessTypeId: [
          {
            required: true,
            message: this.$t('请选择业务类型'),
            trigger: 'blur'
          }
        ],
        remark: [
          {
            max: 150,
            message: this.$t('备注不能超过150个字符'),
            trigger: 'blur'
          }
        ]
      },
      companyMap: new Map() // 以code为基准，值是id + 以id为基准，值是code
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {},
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    console.log('this.modalData', this.modalData)
    this.modalData.companyList.forEach((item) => {
      this.companyMap.set(item.companyCode, item.id)
      this.companyMap.set(item.id, item.companyCode)
    })
    if (this.modalData.row) {
      this.createRequireForm = { ...this.modalData.row }
    }
    this.getUserInfo()
    // this.getFormValidRules(
    //   "formRules",
    //   this.$API.porMine.saveSourceValid
    // );
  },
  methods: {
    //获取当前用户信息
    getUserInfo() {
      this.$API.iamService.getUserDetail().then((res) => {
        this.createRequireForm.purId = res.data.employeeId
        this.createRequireForm.companyId = res.data.companyOrg.id
        this.createRequireForm.deptId = res.data.department.id
        this.createRequireForm.companyCode = res.data.companyOrg.orgCode
        let _purParams = { purId: res.data.employeeId }
        this.getPreRecordInfo(_purParams)
      })
    },
    getPreRecordInfo(_purParams) {
      this.$API.porMine.getUserbusinessType(_purParams).then((res) => {
        this.createRequireForm.businessTypeId = res.data.businessTypeId
      })
    },
    confirm() {
      this.$refs.createRequireRef.validate((valid) => {
        if (valid) {
          // 查找对应的code
          let _businessTypeCode = this.modalData.businessTypeList.find(
            (item) => item.id == this.createRequireForm.businessTypeId
          ).itemCode

          let params = [
            {
              ...this.createRequireForm,
              businessTypeCode: _businessTypeCode,
              applyTime: utils.formatTime(new Date(this.createRequireForm.applyTime)),
              deptName: this.modalData.departmentList.find(
                (item) => item.organizationId === this.createRequireForm.deptId
              ).departmentName,
              purName: this.modalData.tenantIdList.find(
                (item) => item.id === this.createRequireForm.purId
              ).employeeName
            }
          ]
          this.$API.porMine.saveSource(params).then((res) => {
            if (res.code == 200) {
              this.$emit('confirm-function', res.data)
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    // 公司 下拉联动的修改
    handleSelectChange(e, current, keys) {
      this.$nextTick(() => {
        this.createRequireForm[keys] = this.companyMap.get(this.createRequireForm[current])
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
