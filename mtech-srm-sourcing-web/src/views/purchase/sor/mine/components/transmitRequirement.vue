// 转发弹窗
<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <div class="source-label label-left">{{ $t('转发用户：') }}</div>
      <mt-select
        v-model="purId"
        float-label-type="Never"
        :data-source="dataArr"
        :show-clear-button="true"
        :allow-filtering="true"
        :placeholder="$t('请选择转发用户')"
      ></mt-select>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      purId: -1,
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        width: '500px',
        buttons: this.buttons,
        cssClass: ('small-dialog ' + this.modalData.cssClass).trim()
      }
    },
    header() {
      // let _smallHeader =
      //   '<i class="mt-icons mt-icon-icon_solid_Warning"></i><div class="header-text" id="_title">' +
      //   this.modalData.title +
      //   "</div>";
      // return _smallHeader;
      return this.modalData.title
    },

    dataArr() {
      return this.modalData.distributeArr || []
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    confirm() {
      if (this.purId <= -1) {
        this.$toast({ type: 'warning', content: this.$t('请选择转发的人员') })
        return
      }
      let _selectRow = this.dataArr.find((item) => {
        return item.value == this.purId
      })
      this.$API.porMine[this.propsData.requestUrl]({
        idList: this.propsData.ids,
        purId: this.purId,
        purName: _selectRow.text.split(' -')[0]
      }).then(() => {
        this.$emit('confirm-function')
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
