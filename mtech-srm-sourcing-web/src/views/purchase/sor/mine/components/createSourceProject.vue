// 创建寻源项目弹窗、创建RFX弹窗
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="createProjForm" :model="createProjForm" :rules="formRules">
        <mt-form-item prop="rfxName" :label="$t('名称')" label-style="top" label-align="left">
          <mt-input
            v-model="createProjForm.rfxName"
            float-label-type="Never"
            :placeholder="$t('请输入名称')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item
          prop="recContractTypeId"
          :label="$t('推荐合同类型')"
          label-style="top"
          label-align="left"
        >
          <mt-select
            ref="recContractRef"
            v-model="createProjForm.recContractTypeId"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="propsData.recContractList"
            :fields="{ text: 'itemName', value: 'id' }"
            :placeholder="$t('请选择推荐合同类型')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="purOrgId" :label="$t('采购组织')" label-style="top" label-align="left">
          <mt-select
            ref="purOrgRef"
            v-model="createProjForm.purOrgId"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="propsData.purchaseOrganizList"
            :fields="{ text: 'organizationName', value: 'id' }"
            :placeholder="$t('请选择采购组织')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item
          prop="businessTypeId"
          :label="$t('业务类型')"
          label-style="top"
          label-align="left"
        >
          <mt-select
            ref="businessTypeRef"
            v-model="createProjForm.businessTypeId"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="propsData.businessTypeList"
            :fields="{ text: 'itemName', value: 'id' }"
            :placeholder="$t('请选择业务类型')"
          ></mt-select>
        </mt-form-item>

        <!-- <mt-form-item prop="projs">
          <mt-select
            v-model="createProjForm.projs"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="propsData.projStrategyList"
            :fields="{ text: 'itemName', value: 'id' }"
            :placeholder="$t('请选择项目策略')"
          ></mt-select>
        </mt-form-item> -->

        <mt-form-item
          prop="purExecutorId"
          :label="$t('执行人')"
          label-style="top"
          label-align="left"
        >
          <mt-select
            ref="purExecutorRef"
            v-model="createProjForm.purExecutorId"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="propsData.tenantIdList"
            :fields="{ text: 'employeeName', value: 'id' }"
            :placeholder="$t('请选择执行人')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')">
          <mt-input
            v-model="createProjForm.remark"
            float-label-type="Never"
            :placeholder="$t('请输入备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      createProjForm: {
        rfxName: '',
        recContractTypeId: '',
        purOrgId: '',
        businessTypeId: '',
        remark: '',
        purExecutorId: ''
      },
      formRules: {}
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        width: '500px',
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.formInfo) {
      this.createProjForm = { ...this.modalData.formInfo }
    }
    this.getFormValidRules('formRules', this.$API.porMine.saveRFXPorvalid)
  },
  methods: {
    confirm() {
      this.$refs.createProjForm.validate((valid) => {
        if (valid) {
          let params = {
            ...this.createProjForm,
            [this.modalData.idListName]: this.modalData.ids
          }
          console.log(params)
          //处理下拉框数据赋值
          this.$utils.assignDataFromRefs(params, [
            {
              key: 'recContractTypeId', //推荐合同类型 recContractTypeId下拉框数据
              ref: this.$refs.recContractRef.ejsRef,
              fields: {
                recContractTypeCode: 'itemCode',
                recContractTypeName: 'itemName'
              }
            },
            {
              key: 'purOrgId', //采购组织 purOrgId下拉框数据
              ref: this.$refs.purOrgRef.ejsRef,
              fields: {
                purOrgCode: 'organizationCode',
                purOrgName: 'organizationName'
              }
            },
            {
              key: 'businessTypeId', //业务类型 businessTypeId下拉框数据
              ref: this.$refs.businessTypeRef.ejsRef,
              fields: {
                businessTypeCode: 'itemCode',
                businessTypeName: 'itemName'
              }
            },
            {
              key: 'purExecutorId', //执行人 purExecutorId下拉框数据
              ref: this.$refs.purExecutorRef.ejsRef,
              fields: {
                purExecutorName: 'employeeName'
              }
            }
          ])
          this.$API.porMine[this.modalData.requestUrl](params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('创建成功'), type: 'success' })
              this.$emit('confirm-function')
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
