<template>
  <div class="full-height require-mine">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleSelectTab="handleSelectTab"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
      <mt-template-page
        slot="slot-0"
        ref="templateRef0"
        :template-config="requireConfig"
        @handleSelectTab="handleSelectTabSlot"
        @handleClickToolBar="handleClickToolBarSlot"
        @handleClickCellTool="handleClickCellToolSlot"
        @handleClickCellTitle="handleClickCellTitleSlot"
      />
    </mt-template-page>
  </div>
</template>

<script>
import { requireConfig, pageConfig } from './config'
import MixIn from '../config/mixin'
export default {
  mixins: [MixIn],
  data() {
    return {
      requireConfig: requireConfig(
        this.$API.porMine.queryMyHeader,
        this.$API.porMine.getMineDetailList
      ),
      pageConfig: pageConfig(this.$API.porMine.queryCreate, this.$API.porMine.queryNoApprove),
      currentTabIndex: 0,
      currentSlotTabIndex: 0,
      tenantUserList: [], // 用户列表
      recContractList: [], // 推荐合同类型
      purchaseOrganizList: [], // 采购组织列表
      businessTypeList: [], // 业务类型列表
      projStrategyList: [], // 项目策略列表
      tenantIdList: [], // 执行人列表、申请人列表（同用户列表，只是结构不同）
      companyList: [], // 公司列表
      departmentList: [], // 部门列表
      queryDataSource: [], //新增、编辑中用到的下拉列表
      categoryList: [] //品类列表
    }
  },
  mounted() {
    this.getStrategyConfigCondition()
  },

  methods: {
    // -----------------tab页签--------------------------
    handleSelectTab(e) {
      this.currentTabIndex = e
    },
    handleSelectTabSlot(e) {
      this.currentSlotTabIndex = e
    },
    // -----------------顶部按钮--------------------------
    handleClickToolBar(e) {
      if (e.grid.getSelectedRecords().length < 1 && e.toolbar.id !== 'create-require') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _id = []
      e.grid.getSelectedRecords().map((item) => _id.push(item.id))
      if (e.toolbar.id == 'create-proj') {
        // 创建寻源项目 -- tab3
        this.handleCreateProj(_id)
      } else if (e.toolbar.id == 'create-require') {
        // 创建寻源需求 -- tab3 不用勾选
        this.handleCreateRequire(e)
      } else if (e.toolbar.id == 'publish') {
        // 发布 tab3
        this.handlePublish(_id)
      } else if (e.toolbar.id == 'cancel') {
        // 取消 tab3
        this.cancelApproveById(_id)
      }
    },
    handleClickToolBarSlot(e) {
      let _selectRecords = e.grid.getSelectedRecords()
      if (_selectRecords.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _id = []
      _selectRecords.map((item) => _id.push(item.id))
      if (e.toolbar.id == 'create-rfx') {
        // 创建RFX tab1、tab2
        this.handleCreateRFX(e, _id)
      } else if (e.toolbar.id == 'transmit') {
        // 转发 -- tab1、tab2
        this.handleTransmit(_id)
      } else if (e.toolbar.id == 'cancel-claim') {
        // 取消认领 -- tab1、tab2
        this.handleCancelClaim(_id)
      } else if (e.toolbar.id == 'close') {
        // 关闭  tab1、tab2
        this.handleClose(_id)
      } else if (e.toolbar.id == 'cancel-close') {
        // 取消关闭 tab1、tab2
        this.handleCancelClose(_id)
      } else if (e.toolbar.id == 'require-group') {
        // 寻源分组建议
        let _filterStatus = _selectRecords.filter((e) => e.sourcingStatus === 0)
        if (_filterStatus.length < _selectRecords.length) {
          this.$toast({
            content: this.$t("勾选的数据中，存在'已处理'的单据"),
            type: 'warning'
          })
          return
        }
        this.handleShowSliderPanel(_id)
      }
    },
    // -----------------单元格按钮--------------------------
    handleClickCellTool(e) {
      if (e.tool.id == 'publish') {
        //发布  tab3
        this.handlePublish([e.data.id])
      } else if (e.tool.id == 'cancel') {
        //取消 tab3
        this.cancelApproveById([e.data.id])
      } else if (e.tool.id == 'Edit') {
        //编辑 tab3
        this.handleEdit(e.data)
      } else if (e.tool.id == 'delete') {
        //删除 tab3
        this.handleDelete([e.data.id])
      }
    },
    handleClickCellToolSlot(e) {
      if (e.tool.id == 'transmit') {
        //弹窗：转发
        this.handleTransmit([e.data.id])
      } else if (e.tool.id == 'cancel-claim') {
        //弹窗：取消认领
        this.handleCancelClaim([e.data.id])
      }
    },
    // -----------------单元格标题--------------------------
    handleClickCellTitle(e) {
      if (e.field == 'porCode') {
        //单据编码 tab1 tab2 tab3
        // if (e.data.approveStatus == 2) {
        //   this.$toast({ content: "审批通过，不支持编辑。", type: "warning" });
        //   return;
        // } else {
        this.$router.push({
          name: `require-detail`,
          query: {
            porId: e.data.id
          }
        })
        // }
      }
    },
    handleClickCellTitleSlot(e) {
      if (e.field == 'historyPriceNum') {
        //历史价格 tab2
        this.showHistoryPrice(e)
      } else if (e.field == 'porCode') {
        //单据编码 tab1 tab2 tab3
        // if (e.data.approveStatus == 2) {
        //   this.$toast({ content: "审批通过，不支持编辑。", type: "warning" });
        //   return;
        // } else {
        this.$router.push({
          name: `require-detail`,
          query: {
            porId: e.data.id
          }
        })
        // }
      }
    },

    // 弹窗：创建RFX
    handleCreateRFX(e, ids) {
      //  0:未创建 1:部分创建 2:已创建"
      if (e.grid.getSelectedRecords().some((item) => item.sourcingStatus == 2)) {
        this.$toast({
          type: 'warning',
          content: this.$t('请选择未创建过的单据')
        })
        return
      }
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/sor/mine/components/createSourceProject" */ './components/createSourceProject'
          ),
        data: {
          title: this.$t('创建RFX'),
          recContractList: this.recContractList,
          purchaseOrganizList: this.purchaseOrganizList,
          businessTypeList: this.businessTypeList,
          projStrategyList: this.projStrategyList,
          tenantIdList: this.tenantIdList,
          ids,
          requestUrl: this.currentSlotTabIndex == 0 ? 'saveRFXPor' : 'saveRFXDetailPor',
          idListName: this.currentSlotTabIndex == 0 ? 'porIdList' : 'porItemIdList'
        },
        success: () => {
          this.$refs[`templateRef0`].refreshCurrentGridData()
        }
      })
    },

    // 弹窗：转发
    handleTransmit(ids) {
      let _distributeArr = this.tenantUserList.map((item) => {
        let _email = item.email ? ' - ' + item.email : ''
        let _phoneNum = item.phoneNum ? ' - ' + item.phoneNum : ''
        return {
          text: item.employeeName + ' - ' + item.employeeCode + _email + _phoneNum,
          value: item.id
        }
      })
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/sor/mine/components/transmitRequirement" */ './components/transmitRequirement'
          ),
        data: {
          title: this.$t('转发'),
          ids,
          distributeArr: _distributeArr,

          requestUrl: this.currentSlotTabIndex == 0 ? 'forwardById' : 'forwardByIdDetail'
        },
        success: () => {
          this.$refs[`templateRef0`].refreshCurrentGridData()
        }
      })
    },

    // 弹窗：取消认领
    handleCancelClaim(ids) {
      let requestUrl = this.currentSlotTabIndex == 0 ? 'cancelClaimById' : 'cancelClaimByIdDetail'
      this.$dialog({
        data: {
          title: this.$t('取消认领'),
          message: this.$t('是否确认取消认领？'),
          ids,
          confirm: () => this.$API.porMine[requestUrl]({ idList: ids })
        },
        success: () => {
          this.$refs[`templateRef0`].refreshCurrentGridData()
        }
      })
    },

    // 顶部按钮：关闭
    handleClose(ids) {
      let requestUrl = this.currentSlotTabIndex == 0 ? 'closeById' : 'closeByIdDetail'
      this.$API.porMine[requestUrl]({ idList: ids }).then((res) => {
        if (res.code == 200) {
          this.$toast({ content: this.$t('关闭成功'), type: 'success' })
          this.$refs[`templateRef0`].refreshCurrentGridData()
        }
      })
    },

    // 顶部按钮：取消关闭
    handleCancelClose(ids) {
      let requestUrl = this.currentSlotTabIndex == 0 ? 'cancelCloseById' : 'cancelCloseByIdDetail'
      this.$API.porMine[requestUrl]({ idList: ids }).then((res) => {
        if (res.code == 200) {
          this.$toast({ content: this.$t('取消关闭成功'), type: 'success' })
          this.$refs[`templateRef0`].refreshCurrentGridData()
        }
      })
    },

    // 侧拉弹框：寻源分组建议
    handleShowSliderPanel(_ids) {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/sor/mine/components/sourceGrouping" */ './components/sourceGrouping.vue'
          ),
        data: {
          title: this.$t('寻源分组建议'),
          parentVm: this,
          porItemIdList: _ids,
          queryDataSource: this.queryDataSource,
          recContractList: this.recContractList, //推荐合同类型
          purchaseOrganizList: this.purchaseOrganizList, // 采购组织
          businessTypeList: this.businessTypeList //业务类型 - businessType
        },
        success: () => {
          this.$refs[`templateRef0`].refreshCurrentGridData()
        }
      })
    },

    // 弹窗：创建寻源项目
    handleCreateProj(ids) {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/sor/mine/components/createSourceProject" */ './components/createSourceProject'
          ),
        data: {
          title: this.$t('创建寻源项目'),
          requestUrl: '',
          recContractList: this.recContractList,
          purchaseOrganizList: this.purchaseOrganizList,
          businessTypeList: this.businessTypeList,
          projStrategyList: this.projStrategyList,
          tenantIdList: this.tenantIdList,
          ids
        },
        success: () => {
          this.$refs[`templateRef0`].refreshCurrentGridData()
        }
      })
    },

    // 弹窗：创建寻源需求
    handleCreateRequire() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/sor/mine/components/createRequirement" */ './components/createRequirement'
          ),
        data: {
          title: this.$t('创建寻源需求'),
          companyList: this.companyList,
          tenantIdList: this.tenantIdList,
          businessTypeList: this.businessTypeList,
          departmentList: this.departmentList
        },
        success: (data) => {
          if (Array.isArray(data) && data.length > 0) {
            this.$router.push({
              name: `require-detail`,
              query: {
                porId: data[0].id
              }
            })
          }
        }
      })
    },

    // 顶部按钮：发布
    handlePublish(ids) {
      this.$API.porMine.publishById({ idList: ids }).then((res) => {
        if (res.code == 200) {
          this.$toast({ content: this.$t('发布成功'), type: 'success' })
          this.$refs[`templateRef`].refreshCurrentGridData()
        }
      })
    },

    // 顶部按钮：取消
    cancelApproveById(ids) {
      this.$API.porMine.cancelApproveById({ idList: ids }).then((res) => {
        if (res.code == 200) {
          this.$toast({ content: this.$t('取消成功'), type: 'success' })
          this.$refs[`templateRef`].refreshCurrentGridData()
        }
      })
    },
    // 弹窗：编辑
    handleEdit(row) {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/sor/mine/components/createRequirement" */ './components/createRequirement'
          ),
        data: {
          title: this.$t('编辑寻源需求'),
          row,
          companyList: this.companyList,
          tenantIdList: this.tenantIdList,
          businessTypeList: this.businessTypeList,
          departmentList: this.departmentList
        },
        success: () => {
          this.$refs[`templateRef`].refreshCurrentGridData()
        }
      })
    },

    // 弹窗：删除
    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('确认删除？')
        },
        success: () => {
          this.$API.porMine.sourceDeleteById({ idList: ids }).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('删除成功'), type: 'success' })
              this.$refs[`templateRef`].refreshCurrentGridData()
            }
          })
        }
      })
    },

    getStrategyConfigCondition() {
      //获取规则下拉框字段列表
      this.$API.moduleConfig.getStrategyConfigCondition().then((res) => {
        let _res = res.data
        let _conditions = []
        for (let i in _res) {
          _conditions.push({
            field: i,
            headerText: _res[i],
            type: 'text'
          })
        }
        _conditions.forEach((e) => {
          if (e.field == 'requiredDeliveryDate' || e.field == 'applyTime') {
            e.type = 'date'
          }
        })
        this.queryDataSource = _conditions
        this.getMainData()
      })
    },
    getMainData() {
      // 推荐合同类型 - contractType
      this.$API.masterData
        .dictionaryGetList({
          dictCode: 'contractType'
        })
        .then((res) => {
          this.recContractList = res.data
        })

      // 采购组织
      // 数据源 purGroupName
      this.$API.masterData
        .purchaseOraginaze({
          organizationTypeCode: 'BUORG002ADM'
        })
        .then((res) => {
          this.purchaseOrganizList = res.data
          this.queryDataSource.forEach((e) => {
            if (e.field == 'purGroupName') {
              e.type = 'select'
              e.source = res.data
              // e.fields = { text: "organizationName", value: "id" };
              e.fields = {
                text: 'organizationName',
                value: 'organizationName'
              }
            }
          })
        })

      // 业务类型 - businessType
      // 数据源 businessTypeName
      this.$API.masterData
        .dictionaryGetList({
          dictCode: 'businessType'
        })
        .then((res) => {
          this.businessTypeList = res.data
          this.queryDataSource.forEach((e) => {
            if (e.field == 'businessTypeName') {
              e.type = 'select'
              e.source = res.data
              // e.fields = { text: "itemName", value: "id" };
              e.fields = { text: 'itemName', value: 'itemName' }
            }
          })
        })

      // 获取弹窗中的人员列表
      // 数据源 purName
      this.$API.masterData.getUserListByTenantId({ employeeName: '' }).then((res) => {
        this.tenantUserList = res.data
        this.tenantIdList = res.data
        this.queryDataSource.forEach((e) => {
          if (e.field == 'purName') {
            e.type = 'select'
            e.source = res.data
            // e.fields = { text: "employeeName", value: "id" };
            e.fields = { text: 'employeeName', value: 'employeeName' }
          }
        })
      })

      // 申请部门
      // 数据源 deptName
      this.$API.masterData
        .getDepartmentList({
          departmentName: ''
        })
        .then((res) => {
          this.departmentList = res.data
          this.queryDataSource.forEach((e) => {
            if (e.field == 'deptName') {
              e.type = 'select'
              e.source = res.data
              // e.fields = { text: "departmentName", value: "id" };
              e.fields = { text: 'departmentName', value: 'departmentName' }
            }
          })
        })

      // 供应商列表
      // 数据源 supplierName
      this.$API.masterData.getSupplierList().then((res) => {
        this.queryDataSource.forEach((e) => {
          if (e.field == 'supplierName') {
            e.type = 'select'
            e.source = res.data
            // e.fields = { text: "supplierName", value: "id" };
            e.fields = { text: 'supplierName', value: 'supplierName' }
          }
        })
      })

      // 品项列表
      // 数据源 itemCode itemName
      this.$API.masterData.getItemList().then((res) => {
        this.queryDataSource.forEach((e) => {
          if (e.field == 'itemName') {
            e.type = 'select'
            e.source = res.data
            // e.fields = { text: "itemName", value: "id" };
            e.fields = { text: 'itemName', value: 'itemName' }
          } else if (e.field == 'itemCode') {
            e.type = 'select'
            e.source = res.data
            e.fields = { text: 'itemCode', value: 'itemCode' }
          }
        })
      })

      // 品类列表
      // 数据源 getCategoryList
      this.$API.masterData.getCategoryList().then((res) => {
        this.categoryList = res.data
        this.queryDataSource.forEach((e) => {
          if (e.field == 'categoryName') {
            e.type = 'select'
            e.source = res.data
            // e.fields = { text: "categoryName", value: "id" };
            e.fields = { text: 'categoryName', value: 'categoryName' }
          }
        })
      })
      // 地点列表
      // 数据源 siteName
      this.$API.masterData.getSiteList().then((res) => {
        this.queryDataSource.forEach((e) => {
          if (e.field == 'siteName') {
            e.type = 'select'
            e.source = res.data.records
            // e.fields = { text: "siteName", value: "id" };
            e.fields = { text: 'siteName', value: 'siteName' }
          }
        })
      })

      // 库位、地点列表
      // 数据源 stockSite
      this.$API.masterData
        .getLocationList({
          page: {
            current: 1,
            size: 1000
          }
        })
        .then((res) => {
          this.queryDataSource.forEach((e) => {
            if (e.field == 'stockSite') {
              e.type = 'select'
              e.source = res.data.records
              // e.fields = { text: "locationName", value: "id" };
              e.fields = { text: 'locationName', value: 'locationName' }
            }
          })
        })

      // 项目策略 - policyType
      this.$API.masterData
        .dictionaryGetList({
          dictCode: 'policyType'
        })
        .then((res) => {
          this.projStrategyList = res.data
        })
      // 获取公司编码、公司名称
      this.$API.masterData
        .getCompanyList({
          companyCode: '',
          companyName: ''
        })
        .then((res) => {
          this.companyList = res.data
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.require-mine {
  min-width: 1100px;
}
</style>
