import { i18n } from '@/main.js'
import { ApproveStatusCodeMap } from '@/constants'
// 我的列表 - 主单
const claimListToolbar = [
  // {
  //   id: "create-order",
  //   icon: "icon_solid_Createorder",
  //   title: i18n.t("创建采购订单"),
  // },
  // {
  //   id: "create-proj",
  //   icon: "icon_solid_Createorder",
  //   title: i18n.t("创建寻源项目"),
  // },
  {
    id: 'create-rfx',
    icon: 'icon_solid_Createorder',
    title: i18n.t('创建RFX')
  },
  { id: 'transmit', icon: 'icon_solid_Share', title: i18n.t('转发') },
  {
    id: 'cancel-claim',
    icon: 'icon_solid_Cancel',
    title: i18n.t('取消认领')
  },
  { id: 'close', icon: 'icon_solid_Cancel', title: i18n.t('关闭') },
  {
    id: 'cancel-close',
    icon: 'icon_solid_Cancel',
    title: i18n.t('取消关闭')
  }
]
const claimListColumnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'porCode',
    headerText: i18n.t('单据编码'),
    cellTools: []
  },
  {
    field: 'porName',
    headerText: i18n.t('单据名称')
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'deptName',
    headerText: i18n.t('申请部门')
  },
  {
    field: 'purName',
    headerText: i18n.t('申请人')
  },
  {
    field: 'applyTime',
    headerText: i18n.t('申请时间'),
    type: 'date',
    format: 'yyyy-MM-dd HH:mm:ss'
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
  },
  {
    field: 'sourcingStatus',
    headerText: i18n.t('转化RFX状态'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('待处理'), 2: i18n.t('已处理') }
    }
  },
  {
    width: '200',
    field: 'sourceCode',
    headerText: i18n.t('源单据编码')
  },
  {
    field: 'sourceName',
    headerText: i18n.t('源单据名称')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    type: 'date',
    format: 'yyyy-MM-dd HH:mm:ss'
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

// 我的列表 - 明细 ====要动态
const claimDetailToolbar = [
  // {
  //   id: "create-order",
  //   icon: "icon_solid_Createorder",
  //   title: i18n.t("创建采购订单"),
  // },
  // {
  //   id: "create-proj",
  //   icon: "icon_solid_Createorder",
  //   title: i18n.t("创建寻源项目"),
  // },
  {
    id: 'create-rfx',
    icon: 'icon_solid_Createorder',
    title: i18n.t('创建RFX')
  },
  {
    id: 'require-group',
    icon: 'icon_solid_edit',
    title: i18n.t('需求分组建议')
  },
  { id: 'transmit', icon: 'icon_solid_Share', title: i18n.t('转发') },
  {
    id: 'cancel-claim',
    icon: 'icon_solid_Cancel',
    title: i18n.t('取消认领')
  },
  { id: 'close', icon: 'icon_solid_Cancel', title: i18n.t('关闭') },
  {
    id: 'cancel-close',
    icon: 'icon_solid_Cancel',
    title: i18n.t('取消关闭')
  }
]
const claimDetailColumnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'porCode',
    headerText: i18n.t('单据编码'),
    cellTools: []
  },
  {
    field: 'porName',
    headerText: i18n.t('单据名称')
  },
  {
    field: 'historyPriceNum',
    headerText: i18n.t('历史价格记录')
  },
  {
    field: 'claimStatus',
    headerText: i18n.t('认领状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未认领'),
        2: i18n.t('已认领')
      }
    }
  },
  {
    field: 'sourcingStatus',
    headerText: i18n.t('转化RFX状态'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('待处理'), 2: i18n.t('已处理') }
    }
  },
  {
    field: 'status',
    headerText: i18n.t('单据状态'),
    valueConverter: {
      type: 'map',
      map: {
        '-1': i18n.t('关闭'),
        0: i18n.t('草稿'),
        1: i18n.t('已提交'),
        2: i18n.t('发布'),
        8: i18n.t('已完成')
      }
    }
  },
  { field: 'lineNo', headerText: i18n.t('行号') },
  { field: 'itemCode', headerText: i18n.t('品项代码') },
  { field: 'itemName', headerText: i18n.t('品项名称') },
  { field: 'categoryName', headerText: i18n.t('品类') },
  {
    field: 'siteName',
    headerText: i18n.t('地点/工厂')
  },
  { field: 'lineNo', headerText: i18n.t('库存地点') },
  { field: 'quantity', headerText: i18n.t('数量') },
  { field: 'unitName', headerText: i18n.t('基本单位') },
  {
    field: 'requiredDeliveryDate',
    headerText: i18n.t('要求交货日期'),
    type: 'date',
    format: 'yyyy-MM-dd HH:mm:ss'
  },
  { field: 'purGroupName', headerText: i18n.t('采购组') },
  { field: 'itemName', headerText: i18n.t('业务类型') },
  {
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    width: '200',
    field: 'sourceHeaderCode',
    headerText: i18n.t('源单据编码')
  },
  {
    field: 'sourceHeaderName',
    headerText: i18n.t('源单据名称')
  },
  {
    field: 'deptName',
    headerText: i18n.t('申请部门')
  },
  {
    field: 'purName',
    headerText: i18n.t('申请人')
  },
  {
    field: 'applyTime',
    headerText: i18n.t('申请时间'),
    type: 'date',
    format: 'yyyy-MM-dd HH:mm:ss'
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

// 我创建的
const createListToolbar = [
  {
    id: 'create-require',
    icon: 'icon_solid_Createorder',
    title: i18n.t('创建寻源需求')
  },
  { id: 'publish', icon: 'icon_solid_Share', title: i18n.t('发布') }
  // { id: "cancel", icon: "icon_solid_Cancel", title: i18n.t("取消") },
]
const createListColumnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'porCode',
    headerText: i18n.t('单据编码'),
    cellTools: [
      {
        id: 'Edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data.status == 0
        }
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data.status == 0
        }
      }
    ]
  },
  {
    field: 'porName',
    headerText: i18n.t('单据名称')
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'deptName',
    headerText: i18n.t('申请部门')
  },
  {
    field: 'purName',
    headerText: i18n.t('申请人')
  },
  {
    field: 'applyTime',
    headerText: i18n.t('申请时间'),
    type: 'date',
    format: 'yyyy-MM-dd HH:mm:ss'
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
  },
  {
    field: 'sourcingStatus',
    headerText: i18n.t('转化RFX状态'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('待处理'), 2: i18n.t('已处理') }
    }
  },
  {
    field: 'status',
    headerText: i18n.t('单据状态'),
    valueConverter: {
      type: 'map',
      map: {
        '-1': i18n.t('关闭'),
        0: i18n.t('草稿'),
        1: i18n.t('已提交'),
        2: i18n.t('发布'),
        8: i18n.t('已完成')
      }
    },
    cellTools: [
      {
        id: 'publish',
        // icon: "icon_Share_2",
        title: i18n.t('发布'),
        visibleCondition: (data) => {
          return data.status == 0
        }
      },
      {
        id: 'cancel',
        // icon: "icon_Close_2",
        title: i18n.t('取消'), // 取消审批
        visibleCondition: (data) => {
          return data.status == 1
        }
      }
    ]
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    type: 'date',
    format: 'yyyy-MM-dd HH:mm:ss'
  },
  {
    field: 'approveStatus',
    headerText: i18n.t('审批状态'),
    valueConverter: {
      type: 'map',
      map: ApproveStatusCodeMap
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

// 待审批
const approvalListToolbar = []
const approvalListColumnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'porCode',
    headerText: i18n.t('单据编码'),
    cellTools: []
  },
  {
    field: 'porName',
    headerText: i18n.t('单据名称')
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'deptName',
    headerText: i18n.t('申请部门')
  },
  {
    field: 'purName',
    headerText: i18n.t('申请人')
  },
  {
    field: 'applyTime',
    headerText: i18n.t('申请时间'),
    type: 'date',
    format: 'yyyy-MM-dd HH:mm:ss'
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    vtype: 'date',
    format: 'yyyy-MM-dd HH:mm:ss'
  },
  {
    field: 'approveStatus',
    headerText: i18n.t('审批状态'),
    valueConverter: {
      type: 'map',
      map: ApproveStatusCodeMap
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

export const requireConfig = (headerUrl, detailUrl) => [
  {
    title: i18n.t('单据视图'),
    toolbar: claimListToolbar,
    grid: {
      allowFiltering: true,
      frozenColumns: 2,
      columnData: claimListColumnData,
      asyncConfig: {
        url: headerUrl
      }
    }
  },
  {
    title: i18n.t('明细视图'),
    toolbar: claimDetailToolbar,
    grid: {
      allowFiltering: true,
      frozenColumns: 2,
      columnData: claimDetailColumnData,
      asyncConfig: {
        url: detailUrl
      }
    }
  }
]
export const pageConfig = (myCreateUrl, noApproveUrl) => [
  { title: '我认领的' },
  {
    title: i18n.t('我创建的'),
    toolbar: createListToolbar,
    grid: {
      allowFiltering: true,
      columnData: createListColumnData,
      asyncConfig: {
        url: myCreateUrl
      }
    }
  },
  {
    title: i18n.t('待审核'),
    toolbar: approvalListToolbar,
    grid: {
      allowFiltering: true,
      frozenColumns: 2,
      columnData: approvalListColumnData,
      asyncConfig: {
        url: noApproveUrl
      }
    }
  }
]
