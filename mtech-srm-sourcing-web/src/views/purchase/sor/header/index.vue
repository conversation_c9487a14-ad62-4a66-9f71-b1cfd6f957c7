<template>
  <div class="full-height">
    <mt-template-page :template-config="pageConfig" @handleSelectTab="handleSelectTab">
      <mt-template-page
        slot="slot-0"
        ref="template-0"
        :template-config="mainConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
      />
      <mt-template-page
        slot="slot-1"
        ref="template-1"
        :template-config="detailConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitleDetail"
      />
    </mt-template-page>
  </div>
</template>

<script>
import { mainConfig, detailConfig } from './config'
import MixIn from '../config/mixin'
export default {
  mixins: [MixIn],
  data() {
    return {
      tenantUserList: [],
      pageConfig: [{ title: this.$t('需求汇总 - 主单') }, { title: this.$t('需求汇总 - 明细') }],
      mainConfig: mainConfig(this.$API.porHeader.getRequireSummary),
      detailConfig: detailConfig(this.$API.porHeader.getSummaryDetailList),
      currentTabIndex: 0
    }
  },

  mounted() {
    this.getUserListByTenantId() // 获取人员列表
  },

  methods: {
    // 获取弹窗中的人员列表
    getUserListByTenantId() {
      this.$API.masterData
        .getUserListByTenantId({
          employeeName: ''
        })
        .then((res) => {
          this.tenantUserList = res.data
        })
    },
    // -----------------顶部按钮--------------------------
    handleClickToolBar(e) {
      let _selectGridRecords = e.grid.getSelectedRecords()
      console.log('use-handleClickToolBar', e, _selectGridRecords)
      if (_selectGridRecords.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      // 已认领的不能点击 分配、认领按钮
      let _claimStatus = [],
        _id = []

      _selectGridRecords.map((item) => {
        _id.push(item.id), _claimStatus.push(item.claimStatus)
      })
      if (e.toolbar.id == 'Claim') {
        this.handleClaim(_id, _claimStatus)
      } else if (e.toolbar.id == 'Distribute') {
        this.handleDistribute(_id, _claimStatus)
      }
    },

    // 弹窗：认领点击
    handleClaim(ids, claimStatus) {
      if (claimStatus && claimStatus.length > 0 && claimStatus.some((item) => item == 2)) {
        this.$toast({
          content: this.$t('已认领的需求无法进行该操作'),
          type: 'warning'
        })
        return
      }
      let requestUrl = this.currentTabIndex == 0 ? 'claimById' : 'claimByIdDetail'
      this.$dialog({
        data: {
          title: this.$t('认领'),
          message: this.$t('是否确认认领？'),
          confirm: () => this.$API.porHeader[requestUrl]({ idList: ids })
        },
        success: () => {
          this.$refs[`template-${this.currentTabIndex}`].refreshCurrentGridData()
        }
      })
    },

    // 弹窗：分配点击
    handleDistribute(ids) {
      let _distributeArr = this.tenantUserList.map((item) => {
        let _email = item.email ? ' - ' + item.email : ''
        let _phoneNum = item.phoneNum ? ' - ' + item.phoneNum : ''
        return {
          text: item.employeeName + ' - ' + item.employeeCode + _email + _phoneNum,
          // value: item.id,
          value: item.userId //调整取值逻辑，取值userId
        }
      })
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/sor/header/components/distributeRequirement" */ './components/distributeRequirement.vue'
          ),
        data: {
          title: this.$t('分配'),
          distributeArr: _distributeArr,
          requestUrl: this.currentTabIndex == 0 ? 'distributeById' : 'distributeByIdDetail',
          ids
        },
        success: () => {
          this.$refs[`template-${this.currentTabIndex}`].refreshCurrentGridData()
        }
      })
    },

    // -----------------单元格--------------------------
    // 已更换方案
    handleClickCellTool(e) {
      if (e.tool.id == 'Claim') {
        this.handleClaim([e.data.id])
      } else if (e.tool.id == 'Distribute') {
        this.handleDistribute([e.data.id])
      }
    },

    // 单元格title
    handleClickCellTitle(e) {
      if (e.field == 'historyPriceNum') {
        this.showHistoryPrice(e)
      } else if (e.field == 'porCode') {
        // if (e.data.approveStatus == 2) {
        //   this.$toast({ content: "审批通过，不支持编辑。", type: "warning" });
        //   return;
        // } else {
        this.$router.push({
          name: `require-detail`,
          query: {
            porId: e.data.id
          }
        })
        // }
      }
    },
    // 单元格title
    handleClickCellTitleDetail(e) {
      if (e.field == 'historyPriceNum') {
        this.showHistoryPrice(e)
      } else if (e.field == 'porCode') {
        // if (e.data.approveStatus == 2) {
        //   this.$toast({ content: "审批通过，不支持编辑。", type: "warning" });
        //   return;
        // } else {
        this.$router.push({
          name: `require-detail`,
          query: {
            porId: e.data.porId
          }
        })
        // }
      }
    },
    // -----------------tab页签--------------------------
    handleSelectTab(e) {
      this.currentTabIndex = e
    }
  }
}
</script>
