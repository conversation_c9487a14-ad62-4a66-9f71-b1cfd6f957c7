import { i18n } from '@/main.js'
// 主单
const columnDataMain = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'porCode',
    headerText: i18n.t('单据编码'),
    cellTools: [
      // { id: "Claim", icon: "icon_solid_Submit", title: "认领" },
      // { id: "Distribute", icon: "icon_solid_export", title: "分配" },
    ]
  },
  {
    field: 'porName',
    headerText: i18n.t('单据名称')
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'deptName',
    headerText: i18n.t('申请部门')
  },
  {
    field: 'purName',
    headerText: i18n.t('申请人')
  },
  {
    field: 'applyTime',
    headerText: i18n.t('申请时间'),
    type: 'date',
    format: 'yyyy-MM-dd HH:mm:ss'
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
  },
  {
    field: 'sourcingStatus',
    headerText: i18n.t('转化RFX状态'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('待处理'), 2: i18n.t('已处理') }
    }
  },
  {
    field: 'claimStatus',
    headerText: i18n.t('状态'),
    ignore: true,
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('未认领'), 1: i18n.t('部分认领'), 2: i18n.t('已认领') }
    }
  },
  {
    field: 'claimValue',
    headerText: i18n.t('认领进度')
  },
  {
    width: '200',
    field: 'sourceCode',
    headerText: i18n.t('源单据编码'),
    cssClass: 'field-content'
  },
  {
    field: 'sourceName',
    headerText: i18n.t('源单据名称')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    format: 'yyyy-MM-dd HH:mm:ss',
    type: 'date'
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
export const mainConfig = (url) => [
  {
    title: i18n.t('未认领'),
    toolbar: [
      { id: 'Claim', icon: 'icon_solid_Submit', title: i18n.t('认领') },
      { id: 'Distribute', icon: 'icon_solid_export', title: i18n.t('分配') }
    ],
    grid: {
      allowFiltering: true,
      frozenColumns: 2,
      columnData: columnDataMain,
      asyncConfig: {
        url,
        defaultRules: [
          {
            label: i18n.t('状态'),
            field: 'claimStatus',
            type: 'number',
            operator: 'notequal',
            value: '2'
          }
        ]
      }
    }
  },
  {
    title: i18n.t('已认领'),
    toolbar: [
      {
        id: 'Claim',
        icon: 'icon_solid_Submit',
        title: i18n.t('认领'),
        visibleCondition: () => false
      },
      {
        id: 'Distribute',
        icon: 'icon_solid_export',
        title: i18n.t('分配'),
        visibleCondition: () => false
      }
    ],
    grid: {
      allowFiltering: true,
      frozenColumns: 2,
      columnData: columnDataMain,
      asyncConfig: {
        url,
        defaultRules: [
          {
            label: i18n.t('状态'),
            field: 'claimStatus',
            type: 'number',
            operator: 'equal',
            value: '2'
          }
        ]
      }
    }
  }
]

// 明细
const columnDataDetail = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'porCode',
    headerText: i18n.t('单据编码'),
    cellTools: [
      // { id: "Claim", icon: "icon_solid_Submit", title: "认领" },
      // { id: "Distribute", icon: "icon_solid_export", title: "分配" },
    ]
  },
  {
    field: 'porName',
    headerText: i18n.t('单据名称')
  },
  {
    field: 'historyPriceNum',
    headerText: i18n.t('历史价格记录')
  },
  {
    field: 'sourcingStatus',
    headerText: i18n.t('转化RFX状态'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('待处理'), 2: i18n.t('已处理') }
    }
  },
  {
    field: 'claimStatus',
    headerText: i18n.t('认领状态'),
    ignore: true,
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未认领'),
        2: i18n.t('已认领')
      }
    }
  },
  {
    field: 'status',
    headerText: i18n.t('单据状态'),
    valueConverter: {
      type: 'map',
      map: {
        '-1': i18n.t('关闭'),
        0: i18n.t('草稿'),
        1: i18n.t('已提交'),
        2: i18n.t('发布'),
        8: i18n.t('已完成')
      }
    }
  },
  { field: 'lineNo', headerText: i18n.t('行号') },
  { field: 'itemCode', headerText: i18n.t('品项代码') },
  { field: 'itemName', headerText: i18n.t('品项名称') },
  { field: 'categoryName', headerText: i18n.t('品类') },
  {
    field: 'siteName',
    headerText: i18n.t('地点/工厂')
  },
  { field: 'lineNo', headerText: i18n.t('库存地点') },
  { field: 'quantity', headerText: i18n.t('数量') },
  { field: 'unitName', headerText: i18n.t('基本单位') },
  {
    field: 'requiredDeliveryDate',
    headerText: i18n.t('要求交货日期'),
    type: 'date',
    format: 'yyyy-MM-dd HH:mm:ss'
  },
  { field: 'purGroupName', headerText: i18n.t('采购组') },
  { field: 'itemName', headerText: i18n.t('业务类型') },
  {
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'sourceHeaderCode',
    headerText: i18n.t('源单据编码')
  },
  {
    field: 'sourceHeaderName',
    headerText: i18n.t('源单据名称')
  },
  {
    field: 'deptName',
    headerText: i18n.t('申请部门')
  },
  {
    field: 'purName',
    headerText: i18n.t('申请人')
  },
  {
    field: 'applyTime',
    headerText: i18n.t('申请时间'),
    type: 'date',
    format: 'yyyy-MM-dd HH:mm:ss'
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

export const detailConfig = (url) => [
  {
    title: i18n.t('未认领'),
    toolbar: [
      { id: 'Claim', icon: 'icon_solid_Submit', title: i18n.t('认领') },
      { id: 'Distribute', icon: 'icon_solid_export', title: i18n.t('分配') }
    ],
    grid: {
      allowFiltering: true,
      frozenColumns: 2,
      columnData: columnDataDetail,
      asyncConfig: {
        url,
        defaultRules: [
          {
            label: i18n.t('状态'),
            field: 'claimStatus',
            type: 'number',
            operator: 'notequal',
            value: '2'
          }
        ]
      }
    }
  },
  {
    title: i18n.t('已认领'),
    toolbar: [
      {
        id: 'Claim',
        icon: 'icon_solid_Submit',
        title: i18n.t('认领'),
        visibleCondition: () => false
      },
      {
        id: 'Distribute',
        icon: 'icon_solid_export',
        title: i18n.t('分配'),
        visibleCondition: () => false
      }
    ],
    grid: {
      allowFiltering: true,
      frozenColumns: 2,
      columnData: columnDataDetail,
      asyncConfig: {
        url,
        defaultRules: [
          {
            label: i18n.t('状态'),
            field: 'claimStatus',
            type: 'number',
            operator: 'equal',
            value: '2'
          }
        ]
      }
    }
  }
]
