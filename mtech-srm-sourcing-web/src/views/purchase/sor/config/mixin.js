import { i18n } from '@/main.js'
export default {
  methods: {
    // 弹窗：点击了单元格：历史价格
    showHistoryPrice(e) {
      if (e.data.historyPriceNum <= 0) {
        this.$toast({
          content: i18n.t('当前暂无历史价格记录'),
          type: 'warning'
        })
        return
      }
      this.$API.masterData
        .getHistoryPrice({
          itemCode: e.data.itemCode
        })
        .then((res) => {
          this.$dialog({
            modal: () =>
              import(
                /* webpackChunkName: "router/purchase/sor/components/history-price" */ '../components/historyPrice/index.vue'
              ),
            data: {
              title: i18n.t('历史价格记录'),
              rowData: e.data,
              tableData: res.data
            }
          })
        })
    }
  }
}
