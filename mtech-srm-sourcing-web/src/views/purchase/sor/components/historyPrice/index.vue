// 历史价格
<template>
  <mt-dialog
    ref="dialog"
    css-class="history-price-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <ul class="pin-box">
        <li class="one-pin">{{ $t('品项代码：') }}{{ propsData.rowData.itemCode }}</li>
        <li class="one-pin">{{ $t('品项名称：') }}{{ propsData.rowData.itemName }}</li>
        <li class="one-pin">{{ $t('品类：') }}{{ propsData.rowData.categoryName }}</li>
      </ul>

      <mt-data-grid
        :data-source="sampleData"
        :column-data="columnData"
        ref="dataGrid"
        locale="zh"
      ></mt-data-grid>
    </div>
  </mt-dialog>
</template>

<script>
import { historyPriceColumnData } from './config'
export default {
  data() {
    return {
      columnData: historyPriceColumnData,
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        width: '500px',
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    sampleData() {
      return this.modalData.tableData || []
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    confirm() {
      this.$emit('confirm-function')
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
