import { i18n } from '@/main.js'
// 历史价格弹窗
export const historyPriceColumnData = [
  {
    width: '200',
    field: 'time',
    headerText: i18n.t('价格时间'),
    valueConverter: {
      type: 'date',
      format: 'yyyy-MM-dd hh:mm:ss'
    }
  },
  {
    field: 'price',
    headerText: i18n.t('价格')
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商')
  }
]
