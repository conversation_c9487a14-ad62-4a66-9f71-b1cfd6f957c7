import { i18n } from '@/main.js'
export const detailColumnFields = [
  // {

  //   field: "sourceCode",
  //   headerText: i18n.t("源单据编码"),
  // },
  // {

  //   field: "sourceName",
  //   headerText: i18n.t("源单据名称"),
  // },
  {
    field: 'porCode',
    headerText: i18n.t('单据编码')
  },
  {
    field: 'porName',
    headerText: i18n.t('单据名称')
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'deptName',
    headerText: i18n.t('申请部门')
  },
  {
    field: 'purName',
    headerText: i18n.t('申请人')
  },
  {
    field: 'applyTime',
    headerText: i18n.t('申请时间')
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
  },
  {
    field: 'claimStatus',
    headerText: i18n.t('状态')
  },
  // {

  //   field: "createUserName",
  //   headerText: i18n.t("创建人"),
  // },
  // {

  //   field: "createTime",
  //   headerText: i18n.t("创建时间"),
  // },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
export const detailToolbar = ['Add', 'Delete']
export const detailColumnData = [
  {
    field: 'lineNo',
    headerText: i18n.t('需求明细.....')
  }
]
export const supplierToolbar = ['Add', 'Delete']
export const supplierColumnData = [
  {
    field: 'lineNo',
    headerText: i18n.t('需求供方...')
  }
]
export const filesToolbar = []
export const filesColumnData = [
  {
    field: 'lineNo',
    headerText: i18n.t('相关文件...')
  }
]
export const editorToolbar = {
  useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
  tools: [[{ id: 'Save', icon: 'icon_solid_Save', title: i18n.t('保存') }]]
}
