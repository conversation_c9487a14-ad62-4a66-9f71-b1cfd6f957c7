// 审核弹窗
<template>
  <mt-dialog
    ref="dialog"
    css-class="small-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content dialog-approval">
      <ul class="approval-res">
        <li
          v-for="(item, index) in approvalResList"
          :key="index"
          :class="[activeIndex == index && `active-li-${index}`]"
          @click="activeIndex = index"
        >
          {{ item }}
        </li>
      </ul>

      <div class="textarea-box">
        <div class="title-box">{{ $t('审批备注') }}</div>
        <mt-input
          :multiline="true"
          css-class="e-outline remarks"
          float-label-type="Never"
          :rows="4"
          type="text"
          :placeholder="$t('请输入审批备注')"
        ></mt-input>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      selectedUser: -1,
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      approvalResList: [this.$t('通过'), this.$t('驳回'), this.$t('关闭'), this.$t('控制')],
      activeIndex: 0
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        width: '500px',
        buttons: this.buttons,
        cssClass: ('small-dialog ' + this.modalData.cssClass).trim()
      }
    },
    header() {
      // let _smallHeader =
      //   '<div class="header-text" id="_title" style="margin-left: 0">' +
      //   this.modalData.title +
      //   "</div>";
      // return _smallHeader;
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    confirm() {
      if (this.selectedUser <= -1) {
        this.$toast({ type: 'warning', content: this.$t('请选择转发的人员') })
        return
      }
      this.$API.porMine[this.propsData.requestUrl]({
        idList: this.propsData.ids,
        purId: this.selectedUser,
        purName: ''
      }).then(() => {
        this.$emit('confirm-function')
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss">
.dialog-content.dialog-approval {
  .approval-res {
    display: flex;
    justify-content: space-between;
    margin: 20px 0;
    li {
      font-size: 16px;
      line-height: 1;
      padding: 12px 34px;
      cursor: pointer;
      border-radius: 4px;
      color: #9a9a9a;
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(232, 232, 232, 1);

      &.active-li-0 {
        color: #8acc40;
        background: linear-gradient(rgba(138, 204, 64, 0.06), rgba(138, 204, 64, 0.06)),
          linear-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 1));
        border: 2px solid rgba(138, 204, 64, 1);
        box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.06);
      }
      &.active-li-1 {
        color: #ed5633;
        background: rgba(254, 245, 243, 1);
        border: 2px solid rgba(237, 86, 51, 1);
        box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.06);
      }
      &.active-li-2 {
        color: #9a9a9a;
        background: rgba(249, 249, 249, 1);
        border: 2px solid rgba(154, 154, 154, 1);
        box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.06);
      }
      &.active-li-3 {
        color: #eda133;
        background: rgba(254, 250, 243, 1);
        border: 2px solid rgba(237, 161, 51, 1);
        box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.06);
      }
    }
  }

  .textarea-box {
    .title-box {
      font-size: 14px;
      line-height: 1;
      color: #292929;
      font-weight: 500;
      padding-left: 7px;
      margin-bottom: 20px;
      position: relative;
      &::before {
        content: '';
        width: 3px;
        height: 12px;
        background: rgba(237, 161, 51, 1);
        border-radius: 2px 0 0 2px;
        position: absolute;
        left: 0;
        top: 1px;
      }
    }

    .mt-input .remarks {
      &.e-input-group.e-control-wrapper.e-multi-line-input.e-outline {
        border-color: rgba(232, 232, 232, 1);
        background: rgba(250, 250, 250, 1);
        textarea {
          padding: 20px;
          margin: 0;

          input::-webkit-input-placeholder {
            /*WebKit browsers*/
            color: #9a9a9a;
          }

          input::-moz-input-placeholder {
            /*Mozilla Firefox*/
            color: #9a9a9a;
          }

          input::-ms-input-placeholder {
            /*Internet Explorer*/
            color: #9a9a9a;
          }
        }
      }
    }
  }
}
</style>
