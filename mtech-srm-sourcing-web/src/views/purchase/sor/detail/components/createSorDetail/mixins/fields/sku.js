export default {
  methods: {
    // SKU 字段： skuName
    handleSKUGroupField(_column, i) {
      this.$API.masterData.getSKUList().then((r) => {
        _column.type = 'select'
        _column.fields = {
          text: 'name',
          value: 'name'
        }
        let res = { data: r.data.records }
        this.handleGroupCascadeFields(_column, res)
        _column.source = res.data
        this.$nextTick(() => {
          this.$set(this.columnData, i, _column)
        })
      })
    }
  }
}
