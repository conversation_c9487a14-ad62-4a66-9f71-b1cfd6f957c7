export default {
  methods: {
    // 业务类型  字段： businessTypeName
    handleBusinessTypeNameField(_column, i) {
      this.$API.masterData
        .dictionaryGetList({
          dictCode: 'businessType'
        })
        .then((res) => {
          _column.type = 'select'
          _column.fields = { text: 'itemName', value: 'id' }
          _column.source = res.data
          this.$nextTick(() => {
            this.$set(this.columnData, i, _column)
          })
        })
    },
    // 业务类型  字段： businessTypeName
    handleBusinessTypeGroupField(_column, i) {
      this.$API.masterData
        .dictionaryGetList({
          dictCode: 'businessType'
        })
        .then((res) => {
          _column.type = 'select'
          _column.fields = { text: 'itemCode', value: 'itemCode' }
          this.handleGroupCascadeFields(_column, res)
          _column.source = res.data
          this.$nextTick(() => {
            this.$set(this.columnData, i, _column)
          })
        })
    }
  }
}
