export default {
  methods: {
    // 品项列表 字段： itemCode
    handleItemCodeField(_column, i) {
      this.$API.masterData.getItemList().then((res) => {
        _column.type = 'select'
        _column.fields = { text: 'itemCode', value: 'itemCode' }
        _column.source = res.data
        this.$nextTick(() => {
          this.$set(this.columnData, i, _column)
        })
      })
    },
    // 品项列表 字段： itemCode
    handleItemGroupField(_column, i) {
      this.$API.masterData.getItemList().then((res) => {
        _column.type = 'select'
        _column.fields = { text: 'itemCode', value: 'itemCode' }
        //逻辑需求：(新逻辑：品类是品项带出来的，不能独立输入)，选择品项，带出品类信息
        res.data.forEach((e) => {
          if (e?.categoryResponse) {
            e.categoryName = e?.categoryResponse?.categoryName ?? '未维护'
            e.categoryCode = e?.categoryResponse?.categoryCode ?? '未维护'
            e.categoryId = e?.categoryResponse?.id ?? 0
          } else {
            e.categoryName = '未维护'
            e.categoryCode = '未维护'
            e.categoryId = 0
          }
        })
        this.handleGroupCascadeFields(_column, res)
        _column.source = res.data
        this.$nextTick(() => {
          this.$set(this.columnData, i, _column)
        })
      })
    }
  }
}
