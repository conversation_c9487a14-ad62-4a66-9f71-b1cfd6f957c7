export default {
  methods: {
    // //地点列表  字段： siteName
    // handleSiteNameField(_column, i) {
    //   this.$API.masterData.getSiteList().then((res) => {
    //     _column.type = "select";
    //     _column.fields = { text: "siteName", value: "siteName" };
    //     _column.source = res.data.records;
    //     this.$nextTick(() => {
    //       this.$set(this.columnData, i, _column);
    //     });
    //   });
    // },
    // 地点/工厂 字段： siteName
    handleSiteGroupField(_column, i) {
      this.$API.masterData.getSiteList().then((r) => {
        _column.type = 'select'
        _column.fields = {
          text: 'siteName',
          value: 'siteName'
        }
        let res = { data: r.data.records }
        this.handleGroupCascadeFields(_column, res)
        _column.source = res.data
        this.$nextTick(() => {
          this.$set(this.columnData, i, _column)
        })
      })
    }
  }
}
