import { i18n } from '@/main.js'
let _mixins = []
const fieldsMixin = require.context('./fields', true, /\.js$/)
fieldsMixin.keys().forEach((f) => {
  const config = fieldsMixin(f)
  _mixins.push(config.default)
})
export default {
  mixins: _mixins,
  data() {
    return {
      cancatFieldsRules: {
        // 采购周期起
        expectBidPeriodStart: [
          {
            validator: this.validExpectBidPeriodStart,
            trigger: 'blur'
          }
        ],
        // 采购周期止
        expectBidPeriodEnd: [
          {
            validator: this.validExpectBidPeriodEnd,
            trigger: 'blur'
          }
        ],
        // 项目开始时间
        projectStartTime: [
          {
            validator: this.validProjectStartTime,
            trigger: 'blur'
          }
        ],
        // 项目结束时间
        projectEndTime: [
          {
            validator: this.validProjectEndTime,
            trigger: 'blur'
          }
        ]
      }
    }
  },
  methods: {
    //切换form分组收起/展开标记
    toggleFromGroup(i) {
      this.$set(this.formGroupArray[i], 'open', !this.formGroupArray[i]['open'])
    },
    //序列化表单，分组。界面显示
    serializeFieldsGroup(fields) {
      let _map = {},
        _array = []
      for (let i = 0; i < fields.length; i++) {
        let _group = fields[i]['fieldGroup']
        if (_map && Object.prototype.hasOwnProperty.call(_map, _group)) {
          _map[_group].count++
          _map[_group].data.push(fields[i])
        } else {
          _array.push(_group)
          _map[_group] = {
            group: _group,
            count: 1,
            data: [fields[i]]
          }
        }
      }
      let _group = [],
        formGroup = []
      Object.keys(_map).forEach((key) => {
        if (Array.isArray(_map[key]?.data) && _map[key]?.data.length) {
          _group.push({
            group: key || i18n.t('未分组'),
            fields: _map[key]?.data
          })
          formGroup.push({
            open: true
          })
        }
      })
      this.formGroupArray = formGroup
      return _group
    },
    //下拉框数据变化时的操作,如果配置了group，将根据group中配置的字段，依次赋值
    handleSelectDataSource(e) {
      console.log(e)
      let _data = e.itemData
      if (_data && _data.columnGroupTag) {
        //处理联动数据
        let _tag = _data.columnGroupTag
        let _findGroup = this.groupFields.filter((e) => {
          return e.group == _tag
        })
        if (_findGroup.length > 0) {
          let _maps = _findGroup[0]['fieldsMap']
          this.$forceUpdate()
          Object.keys(_maps).forEach((key) => {
            //遍历fieldsMap中所有字段，并赋值Form
            let _item = _maps[key]
            this.$set(this.customFormObject, _item.formField, _data[_item.sourceField])
          })
        }
      }
      // let _data = e.previousItemData;
      if (_data?.columnGroupTag == 'supplier') {
        //执行接口 赋值供应商需要的字段 查一下是什么字段
        this.$API.masterData
          .getItemFindByCode({
            supplierCode: _data.supplierCode
          })
          .then((res) => {
            // this.customFormObject.supplierCode = res.data.supplierCode;
            // this.customFormObject.supplierName = res.data.supplierName;
            // this.customFormObject.supplierLinkUser = "supplierLinkUser";
            // this.customFormObject.supplierPhone = res.data.contactPhone;
            // this.customFormObject.supplierEmail = res.data.contactEmail;
            this.$set(this.customFormObject, 'supplierLinkUser')
            this.customFormObject.supplierLinkUser = res.data.contactPerson
            this.$set(this.customFormObject, 'supplierPhone')
            this.customFormObject.supplierPhone = res.data.contactPhone
            this.$set(this.customFormObject, 'supplierEmail')
            this.customFormObject.supplierEmail = res.data.contactEmail
          })
        console.log(this.customFormObject)
      }
    },
    //校验当前字段，是否是联动字段，比如itemCode、itemName、itemId等
    checkIsGroupFields(_field) {
      let _groups = this.groupFields
      let res = { group: null, isGroup: false }
      for (let i in _groups) {
        let _group = _groups[i]
        let _fields = _group['fieldList']
        if (_fields.indexOf(_field) > -1) {
          //在Group列表汇总匹配
          if (_group['serialized']) {
            //已匹配过其他字段，已经使用过这个Group
            res = { isGroup: true, group: _group, isUsed: true, field: _field }
            break
          } else {
            // _fields.forEach((e) => {
            //   //遍历，将Group中数据依次放入FormObject中
            //   this.customFormObject[e] = null;
            // });
            //添加使用标记
            _group['serialized'] = true
            res = {
              isGroup: true,
              group: _group,
              isUsed: false,
              field: _field
            }
            break
          }
        }
      }
      return res
    },
    //序列化ColumnData，序列化条件：是否联动
    serializeColumnDataByGroupFields(_columns) {
      let _res = []
      _columns.forEach((e) => {
        let _column = {
          field: e.field,
          headerText: e.headerText,
          fieldGroup: e.fieldGroup,
          required: e.required
        }
        let _checkResult = this.checkIsGroupFields(_column.field)
        if (_checkResult.isGroup) {
          //处理需要联动的字段Group
          if (!_checkResult.isUsed) {
            //未使用过的前提下，进行操作
            let _group = _checkResult.group
            //添加需要联动的下拉框Field,如itemCode。下拉框选择Code，其他值均为带入数据
            let _maps = _group.fieldsMap
            Object.keys(_maps).forEach((key) => {
              let _item = _maps[key]
              if (_item.isCascadeField) {
                //如果是级联标记的元素，添加group标记。属于下拉框数据，其他字段均从这个字段选择带出。
                _res.push({
                  field: _item.formField,
                  headerText: _item.text,
                  group: _group.group,
                  groupField: _checkResult.field
                })
              } else if (_item.visible) {
                //如果是设置为visible的字段，则渲染到Form中，仅做展示，不支持编辑
                _res.push({
                  field: _item.formField,
                  headerText: _item.text,
                  groupField: _checkResult.field,
                  type: 'disabled'
                })
              }
            })
          }
        } else {
          //普通字段
          _res.push(_column)
        }
      })
      _res.forEach((i) => {
        if (i.groupField) {
          _columns.forEach((j) => {
            if (i.groupField === j.field) {
              i.fieldGroup = j.fieldGroup
            }
          })
        }
      })
      return _res
    },
    //处理ColumnData中每个字段的原数据，主要是select类型
    serializeColumnDataSourceList() {
      let _res = this.columnData
      _res.forEach((_column, index) => {
        if (_column.group) {
          this.serializeGroupDataSource(_column, index)
        } else {
          this.serializeCommonDataSource(_column, index)
        }
      })
    },
    //序列化联动字段的数据源
    serializeGroupDataSource(_column, index) {
      switch (_column['group']) {
        case 'supplier': //供应商
          this.handleSupplierGroupField(_column, index)
          break
        case 'item': //品项
          this.handleItemGroupField(_column, index)
          break
        case 'businessType': //业务类型
          this.handleBusinessTypeGroupField(_column, index)
          break
        case 'dept': //部门
          this.handleDepartmentGroupField(_column, index)
          break
        case 'site': //地点/工厂
          this.handleSiteGroupField(_column, index)
          break
        case 'sku': //SKU
          this.handleSKUGroupField(_column, index)
          break
        case 'rate': //税率
          this.handleRateGroupField(_column, index)
          break
        case 'purGroup': //采购组
          this.handlePurGroupField(_column, index)
          break
        case 'currency': //物料信息-币种
        case 'budgetCurrency': //需求信息-币种
        case 'referenceCurrency': //参考信息-币种
          this.handleCurrencyGroupField(_column, index)
          break
      }
    },
    //序列化普通字段的数据源
    serializeCommonDataSource(_column, i) {
      if (_column.type == 'disabled') return
      let _field = _column['field']
      _column['type'] = 'text'
      if (_field === 'purGroupName') {
        //采购组织
        this.handlePurGroupNameField(_column, i)
      }
      if (_field === 'purName') {
        //人员列表
        this.handlePurNameField(_column, i)
      }
      // if (
      //   _field === "currencyName" ||
      //   _field === "budgetCurrency" ||
      //   _field === "referenceCurrency"
      // ) {
      //   //货币币种
      //   this.handleCurrencyNameField(_column, i);
      // }
      if (_field === 'unitName' || _field === 'bidUnitName') {
        //基本单位  采购单位
        this.handleUnitNameField(_column, i)
      }
      if (_field === 'location') {
        //库位、地点
        this.handleLocationField(_column, i)
      }
      if (_field === 'tradeTerms') {
        //贸易条款
        this.handleTradeTermsField(_column, i)
      }
      if (_field === 'logisticsWay') {
        //物流方式
        this.handleLogisticsWayField(_column, i)
      }
      if (this.booleanFieldList.indexOf(_field) > -1) {
        //是否抵扣 reduction
        //是否独家 sole
        this.handleBooleanMapField(_column, i)
      }
      //处理日期类型字段
      if (this.dateTimeFieldList.indexOf(_field) > -1) {
        _column['type'] = 'date'
      }
      //处理数字类型字段
      if (this.numberFieldList.indexOf(_field) > -1) {
        _column['type'] = 'number'
      }
      //处理只读类型字段
      if (this.disabledFieldList.indexOf(_field) > -1) {
        _column['type'] = 'disabled'
      }
      // if (_field === "budgetRate") {
      //   //税目税率
      //   this.handleBudgetRateField(_column, i);
      // }
      // if (_field === "businessTypeName") {
      //   //业务类型
      //   this.handleBusinessTypeNameField(_column, i);
      // }
      // if (_field === "deptName") {
      //   //申请部门
      //   this.handleDepartmentField(_column, i);
      // }
      // if (_field === "supplierName") {
      //   //供应商
      //   this.handleSupplierNameField(_column, i);
      // }
      // if (_field === "itemName") {
      //   //品项名称
      //   this.handleItemNameField(_column, i);
      // }
      // if (_field === "itemCode") {
      //   //品项编码
      //   this.handleItemCodeField(_column, i);
      // }
      // if (_field === "categoryName") {
      //   //品类名称
      //   this.handleCategoryNameField(_column, i);
      // }
      // if (_field === "siteName") {
      //   //地点/工厂
      //   this.handleSiteNameField(_column, i);
      // }
    },
    //序列化弹框字段--表单校验规则
    serializeFormRules() {
      let _rules = {}
      //先设置module中返回的规则校验
      for (let i in this.columnData) {
        let _column = this.columnData[i]
        let _field = _column['field']
        let _text = _column['headerText']
        //指定的字段做必填校验
        let _find = this.requiredFields.filter((e) => {
          return e.field == _field
        })
        if (_find.length > 0) {
          _rules[_field] = [
            {
              required: true,
              message: `${_text}为必填项.`,
              trigger: 'blur'
            }
          ]
        }
      }
      //追加定制的规则校验
      Object.keys(this.fieldsRules).forEach((key) => {
        if (key && Array.isArray(_rules[key])) {
          _rules[key] = _rules[key].concat(this.fieldsRules[key])
        } else {
          _rules[key] = this.fieldsRules[key]
        }
      })
      this.$nextTick(() => {
        this.$set(this, 'formRules', _rules)
      })
    },
    //处理Group中，下拉列表的fields
    handleGroupCascadeFields(_column, res) {
      if (_column.group) {
        //如果是级联联动数据，每条数据中依次添加标记
        res.data.forEach((e) => {
          e['columnGroupTag'] = _column.group
        })
        let _group = _column.group
        let _findGroup = this.groupFields.filter((e) => {
          return e.group == _group
        })
        if (_findGroup.length > 0) {
          let _maps = _findGroup[0]['fieldsMap']
          Object.keys(_maps).forEach((key) => {
            let _item = _maps[key]
            if (_item.isCascadeField) {
              //如果是级联标记的元素，读取字段中设置的sourceField
              _column.fields = {
                text: _item.visibleField,
                value: _item.sourceField
              }
            }
          })
        }
      }
    },
    //表单校验-采购周期启
    validExpectBidPeriodStart(rule, value, callback) {
      if (value && value.toString().trim()) {
        this.$refs.customFormRef.validateField('expectBidPeriodEnd')
        callback()
      } else {
        // callback(new Error("请输入开始日期"));
        callback() //如果未填写，直接通过
      }
    },
    //表单校验-采购周期止
    validExpectBidPeriodEnd(rule, value, callback) {
      if (value && value.toString().trim()) {
        let _start = this.customFormObject.expectBidPeriodStart
        let _end = value
        if (_start && _start.toString().trim()) {
          let _startTime = new Date(_start).getTime()
          let _endTime = new Date(_end).getTime()
          if (_endTime < _startTime) {
            callback(new Error('采购周期起止时间，不规范'))
          } else {
            callback()
          }
        } else {
          callback(new Error("未输入'采购周期起'字段"))
        }
      } else {
        // callback(new Error("请输入截止日期"));
        callback() //如果未填写，直接通过
      }
    },
    //表单校验-项目开始时间
    validProjectStartTime(rule, value, callback) {
      if (value && value.toString().trim()) {
        this.$refs.customFormRef.validateField('projectEndTime')
        callback()
      } else {
        // callback(new Error("请输入开始日期"));
        callback() //如果未填写，直接通过
      }
    },
    //表单校验-项目结束时间
    validProjectEndTime(rule, value, callback) {
      if (value && value.toString().trim()) {
        let _start = this.customFormObject.projectStartTime
        let _end = value
        if (_start && _start.toString().trim()) {
          let _startTime = new Date(_start).getTime()
          let _endTime = new Date(_end).getTime()
          if (_endTime < _startTime) {
            callback(new Error('项目开始结束时间，不规范'))
          } else {
            callback()
          }
        } else {
          callback(new Error("未输入'项目开始时间'字段"))
        }
      } else {
        // callback(new Error("请输入截止日期"));
        callback() //如果未填写，直接通过
      }
    }
  }
}
