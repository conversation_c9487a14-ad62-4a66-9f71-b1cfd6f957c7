export default {
  methods: {
    //基本单位  字段： unitName
    handleUnitNameField(_column, i) {
      console.log('column---fields---unitName', _column)
      this.$API.masterData.pagedQueryUnit().then((res) => {
        _column.type = 'select'
        ;(_column.fields = { text: 'unitName', value: 'unitName' }),
          (_column.source = res.data.records)
        this.$nextTick(() => {
          this.$set(this.columnData, i, _column)
        })
      })
    }
  }
}
