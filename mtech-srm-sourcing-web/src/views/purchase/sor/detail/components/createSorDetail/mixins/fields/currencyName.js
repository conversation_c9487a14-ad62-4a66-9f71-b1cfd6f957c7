export default {
  methods: {
    //货币币种  字段： currencyName
    handleCurrencyNameField(_column, i) {
      this.$API.masterData.queryAllCurrency().then((res) => {
        _column.type = 'select'
        _column.fields = { text: 'currencyName', value: 'currencyName' }
        _column.source = res.data
        this.$nextTick(() => {
          this.$set(this.columnData, i, _column)
        })
      })
    },
    //货币币种  字段： currencyName
    handleCurrencyGroupField(_column, i) {
      this.$API.masterData.queryAllCurrency().then((res) => {
        _column.type = 'select'
        _column.fields = { text: 'currencyName', value: 'currencyCode' }
        this.handleGroupCascadeFields(_column, res)
        _column.source = res.data
        this.$nextTick(() => {
          this.$set(this.columnData, i, _column)
        })
      })
    }
  }
}
