import { i18n } from '@/main.js'
export const sorGroupFields = [
  {
    group: 'supplier',
    serialized: false,
    fieldList: ['supplierId', 'supplierCode', 'supplierName'], //供应商代码  供应商名称
    fieldsMap: {
      //code为下拉框字段，id、name为其他带出字段
      id: {
        sourceField: 'id', //源数据中，对应的字段
        formField: 'supplierId', //在Form中，使用的字段值
        text: i18n.t('供应商Id'), //标题
        visible: false //是否渲染到DOM中，设置false后，只传值，不显示
      },
      code: {
        sourceField: 'supplierCode',
        formField: 'supplierCode',
        visibleField: 'supplierCode',
        text: i18n.t('供应商编码'),
        isCascadeField: true //是否是级联字段，用于联动其他相关字段
      },
      name: {
        sourceField: 'supplierName',
        formField: 'supplierName',
        text: i18n.t('供应商名称'),
        visible: true //是否渲染到DOM中，设置true后，显示在页面中，Disabled状态
      }
      // stage: {
      //   sourceField: "stageName",
      //   formField: "stageName",
      //   text: i18n.t("所属阶段"),
      //   visible: true,
      // },
    }
  },
  {
    group: 'site',
    serialized: false,
    fieldList: ['siteId', 'siteCode', 'siteName'], //品类编码 品类名称
    fieldsMap: {
      id: {
        sourceField: 'id', //源数据中，对应的字段
        formField: 'siteId', //在Form中，使用的字段值
        text: i18n.t('工厂Id'),
        visible: false
      },
      code: {
        sourceField: 'siteCode',
        formField: 'siteCode',
        visibleField: 'siteCode',
        text: i18n.t('工厂编码'),
        isCascadeField: true
      },
      name: {
        sourceField: 'siteName',
        formField: 'siteName',
        text: i18n.t('工厂名称'),
        visible: true
      }
    }
  },
  {
    group: 'item',
    serialized: false,
    fieldList: [
      'itemId',
      'itemCode',
      'itemName',
      'categoryId', //品类ID  (新逻辑：品类是品项带出来的。不能独立输入)
      'categoryCode', //品类编码
      'categoryName' //品类名称
    ], //品项编码  品项名称
    fieldsMap: {
      id: {
        sourceField: 'id', //源数据中，对应的字段
        formField: 'itemId', //在Form中，使用的字段值
        text: i18n.t('品项Id'),
        visible: false
      },
      code: {
        sourceField: 'itemCode',
        formField: 'itemCode',
        visibleField: 'itemCode',
        text: i18n.t('品项编码'),
        isCascadeField: true
      },
      name: {
        sourceField: 'itemName',
        formField: 'itemName',
        text: i18n.t('品项名称'),
        visible: true
      },
      categoryId: {
        sourceField: 'categoryId', //源数据中，对应的字段
        formField: 'categoryId', //在Form中，使用的字段值
        text: i18n.t('品类Id'),
        visible: false
      },
      categoryCode: {
        sourceField: 'categoryCode',
        formField: 'categoryCode',
        text: i18n.t('品类编码'),
        visible: true
      },
      categoryName: {
        sourceField: 'categoryName',
        formField: 'categoryName',
        text: i18n.t('品类名称'),
        visible: true
      }
    }
  },
  {
    group: 'sku',
    serialized: false,
    fieldList: ['skuId', 'skuCode', 'skuName'], //SKU编码  SKU名称
    fieldsMap: {
      id: {
        sourceField: 'id', //源数据中，对应的字段
        formField: 'skuId', //在Form中，使用的字段值
        text: 'SKU ID',
        visible: false
      },
      code: {
        sourceField: 'barCode',
        formField: 'skuCode',
        visibleField: 'barCode',
        text: i18n.t('SKU编码'),
        isCascadeField: true
      },
      name: {
        sourceField: 'name',
        formField: 'skuName',
        text: i18n.t('SKU名称'),
        visible: true
      }
    }
  },
  {
    group: 'rate',
    serialized: false,
    fieldList: ['budgetRate', 'budgetRateId', 'budgetRateName'], //税率
    fieldsMap: {
      budgetRate: {
        sourceField: 'taxRate',
        formField: 'budgetRate', //税率值
        text: i18n.t('税率保存值'),
        visible: false
      },
      budgetRateName: {
        sourceField: 'taxItemName',
        formField: 'budgetRateName', //税率名称
        text: i18n.t('税率'),
        visible: false
      },
      budgetRateId: {
        sourceField: 'id',
        formField: 'budgetRateId', //税率Id
        visibleField: 'taxItemName',
        text: i18n.t('税率'),
        isCascadeField: true
      }
    }
  },
  {
    group: 'purGroup', //行内编辑-未用到
    serialized: false,
    fieldList: ['purGroupId', 'purGroupCode', 'purGroupName'], //采购组编码、名称
    fieldsMap: {
      id: {
        sourceField: 'id', //源数据中，对应的字段
        formField: 'purGroupId', //在Form中，使用的字段值
        text: i18n.t('采购组Id'),
        visible: false
      },
      code: {
        sourceField: 'organizationCode',
        formField: 'purGroupCode',
        visibleField: 'organizationCode',
        text: i18n.t('采购组编码'),
        isCascadeField: true
      },
      name: {
        sourceField: 'organizationName',
        formField: 'purGroupName',
        text: i18n.t('采购组名称'),
        visible: true
      }
    }
  },
  {
    group: 'currency',
    serialized: false,
    fieldList: ['currencyName', 'currencyCode'], //物料信息-币种
    fieldsMap: {
      code: {
        sourceField: 'id',
        formField: 'currencyId',
        visibleField: 'currencyName',
        text: i18n.t('币种'),
        isCascadeField: true
      },
      name: {
        sourceField: 'currencyCode',
        formField: 'currencyCode',
        text: i18n.t('货币编码'),
        visible: false
      },
      id: {
        sourceField: 'currencyName',
        formField: 'currencyName',
        text: i18n.t('货币名称'),
        visible: false
      }
    }
  },
  {
    group: 'budgetCurrency',
    serialized: false,
    fieldList: ['budgetCurrencyName', 'budgetCurrencyCode'], //需求信息-币种
    fieldsMap: {
      code: {
        sourceField: 'id',
        formField: 'budgetCurrencyId',
        visibleField: 'currencyName',
        text: i18n.t('币种'),
        isCascadeField: true
      },
      name: {
        sourceField: 'currencyCode',
        formField: 'budgetCurrencyCode',
        text: i18n.t('货币编码'),
        visible: false
      },
      id: {
        sourceField: 'currencyName',
        formField: 'budgetCurrencyName',
        text: i18n.t('货币名称'),
        visible: false
      }
    }
  },
  {
    group: 'referenceCurrency',
    serialized: false,
    fieldList: ['referenceCurrencyCode', 'referenceCurrencyName'], //参考信息-币种
    fieldsMap: {
      code: {
        sourceField: 'id',
        formField: 'referenceCurrencyId',
        visibleField: 'currencyName',
        text: i18n.t('币种'),
        isCascadeField: true
      },
      name: {
        sourceField: 'currencyCode',
        formField: 'referenceCurrencyCode',
        text: i18n.t('货币编码'),
        visible: false
      },
      id: {
        sourceField: 'currencyName',
        formField: 'referenceCurrencyName',
        text: i18n.t('货币名称'),
        visible: false
      }
    }
  }
  // {
  //   group: "category",
  //   serialized: false,
  //   fieldList: ["categoryId", "categoryCode", "categoryName"], //品类编码 品类名称
  //   fieldsMap: {
  //     id: {
  //       sourceField: "id", //源数据中，对应的字段
  //       formField: "categoryId", //在Form中，使用的字段值
  //       text:  i18n.t("品类Id"),
  //       visible: false,
  //     },
  //     code: {
  //       sourceField: "categoryCode",
  //       formField: "categoryCode",
  //       visibleField: "categoryCode",
  //       text: i18n.t("品类编码"),
  //       isCascadeField: true,
  //     },
  //     name: {
  //       sourceField: "categoryName",
  //       formField: "categoryName",
  //       text: i18n.t("品类名称"),
  //       visible: true,
  //     },
  //   },
  // },
  // {
  //   group: "por",
  //   serialized: false,
  //   fieldList: ["porId", "porCode", "porName"], //单据编码 单据名称
  //   fieldsMap: {
  //     id: {
  //       sourceField: "id", //源数据中，对应的字段
  //       formField: "porId", //在Form中，使用的字段值
  //       text:  i18n.t("单据Id"),
  //       visible: false,
  //     },
  //     code: {
  //       sourceField: "porCode",
  //       formField: "porCode",
  //       visibleField: "porCode",
  //       text: i18n.t("单据编码"),
  //       isCascadeField: true,
  //     },
  //     name: {
  //       sourceField: "porName",
  //       formField: "porName",
  //       text: i18n.t("单据名称"),
  //       visible: true,
  //     },
  //   },
  // },
  // {
  //   group: "businessType",
  //   serialized: false,
  //   fieldList: ["businessTypeId", "businessTypeCode", "businessTypeName"], //业务类型编码 业务类型
  //   fieldsMap: {
  //     id: {
  //       sourceField: "id", //源数据中，对应的字段
  //       formField: "businessTypeId", //在Form中，使用的字段值
  //       text:  i18n.t("业务类型Id"),
  //       visible: false,
  //     },
  //     code: {
  //       sourceField: "businessTypeCode",
  //       formField: "businessTypeCode",
  //       visibleField: "businessTypeCode",
  //       text: i18n.t("业务类型编码"),
  //       isCascadeField: true,
  //     },
  //     name: {
  //       sourceField: "businessTypeName",
  //       formField: "businessTypeName",
  //       text: i18n.t("业务类型"),
  //       visible: true,
  //     },
  //   },
  // },
  // {
  //   group: "project",
  //   serialized: false,
  //   fieldList: ["projectId", "projectCode", "projectName"], //项目代码  项目名称
  //   fieldsMap: {
  //     id: {
  //       sourceField: "id", //源数据中，对应的字段
  //       formField: "projectId", //在Form中，使用的字段值
  //       text:  i18n.t("项目Id"),
  //       visible: false,
  //     },
  //     code: {
  //       sourceField: "projectCode",
  //       formField: "projectCode",
  //       visibleField: "projectCode",
  //       text: i18n.t("项目代码"),
  //       isCascadeField: true,
  //     },
  //     name: {
  //       sourceField: "projectName",
  //       formField: "projectName",
  //       text: i18n.t("项目名称"),
  //       visible: true,
  //     },
  //   },
  // },
  // {
  //   group: "dept",
  //   serialized: false,
  //   fieldList: ["deptId", "deptCode", "deptName"], //部门代码  部门名称
  //   fieldsMap: {
  //     id: {
  //       sourceField: "id", //源数据中，对应的字段
  //       formField: "deptId", //在Form中，使用的字段值
  //       text:  i18n.t("部门Id"),
  //       visible: false,
  //     },
  //     code: {
  //       sourceField: "deptCode",
  //       formField: "deptCode",
  //       formField: "deptName",
  //       text: i18n.t("部门代码"),
  //       isCascadeField: true,
  //     },
  //     name: {
  //       sourceField: "deptName",
  //       formField: "deptName",
  //       text: i18n.t("部门名称"),
  //       visible: true,
  //     },
  //   },
  // },
]

//提交数据时，需要从object中移除的字段
export const sorSubmitIgnoreFields = []

//某些字段，新增时，设置默认值
export const sorDefalutFields = {
  quantity: 1 //申请数量，至少为1
}

//预置的校验规则
export const sorFieldsRules = {
  //预测采购量
  bidQuantity: [
    {
      required: false,
      pattern: /(^[0-9]*[1-9][0-9]*$)/,
      message: i18n.t('规定：输入正整数'),
      trigger: 'blur'
    }
  ],
  //申请数量
  quantity: [
    {
      required: false,
      pattern: /(^[0-9]*[1-9][0-9]*$)/,
      message: i18n.t('规定：输入正整数'),
      trigger: 'blur'
    }
  ],
  //历史申购量
  histroyApplyQuantity: [
    {
      required: false,
      pattern: /(^[0-9]*[1-9][0-9]*$)/,
      message: i18n.t('规定：输入正整数'),
      trigger: 'blur'
    }
  ],
  // 供应商邮箱
  supplierEmail: [
    {
      required: false,
      pattern: /^\w+((.\w+)|(-\w+))@[A-Za-z0-9]+((.|-)[A-Za-z0-9]+).[A-Za-z0-9]+$/,
      message: i18n.t('邮箱格式有误'),
      trigger: 'blur'
    }
  ],
  // 预算编号
  budgetCode: [
    {
      required: false,
      pattern: /^[A-Za-z0-9]+$/,
      message: i18n.t('预算编号：由字母与数字组成'),
      trigger: 'blur'
    }
  ]
}

//某些字段，忽略。即便配置了，也不出现在弹框From中
export const sorIgnoreFieldList = [
  'docType', //单据类型
  'sourceType', //源单据类型
  'sourceHeaderCode', //源单据编码
  'sourceHeaderName', //源单据名称
  'businessTypeName', //业务类型
  'porCode', //单据编码
  'porName', //单据名称
  'lineNo' //行号
]

//下拉框数据，支持allowFiltering的字段
export const sorAllowFilteringFields = [
  'itemCode', //品项编码
  'siteCode', //工厂编码
  'supplierCode' //供应商编码
]

//【布尔】枚举类型的字段列表
export const sorBooleanFieldList = [
  'reduction', //是否抵扣
  'sole' //是否独家
]

//【整数】数字类型的字段列表
export const sorIntegerFieldList = [
  // "budgetCode", //预算编号
  'bidQuantity', //预测采购量
  'quantity', //申请数量
  'histroyApplyQuantity' //历史申购量
]

//数字类型的字段列表
export const sorNumberFieldList = [
  // "budgetCode", //预算编号
  'bidQuantity', //预测采购量
  'quantity', //申请数量
  'histroyApplyQuantity', //历史申购量
  'histroyApplyPrice', //历史申购金额
  'histroyApplyCount', //历史申购次数
  'budgetPrice', //预算单价（未税）
  'budgetPriceIncludeTax', //预算单价（含税）
  'budgetTotalPriceIncludeTax', //预算总价（含税）
  'budgetTotalPrice', //预算总价（未税）
  'budgetSubjectTotalPrice' //科目总额
]

//【日期】类型的字段列表
export const sorDateFieldList = [
  'requiredDeliveryDate', //要求交期
  'createTime', //上传时间
  'projectStartTime', //项目开始时间
  'projectEndTime', //项目截止时间
  'expectBidPeriodStart', //预测采购周期起
  'expectBidPeriodEnd' //预测采购周期止
]

//【日期时间】类型的字段列表
export const sorDateTimeFieldList = [
  'requiredDeliveryDate', //要求交期
  'createTime', //上传时间
  'projectStartTime', //项目开始时间
  'projectEndTime', //项目截止时间
  'expectBidPeriodStart', //预测采购周期起
  'expectBidPeriodEnd' //预测采购周期止
]

export const sorDisabledFieldList = [
  'lineNo' //行号
]
