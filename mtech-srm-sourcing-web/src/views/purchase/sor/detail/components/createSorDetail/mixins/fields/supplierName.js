export default {
  methods: {
    // 供应商列表 字段： supplierName
    handleSupplierNameField(_column, i) {
      this.$API.masterData.getSupplierList().then((res) => {
        _column.type = 'select'
        _column.fields = { text: 'supplierName', value: 'supplierName' }
        _column.source = res.data
        this.$nextTick(() => {
          this.$set(this.columnData, i, _column)
        })
      })
    },
    // 供应商列表 字段： supplierName
    handleSupplierGroupField(_column, i) {
      this.$API.masterData.getSupplierList().then((res) => {
        _column.type = 'select'
        _column.fields = { text: 'supplierCode', value: 'supplierCode' }
        this.handleGroupCascadeFields(_column, res)
        _column.source = res.data
        this.$nextTick(() => {
          this.$set(this.columnData, i, _column)
        })
      })
    }
  }
}
