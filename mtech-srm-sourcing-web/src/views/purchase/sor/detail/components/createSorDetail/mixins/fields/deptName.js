export default {
  methods: {
    // 申请部门 字段： deptName
    handleDepartmentField(_column, i) {
      this.$API.masterData
        .getDepartmentList({
          departmentName: ''
        })
        .then((res) => {
          _column.type = 'select'
          _column.fields = {
            text: 'departmentName',
            value: 'departmentName'
          }
          _column.source = res.data
          this.$nextTick(() => {
            this.$set(this.columnData, i, _column)
          })
        })
    },
    // 申请部门 字段： deptName
    handleDepartmentGroupField(_column, i) {
      this.$API.masterData
        .getDepartmentList({
          departmentName: ''
        })
        .then((res) => {
          _column.type = 'select'
          _column.fields = {
            text: 'departmentCode',
            value: 'departmentCode'
          }
          this.handleGroupCascadeFields(_column, res)
          _column.source = res.data
          this.$nextTick(() => {
            this.$set(this.columnData, i, _column)
          })
        })
    }
  }
}
