.dialog-content {
  .form-group {
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(232, 232, 232, 1);
    .group-title {
      height: 46px;
      padding: 0 20px;
      color: #6386c1;
      background: rgba(250, 250, 250, 1);
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
      &.close-panel {
        .mt-icons {
          transform: rotate(180deg);
        }
      }
    }
    .group-panel {
      flex: 1;
      background: #ffffff;
      height: 300px;
      padding: 20px;
      display: flex;
      flex-wrap: wrap;
      border-top: 1px solid #e8e8e8;
      justify-content: space-between;
      .mt-form-item {
        width: 48%;
      }
    }
  }
}
