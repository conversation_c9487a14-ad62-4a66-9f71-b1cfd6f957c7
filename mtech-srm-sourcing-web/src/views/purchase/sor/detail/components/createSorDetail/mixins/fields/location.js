export default {
  methods: {
    //库位、地点列表 location
    handleLocationField(_column, i) {
      this.$API.masterData
        .getLocationList({
          page: {
            current: 1,
            size: 1000
          }
        })
        .then((res) => {
          _column.type = 'select'
          _column.fields = { text: 'locationName', value: 'locationName' }
          _column.source = res.data.records
          this.$nextTick(() => {
            this.$set(this.columnData, i, _column)
          })
        })
    }
  }
}
