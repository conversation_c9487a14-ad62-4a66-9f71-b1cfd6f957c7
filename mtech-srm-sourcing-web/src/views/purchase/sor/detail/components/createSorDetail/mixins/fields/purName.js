export default {
  methods: {
    // 人员列表 字段： purName
    handlePurNameField(_column, i) {
      this.$API.masterData.getUserListByTenantId({ employeeName: '' }).then((res) => {
        _column.type = 'select'
        _column.fields = { text: 'employeeName', value: 'employeeName' }
        _column.source = res.data
        this.$nextTick(() => {
          this.$set(this.columnData, i, _column)
        })
      })
    }
  }
}
