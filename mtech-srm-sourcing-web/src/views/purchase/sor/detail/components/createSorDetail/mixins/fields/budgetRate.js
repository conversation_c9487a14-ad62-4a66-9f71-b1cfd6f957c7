export default {
  methods: {
    //税目税率  字段： budgetRate
    handleBudgetRateField(_column, i) {
      this.$API.masterData.queryAllTaxItem().then((res) => {
        _column.type = 'select'
        _column.fields = { text: 'taxItemName', value: 'id' }
        _column.source = res.data
        this.$nextTick(() => {
          this.$set(this.columnData, i, _column)
        })
      })
    },
    //税目税率  字段： budgetRate
    handleRateGroupField(_column, i) {
      this.$API.masterData.queryAllTaxItem().then((res) => {
        _column.type = 'select'
        _column.fields = { text: 'taxItemName', value: 'id' }
        this.handleGroupCascadeFields(_column, res)
        _column.source = res.data
        this.$nextTick(() => {
          this.$set(this.columnData, i, _column)
        })
      })
    }
  }
}
