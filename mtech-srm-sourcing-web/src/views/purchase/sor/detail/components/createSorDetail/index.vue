<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="modalData.title" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="customFormRef" :model="customFormObject" :rules="getFormRules">
        <div class="form-group" v-for="(item, _index) in getColumnData" :key="_index">
          <div
            :class="['group-title', { 'close-panel': !formGroupArray[_index]['open'] }]"
            @click="toggleFromGroup(_index)"
          >
            <span>{{ item.group }}</span>
            <i class="mt-icons mt-icon-icon_accordion_arrow"></i>
          </div>
          <div class="group-panel" v-show="formGroupArray[_index]['open']">
            <mt-form-item
              :prop="rule.field"
              :label="rule.headerText"
              v-for="(rule, index) in item.fields"
              :key="index"
            >
              <mt-input
                v-if="showElementByType(rule, 'text')"
                v-model="customFormObject[rule.field]"
                :show-clear-button="false"
                :placeholder="rule.headerText"
                type="text"
              />
              <mt-input
                v-if="showElementByType(rule, 'disabled')"
                :value="customFormObject[rule.field]"
                :show-clear-button="false"
                :placeholder="rule.headerText"
                type="text"
                :disabled="true"
              />
              <mt-input-number
                v-if="showElementByType(rule, 'number')"
                v-model="customFormObject[rule.field]"
                :min="0"
                :show-clear-button="false"
                :placeholder="rule.headerText"
              />
              <mt-select
                v-if="showElementByType(rule, 'select')"
                v-model="customFormObject[rule.field]"
                :allow-filtering="checkAllowFiltering(rule.field)"
                :data-source="getElementDataSource(rule)"
                :fields="getElementDataFields(rule)"
                :change="handleSelectDataSource"
                :placeholder="rule.headerText"
              ></mt-select>
              <mt-multi-select
                v-if="showElementByType(rule, 'multi-select')"
                v-model="customFormObject[rule.field]"
                :data-source="getElementDataSource(rule)"
                :fields="getElementDataFields(rule)"
                :placeholder="rule.headerText"
              ></mt-multi-select>
              <mt-date-picker
                v-if="showElementByType(rule, 'date')"
                :open-on-focus="true"
                :show-clear-button="false"
                v-model="customFormObject[rule.field]"
                :placeholder="rule.headerText"
              ></mt-date-picker>
              <mt-time-picker
                v-if="showElementByType(rule, 'time')"
                :open-on-focus="true"
                :show-clear-button="false"
                v-model="customFormObject[rule.field]"
                :placeholder="rule.headerText"
              ></mt-time-picker>
              <mt-date-time-picker
                v-if="showElementByType(rule, 'datetime')"
                :open-on-focus="true"
                :show-clear-button="false"
                v-model="customFormObject[rule.field]"
                :placeholder="rule.headerText"
              ></mt-date-time-picker>
              <mt-drop-down-tree
                v-if="showElementByType(rule, 'drop-down-tree')"
                id="tree"
                :placeholder="rule.headerText"
                v-model="customFormObject[rule.field]"
                :fields="getElementTreeDataFields(rule)"
                :show-clear-button="false"
              ></mt-drop-down-tree>
            </mt-form-item>
          </div>
        </div>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import {
  sorGroupFields, //逻辑上：定义字段分组
  sorDefalutFields, //逻辑上：新增时，设置字段默认值
  sorFieldsRules, //逻辑上：定义字段校验规则
  sorIgnoreFieldList, //逻辑上：弹框需要忽略的字段
  sorBooleanFieldList, //枚举类型的字段列表
  sorNumberFieldList, //数字类型字段
  sorIntegerFieldList, //整数类型字段
  sorDateFieldList, //日期类型字段
  sorDateTimeFieldList, //日期时间类型字段
  sorDisabledFieldList, //只读类型字段
  sorSubmitIgnoreFields, //提交数据时，需要从object中移除的字段
  sorAllowFilteringFields //下拉框数据，支持allowFiltering的字段
} from './config'
import { utils } from '@mtech-common/utils'
import MIX from './mixins'
export default {
  mixins: [MIX],
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      groupFields: utils.cloneDeep(sorGroupFields), //逻辑上：定义字段分组
      defalutFields: utils.cloneDeep(sorDefalutFields), //逻辑上：新增时，设置字段默认值
      fieldsRules: utils.cloneDeep(sorFieldsRules), //逻辑上：定义字段校验规则
      ignoreFieldList: utils.cloneDeep(sorIgnoreFieldList), //逻辑上：弹框需要忽略的字段
      numberFieldList: utils.cloneDeep(sorNumberFieldList), //数字类型字段
      booleanFieldList: utils.cloneDeep(sorBooleanFieldList), //布尔类型字段
      integerFieldList: utils.cloneDeep(sorIntegerFieldList), //整数类型字段
      dateFieldList: utils.cloneDeep(sorDateFieldList), //日期类型字段
      dateTimeFieldList: utils.cloneDeep(sorDateTimeFieldList), //日期时间类型字段
      disabledFieldList: utils.cloneDeep(sorDisabledFieldList), //只读类型字段
      submitIgnoreFields: utils.cloneDeep(sorSubmitIgnoreFields), //提交数据时，需要从object中移除的字段
      allowFilteringFields: utils.cloneDeep(sorAllowFilteringFields), //下拉框数据，支持allowFiltering的字段
      requiredFields: [], //非空校验字段，读取columnData中，设置为required=1的数据
      columnData: [],
      formRules: {},
      customFormObject: {},
      formGroupArray: []
    }
  },
  computed: {
    getColumnData() {
      return this.serializeFieldsGroup(this.columnData)
    },
    //获取表单校验规则
    getFormRules() {
      return this.formRules
    },
    //校验当前字段的下拉框，需要模糊搜索
    checkAllowFiltering() {
      return (field) => {
        return this.allowFilteringFields.indexOf(field) > -1
      }
    },
    showElementByType() {
      return (rule, type) => {
        if (rule && Object.prototype.hasOwnProperty.call(rule, 'field')) {
          if (rule && Object.prototype.hasOwnProperty.call(rule, 'type')) {
            return rule['type'] === type
          } else {
            return type === 'text'
          }
        } else {
          return false
        }
      }
    },
    getElementDataSource() {
      return (rule) => {
        if (rule && Object.prototype.hasOwnProperty.call(rule, 'source')) {
          return rule['source']
        } else {
          return []
        }
      }
    },
    getElementDataFields() {
      return (rule) => {
        if (rule && Object.prototype.hasOwnProperty.call(rule, 'fields')) {
          return rule['fields']
        } else {
          if (rule && Object.prototype.hasOwnProperty.call(rule, 'source')) {
            let _source = rule['source']
            if (_source.length) {
              if (typeof _source[0] === 'string') {
                return { text: null, value: null }
              } else {
                return { text: 'text', value: 'value' }
              }
            } else {
              return { text: null, value: null }
            }
          } else {
            return { text: null, value: null }
          }
        }
      }
    },
    getElementTreeDataFields() {
      return (rule) => {
        if (rule && Object.prototype.hasOwnProperty.call(rule, 'fields')) {
          return rule['fields']
        } else {
          return {
            value: 'value',
            text: 'text',
            dataSource: [],
            child: 'child'
          }
        }
      }
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.fieldsRules = utils.merge(this.fieldsRules, this.cancatFieldsRules)
    let _filterIgnoreFieldList = []
    this.modalData.columnData.forEach((e) => {
      if (this.ignoreFieldList.indexOf(e.field) < 0) {
        _filterIgnoreFieldList.push(e)
      }
    })
    console.log('--form-columns--', _filterIgnoreFieldList)
    this.columnData = this.serializeColumnDataByGroupFields(_filterIgnoreFieldList)
    this.serializeColumnDataSourceList()
    //使用ignoreFieldList过滤后的数据，筛选出非空校验字段
    this.requiredFields = _filterIgnoreFieldList.filter((e) => {
      return e.required > 0
    })
    console.log('--required-fields--', this.requiredFields)
    this.serializeFormRules()
    if (Object.prototype.hasOwnProperty.call(this.modalData, 'data')) {
      this.customFormObject = { ...this.modalData.data }
    } else if (Object.prototype.hasOwnProperty.call(this.modalData, 'columnData')) {
      this.columnData.forEach((e) => {
        if (Object.keys(this.defalutFields).indexOf(e.field) > -1) {
          this.customFormObject[e.field] = this.defalutFields[e.field]
        } else {
          // this.customFormObject[e.field] = null;
        }
      })
    } else {
      this.customFormObject = {}
    }
  },
  methods: {
    // handleSelectDataSource(e) {
    //   console.log(e);

    // },
    confirm() {
      this.$refs.customFormRef.validate((valid) => {
        let _formObject = utils.cloneDeep(this.customFormObject)
        if (valid) {
          for (let i in this.submitIgnoreFields) {
            delete _formObject[this.submitIgnoreFields[i]]
          }
          this.$emit('confirm-function', _formObject)
        } else {
          let _checked = true
          let _checkFields = []
          for (let i = 0; i < this.columnData.length; i++) {
            let _column = this.columnData[i]
            let _field = _column['field']
            let _headerText = _column['headerText']
            let _formRules = Object.keys(this.formRules)
            if (_formRules.indexOf(_field) > -1) {
              let _rules = this.formRules[_field]
              for (let r in _rules) {
                if (typeof _rules[r]['required'] === 'boolean' && _rules[r]['required']) {
                  if (!_formObject[_field]) {
                    _checked = false
                    _checkFields.push(`${_headerText}(${_field})`)
                    console.log('必填：未校验通过：', _field, _rules[r])
                  }
                }
                if (_rules[r]['pattern']) {
                  if (_formObject[_field] && !_rules[r]['pattern'].test(_formObject[_field])) {
                    _checked = false
                    _checkFields.push(`${_headerText}(${_field})`)
                    console.log('正则：未校验通过：', _field, _rules[r])
                  }
                }
              }
            }
          }
          let _emptyNum = 0,
            length = 0
          Object.keys(_formObject).forEach((key) => {
            length++
            if (!_formObject[key]) _emptyNum++
          })
          if (_emptyNum == length) {
            this.$toast({
              content: `您未输入任何字段，不能提交.`,
              type: 'warning'
            })
            return
          }
          if (_checked) {
            for (let i in this.submitIgnoreFields) {
              delete _formObject[this.submitIgnoreFields[i]]
            }
            this.$emit('confirm-function', _formObject)
          } else {
            let _msg = _checkFields.join(',')
            this.$toast({
              content: `以下字段${_msg}未校验通过，请检查提交的数据.`,
              type: 'warning'
            })
            return
          }
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
@import './style/index.scss';
</style>
