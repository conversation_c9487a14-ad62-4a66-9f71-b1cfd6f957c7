export default {
  methods: {
    // 采购组织  字段： purGroupName
    handlePurGroupNameField(_column, i) {
      this.$API.masterData
        .purchaseOraginaze({
          organizationTypeCode: 'BUORG002ADM'
        })
        .then((res) => {
          _column.type = 'select'
          _column.fields = {
            text: 'organizationName',
            value: 'organizationName'
          }
          _column.source = res.data
          this.$nextTick(() => {
            this.$set(this.columnData, i, _column)
          })
        })
    },
    // 采购组织  字段： purGroupName
    handlePurGroupField(_column, i) {
      this.$API.masterData
        .purchaseOraginaze({
          organizationTypeCode: 'BUORG002ADM'
        })
        .then((res) => {
          _column.type = 'select'
          _column.fields = { text: 'organizationName', value: 'id' }
          this.handleGroupCascadeFields(_column, res)
          _column.source = res.data
          this.$nextTick(() => {
            this.$set(this.columnData, i, _column)
          })
        })
    }
  }
}
