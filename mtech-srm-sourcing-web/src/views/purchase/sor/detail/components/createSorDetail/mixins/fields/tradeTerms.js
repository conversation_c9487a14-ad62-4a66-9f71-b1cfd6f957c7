import { i18n } from '@/main.js'
export default {
  methods: {
    //贸易条款 字段： tradeTerms
    handleTradeTermsField(_column) {
      _column.type = 'select'
      _column.source = [
        i18n.t('工厂交货( EXW)'),
        i18n.t('货交承运人(FCA)'),
        i18n.t('船边交货(FAS)'),
        i18n.t('船上交货(FOB)'),
        i18n.t('成本加运费(CFR)'),
        i18n.t('成本、保险费加运费(CIF)'),
        i18n.t('运费付至(CPT)'),
        i18n.t('运费及保险费付至(CIP)'),
        i18n.t('目的港船上交货(DES)'),
        i18n.t('边境交货(DAF) '),
        i18n.t('目的港船上交货(DES)'),
        i18n.t('目的港码头交货(DEQ)'),
        i18n.t('未完税交货(DDU)'),
        i18n.t('完税后交货(DDP)')
      ]
    }
  }
}
