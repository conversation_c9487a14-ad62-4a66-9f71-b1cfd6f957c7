export default {
  methods: {
    // 品类列表 字段： categoryName
    handleCategoryNameField(_column, i) {
      this.$API.masterData.getCategoryList().then((res) => {
        _column.type = 'select'
        _column.fields = { text: 'categoryName', value: 'categoryName' }
        _column.source = res.data
        this.$nextTick(() => {
          this.$set(this.columnData, i, _column)
        })
      })
    }
  }
}
