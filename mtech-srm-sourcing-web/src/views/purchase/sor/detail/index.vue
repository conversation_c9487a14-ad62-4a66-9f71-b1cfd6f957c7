<template>
  <mt-detail-page
    :detail-config="detailConfig"
    @handleClickOptionsToolBar="handleClickOptionsToolBar"
  >
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
      <mt-rich-text-editor
        slot="slot-3"
        ref="editorRef"
        :height="600"
        :css-class="'grid-text-editor'"
        v-model="getEditorValue"
      >
      </mt-rich-text-editor>
      <div
        class="mt-form-parser"
        :slot="'slot-' + (index + 4)"
        v-for="(item, index) in customTabs"
        :key="index"
      >
        <mt-parser ref="parser" :form-conf="item.template" />
      </div>
    </mt-template-page>
  </mt-detail-page>
</template>
<script>
import Vue from 'vue'
import MtRichTextEditor from '@mtech-ui/rich-text-editor'
import MtDetailPage from '@mtech/common-detail-page'
import Parser from '@mtech-form-design/form-parser'
import {
  detailColumnFields,
  detailToolbar,
  supplierToolbar,
  filesToolbar,
  editorToolbar
} from './config'
import axios from 'axios'
import { ApproveStatusCodeMap, ApproveStatusMap } from '@/constants'

Vue.component('mt-parser', Parser)
Vue.use(MtRichTextEditor)
Vue.component('mt-detail-page', MtDetailPage)

export default {
  data() {
    return {
      customTabs: [],
      docId: null,
      textEditorFieldDefine: {},
      detail: {},
      detailConfig: {
        toolbar: [
          { id: 'save', icon: 'icon_solid_Save', title: this.$t('发布') },
          {
            id: 'cancel',
            icon: 'icon_solid_Closeorder',
            title: this.$t('关闭')
          },
          { id: 'back', icon: 'icon_solid_close', title: this.$t('返回') }
        ],
        detailName: '',
        detailCode: '',
        detailStatus: '',
        approveStatus: null,
        columnData: detailColumnFields,
        detailFileds: [
          {
            icon: 'sass',
            close: false,
            details: {}
          }
        ]
      },
      pageConfig: [
        {
          title: this.$t('需求明细'),
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
          toolbar: detailToolbar,
          moduleId: null,
          moduleKey: null,
          fieldDefines: [],
          grid: { allowFiltering: true, dataSource: [], columnData: [] }
        },
        {
          title: this.$t('推荐供方'),
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          toolbar: supplierToolbar,
          moduleId: null,
          fieldDefines: [],
          moduleKey: null,
          grid: { allowFiltering: true, dataSource: [], columnData: [] }
        },
        {
          title: this.$t('文件记录'),
          useToolTemplate: false,
          toolbar: filesToolbar,
          moduleId: null,
          moduleKey: null,
          fieldDefines: [],
          grid: { allowFiltering: true, dataSource: [], columnData: [] }
        },
        {
          useToolTemplate: false,
          title: this.$t('描述说明'),
          toolbar: editorToolbar,
          moduleId: null,
          moduleKey: null,
          fieldDefines: [],
          textEditor: {
            value: ''
          }
        }
      ]
    }
  },
  computed: {
    getEditorValue() {
      return this.pageConfig[3]?.textEditor?.value
    }
  },
  mounted() {
    this.docId = this.$route.query.porId
    this.getSinglePorHeaderInfo() //获取单据详情
  },
  methods: {
    //根据Id获取单条单据数据详情
    getSinglePorHeaderInfo() {
      this.$API.porMine.findPorHeaderDetailById({ id: this.docId }).then((res) => {
        let { porName, porCode, approveStatus } = res.data
        console.log(5555, res.data)
        this.detailConfig.detailName = porName
        this.detailConfig.detailCode = porCode
        this.detailConfig.detailStatus = ApproveStatusCodeMap[approveStatus]
        if (!ApproveStatusCodeMap[approveStatus]) throw Error(this.$t('不正确的需求单状态'))
        this.detailConfig.approveStatus = approveStatus
        this.detail = res.data
        let _claimStatusMap = {
          0: this.$t('未认领'),
          2: this.$t('已认领')
        }
        let _detailData = { ...res.data }
        _detailData.claimStatus = _claimStatusMap[+_detailData.claimStatus] || ''
        this.$set(this.detailConfig.detailFileds[0], 'details', _detailData)
        if (
          [ApproveStatusMap.approved.code, ApproveStatusMap.pending.code].includes(approveStatus)
        ) {
          this.$set(this.detailConfig, 'toolbar', [
            { id: 'back', icon: 'icon_solid_close', title: this.$t('返回') }
          ])
        }
        this.getPorModuleConfigInfo()
      })
    },
    // 获取单据模块配置结构
    getPorModuleConfigInfo() {
      this.$store.commit('startLoading')
      this.$API.businessConfig.getModuleConfig({ docId: this.docId }).then((res) => {
        this.$store.commit('endLoading')
        let _config = res.data
        let _moduleItems = _config.moduleItems
        for (let i in _moduleItems) {
          let _moduleItem = _moduleItems[i]
          let _moduleType = _moduleItem['moduleType']

          if (+_moduleType < 3) {
            //模块类型 0需求明细  1推荐供应商 2文件记录
            this.renderDetailGridTabs(_moduleItem)
          } else if (+_moduleType === 3) {
            //3描述说明
            this.renderDetailTextEditorTabs(_moduleItem)
          } else {
            //99自定义
            this.renderDetailCustomFormTabs(_moduleItem)
            break
          }
        }
      })
    },
    //获取表格结构 0需求明细  1推荐供应商 2文件记录
    renderDetailGridTabs(_moduleItem) {
      let _columns = [
        {
          type: 'checkbox',
          width: '50'
        }
      ]
      let _fieldDefines = _moduleItem['fieldDefines']
      for (let i in _fieldDefines) {
        let _col = {
          field: _fieldDefines[i]['fieldCode'],
          headerText: _fieldDefines[i]['fieldName'],
          required: _fieldDefines[i]['required'],
          fieldGroup: _fieldDefines[i]['fieldGroup']
        }
        if (i < 1) {
          _col.cellTools = ['Edit', 'delete']
        }
        if (
          [ApproveStatusMap.approved.code, ApproveStatusMap.pending.code].includes(
            this.detailConfig.approveStatus
          )
        ) {
          delete _col.cellTools
        }
        _columns.push(_col)
      }
      let _moduleType = _moduleItem['moduleType']
      this.pageConfig[+_moduleType]['grid']['columnData'] = []
      this.pageConfig[+_moduleType]['grid']['fieldDefines'] = []
      this.$nextTick(() => {
        if (
          [ApproveStatusMap.approved.code, ApproveStatusMap.pending.code].includes(
            this.detailConfig.approveStatus
          )
        ) {
          this.$set(this.pageConfig[+_moduleType], 'toolbar', [])
        }
        this.$set(this.pageConfig[+_moduleType], 'moduleId', _moduleItem['moduleId'])
        this.$set(this.pageConfig[+_moduleType], 'moduleKey', _moduleItem['moduleKey'])
        this.$set(this.pageConfig[+_moduleType], 'fieldDefines', _moduleItem['fieldDefines'])
        this.$set(this.pageConfig[+_moduleType].grid, 'columnData', _columns)
        //获取表格结构 0需求明细  1推荐供应商 2文件记录
        let recordsPosition = 'data.porItemDataList.records'
        if (+_moduleType === 1) {
          recordsPosition = 'data.porSupplierDataList.records'
        } else if (+_moduleType === 2) {
          recordsPosition = 'data.sourcingFileDataList.records'
        }
        this.$set(this.pageConfig[+_moduleType].grid, 'asyncConfig', {
          url: this.$API.businessConfig.getSingleModuleData,
          queryBuilderWrap: 'requestParams',
          params: {
            docId: this.docId,
            moduleKey: _moduleItem['moduleKey']
          },
          recordsPosition,
          serializeList: (list) => {
            for (let i in list) {
              if (Array.isArray(list[i]['fieldDataList'])) {
                let _fields = list[i]['fieldDataList']
                for (let j in _fields) {
                  let _field = _fields[j]['fieldCode']
                  let _value = _fields[j]['fieldData']
                  list[i][_field] = _value
                }
              }
            }
            return list
          }
        })
        console.log('页面配置--', this.pageConfig)
      })
    },
    //获取富文本编辑器结构  3描述说明
    renderDetailTextEditorTabs(_moduleItem) {
      let _fieldDefines = _moduleItem['fieldDefines']
      if (_fieldDefines.length > 0) {
        this.textEditorFieldDefine = _fieldDefines[0]
      }
      let _moduleType = _moduleItem['moduleType']
      this.$nextTick(() => {
        if (
          [ApproveStatusMap.approved.code, ApproveStatusMap.pending.code].includes(
            this.detailConfig.approveStatus
          )
        ) {
          this.$set(this.pageConfig[+_moduleType], 'toolbar', [])
        }
        this.$set(this.pageConfig[+_moduleType], 'moduleId', _moduleItem['moduleId'])
        this.$set(this.pageConfig[+_moduleType], 'moduleKey', _moduleItem['moduleKey'])
        this.getPorModuleTextEditorData(_moduleItem['moduleType'], _moduleItem['moduleKey'])
        console.log('富文本编辑器----配置', this.pageConfig)
      })
    },
    //表单数据填充
    fillFormData(form, data) {
      form.fields.forEach((item) => {
        let val = data[item.__vModel__]
        if (val) {
          this.$set(item.__config__, 'defaultValue', val)
        }
      })
    },
    // 获取富文本模块的数据  3描述说明
    getPorModuleTextEditorData(tabIndex, _moduleKey) {
      let _moduleIndex = tabIndex
      this.$API.businessConfig
        .getModuleData({
          docId: this.docId,
          moduleKey: _moduleKey,
          requestParams: {
            page: {
              size: 10
            }
          }
        })
        .then((res) => {
          let _data = res.data
          let _descriptionData = _data.descriptionData
          console.log('富文本编辑器--数据', _descriptionData)
          let _value = ''

          if (
            Array.isArray(_descriptionData?.fieldDataList) &&
            _descriptionData.fieldDataList.length > 0
          ) {
            _value = _descriptionData.fieldDataList[0]['fieldData']
          }
          this.pageConfig[+_moduleIndex]['textEditor']['value'] = ''
          this.$nextTick(() => {
            this.$set(this.pageConfig[+_moduleIndex]['textEditor'], 'value', _value)
          })
        })
    },
    //获取自定义表单结构  99自定义
    renderDetailCustomFormTabs(_moduleItem) {
      let _formTemplateId = _moduleItem['formTemplateId'] //99
      if (_formTemplateId) {
        this.$API.formDesignService.getFormDesignInfo({ id: _formTemplateId }).then((e) => {
          let _ram = parseInt(Math.random(10000) * 10000)
          let _formStructure = {
            template: {
              fields: e.data.template.fields,
              formRef: `elForm-${_ram}`,
              formModel: `formData-${_ram}`,
              size: 'small',
              labelPosition: 'right',
              labelWidth: 100,
              formRules: 'rules',
              gutter: 15,
              disabled: false,
              span: 24,
              formBtns: true
            }
          }
          this.$API.businessConfig
            .getModuleData({
              docId: this.docId,
              moduleKey: _moduleItem['moduleKey'],
              requestParams: {
                page: {
                  size: 10
                }
              }
            })
            .then((res) => {
              let _data = res.data
              let _moduleData = _data.moduleData
              if (_moduleData) {
                //表单数据填充
                this.fillFormData(_formStructure.template, JSON.parse(_moduleData))
              }
              this.customTabs.push(_formStructure)
              this.$nextTick(() => {
                let _formTabConfig = {
                  useToolTemplate: false,
                  title: _moduleItem['moduleName'],
                  toolbar: {
                    useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
                    tools: [
                      [
                        {
                          id: 'Save',
                          icon: 'icon_solid_Save',
                          title: this.$t('保存')
                        }
                      ]
                    ]
                  },
                  moduleId: _moduleItem['moduleId'],
                  moduleKey: _moduleItem['moduleKey']
                }
                if (
                  [ApproveStatusMap.approved.code, ApproveStatusMap.pending.code].includes(
                    this.detailConfig.approveStatus
                  )
                ) {
                  _formTabConfig['toolbar'] = []
                }
                this.pageConfig.push(_formTabConfig)
              })
            })
        })
      }
    },
    //单据详情顶部操作按钮
    handleClickOptionsToolBar(e) {
      console.log('handleClickOptionsToolBar', e)
      if (e.id == 'approval') {
        //审批操作
        this.handleExamine(e)
      } else if (e.id == 'save') {
        //发布
        if (this.detail.approveStatus === ApproveStatusMap.approved.code) {
          return this.$toast({
            content: this.$t('审批通过，不支持该操作。'),
            type: 'warning'
          })
        } else {
          this.$API.porMine.publishById({ idList: [this.docId] }).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('发布成功'), type: 'success' })
              this.$router.go(-1)
            }
          })
        }
      } else if (e.id == 'cancel') {
        if (this.detail.approveStatus === ApproveStatusMap.approved.code) {
          this.$toast({
            content: this.$t('审批通过，不支持该操作。'),
            type: 'warning'
          })
        } else {
          this.$API.porMine.closeById({ idList: [this.docId] }).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('关闭成功'), type: 'success' })
              this.$router.go(-1)
            }
          })
        }
      } else if (e.id == 'back') {
        this.$router.go(-1)
      }
    },
    // 弹窗：审批
    handleExamine() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/sor/detail/components/approval" */ './components/approval'
          ),
        data: {
          title: this.$t('审批')
        },
        success: () => {}
      })
    },
    handleClickToolBar(e) {
      if (e.tabIndex == 0) {
        //需求明细
        if (e.toolbar.id == 'Add') {
          //新增
          this.handleAddModuleForm(e)
        } else if (e.toolbar.id == 'Delete') {
          //删除
          this.handleDeleteModuleData(e)
        } else if (e.toolbar.id == 'Export') {
          //删除
          this.handleExportData(e)
        }
      } else if (e.tabIndex == 1) {
        //候选供方
        if (e.toolbar.id == 'Add') {
          //新增
          this.handleAddModuleForm(e)
        } else if (e.toolbar.id == 'Delete') {
          //删除
          this.handleDeleteModuleData(e)
        }
      } else if (e.tabIndex == 2) {
        //文件记录
        if (e.toolbar.id == 'Upload') {
          //上传
        } else if (e.toolbar.id == 'Download') {
          //下载
        } else if (e.toolbar.id == 'Delete') {
          //删除
        }
      } else if (e.tabIndex == 3) {
        //描述说明
        if (e.toolbar.id == 'Save') {
          //保存
          this.handleSaveTextEditor(e)
        }
      } else {
        //其他自定义
        if (e.toolbar.id == 'Save') {
          //保存自定义表单数据
          this.handleSaveCustomFormData(e)
        }
      }
    },
    handleExportData(e) {
      let _gridConfig = this.pageConfig[+e.tabIndex]
      let _params = {
        docId: this.docId,
        moduleId: _gridConfig?.moduleId ?? '',
        moduleKey: _gridConfig?.moduleKey ?? '',
        requestParams: {
          page: {
            current: 1,
            size: 10
          }
        }
      }
      console.log('导出单据----', _params)
      axios.defaults.headers = {
        'Content-Type': 'application/json',
        // "Content-Type": "application/octet-stream",
        responseType: 'blob'
      }
      axios({
        method: 'post',
        url: '/api/sourcing/tenant/por/module/export',
        data: _params // post 请求传值：formData ，或者 json （根据接口的要求传值即可）
      })
        .then((response) => {
          // const link = document.createElement("a"); // 创建元素
          // const blob = new Blob([response], {
          //   type: "application/vnd.ms-excel",
          // });
          // link.style.display = "none";
          // link.href = URL.createObjectURL(blob); // 创建下载的链接
          // link.setAttribute("download", "xx.xlsx"); // 给下载后的文件命名
          // document.body.appendChild(link);
          // link.click(); // 点击下载
          // document.body.removeChild(link); //  下载完成移除元素
          // window.URL.revokeObjectURL(link.href); // 释放掉blob对象
          const fileName = response.headers['content-disposition']
            .split(';')[1]
            .split('=')[1]
            .split('.xlsx')[0] // 根据接口返回情况拿到文件名
          const blob = new Blob([response.data], {
            type: 'application/vnd.ms-excel'
          }) // 通过返回的流数据 手动构建blob 流
          const reader = new FileReader()
          reader.readAsDataURL(blob) // 转换为base64，可以直接放入a标签的href （转换base64还可用 window.atob ，未实验过）
          reader.onload = (e) => {
            // 转换完成，创建一个a标签用于下载
            const a = document.createElement('a')
            console.log(decodeURIComponent(fileName))
            a.download = decodeURIComponent(fileName) + '.xlsx' // 构建 下载的文件名称以及下载的文件格式（可通过传值输入）
            if (typeof e.target.result === 'string') {
              a.href = e.target.result
            }
            a.click()
          }
        })
        .catch((error) => {
          console.log('下载 Error ', error)
        })
      // this.$http
      //   .post("/sourcing/tenant/por/module/export", _params)
      //   .then((res) => {
      //     console.log("export module-config--data", res);
      //     this.$toast({
      //       content: this.$t("导出成功"),
      //       type: "success",
      //     });
      //     // let _moduleKey = this.pageConfig[tabIndex]["moduleKey"];
      //     // this.getPorModuleTextEditorData(tabIndex, _moduleKey);
      //   });
      // this.$API.businessConfig.exportModuleData(_params).then((res) => {
      //   console.log("export module-config--data", res);
      //   this.$toast({
      //     content: this.$t("导出成功"),
      //     type: "success",
      //   });
      //   // let _moduleKey = this.pageConfig[tabIndex]["moduleKey"];
      //   // this.getPorModuleTextEditorData(tabIndex, _moduleKey);
      // });
    },
    //富文本模块 点击保存
    handleSaveTextEditor(e) {
      let _moduleId = this.pageConfig[+e.tabIndex]['moduleId']
      let _field = this.textEditorFieldDefine
      _field.fieldData = this.$refs.editorRef.ejsRef.getHtml()
      this.handleSaveTextEditorTabData(e.tabIndex, _moduleId, _field)
    },
    //保存富文本模块数据
    handleSaveTextEditorTabData(tabIndex, _moduleId, _data) {
      //moduleType 3
      let _params = {
        formType: 0,
        moduleId: _moduleId,
        moduleType: 3,
        porId: this.docId,
        descriptionSaveRequest: {
          fieldDataList: [_data]
        }
      }
      console.log('保存单据--（富文本）', _params)
      this.$API.businessConfig.saveModuleData(_params).then((res) => {
        console.log('save module-config--data', res)
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        let _moduleKey = this.pageConfig[tabIndex]['moduleKey']
        this.getPorModuleTextEditorData(tabIndex, _moduleKey)
      })
    },
    //保存自定义表单模块数据
    handleSaveCustomFormTabData(_moduleId, _data, _moduleKey) {
      //moduleType 99
      _moduleKey
      let _params = {
        formType: 1,
        moduleData: JSON.stringify(_data),
        moduleId: _moduleId,
        moduleType: 99,
        porId: this.docId
      }
      console.log('保存单据--（用户自定义表单）', _params)
      this.$API.businessConfig.saveModuleData(_params).then((res) => {
        console.log('save module-config--data', res)
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
      })
    },
    //保存表单数据
    handleSaveSingleTabData(tabIndex, _moduleId, _data) {
      //moduleType tabIndex
      let _params = {
        formType: 0,
        moduleId: _moduleId,
        moduleType: tabIndex,
        porId: this.docId
      }

      let _fieldList = []
      let _fieldDefines = this.pageConfig[+tabIndex]['fieldDefines']
      for (let i in _fieldDefines) {
        let _temp = _fieldDefines[i]
        if (_fieldDefines[i]['tableField'] < 1) {
          _temp['fieldData'] = _data[_fieldDefines[i]['fieldCode']]
          _temp.recordId = _data.id ? _data.id : null
          _fieldList.push(_temp)
          delete _data[_fieldDefines[i]['fieldCode']]
        }
      }
      _data['fieldDataList'] = _fieldList
      if (tabIndex == 0) {
        //需求明细
        _params.porItemList = [_data]
      } else if (tabIndex == 1) {
        //候选供方
        _params.porSupplierDataList = [_data]
      } else if (tabIndex == 2) {
        //相关文件
        _params.sourcingFileList = [_data]
      }
      this.$store.commit('startLoading')
      this.$API.businessConfig.saveModuleData(_params).then((res) => {
        console.log('save module-config--data', res)
        this.$store.commit('endLoading')
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },
    //删除行数据
    handleDeleteModuleData(e) {
      let _selectGridRecords = e.grid.getSelectedRecords()
      if (_selectGridRecords.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      this.handleBatchUpdateDelete(_selectGridRecords, e.tabIndex)
    },
    //新增单行数据
    handleAddModuleForm(e) {
      let _columns = this.pageConfig[e.tabIndex]['grid']['columnData']
      //移除没有field的数据列
      let _haveFieldColumns = _columns.filter((c) => {
        return Object.prototype.hasOwnProperty.call(c, 'field')
      })
      let _title = ''
      if (e.tabIndex < 1) _title = this.$t('需求明细')
      if (e.tabIndex == 1) _title = this.$t('候选供方')
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/sor/detail/components/createSorDetail" */ './components/createSorDetail/index.vue'
          ),
        data: {
          title: `新增【${_title}】`,
          columnData: _haveFieldColumns
        },
        success: (form) => {
          let _moduleId = this.pageConfig[+e.tabIndex]['moduleId']
          this.handleSaveSingleTabData(e.tabIndex, _moduleId, form)
        }
      })
    },
    //修改单行数据
    handleEditModuleForm(e) {
      let _columns = this.pageConfig[e.tabIndex]['grid']['columnData']
      //移除没有field的数据列
      let _haveFieldColumns = _columns.filter((c) => {
        return Object.prototype.hasOwnProperty.call(c, 'field')
      })
      let _title = ''
      if (e.tabIndex == 0) _title = this.$t('需求明细')
      if (e.tabIndex == 1) _title = this.$t('候选供方')
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/sor/detail/components/createSorDetail" */ './components/createSorDetail/index.vue'
          ),
        data: {
          title: `${this.$t('编辑')}【${_title}】`,
          columnData: _haveFieldColumns,
          data: e.data
        },
        success: (form) => {
          let _moduleId = this.pageConfig[+e.tabIndex]['moduleId']
          this.handleSaveSingleTabData(e.tabIndex, _moduleId, form)
        }
      })
    },
    //批量删除操作
    handleBatchUpdateDelete(_selectGridRecords, tabIndex) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      this.handleDeleteConfig(_selectIds, tabIndex)
    },
    //删除数据
    handleDeleteConfig(ids, tabIndex) {
      let _urlFunction = tabIndex == 0 ? 'deleteDetailPorItemById' : 'deleteDetailPorSupplierById'
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？'),
          confirm: () => this.$API.porMine[_urlFunction]({ idList: ids })
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      if (e.tool.id == 'edit') {
        this.handleEditModuleForm(e)
      } else if (e.tool.id == 'delete') {
        //删除
        this.handleDeleteConfig([e.data.id], e.tabIndex)
      }
    },
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
      this.handleEditModuleForm(e)
    },
    //用户点击自定义表单-保存
    handleSaveCustomFormData(e) {
      console.log('提价数据----', e, this.$refs.parser)
      let _moduleId = this.pageConfig[+e.tabIndex]['moduleId']
      let _moduleKey = this.pageConfig[+e.tabIndex]['moduleKey']
      this.$refs.parser[e.tabIndex - 4].valiteFormData().then((formData) => {
        this.handleSaveCustomFormTabData(_moduleId, formData, _moduleKey)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/.mt-form-parser {
  padding: 20px;
}
/deep/ .mt-data-grid {
  .e-grid {
    th.e-headercell,
    td.e-rowcell {
      &:not(:first-of-type) {
        width: 200px;
      }
      &:first-of-type {
        width: 40px;
      }
    }
  }
}
/deep/.text-editor-container {
  display: inline-block;
  flex: 1;
  margin-bottom: 20px;

  .mt-rich-text-editor {
    height: 100%;
  }
  .grid-text-editor {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid #e8e8e8 !important;
    border-radius: 8px 8px 0 0 !important;
    .e-toolbar-wrapper {
      height: 42px !important;
    }
    .e-extended-toolbar {
      background: #ffffff;
      border-radius: 8px 8px 0 0 !important;
      .e-toolbar-items {
        border-radius: 8px 0 0 0 !important;
        background: #ffffff;
        .e-tbar-btn {
          background: #ffffff;
        }
      }
      .e-expended-nav {
        border-radius: 0 8px 0 0 !important;
        background: #ffffff;
      }
    }
    .e-rte-content {
      height: auto;
      margin-top: 0px;
      flex: 1;
    }
  }
}
</style>
