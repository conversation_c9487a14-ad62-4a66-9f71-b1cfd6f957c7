<template>
  <div class="source-list">
    <mt-template-page :template-config="pageConfig" :is-detail-page="true" ref="templateRef" />
  </div>
</template>

<script>
export default {
  props: {
    pageConfig: {
      type: Array,
      default: () => {
        return []
      }
    },
    gridColumn: {
      type: Array,
      default: () => {
        return []
      }
    },
    gridData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },

  data() {
    return {}
  },

  mounted() {},
  computed: {},
  methods: {
    getSelectRows() {
      let _currentTabRef = this.$refs.horRef.getCurrentTabRef()
      return _currentTabRef.grid.getSelectedRecords()
    },
    popSelectRows() {
      //移除已选中数据
      let _selectRows = this.getSelectRows()
      let _packages = JSON.parse(JSON.stringify(this['pageConfig'][0]['grid']['dataSource']))
      let _ids = []
      for (let i in _selectRows) {
        _ids.push(_selectRows[i]['id'])
      }
      let _restList = _packages.filter((e) => {
        return _ids.indexOf(e.id) < 0
      })
      this['pageConfig'][0]['grid']['dataSource'] = []
      this.$nextTick(() => {
        this.$set(this['pageConfig'][0]['grid'], 'dataSource', _restList)
      })
    },
    moveRequestFromPackageToList(list) {
      //从寻源建议列表中移入数据
      let _packages = JSON.parse(JSON.stringify(this['pageConfig'][0]['grid']['dataSource']))
      for (let i in list) {
        list[i]['isTempField'] = true
        _packages.unshift(list[i])
      }
      this['pageConfig'][0]['grid']['dataSource'] = []
      this.$nextTick(() => {
        this.$set(this['pageConfig'][0]['grid'], 'dataSource', _packages)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.data-grid-container /deep/ {
  .e-grid {
    tr.temp-field-from-package {
      td {
        position: relative;
        background: #f5f5f5;
        &:before {
          content: '';
          height: 0;
          width: 0;
          border-right: 6px solid transparent;
          border-top: 6px solid #eda133;
          position: absolute;
          left: 0;
          top: 0;
        }
      }
    }
  }
}
</style>
