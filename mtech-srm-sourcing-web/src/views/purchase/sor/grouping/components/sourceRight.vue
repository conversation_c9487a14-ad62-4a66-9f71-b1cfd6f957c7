<template>
  <div class="source-right">
    <div class="require-list">
      <div
        :class="['one-require', activeIndex == index && 'one-require-active']"
        v-for="(item, index) in requireList"
        :key="index"
      >
        <div class="top-box">
          <div class="left-title">
            <span class="indexs">{{ index + 1 }}</span>
            <span>{{ $t('寻源需求建议') }}{{ index + 1 }}</span>
          </div>
          <div class="right-btns">
            <!-- <div class="create-btn" @click="handleCreateProj(index)">
              <i
                class="mt-icons mt-icon-icon_solid_Createorder"
                title=this.$t("创建寻源项目")
              ></i>
              <span v-if="!isLeftBig">{{ $t("创建寻源项目") }}</span>
            </div> -->
            <div class="create-btn" @click="handleCreateProj(index)">
              <i class="mt-icons mt-icon-icon_solid_Createorder" :title="$t('创建RFX')"></i>
              <span v-if="!isLeftBig">{{ $t('创建RFX') }}</span>
            </div>
            <span class="shrink-icon" @click="handleShrink(index)">
              <template v-if="activeIndex == index">
                {{ $t('收起') }}<i class="mt-icons mt-icon-MT_Uparrow"></i>
              </template>
              <template v-else>
                {{ $t('展开') }} <i class="mt-icons mt-icon-MT_DownArrow"></i>
              </template>
            </span>
          </div>
        </div>
        <div class="content-box">
          <!-- <div class="header-box"> -->

          <div class="form-box">
            <div class="form-item">
              <div class="labels">{{ $t('需求名称') }}</div>
              <mt-input
                v-model="item.sourceForm.rfxName"
                float-label-type="Never"
                autocomplete="off"
                :placeholder="$t('请输入需求名称')"
              ></mt-input>
            </div>

            <div class="form-item">
              <div class="labels">{{ $t('推荐合同类型') }}</div>
              <mt-select
                v-model="item.sourceForm.contractType"
                float-label-type="Never"
                :allow-filtering="true"
                :data-source="recContractList"
                :fields="{ text: 'itemName', value: 'id' }"
                :placeholder="$t('请选择推荐合同类型')"
              ></mt-select>
            </div>

            <div class="form-item">
              <div class="labels">{{ $t('采购组织') }}</div>
              <mt-select
                v-model="item.sourceForm.orgnize"
                float-label-type="Never"
                :allow-filtering="true"
                :data-source="purchaseOrganizList"
                :fields="{ text: 'organizationName', value: 'id' }"
                :placeholder="$t('请选择采购组织')"
              ></mt-select>
            </div>

            <div class="form-item">
              <div class="labels">{{ $t('业务类型') }}</div>
              <mt-select
                v-model="item.sourceForm.business"
                float-label-type="Never"
                :allow-filtering="true"
                :data-source="businessTypeList"
                :fields="{ text: 'itemName', value: 'id' }"
                :placeholder="$t('请选择业务类型')"
              ></mt-select>
            </div>

            <div class="form-item">
              <div class="labels">{{ $t('项目策略') }}</div>
              <mt-select
                v-model="item.sourceForm.projs"
                float-label-type="Never"
                :allow-filtering="true"
                :data-source="projStrategyList"
                :fields="{ text: 'itemName', value: 'id' }"
                :placeholder="$t('请选择项目策略')"
              ></mt-select>
            </div>

            <div class="form-item">
              <div class="labels">{{ $t('执行人') }}</div>
              <mt-select
                v-model="item.sourceForm.projj"
                float-label-type="Never"
                :allow-filtering="true"
                :data-source="tenantIdList"
                :fields="{ text: 'employeeName', value: 'id' }"
                :placeholder="$t('请选择执行人')"
              ></mt-select>
            </div>
          </div>
          <!-- </div> -->
          <div class="requires-table">
            <mt-template-page :ref="`requires-table-${index}`" :template-config="item.pageConfig" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  /* eslint-disable vue/require-prop-types */
  props: [
    'isLeftBig',
    'gridColumn',
    'gridData',
    'recContractList',
    'businessTypeList',
    'projStrategyList',
    'purchaseOrganizList',
    'tenantIdList',
    'requireList'
  ],
  data() {
    return {
      activeIndex: 0
    }
  },
  methods: {
    handleShrink(index) {
      if (this.activeIndex == index) {
        this.activeIndex = -1
      } else {
        this.activeIndex = index
      }
    },
    getEnableEditPackage() {
      //返回当前active状态的寻源建议Index
      return this.activeIndex
    },
    getSelectRows() {
      //返回active状态下的选中的数据
      let _index = this.activeIndex
      if (_index < 0) {
        return []
      } else {
        let _currentTabRef = this.$refs[`requires-table-${_index}`][0].getCurrentTabRef()
        return _currentTabRef.grid.getSelectedRecords()
      }
    },
    moveRequestFromListToPackage(list) {
      //将左侧选中的数据移入当前active状态的寻源建议列表
      let _index = this.activeIndex
      let _packages = JSON.parse(
        JSON.stringify(this.requireList[_index]['pageConfig'][0]['grid']['dataSource'])
      )
      for (let i in list) {
        list[i]['isTempField'] = true
        _packages.unshift(list[i])
      }
      this.requireList[_index]['pageConfig'][0]['grid']['dataSource'] = []
      this.$nextTick(function () {
        this.$set(this.requireList[_index]['pageConfig'][0]['grid'], 'dataSource', _packages)
      })
      this.$emit('updatePackagesData')
    },
    popSelectDetails() {
      //将当前active状态下选中的数据移除
      let _selectRows = this.getSelectRows()
      let _index = this.activeIndex
      let _packages = JSON.parse(
        JSON.stringify(this.requireList[_index]['pageConfig'][0]['grid']['dataSource'])
      )
      let _ids = []
      for (let i in _selectRows) {
        _ids.push(_selectRows[i]['id'])
      }
      let _restList = _packages.filter((e) => {
        return _ids.indexOf(e.id) < 0
      })
      this.requireList[_index]['pageConfig'][0]['grid']['dataSource'] = []
      this.$nextTick(function () {
        this.$set(this.requireList[_index]['pageConfig'][0]['grid'], 'dataSource', _restList)
      })
    },

    handleCreateProj() {
      //创建寻源项目
      // 1. 带表单数据过去
      // 2. 带选中的框的ids过去
      let _index = this.activeIndex
      let _packages = JSON.parse(
        JSON.stringify(this.requireList[_index]['pageConfig'][0]['grid']['dataSource'])
      )
      if (_packages.some((item) => item.sourcingStatus == 2)) {
        this.$toast({ type: 'warning', content: this.$t('请选择未创建过的单据') })
        return
      }
      let ids = []
      _packages.forEach((p) => {
        ids.push(p.id)
      })
      let _form = this.requireList[this.activeIndex].sourceForm
      let _sourceForm = {
        rfxName: _form.rfxName, //需求名称
        recContractTypeId: _form.contractType, //合同类型
        purOrgId: _form.orgnize, //采购组织
        businessTypeId: _form.business, //业务类型
        projs: _form.projs, //项目策略
        purExecutorId: _form.projj //执行人
      }
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/sor/mine/components/createSourceProject" */ 'ROUTER_PURCHASE_SOR/mine/components/createSourceProject'
          ),
        data: {
          title: this.$t('创建寻源项目'),
          recContractList: this.recContractList,
          purchaseOrganizList: this.purchaseOrganizList,
          businessTypeList: this.businessTypeList,
          projStrategyList: this.projStrategyList,
          tenantIdList: this.tenantIdList,
          ids,
          formInfo: _sourceForm,
          requestUrl: 'saveRFXDetailPor',
          idListName: 'porItemIdList'
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.source-right {
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 0 8px 0 0;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 10px 20px;
  border-top: none;

  .title-box {
    color: #4f5b6d;
    .mt-icons {
      margin-right: 6px;
      vertical-align: middle;
    }
  }
  .require-list {
    .one-require {
      height: 46px;
      overflow: hidden;
      background: rgba(255, 255, 255, 1);
      border-radius: 4px;
      box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.06);
      margin: 20px 0;
      position: relative;

      &:first-of-type {
        margin-top: 0;
      }
      // .header-box {
      //   padding: 15px 20px 0 28px;
      .top-box {
        display: flex;
        justify-content: space-between;
        color: #232b39;
        padding-bottom: 15px;
        padding: 15px 20px 15px 28px;

        .left-title {
          margin-left: -8px;
          .indexs {
            width: 16px;
            height: 16px;
            background: rgba(237, 161, 51, 1);
            border-radius: 2px;
            font-family: DINAlternate;
            display: inline-block;
            text-align: center;
            color: #fff;
            line-height: 16px;
            margin-right: 10px;
          }
        }

        .right-btns {
          display: flex;
          align-items: center;
          .create-btn {
            cursor: pointer;
            i {
              margin-right: 6px;
              vertical-align: middle;
            }
          }
        }
        .shrink-icon {
          color: #6386c1;
          cursor: pointer;
          margin-left: 20px;
          i {
            font-size: 12px;
            margin-left: 4px;
            vertical-align: middle;
          }
        }
      }

      .form-box {
        width: 332px;
        overflow-x: auto;
        white-space: nowrap;
        // display: flex;
        // justify-content: space-between;
        padding-left: 18px;
        padding-bottom: 20px;
        margin-left: 28px;
        border-left: 1px solid #e8e8e8;
        font-size: 0;
        .form-item {
          width: 155px;
          height: 84px;
          font-size: 14px;
          background: rgba(255, 255, 255, 1);
          border-radius: 4px;
          box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.04);
          flex-shrink: 0;
          display: inline-flex;
          flex-direction: column;
          justify-content: space-between;
          padding: 20px 10px;
          margin-right: 25px;
          border: 1px solid transparent;
          cursor: pointer;

          &:last-child {
            margin-right: 0;
          }

          &:hover {
            border: 1px solid #6386c1;
          }

          .labels {
            color: #465b73;
            font-size: 12px;
            font-weight: 500;
            position: relative;
            padding-left: 8px;
            margin-bottom: 8px;
            &::before {
              content: '';
              position: absolute;
              left: 0;
              top: 0;
              border-top: 6px solid #ff6a77;
              border-right: 6px solid transparent;
            }
          }
          /deep/ .e-input-group {
            border: unset;
            input.e-dropdownlist,
            input.e-input {
              text-align: right;
            }
          }
        }
      }

      .occupy {
        height: 20px;
        margin-left: 28px;
        border-left: 1px solid #e8e8e8;
      }
      //

      .requires-table {
        margin-left: 28px;

        .common-template-page {
          /deep/ .page-grid-container {
            box-shadow: unset;
            border: unset;
            padding: 0 20px 0 0;
            border-radius: 0 !important;
          }
          /deep/ .mt-data-grid {
            height: 256px;
            overflow: hidden;
            > .e-grid {
              overflow: auto;
            }
            th,
            td {
              border-top: none !important;
              border-bottom: none !important;
            }
            .mt-pagertemplate {
              width: 400px;
              border: none;
              padding: 5px 0;
              .mt-page-normal {
                float: none;
              }
            }
            .e-grid {
              border-radius: 0 !important;
              .e-gridcontent,
              .e-gridheader {
                border: none;
              }
              .e-gridheader {
                padding-bottom: 8px !important;
                border-left: 1px solid #e8e8e8;
                border-radius: 0 !important;
                background: transparent;
              }
              th.e-headercell:nth-of-type(1) {
                border-left: none;
              }
              th.e-headercell:nth-of-type(2) {
                border-left: 1px solid #e8e8e8;
              }
              .e-filtermenudiv {
                margin: -12px -7px;
              }
              .e-headercell,
              .e-headercelldiv {
                height: 20px;
              }
              .e-headercelldiv {
                margin: -3px 15px -7px -7px;
                &:first-child {
                  margin: -7px 15px -7px -7px;
                }
              }
              td.e-rowcell {
                height: 32px !important;
                position: relative;
              }
              .e-headercell .e-headerText {
                line-height: 20px;
                vertical-align: middle;
              }
              .e-sortfilterdiv {
                margin: 0;
              }
              td:first-of-type:before {
                content: '';
                background: #e8e8e8;
                height: 1px;
                width: 12px;
                border-radius: 0 4px 4px 0;
                position: absolute;
                top: 15px;
                left: 0;
              }
              td.e-active:first-of-type:before {
                background: #6386c1;
              }
              td:first-of-type {
                border-left: 1px solid #e8e8e8 !important;
              }
              td:first-of-type.e-active {
                border-left: 1px solid #6386c1 !important;
                // animation: list-item-active-animation 0.2s ease;
              }
              @keyframes list-item-active-animation {
                0% {
                  top: 50%;
                  height: 0;
                }
                100% {
                  top: 0;
                  height: 100%;
                }
              }
            }
          }
        }
      }

      &-active {
        height: 405px;
        overflow: hidden;
        border: 1px solid rgba(232, 232, 232, 1);
        background: linear-gradient(
          to top left,
          rgba(99, 134, 193, 0) 0%,
          rgba(99, 134, 193, 0.01)
        );
        // display: flex;
        // flex-direction: column;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          width: 4px;
          top: 0;
          bottom: 0;
          background: rgba(99, 134, 193, 1);
          border-radius: 4px 0 0 4px;
        }

        .content-box {
          width: 1124px;
          position: relative;
          overflow: hidden;
          // flex: 1;
          // height: 359px;
        }
        &::-webkit-scrollbar {
          width: 10px;
        }
        &::-webkit-scrollbar-thumb {
          background: #d8d8d8;
          border-radius: 2px;
        }

        .header-box {
          padding: 15px 10px 0 28px;
        }
      }
    }
  }

  &.right-big {
    .require-list .one-require .form-box {
      width: calc(100% - 28px - 20px);
      margin-right: 20px;
    }
    /deep/ .mt-pagertemplate {
      width: 100% !important;
      overflow: hidden !important;
      padding: 10px 0 !important;
      .mt-page-normal {
        float: right !important;
      }
    }
    .create-btn {
      margin-left: 20px;
    }
  }
}
</style>
