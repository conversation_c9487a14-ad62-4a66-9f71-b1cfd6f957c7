import { i18n } from '@/main.js'
export const requireColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'sourceHeaderCode',
    headerText: i18n.t('源单据编码')
  },
  {
    field: 'sourceHeaderName',
    headerText: i18n.t('源单据名称')
  },
  {
    field: 'lineNo',
    headerText: i18n.t('行号')
  },
  {
    field: 'itemCode',
    headerText: i18n.t('品项代码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('品项名称')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类')
  },
  {
    field: 'siteName',
    headerText: i18n.t('地点/工厂')
  },
  {
    field: 'stockSite',
    headerText: i18n.t('库存地点')
  },
  {
    field: 'xxxName',
    headerText: i18n.t('材料组'),
    valueConverter: { type: 'placeholder', placeholder: i18n.t('无字段') }
  },
  {
    field: 'quantity',
    headerText: i18n.t('数量')
  },
  {
    field: 'unitName',
    headerText: i18n.t('基本单位')
  },
  {
    field: 'requiredDeliveryDate',
    headerText: i18n.t('要求交货日期')
  },
  {
    field: 'purGroupName',
    headerText: i18n.t('采购组')
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商')
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'deptName',
    headerText: i18n.t('申请部门')
  },
  {
    field: 'purName',
    headerText: i18n.t('申请人')
  },
  {
    field: 'applyTime',
    headerText: i18n.t('申请时间')
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
