<template>
  <div class="full-height source-require-page mt-flex-direction-column">
    <div class="source-require-header mt-flex">
      <div class="left-header">{{ $t('寻源需求列表') }}</div>
      <div class="handle-header"></div>
      <div :class="['right-header', !isLeftBig && 'right-big']" class="">
        {{ $t('寻源需求建议列表') }}
      </div>
    </div>
    <div class="source-require-content mt-flex">
      <source-list ref="sourceListContentRef" :page-config="reuireRestListConfig"></source-list>

      <div class="cross-handle mt-flex-direction-column">
        <div :class="['handle-tag', { 'open-panel': isLeftBig }]" @click="isLeftBig = !isLeftBig">
          {{ isLeftBig ? $t('展开') : $t('收起') }}
        </div>

        <mt-tooltip
          opens-on="Custom"
          target=".cross-right"
          :content="moveToPackageTag.msg"
          ref="tooltipForMoveToPackageList"
        >
          <div
            :class="[
              'cross-tag',
              'cross-right',
              {
                'cross-disabled': !moveToPackageTag.enableMove
              }
            ]"
            @click="moveToPackageList"
            @mouseenter="openTipsForMoveToPackageList"
            @mouseleave="closeTipsForMoveToPackageList"
          ></div>
        </mt-tooltip>
        <mt-tooltip
          target=".cross-left"
          class="cross-left"
          opens-on="Custom"
          :content="moveToRequestTag.msg"
          ref="tooltipForMoveToRequestList"
        >
          <div
            :class="[
              'cross-tag',
              'cross-left',
              {
                'cross-disabled': !moveToRequestTag.enableMove
              }
            ]"
            @click="moveToRequestList"
            @mouseenter="openTipsForMoveToRequestList"
            @mouseleave="closeTipsForMoveToRequestList"
          ></div>
        </mt-tooltip>
      </div>

      <source-right
        ref="sourcePackageRef"
        :class="[!isLeftBig && 'right-big']"
        :is-left-big="isLeftBig"
        :grid-column="requireColumnData"
        :require-list="requireGroupListConfig"
        :rec-contract-list="recContractList"
        :business-type-list="businessTypeList"
        :proj-strategy-list="projStrategyList"
        :purchase-organiz-list="purchaseOrganizList"
        :tenant-id-list="tenantIdList"
      ></source-right>
    </div>
  </div>
</template>

<script>
import { requireColumnData } from './config'
export default {
  components: {
    sourceList: () =>
      import(
        /* webpackChunkName: "router/purchase/sor/grouping/components/sourceList" */ './components/sourceList.vue'
      ),
    sourceRight: () =>
      import(
        /* webpackChunkName: "router/purchase/sor/grouping/components/sourceRight" */ './components/sourceRight.vue'
      )
  },
  data() {
    return {
      groupConfigParams: {},
      moveToRequestTag: {},
      moveToPackageTag: {},
      requireColumnData,
      reuireRestListConfig: [
        {
          grid: {
            allowFiltering: true,
            columnData: this.requireColumnData,
            dataSource: []
          }
        }
      ], //左侧列表配置
      requireGroupListConfig: [], //右侧列表配置
      isLeftBig: true, // 是否是左侧大
      recContractList: [], // 推荐合同类型select数据源
      businessTypeList: [], // 业务类型select数据源
      projStrategyList: [], // 项目策略elect数据源
      purchaseOrganizList: [], // 采购组织select数据源
      tenantIdList: [] // 执行人select数据源
    }
  },
  mounted() {
    if (localStorage.requireConfigParams) {
      this.groupConfigParams = JSON.parse(localStorage.requireConfigParams)
    }

    this.getLists()
    this.getUserListByTenantId()
    this.getSourceRequireList()
  },
  methods: {
    getSourceRequireList() {
      let {
        conditions,
        groupFields,
        businessTypeId, //业务类型
        contactType, //合同类型
        purchaseOrgId, //组织机构ID
        porItemIdList
        // strategyName, //策略名称
      } = this.groupConfigParams
      let _params = {
        conditions,
        groupFields,
        porItemIdList
      }
      this.$API.moduleConfig.getStrategyConfigGroupProposalList(_params).then((res) => {
        let { groupData, restData } = res.data
        let _requests = []
        for (let i in groupData) {
          _requests.push({
            title: `${this.$t('寻源需求建议')}${i + 1}`,
            sourceForm: {
              names: '', //需求名称
              contractType: contactType, //合同类型
              orgnize: purchaseOrgId, //采购组织
              business: businessTypeId, //业务类型
              projs: '', //项目策略
              projj: '' //执行人
            },
            pageConfig: [
              {
                grid: {
                  allowFiltering: true,
                  columnData: this.requireColumnData,
                  dataSource: groupData[i]
                }
              }
            ]
          })
        }
        this.reuireRestListConfig[0]['grid']['dataSource'] = []
        this.requireGroupListConfig = []
        this.$nextTick(() => {
          this.$set(this.reuireRestListConfig[0]['grid'], 'dataSource', restData)
          this.$set(this, 'requireGroupListConfig', _requests)
        })
      })
    },
    openTipsForMoveToPackageList(args) {
      let _selectRows = this.$refs.sourceListContentRef.getSelectRows()
      if (_selectRows.length < 1) {
        this.moveToPackageTag = {
          msg: this.$t("当前，'需求池'中，没有选中数据."),
          enableMove: false
        }
      } else {
        let _editPackageIndex = this.$refs.sourcePackageRef.getEnableEditPackage()
        if (_editPackageIndex < 0) {
          this.moveToPackageTag = {
            msg: this.$t('当前，右侧不存在选中状态的需求建议.'),
            enableMove: false
          }
        } else {
          this.moveToPackageTag = {
            msg: this.$t("点击，将'需求池'中选中的数据，移至需求建议列表."),
            enableMove: true
          }
        }
      }
      this.$refs.tooltipForMoveToPackageList.open(args.target)
    },
    closeTipsForMoveToPackageList(args) {
      this.moveToPackageTag.enableMove = false
      this.$refs.tooltipForMoveToPackageList.close(args.target)
    },
    moveToPackageList(args) {
      let _selectRows = this.$refs.sourceListContentRef.getSelectRows()
      if (_selectRows.length < 1) {
        // tooltip:"当前，'需求池'中，没有选中数据."
      } else {
        let _editPackageIndex = this.$refs.sourcePackageRef.getEnableEditPackage()
        if (_editPackageIndex < 0) {
          // tooltip:"当前，不存在选中状态的寻源建议"
        } else {
          this.$refs.sourcePackageRef.moveRequestFromListToPackage(_selectRows) //数据移动至寻源建议列表
          this.$refs.sourceListContentRef.popSelectRows() //移除数据
          // init tooltip status
          this.closeTipsForMoveToPackageList(args)
        }
      }
    },
    openTipsForMoveToRequestList(args) {
      let _editPackageIndex = this.$refs.sourcePackageRef.getEnableEditPackage()
      if (_editPackageIndex < 0) {
        this.moveToRequestTag = {
          msg: this.$t('当前，不存在选中状态的寻源建议'),
          enableMove: false
        }
      } else {
        let _selectRows = this.$refs.sourcePackageRef.getSelectRows()
        if (_selectRows.length < 1) {
          this.moveToRequestTag = {
            msg: this.$t("当前，未选择移动至'需求池'的寻源明细"),
            enableMove: false
          }
        } else {
          this.moveToRequestTag = {
            msg: this.$t("点击，将选中的条目，移至'需求池'"),
            enableMove: true
          }
        }
      }
      this.$refs.tooltipForMoveToRequestList.open(args.target)
    },
    closeTipsForMoveToRequestList(args) {
      this.moveToRequestTag.enableMove = false
      this.$refs.tooltipForMoveToRequestList.close(args.target)
    },
    moveToRequestList(args) {
      let _editPackageIndex = this.$refs.sourcePackageRef.getEnableEditPackage()
      if (_editPackageIndex < 0) {
        // tooltip:"当前，不存在编辑状态的寻源包";
      } else {
        let _selectRows = this.$refs.sourcePackageRef.getSelectRows()
        if (_selectRows.length < 1) {
          // tooltip:"当前，未选择移动至'需求池'的寻源明细";
        } else {
          this.$refs.sourceListContentRef.moveRequestFromPackageToList(_selectRows)
          this.$refs.sourcePackageRef.popSelectDetails()
          // init tooltip status
          this.closeTipsForMoveToRequestList(args)
        }
      }
    },
    getLists() {
      // 推荐合同类型 - contractType
      this.$API.masterData
        .dictionaryGetList({
          dictCode: 'contractType'
        })
        .then((res) => {
          this.recContractList = res.data
        })

      // 业务类型 - businessType
      this.$API.masterData
        .dictionaryGetList({
          dictCode: 'businessType'
        })
        .then((res) => {
          this.businessTypeList = res.data
        })

      // 项目策略 - policyType
      this.$API.masterData
        .dictionaryGetList({
          dictCode: 'policyType'
        })
        .then((res) => {
          this.projStrategyList = res.data
        })

      // 采购组织 -
      this.$API.masterData
        .purchaseOraginaze({
          organizationTypeCode: 'BUORG002ADM'
        })
        .then((res) => {
          this.purchaseOrganizList = res.data
        })

      // 申请部门
      this.$API.masterData
        .getDepartmentList({
          departmentName: ''
        })
        .then((res) => {
          this.departmentList = res.data
        })
    },

    // 获取弹窗中的人员列表
    getUserListByTenantId() {
      this.$API.masterData.getUserListByTenantId({ employeeName: '' }).then((res) => {
        this.tenantUserList = res.data
        this.tenantIdList = res.data
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.inlinebox {
  height: 100%;
  overflow: auto;
  display: inline-block;
  font-size: 14px;
  vertical-align: top;
}
.source-require-page {
  width: 100%;
  overflow: auto;
  font-size: 0;

  .source-require-header {
    width: 100%;
    height: 50px;
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    .left-header {
      flex: 1;
      font-size: 16px;
      color: #232b39;
      font-weight: 500;
      line-height: 50px;
      padding-left: 40px;
      height: 50px;
    }
    .handle-header {
      width: 60px;
      height: 50px;
    }
    .right-header {
      font-size: 16px;
      color: #232b39;
      font-weight: 500;
      line-height: 50px;
      padding-left: 20px;
      width: 400px;
      height: 50px;
      animation: close-card-animation 0.5s ease-in-out;
      @extend .inlinebox;
      &.right-big {
        width: 1168px;
        animation: open-card-animation 0.5s ease-in-out;
      }
    }
  }

  .source-require-content {
    flex: 1;

    .source-list {
      flex: 1;
      @extend .inlinebox;
    }
    .cross-handle {
      width: 60px;
      background: #fbfbfb;
      justify-content: center;
      align-items: center;
      position: relative;
      border-left: 1px solid #e8e8e8;

      .handle-tag {
        line-height: 1;
        font-size: 14px;
        position: absolute;
        color: #9a9a9a;
        top: 0;
        width: 60px;
        text-align: center;
        cursor: pointer;
        padding: 15px 0 15px 15px;
        &:before,
        &:after {
          content: '';
          width: 6px;
          height: 6px;
          background: 0 0;
          border-left: none;
          border-top: none;
          border-right: 1px solid #9a9a9a;
          border-bottom: 1px solid #9a9a9a;
          position: absolute;
          transform: rotate(-45deg);
          cursor: pointer;
        }
        &:before {
          top: 19px;
          left: 8px;
        }
        &:after {
          top: 19px;
          left: 13px;
        }

        &.open-panel {
          &:before,
          &:after {
            content: '';
            width: 6px;
            height: 6px;
            background: 0 0;
            border-left: 1px solid #9a9a9a;
            border-top: 1px solid #9a9a9a;
            border-right: none;
            border-bottom: none;
            position: absolute;
            transform: rotate(-45deg);
          }
        }
      }

      .mt-tooptip {
        width: auto;
        margin-top: 30px;
        &:first-of-type {
          margin-top: 0;
        }
        .cross-tag {
          background: #fbfbfb;
          height: 30px;
          width: 30px;
          border-radius: 50%;
          position: relative;
          border: 1px solid #9daabf;
          &:after {
            content: '';
            width: 10px;
            height: 10px;
            background: 0 0;
            border-left: none;
            border-top: none;
            border-right: 1px solid #9daabf;
            border-bottom: 1px solid #9daabf;
            position: absolute;
            top: 9px;
            left: 6px;
            transform: rotate(-45deg);
          }
          &.cross-left {
            &:after {
              border-left: 1px solid #9daabf;
              border-top: 1px solid #9daabf;
              border-right: none;
              border-bottom: none;
              left: 10px;
            }
          }
          &:hover {
            border-color: #6386c1;
            &:after {
              border-color: #6386c1;
            }
          }
          &.cross-disabled {
            &:hover {
              border-color: #9daabf;
              &:after {
                border-color: #9daabf;
              }
            }
          }
        }
      }
    }
    .source-right {
      width: 400px;
      animation: close-card-animation 0.5s ease-in-out;
      @extend .inlinebox;
      &.right-big {
        width: 1168px;
        animation: open-card-animation 0.5s ease-in-out;
      }
    }
  }

  @keyframes open-card-animation {
    0% {
      width: 400px;
    }
    100% {
      width: 1168px;
    }
  }
  @keyframes close-card-animation {
    0% {
      width: 1168px;
    }
    100% {
      width: 400px;
    }
  }
}
</style>
