<template>
  <div class="quota-container">
    <Calculator :item-list="items" @success="success" @error="error" />
  </div>
</template>

<script>
import Calculator from 'COMPONENTS/Calculator'
export default {
  name: 'QuotaList',
  components: {
    Calculator
  },
  data() {
    return {
      items: [
        { text: this.$t('进检重复次数'), value: 'N1' },
        { text: this.$t('返工后不良数量'), value: 'N2' },
        { text: this.$t('月进货合格批次数量'), value: 'E' },
        { text: this.$t('特采次数'), value: 'N3' },
        { text: this.$t('当月进货批次总数量'), value: 'F' },
        { text: this.$t('月供货批次'), value: 'G' }
      ]
    }
  },
  mounted() {},
  methods: {
    success(formula) {
      console.log(formula)
    },
    error() {
      this.$toast({ type: 'error', content: this.$t('公式错误') })
    }
  }
}
</script>
