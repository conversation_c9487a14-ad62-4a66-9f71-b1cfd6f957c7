<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>
<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      pageConfig: pageConfig(this.$API.quotaConfig.getQuotaDetailList)
    }
  },
  methods: {
    async exportData() {
      const params = {}
      const asyncParams = this.$refs.templateRef.getAsyncParams()
      if (asyncParams.rules) {
        params.rules = asyncParams.rules
      }
      const res = await this.$API.quotaConfig.itemExport(params).catch((e) => console.warn(e))
      if (res) {
        const blob = new Blob([res.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
        const fileName = res.headers['content-disposition']
          .split(';')[1]
          .split('=')[1]
          .split('.xlsx')[0]
        const reader = new FileReader()
        reader.readAsDataURL(blob)
        reader.onload = (e) => {
          const a = document.createElement('a')
          a.download = decodeURIComponent(fileName) + '.xlsx'
          if (typeof e.target.result === 'string') {
            a.href = e.target.result
          }
          a.click()
        }
      }
    },
    //表格头部按钮-点击事件
    handleClickToolBar(e) {
      if (e.toolbar.id == 'Export1') {
        this.exportData()
        return
      }

      let _selectGridRecords = e.grid.getSelectedRecords()
      if (_selectGridRecords.length == 0) {
        this.$toast({
          content: this.$t('请至少选择选择一行数据'),
          type: 'warning'
        })
        return
      }
      let arr = _selectGridRecords.map((item) => {
        return item.id
      })
      this.$API.quotaConfig
        .pricerecordQuota({
          ids: arr
        })
        .then((res) => {
          if (res.code == 200) {
            this.$toast({
              content: this.$t('同步成功'),
              type: 'success'
            })
            this.formObject.quotaCode = res.data
          } else {
            this.$toast({
              content: res.msg ? res.msg : this.$t('同步失败'),
              type: 'warning'
            })
          }
        })
    },
    //单元格按钮
    handleClickCellTool() {},
    //单元格标题
    handleClickCellTitle() {}
  }
}
</script>
