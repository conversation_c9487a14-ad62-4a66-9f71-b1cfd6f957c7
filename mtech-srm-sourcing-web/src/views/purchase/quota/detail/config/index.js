import { i18n, permission } from '@/main.js'
import Vue from 'vue'
import Decimal from 'decimal.js'
const toolbar = [
  { id: 'Sync', icon: 'icon_solid_Createorder', title: i18n.t('同步') },
  { id: 'Export1', icon: 'icon_solid_Download', title: i18n.t('导出') }
]
const columnData = [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    field: 'lineNo',
    width: '85',
    headerText: i18n.t('行号')
  },
  {
    field: 'quotaCode',
    headerText: i18n.t('配额协议编码')
    // cssClass: "field-content",
  },
  {
    field: 'quotaName',
    headerText: i18n.t('配额协议描述')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('失效'), 1: i18n.t('生效'), 3: i18n.t('未合格') }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂')
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'sourceCode',
    headerText: i18n.t('来源单号')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('生效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('失效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'quotaPercent',
    headerText: i18n.t('配额'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<span>{{quotaPercent}}%</span>`,
          data() {
            return { data: {} }
          },
          computed: {
            quotaPercent() {
              return new Decimal(this.data.quotaPercent).mul(new Decimal(100))
            }
          }
        })
      }
    }
  },
  {
    field: 'minOpenQuantity',
    headerText: i18n.t('最小起拆量')
  },
  {
    field: 'minPurchaseQuantity',
    headerText: i18n.t('最小采购量')
  },
  {
    field: 'minPackageQuantity',
    headerText: i18n.t('最小包装量')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    field: 'syncStatus',
    headerText: i18n.t('同步结果'),
    valueConverter: {
      type: 'map',
      map: [
        { status: 0, label: i18n.t('无需同步'), cssClass: '' },
        { status: 1, label: i18n.t('未同步'), cssClass: '' },
        { status: 2, label: i18n.t('同步中'), cssClass: '' },
        { status: 3, label: i18n.t('同步成功'), cssClass: '' },
        { status: 4, label: i18n.t('同步失败'), cssClass: '' }
      ],
      fields: { text: 'label', value: 'status' }
    }
  },
  {
    field: 'syncInfo',
    headerText: i18n.t('返回信息')
  },
  {
    field: 'sourceType',
    headerText: i18n.t('创建方式'),
    valueConverter: {
      type: 'map',
      map: [
        { status: 0, label: i18n.t('手动创建'), cssClass: '' },
        { status: 1, label: i18n.t('寻源生成'), cssClass: '' }
      ],
      fields: { text: 'label', value: 'status' }
    }
  }
]
export const pageConfig = (url) => [
  {
    toolbar,
    useToolTemplate: false,
    gridId: permission.gridId['purchase']['quotaDetail'],
    grid: { allowFiltering: true, columnData, asyncConfig: { url } }
  }
]
