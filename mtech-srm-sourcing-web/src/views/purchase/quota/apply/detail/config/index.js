import { i18n } from '@/main.js'
import Vue from 'vue'
import Decimal from 'decimal.js'
export const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'Edit', icon: 'icon_solid_edit', title: i18n.t('编辑') },
  { id: 'Del', icon: 'icon_table_delete', title: i18n.t('删除') }
  // { id: "Start", icon: "icon_solid_Createorder", title: i18n.t("保存") },
  // { id: "Import", icon: "icon_solid_Createorder", title: i18n.t("导入") },
]
export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'lineNo',
    headerText: i18n.t('行号')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'), //单据状态 -1:已停用 0:草稿 1:审批中 2:审批驳回 3:已生效
    valueConverter: {
      type: 'map',
      map: [
        { status: 0, label: i18n.t('失效'), cssClass: 'title-#9baac1' },
        { status: 1, label: i18n.t('生效'), cssClass: 'title-#eda133' }
      ],
      fields: { text: 'label', value: 'status' }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂')
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'quoteAttribute',
    headerText: i18n.t('报价属性'),
    valueConverter: {
      type: 'map',
      map: {
        mailing_price: i18n.t('寄售价'),
        standard_price: i18n.t('标准价')
      }
    }
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('生效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('失效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'minOpenQuantity',
    headerText: i18n.t('最小起拆量')
  },
  {
    field: 'unitName',
    headerText: i18n.t('单位'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<span>{{data.unitName}}/{{data.unitCode}}</span>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'quotaPercent',
    headerText: i18n.t('配额比（%）'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<span>{{quotaPercent}}</span>`,
          data() {
            return { data: {} }
          },
          computed: {
            quotaPercent() {
              return new Decimal(this.data.quotaPercent).mul(new Decimal(100))
            }
          }
        })
      }
    }
  },
  {
    field: 'minPackageQuantity',
    headerText: i18n.t('最小包装量')
  },
  {
    field: 'minPurchaseQuantity',
    headerText: i18n.t('最小采购量')
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
// export const pageConfig = (data) => [
//   {
//     toolbar,
//     grid: { allowFiltering: true,
//       columnData,
//       dataSource: data,
//     },
//   },
// ];
