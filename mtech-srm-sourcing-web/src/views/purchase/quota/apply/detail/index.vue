<template>
  <div class="supplier-box mt-flex">
    <div class="miam-container">
      <div class="operate-bar">
        <div class="op-item mt-flex" @click="saveDetail">{{ $t('保存') }}</div>
        <div class="op-item mt-flex" @click="submit">{{ $t('提交') }}</div>
        <div class="op-item mt-flex" @click="backToBusinessConfig">
          {{ $t('返回') }}
        </div>
      </div>
      <!-- <input type="file" accept=".xlsx" hidden ref="excel-uploader" @change="fileChange"/> -->
      <!-- 顶部主要信息 -->
      <div class="mian-info">
        <mt-form ref="ruleForm" :model="formObject" :rules="formRules">
          <mt-form-item :label="$t('配额协议编码：')">
            <mt-input
              width="100%"
              :readonly="true"
              type="text"
              v-model="formObject.quotaCode"
              :placeholder="$t('请输入配额协议编码')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item :label="$t('配额协议描述：')" prop="quotaName">
            <mt-input
              width="100%"
              type="text"
              v-model="formObject.quotaName"
              :placeholder="$t('请输入配额协议描述')"
              :max-length="110"
            ></mt-input>
          </mt-form-item>
          <mt-form-item :label="$t('备注：')" style="width: calc(40% - 20px)">
            <mt-input
              width="100%"
              type="text"
              v-model="formObject.remark"
              :placeholder="$t('字数不超过200字')"
              :max-length="200"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
      <div class="relation-ships">
        <p>{{ $t('额度分配') }}</p>
        <mt-template-page
          ref="templateRef"
          :template-config="pageConfig"
          @handleClickToolBar="handleClickToolBar"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { toolbar, columnData } from './config'
export default {
  components: {},
  data() {
    return {
      formObject: {
        // 配额编码
        quotaCode: null,
        // 配额名称
        quotaName: null,
        // 备注
        remark: null,
        // 配额明细列表
        quotaItemList: []
      },
      pageConfig: [
        {
          toolbar,
          grid: { allowFiltering: true, columnData, dataSource: [] }
        }
      ],
      type: 'add',
      formRules: {
        quotaName: [
          {
            required: true,
            message: this.$t('请输入配额协议描述'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  methods: {
    // fileChange(event){
    //   let file = event.target.files[0];
    //   if (file) {
    //     const formData = new FormData();
    //     formData.append("file", file);
    //     this.$API.quotaConfig.excelimport(formData).then(() => {
    //       this.$toast({ type: "success", content: "导入成功" });
    //       this.$refs.templateRef.refreshCurrentGridData();
    //     });
    //   }
    // },
    saveDetail() {
      this.$refs.ruleForm.validate((val) => {
        if (val) {
          if (!this.formObject.quotaCode) {
            this.$API.quotaConfig.addQuotaApply(this.formObject).then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('添加成功'),
                  type: 'success'
                })
                this.formObject.quotaCode = res.data
              }
            })
          } else {
            this.$API.quotaConfig.modifyQuotaApply(this.formObject).then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('编辑成功'),
                  type: 'success'
                })
              }
            })
          }
        }
      })
    },
    submit() {
      this.$dialog({
        data: {
          title: this.$t('操作'),
          message: this.$t(`请在保存后提交，确认提交吗？`)
        },
        success: () => {
          this.$API.quotaConfig.submitQuotaApply([this.formObject.quotaCode]).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('提交成功'),
                type: 'success'
              })
              this.$router.go(-1)
            }
          })
        }
      })
    },
    backToBusinessConfig() {
      this.$router.go(-1)
    },
    handleClickToolBar(e) {
      let _selectGridRecords = e.grid.getSelectedRecords()
      if (_selectGridRecords.length < 1 && (e.toolbar.id == 'Del' || e.toolbar.id == 'Edit')) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        this.handleAddDetail()
      } else if (e.toolbar.id == 'Edit') {
        this.handleEditDetail(_selectGridRecords[0])
      } else if (e.toolbar.id == 'Del') {
        this.handleDelDetail(_selectGridRecords)
      }
      // else if(e.toolbar.id == 'Import'){
      //   this.$refs["excel-uploader"].click();
      // }
    },
    // 添加配额
    handleAddDetail() {
      this.$dialog({
        modal: () => import('./components/quotaDetailDialog/index.vue'),
        data: {
          title: this.$t('新增额度分配')
        },
        success: (val) => {
          let obj = {
            ...val
          }
          let index = 0
          this.formObject.quotaItemList.forEach((item) => {
            if (item.lineNo > index) {
              index = item.lineNo
            }
          })
          obj.lineNo = index + 1
          if (Array.isArray(obj.validTime) && obj.validTime.length === 2) {
            obj.validStartTime = new Date(obj.validTime[0]).getTime()
            obj.validEndTime = new Date(obj.validTime[1]).getTime()
          }
          obj.quotaPercent = obj.quotaPercent / 100
          delete obj.validTime
          this.formObject.quotaItemList.push(obj)
        }
      })
    },
    // 编辑配额
    handleEditDetail(val) {
      this.$dialog({
        modal: () => import('./components/quotaDetailDialog/index.vue'),
        data: {
          title: this.$t('编辑额度分配'),
          info: val
        },
        success: (val) => {
          let obj = {
            ...val
          }
          if (Array.isArray(obj.validTime) && obj.validTime.length === 2) {
            obj.validStartTime = new Date(obj.validTime[0]).getTime()
            obj.validEndTime = new Date(obj.validTime[1]).getTime()
          }
          let index = -1
          this.formObject.quotaItemList.forEach((e, i) => {
            if (e.lineNo == val.lineNo) {
              index = i
            }
          })
          obj.quotaPercent = obj.quotaPercent / 100
          this.formObject.quotaItemList.splice(index, 1, obj)
        }
      })
    },
    // 删除、
    handleDelDetail(arr) {
      let info = arr.map((item) => {
        return item.lineNo
      })
      let list = this.formObject.quotaItemList
      for (let i = 0; i < list.length; i++) {
        if (info.indexOf(list[i].lineNo) != -1) {
          list.splice(i, 1)
          i--
        }
      }
    }
  },
  mounted() {
    this.type = this.$route.query.type
    if (this.type == 'edit') {
      this.$API.quotaConfig
        .getQuotaApplyDetail({
          quotaCode: this.$route.query.quotaCode
        })
        .then((res) => {
          if (res.code == 200) {
            this.formObject = res.data
            this.formObject.quotaItemList.forEach((item) => {
              item.validEndTime = new Date(item.validEndTime).getTime()
              item.validStartTime = new Date(item.validStartTime).getTime()
            })
            this.$set(this.pageConfig[0].grid, 'dataSource', this.formObject.quotaItemList)
          }
        })
    } else {
      this.$set(this.pageConfig[0].grid, 'dataSource', this.formObject.quotaItemList)
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/.mt-form-item {
  width: calc(20% - 20px);
  min-width: 200px;
  display: inline-flex;
  margin-right: 20px;
}
.supplier-box {
  position: absolute;
  width: 100%;
  min-height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: #fff;
  overflow: auto;
  -webkit-overflow-scrolling: scroll;

  .miam-container {
    width: 100%;
    background: #fff;
    padding: 0 20px;

    .operate-bar {
      height: 60px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      color: #4f5b6d;

      .op-item {
        cursor: pointer;
        align-items: center;
        margin-right: 20px;
        align-items: center;
        background-color: rgba(0, 0, 0, 0);
        border-color: rgba(0, 0, 0, 0);
        color: #00469c;
      }
    }

    .mian-info {
      background: rgba(255, 255, 255, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    }

    .relation-ships {
      background: rgba(255, 255, 255, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      margin-top: 20px;
      height: calc(100% - 164px);
    }
  }
}
</style>
