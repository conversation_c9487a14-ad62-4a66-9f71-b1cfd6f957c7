import { i18n } from '@/main.js'
import masterData from '@/apis/modules/service/masterData'
export const categoryTemplateConfig = [
  {
    useToolTemplate: false,
    toolbar: [],
    grid: {
      allowFiltering: true,
      height: 300,
      allowSelection: true,
      selectionSettings: {
        checkboxOnly: false
      },
      columnData: [
        {
          width: '150',
          field: 'itemCode',
          headerText: i18n.t('物料编号')
        },
        { width: '150', field: 'itemName', headerText: i18n.t('物料名称') },
        {
          width: '150',
          field: 'categoryResponse.categoryCode',
          headerText: i18n.t('品类')
        },
        {
          width: '150',
          field: 'itemDescription',
          headerText: i18n.t('规格型号')
        },
        {
          width: '150',
          field: 'oldItemCode',
          headerText: i18n.t('旧物料编号')
        },
        {
          width: '150',
          field: 'manufacturerName',
          headerText: i18n.t('制造商')
        }
      ],
      // dataSource: [],
      asyncConfig: {
        url: masterData.APIS.getItemListUrlPage
        // recordsPosition: "data",
      }
    }
  }
]
