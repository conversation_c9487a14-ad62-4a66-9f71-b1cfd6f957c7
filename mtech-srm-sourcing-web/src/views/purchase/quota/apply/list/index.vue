<template>
  <div class="full-height">
    <input type="file" accept=".xlsx" hidden ref="excel-uploader" @change="fileChange" />
    <mt-template-page
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
    <!-- 上传弹窗 -->
    <UploadExcelDialog
      ref="uploadInitBalanceRef"
      file-key="file"
      :down-template-name="$t('模板')"
      :request-urls="requestUrls"
      :down-template-params="{
        page: {
          current: 1,
          size: 10
        },
        rules: []
      }"
      @closeUploadExcel="uploadClose"
      @upExcelConfirm="uploadConfirm"
    />
  </div>
</template>
<script>
import { pageConfig } from './config'
export default {
  components: {
    UploadExcelDialog: () => import('@/components/Upload/uploadExcelDialog.vue')
  },
  data() {
    return {
      pageConfig: pageConfig(this.$API.quotaConfig.getQuotaApplyList),
      requestUrls: {
        templateUrlPre: 'quotaConfig',
        templateUrl: 'exportTpl',
        uploadUrl: 'excelimport'
      }
    }
  },
  methods: {
    uploadClose() {
      this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.hide()
    },
    uploadConfirm() {
      this.uploadClose()
      this.$refs.templateRef.refreshCurrentGridData()
    },
    uploadOpen() {
      this.$refs.uploadInitBalanceRef.uploadData = [] // 清空数据
      this.$refs.uploadInitBalanceRef.$refs.uploader.files = []
      this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.show()
    },
    fileChange(event) {
      let file = event.target.files[0]
      if (file) {
        const formData = new FormData()
        formData.append('file', file)
        this.$API.quotaConfig.excelimport(formData).then(() => {
          this.$toast({ type: 'success', content: this.$t('导入成功') })
          this.$refs.templateRef.refreshCurrentGridData()
        })
      }
    },
    //表格头部按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.grid.getSelectedRecords()
      if (_selectGridRecords.length < 1 && (e.toolbar.id == 'Stop' || e.toolbar.id == 'Submit')) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        this.handleAddQuota()
      } else if (e.toolbar.id == 'Submit') {
        this.submit(_selectGridRecords)
      } else if (e.toolbar.id == 'Stop') {
        this.cancel(_selectGridRecords)
      }
      if (e.toolbar.id == 'Import') {
        this.uploadOpen()
      }
    },
    cancel(val) {
      let bol = val.every((item) => {
        return item.status == 1
      })
      if (!bol) {
        this.$toast({
          content: this.$t('请选择审批中的数据！'),
          type: 'warning'
        })
        return
      }
      let arr = val.map((item) => {
        return item.quotaCode
      })
      this.$API.quotaConfig.quotaCance(arr).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('取消成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
        } else {
          this.$toast({
            content: res.msg ? res.msg : this.$t('取消失败'),
            type: 'warning'
          })
        }
      })
    },
    // 提交
    submit(val) {
      let bol = val.every((item) => {
        return item.status == 0 || item.status == 2 || item.status == 4
      })
      if (!bol) {
        this.$toast({
          content: this.$t('请选择草稿、驳回、取消的数据！'),
          type: 'warning'
        })
        return
      }
      let arr = val.map((item) => {
        return item.quotaCode
      })
      this.$API.quotaConfig.submitQuotaApply(arr).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('提交成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
        } else {
          this.$toast({
            content: res.msg ? res.msg : this.$t('提交失败'),
            type: 'warning'
          })
        }
      })
    },
    // 新增
    handleAddQuota() {
      this.$router.push({
        name: `purchase-quota-apply-detail`,
        query: {
          type: 'add',
          key: this.$utils.randomString()
        }
      })
    },
    //单元格按钮
    handleClickCellTool() {
      console.log('单元格按钮')
    },
    //单元格标题
    handleClickCellTitle(e) {
      if (e.field == 'quotaCode') {
        this.$router.push({
          name: `purchase-quota-apply-detail`,
          query: {
            type: 'edit',
            quotaCode: e.data.quotaCode
          }
        })
      }
    }
  }
}
</script>
