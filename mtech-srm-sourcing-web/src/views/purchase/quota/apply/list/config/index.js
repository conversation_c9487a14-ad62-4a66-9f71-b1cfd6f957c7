import { i18n, permission } from '@/main.js'
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'Stop', icon: 'icon_solid_Cancel', title: i18n.t('取消') },
  { id: 'Submit', icon: 'icon_solid_Submit', title: i18n.t('提交') },
  { id: 'Import', icon: 'icon_solid_Createorder', title: i18n.t('导入') }
]
const columnData = [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    field: 'quotaCode',
    headerText: i18n.t('配额协议编码'),
    cssClass: 'field-content'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'), //单据状态 -1:已停用 0:草稿 1:审批中 2:审批驳回 3:已生效
    valueConverter: {
      type: 'map',
      map: [
        { status: 0, label: i18n.t('草稿'), cssClass: 'title-#9baac1' },
        { status: 1, label: i18n.t('审批中'), cssClass: 'title-#eda133' },
        { status: 2, label: i18n.t('驳回'), cssClass: 'title-#ed5633' },
        { status: 3, label: i18n.t('通过'), cssClass: 'title-#6386c1' },
        { status: 4, label: i18n.t('取消'), cssClass: 'title-#9a9a9a' }
      ],
      fields: { text: 'label', value: 'status' }
    }
  },
  {
    field: 'quotaName',
    headerText: i18n.t('配额协议描述')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    field: 'sourceType',
    headerText: i18n.t('创建方式'),
    valueConverter: {
      type: 'map',
      map: [
        { status: 0, label: i18n.t('手动创建'), cssClass: '' },
        { status: 1, label: i18n.t('寻源生成'), cssClass: '' }
      ],
      fields: { text: 'label', value: 'status' }
    }
  }
]
export const pageConfig = (url) => [
  {
    toolbar,
    useToolTemplate: false,
    gridId: permission.gridId['purchase']['quotaApply'],
    grid: { allowFiltering: true, columnData, asyncConfig: { url } }
  }
]
