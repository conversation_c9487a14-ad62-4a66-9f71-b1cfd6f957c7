<template>
  <div class="full-height">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
          <mt-select
            v-model="searchFormModel.companyCode"
            css-class="rule-element"
            :data-source="companyList"
            :fields="{ text: 'text', value: 'orgCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择公司')"
          />
        </mt-form-item>
        <mt-form-item prop="materialCode" :label="$t('物料')" label-style="top">
          <magnifier-input
            ref="materialCode"
            :config="materialDialogCofig"
            @change="(e) => (searchFormModel.materialCode = e.itemCode || null)"
          />
        </mt-form-item>
        <mt-form-item prop="categoryCode" :label="$t('品类')" label-style="top">
          <magnifier-input
            ref="categoryCode"
            :config="categoryDialogCofig"
            @change="(e) => (searchFormModel.categoryCode = e.categoryCode || null)"
          />
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商')" label-style="top">
          <magnifier-input
            ref="supplierCode"
            :config="supplierDialogCofig"
            @change="(e) => (searchFormModel.supplierCode = e.supplierCode || null)"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      row-id="customId"
      grid-id="3192c6e9-5684-4115-b1a0-8a6ae6b6235c"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import magnifierInput from '@/components/magnifierInput'
import mixin from './config/mixin'
import pagingMixin from '@/mixins/paging.js'
import { dialogPageConfig } from './config/index'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: { ScTable, CollapseSearch, magnifierInput },
  mixins: [mixin, pagingMixin],
  data() {
    return {
      searchFormModel: {},
      tableData: [],
      loading: false,
      // 物料放大镜弹窗配置
      materialDialogCofig: {
        pageConfig: dialogPageConfig(
          `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
          'material'
        ),
        text: 'itemCode',
        value: 'itemCode'
      },
      // 品类放大镜弹窗配置
      categoryDialogCofig: {
        pageConfig: dialogPageConfig(
          '/masterDataManagement/tenant/permission/queryCategories',
          'category'
        ),
        text: 'categoryName',
        value: 'categoryCode'
      },
      // 供应商放大镜弹窗配置
      supplierDialogCofig: {
        pageConfig: dialogPageConfig(
          '/masterDataManagement/tenant/supplier/pagedQueryByOrgCode',
          'supplier'
        ),
        text: 'supplierName',
        value: 'supplierCode'
      },
      isInitQuery: false
    }
  },
  methods: {
    validateSearchForm() {
      const valueList = Object.values(this.searchFormModel)
      if (!valueList.length || valueList.findIndex((val) => val !== null) === -1) {
        this.$toast({ type: 'warning', content: this.$t('查询条件不能为空！') })
        return false
      }

      return true
    },
    // 获取table数据
    async getTableData() {
      if (!this.validateSearchForm()) return

      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.costAnalysisReport
        .getList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data || []
      }
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.$refs.materialCode.handleClear()
      this.$refs.categoryCode.handleClear()
      this.$refs.supplierCode.handleClear()
      this.handleSearch()
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      switch (e.code) {
        case 'export':
          this.handleExport()
          break
        default:
          break
      }
    },
    // 导出
    async handleExport() {
      if (!this.validateSearchForm()) return

      const params = {
        page: { current: 1, size: 100000 },
        ...this.searchFormModel
      }
      const res = await this.$API.costAnalysisReport.exportCostAnalysisReport(params)
      if (res.data) {
        this.$toast({ type: 'success', content: this.$t('正在导出，请稍后！') })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
