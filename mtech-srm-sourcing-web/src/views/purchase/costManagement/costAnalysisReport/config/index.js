import { i18n } from '@/main.js'

const materialColumn = [
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    operator: 'contains'
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  }
]

const categoryColumn = [
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    operator: 'contains'
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]

const supplierColumn = [
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    operator: 'contains'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  }
]

export const dialogPageConfig = (url, type) => {
  const gridId = {
    material: '911d544e-b862-4d69-9fe7-4165fa6244df',
    category: 'c2e46f48-46a9-4a81-8cb2-c74f108a5f1f',
    supplier: '24fe4a0d-e89c-4fc1-8b91-d1ce1964838c'
  }
  const column = {
    material: materialColumn,
    category: categoryColumn,
    supplier: supplierColumn
  }
  return [
    {
      gridId: gridId[type],
      useToolTemplate: false,
      toolbar: [],
      grid: {
        allowFiltering: true,
        columnData: column[type],
        allowSorting: false,
        allowSelection: true,
        selectionSettings: {
          type: 'Multiple',
          mode: 'Row'
        },
        asyncConfig: { url }
      }
    }
  ]
}
