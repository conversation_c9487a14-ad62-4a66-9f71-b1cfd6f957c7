import XEUtils from 'xe-utils'

export default {
  data() {
    return {
      toolbar: [{ code: 'export', name: this.$t('导出'), status: 'info' }],
      companyList: []
    }
  },
  computed: {
    columns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'companyCode',
          title: this.$t('公司'),
          minWidth: 180,
          slots: {
            default: ({ row }) => {
              return [<span>{(row.companyCode || '') + '-' + (row.companyName || '')}</span>]
            }
          }
        },
        {
          field: 'materialCode',
          title: this.$t('物料编码')
        },
        {
          field: 'materialName',
          title: this.$t('物料名称')
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商'),
          minWidth: 150,
          slots: {
            default: ({ row }) => {
              return [<span>{(row.supplierCode || '') + '-' + (row.supplierName || '')}</span>]
            }
          }
        },
        {
          field: 'costConstruct',
          title: this.$t('成本构成')
        },
        {
          field: 'level',
          title: this.$t('层级')
        },
        {
          field: 'sort',
          title: this.$t('排序')
        },
        {
          field: 'costFactor',
          title: this.$t('成本因子')
        },
        {
          field: 'specification',
          title: this.$t('规格')
        },
        {
          field: 'brand',
          title: this.$t('品牌')
        },
        {
          field: 'unitCode',
          title: this.$t('单位')
        },
        {
          field: 'timeUnit',
          title: this.$t('用量/时间/单位'),
          minWidth: 120
        },
        {
          field: 'initUntaxedPrice',
          title: this.$t('初始单价（不含税）'),
          minWidth: 150
        },
        {
          field: 'lastUntaxedPrice',
          title: this.$t('上一次单价（不含税）'),
          minWidth: 160
        },
        {
          field: 'untaxedUnitPrice',
          title: this.$t('最新报价（不含税）'),
          minWidth: 150
        },
        {
          field: 'usingRate',
          title: this.$t('利用率')
        },
        {
          field: 'priceUnit',
          title: this.$t('价格单位')
        },
        {
          field: 'lastCostPrice',
          title: this.$t('上一次成本')
        },
        {
          field: 'costPrice',
          title: this.$t('成本')
        },
        {
          field: 'calculateFormula',
          title: this.$t('计算公式')
        },
        {
          field: 'directDeliverAddr',
          title: this.$t('直送地')
        },
        {
          field: 'stepValue',
          title: this.$t('阶梯数量')
        },
        {
          field: 'categoryCode',
          title: this.$t('品类编码')
        },
        {
          field: 'categoryName',
          title: this.$t('品类名称')
        },
        {
          field: 'costModelCode',
          title: this.$t('成本模型编码')
        },
        {
          field: 'costModelVersionCode',
          title: this.$t('成本模型版本')
        },
        {
          field: 'effectiveStartTime',
          title: this.$t('生效日期'),
          slots: {
            default: ({ row }) => {
              const effectiveStartTime = XEUtils.toDateString(row.effectiveStartTime, 'yyyy-MM-dd')
              return [<span>{effectiveStartTime}</span>]
            }
          }
        },
        {
          field: 'effectiveEndTime',
          title: this.$t('失效日期'),
          slots: {
            default: ({ row }) => {
              const effectiveEndTime = XEUtils.toDateString(row.effectiveEndTime, 'yyyy-MM-dd')
              return [<span>{effectiveEndTime}</span>]
            }
          }
        }
      ]
    }
  },
  mounted() {
    this.getCompanyList()
  },
  methods: {
    // 获取公司下拉列表
    async getCompanyList() {
      const res = await this.$API.masterData.permissionCompanyList()
      if (res.code === 200) {
        res.data?.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.companyList = res.data
      }
    }
  }
}
