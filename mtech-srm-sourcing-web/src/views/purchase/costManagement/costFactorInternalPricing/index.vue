<template>
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="rfxCode" :label="$t('定价单号')" label-style="top">
          <mt-input
            v-model="searchFormModel.rfxCode"
            :show-clear-button="true"
            :placeholder="$t('请输入定价单号')"
          />
        </mt-form-item>
        <mt-form-item prop="rfxName" :label="$t('定价单名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.rfxName"
            :show-clear-button="true"
            :placeholder="$t('请输入定价单名称')"
          />
        </mt-form-item>
        <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
          <mt-select
            v-model="searchFormModel.companyCode"
            css-class="rule-element"
            :data-source="companyList"
            :fields="{ text: 'text', value: 'orgCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择公司')"
          />
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.status"
            css-class="rule-element"
            :data-source="statusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择状态')"
          />
        </mt-form-item>
        <mt-form-item prop="submitTime" :label="$t('提交日期')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.submitTime"
            :allow-edit="false"
            :placeholder="$t('请选择提交日期')"
            :open-on-focus="true"
            @change="handleDateChange"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      grid-id="37b127fe-637f-499b-9bc8-0b6ded7aada6"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import XEUtils from 'xe-utils'

export default {
  components: {
    ScTable,
    CollapseSearch
  },
  mixins: [mixin],
  data() {
    return {
      searchFormModel: {},
      tableData: [],
      loading: false
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  mounted() {
    this.handleSearch()
  },
  activated() {
    this.handleSearch()
  },
  methods: {
    // 日期格式化
    handleDateChange(e) {
      if (e.startDate) {
        const startDate = XEUtils.toDateString(e.startDate, 'yyyy-MM-dd') + ' 00:00:00'
        const endDate = XEUtils.toDateString(e.endDate, 'yyyy-MM-dd') + ' 23:59:59'
        this.searchFormModel['submitStartTime'] = XEUtils.timestamp(startDate)
        this.searchFormModel['submitEndTime'] = XEUtils.timestamp(endDate)
      } else {
        this.searchFormModel['submitStartTime'] = null
        this.searchFormModel['submitEndTime'] = null
      }
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    // 查询
    async handleSearch(type) {
      // 解决修改查询条件后，查询结果有数据但未正常显示的问题（type: pageChange/refresh）
      if (!type) {
        this.pageInfo.current = 1
        this.$refs.pageRef.jumpNum = 1
        this.$refs.pageRef.currentPage = 1
      }

      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      delete params.submitTime
      this.loading = true
      const res = await this.$API.costFactorInternalPricing
        .getCfipList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []

        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (['delete', 'submit'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          this.handleDelete(selectedRecords)
          break
        case 'submit':
          this.handleSubmit(selectedRecords)
          break
        default:
          break
      }
    },
    // 点击单元格
    handleClickCellTitle(row, column) {
      switch (column.field) {
        case 'rfxCode':
          this.$router.push({
            name: 'cost-factor-internal-pricing-detail',
            query: {
              type: 'edit',
              id: row.id,
              refreshId: Date.now()
            }
          })
          break
        case 'oaApproveLink':
          if (!row.oaApproveLink) {
            this.$toast({
              content: this.$t('暂无申请单'),
              type: 'error'
            })
          } else {
            window.open(row.oaApproveLink)
          }
          break

        default:
          break
      }
    },
    // 新增
    handleAdd() {
      this.$router.push({
        name: 'cost-factor-internal-pricing-detail',
        query: {
          type: 'create',
          refreshId: Date.now()
        }
      })
    },
    // 删除
    handleDelete(list) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确定删除选中的数据？')
        },
        success: async () => {
          const ids = []
          list.forEach((item) => ids.push(item.id))
          const res = await this.$API.costFactorInternalPricing.batchDeleteCfip({ ids })
          if (res.code === 200) {
            this.$toast({ content: this.$t('删除成功！'), type: 'success' })
            this.handleSearch()
          }
        }
      })
    },
    // 提交
    handleSubmit(list) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确定提交选中的数据？')
        },
        success: async () => {
          const ids = []
          list.forEach((item) => ids.push(item.id))
          const res = await this.$API.costFactorInternalPricing.bacthSubmitCfip({ ids })
          if (res.code === 200) {
            this.$toast({ content: this.$t('提交成功！'), type: 'success' })
            this.handleSearch()
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
