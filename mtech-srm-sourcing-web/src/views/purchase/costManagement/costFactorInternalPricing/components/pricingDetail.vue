<template>
  <div>
    <sc-table
      ref="detailSctableRef"
      row-id="id"
      grid-id="305c5e2b-b8eb-461f-a294-6ab6a5c1af75"
      :keep-source="true"
      :edit-config="editConfig"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      :loading="loading"
      @refresh="getTableData"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          v-show="!item.isHidden"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :disabled="item.disabled"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />

    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-name="downTemplateName"
      :down-template-params="downTemplateParams"
      :request-urls="requestUrls"
      file-key="file"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    />
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/pricingDetailMixin'
import { download, getHeadersFileName } from '@/utils/utils'
import XEUtils from 'xe-utils'
import { getEndDate } from '@/utils/obj'
import cloneDeep from 'lodash/cloneDeep'

export default {
  name: 'CfipPricingDetailTab',
  components: {
    ScTable,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  mixins: [mixin],
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 20,
        pageSizes: [20, 50, 200, 500, 1000, 2000]
      },
      pageInfo: {
        size: 20,
        current: 1
      },
      tableData: [],
      downTemplateName: this.$t('成本因子内部定价明细-导入模板'),
      downTemplateParams: {},
      requestUrls: {},
      loading: false
    }
  },
  computed: {
    editConfig() {
      return {
        enabled: this.editable,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    dataList() {
      const dataList = cloneDeep(this.tableData)
      dataList.forEach((item) => {
        if (item.optType === 'add') {
          item.id = null
        } else if (this.tableRef.isUpdateByRow(item)) {
          item.optType = 'modify'
        }
      })
      return dataList
    }
  },
  mounted() {
    if (this.$route.query.type === 'edit') {
      this.getTableData()
    }
  },
  methods: {
    async getTableData() {
      const params = {
        rfxId: this.$route.query.id,
        page: this.pageInfo
      }
      this.loading = true
      const res = await this.$API.costFactorInternalPricing
        .getCfipDetailList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []

        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const { code } = e
      const selectedRecords = this.tableRef.getCheckboxRecords()
      switch (code) {
        case 'add':
          this.handleAdd()
          break
        case 'save':
          this.handleSave()
          break
        case 'delete':
          this.handleDelete(selectedRecords)
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          this.handleExport()
          break
        default:
          break
      }
    },
    // 新增
    handleAdd() {
      this.$dialog({
        modal: () => import('@/views/common/components/dialog/itemCodeDialog.vue'),
        data: {
          title: this.$t('成本因子'),
          valueSet: 'multi_cost_factor',
          companyCode: this.dataInfo.companyCode
        },
        success: async (list) => {
          const newRows = []
          list.forEach(async (data) => {
            const {
              itemCode,
              itemName,
              costFactorSpecification,
              baseMeasureUnitCode,
              baseMeasureUnitName,
              costFactorProperty,
              costFactorGroup,
              minQuotePercent,
              maxQuotePercent
            } = data
            // 1503、0530公司失效日期特殊处理
            let priceValidEndDate = '9999-12-31'
            if (this.dataInfo.companyCode === '1503') {
              priceValidEndDate = XEUtils.toDateString(
                getEndDate(new Date().getTime()),
                'yyyy-MM-dd'
              )
            } else if (this.dataInfo.companyCode === '0530') {
              priceValidEndDate = '2099-12-31'
            }
            // 默认币种
            const selectCurrencyItem = this.currencyList.find((item) => item.currencyCode === 'CNY')
            // 默认税率
            const selectTaxRateItem = this.taxList.find((item) => item.taxItemCode === 'J2')

            const newRowData = {
              optType: 'add',
              costFactorCode: itemCode,
              costFactorName: itemName,
              costFactorSpec: costFactorSpecification,
              costFactorAttr: costFactorProperty,
              costFactorGroup,
              basicMeasureUnitCode: baseMeasureUnitCode,
              basicMeasureUnitName: baseMeasureUnitName,
              minQuotePercent,
              maxQuotePercent,
              priceUnitName: '1',
              currencyCode: 'CNY',
              currencyName: selectCurrencyItem?.currencyName || null,
              taxRateCode: 'J2',
              taxRateName: selectTaxRateItem?.taxItemName || null,
              taxRate: selectTaxRateItem?.taxRate || null,
              priceValidStartDate: XEUtils.toDateString(new Date(), 'yyyy-MM-dd'),
              priceValidEndDate
            }
            newRows.push(newRowData)
          })

          await this.tableRef.insert(newRows)
          this.tableData = this.tableRef.getTableData().fullData

          this.tableRef.setEditRow(this.tableData[0])
        }
      })
    },
    // 保存
    async handleSave() {
      const params = {
        rfxId: this.$route.query.id,
        itemList: this.dataList
      }
      const res = await this.$API.costFactorInternalPricing.saveCfipDetail(params)
      if (res.code === 200) {
        this.$toast({ content: this.$t('保存成功！'), type: 'success' })
        this.getTableData()
      }
    },
    // 删除
    handleDelete(list) {
      if (!list.length) {
        this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: async () => {
          const itemIdList = []

          list.forEach(async (item) => {
            if (item.optType === 'add') {
              await this.tableRef.remove(item)
            } else {
              itemIdList.push(item.id)
            }
          })
          if (itemIdList.length === 0) {
            this.tableData = this.tableRef.getTableData().fullData
            return
          }

          const params = {
            rfxId: this.$route.query.id,
            itemIdList
          }
          const res = await this.$API.costFactorInternalPricing.deleteCfipDetail(params)
          if (res.code === 200) {
            this.$toast({ content: this.$t('删除成功！'), type: 'success' })
            this.getTableData()
          }
        }
      })
    },
    // 导入
    handleImport() {
      this.requestUrls = {
        templateUrlPre: 'costFactorInternalPricing',
        templateUrl: 'downloadCfipDetailImportTemplate',
        uploadUrl: 'importCfipDetail',
        rfxId: this.$route.query.id
      }
      this.showUploadExcel(true)
    },
    // 导出
    async handleExport() {
      const includeColumnFiledNames = []
      const tableColumns = this.tableRef.getColumns()
      tableColumns.forEach((col) => col.field && includeColumnFiledNames.push(col.field))

      const params = {
        rfxId: this.$route.query.id,
        page: {
          size: this.pageSettings.pageSize,
          current: this.pageSettings.current
        },
        includeColumnFiledNames
      }
      const res = await this.$API.costFactorInternalPricing.exportCfipDetail(params)
      if (res.data) {
        this.$toast({
          type: 'success',
          content: this.$t('正在导出，请稍后！')
        })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      }
    },
    // 展示/不展示 上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = [] // 清空数据
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 导入成功
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.getTableData()
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 0px 8px;
  background: #fff;
}
</style>
