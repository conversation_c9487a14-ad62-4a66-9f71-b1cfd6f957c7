import XEUtils from 'xe-utils'
import { getEndDate } from '@/utils/obj'
import { Decimal32 } from '@/utils/decimal'

export default {
  data() {
    return {
      currencyList: [],
      taxList: []
    }
  },
  computed: {
    editable() {
      return this.$route.query.type === 'create' || [0, 3].includes(this.dataInfo.status)
    },
    toolbar() {
      const { id } = this.$route.query
      return [
        {
          code: 'add',
          name: this.$t('新增'),
          status: 'info',
          isHidden: !id || !this.editable
        },
        {
          code: 'save',
          name: this.$t('保存'),
          status: 'info',
          isHidden: !id || !this.editable
        },
        {
          code: 'delete',
          name: this.$t('删除'),
          status: 'info',
          isHidden: !id || !this.editable
        },
        {
          code: 'import',
          name: this.$t('导入'),
          status: 'info',
          isHidden: !id || !this.editable
        },
        {
          code: 'export',
          name: this.$t('导出'),
          status: 'info',
          isHidden: !id
        }
      ]
    },
    tableRef() {
      return this.$refs.detailSctableRef.$refs.xGrid
    },
    columns() {
      const editable = this.dataInfo.companyCode ? true : false
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'itemNo',
          title: this.$t('行号'),
          width: 50
        },
        {
          field: 'costFactorCode',
          title: this.$t('成本因子编码'),
          minWidth: 150,
          editRender: {
            enabled: editable
          },
          slots: {
            edit: ({ row, rowIndex }) => {
              return [
                <vxe-input
                  v-model={row.costFactorCode}
                  clearable={false}
                  readonly
                  type='search'
                  on-clear={() => this.handleClearCostFactor(row, rowIndex)}
                  on-search-click={() => this.handleSelectCostFactor(row, rowIndex)}
                />
              ]
            }
          }
        },
        {
          field: 'costFactorName',
          title: this.$t('成本因子名称')
        },
        {
          field: 'costFactorSpec',
          title: this.$t('规格')
        },
        {
          field: 'costFactorAttr',
          title: this.$t('属性大类')
        },
        {
          field: 'basicMeasureUnitName',
          title: this.$t('单位'),
          slots: {
            default: ({ row }) => {
              return [
                <span>
                  {row.basicMeasureUnitCode
                    ? row.basicMeasureUnitCode + '-' + row.basicMeasureUnitName
                    : ''}
                </span>
              ]
            }
          }
        },
        {
          field: 'costFactorGroup',
          title: this.$t('属性中类')
        },
        {
          field: 'unitPriceUntaxed',
          title: this.$t('单价（未税）'),
          minWidth: 120,
          cellType: 'number',
          editRender: {
            enabled: editable
          },
          slots: {
            edit: ({ row }) => {
              if (row.unitPriceUntaxed === 0) {
                row.unitPriceUntaxed = 0.01
                const taxRate = row.taxRate || 0 // 税率值
                row.unitPriceTaxed = new Decimal32(taxRate)
                  .add(1)
                  .mul(row.unitPriceUntaxed)
                  .toFixed(2)
              }
              return [
                <vxe-input
                  v-model={row.unitPriceUntaxed}
                  clearable
                  type='float'
                  min='0.01'
                  on-change={({ value }) => {
                    if (!value && value !== 0) {
                      row.unitPriceUntaxed = null
                      row.unitPriceTaxed = null
                    } else {
                      // 单价含税  = 单价未税 * (1 + 税率值)
                      const taxRate = row.taxRate || 0 // 税率值
                      row.unitPriceTaxed = new Decimal32(taxRate).add(1).mul(value).toFixed(2)
                    }
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'unitPriceTaxed',
          title: this.$t('单价（含税）')
        },
        {
          field: 'priceUnitName',
          title: this.$t('价格单位'),
          editRender: {
            enabled: editable
          },
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.priceUnitName}
                  options={[
                    { label: 1, value: '1' },
                    { label: 1000, value: '1000' }
                  ]}
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'currencyCode',
          title: this.$t('币种编码'),
          minWidth: 150,
          editRender: {
            enabled: editable
          },
          slots: {
            edit: ({ row }) => {
              if (!row.currencyCode) {
                row.currencyCode = 'CNY'
                const selectItem = this.currencyList.find((item) => item.currencyCode === 'CNY')
                row.currencyName = selectItem?.currencyName
              }
              return [
                <vxe-select
                  v-model={row.currencyCode}
                  options={this.currencyList}
                  option-props={{ label: 'label', value: 'currencyCode' }}
                  filterable
                  transfer
                  on-change={({ value }) => {
                    if (value) {
                      const selectItem = this.currencyList.find(
                        (item) => item.currencyCode === value
                      )
                      row.currencyName = selectItem?.currencyName
                    } else {
                      row.currencyName = null
                    }
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'currencyName',
          title: this.$t('币种名称')
        },
        {
          field: 'taxRateCode',
          title: this.$t('税率编码'),
          minWidth: 150,
          editRender: {
            enabled: editable
          },
          slots: {
            edit: ({ row }) => {
              if (!row.taxRateCode) {
                row.taxRateCode = 'J2'
                const selectItem = this.taxList.find((item) => item.taxItemCode === 'J2')
                row.taxRateName = selectItem?.taxItemName
                row.taxRate = selectItem?.taxRate
              }
              return [
                <vxe-select
                  v-model={row.taxRateCode}
                  options={this.taxList}
                  option-props={{ label: 'label', value: 'taxItemCode' }}
                  filterable
                  transfer
                  on-change={({ value }) => {
                    if (value) {
                      const selectItem = this.taxList.find((item) => item.taxItemCode === value)
                      row.taxRateName = selectItem?.taxItemName
                      row.taxRate = selectItem?.taxRate
                    } else {
                      row.taxRateName = null
                      row.taxRate = null
                    }
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'taxRateName',
          title: this.$t('税率名称')
        },
        {
          field: 'taxRate',
          title: this.$t('税率')
        },
        {
          field: 'priceValidStartDate',
          title: this.$t('生效日期'),
          minWidth: 140,
          editRender: {
            enabled: editable
          },
          slots: {
            default: ({ row }) => {
              const effectiveStartDate = XEUtils.toDateString(row.priceValidStartDate, 'yyyy-MM-dd')
              return [<span>{effectiveStartDate}</span>]
            },
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.priceValidStartDate}
                  clearable
                  editable={false}
                  type='date'
                  transfer
                  disabled-method={({ date }) => {
                    const endTime = row.priceValidEndDate
                      ? new Date(row.priceValidEndDate).getTime()
                      : null
                    return endTime ? endTime - 86400000 < new Date(date).getTime() : false
                  }}
                  on-change={({ value }) => {
                    let endDate = '9999-12-31'
                    // 1503、0530失效日期特殊处理
                    if (value) {
                      if (this.dataInfo.companyCode === '1503') {
                        endDate = XEUtils.toDateString(
                          getEndDate(new Date(value).getTime()),
                          'yyyy-MM-dd'
                        )
                      } else if (this.dataInfo.companyCode === '0530') {
                        endDate = '2099-12-31'
                      }
                    }
                    row.priceValidEndDate = value ? endDate : null
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'priceValidEndDate',
          title: this.$t('失效日期'),
          minWidth: 140,
          editRender: {
            enabled: editable
          },
          slots: {
            default: ({ row }) => {
              const effectiveEndDate = XEUtils.toDateString(row.priceValidEndDate, 'yyyy-MM-dd')
              return [<span>{effectiveEndDate}</span>]
            },
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.priceValidEndDate}
                  clearable
                  editable={false}
                  type='date'
                  transfer
                  disabled-method={({ date }) => {
                    const startTime = row.priceValidStartDate
                      ? new Date(row.priceValidStartDate).getTime()
                      : null
                    return startTime ? startTime + 86400000 > new Date(date).getTime() : false
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'remark',
          title: this.$t('备注'),
          minWidth: 180,
          editRender: {
            enabled: editable
          },
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.remark} clearable />]
            }
          }
        }
      ]
    }
  },
  mounted() {
    this.getCurrencyList()
    this.getTaxList()
  },
  methods: {
    // 获取币种下拉列表
    async getCurrencyList() {
      const res = await this.$API.masterData.queryAllCurrency()
      if (res.code === 200) {
        this.currencyList = []
        res.data?.forEach((item) => {
          item.label = item.currencyCode + '-' + item.currencyName
          this.currencyList.push(item)
        })
      }
    },
    // 获取税率下拉列表
    async getTaxList() {
      this.$API.masterData.queryAllTaxItem().then((res) => {
        if (res.code === 200) {
          this.taxList = []
          res.data?.forEach((item) => {
            item.label = item.taxItemCode + '-' + item.taxItemName
            this.taxList.push(item)
          })
        }
      })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.getTableData()
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.getTableData()
    },
    // 选择成本因子
    handleSelectCostFactor(row, rowIndex) {
      this.$dialog({
        modal: () => import('@/views/common/components/dialog/itemCodeDialog.vue'),
        data: {
          title: this.$t('成本因子'),
          valueSet: 'cost_factor',
          companyCode: this.dataInfo.companyCode
        },
        success: (data) => {
          const {
            itemCode,
            itemName,
            costFactorSpecification,
            baseMeasureUnitCode,
            baseMeasureUnitName,
            costFactorProperty,
            costFactorGroup,
            minQuotePercent,
            maxQuotePercent
          } = data
          const newRowData = {
            ...row,
            costFactorCode: itemCode,
            costFactorName: itemName,
            costFactorSpec: costFactorSpecification,
            costFactorAttr: costFactorProperty,
            costFactorGroup,
            basicMeasureUnitCode: baseMeasureUnitCode,
            basicMeasureUnitName: baseMeasureUnitName,
            minQuotePercent,
            maxQuotePercent,
            optType: 'modify'
          }
          this.$set(this.tableData, rowIndex, newRowData)
          this.tableRef.setEditRow(newRowData)
        },
        close: () => {
          this.tableRef.setEditRow(row)
        }
      })
    },
    // 清除成本因子
    handleClearCostFactor(row, rowIndex) {
      const newRowData = {
        ...row,
        costFactorCode: null,
        costFactorName: null,
        costFactorSpec: null,
        costFactorAttr: null,
        costFactorGroup: null,
        basicMeasureUnitCode: null,
        basicMeasureUnitName: null,
        optType: 'modify'
      }
      this.$set(this.tableData, rowIndex, newRowData)
    }
  }
}
