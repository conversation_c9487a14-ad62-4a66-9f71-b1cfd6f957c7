<template>
  <div class="full-height">
    <div class="top-container toggle-container" :class="[!isExpand && 'top-hidden']">
      <div class="top-content">
        <div class="top-info">
          <div class="title">
            <div v-show="!isExpand">
              <span>{{ dataForm.rfxCode }}</span>
              <span class="sub-title">{{ dataForm.rfxName }}</span>
            </div>
          </div>
          <div class="top-btns">
            <vxe-button
              v-for="(item, index) in detailToolbar"
              v-show="!item.isHidden"
              :key="index"
              :icon="item.icon"
              :status="item.status"
              :disabled="item.disabled"
              size="small"
              @click="handleClickToolBar(item)"
              >{{ item.name }}</vxe-button
            >
          </div>
        </div>
        <div class="top-form">
          <mt-form ref="dataFormRef" :model="dataForm" :rules="formRules">
            <mt-form-item prop="rfxCode" :label="$t('定价单号')" label-style="top">
              <vxe-input v-model="dataForm.rfxCode" disabled />
            </mt-form-item>
            <mt-form-item prop="rfxName" :label="$t('定价单名称')" label-style="top">
              <vxe-input
                v-model="dataForm.rfxName"
                clearable
                :disabled="!editable"
                :placeholder="$t('请输入定价单名称')"
              />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
              <vxe-select
                v-model="dataForm.companyCode"
                :options="companyList"
                :option-props="{ label: 'text', value: 'orgCode' }"
                clearable
                filterable
                :disabled="!editable || dataList[0].length !== 0"
                :placeholder="$t('请选择公司')"
                @change="
                  ({ value }) => {
                    const selectedItem = companyList.find((item) => item.orgCode === value)
                    dataForm.companyName = selectedItem ? selectedItem.orgName : null
                  }
                "
              />
            </mt-form-item>
            <mt-form-item prop="statusName" :label="$t('单据状态')" label-style="top">
              <vxe-input v-model="dataForm.statusName" disabled />
            </mt-form-item>
            <mt-form-item prop="docTypeName" :label="$t('定价类型')" label-style="top">
              <vxe-input v-model="dataForm.docTypeName" disabled />
            </mt-form-item>
            <mt-form-item prop="sourceName" :label="$t('单据来源')" label-style="top">
              <vxe-input v-model="dataForm.sourceName" disabled />
            </mt-form-item>
            <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
              <vxe-input v-model="dataForm.createUserName" disabled />
            </mt-form-item>
            <mt-form-item prop="createTime" :label="$t('创建日期')" label-style="top">
              <vxe-input v-model="dataForm.createTime" disabled />
            </mt-form-item>
            <mt-form-item prop="remark" :label="$t('备注')" label-style="top">
              <vxe-textarea
                v-model="dataForm.remark"
                clearable
                :disabled="!editable"
                :rows="1"
                :placeholder="$t('请输入备注')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </div>
      <div class="top-toggle-btn">
        <div @click="handleToggle"><i class="vxe-icon-arrow-up" /></div>
      </div>
    </div>
    <div class="body-container" v-show="$route.query.type === 'edit'">
      <mt-tabs
        ref="mtTabsRef"
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabList"
        :halt-select="false"
        @handleSelectTab="(index) => handleTabChange(index)"
        style="background-color: #fff"
      />
      <keep-alive :include="keepArr">
        <component ref="mainContent" :is="activeComponent" :data-info="dataForm" />
      </keep-alive>
    </div>
  </div>
</template>

<script>
import {
  Input as VxeInput,
  Button as VxeButton,
  Select as VxeSelect,
  Textarea as VxeTextarea
} from 'vxe-table'
import mixin from './config/mixin'
import { requiredFieldList } from './config/index'

export default {
  name: 'ExpertRating',
  components: {
    VxeInput,
    VxeButton,
    VxeSelect,
    VxeTextarea
  },
  mixins: [mixin],
  data() {
    return {
      dataForm: {
        companyCode: null,
        docTypeCode: 0,
        docTypeName: this.$t('成本因子询价'),
        sourceCode: 0,
        sourceName: this.$t('手动创建'),
        status: 0,
        statusName: this.$t('草稿')
      },
      tabList: [
        { title: this.$t('定价明细'), compName: 'CfipPricingDetailTab' },
        { title: this.$t('附件'), compName: 'CfipAttachmentTab' }
      ],
      activeTabIndex: 0,
      isExpand: true,
      dataList: [[], []],
      keepArr: ['CfipPricingDetailTab', 'CfipAttachmentTab']
    }
  },
  computed: {
    editable() {
      return this.$route.query.type === 'create' || [0, 3].includes(this.dataForm.status)
    },
    activeComponent() {
      let comp = ''
      switch (this.activeTabIndex) {
        case 0:
          comp = () => import('./components/pricingDetail.vue')
          break
        case 1:
          comp = () => import('./components/attachment.vue')
          break
        default:
          return
      }
      return comp
    },
    formRules() {
      return {
        rfxCode: [
          {
            required: this.$route.query.type === 'edit',
            message: this.$t('请输入定价单名称'),
            trigger: 'blur'
          }
        ],
        rfxName: [{ required: true, message: this.$t('请输入定价单名称'), trigger: 'blur' }],
        companyCode: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        docTypeName: [{ required: true, message: this.$t('请输入定价类型'), trigger: 'blur' }],
        sourceName: [{ required: true, message: this.$t('请输入单据来源'), trigger: 'blur' }]
      }
    }
  },
  mounted() {
    if (this.$route.query.type === 'edit') {
      this.getHeaderInfo()
    }
  },
  methods: {
    async getHeaderInfo() {
      const res = await this.$API.costFactorInternalPricing.getCfipDetailHeaderInfo({
        id: this.$route.query.id
      })
      if (res.code === 200) {
        const selectItem = this.statusList.find((item) => item.value === res.data.status)
        const statusName = selectItem?.text
        this.dataForm = { ...res.data, statusName }
      }
    },

    // 点击工具栏按钮
    handleClickToolBar(e) {
      switch (e.code) {
        case 'back':
          this.handleBack()
          break
        case 'delete':
          this.handleDelete()
          break
        case 'save':
        case 'submit':
          this.handleSaveOrSubmit(e.code)
          break
        case 'viewOA':
          this.handleViewOA()
          break
        default:
          break
      }
    },
    // 切换tab页签
    handleTabChange(index) {
      // 保存即将离开tab页签的数据
      this.dataList[this.activeTabIndex] = this.$refs.mainContent?.dataList || []

      this.$refs.mtTabsRef.activeTab = index
      this.activeTabIndex = index

      // 若该tab页修改数据后保存成功从keepArr中移除了，再次进入该页面需要重新加入缓存
      const tabName = this.tabList[index]?.compName
      !this.keepArr.includes(tabName) && this.keepArr.push(tabName)
    },
    // 返回
    handleBack() {
      this.$router.go(-1)
    },
    // 保存、提交
    handleSaveOrSubmit(type) {
      this.$refs.dataFormRef.validate(async (valid) => {
        if (valid) {
          // 定价明细列表
          const itemList =
            this.activeTabIndex === 0 ? this.$refs.mainContent.dataList : this.dataList[0]
          const params = {
            ...this.dataForm,
            itemList
          }
          type === 'save' ? this.handleSave(params) : this.handleSubmit(params)
        }
      })
    },
    // 保存
    async handleSave(params) {
      const res = await this.$API.costFactorInternalPricing.saveCfipAllDetail(params)
      if (res.code === 200) {
        this.$toast({ content: this.$t('保存成功！'), type: 'success' })
        if (this.$route.query.type === 'create') {
          this.$router.replace({
            name: 'cost-factor-internal-pricing-detail',
            query: {
              type: 'edit',
              id: res.data
            }
          })
        } else {
          this.getHeaderInfo()
          this.refreshKeepArr(params)
          this.activeTabIndex === 0 && this.$refs.mainContent?.getTableData()
        }
      }
    },
    // 提交
    handleSubmit(params) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确定提交？')
        },
        success: async () => {
          const res = await this.$API.costFactorInternalPricing.submitCfipAllDetail(params)
          if (res.code === 200) {
            this.$toast({ content: this.$t('提交成功！'), type: 'success' })
            this.getHeaderInfo()
            this.refreshKeepArr(params)
            this.activeTabIndex === 0 && this.$refs.mainContent?.getTableData()
          }
        }
      })
    },
    // 删除
    handleDelete() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确定删除？')
        },
        success: async () => {
          const ids = [this.$route.query.id]
          const res = await this.$API.costFactorInternalPricing.batchDeleteCfip({ ids })
          if (res.code === 200) {
            this.$toast({ content: this.$t('删除成功！'), type: 'success' })
            this.handleBack()
          }
        }
      })
    },
    // 查看OA审批进度
    async handleViewOA() {
      let oaApproveLink = this.dataForm?.oaApproveLink
      oaApproveLink
        ? window.open(oaApproveLink)
        : this.$toast({ content: this.$t('暂无申请单'), type: 'error' })
    },
    // 校验明细列表必填项
    validDetailList(list) {
      for (let i = 0; i < list.length; i++) {
        const row = list[i]
        for (let k = 0; k < requiredFieldList.length; k++) {
          const item = requiredFieldList[k]
          if (!row[item.field] && row[item.field] !== 0) {
            this.$toast({
              content: this.$t(`第${i + 1}行${item?.name}必填，请检查！`),
              type: 'warning'
            })
            return false
          }
        }
      }
      return true
    },
    // 更新缓存列表
    async refreshKeepArr(lists) {
      const { itemList } = lists
      const pricingItem = itemList.find((item) => ['add', 'modify'].includes(item.optType))

      // 对于修改过数据的tab页签，清除缓存，未更新的列表增加缓存
      this.keepArr = ['CfipAttachmentTab']
      !pricingItem && this.keepArr.push('CfipPricingDetailTab')

      // tab中数据更新，择重新初始化dataList，避免新增的数据行重复新增
      if (pricingItem) {
        const params = {
          rfxId: this.$route.query.id,
          page: { size: 20, current: 1 }
        }
        const res = await this.$API.costFactorInternalPricing.getCfipDetailList(params)
        if (res.code === 200) {
          this.$set(this.dataList, 0, res.data?.records || [])
        }
      }
    },
    // 展开/收缩
    handleToggle() {
      this.isExpand = !this.isExpand
    }
  }
}
</script>

<style scoped lang="scss">
.full-height {
  height: 100%;
  padding: 8px;
  background: #fff;
}

.top-container {
  width: 100%;
  .top-content {
    padding: 8px 8px 0 8px;
    background-color: #f9f9f9;
    border-radius: 8px;

    .top-info {
      display: flex;
      justify-content: space-between;
      .title {
        font-size: 20px;
        font-weight: 700;
        color: #292929;
        span {
          margin-right: 15px;
        }
        .sub-title {
          font-size: 15px;
          color: #777;
        }
      }
      .top-btns {
        text-align: right;
        margin-bottom: 10px;
      }
    }

    .top-form {
      .mt-form {
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
        justify-content: space-between;
        grid-gap: 5px;
      }
      .vxe-input,
      .vxe-select {
        width: 100%;
        height: 32px;
        line-height: 32px;
      }
    }
  }
  .top-toggle-btn {
    width: 100%;
    div {
      width: 80px;
      height: 15px;
      line-height: 15px;
      margin: auto;
      text-align: center;
      background-color: #4a556b;
      color: #fff;
      font-weight: bold;
      border-radius: 0 0 6px 6px;
    }
  }
}

.top-hidden {
  .top-form {
    display: none;
  }
  .top-toggle-btn {
    div {
      transform: rotate(180deg);
      border-radius: 6px 6px 0 0;
    }
  }
}

::v-deep {
  .mt-form-item {
    margin-bottom: 10px;
    .label {
      margin: 0 10px 5px 0;
    }
  }
  .mt-tabs-container {
    background-color: #fff;
  }

  .mt-tabs-container ul.tab-container li.tab-item2.active {
    border-bottom: 2px solid #409eff;
  }
  .mt-tabs-container ul.tab-container li.tab-item2.active,
  .mt-tabs-container ul.tab-container li.tab-item2:hover {
    color: #31374e;
  }

  .vxe-textarea--inner {
    min-height: 34px;
    height: 100%;
  }
}
</style>
