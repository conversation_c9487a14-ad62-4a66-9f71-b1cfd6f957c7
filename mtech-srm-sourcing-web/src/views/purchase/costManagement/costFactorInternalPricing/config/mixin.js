export default {
  data() {
    return {
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 20,
        pageSizes: [20, 50, 200, 500, 1000, 2000]
      },
      pageInfo: {
        size: 20,
        current: 1
      },
      toolbar: [
        { code: 'add', name: this.$t('新增'), status: 'info' },
        { code: 'delete', name: this.$t('删除'), status: 'info' },
        { code: 'submit', name: this.$t('提交'), status: 'info' }
      ],
      statusList: [
        { value: 0, text: this.$t('草稿') },
        { value: 1, text: this.$t('审批中') },
        { value: 2, text: this.$t('审批通过') },
        { value: 3, text: this.$t('审批驳回') },
        { value: 7, text: this.$t('审批废弃') }
      ],
      companyList: []
    }
  },
  computed: {
    detailToolbar() {
      const { id, type } = this.$route.query
      const editable = [0, 3].includes(this.dataForm.status)
      const toolbar = [
        {
          code: 'submit',
          name: this.$t('提交'),
          status: '',
          isHidden: !id || !editable
        },
        {
          code: 'save',
          name: this.$t('保存'),
          status: '',
          isHidden: type === 'edit' && !editable
        },
        {
          code: 'delete',
          name: this.$t('删除'),
          status: '',
          isHidden: !id || !editable
        },
        {
          code: 'viewOA',
          name: this.$t('OA审批进度'),
          status: '',
          isHidden: this.dataForm.status === 0
        },
        {
          code: 'back',
          name: this.$t('返回'),
          status: 'primary'
        }
      ]
      return toolbar
    },
    columns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'rfxCode',
          title: this.$t('定价单号'),
          minWidth: 140,
          slots: {
            default: ({ row, column }) => {
              return [<a on-click={() => this.handleClickCellTitle(row, column)}>{row.rfxCode}</a>]
            }
          }
        },
        {
          field: 'rfxName',
          title: this.$t('定价单名称')
        },
        {
          field: 'status',
          title: this.$t('状态'),
          slots: {
            default: ({ row }) => {
              const selectItem = this.statusList.find((item) => item.value === row.status)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'companyCode',
          title: this.$t('公司'),
          minWidth: 200,
          slots: {
            default: ({ row }) => {
              return [<span>{row.companyCode + '-' + row.companyName}</span>]
            }
          }
        },
        {
          field: 'sourceName',
          title: this.$t('单据来源')
        },
        {
          field: 'docTypeName',
          title: this.$t('单据类型')
        },
        {
          field: 'oaApproveLink',
          title: this.$t('OA申请单查看'),
          slots: {
            default: ({ row, column }) => {
              return [
                <div>
                  <a
                    v-show={row.oaApproveLink}
                    on-click={() => this.handleClickCellTitle(row, column)}>
                    {this.$t('查看')}
                  </a>
                  <span v-show={!row.oaApproveLink}>-</span>
                </div>
              ]
            }
          }
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间')
        },
        {
          field: 'submitTime',
          title: this.$t('提交时间')
        }
      ]
    }
  },
  mounted() {
    this.getCompanyList()
  },
  methods: {
    // 获取公司下拉列表
    async getCompanyList() {
      const res = await this.$API.masterData.permissionCompanyList()
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.companyList = res.data
      }
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.handleSearch('pageChange')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.handleSearch()
    }
  }
}
