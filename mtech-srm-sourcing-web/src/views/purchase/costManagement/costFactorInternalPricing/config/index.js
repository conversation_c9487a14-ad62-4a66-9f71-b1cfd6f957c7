import { i18n } from '@/main'

// 明细必填字段
export const requiredFieldList = [
  { field: 'costFactorCode', name: i18n.t('成本因子编码') },
  { field: 'costFactorName', name: i18n.t('成本因子名称') },
  { field: 'costFactorSpec', name: i18n.t('规格') },
  { field: 'costFactorAttr', name: i18n.t('属性大类') },
  { field: 'costFactorGroup', name: i18n.t('属性中类') },
  { field: 'basicMeasureUnitCode', name: i18n.t('单位') },
  { field: 'unitPriceUntaxed', name: i18n.t('单价（未税）') },
  { field: 'unitPriceTaxed', name: i18n.t('单价（含税）') },
  { field: 'priceUnit', name: i18n.t('价格单位') },
  { field: 'currencyCode', name: i18n.t('币种') },
  { field: 'taxRate', name: i18n.t('税率') },
  { field: 'effectiveStartDate', name: i18n.t('生效日期') },
  { field: 'effectiveEndDate', name: i18n.t('失效日期') }
]
