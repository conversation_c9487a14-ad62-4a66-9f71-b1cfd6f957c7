import XEUtils from 'xe-utils'

export default {
  data() {
    return {
      tabList: [{ title: this.$t('因子内部价格') }, { title: this.$t('因子外部价格') }],
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 20,
        pageSizes: [20, 50, 200, 500, 1000, 2000]
      },
      pageInfo: {
        size: 20,
        current: 1
      },
      // toolbar: [
      //   { code: 'export', name: this.$t('导出'), status: 'info' },
      //   { code: 'import', name: this.$t('导入'), status: 'info', hide: true }
      // ],
      statusList: [
        { value: -1, text: this.$t('已作废') },
        { value: 0, text: this.$t('草稿') },
        { value: 1, text: this.$t('审批中') },
        { value: 2, text: this.$t('审批通过') },
        { value: 3, text: this.$t('审批驳回') }
      ],
      companyList: []
    }
  },
  computed: {
    toolbar() {
      return this.activeTabIndex === 0
        ? [{ code: 'export', name: this.$t('导出'), status: 'info' }]
        : [
            { code: 'export', name: this.$t('导出'), status: 'info' },
            { code: 'import', name: this.$t('导入'), status: 'info', permission: ['O_02_1726'] }
          ]
    },
    gridId() {
      const gridIdList = [
        'e2722acc-8d3a-4a5f-b416-286eafdfe415',
        'fbc4169c-259b-48bc-a32b-b48f6ac8d61e'
      ]
      return gridIdList[this.activeTabIndex]
    },
    columns() {
      const defaultColumns = [
        {
          field: 'priceRecordCode',
          title: this.$t('价格记录编码'),
          minWidth: 200
        },
        {
          field: 'companyCode-companyName',
          title: this.$t('公司'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [<span>{row.companyCode + '-' + row.companyName}</span>]
            }
          }
        },
        {
          field: 'costFactorCode',
          title: this.$t('成本因子编码'),
          minWidth: 140
        },
        {
          field: 'costFactorName',
          title: this.$t('成本因子名称'),
          minWidth: 160
        },
        {
          field: 'costFactorSpec',
          title: this.$t('规格')
        },
        {
          field: 'currencyCode-currencyName',
          title: this.$t('币种'),
          slots: {
            default: ({ row }) => {
              return [<span>{row.currencyCode + '-' + row.currencyName}</span>]
            }
          }
        },
        {
          field: 'costFactorAttr',
          title: this.$t('属性大类')
        },
        {
          field: 'costFactorGroup',
          title: this.$t('属性中类')
        },
        {
          field: 'priceUnitName',
          title: this.$t('价格单位')
        },
        {
          field: 'unitPriceUntaxed',
          title: this.$t('单价（未税）')
        },
        {
          field: 'unitPriceTaxed',
          title: this.$t('单价（含税）')
        },
        {
          field: 'sourceCode',
          title: this.$t('来源单号'),
          minWidth: 200
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          minWidth: 140
        },
        {
          field: 'priceValidStartDate',
          title: this.$t('生效日期'),
          slots: {
            default: ({ row }) => {
              const date = XEUtils.toDateString(row.priceValidStartDate, 'yyyy-MM-dd')
              return [<span>{date}</span>]
            }
          }
        },
        {
          field: 'priceValidEndDate',
          title: this.$t('失效日期'),
          slots: {
            default: ({ row }) => {
              const date = XEUtils.toDateString(row.priceValidEndDate, 'yyyy-MM-dd')
              return [<span>{date}</span>]
            }
          }
        },
        {
          field: 'status',
          title: this.$t('状态'),
          slots: {
            default: ({ row }) => {
              const statusMap = {
                0: this.$t('草稿'),
                1: this.$t('启用'),
                2: this.$t('禁用')
              }
              return [<span>{statusMap[row.status]}</span>]
            }
          }
        },
        {
          field: 'historyRecord',
          title: this.$t('历史记录'),
          fixed: 'right',
          slots: {
            default: ({ row, column }) => {
              return [
                <a on-click={() => this.handleClickCellTitle(row, column)}>{this.$t('查看')}</a>
              ]
            }
          }
        },
        {
          field: 'historyChart',
          title: this.$t('历史趋势图'),
          fixed: 'right',
          slots: {
            default: ({ row, column }) => {
              return [
                <a on-click={() => this.handleClickCellTitle(row, column)}>{this.$t('查看')}</a>
              ]
            }
          }
        }
      ]
      const outerColumns = [
        {
          field: 'costFactorBrand',
          title: this.$t('品牌')
        },
        {
          field: 'supplierCode-supplierName',
          title: this.$t('供应商'),
          minWidth: 260,
          slots: {
            default: ({ row }) => {
              return [<span>{row.supplierCode + '-' + row.supplierName}</span>]
            }
          }
        }
      ]

      if (this.activeTabIndex === 1) {
        defaultColumns.splice(4, 0, outerColumns[0])
        defaultColumns.splice(11, 0, outerColumns[1])
      }
      return defaultColumns
    }
  },
  mounted() {
    this.getCompanyList()
  },
  methods: {
    // 获取公司下拉列表
    async getCompanyList() {
      const res = await this.$API.masterData.permissionCompanyList()
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.companyList = res.data
      }
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.handleSearch('pageChange')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.handleSearch()
    },
    // 点击单元格标题
    handleClickCellTitle(row, column) {
      switch (column.field) {
        case 'historyRecord':
          this.$dialog({
            modal: () => import('../dialog/historyRecordDialog.vue'),
            data: {
              title: this.$t('历史记录'),
              id: row.id,
              type: this.activeTabIndex === 1 ? 'outer' : 'inner'
            }
          })
          break
        case 'historyChart':
          this.$dialog({
            modal: () => import('../dialog/historyTrendDialog.vue'),
            data: {
              title: this.$t('历史趋势图'),
              id: row.id
            }
          })
          break
        default:
          break
      }
    }
  }
}
