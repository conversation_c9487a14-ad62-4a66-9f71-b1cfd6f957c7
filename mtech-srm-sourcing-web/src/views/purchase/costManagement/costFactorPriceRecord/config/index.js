import { i18n } from '@/main.js'

const supplierColumn = [
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    operator: 'contains'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    operator: 'contains'
  }
]
const costFactorColumn = [
  {
    field: 'itemCode',
    headerText: i18n.t('成本因子编码'),
    operator: 'contains'
  },
  {
    field: 'itemName',
    headerText: i18n.t('成本因子名称'),
    operator: 'contains'
  }
]
export const dialogPageConfig = (url, type, queryInfo) => {
  const gridId = {
    supplier: '6806c260-9e69-43f4-991e-e769626019f6',
    costFactor: '1cc4752c-0cf4-4db6-805b-0aad3869d6f6'
  }
  const column = {
    supplier: supplierColumn,
    costFactor: costFactorColumn
  }
  return [
    {
      gridId: gridId[type],
      useToolTemplate: false,
      toolbar: [],
      grid: {
        allowFiltering: true,
        columnData: column[type],
        allowSorting: false,
        allowSelection: true,
        selectionSettings: {
          type: 'Multiple',
          mode: 'Row'
        },
        asyncConfig: { url, ...queryInfo }
      }
    }
  ]
}
