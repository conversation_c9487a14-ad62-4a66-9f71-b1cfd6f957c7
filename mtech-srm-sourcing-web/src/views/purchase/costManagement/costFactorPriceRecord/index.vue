<template>
  <div class="full-height">
    <mt-tabs
      ref="mtTabsRef"
      :tab-id="$utils.randomString()"
      :e-tab="false"
      :data-source="tabList"
      :halt-select="false"
      @handleSelectTab="(index, item) => handleTabChange(index)"
      style="background-color: #fff"
    />
    <div class="body-container">
      <!-- 自定义查询条件 -->
      <collapse-search
        class="toggle-container"
        :is-grid-display="true"
        :default-expand="true"
        @reset="handleReset"
        @search="handleSearch"
      >
        <mt-form ref="searchFormRef" :model="searchFormModel">
          <mt-form-item prop="priceRecordCode" :label="$t('价格记录编码')" label-style="top">
            <mt-input
              v-model="searchFormModel.priceRecordCode"
              :show-clear-button="true"
              :placeholder="$t('请输入价格记录编码')"
            />
          </mt-form-item>
          <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
            <mt-select
              v-model="searchFormModel.companyCode"
              css-class="rule-element"
              :data-source="companyList"
              :fields="{ text: 'text', value: 'orgCode' }"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择公司')"
            />
          </mt-form-item>
          <mt-form-item prop="costFactorCode" :label="$t('成本因子编码')" label-style="top">
            <magnifier-input
              ref="costFactorCode"
              :config="costFactorDialogCofig"
              @change="(e) => (searchFormModel.costFactorCode = e.itemCode || null)"
            />
          </mt-form-item>
          <mt-form-item prop="costFactorSpec" :label="$t('规格')" label-style="top">
            <mt-input
              v-model="searchFormModel.costFactorSpec"
              :show-clear-button="true"
              :placeholder="$t('请输入规格')"
            />
          </mt-form-item>
          <mt-form-item
            v-show="activeTabIndex === 1"
            prop="costFactorBrand"
            :label="$t('品牌')"
            label-style="top"
          >
            <mt-input
              v-model="searchFormModel.costFactorBrand"
              :show-clear-button="true"
              :placeholder="$t('请输入品牌')"
            />
          </mt-form-item>
          <mt-form-item
            v-show="activeTabIndex === 1"
            prop="supplierCode"
            :label="$t('供应商')"
            label-style="top"
          >
            <magnifier-input
              ref="supplierCode"
              :config="supplierDialogCofig"
              @change="(e) => (searchFormModel.supplierCode = e.supplierCode || null)"
            />
          </mt-form-item>
          <mt-form-item prop="sourceCode" :label="$t('来源单号')" label-style="top">
            <mt-input
              v-model="searchFormModel.sourceCode"
              :show-clear-button="true"
              :placeholder="$t('请输入来源单号')"
            />
          </mt-form-item>
          <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
            <mt-date-range-picker
              v-model="searchFormModel.createTime"
              :allow-edit="false"
              :open-on-focus="true"
              :placeholder="$t('请选择创建时间')"
              @change="(e) => handleDateChange('createTime', e)"
            />
          </mt-form-item>
          <mt-form-item prop="priceValidStartDate" :label="$t('生效日期')" label-style="top">
            <mt-date-range-picker
              v-model="searchFormModel.priceValidStartDate"
              :allow-edit="false"
              :open-on-focus="true"
              :placeholder="$t('请选择生效日期')"
              @change="(e) => handleDateChange('priceValidStartDate', e)"
            />
          </mt-form-item>
          <mt-form-item prop="priceValidEndDate" :label="$t('失效日期')" label-style="top">
            <mt-date-range-picker
              v-model="searchFormModel.priceValidEndDate"
              :allow-edit="false"
              :open-on-focus="true"
              :placeholder="$t('请选择失效日期')"
              @change="(e) => handleDateChange('priceValidEndDate', e)"
            />
          </mt-form-item>
        </mt-form>
      </collapse-search>
      <!-- 表格 -->
      <sc-table
        ref="sctableRef"
        :loading="loading"
        :is-show-refresh-bth="true"
        :grid-id="gridId"
        :columns="columns"
        :table-data="tableData"
        @refresh="handleSearch"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            v-permission="item.permission"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            size="small"
            @click="handleClickToolBar(item)"
            >{{ item.name }}</vxe-button
          >
        </template>
      </sc-table>
      <!-- 分页 -->
      <mt-page
        ref="pageRef"
        class="flex-keep custom-page"
        :page-settings="pageSettings"
        :total-pages="pageSettings.totalPages"
        @currentChange="handleCurrentChange"
        @sizeChange="handleSizeChange"
      />
    </div>
    <upload-excel-dialog
      ref="uploadInitBalanceRef"
      :down-template-name="downTemplateName"
      :request-urls="requestUrls"
      :down-template-params="downTemplateParams"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
      file-key="file"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import magnifierInput from '@/components/magnifierInput'
import mixin from './config/mixin'
import { dialogPageConfig } from './config/index'
import { download, getHeadersFileName } from '@/utils/utils'
import XEUtils from 'xe-utils'

export default {
  name: 'CostFactorPriceRecord',
  components: {
    CollapseSearch,
    ScTable,
    magnifierInput,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  mixins: [mixin],
  data() {
    return {
      uploadParams: {},
      downTemplateName: this.$t('导入模板'),
      downTemplateParams: {},
      requestUrls: {},
      searchFormModel: {},
      activeTabIndex: 0,
      loading: false,
      tableData: [],
      // 成本因子放大镜弹窗配置
      costFactorDialogCofig: {
        pageConfig: dialogPageConfig(
          '/masterDataManagement/tenant/item-cost-factor/paged-query',
          'costFactor',
          {
            defaultRules: [
              {
                label: this.$t('是否生效'),
                field: 'statusId',
                type: 'string',
                operator: 'equal',
                value: '1'
              }
            ],
            params: {
              onlyCurrentLevel: 0
            }
          }
        ),
        text: 'itemName',
        value: 'itemCode'
      },
      // 供应商放大镜弹窗配置
      supplierDialogCofig: {
        pageConfig: dialogPageConfig(
          '/masterDataManagement/tenant/supplier/pagedQueryByOrgCode',
          'supplier'
        ),
        text: 'supplierName',
        value: 'supplierCode'
      }
    }
  },
  computed: {},
  mounted() {
    this.handleSearch()
  },
  methods: {
    // 日期格式化
    handleDateChange(field, e) {
      let startField = field + 'Start'
      let endField = field + 'End'
      if (field === 'createTime') {
        startField = 'createStartTime'
        endField = 'createEndTime'
      }
      if (e.startDate) {
        const startDate = XEUtils.toDateString(e.startDate, 'yyyy-MM-dd') + ' 00:00:00'
        const endDate = XEUtils.toDateString(e.endDate, 'yyyy-MM-dd') + ' 23:59:59'
        this.searchFormModel[startField] = XEUtils.timestamp(startDate)
        this.searchFormModel[endField] = XEUtils.timestamp(endDate)
      } else {
        this.searchFormModel[startField] = null
        this.searchFormModel[endField] = null
      }
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.$refs.costFactorCode?.handleClear()
      this.$refs.supplierCode?.handleClear()

      this.handleSearch()
    },
    // 查询
    async handleSearch(type) {
      // 解决修改查询条件后，查询结果有数据但未正常显示的问题（type: pageChange/refresh）
      if (!type) {
        this.pageInfo.current = 1
        this.$refs.pageRef.jumpNum = 1
        this.$refs.pageRef.currentPage = 1
      }

      const params = this.formatSearchInfo()
      this.loading = true
      const res = await this.$API.costFactorPriceRecord
        .getCfPriceRecords(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []

        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击工具栏按钮
    handleClickToolBar(e) {
      switch (e.code) {
        case 'export':
          this.handleExport()
          break
        case 'import':
          this.handleImport()
          break
        default:
          break
      }
    },
    // 切换tab页签
    handleTabChange(index) {
      this.$refs.mtTabsRef.activeTab = index
      this.activeTabIndex = index
      this.handleReset()
      this.handleSearch()
    },
    // 导出
    async handleExport() {
      const includeColumnFiledNames = []
      const tableColumns = this.$refs.sctableRef.$refs.xGrid.getColumns()
      tableColumns.forEach((col) => {
        if (col.field?.includes('-')) {
          const fields = col.field?.split('-')
          fields.forEach((field) => includeColumnFiledNames.push(field))
        } else {
          col.field && includeColumnFiledNames.push(col.field)
        }
      })
      const params = {
        ...this.formatSearchInfo(),
        includeColumnFiledNames
      }
      const res = await this.$API.costFactorPriceRecord.exportCfPriceRecords(params)
      if (res.data) {
        this.$toast({
          type: 'success',
          content: this.$t('正在导出，请稍后！')
        })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      }
    },
    // 导入
    handleImport() {
      this.requestUrls = {
        templateUrlPre: 'costFactorPriceRecord',
        templateUrl: 'outPriceImportTemplate',
        uploadUrl: 'outPriceImport'
      }
      this.showUploadExcel(true)
    },
    // 展示/不展示 上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadInitBalanceRef.uploadData = [] // 清空数据
        this.$refs.uploadInitBalanceRef.$refs.uploader.files = []
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.handleSearch()
    },
    // 处理查询参数
    formatSearchInfo() {
      const { createTime, effectiveEndDate, effectiveStartDate } = this.searchFormModel
      const params = {
        priceClassify: this.activeTabIndex + 1, // 1：内部价格，2：外部价格
        page: this.pageInfo,
        ...this.searchFormModel,
        createTime: this.formatDate(createTime),
        effectiveEndDate: this.formatDate(effectiveStartDate),
        effectiveStartDate: this.formatDate(effectiveEndDate)
      }
      delete params.createTime
      delete params.effectiveStartDate
      delete params.effectiveEndDate
      return params
    },
    // 格式化日期-yyyy-MM-dd
    formatDate(date) {
      return date ? XEUtils.toDateString(date, 'yyyy-MM-dd') : null
    }
  }
}
</script>

<style scoped lang="scss">
.full-height {
  height: 100%;
  padding: 0 8px;
  background: #fff;
}

::v-deep {
  .mt-form-item {
    margin-bottom: 10px;
    .label {
      margin: 0 10px 5px 0;
    }
  }
  .mt-tabs-container {
    background-color: #fff;
  }

  .mt-tabs-container ul.tab-container li.tab-item2.active {
    border-bottom: 2px solid #409eff;
  }
  .mt-tabs-container ul.tab-container li.tab-item2.active,
  .mt-tabs-container ul.tab-container li.tab-item2:hover {
    color: #31374e;
  }
}
</style>
