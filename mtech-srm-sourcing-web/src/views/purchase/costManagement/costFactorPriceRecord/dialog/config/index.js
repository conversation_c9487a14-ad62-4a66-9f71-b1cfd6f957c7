import { i18n } from '@/main'

export const columnData = (type) => {
  const defaultColumn = [
    {
      field: 'costFactorCode',
      headerText: i18n.t('成本因子编码')
    },
    {
      field: 'costFactorName',
      headerText: i18n.t('成本因子名称')
    },
    {
      field: 'costFactorSpec',
      headerText: i18n.t('规格')
    },
    {
      field: 'costFactorAttr',
      headerText: i18n.t('属性大类')
    },
    {
      field: 'costFactorGroup',
      headerText: i18n.t('属性中类')
    },
    {
      field: 'basicMeasureUnitName',
      headerText: i18n.t('单位')
    },
    {
      field: 'unitPriceUntaxed',
      headerText: i18n.t('单价（未税）')
    },
    {
      field: 'unitPriceTaxed',
      headerText: i18n.t('单价（含税）')
    },
    {
      field: 'priceUnitName',
      headerText: i18n.t('价格单位')
    },
    {
      field: 'currencyCode',
      headerText: i18n.t('币种编码')
    },
    {
      field: 'currencyName',
      headerText: i18n.t('币种名称')
    },
    {
      field: 'taxRateCode',
      headerText: i18n.t('税率编码')
    },
    {
      field: 'taxRateName',
      headerText: i18n.t('税率名称')
    },
    {
      field: 'taxRate',
      headerText: i18n.t('税率')
    },
    {
      field: 'priceUnitName',
      headerText: i18n.t('价格单位')
    },
    {
      field: 'companyCode',
      headerText: i18n.t('公司编码')
    },
    {
      field: 'companyName',
      headerText: i18n.t('公司名称')
    },
    {
      field: 'sourceCode',
      headerText: i18n.t('来源单号')
    },
    {
      field: 'remark',
      headerText: i18n.t('备注')
    }
  ]

  const outerColumns = [
    {
      field: 'costFactorBrand',
      headerText: i18n.t('品牌')
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码')
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称')
    }
  ]
  if (type === 'outer') {
    defaultColumn.splice(3, 0, outerColumns[0])
    defaultColumn.splice(-2, 0, outerColumns[1], outerColumns[2])
  }
  return defaultColumn
}
