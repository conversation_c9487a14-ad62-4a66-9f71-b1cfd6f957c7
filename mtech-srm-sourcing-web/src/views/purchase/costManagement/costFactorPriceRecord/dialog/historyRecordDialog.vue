<template>
  <mt-dialog ref="dialog" :close="cancel" :buttons="button" :header="header">
    <mt-template-page ref="templateRef" :hidden-tabs="true" :template-config="pageConfig" />
  </mt-dialog>
</template>
<script>
import { columnData } from './config'

export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      button: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('确定') }
        }
      ]
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    pageConfig() {
      return [
        {
          useToolTemplate: false,
          toolbar: { useBaseConfig: false, tools: [[], []] },
          grid: {
            allowFiltering: true,
            columnData: columnData(this.modalData.type),
            asyncConfig: {
              url: this.$API.costFactorPriceRecord.getCfHistoryRecordsUrl,
              params: {
                priceRecordId: this.modalData.id
              }
            }
          }
        }
      ]
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    cancel() {
      this.$emit('confirm-function')
    }
  }
}
</script>
