import { i18n } from '@/main.js'

const materialColumn = [
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]

export const materialPageConfig = (that) => {
  const columns = [...materialColumn]
  if (that.modalData.multiple) {
    columns.unshift({
      width: '50',
      type: 'checkbox'
    })
  }
  return [
    {
      gridId: '74713cd9-0fce-4f4a-a4d8-b190f36a82a0',
      useToolTemplate: false,
      toolbar: [],
      grid: {
        allowFiltering: true,
        columnData: columns,
        allowSorting: false,
        allowSelection: true,
        selectionSettings: {
          type: 'Multiple',
          mode: 'Row'
        },
        asyncConfig: {
          url: `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`
        }
      }
    }
  ]
}
