<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @recordDoubleClick="recordDoubleClick"
      />
    </div>
  </mt-dialog>
</template>

<script>
import { materialPageConfig } from './config/index'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      pageConfig: [],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.pageConfig = materialPageConfig(this)
  },
  methods: {
    recordDoubleClick(e) {
      if (this.modalData.multiple) {
        return
      }
      this.$emit('confirm-function', e.rowData)
    },
    confirm() {
      const selectedRecords = this.$refs.templateRef
        .getCurrentTabRef()
        .gridRef.getMtechGridRecords()
      if (selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      this.$emit('confirm-function', this.modalData.multiple ? selectedRecords : selectedRecords[0])
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  height: 100%;
  width: 100%;
}
</style>
