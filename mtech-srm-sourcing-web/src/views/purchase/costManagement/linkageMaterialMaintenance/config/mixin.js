import XEUtils from 'xe-utils'

export default {
  data() {
    return {
      toolbar: [
        { code: 'add', name: this.$t('新增'), status: 'info' },
        { code: 'save', name: this.$t('保存'), status: 'info' },
        { code: 'enable', name: this.$t('启用'), status: 'info' },
        { code: 'disable', name: this.$t('停用'), status: 'info' },
        { code: 'delete', name: this.$t('删除'), status: 'info' },
        { code: 'import', name: this.$t('导入'), status: 'info' },
        { code: 'export', name: this.$t('导出'), status: 'info' }
      ],
      statusList: [
        { value: 0, text: this.$t('停用') },
        { value: 1, text: this.$t('启用') }
      ],
      companyList: [],
      categoryList: []
    }
  },
  computed: {
    columns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'materialCode',
          title: this.$t('物料编码'),
          editRender: {},
          slots: {
            edit: ({ row, rowIndex }) => {
              return [
                <vxe-input
                  v-model={row.materialCode}
                  disabled={row.optType !== 'add' && row.status === 1}
                  readonly
                  transfer
                  clearable
                  type={row.optType !== 'add' && row.status !== 1 ? 'search' : 'text'}
                  on-clear={() => this.handleClearMaterial(row, rowIndex)}
                  on-search-click={() => this.handleSelectMaterial(row, rowIndex)}
                />
              ]
            }
          }
        },
        {
          field: 'materialName',
          title: this.$t('物料名称')
        },
        {
          field: 'categoryCode',
          title: this.$t('品类编码'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.categoryCode}
                  options={this.categoryList}
                  option-props={{ value: 'categoryCode', label: 'text' }}
                  placeholder={this.$t('请选择')}
                  transfer
                  clearable
                  filterable
                  disabled={row.optType !== 'add' && row.status === 1}
                  onChange={(e) => {
                    const selectItem = this.categoryList.find((t) => t.categoryCode === e.value)
                    row.categoryName = selectItem?.categoryName || null
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'categoryName',
          title: this.$t('品类名称')
        },
        // {
        //   field: 'companyCode',
        //   title: this.$t('公司'),
        //   minWidth: 180,
        //   editRender: {},
        //   slots: {
        //     default: ({ row }) => {
        //       return [<span>{(row.companyCode || '') + '-' + (row.companyName || '')}</span>]
        //     },
        //     edit: ({ row }) => {
        //       return [
        //         <vxe-select
        //           v-model={row.companyCode}
        //           options={this.companyList}
        //           option-props={{ value: 'orgCode', label: 'text' }}
        //           placeholder={this.$t('请选择')}
        //           transfer
        //           clearable
        //           filterable
        //           disabled={row.optType !== 'add' && row.status === 1}
        //           onChange={(e) => {
        //             const selectItem = this.companyList.find((t) => t.orgCode === e.value)
        //             row.companyName = selectItem?.orgName || null
        //           }}
        //         />
        //       ]
        //     }
        //   }
        // },
        {
          field: 'status',
          title: this.$t('状态'),
          slots: {
            default: ({ row }) => {
              const selectItem = this.statusList.find((item) => item.value === row.status)
              const statusName = selectItem?.text
              return [<span>{statusName}</span>]
            }
          }
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          slots: {
            default: ({ row }) => {
              const createTime = XEUtils.toDateString(row.createTime, 'yyyy-MM-dd HH:mm:ss')
              return [<span>{createTime}</span>]
            }
          }
        },
        {
          field: 'updateUserName',
          title: this.$t('更新人')
        },
        {
          field: 'updateTime',
          title: this.$t('更新时间'),
          slots: {
            default: ({ row }) => {
              const updateTime = XEUtils.toDateString(row.updateTime, 'yyyy-MM-dd HH:mm:ss')
              return [<span>{updateTime}</span>]
            }
          }
        }
      ]
    }
  },
  mounted() {
    this.getCategoryList()
    this.getCompanyList()
  },
  methods: {
    // 获取品类下拉列表
    async getCategoryList() {
      const res = await this.$API.masterData.getCategoryList()
      if (res.code === 200) {
        res.data?.forEach((item) => {
          item.text = item.categoryCode + '-' + item.categoryName
        })
        this.categoryList = res.data || []
      }
    },
    // 获取公司下拉列表
    async getCompanyList() {
      const res = await this.$API.masterData.permissionCompanyList()
      if (res.code === 200) {
        res.data?.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.companyList = res.data
      }
    },
    // 清除物料
    handleClearMaterial(row, rowIndex) {
      const fieldList = ['itemCode', 'itemName', 'categoryCode', 'categoryName']
      fieldList.forEach((field) => (row[field] = null))
      this.$set(this.tableData, rowIndex, row)
    },
    // 弹窗选择物料
    handleSelectMaterial(row, rowIndex) {
      this.$dialog({
        modal: () => import('../dialog/materialDialog.vue'),
        data: {
          title: this.$t('物料')
        },
        success: (data) => {
          const { itemCode, itemName, categoryCode, categoryName } = data
          const newRow = {
            ...row,
            materialCode: itemCode,
            materialName: itemName,
            categoryCode,
            categoryName
          }
          this.$set(this.tableData, rowIndex, newRow)
          this.tableRef.setEditRow(newRow)
        },
        close: () => {
          this.tableRef.setEditRow(row)
        }
      })
    }
  }
}
