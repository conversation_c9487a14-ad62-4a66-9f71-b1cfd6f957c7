import { i18n } from '@/main.js'

const materialColumn = [
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  }
]

export const materialDialogPageConfig = [
  {
    gridId: '65de8a0c-6033-4b81-889c-c5890518c21f',
    useToolTemplate: false,
    toolbar: [],
    grid: {
      allowFiltering: true,
      columnData: materialColumn,
      allowSorting: false,
      allowSelection: true,
      selectionSettings: {
        type: 'Multiple',
        mode: 'Row'
      },
      asyncConfig: {
        url: `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`
      }
    }
  }
]
