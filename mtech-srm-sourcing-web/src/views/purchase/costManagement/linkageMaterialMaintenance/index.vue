<template>
  <div class="full-height">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="materialCode" :label="$t('物料')" label-style="top">
          <magnifier-input
            ref="materialCode"
            :config="materialDialogCofig"
            @change="(e) => (searchFormModel.materialCode = e.itemCode || null)"
          />
        </mt-form-item>
        <mt-form-item prop="categoryCode" :label="$t('品类')" label-style="top">
          <mt-select
            v-model="searchFormModel.categoryCode"
            css-class="rule-element"
            :data-source="categoryList"
            :fields="{ text: 'text', value: 'categoryCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择品类')"
          />
        </mt-form-item>
        <!-- <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
          <mt-select
            v-model="searchFormModel.companyCode"
            css-class="rule-element"
            :data-source="companyList"
            :fields="{ text: 'text', value: 'orgCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择公司')"
          />
        </mt-form-item> -->
        <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
          <mt-input
            v-model="searchFormModel.createUserName"
            :show-clear-button="true"
            :placeholder="$t('请输入创建人')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      row-id="id"
      grid-id="6ddbba3b-9bf1-4d3c-a717-eadb798790aa"
      :keep-source="true"
      :edit-config="editConfig"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />

    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-name="downTemplateName"
      :down-template-params="downTemplateParams"
      :request-urls="requestUrls"
      file-key="importFile"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import magnifierInput from '@/components/magnifierInput'
import mixin from './config/mixin'
import pagingMixin from '@/mixins/paging.js'
import { materialDialogPageConfig } from './config/index'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {
    ScTable,
    CollapseSearch,
    magnifierInput,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  mixins: [mixin, pagingMixin],
  data() {
    return {
      searchFormModel: {},
      tableData: [],
      loading: false,
      // 物料放大镜弹窗配置
      materialDialogCofig: {
        pageConfig: materialDialogPageConfig,
        text: 'itemCode',
        value: 'itemCode'
      },
      editConfig: {
        trigger: 'click',
        mode: 'row',
        showStatus: true
      },
      downTemplateName: '',
      downTemplateParams: {},
      requestUrls: {}
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  mounted() {
    this.handleSearch()
  },
  methods: {
    // 获取table数据
    async getTableData() {
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.linkageMaterialMaintenance
        .getLmmList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.total = res.data.total
      }
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.$refs.materialCode.handleClear()
      this.handleSearch()
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (['delete', 'enable', 'disable'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'save':
          this.handleSave()
          break
        case 'enable':
        case 'disable':
          this.handleOperate(e.code, selectedRecords)
          break
        case 'delete':
          this.handleDelete(selectedRecords)
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          this.handleExport()
          break
        default:
          break
      }
    },
    // 新增
    handleAdd() {
      this.$dialog({
        modal: () => import('./dialog/materialDialog.vue'),
        data: {
          title: this.$t('物料'),
          multiple: true
        },
        success: async (list) => {
          const costModelMaterialSaveItemRequestList = []
          // const companyInfo = this.companyList.find((c) => c.orgCode === '1503')
          list.forEach((item) => {
            const { itemCode, itemName, categoryCode, categoryName } = item
            costModelMaterialSaveItemRequestList.push({
              materialCode: itemCode,
              materialName: itemName,
              categoryCode,
              categoryName
              // companyCode: companyInfo.orgCode,
              // companyName: companyInfo.orgName
            })
          })
          const params = {
            costModelMaterialSaveItemRequestList
          }
          const res = await this.$API.linkageMaterialMaintenance.addLmm(params)
          if (res.code === 200) {
            this.$toast({ content: this.$t('新增成功！'), type: 'success' })
            this.handleSearch()
          }
        }
      })
    },
    // 保存
    async handleSave() {
      const costModelMaterialSaveItemRequestList = []
      this.tableData.forEach((row) => {
        const {
          id,
          materialCode,
          materialName,
          categoryCode,
          categoryName,
          companyCode,
          companyName
        } = row
        costModelMaterialSaveItemRequestList.push({
          id,
          materialCode,
          materialName,
          categoryCode,
          categoryName,
          companyCode,
          companyName
        })
      })
      const params = {
        costModelMaterialSaveItemRequestList
      }
      const res = await this.$API.linkageMaterialMaintenance.batchSaveLmm(params)
      if (res.code === 200) {
        this.$toast({ content: this.$t('保存成功！'), type: 'success' })
        this.handleSearch()
      }
    },
    // 启用、停用
    handleOperate(type, list) {
      const operationMap = {
        enable: { title: this.$t('启用'), value: 1 },
        disable: { title: this.$t('停用'), value: 0 }
      }
      const idList = []
      for (let i = 0; i < list.length; i++) {
        if (list[i].optType === 'add') {
          this.$toast({ content: this.$t('包含未保存的行，请先保存！'), type: 'success' })
          return
        }
        if (type == 'enable' && list[i].status === 1) {
          this.$toast({
            content: this.$t('仅可启用状态为【停用】的数据，请检查！'),
            type: 'warning'
          })
          return
        } else if (type == 'disable' && list[i].status === 0) {
          this.$toast({
            content: this.$t('仅可停用状态为【启用】的数据，请检查！'),
            type: 'warning'
          })
          return
        }
        idList.push(list[i].id)
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认${operationMap[type].title}选中的数据？`)
        },
        success: async () => {
          const params = {
            idList,
            status: operationMap[type].value
          }
          const res = await this.$API.linkageMaterialMaintenance.updateStatusLmm(params)
          if (res.code === 200) {
            this.$toast({ content: this.$t(`${operationMap[type].title}成功！`), type: 'success' })
            this.handleSearch()
          }
        }
      })
    },
    // 删除
    handleDelete(list) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: async () => {
          const idList = []
          for (let i = 0; i < list.length; i++) {
            if (list[i].status === 1) {
              this.$toast({
                content: this.$t('仅可删除状态为【停用】的数据，请检查！'),
                type: 'warning'
              })
              return
            }
            idList.push(list[i].id)
          }
          const res = await this.$API.linkageMaterialMaintenance.batchDeleteLmm({ idList })
          if (res.code === 200) {
            this.$toast({ content: this.$t('删除成功！'), type: 'success' })
            this.handleSearch()
          }
        }
      })
    },
    // 导入
    handleImport() {
      this.requestUrls = {
        templateUrlPre: 'linkageMaterialMaintenance',
        templateUrl: 'downloadLmmImportTemplate',
        uploadUrl: 'importLmm'
      }
      this.showUploadExcel(true)
    },
    // 导出
    async handleExport() {
      const res = await this.$API.linkageMaterialMaintenance.exportLmm(this.searchFormModel)
      if (res.data) {
        this.$toast({ type: 'success', content: this.$t('正在导出，请稍后！') })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      }
    },
    // 展示/不展示 上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = [] // 清空数据
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 导入成功
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.handleSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
