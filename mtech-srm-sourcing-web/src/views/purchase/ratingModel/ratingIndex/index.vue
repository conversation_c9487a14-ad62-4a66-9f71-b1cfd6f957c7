<!-- 评分指标 -->
<template>
  <div>
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item prop="indexCode" :label="$t('指标')" label-style="top">
          <mt-select
            v-model="searchFormModel.indexCode"
            :data-source="indexOptions"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择指标')"
          />
        </mt-form-item>
        <mt-form-item prop="indexDimensionsCode" :label="$t('指标维度')" label-style="top">
          <mt-select
            v-model="searchFormModel.indexDimensionsCode"
            :data-source="indexDimensionOptions"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择指标维度')"
          />
        </mt-form-item>
        <mt-form-item prop="indexDescription" :label="$t('指标评分说明')" label-style="top">
          <mt-input
            v-model="searchFormModel.indexDescription"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="scoreMethodCode" :label="$t('评分方式')" label-style="top">
          <mt-select
            v-model="searchFormModel.scoreMethodCode"
            :data-source="scoreMethodOptions"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择评分方式')"
          />
        </mt-form-item>
        <mt-form-item prop="indicatorsStatus" :label="$t('状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.indicatorsStatus"
            :data-source="statusOptions"
            :fields="{ text: 'label', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择状态')"
          />
        </mt-form-item>
        <mt-form-item prop="effectiveTime" :label="$t('生效日期')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.effectiveTime"
            :show-clear-button="true"
            :allow-edit="false"
            :open-on-focus="true"
            :placeholder="$t('请选择创建日期')"
            @change="(e) => handleDateChange(e, 'effectiveTime')"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建日期')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :show-clear-button="true"
            :allow-edit="false"
            :open-on-focus="true"
            :placeholder="$t('请选择创建日期')"
            @change="(e) => handleDateChange(e, 'createTime')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      row-id="id"
      grid-id="6e0443f2-2b0e-4312-beb5-b7a29b9e71c1"
      align="center"
      show-overflow
      keep-source
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      @refresh="handleRefresh"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
      <template #detailDetault="{ row }">
        <div style="cursor: pointer; color: #2783fe" @click="handleDetail(row)">
          {{ $t('明细') }}
        </div>
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
    <DetailDialog ref="detailRef" />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import pagingMixin from '@/mixins/paging.js'
import XEUtils from 'xe-utils'
import { cloneDeep } from 'lodash'
import DetailDialog from './components/DetailDialog.vue'

export default {
  components: {
    CollapseSearch,
    ScTable,
    DetailDialog
  },
  mixins: [mixin, pagingMixin],
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      tableData: [],
      loading: false,
      editConfig: {
        trigger: 'click',
        mode: 'row',
        showStatus: true,
        beforeEditMethod: this.beforeEditMethod
      },
      editRules: {
        indexCode: [{ required: true, message: this.$t('必填') }],
        indexDimensionsCode: [{ required: true, message: this.$t('必填') }],
        scoreMethodCode: [{ required: true, message: this.$t('必填') }]
      },
      indexOptions: [],
      indexDimensionOptions: [],
      scoreMethodOptions: [],
      statusOptions: [
        { label: this.$t('草稿'), value: 0 },
        { label: this.$t('启用'), value: 1 },
        { label: this.$t('失效'), value: 2 },
        { label: this.$t('已删除'), value: 3 }
      ]
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.getIndexOptions()
    this.getIndexDimensionOptions()
    this.getScoreMethodOptions()
  },
  methods: {
    beforeEditMethod({ row }) {
      if ([1, 2].includes(row.indicatorsStatus)) {
        return false
      }
      return true
    },
    async getIndexOptions() {
      const res = await this.$API.masterData.dictionaryGetTreeList({
        dictCode: 'PUBLIC_SOURCING_SCORE_INDEX'
      })
      if (res.code === 200) {
        this.indexOptions = res.data || []
      }
    },
    async getIndexDimensionOptions() {
      const res = await this.$API.masterData.dictionaryGetTreeList({
        dictCode: 'PUBLIC_SOURCING_INDICATOR_DIMENSION'
      })
      if (res.code === 200) {
        this.indexDimensionOptions = res.data || []
      }
    },
    async getScoreMethodOptions() {
      const res = await this.$API.masterData.dictionaryGetTreeList({
        dictCode: 'PUBLIC_SOURCING_SCORE_MODE'
      })
      if (res.code === 200) {
        this.scoreMethodOptions = res.data || []
      }
    },
    handleDateChange(e, flag) {
      if (e.startDate) {
        const startDate = XEUtils.toDateString(e.startDate, 'yyyy-MM-dd') + ' 00:00:00'
        const endDate = XEUtils.toDateString(e.endDate, 'yyyy-MM-dd') + ' 23:59:59'
        this.searchFormModel[flag + 'Start'] = XEUtils.timestamp(startDate)
        this.searchFormModel[flag + 'End'] = XEUtils.timestamp(endDate)
      } else {
        this.searchFormModel[flag + 'Start'] = null
        this.searchFormModel[flag + 'End'] = null
      }
    },
    handleRefresh() {
      const insertRecords = this.tableRef.getInsertRecords()
      const updateRecords = this.tableRef.getUpdateRecords()
      if (insertRecords.length === 0 && updateRecords.length === 0) {
        this.handleSearch()
      } else {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('存在未保存的数据，是否确认刷新？')
          },
          success: () => {
            this.handleSearch()
          }
        })
      }
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          this.currentPage = 1
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    // 获取table数据
    async getTableData() {
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.ratingModel
        .pageRatingIndexApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (['save', 'enable', 'disable', 'delete'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'save':
          this.handleSave(selectedRecords)
          break
        case 'enable':
          this.handleEnable(selectedRecords)
          break
        case 'disable':
          this.handleDisable(selectedRecords)
          break
        case 'delete':
          this.handleDelete(selectedRecords)
          break
        default:
          break
      }
    },
    handleAdd() {
      const item = {}
      this.tableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.tableRef.setEditRow(currentViewRecords[0])
      })
    },
    handleSave(selectedRecords) {
      this.tableRef.validate(selectedRecords).then((valid) => {
        if (valid) {
          this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
          return
        }
        let saveList = cloneDeep([...selectedRecords])
        saveList.forEach((item) => {
          if (item.id?.includes('row_')) {
            item.id = null
          }
        })
        this.handleOperate('save', { saveList })
      })
    },
    handleEnable(selectedRecords) {
      let valid = selectedRecords.every((item) => [0, 2, 3].includes(item.indicatorsStatus))
      if (!valid) {
        this.$toast({
          content: this.$t('请选择【草稿】【失效】【已删除】状态的数据进行启用操作'),
          type: 'warning'
        })
        return
      }
      let statusRequestList = selectedRecords.map((v) => {
        return {
          id: v.id,
          indicatorsStatus: 1,
          indexCode: v.indexCode
        }
      })
      this.handleOperate('enable', { statusRequestList })
    },
    handleDisable(selectedRecords) {
      let valid = selectedRecords.every((item) => [1].includes(item.indicatorsStatus))
      if (!valid) {
        this.$toast({ content: this.$t('请选择【启用】状态的数据进行失效操作'), type: 'warning' })
        return
      }
      let statusRequestList = selectedRecords.map((v) => {
        return {
          id: v.id,
          indicatorsStatus: 2,
          indexCode: v.indexCode
        }
      })
      this.handleOperate('disable', { statusRequestList })
    },
    handleDelete(selectedRecords) {
      let rows = selectedRecords.filter((v) => {
        if (!v?.id?.includes('row_')) {
          return v
        }
      })
      if (rows.length !== 0) {
        let valid = rows.every((item) => [0].includes(item?.indicatorsStatus))
        if (!valid) {
          this.$toast({ content: this.$t('请选择【草稿】状态的数据进行删除操作'), type: 'warning' })
          return
        }
        let statusRequestList = rows.map((v) => {
          return {
            id: v.id,
            indicatorsStatus: 3,
            indexCode: v.indexCode
          }
        })
        this.handleOperate('delete', { statusRequestList })
      }
      this.tableRef.removeCheckboxRow()
    },
    async handleOperate(type, params) {
      let api = null,
        content = ''
      switch (type) {
        case 'save':
          api = this.$API.ratingModel.saveRatingIndexApi
          content = this.$t('保存成功')
          break
        case 'enable':
          api = this.$API.ratingModel.updateStatusRatingIndexApi
          content = this.$t('启用成功')
          break
        case 'disable':
          api = this.$API.ratingModel.updateStatusRatingIndexApi
          content = this.$t('失效成功')
          break
        case 'delete':
          api = this.$API.ratingModel.updateStatusRatingIndexApi
          content = this.$t('删除成功')
          break
        default:
          break
      }
      const res = await api(params)
      if (res.code === 200) {
        this.$toast({ content: content, type: 'success' })
        this.handleSearch()
      }
    },
    handleDetail(row) {
      if (!row?.id?.includes('row_')) {
        this.$refs.detailRef.dialogInit({
          title: this.$t('明细'),
          id: row.id,
          indexCode: row.indexCode,
          indexName: row.indexName,
          editable: [0].includes(row.indicatorsStatus)
        })
      } else {
        this.$toast({ content: this.$t('请先保存数据！'), type: 'warning' })
      }
    }
  }
}
</script>
