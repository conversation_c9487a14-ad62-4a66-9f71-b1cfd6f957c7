<!-- 评分明细 -->
<template>
  <mt-dialog
    ref="dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    :height="685"
    @beforeOpen="beforeOpen"
    @close="close"
  >
    <div class="dialog-content">
      <div>
        <vxe-select
          v-model="scoreIndexType"
          :options="scoreIndexTypeOptions"
          :placeholder="$t('请选择')"
          :disabled="!editable || typeDisable"
        />
      </div>
      <sc-table
        ref="detailSctableRef"
        row-id="id"
        grid-id="a98da3c2-2701-4644-9a5c-0dd49bb48a89"
        align="center"
        show-overflow
        keep-source
        :height="500"
        :loading="loading"
        :columns="columns"
        :table-data="tableData"
        :edit-config="editConfig"
        :edit-rules="editRules"
        :is-show-refresh-bth="true"
        @refresh="getTableData"
        @edit-closed="editComplete"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            size="small"
            @click="handleClickToolBar(item)"
            >{{ item.name }}</vxe-button
          >
        </template>
      </sc-table>
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
export default {
  components: {
    ScTable
  },
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('关闭') }
        }
      ],
      tableData: [],
      loading: false,
      editRules: {
        scoreIndex: [{ required: true, message: this.$t('必填') }],
        scoreGe: [{ required: true, message: this.$t('必填') }],
        scoreLt: [{ required: true, message: this.$t('必填') }],
        scoreStandardCode: [{ required: true, message: this.$t('必填') }],
        score: [{ required: true, message: this.$t('必填') }]
      },
      currentId: null,
      indexCode: null,
      indexName: null,
      editable: false,

      scoreIndexTypeOptions: [
        { label: this.$t('评分'), value: 'SCORE' },
        { label: this.$t('评分标准'), value: 'STANDARD_EVALUATION' }
      ],
      scoreIndexType: 'SCORE',
      scoreStandardOptions: []
    }
  },
  computed: {
    detailTableRef() {
      return this.$refs.detailSctableRef.$refs.xGrid
    },
    columns() {
      let column = [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'scoreIndex',
          title: this.$t('评分指标'),
          minWidth: 160,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input v-model={row.scoreIndex} placeholder={this.$t('请输入')} disabled />
              ]
            }
          }
        },
        {
          field: 'scoreGe',
          title: this.$t('评分') + '≥',
          minWidth: 160,
          editRender: {},
          slots: {
            default: ({ row }) => {
              let text =
                row.scoreIndex === '注册资金'
                  ? row.scoreGe + this.$t('万元')
                  : row.scoreIndex !== '人员规模'
                  ? row.scoreGe + '%'
                  : row.scoreGe
              return [<div>{text}</div>]
            },
            edit: ({ row }) => {
              return [<vxe-input v-model={row.scoreGe} placeholder={this.$t('请输入')} clearable />]
            }
          }
        },
        {
          field: 'scoreLt',
          title: this.$t('评分') + '<',
          minWidth: 160,
          editRender: {},
          slots: {
            default: ({ row }) => {
              let text =
                row.scoreIndex === '注册资金'
                  ? row.scoreLt + this.$t('万元')
                  : row.scoreIndex !== '人员规模'
                  ? row.scoreLt + '%'
                  : row.scoreLt
              return [<div>{text}</div>]
            },
            edit: ({ row }) => {
              return [<vxe-input v-model={row.scoreLt} placeholder={this.$t('请输入')} clearable />]
            }
          }
        },
        {
          field: 'score',
          title: this.$t('得分'),
          minWidth: 160,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type={'number'}
                  v-model={row.score}
                  placeholder={this.$t('请输入')}
                  clearable
                />
              ]
            }
          }
        }
      ]
      if (this.scoreIndexType === 'STANDARD_EVALUATION') {
        column = [
          {
            width: 50,
            type: 'checkbox'
          },
          {
            field: 'scoreIndex',
            title: this.$t('评分指标'),
            minWidth: 160,
            editRender: {},
            slots: {
              edit: ({ row }) => {
                return [
                  <vxe-input v-model={row.scoreIndex} placeholder={this.$t('请输入')} disabled />
                ]
              }
            }
          },
          {
            field: 'scoreStandardCode',
            title: this.$t('评分标准'),
            minWidth: 160,
            editRender: {},
            slots: {
              default: ({ row }) => {
                let label = this.scoreStandardOptions.find(
                  (item) => item.itemCode === row.scoreStandardCode
                )?.itemName
                return [<div>{label || ''}</div>]
              },
              edit: ({ row }) => {
                return [
                  <vxe-select
                    v-model={row.scoreStandardCode}
                    options={this.scoreStandardOptions}
                    option-props={{ label: 'itemName', value: 'itemCode' }}
                    placeholder={this.$t('请选择')}
                    clearable
                    transfer
                    onChange={() => {
                      row.scoreStandard = this.scoreStandardOptions.find(
                        (item) => item.itemCode === row.scoreStandardCode
                      )?.itemName
                    }}
                  />
                ]
              }
            }
          },
          {
            field: 'score',
            title: this.$t('得分'),
            minWidth: 160,
            editRender: {},
            slots: {
              edit: ({ row }) => {
                return [
                  <vxe-input
                    type={'number'}
                    v-model={row.score}
                    placeholder={this.$t('请输入')}
                    clearable
                  />
                ]
              }
            }
          }
        ]
      }
      return column
    },
    toolbar() {
      let arr = []
      if (this.editable) {
        arr = [
          { code: 'add', name: this.$t('新增'), status: 'info' },
          { code: 'delete', name: this.$t('删除'), status: 'info' }
        ]
      }
      return arr
    },
    editConfig() {
      return {
        enabled: this.editable,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    typeDisable() {
      return this.tableData.length > 0
    }
  },
  mounted() {
    this.getScoreStandardOptions()
  },
  methods: {
    async getScoreStandardOptions() {
      const res = await this.$API.masterData.dictionaryGetTreeList({
        dictCode: 'PUBLIC_SOURCING_SCORE_STANDARD'
      })
      if (res.code === 200) {
        this.scoreStandardOptions = res.data || []
      }
    },
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { title, id, indexCode, indexName, editable } = args
      this.dialogTitle = title
      this.currentId = id
      this.indexCode = indexCode
      this.indexName = indexName
      this.editable = editable
      this.getTableData()
    },
    async getTableData() {
      const params = { indexId: this.currentId }
      this.loading = true
      const res = await this.$API.ratingModel
        .detailRatingIndexApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data || []
        if (this.tableData.length > 0) {
          this.scoreIndexType = this.tableData[0].scoreIndexType
        }
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.detailTableRef.getCheckboxRecords()
      if (['delete'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          this.handleDelete(selectedRecords)
          break
        default:
          break
      }
    },
    handleAdd() {
      const item = {
        scoreIndexCode: this.indexCode,
        scoreIndex: this.indexName
      }
      this.detailTableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.detailTableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.detailTableRef.setEditRow(currentViewRecords[0])
      })
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        //1、 校验必填
        this.detailTableRef.validate([row]).then((valid) => {
          if (valid) {
            this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
            return
          }
          this.handleSave(row)
        })
      }
    },
    handleSave(row) {
      let saveRequestList = [row]
      saveRequestList.forEach((item) => {
        item.indexId = this.currentId
        item.scoreIndexType = this.scoreIndexType
        if (item.id?.includes('row_')) {
          item.id = null
        }
      })
      let params = {
        indexId: this.currentId,
        saveRequestList
      }
      this.$API.ratingModel.saveDetailRatingIndexApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('保存成功'), type: 'success' })
          this.getTableData()
        }
      })
    },
    handleDelete(selectedRecords) {
      let idList = selectedRecords.map((v) => v.id)
      let params = {
        indexId: this.currentId,
        idList
      }
      this.$API.ratingModel.deleteDetailRatingIndexApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.getTableData()
        }
      })
    },
    beforeOpen() {
      this.scoreIndexType = 'SCORE'
      this.tableData = []
    },
    onOpen(args) {
      args.preventFocus = true
    },
    close() {
      this.detailTableRef.clearEdit()
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>

<style>
.dialog-content {
  margin-top: 20px;
}
</style>
