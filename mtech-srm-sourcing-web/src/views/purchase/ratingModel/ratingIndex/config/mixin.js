import XEUtils from 'xe-utils'

export default {
  data() {
    return {
      toolbar: [
        { code: 'add', name: this.$t('新增'), status: 'info' },
        { code: 'save', name: this.$t('保存'), status: 'info' },
        { code: 'enable', name: this.$t('启用'), status: 'info' },
        { code: 'disable', name: this.$t('失效'), status: 'info' },
        { code: 'delete', name: this.$t('删除'), status: 'info' }
      ]
    }
  },
  computed: {
    columns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'indexCode',
          title: this.$t('指标名称'),
          minWidth: 160,
          editRender: {},
          slots: {
            default: ({ row }) => {
              let label = this.indexOptions.find(
                (item) => item.itemCode === row.indexCode
              )?.itemName
              return [<div>{label || ''}</div>]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.indexCode}
                  options={this.indexOptions}
                  option-props={{ label: 'itemName', value: 'itemCode' }}
                  placeholder={this.$t('请选择')}
                  clearable
                  transfer
                  onChange={() => {
                    row.indexName = this.indexOptions.find(
                      (item) => item.itemCode === row.indexCode
                    )?.itemName
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'indexDimensionsCode',
          title: this.$t('指标维度'),
          minWidth: 160,
          editRender: {},
          slots: {
            default: ({ row }) => {
              let label = this.indexDimensionOptions.find(
                (item) => item.itemCode === row.indexDimensionsCode
              )?.itemName
              return [<div>{label || ''}</div>]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.indexDimensionsCode}
                  options={this.indexDimensionOptions}
                  option-props={{ label: 'itemName', value: 'itemCode' }}
                  placeholder={this.$t('请选择')}
                  clearable
                  transfer
                  onChange={() => {
                    row.indexDimensions = this.indexDimensionOptions.find(
                      (item) => item.itemCode === row.indexDimensionsCode
                    )?.itemName
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'indexDescription',
          title: this.$t('指标评分说明'),
          minWidth: 160,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.indexDescription}
                  placeholder={this.$t('请输入')}
                  clearable
                  maxlength={110}
                />
              ]
            }
          }
        },
        {
          field: 'scoreMethodCode',
          title: this.$t('评分方式'),
          minWidth: 160,
          editRender: {},
          slots: {
            default: ({ row }) => {
              let label = this.scoreMethodOptions.find(
                (item) => item.itemCode === row.scoreMethodCode
              )?.itemName
              return [<div>{label || ''}</div>]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.scoreMethodCode}
                  options={this.scoreMethodOptions}
                  option-props={{ label: 'itemName', value: 'itemCode' }}
                  placeholder={this.$t('请选择')}
                  clearable
                  transfer
                  onChange={() => {
                    row.scoreMethod = this.scoreMethodOptions.find(
                      (item) => item.itemCode === row.scoreMethodCode
                    )?.itemName
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'indicatorsStatus',
          title: this.$t('状态'),
          minWidth: 120,
          editRender: {},
          slots: {
            default: ({ row }) => {
              let label = this.statusOptions.find(
                (item) => item.value === row.indicatorsStatus
              )?.label
              return [<div>{label || ''}</div>]
            }
          }
        },
        {
          field: 'detail',
          title: this.$t('评分明细'),
          minWidth: 160,
          slots: {
            default: 'detailDetault'
          }
        },
        {
          field: 'effectiveTime',
          title: this.$t('生效日期'),
          minWidth: 160,
          slots: {
            default: ({ row }) => {
              const effectiveTime =
                row.effectiveTime && row.effectiveTime !== '0'
                  ? XEUtils.toDateString(row.effectiveTime, 'yyyy-MM-dd HH:mm:ss')
                  : ''
              return [<span>{effectiveTime}</span>]
            }
          }
        },
        {
          field: 'createUserName',
          title: this.$t('创建人'),
          minWidth: 120
        },
        {
          field: 'createTime',
          title: this.$t('创建日期'),
          minWidth: 160,
          slots: {
            default: ({ row }) => {
              const createTime =
                row.createTime && row.createTime !== '0'
                  ? XEUtils.toDateString(row.createTime, 'yyyy-MM-dd HH:mm:ss')
                  : ''
              return [<span>{createTime}</span>]
            }
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {}
}
