<!-- 评审指标 -->
<template>
  <div>
    <sc-table
      ref="indicatorsRef"
      row-id="id"
      grid-id="93644062-7864-4970-b985-e91cef6abd24"
      align="center"
      show-overflow
      keep-source
      :fix-height="300"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      :is-show-refresh-bth="false"
      @edit-closed="editComplete"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
      <template #detailDetault="{ row }">
        <div style="cursor: pointer; color: #2783fe" @click="handleDetail(row)">
          {{ $t('明细') }}
        </div>
      </template>
    </sc-table>
    <DetailDialog ref="detailRef" @save="indexDetailSave" @delete="indexDetailDelete" />
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import DetailDialog from './DetailDialog.vue'
export default {
  props: {
    editable: {
      type: Boolean,
      default: false
    },
    tableData: {
      type: Array,
      default: () => {
        return []
      }
    },
    loading: {
      type: Boolean,
      default: false
    },
    canAdd: {
      type: Boolean,
      default: true
    },
    dimensionCode: {
      type: String,
      default: null
    }
  },
  components: {
    ScTable,
    DetailDialog
  },
  data() {
    return {
      selectedRow: null,
      editRules: {
        scoreIndexCode: [{ required: true, message: this.$t('必填') }],
        scoreMethodCode: [{ required: true, message: this.$t('必填') }],
        scoreWeight: [{ required: true, message: this.$t('必填') }]
      },
      indexOptions: [],
      scoreMethodOptions: []
    }
  },
  computed: {
    indicatorsTableRef() {
      return this.$refs.indicatorsRef.$refs.xGrid
    },
    columns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'scoreIndexCode',
          title: this.$t('评分指标'),
          minWidth: 160,
          editRender: {},
          slots: {
            default: ({ row }) => {
              let label = this.indexOptions.find(
                (item) => item.itemCode === row.scoreIndexCode
              )?.itemName
              return [<div>{label || ''}</div>]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.scoreIndexCode}
                  options={this.indexOptions}
                  option-props={{ label: 'itemName', value: 'itemCode' }}
                  placeholder={this.$t('请选择')}
                  clearable
                  transfer
                  onChange={() => {
                    row.scoreIndex = this.indexOptions.find(
                      (item) => item.itemCode === row.scoreIndexCode
                    )?.itemName
                    this.indexChange(row)
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'indexDescription',
          title: this.$t('指标说明'),
          minWidth: 160,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.indexDescription}
                  placeholder={this.$t('请输入')}
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'scoreMethodCode',
          title: this.$t('评分方式'),
          minWidth: 160,
          editRender: {},
          slots: {
            default: ({ row }) => {
              let label = this.scoreMethodOptions.find(
                (item) => item.itemCode === row.scoreMethodCode
              )?.itemName
              return [<div>{label || ''}</div>]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.scoreMethodCode}
                  options={this.scoreMethodOptions}
                  option-props={{ label: 'itemName', value: 'itemCode' }}
                  placeholder={this.$t('请选择')}
                  clearable
                  transfer
                  onChange={() => {
                    row.scoreMethod = this.scoreMethodOptions.find(
                      (item) => item.itemCode === row.scoreMethodCode
                    )?.itemName
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'scoreWeight',
          title: this.$t('权重'),
          minWidth: 160,
          editRender: {},
          slots: {
            default: ({ row }) => {
              return row.scoreWeight ? [<div>{row.scoreWeight}%</div>] : ''
            },
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.scoreWeight}
                  type='integer'
                  min={1}
                  max={100}
                  placeholder={this.$t('请输入')}
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'detail',
          title: this.$t('评分明细'),
          minWidth: 160,
          slots: {
            default: 'detailDetault'
          }
        }
      ]
    },
    toolbar() {
      let arr = []
      if (this.editable) {
        arr = [
          { code: 'add', name: this.$t('新增'), status: 'info' },
          { code: 'delete', name: this.$t('删除'), status: 'info' }
        ]
      }
      return arr
    },
    editConfig() {
      return {
        enabled: this.editable,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    }
  },
  created() {
    this.getIndexOptions()
    this.getScoreMethodOptions()
  },
  methods: {
    indexChange(row) {
      let params = {
        indexCode: row.scoreIndexCode,
        indexDimensionsCode: this.dimensionCode,
        indicatorsStatus: 1
      }
      this.$API.ratingModel.queryItemDetailApi(params).then((res) => {
        if (res.code === 200) {
          this.$set(row, 'indexDescription', res.data.indexDescription)
          this.$set(row, 'scoreMethodCode', res.data.scoreMethodCode)
          this.$set(row, 'scoreMethod', res.data.scoreMethod)
          this.$set(
            row,
            'publicSourcingRatingModelIndexItemDtoList',
            res.data.publicSourcingScoringIndicatorsDetailItemRespList
          )
        }
      })
    },
    async getIndexOptions() {
      const res = await this.$API.masterData.dictionaryGetTreeList({
        dictCode: 'PUBLIC_SOURCING_SCORE_INDEX'
      })
      if (res.code === 200) {
        this.indexOptions = res.data || []
      }
    },
    async getScoreMethodOptions() {
      const res = await this.$API.masterData.dictionaryGetTreeList({
        dictCode: 'PUBLIC_SOURCING_SCORE_MODE'
      })
      if (res.code === 200) {
        this.scoreMethodOptions = res.data || []
      }
    },
    indexDetailSave(row) {
      const currentViewRecords = this.indicatorsTableRef.getTableData().visibleData
      let selectedItem = currentViewRecords.find((v) => v.id === this.selectedRow.id)
      if (selectedItem.publicSourcingRatingModelIndexItemDtoList.some((v) => v.id === row.id)) {
        selectedItem.publicSourcingRatingModelIndexItemDtoList.forEach((item) => {
          if (item.id === row.id) {
            item.scoreIndex = row.scoreIndex
            item.scoreGe = row.scoreGe
            item.scoreLt = row.scoreLt
            item.scoreStandardCode = row.scoreStandardCode
            item.scoreStandard = row.scoreStandard
            item.score = row.score
          }
        })
      } else {
        selectedItem.publicSourcingRatingModelIndexItemDtoList.unshift(row)
      }
      this.$emit('save', selectedItem)
    },
    indexDetailDelete(rows) {
      let selectedItem = this.tableData.find((v) => v.id === this.selectedRow.id)
      selectedItem.publicSourcingRatingModelIndexItemDtoList =
        selectedItem.publicSourcingRatingModelIndexItemDtoList.filter((item) =>
          rows.some((v) => v.id !== item.id)
        )
      this.$emit('save', selectedItem)
    },
    handleClickToolBar(e) {
      const selectedRecords = this.indicatorsTableRef.getCheckboxRecords()
      if (['delete'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          this.handleDelete()
          break
        default:
          break
      }
    },
    handleAdd() {
      if (!this.canAdd) {
        this.$toast({ content: this.$t('请先选择维度'), type: 'warning' })
        return
      }
      if (this.indicatorsTableRef.getEditRecord()) {
        // this.$toast({ content: this.$t('数据编辑中！'), type: 'warning' })
        return
      }
      const item = {
        publicSourcingRatingModelIndexItemDtoList: []
      }
      this.indicatorsTableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.indicatorsTableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.indicatorsTableRef.setEditRow(currentViewRecords[0])
      })
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        //1、 校验必填
        this.indicatorsTableRef.validate([row]).then((valid) => {
          if (valid) {
            this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
            return
          }
          this.handleSave(row)
        })
      }
    },
    handleSave(row) {
      this.$emit('save', row)
    },
    handleDelete() {
      const selectedRecords = this.indicatorsTableRef.getCheckboxRecords()
      this.$emit('delete', selectedRecords)
      this.indicatorsTableRef.removeCheckboxRow()
    },
    handleDetail(row) {
      this.selectedRow = row
      this.$refs.detailRef.dialogInit({
        title: this.$t('明细'),
        editable: this.editable,
        scoreIndexCode: row.scoreIndexCode,
        scoreIndex: row.scoreIndex,
        tableData: row?.publicSourcingRatingModelIndexItemDtoList || []
      })
    }
  }
}
</script>
