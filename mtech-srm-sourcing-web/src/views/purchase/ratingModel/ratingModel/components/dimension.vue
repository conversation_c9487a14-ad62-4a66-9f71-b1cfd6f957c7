<!-- 评分维度 -->
<template>
  <div>
    <sc-table
      ref="dimensionRef"
      row-id="id"
      grid-id="b31ca1de-bac5-4890-9b15-7b1a10ac9c8b"
      align="center"
      show-overflow
      keep-source
      :fix-height="300"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      :is-show-refresh-bth="false"
      @edit-closed="editComplete"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { Radio as VxeRadio } from 'vxe-table'
export default {
  props: {
    editable: {
      type: <PERSON>olean,
      default: false
    },
    tableData: {
      type: Array,
      default: () => {
        return []
      }
    },
    loading: {
      type: <PERSON><PERSON>an,
      default: false
    }
  },
  components: {
    ScTable,
    // eslint-disable-next-line vue/no-unused-components
    VxeRadio
  },
  data() {
    return {
      editRules: {
        ratingDimensionCode: [{ required: true, message: this.$t('必填') }],
        scoreWeight: [{ required: true, message: this.$t('必填') }]
      },
      dimensionOptions: []
    }
  },
  computed: {
    dimensionTableRef() {
      return this.$refs.dimensionRef.$refs.xGrid
    },
    columns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'operate',
          title: this.$t('操作'),
          slots: {
            default: ({ row }) => {
              return [
                <vxe-radio
                  v-model={row.checked}
                  label={row.id}
                  onChange={(e) => {
                    this.$set(row, 'isRequest', false)
                    this.radioChange(row, e)
                  }}>
                  {this.$t('评分指标')}
                </vxe-radio>
              ]
            }
          }
        },
        {
          field: 'ratingDimensionCode',
          title: this.$t('评分维度'),
          minWidth: 160,
          editRender: {},
          slots: {
            default: ({ row }) => {
              let label = this.dimensionOptions.find(
                (item) => item.itemCode === row.ratingDimensionCode
              )?.itemName
              return [<div>{label || ''}</div>]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.ratingDimensionCode}
                  options={this.dimensionOptions}
                  option-props={{ label: 'itemName', value: 'itemCode' }}
                  placeholder={this.$t('请选择')}
                  clearable
                  transfer
                  onChange={() => {
                    row.ratingDimension = this.dimensionOptions?.find(
                      (item) => item.itemCode === row.ratingDimensionCode
                    )?.itemName
                    this.$set(row, 'checked', row.id)
                    this.$set(row, 'isRequest', true)
                    this.radioChange(row)
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'scoreWeight',
          title: this.$t('权重'),
          minWidth: 160,
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [<div>{row.scoreWeight}%</div>]
            },
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.scoreWeight}
                  type='integer'
                  min={1}
                  max={100}
                  placeholder={this.$t('请输入')}
                  clearable
                />
              ]
            }
          }
        }
      ]
    },
    toolbar() {
      let arr = []
      if (this.editable) {
        arr = [
          { code: 'add', name: this.$t('新增'), status: 'info' },
          { code: 'delete', name: this.$t('删除'), status: 'info' }
        ]
      }
      return arr
    },
    editConfig() {
      return {
        enabled: this.editable,
        trigger: 'click',
        mode: 'row',
        showStatus: true,
        beforeEditMethod: this.beforeEditMethod
      }
    }
  },
  mounted() {
    this.getDimensionOptions()
  },
  methods: {
    beforeEditMethod({ row }) {
      return row.checked
    },
    async getDimensionOptions() {
      const res = await this.$API.masterData.dictionaryGetTreeList({
        dictCode: 'PUBLIC_SOURCING_INDICATOR_DIMENSION'
      })
      if (res.code === 200) {
        this.dimensionOptions = res.data || []
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.dimensionTableRef.getCheckboxRecords()
      if (['delete'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          this.handleDelete()
          break
        default:
          break
      }
    },
    handleAdd() {
      if (this.dimensionTableRef.getEditRecord()) {
        // this.$toast({ content: this.$t('数据编辑中！'), type: 'warning' })
        return
      }
      const item = {}
      this.dimensionTableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.dimensionTableRef.getTableData().visibleData
        this.$set(currentViewRecords[0], 'checked', currentViewRecords[0].id)
        currentViewRecords.forEach((item) => {
          if (currentViewRecords[0].id !== item.id) {
            this.$set(item, 'checked', null)
          }
        })
        // 将新增的那一条设置为编辑状态
        this.dimensionTableRef.setEditRow(currentViewRecords[0])
      })
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        //1、 校验必填
        this.dimensionTableRef.validate([row]).then((valid) => {
          if (valid) {
            this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
            return
          }
          this.handleSave(row)
        })
      }
    },
    handleSave(row) {
      this.$emit('save', row)
    },
    handleDelete() {
      const selectedRecords = this.dimensionTableRef.getCheckboxRecords()
      this.$emit('delete', selectedRecords)
      this.dimensionTableRef.removeCheckboxRow()
    },
    radioChange(row) {
      const currentViewRecords = this.dimensionTableRef.getTableData().fullData
      currentViewRecords.forEach((item) => {
        if (row.id !== item.id) {
          this.$set(item, 'checked', null)
        }
      })
      this.$emit('radioChange', row)
    }
  }
}
</script>
