<!-- 评分模型详情 -->
<template>
  <div class="publicSourcing-full-height">
    <div class="top-container">
      <div class="top-content">
        <div class="top-info">
          <div class="title"></div>
          <div class="top-btns">
            <vxe-button
              v-for="(item, index) in detailToolbar"
              v-show="item.isShow"
              :key="index"
              :icon="item.icon"
              :status="item.status"
              :disabled="item.disabled"
              size="small"
              @click="handleClickToolBar(item)"
              >{{ item.name }}</vxe-button
            >
          </div>
        </div>
      </div>
    </div>
    <div class="body-container">
      <collapse :title="$t('基本信息')">
        <template slot="content">
          <div class="form-content">
            <mt-form ref="dataFormRef" :model="dataForm" :rules="formRules">
              <mt-form-item prop="modelCode" :label="$t('模型编码')" label-style="top">
                <vxe-input v-model="dataForm.modelCode" disabled />
              </mt-form-item>
              <mt-form-item prop="modelName" :label="$t('模型名称')" label-style="top">
                <vxe-input
                  v-model="dataForm.modelName"
                  :disabled="!editable"
                  clearable
                  :placeholder="$t('请输入模型名称')"
                />
              </mt-form-item>
              <mt-form-item prop="orgCode" :label="$t('适用范围')" label-style="top">
                <vxe-select
                  v-model="dataForm.orgCode"
                  :options="companyList"
                  :option-props="{ label: 'text', value: 'orgCode' }"
                  clearable
                  filterable
                  :disabled="!editable"
                  :placeholder="$t('请选择适用范围')"
                  @change="
                    ({ value }) => {
                      const selectedItem = companyList.find((item) => item.orgCode === value)
                      dataForm.orgId = selectedItem ? selectedItem.id : null
                      dataForm.orgName = selectedItem ? selectedItem.orgName : null
                    }
                  "
                />
              </mt-form-item>
              <mt-form-item prop="statusLabel" :label="$t('状态')" label-style="top">
                <vxe-input v-model="dataForm.statusLabel" disabled />
              </mt-form-item>
            </mt-form>
          </div>
        </template>
      </collapse>
      <collapse :title="$t('评分维度')">
        <template slot="content">
          <dimension
            ref="dimensionRef"
            :editable="editable"
            :table-data="dimensionData"
            :loading="dimensionLoading"
            @radioChange="radioChange"
            @save="dimensionSave"
            @delete="dimensionDelete"
          />
        </template>
      </collapse>
      <collapse :title="$t('评分指标')">
        <template slot="content">
          <indicators
            ref="indicatorsRef"
            :editable="editable"
            :table-data="indexData"
            :loading="indicatorsLoading"
            :can-add="indicatorsAdd"
            :dimension-code="dimensionCode"
            @save="indicatorsSave"
            @delete="indicatorsDelete"
          />
        </template>
      </collapse>
    </div>
  </div>
</template>

<script>
import cloneDeep from 'lodash/cloneDeep'
import collapse from '../components/collapse.vue'
import dimension from '../components/dimension.vue'
import indicators from '../components/indicators.vue'
import mixin from '../config/mixin'
export default {
  components: { collapse, dimension, indicators },
  mixins: [mixin],
  data() {
    return {
      dataForm: {},
      formRules: {
        modelName: [{ required: true, message: this.$t('请输入模型名称'), trigger: 'blur' }],
        orgCode: [{ required: true, message: this.$t('请选择适用范围'), trigger: 'blur' }]
      },
      publicSourcingRatingModelRangeDtoList: [],
      publicSourcingRatingModelScoreLatitudeDtoList: [],

      dimensionData: [],
      dimensionLoading: false,
      indexData: [],
      indicatorsLoading: false,

      selectedRow: null
    }
  },
  computed: {
    indicatorsAdd() {
      return this.selectedRow?.id ? true : false
    },
    dimensionCode() {
      return this.selectedRow?.ratingDimensionCode
    },
    detailToolbar() {
      let toolbar = [
        {
          code: 'back',
          name: this.$t('返回'),
          status: 'primary',
          isShow: true
        }
      ]
      if (this.editable) {
        toolbar = [
          {
            code: 'back',
            name: this.$t('返回'),
            status: 'primary',
            isShow: true
          },
          {
            code: 'save',
            name: this.$t('保存'),
            status: 'info',
            isShow: true
          }
        ]
      }
      return toolbar
    },
    editable() {
      return this.$route.query?.type !== 'view'
    },
    currentId() {
      return this.$route.query?.id
    }
  },
  created() {
    if (this.currentId) {
      this.getDetail(this.currentId)
    }
  },
  methods: {
    dimensionSave(row) {
      if (this.publicSourcingRatingModelScoreLatitudeDtoList.some((v) => v.id === row.id)) {
        this.publicSourcingRatingModelScoreLatitudeDtoList.forEach((item) => {
          if (item.id === row.id) {
            item.ratingDimensionCode = row.ratingDimensionCode
            item.ratingDimension = row.ratingDimension
            item.scoreWeight = row.scoreWeight
            item.publicSourcingRatingModelIndexDtoList = cloneDeep(this.indexData)
          }
        })
      } else {
        row.publicSourcingRatingModelIndexDtoList = cloneDeep(this.indexData)
        this.publicSourcingRatingModelScoreLatitudeDtoList.unshift(row)
      }
    },
    dimensionDelete(rows) {
      this.publicSourcingRatingModelScoreLatitudeDtoList =
        this.publicSourcingRatingModelScoreLatitudeDtoList.filter(
          (item) => !rows.some((v) => v.id === item.id)
        )
    },
    radioChange(row) {
      this.selectedRow = row
      if (row.isRequest) {
        let params = {
          indexDimensionsCode: row.ratingDimensionCode,
          indicatorsStatus: 1,
          page: {
            current: 1,
            size: 500
          }
        }
        this.indicatorsLoading = true
        this.$API.ratingModel
          .pageRatingIndexApi(params)
          .then((res) => {
            if (res.code === 200) {
              this.indexData = res.data?.records.map((item) => {
                return {
                  scoreIndexCode: item.indexCode,
                  scoreIndex: item.indexName,
                  scoreWeight: Number(100 / res.data?.records.length),
                  ...item
                }
              })
              this.getIndexDetail()
            }
          })
          .finally(() => {
            this.indicatorsLoading = false
          })
      } else {
        let selectedItem = this.publicSourcingRatingModelScoreLatitudeDtoList.find(
          (v) => v.id === row.id
        )
        this.indexData = cloneDeep(selectedItem.publicSourcingRatingModelIndexDtoList) || []
      }
    },
    async getIndexDetail() {
      const promises = this.indexData.map((item) =>
        this.$API.ratingModel.detailRatingIndexApi({ indexId: item.id })
      )
      const result = await Promise.all(promises)
      result?.forEach((res, i) => {
        this.indexData[i].publicSourcingRatingModelIndexItemDtoList = res?.data
      })
    },
    indicatorsSave(row) {
      let selectedItem = this.publicSourcingRatingModelScoreLatitudeDtoList.find(
        (v) => v.id === this.selectedRow.id
      )
      if (selectedItem.publicSourcingRatingModelIndexDtoList.some((v) => v.id === row.id)) {
        selectedItem.publicSourcingRatingModelIndexDtoList.forEach((item) => {
          if (item.id === row.id) {
            item.scoreIndexCode = row.scoreIndexCode
            item.scoreIndex = row.scoreIndex
            item.indexDescription = row.indexDescription
            item.scoreMethodCode = row.scoreMethodCode
            item.scoreMethod = row.scoreMethod
            item.scoreWeight = row.scoreWeight
            item.publicSourcingRatingModelIndexItemDtoList =
              row.publicSourcingRatingModelIndexItemDtoList
          }
        })
      } else {
        selectedItem.publicSourcingRatingModelIndexDtoList.unshift(row)
      }
    },
    indicatorsDelete(rows) {
      let selectedItem = this.publicSourcingRatingModelScoreLatitudeDtoList.find(
        (v) => v.id === this.selectedRow.id
      )
      selectedItem.publicSourcingRatingModelIndexDtoList =
        selectedItem.publicSourcingRatingModelIndexDtoList.filter(
          (item) => !rows.some((v) => v.id === item.id)
        )
    },
    getDetail(id) {
      let params = { id }
      this.dimensionLoading = true
      this.$API.ratingModel
        .detailRatingModelApi(params)
        .then((res) => {
          if (res.code === 200) {
            this.dataForm = res.data?.publicSourcingRatingModelDto
            let label = this.statusOptions.find(
              (item) => item.value === this.dataForm.modelStatus
            )?.label
            this.dataForm.statusLabel = label
            this.publicSourcingRatingModelRangeDtoList =
              res.data?.publicSourcingRatingModelRangeDtoList
            this.dataForm.orgId = this.publicSourcingRatingModelRangeDtoList[0].orgId
            this.dataForm.orgName = this.publicSourcingRatingModelRangeDtoList[0].orgName
            this.$set(
              this.dataForm,
              'orgCode',
              this.publicSourcingRatingModelRangeDtoList[0].orgCode
            )
            this.publicSourcingRatingModelScoreLatitudeDtoList =
              res.data?.publicSourcingRatingModelScoreLatitudeDtoList.map((item) => {
                item.publicSourcingRatingModelIndexDtoList =
                  item?.publicSourcingRatingModelIndexDtoList?.map((e) => {
                    e.publicSourcingRatingModelIndexItemDtoList =
                      e?.publicSourcingRatingModelIndexItemDtoList?.map((v) => {
                        return {
                          ...v
                        }
                      }) || []
                    return {
                      ...e
                    }
                  }) || []
                return {
                  ...item
                }
              })
            this.dimensionData = cloneDeep(this.publicSourcingRatingModelScoreLatitudeDtoList)
          }
        })
        .finally(() => {
          this.dimensionLoading = false
        })
    },
    handleClickToolBar(e) {
      switch (e.code) {
        case 'back':
          this.handleBack()
          break
        case 'save':
          this.handleSave()
          break
        default:
          break
      }
    },
    handleBack() {
      // this.$router.go(-1)
      this.$refs.dimensionRef.dimensionTableRef.clearEdit()
      this.$refs.indicatorsRef.indicatorsTableRef.clearEdit()
      this.$router.push({
        name: 'rating-model',
        query: {
          timeStamp: new Date().getTime()
        }
      })
    },
    handleSave() {
      if (!this.dataForm.modelName) {
        this.$toast({ content: this.$t('请输入模型名称'), type: 'warning' })
        return
      }
      if (!this.dataForm.orgCode) {
        this.$toast({ content: this.$t('请选择适用范围'), type: 'warning' })
        return
      }
      let params = {
        publicSourcingRatingModelReq: {
          id: this.dataForm?.id || null,
          modelName: this.dataForm.modelName
        },
        publicSourcingRatingModelRangeReqList: [
          {
            orgId: this.dataForm.orgId,
            orgCode: this.dataForm.orgCode,
            orgName: this.dataForm.orgName
          }
        ],
        publicSourcingRatingModelScoreLatitudeReqList: []
      }
      params.publicSourcingRatingModelScoreLatitudeReqList =
        this.publicSourcingRatingModelScoreLatitudeDtoList.map((item) => {
          let publicSourcingRatingModelIndexReqList =
            item.publicSourcingRatingModelIndexDtoList.map((e) => {
              let publicSourcingRatingModelIndexItemReqList =
                e.publicSourcingRatingModelIndexItemDtoList.map((v) => {
                  return {
                    modelId: v?.modelId || null,
                    indexId: v?.indexId || null,
                    modelIndexId: v?.modelIndexId || null,
                    id: v.id?.includes('row_') ? null : v.id,
                    scoreIndexType: v.scoreIndexType,
                    scoreIndexCode: v.scoreIndexCode,
                    scoreIndex: v.scoreIndex,
                    scoreGe: v.scoreGe,
                    scoreLt: v.scoreLt,
                    scoreStandardCode: v.scoreStandardCode,
                    scoreStandard: v.scoreStandard,
                    score: v.score
                  }
                })
              return {
                modelId: e?.modelId || null,
                indexId: e?.indexId || null,
                id: e.id?.includes('row_') ? null : e.id,
                scoreIndexCode: e.scoreIndexCode,
                scoreIndex: e.scoreIndex,
                indexDescription: e.indexDescription,
                scoreMethodCode: e.scoreMethodCode,
                scoreMethod: e.scoreMethod,
                scoreWeight: e.scoreWeight,
                publicSourcingRatingModelIndexItemReqList
              }
            })
          return {
            modelId: item?.modelId || null,
            id: item.id?.includes('row_') ? null : item.id,
            ratingDimensionCode: item.ratingDimensionCode,
            ratingDimension: item.ratingDimension,
            scoreWeight: item.scoreWeight,
            publicSourcingRatingModelIndexReqList
          }
        })
      this.$store.commit('startLoading')
      this.$API.ratingModel
        .saveRatingModelApi(params)
        .then((res) => {
          if (res.code === 200) {
            this.getDetail(res.data)
            this.indexData = []
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.publicSourcing-full-height {
  display: flex;
  flex-direction: column;
  padding: 8px 8px 0;
  background: #fff;
}
.top-container {
  flex: 1;
  width: 100%;
  .top-content {
    padding: 8px 8px 0 8px;
    // background-color: #f9f9f9;
    border-radius: 8px;
    .top-info {
      display: flex;
      justify-content: space-between;
      .title {
        font-size: 20px;
        font-weight: 700;
        color: #292929;
        span {
          margin-right: 15px;
        }
        .sub-title {
          font-size: 15px;
          color: #777;
        }
      }
      .top-btns {
        text-align: right;
        margin-bottom: 10px;
      }
    }
    .top-form {
      .mt-form {
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
        justify-content: space-between;
        grid-gap: 5px;
      }
      .vxe-input,
      .vxe-select,
      .vxe-pulldown {
        width: 100%;
        height: 34px;
        line-height: 34px;
      }
      /deep/.vxe-pulldown--panel {
        min-width: unset !important;
        width: 100%;
      }
    }
  }
  .top-toggle-btn {
    width: 100%;
    div {
      width: 80px;
      height: 15px;
      line-height: 15px;
      margin: auto;
      text-align: center;
      background-color: #4a556b;
      color: #fff;
      font-weight: bold;
      border-radius: 0 0 6px 6px;
    }
  }
}
.body-container {
  height: calc(100% - 80px);
}
.form-content {
  .mt-form {
    width: 100%;
    box-sizing: border-box;
    display: grid;
    grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
    justify-content: space-between;
    grid-gap: 5px;
  }
  .vxe-input,
  .vxe-select,
  .vxe-pulldown {
    width: 100%;
    height: 34px;
    line-height: 34px;
  }
  /deep/.vxe-pulldown--panel {
    min-width: unset !important;
    width: 100%;
  }
}
::v-deep {
  .mt-form-item {
    margin-bottom: 15px;
    .label {
      margin: 0 10px 5px 0;
    }
  }
  .mt-tabs-container {
    background-color: #fff;
  }

  .mt-tabs-container ul.tab-container li.tab-item2.active {
    border-bottom: 2px solid #409eff;
  }
  .mt-tabs-container ul.tab-container li.tab-item2.active,
  .mt-tabs-container ul.tab-container li.tab-item2:hover {
    color: #31374e;
  }

  .vxe-textarea--inner {
    min-height: 34px;
    height: 100%;
  }
}
</style>
