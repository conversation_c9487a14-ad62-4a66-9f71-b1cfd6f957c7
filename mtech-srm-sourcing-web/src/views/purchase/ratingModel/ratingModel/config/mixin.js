import XEUtils from 'xe-utils'

export default {
  data() {
    return {
      toolbar: [
        { code: 'add', name: this.$t('新增'), status: 'info' },
        { code: 'enable', name: this.$t('启用'), status: 'info' },
        { code: 'disable', name: this.$t('失效'), status: 'info' },
        { code: 'delete', name: this.$t('删除'), status: 'info' }
      ],
      statusOptions: [
        { label: this.$t('草稿'), value: 0 },
        { label: this.$t('启用'), value: 1 },
        { label: this.$t('失效'), value: 2 },
        { label: this.$t('已删除'), value: 3 }
      ],
      companyList: []
    }
  },
  computed: {
    columns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'modelCode',
          title: this.$t('模型编码'),
          minWidth: 160,
          editRender: {},
          slots: {
            default: 'modelDefault',
            edit: ({ row }) => {
              return [
                <vxe-input v-model={row.modelCode} placeholder={this.$t('请输入')} clearable />
              ]
            }
          }
        },
        {
          field: 'modelName',
          title: this.$t('模型名称'),
          minWidth: 160,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input v-model={row.modelName} placeholder={this.$t('请输入')} clearable />
              ]
            }
          }
        },
        {
          field: 'suitRange',
          title: this.$t('适用范围'),
          minWidth: 160
        },
        {
          field: 'modelStatus',
          title: this.$t('状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              let label = this.statusOptions.find((item) => item.value === row.modelStatus)?.label
              return [<div>{label || ''}</div>]
            }
          }
        },
        {
          field: 'effectiveTime',
          title: this.$t('生效日期'),
          minWidth: 160,
          slots: {
            default: ({ row }) => {
              const effectiveTime =
                row.effectiveTime && row.effectiveTime !== '0'
                  ? XEUtils.toDateString(row.effectiveTime, 'yyyy-MM-dd HH:mm:ss')
                  : ''
              return [<span>{effectiveTime}</span>]
            }
          }
        },
        {
          field: 'createUserName',
          title: this.$t('创建人'),
          minWidth: 120
        },
        {
          field: 'createTime',
          title: this.$t('创建日期'),
          slots: {
            default: ({ row }) => {
              const createTime =
                row.createTime && row.createTime !== '0'
                  ? XEUtils.toDateString(row.createTime, 'yyyy-MM-dd HH:mm:ss')
                  : ''
              return [<span>{createTime}</span>]
            }
          },
          minWidth: 120
        }
      ]
    }
  },
  mounted() {
    this.getCompanyList()
  },
  methods: {
    async getCompanyList() {
      const res = await this.$API.masterData.permissionCompanyList()
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.companyList = res.data
      }
    }
  }
}
