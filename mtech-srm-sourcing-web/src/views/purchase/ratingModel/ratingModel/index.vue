<!-- 评分模型 -->
<template>
  <div>
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item prop="modelCode" :label="$t('模型编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.modelCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="modelName" :label="$t('模型名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.modelName"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="orgCodeList" :label="$t('适用范围')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.orgCodeList"
            css-class="rule-element"
            :data-source="companyList"
            :fields="{ text: 'text', value: 'orgCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="modelStatus" :label="$t('状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.modelStatus"
            :data-source="statusOptions"
            :fields="{ text: 'label', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择状态')"
          />
        </mt-form-item>
        <mt-form-item prop="effectiveTime" :label="$t('生效日期')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.effectiveTime"
            :show-clear-button="true"
            :allow-edit="false"
            :open-on-focus="true"
            :placeholder="$t('请选择创建日期')"
            @change="(e) => handleDateChange(e, 'effectiveTime')"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建日期')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :show-clear-button="true"
            :allow-edit="false"
            :open-on-focus="true"
            :placeholder="$t('请选择创建日期')"
            @change="(e) => handleDateChange(e, 'createTime')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      row-id="id"
      grid-id="f80f70b7-3bb2-4767-9f00-2e32d8f04bf5"
      align="center"
      show-overflow
      keep-source
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
      <template #modelDefault="{ row }">
        <div style="cursor: pointer; color: #2783fe" @click="handleDetail(row)">
          {{ row.modelCode }}
        </div>
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import pagingMixin from '@/mixins/paging.js'
import XEUtils from 'xe-utils'

export default {
  components: {
    CollapseSearch,
    ScTable
  },
  mixins: [mixin, pagingMixin],
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      tableData: [],
      loading: false,
      editRules: {}
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  methods: {
    handleDateChange(e, flag) {
      if (e.startDate) {
        const startDate = XEUtils.toDateString(e.startDate, 'yyyy-MM-dd') + ' 00:00:00'
        const endDate = XEUtils.toDateString(e.endDate, 'yyyy-MM-dd') + ' 23:59:59'
        this.searchFormModel[flag + 'Start'] = XEUtils.timestamp(startDate)
        this.searchFormModel[flag + 'End'] = XEUtils.timestamp(endDate)
      } else {
        this.searchFormModel[flag + 'Start'] = null
        this.searchFormModel[flag + 'End'] = null
      }
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          this.currentPage = 1
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    // 获取table数据
    async getTableData() {
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.ratingModel
        .pageRatingModelApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (['enable', 'disable', 'delete'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'enable':
          this.handleEnable(selectedRecords)
          break
        case 'disable':
          this.handleDisable(selectedRecords)
          break
        case 'delete':
          this.handleDelete(selectedRecords)
          break
        default:
          break
      }
    },
    handleAdd() {
      this.$router.push({
        name: 'rating-model-detail',
        query: {
          type: 'create',
          timeStamp: new Date().getTime()
        }
      })
    },
    handleEnable(selectedRecords) {
      let valid = selectedRecords.every((item) => [0, 2, 3].includes(item.modelStatus))
      if (!valid) {
        this.$toast({
          content: this.$t('请选择【草稿】【失效】【已删除】状态的数据进行启用操作'),
          type: 'warning'
        })
        return
      }
      let statusRequestList = selectedRecords.map((v) => {
        return {
          id: v.id,
          modelStatus: 1
        }
      })
      this.handleOperate('enable', { statusRequestList })
    },
    handleDisable(selectedRecords) {
      let valid = selectedRecords.every((item) => [1].includes(item.modelStatus))
      if (!valid) {
        this.$toast({ content: this.$t('请选择【启用】状态的数据进行失效操作'), type: 'warning' })
        return
      }
      let statusRequestList = selectedRecords.map((v) => {
        return {
          id: v.id,
          modelStatus: 2
        }
      })
      this.handleOperate('disable', { statusRequestList })
    },
    handleDelete(selectedRecords) {
      let rows = selectedRecords.filter((v) => {
        if (!v?.id?.includes('row_')) {
          return v
        }
      })
      if (rows.length !== 0) {
        let valid = rows.every((item) => [0].includes(item?.modelStatus))
        if (!valid) {
          this.$toast({ content: this.$t('请选择【草稿】状态的数据进行删除操作'), type: 'warning' })
          return
        }
        let statusRequestList = rows.map((v) => {
          return {
            id: v.id,
            modelStatus: 3
          }
        })
        this.handleOperate('delete', { statusRequestList })
      }
    },
    async handleOperate(type, params) {
      let api = null,
        content = ''
      switch (type) {
        case 'enable':
          api = this.$API.ratingModel.updateStatusRatingModelApi
          content = this.$t('启用成功')
          break
        case 'disable':
          api = this.$API.ratingModel.updateStatusRatingModelApi
          content = this.$t('失效成功')
          break
        case 'delete':
          api = this.$API.ratingModel.updateStatusRatingModelApi
          content = this.$t('删除成功')
          break
        default:
          break
      }
      const res = await api(params)
      if (res.code === 200) {
        this.$toast({ content: content, type: 'success' })
        this.handleSearch()
      }
    },
    handleDetail(row) {
      this.$router.push({
        name: 'rating-model-detail',
        query: {
          id: row.id,
          type: row.modelStatus === 0 ? 'update' : 'view',
          timeStamp: new Date().getTime()
        }
      })
    }
  }
}
</script>
