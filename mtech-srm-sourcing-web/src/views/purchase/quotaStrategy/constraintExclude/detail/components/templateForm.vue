<template>
  <div>
    <div class="header-box">
      <!-- 右侧各种操作按钮 -->
      <span class="header-title">{{ getTitle() }}</span>
      <div>
        <mt-button
          v-if="pageType !== 'detail'"
          css-class="e-flat"
          :is-primary="true"
          @click="save()"
          >{{ $t('保存') }}</mt-button
        >
        <mt-button
          v-if="pageType !== 'detail'"
          css-class="e-flat"
          :is-primary="true"
          @click="submit()"
          >{{ $t('提交') }}</mt-button
        >
        <mt-button css-class="e-flat" :is-primary="true" @click="backTo">{{
          $t('返回')
        }}</mt-button>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-form">
      <mt-form ref="ruleForm" :model="addForm" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="companyId" :label="$t('公司')">
          <mt-select
            v-if="pageType !== 'detail'"
            v-model="addForm.companyId"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="companyList"
            :disabled="pageType === 'detail'"
            :fields="{ text: 'orgName', value: 'id' }"
            :placeholder="$t('请选择公司')"
            @change="companyChange"
          ></mt-select>
          <mt-input v-else v-model="addForm.companyName" disabled />
        </mt-form-item>
        <mt-form-item prop="type" :label="$t('放行供应商类型')">
          <mt-select
            v-model="addForm.type"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :disabled="pageType === 'detail'"
            :show-clear-button="true"
            :data-source="[
              { label: this.$t('新合格'), value: 'XHG' },
              { label: this.$t('黄牌'), value: 'HP' },
              { label: this.$t('暂停恢复'), value: 'ZTHF' },
              { label: this.$t('价差'), value: 'JC' }
            ]"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('请选择放行供应商类型')"
            @change="typeChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商编码')">
          <magnifier-input
            v-if="pageType !== 'detail'"
            :disabled="pageType === 'detail' || !addForm.companyId"
            :default-value="addForm.supplierCode"
            :config="supplierDialogCofig"
            @change="supplierCodeChange($event)"
          ></magnifier-input>
          <mt-input v-else v-model="addForm.supplierCode" disabled />
        </mt-form-item>
        <mt-form-item prop="supplierName" :label="$t('供应商名称')">
          <mt-input v-model="addForm.supplierName" disabled />
        </mt-form-item>
        <mt-form-item prop="dimension" :label="$t('放行维度')">
          <mt-select
            v-model="addForm.dimension"
            float-label-type="Never"
            :disabled="pageType === 'detail'"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="[
              { label: this.$t('整体放行'), value: 'ZTFX' },
              { label: this.$t('部分放行'), value: 'BFFX' }
            ]"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('请选择放行维度')"
            @change="dimensionTypeChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item v-if="addForm.dimension === 'BFFX'" prop="categoryId" :label="$t('品类')">
          <magnifier-input
            v-if="pageType !== 'detail'"
            :disabled="pageType === 'detail'"
            :default-value="addForm.categoryCode"
            :config="categoryDialogCofig"
            @change="categoryCodeChange($event)"
          ></magnifier-input>
          <mt-input v-else v-model="addForm.categoryName" disabled />
        </mt-form-item>
        <mt-form-item prop="startDate" :label="$t('开始月份')">
          <mt-date-picker
            v-model="addForm.startDate"
            :disabled="pageType === 'detail'"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :separator="$t('至')"
            :placeholder="$t('选择开始月份')"
            @change="startDateChange"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="endDate" :label="$t('结束月份')">
          <mt-date-picker
            v-model="addForm.endDate"
            :disabled="pageType === 'detail'"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :separator="$t('至')"
            :placeholder="$t('选择结束月份')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item v-if="pageType" prop="code" :label="$t('放行配额单号')">
          <mt-input v-model="addForm.code" disabled :placeholder="$t('请选择放行配额单号')" />
        </mt-form-item>
        <mt-form-item
          v-if="pageType === 'detail' || pageType === 'edit'"
          prop="status"
          :label="$t('单据状态')"
        >
          <mt-select
            v-model="addForm.status"
            float-label-type="Never"
            disabled
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="statusOptions"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('单据状态')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item v-if="pageType" prop="createUserName" :label="$t('创建人')">
          <mt-input v-model="addForm.createUserName" disabled :placeholder="$t('请选择创建人')" />
        </mt-form-item>
        <mt-form-item v-if="pageType" prop="createTime" :label="$t('创建时间')">
          <mt-date-picker
            v-model="addForm.createTime"
            disabled
            :open-on-focus="true"
            float-label-type="Never"
            width="100%"
            :placeholder="$t('请选择创建时间')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')">
          <mt-input
            v-model="addForm.remark"
            :disabled="pageType === 'detail'"
            type="text"
            :placeholder="$t('请输入备注')"
          />
        </mt-form-item>
        <mt-form-item
          v-if="
            !(
              pageType === 'detail' &&
              addForm.quotaStrategyFileSaveRequests &&
              !addForm.quotaStrategyFileSaveRequests.length
            )
          "
          :label="$t('放行附件')"
          prop="quotaStrategyFileSaveRequests"
        >
          <div class="div-auth" @click="handleUploadDialog">
            {{ getFormFileText }}
          </div>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import magnifierInput from '@/components/magnifierInput'
import { pageConfig, statusOptions } from './../../config'
import utils from '@/utils/utils.js'
import { throttle } from 'lodash'
import * as util from '@mtech-common/utils'
export default {
  components: {
    magnifierInput
  },
  props: {
    pageType: {
      // 判断当前页面的类型，add;edit;detail
      type: String,
      require: true,
      default: ''
    },
    formConfig: {
      type: Object,
      require: true,
      default: () => {
        return {
          companyId: '', // 组织
          type: '', // 放行供应商类型
          supplierId: '', // 供应商编码
          supplierCode: '', // 供应商编码
          supplierName: '', // 供应商名称
          dimension: '', // 放行维度
          categoryId: '', // 品类
          categoryCode: '', // 品类
          categoryName: '', // 品类名称
          startDate: '', // 开始月份
          endDate: '', // 结束月份
          code: '', // 放行配额单号
          status: 0, // 单据状态
          createUserName: '', // 创建人
          createTime: '', // 创建时间
          remark: '', // 备注
          file: '' // 文件
        }
      }
    }
  },
  data() {
    return {
      companyList: [], // 组织下拉列表的配置信息
      // 品类放大镜弹窗配置
      // categoryDialogCofig: {
      //   pageConfig: pageConfig(
      //     '/masterDataManagement/tenant/permission/queryCategories',
      //     'category'
      //   ),
      //   text: 'categoryName',
      //   value: 'categoryCode'
      // },
      // 供应商放大镜弹窗配置
      // supplierDialogCofig: {
      //   pageConfig: pageConfig('/masterDataManagement/tenant/supplier/pagedQueryByOrgCode', 'supplier'),
      //   text: 'supplierCode',
      //   value: 'supplierCode'
      // },
      statusOptions,
      addForm: {
        companyId: '', // 组织
        companyCode: '', // 组织编码
        companyName: '', // 组织名称
        type: '', // 放行供应商类型
        supplierId: '', // 供应商编码
        supplierCode: '', // 供应商编码
        supplierName: '', // 供应商名称
        dimension: '', // 放行维度
        categoryId: '', // 品类
        categoryCode: '', // 品类
        categoryName: '', // 品类名称
        startDate: '', // 开始月份
        endDate: '', // 结束月份
        code: '', // 放行配额单号
        status: 0, // 单据状态
        createUserName: '', // 创建人
        createTime: '', // 创建时间
        remark: '', // 备注
        quotaStrategyFileSaveRequests: [] // 上传附件内容
      },
      rules: {
        companyId: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        type: [{ required: true, message: this.$t('请选择放行供应商类型'), trigger: 'blur' }],
        supplierCode: [
          {
            required: true,
            message: this.$t('请选择供应商编码'),
            trigger: 'blur'
          }
        ],
        dimension: [
          {
            required: true,
            message: this.$t('请选择放行维度'),
            trigger: 'blur'
          }
        ],
        categoryId: [
          {
            required: true,
            message: this.$t('请选择品类'),
            trigger: 'blur'
          }
        ],
        startDate: [
          {
            required: true,
            message: this.$t('请选择开始月份'),
            trigger: 'blur'
          }
        ],
        endDate: [{ required: true, message: this.$t('请选择结束月份'), trigger: 'blur' }],
        quotaStrategyFileSaveRequests: [
          { required: true, message: this.$t('请上传附件'), trigger: 'blur' }
        ]
      },
      isAllowChang: false, // 解决页面初始化时触犯的change事件
      quotaStrategyFileSaveRequests: []
    }
  },
  created() {
    // 组织下拉列表
    this.getCompanyList()
    // 请求组织列表的接口并赋值给templateOrgOptions
  },
  methods: {
    //上传附件弹框
    handleUploadDialog() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "components/Upload/uploaderDialog" */ 'COMPONENTS/Upload/uploaderDialog.vue'
          ),
        data: {
          fileData: util.utils.cloneDeep(this.addForm.quotaStrategyFileSaveRequests),
          isView: this.pageType === 'detail', // 是否为预览
          required: false, // 是否必须
          title: this.$t('附件')
          // isSingleFile: true
        },
        success: (res) => {
          // 判断是否有缓存的文件数组 调文件删除接口后清空缓存的文件数组
          const idList = []
          for (let i = 0; i < this.quotaStrategyFileSaveRequests.length; i++) {
            const file = this.quotaStrategyFileSaveRequests[i]
            if (res.every((j) => j.originId !== file.originId)) {
              idList.push(file.originId)
            }
          }
          if (
            this.quotaStrategyFileSaveRequests &&
            this.quotaStrategyFileSaveRequests.length &&
            idList.length
          ) {
            const params = {
              // id: this.quotaStrategyFileSaveRequests[0]['originId']
              idList
            }
            this.$API.constraintExclude.getConstraintExcludefileDelete(params).then((resp) => {
              const { code } = resp
              if (code === 200) {
                let fileList = JSON.parse(JSON.stringify(res))
                this.quotaStrategyFileSaveRequests = []
                this.addForm.quotaStrategyFileSaveRequests = fileList
                this.$refs.ruleForm.validateField('quotaStrategyFileSaveRequests')
              }
            })
          } else {
            let fileList = JSON.parse(JSON.stringify(res))
            this.addForm.quotaStrategyFileSaveRequests = fileList
            this.$refs.ruleForm.validateField('quotaStrategyFileSaveRequests')
          }
        }
      })
    },
    setFormEmpty(fieldsarr) {
      for (let i = 0; i < fieldsarr.length; i++) {
        this.$set(this.addForm, fieldsarr[i], '')
      }
      this.$emit('clearTable')
    },
    companyChange(e) {
      const { itemData } = e
      this.addForm.companyId = itemData.id // 供应商编码
      this.addForm.companyCode = itemData.orgCode // 供应商编码
      this.addForm.companyName = itemData.orgName // 供应商名称
      // 若重新选择，需自动清空后面字段内容（放行供应商类型、供应商、放行维度、品类、物料、开始月份、结束月份）
      if (this.isAllowChang) {
        const fieldsarr = [
          'type',
          'supplierId',
          'supplierCode',
          'supplierName',
          'dimension',
          'categoryId',
          'categoryCode',
          'categoryName',
          'startDate',
          'endDate'
        ]
        this.setFormEmpty(fieldsarr)
      }
    },
    typeChange() {
      // 若重新选择，需自动清空后面字段内容（供应商、放行维度、品类、物料、开始月份、结束月份）
      if (this.isAllowChang) {
        const fieldsarr = [
          'supplierId',
          'supplierCode',
          'supplierName',
          'dimension',
          'categoryId',
          'categoryCode',
          'categoryName',
          'startDate',
          'endDate'
        ]
        this.setFormEmpty(fieldsarr)
      }
    },
    dimensionTypeChange(e) {
      const { itemData } = e
      let flag = false
      if (itemData && itemData.value === 'BFFX' && this.pageType) {
        flag = true
        this.$emit('getItemList')
      }
      this.$emit('handleChange', {
        flag,
        categoryCode: this.addForm.categoryCode,
        companyId: this.addForm.companyId
      })
      // 若重新选择，需自动清空后面字段内容（品类、物料、开始月份、结束月份）
      if (this.isAllowChang) {
        const fieldsarr = ['categoryId', 'categoryCode', 'categoryName', 'startDate', 'endDate']
        this.setFormEmpty(fieldsarr)
      }
    },
    supplierCodeChange(e) {
      this.addForm.supplierId = e.id // 供应商编码
      this.addForm.supplierCode = e.supplierCode // 供应商编码
      this.addForm.supplierName = e.supplierName // 供应商名称
      // 若重新选择，需自动清空后面字段内容（放行维度、品类、物料、开始月份、结束月份）
      if (this.isAllowChang) {
        const fieldsarr = [
          'dimension',
          'categoryId',
          'categoryCode',
          'categoryName',
          'startDate',
          'endDate'
        ]
        this.setFormEmpty(fieldsarr)
      }
      this.$refs.ruleForm.validateField('supplierCode')
    },
    categoryCodeChange(e) {
      this.addForm.categoryId = e.id // 供应商编码
      this.addForm.categoryCode = e.categoryCode // 供应商名称
      this.addForm.categoryName = e.categoryName // 供应商名称
      // 抛出品类Id给物料接口
      let flag = false
      if (this.addForm.dimension === 'BFFX') {
        flag = true
      }
      this.$emit('handleChange', {
        flag,
        categoryCode: this.addForm.categoryCode,
        companyId: this.addForm.companyId
      })
      // 若重新选择，需自动清空后面字段内容（物料、开始月份、结束月份）
      if (this.isAllowChang) {
        const fieldsarr = ['startDate', 'endDate']
        this.setFormEmpty(fieldsarr)
      }
      this.$refs.ruleForm.validateField('categoryId')
    },
    startDateChange() {
      if (this.isAllowChang) {
        this.addForm.endDate = ''
      }
    },
    getCompanyList() {
      this.$API.constraintExclude.getPermissionCompanyList({}).then((res) => {
        this.companyList = res.data
        if (!this.addForm.companyId && this.companyList && this.companyList.length === 1) {
          this.addForm.companyId = this.companyList[0]['id']
          this.addForm.companyCode = this.companyList[0]['orgCode'] // 供应商编码
          this.addForm.companyName = this.companyList[0]['orgName'] // 供应商名称
        }
      })
    },
    save: throttle(function () {
      if (this.addForm.startDate && new Date(this.addForm.startDate) <= new Date()) {
        this.$toast({
          content: this.$t('开始月份必须大于当前月份'),
          type: 'warning'
        })
        return false
      }
      if (
        this.addForm.endDate &&
        new Date(this.addForm.endDate) < new Date(this.addForm.startDate)
      ) {
        this.$toast({
          content: this.$t('结束月份必须大于等于开始月份'),
          type: 'warning'
        })
        return false
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const params = {
            ...this.addForm,
            startDate: utils.formatTime(new Date(this.addForm.startDate), 'YYYY-mm-dd'),
            endDate: utils.formatTime(new Date(this.addForm.endDate), 'YYYY-mm-dd')
          }
          this.$emit('save', params)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }, 1000),
    submit: throttle(function () {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const params = {
            ...this.addForm,
            startDate: utils.formatTime(new Date(this.addForm.startDate), 'YYYY-mm-dd'),
            endDate: utils.formatTime(new Date(this.addForm.endDate), 'YYYY-mm-dd')
          }
          this.$emit('submit', params)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }, 1000),
    getTitle() {
      let title = this.$t('放行配额')
      // 判断当前页面的类型，Add;Edit;Detail
      switch (this.pageType) {
        case 'edit':
          return this.$t('编辑') + title
        case 'detail':
          return this.$t('查看') + title
        default:
          return this.$t('新增') + title
      }
    },
    backTo() {
      this.$bus.$emit('refreshQuotaConstraintExcludeList')
      this.$router.push({
        path: '/sourcing/quota-strategy/constraint-exclude'
      })
    }
  },
  computed: {
    getFormFileText() {
      let _list = this?.addForm?.quotaStrategyFileSaveRequests ?? []
      if (_list.length) {
        let _name = []
        _list.forEach((e) => {
          _name.push(e.fileName)
        })
        return _name.join(',')
      } else {
        return this.$t('上传附件')
      }
    },
    supplierDialogCofig() {
      const params = {}
      let defaultRules = []
      if (this.addForm.companyCode) {
        defaultRules = [
          // { label: this.$t('状态'), field: 'statusId', type: 'string', operator: 'equal', value: '10' },
          {
            label: this.$t('归属公司编码'),
            field: 'organizationCode',
            type: 'string',
            operator: 'contains',
            value: this.addForm.companyCode
          }
        ]
      }
      return {
        pageConfig: pageConfig(
          '/masterDataManagement/tenant/supplier/pagedQueryByOrgCode',
          'supplier',
          params,
          defaultRules
        ),
        text: 'supplierCode',
        value: 'supplierCode'
      }
    },
    categoryDialogCofig() {
      const params = {}
      if (this.addForm.orgId) {
        params.organizationId = this.addForm.orgId
      }
      return {
        pageConfig: pageConfig(
          '/masterDataManagement/tenant/permission/queryCategories',
          'category',
          params
        ),
        text: 'categoryName',
        value: 'categoryCode'
      }
    }
  },
  watch: {
    /* 监听传进来得内容 */
    formConfig: {
      handler(newVal) {
        this.isAllowChang = false
        this.addForm = {
          ...this.addForm,
          ...newVal
        }
        if (this.addForm.startDate && this.addForm.endDate) {
          this.addForm.startDate = utils.formatTime(new Date(this.addForm.startDate), 'YYYY-mm')
          this.addForm.endDate = utils.formatTime(new Date(this.addForm.endDate), 'YYYY-mm')
        }
        this.quotaStrategyFileSaveRequests = this.addForm.quotaStrategyFileSaveRequests
        // 加定时器是处理 初始赋值时会触发mt表单组件的change事件导致表单置空逻辑触发回显异常
        setTimeout(() => {
          this.isAllowChang = true
        }, 2000)
      },
      immediate: true,
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
.header-box {
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 20px;
  .header-title {
    color: #292929;
    font-family: PingFangSC;
    font-size: 20px;
    font-weight: 600;
  }
}
.main-form {
  /deep/ .mt-form-item {
    width: calc(25% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-top: 5px;
    margin-right: 20px;

    .full-width {
      width: calc(100% - 20px) !important;
    }
    .e-ddt .e-ddt-icon::before {
      content: '\e36a';
      font-size: 16px;
    }
    .mt-form-item-topLabel {
      .div-auth {
        background: #e3e1e1;
        height: 35px;
        display: flex;
        align-items: center;
        padding: 0 10px;
        color: #0f0f0f;
        font-size: 12px;
        cursor: pointer;
        border-radius: 5px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 200px;
        display: inline-block;
        line-height: 35px;
      }
    }
  }
  .check-area {
    transform: translateY(10px);
  }
}
</style>
