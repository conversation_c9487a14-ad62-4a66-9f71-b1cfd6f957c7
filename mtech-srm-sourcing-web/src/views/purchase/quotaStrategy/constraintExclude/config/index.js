import { i18n } from '@/main.js'
import utils from '@/utils/utils'
const toolbar = [
  // { id: "Add", icon: "icon_solid_Createorder", title: i18n.t("新增") },
  // { id: "Stop", icon: "icon_solid_Cancel", title: i18n.t("取消") },
  // { id: "Submit", icon: "icon_solid_Submit", title: i18n.t("提交") },
  // { id: "Import", icon: "icon_solid_Createorder", title: i18n.t("导入") },
]
const materialColumn = [
  // {
  //   type: 'checkbox',
  //   width: '60'
  // },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    cssClass: 'field-content',
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  }
  // {
  //   field: 'categoryCode',
  //   headerText: i18n.t('品类编码'),
  //   cssClass: 'field-content'
  // },
  // {
  //   field: 'categoryName',
  //   headerText: i18n.t('品类名称')
  // }
]
const categoryColumn = [
  // {
  //   type: 'checkbox',
  //   width: '60'
  // },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    cssClass: 'field-content'
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]
const supplierColumn = [
  // {
  //   type: 'checkbox',
  //   width: '60'
  // },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    cssClass: 'field-content'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  }
]
export const pageConfig = (url, columnType, params, defaultRules) => {
  let columnData = []
  let gridId = {
    material: 'f9c6d3d0-29f1-401b-898c-555e50aff03d',
    category: '4de29202-cab3-46b5-a39b-abd44c39d285',
    supplier: '643ab2b5-76a2-4007-8263-9c7c2cc2a9cf'
  }
  if (columnType === 'material') {
    columnData = materialColumn
  } else if (columnType === 'category') {
    columnData = categoryColumn
  } else {
    columnData = supplierColumn
  }
  return [
    {
      toolbar,
      useToolTemplate: false,
      gridId: gridId[columnType],
      grid: {
        allowFiltering: true,
        columnData,
        allowSorting: false,
        allowSelection: true,
        selectionSettings: {
          type: 'Multiple',
          mode: 'Row'
        },
        asyncConfig: { url, params, defaultRules }
      }
    }
  ]
}

export const materialColumns = [
  {
    title: i18n.t('物料编码'),
    field: 'itemCode',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('物料名称'),
    field: 'itemName',
    minWidth: 150,
    align: 'left'
  }
]

export const statusOptions = utils.getQuotaDict('QUOTA_STRATEGY_STATUS') || []

export const supplierOptions = utils.getQuotaDict('QUOTA_STRATEGY_XZPEYXJ_TYPE') || []

export const vxeColumns = [
  {
    type: 'checkbox',
    width: '50'
  },
  {
    title: i18n.t('公司'),
    field: 'companyName',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('放行配额单号'),
    field: 'code',
    minWidth: 200,
    align: 'left',
    slots: { default: 'quatoCode' }
  },
  {
    title: i18n.t('单据状态'),
    field: 'status',
    align: 'left',
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  },
  {
    title: i18n.t('放行供应商类型'),
    field: 'type',
    minWidth: 150,
    align: 'left',
    formatter: ({ cellValue }) => {
      let item = supplierOptions.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  },
  {
    title: i18n.t('供应商编码'),
    field: 'supplierCode',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('供应商名称'),
    field: 'supplierName',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('放行维度'),
    field: 'dimension',
    minWidth: 150,
    align: 'left',
    formatter: ({ cellValue }) => {
      const options = utils.getQuotaDict('QUOTA_STRATEGY_FX_WD') || []
      let item = options.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  },
  {
    title: i18n.t('品类'),
    field: 'categoryName',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('物料'),
    field: 'detailList',
    minWidth: 200,
    align: 'left',
    slots: { default: 'detailList' }
  },
  {
    title: i18n.t('放行附件'),
    field: 'file',
    align: 'left',
    slots: { default: 'file' }
  },
  {
    title: i18n.t('开始月份'),
    field: 'startDate',
    minWidth: 150,
    align: 'left',
    formatter: ({ cellValue }) => {
      let date = ''
      if (cellValue && new Date(cellValue)) {
        date = utils.formatTime(new Date(cellValue), 'YYYY-mm')
      }
      return date
    }
  },
  {
    title: i18n.t('结束月份'),
    field: 'endDate',
    minWidth: 150,
    align: 'left',
    formatter: ({ cellValue }) => {
      let date = ''
      if (cellValue && new Date(cellValue)) {
        date = utils.formatTime(new Date(cellValue), 'YYYY-mm')
      }
      return date
    }
  },
  { title: i18n.t('创建人'), field: 'createUserName', align: 'left' },
  { title: i18n.t('创建时间'), field: 'createTime', align: 'left' },
  {
    title: i18n.t('操作'),
    field: 'operation',
    fixed: 'right',
    slots: { default: 'operationDefault' }
  }
]
