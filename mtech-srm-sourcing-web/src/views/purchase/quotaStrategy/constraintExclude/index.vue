<template>
  <div class="quato-customization">
    <collapse-search :is-grid-display="true" @reset="reset" @search="search">
      <mt-form ref="ruleForm" :model="queryForm">
        <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
          <mt-select
            v-model="queryForm.companyCode"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="companyList"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            :placeholder="$t('请选择公司')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="code" :label="$t('放行配额单号')" label-style="top">
          <mt-input v-model="queryForm.code" :placeholder="$t('请输入放行配额单号')" />
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('单据状态')" label-style="top">
          <mt-select
            v-model="queryForm.status"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="statusOptions"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('单据状态')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="type" :label="$t('放行供应商类型')" label-style="top">
          <mt-select
            v-model="queryForm.type"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="supplierOptions"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('放行供应商类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="supplierId" :label="$t('供应商')" label-style="top">
          <magnifier-input
            ref="supplierCode"
            :config="supplierDialogCofig"
            @change="supplierCodeChange($event)"
          ></magnifier-input>
        </mt-form-item>
        <mt-form-item prop="categoryId" :label="$t('品类')" label-style="top">
          <magnifier-input
            ref="categoryId"
            :config="categoryDialogCofig"
            @change="categoryCodeChange($event)"
          ></magnifier-input>
        </mt-form-item>
        <mt-form-item prop="itemId" :label="$t('物料编码')" label-style="top">
          <magnifier-input
            ref="itemCode"
            :config="materialDialogCofig"
            @change="materialCodeChange($event)"
          ></magnifier-input>
        </mt-form-item>
        <mt-form-item prop="startDateStart" :label="$t('开始月份起')" label-style="top">
          <mt-date-picker
            v-model="queryForm.startDateStart"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :placeholder="$t('选择开始月份起')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="startDateEnd" :label="$t('开始月份止')" label-style="top">
          <mt-date-picker
            v-model="queryForm.startDateEnd"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :placeholder="$t('选择开始月份止')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="endDateStart" :label="$t('结束月份起')" label-style="top">
          <mt-date-picker
            v-model="queryForm.endDateStart"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :placeholder="$t('选择结束月份起')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="endDateEnd" :label="$t('结束月份止')" label-style="top">
          <mt-date-picker
            v-model="queryForm.endDateEnd"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :placeholder="$t('选择结束月份止')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
          <mt-input v-model="queryForm.createUserName" :placeholder="$t('请输入创建人')" />
        </mt-form-item>
        <mt-form-item prop="createTimeStart" :label="$t('创建时间起')" label-style="top">
          <mt-date-time-picker
            v-model="queryForm.createTimeStart"
            :open-on-focus="true"
            time-stamp
            format="yyyy-MM-dd HH:mm:ss"
            :placeholder="$t('选择创建时间起')"
          ></mt-date-time-picker>
        </mt-form-item>
        <mt-form-item prop="createTimeEnd" :label="$t('创建时间止')" label-style="top">
          <mt-date-time-picker
            v-model="queryForm.createTimeEnd"
            :open-on-focus="true"
            time-stamp
            format="yyyy-MM-dd HH:mm:ss"
            :placeholder="$t('选择创建时间止')"
          ></mt-date-time-picker>
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!----  表格  --------->
    <sc-table
      ref="xTable"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="vxeColumns"
      :table-data="dataSource"
      @refresh="search"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
          >{{ item.name }}</vxe-button
        >
      </template>
      <template #quatoCode="{ row }">
        <div class="editable-row-operations">
          <span>
            <a @click="handleOptions(row)">{{ row.code }}</a>
          </span>
        </div>
      </template>
      <template #detailList="{ row }">
        <div class="editable-row-operations">
          <span>
            <a :disabled="row.dimension === 'ZTFX'" @click="() => handleViewDetail(row)"> 查看 </a>
          </span>
        </div>
      </template>
      <template #file="{ row }">
        <div class="editable-row-operations">
          <span>
            <a @click="() => handleDownload(row)">
              <mt-icon name="icon_solid_export" />
            </a>
          </span>
        </div>
      </template>
      <template #operationDefault="{ row }">
        <div class="editable-row-operations">
          <span>
            <a
              :disabled="(row.status !== 0 && row.status !== 2) || row.createUserId !== userId"
              style="margin-right: 4px"
              @click="() => handleDetail(row, 'edit')"
              >{{ $t('编辑') }}</a
            >
            <a
              :disabled="(row.status !== 0 && row.status !== 2) || row.createUserId !== userId"
              @click="() => handleDelete(row)"
              >{{ $t('删除') }}</a
            >
          </span>
        </div>
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import moment from 'moment'
import utils from '@/utils/utils'
import collapseSearch from '@/components/collapseSearch'
import magnifierInput from '@/components/magnifierInput'
import { pageConfig, vxeColumns, materialColumns, statusOptions, supplierOptions } from './config'
import ScTable from '@/components/ScTable/src/index'
import * as util from '@mtech-common/utils'
export default {
  components: {
    collapseSearch,
    magnifierInput,
    ScTable
  },
  data() {
    return {
      loading: false,
      toolbar: [
        {
          code: 'Add',
          name: this.$t('新增'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Import',
          name: this.$t('导入'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Delete',
          name: this.$t('删除'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Export',
          name: this.$t('导出'),
          icon: 'el-icon-delete',
          status: 'info'
        }
      ],
      queryForm: {
        companyCode: '', // 公司
        code: '', // 配额单号
        status: '', // 状态
        type: '', // 放行供应商类型
        supplierId: '',
        categoryId: '', // 品类
        itemId: '', // 物料id
        startDateStart: '', // 开始月份起
        startDateEnd: '', // 开始月份止
        endDateStart: '', // 结束月份起
        endDateEnd: '', // 结束月份止
        createUserName: '', // 创建人
        createTimeStart: '', // 创建时间起
        createTimeEnd: '' // 创建时间止
      },
      pageSettings: {
        pageCount: 5,
        pageSize: 20, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [10, 20, 50, 100]
      },
      statusOptions,
      supplierOptions, // 供应商放行类型
      vxeColumns,
      dataSource: [],
      // fieldsarr: {
      //   dataSource: [], // 组织树下拉数组
      //   value: 'id',
      //   text: 'companyCode',
      //   child: 'childrenList'
      // },
      companyList: [],
      // 物料放大镜弹窗配置
      materialDialogCofig: {
        pageConfig: pageConfig(
          `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
          'material'
        ),
        text: 'itemCode',
        value: 'itemCode'
      },
      // 品类放大镜弹窗配置
      categoryDialogCofig: {
        pageConfig: pageConfig(
          '/masterDataManagement/tenant/permission/queryCategories',
          'category'
        ),
        text: 'categoryName',
        value: 'categoryCode'
      },
      // 供应商放大镜弹窗配置
      supplierDialogCofig: {
        pageConfig: pageConfig('/masterDataManagement/tenant/supplier/paged-query', 'supplier'),
        text: 'supplierName',
        value: 'supplierCode'
      },
      userId: ''
    }
  },
  created() {
    this.getCompanyList()
    this.$bus.$on('refreshQuotaConstraintExcludeList', () => {
      this.search()
    })
    this.search()
    const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
    this.userId = userInfo?.uid
  },
  methods: {
    setFormEmpty(fieldsarr, row) {
      for (let i = 0; i < fieldsarr.length; i++) {
        this.$set(row, fieldsarr[i], '')
      }
    },
    getCompanyList() {
      this.$API.constraintExclude.getPermissionCompanyList({}).then((res) => {
        this.companyList = res.data
      })
    },
    handleOptions(row) {
      const isEdit = (row.status === 0 || row.status === 2) && row.createUserId === this.userId
      this.handleDetail(row, isEdit ? 'edit' : 'detail')
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRows = $grid.getCheckboxRecords() // 表格勾选行的内容
      if (code === 'Delete' && selectedRows.length <= 0) {
        this.$toast({
          content: this.$t('请勾选需要操作的数据'),
          type: 'warning'
        })
        return
      }
      if (code === 'Add') {
        // 新增
        this.handleDetail()
      } else if (code === 'Import') {
        // 导入
        this.handleImport()
      } else if (code === 'Delete') {
        // 删除
        this.handleDelete(selectedRows)
      } else if (code === 'Export') {
        // 导出
        this.handleExport()
      }
    },
    // 导入
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('上传/导入'),
          importApi: this.$API.constraintExclude.importConstraintExclude,
          downloadTemplateApi: this.$API.constraintExclude.exportConstraintExcludeTemplate
        },
        success: () => {
          // 导入之后刷新列表
          this.search()
        }
      })
    },
    // 删除
    handleDelete(data) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          // 校验数据为拟定状态
          let params = null
          if (!Array.isArray(data)) {
            // 单行删除
            if (data.status !== 0 && data.status !== 2) {
              this.$toast({
                content: this.$t('所选数据存在非拟定或非已驳回状态数据'),
                type: 'warning'
              })
              return
            }
            params = [data.id]
          } else {
            // 勾选删除
            if (this.selectedRows.some((i) => i.status !== 0 && i.status !== 2)) {
              this.$toast({
                content: this.$t('所选数据存在非拟定或非已驳回状态数据'),
                type: 'warning'
              })
              return
            }
            params = this.selectedRows.map((i) => i.id)
          }
          this.$API.constraintExclude
            .batchDeleteConstraintExclude({ idList: params })
            .then((res) => {
              const { code } = res
              if (code === 200) {
                this.$toast({
                  content: this.$t('删除成功'),
                  type: 'success'
                })
                this.search()
              }
            })
        }
      })
    },
    // 导出
    handleExport() {
      const params = {
        ...this.queryForm,
        page: {
          current: this.pageSettings.current,
          size: this.pageSettings.pageSize
        }
      }
      if (params.startDateStart) {
        params.startDateStart = utils.formatTime(new Date(params.startDateStart), 'YYYY-mm-01')
      }
      if (params.startDateEnd) {
        let endDate = moment(utils.formatTime(new Date(params.startDateEnd), 'YYYY-mm-01'))
          .add('month', 1)
          .add('days', -1)
        params.startDateEnd = moment(endDate).format('YYYY-MM-DD')
      }
      if (params.endDateStart) {
        params.endDateStart = utils.formatTime(new Date(params.endDateStart), 'YYYY-mm-01')
      }
      if (params.endDateEnd) {
        let endDate = moment(utils.formatTime(new Date(params.endDateEnd), 'YYYY-mm-01'))
          .add('month', 1)
          .add('days', -1)
        params.endDateEnd = moment(endDate).format('YYYY-MM-DD')
      }
      if (params.createTimeStart) {
        params.createTimeStart = utils.formatTime(
          new Date(params.createTimeStart),
          'YYYY-mm-dd HH:MM:SS'
        )
      }
      if (params.createTimeEnd) {
        params.createTimeEnd = utils.formatTime(
          new Date(params.createTimeEnd),
          'YYYY-mm-dd HH:MM:SS'
        )
      }
      this.$API.constraintExclude.exportConstraintExclude(params).then((res) => {
        this.$toast({
          type: 'success',
          content: this.$t('导出成功，请稍后！')
        })
        let fileName = utils.getHeadersFileName(res)
        utils.download({ fileName: fileName, blob: res.data })
      })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageSettings.current = currentPage
      this.search('pageSettings')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.search()
    },
    handleDetail(row, type) {
      const query = {}
      if (row && type) {
        // query = {
        //   id: row.id,
        //   type
        // }
        query.id = row.id
        query.type = type
      }
      query.timeStamp = new Date().getTime()
      this.$router.push({
        path: `/sourcing/quota-strategy/constraint-exclude-detail`,
        query
      })
    },
    handleViewDetail(row) {
      this.$dialog({
        modal: () => import('./components/detailDialog.vue'),
        data: {
          title: this.$t('物料'),
          id: row.id,
          columns: materialColumns
        }
      })
    },
    handleDownload(row) {
      this.$API.constraintExclude.getConstraintExcludefileQuery({ id: row.id }).then((res) => {
        const { code, data } = res
        if (code === 200) {
          if (data && data.length) {
            const fileData = data.map((i) => {
              i.id = i.fileId
              return i
            })
            this.$dialog({
              modal: () =>
                import(
                  /* webpackChunkName: "components/Upload/uploaderDialog" */ 'COMPONENTS/Upload/uploaderDialog.vue'
                ),
              data: {
                fileData: util.utils.cloneDeep(fileData || []),
                isView: true, // 是否为预览
                required: false, // 是否必须
                title: this.$t('附件')
                // isSingleFile: true
              }
            })
          } else {
            this.$toast({
              content: this.$t('暂无附件'),
              type: 'warning'
            })
            return false
          }
        }
      })
    },
    reset() {
      console.log('重置')
      this.queryForm = {
        companyCode: '', // 公司
        categoryId: '', // 品类
        status: '', // 状态
        itemId: '', // 物料id
        supplierId: ''
      }
      this.$refs.itemCode.handleClear()
      this.$refs.supplierCode.handleClear()
      this.$refs.categoryId.handleClear()
      this.search()
    },
    search(pageSettings) {
      console.log('search', this.queryForm)
      const params = {
        ...this.queryForm,
        page: {
          current: pageSettings ? this.pageSettings.current : 1,
          size: this.pageSettings.pageSize
        }
      }
      if (params.startDateStart) {
        params.startDateStart = utils.formatTime(new Date(params.startDateStart), 'YYYY-mm-01')
      }
      if (params.startDateEnd) {
        let endDate = moment(utils.formatTime(new Date(params.startDateEnd), 'YYYY-mm-01'))
          .add('month', 1)
          .add('days', -1)
        params.startDateEnd = moment(endDate).format('YYYY-MM-DD')
      }
      if (params.endDateStart) {
        params.endDateStart = utils.formatTime(new Date(params.endDateStart), 'YYYY-mm-01')
      }
      if (params.endDateEnd) {
        let endDate = moment(utils.formatTime(new Date(params.endDateEnd), 'YYYY-mm-01'))
          .add('month', 1)
          .add('days', -1)
        params.endDateEnd = moment(endDate).format('YYYY-MM-DD')
      }
      if (params.createTimeStart) {
        params.createTimeStart = utils.formatTime(
          new Date(params.createTimeStart),
          'YYYY-mm-dd HH:MM:SS'
        )
      }
      if (params.createTimeEnd) {
        params.createTimeEnd = utils.formatTime(
          new Date(params.createTimeEnd),
          'YYYY-mm-dd HH:MM:SS'
        )
      }
      this.loading = true
      this.$API.constraintExclude
        .getConstraintExcludeList(params)
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            const total = res?.data?.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)
            this.dataSource = data.records
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    materialCodeChange(e) {
      // 物料编码变更
      this.queryForm.itemId = e.id
    },
    categoryCodeChange(e) {
      // 品类变更
      this.queryForm.categoryId = e.id
    },
    supplierCodeChange(e) {
      this.queryForm.supplierId = e.id
    }
  }
}
</script>

<style lang="scss" scoped>
.quato-customization {
  // margin-top: 24px;
  background: #fff;
  padding: 12px;
  border-radius: 8px;
  height: 100%;
  .button-group {
    display: flex;
    padding: 0 0 8px;
  }
}
.components-table-demo-nested {
  // ::v-deep.ant-table-body {
  //   overflow-x: auto !important;
  // }
  ::v-deep.red-color {
    color: red;
  }
}
</style>
