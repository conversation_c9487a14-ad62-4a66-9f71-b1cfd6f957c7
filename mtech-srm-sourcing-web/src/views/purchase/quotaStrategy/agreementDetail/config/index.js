import { i18n } from '@/main.js'
const toolbar = [
  // { id: "Add", icon: "icon_solid_Createorder", title: i18n.t("新增") },
  // { id: "Stop", icon: "icon_solid_Cancel", title: i18n.t("取消") },
  // { id: "Submit", icon: "icon_solid_Submit", title: i18n.t("提交") },
  // { id: "Import", icon: "icon_solid_Createorder", title: i18n.t("导入") },
]
const materialColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    cssClass: 'field-content',
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  }
]
const categoryColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    cssClass: 'field-content'
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]
const supplierColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    cssClass: 'field-content'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  }
]
export const pageConfig = (url, columnType) => {
  let columnData = []
  if (columnType === 'material') {
    columnData = materialColumn
  } else if (columnType === 'category') {
    columnData = categoryColumn
  } else {
    columnData = supplierColumn
  }
  return [
    {
      toolbar,
      useToolTemplate: false,
      grid: {
        allowFiltering: true,
        columnData,
        allowSorting: false,
        allowSelection: true,
        selectionSettings: {
          type: 'Multiple',
          mode: 'Row'
        },
        asyncConfig: { url }
      }
    }
  ]
}

export const vxeColumns = [
  {
    title: i18n.t('工厂'),
    field: 'siteCode',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('工厂名称'),
    field: 'siteName',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('物料编码'),
    field: 'itemCode',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('物料名称'),
    field: 'itemName',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('物料状态'),
    field: 'itemStatus',
    align: 'left',
    formatter: ({ cellValue }) => {
      const options = [
        { text: i18n.t('拟定'), code: '0' },
        { text: i18n.t('有效'), code: '1' },
        { text: i18n.t('失效'), code: '2' }
      ]
      let item = options.find((item) => item.code === cellValue)
      return item ? item.text : ''
    }
  },

  {
    title: i18n.t('供应商编码'),
    field: 'supplierCode',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('供应商名称'),
    field: 'supplierName',
    minWidth: 150,
    align: 'left'
  },
  {
    title: '协议配额(%)',
    field: 'quotaPercent',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('开始日期'),
    field: 'startTime',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('结束日期'),
    field: 'endTime',
    minWidth: 150,
    align: 'left'
  },

  { title: i18n.t('协议配额单号'), field: 'agreementCode', align: 'left' },
  { title: i18n.t('创建人'), field: 'createUserName', align: 'left' },
  { title: i18n.t('创建时间'), field: 'createTime', align: 'left' }
]
