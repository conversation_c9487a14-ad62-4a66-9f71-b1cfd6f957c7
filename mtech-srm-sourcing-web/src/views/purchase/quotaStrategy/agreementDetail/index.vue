<template>
  <div class="quato-customization">
    <collapse-search :is-grid-display="true" @reset="reset" @search="handleSearch">
      <mt-form ref="ruleForm" :model="queryForm">
        <mt-form-item prop="siteCode" :label="$t('工厂代码')" label-style="top">
          <mt-select
            v-model="queryForm.siteCode"
            float-label-type="Never"
            :allow-filtering="true"
            :show-clear-button="true"
            :data-source="factoryList"
            :fields="{ text: 'orgCode', value: 'orgCode' }"
            :placeholder="$t('请选择工厂代码')"
            @change="handleFactoryChange($event)"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
          <magnifier-input
            ref="itemCode"
            :config="materialDialogCofig"
            @change="materialCodeChange($event)"
          ></magnifier-input>
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商')" label-style="top">
          <magnifier-input
            ref="supplierCode"
            :config="supplierDialogCofig"
            @change="supplierCodeChange($event)"
          ></magnifier-input>
        </mt-form-item>
        <mt-form-item prop="code" :label="$t('协议配额单号')" label-style="top">
          <mt-input v-model="queryForm.code" :placeholder="$t('请输入协议配额单号')"></mt-input>
        </mt-form-item>
        <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
          <mt-input v-model="queryForm.createUserName" :placeholder="$t('请输入创建人')" />
        </mt-form-item>
        <mt-form-item prop="startDateStart" :label="$t('开始月份起')" label-style="top">
          <mt-date-picker
            v-model="queryForm.startDateStart"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :placeholder="$t('选择开始月份起')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="startDateEnd" :label="$t('开始月份止')" label-style="top">
          <mt-date-picker
            v-model="queryForm.startDateEnd"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :placeholder="$t('选择开始月份止')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="endDateStart" :label="$t('结束月份起')" label-style="top">
          <mt-date-picker
            v-model="queryForm.endDateStart"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :placeholder="$t('选择结束月份起')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="endDateEnd" :label="$t('结束月份止')" label-style="top">
          <mt-date-picker
            v-model="queryForm.endDateEnd"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :placeholder="$t('选择结束月份止')"
          ></mt-date-picker>
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!----  表格  --------->
    <sc-table ref="xTable" :loading="loading" :columns="vxeColumns" :table-data="dataSource">
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import moment from 'moment'
import collapseSearch from '@/components/collapseSearch'
import { pageConfig, vxeColumns } from './config'
import ScTable from '@/components/ScTable/src/index'
import magnifierInput from '@/components/magnifierInput'
import { download, getHeadersFileName } from '@/utils/utils'
import utils from '@/utils/utils'

export default {
  components: {
    collapseSearch,
    ScTable,
    magnifierInput
  },
  data() {
    return {
      loading: false,
      toolbar: [
        {
          code: 'Export',
          name: this.$t('导出'),
          icon: 'el-icon-delete',
          status: 'info'
        }
      ],
      queryForm: {
        // siteId: '', // 工厂代码
        siteCode: '', // 工厂代码
        itemId: '', // 物料Id
        supplierId: '', //供应商Id
        code: '', //招标配额单号
        createUserName: '', //创建人
        startDateStart: '', // 开始月份起
        startDateEnd: '', // 开始月份止
        endDateStart: '', // 结束月份起
        endDateEnd: '', // 结束月份止
        currentPage: 1,
        pageSize: 10
      },
      pageSettings: {
        pageCount: 5,
        pageSize: 20, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [10, 20, 50, 100]
      },
      factoryList: [], // 工厂列表
      statusOptions: [
        { text: this.$t('拟定'), value: 0 },
        { text: this.$t('有效'), value: 1 },
        { text: this.$t('失效'), value: 2 }
      ],
      vxeColumns,
      dataSource: [],
      fieldsarr: {
        dataSource: [], // 组织树下拉数组
        value: 'id',
        text: 'orgId',
        child: 'childrenList'
      },
      // 物料放大镜弹窗配置
      materialDialogCofig: {
        pageConfig: pageConfig(
          `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
          'material'
        ),
        text: 'itemCode',
        value: 'itemCode'
      },
      // 供应商放大镜弹窗配置
      supplierDialogCofig: {
        pageConfig: pageConfig(
          '/masterDataManagement/tenant/supplier/pagedQueryByOrgCode',
          'supplier'
        ),
        text: 'supplierName',
        value: 'supplierCode'
      },
      cacheRow: {}
    }
  },
  created() {
    this.getFactoryList()
    this.search()
  },
  methods: {
    moment,
    startDateChange(date, row) {
      if (!date) {
        this.$set(row, 'endTime', '')
      }
    },
    getFormatTime(date, format) {
      if (date && new Date(date)) {
        return utils.formatTime(new Date(date), format)
      }
      return ''
    },
    getFactoryList() {
      this.$API.customization.getPermissionSiteList({}).then((res) => {
        this.factoryList = res.data
      })
    },
    handleClickToolBar(args) {
      const { code } = args
      if (code === 'Export') {
        this.handleExport()
      }
    },

    // 导出
    handleExport() {
      const params = {
        ...this.queryForm,
        page: {
          current: this.pageSettings.current,
          size: this.pageSettings.pageSize
        }
      }
      if (params.startDateStart) {
        params.startDateStart = utils.formatTime(new Date(params.startDateStart), 'YYYY-mm-01')
      }
      if (params.startDateEnd) {
        let endDate = moment(utils.formatTime(new Date(params.startDateEnd), 'YYYY-mm-01'))
          .add('month', 1)
          .add('days', -1)
        params.startDateEnd = moment(endDate).format('YYYY-MM-DD')
      }
      if (params.endDateStart) {
        params.endDateStart = utils.formatTime(new Date(params.endDateStart), 'YYYY-mm-01')
      }
      if (params.endDateEnd) {
        let endDate = moment(utils.formatTime(new Date(params.endDateEnd), 'YYYY-mm-01'))
          .add('month', 1)
          .add('days', -1)
        params.endDateEnd = moment(endDate).format('YYYY-MM-DD')
      }
      this.$API.agreementDetail.exportExcel({}).then((res) => {
        this.$toast({
          type: 'success',
          content: this.$t('导出成功，请稍后！')
        })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageSettings.current = currentPage
      this.search('pageSettings')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.search()
    },
    reset() {
      console.log('重置')
      this.queryForm = {
        // siteId: '', // 工厂代码
        siteCode: '', // 工厂代码
        itemId: '', // 物料编码
        supplierId: '', //供应商编码
        supplierName: '', //供应商名称
        code: '', //招标配额单号
        createUserName: '', //创建人
        createTime: '', //创建时间
        currentPage: 1,
        pageSize: 10
      }
      this.$refs.itemCode.handleClear()
      this.$refs.supplierCode.handleClear()
      this.search()
    },
    handleSearch() {
      this.search()
    },
    search(pageSettings) {
      console.log('search', this.queryForm)
      const params = {
        ...this.queryForm,
        page: {
          current: pageSettings ? this.pageSettings.current : 1,
          size: this.pageSettings.pageSize
        }
      }
      if (params.startDateStart) {
        params.startDateStart = utils.formatTime(new Date(params.startDateStart), 'YYYY-mm-01')
      }
      if (params.startDateEnd) {
        let endDate = moment(utils.formatTime(new Date(params.startDateEnd), 'YYYY-mm-01'))
          .add('month', 1)
          .add('days', -1)
        params.startDateEnd = moment(endDate).format('YYYY-MM-DD')
      }
      if (params.endDateStart) {
        params.endDateStart = utils.formatTime(new Date(params.endDateStart), 'YYYY-mm-01')
      }
      if (params.endDateEnd) {
        let endDate = moment(utils.formatTime(new Date(params.endDateEnd), 'YYYY-mm-01'))
          .add('month', 1)
          .add('days', -1)
        params.endDateEnd = moment(endDate).format('YYYY-MM-DD')
      }
      this.loading = true
      this.$API.agreementDetail
        .getCustomizationList(params)
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            const total = res?.data?.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)

            this.dataSource = data.records
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    setTableEmpty(fieldsarr, row) {
      for (let i = 0; i < fieldsarr.length; i++) {
        this.$set(row, fieldsarr[i], '')
      }
    },
    // 工厂改变
    handleFactoryChange(e) {
      // 表单内的下拉选择 使用的是mt组件api与vxe不同
      const { itemData } = e
      // this.queryForm.siteId = itemData.id
      this.queryForm.siteCode = itemData.orgCode
    },
    materialCodeChange(e) {
      // 表单内物料编码变更
      this.queryForm.itemId = e.id
    },
    supplierCodeChange(e) {
      // 表单内物料编码变更
      this.queryForm.supplierId = e.id
    }
  }
}
</script>

<style lang="scss" scoped>
.quato-customization {
  margin-top: 24px;
  background: #fff;
  padding: 12px;
  border-radius: 8px;
  height: 100%;
  .button-group {
    display: flex;
    padding: 0 0 8px;
  }
}
.components-table-demo-nested {
  // ::v-deep.ant-table-body {
  //   overflow-x: auto !important;
  // }
  ::v-deep.red-color {
    color: red;
  }
}
</style>
