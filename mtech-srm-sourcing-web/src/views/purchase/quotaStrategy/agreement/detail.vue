<template>
  <div class="supplier-box mt-flex">
    <div class="miam-container">
      <div class="header">
        <div class="title">{{ $t('协议配额维护') }}</div>
        <div class="operate-bar">
          <div v-if="type != 'detail'" class="op-item mt-flex" @click="save()">
            {{ $t('保存') }}
          </div>
          <div v-if="type != 'detail'" class="op-item mt-flex" @click="submit()">
            {{ $t('提交审批') }}
          </div>
          <div class="op-item mt-flex" @click="backToBusinessConfig">
            {{ $t('返回') }}
          </div>
        </div>
      </div>
      <!-- 顶部主要信息 -->
      <div class="mian-info">
        <div class="hr"></div>
        <mt-form ref="ruleForm" :model="formObject" :rules="formRules">
          <mt-form-item prop="companyId" :label="$t('公司名称：')" label-style="top">
            <mt-select
              :fields="{ text: 'orgName', value: 'id' }"
              v-model="formObject.companyId"
              float-label-type="Never"
              :allow-filtering="true"
              :data-source="buList"
              :placeholder="$t('请选择公司名称')"
              @change="changeBu"
              :disabled="type == 'detail' || formObject.id != ''"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="title" :label="$t('协议配额标题')" label-style="top">
            <mt-input
              width="100%"
              type="text"
              v-model="formObject.title"
              :placeholder="$t('请输入协议配额标题')"
              :disabled="type == 'detail'"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="agreementType" :label="$t('单据类型')" label-style="top">
            <mt-select
              :fields="{ text: 'label', value: 'status' }"
              v-model="formObject.agreementType"
              float-label-type="Never"
              :allow-filtering="true"
              :show-clear-button="true"
              :data-source="agreementTypeList"
              :placeholder="$t('请选择单据类型')"
              @change="changeAgreementType"
              :disabled="type == 'detail' || formObject.id != ''"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="status" :label="$t('单据状态')" label-style="top">
            <mt-select
              :disabled="true"
              :fields="{ text: 'label', value: 'status' }"
              v-model="formObject.status"
              float-label-type="Never"
              :allow-filtering="true"
              :data-source="statusList"
              :placeholder="$t('请选择单据状态')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="code" :label="$t('协议配额单号')" label-style="top">
            <mt-input
              width="100%"
              type="text"
              :disabled="true"
              v-model="formObject.code"
              :placeholder="$t('请输入协议配额单号')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
            <mt-input
              width="100%"
              type="text"
              :disabled="true"
              v-model="formObject.createUserName"
              :placeholder="$t('请输入创建人')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
            <mt-date-picker
              :disabled="true"
              v-model="formObject.createTime"
              format="yyyy-MM-dd hh:mm:ss"
              :placeholder="$t('请选择创建时间')"
            ></mt-date-picker>
          </mt-form-item>
          <mt-form-item :label="$t('附件')" label-style="top">
            <div class="div-auth" @click="handleUploadDialog">
              {{ getFormFileText }}
            </div>
          </mt-form-item>
        </mt-form>
      </div>
      <div class="hr"></div>
      <div class="relation-ships">
        <!----  表格  --------->
        <sc-table ref="xTable" :loading="loading" :columns="vxeColumns" :table-data="dataSource">
          <template slot="custom-tools">
            <template v-for="item in toolbar">
              <vxe-button
                v-if="isShow(item.code)"
                :key="item.code"
                :status="item.status"
                :icon="item.icon"
                size="small"
                :disabled="item.code !== 'Export' && type == 'detail'"
                @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
                >{{ item.name }}</vxe-button
              >
            </template>
          </template>
          <template #changeQuotaPercentHeade="{}">
            <span>
              <span class="red-color">*</span>
              <span>{{ $t('变更后协议配额（%）') }}</span>
            </span>
          </template>
          <template #changeQuotaPercent="{ row }"
            ><vxe-input
              v-if="row.editable"
              style="width: 100%"
              v-model="row.changeQuotaPercent"
              size="small"
              type="number"
              :min="0"
              :max="100"
              :precision="0"
            />
            <span v-else>{{ row.changeQuotaPercent }}</span></template
          >
          <template #changeEndTimeHeade="{}">
            <span>
              <span class="red-color">*</span>
              <span>{{ $t('变更后结束日期') }}</span>
            </span>
          </template>
          <template #changeEndTime="{ row }">
            <vxe-input
              v-if="row.editable"
              v-model="row.changeEndTime"
              type="date"
              transfer
            ></vxe-input>
            <span v-else>{{ row.changeEndTime }}</span></template
          >
          <template #operationDefault="{ row, rowIndex }">
            <div v-if="type != 'detail'">
              <div
                class="editable-row-operations"
                v-if="formObject.agreementType == 2 || formObject.agreementType == 3"
              >
                <span v-if="row.editable">
                  <a @click="() => handleSave(row, rowIndex)">确定 </a>
                  <a @click="() => cancelEdit(row, rowIndex)">{{ $t('取消') }}</a>
                </span>
                <span v-else>
                  <a @click="() => handleEdit(row, rowIndex)">{{ $t('编辑') }}</a>
                </span>
              </div>
            </div>
          </template>
        </sc-table>
        <!-- 分页 -->
        <mt-page
          ref="pageRef"
          class="flex-keep custom-page"
          :page-settings="pageSettings"
          :total-pages="pageSettings.totalPages"
          @currentChange="handleCurrentChange"
          @sizeChange="handleSizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { i18n } from '@/main.js'
import { vxeColumns } from './config/detail.js'
import { statusList, agreementTypeList } from './config/index.js'
// import { cloneDeep } from 'lodash'
import { getHeadersFileName, download } from '@/utils/utils'
import dayjs from 'dayjs'
import * as util from '@mtech-common/utils'
import { cloneDeep, throttle } from 'lodash'
import ScTable from '@/components/ScTable/src/index'
export default {
  components: {
    ScTable
  },
  data() {
    return {
      toolbar: [
        { icon: 'icon_solid_upload', status: 'info', name: this.$t('导入'), code: 'Import' },
        {
          icon: 'icon_solid_upload',
          status: 'info',
          name: this.$t('导入历史协议配额'),
          code: 'ImportHistory'
        },
        { icon: 'icon_solid_export', status: 'info', name: this.$t('删除'), code: 'Delete' },
        { icon: 'icon_solid_export', status: 'info', name: this.$t('导出'), code: 'Export' }
      ],
      loading: false, //禁用
      type: 'add', //add  update detail
      disabled: false, //禁用
      buList: [], //公司名称门
      statusList, //单据状态
      agreementTypeList, //单据类型
      formObject: {
        //表单数据
        id: '', // 协议配额单ID
        companyId: '', //公司
        companyName: '', //公司名称
        companyCode: '', //公司code
        title: null, //协议配额标题
        code: null, //协议配额单号
        agreementType: '', //协议类型
        status: '', //单据状态
        createUserName: '', //创建人
        createTime: '', //创建时间
        quotaStrategyFileSaveRequests: [] //上传附件
      },
      formRules: {
        companyId: [{ required: true, message: i18n.t('请选择公司名称'), trigger: 'blur' }],
        title: [{ required: true, message: i18n.t('请输入协议配额标题'), trigger: 'blur' }],
        agreementType: [{ required: true, message: i18n.t('请选择单据类型'), trigger: 'blur' }]
      },
      vxeColumns: [], //table列配置
      dataSource: [], //table数据
      isEdit: false,
      pageSettings: {
        pageCount: 5,
        pageSize: 20, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [10, 20, 50, 100]
      },
      editRow: {},
      saveFileList: [] //要保存的上传附件
    }
  },
  computed: {
    getFormFileText() {
      console.log('saveFileList', this.saveFileList)
      let _list = this?.saveFileList ?? []
      if (_list.length) {
        let _name = []
        _list.forEach((e) => {
          _name.push(e.fileName)
        })
        return _name.join(',')
      } else {
        return this.$t('上传附件')
      }
    },
    isEnableStatus() {
      return (this.formObject.status == 0 || this.formObject.status == 2) && this.type !== 'detail'
    },
    pageType() {
      return this.$route.query?.type === 'add'
    }
  },
  created() {},
  //进入页面
  activated() {
    let query = this.$route?.query
    this.type = query?.type
    //id存在 为创建新版本
    if (query?.type != 'add') {
      this.disabled = query?.type === 'update' || query?.type === 'detail'
      //根据id查询详情 数据回显
      this.getDetail(query.id)
      //获取列表数据
      this.getList(query.id)
    } else {
      this.vxeColumns = vxeColumns()
    }
  },
  mounted() {
    this.getBuList() //公司下拉数据
  },
  methods: {
    isShow(code) {
      let flag = false
      if (code === 'Import') {
        if (this.formObject.agreementType == 1 || this.formObject.agreementType == '') {
          flag = true
        }
      } else if (code === 'ImportHistory') {
        if (this.formObject.agreementType == 2 || this.formObject.agreementType == 3) {
          flag = true
        }
      } else {
        flag = true
      }
      return flag
    },
    getItemStatusLabel(val) {
      let itemStatusList = [
        { label: this.$t('草稿'), value: 0 },
        { label: this.$t('激活'), value: 1 },
        { label: this.$t('分发'), value: 2 },
        { label: this.$t('失控'), value: 3 }
      ]

      let item = itemStatusList.find((item) => item.value == val) || {}
      return item?.label
    },
    //上传附件弹框
    handleUploadDialog() {
      //上传附件 时必须需要 获取id 才能上传 所以必须保存成功后才能上传
      // if (this.type == 'add' && this.formObject.id == '') {
      //   this.$toast({
      //     content: this.$t('请先保存表单，再上传附件'),
      //     type: 'success'
      //   })
      //   return
      // }
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "components/Upload/uploaderDialog" */ '@/components/Upload/uploaderDialog.vue'
          ),
        data: {
          fileData: util.utils.cloneDeep(this.saveFileList),
          isView: this.type === 'detail', // 是否为预览
          required: false, // 是否必须
          title: this.$t('附件')
          // isSingleFile: true
        },
        success: (res) => {
          // 判断是否有缓存的文件数组 调文件删除接口后清空缓存的文件数组
          const idList = []
          for (let i = 0; i < this.saveFileList.length; i++) {
            const file = this.saveFileList[i]
            if (res.every((j) => j.originId !== file.originId)) {
              idList.push(file.originId)
            }
          }
          if (this.saveFileList && this.saveFileList.length && idList.length) {
            const params = {
              idList
            }
            this.$API.quotaAgreement.getFileDelete(params).then((resp) => {
              const { code } = resp
              if (code === 200) {
                let fileList = JSON.parse(JSON.stringify(res))
                this.saveFileList = fileList
              }
            })
          } else {
            let fileList = JSON.parse(JSON.stringify(res))
            this.saveFileList = fileList
          }
        }
      })
    },
    //获取公司名称
    getBuList() {
      this.$API.quotaAgreement.getPermissionCompanyList({}).then((res) => {
        this.buList = res.data
      })
    },
    //单据类型改变
    changeAgreementType(e) {
      let { itemData } = e
      // //根据单据类型改变table列 1:协议配额新增 2:协议配额结束日期变更 3.协议配额变更 ,2显示变更后结束日期，3显示变更后协议配额
      if (itemData) {
        this.vxeColumns = vxeColumns(itemData.status)
      }
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRows = $grid.getCheckboxRecords() // 表格勾选行的内容
      if (code === 'Import') {
        this.handleImport()
      } else if (code === 'ImportHistory') {
        this.handleHistoryImport()
      } else if (code === 'Delete') {
        this.handleDelete(selectedRows)
      } else if (code === 'Export') {
        this.handleExport()
      }
    },
    //导入
    handleImport() {
      //导入时 时必须先保存一次 获取id 才能导入
      if (this.type == 'add' && this.formObject.id == '') {
        this.$toast({
          content: this.$t('请先保存表单'),
          type: 'success'
        })
        return
      }
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('上传/导入'),
          importApi: this.$API.quotaAgreement.excelimport,
          asyncParams: {
            headerId: this.formObject.id
          },
          downloadTemplateApi: this.$API.quotaAgreement.exportTpl
        },
        success: () => {
          //刷新列表
          this.getList(this.formObject.id)
        }
      })
    },
    //导入历史协议配额
    handleHistoryImport() {
      if (this.type == 'add' && this.formObject.id == '') {
        this.$toast({
          content: this.$t('请先保存表单'),
          type: 'success'
        })
        return
      }
      this.$dialog({
        modal: () => import('./historyDialog.vue'),
        data: {
          title: this.$t('导入历史协议配额'),
          // companyId: this.formObject.companyId,
          companyCode: this.formObject.companyCode
          // pageConfig: this.config.pageConfig
        },
        success: (data) => {
          //将数据追加到table中
          let list = cloneDeep(data)
          //导入的数据 添加changeQuotaPercent和changeEndTime
          list.forEach((i) => {
            i.changeQuotaPercent = ''
            i.changeEndTime = ''
            i.new = true //类型为新增
          })
          this.dataSource.push(...list)
        }
      })
    },
    //导出
    handleExport() {
      //导出时 时必须先保存一次 获取id 才能导出
      if (this.type == 'add' && this.formObject.id == '') {
        this.$toast({
          content: this.$t('请先保存表单'),
          type: 'success'
        })
        return
      }
      let params = {
        id: this.formObject.id
      }
      //调用接口
      this.$API.quotaAgreement.itemExport(params).then((res) => {
        const fileName = getHeadersFileName(res)
        //下载文件
        download({
          fileName,
          blob: res.data
        })
      })
    },
    //公司名称下拉
    changeBu(e) {
      const { itemData } = e
      if (itemData) {
        this.formObject.companyId = itemData.id
        this.formObject.companyName = itemData.orgName
        this.formObject.companyCode = itemData.orgCode
      } else {
        this.formObject.companyId = ''
        this.formObject.companyName = ''
        this.formObject.companyCode = ''
      }
    },
    //根据id获取详情数据
    getDetail(id) {
      let params = {
        id
      }
      //请求详情接口
      this.$API.quotaAgreement.getDetails(params).then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.formObject = data
          if (data.quotaStrategyFileResponses && data.quotaStrategyFileResponses.length) {
            let fileList = data.quotaStrategyFileResponses
            fileList.forEach((i) => {
              i.originId = i.id
              i.id = i.fileId
            })
            // this.formObject.quotaStrategyFileSaveRequests = fileList //quotaStrategyFileSaveRequests 是保存时附件的参数
            this.saveFileList = fileList //cacheFileList 是缓存的附件列表
          } else {
            // this.formObject.quotaStrategyFileSaveRequests = []
            this.saveFileList = []
          }
        }
      })
    },
    //获取表格数据
    getList(id) {
      let params = {
        id,
        page: { current: this.pageSettings.current, size: this.pageSettings.pageSize }
      }
      //请求列表接口
      this.loading = true
      this.$API.quotaAgreement
        .queryList(params)
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.dataSource = data.records
            const total = res?.data?.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)
            this.isEdit = false
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageSettings.current = currentPage
      let query = this.$route?.query
      this.getList(query.id)
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      let query = this.$route?.query
      this.getList(query.id)
    },
    // 删除
    handleDelete(selectedRows) {
      //选中判断
      if (selectedRows.length <= 0) {
        this.$toast({
          content: this.$t('请勾选需要操作的数据'),
          type: 'warning'
        })
        return
      }

      //删除前的确认
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据')
        },
        success: () => {
          const isAllNewItem = selectedRows.every((item) => item.optionType === 'add')
          if (isAllNewItem) {
            selectedRows.forEach((item) => {
              this.dataSource.splice(this.dataSource.indexOf(item), 1)
            })
            return
          }
          const params = {
            code: this.formObject.code,
            detailIdList: selectedRows.filter((item) => item.type !== 'add').map((item) => item.id),
            id: this.formObject.id
          }
          this.$API.quotaAgreement.detailBatchDelete(params).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.getList(this.formObject.id)
          })
        }
      })
    },
    //开启当前行编辑
    handleEdit(row) {
      this.editRow = JSON.parse(JSON.stringify(row))
      this.$set(row, 'editable', true)
    },
    //取消编辑
    cancelEdit(row) {
      this.$set(row, 'editable', false)
      this.$set(row, 'changeEndTime', this.editRow.changeEndTime)
      this.$set(row, 'changeQuotaPercent', this.editRow.changeQuotaPercent)
    },
    //明细列 - 保存编辑  只保存数据在页面内 不提交接口
    async handleSave(row) {
      // if (isValid) {
      //   // let quotaPercent = new Decimal(row.quotaPercent || 0).div(new Decimal(100)).toNumber()
      //   // row.quotaPercent = quotaPercent
      //   this.$set(row, 'editable', false)
      // }
      if (this.formObject.agreementType == 2) {
        // 校验time
        if (!row.changeEndTime) {
          this.$toast({
            content: this.$t('日期不能为空'),
            type: 'warning'
          })
          return
        }
      }
      if (this.formObject.agreementType == 3) {
        if (!row.changeQuotaPercent) {
          this.$toast({
            content: this.$t('请输入变更后协议配额'),
            type: 'warning'
          })
          return
        }
      }
      this.$set(row, 'editable', false)
    },
    //新增时保存
    createSave(row, index) {
      console.log(row, index)
    },
    //编辑时保存
    updateSave(row, index) {
      console.log(row, index)
    },
    getParams() {
      //表单校验
      let ruleForm = false
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          ruleForm = true
        }
      })
      if (!ruleForm) return
      //表格数据处理
      let form = { ...this.formObject }
      let flag = false
      //agreementType=2 校验dataSource 变更后结束日期必填 ， agreementType=3 校验 dataSource变更后协议配额必填
      if (form.agreementType == 2) {
        this.dataSource.forEach((item) => {
          if (item.changeEndTime == null || item.changeEndTime == '') {
            this.$toast({
              content: this.$t(`工厂${item.siteCode},变更后结束日期不能为空`),
              type: 'warning'
            })
            flag = true
            return
          }
        })
      } else if (form.agreementType == 3) {
        this.dataSource.forEach((item) => {
          if (item.changeQuotaPercent == null || item.changeQuotaPercent == '') {
            this.$toast({
              content: this.$t(`工厂${item.siteCode},变更后协议配额不能为空`),
              type: 'warning'
            })
            flag = true
            return
          }
        })
      }
      if (flag) return
      let list = cloneDeep(this.dataSource)
      //明细表数据处理 在单据类型agreementType为2和3时 ，保存时要将new=true（新增导入的数据）进行处理，id转给sourceId ,form.code转给sourceCode
      if (form.agreementType == 2 || form.agreementType == 3) {
        list.forEach((item) => {
          if (item.new) {
            item.sourceId = item.id
            item.sourceCode = form.code
            item.id = ''
            delete item.new
          }
        })
      }
      //创建时间转换为年月日 时分秒
      form.createTime = form.createTime ? dayjs(form.createTime).format('YYYY-MM-DD HH:mm:ss') : ''
      //处理文件数据
      if (this.saveFileList && this.saveFileList.length) {
        form.quotaStrategyFileSaveRequests = this.saveFileList.map((i) => {
          let obj = {
            ...i,
            fileId: i.id
          }
          delete obj.id
          if (obj.originId) {
            obj.id = obj.originId
          }
          // if (form.id) {
          //   obj.strategyId = form.id
          // }
          // if (form.code) {
          //   obj.strategyCode = form.code
          // }
          return obj
        })
      } else {
        form.quotaStrategyFileSaveRequests = []
      }
      delete form.quotaStrategyFileResponses
      let params = {
        ...form,
        detailList: list
      }
      return params
    },
    //保存详情页
    save: throttle(function () {
      let params = this.getParams()
      if (!params) return
      this.$API.quotaAgreement.save(params).then((res) => {
        if (res.code === 200) {
          this.getDetail(res.data)
          this.getList(res.data)
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
        }
      })
    }, 1000),
    //提交详情页
    submit: throttle(function () {
      let params = this.getParams()
      if (!params) return
      this.$dialog({
        data: {
          title: this.$t('操作'),
          message: this.$t(`确认提交吗？`)
        },
        success: () => {
          this.$API.quotaAgreement.commit(params).then((res) => {
            if (res.code === 200) {
              this.$toast({
                content: this.$t('提交成功'),
                type: 'success'
              })
              this.$bus.$emit('refreshQuotaAgreementList')
              this.backToBusinessConfig()
            }
          })
        }
      })
    }, 1000),
    //返回协议配额列表页
    backToBusinessConfig() {
      //清空表单
      this.formObject = {
        //表单数据
        id: '', // 协议配额单ID
        companyId: '', //公司
        companyName: '', //公司名称
        companyCode: '', //公司code
        title: null, //协议配额标题
        code: null, //协议配额单号
        agreementType: '', //协议类型
        status: '', //单据状态
        createUserName: '', //创建人
        createTime: '', //创建时间
        quotaStrategyFileSaveRequests: []
      }
      //表单取消校验
      this.$refs['ruleForm'].clearValidate()
      //重置数据
      this.dataSource = []
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .noClick {
  color: #ccc !important;
}
/deep/ .custom-table-container .ant-table-body tr td {
  height: 42px;
}
.button-group {
  display: flex;
  padding: 8px;
}
/deep/.mt-form-item {
  width: calc(30% - 20px);
  min-width: 200px;
  display: inline-flex;
  margin-right: 20px;
  .mt-input {
    width: 100%;
  }
}
.supplier-box {
  position: absolute;
  width: 100%;
  min-height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: #fff;
  overflow: auto;
  -webkit-overflow-scrolling: scroll;

  .miam-container {
    width: 100%;
    background: #fff;
    padding: 0 20px;
    .header {
      height: 40px;
      display: flex;
      justify-content: space-between;
      .title {
        height: 100%;
        line-height: 40px;
        font-size: 20px;
        font-weight: 600;
        color: #333;
        padding: 5px 10px;
      }
      .operate-bar {
        height: 100%;
        float: right;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        color: #4f5b6d;

        .op-item {
          cursor: pointer;
          align-items: center;
          margin-right: 20px;
          align-items: center;
          background-color: rgba(0, 0, 0, 0);
          border-color: rgba(0, 0, 0, 0);
          color: #00469c;
        }
      }
    }
    .mian-info {
      background: rgba(255, 255, 255, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    }

    .relation-ships {
      background: rgba(255, 255, 255, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      margin-top: 20px;
      // height: calc(100% - 164px);
    }
  }
  .hr {
    margin: 10px 0;
    padding: 0;
    border: 0;
    border-top: 1px solid #e9e9e9;
  }
}
::v-deep.red-color {
  color: red;
}
</style>
