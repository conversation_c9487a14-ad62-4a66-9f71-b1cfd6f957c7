<template>
  <!-- 协议配额 -->
  <div class="full-height">
    <local-template-page
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
// import { i18n } from '@/main.js'
import { getHeadersFileName, download } from '@/utils/utils'
import localTemplatePage from '@/components/template-page'

export default {
  components: {
    localTemplatePage
  },
  data() {
    return {
      companyList: [],
      pageConfig: []
    }
  },
  async created() {
    this.init() //获取公司名称
    // this.$nextTick(() => {
    //   this.pageConfig = pageConfig(this.$API.quotaAgreement.queryBuilder, this.companyList)
    // })

    this.$bus.$on('refreshQuotaAgreementList', () => {
      this.$refs.templateRef.refreshCurrentGridData()
    })
  },
  methods: {
    //初始化
    async init() {
      let orgData = []
      //获取公司名称
      const { code, data } = await this.$API.quotaAgreement.getPermissionCompanyList({})
      if (code === 200) {
        orgData = data
        Array.isArray(orgData) &&
          orgData.forEach((item) => {
            item.orgId = item.id
            item.cssClass = 'title-'
          })
      }
      this.pageConfig = pageConfig(this.$API.quotaAgreement.queryBuilder, orgData)
    },
    //新增
    handleAdd() {
      this.$router.push({
        name: `quota-strategy-agreement-item`,
        query: {
          type: 'add',
          flashKey: new Date().getTime()
        }
      })
    },
    //导出
    handleExport() {
      let params = {
        page: { current: 1, size: 9999 }
      }
      //调用接口
      this.$API.quotaAgreement.excelExport(params).then((res) => {
        const fileName = getHeadersFileName(res)
        //下载文件
        download({
          fileName,
          blob: res.data
        })
      })
    },
    //删除
    handleDelete(data) {
      console.log(data)
      // 调对应接口后刷新列表
      let idList = [data.id]
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.quotaAgreement.batchDelete({ idList }).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    //表格头部按钮-点击事件
    handleClickToolBar(e) {
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'ExportFile') {
        this.handleExport()
      }
    },
    //操作栏
    handleClickCellTool(e) {
      const { tool, data } = e
      if (tool.id === 'edit') {
        this.$router.push({
          name: `quota-strategy-agreement-item`,
          query: {
            type: 'update',
            id: data.id,
            flashKey: new Date().getTime()
          }
        })
      } else if (tool.id === 'delete') {
        this.handleDelete(data)
      }
    },
    //单元格标题点击
    handleClickCellTitle(e) {
      const { data } = e
      this.$router.push({
        name: `quota-strategy-agreement-item`,
        query: {
          type: data['status'] == '0' || data['status'] == '2' ? 'update' : 'detail',
          id: data.id,
          flashKey: new Date().getTime()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .e-grid .e-frozencontent.e-frozen-right-content > .e-table {
  border-left: none !important;
}
/deep/ .e-grid .e-frozenheader.e-frozen-right-header > .e-table {
  border-left: none !important;
}
</style>
