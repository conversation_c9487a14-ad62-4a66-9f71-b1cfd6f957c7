<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <collapse-search @reset="reset" @search="handleSearch">
        <mt-form ref="ruleForm" :model="formObject" :rules="formRules">
          <mt-form-item prop="code" :label="$t('协议配额单号')" label-style="top">
            <mt-input
              width="100%"
              type="text"
              v-model="formObject.code"
              :placeholder="$t('请输入协议配额单号')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="title" :label="$t('协议配额标题')" label-style="top">
            <mt-input
              width="100%"
              type="text"
              v-model="formObject.title"
              :placeholder="$t('请输入协议配额标题')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="siteId" :label="$t('工厂代码')" label-style="top">
            <mt-select
              v-model="formObject.siteId"
              float-label-type="Never"
              :allow-filtering="true"
              filter-type="Contains"
              :show-clear-button="true"
              :data-source="factoryList"
              :fields="{ text: 'orgCode', value: 'id' }"
              :placeholder="$t('请选择工厂代码')"
            ></mt-select
          ></mt-form-item>
          <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
            <mt-input
              width="100%"
              type="text"
              v-model="formObject.itemCode"
              :placeholder="$t('请输入物料编码')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </collapse-search>
      <!----  表格  --------->
      <sc-table
        ref="xTable"
        grid-id="94993b06-61f6-4c77-8ca5-9b2c08a6e066"
        :loading="loading"
        :columns="vxeColumns"
        :table-data="dataSource"
      >
      </sc-table>
      <!-- 分页 -->
      <mt-page
        ref="pageRef"
        class="flex-keep custom-page"
        :page-settings="pageSettings"
        :total-pages="pageSettings.totalPages"
        @currentChange="handleCurrentChange"
        @sizeChange="handleSizeChange"
      />
    </div>
  </mt-dialog>
</template>

<script>
import collapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { detailColumns } from './config/detail.js'
export default {
  components: {
    ScTable,
    collapseSearch
  },
  data() {
    return {
      loading: false, //禁用
      formObject: {
        //表单数据
        title: null, //协议配额标题
        code: null, //协议配额单号
        itemCode: null, //物料编码
        siteId: null //工厂代码ID
      },
      formRules: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('导入数据') }
        }
      ],
      factoryList: [], // 工厂列表
      selectedRowKeys: [], // 表格勾选行的id
      selectedRows: [], // 表格勾选行的内容
      dataSource: [],
      vxeColumns: detailColumns,
      pageSettings: {
        pageCount: 5,
        pageSize: 20, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [10, 20, 50, 100]
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.getFactoryList()
    this.getList()
  },
  methods: {
    //工厂下拉数据
    getFactoryList() {
      this.$API.quotaAgreement.sites({}).then((res) => {
        this.factoryList = res.data
      })
    },
    reset() {
      this.$refs['ruleForm'].resetFields()
    },
    handleSearch() {
      this.pageSettings.current = 1
      this.getList()
    },
    //获取表格数据
    getList() {
      let params = {
        // companyId: this.modalData.companyId,
        companyCode: this.modalData.companyCode,
        ...this.formObject,
        page: { current: this.pageSettings.current, size: this.pageSettings.pageSize }
      }
      //请求列表接口
      this.$API.quotaAgreement.queryHistory(params).then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.dataSource = data.records
          const total = res?.data?.total || 0
          this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
          this.pageSettings.totalRecordsCount = Number(total)
        }
      })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageSettings.current = currentPage
      this.getList()
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.getList()
    },
    //确认
    confirm() {
      const selectedRows = this.$refs.xTable.$refs.xGrid.getCheckboxRecords()
      if (selectedRows <= 0) {
        this.$toast({ content: this.$t('请先选择至少一行'), type: 'warning' })
        return
      } else {
        //处理数组数据 selectedRows添加sourceId和sourceCode 对应id和agreementCode
        let data = [...selectedRows]
        data.forEach((item) => {
          item.sourceId = item.id
          item.sourceCode = item.agreementCode
        })

        this.$emit('confirm-function', data)
      }
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  // display: flex;
  height: 100%;
  width: 100%;
}
.mt-form-item {
  width: calc(30% - 20px);
  min-width: 200px;
  display: inline-flex;
  margin-right: 20px;
}
</style>
