import { i18n } from '@/main.js'
import utils from '@/utils/utils'
let options = utils.getQuotaDict('QUOTA_ITEM_SUBMIT_STATUS') || [] //单据状态
export const statusList = options.map((item) => {
  return {
    label: item.dictName,
    status: item.dictCode,
    cssClass: 'title-'
  }
})
let options2 = utils.getQuotaDict('QUOTA_STRATEGY_XY_TYPE') || [] //单据类型
export const agreementTypeList = options2.map((item) => {
  return {
    label: item.dictName,
    status: item.dictCode,
    cssClass: 'title-'
  }
})
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'ExportFile', icon: 'icon_solid_Createorder', title: i18n.t('导出') }
]
const columnData = function (orgData) {
  return [
    {
      type: 'checkbox',
      width: '60'
    },
    {
      field: 'companyId',
      headerText: i18n.t('公司名称'),
      // valueConverter: {
      //   type: 'map',
      //   map: orgData,
      //   fields: { text: 'orgName', value: 'id' }
      // }
      valueAccessor: (field, data) => {
        // console.log(orgData)
        return data.companyName
      },
      searchOptions: {
        elementType: 'select',
        dataSource: orgData,
        fields: { text: 'orgName', value: 'id' }
      }
    },
    {
      field: 'code',
      headerText: i18n.t('协议单号'),
      cssClass: 'field-content'
    },
    {
      field: 'title',
      headerText: i18n.t('协议标题')
    },
    {
      field: 'agreementType',
      headerText: i18n.t('单据类型'),
      valueConverter: {
        type: 'map',
        map: agreementTypeList,
        fields: { text: 'label', value: 'status' }
      }
    },
    {
      field: 'status',
      headerText: i18n.t('单据状态'),
      valueConverter: {
        type: 'map',
        map: statusList,
        fields: { text: 'label', value: 'status' }
      }
    },

    {
      field: 'createUserName',
      headerText: i18n.t('创建人')
    },
    {
      field: 'createTime',
      headerText: i18n.t('创建时间'),
      //忽略
      ignore: true,
      searchOptions: {
        elementType: 'date' //日期选择范围
      }
    },
    {
      field: 'createDateStart',
      headerText: i18n.t('创建时间从'),
      width: '0.1',
      searchOptions: {
        elementType: 'datetime',
        operator: 'between'
      }
    },
    {
      field: 'createDateEnd',
      headerText: i18n.t('创建时间至'),
      width: '0.1',
      searchOptions: {
        elementType: 'datetime',
        operator: 'between'
      }
    },
    {
      field: 'operation',
      headerText: i18n.t('操作'),
      cssClass: 'field-content',
      ignore: true, //忽略
      // freeze: 'right',
      freeze: 'Right',
      cellTools: [
        {
          id: 'edit',
          icon: 'icon_Editor',
          title: i18n.t('编辑'),
          visibleCondition: (data) => {
            //拟定和已驳回状态可以编辑
            return data['status'] == '0' || data['status'] == '2'
          }
        },
        {
          id: 'delete',
          icon: 'icon_solid_Delete',
          title: i18n.t('删除'),
          visibleCondition: (data) => {
            //拟定和已驳回状态可以删除
            return data['status'] == '0' || data['status'] == '2'
          }
        }
      ]
    }
  ]
}
export const pageConfig = (url, orgData) => [
  {
    toolbar,
    useToolTemplate: false,
    gridId: 'f86c1447-6ccf-4555-bc47-90a87bc2974f',
    grid: {
      allowFilteringFields: ['companyId'],
      columnData: columnData(orgData),
      asyncConfig: { url }
    }
  }
]
