import { i18n } from '@/main.js'
//列配置
export const vxeColumns = function (status) {
  let changeQuotaPercent = {
    title: i18n.t('*变更后协议配额（%）'),
    // slots: { title: 'changeQuotaPercentTitle' },
    field: 'changeQuotaPercent',
    align: 'left',
    width: 200,
    slots: {
      header: 'changeQuotaPercentHeade',
      default: 'changeQuotaPercent'
    }
    // scopedSlots: { customRender: 'changeEndTime' }
  }
  let changeEndTime = {
    title: i18n.t('*变更后结束日期'),
    // slots: { title: 'changeEndTimeTitle' },
    field: 'changeEndTime',
    align: 'left',
    width: 200,
    slots: {
      header: 'changeEndTimeHeade',
      default: 'changeEndTime'
    }
    // scopedSlots: { customRender: 'changeEndTime' }
  }
  let columns = [
    {
      type: 'checkbox',
      minWidth: 50
      // fixed: 'left'
    },
    {
      title: i18n.t('工厂代码'),
      field: 'siteCode',
      align: 'left'
    },
    {
      title: i18n.t('工厂名称'),
      field: 'siteName',
      align: 'left'
    },
    {
      title: i18n.t('物料编码'),
      field: 'itemCode',
      align: 'left'
    },
    {
      title: i18n.t('物料名称'),
      field: 'itemName',
      align: 'left'
    },
    {
      title: i18n.t('物料状态'),
      field: 'itemStatus',
      align: 'left',
      formatter: ({ cellValue }) => {
        const options = [
          { label: i18n.t('草稿'), value: 0 },
          { label: i18n.t('激活'), value: 1 },
          { label: i18n.t('分发'), value: 2 },
          { label: i18n.t('失控'), value: 3 }
        ]
        let item = options.find((item) => item.value === cellValue)
        return item ? item.label : ''
      }
    },
    {
      title: i18n.t('供应商编码'),
      field: 'supplierCode',
      align: 'left'
    },
    {
      title: i18n.t('供应商名称'),
      field: 'supplierName',
      align: 'left'
    },
    {
      title: i18n.t('协议配额（%）'),
      field: 'quotaPercent',
      align: 'left'
    },
    // {
    //   title: i18n.t('*变更后协议配额（%）'),
    //   // slots: { title: 'changeQuotaPercentTitle' },
    //   dataIndex: 'changeQuotaPercent',
    //   field: 'changeQuotaPercent',
    //   align: 'left',
    //   width: 200,
    //   editConfig: {
    //     controlKey: 'editable',
    //     editorProps: { component: 'a-input', placeholder: i18n.t('请输入变更后协议配额') },
    //     rules: () => [
    //       { required: true, message: i18n.t('请输入变更后协议配额'), triggle: 'blur' },
    //       { validator: validateNumber, max: 100, min: 0, checkMin: true, float: 2 }
    //     ]
    //   }
    //   // scopedSlots: { customRender: 'changeEndTime' }
    // },
    {
      title: i18n.t('开始时间'),
      field: 'startTime',
      align: 'left'
    },
    {
      title: i18n.t('结束时间'),
      field: 'endTime',
      align: 'left'
    },
    {
      title: i18n.t('导入信息'),
      field: 'importInfo',
      align: 'left'
    },
    {
      title: i18n.t('操作'),
      width: 80,
      field: 'operation',
      fixed: 'right',
      slots: {
        default: 'operationDefault'
      }
    }
  ]
  //根据单据类型status改变table列 1:协议配额新增 2:协议配额结束日期变更 3.协议配额变更 ,2显示变更后结束日期，3显示变更后协议配额
  if (status == 2) {
    columns.splice(10, 0, changeEndTime)
  } else if (status == 3) {
    columns.splice(8, 0, changeQuotaPercent)
  }
  return columns
}

export const detailColumns = [
  {
    type: 'checkbox',
    minWidth: 50
    // fixed: 'left'
  },
  {
    title: i18n.t('协议配额单号'),
    field: 'agreementCode',
    width: 150,
    align: 'left'
  },
  {
    title: i18n.t('协议配额标题'),
    field: 'title',
    width: 150,
    align: 'left'
  },
  {
    title: i18n.t('工厂代码'),
    field: 'siteCode',
    width: 150,
    align: 'left'
  },
  {
    title: i18n.t('工厂名称'),
    field: 'siteName',
    width: 200,
    align: 'left'
  },
  {
    title: i18n.t('物料编码'),
    field: 'itemCode',
    width: 200,
    align: 'left'
  },
  {
    title: i18n.t('物料名称'),
    field: 'itemName',
    width: 150,
    align: 'left'
  },
  {
    title: i18n.t('物料状态'),
    field: 'itemStatus',
    align: 'left',
    formatter: ({ cellValue }) => {
      const options = [
        { text: i18n.t('拟定'), code: '0' },
        { text: i18n.t('有效'), code: '1' },
        { text: i18n.t('失效'), code: '2' }
      ]
      let item = options.find((item) => item.code === cellValue)
      return item ? item.text : ''
    }
  },
  {
    title: i18n.t('供应商编码'),
    field: 'supplierCode',
    width: 200,
    align: 'left'
  },
  {
    title: i18n.t('供应商名称'),
    field: 'supplierName',
    width: 150,
    align: 'left'
  },
  {
    title: '协议配额(%)',
    field: 'quotaPercent',
    width: 150,
    align: 'left'
  },
  {
    title: i18n.t('开始日期'),
    field: 'startTime',
    width: 150,
    align: 'left'
  },
  {
    title: i18n.t('结束日期'),
    field: 'endTime',
    width: 150,
    align: 'left'
  }
]
