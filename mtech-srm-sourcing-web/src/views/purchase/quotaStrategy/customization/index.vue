<template>
  <div class="quato-customization">
    <collapse-search :is-grid-display="true" @reset="reset" @search="handleSearch">
      <mt-form ref="ruleForm" :model="queryForm">
        <mt-form-item prop="orgId" :label="$t('工厂代码')" label-style="top">
          <!-- <mt-DropDownTree
            v-if="fieldsarr.dataSource.length"
            v-model="queryForm.orgId"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择工厂')"
            :popup-height="500"
            :fields="fieldsarr"
            id="baseTreeSelect"
          ></mt-DropDownTree> -->
          <mt-select
            v-model="queryForm.siteCode"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="factoryList"
            :fields="{ text: 'orgCode', value: 'orgCode' }"
            :placeholder="$t('请选择工厂代码')"
            @change="handleFactoryChange($event)"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
          <magnifier-input
            ref="itemCode"
            :config="materialDialogCofig"
            @change="materialCodeChange($event)"
          ></magnifier-input>
        </mt-form-item>
        <mt-form-item prop="categoryId" :label="$t('品类')" label-style="top">
          <magnifier-input
            ref="categoryId"
            :config="categoryDialogCofig"
            @change="categoryCodeChange($event)"
          ></magnifier-input>
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-select
            v-model="queryForm.status"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="statusOptions"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('发布状态')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商')" label-style="top">
          <magnifier-input
            ref="supplierCode"
            :config="supplierDialogCofig1"
            @change="supplierCodeChange($event)"
          ></magnifier-input>
        </mt-form-item>
        <mt-form-item prop="startDateStart" :label="$t('开始月份起')" label-style="top">
          <mt-date-picker
            v-model="queryForm.startDateStart"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :separator="$t('至')"
            :placeholder="$t('选择开始月份起')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="startDateEnd" :label="$t('开始月份止')" label-style="top">
          <mt-date-picker
            v-model="queryForm.startDateEnd"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :separator="$t('至')"
            :placeholder="$t('选择开始月份止')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="endDateStart" :label="$t('结束月份起')" label-style="top">
          <mt-date-picker
            v-model="queryForm.endDateStart"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :separator="$t('至')"
            :placeholder="$t('选择结束月份起')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="endDateEnd" :label="$t('结束月份止')" label-style="top">
          <mt-date-picker
            v-model="queryForm.endDateEnd"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :separator="$t('至')"
            :placeholder="$t('选择结束月份止')"
          ></mt-date-picker>
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!----  表格  --------->
    <sc-table
      ref="xTable"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="vxeColumns"
      :table-data="dataSource"
      @refresh="search"
      class="components-table-demo-nested"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
          >{{ item.name }}</vxe-button
        >
      </template>
      <template #factoryHead="{}"><span class="red-color">*</span>{{ $t('工厂代码') }} </template>
      <template #orgIdDefault="{ row }">
        <vxe-select
          v-if="row.editable"
          v-model="row.orgId"
          :options="factoryList"
          :option-props="{
            label: 'codeAndName',
            value: 'id'
          }"
          @change="handleFactoryChange($event.value, row)"
          transfer
          filterable
        ></vxe-select>
        <span v-else>{{ row.orgCode }}</span>
      </template>
      <template #itemCodeHead="{}"><span class="red-color">*</span>{{ $t('物料编码') }} </template>
      <template #itemCodeDefault="{ row }">
        <magnifier-input
          v-if="row.editable"
          :disabled="!row.orgId"
          :default-value="row.itemCode"
          :config="materialTableDialogCofig(row)"
          @change="materialCodeChange($event, row)"
        ></magnifier-input>
        <span v-else>{{ row.itemCode }}</span>
      </template>
      <template #supplierCodeHead="{}"
        ><span class="red-color">*</span>{{ $t('供应商编码') }}
      </template>
      <template #supplierCodeDefault="{ row }">
        <magnifier-input
          v-if="row.editable"
          :default-value="row.supplierCode"
          :disabled="!row.orgCode"
          :config="supplierDialogCofig(row)"
          @change="supplierCodeChange($event, row)"
        ></magnifier-input>
        <span v-else>{{ row.supplierCode }}</span>
      </template>
      <template #quotaPercentHead="{}"
        ><span class="red-color">*</span>{{ $t('制定配额%') }}
      </template>
      <template #quotaPercentDefault="{ row }">
        <vxe-input
          v-if="row.editable"
          v-model="row.quotaPercent"
          type="number"
          size="small"
          :min="1"
          :max="100"
          :precision="0"
        />
        <span v-else>{{ row.quotaPercent }}</span>
      </template>
      <template #startDateHead="{}"><span class="red-color">*</span>{{ $t('开始月份') }} </template>
      <template #startDateDefault="{ row }">
        <vxe-input
          v-if="row.editable"
          v-model="row.startDate"
          :placeholder="$t('请选择日期')"
          type="month"
          value-format="yyyy-MM-dd"
          :disabled-method="
            ({ date }) => date.getTime() < new Date(moment(new Date()).add('month', -1)).getTime()
          "
          transfer
        ></vxe-input>
        <span v-else>{{ getFormatTime(row.startDate, 'YYYY-mm') }}</span>
      </template>
      <template #endDateHead="{}"><span class="red-color">*</span>{{ $t('结束月份') }} </template>
      <template #endDateDefault="{ row }">
        <vxe-input
          v-if="row.editable"
          v-model="row.endDate"
          :placeholder="$t('请选择日期')"
          type="month"
          value-format="yyyy-MM-dd"
          :disabled-method="
            ({ date }) => date.getTime() < new Date(moment(new Date(row.startDate))).getTime()
          "
          transfer
        ></vxe-input>
        <span v-else>{{ getFormatTime(row.endDate, 'YYYY-mm') }}</span>
      </template>
      <template #operationDefault="{ row, rowIndex }">
        <div class="editable-row-operations">
          <span v-if="row.editable">
            <a @click="() => save(row, rowIndex)">{{ $t('保存') }}</a>
            &nbsp;
            <a @click="() => cancelEdit(row, rowIndex)">{{ $t('取消') }}</a>
          </span>
          <span v-else>
            <a :disabled="row.status !== 0" @click="() => edit(row, rowIndex)">{{ $t('编辑') }}</a>
          </span>
        </div>
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import moment from 'moment'
import utils from '@/utils/utils'
import collapseSearch from '@/components/collapseSearch'
import magnifierInput from '@/components/magnifierInput'
import { pageConfig, vxeColumns } from './config'
import ScTable from '@/components/ScTable/src/index'
export default {
  components: {
    collapseSearch,
    magnifierInput,
    ScTable
  },
  data() {
    return {
      loading: false,
      toolbar: [
        {
          code: 'Add',
          name: this.$t('新增'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Effect',
          name: this.$t('生效'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Invalid',
          name: this.$t('失效'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Import',
          name: this.$t('导入'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Delete',
          name: this.$t('删除'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Export',
          name: this.$t('导出'),
          icon: 'el-icon-delete',
          status: 'info'
        }
      ],
      queryForm: {
        siteCode: '', // 组织
        categoryId: '', // 品类
        status: '', // 状态
        itemId: '', // 物料编码
        supplierId: '',
        startDateStart: '', // 开始月份起
        startDateEnd: '', // 开始月份止
        endDateStart: '', // 结束月份起
        endDateEnd: '' // 结束月份止
      },
      pageSettings: {
        pageCount: 5,
        pageSize: 20, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [10, 20, 50, 100]
      },
      factoryList: [], // 工厂列表
      statusOptions: [],
      vxeColumns,
      dataSource: [],
      fieldsarr: {
        dataSource: [], // 组织树下拉数组
        value: 'id',
        text: 'orgId',
        child: 'childrenList'
      },
      // 供应商放大镜弹窗配置
      supplierDialogCofig1: {
        pageConfig: pageConfig(
          '/masterDataManagement/tenant/supplier/pagedQueryByOrgCode',
          'supplier'
        ),
        text: 'supplierName',
        value: 'supplierCode'
      },
      // 物料放大镜弹窗配置
      materialDialogCofig: {
        pageConfig: pageConfig(
          `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
          'material'
        ),
        text: 'itemCode',
        value: 'itemCode'
      },
      // 品类放大镜弹窗配置
      categoryDialogCofig: {
        pageConfig: pageConfig(
          '/masterDataManagement/tenant/permission/queryCategories',
          'category'
        ),
        text: 'categoryName',
        value: 'categoryCode'
      },
      isEdit: false,
      cacheRow: {}
    }
  },
  created() {
    this.getStatusOptionsList()
    this.getFactoryList()
    this.search()
  },
  methods: {
    getStatusOptionsList() {
      // 业务类型 - businessType
      // this.$API.masterData
      //   .dictionaryGetList({
      //     dictCode: 'QUOTA_STRATEGY_SHORT_STATUS'
      //   })
      //   .then((res) => {
      //     this.statusOptions = res.data
      //   })
      this.statusOptions = utils.getQuotaDict('QUOTA_STRATEGY_SHORT_STATUS') || []
    },
    // 供应商放大镜弹窗配置
    supplierDialogCofig(row) {
      let params = {}
      if (row && row.orgCode) {
        params = {
          siteCode: row.orgCode
        }
      }
      return {
        pageConfig: pageConfig(
          '/masterDataManagement/tenant/supplier/pagedQueryBySiteCode',
          'supplier',
          params
        ),
        text: 'supplierCode',
        value: 'supplierCode'
      }
    },
    materialTableDialogCofig(row) {
      let params = {}
      if (row && row.orgCode) {
        params = {
          siteCodes: [row.orgCode]
        }
      }
      return {
        pageConfig: pageConfig(
          // '/masterDataManagement/auth/supplier/getSiteItemFuzzyQuery',
          `/masterDataManagement/tenant/item-org-rel/categoryAuthorityByItem?BU_CODE=${localStorage.getItem('currentBu')}`,
          'material',
          params
        ),
        text: 'itemCode',
        value: 'itemCode'
      }
    },
    moment,
    startDateChange(date, row) {
      if (!date) {
        this.$set(row, 'endDate', '')
      }
    },
    getFormatTime(date, format) {
      if (date && new Date(date)) {
        return utils.formatTime(new Date(date), format)
      }
      return ''
    },
    getFactoryList() {
      this.$API.customization.getPermissionSiteList({}).then((res) => {
        this.factoryList = res.data.map((i) => {
          return {
            ...i,
            codeAndName: i.orgCode + '-' + i.orgName
          }
        })
      })
    },
    handleAdd() {
      if (this.isEdit === true) {
        this.$toast({
          content: this.$t('新增失败，存在未保存的数据'),
          type: 'warning'
        })
        return
      }
      const obj = {
        editable: true,
        orgId: '',
        orgName: '',
        itemCode: '',
        itemName: '',
        categoryId: '',
        categoryName: '',
        supplierCode: '',
        supplierName: '',
        coder: '',
        quotaPercent: '',
        startDate: '',
        endDate: '',
        status: '',
        creator: '',
        createTime: '',
        effectiveDate: '',
        expirationDate: ''
      }
      if (!obj.orgId && this.factoryList && this.factoryList.length === 1) {
        obj.orgId = this.factoryList[0]['id']
        obj.orgCode = this.factoryList[0]['orgCode']
        obj.orgName = this.factoryList[0]['orgName']
      }
      this.isEdit = true
      this.dataSource.unshift(obj)
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      if (code === 'Add') {
        this.handleAdd()
        return
      }
      const selectedRows = $grid.getCheckboxRecords() // 表格勾选行的内容
      if (
        (code === 'Effect' || code === 'Invalid' || code === 'Delete') &&
        selectedRows.length <= 0
      ) {
        this.$toast({
          content: this.$t('请勾选需要操作的数据'),
          type: 'warning'
        })
        return
      }
      if (this.isEdit === true) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('存在未保存的数据，是否继续当前操作')
          },
          success: () => {
            if (code === 'Effect') {
              // 生效
              this.handleEffect(selectedRows)
            } else if (code === 'Invalid') {
              // 失效
              this.handleInvalid(selectedRows)
            } else if (code === 'Import') {
              // 导入
              this.handleImport()
            } else if (code === 'Delete') {
              // 删除
              this.handleDelete(selectedRows)
            } else if (code === 'Export') {
              // 导出
              this.handleExport()
            }
          }
        })
        return
      }
      if (code === 'Effect') {
        // 生效
        this.handleEffect(selectedRows)
      } else if (code === 'Invalid') {
        // 失效
        this.handleInvalid(selectedRows)
      } else if (code === 'Import') {
        // 导入
        this.handleImport()
      } else if (code === 'Delete') {
        // 删除
        this.handleDelete(selectedRows)
      } else if (code === 'Export') {
        // 导出
        this.handleExport()
      }
    },
    // 导出
    handleExport() {
      const params = {
        ...this.queryForm,
        page: {
          current: this.pageSettings.current,
          size: this.pageSettings.pageSize
        }
      }
      if (params.startDateStart) {
        params.startDateStart = utils.formatTime(new Date(params.startDateStart), 'YYYY-mm-01')
      }
      if (params.startDateEnd) {
        let endDate = moment(utils.formatTime(new Date(params.startDateEnd), 'YYYY-mm-01'))
          .add('month', 1)
          .add('days', -1)
        params.startDateEnd = moment(endDate).format('YYYY-MM-DD')
      }
      if (params.endDateStart) {
        params.endDateStart = utils.formatTime(new Date(params.endDateStart), 'YYYY-mm-01')
      }
      if (params.endDateEnd) {
        let endDate = moment(utils.formatTime(new Date(params.endDateEnd), 'YYYY-mm-01'))
          .add('month', 1)
          .add('days', -1)
        params.endDateEnd = moment(endDate).format('YYYY-MM-DD')
      }
      this.$API.customization.exportCustomization(params).then((res) => {
        this.$toast({
          type: 'success',
          content: this.$t('导出成功，请稍后！')
        })
        let fileName = utils.getHeadersFileName(res)
        utils.download({ fileName: fileName, blob: res.data })
      })
    },
    // 生效
    handleEffect(selectedRows) {
      // 校验数据为拟定状态
      if (selectedRows.some((i) => i.status !== 0)) {
        this.$toast({
          content: this.$t('所选数据存在非拟定状态数据'),
          type: 'warning'
        })
        return
      }
      this.$API.customization
        .batchEffectiveCustomization({ idList: selectedRows.map((i) => i.id) })
        .then((res) => {
          const { code } = res
          if (code === 200) {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.search()
          }
        })
    },
    // 失效
    handleInvalid(selectedRows) {
      // 校验数据为有效状态
      if (selectedRows.some((i) => i.status !== 1)) {
        this.$toast({
          content: this.$t('所选数据存在非有效状态数据'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        modal: () => import('@/components/inputDialog/index.vue'),
        data: {
          title: this.$t('失效提示')
        },
        success: (e) => {
          console.log('e.inputContent', e.inputContent)
          // e.inputContent是失效弹窗中输入的值
          this.$API.customization
            .batchInvalidCustomization({
              idList: selectedRows.map((i) => i.id),
              remark: e.inputContent
            })
            .then((res) => {
              const { code } = res
              if (code === 200) {
                this.$toast({
                  content: this.$t('操作成功'),
                  type: 'success'
                })
                this.search()
              }
            })
        }
      })
    },
    // 导入
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('上传/导入'),
          importApi: this.$API.customization.importCustomization,
          downloadTemplateApi: this.$API.customization.exportCustomizationTemplate
        },
        success: () => {
          // 导入之后刷新列表
          this.search()
        }
      })
    },
    // 删除
    handleDelete(selectedRows) {
      // 校验数据为拟定状态
      if (selectedRows.some((i) => i.status !== 0)) {
        this.$toast({
          content: this.$t('所选数据存在非拟定状态数据'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          this.$API.customization
            .batchDeleteCustomization({ idList: selectedRows.map((i) => i.id) })
            .then((res) => {
              const { code } = res
              if (code === 200) {
                this.$toast({
                  content: this.$t('删除成功'),
                  type: 'success'
                })
                this.search()
              }
            })
        }
      })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      if (this.isEdit === true) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('存在未保存的数据，是否继续当前操作')
          },
          success: () => {
            this.pageSettings.current = currentPage
            this.search('pageSettings')
          }
        })
        return
      }
      this.pageSettings.current = currentPage
      this.search('pageSettings')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      if (this.isEdit === true) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('存在未保存的数据，是否继续当前操作')
          },
          success: () => {
            this.pageSettings.pageSize = pageSize
            this.search('pageSettings')
          }
        })
        return
      }
      this.pageSettings.pageSize = pageSize
      this.search()
    },
    edit(row) {
      if (this.isEdit === true) {
        this.$toast({
          content: this.$t('请先保存或取消编辑当前数据'),
          type: 'warning'
        })
        return
      }
      this.isEdit = true
      this.cacheRow = JSON.parse(JSON.stringify(row))
      this.$set(row, 'editable', true)
    },
    cancelEdit(row, index) {
      this.$set(row, 'editable', false)
      this.isEdit = false
      this.$set(this.dataSource, index, this.cacheRow)
      if (!row.createUserName) {
        this.dataSource.splice(index, 1)
      }
      // 重新请求数据
      // this.search()
    },
    async save(row) {
      // 组件tooltip有问题，暂时先用吐司代替
      if (!row.orgId) {
        this.$toast({
          content: this.$t('请选择工厂'),
          type: 'warning'
        })
        return
      }
      if (!row.itemCode) {
        this.$toast({
          content: this.$t('请选择物料编码'),
          type: 'warning'
        })
        return
      }
      if (!row.supplierCode) {
        this.$toast({
          content: this.$t('请选择供应商编码'),
          type: 'warning'
        })
        return
      }
      if (!row.quotaPercent) {
        this.$toast({
          content: this.$t('请输入制定配额'),
          type: 'warning'
        })
        return
      }
      if (!row.startDate) {
        this.$toast({
          content: this.$t('请选择开始月份'),
          type: 'warning'
        })
        return
      }
      if (!row.endDate) {
        this.$toast({
          content: this.$t('请选择结束月份'),
          type: 'warning'
        })
        return
      }
      const params = {
        ...row
      }
      if (params.id.includes('row_')) {
        delete params.id
      }
      this.$API.customization.saveCustomization(params).then((res) => {
        const { code } = res
        if (code === 200) {
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
          // 重新请求数据
          this.search()
        }
      })
    },
    reset() {
      console.log('重置')
      if (this.isEdit === true) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('存在未保存的数据，是否继续当前操作')
          },
          success: () => {
            this.queryForm = {
              siteCode: '', // 组织
              categoryId: '', // 品类
              status: '', // 状态
              itemId: '', // 物料编码
              supplierId: '',
              startDateStart: null, // 开始月份起
              startDateEnd: null, // 开始月份止
              endDateStart: null, // 结束月份起
              endDateEnd: null // 结束月份止
            }
            this.$refs.itemCode.handleClear()
            this.$refs.supplierCode.handleClear()
            this.$refs.categoryId.handleClear()
            this.search()
          }
        })
        return
      }
      this.queryForm = {
        siteCode: '', // 组织
        categoryId: '', // 品类
        status: '', // 状态
        itemId: '', // 物料编码
        supplierId: '',
        startDateStart: null, // 开始月份起
        startDateEnd: null, // 开始月份止
        endDateStart: null, // 结束月份起
        endDateEnd: null // 结束月份止
      }
      this.$refs.itemCode.handleClear()
      this.$refs.supplierCode.handleClear()
      this.$refs.categoryId.handleClear()
      this.search()
    },
    handleSearch() {
      if (this.isEdit === true) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('存在未保存的数据，是否继续当前操作')
          },
          success: () => {
            this.search()
          }
        })
        return
      }
      this.search()
    },
    search(pageSettings) {
      console.log('search', this.queryForm)
      const params = {
        ...this.queryForm,
        page: {
          current: pageSettings ? this.pageSettings.current : 1,
          size: this.pageSettings.pageSize
        }
      }
      if (params.startDateStart) {
        params.startDateStart = utils.formatTime(new Date(params.startDateStart), 'YYYY-mm-01')
      }
      if (params.startDateEnd) {
        let endDate = moment(utils.formatTime(new Date(params.startDateEnd), 'YYYY-mm-01'))
          .add('month', 1)
          .add('days', -1)
        params.startDateEnd = moment(endDate).format('YYYY-MM-DD')
      }
      if (params.endDateStart) {
        params.endDateStart = utils.formatTime(new Date(params.endDateStart), 'YYYY-mm-01')
      }
      if (params.endDateEnd) {
        let endDate = moment(utils.formatTime(new Date(params.endDateEnd), 'YYYY-mm-01'))
          .add('month', 1)
          .add('days', -1)
        params.endDateEnd = moment(endDate).format('YYYY-MM-DD')
      }
      this.loading = true
      this.$API.customization
        .getCustomizationList(params)
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            const total = res?.data?.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)
            this.dataSource = data.records
            this.isEdit = false
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    setTableEmpty(fieldsarr, row) {
      for (let i = 0; i < fieldsarr.length; i++) {
        this.$set(row, fieldsarr[i], '')
      }
    },
    handleFactoryChange(e, row) {
      // 工厂下拉变更
      if (row) {
        // 表格内的下拉选择
        // 重新选择时，需清空行上已填写的字段（物料、品类、供应商、采购开发、自制配额%、开始月份、结束月份）
        const fieldsarr = [
          'itemCode',
          'itemName',
          'categoryId',
          'categoryName',
          'supplierCode',
          'supplierName',
          'coder',
          'quotaPercent',
          'startDate',
          'endDate'
        ]
        row.orgId = e
        this.setTableEmpty(fieldsarr, row)
        for (let i = 0; i < this.factoryList.length; i++) {
          const element = this.factoryList[i]
          if (element.id === e) {
            // row.orgCode = element.orgCode
            // row.orgName = element.orgName
            this.$set(row, 'orgCode', element.orgCode)
            this.$set(row, 'orgName', element.orgName)
            return
          }
        }
      } else {
        // 表单内的下拉选择 使用的是mt组件api与vxe不同
        const { itemData } = e
        this.queryForm.siteCode = itemData.orgCode
      }
    },
    materialCodeChange(e, row) {
      // 物料编码变更
      if (row) {
        // 表格内物料编码变更
        // 重新选择时，需清空行上已填写的字段（品类、供应商、采购开发、自制配额%、开始月份、结束月份）
        const fieldsarr = [
          'itemName',
          'categoryId',
          'categoryName',
          'supplierCode',
          'supplierName',
          'coder',
          'quotaPercent',
          'startDate',
          'endDate'
        ]
        this.setTableEmpty(fieldsarr, row)
        const { id, itemCode, itemName, categoryResponse, categoryItemResponse } = e
        row.itemId = id
        row.itemCode = itemCode
        row.itemName = itemName
        // 带出品类信息
        if (categoryResponse) {
          row.categoryCode = categoryResponse.categoryCode
          row.categoryName = categoryResponse.categoryName
          row.categoryId = categoryResponse.id
        } else if (categoryItemResponse) {
          row.categoryCode = categoryItemResponse.categoryCode
          row.categoryName = categoryItemResponse.categoryName
          row.categoryId = categoryItemResponse.categoryId
        } else {
          row.categoryCode = e.categoryCode
          row.categoryName = e.categoryName
          row.categoryId = e.categoryId
        }
      } else {
        // 表单内物料编码变更
        this.queryForm.itemId = e.id
      }
    },
    categoryCodeChange(e) {
      // 品类变更
      this.queryForm.categoryId = e.id
    },
    supplierCodeChange(e, row) {
      // 供应商编码变更
      if (row) {
        // 表格内供应商编码变更
        // 重新选择时，需清空行上已填写的字段（自制配额%、开始月份、结束月份）
        const fieldsarr = ['supplierName', 'quotaPercent', 'startDate', 'endDate']
        this.setTableEmpty(fieldsarr, row)
        row.supplierId = e.id
        row.supplierCode = e.supplierCode
        row.supplierName = e.supplierName
      } else {
        // 表单内物料编码变更
        this.queryForm.supplierId = e.id
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.quato-customization {
  // margin-top: 24px;
  background: #fff;
  padding: 12px;
  border-radius: 8px;
  height: 100%;
  .button-group {
    display: flex;
    padding: 0 0 8px;
  }
}
.components-table-demo-nested {
  // ::v-deep.ant-table-body {
  //   overflow-x: auto !important;
  // }
  ::v-deep.red-color {
    color: red;
  }
}
</style>
