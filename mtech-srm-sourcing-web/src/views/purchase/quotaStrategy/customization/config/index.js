import { i18n } from '@/main.js'
import utils from '@/utils/utils'
const toolbar = [
  // { id: "Add", icon: "icon_solid_Createorder", title: i18n.t("新增") },
  // { id: "Stop", icon: "icon_solid_Cancel", title: i18n.t("取消") },
  // { id: "Submit", icon: "icon_solid_Submit", title: i18n.t("提交") },
  // { id: "Import", icon: "icon_solid_Createorder", title: i18n.t("导入") },
]
const materialColumn = [
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    cssClass: 'field-content',
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    cssClass: 'field-content'
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]
const categoryColumn = [
  // {
  //   type: 'checkbox',
  //   width: '60'
  // },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    cssClass: 'field-content'
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]
const supplierColumn = [
  // {
  //   type: 'checkbox',
  //   width: '60'
  // },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    cssClass: 'field-content'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  }
]
export const pageConfig = (url, columnType, params) => {
  let columnData = []
  let gridId = {
    material: '2e6496f6-7c6c-405f-803e-a2aa48feecc2',
    category: '2b9b9f74-564f-4a93-a008-0295d7d13e60',
    supplier: '29710801-8c71-41dc-b9ee-eed11d26c4b6'
  }
  if (columnType === 'material') {
    columnData = materialColumn
  } else if (columnType === 'category') {
    columnData = categoryColumn
  } else {
    columnData = supplierColumn
  }
  let asyncConfig = {
    url
  }
  if (params) {
    asyncConfig.params = params
  }
  return [
    {
      toolbar,
      useToolTemplate: false,
      gridId: gridId[columnType],
      grid: {
        allowFiltering: true,
        columnData,
        allowSorting: false,
        allowSelection: true,
        selectionSettings: {
          type: 'Multiple',
          mode: 'Row'
        },
        asyncConfig
      }
    }
  ]
}

export const vxeColumns = [
  {
    type: 'checkbox',
    width: '50'
  },
  {
    // title: i18n.t('工厂'),
    field: 'orgId',
    width: 150,
    align: 'left',
    scopedSlots: { customRender: 'orgId' },
    slots: { header: 'factoryHead', default: 'orgIdDefault' }
  },
  {
    title: i18n.t('工厂名称'),
    field: 'orgName',
    width: 200,
    align: 'left'
    // editConfig: {
    //   controlfield: 'editable',
    //   editorProps: { component: 'a-input', disabled: true, placeholder: i18n.t('请输入工厂名称') }
    // }
  },
  {
    // title: i18n.t('物料编码'),
    field: 'itemCode',
    width: 200,
    align: 'left',
    // editConfig: {
    //   // controlfield: 'editable',
    //   rules: () => [{ required: true, message: i18n.t('物料编码'), triggle: 'input' }]
    // },
    slots: { header: 'itemCodeHead', default: 'itemCodeDefault' }
  },
  {
    title: i18n.t('物料名称'),
    field: 'itemName',
    width: 150,
    align: 'left',
    ellipsis: true
    // editConfig: {
    //   controlfield: 'editable',
    //   editorProps: { component: 'a-input', disabled: true, placeholder: i18n.t('请输入物料名称') }
    // }
  },
  {
    title: i18n.t('品类'),
    field: 'categoryName',
    width: 150,
    align: 'left'
    // editConfig: {
    //   controlfield: 'editable',
    //   editorProps: { component: 'a-input', disabled: true, placeholder: i18n.t('请输入品类') }
    // }
  },
  {
    // title: i18n.t('供应商编码'),
    field: 'supplierCode',
    width: 200,
    align: 'left',
    // scopedSlots: { customRender: 'supplierCode' },
    // slots: { title: 'supplierCodeHead' }
    slots: { header: 'supplierCodeHead', default: 'supplierCodeDefault' }
  },
  {
    title: i18n.t('供应商名称'),
    field: 'supplierName',
    width: 150,
    align: 'left'
    // editConfig: {
    //   controlfield: 'editable',
    //   editorProps: {
    //     component: 'a-input',
    //     disabled: true,
    //     placeholder: i18n.t('请输入供应商名称')
    //   }
    // }
  },
  // { title: i18n.t('采购开发'), dataIndex: 'coder', field: 'coder', align: 'left' },
  {
    // title: '制定配额%',
    field: 'quotaPercent',
    width: 150,
    align: 'left',
    // slots: { title: 'quotaPercentHead' },
    // scopedSlots: { customRender: 'quotaPercent' }
    slots: { header: 'quotaPercentHead', default: 'quotaPercentDefault' }
    // editConfig: {
    //   controlfield: 'editable',
    //   editorProps: {
    //     component: 'a-input-number',
    //     min: 1,
    //     max: 100,
    //     step: 1,
    //     precision: 0,
    //     placeholder: i18n.t('请输入制定配额')
    //   },
    //   rules: () => [
    //     { required: true, type: 'number', message: i18n.t('制定配额且须是数值类型'), triggle: 'blur' }
    //   ]
    // }
  },
  {
    // title: i18n.t('开始月份'),
    field: 'startDate',
    width: 150,
    align: 'left',
    // slots: { title: 'startDateHead' },
    // scopedSlots: { customRender: 'startDate' }
    slots: { header: 'startDateHead', default: 'startDateDefault' }
    // editConfig: {
    //   controlfield: 'editable',
    //   editorProps: {
    //     component: 'a-month-picker',
    //     valueFormat: 'YYYY-MM',
    //     placeholder: i18n.t('请选择日期'),
    //     disabledDate: function (startValue) {
    //       const endValue = this.endValue
    //       if (!startValue || !endValue) {
    //         return false
    //       }
    //       return startValue.valueOf() < new Date().getTime()
    //     }
    //   },
    //   rules: () => [
    //     {
    //       required: true,
    //       message: i18n.t('日期不能为空'),
    //       triggle: 'change'
    //     },
    //     {
    //       type: 'date',
    //       message: i18n.t('必须为日期格式'),
    //       triggle: 'change'
    //     }
    //   ]
    // }
  },
  {
    // title: i18n.t('结束月份'),
    field: 'endDate',
    width: 150,
    align: 'left',
    // slots: { title: 'endDateHead' },
    // scopedSlots: { customRender: 'endDate' }
    slots: { header: 'endDateHead', default: 'endDateDefault' }
    // editConfig: {
    //   controlfield: 'editable',
    //   editorProps: {
    //     component: 'a-month-picker',
    //     valueFormat: 'YYYY-MM',
    //     placeholder: i18n.t('请选择日期')
    //   },
    //   rules: () => [
    //     {
    //       required: true,
    //       message: i18n.t('日期不能为空'),
    //       triggle: 'change'
    //     },
    //     {
    //       type: 'date',
    //       message: i18n.t('必须为日期格式'),
    //       triggle: 'change'
    //     }
    //   ]
    // }
  },
  {
    title: i18n.t('状态'),
    field: 'status',
    align: 'left',
    formatter: ({ cellValue }) => {
      const statusOptions = utils.getQuotaDict('QUOTA_STRATEGY_SHORT_STATUS') || []
      let item = statusOptions.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  },
  { title: i18n.t('创建人'), field: 'createUserName', align: 'left' },
  { title: i18n.t('创建时间'), field: 'createTime', align: 'left' },
  { title: i18n.t('生效日期'), field: 'effectiveDate', align: 'left' },
  { title: i18n.t('失效日期'), field: 'invalidDate', align: 'left' },
  {
    title: i18n.t('失效原因'),
    width: 200,
    field: 'invalidReason',
    align: 'left'
  },
  { title: i18n.t('最后更新人'), field: 'updateUserName', align: 'left' },
  { title: i18n.t('操作'), fixed: 'right', slots: { default: 'operationDefault' } }
]
