<template>
  <div>
    <div class="header-box">
      <!-- 右侧各种操作按钮 -->
      <span class="header-title">{{ getTitle() }}</span>
      <div>
        <mt-button
          v-if="pageType !== 'detail'"
          css-class="e-flat"
          :is-primary="true"
          @click="save()"
          >{{ $t('保存') }}</mt-button
        >
        <mt-button
          v-if="pageType !== 'detail'"
          css-class="e-flat"
          :is-primary="true"
          @click="submit()"
          >{{ $t('提交') }}</mt-button
        >
        <mt-button css-class="e-flat" :is-primary="true" @click="backTo">{{
          $t('返回')
        }}</mt-button>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-form">
      <mt-form ref="ruleForm" :model="addForm" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="orgId" :label="$t('工厂代码')">
          <mt-select
            v-if="pageType !== 'detail'"
            v-model="addForm.orgId"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="factoryList"
            :disabled="pageType === 'detail'"
            :fields="{ text: 'orgCode', value: 'id' }"
            :placeholder="$t('请选择工厂代码')"
            @change="factoryChange"
          ></mt-select>
          <mt-input v-else v-model="addForm.orgCode" disabled />
        </mt-form-item>
        <mt-form-item prop="orgName" :label="$t('工厂名称')">
          <mt-input v-model="addForm.orgName" disabled :placeholder="$t('请输入工厂名称')" />
        </mt-form-item>
        <mt-form-item prop="typeCode" :label="$t('例外类型')">
          <mt-select
            v-model="addForm.typeCode"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :disabled="pageType === 'detail'"
            :show-clear-button="true"
            :data-source="supplierOptions"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('例外类型')"
            @change="typeChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商编码')">
          <magnifier-input
            v-if="pageType !== 'detail'"
            :disabled="pageType === 'detail' || !this.addForm.orgId"
            :default-value="addForm.supplierCode"
            :config="supplierDialogCofig"
            @change="supplierCodeChange($event)"
          ></magnifier-input>
          <mt-input v-else v-model="addForm.supplierCode" disabled />
        </mt-form-item>
        <mt-form-item prop="supplierName" :label="$t('供应商名称')">
          <mt-input v-model="addForm.supplierName" disabled :placeholder="$t('请输入供应商名称')" />
        </mt-form-item>
        <mt-form-item prop="dimensionCode" :label="$t('例外维度')">
          <mt-select
            v-model="addForm.dimensionCode"
            float-label-type="Never"
            :disabled="pageType === 'detail'"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="dimensionCodeList"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('例外维度')"
            @change="dimensionTypeChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="startDate" :label="$t('开始月份')">
          <mt-date-picker
            v-model="addForm.startDate"
            :disabled="pageType === 'detail'"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :separator="$t('至')"
            :placeholder="$t('选择开始月份')"
            @change="startDateChange"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="endDate" :label="$t('结束月份')">
          <mt-date-picker
            v-model="addForm.endDate"
            :disabled="pageType === 'detail'"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :separator="$t('至')"
            :placeholder="$t('选择结束月份')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item v-if="pageType" prop="code" :label="$t('配额例外单号')">
          <mt-input v-model="addForm.code" disabled :placeholder="$t('请选择配额例外单号')" />
        </mt-form-item>
        <mt-form-item v-if="pageType" prop="status" :label="$t('单据状态')">
          <mt-select
            v-model="addForm.status"
            float-label-type="Never"
            disabled
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="statusOptions"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('单据状态')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item v-if="pageType" prop="createUserName" :label="$t('创建人')">
          <mt-input v-model="addForm.createUserName" disabled :placeholder="$t('请选择创建人')" />
        </mt-form-item>
        <mt-form-item v-if="pageType" prop="createTime" :label="$t('创建时间')">
          <mt-date-picker
            v-model="addForm.createTime"
            disabled
            :open-on-focus="true"
            float-label-type="Never"
            width="100%"
            :placeholder="$t('请选择创建时间')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')">
          <mt-input
            v-model="addForm.remark"
            :disabled="pageType === 'detail'"
            type="text"
            :placeholder="$t('请输入备注')"
          />
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import magnifierInput from '@/components/magnifierInput'
import { pageConfig, statusOptions, supplierOptions } from './../../config'
import utils from '@/utils/utils.js'
import { throttle } from 'lodash'
export default {
  components: {
    magnifierInput
  },
  props: {
    pageType: {
      // 判断当前页面的类型，add;edit;detail
      type: String,
      require: true,
      default: ''
    },
    formConfig: {
      type: Object,
      require: true,
      default: () => {
        return {
          orgId: '', // 组织
          typeCode: '', // 例外类型
          supplierId: '', // 供应商编码
          supplierCode: '', // 供应商编码
          supplierName: '', // 供应商名称
          dimensionCode: '', // 例外维度
          startDate: '', // 开始月份
          endDate: '', // 结束月份
          code: '', // 配额例外单号
          status: 0, // 单据状态
          createUserName: '', // 创建人
          createTime: '', // 创建时间
          remark: '', // 备注
          file: '' // 文件
        }
      }
    }
  },
  data() {
    return {
      factoryList: [], // 组织下拉列表的配置信息
      dimensionCodeList: utils.getQuotaDict('QUOTA_STRATEGY_LW_WD') || [], // 组织下拉列表的配置信息
      // 供应商放大镜弹窗配置
      // supplierDialogCofig: {
      //   pageConfig: pageConfig('/masterDataManagement/tenant/supplier/pagedQueryByOrgCode', 'supplier'),
      //   text: 'supplierCode',
      //   value: 'supplierCode'
      // },
      supplierOptions,
      statusOptions,
      addForm: {
        orgId: '', // 组织
        orgCode: '', // 组织编码
        orgName: '', // 组织名称
        typeCode: '', // 例外类型
        supplierId: '', // 供应商编码
        supplierCode: '', // 供应商编码
        supplierName: '', // 供应商名称
        dimensionCode: '', // 例外维度
        startDate: '', // 开始月份
        endDate: '', // 结束月份
        code: '', // 配额例外单号
        status: 0, // 单据状态
        createUserName: '', // 创建人
        createTime: '', // 创建时间
        remark: '' // 备注
      },
      rules: {
        orgId: [{ required: true, message: this.$t('请选择工厂代码'), trigger: 'blur' }],
        typeCode: [{ required: true, message: this.$t('请选择例外类型'), trigger: 'blur' }],
        supplierCode: [
          {
            required: true,
            message: this.$t('请选择供应商编码'),
            trigger: 'blur'
          }
        ],
        dimensionCode: [
          {
            required: true,
            message: this.$t('请选择例外维度'),
            trigger: 'blur'
          }
        ],
        startDate: [
          {
            required: true,
            message: this.$t('请选择开始月份'),
            trigger: 'blur'
          }
        ],
        endDate: [{ required: true, message: this.$t('请选择结束月份'), trigger: 'blur' }]
      },
      isAllowChang: false // 解决页面初始化时触犯的change事件
    }
  },
  created() {
    // 组织下拉列表
    this.getFactoryList()
    // 请求组织列表的接口并赋值给templateOrgOptions
  },
  methods: {
    setFormEmpty(fieldsarr) {
      for (let i = 0; i < fieldsarr.length; i++) {
        this.$set(this.addForm, fieldsarr[i], '')
      }
      this.$emit('clearTable')
    },
    factoryChange(e) {
      const { itemData } = e
      if (itemData && itemData.id) {
        this.addForm.orgId = itemData.id // 供应商编码
        this.addForm.orgCode = itemData.orgCode // 供应商编码
        this.addForm.orgName = itemData.orgName // 供应商名称
      } else {
        this.addForm.orgId = '' // 供应商编码
        this.addForm.orgCode = '' // 供应商编码
        this.addForm.orgName = '' // 供应商名称
      }
      // 抛出orgId给品类接口
      let flag = false
      if (this.addForm.dimensionCode === 'BFLW') {
        flag = true
      }
      this.$emit('handleChange', { flag, orgId: this.addForm.orgId })
      // 若重新选择，需自动清空后面字段内容（例外类型、供应商、例外维度、品类、开始月份、结束月份）
      if (this.isAllowChang) {
        const fieldsarr = [
          'typeCode',
          'supplierId',
          'supplierCode',
          'supplierName',
          'dimensionCode',
          'startDate',
          'endDate'
        ]
        this.setFormEmpty(fieldsarr)
      }
    },
    typeChange() {
      // 若重新选择，需自动清空后面字段内容（供应商、例外维度、品类、开始月份、结束月份）
      if (this.isAllowChang) {
        const fieldsarr = [
          'supplierId',
          'supplierCode',
          'supplierName',
          'dimensionCode',
          'startDate',
          'endDate'
        ]
        this.setFormEmpty(fieldsarr)
      }
    },
    dimensionTypeChange(e) {
      const { itemData } = e
      let flag = false
      if (itemData && itemData.dictCode === 'BFLW') {
        flag = true
        if (this.pageType) {
          this.$emit('getItemList')
        }
      }
      this.$emit('handleChange', { flag, orgId: this.addForm.orgId })
      // 若重新选择，需自动清空后面字段内容（品类、开始月份、结束月份）
      if (this.isAllowChang) {
        const fieldsarr = ['startDate', 'endDate']
        this.setFormEmpty(fieldsarr)
      }
    },
    supplierCodeChange(e) {
      this.addForm.supplierId = e.id // 供应商编码
      this.addForm.supplierCode = e.supplierCode // 供应商编码
      this.addForm.supplierName = e.supplierName // 供应商名称
      // 若重新选择，需自动清空后面字段内容（例外维度、品类、开始月份、结束月份）
      if (this.isAllowChang) {
        const fieldsarr = ['dimensionCode', 'startDate', 'endDate']
        this.setFormEmpty(fieldsarr)
      }
      this.$refs.ruleForm.validateField('supplierCode')
    },
    startDateChange() {
      if (this.isAllowChang) {
        this.addForm.endDate = ''
      }
    },
    getFactoryList() {
      this.$API.customization.getPermissionSiteList({}).then((res) => {
        this.factoryList = res.data
        if (!this.addForm.orgId && this.factoryList && this.factoryList.length === 1) {
          this.addForm.orgId = this.factoryList[0]['id']
          this.addForm.orgCode = this.factoryList[0]['orgCode'] // 供应商编码
          this.addForm.orgName = this.factoryList[0]['orgName'] // 供应商名称
        }
      })
    },
    save: throttle(function () {
      if (this.addForm.startDate && new Date(this.addForm.startDate) <= new Date()) {
        this.$toast({
          content: this.$t('开始月份必须大于当前月份'),
          type: 'warning'
        })
        return false
      }
      if (
        this.addForm.endDate &&
        new Date(this.addForm.endDate) < new Date(this.addForm.startDate)
      ) {
        this.$toast({
          content: this.$t('结束月份必须大于等于开始月份'),
          type: 'warning'
        })
        return false
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const params = {
            ...this.addForm,
            startDate: utils.formatTime(new Date(this.addForm.startDate), 'YYYY-mm-dd'),
            endDate: utils.formatTime(new Date(this.addForm.endDate), 'YYYY-mm-dd')
          }
          this.$emit('save', params)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }, 1000),
    submit: throttle(function () {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const params = {
            ...this.addForm,
            startDate: utils.formatTime(new Date(this.addForm.startDate), 'YYYY-mm-dd'),
            endDate: utils.formatTime(new Date(this.addForm.endDate), 'YYYY-mm-dd')
          }
          this.$emit('submit', params)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }, 1000),
    getTitle() {
      let title = this.$t('配额例外')
      // 判断当前页面的类型，add;edit;detail
      switch (this.pageType) {
        case 'edit':
          return this.$t('编辑') + title
        case 'detail':
          return this.$t('查看') + title
        default:
          return this.$t('新增') + title
      }
    },
    backTo() {
      this.$bus.$emit('refreshQuotaExcludeList')
      this.$router.push({
        path: '/sourcing/quota-strategy/exclude'
      })
    }
  },
  computed: {
    supplierDialogCofig() {
      const params = {}
      if (this.addForm.orgCode) {
        params.siteCode = this.addForm.orgCode
        // params.orgType = 'site'
      }
      return {
        pageConfig: pageConfig(
          '/masterDataManagement/tenant/supplier/pagedQueryBySiteCode',
          'supplier',
          params
        ),
        text: 'supplierCode',
        value: 'supplierCode'
      }
    }
  },
  watch: {
    /* 监听传进来得内容 */
    formConfig: {
      handler(newVal) {
        this.isAllowChang = false
        this.addForm = {
          ...this.addForm,
          ...newVal
        }
        if (this.addForm.startDate && this.addForm.endDate) {
          this.addForm.startDate = utils.formatTime(new Date(this.addForm.startDate), 'YYYY-mm')
          this.addForm.endDate = utils.formatTime(new Date(this.addForm.endDate), 'YYYY-mm')
        }
        // 加定时器是处理 初始赋值时会触发mt表单组件的change事件导致表单置空逻辑触发回显异常
        setTimeout(() => {
          this.isAllowChang = true
        }, 2000)
      },
      immediate: true,
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
.header-box {
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 20px;
  .header-title {
    color: #292929;
    font-family: PingFangSC;
    font-size: 20px;
    font-weight: 600;
  }
}
.main-form {
  /deep/ .mt-form-item {
    width: calc(25% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-top: 5px;
    margin-right: 20px;

    .full-width {
      width: calc(100% - 20px) !important;
    }
    .e-ddt .e-ddt-icon::before {
      content: '\e36a';
      font-size: 16px;
    }
    .mt-form-item-topLabel {
      .div-auth {
        background: #e3e1e1;
        height: 35px;
        display: flex;
        align-items: center;
        padding: 0 10px;
        color: #0f0f0f;
        font-size: 12px;
        cursor: pointer;
        border-radius: 5px;
      }
    }
  }
  .check-area {
    transform: translateY(10px);
  }
}
</style>
