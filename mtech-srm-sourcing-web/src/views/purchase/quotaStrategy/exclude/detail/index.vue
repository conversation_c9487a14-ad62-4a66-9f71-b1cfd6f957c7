<template>
  <div class="template-container">
    <!--  表单区域--->
    <templateForm
      :form-config="formConfig"
      :page-type="pageType"
      @save="save"
      @submit="submit"
      @handleChange="handleChange"
      @clearTable="clearTable"
      @getItemList="getItemList"
    />
    <!-----   物料选择区域  ------->
    <template v-if="isShowTable && orgId">
      <!----  表格  --------->
      <div v-if="pageType !== 'detail'" class="button-group">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable })"
          >{{ item.name }}</vxe-button
        >
      </div>
      <vxe-grid
        ref="xTable"
        :columns="columns"
        :data="dataSource"
        :edit-config="{
          trigger: pageType !== 'detail' ? 'click' : '',
          mode: 'row',
          showStatus: true,
          autoClear: false
        }"
      >
        <template #categoryCodeEdit="{ row }">
          <magnifier-input
            :disabled="pageType === 'detail'"
            :default-value="row.categoryCode"
            :config="categoryDialogCofig"
            @change="categoryCodeChange($event, row)"
          ></magnifier-input>
        </template>
      </vxe-grid>
      <!-- 分页 -->
      <mt-page
        ref="pageRef"
        class="flex-keep custom-page"
        :page-settings="pageSettings"
        :total-pages="pageSettings.totalPages"
        @currentChange="handleCurrentChange"
        @sizeChange="handleSizeChange"
      />
    </template>
  </div>
</template>

<script>
import magnifierInput from '@/components/magnifierInput'
import { columns } from './config'
import { pageConfig } from './../config'
export default {
  components: {
    templateForm: require('./components/templateForm.vue').default,
    magnifierInput
  },
  data() {
    return {
      toolbar: [
        {
          code: 'Add',
          name: this.$t('新增'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Delete',
          name: this.$t('删除'),
          icon: 'el-icon-delete',
          status: 'info'
        }
      ],
      columns,
      formConfig: {}, // 表单部分回显
      dataSource: [],
      pageSettings: {
        pageCount: 5,
        pageSize: 20, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [10, 20, 50, 100]
      },
      // 品类放大镜弹窗配置
      // categoryDialogCofig: {
      //   // pageConfig: pageConfig(
      //   //   '/masterDataManagement/tenant/permission/queryCategories',
      //   //   'category'
      //   // ),
      //   // text: 'categoryName',
      //   // value: 'categoryCode'
      // },
      isShowTable: false,
      isEdit: false,
      orgId: ''
    }
  },
  methods: {
    getItemList() {
      const params = {
        id: this.detailId,
        page: {
          current: this.pagination.current,
          size: this.pagination.pageSize
        }
      }
      this.$API.exclude.getExcludeItemList(params).then((res) => {
        const { code, data } = res
        if (code === 200) {
          const total = res?.data?.total || 0
          this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
          this.pageSettings.totalRecordsCount = Number(total)
          this.dataSource = data.records
          this.isEdit = false
        }
      })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      if (this.isEdit) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t(`存在未保存的数据，是否继续分页?`)
          },
          success: () => {
            this.pageSettings.current = currentPage
            this.getItemList()
          }
        })
      } else {
        this.pageSettings.current = currentPage
        this.getItemList()
      }
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      if (this.isEdit) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t(`存在未保存的数据，是否继续分页?`)
          },
          success: () => {
            this.pageSettings.pageSize = pageSize
            this.getItemList()
          }
        })
      } else {
        this.pageSettings.pageSize = pageSize
        this.getItemList()
      }
    },
    getDetailData(detailId) {
      let id = this.detailId
      if (detailId) {
        id = detailId
      }
      this.$API.exclude.getExcludeDetail({ id }).then((res) => {
        const { code, data } = res
        if (code === 200) {
          // 表单数据
          this.formConfig = JSON.parse(JSON.stringify(data))
          if (this.formConfig.dimension === 'BFFX') {
            this.getItemList()
          }
          // 表格数据
          // this.dataSource = data.detailSaveRequests || []
        }
      })
    },
    handleChange(e) {
      const { flag, orgId } = e
      this.isShowTable = flag
      this.orgId = orgId
    },
    clearTable() {
      this.dataSource = []
    },
    // 表格上方按钮点击事件
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRows = $grid.getCheckboxRecords() // 表格勾选行的内容
      if (code === 'Add') {
        this.handleAdd()
      }
      if (code === 'Delete' && selectedRows.length <= 0) {
        this.$toast({
          content: this.$t('请勾选需要操作的数据'),
          type: 'warning'
        })
        return
      }
      if (code !== 'Add' && this.dataSource.some((i) => !i.id || i.id.includes('row_'))) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('存在未保存的数据，是否继续当前操作')
          },
          success: () => {
            if (code === 'Delete') {
              // 删除
              this.handleDelete(selectedRows)
            }
          }
        })
        return
      }
      if (code === 'Delete') {
        // 删除
        this.handleDelete(selectedRows)
      }
    },
    // 表格底部添加行
    handleAdd() {
      if (this.dataSource.length === 40) {
        this.$toast({
          content: this.$t('超过编辑条数限制，请先保存当前数据后再进行新增操作'),
          type: 'warning'
        })
      }
      const obj = {
        categoryId: '',
        categoryCode: '',
        categoryName: ''
      }
      this.dataSource.push(obj)
      if (this.dataSource.length > this.pageSettings.pageSize) {
        this.pageSettings.pageSize++
      }
      this.pageSettings.total++
      this.isEdit = true
    },
    // 表格勾选删除
    handleDelete(selectedRows) {
      if (!selectedRows.length) {
        this.$toast({
          content: this.$t('请勾选需要删除的数据'),
          type: 'warning'
        })
        return
      }
      const deleteList = []
      // 遍历已勾选的数据 有id的就是线上数据需要调删除接口
      const arr = this.dataSource.filter((i) => {
        return !selectedRows.some((j) => {
          if (j && j.id && !j.id.includes('row_')) {
            deleteList.push(j.id)
          }
          return j._X_ROW_KEY === i._X_ROW_KEY
        })
      })
      if (deleteList.length) {
        const params = {
          code: this.formConfig.code,
          detailIdList: Array.from(new Set(deleteList)), // 去个重
          id: this.detailId
        }
        this.$API.exclude.batchDeleteExcludeItemList(params).then((res) => {
          const { code } = res
          if (code === 200) {
            this.afterDeleteEvent(arr)
          }
        })
        return
      }
      // 兼容未勾选数据的情况
      this.afterDeleteEvent(arr)
    },
    afterDeleteEvent(arr) {
      this.$toast({
        content: this.$t('删除成功'),
        type: 'success'
      })
      this.pageSettings.total = this.pageSettings.total - (this.dataSource.length - arr.length)
      this.pageSettings.pageSize =
        this.pageSettings.pageSize - (this.dataSource.length - arr.length)
      this.dataSource = arr
    },
    // 物料编码变更
    categoryCodeChange(e, row) {
      this.isEdit = true
      row.categoryId = e.id
      row.categoryCode = e.categoryCode
      row.categoryName = e.categoryName
      if (this.detailId) {
        row.excludeId = this.detailId
      }
    },
    getParams(addForm) {
      console.log('addform', addForm, this.dataSource)
      // 接口要求部分例外时品类为必填
      if (addForm.dimension === 'BFFX' && this.dataSource.length === 0) {
        this.$toast({
          content: this.$t('例外维度为部分例外时品类不可为空'),
          type: 'warning'
        })
        return false
      }
      if (
        this.dataSource.length != 0 &&
        this.dataSource.some((i) => !i.categoryCode && !i.categoryName)
      ) {
        this.$toast({
          content: this.$t('存在未输入完成的品类数据'),
          type: 'warning'
        })
        return false
      }
      const params = {
        ...addForm
      }
      if (params.dimensionCode === 'BFLW') {
        params.detailList = this.dataSource
      }
      return params
    },
    save(addForm) {
      let params = {}
      if (this.getParams(addForm)) {
        params = this.getParams(addForm)
      } else {
        return false
      }
      this.$API.exclude.saveExclude(params).then((res) => {
        const { code, data } = res
        if (code === 200) {
          // 保存后根据回调的id请求详情
          if (!this.pageType) {
            this.$router.push({
              query: {
                id: data,
                type: 'edit'
              }
            })
          }
          this.getDetailData(data)
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
          this.isEdit = false
        }
      })
    },
    submit(addForm) {
      let params = {}
      if (this.getParams(addForm)) {
        params = this.getParams(addForm)
      } else {
        return false
      }
      this.$API.exclude.submitExclude(params).then((res) => {
        const { code } = res
        if (code === 200) {
          // // 保存后根据回调的id请求详情
          // this.getDetailData(data.id)
          this.$toast({
            content: this.$t('提交成功'),
            type: 'success'
          })
          this.isEdit = false
          this.$bus.$emit('refreshQuotaExcludeList')
          this.$router.push({
            path: '/sourcing/quota-strategy/exclude'
          })
        }
      })
    }
  },
  computed: {
    pageType() {
      // 判断当前页面的类型，add;edit;detail
      return this.$route.query.type || ''
    },
    detailId() {
      // 非新增页面获取详情id
      return this.$route.query.id || ''
    },
    categoryDialogCofig() {
      const params = {}
      if (this.orgId) {
        params.organizationId = this.orgId
      }
      return {
        pageConfig: pageConfig(
          '/masterDataManagement/tenant/permission/queryCategories',
          'category',
          params
        ),
        text: 'categoryName',
        value: 'categoryCode'
      }
    }
  },
  created() {
    // 非新增页面获取详情
    if (this.pageType) {
      this.getDetailData()
    }
  }
}
</script>

<style lang="scss" scoped>
.template-container {
  background: #fff;
  height: 100%;
  overflow: auto;
  // margin-top: 20px;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;
  ::v-deep.e-btn.e-flat.e-primary {
    color: #6386ce !important;
    font: inherit !important;
  }
}
.button-group {
  display: flex;
  padding: 0 0 16px;
}
</style>
