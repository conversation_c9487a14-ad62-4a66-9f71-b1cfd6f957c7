<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div style="padding-top: 18px">
      <!----  表格  --------->
      <vxe-grid ref="xTable" :loading="loading" :columns="columns" :data="dataSource"> </vxe-grid>
      <!-- 分页 -->
      <mt-page
        ref="pageRef"
        class="flex-keep custom-page"
        :page-settings="pageSettings"
        :total-pages="pageSettings.totalPages"
        @currentChange="handleCurrentChange"
        @sizeChange="handleSizeChange"
      />
    </div>
  </mt-dialog>
</template>
<script>
export default {
  props: {
    // 父组件传值 数据集合
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    columns() {
      return this.modalData.columns
    },
    header() {
      return this.modalData.title
    }
  },
  data() {
    return {
      loading: false,
      pageSettings: {
        pageCount: 5,
        pageSize: 20, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [10, 20, 50, 100]
      },
      dataSource: [],
      buttons: [
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确认') }
        },
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        }
      ]
    }
  },
  methods: {
    getItemList() {
      const params = {
        id: this.modalData.id,
        page: {
          current: this.pageSettings.current,
          size: this.pageSettings.pageSize
        }
      }
      this.loading = true
      this.$API.exclude
        .getExcludeItemList(params)
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            const total = res?.data?.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)
            this.dataSource = data.records
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageSettings.current = currentPage
      this.getItemList('pageSettings')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.getItemList()
    },
    save() {
      this.$emit('confirm-function') // 确认
    },
    cancel() {
      this.$emit('cancel-function') // 确认
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.getItemList()
  }
}
</script>
<style lang="scss">
.dialog-content {
  padding: 32px 6px 0 6px !important;
}
/deep/ .mt-date-picker > .e-input-group {
  line-height: 32px;
}
/deep/ .icon-style {
  margin: -14px 0 0 42px;
  position: absolute;
}
/deep/ #describe {
  width: 100%;
}
</style>
