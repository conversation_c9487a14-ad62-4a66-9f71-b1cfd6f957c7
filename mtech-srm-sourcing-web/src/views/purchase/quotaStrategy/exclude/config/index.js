import { i18n } from '@/main.js'
import utils from '@/utils/utils'
const toolbar = [
  // { id: "Add", icon: "icon_solid_Createorder", title: i18n.t("新增") },
  // { id: "Stop", icon: "icon_solid_Cancel", title: i18n.t("取消") },
  // { id: "Submit", icon: "icon_solid_Submit", title: i18n.t("提交") },
  // { id: "Import", icon: "icon_solid_Createorder", title: i18n.t("导入") },
]
const materialColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    cssClass: 'field-content',
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  }
]
const categoryColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    cssClass: 'field-content'
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]
const supplierColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    cssClass: 'field-content'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  }
]
export const pageConfig = (url, columnType, params) => {
  let columnData = []
  let gridId = {
    material: '3f9d609e-1e75-406b-9741-f4f6c386c35d',
    category: '556f6486-ad39-4f58-8f9c-2200a35fe2d1',
    supplier: '05f52ef6-8330-4df4-a4c9-4bcfd196978b'
  }
  if (columnType === 'material') {
    columnData = materialColumn
  } else if (columnType === 'category') {
    columnData = categoryColumn
  } else {
    columnData = supplierColumn
  }
  return [
    {
      toolbar,
      useToolTemplate: false,
      gridId: gridId[columnType],
      grid: {
        allowFiltering: true,
        columnData,
        allowSorting: false,
        allowSelection: true,
        selectionSettings: {
          type: 'Multiple',
          mode: 'Row'
        },
        asyncConfig: { url, params }
      }
    }
  ]
}

export const materialColumns = [
  {
    title: i18n.t('物料编码'),
    dataIndex: 'itemCode',
    key: 'itemCode',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('物料名称'),
    dataIndex: 'itemName',
    key: 'itemName',
    minWidth: 150,
    align: 'left'
  }
]

export const categoryColumns = [
  {
    title: i18n.t('品类编码'),
    field: 'categoryCode',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('品类名称'),
    field: 'categoryName',
    minWidth: 150,
    align: 'left'
  }
]

export const statusOptions = utils.getQuotaDict('QUOTA_STRATEGY_STATUS') || []

export const supplierOptions = utils.getQuotaDict('QUOTA_STRATEGY_PELW_TYPE') || []

export const vxeColumns = [
  {
    type: 'checkbox',
    minWidth: '50'
  },
  {
    title: i18n.t('工厂代码'),
    field: 'orgCode',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('工厂名称'),
    field: 'orgName',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('配额例外单号'),
    field: 'code',
    minWidth: 200,
    align: 'left',
    slots: { default: 'quatoCode' }
  },
  {
    title: i18n.t('单据状态'),
    field: 'status',
    align: 'left',
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  },
  {
    title: i18n.t('例外类型'),
    field: 'typeCode',
    align: 'left',
    formatter: ({ cellValue }) => {
      let item = supplierOptions.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  },
  {
    title: i18n.t('供应商编码'),
    field: 'supplierCode',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('供应商名称'),
    field: 'supplierName',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('例外维度'),
    field: 'dimensionCode',
    minWidth: 150,
    align: 'left',
    formatter: ({ cellValue }) => {
      const options = utils.getQuotaDict('QUOTA_STRATEGY_LW_WD') || []
      let item = options.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  },
  {
    title: i18n.t('品类'),
    field: 'detailList',
    minWidth: 200,
    align: 'left',
    slots: { default: 'detailList' }
  },
  {
    title: i18n.t('开始月份'),
    field: 'startDate',
    minWidth: 150,
    align: 'left',
    formatter: ({ cellValue }) => {
      let date = ''
      if (cellValue && new Date(cellValue)) {
        date = utils.formatTime(new Date(cellValue), 'YYYY-mm')
      }
      return date
    }
  },
  {
    title: i18n.t('结束月份'),
    field: 'endDate',
    minWidth: 150,
    align: 'left',
    formatter: ({ cellValue }) => {
      let date = ''
      if (cellValue && new Date(cellValue)) {
        date = utils.formatTime(new Date(cellValue), 'YYYY-mm')
      }
      return date
    }
  },
  { title: i18n.t('创建人'), field: 'createUserName', align: 'left' },
  { title: i18n.t('创建时间'), field: 'createTime', align: 'left' },
  {
    title: i18n.t('操作'),
    field: 'operation',
    fixed: 'right',
    slots: { default: 'operationDefault' }
  }
]
