<template>
  <!-- 限制配额 -->
  <div class="full-height">
    <local-template-page
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
import { download, getHeadersFileName } from '@/utils/utils'
import localTemplatePage from '@/components/template-page'

export default {
  components: {
    localTemplatePage
  },
  data() {
    return {
      pageConfig: []
    }
  },
  created() {
    this.init()
    this.$bus.$on('refreshQuotaConstraintList', () => {
      this.refreshData()
    })
  },
  methods: {
    async init() {
      //获取到当前登录用户信息
      let userInfo = JSON.parse(sessionStorage.getItem('userInfo')) || {}
      let orgData = []
      const { code, data } = await this.$API.constraint.queryOrg()
      if (code === 200) {
        orgData = data
        Array.isArray(orgData) &&
          orgData.forEach((item) => {
            item.orgId = item.id
            item.cssClass = 'title-#9baac1'
          })
      }
      this.pageConfig = pageConfig(this.$API.constraint.queryBuilder, orgData, userInfo?.uid)
    },
    //新增
    handleAdd() {
      this.$router.push({
        name: `quota-strategy-constraint-detail`,
        query: {
          type: 'add',
          flashKey: new Date().getTime()
        }
      })
    },
    //创建新版本
    handleCreate(row) {
      if (row.status != 3) {
        this.$toast({ content: this.$t('审核通过的单据才能创建新版本'), type: 'warning' })
        return
      }
      const params = {
        ...row,
        version: 0
      }
      params.sourceCode = params.code
      params.sourceId = params.id
      params.code = null
      params.id = null
      // 根据当前单据生成新单号之后跳进详情页面
      this.$API.constraint.save(params).then((res) => {
        if (res.code === 200) {
          this.refreshData()
          this.$router.push({
            name: `quota-strategy-constraint-detail`,
            query: {
              type: 'update',
              id: res.data,
              flashKey: new Date().getTime()
            }
          })
        }
      })
    },
    //导出
    handleExport() {
      // 先要点击“查询”按钮才能取得queryBuilderRules
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules,
        defaultRules: [
          // {
          //   condition: 'and',
          //   field: 'rfx_item.rfxHeaderId',
          //   operator: 'equal',
          //   value: this.rfxId
          // }
        ].concat(queryBuilderRules.rules || [])
      } // 筛选条件
      this.$API.constraint.excelExport(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    //表格头部按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.grid.getSelectedRecords()
      if (_selectGridRecords.length != 1 && e.toolbar.id == 'CreateNew') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'CreateNew') {
        this.handleCreate(_selectGridRecords[0])
      } else if (e.toolbar.id == 'ExportFile') {
        this.handleExport()
      }
    },
    handleClickCellTool(e) {
      console.log('handleClickCellTool-', e)
      const { tool, data } = e
      if (tool.id === 'edit') {
        this.$router.push({
          name: `quota-strategy-constraint-detail`,
          query: {
            type: 'Edit',
            id: data.id,
            flashKey: new Date().getTime()
          }
        })
      } else if (tool.id === 'delete') {
        this.quotaConstraintDelete([data])
      } else if (tool.id === 'detail') {
        this.$router.push({
          name: `quota-strategy-constraint-detail`,
          query: {
            type: 'detail',
            id: data.id,
            flashKey: new Date().getTime()
          }
        })
      }
    },
    // 删除
    quotaConstraintDelete(rows) {
      // 调对应接口后刷新列表
      let idList = rows.map((v) => {
        return v.id
      })
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.constraint.batchDelete({ idList }).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.refreshData()
          })
        }
      })
    },
    refreshData() {
      this.$refs.templateRef.refreshCurrentGridData()
    }
  }
}
</script>

<style></style>
