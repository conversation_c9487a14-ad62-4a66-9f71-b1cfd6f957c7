<template>
  <div style="margin-top: 16px">
    <!----  表格  --------->
    <div v-if="isEnableStatus" class="button-group">
      <vxe-button
        v-for="item in toolbar"
        :key="item.code"
        :status="item.status"
        :icon="item.icon"
        size="small"
        @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable })"
        >{{ item.name }}</vxe-button
      >
    </div>
    <vxe-grid
      ref="xTable"
      :columns="categoryColumns"
      :data="dataSource"
      :edit-config="editConfig"
      max-height="300px"
    >
      <template #categoryNameEdit="{ row }">
        <MagnifierInput
          :config="categoryDialogCofig"
          :default-value="row.categoryCode"
          @change="
            (e) => {
              row.categoryCode = e.categoryCode
              row.categoryName = e.categoryName
              row.categoryId = e.categoryTypeId
            }
          "
        ></MagnifierInput>
      </template>
    </vxe-grid>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>
<script>
import MagnifierInput from '@/components/magnifierInput'
import { categoryColumnsCfg } from '../config/detail.js'
import { categoryConfig } from '../config/categorys.js'
export default {
  components: {
    // ScTable,
    // eslint-disable-next-line
    MagnifierInput
  },
  props: {
    formObject: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      editConfig: {
        trigger: 'click',
        mode: 'row',
        showStatus: true,
        autoClear: false
      },
      toolbar: [
        {
          code: 'Add',
          name: this.$t('新增'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Import',
          name: this.$t('导入'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Delete',
          name: this.$t('删除'),
          icon: 'el-icon-delete',
          status: 'info'
        }
      ],
      pageSettings: {
        pageCount: 5,
        pageSize: 20, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [10, 20, 50, 100]
      },
      dataSource: [], //table数据
      // 品类放大镜弹窗配置
      categoryDialogCofig: {
        pageConfig: categoryConfig('/sourcing/tenant/permission/queryCategorys'),
        text: 'categoryName',
        value: 'categoryCode'
      }
    }
  },
  computed: {
    categoryColumns() {
      return categoryColumnsCfg(this.categoryDialogCofig, MagnifierInput)
    },
    isEnableStatus() {
      return (
        (this.formObject.status == 0 || this.formObject.status == 2) && this.pageType !== 'detail'
      )
    },
    pageType() {
      return this.$route?.query?.type || 'add'
    },
    //检测dataSource是否含有optionType=add的数据 是否含有新增未保存的数据 删除 导入 翻页等需提示刷新后数据丢失
    hasNewItem() {
      return this.dataSource.some((item) => item.optionType === 'add')
    }
  },
  methods: {
    //获取表格数据
    getList(id) {
      let params = {
        id,
        page: {
          current: this.pageSettings.current ? this.pageSettings.current : 1,
          size: this.pageSettings.pageSize
        }
      }
      //请求列表接口
      this.$API.constraint.queryCategoryList(params).then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.dataSource = data.records
          const total = res?.data?.total || 0
          this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
          this.pageSettings.totalRecordsCount = Number(total)
        }
      })
    },
    //新增
    handleAdd() {
      const obj = {
        categoryCode: null,
        categoryName: null,
        optionType: 'add'
      }
      this.dataSource.unshift(obj)
    },
    // 删除
    handleDelete(selectedRows) {
      //选中判断
      if (selectedRows.length <= 0) {
        this.$toast({
          content: this.$t('请勾选需要操作的数据'),
          type: 'warning'
        })
        return
      }
      //如果sourceId有值且id为空 表示为创建新版本但未保存 提示保存后再删除
      if (this.formObject.sourceId && !this.formObject.id) {
        this.$toast({
          content: this.$t('创建新版本时，请先保存后再删除'),
          type: 'warning'
        })
        return
      }
      let tips = this.$t('确认删除选中的数据')
      const addRow = selectedRows.filter((item) => item.optionType === 'add') // 选中的 新增未保存的行
      const saveRow = selectedRows.filter((item) => item.optionType != 'add') // 选中的 已保存的行
      // 选中行 全部为新增未保存的行 询问确认删除选中的数据后, 直接删除
      if (addRow.length === selectedRows.length) {
        tips = this.$t('确认删除选中的数据')
      } else {
        //新增未保存的行数 < 全部新增未保存的行数 && 包含已保存的数据 询问 是否删除 删除会丢失未保存的新增数据，调用接口删除
        if (
          addRow.length < this.dataSource.filter((item) => item.optionType === 'add').length &&
          saveRow.length > 0
        ) {
          tips = this.$t('存在新增未保存的数据，删除后刷新后会导致未保存数据丢失，是否继续删除？')
        }
      }

      //删除前的确认
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: tips
        },
        success: () => {
          const isAllNewItem = selectedRows.every((item) => item.optionType === 'add')
          if (isAllNewItem) {
            selectedRows.forEach((item) => {
              this.dataSource.splice(this.dataSource.indexOf(item), 1)
            })
            return
          }
          const params = {
            code: this.formObject.code,
            detailIdList: selectedRows.filter((item) => item.type !== 'add').map((item) => item.id),
            id: this.formObject.id
          }
          this.$API.constraint.categoryBatchDelete(params).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.getList(this.formObject.id)
          })
        }
      })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      if (this.dataSource.some((i) => i.optionType === 'add')) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('表格中存在未保存的数据，翻页将会丢失当前修改的内容，是否确定？')
          },
          success: () => {
            this.pageSettings.current = currentPage
            this.getList(this.formObject.id) //刷新列表
          }
        })
      } else {
        this.pageSettings.current = currentPage
        this.getList(this.formObject.id) //刷新列表
      }
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      if (this.dataSource.some((i) => i.optionType === 'add')) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('表格中存在未保存的数据，翻页将会丢失当前修改的内容，是否确定？')
          },
          success: () => {
            this.pageSettings.pageSize = pageSize
            this.getList(this.formObject.id) //刷新列表
          }
        })
      } else {
        this.pageSettings.pageSize = pageSize
        this.getList(this.formObject.id) //刷新列表
      }
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      if (code === 'Add') {
        this.handleAdd()
      }
      if (code === 'Delete') {
        const selectedRows = $grid.getCheckboxRecords() // 表格勾选行的内容
        this.handleDelete(selectedRows)
      }
      if (code === 'Import') {
        this.handleImport()
      }
    },
    //导入
    handleImport() {
      //导入时 时必须先保存一次 获取id 才能导入
      if (this.pageType == 'add' && !this.formObject.id) {
        this.$toast({
          content: this.$t('请先保存表单'),
          type: 'success'
        })
        return
      }
      //如果sourceId有值且id为空 表示为创建新版本但未保存 提示保存后再删除
      if (this.formObject.sourceId && !this.formObject.id) {
        this.$toast({
          content: this.$t('创建新版本时，请先保存后再导入'),
          type: 'warning'
        })
        return
      }
      //如果导入时有未保存的数据 提示保存后再导入，否则会丢失未保存的数据
      if (this.hasNewItem) {
        this.$toast({
          content: this.$t(
            '存在新增未保存的数据，请先保存后再导入,否则会导入成功后刷新会丢失未保存的数据'
          ),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('上传/导入'),
          importApi: this.$API.constraint.excelimport,
          asyncParams: {
            headerId: this.formObject.id
          },
          downloadTemplateApi: this.$API.constraint.exportTpl
        },
        success: () => {
          //刷新列表
          this.getList(this.formObject.id)
        }
      })
    },
    async getTableData(isCheck = false) {
      if (isCheck && (!this.dataSource || !this.dataSource.length)) {
        this.$toast({
          content: this.$t('请先添加品类明细数据'),
          type: 'warning'
        })
        return null
      }
      if (this.dataSource.some((i) => !i.categoryCode || !i.categoryName)) {
        this.$toast({
          content: this.$t('表格中有未通过校验的数据'),
          type: 'warning'
        })
        return null
      }
      this.dataSource.forEach((item) => {
        item.opt = item.optionType === 'add' ? 0 : 1
      })
      return this.dataSource
    }
  }
}
</script>
<style lang="scss" scoped>
.button-group {
  display: flex;
  padding: 0 16px 16px;
}
</style>
