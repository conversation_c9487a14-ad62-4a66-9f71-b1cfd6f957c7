<template>
  <div class="supplier-box mt-flex">
    <div class="miam-container">
      <div class="header">
        <div class="title">{{ $t('限制配额维护') }}</div>
        <div class="operate-bar">
          <div v-if="isEnableStatus || isAddPage" class="op-item mt-flex" @click="save">
            {{ $t('保存') }}
          </div>
          <div v-if="isEnableStatus" class="op-item mt-flex" @click="submit">
            {{ $t('提交审批') }}
          </div>
          <div class="op-item mt-flex" @click="backToBusinessConfig">
            {{ $t('返回') }}
          </div>
        </div>
      </div>
      <!-- 顶部主要信息 -->
      <div class="mian-info">
        <div class="hr"></div>
        <mt-form ref="ruleForm" :model="formObject" :rules="formRules" :disabled="disabled">
          <mt-form-item prop="buId" :label="$t('事业部：')" label-style="top">
            <mt-select
              :fields="{ text: 'orgName', value: 'id' }"
              v-model="formObject.buId"
              float-label-type="Never"
              :allow-filtering="true"
              filter-type="Contains"
              :data-source="buList"
              :placeholder="$t('请选择事业部：')"
              @change="changeOrg"
              :disabled="disabled"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="title" :label="$t('限制配额标题')" label-style="top">
            <mt-input
              width="100%"
              type="text"
              v-model="formObject.title"
              :placeholder="$t('请输入限制配额标题')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="status" :label="$t('单据状态')" label-style="top">
            <mt-select
              :disabled="true"
              :fields="{ text: 'label', value: 'status' }"
              v-model="formObject.status"
              float-label-type="Never"
              :allow-filtering="true"
              :data-source="statusList"
              :placeholder="$t('请选择单据状态')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="code" :label="$t('限制配额单号')" label-style="top">
            <mt-input
              width="100%"
              type="text"
              :disabled="true"
              v-model="formObject.code"
              :placeholder="$t('请输入限制配额单号')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="sourceCode" :label="$t('历史单据号')" label-style="top">
            <mt-input
              width="100%"
              type="text"
              :disabled="true"
              v-model="formObject.sourceCode"
              :placeholder="$t('请输入历史单据号')"
            ></mt-input>
          </mt-form-item>
          <!-- <mt-form-item prop="version" :label="$t('当前版本号')" label-style="top">
            <mt-input
              width="100%"
              type="text"
              :disabled="true"
              v-model="formObject.version"
              :placeholder="$t('请输入当前版本号')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="beforeVersion" :label="$t('历史版本号')" label-style="top">
            <mt-input
              width="100%"
              type="text"
              :disabled="true"
              v-model="formObject.version"
              :placeholder="$t('请输入历史版本号')"
            ></mt-input>
          </mt-form-item> -->
          <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
            <mt-input
              width="100%"
              type="text"
              :disabled="true"
              v-model="formObject.createUserName"
              :placeholder="$t('请输入创建人')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
            <mt-input
              :disabled="true"
              v-model="formObject.createTime"
              :placeholder="$t('请输入创建时间')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="remark" :label="$t('备注')" label-style="top">
            <mt-input
              width="100%"
              type="text"
              v-model="formObject.remark"
              :placeholder="$t('备注')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
      <div class="hr"></div>
      <div class="relation-ships">
        <ConstraintTable ref="constraintTable" :form-object="formObject" />
        <CategoryTable ref="categoryTable" :form-object="formObject" />
      </div>
    </div>
  </div>
</template>

<script>
import ConstraintTable from './component/constraintTable'
import CategoryTable from './component/categoryTable'
import { statusList } from './config'
import { throttle } from 'lodash'
export default {
  components: {
    ConstraintTable,
    CategoryTable
  },
  data() {
    return {
      disabled: false, //禁用
      buList: [], //事业部门
      statusList,
      formObject: {
        //表单数据
        id: null, // 限制配额单号
        buId: null, //工厂
        title: null, //限制配额标题
        code: null, //限制配额单号
        version: null, //当前版本号
        sourceCode: null, //前版本号
        status: 0, //单据状态
        createUserName: null, //创建人
        createTime: null, //创建时间
        remark: null //备注
      },
      formRules: {
        buId: [{ required: true, message: this.$t('请选择事业部'), trigger: 'change' }],
        title: [{ required: true, message: this.$t('请输入限制配额标题'), trigger: 'change' }]
      }
    }
  },
  computed: {
    isEnableStatus() {
      return (
        (this.formObject.status == 0 || this.formObject.status == 2) && this.pageType !== 'detail'
      )
    },
    isAddPage() {
      return this.$route.query?.type === 'add'
    },
    pageType() {
      return this.$route?.query?.type || 'add'
    }
  },
  created() {
    this.getOrgList()
  },
  mounted() {
    let query = this.$route?.query
    //id存在 为创建新版本
    if (this.pageType !== 'add') {
      this.disabled = query?.type === 'detail'
      //根据id查询详情 数据回显
      this.getDetail(query.id)
      //获取列表数据
      this.$refs.constraintTable.getList(query.id)
      this.$refs.categoryTable.getList(query.id)
    }
  },
  methods: {
    async getOrgList() {
      const { code, data } = await this.$API.constraint.queryOrg()
      if (code === 200) {
        this.buList = data
        if (!this.formObject.buId && this.buList && this.buList.length === 1) {
          this.formObject.buId = this.buList[0]['id']
          this.formObject.buName = this.buList[0]['orgName']
          this.formObject.buCode = this.buList[0]['orgCode']
        }
      }
    },
    //根据id获取详情数据
    getDetail(id) {
      let params = {
        id
      }
      console.log(params)
      //请求详情接口
      this.$API.constraint.getDetails(params).then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.formObject = data
        }
      })
    },
    //保存详情页
    save: throttle(async function () {
      // 表单校验
      let validateRes = false
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          validateRes = true
        }
      })
      if (!validateRes) {
        return
      }
      const res = await this.getDetailData(false)
      if (!res) return
      const { constraintList, categoryList } = res
      const params = {
        ...this.formObject,
        detailList: constraintList,
        categoryList
      }
      this.$API.constraint.save(params).then((res) => {
        if (res.code === 200) {
          this.getDetail(res.data)
          this.$refs.constraintTable.getList(res.data)
          this.$refs.categoryTable.getList(res.data)
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
        }
      })
    }, 1000),
    //提交详情页
    submit: throttle(async function () {
      // 表单校验
      let validateRes = false
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          validateRes = true
        }
      })
      if (!validateRes) {
        return
      }
      const res = await this.getDetailData(true)
      if (!res) return
      const { constraintList, categoryList } = res
      const params = {
        ...this.formObject,
        detailList: constraintList,
        categoryList
      }
      this.$API.constraint.commit(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('提交成功'),
            type: 'success'
          })
          this.$bus.$emit('refreshQuotaConstraintList')
          this.backToBusinessConfig()
        }
      })
    }, 1000),
    async getDetailData(isCheck = false) {
      const constraintList = await this.$refs.constraintTable.getTableData(isCheck)
      const categoryList = await this.$refs.categoryTable.getTableData(isCheck)
      if (constraintList && categoryList) {
        return {
          constraintList,
          categoryList
        }
      }
    },
    //事业部下拉
    changeOrg(e) {
      const { itemData } = e
      this.formObject.buId = itemData.id
      this.formObject.buName = itemData.orgName
      this.formObject.buCode = itemData.orgCode
    },
    //返回限制配额列表页
    backToBusinessConfig() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .custom-table-container .ant-table-body tr td {
  height: 42px;
}
/deep/.mt-form-item {
  width: calc(30% - 20px);
  min-width: 200px;
  display: inline-flex;
  margin-right: 20px;
  .mt-input {
    width: 100%;
  }
}
.supplier-box {
  position: absolute;
  width: 100%;
  min-height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: #fff;
  overflow: auto;
  -webkit-overflow-scrolling: scroll;

  .miam-container {
    width: 100%;
    background: #fff;
    padding: 0 20px;

    .header {
      height: 40px;
      display: flex;
      justify-content: space-between;
      .title {
        height: 100%;
        line-height: 40px;
        font-size: 20px;
        font-weight: 600;
        color: #333;
        padding: 5px 10px;
      }
      .operate-bar {
        height: 100%;
        float: right;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        color: #4f5b6d;

        .op-item {
          cursor: pointer;
          align-items: center;
          margin-right: 20px;
          align-items: center;
          background-color: rgba(0, 0, 0, 0);
          border-color: rgba(0, 0, 0, 0);
          color: #00469c;
        }
      }
    }

    .mian-info {
      background: rgba(255, 255, 255, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    }

    .relation-ships {
      background: rgba(255, 255, 255, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      margin-top: 20px;
      // height: calc(100% - 164px);
    }
  }
  .hr {
    margin: 10px 0;
    padding: 0;
    border: 0;
    border-top: 1px solid #e9e9e9;
  }
}
::v-deep.red-color {
  color: red;
}
</style>
