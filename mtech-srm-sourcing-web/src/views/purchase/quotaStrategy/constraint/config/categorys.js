import { i18n } from '@/main.js'
const categoryColumn = [
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    cssClass: 'field-content'
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]

export const categoryConfig = (url) => {
  return [
    {
      gridId: '5e5d94e4-bca1-4201-ae09-429d372b2e9b',
      toolbar: [],
      useToolTemplate: false,
      grid: {
        allowFiltering: true,
        columnData: categoryColumn,
        allowSorting: false,
        allowSelection: true,
        selectionSettings: {
          type: 'Multiple',
          mode: 'Row'
        },
        asyncConfig: { url }
      }
    }
  ]
}
