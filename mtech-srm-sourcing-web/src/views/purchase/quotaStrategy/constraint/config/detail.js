import { i18n } from '@/main.js'
import utils from '@/utils/utils'
let options = utils.getQuotaDict('QUOTA_STRATEGY_CONSTRAINT_TYPE') || [] //限制类型
export const constraintTypeList = options.map((item) => {
  return {
    label: item.dictName,
    value: item.dictCode
  }
})
//逻辑符
let options2 = utils.getQuotaDict('QUOTA_STRATEGY_CONSTRAINT_SYMBOL') || [
  { dictName: '<=', dictCode: '1' },
  { dictName: '>', dictCode: '2' }
] //限制类型
export const logicSymbolCodeList = options2.map((item) => {
  return {
    label: item.dictName,
    value: item.dictCode
  }
})
//列配置
export const constraintColumns = [
  {
    type: 'checkbox',
    width: 50
    // fixed: 'left'
  },
  {
    title: i18n.t('限制类型'),
    field: 'constraintTypeCode',
    minWidth: 150,
    editRender: {
      name: '$select',
      options: constraintTypeList,
      props: { placeholder: '请选择限制类型' },
      events: {
        change: ({ row }) => {
          const exitItem = constraintTypeList.find((el) => el.value === row.constraintTypeCode)
          row.constraintTypeName = exitItem ? exitItem.label : ''
        }
      }
    }
  },
  {
    title: i18n.t('逻辑符'),
    field: `logicSymbolCode`,
    minWidth: 150,
    editRender: {
      name: '$select',
      options: logicSymbolCodeList,
      props: { placeholder: '请选择限制类型', disabled: true }
    }
  },
  {
    title: i18n.t('配额(%)'),
    field: `quotaPercent`,
    align: 'center',
    editRender: { name: 'input' }
  }
]

export const categoryColumnsCfg = function () {
  return [
    {
      type: 'checkbox',
      width: 50
      // fixed: 'left'
    },
    {
      title: i18n.t('品类名称'),
      field: `categoryName`,
      editRender: {},
      slots: {
        edit: 'categoryNameEdit'
      }
      // editConfig: {
      //   controlKey: 'editable',
      //   editor: (h, scoped) => {
      //     return (
      //       <MagnifierInput
      //         config={categoryDialogCofig}
      //         onChange={(e) => {
      //           scoped.row.categoryCode = e.categoryCode
      //           scoped.row.categoryName = e.categoryName
      //           scoped.row.categoryId = e.categoryTypeId
      //         }}></MagnifierInput>
      //     )
      //   },
      //   rules: function () {
      //     return [
      //       {
      //         required: true,
      //         message: i18n.t('品类名称不能为空'),
      //         triggle: 'change'
      //       }
      //     ]
      //   }
      // }
    },
    {
      title: i18n.t('品类编码'),
      field: `categoryCode`
      // editConfig: {
      //   controlKey: 'editable',
      //   editorProps: {
      //     component: 'a-input',
      //     disabled: true
      //   },
      //   rules: () => [{ required: true, message: i18n.t('品类编码不能为空'), triggle: 'blur' }]
      // }
    }
  ]
}
