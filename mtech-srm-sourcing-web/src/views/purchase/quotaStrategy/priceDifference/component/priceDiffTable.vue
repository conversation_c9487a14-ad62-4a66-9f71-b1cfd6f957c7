<template>
  <div>
    <!----  表格  --------->
    <div v-if="isEnableStatus" class="button-group">
      <vxe-button
        v-for="item in toolbar"
        :key="item.code"
        :status="item.status"
        :icon="item.icon"
        size="small"
        @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable })"
        >{{ item.name }}</vxe-button
      >
    </div>
    <vxe-grid
      ref="xTable"
      :columns="priceDiffColumns"
      :data="dataSource"
      :edit-config="editConfig"
      :edit-rules="editRules"
      max-height="300px"
    >
    </vxe-grid>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>
<script>
import { priceDiffColumns } from '../config/detail.js'
export default {
  components: {},
  props: {
    formObject: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      editRules: {
        priceDifferencePercent: [{ required: true, message: this.$t('请输入价差') }],
        quotaPercent: [{ required: true, message: this.$t('请输入比例') }]
      },
      editConfig: {
        trigger: 'click',
        mode: 'row',
        showStatus: true
      },
      toolbar: [
        {
          code: 'Add',
          name: this.$t('新增'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Delete',
          name: this.$t('删除'),
          icon: 'el-icon-delete',
          status: 'info'
        }
      ],
      pageSettings: {
        pageCount: 5,
        pageSize: 20, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [10, 20, 50, 100]
      },
      priceDiffColumns, //table列配置
      dataSource: [] //table数据
    }
  },
  computed: {
    isEnableStatus() {
      return (
        (this.formObject.status == 0 || this.formObject.status == 2) && this.pageType !== 'detail'
      )
    },
    pageType() {
      return this.$route?.query?.type || 'add'
    }
  },
  methods: {
    //获取表格数据
    getList(id) {
      let params = {
        id,
        page: {
          current: this.pageSettings.current ? this.pageSettings.current : 1,
          size: this.pageSettings.pageSize
        }
      }
      //请求列表接口
      this.$API.priceDiff.queryList(params).then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.dataSource = data.records
          const total = res?.data?.total || 0
          this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
          this.pageSettings.totalRecordsCount = Number(total)
        }
      })
    },
    //新增
    handleAdd() {
      const obj = {
        priceDifferencePercent: null,
        quotaPercent: null,
        optionType: 'add'
      }
      this.dataSource.unshift(obj)
    },
    // 删除
    handleDelete(selectedRows) {
      //选中判断
      if (selectedRows.length <= 0) {
        this.$toast({
          content: this.$t('请勾选需要操作的数据'),
          type: 'warning'
        })
        return
      }
      //删除前的确认
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据')
        },
        success: () => {
          const isAllNewItem = selectedRows.every((item) => item.optionType === 'add')
          if (isAllNewItem) {
            selectedRows.forEach((item) => {
              this.dataSource.splice(this.dataSource.indexOf(item), 1)
            })
            return
          }
          const params = {
            code: this.formObject.code,
            detailIdList: selectedRows.filter((item) => item.type !== 'add').map((item) => item.id),
            id: this.formObject.id
          }
          this.$API.priceDiff.detailBatchDelete(params).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.getList(this.formObject.id)
          })
        }
      })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      if (this.dataSource.some((i) => i.optionType === 'add')) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('表格中存在未保存的数据，翻页将会丢失当前修改的内容，是否确定？')
          },
          success: () => {
            this.pageSettings.current = currentPage
            this.getList(this.formObject.id) //刷新列表
          }
        })
      } else {
        this.pageSettings.current = currentPage
        this.getList(this.formObject.id) //刷新列表
      }
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      if (this.dataSource.some((i) => i.optionType === 'add')) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('表格中存在未保存的数据，翻页将会丢失当前修改的内容，是否确定？')
          },
          success: () => {
            this.pageSettings.pageSize = pageSize
            this.getList(this.formObject.id) //刷新列表
          }
        })
      } else {
        this.pageSettings.pageSize = pageSize
        this.getList(this.formObject.id) //刷新列表
      }
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      if (code === 'Add') {
        this.handleAdd()
      }
      if (code === 'Delete') {
        const selectedRows = $grid.getCheckboxRecords() // 表格勾选行的内容
        this.handleDelete(selectedRows)
      }
    },
    async getTableData() {
      if (!this.dataSource || !this.dataSource.length) {
        this.$toast({
          content: this.$t('请先添加价差标准明细数据'),
          type: 'warning'
        })
        return null
      }
      if (this.dataSource.some((i) => !i.priceDifferencePercent || !i.quotaPercent)) {
        this.$toast({
          content: this.$t('表格中有未通过校验的数据'),
          type: 'warning'
        })
        return null
      }
      this.dataSource.forEach((item) => {
        item.opt = item.optionType === 'add' ? 0 : 1
      })
      return this.dataSource
    }
  }
}
</script>
<style lang="scss" scoped>
.button-group {
  display: flex;
  padding: 0 16px 16px;
}
</style>
