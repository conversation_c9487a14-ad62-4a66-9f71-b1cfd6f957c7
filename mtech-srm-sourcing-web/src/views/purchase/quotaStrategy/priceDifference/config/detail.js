import { i18n } from '@/main.js'

//列配置
export const priceDiffColumns = [
  {
    type: 'checkbox',
    width: 50
    // fixed: 'left'
  },
  {
    title: i18n.t('价差>%'),
    field: `priceDifferencePercent`,
    // editConfig: {
    //   controlKey: 'editable',
    //   editorProps: { component: 'a-input' },
    //   rules: function () {
    //     return [
    //       { required: true, message: i18n.t('价差不能为空'), triggle: 'blur' },
    //       { validator: validateNumber, int: true }
    //     ]
    //   }
    // }
    editRender: { name: 'input' }
  },
  {
    title: i18n.t('比例≤%'),
    field: `quotaPercent`,
    // editConfig: {
    //   controlKey: 'editable',
    //   editorProps: { component: 'a-input' },
    //   rules: function () {
    //     return [
    //       { required: true, message: i18n.t('比例不能为空'), triggle: 'blur' },
    //       { validator: validateNumber, max: 100, min: -1, checkMin: true, int: true }
    //     ]
    //   }
    // }
    editRender: { name: 'input' }
  }
  // {
  //   title: i18n.t('操作'),
  //   key: 'operation',
  //   fixed: 'right',
  //   width: 120,
  //   scopedSlots: { customRender: 'operation' }
  // }
]
