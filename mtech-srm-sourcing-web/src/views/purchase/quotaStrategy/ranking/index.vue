<template>
  <div class="quato-customization">
    <collapse-search :is-grid-display="true" @reset="reset" @search="handleSearch">
      <mt-form ref="ruleForm" :model="queryForm">
        <mt-form-item prop="orgName" :label="$t('事业部')" label-style="top">
          <mt-select
            v-model="queryForm.buId"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="factoryList"
            :fields="{ text: 'orgName', value: 'id' }"
            :placeholder="$t('请选择事业部')"
            @change="handleFactoryChange($event)"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="rankingType" :label="$t('排名类型')" label-style="top">
          <mt-select
            v-model="queryForm.rankingType"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="rankTypeOptions"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('请选择排名类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-select
            v-model="queryForm.status"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="statusOptions"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('发布状态')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="effectiveDate" :label="$t('生效日期')" label-style="top">
          <mt-date-range-picker
            v-if="isReloadPicker"
            v-model="queryForm.effectiveDate"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :separator="$t('至')"
            :placeholder="$t('选择开始日期')"
          ></mt-date-range-picker>
        </mt-form-item>
        <mt-form-item prop="invalidDate" :label="$t('失效日期')" label-style="top">
          <mt-date-range-picker
            v-if="isReloadPicker"
            v-model="queryForm.invalidDate"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :separator="$t('至')"
            :placeholder="$t('选择结束日期')"
          ></mt-date-range-picker>
        </mt-form-item>
        <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
          <mt-input v-model="queryForm.createUserName" :placeholder="$t('请输入创建人')" />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!----  表格  --------->
    <sc-table
      ref="xTable"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="vxeColumns"
      :table-data="dataSource"
      @refresh="search"
      class="components-table-demo-nested"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
          >{{ item.name }}</vxe-button
        >
      </template>
      <template #orgNameHead="{}"> <span class="red-color">*</span>{{ $t('事业部') }} </template>
      <template #orgNameDefault="{ row }">
        <vxe-select
          v-if="row.editable"
          v-model="row.orgCode"
          :options="factoryList"
          :option-props="{
            label: 'orgName',
            value: 'orgCode'
          }"
          @change="handleFactoryChange($event.value, row)"
          transfer
          filterable
        ></vxe-select>
        <span v-else>{{ row.orgName }}</span>
      </template>
      <template #rankingTypeHead="{}">
        <span class="red-color">*</span>{{ $t('排名类型') }}
      </template>
      <template #rankingTypeDefault="{ row }">
        <vxe-select
          v-if="row.editable"
          v-model="row.rankingType"
          :options="rankTypeOptions"
          :option-props="{
            label: 'dictName',
            value: 'dictCode'
          }"
          @change="rankingTypeChange($event.value, row)"
          transfer
          filterable
        ></vxe-select>
        <span v-else>{{ rankingTypedefa(row.rankingType) }}</span>
      </template>
      <template #priorityLevelHead="{}">
        <span class="red-color">*</span>{{ $t('优先级') }}</template
      >
      <template #priorityLevelDefault="{ row }">
        <vxe-select
          v-if="row.editable"
          v-model="row.priorityLevel"
          :options="priorityLevelOptions"
          :option-props="{
            label: 'dictName',
            value: 'dictCode'
          }"
          @change="priorityLevelChange($event.value, row)"
          transfer
          filterable
        ></vxe-select>
        <span v-else>{{ priorityLeveldefa(row.priorityLevel) }}</span>
      </template>
      <template #descriptionDefault="{ row }">
        <vxe-input
          v-if="row.editable"
          size="small"
          :max-length="200"
          v-model="row.description"
        ></vxe-input>
        <span v-else>{{ row.description }}</span>
      </template>
      <template #operationDefault="{ row, rowIndex }">
        <div class="editable-row-operations">
          <span v-if="row.editable">
            <a @click="() => save(row, rowIndex)">{{ $t('保存') }}</a>
            <a @click="() => cancelEdit(row, rowIndex)">{{ $t('取消') }}</a>
          </span>
          <span v-else>
            <a :disabled="row.status !== 0" @click="() => edit(row, rowIndex)">{{ $t('编辑') }}</a>
          </span>
        </div>
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import moment from 'moment'
import utils from '@/utils/utils'
import collapseSearch from '@/components/collapseSearch'
import { vxeColumns } from './config'
import ScTable from '@/components/ScTable/src/index'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {
    collapseSearch,
    ScTable
  },
  data() {
    return {
      loading: false,
      toolbar: [
        {
          code: 'Add',
          name: this.$t('新增'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Effect',
          name: this.$t('生效'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Invalid',
          name: this.$t('失效'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Delete',
          name: this.$t('删除'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Export',
          name: this.$t('导出'),
          icon: 'el-icon-delete',
          status: 'info'
        }
      ],
      queryForm: {
        orgCode: '', // 事业部
        rankingType: '', // 排名类型
        status: '', // 状态
        effectiveDate: '', // 生效日期
        invalidDate: '', //失效日期
        createUserName: '', // 创建人
        currentPage: 1,
        pageSize: 10
      },
      pageSettings: {
        pageCount: 5,
        pageSize: 20, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [10, 20, 50, 100]
      },
      factoryList: [], // 事业部列表
      rankTypeOptions: utils.getQuotaDict('QUOTA_STRATEGY_RANKING_TYPE') || [],
      priorityLevelOptions: utils.getQuotaDict('QUOTA_STRATEGY_RANKING_PRIORITY') || [],
      statusOptions: utils.getQuotaDict('QUOTA_STRATEGY_SHORT_STATUS') || [],
      vxeColumns,
      dataSource: [],
      fieldsarr: {
        dataSource: [], // 组织树下拉数组
        value: 'id',
        text: 'orgId',
        child: 'childrenList'
      },
      isEdit: false,
      cacheRow: {},
      isReloadPicker: true
    }
  },
  created() {
    this.getFactoryList()
    this.search()
  },
  methods: {
    moment,
    getFactoryList() {
      this.$API.ranking.getPermissionSiteList({}).then((res) => {
        this.factoryList = res.data
        console.log(this.factoryList)
      })
    },
    handleAdd() {
      if (this.isEdit === true) {
        this.$toast({
          content: this.$t('新增失败，存在未保存的数据'),
          type: 'warning'
        })
        return
      }
      const obj = {
        editable: true,
        orgName: '',
        rankingType: '',
        priorityLevel: '',
        description: '',
        effectiveDate: '',
        invalidDate: '',
        status: '',
        createUserName: '',
        createTime: '',
        startDate: ''
      }
      if (!obj.orgId && this.factoryList && this.factoryList.length === 1) {
        obj.orgName = this.factoryList[0]['orgName']
        obj.orgId = this.factoryList[0]['id']
        obj.orgCode = this.factoryList[0]['orgCode']
      }
      this.isEdit = true
      this.dataSource.unshift(obj)
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      if (code === 'Add') {
        this.handleAdd()
        return
      }
      const selectedRows = $grid.getCheckboxRecords() // 表格勾选行的内容
      if (
        (code === 'Effect' || code === 'Invalid' || code === 'Delete') &&
        selectedRows.length <= 0
      ) {
        this.$toast({
          content: this.$t('请勾选需要操作的数据'),
          type: 'warning'
        })
        return
      }
      if (this.isEdit === true) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('存在未保存的数据，是否继续当前操作')
          },
          success: () => {
            if (code === 'Effect') {
              // 生效
              this.handleEffect(selectedRows)
            } else if (code === 'Invalid') {
              // 失效
              this.handleInvalid(selectedRows)
            } else if (code === 'Delete') {
              // 删除
              this.handleDelete(selectedRows)
            } else if (code === 'Export') {
              // 导出
              this.handleExport()
            }
          }
        })
        return
      }
      if (code === 'Effect') {
        // 生效
        this.handleEffect(selectedRows)
      } else if (code === 'Invalid') {
        // 失效
        this.handleInvalid(selectedRows)
      } else if (code === 'Delete') {
        // 删除
        this.handleDelete(selectedRows)
      } else if (code === 'Export') {
        // 导出
        this.handleExport()
      }
    },
    // 生效
    handleEffect(selectedRows) {
      // 校验数据为拟定状态
      if (selectedRows.some((i) => i.status !== 0)) {
        this.$toast({
          content: this.$t('所选数据存在非拟定状态数据'),
          type: 'warning'
        })
        return
      }
      this.$API.ranking
        .batchEffectiveCustomization({ idList: selectedRows.map((i) => i.id) })
        .then((res) => {
          const { code } = res
          if (code === 200) {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.search()
          }
        })
    },
    // 失效
    handleInvalid(selectedRows) {
      // 校验数据为有效状态
      if (selectedRows.some((i) => i.status !== 1)) {
        this.$toast({
          content: this.$t('所选数据存在非有效状态数据'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        modal: () => import('@/components/inputDialog/index.vue'),
        data: {
          title: this.$t('失效提示')
        },
        success: (e) => {
          console.log('e.inputContent', e.inputContent)
          // e.inputContent是失效弹窗中输入的值
          this.$API.ranking
            .batchInvalidCustomization({
              idList: selectedRows.map((i) => i.id),
              remark: e.inputContent
            })
            .then((res) => {
              const { code } = res
              if (code === 200) {
                this.$toast({
                  content: this.$t('操作成功'),
                  type: 'success'
                })
                this.search()
              }
            })
        }
      })
    },
    // 删除
    handleDelete(selectedRows) {
      // 校验数据为拟定状态
      if (selectedRows.some((i) => i.status !== 0)) {
        this.$toast({
          content: this.$t('所选数据存在非拟定状态数据'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          this.$API.ranking
            .batchDeleteCustomization({ idList: selectedRows.map((i) => i.id) })
            .then((res) => {
              console.log()
              const { code } = res
              if (code === 200) {
                this.$toast({
                  content: this.$t('删除成功'),
                  type: 'success'
                })
                this.search()
              }
            })
        }
      })
    },
    // 导出
    handleExport() {
      const params = {
        ...this.queryForm,
        page: {
          current: this.pageSettings.current,
          size: this.pageSettings.pageSize
        }
      }
      if (params.startDate) {
        params.createTimeStart = utils.formatTime(
          new Date(this.queryForm.startDate[0]),
          'YYYY-mm-dd'
        )
        params.createTimeEnd = utils.formatTime(new Date(this.queryForm.startDate[1]), 'YYYY-mm-dd')
      }
      if (params.endDate) {
        params.endDateStart = utils.formatTime(new Date(this.queryForm.endDate[0]), 'YYYY-mm-dd')
        params.endDateEnd = utils.formatTime(new Date(this.queryForm.endDate[1]), 'YYYY-mm-dd')
      }
      delete params.startDate
      delete params.endDate
      this.$API.ranking.exportExcel({}).then((res) => {
        this.$toast({
          type: 'success',
          content: this.$t('导出成功，请稍后！')
        })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      if (this.isEdit === true) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('存在未保存的数据，是否继续当前操作')
          },
          success: () => {
            this.pageSettings.current = currentPage
            this.search('pageSettings')
          }
        })
        return
      }
      this.pageSettings.current = currentPage
      this.search('pageSettings')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      if (this.isEdit === true) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('存在未保存的数据，是否继续当前操作')
          },
          success: () => {
            this.pageSettings.pageSize = pageSize
            this.search()
          }
        })
        return
      }
      this.pageSettings.pageSize = pageSize
      this.search()
    },
    edit(row) {
      if (row.status === 2 || row.status === 1) {
        this.$toast({
          content: this.$t('有效或失效状态不可编辑'),
          type: 'warning'
        })
        return
      }
      if (this.isEdit === true) {
        this.$toast({
          content: this.$t('请先保存或取消编辑当前数据'),
          type: 'warning'
        })
        return
      }
      this.isEdit = true
      this.cacheRow = JSON.parse(JSON.stringify(row))
      this.$set(row, 'editable', true)
    },
    cancelEdit(row, index) {
      this.$set(row, 'editable', false)
      this.isEdit = false
      this.$set(this.dataSource, index, this.cacheRow)
      if (!row.createUserName) {
        this.dataSource.splice(index, 1)
      }
      // 重新请求数据
      // this.search()
    },
    async save(row) {
      // 组件tooltip有问题，暂时先用吐司代替
      if (!row.orgName) {
        this.$toast({
          content: this.$t('请选择事业部'),
          type: 'warning'
        })
        return
      }
      if (!row.rankingType) {
        this.$toast({
          content: this.$t('请选择排名类型'),
          type: 'warning'
        })
        return
      }
      if (!row.priorityLevel) {
        this.$toast({
          content: this.$t('请选择优先级'),
          type: 'warning'
        })
        return
      }
      const params = {
        ...row
      }
      if (params.id?.includes('row_')) {
        delete params.id
      }
      let arr = []
      arr.push(params)
      this.$API.ranking.saveCustomization(arr).then((res) => {
        const { code } = res
        if (code === 200) {
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
          // 重新请求数据
          this.search()
        }
      })
    },
    reset() {
      console.log('重置')
      if (this.isEdit === true) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('存在未保存的数据，是否继续当前操作')
          },
          success: () => {
            this.isReloadPicker = false
            this.queryForm = {
              orgName: '', // 事业部
              rankingType: '', // 排名类型
              status: '', // 状态
              effectiveDate: '', // 生效日期
              invalidDate: '', // 失效日期
              createUserName: '', // 创建人
              currentPage: 1,
              pageSize: 10
            }
            this.$nextTick(() => {
              this.isReloadPicker = true
            })
            this.search()
          }
        })
        return
      }
      this.isReloadPicker = false
      this.queryForm = {
        orgName: '', // 事业部
        rankingType: '', // 排名类型
        status: '', // 状态
        effectiveDate: '', // 生效日期
        invalidDate: '', // 失效日期
        createUserName: '', // 创建人
        currentPage: 1,
        pageSize: 10
      }
      this.$nextTick(() => {
        this.isReloadPicker = true
      })
      this.search()
    },
    handleSearch() {
      if (this.isEdit === true) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('存在未保存的数据，是否继续当前操作')
          },
          success: () => {
            this.search()
          }
        })
        return
      }
      this.search()
    },
    search(pageSettings) {
      console.log('search', this.queryForm)
      const params = {
        ...this.queryForm,
        page: {
          current: pageSettings ? this.pageSettings.current : 1,
          size: this.pageSettings.pageSize
        }
      }
      if (params.effectiveDate) {
        params.effectiveDateStart = utils.formatTime(
          new Date(this.queryForm.effectiveDate[0]),
          'YYYY-mm-dd'
        )
        params.effectiveDateEnd = utils.formatTime(
          new Date(this.queryForm.effectiveDate[1]),
          'YYYY-mm-dd'
        )
      }
      if (params.invalidDate) {
        params.invalidDateStart = utils.formatTime(
          new Date(this.queryForm.invalidDate[0]),
          'YYYY-mm-dd'
        )
        params.invalidDateEnd = utils.formatTime(
          new Date(this.queryForm.invalidDate[1]),
          'YYYY-mm-dd'
        )
      }
      delete params.startDate
      delete params.endDate
      this.loading = true
      this.$API.ranking
        .getCustomizationList(params)
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            const total = res?.data?.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)
            this.dataSource = data.records
            this.isEdit = false
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    setTableEmpty(fieldsarr, row) {
      for (let i = 0; i < fieldsarr.length; i++) {
        this.$set(row, fieldsarr[i], '')
      }
    },
    handleFactoryChange(e, row) {
      // 事业部下拉变更
      if (row) {
        // 表格内的下拉选择
        for (let i = 0; i < this.factoryList.length; i++) {
          const element = this.factoryList[i]
          if (element.orgCode === e) {
            row.orgName = element.orgName
            row.orgId = element.id
            row.orgCode = element.orgCode
            return
          }
        }
      } else {
        // 表单内的下拉选择 使用的是mt组件api与vxe不同
        const { itemData } = e
        this.queryForm.buId = itemData.id
      }
    },
    rankingTypeChange(e, row) {
      // 排名类型变更
      if (row) {
        // 表格内物料编码变更
        for (let i = 0; i < this.rankTypeOptions.length; i++) {
          const element = this.rankTypeOptions[i]
          if (element.dictCode === e) {
            row.rankingType = element.dictCode
            return
          }
        }
      } else {
        // 表单内的下拉选择 使用的是mt组件api与vxe不同
        const { itemData } = e
        this.queryForm.rankingType = itemData.dictCode
      }
    },
    priorityLevelChange(e, row) {
      // 优先级变更
      // 排名类型变更
      if (row) {
        // 表格内物料编码变更
        for (let i = 0; i < this.priorityLevelOptions.length; i++) {
          const element = this.priorityLevelOptions[i]
          if (element.dictCode === e) {
            row.priorityLevel = element.dictCode
            return
          }
        }
      } else {
        // 表单内的下拉选择 使用的是mt组件api与vxe不同
        const { itemData } = e
        this.queryForm.priorityLevel = itemData.dictCode
      }
    },
    // 排名类型默认显示
    rankingTypedefa(e) {
      for (let i = 0; i < this.rankTypeOptions.length; i++) {
        const element = this.rankTypeOptions[i]
        if (element.dictCode === e) {
          return element.dictName
        }
      }
    },
    // 优先级默认显示
    priorityLeveldefa(e) {
      for (let i = 0; i < this.priorityLevelOptions.length; i++) {
        const element = this.priorityLevelOptions[i]
        if (element.dictCode === e) {
          return element.dictName
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.quato-customization {
  margin-top: 24px;
  background: #fff;
  padding: 12px;
  border-radius: 8px;
  height: 100%;
  .button-group {
    display: flex;
    padding: 0 0 8px;
  }
}
.components-table-demo-nested {
  // ::v-deep.ant-table-body {
  //   overflow-x: auto !important;
  // }
  ::v-deep.red-color {
    color: red;
  }
}
</style>
