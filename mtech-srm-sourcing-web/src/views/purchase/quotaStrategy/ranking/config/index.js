import { i18n } from '@/main.js'
const toolbar = [
  // { id: "Add", icon: "icon_solid_Createorder", title: i18n.t("新增") },
  // { id: "Stop", icon: "icon_solid_Cancel", title: i18n.t("取消") },
  // { id: "Submit", icon: "icon_solid_Submit", title: i18n.t("提交") },
  // { id: "Import", icon: "icon_solid_Createorder", title: i18n.t("导入") },
]
const materialColumn = [
  // {
  //   type: 'checkbox',
  //   width: '60'
  // },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    cssClass: 'field-content',
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  }
]
const categoryColumn = [
  // {
  //   type: 'checkbox',
  //   width: '60'
  // },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    cssClass: 'field-content'
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]
const supplierColumn = [
  // {
  //   type: 'checkbox',
  //   width: '60'
  // },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    cssClass: 'field-content'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  }
]
export const pageConfig = (url, columnType) => {
  let columnData = []
  if (columnType === 'material') {
    columnData = materialColumn
  } else if (columnType === 'category') {
    columnData = categoryColumn
  } else {
    columnData = supplierColumn
  }
  return [
    {
      toolbar,
      useToolTemplate: false,
      grid: {
        allowFiltering: true,
        columnData,
        allowSorting: false,
        allowSelection: true,
        selectionSettings: {
          type: 'Multiple',
          mode: 'Row'
        },
        asyncConfig: { url }
      }
    }
  ]
}

export const vxeColumns = [
  {
    type: 'checkbox',
    width: '50'
  },
  {
    // title: i18n.t('事业部'),
    field: 'orgName',
    width: 150,
    align: 'left',
    slots: { header: 'orgNameHead', default: 'orgNameDefault' }
  },
  {
    // title: i18n.t('排名类型'),
    field: 'rankingType',
    width: 180,
    align: 'left',
    slots: { header: 'rankingTypeHead', default: 'rankingTypeDefault' }
  },
  {
    // title: i18n.t('优先级'),
    field: 'priorityLevel',
    width: 120,
    align: 'left',
    slots: { header: 'priorityLevelHead', default: 'priorityLevelDefault' }
  },
  {
    title: i18n.t('说明'),
    field: 'description',
    width: 200,
    align: 'left',
    slots: { default: 'descriptionDefault' }
  },
  {
    title: i18n.t('生效日期'),
    field: 'effectiveDate',
    width: 150,
    align: 'left'
  },
  {
    title: i18n.t('失效日期'),
    field: 'invalidDate',
    width: 150,
    align: 'left'
  },
  {
    title: i18n.t('失效原因'),
    field: 'invalidReason',
    width: 150,
    align: 'left'
  },
  {
    title: i18n.t('状态'),
    field: 'status',
    align: 'left',
    formatter: ({ cellValue }) => {
      const statusOptions = [
        { text: i18n.t('拟定'), code: 0 },
        { text: i18n.t('有效'), code: 1 },
        { text: i18n.t('失效'), code: 2 }
      ]
      let item = statusOptions.find((item) => item.code === cellValue)
      return item ? item.text : ''
    }
  },
  { title: i18n.t('创建人'), field: 'createUserName', align: 'left' },
  { title: i18n.t('创建时间'), field: 'createTime', align: 'left' },
  { title: i18n.t('最后更新人'), field: 'updateUserName', align: 'left' },
  {
    title: i18n.t('操作'),
    field: 'operation',
    fixed: 'right',
    slots: { default: 'operationDefault' }
  }
]
