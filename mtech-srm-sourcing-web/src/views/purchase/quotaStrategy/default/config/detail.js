import { i18n } from '@/main.js'
//循环预设配额1-10的对象
const defaultQuota = []
for (let i = 1; i <= 10; i++) {
  defaultQuota.push({
    title: `预设配额${i}(%)`,
    field: `defaultPercent${i}`,
    align: 'left',
    minWidth: 150,
    slots: { default: `defaultPercent${i}` }
  })
}

//列配置
export const vxeColumns = [
  {
    type: 'checkbox',
    minWidth: 50
    // fixed: 'left'
  },
  {
    field: 'supplierCount',
    width: 150,
    align: 'left',
    slots: { header: 'suppliersHead', default: 'supplierCount' }
  },
  ...defaultQuota,
  {
    title: i18n.t('操作'),
    width: 80,
    field: 'operation',
    fixed: 'right',
    slots: { default: 'operation' }
  }
]
