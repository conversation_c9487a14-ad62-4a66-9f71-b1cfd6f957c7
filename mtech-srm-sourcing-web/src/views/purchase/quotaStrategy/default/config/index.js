import { i18n } from '@/main.js'
import utils from '@/utils/utils'
import { searchOptionsList } from '@/constants'
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'CreateNew', icon: 'icon_solid_Cancel', title: i18n.t('创建新版本') },
  { id: 'ExportFile', icon: 'icon_solid_Createorder', title: i18n.t('导出') }
]
//单据状态
let options = utils.getQuotaDict('QUOTA_STRATEGY_STATUS') || [] //单据状态
export const statusList = options.map((item) => {
  return {
    label: item.dictName,
    status: item.dictCode,
    cssClass: 'title-'
  }
})

const columnData = function (orgData, uid) {
  return [
    {
      type: 'checkbox',
      width: '60'
    },
    {
      field: 'orgId',
      headerText: i18n.t('事业部'),
      valueAccessor: (field, data) => {
        return data.orgName
      },
      searchOptions: {
        elementType: 'select',
        dataSource: orgData,
        fields: { text: 'orgName', value: 'orgId' }
      }
    },
    {
      field: 'code',
      headerText: i18n.t('预设配额单号'),
      cssClass: 'field-content',
      cellTools: [
        {
          id: 'edit',
          icon: 'icon_Editor',
          title: i18n.t('编辑'),
          visibleCondition: (data) => {
            //创建人=当前登录用户 且status =  0:拟定 2:已驳回 才能操作
            return (data['status'] == '0' || data['status'] == '2') && data['createUserId'] == uid
          }
        },
        {
          id: 'delete',
          icon: 'icon_solid_Delete',
          title: i18n.t('删除'),
          visibleCondition: (data) => {
            //创建人=当前登录用户 且status =  0:拟定 2:已驳回 才能操作
            return (data['status'] == '0' || data['status'] == '2') && data['createUserId'] == uid
          }
        },
        {
          id: 'detail',
          icon: 'icon_solid_Submit',
          title: i18n.t('明细')
        }
      ]
    },
    {
      field: 'title',
      headerText: i18n.t('预设配额标题')
    },
    {
      field: 'remark',
      headerText: i18n.t('备注'),
      ignore: true //忽略
    },
    {
      field: 'sourceCode',
      headerText: i18n.t('历史单据号'),
      ignore: true //忽略
    },
    {
      field: 'status',
      headerText: i18n.t('单据状态'), //单据状态拟定 0-拟定,1-审批中,2-已驳回,3-已审批,4-失效
      valueConverter: {
        type: 'map',
        map: statusList,
        fields: { text: 'label', value: 'status' }
      }
    },
    {
      field: 'createUserName',
      headerText: i18n.t('创建人')
    },
    {
      field: 'createTime',
      headerText: i18n.t('创建时间'),
      ignore: true, //忽略
      searchOptions: {
        ...searchOptionsList.timeRange
      }
    },
    {
      field: 'createDateStart',
      headerText: i18n.t('创建时间从'),
      width: '1',
      searchOptions: {
        elementType: 'datetime',
        operator: 'between'
      }
    },
    {
      field: 'createDateEnd',
      headerText: i18n.t('创建时间至'),
      width: '1',
      searchOptions: {
        elementType: 'datetime',
        operator: 'between'
      }
    }
  ]
}
export const pageConfig = (url, orgData, uid) => [
  {
    toolbar,
    useToolTemplate: false,
    gridId: 'f8433fad-e74b-4271-9fe3-331a826fe2bd',
    grid: { columnData: columnData(orgData, uid), asyncConfig: { url } }
  }
]
