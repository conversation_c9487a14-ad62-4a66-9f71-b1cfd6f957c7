<template>
  <div class="supplier-box mt-flex">
    <div class="miam-container">
      <div class="header">
        <div class="title">{{ $t('预设配额维护') }}</div>
        <div class="operate-bar">
          <div v-if="type != 'detail'" class="op-item mt-flex" @click="save">
            {{ $t('保存') }}
          </div>
          <div v-if="type != 'detail'" class="op-item mt-flex" @click="submit">
            {{ $t('提交审批') }}
          </div>
          <div class="op-item mt-flex" @click="backToBusinessConfig">
            {{ $t('返回') }}
          </div>
        </div>
      </div>
      <!-- 顶部主要信息 -->
      <div class="mian-info">
        <div class="hr"></div>
        <mt-form ref="ruleForm" :model="formObject" :rules="formRules">
          <mt-form-item prop="orgId" :label="$t('事业部：')" label-style="top">
            <mt-select
              :disabled="type == 'detail'"
              :fields="{ text: 'orgName', value: 'id' }"
              v-model="formObject.orgId"
              float-label-type="Never"
              :allow-filtering="true"
              :data-source="buList"
              :placeholder="$t('请选择事业部')"
              @change="changeBu"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="title" :label="$t('预设配额标题')" label-style="top">
            <mt-input
              :disabled="type == 'detail'"
              width="100%"
              type="text"
              v-model="formObject.title"
              :placeholder="$t('请输入预设配额标题')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="code" :label="$t('预设配额单号')" label-style="top">
            <mt-input
              width="100%"
              type="text"
              :disabled="true"
              v-model="formObject.code"
              :placeholder="$t('请输入预设配额单号')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="sourceCode" :label="$t('历史单据号')" label-style="top">
            <mt-input
              width="100%"
              type="text"
              :disabled="true"
              v-model="formObject.sourceCode"
              :placeholder="$t('请输入历史单据号')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="status" :label="$t('单据状态')" label-style="top">
            <mt-select
              :disabled="true"
              :fields="{ text: 'label', value: 'status' }"
              v-model="formObject.status"
              float-label-type="Never"
              :allow-filtering="true"
              :data-source="statusList"
              :placeholder="$t('请选择单据状态')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
            <mt-input
              width="100%"
              type="text"
              :disabled="true"
              v-model="formObject.createUserName"
              :placeholder="$t('请输入创建人')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
            <mt-input
              :disabled="true"
              v-model="formObject.createTime"
              :placeholder="$t('请输入创建时间')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="remark" :label="$t('备注')" label-style="top">
            <mt-input
              width="100%"
              type="text"
              v-model="formObject.remark"
              :placeholder="$t('备注')"
              :disabled="type == 'detail'"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
      <div class="hr"></div>
      <div class="relation-ships">
        <!----  表格  --------->
        <sc-table ref="xTable" :columns="vxeColumns" :table-data="dataSource">
          <template slot="custom-tools">
            <vxe-button
              v-for="item in toolbar"
              :key="item.code"
              :status="item.status"
              :icon="item.icon"
              :disabled="type == 'detail'"
              size="small"
              @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
              >{{ item.name }}</vxe-button
            >
          </template>
          <template #suppliersHead="{}">
            <span class="red-color">*</span>{{ $t('供应商数量') }}</template
          >
          <template #supplierCount="{ row }">
            <vxe-select
              v-if="editRow[row.id]"
              v-model="row.supplierCount"
              :options="supplierCountList"
              :option-props="{
                label: 'label',
                value: 'value'
              }"
              transfer
              filterable
              @change="handlesupplierCountChange($event, editRow[row.id])"
            ></vxe-select>
            <span v-else>{{ row.supplierCount }}</span>
          </template>
          <template #defaultPercent1="{ row }">
            <vxe-input
              v-if="editRow[row.id]"
              v-model="editRow[row.id].defaultPercent1"
              size="small"
            />
            <span v-else>{{ row.defaultPercent1 }}</span>
          </template>
          <template #defaultPercent2="{ row }">
            <div v-if="editRow[row.id]">
              <vxe-input
                v-if="editRow[row.id].supplierCount >= 2"
                v-model="editRow[row.id].defaultPercent2"
                size="small"
              />
            </div>
            <span v-else>{{ row.defaultPercent2 }}</span>
          </template>
          <template #defaultPercent3="{ row }">
            <div v-if="editRow[row.id]">
              <vxe-input
                v-if="editRow[row.id].supplierCount >= 3"
                v-model="editRow[row.id].defaultPercent3"
                size="small"
              />
            </div>
            <span v-else>{{ row.defaultPercent3 }}</span>
          </template>
          <template #defaultPercent4="{ row }">
            <div v-if="editRow[row.id]">
              <vxe-input
                v-if="editRow[row.id].supplierCount >= 4"
                v-model="editRow[row.id].defaultPercent4"
                size="small"
              />
            </div>
            <span v-else>{{ row.defaultPercent4 }}</span>
          </template>
          <template #defaultPercent5="{ row }">
            <div v-if="editRow[row.id]">
              <vxe-input
                v-if="editRow[row.id].supplierCount >= 5"
                v-model="editRow[row.id].defaultPercent5"
                size="small"
              />
            </div>
            <span v-else>{{ row.defaultPercent5 }}</span>
          </template>
          <template #defaultPercent6="{ row }">
            <div v-if="editRow[row.id]">
              <vxe-input
                v-if="editRow[row.id].supplierCount >= 6"
                v-model="editRow[row.id].defaultPercent6"
                size="small"
              />
            </div>
            <span v-else>{{ row.defaultPercent6 }}</span>
          </template>
          <template #defaultPercent7="{ row }">
            <div v-if="editRow[row.id]">
              <vxe-input
                v-if="editRow[row.id].supplierCount >= 7"
                v-model="editRow[row.id].defaultPercent7"
                size="small"
              />
            </div>
            <span v-else>{{ row.defaultPercent7 }}</span>
          </template>
          <template #defaultPercent8="{ row }">
            <div v-if="editRow[row.id]">
              <vxe-input
                v-if="editRow[row.id].supplierCount >= 8"
                v-model="editRow[row.id].defaultPercent8"
                size="small"
              />
            </div>
            <span v-else>{{ row.defaultPercent8 }}</span>
          </template>
          <template #defaultPercent9="{ row }">
            <div v-if="editRow[row.id]">
              <vxe-input
                v-if="editRow[row.id].supplierCount >= 9"
                v-model="editRow[row.id].defaultPercent9"
                size="small"
              />
            </div>
            <span v-else>{{ row.defaultPercent9 }}</span>
          </template>
          <template #defaultPercent10="{ row }">
            <div v-if="editRow[row.id]">
              <vxe-input
                v-if="editRow[row.id].supplierCount >= 10"
                v-model="editRow[row.id].defaultPercent10"
                size="small"
              />
            </div>
            <span v-else>{{ row.defaultPercent10 }}</span>
          </template>
          <template #operation="{ row, rowIndex }">
            <div class="editable-row-operations" v-if="type != 'detail'">
              <span v-if="editRow[row.id]">
                <a @click="() => handleSave(editRow[row.id], rowIndex)">{{ $t('确定') }}</a>
                <a @click="() => cancelEdit(row, rowIndex)">{{ $t('取消') }}</a>
              </span>
              <span v-else>
                <a @click="() => handleEdit(row, rowIndex)">{{ $t('编辑') }}</a>
              </span>
            </div>
          </template>
        </sc-table>
      </div>
    </div>
  </div>
</template>

<script>
import { i18n } from '@/main.js'
import ScTable from '@/components/ScTable/src/index'
import { vxeColumns } from './config/detail.js'
import { statusList } from './config'
import { cloneDeep, throttle, maxBy, uniqBy } from 'lodash'
export default {
  components: {
    ScTable
  },
  data() {
    return {
      toolbar: [
        {
          code: 'Add',
          name: this.$t('新增'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Delete',
          name: this.$t('删除'),
          icon: 'el-icon-delete',
          status: 'info'
        }
      ],
      type: 'add', //add 新增、update创建新版本 、detail详情 权限不同 详情只能查看  新增和创建新版本可以编辑和保存 提交审批
      editRow: {}, // 当前编辑的行对象 使用枚举 可保存多行数据
      supplierCountList: [
        //供应商数量
        { label: '1', value: 1 },
        { label: '2', value: 2 },
        { label: '3', value: 3 },
        { label: '4', value: 4 },
        { label: '5', value: 5 },
        { label: '6', value: 6 },
        { label: '7', value: 7 },
        { label: '8', value: 8 },
        { label: '9', value: 9 },
        { label: '10', value: 10 }
      ],
      disabled: false, //禁用
      buList: [], //事业部门
      statusList,
      formObject: {
        //表单数据
        id: null, // 预设配额单ID
        orgId: null, //公司
        orgName: null, //公司名称
        orgCode: null, //公司code
        title: null, //预设配额标题
        code: null, //预设配额单号
        sourceCode: null, //历史单据号
        status: 0, //单据状态
        createUserName: null, //创建人
        createTime: null, //创建时间
        remark: null //备注
      },
      formRules: {
        orgId: [{ required: true, message: i18n.t('请选择事业部：'), trigger: 'change' }],
        title: [{ required: true, message: i18n.t('请输入预设配额标题：'), trigger: 'change' }]
      },
      vxeColumns, //table列配置
      dataSource: [], //table数据
      isEdit: false
    }
  },
  computed: {
    isEnableStatus() {
      return (this.formObject.status == 0 || this.formObject.status == 2) && this.type !== 'detail'
    },
    pageType() {
      return this.$route.query?.type === 'add'
    }
  },
  created() {
    this.getOrgList()
  },
  mounted() {
    let query = this.$route?.query
    this.type = query?.type
    //id存在 为创建新版本
    if (query?.type !== 'add') {
      this.disabled = query?.type === 'update' || query?.type === 'detail'
      //根据id查询详情 数据回显
      this.getDetail(query.id)
      //获取列表数据
      this.getList(query.id)
    }
  },
  methods: {
    // 获取事业部数据
    async getOrgList() {
      const { code, data } = await this.$API.quotaDefault.queryOrg()
      if (code === 200) {
        this.buList = data
        if (!this.formObject.orgId && this.buList && this.buList.length === 1) {
          this.formObject.orgId = this.buList[0]['id']
          this.formObject.orgName = this.buList[0]['orgName']
          this.formObject.orgCode = this.buList[0]['orgCode']
        }
      }
    },
    //事业部下拉
    changeBu(e) {
      const { itemData } = e
      this.formObject.orgId = itemData.id
      this.formObject.orgName = itemData.orgName
      this.formObject.orgCode = itemData.orgCode
    },
    //根据id获取详情数据 transfer 在创建新版本时 保存后刷新不转移code和id
    getDetail(id, transfer = true) {
      let params = {
        id
      }
      //请求详情接口
      this.$API.quotaDefault.getDetails(params).then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.formObject = data
          if (this.type === 'update' && transfer) {
            //创建新版本 历史单据号为原来的单据号 历史id为原来的id， 创建时间 创建人 状态清空
            this.formObject.sourceCode = this.formObject.code
            this.formObject.sourceId = this.formObject.id
            this.formObject.code = null
            this.formObject.id = null
            this.formObject.createTime = null
            this.formObject.createUserName = null
            this.formObject.status = null
          }
        }
      })
    },
    //获取表格数据
    getList(id) {
      let params = {
        id,
        page: { current: 1, size: 9999 }
      }
      //请求列表接口
      this.$API.quotaDefault.queryList(params).then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.dataSource = data.records
          this.dataSource.forEach((item) => {
            for (let i = 1; i <= item.supplierCount; i++) {
              const percens = JSON.parse(item.quotaPercentJson || JSON.stringify([]))
              item[`defaultPercent${i}`] = percens[i - 1]['quotaPercent']
            }
          })
          //this.editRow 枚举里的对象删除 取消编辑状态
          for (let item in this.editRow) {
            delete this.editRow[item]
          }
        }
      })
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      if (code === 'Add') {
        this.handleAdd()
      } else if (code === 'Delete') {
        const selectedRows = $grid.getCheckboxRecords() // 表格勾选行的内容
        this.handleDelete(selectedRows)
      }
    },
    //新增
    handleAdd() {
      //最大行数为10
      if (this.dataSource.length >= 10) {
        this.$toast({
          content: this.$t('最多只能添加10行'),
          type: 'warning'
        })
        return
      }
      //生成随机字符串为id
      const obj = {
        id: (Math.random() + new Date().getTime()).toString(32).slice(0, 8),
        supplierCount: this.dataSource.length + 1, //列表数量
        defaultPercent1: '',
        defaultPercent2: '',
        defaultPercent3: '',
        defaultPercent4: '',
        defaultPercent5: '',
        defaultPercent6: '',
        defaultPercent7: '',
        defaultPercent8: '',
        defaultPercent9: '',
        defaultPercent10: '',
        type: 'add' //新数据 未保存
      }
      this.dataSource.push(obj)
      this.handleEdit(obj) //开启编辑模式
    },
    // 删除
    handleDelete(selectedRows) {
      //选中判断
      if (selectedRows.length <= 0) {
        this.$toast({
          content: this.$t('请勾选需要操作的数据'),
          type: 'warning'
        })
        return
      }
      //如果sourceId有值且id为空 表示为创建新版本但未保存 提示保存后再删除
      if (this.formObject.sourceId && !this.formObject.id) {
        this.$toast({
          content: this.$t('创建新版本时，请先保存后再删除'),
          type: 'warning'
        })
        return
      }
      /*
       * 选中的行 逻辑判断
       * 1.选中行 全部为新增未保存的行 询问确认删除选中的数据后, 直接删除
       * 2.选中的行 包含新增未保存的数据和已保存的数据 判断
       *  2.1 新增未保存的行数 = 全部新增未保存的行数 && 包含已保存的数据 询问确认删除选中的数据后，调用接口删除
       *  2.2 新增未保存的行数 < 全部新增未保存的行数 && 包含已保存的数据 询问 是否删除 删除会丢失未保存的新增数据，调用接口删除
       */
      let tips = this.$t('确认删除选中的数据吗？')
      const addRow = selectedRows.filter((item) => item.type === 'add') // 选中的 新增未保存的行
      const saveRow = selectedRows.filter((item) => item.type != 'add') // 选中的 已保存的行
      // 1.选中行 全部为新增未保存的行 询问确认删除选中的数据后, 直接删除
      if (addRow.length === selectedRows.length) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: tips
          },
          success: () => {
            addRow.forEach((item) => {
              this.dataSource.splice(this.dataSource.indexOf(item), 1)
              this.$set(this.editRow, item.id, null) //将 editRow 中当前行的数据清空 更新视图
              delete this.editRow[item.id]
            })
          }
        })
      } else {
        //新增未保存的行数 < 全部新增未保存的行数 && 包含已保存的数据 询问 是否删除 删除会丢失未保存的新增数据，调用接口删除
        let isAllDiscard = false //是否全部丢弃
        if (
          addRow.length < this.dataSource.filter((item) => item.type === 'add').length &&
          saveRow.length > 0
        ) {
          isAllDiscard = true
          tips = this.$t('删除会丢失未保存的新增数据，确认删除吗？')
        }
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: tips
          },
          success: () => {
            const params = {
              code: this.formObject.code,
              detailIdList: saveRow.map((item) => item.id),
              id: this.formObject.id
            }
            this.$API.quotaDefault.detailBatchDelete(params).then(() => {
              this.$toast({
                content: this.$t('删除成功'),
                type: 'success'
              })
              if (isAllDiscard) {
                //全部丢弃 编辑模式保存的内容也要清空
                this.dataSource.forEach((item) => {
                  this.$set(this.editRow, item.id, null)
                  delete this.editRow[item.id]
                })
              } else {
                //部分丢弃 将 editRow 中当前行的数据清空 更新视图
                selectedRows.forEach((item) => {
                  this.$set(this.editRow, item.id, null)
                  delete this.editRow[item.id]
                })
              }
              //删除选中的数据
              selectedRows.forEach((item) => {
                this.dataSource.splice(this.dataSource.indexOf(item), 1)
              })
            })
          }
        })
      }
    },
    //开启当前行编辑
    handleEdit(row) {
      let editRow = cloneDeep(row)
      this.$set(this.editRow, editRow.id, editRow)
    },
    //取消编辑
    cancelEdit(row) {
      this.$set(this.editRow, row.id, null) // 取消编辑时将 editRow 中当前行的数据清空 更新视图
      delete this.editRow[row.id] //删除 editRow 中当前行的数据
    },
    //明细列 - 保存编辑  只保存数据在页面内 不提交接口
    handleSave(row, index) {
      this.$set(this.editRow, row.id, null) // 取消编辑时将 editRow 中当前行的数据清空 更新视图
      this.$set(this.dataSource, index, row) // 更新分页列表视图
    },
    //校验明细列表
    checkList(row) {
      //校验 根据supplierCount 将defaultPercent1-10转化为quotaList数组
      let count = Number(row.supplierCount)
      let quotaList = []
      for (let i = 1; i <= count; i++) {
        quotaList.push({
          index: i - 1,
          quotaPercent: row[`defaultPercent${i}`]
        })
      }
      //校验quotaList数组 quotaPercent是否为空 且是否为数字 且是否大于0 且是否小于100 且是否为整数 且所有的值相加是否为100
      let flag = true
      let sum = 0
      quotaList.forEach((item) => {
        if (item.quotaPercent == '' || item.quotaPercent == null) {
          flag = false
          this.$toast({
            content: this.$t(`预设配额${item.index + 1}不能为空`),
            type: 'warning'
          })
          return
        }
        if (isNaN(item.quotaPercent)) {
          flag = false
          this.$toast({
            content: this.$t(`预设配额${item.index + 1}必须为数字`),
            type: 'warning'
          })
          return
        }
        if (Number(item.quotaPercent) <= 0) {
          flag = false
          this.$toast({
            content: this.$t(`预设配额${item.index + 1}必须大于0`),
            type: 'warning'
          })
          return
        }
        if (Number(item.quotaPercent) > 100) {
          flag = false
          this.$toast({
            content: this.$t(`预设配额${item.index + 1}必须小于100`),
            type: 'warning'
          })
          return
        }
        //整数 不包含小数点
        if (item.quotaPercent.toString().indexOf('.') != -1) {
          flag = false
          this.$toast({
            content: this.$t(`预设配额${item.index + 1}必须为整数,且不包含小数`),
            type: 'warning'
          })
          return
        }
        sum += Number(item.quotaPercent)
      })
      if (flag && sum !== 100) {
        flag = false
        this.$toast({
          content: this.$t(`供应商数量为${row.supplierCount}，预设配额值不为100，请确认`),
          type: 'warning'
        })
      }
      // 预设配额1>=预设配额2>=预设配额3.....>=预设配额10
      if (flag) {
        for (let i = 1; i < row.supplierCount; i++) {
          if (Number(row[`defaultPercent${i}`]) < Number(row[`defaultPercent${i + 1}`])) {
            flag = false
            this.$toast({
              content: this.$t(
                `供应商数量为${row.supplierCount}，预设配额${i}必须大于等于预设配额${i + 1}`
              ),
              type: 'warning'
            })
          }
        }
      }
      return {
        flag: flag,
        quotaList,
        supplierCount: row.supplierCount
      }
    },
    //保存详情页
    getDetailData() {
      //校验表单
      let ruleForm = false
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          ruleForm = true
        }
      })
      if (!ruleForm) {
        return
      }
      if (this.dataSource.length == 0) {
        this.$toast({
          content: this.$t('请添加预设配额明细'),
          type: 'warning'
        })
        return
      }

      let list = [] //深拷贝
      //this.dataSource数组为原始数据，this.editRow枚举为编辑数据 两者合并
      this.dataSource.forEach((item) => {
        if (this.editRow[item.id]) {
          list.push(cloneDeep(this.editRow[item.id]))
        } else {
          list.push(cloneDeep(item))
        }
      })
      //校验 供应商数量不能重复
      if (list.length != uniqBy(list, 'supplierCount').length) {
        this.$toast({
          content: this.$t('供应商数量不能重复'),
          type: 'warning'
        })
        return
      }
      //校验 供应商最大数量 = this.dataSource.length
      let maxCount = maxBy(list, 'supplierCount').supplierCount
      if (maxCount != list.length) {
        this.$toast({
          content: this.$t('供应商数量必须连续'),
          type: 'warning'
        })
        return
      }
      //循环校验list每一行数据 checkList
      let flag = true
      list.forEach((item) => {
        let result = this.checkList(item)
        if (result.flag == false) {
          flag = false
          return
        }
        //flag 校验通过 ，转换为后台需要的数据 defaultPercent1-10 转换为 quotaList
        let count = Number(item.supplierCount)
        let quotaList = []
        for (let i = 1; i <= count; i++) {
          quotaList.push({
            index: i,
            quotaPercent: Number(item[`defaultPercent${i}`])
          })
        }
        item.quotaList = quotaList
        delete item.quotaPercentJson
        //type为add时，是新数据 没有id
        if (item.type === 'add') {
          delete item.id
        }
      })
      if (flag == false) {
        return
      }
      let form = { ...this.formObject }
      let params = {}
      //新增的保存数据
      if (this.type === 'add') {
        delete form.createTime
        delete form.createUserName
        delete form.sourceCode
        params = {
          ...form,
          detailList: list
        }
      } else {
        //编辑的保存数据
        params = {
          ...this.formObject,
          detailList: list
        }
      }
      return params
    },
    save: throttle(function () {
      const params = this.getDetailData()
      if (!params) return
      this.$API.quotaDefault.save(params).then((res) => {
        if (res.code === 200) {
          this.getDetail(res.data, false)
          this.getList(res.data)
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
        }
      })
    }, 1000), //提交详情页
    submit: throttle(function () {
      const params = this.getDetailData()
      if (!params) return
      this.$dialog({
        data: {
          title: this.$t('操作'),
          message: this.$t(`确认提交吗？`)
        },
        success: () => {
          this.$API.quotaDefault.commit(params).then((res) => {
            if (res.code === 200) {
              this.$toast({
                content: this.$t('提交成功'),
                type: 'success'
              })
              this.$bus.$emit('refreshQuotaDefualtList')
              this.backToBusinessConfig()
            }
          })
        }
      })
    }, 1000), //提交详情页
    //返回预设配额列表页
    backToBusinessConfig() {
      this.$router.go(-1)
    },
    //供应商数量下拉
    handlesupplierCountChange(e, row) {
      this.$set(row, 'supplierCount', e.value) //  更新视图
      //预设配额1-10的值清空
      for (let i = 1; i <= 10; i++) {
        this.$set(row, 'defaultPercent' + i, '')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .custom-table-container .ant-table-body tr td {
  height: 42px;
}
.button-group {
  display: flex;
  padding: 8px;
}
/deep/.mt-form-item {
  width: calc(30% - 20px);
  min-width: 200px;
  display: inline-flex;
  margin-right: 20px;
  .mt-input {
    width: 100%;
  }
}
.supplier-box {
  position: absolute;
  width: 100%;
  min-height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: #fff;
  overflow: auto;
  -webkit-overflow-scrolling: scroll;

  .miam-container {
    width: 100%;
    background: #fff;
    padding: 0 20px;
    .header {
      height: 40px;
      display: flex;
      justify-content: space-between;
      .title {
        height: 100%;
        line-height: 40px;
        font-size: 20px;
        font-weight: 600;
        color: #333;
        padding: 5px 10px;
      }
      .operate-bar {
        height: 100%;
        float: right;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        color: #4f5b6d;

        .op-item {
          cursor: pointer;
          align-items: center;
          margin-right: 20px;
          align-items: center;
          background-color: rgba(0, 0, 0, 0);
          border-color: rgba(0, 0, 0, 0);
          color: #00469c;
        }
      }
    }
    .mian-info {
      background: rgba(255, 255, 255, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    }

    .relation-ships {
      background: rgba(255, 255, 255, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      margin-top: 20px;
      // height: calc(100% - 164px);
    }
  }
  .hr {
    margin: 10px 0;
    padding: 0;
    border: 0;
    border-top: 1px solid #e9e9e9;
  }
}
::v-deep.red-color {
  color: red;
}
</style>
