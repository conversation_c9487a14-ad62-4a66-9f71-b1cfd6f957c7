import { i18n } from '@/main.js'
import utils from '@/utils/utils'
export const vxeColumns = [
  {
    type: 'checkbox',
    width: '50'
  },
  {
    // title: i18n.t('事业部'),
    field: 'orgName',
    width: 150,
    align: 'left',
    slots: { header: 'factoryHead', default: 'orgNameDefault' }
  },
  {
    // title: i18n.t('限制类型'),
    field: 'constraintType',
    width: 120,
    align: 'left',
    slots: { header: 'constraintTypeHead', default: 'constraintTypeDefault' }
  },
  {
    // title: i18n.t('限制等级'),
    field: 'constraintLevel',
    width: 120,
    align: 'left',
    slots: { header: 'constraintLevelHead', default: 'constraintLevelDefault' }
  },
  {
    // title: i18n.t('是否可再分配'),
    field: 'isDistribute',
    width: 120,
    align: 'left',
    slots: { header: 'isDistributeHead', default: 'isDistributeDefault' }
  },
  {
    title: i18n.t('说明'),
    field: 'description',
    width: 200,
    align: 'left',
    slots: { default: 'descriptionDefault' }
  },
  {
    title: i18n.t('生效日期'),
    field: 'effectiveDate',
    width: 200,
    align: 'left'
    // scopedSlots: { customRender: 'effectiveDate' }
  },
  {
    title: i18n.t('失效日期'),
    field: 'invalidDate',
    width: 200,
    align: 'left'
    // scopedSlots: { customRender: 'invalidDate' }
  },
  { title: i18n.t('失效原因'), field: 'invalidReason', align: 'left' },
  {
    title: i18n.t('状态'),
    field: 'status',
    align: 'left',
    formatter: ({ cellValue }) => {
      const statusOptions = utils.getQuotaDict('QUOTA_STRATEGY_SHORT_STATUS') || []
      let item = statusOptions.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  },
  { title: i18n.t('创建人'), field: 'createUserName', align: 'left' },
  { title: i18n.t('创建时间'), field: 'createTime', align: 'left' },
  { title: i18n.t('最后更新人'), field: 'updateUserName', align: 'left' },
  {
    title: i18n.t('操作'),
    field: 'operation',
    fixed: 'right',
    slots: { default: 'operationDefault' }
  }
]
