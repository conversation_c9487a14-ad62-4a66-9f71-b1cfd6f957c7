<template>
  <div class="quato-customization">
    <collapse-search :is-grid-display="true" @reset="reset" @search="handleSearch">
      <mt-form ref="ruleForm" :model="queryForm">
        <mt-form-item prop="orgName" :label="$t('事业部')" label-style="top">
          <mt-select
            v-model="queryForm.buId"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="orgList"
            :fields="{ text: 'orgName', value: 'id' }"
            :placeholder="$t('请选择事业部')"
            @change="orgChange($event)"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="constraintType" :label="$t('限制类型')" label-style="top">
          <mt-select
            v-model="queryForm.constraintType"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="constraintTypeList"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('请选择限制类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-select
            v-model="queryForm.status"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="statusList"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('发布状态')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="effectiveDate" :label="$t('生效日期')" label-style="top">
          <mt-date-range-picker
            v-model="queryForm.effectiveDate"
            :separator="$t('至')"
            :placeholder="$t('请选择生效日期')"
            format="yyyy-MM-dd"
            :open-on-focus="true"
          ></mt-date-range-picker>
        </mt-form-item>
        <mt-form-item prop="invalidDate" :label="$t('失效日期')" label-style="top">
          <mt-date-range-picker
            v-model="queryForm.invalidDate"
            :separator="$t('至')"
            :placeholder="$t('请选择失效日期')"
            format="yyyy-MM-dd"
            :open-on-focus="true"
          ></mt-date-range-picker>
        </mt-form-item>
        <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
          <mt-input v-model="queryForm.createUserName" :placeholder="$t('请输入创建人')" />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!----  表格  --------->
    <sc-table
      ref="xTable"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="vxeColumns"
      :table-data="dataSource"
      @refresh="search"
      class="components-table-demo-nested"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
          >{{ item.name }}</vxe-button
        >
      </template>
      <template #factoryHead="{}"> <span class="red-color">*</span>{{ $t('事业部') }} </template>
      <template #orgNameDefault="{ row }">
        <vxe-select
          v-if="row.editable"
          v-model="row.orgCode"
          :options="orgList"
          :option-props="{
            label: 'orgName',
            value: 'orgCode'
          }"
          @change="orgChange($event.value, row)"
          transfer
          filterable
        ></vxe-select>
        <span v-else>{{ row.orgName }}</span>
      </template>
      <template #constraintTypeHead="{}">
        <span class="red-color">*</span>{{ $t('限制类型') }}
      </template>
      <template #constraintTypeDefault="{ row }">
        <vxe-select
          v-if="row.editable"
          v-model="row.constraintType"
          :options="constraintTypeList"
          :option-props="{
            label: 'dictName',
            value: 'dictCode'
          }"
          @change="cellChange($event.value, 'constraintType', row)"
          transfer
          filterable
        ></vxe-select>
        <span v-else>{{ cellDisplay(constraintTypeList, row.constraintType) }}</span>
      </template>
      <template #constraintLevelHead="{}">
        <span class="red-color">*</span>{{ $t('限制等级') }}</template
      >
      <template #constraintLevelDefault="{ row }">
        <vxe-select
          v-if="row.editable"
          v-model="row.constraintLevel"
          :options="constraintLevelList"
          :option-props="{
            label: 'dictName',
            value: 'dictCode'
          }"
          @change="cellChange($event.value, 'constraintLevel', row)"
          transfer
          filterable
        ></vxe-select>
        <span v-else>{{ cellDisplay(constraintLevelList, row.constraintLevel) }}</span>
      </template>
      <template #isDistributeHead="{}">
        <span class="red-color">*</span>{{ $t('是否可再分配') }}</template
      >
      <template #isDistributeDefault="{ row }">
        <vxe-select
          v-if="row.editable"
          v-model="row.isDistribute"
          :options="isDistributeList"
          :option-props="{
            label: 'dictName',
            value: 'dictCode'
          }"
          @change="cellChange($event.value, 'isDistribute', row)"
          transfer
          filterable
        ></vxe-select>
        <span v-else>{{ cellDisplay(isDistributeList, row.isDistribute) }}</span>
      </template>
      <template #descriptionHead="{}"> <span class="red-color"></span>{{ $t('说明') }}</template>
      <template #descriptionDefault="{ row }">
        <vxe-input
          v-if="row.editable"
          size="small"
          :max-length="200"
          v-model="row.description"
        ></vxe-input>
        <span v-else>{{ row.description }}</span>
      </template>
      <template #operationDefault="{ row, rowIndex }">
        <div class="editable-row-operations">
          <span v-if="row.editable">
            <a @click="() => save(row, rowIndex)">{{ $t('保存') }}</a>
            <a @click="() => cancelEdit(row, rowIndex)">{{ $t('取消') }}</a>
          </span>
          <span v-else>
            <a :disabled="row.status !== 0" @click="() => edit(row, rowIndex)">{{ $t('编辑') }}</a>
          </span>
        </div>
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import { throttle } from 'lodash'
import moment from 'moment'
import collapseSearch from '@/components/collapseSearch'
import { vxeColumns } from './config'
import ScTable from '@/components/ScTable/src/index'
import { download, getHeadersFileName, formatTime, getQuotaDict } from '@/utils/utils'

export default {
  components: {
    collapseSearch,
    ScTable
  },
  data() {
    return {
      loading: false,
      toolbar: [
        {
          code: 'Add',
          name: this.$t('新增'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Effect',
          name: this.$t('生效'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Invalid',
          name: this.$t('失效'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Import',
          name: this.$t('导入'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Delete',
          name: this.$t('删除'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Export',
          name: this.$t('导出'),
          icon: 'el-icon-delete',
          status: 'info'
        }
      ],
      queryForm: {
        buId: '', // 事业部id
        constraintType: '', // 限制类型
        status: '', // 状态
        effectiveDate: null, // 生效日期
        invalidDate: null, //失效日期
        createUserName: '', // 创建人
        currentPage: 1,
        pageSize: 10
      },
      cacheQueryForm: {},
      pageSettings: {
        pageCount: 5,
        pageSize: 20, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [10, 20, 50, 100]
      },
      orgList: [], //事业部列表数据
      constraintLevelList: getQuotaDict('QUOTA_STRATEGY_CONSTRAINT_LEVEL'),
      constraintTypeList: getQuotaDict('QUOTA_STRATEGY_XZPEYXJ_TYPE'),

      isDistributeList: getQuotaDict('QUOTA_STRATEGY_PRIORITY_IS_DISTRIBUTE'),
      statusList: getQuotaDict('QUOTA_STRATEGY_SHORT_STATUS'),
      vxeColumns,
      dataSource: [],
      isEdit: false,
      cacheRow: {},
      validRules: {
        orgName: {
          required: true,
          tips: this.$t('请选择事业部')
        },
        constraintType: {
          required: true,
          tips: this.$t('请选择限制类型')
        },
        constraintLevel: {
          required: true,
          tips: this.$t('请选择限制等级')
        },
        isDistribute: {
          required: true,
          tips: this.$t('请选择是否可再分配')
        }
      }
    }
  },
  created() {
    this.initData()
    this.search()
  },
  methods: {
    moment,
    // 数据初始化
    initData() {
      this.cacheQueryForm = Object.assign({}, this.queryForm)
      this.getOrgList()
    },
    // 数据查询
    search(pageSettings) {
      const _queryForm = this.getParams()
      const _params = {
        ..._queryForm,
        page: {
          current: pageSettings ? this.pageSettings.current : 1,
          size: this.pageSettings.pageSize
        }
      }
      this.loading = true
      this.$API.quotaPriority
        .getPriorityList(_params)
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            const total = res?.data?.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)
            this.dataSource = data.records
            this.isEdit = false
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 获取事业部数据
    getOrgList() {
      this.$API.quotaPriority.getOrgList().then((res) => {
        this.$set(this, 'orgList', res.data || [])
      })
    },
    // 操作
    handleClickToolBar(args) {
      const { code, $grid } = args
      if (code === 'Add') {
        this.handleAdd()
        return
      }
      const selectedRows = $grid.getCheckboxRecords() // 表格勾选行的内容
      if (
        (code === 'Effect' || code === 'Invalid' || code === 'Delete') &&
        selectedRows.length <= 0
      ) {
        this.$toast({
          content: this.$t('请勾选需要操作的数据'),
          type: 'warning'
        })
        return
      }
      if (this.isEdit === true) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('存在未保存的数据，是否继续当前操作')
          },
          success: () => {
            this.eventHandle(code, selectedRows)
          }
        })
        return
      }
      this.eventHandle(code, selectedRows)
    },
    //新增
    handleAdd() {
      if (this.isEdit === true) {
        this.$toast({
          content: this.$t('新增失败，存在未保存的数据'),
          type: 'warning'
        })
        return
      }
      const obj = {
        editable: true,
        orgName: '',
        constraintType: '',
        constraintLevel: '',
        isDistribute: '',
        description: '',
        effectiveDate: '',
        invalidDate: '',
        status: '',
        createUserName: '',
        createTime: '',
        updateUserName: ''
      }
      if (!obj.orgId && this.orgList && this.orgList.length === 1) {
        obj.orgName = this.orgList[0]['orgName']
        obj.orgId = this.orgList[0]['id']
        obj.orgCode = this.orgList[0]['orgCode']
      }
      this.isEdit = true
      this.dataSource.unshift(obj)
    },
    // 删除
    handleDelete(selectList) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          this.dataRequest(selectList, 'delete')
        }
      })
    },
    // 生效
    handleEffect(selectList) {
      this.dataRequest(selectList, 'effect')
    },
    // 失效
    handleInvalid(selectList) {
      const data = {
        title: this.$t('失效提示'),
        labelName: this.$t('失效原因')
      }
      this.$dialog({
        modal: () => import('@/components/inputDialog/index.vue'),
        data,
        success: (e) => {
          this.dataRequest(selectList, 'invalid', e.inputContent)
        }
      })
    },
    // 导入
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('上传/导入'),
          importApi: this.$API.quotaPriority.excelImport,
          downloadTemplateApi: this.$API.quotaPriority.excelTemplate
        },
        success: () => {
          // 导入之后刷新列表
          this.search()
        }
      })
    },
    // 导出
    handleExport: throttle(function () {
      const _queryForm = this.getParams()
      this.$API.quotaPriority.excelExport({ ..._queryForm }).then((res) => {
        this.$toast({
          type: 'success',
          content: this.$t('导出成功，请稍后！')
        })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      })
    }, 1000),
    // 数据编辑
    edit(row) {
      if (row.status !== 0) {
        this.$toast({
          content: this.$t('此状态不可编辑'),
          type: 'warning'
        })
        return
      }
      if (this.isEdit === true) {
        this.$toast({
          content: this.$t('请先保存或取消编辑当前数据'),
          type: 'warning'
        })
        return
      }

      this.isEdit = true
      this.cacheRow = JSON.parse(JSON.stringify(row))
      this.$set(row, 'editable', true)
    },
    // 取消编辑
    cancelEdit(row, index) {
      this.$set(row, 'editable', false)
      this.isEdit = false
      this.$set(this.dataSource, index, this.cacheRow)
      if (row.id?.includes('row_')) {
        this.dataSource.splice(index, 1)
      }
    },
    // 数据保存
    save(row) {
      if (!this.validData(row)) return
      const params = {
        ...row
      }
      if (params.id?.includes('row_')) {
        delete params.id
      }
      this.$API.quotaPriority.savePriority([params]).then((res) => {
        const { code } = res
        if (code === 200) {
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
          this.cacheRow = {}
          // 重新请求数据
          this.search()
        }
      })
    },
    // 数据校验
    validData(data) {
      let flag = true
      for (let i in this.validRules) {
        if (!data[i] && data[i] !== 0 && this.validRules[i]?.required) {
          this.$toast({
            content: this.$t(this.validRules[i].tips),
            type: 'warning'
          })
          flag = false
          return false
        }
      }

      return flag
    },

    // 重置
    reset() {
      if (this.isEdit === true) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('存在未保存的数据，是否继续当前操作')
          },
          success: () => {
            this.queryForm = Object.assign({}, this.cacheQueryForm)
            this.search()
          }
        })
        return
      }
      this.queryForm = Object.assign({}, this.cacheQueryForm)
      this.search()
    },
    // 查询
    handleSearch() {
      if (this.isEdit === true) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('存在未保存的数据，是否继续当前操作')
          },
          success: () => {
            this.search()
          }
        })
        return
      }
      this.search()
    },

    // 事业部chang事件
    orgChange(e, row) {
      // 事业部下拉变更
      if (row) {
        // 表格内的下拉选择
        for (let i = 0; i < this.orgList.length; i++) {
          const element = this.orgList[i]
          if (element.orgCode === e) {
            row.orgName = element.orgName
            row.orgId = element.id
            row.orgCode = element.orgCode
            return
          }
        }
      } else {
        // 表单内的下拉选择 使用的是mt组件api与vxe不同
        const { itemData } = e
        this.queryForm.buId = itemData.id
      }
    },
    // 表格下拉框
    cellChange(e, type, row) {
      const typeMap = {
        constraintType: 'constraintTypeList',
        constraintLevel: 'constraintLevelList',
        isDistribute: 'isDistributeList'
      }

      const _find = this[typeMap[type]].find((item) => item.dictCode === e)
      row[type] = _find.dictCode
    },
    // 切换page
    handleCurrentChange(currentPage) {
      if (this.isEdit === true) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('存在未保存的数据，是否继续当前操作')
          },
          success: () => {
            this.pageSettings.current = currentPage
            this.search('pageSettings')
          }
        })
        return
      }
      this.pageSettings.current = currentPage
      this.search('pageSettings')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      if (this.isEdit === true) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('存在未保存的数据，是否继续当前操作')
          },
          success: () => {
            this.pageSettings.pageSize = pageSize
            this.search()
          }
        })
        return
      }
      this.pageSettings.pageSize = pageSize
      this.search()
    },
    // 单元格显示控制
    cellDisplay(list, val) {
      const _item = list.find((item) => item.dictCode === val)
      return _item?.dictName
    },
    // 事件操作
    eventHandle(code, selectedRows) {
      let _selectGridRecords = selectedRows
      let notStatus0Records = _selectGridRecords.filter((item) => item.status !== 0) // 非拟定状态数据
      let notStatus1Records = _selectGridRecords.filter((item) => item.status !== 1) // 非有效状态数据
      if (code === 'Effect') {
        // 生效
        if (notStatus0Records.length > 0) {
          this.$toast({ content: this.$t('只能生效拟定状态数据'), type: 'warning' })
          return
        }
        this.handleEffect(_selectGridRecords)
      } else if (code === 'Invalid') {
        // 失效
        if (notStatus1Records.length > 0) {
          this.$toast({ content: this.$t('只能失效有效状态数据'), type: 'warning' })
          return
        }
        this.handleInvalid(_selectGridRecords)
      } else if (code === 'Delete') {
        // 删除
        if (notStatus0Records.length > 0) {
          this.$toast({ content: this.$t('只能删除拟定状态数据'), type: 'warning' })
          return
        }
        this.handleDelete(_selectGridRecords)
      } else if (code === 'Import') {
        // 导入
        this.handleImport()
      } else if (code === 'Export') {
        // 导出
        this.handleExport()
      }
    },
    // 数据请求 Del:删除 Enable:生效 Disable:失效
    dataRequest(selectList, type, remark) {
      const idList = selectList.map((item) => item.id)
      const methodMap = {
        delete: 'delPriority',
        effect: 'enablePriority',
        invalid: 'disabledPriority'
      }

      const params = { idList }
      if (type === 'invalid') {
        params.remark = remark
      }

      this.$API.quotaPriority[methodMap[type]](params).then(() => {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        this.search()
      })
    },
    getParams() {
      const _queryForm = {
        ...this.queryForm,
        effectiveDateStart: this.queryForm.effectiveDate
          ? formatTime(this.queryForm.effectiveDate[0]) + ' 00:00:00'
          : '',
        effectiveDateEnd: this.queryForm.effectiveDate
          ? formatTime(this.queryForm.effectiveDate[1]) + ' 23:59:59'
          : '',
        invalidDateStart: this.queryForm.invalidDate
          ? formatTime(this.queryForm.invalidDate[0]) + ' 00:00:00'
          : '',
        invalidDateEnd: this.queryForm.invalidDate
          ? formatTime(this.queryForm.invalidDate[1]) + ' 23:59:59'
          : ''
      }
      delete _queryForm.effectiveDate
      delete _queryForm.invalidDate
      return _queryForm
    }
  }
}
</script>

<style lang="scss" scoped>
.quato-customization {
  margin-top: 24px;
  background: #fff;
  padding: 12px;
  border-radius: 8px;
  height: 100%;
  .button-group {
    display: flex;
    padding: 0 0 8px;
  }
}
.components-table-demo-nested {
  ::v-deep.red-color {
    color: red;
  }
}
</style>
