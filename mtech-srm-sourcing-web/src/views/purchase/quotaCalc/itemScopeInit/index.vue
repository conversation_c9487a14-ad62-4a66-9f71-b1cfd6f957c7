<!--
 * @Author: wenjie20.wang <EMAIL>
 * @Date: 2022-09-21 16:43:23
 * @LastEditors: wenjie20.wang <EMAIL>
 * @LastEditTime: 2022-09-30 11:32:00
 * @FilePath: \mtech-srm-sourcing-web\src\views\purchase\quotaCalc\itemScopeInit\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="item-scopeInit">
    <collapse-search :is-grid-display="true" @reset="reset" @search="handleSearch">
      <mt-form ref="ruleForm" :model="queryForm">
        <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
          <mt-select
            v-model="queryForm.companyCode"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="companyList"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            :placeholder="$t('请选择公司')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="siteCode" :label="$t('工厂代码')" label-style="top">
          <mt-select
            v-model="queryForm.siteCode"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="factoryList"
            :fields="{ text: 'orgCode', value: 'orgCode' }"
            :placeholder="$t('请选择工厂代码')"
            @change="handleFactoryChange($event)"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="categoryCode" :label="$t('品类')" label-style="top">
          <magnifier-input
            ref="categoryCode"
            :config="categoryDialogCofig"
            @change="categoryCodeChange($event)"
          ></magnifier-input>
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商')" label-style="top">
          <magnifier-input
            ref="supplierCode"
            :config="supplierDialogCofig"
            @change="supplierCodeChange($event)"
          ></magnifier-input>
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
          <magnifier-input
            ref="itemCode"
            :config="materialDialogCofig"
            @change="materialCodeChange($event)"
          ></magnifier-input>
        </mt-form-item>
        <mt-form-item prop="purGroupName" :label="$t('采购组')" label-style="top">
          <mt-input v-model="queryForm.purGroupName" :placeholder="$t('请输入创建人')" />
        </mt-form-item>
        <mt-form-item prop="buyerUserName" :label="$t('采购开发')" label-style="top">
          <mt-input v-model="queryForm.buyerUserName" :placeholder="$t('请输入创建人')" />
        </mt-form-item>
        <mt-form-item prop="month" :label="$t('筛选月份')" label-style="top">
          <mt-date-picker
            v-model="queryForm.month"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :placeholder="$t('选择筛选月份')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="commitStatus" :label="$t('确认状态')" label-style="top">
          <mt-select
            v-model="queryForm.commitStatus"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="commitStatusOptions"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('确认状态')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="unCommitType" :label="$t('未自动筛选原因')" label-style="top">
          <mt-select
            v-model="queryForm.unCommitType"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="unCommitTypeOptions"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('未自动筛选原因')"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!----  表格  --------->
    <sc-table
      ref="xTable"
      grid-id="58d914fb-d3b0-4bb1-b689-b914a4f6eecf"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="vxeColumns"
      :table-data="dataSource"
      @refresh="search"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
          >{{ item.name }}</vxe-button
        >
      </template>
      <template #submitCode="{ row }">
        <div class="editable-row-operations">
          <span>
            <a @click="() => handleDetail(row)">{{ row.submitCode }}</a>
          </span>
        </div></template
      >
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import moment from 'moment'
import utils from '@/utils/utils'
import { getHeadersFileName, download } from '@/utils/utils'
import collapseSearch from '@/components/collapseSearch'
import magnifierInput from '@/components/magnifierInput'
import { pageConfig, vxeColumns } from './config'
import ScTable from '@/components/ScTable/src/index'
import dayjs from 'dayjs'
export default {
  components: {
    collapseSearch,
    magnifierInput,
    ScTable
  },
  data() {
    return {
      loading: false,
      toolbar: [
        {
          code: 'Export',
          name: this.$t('导出'),
          icon: 'el-icon-delete',
          status: 'info'
        }
      ],
      queryForm: {
        siteCode: '', // 组织
        categoryCode: '', // 品类
        itemCode: '', // 物料编码
        supplierCode: '', //供应商
        purGroupName: '', //采购组
        buyerUserName: '', //采购开发
        month: this.getNextMonth(), // 筛选月份
        commitStatus: 1, // 确认状态
        unCommitType: '' // 未自动筛选原因
      },
      pageSettings: {
        pageCount: 5,
        pageSize: 20, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [10, 20, 50, 100]
      },
      vxeColumns,
      dataSource: [],
      factoryList: [],
      companyList: [],
      // 物料放大镜弹窗配置
      materialDialogCofig: {
        pageConfig: pageConfig(
          `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
          'material'
        ),
        text: 'itemCode',
        value: 'itemCode'
      },
      // 品类放大镜弹窗配置
      categoryDialogCofig: {
        pageConfig: pageConfig(
          '/masterDataManagement/tenant/permission/queryCategories',
          'category'
        ),
        text: 'categoryName',
        value: 'categoryCode'
      },
      // 供应商放大镜弹窗配置
      supplierDialogCofig: {
        pageConfig: pageConfig(
          '/masterDataManagement/tenant/supplier/pagedQueryByOrgCode',
          'supplier'
        ),
        text: 'supplierName',
        value: 'supplierCode'
      },
      dictItems: [],
      unCommitTypeOptions: utils.getQuotaDict('QUOTA_ITEM_UN_COMMIT_TYPE') || [],
      commitStatusOptions: utils.getQuotaDict('QUOTA_ITEM_COMMIT_STATUS') || []
    }
  },
  // watch: {
  //   $route() {
  //     console.log(this.$route.query.status, '跳转过来的参数')
  //   }
  // },
  activated() {
    this.setCommitStatus()
  },
  created() {
    // this.$route.query.status ? (this.queryForm.commitStatus = this.$route.query.status) : ''
    this.setCommitStatus()
    this.getCompanyList()
    this.getFactoryList()
    this.search()
  },
  methods: {
    getNextMonth() {
      let nowMonthDate = new Date()
      let getMonth = nowMonthDate.getMonth() + 1
      nowMonthDate.setMonth(getMonth, 1)
      nowMonthDate.setDate(1) //set设置时间
      nowMonthDate.setHours(0)
      nowMonthDate.setSeconds(0)
      nowMonthDate.setMinutes(0)
      return nowMonthDate
    },
    setCommitStatus() {
      console.log(this.$route.query.status, '跳转过来的参数')
      if (this.$route.query && this.$route.query.status) {
        this.queryForm.commitStatus = Number(this.$route.query.status)
      }
    },
    isEqual(key) {
      if (
        key === 'siteCode' ||
        key === 'categoryCode' ||
        key === 'itemCode' ||
        key === 'supplierCode' ||
        key === 'commitStatus' ||
        key === 'unCommitType' ||
        key === 'companyCode'
      ) {
        return true
      }
      return false
    },
    getParams(pageSettings) {
      const params = {
        // ...this.queryForm,
        page: {
          current: pageSettings ? this.pageSettings.current : 1,
          size: this.pageSettings.pageSize
        }
      }
      const rules = []
      const keys = Object.keys(this.queryForm)
      const values = Object.values(this.queryForm)
      for (let i = 0; i < values.length; i++) {
        const element = values[i]
        if ((element || element === 0) && keys[i] !== 'createTime') {
          let obj = {
            field: keys[i],
            label: '',
            operator: this.isEqual(keys[i]) ? 'equal' : 'contains',
            type: 'string',
            value: keys[i] === 'month' ? dayjs(element).format('YYYY-MM') : element
          }
          rules.push(obj)
        }
      }
      if (rules.length) {
        params.condition = 'and'
        params.rules = rules
      }
      return params
    },
    // 获取公司值集
    getCompanyList() {
      this.$API.itemScopeInit.getPermissionCompanyList({}).then((res) => {
        this.companyList = res.data
      })
    },
    // 获取工厂值集
    getFactoryList() {
      this.$API.itemScopeInit.getPermissionSiteList({}).then((res) => {
        this.factoryList = res.data
      })
    },
    moment,
    // 查询
    handleSearch() {
      console.log(this.queryForm, '表单数据')
      this.search()
    },
    // 查询方法
    search(pageSettings) {
      // console.log('search', this.queryForm)
      const params = this.getParams(pageSettings)
      // if (params.month) {
      //   params.month = dayjs(params.month).format('YYYY-MM')
      // }
      console.log(params.month, '选择的月份')
      this.loading = true
      this.$API.itemScopeInit
        .getItemScopeInitList(params)
        .then((res) => {
          console.log(res)
          const { code, data } = res
          if (code === 200) {
            const total = res?.data?.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)
            this.dataSource = data.records
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 重置
    reset() {
      console.log('重置')
      this.queryForm = {
        siteCode: '', // 组织
        categoryCode: '', // 品类
        itemCode: '', // 物料编码
        supplierCode: '', //供应商
        purGroupName: '', //采购组
        buyerUserName: '', //采购开发
        month: this.getNextMonth(), // 筛选月份
        commitStatus: 1, // 确认状态
        unCommitType: '' // 未自动筛选原因
      }
      this.$refs.itemCode.handleClear()
      this.$refs.supplierCode.handleClear()
      this.$refs.categoryCode.handleClear()
      this.search()
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageSettings.current = currentPage
      this.search('pageSettings')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.search()
    },
    handleClickToolBar(args) {
      const { code } = args
      if (code === 'Export') {
        this.handleExport()
      }
    },
    // 导出
    handleExport() {
      //调用接口
      const params = this.getParams()
      this.$API.itemScopeInit.excelExport(params).then((res) => {
        const fileName = getHeadersFileName(res)
        //下载文件
        download({
          fileName,
          blob: res.data
        })
      })
    },
    handleFactoryChange(e) {
      // 工厂下拉变更
      const { itemData } = e
      this.queryForm.siteCode = itemData.orgCode
    },
    // 物料编码变更
    materialCodeChange(e) {
      // 表单内物料编码变更
      this.queryForm.itemCode = e.itemCode
    },
    // 品类变更
    categoryCodeChange(e) {
      this.queryForm.categoryCode = e.categoryCode
    },
    // 供应商编码变更
    supplierCodeChange(e) {
      // 表单内物料编码变更
      this.queryForm.supplierCode = e.supplierCode
    }
  }
}
</script>

<style lang="scss" scoped>
.item-scopeInit {
  // margin-top: 24px;
  background: #fff;
  padding: 12px;
  border-radius: 8px;
  height: 100%;
  .button-group {
    display: flex;
    padding: 0 0 8px;
  }
}
.components-table-demo-nested {
  // ::v-deep.ant-table-body {
  //   overflow-x: auto !important;
  // }
  ::v-deep.red-color {
    color: red;
  }
}
</style>
