import { i18n } from '@/main.js'
import utils from '@/utils/utils'
const toolbar = [
  // { id: "Add", icon: "icon_solid_Createorder", title: i18n.t("新增") },
  // { id: "Stop", icon: "icon_solid_Cancel", title: i18n.t("取消") },
  // { id: "Submit", icon: "icon_solid_Submit", title: i18n.t("提交") },
  // { id: "Import", icon: "icon_solid_Createorder", title: i18n.t("导入") },
]
const materialColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    cssClass: 'field-content',
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  }
]
const categoryColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    cssClass: 'field-content'
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]
const supplierColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    cssClass: 'field-content'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  }
]
export const pageConfig = (url, columnType, params) => {
  let columnData = []
  let gridId = {
    material: 'a285fb6b-2fe7-48c4-b1a7-de19a245c836',
    category: 'ad08056e-5956-43bb-a0c1-557fa49949f5',
    supplier: '8a9e0537-cd75-4139-8ab1-21194284497e'
  }
  if (columnType === 'material') {
    columnData = materialColumn
  } else if (columnType === 'category') {
    columnData = categoryColumn
  } else {
    columnData = supplierColumn
  }
  let asyncConfig = {
    url
  }
  if (params) {
    asyncConfig.params = params
  }
  return [
    {
      toolbar,
      useToolTemplate: false,
      gridId: gridId[columnType],
      grid: {
        allowFiltering: true,
        columnData,
        allowSorting: false,
        allowSelection: true,
        selectionSettings: {
          type: 'Multiple',
          mode: 'Row'
        },
        asyncConfig
      }
    }
  ]
}

export const vxeColumns = [
  {
    title: i18n.t('工厂代码'),
    field: 'siteCode',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('工厂名称'),
    field: 'siteName',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('筛选月份'),
    field: 'month',
    minWidth: 150,
    align: 'left',
    formatter: ({ cellValue }) => {
      let date = ''
      if (cellValue && new Date(cellValue)) {
        date = utils.formatTime(new Date(cellValue), 'YYYY-mm')
      }
      return date
    }
  },
  {
    title: i18n.t('品类'),
    field: 'categoryName',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('采购组'),
    field: 'purGroupName',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('采购开发'),
    field: 'buyerUserName',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('物料编码'),
    field: 'itemCode',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('物料名称'),
    field: 'itemName',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('供应商名称'),
    field: 'supplierName',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('供应商编码'),
    field: 'supplierCode',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('确认状态'),
    field: 'commitStatus',
    minWidth: 100,
    align: 'left',
    formatter: ({ cellValue }) => {
      const options = utils.getQuotaDict('QUOTA_ITEM_COMMIT_STATUS') || []
      let item = options.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  },
  {
    title: i18n.t('近期交易量'),
    field: 'quantity',
    minWidth: 100,
    align: 'left'
  },
  { title: i18n.t('确认时间'), field: 'commitTime', minWidth: 150, align: 'left' },
  {
    title: i18n.t('未自动筛选原因'),
    field: 'unCommitType',
    minWidth: 150,
    align: 'left',
    formatter: ({ cellValue }) => {
      const options = utils.getQuotaDict('QUOTA_ITEM_UN_COMMIT_TYPE') || []
      let item = options.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  }
]
