<template>
  <div class="quato-customization">
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="reset"
      @search="search"
    >
      <mt-form ref="ruleForm" :model="queryForm">
        <mt-form-item prop="quotaCode" :label="$t('配额协议编码')">
          <mt-input v-model="queryForm.quotaCode" :placeholder="$t('请输入配额协议编码')" />
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')">
          <magnifier-input
            ref="itemCode"
            :config="materialDialogCofig"
            @change="materialCodeChange($event)"
          ></magnifier-input>
        </mt-form-item>
        <mt-form-item prop="siteCode" :label="$t('工厂代码')">
          <mt-select
            v-model="queryForm.siteCode"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="factoryList"
            :fields="{ text: 'orgCode', value: 'orgCode' }"
            :placeholder="$t('请选择工厂代码')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="syncStatus" :label="$t('SAP同步结果')">
          <mt-select
            v-model="queryForm.syncStatus"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="syncStatusList"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('请选择SAP同步结果')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')">
          <mt-date-range-picker
            v-if="isReloadPicker"
            v-model="queryForm.createTime"
            format="yyyy-MM-dd"
            :separator="$t('至')"
            :placeholder="$t('选择创建时间')"
          ></mt-date-range-picker>
        </mt-form-item>
        <mt-form-item prop="syncScmStatus" :label="$t('SCM同步结果')">
          <mt-select
            v-model="queryForm.syncScmStatus"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="syncStatusList"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('请选择SCM同步结果')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('状态')">
          <mt-select
            v-model="queryForm.status"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="statusOptions"
            :fields="{ text: 'text', value: 'code' }"
            :placeholder="$t('状态')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="validateTime" :label="$t('有效日期')">
          <mt-date-picker
            v-model="queryForm.validateTime"
            :open-on-focus="true"
            :placeholder="$t('请选择有效日期')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商编码')">
          <magnifier-input
            ref="supplierName"
            :config="supplierDialogCofig"
            @change="supplierCodeChange($event)"
          ></magnifier-input>
        </mt-form-item>
        <mt-form-item prop="sourceCode" :label="$t('来源单号')">
          <mt-input v-model="queryForm.sourceCode" :placeholder="$t('请输入来源单号')" />
        </mt-form-item>
        <mt-form-item prop="sourceType" :label="$t('创建方式')">
          <mt-select
            v-model="queryForm.sourceType"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="typeOptions"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('创建方式')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="categoryName" :label="$t('品类')">
          <magnifier-input
            ref="categoryName"
            :config="categoryDialogCofig"
            @change="categoryCodeChange($event)"
          ></magnifier-input>
        </mt-form-item>
        <!-- <mt-form-item prop="buyerUserName" :label="$t('采购开发')">
          <mt-input v-model="queryForm.buyerUserName" :placeholder="`请输入采购开发`" />
        </mt-form-item> -->
        <mt-form-item prop="purType" :label="$t('采购类型')">
          <mt-select
            v-model="queryForm.purType"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="purTypeList"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('请选择采购类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="quoteAttribute" :label="$t('特殊采购/报价属性')">
          <mt-select
            v-model="queryForm.quoteAttribute"
            :allow-filtering="true"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('请选择特殊采购/报价属性')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="purSiteCode" :label="$t('采购工厂')">
          <mt-select
            v-model="queryForm.purSiteCode"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="factoryList"
            :fields="{ text: 'orgCode', value: 'orgCode' }"
            :placeholder="$t('请选择工厂代码')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="createUserName" :label="$t('创建人')">
          <mt-input v-model="queryForm.createUserName" :placeholder="$t('请输入创建人')" />
        </mt-form-item>
        <mt-form-item prop="syncInfo" :label="$t('SAP返回信息')">
          <mt-input v-model="queryForm.syncInfo" :placeholder="$t('请输入SAP返回信息')" />
        </mt-form-item>
        <mt-form-item prop="syncScmMsg" :label="$t('SCM返回信息')">
          <mt-input v-model="queryForm.syncScmMsg" :placeholder="$t('请输入SCM返回信息')" />
        </mt-form-item>
        <mt-form-item prop="quotaName" :label="$t('配额协议描述')">
          <mt-input v-model="queryForm.quotaName" :placeholder="$t('请输入配额协议描述')" />
        </mt-form-item>
      </mt-form>
    </collapse-search>

    <!----  表格  --------->
    <sc-table
      ref="xTable"
      grid-id="b6ae97e7-6580-40dd-8918-ca08ccff6965"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="vxeColumns"
      :table-data="dataSource"
      @refresh="search"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import utils from '@/utils/utils'
import collapseSearch from '@/components/collapseSearch'
import magnifierInput from '@/components/magnifierInput'
import ScTable from '@/components/ScTable/src/index'
import {
  pageConfig,
  vxeColumns,
  statusOptions,
  typeOptions,
  purTypeList,
  quoteAttributeList,
  syncStatusList
} from './config'
export default {
  components: {
    collapseSearch,
    magnifierInput,
    ScTable
  },
  data() {
    return {
      loading: false,
      toolbar: [
        {
          code: 'Export',
          name: this.$t('导出'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'SyncSap',
          name: this.$t('同步SAP'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'SyncScm',
          name: this.$t('同步SCM'),
          icon: 'el-icon-delete',
          status: 'info'
        }
      ],
      syncStatusList,
      quoteAttributeList,
      purTypeList,
      queryForm: {
        siteCode: '', // 工厂id
        status: '', // 状态
        itemCode: '', // 物料id
        // supplierName: '',
        supplierCode: '', // 供应商编码
        sourceCode: '', // 来源单号
        sourceType: '', // 创建方式
        purType: '', // 采购类型
        quoteAttribute: '', // 特殊采购/报价属性
        purSiteCode: '', // 采购工厂
        syncInfo: '', // 返回信息
        quotaName: '', // 配额协议描述
        syncStatus: '', // 同步结果
        categoryName: '', // 品类
        buyerUserName: '', // 采购开发
        validateTime: '', // 有效日期
        createUserName: '', // 创建人
        createTime: '' // 创建时间
      },
      pageSettings: {
        pageCount: 5,
        pageSize: 20, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [10, 20, 50, 100]
      },
      statusOptions,
      typeOptions, // 创建方式
      vxeColumns,
      dataSource: [],
      factoryList: [],
      // 物料放大镜弹窗配置
      materialDialogCofig: {
        pageConfig: pageConfig(
          `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
          'material'
        ),
        text: 'itemCode',
        value: 'itemCode'
      },
      // 品类放大镜弹窗配置
      categoryDialogCofig: {
        pageConfig: pageConfig(
          '/masterDataManagement/tenant/permission/queryCategories',
          'category'
        ),
        text: 'categoryName',
        value: 'categoryCode'
      },
      // 供应商放大镜弹窗配置
      supplierDialogCofig: {
        pageConfig: pageConfig(
          '/masterDataManagement/tenant/supplier/pagedQueryByOrgCode',
          'supplier'
        ),
        text: 'supplierName',
        value: 'supplierCode'
      },
      isReloadPicker: true
    }
  },
  created() {
    this.getFactoryList()
    // this.$set(this.queryForm, 'createTime', this.getTimeList())
    // this.search()
  },
  methods: {
    // 获取默认创建时间，默认查三个月（跨度）
    getTimeList() {
      let _date = new Date()
      _date.setMonth(_date.getMonth() - 2)
      _date.setDate(1)
      return [_date, new Date()]
    },
    getFactoryList() {
      this.$API.customization.getPermissionSiteList({}).then((res) => {
        this.factoryList = res.data
      })
    },
    isEqual(key) {
      if (
        key === 'siteCode' ||
        key === 'status' ||
        key === 'itemCode' ||
        key === 'categoryName' ||
        key === 'supplierCode' ||
        key === 'purType' ||
        key === 'quoteAttribute' ||
        key === 'purSiteCode' ||
        key === 'syncStatus' ||
        key === 'quotaCode'
      ) {
        return true
      }
      return false
    },
    getParams(pageSettings) {
      const params = {
        // ...this.queryForm,
        page: {
          current: pageSettings ? this.pageSettings.current : 1,
          size: this.pageSettings.pageSize
        }
      }
      const rules = []
      const keys = Object.keys(this.queryForm)
      const values = Object.values(this.queryForm)
      for (let i = 0; i < values.length; i++) {
        const element = values[i]
        if ((element || element === 0) && keys[i] !== 'createTime') {
          if (keys[i] === 'validateTime') {
            let date = utils.formatTime(new Date(element), 'YYYY-mm-dd')
            let obj = {
              field: 'validStartTime',
              label: '',
              operator: 'lessthanorequal',
              type: 'string',
              value: new Date(`${date} 00:00:00`).getTime()
            }
            let obj1 = {
              field: 'validEndTime',
              label: '',
              operator: 'greaterthanorequal',
              type: 'string',
              value: new Date(`${date} 00:00:00`).getTime()
            }
            rules.push(obj)
            rules.push(obj1)
          } else {
            let obj = {
              field: keys[i],
              label: '',
              operator: this.isEqual(keys[i])
                ? 'equal'
                : keys[i] === 'sourceCode' //来源单号特殊处理为lkeright
                ? 'likeright'
                : 'contains',
              type: 'string',
              value: element
            }
            rules.push(obj)
          }
        }
      }
      if (this.queryForm.createTime && this.queryForm.createTime.length) {
        for (let i = 0; i < this.queryForm.createTime.length; i++) {
          let element = `${new Date(
            utils.formatTime(new Date(this.queryForm.createTime[0]), 'YYYY-mm-dd HH:MM:SS')
          ).getTime()}`
          // let element = utils.formatTime(
          //   new Date(this.queryForm.createTime[0]),
          //   'YYYY-mm-dd HH:MM:SS'
          // )
          if (i === 1) {
            element = `${utils.formatTime(
              new Date(this.queryForm.createTime[1]),
              'YYYY-mm-dd'
            )} 23:59:59`
            element = `${new Date(element).getTime()}`
          }
          let obj = {
            field: 'createTime',
            label: '',
            operator: i === 0 ? 'greaterthanorequal' : 'lessthanorequal',
            type: 'string',
            value: element
          }
          rules.push(obj)
        }
      }
      if (rules.length) {
        params.condition = 'and'
        params.rules = rules
      }
      return params
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRows = $grid.getCheckboxRecords()
      switch (code) {
        case 'Export':
          this.handleExport()
          break
        case 'SyncSap':
          this.handleSync('SAP', selectedRows)
          break
        case 'SyncScm':
          this.handleSync('SCM', selectedRows)
          break
        default:
          break
      }
    },
    // 同步SAP、SCM
    handleSync(type, list) {
      if (list.length == 0) {
        this.$toast({
          content: this.$t('请至少选择选择一行数据'),
          type: 'warning'
        })
        return
      }
      const ids = list.map((item) => item.id)
      const funcName = type === 'SAP' ? 'pricerecordQuota' : 'pricerecordQuotaSyncScm'
      this.$store.commit('startLoading')
      this.$API.quotaConfig[funcName]({ ids })
        .then((res) => {
          if (res.code == 200) {
            this.$toast({
              content: this.$t(`同步${type}申请提交成功！`),
              type: 'success'
            })
            this.search()
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    // 导出
    handleExport() {
      const params = this.getParams()
      this.$API.quotaConfig.itemExport(params).then((res) => {
        this.$toast({
          type: 'success',
          content: this.$t('导出成功，请稍后！')
        })
        let fileName = utils.getHeadersFileName(res)
        utils.download({ fileName: fileName, blob: res.data })
      })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageSettings.current = currentPage
      this.search('pageSettings')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.search()
    },
    reset() {
      this.isReloadPicker = false
      this.queryForm = {
        siteCode: '', // 工厂id
        status: '', // 状态
        itemCode: '', // 物料id
        // supplierName: '',
        supplierCode: '', // 供应商编码
        sourceCode: '', // 来源单号
        sourceType: '', // 创建方式
        purType: '', // 采购类型
        quoteAttribute: '', // 特殊采购/报价属性
        purSiteCode: '', // 采购工厂
        syncInfo: '', // 返回信息
        quotaName: '', // 配额协议描述
        syncStatus: '', // 同步结果
        categoryName: '', // 品类
        buyerUserName: '', // 采购开发
        validateTime: '', // 有效日期
        createUserName: '', // 创建人
        createTime: '' // 创建时间
      }
      this.$refs.itemCode.handleClear()
      this.$refs.supplierName.handleClear()
      this.$refs.categoryName.handleClear()
      this.$nextTick(() => {
        this.isReloadPicker = true
      })
      this.dataSource = []
      // this.search()
    },
    search(pageSettings) {
      const notEmpty = (obj) => {
        for (const key in obj) {
          const value = obj[key]
          if (
            Object.hasOwnProperty.call(obj, key) &&
            ((typeof value === 'string' && value.trim() !== '') ||
              (typeof value === 'object' && value && value?.length !== 0) ||
              typeof value === 'number')
          ) {
            return false
          }
        }
        return true
      }
      const canSearch = notEmpty(this.queryForm)
      if (canSearch) {
        this.$toast({
          type: 'warning',
          content: this.$t('请选择至少一个查询条件')
        })
        return
      }
      const params = this.getParams(pageSettings)
      this.loading = true
      this.$API.quotaConfig
        .queryQuotaDetailList(params)
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            const total = res?.data?.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)
            this.dataSource = data.records
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    materialCodeChange(e) {
      // 物料编码变更
      this.queryForm.itemCode = e.itemCode
    },
    categoryCodeChange(e) {
      // 品类变更
      this.queryForm.categoryName = e.categoryName
    },
    supplierCodeChange(e) {
      // this.queryForm.supplierName = e.supplierName
      this.queryForm.supplierCode = e.supplierCode
    }
  }
}
</script>

<style lang="scss" scoped>
.quato-customization {
  // margin-top: 24px;
  background: #fff;
  padding: 12px;
  border-radius: 8px;
  height: 100%;
  .button-group {
    display: flex;
    padding: 0 0 8px;
  }
}
.components-table-demo-nested {
  // ::v-deep.ant-table-body {
  //   overflow-x: auto !important;
  // }
  ::v-deep.red-color {
    color: red;
  }
}
</style>
