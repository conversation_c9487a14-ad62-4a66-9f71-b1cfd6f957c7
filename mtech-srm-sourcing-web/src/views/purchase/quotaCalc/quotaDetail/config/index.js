import { i18n } from '@/main.js'
import utils from '@/utils/utils'
const toolbar = [
  // { id: "Add", icon: "icon_solid_Createorder", title: i18n.t("新增") },
  // { id: "Stop", icon: "icon_solid_Cancel", title: i18n.t("取消") },
  // { id: "Submit", icon: "icon_solid_Submit", title: i18n.t("提交") },
  // { id: "Import", icon: "icon_solid_Createorder", title: i18n.t("导入") },
]
const materialColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    cssClass: 'field-content',
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  }
]
const categoryColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    cssClass: 'field-content'
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]
const supplierColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    cssClass: 'field-content'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  }
]
export const pageConfig = (url, columnType, params) => {
  let columnData = []
  let gridId = {
    material: '57b5ab24-4352-4ab6-b81c-5890df7e3831',
    category: 'e4ffe020-2a3c-4b23-b155-0f03c3b59e1e',
    supplier: '8413d483-11ee-4454-a957-535a3a387eef'
  }
  if (columnType === 'material') {
    columnData = materialColumn
  } else if (columnType === 'category') {
    columnData = categoryColumn
  } else {
    columnData = supplierColumn
  }
  return [
    {
      toolbar,
      useToolTemplate: false,
      gridId: gridId[columnType],
      grid: {
        allowFiltering: true,
        columnData,
        allowSorting: false,
        allowSelection: true,
        selectionSettings: {
          type: 'Multiple',
          mode: 'Row'
        },
        asyncConfig: { url, params }
      }
    }
  ]
}

export const statusOptions = [
  { text: i18n.t('有效'), code: 1 },
  { text: i18n.t('失效'), code: 2 },
  { text: i18n.t('供应商未合格'), code: 3 }
]

export const typeOptions = [
  { label: i18n.t('手动创建'), value: 0 },
  { label: i18n.t('寻源生成'), value: 1 },
  { label: i18n.t('配额自动制定'), value: 2 }
]

export const purTypeList = utils.getQuotaDict('PROCUREMENT TYPE')

export const quoteAttributeList = utils.getQuotaDict('SPECIAL PUR TYPE')

export const syncStatusList = [
  { value: 0, label: i18n.t('无需同步') },
  { value: 1, label: i18n.t('未同步') },
  { value: 2, label: i18n.t('同步中') },
  { value: 3, label: i18n.t('同步成功') },
  { value: 4, label: i18n.t('同步失败') }
]

export const vxeColumns = [
  {
    type: 'checkbox',
    minWidth: 50
    // fixed: 'left'
  },
  {
    title: i18n.t('SAP同步结果'),
    field: 'syncStatus',
    minWidth: 120,
    align: 'left',
    formatter: ({ cellValue }) => {
      let item = syncStatusList.find((item) => item.value === cellValue)
      return item ? item.label : ''
    }
  },
  {
    title: i18n.t('SAP返回信息'),
    minWidth: 180,
    field: 'syncInfo',
    align: 'left'
  },
  {
    title: i18n.t('SCM同步结果'),
    field: 'syncScmStatus',
    minWidth: 120,
    align: 'left',
    formatter: ({ cellValue }) => {
      let item = syncStatusList.find((item) => item.value === cellValue)
      return item ? item.label : ''
    }
  },
  {
    title: i18n.t('SCM返回信息'),
    minWidth: 180,
    field: 'syncScmMsg',
    align: 'left'
  },
  {
    title: i18n.t('状态'),
    field: 'status',
    minWidth: 70,
    align: 'left',
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.code === cellValue)
      return item ? item.text : ''
    }
  },
  {
    title: i18n.t('工厂代码'),
    field: 'siteCode',
    minWidth: 100,
    align: 'left'
  },
  {
    title: i18n.t('工厂名称'),
    field: 'siteName',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('物料编码'),
    field: 'itemCode',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('物料名称'),
    field: 'itemName',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('供应商编码'),
    field: 'supplierCode',
    minWidth: 110,
    align: 'left'
  },
  {
    title: i18n.t('供应商名称'),
    field: 'supplierName',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('配额'),
    field: 'quotaPercent',
    minWidth: 70,
    align: 'left',
    formatter: ({ cellValue }) => {
      let quotaPercent = ''
      if (cellValue) {
        quotaPercent = cellValue * 100
        quotaPercent = parseInt(quotaPercent) + '%'
      }
      return quotaPercent
    }
  },
  {
    title: i18n.t('生效日期'),
    field: 'validStartTime',
    minWidth: 100,
    align: 'left',
    formatter: ({ cellValue }) => {
      let date = ''
      if (cellValue && new Date(cellValue)) {
        date = utils.formatTime(new Date(cellValue), 'YYYY-mm-dd')
      }
      return date
    }
  },
  {
    title: i18n.t('失效日期'),
    field: 'validEndTime',
    minWidth: 100,
    align: 'left',
    formatter: ({ cellValue }) => {
      let date = ''
      if (cellValue && new Date(cellValue)) {
        date = utils.formatTime(new Date(cellValue), 'YYYY-mm-dd')
      }
      return date
    }
  },
  {
    title: i18n.t('配额协议编码'),
    field: 'quotaCode',
    minWidth: 220,
    align: 'left'
  },
  {
    title: i18n.t('配额协议描述'),
    field: 'quotaName',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('采购类型'),
    field: 'purType',
    align: 'left',
    minWidth: 100,
    formatter: ({ cellValue }) => {
      let item = purTypeList.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  },
  {
    title: i18n.t('特殊采购/报价属性'),
    field: 'quoteAttribute',
    align: 'left',
    minWidth: 160,
    formatter: ({ cellValue }) => {
      let item = quoteAttributeList.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  },
  {
    title: i18n.t('来源单号'),
    field: 'sourceCode',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('品类编码'),
    field: 'categoryCode',
    minWidth: 100,
    align: 'left'
  },
  {
    title: i18n.t('品类名称'),
    field: 'categoryName',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('采购工厂'),
    field: 'purSiteCode',
    align: 'left',
    minWidth: 100
  },
  {
    title: i18n.t('最小起拆量'),
    minWidth: 120,
    field: 'minOpenQuantity',
    align: 'left'
  },
  {
    title: i18n.t('最小采购量'),
    minWidth: 120,
    field: 'minPurchaseQuantity',
    align: 'left'
  },
  {
    title: i18n.t('最小包装量'),
    minWidth: 120,
    field: 'minPackageQuantity',
    align: 'left'
  },
  {
    title: i18n.t('创建人'),
    minWidth: 180,
    field: 'createUserName',
    align: 'left'
  },
  {
    title: i18n.t('创建时间'),
    minWidth: 180,
    field: 'createTime',
    align: 'left'
  },
  {
    title: i18n.t('创建方式'),
    minWidth: 120,
    field: 'sourceType',
    align: 'left',
    formatter: ({ cellValue }) => {
      let item = typeOptions.find((item) => item.value === cellValue)
      return item ? item.label : ''
    }
  },
  {
    title: i18n.t('最后更新时间'),
    minWidth: 180,
    field: 'updateTime',
    align: 'left'
  }
]
