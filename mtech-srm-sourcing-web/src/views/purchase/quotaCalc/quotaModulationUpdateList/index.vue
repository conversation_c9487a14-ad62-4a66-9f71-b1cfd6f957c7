<template>
  <!-- 客户对账协同（供方）-对账单清单列表 -->
  <div class="full-height pt20">
    <local-template-page
      ref="templateRef"
      :current-tab="currentTab"
      :template-config="componentConfig"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickToolBar="handleClickToolBar"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    />
  </div>
</template>

<script>
import { PendingColumnData, FeedbackColumnData, Tab } from './config/constant'
import { formatTableColumnData } from './config/index'
import localTemplatePage from '@/components/template-page'

export default {
  components: {
    localTemplatePage
  },
  data() {
    const currentTab = JSON.parse(localStorage.getItem('tabIndex')) ?? 0

    return {
      feedbackShow: false,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      currentTab, // 当前加载的 Tab 默认 0
      componentConfig: [
        {
          title: this.$t('待查阅'),
          toolbar: [
            {
              id: 'all',
              icon: 'icon_solid_All',
              title: this.$t('全部标记已读')
            }
          ],
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          grid: {
            columnData: PendingColumnData,
            asyncConfig: {
              url: `/price/tenant/price/change/record/queryBuilder`, // queryBuilder查询-待处理对账信息
              defaultRules: [],
              rules: [
                {
                  field: 'isRead',
                  label: this.$t('查阅状态'),
                  operator: 'equal',
                  type: 'string',
                  value: '0'
                }
              ]
              // serializeList: (list) => {
              //   // debugger
              //   list.unshift({
              //     changeInfo: '',
              //     createTime: '',
              //     operation: ''
              //   })
              //   return list
              // }
            }
          }
        },
        {
          title: this.$t('已查阅'),
          toolbar: [],
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置

          grid: {
            columnData: formatTableColumnData({
              tab: Tab.feedback,
              data: FeedbackColumnData
            }),
            dataSource: [],
            allowEditing: true,
            editSettings: {
              allowEditing: true
            },
            asyncConfig: {
              url: `/price/tenant/price/change/record/queryBuilder`, // queryBuilder查询-已反馈对账信息
              defaultRules: [],
              rules: [
                {
                  field: 'isRead',
                  label: this.$t('查阅状态'),
                  operator: 'equal',
                  type: 'string',
                  value: '1'
                }
              ]
              // serializeList: (list) => {
              //   list.unshift({
              //     changeInfo: '',
              //     createTime: '',
              //     operation: ''
              //   })
              //   return list
              // }
            }
          }
        }
      ]
    }
  },
  mounted() {},
  beforeDestroy() {
    localStorage.removeItem('tabIndex')
  },
  methods: {
    handleAddDialogShow() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // CellTool
    handleClickCellTool(e) {
      console.log('eeeeeeeeeeeeeee')
      console.log(e)
      console.log('ereeerrreeeeeee')
      const { id } = e.tool
      const { data } = e
      const params = []
      if (id === 'edit') {
        params.push(data.id)
      }
      this.$API.quotaModulation
        .handleMarkQuotaList(params)
        .then((res) => {
          if (res && res?.code === 200) {
            this.$toast({
              type: 'success',
              content: this.$t('操作成功')
            })
          }
        })
        .catch((err) => {
          this.$toast({
            type: 'error',
            content: err.msg
          })
        })
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // CellTitle
    handleClickCellTitle(e) {
      if (e.field == 'opertaion') {
        console.log('errrrrrrrrrr', e)
      }
    },
    actionBegin(args) {
      // const { rowData } = args
      // if (rowData.oldProductsInvQty == null) {
      //   args.cancel = false
      // } else {
      //   args.cancel = true
      // }
      console.log('args', args)
    },
    actionComplete(args) {
      // const { requestType, data } = args

      // if (requestType == 'save') {
      //   this.apiStartLoading()
      //   this.saveQty(data)
      //   // 调保存接口
      // }
      console.log('argsa', args)
    },
    saveQty(data) {
      this.$API.drawingTogether
        .supViewFeedback({
          drawingUpdateFormId: data.drawingUpdateFormId,
          oldProductsInvQty: data.oldProductsInvQty
        })
        .then((res) => {
          if (res && res?.code == 200) {
            this.$refs.templateRef.refreshCurrentGridData()
            this.apiEndLoading()
          }
        })
    },
    // ToolBar
    handleClickToolBar(e) {
      console.log(e)
      if (e.toolbar.id === 'all') {
        console.log('eeeeeeeeeeeeeee')
        console.log(e)
        console.log('ereeerrreeeeeee')
        const { dataSource } = e.grid
        const params = []
        dataSource && dataSource.map((item) => params.push(item.id))
        this.$API.quotaModulation
          .handleMarkQuotaList(params)
          .then((res) => {
            if (res && res?.code === 200) {
              this.$toast({
                type: 'success',
                content: this.$t('操作成功')
              })
            }
          })
          .catch((err) => {
            this.$toast({
              type: 'error',
              content: err.msg
            })
          })
        this.$refs.templateRef.refreshCurrentGridData()
      }
    },
    // 刷新当前 Grid
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
