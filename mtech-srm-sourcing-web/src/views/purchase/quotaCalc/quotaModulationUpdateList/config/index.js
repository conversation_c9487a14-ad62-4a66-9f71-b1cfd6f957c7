import Vue from 'vue'
import utils from '@/utils/utils'
import { Tab } from './constant'
import { searchOptionsList } from '@/constants/index'

// data: yyyy-mm-dd hh:mm:ss
export const timeToDate = (data) => {
  const { formatString, value } = data
  if (formatString && value) {
    const date = new Date(value)
    if (isNaN(date.getTime())) {
      return value
    } else {
      return utils.formatTime(date, formatString)
    }
  } else {
    return value
  }
}

export const formatTableColumnData = (config) => {
  const { tab, data } = config
  const colData = []
  if (tab === Tab.feedback) {
    // 已反馈 tab
    data.forEach((col) => {
      const defaultCol = {
        ...col,
        field: col.fieldCode,
        headerText: col.fieldName,
        width: '150'
      }
      if (col.fieldCode === 'changeInfo') {
        defaultCol.width = '80%'
      } else if (col.fieldCode === 'createTime') {
        // 创建时间
        defaultCol.width = '20%'
        defaultCol.template = () => {
          return {
            template: Vue.component('createTime', {
              template: `<div><div>{{data.createTime | timeFormat}}</div><div>{{data.createTime | dateFormat}}</div></div>`,
              data: function () {
                return { data: {} }
              },
              mounted() {},
              methods: {},
              filters: {
                dateFormat(value) {
                  return timeToDate({ formatString: 'YYYY-mm-dd', value })
                },
                timeFormat(value) {
                  return timeToDate({ formatString: 'HH:MM:SS', value })
                }
              }
            })
          }
        }
        defaultCol.searchOptions = searchOptionsList.dateRange
      }
      colData.push(defaultCol)
    })
  }
  return colData
}
