import { i18n } from '@/main.js'
import { searchOptionsList } from '@/constants/index'

export const ConstantType = {
  Add: '1', // 新增
  Edit: '2', // 编辑
  Look: '3' // 查看
}

// 对账单确认 页面类型
export const ConstantConfirmDetailType = {
  Confirm: '1', // 确认待反馈 编辑：附件、反馈意见，操作：接受、拒绝
  ConfirmClose: '2', // 确认关闭 不可编辑，操作：接受
  Edit: '3', // 编辑对账单 编辑：附件、备注，操作：提交
  Look: '4' // 查看
}

export const Tab = {
  pending: 0, // 待查阅
  feedback: 1 // 已查阅
}

// 来源途径
export const ConstSourcePath = {
  purchaser: 0, // 采方
  supplier: 1 // 供方
}

// 待查阅
export const PendingColumnData = [
  {
    field: 'changeInfo',
    width: '80%',
    allowEditing: false,
    headerText: i18n.t('内容')
  },
  {
    field: 'createTime',
    allowEditing: false,
    headerText: i18n.t('创建时间'),
    searchOptions: {
      ...searchOptionsList.dateRange
    }
  },
  {
    field: 'operation',
    width: '10%',
    allowEditing: false,
    headerText: i18n.t('操作'),
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('标记已读'),
        visibleCondition: (data) => data['changeInfo'] && data['createTime']
      }
    ]
  }
]

// 已查阅
export const FeedbackColumnData = [
  {
    fieldCode: 'changeInfo',
    fieldName: i18n.t('内容'),
    allowEditing: false
  },
  {
    fieldCode: 'modifyDate',
    fieldName: i18n.t('查阅时间'),
    allowEditing: false
  }
]
