<template>
  <div class="full-height">
    <mt-tabs :e-tab="false" :data-source="pageConfig" @handleSelectTab="handleSelectTab"></mt-tabs>
    <div style="height: calc(100vh - 150px)" v-show="currentTabIndex == 0">
      <quota-apply />
    </div>
    <div style="height: calc(100vh - 150px)" v-show="currentTabIndex == 1">
      <quota-apply-item />
    </div>
  </div>
</template>
<script>
export default {
  components: {
    //配额申请单据
    quotaApply: () => import('./components/quotaApply.vue'),
    quotaApplyItem: () => import('./components/quotaApplyItem.vue')
  },
  data() {
    return {
      currentTabIndex: 0,
      pageConfig: [
        {
          gridId: '48b95ce5-e89a-425d-8a1b-fe57b80a3243',
          title: this.$t('配额申请单据')
        },
        {
          gridId: 'e5bcde7d-aa06-442f-96c3-68c5fe948459',
          title: this.$t('配额申请单据明细查询')
        }
      ]
    }
  },
  methods: {
    handleSelectTab(e) {
      this.currentTabIndex = e
    }
  }
}
</script>
<style lang="scss" scoped></style>
