import { i18n } from '@/main.js'
export const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'ExportFile', icon: 'icon_solid_Createorder', title: i18n.t('导出') },
  { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') }
]
export const columnData = [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    field: 'quotaCode',
    headerText: i18n.t('配额申请编号'),
    cssClass: 'field-content'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'), //单据状态 -1:已停用 0:草稿 1:审批中 2:审批驳回 3:已生效
    valueConverter: {
      type: 'map',
      map: [
        { status: 0, label: i18n.t('草稿'), cssClass: 'title-#9baac1' },
        { status: 1, label: i18n.t('审批中'), cssClass: 'title-#eda133' },
        { status: 2, label: i18n.t('驳回'), cssClass: 'title-#ed5633' },
        { status: 3, label: i18n.t('通过'), cssClass: 'title-#6386c1' },
        { status: 4, label: i18n.t('取消'), cssClass: 'title-#9a9a9a' }
      ],
      fields: { text: 'label', value: 'status' }
    }
  },
  {
    field: 'quotaName',
    headerText: i18n.t('配额申请标题')
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    field: 'operate',
    headerText: i18n.t('操作'),
    ignore: true, //忽略
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          //拟定和已驳回状态可以编辑
          return (
            (data['status'] == '0' || data['status'] == '2') &&
            (JSON.parse(sessionStorage.getItem('userInfo')) || {})?.username ===
              data['createUserName']
          )
        }
      },
      {
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          //拟定和已驳回状态可以删除
          return (
            (data['status'] == '0' || data['status'] == '2') &&
            (JSON.parse(sessionStorage.getItem('userInfo')) || {})?.username ===
              data['createUserName']
          )
        }
      }
    ]
  }
]
export const pageConfig = [
  {
    toolbar,
    useToolTemplate: false,
    gridId: '1c1dea1e-2675-4fdd-83f3-731d6712d744',
    grid: {
      allowFiltering: true,
      columnData,
      asyncConfig: { url: '/price/tenant/quota/query/page' }
    }
  }
]
