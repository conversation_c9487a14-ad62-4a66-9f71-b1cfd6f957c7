import { i18n } from '@/main.js'
import Decimal from 'decimal.js'
import { getQuotaDict } from '@/utils/utils'
const toolbar = []
const materialColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    cssClass: 'field-content',
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  }
]
const categoryColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    cssClass: 'field-content'
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]
const supplierColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    cssClass: 'field-content'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  }
]
export const pageConfig = (url, columnType, params) => {
  let columnData = []
  let gridId = {
    material: '05a12ab5-bed6-4f97-8185-399e0408edf8',
    category: 'a0882733-11a3-454e-9868-bbb0e65061fa',
    supplier: '46c526e7-e4a5-4dda-aedd-2fd441593681'
  }
  if (columnType === 'material') {
    columnData = materialColumn
  } else if (columnType === 'category') {
    columnData = categoryColumn
  } else {
    columnData = supplierColumn
  }
  let asyncConfig = {
    url
  }
  if (params) {
    asyncConfig.params = params
  }
  return [
    {
      toolbar,
      useToolTemplate: false,
      gridId: gridId[columnType],
      grid: {
        allowFiltering: true,
        columnData,
        allowSorting: false,
        allowSelection: true,
        selectionSettings: {
          type: 'Multiple',
          mode: 'Row'
        },
        asyncConfig
      }
    }
  ]
}

export const purTypeList = getQuotaDict('PROCUREMENT TYPE')

export const quoteAttributeList = getQuotaDict('SPECIAL PUR TYPE')

export const vxeColumns = [
  {
    title: i18n.t('状态'), //单据状态 -1:已停用 0:草稿 1:审批中 2:审批驳回 3:已生效
    field: 'status',
    align: 'left',
    minWidth: 80,
    formatter: ({ cellValue }) => {
      const options = [
        { status: 0, label: i18n.t('草稿'), cssClass: 'title-#9baac1' },
        { status: 1, label: i18n.t('审批中'), cssClass: 'title-#eda133' },
        { status: 2, label: i18n.t('驳回'), cssClass: 'title-#ed5633' },
        { status: 3, label: i18n.t('通过'), cssClass: 'title-#6386c1' },
        { status: 4, label: i18n.t('取消'), cssClass: 'title-#9a9a9a' }
      ]
      let item = options.find((item) => item.status === cellValue)
      return item ? item.label : ''
    }
  },

  {
    title: i18n.t('配额申请编号'),
    field: 'quotaCode',
    align: 'left',
    minWidth: 210,
    slots: { default: 'quotaCode' }
  },
  {
    title: i18n.t('配额申请标题'),
    field: 'quotaName',
    align: 'left',
    minWidth: 200
  },
  {
    title: i18n.t('工厂代码'),
    field: 'siteCode',
    align: 'left',
    minWidth: 100
  },
  {
    title: i18n.t('工厂名称'),
    field: 'siteName',
    align: 'left',
    minWidth: 200
  },
  {
    title: i18n.t('物料编码'),
    field: 'itemCode',
    align: 'left',
    minWidth: 150
  },
  {
    title: i18n.t('物料名称'),
    field: 'itemName',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('采购类型'),
    field: 'purType',
    align: 'left',
    minWidth: 100,
    formatter: ({ cellValue }) => {
      let item = purTypeList.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  },
  {
    title: i18n.t('特殊采购/报价属性'),
    field: 'quoteAttribute',
    align: 'left',
    minWidth: 160,
    formatter: ({ cellValue }) => {
      let item = quoteAttributeList.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  },
  {
    title: i18n.t('品类编码'),
    field: 'categoryCode',
    minWidth: 100,
    align: 'left'
  },
  {
    title: i18n.t('品类名称'),
    field: 'categoryName',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('采购工厂'),
    field: 'purSiteCode',
    align: 'left',
    minWidth: 100
  },
  {
    title: i18n.t('供应商编码'),
    field: 'supplierCode',
    align: 'left',
    minWidth: 110
  },
  {
    title: i18n.t('供应商名称'),
    field: 'supplierName',
    align: 'left',
    minWidth: 200
  },
  {
    title: i18n.t('生效日期'),
    field: 'validStartTime',
    align: 'left',
    minWidth: 100,
    formatter: ({ cellValue }) => {
      let str = ''
      if (cellValue) {
        str = cellValue.split(' ')[0]
      }
      return str
    }
  },
  {
    title: i18n.t('失效日期'),
    field: 'validEndTime',
    align: 'left',
    minWidth: 100,
    formatter: ({ cellValue }) => {
      let str = ''
      if (cellValue) {
        str = cellValue.split(' ')[0]
      }
      return str
    }
  },
  {
    title: i18n.t('最小起拆量'),
    field: 'minOpenQuantity',
    align: 'left',
    minWidth: 100
  },
  {
    title: i18n.t('最小包装量'),
    field: 'minPackageQuantity',
    align: 'left',
    minWidth: 100
  },
  {
    title: i18n.t('最小采购量'),
    field: 'minPurchaseQuantity',
    align: 'left',
    minWidth: 100
  },
  {
    title: i18n.t('配额比（%）'),
    field: 'quotaPercent',
    align: 'left',
    minWidth: 110,
    formatter: ({ cellValue }) => {
      let str = ''
      if (cellValue) {
        str = new Decimal(cellValue || 0).mul(new Decimal(100)).toNumber()
      }
      return str
    }
  },
  {
    title: i18n.t('创建人'),
    field: 'createUserName',
    align: 'left',
    minWidth: 80
  },
  {
    title: i18n.t('创建时间'),
    field: 'createTime',
    align: 'left',
    minWidth: 170
  }
]
