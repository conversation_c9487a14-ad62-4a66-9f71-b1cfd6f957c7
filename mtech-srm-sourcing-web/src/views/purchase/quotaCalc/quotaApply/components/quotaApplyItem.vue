<template>
  <div class="quato-customization">
    <collapse-search :is-grid-display="true" @reset="reset" @search="handleSearch">
      <mt-form ref="ruleForm" :model="queryForm">
        <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
          <magnifier-input
            style="width: 100%"
            ref="itemCode"
            :config="materialDialogCofig"
            @change="materialCodeChange($event)"
          ></magnifier-input>
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('单据状态')" label-style="top">
          <mt-select
            style="width: 100%"
            v-model="queryForm.status"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="statusList"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('请选择单据状态')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="quotaCode" :label="$t('配额申请编号')" label-style="top">
          <mt-input v-model="queryForm.quotaCode" style="width: 100%" />
        </mt-form-item>
        <mt-form-item prop="quotaName" :label="$t('配额申请标题')" label-style="top">
          <mt-input v-model="queryForm.quotaName" style="width: 100%" />
        </mt-form-item>
        <mt-form-item prop="siteCode" :label="$t('工厂代码')" label-style="top">
          <mt-select
            style="width: 100%"
            v-model="queryForm.siteCode"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="siteList"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('请选择工厂')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商名称')" label-style="top">
          <magnifier-input
            style="width: 100%"
            ref="supplierCode"
            :config="supplierDialogCofig"
            @change="supplierCodeChange($event)"
          ></magnifier-input>
        </mt-form-item>
        <mt-form-item prop="categoryCode" :label="$t('品类')" label-style="top">
          <magnifier-input
            style="width: 100%"
            ref="categoryCode"
            :config="categoryDialogCofig"
            @change="categoryCodeChange($event)"
          ></magnifier-input>
        </mt-form-item>
        <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
          <mt-input v-model="queryForm.createUserName" style="width: 100%" />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
          <mt-date-range-picker
            v-model="queryForm.createTime"
            :placeholder="$t('请选择')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="purType" :label="$t('采购类型')" label-style="top">
          <mt-select
            style="width: 100%"
            v-model="queryForm.purType"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="purTypeList"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('请选择采购类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="quoteAttribute" :label="$t('特殊采购/报价属性')" label-style="top">
          <mt-select
            style="width: 100%"
            v-model="queryForm.quoteAttribute"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="quoteAttributeList"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('请选择特殊采购/报价属性')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="purSiteCode" :label="$t('采购工厂')" label-style="top">
          <mt-select
            style="width: 100%"
            v-model="queryForm.purSiteCode"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="siteList"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('请选择工厂代码')"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!----  表格  --------->
    <sc-table
      ref="xTable"
      grid-id="2c6cf3d7-ce2a-4928-99c0-c216ba5d00d6"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="vxeColumns"
      :table-data="dataSource"
      @refresh="getList"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
          >{{ item.name }}</vxe-button
        >
      </template>
      <template #quotaCode="{ row }">
        <div class="editable-row-operations">
          <span>
            <a @click="() => handleDetail(row)">{{ row.quotaCode }}</a>
          </span>
        </div></template
      >
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>
<script>
import { i18n } from '@/main.js'
import ScTable from '@/components/ScTable/src/index'
// import { pageConfig } from './../config'
import { vxeColumns, pageConfig, purTypeList, quoteAttributeList } from './../config/item.js'
import { download, getHeadersFileName, formatTime } from '@/utils/utils'
import collapseSearch from '@/components/collapseSearch'
import magnifierInput from '@/components/magnifierInput'

export default {
  components: {
    collapseSearch,
    magnifierInput,
    ScTable
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: (selectedRowKeys, selectedRows) => {
          console.log('选择', selectedRowKeys, selectedRows)
          this.selectedRowKeys = selectedRowKeys
          this.selectedRows = selectedRows
        }
      }
    }
  },
  data() {
    return {
      loading: false,
      toolbar: [
        {
          code: 'Export',
          name: this.$t('导出'),
          icon: 'el-icon-delete',
          status: 'info'
        }
      ],
      purTypeList,
      quoteAttributeList,
      // 物料放大镜弹窗配置
      materialDialogCofig: {
        pageConfig: pageConfig(
          `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
          'material'
        ),
        text: 'itemCode',
        value: 'itemCode'
      },
      // 品类放大镜弹窗配置
      categoryDialogCofig: {
        pageConfig: pageConfig(
          '/masterDataManagement/tenant/permission/queryCategories',
          'category'
        ),
        text: 'categoryName',
        value: 'categoryCode'
      },
      // 供应商放大镜弹窗配置
      supplierDialogCofig: {
        pageConfig: pageConfig(
          '/masterDataManagement/tenant/supplier/pagedQueryByOrgCode',
          'supplier'
        ),
        text: 'supplierName',
        value: 'supplierCode'
      },
      statusList: [
        { value: 0, label: i18n.t('草稿') },
        { value: 1, label: i18n.t('审批中') },
        { value: 2, label: i18n.t('驳回') },
        { value: 3, label: i18n.t('通过') },
        { value: 4, label: i18n.t('取消') }
      ],
      queryForm: {
        status: '',
        quotaCode: '', //配额编码
        quotaName: '', //配额名称
        siteCode: '', //工厂代码
        itemCode: '', //物料编码
        supplierCode: '', //供应商编码
        categoryCode: '', //品类编码
        createUserName: (JSON.parse(sessionStorage.getItem('userInfo')) || {})?.username, //创建人
        purType: '', // 采购类型
        quoteAttribute: '', // 特殊采购/报价属性
        purSiteCode: '', // 采购工厂
        createTime: null //创建时间
      },
      siteList: [], // 工厂列表
      pageConfig: pageConfig(this.$API.quotaConfig.applyItemPage),
      vxeColumns,
      dataSource: [],
      pageSettings: {
        pageCount: 5,
        pageSize: 20, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [10, 20, 50, 100]
      },
      selectedRowKeys: [], // 表格勾选行的id
      selectedRows: [] // 表格勾选行的内容
    }
  },
  async mounted() {
    //工厂
    await this.$API.quotaConfig.getSiteList().then((res) => {
      this.siteList = res.data.records.map((item) => {
        return {
          value: item.siteCode,
          label: item.siteCode
        }
      })
    })
    this.getList()
  },
  methods: {
    //跳转到详情页
    handleDetail(row) {
      this.$router.push({
        name: `quota-apply-detail`,
        query: {
          type: 'detail',
          quotaCode: row.quotaCode,
          timeStamp: new Date().getTime()
        }
      })
    },
    //工厂change
    siteChange(siteCode) {
      console.log(siteCode)
    },
    //物料编码change
    materialCodeChange(e) {
      this.queryForm.itemCode = e.itemCode
    },
    // 品类变更
    categoryCodeChange(e) {
      this.queryForm.categoryCode = e.categoryCode
    },
    //供应商编码change
    supplierCodeChange(e) {
      this.queryForm.supplierCode = e.supplierCode
    },

    //获取列表数据
    getList() {
      //将queryForm的参数转换为mt-template-page查询参数 status单独放在外面
      // let rules = []
      let queryForm = {
        ...this.queryForm
      }
      // status quotaCode quotaName createTime 不转换
      // delete queryForm.status
      // delete queryForm.quotaCode
      // delete queryForm.quotaName
      delete queryForm.createTime

      // for (let key in queryForm) {
      //   //有值的参数才传给后台
      //   if (queryForm[key] || queryForm[key] === 0) {
      //     rules.push({
      //       field: key,
      //       type: 'string',
      //       operator: 'contains',
      //       value: queryForm[key]
      //     })
      //   }
      // }
      const params = {
        condition: 'and',
        page: {
          current: this.pageSettings.current,
          size: this.pageSettings.pageSize
        },
        // rules,
        ...queryForm
      }
      // status quotaCode quotaName createTime 放置在外面
      if (this.queryForm.status || this.queryForm.status === 0) {
        params.status = this.queryForm.status
      }
      if (this.queryForm.quotaCode) {
        params.quotaCode = this.queryForm.quotaCode
      }
      if (this.queryForm.quotaName) {
        params.quotaName = this.queryForm.quotaName
      }
      if (this.queryForm.createTime) {
        //createTime拆成createTimeEnd和createTimeFrom  格式为2022-10-10T03:18:46.249Z .toISOString()
        params.createTimeEnd = `${formatTime(
          new Date(this.queryForm.createTime[1]),
          'YYYY-mm-dd'
        )} 23:59:59`
        params.createTimeFrom = `${formatTime(
          new Date(this.queryForm.createTime[0]),
          'YYYY-mm-dd'
        )} 00:00:00`

        // params.createTime = this.queryForm.createTime
      }
      this.loading = true
      this.$API.quotaConfig
        .applyItemPage(params)
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            const total = res?.data?.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)
            this.dataSource = data.records
            this.isEdit = false
          }
          this.selectedRowKeys = []
          this.selectedRows = []
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 重置
    reset() {
      this.queryForm = {
        status: '',
        quotaCode: '', //配额编码
        quotaName: '', //配额名称
        siteCode: '', //工厂代码
        itemCode: '', //物料编码
        purType: '', // 采购类型
        quoteAttribute: '', // 特殊采购/报价属性
        purSiteCode: '', // 采购工厂
        supplierCode: '', //供应商编码
        categoryCode: '', //品类编码
        createUserName: '', //创建人
        createTime: null //创建时间
      }
      this.$refs.itemCode.handleClear()
      this.$refs.supplierCode.handleClear()
      this.$refs.categoryCode.handleClear()
      this.pageSettings.current = 1
      this.getList()
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageSettings.current = currentPage
      this.getList('pageSettings')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.getList()
    },
    //搜索
    handleSearch() {
      this.pageSettings.current = 1
      this.getList()
    },
    handleClickToolBar(args) {
      const { code } = args
      if (code === 'Export') {
        this.handleExport()
      }
    },
    //导出
    handleExport() {
      //将queryForm的参数转换为mt-template-page查询参数 status单独放在外面
      // let rules = []
      let queryForm = {
        ...this.queryForm
      }
      // status quotaCode quotaName createTime 不转换
      // delete queryForm.status
      // delete queryForm.quotaCode
      // delete queryForm.quotaName
      delete queryForm.createTime

      // for (let key in queryForm) {
      //   //有值的参数才传给后台
      //   if (queryForm[key] || queryForm[key] === 0) {
      //     rules.push({
      //       field: key,
      //       type: 'string',
      //       operator: 'contains',
      //       value: queryForm[key]
      //     })
      //   }
      // }
      const params = {
        condition: 'and',
        // rules,
        ...queryForm
      }
      // status quotaCode quotaName createTime 放置在外面
      if (this.queryForm.status || this.queryForm.status === 0) {
        params.status = this.queryForm.status
      }
      if (this.queryForm.quotaCode) {
        params.quotaCode = this.queryForm.quotaCode
      }
      if (this.queryForm.quotaName) {
        params.quotaName = this.queryForm.quotaName
      }
      if (this.queryForm.createTime) {
        //createTime拆成createTimeEnd和createTimeFrom  格式为2022-10-10T03:18:46.249Z
        params.createTimeEnd = `${formatTime(
          new Date(this.queryForm.createTime[1]),
          'YYYY-mm-dd'
        )} 23:59:59`
        params.createTimeFrom = `${formatTime(
          new Date(this.queryForm.createTime[0]),
          'YYYY-mm-dd'
        )} 00:00:00`
      }
      this.$API.quotaConfig.applyItemExport(params).then((res) => {
        this.$toast({
          type: 'success',
          content: this.$t('导出成功，请稍后！')
        })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.quato-customization {
  // margin-top: 24px;
  background: #fff;
  padding: 12px;
  border-radius: 8px;
  height: 100%;
  .button-group {
    display: flex;
    padding: 0 0 8px;
  }
  /deep/ .antd-form {
    .ant-input {
      border: 0;
      background: unset;
      border-bottom: 1px solid rgba(0, 0, 0, 0.42);
      border-radius: unset;
      &:focus {
        box-shadow: unset;
        border-bottom: 2px solid #000;
      }
    }
    .ant-select-selection {
      border: 0;
      background: unset;
      border-bottom: 1px solid rgba(0, 0, 0, 0.42);
      border-radius: unset;
      box-shadow: unset !important;
      &:focus {
        box-shadow: unset;
      }
    }
    .ant-select-open {
      .ant-select-selection {
        border-bottom: 2px solid #000;
      }
    }
  }
}
.components-table-demo-nested {
  // ::v-deep.ant-table-body {
  //   overflow-x: auto !important;
  // }
  ::v-deep.red-color {
    color: red;
  }
}
/deep/ .ant-select-selection-selected-value {
  width: 100% !important;
}
/deep/ .ant-select-selection--single {
  width: 100% !important;
}
</style>
