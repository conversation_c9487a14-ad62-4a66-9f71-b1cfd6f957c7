<template>
  <div class="full-height">
    <local-template-page
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>
<script>
import { toolbar, columnData } from './../config'
import { download, getHeadersFileName } from '@/utils/utils'
import localTemplatePage from '@/components/template-page'

export default {
  components: {
    localTemplatePage
  },
  data() {
    return {
      dataSource: [],
      pageConfig: [
        {
          toolbar,
          useToolTemplate: false,
          gridId: '1c1dea1e-2675-4fdd-83f3-731d6712d744',
          grid: {
            allowFiltering: true,
            columnData,
            asyncConfig: {
              url: '/price/tenant/quota/query/page',
              defaultRules: [
                {
                  label: this.$t('创建人'),
                  field: 'createUserName',
                  type: 'string',
                  operator: 'contains',
                  value: (JSON.parse(sessionStorage.getItem('userInfo')) || {})?.username
                }
              ]
            }
          }
        }
      ]
    }
  },
  methods: {
    //表格头部按钮-点击事件
    handleClickToolBar(e) {
      console.log(e)
      if (e.toolbar.id == 'Add') {
        this.handleAddQuota()
      } else if (e.toolbar.id == 'ExportFile') {
        this.ExportFile()
      } else if (e.toolbar.id == 'Delete') {
        const _selectRows = e.gridRef.getMtechGridRecords()
        if (_selectRows.length == 0) {
          this.$toast({
            content: this.$t('请选择一条数据'),
            type: 'warning'
          })
          return
        }
        //拟定和已驳回状态可以删除 status=0 or status=2 其余状态不可删除
        // let flag = false
        // _selectRows.forEach((item) => {
        //   if (item.status != 0 && item.status != 2) {
        //     flag = true
        //   }
        // })
        // if (flag) {
        //   this.$toast({
        //     content: this.$t('只有拟定和已驳回状态的数据可以删除'),
        //     type: 'warning'
        //   })
        //   return
        // }
        // let ids = _selectRows.map((item) => item.id)
        const ids = []
        for (let i = 0; i < _selectRows.length; i++) {
          const item = _selectRows[i]
          if (item.status != 0 && item.status != 2) {
            this.$toast({
              content: this.$t('只有草稿和已驳回状态的数据可以删除'),
              type: 'warning'
            })
            return
          }
          if (
            (JSON.parse(sessionStorage.getItem('userInfo')) || {})?.username !==
            item['createUserName']
          ) {
            this.$toast({
              content: this.$t('只可删除当前用户创建的数据'),
              type: 'warning'
            })
            return
          }
          ids.push(item.id)
        }
        this.handleDelete(ids)
      }
    },
    //导出
    ExportFile() {
      //获取组件的查询参数
      // 先要点击“查询”按钮才能取得queryBuilderRules
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules,
        // defaultRules: [].concat(queryBuilderRules.rules || [])
        defaultRules: [
          {
            label: this.$t('创建人'),
            field: 'createUserName',
            type: 'string',
            operator: 'contains',
            value: (JSON.parse(sessionStorage.getItem('userInfo')) || {})?.username
          }
        ]
      } // 筛选条件
      this.$API.quotaConfig.applyExport(params).then((res) => {
        // this.$toast({
        //   type: 'success',
        //   content: this.$t('导出成功，请稍后！')
        // })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      })
    },
    // 新增
    handleAddQuota() {
      this.$router.push({
        name: `quota-apply-detail`,
        query: {
          type: 'add',
          key: this.$utils.randomString(),
          timeStamp: new Date().getTime()
        }
      })
    },
    //单元格按钮
    handleClickCellTool(e) {
      console.log('单元格按钮')
      const { tool, data } = e
      if (tool.id === 'edit') {
        this.$router.push({
          name: `quota-apply-detail`,
          query: {
            type: 'edit',
            quotaCode: e.data.quotaCode,
            timeStamp: new Date().getTime()
          }
        })
      } else if (tool.id === 'delete') {
        this.handleDelete([data.id])
      }
    },
    //单元格标题
    handleClickCellTitle(e) {
      if (e.field == 'quotaCode') {
        this.$router.push({
          name: `quota-apply-detail`,
          query: {
            type:
              (e.data['status'] == '0' || e.data['status'] == '2') &&
              (JSON.parse(sessionStorage.getItem('userInfo')) || {})?.username ===
                e.data['createUserName']
                ? 'edit'
                : 'detail',
            quotaCode: e.data.quotaCode,
            timeStamp: new Date().getTime()
          }
        })
      }
    },
    //删除
    handleDelete(ids) {
      // 调对应接口后刷新列表
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.quotaConfig.delete(ids).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    }
  }
}
</script>
