import { i18n } from '@/main.js'
import utils from '@/utils/utils'
export const statusOptions = utils.getQuotaDict('QUOTA_ITEM_COMMIT_STATUS') || []

export const supplierOptions = utils.getQuotaDict('QUOTA_ITEM_SUBMIT_STATUS') || []
export const columns = [
  {
    type: 'checkbox',
    minWidth: 50
    // fixed: 'left'
  },
  {
    title: i18n.t('审批单号'),
    field: 'submitCode',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('工厂代码'),
    field: 'siteCode',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('工厂名称'),
    field: 'siteName',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('确认状态'),
    field: 'status',
    minWidth: 100,
    align: 'left',
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  },
  {
    title: i18n.t('月份'),
    field: 'month',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('采购组'),
    field: 'purGroupName',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('采购开发'),
    field: 'buyerUserName',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('品类'),
    field: 'categoryName',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('物料编码'),
    field: 'itemCode',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('物料名称'),
    field: 'itemName',
    minWidth: 150,
    align: 'left'
  },

  {
    title: i18n.t('供应商编码'),
    field: 'supplierCode',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('供应商名称'),
    field: 'supplierName',
    minWidth: 150,
    align: 'left'
  },
  {
    title: '协议配额（%）',
    field: 'agreementQuotaPercent',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('限制类型'),
    field: 'constraintType',
    minWidth: 150,
    align: 'left',
    formatter: ({ cellValue }) => {
      const options = utils.getQuotaDict('QUOTA_STRATEGY_XZPEYXJ_TYPE') || []
      let item = options.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  },
  {
    title: i18n.t('限制等级'),
    field: 'constraintLevel',
    minWidth: 150,
    align: 'left'
  },
  {
    title: '限制配额%',
    field: 'constraintQuotaPercent',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('价格'),
    field: 'price',
    minWidth: 150,
    align: 'left'
  },
  {
    title: '价差%',
    field: 'priceDifferencePercent',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('综合排名'),
    field: 'priceRanking',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('绩效排名'),
    field: 'analysisRanking',
    minWidth: 150,
    align: 'left'
  },
  {
    title: '初分配配额%',
    field: 'initQuotaPercent',
    minWidth: 150,
    align: 'left'
  },
  {
    title: '限制配额执行%',
    field: 'constraintQuotaExecutePercent',
    minWidth: 150,
    align: 'left'
  },
  {
    title: '建议配额%',
    field: 'systemQuotaPercent',
    minWidth: 100,
    align: 'left'
  },
  {
    title: '制定配额%',
    field: 'quotaPercent',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('最小起拆量'),
    field: 'recentQuanminOpenQuantitytity1',
    minWidth: 100,
    align: 'left'
  },
  {
    title: i18n.t('最小采购量'),
    field: 'minPurchaseQuantity',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('最小包装量'),
    field: 'minPackageQuantity',
    minWidth: 100,
    align: 'left'
  },
  {
    title: i18n.t('调整类型'),
    field: 'adjustType',
    minWidth: 150,
    align: 'left',
    formatter: ({ cellValue }) => {
      const options = utils.getQuotaDict('QUOTA_ITEM_ADJUST_TYPE') || []
      let item = options.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  },
  {
    title: i18n.t('调整原因'),
    field: 'adjustReason',
    minWidth: 100,
    align: 'left'
  },
  {
    title: i18n.t('是否是自制配额'),
    field: 'customizationFlag',
    minWidth: 150,
    align: 'left',
    formatter: ({ cellValue }) => {
      const options = utils.getQuotaDict('QUOTA_ITEM_CUSTOMIZATION_FLAG') || []
      let item = options.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  },
  {
    title: i18n.t('放行供应商类型'),
    field: 'excludeSupplierType',
    minWidth: 150,
    align: 'left',
    formatter: ({ cellValue }) => {
      const options = utils.getQuotaDict('QUOTA_STRATEGY_XZPEYXJ_TYPE') || []
      let item = options.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  }
]
