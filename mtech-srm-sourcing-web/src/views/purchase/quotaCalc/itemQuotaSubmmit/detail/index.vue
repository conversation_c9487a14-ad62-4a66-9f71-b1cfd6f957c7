<template>
  <div class="template-container">
    <!--  表单区域--->
    <template-form
      :form-config="formConfig"
      :page-type="pageType"
      @save="save"
      @submit="submit"
      @handleChange="handleChange"
      @clearTable="clearTable"
      @getItemList="getItemList"
    />
    <!-----   物料选择区域  ------->
    <template>
      <!--  表格区域--->
      <sc-table
        ref="xTable"
        grid-id="eaeb3e2a-c9a6-4546-a994-d8cedf45bc72"
        :loading="loading"
        :is-show-refresh-bth="true"
        :columns="columns"
        :table-data="dataSource"
        @refresh="getDetailData"
      >
      </sc-table>
      <!-- 分页 -->
      <mt-page
        ref="pageRef"
        class="flex-keep custom-page"
        :page-settings="pageSettings"
        :total-pages="pageSettings.totalPages"
        @currentChange="handleCurrentChange"
        @sizeChange="handleSizeChange"
      />
    </template>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { columns } from './config'
export default {
  components: {
    templateForm: require('./components/templateForm.vue').default,
    ScTable
  },
  data() {
    return {
      loading: false,
      columns,
      formConfig: {}, // 表单部分回显
      dataSource: [],
      pageSettings: {
        pageCount: 5,
        pageSize: 20, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [10, 20, 50, 100]
      },
      isShowTable: false,
      isEdit: false,
      categoryId: ''
    }
  },
  methods: {
    getItemList(code) {
      const params = {
        submitCode: code,
        page: {
          current: this.pageSettings.current,
          size: 10
        }
      }
      this.loading = true
      this.$API.itemQuotaSubmmit
        .getQueryBuild(params)
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            const total = res?.data?.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)
            this.dataSource = data.records
            this.isEdit = false
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageSettings.current = currentPage
      this.getDetailData()
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.getDetailData()
    },
    getDetailData() {
      let id = this.detailId
      // if (detailId) {
      //   id = detailId
      // }
      if (this.$route.query.type == 'detail') {
        const code = this.$route.query.submitCode
        this.$API.itemQuotaSubmmit.getTitleQueryBuild(code).then((res) => {
          const { code, data } = res
          if (code === 200) {
            // 表单数据
            this.formConfig = JSON.parse(JSON.stringify(data))
            this.getItemList(data.code)
            if (data.sourcingFileSaveRequest && data.sourcingFileSaveRequest.length) {
              let sourcingFileSaveRequest = data.sourcingFileSaveRequest
              sourcingFileSaveRequest.forEach((i) => {
                i.originId = i.id
                i.id = i.fileId
              })
              this.formConfig.sourcingFileSaveRequest = sourcingFileSaveRequest
            }
            // 表格数据
            // this.dataSource = data.detailSaveRequests || []
          }
        })
      } else {
        if (id instanceof Array) {
          this.$API.itemQuotaSubmmit.getDetailList(id).then((res) => {
            const { code, data } = res
            if (code === 200) {
              // 表单数据
              this.formConfig = JSON.parse(JSON.stringify(data))
              this.getItemList(data.code)
              if (data.sourcingFileSaveRequest && data.sourcingFileSaveRequest.length) {
                let quotaStrategyFileSaveRequests = data.sourcingFileSaveRequest
                quotaStrategyFileSaveRequests.forEach((i) => {
                  i.originId = i.id
                  i.id = i.fileId
                })
                this.formConfig.sourcingFileSaveRequest = quotaStrategyFileSaveRequests
              }
              // 表格数据
              // this.dataSource = data.detailSaveRequests || []
            }
          })
        } else {
          this.$API.itemQuotaSubmmit.getDetailList([id]).then((res) => {
            const { code, data } = res
            if (code === 200) {
              // 表单数据
              this.formConfig = JSON.parse(JSON.stringify(data))
              this.getItemList(data.code)
              if (data.sourcingFileSaveRequest && data.sourcingFileSaveRequest.length) {
                let quotaStrategyFileSaveRequests = data.sourcingFileSaveRequest
                quotaStrategyFileSaveRequests.forEach((i) => {
                  i.originId = i.id
                  i.id = i.fileId
                })
                this.formConfig.sourcingFileSaveRequest = quotaStrategyFileSaveRequests
              }
              // 表格数据
              // this.dataSource = data.detailSaveRequests || []
            }
          })
        }
      }
    },
    handleChange(e) {
      const { flag, categoryId } = e
      this.isShowTable = flag
      this.categoryId = categoryId
    },
    clearTable() {
      this.dataSource = []
    },

    getParams(addForm) {
      console.log('addform', addForm, this.dataSource)
      // 接口要求部分放行时物料为必填
      const params = {
        ...addForm
      }
      if (params.quotaStrategyFileSaveRequests && params.quotaStrategyFileSaveRequests.length) {
        params.quotaStrategyFileSaveRequests = params.quotaStrategyFileSaveRequests.map((i) => {
          let obj = {
            ...i,
            fileId: i.id
          }
          delete obj.id
          if (obj.originId) {
            obj.id = obj.originId
          }
          if (params.id) {
            obj.strategyId = params.id
          }
          if (params.code) {
            obj.strategyCode = params.code
          }
          return obj
        })
      }
      return params
    },
    save(addForm) {
      let params = {}
      if (this.getParams(addForm)) {
        params = this.getParams(addForm)
      } else {
        return false
      }
      this.$API.itemQuotaSubmmit.saveList(params).then((res) => {
        const { code, data } = res
        if (code === 200) {
          // 保存后根据回调的id请求详情

          this.$router.push({
            path: '/sourcing/quota-calc/item-quota-submmit',
            query: {
              id: data,
              type: 'edit',
              refreshKey: new Date().getTime()
            }
          })

          // this.getDetailData(data.id)
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
          this.isEdit = false
        }
      })
    },
    submit(addForm) {
      let params = {}
      if (this.getParams(addForm)) {
        params = this.getParams(addForm)
      } else {
        return false
      }
      this.$API.itemQuotaSubmmit.submitList(params).then((res) => {
        const { code } = res
        if (code === 200) {
          // // 保存后根据回调的id请求详情
          // this.getDetailData(data.id)
          this.$toast({
            content: this.$t('提交成功'),
            type: 'success'
          })
          this.isEdit = false
          this.$bus.$emit('refreshQuotaConstraintExcludeList')
          this.$router.push({
            path: '/sourcing/quota-calc/item-quota-submmit',
            query: {
              refreshKey: new Date().getTime()
            }
          })
        }
      })
    }
  },
  computed: {
    pageType() {
      // 判断当前页面的类型，add;edit;detail
      return this.$route.query.type || ''
    },
    detailId() {
      // 非新增页面获取详情id
      return this.$route.query.id || ''
    }
  },
  activated() {
    // 非新增页面获取详情
    // this.getItemList()
    this.getDetailData()
  }
}
</script>

<style lang="scss" scoped>
.template-container {
  background: #fff;
  height: 100%;
  overflow: auto;
  // margin-top: 20px;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;
  ::v-deep.e-btn.e-flat.e-primary {
    color: #6386ce !important;
    font: inherit !important;
  }
}
.button-group {
  display: flex;
  padding: 0 0 16px;
}
</style>
