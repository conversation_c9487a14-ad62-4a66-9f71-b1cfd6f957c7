<template>
  <div>
    <div class="header-box">
      <!-- 右侧各种操作按钮 -->
      <span class="header-title">{{ getTitle() }}</span>
      <div>
        <mt-button
          v-if="
            this.$route.query.submitStatus == 0 ||
            this.$route.query.submitStatus == 2 ||
            this.$route.query.type == 'submit'
          "
          css-class="e-flat"
          :is-primary="true"
          @click="save()"
          >{{ $t('保存') }}</mt-button
        >
        <mt-button
          v-if="
            this.$route.query.submitStatus == 0 ||
            this.$route.query.submitStatus == 2 ||
            this.$route.query.type == 'submit'
          "
          css-class="e-flat"
          :is-primary="true"
          @click="submit()"
          >{{ $t('提交审批') }}</mt-button
        >
        <mt-button css-class="e-flat" :is-primary="true" @click="backTo">{{
          $t('返回')
        }}</mt-button>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-form">
      <mt-form ref="ruleForm" :model="addForm" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="code" :label="$t('审批单号')">
          <mt-input v-model="addForm.code" disabled :placeholder="$t('审批单号')" />
        </mt-form-item>
        <mt-form-item prop="title" :label="$t('配额制定单据标题')">
          <mt-input
            v-model="addForm.title"
            :disabled="
              (this.$route.query.submitStatus == 1 || this.$route.query.submitStatus == 3) &&
              this.$route.query.type !== 'submit'
            "
            type="text"
            :placeholder="$t('请输入配额制定单据标题')"
          />
        </mt-form-item>
        <mt-form-item prop="submitStatus" :label="$t('审批单状态')">
          <mt-select
            v-model="addForm.submitStatus"
            float-label-type="Never"
            disabled
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="supplierOptions"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('审批单状态')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="createUserName" :label="$t('创建人')">
          <mt-input v-model="addForm.createUserName" disabled :placeholder="$t('创建人')" />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')">
          <mt-input
            v-model="addForm.createTime"
            disabled
            :open-on-focus="true"
            float-label-type="Never"
            width="100%"
            :placeholder="$t('创建时间')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')">
          <mt-input
            v-model="addForm.remark"
            :disabled="
              (this.$route.query.submitStatus == 1 || this.$route.query.submitStatus == 3) &&
              this.$route.query.type !== 'submit'
            "
            type="text"
            :placeholder="$t('请输入备注')"
          />
        </mt-form-item>
        <mt-form-item
          v-if="!(addForm.sourcingFileSaveRequest && !addForm.sourcingFileSaveRequest.length)"
          :label="$t('附件')"
        >
          <div class="div-auth" @click="handleUploadDialog">
            {{ getFormFileText }}
          </div>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { pageConfig, supplierOptions, statusOptions } from './../../config'
import utils from '@/utils/utils.js'
import { throttle } from 'lodash'
import * as util from '@mtech-common/utils'
export default {
  components: {},
  props: {
    pageType: {
      // 判断当前页面的类型，add;edit;detail
      type: String,
      require: true,
      default: ''
    },
    formConfig: {
      type: Object,
      require: true,
      default: () => {
        return {
          code: '', // 审批单号
          title: '', // 配额审批单据标题
          submitStatus: '', // 审批单状态
          createUserName: '', // 创建人
          createTime: '', // 创建时间
          remark: '', // 备注
          file: '' // 文件
        }
      }
    }
  },
  data() {
    return {
      companyList: [], // 组织下拉列表的配置信息
      statusOptions,
      supplierOptions,
      addForm: {
        code: '', // 审批单号
        title: '', // 配额审批单据标题
        submitStatus: '', // 审批单状态
        createUserName: '', // 创建人
        createTime: '', // 创建时间
        remark: '', // 备注
        sourcingFileSaveRequest: [] // 上传附件内容
      },
      rules: {},
      isAllowChang: false, // 解决页面初始化时触犯的change事件
      quotaStrategyFileSaveRequests: null
    }
  },

  methods: {
    //上传附件弹框
    handleUploadDialog() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "components/Upload/uploaderDialog" */ 'COMPONENTS/Upload/uploaderDialog.vue'
          ),
        data: {
          fileData: util.utils.cloneDeep(this.addForm.sourcingFileSaveRequest),
          isView: false, // 是否为预览
          required: false, // 是否必须
          title: this.$t('附件')
          // isSingleFile: true
        },
        success: (res) => {
          let fileList = JSON.parse(JSON.stringify(res))
          fileList.forEach((i) => {
            i.fileId = i.id
            // delete i.id
            // if (this.addForm.id) {
            //   i.strategyId = this.addForm.id
            // }
            // if (this.addForm.code) {
            //   i.strategyCode = this.addForm.code
            // }
          })
          this.addForm.sourcingFileSaveRequest = fileList
        }
      })
    },
    setFormEmpty(fieldsarr) {
      for (let i = 0; i < fieldsarr.length; i++) {
        this.$set(this.addForm, fieldsarr[i], '')
      }
      this.$emit('clearTable')
    },
    startDateChange() {
      if (this.isAllowChang) {
        this.addForm.endDate = ''
      }
    },
    save: throttle(function () {
      if (!this.addForm.title) {
        this.$toast({
          content: this.$t('请输入配额审批单据标题'),
          type: 'warning'
        })
        return false
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const params = {
            ...this.addForm
          }
          this.$emit('save', params)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }, 1000),
    submit: throttle(function () {
      if (!this.addForm.title) {
        this.$toast({
          content: this.$t('请输入配额审批单据标题'),
          type: 'warning'
        })
        return false
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const params = {
            ...this.addForm
          }
          this.$emit('submit', params)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }, 1000),
    getTitle() {
      let title = this.$t('配额制定明细')

      return title
    },
    backTo() {
      this.$bus.$emit('refreshQuotaConstraintExcludeList')
      this.$router.push({
        path: '/sourcing/quota-calc/item-quota-submmit',
        query: {
          refreshKey: new Date().getTime()
        }
      })
    }
  },
  computed: {
    getFormFileText() {
      let _list = this?.addForm?.sourcingFileSaveRequest ?? []
      if (_list.length) {
        let _name = []
        _list.forEach((e) => {
          _name.push(e.fileName)
        })
        return _name.join(',')
      } else {
        return this.$t('上传附件')
      }
    },
    categoryDialogCofig() {
      const params = {}
      if (this.addForm.orgId) {
        params.organizationId = this.addForm.orgId
      }
      return {
        pageConfig: pageConfig(
          '/masterDataManagement/tenant/permission/queryCategories',
          'category',
          params
        ),
        text: 'categoryName',
        value: 'categoryCode'
      }
    }
  },
  watch: {
    /* 监听传进来得内容 */
    formConfig: {
      handler(newVal) {
        this.isAllowChang = false
        this.addForm = {
          ...this.addForm,
          ...newVal
        }
        if (this.addForm.startDate && this.addForm.endDate) {
          this.addForm.startDate = utils.formatTime(new Date(this.addForm.startDate), 'YYYY-mm')
          this.addForm.endDate = utils.formatTime(new Date(this.addForm.endDate), 'YYYY-mm')
        }
        // 加定时器是处理 初始赋值时会触发mt表单组件的change事件导致表单置空逻辑触发回显异常
        setTimeout(() => {
          this.isAllowChang = true
        }, 2000)
      },
      immediate: true,
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
.header-box {
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 20px;
  .header-title {
    color: #292929;
    font-family: PingFangSC;
    font-size: 20px;
    font-weight: 600;
  }
}
.main-form {
  /deep/ .mt-form-item {
    width: calc(25% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-top: 5px;
    margin-right: 20px;

    .full-width {
      width: calc(100% - 20px) !important;
    }
    .e-ddt .e-ddt-icon::before {
      content: '\e36a';
      font-size: 16px;
    }
    .mt-form-item-topLabel {
      .div-auth {
        background: #e3e1e1;
        height: 35px;
        display: flex;
        align-items: center;
        padding: 0 10px;
        color: #0f0f0f;
        font-size: 12px;
        cursor: pointer;
        border-radius: 5px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 200px;
        display: inline-block;
        line-height: 35px;
      }
    }
  }
  .check-area {
    transform: translateY(10px);
  }
}
</style>
