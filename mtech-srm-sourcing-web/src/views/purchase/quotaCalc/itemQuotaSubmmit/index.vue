<template>
  <div class="quato-customization">
    <collapse-search :is-grid-display="true" @reset="reset" @search="search">
      <mt-form ref="ruleForm" :model="queryForm">
        <mt-form-item prop="month" :label="$t('月份')" label-style="top">
          <mt-date-picker
            v-model="queryForm.month"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :placeholder="$t('选择月份')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="submitCode" :label="$t('审批单号')" label-style="top">
          <mt-input v-model="queryForm.submitCode" :placeholder="$t('请输入审批单号')" />
        </mt-form-item>
        <mt-form-item prop="buyerUserName" :label="$t('采购开发')" label-style="top">
          <mt-input v-model="queryForm.buyerUserName" :placeholder="$t('请输入采购开发')" />
        </mt-form-item>
        <mt-form-item prop="commitStatus" :label="$t('确认提交状态')" label-style="top">
          <mt-select
            v-model="queryForm.commitStatus"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="statusOptions"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('请选择确认提交状态')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="submitStatus" :label="$t('审批状态')" label-style="top">
          <mt-select
            v-model="queryForm.submitStatus"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="supplierOptions"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('请选择审批状态')"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!----  表格  --------->

    <sc-table
      ref="xTable"
      grid-id="fa7802ae-12bb-48bf-9cea-679ef85db300"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="vxeColumns"
      :table-data="dataSource"
      @refresh="search"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
          >{{ item.name }}</vxe-button
        >
      </template>
      <template #submitCode="{ row }">
        <div class="editable-row-operations">
          <span>
            <a @click="() => handleDetail(row)">{{ row.submitCode }}</a>
          </span>
        </div></template
      >
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import collapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { vxeColumns, statusOptions, supplierOptions } from './config'
import utils from '@/utils/utils'

export default {
  components: {
    collapseSearch,
    ScTable
  },
  data() {
    return {
      loading: false,
      toolbar: [
        {
          code: 'Submit',
          name: this.$t('提交'),
          icon: 'el-icon-delete',
          status: 'info'
        }
      ],
      queryForm: {
        month: this.getPrevMonth(), // 月份
        submitCode: '', // 审批单号
        commitStatus: '', //确认提交状态
        submitStatus: '', // 审批状态
        buyerUserName: '', // 采购开发
        currentPage: 1,
        pageSize: 10
      },
      pageSettings: {
        pageCount: 5,
        pageSize: 20, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [10, 20, 50, 100]
      },
      selectedRows: [], // 表格勾选行的内容
      statusOptions,
      supplierOptions, // 供应商放行类型
      vxeColumns,
      dataSource: [],
      // fieldsarr: {
      //   dataSource: [], // 组织树下拉数组
      //   value: 'id',
      //   text: 'companyId',
      //   child: 'childrenList'
      // },
      companyList: []
    }
  },
  created() {
    this.$bus.$on('refreshQuotaConstraintExcludeList', () => {
      this.search()
    })
    this.search()
  },
  methods: {
    getPrevMonth() {
      let nowMonthDate = new Date()
      let getMonth = nowMonthDate.getMonth() + 1
      nowMonthDate.setMonth(getMonth, 1)

      return nowMonthDate
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageSettings.current = currentPage
      this.search('pageSettings')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.search()
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      if (code === 'Submit') {
        this.selectedRows = $grid.getCheckboxRecords() // 表格勾选行的内容
        this.handleDetail()
      }
      this.selectedRows = []
    },
    handleDetail(row) {
      const query = { refreshKey: new Date().getTime() }
      if (row) {
        query.id = row.id
        query.type = 'detail'
        query.submitCode = row.submitCode
        query.submitStatus = row.submitStatus
        this.$router.push({
          path: `/sourcing/quota-calc/item-quota-submmit-detail`,
          query
        })

        return
      } else if (this.selectedRows.length <= 0) {
        this.$toast({
          content: this.$t('请勾选需要操作的数据'),
          type: 'warning'
        })
        return
      } else {
        const arr = []
        for (let td of this.selectedRows) {
          if (td.submitStatus == '3' || td.submitStatus == '1') {
            this.$toast({
              content: this.$t('审批状态不为拟定、已驳回'),
              type: 'warning'
            })
            return
          } else if (td.commitStatus == '0' || td.commitStatus == '1') {
            this.$toast({
              content: this.$t('确认状态不为已提交'),
              type: 'warning'
            })
            return
          }
          arr.push(td.id)
        }
        query.id = arr
      }
      for (let i in this.selectedRows) {
        if (this.selectedRows[0].buId != this.selectedRows[i].buId) {
          this.$toast({
            content: this.$t('仅同事业部的配额可批量提交审批'),
            type: 'warning'
          })
          return
        }
      }
      query.type = 'submit'
      this.$API.itemQuotaSubmmit.getDetailList(query.id).then((res) => {
        const { code } = res
        if (code === 200) {
          // 表单数据
          this.$router.push({
            path: `/sourcing/quota-calc/item-quota-submmit-detail`,
            query
          })
        }
      })
    },
    reset() {
      console.log('重置')
      this.queryForm = {
        month: this.getPrevMonth(), // 月份
        submitCode: '', // 审批单号
        commitStatus: '', //确认提交状态
        submitStatus: '', // 审批状态
        buyerUserName: '', // 采购开发
        currentPage: 1,
        pageSize: 10
      }
      this.search()
    },
    search(pageSettings) {
      console.log('search', this.queryForm)
      this.queryForm.month = utils.formatTime(new Date(this.queryForm.month), 'YYYY-mm')
      const params = {
        ...this.queryForm,
        page: {
          current: pageSettings ? this.pageSettings.current : 1,
          size: this.pageSettings.pageSize
        }
      }
      this.loading = true
      this.$API.itemQuotaSubmmit
        .getItemList(params)
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            const total = res?.data?.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)
            this.dataSource = data.records
          }
        })
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.quato-customization {
  // margin-top: 24px;
  background: #fff;
  padding: 12px;
  border-radius: 8px;
  height: 100%;
  .button-group {
    display: flex;
    padding: 0 0 8px;
  }
}
.components-table-demo-nested {
  // ::v-deep.ant-table-body {
  //   overflow-x: auto !important;
  // }
  ::v-deep.red-color {
    color: red;
  }
}
</style>
