import { i18n } from '@/main.js'
import utils from '@/utils/utils'
const toolbar = [
  // { id: "Add", icon: "icon_solid_Createorder", title: i18n.t("新增") },
  // { id: "Stop", icon: "icon_solid_Cancel", title: i18n.t("取消") },
  // { id: "Submit", icon: "icon_solid_Submit", title: i18n.t("提交") },
  // { id: "Import", icon: "icon_solid_Createorder", title: i18n.t("导入") },
]
const materialColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    cssClass: 'field-content',
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  }
]
const categoryColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    cssClass: 'field-content'
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]
const supplierColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    cssClass: 'field-content'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  }
]
export const pageConfig = (url, columnType, params) => {
  let columnData = []
  let gridId = {
    material: '3186a8e7-8951-4c94-93ee-3e1bdf968980',
    category: '19121446-8ee7-4e2b-9462-f8e3eb73a19c',
    supplier: '433d79d6-6957-4345-aa71-7978f36de523'
  }
  if (columnType === 'material') {
    columnData = materialColumn
  } else if (columnType === 'category') {
    columnData = categoryColumn
  } else {
    columnData = supplierColumn
  }
  return [
    {
      toolbar,
      useToolTemplate: false,
      gridId: gridId[columnType],
      grid: {
        allowFiltering: true,
        columnData,
        allowSorting: false,
        allowSelection: true,
        selectionSettings: {
          type: 'Multiple',
          mode: 'Row'
        },
        asyncConfig: { url, params }
      }
    }
  ]
}

export const materialColumns = [
  {
    title: i18n.t('物料编码'),
    dataIndex: 'itemCode',
    field: 'itemCode',
    width: 200,
    align: 'left'
  },
  {
    title: i18n.t('物料名称'),
    dataIndex: 'itemName',
    field: 'itemName',
    width: 150,
    align: 'left'
  }
]

export const statusOptions = utils.getQuotaDict('QUOTA_ITEM_COMMIT_STATUS') || []

export const supplierOptions = utils.getQuotaDict('QUOTA_ITEM_SUBMIT_STATUS') || []

export const vxeColumns = [
  {
    type: 'checkbox',
    minWidth: 50
    // fixed: 'left'
  },
  {
    title: i18n.t('采购开发'),
    field: 'buyerUserName',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('事业部'),
    field: 'buName',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('月份'),
    field: 'month',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('确认状态'),
    field: 'commitStatus',
    minWidth: 150,
    align: 'left',
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  },

  {
    title: i18n.t('确认提交时间'),
    field: 'commitTime',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('审批状态'),
    field: 'submitStatus',
    minWidth: 150,
    align: 'left',
    formatter: ({ cellValue }) => {
      let item = supplierOptions.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  },
  {
    title: i18n.t('审批单号'),
    minWidth: 180,
    field: 'submitCode',
    align: 'left',
    slots: { default: 'submitCode' }
  },

  { title: i18n.t('提交人'), minWidth: 150, field: 'submitUserName', align: 'left' }
]
