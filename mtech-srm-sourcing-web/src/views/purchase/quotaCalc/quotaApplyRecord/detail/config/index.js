import { i18n } from '@/main.js'
const toolbar = [
  // { id: "Add", icon: "icon_solid_Createorder", title: i18n.t("新增") },
  // { id: "Stop", icon: "icon_solid_Cancel", title: i18n.t("取消") },
  // { id: "Submit", icon: "icon_solid_Submit", title: i18n.t("提交") },
  // { id: "Import", icon: "icon_solid_Createorder", title: i18n.t("导入") },
]
const materialColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    cssClass: 'field-content',
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  }
]
const categoryColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    cssClass: 'field-content'
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]
const supplierColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    cssClass: 'field-content'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  }
]
export const pageConfig = (url, columnType, params) => {
  let columnData = []
  let gridId = {
    material: '6a328c00-ab6e-4d3f-a1f3-a67a3b7446f1',
    category: '9df7a3d9-1a37-4552-88aa-b229b1e41625',
    supplier: 'fc3ee580-1981-4eda-bdb7-a6a4777264cf'
  }
  if (columnType === 'material') {
    columnData = materialColumn
  } else if (columnType === 'category') {
    columnData = categoryColumn
  } else {
    columnData = supplierColumn
  }
  let asyncConfig = {
    url
  }
  if (params) {
    asyncConfig.params = params
  }
  return [
    {
      toolbar,
      useToolTemplate: false,
      gridId: gridId[columnType],
      grid: {
        allowFiltering: true,
        columnData,
        allowSorting: false,
        allowSelection: true,
        selectionSettings: {
          type: 'Multiple',
          mode: 'Row'
        },
        asyncConfig
      }
    }
  ]
}

export const vxeColumns = [
  {
    type: 'checkbox',
    minWidth: 50
  },
  {
    title: i18n.t('工厂'),
    field: 'siteCode',
    minWidth: 250,
    align: 'left',
    editRender: {},
    slots: { header: 'factoryHead', edit: 'factoryEdit' }
  },
  {
    title: i18n.t('工厂名称'),
    field: 'siteName',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('供应商编码'),
    field: 'supplierCode',
    minWidth: 200,
    align: 'left',
    editRender: {},
    slots: { header: 'supplierCodeHead', edit: 'supplierCodeEdit' }
  },
  {
    title: i18n.t('供应商名称'),
    field: 'supplierName',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('物料编码'),
    field: 'itemCode',
    minWidth: 200,
    align: 'left',
    editRender: {},
    slots: { header: 'materialCodeHead', edit: 'itemCodeEdit' }
  },
  {
    title: i18n.t('物料名称'),
    field: 'itemName',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('调整数量'),
    field: 'quantity',
    minWidth: 150,
    align: 'left',
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入调整数量') } }
  },
  {
    title: i18n.t('品类'),
    field: 'categoryName',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('调整原因'),
    field: 'reason',
    minWidth: 150,
    align: 'left',
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入调整数量') } },
    slots: { header: 'reasonHead' }
  },
  {
    title: i18n.t('建议对策'),
    field: 'measure',
    minWidth: 150,
    align: 'left',
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入建议对策') } }
  },
  {
    title: i18n.t('调整配额'),
    field: 'adjust',
    minWidth: 150,
    align: 'left',
    editRender: {},
    slots: { header: 'adjustHead', edit: 'adjustEdit' },
    formatter: ({ cellValue }) => {
      const options = [
        { text: i18n.t('否'), code: 1 },
        { text: i18n.t('是'), code: 2 }
      ]
      let item = options.find((item) => item.code === cellValue)
      return item ? item.text : ''
    }
  },
  {
    title: i18n.t('开始月份'),
    field: 'startMonthTime',
    minWidth: 150,
    align: 'left',
    editRender: {},
    slots: { edit: 'startMonthTimeEdit' }
  },
  {
    title: i18n.t('结束月份'),
    field: 'endMonthTime',
    minWidth: 150,
    align: 'left',
    editRender: {},
    slots: { edit: 'endMonthTimeEdit' }
  }
  // { title: i18n.t('操作'), field: 'operation', fixed: 'right', scopedSlots: { customRender: 'operation' } }
]
