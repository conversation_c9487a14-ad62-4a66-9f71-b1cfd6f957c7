<template>
  <div class="supplier-box mt-flex">
    <div class="miam-container">
      <div class="page-header">
        <p class="page-title">{{ $t('配额调整申请维护') }}</p>
        <div class="operate-bar">
          <div v-if="pageType !== 'detail'" class="op-item mt-flex" @click="saveDetail">
            {{ $t('保存') }}
          </div>
          <div v-if="pageType !== 'detail'" class="op-item mt-flex" @click="submit">
            {{ $t('提交') }}
          </div>
          <div class="op-item mt-flex" @click="backToBusinessConfig">
            {{ $t('返回') }}
          </div>
        </div>
      </div>
      <!-- <input type="file" accept=".xlsx" hidden ref="excel-uploader" @change="fileChange"/> -->
      <!-- 顶部主要信息 -->
      <div class="mian-info">
        <mt-form ref="ruleForm" :model="queryForm" :rules="formRules">
          <mt-form-item :label="$t('配额调整申请标题')" prop="title">
            <mt-input
              width="100%"
              type="text"
              :disabled="pageType === 'detail'"
              v-model="queryForm.title"
              :placeholder="$t('请输入配额调整申请标题')"
              :max-length="110"
            ></mt-input>
          </mt-form-item>
          <mt-form-item :label="$t('配额调整申请单号')">
            <mt-input
              width="100%"
              disabled
              type="text"
              v-model="queryForm.requestNumber"
              :placeholder="$t('请输入配额调整申请单号')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="status" :label="$t('单据状态')">
            <mt-select
              disabled
              v-model="queryForm.status"
              float-label-type="Never"
              :allow-filtering="true"
              filter-type="Contains"
              :show-clear-button="true"
              :data-source="statusOptions"
              :fields="{ text: 'dictName', value: 'dictCode' }"
              :placeholder="$t('状态')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item :label="$t('创建人')" prop="createUserName">
            <mt-input
              width="100%"
              type="text"
              disabled
              v-model="queryForm.createUserName"
              :placeholder="$t('请输入创建人')"
              :max-length="110"
            ></mt-input>
          </mt-form-item>
          <mt-form-item :label="$t('创建时间')" prop="createDate">
            <mt-input
              width="100%"
              type="text"
              disabled
              v-model="queryForm.createDate"
              :placeholder="$t('请输入创建时间')"
              :max-length="110"
            ></mt-input>
          </mt-form-item>
          <mt-form-item :label="$t('备注')" style="width: calc(40% - 20px)">
            <mt-input
              width="100%"
              type="text"
              :disabled="pageType === 'detail'"
              v-model="queryForm.remark"
              :placeholder="$t('字数不超过200字')"
              :max-length="200"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="userList" :label="$t('采购开发')">
            <!-- <mt-multi-select
              :allow-filtering="true"
              :disabled="pageType === 'detail'"
              v-model="queryForm.userList"
              :data-source="userArrList"
              filter-type="Contains"
              :filtering="getUserList"
              :fields="{
                text: 'employeeName',
                value: 'employeeName'
              }"
              :show-clear-button="true"
              :placeholder="$t('指标名称')"
            ></mt-multi-select> -->
            <mt-multi-select
              v-model="queryForm.userList"
              id="dropDownTreeCom"
              style="width: 100%"
              :fields="{
                text: 'employeeName',
                value: 'employeeName'
              }"
              :data-source="userArrList"
              filter-bar-:placeholder="$t('Search')"
              :allow-filtering="true"
              :filtering="getUserList"
              :placeholder="$t('采购开发')"
              filter-bar-placeholder="$t('请输入用户名称进行搜索')"
              :no-records-template="noRecordsTemplate"
            ></mt-multi-select>
          </mt-form-item>
          <mt-form-item
            v-if="
              !(
                pageType === 'detail' &&
                queryForm.quotaStrategyFileSaveRequests &&
                !queryForm.quotaStrategyFileSaveRequests.length
              )
            "
            :label="$t('上传附件')"
            prop="quotaStrategyFileSaveRequests"
          >
            <div class="div-auth" @click="handleUploadDialog">
              {{ formFileText }}
            </div>
          </mt-form-item>
        </mt-form>
      </div>
      <div class="relation-ships">
        <!----  表格  --------->
        <sc-table
          ref="xTable"
          :loading="loading"
          :columns="vxeColumns"
          :table-data="dataSource"
          :edit-config="{
            trigger: pageType !== 'detail' ? 'click' : '',
            mode: 'row',
            showStatus: true,
            autoClear: false
          }"
        >
          <template v-if="pageType !== 'detail'" slot="custom-tools">
            <vxe-button
              v-for="item in toolbar"
              :key="item.code"
              :status="item.status"
              :icon="item.icon"
              size="small"
              @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
              >{{ item.name }}</vxe-button
            >
          </template>
          <template #factoryHead="{}">
            <span class="red-color">*</span>{{ $t('工厂代码') }}</template
          >
          <template #factoryEdit="{ row }">
            <vxe-select
              v-model="row.siteCode"
              :options="factoryList"
              :option-props="{
                label: 'codeAndName',
                value: 'dimensionCodeValue'
              }"
              @change="handleFactoryChange($event, row)"
              transfer
              filterable
            ></vxe-select>
          </template>
          <template #materialCodeHead="{}">
            <span class="red-color">*</span>{{ $t('物料编码') }}</template
          >
          <template #itemCodeEdit="{ row }">
            <magnifier-input
              :disabled="!row.siteCode"
              :default-value="row.itemCode"
              :config="materialTableDialogCofig(row)"
              @change="materialCodeChange($event, row)"
            ></magnifier-input>
          </template>
          <template #supplierCodeHead="{}">
            <span class="red-color">*</span>{{ $t('供应商编码') }}</template
          >
          <template #supplierCodeEdit="{ row }">
            <magnifier-input
              :default-value="row.supplierCode"
              :disabled="!row.siteCode"
              :config="supplierDialogCofig(row.siteCode)"
              @change="supplierCodeChange($event, row)"
            ></magnifier-input>
          </template>
          <template #reasonHead="{}">
            <span class="red-color">*</span>{{ $t('调整原因') }}</template
          >
          <template #adjustHead="{}">
            <span class="red-color">*</span>{{ $t('调整配额') }}</template
          >
          <template #adjustEdit="{ row }">
            <vxe-select
              v-model="row.adjust"
              :options="isAdjustList"
              :option-props="{
                label: 'text',
                value: 'code'
              }"
              @change="handleAdjustChange($event, row)"
              transfer
              filterable
            ></vxe-select>
          </template>
          <template #startMonthTimeEdit="{ row }">
            <vxe-input
              v-model="row.startMonthTime"
              :placeholder="$t('请选择开始日期')"
              type="date"
              transfer
            ></vxe-input
          ></template>
          <template #endMonthTimeEdit="{ row }">
            <vxe-input
              v-model="row.endMonthTime"
              :placeholder="$t('请选择结束日期')"
              type="date"
              transfer
            ></vxe-input
          ></template>
        </sc-table>
        <!-- 分页 -->
        <mt-page
          ref="pageRef"
          class="flex-keep custom-page"
          :page-settings="pageSettings"
          :total-pages="pageSettings.totalPages"
          @currentChange="handleCurrentChange"
          @sizeChange="handleSizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import utils from '@/utils/utils'
import debounce from 'lodash.debounce'
import { throttle } from 'lodash'
import magnifierInput from '@/components/magnifierInput'
import { pageConfig, vxeColumns } from './config'
import ScTable from '@/components/ScTable/src/index'
import * as util from '@mtech-common/utils'
export default {
  components: {
    magnifierInput,
    ScTable
  },
  data() {
    return {
      toolbar: [
        {
          code: 'Add',
          name: this.$t('新增'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Import',
          name: this.$t('导入'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Delete',
          name: this.$t('删除'),
          icon: 'el-icon-delete',
          status: 'info'
        }
      ],
      loading: false,
      isSaving: false,
      queryForm: {
        title: '', // 配额调整申请标题
        requestNumber: '', // 配额调整申请单号
        status: '', // 单据状态
        createUserName: '', // 创建人
        createDate: '', // 创建时间
        remark: '', // 备注
        userList: [], // 采购开发已选列表
        quotaStrategyFileSaveRequests: [] // 上传附件内容
      },
      quotaStrategyFileSaveRequests: [],
      pageSettings: {
        pageCount: 5,
        pageSize: 20, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [10, 20, 50, 100]
      },
      factoryList: [], // 工厂列表
      isAdjustList: [
        { text: this.$t('否'), code: 1 },
        { text: this.$t('是'), code: 2 }
      ],
      statusOptions: utils.getQuotaDict('QUOTA_STRATEGY_STATUS') || [],
      vxeColumns,
      dataSource: [],
      userArrList: [], // 采购开发可选列表
      // 物料放大镜弹窗配置
      materialDialogCofig: {
        pageConfig: pageConfig(
          `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
          'material'
        ),
        text: 'itemCode',
        value: 'itemCode'
      },
      // 品类放大镜弹窗配置
      categoryDialogCofig: {
        pageConfig: pageConfig(
          '/masterDataManagement/tenant/permission/queryCategories',
          'category'
        ),
        text: 'categoryName',
        value: 'categoryCode'
      },
      isEdit: false,
      cacheRow: {},
      formRules: {
        title: [
          {
            required: true,
            message: this.$t('请输入配额调整申请标题'),
            trigger: 'blur'
          }
        ],
        userList: [
          {
            required: true,
            message: this.$t('请至少选择一名采购开发'),
            trigger: 'blur'
          }
        ]
      },
      isReloadPicker: true,
      addDetailId: '',
      formFileText: this.$t('上传附件'),
      noRecordsTemplate: this.$t('请输入用户名称进行搜索')
    }
  },
  created() {
    this.getFactoryList()
    // this.getUserList()
    if (this.pageType !== 'add') {
      this.getDetail()
    }
    this.getUserList = debounce(this.getUserList, 1000)
  },
  methods: {
    //上传附件弹框
    handleUploadDialog() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "components/Upload/uploaderDialog" */ 'COMPONENTS/Upload/uploaderDialog.vue'
          ),
        data: {
          fileData: util.utils.cloneDeep(this.queryForm.quotaStrategyFileSaveRequests),
          isView: this.pageType === 'detail', // 是否为预览
          required: false, // 是否必须
          title: this.$t('附件')
          // isSingleFile: true
        },
        success: (res) => {
          // 判断是否有缓存的文件数组 调文件删除接口后清空缓存的文件数组
          const ids = []
          for (let i = 0; i < this.quotaStrategyFileSaveRequests.length; i++) {
            const file = this.quotaStrategyFileSaveRequests[i]
            if (res.every((j) => j.originId !== file.originId)) {
              ids.push(file.originId)
            }
          }
          if (
            this.quotaStrategyFileSaveRequests &&
            this.quotaStrategyFileSaveRequests.length &&
            ids.length
          ) {
            const params = ids
            this.$API.quotaApplyRecord.deleteFileByIds(params).then((resp) => {
              const { code } = resp
              if (code === 200) {
                let fileList = JSON.parse(JSON.stringify(res))
                this.quotaStrategyFileSaveRequests = []
                this.queryForm.quotaStrategyFileSaveRequests = fileList
                this.getFormFileText()
                // this.$refs.ruleForm.validateField('quotaStrategyFileSaveRequests')
              }
            })
          } else {
            let fileList = JSON.parse(JSON.stringify(res))
            this.queryForm.quotaStrategyFileSaveRequests = fileList
            this.getFormFileText()
            // this.$refs.ruleForm.validateField('quotaStrategyFileSaveRequests')
          }
        }
      })
    },
    getAdjustText(code) {
      let text = ''
      this.isAdjustList.forEach((i) => {
        if (i.code == code) {
          text = i.text
        }
      })
      return text
    },
    // 获取采购开发列表
    getUserList(val) {
      // let params = null
      // params = {
      //   page: {
      //     current: 1,
      //     size: 20
      //   },
      //   subjectType: 0
      // }
      let params = {
        fuzzyName: val.text,
        // fuzzyName: '',
        orgLevelCode: 'ORG05',
        orgType: 'ORG001ADM'
      }
      this.$API.quotaApplyRecord.getBuyerList(params).then((res) => {
        // let data = JSON.parse(JSON.stringify(res.data))
        // this.userArrList = []
        if (res.code == 200 && res.data != null) {
          const userArrList = res.data.map((item) => {
            item.employeeName = item.accountName + '-' + item.employeeName
            return item
          })
          const newArr = userArrList.concat(this.userArrList)
          let map = new Map()
          for (let item of newArr) {
            map.set(item.accountName, item)
          }
          this.userArrList = [...map.values()]
          this.noRecordsTemplate = this.$t('没有找到记录')
        }
        // data.map((item) => {
        //   item.employeeName = item.userName + '-' + item.fullName
        // })
        // this.userArrList = data
        // this.fields = { text: 'employeeName', value: 'employeeName' }
      })
    },
    // 获取详情数据
    getDetail() {
      const params = {
        condition: 'and',
        rules: [
          {
            field: 'id',
            label: '',
            operator: 'equal',
            type: 'string',
            value: this.detailId
          }
        ],
        page: {
          current: this.pageSettings.current,
          size: this.pageSettings.pageSize
        }
      }
      // 调用行表条件查询查详情
      this.$API.quotaApplyRecord.getRecordList(params).then((res) => {
        const { code, data } = res
        if (code === 200) {
          if (data.records && data.records.length) {
            const row = { ...data.records[0] }
            const userList = row.purchaseDeveloper ? row.purchaseDeveloper.split(',') : []
            this.userArrList = userList.map((item) => {
              return {
                employeeName: item
              }
            })
            this.queryForm = {
              ...row,
              userList,
              createDate: utils.formatTime(new Date(row.createDate), 'YYYY-mm-dd HH:MM:SS')
            }
            let quotaStrategyFileSaveRequests = row.fileList
            quotaStrategyFileSaveRequests.forEach((i) => {
              i.originId = i.id
              i.id = i.fileId
            })
            // quotaStrategyFileSaveRequests:
            //   row.fileList.map((i) => {
            //     let obj = {
            //       ...i,
            //       originId: i.id,
            //       id: i.fileId
            //     }
            //     return obj
            //   }) || []
            this.queryForm.quotaStrategyFileSaveRequests = quotaStrategyFileSaveRequests
            this.quotaStrategyFileSaveRequests = this.queryForm.quotaStrategyFileSaveRequests
            this.getFormFileText()
            this.search()
          }
        }
      })
    },
    materialTableDialogCofig(row) {
      let params = {}
      if (row && row.siteCode) {
        params = {
          siteCode: row.siteCode
        }
      }
      return {
        pageConfig: pageConfig(
          // '/masterDataManagement/auth/supplier/getSiteItemFuzzyQuery',
          `/masterDataManagement/tenant/item-org-rel/pagedQueryWithCategory?BU_CODE=${localStorage.getItem('currentBu')}`,
          'material',
          params
        ),
        text: 'itemCode',
        value: 'itemCode'
      }
    },
    // 供应商放大镜弹窗配置
    supplierDialogCofig(siteCode) {
      let params = {}
      if (siteCode) {
        params = {
          siteCode
        }
      }
      return {
        pageConfig: pageConfig(
          '/masterDataManagement/tenant/supplier/pagedQueryBySiteCode',
          'supplier',
          params
        ),
        text: 'supplierName',
        value: 'supplierCode'
      }
    },
    moment,
    startDateChange(date, row) {
      if (!date) {
        this.$set(row, 'endMonthTime', '')
      }
    },
    getFormatTime(date, format) {
      if (date && new Date(date)) {
        return utils.formatTime(new Date(date), format)
      }
      return ''
    },
    getFactoryList() {
      // this.$API.customization
      //   .queryUserPermission({ dimensionCode: 'SITE', subjectId: 0, subjectType: 0 })
      //   .then((res) => {
      //     this.factoryList = res.data
      //   })
      this.$API.customization.getOrgListByCode({ orgCode: 'SITE' }).then((res) => {
        this.factoryList = res.data.map((i) => {
          return {
            ...i,
            codeAndName: i.dimensionCodeValue + '-' + i.dimensionNameValue
          }
        })
      })
    },
    handleAdd() {
      const obj = {
        isAdd: true,
        editable: true,
        siteId: '',
        siteCode: '',
        siteName: '',
        itemCode: '',
        itemId: '',
        itemName: '',
        categoryId: '',
        quantity: '',
        categoryCode: '',
        categoryName: '',
        supplierId: '',
        supplierCode: '',
        supplierName: '',
        reason: '',
        measure: '',
        startMonthTime: null,
        endMonthTime: null
      }
      if (!obj.siteCode && this.factoryList && this.factoryList.length === 1) {
        obj.siteCode = this.factoryList[0]['dimensionCodeValue']
        obj.siteId = this.factoryList[0]['dimensionIdValue']
        obj.siteName = this.factoryList[0]['dimensionNameValue']
      }
      this.dataSource.unshift(obj)
      this.$refs.xTable.$refs.xGrid.setEditRow(obj)
    },
    // 表格上方按钮点击事件
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRows = $grid.getCheckboxRecords() // 表格勾选行的内容
      if (code === 'Add') {
        this.handleAdd()
      }
      if (code === 'Delete' && selectedRows.length <= 0) {
        this.$toast({
          content: this.$t('请勾选需要操作的数据'),
          type: 'warning'
        })
        return
      }
      if (code !== 'Add' && this.dataSource.some((i) => !i.id || i.id.includes('row_'))) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('存在未保存的数据，是否继续当前操作')
          },
          success: () => {
            if (code === 'Import') {
              // 导入
              this.handleImport()
            } else if (code === 'Delete') {
              // 删除
              this.handleDelete(selectedRows)
            }
          }
        })
        return
      }
      if (code === 'Import') {
        // 导入
        this.handleImport()
      } else if (code === 'Delete') {
        // 删除
        this.handleDelete(selectedRows)
      }
    },
    // 导入
    handleImport() {
      // if (!this.queryForm.title) {
      //   this.$toast({
      //     content: this.$t('请输入配额调整申请标题'),
      //     type: 'warning'
      //   })
      //   return
      // }
      // if (!this.queryForm.userList || this.queryForm.userList.length === 0) {
      //   this.$toast({
      //     content: this.$t('请至少选择一名采购开发'),
      //     type: 'warning'
      //   })
      //   return
      // }
      this.$refs.ruleForm.validate((val) => {
        if (val) {
          const asyncParams = {
            header: {
              title: this.queryForm.title,
              remark: this.queryForm.remark,
              userList: this.queryForm.userList.join()
            }
          }
          if (this.detailId) {
            asyncParams.header.id = this.detailId
          }
          this.$dialog({
            modal: () => import('@/components/uploadDialog'),
            data: {
              title: this.$t('上传/导入'),
              importApi: this.$API.quotaApplyRecord.importRecordRowList,
              asyncParams: { header: encodeURIComponent(JSON.stringify(asyncParams.header)) },
              downloadTemplateApi: this.$API.quotaApplyRecord.getImportTemplate
            },
            success: (res) => {
              const { msg, data } = res
              if (msg === 'success') {
                this.addDetailId = data
                this.$router.push({
                  path: `/sourcing/quota-calc/quota-apply-record-detail`,
                  query: {
                    id: data,
                    type: 'edit',
                    timeStamp: new Date().getTime()
                  }
                })
              }
              // 导入之后刷新列表
              // this.search()
            }
          })
        }
      })
    },
    // 删除
    handleDelete(selectedRows) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const deleteList = []
          // 遍历已勾选的数据 有id的就是线上数据需要调删除接口
          const arr = this.dataSource.filter((i) => {
            return !selectedRows.some((j) => {
              if (j && j.id && !j.id.includes('row_')) {
                deleteList.push(j.id)
              }
              if (j && j.id && j.id.includes('row_')) {
                return j.id === i.id
              }
            })
          })
          if (deleteList.length) {
            const params = Array.from(new Set(deleteList)) // 去个重
            this.$API.quotaApplyRecord.deleteRecordItem(params).then((res) => {
              const { code } = res
              if (code === 200) {
                this.afterDeleteEvent(arr)
              }
            })
            return
          }
          // 删除后刷新列表
          this.afterDeleteEvent(arr)
        }
      })
    },
    afterDeleteEvent(arr) {
      this.$toast({
        content: this.$t('删除成功'),
        type: 'success'
      })
      this.dataSource = arr
    },
    // 切换page
    handleCurrentChange(currentPage) {
      if (this.dataSource.some((i) => i.id.includes('row_'))) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('存在未保存的数据，是否继续当前操作')
          },
          success: () => {
            this.pageSettings.current = currentPage
            this.search()
          }
        })
        return
      }
      this.pageSettings.current = currentPage
      this.search()
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      if (this.dataSource.some((i) => i.id.includes('row_'))) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('存在未保存的数据，是否继续当前操作')
          },
          success: () => {
            this.pageSettings.pageSize = pageSize
            this.search()
          }
        })
        return
      }
      this.pageSettings.pageSize = pageSize
      this.search()
    },
    // 查询行表数据 、利用的是分页查询的接口进行条件查询查详情，入参格式为京东组件格式
    search() {
      console.log('search', this.queryForm)
      const params = {
        condition: 'and',
        rules: [
          {
            field: 'requestNumber',
            label: '',
            operator: 'contains',
            type: 'string',
            value: this.queryForm.requestNumber
          }
        ],
        page: {
          current: this.pageSettings.current,
          size: this.pageSettings.pageSize
        }
      }
      this.loading = true
      this.$API.quotaApplyRecord
        .getRecordRowList(params)
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            const total = res?.data?.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)
            this.dataSource = data.records.map((i) => {
              return {
                ...i,
                startMonthTime: i.startMonthTime
                  ? this.getFormatTime(Number(i.startMonthTime), 'YYYY-mm-dd')
                  : null,
                endMonthTime: i.endMonthTime
                  ? this.getFormatTime(Number(i.endMonthTime), 'YYYY-mm-dd')
                  : null
              }
            })
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    setTableEmpty(fieldsarr, row) {
      for (let i = 0; i < fieldsarr.length; i++) {
        this.$set(row, fieldsarr[i], '')
      }
    },
    handleFactoryChange(e, row) {
      // 工厂下拉变更
      if (row) {
        // 表格内的下拉选择
        // 重新选择时，需清空行上已填写的字段（供应商、物料、品类、开始日期、结束日期）
        const fieldsarr = [
          'supplierCode',
          'supplierName',
          'supplierId',
          'itemId',
          'itemCode',
          'itemName',
          'categoryId',
          'categoryCode',
          'categoryName',
          'startMonthTime',
          'endMonthTime'
        ]
        row.siteCode = e.value
        this.setTableEmpty(fieldsarr, row)
        for (let i = 0; i < this.factoryList.length; i++) {
          const element = this.factoryList[i]
          if (element.dimensionCodeValue === e.value) {
            this.$set(row, 'siteId', element.dimensionIdValue)
            this.$set(row, 'siteName', element.dimensionNameValue)
            // row.siteId = element.dimensionIdValue
            // row.siteName = element.dimensionNameValue
            return
          }
        }
      }
    },
    materialCodeChange(e, row) {
      // 物料编码变更
      if (row) {
        // 表格内物料编码变更
        // 工厂选择后，才可选择物料编码；物料需在工厂下是有效物料（物料与工厂关系表）
        const fieldsarr = ['startMonthTime', 'endMonthTime']
        this.setTableEmpty(fieldsarr, row)
        const { itemId, itemCode, itemName, categoryCode, categoryName, categoryId } = e
        row.itemId = itemId
        row.itemCode = itemCode
        row.itemName = itemName
        // 带出品类信息
        row.categoryCode = categoryCode
        row.categoryName = categoryName
        row.categoryId = categoryId
        // if (categoryResponse) {
        // } else if (categoryItemResponse) {
        //   row.categoryCode = categoryItemResponse.categoryCode
        //   row.categoryName = categoryItemResponse.categoryName
        //   row.categoryId = categoryItemResponse.categoryId
        // }
      }
    },
    supplierCodeChange(e, row) {
      // 供应商编码变更
      if (row) {
        // 表格内供应商编码变更
        // 重新选择时，需清空行上已填写的字段（物料、品类、开始日期、结束日期）
        const fieldsarr = [
          'itemId',
          'itemCode',
          'itemName',
          'categoryId',
          'categoryCode',
          'categoryName',
          'startMonthTime',
          'endMonthTime'
        ]
        this.setTableEmpty(fieldsarr, row)
        row.supplierId = e.id
        row.supplierCode = e.supplierCode
        row.supplierName = e.supplierName
      }
    },
    handleAdjustChange(e, row) {
      const fieldsarr = ['startMonthTime', 'endMonthTime']
      this.setTableEmpty(fieldsarr, row)
      row.adjust = e.value
    },
    getParams() {
      const itemList = this.dataSource.map((i) => {
        let obj = {
          ...i,
          id: i.id.includes('row_') ? null : i.id,
          startMonthTimeStr: i.startMonthTime
            ? utils.formatTime(new Date(i.startMonthTime), 'YYYYmmdd')
            : null,
          endMonthTimeStr: i.endMonthTime
            ? utils.formatTime(new Date(i.endMonthTime), 'YYYYmmdd')
            : null,
          startMonthTime: null,
          endMonthTime: null
        }
        return obj
      })
      const params = {
        itemList,
        remark: this.queryForm.remark,
        title: this.queryForm.title,
        quotaStrategyFileSaveRequests: this.queryForm.quotaStrategyFileSaveRequests.map((i) => {
          let obj = {
            ...i,
            fileId: i.id
          }
          delete obj.id
          if (obj.originId) {
            obj.id = obj.originId
          }
          // if (params.id) {
          //   obj.strategyId = params.id
          // }
          // if (params.code) {
          //   obj.strategyCode = params.code
          // }
          return obj
        })
      }
      if (this.queryForm.userList && this.queryForm.userList.length) {
        params.userList = this.queryForm.userList.join()
      }
      if (this.pageType !== 'add') {
        params.headerId = this.detailId
      }
      if (this.pageType === 'add' && this.addDetailId) {
        params.headerId = this.addDetailId
      }
      return params
    },
    // 保存的接口分为新增和更新
    getSaveApi() {
      let api = this.$API.quotaApplyRecord.addRecordRow
      if (this.pageType === 'edit') {
        api = this.$API.quotaApplyRecord.updateRecordRow
      }
      return api
    },
    // 保存时防抖、判断是否存在没点确定的行数据在调用保存接口
    saveDetail: throttle(function () {
      if (this.isSaving === true) {
        return false
      }
      for (let i = 0; i < this.dataSource.length; i++) {
        const row = this.dataSource[i]
        // if (row.editable) {
        //   this.$toast({
        //     content: this.$t('请确定或取消未完成编辑的数据'),
        //     type: 'warning'
        //   })
        //   return
        // }
        if (!row.siteCode) {
          this.$toast({
            content: this.$t(`第${i + 1}条数据请选择工厂`),
            type: 'warning'
          })
          return
        }
        if (!row.supplierCode) {
          this.$toast({
            content: this.$t(`第${i + 1}条数据请选择供应商编码`),
            type: 'warning'
          })
          return
        }
        if (!row.itemCode) {
          this.$toast({
            content: this.$t(`第${i + 1}条数据请选择物料编码`),
            type: 'warning'
          })
          return
        }
        if (row.quantity && isNaN(row.quantity)) {
          this.$toast({
            content: this.$t(`第${i + 1}条数据调整数量请输入数字`),
            type: 'warning'
          })
          return
        }
        if (!row.reason) {
          this.$toast({
            content: this.$t(`第${i + 1}条数据请输入调整原因`),
            type: 'warning'
          })
          return
        }
        if (!row.adjust) {
          this.$toast({
            content: this.$t(`第${i + 1}条数据请选择是否调整配额`),
            type: 'warning'
          })
          return
        }
        if ((row.adjust == 2 && !row.startMonthTime) || (!row.startMonthTime && row.endMonthTime)) {
          this.$toast({
            content: this.$t(`第${i + 1}条数据调整配额为“是”请选择开始日期`),
            type: 'warning'
          })
          return
        }
        if (
          row.startMonthTime &&
          row.endMonthTime &&
          new Date(this.getFormatTime(row.startMonthTime, 'YYYY-mm-dd')).getTime() >
            new Date(this.getFormatTime(row.endMonthTime, 'YYYY-mm-dd')).getTime()
        ) {
          this.$toast({
            content: this.$t(`第${i + 1}条数据结束日期不可早于开始日期`),
            type: 'warning'
          })
          return
        }
      }
      this.saveDetailFn()
    }, 1000),
    saveDetailFn() {
      this.$refs.ruleForm.validate((val) => {
        if (val) {
          this.$dialog({
            data: {
              title: this.$t('操作'),
              message: this.$t(`确认保存吗？`)
            },
            success: () => {
              const params = this.getParams()
              this.isSaving = true
              this.getSaveApi()(params)
                .then((res) => {
                  if (res.code == 200) {
                    this.$toast({
                      content: this.$t('保存成功'),
                      type: 'success'
                    })
                    this.$router.push({
                      path: `/sourcing/quota-calc/quota-apply-record-detail`,
                      query: {
                        type: 'edit',
                        id: res.data,
                        timeStamp: new Date().getTime()
                      }
                    })
                    // if (this.pageType === 'add') {
                    //   // 如果是新增页面就跳转编辑
                    // } else {
                    //   this.search()
                    // }
                  }
                })
                .finally(() => {
                  this.isSaving = false
                })
            }
          })
        }
      })
    },
    // 提交时防抖、判断是否存在没点确定的行数据再调用提交接口
    submit: throttle(function () {
      if (this.isSaving === true) {
        return false
      }
      this.$refs.ruleForm.validate((val) => {
        if (val) {
          for (let i = 0; i < this.dataSource.length; i++) {
            const row = this.dataSource[i]
            // if (row.editable) {
            //   this.$toast({
            //     content: this.$t('请确定或取消未完成编辑的数据'),
            //     type: 'warning'
            //   })
            //   return
            // }
            if (!row.siteCode) {
              this.$toast({
                content: this.$t(`第${i + 1}条数据请选择工厂`),
                type: 'warning'
              })
              return
            }
            if (!row.supplierCode) {
              this.$toast({
                content: this.$t(`第${i + 1}条数据请选择供应商编码`),
                type: 'warning'
              })
              return
            }
            if (!row.itemCode) {
              this.$toast({
                content: this.$t(`第${i + 1}条数据请选择物料编码`),
                type: 'warning'
              })
              return
            }
            if (isNaN(row.quantity)) {
              this.$toast({
                content: this.$t(`第${i + 1}条数据调整数量请输入数字`),
                type: 'warning'
              })
              return
            }
            if (!row.reason) {
              this.$toast({
                content: this.$t(`第${i + 1}条数据请输入调整原因`),
                type: 'warning'
              })
              return
            }
            if (!row.adjust) {
              this.$toast({
                content: this.$t(`第${i + 1}条数据请选择是否调整配额`),
                type: 'warning'
              })
              return
            }
            if (
              (row.adjust == 2 && !row.startMonthTime) ||
              (!row.startMonthTime && row.endMonthTime)
            ) {
              this.$toast({
                content: this.$t(`第${i + 1}条数据调整配额为“是”请选择开始日期`),
                type: 'warning'
              })
              return
            }
            if (
              row.startMonthTime &&
              row.endMonthTime &&
              new Date(row.startMonthTime) > new Date(row.endMonthTime)
            ) {
              this.$toast({
                content: this.$t(`第${i + 1}条数据结束日期不可早于开始日期`),
                type: 'warning'
              })
              return
            }
          }
          this.submitFn()
        }
      })
    }, 1000),
    submitFn() {
      this.$dialog({
        data: {
          title: this.$t('操作'),
          message: this.$t(`确认提交吗？`)
        },
        success: () => {
          const params = this.getParams()
          this.isSaving = true
          this.$API.quotaApplyRecord
            .submitQuotaApply(params)
            .then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('提交成功'),
                  type: 'success'
                })
                this.$router.push({
                  path: `/sourcing/quota-calc/quota-apply-record`,
                  query: {
                    timeStamp: new Date().getTime()
                  }
                })
              }
            })
            .finally(() => {
              this.isSaving = false
            })
        }
      })
    },
    // 返回按钮事件
    backToBusinessConfig() {
      // this.$router.go(-1)
      this.$router.push({
        path: `/sourcing/quota-calc/quota-apply-record`,
        query: {
          timeStamp: new Date().getTime()
        }
      })
    },
    getFormFileText() {
      let _list = this?.queryForm?.quotaStrategyFileSaveRequests ?? []
      if (_list.length) {
        let _name = []
        _list.forEach((e) => {
          _name.push(e.fileName)
        })
        this.formFileText = _name.join(',')
      } else {
        this.formFileText = this.$t('上传附件')
      }
    }
  },
  computed: {
    pageType() {
      // 判断当前页面的类型，add;edit;detail
      return this.$route.query.type || ''
    },
    detailId() {
      // 非新增页面获取详情id
      return this.$route.query.id || ''
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/.mt-form-item {
  width: calc(20% - 20px);
  min-width: 200px;
  display: inline-flex;
  margin-right: 20px;
  .mt-form-item-topLabel {
    .div-auth {
      background: #e3e1e1;
      height: 35px;
      display: flex;
      align-items: center;
      padding: 0 10px;
      color: #0f0f0f;
      font-size: 12px;
      cursor: pointer;
      border-radius: 5px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 200px;
      display: inline-block;
      line-height: 35px;
    }
  }
}
.supplier-box {
  position: absolute;
  width: 100%;
  min-height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: #fff;
  overflow: auto;
  -webkit-overflow-scrolling: scroll;

  .miam-container {
    width: 100%;
    background: #fff;
    padding: 0 20px;

    .page-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .page-title {
        color: #292929;
        font-family: PingFangSC;
        font-size: 20px;
        font-weight: 600;
      }
    }

    .operate-bar {
      height: 60px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      color: #4f5b6d;

      .op-item {
        cursor: pointer;
        align-items: center;
        margin-right: 20px;
        align-items: center;
        background-color: rgba(0, 0, 0, 0);
        border-color: rgba(0, 0, 0, 0);
        color: #00469c;
      }
    }

    .mian-info {
      background: rgba(255, 255, 255, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    }

    .relation-ships {
      background: rgba(255, 255, 255, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      margin-top: 20px;
      height: calc(100% - 164px);
      .button-group {
        display: flex;
        padding: 0 0 16px;
      }
    }
  }
}
.relation-ships {
  // ::v-deep.ant-table-body {
  //   overflow-x: auto !important;
  // }
  ::v-deep.red-color {
    color: red;
  }
}
</style>
