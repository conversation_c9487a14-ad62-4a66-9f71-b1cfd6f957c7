<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item :label="$t('工厂')" prop="siteId">
          <mt-select
            v-model="formObject.siteId"
            :data-source="siteArr"
            :allow-filtering="true"
            :show-clear-button="true"
            :placeholder="$t('请选择工厂')"
            @change="siteIdChange"
            :fields="{ text: 'siteName', value: 'id' }"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('物料编码')" prop="itemCode">
          <div style="display: flex">
            <mt-input
              :readonly="true"
              v-model="formObject.itemCode"
              :show-clear-button="false"
              float-label-type="Never"
              :placeholder="$t('物料编码')"
              style="width: 355px"
            ></mt-input>
            <!-- <VirtualSelect%"
              row-key="id"
              style="flex: 1"
              row-name="itemName"
              :selected-value="selectedMaterialItem"
              :search-keys="['itemName']"
              @change="handleMaterialItemChange"
              :display-keys="['itemName']"
              :remote-method="materialItemRemoteMethod"
              filter-type="local"
              float-label-type="Never"
            /> -->
            <mt-button
              style="width: 28px"
              css-class="e-flat"
              @click="showInnerDialog($t('物料'))"
              icon-css="mt-icons mt-icon-icon_search"
            ></mt-button>
          </div>
        </mt-form-item>
        <mt-form-item :label="$t('物料名称')">
          <mt-input
            :readonly="true"
            v-model="formObject.itemName"
            :show-clear-button="false"
            float-label-type="Never"
            :placeholder="$t('物料名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="supplierId" :label="$t('供应商编码')">
          <mt-select
            ref="supplierRef"
            v-model="formObject.supplierId"
            :allow-filtering="true"
            float-label-type="Never"
            :data-source="supplierList"
            :fields="{ text: 'supplierCode', value: 'id' }"
            @change="handleSelectChange"
            :filtering="filteringCompany"
            :placeholder="$t('请选择供应商')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="supplierName" :label="$t('供应商名称')">
          <mt-input
            :readonly="true"
            v-model="formObject.supplierName"
            :show-clear-button="false"
            float-label-type="Never"
            :placeholder="$t('供应商编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="quoteAttribute" :label="$t('报价属性')">
          <mt-select
            ref="quoteAttributeRef"
            v-model="formObject.quoteAttribute"
            float-label-type="Never"
            :data-source="quoteAttributeList"
            :placeholder="$t('价格属性')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('有效期限')" prop="validTime">
          <mt-date-range-picker
            ref="validTimeRef"
            v-model="formObject.validTime"
            :placeholder="$t('请选择有效期限')"
            :open-on-focus="true"
            format="yyyy-MM-dd"
          ></mt-date-range-picker>
        </mt-form-item>
        <mt-form-item :label="$t('最小起拆量')" prop="minOpenQuantity">
          <mt-inputNumber
            v-model="formObject.minOpenQuantity"
            :min="0"
            :placeholder="$t('请输入最小起拆量')"
            class="number-item"
            @change="numberChange(formObject, 'minOpenQuantity')"
          ></mt-inputNumber>
        </mt-form-item>
        <mt-form-item :label="$t('最小包装量')" prop="minPackageQuantity">
          <mt-inputNumber
            v-model="formObject.minPackageQuantity"
            :min="0"
            :placeholder="$t('请输入最小包装量')"
            class="number-item"
            @change="numberChange(formObject, 'minPackageQuantity')"
          ></mt-inputNumber>
        </mt-form-item>
        <mt-form-item :label="$t('最小采购量')" prop="minPurchaseQuantity">
          <mt-inputNumber
            v-model="formObject.minPurchaseQuantity"
            :min="0"
            class="number-item"
            :placeholder="$t('请输入最小采购量')"
            @change="numberChange(formObject, 'minPurchaseQuantity')"
          ></mt-inputNumber>
        </mt-form-item>
        <mt-form-item :label="$t('配额比')" prop="quotaPercent">
          <mt-inputNumber
            v-model="formObject.quotaPercent"
            :min="0"
            :max="100"
            class="number-item"
            :placeholder="$t('请输入配额比')"
            @change="quotaPercentChange(formObject, 'quotaPercent')"
          ></mt-inputNumber>
        </mt-form-item>
        <mt-form-item prop="unitId" :label="$t('单位')">
          <mt-select
            ref="unitRef"
            v-model="formObject.unitId"
            float-label-type="Never"
            :data-source="unitList"
            :fields="{ text: 'unitName', value: 'id' }"
            :placeholder="$t('请选择单位')"
            @change="unitChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('备注')">
          <mt-input
            width="100%"
            type="text"
            v-model="formObject.remark"
            :max-length="110"
            :placeholder="$t('请输入备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
// import VirtualSelect from "COMPONENTS/VirtualSelect";
// import utils from "UTILS/utils.js";
import { throttle } from 'lodash'
export default {
  // components: { VirtualSelect },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('关闭') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确认') }
        }
      ],
      formObject: {
        // 物料编码
        itemCode: '',
        // 物料ID
        itemId: '',
        // 物料名称
        itemName: '',
        // 行号
        lineNo: '',
        // 最小起拆量
        minOpenQuantity: '',
        // 最小包装量
        minPackageQuantity: '',
        // 最小采购量
        minPurchaseQuantity: '',
        // 配额值
        quotaPercent: '',
        // 备注
        remark: '',
        // 工厂 库存组织编码
        siteCode: '',
        // 工厂 库存组织ID
        siteId: '',
        // 工厂 库存组织名称
        siteName: '',
        // 状态
        status: 0,
        // 供应商编码
        supplierCode: '',
        // 供应商ID
        supplierId: '',
        // 供应商名称
        supplierName: '',
        // 同步信息
        syncInfo: '',
        // 同步状态
        syncStatus: '',
        // 单位编码
        unitCode: '',
        // 单位ID
        unitId: '',
        // 单位名称
        unitName: '',
        // 有效结束时间
        validEndTime: '',
        // 有效开始时间
        validStartTime: '',
        // 报价属性
        quoteAttribute: ''
      },
      formRules: {
        siteId: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }],
        itemCode: [{ required: true, message: this.$t('请选择物料'), trigger: 'blur' }],
        supplierId: [{ required: true, message: this.$t('请选择供应商'), trigger: 'blur' }],
        validTime: [
          {
            required: true,
            message: this.$t('请选择有效期限'),
            trigger: 'blur'
          }
        ],
        minOpenQuantity: [
          {
            required: true,
            message: this.$t('请输入最小起拆量'),
            trigger: 'blur'
          }
        ],
        minPackageQuantity: [
          {
            required: true,
            message: this.$t('请输入最小包装量'),
            trigger: 'blur'
          }
        ],
        minPurchaseQuantity: [
          {
            required: true,
            message: this.$t('请输入最小采购量'),
            trigger: 'blur'
          }
        ],
        quotaPercent: [{ required: true, message: this.$t('请输入配额比'), trigger: 'blur' }],
        unitId: [{ required: true, message: this.$t('请选择单位'), trigger: 'blur' }],
        quoteAttribute: [
          {
            required: true,
            message: this.$t('请选择报价属性'),
            trigger: 'blur'
          }
        ]
      },
      editStatus: false,

      siteArr: [],
      selectedMaterialItem: {},
      supplierList: [], //供应商列表
      unitList: [], //基本单位列表
      quoteAttributeList: [
        {
          text: this.$t('寄售价'),
          value: 'mailing_price'
        },
        {
          text: this.$t('标准价'),
          value: 'standard_price'
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData.title == this.$t('编辑额度分配')) {
      this.formObject = JSON.parse(JSON.stringify(this.modalData.info))
      this.formObject.quotaPercent = Number(this.formObject.quotaPercent) * 100
      if (this.formObject.validStartTime) {
        this.selectedMaterialItem = {
          itemName: this.formObject.itemName
        }
        this.formObject.validTime = [
          new Date(this.formObject.validStartTime),
          new Date(this.formObject.validEndTime)
        ]
      }
    }
    this.init()
  },
  methods: {
    filteringCompany: throttle(function (e) {
      let { text } = e
      this.$API.quotaConfig
        .getSupplierList({
          supplierCode: text
        })
        .then((res) => {
          this.supplierList = res.data
        })
    }, 1000),
    numberChange(item, type) {
      if (String(item[type]).length > 16) {
        this.$set(item, type, String(item[type]).slice(0, 16))
      }
      let arr = String(item[type]).split('.')
      if (arr.length == 2 && arr[1].length > 2) {
        this.$set(item, type, String(item[type]).slice(0, arr[0].length + 3))
      }
    },
    quotaPercentChange(item, type) {
      let arr = String(item[type]).split('.')
      if (arr.length == 2 && arr[1].length > 2) {
        item[type] = String(item[type]).slice(0, arr[0].length + 3)
      }
    },
    // 初始化
    init() {
      this.$API.quotaConfig.getSupplierList().then((res) => {
        this.supplierList = res.data
      })
      this.$API.quotaConfig.pagedQueryUnit().then((res) => {
        this.unitList = res.data.records
      })
      this.$API.quotaConfig.getSiteList().then((res) => {
        this.siteArr = res.data.records
      })
    },
    confirm() {
      this.$refs.dialogRef.validate((val) => {
        if (val) {
          this.$emit('confirm-function', this.formObject)
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    handleMaterialItemChange(item) {
      this.selectedMaterialItem = item
      this.formObject.itemCode = item.itemCode
      this.formObject.itemName = item.itemName
      this.formObject.itemId = item.id
      this.$refs.dialogRef.validateField('itemCode')
    },
    materialItemRemoteMethod() {
      return this.$API.masterData.getItemList().then((res) => res.data)
    },
    showInnerDialog(type) {
      this.$dialog({
        modal: () => import('./components/selectSKUOrCategory.vue'),
        data: { type },
        success: (items) => {
          this.selectedMaterialItem = items[0]
          this.formObject.itemCode = items[0].itemCode
          this.formObject.itemName = items[0].itemName
          this.formObject.itemId = items[0].id
          this.$nextTick(() => {
            this.$refs.dialogRef.validateField('itemCode')
          })
        }
      })
    },
    // 供应商选择
    handleSelectChange(e) {
      let _data = e.itemData
      this.formObject.supplierCode = _data.supplierCode
      this.formObject.supplierId = _data.id
      this.formObject.supplierName = _data.supplierName
    },
    // 单位选择
    unitChange(e) {
      let _data = e.itemData
      this.formObject.unitCode = _data.unitCode
      this.formObject.unitId = _data.id
      this.formObject.unitName = _data.unitName
    },
    // 选择工厂
    siteIdChange(e) {
      let _data = e.itemData
      this.formObject.siteCode = _data.siteCode
      this.formObject.siteId = _data.id
      this.formObject.siteName = _data.siteName
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  padding-top: 18px;
  font-size: 16px;
  display: flex;
  flex-direction: column;
  .mt-form {
    display: flex;
    width: 100%;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 46%;
    }
  }
  .table-content {
    flex: 1;
  }
}
</style>
