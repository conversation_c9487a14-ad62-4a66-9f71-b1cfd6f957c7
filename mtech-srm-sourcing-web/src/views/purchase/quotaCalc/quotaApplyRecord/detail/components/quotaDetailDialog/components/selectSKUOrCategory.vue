<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :style="{ width: '940px' }"
    :header="$t('选择') + modalData.type"
    @beforeClose="cancel"
  >
    <mt-template-page ref="table" :template-config="pageConfig" />
  </mt-dialog>
</template>

<script>
import { categoryTemplateConfig } from '../config'
export default {
  name: 'CategoryTemplateConfig',
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  computed: {
    pageConfig() {
      return categoryTemplateConfig
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    confirm() {
      this.$emit(
        'confirm-function',
        this.$refs.table.getCurrentUsefulRef().ejsRef.getSelectedRecords()
      )
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style scoped></style>
