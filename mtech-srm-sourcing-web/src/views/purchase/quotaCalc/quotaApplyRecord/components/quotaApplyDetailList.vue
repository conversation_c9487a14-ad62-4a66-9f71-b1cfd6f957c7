<template>
  <div class="quato-customization">
    <collapse-search :is-grid-display="true" @reset="reset" @search="search">
      <mt-form ref="ruleForm" :model="queryForm">
        <mt-form-item prop="requestNumber" :label="$t('配额调整申请单号')" label-style="top">
          <mt-input v-model="queryForm.requestNumber" :placeholder="$t('请输入配额调整申请单号')" />
        </mt-form-item>
        <mt-form-item prop="title" :label="$t('配额调整申请标题')" label-style="top">
          <mt-input v-model="queryForm.title" :placeholder="$t('请输入配额调整申请标题')" />
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('单据状态')" label-style="top">
          <mt-select
            v-model="queryForm.status"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="statusOptions"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('状态')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="siteCode" :label="$t('工厂代码')" label-style="top">
          <mt-select
            v-model="queryForm.siteCode"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="factoryList"
            :fields="{ text: 'orgCode', value: 'orgCode' }"
            :placeholder="$t('请选择工厂代码')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
          <magnifier-input
            ref="itemCode"
            :config="materialDialogCofig"
            @change="materialCodeChange($event)"
          ></magnifier-input>
        </mt-form-item>
        <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
          <magnifier-input
            ref="supplierName"
            :config="supplierDialogCofig"
            @change="supplierCodeChange($event)"
          ></magnifier-input>
        </mt-form-item>
        <mt-form-item prop="categoryName" :label="$t('品类')" label-style="top">
          <magnifier-input
            ref="categoryName"
            :config="categoryDialogCofig"
            @change="categoryCodeChange($event)"
          ></magnifier-input>
        </mt-form-item>
        <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
          <mt-input v-model="queryForm.createUserName" :placeholder="$t('请输入创建人')" />
        </mt-form-item>
        <mt-form-item prop="createDate" :label="$t('创建时间')" label-style="top">
          <mt-date-range-picker
            v-if="isReloadPicker"
            v-model="queryForm.createDate"
            format="yyyy-MM-dd"
            :separator="$t('至')"
            :placeholder="$t('选择创建时间')"
          ></mt-date-range-picker>
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!----  表格  --------->
    <sc-table
      ref="xTable"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="vxeColumns"
      :table-data="dataSource"
      @refresh="search"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import utils from '@/utils/utils'
import collapseSearch from '@/components/collapseSearch'
import magnifierInput from '@/components/magnifierInput'
import { pageConfig, vxeColumns, statusOptions } from './../config/applyDetail.js'
import ScTable from '@/components/ScTable/src/index'
export default {
  components: {
    collapseSearch,
    magnifierInput,
    ScTable
  },
  data() {
    return {
      loading: false,
      toolbar: [
        {
          code: 'Export',
          name: this.$t('导出'),
          icon: 'el-icon-delete',
          status: 'info'
        }
      ],
      queryForm: {
        requestNumber: '', // 配额调整申请单号
        title: '', // 配额调整申请标题
        status: '', // 状态
        siteCode: '', // 工厂代码
        itemCode: '', // 物料代码
        supplierName: '',
        categoryName: '', // 品类
        createUserName: '', // 创建人
        createDate: '' // 创建时间
      },
      pageSettings: {
        pageCount: 5,
        pageSize: 20, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [10, 20, 50, 100]
      },
      statusOptions,
      vxeColumns,
      dataSource: [],
      factoryList: [],
      // 物料放大镜弹窗配置
      materialDialogCofig: {
        pageConfig: pageConfig(
          `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
          'material'
        ),
        text: 'itemCode',
        value: 'itemCode'
      },
      // 品类放大镜弹窗配置
      categoryDialogCofig: {
        pageConfig: pageConfig(
          '/masterDataManagement/tenant/permission/queryCategories',
          'category'
        ),
        text: 'categoryName',
        value: 'categoryCode'
      },
      // 供应商放大镜弹窗配置
      supplierDialogCofig: {
        pageConfig: pageConfig(
          '/masterDataManagement/tenant/supplier/pagedQueryByOrgCode',
          'supplier'
        ),
        text: 'supplierName',
        value: 'supplierCode'
      },
      isReloadPicker: true
    }
  },
  created() {
    this.getFactoryList()
    this.search()
  },
  methods: {
    getFactoryList() {
      this.$API.customization.getPermissionSiteList({}).then((res) => {
        this.factoryList = res.data
      })
    },
    isEqual(key) {
      if (key === 'siteCode' || key === 'itemCode') {
        return true
      }
      return false
    },
    getParams(pageSettings) {
      const params = {
        page: {
          current: pageSettings ? this.pageSettings.current : 1,
          size: this.pageSettings.pageSize
        }
      }
      const rules = []
      const keys = Object.keys(this.queryForm)
      const values = Object.values(this.queryForm)
      for (let i = 0; i < values.length; i++) {
        const element = values[i]
        if ((element || element === 0) && keys[i] !== 'createDate') {
          let obj = {
            field: keys[i] === 'createUserName' ? 'header.' + keys[i] : keys[i],
            label: '',
            operator: this.isEqual(keys[i]) ? 'equal' : 'contains',
            type: 'string',
            value: element
          }
          // if (keys[i] === 'createUserName') {
          //   obj[keys[i]] = 'header.' + keys[i]
          // }
          rules.push(obj)
        }
      }
      if (this.queryForm.createDate && this.queryForm.createDate.length) {
        for (let i = 0; i < this.queryForm.createDate.length; i++) {
          // let element = `${new Date(
          //   utils.formatTime(new Date(this.queryForm.createDate[0]), 'YYYY-mm-dd HH:MM:SS')
          // ).getTime()}`
          let element = utils.formatTime(
            new Date(this.queryForm.createDate[0]),
            'YYYY-mm-dd HH:MM:SS'
          )
          if (i === 1) {
            element = `${utils.formatTime(
              new Date(this.queryForm.createDate[1]),
              'YYYY-mm-dd'
            )} 23:59:59`
            // element = `${new Date(element).getTime()}`
          }
          let obj = {
            field: 'header.createDate',
            label: '',
            operator: i === 0 ? 'greaterthan' : 'lessthan',
            type: 'string',
            value: element
          }
          rules.push(obj)
        }
      }
      if (rules.length) {
        params.condition = 'and'
        params.rules = rules
      }
      return params
    },
    handleClickToolBar(args) {
      const { code } = args
      if (code === 'Export') {
        // 导出
        this.handleExport()
      }
    },
    // 导出
    handleExport() {
      const params = this.getParams()
      params.page.size = 9999
      this.$API.quotaApplyRecord.exportRecordRowList(params).then((res) => {
        let fileName = utils.getHeadersFileName(res)
        utils.download({ fileName: fileName, blob: res.data })
      })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageSettings.current = currentPage
      this.search('pageSettings')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.search()
    },
    reset() {
      console.log('重置')
      this.isReloadPicker = false
      this.queryForm = {
        requestNumber: '', // 配额调整申请单号
        title: '', // 配额调整申请标题
        status: '', // 状态
        siteCode: '', // 工厂代码
        itemCode: '', // 物料代码
        supplierName: '',
        categoryName: '', // 品类
        createUserName: '', // 创建人
        createDate: '' // 创建时间
      }
      this.$refs.itemCode.handleClear()
      this.$refs.supplierName.handleClear()
      this.$refs.categoryName.handleClear()
      this.$nextTick(() => {
        this.isReloadPicker = true
      })
      this.search()
    },
    search(pageSettings) {
      console.log('search', this.queryForm)
      const params = this.getParams(pageSettings)
      this.loading = true
      this.$API.quotaApplyRecord
        .getRecordRowList(params)
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            const total = res?.data?.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)
            this.dataSource = data.records
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    materialCodeChange(e) {
      // 物料编码变更
      this.queryForm.itemCode = e.itemCode
    },
    categoryCodeChange(e) {
      // 品类变更
      this.queryForm.categoryName = e.categoryName
    },
    supplierCodeChange(e) {
      this.queryForm.supplierName = e.supplierName
    }
  }
}
</script>

<style lang="scss" scoped>
.quato-customization {
  // margin-top: 24px;
  background: #fff;
  padding: 12px;
  border-radius: 8px;
  height: 100%;
  .button-group {
    display: flex;
    padding: 0 0 8px;
  }
}
.components-table-demo-nested {
  // ::v-deep.ant-table-body {
  //   overflow-x: auto !important;
  // }
  ::v-deep.red-color {
    color: red;
  }
}
</style>
