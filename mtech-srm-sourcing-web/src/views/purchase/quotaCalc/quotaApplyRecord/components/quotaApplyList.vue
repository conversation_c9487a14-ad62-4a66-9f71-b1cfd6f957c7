<template>
  <div class="quato-customization">
    <collapse-search :is-grid-display="true" @reset="reset" @search="search">
      <mt-form ref="ruleForm" :model="queryForm">
        <mt-form-item prop="requestNumber" :label="$t('配额调整申请单号')" label-style="top">
          <mt-input v-model="queryForm.requestNumber" :placeholder="$t('请输入配额调整申请单号')" />
        </mt-form-item>
        <mt-form-item prop="title" :label="$t('配额调整申请标题')" label-style="top">
          <mt-input v-model="queryForm.title" :placeholder="$t('请输入配额调整申请标题')" />
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('单据状态')" label-style="top">
          <mt-select
            v-model="queryForm.status"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="statusOptions"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('状态')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')" label-style="top">
          <mt-input v-model="queryForm.remark" :placeholder="$t('请输入备注')" />
        </mt-form-item>
        <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
          <mt-input v-model="queryForm.createUserName" :placeholder="$t('请选择创建人')" />
        </mt-form-item>
        <mt-form-item prop="createDate" :label="$t('创建时间')" label-style="top">
          <mt-date-range-picker
            v-if="isReloadPicker"
            v-model="queryForm.createDate"
            format="yyyy-MM-dd"
            :separator="$t('至')"
            :placeholder="$t('选择创建时间')"
          ></mt-date-range-picker>
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!----  表格  --------->
    <sc-table
      ref="xTable"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="vxeColumns"
      :table-data="dataSource"
      @refresh="search"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
          >{{ item.name }}</vxe-button
        >
      </template>
      <template #requestNumber="{ row }">
        <div class="editable-row-operations">
          <a
            @click="
              () => handleDetail(row.status === 0 || row.status === 2 ? 'edit' : 'detail', row)
            "
            >{{ row.requestNumber }}</a
          >
        </div>
      </template>
      <template #operationDefault="{ row }">
        <div class="editable-row-operations">
          <a
            :disabled="row.status !== 0 && row.status !== 2"
            @click="() => handleDetail('edit', row)"
            >{{ $t('编辑') }}</a
          >
          <a
            :disabled="row.status !== 0 && row.status !== 2"
            style="margin-left: 10px"
            @click="() => handleDelete(row, index)"
            >{{ $t('删除') }}</a
          >
        </div>
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import utils from '@/utils/utils'
import collapseSearch from '@/components/collapseSearch'
import { vxeColumns, statusOptions } from './../config'
import ScTable from '@/components/ScTable/src/index'
export default {
  components: {
    collapseSearch,
    ScTable
  },
  data() {
    return {
      loading: false,
      toolbar: [
        {
          code: 'Add',
          name: this.$t('新增'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Export',
          name: this.$t('导出'),
          icon: 'el-icon-delete',
          status: 'info'
        }
      ],
      queryForm: {
        requestNumber: '', // 配额调整申请单号
        title: '', // 配额调整申请标题
        remark: '', // 备注
        status: '', // 状态
        createUserName: '', // 创建人
        createDate: null // 创建时间
      },
      pageSettings: {
        pageCount: 5,
        pageSize: 20, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [10, 20, 50, 100]
      },
      statusOptions,
      vxeColumns,
      dataSource: [],
      isReloadPicker: true,
      userId: ''
    }
  },
  created() {
    const pageSettings = JSON.parse(sessionStorage.getItem('quotaApplyPagination'))
    this.pageSettings = {
      ...this.pageSettings,
      ...pageSettings
    }
    this.search()
    const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
    this.userId = userInfo?.uid
  },
  methods: {
    getParams(pageSettings) {
      const params = {
        // ...this.queryForm,
        page: {
          current: pageSettings ? this.pageSettings.current : 1,
          size: this.pageSettings.pageSize
        }
      }
      const rules = []
      const keys = Object.keys(this.queryForm)
      const values = Object.values(this.queryForm)
      for (let i = 0; i < values.length; i++) {
        const element = values[i]
        if ((element || element === 0) && keys[i] !== 'createDate') {
          let obj = {
            field: keys[i],
            label: '',
            operator: keys[i] === 'status' ? 'equal' : 'contains',
            type: 'string',
            value: element
          }
          rules.push(obj)
        }
      }
      if (this.queryForm.createDate && this.queryForm.createDate.length) {
        for (let i = 0; i < this.queryForm.createDate.length; i++) {
          let element = utils.formatTime(
            new Date(this.queryForm.createDate[0]),
            'YYYY-mm-dd HH:MM:SS'
          )
          if (i === 1) {
            element = `${utils.formatTime(
              new Date(this.queryForm.createDate[1]),
              'YYYY-mm-dd'
            )} 23:59:59`
          }
          let obj = {
            field: 'createDate',
            label: '',
            operator: i === 0 ? 'greaterthan' : 'lessthan',
            type: 'string',
            value: element
          }
          rules.push(obj)
        }
      }
      if (rules.length) {
        params.condition = 'and'
        params.rules = rules
      }
      return params
    },
    handleDetail(type, row) {
      const query = {}
      if (row && type) {
        query.id = row.id
      }
      query.type = type
      query.timeStamp = new Date().getTime()
      sessionStorage.setItem('quotaApplyPagination', JSON.stringify(this.pageSettings))
      this.$router.push({
        path: `/sourcing/quota-calc/quota-apply-record-detail`,
        query
      })
    },
    // 删除
    handleDelete(row) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          // 校验数据为拟定状态
          // const ids = this.selectedRowKeys.map((i) => i.id)
          let params = null
          if (row) {
            // 单行删除
            if (row.status !== 0 && row.status !== 2) {
              this.$toast({
                content: this.$t('所选数据存在非拟定或非已驳回状态数据'),
                type: 'warning'
              })
              return
            }
            params = [row.id]
          } else {
            // 勾选删除
            if (this.selectedRows.some((i) => i.status !== 0 && i.status !== 2)) {
              this.$toast({
                content: this.$t('所选数据存在非拟定或非已驳回状态数据'),
                type: 'warning'
              })
              return
            }
            params = this.selectedRows.map((i) => i.id)
          }
          this.$API.quotaApplyRecord.deleteRecordRow(params).then((res) => {
            const { code } = res
            if (code === 200) {
              this.$toast({
                content: this.$t('删除成功'),
                type: 'success'
              })
              this.search()
            }
          })
        }
      })
    },
    handleClickToolBar(args) {
      const { code } = args
      if (code === 'Export') {
        // 导出
        this.handleExport()
      }
      if (code === 'Add') {
        // 导出
        this.handleDetail('add')
      }
    },
    // 导出
    handleExport() {
      const params = this.getParams()
      params.page.size = 9999
      this.$API.quotaApplyRecord.exportRecordList(params).then((res) => {
        let fileName = utils.getHeadersFileName(res)
        utils.download({ fileName: fileName, blob: res.data })
      })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageSettings.current = currentPage
      this.search('pageSettings')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.search()
    },
    reset() {
      this.isReloadPicker = false
      this.queryForm = {
        requestNumber: '', // 配额调整申请单号
        title: '', // 配额调整申请标题
        remark: '', // 备注
        status: '', // 状态
        createUserName: '', // 创建人
        createDate: null // 创建时间
      }
      this.$nextTick(() => {
        this.isReloadPicker = true
      })
      this.search()
    },
    search(pageSettings) {
      console.log('search', this.queryForm)
      // const params = {
      //   // ...this.queryForm,
      //   page: {
      //     current: this.pageSettings.current,
      //     size: this.pageSettings.pageSize
      //   }
      // }
      const params = this.getParams(pageSettings)
      this.loading = true
      this.$API.quotaApplyRecord
        .getRecordList(params)
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            const total = res?.data?.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)
            this.dataSource = data.records
            const pageSettings = JSON.parse(sessionStorage.getItem('quotaApplyPagination'))
            if (pageSettings) {
              sessionStorage.removeItem('quotaApplyPagination')
            }
          }
        })
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.quato-customization {
  // margin-top: 24px;
  background: #fff;
  padding: 12px;
  border-radius: 8px;
  height: 100%;
  .button-group {
    display: flex;
    padding: 0 0 8px;
  }
}
.components-table-demo-nested {
  // ::v-deep.ant-table-body {
  //   overflow-x: auto !important;
  // }
  ::v-deep.red-color {
    color: red;
  }
}
</style>
