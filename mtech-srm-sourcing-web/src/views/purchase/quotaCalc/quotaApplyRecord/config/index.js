import { i18n } from '@/main.js'
import utils from '@/utils/utils'
const materialColumn = [
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    cssClass: 'field-content',
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  }
]
const categoryColumn = [
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    cssClass: 'field-content'
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]
const supplierColumn = [
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    cssClass: 'field-content'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  }
]
export const pageConfig = (url, columnType, params) => {
  let columnData = []
  let gridId = {
    material: '59f94bc5-a9fe-4058-ba6f-7a1d1717aaca',
    category: 'f4f5f721-a9dd-4c0b-8c56-c972ef128b84',
    supplier: 'd374dcea-8e3d-4bc6-88e0-dcc84cd932eb'
  }
  if (columnType === 'material') {
    columnData = materialColumn
  } else if (columnType === 'category') {
    columnData = categoryColumn
  } else {
    columnData = supplierColumn
  }
  return [
    {
      toolbar,
      useToolTemplate: false,
      gridId: gridId[columnType],
      grid: {
        allowFiltering: true,
        columnData,
        allowSorting: false,
        allowSelection: true,
        selectionSettings: {
          type: 'Multiple',
          mode: 'Row'
        },
        asyncConfig: { url, params }
      }
    }
  ]
}

export const statusOptions = utils.getQuotaDict('QUOTA_ITEM_SUBMIT_STATUS') || []

export const vxeColumns = [
  {
    title: i18n.t('配额调整申请单号'),
    field: 'requestNumber',
    width: 200,
    align: 'left',
    slots: {
      default: 'requestNumber'
    }
  },
  {
    title: i18n.t('配额调整申请标题'),
    field: 'title',
    width: 200,
    align: 'left'
  },
  {
    title: i18n.t('备注'),
    field: 'remark',
    width: 300,
    align: 'left'
  },
  {
    title: i18n.t('单据状态'),
    field: 'status',
    align: 'left',
    width: 100,
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  },
  {
    title: i18n.t('创建人'),
    field: 'createUserName',
    width: 100,
    align: 'left'
  },
  { title: i18n.t('创建时间'), field: 'createTime', align: 'left' },
  {
    title: i18n.t('操作'),
    field: 'operation',
    width: 100,
    fixed: 'right',
    slots: {
      default: 'operationDefault'
    }
  }
]
