import { i18n } from '@/main.js'
import utils from '@/utils/utils'
const toolbar = [
  // { id: "Add", icon: "icon_solid_Createorder", title: i18n.t("新增") },
  // { id: "Stop", icon: "icon_solid_Cancel", title: i18n.t("取消") },
  // { id: "Submit", icon: "icon_solid_Submit", title: i18n.t("提交") },
  // { id: "Import", icon: "icon_solid_Createorder", title: i18n.t("导入") },
]
const materialColumn = [
  // {
  //   type: 'checkbox',
  //   width: '60'
  // },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    cssClass: 'field-content',
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  }
]
const categoryColumn = [
  // {
  //   type: 'checkbox',
  //   width: '60'
  // },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    cssClass: 'field-content'
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]
const supplierColumn = [
  // {
  //   type: 'checkbox',
  //   width: '60'
  // },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    cssClass: 'field-content'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  }
]
export const pageConfig = (url, columnType, params) => {
  let columnData = []
  let gridId = {
    material: '2c2cc9d7-13d3-4da8-945b-eb7e9febf088',
    category: '0c31a23a-f482-454f-9efc-0fbbb3122afa',
    supplier: '83b61422-eb5f-476f-a6df-6ebe65462e55'
  }
  if (columnType === 'material') {
    columnData = materialColumn
  } else if (columnType === 'category') {
    columnData = categoryColumn
  } else {
    columnData = supplierColumn
  }
  return [
    {
      toolbar,
      useToolTemplate: false,
      gridId: gridId[columnType],
      grid: {
        allowFiltering: true,
        columnData,
        allowSorting: false,
        allowSelection: true,
        selectionSettings: {
          type: 'Multiple',
          mode: 'Row'
        },
        asyncConfig: { url, params }
      }
    }
  ]
}

export const materialColumns = [
  {
    title: i18n.t('物料编码'),
    dataIndex: 'itemCode',
    key: 'itemCode',
    width: 200,
    align: 'left'
  },
  {
    title: i18n.t('物料名称'),
    dataIndex: 'itemName',
    key: 'itemName',
    width: 150,
    align: 'left'
  }
]

export const categoryColumns = [
  {
    title: i18n.t('品类编码'),
    dataIndex: 'categoryCode',
    key: 'categoryCode',
    width: 200,
    align: 'left'
  },
  {
    title: i18n.t('品类名称'),
    dataIndex: 'categoryName',
    key: 'categoryName',
    width: 150,
    align: 'left'
  }
]

export const statusOptions = utils.getQuotaDict('QUOTA_ITEM_SUBMIT_STATUS') || []

export const vxeColumns = [
  {
    title: i18n.t('单据状态'),
    field: 'status',
    align: 'left',
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  },
  {
    title: i18n.t('配额调整申请单号'),
    field: 'requestNumber',
    width: 150,
    align: 'left'
  },
  {
    title: i18n.t('配额调整申请标题'),
    field: 'title',
    width: 150,
    align: 'left'
  },
  {
    title: i18n.t('工厂代码'),
    field: 'siteCode',
    width: 150,
    align: 'left'
  },
  {
    title: i18n.t('工厂名称'),
    field: 'siteName',
    width: 150,
    align: 'left'
  },
  {
    title: i18n.t('供应商编码'),
    field: 'supplierCode',
    width: 200,
    align: 'left'
  },
  {
    title: i18n.t('供应商名称'),
    field: 'supplierName',
    width: 150,
    align: 'left'
  },
  {
    title: i18n.t('物料编码'),
    field: 'itemCode',
    width: 200,
    align: 'left'
  },
  {
    title: i18n.t('物料名称'),
    field: 'itemName',
    width: 150,
    align: 'left'
  },
  {
    title: i18n.t('调整数量'),
    field: 'quantity',
    width: 150,
    align: 'left'
  },
  {
    title: i18n.t('品类'),
    field: 'categoryName',
    width: 200,
    align: 'left'
  },
  {
    title: i18n.t('调整原因'),
    field: 'reason',
    width: 200,
    align: 'left'
  },
  {
    title: i18n.t('建议对策'),
    field: 'measure',
    width: 200,
    align: 'left'
  },
  {
    title: i18n.t('调整配额'),
    field: 'adjust',
    width: 150,
    align: 'left',
    formatter: ({ cellValue }) => {
      let str = ''
      if (cellValue === 1) {
        str = i18n.t('否')
      }
      if (cellValue === 2) {
        str = i18n.t('是')
      }
      return str
    }
  },
  {
    title: i18n.t('开始日期'),
    field: 'startMonthTime',
    width: 150,
    align: 'left',
    formatter: ({ cellValue }) => {
      let str = ''
      if (cellValue && cellValue > 0 && new Date(Number(cellValue))) {
        str = utils.formatTime(new Date(Number(cellValue)), 'YYYY-mm-dd')
      }
      return str
    }
  },
  {
    title: i18n.t('结束日期'),
    field: 'endMonthTime',
    width: 150,
    align: 'left',
    formatter: ({ cellValue }) => {
      let str = ''
      if (cellValue && cellValue > 0 && new Date(Number(cellValue))) {
        str = utils.formatTime(new Date(Number(cellValue)), 'YYYY-mm-dd')
      }
      return str
    }
  },
  { title: i18n.t('创建人'), field: 'createUserName', align: 'left' },
  {
    title: i18n.t('创建时间'),
    field: 'createDate',
    align: 'left',
    formatter: ({ cellValue }) => {
      let str = ''
      if (cellValue && new Date(cellValue)) {
        str = utils.formatTime(new Date(cellValue), 'YYYY-mm-dd HH:MM:SS')
      }
      return str
    }
  }
]
