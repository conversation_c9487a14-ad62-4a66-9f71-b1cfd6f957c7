import { i18n } from '@/main.js'
import utils from '@/utils/utils'
export const Toolbar = [
  { code: 'Confirm', status: 'info', name: i18n.t('确认') },
  { code: 'Cancel', status: 'info', name: i18n.t('取消') },
  { code: 'SubmitConfirm', status: 'info', name: i18n.t('提交确认') },
  { code: 'import', status: 'info', name: i18n.t('调整导入') },
  { code: 'Export', status: 'info', name: i18n.t('导出') },
  { code: 'Jump', status: 'info', name: i18n.t('未自动筛选清单') }
]
const toolbar = [
  // { id: "Add", icon: "icon_solid_Createorder", title: i18n.t("新增") },
  // { id: "Stop", icon: "icon_solid_Cancel", title: i18n.t("取消") },
  // { id: "Submit", icon: "icon_solid_Submit", title: i18n.t("提交") },
  // { id: "Import", icon: "icon_solid_Createorder", title: i18n.t("导入") },
]
const materialColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    cssClass: 'field-content',
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  }
]
const categoryColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    cssClass: 'field-content'
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]
const supplierColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    cssClass: 'field-content'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  }
]
export const pageConfig = (url, columnType, params) => {
  let columnData = []
  let gridId = {
    material: 'dc44358d-94f1-4083-9a3d-3d3ad04cb756',
    category: '5eba6472-f20a-4d11-b4ff-f6e2c2ffb97c',
    supplier: '5ebc1488-d472-454c-b391-93afc11b534f'
  }
  if (columnType === 'material') {
    columnData = materialColumn
  } else if (columnType === 'category') {
    columnData = categoryColumn
  } else {
    columnData = supplierColumn
  }
  let asyncConfig = {
    url
  }
  if (params) {
    asyncConfig.params = params
  }
  return [
    {
      toolbar,
      useToolTemplate: false,
      gridId: gridId[columnType],
      grid: {
        allowFiltering: true,
        columnData,
        allowSorting: false,
        allowSelection: true,
        selectionSettings: {
          type: 'Multiple',
          mode: 'Row'
        },
        asyncConfig
      }
    }
  ]
}

export const statusOptions = [
  { text: i18n.t('未确认'), code: 0 },
  { text: i18n.t('已确认'), code: 1 },
  { text: i18n.t('已提交'), code: 2 }
]

export const vxeColumns = [
  {
    type: 'checkbox',
    minWidth: 50
    // fixed: 'left'
  },
  {
    title: i18n.t('工厂代码'),
    field: 'siteCode',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('工厂名称'),
    field: 'siteName',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('确认状态'),
    field: 'status',
    minWidth: 150,
    align: 'left',
    formatter: ({ cellValue }) => {
      const commitStatusOptions = utils.getQuotaDict('QUOTA_ITEM_COMMIT_STATUS') || []
      let item = commitStatusOptions.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
    // slots: { header: 'statusHead' }
  },
  {
    title: i18n.t('月份'),
    field: 'month',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('采购组'),
    field: 'purGroupName',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('采购开发'),
    field: 'buyerUserName',
    minWidth: 200,
    align: 'left'
  },
  {
    title: i18n.t('品类'),
    field: 'categoryName',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('物料编码'),
    field: 'itemCode',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('物料名称'),
    field: 'itemName',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('供应商编码'),
    field: 'supplierCode',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('供应商名称'),
    field: 'supplierName',
    minWidth: 200,
    align: 'left'
  },
  {
    title: '协议配额%',
    field: 'agreementQuotaPercent',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('限制类型'),
    field: 'constraintType',
    minWidth: 150,
    align: 'left',
    formatter: ({ cellValue }) => {
      const commitStatusOptions = utils.getQuotaDict('QUOTA_STRATEGY_XZPEYXJ_TYPE') || []
      let item = commitStatusOptions.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  },
  {
    title: i18n.t('限制等级'),
    field: 'constraintLevel',
    minWidth: 150,
    align: 'left'
  },
  {
    title: '限制配额%',
    field: 'constraintQuotaPercent',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('价格'),
    field: 'price',
    minWidth: 150,
    align: 'left'
  },
  {
    title: '价差%',
    field: 'priceDifferencePercent',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('综合排名'),
    field: 'ranking',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('价格排名'),
    field: 'priceRanking',
    minWidth: 150,
    align: 'left'
  },
  {
    title: i18n.t('绩效排名'),
    field: 'analysisRanking',
    minWidth: 150,
    align: 'left'
  },
  {
    title: '初分配配额%',
    field: 'initQuotaPercent',
    minWidth: 150,
    align: 'left'
  },
  {
    title: '限制配额执行%',
    field: 'constraintQuotaExecutePercent',
    minWidth: 150,
    align: 'left'
  },
  {
    title: '建议配额%',
    field: 'systemQuotaPercent',
    minWidth: 150,
    align: 'left'
  },
  {
    // title: '制定配额%',
    field: 'quotaPercent',
    minWidth: 150,
    align: 'left',
    slots: { header: 'quotaPercentHead', default: 'quotaPercent' }
  },
  {
    title: i18n.t('制定与建议差异值'),
    field: 'deviationQuotaPercent',
    minWidth: 150,
    align: 'left'
  },
  {
    // title: i18n.t('最小起拆量'),
    field: 'minOpenQuantity',
    minWidth: 150,
    align: 'left',
    slots: { header: 'minOpenQuantityHead', default: 'minOpenQuantity' }
  },
  {
    // title: i18n.t('最小包装量'),
    field: 'minPackageQuantity',
    minWidth: 150,
    align: 'left',
    slots: { header: 'minPackageQuantityHead', default: 'minPackageQuantity' }
  },
  {
    title: i18n.t('最小采购量'),
    field: 'minPurchaseQuantity',
    minWidth: 150,
    align: 'left',
    slots: { default: 'minPurchaseQuantity' }
  },
  {
    title: i18n.t('调整类型'),
    field: 'adjustType',
    align: 'left',
    minWidth: 150,
    slots: { default: 'adjustType' }
  },
  {
    title: i18n.t('调整原因'),
    field: 'adjustReason',
    minWidth: 150,
    align: 'left',
    slots: { default: 'adjustReason' }
  },
  {
    title: i18n.t('是否自制配额'),
    field: 'customizationFlag',
    minWidth: 150,
    align: 'left',
    formatter: ({ cellValue }) => {
      const commitStatusOptions = [
        { text: i18n.t('是'), code: 'Y' },
        { text: i18n.t('否'), code: 'N' }
      ]
      let item = commitStatusOptions.find((item) => item.code === cellValue)
      return item ? item.text : ''
    }
  },
  {
    title: i18n.t('放行供应商类型'),
    field: 'excludeSupplierType',
    minWidth: 150,
    align: 'left',
    formatter: ({ cellValue }) => {
      const commitStatusOptions = utils.getQuotaDict('QUOTA_STRATEGY_XZPEYXJ_TYPE') || []
      let item = commitStatusOptions.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    }
  },
  {
    title: i18n.t('异常信息'),
    field: 'errorMessage',
    minWidth: 150,
    align: 'left'
  }
]
