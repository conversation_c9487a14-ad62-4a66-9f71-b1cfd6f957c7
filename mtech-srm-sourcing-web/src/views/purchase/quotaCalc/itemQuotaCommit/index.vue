<!--
 * @Author: wenjie20.wang <EMAIL>
 * @Date: 2022-09-21 16:43:23
 * @LastEditors: wenjie20.wang <EMAIL>
 * @LastEditTime: 2022-10-01 09:44:39
 * @FilePath: \mtech-srm-sourcing-web\src\views\purchase\quotaCalc\itemQuotaCommit\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="item-quota-commit">
    <collapse-search :is-grid-display="true" @reset="reset" @search="handleSearch">
      <mt-form ref="ruleForm" :model="queryForm">
        <mt-form-item prop="siteCode" :label="$t('工厂代码')" label-style="top">
          <mt-select
            v-model="queryForm.siteCode"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="factoryList"
            :fields="{ text: 'orgCode', value: 'orgCode' }"
            :placeholder="$t('请选择工厂代码')"
            @change="handleFactoryChange($event)"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商')" label-style="top">
          <magnifier-input
            ref="supplierCode"
            :config="supplierDialogCofig"
            @change="supplierCodeChange($event)"
          ></magnifier-input>
        </mt-form-item>
        <mt-form-item prop="categoryCode" :label="$t('品类')" label-style="top">
          <magnifier-input
            ref="categoryCode"
            :config="categoryDialogCofig"
            @change="categoryCodeChange($event)"
          ></magnifier-input>
        </mt-form-item>

        <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
          <magnifier-input
            ref="itemCode"
            :config="materialDialogCofig"
            @change="materialCodeChange($event)"
          ></magnifier-input>
        </mt-form-item>
        <mt-form-item prop="month" :label="$t('月份')" label-style="top">
          <mt-date-picker
            v-model="queryForm.month"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            placeholder="$t('请选择月份')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('确认状态')" label-style="top">
          <mt-select
            v-model="queryForm.status"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="commitStatusOptions"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('确认状态')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="buyerUserName" :label="$t('采购开发')" label-style="top">
          <mt-input v-model="queryForm.buyerUserName" :placeholder="$t('请输入采购开发')" />
        </mt-form-item>
        <mt-form-item prop="purGroupName" :label="$t('采购组')" label-style="top">
          <mt-input v-model="queryForm.purGroupName" :placeholder="$t('请输入采购组')" />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- <div class="button-group">
      <icon-button icon="icon_solid_Submit" :text="$t('确认')" @click="handleClick('Confirm')" />
      <icon-button icon="icon_solid_Cancel" :text="$t('取消')" @click="handleClick('Cancel')" />
      <icon-button
        icon="icon_solid_Submit"
        :text="$t('提交确认')"
        @click="handleClick('SubmitConfirm')"
      />
      <icon-button icon="icon_solid_Import" :text="$t('调整导入')" @click="handleClick('Import')" />
      <icon-button icon="icon_solid_export" :text="$t('导出')" @click="handleClick('Export')" />
      <icon-button
        icon="icon_solid_Createproject"
        :text="$t('未自动筛选清单')"
        @click="handleJump()"
      />
    </div> -->
    <!-- 表格 -->
    <sc-table
      ref="xTable"
      grid-id="a6cc25b9-1184-4d27-8f3f-dd996f1a3166"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="vxeColumns"
      :table-data="dataSource"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
          >{{ item.name }}</vxe-button
        >
      </template>
      <template #quotaPercentHead="{}"
        ><span class="red-color">*</span>{{ $t('制定配额%') }}</template
      >
      <template #quotaPercent="{ row }">
        <vxe-input
          v-if="row.editable"
          v-model="row.quotaPercent"
          type="number"
          :min="0"
          :max="100"
          :precision="0"
          @change="quotaPercentChange(row)"
        />
        <span v-else>{{ row.quotaPercent }}</span></template
      >
      <template #minOpenQuantityHead="{}"
        ><span class="red-color">*</span>{{ $t('最小起拆量') }}</template
      >
      <template #minOpenQuantity="{ row }">
        <vxe-input
          v-if="row.editable"
          style="width: 100%"
          v-model="row.minOpenQuantity"
          size="small"
          type="number"
          :min="0"
          :precision="0"
        />
        <span v-else>{{ row.minOpenQuantity }}</span></template
      >
      <template #minPackageQuantityHead="{}"
        ><span class="red-color">*</span>{{ $t('最小包装量') }}</template
      >
      <template #minPackageQuantity="{ row }">
        <vxe-input
          v-if="row.editable"
          style="width: 100%"
          v-model="row.minPackageQuantity"
          size="small"
          type="number"
          :min="0"
          :precision="0"
        />
        <span v-else>{{ row.minPackageQuantity }}</span></template
      >
      <template #minPurchaseQuantity="{ row }">
        <vxe-input
          v-if="row.editable"
          style="width: 100%"
          v-model="row.minPurchaseQuantity"
          size="small"
          type="number"
          :min="0"
          :precision="0"
        />
        <span v-else>{{ row.minPurchaseQuantity }}</span></template
      >
      <template #adjustType="{ row }">
        <vxe-select
          v-model="row.adjustType"
          :options="adjustTypeList"
          :option-props="{
            label: 'dictName',
            value: 'dictCode'
          }"
          transfer
          filterable
        ></vxe-select
      ></template>

      <template #adjustReason="{ row }">
        <vxe-input
          v-if="row.editable"
          style="width: 100%"
          v-model="row.adjustReason"
          size="small"
        />
        <span v-else>{{ row.adjustReason }}</span></template
      >
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
// import moment from 'moment'
import utils from '@/utils/utils'
import { getHeadersFileName, download } from '@/utils/utils'
import collapseSearch from '@/components/collapseSearch'
import magnifierInput from '@/components/magnifierInput'
import { pageConfig, vxeColumns, statusOptions, Toolbar } from './config/index.js'
import ScTable from '@/components/ScTable/src/index'
import dayjs from 'dayjs'
import cloneDeep from 'lodash/cloneDeep'
// import * as util from '@mtech-common/utils'
export default {
  components: {
    collapseSearch,
    magnifierInput,
    ScTable
  },
  data() {
    return {
      loading: false,
      toolbar: Toolbar,
      queryForm: {
        siteCode: '', // 组织
        status: '', // 状态
        month: this.getNextMonth(),
        purGroupName: '', // 采购组
        buyerUserName: '', // 开发组
        categoryCode: '', // 品类
        itemCode: '', // 物料编码
        supplierCode: ''
      },
      pageSettings: {
        pageCount: 5,
        pageSize: 20, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [10, 20, 50, 100]
      },
      factoryList: [], // 工厂列表
      selectedRows: [], // 表格勾选行的内容
      customizationFlagList: utils.getQuotaDict('QUOTA_ITEM_CUSTOMIZATION_FLAG') || [],
      adjustTypeList: utils.getQuotaDict('QUOTA_ITEM_ADJUST_TYPE') || [],
      vxeColumns,
      dataSource: [],
      statusOptions,
      commitStatusOptions: utils.getQuotaDict('QUOTA_ITEM_COMMIT_STATUS') || [],
      // 物料放大镜弹窗配置
      materialDialogCofig: {
        pageConfig: pageConfig(
          `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
          'material'
        ),
        text: 'itemCode',
        value: 'itemCode'
      },
      // 品类放大镜弹窗配置
      categoryDialogCofig: {
        pageConfig: pageConfig(
          '/masterDataManagement/tenant/permission/queryCategories',
          'category'
        ),
        text: 'categoryName',
        value: 'categoryCode'
      },
      // 供应商放大镜弹窗配置
      supplierDialogCofig: {
        pageConfig: pageConfig(
          '/masterDataManagement/tenant/supplier/pagedQueryByOrgCode',
          'supplier'
        ),
        text: 'supplierName',
        value: 'supplierCode'
      }
    }
  },
  created() {
    this.getFactoryList()
    this.search()
  },
  methods: {
    quotaPercentChange(row) {
      row.deviationQuotaPercent = Math.abs(row.systemQuotaPercent - row.quotaPercent)
    },
    getNextMonth() {
      let nowMonthDate = new Date()
      let getMonth = nowMonthDate.getMonth() + 1
      nowMonthDate.setMonth(getMonth, 1)
      nowMonthDate.setDate(1) //set设置时间
      nowMonthDate.setHours(0)
      nowMonthDate.setSeconds(0)
      nowMonthDate.setMinutes(0)
      return nowMonthDate
    },
    // moment,
    // 处理月份格式化
    getMonth(obj) {
      const params = cloneDeep(obj)
      if (params.month) {
        params.month = dayjs(params.month).format('YYYY-MM')
      }
      return params
    },
    // 获取用户信息
    getUserInfoDetail() {
      this.$API.iamService.getUserDetail().then((res) => {
        console.log(res.data)
      })
    },
    getFormatTime(date, format) {
      if (date && new Date(date)) {
        return utils.formatTime(new Date(date), format)
      }
      return ''
    },
    getFactoryList() {
      this.$API.customization.getPermissionSiteList({}).then((res) => {
        this.factoryList = res.data
      })
    },
    // 确认
    handleConfirm() {
      let params = {}
      if (this.selectedRows.length > 0) {
        // 表格勾选
        for (let i = 0; i < this.selectedRows.length; i++) {
          const element = this.selectedRows[i]
          if (element.status !== 0) {
            this.$toast({
              content: this.$t('勾选数据状态不为“未确认”'),
              type: 'warning'
            })
            return
          }
          // 制定配额、最小起拆量、最小采购量、最小包装量不能为空，否则报错提示“XXX不能为空”
          if (!element.quotaPercent && element.quotaPercent !== 0) {
            this.$toast({
              content: this.$t('制定配额不能为空'),
              type: 'warning'
            })
            return
          }
          if (!element.minOpenQuantity && element.minOpenQuantity !== 0) {
            this.$toast({
              content: this.$t('最小起拆量不能为空'),
              type: 'warning'
            })
            return
          }
          // if (!element.minPurchaseQuantity && element.minPurchaseQuantity !== 0) {
          //   this.$toast({
          //     content: this.$t('最小采购量不能为空'),
          //     type: 'warning'
          //   })
          //   return
          // }
          if (!element.minPackageQuantity && element.minPackageQuantity !== 0) {
            this.$toast({
              content: this.$t('最小包装量不能为空'),
              type: 'warning'
            })
            return
          }
          // 当建议配额不等于 制定配额时，则校验调整原因及调整类型不能为空，为空则报错“调整配额需填写调整原因及调整类型”
          if (
            element.deviationQuotaPercent >= 10 &&
            (!element.adjustType || !element.adjustReason)
          ) {
            this.$toast({
              content: this.$t('调整配额需填写调整原因及调整类型'),
              type: 'warning'
            })
            return
          }
        }
        params.selectedList = this.selectedRows
      } else {
        params = this.getMonth(this.queryForm)
        if (!params.month) {
          this.$toast({
            content: this.$t('未勾选数据，默认确认筛选条件对应的数据，请选择月份再确认'),
            type: 'warning'
          })
          return
        }
      }
      this.$API.itemQuotaCommit.batchConfirmItemQuotaCommit(params).then((res) => {
        const { code } = res
        if (code === 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.search()
        }
      })
    },
    // 取消
    handleCancel(param) {
      const params = param
      if (this.selectedRows.length > 0) {
        if (this.selectedRows.some((i) => i.status !== 1)) {
          this.$toast({
            content: this.$t('勾选数据状态不为“已确认”不可进行取消'),
            type: 'warning'
          })
          return
        }
        params.idList = this.selectedRows.map((item) => item.id)
      }
      this.$API.itemQuotaCommit.batchCancelItemQuotaCommit(params).then((res) => {
        const { code } = res
        if (code === 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.search()
        }
      })
    },
    // 提交确认
    handleSubmitConfirm(param) {
      const params = { month: param.month }
      this.$API.itemQuotaCommit.batchSubimtItemQuotaCommit(params).then((res) => {
        const { code } = res
        if (code === 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.search()
        }
      })
    },
    // 导入
    handleImport(params) {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('上传/导入'),
          importApi: this.$API.itemQuotaCommit.excelimport,
          asyncParams: {
            month: params.month
          },
          downloadTemplateApi: this.$API.itemQuotaCommit.excelExport,
          downloadTemplateParams: {
            ...this.getMonth(this.queryForm)
          }
        },
        success: () => {
          // 导入之后刷新列表
          this.search()
        }
      })
    },
    // 导出
    handleExport() {
      //调用接口
      this.$API.itemQuotaCommit.excelExport(this.getMonth(this.queryForm)).then((res) => {
        const fileName = getHeadersFileName(res)
        //下载文件
        download({
          fileName,
          blob: res.data
        })
      })
    },
    // 按钮操作
    handleClickToolBar(args) {
      const { code, $grid } = args

      this.selectedRows = $grid.getCheckboxRecords() // 表格勾选行的内容
      if (code === 'Confirm') {
        this.handleConfirm()
      } else if (code === 'Cancel') {
        this.handleCancel(this.getMonth(this.queryForm))
      } else if (code === 'SubmitConfirm') {
        this.handleSubmitConfirm(this.getMonth(this.queryForm))
      } else if (code === 'Import') {
        this.handleImport(this.getMonth(this.queryForm))
      } else if (code === 'Export') {
        this.handleExport()
      } else if (code === 'Jump') {
        this.handleJump()
      }
    },
    // 跳转
    handleJump() {
      this.$router.push({
        path: `item-scope-init`,
        query: { status: 0 }
      })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageSettings.current = currentPage
      this.search('pageSettings')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.search()
    },
    // 重置
    reset() {
      console.log('重置')
      this.queryForm = {
        siteCode: '', // 组织
        categoryCode: '', // 品类
        status: '', // 状态
        itemCode: '', // 物料编码
        supplierCode: '',
        startDate: '', // 开始月份
        endDate: '', // 结束月份
        currentPage: 1,
        pageSize: 10
      }
      this.$refs.itemCode.handleClear()
      this.$refs.supplierCode.handleClear()
      this.$refs.categoryCode.handleClear()
      this.search()
    },
    handleSearch() {
      this.search()
    },
    // 查询的方法
    search(pageSettings) {
      console.log('search', this.queryForm)
      const params = {
        ...this.queryForm,
        page: {
          current: pageSettings ? this.pageSettings.current : 1,
          size: this.pageSettings.pageSize
        }
      }

      if (params.month) {
        params.month = dayjs(params.month).format('YYYY-MM')
      }
      this.loading = true
      this.$API.itemQuotaCommit
        .getItemQuotaCommitList(params)
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            const total = res?.data?.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)
            // 表格内组件编辑状态常驻
            this.dataSource = data.records.map((item) => {
              item.editable = true
              this.commitStatusOptions.forEach((i) => {
                if (i.code === item.status) {
                  item.statusName = i.text
                  return
                }
              })
              return item
            })
            console.log(this.dataSource, '处理好的数据')
            this.isEdit = false
          }
          this.selectedRows = []
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleFactoryChange(e) {
      // 工厂下拉变更
      const { itemData } = e
      this.queryForm.siteCode = itemData.orgCode
    },
    // 物料编码变更
    materialCodeChange(e) {
      // 表单内物料编码变更
      this.queryForm.itemCode = e.itemCode
    },
    // 品类变更
    categoryCodeChange(e) {
      this.queryForm.categoryCode = e.categoryCode
    },
    // 供应商编码变更
    supplierCodeChange(e) {
      // 表单内物料编码变更
      this.queryForm.supplierCode = e.supplierCode
    }
  }

  //
}
</script>

<style lang="scss" scoped>
.item-quota-commit {
  // margin-top: 24px;
  background: #fff;
  padding: 12px;
  border-radius: 8px;
  height: 100%;
  ::v-deep.red-color {
    color: red;
  }
}
</style>
