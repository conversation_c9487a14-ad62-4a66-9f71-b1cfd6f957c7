import { i18n } from '@/main.js'
import utils from '@/utils/utils'
const toolbar = [
  // { id: "Add", icon: "icon_solid_Createorder", title: i18n.t("新增") },
  // { id: "Stop", icon: "icon_solid_Cancel", title: i18n.t("取消") },
  // { id: "Submit", icon: "icon_solid_Submit", title: i18n.t("提交") },
  // { id: "Import", icon: "icon_solid_Createorder", title: i18n.t("导入") },
]
const materialColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    cssClass: 'field-content',
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  }
]
const categoryColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    cssClass: 'field-content'
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]
const supplierColumn = [
  // {
  //   type: 'checkbox',
  //   minWidth: '60'
  // },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    cssClass: 'field-content'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  }
]
export const pageConfig = (url, columnType, params) => {
  let columnData = []
  let gridId = {
    material: '2b75ac3e-0497-465f-992e-a4882f6872fd',
    category: '32c0c624-67b0-49ce-b078-3e46e27fa9d3',
    supplier: '8c9ac6ae-69e9-4da0-803a-822eb67a3cca'
  }
  if (columnType === 'material') {
    columnData = materialColumn
  } else if (columnType === 'category') {
    columnData = categoryColumn
  } else {
    columnData = supplierColumn
  }
  return [
    {
      toolbar,
      useToolTemplate: false,
      gridId: gridId[columnType],
      grid: {
        allowFiltering: true,
        columnData,
        allowSorting: false,
        allowSelection: true,
        selectionSettings: {
          type: 'Multiple',
          mode: 'Row'
        },
        asyncConfig: { url, params }
      }
    }
  ]
}

export const statusOptions = utils.getQuotaDict('QUOTA_BIAS_STATUS') || []

export const biasTypeOptions = utils.getQuotaDict('QUOTA_BIAS_TYPE') || []

export const vxeColumns = (pageType) => {
  return [
    {
      type: 'checkbox',
      minWidth: 50
      // fixed: 'left'
    },
    {
      title: i18n.t('跟踪单号'),
      field: 'code',
      minWidth: 160,
      align: 'left'
    },
    {
      title: i18n.t('偏差月份'),
      field: 'month',
      minWidth: 120,
      align: 'left',
      formatter: ({ cellValue }) => {
        let date = ''
        if (cellValue && new Date(cellValue)) {
          date = utils.formatTime(new Date(cellValue), 'YYYY-mm')
        }
        return date
      }
    },
    {
      title: i18n.t('工厂代码'),
      field: 'siteCode',
      minWidth: 100,
      align: 'left'
    },
    {
      title: i18n.t('工厂名称'),
      field: 'siteName',
      minWidth: 150,
      align: 'left'
    },
    {
      title: i18n.t('品类'),
      field: 'categoryName',
      minWidth: 100,
      align: 'left'
    },
    {
      title: i18n.t('供应商编码'),
      minWidth: 110,
      field: 'supplierCode',
      align: 'left'
    },
    {
      title: i18n.t('供应商名称'),
      field: 'supplierName',
      minWidth: 150,
      align: 'left'
    },
    {
      title: i18n.t('物料编码'),
      minWidth: 120,
      field: 'itemCode',
      align: 'left'
    },
    {
      title: i18n.t('物料名称'),
      field: 'itemName',
      minWidth: 150,
      align: 'left'
    },
    {
      title: i18n.t('采购组'),
      minWidth: 150,
      field: 'purGroupName',
      align: 'left'
    },
    {
      title: i18n.t('物控'),
      minWidth: 80,
      field: 'itemControlName',
      align: 'left'
    },
    {
      title: i18n.t('采购开发'),
      minWidth: 100,
      field: 'buyerUserName',
      align: 'left'
    },
    {
      title: pageType === 'summary' ? i18n.t('汇总数量') : i18n.t('下单总量'),
      minWidth: 100,
      field: 'quantityTotal',
      align: 'left'
    },
    {
      title: pageType === 'summary' ? i18n.t('数量') : i18n.t('实际下单'),
      minWidth: 100,
      field: 'quantity',
      align: 'left'
    },
    {
      title: '制定配额%',
      field: 'quotaPercent',
      minWidth: 100,
      align: 'left',
      formatter: ({ cellValue }) => {
        let quotaPercent = 0
        if (cellValue) {
          quotaPercent = parseInt(cellValue * 100)
        }
        return quotaPercent
      }
    },
    {
      title: '执行配额%',
      field: 'executeQuotaPercent',
      minWidth: 100,
      align: 'left',
      formatter: ({ cellValue }) => {
        let executeQuotaPercent = 0
        if (cellValue) {
          executeQuotaPercent = parseInt(cellValue * 100)
        }
        return executeQuotaPercent
      }
    },
    {
      title: '配额偏差%',
      field: 'biasQuotaPercent',
      minWidth: 100,
      align: 'left',
      formatter: ({ cellValue }) => {
        let biasQuotaPercent = 0
        if (cellValue) {
          biasQuotaPercent = parseInt(cellValue * 100)
        }
        return biasQuotaPercent
      }
    },
    {
      title: i18n.t('最小包装量'),
      field: 'minPackageQuantity',
      align: 'left',
      minWidth: 100
    },
    {
      title: i18n.t('最小起拆量'),
      field: 'minOpenQuantity',
      align: 'left',
      minWidth: 100
    },
    {
      title: i18n.t('状态'),
      field: 'status',
      minWidth: 80,
      align: 'left',
      formatter: ({ cellValue }) => {
        let item = statusOptions.find((item) => item.dictCode === cellValue)
        return item ? item.dictName : ''
      }
    },
    {
      title: i18n.t('发布人'), // 配额执行偏差清单没有
      field: 'publishUserName',
      minWidth: 100,
      align: 'left'
    },
    {
      title: i18n.t('发布时间'),
      field: 'publishTime',
      minWidth: 180,
      align: 'left'
    },
    {
      title: i18n.t('物控反馈偏差类型'), // 【配额执行偏差反馈】页面可操作；状态需为“反馈中”、“反馈已驳回”且 当前账号为 对应 物控可操作，可选择类型取数字字典【XXX】
      field: 'biasType',
      minWidth: 150,
      align: 'left',
      slots: {
        // 使用插槽模板渲染
        header: 'biasTypeHeader',
        default: 'biasTypeDefault'
      }
    },
    {
      title: i18n.t('物控反馈偏差原因'), // 【配额执行偏差反馈】页面可操作；状态需为“反馈中”、“反馈已驳回”且 当前账号为 对应 物控可操作，可选择类型取数字字典【XXX】
      field: 'remark',
      minWidth: 150,
      align: 'left',
      slots: {
        // 使用插槽模板渲染
        header: 'remarkHeader',
        default: 'remarkDefault'
      }
    },
    {
      title: i18n.t('物控提交日期'),
      field: 'biasCommitTime',
      minWidth: 150,
      align: 'left'
    },
    {
      title: i18n.t('采购开发审核意见'),
      field: 'reviewComment',
      minWidth: 150,
      align: 'left'
    },
    {
      title: i18n.t('采购开发审核时间'),
      field: 'reviewTime',
      minWidth: 150,
      align: 'left'
    },
    {
      title: i18n.t('创建人'),
      field: 'createUserName',
      minWidth: 150,
      align: 'left'
    },
    {
      title: i18n.t('创建时间'),
      field: 'createTime',
      minWidth: 180,
      align: 'left'
    }
  ]
}
