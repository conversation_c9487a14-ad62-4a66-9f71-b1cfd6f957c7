<template>
  <div>
    <!--- tabs --->
    <div class="list-item-tabs">
      <div
        v-for="(item, index) in pageList"
        :key="item.pageName"
        :class="['list-item-tabs__tab', currentTabIndex === index ? 'active' : '']"
        @click="clickTab(index)"
      >
        <p>{{ item.pageName }}</p>
        <!-- 注释右上角圆标 -->
        <!-- <span>{{ item.count }}</span> -->
      </div>
      <span class="tabs-bottom" :style="getStyle()" />
    </div>
    <quota-bias-list v-if="currentTabIndex === 0" />
  </div>
</template>

<script>
import quotaBiasList from './../index.vue'
export default {
  components: {
    quotaBiasList
  },
  data() {
    return {
      pageList: [{ pageName: this.$t('配额执行偏差明细') }],
      currentTabIndex: 0
    }
  },
  methods: {
    getStyle() {
      if (this.currentTabIndex !== 0) {
        return 'transform: translateX(146px); width: 128px'
      }
      return 'transform: translateX(0px); width: 128px'
    },
    clickTab(index) {
      if (this.currentTabIndex !== index) {
        // 点击tab按钮更新tab当前下标及数据
        this.currentTabIndex = index
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.list-item-tabs {
  border-bottom: 1px dashed #ececec;
  display: flex;
  &__tab {
    margin: 0 10px;
    padding: 18px 0;
    box-sizing: border-box;
    cursor: pointer;
    &:first-child {
      margin-left: 0;
    }
    display: flex;
    align-items: center;
    p {
      margin-bottom: unset;
      color: #999;
      font-size: 16px;
      white-space: nowrap;
    }
    span {
      font-size: 12px;
      transform: scale(0.83);
      color: #fff;
      display: block;
      width: 19.2px;
      height: 19.2px;
      background: #f55448;
      border: 1px solid #ffffff;
      line-height: 19.2px;
      text-align: center;
      border-radius: 50%;
      margin: -8px 0 0 2px;
    }
    &.active {
      // padding: 10px 0 8px;
      // border-bottom: 2px solid red;
      p {
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }
  position: relative;
  .tabs-bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    transition: all 0.3s linear;
    width: 33px;
    height: 2px;
    background: #f55448;
    border-radius: 1px;
  }
}
</style>
