<template>
  <div class="quato-customization">
    <collapse-search :is-grid-display="true" @reset="reset" @search="search">
      <mt-form ref="ruleForm" :model="queryForm">
        <mt-form-item prop="code" :label="$t('跟踪单号')" label-style="top">
          <mt-input v-model="queryForm.code" :placeholder="$t('请输入跟踪单号')" />
        </mt-form-item>
        <mt-form-item prop="month" :label="$t('偏差月份')" label-style="top">
          <mt-date-picker
            v-model="queryForm.month"
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :separator="$t('至')"
            :placeholder="$t('选择偏差月份')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="siteCode" :label="$t('工厂代码')" label-style="top">
          <mt-select
            v-model="queryForm.siteCode"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="factoryList"
            :fields="{ text: 'orgCode', value: 'orgCode' }"
            :placeholder="$t('请选择工厂代码')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="supplierId" :label="$t('供应商名称')" label-style="top">
          <magnifier-input
            ref="supplierId"
            :config="supplierDialogCofig"
            @change="supplierCodeChange($event)"
          ></magnifier-input>
        </mt-form-item>
        <mt-form-item prop="categoryId" :label="$t('品类')" label-style="top">
          <magnifier-input
            ref="categoryId"
            :config="categoryDialogCofig"
            @change="categoryCodeChange($event)"
          ></magnifier-input>
        </mt-form-item>
        <!-- 配额执行偏差汇总才有物料查询 -->
        <mt-form-item
          v-if="pageType === 'summary'"
          prop="itemId"
          :label="$t('物料编码')"
          label-style="top"
        >
          <magnifier-input
            ref="itemId"
            :config="materialDialogCofig"
            @change="materialCodeChange($event)"
          ></magnifier-input>
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-select
            v-model="queryForm.status"
            float-label-type="Never"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="true"
            :data-source="statusOptions"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('状态')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="buyerUserName" :label="$t('采购开发')" label-style="top">
          <mt-input v-model="queryForm.buyerUserName" :placeholder="$t('请输入采购开发')" />
        </mt-form-item>
        <mt-form-item prop="purGroupName" :label="$t('采购组')" label-style="top">
          <mt-input v-model="queryForm.purGroupName" :placeholder="$t('请输入采购组')" />
        </mt-form-item>
        <mt-form-item prop="itemControlName" :label="$t('物控')" label-style="top">
          <mt-input v-model="queryForm.itemControlName" :placeholder="$t('请输入物控')" />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!----  表格  --------->
    <sc-table
      ref="xTable"
      grid-id="b3607455-9706-43dd-9499-05e006d6cfd0"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="vxeColumns"
      :table-data="dataSource"
      @refresh="search"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in buttonList"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
          >{{ item.name }}</vxe-button
        >
      </template>
      <template #biasTypeHeader="{}">
        <div>
          <em style="color: red">*</em> <span>{{ $t('物控反馈偏差类型') }}</span>
        </div></template
      >
      <template #biasTypeDefault="{ row }">
        <vxe-select
          v-if="(row.status === 1 || row.status === 4) && pageType === 'feedback'"
          v-model="row.biasType"
          :options="biasTypeOptions"
          :option-props="{
            label: 'dictName',
            value: 'dictCode'
          }"
          transfer
          filterable
        ></vxe-select>
        <span v-else>
          {{ getBiasTypeLabel(row.biasType) }}
        </span>
      </template>
      <template #remarkHeader="{}">
        <div>
          <em style="color: red">*</em> <span>{{ $t('物控反馈偏差原因') }}</span>
        </div></template
      >
      <template #remarkDefault="{ row }">
        <vxe-input
          v-if="(row.status === 1 || row.status === 4) && pageType === 'feedback'"
          v-model="row.remark"
        />
        <span v-else>
          {{ row.remark }}
        </span>
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import utils from '@/utils/utils'
import { download, getHeadersFileName, formatTime } from '@/utils/utils'
import { throttle } from 'lodash'
import collapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import magnifierInput from '@/components/magnifierInput'
import { pageConfig, vxeColumns, statusOptions, biasTypeOptions } from './config'
export default {
  components: {
    collapseSearch,
    magnifierInput,
    ScTable
  },
  data() {
    return {
      loading: false,
      biasTypeOptions,
      queryForm: {
        code: '', // 跟踪单号
        month: this.getPrevMonth(), // 偏差月份
        siteCode: '', // 工厂
        supplierId: '',
        categoryId: '', // 品类
        itemId: '', // 物料id
        status: '', // 状态
        itemControlName: '', // 物控专员
        buyerUserName: '', // 采购开发
        purGroupName: '' // 采购组
      },
      pageSettings: {
        pageCount: 5,
        pageSize: 20, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [10, 20, 50, 100]
      },
      selectedRowKeys: [], // 表格勾选行的id
      selectedRows: [], // 表格勾选行的内容
      statusOptions,
      dataSource: [],
      factoryList: [],
      // 物料放大镜弹窗配置
      materialDialogCofig: {
        pageConfig: pageConfig(
          `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
          'material'
        ),
        text: 'itemCode',
        value: 'itemCode'
      },
      // 品类放大镜弹窗配置
      categoryDialogCofig: {
        pageConfig: pageConfig(
          '/masterDataManagement/tenant/permission/queryCategories',
          'category'
        ),
        text: 'categoryName',
        value: 'categoryCode'
      },
      // 供应商放大镜弹窗配置
      supplierDialogCofig: {
        pageConfig: pageConfig(
          '/masterDataManagement/tenant/supplier/pagedQueryByOrgCode',
          'supplier'
        ),
        text: 'supplierName',
        value: 'supplierCode'
      },
      pageType: 'summary' // 当前页面类型 汇总: summary  反馈: feedback  审核: examine
    }
  },
  created() {
    this.getPageType()
    this.getFactoryList()
    this.search()
  },
  methods: {
    getBiasTypeLabel(biasType) {
      let label = ''
      if (biasType) {
        const item = biasTypeOptions.find((item) => item.dictCode === biasType)
        label = item.dictName
      }
      return label
    },
    isEqual(key) {
      if (
        key === 'siteCode' ||
        key === 'categoryId' ||
        key === 'supplierId' ||
        key === 'itemId' ||
        key === 'status'
      ) {
        return true
      }
      return false
    },
    getParams(pageSettings) {
      const params = {
        // ...this.queryForm,
        page: {
          current: pageSettings ? this.pageSettings.current : 1,
          size: this.pageSettings.pageSize
        }
      }
      const rules = []
      const keys = Object.keys(this.queryForm)
      const values = Object.values(this.queryForm)
      for (let i = 0; i < values.length; i++) {
        const element = values[i]
        if ((element || element === 0) && keys[i] !== 'month') {
          let obj = {
            field: keys[i],
            label: '',
            operator: this.isEqual(keys[i]) ? 'equal' : 'contains',
            type: 'string',
            value: element
          }
          rules.push(obj)
        }
      }
      if (this.queryForm.month) {
        let element = utils.formatTime(new Date(this.queryForm.month), 'YYYY-mm-01')
        let obj = {
          field: 'month',
          label: '',
          operator: 'equal',
          type: 'string',
          value: element
        }
        rules.push(obj)
      }
      if (rules.length) {
        params.condition = 'and'
        params.rules = rules
      }
      return params
    },
    getPrevMonth() {
      let nowMonthDate = new Date()
      let getMonth = nowMonthDate.getMonth() + 1
      nowMonthDate.setMonth(getMonth - 2, 1)
      nowMonthDate.setDate(1) //set设置时间
      nowMonthDate.setHours(0)
      nowMonthDate.setSeconds(0)
      nowMonthDate.setMinutes(0)
      return nowMonthDate
    },
    getFactoryList() {
      this.$API.customization.getPermissionSiteList({}).then((res) => {
        this.factoryList = res.data
      })
    },
    handleClickToolBar: throttle(function (args) {
      const { code, $grid } = args
      const selectedRows = $grid.getCheckboxRecords() // 表格勾选行的内容
      if (
        (code === 'Adopt' || code === 'Reject' || code === 'Recall' || code === 'Submit') &&
        selectedRows.length <= 0
      ) {
        this.$toast({
          content: this.$t('请勾选需要操作的数据'),
          type: 'warning'
        })
        return
      }
      if (code === 'Publish') {
        // 发布
        this.handlePublish(selectedRows)
      } else if (code === 'Import') {
        // 调整导入 反馈导入
        this.handleImport()
      } else if (code === 'Recall') {
        // 撤回
        this.handleRecall(selectedRows)
      } else if (code === 'Submit') {
        // 确认提交
        this.handleSubmit(selectedRows)
      } else if (code === 'Adopt') {
        // 通过
        this.handleApproval(code, selectedRows)
      } else if (code === 'Reject') {
        // 驳回
        this.handleApproval(code, selectedRows)
      } else if (code === 'Export') {
        // 导出
        this.handleExport()
      }
    }, 200),
    // 发布
    handlePublish(selectedRows) {
      if (selectedRows.length > 0) {
        const ids = []
        // 表格勾选传idList
        for (let i = 0; i < selectedRows.length; i++) {
          const item = selectedRows[i]
          const beforeYear = new Date(item.month).getFullYear()
          const currentYear = new Date().getFullYear()
          const beforeMonth = new Date(item.month).getMonth()
          const currentMonth = new Date().getMonth()
          if (
            beforeYear > currentYear ||
            (beforeYear === currentYear && beforeMonth >= currentMonth)
          ) {
            // if (new Date(item.month).getMonth() >= new Date().getMonth()) {
            this.$toast({
              content: this.$t('仅能发布过去月份偏差数据'),
              type: 'warning'
            })
            return
          }
          if (!item.itemControlName) {
            this.$toast({
              content: this.$t('存在无对应物控人员的数据，请补充完整'),
              type: 'warning'
            })
            return
          }
          if (item.status !== 0) {
            this.$toast({
              content: this.$t('请勾选状态为“未发布”偏差数据才能发布'),
              type: 'warning'
            })
            return
          }
          ids.push(item.id)
        }
        this.publishFn({ ids })
      } else {
        // 不勾选传筛选条件
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t(`未勾选数据，默认发布筛选条件对应的偏差数据`)
          },
          success: () => {
            if (!this.queryForm.month) {
              this.$toast({
                content: this.$t('请选择偏差月份'),
                type: 'warning'
              })
              return
            }
            const beforeYear = new Date(this.queryForm.month).getFullYear()
            const currentYear = new Date().getFullYear()
            const beforeMonth = new Date(this.queryForm.month).getMonth()
            const currentMonth = new Date().getMonth()
            if (
              beforeYear > currentYear ||
              (beforeYear === currentYear && beforeMonth >= currentMonth)
            ) {
              // if (new Date(this.queryForm.month).getMonth() >= new Date().getMonth()) {
              this.$toast({
                content: this.$t('仅能发布过去月份偏差数据'),
                type: 'warning'
              })
              return
            }
            if (!this.queryForm.itemControlName) {
              this.$toast({
                content: this.$t('查询条件物控不能为空，请补充完整'),
                type: 'warning'
              })
              return
            }
            this.publishFn({
              queryBuilderDTO: this.getParams()
            })
          }
        })
      }
    },
    publishFn(params) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`是否发布当前数据？`)
        },
        success: () => {
          this.$API.quotaBias.publishQuotaBias(params).then((res) => {
            const { code } = res
            if (code === 200) {
              this.$toast({
                content: res.message ? res.message : this.$t('发布成功'),
                type: 'success'
              })
              this.search()
            }
          })
        }
      })
    },
    // 导入
    handleImport() {
      let data = {
        title: this.$t('调整导入'),
        importApi: this.$API.quotaBias.importQuotaBias,
        downloadTemplateApi: this.$API.quotaBias.getQuotaBiasTemplate,
        downloadTemplateParams: this.getParams()
      }
      if (this.pageType === 'feedback') {
        data = {
          title: this.$t('反馈导入'),
          importApi: this.$API.quotaBias.importQuotaBiasFeedback,
          downloadTemplateApi: this.$API.quotaBias.getQuotaBiasFeedbackTemplate,
          downloadTemplateParams: this.getParams()
        }
      }
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data,
        success: () => {
          // 导入之后刷新列表
          this.search()
        }
      })
    },
    // 撤回
    handleRecall(selectedRows) {
      // 勾选的数据状态需为“反馈中”
      const ids = []
      for (let i = 0; i < selectedRows.length; i++) {
        let item = selectedRows[i]
        if (item.status != 1) {
          this.$toast({
            content: this.$t('勾选的数据存在状态不为反馈中，无法撤回'),
            type: 'warning'
          })
          return false
        }
        ids.push(item.id)
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`是否撤回当前数据？`)
        },
        success: () => {
          this.$API.quotaBias.recallQuotaBias({ ids }).then((res) => {
            const { code } = res
            if (code === 200) {
              this.$toast({
                content: res.message ? res.message : this.$t('撤回成功'),
                type: 'success'
              })
              this.search()
            }
          })
        }
      })
    },
    // 提交
    handleSubmit(selectedRows) {
      if (selectedRows.length > 0) {
        const ids = []
        // 表格勾选传idList
        for (let i = 0; i < selectedRows.length; i++) {
          const item = selectedRows[i]
          if (item.status !== 1 && item.status !== 4) {
            this.$toast({
              content: this.$t('所选数据状态非反馈中且非已驳回'),
              type: 'warning'
            })
            return
          }
          if (!item.biasType && item.biasType !== 0) {
            this.$toast({
              content: this.$t('所选数据物控反馈偏差类型不可为空'),
              type: 'warning'
            })
            return
          }
          if (!item.remark) {
            this.$toast({
              content: this.$t('所选数据物控反馈偏差原因不可为空'),
              type: 'warning'
            })
            return
          }
          ids.push(item)
        }
        this.submitFn({ commitReqs: ids })
      } else {
        // 不勾选传筛选条件
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t(`未勾选数据，默认提交筛选条件对应的偏差数据`)
          },
          success: () => {
            if (!this.queryForm.month) {
              this.$toast({
                content: this.$t('请选择偏差月份'),
                type: 'warning'
              })
              return
            }
            this.submitFn({
              month: formatTime(new Date(this.queryForm.month), 'YYYY-mm-dd'),
              itemControlName: this.queryForm.itemControlName
            })
          }
        })
      }
    },
    submitFn(params) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`是否确认提交当前数据？`)
        },
        success: () => {
          this.$API.quotaBias.commitQuotaBias(params).then((res) => {
            const { code } = res
            if (code === 200) {
              this.$toast({
                content: res.message ? res.message : this.$t('提交成功'),
                type: 'success'
              })
              this.search()
            }
          })
        }
      })
    },
    // 通过、驳回事件
    handleApproval(id, selectedRows) {
      if (selectedRows.some((i) => i.status !== 2)) {
        this.$toast({
          content: this.$t('不在审核中的数据不能进行审核'),
          type: 'warning'
        })
        return
      }
      let data = {}
      if (id === 'Adopt') {
        data = {
          title: this.$t('审核通过'),
          labelName: this.$t('审核说明'),
          required: false,
          inputContent: this.$t('通过')
        }
      } else {
        data = {
          title: this.$t('审核驳回'),
          labelName: this.$t('审核说明')
        }
      }
      this.$dialog({
        modal: () => import('@/components/inputDialog/index.vue'),
        data,
        success: (e) => {
          console.log('e.inputContent', e.inputContent)
          // e.inputContent是失效弹窗中输入的值
          const reviewReqs = selectedRows.map((i) => {
            return {
              id: i.id,
              reviewComment: e.inputContent
            }
          })
          const params = {
            // status: 3,
            // remark: e.inputContent
            reviewAction: id === 'Adopt' ? 1 : 2,
            reviewReqs
          }
          this.$API.quotaBias.approvalQuotaBias(params).then((res) => {
            const { code } = res
            if (code === 200) {
              this.$toast({
                content: res.message ? res.message : this.$t('审核成功'),
                type: 'success'
              })
              this.search()
            }
          })
        }
      })
    },
    getExportApi() {
      let api = this.$API.quotaBias.exportQuotaBias
      if (this.pageType === 'feedback') {
        api = this.$API.quotaBias.exportQuotaBiasFeedback
      }
      if (this.pageType === 'examine') {
        api = this.$API.quotaBias.exportQuotaBiasReview
      }
      return api
    },
    // 导出
    handleExport() {
      const params = this.getParams()
      // if (this.queryForm.month) {
      //   params.month = formatTime(new Date(this.queryForm.month), 'YYYY-mm-dd')
      // }
      this.getExportApi()(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageSettings.current = currentPage
      this.search('pageSettings')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.search()
    },
    reset() {
      console.log('重置')
      this.queryForm = {
        code: '', // 跟踪单号
        month: '', // 偏差月份
        siteCode: '', // 工厂
        supplierId: '',
        categoryId: '', // 品类
        itemId: '', // 物料id
        status: '', // 状态
        itemControlName: '', // 物控专员
        buyerUserName: '', // 采购开发
        purGroupName: '' // 采购组
      }
      if (this.pageType === 'summary') {
        this.$refs.itemId.handleClear()
      }
      this.$refs.supplierId.handleClear()
      this.$refs.categoryId.handleClear()
      this.search()
    },
    getQueryApi() {
      let api = this.$API.quotaBias.getQuotaBiasList
      if (this.pageType === 'feedback') {
        api = this.$API.quotaBias.getQuotaBiasFeedBackList
      }
      if (this.pageType === 'examine') {
        api = this.$API.quotaBias.getQuotaBiasReviewList
      }
      return api
    },
    search(pageSettings) {
      console.log('search', this.queryForm)
      const params = this.getParams(pageSettings)
      // if (this.queryForm.month) {
      //   params.month = formatTime(new Date(this.queryForm.month), 'YYYY-mm-dd')
      // }
      this.loading = true
      this.getQueryApi()(params)
        .then((res) => {
          // this.$API.customization.getCustomizationList(params).then((res) => {
          const { code, data } = res
          if (code === 200) {
            const total = res?.data?.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)
            this.dataSource = data.records
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    materialCodeChange(e) {
      // 物料编码变更
      this.queryForm.itemId = e.id
    },
    categoryCodeChange(e) {
      // 品类变更
      this.queryForm.categoryId = e.id
    },
    supplierCodeChange(e) {
      this.queryForm.supplierId = e.id
    },
    getPageType() {
      // 三个页面共用一个文件通过路由名区分
      if (this.$route.name === 'quota-bias-list') {
        this.pageType = 'summary'
      } else if (this.$route.name === 'quota-bias-feedback') {
        this.pageType = 'feedback'
      } else if (this.$route.name === 'quota-bias-examine') {
        this.pageType = 'examine'
      }
    }
  },
  computed: {
    vxeColumns() {
      return vxeColumns(this.pageType)
    },
    buttonList() {
      let buttonList = []
      if (this.pageType === 'summary') {
        buttonList = [
          { icon: 'icon_solid_upload', status: 'info', name: this.$t('发布'), code: 'Publish' },
          { icon: 'icon_solid_upload', status: 'info', name: this.$t('调整导入'), code: 'Import' },
          { icon: 'icon_solid_export', status: 'info', name: this.$t('导出'), code: 'Export' },
          { icon: 'icon_solid_export', status: 'info', name: this.$t('撤回'), code: 'Recall' }
        ]
      }
      if (this.pageType === 'feedback') {
        buttonList = [
          { icon: 'icon_solid_upload', status: 'info', name: this.$t('反馈导入'), code: 'Import' },
          { icon: 'icon_solid_upload', status: 'info', name: this.$t('确认提交'), code: 'Submit' },
          { icon: 'icon_solid_export', status: 'info', name: this.$t('导出'), code: 'Export' }
        ]
      }
      if (this.pageType === 'examine') {
        buttonList = [
          { icon: 'icon_solid_upload', status: 'info', name: this.$t('通过'), code: 'Adopt' },
          { icon: 'icon_solid_upload', status: 'info', name: this.$t('驳回'), code: 'Reject' },
          { icon: 'icon_solid_export', status: 'info', name: this.$t('导出'), code: 'Export' }
        ]
      }
      return buttonList
    }
  },
  activated() {
    this.getPageType()
  }
}
</script>

<style lang="scss" scoped>
.quato-customization {
  // margin-top: 24px;
  background: #fff;
  padding: 12px;
  border-radius: 8px;
  height: 100%;
  .button-group {
    display: flex;
    padding: 0 0 8px;
  }
}
.components-table-demo-nested {
  ::v-deep.red-color {
    color: red;
  }
}
</style>
