<template>
  <div class="supplier-box mt-flex">
    <div class="miam-container">
      <div class="operate-bar">
        <div v-if="type != 'detail'" class="op-item mt-flex" @click="saveDetail(false)">
          {{ $t('保存') }}
        </div>
        <div
          v-if="type != 'detail'"
          class="op-item mt-flex"
          :class="{ grayBtn: submitLoading }"
          @click="saveDetail(true)"
        >
          {{ $t('提交') }}
        </div>
        <div class="op-item mt-flex" @click="backToBusinessConfig">
          {{ $t('返回') }}
        </div>
      </div>
      <!-- 顶部主要信息 -->
      <div class="mian-info">
        <mt-form ref="ruleForm" :model="formObject" :rules="formRules">
          <mt-form-item :label="$t('配额申请单号')">
            <mt-input
              width="100%"
              :disabled="true"
              type="text"
              v-model="formObject.quotaCode"
              :placeholder="$t('请输入配额申请单号')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="quotaName" :label="$t('配额申请标题')">
            <mt-input
              width="100%"
              :disabled="type == 'detail'"
              type="text"
              v-model="formObject.quotaName"
              :placeholder="$t('请输入配额申请标题')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item :label="$t('状态')" prop="status">
            <mt-select
              :disabled="true"
              :fields="{ text: 'label', value: 'status' }"
              v-model="formObject.status"
              float-label-type="Never"
              :allow-filtering="true"
              :data-source="statusList"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="extendFactoryList" :label="$t('工厂扩展')">
            <mt-multi-select
              v-model="formObject.extendFactoryList"
              float-label-type="Never"
              :allow-filtering="true"
              filter-type="Contains"
              :show-clear-button="true"
              :data-source="factoryList"
              :fields="{ text: 'label', value: 'siteCode' }"
              :placeholder="$t('请选择工厂扩展')"
            ></mt-multi-select>
          </mt-form-item>
          <mt-form-item :label="$t('配额是否传SAP')">
            <mt-select
              v-model="formObject.zeroQuotaToSap"
              float-label-type="Never"
              :data-source="isNotifySapMap"
            ></mt-select>
          </mt-form-item>
          <mt-form-item :label="$t('创建人：')">
            <mt-input
              width="100%"
              type="text"
              :disabled="true"
              v-model="formObject.createUserName"
              :max-length="200"
            ></mt-input>
          </mt-form-item>
          <mt-form-item :label="$t('创建时间：')">
            <mt-input
              width="100%"
              type="text"
              :disabled="true"
              v-model="formObject.createTime"
              :max-length="200"
            ></mt-input>
          </mt-form-item>
          <mt-form-item :label="$t('备注：')">
            <mt-input
              width="100%"
              type="text"
              :disabled="type == 'detail'"
              v-model="formObject.remark"
              :placeholder="$t('字数不超过200字')"
              :max-length="200"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
      <div class="relation-ships">
        <p>{{ $t('额度分配') }}</p>
        <!----  表格  --------->
        <sc-table
          ref="xTable"
          :columns="vxeColumns"
          :table-data="formObject.quotaItemList"
          grid-id="73f15c44-a47b-47ec-9958-e4455d8e6115"
          :edit-config="{
            trigger: 'dblclick',
            mode: 'row',
            showStatus: true,
            autoClear: false
          }"
          :edit-rules="editRules"
          class="components-table-demo-nested"
          @edit-actived="editAction"
        >
          <template slot="custom-tools">
            <vxe-button
              v-for="item in toolbar"
              :key="item.code"
              :status="item.status"
              :icon="item.icon"
              :disabled="type == 'detail'"
              size="small"
              @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
              >{{ item.name }}</vxe-button
            >
          </template>
          <template #siteCodeHead="{}"> <span class="red-color">*</span>工厂代码 </template>
          <template #siteCodeEdit="{ row }">
            <div style="width: 100%">
              <vxe-select
                v-model="row.siteCode"
                :options="factoryList"
                :option-props="{
                  label: 'label',
                  value: 'dimensionCodeValue'
                }"
                @change="handleFactoryChange($event.value, row)"
                transfer
                filterable
              ></vxe-select>
            </div>
          </template>
          <template #itemCodeHead="{}"> <span class="red-color">*</span>物料编码 </template>
          <template #itemCodeEdit="{ row }">
            <div style="width: 100%">
              <magnifier-input
                :disabled="!row.siteCode"
                :default-value="row.itemCode"
                :config="materialTableDialogCofig(row)"
                @change="materialCodeChange($event, row)"
              ></magnifier-input>
            </div>
          </template>
          <template #purTypeHead="{}"> <span class="red-color">*</span>采购类型 </template>
          <template #purTypeEdit="{ row }">
            <div style="width: 100%" v-if="!isReload">
              <vxe-select
                v-model="row.purType"
                :options="purTypeList"
                :disabled="row.isNotKT"
                :option-props="{
                  label: 'label',
                  value: 'dictCode'
                }"
                @change="handlePurchaseTypeChange($event.value, row)"
                transfer
                filterable
              ></vxe-select>
            </div>
          </template>
          <template #quoteAttributeEdit="{ row }">
            <div style="width: 100%" v-if="!isReload">
              <vxe-select
                v-model="row.quoteAttribute"
                :options="quoteAttributeList"
                :disabled="row.isNotKT"
                :option-props="{
                  label: 'label',
                  value: 'dictCode'
                }"
                @change="handleQuoteAttributeChange($event.value, row)"
                transfer
                filterable
              ></vxe-select>
            </div>
          </template>
          <template #supplierCodeEdit="{ row }">
            <magnifier-input
              v-if="!isReload"
              :default-value="row.supplierCode"
              :disabled="!row.siteCode || row.quoteAttribute === 'E'"
              :config="supplierDialogCofig(row.siteCode)"
              @change="supplierCodeChange($event, row)"
            ></magnifier-input>
          </template>
          <template #purSiteCodeEdit="{ row }">
            <div style="width: 100%" v-if="!isReload">
              <vxe-select
                v-model="row.purSiteCode"
                :options="purFactoryList"
                :disabled="row.quoteAttribute === 'E' || row.quoteAttribute !== 'U'"
                :option-props="{
                  label: 'label',
                  value: 'dimensionCodeValue'
                }"
                @change="handleQuoteAttributeChange($event.value, row)"
                transfer
                filterable
              ></vxe-select>
            </div>
          </template>
          <template #validStartTimeEdit="{ row }">
            <div style="width: 100%">
              <vxe-input
                v-model="row.validStartTime"
                type="date"
                :disabled-method="
                  ({ date }) =>
                    date.getTime() < new Date(moment(new Date()).add('day', -1)).getTime()
                "
                transfer
              ></vxe-input>
            </div>
          </template>
          <template #validEndTimeEdit="{ row }">
            <div style="width: 100%">
              <vxe-input
                v-model="row.validEndTime"
                type="date"
                :disabled="row.isNotKT"
                transfer
              ></vxe-input>
            </div>
          </template>
        </sc-table>
        <!-- 分页 -->
        <mt-page
          ref="pageRef"
          class="flex-keep custom-page"
          :page-settings="pageSettings"
          :total-pages="pageSettings.totalPages"
          @currentChange="handleCurrentChange"
          @sizeChange="handleSizeChange"
        />
      </div>
    </div>
    <!-- 上传弹窗 -->
    <UploadExcelDialog
      ref="uploadInitBalanceRef"
      file-key="file"
      :down-template-name="$t('模板')"
      :request-urls="requestUrls"
      :upload-params="uploadParams"
      :down-template-params="{
        page: {
          current: 1,
          size: 10
        },
        rules: []
      }"
      @closeUploadExcel="uploadClose"
      @upExcelConfirm="uploadConfirm"
    />
  </div>
</template>

<script>
import moment from 'moment'
import ScTable from '@/components/ScTable/src/index'
import { vxeColumns, pageConfig } from './config'
import magnifierInput from '@/components/magnifierInput'
import Decimal from 'decimal.js'
import lodash from 'lodash'
import { throttle } from 'lodash'
import dayjs from 'dayjs'
import { i18n } from '@/main.js'
import utils from '@/utils/utils'
export default {
  components: {
    magnifierInput,
    ScTable,
    UploadExcelDialog: () => import('@/components/Upload/uploadExcelDialog.vue')
  },
  data() {
    return {
      editRules: {
        siteCode: [{ required: true, message: this.$t('请选择工厂') }],
        itemCode: [{ required: true, message: this.$t('请选择物料编码') }],
        purType: [{ required: true, message: this.$t('请选择采购类型') }],
        supplierCode: [{ required: true, message: this.$t('请选择供应商') }],
        validStartTime: [{ required: true, message: this.$t('请选择生效日期') }],
        validEndTime: [{ required: true, message: this.$t('请选择失效日期') }],
        minOpenQuantity: [{ required: true, message: this.$t('请输入最小起拆量') }],
        minPackageQuantity: [{ required: true, message: this.$t('请输入最小包装量') }],
        quotaPercent: [{ required: true, message: this.$t('请输入配额比') }]
      },
      toolbar: [
        {
          code: 'Add',
          name: this.$t('新增'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Import',
          name: this.$t('导入'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Export',
          name: this.$t('导出'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'Delete',
          name: this.$t('删除'),
          icon: 'el-icon-delete',
          status: 'info'
        },
        {
          code: 'AddHistory',
          name: this.$t('参考历史配额'),
          icon: 'el-icon-delete',
          status: 'info'
        }
      ],
      submitLoading: false, //提交按钮loading
      factoryList: [], //工厂列表
      purFactoryList: [], // 采购工厂列表
      requestUrls: {
        //导入上传地址
        templateUrlPre: 'quotaConfig',
        templateUrl: 'importTemplate',
        uploadUrl: 'applyItemImport'
      },
      uploadParams: {
        //导入上传参数
        quotaCode: ''
      },
      editRow: {}, //编辑行
      isEdit: false, //编辑状态
      statusList: [
        { status: 0, label: i18n.t('草稿'), cssClass: 'title-#9baac1' },
        { status: 1, label: i18n.t('审批中'), cssClass: 'title-#eda133' },
        { status: 2, label: i18n.t('驳回'), cssClass: 'title-#ed5633' },
        { status: 3, label: i18n.t('通过'), cssClass: 'title-#6386c1' },
        { status: 4, label: i18n.t('取消'), cssClass: 'title-#9a9a9a' }
      ], //状态列表
      isNotifySapMap: [
        { value: 1, text: i18n.t('是') },
        { value: 0, text: i18n.t('否') }
      ],
      purTypeList: utils
        .getQuotaDict('PROCUREMENT TYPE')
        .map((i) => ({ ...i, label: `${i.dictCode} - ${i.dictName}` })),
      quoteAttributeList: utils
        .getQuotaDict('SPECIAL PUR TYPE')
        .map((i) => ({ ...i, label: `${i.dictCode} - ${i.dictName}` })),
      quoteAttributeShortList: [
        {
          dictName: this.$t('寄售'),
          dictCode: 'K'
        },
        {
          dictName: this.$t('分包'),
          dictCode: 'L'
        },
        {
          dictName: this.$t('库存转储'),
          dictCode: 'U'
        }
      ],
      factorySiteList: [], //工厂列表
      formObject: {
        status: null,
        zeroQuotaToSap: 1,
        // 配额编码
        quotaCode: null,
        // 配额名称
        quotaName: null,
        // 备注
        remark: null,
        createTime: null,
        createUserName: null,
        // 配额明细列表
        quotaItemList: [],
        extendFactoryList: []
      },
      type: 'add',
      formRules: {
        quotaName: [
          {
            required: true,
            message: this.$t('请输入配额标题'),
            trigger: 'blur'
          }
        ]
      },
      vxeColumns: [], //table列配置
      dataSource: [], //table数据
      pageSettings: {
        pageCount: 5,
        pageSize: 20, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [10, 20, 50, 100]
      },
      isReload: false,
      detailId: ''
    }
  },
  async mounted() {
    this.submitLoading = false
    this.init()
    this.vxeColumns = vxeColumns(this)
    this.type = this.$route.query.type
    if (this.type != 'add') {
      this.getDetail()
    } else {
      // this.$set(this.pageConfig[0].grid, 'dataSource', this.formObject.quotaItemList)
    }
  },
  methods: {
    moment,
    getDictName(list, code) {
      let name = ''
      for (let i = 0; i < list.length; i++) {
        if (list[i]['dictCode'] === code) {
          name = list[i]['dictName']
        }
      }
      return name
    },
    //根据工厂+物料+供应商获取最新的包装量,起拆量，采购量，历史价格，历史配额
    getLastQuotaItem(row) {
      //新增的数据才需要获取最新的包装量,起拆量，采购量-----后来编辑时也要根据供应商的重新选择做出表更
      if (!row.siteCode || !row.itemCode || !row.supplierCode) {
        return
      }
      let params = {
        // siteCode: '2000',
        // itemCode: '2C105-000001',
        // supplierCode: '100358'
        siteCode: row.siteCode,
        itemCode: row.itemCode,
        supplierCode: row.supplierCode
      }

      this.$API.quotaConfig.getLastQuotaItem(params).then((res) => {
        if (res.code == 200 && res.data) {
          let data = res.data
          //如果为空直接赋值 ，有值时不赋值
          if (!row.minOpenQuantity) {
            row.minOpenQuantity = data.minOpenQuantity //最小起拆量
          }
          if (!row.minPackageQuantity) {
            row.minPackageQuantity = data.minPackageQuantity //最小包装量
          }
          if (!row.minPurchaseQuantity) {
            row.minPurchaseQuantity = data.minPurchaseQuantity //最小采购量
          }
          row.historyPrice = data.historyPrice // 历史价格
          row.historyQuotaPercent = this.transData(data.historyQuotaPercent) // 历史配额
        }
      })
    },
    //上传弹窗 关闭
    uploadClose() {
      this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.hide()
    },
    //上传弹窗 确定
    uploadConfirm() {
      this.uploadClose()
      //刷新列表
      this.getDetail()
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRows = $grid.getCheckboxRecords() // 表格勾选行的内容
      if (code === 'Add') {
        this.handleAdd()
      }
      if (code === 'Import') {
        this.handleImport()
      }
      if (code === 'Export') {
        this.handleExport()
      }
      if (code === 'Delete') {
        this.handleDelete(selectedRows)
      }
      if (code === 'AddHistory') {
        this.handleAddHistory(selectedRows)
      }
    },
    //新增
    handleAdd() {
      //添加一行 开启编辑状态 生效日期默认当天 失效日期默认 9999
      // let validStartTime = dayjs().format('YYYY-MM-DD') + ' 00:00:00'
      let validStartTime = dayjs().format('YYYY-MM-DD')
      let addForm = {
        rowId: '', //行索引id 作为前端删除新增行的标识 不传后台
        purType: 'F', // 采购类型
        purTypeName: 'F', // 采购类型
        itemCode: '', // 物料编码
        itemId: '', // 物料ID
        itemName: '', // 物料名称
        minOpenQuantity: '', // 最小起拆量
        minPackageQuantity: '', // 最小包装量
        minPurchaseQuantity: '', // 最小采购量
        quotaPercent: '', // 配额值
        remark: '', // 备注
        siteCode: '', // 工厂 库存组织编码
        siteId: '', // 工厂 库存组织ID
        siteName: '', // 工厂 库存组织名称
        supplierCode: '', // 供应商编码
        supplierId: '', // 供应商ID
        supplierName: '', // 供应商名称
        syncInfo: '', // 同步信息
        syncStatus: '', // 同步状态
        validEndTime: '', // 失效日期
        validStartTime, // 生效日期
        historyPrice: '',
        historyQuotaPercent: '',
        editable: true,
        rowType: 'add'
      }
      //rowid 随机uuid
      addForm.rowId = lodash.uniqueId('row_')
      if (!addForm.siteCode && this.factoryList && this.factoryList.length === 1) {
        addForm.siteCode = this.factoryList[0]['dimensionCodeValue']
        addForm.siteId = this.factoryList[0]['dimensionIdValue']
        addForm.siteName = this.factoryList[0]['dimensionNameValue']
        this.checkEndTime(addForm)
      }
      //更新视图
      this.$set(this.formObject, 'quotaItemList', [addForm, ...this.formObject.quotaItemList])
    },
    //导入
    handleImport() {
      //校验quotaCode 是否存在 上传必须传quotaCode
      if (!this.formObject.quotaCode) {
        this.$message.warning(this.$t('请先保存'))
        return
      }

      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('上传/导入'),
          importApi: this.$API.quotaConfig.excelimport2,
          asyncParams: {
            quotaCode: this.formObject.quotaCode
          },
          downloadTemplateApi: this.$API.quotaConfig.exportTpl2
        },
        success: () => {
          //刷新列表
          this.getDetail()
        }
      })
    },
    //导出
    handleExport() {
      this.$API.quotaApplyRecord
        .getQuotaCodeList({ quotaCode: this.formObject.quotaCode })
        .then((res) => {
          let fileName = utils.getHeadersFileName(res)
          utils.download({ fileName: fileName, blob: res.data })
        })
    },
    //删除
    handleDelete(selectedRows) {
      //获取当前选中行
      if (selectedRows.length === 0) {
        this.$toast({ content: this.$t('请选择要删除的行'), type: 'warning' })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const deleteList = []
          // 遍历已勾选的数据 有id的就是线上数据需要调删除接口
          const arr = this.formObject.quotaItemList.filter((i) => {
            return !selectedRows.some((j) => {
              if (j && j.id && !j.id.includes('row_')) {
                deleteList.push(j.id)
              }
              return j.rowId === i.rowId
            })
          })
          if (deleteList.length) {
            const params = Array.from(new Set(deleteList)) // 去个重
            this.$API.quotaConfig.deleteQuotaDetail(params).then((res) => {
              const { code } = res
              if (code === 200) {
                this.afterDeleteEvent(arr)
              }
            })
            return
          }
          // 删除后刷新列表
          this.afterDeleteEvent(arr)
        }
      })
    },
    afterDeleteEvent(arr) {
      this.$toast({
        content: this.$t('删除成功'),
        type: 'success'
      })
      if (arr.some((i) => !i.id || i.id.includes('row_'))) {
        this.formObject.quotaItemList = arr
      } else {
        this.formObject.quotaItemList = arr
        this.search()
      }
    },
    handlePurchaseTypeChange(e, row) {
      row.purType = e
      this.isReload = !this.isReload
      if (row.purType === 'E') {
        row.quoteAttribute = 'E'
        const fieldsarr = [
          'supplierCode',
          'supplierName',
          'supplierId',
          'purSiteCode',
          'purSiteName',
          'purSiteId'
        ]
        this.setTableEmpty(fieldsarr, row)
      } else {
        const fieldsarr = ['quoteAttribute', 'supplierCode', 'supplierName', 'supplierId']
        this.setTableEmpty(fieldsarr, row)
      }
      this.$nextTick(() => {
        this.isReload = !this.isReload
      })
    },
    handleQuoteAttributeChange(e, row) {
      this.isReload = !this.isReload
      row.quoteAttribute = e
      if (row.quoteAttribute === 'E') {
        row.purType = 'E'
        const fieldsarr = ['supplierCode', 'supplierName', 'supplierId']
        this.setTableEmpty(fieldsarr, row)
      } else {
        row.purType = 'F'
      }
      if (row.quoteAttribute !== 'U') {
        const fieldsarr = ['purSiteCode', 'purSiteName', 'purSiteId']
        this.setTableEmpty(fieldsarr, row)
      }
      this.$nextTick(() => {
        this.isReload = !this.isReload
      })
    },
    handlePurFactoryChange(e, row) {
      row.purSiteCode = e
      for (let i = 0; i < this.purFactoryList.length; i++) {
        const element = this.purFactoryList[i]
        if (element.dimensionCodeValue === e) {
          row.purSiteId = element.dimensionIdValue
          row.purSiteName = element.dimensionNameValue
          //失效日期判断
          // this.checkEndTime(row)
          return
        }
      }
      if (!row.purSiteCode) {
        row.purSiteCode = ''
        row.purSiteId = ''
        row.purSiteName = ''
      }
    },
    //工厂change
    handleFactoryChange(e, row) {
      // 工厂下拉变更
      if (row) {
        // 表格内的下拉选择
        // 重新选择时，需清空行上已填写的字段（供应商、物料、品类、）
        const fieldsarr = [
          'supplierCode',
          'supplierName',
          'supplierId',
          'itemId',
          'itemCode',
          'itemName',
          'categoryId',
          'categoryCode',
          'categoryName'
        ]
        row.siteCode = e
        console.log(e)
        this.setTableEmpty(fieldsarr, row)
        for (let i = 0; i < this.factoryList.length; i++) {
          const element = this.factoryList[i]
          if (element.dimensionCodeValue === e) {
            // row.siteCode = element.dimensionCodeValue
            row.siteId = element.dimensionIdValue
            row.siteName = element.dimensionNameValue
            //失效日期判断
            this.checkEndTime(row)
            return
          }
        }
      }
    },
    editAction(args) {
      const { row } = args
      this.isEdit = true
      // 双击进入编辑状态，判断isNotKT、siteCode是否有值，再调接口判断当前工厂是否属于空调事业部，进而判断采购类型与特殊采购是否禁用
      if (row.isNotKT !== true && row.isNotKT !== false && row.siteCode) {
        let params = {
          siteCode: row.siteCode
        }
        this.$API.quotaConfig.judgeIsKT(params).then((res) => {
          if (res.code == 200) {
            this.isReload = !this.isReload
            this.$set(row, 'isNotKT', false)
            if (!res.data) {
              this.$set(row, 'purType', 'F')
              if (row.quoteAttribute === 'E') {
                this.$set(row, 'quoteAttribute', '')
              }
              this.$set(row, 'isNotKT', true)
            }
            this.$nextTick(() => {
              this.isReload = !this.isReload
            })
          }
        })
      }
    },
    //判断工厂是否属于空调事业部 是 按照失效日期处理为 当前年+1年  的最后一天；其他事业部 失效日期保持9999-12-31
    checkEndTime(row) {
      let params = {
        siteCode: row.siteCode
      }
      this.$API.quotaConfig.judgeIsKT(params).then((res) => {
        if (res.code == 200) {
          let validEndTime = '9999-12-31 00:00:00' //非空调事业部的失效日期
          let validEndTime2 =
            dayjs().add(1, 'year').endOf('year').format('YYYY-MM-DD') + ' 00:00:00' //空调事业部的失效日期
          //空调事业部 如果原来的日期是9999-12-31 00:00:00 则更新为当前年+1年  的最后一天
          if (res.data && (row.validEndTime == validEndTime || !row.validEndTime)) {
            this.$set(row, 'validEndTime', validEndTime2)
          }
          //非空调事业部 如果原来的日期是当前年+1年  的最后一天 则更新为9999-12-31 00:00:00
          this.isReload = !this.isReload
          this.$set(row, 'isNotKT', false)
          if (!res.data && (row.validEndTime == validEndTime2 || !row.validEndTime)) {
            this.$set(row, 'validEndTime', validEndTime)
          }
          if (!res.data) {
            this.$set(row, 'purType', 'F')
            this.$set(row, 'quoteAttribute', '')
            this.$set(row, 'isNotKT', true)
          }
          this.$nextTick(() => {
            this.isReload = !this.isReload
          })
        }
      })
    },
    //清空表格字段
    setTableEmpty(fieldsarr, row) {
      for (let i = 0; i < fieldsarr.length; i++) {
        this.$set(row, fieldsarr[i], '')
      }
    },
    // 供应商编码变更
    supplierCodeChange(e, row) {
      // 供应商编码变更
      if (row) {
        // 表格内供应商编码变更
        row.supplierId = e.id
        row.supplierCode = e.supplierCode
        row.supplierName = e.supplierName
      }
      this.getLastQuotaItem(row)
    },
    //物料编码变更
    materialCodeChange(e, row) {
      // 物料编码变更
      if (row) {
        // 表格内物料编码变更
        // 工厂选择后，才可选择物料编码；物料需在工厂下是有效物料（物料与工厂关系表）
        const { id, itemCode, itemName, categoryCode, categoryName, categoryId } = e
        row.itemId = id
        row.itemCode = itemCode
        row.itemName = itemName
        // 带出品类信息
        row.categoryCode = categoryCode
        row.categoryName = categoryName
        row.categoryId = categoryId

        //物料编码 重新选择 了就清空 供应商的内容
        const fieldsarr = ['supplierId', 'supplierCode', 'supplierName']
        this.setTableEmpty(fieldsarr, row)
      }
    },
    //物料编码接口配置
    materialTableDialogCofig(row) {
      let params = {}
      if (row && row.siteCode) {
        params = {
          siteCode: row.siteCode
        }
      }
      return {
        pageConfig: pageConfig(
          // '/masterDataManagement/auth/supplier/getSiteItemFuzzyQuery',
          `/masterDataManagement/tenant/item-org-rel/pagedQueryWithCategory?BU_CODE=${localStorage.getItem('currentBu')}`,
          'material',
          params
        ),
        text: 'itemCode',
        value: 'itemCode'
      }
    },
    // 供应商放大镜弹窗配置
    supplierDialogCofig(siteCode) {
      let params = {}
      if (siteCode) {
        params = {
          siteCode
        }
      }
      return {
        pageConfig: pageConfig(
          '/masterDataManagement/tenant/supplier/pagedQueryBySiteCode',
          'supplier',
          params
        ),
        text: 'supplierName',
        value: 'supplierCode'
      }
    },
    // 切换page
    handleCurrentChange(currentPage) {
      if (
        this.formObject.quotaItemList.some((i) => !i.id || i.id?.includes('row_')) ||
        this.isEdit
      ) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('存在未保存的数据，是否继续当前操作')
          },
          success: () => {
            this.pageSettings.current = currentPage
            this.search() //刷新列表
          }
        })
        return
      }
      this.pageSettings.current = currentPage
      this.search() //刷新列表
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      if (
        this.formObject.quotaItemList.some((i) => !i.id || i.id?.includes('row_')) ||
        this.isEdit
      ) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('存在未保存的数据，是否继续当前操作')
          },
          success: () => {
            this.pageSettings.pageSize = pageSize
            this.search() //刷新列表
          }
        })
        return
      }
      this.pageSettings.pageSize = pageSize
      this.search() //刷新列表
    },
    search() {
      const params = {
        condition: 'and',
        // rules: [
        //   {
        //     field: 'quotaCode',
        //     label: '',
        //     operator: 'contains',
        //     type: 'string',
        //     value: this.$route.query.quotaCode || this.formObject.quotaCode
        //   }
        // ],
        id: this.detailId,
        page: {
          current: this.pageSettings.current,
          size: this.pageSettings.pageSize
        }
      }
      // 调用行表条件查询查详情
      this.$API.quotaConfig
        .getAntdQuotaDetailList(params)
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            if (data.records && data.records.length) {
              const total = res?.data?.total || 0
              this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
              this.pageSettings.totalRecordsCount = Number(total)
              this.formObject.quotaItemList = data.records.map((item) => {
                return {
                  ...item,
                  rowId: lodash.uniqueId('row_'), //给每一行添加唯一标识 用于删除
                  validStartTime: dayjs(item.validStartTime).format('YYYY-MM-DD'),
                  validEndTime: dayjs(item.validEndTime).format('YYYY-MM-DD'),
                  quotaPercent: this.transData(item.quotaPercent),
                  historyQuotaPercent: this.transData(item.historyQuotaPercent)
                }
                // item.rowId = lodash.uniqueId('row_') //给每一行添加唯一标识 用于删除
                // //时间转换为 yyyy-mm-dd
                // item.validStartTime = dayjs(item.validStartTime).format('YYYY-MM-DD')
                // item.validEndTime = dayjs(item.validEndTime).format('YYYY-MM-DD')
                // item.quotaPercent = new Decimal(item.quotaPercent || 0)
                //   .mul(new Decimal(100))
                //   .toNumber() //配额比 乘以100
              })
            }
          }
        })
        .finally(() => {
          this.isEdit = false
        })
    },
    transData(data) {
      return data || data === 0 ? new Decimal(data).mul(new Decimal(100)).toNumber() : null
    },
    //保存前校验 校验通过返回params
    async getParams() {
      let params = lodash.cloneDeep(this.formObject)
      //循环 校验必填 工厂 物料编码 供应商编码 生效日期 失效日期 最小起拆量 最小包装量 配额比 校验通过返回params 不通过返回false
      if (params.quotaItemList && params.quotaItemList.length) {
        for (let i = 0; i < params.quotaItemList.length; i++) {
          const row = params.quotaItemList[i]
          if (!row.siteCode) {
            this.$toast({
              content: this.$t(`第${i + 1}条数据请选择工厂`),
              type: 'warning'
            })
            return
          }
          if (!row.supplierCode && row.quoteAttribute !== 'U' && row.quoteAttribute !== 'E') {
            this.$toast({
              content: this.$t(`第${i + 1}条数据请选择供应商编码`),
              type: 'warning'
            })
            return
          }
          if (
            ((!row.supplierCode && !row.purSiteCode) ||
              (row.supplierCode && row.purSiteCode && row.quoteAttribute === 'U')) &&
            row.quoteAttribute !== 'E'
          ) {
            this.$toast({
              content: this.$t(
                `第${i + 1}条数据当特殊采购字段为U时，供应商及工厂有且仅选择一个填写`
              ),
              type: 'warning'
            })
            return
          }
          if (!row.itemCode) {
            this.$toast({
              content: this.$t(`第${i + 1}条数据请选择物料编码`),
              type: 'warning'
            })
            return
          }
          if (!row.validStartTime) {
            this.$toast({
              content: this.$t(`第${i + 1}条数据生效日期不能为空`),
              type: 'warning'
            })
            return
          }
          if (!row.validEndTime) {
            this.$toast({
              content: this.$t(`第${i + 1}条数据失效日期不能为空`),
              type: 'warning'
            })
            return
          }
          //最小起拆量不能为空且须是数字类型 0-100000000，float: 2
          if (row.minOpenQuantity === null || row.minOpenQuantity === '') {
            this.$toast({
              content: this.$t(`第${i + 1}条数据最小起拆量不能为空`),
              type: 'warning'
            })
            return
          }
          if (!/^[0-9]+(.[0-9]{1,2})?$/.test(row.minOpenQuantity)) {
            this.$toast({
              content: this.$t(
                `第${i + 1}条数据最小起拆量必须是数字类型 0~100000000,可保留两位小数`
              ),
              type: 'warning'
            })
            return
          }
          //不能为空 可以为0
          if (row.minPackageQuantity === null || row.minPackageQuantity === '') {
            this.$toast({
              content: this.$t(`第${i + 1}条数据最小包装量不能为空`),
              type: 'warning'
            })
            return
          }
          if (!/^[0-9]+(.[0-9]{1,2})?$/.test(row.minPackageQuantity)) {
            this.$toast({
              content: this.$t(
                `第${i + 1}条数据最小包装量必须是数字类型 0~100000000,可保留两位小数`
              ),
              type: 'warning'
            })
            return
          }
          //配额比 不为空 最大100 最小0 保留两位小数 必须为数字
          if (row.quotaPercent === null || row.quotaPercent === '') {
            this.$toast({
              content: this.$t(`第${i + 1}条数据配额比不能为空`),
              type: 'warning'
            })
            return
          }
          if (!/^[0-9]+(.[0-9]{1,2})?$/.test(row.quotaPercent)) {
            this.$toast({
              content: this.$t(`第${i + 1}条数据配额比必须是数字类型 0~100,可保留两位小数`),
              type: 'warning'
            })
            return
          }
        }
        //处理数据
        //quotaItemList 对生效日期和失效日期进行处理 getTime()转换为时间戳
        params.quotaItemList.forEach((item) => {
          item.validEndTime = dayjs(item.validEndTime).valueOf()
          // let validStartTime = dayjs().format('YYYY-MM-DD') + ' 00:00:00'
          item.validStartTime = dayjs(
            dayjs(item.validStartTime).format('YYYY-MM-DD') + ' 00:00:00'
          ).valueOf()
          item.quotaPercent = new Decimal(item.quotaPercent || 0).div(new Decimal(100)).toNumber() //配额比 除以100
          if (item.id?.includes('row_')) {
            delete item.id
          }
        })
      }

      //校验通过 返回params
      return params
    },
    //保存
    saveDetail: throttle(function (isSubmit = false) {
      // 清除编辑状态
      this.$refs.xTable.$refs.xGrid.clearEdit()
      if (this.submitLoading) return //防止重复提交
      //保存 保存并提交
      this.$refs.ruleForm.validate(async (val) => {
        if (val) {
          const params = await this.getParams()
          if (!params) {
            return
          }
          params.extendSiteList = []
          if (params.extendFactoryList?.length) {
            params.extendSiteList = params.extendFactoryList.map((item) => {
              const targetItem = this.factoryList.find((el) => el.siteCode === item)
              return {
                siteId: targetItem.siteId,
                siteCode: targetItem.siteCode,
                siteName: targetItem.siteName
              }
            })
          }
          delete params.extendFactoryList
          if (!params.quotaCode) {
            if (isSubmit) {
              this.$dialog({
                data: {
                  title: this.$t('操作'),
                  message: this.$t(`确认提交吗？`)
                },
                success: () => {
                  this.$API.quotaConfig.saveQuotaApply(params).then((res) => {
                    if (res.code == 200) {
                      this.formObject.quotaCode = res.data
                      this.getDetail()
                      // this.$toast({
                      //   content: this.$t('添加成功'),
                      //   type: 'success'
                      // })
                      this.submit()
                    }
                  })
                }
              })
            } else {
              this.$API.quotaConfig.saveQuotaApply(params).then((res) => {
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('添加成功'),
                    type: 'success'
                  })
                  this.formObject.quotaCode = res.data
                  this.$router.push({
                    // name: `quota-apply-detail`,
                    query: {
                      type: 'edit',
                      quotaCode: res.data,
                      timeStamp: new Date().getTime()
                    }
                  })
                  this.getDetail()
                }
              })
            }
          } else {
            if (isSubmit) {
              this.$dialog({
                data: {
                  title: this.$t('操作'),
                  message: this.$t(`确认提交吗？`)
                },
                success: () => {
                  this.submitLoading = true
                  this.$API.quotaConfig
                    .saveQuotaApply(params)
                    .then((res) => {
                      if (res.code == 200) {
                        // this.$toast({
                        //   content: this.$t('编辑成功'),
                        //   type: 'success'
                        // })
                        this.submit()
                      }
                    })
                    .catch(() => {
                      this.submitLoading = false
                    })
                }
              })
            } else {
              this.$API.quotaConfig.saveQuotaApply(params).then((res) => {
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('编辑成功'),
                    type: 'success'
                  })
                  this.getDetail()
                }
              })
            }
          }
        }
      })
    }, 1000),
    submit: throttle(function () {
      //校验quotaItemList的工厂代码+物料编码+供应商编码是否唯一 提示所有的重复项
      // const quotaItemList = this.formObject.quotaItemList
      // const quotaItemListLength = quotaItemList.length
      // const quotaItemListMap = {}
      // for (let i = 0; i < quotaItemListLength; i++) {
      //   const item = quotaItemList[i]
      //   const key = `${item.siteCode}${item.itemCode}${item.supplierCode}`
      //   if (quotaItemListMap[key]) {
      //     quotaItemListMap[key].push(i + 1)
      //   } else {
      //     quotaItemListMap[key] = [i + 1]
      //   }
      // }
      // let isRepeat = false
      // let tips = ''
      // for (const key in quotaItemListMap) {
      //   if (quotaItemListMap[key].length > 1) {
      //     isRepeat = true
      //     tips += `第${quotaItemListMap[key].join(
      //       '、'
      //     )} 条数据,工厂代码+物料编码+供应商编码 重复;\n<br/>`
      //   }
      // }
      // if (isRepeat) {
      //   this.$toast({
      //     content: this.$t(tips),
      //     type: 'warning'
      //   })
      //   return
      // }

      // this.$dialog({
      //   data: {
      //     title: this.$t('操作'),
      //     message: this.$t(`确认提交吗？`)
      //   },
      //   success: () => {
      //     }
      // })
      this.submitLoading = true
      this.$API.quotaConfig
        .submitQuotaApply([this.formObject.quotaCode])
        .then((res) => {
          this.submitLoading = false
          if (res.code == 200) {
            this.$toast({
              content: this.$t('提交成功'),
              type: 'success'
            })
            this.backToBusinessConfig()
          }
        })
        .catch(() => {
          this.submitLoading = false
        })
    }, 1000),
    backToBusinessConfig() {
      // this.$router.go(-1)
      this.$router.push({
        path: `/sourcing/quota-calc/quota-apply`
      })
    },
    // 初始化 获取下拉数据
    async init() {
      //工厂
      // await this.$API.customization
      //   .queryUserPermission({ dimensionCode: 'SITE', subjectId: 0, subjectType: 0 })
      //   .then((res) => {
      //     this.factoryList = res.data
      //     this.purFactoryList = res.data
      //   })
      await this.$API.customization.getOrgListByCode({ orgCode: 'SITE' }).then((res) => {
        this.factoryList = res.data
        this.purFactoryList = res.data
        this.factoryList.forEach((item) => {
          // siteId, siteCode, siteName要跟后端返回的数据做匹配以及保存
          item.siteId = item.dimensionIdValue
          item.siteCode = item.dimensionCodeValue
          item.siteName = item.dimensionNameValue
          item.label = item.siteCode + ' - ' + item.siteName
        })
      })
    },
    //获取详情
    getDetail() {
      this.$API.quotaConfig
        .getQuotaDetailHead({
          quotaCode: this.$route.query.quotaCode || this.formObject.quotaCode
        })
        .then((res) => {
          if (res.code == 200) {
            const formObject = res.data
            delete formObject.quotaItemList
            formObject.quotaItemList = []
            if (!formObject.extendSiteList) formObject.extendSiteList = []
            formObject.extendFactoryList = formObject.extendSiteList.map((item) => item.siteCode)
            this.formObject = formObject
            this.detailId = res.data.id
            this.search()
          }
        })
    },
    handleAddHistory() {
      if (!this.formObject.quotaCode) {
        this.$toast({
          content: this.$t('请先保存单据！'),
          type: 'warning'
        })
        return
      }
      if (this.formObject.quotaItemList.some((item) => !item.id || item.id?.includes('row_'))) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t(
              '当前表格中存在未保存数据，点击 "确定" 将放弃未保存的数据并打开历史配额数据弹窗！'
            )
          },
          success: () => {
            this.openHistoryWin()
          }
        })
      } else {
        this.openHistoryWin()
      }
    },
    openHistoryWin() {
      this.$dialog({
        modal: () => import('./components/referHistory/index.vue'),
        data: {
          title: this.title + this.$t('选择'),
          factoryList: this.factoryList,
          quotaCode: this.formObject.quotaCode
        },
        success: () => {
          // const list = lodash.cloneDeep(data)
          // list.forEach((item) => {
          //   item.customId = item.id
          //   delete item.id
          // })
          // const unExitItems = list.filter((item) => {
          //   return !this.formObject.quotaItemList.some((el) => item.customId === el.customId)
          // })
          // this.formObject.quotaItemList = unExitItems.concat(this.formObject.quotaItemList || [])
          this.search()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.button-group {
  display: flex;
  padding: 0 0 16px;
}
/deep/.mt-form-item {
  width: calc(20% - 20px);
  min-width: 200px;
  display: inline-flex;
  margin-right: 20px;
}
.supplier-box {
  position: absolute;
  width: 100%;
  min-height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: #fff;
  overflow: auto;
  -webkit-overflow-scrolling: scroll;

  .miam-container {
    width: 100%;
    background: #fff;
    padding: 0 20px;

    .operate-bar {
      height: 40px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      color: #4f5b6d;

      .op-item {
        cursor: pointer;
        align-items: center;
        margin-right: 20px;
        align-items: center;
        background-color: rgba(0, 0, 0, 0);
        border-color: rgba(0, 0, 0, 0);
        color: #00469c;
      }
    }

    .mian-info {
      background: rgba(255, 255, 255, 1);
      border-radius: 8px;
      // box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    }

    .relation-ships {
      background: rgba(255, 255, 255, 1);
      border-radius: 8px;
      // box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      // margin-top: 20px;
      // height: calc(100% - 164px);
      p {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 8px;
        padding-top: 8px;
      }
    }
  }
}
::v-deep.red-color {
  color: red;
}
.grayBtn {
  color: #999999 !important;
  cursor: not-allowed !important;
}
</style>
