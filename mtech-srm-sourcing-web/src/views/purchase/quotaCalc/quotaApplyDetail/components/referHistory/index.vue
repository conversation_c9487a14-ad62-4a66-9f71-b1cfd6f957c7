<template>
  <mt-dialog
    ref="dialog"
    :buttons="buttons"
    :style="{ width: '940px' }"
    :header="$t('历史配额参考')"
    @beforeClose="cancel"
  >
    <div class="main-container">
      <!-- 顶部主要信息 -->
      <div class="mian-info">
        <mt-form ref="ruleForm" :model="formObject" :rules="formRules">
          <mt-form-item prop="siteCode" :label="$t('工厂')">
            <mt-select
              v-model="formObject.siteCode"
              float-label-type="Never"
              :allow-filtering="true"
              filter-type="Contains"
              :show-clear-button="true"
              :data-source="factoryList"
              :fields="{ text: 'label', value: 'siteCode' }"
              :placeholder="$t('请选择工厂')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item :label="$t('物料编码')">
            <mt-input width="100%" type="text" v-model="formObject.itemCode"></mt-input>
          </mt-form-item>
          <mt-form-item :label="$t('品类编码')">
            <mt-input width="100%" type="text" v-model="formObject.categoryCode"></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
      <div class="button-bar">
        <span @click="reset()">{{ $t('重置') }}</span>
        <span @click="search('btn')">{{ $t('查询') }}</span>
      </div>
      <div class="relation-ships">
        <!----  表格  --------->
        <sc-table
          ref="xTable"
          grid-id="7c910cd8-baa9-4717-849b-d2402d3d7ba4"
          :loading="loading"
          :is-show-refresh-bth="true"
          :columns="vxeColumns"
          :table-data="quotaItemList"
          @refresh="search"
        >
        </sc-table>
        <!-- 分页 -->
        <mt-page
          ref="pageRef"
          class="flex-keep custom-page"
          :page-settings="pageSettings"
          :total-pages="pageSettings.totalPages"
          @currentChange="handleCurrentChange"
          @sizeChange="handleSizeChange"
        />
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { vxeColumns } from './config'
import ScTable from '@/components/ScTable/src/index'

import Decimal from 'decimal.js'
import lodash from 'lodash'
import dayjs from 'dayjs'

export default {
  components: {
    ScTable
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      loading: false,
      factoryList: [],
      formObject: {
        itemCode: null,
        categoryCode: null,
        siteCode: []
      },
      // 配额明细列表
      quotaItemList: [],
      selectedRowKeys: [], // 表格勾选行的id
      selectedRows: [], // 表格勾选行的内容
      vxeColumns: [], //table列配置
      pageSettings: {
        pageCount: 5,
        pageSize: 10, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [10, 20, 50, 100, 200]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('添加') }
        }
      ],
      formRules: {
        siteCode: [
          {
            required: true,
            message: this.$t('请先选择工厂'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  mounted() {
    this.init()
    this.vxeColumns = vxeColumns()
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    //新增
    handleAdd() {
      this.confirm()
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageSettings.current = currentPage
      this.search('pageSettings')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.search()
    },
    reset() {
      this.formObject.itemCode = null
      this.formObject.categoryCode = null
      this.formObject.siteList = []
    },
    search(type) {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const params = {
            ...this.formObject,
            page: {
              current: type === 'btn' ? 1 : this.pageSettings.current,
              size: this.pageSettings.pageSize
            }
          }
          this.loading = true
          // 调用行表条件查询查详情
          this.$API.quotaConfig
            .getQuotaHistoryData(params)
            .then((res) => {
              const { code, data } = res
              if (code === 200) {
                if (data.records) {
                  const total = res?.data?.total || 0
                  this.pageSettings.totalPages = Math.ceil(
                    Number(total) / this.pageSettings.pageSize
                  )
                  this.pageSettings.totalRecordsCount = Number(total)
                  this.quotaItemList = data.records.map((item) => {
                    return {
                      ...item,
                      rowId: lodash.uniqueId('row_'), //给每一行添加唯一标识 用于删除
                      validStartTime: dayjs(item.validStartTime).format('YYYY-MM-DD'),
                      validEndTime: dayjs(item.validEndTime).format('YYYY-MM-DD'),
                      quotaPercent: this.transData(item.quotaPercent),
                      historyQuotaPercent: this.transData(item.historyQuotaPercent)
                    }
                  })
                }
              }
            })
            .finally(() => {
              this.loading = false
            })
        }
      })
    },
    transData(data) {
      return data || data === 0 ? new Decimal(data).mul(new Decimal(100)).toNumber() : null
    },
    // 初始化 获取下拉数据
    init() {
      this.factoryList = this.modalData.factoryList
    },
    async confirm() {
      // let selectedRows = lodash.cloneDeep(this.selectedRows || [])
      // // if (!this.selectedRows.length) {
      // //   selectedRows = this.quotaItemList || []
      // // }
      let selectedRows = this.$refs.xTable.$refs.xGrid.getCheckboxRecords()
      selectedRows.forEach((item) => {
        item.historyQuotaPercent = new Decimal(item.historyQuotaPercent || 0)
          .div(new Decimal(100))
          .toNumber()
      })
      const params = {
        ...this.formObject,
        quotaCode: this.modalData.quotaCode,
        itemDtoList: selectedRows
      }
      const res = await this.$API.quotaConfig.postQuotaHistoryData(params)
      if (res.code === 200) {
        this.$toast({
          content: this.$t('添加成功'),
          type: 'success'
        })
        this.$emit('confirm-function', selectedRows)
      }
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.button-group {
  display: flex;
  padding: 0 0 16px;
}
/deep/ .mt-form {
  padding-top: 16px;
  .mt-form-item {
    width: calc(33% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;
    margin-bottom: 0;
  }
}
.main-container {
  width: 100%;
  background: #fff;

  .operate-bar {
    height: 60px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: #4f5b6d;

    .op-item {
      cursor: pointer;
      align-items: center;
      margin-right: 20px;
      align-items: center;
      background-color: rgba(0, 0, 0, 0);
      border-color: rgba(0, 0, 0, 0);
      color: #00469c;
    }
  }
  .button-bar {
    padding: 12px 0 0 0;
    span {
      margin-right: 16px;
      font-family: PingFangSC-Regular;
      font-size: 12px;
      color: #2783fe;
      cursor: pointer;
      user-select: none;
    }
  }

  .mian-info {
    background: rgba(255, 255, 255, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  }

  .relation-ships {
    background: rgba(255, 255, 255, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    // margin-top: 12px;
    // height: calc(100% - 164px);
    p {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 8px;
      padding-top: 8px;
    }
  }
}
::v-deep.red-color {
  color: red;
}
.grayBtn {
  color: #999999 !important;
  cursor: not-allowed !important;
}
</style>
