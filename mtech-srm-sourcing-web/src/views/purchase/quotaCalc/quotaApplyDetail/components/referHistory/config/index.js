import { i18n } from '@/main.js'

export const vxeColumns = function () {
  let columns = [
    {
      type: 'checkbox',
      minWidth: 50
      // fixed: 'left'
    },
    {
      title: i18n.t('工厂代码'),
      field: 'siteCode',
      align: 'left',
      minWidth: 100
    },
    {
      title: i18n.t('物料编码'),
      field: 'itemCode',
      align: 'left',
      minWidth: 120
    },
    {
      title: i18n.t('物料名称'),
      field: 'itemName',
      minWidth: 160,
      align: 'left'
    },
    {
      title: i18n.t('供应商编码'),
      field: 'supplierCode',
      align: 'left',
      minWidth: 100
    },
    {
      title: i18n.t('供应商名称'),
      field: 'supplierName',
      align: 'left',
      minWidth: 120
    },
    {
      title: i18n.t('品类编码'),
      field: 'categoryCode',
      minWidth: 100,
      align: 'left'
    },
    {
      title: i18n.t('配额(%)'),
      field: 'historyQuotaPercent',
      align: 'left',
      minWidth: 90
    }
  ]
  return columns
}
