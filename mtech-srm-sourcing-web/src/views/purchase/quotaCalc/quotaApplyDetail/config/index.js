import { i18n } from '@/main.js'
import Vue from 'vue'
import Decimal from 'decimal.js'
import utils from '@/utils/utils'

export const toolbar = []
export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'lineNo',
    headerText: i18n.t('行号')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'), //单据状态 -1:已停用 0:草稿 1:审批中 2:审批驳回 3:已生效
    valueConverter: {
      type: 'map',
      map: [
        { status: 0, label: i18n.t('失效'), cssClass: 'title-#9baac1' },
        { status: 1, label: i18n.t('生效'), cssClass: 'title-#eda133' }
      ],
      fields: { text: 'label', value: 'status' }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂')
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'quoteAttribute',
    headerText: i18n.t('报价属性'),
    valueConverter: {
      type: 'map',
      map: {
        mailing_price: i18n.t('寄售价'),
        standard_price: i18n.t('标准价')
      }
    }
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('生效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('失效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'minOpenQuantity',
    headerText: i18n.t('最小起拆量')
  },
  {
    field: 'unitName',
    headerText: i18n.t('单位'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<span>{{data.unitName}}/{{data.unitCode}}</span>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'quotaPercent',
    headerText: i18n.t('配额比（%）'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<span>{{quotaPercent}}</span>`,
          data() {
            return { data: {} }
          },
          computed: {
            quotaPercent() {
              return new Decimal(this.data.quotaPercent).mul(new Decimal(100))
            }
          }
        })
      }
    }
  },
  {
    field: 'minPackageQuantity',
    headerText: i18n.t('最小包装量')
  },
  {
    field: 'minPurchaseQuantity',
    headerText: i18n.t('最小采购量')
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

export const vxeColumns = function (that) {
  let columns = [
    {
      width: '50',
      type: 'checkbox'
    },
    {
      // title: i18n.t('*工厂代码'),
      title: i18n.t('工厂代码'),
      field: 'siteCode',
      align: 'left',
      minWidth: 110,
      editRender: {},
      slots: { edit: 'siteCodeEdit' }
    },
    {
      title: i18n.t('工厂名称'),
      field: 'siteName',
      align: 'left',
      minWidth: 180
      // scopedSlots: { customRender: 'siteName' }
    },
    {
      title: i18n.t('物料编码'),
      field: 'itemCode',
      align: 'left',
      minWidth: 150,
      editRender: {},
      slots: { edit: 'itemCodeEdit' }
    },
    {
      title: i18n.t('物料名称'),
      field: 'itemName',
      minWidth: 180,
      align: 'left'
    },
    {
      title: i18n.t('采购类型'),
      field: 'purType',
      align: 'left',
      minWidth: 120,
      editRender: {},
      slots: { edit: 'purTypeEdit' },
      formatter: ({ cellValue }) => {
        return that.getDictName(that.purTypeList, cellValue)
      }
    },
    {
      title: i18n.t('特殊采购/报价属性'),
      field: 'quoteAttribute',
      align: 'left',
      minWidth: 180,
      editRender: {},
      slots: { edit: 'quoteAttributeEdit' },
      formatter: ({ cellValue }) => {
        return that.getDictName(that.quoteAttributeList, cellValue)
      }
    },
    {
      // title: i18n.t('*供应商编码'),
      title: i18n.t('供应商编码'),
      field: 'supplierCode',
      align: 'left',
      minWidth: 120,
      editRender: {},
      slots: { edit: 'supplierCodeEdit' }
    },
    {
      title: i18n.t('供应商名称'),
      field: 'supplierName',
      align: 'left',
      minWidth: 120
    },
    {
      title: i18n.t('采购工厂'),
      field: 'purSiteCode',
      align: 'left',
      minWidth: 160,
      editRender: {},
      slots: { edit: 'purSiteCodeEdit' }
    },
    {
      title: i18n.t('品类编码'),
      field: 'categoryCode',
      minWidth: 100,
      align: 'left'
    },
    {
      title: i18n.t('品类名称'),
      field: 'categoryName',
      minWidth: 100,
      align: 'left'
    },
    {
      title: i18n.t('最小起拆量'),
      field: 'minOpenQuantity',
      align: 'left',
      minWidth: 120,
      editRender: { name: 'input', attrs: { type: 'number' } }
    },

    {
      title: i18n.t('最小包装量'),
      field: 'minPackageQuantity',
      align: 'left',
      minWidth: 120,
      editRender: { name: 'input', attrs: { type: 'number' } }
    },
    {
      title: i18n.t('最小采购量'),
      field: 'minPurchaseQuantity',
      align: 'left',
      minWidth: 120,
      editRender: { name: 'input', attrs: { type: 'number' } }
    },
    {
      title: i18n.t('历史配额(%)'),
      field: 'historyQuotaPercent',
      minWidth: 100,
      align: 'left'
    },
    {
      title: i18n.t('配额比(%)'),
      field: 'quotaPercent',
      align: 'left',
      minWidth: 120,
      // editConfig: {
      //   controlKey: 'editable',
      //   render(h, scoped) {
      //     return <span>{scoped.row.quotaPercent}</span>
      //   },
      //   //不为空 最大100 最小0 保留两位小数 必须为数字
      //   rules: function () {
      //     return [
      //       {
      //         required: true,
      //         message: i18n.t('配额比不能为空'),
      //         triggle: 'change'
      //       },
      //       { validator: validateNumber, max: 100, min: -1, checkMin: true, float: 2 }
      //     ]
      //   }
      // }
      editRender: { name: 'input', attrs: { type: 'number', max: 100, min: 0, digits: 2 } }
    },
    {
      title: i18n.t('价格'),
      field: 'historyPrice',
      minWidth: 100,
      align: 'left'
    },
    {
      title: i18n.t('备注'),
      field: 'remark',
      align: 'left',
      minWidth: 200,
      editRender: { name: 'input' }
    },
    {
      title: i18n.t('生效日期'),
      field: 'validStartTime',
      align: 'left',
      minWidth: 140,
      editRender: {},
      slots: { edit: 'validStartTimeEdit' },
      formatter: ({ cellValue }) => {
        let date = ''
        if (cellValue && new Date(cellValue)) {
          date = utils.formatTime(new Date(cellValue), 'YYYY-mm-dd')
        }
        return date
      }
    },
    {
      title: i18n.t('失效日期'),
      field: 'validEndTime',
      align: 'left',
      minWidth: 140,
      editRender: {},
      slots: { edit: 'validEndTimeEdit' },
      formatter: ({ cellValue }) => {
        let date = ''
        if (cellValue && new Date(cellValue)) {
          date = utils.formatTime(new Date(cellValue), 'YYYY-mm-dd')
        }
        return date
      }
    }
    // {
    //   title: i18n.t('操作'),
    //   dataIndex: 'operation',
    //   field: 'operation',
    //   fixed: 'right',
    //   scopedSlots: { customRender: 'operation' }
    // }
  ]
  return columns
}
const materialColumn = [
  // {
  //   type: 'checkbox',
  //   width: '60'
  // },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    cssClass: 'field-content',
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  }
]
const categoryColumn = [
  // {
  //   type: 'checkbox',
  //   width: '60'
  // },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    cssClass: 'field-content'
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]
const supplierColumn = [
  // {
  //   type: 'checkbox',
  //   width: '60'
  // },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    cssClass: 'field-content'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  }
]
export const pageConfig = (url, columnType, params) => {
  let columnData = []
  let gridId = {
    material: '1f682e86-df76-4846-8755-9f89259b323e',
    category: '4b5abc83-e223-4016-ae7c-3ef121c52b8d',
    supplier: '80ee09b6-ed0a-4010-a493-30d93afc66eb'
  }
  if (columnType === 'material') {
    columnData = materialColumn
  } else if (columnType === 'category') {
    columnData = categoryColumn
  } else {
    columnData = supplierColumn
  }
  let asyncConfig = {
    url
  }
  if (params) {
    asyncConfig.params = params
  }
  return [
    {
      toolbar,
      useToolTemplate: false,
      gridId: gridId[columnType],
      grid: {
        allowFiltering: true,
        columnData,
        allowSorting: false,
        allowSelection: true,
        selectionSettings: {
          type: 'Multiple',
          mode: 'Row'
        },
        asyncConfig
      }
    }
  ]
}
