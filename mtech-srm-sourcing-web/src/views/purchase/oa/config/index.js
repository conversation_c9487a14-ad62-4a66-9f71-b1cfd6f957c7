import { getValueByPath } from '@/utils/obj'
import { i18n, permission } from '@/main.js'
import selectedItemCode from 'COMPONENTS/NormalEdit/selectItemCode' // 物料
import { useFiltering } from '@/utils/ej/select'
import cellChanged from 'COMPONENTS/NormalEdit/cellChanged' // 单元格被改变（纯展示）
import { createEditInstance } from '@/utils/ej/dataGrid/index'
let time1 = 0
let time2 = 0
export const resetTime = () => {
  time1 = 0
  time2 = 0
}
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'Delete', icon: 'icon_solid_edit', title: i18n.t('删除') },
  // { id: "save", icon: "icon_solid_edit", title: i18n.t("保存") },
  { id: 'Cancel', icon: 'icon_solid_Cancel', title: i18n.t('取消') },
  { id: 'Import', icon: 'icon_solid_Createorder', title: i18n.t('导入') },
  { id: 'Export_a', icon: 'icon_solid_Createorder', title: i18n.t('导出') }
]
export const columnDataOne = ({
  supplierList = [],
  getSupplierList,
  siteNameDataSource = [],
  allCurrency = []
} = {}) => {
  const editInstance = createEditInstance().onInput((ctx, { field, rowData, value }) => {
    console.log('onInput', { ctx, rowData, value, field })
    if (field === 'supplierName') {
      // 供应商
      const { dataSource, fields } = ctx.getOptions(field)
      const row = dataSource.find((e) => e[fields?.value || 'value'] === value)
      if (row) {
        ctx.setValueByField('supplierCode', row.supplierCode)
        ctx.setValueByField('supplierId', row.id)
      }
    } else if (field === 'bidCurrencyName') {
      ctx.setValueByField('bidCurrencyCode', value)
    } else if (field === 'siteName') {
      // 工厂被编辑
      const { dataSource, fields } = ctx.getOptions(field)
      const row = dataSource.find((e) => e[fields?.value || 'value'] === value)
      console.log('row', row)
      if (row) {
        ctx.setValueByField('siteCode', row.siteCode)
        ctx.setValueByField('siteId', row.id)
        ctx.setValueByField('companyName', row.parentName)
        sessionStorage.setItem('organizationId', row.organizationId)
        sessionStorage.setItem('organizationCode', row.parentCode)
      }
    }
  })
  return [
    {
      width: '50',
      type: 'checkbox',
      allowEditing: false
    },
    {
      width: '120',
      field: 'siteCode',
      headerText: i18n.t('工厂编码'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      width: '150',
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      edit: editInstance.create({
        getEditConfig: (e) => ({
          type: 'mt-select',
          dataSource: siteNameDataSource,
          fields: { value: 'siteName', text: 'siteName' },
          placeholder: i18n.t('请选择工厂'),
          created: () => {
            let a = siteNameDataSource.filter((item) => {
              return item.siteCode == e.rowData.siteCode
            })
            console.log('create2', a)
            if (a.length == 0) {
              sessionStorage.setItem('organizationId', '')
              sessionStorage.setItem('organizationCode', '')
            } else {
              sessionStorage.setItem('organizationId', a[0].organizationId)
              sessionStorage.setItem('organizationCode', a[0].parentCode)
              getSupplierList({
                fuzzyNameOrCode: '',
                organizationCode: sessionStorage.getItem('organizationCode')
              }).then((r) => {
                editInstance.setOptions('supplierName', {
                  dataSource: Array.isArray(r?.data) ? r.data : []
                })
              })
            }
          },
          change: function (args) {
            console.log('callback', args)
            if (args.name == 'change') {
              if (args?.itemData?.organizationId) {
                sessionStorage.setItem(
                  'siteParam',
                  JSON.stringify({
                    organizationId: args?.itemData?.organizationId,
                    siteCode: args?.itemData?.siteCode
                  })
                )

                sessionStorage.setItem('organizationId', args?.itemData?.organizationId)
                sessionStorage.setItem('organizationCode', args?.itemData?.parentCode)
                getSupplierList({
                  fuzzyNameOrCode: '',
                  organizationCode: sessionStorage.getItem('organizationCode')
                }).then((r) => {
                  editInstance.setOptions('supplierName', {
                    dataSource: Array.isArray(r?.data) ? r.data : []
                  })
                })
              } else {
                sessionStorage.removeItem('siteParam')
              }
            }
          }
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return siteNameDataSource.find((e) => e.supplierName === cellVal)?.text ?? cellVal
      }
    },
    {
      width: '120',
      field: 'categoryName',
      headerText: i18n.t('品类名称'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      width: '200',
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      editTemplate: () => {
        return {
          template: selectedItemCode
        }
      }
    },
    {
      width: '120',
      field: 'itemName',
      headerText: i18n.t('物料描述'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      width: '120',
      field: 'basicPrice',
      headerText: i18n.t('基准价格'),
      type: 'number',
      validationRules: {
        required: true,
        min: 0,
        regex: ['^[1-9]\\d{0,11}(\\.\\d{1,2})?$|^0(\\.\\d{1,2})?$', '最多输入12位整数和两位小数！']
      },
      editType: 'numericEdit'
    },
    {
      width: '120',
      field: 'unitName',
      headerText: i18n.t('基本单位'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      width: '120',
      field: 'currencyName',
      headerText: i18n.t('币种'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          dataSource: allCurrency,
          placeholder: i18n.t('请选择币种'),
          'allow-filtering': true,
          filtering: useFiltering(function (e) {
            if (typeof e.text === 'string' && e.text) {
              e.updateData(
                allCurrency.filter((f) => {
                  return f?.text.indexOf(e.text) > -1
                })
              )
            } else {
              e.updateData(allCurrency)
            }
          })
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return allCurrency.find((e) => e.value === cellVal)?.text ?? cellVal
      }
    },
    {
      width: '120',
      field: 'companyName',
      headerText: i18n.t('所属公司'),
      allowEditing: false,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      width: '150',
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          'allow-filtering': true,
          dataSource: supplierList,
          fields: { value: 'supplierName', text: 'supplierName' },
          placeholder: i18n.t('供应商'),
          filtering: useFiltering(function (e) {
            getSupplierList({
              fuzzyNameOrCode: e.text,
              organizationCode: sessionStorage.getItem('organizationCode')
            }).then((r) => {
              // return e.updateData(r.data);
              editInstance.setOptions('supplierName', {
                dataSource: Array.isArray(r?.data) ? r.data : []
              })
            })
            // if (typeof e.text === "string" && e.text) {
            //   e.updateData(
            //     supplierList.filter((f) => {
            //       return f?.text.indexOf(e.text) > -1;
            //     })
            //   );
            // } else {
            //   e.updateData(supplierList);
            // }
          })
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return supplierList.find((e) => e.value === cellVal)?.text ?? cellVal
      }
    },
    {
      width: '150',
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      width: '120',
      field: 'effectiveStartDate',
      headerText: i18n.t('生效日期'),
      format: 'y-M-d',
      validationRules: {
        required: true,
        max: [
          (args) => {
            time1 = new Date(args.value).getTime()
            if (time2 === 0 || time1 === time2) {
              return true
            }
            return time1 < time2
          },
          i18n.t('生效日期不能晚于失效日期')
        ]
      },
      type: 'date',
      editType: 'datePickerEdit',
      valueAccessor: (field, data) => {
        data['effectiveStartDate'] = Number(data['effectiveStartDate'])
        return Number(data['effectiveStartDate'])
      }
    },
    {
      width: '120',
      field: 'effectiveEndDate',
      headerText: i18n.t('失效日期'),
      // formatter: Formatter.createFmtDatetime("YYYY-MM-DD"),
      format: 'y-M-d',
      validationRules: {
        required: true,
        max: [
          (args) => {
            time2 = new Date(args.value).getTime()
            if (time2 === 0 || time1 === time2) {
              return true
            }
            return time1 < time2
          },
          i18n.t('生效日期不能早于失效日期')
        ]
      },
      type: 'date',
      editType: 'datePickerEdit',
      valueAccessor: (field, data) => {
        data['effectiveEndDate'] = Number(data['effectiveEndDate'])
        return Number(data['effectiveEndDate'])
      }
    },
    {
      width: '120',
      field: 'createUserName',
      allowEditing: false,
      headerText: i18n.t('创建人')
    },
    {
      width: '120',
      field: 'createTime',
      headerText: i18n.t('创建日期'),
      allowEditing: false,
      valueConverter: { type: 'date', format: 'YYYY-MM-DD' }
    }
  ]
}

export function pageConfig(url) {
  return [
    {
      toolbar,
      gridId: permission.gridId['purchase']['oa'],
      grid: {
        editSettings: {
          allowAdding: true,
          allowEditing: true,
          allowDeleting: true,
          mode: 'Normal',
          allowEditOnDblClick: true,
          showConfirmDialog: false,
          showDeleteConfirmDialog: true,
          newRowPosition: 'Top'
        },
        actionComplete: this.actionComplete,
        allowFiltering: true,
        allowSorting: false,
        columnData: [],
        asyncConfig: {
          url
        }
      }
    }
  ]
}
