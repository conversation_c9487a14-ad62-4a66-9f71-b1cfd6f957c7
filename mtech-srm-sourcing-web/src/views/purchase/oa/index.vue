<template>
  <div style="height: 100%">
    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadInitBalanceRef"
      :down-template-name="downTemplateName"
      :request-urls="requestUrls"
      :down-template-params="downTemplateParams"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    />
  </div>
</template>

<script>
import { pageConfig, columnDataOne, resetTime } from './config'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  components: {
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  data() {
    return {
      downTemplateParams: {
        page: {
          current: 1,
          size: 2
        },
        isTemplate: 1,
        rules: []
      },
      requestUrls: {},
      downTemplateName: this.$t('OA审批模板'),
      pageConfig: []
    }
  },
  async created() {
    const res = await this.$API.masterData
      .getSiteList({
        condition: '',
        page: {
          current: 1,
          size: 1000
        },
        pageFlag: false,
        rules: []
      })
      .catch(() => {})
    let siteNameDataSource = res?.data?.records || []
    const item = await this.$API.masterData.getSupplierList().catch(() => {})
    let supplierList = item.data || []

    const direct = await this.$API.priceService.getDeliveryPlace().catch(() => {})
    let directDelivery = []
    if (direct) {
      directDelivery = direct.data.map(({ itemName, itemCode }) => ({
        value: itemCode,
        text: itemName
      }))
    }
    const Currency = await this.$API.masterData.queryAllCurrency().catch(() => {})
    let allCurrency = []
    if (Currency) {
      allCurrency = Currency.data.map(({ currencyName, currencyCode }) => ({
        value: currencyCode,
        text: currencyName
      }))
    }
    let a = pageConfig.call(this, 'sourcing/tenant/basic/price/query')
    a[0].grid.columnData = columnDataOne({
      supplierList,
      getSupplierList: (e) => {
        return this.$API.masterData.getSupplierList(e)
      },
      siteNameDataSource,
      directDelivery,
      allCurrency
    })
    // this.$set(
    //   this.pageConfig[0].grid,
    //   "columnData",
    //   columnDataOne({
    //     supplierList,
    //     siteNameDataSource,
    //     directDelivery,
    //     allCurrency,
    //   })
    // );
    this.pageConfig.push(a[0])
    // this.$set(this, "pageConfig", this.pageConfig);
  },
  methods: {
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.$refs.templateRef.refreshCurrentGridData()
    },
    cellEdit(e) {
      console.log('cellEdit', e)
    },
    addE(e) {
      if (e.data && e.data.effectiveEndDate && typeof e.data.effectiveEndDate != 'number') {
        e.data.effectiveEndDate = e.data.effectiveEndDate.getTime()
        e.data.effectiveStartDate = e.data.effectiveStartDate.getTime()
      }
      this.$API.purchaseOa
        .saveExcel(e.data)
        .then(() => {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          resetTime()
          this.$refs.templateRef.refreshCurrentGridData()
        })
        .catch((e) => {
          this.$toast({
            content: e?.errorLabels[0]?.message,
            type: 'error'
          })
          // resetTime();
          // this.$refs.templateRef.refreshCurrentGridData();
        })
    },
    actionComplete(e) {
      if (e.requestType == 'add') {
        sessionStorage.removeItem('siteParam')
        console.log('actionComplete', e)
        if (e.data.effectiveEndDate) this.addE(e)
      } else if (e.requestType == 'save') {
        console.log('save', e)
        this.addE(e)
      }
    },
    // 上传（显示弹窗）
    handleUpload() {
      this.requestUrls = {
        templateUrlPre: 'purchaseOa',
        templateUrl: 'exportExcel',
        uploadUrl: 'importExcel'
        // rfxId: this.$route.query.rfxId,
      }
      this.showUploadExcel(true)
    },
    // 展示/不展示 上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadInitBalanceRef.uploadData = [] // 清空数据
        this.$refs.uploadInitBalanceRef.$refs.uploader.files = []
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    delete(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          this.$API.purchaseOa.deleteExcel({ ids }).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    //表格头部按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.grid.getSelectedRecords()
      if (_selectGridRecords.length < 1 && (e.toolbar.id == 'Edit' || e.toolbar.id == 'Delete')) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Import') {
        this.handleUpload()
      } else if (e.toolbar.id == 'Export_a') {
        this.$API.purchaseOa.exportExcel().then((res) => {
          this.$toast({
            type: 'success',
            content: this.$t('正在导出，请稍后！')
          })
          let fileName = getHeadersFileName(res)
          download({ fileName: fileName, blob: res.data })
        })
      } else if (e.toolbar.id == 'Delete') {
        const ids = _selectGridRecords
          .map((e) => {
            return e.id
          })
          .join(',')
        this.delete(ids)
      } else if (e.toolbar.id == 'Add') {
        e.grid.addRecord()
      } else if (e.toolbar.id == 'Cancel') {
        e.grid.closeEdit()
      }
    }
  }
}
</script>

<style></style>
