<template>
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.status"
            css-class="rule-element"
            :data-source="statusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择状态')"
          />
        </mt-form-item>
        <mt-form-item prop="rfxCode" :label="$t('单据号')" label-style="top">
          <mt-input
            v-model="searchFormModel.rfxCode"
            :show-clear-button="true"
            :placeholder="$t('请输入单据号')"
          />
        </mt-form-item>
        <mt-form-item prop="rfxName" :label="$t('单据名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.rfxName"
            :show-clear-button="true"
            :placeholder="$t('请输入单据名称')"
          />
        </mt-form-item>
        <!-- <mt-form-item prop="fixPriceObjectType" :label="$t('定价对象')" label-style="top">
          <mt-select
            v-model="searchFormModel.fixPriceObject"
            css-class="rule-element"
            :data-source="fixPriceObjectTypeList"
            :fields="{ label: 'sourcingObj', value: 'sourcingObj' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择定价对象类型')"
            @change="handleFixPriceObjectChange"
          />
        </mt-form-item> -->
        <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
          <mt-select
            v-model="searchFormModel.companyCode"
            css-class="rule-element"
            :data-source="companyList"
            :fields="{ text: 'text', value: 'orgCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择公司')"
            @change="handleCompanyChange"
          />
        </mt-form-item>
        <mt-form-item prop="purchaseOrgCode" :label="$t('采购组织')">
          <mt-select
            v-model="searchFormModel.purchaseOrgCode"
            :disabled="!searchFormModel.companyCode"
            :data-source="purchaseOrgList"
            :fields="{ text: 'text', value: 'organizationCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择采购组织')"
          />
        </mt-form-item>
        <mt-form-item prop="factoryCode" :label="$t('工厂')">
          <mt-select
            v-model="searchFormModel.factoryCode"
            :disabled="!searchFormModel.purchaseOrgCode"
            :data-source="factoryList"
            :fields="{ text: 'text', value: 'orgCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择工厂')"
          />
        </mt-form-item>
        <mt-form-item prop="categoryCode" :label="$t('品类编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.categoryCode"
            :show-clear-button="true"
            :placeholder="$t('请输入品类编码')"
          />
        </mt-form-item>
        <mt-form-item prop="categoryName" :label="$t('品类名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.categoryName"
            :show-clear-button="true"
            :placeholder="$t('请输入品类名称')"
          />
        </mt-form-item>
        <mt-form-item prop="materialCode" :label="$t('物料编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.materialCode"
            :show-clear-button="true"
            :placeholder="$t('请输入物料编码')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.supplierCode"
            :show-clear-button="true"
            :placeholder="$t('请输入供应商编码')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.supplierName"
            :show-clear-button="true"
            :placeholder="$t('请输入供应商名称')"
          />
        </mt-form-item>
        <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
          <mt-input
            v-model="searchFormModel.createUserName"
            :show-clear-button="true"
            :placeholder="$t('请输入创建人')"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :allow-edit="false"
            :placeholder="$t('请选择创建时间')"
            :open-on-focus="true"
            @change="handleDateChange"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      grid-id="57e6e96b-9c11-4bb8-a69a-8746468d845d"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="listColumns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import { getTimeList } from '@/utils/obj'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import { statusList, priceClassifyList, listToolbar, docTypeCodeList } from './config/index'
import XEUtils from 'xe-utils'

export default {
  components: {
    ScTable,
    CollapseSearch
  },
  mixins: [mixin],
  data() {
    return {
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 20,
        pageSizes: [20, 50, 200, 500, 1000, 2000]
      },
      pageInfo: {
        size: 20,
        current: 1
      },
      statusList,
      docTypeCodeList,
      priceClassifyList,
      toolbar: listToolbar,
      searchFormModel: {
        createUserName: JSON.parse(sessionStorage.getItem('userInfo'))?.username || null,
        createTime: getTimeList(3)
      },
      tableData: [],
      loading: false,
      type: 'list'
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    // 列配置
    listColumns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          width: 50,
          field: 'index',
          title: this.$t('序号')
        },
        {
          field: 'status',
          title: this.$t('状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = statusList.find((item) => item.value === row.status)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'rfxCode',
          title: this.$t('单据号'),
          minWidth: 180,
          slots: {
            default: ({ row, column }) => {
              return [<a on-click={() => this.handleClickCellTitle(row, column)}>{row.rfxCode}</a>]
            }
          }
        },
        {
          field: 'rfxName',
          title: this.$t('单据名称'),
          minWidth: 140
        },
        {
          field: 'companyCode',
          title: this.$t('公司'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [<span>{row.companyCode + '-' + row.companyName}</span>]
            }
          }
        },
        {
          field: 'purchaseOrgCode',
          title: this.$t('采购组织'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [<span>{row.purchaseOrgCode + '-' + row.purchaseOrgName}</span>]
            }
          }
        },
        {
          field: 'factoryCode',
          title: this.$t('工厂'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [<span>{row.factoryCode + '-' + row.factoryName}</span>]
            }
          }
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [<span>{row.supplierCode + '-' + row.supplierName}</span>]
            }
          }
        },
        {
          field: 'oaApproveLink',
          title: this.$t('OA申请单查看'),
          minWidth: 120,
          slots: {
            default: ({ row, column }) => {
              return [
                <div>
                  <a
                    v-show={row.oaApproveLink}
                    on-click={() => this.handleClickCellTitle(row, column)}>
                    {this.$t('查看')}
                  </a>
                  <span v-show={!row.oaApproveLink}>-</span>
                </div>
              ]
            }
          }
        },
        {
          field: 'remark',
          title: this.$t('备注')
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          minWidth: 140
        }
      ]
    }
  },
  mounted() {
    this.handleSearch()
  },
  activated() {
    this.handleSearch()
  },
  methods: {
    // 选择公司
    handleCompanyChange(e) {
      this.$set(this.searchFormModel, 'purchaseOrgCode', null)
      if (e.itemData.id) {
        this.getPurchaseOrgList(e.itemData.id)
      } else {
        this.purchaseOrgList = []
      }
    },
    // 选择定价对象
    handleFixPriceObjectChange(e) {
      this.$set(
        this.searchFormModel,
        'fixPriceObjectType',
        e.value ? e.itemData.sourcingObjType : null
      )
    },
    // 日期格式化
    handleDateChange(e) {
      if (e.startDate) {
        const startDate = XEUtils.toDateString(e.startDate, 'yyyy-MM-dd') + ' 00:00:00'
        const endDate = XEUtils.toDateString(e.endDate, 'yyyy-MM-dd') + ' 23:59:59'
        this.searchFormModel['createStartTime'] = XEUtils.timestamp(startDate)
        this.searchFormModel['createEndTime'] = XEUtils.timestamp(endDate)
      } else {
        this.searchFormModel['createStartTime'] = null
        this.searchFormModel['createEndTime'] = null
      }
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    // 查询
    async handleSearch(type) {
      // 解决修改查询条件后，查询结果有数据但未正常显示的问题（type: pageChange/refresh）
      if (!type) {
        this.pageInfo.current = 1
        this.$refs.pageRef.jumpNum = 1
        this.$refs.pageRef.currentPage = 1
      }
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      delete params.submitTime
      this.loading = true
      const res = await this.$API.costCalculation
        .queryCostCalculationList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.tableData?.forEach((item, index) => {
          item.index = index + 1
        }) || []
        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (['delete', 'batchSubmit'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'export':
          this.handleExport()
          break
        case 'delete': // 删除
        case 'batchSubmit': // 提交
          this.handleOperate(selectedRecords, e.code)
          break
        default:
          break
      }
    },
    handleExport() {
      const params = { page: { size: 9999, current: 1 }, ...this.searchFormModel } // 筛选条件
      this.$API.costCalculation.exportCostCalculationList(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 点击单元格
    handleClickCellTitle(row, column) {
      if (column.field === 'rfxCode') {
        this.$router.push({
          name: 'cost-calculation-detail',
          query: {
            type: 'edit',
            id: row.id,
            refreshId: Date.now()
          }
        })
      } else if (column.field === 'oaApproveLink') {
        window.open(row.oaApproveLink)
      }
    },
    // 新增
    handleAdd() {
      this.$router.push({
        name: 'cost-calculation-detail',
        query: {
          type: 'create',
          refreshId: Date.now()
        }
      })
    },
    // 删除、提交
    handleOperate(list, type) {
      const tipMap = {
        delete: this.$t('删除'),
        batchSubmit: this.$t('提交')
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认${tipMap[type]}选中的数据？`)
        },
        success: async () => {
          const ids = []
          list.forEach((item) => ids.push(item.id))
          const res = await this.$API.costCalculation[type + 'CostCalculation']({ ids })
          if (res.code === 200) {
            this.$toast({ content: this.$t(`${tipMap[type]}成功！`), type: 'success' })
            this.handleSearch()
          }
        }
      })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.handleSearch('pageChange')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.handleSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
