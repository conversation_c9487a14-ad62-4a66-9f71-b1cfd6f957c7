import { i18n } from '@/main'
import { formatTime } from '@/utils/utils'
import { PRICE_EDIT_CONFIG } from '@/constants/editConfig'
// 报价属性
const quoteAttrMap = {
  mailing_price: i18n.t('寄售价'),
  standard_price: i18n.t('标准价'),
  outsource: i18n.t('委外价')
}
// 报价生效方式
const priceEffectiveMethodMap = {
  in_warehouse: i18n.t('按照入库'),
  out_warehouse: i18n.t('按出库'),
  order_date: i18n.t('按订单日期')
}

//详情 - 物料明细 - 列表字段
export const itemColumns = (args) => [
  { option: 'checkboxSelection', width: 55 },
  {
    field: 'factoryCode',
    headerName: i18n.t('工厂'),
    width: 170,
    valueFormatter: (params) => {
      return params.data.factoryCode + '-' + params.data.factoryName
    }
  },
  {
    field: 'materialCode',
    headerName: i18n.t('物料编码'),
    width: 155
  },
  {
    field: 'materialName',
    headerName: i18n.t('物料描述')
  },
  {
    field: 'categoryCode',
    headerName: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    headerName: i18n.t('品类名称')
  },

  {
    field: 'costModelName',
    headerName: i18n.t('成本模型'),
    width: 120,
    editable: true,
    option: 'customEdit',
    editConfig: {
      type: 'cellCost',
      props: {
        rfxId: args.rfxId,
        detailInfo: args.detailInfo,
        isKT: true
      }
    },
    cellClass: 'cellEditable'
  },
  {
    field: 'costEstimation',
    headerName: i18n.t('成本测算'),
    cellRenderer: 'cellLink',
    cellRendererParams: {
      handleable: true //“可操作”（不可编辑），取editable避免从历史报价进入成本分析页面
    }
  },

  {
    field: 'unitPriceUntaxed',
    headerName: i18n.t('单价（未税）'),
    width: 115
  },
  {
    field: 'unitPriceTaxed',
    headerName: i18n.t('单价（含税）'),
    width: 115
  },
  {
    field: 'taxRateName',
    headerName: i18n.t('税率名称'),
    width: 170,
    valueFormatter: (params) => {
      return params.data.taxRateCode + '-' + params.data.taxRateName
    }
  },
  {
    field: 'taxRateValue',
    headerName: i18n.t('税率值'),
    width: 90
  },

  // {
  //   field: 'unitCode',
  //   headerName: i18n.t('单位编码'),
  //   width: 90
  // },
  {
    field: 'basicUnit',
    headerName: i18n.t('基本单位'),
    width: 150,
    valueFormatter: (params) => {
      return params.data.basicUnit + '-' + params.data.basicUnitName
    }
  },
  {
    field: 'purchaseUnitCode',
    headerName: i18n.t('订单单位'),
    width: 150,
    valueFormatter: (params) => {
      return params.data.purchaseUnitCode + '-' + params.data.purchaseUnitName
    }
  },
  {
    field: 'quoteAttr',
    headerName: i18n.t('报价属性'),
    width: 90,
    valueFormatter: (params) => {
      return quoteAttrMap[params.value]
    }
  },
  {
    field: 'priceEffectiveMethod',
    headerName: i18n.t('报价生效方式'),
    width: 115,
    valueFormatter: (params) => {
      return priceEffectiveMethodMap[params.value]
    }
  },
  {
    field: 'priceUnitName',
    headerName: i18n.t('价格单位'),
    width: 90,
    editable: true,
    option: 'customEdit',
    editConfig: {
      type: 'select',
      props: {
        'show-clear-button': true,
        placeholder: i18n.t('价格单位'),
        dataSource: [
          { text: '1', value: '1' },
          { text: '1000', value: '1000' }
        ]
      }
    },
    cellClass: 'cellEditable'
  },
  {
    field: 'directDeliverAddr',
    headerName: i18n.t('直送地'),
    width: 120,
    editable: true,
    option: 'customEdit',
    editConfig: {
      type: 'select',
      props: {
        'show-clear-button': true,
        fields: { value: 'dictName', text: 'dictName' },
        placeholder: i18n.t('选择直送地'),
        dataSource: args.dictItems['DELIVERY_PLACE']
      }
    },
    cellClass: 'cellEditable'
  },
  {
    field: 'purchaseGroupCode',
    headerName: i18n.t('采购组'),
    editable: true,
    option: 'customEdit',
    editConfig: {
      type: 'select',
      props: {
        dataSource: args?.purGroupList,
        placeholder: i18n.t('请选择'),
        'allow-filtering': true,
        'filter-type': 'Contains'
      }
    },
    cellClass: 'cellEditable'
  },
  {
    field: 'leadTime',
    headerName: i18n.t('计划交货时间'),
    width: 120,
    editable: true,
    option: 'customEdit',
    editConfig: {
      type: 'number',
      props: {
        ...PRICE_EDIT_CONFIG
      }
    },
    cellClass: 'cellEditable'
  },
  {
    field: 'validStartTime',
    headerName: i18n.t('生效日期'),
    width: 130,
    valueFormatter: (params) => {
      return params.value ? formatTime(new Date(params.value), 'Y-mm-dd') : ''
    },
    option: 'customEdit',
    editable: true,
    editConfig: {
      type: 'date',
      props: {
        format: 'yyyy-MM-dd',
        'time-stamp': true,
        'show-clear-button': false
      }
    },
    cellClass: 'cellEditable'
  },
  {
    field: 'validEndTime',
    headerName: i18n.t('失效日期'),
    width: 130,
    editable: true,
    valueFormatter: (params) => {
      return params.value ? formatTime(new Date(params.value), 'Y-mm-dd') : ''
    },
    option: 'customEdit',
    editConfig: {
      type: 'date',
      props: {
        format: 'yyyy-MM-dd',
        'time-stamp': true,
        'show-clear-button': false
      }
    },
    cellClass: 'cellEditable'
  },
  {
    field: 'remark',
    headerName: i18n.t('备注'),
    width: 300,
    editable: true,
    cellClass: 'cellEditable'
  },
  {
    field: 'errorMsg',
    headerName: i18n.t('错误信息'),
    width: 300,
    tooltipValueGetter: (params) => {
      return params.value
    }
  }
]

//详情 - 物料明细 - 操作按钮
export const itemToolbar = (status) => [
  {
    id: 'add',
    title: i18n.t('新增'),
    hide: ![0, 3, 7, 8].includes(status) //草稿、审批驳回、审批废弃、审批撤回
  },
  {
    id: 'save',
    title: i18n.t('保存'),
    hide: ![0, 3, 7, 8].includes(status) //草稿、审批驳回、审批废弃、审批撤回
  },
  {
    id: 'delete',
    title: i18n.t('删除'),
    hide: ![0, 3, 7, 8].includes(status) //草稿、审批驳回、审批废弃、审批撤回
  },
  // {
  //   id: 'import',
  //   title: i18n.t('导入'),
  //   hide: ![0, 3, 7, 8].includes(status) //草稿、审批驳回、审批废弃、审批撤回
  // },
  {
    id: 'export',
    title: i18n.t('导出')
  }
]
