import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '160',
    field: 'organizationCode',
    headerText: i18n.t('工厂'),
    valueAccessor: (field, data) => {
      return data.organizationCode + '-' + data.organizationName
    }
  },
  {
    width: '140',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '140',
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    width: '150',
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]
