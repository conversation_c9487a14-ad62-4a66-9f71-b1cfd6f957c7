<template>
  <div class="flex-full-height">
    <CustomAgGrid
      ref="CustomAgGrid"
      :search-config="searchConfig"
      :toolbar="toolbar"
      :columns="columns"
      :suppress-row-transform="true"
      :row-data="tableData"
      :animate-rows="false"
      :is-pagination="true"
      :get-row-id="getRowId"
      :row-class-rules="rowClassRules"
      :params-type="2"
      :is-stop-editing-on-blur="false"
      @handleClickToolbar="handleClickToolbar"
      @onGridReady="onGridReady"
      @row-selected="onRowSelected"
      @cell-value-changed="cellValueChanged"
      @refresh="refresh"
      @search="search"
    >
    </CustomAgGrid>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
    <!-- 失效时间设置 -->
    <mt-dialog ref="dialog" :buttons="buttons" :header="$t('失效时间设置')" size="small">
      <div class="dialog-content">
        <mt-form style="margin-top: 20px">
          <mt-form-item prop="validEndDate" :label="$t('失效时间：')">
            <mt-date-picker
              v-model="validEndDate"
              float-label-type="Never"
              width="100%"
              :placeholder="$t('请选择失效时间')"
            ></mt-date-picker>
          </mt-form-item>
        </mt-form>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import CustomAgGrid from '@/components/CustomAgGrid'
import { itemColumns, itemToolbar } from './config/item'
import { download, getHeadersFileName } from '@/utils/utils'
import cellLink from './cellLink'

export default {
  name: 'ItemTab',
  components: {
    CustomAgGrid,
    // eslint-disable-next-line
    cellLink
  },

  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 500,
        pageSizes: [20, 50, 200, 500, 1000, 2000]
      },
      pageInfo: {
        size: 500,
        current: 1
      },
      tableData: [],
      purGroupList: [],
      columns: [],
      toolbar: [],
      searchConfig: [],
      type: 'material',
      validEndDate: '', //更新后失效时间设置
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      dictItems: {}
    }
  },
  computed: {
    editConfig() {
      return {
        enabled: this.$route.query.id && [0, 3, 5, 9].includes(this.dataInfo.status),
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    rowClassRules() {
      return {
        errorBg: function (params) {
          return params.data.errorMsg
        }
      }
    }
  },
  watch: {
    'dataInfo.status': {
      handler(v) {
        if (v || v === 0) {
          this.toolbar = itemToolbar(this.dataInfo.status)
        }
      },
      deep: true,
      immediate: true
    },
    'tableData.length': {
      handler(v) {
        if (v || v === 0) {
          this.$emit('itemDataChange', v)
        }
      },
      deep: true
    },
    $route(to, from) {
      if (from.name === 'calculation') {
        // 由成本分析页面返回，则刷新当前页面
        this.refresh()
      }
    }
  },
  async mounted() {
    if (this.$route.query.type === 'edit') {
      this.initSearchConfig()
      await this.getPurGroupList()
      await this.initDictItems()
      this.initGridColumns()
      this.initTableData()
    }
  },
  methods: {
    /**
     * ---------------------------ag相关---------------------------
     */
    // ag - 初始监听
    onGridReady(params) {
      this.agGrid = params
    },
    // ag - 监听select选择框
    onRowSelected(e) {
      if (!this.dataInfo.stepQuote) return
      if (!e.data.isFirstLine) return
      let isSelected = e.node.selected
      let itemGroupId = e.data.itemGroupId
      //如果勾选的是阶梯第一行数据,同步其他行
      this.agGrid.api.forEachNode((node) => {
        if (node.data.itemGroupId === itemGroupId && !node.data.isFirstLine) {
          node.setSelected(isSelected)
        }
      })
    },
    // ag - 编辑框监听
    cellValueChanged(params) {
      if (params.oldValue === params.newValue) return
    },
    // ag - 获取rowId
    getRowId(params) {
      return params.data.id ? params.data.id : params.data?.customId
    },
    getRowClass(params) {
      if (params.data?.errorMsg) return 'errorBg'
      return ''
    },
    /**
     * ---------------------------初始化---------------------------
     */
    // 初始化列表
    initGridColumns() {
      let _columns = itemColumns({
        rfxId: this.$route.query.id,
        purGroupList: this.purGroupList,
        dictItems: this.dictItems,
        detailInfo: this.dataInfo
      })
      this.columns = _columns
    },
    // 初始化查询条件
    initSearchConfig() {
      let _config = [
        {
          field: 'materialCode',
          label: '物料编码'
        },
        {
          field: 'materialName',
          label: '物料描述'
        }
      ]
      this.searchConfig = _config
    },
    // 初始化获取数据
    async initTableData(params) {
      const _params = {
        rfxId: this.$route.query.id,
        page: this.pageInfo,
        ...params
      }
      const res = await this.$API.costCalculation.costItemPageQuery(_params)
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // btn - 刷新
    refresh() {
      this.initTableData()
    },
    // btn - 查询
    search(params) {
      this.initTableData(params)
    },
    /**
     * ---------------------------事件操作---------------------------
     */
    // 点击按钮栏
    handleClickToolbar(e) {
      const selectedRows = this.agGrid.api.getSelectedRows()
      switch (e?.toolbar?.id) {
        case 'add':
          this.handleAdd()
          break
        case 'save':
          this.handleSave()
          break
        case 'delete':
          this.handleDelete(selectedRows)
          break
        // case 'import':
        //   this.handleImport()
        //   break
        case 'export':
          this.handleExport()
          break
        case 'setting':
          this.handleSetting()
          break
        default:
          break
      }
    },
    // 新增
    handleAdd() {
      this.$dialog({
        modal: () => import('./priceRecord'),
        data: {
          title: this.$t('物料'),
          rfxId: this.$route.query.id,
          tableData: this.tableData,
          companyCode: this.dataInfo.companyCode,
          purchaseOrgCode: this.dataInfo.purchaseOrgCode,
          supplierCode: this.dataInfo.supplierCode
        },
        success: (list) => {
          this.tableData = [...list, ...this.tableData]
          // 添加后调用保存接口
          // this.handleSave()
          let _total = this.tableData.length
          this.pageSettings.totalPages = Math.ceil(Number(_total) / this.pageSettings.pageSize)
          this.pageSettings.totalRecordsCount = Number(_total)
        }
      })
    },
    // 表格 - 工具 - 获取表格数据
    getRowData() {
      const rowData = []
      this.agGrid.api.forEachNode(function (node) {
        rowData.push(node.data)
      })
      return rowData
    },
    // 保存
    async handleSave() {
      this.agGrid.api.stopEditing()
      let itemList = this.getRowData()
      itemList?.forEach((item) => {
        item.rfxId = this.$route.query.id
        item.optType = item.id ? 'modify' : 'add'
      })
      const params = {
        rfxId: this.$route.query.id,
        itemList
      }
      this.$store.commit('startLoading')
      const res = await this.$API.costCalculation.saveCostItem(params)
      this.$store.commit('endLoading')
      if (res.code === 200) {
        this.$toast({ content: this.$t('保存成功！'), type: 'success' })
        this.initTableData()
      }
    },
    // 删除
    handleDelete(list) {
      if (!list.length) {
        this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
        return
      }
      let itemIdList = []
      let localItemIdList = []
      list.forEach((item) => {
        if (item.id) {
          itemIdList.push(item.id)
        } else {
          localItemIdList.push(item.customId)
        }
      })
      // 本地删除
      if (localItemIdList.length > 0) {
        this.tableData = this.tableData.filter((item) => !localItemIdList.includes(item.customId))
      }
      if (!itemIdList.length) return
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: async () => {
          this.$store.commit('startLoading')
          const params = {
            rfxId: this.$route.query.id,
            itemIdList
          }
          const res = await this.$API.costCalculation.deleteCostItem(params)
          this.$store.commit('endLoading')
          if (res.code === 200) {
            this.$toast({ content: this.$t('删除成功！'), type: 'success' })
            this.initTableData()
          }
        }
      })
    },
    // 导入
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('上传/导入'),
          importApi: this.$API.costCalculation.importCostItem,
          downloadTemplateApi: this.$API.costCalculation.downloadImportTemplate,
          asyncParams: {
            rfxId: this.$route.query.id
          }
        },
        success: () => {
          // 导入之后刷新列表
          this.initTableData()
        }
      })
    },
    // 导出
    async handleExport() {
      const params = {
        rfxId: this.$route.query.id,
        page: this.pageInfo
      }
      const res = await this.$API.costCalculation.exportCostItem(params)
      if (res.data) {
        this.$toast({
          type: 'success',
          content: this.$t('正在导出，请稍后！')
        })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      }
    },
    // 失效事件设置
    handleSetting() {
      if (!this.tableData.length) {
        this.$toast({ content: this.$t('暂无数据'), type: 'warning' })
        return
      }
      this.$refs.dialog.ejsRef.show()
    },
    // dialog - cancel
    cancel() {
      this.$refs.dialog.ejsRef.hide()
    },
    // dialog - confirm
    confirm() {
      // 保存数据
      this.handleSave()
      this.$refs.dialog.ejsRef.hide()
    },
    /**
     * ---------------------------页码---------------------------
     */
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.initTableData()
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.initTableData()
    },
    /**
     * ---------------------------工具---------------------------
     */

    // 获取采购组
    async getPurGroupList() {
      const res = await this.$API.masterData.getbussinessGroup({
        groupTypeCode: 'BG001CG'
      })
      if (res.code === 200) {
        this.purGroupList = res.data.map(({ groupCode, groupName, id }) => ({
          value: groupCode,
          text: `${groupCode}-${groupName}`,
          id
        }))
      }
    },
    // 初始化字典数据
    async initDictItems() {
      let codeList = [{ code: 'DELIVERY_PLACE', type: 'string' }]
      await this.$API.masterData.getNumDictListAllByCode(codeList).then((res) => {
        if (res.code === 200) {
          this.dictItems = res.data
        }
      })
    }
  }
}
</script>
<style lang="scss">
.ag-theme-alpine .errorBg,
.ag-theme-alpine .errorBg .mergeCell {
  background: rgb(253, 137, 137) !important;
}
</style>
<style lang="scss" scoped>
.mt-pagertemplate {
  margin-bottom: 0px !important;
}
</style>
