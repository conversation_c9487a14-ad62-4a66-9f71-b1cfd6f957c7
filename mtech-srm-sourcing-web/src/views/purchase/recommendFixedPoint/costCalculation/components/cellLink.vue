<template>
  <!-- 用作单元格点击跳转，后续扩展 -->
  <div class="cell-link">
    <span :class="{ hoverClass: queryParams.handleable }" @click="handleClick">{{
      $t('成本测算')
    }}</span>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
  computed: {
    queryParams() {
      // 字段
      return this.params.colDef?.cellRendererParams || {}
    },
    field() {
      return this.params.colDef?.field
    }
  },
  methods: {
    // handler - 点击跳转
    handleClick() {
      if (!this.queryParams?.handleable) return

      if (this.queryParams.isAutoSave) {
        this.params.context.componentParent.handleSave(true)
        this.confirm()
        return
      }
      this.confirm()
    },
    confirm() {
      // costModelName、costEstimation  成本模型、成本测算
      if (!this.params.data?.id) {
        this.$toast({
          content: this.$t('请保存后再测算'),
          type: 'warning'
        })
        return
      }
      this.$router.push({
        name: 'calculation',
        query: {
          rfxId: this.$route.query.id,
          rfxItemId: this.params.data?.id,
          type: this.field === 'costModelName' ? 'costModel' : 'calculation',
          refreshId: Date.now()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.cell-link span {
  color: #00469c;
  cursor: pointer;
}
.hoverClass:hover {
  text-decoration: underline;
}
</style>
