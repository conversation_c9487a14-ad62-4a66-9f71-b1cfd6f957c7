<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-local-template-page ref="templateRef" :template-config="pageConfig">
        <template v-slot:quick-search-form>
          <div class="custom-form-box">
            <mt-form ref="searchFormRef" :model="searchFormModel">
              <mt-form-item prop="materialCode" :label="$t('物料编码')">
                <mt-input v-model="searchFormModel.materialCode" show-clear-button></mt-input>
              </mt-form-item>
              <mt-form-item prop="categoryCode" :label="$t('品类编码')">
                <mt-input v-model="searchFormModel.categoryCode" show-clear-button></mt-input>
              </mt-form-item>
            </mt-form>
          </div>
        </template>
      </mt-local-template-page>
    </div>
  </mt-dialog>
</template>

<script>
import MtLocalTemplatePage from '@/components/template-page'
import { columnData } from './config/priceRecord'
export default {
  components: {
    MtLocalTemplatePage
  },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      pageConfig: [],
      searchFormModel: {},
      data: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.modalData.isShow ? [] : this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.initData()
  },
  methods: {
    // 时间选择
    handleValidTime() {},
    initData() {
      this.pageConfig = [
        {
          useToolTemplate: false,
          toolbar: [],
          gridId: 'b9674431-2077-4ddb-ba61-7f365fcce0e8',
          showSelected: true,
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          grid: {
            allowFiltering: true,
            columnData: columnData,
            allowSorting: false,
            allowSelection: true,
            selectionSettings: {
              type: 'Single',
              mode: 'Row'
            },
            dataSource: [],
            asyncConfig: {
              url: '/sourcing/tenant/kt/cost-model/material-paged-query',
              params: {
                rfxId: this.modalData.rfxId
              }
            },
            queryCellInfo: this.queryCellInfo,
            recordDoubleClick: this.recordDoubleClick
          }
        }
      ]
    },
    recordDoubleClick(e) {
      this.confirm([e.rowData])
    },
    async confirm(records) {
      let _records = records?.length
        ? records
        : this.$refs.templateRef.getCurrentTabRef().gridRef.getMtechGridRecords()
      if (_records.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      // const itemCodes = _records.map((item) => item.itemCode)
      // const dataRecords = await this.queryPriceRecord(itemCodes) // 获取税率、价格单位等信息
      const dataDefault = await this.queryDefaultValue(_records[0].categoryCode) // 获取报价属性、价格生效方式
      const dataCostModel = await this.queryCostModel(_records[0]) // 查询成本模型, 默认渲染第一项
      const dataPurGroup = await this.getPurGroup(_records[0]) // 查询采购组
      const dataLT = await this.getPlanTime(_records[0]) // 查询L/T事件
      const costObj = {
        costModelId: dataCostModel[0]?.id,
        costModelCode: dataCostModel[0]?.costModelCode,
        costModelName: dataCostModel[0]?.costModelName,
        costModelVersionCode: dataCostModel[0]?.versionCode
      }
      const purObj = {
        purchaseGroupCode: dataPurGroup.purchaseGroupCode,
        purchaseGroupId: dataPurGroup.purchaseGroupId,
        purchaseGroupName: dataPurGroup.purchaseGroupName
      }
      let data = {
        factoryCode: _records[0].organizationCode,
        factoryName: _records[0].organizationName,
        materialCode: _records[0].itemCode,
        materialName: _records[0].itemName,
        categoryCode: _records[0].categoryCode,
        categoryName: _records[0].categoryName,
        basicUnit: _records[0].baseMeasureUnitCode,
        basicUnitName: _records[0].baseMeasureUnitName,
        purchaseUnitCode: _records[0].baseMeasureUnitCode,
        purchaseUnitName: _records[0].baseMeasureUnitName,
        quoteAttr: dataDefault.quoteAttr,
        priceEffectiveMethod: dataDefault.priceEffectiveMethod,
        currencyCode: dataDefault.currencyCode,
        currencyName: dataDefault.currencyName,
        directDeliverAddr: '中山',
        taxRateCode: dataDefault.taxRateCode,
        taxRateName: dataDefault.taxRateName,
        taxRateValue: dataDefault.taxRateValue,
        priceUnitName: _records[0].priceUnitName || '1',
        unitCode: dataDefault.unitCode,
        leadTime: dataLT,
        ...purObj,
        ...costObj
      }
      this.$emit('confirm-function', [data])
    },
    // 查询物料信息
    async queryPriceRecord(itemCodes) {
      const _params = {
        materialCodeList: itemCodes,
        rfxId: this.modalData.rfxId
      }
      const res = await this.$API.costCalculation.queryLatestPriceRecord(_params)
      return res.code === 200 ? res.data : []
    },
    // 查询报价属性、价格生效方式
    async queryDefaultValue(categoryCode) {
      const { companyCode, purchaseOrgCode, supplierCode } = this.modalData
      const _params = {
        categoryCode,
        companyCode,
        purchaseOrgCode,
        supplierCode
      }
      const res = await this.$API.costCalculation.queryDefaultValue(_params)
      return res.code === 200 ? res.data : {}
    },
    // 查询成本模型
    async queryCostModel(data) {
      let _params = {
        rfxId: this.modalData.rfxId,
        categoryCode: data.categoryCode || '',
        companyCodeList: [this.modalData.companyCode || ''],
        relCode: data.itemCode || data.categoryCode,
        relationType: 1, // 品类:0、物料:1、行情因子:2
        page: { current: 1, size: 20 }
      }
      const res = await this.$API.costModel.queryRelKtCostModel(_params)
      return res.code === 200 ? res.data?.records : []
    },
    // 获取LT
    async getPlanTime(data) {
      let _params = {
        materialCode: data.itemCode || '',
        factoryCode: data.organizationCode
      }
      const res = await this.$API.costCalculation.getPlanTime(_params)
      return res.code === 200 ? res.data : []
    },
    // 获取采购组
    async getPurGroup(data) {
      let _params = {
        itemCode: data.itemCode || '',
        organizationCode: data.organizationCode
      }
      const res = await this.$API.masterData.getPurGroup(_params)
      return res.code === 200 ? res.data : []
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
