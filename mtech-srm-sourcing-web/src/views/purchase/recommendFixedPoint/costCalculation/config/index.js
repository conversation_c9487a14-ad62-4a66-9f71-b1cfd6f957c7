import { i18n } from '@/main'

// 状态列表
export const statusList = [
  { value: -1, text: i18n.t('已作废') },
  { value: 0, text: i18n.t('草稿') },
  { value: 1, text: i18n.t('审批中') },
  { value: 2, text: i18n.t('审批通过') },
  { value: 3, text: i18n.t('审批驳回') },
  // { value: 4, text: i18n.t('同步中') },
  // { value: 5, text: i18n.t('同步成功') },
  // { value: 6, text: i18n.t('同步失败') },
  { value: 7, text: i18n.t('审批废弃') },
  { value: 8, text: i18n.t('审批撤回') }
]
// 定价类型
export const fixPriceTypeList = [
  // { text: i18n.t('新品'), value: '0' }, //new_products
  // { text: i18n.t('二次'), value: '1' }, //second_inquiry
  { text: i18n.t('已有'), value: '2' } //exist
]
// 价格类型
export const priceClassifyList = [
  { value: '3', text: i18n.t('暂估价格') },
  { value: '4', text: i18n.t('执行价格') }
]

// 单据来源
export const sourceList = [{ value: '0', text: i18n.t('手动创建') }]

// 查询列表-操作按钮
export const listToolbar = [
  { code: 'add', name: i18n.t('新增'), status: 'info' },
  { code: 'delete', name: i18n.t('删除'), status: 'info' },
  { code: 'batchSubmit', name: i18n.t('提交'), status: 'info' },
  { code: 'export', name: i18n.t('导出'), status: 'info' }
]
