<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { detailToolBar, averageDetailColumnData } from './config'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  data() {
    return {
      downTemplateParams: {},
      pageConfig: [
        {
          toolbar: detailToolBar,
          useToolTemplate: false,
          gridId: 'd5ab0ae7-32e7-4722-8ef2-43c92efee7bb',
          grid: {
            allowFiltering: false,
            asyncConfig: {
              ignoreDefaultSearch: true,
              url: this.$API.rfxList.pagePointDetailItem,
              afterAsyncData: this.updateTabTitle,
              defaultRules: [
                {
                  field: 'point.decidePriceType',
                  operator: 'in',
                  value: ['4']
                },
                {
                  label: this.$t('状态'),
                  field: 'point.status',
                  type: 'string',
                  operator: 'equal',
                  value: '2'
                }
              ]
            },
            lineIndex: true,
            columnData: averageDetailColumnData,
            dataSource: []
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    //单元格标题点击操作
    handleClickCellTitle(e) {
      if (e.field == 'pointNo') {
        this.$router.push({
          name: 'fixed-point',
          query: {
            id: e.data.pointId,
            decidePriceType: e.data.decidePriceType,
            status: e.data.status,
            key: this.$utils.randomString()
          }
        })
      }
    },
    //表格按钮-点击事件
    handleClickToolBar(e) {
      if (e.toolbar.id === 'export') {
        this.handleExport()
      }
    },
    //导出
    handleExport() {
      this.$store.commit('startLoading')
      // 先要点击“查询”按钮才能取得queryBuilderRules
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 500000 },
        ...queryBuilderRules,
        defaultRules: [
          {
            field: 'point.decidePriceType',
            operator: 'equal',
            value: 4
          },
          {
            label: this.$t('状态'),
            field: 'point.status',
            type: 'string',
            operator: 'equal',
            value: '2'
          }
        ]
      } // 筛选条件
      this.$API.rfxList
        .basicAndAverageExport(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
          this.$store.commit('endLoading')
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    }
  }
}
</script>
