<template>
  <div class="full-height">
    <div class="main-form">
      <mt-form ref="ruleForm" :model="forObject" :rules="rules">
        <mt-form-item
          prop="companyCode"
          :title="forObject.companyCode"
          :label="$t('公司')"
          label-style="left"
          label-width="50px"
        >
          <!-- <supDialog
            v-model="forObject.companyCode"
            :async-config-url="supplierUrl"
          ></supDialog> -->
          <mt-multi-select
            :allow-filtering="true"
            filter-type="Contains"
            :data-source="companyList"
            v-model="forObject.companyCode"
            :show-clear-button="true"
            :fields="{ value: 'orgCode', text: 'text' }"
            :placeholder="$t('请选择')"
            @change="companyChange"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item
          prop="supplierCode"
          :label="$t('供应商名称')"
          label-style="left"
          label-width="80px"
        >
          <!-- <mt-select
            :width="300"
            :data-source="[
              { text: $t('直接定价'), value: 0 },
              { text: $t('寻源结果定价'), value: 1 },
              { text: $t('基价定价'), value: 3 },
              { text: $t('均价定价'), value: 4 },
              { text: $t('执行价定价'), value: 5 },
            ]"
            v-model="forObject.pricingType"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
          ></mt-select> -->
          <supDialog
            class="select-width"
            v-model="forObject.supplierCode"
            :async-config-url="supplierUrl"
            @change="supplierCodeChange"
          ></supDialog>
        </mt-form-item>
        <mt-form-item
          ref="state"
          prop="categoryCodeList"
          :label="$t('品类名称')"
          label-style="left"
          label-width="65px"
          :title="forObject.categoryCodeList"
        >
          <mt-multi-select
            :allow-filtering="true"
            filter-type="Contains"
            :data-source="categoryList"
            v-model="forObject.categoryCodeList"
            :show-clear-button="true"
            :fields="{ value: 'categoryCode', text: 'text' }"
            :placeholder="catePlaceholder"
          ></mt-multi-select>
          <!-- <cateDialog
            class="select-width"
            v-model="forObject.categoryCode"
            :async-config-url="cateUrl"
            :params-data="forObject"
          ></cateDialog> -->
        </mt-form-item>
        <mt-form-item
          ref="state"
          prop="dateRange"
          :label="$t('有效期区间')"
          label-style="left"
          label-width="80px"
        >
          <mt-date-range-picker
            :show-clear-button="true"
            :allow-edit="false"
            v-model="forObject.dateRange"
            :placeholder="$t('有效期从-有效期至')"
          ></mt-date-range-picker>
        </mt-form-item>
      </mt-form>
    </div>
    <div class="grid-height">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
      />
    </div>
    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadInitBalanceRef"
      :down-template-name="downTemplateName"
      :request-urls="requestUrls"
      :down-template-params="downTemplateParams"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import { strikeToolBar, strikeColumnData } from './config'
import supDialog from './components/editComponents/dialog.vue'
// import cateDialog from './components/editComponents/cateDialog.vue'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {
    supDialog,
    // cateDialog,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  data() {
    return {
      catePlaceholder: this.$t('请先选择公司和供应商'),
      categoryList: [],
      requestUrls: {},
      downTemplateName: this.$t('导入模板'),
      downTemplateParams: {},
      companyList: [],
      cateUrl: '/masterDataManagement/tenant/supply/source/query-category',
      supplierUrl: '/masterDataManagement/tenant/supplier/paged-query-distinct',
      forObject: {
        companyCode: null,
        supplierCode: null,
        categoryCodeList: null,
        dateRange: null
      },
      pageConfig: [
        {
          // title: this.$t("执行价定价"),
          toolbar: strikeToolBar,
          useToolTemplate: false,
          gridId: '1cf9e06b-ae64-4830-a3ad-1df0e7deea1c',
          grid: {
            allowFiltering: true,
            asyncConfig: {
              url: this.$API.rfxList.getPagePoint,
              afterAsyncData: this.updateTabTitle,
              defaultRules: [
                {
                  field: 'decidePriceType',
                  operator: 'in',
                  value: ['5']
                }
              ]
            },
            lineIndex: true,
            columnData: strikeColumnData,
            dataSource: []
          }
        }
      ],
      rules: {
        companyCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        supplierCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        categoryCodeList: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      }
    }
  },
  mounted() {
    this.getCompany()
  },
  methods: {
    companyChange(e) {
      if (e?.value?.length == 0) {
        this.forObject.categoryCodeList = null
        this.catePlaceholder = this.$t('请先选择公司和供应商')
      } else if (e.value && this.forObject.supplierCode) {
        this.forObject.categoryCodeList = null
        this.forObject.companyCode = e.value
        this.getCateList()
        this.catePlaceholder = ''
      }
    },
    supplierCodeChange(e) {
      this.forObject.supplierCode = e.supplierCode
      if (this.forObject?.companyCode) {
        this.forObject.categoryCodeList = null
        this.getCateList()
        this.catePlaceholder = ''
      }
    },
    // 上传（显示弹窗）
    handleUpload() {
      this.requestUrls = {
        templateUrlPre: 'rfxList',
        templateUrl: 'getStrikeTemplate',
        uploadUrl: 'importStrikePrice',
        rfxId: '0'
      }
      this.showUploadExcel(true)
    },
    // 展示/不展示 上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadInitBalanceRef.uploadData = [] // 清空数据
        this.$refs.uploadInitBalanceRef.$refs.uploader.files = []
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.$refs.templateRef.refreshCurrentGridData()
    },
    updateTabTitle() {},
    getCompany() {
      this.$API.masterData.permissionCompanyList().then((res) => {
        this.companyList.length = 0
        res.data.forEach((item) => {
          this.companyList.push({ ...item, text: `${item.orgCode}-${item.orgName}` })
        })
      })
    },
    getCateList() {
      this.$API.masterData
        .queryCateByCompSup({
          organizationCodes: this.forObject.companyCode,
          supplierCodes: [this.forObject.supplierCode]
        })
        .then((res) => {
          let _categoryList = []
          setTimeout(() => {
            res.data.forEach((item) => {
              _categoryList.push({ ...item, text: `${item.categoryCode}-${item.categoryName}` })
            })
            this.categoryList = _categoryList
          }, 1)
        })
    },
    //新增
    handleAddFn(decidePriceType) {
      this.$dialog({
        modal: () => import('./newAddComponents/index.vue'),
        data: {
          title: this.$t('创建执行价定价'),
          decidePriceType
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    //删除
    handleDelFn(selectGridRecords, idList) {
      if (selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            let params = {
              idList: idList
            }
            this.$API.rfxList.deletePointList(params).then(() => {
              this.$toast({ content: this.$t('删除成功'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
            })
          }
        })
      }
    },

    //提交
    handleSubmitFn(selectGridRecords) {
      if (selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else if (selectGridRecords.length > 1) {
        this.$toast({ content: this.$t('只选择一行提交'), type: 'warning' })
      } else {
        if (
          selectGridRecords[0].status < 1 ||
          selectGridRecords[0].status == 3 ||
          selectGridRecords[0].status == 7 ||
          selectGridRecords[0].status == 8
        ) {
          let params = {
            id: selectGridRecords[0].id
          }
          this.$store.commit('startLoading')
          this.$API.rfxList
            .submitPricing(params)
            .then(() => {
              this.$store.commit('endLoading')
              this.$toast({ content: this.$t('提交成功'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
            })
            .catch(() => {
              this.$store.commit('endLoading')
            })
        } else {
          this.$toast({
            content: this.$t('此状态下无法提交'),
            type: 'warning'
          })
        }
      }
    },

    //表格按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      let idList = []
      _selectGridRecords.map((item) => {
        idList.push(item.id)
      })
      let _decidePriceType = 5
      if (e.toolbar.id == 'Add') {
        this.handleAddFn(_decidePriceType) //新增
      } else if (e.toolbar.id == 'del') {
        for (let item of _selectGridRecords) {
          if (item.status != 0 && item.status != 7 && item.status != 8) {
            this.$toast({
              content: this.$t('只能删除草稿、撤回、废弃状态的单据'),
              type: 'warning'
            })
            return
          }
        }
        this.handleDelFn(_selectGridRecords, idList) //删除
      } else if (e.toolbar.id == 'getData') {
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            this.$store.commit('startLoading')
            this.saveExercisePrice({
              ...this.forObject,
              companyCodeList: this.forObject.companyCode
            })
          }
        })
      } else if (e.toolbar.id == 'uploadSap') {
        for (let i = 0; i < _selectGridRecords.length; i++) {
          if (![0, '0', 1, '1'].includes(_selectGridRecords[i].status)) {
            this.$toast({
              type: 'warning',
              content: this.$t('推送SAP, 只能操作状态为: "草稿" 或 "审批中"的数据')
            })
            return
          }
        }
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('是否确认推送SAP')
          },
          success: () => {
            this.$store.commit('startLoading')
            this.uploadSap(_selectGridRecords)
          }
        })
      } else if (e.toolbar.id == 'import') {
        this.handleUpload()
      } else if (e.toolbar.id === 'Submit') {
        this.handleSubmitFn(_selectGridRecords) //提交
      } else if (e.toolbar.id === 'export') {
        this.handleExport(e)
      }
    },
    // 推送SAP
    uploadSap(idList = []) {
      const _ldList = idList.map((item) => item.id).filter((n) => !!n)
      this.$API.rfxList
        .sapUpload({ idList: _ldList })
        .then((res) => {
          if (res.code == 200) {
            this.$store.commit('endLoading')
            this.$refs.templateRef.refreshCurrentGridData()
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    saveExercisePrice(params) {
      if (params?.dateRange) {
        params.startTime = Number(new Date(params.dateRange[0].toString()))
        params.endTime = Number(
          new Date(params.dateRange[1].toString().replace('00:00:00', '23:59:59'))
        )
      }
      this.$API.rfxList
        .saveExercisePrice(params)
        .then((res) => {
          if (res.code == 200) {
            this.$store.commit('endLoading')
            this.$refs.templateRef.refreshCurrentGridData()
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
    },
    //单元格标题点击操作
    handleClickCellTitle(e) {
      if (e.field == 'pointNo') {
        this.$router.push({
          name: 'fixed-point',
          query: {
            id: e.data.id,
            decidePriceType: e.data.decidePriceType,
            status: e.data.status,
            key: this.$utils.randomString()
          }
        })
      }
    },
    //导出
    handleExport(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (_selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      // 先要点击“查询”按钮才能取得queryBuilderRules
      // const queryBuilderRules =
      //   this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      this.$store.commit('startLoading')
      const params = {
        page: { current: 1, size: 500000 },
        // ...queryBuilderRules,
        defaultRules: [
          {
            field: 'decidePriceType',
            operator: 'equal',
            value: 5
          },
          {
            field: 'id',
            operator: 'in',
            value: _selectGridRecords.map((e) => e.id)
          }
        ]
      } // 筛选条件
      this.$API.rfxList
        .strikePriceExport(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
          this.$store.commit('endLoading')
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.main-form {
  padding-top: 15px;
  /deep/ .mt-form {
    display: flex;
    flex-direction: row;
    .mt-form-item {
      width: 25%;
    }
    .mt-date-range-picker {
      width: 100%;
    }
    .mutliselect-container {
      width: 100%;
      .e-searcher {
        width: 100%;
      }
    }
  }
}
.grid-height {
  height: calc(100% - 82px);
}
.select-width {
  width: 100%;
}
</style>
