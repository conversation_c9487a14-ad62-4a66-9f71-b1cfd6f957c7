<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { detailToolBar, contractDetailColumnData, contractDetailCols } from './config'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  data() {
    return {
      downTemplateParams: {},
      pageConfig: [
        {
          title: this.$t('价格合同查询'),
          toolbar: detailToolBar,
          useToolTemplate: false,
          gridId: '749d3a98-adef-4311-8108-0e4f24aa67b3',
          grid: {
            allowFiltering: true,
            asyncConfig: {
              ignoreDefaultSearch: true,
              url: this.$API.rfxList.getPagePoint,
              afterAsyncData: this.updateTabTitle,
              defaultRules: [
                {
                  field: 'decidePriceType',
                  operator: 'in',
                  value: ['5', '7']
                },
                {
                  label: this.$t('状态'),
                  field: 'status',
                  operator: 'in',
                  value: ['2', '4', '5', '6']
                }
              ]
            },
            lineIndex: true,
            columnData: contractDetailCols
          }
        },
        {
          title: this.$t('价格合同明细查询'),
          toolbar: detailToolBar,
          useToolTemplate: false,
          gridId: '5e602bd3-1695-4696-b9d7-1894ca36cc4f',
          grid: {
            allowFiltering: false,
            asyncConfig: {
              ignoreDefaultSearch: true,
              url: this.$API.rfxList.pagePointDetailItem,
              afterAsyncData: this.updateTabTitle,
              defaultRules: [
                {
                  field: 'point.decidePriceType',
                  operator: 'in',
                  value: ['5', '7']
                },
                {
                  label: this.$t('状态'),
                  field: 'point.status',
                  operator: 'in',
                  value: ['2', '5', '6']
                },
                {
                  field: 'item.status',
                  operator: 'equal',
                  value: '0'
                }
              ]
            },
            lineIndex: true,
            columnData: contractDetailColumnData,
            dataSource: []
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    //表格按钮-点击事件
    handleClickToolBar(e) {
      if (e.toolbar.id === 'export') {
        if (e.tabIndex == 0) {
          this.handleExportMain(e)
        } else {
          this.handleExport()
        }
      }
    },
    //导出
    handleExportMain(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (_selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      // 先要点击“查询”按钮才能取得queryBuilderRules
      // const queryBuilderRules =
      //   this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      this.$store.commit('startLoading')
      const params = {
        page: { current: 1, size: 500000 },
        // ...queryBuilderRules,
        defaultRules: [
          {
            field: 'decidePriceType',
            operator: 'in',
            value: ['5', '7']
          },
          {
            field: 'id',
            operator: 'in',
            value: _selectGridRecords.map((e) => e.id)
          }
        ]
      } // 筛选条件
      this.$API.rfxList
        .excelExport(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
          this.$store.commit('endLoading')
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    //导出
    handleExport() {
      this.$store.commit('startLoading')
      // 先要点击“查询”按钮才能取得queryBuilderRules
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 500000 },
        ...queryBuilderRules,
        defaultRules: [
          {
            field: 'point.decidePriceType',
            operator: 'in',
            value: ['5', '7']
          },
          {
            label: this.$t('状态'),
            field: 'point.status',
            type: 'string',
            operator: 'equal',
            value: '2'
          }
        ]
      } // 筛选条件
      this.$API.rfxList
        .basicAndAverageExport(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
          this.$store.commit('endLoading')
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    //单元格标题点击操作
    handleClickCellTitle(e) {
      if (e.field == 'pointNo') {
        let routerName, _id
        if (e.data.decidePriceType == '7') {
          routerName = 'contract-price-detail'
        } else {
          routerName = 'fixed-point'
        }
        if (e.tabIndex == 0) {
          _id = e.data.id
        } else {
          _id = e.data.pointId
        }
        this.$router.push({
          name: routerName,
          query: {
            id: _id,
            decidePriceType: e.data.decidePriceType,
            status: e.data.status,
            key: this.$utils.randomString()
          }
        })
      }
    }
  }
}
</script>
