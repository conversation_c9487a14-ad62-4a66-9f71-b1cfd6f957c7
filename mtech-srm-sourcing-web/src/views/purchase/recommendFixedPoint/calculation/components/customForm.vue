<template>
  <mt-form ref="editForm" :model="formData">
    <mt-form-item
      v-for="(item, index) in formItems"
      :key="index"
      :prop="item.fieldCode"
      :label="item.fieldName"
      label-style="top"
    >
      <!-- 文本、数值输入框 -->
      <vxe-input
        v-if="item.type !== 'select'"
        v-model="formData[item.fieldCode]"
        :type="item.type"
        :clearable="!item.readonly"
        min="0"
        :readonly="item.readonly"
        :disabled="item.disabled"
        :placeholder="
          type === 'extra' && item.readonly
            ? ''
            : $t('请输入') + item.fieldName + (type === 'extra' ? $t('公式') : '')
        "
        @blur="handleFormValueChange"
        @prev-number="handleNumberChange"
        @next-number="handleNumberChange"
      />
      <!-- 选择框 -->
      <vxe-select
        v-else-if="item.type === 'select' && !item.valueSet"
        v-model="formData[item.fieldCode]"
        :options="item.dataSource || []"
        :option-props="{ label: 'text', value: 'value' }"
        :clearable="!item.readonly"
        filterable
        :readonly="item.readonly"
        :disabled="item.disabled"
        :placeholder="$t('请选择') + item.fieldName"
        @change="(e) => handleSelectChange(item.fieldCode, e)"
      />
      <div v-else class="select-dialog">
        <vxe-input
          v-model="formData[item.fieldCode]"
          :clearable="!item.readonly"
          readonly
          :disabled="item.disabled"
          :type="item.readonly ? 'text' : 'search'"
          :placeholder="
            type === 'extra' && item.readonly
              ? ''
              : $t('请选择') + item.fieldName + (type === 'extra' ? $t('公式') : '')
          "
          @clear="
            () => {
              $set(formData, item.fieldCode, null)
              $set(formData, item + 'Json', {})
              handleFormValueChange()
            }
          "
          @search-click="handleSelectItem(item)"
        />
      </div>
    </mt-form-item>
  </mt-form>
</template>
<script>
import selectMixin from '../config/selectMixin.js'
import debounce from 'lodash.debounce'

export default {
  props: {
    type: {
      type: String,
      default: ''
    },
    formItems: {
      type: Array,
      default: () => []
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  mixins: [selectMixin],
  data() {
    return {
      formData: {}
    }
  },
  computed: {},
  watch: {
    data: {
      handler(newValue) {
        this.formData = newValue
      },
      deep: true
    }
  },
  mounted() {
    this.formData = this.data
  },
  methods: {
    // 数值输入框，在点击右侧向上/向下按钮时触发该事件
    handleNumberChange: debounce(function () {
      this.handleFormValueChange()
    }, 1000),
    // 单位值集，选择弹窗
    handleSelectItem(item) {
      let comp = import('@/views/common/components/dialog/itemCodeDialog.vue')
      if (item.valueSet === 'cost_factor') {
        comp = import('@/views/common/components/dialog/costFactorPriceDialog.vue')
      }
      this.$dialog({
        modal: () => comp,
        data: {
          title: item.fieldName,
          type: 'Cost',
          rfxId: this.$route.query.rfxId,
          valueSet: item.valueSet
          // nodeCode: row?.nodeCode
        },
        success: (data) => {
          const oldCellValue = this.formData[item.fieldCode]
          const fields = this.getJsonValueFields(item.valueSet)
          const newValue = data[fields.valueCode]

          const jsonValue = {}
          jsonValue[fields.valueCode] = data[fields.valueCode]
          jsonValue[fields.nameCode] = data[fields.nameCode]

          if (newValue !== oldCellValue) {
            this.$set(this.formData, item.fieldCode, newValue)
            this.$set(this.formData, item.fieldCode + 'Json', jsonValue)

            // 成本因子，带出规格、品牌、单位、单价
            if (item.valueSet === 'cost_factor') {
              const tempList = [
                { field: 'costFactorSpec', valueKey: 'costFactorSpec' },
                { field: 'costFactorBrand', valueKey: 'costFactorBrand' },
                { field: 'unitName', valueKey: 'baseMeasureUnitCode' },
                { field: 'price', valueKey: 'unitPriceUntaxed' }
              ]
              tempList.forEach((item) => {
                this.$set(this.formData, item.field, data[item.valueKey])
                if (item.field === 'unitName') {
                  this.$set(this.formData, 'unitNameJson', {
                    unitCode: data.baseMeasureUnitCode,
                    unitName: data.baseMeasureUnitName
                  })
                }
              })
            }
            this.handleFormValueChange()
          }
        }
      })
    },
    handleFormValueChange() {
      this.$emit('change', this.formData)
    }
  }
}
</script>
<style lang="scss" scoped>
.select-dialog {
  display: flex;
  .select-dialog-icon {
    margin: 0 0 0 5px;
    line-height: 35px !important;
  }
}

::v-deep {
  .mt-form-item {
    margin: 5px 0 0 0;
    .label {
      margin: 0 5px 3px 5px;
      font-size: 12px;
    }
  }
  .vxe-input {
    width: 100%;
    height: 28px;
    line-height: 28px;
    .vxe-input--extra-suffix {
      font-size: 12px;
    }
  }
  .vxe-select {
    width: 100%;
    height: 28px;
    line-height: 28px;
    .vxe-input--extra-suffix {
      font-size: 12px;
    }
  }
}
</style>
