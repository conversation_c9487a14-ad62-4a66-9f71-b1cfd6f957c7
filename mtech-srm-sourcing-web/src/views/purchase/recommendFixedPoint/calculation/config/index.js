import { i18n } from '@/main.js'
import detailTemplate from '../components/detailTemplate.vue'
import { createEditInstance } from '@/utils/ej/dataGrid/index'

const editInstance = createEditInstance().onInput((ctx, { field }) => {
  const val = ctx.getValueByField(field)

  if (field === 'columnType') {
    if (val === 0) {
      // ctx.setValueByField("supplierVisible", 0);
      ctx.setOptions('calculationFormula', {
        disabled: false
      })
      ctx.setOptions('valueSet', {
        disabled: true,
        value: ''
      })
    } else if (val === 2) {
      ctx.setOptions('calculationFormula', {
        disabled: true
      })
      ctx.setOptions('valueSet', {
        disabled: false
      })
    } else {
      ctx.setOptions('calculationFormula', {
        disabled: true
      })
      ctx.setOptions('valueSet', {
        disabled: true,
        value: ''
      })
    }
  }
})
const columnData = () => {
  return [
    {
      field: 'nodeName',
      allowEditing: false,
      headerText: i18n.t('成本项明细'),
      width: 500,
      cssClass: ''
    },
    {
      field: 'price',
      width: 200,
      headerText: i18n.t('总价'),
      edit: editInstance.create({
        getEditConfig: (ctx) => ({
          type: 'number',
          disabled: ctx.rowData.leafNode == 1 && ctx.rowData.enableItem == 0 ? false : true
        })
      })
    },
    {
      field: 'id',
      isPrimaryKey: true,
      visible: false
    },
    {
      field: 'id',
      visible: false
    },
    {
      field: 'enableItem',
      visible: false
    },
    {
      field: 'childrenList',
      visible: false
    },
    {
      field: 'leafNode',
      visible: false
    }
  ]
}
// 树状表
export const pageConfig = (params, _this) => {
  return [
    {
      // gridId: "8e7bf6a0-d23f-43f2-90d6-01ad41ac3b1c",
      useToolTemplate: false,
      useBaseConfig: false,
      toolbar: [
        [],
        [
          {
            id: 'Setting',
            icon: 'icon_solid_Filter',
            title: i18n.t('筛选供应商')
          }
        ]
      ],
      treeGrid: {
        useRowSelect: false,
        detailTemplate: () => {
          return { template: detailTemplate }
        },
        actionComplete: (e) => {
          console.log('actionComplete', e)
          if (e.type == 'actionComplete' && e.action) {
            _this.actionComplete(e.data)
          }
        },
        rowSelected: (e) => {
          console.log('rowSelected1', e)
          _this.rowSelected(e)
        },
        dataBound: () => {
          console.log('dataBound')
          _this.dataBound()
        },
        allowPaging: false,
        columnData: columnData(),
        childMapping: 'childrenList',
        // rowSelected: (e) => {
        //   _this.rowSelected(e);
        // },
        editSettings: {
          allowEditing: true,
          allowAdding: true,
          allowDeleting: true,
          mode: 'Row'
        },
        selectionSettings: { type: 'Single', checkboxOnly: false },
        dataSource: []

        // asyncConfig: {
        //   url: "sourcing/tenant/rfxCostModel/queryCostModelItem",
        //   params: params,
        //   recordsPosition: "data",
        //   methods: "get",
        // },
      }
    }
  ]
}
