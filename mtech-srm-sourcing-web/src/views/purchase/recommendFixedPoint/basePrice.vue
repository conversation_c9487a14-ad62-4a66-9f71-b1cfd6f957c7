<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadInitBalanceRef"
      :down-template-name="downTemplateName"
      :request-urls="requestUrls"
      :down-template-params="downTemplateParams"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import { todoListToolBar, baseColumnData, detailToolBar, baseDetailColumnData } from './config'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  data() {
    return {
      requestUrls: {},
      downTemplateName: this.$t('导入模板'),
      downTemplateParams: {},
      pageConfig: [
        {
          title: this.$t('基价定价'),
          toolbar: todoListToolBar,
          useToolTemplate: false,
          gridId: 'a0c16e5b-ec6c-4363-a867-d4016d532f62',
          grid: {
            allowFiltering: true,
            asyncConfig: {
              url: this.$API.rfxList.getPagePoint,
              afterAsyncData: this.updateTabTitle,
              defaultRules: [
                {
                  field: 'decidePriceType',
                  operator: 'in',
                  value: ['3']
                }
              ]
            },
            lineIndex: true,
            columnData: baseColumnData,
            dataSource: []
          }
        },
        {
          title: this.$t('基价查询'),
          toolbar: detailToolBar,
          useToolTemplate: false,
          gridId: '49ed7a94-4f43-4d27-be0d-0b9bab871545',
          grid: {
            allowFiltering: true,
            asyncConfig: {
              url: this.$API.rfxList.pagePointDetailItem,
              afterAsyncData: this.updateTabTitle,
              defaultRules: [
                {
                  field: 'point.decidePriceType',
                  operator: 'in',
                  value: ['3']
                }
              ]
            },
            lineIndex: true,
            columnData: baseDetailColumnData,
            dataSource: []
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    // 上传（显示弹窗）
    handleUpload() {
      this.requestUrls = {
        templateUrlPre: 'rfxList',
        templateUrl: 'getBaseTemplate',
        uploadUrl: 'importBasePrice',
        rfxId: '0'
      }
      this.showUploadExcel(true)
    },
    // 展示/不展示 上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadInitBalanceRef.uploadData = [] // 清空数据
        this.$refs.uploadInitBalanceRef.$refs.uploader.files = []
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.$refs.templateRef.refreshCurrentGridData()
    },
    updateTabTitle() {},

    //新增
    handleAddFn(decidePriceType) {
      this.$dialog({
        modal: () => import('./newAddComponents/index.vue'),
        data: {
          title: this.$t('创建基价定价'),
          decidePriceType
        },
        success: (res) => {
          // this.$refs.templateRef.refreshCurrentGridData()
          this.$router.push({
            name: 'fixed-point',
            query: {
              id: res.data.id,
              decidePriceType: res.data.decidePriceType,
              status: res.data?.status ? res.data.status : 0,
              key: this.$utils.randomString()
            }
          })
        }
      })
    },

    //删除
    handleDelFn(selectGridRecords, idList) {
      if (selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            let params = {
              idList: idList
            }
            this.$API.rfxList.deletePointList(params).then(() => {
              this.$toast({ content: this.$t('删除成功'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
            })
          }
        })
      }
    },

    //提交
    handleSubmitFn(selectGridRecords) {
      if (selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else if (selectGridRecords.length > 1) {
        this.$toast({ content: this.$t('只选择一行提交'), type: 'warning' })
      } else {
        if (
          selectGridRecords[0].status < 1 ||
          selectGridRecords[0].status == 3 ||
          selectGridRecords[0].status == 7 ||
          selectGridRecords[0].status == 8
        ) {
          let params = {
            id: selectGridRecords[0].id
          }
          this.$store.commit('startLoading')
          this.$API.rfxList
            .submitPricing(params)
            .then(() => {
              this.$store.commit('endLoading')
              this.$toast({ content: this.$t('提交成功'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
            })
            .catch(() => {
              this.$store.commit('endLoading')
            })
        } else {
          this.$toast({
            content: this.$t('此状态下无法提交'),
            type: 'warning'
          })
        }
      }
    },

    //表格按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      let idList = []
      _selectGridRecords.map((item) => {
        idList.push(item.id)
      })
      let _decidePriceType = 3
      if (e.toolbar.id == 'Add') {
        this.handleAddFn(_decidePriceType) //新增
      } else if (e.toolbar.id == 'del') {
        for (let item of _selectGridRecords) {
          if (item.status != 0 && item.status != 7 && item.status != 8) {
            this.$toast({
              content: this.$t('只能删除草稿、撤回、废弃状态的单据'),
              type: 'warning'
            })
            return
          }
        }
        this.handleDelFn(_selectGridRecords, idList) //删除
      } else if (e.toolbar.id == 'Submit') {
        this.handleSubmitFn(_selectGridRecords) //提交
      } else if (e.toolbar.id == 'import') {
        this.handleUpload()
      } else if (e.toolbar.id == 'export') {
        if (e.tabIndex === 0) {
          this.handleExport(e)
        } else {
          this.handleExportDetail()
        }
      }
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
    },
    //单元格标题点击操作
    handleClickCellTitle(e) {
      if (e.field == 'pointNo') {
        if (e.tabIndex == 0) {
          this.$router.push({
            name: 'fixed-point',
            query: {
              id: e.data.id,
              decidePriceType: e.data.decidePriceType,
              status: e.data.status,
              key: this.$utils.randomString()
            }
          })
        } else {
          this.$router.push({
            name: 'fixed-point',
            query: {
              id: e.data.pointId,
              decidePriceType: e.data.decidePriceType,
              status: e.data.status,
              key: this.$utils.randomString()
            }
          })
        }
      }
    },
    //导出
    handleExport(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (_selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      // 先要点击“查询”按钮才能取得queryBuilderRules
      // const queryBuilderRules =
      //   this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      this.$store.commit('startLoading')
      const params = {
        page: { current: 1, size: 500000 },
        // ...queryBuilderRules,
        defaultRules: [
          {
            field: 'decidePriceType',
            operator: 'equal',
            value: 3
          },
          {
            field: 'id',
            operator: 'in',
            value: _selectGridRecords.map((e) => e.id)
          }
        ]
      } // 筛选条件
      this.$API.rfxList
        .basePriceExport(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
          this.$store.commit('endLoading')
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    //导出
    handleExportDetail() {
      this.$store.commit('startLoading')
      // 先要点击“查询”按钮才能取得queryBuilderRules
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 500000 },
        ...queryBuilderRules,
        defaultRules: [
          {
            field: 'point.decidePriceType',
            operator: 'equal',
            value: 3
          }
        ]
      } // 筛选条件
      this.$API.rfxList
        .basicAndAverageExport(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
          this.$store.commit('endLoading')
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    }
  }
}
</script>
