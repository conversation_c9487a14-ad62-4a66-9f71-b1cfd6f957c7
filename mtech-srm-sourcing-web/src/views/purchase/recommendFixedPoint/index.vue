<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { todoListToolBar, todoListColumnData } from './config'

export default {
  data() {
    return {
      pageConfig: [
        {
          // title: this.$t("直接定价"),
          toolbar: todoListToolBar,
          useToolTemplate: false,
          gridId: this.$permission.gridId['purchase']['recommendFixedPoint'],
          grid: {
            allowFiltering: true,
            asyncConfig: {
              url: this.$API.rfxList.getPagePoint,
              afterAsyncData: this.updateTabTitle,
              defaultRules: [
                {
                  field: 'decidePriceType',
                  operator: 'in',
                  value: ['0']
                }
              ]
            },
            lineIndex: true,
            columnData: todoListColumnData,
            dataSource: []
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    updateTabTitle() {},

    //新增
    handleAddFn() {
      this.$dialog({
        modal: () => import('./newAddComponents/index.vue'),
        data: {
          title: this.$t('创建定价'),
          decidePriceType: 0
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    //删除
    handleDelFn(selectGridRecords, idList) {
      if (selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            let params = {
              idList: idList
            }
            this.$API.rfxList.deletePointList(params).then(() => {
              this.$toast({ content: this.$t('删除成功'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
            })
          }
        })
      }
    },

    //提交
    handleSubmitFn(selectGridRecords) {
      if (selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else if (selectGridRecords.length > 1) {
        this.$toast({ content: this.$t('只选择一行提交'), type: 'warning' })
      } else {
        if (selectGridRecords[0].status < 1 || selectGridRecords[0].status == 3) {
          let params = {
            id: selectGridRecords[0].id
          }
          this.$API.rfxList.submitPoint(params).then(() => {
            this.$toast({ content: this.$t('提交成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        } else {
          this.$toast({
            content: this.$t('只能提交状态为草稿或审批驳回的单据'),
            type: 'warning'
          })
        }
      }
    },

    //表格按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      let idList = []
      _selectGridRecords.map((item) => {
        idList.push(item.id)
      })
      if (e.toolbar.id == 'Add') {
        this.handleAddFn() //新增
      } else if (e.toolbar.id == 'del') {
        for (let item of _selectGridRecords) {
          if (item.status != 0) {
            this.$toast({
              content: this.$t('只能删除草稿状态的单据'),
              type: 'warning'
            })
            return
          }
        }
        this.handleDelFn(_selectGridRecords, idList) //删除
      } else {
        this.handleSubmitFn(_selectGridRecords) //提交
      }
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
    },
    //单元格标题点击操作
    handleClickCellTitle(e) {
      if (e.field == 'pointNo') {
        this.$router.push({
          name: 'fixed-point',
          query: {
            id: e.data.id,
            decidePriceType: e.data.decidePriceType,
            status: e.data.status,
            key: this.$utils.randomString()
          }
        })
      }
    }
  }
}
</script>
