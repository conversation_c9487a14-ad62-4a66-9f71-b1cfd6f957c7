<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="modalData.title"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="appForm" :model="form.data" :rules="form.rules">
        <mt-row :gutter="20">
          <mt-col :span="12" v-if="form.data.decidePriceType < 2">
            <mt-form-item prop="title" :label="$t('标题')">
              <mt-input
                type="text"
                v-model="form.data.title"
                :placeholder="$t('请输入标题')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col
            :span="12"
            v-if="form.data.decidePriceType != 3 && form.data.decidePriceType != 4"
          >
            <mt-form-item prop="priceObjectName" :label="$t('定价对象')">
              <mt-select
                :value="form.data.priceObjectName"
                :fields="{ text: 'sourcingObj', value: 'templateCode' }"
                :data-source="form.dataSource.pricing"
                :placeholder="$t('请选择定价对象')"
                @input="inputPricing($event, 'priceObjectName')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12" v-if="form.data.decidePriceType > 2">
            <mt-form-item prop="supplierName" :label="$t('供应商')">
              <mt-select
                v-model="form.data.supplierName"
                float-label-type="Never"
                :data-source="supplierList"
                :allow-filtering="true"
                filter-type="Contains"
                :fields="{ text: 'text', value: 'supplierName' }"
                @change="changeSupplier"
                :placeholder="$t('请选择供应商')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12" v-if="form.data.decidePriceType < 2">
            <mt-form-item prop="companyName" :label="$t('公司')">
              <!-- <mt-select
                v-model="form.data.companyName"
                :fields="{
                  text: 'orgName',
                  value: 'orgTypeCode',
                }"
                :data-source="form.dataSource.firm"
                :placeholder="$t('请选择公司')"
                @input="inputPricing($event, 'companyName')"
                :show-clear-button="true"
              ></mt-select> -->
              <mt-select
                v-model="form.data.companyName"
                float-label-type="Never"
                :data-source="form.dataSource.firm"
                :fields="{ text: 'orgName', value: 'orgName' }"
                @change="changeTaxItem"
                :placeholder="$t('请选择公司')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12" v-if="form.data.decidePriceType < 2">
            <mt-form-item prop="purOrgName" :label="$t('采购组织')">
              <mt-multi-select
                :value="form.data.purOrgName"
                :fields="{
                  text: 'organizationName',
                  value: 'organizationCode'
                }"
                :data-source="form.dataSource.procurement"
                :placeholder="$t('请选择')"
                @change="purOrgChange"
                :show-clear-button="true"
              ></mt-multi-select>
            </mt-form-item>
          </mt-col>
          <mt-col
            :span="12"
            v-if="form.data.decidePriceType != 3 && form.data.decidePriceType != 4"
          >
            <mt-form-item prop="purId" :label="$t('采购员')">
              <!-- <mt-select
                :value="form.data.purId"
                :fields="{
                  text: 'employeeName',
                  value: 'employeeCode',
                }"
                :data-source="form.dataSource.personnel"
                :placeholder="$t('请选择采购员')"
                @input="inputPricing($event, 'purId')"
                :show-clear-button="true"
                :allow-filtering="true"
                :filtering="filteringPurId"
              ></mt-select> -->
              <debounce-filter-select
                v-model="form.data.purId"
                :request="getCurrentEmployees"
                :data-source="form.dataSource.personnel"
                @change="inputPricing($event, 'purId')"
                :show-clear-button="false"
                :fields="{ text: 'text', value: 'uid' }"
                :placeholder="$t('请选择采购员')"
              ></debounce-filter-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12" v-if="form.data.decidePriceType != 4">
            <mt-form-item prop="decidePriceType" :label="$t('定价单类型')">
              <mt-select
                :disabled="true"
                :value="form.data.decidePriceType"
                :data-source="form.dataSource.pricingType"
                :placeholder="$t('请选择定价单类型')"
                @input="inputPricing($event, 'decidePriceType')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col
            :span="12"
            v-if="form.data.decidePriceType == 3 || form.data.decidePriceType == 4"
          >
            <mt-form-item prop="currencyName" :label="$t('币种')">
              <mt-input type="text" :disabled="true" v-model="form.data.currencyName"></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col
            :span="12"
            v-if="form.data.decidePriceType == 3 || form.data.decidePriceType == 4"
          >
            <mt-form-item prop="taxRateName" :label="$t('税率')">
              <mt-input type="text" :disabled="true" v-model="form.data.taxRateName"></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12" v-if="form.data.priceClassification == 4">
            <mt-form-item prop="contractType" :label="$t('合同类型')">
              <mt-select
                :value="form.data.contractType"
                :data-source="contractTypeList"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col
            :span="12"
            v-if="(form.data.decidePriceType == 0 || form.data.decidePriceType == 5) && ktFlag"
          >
            <mt-form-item
              prop="compressor"
              :label="$t('压缩机（暖通压缩机/电机/电控）')"
              class="custom-height"
            >
              <mt-checkbox
                class="checkbox-item"
                v-model="form.data.compressor"
                @change="compressorChange"
              ></mt-checkbox>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12" v-if="form.data.decidePriceType != 4">
            <mt-form-item prop="priceClassification" :label="$t('价格分类')">
              <mt-select
                :disabled="form.data.decidePriceType != 0"
                :value="form.data.priceClassification"
                :data-source="form.dataSource.priceClassification"
                :placeholder="$t('请选择价格分类')"
                @input="inputPricing($event, 'priceClassification')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="remark" :label="$t('备注')">
              <mt-input
                type="text"
                v-model="form.data.remark"
                :placeholder="$t('请输入备注')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
import debounceFilterSelect from 'ROUTER_PURCHASE_RFX/components/debounceFilterSelect'
const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
export default {
  components: {
    debounceFilterSelect
  },
  data() {
    return {
      currencyList: [],
      purOrgList: [],
      contractTypeList: [
        { text: this.$t('标准'), value: 'standard' },
        { text: this.$t('钢材'), value: 'rolled' },
        { text: this.$t('委外'), value: 'outsource' }
      ],
      ktFlag: 0,
      supplierList: [],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.saveRule,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      form: {
        data: {
          companyName: '', // 公司
          companyCode: '',
          companyId: '',
          priceObjectName: '', // 定价对象
          priceObjectCode: '',
          priceObjectId: '',
          purOrgName: [], // 	采购组织
          compressor: false,
          purOrgCode: '',
          purOrgId: '',
          decidePriceType: '', //定价单类型
          id: '',
          pointNo: '', //定价单号
          remark: '',
          title: '',
          purId: userInfo.uid, //采购员
          purName: '',
          priceClassification: '',
          currencyName: '',
          taxRateName: ''
        },
        rules: {
          //定价单类型
          decidePriceType: {
            required: true,
            message: this.$t('请选择定价单类型'),
            trigger: 'blur'
          },
          // 采购员
          purId: {
            required: true,
            message: this.$t('请选择采购员'),
            trigger: 'blur'
          },
          // 供应商
          supplierName: {
            required: true,
            message: this.$t('请选择供应商'),
            trigger: 'blur'
          },
          // 公司
          companyName: {
            required: true,
            message: this.$t('请选择公司'),
            trigger: 'blur'
          },
          // 	采购组织
          purOrgName: {
            required: true,
            message: this.$t('请选择采购组织'),
            trigger: 'blur'
          },
          // 定价对对像
          priceObjectName: {
            required: true,
            message: this.$t('请选择定价对象'),
            trigger: 'blur'
          },
          priceClassification: {
            required: true,
            message: this.$t('请选择价格分类'),
            trigger: 'blur'
          },
          title: {
            required: true,
            message: this.$t('请输入标题'),
            trigger: 'blur'
          }
        },
        dataSource: {
          pricing: [],
          procurement: [],
          personnel: [],
          firm: [],
          pricingType: [
            { text: this.$t('直接定价'), value: 0 },
            { text: this.$t('基价定价'), value: 3 },
            { text: this.$t('均价定价'), value: 4 },
            { text: this.$t('执行价定价'), value: 5 }
            // { text: this.$t("寻源结果定价"), value: 1 },
          ],
          priceClassification: [
            { text: this.$t('基价'), value: 1 },
            { text: this.$t('SRM价格'), value: 2 },
            { text: this.$t('暂估价格'), value: 3 },
            { text: this.$t('执行价格'), value: 4 }
          ]
        }
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  beforeMount() {
    this.mergeFormData()
  },
  mounted() {
    this.getAllCurrency()
    this.form.data.decidePriceType = this.modalData.decidePriceType
    if (this.modalData.decidePriceType == 3) {
      this.form.data.priceClassification = 1
    } else if (this.modalData.decidePriceType == 4) {
      this.form.data.priceClassification = 2
    } else if (this.modalData.decidePriceType == 5) {
      this.form.data.priceClassification = 4
      // this.getKtFlag()
      this.form.data.contractType = 'standard'
    } else {
      // this.getKtFlag()
    }

    this.getConfigList()
    // this.getUserPageList();
    this.getCurrentEmployees({ text: '' })
    this.getSpecifiedChildrenLevelOrgs()
    this.getSupplierList()
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    getAllCurrency() {
      this.$API.masterData.queryAllCurrency().then((res) => {
        if (res.code == 200) {
          this.currencyList = res.data
        }
      })
    },
    // getKtFlag() {
    //   this.$API.rfxList.getKtFlag().then((res) => {
    //     if (res.code == 200) {
    //       this.ktFlag = res.data
    //       if (this.ktFlag) {
    //         this.form.data.contractType = 'standard'
    //       }
    //     }
    //   })
    // },
    compressorChange(e) {
      console.log(e)
      this.form.data.compressor = e.checked
    },
    getSupplierList() {
      // this.$API.masterData.getSupplierList().then((res) => {
      //   if (res.code == 200) {
      //     this.supplierList = res.data
      //   }
      // })
      let _param = {
        page: { current: 1, size: 10000 },
        defaultRules: [{ field: 'statusId', operator: 'in', value: [3, 10] }]
      }
      this.$API.masterData.supplierDistinctPagedQuery(_param).then((res) => {
        if (res.code == 200) {
          this.supplierList.length = 0
          res.data.records.forEach((item) =>
            this.supplierList.push({ ...item, text: `${item.supplierCode}-${item.supplierName}` })
          )
        }
      })
    },
    changeSupplier(e) {
      if (e.value) {
        this.form.data.supplierCode = e.itemData.supplierCode
        this.form.data.supplierId = e.itemData.id
        if (this.modalData.decidePriceType == 3 || this.modalData.decidePriceType == 4) {
          this.$API.rfxList
            .getRfxCountdown({ supplierCode: this.form.data.supplierCode })
            .then((res) => {
              if (
                res.code == 200 &&
                res.data &&
                res.data.bidCurrencyCode &&
                res.data.bidTaxRateCode
              ) {
                let _record = this.currencyList.find(
                  (e) => e.currencyCode == res.data.bidCurrencyCode
                )
                if (_record) {
                  this.form.data.currencyName = _record.currencyName
                }
                this.form.data.currencyCode = res.data.bidCurrencyCode
                this.form.data.taxRateName = res.data.bidTaxRateName
                this.form.data.taxRateCode = res.data.bidTaxRateCode
                this.form.data.taxRateValue = res.data.bidTaxRateValue
              } else {
                this.form.data.currencyCode = null
                this.form.data.taxRateName = null
                this.form.data.currencyName = null
                this.form.data.taxRateCode = null
                this.form.data.taxRateValue = null
                this.$toast({
                  content: this.$t('当前供应商没有对应的币种税率，请重新选择'),
                  type: 'warning'
                })
              }
            })
        }
      } else {
        this.form.data.supplierCode = null
        this.form.data.supplierId = null
      }
    },
    inputPricing(val, type) {
      if (type == 'priceObjectName') {
        const arr = this.form.dataSource.pricing.find((e) => e.templateCode === val)
        if (arr) {
          this.form.data.priceObjectName = arr.sourcingObj
          this.form.data.priceObjectId = arr.id
          this.form.data.priceObjectCode = arr.templateCode
        }
      } else if (type == 'purId') {
        this.form.data.purName = val.itemData.employeeName
        this.form.data.purId = val.itemData.employeeId
      } else if (type == 'companyName') {
        const arr = this.form.dataSource.firm.find((e) => e.orgCode === val)
        if (arr) {
          this.form.data.companyName = arr.orgName
          this.form.data.companyId = arr.id
          this.form.data.companyCode = arr.orgCode
        }
      } else if (type == 'decidePriceType') {
        const arr = this.form.dataSource.pricingType.find((e) => e.value === val)
        console.log(224, arr)
        if (arr) {
          this.form.data.decidePriceType = arr.value
        }
      } else if (type == 'priceClassification') {
        const arr = this.form.dataSource.priceClassification.find((e) => e.value === val)
        if (arr) {
          this.form.data.priceClassification = arr.value
          console.error(this.form.data.priceClassification)
        }
      }
    },
    purOrgChange(e) {
      console.log('purOrgChange--', e)
      this.form.data.purOrgName = e.value
      this.purOrgList = this.form.dataSource.procurement.filter((item) => {
        return e.value.includes(item.organizationCode)
      })
      console.log(192, this.purOrgList)
      //   if (arr) {
      //     this.form.data.purOrgName = arr.organizationName;
      //     this.form.data.purOrgId = arr.id;
      //     this.form.data.purOrgCode = arr.organizationTypeCode;
      //   }
    },
    changeTaxItem(e) {
      this.form.data.companyName = e.itemData.orgName
      this.form.data.companyId = e.itemData.id
      this.form.data.companyCode = e.itemData.orgCode
      this.form.data.purOrgName = []
      this.form.data.purOrgCode = ''
      this.form.data.purOrgId = ''
      this.getMainData(e.itemData.id)
    },
    mergeFormData() {
      const formKeys = Object.keys(this.form.data)
      const pData = this.modalData.data
      if (!pData) return
      for (let i = 0; i < formKeys.length; i++) {
        const formKey = formKeys[i]
        if (typeof pData[formKey] !== 'undefined') {
          this.form.data[formKey] = pData[formKey]
        }
      }
    },
    async saveRule() {
      const validate = await this.asyncFormValidate('appForm')
      if (!validate) {
        return
      }
      if (this.modalData.decidePriceType == 3 || this.modalData.decidePriceType == 4) {
        if (!this.form.data.taxRateCode || !this.form.data.currencyCode) {
          this.$toast({
            content: this.$t('当前供应商没有对应的币种税率，请重新选择'),
            type: 'warning'
          })
          return
        }
      }
      if (this.modalData.decidePriceType == 0) {
        let _dataList = []
        this.purOrgList.forEach((ele) => {
          _dataList.push({
            ...this.form.data,
            purOrgName: ele.organizationName,
            purOrgId: ele.id,
            purOrgCode: ele.organizationCode,
            compressor: ele.compressor ? 1 : 0
          })
        })
        console.log(this.form.data, _dataList)
        this.$API.rfxList.savePoint(_dataList[0]).then((res) => {
          this.emitConfirm(res)
        })
      } else {
        if (this.modalData.decidePriceType == 3 || this.modalData.decidePriceType == 4) {
          this.$API.rfxList
            .getRfxCountdown({ supplierCode: this.form.data.supplierCode })
            .then((res) => {
              if (res.code == 200 && res.data) {
                let _record = this.currencyList.find(
                  (e) => e.currencyCode == res.data.bidCurrencyCode
                )
                if (_record) {
                  this.form.data.currencyName = _record.currencyName
                }
                this.form.data.currencyCode = res.data.bidCurrencyCode
                this.form.data.taxRateName = res.data.bidTaxRateName
                this.form.data.taxRateCode = res.data.bidTaxRateCode
                this.form.data.taxRateValue = res.data.bidTaxRateValue
              } else {
                this.form.data.currencyCode = null
                this.form.data.taxRateName = null
                this.form.data.currencyName = null
                this.form.data.taxRateCode = null
                this.form.data.taxRateValue = null
                this.$toast({
                  content: this.$t('当前供应商没有对应的币种税率，请重新选择'),
                  type: 'warning'
                })
              }
            })
        }
        let _params = {
          ...this.form.data
        }
        if (this.modalData.decidePriceType == 5) {
          _params.compressor = _params.compressor ? 1 : 0
        }
        delete _params.purOrgName
        this.$API.rfxList.savePoint(_params).then((res) => {
          this.emitConfirm(res)
        })
      }
    },

    asyncFormValidate(el) {
      return new Promise((c) => {
        this.$refs[el].validate((valid) => c(valid))
      })
    },
    cancel() {
      this.emitConfirm()
    },
    emitConfirm(...arg) {
      this.$emit('confirm-function', ...arg)
    },
    //定价对象列表
    getConfigList() {
      this.$API.businessConfig
        .getConfigList({
          page: {
            current: 1,
            size: 1000
          },
          condition: 'and',
          rules: [
            {
              field: 'sourcingMode',
              type: 'string',
              operator: 'equal',
              value: 'rfq'
            }
          ]
        })
        .then((res) => {
          this.form.dataSource.pricing = res.data.records
        })
    },

    //采购组织
    getMainData(companyId) {
      // 数据源 purGroupName
      // this.$API.masterData
      //   .purchaseOraginaze({
      //     organizationTypeCode: "BUORG002ADM",
      //   })
      //   .then((res) => {
      //     this.form.dataSource.procurement = res.data;
      //   });
      this.$API.masterData
        .permissionOrgList({
          orgId: companyId
        })
        .then((res) => {
          this.form.dataSource.procurement = res.data
        })
    },
    //采购员
    // getUserPageList() {
    //   const DEFAULTPARAM = {
    //     condition: "",
    //     page: {
    //       current: 1,
    //       size: 200,
    //     },
    //     pageFlag: false,
    //   };
    //   this.$API.masterData.getUserPageList(DEFAULTPARAM).then((res) => {
    //     this.form.dataSource.personnel = res.data.records;
    //   });
    // },
    // 获取 用户列表
    getCurrentEmployees(e) {
      const { text: fuzzyName } = e
      this.form.dataSource.personnel = []
      this.$API.masterData.getCurrentTenantEmployees({ fuzzyName }).then((res) => {
        const tmp = []
        res.data.forEach((item) => {
          tmp.push({
            ...item,
            text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`
          })
        })
        this.form.dataSource.personnel = tmp
        if (fuzzyName == '') {
          this.form.data.purId = this.form.dataSource.personnel[0].uid
        }
      })
    },
    filteringPurId(e) {
      if (typeof e.text === 'string' && e.text) {
        e.updateData(
          this.form.dataSource.personnel.filter((f) => f?.employeeName.indexOf(e.text) > -1)
        )
      } else {
        e.updateData(this.form.dataSource.personnel)
      }
    },
    //获取公司
    getSpecifiedChildrenLevelOrgs() {
      this.$API.masterData
        .findSpecifiedChildrenLevelOrgs({
          organizationLevelCodes: ['ORG02'],
          orgType: 'ORG001PRO',
          includeItself: false,
          organizationIds: []
        })
        .then((res) => {
          this.form.dataSource.firm = res.data
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  padding-top: 20px;
}
.custom-height {
  height: 54px;
}
</style>
