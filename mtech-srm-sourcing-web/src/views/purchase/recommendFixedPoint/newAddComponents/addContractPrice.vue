<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="modalData.title"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="appForm" :model="form.data" :rules="form.rules">
        <mt-row :gutter="20">
          <!-- <mt-col :span="12">
            <mt-form-item prop="companyName" :label="$t('公司')">
              <mt-select
                v-model="form.data.companyName"
                float-label-type="Never"
                :data-source="form.dataSource.firm"
                :fields="{ text: 'orgName', value: 'orgName' }"
                @change="companyChange"
                :placeholder="$t('请选择公司')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="purOrgCode" :label="$t('采购组织')">
              <mt-select
                v-model="form.data.purOrgCode"
                :fields="{
                  text: 'organizationName',
                  value: 'organizationCode'
                }"
                :data-source="form.dataSource.procurement"
                :placeholder="$t('请选择')"
                @change="purOrgChange"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="siteCode" :label="$t('工厂')">
              <mt-select
                v-model="form.data.siteCode"
                float-label-type="Never"
                :data-source="siteList"
                :fields="{ text: 'orgName', value: 'orgCode' }"
                @change="siteChange"
                :placeholder="$t('请选择工厂')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col> -->
          <mt-col :span="12">
            <mt-form-item prop="supplierCode" :label="$t('供应商')">
              <mt-select
                v-model="form.data.supplierCode"
                float-label-type="Never"
                :data-source="supplierList"
                :allow-filtering="true"
                filter-type="Contains"
                :fields="{ text: 'text', value: 'supplierCode' }"
                @change="changeSupplier"
                :placeholder="$t('请选择供应商')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="priceClassification" :label="$t('价格分类')">
              <mt-select
                v-model="form.data.priceClassification"
                :data-source="form.dataSource.priceClassification"
                :placeholder="$t('请选择价格分类')"
                @change="priceClassificationChange"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="contractType" :label="$t('合同类型')">
              <mt-select
                v-model="form.data.contractType"
                :data-source="contractTypeList"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="currencyName" :label="$t('币种')">
              <mt-input type="text" :disabled="true" v-model="form.data.currencyName"></mt-input>
            </mt-form-item>
          </mt-col>
          <!-- <mt-col :span="12">
            <mt-form-item prop="taxRateName" :label="$t('税率')">
              <mt-input type="text" :disabled="true" v-model="form.data.taxRateName"></mt-input>
            </mt-form-item>
          </mt-col> -->
          <mt-col :span="12">
            <mt-form-item prop="signedTime" :label="$t('签订日期')">
              <mt-input type="text" :disabled="true" v-model="form.data.signedTime"></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="purId" :label="$t('创建人')">
              <mt-input type="text" :disabled="true" v-model="form.data.purName"></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12" v-if="ktFlag">
            <mt-checkbox
              class="checkbox-item"
              :label="$t('压缩机（暖通压缩机/电机/电控）')"
              v-model="form.data.compressor"
              @change="compressorChange"
            ></mt-checkbox>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
import utils from '@/utils/utils'
const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
export default {
  data() {
    return {
      siteList: [],
      purOrgList: [],
      currencyList: [],
      contractTypeList: [
        { text: this.$t('标准'), value: 'standard' },
        { text: this.$t('钢材'), value: 'rolled' },
        { text: this.$t('委外'), value: 'outsource' }
      ],
      ktFlag: 1,
      supplierList: [],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.saveRule,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      form: {
        data: {
          companyName: '', // 公司
          companyCode: '',
          companyId: '',
          priceObjectName: '', // 定价对象
          priceObjectCode: '',
          priceObjectId: '',
          purOrgName: '', // 	采购组织
          compressor: false,
          purOrgCode: '',
          siteCode: '',
          purOrgId: '',
          decidePriceType: '', //定价单类型
          id: '',
          pointNo: '', //定价单号
          remark: '',
          title: '',
          purId: userInfo.uid, //采购员
          purName: userInfo.accountName,
          priceClassification: '',
          currencyName: '',
          taxRateName: ''
        },
        rules: {
          //定价单类型
          decidePriceType: {
            required: true,
            message: this.$t('请选择定价单类型'),
            trigger: 'blur'
          },
          // 供应商
          supplierCode: {
            required: true,
            message: this.$t('请选择供应商'),
            trigger: 'blur'
          },
          // 公司
          companyName: {
            required: true,
            message: this.$t('请选择公司'),
            trigger: 'blur'
          },
          // 	采购组织
          purOrgCode: {
            required: true,
            message: this.$t('请选择采购组织'),
            trigger: 'blur'
          },
          // 	工厂
          siteCode: {
            required: true,
            message: this.$t('请选择工厂'),
            trigger: 'blur'
          },
          priceClassification: {
            required: true,
            message: this.$t('请选择价格分类'),
            trigger: 'blur'
          },
          contractType: {
            required: true,
            message: this.$t('请选择合同类型'),
            trigger: 'blur'
          }
        },
        dataSource: {
          pricing: [],
          procurement: [],
          personnel: [],
          firm: [],
          priceClassification: [
            { text: this.$t('暂估价'), value: '3' },
            { text: this.$t('正式价'), value: '4' }
          ]
        }
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  beforeMount() {
    this.mergeFormData()
  },
  mounted() {
    this.form.data.signedTime = utils.formatTime(new Date(), 'YYYY-mm-dd')
    this.form.data.decidePriceType = this.modalData.decidePriceType
    this.form.data.contractType = 'standard'
    // this.getKtFlag();
    // this.getSpecifiedChildrenLevelOrgs()
    this.getSupplierList()
    this.$refs['dialog'].ejsRef.show()
    this.getAllCurrency()
  },
  methods: {
    getAllCurrency() {
      this.$API.masterData.queryAllCurrency().then((res) => {
        if (res.code == 200) {
          this.currencyList = res.data
        }
      })
    },
    siteChange(e) {
      if (e.value) {
        this.form.data.siteId = e.itemData.id
        this.form.data.siteCode = e.itemData.orgCode
        this.form.data.siteName = e.itemData.orgName
      }
    },
    priceClassificationChange(e) {
      console.log('priceClassificationChange--', e)
      // this.form.data.priceClassification=e.value
    },
    getFactoryList(params) {
      this.$API.masterData
        .permissionSiteList({
          buOrgId: params.purOrgId,
          companyId: params.companyId,
          orgLevelTypeCode: 'ORG06'
        })
        .then((res) => {
          this.siteList = res.data
        })
    },
    // getKtFlag() {
    //   this.$API.rfxList.getKtFlag().then((res) => {
    //     if (res.code == 200) {
    //       this.ktFlag = res.data;
    //       if (this.ktFlag) {
    //         this.form.data.contractType = "standard";
    //       }
    //     }
    //   });
    // },
    compressorChange(e) {
      console.log(e)
      this.form.data.compressor = e.checked
    },
    // getSupplierList(param) {
    //   this.$API.masterData.getSupplierList(param).then((res) => {
    //     if (res.code == 200) {
    //       this.supplierList = res.data
    //     }
    //   })
    // },
    getSupplierList() {
      let _param = {
        page: { current: 1, size: 10000 },
        defaultRules: [{ field: 'statusId', operator: 'in', value: [3, 10] }]
      }
      this.$API.masterData.supplierDistinctPagedQuery(_param).then((res) => {
        if (res.code == 200) {
          this.supplierList.length = 0
          res.data.records.forEach((item) =>
            this.supplierList.push({ ...item, text: `${item.supplierCode}-${item.supplierName}` })
          )
        }
      })
    },
    async changeSupplier(e) {
      if (e.value) {
        this.form.data.supplierName = e.itemData.supplierName
        this.form.data.supplierId = e.itemData.id
        await this.$API.rfxList.getRfxCountdown({ supplierCode: e.value }).then((res) => {
          if (res.code == 200 && res.data && res.data.bidCurrencyCode) {
            let _record = this.currencyList.find((e) => e.currencyCode == res.data.bidCurrencyCode)

            this.form.data.currencyCode = res.data.bidCurrencyCode
            this.form.data.taxRateName = res.data.bidTaxRateName
            this.form.data.currencyName = _record.currencyName
            this.form.data.taxRateCode = res.data.bidTaxRateCode
            this.form.data.taxRateValue = res.data.bidTaxRateValue
          } else {
            this.form.data.currencyCode = null
            this.form.data.taxRateName = null
            this.form.data.currencyName = null
            this.form.data.taxRateCode = null
            this.form.data.taxRateValue = null
            this.$toast({
              content: this.$t('当前供应商没有对应的币种，请重新选择'),
              type: 'warning'
            })
          }
        })
      } else {
        this.form.data.supplierName = null
        this.form.data.supplierId = null
        this.form.data.currencyCode = null
        this.form.data.taxRateName = null
        this.form.data.currencyName = null
        this.form.data.taxRateCode = null
        this.form.data.taxRateValue = null
      }
    },
    purOrgChange(e) {
      console.log('purOrgChange--', e)
      if (e.value) {
        let param = {
          purOrgId: e.itemData.id,
          companyId: this.form.data.companyId
        }
        this.form.data.purOrgName = e.itemData.organizationName
        this.form.data.purOrgId = e.itemData.id
        this.form.data.purOrgCode = e.itemData.organizationCode
        this.getFactoryList(param)
      }
      this.form.data.siteCode = null
      this.form.data.siteId = null
      this.form.data.siteName = null
    },
    companyChange(e) {
      this.form.data.companyName = e.itemData.orgName
      this.form.data.companyId = e.itemData.id
      this.form.data.companyCode = e.itemData.orgCode
      this.form.data.purOrgName = null
      this.form.data.purOrgCode = null
      this.form.data.purOrgId = null
      this.form.data.siteCode = null
      this.form.data.siteId = null
      this.form.data.siteName = null
      this.form.data.taxRateCode = null
      this.form.data.taxRateValue = null
      this.form.data.taxRateName = null
      this.form.data.currencyCode = null
      this.form.data.currencyName = null
      this.getMainData(e.itemData.id)
      this.getSupplierList({ organizationCode: e.itemData.orgCode })
    },
    mergeFormData() {
      const formKeys = Object.keys(this.form.data)
      const pData = this.modalData.data
      if (!pData) return
      for (let i = 0; i < formKeys.length; i++) {
        const formKey = formKeys[i]
        if (typeof pData[formKey] !== 'undefined') {
          this.form.data[formKey] = pData[formKey]
        }
      }
    },
    async saveRule() {
      const validate = await this.asyncFormValidate('appForm')
      if (!validate) {
        return
      }
      if (!this.form.data.taxRateCode || !this.form.data.currencyCode) {
        this.$toast({
          content: this.$t('当前供应商没有对应的币种税率，请重新选择'),
          type: 'warning'
        })
        return
      }
      let _dataList = [
        {
          ...this.form.data,
          compressor: this.form.data.compressor ? 1 : 0,
          signedTime: Number(new Date(this.form.data.signedTime))
        }
      ]

      console.log(this.form.data, _dataList)
      this.$API.rfxList.savePoint(_dataList[0]).then((res) => {
        this.emitConfirm(res)
      })
    },

    asyncFormValidate(el) {
      return new Promise((c) => {
        this.$refs[el].validate((valid) => c(valid))
      })
    },
    cancel() {
      this.emitConfirm()
    },
    emitConfirm(...arg) {
      this.$emit('confirm-function', ...arg)
    },

    //采购组织
    getMainData(companyId) {
      this.$API.masterData
        .permissionOrgList({
          orgId: companyId
        })
        .then((res) => {
          this.form.dataSource.procurement = res.data
        })
    },

    filteringPurId(e) {
      if (typeof e.text === 'string' && e.text) {
        e.updateData(
          this.form.dataSource.personnel.filter((f) => f?.employeeName.indexOf(e.text) > -1)
        )
      } else {
        e.updateData(this.form.dataSource.personnel)
      }
    },
    //获取公司
    getSpecifiedChildrenLevelOrgs() {
      this.$API.masterData
        .findSpecifiedChildrenLevelOrgs({
          organizationLevelCodes: ['ORG02'],
          orgType: 'ORG001PRO',
          includeItself: false,
          organizationIds: []
        })
        .then((res) => {
          this.form.dataSource.firm = res.data
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  padding-top: 20px;
}
.custom-height {
  height: 54px;
}
</style>
