<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { contractToolBar, contractColumnData } from './config'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  data() {
    return {
      pageConfig: [
        {
          // title: this.$t("直接定价"),
          toolbar: contractToolBar,
          useToolTemplate: false,
          gridId: 'd64e638e-6076-4b27-bf60-b534eb4e8ec6',
          buttonQuantity: 6,
          grid: {
            allowFiltering: true,
            asyncConfig: {
              url: this.$API.rfxList.getPagePoint,
              afterAsyncData: this.updateTabTitle,
              defaultRules: [
                {
                  field: 'decidePriceType',
                  operator: 'in',
                  value: ['7']
                }
              ]
            },
            lineIndex: true,
            columnData: contractColumnData,
            dataSource: []
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    updateTabTitle() {},

    //新增
    handleAddFn() {
      this.$dialog({
        modal: () => import('./newAddComponents/addContractPrice.vue'),
        data: {
          title: this.$t('创建价格合同'),
          decidePriceType: 7
        },
        success: (res) => {
          // this.$refs.templateRef.refreshCurrentGridData()
          this.$router.push({
            name: 'contract-price-detail',
            query: {
              id: res.data.id,
              decidePriceType: res.data.decidePriceType,
              status: res.data?.status ? res.data.status : 0,
              key: this.$utils.randomString()
            }
          })
        }
      })
    },

    //删除
    handleDelFn(selectGridRecords, idList) {
      if (selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            let params = {
              idList: idList
            }
            this.$API.rfxList.deletePointList(params).then(() => {
              this.$toast({ content: this.$t('删除成功'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
            })
          }
        })
      }
    },

    //提交
    handleSubmitFn(selectGridRecords) {
      if (selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else if (selectGridRecords.length > 1) {
        this.$toast({ content: this.$t('只选择一行提交'), type: 'warning' })
      } else {
        if (
          selectGridRecords[0].status < 1 ||
          selectGridRecords[0].status == 3 ||
          selectGridRecords[0].status == 7 ||
          selectGridRecords[0].status == 8
        ) {
          let params = {
            id: selectGridRecords[0].id
          }
          this.$API.rfxList
            .preCheckUntaxedHistoricalPricesSubmit(params)
            .then((res) => {
              if (res.code == 200 && res.msg.includes('请确认') && res.msg.includes('物料')) {
                this.$dialog({
                  data: {
                    title: this.$t('提示'),
                    message: res.msg
                  },
                  success: () => {
                    this.submit(params)
                  }
                })
              } else {
                this.submit(params)
              }
            })
            .catch((err) => {
              this.$toast({
                type: 'error',
                content: err.msg
              })
            })
        } else {
          this.$toast({
            content: this.$t('此状态下无法提交'),
            type: 'warning'
          })
        }
      }
    },
    submit(params) {
      this.$API.rfxList.preCheckSubmit(params).then((res) => {
        if (res.code === 200) {
          this.$store.commit('startLoading')
          this.$API.rfxList
            .submitPricing(params)
            .then(() => {
              this.$store.commit('endLoading')
              this.$toast({ content: this.$t('提交成功'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
            })
            .catch(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    },

    //表格按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      let idList = []
      _selectGridRecords.map((item) => {
        idList.push(item.id)
      })
      if (e.toolbar.id == 'Add') {
        this.handleAddFn() //新增
      }
      if (e.toolbar.id == 'del') {
        for (let item of _selectGridRecords) {
          if (item.status != 0 && item.status != 7 && item.status != 8) {
            this.$toast({
              content: this.$t('只能删除草稿、撤回、废弃状态的单据'),
              type: 'warning'
            })
            return
          }
        }
        this.handleDelFn(_selectGridRecords, idList) //删除
      }
      if (e.toolbar.id == 'syncSap') {
        this.handleSyncFn(_selectGridRecords)
      }
      if (e.toolbar.id === 'Submit') {
        this.handleSubmitFn(_selectGridRecords) //提交
      }
      if (e.toolbar.id === 'import') this.handleImport()
      if (e.toolbar.id === 'export') this.handleExport(e)
    },
    handleSyncFn(selectGridRecords) {
      if (selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else if (selectGridRecords.length > 1) {
        this.$toast({ content: this.$t('只能选择一行提交'), type: 'warning' })
      } else {
        if (selectGridRecords[0].status == 2 || selectGridRecords[0].status == 6) {
          let params = {
            pointId: selectGridRecords[0].id
          }
          this.$store.commit('startLoading')
          this.$API.rfxList
            .syncSap(params)
            .then(() => {
              this.$store.commit('endLoading')
              this.$toast({ content: this.$t('提交成功'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
            })
            .catch(() => {
              this.$store.commit('endLoading')
            })
        } else {
          this.$toast({
            content: this.$t('只能同步审批通过或同步失败的数据'),
            type: 'warning'
          })
        }
      }
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
    },
    //单元格标题点击操作
    handleClickCellTitle(e) {
      if (e.field == 'pointNo') {
        this.$router.push({
          name: 'contract-price-detail',
          query: {
            id: e.data.id,
            decidePriceType: e.data.decidePriceType,
            status: e.data.status,
            key: this.$utils.randomString()
          }
        })
      }
    },
    //导出
    handleExport(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (_selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      // 先要点击“查询”按钮才能取得queryBuilderRules
      // const queryBuilderRules =
      //   this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      this.$store.commit('startLoading')
      const params = {
        page: { current: 1, size: 500000 },
        // ...queryBuilderRules,
        defaultRules: [
          {
            field: 'decidePriceType',
            operator: 'equal',
            value: 7
          },
          {
            field: 'id',
            operator: 'in',
            value: _selectGridRecords.map((e) => e.id)
          }
        ]
      } // 筛选条件
      this.$API.rfxList
        .excelExport(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
          this.$store.commit('endLoading')
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    //导入
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('上传/导入'),
          importApi: this.$API.rfxList.excelimport,
          asyncParams: {},
          downloadTemplateApi: this.$API.rfxList.exportTpl
        },
        success: () => {
          //刷新列表
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    }
  }
}
</script>
