<template>
  <div v-if="show && sport" class="tree-grid-detail" :class="[active ? 'active' : '']">
    <mt-DataGrid
      v-if="sport"
      :data-source="pageConfig.dataSource"
      :column-data="pageConfig.columnData"
      ref="dataGrid"
    ></mt-DataGrid>

    <!-- <mt-template-page ref="template123" :template-config="pageConfig" /> -->
  </div>
</template>
<script>
import { createEditInstance } from '@/utils/ej/dataGrid/index'
import checkSelectedItemCode from 'COMPONENTS/NormalEdit/checkSelectItemCodeAll'
export default {
  data() {
    return {
      data: {},
      activeData: {},
      pageConfig: {
        dataSource: [],
        columnData: []
      },
      columnData: [],
      sport: true,
      toolbar: ['Edit', 'Update', 'Cancel'],
      editing: {
        allowEditing: true
      },
      unitNameList: [],
      tab2ItemDataList: [],
      tab2Column: []
    }
  },
  watch: {
    activeData: {
      handler(v) {
        this.data.taskData = v
      },
      deep: true
    }
  },
  computed: {
    show() {
      return this.data.taskData.enableItem == 1 ? true : false
    },
    active() {
      return JSON.stringify(this.activeData) == '{}' ? false : true
    }
  },
  created() {
    this.init()
    this.getUnitName()
  },
  mounted() {
    this.tab2ItemDataList = this.data.itemDataList
    this.tab2Column = this.data?.columnList?.map((item) => {
      return {
        columnId: item.id,
        columnAlias: item.columnAlias
      }
    })
    // EventBus.$on(`treeGridRowSelect-${this.data.id}`, (e) => {
    //   console.log(`treeGridRowSelect-${this.data.id}`, e);
    //   this.activeData = e;
    //   if (this.activeData.columnData) {
    //     this.$set(this.pageConfig, "columnData", this.activeData.columnData);
    //   }
    //   if (this.activeData.dataSource) {
    //     this.$set(this.pageConfig, "dataSource", this.activeData.dataSource);
    //   }
    // });
  },
  methods: {
    getUnitName() {
      this.$API.masterData.pagedQueryUnit().then((res) => {
        this.unitNameList = res?.data?.records || []
      })
    },
    init() {
      if (this.data.taskData.enableItem == 1) {
        console.log('this.data', this.data)
        const editInstance = createEditInstance().component(
          'checkSelectedItemCode',
          checkSelectedItemCode
        )
        if (this.data.columnList.length > 0) {
          const a = this.data.columnList.map((item) => {
            return {
              field: item.id,
              headerText: item.columnAlias || item.columnName,
              edit: editInstance.create({
                getEditConfig: (ctx) => {
                  console.log('rowData', ctx)
                  if (item.valueSet == 'item') {
                    return {
                      type: 'checkSelectedItemCode',
                      field: 'itemCode'
                    }
                  } else if (item.valueSet == 'unit') {
                    return {
                      type: 'select',
                      fields: { text: 'unitCode', value: 'unitCode' },
                      'show-clear-button': true,
                      dataSource: this.unitNameList
                    }
                  } else if (item.valueSet == 'cost_factor') {
                    return {
                      type: 'checkSelectedItemCode',
                      sourcingObjType: 'cost_factor',
                      field: 'itemCode'
                    }
                  } else if (item.columnType == 0) {
                    return {
                      type: 'number'
                    }
                  } else {
                    return {
                      type: 'text'
                    }
                  }
                }
              })
            }
          })
          this.$set(this.pageConfig, 'columnData', a)
          let c = []
          let b = {}
          this.data.itemDataList.forEach((item, i) => {
            b = {}
            item.forEach((item2) => {
              // a[i][item2.columnAlias] = item2.dataValue;
              b[item2.columnId] = item2.dataValue == -999 ? '******' : item2.dataValue
              b.rowKey = item2.rowKey
            })
            console.log('queryLeafNodeData', i, b)
            c.push(b)
          })
          this.$set(this.pageConfig, 'dataSource', c)
        }
      }
    },
    getType(type) {
      switch (type) {
        case 0:
          return 'number'
        case 1:
          return 'text'
        case 2:
          return 'select'
        default:
          return 'text'
      }
    },
    actionComplete(e) {
      console.log('actionComplete', e)
    },
    saveItemData(e) {
      if (e.requestType != 'save') {
        return
      }
      let arr = []
      let b = [...this.tab2ItemDataList]
      let c = []
      console.log('this.tab2ItemDataList', this.tab2ItemDataList, e)
      for (let i = 0; i < b.length; i++) {
        b[i].forEach((item) => {
          if (item.rowKey == e.data.rowKey) {
            c.push(item)
          }
        })
      }
      for (const key in e.data) {
        if (key == 'id' || key == 'rowKey') {
          continue
        }
        console.log('saveItemData', key, e.data[key], this.tab2Column)
        arr.push({ dataValue: e.data[key] })
      }
      console.log('arrcc', arr, c)
      if (c.length > 0) {
        arr.forEach((item, i) => {
          item.columnId = this.tab2Column[i].columnId
          item.columnAlias = this.tab2Column[i].columnAlias
          item.id = c[i]?.id
        })
      } else {
        arr.forEach((item, i) => {
          item.columnId = this.tab2Column[i].columnId
          item.columnAlias = this.tab2Column[i].columnAlias
        })
      }
      console.log('arrarrarr', arr)
      let params = {
        itemDataSaveRequestList: [arr],
        rfxItemId: this.data.rfxItemId,
        rfxCostModelItemId: this.data.id
      }
      this.$API.rfxCostModel.saveItemData(params).then(() => {
        // this.queryLeafNodeData();
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.tree-grid-detail {
  border: 1px solid #e0e0e0;
  padding: 10px 40px;
  // background: #e0e0e0;
}
.active {
  background: #e0e0e0;
}
/deep/ .mt-data-grid {
  border: 1px solid #e0e0e0;
  // .e-grid {
  //   .e-content {
  //     height: 300px !important;
  //   }
  // }
}
</style>
