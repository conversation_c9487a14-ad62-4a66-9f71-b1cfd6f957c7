import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '60',
    type: 'checkbox'
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂')
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'costModelQuote',
    headerText: i18n.t('成本模型报价'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('是'), 1: i18n.t('否') }
    }
  },
  {
    field: 'expertLevel',
    headerText: i18n.t('成本模板')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('最小起折点点')
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('单价(不含税)')
  },
  {
    field: 'stepQuote',
    headerText: i18n.t('是否阶梯报价'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('是'), 1: i18n.t('否') }
    }
  },
  {
    field: 'biddingItemStageStrName',
    headerText: i18n.t('阶梯价格')
  },
  {
    field: 'requireQuantity',
    headerText: i18n.t('需求数量')
  },
  {
    field: 'bidCurrencyName',
    headerText: i18n.t('币种')
  },
  {
    field: 'bidTaxRateValue',
    headerText: i18n.t('税率(%)')
  },
  {
    field: 'minPackageQuantity',
    headerText: i18n.t('最小包装量/MPQ')
  },
  {
    field: 'unitName',
    headerText: i18n.t('基本单位')
  },
  {
    field: 'purUnitName',
    headerText: i18n.t('订单单位')
  },
  {
    field: 'minPurQuantity',
    headerText: i18n.t('最小采购量/MOQ')
  },
  {
    field: 'leadTime',
    headerText: i18n.t('L/T')
  },
  {
    field: 'unconditionalLeadTime',
    headerText: i18n.t('无条件L/T')
  },
  {
    field: 'quoteEffectiveStartDate',
    headerText: i18n.t('生效日期')
  },
  {
    field: 'quoteEffectiveEndDate',
    headerText: i18n.t('失效日期')
  },
  {
    field: 'quoteEffectiveEndDate',
    headerText: i18n.t('议价理由')
  },
  {
    field: 'quoteEffectiveEndDate',
    headerText: i18n.t('报价说明')
  },
  {
    field: 'quoteEffectiveEndDate',
    headerText: i18n.t('报价轮次')
  },
  {
    field: 'quoteEffectiveEndDate',
    headerText: i18n.t('本次报价次数')
  },
  {
    field: 'bidCurrencyName',
    headerText: i18n.t('币种')
  },
  {
    field: 'priceUnitName',
    headerText: i18n.t('价格单位')
  },
  {
    field: 'deliveryPlace',
    headerText: i18n.t('直送地')
  },
  {
    field: 'deliveryPlace',
    headerText: i18n.t('建议生效日期')
  },
  {
    field: 'deliveryPlace',
    headerText: i18n.t('最高限价')
  },
  {
    field: 'deliveryPlace',
    headerText: i18n.t('最低限价')
  },
  {
    field: 'deliveryPlace',
    headerText: i18n.t('最小报价幅度')
  },
  {
    field: 'spec',
    headerText: i18n.t('规格描述')
  },
  {
    field: 'purExecutorName',
    headerText: i18n.t('材质')
  }
]
