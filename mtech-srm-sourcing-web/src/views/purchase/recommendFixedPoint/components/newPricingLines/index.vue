<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
        v-if="pageConfig[0].grid.columnData.length > 0"
      />
    </div>
  </mt-dialog>
</template>

<script>
import { columnData } from './config'
export default {
  data() {
    return {
      pageConfig: [
        {
          useToolTemplate: false,
          grid: {
            allowFiltering: true,
            asyncConfig: {
              queryBuilderWrap: 'queryBuilderDTO',
              url: this.$API.rfxList.pageToBiddingItem,
              params: {
                pointId: this.modalData.pointId
              }
            },
            lineIndex: true,
            columnData: columnData,
            dataSource: []
          }
        }
      ],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  watch: {
    modalData: {
      handler(newVal) {
        console.log(5050505, newVal)
      }
    }
  },

  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    confirm() {
      //获取当前行的数据
      const ref = this.$refs.templateRef.getCurrentTabRef()
      this.$emit('confirm-function', ref.gridRef.getMtechGridRecords())
    },
    cancel() {
      this.$emit('cancel-function')
    },
    handleClickToolBar(e) {
      console.log(123, e)
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  height: 100%;
  width: 100%;
}
</style>
