import { getValueByPath } from '@/utils/obj'
import { createEditInstance, Formatter } from '@/utils/ej/dataGrid/index'
import selectedItemCode from 'COMPONENTS/NormalEdit/selectItemCode' // 物料
import cellChanged from 'COMPONENTS/NormalEdit/cellChanged' // 单元格被改变（纯展示）
import { i18n } from '@/main.js'
import { useFiltering } from '@/utils/ej/select'
import directCellView from 'COMPONENTS/NormalEdit/directTemplate/cellShow'
import selectSourceNo from 'COMPONENTS/NormalEdit/selectSourceNo' // 来源单号
import Select from '../editComponents/Select.vue'
import Decimal from 'decimal.js'
import Vue from 'vue'
import selectAllCostFactorCode from 'COMPONENTS/NormalEdit/selectAllCostFactorCode' // 成本因子
import selectAllAttrCode from 'COMPONENTS/NormalEdit/selectAllAttrCode' // 属性
import { searchOptionsList } from '@/constants'

const _isKtFlag = sessionStorage.getItem('_isKtFlag')
import { addArrTextField, makeTextFields } from '@/views/common/columnData/utils'
import { cloneDeep } from 'lodash'
import { formatTime } from '@/utils/utils'
import axios from 'axios'
export const columnDataOne = (
  {
    purGroupList = [],
    taxList = [],
    companyList = [],
    type,
    getSupplierList,
    queryBuyerOrg,
    categoryPagedQuery,
    querySite,
    getLastPrice,
    isProperty,
    getDefaultValue,
    getPlanTime,
    getPurGroup,
    getPropertyPrice,
    supplierCode = ''
  } = {},
  bus
) => {
  const quoteAttributeList = [
    {
      text: i18n.t('标准价'),
      value: 'standard_price'
    },
    {
      text: i18n.t('寄售价'),
      value: 'mailing_price'
    },
    {
      text: i18n.t('委外价'),
      value: 'outsource'
    }
  ]
  const propertyPriceList = [
    {
      text: i18n.t('新品'),
      value: 'new'
    },
    {
      text: i18n.t('日常'),
      value: 'daily'
    },
    {
      text: i18n.t('招标'),
      value: 'bidding'
    }
  ]
  const quoteModeList = [
    {
      text: i18n.t('按照入库'),
      value: 'in_warehouse'
    },
    {
      text: i18n.t('按出库'),
      value: 'out_warehouse'
    },
    {
      text: i18n.t('按订单日期'),
      value: 'order_date'
    }
  ]
  const editInstance = createEditInstance()
    .onInput((ctx, { field, rowData, value }) => {
      console.log('onInput', { ctx, rowData, value, field }, ctx.getValueByField('itemCode'))

      if (field === 'supplierName') {
        // 供应商
        const { dataSource, fields } = ctx.getOptions(field)
        const row = dataSource.find((e) => e[fields?.value || 'value'] === value)
        if (row) {
          ctx.setValueByField('supplierCode', row.supplierCode)
          ctx.setValueByField('supplierId', row.id)
        }
      } else if (field === 'bidCurrencyName') {
        ctx.setValueByField('bidCurrencyCode', value)
      } else if (field === 'companyName') {
        // 公司被编辑
        ctx.setValueByField('purOrgName', null)
        ctx.setValueByField('purOrgCode', null)
        ctx.setValueByField('purOrgId', null)
        ctx.setValueByField('siteName', null)
        ctx.setValueByField('siteCode', null)
        ctx.setValueByField('siteId', null)
        const { dataSource, fields } = ctx.getOptions(field)
        const row = dataSource.find((e) => e[fields?.value || 'value'] === value)

        if (row) {
          bus.$emit('purOrgNameChange', row.id)
          sessionStorage.setItem('priceCompanyId', row.id)
          ctx.setValueByField('companyCode', row.orgCode)
          ctx.setValueByField('companyId', row.id)
        }
      } else if (field === 'purOrgName') {
        // 采购组织被编辑
        const { dataSource, fields } = ctx.getOptions(field)
        const row = dataSource.find((e) => e[fields?.value || 'value'] === value)

        if (row) {
          bus.$emit('siteNameChange', row.id)
          ctx.setValueByField('purOrgCode', row.organizationCode)
          ctx.setValueByField('purOrgId', row.id)
          if (ctx.getValueByField('companyCode') && supplierCode) {
            axios
              .get(
                `/api/sourcing/tenant/point2/queryTaxRate?companyCode=${ctx.getValueByField(
                  'companyCode'
                )}&purOrgCode=${row.organizationCode}&supplierCode=${supplierCode}`
              )
              .then((res) => {
                if (res?.data) {
                  ctx.setValueByField('bidTaxRateCode', res.data.taxRateCode)
                  ctx.setValueByField('bidTaxRateName', res.data.taxRateName)
                  ctx.setValueByField('bidTaxRateValue', res.data.taxRateValue)
                }
              })
          }
        }
      } else if (field === 'siteName') {
        // 工厂被编辑
        const { dataSource, fields } = ctx.getOptions(field)
        const row = dataSource.find((e) => e[fields?.value || 'value'] === value)
        if (row) {
          ctx.setValueByField('siteCode', row.orgCode)
          ctx.setValueByField('siteId', row.id)
          sessionStorage.setItem('organizationId', row.id)
          sessionStorage.setItem('pricingSiteCode', row.orgCode)
        }

        if (row.orgCode) {
          let { _supplierCode, _decidePriceType, _priceClassification, _isProperty } = JSON.parse(
            sessionStorage.getItem('fixedPointObj')
          )
          let _param
          if (_decidePriceType == 3) {
            _param = {
              isLowPrice: 1,
              itemCode: _isProperty == 0 ? rowData?.itemCode : rowData?.propertyCode,
              priceClassification: _priceClassification,
              purOrgCode: rowData?.purOrgCode,
              siteCode: row.orgCode,
              companyCode: rowData?.companyCode,
              supplierCode: _supplierCode
            }
          } else if (_decidePriceType == 7) {
            _param = {
              isLowPrice: 1,
              itemCode: rowData?.itemCode,
              priceClassification: 4,
              purOrgCode: rowData?.purOrgCode,
              siteCode: row.orgCode,
              companyCode: rowData?.companyCode,
              supplierCode: _supplierCode
            }
          } else if (_decidePriceType == 4) {
            _param = {
              isLowPrice: 0,
              itemCode: _isProperty == 0 ? rowData?.itemCode : rowData?.propertyCode,
              priceClassification: _priceClassification,
              purOrgCode: rowData?.purOrgCode,
              siteCode: row.orgCode,
              companyCode: rowData?.companyCode,
              supplierCode: _supplierCode
            }
          } else {
            _param = {
              isLowPrice: 1,
              itemCode: rowData?.itemCode,
              priceClassification: _priceClassification,
              purOrgCode: rowData?.purOrgCode,
              siteCode: row.orgCode,
              companyCode: rowData?.companyCode,
              supplierCode: _supplierCode
            }
          }

          if (_param?.companyCode && _param?.purOrgCode && row?.orgCode && _param?.itemCode) {
            getLastPrice(_param).then((res) => {
              if (_decidePriceType == 3) {
                let _lastBasePriceParam = {
                  isLowPrice: 0,
                  itemCode: rowData?.itemCode,
                  priceClassification: _priceClassification,
                  purOrgCode: rowData?.purOrgCode,
                  siteCode: row.orgCode,
                  companyCode: rowData?.companyCode,
                  supplierCode: _supplierCode
                }
                getLastPrice(_lastBasePriceParam).then((item) => {
                  //上次含税基价
                  editInstance.setValueByField('lastTaxedBasicPrice', item.data.lastTaxedBasicPrice)
                  if (item.data?.lastTaxedBasicPrice && Number(item.data.lastTaxedBasicPrice) > 0) {
                    editInstance.setValueByField('propertyPrice', 'daily')
                  } else {
                    editInstance.setValueByField('propertyPrice', 'new')
                  }
                  //最低含税基价
                  editInstance.setValueByField('lowTaxedBasicPrice', res.data.lowTaxedBasicPrice)
                  let taxedBasicPrice = editInstance.getValueByField('taxedBasicPrice')
                  // 价格差异 (含税基价-最低含税基价)/ 最低含税基价*100%
                  if (taxedBasicPrice == null || res.data.lowTaxedBasicPrice == null) {
                    editInstance.setValueByField('priceDifference', 0)
                  } else {
                    let _priceDifference = (
                      (Number(taxedBasicPrice) - Number(res.data.lowTaxedBasicPrice)) /
                      Number(res.data.lowTaxedBasicPrice)
                    ).toFixed(5)
                    editInstance.setValueByField('priceDifference', _priceDifference)
                  }
                })
              } else if (_decidePriceType == 4) {
                // 上次含税均价
                editInstance.setValueByField(
                  'lastTaxedAveragePrice',
                  res.data.lastTaxedAveragePrice
                )
                // 价格差异  含税均价-上次含税均价
                let _taxedAveragePrice = editInstance.getValueByField('taxedAveragePrice')
                if (_taxedAveragePrice == null || res.data.lastTaxedAveragePrice == null) {
                  editInstance.setValueByField('priceDifference', 0)
                } else {
                  let _priceDifference = (
                    parseFloat(_taxedAveragePrice) - parseFloat(res.data.lastTaxedAveragePrice)
                  ).toFixed(5)
                  editInstance.setValueByField('priceDifference', _priceDifference)
                }
              } else if (_decidePriceType == 7) {
                editInstance.setValueByField(
                  'lowUntaxedExercisePrice',
                  res.data.lowUntaxedExercisePrice
                )
                // 价格差异计算逻辑=单价（不含税）-未税采购最低价
                let _untaxedUnitPrice = editInstance.getValueByField('untaxedUnitPrice')
                if (_untaxedUnitPrice == null || res.data.lowUntaxedExercisePrice == null) {
                  editInstance.setValueByField('priceDifference', 0)
                } else {
                  let _priceDifference = (
                    parseFloat(_untaxedUnitPrice) - parseFloat(res.data.lowUntaxedExercisePrice)
                  ).toFixed(5)
                  editInstance.setValueByField('priceDifference', _priceDifference)
                }
                let _getPropertyPriceParam = {
                  isLowPrice: 0,
                  itemCode: rowData?.itemCode,
                  priceClassification: 4,
                  purOrgCode: rowData?.purOrgCode,
                  siteCode: row.orgCode,
                  supplierCode: _supplierCode
                }
                getPropertyPrice(_getPropertyPriceParam).then((res) => {
                  if (res.code === 200) {
                    editInstance.setValueByField('propertyPrice', res.data)
                  }
                })
              }
            })
          }
          if (row.orgCode && _param?.itemCode) {
            getPlanTime(_param).then((res) => {
              editInstance.setValueByField('plannedDeliveryTime', res?.data?.plannedDeliveryTime)
            })
          }
        }
      } else if (field == 'untaxedUnitPrice') {
        setTaxedUnitPrice()
      } else if (field == 'taxedUnitPrice') {
        setUnTaxedUnitPrice()
      } else if (field == 'referChannel') {
        if (value == '2' && _isKtFlag == 0) {
          ctx.setOptions('taxedUnitPrice', {
            readonly: true,
            disabled: true
          })
          ctx.setOptions('untaxedUnitPrice', {
            readonly: true,
            disabled: true
          })
        } else {
          ctx.setOptions('taxedUnitPrice', {
            readonly: false,
            disabled: false
          })
          ctx.setOptions('untaxedUnitPrice', {
            readonly: false,
            disabled: false
          })
        }
      } else if (field == 'categoryName') {
        const { dataSource, fields } = ctx.getOptions(field)
        const key = rowData?.categoryCode ? 'categoryCode' : fields?.value || 'value'
        const row = dataSource.find((e) => e[key] === rowData[key])
        if (row) {
          if (type == 7) {
            let { _supplierCode } = JSON.parse(sessionStorage.getItem('fixedPointObjForContract'))
            let _param = {
              categoryCode: row.categoryCode,
              companyCode: rowData?.companyCode,
              purOrgCode: rowData?.purOrgCode,
              supplierCode: _supplierCode
            }
            getDefaultValue(_param).then((res) => {
              if (res.code == 200) {
                ctx.setValueByField(
                  'quoteAttribute',
                  res.data?.quoteAttribute ? res.data.quoteAttribute : ''
                )
                ctx.setValueByField('quoteMode', res.data?.quoteMode ? res.data.quoteMode : '')
              }
            })
          }
          ctx.setValueByField('categoryName', row.categoryName)
          ctx.setValueByField('categoryCode', row.categoryCode)
          ctx.setValueByField('categoryId', row.id)
        }
      } else if (field == 'taxedBasicPrice' && type == 3) {
        if (!rowData?.lowTaxedBasicPrice) {
          editInstance.setValueByField('priceDifference', 0)
        } else {
          let _priceDifference = (
            (Number(value) - Number(rowData.lowTaxedBasicPrice)) /
            Number(rowData.lowTaxedBasicPrice)
          ).toFixed(5)
          editInstance.setValueByField('priceDifference', _priceDifference)
        }
      } else if (field == 'taxedAveragePrice' && type == 4) {
        // 价格差异  含税均价-上次含税均价
        if (!rowData?.lastTaxedAveragePrice) {
          editInstance.setValueByField('priceDifference', 0)
        } else {
          let _priceDifference = (
            parseFloat(value) - parseFloat(rowData.lastTaxedAveragePrice)
          ).toFixed(5)
          editInstance.setValueByField('priceDifference', _priceDifference)
        }
      } else if (field == 'untaxedUnitPrice' && type == 7) {
        // 价格差异计算逻辑=单价（不含税）-未税采购最低价
        if (!rowData?.lowUntaxedExercisePrice) {
          editInstance.setValueByField('priceDifference', 0)
        } else {
          let _priceDifference = (
            parseFloat(value) - parseFloat(rowData.lowUntaxedExercisePrice)
          ).toFixed(5)
          editInstance.setValueByField('priceDifference', _priceDifference)
        }
      } else if (field === 'quoteEffectiveEndDate') {
        if (value && value != '0') {
          let _tempDate = formatTime(new Date(value), 'YYYY-mm-dd')
          _tempDate = Number(new Date(_tempDate + ' 23:59:59'))
          editInstance.setValueByField('quoteEffectiveEndDate', _tempDate)
        }
      }
    })
    .onChange(async (ctx, { field }, event) => {
      const val =
        typeof event != Object || !Object.prototype.hasOwnProperty.call(event, 'value')
          ? ''
          : event.value === null
          ? ''
          : event.value
      if (field === 'stepQuote') {
        bus.$emit('changeStepQuote', val)
        ctx.setOptions('untaxedUnitPrice', {
          readonly: false,
          disabled: false
        })
        if (val == 0) {
          ctx.setValueByField('stepQuoteType', '')
          ctx.setOptions('stepQuoteType', {
            readonly: true,
            disabled: true
          })
        } else {
          ctx.setOptions('stepQuoteType', {
            readonly: false,
            disabled: false
          })
        }
      } else if (field === 'stepQuoteType') {
        bus.$emit('changeStepQuoteType', val)
      } else if (field === 'referChannel') {
        bus.$emit('changeReferChannel', val)
      } else if (field === 'bidTaxRateName') {
        let taxRateInfo = taxList.find((e) => e.taxItemName == val)
        editInstance.setValueByField('bidTaxRateValue', taxRateInfo.taxRate)
        editInstance.setValueByField('bidTaxRateCode', taxRateInfo.taxItemCode)
        setTaxedUnitPrice()
        setUnTaxedUnitPrice()
      } else if (field === 'purOrgName' && event.e) {
        editInstance.setValueByField('siteName', null)
        editInstance.setValueByField('siteCode', null)
        editInstance.setValueByField('siteId', null)
      } else if (field === 'purGroupCode') {
        if (event?.itemData) {
          editInstance.setValueByField('purGroupId', event.itemData.id)
          editInstance.setValueByField('purGroupName', event.itemData.text)
        } else {
          editInstance.setValueByField('purGroupId', null)
          editInstance.setValueByField('purGroupName', null)
        }
      }
    })
  let busFields = [
    'bidTaxRateCode',
    'bidTaxRateName',
    'bidTaxRateValue',
    'supplierCode',
    'pointBiddingItemStage',
    'supplierId',
    'supplierName',
    'deliveryPlace',
    'itemId',
    'itemName',
    'taxedUnitPrice',
    'untaxedUnitPrice',
    'purOrgName',
    'siteName',
    'itemCode',
    'propertyName',
    'propertyCode'
  ]
  if (type != 3 && type != 4) {
    busFields = busFields.concat(['categoryCode', 'categoryId', 'categoryName'])
  }
  busFields.forEach((busField) => {
    bus.$on(`${busField}Change`, async (data) => {
      if (busField == 'pointBiddingItemStage') {
        if (!!data && data.length > 0) {
          editInstance.setValueByField('untaxedUnitPrice', data[0].untaxedUnitPrice)
          setTaxedUnitPrice()
          editInstance.setValueByField('stepQuote', 1)
          editInstance.setValueByField('stepQuoteType', 1)
          editInstance.setValueByField('biddingItemStageStrName', JSON.stringify(data))
          editInstance.setValueByField('pointBiddingItemStage', data)
        }
      } else if (busField == 'supplierName') {
        getSupplierList({
          fuzzyNameOrCode: data
          // organizationCode: sessionStorage.getItem("organizationCode"),
        }).then((r) => {
          editInstance.setOptions('supplierName', {
            dataSource: Array.isArray(r?.data) ? r.data : []
          })
        })
        editInstance.setValueByField(busField, data)
      } else if (busField == 'purOrgName') {
        let _records = await queryBuyerOrg({ orgId: data }).catch(() => {})
        mergebuyerOrgDataSource(_records.data)
      } else if (busField == 'siteName') {
        let _companyId = sessionStorage.getItem('priceCompanyId')
        let _records = await querySite({
          buOrgId: data,
          companyId: _companyId
        }).catch(() => {})
        mergeSiteDataSource(_records.data)
      } else if (busField == 'itemCode') {
        sessionStorage.setItem('currentPriceItemCode', data)
        console.log('itemCode---', data, editInstance)
        editInstance.setValueByField(busField, data)
        let _tempRecord = cloneDeep(editInstance.rowData)
        if (data) {
          // editInstance.setOptions('companyName', {
          //   readonly: true,
          //   disabled: true
          // })
          // editInstance.setOptions('purOrgName', {
          //   readonly: true,
          //   disabled: true
          // })
          // editInstance.setOptions('siteName', {
          //   readonly: true,
          //   disabled: true
          // })
          let { _supplierCode, _decidePriceType, _priceClassification } = JSON.parse(
            sessionStorage.getItem('fixedPointObj')
          )
          let _param
          if (_decidePriceType == 7) {
            //   let { _purOrgCode, _siteCode, _companyCode } = JSON.parse(
            //     sessionStorage.getItem('fixedPointObjForContract')
            //   )
            //   _param = {
            //     isLowPrice: 1,
            //     itemCode: data,
            //     priceClassification: 4,
            //     purOrgCode: _purOrgCode,
            //     siteCode: _siteCode,
            //     companyCode: _companyCode,
            //     supplierCode: _supplierCode
            //   }
            _param = {
              isLowPrice: 1,
              itemCode: data,
              priceClassification: 4,
              purOrgCode: _tempRecord?.purOrgCode,
              siteCode: _tempRecord?.siteCode,
              companyCode: _tempRecord?.companyCode,
              supplierCode: _supplierCode
            }
            if (_tempRecord?.siteCode) {
              let _getPurGroupParams = {
                itemCode: data, // 物料code
                organizationCode: _tempRecord.siteCode //工厂code
              }
              //查询采购组
              getPurGroup(_getPurGroupParams).then((res) => {
                editInstance.setValueByField('purGroupName', res.data?.purchaseGroupName)
                editInstance.setValueByField('purGroupCode', res.data?.purchaseGroupCode)
              })
            }
          } else if (_decidePriceType == 4) {
            _param = {
              isLowPrice: 0,
              itemCode: data,
              priceClassification: _priceClassification,
              purOrgCode: _tempRecord?.purOrgCode,
              siteCode: _tempRecord?.siteCode,
              companyCode: _tempRecord?.companyCode,
              supplierCode: _supplierCode
            }
          } else {
            _param = {
              isLowPrice: 1,
              itemCode: data,
              priceClassification: _priceClassification,
              purOrgCode: _tempRecord?.purOrgCode,
              siteCode: _tempRecord?.siteCode,
              companyCode: _tempRecord?.companyCode,
              supplierCode: _supplierCode
            }
          }

          if (_param?.companyCode && _param?.purOrgCode && _param?.siteCode) {
            getLastPrice(_param).then((res) => {
              if (_decidePriceType == 3) {
                let _lastBasePriceParam = {
                  isLowPrice: 0,
                  itemCode: data,
                  priceClassification: _priceClassification,
                  purOrgCode: _tempRecord?.purOrgCode,
                  siteCode: _tempRecord?.siteCode,
                  companyCode: _tempRecord?.companyCode,
                  supplierCode: _supplierCode
                }
                getLastPrice(_lastBasePriceParam).then((item) => {
                  //上次含税基价
                  editInstance.setValueByField('lastTaxedBasicPrice', item.data.lastTaxedBasicPrice)
                  if (item.data?.lastTaxedBasicPrice && Number(item.data.lastTaxedBasicPrice) > 0) {
                    editInstance.setValueByField('propertyPrice', 'daily')
                  } else {
                    editInstance.setValueByField('propertyPrice', 'new')
                  }
                  //最低含税基价
                  editInstance.setValueByField('lowTaxedBasicPrice', res.data.lowTaxedBasicPrice)
                  let taxedBasicPrice = editInstance.getValueByField('taxedBasicPrice')
                  // 价格差异 (含税基价-最低含税基价)/ 最低含税基价*100%
                  if (taxedBasicPrice == null || res.data.lowTaxedBasicPrice == null) {
                    editInstance.setValueByField('priceDifference', 0)
                  } else {
                    let _priceDifference = (
                      (Number(taxedBasicPrice) - Number(res.data.lowTaxedBasicPrice)) /
                      Number(res.data.lowTaxedBasicPrice)
                    ).toFixed(5)
                    editInstance.setValueByField('priceDifference', _priceDifference)
                  }
                })
              } else if (_decidePriceType == 4) {
                // 上次含税均价
                editInstance.setValueByField(
                  'lastTaxedAveragePrice',
                  res.data.lastTaxedAveragePrice
                )
                // 价格差异  含税均价-上次含税均价
                let _taxedAveragePrice = editInstance.getValueByField('taxedAveragePrice')
                if (_taxedAveragePrice == null || res.data.lastTaxedAveragePrice == null) {
                  editInstance.setValueByField('priceDifference', 0)
                } else {
                  let _priceDifference = (
                    parseFloat(_taxedAveragePrice) - parseFloat(res.data.lastTaxedAveragePrice)
                  ).toFixed(5)
                  editInstance.setValueByField('priceDifference', _priceDifference)
                }
              } else if (_decidePriceType == 7) {
                let _getPropertyPriceParam = {
                  isLowPrice: 0,
                  itemCode: data,
                  priceClassification: 4,
                  purOrgCode: _tempRecord?.purOrgCode,
                  siteCode: _tempRecord?.siteCode,
                  supplierCode: _supplierCode
                }
                getPropertyPrice(_getPropertyPriceParam).then((res) => {
                  if (res.code === 200) {
                    editInstance.setValueByField('propertyPrice', res.data)
                  }
                })
                editInstance.setValueByField(
                  'lowUntaxedExercisePrice',
                  res.data.lowUntaxedExercisePrice
                )
                // 价格差异计算逻辑=单价（不含税）-未税采购最低价
                let _untaxedUnitPrice = editInstance.getValueByField('untaxedUnitPrice')
                if (_untaxedUnitPrice == null || res.data.lowUntaxedExercisePrice == null) {
                  editInstance.setValueByField('priceDifference', 0)
                } else {
                  let _priceDifference = (
                    parseFloat(_untaxedUnitPrice) - parseFloat(res.data.lowUntaxedExercisePrice)
                  ).toFixed(5)
                  editInstance.setValueByField('priceDifference', _priceDifference)
                }
              }
            })
          }
          if (_tempRecord?.siteCode) {
            getPlanTime(_param).then((res) => {
              editInstance.setValueByField('plannedDeliveryTime', res?.data?.plannedDeliveryTime)
            })
            if (_decidePriceType == 3) {
              let _getPurGroupParams = {
                itemCode: data, // 物料code
                organizationCode: _tempRecord.siteCode //工厂code
              }
              //查询采购组
              getPurGroup(_getPurGroupParams).then((res) => {
                editInstance.setValueByField('purGroupName', res.data?.purchaseGroupName)
                editInstance.setValueByField('purGroupCode', res.data?.purchaseGroupCode)
              })
            }
          }
        }
      } else if (busField == 'propertyCode') {
        console.log('propertyCode---', data, editInstance)
        editInstance.setValueByField(busField, data)
        let _tempRecord = cloneDeep(editInstance.rowData)
        if (data) {
          let { _supplierCode, _decidePriceType, _priceClassification } = JSON.parse(
            sessionStorage.getItem('fixedPointObj')
          )
          let _param = {
            isLowPrice: 1,
            itemCode: data,
            priceClassification: _priceClassification,
            purOrgCode: _tempRecord?.purOrgCode,
            siteCode: _tempRecord?.siteCode,
            companyCode: _tempRecord?.companyCode,
            supplierCode: _supplierCode
          }

          if (_param?.companyCode && _param?.purOrgCode && _param?.siteCode) {
            getLastPrice(_param).then((res) => {
              if (_decidePriceType == 3) {
                let _lastBasePriceParam = {
                  isLowPrice: 0,
                  itemCode: data,
                  priceClassification: _priceClassification,
                  purOrgCode: _tempRecord?.purOrgCode,
                  siteCode: _tempRecord?.siteCode,
                  companyCode: _tempRecord?.companyCode,
                  supplierCode: _supplierCode
                }
                getLastPrice(_lastBasePriceParam).then((item) => {
                  //上次含税基价
                  editInstance.setValueByField('lastTaxedBasicPrice', item.data.lastTaxedBasicPrice)
                  if (item.data?.lastTaxedBasicPrice && Number(item.data.lastTaxedBasicPrice) > 0) {
                    editInstance.setValueByField('propertyPrice', 'daily')
                  } else {
                    editInstance.setValueByField('propertyPrice', 'new')
                  }
                  //最低含税基价
                  editInstance.setValueByField('lowTaxedBasicPrice', res.data.lowTaxedBasicPrice)
                  let taxedBasicPrice = editInstance.getValueByField('taxedBasicPrice')
                  // 价格差异 (含税基价-最低含税基价)/ 最低含税基价*100%
                  if (taxedBasicPrice == null || res.data.lowTaxedBasicPrice == null) {
                    editInstance.setValueByField('priceDifference', 0)
                  } else {
                    let _priceDifference = (
                      (Number(taxedBasicPrice) - Number(res.data.lowTaxedBasicPrice)) /
                      Number(res.data.lowTaxedBasicPrice)
                    ).toFixed(5)
                    editInstance.setValueByField('priceDifference', _priceDifference)
                  }
                })
              }
            })
          }
          if (_tempRecord?.siteCode) {
            getPlanTime(_param).then((res) => {
              editInstance.setValueByField('plannedDeliveryTime', res?.data?.plannedDeliveryTime)
            })
          }
        }
        // } else if (busField == 'itemName') {
        //   if (!data) {
        //     editInstance.setOptions('companyName', {
        //       readonly: false,
        //       disabled: false
        //     })
        //     editInstance.setOptions('purOrgName', {
        //       readonly: false,
        //       disabled: false
        //     })
        //     editInstance.setOptions('siteName', {
        //       readonly: false,
        //       disabled: false
        //     })
        //   }
      } else {
        editInstance.setValueByField(busField, data)
      }
    })
  })
  let buyerOrgSource = []
  const querybuyerOrgDataSource = async (arg) => {
    const records = await purOrgQueryEx(arg)
    mergebuyerOrgDataSource(records)
    return records
  }
  function mergebuyerOrgDataSource(records) {
    addArrTextField(records, 'organizationCode', 'organizationName')
    buyerOrgSource = records
    initBuyerOrgSource()
  }
  // 获取采购组织名称
  async function purOrgQueryEx(value) {
    console.log('purOrgQueryEx---', value)
    if (!value) {
      return []
    }
    let params = {
      orgId: value
    }

    const res = await queryBuyerOrg(params).catch(() => {})
    const records = res?.data || []
    return addArrTextField(records, 'organizationCode', 'organizationName')
  }

  function initBuyerOrgSource() {
    editInstance.setOptions('purOrgName', {
      dataSource: buyerOrgSource
    })
    editInstance.setOptions('purOrgCode', {
      dataSource: buyerOrgSource
    })
  }

  let siteSource = []
  const querySiteDataSource = async (arg) => {
    const records = await siteQueryEx(arg)
    mergeSiteDataSource(records)
    return records
  }
  function mergeSiteDataSource(records) {
    addArrTextField(records, 'orgCode', 'orgName')
    siteSource = records
    initSiteSource()
  }
  // 获取工厂名称
  async function siteQueryEx(value) {
    console.log('siteQueryEx---', value)
    if (!value) {
      return []
    }
    let _companyId = sessionStorage.getItem('priceCompanyId')
    let params = {
      buOrgId: value,
      companyId: _companyId
    }

    const res = await querySite(params).catch(() => {})
    const records = res?.data || []
    return addArrTextField(records, 'orgCode', 'orgName')
  }
  function initSiteSource() {
    editInstance.setOptions('siteName', {
      dataSource: siteSource
    })
    editInstance.setOptions('siteCode', {
      dataSource: siteSource
    })
  }

  let categoryDataSource = []
  function initCategoryDataSource() {
    editInstance.setOptions('categoryName', {
      dataSource: categoryDataSource
    })
    // editInstance.setOptions("categoryCode", {
    //   dataSource: categoryDataSource,
    // });
  }

  // 获取品类名称
  async function categoryPagedQueryEx(value) {
    let params = {
      page: { current: 1, size: 10000 }
    }
    if (value) {
      params = {
        ...params,
        condition: 'or',
        rules: [
          {
            field: 'categoryName',
            type: 'string',
            operator: 'contains',
            value
          },
          {
            field: 'categoryCode',
            type: 'string',
            operator: 'contains',
            value
          }
        ]
      }
    }
    const res = await categoryPagedQuery(params).catch(() => {})
    const records = res?.data?.records || []
    return addArrTextField(records, 'categoryCode', 'categoryName')
  }

  const queryCategoryDataSource = async (...arg) => {
    const records = await categoryPagedQueryEx(...arg)
    mergeCategoryDataSource(records)
    return records
  }

  function mergeCategoryDataSource(records) {
    addArrTextField(records, 'categoryCode', 'categoryName')
    categoryDataSource = records
    initCategoryDataSource()
  }

  const setTaxedUnitPrice = () => {
    let untaxedUnitPrice = editInstance.getValueByField('untaxedUnitPrice')
    let _tempRate = sessionStorage.getItem('priceTaxRateValue')
    let bidTaxRateValue
    if (_tempRate) {
      bidTaxRateValue = _tempRate
    } else {
      bidTaxRateValue = editInstance.getValueByField('bidTaxRateValue')
    }
    if (!untaxedUnitPrice || !bidTaxRateValue) return
    let taxedUnitPrice = new Decimal(untaxedUnitPrice)
      .mul(new Decimal(bidTaxRateValue).add(1))
      .toNumber()
      .toFixed(5)
    editInstance.setValueByField('taxedUnitPrice', taxedUnitPrice)
  }
  const setUnTaxedUnitPrice = () => {
    let taxedUnitPrice = editInstance.getValueByField('taxedUnitPrice')
    let _tempRate = sessionStorage.getItem('priceTaxRateValue')
    let bidTaxRateValue
    if (_tempRate) {
      bidTaxRateValue = _tempRate
    } else {
      bidTaxRateValue = editInstance.getValueByField('bidTaxRateValue')
    }
    if (!taxedUnitPrice || !bidTaxRateValue) return
    let untaxedUnitPrice = new Decimal(taxedUnitPrice)
      .div(new Decimal(bidTaxRateValue).add(1))
      .toNumber()
      .toFixed(5)
    if (type == 7) {
      // 价格差异计算逻辑=单价（不含税）-未税采购最低价
      let _lowUntaxedExercisePrice = editInstance.getValueByField('lowUntaxedExercisePrice')
      if (!_lowUntaxedExercisePrice) {
        editInstance.setValueByField('priceDifference', 0)
      } else {
        let _priceDifference = (
          parseFloat(untaxedUnitPrice) - parseFloat(_lowUntaxedExercisePrice)
        ).toFixed(5)
        editInstance.setValueByField('priceDifference', _priceDifference)
      }
    }
    editInstance.setValueByField('untaxedUnitPrice', untaxedUnitPrice)
  }
  if (type < 2) {
    return [
      {
        width: '60',
        type: 'checkbox',
        allowEditing: false,
        showInColumnChooser: false
      },
      {
        field: 'lineNo',
        headerText: i18n.t('行号'),
        allowEditing: false
      },
      {
        field: 'orderNo',
        headerText: i18n.t('订单号'),
        allowEditing: false
        // editTemplate: () => {
        //   return { template: selectAllOrder }
        // }
      },
      // {
      //   field: "supplierName",
      //   headerText: i18n.t("供应商"),
      //   edit: editInstance.create({
      //     getEditConfig: () => ({
      //       type: "mt-select",
      //       dataSource: supplierList,
      //       fields: { value: "supplierName", text: "supplierName" },
      //       placeholder: i18n.t("供应商"),
      //       "allow-filtering": true,
      //       filtering: useFiltering(function (e) {
      //         getSupplierList({
      //           fuzzyNameOrCode: e.text,
      //           // organizationCode: sessionStorage.getItem("organizationCode"),
      //         }).then((r) => {
      //           editInstance.setOptions("supplierName", {
      //             dataSource: Array.isArray(r?.data) ? r.data : [],
      //           });
      //         });
      //       }),
      //     }),
      //   }),
      //   formatter: ({ field }, item) => {
      //     const cellVal = getValueByPath(item, field);
      //     return supplierList.find((e) => e.value === cellVal)?.text ?? cellVal;
      //   },
      //   searchOptions: {
      //     renameField: "biddingItem.supplierName",
      //   },
      // },
      // {
      //   field: "supplierCode",
      //   headerText: i18n.t("供应商编码"),
      //   edit: editInstance.create({
      //     getEditConfig: () => ({
      //       type: "text",
      //       disabled: true,
      //       readonly: true,
      //     }),
      //   }),
      //   searchOptions: {
      //     renameField: "biddingItem.supplierCode",
      //   },
      // },
      // {
      //   field: "supplierId",
      //   headerText: i18n.t("供应商ID"),
      //   width: 0,allowResizing: false,
      //   edit: editInstance.create({
      //     getEditConfig: () => ({
      //       type: "text",
      //       disabled: true,
      //       readonly: true,
      //     }),
      //   }),
      //   searchOptions: {
      //     renameField: "biddingItem.supplierId",
      //   },
      // },
      // {
      //   field: "siteName",
      //   headerText: i18n.t("工厂"),
      //   edit: editInstance.create({
      //     getEditConfig: () => ({
      //       type: "mt-select",
      //       dataSource: siteNameDataSource,
      //       fields: { value: "orgName", text: "orgName" },
      //       placeholder: i18n.t("请选择工厂"),
      //     }),
      //   }),
      //   formatter: ({ field }, item) => {
      //     const cellVal = getValueByPath(item, field);
      //     return (
      //       siteNameDataSource.find((e) => e.orgName === cellVal)?.orgName ??
      //       cellVal
      //     );
      //   },
      //   searchOptions: {
      //     renameField: "item.siteName",
      //   },
      // },
      // {
      //   field: "siteCode",
      //   headerText: i18n.t("工厂编码"),
      //   edit: editInstance.create({
      //     getEditConfig: () => ({
      //       type: "text",
      //       disabled: true,
      //       readonly: true,
      //     }),
      //   }),
      //   searchOptions: {
      //     renameField: "item.siteCode",
      //   },
      // },
      // {
      //   field: "siteId",
      //   headerText: i18n.t("工厂Id"),
      //   width: 0,allowResizing: false,
      //   edit: editInstance.create({
      //     getEditConfig: () => ({
      //       type: "text",
      //       disabled: true,
      //       readonly: true,
      //     }),
      //   }),
      //   searchOptions: {
      //     renameField: "item.siteId",
      //   },
      // },
      {
        field: 'itemCode',
        headerText: i18n.t('物料编码'),
        width: 200,
        editTemplate: () => {
          return {
            template: selectedItemCode
          }
        }
      },
      {
        field: 'itemName',
        headerText: i18n.t('物料名称'),
        width: 200,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        })
      },
      {
        field: 'itemId',
        headerText: i18n.t('物料Id'),
        width: 0,
        allowResizing: false,
        editTemplate: () => {
          return {
            template: cellChanged
          }
        }
      },
      {
        field: 'categoryId',
        headerText: i18n.t('品类ID'),
        edit: editInstance.create({
          valueConvert: (val) => (val === null ? '' : val),
          getEditConfig: () => ({
            type: 'text',
            readonly: true,
            disabled: true
          })
        })
      },
      {
        field: 'categoryCode',
        headerText: i18n.t('品类编码'),
        width: 200,
        edit: editInstance.create({
          valueConvert: (val) => (val === null ? '' : val),
          getEditConfig: () => ({
            type: 'text',
            readonly: true,
            disabled: true
          })
        })
      },
      {
        field: 'categoryName',
        headerText: i18n.t('品类名称'),
        width: 200,
        allowEditing: true,
        edit: editInstance.create({
          valueConvert: (val) => (val === null ? '' : val),
          getEditConfig: ({ rowData }) => {
            return {
              type: 'select',
              // 有物料的不能编辑
              // > 物料肯定是有品类的，没有品类的物料那应该是数据问题
              // disabled: !!(rowData.itemCode && rowData.categoryCode),
              disabled: true,
              'show-clear-button': true,
              'allow-filtering': true,
              created: async function () {
                // 品类是搜索带出的,非一次性加载
                // 品类名称需要和品类编码的 dataSource 保持一致
                // 品类名称需要和品类编码保持互相联动
                const categoryName = rowData.categoryName
                initCategoryDataSource()
                await queryCategoryDataSource().catch((err) => console.warn(err))
                if (!categoryDataSource.find((e) => e.categoryName === categoryName)) {
                  await queryCategoryDataSource(categoryName).catch((err) => console.warn(err))
                }
                setTimeout(() => {
                  editInstance.setValueByField('categoryName', categoryName)
                }, 0)
              },
              filtering: useFiltering(async function (e) {
                e.updateData(await queryCategoryDataSource(e.text))
              }),
              fields: makeTextFields('categoryName'),
              dataSource: []
            }
          }
        })
      },
      {
        field: 'purGroupCode',
        headerText: i18n.t('采购组'),
        width: 250,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'mt-select',
            dataSource: purGroupList,
            placeholder: i18n.t('请选择'),
            'allow-filtering': true,
            'filter-type': 'Contains'
            // filtering: useFiltering(function (e) {
            //   if (typeof e.text === 'string' && e.text) {
            //     e.updateData(purGroupList.filter((f) => f?.text.indexOf(e.text) > -1))
            //   } else {
            //     e.updateData(purGroupList)
            //   }
            // })
          })
        }),
        formatter: ({ field }, item) => {
          const cellVal = getValueByPath(item, field)
          return purGroupList.find((e) => e.value === cellVal)?.text ?? cellVal
        }
      },
      {
        field: 'unitCode',
        headerText: i18n.t('单位编码'),
        width: 95,
        allowEditing: false,
        editTemplate: () => {
          return {
            template: cellChanged
          }
        }
      },
      {
        field: 'unitName',
        headerText: i18n.t('单位'),
        allowEditing: false,
        editTemplate: () => {
          return {
            template: cellChanged
          }
        }
      },
      {
        field: 'taxedUnitPrice',
        headerText: i18n.t('单价(含税)'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'number',
            precision: '5',
            readonly: _isKtFlag == 0,
            disabled: _isKtFlag == 0
          })
        })
      },
      {
        field: 'untaxedUnitPrice',
        headerText: i18n.t('单价(不含税)'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'number',
            precision: '5',
            readonly: _isKtFlag == 0,
            disabled: _isKtFlag == 0
          })
        })
      },
      {
        field: 'referSourceCode',
        headerText: i18n.t('参考价'),
        width: 120,
        editTemplate: () => {
          return {
            template: selectSourceNo
          }
        }
      },
      {
        field: 'priceUnitName',
        headerText: i18n.t('价格单位'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'select',
            disabled: true,
            readonly: true,
            dataSource: [
              { text: '1', value: '1' },
              { text: '1000', value: '1000' }
            ]
          })
        })
      },
      {
        field: 'lastTaxedAveragePrice',
        headerText: i18n.t('上次含税均价'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text'
          })
        })
      },
      {
        field: 'taxedAveragePrice',
        headerText: i18n.t('含税均价'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text'
          })
        })
      },
      {
        field: 'priceDifference',
        headerText: i18n.t('价格差异'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        })
      },
      {
        field: 'quoteEffectiveStartDate',
        headerText: i18n.t('有效期从'),
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('有效期从')}}</span>
                  </div>
                `
            })
          }
        },
        formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'date',
            format: 'yyyy-MM-dd',
            'time-stamp': true,
            // readonly: getReadOnly(),
            placeholder: i18n.t('有效期从')
          })
        }),
        // format: "yyyy-MM-dd HH:mm:ss",
        // type: "date",
        searchOptions: {
          renameField: 'biddingItem.quoteEffectiveStartDate',
          ...searchOptionsList.timeRange
        }
      },
      {
        field: 'quoteEffectiveEndDate',
        headerText: i18n.t('有效期至'),
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('有效期至')}}</span>
                  </div>
                `
            })
          }
        },
        formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'date',
            format: 'yyyy-MM-dd',
            'time-stamp': true,
            // readonly: getReadOnly(),
            placeholder: i18n.t('有效期至')
          })
        }),
        // format: "yyyy-MM-dd HH:mm:ss",
        // type: "date",
        searchOptions: {
          renameField: 'biddingItem.quoteEffectiveEndDate',
          ...searchOptionsList.timeRange
        }
      },
      {
        field: '',
        headerText: i18n.t('开始数量'),
        allowEditing: false,
        editTemplate: () => {
          return {
            template: cellChanged
          }
        }
      },
      {
        field: '',
        headerText: i18n.t('结束数量'),
        allowEditing: false,
        editTemplate: () => {
          return {
            template: cellChanged
          }
        }
      },
      {
        field: 'propertyPrice',
        headerText: i18n.t('价格属性'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'mt-select',
            dataSource: propertyPriceList,
            placeholder: i18n.t('价格属性'),
            'allow-filtering': false
          }),
          formatter: ({ field }, item) => {
            const cellVal = getValueByPath(item, field)
            return propertyPriceList.find((e) => e.value === cellVal)?.text ?? cellVal
          }
        }),
        valueAccessor: (field, data) => {
          return propertyPriceList.find((e) => e.value == data[field])?.text || ''
        },
        searchOptions: {
          renameField: 'biddingItem.propertyPrice',
          elementType: 'select',
          dataSource: propertyPriceList
        }
      },
      {
        field: 'spec',
        headerText: i18n.t('备注'),
        allowEditing: false,
        editTemplate: () => {
          return {
            template: cellChanged
          }
        }
      }
    ]
  } else if (type == 3) {
    return [
      {
        width: '60',
        type: 'checkbox',
        allowEditing: false,
        showInColumnChooser: false
      },
      {
        field: 'lineNo',
        headerText: i18n.t('行号'),
        allowEditing: false,
        searchOptions: {
          renameField: 'item.lineNo'
        }
      },
      {
        field: 'companyName',
        headerText: i18n.t('公司'),
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('公司')}}</span>
                  </div>
                `
            })
          }
        },
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'mt-select',
            dataSource: companyList,
            fields: { value: 'orgName', text: 'orgName' },
            placeholder: i18n.t('公司'),
            'allow-filtering': true,
            popupWidth: '400px',
            'filter-type': 'Contains'
            // filtering: useFiltering(function (e) {
            //   getSupplierList({
            //     fuzzyNameOrCode: e.text
            //     // organizationCode: sessionStorage.getItem("organizationCode"),
            //   }).then((r) => {
            //     editInstance.setOptions('orgName', {
            //       dataSource: Array.isArray(r?.data) ? r.data : []
            //     })
            //   })
            // })
          })
        }),
        formatter: ({ field }, item) => {
          const cellVal = getValueByPath(item, field)
          return companyList.find((e) => e.value === cellVal)?.text ?? cellVal
        },
        searchOptions: {
          renameField: 'item.companyName'
        }
      },
      {
        field: 'companyCode',
        headerText: i18n.t('公司编码'),
        width: 0,
        allowResizing: false,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        ignore: true
      },
      {
        field: 'companyId',
        headerText: i18n.t('公司ID'),
        width: 0,
        allowResizing: false,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        ignore: true
      },
      {
        field: 'purOrgName',
        headerText: i18n.t('采购组织'),
        width: 200,
        allowEditing: true,
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('采购组织')}}</span>
                  </div>
                `
            })
          }
        },
        edit: editInstance.create({
          valueConvert: (val) => (val === null ? '' : val),
          getEditConfig: ({ rowData }) => ({
            type: 'select',
            'show-clear-button': true,
            'allow-filtering': true,
            'filter-type': 'Contains',
            popupWidth: '400px',
            created: async function () {
              initBuyerOrgSource()
              await querybuyerOrgDataSource(rowData?.companyId).catch((err) => console.warn(err))
              if (!buyerOrgSource.find((e) => e.organizationName === rowData.organizationName)) {
                await querybuyerOrgDataSource(rowData.companyId).catch((err) => console.warn(err))
              }
            },
            // filtering: useFiltering(async function (e) {
            //   if (typeof e.text === 'string' && e.text) {
            //     e.updateData(buyerOrgSource.filter((f) => f?.organizationName.indexOf(e.text) > -1))
            //   } else {
            //     e.updateData(buyerOrgSource)
            //   }
            // }),
            fields: makeTextFields('organizationName'),
            dataSource: []
          })
        }),
        // formatter: ({ field }, item) => {
        //   const cellVal = getValueByPath(item, field);
        //   return buyerOrgList.find((e) => e.value === cellVal)?.text ?? cellVal;
        // },
        searchOptions: {
          renameField: 'item.purOrgName'
        }
      },
      {
        field: 'purOrgCode',
        headerText: i18n.t('采购组织编码'),
        width: 0,
        allowResizing: false,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        ignore: true
      },
      {
        field: 'purOrgId',
        headerText: i18n.t('采购组织ID'),
        width: 0,
        allowResizing: false,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        ignore: true
      },
      {
        field: 'siteName',
        headerText: i18n.t('工厂'),
        width: 250,
        allowEditing: true,
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('工厂')}}</span>
                  </div>
                `
            })
          }
        },
        edit: editInstance.create({
          valueConvert: (val) => (val === null ? '' : val),
          getEditConfig: ({ rowData }) => ({
            type: 'select',
            'show-clear-button': true,
            'allow-filtering': true,
            popupWidth: '400px',
            'filter-type': 'Contains',
            created: async function () {
              initSiteSource()

              await querySiteDataSource(rowData?.purOrgId).catch((err) => console.warn(err))
              if (!siteSource.find((e) => e.orgName === rowData.siteName)) {
                await querySiteDataSource(rowData?.purOrgId).catch((err) => console.warn(err))
              }
            },
            // filtering: useFiltering(async function (e) {
            //   if (typeof e.text === 'string' && e.text) {
            //     e.updateData(siteSource.filter((f) => f?.orgName.indexOf(e.text) > -1))
            //   } else {
            //     e.updateData(siteSource)
            //   }
            // }),
            fields: makeTextFields('orgName'),
            dataSource: []
          })
        }),
        searchOptions: {
          renameField: 'item.siteName'
        }
      },
      {
        field: 'siteCode',
        headerText: i18n.t('工厂编码'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        searchOptions: {
          renameField: 'item.siteCode'
        }
      },
      {
        field: 'siteId',
        headerText: i18n.t('工厂Id'),
        width: 0,
        allowResizing: false,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        ignore: true
      },
      {
        field: 'categoryId',
        headerText: i18n.t('品类ID'),
        width: 0,
        allowResizing: false,
        allowEditing: false,
        edit: editInstance.create({
          valueConvert: (val) => (val === null ? '' : val),
          getEditConfig: () => ({
            type: 'text',
            readonly: true,
            disabled: true
          })
        }),
        ignore: true
      },
      {
        field: 'categoryCode',
        headerText: i18n.t('品类编码'),
        width: 200,
        allowEditing: false,
        edit: editInstance.create({
          valueConvert: (val) => (val === null ? '' : val),
          getEditConfig: () => ({
            type: 'text',
            readonly: true,
            disabled: true
          })
        }),
        searchOptions: {
          renameField: 'item.categoryCode'
        }
      },
      {
        field: 'categoryName',
        headerText: i18n.t('品类名称'),
        width: 200,
        allowEditing: true,
        edit: editInstance.create({
          valueConvert: (val) => (val === null ? '' : val),
          getEditConfig: ({ rowData }) => {
            return {
              type: 'select',
              // 有物料的不能编辑
              // > 物料肯定是有品类的，没有品类的物料那应该是数据问题
              // disabled: !!(rowData.itemCode && rowData.categoryCode),
              disabled: !isProperty,
              'show-clear-button': true,
              'allow-filtering': true,
              created: async function () {
                // 品类是搜索带出的,非一次性加载
                // 品类名称需要和品类编码的 dataSource 保持一致
                // 品类名称需要和品类编码保持互相联动
                const categoryName = rowData.categoryName
                initCategoryDataSource()
                await queryCategoryDataSource().catch((err) => console.warn(err))
                if (!categoryDataSource.find((e) => e.categoryName === categoryName)) {
                  await queryCategoryDataSource(categoryName).catch((err) => console.warn(err))
                }
                setTimeout(() => {
                  editInstance.setValueByField('categoryName', categoryName)
                }, 0)
              },
              filtering: useFiltering(async function (e) {
                e.updateData(await queryCategoryDataSource(e.text))
              }),
              fields: makeTextFields('categoryName'),
              dataSource: []
            }
          }
        }),
        searchOptions: {
          renameField: 'item.categoryName'
        }
      },
      {
        field: 'propertyCode',
        headerText: i18n.t('属性编码'),
        width: 200,
        allowEditing: isProperty,
        editTemplate: () => {
          return {
            template: selectAllAttrCode
          }
        },
        searchOptions: {
          renameField: 'item.propertyCode'
        }
      },
      {
        field: 'propertyName',
        headerText: i18n.t('属性名称'),
        width: 200,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        searchOptions: {
          renameField: 'item.propertyName'
        }
      },
      {
        field: 'itemCode',
        headerText: i18n.t('物料编码'),
        width: 200,
        allowEditing: !isProperty,
        editTemplate: () => {
          return {
            template: selectedItemCode
          }
        },
        searchOptions: {
          renameField: 'item.itemCode'
        }
      },
      {
        field: 'itemName',
        headerText: i18n.t('物料名称'),
        width: 200,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        searchOptions: {
          renameField: 'item.itemName'
        }
      },
      {
        field: 'itemId',
        headerText: i18n.t('物料Id'),
        width: 0,
        allowResizing: false,
        editTemplate: () => {
          return {
            template: cellChanged
          }
        },
        ignore: true
      },
      {
        field: 'costModel',
        headerText: i18n.t('成本模型'),
        width: 0,
        allowResizing: false,
        editTemplate: () => {
          return {
            template: cellChanged
          }
        },
        ignore: true
      },
      {
        field: 'unitCode',
        headerText: i18n.t('单位编码'),
        width: 95,
        allowEditing: false,
        editTemplate: () => {
          return {
            template: cellChanged
          }
        }
      },
      {
        field: 'unitName',
        headerText: i18n.t('基本单位'),
        allowEditing: false,
        editTemplate: () => {
          return {
            template: cellChanged
          }
        },
        searchOptions: {
          renameField: 'item.unitName'
        }
      },
      {
        field: 'priceUnitName',
        headerText: i18n.t('价格单位'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'select',
            disabled: true,
            readonly: true,
            dataSource: [
              { text: '1', value: '1' },
              { text: '1000', value: '1000' }
            ]
          })
        }),
        searchOptions: {
          renameField: 'item.priceUnitName'
        }
      },
      {
        field: 'lastTaxedBasicPrice',
        headerText: i18n.t('原含税基价'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        searchOptions: {
          renameField: 'biddingItem.lastTaxedBasicPrice'
        }
      },
      {
        field: 'taxedBasicPrice',
        headerText: i18n.t('含税基价'),
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('含税基价')}}</span>
                  </div>
                `
            })
          }
        },
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'number',
            precision: '5'
          })
        }),
        searchOptions: {
          renameField: 'biddingItem.taxedBasicPrice'
        }
      },
      {
        field: 'lowTaxedBasicPrice',
        headerText: i18n.t('最低含税基价'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        searchOptions: {
          renameField: 'biddingItem.lowTaxedBasicPrice'
        }
      },
      {
        field: 'priceDifference',
        headerText: i18n.t('价格差异'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        valueAccessor: (field, data) => {
          return data[field]
            ? (data[field] * 100).toFixed(3) + '%'
            : data[field] == 0
            ? data[field]
            : ''
        },
        searchOptions: {
          renameField: 'biddingItem.priceDifference',
          elementType: 'number'
        }
      },
      {
        field: 'quoteEffectiveStartDate',
        headerText: i18n.t('有效期从'),
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('有效期从')}}</span>
                  </div>
                `
            })
          }
        },
        formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'date',
            format: 'yyyy-MM-dd',
            'time-stamp': true,
            // readonly: getReadOnly(),
            placeholder: i18n.t('有效期从')
          })
        }),
        // format: "yyyy-MM-dd HH:mm:ss",
        // type: "date",
        searchOptions: {
          renameField: 'biddingItem.quoteEffectiveStartDate',
          ...searchOptionsList.timeRange
        }
      },
      {
        field: 'quoteEffectiveEndDate',
        headerText: i18n.t('有效期至'),
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('有效期至')}}</span>
                  </div>
                `
            })
          }
        },
        formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'date',
            format: 'yyyy-MM-dd',
            'time-stamp': true,
            // readonly: getReadOnly(),
            placeholder: i18n.t('有效期至')
          })
        }),
        // format: "yyyy-MM-dd HH:mm:ss",
        // type: "date",
        searchOptions: {
          renameField: 'biddingItem.quoteEffectiveEndDate',
          ...searchOptionsList.timeRange
        }
      },
      {
        field: 'plannedDeliveryTime',
        headerText: i18n.t('计划交货时间（天）'),
        allowEditing: true,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'number',
            min: 0,
            precision: '0'
          })
        }),
        searchOptions: {
          renameField: 'biddingItem.plannedDeliveryTime'
        }
      },
      {
        field: 'purGroupId',
        headerText: i18n.t('采购组Id'),
        width: 0,
        allowResizing: false,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        ignore: true
      },
      {
        field: 'purGroupName',
        headerText: i18n.t('采购组名称'),
        width: 0,
        allowResizing: false,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        ignore: true
      },
      {
        field: 'purGroupCode',
        headerText: i18n.t('采购组'),
        width: 250,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'mt-select',
            dataSource: purGroupList,
            placeholder: i18n.t('请选择'),
            'allow-filtering': true,
            'filter-type': 'Contains'
            // filtering: useFiltering(function (e) {
            //   if (typeof e.text === 'string' && e.text) {
            //     e.updateData(purGroupList.filter((f) => f?.text.indexOf(e.text) > -1))
            //   } else {
            //     e.updateData(purGroupList)
            //   }
            // })
          })
        }),
        formatter: ({ field }, item) => {
          const cellVal = getValueByPath(item, field)
          return purGroupList.find((e) => e.value === cellVal)?.text ?? cellVal
        },
        searchOptions: {
          renameField: 'item.purGroupCode'
        }
      },
      // {
      //   field: "purExecutorName",
      //   headerText: i18n.t("材质"),
      //   allowEditing: false,
      //   editTemplate: () => {
      //     return {
      //       template: cellChanged,
      //     };
      //   },
      // },
      {
        field: 'propertyPrice',
        headerText: i18n.t('价格属性'),
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('价格属性')}}</span>
                  </div>
                `
            })
          }
        },
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'mt-select',
            dataSource: propertyPriceList,
            placeholder: i18n.t('价格属性'),
            'allow-filtering': false
          }),
          formatter: ({ field }, item) => {
            const cellVal = getValueByPath(item, field)
            return propertyPriceList.find((e) => e.value === cellVal)?.text ?? cellVal
          }
        }),
        valueAccessor: (field, data) => {
          return propertyPriceList.find((e) => e.value == data[field])?.text || ''
        },
        searchOptions: {
          renameField: 'biddingItem.propertyPrice',
          elementType: 'select',
          dataSource: propertyPriceList
        }
      },
      {
        field: 'remark',
        headerText: i18n.t('备注'),
        allowEditing: true,
        searchOptions: {
          renameField: 'biddingItem.remark'
        }
        // editTemplate: () => {
        //   return {
        //     template: cellChanged,
        //   };
        // },
      }
    ]
  } else if (type == 4) {
    return [
      {
        width: '60',
        type: 'checkbox',
        allowEditing: false,
        showInColumnChooser: false
      },
      {
        field: 'lineNo',
        headerText: i18n.t('行号'),
        allowEditing: false,
        searchOptions: {
          renameField: 'item.lineNo'
        }
      },
      {
        field: 'companyName',
        headerText: i18n.t('公司'),
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('公司')}}</span>
                  </div>
                `
            })
          }
        },
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'mt-select',
            dataSource: companyList,
            fields: { value: 'orgName', text: 'orgName' },
            placeholder: i18n.t('公司'),
            'allow-filtering': true,
            popupWidth: '400px',
            'filter-type': 'Contains'
            // filtering: useFiltering(function (e) {
            //   getSupplierList({
            //     fuzzyNameOrCode: e.text
            //     // organizationCode: sessionStorage.getItem("organizationCode"),
            //   }).then((r) => {
            //     editInstance.setOptions('orgName', {
            //       dataSource: Array.isArray(r?.data) ? r.data : []
            //     })
            //   })
            // })
          })
        }),
        formatter: ({ field }, item) => {
          const cellVal = getValueByPath(item, field)
          return companyList.find((e) => e.value === cellVal)?.text ?? cellVal
        },
        searchOptions: {
          renameField: 'item.companyName'
        }
      },
      {
        field: 'companyCode',
        headerText: i18n.t('公司编码'),
        width: 0,
        allowResizing: false,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        ignore: true
      },
      {
        field: 'companyId',
        headerText: i18n.t('公司ID'),
        width: 0,
        allowResizing: false,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        ignore: true
      },
      {
        field: 'purOrgName',
        headerText: i18n.t('采购组织'),
        width: 200,
        allowEditing: true,
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('采购组织')}}</span>
                  </div>
                `
            })
          }
        },
        edit: editInstance.create({
          valueConvert: (val) => (val === null ? '' : val),
          getEditConfig: ({ rowData }) => ({
            type: 'select',
            'show-clear-button': true,
            'allow-filtering': true,
            popupWidth: '400px',
            created: async function () {
              initBuyerOrgSource()
              await querybuyerOrgDataSource(rowData?.companyId).catch((err) => console.warn(err))
              if (!buyerOrgSource.find((e) => e.organizationName === rowData.organizationName)) {
                await querybuyerOrgDataSource(rowData.companyId).catch((err) => console.warn(err))
              }
            },
            filtering: useFiltering(async function (e) {
              e.updateData(await querybuyerOrgDataSource(e.text))
            }),
            fields: makeTextFields('organizationName'),
            dataSource: []
          })
        }),
        // formatter: ({ field }, item) => {
        //   const cellVal = getValueByPath(item, field);
        //   return buyerOrgList.find((e) => e.value === cellVal)?.text ?? cellVal;
        // },
        searchOptions: {
          renameField: 'item.purOrgName'
        }
      },
      {
        field: 'purOrgCode',
        headerText: i18n.t('采购组织编码'),
        width: 0,
        allowResizing: false,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        ignore: true
      },
      {
        field: 'purOrgId',
        headerText: i18n.t('采购组织ID'),
        width: 0,
        allowResizing: false,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        ignore: true
      },
      {
        field: 'siteName',
        headerText: i18n.t('工厂'),
        width: 250,
        allowEditing: true,
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('工厂')}}</span>
                  </div>
                `
            })
          }
        },
        edit: editInstance.create({
          valueConvert: (val) => (val === null ? '' : val),
          getEditConfig: ({ rowData }) => ({
            type: 'select',
            'show-clear-button': true,
            'allow-filtering': true,
            'filter-type': 'Contains',
            popupWidth: '400px',
            created: async function () {
              initSiteSource()
              await querySiteDataSource(rowData?.purOrgId).catch((err) => console.warn(err))
              if (!siteSource.find((e) => e.orgName === rowData.siteName)) {
                await querySiteDataSource(rowData?.purOrgId).catch((err) => console.warn(err))
              }
            },
            // filtering: useFiltering(async function (e) {
            //   if (typeof e.text === 'string' && e.text) {
            //     e.updateData(siteSource.filter((f) => f?.orgName.indexOf(e.text) > -1))
            //   } else {
            //     e.updateData(siteSource)
            //   }
            // }),
            fields: makeTextFields('orgName'),
            dataSource: []
          })
        }),
        searchOptions: {
          renameField: 'item.siteName'
        }
      },
      {
        field: 'siteCode',
        headerText: i18n.t('工厂编码'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        searchOptions: {
          renameField: 'item.siteCode'
        }
      },
      {
        field: 'siteId',
        headerText: i18n.t('工厂Id'),
        width: 0,
        allowResizing: false,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        ignore: true
      },
      {
        field: 'categoryCode',
        headerText: i18n.t('品类编码'),
        width: 200,
        edit: editInstance.create({
          valueConvert: (val) => (val === null ? '' : val),
          getEditConfig: () => ({
            type: 'text',
            readonly: true,
            disabled: true
          })
        }),
        searchOptions: {
          renameField: 'item.categoryCode'
        }
      },
      {
        field: 'categoryName',
        headerText: i18n.t('品类名称'),
        width: 200,
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('品类名称')}}</span>
                  </div>
                `
            })
          }
        },
        edit: editInstance.create({
          valueConvert: (val) => (val === null ? '' : val),
          getEditConfig: ({ rowData }) => {
            return {
              type: 'select',
              // 有物料的不能编辑
              // > 物料肯定是有品类的，没有品类的物料那应该是数据问题
              // disabled: !!(rowData.itemCode && rowData.categoryCode),
              disabled: isProperty,
              'show-clear-button': true,
              'allow-filtering': true,
              created: async function () {
                // 品类是搜索带出的,非一次性加载
                // 品类名称需要和品类编码的 dataSource 保持一致
                // 品类名称需要和品类编码保持互相联动
                const categoryName = rowData.categoryName
                initCategoryDataSource()
                await queryCategoryDataSource().catch((err) => console.warn(err))
                if (!categoryDataSource.find((e) => e.categoryName === categoryName)) {
                  await queryCategoryDataSource(categoryName).catch((err) => console.warn(err))
                }
                setTimeout(() => {
                  editInstance.setValueByField('categoryName', categoryName)
                }, 0)
              },
              filtering: useFiltering(async function (e) {
                e.updateData(await queryCategoryDataSource(e.text))
              }),
              fields: makeTextFields('categoryName'),
              dataSource: []
            }
          }
        }),
        searchOptions: {
          renameField: 'item.categoryName'
        }
      },
      {
        field: 'categoryId',
        headerText: i18n.t('品类Id'),
        width: 0,
        allowResizing: false,
        editTemplate: () => {
          return {
            template: cellChanged
          }
        },
        ignore: true
      },
      {
        field: 'itemCode',
        headerText: i18n.t('成本因子编码'),
        width: 200,
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('成本因子编码')}}</span>
                  </div>
                `
            })
          }
        },
        editTemplate: () => {
          return {
            template: selectAllCostFactorCode
          }
        },
        searchOptions: {
          renameField: 'item.itemCode'
        }
      },
      {
        field: 'itemName',
        headerText: i18n.t('成本因子名称'),
        width: 200,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        searchOptions: {
          renameField: 'item.itemName'
        }
      },
      // {
      //   field: "itemId",
      //   headerText: i18n.t("物料Id"),
      //   width: 0,allowResizing: false,
      //   editTemplate: () => {
      //     return {
      //       template: cellChanged,
      //     };
      //   },
      // },
      {
        field: 'unitCode',
        headerText: i18n.t('单位编码'),
        width: 95,
        allowEditing: false,
        editTemplate: () => {
          return {
            template: cellChanged
          }
        }
      },
      {
        field: 'unitName',
        headerText: i18n.t('基本单位'),
        allowEditing: false,
        editTemplate: () => {
          return {
            template: cellChanged
          }
        },
        searchOptions: {
          renameField: 'item.unitName'
        }
      },
      {
        field: 'lastTaxedAveragePrice',
        headerText: i18n.t('上次含税均价'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        searchOptions: {
          renameField: 'biddingItem.lastTaxedAveragePrice'
        }
      },
      {
        field: 'taxedAveragePrice',
        headerText: i18n.t('含税均价'),
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('含税均价')}}</span>
                  </div>
                `
            })
          }
        },
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'number',
            precision: '5'
          })
        }),
        searchOptions: {
          renameField: 'biddingItem.taxedAveragePrice'
        }
      },
      {
        field: 'priceDifference',
        headerText: i18n.t('价格差异'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        searchOptions: {
          renameField: 'biddingItem.priceDifference',
          elementType: 'number'
        }
      },
      {
        field: 'quoteEffectiveStartDate',
        headerText: i18n.t('有效期从'),
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('有效期从')}}</span>
                  </div>
                `
            })
          }
        },
        formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'date',
            format: 'yyyy-MM-dd',
            'time-stamp': true,
            // readonly: getReadOnly(),
            placeholder: i18n.t('有效期从')
          })
        }),
        // format: "yyyy-MM-dd HH:mm:ss",
        // type: "date",
        searchOptions: {
          renameField: 'biddingItem.quoteEffectiveStartDate',
          ...searchOptionsList.timeRange
        }
      },
      {
        field: 'quoteEffectiveEndDate',
        headerText: i18n.t('有效期至'),
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('有效期至')}}</span>
                  </div>
                `
            })
          }
        },
        formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'date',
            format: 'yyyy-MM-dd',
            'time-stamp': true,
            // readonly: getReadOnly(),
            placeholder: i18n.t('有效期至')
          })
        }),
        // format: "yyyy-MM-dd HH:mm:ss",
        // type: "date",
        searchOptions: {
          renameField: 'biddingItem.quoteEffectiveEndDate',
          ...searchOptionsList.timeRange
        }
      },
      {
        field: 'remark',
        headerText: i18n.t('备注'),
        allowEditing: true,
        searchOptions: {
          renameField: 'biddingItem.remark'
        }
        // editTemplate: () => {
        //   return {
        //     template: cellChanged,
        //   };
        // },
      }
    ]
  } else if (type == 5) {
    return [
      {
        width: '60',
        type: 'checkbox',
        allowEditing: false,
        showInColumnChooser: false
      },
      {
        field: 'lineNo',
        headerText: i18n.t('行号'),
        allowEditing: false,
        searchOptions: {
          renameField: 'item.lineNo'
        }
      },
      {
        field: 'orderNo',
        headerText: i18n.t('订单号'),
        allowEditing: false,
        searchOptions: {
          renameField: 'item.orderNo'
        }
      },
      {
        field: 'companyName',
        headerText: i18n.t('公司'),
        allowEditing: false,
        searchOptions: {
          renameField: 'item.companyName'
        }
      },
      {
        field: 'purOrgName',
        headerText: i18n.t('采购组织'),
        width: 200,
        allowEditing: false,
        searchOptions: {
          renameField: 'item.purOrgName'
        }
      },
      {
        field: 'siteName',
        headerText: i18n.t('工厂'),
        width: 250,
        allowEditing: false,
        searchOptions: {
          renameField: 'item.siteName'
        }
      },
      {
        field: 'itemCode',
        headerText: i18n.t('物料编码'),
        width: 200,
        allowEditing: false,
        searchOptions: {
          renameField: 'item.itemCode'
        }
      },
      {
        field: 'itemName',
        headerText: i18n.t('物料名称'),
        width: 200,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        searchOptions: {
          renameField: 'item.itemName'
        }
      },
      {
        field: 'categoryCode',
        headerText: i18n.t('品类编码'),
        width: 200,
        allowEditing: false,
        edit: editInstance.create({
          valueConvert: (val) => (val === null ? '' : val),
          getEditConfig: () => ({
            type: 'text',
            readonly: true,
            disabled: true
          })
        }),
        searchOptions: {
          renameField: 'item.categoryCode'
        }
      },
      {
        field: 'categoryName',
        headerText: i18n.t('品类名称'),
        width: 200,
        allowEditing: false,
        searchOptions: {
          renameField: 'item.categoryName'
        }
      },
      {
        field: 'costModelStrName',
        headerText: i18n.t('核价模型'),
        width: 250,
        allowEditing: false,
        searchOptions: {
          renameField: 'item.costModelStrName'
        },
        editTemplate: () => {
          return {
            template: Vue.component('common', {
              template: `<span style="color: #6386c1;cursor: pointer" @click="viewDetail">{{ $t('查看核价模型') }}</span>`,
              data: function () {
                return {
                  data: {}
                }
              },
              methods: {
                viewDetail() {
                  this.$router.push({
                    name: 'contract-price-cost-model',
                    query: {
                      pointId: this.data.pointId,
                      pointItemId: this.data.pointItemId
                    }
                  })
                }
              }
            })
          }
        },
        template: () => {
          return {
            template: Vue.component('common', {
              template: `<span style="color: #6386c1;cursor: pointer" @click="viewDetail">{{ $t('查看核价模型') }}</span>`,
              data: function () {
                return {
                  data: {}
                }
              },
              methods: {
                viewDetail() {
                  this.$router.push({
                    name: 'contract-price-cost-model',
                    query: {
                      pointId: this.data.pointId,
                      pointItemId: this.data.pointItemId
                    }
                  })
                }
              }
            })
          }
        }
      },
      {
        field: 'dataFormula',
        headerText: i18n.t('计算公式'),
        width: 200,
        allowEditing: false,
        ignore: true
      },
      {
        field: 'bidCurrencyName',
        headerText: i18n.t('币种'),
        width: 250,
        allowEditing: false,
        searchOptions: {
          renameField: 'biddingItem.bidCurrencyName'
        }
      },
      {
        field: 'bidTaxRateName',
        headerText: i18n.t('税率名称'),
        allowEditing: false,
        searchOptions: {
          renameField: 'biddingItem.bidTaxRateName'
        }
      },
      {
        field: 'priceUnitName',
        headerText: i18n.t('价格单位'),
        allowEditing: false,
        searchOptions: {
          renameField: 'item.priceUnitName'
        }
      },
      {
        field: 'taxedBasicPrice',
        headerText: i18n.t('含税基价'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        searchOptions: {
          renameField: 'biddingItem.taxedBasicPrice'
        }
      },
      {
        field: 'taxedQuotaValue',
        headerText: i18n.t('定额取值'),
        allowEditing: false,
        searchOptions: {
          renameField: 'biddingItem.taxedQuotaValue'
        }
      },
      {
        field: 'taxedBasicAverageValue',
        headerText: i18n.t('均价取值'),
        allowEditing: false,
        searchOptions: {
          renameField: 'biddingItem.taxedBasicAverageValue'
        }
      },
      {
        field: 'untaxedExercisePrice',
        headerText: i18n.t('未税执行价'),
        allowEditing: false,
        searchOptions: {
          renameField: 'biddingItem.untaxedExercisePrice'
        }
      },
      {
        field: 'taxedExercisePrice',
        headerText: i18n.t('含税执行价'),
        allowEditing: false,
        searchOptions: {
          renameField: 'biddingItem.taxedExercisePrice'
        }
      },
      {
        field: 'referSourceCode',
        headerText: i18n.t('参考价'),
        width: 120,
        editTemplate: () => {
          return {
            template: selectSourceNo
          }
        },
        template: () => {
          return {
            template: selectSourceNo
          }
        },
        ignore: true
      },
      {
        field: 'lowUntaxedExercisePrice',
        width: '200',
        headerText: i18n.t('采购最低价(未税)'),
        allowEditing: false,
        searchOptions: {
          renameField: 'biddingItem.lowUntaxedExercisePrice'
        }
      },
      {
        field: 'priceDifference',
        headerText: i18n.t('价格差异'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        searchOptions: {
          renameField: 'biddingItem.priceDifference',
          elementType: 'number'
        }
      },
      {
        field: 'createUserName',
        headerText: i18n.t('创建人'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        searchOptions: {
          renameField: 'item.createUserName'
        }
      },
      {
        field: 'purGroupId',
        headerText: i18n.t('采购组Id'),
        width: 0,
        allowResizing: false,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        ignore: true
      },
      {
        field: 'purGroupName',
        headerText: i18n.t('采购组名称'),
        width: 0,
        allowResizing: false,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        ignore: true
      },
      {
        field: 'purGroupCode',
        headerText: i18n.t('采购组'),
        width: 250,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'mt-select',
            dataSource: purGroupList,
            placeholder: i18n.t('请选择'),
            'allow-filtering': true,
            'filter-type': 'Contains'
            // filtering: useFiltering(function (e) {
            //   if (typeof e.text === 'string' && e.text) {
            //     e.updateData(purGroupList.filter((f) => f?.text.indexOf(e.text) > -1))
            //   } else {
            //     e.updateData(purGroupList)
            //   }
            // })
          })
        }),
        formatter: ({ field }, item) => {
          const cellVal = getValueByPath(item, field)
          return purGroupList.find((e) => e.value === cellVal)?.text ?? cellVal
        },
        searchOptions: {
          renameField: 'item.purGroupCode'
        }
      },
      {
        field: 'quoteEffectiveStartDate',
        headerText: i18n.t('有效期从'),
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('有效期从')}}</span>
                  </div>
                `
            })
          }
        },
        formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'date',
            format: 'yyyy-MM-dd',
            'time-stamp': true,
            // readonly: getReadOnly(),
            placeholder: i18n.t('请选择')
          })
        }),
        // format: "yyyy-MM-dd HH:mm:ss",
        // type: "date",
        searchOptions: {
          renameField: 'biddingItem.quoteEffectiveStartDate',
          ...searchOptionsList.timeRange
        }
      },
      {
        field: 'quoteEffectiveEndDate',
        headerText: i18n.t('有效期至'),
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('有效期至')}}</span>
                  </div>
                `
            })
          }
        },
        formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'date',
            format: 'yyyy-MM-dd',
            'time-stamp': true,
            // readonly: getReadOnly(),
            placeholder: i18n.t('请选择')
          })
        }),
        // format: "yyyy-MM-dd HH:mm:ss",
        // type: "date",
        searchOptions: {
          renameField: 'biddingItem.quoteEffectiveEndDate',
          ...searchOptionsList.timeRange
        }
      },
      {
        field: 'plannedDeliveryTime',
        headerText: i18n.t('计划交货时间（天）'),
        allowEditing: true,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'number',
            min: 0,
            precision: '0'
          })
        }),
        searchOptions: {
          renameField: 'biddingItem.plannedDeliveryTime'
        }
      },
      {
        field: 'propertyPrice',
        headerText: i18n.t('价格属性'),
        allowEditing: false,
        valueAccessor: (field, data) => {
          return propertyPriceList.find((e) => e.value == data[field])?.text || ''
        },
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'mt-select',
            disabled: true,
            dataSource: propertyPriceList,
            placeholder: i18n.t('价格属性'),
            'allow-filtering': false
          }),
          formatter: ({ field }, item) => {
            const cellVal = getValueByPath(item, field)
            return propertyPriceList.find((e) => e.value === cellVal)?.text ?? cellVal
          }
        }),
        searchOptions: {
          renameField: 'biddingItem.propertyPrice',
          elementType: 'select',
          dataSource: propertyPriceList
        }
      },
      {
        field: 'remark',
        headerText: i18n.t('备注'),
        allowEditing: true,
        searchOptions: {
          renameField: 'biddingItem.remark'
        }
      }
    ]
  } else if (type == 7) {
    return [
      {
        width: '60',
        type: 'checkbox',
        allowEditing: false,
        showInColumnChooser: false
      },
      {
        field: 'lineNo',
        headerText: i18n.t('行号'),
        allowEditing: false,
        searchOptions: {
          renameField: 'item.lineNo'
        }
      },
      {
        field: 'orderNo',
        headerText: i18n.t('订单号'),
        minWidth: '80px',
        allowEditing: false,
        // editTemplate: () => {
        //   return { template: selectAllOrder }
        // },
        searchOptions: {
          renameField: 'item.orderNo'
        }
      },
      {
        field: 'companyName',
        headerText: i18n.t('公司'),
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('公司')}}</span>
                  </div>
                `
            })
          }
        },
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'mt-select',
            dataSource: companyList,
            fields: { value: 'orgName', text: 'orgName' },
            placeholder: i18n.t('公司'),
            popupWidth: '400px',
            'allow-filtering': true,
            'filter-type': 'Contains'
            // filtering: useFiltering(function (e) {
            //   getSupplierList({
            //     fuzzyNameOrCode: e.text
            //     // organizationCode: sessionStorage.getItem("organizationCode"),
            //   }).then((r) => {
            //     editInstance.setOptions('orgName', {
            //       dataSource: Array.isArray(r?.data) ? r.data : []
            //     })
            //   })
            // })
          })
        }),
        formatter: ({ field }, item) => {
          const cellVal = getValueByPath(item, field)
          return companyList.find((e) => e.value === cellVal)?.text ?? cellVal
        },
        searchOptions: {
          renameField: 'item.companyName'
        }
      },
      {
        field: 'companyCode',
        headerText: i18n.t('公司编码'),
        width: 0,
        allowResizing: false,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        ignore: true
      },
      {
        field: 'companyId',
        headerText: i18n.t('公司ID'),
        width: 0,
        allowResizing: false,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        ignore: true
      },
      {
        field: 'purOrgName',
        headerText: i18n.t('采购组织'),
        width: 200,
        allowEditing: true,
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('采购组织')}}</span>
                  </div>
                `
            })
          }
        },
        edit: editInstance.create({
          valueConvert: (val) => (val === null ? '' : val),
          getEditConfig: ({ rowData }) => ({
            type: 'select',
            'show-clear-button': true,
            'allow-filtering': true,
            popupWidth: '400px',
            'filter-type': 'Contains',
            created: async function () {
              initBuyerOrgSource()
              await querybuyerOrgDataSource(rowData?.companyId).catch((err) => console.warn(err))
              if (!buyerOrgSource.find((e) => e.organizationName === rowData.organizationName)) {
                await querybuyerOrgDataSource(rowData.companyId).catch((err) => console.warn(err))
              }
            },
            // filtering: useFiltering(async function (e) {
            //   if (typeof e.text === 'string' && e.text) {
            //     e.updateData(buyerOrgSource.filter((f) => f?.organizationName.indexOf(e.text) > -1))
            //   } else {
            //     e.updateData(buyerOrgSource)
            //   }
            // }),
            fields: makeTextFields('organizationName'),
            dataSource: []
          })
        }),
        // formatter: ({ field }, item) => {
        //   const cellVal = getValueByPath(item, field);
        //   return buyerOrgList.find((e) => e.value === cellVal)?.text ?? cellVal;
        // },
        searchOptions: {
          renameField: 'item.purOrgName'
        }
      },
      {
        field: 'purOrgCode',
        headerText: i18n.t('采购组织编码'),
        width: 0,
        allowResizing: false,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        ignore: true
      },
      {
        field: 'purOrgId',
        headerText: i18n.t('采购组织ID'),
        width: 0,
        allowResizing: false,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        ignore: true
      },
      {
        field: 'siteName',
        headerText: i18n.t('工厂'),
        width: 250,
        allowEditing: true,
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('工厂')}}</span>
                  </div>
                `
            })
          }
        },
        edit: editInstance.create({
          valueConvert: (val) => (val === null ? '' : val),
          getEditConfig: ({ rowData }) => ({
            type: 'select',
            'show-clear-button': true,
            'allow-filtering': true,
            popupWidth: '400px',
            'filter-type': 'Contains',
            created: async function () {
              initSiteSource()

              await querySiteDataSource(rowData?.purOrgId).catch((err) => console.warn(err))
              if (!siteSource.find((e) => e.orgName === rowData.siteName)) {
                await querySiteDataSource(rowData?.purOrgId).catch((err) => console.warn(err))
              }
            },
            // filtering: useFiltering(async function (e) {
            //   if (typeof e.text === 'string' && e.text) {
            //     e.updateData(siteSource.filter((f) => f?.orgName.indexOf(e.text) > -1))
            //   } else {
            //     e.updateData(siteSource)
            //   }
            // }),
            fields: makeTextFields('orgName'),
            dataSource: []
          })
        }),
        searchOptions: {
          renameField: 'item.siteName'
        }
      },
      {
        field: 'siteCode',
        headerText: i18n.t('工厂编码'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        searchOptions: {
          renameField: 'item.siteCode'
        }
      },
      {
        field: 'siteId',
        headerText: i18n.t('工厂Id'),
        width: 0,
        allowResizing: false,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        ignore: true
      },
      {
        field: 'itemCode',
        headerText: i18n.t('物料编码'),
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('物料编码')}}</span>
                  </div>
                `
            })
          }
        },
        width: 200,
        editTemplate: () => {
          return {
            template: selectedItemCode
          }
        },
        searchOptions: {
          renameField: 'item.itemCode'
        }
      },
      {
        field: 'itemName',
        headerText: i18n.t('物料名称'),
        width: 200,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        searchOptions: {
          renameField: 'item.itemName'
        }
      },
      {
        field: 'itemId',
        headerText: i18n.t('物料Id'),
        width: 0,
        allowResizing: false,
        editTemplate: () => {
          return {
            template: cellChanged
          }
        },
        ignore: true
      },
      {
        field: 'categoryId',
        headerText: i18n.t('品类ID'),
        width: 0,
        allowResizing: false,
        edit: editInstance.create({
          valueConvert: (val) => (val === null ? '' : val),
          getEditConfig: () => ({
            type: 'text',
            readonly: true,
            disabled: true
          })
        }),
        ignore: true
      },
      {
        field: 'categoryCode',
        headerText: i18n.t('品类编码'),
        width: 200,
        edit: editInstance.create({
          valueConvert: (val) => (val === null ? '' : val),
          getEditConfig: () => ({
            type: 'text',
            readonly: true,
            disabled: true
          })
        }),
        searchOptions: {
          renameField: 'item.categoryCode'
        }
      },
      {
        field: 'categoryName',
        headerText: i18n.t('品类名称'),
        width: 250,
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('品类名称')}}</span>
                  </div>
                `
            })
          }
        },
        allowEditing: false,
        edit: editInstance.create({
          valueConvert: (val) => (val === null ? '' : val),
          getEditConfig: ({ rowData }) => {
            return {
              type: 'select',
              // 有物料的不能编辑
              // > 物料肯定是有品类的，没有品类的物料那应该是数据问题
              // disabled: !!(rowData.itemCode && rowData.categoryCode),
              disabled: true,
              'show-clear-button': true,
              'allow-filtering': true,
              created: async function () {
                // 品类是搜索带出的,非一次性加载
                // 品类名称需要和品类编码的 dataSource 保持一致
                // 品类名称需要和品类编码保持互相联动
                const categoryName = rowData.categoryName
                initCategoryDataSource()
                await queryCategoryDataSource().catch((err) => console.warn(err))
                if (!categoryDataSource.find((e) => e.categoryName === categoryName)) {
                  await queryCategoryDataSource(categoryName).catch((err) => console.warn(err))
                }
                setTimeout(() => {
                  editInstance.setValueByField('categoryName', categoryName)
                }, 0)
              },
              filtering: useFiltering(async function (e) {
                e.updateData(await queryCategoryDataSource(e.text))
              }),
              fields: { text: 'categoryName', value: 'categoryName' },
              dataSource: []
            }
          }
        }),
        searchOptions: {
          renameField: 'item.categoryName'
        }
      },
      {
        field: 'plannedDeliveryTime',
        headerText: i18n.t('计划交货时间'),
        width: 200,
        allowEditing: true,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'number',
            min: 0,
            precision: '0'
          })
        }),
        searchOptions: {
          renameField: 'biddingItem.categoryName'
        }
      },
      {
        field: 'purGroupId',
        headerText: i18n.t('采购组Id'),
        width: 0,
        allowResizing: false,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        ignore: true
      },
      {
        field: 'purGroupName',
        headerText: i18n.t('采购组名称'),
        width: 0,
        allowResizing: false,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        ignore: true
      },
      {
        field: 'purGroupCode',
        headerText: i18n.t('采购组'),
        width: 250,
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('采购组')}}</span>
                  </div>
                `
            })
          }
        },
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'mt-select',
            dataSource: purGroupList,
            placeholder: i18n.t('请选择'),
            'allow-filtering': true,
            'filter-type': 'Contains'
            // filtering: useFiltering(function (e) {
            //   if (typeof e.text === 'string' && e.text) {
            //     e.updateData(purGroupList.filter((f) => f?.text.indexOf(e.text) > -1))
            //   } else {
            //     e.updateData(purGroupList)
            //   }
            // })
          })
        }),
        formatter: ({ field }, item) => {
          const cellVal = getValueByPath(item, field)
          return purGroupList.find((e) => e.value === cellVal)?.text ?? cellVal
        },
        searchOptions: {
          renameField: 'item.purGroupCode'
        }
      },
      {
        field: 'unitCode',
        headerText: i18n.t('单位编码'),
        width: 95,
        allowEditing: false,
        editTemplate: () => {
          return {
            template: cellChanged
          }
        }
      },
      {
        field: 'unitName',
        headerText: i18n.t('单位'),
        allowEditing: false,
        editTemplate: () => {
          return {
            template: cellChanged
          }
        },
        searchOptions: {
          renameField: 'item.unitName'
        }
      },
      {
        field: 'taxedUnitPrice',
        headerText: i18n.t('单价(含税)'),
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('单价(含税)')}}</span>
                  </div>
                `
            })
          }
        },
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'number',
            precision: '5',
            readonly: _isKtFlag == 0,
            disabled: _isKtFlag == 0
          })
        }),
        searchOptions: {
          renameField: 'biddingItem.taxedUnitPrice'
        }
      },
      {
        field: 'untaxedUnitPrice',
        headerText: i18n.t('单价(不含税)'),
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('单价(不含税)')}}</span>
                  </div>
                `
            })
          }
        },
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'number',
            precision: '5',
            readonly: _isKtFlag == 0,
            disabled: _isKtFlag == 0
          })
        }),
        searchOptions: {
          renameField: 'biddingItem.untaxedUnitPrice'
        }
      },
      {
        field: 'bidTaxRateCode',
        headerText: i18n.t('税率编码'),
        width: 95,
        allowEditing: false,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'input',
            disabled: true,
            readonly: true
          })
        })
      },
      {
        field: 'bidTaxRateName',
        headerText: i18n.t('税率'),
        width: 95,
        allowEditing: false,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'input',
            disabled: true,
            readonly: true
          })
        })
      },
      {
        field: 'referSourceCode',
        headerText: i18n.t('参考价'),
        width: 120,
        editTemplate: () => {
          return {
            template: selectSourceNo
          }
        },
        template: () => {
          return {
            template: Vue.component('common', { template: `<div></div>` })
          }
        },
        ignore: true
      },
      {
        field: 'priceUnitName',
        headerText: i18n.t('价格单位'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'select',
            disabled: true,
            readonly: true,
            dataSource: [
              { text: '1', value: '1' },
              { text: '1000', value: '1000' }
            ]
          })
        }),
        searchOptions: {
          renameField: 'item.priceUnitName'
        }
      },
      {
        field: 'lowUntaxedExercisePrice',
        headerText: i18n.t('未税采购最低价'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        searchOptions: {
          renameField: 'biddingItem.lowUntaxedExercisePrice'
        }
      },
      // 增加未税暂估最低价 0809 hjh
      {
        field: 'untaxedEstimatedPrice',
        headerText: i18n.t('未税暂估最低价'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        searchOptions: {
          renameField: 'biddingItem.untaxedEstimatedPrice'
        }
      },
      {
        field: 'priceDifference',
        headerText: i18n.t('价格差异'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'text',
            disabled: true,
            readonly: true
          })
        }),
        searchOptions: {
          renameField: 'biddingItem.priceDifference',
          elementType: 'number'
        }
      },
      {
        field: 'quoteAttribute',
        headerText: i18n.t('报价属性'),
        allowEditing: false,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'mt-select',
            disabled: true,
            dataSource: quoteAttributeList,
            placeholder: i18n.t('请选择报价属性')
          })
        }),
        formatter: ({ field }, item) => {
          const cellVal = getValueByPath(item, field)
          return quoteAttributeList.find((e) => e.value === cellVal)?.text ?? cellVal
        },
        searchOptions: {
          renameField: 'biddingItem.quoteAttribute'
        }
      },
      {
        field: 'quoteMode',
        headerText: i18n.t('报价方式'),
        allowEditing: false,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'mt-select',
            disabled: true,
            dataSource: quoteModeList,
            placeholder: i18n.t('请选择报价方式')
          })
        }),
        formatter: ({ field }, item) => {
          const cellVal = getValueByPath(item, field)
          return quoteModeList.find((e) => e.value === cellVal)?.text ?? cellVal
        },
        searchOptions: {
          renameField: 'biddingItem.quoteMode'
        }
      },
      {
        field: 'quoteEffectiveStartDate',
        headerText: i18n.t('有效期从'),
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('有效期从')}}</span>
                  </div>
                `
            })
          }
        },
        formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'date',
            format: 'yyyy-MM-dd',
            'time-stamp': true,
            // readonly: getReadOnly(),
            placeholder: i18n.t('有效期从')
          })
        }),
        // format: "yyyy-MM-dd HH:mm:ss",
        // type: "date",
        searchOptions: {
          renameField: 'biddingItem.quoteEffectiveStartDate',
          ...searchOptionsList.timeRange
        }
      },
      {
        field: 'quoteEffectiveEndDate',
        headerText: i18n.t('有效期至'),
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('有效期至')}}</span>
                  </div>
                `
            })
          }
        },
        formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'date',
            format: 'yyyy-MM-dd',
            'time-stamp': true,
            // readonly: getReadOnly(),
            placeholder: i18n.t('有效期至')
          })
        }),
        // format: "yyyy-MM-dd HH:mm:ss",
        // type: "date",
        searchOptions: {
          renameField: 'biddingItem.quoteEffectiveEndDate',
          ...searchOptionsList.timeRange
        }
      },
      {
        field: 'startValue',
        headerText: i18n.t('开始数量'),
        allowEditing: true,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'number',
            min: 0,
            precision: '0'
          })
        }),
        searchOptions: {
          renameField: 'biddingItem.startValue'
        }
      },
      {
        field: 'endValue',
        headerText: i18n.t('结束数量'),
        allowEditing: true,
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'number',
            min: 0,
            precision: '0'
          })
        }),
        searchOptions: {
          renameField: 'biddingItem.endValue'
        }
      },
      {
        field: 'propertyPrice',
        headerText: i18n.t('价格属性'),
        headerTemplate: () => {
          return {
            template: Vue.component('headers', {
              template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{$t('价格属性')}}</span>
                  </div>
                `
            })
          }
        },
        edit: editInstance.create({
          getEditConfig: () => ({
            type: 'mt-select',
            dataSource: propertyPriceList,
            placeholder: i18n.t('价格属性'),
            'allow-filtering': false
          }),
          formatter: ({ field }, item) => {
            const cellVal = getValueByPath(item, field)
            return propertyPriceList.find((e) => e.value === cellVal)?.text ?? cellVal
          }
        }),
        valueAccessor: (field, data) => {
          return propertyPriceList.find((e) => e.value == data[field])?.text || ''
        },
        searchOptions: {
          renameField: 'biddingItem.propertyPrice',
          elementType: 'select',
          dataSource: propertyPriceList
        }
      },
      {
        field: 'remark',
        headerText: i18n.t('备注'),
        allowEditing: true,
        searchOptions: {
          renameField: 'biddingItem.remark'
        }
      }
    ]
  }
}

export const columnDataTwo = ({
  directDelivery = [],
  allCurrency = [],
  supplierList = [],
  siteNameDataSource = []
} = {}) => {
  const editInstance = createEditInstance()
  return [
    {
      width: '60',
      type: 'checkbox',
      allowEditing: false,
      showInColumnChooser: false
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          dataSource: supplierList,
          fields: { value: 'supplierName', text: 'supplierName' },
          placeholder: i18n.t('供应商'),
          disabled: true,
          readonly: true
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return supplierList.find((e) => e.value === cellVal)?.text ?? cellVal
      },
      searchOptions: {
        renameField: 'biddingItem.supplierName'
      }
    },
    {
      field: 'siteName',
      headerText: i18n.t('工厂'),
      width: 250,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          dataSource: siteNameDataSource,
          fields: { value: 'siteName', text: 'siteName' },
          placeholder: i18n.t('请选择工厂'),
          disabled: true,
          readonly: true
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return siteNameDataSource.find((e) => e.supplierName === cellVal)?.text ?? cellVal
      },
      searchOptions: {
        renameField: 'item.siteName'
      }
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      width: 200,
      allowEditing: false,
      editTemplate: () => {
        return {
          template: selectedItemCode
        }
      }
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料名称'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
      // valueConverter: {
      //   type: "map",
      //   map: { 0: i18n.t("评分中"), 1: i18n.t("已提交"), 2: i18n.t("评分汇总") },
      // },
    },
    {
      field: 'scoreGroup',
      headerText: i18n.t('最小起拆点'),
      allowEditing: false
    },
    {
      field: 'rfxName',
      headerText: i18n.t('价格类型'),
      allowEditing: false
    },
    {
      field: 'allocationQuantity',
      headerText: i18n.t('分配数量'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number'
        })
      })
    },
    {
      field: 'allocationRatio',
      headerText: i18n.t('配额比例（%）'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number'
        })
      }),
      formatter: ({ field }, item) => {
        let val = item[field] || 0
        return val + '%'
      }
    },
    // {
    //   field: "allocationRatio",
    //   headerText: i18n.t("配额"),
    //   edit: editInstance.create({
    //     getEditConfig: () => ({
    //       type: "text",
    //     }),
    //   }),
    // },
    {
      field: 'deliveryPlace',
      headerText: i18n.t('直送地'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          disabled: true,
          readonly: true,
          dataSource: directDelivery,
          placeholder: i18n.t('直送地')
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return directDelivery.find((e) => e.value === cellVal)?.text ?? cellVal
      }
    },
    {
      field: 'taxedUnitPrice',
      headerText: i18n.t('单价(含税)'),
      allowEditing: false
    },
    {
      field: 'untaxedUnitPrice',
      headerText: i18n.t('单价(不含税)'),
      allowEditing: false
    },
    {
      field: 'stepQuote',
      headerText: i18n.t('是否阶梯报价'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          // readonly: getReadOnly(),
          dataSource: [
            { text: i18n.t('否'), value: 0 },
            { text: i18n.t('是'), value: 1 }
          ],
          placeholder: i18n.t('是否阶梯报价'),
          disabled: true,
          readonly: true
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        switch (Number(cellVal)) {
          case 0:
            return i18n.t('否')
          case 1:
            return i18n.t('是')
          default:
            return cellVal
        }
      },
      valueConverter: {
        type: 'map',
        map: {
          0: i18n.t('否'),
          1: i18n.t('是')
        }
      }
    },
    // {
    //   field: "biddingItemStageStrName",
    //   headerText: i18n.t("阶梯价格"),
    //   cssClass: "field-content",
    //   allowEditing: false,
    // },
    {
      field: 'biddingItemStageStrName',
      headerText: i18n.t('阶梯价格'),
      // cssClass: "field-content",
      // allowEditing: false,
      width: 400,
      allowFiltering: false,
      template: function () {
        return {
          template: directCellView
        }
      },
      editTemplate: () => {
        return {
          template: directCellView
        }
      }
    },
    {
      field: 'requireQuantity',
      headerText: i18n.t('需求数量'),
      allowEditing: false
    },
    {
      field: 'bidCurrencyName',
      headerText: i18n.t('币种'),
      width: 150,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          dataSource: allCurrency,
          disabled: true,
          readonly: true,
          placeholder: i18n.t('请选择币种'),
          'allow-filtering': true
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return allCurrency.find((e) => e.value === cellVal)?.text ?? cellVal
      }
    },
    {
      field: 'bidCurrencyCode',
      headerText: i18n.t('币种编码'),
      allowEditing: false
    },
    {
      field: 'bidTaxRateValue',
      headerText: i18n.t('税率'),
      allowEditing: false
    },
    {
      field: 'minPackageQuantity',
      headerText: i18n.t('最小包装量/MPQ'),
      allowEditing: false
    },
    {
      field: 'unitCode',
      headerText: i18n.t('单位编码'),
      width: 95,
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'unitName',
      headerText: i18n.t('基本单位'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'purUnitName',
      headerText: i18n.t('订单单位'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'minPurQuantity',
      headerText: i18n.t('最小采购量/MOQ'),
      allowEditing: false
    },
    {
      field: 'leadTime',
      headerText: i18n.t('L/T'),
      allowEditing: false
    },
    {
      field: 'unconditionalLeadTime',
      headerText: i18n.t('无条件L/T'),
      allowEditing: false
    },
    {
      field: 'quoteEffectiveStartDate',
      headerText: i18n.t('生效日期'),
      formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'date',
          format: 'yyyy-MM-dd',
          disabled: true,
          readonly: true,
          placeholder: i18n.t('请输入生效日期')
        })
      }),
      format: 'yyyy-MM-dd HH:mm:ss',
      type: 'date',
      searchOptions: {
        renameField: 'biddingItem.quoteEffectiveStartDate'
      }
    },
    {
      field: 'quoteEffectiveEndDate',
      headerText: i18n.t('失效日期'),
      formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'date',
          format: 'yyyy-MM-dd',
          disabled: true,
          readonly: true,
          placeholder: i18n.t('请输入失效日期')
        })
      }),
      format: 'yyyy-MM-dd HH:mm:ss',
      type: 'date',
      searchOptions: {
        renameField: 'biddingItem.quoteEffectiveEndDate'
      }
    },
    {
      field: 'priceUnitName',
      headerText: i18n.t('价格单位'),
      allowEditing: false
    },
    {
      field: 'deliveryPlace',
      headerText: i18n.t('直送地'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          disabled: true,
          readonly: true,
          dataSource: directDelivery,
          placeholder: i18n.t('直送地')
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return directDelivery.find((e) => e.value === cellVal)?.text ?? cellVal
      }
    },
    {
      field: 'spec',
      headerText: i18n.t('规格描述'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'material',
      headerText: i18n.t('材质'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    }
  ]
}
export const toolbar = (enabledAdd = false) => [
  {
    id: 'Add',
    icon: 'icon_solid_Createorder',
    title: i18n.t('新增'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'Save',
    icon: 'icon_solid_Save',
    title: i18n.t('保存'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'Cancel',
    icon: 'icon_solid_Cancel',
    title: i18n.t('取消'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'del',
    icon: 'icon_solid_Cancel',
    title: i18n.t('删除'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'export',
    icon: 'icon_solid_Download',
    title: i18n.t('导出')
  }
]
export const contractToolbar = (enabledAdd = false, require = false) => [
  {
    id: 'Add',
    icon: 'icon_solid_Createorder',
    title: i18n.t('新增'),
    visibleCondition: () => enabledAdd && !require
  },
  {
    id: 'Save',
    icon: 'icon_solid_Save',
    title: i18n.t('保存'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'Cancel',
    icon: 'icon_solid_Cancel',
    title: i18n.t('取消'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'del',
    icon: 'icon_solid_Cancel',
    title: i18n.t('删除'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'quickAdd',
    icon: 'icon_solid_Createorder',
    title: i18n.t('快速添加'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'export',
    icon: 'icon_solid_Download',
    title: i18n.t('导出')
  }
]
export const strikeToolbar = (enabledAdd = false) => [
  {
    id: 'Save',
    icon: 'icon_solid_Save',
    title: i18n.t('保存'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'Cancel',
    icon: 'icon_solid_Cancel',
    title: i18n.t('取消'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'del',
    icon: 'icon_solid_Cancel',
    title: i18n.t('删除'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'export',
    icon: 'icon_solid_Download',
    title: i18n.t('导出')
  }
]
export const toolbarTwo = [{ id: 'SaveTwo', icon: 'icon_solid_Save', title: i18n.t('保存') }]
export const distributeToolbar = (enabledAdd = false) => [
  {
    id: 'Add',
    icon: 'icon_solid_Createorder',
    title: i18n.t('新增'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'Save',
    icon: 'icon_solid_Save',
    title: i18n.t('保存'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'Cancel',
    icon: 'icon_solid_Cancel',
    title: i18n.t('取消'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'del',
    icon: 'icon_solid_Cancel',
    title: i18n.t('删除'),
    visibleCondition: () => enabledAdd
  }
]
export const pageConfig = (url, id, serializeList, recordDoubleClick, decidePriceType) => {
  let _pageConfig = []
  if (decidePriceType < 2) {
    _pageConfig = [
      {
        title: i18n.t('定价物料'),
        useToolTemplate: false,
        toolbar: toolbar(),
        gridId: 'db92366b-01f6-43a1-a46a-09e6d732fc59',
        grid: {
          allowFiltering: false,
          // lineIndex: true,
          columnData: [],
          dataSource: [{ itemCode: 'itemCode' }],
          asyncConfig: {
            url,
            defaultRules: [
              {
                label: '定点推荐id',
                field: 'point.id',
                type: 'string',
                operator: 'equal',
                value: id
              }
            ],
            serializeList
          },
          recordDoubleClick,
          editSettings: {
            allowAdding: true,
            allowEditing: true,
            allowDeleting: true,
            mode: 'Normal',
            allowEditOnDblClick: true,
            showConfirmDialog: false,
            showDeleteConfirmDialog: false,
            newRowPosition: 'Top'
          }
        }
      },
      {
        title: i18n.t('配额分配'),
        useToolTemplate: false,
        toolbar: toolbarTwo,
        grid: {
          allowFiltering: false,
          lineIndex: true,
          columnData: [],
          // dataSource: [],
          asyncConfig: {
            url,
            defaultRules: [
              {
                label: '定点推荐id',
                field: 'point.id',
                type: 'string',
                operator: 'equal',
                value: id
              }
            ],
            serializeList
          },
          recordDoubleClick,
          editSettings: {
            allowAdding: true,
            allowEditing: true,
            allowDeleting: true,
            mode: 'Normal',
            allowEditOnDblClick: true,
            showConfirmDialog: false,
            showDeleteConfirmDialog: false,
            newRowPosition: 'Top'
          }
        }
      }
    ]
  } else if (decidePriceType == 3) {
    _pageConfig = [
      {
        title: i18n.t('定价物料'),
        useToolTemplate: false,
        toolbar: toolbar(),
        gridId: '8f395337-43bf-4aca-8b80-7b5b4def0b38',
        grid: {
          allowFiltering: false,
          // lineIndex: true,
          columnData: [],
          dataSource: [{ itemCode: 'itemCode' }],
          asyncConfig: {
            url,
            defaultRules: [
              {
                label: '定点推荐id',
                field: 'point.id',
                type: 'string',
                operator: 'equal',
                value: id
              }
            ],
            serializeList
          },
          recordDoubleClick,
          editSettings: {
            allowAdding: true,
            allowEditing: true,
            allowDeleting: true,
            mode: 'Normal',
            allowEditOnDblClick: true,
            showConfirmDialog: false,
            showDeleteConfirmDialog: false,
            newRowPosition: 'Top'
          }
        }
      }
    ]
  } else if (decidePriceType == 4) {
    _pageConfig = [
      {
        title: i18n.t('定价物料'),
        useToolTemplate: false,
        toolbar: toolbar(),
        gridId: '0b7df138-7e3d-4b34-8bec-64e8a648dfc9',
        grid: {
          allowFiltering: false,
          // lineIndex: true,
          columnData: [],
          dataSource: [{ itemCode: 'itemCode' }],
          asyncConfig: {
            url,
            defaultRules: [
              {
                label: '定点推荐id',
                field: 'point.id',
                type: 'string',
                operator: 'equal',
                value: id
              }
            ],
            serializeList
          },
          recordDoubleClick,
          editSettings: {
            allowAdding: true,
            allowEditing: true,
            allowDeleting: true,
            mode: 'Normal',
            allowEditOnDblClick: true,
            showConfirmDialog: false,
            showDeleteConfirmDialog: false,
            newRowPosition: 'Top'
          }
        }
      },
      {
        title: i18n.t('分配组织'),
        useToolTemplate: false,
        toolbar: distributeToolbar(),
        grid: {
          allowFiltering: false,
          // lineIndex: true,
          columnData: [],
          dataSource: [{ itemCode: 'itemCode' }],
          asyncConfig: {
            url: '/sourcing/tenant/point2/pagePointExpand',
            defaultRules: [
              {
                label: '定点推荐id',
                field: 'pointId',
                type: 'string',
                operator: 'equal',
                value: id
              }
            ]
          },
          recordDoubleClick,
          editSettings: {
            allowAdding: true,
            allowEditing: true,
            allowDeleting: true,
            mode: 'Normal',
            allowEditOnDblClick: true,
            showConfirmDialog: false,
            showDeleteConfirmDialog: false,
            newRowPosition: 'Top'
          }
        }
      }
    ]
  } else if (decidePriceType == 5) {
    _pageConfig = [
      {
        title: i18n.t('定价物料'),
        useToolTemplate: false,
        toolbar: {
          useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
          tools: strikeToolbar()
        },
        gridId: '51b9f826-9b4d-4bc5-85eb-bc32c1866473',
        grid: {
          allowFiltering: false,
          // lineIndex: true,
          columnData: [],
          dataSource: [{ itemCode: 'itemCode' }],
          asyncConfig: {
            url,
            defaultRules: [
              {
                label: '定点推荐id',
                field: 'point.id',
                type: 'string',
                operator: 'equal',
                value: id
              },
              {
                field: 'item.status',
                type: 'string',
                operator: 'equal',
                value: '0'
              }
            ],
            serializeList
          },
          recordDoubleClick,
          editSettings: {
            allowAdding: true,
            allowEditing: true,
            allowDeleting: true,
            mode: 'Normal',
            allowEditOnDblClick: true,
            showConfirmDialog: false,
            showDeleteConfirmDialog: false,
            newRowPosition: 'Top'
          }
        }
      }
    ]
  } else if (decidePriceType == 7) {
    _pageConfig = [
      {
        title: i18n.t('定价物料'),
        useToolTemplate: false,
        toolbar: toolbar(),
        buttonQuantity: 6,
        gridId: 'a4dc4a44-a953-42ca-9c06-fbfd07790153',
        grid: {
          allowFiltering: false,
          // lineIndex: true,
          columnData: [],
          dataSource: [{ itemCode: 'itemCode' }],
          asyncConfig: {
            url,
            defaultRules: [
              {
                label: '定点推荐id',
                field: 'point.id',
                type: 'string',
                operator: 'equal',
                value: id
              }
            ],
            serializeList
          },
          recordDoubleClick,
          editSettings: {
            allowAdding: true,
            allowEditing: true,
            allowDeleting: true,
            mode: 'Normal',
            allowEditOnDblClick: true,
            showConfirmDialog: false,
            showDeleteConfirmDialog: false,
            newRowPosition: 'Top'
          }
        }
      }
    ]
  }
  return _pageConfig
}

export const otherPageConfig = (url, id, serializeList, recordDoubleClick) => [
  {
    title: i18n.t('定价物料'),
    useToolTemplate: false,
    toolbar: toolbar(),
    grid: {
      allowFiltering: false,
      lineIndex: true,
      columnData: [],
      dataSource: [{ itemCode: 'itemCode' }],
      asyncConfig: {
        url,
        defaultRules: [
          {
            label: '定点推荐id',
            field: 'point.id',
            type: 'string',
            operator: 'equal',
            value: id
          }
        ],
        serializeList
      },
      recordDoubleClick,
      editSettings: {
        allowAdding: true,
        allowEditing: true,
        allowDeleting: true,
        mode: 'Normal',
        allowEditOnDblClick: true,
        showConfirmDialog: false,
        showDeleteConfirmDialog: false,
        newRowPosition: 'Top'
      }
    }
  }
]

export const columnDataThree = [
  {
    width: '60',
    type: 'checkbox',
    allowEditing: false,
    showInColumnChooser: false
  },
  {
    field: 'lineNo',
    headerText: i18n.t('行号'),
    allowEditing: false
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司编码'),
    allowEditing: false,
    editTemplate: () => {
      return {
        template: cellChanged
      }
    }
  },
  {
    field: 'companyId',
    headerText: i18n.t('公司ID'),
    width: 0,
    allowResizing: false,
    allowEditing: false,
    editTemplate: () => {
      return {
        template: cellChanged
      }
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司'),
    editTemplate: () => {
      return {
        template: Select
      }
    }
  },
  {
    field: 'purOrgCode',
    headerText: i18n.t('采购组织编码'),
    allowEditing: false,
    editTemplate: () => {
      return {
        template: cellChanged
      }
    }
  },
  {
    field: 'purOrgId',
    headerText: i18n.t('采购组织ID'),
    width: 0,
    allowResizing: false,
    allowEditing: false,
    editTemplate: () => {
      return {
        template: cellChanged
      }
    }
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织'),
    width: 220,
    editTemplate: () => {
      return {
        template: Select
      }
    }
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂编码'),
    allowEditing: false,
    editTemplate: () => {
      return {
        template: cellChanged
      }
    }
  },
  {
    field: 'siteId',
    headerText: i18n.t('工厂Id'),
    width: 0,
    allowResizing: false,
    allowEditing: false,
    editTemplate: () => {
      return {
        template: cellChanged
      }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: 250,
    editTemplate: () => {
      return {
        template: Select
      }
    }
  }
]
