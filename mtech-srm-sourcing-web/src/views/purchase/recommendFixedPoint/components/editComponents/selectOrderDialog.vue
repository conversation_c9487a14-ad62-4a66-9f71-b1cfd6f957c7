<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel" z-index="2">
    <div class="dialog-content">
      <mt-template-page ref="templateRef" :template-config="pageConfig" />
    </div>
  </mt-dialog>
</template>

<script>
import { orderColumnData } from './config'
export default {
  data() {
    return {
      data: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: [],
          gridId: 'a0ef0c9b-c6ee-4ad6-8adc-47cba9204a7e',
          grid: {
            lineSelection: true,
            allowFiltering: true,
            columnData: orderColumnData,
            allowSorting: false,
            allowSelection: true,
            selectionSettings: {
              type: 'Multiple',
              mode: 'Row'
            },
            // recordDoubleClick: this.recordDoubleClick,
            asyncConfig: {
              url: `/srm-purchase-execute/tenant/po/detail`,
              condition: 'and',
              defaultRules: [
                {
                  condition: 'and',
                  field: 'business_type_id',
                  label: '',
                  operator: 'equal',
                  value: '1493479350943789058'
                },
                {
                  label: this.$t('订单状态'),
                  field: 'status',
                  operator: 'equal',
                  value: '5'
                },
                {
                  label: this.$t('供应商编码'),
                  field: 'supplierCode',
                  operator: 'equal',
                  value: this.modalData.supplierCode
                }
              ]
            }
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    recordDoubleClick(e) {
      console.log('double', e)
      this.confirmEvent([e.rowData])
    },
    confirm() {
      let _records = this.$refs.templateRef.getCurrentTabRef().gridRef.getMtechGridRecords()
      if (_records.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      } else {
        this.$store.commit('startLoading')
        this.confirmEvent(_records)
      }
    },
    async confirmEvent(_records) {
      let _saveRecords = []
      for (const item of _records) {
        let getPlanTime = this.$API.rfxList.getPlanTime({
          itemCode: item.itemCode,
          siteCode: item.siteCode
        })
        // 报价属性、报价方式
        let getDefaultValue = this.$API.rfxList.getDefaultValue({
          categoryCode: item.categoryCode,
          companyCode: item.companyCode,
          supplierCode: item.supplierCode
        })
        //  上次价格
        let getLastPrice = this.$API.rfxList.getLastPrice({
          isLowPrice: 1,
          itemCode: item.itemCode,
          priceClassification: 4,
          purOrgCode: item.buyerGroupCode,
          siteCode: item.siteCode,
          companyCode: item.companyCode,
          supplierCode: item.supplierCode
        })

        await Promise.all([getPlanTime, getDefaultValue, getLastPrice])
          .then((result) => {
            _saveRecords.push({
              plannedDeliveryTime: result[0].data.plannedDeliveryTime,
              quoteAttribute: result[1].data?.quoteAttribute ? result[1].data.quoteAttribute : '',
              quoteMode: result[1].data?.quoteMode ? result[1].data.quoteMode : '',
              lowUntaxedExercisePrice: result[2].data.lowUntaxedExercisePrice,
              priceDifference: 0,
              orderNo: item.orderCode,
              companyId: item.realCompanyId,
              companyName: item.companyName,
              companyCode: item.companyCode,
              purOrgId: item.buyerGroupId,
              purOrgName: item.buyerGroupName,
              purOrgCode: item.buyerGroupCode,
              siteName: item.siteName,
              siteId: item.siteId,
              siteCode: item.siteCode,
              itemCode: item.itemCode,
              itemName: item.itemName,
              categoryCode: item.categoryCode,
              categoryName: item.categoryName,
              purGroupName: item.buyerOrgName,
              purGroupCode: item.buyerOrgCode,
              unitCode: item.unitCode,
              unitName: item.unitName,
              taxedUnitPrice: item.taxPrice,
              untaxedUnitPrice: item.freePrice,
              quoteEffectiveStartDate: 0,
              quoteEffectiveEndDate: 0,
              pointId: this.modalData.pointId,
              referChannel: 0,
              stepQuote: 0,
              priceUnitName: 1
            })
          })
          .catch(() => {
            this.$store.commit('endLoading')
          })
      }
      console.log('_saveRecords', _saveRecords)
      this.$API.rfxList
        .savePointItemList(_saveRecords)
        .then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('添加成功'), type: 'success' })
            this.$store.commit('endLoading')
            this.$emit('confirm-function', _records)
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
