<template>
  <div style="width: calc(100% - 10px)">
    <mt-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @open="startOpen"
      @change="selectChange"
      :open-dispatch-change="false"
      :disabled="isDisabled"
      :allow-filtering="true"
      :filtering="serchText"
    ></mt-select>
  </div>
</template>
<script>
import bus from '@/utils/bus'
export default {
  data() {
    return {
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'label', value: 'value' },
      dataSource: [],
      itemCode: '',
      itemId: '',
      costCenterCode: '',
      isDisabled: false,
      siteName: ''
    }
  },
  async mounted() {
    if (this.data.column.field === 'companyName') {
      console.log('this.data.companyName', this.data)
      this.getCompanyList()
      this.fields = { text: 'orgName', value: 'orgName' }
    }
    if (this.data.column.field === 'purOrgName') {
      console.log('this.data.purOrgName', this.data)
      if (!this.data.companyId) {
        this.isDisabled = true
      } else {
        this.getPurOrgList(this.data.companyId)
      }
      bus.$off('purOrgNameChange')
      bus.$on('purOrgNameChange', (val) => {
        console.log('purOrgNameChange=', val)
        this.isDisabled = !val.id
        this.data.purOrgName = null
        if (val.id) {
          this.getPurOrgList(val.id)
        }
      })
      this.fields = { text: 'organizationName', value: 'organizationName' }
    }
    if (this.data.column.field === 'siteName') {
      if (!this.data.purOrgName || !this.data.companyName) {
        this.isDisabled = true
      } else if (this.data.purOrgName && this.data.companyName) {
        let params = {
          purOrgId: this.data.purOrgId,
          companyId: this.data.companyId
        }
        this.getFactoryList(params)
      }
      bus.$off('siteNameChange')
      bus.$on('siteNameChange', (val) => {
        console.log('siteNameChange=', val, this.data)
        this.isDisabled = !val?.id
        this.data.siteName = null
        if (val?.id) {
          let params = {
            purOrgId: val.id,
            companyId: sessionStorage.getItem('dstriOrgCompanyId')
          }
          this.getFactoryList(params)
        }
      })

      this.fields = { text: 'orgName', value: 'orgName' }
    }
  },
  methods: {
    getCompanyList() {
      this.$API.masterData
        .findSpecifiedChildrenLevelOrgs({
          organizationLevelCodes: ['ORG02'],
          orgType: 'ORG001PRO',
          includeItself: false,
          organizationIds: []
        })
        .then((res) => {
          this.dataSource = res.data
        })
    },
    getPurOrgList(companyId) {
      this.$API.masterData
        .permissionOrgList({
          orgId: companyId
        })
        .then((res) => {
          this.dataSource = res.data
        })
    },
    getFactoryList(params) {
      this.$API.masterData
        .permissionSiteList({
          buOrgId: params.purOrgId,
          companyId: params.companyId,
          orgLevelTypeCode: 'ORG06'
        })
        .then((res) => {
          this.dataSource = res.data
        })
    },
    serchText(val) {
      console.log('搜索值', val, this.data.column.field)
      val.updateData(this.dataSource.filter((e) => e[this.fields.value].includes(val.text)))
    },
    startOpen() {
      // // 成本中心
      // if (this.data.column.field === "costCenterCode") {
      //   console.log("startOpen=", this);
      //   if (!!this.data.companyId) {
      //     this.getCostCenter();
      //   }
      // }
    },
    selectChange(val) {
      console.log(val.itemData, '下拉数据的信息')
      console.log('val', val, this.data)
      if (this.data.column.field === 'companyName') {
        bus.$emit('purOrgIdChange', null)
        bus.$emit('purOrgCodeChange', null)
        bus.$emit('siteIdChange', null)
        bus.$emit('siteCodeChange', null)
        if (val.value) {
          sessionStorage.setItem('dstriOrgCompanyId', val.itemData.id)
          bus.$emit('purOrgNameChange', val.itemData)
          bus.$emit('siteNameChange', null)
          bus.$emit('companyIdChange', val.itemData.id)
          bus.$emit('companyCodeChange', val.itemData.orgCode)
          this.$parent.$emit('selectedChanged', {
            fieldCode: 'companyName',
            itemInfo: {
              companyId: val.itemData.id,
              companyCode: val.itemData.orgCode,
              purOrgId: null,
              purOrgCode: null,
              purOrgName: null,
              siteId: null,
              siteCode: null
            }
          })
        } else {
          this.$parent.$emit('selectedChanged', {
            fieldCode: 'companyName',
            itemInfo: {
              companyId: null,
              companyCode: null,
              purOrgId: null,
              purOrgCode: null,
              purOrgName: null,
              siteId: null,
              siteCode: null
            }
          })
        }
      }
      if (this.data.column.field === 'purOrgName') {
        if (val.e) {
          bus.$emit('siteIdChange', null)
          bus.$emit('siteCodeChange', null)

          if (val.value) {
            bus.$emit('siteNameChange', val.itemData)
            bus.$emit('purOrgIdChange', val.itemData.id)
            bus.$emit('purOrgCodeChange', val.itemData.organizationCode)
            this.$parent.$emit('selectedChanged', {
              fieldCode: 'purOrgName',
              itemInfo: {
                purOrgId: val.itemData.id,
                purOrgCode: val.itemData.organizationCode,
                siteId: null,
                siteCode: null
              }
            })
          } else {
            this.$parent.$emit('selectedChanged', {
              fieldCode: 'purOrgName',
              itemInfo: {
                purOrgId: null,
                purOrgCode: null,
                siteId: null,
                siteCode: null
              }
            })
          }
        }
      }
      if (this.data.column.field === 'siteName') {
        if (val.value) {
          bus.$emit('siteIdChange', val.itemData.id)
          bus.$emit('siteCodeChange', val.itemData.orgCode)
          this.$parent.$emit('selectedChanged', {
            fieldCode: 'siteName',
            itemInfo: {
              siteId: val.itemData.id,
              siteCode: val.itemData.orgCode
            }
          })
        } else {
          this.$parent.$emit('selectedChanged', {
            fieldCode: 'siteName',
            itemInfo: {
              siteId: null,
              siteCode: null
            }
          })
        }
      }
    }
  }
}
</script>
