<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell" :title="value">
      <!-- 选择供应商 -->
      <mt-input class="flex-1" v-model="value" disabled :placeholder="headerTxt"></mt-input>
      <mt-icon
        v-if="isEdit"
        style="width: 20px"
        name="icon_input_search"
        @click.native="showDialog"
      ></mt-icon>
    </div>

    <mt-dialog
      ref="dialog"
      css-class="pc-item-dialog"
      :header="$t('选择供应商')"
      :buttons="buttons"
      @close="handleClose"
    >
      <mt-template-page
        v-show="dialogShow"
        ref="templateRef"
        :hidden-tabs="true"
        :template-config="pageConfig"
        @recordDoubleClick="recordDoubleClick"
      >
      </mt-template-page>
    </mt-dialog>
  </div>
</template>

<script>
import { columnData } from './config/index' // 命名要与field code一致
export default {
  props: {
    asyncConfigUrl: {
      type: String,
      default: ''
    },
    isEdit: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      data: {},
      value: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [],
          gridId: 'bfda271f-09ef-4d79-b0e3-11a508dc8413',
          grid: {
            allowPaging: true,
            allowSelection: true,
            selectionSettings: {
              checkboxOnly: false
            },
            columnData,
            asyncConfig: {
              url: this.asyncConfigUrl
              // params: {
              //   defaultRules: [
              //     {
              //       field: "statusId",
              //       operator: "equal",
              //       value: "10",
              //     },
              //   ],
              // },
            }
          }
        }
      ],
      fields: {},
      headerTxt: '',
      dialogShow: false,
      itemCode: ''
    }
  },
  mounted() {},

  methods: {
    // 双击行，也进行提交
    recordDoubleClick(args) {
      console.log('recordDoubleClick', args)
      this.confirm([args.rowData])
    },
    // 提交
    confirm(records) {
      let _records = records
      if (!_records || !_records.length) {
        _records = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords()
      }
      if (!_records.length) return
      console.log('选择到的信息：', _records[0])
      if (_records[0]) {
        let selectedRowInfo = _records[0]
        this.value = selectedRowInfo.supplierCode + '-' + selectedRowInfo.supplierName
        // this.$parent.$parent.$parent.forObject.supplierCode = selectedRowInfo.supplierCode
        // this.$parent.$parent.$parent.forObject.supplierName = selectedRowInfo.supplierName
        // this.$parent.$parent.$parent.forObject.supplierId = selectedRowInfo.supplierId
        this.$emit('change', selectedRowInfo)
        this.handleClose()
      }
      // if (_records) {
      //   let selectedRowInfo = _records;
      //   this.value = selectedRowInfo
      //     .map((e) => `${e.supplierCode}-${e.supplierName}`)
      //     .toString();
      //   this.$parent.$parent.$parent.forObject.supplierCode =
      //     selectedRowInfo.map((e) => e.supplierCode);
      //   if (this.value) {
      //     this.$parent.$parent.$parent.$refs.ruleForm.clearValidate(
      //       "supplierCode"
      //     );
      //   }
      //   this.handleClose();
      // }
    },
    showDialog() {
      this.dialogShow = true
      this.$refs.dialog.ejsRef.show()
    },
    handleClose() {
      this.dialogShow = false
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
.flex-1 {
  flex: 1;
}
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > .mt-icons {
      margin-left: 5px;
      cursor: pointer;
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
</style>

<style lang="scss">
.pc-item-dialog {
  .mt-data-grid {
    height: 100%;
    > .e-grid {
      height: calc(100% - 40px) !important;
      // display: flex;

      > .e-gridcontent {
        flex: 1;
        overflow: auto;
      }
    }

    .e-rowcell.e-active {
      background: #e0e0e0 !important;
    }
  }
}
</style>
