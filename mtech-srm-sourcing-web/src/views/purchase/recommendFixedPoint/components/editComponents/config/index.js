import { i18n } from '@/main.js'
export const columnData = [
  // { type: "checkbox", width: "50" },
  { width: '150', field: 'supplierCode', headerText: i18n.t('供应商编码') },
  { width: '150', field: 'supplierName', headerText: i18n.t('供应商名称') },
  { width: '150', field: 'organizationName', headerText: i18n.t('采方公司') },
  { width: '150', field: 'statusDescription', headerText: i18n.t('状态') }
]

export const cateColumnData = [
  { width: '150', field: 'categoryCode', headerText: i18n.t('品类编码') },
  { width: '150', field: 'categoryName', headerText: i18n.t('品类名称') }
  // { width: "150", field: "statusDescription", headerText: i18n.t("状态") },
]

export const orderColumnData = [
  {
    width: '150',
    field: 'orderCode',
    headerText: i18n.t('采购订单号'),
    cssClass: 'click'
  },
  {
    width: '120',
    field: 'companyCode',
    headerText: i18n.t('公司编码'),
    cssClass: 'click'
  },
  {
    width: '220',
    field: 'companyName',
    headerText: i18n.t('公司名称'),
    cssClass: 'click'
  },
  {
    width: '145',
    field: 'buyerGroupCode',
    headerText: i18n.t('采购组织编码'),
    cssClass: 'click'
  },
  {
    width: '200',
    field: 'buyerGroupName',
    headerText: i18n.t('采购组织名称'),
    cssClass: 'click'
  },
  {
    width: '120',
    field: 'siteCode',
    headerText: i18n.t('工厂编码'),
    cssClass: 'click'
  },
  {
    width: '240',
    field: 'siteName',
    headerText: i18n.t('工厂名称'),
    cssClass: 'click'
  },
  {
    width: '130',
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    cssClass: 'click'
  },
  {
    width: '220',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    cssClass: 'click'
  },
  {
    width: '120',
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    cssClass: 'click'
  },
  {
    width: '140',
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组编码'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'buyerOrgName',
    headerText: i18n.t('采购组名称'),
    cssClass: 'click'
  },
  {
    width: '120',
    field: 'unitCode',
    headerText: i18n.t('单位编码'),
    cssClass: 'click'
  },
  {
    width: '120',
    field: 'unitName',
    headerText: i18n.t('单位名称'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'freePrice',
    headerText: i18n.t('单价（未税）'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'taxPrice',
    headerText: i18n.t('单价（含税）'),
    cssClass: 'click'
  }
]
