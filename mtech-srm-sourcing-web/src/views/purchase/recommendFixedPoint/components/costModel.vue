<template>
  <div class="tempalte-detail mt-flex-direction-column">
    <!-- <div class="page-top" @click="cancel"></div> -->
    <div class="page-bottom mt-flex">
      <div class="page-content fbox">
        <div class="detail-top">
          <div class="left">
            <div class="title">
              {{ formObject.costModelCode }}

              <span class="titleC">{{ formObject.costModelName }}</span>
              <!-- <span class="statusC">{{ status }}</span> -->
            </div>
            <div class="form">
              <div class="form_div">
                {{ $t('是否暖通') }}：{{ formObject.isHavc == 1 ? $t('是') : $t('否') }}
              </div>
              <div class="form_div">
                {{ $t('是否联动') }}：{{ formObject.isLinkage == 1 ? $t('是') : $t('否') }}
              </div>
              <div class="form_div">{{ $t('物料') }}：{{ formObject.itemName }}</div>
              <div class="form_div">{{ $t('品类') }}：{{ formObject.categoryName }}</div>
              <div class="form_div">{{ $t('供应商') }}：{{ formObject.supplierName }}</div>
              <!-- <div class="form_div">{{ $t('核价模型名称') }}：{{ formObject.costModelName }}</div> -->
              <div class="form_div">
                {{ $t('核价模型版本号') }}：{{ formObject.version || '--' }}
              </div>
              <!-- <div class="form_div">{{ $t('报价类型') }}：{{ $t('测算') }}</div>
              <div class="form_div">{{ $t('业务方') }}：{{ $t('采购方') }}</div> -->
              <div class="form_formulaTotal">{{ $t('公式') }}：{{ formObject.formulaTotal }}</div>
            </div>
          </div>
          <div class="right">
            <div class="right_but" @click="$router.go(-1)">
              {{ $t('返回') }}
            </div>
            <!-- <div class="right_but" @click="confirm">保存</div> -->
          </div>
        </div>
        <div :class="[bottomShow ? 'detail-content' : 'detail-content-no']">
          <mt-template-page
            v-if="show"
            ref="templateRefTree"
            :template-config="pageConfig"
            @rowSelected="rowSelected"
            @rowDeselected="rowDeselected"
            @handleClickToolBar="handleClickToolBar"
            @handleClickCellTitle="handleClickCellTitle"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { pageConfig } from './config/costModel'
// import HandleFormulaDialog from "./dialog/handleFormulaDialog.vue";
// import { EventBus } from "@/utils/event-bus.js";
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  components: {},
  computed: {
    bottomShow() {
      return JSON.stringify(this.activeData) == '{}' ? false : true
    },
    costModelId() {
      return this.$route.query.configId ? this.$route.query.configId : '176786814758723680'
    },
    status() {
      switch (this.formObject.status) {
        case -1:
          return this.$t('已停用')
        case 0:
          return this.$t('草稿')
        case 1:
          return this.$t('审批中')
        case 2:
          return this.$t('审批驳回')
        case 3:
          return this.$t('已生效')
        default:
          return this.$t('暂无状态')
      }
    }
  },
  data() {
    return {
      editStatus: false,
      treeGridRef: {},
      show: true,
      pageConfig: pageConfig(
        {
          pointId: this.$route.query.pointId,
          pointItemId: this.$route.query.pointItemId
        },
        'sourcing/tenant/point2/queryCostModelItem',
        this
      ),
      activeData: {},
      formData: {},
      formObject: {},
      dataSource: [
        {
          nodeName: this.$t('初始化') + '1',
          id: '1',
          node: '0',
          children: [
            {
              nodeName: this.$t('合同指标') + '1',
              id: '2',
              node: '1',
              children: []
            },
            {
              nodeName: this.$t('合同指标') + '2',
              id: '3',
              node: '1'
            }
          ]
        },
        {
          nodeName: this.$t('初始化') + '2',
          id: '4',
          node: '0',
          indexList: [
            {
              nodeName: this.$t('合同指标') + '3',
              id: '5',
              node: '1'
            }
          ]
        }
      ],
      tab2Column: [],
      tab2ItemDataList: [],
      dimensionList: [] //维度列表
    }
  },
  mounted() {
    // this.queryCostModelItem()
  },
  methods: {
    handleClickToolBar(e) {
      if (e.toolbar.id == 'excelExport') {
        this.handleExcelExport()
      }
    },
    // 导出
    handleExcelExport() {
      this.$API.supplierBiddingCost
        .exportPurList({
          pointId: this.$route.query.pointId,
          pointItemId: this.$route.query.pointItemId
        })
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .e-grid .e-detailcell {
  padding: 0;
  border: none;
}
/deep/ .e-toolbar.e-control {
  text-align: left !important;
}
.tempalte-detail {
  background: transparent;
  // height: 100%;

  .page-top {
    height: 60px;
    flex-shrink: 0;
  }
  .page-bottom {
    flex: 1;
    height: 100%;
  }

  .page-content {
    background: #fff;
    flex: 1;
    // padding: 20px;
    height: 100%;
    padding: 19px 20px 0 20px;
    flex-direction: column;
    width: 100%;
  }
  .detail-top {
    margin-bottom: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 20px 30px 10px 30px;
    background: rgba(99, 134, 193, 0.08);
    border: 1px solid #e8e8e8ff;
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    .right_but {
      display: inline-block;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      color: #00469cff;
      margin: 0 10px;
    }
    .left {
      color: #292929;
      .title {
        font-size: 20px;
        font-weight: 600;
        .statusC {
          display: inline-block;
          margin-left: 22px;
          // width: 48px;
          padding: 0 5px;
          height: 24px;
          line-height: 22px;
          text-align: center;
          background: #e8ecf5;
          border-radius: 2px;
          font-size: 12px;
          color: rgba(99, 134, 193, 1);
          font-weight: 500;
        }
        .titleC {
          display: inline-block;
          margin-left: 11px;
          height: 22px;
          font-size: 16px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(154, 154, 154, 1);
        }
      }
      .form {
        padding: 10px 0;
        font-size: 12px;
        font-weight: normal;
      }
      .form_div {
        display: inline-block;
        padding-right: 20px;
      }
      .form_formulaTotal {
        padding-top: 8px;
      }
      .data {
        padding: 10px 0;
        font-size: 14px;
        font-weight: 600;
        span {
          margin-left: 3px;
          color: #00469c;
        }
      }
    }
  }
  .detail-content-no {
    background: #e8e8e8;
    // height: calc(100vh - 240px);
    overflow: auto;
    transition: 0.3s;
  }
  .detail-content {
    background: #e8e8e8;
    height: calc(100vh - 550px);
    min-height: 500px;
    overflow: auto;
    // transition: 0.3s;
  }
}

/deep/.e-grid {
  .e-content {
    overflow: auto !important;
    position: relative;
    // height: calc(100vh - 580px) !important;
    height: 100% !important;
  }
  .e-rowcell {
    text-align: left !important;
    .grid-edit-column {
      // display: inline-block !important;
    }
  }
}
</style>
