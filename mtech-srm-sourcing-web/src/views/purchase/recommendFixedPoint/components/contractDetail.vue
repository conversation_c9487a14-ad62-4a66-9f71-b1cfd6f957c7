<template>
  <div class="full-height mt-flex-direction-column">
    <div class="expand-and-collapse-box">
      <div class="sort-box" @click="expandAndCollapseFn">
        <span>{{ expandAndCollapse ? $t('收起') : $t('展开') }}</span>
        <div class="icon-container">
          <p>
            <i
              :class="expandAndCollapse ? 'mt-icon-icon_Sort_up' : 'mt-icon-icon_Sort_down'"
              class="mt-icons"
            ></i>
          </p>
          <p>
            <i
              :class="expandAndCollapse ? 'mt-icon-icon_Sort_up' : 'mt-icon-icon_Sort_down'"
              class="mt-icons"
            ></i>
          </p>
        </div>
      </div>
    </div>

    <div class="miam-container">
      <div class="operate-bar">
        <div class="operate-content">
          <!-- <div class="operate-bars" @click="comeBack">
            <i class="mt-icons mt-icon-icon_input_arrow"></i>
          </div> -->
          <p class="operate-input">
            {{ this.forObject.pricingCode }} {{ $t('创建时间：')
            }}<span>{{ formatDate(this.forObject.createTime) }}</span>
          </p>
          <div class="btns-wrap">
            <mt-button flat @click="comeBack()">{{ $t('返回') }}</mt-button>
            <mt-button v-show="!isDisabled" @click="save()">{{ $t('保存') }}</mt-button>
            <mt-button flat v-show="!isDisabled && allowSubmit" @click="submit()">{{
              $t('提交')
            }}</mt-button>
          </div>
        </div>
      </div>
      <!-- 顶部主要信息 -->
      <div class="mian-info" v-show="expandAndCollapse">
        <mt-form ref="ruleForm" :model="forObject" :rules="rules">
          <mt-form-item
            prop="pricingCode"
            :label="$t('合同编号')"
            label-style="left"
            label-width="80px"
          >
            <mt-input disabled float-label-type="Never" v-model="forObject.pricingCode"></mt-input>
          </mt-form-item>
          <!-- <mt-form-item
            prop="companyCode"
            :label="$t('公司')"
            label-style="left"
            label-width="80px"
          >
            <mt-select
              :disabled="resInfo.status != 0 && resInfo.status != 3"
              :open-dispatch-change="false"
              v-model="forObject.companyCode"
              float-label-type="Never"
              :data-source="companyList"
              :fields="{ text: 'orgName', value: 'orgCode' }"
              @change="companyChange"
              :placeholder="$t('请选择公司')"
              :show-clear-button="true"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            prop="purOrgCode"
            :label="$t('采购组织')"
            label-style="left"
            label-width="80px"
          >
            <mt-select
              :disabled="resInfo.status != 0 && resInfo.status != 3"
              :open-dispatch-change="false"
              v-model="forObject.purOrgCode"
              :fields="{
                text: 'organizationName',
                value: 'organizationCode'
              }"
              :data-source="purOrgList"
              :placeholder="$t('请选择')"
              @change="purOrgChange"
              :show-clear-button="true"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="siteCode" :label="$t('工厂')" label-style="left" label-width="80px">
            <mt-select
              :disabled="resInfo.status != 0 && resInfo.status != 3"
              :open-dispatch-change="false"
              v-model="forObject.siteCode"
              float-label-type="Never"
              :data-source="siteList"
              :fields="{ text: 'orgName', value: 'orgCode' }"
              @change="siteChange"
              :placeholder="$t('请选择工厂')"
              :show-clear-button="true"
            ></mt-select>
          </mt-form-item> -->
          <mt-form-item
            ref="state"
            prop="state"
            :label="$t('状态')"
            label-style="left"
            label-width="80px"
          >
            <mt-select
              disabled
              :data-source="[
                { text: $t('已作废'), value: -1 },
                { text: $t('草稿'), value: 0 },
                { text: $t('审批中'), value: 1 },
                { text: $t('审批通过'), value: 2 },
                { text: $t('审批驳回'), value: 3 },
                { text: $t('同步中'), value: 4 },
                { text: $t('同步成功'), value: 5 },
                { text: $t('同步失败'), value: 6 },
                { text: $t('审批废弃'), value: 7 },
                { text: $t('审批撤回'), value: 8 }
              ]"
              v-model="forObject.state"
              :show-clear-button="true"
              :placeholder="$t('请输入状态')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            prop="supplierCode"
            :label="$t('供应商')"
            label-style="left"
            label-width="80px"
          >
            <!-- <mt-select
              :disabled="resInfo.status != 0 && resInfo.status != 3"
              :open-dispatch-change="false"
              v-model="forObject.supplierCode"
              float-label-type="Never"
              :data-source="supplierList"
              :fields="{ text: 'supplierName', value: 'supplierCode' }"
              @change="changeSupplier"
              :placeholder="$t('请选择供应商')"
              :show-clear-button="true"
            ></mt-select> -->
            <supDialog
              style="width: 100%"
              ref="supDialog"
              v-bind="$attrs"
              v-on="$listeners"
              v-model="forObject.supplierCode"
              :async-config-url="supplierUrl"
              :is-edit="!isDisabled"
              @change="supplierCodeChange"
            ></supDialog>
          </mt-form-item>
          <mt-form-item
            prop="priceClassification"
            :label="$t('价格类型')"
            label-style="left"
            label-width="80px"
          >
            <mt-select
              :open-dispatch-change="false"
              :disabled="isDisabled"
              :data-source="[
                { text: $t('暂估价'), value: 3 },
                { text: $t('正式价'), value: 4 }
              ]"
              v-model="forObject.priceClassification"
              :show-clear-button="true"
              :placeholder="$t('请输入定价单类型')"
              @change="priceClassificationChange"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            prop="contractType"
            :label="$t('合同类型')"
            label-style="left"
            label-width="80px"
          >
            <mt-select
              :open-dispatch-change="false"
              :disabled="isDisabled"
              v-model="forObject.contractType"
              :data-source="contractTypeList"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              @change="contractTypeChange"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            prop="contractYear"
            :label="$t('合同年度')"
            label-style="left"
            label-width="80px"
          >
            <mt-input
              :disabled="isDisabled"
              v-model="forObject.contractYear"
              :show-clear-button="true"
              :placeholder="$t('请输入')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            prop="signedAddress"
            :label="$t('签约地点')"
            label-style="left"
            label-width="80px"
          >
            <mt-input
              :disabled="isDisabled"
              v-model="forObject.signedAddress"
              :show-clear-button="true"
              :placeholder="$t('请输入')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            prop="currencyName"
            :label="$t('币种')"
            label-style="left"
            label-width="80px"
          >
            <mt-input
              disabled
              v-model="forObject.currencyName"
              :show-clear-button="true"
            ></mt-input>
          </mt-form-item>
          <!-- <mt-form-item
            prop="taxRateName"
            :label="$t('税率')"
            label-style="left"
            label-width="80px"
          >
            <mt-input disabled v-model="forObject.taxRateName" :show-clear-button="true"></mt-input>
          </mt-form-item> -->
          <mt-form-item
            prop="contractStartTime"
            :label="$t('合同日期从')"
            label-style="left"
            label-width="80px"
          >
            <mt-date-picker
              :disabled="isDisabled"
              v-model="forObject.contractStartTime"
              :placeholder="$t('选择日期')"
            ></mt-date-picker>
          </mt-form-item>
          <mt-form-item
            prop="contractEndTime"
            :label="$t('合同日期至')"
            label-style="left"
            label-width="80px"
          >
            <mt-date-picker
              :disabled="isDisabled"
              v-model="forObject.contractEndTime"
              :placeholder="$t('选择日期')"
            ></mt-date-picker>
          </mt-form-item>
          <mt-form-item
            prop="signedTime"
            :label="$t('签订日期')"
            label-style="left"
            label-width="80px"
          >
            <mt-date-picker
              :disabled="isDisabled"
              v-model="forObject.signedTime"
              :placeholder="$t('选择日期')"
            ></mt-date-picker>
          </mt-form-item>
          <mt-form-item
            prop="createUserName"
            :label="$t('创建人')"
            label-style="left"
            label-width="80px"
          >
            <mt-input
              disabled
              v-model="forObject.createUserName"
              :show-clear-button="true"
            ></mt-input>
          </mt-form-item>
          <mt-form-item ref="state" :label="$t('附件')" label-style="left" label-width="80px">
            <mt-common-uploader
              ref="uploadFile"
              :is-view="isDisabled"
              :is-single-file="false"
              :save-url="saveUrl"
              :download-url="downloadUrl"
              type="line"
              v-model="fileList"
              @confirm="setFile"
            ></mt-common-uploader>
          </mt-form-item>
          <mt-form-item
            prop="compressor"
            label-style="left"
            class="custom-width"
            label=" "
            label-width="50px"
          >
            <mt-checkbox
              :disabled="isDisabled"
              :label="$t('压缩机（暖通压缩机/电机/电控）')"
              class="checkbox-item"
              v-model="forObject.compressor"
              @change="compressorChange"
            ></mt-checkbox>
            <mt-checkbox
              :disabled="isDisabled"
              :label="$t('是否自动同步货源到SAP')"
              class="checkbox-item"
              v-model="forObject.sourceToSapFlag"
              @change="sourceToSapFlagChange"
            ></mt-checkbox>
          </mt-form-item>
          <mt-form-item label-style="left" label-width="80px"> </mt-form-item>
          <mt-form-item
            prop="contractTerms"
            :label="$t('合同条款')"
            label-style="left"
            label-width="80px"
            class="full-width"
          >
            <mt-input
              :width="1075"
              :disabled="isDisabled"
              v-model="forObject.contractTerms"
              :show-clear-button="true"
              :placeholder="$t('字数不超过200字')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            prop="remark"
            :label="$t('合同备注')"
            label-style="left"
            label-width="80px"
            class="full-width"
          >
            <mt-input
              :width="1075"
              :disabled="isDisabled"
              v-model="forObject.remark"
              :show-clear-button="true"
              :placeholder="$t('字数不超过200字')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </div>
    <div class="relation-ships">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickCellTitle="handleClickCellTitle"
        @handleClickToolBar="handleClickToolBar"
        v-if="pageConfig.length > 0 && pageConfig[0].grid.columnData.length > 0"
        @actionBegin="actionBegin"
        @actionComplete="actionComplete"
      />
    </div>
  </div>
</template>

<script>
import bus from '@/utils/bus'
import utils from '@/utils/utils'
import supDialog from './editComponents/dialog.vue'
import { pageConfig, columnDataOne, contractToolbar } from './config'
import { download, getHeadersFileName } from '@/utils/utils'

let directDelivery = []
let allCurrency = []
let taxList,
  companyList = []

export default {
  name: 'ExpertRating',
  inject: ['reload'],
  components: { supDialog },
  data() {
    return {
      expandAndCollapse: true,
      isDisabled: true,
      supplierUrl: '/masterDataManagement/tenant/supplier/paged-query-distinct',
      allowSubmit: true,
      currencyList: [],
      purOrgList: [],
      siteList: [],
      tabIndex: 0,
      ktFlag: 1,
      contractTypeList: [
        { text: this.$t('标准'), value: 'standard' },
        { text: this.$t('钢材'), value: 'rolled' },
        { text: this.$t('委外'), value: 'outsource' }
      ],
      fileList: [],
      saveUrl: '/api/file/user/file/uploadPublic?useType=1',
      downloadUrl: '/api/file/user/file/downloadPrivateFile?useType=1',
      pageConfig: [],
      forObject: {
        compressor: false,
        sourceToSapFlag: false,
        companyCode: '', //公司
        personnel: '', //采购人员
        pricingCode: '', //定价单号
        pricingName: '',
        companyName: '',
        purOrgCode: '', //采购组织
        pricingType: '', //定价类型
        state: '', //状态
        remark: '', //备注
        priceClassification: '',
        title: ''
      },
      busEvent: new Set(), // 事件收集
      resInfo: {},
      siteNameDataSource: [],
      supplierList: [],
      purGroupList: [],
      rules: {
        //定价单类型
        decidePriceType: {
          required: true,
          message: this.$t('请选择定价单类型'),
          trigger: 'blur'
        },
        // 供应商
        supplierCode: {
          required: true,
          message: this.$t('请选择供应商'),
          trigger: 'blur'
        },
        // 公司
        companyCode: {
          required: true,
          message: this.$t('请选择公司'),
          trigger: 'blur'
        },
        // 	采购组织
        purOrgCode: {
          required: true,
          message: this.$t('请选择采购组织'),
          trigger: 'blur'
        },
        // 	工厂
        siteCode: {
          required: true,
          message: this.$t('请选择工厂'),
          trigger: 'blur'
        },
        priceClassification: {
          required: true,
          message: this.$t('请选择价格分类'),
          trigger: 'blur'
        },
        contractType: {
          required: true,
          message: this.$t('请选择合同类型'),
          trigger: 'blur'
        }
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  watch: {
    modalData: {
      handler(newVal) {
        console.log(5050505, newVal)
      }
    }
  },

  computed: {
    propsData() {
      return {
        ...this.modalData
      }
    },
    decidePriceType() {
      return Number(this.$route.query.decidePriceType)
    }
  },
  mounted() {
    this.initData()
  },
  activated() {
    this.initData()
  },
  methods: {
    expandAndCollapseFn() {
      return (this.expandAndCollapse = !this.expandAndCollapse)
    },

    async initData() {
      // this.getAllCurrency()
      sessionStorage.setItem('_isKTflag', 1)
      // let _pageConfig = pageConfig(
      //   this.$API.rfxList.pagePointItem,
      //   this.$route.query.id,
      //   this.serializeList,
      //   this.recordDoubleClick,
      //   this.$route.query.decidePriceType
      // );

      // if (Number(this.$route.query.decidePriceType) > 2) {
      //   this.pageConfig = _pageConfig.splice(0, 1);
      // } else {
      //   this.pageConfig = _pageConfig;
      // }
      // await this.getSpecifiedChildrenLevelOrgs()
      await this.getSupplierList()
      await this.detailPoint()
      await this.getFile()
      await this.getBuyerOrgList()

      const direct = await this.$API.priceService.getDeliveryPlace().catch(() => {})

      if (direct) {
        directDelivery = direct.data.map(({ itemName, itemCode }) => ({
          value: itemCode,
          text: itemName
        }))
      }
      const Currency = await this.$API.masterData.queryAllCurrency().catch(() => {})

      if (Currency) {
        allCurrency = Currency.data.map(({ currencyName, currencyCode }) => ({
          value: currencyCode,
          text: currencyName
        }))
        this.currencyList = Currency.data
      }
      const TaxItem = await this.$API.masterData.queryAllTaxItem().catch(() => {})
      taxList = TaxItem.data

      //获取公司
      const companyItems = await this.$API.masterData
        .findSpecifiedChildrenLevelOrgs({
          organizationLevelCodes: ['ORG02'],
          orgType: 'ORG001PRO',
          includeItself: false,
          organizationIds: []
        })
        .catch(() => {})
      companyList = companyItems.data

      this.$set(
        this.pageConfig[0].grid,
        'columnData',
        columnDataOne(
          {
            purGroupList: this.purGroupList,
            supplierList: this.supplierList,
            siteNameDataSource: this.siteNameDataSource,
            directDelivery,
            allCurrency,
            taxList,
            companyList,
            type: this.decidePriceType,
            queryBuyerOrg: this.$API.masterData.permissionOrgList,
            categoryPagedQuery: this.$API.masterData.categoryPagedQuery,
            querySite: this.$API.masterData.permissionSiteList,
            getLastPrice: this.$API.rfxList.getLastPrice,
            getDefaultValue: this.$API.rfxList.getDefaultValue,
            getPlanTime: this.$API.rfxList.getPlanTime,
            getPurGroup: this.$API.masterData.getPurGroup,
            getPropertyPrice: this.$API.rfxList.getPropertyPrice,
            getSupplierList: (e) => {
              return this.$API.masterData.getSupplierList(e)
            },
            supplierCode: this.forObject.supplierCode
          },
          {
            $on: (event, fn) => {
              this.busEvent.add(event)
              this.$bus.$on(event, fn)
            },
            $emit: (event, fn) => {
              this.busEvent.add(event)
              this.$bus.$emit(event, fn)
            }
          }
        )
      )
      this.getReadOnly()
    },
    contractTypeChange() {
      this.save()
    },
    priceClassificationChange() {
      this.save()
    },
    formatDate(e) {
      let _tempDate = utils.formatTime(new Date(parseInt(e)), 'YYYY-mm-dd')
      return _tempDate
    },
    // getAllCurrency() {
    //   this.$API.masterData.queryAllCurrency().then((res) => {
    //     if (res.code == 200) {
    //       this.currencyList = res.data
    //     }
    //   })
    // },
    //获取公司
    getSpecifiedChildrenLevelOrgs() {
      this.$API.masterData
        .findSpecifiedChildrenLevelOrgs({
          organizationLevelCodes: ['ORG02'],
          orgType: 'ORG001PRO',
          includeItself: false,
          organizationIds: []
        })
        .then((res) => {
          this.companyList = res.data
        })
    },
    companyChange(e) {
      this.forObject.companyName = e.itemData.orgName
      this.forObject.companyId = e.itemData.id
      this.forObject.companyCode = e.itemData.orgCode
      this.forObject.purOrgName = null
      this.forObject.purOrgCode = null
      this.forObject.purOrgId = null
      this.forObject.siteCode = null
      this.forObject.siteId = null
      this.forObject.siteName = null
      this.forObject.taxRateCode = null
      this.forObject.taxRateValue = null
      this.forObject.taxRateName = null
      this.forObject.currencyCode = null
      this.forObject.currencyName = null

      // this.getMainData(e.itemData.id)
      this.getSupplierList({ organizationCode: e.itemData.orgCode })
    },
    //采购组织
    getMainData(companyId) {
      this.$API.masterData
        .permissionOrgList({
          orgId: companyId
        })
        .then((res) => {
          this.purOrgList = res.data
        })
    },
    purOrgChange(e) {
      console.log('purOrgChange--', e)
      if (e.value) {
        let param = {
          purOrgId: e.itemData.id,
          companyId: this.forObject.companyId
        }
        this.forObject.purOrgName = e.itemData.organizationName
        this.forObject.purOrgId = e.itemData.id
        this.forObject.purOrgCode = e.itemData.organizationCode
        this.getFactoryList(param)
      }
      this.forObject.siteCode = null
      this.forObject.siteId = null
      this.forObject.siteName = null
    },
    getFactoryList(params) {
      this.$API.masterData
        .permissionSiteList({
          buOrgId: params.purOrgId,
          companyId: params.companyId,
          orgLevelTypeCode: 'ORG06'
        })
        .then((res) => {
          this.siteList = res.data
        })
    },
    getSupplierList(param) {
      this.$API.masterData.getSupplierList(param).then((res) => {
        if (res.code == 200) {
          this.supplierList = res.data
        }
      })
    },
    async supplierCodeChange(e) {
      let _flag = await this.checkCurrencyAndTax(e.supplierCode)
      this.forObject.supplierCode = e.supplierCode
      this.forObject.supplierName = e.supplierName
      this.forObject.supplierId = e.id
      if (!_flag) {
        this.$set(this.forObject, 'taxRateName', null)
        this.$set(this.forObject, 'currencyName', null)
        this.forObject.taxRateCode = null
        this.forObject.taxRateValue = null
        this.forObject.currencyCode = null
      }
    },
    async checkCurrencyAndTax(e) {
      let _flag
      await this.$API.rfxList.getRfxCountdown({ supplierCode: e }).then((res) => {
        if (res.code == 200 && res.data && res.data.bidCurrencyCode) {
          // if (res.code == 200 && res.data && res.data.bidCurrencyCode && res.data.bidTaxRateCode) {
          let _record = this.currencyList.find((e) => e.currencyCode == res.data.bidCurrencyCode)
          if (_record) {
            this.$set(this.forObject, 'currencyName', _record.currencyName)
            this.forObject.currencyName = _record.currencyName
          }
          this.$set(this.forObject, 'taxRateName', res.data.bidTaxRateName)

          this.forObject.currencyCode = res.data.bidCurrencyCode
          this.forObject.taxRateCode = res.data.bidTaxRateCode
          this.forObject.taxRateValue = res.data.bidTaxRateValue
          _flag = true
        } else {
          this.forObject.currencyName = null
          this.forObject.taxRateName = null
          this.forObject.currencyCode = null
          this.forObject.taxRateCode = null
          this.forObject.taxRateValue = null
          this.$toast({
            content: this.$t('当前供应商没有对应的币种，请重新选择'),
            type: 'warning'
          })
          _flag = false
        }
      })
      return _flag
    },
    // changeSupplier(e) {
    //   if (e.value) {
    //     this.forObject.supplierName = e.itemData.supplierName
    //     this.forObject.supplierId = e.itemData.id
    //     this.$API.rfxList.getRfxCountdown({ supplierCode: e.value }).then((res) => {
    //       if (res.code == 200 && res.data && res.data.bidCurrencyCode && res.data.bidTaxRateCode) {
    //         let _record = this.currencyList.find((e) => e.currencyCode == res.data.bidCurrencyCode)
    //         if (_record) {
    //           this.$set(this.forObject, 'currencyName', _record.currencyName)
    //           this.forObject.currencyName = _record.currencyName
    //         }
    //         this.$set(this.forObject, 'taxRateName', res.data.bidTaxRateName)

    //         this.forObject.currencyCode = res.data.bidCurrencyCode
    //         this.forObject.taxRateCode = res.data.bidTaxRateCode
    //         this.forObject.taxRateValue = res.data.bidTaxRateValue
    //       } else {
    //         this.forObject.currencyName = null
    //         this.forObject.taxRateName = null
    //         this.forObject.currencyCode = null
    //         this.forObject.taxRateCode = null
    //         this.forObject.taxRateValue = null
    //         this.$toast({
    //           content: this.$t('当前供应商没有对应的币种税率，请重新选择'),
    //           type: 'warning'
    //         })
    //       }
    //     })
    //     let objContract = {
    //       _purOrgCode: this.forObject?.purOrgCode,
    //       _siteCode: this.forObject?.siteCode,
    //       _companyCode: this.forObject.companyCode,
    //       _supplierCode: this.resInfo.supplierCode
    //     }
    //     sessionStorage.setItem('fixedPointObjForContract', JSON.stringify(objContract))
    //   } else {
    //     this.forObject.supplierName = null
    //     this.forObject.supplierId = null
    //   }
    // },
    siteChange(e) {
      if (e.value) {
        this.forObject.siteId = e.itemData.id
        this.forObject.siteCode = e.itemData.orgCode
        this.forObject.siteName = e.itemData.orgName
        sessionStorage.setItem('organizationId', e.itemData.id)

        let objContract = {
          _purOrgCode: this.forObject.purOrgCode,
          _siteCode: this.forObject.siteCode,
          _companyCode: this.forObject.companyCode,
          _supplierCode: this.resInfo?.supplierCode
        }
        sessionStorage.setItem('fixedPointObjForContract', JSON.stringify(objContract))
      }
    },
    handleChange() {},
    // 获取主数据-采购组
    getBuyerOrgList() {
      this.$API.masterData
        .getbussinessGroup({
          groupTypeCode: 'BG001CG'
        })
        .then((res) => {
          this.purGroupList = res.data.map(({ groupCode, groupName, id }) => ({
            value: groupCode,
            text: groupName,
            id
          }))
        })
    },
    actionBegin(args) {
      console.log('actionBegin', args, this.$refs.templateRef.getCurrentUsefulRef())
      if (args.requestType === 'add') {
        args.data.quoteEffectiveStartDate = this.forObject?.contractStartTime
          ? Number(this.forObject.contractStartTime)
          : null
        args.data.quoteEffectiveEndDate = this.forObject?.contractEndTime
          ? Number(this.forObject.contractEndTime)
          : null
        args.data.priceUnitName = '1'
        this.allowSubmit = false
      } else if (args.requestType === 'beginEdit') {
        sessionStorage.setItem('organizationId', args.rowData?.siteId)
        sessionStorage.setItem('pricingSiteCode', args.rowData?.siteCode)
        sessionStorage.setItem('priceCompanyId', args.rowData.companyId)
        this.allowSubmit = false
        if (args.rowData?.quoteEffectiveStartDate == '0') {
          args.rowData.quoteEffectiveStartDate = null
        }
        if (args.rowData?.quoteEffectiveEndDate == '0') {
          args.rowData.quoteEffectiveEndDate = null
        }
      } else if (args.requestType === 'sorting') {
        if (this.allowSubmit == false) {
          args.cancel = true
        }
      }
    },
    compressorChange(e) {
      this.forObject.compressor = e.checked
    },
    sourceToSapFlagChange(e) {
      this.forObject.sourceToSapFlag = e.checked
    },
    setFile() {
      console.log('setFile--', this.fileList)
      let _records = []
      this.fileList.forEach((item) =>
        _records.push({
          ...item,
          docId: this.$route.query.id,
          sysFileId: item?.sysFileId || item.id
        })
      )
      this.$API.rfxList.saveFiles(_records).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
        }
      })
    },
    getFile() {
      this.$API.rfxList.getFile({ docId: this.$route.query.id }).then((res) => {
        if (res.code == 200) {
          console.log('getFile', res)
          this.fileList = res.data
        }
      })
    },
    recordDoubleClick(row) {
      const info = this.siteNameDataSource.find((e) => e.id === row.rowData.siteId)
      if (info) {
        sessionStorage.setItem('organizationId', info.id)
        sessionStorage.setItem('pricingSiteCode', info.orgCode)
      }
    },
    async actionComplete(e) {
      console.log('actionComplete--', e, this.decidePriceType)
      const { requestType, rowIndex } = e
      if (e.requestType === 'add' && this.decidePriceType > 2) {
        e.data.referChannel = 0
        e.data.stepQuote = 0
        sessionStorage.removeItem('currentPriceItemCode')
        sessionStorage.removeItem('organizationId')
        sessionStorage.removeItem('pricingSiteCode')
      }
      if (e.requestType === 'beginEdit') {
        if (this.decidePriceType < 2) {
          if (e.rowData.pointBiddingItemStage) {
            e.row.editInstance.setOptions('untaxedUnitPrice', {
              disabled: true,
              readonly: true
            })
          }
        }
        sessionStorage.setItem('currentPriceItemCode', e.rowData.itemCode)
      }
      if (e.requestType === 'save') {
        let _result = await this.isValidSaveData(e.data)
        if (!_result) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          sessionStorage.removeItem('currentPriceItemCode')
          sessionStorage.removeItem('organizationId')
          sessionStorage.removeItem('pricingSiteCode')
          this.$nextTick(() => {
            this.handleSaveFn(e.data)
            this.allowSubmit = true
          })
        }

        // sessionStorage.removeItem("organizationId");
      }
      if (e.requestType === 'refresh' || requestType === 'cancel') {
        this.allowSubmit = true
      }
    },
    // 结束编辑状态
    endEdit() {
      let _current = this.$refs.templateRef.getCurrentTabRef()
      _current?.grid.endEdit()
      _current?.grid.hideSpinner()
    },
    // 校验数据
    async isValidSaveData(data) {
      const {
        itemCode,
        categoryCode,
        purGroupCode,
        taxedUnitPrice,
        untaxedUnitPrice,
        quoteEffectiveStartDate,
        quoteEffectiveEndDate,
        propertyPrice,
        orderNo
      } = data
      let valid = true
      if (!itemCode) {
        this.$toast({ content: this.$t('请选择物料'), type: 'warning' })
        valid = false
      } else if (!categoryCode) {
        this.$toast({
          content: this.$t('请选择品类'),
          type: 'warning'
        })
        valid = false
      } else if (!purGroupCode) {
        this.$toast({ content: this.$t('请选择采购组'), type: 'warning' })
        valid = false
      } else if (!taxedUnitPrice) {
        this.$toast({ content: this.$t('请输入单价(含税)'), type: 'warning' })
        valid = false
      } else if (!untaxedUnitPrice) {
        this.$toast({
          content: this.$t('请输入单价(不含税)'),
          type: 'warning'
        })
        valid = false
      } else if (!quoteEffectiveStartDate) {
        this.$toast({ content: this.$t('请选择有效期'), type: 'warning' })
        valid = false
      } else if (!quoteEffectiveEndDate) {
        this.$toast({ content: this.$t('请选择有效期'), type: 'warning' })
        valid = false
      } else if (!propertyPrice) {
        this.$toast({ content: this.$t('请选择价格属性'), type: 'warning' })
        valid = false
      } else if (
        this.forObject.priceClassification == 4 &&
        this.forObject.contractType == 'rolled' &&
        !orderNo
      ) {
        this.$toast({ content: this.$t('正式价且合同类型为钢材时需填写订单号'), type: 'warning' })
        valid = false
      } else if (data?.companyCode && data?.purOrgCode && valid) {
        let _param = {
          companyCode: data.companyCode,
          purOrgCodeList: [data.purOrgCode],
          supplierCode: this.resInfo.supplierCode
        }
        await this.$API.rfxList
          .checkSupAndPur(_param)
          .then((res) => {
            if (res.code == 503) {
              valid = false
            }
          })
          .catch((err) => {
            if (err.code == 503) {
              valid = false
            }
          })
      }

      return valid
    },
    getReadOnly() {
      if (
        this.$route.query.status != 0 &&
        this.$route.query.status != 3 &&
        this.$route.query.status != 7 &&
        this.$route.query.status != 8
      ) {
        this.pageConfig[0].grid.editSettings.allowEditing = false
        // this.pageConfig[1].grid.editSettings.allowEditing = false;
      }
    },
    comeBack() {
      // this.$router.push({ name: 'contract-price' })
      this.$router.go(-1)
    },
    asyncFormValidate(el) {
      return new Promise((c) => {
        this.$refs[el].validate((valid) => c(valid))
      })
    },
    async save(type = 'save') {
      this.$store.commit('startLoading')
      const validate = await this.asyncFormValidate('ruleForm')
      if (!validate) {
        this.$store.commit('endLoading')
        return
      }
      if (!this.forObject.currencyCode) {
        this.$toast({
          content: this.$t('当前供应商没有对应的币种，请重新选择'),
          type: 'warning'
        })
        this.$store.commit('endLoading')
        return
      }
      let params = { ...this.resInfo, ...this.forObject }
      // params.title = this.forObject.pricingName;
      // params.remark = this.forObject.remark;
      params.compressor = this.forObject.compressor ? 1 : 0
      params.sourceToSapFlag = this.forObject.sourceToSapFlag ? 1 : 0
      // params.priceClassification = this.forObject.priceClassification;
      params.contractStartTime = Number(new Date(this.forObject.contractStartTime))
      params.contractEndTime = Number(new Date(this.forObject.contractEndTime))
      params.signedTime = Number(new Date(this.forObject.signedTime))
      if (this.decidePriceType == 3) {
        params.supplierName = this.forObject.supplierName
        params.supplierCode = this.forObject.supplierCode
        params.supplierId = this.forObject.supplierId
      }
      this.$API.rfxList
        .savePoint(params)
        .then((res) => {
          if (res.code == 200) {
            if (type == 'save') {
              this.$toast({
                content: res.msg,
                type: 'success'
              })
              this.$store.commit('endLoading')
              this.reload()
            } else if (type == 'submit') {
              if (
                this.resInfo.status < 1 ||
                this.resInfo.status == 3 ||
                this.resInfo.status == 7 ||
                this.resInfo.status == 8
              ) {
                let params = {
                  id: this.resInfo.id
                }
                this.$API.rfxList
                  .preCheckUntaxedHistoricalPricesSubmit(params)
                  .then((res) => {
                    if (res.code == 200 && res.msg.includes('请确认') && res.msg.includes('物料')) {
                      this.$dialog({
                        data: {
                          title: this.$t('提示'),
                          message: res.msg
                        },
                        success: () => {
                          this.submitConfirm(params)
                        },
                        close: () => {
                          this.$store.commit('endLoading')
                        }
                      })
                    } else {
                      this.submitConfirm(params)
                    }
                  })
                  .catch((err) => {
                    this.$toast({
                      type: 'error',
                      content: err.msg
                    })
                    this.$store.commit('endLoading')
                  })
              } else {
                this.$store.commit('endLoading')
                this.$toast({
                  content: this.$t('只能提交状态为草稿、审批驳回、审批废弃、审批撤回的单据'),
                  type: 'warning'
                })
              }
            }
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    submitConfirm(params) {
      this.$API.rfxList
        .preCheckSubmit(params)
        .then((res) => {
          if (res.code === 200) {
            this.submitPricing(params)
          } else {
            this.$store.commit('endLoading')
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    submitPricing(params) {
      // 提交审批时校验：弹窗提示：“物料：A、B、C，当前未税价格与历史价格偏差绝对值大于100%，请确认”
      const queryData = {
        condition: 'and',
        rules: [],
        defaultRules: [
          {
            label: this.$t('定点推荐id'),
            field: 'point.id',
            type: 'string',
            operator: 'equal',
            value: this.$route.query.id
          }
        ],
        page: { current: 1, size: 20 }
      }
      this.$API.rfxList
        .checkBasicAndAverage(queryData)
        .then((res) => {
          const { data } = res
          if (data && data.length) {
            let msg = this.$t('物料：')
            for (let i = 0; i < data.length; i++) {
              if (i === 0) {
                msg += data[i]
              } else {
                msg += `、${data[i]}`
              }
            }
            msg += '，当前未税价格与历史价格偏差绝对值大于100%，请确认'
            // 校验不通过弹窗提示
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: msg
              },
              success: () => {
                // 调提交接口
                this.$API.rfxList
                  .submitPricing(params)
                  .then(() => {
                    this.$store.commit('endLoading')
                    this.$toast({ content: this.$t('提交成功'), type: 'success' })
                    this.comeBack()
                  })
                  .catch(() => {
                    this.$store.commit('endLoading')
                  })
              }
            })
            this.$store.commit('endLoading')
          } else {
            // 校验通过 调提交接口
            this.$API.rfxList
              .submitPricing(params)
              .then(() => {
                this.$store.commit('endLoading')
                this.$toast({ content: this.$t('提交成功'), type: 'success' })
                this.comeBack()
              })
              .catch(() => {
                this.$store.commit('endLoading')
              })
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    submit() {
      this.save('submit')
    },

    //保存
    handleSaveFn(arg) {
      // let _selectGridRecords = e.grid.getCurrentViewRecords();
      let _selectGridRecords = this.$refs.templateRef
        .getCurrentUsefulRef()
        .ejsRef.getCurrentViewRecords()
        .filter((e) => e.id == arg.id)
      console.log(
        'handleSaveFn---',
        this.$refs.templateRef.getCurrentUsefulRef().ejsRef.getCurrentViewRecords()
      )

      let flag = false
      _selectGridRecords.forEach((e) => {
        e.pointId = this.$route.query.id
      })
      for (const value of _selectGridRecords) {
        if (value.quoteEffectiveStartDate > value.quoteEffectiveEndDate) {
          flag = true
        } else {
          value.referChannel = 0
          value.stepQuote = 0
        }
      }
      if (flag) {
        this.$toast({
          content: this.$t('开始日期不能大于失效日期'),
          type: 'warning'
        })
      } else {
        this.$API.rfxList.savePointItemList(_selectGridRecords).then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    },
    // 保存-定点扩展工厂明细
    handleSavePointExpand() {
      let _selectGridRecords = this.$refs.templateRef
        .getCurrentUsefulRef()
        .ejsRef.getCurrentViewRecords()

      _selectGridRecords.forEach((e) => {
        e.pointId = this.$route.query.id
      })

      this.$API.rfxList.savePointExpand(_selectGridRecords).then((res) => {
        if (res.code == 200) {
          this.$toast({ content: this.$t('保存成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //保存
    handleSaveTwoFn(e) {
      let _selectGridRecords = e.grid.getCurrentViewRecords()
      let str = []
      _selectGridRecords.forEach((item, index) => {
        if (item.allocationRatio < 0 || item.allocationRatio > 100) {
          str.push(this.$t(`第${index + 1}行`))
        }
      })
      if (str.length != 0) {
        this.$toast({
          content: str.join(',') + '的配额字段，请输入（0-100）的数字',
          type: 'warning'
        })
        return
      }
      let params = _selectGridRecords.map((x) => {
        return {
          allocationQuantity: x.allocationQuantity,
          allocationRatio: (x.allocationRatio / 100).toFixed(2),
          id: x.id
        }
      })
      this.$API.rfxList.savePointItemAllocationList(params).then((res) => {
        if (res.code == 200) {
          this.$toast({ content: this.$t('保存成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    //删除
    handleDelFn(_selectGridRecords, idList) {
      if (_selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else {
        let params = {
          idList: idList
        }
        this.$API.rfxList.deletePointItemList(params).then(() => {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        })
      }
    },
    // 删除-定点扩展工厂明细
    handleDelPointExpand(_selectGridRecords, idList) {
      if (_selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else {
        let params = {
          idList: idList
        }
        this.$API.rfxList.deletePointExpandList(params).then(() => {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        })
      }
    },

    //新增
    handleAddFn(e) {
      e.grid.addRecord()
    },
    handleClickToolBar(e) {
      if (e.toolbar.id == 'Cancel') {
        e.grid.closeEdit()
        sessionStorage.removeItem('currentPriceItemCode')
        this.allowSubmit = true
        setTimeout(() => {
          this.$refs.templateRef.refreshCurrentGridData()
        }, 1)
        return
      }
      this.tabIndex = e.tabIndex
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      let idList = []
      _selectGridRecords.map((item) => {
        idList.push(item.id)
      })
      if (e.toolbar.id == 'Save') {
        this.endEdit()
      } else if (e.toolbar.id == 'del') {
        if (e.tabIndex == 0) {
          this.handleDelFn(_selectGridRecords, idList) //删除
        } else {
          this.handleDelPointExpand(_selectGridRecords, idList) //删除
        }
      } else if (e.toolbar.id == 'SaveTwo') {
        this.endEdit()
        setTimeout(() => {
          this.handleSaveTwoFn(e) //保存
        }, 100)
      } else if (e.toolbar.id == 'quickAdd') {
        this.$dialog({
          modal: () => import('./editComponents/selectOrderDialog.vue'),
          data: {
            title: this.$t('快速添加'),
            supplierCode: this.forObject.supplierCode,
            pointId: this.$route.query.id
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      } else if (e.toolbar.id == 'Add') {
        this.handleAddFn(e) //新增
      } else if (e.toolbar.id == 'export') {
        this.handleExport()
      }
    },
    //导出
    handleExport() {
      this.$store.commit('startLoading')
      const params = {
        page: { current: 1, size: 500000 },
        defaultRules: [
          { field: 'decidePriceType', operator: 'equal', value: 7 },
          {
            field: 'id',
            operator: 'equal',
            value: this.$route.query.id
          }
        ]
      } // 筛选条件
      this.$API.rfxList
        .excelExport(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
          this.$store.commit('endLoading')
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    handleClickCellTitle(e) {
      console.log(333, e.data)
      if (e.field == 'biddingItemStageStrName') {
        if (e.data.id) {
          this.$dialog({
            modal: () => import('./ladderQuotes/index.vue'),
            data: {
              title: this.$t('阶梯报价'),
              id: e.data.id,
              pricingType: this.forObject.pricingType,
              status: this.$route.query.status,
              stepQuote: e.data.stepQuote
            },
            success: () => {
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        } else {
          this.$toast({
            content: this.$t('请先保存新增的数据'),
            type: 'warning'
          })
        }
      }
    },
    async detailPoint() {
      let param = {
        id: this.$route.query.id
      }
      const res = await this.$API.rfxList.detailPoint(param).catch(() => {})
      if (res.code == 200) {
        this.forObject = { ...res.data }
        this.forObject.personnel = res.data.purName
        this.forObject.pricingCode = res.data.pointNo
        this.forObject.pricingName = res.data.title
        this.forObject.pricingType = res.data.decidePriceType
        this.forObject.state = res.data.status
        this.forObject.compressor = res?.data?.compressor == '1' ? true : false
        this.forObject.sourceToSapFlag = res?.data?.sourceToSapFlag == '1' ? true : false
        this.forObject.contractStartTime =
          res.data.contractStartTime != 0 ? new Date(parseInt(res.data.contractStartTime)) : null
        this.forObject.contractEndTime =
          res.data.contractEndTime != 0 ? new Date(parseInt(res.data.contractEndTime)) : null
        this.forObject.signedTime =
          res.data.signedTime != 0 ? new Date(parseInt(res.data.signedTime)) : null
        this.$refs.supDialog.value = res.data?.supplierCode + '-' + res.data?.supplierName
        this.resInfo = res.data
        if ([0, 3, 7, 8].includes(res.data.status)) {
          this.isDisabled = false
        } else {
          this.isDisabled = true
        }
        // this.getMainData(this.forObject.companyId)
        // this.getSupplierList({ organizationCode: this.forObject.companyCode })

        // let param = {
        //   purOrgId: res.data.purOrgId,
        //   companyId: this.forObject.companyId
        // }
        // this.getFactoryList(param)
        // sessionStorage.setItem('organizationId', this.resInfo.siteId)
        sessionStorage.setItem('priceTaxRateValue', this.resInfo.taxRateValue)
        let obj = {
          _supplierCode: this.resInfo?.supplierCode,
          _decidePriceType: this.resInfo.decidePriceType
        }
        sessionStorage.setItem('fixedPointObj', JSON.stringify(obj))
        let objContract = {
          _purOrgCode: this.resInfo?.purOrgCode,
          _siteCode: this.resInfo?.siteCode,
          _companyCode: this.resInfo?.companyCode,
          _supplierCode: this.resInfo?.supplierCode
        }
        sessionStorage.setItem('fixedPointObjForContract', JSON.stringify(objContract))

        this.pageConfig = pageConfig(
          this.$API.rfxList.pagePointDetailItem,
          this.$route.query.id,
          this.serializeList,
          this.recordDoubleClick,
          this.decidePriceType
        )
        this.$set(
          this.pageConfig[0],
          'toolbar',
          contractToolbar(
            ![1, 2, 4, 5, 6].includes(Number(this.forObject.state)),
            this.forObject.priceClassification == 4 && this.forObject.contractType == 'rolled'
          ) // 1,2 禁止
        )
      }
    },
    serializeList(list) {
      list.forEach((x) => {
        x.biddingItemStageStrName =
          x.pointBiddingItemStage && x.pointBiddingItemStage.length > 0
            ? JSON.stringify(x.pointBiddingItemStage)
            : null //单独处理阶梯报价
        x.allocationRatio = (x.allocationRatio * 100).toFixed(2)
      })
      return list
    }
  },
  deactivated() {
    sessionStorage.removeItem('fixedPointObj')
    sessionStorage.removeItem('organizationId')
    sessionStorage.removeItem('currentPriceItemCode')
    sessionStorage.removeItem('pricingSiteCode')
    sessionStorage.removeItem('priceCompanyId')
    bus.$off('purOrgNameChange')
    bus.$off('siteNameChange')
  },
  beforeDestroy() {
    sessionStorage.removeItem('fixedPointObj')
    sessionStorage.removeItem('organizationId')
    sessionStorage.removeItem('currentPriceItemCode')
    sessionStorage.removeItem('pricingSiteCode')
    sessionStorage.removeItem('priceCompanyId')
    bus.$off('purOrgNameChange')
    bus.$off('siteNameChange')
  }
}
</script>

<style scoped lang="scss">
@import '../../../../../node_modules/@mtech/mtech-common-uploader/build/esm/bundle.css';

.expand-and-collapse-box {
  padding: 10px 0 0 10px;

  .sort-box {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #6386c1;
    .icon-container {
      height: 100%;
      width: 20px;
      p {
        width: 100%;
        height: 7px;
        position: relative;
        i {
          position: absolute;
          top: -3px;
          transform: scale(0.4);
          font-size: 14px;
        }
      }
    }
  }
}

.operate-content {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
}

.mt-form {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;

  .grid_flex {
    width: 679px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.operate-bar {
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #4f5b6d;
  font-size: 14px;
  // padding: 0 20px;
}

.operate-bars {
  width: 20px;
  height: 20px;
  background: rgba(0, 70, 156, 0.1);
  border: 1px solid rgba(0, 70, 156, 0.9);
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;

  i {
    width: 6px;
    height: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: rotate(90deg);
  }
}

.miam-container {
  background: #fff;
  padding: 0 10px;

  .mian-info {
    // height: 300px;
    background: rgba(255, 255, 255, 1);
    // border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    // padding: 0 20px;
    min-width: 1300px;

    .normal-title {
      width: 100%;
      height: 14px;
      line-height: 14px;
      font-size: 14px;
      color: #292929;
      font-family: PingFangSC;
      font-weight: 500;

      &:before {
        content: ' ';
        display: inline-block;
        vertical-align: middle;
        width: 2px;
        height: 10px;
        background: rgba(0, 70, 156, 1);
        border-radius: 1px;
        margin-right: 10px;
      }
    }

    .input-item {
      margin-top: 20px;
      padding-right: 50px;

      .label-txt {
        height: 40px;
        width: 130px;
        font-size: 14px;
        // line-height: 40px;
        color: #292929;
      }

      .label-value {
        height: 40px;
        width: 130px;
        font-size: 14px;
        // line-height: 40px;
        color: #35404e;
      }

      .select-container {
        height: 40px;
      }

      .e-label {
        color: #35404e;
      }

      .label-text {
        color: #35404e;
      }
    }

    .input-item /deep/ .normal-width {
      width: 240px;
    }

    .input-item /deep/ .e-radio + label .e-label {
      color: #35404e;
    }

    /deep/.mt-form-item {
      width: 24.5%;

      .mt-form-item-label {
        .mt-common-uploader,
        .mt-date-picker,
        .mt-input {
          width: 100%;
        }
      }
    }
  }
}

.relation-ships {
  flex: 1;
  min-height: 400px;
  background: rgba(255, 255, 255, 1);
}

.btns-wrap {
  /deep/ .mt-button {
    margin-right: 0;

    button {
      background: transparent;
      //border: 1px solid rgba(0, 70, 156, 0.1);
      border-radius: 4px;
      box-shadow: unset;
      padding: 6px 12px 4px;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(0, 70, 156, 1);
    }
  }
}

.custom-width {
  width: 330px;
}

.pl-50 {
  padding-left: 50px;
}

.full-width {
  width: 100% !important;
}
</style>
