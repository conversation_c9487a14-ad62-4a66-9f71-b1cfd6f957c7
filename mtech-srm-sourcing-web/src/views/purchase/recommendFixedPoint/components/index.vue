<template>
  <div class="full-height mt-flex-direction-column">
    <div class="miam-container">
      <div class="operate-bar">
        <div class="operate-content">
          <!-- <div class="operate-bars" @click="comeBack">
            <i class="mt-icons mt-icon-icon_input_arrow"></i>
          </div> -->
          <p class="operate-input">
            {{ this.forObject.pricingCode }} {{ $t('创建时间：')
            }}<span>{{ formatDate(this.forObject.createTime) }}</span>
          </p>
          <div class="btns-wrap">
            <mt-button flat @click="comeBack()">{{ $t('返回') }}</mt-button>
            <mt-button v-show="!isDisabled && forObject.priceObjectId !== '-999'" @click="save()">{{
              $t('保存')
            }}</mt-button>
            <mt-button
              flat
              v-show="!isDisabled && allowSubmit && forObject.priceObjectId !== '-999'"
              @click="submit()"
              >{{ $t('提交') }}</mt-button
            >
          </div>
        </div>
      </div>
      <!-- 顶部主要信息 -->
      <div class="mian-info">
        <mt-form ref="ruleForm" :model="forObject">
          <mt-form-item
            ref="pricingCode"
            prop="id"
            :label="$t('定价单号')"
            label-style="left"
            label-width="80px"
          >
            <mt-input
              disabled
              float-label-type="Never"
              v-model="forObject.pricingCode"
              :placeholder="$t('请输入定价单号')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            ref="pricingName"
            prop="id"
            :label="$t('标题')"
            label-style="left"
            label-width="80px"
            v-if="decidePriceType < 2"
          >
            <mt-input
              float-label-type="Never"
              v-model="forObject.pricingName"
              :placeholder="$t('请输入标题')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            ref="priceObjectName"
            prop="id"
            :label="$t('寻源对象')"
            label-style="left"
            label-width="80px"
            v-if="decidePriceType != 3 && decidePriceType != 4 && decidePriceType != 5"
          >
            <mt-input
              disabled
              float-label-type="Never"
              v-model="forObject.priceObjectName"
              :placeholder="$t('请输入寻源对象')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            v-if="decidePriceType == 5"
            prop="id"
            :label="$t('供应商编码')"
            label-style="left"
            label-width="80px"
          >
            <mt-input disabled float-label-type="Never" v-model="forObject.supplierCode"></mt-input>
          </mt-form-item>
          <mt-form-item
            v-if="decidePriceType > 2"
            prop="id"
            :label="$t('供应商')"
            label-style="left"
            label-width="80px"
          >
            <supDialog
              style="width: 100%"
              ref="supDialog"
              v-bind="$attrs"
              v-on="$listeners"
              v-if="decidePriceType < 5"
              v-model="forObject.supplierCode"
              :async-config-url="supplierUrl"
              :is-edit="!isDisabled"
              @change="supplierCodeChange"
            ></supDialog>
            <!-- <mt-select
              v-if="decidePriceType == 3"
              :disabled="forObject.state == 1"
              :data-source="supplierList"
              :fields="{ text: 'supplierName', value: 'supplierCode' }"
              v-model="forObject.supplierCode"
              :show-clear-button="true"
              :placeholder="$t('请选择')"
              @change="changeSupplier"
            ></mt-select> -->
            <mt-input
              v-if="decidePriceType == 5"
              disabled
              float-label-type="Never"
              v-model="forObject.supplierName"
              :placeholder="$t('请输入供应商')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            ref="firm"
            prop="id"
            :label="$t('公司')"
            label-style="left"
            label-width="80px"
            v-if="decidePriceType < 2"
          >
            <mt-input
              disabled
              float-label-type="Never"
              v-model="forObject.firm"
              :placeholder="$t('请输入公司')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            ref="Purchasing"
            prop="id"
            :label="$t('采购组织')"
            label-style="left"
            label-width="80px"
            v-if="decidePriceType < 2"
          >
            <mt-input
              disabled
              float-label-type="Never"
              v-model="forObject.Purchasing"
              :placeholder="$t('请输入采购组织')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            prop="taxRateName"
            :label="$t('税率')"
            label-style="left"
            label-width="80px"
            v-if="decidePriceType == 3 || decidePriceType == 4"
          >
            <mt-input disabled v-model="forObject.taxRateName" :show-clear-button="true"></mt-input>
          </mt-form-item>
          <mt-form-item
            prop="currencyName"
            :label="$t('币种')"
            label-style="left"
            label-width="80px"
            v-if="decidePriceType == 3 || decidePriceType == 4"
          >
            <mt-input
              disabled
              v-model="forObject.currencyName"
              :show-clear-button="true"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            ref="personnel"
            prop="id"
            :label="$t('采购人员')"
            label-style="left"
            label-width="80px"
            v-if="decidePriceType != 3 && decidePriceType != 4 && decidePriceType != 5"
          >
            <mt-input
              disabled
              v-model="forObject.personnel"
              :show-clear-button="true"
              :placeholder="$t('请选输入采购人员')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            ref="personnel"
            prop="id"
            :label="$t('创建人')"
            label-style="left"
            label-width="80px"
            v-if="decidePriceType == 3 || decidePriceType == 4 || decidePriceType == 5"
          >
            <mt-input
              disabled
              v-model="forObject.createUserName"
              :show-clear-button="true"
              :placeholder="$t('请选择创建人')"
            ></mt-input>
          </mt-form-item>
          <!-- <mt-form-item
            ref="pricingType"
            prop="id"
            :label="$t('定价单类型')"
            label-style="left"
            label-width="80px"
          >
            <mt-select
              disabled
              :data-source="[
                { text: $t('直接定价'), value: 0 },
                { text: $t('寻源结果定价'), value: 1 },
                { text: $t('基价定价'), value: 3 },
                { text: $t('均价定价'), value: 4 },
                { text: $t('执行价定价'), value: 5 },
              ]"
              v-model="forObject.pricingType"
              :show-clear-button="true"
              :placeholder="$t('请输入定价单类型')"
            ></mt-select>
          </mt-form-item> -->
          <mt-form-item
            prop="contractType"
            :label="$t('合同类型')"
            label-style="left"
            label-width="80px"
            v-if="forObject.priceClassification == 4"
          >
            <mt-select
              v-model="forObject.contractType"
              :disabled="forObject.priceObjectId === '-999'"
              :data-source="contractTypeList"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            ref="state"
            prop="id"
            :label="$t('状态')"
            label-style="left"
            label-width="80px"
          >
            <mt-select
              disabled
              :data-source="[
                { text: $t('已作废'), value: -1 },
                { text: $t('草稿'), value: 0 },
                { text: $t('审批中'), value: 1 },
                { text: $t('审批通过'), value: 2 },
                { text: $t('审批驳回'), value: 3 },
                { text: $t('审批废弃'), value: 7 },
                { text: $t('审批撤回'), value: 8 }
              ]"
              v-model="forObject.state"
              :show-clear-button="true"
              :placeholder="$t('请输入状态')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            ref="state"
            prop="id"
            :label="$t('附件')"
            label-style="left"
            label-width="80px"
          >
            <mt-common-uploader
              :is-view="isDisabled || forObject.priceObjectId === '-999'"
              :disabled="forObject.priceObjectId === '-999'"
              ref="uploadFile"
              :is-single-file="false"
              :save-url="saveUrl"
              :download-url="downloadUrl"
              type="line"
              v-model="fileList"
              @confirm="setFile"
            ></mt-common-uploader>
          </mt-form-item>
          <mt-form-item label-style="left" label-width="80px" v-if="decidePriceType == 4">
          </mt-form-item>
          <mt-form-item
            prop="compressor"
            label-style="left"
            class="custom-width"
            label=" "
            label-width="50px"
            v-if="(decidePriceType == 0 || decidePriceType == 5) && ktFlag"
          >
            <mt-checkbox
              :disabled="isDisabled || forObject.priceObjectId === '-999'"
              :label="$t('压缩机（暖通压缩机/电机/电控）')"
              class="checkbox-item"
              v-model="forObject.compressor"
              @change="compressorChange"
            ></mt-checkbox>
            <mt-checkbox
              :disabled="isDisabled || forObject.priceObjectId === '-999'"
              :label="$t('是否自动同步货源到SAP')"
              class="checkbox-item"
              v-model="forObject.sourceToSapFlag"
              @change="sourceToSapFlagChange"
            ></mt-checkbox>
          </mt-form-item>
          <mt-form-item
            prop="isProperty"
            label-style="left"
            class="custom-width"
            v-if="decidePriceType == 3"
          >
            <mt-checkbox
              :disabled="isDisabled"
              class="checkbox-item pl-30"
              v-model="forObject.isNewUpFlag"
              @change="isNewUpFlagChange"
              :label="$t('新、涨价')"
            ></mt-checkbox>
            <mt-checkbox
              :disabled="isDisabled"
              :label="$t('属性大类')"
              v-if="isShowProperty"
              class="checkbox-item"
              v-model="forObject.isProperty"
              @change="isPropertyChange"
            ></mt-checkbox>
            <mt-checkbox
              :disabled="isDisabled"
              class="checkbox-item min-80"
              v-model="forObject.isNewProduct"
              @change="isNewProductChange"
              :label="$t('新建基价（已走纸质流程）')"
            ></mt-checkbox>
          </mt-form-item>
          <mt-form-item
            ref="remark"
            prop="id"
            :label="$t('备注')"
            label-style="left"
            label-width="80px"
            class="full-width"
          >
            <mt-input
              :disabled="isDisabled || forObject.priceObjectId === '-999'"
              :width="1075"
              v-model="forObject.remark"
              :show-clear-button="true"
              :placeholder="$t('字数不超过200字')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </div>
    <div class="relation-ships">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickCellTitle="handleClickCellTitle"
        @handleClickToolBar="handleClickToolBar"
        v-if="pageConfig.length > 0 && pageConfig[0].grid.columnData.length > 0"
        @actionBegin="actionBegin"
        @actionComplete="actionComplete"
      />
    </div>
  </div>
</template>

<script>
import bus from '@/utils/bus'
import {
  pageConfig,
  columnDataOne,
  columnDataTwo,
  columnDataThree,
  toolbar,
  strikeToolbar,
  distributeToolbar
} from './config'
import supDialog from './editComponents/dialog.vue'
import utils from '@/utils/utils'
import { download, getHeadersFileName } from '@/utils/utils'

let directDelivery = []
let allCurrency = []
let taxList, companyList

export default {
  name: 'ExpertRating',
  inject: ['reload'],
  components: { supDialog },
  data() {
    return {
      isDisabled: true,
      allowSubmit: true,
      currencyList: [],
      supplierUrl: '/masterDataManagement/tenant/supplier/paged-query-distinct',
      isShowProperty: true,
      tabIndex: 0,
      ktFlag: 1,
      contractTypeList: [
        { text: this.$t('标准'), value: 'standard' },
        { text: this.$t('钢材'), value: 'rolled' },
        { text: this.$t('委外'), value: 'outsource' }
      ],
      fileList: [],
      saveUrl: '/api/file/user/file/uploadPublic?useType=1',
      downloadUrl: '/api/file/user/file/downloadPrivateFile?useType=1',
      pageConfig: [],
      forObject: {
        compressor: false,
        isNewUpFlag: false,
        isNewProduct: false,
        sourceToSapFlag: false,
        firm: '', //公司
        personnel: '', //采购人员
        pricingCode: '', //定价单号
        pricingName: '',
        companyName: '',
        Purchasing: '', //采购组织
        pricingType: '', //定价类型
        state: '', //状态
        remark: '', //备注
        priceClassification: '',
        title: ''
      },
      busEvent: new Set(), // 事件收集
      resInfo: {},
      siteNameDataSource: [],
      supplierList: [],
      purGroupList: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  watch: {
    modalData: {
      handler(newVal) {
        console.log(5050505, newVal)
      }
    }
  },

  computed: {
    propsData() {
      return {
        ...this.modalData
      }
    },
    decidePriceType() {
      return Number(this.$route.query.decidePriceType)
    }
  },
  mounted() {
    this.initData()
  },
  activated() {
    this.initData()
  },
  methods: {
    async initData() {
      // this.getAllCurrency()
      // let _pageConfig = pageConfig(
      //   this.$API.rfxList.pagePointItem,
      //   this.$route.query.id,
      //   this.serializeList,
      //   this.recordDoubleClick,
      //   this.$route.query.decidePriceType
      // );
      // await this.getKtFlag()
      sessionStorage.setItem('_isKTflag', 1)
      // if (Number(this.$route.query.decidePriceType) > 2) {
      //   this.pageConfig = _pageConfig.splice(0, 1);
      // } else {
      //   this.pageConfig = _pageConfig;
      // }
      await this.getSupplierList()
      await this.detailPoint()
      await this.getFile()

      await this.getBuyerOrgList()
      if (this.decidePriceType < 2) {
        const res = await this.$API.masterData
          .permissionSiteList({
            buOrgId: this.resInfo.purOrgId,
            companyId: this.resInfo.companyId,
            orgLevelTypeCode: 'ORG06'
          })
          .catch(() => {})
        this.siteNameDataSource = res?.data || []
        const item = await this.$API.masterData.getSupplierList().catch(() => {})
        this.supplierList = item.data || []
      }

      const direct = await this.$API.priceService.getDeliveryPlace().catch(() => {})

      if (direct) {
        directDelivery = direct.data.map(({ itemName, itemCode }) => ({
          value: itemCode,
          text: itemName
        }))
      }
      const Currency = await this.$API.masterData.queryAllCurrency().catch(() => {})

      if (Currency) {
        allCurrency = Currency.data.map(({ currencyName, currencyCode }) => ({
          value: currencyCode,
          text: currencyName
        }))
        this.currencyList = Currency.data
      }
      const TaxItem = await this.$API.masterData.queryAllTaxItem().catch(() => {})
      taxList = TaxItem.data

      //获取公司
      const companyItems = await this.$API.masterData
        .findSpecifiedChildrenLevelOrgs({
          organizationLevelCodes: ['ORG02'],
          orgType: 'ORG001PRO',
          includeItself: false,
          organizationIds: []
        })
        .catch(() => {})
      companyList = companyItems.data

      this.$set(
        this.pageConfig[0].grid,
        'columnData',
        columnDataOne(
          {
            purGroupList: this.purGroupList,
            supplierList: this.supplierList,
            siteNameDataSource: this.siteNameDataSource,
            directDelivery,
            allCurrency,
            taxList,
            companyList,
            type: this.decidePriceType,
            queryBuyerOrg: this.$API.masterData.permissionOrgList,
            categoryPagedQuery: this.$API.masterData.categoryPagedQuery,
            querySite: this.$API.masterData.permissionSiteList,
            getLastPrice: this.$API.rfxList.getLastPrice,
            isProperty: this.forObject?.isProperty ? true : false,
            getDefaultValue: this.$API.rfxList.getDefaultValue,
            getPlanTime: this.$API.rfxList.getPlanTime,
            getPurGroup: this.$API.masterData.getPurGroup,
            getSupplierList: (e) => {
              return this.$API.masterData.getSupplierList(e)
            }
          },
          {
            $on: (event, fn) => {
              this.busEvent.add(event)
              this.$bus.$on(event, fn)
            },
            $emit: (event, fn) => {
              this.busEvent.add(event)
              this.$bus.$emit(event, fn)
            }
          }
        )
      )
      if (this.decidePriceType < 2) {
        this.$set(
          this.pageConfig[1].grid,
          'columnData',
          columnDataTwo({
            directDelivery,
            allCurrency
          })
        )
      } else if (this.decidePriceType == 4) {
        this.$set(this.pageConfig[1].grid, 'columnData', columnDataThree)
      }
      this.getReadOnly()
    },
    formatDate(e) {
      let _tempDate = utils.formatTime(new Date(parseInt(e)), 'YYYY-mm-dd')
      console.log(e)
      return _tempDate
    },
    async supplierCodeChange(e) {
      let _flag = await this.checkCurrencyAndTax(e.supplierCode)
      this.forObject.supplierCode = e.supplierCode
      this.forObject.supplierName = e.supplierName
      this.forObject.supplierId = e.id
      if (!_flag) {
        this.$set(this.forObject, 'taxRateName', null)
        this.$set(this.forObject, 'currencyName', null)
        this.forObject.taxRateCode = null
        this.forObject.taxRateValue = null
        this.forObject.currencyCode = null
      }
    },
    // getAllCurrency() {
    //   this.$API.masterData.queryAllCurrency().then((res) => {
    //     if (res.code == 200) {
    //       this.currencyList = res.data
    //     }
    //   })
    // },
    changeSupplier(e) {
      if (e.value) {
        this.forObject.supplierName = e.itemData.supplierName
        this.forObject.supplierId = e.itemData.id
      } else {
        this.forObject.supplierName = null
        this.forObject.supplierId = null
      }
    },
    getSupplierList() {
      this.$API.masterData.getSupplierList().then((res) => {
        if (res.code == 200) {
          this.supplierList = res.data
        }
      })
    },
    isPropertyChange(e) {
      this.endEdit()
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('变更属性大类将会导致行数据品类、物料、属性清空，是否继续更改属性大类？')
        },
        success: () => {
          // this.isShowProperty = false
          // setTimeout(() => {

          //   this.isShowProperty = true
          // }, 1)
          this.forObject.isProperty = e.checked
          this.save()
          // this.$toast({ content: this.$t("操作成功"), type: "success" });
          // this.$set(
          //   this.pageConfig[0].grid,
          //   "columnData",
          //   columnDataOne(
          //     {
          //       purGroupList: this.purGroupList,
          //       supplierList: this.supplierList,
          //       siteNameDataSource: this.siteNameDataSource,
          //       directDelivery,
          //       allCurrency,
          //       taxList,
          //       companyList,
          //       type: this.decidePriceType,
          //       queryBuyerOrg: this.$API.masterData.permissionOrgList,
          //       categoryPagedQuery: this.$API.masterData.categoryPagedQuery,
          //       querySite: this.$API.masterData.permissionSiteList,
          //       getLastPrice: this.$API.rfxList.getLastPrice,
          //       isProperty: e.checked,
          //       getDefaultValue: this.$API.rfxList.getDefaultValue,
          //       getSupplierList: (e) => {
          //         return this.$API.masterData.getSupplierList(e);
          //       },
          //     },
          //     {
          //       $on: (event, fn) => {
          //         this.busEvent.add(event);
          //         this.$bus.$on(event, fn);
          //       },
          //       $emit: (event, fn) => {
          //         this.busEvent.add(event);
          //         this.$bus.$emit(event, fn);
          //       },
          //     }
          //   )
          // );
        },
        close: () => {
          this.isShowProperty = false
          setTimeout(() => {
            this.forObject.isProperty = !e.checked
            this.isShowProperty = true
          }, 1)
        }
      })
    },
    handleChange() {},
    // 获取主数据-采购组
    getBuyerOrgList() {
      this.$API.masterData
        .getbussinessGroup({
          groupTypeCode: 'BG001CG'
        })
        .then((res) => {
          this.purGroupList = res.data.map(({ groupCode, groupName, id }) => ({
            value: groupCode,
            text: `${groupCode}-${groupName}`,
            id
          }))
        })
    },
    actionBegin(args) {
      console.log('actionBegin', args)
      if (args.requestType === 'add') {
        this.allowSubmit = false
        if (this.resInfo.isKtFlag == 1) {
          args.data.priceUnitName = '1'
        } else {
          args.data.priceUnitName = '1000'
        }
      } else if (args.requestType === 'beginEdit') {
        this.allowSubmit = false
        if (this.decidePriceType == 3) {
          sessionStorage.setItem('organizationId', args.rowData?.siteId)
          sessionStorage.setItem('pricingSiteCode', args.rowData?.siteCode)
        }
        if (this.decidePriceType == 3 || this.decidePriceType == 4) {
          sessionStorage.setItem('priceCompanyId', args.rowData.companyId)
        }
        if (args.rowData?.quoteEffectiveStartDate == '0') {
          args.rowData.quoteEffectiveStartDate = null
        }
        if (args.rowData?.quoteEffectiveEndDate == '0') {
          args.rowData.quoteEffectiveEndDate = null
        }
      } else if (args.requestType === 'sorting') {
        if (this.allowSubmit == false) {
          args.cancel = true
        }
      }
    },
    compressorChange(e) {
      this.forObject.compressor = e.checked
    },
    isNewUpFlagChange(e) {
      this.forObject.isNewUpFlag = e.checked
    },
    isNewProductChange(e) {
      this.forObject.isNewProduct = e.checked
      this.save()
    },
    sourceToSapFlagChange(e) {
      this.forObject.sourceToSapFlag = e.checked
    },
    // getKtFlag() {
    //   this.$API.rfxList.getKtFlag().then((res) => {
    //     if (res.code == 200) {
    //       this.ktFlag = res.data
    //       sessionStorage.setItem('_isKTflag', res.data)
    //     }
    //   })
    // },
    setFile() {
      if (this.forObject.priceObjectId === '-999') {
        return
      }
      console.log('setFile--', this.fileList)
      let _records = []
      this.fileList.forEach((item) =>
        _records.push({
          ...item,
          docId: this.$route.query.id,
          sysFileId: item?.sysFileId || item.id
        })
      )
      this.$API.rfxList.saveFiles(_records).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
        }
      })
    },
    getFile() {
      this.$API.rfxList.getFile({ docId: this.$route.query.id }).then((res) => {
        if (res.code == 200) {
          console.log('getFile', res)
          this.fileList = res.data
        }
      })
    },
    //采购组织
    getMainData(companyId) {
      this.$API.masterData
        .permissionOrgList({
          orgId: companyId
        })
        .then((res) => {
          this.form.dataSource.procurement = res.data
        })
    },
    recordDoubleClick(row) {
      const info = this.siteNameDataSource.find((e) => e.id === row.rowData.siteId)
      if (info) {
        sessionStorage.setItem('organizationId', info.id)
        sessionStorage.setItem('pricingSiteCode', info.orgCode)
      }
    },
    // 校验数据
    async isValidBaseSaveData(data) {
      const {
        companyName,
        purOrgName,
        siteName,
        taxedBasicPrice,
        quoteEffectiveStartDate,
        quoteEffectiveEndDate,
        propertyPrice
      } = data
      let valid = true
      if (!companyName) {
        this.$toast({ content: this.$t('请选择公司'), type: 'warning' })
        valid = false
      } else if (!purOrgName) {
        this.$toast({
          content: this.$t('请选择采购组织'),
          type: 'warning'
        })
        valid = false
      } else if (!siteName) {
        this.$toast({ content: this.$t('请选择工厂'), type: 'warning' })
        valid = false
      } else if (!taxedBasicPrice) {
        this.$toast({ content: this.$t('请输入含税基价'), type: 'warning' })
        valid = false
      } else if (!quoteEffectiveStartDate) {
        this.$toast({ content: this.$t('请选择有效期'), type: 'warning' })
        valid = false
      } else if (!quoteEffectiveEndDate) {
        this.$toast({ content: this.$t('请选择有效期'), type: 'warning' })
        valid = false
      } else if (!propertyPrice) {
        this.$toast({ content: this.$t('请选择价格属性'), type: 'warning' })
        valid = false
      } else if (data?.companyCode && data?.purOrgCode && valid) {
        let _param = {
          companyCode: data.companyCode,
          purOrgCodeList: [data.purOrgCode],
          supplierCode: this.resInfo.supplierCode
        }
        await this.$API.rfxList
          .checkSupAndPur(_param)
          .then((res) => {
            if (res.code == 503) {
              valid = false
            }
          })
          .catch((err) => {
            if (err.code == 503) {
              valid = false
            }
          })
      }

      return valid
    },
    // 校验数据
    async isValidAverageSaveData(data) {
      const {
        companyName,
        purOrgName,
        siteName,
        itemCode,
        categoryCode,
        taxedAveragePrice,
        quoteEffectiveStartDate,
        quoteEffectiveEndDate
      } = data
      let valid = true
      if (!companyName) {
        this.$toast({ content: this.$t('请选择公司'), type: 'warning' })
        valid = false
      } else if (!purOrgName) {
        this.$toast({
          content: this.$t('请选择采购组织'),
          type: 'warning'
        })
        valid = false
      } else if (!siteName) {
        this.$toast({ content: this.$t('请选择工厂'), type: 'warning' })
        valid = false
      } else if (!itemCode) {
        this.$toast({ content: this.$t('请选择成本因子'), type: 'warning' })
        valid = false
      } else if (!categoryCode) {
        this.$toast({
          content: this.$t('请选择品类'),
          type: 'warning'
        })
        valid = false
      } else if (!taxedAveragePrice) {
        this.$toast({ content: this.$t('请输入含税均价'), type: 'warning' })
        valid = false
      } else if (!quoteEffectiveStartDate) {
        this.$toast({ content: this.$t('请选择有效期'), type: 'warning' })
        valid = false
      } else if (!quoteEffectiveEndDate) {
        this.$toast({ content: this.$t('请选择有效期'), type: 'warning' })
        valid = false
      } else if (data?.companyCode && data?.purOrgCode && valid) {
        let _param = {
          companyCode: data.companyCode,
          purOrgCodeList: [data.purOrgCode],
          supplierCode: this.resInfo.supplierCode
        }
        await this.$API.rfxList
          .checkSupAndPur(_param)
          .then((res) => {
            if (res.code == 503) {
              valid = false
            }
          })
          .catch((err) => {
            if (err.code == 503) {
              valid = false
            }
          })
      }

      return valid
    },
    isValidStrikeSaveData(data) {
      const { quoteEffectiveStartDate, quoteEffectiveEndDate } = data
      let valid = true
      if (!quoteEffectiveStartDate) {
        this.$toast({ content: this.$t('请选择有效期'), type: 'warning' })
        valid = false
      } else if (!quoteEffectiveEndDate) {
        this.$toast({ content: this.$t('请选择有效期'), type: 'warning' })
        valid = false
      }

      return valid
    },
    async actionComplete(e) {
      console.log('actionComplete--', e, this.decidePriceType)
      const { requestType, rowIndex } = e
      if (e.requestType === 'add' && this.decidePriceType > 2) {
        e.data.referChannel = 0
        e.data.stepQuote = 0
      }
      if (e.requestType === 'beginEdit') {
        if (this.decidePriceType < 2) {
          if (e.rowData.pointBiddingItemStage) {
            e.row.editInstance.setOptions('untaxedUnitPrice', {
              disabled: true,
              readonly: true
            })
          }
        }
      }
      if (e.requestType === 'save') {
        if (this.decidePriceType == 4 && this.tabIndex == 1) {
          this.$nextTick(() => {
            this.handleSavePointExpand()
          })
        } else if (this.decidePriceType == 3) {
          let _result = await this.isValidBaseSaveData(e.data)
          if (!_result) {
            // 当出现错误时，指定行进入编辑状态
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          } else {
            sessionStorage.removeItem('organizationId')
            sessionStorage.removeItem('pricingSiteCode')
            this.$nextTick(() => {
              this.handleSaveFn(e.data)
            })
          }
        } else if (this.decidePriceType == 4) {
          let _result = await this.isValidAverageSaveData(e.data)
          if (!_result) {
            // 当出现错误时，指定行进入编辑状态
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          } else {
            this.$nextTick(() => {
              this.handleSaveFn(e.data)
            })
          }
        } else if (this.decidePriceType == 5) {
          if (!this.isValidStrikeSaveData(e.data)) {
            // 当出现错误时，指定行进入编辑状态
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          } else {
            this.$nextTick(() => {
              this.handleSaveFn(e.data)
            })
          }
        } else {
          this.$nextTick(() => {
            this.handleSaveFn(e.data)
          })
        }
        if (
          (e.rowData.biddingItemStageStrName && e.data.biddingItemStageStrName) ||
          e.data.pointBiddingItemStage
        ) {
          e.data.biddingItemStageStrName = e.rowData.biddingItemStageStrName
          e.data.pointBiddingItemStage = e.rowData.pointBiddingItemStage
          this.$bus.$emit('pointBiddingItemStageChange2', {
            index: e.rowIndex || '',
            data: e.rowData.pointBiddingItemStage
          })
        }
        this.allowSubmit = true
        // sessionStorage.removeItem("organizationId");
      }
      if (e.requestType === 'refresh' || requestType === 'cancel') {
        this.allowSubmit = true
      }
    },
    // 结束编辑状态
    endEdit() {
      let _current = this.$refs.templateRef.getCurrentTabRef()
      _current?.grid.endEdit()
      _current?.grid.hideSpinner()
    },
    getReadOnly() {
      if (
        this.decidePriceType == 1 ||
        (this.$route.query.status != 0 &&
          this.$route.query.status != 3 &&
          this.$route.query.status != 7 &&
          this.$route.query.status != 8)
      ) {
        this.pageConfig[0].grid.editSettings.allowEditing = false
        // this.pageConfig[1].grid.editSettings.allowEditing = false;
      }
    },
    comeBack() {
      this.$router.go(-1)
    },
    async save() {
      let params = { ...this.resInfo, ...this.forObject }
      params.title = this.forObject.pricingName
      params.remark = this.forObject.remark
      params.compressor = this.forObject.compressor ? 1 : 0
      params.isProperty = this.forObject.isProperty ? 1 : 0
      params.isNewUpFlag = this.forObject.isNewUpFlag ? 1 : 0
      params.isNewProduct = this.forObject.isNewProduct ? 1 : 0
      params.sourceToSapFlag = this.forObject.sourceToSapFlag ? 1 : 0
      params.priceClassification = this.forObject.priceClassification
      let flag = true
      if (this.decidePriceType == 3 || this.decidePriceType == 4) {
        if (!this.forObject.taxRateCode || !this.forObject.currencyCode) {
          flag = false
        }
        params.supplierName = this.forObject.supplierName
        params.supplierCode = this.forObject.supplierCode
        params.supplierId = this.forObject.supplierId
      }
      if (!flag) {
        this.$toast({
          content: this.$t('当前供应商没有对应的币种税率，请重新选择'),
          type: 'warning'
        })
        return
      }
      await this.$API.rfxList
        .savePoint(params)
        .then((res) => {
          if (res.code == 200) {
            this.$toast({
              content: res.msg,
              type: 'success'
            })
            this.reload()
          }
        })
        .catch(() => {
          this.reload()
        })
    },
    async checkCurrencyAndTax(e) {
      let _flag
      await this.$API.rfxList.getRfxCountdown({ supplierCode: e }).then((res) => {
        if (res.code == 200 && res.data && res.data.bidCurrencyCode && res.data.bidTaxRateCode) {
          let _record = this.currencyList.find((e) => e.currencyCode == res.data.bidCurrencyCode)
          if (_record) {
            this.$set(this.forObject, 'currencyName', _record.currencyName)
            this.forObject.currencyName = _record.currencyName
          }
          this.$set(this.forObject, 'taxRateName', res.data.bidTaxRateName)

          this.forObject.currencyCode = res.data.bidCurrencyCode
          this.forObject.taxRateCode = res.data.bidTaxRateCode
          this.forObject.taxRateValue = res.data.bidTaxRateValue
          _flag = true
        } else {
          this.forObject.currencyName = null
          this.forObject.taxRateName = null
          this.forObject.currencyCode = null
          this.forObject.taxRateCode = null
          this.forObject.taxRateValue = null
          this.$toast({
            content: this.$t('当前供应商没有对应的币种税率，请重新选择'),
            type: 'warning'
          })
          _flag = false
        }
      })
      return _flag
    },
    submit() {
      if (
        this.resInfo.status < 1 ||
        this.resInfo.status == 3 ||
        this.resInfo.status == 7 ||
        this.resInfo.status == 8
      ) {
        let params = {
          id: this.resInfo.id
        }
        this.$API.rfxList.preCheckSubmit(params).then((res) => {
          if (res.code === 200) {
            this.$store.commit('startLoading')
            if (this.decidePriceType < 2) {
              this.$API.rfxList
                .submitPoint(params)
                .then(() => {
                  this.$store.commit('endLoading')
                  this.$toast({ content: this.$t('提交成功'), type: 'success' })
                  this.$router.push({ name: 'fixed-point-recommendations' })
                })
                .catch(() => {
                  this.$store.commit('endLoading')
                })
            } else {
              this.$API.rfxList
                .submitPricing(params)
                .then(() => {
                  this.$store.commit('endLoading')
                  this.$toast({ content: this.$t('提交成功'), type: 'success' })
                  if (this.decidePriceType == 3) {
                    this.$router.push({ name: 'base-price' })
                  } else if (this.decidePriceType == 4) {
                    this.$router.push({ name: 'average-price' })
                  } else if (this.decidePriceType == 5) {
                    this.$router.push({ name: 'strike-price' })
                  }
                })
                .catch(() => {
                  this.$store.commit('endLoading')
                })
            }
          }
        })
      } else {
        this.$toast({
          content: this.$t('只能提交状态为草稿、审批驳回、审批废弃、审批撤回的单据'),
          type: 'warning'
        })
      }
    },

    //保存
    handleSaveFn(arg) {
      // let _selectGridRecords = e.grid.getCurrentViewRecords();
      let _selectGridRecords = this.$refs.templateRef
        .getCurrentUsefulRef()
        .ejsRef.getCurrentViewRecords()
        .filter((e) => e.id == arg.id)
      console.log(
        'handleSaveFn---',
        this.$refs.templateRef.getCurrentUsefulRef().ejsRef.getCurrentViewRecords()
      )
      if (this.decidePriceType < 2) {
        // _selectGridRecords.forEach((x) => {
        //   x.pointBiddingItemStage = JSON.parse(x.biddingItemStageStrName);
        // });
      }

      let flag = false
      _selectGridRecords.forEach((e) => {
        e.pointId = this.$route.query.id
      })
      for (const value of _selectGridRecords) {
        if (value.quoteEffectiveStartDate > value.quoteEffectiveEndDate) {
          flag = true
        } else {
          value.referChannel = 0
          value.stepQuote = 0
        }
      }
      if (flag) {
        this.$toast({
          content: this.$t('开始日期不能大于失效日期'),
          type: 'warning'
        })
      } else {
        this.$API.rfxList.savePointItemList(_selectGridRecords).then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    },
    // 保存-定点扩展工厂明细
    handleSavePointExpand() {
      let _selectGridRecords = this.$refs.templateRef
        .getCurrentUsefulRef()
        .ejsRef.getCurrentViewRecords()

      _selectGridRecords.forEach((e) => {
        e.pointId = this.$route.query.id
      })

      this.$API.rfxList.savePointExpand(_selectGridRecords).then((res) => {
        if (res.code == 200) {
          this.$toast({ content: this.$t('保存成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //保存
    handleSaveTwoFn(e) {
      let _selectGridRecords = e.grid.getCurrentViewRecords()
      let str = []
      _selectGridRecords.forEach((item, index) => {
        if (item.allocationRatio < 0 || item.allocationRatio > 100) {
          str.push(this.$t(`第${index + 1}行`))
        }
      })
      if (str.length != 0) {
        this.$toast({
          content: str.join(',') + '的配额字段，请输入（0-100）的数字',
          type: 'warning'
        })
        return
      }
      let params = _selectGridRecords.map((x) => {
        return {
          allocationQuantity: x.allocationQuantity,
          allocationRatio: (x.allocationRatio / 100).toFixed(2),
          id: x.id
        }
      })
      this.$API.rfxList.savePointItemAllocationList(params).then((res) => {
        if (res.code == 200) {
          this.$toast({ content: this.$t('保存成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    //删除
    handleDelFn(_selectGridRecords, idList) {
      if (_selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else {
        let params = {
          idList: idList
        }
        this.$API.rfxList.deletePointItemList(params).then(() => {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        })
      }
    },
    // 删除-定点扩展工厂明细
    handleDelPointExpand(_selectGridRecords, idList) {
      if (_selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else {
        let params = {
          idList: idList
        }
        this.$API.rfxList.deletePointExpandList(params).then(() => {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        })
      }
    },

    //新增
    handleAddFn(e) {
      if (
        this.forObject.pricingType == 0 ||
        this.forObject.pricingType == 3 ||
        this.forObject.pricingType == 4 ||
        this.forObject.pricingType == 5
      ) {
        e.grid.addRecord()
      } else {
        this.$dialog({
          modal: () => import('./newPricingLines/index.vue'),
          data: {
            title: this.$t('选择定价行'),
            pointId: this.$route.query.id
          },
          success: (data) => {
            const grid = this.$refs.templateRef.getCurrentTabRef().grid //获取表格实例
            grid.$options.propsData.dataSource.push(
              ...data.map((i) => ({
                ...i,
                id: undefined
              }))
            )
            grid.refresh() //刷新表格
          }
        })
      }
    },

    handleClickToolBar(e) {
      if (e.toolbar.id == 'Cancel') {
        e.grid.closeEdit()
        this.allowSubmit = true
        setTimeout(() => {
          this.$refs.templateRef.refreshCurrentGridData()
        }, 1)
        return
      }
      this.tabIndex = e.tabIndex
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      let idList = []
      _selectGridRecords.map((item) => {
        idList.push(item.id)
      })
      if (e.toolbar.id == 'Save') {
        this.endEdit()
        // if (e.tabIndex == 0) {
        //   setTimeout(() => {
        //     // this.handleSaveFn(e); //保存
        //     this.handleSaveFn(); //保存
        //   }, 100);
        // } else {
        //   setTimeout(() => {
        //     this.handleSavePointExpand(); //保存
        //   }, 100);
        // }
      } else if (e.toolbar.id == 'del') {
        if (e.tabIndex == 0) {
          this.handleDelFn(_selectGridRecords, idList) //删除
        } else {
          this.handleDelPointExpand(_selectGridRecords, idList) //删除
        }
      } else if (e.toolbar.id == 'SaveTwo') {
        this.endEdit()
        setTimeout(() => {
          this.handleSaveTwoFn(e) //保存
        }, 100)
      } else if (e.toolbar.id == 'Add') {
        this.handleAddFn(e) //新增
      } else if (e.toolbar.id == 'export') {
        this.handleExport()
      }
    },
    //导出
    handleExport() {
      this.$store.commit('startLoading')
      const params = {
        page: { current: 1, size: 500000 },
        defaultRules: [
          { field: 'decidePriceType', operator: 'equal', value: this.decidePriceType },
          {
            field: 'id',
            operator: 'equal',
            value: this.$route.query.id
          }
        ]
      } // 筛选条件
      this.$API.rfxList
        .excelExport(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
          this.$store.commit('endLoading')
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    handleClickCellTitle(e) {
      console.log(333, e.data)
      if (e.field == 'biddingItemStageStrName') {
        if (e.data.id) {
          this.$dialog({
            modal: () => import('./ladderQuotes/index.vue'),
            data: {
              title: this.$t('阶梯报价'),
              id: e.data.id,
              pricingType: this.forObject.pricingType,
              status: this.$route.query.status,
              stepQuote: e.data.stepQuote
            },
            success: () => {
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        } else {
          this.$toast({
            content: this.$t('请先保存新增的数据'),
            type: 'warning'
          })
        }
      }
    },
    async detailPoint() {
      let param = {
        id: this.$route.query.id
      }
      const res = await this.$API.rfxList.detailPoint(param).catch(() => {})
      if (res.code == 200) {
        this.forObject = { ...res.data }
        this.forObject.firm = res.data.companyName
        this.forObject.personnel = res.data.purName
        this.forObject.pricingCode = res.data.pointNo
        this.forObject.pricingName = res.data.title
        this.forObject.Purchasing = res.data.purOrgName
        this.forObject.pricingType = res.data.decidePriceType
        this.forObject.state = res.data.status
        this.forObject.compressor = res?.data?.compressor == '1' ? true : false
        this.forObject.isProperty = res?.data?.isProperty == '1' ? true : false
        this.forObject.isNewUpFlag = res?.data?.isNewUpFlag == '1' ? true : false
        this.forObject.isNewProduct = res?.data?.isNewProduct == '1' ? true : false
        this.forObject.sourceToSapFlag = res?.data?.sourceToSapFlag == '1' ? true : false
        if (this.decidePriceType < 5) {
          this.$refs.supDialog.value = res.data?.supplierCode + '-' + res.data?.supplierName
        }
        this.resInfo = res.data
        if ([0, 3, 7, 8].includes(res.data.status)) {
          this.isDisabled = false
        } else {
          this.isDisabled = true
        }
        // sessionStorage.setItem("organizationId", this.resInfo.purOrgId);
        let obj = {
          _supplierCode: this.resInfo?.supplierCode,
          _decidePriceType: this.resInfo.decidePriceType,
          _priceClassification: this.resInfo.priceClassification,
          _isProperty: this.resInfo.isProperty
        }
        sessionStorage.setItem('fixedPointObj', JSON.stringify(obj))
        this.pageConfig = pageConfig(
          this.$API.rfxList.pagePointDetailItem,
          this.$route.query.id,
          this.serializeList,
          this.recordDoubleClick,
          this.decidePriceType
        )
        if (this.decidePriceType == 4) {
          this.$set(
            this.pageConfig[0],
            'toolbar',
            toolbar(![1, 2].includes(Number(this.forObject.state))) // 1,2 禁止
          )
          this.$set(
            this.pageConfig[1],
            'toolbar',
            distributeToolbar(![1, 2].includes(Number(this.forObject.state))) // 1,2 禁止
          )
        } else if (this.decidePriceType == 5) {
          this.$set(
            this.pageConfig[0],
            'toolbar',
            strikeToolbar(![1, 2].includes(Number(this.forObject.state))) // 1,2 禁止
          )
        } else {
          this.$set(
            this.pageConfig[0],
            'toolbar',
            toolbar(![1, 2].includes(Number(this.forObject.state))) // 1,2 禁止
          )
        }
      }
    },
    serializeList(list) {
      list.forEach((x) => {
        x.biddingItemStageStrName =
          x.pointBiddingItemStage && x.pointBiddingItemStage.length > 0
            ? JSON.stringify(x.pointBiddingItemStage)
            : null //单独处理阶梯报价
        x.allocationRatio = (x.allocationRatio * 100).toFixed(2)
      })
      return list
    }
  },
  deactivated() {
    sessionStorage.removeItem('fixedPointObj')
    sessionStorage.removeItem('organizationId')
    sessionStorage.removeItem('currentPriceItemCode')
    sessionStorage.removeItem('priceCompanyId')
    sessionStorage.removeItem('pricingSiteCode')
    bus.$off('purOrgNameChange')
    bus.$off('siteNameChange')
  },
  beforeDestroy() {
    sessionStorage.removeItem('fixedPointObj')
    sessionStorage.removeItem('organizationId')
    sessionStorage.removeItem('currentPriceItemCode')
    sessionStorage.removeItem('priceCompanyId')
    sessionStorage.removeItem('pricingSiteCode')
    bus.$off('purOrgNameChange')
    bus.$off('siteNameChange')
  }
}
</script>

<style scoped lang="scss">
@import '../../../../../node_modules/@mtech/mtech-common-uploader/build/esm/bundle.css';
.operate-content {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
}
.mt-form {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  .grid_flex {
    width: 679px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.operate-bar {
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #4f5b6d;
  font-size: 14px;
  // padding: 0 20px;
}
.operate-bars {
  width: 20px;
  height: 20px;
  background: rgba(0, 70, 156, 0.1);
  border: 1px solid rgba(0, 70, 156, 0.9);
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;

  i {
    width: 6px;
    height: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: rotate(90deg);
  }
}
.miam-container {
  background: #fff;
  padding: 0 10px;

  .mian-info {
    // height: 300px;
    background: rgba(255, 255, 255, 1);
    // border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    // padding: 0 20px;
    min-width: 1300px;
    .normal-title {
      width: 100%;
      height: 14px;
      line-height: 14px;
      font-size: 14px;
      color: #292929;
      font-family: PingFangSC;
      font-weight: 500;

      &:before {
        content: ' ';
        display: inline-block;
        vertical-align: middle;
        width: 2px;
        height: 10px;
        background: rgba(0, 70, 156, 1);
        border-radius: 1px;
        margin-right: 10px;
      }
    }

    .input-item {
      margin-top: 20px;
      padding-right: 50px;
      .label-txt {
        height: 40px;
        width: 130px;
        font-size: 14px;
        // line-height: 40px;
        color: #292929;
      }
      .label-value {
        height: 40px;
        width: 130px;
        font-size: 14px;
        // line-height: 40px;
        color: #35404e;
      }
      .select-container {
        height: 40px;
      }
      .e-label {
        color: #35404e;
      }
      .label-text {
        color: #35404e;
      }
    }
    .input-item /deep/ .normal-width {
      width: 240px;
    }
    .input-item /deep/ .e-radio + label .e-label {
      color: #35404e;
    }
    /deep/.mt-form-item {
      width: 24.5%;
      .mt-form-item-label {
        .mt-common-uploader,
        .mt-input {
          width: 100%;
        }
      }
    }
  }
}
.relation-ships {
  flex: 1;
  background: rgba(255, 255, 255, 1);
}
.btns-wrap {
  /deep/ .mt-button {
    margin-right: 0;
    button {
      background: transparent;
      //border: 1px solid rgba(0, 70, 156, 0.1);
      border-radius: 4px;
      box-shadow: unset;
      padding: 6px 12px 4px;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(0, 70, 156, 1);
    }
  }
}
.custom-width {
  width: 330px;
}
.pl-30 {
  padding-left: 30px;
}
.full-width {
  width: 100% !important;
}
.min-80 {
  /deep/ .e-label {
    width: 80px;
  }
}
</style>
