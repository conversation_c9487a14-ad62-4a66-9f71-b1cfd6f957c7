<template>
  <mt-dialog ref="arbitratorDialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="full-height">
      <mt-template-page
        ref="templateRefEdit"
        :key="templateKey"
        :use-tool-template="false"
        :template-config="templateConfig"
        @handleClickToolBar="handleClickToolBar"
      ></mt-template-page>
    </div>
  </mt-dialog>
</template>
<script>
import { errorColumn } from '../config/index'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  data() {
    return {
      templateConfig: [
        {
          toolbar: [{ id: 'export', icon: 'icon_solid_Download ', title: this.$t('导出') }],
          grid: {
            columnData: errorColumn,
            asyncConfig: {
              url: `/sourcing/tenant/point2/pageBasicAndAverage`,
              defaultRules: [
                {
                  label: this.$t('定点推荐id'),
                  field: 'point.id',
                  type: 'string',
                  operator: 'equal',
                  value: this.modalData.id
                },
                {
                  label: this.$t('状态'),
                  field: 'item.status',
                  type: 'string',
                  operator: 'equal',
                  value: -1
                }
              ]
            }
          }
        }
      ],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('关闭') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    console.log('modalData=', this.modalData)
    this.getList()
    this.show()
  },
  methods: {
    //表格按钮-点击事件
    handleClickToolBar(e) {
      if (e.toolbar.id === 'export') {
        this.handleExport()
      }
    },
    //导出
    handleExport() {
      // 先要点击“查询”按钮才能取得queryBuilderRules
      const queryBuilderRules =
        this.$refs.templateRefEdit.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules,
        defaultRules: [
          {
            label: this.$t('定点推荐id'),
            field: 'point.id',
            type: 'string',
            operator: 'equal',
            value: this.modalData.id
          },
          {
            label: this.$t('状态'),
            field: 'item.status',
            type: 'string',
            operator: 'equal',
            value: -1
          }
        ]
      } // 筛选条件
      this.$API.rfxList.exportErrorInfoList(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    getList() {},
    show() {
      this.$refs['arbitratorDialog'].ejsRef.show()
    },
    hide() {
      this.$refs['arbitratorDialog'].ejsRef.hide()
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
