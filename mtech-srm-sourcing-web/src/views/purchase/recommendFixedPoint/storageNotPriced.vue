<template>
  <!-- 入库未定价 -->
  <div class="full-height">
    <!-- 列模板 -->
    <mt-template-page
      ref="templateRef"
      :template-config="templateConfig"
      @handleClickToolBar="handleClickToolBar"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { storageColumnData } from './config'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  components: {},
  data() {
    return {
      templateConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          toolbar: {
            tools: [
              [
                {
                  id: 'export',
                  icon: 'icon_solid_export',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          gridId: 'bc1c3f6d-d116-40c2-a7c0-34f9bd9e726e',
          activatedRefresh: false,
          grid: {
            allowPaging: true, // 分页
            // allowTextWrap: true,
            lineSelection: true,
            columnData: storageColumnData,
            asyncConfig: {
              ignoreDefaultSearch: true,
              url: `/srm-purchase-execute/tenant/po/in_out_record/queryNotPriced` // 订单库存出入库接口 - 获取采方订单主单列表
            }
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(args) {
      const { toolbar } = args
      // const selectRows = gridRef.getMtechGridRecords();
      // if (selectRows.length === 0 && toolbar.id == "export") {
      //   this.$toast({
      //     content: this.$t("请先选择需要导出的数据"),
      //     type: "warning",
      //   });
      //   return;
      // }
      // let _ids = selectRows.map((e) => e.id);
      if (toolbar.id == 'export') {
        this.$store.commit('startLoading')
        const queryBuilderRules =
          this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        const params = {
          condition: 'and',
          page: { current: 1, size: 200000 },
          ...queryBuilderRules
          // rules: [
          //   {
          //     field: "id",
          //     type: "string",
          //     operator: "in",
          //     value: _ids,
          //   },
          // ],
        } // 筛选条件
        this.$API.rfxList
          .exportNotPriced(params)
          .then((res) => {
            this.$store.commit('endLoading')
            const fileName = getHeadersFileName(res)
            download({ fileName: `${fileName}`, blob: res.data })
          })
          .catch(() => {
            this.$store.commit('endLoading')
          })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/.container {
  position: unset;
  width: unset;
  height: unset;
  overflow: unset;
  background: unset;
}
</style>
