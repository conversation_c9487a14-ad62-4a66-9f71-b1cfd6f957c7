import Vue from 'vue'
import { i18n } from '@/main.js'
import { Formatter } from '@/utils/ej/dataGrid/index'
import { searchOptionsList } from '@/constants'
import utils from '@/utils/utils'
import { codeNameColumn } from '@/utils/utils'
export const todoListToolBar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'del', icon: 'icon_solid_Cancel', title: i18n.t('删除') },
  { id: 'Submit', icon: 'icon_solid_Submit', title: i18n.t('提交') },
  { id: 'import', icon: 'icon_solid_upload', title: i18n.t('导入') },
  { id: 'export', icon: 'icon_solid_Download ', title: i18n.t('导出') }
]
export const detailToolBar = [{ id: 'export', icon: 'icon_solid_Download ', title: i18n.t('导出') }]

export const strikeToolBar = [
  { id: 'del', icon: 'icon_solid_Cancel', title: i18n.t('删除') },
  { id: 'import', icon: 'icon_solid_upload', title: i18n.t('导入') },
  { id: 'export', icon: 'icon_solid_Download ', title: i18n.t('导出') },
  { id: 'getData', icon: 'icon_solid_Submit', title: i18n.t('获取数据') },
  {
    id: 'uploadSap',
    icon: 'icon_solid_upload',
    permission: ['O_02_1609'],
    title: i18n.t('推送SAP')
  }
]

export const contractToolBar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'del', icon: 'icon_solid_Cancel', title: i18n.t('删除') },
  { id: 'Submit', icon: 'icon_solid_Submit', title: i18n.t('提交') },
  { id: 'import', icon: 'icon_solid_upload', title: i18n.t('导入') },
  { id: 'export', icon: 'icon_solid_Download ', title: i18n.t('导出') },
  { id: 'syncSap', icon: 'icon_solid_Download ', title: i18n.t('同步SAP') }
]

export const todoListColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        '-1': i18n.t('已失效'),
        0: i18n.t('草稿'),
        1: i18n.t('审批中'),
        2: i18n.t('审批通过'),
        3: i18n.t('审批驳回')
      }
    }
  },
  {
    field: 'pointNo',
    width: '220',
    headerText: i18n.t('定价单号'),
    cssClass: 'field-content'
  },
  {
    field: 'title',
    headerText: i18n.t('标题')
  },
  {
    field: 'decidePriceType',
    headerText: i18n.t('定价单类型'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('直接定价'), 1: i18n.t('寻源结果定价') }
    }
  },
  {
    field: 'priceObjectName',
    headerText: i18n.t('寻源对象')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司')
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织')
  },
  {
    field: 'purName',
    headerText: i18n.t('采购员')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    allowFiltering: false,
    searchOptions: {
      ...searchOptionsList.timeRange
    }
  }
]
export const baseColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        '-1': i18n.t('已失效'),
        0: i18n.t('草稿'),
        1: i18n.t('审批中'),
        2: i18n.t('审批通过'),
        3: i18n.t('审批驳回'),
        // 4: i18n.t('同步中'),
        // 5: i18n.t('同步成功'),
        // 6: i18n.t('同步失败'),
        7: i18n.t('审批废弃'),
        8: i18n.t('审批撤回')
      }
    }
  },
  {
    field: 'pointNo',
    width: '220',
    headerText: i18n.t('定价单号'),
    cssClass: 'field-content'
  },
  // {
  //   field: "title",
  //   headerText: i18n.t("标题"),
  // },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商')
  },
  // {
  //   field: "companyName",
  //   headerText: i18n.t("公司"),
  // },
  // {
  //   field: "purOrgName",
  //   headerText: i18n.t("采购组织"),
  // },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    allowFiltering: false,
    searchOptions: {
      ...searchOptionsList.timeRange
    },
    allowGlobalSorting: true
  }
]
export const newColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        '-1': i18n.t('已失效'),
        0: i18n.t('草稿'),
        1: i18n.t('审批中'),
        2: i18n.t('审批通过'),
        3: i18n.t('审批驳回'),
        // 4: i18n.t('同步中'),
        // 5: i18n.t('同步成功'),
        // 6: i18n.t('同步失败'),
        7: i18n.t('审批废弃')
      }
    }
  },
  {
    field: 'pointNo',
    width: '220',
    headerText: i18n.t('定价单号'),
    cssClass: 'field-content'
  },
  // {
  //   field: "title",
  //   headerText: i18n.t("标题"),
  // },
  // {
  //   field: "decidePriceType",
  //   headerText: i18n.t("定价单类型"),
  //   valueConverter: {
  //     type: "map",
  //     map: {
  //       0: i18n.t("直接定价"),
  //       1: i18n.t("寻源结果定价"),
  //       3: i18n.t("基价定价"),
  //       4: i18n.t("均价定价"),
  //       5: i18n.t("执行价定价"),
  //     },
  //   },
  // },
  // {
  //   field: "priceObjectName",
  //   headerText: i18n.t("寻源对象"),
  // },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商')
  },
  // {
  //   field: "companyName",
  //   headerText: i18n.t("公司"),
  // },
  // {
  //   field: "purOrgName",
  //   headerText: i18n.t("采购组织"),
  // },
  // {
  //   field: "purName",
  //   headerText: i18n.t("采购员"),
  // },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    allowFiltering: false,
    allowGlobalSorting: true,
    searchOptions: {
      ...searchOptionsList.timeRange
    }
  }
]
export const strikeColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        '-1': i18n.t('已失效'),
        0: i18n.t('草稿'),
        1: i18n.t('审批中'),
        2: i18n.t('审批通过'),
        3: i18n.t('审批驳回'),
        // 4: i18n.t('同步中'),
        5: i18n.t('同步成功'),
        6: i18n.t('同步失败'),
        7: i18n.t('审批废弃'),
        8: i18n.t('审批撤回')
      }
    }
  },
  {
    field: 'pointNo',
    width: '220',
    headerText: i18n.t('定价单号'),
    cssClass: 'field-content'
  },
  {
    field: 'decidePriceType',
    headerText: i18n.t('定价单类型'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('直接定价'),
        1: i18n.t('寻源结果定价'),
        3: i18n.t('基价定价'),
        4: i18n.t('均价定价'),
        5: i18n.t('执行价定价')
      }
    }
  },
  {
    field: 'priceObjectName',
    headerText: i18n.t('定价对象')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    allowFiltering: false,
    allowGlobalSorting: true,
    searchOptions: {
      ...searchOptionsList.timeRange
    }
  },
  {
    field: 'operator',
    width: '150',
    headerText: i18n.t('操作'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<a style="font-size: 14px;color: rgba(99,134,193,1);" @click="showDetail">${i18n.t(
            '错误信息'
          )}</a>`,
          data() {
            return { data: {} }
          },
          methods: {
            showDetail() {
              sessionStorage.setItem('assessTypeCode', this.data.typeCode)
              this.$dialog({
                modal: () => import('../components/errorList.vue'),
                data: {
                  title: i18n.t('错误信息'),
                  id: this.data.id
                }
              })
            }
          }
        })
      }
    }
  }
]

export const errorColumn = [
  {
    field: 'formulaStage',
    headerText: i18n.t('公式阶段')
  },
  {
    field: 'errorInfo',
    headerText: i18n.t('错误信息')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'storageTime',
    headerText: i18n.t('入库日期'),
    formatter: Formatter.createFmtDatetime('YYYY-MM-DD')
  }
]

export const contractColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        '-1': i18n.t('已失效'),
        0: i18n.t('草稿'),
        1: i18n.t('审批中'),
        2: i18n.t('审批通过'),
        3: i18n.t('审批驳回'),
        4: i18n.t('同步中'),
        5: i18n.t('同步成功'),
        6: i18n.t('同步失败'),
        7: i18n.t('审批废弃'),
        8: i18n.t('审批撤回')
      }
    }
  },
  {
    field: 'pointNo',
    width: '220',
    headerText: i18n.t('定价单号'),
    cssClass: 'field-content'
  },
  // {
  //   field: 'companyName',
  //   headerText: i18n.t('公司')
  // },
  // {
  //   field: 'purOrgName',
  //   headerText: i18n.t('采购组织')
  // },
  // {
  //   field: 'siteName',
  //   headerText: i18n.t('工厂')
  // },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'purName',
    headerText: i18n.t('采购员')
  },
  {
    field: 'priceClassification',
    headerText: i18n.t('价格类型'),
    valueConverter: {
      type: 'map',
      map: {
        null: '',
        3: i18n.t('暂估价'),
        4: i18n.t('正式价')
      }
    }
  },
  {
    field: 'contractType',
    headerText: i18n.t('合同类型'),
    valueConverter: {
      type: 'map',
      map: {
        '': '',
        standard: i18n.t('标准'),
        rolled: i18n.t('钢材'),
        outsource: i18n.t('委外')
      }
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    allowFiltering: false,
    allowGlobalSorting: true,
    searchOptions: {
      ...searchOptionsList.timeRange
    }
  }
]

export const contractDetailCols = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        '-1': i18n.t('已失效'),
        0: i18n.t('草稿'),
        1: i18n.t('审批中'),
        2: i18n.t('审批通过'),
        3: i18n.t('审批驳回'),
        4: i18n.t('同步中'),
        5: i18n.t('同步成功'),
        6: i18n.t('同步失败'),
        7: i18n.t('审批废弃'),
        8: i18n.t('审批撤回')
      }
    }
  },
  {
    field: 'pointNo',
    width: '220',
    headerText: i18n.t('定价单号'),
    cssClass: 'field-content'
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'purName',
    headerText: i18n.t('采购员')
  },
  {
    field: 'decidePriceType',
    headerText: i18n.t('来源'),
    valueConverter: {
      type: 'map',
      map: {
        5: i18n.t('执行价'),
        7: i18n.t('价格合同')
      }
    }
  },
  {
    field: 'priceClassification',
    headerText: i18n.t('价格类型'),
    valueConverter: {
      type: 'map',
      map: {
        null: '',
        3: i18n.t('暂估价'),
        4: i18n.t('正式价')
      }
    },
    searchOptions: {
      elementType: 'multi-select'
    }
  },
  {
    field: 'contractType',
    headerText: i18n.t('合同类型'),
    valueConverter: {
      type: 'map',
      map: {
        '': '',
        standard: i18n.t('标准'),
        rolled: i18n.t('钢材'),
        outsource: i18n.t('委外')
      }
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    allowFiltering: false,
    allowGlobalSorting: true,
    searchOptions: {
      ...searchOptionsList.timeRange
    }
  }
]

export const baseDetailColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        '-1': i18n.t('已失效'),
        0: i18n.t('草稿'),
        1: i18n.t('审批中'),
        2: i18n.t('审批通过'),
        3: i18n.t('审批驳回'),
        // 4: i18n.t('同步中'),
        // 5: i18n.t('同步成功'),
        // 6: i18n.t('同步失败'),
        7: i18n.t('审批废弃')
      }
    },
    ignore: true,
    allowFiltering: false,
    searchOptions: {
      renameField: 'point.status'
    }
  },
  {
    field: 'pointNo',
    width: '220',
    headerText: i18n.t('基价单号'),
    cssClass: 'field-content',
    searchOptions: {
      renameField: 'point.point_no'
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司'),
    searchOptions: {
      renameField: 'item.companyName'
    }
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织'),
    searchOptions: {
      renameField: 'item.purOrgName'
    }
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂'),
    searchOptions: {
      renameField: 'item.siteCode',
      operator: 'likeright'
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    searchOptions: {
      renameField: 'point.supplierCode'
    }
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    searchOptions: {
      renameField: 'point.supplierName'
    }
  },
  {
    field: 'lineNo',
    headerText: i18n.t('行号'),
    width: 87,
    searchOptions: {
      renameField: 'item.lineNo'
    }
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类代码'),
    searchOptions: {
      renameField: 'item.categoryCode'
    }
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    searchOptions: {
      renameField: 'item.categoryName'
    }
  },
  {
    field: 'propertyCode',
    headerText: i18n.t('属性代码'),
    searchOptions: {
      renameField: 'item.propertyCode'
    }
  },
  {
    field: 'propertyName',
    headerText: i18n.t('属性名称'),
    searchOptions: {
      renameField: 'item.propertyName'
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    searchOptions: {
      renameField: 'item.itemCode',
      operator: 'likeright'
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料描述'),
    searchOptions: {
      renameField: 'item.itemName'
    }
  },
  {
    field: 'unitName',
    headerText: i18n.t('单位'),
    searchOptions: {
      renameField: 'item.unitName'
    }
  },
  {
    field: 'priceUnitName',
    headerText: i18n.t('价格单位'),
    searchOptions: {
      renameField: 'item.priceUnitName'
    }
  },
  {
    field: 'lastTaxedBasicPrice',
    headerText: i18n.t('原含税基价'),
    searchOptions: {
      renameField: 'biddingItem.lastTaxedBasicPrice'
    }
  },
  {
    field: 'taxedBasicPrice',
    headerText: i18n.t('含税基价'),
    searchOptions: {
      renameField: 'biddingItem.taxedBasicPrice'
    }
  },
  {
    field: 'lowTaxedBasicPrice',
    headerText: i18n.t('最低含税基价'),
    searchOptions: {
      renameField: 'biddingItem.lowTaxedBasicPrice'
    }
  },
  {
    field: 'priceDifference',
    headerText: i18n.t('价格差异'),
    searchOptions: {
      renameField: 'biddingItem.priceDifference'
    }
  },
  {
    field: 'quoteEffectiveStartDate',
    headerText: i18n.t('有效期从'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (data && data != 0) {
          return utils.formatTime(new Date(parseInt(data)), 'YYYY-mm-dd')
        } else {
          return '-'
        }
      }
    },
    searchOptions: {
      renameField: 'biddingItem.quoteEffectiveStartDate',
      ...searchOptionsList.timeRange
    }
  },
  {
    field: 'quoteEffectiveEndDate',
    headerText: i18n.t('有效期至'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (data && data != 0) {
          return utils.formatTime(new Date(parseInt(data)), 'YYYY-mm-dd')
        } else {
          return '-'
        }
      }
    },
    searchOptions: {
      renameField: 'biddingItem.quoteEffectiveEndDate',
      ...searchOptionsList.timeRange
    }
  },
  {
    field: 'plannedDeliveryTime',
    headerText: i18n.t('计划交货时间（天）'),
    searchOptions: {
      renameField: 'biddingItem.plannedDeliveryTime'
    }
  },
  {
    field: 'purGroupName',
    headerText: i18n.t('采购组'),
    searchOptions: {
      renameField: 'item.purGroupName'
    }
  },
  {
    field: 'propertyPrice',
    headerText: i18n.t('价格属性'),
    valueConverter: {
      type: 'map',
      map: { new: i18n.t('新品'), daily: i18n.t('日常'), bidding: i18n.t('招标') }
    },
    searchOptions: {
      renameField: 'biddingItem.propertyPrice',
      elementType: 'select'
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    searchOptions: {
      renameField: 'item.createUserName'
    }
  },
  {
    field: 'updateTime',
    headerText: i18n.t('审批时间'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (data && data != 0) {
          return utils.formatTime(new Date(parseInt(data)), 'YYYY-mm-dd')
        } else {
          return '-'
        }
      }
    },
    searchOptions: {
      ...searchOptionsList.timeRange,
      renameField: 'point.updateTime'
    },
    allowGlobalSorting: true
  },
  {
    field: 'remark',
    headerText: i18n.t('备注'),
    searchOptions: {
      renameField: 'item.remark'
    }
  }
]

export const averageDetailColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        '-1': i18n.t('已失效'),
        0: i18n.t('草稿'),
        1: i18n.t('审批中'),
        2: i18n.t('审批通过'),
        3: i18n.t('审批驳回'),
        // 4: i18n.t('同步中'),
        // 5: i18n.t('同步成功'),
        // 6: i18n.t('同步失败'),
        7: i18n.t('审批废弃')
      }
    },
    ignore: true,
    allowFiltering: false,
    searchOptions: {
      renameField: 'point.status'
    }
  },
  {
    field: 'pointNo',
    width: '220',
    headerText: i18n.t('均价单号'),
    cssClass: 'field-content',
    searchOptions: {
      renameField: 'point.point_no'
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司'),
    searchOptions: {
      renameField: 'item.companyName'
    }
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织'),
    searchOptions: {
      renameField: 'item.purOrgName'
    }
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂'),
    searchOptions: {
      renameField: 'item.siteCode'
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    searchOptions: {
      renameField: 'point.supplierCode'
    }
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    searchOptions: {
      renameField: 'point.supplierName'
    }
  },
  {
    field: 'lineNo',
    headerText: i18n.t('行号'),
    width: 87,
    searchOptions: {
      renameField: 'item.lineNo'
    }
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类代码'),
    searchOptions: {
      renameField: 'item.categoryCode'
    }
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    searchOptions: {
      renameField: 'item.categoryName'
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('成本因子编码'),
    searchOptions: {
      renameField: 'item.itemCode'
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('成本因子名称'),
    searchOptions: {
      renameField: 'item.itemName'
    }
  },
  {
    field: 'priceUnitName',
    headerText: i18n.t('单位'),
    searchOptions: {
      renameField: 'item.priceUnitName'
    }
  },
  {
    field: 'lastTaxedAveragePrice',
    headerText: i18n.t('上次含税均价'),
    searchOptions: {
      renameField: 'biddingItem.lastTaxedAveragePrice'
    }
  },
  {
    field: 'taxedAveragePrice',
    headerText: i18n.t('含税均价'),
    searchOptions: {
      renameField: 'biddingItem.taxedAveragePrice'
    }
  },
  {
    field: 'priceDifference',
    headerText: i18n.t('价格差异'),
    searchOptions: {
      renameField: 'biddingItem.priceDifference'
    }
  },
  {
    field: 'quoteEffectiveStartDate',
    headerText: i18n.t('有效期从'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (data && data != 0) {
          return utils.formatTime(new Date(parseInt(data)), 'YYYY-mm-dd')
        } else {
          return '-'
        }
      }
    },
    searchOptions: {
      renameField: 'biddingItem.quoteEffectiveStartDate',
      ...searchOptionsList.timeRange
    }
  },
  {
    field: 'quoteEffectiveEndDate',
    headerText: i18n.t('有效期至'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (data && data != 0) {
          return utils.formatTime(new Date(parseInt(data)), 'YYYY-mm-dd')
        } else {
          return '-'
        }
      }
    },
    searchOptions: {
      renameField: 'biddingItem.quoteEffectiveEndDate',
      ...searchOptionsList.timeRange
    }
  },
  {
    field: 'updateTime',
    headerText: i18n.t('审批时间'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (data && data != 0) {
          return utils.formatTime(new Date(parseInt(data)), 'YYYY-mm-dd')
        } else {
          return '-'
        }
      }
    },
    allowGlobalSorting: true,
    searchOptions: {
      ...searchOptionsList.timeRange,
      renameField: 'point.updateTime'
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('备注'),
    searchOptions: {
      renameField: 'item.remark'
    }
  }
]

export const contractDetailColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        2: i18n.t('审批通过'),
        5: i18n.t('同步成功'),
        6: i18n.t('同步失败')
      }
    },
    ignore: true,
    allowFiltering: false,
    searchOptions: {
      renameField: 'point.status'
    }
  },
  {
    field: 'pointNo',
    width: '220',
    headerText: i18n.t('订单编号'),
    searchOptions: {
      renameField: 'point.point_no'
    },
    cssClass: 'field-content'
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    searchOptions: {
      renameField: 'point.supplierCode'
    }
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    searchOptions: {
      renameField: 'point.supplierName'
    }
  },
  {
    field: 'priceClassificationValue',
    headerText: i18n.t('价格类型'),
    valueConverter: {
      type: 'map',
      map: {
        3: i18n.t('暂估价'),
        4: i18n.t('正式价')
      }
    },
    searchOptions: {
      renameField: 'point.priceClassification'
    }
  },
  {
    field: 'contractType',
    headerText: i18n.t('合同类型'),
    valueConverter: {
      type: 'map',
      map: {
        '': '',
        standard: i18n.t('标准'),
        rolled: i18n.t('钢材'),
        outsource: i18n.t('委外')
      }
    },
    searchOptions: {
      renameField: 'point.contractType'
    }
  },
  {
    field: 'decidePriceType',
    headerText: i18n.t('来源'),
    valueConverter: {
      type: 'map',
      map: {
        5: i18n.t('执行价'),
        7: i18n.t('价格合同')
      }
    },
    searchOptions: {
      renameField: 'point.decidePriceType'
    }
  },
  {
    field: 'bidCurrencyName',
    headerText: i18n.t('币种'),
    searchOptions: {
      renameField: 'point.bidCurrencyName'
    }
  },
  {
    field: 'bidTaxRateValue',
    headerText: i18n.t('税率'),
    searchOptions: {
      renameField: 'point.bidCurrencyName'
    }
  },
  {
    field: 'lineNo',
    headerText: i18n.t('行号'),
    width: 87,
    searchOptions: {
      renameField: 'item.lineNo'
    }
  },
  {
    field: 'orderNo',
    headerText: i18n.t('订单号'),
    searchOptions: {
      renameField: 'item.orderNo'
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司'),
    searchOptions: {
      renameField: 'item.companyName'
    }
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织'),
    searchOptions: {
      renameField: 'item.purOrgName'
    }
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂编码'),
    searchOptions: {
      renameField: 'item.siteCode'
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    searchOptions: {
      renameField: 'item.siteName'
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    searchOptions: {
      renameField: 'item.itemCode'
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    searchOptions: {
      renameField: 'item.itemName'
    }
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类代码'),
    searchOptions: {
      renameField: 'item.categoryCode'
    }
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    searchOptions: {
      renameField: 'item.categoryName'
    }
  },
  {
    field: 'plannedDeliveryTime',
    headerText: i18n.t('计划交货时间（天）'),
    searchOptions: {
      renameField: 'biddingItem.plannedDeliveryTime'
    }
  },
  {
    field: 'purGroupName',
    headerText: i18n.t('采购组'),
    searchOptions: {
      renameField: 'item.purGroupName'
    }
  },
  {
    field: 'unitName',
    headerText: i18n.t('单位'),
    searchOptions: {
      renameField: 'item.unitName'
    }
  },
  {
    field: 'priceUnitName',
    headerText: i18n.t('价格单位'),
    searchOptions: {
      renameField: 'item.priceUnitName'
    }
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('含税单价'),
    searchOptions: {
      renameField: 'biddingItem.taxedUnitPrice'
    }
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('未税单价'),
    searchOptions: {
      renameField: 'biddingItem.untaxedUnitPrice'
    }
  },
  {
    field: 'lowUntaxedExercisePrice',
    headerText: i18n.t('未税采购最低价'),
    searchOptions: {
      renameField: 'biddingItem.lowUntaxedExercisePrice'
    }
  },
  {
    field: 'priceDifference',
    headerText: i18n.t('价格差异'),
    searchOptions: {
      renameField: 'biddingItem.priceDifference'
    }
  },
  {
    field: 'quoteAttribute',
    headerText: i18n.t('报价属性'),
    valueConverter: {
      type: 'map',
      map: {
        '': '',
        standard_price: i18n.t('标准价'),
        mailing_price: i18n.t('寄售价')
      }
    },
    searchOptions: {
      renameField: 'biddingItem.quoteAttribute'
    }
  },
  {
    field: 'quoteMode',
    headerText: i18n.t('报价方式'),
    valueConverter: {
      type: 'map',
      map: {
        '': '',
        in_warehouse: i18n.t('按照入库'),
        out_warehouse: i18n.t('按出库'),
        order_date: i18n.t('按订单日期')
      }
    },
    searchOptions: {
      renameField: 'biddingItem.quoteMode'
    }
  },
  {
    field: 'quoteEffectiveStartDate',
    headerText: i18n.t('有效期从'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (data && data != 0) {
          return utils.formatTime(new Date(parseInt(data)), 'YYYY-mm-dd')
        } else {
          return '-'
        }
      }
    },
    searchOptions: {
      renameField: 'biddingItem.quoteEffectiveStartDate',
      ...searchOptionsList.timeRange
    }
  },
  {
    field: 'quoteEffectiveEndDate',
    headerText: i18n.t('有效期至'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (data && data != 0) {
          return utils.formatTime(new Date(parseInt(data)), 'YYYY-mm-dd')
        } else {
          return '-'
        }
      }
    },
    searchOptions: {
      renameField: 'biddingItem.quoteEffectiveEndDate',
      ...searchOptionsList.timeRange
    }
  },
  {
    field: 'startValue',
    headerText: i18n.t('开始数量'),
    searchOptions: {
      renameField: 'biddingItem.startValue'
    }
  },
  {
    field: 'endValue',
    headerText: i18n.t('结束数量'),
    searchOptions: {
      renameField: 'biddingItem.endValue'
    }
  },
  {
    field: 'propertyPrice',
    headerText: i18n.t('价格属性'),
    valueConverter: {
      type: 'map',
      map: { new: i18n.t('新品'), daily: i18n.t('日常'), bidding: i18n.t('招标') }
    },
    searchOptions: {
      renameField: 'biddingItem.propertyPrice',
      elementType: 'select'
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    searchOptions: {
      renameField: 'item.createUserName'
    }
  },
  {
    field: 'signedTime',
    headerText: i18n.t('签订日期'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (data && data != 0) {
          return utils.formatTime(new Date(parseInt(data)), 'YYYY-mm-dd')
        } else {
          return '-'
        }
      }
    },
    searchOptions: {
      ...searchOptionsList.timeRange,
      renameField: 'point.signedTime'
    }
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (data && data != 0) {
          return utils.formatTime(new Date(parseInt(data)), 'YYYY-mm-dd')
        } else {
          return '-'
        }
      }
    },
    allowGlobalSorting: true,
    searchOptions: {
      ...searchOptionsList.timeRange,
      renameField: 'point.createTime'
    }
  }
]

export const storageColumnData = [
  {
    width: '150',
    field: 'orderCode',
    headerText: i18n.t('采购订单号')
  },
  {
    width: '120',
    field: 'lineNo',
    headerText: i18n.t('采购订单行号')
  },
  {
    width: '150',
    field: 'itemVoucherDate',
    headerText: i18n.t('物料凭证日期'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (data && data != 0) {
          return utils.formatTime(new Date(parseInt(data)), 'YYYY-mm-dd')
        } else {
          return '-'
        }
      }
    },
    searchOptions: {
      ...searchOptionsList.timeRange
    }
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '250',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '180',
    field: 'prototypeCode',
    headerText: i18n.t('原机型编号')
  },
  {
    width: '200',
    field: 'buyerOrgName',
    headerText: i18n.t('采购组'),
    allowFiltering: false,
    ignore: true
  },
  {
    width: '150',
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    searchOptions: {
      ...searchOptionsList.categoryCode
    }
  },
  {
    width: '150',
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    ignore: true
  },
  {
    width: '120',
    field: 'unitName',
    headerText: i18n.t('单位'),
    searchOptions: {
      ...searchOptionsList.unit,
      fields: { text: 'title', value: 'unitName' }
    }
  },
  {
    width: '120',
    field: 'isNewItem',
    headerText: i18n.t('是否新品'),
    // ignore: true,
    allowFiltering: false,
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    width: '120',
    field: 'quantity',
    headerText: i18n.t('入库数量')
  },
  {
    width: '150',
    field: 'taxedUnitPrice',
    headerText: i18n.t('入库含税单价')
  },
  {
    width: '150',
    field: 'untaxedUnitPrice',
    headerText: i18n.t('入库未税单价')
  },
  {
    width: '250',
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    template: codeNameColumn({
      firstKey: 'supplierCode',
      secondKey: 'supplierName'
    }),
    allowFiltering: false,
    searchOptions: {
      ...searchOptionsList.supplier
    }
  },
  {
    width: '120',
    field: 'currencyName',
    headerText: i18n.t('币种')
  },
  {
    width: '200',
    field: 'orderTypeCode', // orderTypeName
    template: codeNameColumn({
      firstKey: 'orderTypeCode',
      secondKey: 'orderTypeName'
    }),
    allowFiltering: false,
    headerText: i18n.t('订单类型'),
    searchOptions: {
      ...searchOptionsList.dictOrderType,
      placeholder: i18n.t('请选择订单类型')
    }
  },
  {
    width: '300',
    field: 'companyCode',
    headerText: i18n.t('公司'),
    template: codeNameColumn({
      firstKey: 'companyCode',
      secondKey: 'companyName'
    }),
    allowFiltering: false,
    searchOptions: {
      ...searchOptionsList.businessCompany
    }
  },
  {
    width: '300',
    field: 'siteCode',
    headerText: i18n.t('地点/工厂'),
    template: codeNameColumn({
      firstKey: 'siteCode',
      secondKey: 'siteName'
    }),
    allowFiltering: false,
    searchOptions: {
      ...searchOptionsList.factoryAddress
    }
  },
  {
    width: '150',
    field: 'stockSite',
    headerText: i18n.t('库存地点'),
    searchOptions: {
      ...searchOptionsList.stockAddress,
      fields: { text: 'title', value: 'locationName' }
    }
  },
  {
    width: '150',
    field: 'transType',
    headerText: i18n.t('事务类型'),
    valueConverter: {
      type: 'map',
      map: {
        101: '101',
        102: '102',
        122: '122',
        123: '123',
        161: '161',
        261: '261',
        411: '411',
        YZ3: 'YZ3'
      }
    },
    searchOptions: {
      elementType: 'multi-select',
      operator: 'in'
    }
  },
  {
    width: '150',
    field: 'receiveCode',
    headerText: i18n.t('物料凭证号')
  },
  {
    width: '150',
    field: 'receiveItemNo',
    headerText: i18n.t('物料凭证行号')
  },
  {
    width: '200',
    field: 'deliveryCode',
    headerText: i18n.t('送货单号')
  },
  {
    width: '200',
    field: 'deliveryLineNo',
    headerText: i18n.t('送货单行号')
  },
  {
    width: '150',
    field: 'receiveTime',
    headerText: i18n.t('收货时间'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (data && data != 0) {
          return utils.formatTime(new Date(parseInt(data)), 'YYYY-mm-dd')
        } else {
          return '-'
        }
      }
    },
    searchOptions: {
      ...searchOptionsList.timeRange
    }
  }
]
