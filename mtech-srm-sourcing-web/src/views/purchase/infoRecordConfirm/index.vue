<template>
  <div class="full-height">
    <mt-local-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
              <mt-select
                v-model="searchFormModel.companyCode"
                css-class="rule-element"
                :data-source="companyList"
                :fields="{ text: 'text', value: 'orgCode' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择公司')"
                @change="handleCompanyChange"
              />
            </mt-form-item>
            <mt-form-item prop="purGroupCode" :label="$t('采购组织')">
              <mt-select
                v-model="searchFormModel.purGroupCode"
                :disabled="!searchFormModel.companyCode"
                :data-source="purchaseOrgList"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择采购组织')"
                :fields="{ text: 'organizationName', value: 'organizationCode' }"
              />
            </mt-form-item>
            <mt-form-item prop="siteCode" :label="$t('工厂')" label-style="top">
              <mt-select
                v-model="searchFormModel.siteCode"
                css-class="rule-element"
                :data-source="factoryList"
                :fields="{ text: 'text', value: 'orgCode' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择工厂')"
              />
            </mt-form-item>
            <mt-form-item prop="itemCode" :label="$t('物料')" label-style="top">
              <magnifier-input
                ref="itemCode"
                :config="materialDialogCofig"
                @change="(e) => (searchFormModel.itemCode = e.itemCode || null)"
              />
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('供应商')" label-style="top">
              <magnifier-input
                ref="supplierCode"
                :config="supplierDialogCofig"
                @change="(e) => (searchFormModel.supplierCode = e.supplierCode || null)"
              />
            </mt-form-item>
            <!-- <mt-form-item prop="categoryCode" :label="$t('品类')" label-style="top">
              <mt-select
                v-model="searchFormModel.categoryCode"
                css-class="rule-element"
                :data-source="categoryList"
                :fields="{ text: 'text', value: 'categoryCode' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择品类')"
              />
            </mt-form-item> -->
            <mt-form-item prop="rfxCode" :label="$t('招标编号')" label-style="top">
              <mt-input
                v-model="searchFormModel.rfxCode"
                :show-clear-button="true"
                :placeholder="$t('请输入招标编号')"
                @change="(val) => !val && (searchFormModel.rfxCode = null)"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-local-template-page>
  </div>
</template>

<script>
import { pageConfig, dialogPageConfig } from './config/index'
import magnifierInput from '@/components/magnifierInput'
// 引入本地的emplate-page组件
import MtLocalTemplatePage from '@/components/template-page'

export default {
  components: {
    magnifierInput,
    MtLocalTemplatePage
  },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      pageConfig: pageConfig(this),
      companyList: [],
      purchaseOrgList: [],
      factoryList: [],
      categoryList: [],
      // 物料放大镜弹窗配置
      materialDialogCofig: {
        pageConfig: dialogPageConfig(
          `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
          'material'
        ),
        text: 'itemCode',
        value: 'itemCode'
      },
      // 供应商放大镜弹窗配置
      supplierDialogCofig: {
        pageConfig: dialogPageConfig(
          '/masterDataManagement/tenant/supplier/pagedQueryByOrgCode',
          'supplier'
        ),
        text: 'supplierName',
        value: 'supplierCode'
      }
    }
  },
  created() {
    this.getSelectList()
  },
  methods: {
    getSelectList() {
      this.getCompanyList()
      this.getFactoryList()
      // this.getCategoryList()
    },
    // 获取公司下拉列表
    async getCompanyList() {
      const res = await this.$API.masterData.permissionCompanyList()
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.companyList = res.data
      }
    },
    // 获取采购组织下拉列表
    getPurchaseOrgList(companyId) {
      if (!companyId) {
        this.purchaseOrgList = []
        return
      }
      this.$API.masterData
        .permissionOrgList({
          orgId: companyId
        })
        .then((res) => {
          this.purchaseOrgList = res.data
        })
    },
    // 获取工厂下拉列表
    async getFactoryList() {
      const res = await this.$API.customization.getPermissionSiteList({})
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.factoryList = res.data
      }
    },
    async getCategoryList() {
      const res = await this.$API.masterData.getCategoryList()
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.categoryCode + '-' + item.categoryName
        })
        this.categoryList = res.data
      }
    },
    handleCompanyChange(e) {
      this.searchFormModel.purGroupCode = null
      if (e.itemData.id) {
        this.getPurchaseOrgList(e.itemData.id)
      } else {
        this.purchaseOrgList = []
      }
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.$refs.itemCode.handleClear()
      this.$refs.supplierCode.handleClear()
    },
    // 点击按钮工具栏
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const selectedRecords = grid.getSelectedRecords()
      if (selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请先至少选择一行数据'), type: 'warning' })
        return
      }
      if (toolbar.id === 'pass' || toolbar.id === 'reject') {
        // 审核
        this.handleAudit(toolbar.id, selectedRecords)
      }
    },
    // 点击单元格
    handleClickCellTitle(e) {
      if (e.field === 'infoRecord') {
        this.$dialog({
          modal: () => import('./components/infoRecord.vue'),
          data: {
            title: this.$t('信息记录'),
            biddingItemId: e.data?.id
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    },
    // 审核
    handleAudit(type, selectedRecords) {
      const operateMap = {
        pass: {
          title: this.$t('审核通过'),
          approvalStatus: 1
        },
        reject: {
          title: this.$t('审核驳回'),
          approvalStatus: -1
        }
      }
      const biddingItemIdList = []
      selectedRecords.forEach((item) => biddingItemIdList.push(item.id))

      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确定${operateMap[type]['title']}？`)
        },
        success: () => {
          this.$API.infoRecordConfirm
            .auditBiddingInfo({
              approvalStatus: operateMap[type]['approvalStatus'],
              biddingItemIdList
            })
            .then(() => {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
            })
            .catch(() => {
              // this.$toast({ content: err.msg || this.$t('未知错误，请重试'), type: 'error' })
            })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  /deep/.e-headercelldiv {
    padding: 0 0.6em 0 0.6em !important;
  }
}
</style>
