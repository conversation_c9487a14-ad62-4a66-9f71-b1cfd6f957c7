import { i18n } from '@/main.js'
import { getValueByPath } from '@/utils/obj'
import { Formatter } from '@/utils/ej/dataGrid/index'

export const columnData = [
  {
    field: 'syncStatus',
    headerText: i18n.t('价格记录状态'),
    formatter: ({ field }, item) => {
      const cellVal = getValueByPath(item, field)
      switch (Number(cellVal)) {
        case 0:
          return i18n.t('未同步')
        case 1:
          return i18n.t('已同步')
        default:
          return cellVal
      }
    }
  },
  {
    field: 'syncSapStatus',
    headerText: i18n.t('导入Sap状态'),
    formatter: ({ field }, item) => {
      const cellVal = getValueByPath(item, field)
      switch (Number(cellVal)) {
        case 0:
          return i18n.t('无需同步')
        case 1:
          return i18n.t('未同步')
        case 2:
          return i18n.t('同步中')
        case 3:
          return i18n.t('同步成功')
        case 4:
          return i18n.t('同步失败')
        case 5:
          return i18n.t('等待直送地惠州同步成功')
        default:
          return cellVal
      }
    }
  },
  {
    field: 'vendorCompleteFlag',
    headerText: i18n.t('供方报价状态'),
    formatter: ({ field }, item) => {
      const cellVal = getValueByPath(item, field)
      switch (Number(cellVal)) {
        case 0:
          return i18n.t('未报价')
        case 1:
          return i18n.t('已报价')
        default:
          return cellVal
      }
    }
  },
  {
    field: 'syncResultMsg',
    headerText: i18n.t('同步结果')
  },
  {
    field: 'approvalStatus',
    headerText: i18n.t('审核状态'),

    formatter: ({ field }, item) => {
      const cellVal = getValueByPath(item, field)
      switch (Number(cellVal)) {
        case 0:
          return i18n.t('待审核')
        case -1:
          return i18n.t('审核驳回')
        case 1:
          return i18n.t('采购员审核通过')
        case 2:
          return i18n.t('采购经理审核通过')
        default:
          return cellVal
      }
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'unitName',
    headerText: i18n.t('单位')
  },
  {
    field: 'priceUnitName',
    headerText: i18n.t('价格单位')
  },
  {
    field: 'purUnitName',
    headerText: i18n.t('订单单位名称')
  },
  {
    field: 'purUnitCode', // 订单单位编码
    headerText: i18n.t('订单单位编码')
  },
  {
    field: 'supplierItemCode',
    headerText: i18n.t('供应商物料编码')
  },
  {
    field: 'priceClassification',
    headerText: i18n.t('价格分类'),
    formatter: ({ field }, item) => {
      const cellVal = getValueByPath(item, field)
      switch (cellVal) {
        case 'predict_price':
          return i18n.t('暂估价格')
        case 'srm_price':
          return i18n.t('SRM价格')
        case 'execute_price':
          return i18n.t('执行价格')
        case 'basic_price':
          return i18n.t('基价')
        default:
          return cellVal
      }
    }
  },
  {
    field: 'conversionRate',
    headerText: i18n.t('转换率')
  },
  {
    field: 'currencyExchangeValue',
    headerText: i18n.t('汇率')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂')
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    field: 'purGroupName',
    headerText: i18n.t('采购组织')
  },
  {
    field: 'purGroupCode',
    headerText: i18n.t('采购组织编码')
  },
  {
    field: 'deliveryPlace',
    headerText: i18n.t('直送地')
  },
  {
    field: 'bidTaxRateValue',
    headerText: i18n.t('税率')
  },
  {
    field: 'bidCurrencyName',
    headerText: i18n.t('原币')
  },
  {
    field: 'bidCurrencyCode',
    headerText: i18n.t('原币编码')
  },
  {
    field: 'localCurrencyName',
    headerText: i18n.t('本币名称'),
    with: 200
  },
  {
    field: 'localCurrencyCode',
    with: 200,
    headerText: i18n.t('本币编码'),
    visible: false
  },
  {
    field: 'quoteAttribute',
    headerText: i18n.t('报价属性'),

    formatter: ({ field }, item) => {
      const cellVal = getValueByPath(item, field)
      switch (cellVal) {
        case 'standard_price':
          return i18n.t('标准价')
        case 'mailing_price':
          return i18n.t('寄售价')
        case 'outsource':
          return i18n.t('委外价')
        default:
          return cellVal
      }
    }
  },
  {
    field: 'quoteMode',
    headerText: i18n.t('价格生效方式'),
    formatter: ({ field }, item) => {
      const cellVal = getValueByPath(item, field)
      switch (cellVal) {
        case 'in_warehouse':
          return i18n.t('按入库')
        case 'out_warehouse':
          return i18n.t('按出库')
        case 'order_date':
          return i18n.t('按订单日期')
        default:
          return cellVal
      }
    }
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('单价（原币）')
  },
  {
    field: 'untaxedLocalUnitPrice',
    headerText: i18n.t('单价（本币）')
  },
  {
    field: 'quoteEffectiveStartDate',
    headerText: i18n.t('有效期从'),
    formatter: Formatter.createFmtDatetime('YYYY-MM-DD')
  },
  {
    field: 'quoteEffectiveEndDate',
    headerText: i18n.t('有效期至'),
    formatter: Formatter.createFmtDatetime('YYYY-MM-DD')
  },
  {
    field: 'leadTime',
    headerText: i18n.t('L/T')
  },
  {
    field: 'unconditionalLeadTime',
    headerText: i18n.t('无条件L/T')
  },
  {
    field: 'minPackageQuantity',
    headerText: i18n.t('最小包装数量')
  },
  {
    field: 'minPurQuantity',
    headerText: i18n.t('最小采购数量')
  },
  {
    field: 'directDestLocalPrice',
    headerText: i18n.t('直送地价格（本币）')
  },
  {
    field: 'directDestOriginalPrice',
    headerText: i18n.t('直送地价格（原币）')
  }
]

export const pageConfig = (that) => {
  return [
    {
      useToolTemplate: false,
      toolbar: {
        useBaseConfig: false,
        tools: [[], ['Refresh', 'Setting']]
      },
      gridId: '615d5485-fbd7-4c4c-8808-29b5aec271a1',
      grid: {
        lineIndex: true,
        columnData,
        asyncConfig: {
          url: that.$API.infoRecordConfirm.getInfoRecordListUrl,
          params: {
            biddingItemId: that.modalData.biddingItemId
          },
          recordsPosition: 'data.infoAppendDTOList'
        }
      }
    }
  ]
}
