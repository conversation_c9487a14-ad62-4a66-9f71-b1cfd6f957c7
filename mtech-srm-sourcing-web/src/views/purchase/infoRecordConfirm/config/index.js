import { i18n } from '@/main.js'

export const columnData = [
  {
    width: 50,
    type: 'checkbox'
  },
  {
    field: 'infoRecord',
    headerText: i18n.t('信息记录'),
    cssClass: 'field-content',
    width: 100
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司')
  },
  {
    field: 'purGroupName',
    headerText: i18n.t('采购组织')
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂编码'),
    width: 100
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    field: 'rfxCode',
    headerText: i18n.t('招标编号')
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('单价（未税）'),
    width: 140
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('单价（含税）'),
    width: 140
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    width: 120
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    width: 100
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]

export const pageConfig = (that) => {
  return [
    {
      title: i18n.t('未审核'),
      isUseCustomSearch: true,
      isCustomSearchRules: true,
      useToolTemplate: false,
      toolbar: {
        useBaseConfig: false,
        tools: [
          [
            { id: 'pass', icon: 'icon_solid_edit', title: i18n.t('审核通过') },
            { id: 'reject', icon: 'icon_solid_edit', title: i18n.t('审核驳回') }
          ],
          ['Refresh', 'Setting']
        ]
      },
      gridId: '7748f6c0-0b09-4b91-8566-2126208f9ce8',
      grid: {
        lineIndex: true,
        columnData,
        asyncConfig: {
          url: that.$API.infoRecordConfirm.getBiddingInfoListUrl,
          params: {
            approvalStatus: -1
          },
          serializeList: (list) => {
            list.forEach((item) => {
              item.infoRecord = i18n.t('信息记录')
            })
            return list
          }
        }
      }
    },
    {
      title: i18n.t('已审核'),
      isUseCustomSearch: true,
      isCustomSearchRules: true,
      useToolTemplate: false,
      toolbar: {
        useBaseConfig: false,
        tools: [[], ['Refresh', 'Setting']]
      },
      gridId: '8bf58b2f-d886-4faf-a10e-c5ac3138026e',
      grid: {
        lineIndex: true,
        columnData,
        asyncConfig: {
          url: that.$API.infoRecordConfirm.getBiddingInfoListUrl,
          params: {
            approvalStatus: 1
          },
          serializeList: (list) => {
            list.forEach((item) => {
              item.infoRecord = i18n.t('信息记录')
            })
            return list
          }
        }
      }
    }
  ]
}

const materialColumn = [
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  }
]
const supplierColumn = [
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  }
]
export const dialogPageConfig = (url, type, params) => {
  const gridId = {
    material: 'ba659921-eb09-46bf-a091-77a913fbee34',
    supplier: '311edd94-5756-4932-bcb7-3699e2e3d8d8'
  }
  const column = {
    material: materialColumn,
    supplier: supplierColumn
  }
  return [
    {
      gridId: gridId[type],
      useToolTemplate: false,
      toolbar: [],
      grid: {
        allowFiltering: true,
        columnData: column[type],
        allowSorting: false,
        allowSelection: true,
        selectionSettings: {
          type: 'Multiple',
          mode: 'Row'
        },
        asyncConfig: { url, params }
      }
    }
  ]
}
