import { i18n } from '@/main.js'
import Vue from 'vue'
// const toolbar = [{ id: "Add", icon: "icon_solid_Createorder", title: i18n.t("新增") }];
const columnData = [
  {
    field: 'levelCode',
    headerText: i18n.t('层级编码'),
    cellTools: [
      {
        id: 'add',
        icon: 'icon_solid_Createorder',
        title: i18n.t('新增')
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除')
      }
    ]
  },
  {
    field: 'levelName',
    headerText: i18n.t('层级名称')
  },
  {
    field: 'levelSort',
    headerText: i18n.t('层级')
  },
  {
    field: 'needQuote',
    headerText: i18n.t('是否报价项'),
    // valueConverter: {
    //   type: "map",
    //   map: { 0: i18n.t("是"), 1: i18n.t("否") },
    // },
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<span>{{data.needQuote>0?'是':'否'}}</span>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'requiredQuote',
    headerText: i18n.t('是否必须报价'),
    // valueConverter: {
    //   type: "map",
    //   map: { 0: i18n.t("是"), 1: i18n.t("否") },
    // },
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<span>{{data.needQuote>0?'是':'否'}}</span>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'calculationFormula',
    headerText: i18n.t('计算公式')
  }
]
export const pageConfig = [
  {
    useToolTemplate: false,
    treeGrid: { allowFiltering: true, columnData, dataSource: [] }
  }
]

const supplierColumnData = [
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    width: 70
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: 150
  },
  {
    field: 'untaxedTotalPrice',
    headerText: i18n.t('总价（未税）'),
    width: 100
  },
  {
    field: 'taxedTotalPrice',
    headerText: i18n.t('总价（含税）'),
    width: 100
  },
  {
    field: 'totalRankNum',
    headerText: i18n.t('排名'),
    width: 80,
    formatter: function ({ field }, item) {
      const cellVal = item[field]
      return cellVal == -999 || cellVal == -99 ? '**' : cellVal
    }
  }
]

export const supplierPageConfig = [
  {
    useToolTemplate: false,
    gridId: '9633a2df-1416-4b8a-9990-deecd84e1e58',
    grid: {
      columnData: supplierColumnData,
      allowPaging: false
    }
  }
]
