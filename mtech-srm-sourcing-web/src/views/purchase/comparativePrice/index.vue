<template>
  <div class="comparative-price">
    <mt-tabs
      :tab-id="$utils.randomString()"
      :e-tab="false"
      :data-source="dataSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <div v-if="activated == 0">
      <Assistant
        :rfx-id="rfxId"
        :sourcing-obj-type="sourcingObjType"
        :current-round="currentRound"
      ></Assistant>
    </div>
    <div v-else-if="activated == 1">
      <QuotationProcess :rfx-id="rfxId"></QuotationProcess>
    </div>
    <div v-else-if="activated == 2">
      <HistoryPriceAnalysis :rfx-id="rfxId"></HistoryPriceAnalysis>
    </div>
  </div>
</template>
<script>
import Assistant from './tabs/assistant.vue'
import HistoryPriceAnalysis from './tabs/historyPriceAnalysis.vue'
import QuotationProcess from './tabs/quotationProcess.vue'
export default {
  components: {
    Assistant,
    HistoryPriceAnalysis,
    QuotationProcess
  },
  props: {
    rfxId: {
      type: String,
      default: ''
    },
    sourcingObjType: {
      type: String,
      default: ''
    },
    currentRound: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      dataSource: [
        {
          title: this.$t('比价助手')
        },
        {
          title: this.$t('本次报价过程')
        },
        {
          title: this.$t('历史价格分析')
        }
      ],
      activated: 0
    }
  },
  methods: {
    handleSelectTab(item) {
      this.activated = item
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .mt-tabs-container {
  background: transparent;
}
.comparative-price {
}
</style>
