import { i18n } from '@/main.js'
export const columnData1 = [
  { field: 'td1', headerText: i18n.t('物料信息'), width: '300' },
  { field: 'td2', headerText: i18n.t('报价信息'), width: '200' },
  { field: 'td3', headerText: i18n.t('上海科技有限公司') }
]

export const columnData2 = [
  { field: 'td1', headerText: i18n.t('物料信息'), width: '200' },
  { field: 'td2', headerText: i18n.t('报价信息'), width: '180' },
  { field: 'td3', headerText: i18n.t('阶梯数量'), width: '150' },
  { field: 'td4', headerText: i18n.t('') }
]
