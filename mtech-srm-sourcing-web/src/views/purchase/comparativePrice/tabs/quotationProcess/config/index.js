import { i18n } from '@/main.js'

const columnData = [
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'createTime',
    headerText: i18n.t('报价时间')
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('价格(未税)')
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('价格(含税)')
  },
  {
    field: 'priceType',
    headerText: i18n.t('报价节点'),
    valueConverter: {
      type: 'map', //(map为key/value对象)：此时，fields可不传。
      map: { 0: i18n.t('报价'), 1: i18n.t('议价') }
    }
  },
  {
    field: 'roundNo',
    headerText: i18n.t('轮次')
  }
]

export const pageConfig = (rfxId) => [
  {
    toolbar: [[], ['Filter', 'Refresh']],
    useToolTemplate: false,
    grid: {
      allowFiltering: true,
      columnData,
      dataSource: [],
      asyncConfig: {
        url: 'sourcing/tenant/comp/queryCurPrice',
        queryBuilderWrap: 'queryBuilderDTO',
        params: { rfxId: rfxId, itemCodes: [] },
        recordsPosition: 'data.records'
      }
    }
  }
]
