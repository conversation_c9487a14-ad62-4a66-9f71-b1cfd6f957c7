<template>
  <div class="totalPrice">
    <div class="totalPrice-box">
      <div class="left">
        <div :style="{ height: '400px', width: '90%', margin: '0 auto' }" ref="myEchart2"></div>
      </div>
    </div>
    <div class="bottom">
      <mt-template-page ref="templateRef" style="height: 300px" :template-config="pageConfig" />
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { pageConfig } from './config'
export default {
  data() {
    return {
      rfxId: 111,
      dataArr1: [],
      modelarr1: '',
      pageConfig: pageConfig(111)
    }
  },
  created() {
    this.queryCurPriceTable()
  },
  mounted() {},
  methods: {
    muCharts(e) {
      let myChart = echarts.init(this.$refs.myEchart2) //这里是为了获得容器所在位置
      myChart.setOption({
        tooltip: {
          trigger: 'axis',
          position: function (pt) {
            return [pt[0], '10%']
          }
        },
        xAxis: {
          type: 'time',
          boundaryGap: false,
          axisLabel: {
            interale: 0,
            rotate: 0, //设置日期显示样式（倾斜度）
            formatter: function (value, index) {
              //在这里写你需要的时间格式
              var t_date = new Date(value)
              if (index % 2 == 0) {
                return ''
              }
              return (
                [t_date.getFullYear(), t_date.getMonth() + 1, t_date.getDate()].join('-') +
                ' ' +
                [t_date.getHours(), t_date.getMinutes()].join(':')
              )
              // + " " + [t_date.getHours(), t_date.getMinutes()].join(':'); 时分
            }
          }
        },
        yAxis: {
          type: 'value',
          boundaryGap: [0, '100%']
        },
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100
          },
          {
            start: 0,
            end: 10
          }
        ],
        series: e.series || [
          {
            name: 'Fake Data',
            type: 'line',
            smooth: true,
            areaStyle: {},
            data: [[new Date(parseInt('1645096221000')), 1]]
          }
        ]
      })

      //让图表自适应
      window.addEventListener('resize', function () {
        myChart.resize() // myChart 是实例对象
      })
    },
    change(e) {
      this.pageConfig[0].grid.asyncConfig.params.itemCodes = [e.value]
    },
    queryCurPriceTable() {
      this.$API.comparativePrice.queryCurPriceTable({ rfxId: this.rfxId }).then((r) => {
        // this.dataArr1 = r.data;
        let chartData = {
          xAxis: [], // 横坐标数组
          legend: [], // 线段信息
          series: []
        }
        r.data.forEach((element) => {
          let x = []
          element.rfxBiddingItemDTOList.forEach((e) => {
            // chartData.xAxis.push([e.createTime,new Date(parseInt(e.createTime))]);
            x.push([new Date(parseInt(e.createTime)), e.taxedTotalPrice])
          })
          chartData.series.push({
            name: element.supplierName,
            type: 'line',
            smooth: true,
            data: x
          })
          chartData.legend.push(element.supplierName)
        })
        this.muCharts(chartData)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.totalPrice {
  background: #fafafa;
  .totalPrice-box {
    display: flex;
    .left {
      background: #fff;
      width: 100%;
      min-width: 600px;
      padding: 18px;
    }
  }
  .bottom {
    width: 100%;
    min-height: 300px;
  }
  .top1 {
    display: flex;
    justify-content: space-between;
    margin-top: 18px;
  }
  .serch {
    width: 260px;
    .mt-input-group {
    }
  }
  .tags {
    height: 14px;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
    position: relative;
    padding-left: 8px;
    &:before {
      content: '';
      position: absolute;
      width: 3px;
      height: 14px;
      background: #00469c;
      border-radius: 2px;
      left: 0;
    }
  }
}
</style>
