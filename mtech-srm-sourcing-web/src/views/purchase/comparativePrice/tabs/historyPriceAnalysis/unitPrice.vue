<template>
  <div class="unitPrice">
    <div class="unitPrice-box">
      <div class="left">
        <div class="top1">
          <div class="serch">
            <div class="mt-input-group">
              <mt-select
                :width="260"
                :data-source="dataArr1"
                :fields="{ value: 'itemCode', text: 'itemName' }"
                v-model="itemCode"
                @change="change1"
                :open-dispatch-change="false"
                :allow-filtering="true"
                :placeholder="$t('搜索物品')"
              ></mt-select>
              <mt-select
                :width="260"
                :data-source="dataArr4"
                v-model="deliveryPlace"
                @change="change4"
                :open-dispatch-change="false"
                :allow-filtering="true"
                :placeholder="$t('直送地')"
              ></mt-select>
            </div>
            <div class="serch-right">
              <mt-select
                :width="100"
                :data-source="dataArr2"
                v-model="isUntaxedPrice"
                :open-dispatch-change="false"
                @change="change2"
              ></mt-select>
              <mt-select
                style="margin-left: 20px"
                :width="100"
                :data-source="dataArr3"
                :open-dispatch-change="false"
                v-model="timeSection"
                @change="change3"
              ></mt-select>
            </div>
          </div>
          <div></div>
        </div>
        <div>
          <div :style="{ height: '400px', width: '90%', margin: '0 auto' }" ref="myEchart"></div>
        </div>
      </div>
      <div class="right">
        <div class="tags">{{ $t('标记物信息') }}</div>
        <div v-for="(item, i) in dataArr1" :key="i">
          <div v-if="item.itemCode === itemCode">
            <div class="right-title">{{ item.itemName }}</div>
            <div class="right-text">
              <span style="color: #292929"> {{ $t('标记物编码：') }}</span
              ><span style="color: #747474" v-if="item.itemCode">{{ item.itemCode }}</span>
              <span style="color: #747474" v-if="!item.itemCode">{{ item.temporaryItemCode }}</span>
            </div>
            <div class="right-text">
              <span style="color: #292929"> {{ $t('标记物id：') }}</span
              ><span style="color: #747474">{{ item.itemId }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import utils from '@/utils/utils'
export default {
  props: {
    rfxId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dataArr1: [],
      dataArr4: [],
      dataArr2: [
        { text: this.$t('未税价'), value: '0' },
        { text: this.$t('已税价'), value: '1' }
      ],
      dataArr3: [
        { text: this.$t('全部时间'), value: '0' },
        { text: this.$t('近一年'), value: '1' },
        { text: this.$t('近三个月'), value: '2' }
      ],
      itemCode: '',
      deliveryPlace: '',
      isUntaxedPrice: '0',
      timeSection: '0'
    }
  },
  created() {},
  async mounted() {
    await this.queryDistinctItem()
    await this.queryDeliveryPlaces()
    this.queryHisPrice()
  },
  methods: {
    muCharts(e) {
      let myChart = echarts.init(this.$refs.myEchart) //这里是为了获得容器所在位置
      myChart.setOption({
        tooltip: {
          trigger: 'item',
          position: function (pt) {
            return [pt[0], '10%']
          }
        },
        xAxis: {
          type: 'time',
          boundaryGap: false,
          axisLabel: {
            interale: 0,
            rotate: 0, //设置日期显示样式（倾斜度）
            formatter: function (value, index) {
              //在这里写你需要的时间格式
              var t_date = new Date(value)
              if (index % 2 == 0) {
                return ''
              }
              return utils.formatTime(t_date, 'YYYY-mm-dd HH:MM:SS')
              // + " " + [t_date.getHours(), t_date.getMinutes()].join(':'); 时分
            }
          }
        },
        yAxis: {
          type: 'value',
          name: this.$t('元')
        },
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100
          },
          {
            start: 0,
            end: 10
          }
        ],
        series: e.series || [
          {
            name: 'Fake Data',
            type: 'line',
            smooth: true,
            areaStyle: {},
            data: [[new Date(parseInt('1645096221000')), 1]]
          }
        ]
      })

      //让图表自适应
      window.addEventListener('resize', function () {
        myChart.resize() // myChart 是实例对象
      })
    },
    change1(e) {
      this.itemCode = e.value
      this.queryHisPrice()
    },
    change2(e) {
      this.isUntaxedPrice = e.value
      this.queryHisPrice()
    },
    change3(e) {
      this.timeSection = e.value
      this.queryHisPrice()
    },
    change4(e) {
      this.deliveryPlace = e.value
      this.queryHisPrice()
    },
    queryDistinctItem() {
      this.$API.comparativePrice.queryDistinctItem({ rfxId: this.rfxId }).then((r) => {
        this.dataArr1 = r.data
        this.itemCode = r.data[0].itemCode
      })
    },
    // 获取直送地下拉数据
    async queryDeliveryPlaces() {
      await this.$API.comparativePrice.queryDeliveryPlaces({ rfxId: this.rfxId }).then((r) => {
        let _data = [...new Set(r.data)]
        const d = _data.map((i) => {
          return {
            text: i,
            value: i
          }
        })
        this.dataArr4 = d
        let _find = r.data.find((item) => !['青白江', '成都', '内蒙'].includes(item))
        this.deliveryPlace = _find
      })
    },
    async queryHisPrice() {
      await this.$API.comparativePrice
        .queryHisPrice({
          rfxId: this.rfxId,
          isUntaxedPrice: parseInt(this.isUntaxedPrice),
          timeSection: parseInt(this.timeSection),
          itemCode: this.itemCode,
          deliveryPlaces: [this.deliveryPlace]
        })
        .then((r) => {
          // this.dataArr1 = r.data;
          let chartData = {
            xAxis: [], // 横坐标数组
            legend: [], // 线段信息
            series: []
          }
          r.data.forEach((element) => {
            let x = []
            element.trendList.forEach((e) => {
              // chartData.xAxis.push([e.createTime,new Date(parseInt(e.createTime))]);
              x.push([new Date(parseInt(e.date)), e.unitPrice])
            })
            chartData.series.push({
              name: element.supplierName,
              type: 'line',
              smooth: true,
              data: x
            })
            chartData.legend.push(element.supplierName)
          })
          // chartData.xAxis = chartData.xAxis.filter(function (
          //   value,
          //   index,
          //   self
          // ) {
          //   return self.indexOf(value) === index;
          // });
          console.log('chartData', chartData)
          this.muCharts(chartData)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.unitPrice {
  background: #fafafa;
  .unitPrice-box {
    display: flex;
    .left {
      background: #fff;
      width: 80%;
      min-width: 600px;
      padding: 18px;
    }
    .right {
      width: 350px;
      margin-left: 20px;
      background: #fff;
      padding: 30px;
      max-height: 690px;
      overflow: auto;
      .right-title {
        margin: 30px 0 20px 0;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 600;
        color: rgba(0, 70, 156, 1);
      }
      .right-text {
        margin: 6px 0;
      }
    }
  }
  .bottom {
    width: 100%;
    min-height: 300px;
  }
  .top1 {
    display: flex;
    justify-content: space-between;
    margin-top: 18px;
  }
  .serch {
    width: 100%;
    display: flex;
    justify-content: space-between;
    .mt-input-group {
      .mt-select {
        margin-right: 30px;
      }
    }
  }
  .tags {
    height: 14px;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
    position: relative;
    padding-left: 8px;
    &:before {
      content: '';
      position: absolute;
      width: 3px;
      height: 14px;
      background: #00469c;
      border-radius: 2px;
      left: 0;
    }
  }
}
</style>
