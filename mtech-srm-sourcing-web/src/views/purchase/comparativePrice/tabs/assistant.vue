<template>
  <div class="CP-assistant">
    <div class="supplier-content">
      <mt-template-page ref="templateRef" :template-config="supplierPageConfig" />
    </div>

    <mt-row :gutter="5" v-if="priceInfo.length >= 1">
      <!-- <mt-col :sm="8">
        <div class="grid-content bg-purple">
          <p class="title">
            {{ priceInfo[0].supplierName || '' }}
            <span class="tag-b">{{ $t('最高报价供应商') }}</span>
          </p>
          <p class="number bule">{{ priceInfo[0].untaxedTotalPrice }}</p>
          <p class="money">{{ $t('最高报价总价(未税)：元') }}</p>
        </div>
      </mt-col>
      <mt-col :sm="8">
        <div class="grid-content bg-purple-light">
          <p class="title">
            {{ priceInfo[1].supplierName || '' }}
            <span class="tag-g">{{ $t('最低报价供应商') }}</span>
          </p>
          <p class="number green">{{ priceInfo[1].untaxedTotalPrice }}</p>
          <p class="money">{{ $t('最低报价总价(未税)：元') }}</p>
        </div>
      </mt-col> -->
      <mt-col>
        <mt-form ref="ruleForm" class="assistantForm" style="display: flex; margin-top: 20px">
          <mt-form-item
            class="form-item"
            :label="$t('筛选标的物')"
            label-width="200"
            label-align="right"
            label-style="top"
          >
            <mt-multi-select
              :width="290"
              :data-source="dataArr1"
              v-model="modelarr1"
              :allow-filtering="true"
              :show-clear-button="true"
              :placeholder="$t('请选择')"
              :fields="{ value: 'itemCode', text: 'itemName' }"
              @change="change"
              style="margin-right: 40px"
            ></mt-multi-select
          ></mt-form-item>

          <mt-form-item
            class="form-item"
            :label="$t('筛选供应商')"
            label-width="200"
            label-align="right"
            label-style="top"
          >
            <mt-multi-select
              :width="290"
              :allow-filtering="true"
              v-model="modelarr2"
              :data-source="dataArr2"
              :show-clear-button="true"
              @change="change"
              :fields="{ value: 'supplierId', text: 'supplierName' }"
              :placeholder="$t('请选择')"
            ></mt-multi-select
          ></mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('直送地')"
            label-width="200"
            label-align="right"
            label-style="top"
          >
            <mt-multi-select
              :width="290"
              :allow-filtering="true"
              v-model="modelarr3"
              :data-source="dataArr3"
              :show-clear-button="true"
              @change="change"
              :placeholder="$t('请选择')"
            ></mt-multi-select
          ></mt-form-item>
          <mt-form-item
            class="form-item"
            :label="$t('轮次')"
            label-width="200"
            label-align="right"
            label-style="top"
          >
            <mt-select
              :width="290"
              v-model="roundNo"
              :data-source="dataArr4"
              :show-clear-button="true"
              @change="roundChange"
              :placeholder="$t('请选择')"
            ></mt-select
          ></mt-form-item>
        </mt-form>
      </mt-col>
    </mt-row>
    <div class="template-page">
      <mt-button
        type="text"
        icon-css="mt-icons mt-icon-icon_solid_Createorder"
        @click="exportTable"
        >{{ $t('导出') }}</mt-button
      >
      <!-- 此处待改造，多物料加载卡顿 -->
      <div v-for="(item, index) in gridArr" :key="'table-' + index" class="gridStyle">
        <mt-data-grid
          :ref="'tab-grid-' + index"
          :gird-lines="'Both'"
          :filter-settings="filterSettings"
          :allow-filtering="true"
          :allow-text-wrap="true"
          :data-source="item.dataSource"
          :column-data="item.columnData"
          :allow-selection="false"
          :enable-hover="false"
          :allow-paging="false"
          :query-cell-info="queryCellInfoEvent"
        ></mt-data-grid>
      </div>
    </div>
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import { pageConfig, supplierPageConfig } from '../config'
import { dataSource1 } from './data-source-base'
import { columnData1, columnData2 } from './columns-base'
import { stepPriceFieldArr } from '@/views/common/columnData/constant'
import Vue from 'vue'
export default {
  props: {
    rfxId: {
      type: String,
      default: ''
    },
    sourcingObjType: {
      type: String,
      default: ''
    },
    currentRound: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      pageConfig: pageConfig,
      supplierPageConfig: supplierPageConfig,
      priceInfo: [],
      filterSettings: { type: 'CheckBox' },
      gridArr: [
        {
          dataSource: dataSource1,
          columnData: columnData1
        }
      ],
      dataArr1: [],
      dataArr2: [],
      dataArr3: [],
      dataArr4: [
        { text: '1', value: 1 },
        { text: '2', value: 2 },
        { text: '3', value: 3 }
      ],
      modelarr1: [],
      modelarr2: [],
      modelarr3: [],
      roundNo: 1,
      priceInfoArr: []
    }
  },
  created() {
    this.queryTotalPrice()
    this.queryDistinctSupplier()
    this.queryDistinctItem()
    this.queryDeliveryPlaces()
    this.priceInfoFunc()
  },
  mounted() {
    this.roundNo = this.currentRound
  },
  methods: {
    getInfo(roundNo) {
      this.$API.comparativePrice
        .queryComp({
          rfxId: this.rfxId,
          itemCodes: this.modelarr1,
          supplierIds: this.modelarr2,
          deliveryPlaces: this.modelarr3,
          roundNo: roundNo || this.roundNo
        })
        .then((r) => {
          // 物料循环
          r.data?.forEach((e, i) => {
            let isStepQuote = e.stepQuote ? true : false
            let dataSource = []
            let columnData = isStepQuote
              ? JSON.parse(JSON.stringify(columnData2))
              : JSON.parse(JSON.stringify(columnData1))
            let _columnIndex = isStepQuote ? 3 : 2

            this.priceInfoArr.forEach((p) => {
              if (isStepQuote) {
                //阶梯报价添加阶梯数据
                dataSource.push({
                  td2: p.fieldName,
                  td3: stepPriceFieldArr.includes(p.fieldCode) ? 'isStep' : null
                })
              } else {
                dataSource.push({
                  td2: p.fieldName
                })
              }
            })
            this.gridArr[i] = {
              dataSource: JSON.parse(JSON.stringify(dataSource)),
              columnData: JSON.parse(JSON.stringify(columnData))
            }
            // 如果是阶梯报价 展示阶梯数量
            if (isStepQuote) {
              let _stepNumArr = e.rfxBiddingItems[0]?.itemStageList
              columnData[2].template = function () {
                return {
                  template: Vue.component('stepNumTemplate', {
                    template: `<div class="cell-boxs">
                        <div v-if="isStepField">
                           <p v-for="(item,index) in dataSource" :key="index">{{item['startValue']}}</p>
                        </div>
                        <div class="field" v-else></div>
                      </div>`,
                    data() {
                      return {
                        data: {},
                        dataSource: _stepNumArr
                      }
                    },
                    computed: {
                      isStepField() {
                        return isStepQuote && this.data.td3 === 'isStep'
                      }
                    }
                  })
                }
              }
            }

            //供应商循环
            e.rfxBiddingItems.forEach((c, j) => {
              columnData[j + _columnIndex] = {
                headerText: c.supplierName,
                field: `td${j + _columnIndex + 1}`,
                template: function () {
                  return {
                    template: Vue.component('common', {
                      template: `<div class="cell-boxs">
                        <div v-if="isStepField">
                             <p v-for="(item,index) in list" :key="index">{{item[field] || '-'}}</p>
                        </div>
                        <div v-else class="field">{{field}}</div>
                      </div>`,
                      data() {
                        return {
                          data: {},
                          list: []
                        }
                      },
                      computed: {
                        field() {
                          let _index = j + _columnIndex + 1
                          let td = 'td' + _index
                          return this.data[td]
                        },
                        isStepField() {
                          return isStepQuote && this.data.td3 === 'isStep'
                        }
                      },
                      created() {
                        this.list = c.itemStageList
                      }
                    })
                  }
                }
              }
              // 添加供应商数据源
              this.priceInfoArr.forEach((p, k) => {
                switch (c[p.fieldCode]) {
                  case 'mailing_price':
                    dataSource[k][`td${j + _columnIndex + 1}`] = this.$t('寄售价')
                    break
                  case 'standard_price':
                    dataSource[k][`td${j + _columnIndex + 1}`] = this.$t('标准价')
                    break
                  case 'outsource':
                    dataSource[k][`td${j + _columnIndex + 1}`] = this.$t('委外价')
                    break
                  case 'in_warehouse':
                    dataSource[k][`td${j + _columnIndex + 1}`] = this.$t('按照入库')
                    break
                  case 'out_warehouse':
                    dataSource[k][`td${j + _columnIndex + 1}`] = this.$t('按出库')
                    break
                  case 'order_date':
                    dataSource[k][`td${j + _columnIndex + 1}`] = this.$t('按订单日期')
                    break
                  default:
                    dataSource[k][`td${j + _columnIndex + 1}`] =
                      isStepQuote && stepPriceFieldArr.includes(p.fieldCode)
                        ? p.fieldCode
                        : c[p.fieldCode]
                    break
                }
                // dataSource[k].td2 = p.fieldCode;
              })
            })
            if (dataSource.length > 1) {
              dataSource[0].td1 = `
              ${this.$t('物料编码：')}
              ${e.itemCode}，
              ${this.$t('物料名称：')}
              ${e.itemName}，
              ${this.$t('直送地：')}
              ${e.deliveryPlace || ''}，
              ${this.$t('小计：')}`
              dataSource[1].isSecondGroupField = true
              dataSource[0].isFirstGroupField = true
            }
            let grid = {
              columnData: columnData,
              dataSource: JSON.parse(JSON.stringify(dataSource))
            }
            this.gridArr.splice(i, 1, grid)
          })
        })
    },

    // 查询最高报价和最低报价
    queryTotalPrice() {
      this.$API.comparativePrice.queryTotalPriceRanking({ id: this.rfxId, isComp: 1 }).then((r) => {
        this.priceInfo = r.data
        this.$set(this.supplierPageConfig[0].grid, 'dataSource', [...this.priceInfo])
      })
    },
    // 获取供应商下拉数据
    queryDistinctSupplier() {
      this.$API.comparativePrice.queryDistinctSupplier({ rfxId: this.rfxId }).then((r) => {
        this.dataArr2 = r.data
      })
    },
    // 获取标的物下拉数据
    queryDistinctItem() {
      this.$API.comparativePrice.queryDistinctItem({ rfxId: this.rfxId }).then((r) => {
        this.dataArr1 = r.data
      })
    },
    // 获取标直送地下拉数据
    queryDeliveryPlaces() {
      this.$API.comparativePrice.queryDeliveryPlaces({ rfxId: this.rfxId }).then((r) => {
        let _data = [...new Set(r.data)]
        const d = _data.map((i) => {
          return {
            text: i,
            value: i
          }
        })
        this.dataArr3 = d
      })
    },
    priceInfoFunc() {
      this.$API.comparativePrice
        .priceInfo({ rfxId: this.rfxId, docType: 'rfx', needStepQuote: 'Y' })
        .then((r) => {
          let _data = [...r.data]
          if (this.sourcingObjType !== 'HIERARCHY') {
            //非部品模具 过滤掉分摊价格信息
            _data = _data.filter((item) => item.fieldCode !== 'sharePriceUntaxed')
          }
          this.priceInfoArr = _data
          this.getInfo()
        })
    },
    change() {
      this.gridArr = []
      this.getInfo()
    },
    roundChange(e) {
      this.gridArr = []
      this.getInfo(e.value)
    },
    queryCellInfoEvent(args) {
      if (args.column.field == 'td1') {
        let _data = args.data
        if (_data?.isFirstGroupField) {
          args.rowSpan = this.gridArr[0].dataSource.length
        }
      }
    },
    handleChangeCellCheckBox() {},
    exportTable() {
      let _param = {
        rfxId: this.rfxId,
        itemCodes: this.modelarr1,
        supplierIds: this.modelarr2,
        roundNo: this.roundNo
      }
      this.$API.comparativePrice.queryCompExport(_param).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>
<style lang="scss">
.cell-boxs {
  width: calc(100% + 20px);
  margin-left: -10px;
  margin-right: -10px;
  p {
    border-left: 1px solid #e0e0e0;
    border-bottom: 1px solid #e0e0e0;
    padding-left: 10px;
    margin-bottom: 0 !important;
    &:last-child {
      border-bottom: none;
    }
  }
  .field {
    min-height: 18px;
    padding-left: 10px;
    border-left: 1px solid #e0e0e0;
  }
}
</style>
<style lang="scss" scoped>
.supplier-content {
  width: 800px;
}
/deep/ .supplier-content .e-content {
  min-height: 100px;
}
/deep/ .e-grid td.e-rowcell:first-of-type {
  padding-left: 10px !important;
}
/deep/ .e-grid .e-dialog.e-checkboxfilter {
  height: 322px;
}
/deep/.e-frozenheader > .e-table,
.e-frozencontent > .e-table {
  border-right: 0 !important;
  box-shadow: 1px 0px 5px 0 #e8e8e8 !important;
  position: relative;
}
/deep/.e-grid .e-frozencontent > .e-table {
  border-right: 0 !important;
  box-shadow: 1px 0px 5px 0 #e8e8e8 !important;
  position: relative;
}
.gridStyle
  /deep/.e-grid:not(.e-row-responsive)
  .e-gridcontent
  tr.e-row:first-child
  .e-rowcell:first-child {
  border-right: 1px solid #ccc !important;
}

.e-emptyrow {
  display: none;
}
.e-movablecontent {
  td.e-active:first-of-type:before {
    display: none;
  }
}
.CP-assistant {
  .title {
    color: #292929;
    font-size: 20px;
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    margin-top: 20px;
    .tag-b {
      font-size: 12px;
      background: #eff2f8;
      height: 20px;
      border-radius: 2px;
      color: #6386c1;
      padding: 0 4px;
      line-height: 20px;
      margin-left: 14px;
    }
    .tag-g {
      font-size: 12px;
      background: #f3f9eb;
      height: 20px;
      border-radius: 2px;
      color: #8acc40;
      padding: 0 4px;
      line-height: 20px;
      margin-left: 14px;
    }
  }
  .number {
    font-size: 22px;
    margin: 20px 0 8px 0;
  }
  .bule {
    color: #00469c;
  }
  .green {
    color: #8acc40;
  }
  .money {
    color: #646464;
    font-size: 14px;
  }
  .template-page {
    width: 100%;
    margin-top: 20px;
    .gridStyle {
      width: 100%;
      margin: 15px 0;
    }
    .title-1 {
      width: 100%;
      height: 74px;
      background: rgba(240, 243, 249, 1);
      border: 1px solid rgba(232, 232, 232, 1);
      border-radius: 6px 6px 0 0;
    }
  }
  .tags {
    height: 14px;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
    position: relative;
    padding-left: 8px;
    &:before {
      content: '';
      position: absolute;
      width: 3px;
      height: 14px;
      background: #00469c;
      border-radius: 2px;
      left: 0;
    }
  }
}
</style>
