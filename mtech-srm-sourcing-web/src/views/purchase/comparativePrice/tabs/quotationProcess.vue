<template>
  <div class="quotationProcess">
    <div class="title">
      <div class="tags">{{ $t('标的物报价记录') }}</div>
      <!-- <div class="choose-div">
        <div
          class="choose-un"
          :class="activeId === item.id ? 'choose-active' : ''"
          v-for="(item, i) in chooseArr"
          :key="i"
          @click="clickChoose(item)"
        >
          {{ item.text }}
        </div>
      </div> -->
    </div>

    <div v-if="activeId == 0">
      <UnitPrice :rfx-id="rfxId"></UnitPrice>
    </div>
    <!-- <div v-else>
      <TotalPrice></TotalPrice>
    </div> -->
  </div>
</template>

<script>
import UnitPrice from './quotationProcess/unitPrice.vue'
// import TotalPrice from "./quotationProcess/totalPrice.vue";
export default {
  components: {
    UnitPrice
    // TotalPrice,
  },
  props: {
    rfxId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      activeId: 0,
      chooseArr: [
        { id: 0, text: this.$t('单价') },
        { id: 1, text: this.$t('总价(含税)') }
      ]
    }
  },
  created() {},
  methods: {
    clickChoose(item) {
      this.activeId = item.id
    }
  }
}
</script>

<style lang="scss">
.quotationProcess {
  .title {
    display: flex;
    justify-content: space-between;
    width: 80%;
    min-width: 600px;
    align-items: center;
    padding: 18px 80px 0 18px;
    .choose-div {
      display: flex;
      cursor: pointer;
      .choose-un {
        width: 60px;
        height: 22px;
        background: rgba(255, 255, 255, 1);
        border: 1px solid rgba(232, 232, 232, 1);
        font-size: 12px;
        text-align: center;
        line-height: 22px;
      }
      .choose-un:nth-of-type(1) {
        border-radius: 4px 0 0 4px;
        border-right: none;
      }
      .choose-un:nth-of-type(2) {
        border-radius: none;
      }
      .choose-un:nth-of-type(3) {
        border-radius: 0 4px 4px 0;
        border-left: none;
      }
      .choose-active {
        color: rgba(255, 255, 255, 1);
        background: rgba(0, 70, 156, 1);
      }
    }
  }
  .tags {
    height: 14px;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
    position: relative;
    padding-left: 8px;
    &:before {
      content: '';
      position: absolute;
      width: 3px;
      height: 14px;
      background: #00469c;
      border-radius: 2px;
      left: 0;
    }
  }
}
</style>
