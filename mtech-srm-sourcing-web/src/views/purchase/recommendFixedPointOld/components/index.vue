<template>
  <div class="full-height mt-flex-direction-column">
    <div class="top-info">
      <div class="lf-wrap">
        <div class="detail-info">
          <div class="name-wrap">
            <div class="first-line">
              <span class="code">{{ this.forObject.pointNo || '-' }}</span>
            </div>
          </div>
          <div class="btns-wrap">
            <mt-button flat @click="comeBack()">{{ $t('返回') }}</mt-button>
            <mt-button flat v-show="resInfo.status == 0" @click="save()">{{
              $t('保存')
            }}</mt-button>
            <mt-button flat v-show="resInfo.status == 0" @click="submit()">{{
              $t('提交')
            }}</mt-button>
          </div>
        </div>
      </div>
      <!-- 顶部主要信息 -->
      <div v-if="showHeaderForm" class="mian-info">
        <mt-form
          ref="ruleForm"
          :model="forObject"
          class="form-generator-form"
          style="justify-content: start"
        >
          <mt-form-item
            ref="pointNo"
            prop="id"
            :label="$t('定价单号')"
            class="form-generator-form-item form-generator-item-col-4"
          >
            <mt-input
              disabled
              float-label-type="Never"
              v-model="forObject.pointNo"
              :placeholder="$t('请输入定价单号')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            ref="title"
            prop="id"
            :label="$t('标题')"
            class="form-generator-form-item form-generator-item-col-4"
          >
            <mt-input
              float-label-type="Never"
              v-model="forObject.title"
              :placeholder="$t('请输入标题')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            ref="priceObjectCode"
            prop="id"
            :label="$t('定价对象')"
            class="form-generator-form-item form-generator-item-col-4"
          >
            <!-- <mt-input
              :disabled="isDisabled"
              float-label-type="Never"
              v-model="forObject.priceObjectCode"
              :placeholder="$t('请输入定价对象')"
            ></mt-input> -->
            <mt-select
              :disabled="pointItemExist"
              :value="forObject.priceObjectCode"
              :fields="{ text: 'sourcingObj', value: 'templateCode' }"
              :data-source="priceObjectNameList"
              :placeholder="$t('请选择定价对象')"
              @input="inputPricing($event, 'priceObjectName')"
              :show-clear-button="true"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            ref="companyCode"
            prop="id"
            :label="$t('公司')"
            class="form-generator-form-item form-generator-item-col-4"
          >
            <!-- <mt-input
              :disabled="isDisabled"
              float-label-type="Never"
              v-model="forObject.companyCode"
              :placeholder="$t('请输入公司')"
            ></mt-input> -->
            <mt-select
              :disabled="pointItemExist"
              :value="forObject.companyName"
              float-label-type="Never"
              :data-source="companyNameDataSource"
              :fields="{ text: 'text', value: 'orgName' }"
              :open-dispatch-change="true"
              @change="changeTaxItem"
              :placeholder="$t('请选择公司')"
              :show-clear-button="true"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            ref="purOrgCode"
            prop="id"
            :label="$t('采购组织')"
            class="form-generator-form-item form-generator-item-col-4"
          >
            <!-- <mt-input
              :disabled="isDisabled"
              float-label-type="Never"
              v-model="forObject.purOrgCode"
              :placeholder="$t('请输入采购组织')"
            ></mt-input> -->
            <mt-select
              :disabled="pointItemExist"
              :value="forObject.purOrgCode"
              :fields="{
                text: 'text',
                value: 'organizationCode'
              }"
              :data-source="purOrgNameDateSource"
              :placeholder="$t('请选择采购组织')"
              @input="inputPricing($event, 'purOrgName')"
              :show-clear-button="true"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            ref="site"
            prop="site"
            :label="$t('工厂')"
            class="form-generator-form-item form-generator-item-col-4"
          >
            <!-- <mt-input
              :disabled="isDisabled"
              float-label-type="Never"
              v-model="forObject.siteCode"
              :placeholder="$t('请选择工厂')"
            ></mt-input> -->
            <mt-select
              :disabled="pointItemExist"
              :value="forObject.siteName"
              :fields="{
                text: 'text',
                value: 'orgName'
              }"
              :data-source="siteNameDataSource"
              :placeholder="$t('请选择工厂')"
              @input="inputPricing($event, 'siteName')"
              @change="siteNameChange"
              :show-clear-button="true"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            ref="expandSaveRequestList"
            prop="expandSaveRequestList"
            :label="$t('工厂扩展')"
            class="form-generator-form-item form-generator-item-col-4"
          >
            <mt-multi-select
              :key="sourcingExpandKey"
              class="sourcing-expand"
              :data-source="sourcingExpandList"
              :allow-filtering="true"
              :title="sourcingExpandTitle"
              filter-type="Contains"
              v-model="forObject.expandSaveRequestList"
              @change="sourcingExpandChange"
              popup-width="470px"
            />
          </mt-form-item>
          <mt-form-item
            ref="purId"
            prop="id"
            :label="$t('采购人员')"
            class="form-generator-form-item form-generator-item-col-4"
          >
            <!-- <mt-input
              :disabled="isDisabled"
              v-model="forObject.purId"
              :show-clear-button="true"
              :placeholder="$t('请选输入采购人员')"
            ></mt-input> -->
            <debounce-filter-select
              :disabled="pointItemExist"
              v-model="forObject.purId"
              :width="NaN"
              :request="getCurrentEmployees"
              :data-source="personnelDataSource"
              @change="inputPricing($event, 'purId')"
              :show-clear-button="false"
              :fields="{ text: 'text', value: 'employeeId' }"
              :placeholder="$t('请选择采购员')"
            ></debounce-filter-select>
          </mt-form-item>
          <mt-form-item
            ref="decidePriceType"
            prop="id"
            :label="$t('定价单类型')"
            class="form-generator-form-item form-generator-item-col-4"
          >
            <mt-select
              :disabled="pointItemExist"
              :data-source="[
                { text: $t('直接定价'), value: 0 },
                { text: $t('寻源结果定价'), value: 1 }
              ]"
              v-model="forObject.decidePriceType"
              :show-clear-button="true"
              :placeholder="$t('请输入定价单类型')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            ref="priceClassification"
            prop="priceClassification"
            :label="$t('价格分类')"
            class="form-generator-form-item form-generator-item-col-4"
          >
            <mt-select
              :disabled="pointItemExist"
              :data-source="[
                { text: this.$t('基价'), value: 1 },
                { text: this.$t('SRM价格'), value: 2 },
                { text: this.$t('暂估价格'), value: 3 },
                { text: this.$t('执行价格'), value: 4 }
              ]"
              v-model="forObject.priceClassification"
              :show-clear-button="true"
              :placeholder="$t('请选择价格分类')"
            />
          </mt-form-item>
          <mt-form-item
            ref="status"
            prop="id"
            :label="$t('状态')"
            class="form-generator-form-item form-generator-item-col-4"
          >
            <mt-select
              disabled
              :data-source="[
                { text: $t('已作废'), value: -1 },
                { text: $t('草稿'), value: 0 },
                { text: $t('审批中'), value: 1 },
                { text: $t('审批通过'), value: 2 },
                { text: $t('审批驳回'), value: 3 }
              ]"
              v-model="forObject.status"
              :show-clear-button="true"
              :placeholder="$t('请输入状态')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item
            ref="remark"
            prop="id"
            :label="$t('备注')"
            class="form-generator-form-item form-generator-item-col-4"
          >
            <mt-input
              v-model="forObject.remark"
              :show-clear-button="true"
              :placeholder="$t('字数不超过200字')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </div>
    <div class="top-shrink" @click="showHeaderForm = !showHeaderForm">
      <mt-icon v-show="!showHeaderForm" name="a-iconxl"></mt-icon>
      <mt-icon v-show="showHeaderForm" name="a-iconsq"></mt-icon>
    </div>
    <div class="relation-ships">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickCellTitle="handleClickCellTitle"
        @handleClickToolBar="handleClickToolBar"
        v-if="pageConfig[0].grid.columnData.length > 0"
        @actionBegin="actionBegin"
        @actionComplete="actionComplete"
        @cellEdit="cellEdit"
      >
        <template slot="slot-2"><attachment-tab :detail-info="resInfo" /> </template>
      </mt-template-page>
    </div>
  </div>
</template>

<script>
import debounceFilterSelect from 'ROUTER_PURCHASE_RFX/components/debounceFilterSelect'
import AttachmentTab from './attachment.vue'
import { pageConfig, columnDataOne, columnDataTwo, toolbar, toolbarTwo } from './config'
import { download, getHeadersFileName } from '@/utils/utils'
import { v1 as uuidv1 } from 'uuid'
import { addArrTextField } from '@/views/common/columnData/utils'
import { getEndDate } from '@/utils/obj'

export default {
  components: {
    debounceFilterSelect,
    AttachmentTab
  },
  name: 'ExpertRating',
  data() {
    return {
      showHeaderForm: false,
      pageConfig: pageConfig(
        this.$API.rfxList.pagePointItemTv,
        this.$route.query.id,
        this.serializeList,
        this.recordDoubleClick,
        this.queryCellInfo,
        this.$API.rfxList.pagePointQuota
      ),
      forObject: {
        companyCode: '', //公司
        purId: '', //采购人员
        pointNo: '', //定价单号
        priceObjectCode: '',
        purOrgCode: '', //采购组织
        siteCode: '', // 工厂
        expandSaveRequestList: [], // 扩展工厂
        decidePriceType: '', //定价类型
        status: '', //状态
        remark: '', //备注
        priceClassification: '',
        title: ''
      },
      busEvent: new Set(), // 事件收集
      resInfo: {},
      siteNameDataSource: [],
      isDisabled: false,
      priceObjectNameList: [],
      companyNameDataSource: [],
      purOrgNameDateSource: [],
      sourcingExpandList: [],
      sourcingExpandTitle: '',
      personnelDataSource: [],
      pointItemExist: false, // 是否存在明细数据
      isfirstRender: false,
      deleteStepValueIds: [],
      isSaving: false,
      isSubmit: false,
      modifyRecords: [], // 修改的行
      sourcingExpandKey: new Date().getTime()
    }
  },
  computed: {
    gridCpnt() {
      const cpnt = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.ej2Instances
      return cpnt
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('pricingSiteCode')
    sessionStorage.removeItem('pricingSiteInfo')
    sessionStorage.removeItem('directRowData')
    sessionStorage.removeItem('companyName')
    sessionStorage.removeItem('pricingReferChannel')

    this.$bus.$off(`directPriceStepValueEdit`)
  },
  async mounted() {
    this.$bus.$on(`directPriceStepValueEdit`, (data) => {
      this.directPriceStepValueEdit(data)
    })
    await this.getConfigList()
    await this.getSpecifiedChildrenLevelOrgs()
    await this.getCurrentEmployees({ text: '' })
    await this.detailPoint()
    this.getReadOnly()
    this.getSourcingExpandTitle()
  },
  methods: {
    inputPricing(val, type) {
      if (type == 'priceObjectName') {
        const arr = this.priceObjectNameList.find((e) => e.templateCode === val)
        if (arr) {
          this.forObject.priceObjectName = arr.sourcingObj
          this.forObject.priceObjectId = arr.id
          this.forObject.priceObjectCode = arr.templateCode
        }
      } else if (type == 'purOrgName') {
        const arr = this.purOrgNameDateSource.find((e) => {
          return e.organizationCode == val
        })
        console.log(192, arr)
        if (arr) {
          this.forObject.purOrgName = arr.organizationName
          this.forObject.purOrgId = arr.id
          this.forObject.purOrgCode = arr.organizationCode
        }
        // 获取工厂列表
        if (this.forObject.purOrgId) {
          this.$API.masterData
            .permissionSiteList({
              buOrgId: this.forObject.purOrgId,
              companyId: this.forObject.companyId
            })
            .then((res) => {
              if (res.data == null) {
                this.siteNameDataSource = []
              }
              const siteList = []
              res.data.forEach((item) => {
                siteList.push({
                  ...item,
                  text: item.orgCode + '-' + item.orgName,
                  siteAndPurchaseOrg: `${this.forObject.purOrgCode}${this.forObject.purOrgName} - ${item.orgCode}${item.orgName}`,
                  siteAndPurchaseOrgVal: `${this.forObject.purOrgCode}+${item.orgCode}`,
                  data: {
                    purOrgName: this.forObject.purOrgName,
                    purOrgCode: this.forObject.purOrgCode,
                    purOrgId: this.forObject.purOrgId,
                    siteCode: item.orgCode,
                    siteId: item.id,
                    siteName: item.orgName
                  }
                })
              })
              this.siteNameDataSource = siteList
            })
        } else {
          this.siteNameDataSource = []
        }
      } else if (type === 'siteName') {
        const arr = this.siteNameDataSource.find((e) => e.orgName === val)
        if (arr) {
          this.forObject.siteCode = arr.orgCode
          this.forObject.siteId = arr.id
          this.forObject.siteName = arr.orgName
          sessionStorage.setItem('pricingSiteCode', arr.orgCode)
          sessionStorage.setItem('organizationId', arr.id)
        }
      } else if (type == 'purId') {
        this.forObject.purName = val.itemData.employeeName
        this.forObject.purId = val.itemData.employeeId
      } else if (type == 'companyName') {
        const arr = this.companyNameDataSource.find((e) => e.orgCode === val)
        if (arr) {
          this.forObject.companyName = arr.orgName
          this.forObject.companyId = arr.id
          this.forObject.companyCode = arr.orgCode
        }
      } else if (type == 'decidePriceType') {
        const arr = [
          { text: this.$t('直接定价'), value: 0 },
          { text: this.$t('寻源结果定价'), value: 1 }
        ].find((e) => e.value === val)
        console.log(224, arr)
        if (arr) {
          this.forObject.decidePriceType = arr.value
        }
      }
    },
    //定价对象列表
    async getConfigList() {
      this.$API.businessConfig
        .getConfigList({
          page: {
            current: 1,
            size: 1000
          },
          condition: 'and',
          rules: [
            {
              field: 'sourcingMode',
              type: 'string',
              operator: 'equal',
              value: 'direct_pricing'
            }
          ]
        })
        .then((res) => {
          this.priceObjectNameList = res.data.records
        })
    },
    //获取公司
    async getSpecifiedChildrenLevelOrgs() {
      this.$API.masterData
        .findSpecifiedChildrenLevelOrgs({
          organizationLevelCodes: ['ORG02'],
          orgType: 'ORG001PRO',
          includeItself: false,
          organizationIds: []
        })
        .then((res) => {
          let _data = res.data
          _data?.forEach((item) => {
            item.text = item.orgCode + '-' + item.orgName
          })
          this.companyNameDataSource = [..._data]
        })
    },
    changeTaxItem(e) {
      if (!this.isfirstRender) {
        this.forObject.companyName = e.itemData?.orgName
        this.forObject.companyId = e.itemData?.id
        this.forObject.companyCode = e.itemData?.orgCode
        this.forObject.purOrgName = ''
        this.forObject.purOrgCode = ''
        this.forObject.purOrgId = ''
        this.forObject.siteCode = ''
        this.forObject.siteId = ''
        this.forObject.siteName = ''
        this.forObject.expandSaveRequestList = []
        this.getMainData(e.itemData?.id)
      }

      sessionStorage.setItem('companyName', this.forObject.companyName)
    },
    siteNameChange(e) {
      this.getSourcingExpandList(e.itemData?.data?.siteCode, this.forObject.purOrgCode)
    },
    sourcingExpandChange() {
      this.getSourcingExpandTitle()
    },
    //采购组织
    getMainData(companyId) {
      if (!companyId) {
        this.purOrgNameDateSource = []
        this.siteNameDataSource = []
      }
      // 数据源 purGroupName
      // this.$API.masterData
      //   .purchaseOraginaze({
      //     organizationCode: "BUORG002ADM",
      //   })
      //   .then((res) => {
      //     this.form.dataSource.procurement = res.data;
      //   });
      this.$API.masterData
        .permissionOrgList({
          orgId: companyId
        })
        .then((res) => {
          let _data = res.data
          _data?.forEach((item) => {
            item.text = item.organizationCode + '-' + item.organizationName
          })
          this.purOrgNameDateSource = [..._data]
          this.siteNameDataSource = []
        })
    },
    // 工厂扩展
    async getSourcingExpandList(siteCode, purOrgCode) {
      if (!siteCode) {
        this.sourcingExpandList = []
        return
      }
      const res = await this.$API.rfxDetail.purAllOrgWithSite({ fuzzyParam: siteCode, purOrgCode })
      if (res.code === 200 && res.data) {
        let dataSource = []
        res.data.forEach((v) => {
          v.siteOrgs?.forEach((x) => {
            dataSource.push({
              text: `${v.companyCode}-${v.businessOrganizationCode}-${v.businessOrganizationName}+${x.orgCode}-${x.orgName}`,
              value: v.companyCode + '+' + v.businessOrganizationCode + '+' + x.orgCode,
              data: {
                companyCode: v.companyCode,
                companyId: v.companyId,
                companyName: v.companyName,
                purOrgCode: v.businessOrganizationCode,
                purOrgId: v.id,
                purOrgName: v.businessOrganizationName,
                siteCode: x.orgCode,
                siteId: x.id,
                siteName: x.orgName
              }
            })
          })
        })
        this.sourcingExpandList = dataSource
      } else {
        this.sourcingExpandList = []
      }
    },
    // 获取 用户列表
    getCurrentEmployees(e) {
      const { text: fuzzyName } = e
      this.personnelDataSource = []
      this.$API.masterData.getCurrentTenantEmployees({ fuzzyName }).then((res) => {
        const tmp = []
        res.data.forEach((item) => {
          tmp.push({
            ...item,
            text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`
          })
        })
        this.personnelDataSource = tmp
      })
    },
    recordDoubleClick(row) {
      const info = this.siteNameDataSource.find((e) => e.id === row.rowData.siteId)
      if (info) {
        sessionStorage.setItem('organizationId', info.id)
      }
    },
    // 监听单元格编辑
    cellEdit(e) {
      // 如果存在id或者存在参考物料编码（美工阶梯），则为保存后的数据，禁止编辑是否阶梯报价
      if (
        e.columnName === 'stepQuote' &&
        (e.rowData.id ||
          (this.forObject.priceObjectCode === 'artist_component' && e.rowData.referItemCode))
      )
        e.cancel = true
    },
    actionBegin(e) {
      if (e.requestType === 'save' && e.action == 'add') {
        const grid = this.$refs.templateRef.getCurrentTabRef().grid
        const rowData = e.data
        const tableList = grid.getCurrentViewRecords()
        if (
          tableList.some(
            (i) =>
              i.itemGroupId !== rowData.itemGroupId &&
              i.supplierCode === rowData.supplierCode &&
              i.siteCode === rowData.siteCode &&
              i.itemCode === rowData.itemCode &&
              i.stepQuote === rowData.stepQuote &&
              i.stepQuote == 1
          )
        ) {
          this.$toast({
            content: this.$t('已存在相同供应商、工厂、物料的阶梯数据，请勿重复添加'),
            type: 'warning'
          })
          e.cancel = true
          return
        }
      }
    },
    actionComplete(e) {
      if (e.requestType === 'save') {
        const grid = this.$refs.templateRef.getCurrentTabRef().grid
        const rowData = e.data
        const tableList = grid.getCurrentViewRecords()
        const keyList = Object.keys(rowData)
        tableList.forEach((item, index) => {
          if (
            item.itemGroupId === rowData.itemGroupId &&
            item.stepQuote === rowData.stepQuote &&
            item.stepQuote == 1
          ) {
            keyList.forEach((keyName) => {
              if (
                keyName !== 'id' &&
                keyName !== 'addId' &&
                keyName !== 'stepValue' &&
                keyName !== 'column' &&
                keyName !== 'index' &&
                keyName !== 'pointItemId' &&
                keyName !== 'pointItemExtId'
              ) {
                item[keyName] = rowData[keyName]
              }
            })
            this.$set(this.$refs.templateRef.getCurrentTabRef().grid.dataSource, index, item)
          }
        })
        if (this.$refs.templateRef.getCurrentTabRef().tabIndex === 0) {
          grid.refresh()
        }
      }
    },
    // 结束编辑状态
    endEdit() {
      let _current = this.$refs.templateRef.getCurrentTabRef()
      _current?.grid.endEdit()
      _current?.grid.hideSpinner()
    },
    getReadOnly() {
      if (this.$route.query.decidePriceType == 1 || this.$route.query.status != 0) {
        this.pageConfig[0].grid.editSettings.allowEditing = false
        this.pageConfig[1].grid.editSettings.allowEditing = false
      }
    },
    // 获取工厂拓展title
    getSourcingExpandTitle() {
      let _textList = []
      this.sourcingExpandList?.forEach((item) => {
        if (this.forObject.expandSaveRequestList.includes(item.value)) {
          _textList.push(item.text)
        }
      })
      this.sourcingExpandTitle = _textList?.join(',')
      this.sourcingExpandKey = new Date().getTime()
    },
    comeBack() {
      this.$router.go(-1)
    },
    save() {
      let params = this.resInfo
      const expandSaveRequestList = []
      this.forObject.expandSaveRequestList.forEach((v) => {
        const temp = this.sourcingExpandList.find((t) => t.value === v)
        expandSaveRequestList.push(temp.data)
      })
      params.expandSaveRequestList = expandSaveRequestList
      this.$API.rfxList.savePointTv(params).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: res.msg,
            type: 'success'
          })
        }
      })
    },
    submit() {
      if (this.resInfo.status < 1) {
        if (this.isSubmit) {
          this.$toast({
            content: this.$t('正在提交中，请勿重复点击'),
            type: 'warning'
          })
          return
        }
        this.isSubmit = true
        let params = {
          id: this.resInfo.id
        }
        this.$API.rfxList
          .submitPointTv(params)
          .then(() => {
            this.$toast({ content: this.$t('提交成功'), type: 'success' })
            this.isSubmit = false
            this.detailPoint()
          })
          .catch(() => {
            this.isSubmit = false
          })
      } else {
        this.$toast({
          content: this.$t('审批通过，不可重复提交'),
          type: 'warning'
        })
      }
    },
    //保存
    handleSave(_selectGridRecords, saveType) {
      // let flag = false
      // _selectGridRecords.forEach((e) => {
      //   e.pointId = this.$route.query.id
      //   e.siteCode = this.forObject.siteCode
      //   e.siteId = this.forObject.siteId
      //   e.siteName = this.forObject.siteName
      // })
      // for (const value of _selectGridRecords) {
      //   if (value.quoteEffectiveStartDate > value.quoteEffectiveEndDate) {
      //     flag = true
      //   }
      // }
      // if (flag) {
      //   this.$toast({
      //     content: this.$t('开始日期不能大于失效日期'),
      //     type: 'warning'
      //   })
      // } else {
      // }
      let gridRecords = _selectGridRecords
      if (saveType && saveType === 'stepValue') {
        let currentViewRecords = this.$refs.templateRef
          .getCurrentTabRef()
          .grid.getCurrentViewRecords()
        const currentRecordsData = currentViewRecords.filter(
          (i) => i.itemGroupId !== _selectGridRecords[0]['itemGroupId']
        )
        gridRecords = [...currentRecordsData, ..._selectGridRecords]
      } else {
        gridRecords = this.syncRowData(_selectGridRecords)
      }
      this.endEdit()
      for (let i = 0; i < gridRecords.length; i++) {
        const item = gridRecords[i]
        item.pointId = this.$route.query.id
        item.siteCode = this.forObject.siteCode
        item.siteId = this.forObject.siteId
        item.siteName = this.forObject.siteName
        if (!item.supplierCode) {
          this.$toast({
            content: this.$t(`第${i + 1}行请选择供应商`),
            type: 'warning'
          })
          return
        }
        if (!item.itemCode) {
          this.$toast({
            content: this.$t(`第${i + 1}行请选择物料`),
            type: 'warning'
          })
          return
        }
        if (!item.referChannel && item.referChannel !== 0) {
          this.$toast({
            content: this.$t(`第${i + 1}行请选择价格来源`),
            type: 'warning'
          })
          return
        }
        if (!item.untaxedUnitPrice && item.untaxedUnitPrice !== 0) {
          this.$toast({
            content: this.$t(`第${i + 1}行单价未税必填`),
            type: 'warning'
          })
          return
        }
        if (!item.priceUnitName) {
          this.$toast({
            content: this.$t(`第${i + 1}行价格单位必填`),
            type: 'warning'
          })
          return
        }
        if (!item.deliveryPlace) {
          this.$toast({
            content: this.$t(`第${i + 1}行直送地必填`),
            type: 'warning'
          })
          return
        }
        if (item.id && item.stepQuote == 1) {
          if (!item.stepQuoteType && item.stepQuoteType !== 0) {
            this.$toast({
              content: this.$t(`第${i + 1}行阶梯类型必填`),
              type: 'warning'
            })
            return
          }
          if (!item.stepValue && item.stepValue !== 0) {
            this.$toast({
              content: this.$t(`第${i + 1}行阶梯数量必填`),
              type: 'warning'
            })
            return
          }
        }
        if (item.referChannel === 2 && !item.referItemCode) {
          this.$toast({
            content: this.$t(`第${i + 1}行价格来源为价格记录时参考物料必填`),
            type: 'warning'
          })
          return
        }
        if (!item.bidCurrencyCode) {
          this.$toast({
            content: this.$t(`第${i + 1}行币种必填`),
            type: 'warning'
          })
          return
        }
        if (!item.bidTaxRateCode) {
          this.$toast({
            content: this.$t(`第${i + 1}行税率必填`),
            type: 'warning'
          })
          return
        }
        if (!item.unitCode) {
          this.$toast({
            content: this.$t(`第${i + 1}行基本单位编码必填`),
            type: 'warning'
          })
          return
        }
        if (!item.purUnitName) {
          this.$toast({
            content: this.$t(`第${i + 1}行订单单位必填`),
            type: 'warning'
          })
          return
        }
        if (!item.quoteAttribute && item.quoteAttribute !== 0) {
          this.$toast({
            content: this.$t(`第${i + 1}行报价属性必填`),
            type: 'warning'
          })
          return
        }
        if (!item.quoteMode && item.quoteMode !== 0) {
          this.$toast({
            content: this.$t(`第${i + 1}行价格生效方式必填`),
            type: 'warning'
          })
          return
        }
        if (!item.minPackageQuantity && item.minPackageQuantity !== 0) {
          this.$toast({
            content: this.$t(`第${i + 1}行最小包装量/MPQ必填`),
            type: 'warning'
          })
          return
        }
        if (!item.minPurQuantity && item.minPurQuantity !== 0) {
          this.$toast({
            content: this.$t(`第${i + 1}行最小采购量/MOQ必填`),
            type: 'warning'
          })
          return
        }
        if (!item.leadTime && item.leadTime !== 0) {
          this.$toast({
            content: this.$t(`第${i + 1}行L/T必填`),
            type: 'warning'
          })
          return
        }
        if (!item.unconditionalLeadTime && item.unconditionalLeadTime !== 0) {
          this.$toast({
            content: this.$t(`第${i + 1}行无条件L/T必填`),
            type: 'warning'
          })
          return
        }
        if (Number(item.unconditionalLeadTime) < Number(item.leadTime)) {
          this.$toast({
            content: this.$t(`第${i + 1}行无条件L/T需大于等于L/T`),
            type: 'warning'
          })
          return
        }
        if (!item.quoteEffectiveStartDate) {
          this.$toast({
            content: this.$t(`第${i + 1}行价格生效日期必填`),
            type: 'warning'
          })
          return
        }
        if (!item.quoteEffectiveEndDate) {
          this.$toast({
            content: this.$t(`第${i + 1}行价格失效日期必填`),
            type: 'warning'
          })
          return
        }
        if (item.quoteEffectiveStartDate > item.quoteEffectiveEndDate) {
          this.$toast({
            content: this.$t(`第${i + 1}行生效日期不能大于失效日期`),
            type: 'warning'
          })
          return
        }
      }
      if (this.isSaving) {
        return
      }
      this.isSaving = true
      this.$API.rfxList
        .savePointItemListTv(gridRecords)
        .then((res) => {
          if (res.code == 200) {
            // if (saveType && saveType === 'stepValue' && this.deleteStepValueIds) {
            //   let params = {
            //     idList: this.deleteStepValueIds
            //   }
            //   this.$API.rfxList.deletePointItemList(params).then(() => {
            //     // this.$toast({ content: this.$t('删除成功'), type: 'success' })
            //     this.$refs.templateRef.refreshCurrentGridData()
            //   })
            //   return
            // }
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
            this.isSaving = false
          }
        })
        .catch(() => {
          this.isSaving = false
        })
    },
    //保存
    handleSaveFn(e) {
      let _selectGridRecords = e.grid.getCurrentViewRecords()
      _selectGridRecords.forEach((x) => {
        // 新增的明细数据设置该字段为false后端将不校验阶梯数量必填
        if (!x.id) {
          x.excludeValid = false
        }
      })
      this.handleSave(_selectGridRecords)
    },
    //保存
    handleSaveTwoFn(e) {
      let _selectGridRecords = e.grid.getCurrentViewRecords()
      let str = []
      _selectGridRecords.forEach((item, index) => {
        if (item.allocationRatioText < 0 || item.allocationRatioText > 100) {
          str.push(this.$t(`第${index + 1}行`))
        }
      })
      if (str.length != 0) {
        this.$toast({
          content: str.join(',') + '的配额字段，请输入（0-100）的数字',
          type: 'warning'
        })
        return
      }
      let primaryRow = {}
      let quotaList = _selectGridRecords.map((x) => {
        // 分组同步数据
        if (x.itemGroupId && this.modifyRecords.includes(x.itemGroupId)) {
          x.optType = 'modify'
        }
        if (!x.rowSpan) {
          x.allocationRatioText = primaryRow.allocationRatioText
        } else {
          primaryRow = { ...x }
        }
        return {
          allocationQuantity: x.allocationQuantity,
          allocationRatio: (x.allocationRatioText / 100).toFixed(2),
          id: x.id,
          minSplitQuantity: x.minSplitQuantity,
          optType: x.optType,
          minPurQuantity: x.minPurQuantity,
          itemCode: x.itemCode,
          siteCode: x.siteCode,
          stepValue: x?.stepValue || 0
        }
      })
      const params = {
        pointId: this.$route.query.id,
        quotaDTOList: quotaList
      }
      this.$API.rfxList.savetvQuotaItemAllocationList(params).then((res) => {
        if (res.code == 200) {
          this.$toast({ content: this.$t('保存成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    //删除
    handleDelFn(_selectGridRecords, idList) {
      if (_selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else {
        let params = {
          idList: idList
        }
        //  删除的都是新增行，无id，不需要掉用接口进行删除
        if (idList.length === 0) {
          this.$refs.templateRef.refreshCurrentGridData()
          return
        }
        this.$API.rfxList.deletePointItemListTv(params).then(() => {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        })
      }
    },

    //新增
    handleAddFn(e) {
      if (this.forObject.decidePriceType == 0) {
        // 电源板外发
        let item = {}
        if (this.forObject.priceObjectCode === 'POWER_PANEL_SEND_OUT') {
          let _end = ['1503', '0602'].includes(this.forObject.companyCode)
            ? getEndDate(new Date().getTime())
            : new Date('9999-12-31').getTime()
          item = {
            addId: uuidv1(),
            deliveryPlace: this.$t('惠州'),
            itemGroupId: uuidv1(),
            leadTime: 7,
            minPackageQuantity: 1,
            minPurQuantity: 1,
            priceUnitName: '1000',
            quoteEffectiveEndDate: _end,
            quoteEffectiveStartDate: new Date().getTime(),
            referChannel: 0,
            stepQuote: 0,
            stepQuoteType: null,
            unconditionalLeadTime: 10,
            untaxedUnitPrice: 12000,
            optType: 'add'
          }
        } else if (this.forObject.priceObjectCode === 'reference_pricing') {
          // 参考定价
          item = {
            addId: uuidv1(),
            itemGroupId: uuidv1(),
            referChannel: 2,
            stepQuote: 0,
            stepQuoteType: null,
            optType: 'add',
            priceObjectCode: 'reference_pricing'
          }
        } else {
          item = {
            addId: uuidv1(),
            itemGroupId: uuidv1(),
            referChannel: 2,
            stepQuote: 1,
            stepQuoteType: 3,
            optType: 'add'
          }
        }
        sessionStorage.setItem('pricingReferChannel', item.referChannel)
        e.grid.addRecord(item)

        // 新增的行设置为不选中（默认是显示为选中，但实际上无法通过获取选中行数据拿到，会导致bug）
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
      } else {
        this.$dialog({
          modal: () => import('./newPricingLines/index.vue'),
          data: {
            title: this.$t('选择定价行'),
            pointId: this.$route.query.id
          },
          success: (data) => {
            const grid = this.$refs.templateRef.getCurrentTabRef().grid //获取表格实例
            grid.$options.propsData.dataSource.push(
              ...data.map((i) => ({
                ...i,
                id: undefined
              }))
            )
            grid.refresh() //刷新表格
          }
        })
      }
    },

    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      let idList = []
      _selectGridRecords.map((item) => {
        idList.push(item.id)
      })
      if (e.toolbar.id == 'Save') {
        this.endEdit()
        setTimeout(() => {
          this.handleSaveFn(e) //保存
        }, 100)
      } else if (e.toolbar.id == 'del') {
        // 按组进行删除
        idList = []
        const tableList = e.grid.getCurrentViewRecords()
        _selectGridRecords.forEach((item) => {
          const arr = tableList.filter((row) => row.itemGroupId === item.itemGroupId)
          arr.map((item) => !idList.includes(item.id) && idList.push(item.id))
        })
        this.handleDelFn(_selectGridRecords, idList) //删除
      } else if (e.toolbar.id == 'SaveTwo') {
        this.endEdit()
        setTimeout(() => {
          this.handleSaveTwoFn(e) //保存
        }, 100)
      } else if (e.toolbar.id == 'import') {
        this.handleImport()
      } else if (e.toolbar.id == 'export') {
        this.handleExport()
      } else if (e.toolbar.id == 'importTwo') {
        this.handleImportTwo()
      } else if (e.toolbar.id == 'exportTwo') {
        this.handleExportTwo()
      } else if (e.toolbar.id == 'redirectOA') {
        this.redirectOA()
      } else if (e.toolbar.id == 'batchVMI') {
        this.batchVMI(e)
      } else {
        this.handleAddFn(e) //新增
      }
    },
    batchVMI(e) {
      let _selectGridRecords = e.grid.getSelectedRecords()
      if (!_selectGridRecords.length) {
        this.$toast({
          content: this.$t('请先勾选需要更改的数据'),
          type: 'warning'
        })
        return
      }
      const groupIdList = []
      _selectGridRecords.forEach((item) => {
        !groupIdList.includes(item.itemGroupId) && groupIdList.push(item.itemGroupId)
      })
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认执行“批量VMI”操作？')
        },
        success: () => {
          this.$API.rfxList
            .batchSetVmi({
              pointId: this.$route.query.id,
              itemGroupIds: groupIdList
            })
            .then((res) => {
              this.$toast({
                content: res.message || this.$t('操作成功！'),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData()
            })
        }
      })
    },
    // 跳转OA审批
    redirectOA() {
      this.$API.rfxList
        .getSourceOaLink({ docId: this.$route.query.id, operationType: 'direct_pricing' })
        .then((res) => {
          const { code, data } = res
          if (code == 200) {
            window.open(data)
          }
        })
    },
    // 导出
    handleExport() {
      // 先要点击“查询”按钮才能取得queryBuilderRules
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules,
        defaultRules: [
          {
            field: 'point.id',
            label: this.$t('定点推荐id'),
            operator: 'equal',
            type: 'string',
            value: this.$route.query.id
          }
        ].concat(queryBuilderRules.rules || [])
      } // 筛选条件
      this.$API.rfxList.pointItemDetailExportTv(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 导入
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('上传/导入'),
          importApi:
            this.forObject.priceObjectCode === 'POWER_PANEL_SEND_OUT'
              ? this.$API.rfxList.powerPointItemImport
              : this.$API.rfxList.pointItemImport,
          downloadTemplateApi:
            this.forObject.priceObjectCode === 'POWER_PANEL_SEND_OUT'
              ? this.$API.rfxList.powerPointItemImportTemplate
              : this.$API.rfxList.pointItemImportTemplate,
          asyncParams: {
            pointId: this.$route.query.id
          }
        },
        success: () => {
          // 导入之后刷新列表
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 配额分配导出
    handleExportTwo() {
      // 先要点击“查询”按钮才能取得queryBuilderRules
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules,
        defaultRules: [
          {
            label: this.$t('定点推荐id'),
            field: 'pointId',
            type: 'long',
            operator: 'equal',
            value: this.$route.query.id
          },
          {
            label: this.$t('定点推荐type'),
            field: 'sourceDocType',
            type: 'string',
            operator: 'equal',
            value: 'tv_point'
          }
        ].concat(queryBuilderRules.rules || [])
      } // 筛选条件
      this.$API.rfxList.pointItemExportTv(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 配额分配导入
    handleImportTwo() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('上传/导入'),
          importApi: this.$API.rfxList.pointBiddingImportTv,
          // downloadTemplateApi: this.$API.constraintExclude.exportConstraintExcludeTemplate,
          paramsKey: 'importFile',
          asyncParams: {
            pointId: this.$route.query.id
          }
        },
        success: () => {
          // 导入之后刷新列表
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    handleClickCellTitle(e) {
      console.log(333, e.data)
      if (e.field == 'stepValue') {
        if (e.data.id) {
          this.$dialog({
            modal: () => import('./ladderQuotes/index.vue'),
            data: {
              title: this.$t('阶梯报价'),
              id: e.data.id,
              decidePriceType: this.forObject.decidePriceType,
              status: this.$route.query.status,
              stepQuote: e.data.stepQuote
            },
            success: () => {
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        } else {
          this.$toast({
            content: this.$t('请先保存新增的数据'),
            type: 'warning'
          })
        }
      }
    },
    async detailPoint() {
      this.isfirstRender = true
      let param = {
        id: this.$route.query.id
      }
      const res = await this.$API.rfxList.detailPointTv(param).catch(() => {})
      if (res.code == 200) {
        // this.forObject.companyCode = res.data.companyName
        // this.forObject.purId = res.data.purId
        // this.forObject.pointNo = res.data.pointNo
        // this.forObject.title = res.data.title
        // this.forObject.companyName = res.data.priceObjectName
        // this.forObject.purOrgCode = res.data.purOrgName
        // this.forObject.siteCode = res.data.siteCode
        // this.forObject.decidePriceType = res.data.decidePriceType
        // this.forObject.status = res.data.status
        // this.forObject.remark = res.data.remark
        this.forObject = res.data
        this.forObject.expandSaveRequestList = res.data.expandRespList?.map((itm) => {
          return `${itm?.companyCode}+${itm.purOrgCode}+${itm.siteCode}`
        })
        sessionStorage.setItem('pricingSiteCode', res.data.siteCode)
        sessionStorage.setItem('pricingSiteInfo', JSON.stringify(res.data))
        sessionStorage.setItem('companyName', this.forObject.companyName)
        this.resInfo = res.data
        if (res.data.pointItemExist) {
          this.pointItemExist = true
        }
        sessionStorage.setItem('organizationId', this.resInfo.siteId)
        sessionStorage.setItem('isKtFlag', this.resInfo.isKtFlag)
        // 美工阶梯，增加附件栏
        if (this.forObject.priceObjectCode === 'artist_component') {
          this.pageConfig.length === 2 && this.pageConfig.push({ title: this.$t('附件') })
        }
        this.$set(
          this.pageConfig[0],
          'toolbar',
          toolbar(![1, 2].includes(Number(this.forObject.status)), this.forObject.priceObjectCode) // 1,2 禁止
        )
        this.$set(
          this.pageConfig[1],
          'toolbar',
          toolbarTwo(
            ![1, 2].includes(Number(this.forObject.status)),
            this.forObject.priceObjectCode
          ) // 1,2 禁止
        )
        this.isfirstRender = false
        const resp = await this.$API.masterData
          .permissionSiteList({
            buOrgId: this.resInfo.purOrgId,
            companyId: this.resInfo.companyId
          })
          .catch(() => {})
        let siteNameDataSource = resp?.data || []
        this.siteNameDataSource = siteNameDataSource.map((itm) => {
          return {
            ...itm,
            siteAndPurchaseOrg: `${this.resInfo.purOrgCode}${this.resInfo.purOrgName} - ${itm.orgCode}${itm.orgName}`,
            siteAndPurchaseOrgVal: `${this.resInfo.purOrgCode}+${itm.orgCode}`,
            text: itm.orgCode + '-' + itm.orgName,
            data: {
              pointId: this.$route.query.id,
              purOrgName: this.resInfo.purOrgName,
              purOrgCode: this.resInfo.purOrgCode,
              purOrgId: this.resInfo.purOrgId,
              siteCode: itm.orgCode,
              siteId: itm.id,
              siteName: itm.orgName
            }
          }
        })
        this.$API.masterData
          .permissionOrgList({
            orgId: this.resInfo.companyId
          })
          .then((r) => {
            let _data = r.data
            _data?.forEach((item) => {
              item.text = item.organizationCode + '-' + item.organizationName
            })
            this.purOrgNameDateSource = [..._data]
          })
          .catch(() => {})
        await this.getSourcingExpandList(this.resInfo.siteCode, this.resInfo.purOrgCode)

        const item = await this.$API.masterData.getSupplierList().catch(() => {})
        let supplierList = item.data || []

        const direct = await this.$API.priceService.getDeliveryPlace().catch(() => {})
        let directDelivery = []
        if (direct) {
          directDelivery = direct.data.map(({ itemName }) => ({
            value: itemName,
            text: itemName
          }))
        }
        const Currency = await this.$API.masterData.queryAllCurrency().catch(() => {})
        let allCurrency = []
        if (Currency) {
          allCurrency = Currency.data.map(({ currencyName, currencyCode }) => ({
            value: currencyCode,
            text: currencyName
          }))
        }
        const TaxItem = await this.$API.masterData.queryAllTaxItem().catch(() => {})
        const taxList = TaxItem.data

        let unitNameList = []
        let unitRes = await this.$API.masterData.pagedQueryUnit().catch(() => {})
        if (unitRes) {
          unitNameList = unitRes?.data?.records || []
        }
        const unitNameListCN = addArrTextField(unitNameList, 'unitCode', 'unitName')
        this.$set(
          this.pageConfig[0].grid,
          'columnData',
          columnDataOne(
            this,
            {
              supplierList,
              siteNameDataSource,
              directDelivery,
              allCurrency,
              taxList,

              unitNameList: unitNameListCN,
              pagedQueryUnit: this.$API.masterData.pagedQueryUnit,
              getSupplierList: (e) => {
                return this.$API.masterData.getSupplierList(e)
              },
              getDefaultValue: (e) => {
                return this.$API.rfxList.getDefaultValueTv(e)
              }
            },
            {
              $on: (event, fn) => {
                this.busEvent.add(event)
                this.$bus.$on(event, fn)
              },
              $emit: (event, fn) => {
                this.busEvent.add(event)
                this.$bus.$emit(event, fn)
              }
            }
          )
        )
        this.$set(
          this.pageConfig[1].grid,
          'columnData',
          columnDataTwo(this, {
            directDelivery,
            allCurrency
          })
        )
      }
    },
    serializeList(list) {
      let preClassName = ''
      let preItemcode = ''
      list.forEach((x) => {
        x.addId = x.id ? x.id : uuidv1()
        x.quoteEffectiveStartDate = Number(x.quoteEffectiveStartDate)
        x.quoteEffectiveEndDate = Number(x.quoteEffectiveEndDate)
        x.allocationRatioText = (x.allocationRatio * 100).toFixed(0)
        x.declinePercent = x.declinePercent ? (x.declinePercent * 100).toFixed(2) : ''
        if (preItemcode !== x.itemCode) {
          preItemcode = x.itemCode
          preClassName = preClassName === 'bg-grey' ? '' : 'bg-grey'
        }
        x.className = preClassName
      })
      return this.groupData(list)
    },
    // 对数据进行分组
    groupData(list) {
      const kindList = []
      const tempList = []
      const resList = []
      list.forEach((item) => {
        let groupByKey = item.itemGroupId
        if (!kindList.includes(groupByKey)) {
          kindList.push(groupByKey)
          tempList.push([])
        }
        const i = kindList.indexOf(groupByKey)
        tempList[i].push(item)
      })
      tempList.forEach((item, index) => {
        item[0].rowSpan = item.length
        item[0].itemStageList = []
        item[0].groupLineIndex = index + 1
        item.forEach((i) => item[0].itemStageList.push({ stepValue: i.stepValue }))
        resList.push(...item)
      })
      return resList
    },
    queryCellInfo(args) {
      // 不合并的单元格
      const arr = [
        'id',
        'addId',
        'stepValue',
        'itemStageList',
        'column',
        'index',
        'pointItemId',
        'pointItemExtId',
        'untaxedUnitPrice',
        'taxedUnitPrice',
        'historyUntaxedUnitPrice',
        'referItemUnitPriceUntaxed',
        'declinePercent'
      ]
      if (args.data.rowSpan && !arr.includes(args.column.field)) {
        args.rowSpan = args.data.rowSpan
        args.cell.style.borderTop = 'none'
        args.cell.style.borderBottom = '1px solid #e0e0e0'
      }
      args.cell.style.borderLeft = '1px solid #e0e0e0'
    },
    // 根据分组进行数据同步
    syncRowData(rows) {
      if (!rows?.length) {
        return []
      }
      const resList = []
      const keyList = Object.keys(rows[0])
      const arr = [
        'id',
        'addId',
        'stepValue',
        'itemStageList',
        'column',
        'index',
        'pointItemId',
        'pointItemExtId',
        'rowSpan',
        'untaxedUnitPrice',
        'taxedUnitPrice',
        'historyUntaxedUnitPrice',
        'referItemUnitPriceUntaxed',
        'declinePercent'
      ]
      let rowData = {}
      rows.forEach((item) => {
        if (item.itemGroupId && this.modifyRecords.includes(item.itemGroupId)) {
          item.optType = 'modify'
        }
        if (!item.rowSpan) {
          if (item.itemGroupId === rowData.itemGroupId) {
            keyList.forEach((keyName) => {
              if (!arr.includes(keyName)) {
                item[keyName] = rowData[keyName]
              }
            })
          }
        } else {
          rowData = { ...item }
        }
        resList.push({
          ...item,
          stepQuote: item.stepQuote || null
        })
      })
      return resList
    }
  }
}
</script>

<style scoped lang="scss">
.operate-content {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
}
.mt-form {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  .grid_flex {
    width: 679px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.operate-bar {
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #4f5b6d;
  font-size: 14px;
  padding: 20px;
}
.operate-bars {
  width: 20px;
  height: 20px;
  background: rgba(0, 70, 156, 0.1);
  border: 1px solid rgba(0, 70, 156, 0.9);
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;

  i {
    width: 6px;
    height: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: rotate(90deg);
  }
}
.top-info {
  min-height: 100px;
  flex-wrap: wrap;
  display: flex;
  flex-shrink: 0;
  background: rgba(245, 248, 251, 1);
  border-radius: 0 8px 0 0;
  position: relative;
  .lf-wrap {
    flex: 1;
    transition: all 2s ease-in-out;
    background: linear-gradient(rgba(99, 134, 193, 0.06), rgba(99, 134, 193, 0.06)),
      linear-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 1));
    .detail-info {
      padding: 20px 20px 3px 20px;
      display: flex;
      line-height: 1;

      .name-wrap {
        flex: 1;
        .first-line {
          display: flex;
          align-items: center;
          .code {
            font-size: 20px;
            font-family: DINAlternate;
            font-weight: bold;
            color: rgba(41, 41, 41, 1);
          }
        }
      }

      .btns-wrap {
        /deep/ .mt-button {
          margin-right: 0;
          button {
            background: transparent;
            //border: 1px solid rgba(0, 70, 156, 0.1);
            border-radius: 4px;
            box-shadow: unset;
            padding: 6px 12px 4px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: 500;
            color: rgba(0, 70, 156, 1);
          }
        }
      }
    }
  }
  .mian-info {
    padding: 20px;
    padding-bottom: 10px;
  }
}
.form-generator-form {
  display: flex;
  flex-flow: row wrap;
  row-gap: 20px;
  margin: 0 -10px;
  .form-generator-form-item {
    padding-left: 10px;
    padding-right: 10px;
    margin-bottom: 0;
  }

  .form-generator-item-col-4 {
    display: block;
    flex: 0 0 25%;
    max-width: 25%;
  }
  .form-generator-item-col-2 {
    display: block;
    flex: 0 0 50%;
    max-width: 50%;
  }
  .form-generator-item-col-1 {
    display: block;
    flex: 0 0 100%;
    max-width: 100%;
  }
}
.relation-ships {
  flex: 1;
  background: rgba(255, 255, 255, 1);
}
.btns-wrap {
  /deep/ .mt-button {
    margin-right: 0;
    button {
      background: transparent;
      //border: 1px solid rgba(0, 70, 156, 0.1);
      border-radius: 4px;
      box-shadow: unset;
      padding: 6px 12px 4px;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(0, 70, 156, 1);
    }
  }
}
.top-shrink {
  color: #9bb0cb;
  display: flex;
  justify-content: center;
}
/deep/.bg-grey {
  background: #f5f5f5;
}
/deep/.bg-orange {
  background: #fdf5ea;
}
</style>
