import { i18n } from '@/main.js'
import { createEditInstance } from '@/utils/ej/dataGrid'
const editInstance = createEditInstance()
const MAX_SAFE_INTEGER = 9999999999
export const columnData = [
  {
    type: 'checkbox',
    width: '60',
    allowEditing: false
  },
  {
    width: 0,
    field: 'addId', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    headerText: i18n.t('addId主键'),
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true, // 一定要有主键，不然已有的行会被修改掉。。
    allowEditing: false
  },
  {
    field: 'stepValue',
    headerText: i18n.t('阶梯数量'),
    edit: editInstance.create({
      getEditConfig: () => ({
        type: 'number',
        min: 0,
        precision: 2,
        max: MAX_SAFE_INTEGER
      })
    })
  }
  // {
  //   field: 'remark',
  //   headerText: i18n.t('说明'),
  //   allowEditing: true,
  //   edit: editInstance.create({
  //     getEditConfig: () => ({
  //       type: 'text',
  //       maxlength: 20
  //     })
  //   })
  // }
]
