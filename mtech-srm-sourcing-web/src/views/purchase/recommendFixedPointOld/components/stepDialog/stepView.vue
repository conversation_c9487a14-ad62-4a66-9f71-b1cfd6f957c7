<template>
  <div class="viewBox">
    <div v-if="data.id || data.priceObjectCode === 'reference_pricing'">
      {{ data.stepValue || data.stepValue === 0 ? data.stepValue : '-' }}
    </div>
    <div v-else>{{ $t('阶梯报价') }}（{{ dataSource.length }}）</div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      data: {},
      dataSource: []
    }
  },
  mounted() {
    // 采供方取值不通
    this.data.itemStageList?.length && (this.dataSource = [...this.data.itemStageList])
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.viewBox {
  width: calc(100% + 20px);
  margin-left: 3px;
  // margin-right: -10px;
  &-btn {
    width: 100%;
    display: flex;
    justify-content: center;
    margin: 3px 0;
    div {
      color: rgba(0, 70, 156, 1);
    }
  }
}
/deep/ .mt-data-grid {
  .e-grid {
    th.e-headercell,
    td.e-rowcell {
      font-size: 8px !important;
      height: 30px;
    }
  }
}
/deep/ .e-headertext {
  font-size: 8px !important;
}
/deep/ .e-headercelldiv {
  height: 12px !important;
  line-height: 12px !important;
  padding: 0 !important;
}
</style>
