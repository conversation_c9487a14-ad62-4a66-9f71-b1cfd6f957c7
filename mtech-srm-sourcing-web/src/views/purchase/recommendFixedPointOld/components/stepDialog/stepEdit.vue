<template>
  <!-- 结构阶梯格式编辑 -->
  <div class="cellBox">
    <div class="dt-wrapper">
      <div
        v-if="
          (data.stepValue && data.stepValue !== 0) ||
          (data.itemStageList && data.itemStageList.length)
        "
      >
        <mt-input v-if="data.stepValue && data.stepValue !== 0" :value="data.stepValue" disabled />
        <div
          v-if="!data.id"
          class="cellBox-btn"
          style="color: rgba(0, 70, 156, 1); cursor: pointer"
          @click="showDialog"
        >
          {{ $t('编辑阶梯数量方案') }}
        </div>
      </div>
      <div
        v-else
        class="cellBox-btn"
        style="color: rgba(0, 70, 156, 1); cursor: pointer"
        @click="showDialog"
      >
        {{ $t('新增阶梯数量方案') }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    field: {
      type: String,
      default: ''
    },
    dataInfo: {
      type: Object,
      default: () => null
    },
    pointId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      data: {}
    }
  },
  mounted() {
    this.data = { ...this.dataInfo }
  },
  methods: {
    canEditStepValue() {
      const item = { ...this.data }
      if (!item.supplierCode) {
        this.$toast({
          content: this.$t(`请先选择供应商`),
          type: 'warning'
        })
        return
      }
      if (!item.itemCode) {
        this.$toast({
          content: this.$t(`请先选择物料`),
          type: 'warning'
        })
        return
      }
      if (!item.referChannel && item.referChannel !== 0) {
        this.$toast({
          content: this.$t(`请先选择价格来源`),
          type: 'warning'
        })
        return
      }
      if (!item.untaxedUnitPrice && item.untaxedUnitPrice !== 0) {
        this.$toast({
          content: this.$t(`单价未税必填`),
          type: 'warning'
        })
        return
      }
      if (!item.priceUnitName) {
        this.$toast({
          content: this.$t(`价格单位必填`),
          type: 'warning'
        })
        return
      }
      if (!item.deliveryPlace) {
        this.$toast({
          content: this.$t(`直送地必填`),
          type: 'warning'
        })
        return
      }
      if (item.id && item.stepQuote == 1) {
        if (!item.stepQuoteType && item.stepQuoteType !== 0) {
          this.$toast({
            content: this.$t(`阶梯类型必填`),
            type: 'warning'
          })
          return
        }
        if (!item.stepValue && item.stepValue !== 0) {
          this.$toast({
            content: this.$t(`阶梯数量必填`),
            type: 'warning'
          })
          return
        }
      }
      if (item.referChannel === 2 && !item.referItemCode) {
        this.$toast({
          content: this.$t(`价格来源为价格记录时参考物料必填`),
          type: 'warning'
        })
        return
      }
      if (!item.bidCurrencyCode) {
        this.$toast({
          content: this.$t(`币种必填`),
          type: 'warning'
        })
        return
      }
      if (!item.bidTaxRateCode) {
        this.$toast({
          content: this.$t(`税率必填`),
          type: 'warning'
        })
        return
      }
      if (!item.unitCode) {
        this.$toast({
          content: this.$t(`基本单位编码必填`),
          type: 'warning'
        })
        return
      }
      if (!item.purUnitName) {
        this.$toast({
          content: this.$t(`订单单位必填`),
          type: 'warning'
        })
        return
      }
      if (!item.quoteAttribute && item.quoteAttribute !== 0) {
        this.$toast({
          content: this.$t(`报价属性必填`),
          type: 'warning'
        })
        return
      }
      if (!item.quoteMode && item.quoteMode !== 0) {
        this.$toast({
          content: this.$t(`价格生效方式必填`),
          type: 'warning'
        })
        return
      }
      if (!item.minPackageQuantity && item.minPackageQuantity !== 0) {
        this.$toast({
          content: this.$t(`最小包装量/MPQ必填`),
          type: 'warning'
        })
        return
      }
      if (!item.minPurQuantity && item.minPurQuantity !== 0) {
        this.$toast({
          content: this.$t(`最小采购量/MOQ必填`),
          type: 'warning'
        })
        return
      }
      if (!item.leadTime && item.leadTime !== 0) {
        this.$toast({
          content: this.$t(`L/T必填`),
          type: 'warning'
        })
        return
      }
      if (!item.unconditionalLeadTime && item.unconditionalLeadTime !== 0) {
        this.$toast({
          content: this.$t(`无条件L/T必填`),
          type: 'warning'
        })
        return
      }
      if (!item.quoteEffectiveStartDate) {
        this.$toast({
          content: this.$t(`价格生效日期必填`),
          type: 'warning'
        })
        return
      }
      if (!item.quoteEffectiveEndDate) {
        this.$toast({
          content: this.$t(`价格失效日期必填`),
          type: 'warning'
        })
        return
      }
      if (item.quoteEffectiveStartDate > item.quoteEffectiveEndDate) {
        this.$toast({
          content: this.$t(`开始日期不能大于失效日期`),
          type: 'warning'
        })
        return
      }
      return true
    },
    showDialog() {
      if (!this.canEditStepValue()) {
        return
      }
      this.$dialog({
        modal: () => import('./index.vue'),
        data: {
          title: this.$t('阶梯数量'),
          pointId: this.pointId,
          data: this.data
        },
        success: (res) => {
          this.$bus.$emit(`${this.field}Change`, res)
        }
      })
    }
  }
}
</script>
<style lang="scss"></style>
