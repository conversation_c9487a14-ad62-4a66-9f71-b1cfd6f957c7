<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
      />
    </div>
  </mt-dialog>
</template>

<script>
import { columnData } from './config'
import { v1 as uuidv1 } from 'uuid'
export default {
  data() {
    return {
      data: {},
      dataSource: [{ stepValue: 1 }, { stepValue: 500 }, { stepValue: 2000 }, { stepValue: 3000 }],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [['Add', 'Delete']]
          },
          gridId: 'ec1bc6c0-ab61-478b-ad34-2a23b67b1ed9',
          grid: {
            columnData: columnData,
            dataSource: [],
            allowFiltering: false,
            allowPaging: false,
            allowSorting: false,
            editSettings: {
              allowAdding: true,
              allowEditing: true,
              allowDeleting: true,
              allowEditOnDblClick: true,
              newRowPosition: 'Bottom'
            }
          }
        }
      ],
      rowData: null
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    showLeft() {
      return false
    }
  },
  mounted() {
    const keysList = Object.keys(this.modalData.data)
    this.dataSource.forEach((item) => {
      keysList.forEach((keyName) => {
        if (
          keyName !== 'id' &&
          keyName !== 'stepValue' &&
          keyName !== 'column' &&
          keyName !== 'index' &&
          keyName !== 'pointItemId' &&
          keyName !== 'pointItemExtId' &&
          keyName !== 'addId'
        ) {
          item[keyName] = this.modalData.data[keyName]
          item['addId'] = uuidv1()
        }
      })
    })
    if (this.modalData.data.itemStageList?.length) {
      this.$set(this.pageConfig[0].grid, 'dataSource', this.modalData.data.itemStageList)
      this.$refs['dialog'].ejsRef.show()
    } else if (this.modalData.data && this.modalData.data.itemGroupId) {
      // 调获取阶梯数量的接口
      const params = {
        page: { current: 1, size: 10000 },
        defaultRules: [
          {
            label: this.$t('定点推荐id'),
            field: 'point.id',
            type: 'string',
            operator: 'equal',
            value: this.modalData.data.pointId
          },
          {
            label: this.$t('阶梯分组id'),
            field: 'bidding_item.item_group_id',
            type: 'string',
            operator: 'equal',
            value: this.modalData.data.itemGroupId
          }
        ]
      }
      this.$API.rfxList
        .queryItemByGroupId(params)
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            if (data && data.length) {
              const tableData = data.map((i) => {
                return {
                  ...i,
                  addId: uuidv1()
                }
              })
              this.$set(this.pageConfig[0].grid, 'dataSource', tableData)
            } else {
              this.$set(this.pageConfig[0].grid, 'dataSource', this.dataSource)
            }
            this.$refs['dialog'].ejsRef.show()
          }
        })
        .catch(() => {
          this.$set(this.pageConfig[0].grid, 'dataSource', this.dataSource)
          this.$refs['dialog'].ejsRef.show()
        })
    } else {
      this.$set(this.pageConfig[0].grid, 'dataSource', this.dataSource)
      this.$refs['dialog'].ejsRef.show()
    }
  },
  methods: {
    // 结束编辑状态
    endEdit() {
      let _current = this.$refs.templateRef.getCurrentTabRef()
      _current?.grid.endEdit()
      _current?.grid.hideSpinner()
    },

    confirm() {
      this.endEdit()
      let dataSource = this.$refs.templateRef.getCurrentTabRef().grid.getCurrentViewRecords()
      for (let i = 0; i < dataSource.length; i++) {
        let _currentNum = dataSource[i].stepValue
          ? Number(dataSource[i].stepValue)
          : dataSource[i].stepValue
        if (!_currentNum && _currentNum !== 0) {
          this.$toast({
            content: this.$t('阶梯数量不能为空'),
            type: 'warning'
          })
          return
        }
        if (i > 0 && _currentNum <= Number(dataSource[i - 1].stepValue)) {
          this.$toast({
            content: this.$t('阶梯数量不能小于等于上一行值'),
            type: 'warning'
          })
          return
        }
      }
      this.$emit('confirm-function', dataSource)
    },
    cancel() {
      this.$emit('cancel-function')
    },
    handleClickToolBar(e) {
      // this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      if (e.toolbar.id == 'Add') {
        let _currentTabRef = this.$refs.templateRef.getCurrentTabRef()
        let _selectRecords = _currentTabRef?.grid?.getCurrentViewRecords() ?? []
        this.pageConfig[0].grid.dataSource.push({
          ...this.modalData.data,
          addId: uuidv1(),
          stepValue: ''
        })
        // 未知原因新增下一行时上一行的结束值与说明丢失
        if (_selectRecords.length > 0) {
          let stepValue = _selectRecords[_selectRecords.length - 1].stepValue
          let remark = _selectRecords[_selectRecords.length - 1].remark
          let _index = this.pageConfig[0].grid.dataSource.length - 2
          this.pageConfig[0].grid.dataSource[_index].stepValue = stepValue
          this.pageConfig[0].grid.dataSource[_index].remark = remark
        }
        return
      }
      if (e.toolbar.id == 'Delete') {
        let _selectGridRecords = e.gridRef.getMtechGridRecords()
        if (_selectGridRecords?.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        this.handleBatchDelete(_selectGridRecords)
      }
    },
    //删除
    handleBatchDelete(_records) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          let _currentTabRef = this.$refs.templateRef.getCurrentTabRef()
          let _selectRecords = _currentTabRef?.grid?.getCurrentViewRecords() ?? []
          _records.forEach((x) => {
            _selectRecords.forEach((v, i) => {
              if (JSON.stringify(x) === JSON.stringify(v)) {
                _selectRecords.splice(i, 1)
              }
            })
          })
          this.$set(this.pageConfig[0].grid, 'dataSource', _selectRecords)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
