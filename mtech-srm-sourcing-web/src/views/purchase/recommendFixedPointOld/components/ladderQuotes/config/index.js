import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '60',
    type: 'checkbox'
  },
  {
    field: 'startValue',
    headerText: i18n.t('阶梯开始')
  },
  {
    field: 'endValue',
    headerText: i18n.t('阶梯结束')
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('未税单价')
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('含税单价')
  }
]

export const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'del', icon: 'icon_solid_Cancel', title: i18n.t('删除') }
]
export const toolbarTwo = []
