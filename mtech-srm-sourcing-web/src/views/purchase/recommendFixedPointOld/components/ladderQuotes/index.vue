<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
      />
    </div>
  </mt-dialog>
</template>

<script>
import { columnData, toolbar, toolbarTwo } from './config'
import { isNullOrUndefined } from '@/utils/is'
export default {
  data() {
    return {
      isEdit: false,
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: [],
          useBaseConfig: false,
          grid: {
            allowFiltering: true,
            lineIndex: true,
            asyncConfig: null,
            columnData: columnData,
            actionBegin: this.actionBegin,
            actionComplete: this.actionComplete,
            dataSource: [],
            editSettings: {
              allowAdding: true,
              allowEditing: true,
              allowDeleting: true,
              mode: 'Normal',
              allowEditOnDblClick: true,
              showConfirmDialog: false,
              showDeleteConfirmDialog: true,
              newRowPosition: 'Top'
            }
          }
        }
      ],
      buttons: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  watch: {
    modalData: {
      handler(newVal) {
        console.log(5050505, newVal)
      }
    }
  },

  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  beforeMount() {
    this.url()
    if (this.modalData.pricingType == 0) {
      if (this.modalData.status == 0) {
        if (this.modalData.stepQuote != 0) {
          this.$set(this.pageConfig[0], 'toolbar', [toolbar])
          this.buttons = [
            {
              click: this.cancel,
              buttonModel: { content: this.$t('取消') }
            },
            {
              click: this.confirm,
              buttonModel: { isPrimary: 'true', content: this.$t('确定') }
            }
          ]
        } else {
          this.$set(this.pageConfig[0], 'toolbar', [toolbarTwo])
          this.buttons = [
            {
              click: this.cancel,
              buttonModel: { content: this.$t('取消') }
            }
          ]
        }
      } else {
        this.$set(this.pageConfig[0], 'toolbar', [toolbarTwo])
        this.buttons = [
          {
            click: this.cancel,
            buttonModel: { content: this.$t('取消') }
          }
        ]
      }
    } else {
      this.buttons = [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        }
      ]
      this.$set(this.pageConfig[0], 'toolbar', [toolbarTwo])
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    url() {
      if (this.modalData.pricingType == 0) {
        this.pageConfig[0].grid.asyncConfig = {
          url: this.$API.rfxList.ladderList,
          params: { pointBiddingItemId: this.modalData.id }
        }
      } else {
        this.pageConfig[0].grid.asyncConfig = {
          url: this.$API.rfxList.ladderListV2,
          params: { rfxBiddingItemId: this.modalData.id }
        }
      }
    },
    actionBegin() {},
    actionComplete({ requestType }) {
      if (['beginEdit', 'add'].includes(requestType)) {
        this.isEdit = true
      } else {
        this.isEdit = false
      }
    },
    validateRecords(records) {
      const toast = (content, i) => {
        this.$toast({
          content: `第${i + 1}行:${content}`,
          type: 'warning'
        })
      }
      let minValue, maxValue
      // 在范围内
      const includesVal = (val) => val < maxValue && val > minValue

      for (let index = 0; index < records.length; index++) {
        const { startValue, endValue } = records[index]
        if (isNullOrUndefined(startValue) || isNullOrUndefined(endValue)) {
          toast(this.$t('阶梯开始或阶梯结束不能为空'), index)
          return false
        }
        if (Number(startValue) > Number(endValue)) {
          toast(this.$t('阶梯开始不能大于阶梯结束'), index)
          return false
        }
        if (!isNullOrUndefined(minValue) && includesVal(Number(startValue))) {
          toast(this.$t('阶梯开始输入有误'), index)
          return false
        }
        if (!isNullOrUndefined(maxValue) && includesVal(Number(endValue))) {
          toast(this.$t('阶梯结束输入有误'), index)
          return false
        }

        minValue = Number(startValue)
        maxValue = Number(endValue)
      }
      return true
    },
    async confirm() {
      if (this.isEdit) {
        this.$toast({ content: this.$t('请先退出编辑'), type: 'warning' })
        return
      }
      const grid = this.$refs.templateRef.getCurrentTabRef().grid
      console.log(136, grid.getCurrentViewRecords())
      let _selectGridRecords = grid.getCurrentViewRecords()
      if (!this.validateRecords(_selectGridRecords)) {
        return
      }
      _selectGridRecords.forEach((e) => (e.stepType = '2'))
      let param = {
        biddingItemStageSaveDTOList: _selectGridRecords,
        pointBiddingItemId: this.modalData.id
      }
      this.$API.rfxList.ladderListAdd(param).then(() => {
        this.$emit('confirm-function')
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    handleClickToolBar(e) {
      if (e.toolbar.id == 'Add') {
        if (this.modalData.pricingType == 0) {
          e.grid.addRecord()
          let params = {
            biddingItemStageSaveDTOList: [
              {
                endValue: '0',
                remark: '0',
                startValue: '1',
                stepType: '2',
                taxedUnitPrice: 0,
                untaxedUnitPrice: 0
              }
            ],
            pointBiddingItemId: this.modalData.id
          }
          this.$API.rfxList.ladderListAdd(params).then(() => {})
        }
      } else {
        let _selectGridRecords = e.gridRef.getMtechGridRecords()
        if (_selectGridRecords.length <= 0) {
          this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        } else {
          let rfxPricingDataSourceId = []
          _selectGridRecords.map((item) => {
            if (item.id) {
              rfxPricingDataSourceId.push(item.id)
            }
          })
          let params = {
            ids: rfxPricingDataSourceId
          }
          this.$API.rfxList.ladderListdelete(params).then(() => {
            this.$toast({ content: this.$t('删除成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  height: 100%;
  width: 100%;
}
</style>
