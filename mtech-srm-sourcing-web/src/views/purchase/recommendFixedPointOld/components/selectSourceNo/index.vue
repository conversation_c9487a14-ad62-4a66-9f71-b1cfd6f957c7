<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <mt-input :id="fieldName" v-model="data['referItemCode']" disabled :width="130"></mt-input>
      <mt-icon
        v-if="allowEditing"
        style="width: 20px"
        name="icon_list_refuse"
        @click.native="handleClear"
      />
      <mt-icon
        v-if="allowEditing"
        style="width: 20px"
        name="icon_input_search"
        @click.native="showDialog"
      />
    </div>
  </div>
</template>

<script>
import { getValueByPath, getEndDate } from '@/utils/obj'
export default {
  props: {
    field: {
      type: String,
      default: ''
    },
    dataInfo: {
      type: Object,
      default: () => null
    },
    companyCode: {
      type: String,
      default: ''
    },
    companyName: {
      type: String,
      default: ''
    },
    priceObjectCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      data: {},
      fieldName: '',
      headerTxt: '',
      allowEditing: true,
      fieldMap: {
        supplierCode: 'supplierCode',
        supplierId: 'supplierId',
        supplierName: 'supplierName',
        taxedUnitPrice: 'taxedUnitPrice',
        untaxedUnitPrice: 'untaxedUnitPrice',
        deliveryPlace: 'deliveryPlace',
        quoteAttribute: 'quoteAttribute',
        quoteMode: 'quoteMode',
        referLineNo: 'referLineNo',
        bidTaxRateCode: 'bidTaxRateCode',
        bidTaxRateName: 'bidTaxRateName',
        bidTaxRateValue: 'bidTaxRateValue',
        referSourceCode: 'priceRecordCode',
        referItemCode: 'itemCode',
        referItemName: 'itemName',
        referItemUnitPriceTaxed: 'taxedUnitPrice',
        referItemUnitPriceUntaxed: 'untaxedUnitPrice',
        historyUntaxedUnitPrice: 'historyUntaxedUnitPrice',
        priceGroupCode: 'priceGroupCode',
        stepValue: 'stepValue',
        priceUnitName: 'priceUnitName',
        minPackageQuantity: 'minPackageQuantity',
        minPurQuantity: 'minPurQuantity',
        leadTime: 'leadTime',
        unconditionalLeadTime: 'unconditionalLeadTime',
        quoteEffectiveStartDate: 'quoteEffectiveStartDate',
        quoteEffectiveEndDate: 'quoteEffectiveEndDate',
        purUnitCode: 'purUnitCode',
        purUnitName: 'purUnitName'
      }
    }
  },
  mounted() {
    if (this.dataInfo) {
      this.data = { ...this.dataInfo }
    }
    this.fieldName = this.data.column?.field || this.field
    this.allowEditing = this.data.column?.allowEditing === false ? false : true
    this.$bus.$on('changeReferChannel', (val) => {
      if (val == 0) {
        this.allowEditing = false
      } else {
        this.allowEditing = true
      }
    })
    if (!this.allowEditing) return
  },
  methods: {
    handleClear() {
      this.$set(this.data, 'priceReferRfxCodeAndLineNo', null)
      this.$bus.$emit('clearPriceReferRfxCodeAndLineNo')

      this.$set(this.data, 'referItemCode', null)
      Object.entries(this.fieldMap).map(([field]) => {
        this.$bus.$emit(`${field}Change`, null)
      })
    },
    showDialog(isShow) {
      let _itemCode = sessionStorage.getItem('currentPriceItemCode')
      let _referChannel = sessionStorage.getItem('pricingReferChannel')
      if (_referChannel === '0') {
        this.$toast({
          content: this.$t('价格来源为手工录入不可操作'),
          type: 'warning'
        })
        return
      }
      if (!_itemCode && this.$route?.name == 'contract-price-detail') {
        this.$toast({
          content: this.$t('请先选择物料'),
          type: 'warning'
        })
      } else {
        this.$dialog({
          modal: () => import('./components/selectGrid'),
          data: {
            title: this.$t('价格记录'),
            organizationId: sessionStorage.getItem('organizationId'),
            itemCode: _itemCode,
            isShow,
            route: this.$route,
            data: this.data,
            companyName: this.companyName,
            priceObjectCode: this.priceObjectCode
          },
          success: (data) => {
            this.$set(this.data, 'referSourceCode', data[0]['priceRecordCode'])
            this.$set(this.data, 'referItemCode', data[0]['itemCode'])
            let _data = data[0]
            // 生效日期起、止不取价格记录
            let _start = new Date().getTime()
            let _endDate = ['1503', '0602'].includes(this.companyCode)
              ? getEndDate(_start)
              : '9999-12-31'
            _data = Object.assign({}, _data, {
              quoteEffectiveStartDate: _start,
              quoteEffectiveEndDate: _endDate
            })
            Object.entries(this.fieldMap).map(([field, key]) => {
              this.$bus.$emit(`${field}Change`, getValueByPath(_data, key))
            })
            this.$bus.$emit('priceRecordChange', data[0].groupDataList || [])
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
.optionBtn {
  color: #6386c1;
  cursor: pointer;
  margin-right: 10px;
}
</style>
