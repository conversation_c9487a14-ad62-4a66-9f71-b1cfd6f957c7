<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-template-page ref="templateRef" :template-config="pageConfig" />
    </div>
  </mt-dialog>
</template>

<script>
import { columnData, columnDataNoStage } from './config'
export default {
  data() {
    return {
      data: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.modalData.isShow ? [] : this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    if (this.modalData.route?.name == 'contract-price-detail') {
      this.pageConfig = [
        {
          useToolTemplate: false,
          toolbar: [],
          grid: {
            allowFiltering: true,
            columnData: columnDataNoStage,
            allowSorting: false,
            allowSelection: true,
            selectionSettings: {
              type: 'Multiple',
              mode: 'Row'
            },
            recordDoubleClick: this.recordDoubleClick,
            asyncConfig: {
              url: this.$API.priceService.getReferList,
              params: { type: 1 },
              defaultRules: [
                {
                  field: 'record.itemCode',
                  type: 'string',
                  operator: 'equal',
                  value: this.modalData.itemCode
                }
              ]
            }
          }
        }
      ]
    } else if (
      this.modalData.route?.name == 'fixed-point' &&
      this.modalData.route?.query.decidePriceType == 5
    ) {
      this.pageConfig = [
        {
          useToolTemplate: false,
          toolbar: [],
          grid: {
            allowFiltering: true,
            columnData: columnDataNoStage,
            allowSorting: false,
            allowSelection: true,
            selectionSettings: {
              type: 'Multiple',
              mode: 'Row'
            },
            recordDoubleClick: this.recordDoubleClick,
            asyncConfig: {
              url: this.$API.priceService.getReferList,
              params: { type: 2 },
              defaultRules: [
                {
                  label: this.$t('物料编码'),
                  field: 'record.itemCode',
                  type: 'string',
                  operator: 'equal',
                  value: this.modalData.data.itemCode
                },
                {
                  label: this.$t('价格类型'),
                  field: 'record.priceValueType',
                  type: 'string',
                  operator: 'equal',
                  value: '4'
                }
              ]
            }
          }
        }
      ]
      this.buttons = [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('确定') }
        }
      ]
    } else {
      let templateColumnData = [...columnData]
      templateColumnData[0].ignore = true

      const defaultRules =
        this.modalData.priceObjectCode === 'reference_pricing'
          ? [
              {
                field: 'companyName',
                label: this.$t('公司名称'),
                operator: 'equal',
                type: 'string',
                value: this.modalData.companyName
              },

              {
                field: 'priceType',
                label: this.$t('价格类型'),
                operator: 'notequal',
                type: 'string',
                value: 10
              },
              {
                field: 'priceType',
                label: this.$t('价格类型'),
                operator: 'notequal',
                type: 'string',
                value: 11
              }
            ]
          : [
              {
                field: 'companyName',
                label: this.$t('公司名称'),
                operator: 'equal',
                type: 'string',
                value: this.modalData.companyName
              },
              {
                field: 'stageType',
                label: this.$t('阶梯类型'),
                operator: 'in',
                type: 'string',
                value: [0, 1, 2, 3]
              },
              {
                field: 'priceType',
                label: this.$t('价格类型'),
                operator: 'in',
                type: 'string',
                value: [10, 11]
              }
            ]
      this.pageConfig = [
        {
          useToolTemplate: false,
          toolbar: [],
          gridId: 'b9674431-2077-4ddb-ba61-7f365fcce0e5',
          grid: {
            allowFiltering: true,
            columnData: templateColumnData,
            allowSorting: false,
            allowSelection: true,
            selectionSettings: {
              type: 'Multiple',
              mode: 'Row'
            },
            recordDoubleClick: this.recordDoubleClick,
            asyncConfig: {
              url: this.$API.priceService.getReferListNewTv,
              params: { type: 1 },
              defaultRules,
              serializeList: (list) => {
                return this.groupData(list)
              }
            },
            queryCellInfo: this.queryCellInfo
          }
        }
      ]
    }
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    recordDoubleClick(e) {
      e.rowData.groupDataList = [...this.getGroupDataList(e.rowData.priceGroupCode)]
      this.$emit('confirm-function', [e.rowData])
    },
    confirm() {
      let _records = this.$refs.templateRef.getCurrentTabRef().gridRef.getMtechGridRecords()
      if (_records.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      _records[0].groupDataList = [...this.getGroupDataList(_records[0].priceGroupCode)]
      this.$emit('confirm-function', _records)
    },
    cancel() {
      this.$emit('cancel-function')
    },
    // 对数据进行分组
    groupData(list) {
      const kindList = []
      const tempList = []
      const resList = []
      list.forEach((item) => {
        const groupByKey = item.priceGroupCode
        if (!kindList.includes(groupByKey)) {
          kindList.push(groupByKey)
          tempList.push([])
        }
        const i = kindList.indexOf(groupByKey)
        tempList[i].push(item)
      })
      tempList.forEach((item) => {
        item[0].rowSpan = item.length
        resList.push(...item)
      })
      return resList
    },
    queryCellInfo(args) {
      // 不合并的单元格
      const arr = ['stepValue', 'taxedUnitPrice', 'untaxedUnitPrice']
      if (args.data.rowSpan && !arr.includes(args.column.field)) {
        args.rowSpan = args.data.rowSpan
      }
      args.column.field !== 'companyName' && (args.cell.style.borderLeft = '1px solid #e0e0e0')
    },
    getGroupDataList(groupCode) {
      const tableList = this.$refs.templateRef.getCurrentUsefulRef().ejsRef.getCurrentViewRecords()
      const groupDataList = tableList.filter((item) => item.priceGroupCode === groupCode)
      return groupDataList
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  // padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
