<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <mt-select
        v-if="showSelect"
        :id="fieldName"
        :allow-filtering="true"
        :data-source="itemsDataSources"
        :filtering="onFiltering"
        :fields="{ text: 'text', value: 'supplierCode' }"
        :value="value"
        :width="130"
        :disabled="!allowEditing"
        @select="handleSelectChange"
        :placeholder="headerTxt"
        popup-width="280px"
      ></mt-select>
      <mt-input
        :id="fieldName"
        v-model="data[fieldName]"
        disabled
        :width="130"
        :placeholder="headerTxt"
        v-show="!showSelect"
      ></mt-input>
      <mt-icon
        style="width: 20px"
        name="icon_list_refuse"
        @click.native="handleClear"
        v-if="allowEditing"
      ></mt-icon>
      <mt-icon
        v-if="allowEditing"
        style="width: 20px"
        name="icon_input_search"
        @click.native="showDialog"
      ></mt-icon>
    </div>
  </div>
</template>

<script>
import debounce from 'lodash.debounce'
import { getValueByPath } from '@/utils/obj'

export default {
  props: {
    field: {
      type: String,
      default: ''
    },
    rowData: {
      type: Object,
      default: () => null
    },
    purchaseOrgCode: {
      type: String,
      default: ''
    },
    companyCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      data: {},
      fieldName: '',
      headerTxt: '',
      allowEditing: true,
      itemsDataSources: [],
      showSelect: true,
      value: ''
    }
  },
  async mounted() {
    this.init()
  },
  methods: {
    async init() {
      this.showSelect = false
      this.fieldName = this.field || ''
      if (this.rowData) {
        this.data = { ...this.rowData }
      }
      this.value = this.data?.supplierCode

      await this.getDataSource()
      this.allowEditing = this.data?.column?.allowEditing === false ? false : true
      if (!this.allowEditing) return
      this.$bus.$on(`${this.fieldName}Clear`, () => {
        this.handleClear()
      })
    },
    // 获取下拉列表
    async getDataSource(supplierCode) {
      const res = await this.$API.masterData.getStandardSupplierInfoList({
        purchaseOrgCode: this.purchaseOrgCode,
        companyCode: this.companyCode,
        categoryCode: this.rowData.categoryCode,
        searchText: supplierCode || this.value,
        page: {
          current: 1,
          size: 10
        }
      })
      if (res.code === 200) {
        res.data.records?.forEach((item) => {
          const { supplierCode, supplierName } = item
          item.text = supplierCode + '-' + supplierName
        })
        this.itemsDataSources = [...this.itemsDataSources, ...res.data.records]
      }
      this.showSelect = true
    },
    onFiltering: debounce(function (e) {
      this.$API.masterData
        .getStandardSupplierInfoList({
          purchaseOrgCode: this.purchaseOrgCode,
          companyCode: this.companyCode,
          categoryCode: this.rowData.categoryCode,
          searchText: e.text,
          page: {
            current: 1,
            size: 10
          }
        })
        .then((res) => {
          res.data.records?.forEach((item) => {
            const { supplierCode, supplierName } = item
            item.text = supplierCode + '-' + supplierName
          })
          e.updateData(res.data.records)
        })
    }, 1000),
    handleSelectChange(e) {
      if (e.e !== null) {
        this.updateRowData(e.itemData)
      }
    },
    handleClear() {
      this.updateRowData({})
    },
    showDialog() {
      this.showSelect = false
      this.$dialog({
        modal: () => import('./components/selectGrid'),
        data: {
          title: this.$t('供应商'),
          purchaseOrgCode: this.purchaseOrgCode,
          companyCode: this.companyCode,
          categoryCode: this.rowData.categoryCode
        },
        success: (data) => {
          this.getDataSource(data[0]?.supplierCode)
          this.value = data[0] ? data[0].supplierCode : null
          const fieldMap = {
            supplierId: 'id',
            supplierCode: 'supplierCode',
            supplierName: 'supplierName',
            quoteAttribute: 'offerAttribute',
            quoteMode: 'priceEffectiveMode'
          }
          Object.entries(fieldMap).map(([field, key]) => {
            this.$bus.$emit(`${field}Change`, getValueByPath(data[0], key))
          })
        },
        close: () => {
          this.showSelect = true
        }
      })
    },
    updateRowData(data) {
      this.value = data ? data.supplierCode : null
      this.$bus.$emit('supplierNameChange', data)
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
</style>
