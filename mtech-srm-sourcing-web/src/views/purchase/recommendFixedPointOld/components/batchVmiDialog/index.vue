<template>
  <mt-dialog
    ref="dialog"
    css-class="small-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <div class="dialog-content">
      <mt-form ref="ruleForm" :model="formObject">
        <mt-form-item prop="orgCode" :label="$t('VMI标识')" label-style="left">
          <mt-radio v-model="formObject.allowAppeal" :data-source="radioData"></mt-radio>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
import Vue from 'vue'
import MtRadio from '@mtech-ui/radio'
Vue.use(MtRadio)
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      dialogTitle: this.$t('批量VMI'),
      formObject: {
        allowAppeal: null
      },
      radioData: [
        {
          label: this.$t('是'),
          value: 'Y'
        },
        {
          label: this.$t('否'),
          value: 'N'
        }
      ]
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    onOpen(args) {
      args.preventFocus = true
    },
    handleClose() {
      this.$emit('cancel-function')
    },
    save() {
      if (!this.formObject.allowAppeal) {
        this.$toast({
          content: this.$t('请先选择VMI标识'),
          type: 'warning'
        })
        return
      }
      // 调驳回接口，成功回调之后，调用下方的方法关闭弹窗
      this.$emit('confirm-function', this.formObject.allowAppeal)
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding: 32px 6px 0 6px !important;
}
</style>
