import { getValueByPath, getEndDate } from '@/utils/obj'
import { createEditInstance, Formatter } from '@/utils/ej/dataGrid/index'
import selectedItemCode from 'COMPONENTS/NormalEdit/selectItemCode' // 物料
import cellChanged from 'COMPONENTS/NormalEdit/cellChanged' // 单元格被改变（纯展示）
import { i18n } from '@/main.js'
import { useFiltering } from '@/utils/ej/select'
import selectSourceNo from '../selectSourceNo/index.vue' // 来源单号
import stepEdit from '../stepDialog/stepEdit.vue' // 阶梯数量
import stepView from '../stepDialog/stepView.vue' // 阶梯合并单元格查看
import Decimal from 'decimal.js'
import debounce from 'lodash.debounce'
import { v1 as uuidv1 } from 'uuid'
import { makeTextFields, addArrTextField } from '@/views/common/columnData/utils'
import { mergeDataSource } from '@/utils/ej/dataGrid/utils'
import axios from 'axios'
import selectSupplier from '../selectSupplier'

const _isKtFlag = sessionStorage.getItem('isKtFlag')
export const columnDataOne = (
  that,
  {
    supplierList = [],
    directDelivery = [],
    allCurrency = [],
    taxList = [],
    unitNameList = [],
    pagedQueryUnit
  } = {},
  bus
) => {
  const referChannelList = [
    {
      text: i18n.t('手工定价'),
      value: 0
    },
    // {
    //   text: i18n.t("引用寻源结果"),
    //   value: 1,
    // },
    {
      text: i18n.t('价格记录'),
      value: 2
    }
  ]
  const quoteAttributeList = [
    {
      text: i18n.t('标准价'),
      value: 'standard_price'
    },
    {
      text: i18n.t('寄售价'),
      value: 'mailing_price'
    }
  ]
  const quoteModeList = [
    {
      text: i18n.t('按照入库'),
      value: 'in_warehouse'
    },
    {
      text: i18n.t('按出库'),
      value: 'out_warehouse'
    },
    {
      text: i18n.t('按订单日期'),
      value: 'order_date'
    }
  ]
  const editInstance = createEditInstance()
    .component('selectedItemCode', selectedItemCode)
    .component('selectSourceNo', selectSourceNo)
    .component('stepEdit', stepEdit)
    .component('selectSupplier', selectSupplier)
    .onInput((ctx, { field, value, row }) => {
      sessionStorage.setItem('directRowData', JSON.stringify(editInstance.rowData))
      const pricingSiteInfo = sessionStorage.getItem('pricingSiteInfo')
        ? JSON.parse(sessionStorage.getItem('pricingSiteInfo'))
        : {}
      const gridCpnt = that.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.ej2Instances
      const rowIndex = gridCpnt?.getRowInfo(row).rowIndex
      if (field === 'supplierName') {
        // 供应商
        const { dataSource, fields } = ctx.getOptions(field)
        const rowItem = dataSource.find((e) => e[fields?.value || 'value'] === value)
        if (rowItem) {
          ctx.setValueByField('supplierCode', rowItem.supplierCode)
          ctx.setValueByField('supplierId', rowItem.id)
          sessionStorage.setItem('pricingSupplierCode', rowItem.supplierCode)
        }
      } else if (field === 'bidCurrencyName') {
        const { dataSource, fields } = ctx.getOptions(field)
        const rowItem = dataSource.find((e) => e[fields?.value || 'value'] === value)
        ctx.setValueByField('bidCurrencyCode', rowItem.value)
      } else if (field === 'siteName') {
        // 工厂被编辑
        const { dataSource, fields } = ctx.getOptions(field)
        const rowItem = dataSource.find((e) => e[fields?.value || 'value'] === value)
        if (rowItem) {
          ctx.setValueByField('siteCode', rowItem.orgCode)
          ctx.setValueByField('siteId', rowItem.id)
          sessionStorage.setItem('organizationId', rowItem.id)
          sessionStorage.setItem('pricingSiteCode', rowItem.orgCode)
        }
      } else if (field == 'untaxedUnitPrice') {
        setTaxedUnitPrice()
      } else if (field == 'taxedUnitPrice') {
        setUnTaxedUnitPrice()
      } else if (field == 'referChannel') {
        if (value == '2' || _isKtFlag == 0) {
          ctx.setOptions('taxedUnitPrice', {
            readonly: true,
            disabled: true
          })
          ctx.setOptions('untaxedUnitPrice', {
            readonly: true,
            disabled: true
          })
        } else {
          ctx.setOptions('taxedUnitPrice', {
            readonly: false,
            disabled: false
          })
          ctx.setOptions('untaxedUnitPrice', {
            readonly: false,
            disabled: false
          })
        }
      } else if (field === 'quoteEffectiveStartDate') {
        if (['1503', '0602'].includes(pricingSiteInfo.companyCode)) {
          // 1503公司特殊判断
          let _endDate = getEndDate(value)
          editInstance.setValueByField('quoteEffectiveEndDate', _endDate)
          gridCpnt && gridCpnt.updateCell(rowIndex, 'quoteEffectiveEndDate', _endDate)
        }
      }
      addRowModifyFlag(editInstance.rowData)
    })
    .onChange(async (ctx, { field, row }, event) => {
      const val = event.value === null ? '' : event.value
      const gridCpnt = that.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.ej2Instances
      const rowIndex = gridCpnt?.getRowInfo(row).rowIndex
      if (field === 'stepQuote') {
        bus.$emit('changeStepQuote', val)
        ctx.setOptions('untaxedUnitPrice', {
          readonly: false,
          disabled: false
        })
        if (val == 0) {
          ctx.setValueByField('stepQuoteType', null)
          ctx.setOptions('stepQuoteType', {
            readonly: true,
            disabled: true
          })
        } else {
          ctx.setOptions('stepQuoteType', {
            readonly: false,
            disabled: false
          })
        }
      } else if (field === 'stepQuoteType') {
        bus.$emit('changeStepQuoteType', val)
      } else if (field === 'referChannel') {
        bus.$emit('changeReferChannel', val)
        sessionStorage.setItem('pricingReferChannel', val)
        if (val === 2) {
          editInstance.setOptions('untaxedUnitPrice', {
            disabled: true,
            readonly: true
          })
          editInstance.setOptions('stepQuote', {
            disabled: false,
            readonly: false
          })
        } else {
          editInstance.setOptions('untaxedUnitPrice', {
            disabled: _isKtFlag == 0,
            readonly: false
          })
          editInstance.setOptions('stepQuote', {
            disabled: true,
            readonly: true
          })
          const pricingSiteInfo = sessionStorage.getItem('pricingSiteInfo')
            ? JSON.parse(sessionStorage.getItem('pricingSiteInfo'))
            : {}
          if (
            pricingSiteInfo.priceObjectCode === 'POWER_PANEL_SEND_OUT' &&
            !editInstance.getValueByField('untaxedUnitPrice')
          ) {
            editInstance.setValueByField('untaxedUnitPrice', 12000)
          }
        }
      } else if (field === 'bidTaxRateName') {
        let taxRateInfo = taxList.find((e) => e.taxItemName == val)
        editInstance.setValueByField('bidTaxRateValue', taxRateInfo.taxRate)
        editInstance.setValueByField('bidTaxRateCode', taxRateInfo.taxItemCode)
        setTaxedUnitPrice()
        setUnTaxedUnitPrice()

        if (gridCpnt) {
          const rowIndex = gridCpnt?.getRowInfo(row).rowIndex
          gridCpnt.updateCell(rowIndex, 'bidTaxRateValue', taxRateInfo.taxRate)
          gridCpnt.updateCell(rowIndex, 'bidTaxRateCode', taxRateInfo.taxItemCode)
        }
      } else if (field === 'purUnitName') {
        editInstance.setValueByField('purUnitCode', event.itemData?.unitCode)
        editInstance.setValueByField('purUnitId', event.itemData?.id)
      } else if (field === 'bidCurrencyName') {
        ctx.setValueByField('bidCurrencyCode', event.itemData?.value || null)
        gridCpnt && gridCpnt.updateCell(rowIndex, 'bidCurrencyCode', event.itemData?.value || null)
      }

      addRowModifyFlag(editInstance.rowData)
      sessionStorage.setItem('directRowData', JSON.stringify(editInstance.rowData))
    })
  const busFields = [
    'bidTaxRateCode',
    'bidTaxRateName',
    'bidTaxRateValue',
    'supplierCode',
    'pointBiddingItemStage',
    'supplierId',
    'supplierName',
    'deliveryPlace',
    'purUnitName',
    'purUnitCode',
    'itemName',
    'itemCode',
    'itemId',
    'taxedUnitPrice',
    'untaxedUnitPrice',
    'bidCurrencyCode',
    'bidCurrencyName',
    'categoryCode',
    'categoryId',
    'categoryName',
    'leadTime',
    'lineIndexComponent',
    'minPackageQuantity',
    'minPurQuantity',
    'priceGroupCode',
    'priceUnitName',
    'quoteAttribute',
    'quoteEffectiveEndDate',
    'quoteEffectiveStartDate',
    'quoteMode',
    'referChannel',
    'referItemCode',
    'referItemName',
    'referItemUnitPriceTaxed',
    'referItemUnitPriceUntaxed',
    'referSourceCode',
    'spec',
    'stepQuote',
    'stepQuoteType',
    'stepValue',
    'unconditionalLeadTime',
    'unitCode',
    'unitName',
    'vmi',
    'historyUntaxedUnitPrice'
  ]
  busFields.forEach((busField) => {
    bus.$on(`${busField}Change`, async (data) => {
      const gridCpnt = that.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.ej2Instances
      const rowIndex = gridCpnt?.getRowIndexByPrimaryKey(editInstance.rowData?.addId)
      let isUpdateFlag = true // 是否更新单元格

      if (busField == 'pointBiddingItemStage') {
        editInstance.setValueByField('untaxedUnitPrice', data[0].untaxedUnitPrice)
        setTaxedUnitPrice()
        if (data.length > 0) {
          editInstance.setValueByField('stepQuote', 1)
          editInstance.setValueByField('stepQuoteType', 1)
          editInstance.setValueByField('biddingItemStageStrName', JSON.stringify(data))
          editInstance.setValueByField('pointBiddingItemStage', data)
        }
      } else if (busField == 'purUnitName' && typeof data === 'object') {
        editInstance.setValueByField('purUnitName', data.baseMeasureUnitName)
        editInstance.setValueByField('purUnitCode', data.baseMeasureUnitCode)
        editInstance.setValueByField('itemId', data.id)
        editInstance.setValueByField('itemCode', data.itemCode)
        editInstance.setValueByField('itemName', data.itemName)
        editInstance.setValueByField('spec', data.itemDescription)
        editInstance.setValueByField('material', data.materialTypeName)
        editInstance.setValueByField('unitCode', data.baseMeasureUnitCode)
        editInstance.setValueByField('unitName', data.baseMeasureUnitName)
        editInstance.setValueByField('categoryId', data.categoryResponse.id)
        editInstance.setValueByField('categoryCode', data.categoryResponse.categoryCode)
        editInstance.setValueByField('categoryName', data.categoryResponse.categoryName)
        let row
        if (data.baseMeasureUnitCode) {
          row = unitNameList.find((e) => e.unitCode === data.baseMeasureUnitCode)
        }
        if (row) {
          setTimeout(() => {
            editInstance.setValueByField('purUnitName', row.unitName)
          }, 0)
        }
        if (editInstance.getValueByField('supplierCode')) {
          setTimeout(() => {
            getDefaultValueAndTaxRate()
          }, 10)
        }
      } else if (busField == 'deliveryPlace') {
        editInstance.setValueByField('deliveryPlace', data)
      } else if (busField == 'referSourceCode') {
        editInstance.setValueByField('referSourceCode', data)
      } else if (busField === 'stepValue') {
        // 保存阶梯数量
        if (typeof data !== 'object') {
          editInstance.setValueByField(busField, data)
        } else {
          updateItemStageList(data)
          isUpdateFlag = false
        }
      } else if (busField == 'supplierName') {
        if (data && typeof data === 'object') {
          const fieldMap = {
            supplierId: 'id',
            supplierCode: 'supplierCode',
            supplierName: 'supplierName',
            quoteAttribute: 'offerAttribute',
            quoteMode: 'priceEffectiveMode'
          }
          Object.entries(fieldMap).map(([field, key]) => {
            editInstance.setValueByField(field, data ? data[key] : null)
            // field !== busField 时进行updateCell，否则页面会卡住无法进入编辑状态
            field !== busField && gridCpnt?.updateCell(rowIndex, field, data ? data[key] : null)
          })
          isUpdateFlag = false
        } else {
          editInstance.setValueByField(busField, data)
        }
        setTimeout(() => {
          if (editInstance.getValueByField('supplierCode')) {
            getDefaultValueAndTaxRate(editInstance.rowData.categoryCode)
          }
        }, 10)
      } else if (busField === 'unitName') {
        editInstance.setValueByField(busField, data)

        editInstance.setValueByField('purUnitName', data)
        gridCpnt && gridCpnt.updateCell(rowIndex, 'purUnitName', data)
      } else if (busField === 'itemCode') {
        if (data && typeof data === 'object') {
          editInstance.setValueByField(busField, data.itemCode)
          const fieldMap = {
            itemId: 'id',
            itemCode: 'itemCode',
            itemName: 'itemName',
            spec: 'itemDescription', // 规格描述
            material: 'materialTypeName', // 材质
            unitCode: 'baseMeasureUnitCode',
            unitName: 'baseMeasureUnitName', // 单位
            categoryId: 'categoryResponse.id', //品类名称
            categoryCode: 'categoryResponse.categoryCode', //品类名称
            categoryName: 'categoryResponse.categoryName', //品类名称
            purUnitName: 'baseMeasureUnitName'
          }
          Object.entries(fieldMap).map(([field, key]) => {
            editInstance.setValueByField(field, getValueByPath(data, key))
            // field !== busField 时进行updateCell，否则页面会卡住无法进入编辑状态
            field !== busField && gridCpnt?.updateCell(rowIndex, field, getValueByPath(data, key))
          })
          isUpdateFlag = false
        } else {
          editInstance.setValueByField(busField, data)
        }
      } else {
        editInstance.setValueByField(busField, data)
      }

      // 合并单元格编辑，修改数据后需要更新单元格，否则无法看到数据修改
      // 清空单元格，无需对操作的单元格进行更新，仅需对其联动的单元格更新，否则会导致页面卡死
      const fieldList = ['referItemCode', 'itemCode']
      if (data || (!data && !fieldList.includes(busField))) {
        isUpdateFlag && gridCpnt && gridCpnt.updateCell(rowIndex, busField, data)
      }
      addRowModifyFlag(editInstance.rowData)
    })
    sessionStorage.setItem('directRowData', JSON.stringify(editInstance.rowData))
  })

  bus.$on('priceRecordChange', (dataList) => {
    updateItemStageList(dataList)
    addRowModifyFlag(editInstance.rowData)
  })

  // 添加修改行标记
  const addRowModifyFlag = (rowData) => {
    const itemGroupId = rowData?.itemGroupId
    rowData?.id && !that.modifyRecords.includes(itemGroupId) && that.modifyRecords.push(itemGroupId)
  }
  const updateItemStageList = (list) => {
    const gridCpnt = that.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.ej2Instances
    const rowIndex = gridCpnt?.getRowIndexByPrimaryKey(editInstance.rowData?.addId)
    const itemStageList = []
    let isStep = false
    list?.forEach((item) => {
      itemStageList.push({
        stepValue: item.stepValue
      })
      if (item.stepQuoteType > -1) isStep = true
    })
    editInstance.setValueByField('itemStageList', itemStageList)
    if (gridCpnt) {
      gridCpnt.updateCell(rowIndex, 'itemStageList', itemStageList)
      isStep && gridCpnt.updateCell(rowIndex, 'stepQuote', 1)
    }
  }

  const getDefaultValueAndTaxRate = debounce(() => {
    const gridCpnt = that.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.ej2Instances
    const rowIndex = gridCpnt?.getRowIndexByPrimaryKey(editInstance.rowData?.addId)
    const pricingSiteInfo = sessionStorage.getItem('pricingSiteInfo')
      ? JSON.parse(sessionStorage.getItem('pricingSiteInfo'))
      : {}
    axios
      .get(
        `/api/sourcing/tenant/point2/queryTaxRate?companyCode=${
          pricingSiteInfo.companyCode
        }&purOrgCode=${pricingSiteInfo.purOrgCode}&supplierCode=${editInstance.getValueByField(
          'supplierCode'
        )}`
      )
      .then((res) => {
        if (res && res.data) {
          const arr = {
            bidCurrencyName: 'currencyName',
            bidCurrencyCode: 'currencyCode',
            bidTaxRateName: 'taxRateName',
            bidTaxRateCode: 'taxRateCode',
            bidTaxRateValue: 'taxRateValue'
          }
          if (that.forObject.companyCode === '0602' && res.data.data['currencyCode'] === 'VND') {
            // 越南盾设置价格单位
            editInstance.setValueByField('priceUnitName', '1')
            gridCpnt && gridCpnt.updateCell(rowIndex, 'priceUnitName', 1)
          }
          Object.entries(arr).map(([field, key]) => {
            editInstance.setValueByField(field, res.data.data ? res.data.data[key] : null)
            gridCpnt &&
              gridCpnt.updateCell(rowIndex, field, res.data.data ? res.data.data[key] : null)
          })
        }
      })
  })

  const setTaxedUnitPrice = () => {
    let untaxedUnitPrice = editInstance.getValueByField('untaxedUnitPrice')
    let bidTaxRateValue = editInstance.getValueByField('bidTaxRateValue')
    if (!untaxedUnitPrice || !bidTaxRateValue) return
    let taxedUnitPrice = new Decimal(untaxedUnitPrice)
      .mul(new Decimal(bidTaxRateValue).add(1))
      .toNumber()
      .toFixed(2)
    editInstance.setValueByField('taxedUnitPrice', taxedUnitPrice)
  }
  const setUnTaxedUnitPrice = () => {
    let taxedUnitPrice = editInstance.getValueByField('taxedUnitPrice')
    let bidTaxRateValue = editInstance.getValueByField('bidTaxRateValue')
    if (!taxedUnitPrice || !bidTaxRateValue) return
    let untaxedUnitPrice = new Decimal(taxedUnitPrice)
      .div(new Decimal(bidTaxRateValue).add(1))
      .toNumber()
      .toFixed(2)
    editInstance.setValueByField('untaxedUnitPrice', untaxedUnitPrice)
  }
  return [
    {
      width: '60',
      type: 'checkbox'
    },
    {
      field: 'groupLineIndex',
      headerText: i18n.t('序号'),
      allowFiltering: false, // 序号列，不参与数据筛选
      allowResizing: false, // 序号列，不参与列顺序变化
      allowSorting: false, // 序号列，不参与列宽变化
      allowEditing: false, // 序号列，不参与数据编辑
      ignore: true, // 序号列，不参与数据筛选
      width: 70
    },
    {
      width: 0,
      field: 'addId', // 隐藏的主键，获取到数据源时，需要把id赋值给它
      headerText: i18n.t('addId主键'),
      visible: false,
      isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
      isPrimaryKey: true, // 一定要有主键，不然已有的行会被修改掉。。
      allowEditing: false
    },
    {
      width: 0,
      field: 'itemGroupId', // 前端逻辑控制增加的隐藏的组id
      headerText: i18n.t('itemGroupId'),
      ignore: true,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      width: 0,
      field: 'priceGroupCode', // 前端逻辑控制增加的隐藏的组id
      headerText: i18n.t('priceGroupCode'),
      ignore: true,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      width: 200,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'selectedItemCode',
          field: 'itemCode',
          dataInfo: editInstance.rowData,
          moduleType: 'zjdj'
        })
      })
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料名称'),
      width: 150,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    // {
    //   field: 'itemId',
    //   headerText: i18n.t('物料Id'),
    //   width: 1,
    //   ignore: true,
    //   editTemplate: () => {
    //     return {
    //       template: cellChanged
    //     }
    //   }
    // },
    {
      field: 'referChannel',
      headerText: i18n.t('价格来源'),
      width: 120,
      edit: editInstance.create({
        getEditConfig: ({ rowData }) => ({
          created: function () {
            setTimeout(() => {
              sessionStorage.setItem('pricingReferChannel', rowData.referChannel)
            })
          },
          type: 'mt-select',
          dataSource: referChannelList,
          placeholder: i18n.t('请选择价格来源')
          // onchange: (ctx, { value, field }) => {
          //   setTimeout(() => {
          //     if (ctx.rowData.referChannel === 2) {
          //       editInstance.setOptions('untaxedUnitPrice', {
          //         disabled: true
          //       })
          //     } else {
          //       editInstance.setOptions('untaxedUnitPrice', {
          //         disabled: _isKtFlag == 0
          //       })
          //       const pricingSiteInfo = sessionStorage.getItem('pricingSiteInfo')
          //         ? JSON.parse(sessionStorage.getItem('pricingSiteInfo'))
          //         : {}
          //       if (
          //         pricingSiteInfo.priceObjectCode === 'POWER_PANEL_SEND_OUT' &&
          //         !rowData.untaxedUnitPrice
          //       ) {
          //         editInstance.setValueByField('untaxedUnitPrice', 12000)
          //       }
          //     }
          //   }, 500)
          // }
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return referChannelList.find((e) => e.value === cellVal)?.text ?? cellVal
      }
    },
    {
      field: 'referItemCode',
      headerText: i18n.t('参考物料编码'),
      width: 200,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'selectSourceNo',
          field: 'referItemCode',
          dataInfo: editInstance.rowData,
          companyCode: that.forObject.companyCode,
          companyName: that.forObject.companyName,
          priceObjectCode: that.forObject.priceObjectCode
        })
      })
    },
    {
      field: 'referItemName',
      headerText: i18n.t('参考物料名称'),
      width: 150,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'stepQuote',
      headerText: i18n.t('是否阶梯报价'),
      width: 120,
      edit: editInstance.create({
        getEditConfig: ({ rowData }) => ({
          created: function () {
            setTimeout(() => {
              if (
                rowData.referChannel === 2 &&
                that.forObject.priceObjectCode !== 'reference_pricing'
              ) {
                editInstance.setOptions('stepQuote', {
                  disabled: false
                })
              } else {
                editInstance.setOptions('stepQuote', {
                  disabled: true
                })
              }
            })
          },
          disabled: true,
          type: 'mt-select',
          dataSource: [
            { text: i18n.t('否'), value: 0 },
            { text: i18n.t('是'), value: 1 }
          ],
          placeholder: i18n.t('是否阶梯报价')
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        switch (Number(cellVal)) {
          case 0:
            return i18n.t('否')
          case 1:
            return i18n.t('是')
          default:
            return cellVal
        }
      },
      valueConverter: {
        type: 'map',
        map: {
          0: i18n.t('否'),
          1: i18n.t('是')
        }
      }
    },
    {
      field: 'stepQuoteType',
      headerText: i18n.t('阶梯类型'),
      width: 120,
      edit: editInstance.create({
        getEditConfig: ({ rowData }) => ({
          created: function () {
            setTimeout(() => {
              if (rowData.stepQuote === 1) {
                editInstance.setOptions('stepQuoteType', {
                  disabled: false
                })
              } else {
                editInstance.setOptions('stepQuoteType', {
                  disabled: true
                })
              }
            })
          },
          type: 'select',
          'show-clear-button': true,
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { value: 0, text: i18n.t('数量累计阶梯') },
            { value: 3, text: i18n.t('数量逐层阶梯') }
          ],
          placeholder: i18n.t('请选择')
        })
      }),
      valueConverter: {
        type: 'map',
        map: {
          0: i18n.t('数量累计阶梯'),
          3: i18n.t('数量逐层阶梯'),
          null: '-',
          '-1': '-'
        }
      }
    },
    {
      field: 'itemStageList',
      headerText: i18n.t('阶梯数量'),
      width: 150,
      template: function () {
        return {
          template: stepView
        }
      },
      edit: editInstance.create({
        getEditConfig: () => ({
          type: that.forObject.priceObjectCode === 'reference_pricing' ? '' : 'stepEdit',
          field: 'stepValue',
          dataInfo: editInstance.rowData,
          pointId: that.$route?.query?.id
        })
      })
    },
    {
      field: 'untaxedUnitPrice',
      headerText: i18n.t('单价(未税)'),
      width: 120,
      edit: editInstance.create({
        getEditConfig: ({ rowData }) => ({
          type: 'number',
          readonly: _isKtFlag == 0 || rowData.referChannel === 2,
          disabled: _isKtFlag == 0 || rowData.referChannel === 2,
          created: function () {
            setTimeout(() => {
              if (rowData.referChannel === 2) {
                editInstance.setOptions('untaxedUnitPrice', {
                  disabled: true
                })
              } else {
                editInstance.setOptions('untaxedUnitPrice', {
                  disabled: _isKtFlag == 0
                })
                // const pricingSiteInfo = sessionStorage.getItem('pricingSiteInfo')
                //   ? JSON.parse(sessionStorage.getItem('pricingSiteInfo'))
                //   : {}
                // if (
                //   pricingSiteInfo.priceObjectCode === 'POWER_PANEL_SEND_OUT' &&
                //   !rowData.untaxedUnitPrice
                // ) {
                //   editInstance.setValueByField('untaxedUnitPrice', 12000)
                // }
              }
            })
          }
        })
      })
    },
    {
      field: 'taxedUnitPrice',
      headerText: i18n.t('单价(含税)'),
      width: 120,
      edit: editInstance.create({
        getEditConfig: ({ rowData }) => ({
          type: 'number',
          readonly: _isKtFlag == 0 || rowData.referChannel === 2,
          disabled: _isKtFlag == 0 || rowData.referChannel === 2
        })
      })
    },
    {
      field: 'historyUntaxedUnitPrice',
      headerText: i18n.t('历史价格（未税）'),
      width: 150,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          readonly: true,
          disabled: true
        })
      })
    },
    {
      field: 'referItemUnitPriceUntaxed',
      headerText: i18n.t('参考物料价格'),
      width: 150,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          readonly: true,
          disabled: true
        })
      })
    },
    {
      field: 'declinePercent',
      headerText: i18n.t('降幅%'),
      width: 150,
      allowEditing: false
    },
    {
      field: 'vmi',
      headerText: i18n.t('VMI标识'),
      width: 100,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          dataSource: [
            { text: i18n.t('否'), value: 'N' },
            { text: i18n.t('是'), value: 'Y' }
          ],
          placeholder: i18n.t('VMI标识')
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        switch (cellVal) {
          case 'N':
            return i18n.t('否')
          case 'Y':
            return i18n.t('是')
          default:
            return cellVal
        }
      }
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商'),
      width: 200,
      edit: editInstance.create({
        getEditConfig: ({ rowData }) => ({
          type: 'selectSupplier',
          field: 'supplierName',
          rowData,
          purchaseOrgCode: that.forObject.purOrgCode,
          companyCode: that.forObject.companyCode
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return supplierList.find((e) => e.value === cellVal)?.text ?? cellVal
      },
      searchOptions: {
        renameField: 'biddingItem.supplierName'
      }
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      width: 120,
      edit: editInstance.create({
        // eslint-disable-next-line no-unused-vars
        onChange: (ctx, { value, field }) => {
          setTimeout(() => {
            // const supplierCode = ctx.getValueByField('supplierCode')
            // const siteCode = ctx.getValueByField('siteCode')
            // const itemCode = ctx.getValueByField('itemCode')
            // if (supplierCode && siteCode && itemCode) {
            //   // 因为使用了cellChanged，只能通过bus修改
            //   bus.$emit('groupIdChange', `${supplierCode}-${siteCode}-${itemCode}`)
            // }
            if (!ctx.getValueByField('addId')) {
              ctx.setValueByField('addId', uuidv1())
            }
          }, 500)
        },
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      }),
      searchOptions: {
        renameField: 'biddingItem.supplierCode'
      }
    },
    {
      field: 'supplierId',
      headerText: i18n.t('供应商ID'),
      width: 0,
      ignore: true,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      }),
      searchOptions: {
        renameField: 'biddingItem.supplierId'
      }
    },
    {
      field: 'categoryCode',
      headerText: i18n.t('品类编码'),
      width: 120,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'categoryName',
      headerText: i18n.t('品类名称'),
      width: 150,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'categoryId',
      headerText: i18n.t('品类Id'),
      width: 0,
      ignore: true,
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'priceUnitName',
      headerText: i18n.t('价格单位'),
      width: 100,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          'show-clear-button': true,
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { value: '1', text: 1 },
            { value: '1000', text: 1000 }
          ],
          placeholder: i18n.t('请选择价格单位')
        })
      })
    },
    {
      field: 'deliveryPlace',
      headerText: i18n.t('直送地'),
      width: 150,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          dataSource: directDelivery,
          placeholder: i18n.t('直送地')
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return directDelivery.find((e) => e.value === cellVal)?.text ?? cellVal
      }
    },
    // {
    //   field: "costModelQuote",
    //   headerText: i18n.t("成本模型报价"),
    //   allowEditing: false,
    //   edit: editInstance.create({
    //     getEditConfig: () => ({
    //       type: "mt-select",
    //       // readonly: true,
    //       dataSource: [
    //         { text: i18n.t("否"), value: 0 },
    //         { text: i18n.t("是"), value: 1 },
    //       ],
    //       placeholder: i18n.t("成本模型报价"),
    //     }),
    //   }),
    //   formatter: ({ field }, item) => {
    //     const cellVal = getValueByPath(item, field);
    //     switch (Number(cellVal)) {
    //       case 0:
    //         return i18n.t("否");
    //       case 1:
    //         return i18n.t("是");
    //       default:
    //         return cellVal;
    //     }
    //   },
    // },
    // {
    //   field: "expertLevel",
    //   headerText: i18n.t("成本模板"),
    // },
    // {
    //   field: 'referSourceCode',
    //   headerText: i18n.t('参考来源单号'),
    //   editTemplate: () => {
    //     return {
    //       template: selectSourceNo
    //     }
    //   }
    // },
    // {
    //   field: 'biddingItemStageStrName',
    //   headerText: i18n.t('阶梯价格'),
    //   width: 400,
    //   allowEditing: true,
    //   template: function () {
    //     return {
    //       template: directCellView
    //     }
    //   },
    //   editTemplate: () => {
    //     return {
    //       template: directCell
    //     }
    //   }
    // },
    {
      field: 'referSourceCode',
      headerText: i18n.t('参考来源单号'),
      width: 0,
      ignore: true,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          readonly: true,
          disabled: true
        })
      })
    },

    {
      field: 'bidCurrencyName',
      headerText: i18n.t('币种'),
      width: 150,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          disabled: true,
          readonly: true,
          dataSource: allCurrency,
          fields: { text: 'text', value: 'text' },
          placeholder: i18n.t('请选择币种'),
          'allow-filtering': true,
          filtering: useFiltering(function (e) {
            if (typeof e.text === 'string' && e.text) {
              e.updateData(allCurrency.filter((f) => f?.text.indexOf(e.text) > -1))
            } else {
              e.updateData(allCurrency)
            }
          })
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return allCurrency.find((e) => e.value === cellVal)?.text ?? cellVal
      }
    },
    {
      field: 'bidCurrencyCode',
      headerText: i18n.t('币种编码'),
      width: 120,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'bidTaxRateName',
      headerText: i18n.t('税率名称'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          disabled: true,
          readonly: true,
          dataSource: taxList,
          placeholder: i18n.t('请选择税率'),
          'allow-filtering': true,
          fields: { text: 'taxItemName', value: 'taxItemName' },
          filtering: useFiltering(function (e) {
            if (typeof e.text === 'string' && e.text) {
              e.updateData(this.dataSource.filter((f) => f?.taxItemName.indexOf(e.text) > -1))
            } else {
              e.updateData(this.dataSource)
            }
          })
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return taxList.find((e) => e.taxItemCode === cellVal)?.taxItemName ?? cellVal
      }
    },
    {
      field: 'bidTaxRateCode',
      headerText: i18n.t('税率编码'),
      width: 120,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'bidTaxRateValue',
      headerText: i18n.t('税率值'),
      width: 120,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    // {
    //   field: 'unitName',
    //   headerText: i18n.t('基本单位'),
    //   allowEditing: false,
    //   editTemplate: () => {
    //     return {
    //       template: cellChanged
    //     }
    //   }
    // },
    {
      field: 'unitCode',
      headerText: i18n.t('基本单位编码'),
      width: 120,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'unitName',
      headerText: i18n.t('基本单位名称'),
      width: 0
    },
    {
      field: 'purUnitName',
      headerText: i18n.t('订单单位'),
      // editTemplate: () => {
      //   return {
      //     template: cellChanged
      //   }
      // },
      width: 120,
      edit: editInstance.create({
        valueConvert: (val, { options, column }) => {
          const dataSource = options.dataSource || []
          const row = dataSource.find((e) => e.unitName === val)
          const purUnitName = row?.unitName || ''
          editInstance.setValueByField(column.field, purUnitName)
          return purUnitName
        },
        getEditConfig: ({ rowData }) => ({
          type: 'select',
          'show-clear-button': true,
          fields: makeTextFields('unitName'),
          dataSource: unitNameList,
          placeholder: rowData.purUnitName,
          'allow-filtering': true,
          filtering: useFiltering(function (e) {
            pagedQueryUnit({
              condition: 'or',
              page: { current: 1, size: 20 },
              rules: [
                {
                  field: 'unitName',
                  type: 'string',
                  operator: 'contains',
                  value: e.text
                },
                {
                  field: 'unitCode',
                  type: 'string',
                  operator: 'contains',
                  value: e.text
                }
              ]
            }).then((res) => {
              if (Array.isArray(res?.data?.records)) {
                const records = addArrTextField(res.data.records, 'unitCode', 'unitName')
                mergeDataSource(editInstance, 'purUnitName', records)
                e.updateData(records)
              }
            })
          }),
          created: () => {
            let row
            if (rowData.purUnitCode) {
              row = unitNameList.find((e) => e.unitCode === rowData.purUnitCode)
            } else if (rowData.purUnitName) {
              row = unitNameList.find(
                (e) => e.unitName === rowData.purUnitName || e.unitCode === rowData.unitCode
              )
            }
            if (row) {
              setTimeout(() => {
                editInstance.setValueByField('purUnitName', row.unitName)
              }, 0)
            }
          }
        })
      })
    },
    {
      field: 'purUnitCode',
      headerText: i18n.t('订单单位编码'),
      width: 0,
      ignore: true,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text'
        })
      })
    },
    // {
    //   field: 'purUnitCode',
    //   headerText: i18n.t('订单单位编码'),
    //   allowEditing: false,
    //   width: 0,
    //   ignore: true,
    //   editTemplate: () => {
    //     return {
    //       template: cellChanged
    //     }
    //   }
    // },
    // {
    //   field: 'referLineNo',
    //   headerText: i18n.t('参考来源行号'),
    //   allowEditing: false
    // },
    {
      field: 'quoteAttribute',
      headerText: i18n.t('报价属性'),
      width: 120,
      edit: editInstance.create({
        getEditConfig: () => ({
          disabled: true,
          readonly: true,
          type: 'mt-select',
          dataSource: quoteAttributeList,
          placeholder: i18n.t('请选择报价属性')
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return quoteAttributeList.find((e) => e.value === cellVal)?.text ?? cellVal
      }
    },
    {
      field: 'quoteMode',
      headerText: i18n.t('价格生效方式'),
      width: 120,
      edit: editInstance.create({
        getEditConfig: () => ({
          disabled: true,
          readonly: true,
          type: 'mt-select',
          dataSource: quoteModeList,
          placeholder: i18n.t('请选择价格生效方式')
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return quoteModeList.find((e) => e.value === cellVal)?.text ?? cellVal
      }
    },
    {
      field: 'minPackageQuantity',
      headerText: i18n.t('最小包装量/MPQ'),
      width: 150,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number'
        })
      })
    },
    {
      field: 'minPurQuantity',
      headerText: i18n.t('最小采购量/MOQ'),
      width: 150,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number'
        })
      })
    },
    {
      field: 'leadTime',
      headerText: i18n.t('L/T'),
      width: 120,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number'
        })
      })
    },
    {
      field: 'unconditionalLeadTime',
      headerText: i18n.t('无条件L/T'),
      width: 120,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number'
        })
      })
    },
    {
      field: 'quoteEffectiveStartDate',
      headerText: i18n.t('生效日期'),
      width: 120,
      formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
      edit: editInstance.create({
        getEditConfig: ({ rowData }) => ({
          created: function () {
            setTimeout(() => {
              if (!rowData.quoteEffectiveStartDate && !rowData.id) {
                editInstance.setValueByField('quoteEffectiveStartDate', new Date().getTime())
              }
            })
          },
          type: 'date',
          format: 'yyyy-MM-dd',
          'time-stamp': true,
          // readonly: getReadOnly(),
          placeholder: i18n.t('请输入生效日期')
        })
      }),
      // format: "yyyy-MM-dd HH:mm:ss",
      // type: "date",
      searchOptions: {
        renameField: 'biddingItem.quoteEffectiveStartDate'
      }
    },
    {
      field: 'quoteEffectiveEndDate',
      headerText: i18n.t('失效日期'),
      width: 120,
      formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'date',
          format: 'yyyy-MM-dd',
          'time-stamp': true,
          // readonly: getReadOnly(),
          placeholder: i18n.t('请输入失效日期')
        })
      }),
      // format: "yyyy-MM-dd HH:mm:ss",
      // type: "date",
      searchOptions: {
        renameField: 'biddingItem.quoteEffectiveEndDate'
      }
    },
    {
      field: 'remark',
      headerText: i18n.t('备注'),
      width: 150,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text'
        })
      })
    }
    // {
    //   field: "biddingItemStageStrName",
    //   headerText: i18n.t("阶梯价格"),
    //   cssClass: "field-content",
    // },
    // {
    //   field: 'biddingItemStageStrName',
    //   headerText: i18n.t('阶梯价格'),
    //   width: 400,
    //   allowEditing: true,
    //   template: function () {
    //     return {
    //       template: directCellView
    //     }
    //   },
    //   editTemplate: () => {
    //     return {
    //       template: directCell
    //     }
    //   }
    // },
    // {
    //   field: 'requireQuantity',
    //   headerText: i18n.t('需求数量'),
    //   edit: editInstance.create({
    //     getEditConfig: () => ({
    //       type: 'number'
    //     })
    //   })
    // },
    // {
    //   field: 'purExecutorName',
    //   headerText: i18n.t('材质'),
    //   allowEditing: false,
    //   editTemplate: () => {
    //     return {
    //       template: cellChanged
    //     }
    //   }
    // }
  ]
}

export const columnDataTwo = (that) => {
  const editInstance = createEditInstance().onChange(async () => {
    addRowModifyFlag(editInstance.rowData)
  })
  const sourceTypeList = [
    {
      text: i18n.t('内部'),
      value: 0
    },
    {
      text: i18n.t('外部'),
      value: 1
    }
  ]
  // 添加修改行标记
  const addRowModifyFlag = (rowData) => {
    const { id, itemGroupId } = rowData
    id && !that.modifyRecords.includes(itemGroupId) && that.modifyRecords.push(itemGroupId)
  }
  return [
    {
      width: '60',
      type: 'checkbox',
      allowEditing: false
    },
    {
      field: 'groupLineIndex',
      headerText: i18n.t('序号'),
      allowFiltering: false, // 序号列，不参与数据筛选
      allowResizing: false, // 序号列，不参与列顺序变化
      allowSorting: false, // 序号列，不参与列宽变化
      allowEditing: false, // 序号列，不参与数据编辑
      ignore: true, // 序号列，不参与数据筛选
      width: 70
    },
    {
      field: 'id', // 隐藏的主键，获取到数据源时，需要把id赋值给它
      headerText: i18n.t('主键'),
      visible: false,
      isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
      isPrimaryKey: true, // 一定要有主键，不然已有的行会被修改掉。。
      allowEditing: false
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      width: 150,
      allowEditing: false,
      // edit: editInstance.create({
      //   getEditConfig: () => ({
      //     type: 'mt-select',
      //     dataSource: supplierList,
      //     fields: { value: 'supplierName', text: 'supplierName' },
      //     placeholder: i18n.t('供应商名称'),
      //     disabled: true,
      //     readonly: true
      //   })
      // }),
      // formatter: ({ field }, item) => {
      //   const cellVal = getValueByPath(item, field)
      //   return supplierList.find((e) => e.value === cellVal)?.text ?? cellVal
      // },
      searchOptions: {
        renameField: 'biddingItem.supplierName'
      }
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      width: 120,
      allowEditing: false,
      // edit: editInstance.create({
      //   getEditConfig: () => ({
      //     type: 'mt-select',
      //     dataSource: supplierList,
      //     fields: { value: 'supplierCode', text: 'supplierCode' },
      //     placeholder: i18n.t('供应商编码'),
      //     disabled: true,
      //     readonly: true
      //   })
      // }),
      // formatter: ({ field }, item) => {
      //   const cellVal = getValueByPath(item, field)
      //   return supplierList.find((e) => e.value === cellVal)?.text ?? cellVal
      // },
      searchOptions: {
        renameField: 'biddingItem.supplierCode'
      }
    },
    {
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      width: 150,
      allowEditing: false,
      // edit: editInstance.create({
      //   getEditConfig: () => ({
      //     type: 'mt-select',
      //     dataSource: siteNameDataSource,
      //     fields: { value: 'siteName', text: 'siteName' },
      //     placeholder: i18n.t('请选择工厂名称'),
      //     disabled: true,
      //     readonly: true
      //   })
      // }),
      // formatter: ({ field }, item) => {
      //   const cellVal = getValueByPath(item, field)
      //   return siteNameDataSource.find((e) => e.supplierName === cellVal)?.text ?? cellVal
      // },
      searchOptions: {
        renameField: 'item.siteName'
      }
    },
    {
      field: 'siteCode',
      headerText: i18n.t('工厂编码'),
      width: 120,
      allowEditing: false,
      // edit: editInstance.create({
      //   getEditConfig: () => ({
      //     type: 'mt-select',
      //     dataSource: siteNameDataSource,
      //     fields: { value: 'siteCode', text: 'siteCode' },
      //     placeholder: i18n.t('请选择工厂'),
      //     disabled: true,
      //     readonly: true
      //   })
      // }),
      // formatter: ({ field }, item) => {
      //   const cellVal = getValueByPath(item, field)
      //   return siteNameDataSource.find((e) => e.supplierName === cellVal)?.text ?? cellVal
      // },
      searchOptions: {
        renameField: 'item.siteCode'
      }
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      width: 150,
      allowEditing: false,
      editTemplate: () => {
        return {
          template: selectedItemCode
        }
      }
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料名称'),
      width: 150,
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
      // valueConverter: {
      //   type: "map",
      //   map: { 0: i18n.t("评分中"), 1: i18n.t("已提交"), 2: i18n.t("评分汇总") },
      // },
    },
    {
      field: 'allocationRatioText',
      headerText: i18n.t('配额比例（%）'),
      width: 150,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          precision: '0',
          min: 0,
          max: 100
        })
      }),
      formatter: ({ field }, item) => {
        let val = Number(item[field]).toFixed(0) || 0
        return val + '%'
      }
    },
    {
      field: 'stepValue',
      headerText: i18n.t('阶梯数量'),
      width: 120,
      allowEditing: false
    },
    {
      field: 'taxedUnitPrice',
      headerText: i18n.t('单价(含税)'),
      width: 120,
      allowEditing: false
    },
    {
      field: 'untaxedUnitPrice',
      headerText: i18n.t('单价(不含税)'),
      width: 120,
      allowEditing: false
    },
    {
      field: 'priceUnitName',
      headerText: i18n.t('价格单位'),
      width: 120,
      allowEditing: false
    },
    {
      field: 'quotaEffectiveStartDate',
      headerText: i18n.t('生效日期'),
      width: 120,
      formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'date',
          format: 'yyyy-MM-dd',
          disabled: true,
          readonly: true,
          placeholder: i18n.t('请输入生效日期')
        })
      }),
      format: 'yyyy-MM-dd HH:mm:ss',
      type: 'date',
      searchOptions: {
        renameField: 'biddingItem.quotaEffectiveStartDate'
      }
    },
    {
      field: 'quotaEffectiveEndDate',
      headerText: i18n.t('失效日期'),
      width: 120,
      formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'date',
          format: 'yyyy-MM-dd',
          disabled: true,
          readonly: true,
          placeholder: i18n.t('请输入失效日期')
        })
      }),
      format: 'yyyy-MM-dd HH:mm:ss',
      type: 'date',
      searchOptions: {
        renameField: 'biddingItem.quotaEffectiveEndDate'
      }
    },
    {
      field: 'minSplitQuantity',
      headerText: i18n.t('最小起拆点'),
      width: 120,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number'
        })
      })
    },
    {
      field: 'minPackageQuantity',
      headerText: i18n.t('最小包装量/MPQ'),
      width: 150,
      allowEditing: false
    },
    {
      field: 'minPurQuantity',
      headerText: i18n.t('最小采购量/MOQ'),
      width: 150,
      allowEditing: false
    },
    // {
    //   field: 'priceValueType',
    //   headerText: i18n.t('价格类型'),
    //   allowEditing: false
    // },
    {
      field: 'quoteAttribute',
      headerText: i18n.t('报价属性'),
      width: 120,
      allowEditing: false,
      edit: editInstance.create({
        getEditConfig: () => ({
          disabled: true,
          // readonly: true,
          type: 'mt-select',
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { text: i18n.t('标准价'), value: 'standard_price' },
            { text: i18n.t('寄售价'), value: 'mailing_price' },
            { text: i18n.t('委外价'), value: 'outsource' }
          ]
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        switch (cellVal) {
          case 'standard_price':
            return i18n.t('标准价')
          case 'mailing_price':
            return i18n.t('寄售价')
          case 'outsource':
            return i18n.t('委外价')
          default:
            return cellVal
        }
      }
    },
    {
      field: 'sourceType',
      headerText: i18n.t('来源'),
      width: 120,
      allowEditing: false,
      edit: editInstance.create({
        getEditConfig: () => ({
          disabled: true,
          readonly: true,
          type: 'mt-select',
          dataSource: sourceTypeList
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return sourceTypeList.find((e) => e.value === cellVal)?.text ?? cellVal
      }
    }
    // {
    //   field: 'allocationQuantity',
    //   headerText: i18n.t('分配数量'),
    //   edit: editInstance.create({
    //     getEditConfig: () => ({
    //       type: 'number'
    //     })
    //   })
    // },
    // {
    //   field: "allocationRatio",
    //   headerText: i18n.t("配额"),
    //   edit: editInstance.create({
    //     getEditConfig: () => ({
    //       type: "text",
    //     }),
    //   }),
    // },
    // {
    //   field: 'deliveryPlace',
    //   headerText: i18n.t('直送地'),
    //   edit: editInstance.create({
    //     getEditConfig: () => ({
    //       type: 'mt-select',
    //       disabled: true,
    //       readonly: true,
    //       dataSource: directDelivery,
    //       placeholder: i18n.t('直送地')
    //     })
    //   }),
    //   formatter: ({ field }, item) => {
    //     const cellVal = getValueByPath(item, field)
    //     return directDelivery.find((e) => e.value === cellVal)?.text ?? cellVal
    //   }
    // },
    // {
    //   field: 'stepQuote',
    //   headerText: i18n.t('是否阶梯报价'),
    //   edit: editInstance.create({
    //     getEditConfig: () => ({
    //       type: 'mt-select',
    //       // readonly: getReadOnly(),
    //       dataSource: [
    //         { text: i18n.t('否'), value: 0 },
    //         { text: i18n.t('是'), value: 1 }
    //       ],
    //       placeholder: i18n.t('是否阶梯报价'),
    //       disabled: true,
    //       readonly: true
    //     })
    //   }),
    //   formatter: ({ field }, item) => {
    //     const cellVal = getValueByPath(item, field)
    //     switch (Number(cellVal)) {
    //       case 0:
    //         return i18n.t('否')
    //       case 1:
    //         return i18n.t('是')
    //       default:
    //         return cellVal
    //     }
    //   },
    //   valueConverter: {
    //     type: 'map',
    //     map: {
    //       0: i18n.t('否'),
    //       1: i18n.t('是')
    //     }
    //   }
    // },
    // {
    //   field: "biddingItemStageStrName",
    //   headerText: i18n.t("阶梯价格"),
    //   cssClass: "field-content",
    //   allowEditing: false,
    // },
    // {
    //   field: 'biddingItemStageStrName',
    //   headerText: i18n.t('阶梯价格'),
    //   // cssClass: "field-content",
    //   // allowEditing: false,
    //   width: 400,
    //   allowFiltering: false,
    //   template: function () {
    //     return {
    //       template: directCellView
    //     }
    //   },
    //   editTemplate: () => {
    //     return {
    //       template: directCellView
    //     }
    //   }
    // },
    // {
    //   field: 'stepValue',
    //   headerText: i18n.t('阶梯值'),
    //   width: 400,
    //   allowEditing: false
    // },
    // {
    //   field: 'requireQuantity',
    //   headerText: i18n.t('需求数量'),
    //   allowEditing: false
    // },
    // {
    //   field: 'bidCurrencyName',
    //   headerText: i18n.t('币种'),
    //   width: 150,
    //   edit: editInstance.create({
    //     getEditConfig: () => ({
    //       type: 'mt-select',
    //       dataSource: allCurrency,
    //       disabled: true,
    //       readonly: true,
    //       placeholder: i18n.t('请选择币种'),
    //       'allow-filtering': true
    //     })
    //   }),
    //   formatter: ({ field }, item) => {
    //     const cellVal = getValueByPath(item, field)
    //     return allCurrency.find((e) => e.value === cellVal)?.text ?? cellVal
    //   }
    // },
    // {
    //   field: 'bidCurrencyCode',
    //   headerText: i18n.t('币种编码'),
    //   allowEditing: false
    // },
    // {
    //   field: 'bidTaxRateValue',
    //   headerText: i18n.t('税率'),
    //   allowEditing: false
    // },
    // {
    //   field: 'unitName',
    //   headerText: i18n.t('基本单位'),
    //   allowEditing: false,
    //   editTemplate: () => {
    //     return {
    //       template: cellChanged
    //     }
    //   }
    // },
    // {
    //   field: 'purUnitName',
    //   headerText: i18n.t('订单单位'),
    //   allowEditing: false,
    //   editTemplate: () => {
    //     return {
    //       template: cellChanged
    //     }
    //   }
    // },
    // {
    //   field: 'leadTime',
    //   headerText: i18n.t('L/T'),
    //   allowEditing: false
    // },
    // {
    //   field: 'unconditionalLeadTime',
    //   headerText: i18n.t('无条件L/T'),
    //   allowEditing: false
    // },
    // {
    //   field: 'priceUnitName',
    //   headerText: i18n.t('价格单位'),
    //   allowEditing: false
    // },
    // {
    //   field: 'deliveryPlace',
    //   headerText: i18n.t('直送地'),
    //   edit: editInstance.create({
    //     getEditConfig: () => ({
    //       type: 'mt-select',
    //       disabled: true,
    //       readonly: true,
    //       dataSource: directDelivery,
    //       placeholder: i18n.t('直送地')
    //     })
    //   }),
    //   formatter: ({ field }, item) => {
    //     const cellVal = getValueByPath(item, field)
    //     return directDelivery.find((e) => e.value === cellVal)?.text ?? cellVal
    //   }
    // },
    // {
    //   field: 'spec',
    //   headerText: i18n.t('规格描述'),
    //   allowEditing: false,
    //   editTemplate: () => {
    //     return {
    //       template: cellChanged
    //     }
    //   }
    // },
    // {
    //   field: 'material',
    //   headerText: i18n.t('材质'),
    //   allowEditing: false,
    //   editTemplate: () => {
    //     return {
    //       template: cellChanged
    //     }
    //   }
    // }
  ]
}
export const toolbar = (enabledAdd = false, priceObjectCode = '') => [
  {
    id: 'Add',
    icon: 'icon_solid_Createorder',
    title: i18n.t('新增'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'Save',
    icon: 'icon_solid_Save',
    title: i18n.t('保存'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'del',
    icon: 'icon_solid_Cancel',
    title: i18n.t('删除'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'import',
    icon: 'icon_solid_upload',
    title: i18n.t('导入'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'export',
    icon: 'icon_solid_Download ',
    title: i18n.t('导出')
  },
  {
    id: 'redirectOA',
    icon: 'icon_solid_Save',
    title: i18n.t('OA审批进度'),
    visibleCondition: () => !enabledAdd && priceObjectCode !== 'POWER_PANEL_SEND_OUT'
  },
  {
    id: 'batchVMI',
    icon: 'icon_solid_Save',
    title: i18n.t('批量VMI'),
    visibleCondition: () => enabledAdd
  }
]
export const toolbarTwo = (enabledAdd = false, priceObjectCode = '') => [
  {
    id: 'SaveTwo',
    icon: 'icon_solid_Save',
    title: i18n.t('保存'),
    visibleCondition: () => enabledAdd
  },

  {
    id: 'importTwo',
    icon: 'icon_solid_upload ',
    title: i18n.t('导入'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'exportTwo',
    icon: 'icon_solid_Download ',
    title: i18n.t('导出')
  },
  {
    id: 'redirectOA',
    icon: 'icon_solid_Save',
    title: i18n.t('OA审批进度'),
    visibleCondition: () => !enabledAdd && priceObjectCode !== 'POWER_PANEL_SEND_OUT'
  }
]
export const pageConfig = (url, id, serializeList, recordDoubleClick, queryCellInfo) => [
  {
    title: i18n.t('定价物料'),
    useToolTemplate: false,
    toolbar: toolbar(),
    grid: {
      allowFiltering: false,
      lineIndex: false,
      columnData: [],
      dataSource: [{ itemCode: 'itemCode' }],
      rowDataBound: (args) => {
        if (args.data.className) {
          args.row.classList.add(args.data.className)
        }
      },
      asyncConfig: {
        url,
        defaultRules: [
          {
            label: '定点推荐id',
            field: 'point.id',
            type: 'string',
            operator: 'equal',
            value: id
          }
        ],
        serializeList
      },
      recordDoubleClick,
      editSettings: {
        allowAdding: true,
        allowEditing: true,
        allowDeleting: true,
        mode: 'Batch',
        allowEditOnDblClick: true,
        showConfirmDialog: false,
        showDeleteConfirmDialog: true,
        newRowPosition: 'Top'
      },
      pageSettings: {
        pageSize: 100
      },
      queryCellInfo
    }
  },
  {
    title: i18n.t('配额分配'),
    useToolTemplate: false,
    toolbar: toolbarTwo(),
    grid: {
      allowFiltering: false,
      lineIndex: false,
      columnData: [],
      dataSource: [],
      rowDataBound: (args) => {
        if (args.data.className) {
          args.row.classList.add(args.data.className)
        }
      },
      asyncConfig: {
        url: '/sourcing/tenant/pointQuota/queryBuilder',
        defaultRules: [
          {
            label: '定点推荐id',
            field: 'pointId',
            type: 'long',
            operator: 'equal',
            value: id
          },
          {
            label: '定点推荐type',
            field: 'sourceDocType',
            type: 'string',
            operator: 'equal',
            value: 'tv_point'
          }
        ],
        serializeList
      },
      recordDoubleClick,
      editSettings: {
        allowAdding: true,
        allowEditing: true,
        allowDeleting: true,
        mode: 'Batch',
        allowEditOnDblClick: true,
        showConfirmDialog: false,
        showDeleteConfirmDialog: true,
        newRowPosition: 'Top'
      },
      queryCellInfo
    }
  }
]
