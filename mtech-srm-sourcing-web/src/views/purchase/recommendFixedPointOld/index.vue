<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { todoListToolBar, todoListColumnData } from './config'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  data() {
    return {
      pageConfig: [
        {
          toolbar: todoListToolBar,
          useToolTemplate: false,
          gridId: this.$permission.gridId['purchase']['recommendFixedPoint'],
          grid: {
            allowFiltering: true,
            asyncConfig: {
              url: this.$API.rfxList.getPagePointTv,
              defaultRules: [
                {
                  field: 'decidePriceType',
                  operator: 'in',
                  value: ['0']
                }
              ],
              afterAsyncData: this.updateTabTitle
            },
            lineIndex: true,
            columnData: todoListColumnData,
            dataSource: []
          }
        }
      ],
      isSubmit: false
    }
  },
  mounted() {},
  methods: {
    updateTabTitle() {},

    //新增
    handleAddFn() {
      this.$dialog({
        modal: () => import('./newAddComponents/index.vue'),
        data: {
          title: this.$t('创建定价'),
          type: 'recommendations'
        },
        success: (data) => {
          // this.$refs.templateRef.refreshCurrentGridData()
          if (data) {
            this.$router.push({
              name: 'direct-pricing-detail-tv',
              query: {
                id: data.id,
                decidePriceType: data.decidePriceType,
                status: 0,
                timeStamp: new Date().getTime()
              }
            })
          } else {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        }
      })
    },

    //删除
    handleDelFn(selectGridRecords, idList) {
      if (selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            let params = {
              idList: idList
            }
            this.$API.rfxList.deletePointListTV(params).then(() => {
              this.$toast({ content: this.$t('删除成功'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
            })
          }
        })
      }
    },

    //提交
    handleSubmitFn(selectGridRecords) {
      if (selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else if (selectGridRecords.length > 1) {
        this.$toast({ content: this.$t('只选择一行提交'), type: 'warning' })
      } else {
        if (selectGridRecords[0].status < 1) {
          if (this.isSubmit) {
            this.$toast({
              content: this.$t('正在提交中，请勿重复点击'),
              type: 'warning'
            })
            return
          }
          this.isSubmit = true
          let params = {
            id: selectGridRecords[0].id
          }
          this.$API.rfxList
            .submitPointTv(params)
            .then(() => {
              this.$toast({ content: this.$t('提交成功'), type: 'success' })
              this.isSubmit = false
              this.$refs.templateRef.refreshCurrentGridData()
            })
            .catch(() => {
              this.isSubmit = false
            })
        } else {
          this.$toast({
            content: this.$t('审批通过，不可重复提交'),
            type: 'warning'
          })
        }
      }
    },

    //表格按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      let idList = []
      _selectGridRecords.map((item) => {
        idList.push(item.id)
      })
      if (e.toolbar.id == 'Add') {
        this.handleAddFn() //新增
      }
      if (e.toolbar.id == 'del') {
        this.handleDelFn(_selectGridRecords, idList) //删除
      }
      if (e.toolbar.id == 'Submit') {
        this.handleSubmitFn(_selectGridRecords) //提交
      }
      if (e.toolbar.id === 'export') this.handleExport()
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
    },
    //单元格标题点击操作
    handleClickCellTitle(e) {
      if (e.field == 'pointNo') {
        this.$router.push({
          name: 'direct-pricing-detail-tv',
          query: {
            id: e.data.id,
            decidePriceType: e.data.decidePriceType,
            status: e.data.status,
            timeStamp: new Date().getTime()
          }
        })
      }
    },
    //导出
    handleExport() {
      // 先要点击“查询”按钮才能取得queryBuilderRules
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules,
        defaultRules: [
          {
            field: 'decidePriceType',
            operator: 'equal',
            value: 7
          }
        ].concat(queryBuilderRules.rules || [])
      } // 筛选条件
      this.$API.rfxList.reFpExportTv(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>
