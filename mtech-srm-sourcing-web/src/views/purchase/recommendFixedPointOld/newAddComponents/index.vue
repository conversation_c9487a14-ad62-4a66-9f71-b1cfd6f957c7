<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="modalData.title"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="appForm" :model="form.data" :rules="form.rules">
        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item prop="title" :label="$t('标题')">
              <mt-input
                type="text"
                v-model="form.data.title"
                :placeholder="$t('请输入标题')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="priceObjectName" :label="$t('定价对象')">
              <mt-select
                :value="form.data.priceObjectName"
                :fields="{ text: 'sourcingObj', value: 'templateCode' }"
                :data-source="form.dataSource.pricing"
                :placeholder="$t('请选择定价对象')"
                @input="inputPricing($event, 'priceObjectName')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="companyName" :label="$t('公司')">
              <!-- <mt-select
                v-model="form.data.companyName"
                :fields="{
                  text: 'orgName',
                  value: 'orgTypeCode',
                }"
                :data-source="form.dataSource.firm"
                :placeholder="$t('请选择公司')"
                @input="inputPricing($event, 'companyName')"
                :show-clear-button="true"
              ></mt-select> -->
              <mt-select
                v-model="form.data.companyName"
                float-label-type="Never"
                :data-source="form.dataSource.firm"
                :fields="{ text: 'codeAndName', value: 'orgName' }"
                @change="changeTaxItem"
                :placeholder="$t('请选择公司')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="purOrgName" :label="$t('采购组织')">
              <mt-select
                :value="form.data.purOrgName"
                :fields="{
                  text: 'codeAndName',
                  value: 'organizationCode'
                }"
                :data-source="form.dataSource.procurement"
                :placeholder="$t('请选择采购组织')"
                @input="inputPricing($event, 'purOrgName')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="siteName" :label="$t('工厂')">
              <mt-select
                :value="form.data.siteName"
                :fields="{
                  text: 'codeAndName',
                  value: 'orgName'
                }"
                :data-source="form.dataSource.siteList"
                :placeholder="$t('请选择工厂')"
                @input="inputPricing($event, 'siteName')"
                @change="siteNameChange"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="expandSaveRequestList" :label="$t('工厂扩展')">
              <mt-multi-select
                class="sourcing-expand"
                :data-source="form.dataSource.sourcingExpandList"
                :allow-filtering="true"
                filter-type="Contains"
                v-model="form.data.expandSaveRequestList"
                popup-width="470px"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="purId" :label="$t('采购员')">
              <!-- <mt-select
                :value="form.data.purId"
                :fields="{
                  text: 'employeeName',
                  value: 'employeeCode',
                }"
                :data-source="form.dataSource.personnel"
                :placeholder="$t('请选择采购员')"
                @input="inputPricing($event, 'purId')"
                :show-clear-button="true"
                :allow-filtering="true"
                :filtering="filteringPurId"
              ></mt-select> -->
              <debounce-filter-select
                v-model="form.data.purId"
                :request="getCurrentEmployees"
                :data-source="form.dataSource.personnel"
                @change="inputPricing($event, 'purId')"
                :show-clear-button="false"
                :fields="{ text: 'text', value: 'uid' }"
                :placeholder="$t('请选择采购员')"
              ></debounce-filter-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="decidePriceType" :label="$t('定价单类型')">
              <mt-select
                :value="form.data.decidePriceType"
                :data-source="form.dataSource.pricingType"
                :placeholder="$t('请选择定价单类型')"
                @input="inputPricing($event, 'decidePriceType')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="priceClassification" :label="$t('价格分类')">
              <mt-select
                :value="form.data.priceClassification"
                :data-source="form.dataSource.priceClassification"
                :placeholder="$t('请选择价格分类')"
                @input="inputPricing($event, 'priceClassification')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="remark" :label="$t('备注')">
              <mt-input
                type="text"
                v-model="form.data.remark"
                :placeholder="$t('请输入备注')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
import debounceFilterSelect from 'ROUTER_PURCHASE_RFX/components/debounceFilterSelect'
export default {
  components: {
    debounceFilterSelect
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.saveRule,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      form: {
        data: {
          companyName: '', // 公司
          companyCode: '',
          companyId: '',
          priceObjectName: '', // 定价对象
          priceObjectCode: '',
          priceObjectId: '',
          purOrgName: '', // 	采购组织
          purOrgCode: '',
          purOrgId: '',
          siteName: '', // 	工厂
          siteCode: '',
          siteId: '',
          expandSaveRequestList: [], // 工厂扩展
          decidePriceType: 0, //定价单类型
          id: '',
          pointNo: '', //定价单号
          remark: '',
          title: '',
          purId: '', //采购员
          purName: '',
          priceClassification: 4
        },
        rules: {
          //定价单类型
          decidePriceType: {
            required: true,
            message: this.$t('请选择定价单类型'),
            trigger: 'blur'
          },
          // 采购员
          purId: {
            required: true,
            message: this.$t('请选择采购员'),
            trigger: 'blur'
          },
          // 公司
          companyName: {
            required: true,
            message: this.$t('请选择公司'),
            trigger: 'blur'
          },
          // 	采购组织
          purOrgName: {
            required: true,
            message: this.$t('请选择采购组织'),
            trigger: 'blur'
          },
          // 	工厂
          siteName: {
            required: true,
            message: this.$t('请选择工厂'),
            trigger: 'blur'
          },
          // 定价对对像
          priceObjectName: {
            required: true,
            message: this.$t('请选择定价对象'),
            trigger: 'blur'
          },
          priceClassification: {
            required: true,
            message: this.$t('请选择价格分类'),
            trigger: 'blur'
          },
          title: {
            required: true,
            message: this.$t('请输入标题'),
            trigger: 'blur'
          }
        },
        dataSource: {
          pricing: [],
          procurement: [],
          personnel: [],
          firm: [],
          siteList: [],
          pricingType: [
            { text: this.$t('直接定价'), value: 0 }
            // { text: this.$t("寻源结果定价"), value: 1 },
          ],
          priceClassification: [
            { text: this.$t('基价'), value: 1 },
            { text: this.$t('SRM价格'), value: 2 },
            { text: this.$t('暂估价格'), value: 3 },
            { text: this.$t('执行价格'), value: 4 }
          ]
        }
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  beforeMount() {
    this.mergeFormData()
  },
  mounted() {
    this.getConfigList()
    // this.getUserPageList();
    this.getCurrentEmployees({ text: '' })
    this.getSpecifiedChildrenLevelOrgs()
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    inputPricing(val, type) {
      if (type == 'priceObjectName') {
        const arr = this.form.dataSource.pricing.find((e) => e.templateCode === val)
        if (arr) {
          this.form.data.priceObjectName = arr.sourcingObj
          this.form.data.priceObjectId = arr.id
          this.form.data.priceObjectCode = arr.templateCode
          if (arr.templateCode === 'POWER_PANEL_SEND_OUT') {
            this.form.data.priceClassification = 3
          }
        }
      } else if (type == 'purOrgName') {
        const arr = this.form.dataSource.procurement.find((e) => {
          return e.organizationCode == val
        })
        console.log(192, arr)
        if (arr) {
          this.form.data.purOrgName = arr.organizationName
          this.form.data.purOrgId = arr.id
          this.form.data.purOrgCode = arr.organizationCode
        }
        // 获取工厂列表
        if (this.form.data.purOrgId) {
          this.$API.masterData
            .permissionSiteList({
              buOrgId: this.form.data.purOrgId,
              companyId: this.form.data.companyId,
              orgLevelTypeCode: 'ORG06'
            })
            .then((res) => {
              if (res.data == null) {
                this.form.dataSource.siteList = []
              }
              const siteList = []
              res.data.forEach((item) => {
                siteList.push({
                  ...item,
                  codeAndName: item.orgCode + '-' + item.orgName,
                  siteAndPurchaseOrg: `${this.form.data.purOrgCode}${this.form.data.purOrgName} - ${item.orgCode}${item.orgName}`,
                  siteAndPurchaseOrgVal: `${this.form.data.purOrgCode}+${item.orgCode}`,
                  data: {
                    purOrgName: this.form.data.purOrgName,
                    purOrgCode: this.form.data.purOrgCode,
                    purOrgId: this.form.data.purOrgId,
                    siteCode: item.orgCode,
                    siteId: item.id,
                    siteName: item.orgName
                  }
                })
              })
              this.form.dataSource.siteList = siteList
              if (siteList.length === 1) {
                this.form.data.siteName = siteList[0].orgName
                this.form.data.siteCode = siteList[0].orgCode
                this.form.data.siteId = siteList[0].id
              } else {
                this.form.data.siteName = ''
                this.form.data.siteCode = ''
                this.form.data.siteId = ''
              }
            })
        } else {
          this.form.dataSource.siteList = []
        }
      } else if (type === 'siteName') {
        const arr = this.form.dataSource.siteList.find((e) => e.orgName === val)
        if (arr) {
          this.form.data.siteCode = arr.orgCode
          this.form.data.siteId = arr.id
          this.form.data.siteName = arr.orgName
        }
      } else if (type == 'purId') {
        this.form.data.purName = val.itemData.employeeName
        this.form.data.purId = val.itemData.employeeId
      } else if (type == 'companyName') {
        const arr = this.form.dataSource.firm.find((e) => e.orgCode === val)
        if (arr) {
          this.form.data.companyName = arr.orgName
          this.form.data.companyId = arr.id
          this.form.data.companyCode = arr.orgCode
        }
      } else if (type == 'decidePriceType') {
        const arr = this.form.dataSource.pricingType.find((e) => e.value === val)
        console.log(224, arr)
        if (arr) {
          this.form.data.decidePriceType = arr.value
        }
      } else if (type == 'priceClassification') {
        const arr = this.form.dataSource.priceClassification.find((e) => e.value === val)
        if (arr) {
          this.form.data.priceClassification = arr.value
          console.error(this.form.data.priceClassification)
        }
      }
    },
    changeTaxItem(e) {
      this.form.data.companyName = e.itemData?.orgName
      this.form.data.companyId = e.itemData?.id
      this.form.data.companyCode = e.itemData?.orgCode
      this.form.data.purOrgName = ''
      this.form.data.purOrgCode = ''
      this.form.data.purOrgId = ''
      this.form.data.siteCode = ''
      this.form.data.siteId = ''
      this.form.data.siteName = ''
      this.form.data.expandSaveRequestList = []
      this.getMainData(e.itemData?.id)
    },
    // 工厂切换
    siteNameChange(e) {
      this.getSourcingExpandList(e.itemData?.data?.siteCode, this.form.data.purOrgCode)
    },
    mergeFormData() {
      const formKeys = Object.keys(this.form.data)
      const pData = this.modalData.data
      if (!pData) return
      for (let i = 0; i < formKeys.length; i++) {
        const formKey = formKeys[i]
        if (typeof pData[formKey] !== 'undefined') {
          this.form.data[formKey] = pData[formKey]
        }
      }
    },
    async saveRule() {
      const validate = await this.asyncFormValidate('appForm')
      if (!validate) {
        return
      }
      let expandSaveRequestList = []
      this.form.data.expandSaveRequestList.forEach((v) => {
        const temp = this.form.dataSource.sourcingExpandList.find((t) => t.value === v)
        temp && expandSaveRequestList.push(temp.data)
      })
      const params = {
        ...this.form.data,
        expandSaveRequestList
      }
      this.$API.rfxList.savePointTv(params).then((res) => {
        const { data, code } = res
        if (code === 200) {
          if (this.modalData && this.modalData.type === 'recommendations') {
            this.$emit('confirm-function', data)
          } else {
            this.emitConfirm()
          }
        }
      })
    },

    asyncFormValidate(el) {
      return new Promise((c) => {
        this.$refs[el].validate((valid) => c(valid))
      })
    },
    cancel() {
      this.emitConfirm()
    },
    emitConfirm() {
      this.$emit('confirm-function')
    },
    //定价对象列表
    getConfigList() {
      this.$API.businessConfig
        .getConfigList({
          page: {
            current: 1,
            size: 1000
          },
          condition: 'and',
          rules: [
            {
              field: 'sourcingMode',
              type: 'string',
              operator: 'equal',
              value: 'direct_pricing'
            }
          ]
        })
        .then((res) => {
          this.form.dataSource.pricing = res.data.records
        })
    },

    //采购组织
    getMainData(companyId) {
      if (!companyId) {
        this.form.dataSource.procurement = []
        this.form.dataSource.siteList = []
        return
      }
      // 数据源 purGroupName
      // this.$API.masterData
      //   .purchaseOraginaze({
      //     organizationCode: "BUORG002ADM",
      //   })
      //   .then((res) => {
      //     this.form.dataSource.procurement = res.data;
      //   });
      this.$API.masterData
        .permissionOrgList({
          orgId: companyId
        })
        .then((res) => {
          this.form.dataSource.procurement = res.data.map((i) => {
            return {
              ...i,
              codeAndName: `${i.organizationCode}-${i.organizationName}`
            }
          })
          this.form.dataSource.siteList = []
        })
    },
    //采购员
    // getUserPageList() {
    //   const DEFAULTPARAM = {
    //     condition: "",
    //     page: {
    //       current: 1,
    //       size: 200,
    //     },
    //     pageFlag: false,
    //   };
    //   this.$API.masterData.getUserPageList(DEFAULTPARAM).then((res) => {
    //     this.form.dataSource.personnel = res.data.records;
    //   });
    // },
    // 获取 用户列表
    getCurrentEmployees(e) {
      const { text: fuzzyName } = e
      this.form.dataSource.personnel = []
      this.$API.masterData.getCurrentTenantEmployees({ fuzzyName }).then((res) => {
        const tmp = []
        res.data.forEach((item) => {
          tmp.push({
            ...item,
            text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`
          })
        })
        this.form.dataSource.personnel = tmp
        this.form.data.purId = (JSON.parse(sessionStorage.getItem('userInfo')) || {})?.uid
      })
    },
    filteringPurId(e) {
      if (typeof e.text === 'string' && e.text) {
        e.updateData(
          this.form.dataSource.personnel.filter((f) => f?.employeeName.indexOf(e.text) > -1)
        )
      } else {
        e.updateData(this.form.dataSource.personnel)
      }
    },
    //获取公司
    getSpecifiedChildrenLevelOrgs() {
      this.$API.masterData
        .findSpecifiedChildrenLevelOrgs({
          organizationLevelCodes: ['ORG02'],
          orgType: 'ORG001PRO',
          includeItself: false,
          organizationIds: []
        })
        .then((res) => {
          this.form.dataSource.firm = res.data.map((i) => {
            return {
              ...i,
              codeAndName: `${i.orgCode}-${i.orgName}`
            }
          })
        })
    },
    // 工厂扩展
    async getSourcingExpandList(siteCode, purOrgCode) {
      if (!siteCode) {
        this.form.dataSource.sourcingExpandList = []
        return
      }
      const res = await this.$API.rfxDetail.purAllOrgWithSite({ fuzzyParam: siteCode, purOrgCode })
      if (res.code === 200 && res.data) {
        let dataSource = []
        res.data.forEach((v) => {
          v.siteOrgs?.forEach((x) => {
            dataSource.push({
              text: `${v.companyCode}-${v.businessOrganizationCode}-${v.businessOrganizationName}+${x.orgCode}-${x.orgName}`,
              value: v.companyCode + '+' + v.businessOrganizationCode + '+' + x.orgCode,
              data: {
                companyCode: v.companyCode,
                companyId: v.companyId,
                companyName: v.companyName,
                purOrgCode: v.businessOrganizationCode,
                purOrgId: v.id,
                purOrgName: v.businessOrganizationName,
                siteCode: x.orgCode,
                siteId: x.id,
                siteName: x.orgName
              }
            })
          })
        })
        this.$set(this.form.dataSource, 'sourcingExpandList', dataSource)
      } else {
        this.$set(this.form.dataSource, 'sourcingExpandList', [])
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  padding-top: 20px;
}
</style>
