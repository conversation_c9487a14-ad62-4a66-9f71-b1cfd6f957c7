<template>
  <div>
    <magnifier-input
      ref="materialCode"
      :id="data.column.field"
      :config="materialDialogCofig"
      @change="handleChange"
    />
  </div>
</template>
<script>
// import bus from '@/utils/bus'
// import util from '@/utils/utils'
import magnifierInput from '@/components/magnifierInput'
import { i18n } from '@/main.js'

const dialogPageConfig = (url, type, params) => {
  const gridId = {
    material: '44174cbf-7edf-4ccc-adf0-ddb89c266c07',
    supplier: 'b5c96abb-0dc8-4ff0-a6c5-4eb5ac19f566'
  }
  const column = {
    material: materialColumn
    // supplier: supplierColumn
  }
  return [
    {
      gridId: gridId[type],
      useToolTemplate: false,
      toolbar: [],
      grid: {
        allowFiltering: true,
        columnData: column[type],
        allowSorting: false,
        allowSelection: true,
        selectionSettings: {
          type: 'Multiple',
          mode: 'Row'
        },
        asyncConfig: { url, params }
      }
    }
  ]
}
const materialColumn = [
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  }
]

export default {
  components: {
    magnifierInput
  },
  data() {
    return {
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'label', value: 'value' },
      dataSource: [],
      supplierCode: '',
      // 物料放大镜弹窗配置
      materialDialogCofig: {
        pageConfig: dialogPageConfig(
          `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
          'material'
        ),
        text: 'itemName',
        value: 'itemCode'
      }
    }
  },
  mounted() {
    console.log('mountedmountedmounted', this.data)
  },
  methods: {
    handleChange(e) {
      if (this.data.column.field === 'materialCode') {
        console.log('changechange', e, this.data)
        this.data.materialCode = e.itemCode
      }
    },
    async getCostCenter(val) {
      const param = {
        conditio: 'or',
        page: {
          current: 1,
          size: 100
        },
        rules: [
          {
            field: 'supplierCode',
            label: '',
            type: 'string',
            operator: 'contains',
            value: val
          },
          {
            field: 'supplierName',
            label: '',
            type: 'string',
            operator: 'contains',
            value: val
          }
        ]
      }
      console.log('res.data.records', param, val)

      // 供应商 下拉取值接口
      await this.$API.priceService
        .encodingSupplierQuery(param)
        .then((res) => {
          // console.log('res.data.records', res.data.records)
          this.dataSource = res.data.records
          this.fields = { text: 'supplierName', value: 'supplierCode' }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    serchText(val) {
      if (this.data.column.field === 'supplierCode') {
        console.log('搜索值', val, this.data.column.field)
        this.getCostCenter(val.text)
        this.fields = { text: 'supplierName', value: 'supplierCode' }
      } else {
        val.updateData(this.dataSource.filter((e) => e[this.fields.value].includes(val.text)))
      }
    },
    selectChange(val) {
      console.log(val.itemData, '下拉数据的信息')
      console.log('val', val, this.data)
    }
  },
  beforeDestroy() {
    this.$bus.$off('changeUntaxedPricebus')
  }
}
</script>
