<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="modalData.title"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="appForm" :model="form.data" :rules="form.rules">
        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item prop="materialCode" :label="$t('物料')" label-style="top">
              <magnifier-input
                :default-value="row.materialCode"
                ref="materialCode"
                :config="materialDialogCofig"
                @change="(e) => handleChange(e, 'materialCode')"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="supplierCode" :label="$t('供应商')" label-style="top">
              <magnifier-input
                :default-value="row.supplierCode"
                ref="supplierCode"
                :config="supplierDialogCofig"
                @change="(e) => handleChange(e, 'supplierCode')"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="supplierMaterialCode" :label="$t('供应商物料编码')">
              <mt-input type="text" v-model="form.data.supplierMaterialCode"></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
import { i18n } from '@/main.js'
import magnifierInput from '@/components/magnifierInput'

const dialogPageConfig = (url, type, params) => {
  const gridId = {
    material: '44174cbf-7edf-4ccc-adf0-ddb89c266c07',
    supplier: 'b5c96abb-0dc8-4ff0-a6c5-4eb5ac19f566'
  }
  const column = {
    material: materialColumn,
    supplier: supplierColumn
  }
  return [
    {
      gridId: gridId[type],
      useToolTemplate: false,
      toolbar: [],
      grid: {
        allowFiltering: true,
        columnData: column[type],
        allowSorting: false,
        allowSelection: true,
        selectionSettings: {
          type: 'Multiple',
          mode: 'Row'
        },
        asyncConfig: { url, params }
      }
    }
  ]
}
const materialColumn = [
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  }
]
const supplierColumn = [
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  }
]

export default {
  components: {
    magnifierInput
  },
  data() {
    return {
      // 物料放大镜弹窗配置
      materialDialogCofig: {
        pageConfig: dialogPageConfig(
          `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
          'material'
        ),
        text: 'itemCode',
        value: 'itemCode'
      },
      // 供应商放大镜弹窗配置
      supplierDialogCofig: {
        pageConfig: dialogPageConfig(
          '/masterDataManagement/tenant/supplier/pagedQueryByOrgCode',
          'supplier'
        ),
        text: 'supplierName',
        value: 'supplierCode'
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.saveRule,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      row: {
        materialCode: '',
        supplierCode: ''
      },
      form: {
        data: {
          materialCode: '',
          materialName: '',
          supplierCode: '',
          supplierName: '',
          supplierMaterialCode: ''
        },
        rules: {
          materialCode: {
            required: true,
            message: this.$t('请选择物料编码'),
            trigger: 'blur'
          },
          // 供应商
          supplierCode: {
            required: true,
            message: this.$t('请选择供应商'),
            trigger: 'blur'
          },
          supplierMaterialCode: {
            required: true,
            message: this.$t('请选择供应商物料编码'),
            trigger: 'blur'
          }
        }
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  mounted() {
    console.log('this.modalData', this.row.materialCode)
    if (this.modalData._data) {
      this.row.materialCode = this.modalData._data.materialCode
      this.row.supplierCode = this.modalData._data.supplierCode
      this.form.data.materialCode = this.modalData._data.materialCode
      this.form.data.supplierCode = this.modalData._data.supplierCode
      this.form.data.materialName = this.modalData._data.materialName
      this.form.data.supplierName = this.modalData._data.supplierName
      this.form.data.supplierMaterialCode = this.modalData._data.supplierMaterialCode
    }
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    handleChange(e, type) {
      if (type == 'materialCode') {
        this.form.data.materialCode = e.itemCode
        this.form.data.materialName = e.itemName
      } else if (type == 'supplierCode') {
        this.form.data.supplierCode = e.supplierCode
        this.form.data.supplierName = e.supplierName
      }
    },
    async saveRule() {
      const validate = await this.asyncFormValidate('appForm')
      if (!validate) {
        return
      }
      const params = {
        id: this.modalData?._data?.id ? this.modalData._data.id : '',
        ...this.form.data
        // materialCode: '',
        // materialName: '',
        // supplierCode: '',
        // supplierMaterialCode: '',
        // supplierName: ''
      }
      // console.log('paramparams', params)
      this.save(params)
    },
    async save(data) {
      const res = await this.$API.priceService.encodingRowSave(data)
      if (res.code === 200) {
        this.$toast({ content: this.$t('保存成功'), type: 'success' })
        this.$refs['dialog'].ejsRef.hide()
        this.$emit('confirm-function')
      }
    },
    asyncFormValidate(el) {
      return new Promise((c) => {
        this.$refs[el].validate((valid) => c(valid))
      })
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  padding-top: 20px;
}
.custom-height {
  height: 54px;
}
</style>
