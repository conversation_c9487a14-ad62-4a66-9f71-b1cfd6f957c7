<template>
  <div>
    <mt-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @open="startOpen"
      @change="selectChange"
      :open-dispatch-change="false"
      :disabled="isDisabled"
      :allow-filtering="true"
      :filtering="serchText"
    ></mt-select>
  </div>
</template>
<script>
// import bus from '@/utils/bus'
import { utils } from '@mtech-common/utils'
// import dayjs from 'dayjs'

export default {
  data() {
    return {
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'label', value: 'value' },
      dataSource: [],
      supplierCode: ''
    }
  },
  async mounted() {
    this.serchText = utils.debounce(this.serchText, 300)
    if (this.data.column.field === 'supplierCode') {
      console.log('this.data.supplierCode', this.data.supplierCode)
      // 供应商下拉
      this.getCostCenter(this.data.supplierCode)
      this.fields = { text: 'supplierName', value: 'supplierCode' }
    }
  },
  methods: {
    async getCostCenter(val) {
      const param = {
        conditio: 'or',
        page: {
          current: 1,
          size: 100
        },
        rules: [
          {
            field: 'supplierCode',
            label: '',
            type: 'string',
            operator: 'contains',
            value: val
          },
          {
            field: 'supplierName',
            label: '',
            type: 'string',
            operator: 'contains',
            value: val
          }
        ]
      }
      console.log('res.data.records', param, val)

      // 供应商 下拉取值接口
      await this.$API.priceService
        .encodingSupplierQuery(param)
        .then((res) => {
          // console.log('res.data.records', res.data.records)
          this.dataSource = res.data.records
          this.fields = { text: 'supplierName', value: 'supplierCode' }
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
    },
    serchText(val) {
      if (this.data.column.field === 'supplierCode') {
        console.log('搜索值', val, this.data.column.field)
        this.getCostCenter(val.text)
        this.fields = { text: 'supplierName', value: 'supplierCode' }
      } else {
        val.updateData(this.dataSource.filter((e) => e[this.fields.value].includes(val.text)))
      }
    },
    selectChange(val) {
      console.log(val.itemData, '下拉数据的信息')
      console.log('val', val, this.data)
    }
  },
  beforeDestroy() {
    this.$bus.$off('changeUntaxedPricebus')
  }
}
</script>
