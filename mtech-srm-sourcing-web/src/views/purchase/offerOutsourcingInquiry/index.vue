<template>
  <div class="full-height">
    <mt-local-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleCustomReset="handleCustomReset"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="materialCode" :label="$t('物料编码')" label-style="left">
              <mt-input v-model="searchFormModel.materialCode"></mt-input>
            </mt-form-item>
            <mt-form-item
              prop="supplierMaterialCode"
              :label="$t('供应商物料编码')"
              label-style="left"
            >
              <mt-input v-model="searchFormModel.supplierMaterialCode"></mt-input>
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('供应商编码')" label-style="left">
              <magnifier-input
                ref="supplierCode"
                :config="supplierDialogCofig"
                @change="(e) => (searchFormModel.supplierCode = e.supplierCode)"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-local-template-page>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { statusList, pageConfig, dialogPageConfig } from './config/index'
import magnifierInput from '@/components/magnifierInput'
import { download } from '@/utils/utils'
// 引入本地的emplate-page组件
import MtLocalTemplatePage from '@/components/template-page'

export default {
  components: {
    magnifierInput,
    MtLocalTemplatePage
  },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      pageConfig,
      statusList,
      companyList: [],
      factoryList: [],
      // 物料放大镜弹窗配置
      materialDialogCofig: {
        pageConfig: dialogPageConfig(
          `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
          'material'
        ),
        text: 'itemCode',
        value: 'itemCode'
      },
      // 供应商放大镜弹窗配置
      supplierDialogCofig: {
        pageConfig: dialogPageConfig(
          '/masterDataManagement/tenant/supplier/pagedQueryByOrgCode',
          'supplier'
        ),
        text: 'supplierName',
        value: 'supplierCode'
      }
    }
  },
  created() {
    // this.getSelectList()
  },
  methods: {
    // 日期格式化
    handleApproveDateChange(e) {
      if (e.startDate) {
        this.searchFormModel['approveStartDate'] =
          dayjs(e.startDate).format('YYYY-MM-DD') + ' 00:00:00'
        this.searchFormModel['approveEndDate'] = dayjs(e.endDate).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.searchFormModel['approveStartDate'] = null
        this.searchFormModel['approveEndDate'] = null
      }
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.$refs.materialCode.handleClear()
      this.$refs.supplierCode.handleClear()
    },
    // 点击按钮工具栏
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const selectedRecords = grid.getSelectedRecords()
      if (toolbar.id === 'Add') {
        this.handleAddFn()
      } else if (toolbar.id === 'download') {
        this.handleExport()
      } else if (toolbar.id === 'Import') {
        this.handleImport()
      } else if (toolbar.id === 'Cancel') {
        this.handleExport()
      } else if (toolbar.id === 'Delete') {
        if (selectedRecords.length === 0) {
          this.$toast({ content: this.$t('请先至少选择一行数据'), type: 'warning' })
          return
        }
        const ids = selectedRecords
          .map((e) => {
            return e.id
          })
          .join(',')
        this.delete(ids)
      }
    },
    // 单元格字段点击事件,跳转页面
    handleClickCellTitle(e) {
      console.log('eeeeee', e)
      if (e.field === 'materialCode') {
        this.$dialog({
          modal: () => import('./coms/addContractPrice.vue'),
          data: {
            title: this.$t('编辑数据'),
            _data: e.data
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    },
    // 行新增
    handleAdd() {
      // this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
    },
    // 删除
    delete(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          this.$API.priceService.encodingRowDelete({ ids }).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    // dialog新增
    handleAddFn() {
      this.$dialog({
        modal: () => import('./coms/addContractPrice.vue'),
        data: {
          title: this.$t('新建数据')
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //导入
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('上传/导入'),
          paramsKey: 'importFile',
          importApi: this.$API.priceService.excelimport,
          downloadTemplateApi: this.$API.priceService.exportTpl
        },
        success: () => {
          //刷新列表
          console.log('1111s')
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    // 导出
    handleExport() {
      const page = this.$refs.templateRef.getCurrentUsefulRef().ejsRef.pageSettings
      const params = {
        ...this.searchFormModel,
        page: {
          current: page.currentPage,
          size: page.pageSize
        }
      }
      this.$loading()
      this.$API.priceService
        .reFpExport(params)
        .then((res) => {
          download({
            fileName: this.$t('屏编码对照表.xlsx'),
            blob: new Blob([res.data])
          })
          this.$hloading()
        })
        .catch(() => {
          this.$hloading()
        })
    },
    actionBegin(args) {
      // rowData是编辑前的行数据， data是编辑后的数据
      console.log('actionBegin', args)
      const { requestType, data } = args
      if (requestType === 'save') {
        if (!data.materialCode && !data.supplierMaterialCode && !data.supplierCode) {
          this.$toast({ content: this.$t('请填写必填值'), type: 'warning' })
          args.cancel = true
        }
        // args.data = rowData
      }
    },
    actionComplete(args) {
      console.log('actionComplete', args)
      const { requestType, data } = args
      if (requestType === 'save') {
        // 调保存接口
        this.save(data)
      }
    },
    async save(data) {
      const res = await this.$API.priceService.encodingRowSave(data)
      if (res.code === 200) {
        this.$toast({ content: this.$t('保存成功'), type: 'success' })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  /deep/.e-headercelldiv {
    padding: 0 0.6em 0 0.6em !important;
  }
}
</style>
