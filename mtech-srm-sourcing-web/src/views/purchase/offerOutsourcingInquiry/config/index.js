import { i18n } from '@/main.js'
// import { Formatter } from '@/utils/ej/dataGrid/index'
import Vue from 'vue'
// import Select from '../coms/Select.vue'
// import magnifierInput from '../coms/magnifierInput.vue'

export const statusList = [
  { value: 0, text: i18n.t('未同步'), cssClass: 'title-#9baac1' },
  { value: 1, text: i18n.t('同步中'), cssClass: 'title-#6386c1' },
  { value: 2, text: i18n.t('同步成功'), cssClass: 'title-#6386c1' },
  { value: 3, text: i18n.t('同步失败'), cssClass: 'title-#9baac1' }
]
export const quoteAttributeList = [
  { value: 'mailing_price', text: i18n.t('寄售价'), cssClass: '' },
  { value: 'standard_price', text: i18n.t('标准价'), cssClass: '' }
]
export const quoteModeList = [
  { value: 'in_warehouse', text: i18n.t('按照入库'), cssClass: '' },
  { value: 'out_warehouse', text: i18n.t('按出库'), cssClass: '' },
  { value: 'order_date', text: i18n.t('按订单日期'), cssClass: '' }
]

// const supplierDialogCofig = {
//   pageConfig: dialogPageConfig(
//     '/masterDataManagement/tenant/supplier/pagedQueryByOrgCode',
//     'supplier'
//   ),
//   text: 'supplierName',
//   value: 'supplierCode'
// }

const columnData = [
  {
    width: 50,
    type: 'checkbox',
    allowEditing: false
  },
  {
    field: 'materialCode',
    headerText: i18n.t('物料编码'),
    cssClass: 'field-content',
    headerTemplate: () => {
      return {
        template: Vue.component('autoCalcIndexTypeTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('物料编码')}}</span>
              </div>
            `
        })
      }
    }
    // editTemplate: () => {
    //   return { template: magnifierInput }
    // }
  },
  {
    field: 'materialName',
    headerText: i18n.t('物料描述'),
    allowEditing: false
  },
  {
    field: 'supplierMaterialCode',
    headerText: i18n.t('供应商物料编码'),
    headerTemplate: () => {
      return {
        template: Vue.component('autoCalcIndexTypeTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('供应商物料编码')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    headerTemplate: () => {
      return {
        template: Vue.component('autoCalcIndexTypeTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('供应商编码')}}</span>
              </div>
            `
        })
      }
    }
    // editTemplate: () => {
    //   return { template: Select }
    // }
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    allowEditing: false
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    allowEditing: false
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    ignore: true,
    allowEditing: false
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('修改人'),
    allowEditing: false
    // formatter: Formatter.createFmtDatetime('YYYY-MM-DD')
  },
  {
    field: 'updateTime',
    headerText: i18n.t('修改时间'),
    allowEditing: false
    // formatter: Formatter.createFmtDatetime('YYYY-MM-DD')
  }
]
export const pageConfig = [
  {
    gridId: '457e9f13-d215-4d5f-97e6-1d0553eaecbb',
    isUseCustomEditor: true,
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    useToolTemplate: false,
    toolbar: {
      useBaseConfig: false,
      tools: [
        [
          { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
          { id: 'Delete', icon: 'icon_solid_edit', title: i18n.t('删除') },
          // { id: "save", icon: "icon_solid_edit", title: i18n.t("保存") },
          { id: 'Import', icon: 'icon_solid_Createorder', title: i18n.t('导入') },
          { id: 'download', icon: 'icon_solid_export', title: i18n.t('导出') }
        ],
        ['Refresh', 'Setting']
      ]
    },
    grid: {
      columnData,
      frozenColumns: 2,
      editSettings: {
        // allowEditing: true,
        // allowAdding: true,
        // allowDeleting: true,
        // newRowPosition: 'Top',
        // mode: 'Normal'
      },
      asyncConfig: {
        url: '/sourcing/tenant/screen/contrast/queryBuilder'
      }
    }
  }
]

const materialColumn = [
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  }
]
const supplierColumn = [
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  }
]
export const dialogPageConfig = (url, type, params) => {
  const gridId = {
    material: '44174cbf-7edf-4ccc-adf0-ddb89c266c07',
    supplier: 'b5c96abb-0dc8-4ff0-a6c5-4eb5ac19f566'
  }
  const column = {
    material: materialColumn,
    supplier: supplierColumn
  }
  return [
    {
      gridId: gridId[type],
      useToolTemplate: false,
      toolbar: [],
      grid: {
        allowFiltering: true,
        columnData: column[type],
        allowSorting: false,
        allowSelection: true,
        selectionSettings: {
          type: 'Multiple',
          mode: 'Row'
        },
        asyncConfig: { url, params }
      }
    }
  ]
}
