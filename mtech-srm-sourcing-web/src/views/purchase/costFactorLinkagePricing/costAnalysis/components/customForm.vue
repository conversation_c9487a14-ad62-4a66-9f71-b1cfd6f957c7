<template>
  <mt-form ref="formRef" :model="formData">
    <mt-form-item
      v-for="(item, index) in formItems"
      :key="index"
      :prop="item.fieldCode"
      :label="item.fieldName"
      label-style="left"
    >
      <!-- 文本、数值输入框 -->
      <vxe-input
        v-if="item.type !== 'select'"
        v-model="formData[item.fieldCode]"
        :type="item.type"
        :clearable="!item.readonly"
        min="0"
        :readonly="item.readonly"
        :disabled="item.disabled"
        @blur="handleFormValueChange"
        @prev-number="handleNumberChange"
        @next-number="handleNumberChange"
      />
      <!-- 选择框 -->
      <div v-else class="select-dialog">
        <vxe-select v-model="formData[item.fieldCode]" :options="[]" clearable filterable />
      </div>
    </mt-form-item>
  </mt-form>
</template>
<script>
import debounce from 'lodash.debounce'

export default {
  props: {
    type: {
      type: String,
      default: ''
    },
    formItems: {
      type: Array,
      default: () => []
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      formData: {}
    }
  },
  computed: {},
  watch: {
    data: {
      handler(newValue) {
        this.formData = newValue
      },
      deep: true
    }
  },
  mounted() {
    this.formData = this.data
  },
  methods: {
    // 数值输入框，在点击右侧向上/向下按钮时触发该事件
    handleNumberChange: debounce(function () {
      this.handleFormValueChange()
    }, 1000),
    handleFormValueChange() {
      this.$emit('change', this.formData)
    }
  }
}
</script>
<style lang="scss" scoped>
.select-dialog {
  display: flex;
  .select-dialog-icon {
    margin: 0 0 0 5px;
    line-height: 35px !important;
  }
}

::v-deep {
  .mt-form-item {
    margin: 5px 0 0 0;
    .label {
      margin: 0 5px 3px 5px;
      font-size: 12px;
    }
  }
  .vxe-input {
    width: 100%;
    height: 28px;
    line-height: 28px;
    .vxe-input--extra-suffix {
      font-size: 12px;
    }
  }
}
</style>
