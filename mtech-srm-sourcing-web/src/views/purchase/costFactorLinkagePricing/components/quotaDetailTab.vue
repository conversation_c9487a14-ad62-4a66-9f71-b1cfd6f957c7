<template>
  <div>
    <sc-table
      ref="detailSctableRef"
      row-id="id"
      grid-id="2bb74a69-c73e-49e6-a347-56be0452233f"
      :keep-source="true"
      :edit-config="editConfig"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      :loading="loading"
      :cell-style="handleCellStyle"
      @refresh="getTableData"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          v-show="!item.isHidden"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :disabled="item.disabled"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />

    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-name="downTemplateName"
      :down-template-params="downTemplateParams"
      :request-urls="requestUrls"
      file-key="file"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    />
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import { download, getHeadersFileName } from '@/utils/utils'
import cloneDeep from 'lodash/cloneDeep'

export default {
  name: 'QuotaDetailTab',
  components: {
    ScTable,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  mixins: [mixin],
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      type: 'quota',
      downTemplateName: this.$t('联动定价配额明细-导入模板'),
      downTemplateParams: {},
      requestUrls: {},
      // 不合并的单元格：配额比例、阶梯数量、单价含税/未税
      unmergeList: ['id', 'allocationRate', 'stepValue', 'unitPriceUntaxed', 'unitPriceTaxed']
    }
  },
  computed: {
    editConfig() {
      return {
        enabled: [6, 9].includes(this.dataInfo.status),
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    dataList() {
      const list = cloneDeep(this.tableData)
      const dataList = []
      list.forEach((item) => {
        if (item.isAdd) {
          item.id = null
        } else if (this.tableRef.isUpdateByRow(item)) {
          item.optType = 'modify'
        }
        for (let i = 0; i < item.rowSpan; i++) {
          const tempItem = { ...item }
          this.unmergeList.forEach((key) => {
            if (key === 'allocationRate') {
              tempItem[key] = item.allocationRate
            } else {
              item[key + 'List'] && (tempItem[key] = item[key + 'List'][i])
            }
          })
          dataList.push(tempItem)
        }
      })
      return dataList
    }
  },
  mounted() {
    if (this.$route.query.type === 'edit') {
      this.getTableData()
    }
  },
  methods: {
    async getTableData() {
      const params = {
        rfxId: this.$route.query.id,
        page: this.pageInfo
      }
      this.loading = true
      const res = await this.$API.costFactorLinkagePricing
        .queryCflpQuotaDetailList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = this.groupData(res.data?.records || [])

        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      switch (e.code) {
        case 'save':
          this.handleSave()
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          this.handleExport()
          break
        default:
          break
      }
    },
    // 保存
    async handleSave() {
      const params = {
        id: this.$route.query.id,
        quotaItemList: this.dataList
      }
      const res = await this.$API.costFactorLinkagePricing.saveCflpQuotaDetailList(params)
      if (res.code === 200) {
        this.$toast({ content: this.$t('保存成功！'), type: 'success' })
        this.getTableData()
      }
    },
    // 导入
    handleImport() {
      this.requestUrls = {
        templateUrlPre: 'costFactorLinkagePricing',
        templateUrl: 'downloadCflpQuotaDetailListTemplate',
        uploadUrl: 'importCflpQuotaDetailList',
        rfxId: this.$route.query.id
      }
      this.showUploadExcel(true)
    },
    // 导出
    async handleExport() {
      const includeColumnFiledNames = []
      const tableColumns = this.tableRef.getColumns()
      tableColumns.forEach((col) => col.field && includeColumnFiledNames.push(col.field))

      const params = {
        rfxId: this.$route.query.id,
        page: this.pageInfo,
        includeColumnFiledNames
      }
      const res = await this.$API.costFactorLinkagePricing.exportCflpQuotaDetailList(params)
      if (res.data) {
        this.$toast({
          type: 'success',
          content: this.$t('正在导出，请稍后！')
        })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      }
    },
    // 展示/不展示 上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = [] // 清空数据
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 导入成功
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.getTableData()
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 0px 8px;
  background: #fff;
}
::v-deep {
  .vxe-table--render-default .vxe-body--column.col--ellipsis:not(.col--actived) > .vxe-cell {
    max-height: unset !important;
    padding: 0;
  }
}
</style>
