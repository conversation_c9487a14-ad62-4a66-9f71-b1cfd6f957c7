<template>
  <div>
    <sc-table
      ref="detailSctableRef"
      row-id="id"
      grid-id="f2a31292-c072-4176-93d8-1777eab406b8"
      :keep-source="true"
      :edit-config="editConfig"
      :row-config="{ height: '' }"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      :loading="loading"
      :cell-style="handleCellStyle"
      @refresh="getTableData"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          v-show="!item.isHidden"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :disabled="item.disabled"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import { download, getHeadersFileName } from '@/utils/utils'
import cloneDeep from 'lodash/cloneDeep'

export default {
  name: 'MaterialDetailTab',
  components: {
    ScTable
  },
  mixins: [mixin],
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      type: 'material',
      // 不合并的单元格：历史单价含税/未税、单价含税/未税、历史降幅、阶梯数量
      unmergeList: [
        'id',
        'historyUntaxedUnitPrice',
        'historyTaxedUnitPrice',
        'unitPriceUntaxed',
        'unitPriceTaxed',
        'declinePercent',
        'stepValue',
        'priceRecordCode'
      ]
    }
  },
  computed: {
    editConfig() {
      return {
        enabled: this.$route.query.id && [0, 3, 5, 9].includes(this.dataInfo.status),
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    dataList() {
      const list = cloneDeep(this.tableData)
      const dataList = []
      list.forEach((item) => {
        if (item.isAdd) {
          item.id = null
        } else if (this.tableRef.isUpdateByRow(item)) {
          item.optType = 'modify'
        }
        for (let i = 0; i < item.rowSpan; i++) {
          const tempItem = { ...item }
          this.unmergeList.forEach((key) => {
            item[key + 'List'] && (tempItem[key] = item[key + 'List'][i])
          })
          dataList.push(tempItem)
        }
      })
      return dataList
    }
  },
  mounted() {
    if (this.$route.query.type === 'edit') {
      this.getTableData()
    }
  },
  methods: {
    async getTableData() {
      const params = {
        rfxId: this.$route.query.id,
        page: this.pageInfo
      }
      this.loading = true
      const res = await this.$API.costFactorLinkagePricing
        .queryCflpMdDetailList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = this.groupData(res.data?.records || [])

        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'save':
          this.handleSave()
          break
        case 'delete':
          this.handleDelete(selectedRecords)
          break
        case 'callback':
          this.handleCallback()
          break
        case 'export':
          this.handleExport()
          break
        default:
          break
      }
    },
    // 新增
    handleAdd() {
      if (this.$parent.dataList[0]?.length === 0) {
        this.$toast({ content: this.$t('成本因子为空，不允许添加物料明细！'), type: 'warning' })
        return
      }
      const savedList = []
      this.$parent.dataList[0].forEach((item) => !item.isAdd && savedList.push(item))
      if (savedList.length === 0) {
        this.$toast({ content: this.$t('请先保存成本因子！'), type: 'warning' })
        return
      }

      this.$dialog({
        modal: () => import('../dialog/materialDialog.vue'),
        data: {
          title: this.$t('物料'),
          rfxId: this.$route.query.id
        },
        success: (list) => {
          list.forEach(async (data, index) => {
            const newRowData = {
              isAdd: true,
              optType: 'add',
              ...data,
              itemGroupId: data.priceGroupCode
            }
            const { row: newRow } = await this.tableRef.insertAt(newRowData)
            this.tableData.unshift(newRow)
            index === list.length - 1 && this.tableRef.setEditRow(newRow)
          })
        }
      })
    },
    // 保存
    async handleSave() {
      const params = {
        rfxId: this.$route.query.id,
        materialItemList: this.dataList
      }
      const res = await this.$API.costFactorLinkagePricing.saveCflpDetail(params)
      if (res.code === 200) {
        this.$toast({ content: this.$t('保存成功！'), type: 'success' })
        this.getTableData()
      }
    },
    // 删除
    handleDelete(list) {
      if (!list.length) {
        this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: async () => {
          const itemIdList = []
          list.forEach(async (item) => {
            if (item.isAdd) {
              await this.tableRef.remove(item)
            } else {
              item.idList?.forEach((id) => itemIdList.push(id))
            }
          })

          if (itemIdList.length === 0) {
            this.tableData = this.tableRef.getTableData().fullData
            return
          }
          const params = {
            rfxId: this.$route.query.id,
            itemIdList
          }
          const res = await this.$API.costFactorLinkagePricing.deleteCflpMdDetailList(params)
          if (res.code === 200) {
            this.$toast({ content: this.$t('删除成功！'), type: 'success' })
            this.getTableData()
          }
        }
      })
    },
    // 驳回定价
    handleCallback() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认驳回定价？')
        },
        success: async () => {
          const res = await this.$API.costFactorLinkagePricing.callbackCflpMdDetailList({
            rfxId: this.$route.query.id
          })
          if (res.code === 200) {
            this.$toast({ content: this.$t('驳回定价成功！'), type: 'success' })
            this.$emit('updateDetail')
          }
        }
      })
    },
    // 导出
    async handleExport() {
      const includeColumnFiledNames = []
      const tableColumns = this.tableRef.getColumns()
      tableColumns.forEach((col) => col.field && includeColumnFiledNames.push(col.field))

      const params = {
        rfxId: this.$route.query.id,
        page: this.pageInfo,
        includeColumnFiledNames
      }
      const res = await this.$API.costFactorLinkagePricing.exportCflpMdDetailList(params)
      if (res.data) {
        this.$toast({
          type: 'success',
          content: this.$t('正在导出，请稍后！')
        })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      }
    },
    // 成本分析
    handleCostAnalysis(row, index) {
      this.$router.push({
        name: 'cost-factor-linkage-pricing-cost-analysis',
        query: {
          rfxId: this.$route.query.id,
          rfxItemId: row.isAdd ? null : row.idList[index], // 新增时候不传rfxItemId
          priceRecordCode: row.priceRecordCodeList[index],
          refreshId: Date.now()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 0px 8px;
  background: #fff;
}
::v-deep {
  .vxe-table--render-default .vxe-body--column.col--ellipsis:not(.col--actived) > .vxe-cell {
    max-height: unset !important;
    padding: 0;
  }
}
</style>
