<template>
  <div>
    <sc-table
      ref="detailSctableRef"
      row-id="id"
      grid-id="f0c52759-2ad9-4673-aa24-1d86af20661a"
      :keep-source="true"
      :edit-config="editConfig"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      :loading="loading"
      @refresh="getTableData"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          v-show="!item.isHidden"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :disabled="item.disabled"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  name: 'CostFactorTab',
  components: {
    ScTable
  },
  mixins: [mixin],
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      type: 'costFactor'
    }
  },
  computed: {
    editConfig() {
      return {
        enabled: this.$route.query.id && [0, 3, 5, 9].includes(this.dataInfo.status),
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    }
  },
  mounted() {
    if (this.$route.query.type === 'edit') {
      this.getTableData()
    }
  },
  methods: {
    async getTableData() {
      const params = {
        rfxId: this.$route.query.id,
        page: this.pageInfo
      }
      this.loading = true
      const res = await this.$API.costFactorLinkagePricing
        .queryCflpCfDetailList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []

        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const { code } = e
      const selectedRecords = this.tableRef.getCheckboxRecords()
      switch (code) {
        case 'add':
          this.handleAdd()
          break
        case 'save':
          this.handleSave()
          break
        case 'delete':
          this.handleDelete(selectedRecords)
          break
        case 'export':
          this.handleExport()
          break
        default:
          break
      }
    },
    // 新增
    handleAdd() {
      this.$dialog({
        modal: () => import('../dialog/costFactorDialog.vue'),
        data: {
          title: this.$t('成本因子'),
          rfxId: this.$route.query.id
        },
        success: (list) => {
          list.forEach(async (data, index) => {
            const newRowData = {
              isAdd: true,
              optType: 'add',
              ...data
            }
            const { row: newRow } = await this.tableRef.insertAt(newRowData)
            this.tableData.unshift(newRow)

            index === list.length - 1 && this.tableRef.setEditRow(newRow)
          })
        }
      })
    },
    // 保存
    async handleSave() {
      const params = {
        rfxId: this.$route.query.id,
        factorItemList: this.dataList
      }
      const res = await this.$API.costFactorLinkagePricing.saveCflpDetail(params)
      if (res.code === 200) {
        this.$toast({ content: this.$t('保存成功！'), type: 'success' })
        this.getTableData()
      }
    },
    // 删除
    handleDelete(list) {
      if (!list.length) {
        this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: async () => {
          const itemIdList = []
          list.forEach(async (item) => {
            if (item.isAdd) {
              await this.tableRef.remove(item)
            } else {
              itemIdList.push(item.id)
            }
          })

          if (itemIdList.length === 0) {
            this.tableData = this.tableRef.getTableData().fullData
            return
          }
          const params = {
            rfxId: this.$route.query.id,
            itemIdList
          }
          const res = await this.$API.costFactorLinkagePricing.deleteCflpCfDetail(params)
          if (res.code === 200) {
            this.$toast({ content: this.$t('删除成功！'), type: 'success' })
            this.getTableData()
          }
        }
      })
    },
    // 导出
    async handleExport() {
      const includeColumnFiledNames = []
      const tableColumns = this.tableRef.getColumns()
      tableColumns.forEach((col) => col.field && includeColumnFiledNames.push(col.field))

      const params = {
        rfxId: this.$route.query.id,
        page: this.pageInfo,
        includeColumnFiledNames
      }
      const res = await this.$API.costFactorLinkagePricing.exportCflpCfDetail(params)
      if (res.data) {
        this.$toast({
          type: 'success',
          content: this.$t('正在导出，请稍后！')
        })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 0px 8px;
  background: #fff;
}
</style>
