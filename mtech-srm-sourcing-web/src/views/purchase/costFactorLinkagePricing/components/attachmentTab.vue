<template>
  <div class="r-d-container mt-flex">
    <div class="tree-view--wrap">
      <div class="trew-node--add">
        <div class="node-title">{{ $t('层级') }}</div>
      </div>
      <mt-common-tree
        v-if="treeViewData.dataSource.length"
        ref="treeView"
        class="tree-view--template"
        :un-button="true"
        :fields="treeViewData"
        :selected-nodes="selectedNodes"
        :expanded-nodes="expandedNodes"
        @nodeSelected="nodeSelected"
      />
    </div>
    <div class="table-container">
      <sc-table
        ref="sctableRef"
        grid-id="9bd2798e-4b7c-4cb4-b2a0-44158a5876d3"
        :row-config="{ height: 48 }"
        :is-show-refresh-bth="true"
        :loading="loading"
        :columns="columns"
        :table-data="tableData"
        @refresh="getTableData"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            size="small"
            @click="handleClickToolBar(item)"
            >{{ item.name }}</vxe-button
          >
        </template>
      </sc-table>
    </div>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { treeViewData, toolObj, fileTypeIconSetting } from './config/attachment.js'
import { download } from '@/utils/utils'

export default {
  name: 'AttachmentTab',
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    }
  },
  components: { ScTable },
  data() {
    return {
      selectedNodes: [],
      expandedNodes: [],
      treeDataHash: {},
      treeViewData,
      toolbar: [],
      loading: false,
      tableData: [],
      isSupplierNode: false,
      cellTools: ['download', 'delete']
    }
  },
  computed: {
    editable() {
      // 仅已作废、草稿、审批驳回可以编辑
      return [0, 3, 7].includes(this.dataInfo?.status)
    },
    columns() {
      const defaultColumns = [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'fileName',
          title: this.$t('文件名称'),
          slots: {
            default: ({ row, column }) => {
              return [
                <div>
                  <a style='display: block' on-click={() => this.handleClickCellTitle(row, column)}>
                    {row.fileName}
                  </a>
                  <a
                    class='cell-btn'
                    v-show={this.cellTools.includes('download')}
                    on-click={() => this.handleClickCellTool('download', row)}>
                    <i class='vxe-icon-download' />
                    {this.$t('下载')}
                  </a>
                  <a
                    class='cell-btn'
                    v-show={this.cellTools.includes('delete')}
                    on-click={() => this.handleDelete([row.id])}>
                    <i class='vxe-icon-delete' />
                    {this.$t('删除')}
                  </a>
                </div>
              ]
            }
          }
        },
        {
          field: 'fileSize',
          title: this.$t('文件大小'),
          slots: {
            default: ({ row }) => {
              return [<span>{Number(((row.fileSize / 1024) * 100) / 100).toFixed(2)}KB</span>]
            }
          }
        },
        {
          field: 'fileType',
          title: this.$t('文件类型'),
          slots: {
            default: ({ row }) => {
              return [
                <div>
                  <i class={fileTypeIconSetting[row.fileType]} />
                  <span>{row.fileType}</span>
                </div>
              ]
            }
          }
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间')
        }
      ]
      if (this.isSupplierNode) {
        defaultColumns.push({
          field: 'supplierName',
          title: this.$t('上传供应商')
        })
      }
      return defaultColumns
    }
  },
  mounted() {
    this.getFileFolderData()
  },
  methods: {
    // 获取列表数据
    async getTableData() {
      const params = {
        docId: this.$route.query.id,
        parentId: this.selectNodeId
      }
      this.loading = true
      const res = await this.$API.costFactorLinkagePricing
        .getFileList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data || []
      }
    },
    // 获取侧边节点信息
    getFileFolderData() {
      let _params = {
        docId: this.$route.query.id
      }
      this.$API.costFactorLinkagePricing.getFileNodeList(_params).then((res) => {
        this.$set(this.treeViewData, 'dataSource', res.data)
        let treeDataHash = {}
        const nodeIds = []
        let recursiveQuery = function (children) {
          for (let item of children) {
            treeDataHash[item.id] = item
            nodeIds.push(item.id)
            if (Array.isArray(item.fileNodeResponseList) && item.fileNodeResponseList.length > 0) {
              recursiveQuery(item.fileNodeResponseList)
            }
          }
        }
        recursiveQuery(res.data)
        this.treeDataHash = treeDataHash
        if (Array.isArray(res.data) && res.data.length) {
          const selectedId = res.data[0]['id']
          this.selectedNodes = [selectedId]

          // 默认选中第一个节
          this.nodeSelected({ nodeData: { id: selectedId } })
        }
        this.$nextTick(() => (this.expandedNodes = nodeIds))
      })
    },
    // 选择节点
    nodeSelected(event) {
      if (event?.nodeData?.id) {
        this.selectNodeId = event?.nodeData?.id
        if (this.treeDataHash[this.selectNodeId].nodeCode.substring(0, 3) == 'sup') {
          // 供方
          this.isSupplierNode = true
          this.toolbar = [toolObj['download']]
          this.cellTools = ['download']
        } else {
          // 采方
          this.isSupplierNode = false
          if (!this.editable) {
            this.toolbar = [toolObj['download']]
            this.cellTools = ['download']
          } else {
            if (!event.nodeData.hasChildren) {
              this.toolbar = [toolObj['upload'], toolObj['download']]
            } else {
              this.toolbar = [toolObj['download']]
            }
            this.toolbar.splice(-1, 0, toolObj['delete'])
            this.cellTools = ['download', 'delete']
          }
        }
        this.getTableData()
      }
    },
    // 工具栏按钮点击
    handleClickToolBar(e) {
      const tableRef = this.$refs.sctableRef.$refs.xGrid
      const _selectRows = tableRef.getCheckboxRecords()
      if (['download', 'delete'].includes(e.code) && !_selectRows.length) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'upload':
          this.$dialog({
            modal: () => import('COMPONENTS/Upload/index.vue'),
            data: {
              title: this.$t('上传')
            },
            success: (data) => {
              this.handleUploadFiles(data)
            }
          })
          break
        case 'download':
          for (let item of _selectRows) {
            this.$API.fileService.downloadPrivateFile({ id: item.sysFileId }).then((res) => {
              download({
                fileName: item.fileName,
                blob: new Blob([res.data])
              })
            })
          }
          break
        case 'delete':
          this.handleBatchDelete(_selectRows)
          break

        default:
          break
      }
    },
    // 单元格按钮点击
    handleClickCellTool(code, row) {
      switch (code) {
        case 'download':
          this.$API.fileService.downloadPrivateFile({ id: row.sysFileId }).then((res) => {
            download({ fileName: row.fileName, blob: new Blob([res.data]) })
          })
          break
        case 'delete':
          this.handleDelete([row.id])
          break
        default:
          break
      }
    },
    // 单元格title文字点击
    handleClickCellTitle(row, column) {
      if (column.field == 'fileName') {
        let params = {
          id: row?.sysFileId || row?.id,
          useType: 2
        }
        this.$API.fileService.getMtPreview(params).then((res) => {
          window.open(res.data)
        })
      }
    },
    // 上传文件
    handleUploadFiles(data) {
      let { id, fileName, fileSize, fileType, url, remoteUrl, sysName } = data
      let _params = {
        docId: this.$route.query.id,
        parentId: this.selectNodeId,
        fileName: fileName,
        fileSize: fileSize,
        fileType: fileType,
        remoteUrl: remoteUrl,
        sysFileId: id,
        sysName: sysName,
        url: url
      }
      this.$API.costFactorLinkagePricing.saveFile(_params).then(() => {
        this.$toast({
          content: this.$t('上传成功'),
          type: 'success'
        })
        this.getTableData()
      })
    },
    //批量删除操作
    handleBatchDelete(_selectGridRecords) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      this.handleDelete(_selectIds)
    },
    //删除文件
    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          this.$API.rfxFiles.deleteSourcingFileById({ idList: ids }).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.getTableData()
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.r-d-container {
  width: 100%;
  // box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  .tree-view--wrap {
    min-width: 150px;
    background: #fff;
    margin-right: 10px;
    .trew-node--add {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      height: 40px;
      padding: 0 20px;
      color: #4f5b6d;
      box-shadow: inset 0 -1px 0 0 #e8e8e8;
      .node-title {
        font-size: 14px;
        color: #292929;
        display: inline-block;
        position: relative;
        padding-left: 10px;

        &:before {
          content: '';
          position: absolute;
          width: 3px;
          height: 14px;
          background: #409eff;
          border-radius: 0 2px 2px 0;
          left: 0;
          top: 0;
        }
      }
    }
    .tree-view--template {
      width: 100%;
      padding-top: 10px;
      padding-left: 20px;
    }
  }
  .table-container {
    width: 100%;
    max-height: none !important;
    .cell-btn {
      font-size: 12px;
      font-weight: 400;
      margin-right: 10px;
    }
  }
}

::v-deep {
  .mt-commom-tree-view {
    padding-left: 0;
  }
  .e-treeview .e-list-item:not(.e-has-child).e-active,
  .e-treeview .e-list-item:not(.e-has-child).e-hover {
    background: #f5f6f7 !important;
  }
  .mt-commom-tree-view .mt-tree-view .e-treeview .e-list-item.e-hover > .e-fullrow,
  .mt-commom-tree-view .mt-tree-view .e-treeview .e-list-item.e-active > .e-fullrow {
    border: 1px solid #f5f6f7 !important;
    background: #f5f6f7;
    opacity: 1;
  }

  .mt-commom-tree-view .expandedLevel1 {
    background: transparent !important;
  }

  .mt-tree-view .e-treeview .e-list-item {
    margin: 5px 0 5px 12px;
    padding: 0 12px;
  }
}
</style>
