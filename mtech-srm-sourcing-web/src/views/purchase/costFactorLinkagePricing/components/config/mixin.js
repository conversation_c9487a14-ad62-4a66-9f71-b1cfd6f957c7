import XEUtils from 'xe-utils'
import { getEndDate } from '@/utils/obj'
import cloneDeep from 'lodash/cloneDeep'
import SplitCell from '@/components/VxeComponents/SplitCell/index.vue'

export default {
  components: { SplitCell },
  data() {
    return {
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 500,
        pageSizes: [20, 50, 200, 500, 1000, 2000]
      },
      pageInfo: {
        size: 500,
        current: 1
      },
      tableData: [],
      loading: false
    }
  },
  computed: {
    editable() {
      return this.$route.query.type === 'create' || [0, 5, 9].includes(this.dataInfo.status)
    },
    toolbar() {
      const { id } = this.$route.query
      switch (this.type) {
        case 'costFactor':
          return [
            {
              code: 'add',
              name: this.$t('新增'),
              status: 'info',
              isHidden: !id || ![0, 9].includes(this.dataInfo.status)
            },
            {
              code: 'save',
              name: this.$t('保存'),
              status: 'info',
              isHidden: !id || ![0, 3, 5, 9].includes(this.dataInfo.status)
            },
            {
              code: 'delete',
              name: this.$t('删除'),
              status: 'info',
              isHidden: !id || ![0, 3, 5, 9].includes(this.dataInfo.status)
            },
            {
              code: 'export',
              name: this.$t('导出'),
              status: 'info',
              isHidden: !id
            }
          ]
        case 'material':
          return [
            {
              code: 'add',
              name: this.$t('新增'),
              status: 'info',
              isHidden: !id || ![0, 9].includes(this.dataInfo.status)
            },
            {
              code: 'save',
              name: this.$t('保存'),
              status: 'info',
              isHidden: !id || ![0, 3, 5, 9].includes(this.dataInfo.status)
            },
            {
              code: 'delete',
              name: this.$t('删除'),
              status: 'info',
              isHidden: !id || ![0, 3, 5, 9].includes(this.dataInfo.status)
            },
            {
              code: 'callback',
              name: this.$t('驳回定价'),
              status: 'info',
              isHidden: !id || ![6, 9].includes(this.dataInfo.status)
            },
            {
              code: 'export',
              name: this.$t('导出'),
              status: 'info',
              isHidden: !id
            }
          ]
        case 'quota':
          return [
            {
              code: 'save',
              name: this.$t('保存'),
              status: 'info',
              isHidden: !id || ![6, 9].includes(this.dataInfo.status)
            },
            {
              code: 'import',
              name: this.$t('导入'),
              status: 'info',
              isHidden: !id || ![6, 9].includes(this.dataInfo.status)
            },
            {
              code: 'export',
              name: this.$t('导出'),
              status: 'info',
              isHidden: !id
            }
          ]
        default:
          return []
      }
    },
    tableRef() {
      return this.$refs.detailSctableRef.$refs.xGrid
    },
    columns() {
      const editable = this.dataInfo.companyCode ? true : false
      switch (this.type) {
        case 'costFactor':
          return [
            {
              width: 50,
              type: 'checkbox'
            },
            {
              field: 'costFactorCode',
              title: this.$t('成本因子编码'),
              minWidth: 150
            },
            {
              field: 'costFactorName',
              title: this.$t('成本因子名称')
            },
            {
              field: 'costFactorSpec',
              title: this.$t('规格')
            },
            {
              field: 'costFactorBrand',
              title: this.$t('品牌')
            },
            {
              field: 'basicMeasureUnitName',
              title: this.$t('单位'),
              slots: {
                default: ({ row }) => {
                  return [
                    <span>
                      {(row.basicMeasureUnitCode || '') + '-' + (row.basicMeasureUnitName || '')}
                    </span>
                  ]
                }
              }
            },
            {
              field: 'costFactorAttr',
              title: this.$t('属性大类')
            },
            {
              field: 'costFactorGroup',
              title: this.$t('属性中类')
            },
            {
              field: 'historyUntaxedUnitPrice',
              title: this.$t('历史单价（未税）'),
              minWidth: 140
            },
            {
              field: 'historyTaxedUnitPrice',
              title: this.$t('历史单价（含税）'),
              minWidth: 140
            },
            {
              field: 'unitPriceUntaxed',
              title: this.$t('单价（未税）')
            },
            {
              field: 'unitPriceTaxed',
              title: this.$t('单价（含税）')
            },
            {
              field: 'declinePercent',
              title: this.$t('历史降幅（%）'),
              minWidth: 120,
              slots: {
                default: ({ row }) => {
                  return [
                    <span>
                      {row.declinePercent || row.declinePercent === 0
                        ? Number(row.declinePercent).toFixed(2)
                        : ''}
                    </span>
                  ]
                }
              }
            },
            {
              field: 'supplierCode',
              title: this.$t('供应商编码')
            },
            {
              field: 'supplierName',
              title: this.$t('供应商名称'),
              minWidth: 150
            },
            {
              field: 'status',
              title: this.$t('是否生效'),
              slots: {
                default: ({ row }) => {
                  const statusrMap = {
                    0: this.$t('草稿'),
                    1: this.$t('生效'),
                    2: this.$t('失效')
                  }
                  return [<span>{row.status ? statusrMap[row.status] : '-'}</span>]
                }
              }
            },
            {
              field: 'priceValidStartDate',
              title: this.$t('生效日期'),
              minWidth: 140,
              editRender: {
                enabled: editable
              },
              slots: {
                default: ({ row }) => {
                  const priceValidStartDate = XEUtils.toDateString(
                    row.priceValidStartDate,
                    'yyyy-MM-dd'
                  )
                  return [<span>{priceValidStartDate}</span>]
                },
                edit: ({ row }) => {
                  return [
                    <vxe-input
                      v-model={row.priceValidStartDate}
                      clearable
                      type='date'
                      editable={false}
                      transfer
                      disabled-method={(e) => this.disabledDateMethod('start', row, e)}
                      on-change={({ value }) => {
                        let endDate = '9999-12-31'
                        // 1503、0530失效日期特殊处理
                        if (value) {
                          if (this.dataInfo.companyCode === '1503') {
                            endDate = XEUtils.toDateString(
                              getEndDate(new Date(value).getTime()),
                              'yyyy-MM-dd'
                            )
                          } else if (this.dataInfo.companyCode === '0530') {
                            endDate = '2099-12-31'
                          }
                        }
                        row.priceValidEndDate = value ? endDate : null
                      }}
                    />
                  ]
                }
              }
            },
            {
              field: 'priceValidEndDate',
              title: this.$t('失效日期'),
              minWidth: 140,
              editRender: {
                enabled: editable
              },
              slots: {
                default: ({ row }) => {
                  const priceValidEndDate = XEUtils.toDateString(
                    row.priceValidEndDate,
                    'yyyy-MM-dd'
                  )
                  return [<span>{priceValidEndDate}</span>]
                },
                edit: ({ row }) => {
                  return [
                    <vxe-input
                      v-model={row.priceValidEndDate}
                      clearable
                      type='date'
                      editable={false}
                      transfer
                      disabled-method={(e) => this.disabledDateMethod('end', row, e)}
                    />
                  ]
                }
              }
            },
            {
              field: 'remark',
              title: this.$t('备注'),
              minWidth: 180,
              editRender: {
                enabled: editable
              },
              slots: {
                edit: ({ row }) => {
                  return [<vxe-input v-model={row.remark} clearable />]
                }
              }
            }
          ]
        case 'material':
          return [
            {
              width: 50,
              type: 'checkbox'
            },
            {
              field: 'itemNo',
              title: this.$t('行号'),
              width: 50
            },
            {
              field: 'costFactorCode',
              title: this.$t('成本因子编码'),
              slots: {
                default: ({ row }) => {
                  const arr = []
                  row.costFactorList?.forEach((item) => {
                    arr.push(item.costFactorCode)
                  })
                  return [<span>{arr.join(',')}</span>]
                }
              }
            },
            {
              field: 'costFactorName',
              title: this.$t('成本因子名称'),
              minWidth: 150,
              slots: {
                default: ({ row }) => {
                  const arr = []
                  row.costFactorList?.forEach((item) => {
                    arr.push(item.costFactorName)
                  })
                  return [<span>{arr.join(',')}</span>]
                }
              }
            },
            {
              field: 'supplierCode',
              title: this.$t('供应商编码')
            },
            {
              field: 'supplierName',
              title: this.$t('供应商名称'),
              minWidth: 180
            },
            {
              field: 'factoryCode',
              title: this.$t('工厂编码')
            },
            {
              field: 'factoryName',
              title: this.$t('工厂名称'),
              minWidth: 150
            },
            {
              field: 'materialCode',
              title: this.$t('物料编码')
            },
            {
              field: 'materialName',
              title: this.$t('物料名称'),
              minWidth: 150
            },
            {
              field: 'categoryCode',
              title: this.$t('品类编码')
            },
            {
              field: 'categoryName',
              title: this.$t('品类名称')
            },
            {
              field: 'historyUntaxedUnitPrice',
              title: this.$t('历史单价（未税）'),
              minWidth: 140,
              slots: {
                default: ({ row }) => {
                  return [<SplitCell list={row.historyUntaxedUnitPriceList} />]
                }
              }
            },
            {
              field: 'historyTaxedUnitPrice',
              title: this.$t('历史单价（含税）'),
              minWidth: 140,
              slots: {
                default: ({ row }) => {
                  return [<SplitCell list={row.historyTaxedUnitPriceList} />]
                }
              }
            },
            {
              field: 'unitPriceUntaxed',
              title: this.$t('单价（未税）'),
              slots: {
                default: ({ row }) => {
                  return [<SplitCell list={row.unitPriceUntaxedList} />]
                }
              }
            },
            {
              field: 'unitPriceTaxed',
              title: this.$t('单价（含税）'),
              slots: {
                default: ({ row }) => {
                  return [<SplitCell list={row.unitPriceTaxedList} />]
                }
              }
            },
            {
              field: 'declinePercent',
              title: this.$t('历史降幅（%）'),
              minWidth: 120,
              slots: {
                default: ({ row }) => {
                  return [<SplitCell list={row.declinePercentList} />]
                }
              }
            },
            {
              field: 'stepValue',
              title: this.$t('阶梯数量'),
              slots: {
                default: ({ row }) => {
                  return [<SplitCell list={row.stepValueList} />]
                }
              }
            },
            {
              field: 'stageType',
              title: this.$t('阶梯类型'),
              slots: {
                default: ({ row }) => {
                  const stageTypeMap = {
                    '-1': this.$t('无阶梯'),
                    0: this.$t('按数量'),
                    1: this.$t('按时间'),
                    2: this.$t('按金额'),
                    3: this.$t('数量逐层')
                  }
                  return [<span>{stageTypeMap[row.stageType]}</span>]
                }
              }
            },
            {
              field: 'directDeliverAddr',
              title: this.$t('直送地')
            },
            {
              field: 'costModelName',
              title: this.$t('成本模型名称'),
              minWidth: 150
            },
            {
              field: 'currencyCode',
              title: this.$t('币种编码')
            },
            {
              field: 'currencyName',
              title: this.$t('币种名称')
            },
            {
              field: 'basicUnit',
              title: this.$t('基本单位'),
              slots: {
                default: ({ row }) => {
                  return [<span>{(row.basicUnit || '') + '-' + (row.basicUnitName || '')}</span>]
                }
              }
            },
            {
              field: 'purchaseUnitName',
              title: this.$t('订单单位'),
              slots: {
                default: ({ row }) => {
                  return [
                    <span>{(row.purchaseUnitCode || '') + '-' + (row.purchaseUnitName || '')}</span>
                  ]
                }
              }
            },
            {
              field: 'priceUnitName',
              title: this.$t('价格单位')
            },
            {
              field: 'taxRateCode',
              title: this.$t('税率编码')
            },
            {
              field: 'taxRateName',
              title: this.$t('税率名称'),
              minWidth: 150
            },
            {
              field: 'taxRate',
              title: this.$t('税率')
            },
            {
              field: 'quoteAttr',
              title: this.$t('报价属性'),
              slots: {
                default: ({ row }) => {
                  const quoteAttrMap = {
                    mailing_price: this.$t('寄售价'),
                    standard_price: this.$t('标准价'),
                    outsource: this.$t('委外价')
                  }

                  return [<span>{row.quoteAttr ? quoteAttrMap[row.quoteAttr] : '-'}</span>]
                }
              }
            },
            {
              field: 'priceEffectiveMethod',
              title: this.$t('报价生效方式'),
              slots: {
                default: ({ row }) => {
                  const methodMap = {
                    in_warehouse: this.$t('按照入库'),
                    out_warehouse: this.$t('按出库'),
                    order_date: this.$t('按订单日期')
                  }
                  return [
                    <span>
                      {row.priceEffectiveMethod ? methodMap[row.priceEffectiveMethod] : '-'}
                    </span>
                  ]
                }
              }
            },
            {
              field: 'minPackageQty',
              title: this.$t('最小包装量')
            },
            {
              field: 'minPurchaseQty',
              title: this.$t('最小采购量')
            },
            {
              field: 'leadTime',
              title: this.$t('L/T')
            },
            {
              field: 'unconditionalLeadTime',
              title: this.$t('无条件L/T')
            },
            {
              field: 'priceValidStartDate',
              title: this.$t('生效日期'),
              minWidth: 140,
              editRender: {
                enabled: editable
              },
              slots: {
                default: ({ row }) => {
                  const priceValidStartDate = XEUtils.toDateString(
                    row.priceValidStartDate,
                    'yyyy-MM-dd'
                  )
                  return [<span>{priceValidStartDate}</span>]
                },
                edit: ({ row }) => {
                  const priceValidStartDate = row.priceValidStartDate
                    ? XEUtils.toDateString(row.priceValidStartDate, 'yyyy-MM-dd')
                    : ''
                  return [
                    <div>
                      <vxe-input
                        v-show={row.status !== 1}
                        v-model={row.priceValidStartDate}
                        clearable
                        type='date'
                        editable={false}
                        transfer
                        disabled-method={(e) => this.disabledDateMethod('start', row, e)}
                        on-change={({ value }) => {
                          let endDate = '9999-12-31'
                          // 1503、0530失效日期特殊处理
                          if (value) {
                            if (this.dataInfo.companyCode === '1503') {
                              endDate = XEUtils.toDateString(
                                getEndDate(new Date(value).getTime()),
                                'yyyy-MM-dd'
                              )
                            } else if (this.dataInfo.companyCode === '0530') {
                              endDate = '2099-12-31'
                            }
                          }
                          row.priceValidEndDate = value ? endDate : null
                        }}
                      />
                      <span v-show={row.status === 1}>{priceValidStartDate}</span>
                    </div>
                  ]
                }
              }
            },
            {
              field: 'priceValidEndDate',
              title: this.$t('失效日期'),
              minWidth: 140,
              editRender: {
                enabled: editable
              },
              slots: {
                default: ({ row }) => {
                  const priceValidEndDate = XEUtils.toDateString(
                    row.priceValidEndDate,
                    'yyyy-MM-dd'
                  )
                  return [<span>{priceValidEndDate}</span>]
                },
                edit: ({ row }) => {
                  const priceValidEndDate = row.priceValidEndDate
                    ? XEUtils.toDateString(row.priceValidEndDate, 'yyyy-MM-dd')
                    : ''
                  return [
                    <div>
                      <vxe-input
                        v-show={row.status !== 1}
                        v-model={row.priceValidEndDate}
                        clearable
                        type='date'
                        editable={false}
                        transfer
                        disabled-method={(e) => this.disabledDateMethod('end', row, e)}
                      />
                      <span v-show={row.status === 1}>{priceValidEndDate}</span>
                    </div>
                  ]
                }
              }
            },
            {
              field: 'status',
              title: this.$t('确认状态'),
              slots: {
                default: ({ row }) => {
                  const statusMap = {
                    0: this.$t('待确认'),
                    1: this.$t('已确认'),
                    2: this.$t('已驳回')
                  }
                  return [
                    <span>{row.status || row.status === 0 ? statusMap[row.status] : '-'}</span>
                  ]
                }
              }
            },
            {
              field: 'remark',
              title: this.$t('备注'),
              minWidth: 180,
              editRender: {
                enabled: editable
              },
              slots: {
                edit: ({ row }) => {
                  return [<vxe-input v-model={row.remark} clearable readonly={row.status === 1} />]
                }
              }
            },
            {
              field: 'priceRecordCode',
              title: this.$t('成本分析'),
              minWidth: 140,
              fixed: 'right',
              slots: {
                default: ({ row }) => {
                  return [
                    <SplitCell
                      list={row.priceRecordCodeList}
                      type='operation'
                      operationName={this.$t('成本分析')}
                      on-click={(index) => this.handleCostAnalysis(row, index)}
                    />
                  ]
                }
              }
            }
          ]
        case 'quota':
          return [
            {
              field: 'factoryCode',
              title: this.$t('工厂编码')
            },
            {
              field: 'factoryName',
              title: this.$t('工厂名称')
            },
            {
              field: 'materialCode',
              title: this.$t('物料编码')
            },
            {
              field: 'materialName',
              title: this.$t('物料名称')
            },
            {
              field: 'supplierCode',
              title: this.$t('供应商编码')
            },
            {
              field: 'supplierName',
              title: this.$t('供应商名称')
            },
            {
              field: 'allocationRate',
              title: this.$t('配额比例'),
              minWidth: 140,
              editRender: {
                enabled: [6, 9].includes(this.dataInfo.status)
              },
              slots: {
                default: ({ row }) => {
                  return [
                    <span style='padding: 0 10px;'>
                      {row.allocationRate}
                      {row.allocationRate || row.allocationRate === 0 ? '%' : ''}
                    </span>
                  ]
                },
                edit: ({ row }) => {
                  return [
                    <vxe-input
                      v-model={row.allocationRate}
                      clearable
                      type='integer'
                      min='0'
                      max='100'
                    />
                  ]
                }
              }
            },
            {
              field: 'stepValue',
              title: this.$t('阶梯数量'),
              slots: {
                default: ({ row }) => {
                  return [<SplitCell list={row.stepValueList} />]
                }
              }
            },
            {
              field: 'unitPriceUntaxed',
              title: this.$t('单价（未税）'),
              slots: {
                default: ({ row }) => {
                  return [<SplitCell list={row.unitPriceUntaxedList} />]
                }
              }
            },
            {
              field: 'unitPriceTaxed',
              title: this.$t('单价（含税）'),
              slots: {
                default: ({ row }) => {
                  return [<SplitCell list={row.unitPriceTaxedList} />]
                }
              }
            },
            {
              field: 'priceUnitName',
              title: this.$t('价格单位')
            },
            {
              field: 'priceValidStartDate',
              title: this.$t('报价生效日期'),
              minWidth: 140,
              editRender: {
                enabled: [6, 9].includes(this.dataInfo.status)
              },
              slots: {
                default: ({ row }) => {
                  const priceValidStartDate = XEUtils.toDateString(
                    row.priceValidStartDate,
                    'yyyy-MM-dd'
                  )
                  return [<span>{priceValidStartDate}</span>]
                },
                edit: ({ row }) => {
                  return [
                    <vxe-input
                      v-model={row.priceValidStartDate}
                      clearable
                      type='date'
                      editable={false}
                      transfer
                      disabled-method={(e) => this.disabledDateMethod('start', row, e)}
                      on-change={({ value }) => {
                        let endDate = '9999-12-31'
                        // 1503、0530失效日期特殊处理
                        if (value) {
                          if (this.dataInfo.companyCode === '1503') {
                            endDate = XEUtils.toDateString(
                              getEndDate(new Date(value).getTime()),
                              'yyyy-MM-dd'
                            )
                          } else if (this.dataInfo.companyCode === '0530') {
                            endDate = '2099-12-31'
                          }
                        }
                        row.priceValidEndDate = value ? endDate : null
                      }}
                    />
                  ]
                }
              }
            },
            {
              field: 'priceValidEndDate',
              title: this.$t('报价失效日期'),
              minWidth: 140,
              editRender: {
                enabled: [6, 9].includes(this.dataInfo.status)
              },
              slots: {
                default: ({ row }) => {
                  const priceValidEndDate = XEUtils.toDateString(
                    row.priceValidEndDate,
                    'yyyy-MM-dd'
                  )
                  return [<span>{priceValidEndDate}</span>]
                },
                edit: ({ row }) => {
                  return [
                    <vxe-input
                      v-model={row.priceValidEndDate}
                      clearable
                      type='date'
                      editable={false}
                      transfer
                      disabled-method={(e) => this.disabledDateMethod('end', row, e)}
                    />
                  ]
                }
              }
            },
            {
              field: 'minLiftingDsmPoint',
              title: this.$t('最小起拆点')
            },
            {
              field: 'minPackageQty',
              title: this.$t('最小包装量')
            },
            {
              field: 'minPurchaseQty',
              title: this.$t('最小采购量')
            },
            {
              field: 'quoteAttr',
              title: this.$t('报价属性'),
              slots: {
                default: ({ row }) => {
                  const quoteAttrMap = {
                    mailing_price: this.$t('寄售价'),
                    standard_price: this.$t('标准价'),
                    outsource: this.$t('委外价')
                  }
                  return [<span>{row.quoteAttr ? quoteAttrMap[row.quoteAttr] : '-'}</span>]
                }
              }
            },
            {
              field: 'sourceType',
              title: this.$t('来源'),
              slots: {
                default: ({ row }) => {
                  const sourceTypeMap = {
                    0: this.$t('内部'),
                    1: this.$t('外部')
                  }
                  return [
                    <span>
                      {[0, 1].includes(row.sourceType) ? sourceTypeMap[row.sourceType] : '-'}
                    </span>
                  ]
                }
              }
            },
            {
              field: 'remark',
              title: this.$t('备注'),
              minWidth: 180,
              editRender: {
                enabled: [6, 9].includes(this.dataInfo.status)
              },
              slots: {
                edit: ({ row }) => {
                  return [<vxe-input v-model={row.remark} clearable />]
                }
              }
            }
          ]
        default:
          return []
      }
    },
    dataList() {
      const dataList = cloneDeep(this.tableData)
      dataList.forEach((item) => {
        if (item.isAdd) {
          item.id = null
        } else if (this.tableRef.isUpdateByRow(item)) {
          item.optType = 'modify'
        }
      })
      return dataList
    }
  },
  mounted() {},
  methods: {
    // 限制日期选择
    disabledDateMethod(type, row, e) {
      const { date } = e
      if (type === 'start') {
        const endTime = row.priceValidEndDate ? new Date(row.priceValidEndDate).getTime() : null
        return endTime ? endTime - 86400000 < new Date(date).getTime() : false
      }
      if (type === 'end') {
        const startTime = row.priceValidStartDate
          ? new Date(row.priceValidStartDate).getTime()
          : null
        return startTime + 86400000 ? startTime > new Date(date).getTime() : false
      }
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.getTableData()
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.getTableData()
    },
    // 对数据进行分组
    groupData(list) {
      const groupKeyList = []
      const tempList = []
      const resList = []
      list.forEach((item) => {
        const { itemGroupId, id } = item
        const groupKey = itemGroupId || id
        if (!groupKeyList.includes(groupKey)) {
          groupKeyList.push(groupKey)
          tempList.push([])
        }
        const i = groupKeyList.indexOf(groupKey)
        tempList[i].push(item)
      })
      tempList.forEach((item) => {
        const obj = {}
        item.forEach((t) => {
          this.unmergeList.forEach((key) => {
            if (!obj[key + 'List']) {
              obj[key + 'List'] = []
            }
            let value = t[key]
            // 历史降幅，保留两位小数
            if (key === 'declinePercent') {
              value = t[key] || t[key] === 0 ? Number(t[key]).toFixed(2) : null
            }
            obj[key + 'List'].push(value)
          })
        })
        resList.push({ ...item[0], ...obj, rowSpan: item.length })
      })
      return resList
    },
    // 单元格样式
    handleCellStyle(e) {
      const { row, column } = e
      const height = row?.rowSpan ? row.rowSpan * 32 : 32
      let padding = ''
      if (!this.unmergeList.includes(column.field)) {
        padding = '0 10px'
      }
      return { padding, height: `${height}px` }
    }
  }
}
