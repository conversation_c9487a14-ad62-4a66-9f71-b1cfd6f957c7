import { i18n } from '@/main.js'
import Vue from 'vue'

export const toolObj = {
  delete: { code: 'delete', name: i18n.t('删除'), status: 'info' },
  upload: { code: 'upload', name: i18n.t('上传'), status: 'info' },
  download: { code: 'download', name: i18n.t('下载'), status: 'info' }
}

export const fileTypeIconSetting = {
  '.ppt': 'vxe-icon-file-ppt',
  '.doc': 'vxe-icon-file-word',
  '.docx': 'vxe-icon-file-word',
  '.pdf': 'vxe-icon-file-pdf',
  '.xls': 'vxe-icon-file-excel',
  '.xlsx': 'vxe-icon-file-excel',
  '.txt': 'vxe-icon-file-txt',
  '.png': 'vxe-icon-file-image',
  '.jpg': 'vxe-icon-file-image'
}

export const treeViewData = {
  //相关文件-目录结构-原数据
  nodeTemplate: function () {
    return {
      template: Vue.component('common', {
        template: `<div class="action-boxs">
                      <div>{{getNodeName}}</div>
                    </div>`,
        data() {
          return { data: {} }
        },
        computed: {
          getNodeName() {
            return i18n.t(this?.mtData?.nodeName)
          }
        },
        props: {
          mtData: {
            type: Object,
            default: () => {}
          }
        }
      })
    }
  },
  dataSource: [],
  id: 'id',
  text: 'nodeName',
  child: 'fileNodeResponseList'
}
