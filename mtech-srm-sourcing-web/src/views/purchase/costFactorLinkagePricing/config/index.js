import { i18n } from '@/main'

// 状态列表
export const statusList = [
  { value: -1, text: i18n.t('已失效') },
  { value: 0, text: i18n.t('草稿') },
  { value: 1, text: i18n.t('采方正在发布中') },
  { value: 2, text: i18n.t('供应商确认中') },
  { value: 3, text: i18n.t('供应商部分确认') },
  { value: 4, text: i18n.t('供应商已确认') },
  { value: 5, text: i18n.t('供应商已驳回') },
  { value: 6, text: i18n.t('定价中') },
  { value: 7, text: i18n.t('审批中') },
  { value: 8, text: i18n.t('审批通过') },
  { value: 9, text: i18n.t('审批驳回') }
]
// 价格分类列表
export const priceClassifyList = [
  { value: '1', text: i18n.t('基价') },
  { value: '2', text: i18n.t('SRM价格') },
  { value: '3', text: i18n.t('暂估价格') },
  { value: '4', text: i18n.t('执行价格') }
]
// 定价单类型
export const orderTypeList = [{ value: '0', text: i18n.t('因子联动部品询报价') }]
// 单据来源
export const sourceList = [{ value: '0', text: i18n.t('手动创建') }]
// 是否阶梯报价
export const stepQuoteList = [
  { value: 0, text: i18n.t('否') },
  { value: 1, text: i18n.t('是') }
]

// 查询列表-操作按钮
export const listToolbar = [
  { code: 'add', name: i18n.t('新增'), status: 'info' },
  { code: 'delete', name: i18n.t('删除'), status: 'info' },
  { code: 'publish', name: i18n.t('发布'), status: 'info' },
  { code: 'export', name: i18n.t('导出'), status: 'info' }
]
