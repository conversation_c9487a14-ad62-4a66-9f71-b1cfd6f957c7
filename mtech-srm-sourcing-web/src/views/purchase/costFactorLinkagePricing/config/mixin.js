import { statusList, priceClassifyList } from './index'

export default {
  data() {
    return {
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 20,
        pageSizes: [20, 50, 200, 500, 1000, 2000]
      },
      pageInfo: {
        size: 20,
        current: 1
      },
      companyList: [],
      purchaseOrgList: [],
      factoryList: [],
      sourcingExpandList: [],
      purchaserList: []
    }
  },
  computed: {
    detailToolbar() {
      const { id, type } = this.$route.query

      const toolbar = [
        {
          code: 'publish',
          name: this.$t('发布'),
          status: '',
          isHidden: !id || ![0, 3, 5, 9].includes(this.dataForm.status)
        },
        {
          code: 'trasnformToPricing',
          name: this.$t('转定价'),
          status: '',
          isHidden: ![3, 4].includes(this.dataForm.status)
        },
        {
          code: 'submitOA',
          name: this.$t('提交OA审批'),
          status: '',
          isHidden: this.dataForm.status !== 6
        },
        {
          code: 'save',
          name: this.$t('保存'),
          status: '',
          isHidden: type === 'edit' && [7, 8].includes(this.dataForm.status)
        },
        {
          code: 'abandon',
          name: this.$t('作废'),
          status: '',
          isHidden: [0, 7, 8].includes(this.dataForm.status)
        },
        {
          code: 'viewOA',
          name: this.$t('OA审批进度'),
          status: '',
          isHidden: ![7, 8, 9].includes(this.dataForm.status)
        },
        {
          code: 'back',
          name: this.$t('返回'),
          status: 'primary'
        }
      ]
      return toolbar
    },
    columns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'rfxCode',
          title: this.$t('定价单号'),
          minWidth: 140,
          slots: {
            default: ({ row, column }) => {
              return [<a on-click={() => this.handleClickCellTitle(row, column)}>{row.rfxCode}</a>]
            }
          }
        },
        {
          field: 'rfxName',
          title: this.$t('定价单名称'),
          minWidth: 140
        },
        {
          field: 'status',
          title: this.$t('状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = statusList.find((item) => item.value === row.status)
              const statusName = selectItem.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'companyCode',
          title: this.$t('公司'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [<span>{row.companyCode + '-' + row.companyName}</span>]
            }
          }
        },
        {
          field: 'purchaseOrgCode',
          title: this.$t('采购组织'),
          minWidth: 200,
          slots: {
            default: ({ row }) => {
              return [<span>{row.purchaseOrgCode + '-' + row.purchaseOrgName}</span>]
            }
          }
        },
        {
          field: 'factoryCode',
          title: this.$t('工厂'),
          minWidth: 200,
          slots: {
            default: ({ row }) => {
              return [<span>{row.factoryCode + '-' + row.factoryName}</span>]
            }
          }
        },
        {
          field: 'priceClassify',
          title: this.$t('价格分类'),
          slots: {
            default: ({ row }) => {
              const selectItem = priceClassifyList.find((item) => item.value == row.priceClassify)
              const priceClassifyName = selectItem?.text
              return [<div>{priceClassifyName}</div>]
            }
          }
        },
        {
          field: 'sourceName',
          title: this.$t('单据来源')
        },
        {
          field: 'docTypeName',
          title: this.$t('单据类型'),
          minWidth: 140
        },
        {
          field: 'oaApproveLink',
          title: this.$t('OA申请单查看'),
          minWidth: 120,
          slots: {
            default: ({ row, column }) => {
              return [
                <div>
                  <a
                    v-show={row.oaApproveLink}
                    on-click={() => this.handleClickCellTitle(row, column)}>
                    {this.$t('查看')}
                  </a>
                  <span v-show={!row.oaApproveLink}>-</span>
                </div>
              ]
            }
          }
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          minWidth: 140
        },
        {
          field: 'publishTime',
          title: this.$t('发布时间'),
          minWidth: 140
        }
      ]
    }
  },
  mounted() {
    this.type === 'list' && this.getFactoryList()
    if (this.$route.query.type !== 'edit') {
      this.getCompanyList()
    }
  },
  methods: {
    // 获取公司下拉列表
    async getCompanyList(isInit) {
      const res = await this.$API.masterData.permissionCompanyList()
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.companyList = res.data

        if (isInit) {
          const selectItem = this.companyList?.find(
            (item) => item.orgCode === this.dataForm?.companyCode
          )
          selectItem && this.getPurchaseOrgList(selectItem.id, true)
          !this.dataForm.companyId && (this.dataForm.companyId = selectItem.id)
        }
      }
    },
    // 获取采购组织下拉列表
    getPurchaseOrgList(companyId, isInit) {
      if (!companyId) {
        this.purchaseOrgList = []
        return
      }
      this.$API.masterData
        .permissionOrgList({
          orgId: companyId
        })
        .then((res) => {
          res.data.forEach((item) => {
            item.text = item.organizationCode + '-' + item.organizationName
          })
          this.purchaseOrgList = res.data

          if (isInit) {
            const selectItem = this.purchaseOrgList?.find(
              (item) => item.organizationCode === this.dataForm?.purchaseOrgCode
            )
            selectItem && this.getFactoryListByCompanyAndPur(companyId, selectItem.id, true)
          }
        })
    },
    // 获取工厂下拉列表
    async getFactoryList() {
      const res = await this.$API.customization.getPermissionSiteList({})
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.factoryList = res.data
      }
    },
    // 根据公司id、采购组织id获取工厂下拉列表
    async getFactoryListByCompanyAndPur(companyId, purOrgId, isInit) {
      const res = await this.$API.masterData.permissionSiteList({
        companyId,
        buOrgId: purOrgId
      })
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.factoryList = res.data

        // 采购组织下只有一个工厂，选择采购组织后自动带出
        if (!isInit && res.data?.length === 1) {
          const selectItem = res.data[0]
          this.$set(this.dataForm, 'factoryCode', selectItem.orgCode)
          this.$set(this.dataForm, 'factoryName', selectItem.orgName)
          this.getSourcingExpandList(selectItem.orgCode, this.dataForm.purchaseOrgCode)
        }
      }
    },
    // 拓展
    async getSourcingExpandList(factoryCode, purOrgCode) {
      if (!factoryCode) {
        this.sourcingExpandList = []
        return
      }
      const res = await this.$API.rfxDetail.purAllOrgWithSite({
        fuzzyParam: factoryCode,
        purOrgCode
      })
      if (res.code === 200 && res.data) {
        let dataSource = []
        res.data.forEach((v) => {
          v.siteOrgs?.forEach((x) => {
            dataSource.push({
              text: `${v.companyCode}-${v.businessOrganizationCode}-${v.businessOrganizationName}-${x.orgCode}-${x.orgName}`,
              value: v.companyCode + '+' + v.businessOrganizationCode + '+' + x.orgCode,
              data: {
                companyId: v.companyId,
                companyCode: v.companyCode,
                companyName: v.companyName,
                purchaseOrgId: v.id,
                purchaseOrgCode: v.businessOrganizationCode,
                purchaseOrgName: v.businessOrganizationName,
                factoryId: x.id,
                factoryCode: x.orgCode,
                factoryName: x.orgName
              }
            })
          })
        })
        this.sourcingExpandList = dataSource
      }
    },
    // 获取采购员下拉列表
    async getPurchaserList(searchText) {
      const res = await this.$API.masterData.getCurrentTenantEmployees({ fuzzyName: searchText })
      if (res.code === 200) {
        const tmp = []
        res.data.forEach((item) => {
          tmp.push({
            ...item,
            text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`
          })
        })
        this.purchaserList = tmp

        if (this.dataForm && tmp?.length) {
          const { employeeId, employeeName } = tmp[0]
          this.$set(this.dataForm, 'purchaserId', employeeId)
          this.$set(this.dataForm, 'purchaserName', employeeName)
        }
      }
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.handleSearch('pageChange')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.handleSearch()
    }
  }
}
