<template>
  <div class="full-height">
    <div class="top-container toggle-container" :class="[!isExpand && 'top-hidden']">
      <div class="top-content">
        <div class="top-info">
          <div class="title">
            <div v-show="!isExpand">
              <span>{{ dataForm.rfxCode }}</span>
              <span class="sub-title">{{ dataForm.rfxName }}</span>
            </div>
          </div>
          <div class="top-btns">
            <vxe-button
              v-for="(item, index) in detailToolbar"
              v-show="!item.isHidden"
              :key="index"
              :icon="item.icon"
              :status="item.status"
              :disabled="item.disabled"
              size="small"
              @click="handleClickToolBar(item)"
              >{{ item.name }}</vxe-button
            >
          </div>
        </div>
        <div class="top-form">
          <mt-form ref="dataFormRef" :model="dataForm" :rules="formRules">
            <mt-form-item prop="rfxCode" :label="$t('定价单号')" label-style="top">
              <vxe-input v-model="dataForm.rfxCode" disabled />
            </mt-form-item>
            <mt-form-item prop="rfxName" :label="$t('定价单名称')" label-style="top">
              <vxe-input
                v-model="dataForm.rfxName"
                clearable
                :disabled="!editable"
                :placeholder="$t('请输入定价单名称')"
              />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
              <vxe-select
                v-model="dataForm.companyCode"
                :options="companyList"
                :option-props="{ label: 'text', value: 'orgCode' }"
                clearable
                filterable
                :disabled="!editable || dataList[1].length !== 0"
                :placeholder="$t('请选择公司')"
                @change="(e) => handleValueChange('company', e)"
              />
            </mt-form-item>
            <mt-form-item prop="purchaseOrgCode" :label="$t('采购组织')">
              <vxe-select
                v-model="dataForm.purchaseOrgCode"
                :options="purchaseOrgList"
                :option-props="{ label: 'text', value: 'organizationCode' }"
                clearable
                filterable
                :disabled="!editable || !dataForm.companyCode || dataList[1].length !== 0"
                :placeholder="$t('请选择采购组织')"
                @change="(e) => handleValueChange('purchaseOrg', e)"
              />
            </mt-form-item>
            <mt-form-item prop="factoryCode" :label="$t('工厂')" label-style="top">
              <vxe-select
                v-model="dataForm.factoryCode"
                :options="factoryList"
                :option-props="{ label: 'text', value: 'orgCode' }"
                clearable
                filterable
                :disabled="!editable || !dataForm.purchaseOrgCode || dataList[1].length !== 0"
                :placeholder="$t('请选择工厂')"
                @change="(e) => handleValueChange('factory', e)"
              />
            </mt-form-item>
            <mt-form-item prop="expandList" :label="$t('工厂拓展')" label-style="top">
              <vxe-select
                v-model="dataForm.expandList"
                :options="sourcingExpandList"
                :option-props="{ label: 'text', value: 'value' }"
                :multi-char-overflow="
                  dataForm.expandList && dataForm.expandList.length > 1 ? 8 : -1
                "
                clearable
                filterable
                multiple
                :disabled="[7, 8].includes(dataForm.status) || !dataForm.factoryCode"
                :placeholder="$t('请选择工厂拓展')"
                @change="(e) => handleValueChange('expand', e)"
              />
            </mt-form-item>
            <mt-form-item prop="priceClassify" :label="$t('价格类型')" label-style="top">
              <vxe-select
                v-model="dataForm.priceClassify"
                :options="priceClassifyList"
                :option-props="{ label: 'text', value: 'value' }"
                :disabled="!editable || dataList[1].length !== 0"
                :placeholder="$t('请选择价格类型')"
              />
            </mt-form-item>
            <mt-form-item prop="docTypeCode" :label="$t('定价单类型')" label-style="top">
              <vxe-select
                v-model="dataForm.docTypeCode"
                :options="orderTypeList"
                :option-props="{ label: 'text', value: 'value' }"
                :disabled="!editable || dataList[1].length !== 0"
                :placeholder="$t('请选择定价单类型')"
                @change="
                  ({ value }) => {
                    const selectedItem = orderTypeList.find((item) => item.value === value)
                    dataForm.docTypeName = selectedItem ? selectedItem.text : null
                  }
                "
              />
            </mt-form-item>
            <mt-form-item prop="stepQuote" :label="$t('是否阶梯报价')" label-style="top">
              <vxe-select
                v-model="dataForm.stepQuote"
                :options="stepQuoteList"
                :option-props="{ label: 'text', value: 'value' }"
                :disabled="!editable || dataList[1].length !== 0"
                :placeholder="$t('请选择是否阶梯报价')"
              />
            </mt-form-item>
            <mt-form-item prop="purchaserName" :label="$t('采购员')" label-style="top">
              <vxe-pulldown ref="pulldownRef" destroy-on-close>
                <template #default>
                  <vxe-input
                    v-model="dataForm.purchaserName"
                    suffix-icon="vxe-icon-caret-down"
                    readonly
                    :disabled="!editable"
                    :placeholder="$t('请选择定采购员')"
                    @click="handlePulldown"
                  />
                </template>
                <template #dropdown>
                  <vxe-input
                    ref="purchaserSearcInputRef"
                    v-model="purchaserSearcValue"
                    :prefix-icon="'vxe-icon-search'"
                    clearable
                    :placeholder="$t('搜索')"
                    @input="handlePulldownSearchInput"
                  />
                  <vxe-list class="my-dropdown2" :data="purchaserList" auto-resize height="auto">
                    <template #default="{ items }">
                      <div v-if="items.length">
                        <div
                          class="list-item2"
                          v-for="item in items"
                          :key="item.employeeId"
                          @click="handlePulldownItemSelected(item)"
                        >
                          <span :class="{ isSelected: item.employeeId === dataForm.purchaserId }">{{
                            item.text
                          }}</span>
                        </div>
                      </div>
                      <div v-else class="empty-tip">
                        {{ $t('暂无数据') }}
                      </div>
                    </template>
                  </vxe-list>
                </template>
              </vxe-pulldown>
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('单据状态')" label-style="top">
              <vxe-select
                v-model="dataForm.status"
                :options="statusList"
                :option-props="{ label: 'text', value: 'value' }"
                disabled
              />
            </mt-form-item>
            <mt-form-item prop="sourceCode" :label="$t('单据来源')" label-style="top">
              <vxe-select
                v-model="dataForm.sourceCode"
                :options="sourceList"
                :option-props="{ label: 'text', value: 'value' }"
                disabled
                @change="
                  ({ value }) => {
                    const selectedItem = sourceList.find((item) => item.value === value)
                    dataForm.sourceName = selectedItem ? selectedItem.text : null
                  }
                "
              />
            </mt-form-item>
            <mt-form-item prop="endTime" :label="$t('确认截止时间')" label-style="top">
              <vxe-input
                v-model="dataForm.endTime"
                type="datetime"
                clearable
                :editable="false"
                :disabled="!editable && dataForm.status !== 3"
                :placeholder="$t('请选择确认截止时间')"
              />
            </mt-form-item>
            <mt-form-item prop="remark" :label="$t('备注')" label-style="top">
              <vxe-textarea
                v-model="dataForm.remark"
                clearable
                :disabled="[7, 8].includes(dataForm.status)"
                :rows="1"
                :placeholder="$t('请输入备注')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </div>
      <div class="top-toggle-btn">
        <div @click="handleToggle"><i class="vxe-icon-arrow-up" /></div>
      </div>
    </div>
    <div class="body-container" v-show="$route.query.type === 'edit'">
      <mt-tabs
        ref="mtTabsRef"
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabList"
        :halt-select="false"
        @handleSelectTab="(index, item) => handleTabChange(index)"
        style="background-color: #fff"
      />
      <keep-alive :include="keepArr">
        <component
          ref="mainContent"
          :is="activeComponent"
          :data-info="dataForm"
          @updateDetail="init"
        />
      </keep-alive>
    </div>
  </div>
</template>

<script>
import {
  Input as VxeInput,
  Button as VxeButton,
  Select as VxeSelect,
  Textarea as VxeTextarea
} from 'vxe-table'
import mixin from './config/mixin'
import {
  priceClassifyList,
  orderTypeList,
  statusList,
  sourceList,
  stepQuoteList
} from './config/index'
import debounce from 'lodash.debounce'

export default {
  name: 'ExpertRating',
  components: {
    VxeInput,
    VxeButton,
    VxeSelect,
    VxeTextarea
  },
  mixins: [mixin],
  data() {
    return {
      priceClassifyList,
      orderTypeList,
      statusList,
      sourceList,
      stepQuoteList,
      dataForm: {},
      activeTabIndex: 0,
      isExpand: true,
      dataList: [[], [], [], []],
      type: 'detail',
      isInit: true,
      purchaserSearcValue: null,
      keepArr: ['CostFactorTab', 'MaterialDetailTab', 'QuotaDetailTab', 'AttachmentTab']
    }
  },
  computed: {
    editable() {
      return this.$route.query.type === 'create' || [0, 5, 9].includes(this.dataForm.status)
    },
    tabList() {
      const tabs = [
        { title: this.$t('成本因子'), compName: 'CostFactorTab' },
        { title: this.$t('物料明细'), compName: 'MaterialDetailTab' },
        { title: this.$t('附件'), compName: 'AttachmentTab' }
      ]
      if ([6, 7, 8, 9].includes(this.dataForm.status)) {
        tabs.splice(2, 0, { title: this.$t('配额明细'), compName: 'QuotaDetailTab' })
      }
      return tabs
    },
    activeComponent() {
      let comp = ''
      switch (this.activeTabIndex) {
        case 0:
          // 成本因子
          comp = () => import('./components/costFactorTab.vue')
          break
        case 1:
          // 物料明细
          comp = () => import('./components/materialDetailTab.vue')
          break
        case 2:
          if (this.tabList.length === 4) {
            // 配额明细
            comp = () => import('./components/quotaDetailTab.vue')
          } else {
            // 附件
            comp = () => import('./components/attachmentTab.vue')
          }
          break
        case 3:
          // 附件
          comp = () => import('./components/attachmentTab.vue')
          break
        default:
          return
      }
      return comp
    },
    formRules() {
      return {
        rfxCode: [
          {
            required: this.$route.query.type === 'edit',
            message: this.$t('请输入定价单名称'),
            trigger: 'blur'
          }
        ],
        rfxName: [{ required: true, message: this.$t('请输入定价单名称'), trigger: 'blur' }],
        companyCode: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        purchaseOrgCode: [{ required: true, message: this.$t('请选择采购组织'), trigger: 'blur' }],
        factoryCode: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }],
        priceClassify: [{ required: true, message: this.$t('请选择价格类型'), trigger: 'blur' }],
        docTypeCode: [{ required: true, message: this.$t('请选择定价单类型'), trigger: 'blur' }],
        stepQuote: [{ required: true, message: this.$t('请选择是否阶梯'), trigger: 'blur' }],
        sourceCode: [{ required: true, message: this.$t('请选择单据来源'), trigger: 'blur' }],
        endTime: [{ required: true, message: this.$t('请选择确认截止时间'), trigger: 'blur' }]
      }
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    // 初始化
    init() {
      if (this.$route.query.type === 'edit') {
        this.getHeaderInfo()
        this.initDataList()
        this.isExpand = false
      } else {
        this.getPurchaserList()
        this.dataForm = {
          companyCode: null,
          priceClassify: '4',
          docTypeCode: '0',
          docTypeName: orderTypeList[0].text,
          stepQuote: 0,
          status: 0,
          sourceCode: '0',
          sourceName: sourceList[0].text
        }
      }
    },
    // 获取头部基础信息
    async getHeaderInfo() {
      const res = await this.$API.costFactorLinkagePricing.queryCflpHeaderInfo({
        id: this.$route.query.id
      })
      if (res.code === 200) {
        const expandList = []
        res.data?.extendFactoryList?.forEach((t) => {
          expandList.push(t.companyCode + '+' + t.purchaseOrgCode + '+' + t.factoryCode)
        })
        this.dataForm = { ...res.data, expandList }
        this.getCompanyList(true)
        this.getSourcingExpandList(res.data.factoryCode, res.data.purchaseOrgCode)
        this.getPurchaserList(res.data?.purchaserName)
      }
    },
    // 获取tab页签列表数据
    async initDataList() {
      const params = {
        rfxId: this.$route.query.id,
        page: {
          size: 20,
          current: 1
        }
      }

      const tempList = [
        'queryCflpCfDetailList', // 成本因子
        'queryCflpMdDetailList', // 物料明细
        'queryCflpQuotaDetailList' //配额明细
      ]
      const tasks = tempList.map((funcName) => this.$API.costFactorLinkagePricing[funcName](params))

      const result = await Promise.all(tasks)
      result?.forEach((res, index) => {
        this.$set(this.dataList, index, res.data?.records || [])
      })
    },
    // 下拉列表选中值修改
    handleValueChange(prefix, e) {
      const { value } = e
      switch (prefix) {
        // 选择公司
        case 'company':
          this.$set(this.dataForm, 'purchaseOrgCode', null)
          this.$set(this.dataForm, 'factoryCode', null)
          this.$set(this.dataForm, 'expandList', [])

          if (value) {
            const selectedItem = this.companyList.find((item) => item.orgCode === value)
            this.dataForm.companyId = selectedItem ? selectedItem.id : null
            this.dataForm.companyName = selectedItem ? selectedItem.orgName : null

            this.getPurchaseOrgList(selectedItem?.id)
          } else {
            this.dataForm.companyId = null
            this.dataForm.companyName = null
          }
          break
        // 选择采购组织
        case 'purchaseOrg':
          this.$set(this.dataForm, 'factoryCode', null)
          this.$set(this.dataForm, 'expandList', [])
          if (value) {
            const selectedItem = this.purchaseOrgList.find(
              (item) => item.organizationCode === value
            )
            this.dataForm.purchaseOrgId = selectedItem ? selectedItem.id : null
            this.dataForm.purchaseOrgName = selectedItem ? selectedItem.organizationName : null

            const { companyId, purchaseOrgId } = this.dataForm
            this.getFactoryListByCompanyAndPur(companyId, purchaseOrgId)
          } else {
            this.dataForm.purchaseOrgId = null
            this.dataForm.purchaseOrgName = null

            this.dataForm.factoryCode = null
            this.dataForm.factoryName = null
          }
          break
        // 选择工厂
        case 'factory':
          this.$set(this.dataForm, 'expandList', [])
          if (value) {
            const selectedItem = this.factoryList.find((item) => item.orgCode === value)
            this.dataForm.factoryName = selectedItem ? selectedItem.orgName : null
          } else {
            this.dataForm.factoryName = null
          }
          this.getSourcingExpandList(value, this.dataForm.purchaseOrgCode)
          break
        default:
          break
      }
    },
    // 远程搜索查询-展开面板
    handlePulldown() {
      this.$refs.pulldownRef?.showPanel()
      this.$nextTick(() => {
        this.$refs.purchaserSearcInputRef?.focus()
      })
    },
    // 远程搜索查询-查询
    handlePulldownSearchInput: debounce(function (e) {
      this.getPurchaserList(e?.value)
    }, 500),
    // 远程搜索查询-选中
    handlePulldownItemSelected(item) {
      if (this.$refs.pulldownRef) {
        this.$set(this.dataForm, 'purchaserId', item.employeeId)
        this.$set(this.dataForm, 'purchaserName', item.employeeName)
        this.$refs.pulldownRef.hidePanel()
        this.purchaserSearcValue = null
      }
    },
    // 点击工具栏按钮
    handleClickToolBar(e) {
      switch (e.code) {
        case 'back':
          this.handleBack()
          break
        case 'delete':
          this.handleDelete()
          break
        case 'abandon':
          this.handleAbandon()
          break
        case 'save':
        case 'publish':
          this.handleSaveOrPublish(e.code)
          break
        case 'trasnformToPricing':
          this.handleTransformToPricing()
          break
        case 'submitOA':
          this.hanldeSubmitOA()
          break
        case 'viewOA':
          this.handleViewOA()
          break
        default:
          break
      }
    },
    // 切换tab页签
    handleTabChange(index) {
      // 保存即将离开tab页签的数据
      this.dataList[this.activeTabIndex] = this.$refs.mainContent?.dataList || []

      this.$refs.mtTabsRef.activeTab = index
      this.activeTabIndex = index

      // 若该tab页修改数据后保存成功从keepArr中移除了，再次进入该页面需要重新加入缓存
      const tabName = this.tabList[index]?.compName
      !this.keepArr.includes(tabName) && this.keepArr.push(tabName)
    },
    // 返回
    handleBack() {
      this.$router.go(-1)
    },
    // 保存、发布
    async handleSaveOrPublish(type) {
      this.$refs.dataFormRef.validate(async (valid) => {
        if (valid) {
          const latestDataList = this.$refs.mainContent.dataList || []
          // 成本因子明细列表
          let factorItemList = this.activeTabIndex === 0 ? latestDataList : this.dataList[0]
          // 物料明细列表
          let materialItemList = this.activeTabIndex === 1 ? latestDataList : this.dataList[1]
          // 配额明显列表
          let quotaItemList = this.activeTabIndex === 2 ? latestDataList : this.dataList[2]

          // 工厂拓展列表
          let extendFactoryList = []
          this.dataForm.expandList?.forEach((item) => {
            const selectedItem = this.sourcingExpandList.find((t) => t.value === item)
            extendFactoryList.push(selectedItem?.data)
          })

          if (type === 'publish') {
            if (!materialItemList?.length) {
              this.$toast({ content: this.$t('无物料，不允许发布！'), type: 'warning' })
              return
            }
          }

          const params = {
            ...this.dataForm,
            extendFactoryList,
            factorItemList,
            materialItemList,
            quotaItemList
          }
          type === 'save' ? this.handleSave(params) : this.handlePublish(params)
        } else {
          this.isExpand = true
        }
      })
    },
    // 保存
    async handleSave(params) {
      const res = await this.$API.costFactorLinkagePricing.saveCflpAll(params)
      if (res.code === 200) {
        this.$toast({ content: this.$t('操作成功！'), type: 'success' })
        if (this.$route.query.type === 'create') {
          this.$router.replace({
            name: 'cost-factor-linkage-pricing-detail',
            query: {
              type: 'edit',
              id: res.data
            }
          })
        } else {
          this.getHeaderInfo()
          this.refreshKeepArr(params)
          if (this.activeComponent?.name !== 'AttachmentTab') {
            this.$refs.mainContent?.getTableData()
          }
        }
      }
    },
    // 发布
    async handlePublish(params) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确定发布？')
        },
        success: async () => {
          const res = await this.$API.costFactorLinkagePricing.publishCflpAll(params)
          if (res.code === 200) {
            this.$toast({ content: this.$t('操作成功！'), type: 'success' })
            this.getHeaderInfo()
            this.refreshKeepArr(params)
            if (this.activeComponent?.name !== 'AttachmentTab') {
              this.$refs.mainContent?.getTableData()
            }
          }
        }
      })
    },
    // 更新缓存列表
    async refreshKeepArr(lists) {
      const { factorItemList, materialItemList, quotaItemList } = lists
      const factorItem = factorItemList?.find((item) => item.isAdd || item.optType === 'modify')
      const materialItem = materialItemList?.find((item) => item.isAdd || item.optType === 'modify')
      const quotaItem = quotaItemList?.find((item) => item.isAdd || item.optType === 'modify')

      // 对于修改过数据的tab页签，清除缓存，未更新的列表增加缓存
      this.keepArr = ['AttachmentTab']
      !factorItem && this.keepArr.push('CostFactorTab')
      !materialItem && this.keepArr.push('MaterialDetailTab')
      !quotaItem && this.keepArr.push('QuotaDetailTab')

      // tab中数据更新，择重新初始化dataList，避免新增的数据行重复新增
      if (factorItem || materialItem || quotaItem) {
        this.initDataList()
      }
    },
    // 转定价
    async handleTransformToPricing() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确定转定价？')
        },
        success: async () => {
          const res = await this.$API.costFactorLinkagePricing.trasnformToPricingCflp({
            id: this.$route.query.id
          })
          if (res.code === 200) {
            this.$toast({ content: this.$t('操作成功！'), type: 'success' })
            this.getHeaderInfo()
            this.$refs.mainContent?.getTableData()
          }
        }
      })
    },
    // 提交OA审批
    hanldeSubmitOA() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确定提交OA审批？')
        },
        success: async () => {
          const quotaItemList =
            this.activeTabIndex === 2 ? this.$refs.mainContent.dataList || [] : this.dataList[2]
          const params = {
            id: this.$route.query.id,
            quotaItemList
          }
          const res = await this.$API.costFactorLinkagePricing.submitOACflp(params)
          if (res.code === 200) {
            this.$toast({ content: this.$t('操作成功！'), type: 'success' })
            this.getHeaderInfo()
            this.$refs.mainContent?.getTableData()
            this.refreshKeepArr(params)
          }
        }
      })
    },
    // 查看OA审批进度
    async handleViewOA() {
      let oaApproveLink = this.dataForm?.oaApproveLink
      if (!oaApproveLink) {
        const res = await this.$API.costFactorLinkagePricing.queryOALinkCflp({
          id: this.$route.query.id
        })
        res.code === 200 && (oaApproveLink = res.data)
      }
      oaApproveLink
        ? window.open(oaApproveLink)
        : this.$toast({ content: this.$t('暂无申请单'), type: 'error' })
    },
    // 删除
    handleDelete() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确定删除？')
        },
        success: async () => {
          const ids = [this.$route.query.id]
          const res = await this.$API.costFactorLinkagePricing.deleteCflp({ ids })
          if (res.code === 200) {
            this.$toast({ content: this.$t('删除成功！'), type: 'success' })
            this.handleBack()
          }
        }
      })
    },
    // 作废
    handleAbandon() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确定作废？')
        },
        success: async () => {
          const res = await this.$API.costFactorLinkagePricing.abandonCflp({
            rfxId: this.$route.query.id
          })
          if (res.code === 200) {
            this.$toast({ content: this.$t('作废成功！'), type: 'success' })
            this.handleBack()
          }
        }
      })
    },
    // 展开/收缩
    handleToggle() {
      this.isExpand = !this.isExpand
    }
  }
}
</script>

<style scoped lang="scss">
.full-height {
  height: 100%;
  padding: 8px;
  background: #fff;
}

.top-container {
  width: 100%;
  .top-content {
    padding: 8px 8px 0 8px;
    background-color: #f9f9f9;
    border-radius: 8px;
    .top-info {
      display: flex;
      justify-content: space-between;
      .title {
        font-size: 20px;
        font-weight: 700;
        color: #292929;
        span {
          margin-right: 15px;
        }
        .sub-title {
          font-size: 15px;
          color: #777;
        }
      }
      .top-btns {
        text-align: right;
        margin-bottom: 10px;
      }
    }
    .top-form {
      .mt-form {
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
        justify-content: space-between;
        grid-gap: 5px;
      }
      .vxe-input,
      .vxe-select,
      .vxe-pulldown {
        width: 100%;
        height: 34px;
        line-height: 34px;
      }
      /deep/.vxe-pulldown--panel {
        min-width: unset !important;
        width: 100%;
      }
    }
  }
  .top-toggle-btn {
    width: 100%;
    div {
      width: 80px;
      height: 15px;
      line-height: 15px;
      margin: auto;
      text-align: center;
      background-color: #4a556b;
      color: #fff;
      font-weight: bold;
      border-radius: 0 0 6px 6px;
    }
  }
}

.top-hidden {
  .top-form {
    display: none;
  }
  .top-toggle-btn {
    div {
      transform: rotate(180deg);
      border-radius: 6px 6px 0 0;
    }
  }
}

.my-dropdown2 {
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 4px 0;
  max-height: 200px;
  border-radius: 4px;
  border: 1px solid #dadce0;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}
.list-item2 {
  line-height: 30px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 0 4px;

  .isSelected {
    font-weight: 700;
    color: #409eff;
  }
}
.list-item2:hover {
  background-color: #f5f7fa;
}
.empty-tip {
  width: 100%;
  line-height: 30px;
  padding: 0 4px;
  text-align: center;
  color: #c0c4cc;
}

::v-deep {
  .mt-form-item {
    margin-bottom: 15px;
    .label {
      margin: 0 10px 5px 0;
    }
  }
  .mt-tabs-container {
    background-color: #fff;
  }

  .mt-tabs-container ul.tab-container li.tab-item2.active {
    border-bottom: 2px solid #409eff;
  }
  .mt-tabs-container ul.tab-container li.tab-item2.active,
  .mt-tabs-container ul.tab-container li.tab-item2:hover {
    color: #31374e;
  }

  .vxe-textarea--inner {
    min-height: 34px;
    height: 100%;
  }
}
</style>
