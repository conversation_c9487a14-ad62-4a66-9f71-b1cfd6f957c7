<template>
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="rfxCode" :label="$t('定价单号')" label-style="top">
          <mt-input
            v-model="searchFormModel.rfxCode"
            :show-clear-button="true"
            :placeholder="$t('请输入定价单号')"
          />
        </mt-form-item>
        <mt-form-item prop="rfxName" :label="$t('定价单名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.rfxName"
            :show-clear-button="true"
            :placeholder="$t('请输入定价单名称')"
          />
        </mt-form-item>
        <mt-form-item prop="costFactorCode" :label="$t('成本因子')" label-style="top">
          <mt-input
            v-model="searchFormModel.costFactorCode"
            :show-clear-button="true"
            :placeholder="$t('请输入成本因子')"
          />
        </mt-form-item>
        <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
          <mt-select
            v-model="searchFormModel.companyCode"
            css-class="rule-element"
            :data-source="companyList"
            :fields="{ text: 'text', value: 'orgCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择公司')"
            @change="handleCompanyChange"
          />
        </mt-form-item>
        <mt-form-item prop="purchaseOrgCode" :label="$t('采购组织')">
          <mt-select
            v-model="searchFormModel.purchaseOrgCode"
            :disabled="!searchFormModel.companyCode"
            :data-source="purchaseOrgList"
            :fields="{ text: 'organizationName', value: 'organizationCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择采购组织')"
          />
        </mt-form-item>
        <mt-form-item prop="factoryCode" :label="$t('工厂')" label-style="top">
          <mt-select
            v-model="searchFormModel.factoryCode"
            css-class="rule-element"
            :data-source="factoryList"
            :fields="{ text: 'text', value: 'orgCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择工厂')"
          />
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.status"
            css-class="rule-element"
            :data-source="statusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择状态')"
          />
        </mt-form-item>
        <mt-form-item prop="publishTime" :label="$t('发布日期')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.publishTime"
            :allow-edit="false"
            :placeholder="$t('请选择提交日期')"
            :open-on-focus="true"
            @change="handleDateChange"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      grid-id="f976dd8f-d024-4a67-8bab-21c7f624b483"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import { statusList, priceClassifyList, listToolbar } from './config/index'
import XEUtils from 'xe-utils'

export default {
  components: {
    ScTable,
    CollapseSearch
  },
  mixins: [mixin],
  data() {
    return {
      statusList,
      priceClassifyList,
      toolbar: listToolbar,
      searchFormModel: {},
      tableData: [],
      loading: false,
      type: 'list'
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  mounted() {
    this.handleSearch()
  },
  activated() {
    this.handleSearch()
  },
  methods: {
    // 选择公司
    handleCompanyChange(e) {
      this.$set(this.searchFormModel, 'purchaseOrgCode', null)
      if (e.itemData.id) {
        this.getPurchaseOrgList(e.itemData.id)
      } else {
        this.purchaseOrgList = []
      }
    },
    // 日期格式化
    handleDateChange(e) {
      if (e.startDate) {
        const startDate = XEUtils.toDateString(e.startDate, 'yyyy-MM-dd') + ' 00:00:00'
        const endDate = XEUtils.toDateString(e.endDate, 'yyyy-MM-dd') + ' 23:59:59'
        this.searchFormModel['publishStartTime'] = XEUtils.timestamp(startDate)
        this.searchFormModel['publishEndTime'] = XEUtils.timestamp(endDate)
      } else {
        this.searchFormModel['publishStartTime'] = null
        this.searchFormModel['publishEndTime'] = null
      }
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    // 查询
    async handleSearch(type) {
      // 解决修改查询条件后，查询结果有数据但未正常显示的问题（type: pageChange/refresh）
      if (!type) {
        this.pageInfo.current = 1
        this.$refs.pageRef.jumpNum = 1
        this.$refs.pageRef.currentPage = 1
      }
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      delete params.submitTime
      this.loading = true
      const res = await this.$API.costFactorLinkagePricing
        .queryCflpList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []

        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (['delete', 'submit'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
        case 'export':
          this.handleExport()
          break
        case 'publish':
          this.handleOperate(e.code, selectedRecords)
          break
        default:
          break
      }
    },
    handleExport() {
      const params = { page: { size: 9999, current: 1 }, ...this.searchFormModel } // 筛选条件
      this.$API.costFactorLinkagePricing.exportCflpList(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 点击单元格
    handleClickCellTitle(row, column) {
      switch (column.field) {
        case 'rfxCode':
          this.$router.push({
            name: 'cost-factor-linkage-pricing-detail',
            query: {
              type: 'edit',
              id: row.id,
              refreshId: Date.now()
            }
          })
          break
        case 'oaApproveLink':
          window.open(row.oaApproveLink)
          break
        default:
          break
      }
    },
    // 新增
    handleAdd() {
      this.$router.push({
        name: 'cost-factor-linkage-pricing-detail',
        query: {
          type: 'create',
          refreshId: Date.now()
        }
      })
    },
    // 删除、发布
    handleOperate(type, list) {
      const tipMap = {
        delete: this.$t('删除'),
        publish: this.$t('发布')
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认${tipMap[type]}选中的数据？`)
        },
        success: async () => {
          const ids = []
          list.forEach((item) => ids.push(item.id))
          const res = await this.$API.costFactorLinkagePricing[type + 'Cflp']({ ids })
          if (res.code === 200) {
            this.$toast({ content: this.$t(`${tipMap[type]}成功！`), type: 'success' })
            this.handleSearch()
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
