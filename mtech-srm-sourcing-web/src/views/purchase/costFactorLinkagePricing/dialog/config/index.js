import { i18n } from '@/main.js'

const supplierColumn = [
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    operator: 'contains'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    operator: 'contains'
  }
]
export const dialogPageConfig = (url, queryInfo) => {
  return [
    {
      gridId: 'e9d3bf36-e442-48e8-baed-133b68c35998',
      useToolTemplate: false,
      toolbar: [],
      grid: {
        allowFiltering: true,
        columnData: supplierColumn,
        allowSorting: false,
        allowSelection: true,
        selectionSettings: {
          type: 'Multiple',
          mode: 'Row'
        },
        asyncConfig: { url, ...queryInfo }
      }
    }
  ]
}
