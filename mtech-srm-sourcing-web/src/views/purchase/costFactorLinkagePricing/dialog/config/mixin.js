import { dialogPageConfig } from './index'

export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      searchFormModel: {},
      loading: false,
      tableData: [],
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalRecordsCount: 0,
        totalPages: 1,
        pageSize: 20,
        pageSizes: [20, 50, 100, 500, 1000, 2000]
      },
      pageInfo: {
        size: 20,
        current: 1
      },
      selectedRecords: [],
      factoryList: [],
      // 供应商放大镜弹窗配置
      supplierDialogCofig: {
        pageConfig: dialogPageConfig('/masterDataManagement/tenant/supplier/pagedQueryByOrgCode'),
        text: 'supplierName',
        value: 'supplierCode'
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    checkboxConfig() {
      return {
        reserve: true,
        checkMethod: ({ row }) => {
          // 历史单价未税等于单价未税的行，禁止勾选
          const { historyUntaxedUnitPrice, unitPriceUntaxed } = row
          return historyUntaxedUnitPrice !== unitPriceUntaxed
        }
      }
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.getFactoryList()
    this.handleSearch()
  },
  methods: {
    // 获取工厂下拉列表
    async getFactoryList() {
      const res = await this.$API.customization.getPermissionSiteList({})
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.factoryList = res.data
      }
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.$refs.supplier?.handleClear()
      this.handleSearch()
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.handleSearch('pageChange')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.handleSearch()
    },
    // 选中/取消勾选
    handleSelectChange() {
      this.selectedRecords = this.$refs.sctableRef.$refs.xGrid.getCheckboxRecords(true)
    },
    // 清空选择
    handleClearAll() {
      this.$refs.sctableRef.$refs.xGrid.setAllCheckboxRow(false)
      this.selectedRecords = []
    },
    confirm() {
      if (this.selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请至少选择一条数据'), type: 'warning' })
        return
      }
      for (let row of this.selectedRecords) {
        if (row.historyUntaxedUnitPrice === row.unitPriceUntaxed) {
          this.$toast({
            content: this.$t('包含历史价格（含税）等于单价（含税）的行，请检查后重新选择！'),
            type: 'warning'
          })
          return
        }
      }
      this.$emit('confirm-function', this.selectedRecords)
    },
    cancel() {
      this.$emit('cancel-function')
    },
    // 单元格样式
    handleCellStyle(e) {
      const { row, column } = e
      const { historyUntaxedUnitPrice, unitPriceUntaxed } = row
      let color = ''
      switch (column.field) {
        case 'historyUntaxedUnitPrice':
          color =
            historyUntaxedUnitPrice > unitPriceUntaxed
              ? 'red'
              : historyUntaxedUnitPrice < unitPriceUntaxed
              ? '#06d006'
              : null
          break
        case 'unitPriceUntaxed':
          color =
            unitPriceUntaxed > historyUntaxedUnitPrice
              ? 'red'
              : unitPriceUntaxed < historyUntaxedUnitPrice
              ? '#06d006'
              : null
          break
        default:
          break
      }
      let padding = ''
      const height = row?.rowSpan ? row.rowSpan * 32 : 32
      if (this.type === 'material' && !this.unmergeList.includes(column.field)) {
        padding = '0 10px'
      }
      return { height: `${height}px`, padding, color }
    }
  }
}
