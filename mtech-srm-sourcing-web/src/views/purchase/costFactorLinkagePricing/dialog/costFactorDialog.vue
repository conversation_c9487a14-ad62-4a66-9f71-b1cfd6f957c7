<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <!-- 自定义查询条件 -->
      <collapse-search
        class="toggle-container"
        :default-expand="false"
        @reset="handleReset"
        @search="handleSearch"
      >
        <mt-form ref="searchFormRef" :model="searchFormModel">
          <mt-form-item prop="costFactorCode" :label="$t('成本因子编码')" label-style="top">
            <mt-input
              v-model="searchFormModel.costFactorCode"
              :show-clear-button="true"
              :placeholder="$t('请输入成本因子编码')"
            />
          </mt-form-item>
          <mt-form-item prop="costFactorName" :label="$t('成本因子名称')" label-style="top">
            <mt-input
              v-model="searchFormModel.costFactorName"
              :show-clear-button="true"
              :placeholder="$t('请输入成本因子名称')"
            />
          </mt-form-item>
          <mt-form-item prop="costFactorBrand" :label="$t('品牌')" label-style="top">
            <mt-input
              v-model="searchFormModel.costFactorBrand"
              :show-clear-button="true"
              :placeholder="$t('请输入品牌')"
            />
          </mt-form-item>
          <mt-form-item prop="costFactorAttr" :label="$t('属性大类')" label-style="top">
            <mt-input
              v-model="searchFormModel.costFactorAttr"
              :show-clear-button="true"
              :placeholder="$t('请输入属性大类')"
            />
          </mt-form-item>
          <mt-form-item prop="costFactorGroup" :label="$t('属性中类')" label-style="top">
            <mt-input
              v-model="searchFormModel.costFactorGroup"
              :show-clear-button="true"
              :placeholder="$t('请输入属性中类')"
            />
          </mt-form-item>
          <mt-form-item prop="supplierCode" :label="$t('供应商')" label-style="top">
            <magnifier-input
              ref="supplier"
              :config="supplierDialogCofig"
              @change="(e) => (searchFormModel.supplierCode = e.supplierCode || null)"
            />
          </mt-form-item>
        </mt-form>
      </collapse-search>
      <!-- 表格 -->
      <sc-table
        ref="sctableRef"
        :loading="loading"
        :checkbox-config="checkboxConfig"
        :is-show-refresh-bth="true"
        :columns="columns"
        :table-data="tableData"
        :cell-style="handleCellStyle"
        @refresh="handleSearch"
        @checkbox-change="handleSelectChange"
        @checkbox-all="handleSelectChange"
      />
      <!-- 分页 -->
      <mt-page
        ref="pageRef"
        class="flex-keep custom-page"
        :page-settings="pageSettings"
        :total-pages="pageSettings.totalPages"
        :selected="selectedRecords.length"
        :show-selected="true"
        @currentChange="handleCurrentChange"
        @sizeChange="handleSizeChange"
        @clearAll="handleClearAll"
      />
    </div>
  </mt-dialog>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import magnifierInput from '@/components/magnifierInput'
import XEUtils from 'xe-utils'
import mixin from './config/mixin'

export default {
  components: { ScTable, CollapseSearch, magnifierInput },
  mixins: [mixin],
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      type: 'costFactor'
    }
  },
  computed: {
    columns() {
      const defaultColumns = [
        {
          width: 50,
          type: 'checkbox',
          fixed: 'left'
        },
        {
          field: 'costFactorCode',
          title: this.$t('成本因子编码'),
          minWidth: 120,
          fixed: 'left'
        },
        {
          field: 'costFactorName',
          title: this.$t('成本因子名称'),
          minWidth: 160
        },
        {
          field: 'costFactorSpec',
          title: this.$t('规格'),
          minWidth: 160
        },
        {
          field: 'costFactorBrand',
          title: this.$t('品牌')
        },
        {
          field: 'basicMeasureUnitName',
          title: this.$t('单位'),
          slots: {
            default: ({ row }) => {
              return [
                <span>
                  {(row.basicMeasureUnitCode || '') + '-' + (row.basicMeasureUnitName || '')}
                </span>
              ]
            }
          }
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商编码')
        },
        {
          field: 'supplierName',
          title: this.$t('供应商名称'),
          minWidth: 150
        },
        {
          field: 'historyUntaxedUnitPrice',
          title: this.$t('历史价格（未税）'),
          minWidth: 140
        },
        {
          field: 'historyTaxedUnitPrice',
          title: this.$t('历史价格（含税）'),
          minWidth: 140
        },
        {
          field: 'unitPriceUntaxed',
          title: this.$t('单价（未税）')
        },
        {
          field: 'unitPriceTaxed',
          title: this.$t('单价（含税）')
        },
        {
          field: 'costFactorAttr',
          title: this.$t('属性大类')
        },
        {
          field: 'costFactorGroup',
          title: this.$t('属性中类')
        },
        {
          field: 'priceValidStartDate',
          title: this.$t('生效日期'),
          slots: {
            default: ({ row }) => {
              const date = XEUtils.toDateString(row.priceValidStartDate, 'yyyy-MM-dd')
              return [<span>{date}</span>]
            }
          }
        },
        {
          field: 'priceValidEndDate',
          title: this.$t('失效日期'),
          slots: {
            default: ({ row }) => {
              const date = XEUtils.toDateString(row.priceValidEndDate, 'yyyy-MM-dd')
              return [<span>{date}</span>]
            }
          }
        }
      ]
      return defaultColumns
    }
  },
  mounted() {},
  methods: {
    // 查询
    async handleSearch(type) {
      // 解决修改查询条件后，查询结果有数据但未正常显示的问题（type: pageChange/refresh）
      if (!type) {
        this.pageInfo.current = 1
        this.$refs.pageRef.jumpNum = 1
        this.$refs.pageRef.currentPage = 1
      }

      const params = {
        rfxId: this.modalData.rfxId,
        priceClassify: 2, // 1：内部价格，2：外部价格
        page: this.pageInfo,
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.costFactorLinkagePricing
        .querySupplierFactorPriceList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []

        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
        this.handleClearAll()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 5px 0 0 0;
  height: 100%;
  width: 100%;
  .collapse-search-area {
    padding: 0;
  }
  .mt-pagertemplate {
    margin: 10px 0 0 0;
  }
}
</style>
