<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <!-- 自定义查询条件 -->
      <collapse-search
        class="toggle-container"
        :default-expand="false"
        @reset="handleReset"
        @search="handleSearch"
      >
        <mt-form ref="searchFormRef" :model="searchFormModel">
          <mt-form-item prop="materialCode" :label="$t('物料编码')" label-style="top">
            <mt-input
              v-model="searchFormModel.materialCode"
              :show-clear-button="true"
              :placeholder="$t('请输入物料编码')"
            />
          </mt-form-item>
          <mt-form-item prop="materialName" :label="$t('物料名称')" label-style="top">
            <mt-input
              v-model="searchFormModel.materialName"
              :show-clear-button="true"
              :placeholder="$t('请输入物料名称')"
            />
          </mt-form-item>
          <mt-form-item prop="factoryCode" :label="$t('工厂')" label-style="top">
            <mt-select
              v-model="searchFormModel.factoryCode"
              :data-source="factoryList"
              :fields="{ text: 'text', value: 'orgCode' }"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择工厂')"
            />
          </mt-form-item>
          <mt-form-item prop="supplierCode" :label="$t('供应商')" label-style="top">
            <magnifier-input
              ref="supplier"
              :config="supplierDialogCofig"
              @change="(e) => (searchFormModel.supplierCode = e.supplierCode || null)"
            />
          </mt-form-item>
        </mt-form>
      </collapse-search>
      <!-- 表格 -->
      <sc-table
        ref="sctableRef"
        :loading="loading"
        :checkbox-config="checkboxConfig"
        :is-show-refresh-bth="true"
        :columns="columns"
        :table-data="tableData"
        :cell-style="handleCellStyle"
        @refresh="handleSearch"
        @checkbox-change="handleSelectChange"
        @checkbox-all="handleSelectChange"
      />
      <!-- 分页 -->
      <mt-page
        ref="pageRef"
        class="flex-keep custom-page"
        :page-settings="pageSettings"
        :total-pages="pageSettings.totalPages"
        :selected="selectedRecords.length"
        :show-selected="true"
        @currentChange="handleCurrentChange"
        @sizeChange="handleSizeChange"
        @clearAll="handleClearAll"
      />
    </div>
  </mt-dialog>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import magnifierInput from '@/components/magnifierInput'
import SplitCell from '@/components/VxeComponents/SplitCell/index.vue'
import mixin from './config/mixin'

export default {
  components: { ScTable, CollapseSearch, magnifierInput },
  mixins: [mixin],
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      type: 'material',
      // 不合并的单元格：历史单价含税/未税、单价含税/未税、历史降幅、阶梯数量
      unmergeList: [
        'historyUntaxedUnitPrice',
        'historyTaxedUnitPrice',
        'unitPriceUntaxed',
        'unitPriceTaxed',
        'stepValue',
        'priceRecordCode'
      ]
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    columns() {
      const defaultColumns = [
        {
          width: 50,
          type: 'checkbox',
          fixed: 'left'
        },
        {
          field: 'materialCode',
          title: this.$t('物料编码'),
          fixed: 'left',
          minWidth: 120
        },
        {
          field: 'materialName',
          title: this.$t('物料名称名称'),
          minWidth: 150
        },
        {
          field: 'historyUntaxedUnitPrice',
          title: this.$t('历史价格（未税）'),
          minWidth: 140,
          slots: {
            default: ({ row }) => {
              return [<SplitCell list={row.historyUntaxedUnitPriceList} />]
            }
          }
        },
        {
          field: 'historyTaxedUnitPrice',
          title: this.$t('历史价格（含税）'),
          minWidth: 140,
          slots: {
            default: ({ row }) => {
              return [<SplitCell list={row.historyTaxedUnitPriceList} />]
            }
          }
        },
        {
          field: 'unitPriceUntaxed',
          title: this.$t('单价（未税）'),
          slots: {
            default: ({ row }) => {
              return [<SplitCell list={row.unitPriceUntaxedList} />]
            }
          }
        },
        {
          field: 'unitPriceTaxed',
          title: this.$t('单价（含税）'),
          slots: {
            default: ({ row }) => {
              return [<SplitCell list={row.unitPriceTaxedList} />]
            }
          }
        },
        {
          field: 'factoryCode',
          title: this.$t('工厂编码')
        },
        {
          field: 'factoryName',
          title: this.$t('工厂名称'),
          minWidth: 150
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商编码')
        },
        {
          field: 'supplierName',
          title: this.$t('供应商名称'),
          minWidth: 150
        },
        {
          field: 'stageType',
          title: this.$t('阶梯类型'),
          slots: {
            default: ({ row }) => {
              const stageTypeMap = {
                '-1': this.$t('无阶梯'),
                0: this.$t('按数量'),
                1: this.$t('按时间'),
                2: this.$t('按金额'),
                3: this.$t('数量逐层')
              }
              return [<span>{stageTypeMap[row.stageType]}</span>]
            }
          }
        },
        {
          field: 'stepValue',
          title: this.$t('阶梯数量'),
          slots: {
            default: ({ row }) => {
              return [<SplitCell list={row.stepValueList} />]
            }
          }
        },
        {
          field: 'directDeliverAddr',
          title: this.$t('直送地')
        },
        {
          field: 'costFactorCode',
          title: this.$t('成本因子编码'),
          minWidth: 160,
          slots: {
            default: ({ row }) => {
              const arr = []
              row.costFactorList?.forEach((item) => {
                arr.push(item.costFactorCode)
              })
              return [<span>{arr.join(',')}</span>]
            }
          }
        },
        {
          field: 'costFactorName',
          title: this.$t('成本因子名称'),
          minWidth: 160,
          slots: {
            default: ({ row }) => {
              const arr = []
              row.costFactorList?.forEach((item) => {
                arr.push(item.costFactorName)
              })
              return [<span>{arr.join(',')}</span>]
            }
          }
        }
      ]
      return defaultColumns
    }
  },
  mounted() {},
  methods: {
    // 查询
    async handleSearch(type) {
      // 解决修改查询条件后，查询结果有数据但未正常显示的问题（type: pageChange/refresh）
      if (!type) {
        this.pageInfo.current = 1
        this.$refs.pageRef.jumpNum = 1
        this.$refs.pageRef.currentPage = 1
      }

      const params = {
        rfxId: this.modalData.rfxId,
        page: this.pageInfo,
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.costFactorLinkagePricing
        .querySupplierMaterialPriceList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        // this.tableData = res.data?.records || []
        this.tableData = this.groupData(res.data?.records || [])

        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)

        this.handleClearAll()
      }
    },
    // 对数据进行分组
    groupData(list) {
      const groupKeyList = []
      const tempList = []
      const resList = []
      list.forEach((item) => {
        const { priceGroupCode, id } = item
        const groupKey = priceGroupCode || id
        if (!groupKeyList.includes(groupKey)) {
          groupKeyList.push(groupKey)
          tempList.push([])
        }
        const i = groupKeyList.indexOf(groupKey)
        tempList[i].push(item)
      })
      tempList.forEach((item) => {
        const obj = {}
        item.forEach((t) => {
          this.unmergeList.forEach((key) => {
            if (!obj[key + 'List']) {
              obj[key + 'List'] = []
            }
            let value = t[key]
            // 历史降幅，保留两位小数
            if (key === 'declinePercent') {
              value = t[key] || t[key] === 0 ? Number(t[key]).toFixed(2) : null
            }
            obj[key + 'List'].push(value)
          })
        })
        resList.push({ ...item[0], ...obj, rowSpan: item.length })
      })
      return resList
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 5px 0 0 0;
  height: 100%;
  width: 100%;
  .collapse-search-area {
    padding: 0;
  }
  .mt-pagertemplate {
    margin: 10px 0 0 0;
  }
}
::v-deep {
  .vxe-table--render-default .vxe-body--column.col--ellipsis:not(.col--actived) > .vxe-cell {
    max-height: unset !important;
    padding: 0;
  }
}
</style>
