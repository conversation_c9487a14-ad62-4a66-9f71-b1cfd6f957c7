import { i18n } from '@/main.js'
import Vue from 'vue'
export const toolbar = []
export const dialogcolumnData = [
  {
    field: 'areaId',
    headerText: i18n.t('地区编码')
  },
  {
    field: 'areaName',
    headerText: i18n.t('地区名称')
  }
]
export const columnData = [
  {
    field: 'skuId',
    headerText: i18n.t('SKU编码')
  },
  {
    field: 'skuName',
    headerText: i18n.t('SKU名称')
  },
  {
    field: 'priceType',
    headerText: i18n.t('价格类型'),
    valueConverter: {
      // 价格类型，1-统一价格，2-按区域定价，3-按采购组织价
      type: 'map',
      map: {
        1: i18n.t('统一价格'),
        2: i18n.t('按区域定价'),
        3: i18n.t('按采购组织价')
      }
    }
  },
  {
    field: 'areaName',
    headerText: i18n.t('地区名称'),
    template: () => {
      return {
        template: Vue.component('areaName', {
          template: `
          <div >
              <div @click='click' style="color:#005ca9; cursor: pointer;" v-if="data.priceType == 2 ">{{ $t("地区列表") }}</div>
          </div>
                     `,
          data: function () {
            return {
              data: {}
            }
          },
          methods: {
            click() {
              if (this.data.priceType == 2) {
                this.$parent.$emit('handleClickCellTitle', {
                  field: 'areaName',
                  data: { ...this.data }
                })
              }
            }
          }
        })
      }
    }
  },
  {
    field: 'price',
    headerText: i18n.t('售价')
  },
  {
    field: 'marketPrice',
    headerText: i18n.t('市场价')
  },
  {
    field: 'itemName',
    headerText: i18n.t('SPU名称')
  },
  {
    field: 'itemId',
    headerText: i18n.t('SPU编码')
  },
  {
    field: 'mallId',
    headerText: i18n.t('商城ID')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    type: 'datetime',
    format: 'yyyy-MM-dd HH:MM:ss'
  }
]
