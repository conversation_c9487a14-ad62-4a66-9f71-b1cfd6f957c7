<template>
  <div class="full-height">
    <!-- <input type="file" accept=".xlsx" hidden ref="excel-uploader" /> -->
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :padding-top="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
    <!-- 弹窗 -->
    <mt-dialog
      ref="dialog"
      css-class="create-proj-dialog"
      :buttons="buttons"
      :style="{ width: '940px' }"
      :header="title"
      @beforeClose="cancel"
    >
      <mt-template-page ref="table" :template-config="dialogPageConfig" />
    </mt-dialog>
  </div>
</template>

<script>
import { toolbar, dialogcolumnData, columnData } from './config'
export default {
  data() {
    return {
      // show:false,
      title: this.$t('地区'),
      buttons: [
        {
          click: this.cancels,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      dialogPageConfig: [
        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          toolbar,
          grid: {
            allowFiltering: true,
            columnData: dialogcolumnData,
            dataSource: []
          }
        }
      ],
      id: null,
      pageConfig: [
        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          toolbar,
          gridId: this.$permission.gridId['purchase']['skuPrice'],
          grid: {
            allowFiltering: true,
            columnData,
            asyncConfig: {
              url: this.$API.priceService.getSkuPriceRecords
            }
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    // 弹窗确定的按钮
    confirm() {
      this.cancels()
    },
    // 弹窗中取消的按钮
    cancels() {
      this.$refs.dialog.ejsRef.hide()
    },
    cancel() {
      this.$emit('cancel-function')
      // this.show=false
    },
    //表格按钮-点击事件
    handleClickToolBar() {},
    //单元格icons点击事件
    handleClickCellTool() {},
    //单元格标题点击操作
    handleClickCellTitle(e) {
      // console.log('e',e.data.region)
      // console.log(this);
      this.id = e.data.id
      this.dialogPageConfig[0].grid.dataSource = e.data.region
      if (e.field == 'areaId') {
        // this.show=true
        this.title = this.$t('地区编码')
        this.$refs.dialog.ejsRef.show()
      } else if (e.field == 'areaName') {
        // this.show=true
        this.title = this.$t('地区名称')
        this.$refs.dialog.ejsRef.show()
      }
    }
  }
}
</script>
