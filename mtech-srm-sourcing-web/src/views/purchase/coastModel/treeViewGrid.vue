<template>
  <div class="treeViewGrid">
    <div class="leftBox">
      <div class="leftTitle">
        <mt-button
          css-class="e-flat addBtn"
          icon-css="mt-icons mt-icon-M_Saveas"
          :content="$t('新增一级')"
          @click="addNodes"
        ></mt-button>
      </div>
      <mt-tree-view
        ref="treeview"
        :allow-editing="true"
        :fields="filedsIcon"
        :allow-drag-and-drop="true"
        :node-template="templateData"
      ></mt-tree-view>
    </div>
    <div class="rightBox">
      <div class="rightMain"></div>
    </div>
  </div>
</template>
<script>
import Vue from 'vue'
export default {
  data() {
    return {
      templateData: () => {
        let _this = this
        return {
          template: Vue.component('EmpTemplate', {
            data() {
              return {
                data: {}
              }
            },
            methods: {
              //   getData() {
              //     if (this.data.isUnDrag) {
              //     } else {
              //       _this.$refs.cmEditor.setLine(this.data.name);
              //     }
              //   },
              add() {
                _this.$refs.treeview.ejsRef.addNodes([this.data.nodeId])
              },
              del() {
                _this.$refs.treeview.ejsRef.removeNodes([this.data.nodeId])
              }
            },
            template: `<div>
                            <div style="display: flex;justify-content: space-between;flex-flow: row nowrap;">
                                <div style="width: 240px;overflow: hidden;text-overflow:ellipsis" class="fDiv">{{data.nodeText}}</div>
                                <div>
                                    <mt-button
                                    css-class="e-flat delBtn"
                                    icon-css="mt-icons mt-icon-icon_solid_add"
                                    content=""
                                    @click="add"
                                    ></mt-button>
                                    <mt-button
                                    css-class="e-flat delBtn"
                                    icon-css="mt-icons mt-icon-icon_solid_close"
                                    content=""
                                    @click="del"
                                    ></mt-button>
                                </div>
                            </div>
                        </div>`
          })
        }
      },
      filedsIcon: {
        // id: "id",
        // text: "name",
        // child: "children",
        id: 'nodeId',
        text: 'nodeText',
        child: 'nodeChild',
        dataSource: [
          {
            nodeId: '04',
            nodeText: 'Pictures',
            expanded: true,
            nodeChild: [
              {
                nodeId: '04-01',
                nodeText: 'Camera Roll',
                expanded: true,
                nodeChild: [
                  {
                    nodeId: '04-01-01',
                    nodeText: 'WIN_20160726_094117.JPG'
                  },
                  {
                    nodeId: '04-01-02',
                    nodeText: 'WIN_20160726_094118.JPG'
                  }
                ]
              },
              { nodeId: '04-02', nodeText: 'Wind.jpg' },
              { nodeId: '04-03', nodeText: 'Stone.jpg' }
            ]
          },
          {
            nodeId: '05',
            nodeText: 'Downloads',
            nodeChild: [
              { nodeId: '05-01', nodeText: 'UI-Guide.pdf' },
              { nodeId: '05-02', nodeText: 'Tutorials.zip' },
              { nodeId: '05-03', nodeText: 'Game.exe' },
              { nodeId: '05-04', nodeText: 'TypeScript.7z' }
            ]
          }
        ]
      }
    }
  },
  methods: {
    addNodes() {
      this.$refs.treeview.ejsRef.addNodes('04')
    }
  }
}
</script>
<style lang="scss">
.treeViewGrid {
  display: flex;
  .e-treeview .e-list-item .e-ul {
    // margin: 0;
    // padding: 0;
    // .fDiv {
    //   padding-left: 20px;
    // }
  }
  .addBtn {
    font-size: 14px;
  }
  .delBtn {
    .mt-icons {
      font-size: 16px;
      font-weight: 600;
    }
    .mt-icon-icon_solid_close {
      color: #ff0020;
    }
    .mt-icon-icon_solid_add {
      color: #6386c1;
    }
  }
  .e-fullrow {
    // width: 399px;
  }
  .e-treeview .e-list-item.e-active > .e-fullrow {
    font-size: 14px;
    background-color: rgba(250, 250, 250, 1);
  }
  .e-treeview .e-list-text {
    width: 100%;
  }
  .leftTitle {
    // width: 400px;
    border-bottom: 1px solid rgba(232, 232, 232, 1);
    height: 50px;
    padding-left: 42px;
    background: linear-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)),
      linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 1));
    display: flex;
    align-items: center;
  }
  .leftBox {
    width: 400px;
    background: #ffffff;
    height: 100vh;
    border: 1px solid rgba(232, 232, 232, 1);
    overflow: auto;
    .mt-tree-view {
      width: 400px;
    }
  }
  .rightBox {
    flex: 1;
    padding: 11px;
    // background: #ffffff;
    height: 100vh;
    // width: 100%;
    .rightMain {
      height: 100%;
      background: #ffffff;
      border: 1px solid rgba(232, 232, 232, 1);
      border-radius: 4px;
    }
  }
}
</style>
