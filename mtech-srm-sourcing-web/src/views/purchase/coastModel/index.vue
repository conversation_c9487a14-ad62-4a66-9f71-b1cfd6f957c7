<template>
  <div id="full-height">
    <mt-template-page ref="templateRef" :template-config="pageConfig">
      <tree-view-grid slot="slot-1" />
    </mt-template-page>
  </div>
</template>

<script>
import TreeViewGrid from './treeViewGrid'
import { columnData, getGridData } from './config'

export default {
  components: { TreeViewGrid },
  data() {
    return {
      pageConfig: [
        {
          title: this.$t('关联物料'),
          toolbar: [],
          grid: {
            allowFiltering: true,
            allowPaging: false,
            columnData,
            dataSource: getGridData(15)
          }
        },
        {
          title: this.$t('成本项')
        }
      ]
    }
  },
  methods: {}
}
</script>
