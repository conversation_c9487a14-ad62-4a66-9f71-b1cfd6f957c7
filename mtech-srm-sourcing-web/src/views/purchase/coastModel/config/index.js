import { i18n } from '@/main.js'
export const columnData = [
  {
    field: 'OrderID',
    headerText: i18n.t('关联方式'),

    cellTools: [
      // "edit",
      // "publish",
      // "share",
      {
        id: 'back',
        // icon: "Pdf_Export",
        title: i18n.t('品类类型'),
        visibleCondition: (data) => {
          return data['Freight'] > 5000
        }
      },
      {
        id: 'back',
        // icon: "MT_Daterange",
        title: i18n.t('返回'),
        visibleCondition: (data) => {
          return data['Freight'] < 5000
        }
      }
    ]
  },
  {
    field: 'CustomerName',
    headerText: i18n.t('品类类型')
  },
  {
    field: 'OrderDate',
    headerText: i18n.t('品类')
  },
  {
    field: 'Check1',
    headerText: '零件/品项编码'
  },
  {
    field: 'Check1',
    headerText: '零件/品项名称'
  },
  {
    field: 'Check1',
    headerText: '工厂/地点'
  },
  {
    field: 'ShipName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'OrderDate',
    headerText: i18n.t('创建时间')
  },
  {
    field: 'ShipAddress',
    headerText: i18n.t('备注')
  }
]
export const getGridData = (len = 10) => {
  let res = []
  for (let i = 0; i < len; i++) {
    let _ram = parseInt(Math.random(100000) * 10000)
    res.push({
      OrderID: 'SK1244' + _ram,
      CustomerID: 'k' + i,
      CustomerName: i18n.t('数据') + i,
      OrderDate: '1996-07-04',
      ShippedDate: new Date(),
      Freight: _ram,
      ShipName: '',
      Check1: true,
      // Check2: false,
      // Check3: false,
      ShipAddress: parseInt(_ram % 3)
    })
  }
  return res
}
