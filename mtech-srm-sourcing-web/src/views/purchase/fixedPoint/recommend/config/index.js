import { i18n } from '@/main.js'
const todoListToolBar = [
  { id: 'backPrice', icon: 'icon_solid_edit', title: i18n.t('退回比价') },
  { id: 'createPoint', icon: 'icon_solid_edit', title: i18n.t('创建定点单') }
  // { id: "pointPropsol", icon: "icon_solid_edit", title: i18n.t("定点合并建议") },
]

const completedListToolBar = []

const todoListColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
  },
  {
    field: 'rfxCode',
    headerText: i18n.t('RFX编号')
  },
  {
    field: 'skuCode',
    headerText: i18n.t('SKU编号')
  },
  {
    field: 'skuName',
    headerText: i18n.t('SKU名称')
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料品项编号')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料品项名称')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编号')
  },
  {
    field: 'itemName',
    headerText: i18n.t('品项名称')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织')
  },
  {
    field: 'purGroupName',
    headerText: i18n.t('采购组名称')
  },
  {
    field: 'purExecutorName',
    headerText: i18n.t('采购执行人')
  },
  {
    field: 'siteCode',
    headerText: '工厂/地点编号'
  },
  {
    field: 'siteName',
    headerText: '工厂/地点名称'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'bidQuantity',
    headerText: i18n.t('数量')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('单位')
  },
  {
    field: 'taxRate',
    headerText: i18n.t('税率')
  },
  {
    field: 'currencyName',
    headerText: i18n.t('货币')
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('未税单价')
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('含税单价')
  },
  {
    field: 'allocationQuantity',
    headerText: i18n.t('分配数量')
  },
  {
    field: 'allocationRatio',
    headerText: i18n.t('配额')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    field: 'createUser',
    headerText: i18n.t('创建人')
  }
]

const completedListColumnData = [
  // {
  //   width: "50",
  //   type: "checkbox",
  // },
  {
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
  },
  {
    field: 'rfxCode',
    headerText: i18n.t('RFX编号')
  },
  {
    field: 'skuCode',
    headerText: i18n.t('SKU编号')
  },
  {
    field: 'skuName',
    headerText: i18n.t('SKU名称')
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料品项编号')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料品项名称')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编号')
  },
  {
    field: 'itemName',
    headerText: i18n.t('品项名称')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织')
  },
  {
    field: 'purGroupName',
    headerText: i18n.t('采购组名称')
  },
  {
    field: 'purExecutorName',
    headerText: i18n.t('采购执行人')
  },
  {
    field: 'siteCode',
    headerText: '工厂/地点编号'
  },
  {
    field: 'siteName',
    headerText: '工厂/地点名称'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'bidQuantity',
    headerText: i18n.t('数量')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('单位')
  },
  {
    field: 'taxRate',
    headerText: i18n.t('税率')
  },
  {
    field: 'currencyName',
    headerText: i18n.t('货币')
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('未税单价')
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('含税单价')
  },
  {
    field: 'allocationQuantity',
    headerText: i18n.t('分配数量')
  },
  {
    field: 'allocationRatio',
    headerText: i18n.t('配额')
  },
  {
    field: 'createTime',
    headerText: '创建时间？'
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'applyDeptName',
    headerText: i18n.t('申请部门')
  },
  {
    field: 'applyTime',
    headerText: i18n.t('申请时间')
  },
  {
    field: 'applyUserName',
    headerText: i18n.t('申请人')
  }
]

//status  定点推荐主表状态 0待处理 3已处理 4已退回
export const pageConfig = (url) => [
  {
    title: i18n.t('待处理'),
    toolbar: todoListToolBar,
    grid: {
      allowFiltering: true,
      frozenColumns: 2,
      columnData: todoListColumnData,
      asyncConfig: {
        url,
        defaultRules: [
          {
            label: i18n.t('状态'),
            field: 'status',
            type: 'number',
            operator: 'equal',
            value: '0'
          }
        ]
      }
    }
  },
  {
    title: i18n.t('已处理'),
    toolbar: completedListToolBar,
    grid: {
      allowFiltering: true,
      columnData: completedListColumnData,
      asyncConfig: {
        url,
        defaultRules: [
          {
            label: i18n.t('状态'),
            field: 'status',
            type: 'number',
            operator: 'equal',
            value: '3'
          }
        ]
      }
    }
  }
]
