<template>
  <mt-dialog
    ref="dialog"
    css-class="small-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="dialogObject" :rules="formRules">
        <mt-form-item class="process-desc" prop="reason" label="$t('退回比价原因：')">
          <mt-input
            :multiline="true"
            :rows="3"
            type="text"
            float-label-type="Never"
            v-model="dialogObject.reason"
            :placeholder="$t('请输入退回比价原因')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      dialogObject: {
        reason: '' //	理由
      },
      formRules: {}
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    // this.getFormValidRules(
    //   "formRules",
    //   this.$API.fixedPoint.doBackPointRecommendValid
    // );
  },
  methods: {
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          this.$emit('confirm-function', this.dialogObject)
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
