<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'

export default {
  data() {
    return {
      pageConfig: pageConfig(this.$API.fixedPoint.getPointRecommendList)
    }
  },
  mounted() {},
  methods: {
    //表格按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.grid.getSelectedRecords()
      console.log('use-handleClickToolBar', e, _selectGridRecords)
      if (_selectGridRecords.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'createPoint') {
        //创建定点单
        this.doCreatePointRecommend(_selectGridRecords)
        // localStorage.PointSetailPageStatus = "NEW";
        // this.$router.push({ name: `fixed-point-detail` });
      } else if (e.toolbar.id == 'backPrice') {
        //退回比价
        this.doBackPointRecommend(_selectGridRecords)
      }
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
    },
    //单元格标题点击操作
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
    },
    //创建定点单
    doCreatePointRecommend(_records) {
      let _query = []
      _records.forEach((e) => {
        _query.push(e.id)
      })
      this.$API.fixedPoint.addPointDetail(_query).then((res) => {
        localStorage.createPointListRecords = JSON.stringify({
          ...res.data,
          recommandList: _query
        })
        this.$router.push({ name: `fixed-point-detail` })
      })
    },
    //退回比价
    doBackPointRecommend(_records) {
      this.$dialog({
        // modal: () =>
        //   import(
        //     /* webpackChunkName: "router/purchase/fixedPoint/recommend/components/backPointRecommend" */ "./components/backPointRecommend.vue"
        //   ),
        data: {
          title: this.$t('提示'),
          message: this.$t("确认执行'退回比价'？")
        },
        success: () => {
          let _query = []
          _records.forEach((e) => {
            _query.push({
              id: e.id,
              backReason: ''
              // backReason: data?.reason,
            })
          })
          this.$API.fixedPoint.doBackPointRecommend(_query).then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    }
  }
}
</script>
