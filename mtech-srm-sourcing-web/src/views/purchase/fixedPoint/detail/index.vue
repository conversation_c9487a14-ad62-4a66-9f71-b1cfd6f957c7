<template>
  <div class="full-height fixed-point-list mt-flex-direction-column">
    <div class="point-header mt-flex-direction-column">
      <div class="header-options mt-flex">
        <div class="point-info">
          <span class="company-name">{{ pointListRecords.companyName || $t('公司名称') }}</span>
          <span class="busuiness-type">{{ pointListRecords.businessTypeName }}</span>
        </div>
        <div class="btns-wrap invite-btn">
          <mt-button
            @click="saveStrategyConfig"
            icon-css="mt-icons mt-icon-icon_solid_finish"
            class="submit-btn"
            >{{ $t('提交') }}</mt-button
          >
          <mt-button
            @click="backToStrategyMapsPage"
            icon-css="mt-icons mt-icon-icon_Close_2"
            class="close-btn"
            >{{ $t('取消') }}</mt-button
          >
        </div>
      </div>
      <div class="header-forms">
        <mt-form :rules="formRules">
          <!-- <mt-form-item :label="$t('采购组')">
            <mt-select
              ref="purGroupIdRef"
              css-class="rule-element"
              float-label-type="Never"
              :allow-filtering="true"
              :data-source="purchaseOrganizList"
              v-model="submitObject.purGroupId"
              :fields="{ text: 'organizationName', value: 'id' }"
              :placeholder="$t('选择采购组')"
            ></mt-select> </mt-form-item> -->
          <mt-form-item :label="$t('申请部门')">
            <mt-select
              ref="applyDeptIdRef"
              css-class="rule-element"
              float-label-type="Never"
              :allow-filtering="true"
              v-model="submitObject.applyDeptId"
              :data-source="departmentList"
              :fields="{ text: 'departmentName', value: 'id' }"
              :placeholder="$t('选择申请部门')"
            ></mt-select> </mt-form-item
          ><mt-form-item :label="$t('申请人')">
            <mt-select
              ref="applyUserIdRef"
              css-class="rule-element"
              float-label-type="Never"
              :allow-filtering="true"
              v-model="submitObject.applyUserId"
              :data-source="tenantIdList"
              :placeholder="$t('选择申请人')"
              :fields="{ text: 'employeeName', value: 'id' }"
            ></mt-select> </mt-form-item
          ><mt-form-item :label="$t('推荐理由')">
            <mt-input
              css-class="rule-element"
              :show-clear-button="false"
              float-label-type="Never"
              :placeholder="$t('字数不超过200字')"
              v-model="submitObject.recommendReason"
            ></mt-input> </mt-form-item
        ></mt-form>
      </div>
    </div>
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
      <filesToolBar slot="slot-2"></filesToolBar>
    </mt-template-page>
  </div>
</template>

<script>
import { resultToolBar, supplierToolBar } from './config'

export default {
  components: {
    //相关文件
    filesToolBar: () => import('./components/filesToolBar')
  },
  data() {
    return {
      submitObject: {
        applyDeptId: null,
        applyUserId: null,
        // purGroupId: null,
        recommendReason: null
      },
      configInfo: {
        companyName: this.$t('公司名称'),
        businessType: this.$t('一般采购'),
        strategyName: '',
        strategyDesc: '',
        createUserName: '',
        createTime: '',
        strategyCode: ''
      },
      pageConfig: [
        {
          title: this.$t('定点明细'),
          toolbar: resultToolBar,
          grid: {
            allowFiltering: true,
            allowPaging: false,
            columnData: [],
            dataSource: []
          }
        },
        {
          title: this.$t('候选供方'),
          toolbar: supplierToolBar,
          grid: {
            allowFiltering: true,
            allowPaging: false,
            allowEditting: true,
            editSettings: {
              allowEditing: true,
              mode: 'Batch'
            },
            cellSaved: this.handleEditSupplierFields,
            columnData: [],
            dataSource: []
          }
        },
        {
          title: this.$t('相关文件')

          // toolbar: filesToolBar,
          // grid: { allowFiltering: true,
          //   allowPaging: false,
          //   columnData: [],
          //   dataSource: [],
          // },
        }
      ],
      editFields: [
        'contactUser', // 联系人
        'contactUserPhone', //  联系电话
        'contactUserEmail', //  电子邮箱
        'contactUserRemarks' //  备注
      ],
      formRules: {},
      // purchaseOrganizList: [], // 采购组织
      departmentList: [], //申请部门列表
      tenantIdList: [], //申请人列表
      pointListRecords: {}, //当前组建定点单，原数据
      fixedPointItemDTOList: [], //定点明细数据
      fixedPointSupplierDTOList: [], //候选供方 数据
      moduleItems: []
    }
  },
  mounted() {
    this.getPointDetailModuleData()
    this.getMainData()
    this.pointListRecords = JSON.parse(localStorage?.createPointListRecords)
    this.fixedPointItemDTOList = this.pointListRecords?.fixedPointItemDTOList
    this.fixedPointSupplierDTOList = this.pointListRecords?.fixedPointSupplierDTOList
    // this.getFormValidRules(
    //   "formRules",
    //   this.$API.fixedPoint.saveFixedPointValid
    // );
  },
  methods: {
    //获取页面模板数据
    getPointDetailModuleData() {
      let _param = {
        fixedPointId: '0',
        businessTypeCode: this.pointListRecords.businessTypeCode,
        docType: 'fixed_point',
        isReturnField: '1'
      }
      this.$API.fixedPoint.getPointModule(_param).then((res) => {
        this.moduleItems = res?.data?.moduleItems
        if (Array.isArray(this.moduleItems) && this.moduleItems.length) {
          this.moduleItems.forEach((e, i) => {
            let _columns = []
            let _fieldDefines = e?.fieldDefines
            for (let i in _fieldDefines) {
              let _col = {
                field: _fieldDefines[i]['fieldCode'],
                headerText: _fieldDefines[i]['fieldName'],
                required: _fieldDefines[i]['required']
              }
              _col.allowEditing = this.editFields.indexOf(_col.field) > -1
              _columns.push(_col)
            }
            this.pageConfig[i]['grid']['columnData'] = []
            this.pageConfig[i]['grid']['dataSource'] = []
            this.$nextTick(() => {
              let _dataSource = []
              if (i === 0) {
                _dataSource = this.fixedPointItemDTOList
              } else if (i === 1) {
                _dataSource = this.fixedPointSupplierDTOList
              }
              this.pageConfig[i]['grid']['columnData'] = _columns
              this.pageConfig[i]['grid']['dataSource'] = _dataSource
              this.pageConfig[i]['title'] = e.moduleName
            })
          })
        }
      })
    },
    handleEditSupplierFields(e) {
      let { columnObject, rowData, value } = e
      this.fixedPointSupplierDTOList.forEach((e) => {
        if (e.id === rowData.id) {
          e[columnObject.field] = value
        }
      })
    },
    //表格按钮-点击事件
    handleClickToolBar(e) {
      console.log('use-handleClickToolBar', e)
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
    },
    //单元格标题点击操作
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
    },
    saveStrategyConfig() {
      // let _records = [];
      // this.fixedPointItemDTOList.forEach((e) => {
      //   _records.push(e.id);
      // });
      let params = {
        applyDeptCode: null,
        applyDeptId: this.submitObject.applyDeptId,
        applyDeptName: null,
        applyUserId: this.submitObject.applyUserId,
        applyUserName: null,
        businessTypeCode: this.pointListRecords.businessTypeCode,
        businessTypeId: this.pointListRecords.businessTypeId,
        businessTypeName: this.pointListRecords.businessTypeName,
        fixedPointRecommendIdList: this.pointListRecords.recommandList,
        fixedPointSupplierDTOList: this.fixedPointSupplierDTOList,
        // purGroupCode: null,
        // purGroupId: this.submitObject.purGroupId,
        // purGroupName: null,
        recommendReason: this.submitObject.recommendReason
      }
      //处理下拉框数据赋值
      this.$utils.assignDataFromRefs(params, [
        {
          key: 'applyUserId', //申请人applyUserId下拉框数据
          ref: this.$refs.applyUserIdRef.ejsRef,
          fields: {
            applyUserName: 'employeeName'
          }
        },
        {
          key: 'applyDeptId', //申请部门applyDeptId下拉框数据
          ref: this.$refs.applyDeptIdRef.ejsRef,
          fields: {
            applyDeptCode: 'departmentCode',
            applyDeptName: 'departmentName'
          }
        }
        // {
        //   key: "purGroupId", //采购组purGroupId下拉框数据
        //   ref: this.$refs.purGroupIdRef.ejsRef,
        //   fields: {
        //     purGroupCode: "organizationCode",
        //     purGroupName: "organizationName",
        //   },
        // },
      ])
      this.$API.fixedPoint.saveFixedPoint(params).then(() => {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
      })
    },
    backToStrategyMapsPage() {
      this.$router.go(-1)
    },
    getMainData() {
      // 采购组织
      // this.$API.masterData
      //   .purchaseOraginaze({
      //     organizationTypeCode: "BUORG002ADM",
      //   })
      //   .then((res) => {
      //     this.purchaseOrganizList = res.data;
      //   });

      // 人员列表
      this.$API.masterData.getUserListByTenantId({ employeeName: '' }).then((res) => {
        this.tenantIdList = res.data
      })

      // 申请部门
      this.$API.masterData
        .getDepartmentList({
          departmentName: ''
        })
        .then((res) => {
          this.departmentList = res.data
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.fixed-point-list {
  padding-top: 20px;
  width: 100%;
  .text-ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .point-header {
    padding: 20px;
    width: 100%;
    min-width: 1200px;
    height: 120px;
    justify-content: space-between;
    background: linear-gradient(rgba(99, 134, 193, 0.06), rgba(99, 134, 193, 0.06)),
      linear-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 1));
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 0 8px 0 0;
    .header-options {
      justify-content: space-between;
      .point-info {
        .company-name {
          font-size: 20px;
          font-family: PingFangSC;
          font-weight: 600;
          color: rgba(41, 41, 41, 1);
        }
        .busuiness-type {
          font-size: 12px;
          font-family: PingFangSC;
          font-weight: 500;
          padding: 4px;
          border-radius: 2px;
          margin-left: 10px;
          color: rgba(237, 161, 51, 1);
          background: rgba(237, 161, 51, 0.1);
        }
      }
      .btns-wrap {
        /deep/ .mt-button {
          margin-right: 20px;
          button {
            background: transparent;
            border: none;
            width: auto;
            height: 34px;
            // background: rgba(255, 255, 255, 1);
            // border: 1px solid rgba(0, 70, 156, 0.1);
            border-radius: 4px;
            box-shadow: unset;
            padding: 0;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: 500;
            color: #9daabf;
          }
          &:last-of-type {
            margin-right: 0;
          }
          &.submit-btn {
            button {
              color: #6386c1;
            }
          }
        }
      }
    }
    .header-forms {
      .mt-form {
        display: flex;
        .mt-form-item {
          margin-bottom: 0;
          margin: 0 10px;
          width: 300px;
          flex-shrink: 0;

          &:last-of-type {
            margin-right: 0;
            flex: 1;
          }
          &:first-of-type {
            margin-left: 0;
          }
          /deep/ .rule-element {
            padding: 0;
          }
        }
      }
    }
    .header-labels {
      .label-item {
        margin-left: 30px;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(157, 170, 191, 1);
        display: inline-block;
        &:first-of-type {
          margin-left: 0;
        }
      }
    }
  }

  /deep/ .mt-data-grid {
    .e-grid {
      th.e-headercell,
      td.e-rowcell {
        width: 150px;
      }
    }
  }
}
</style>
