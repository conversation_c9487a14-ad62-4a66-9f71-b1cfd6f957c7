import { i18n } from '@/main.js'
import Vue from 'vue'
export const resultToolBar = []

export const supplierToolBar = []

export const filesToolBar = [
  { id: 'Add', icon: 'icon_solid_upload', title: i18n.t('上传') },
  { id: 'pull', icon: 'icon_solid_Download', title: i18n.t('下载') },
  { id: 'delete', icon: 'icon_solid_Delete', title: i18n.t('删除') }
]

export const resultColumnData = [
  // {
  //   width: "50",
  //   type: "checkbox",
  // },
  {
    field: 'processDesc',
    headerText: i18n.t('RFX编号')
  },
  {
    field: 'operationTypeName',
    headerText: i18n.t('SKU编号')
  },
  {
    field: 'enableStatus',
    headerText: i18n.t('SKU名称')
  },
  {
    field: 'createTime',
    headerText: i18n.t('物料品项编号')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('物料品项名称')
  },
  {
    field: 'processName',
    headerText: i18n.t('品类编号')
  },
  {
    field: 'processDesc',
    headerText: i18n.t('品项名称')
  },
  {
    field: 'operationTypeName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'enableStatus',
    headerText: i18n.t('采购组织')
  },
  {
    field: 'createTime',
    headerText: i18n.t('采购组织名称')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('采购执行人')
  },
  {
    field: 'createUserName',
    headerText: '工厂/地点编号'
  },
  {
    field: 'createUserName',
    headerText: '工厂/地点名称'
  },
  {
    field: 'createUserName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('数量')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('单位')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('税率')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('货币')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('未税单价')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('含税单价')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('分配数量')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('配额')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建时间')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  }
]

export const supplierColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'processName',
    headerText: i18n.t('供应商编号')
  },
  {
    field: 'processDesc',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'operationTypeName',
    headerText: i18n.t('评审状态')
  },
  {
    field: 'enableStatus',
    headerText: i18n.t('供应商阶段')
  },
  {
    field: 'createTime',
    headerText: i18n.t('保证金缴纳状态')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('标书购买')
  },
  {
    field: 'processName',
    headerText: i18n.t('联系人')
  },
  {
    field: 'processDesc',
    headerText: i18n.t('联系电话')
  },
  {
    field: 'operationTypeName',
    headerText: i18n.t('电子邮箱')
  },
  {
    field: 'enableStatus',
    headerText: i18n.t('备注')
  }
]

const iconSetting = {
  '.ppt': 'mt-icon-icon_ppt',
  '.docx': 'mt-icon-icon_word',
  '.pdf': 'mt-icon-icon_pdf',
  '.xls': 'mt-icon-icon_excel',
  '.png': 'mt-icon-icon_File1'
}
export const filesColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'fileName',
    cssClass: 'field-content',
    headerText: i18n.t('文件名称'),
    cellTools: [
      { id: 'download', icon: 'icon_solid_Download', title: i18n.t('下载') },
      { id: 'delete', icon: 'icon_solid_Delete', title: i18n.t('删除') }
    ]
  },
  {
    field: 'fileSize',
    headerText: i18n.t('RFX编号')
  },
  {
    field: 'fileSize',
    headerText: i18n.t('文件大小')
  },
  {
    field: 'fileType',
    headerText: i18n.t('文件类型'),
    template: function () {
      return {
        template: Vue.component('fileTypeOption', {
          template: `<div><i :class="['mt-icons', icon]"></i><span style="margin-left: 5px">{{data.fileType}}</span></div>`,
          data() {
            return { data: { data: {} } }
          },
          computed: {
            icon() {
              const { fileType } = this.data
              return fileType ? iconSetting[fileType] : ''
            }
          }
        })
      }
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  }
]
