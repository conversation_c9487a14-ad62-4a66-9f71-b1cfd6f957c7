import { i18n } from '@/main.js'
const todoToolBar = [
  { id: 'createApproval', icon: 'icon_solid_edit', title: i18n.t('提交审批') },
  { id: 'cancelApproval', icon: 'icon_solid_edit', title: i18n.t('取消定点单') }
]

const rejectToolBar = [
  { id: 'closePoint', icon: 'icon_solid_edit', title: i18n.t('关闭') },
  { id: 'reEdit', icon: 'icon_solid_edit', title: i18n.t('重新编辑') },
  { id: 'cancelApproval', icon: 'icon_solid_edit', title: i18n.t('取消定点单') }
]

const todoPointListColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
  },
  {
    field: 'pointCode',
    headerText: i18n.t('定点编号'),
    cssClass: 'field-content'
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织')
  },
  {
    field: 'purGroupName',
    headerText: i18n.t('采购组名称')
  },
  {
    field: 'purExecutorName',
    headerText: i18n.t('采购执行人')
  },
  {
    field: 'recommendReason',
    headerText: i18n.t('推荐理由')
  },
  {
    field: 'createTime',
    headerText: i18n.t('申请时间')
  },
  {
    field: 'applyDeptName',
    headerText: i18n.t('申请部门')
  },
  {
    field: 'applyUserName',
    headerText: i18n.t('申请人')
  }
]
const pointListColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
  },
  {
    field: 'pointCode',
    headerText: i18n.t('定点编号')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织')
  },
  {
    field: 'purGroupName',
    headerText: i18n.t('采购组名称')
  },
  {
    field: 'purExecutorName',
    headerText: i18n.t('采购执行人')
  },
  {
    field: 'recommendReason',
    headerText: i18n.t('推荐理由')
  },
  {
    field: 'createTime',
    headerText: i18n.t('申请时间')
  },
  {
    field: 'applyDeptName',
    headerText: i18n.t('申请部门')
  },
  {
    field: 'applyUserName',
    headerText: i18n.t('申请人')
  }
]

//status  定点管理主表状态 0待处理 1待审批 2已拒绝 3已通过 4已关闭
export const pageConfig = (url) => [
  {
    title: i18n.t('待处理'),
    toolbar: todoToolBar,
    grid: {
      allowFiltering: true,
      frozenColumns: 2,
      columnData: todoPointListColumnData,
      asyncConfig: {
        url,
        defaultRules: [
          {
            label: i18n.t('状态'),
            field: 'status',
            type: 'number',
            operator: 'equal',
            value: '0'
          }
        ]
      }
    }
  },
  {
    title: i18n.t('待审批'),
    toolbar: [],
    grid: {
      allowFiltering: true,
      frozenColumns: 2,
      columnData: pointListColumnData,
      asyncConfig: {
        url,
        defaultRules: [
          {
            label: i18n.t('状态'),
            field: 'status',
            type: 'number',
            operator: 'equal',
            value: '1'
          }
        ]
      }
    }
  },
  {
    title: i18n.t('已拒绝'),
    toolbar: rejectToolBar,
    grid: {
      allowFiltering: true,
      frozenColumns: 2,
      columnData: pointListColumnData,
      asyncConfig: {
        url,
        defaultRules: [
          {
            label: i18n.t('状态'),
            field: 'status',
            type: 'number',
            operator: 'equal',
            value: '2'
          }
        ]
      }
    }
  },
  {
    title: i18n.t('已通过'),
    toolbar: [],
    grid: {
      allowFiltering: true,
      frozenColumns: 2,
      columnData: pointListColumnData,
      asyncConfig: {
        url,
        defaultRules: [
          {
            label: i18n.t('状态'),
            field: 'status',
            type: 'number',
            operator: 'equal',
            value: '3'
          }
        ]
      }
    }
  },
  {
    title: i18n.t('已关闭'),
    toolbar: [],
    grid: {
      allowFiltering: true,
      frozenColumns: 2,
      columnData: pointListColumnData,
      asyncConfig: {
        url,
        defaultRules: [
          {
            label: i18n.t('状态'),
            field: 'status',
            type: 'number',
            operator: 'equal',
            value: '4'
          }
        ]
      }
    }
  }
]
