<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'

export default {
  data() {
    return {
      pageConfig: pageConfig(this.$API.fixedPoint.getPointList)
    }
  },
  mounted() {},
  methods: {
    //表格按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.grid.getSelectedRecords()
      if (_selectGridRecords.length < 1) {
        this.$toast({ content: this.$t('至少选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'closePoint') {
        //关闭定点
        if (_selectGridRecords.length > 1) {
          this.$toast({ content: this.$t('只支持操作一条数据'), type: 'warning' })
        } else {
          this.doClosePoint(_selectGridRecords[0])
        }
      } else if (e.toolbar.id == 'createApproval') {
        //提交审批
        this.doCreateApproval(_selectGridRecords)
      } else if (e.toolbar.id == 'cancelApproval') {
        //取消定点单
        this.cancelFixedPoint(_selectGridRecords)
      }
    },
    //单元格icons点击事件
    // handleClickCellTool(e) {
    // },
    //单元格标题点击操作
    handleClickCellTitle(e) {
      if (e.field === 'pointCode') {
        this.$router.push({
          name: `fixed-point-detail-edit`,
          query: {
            pointId: e.data.id
          }
        })
        // this.$API.fixedPoint
        //   .getPointDetail({ fixedPointId: e.data.id })
        //   .then(() => {
        //     localStorage.createPointListRecords = JSON.stringify(res.data);
        //     this.$router.push({
        //       name: `fixed-point-detail-edit`,
        //       query: {
        //         pointId: e.data.id,
        //       },
        //     });
        //   });
      }
    },
    //提交审批
    doCreateApproval(_records) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t("确认执行'提交审批'操作？")
        },
        success: () => {
          let _query = []
          _records.forEach((e) => {
            _query.push(e.id)
          })
          this.$API.fixedPoint.doSubmitPoint(_query).then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    //取消定点单
    cancelFixedPoint(_records) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t("确认执行'取消定点单'？")
        },
        success: () => {
          let _query = []
          _records.forEach((e) => {
            _query.push(e.id)
          })
          this.$API.fixedPoint.cancelFixedPoint(_query).then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    //关闭定点
    doClosePoint(_point) {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/fixedPoint/list/components/closePoint" */ './components/closePoint.vue'
          ),
        data: {
          title: this.$t('关闭RFX')
        },
        success: (data) => {
          this.$API.fixedPoint
            .doClosePoint({
              fixedPointId: _point.id,
              closeReason: data?.reason
            })
            .then(() => {
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData()
            })
        }
      })
    }
  }
}
</script>
