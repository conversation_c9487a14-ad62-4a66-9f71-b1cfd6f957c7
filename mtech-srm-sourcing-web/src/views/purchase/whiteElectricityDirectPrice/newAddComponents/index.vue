<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="modalData.title"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="appForm" :model="form.data" :rules="form.rules">
        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item prop="title" :label="$t('标题')">
              <mt-input
                type="text"
                v-model="form.data.title"
                :placeholder="$t('请输入标题')"
                maxlength="20"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <!-- <mt-col :span="12">
            <mt-form-item prop="priceObjectName" :label="$t('定价对象')">
              <mt-select
                :value="form.data.priceObjectName"
                :fields="{ text: 'sourcingObj', value: 'templateCode' }"
                :data-source="form.dataSource.pricing"
                :placeholder="$t('请选择定价对象')"
                @input="inputPricing($event, 'priceObjectName')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col> -->
          <mt-col :span="12">
            <mt-form-item prop="sourcingType" :label="$t('定价类型')">
              <mt-select
                :value="form.data.sourcingType"
                :data-source="form.dataSource.sourcingTypeList"
                :placeholder="$t('定价类型')"
                @change="changeSourcingType"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="companyName" :label="$t('公司')">
              <!-- <mt-select
                v-model="form.data.companyName"
                :fields="{
                  text: 'orgName',
                  value: 'orgTypeCode',
                }"
                :data-source="form.dataSource.firm"
                :placeholder="$t('请选择公司')"
                @input="inputPricing($event, 'companyName')"
                :show-clear-button="true"
              ></mt-select> -->
              <mt-select
                v-model="form.data.companyName"
                float-label-type="Never"
                :data-source="form.dataSource.firm"
                :fields="{ text: 'orgName', value: 'orgName' }"
                @change="changeTaxItem"
                :placeholder="$t('请选择公司')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="purOrgName" :label="$t('采购组织')">
              <mt-select
                :value="form.data.purOrgName"
                :fields="{
                  text: 'organizationName',
                  value: 'organizationName'
                }"
                :data-source="form.dataSource.procurement"
                :placeholder="$t('请选择采购组织')"
                @change="changePurOrgName"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="siteName" :label="$t('工厂')">
              <mt-select
                :value="form.data.siteName"
                :data-source="form.dataSource.siteNameList"
                :placeholder="$t('请选择工厂')"
                @input="inputPricing($event, 'siteName')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="sourcingExpand" :label="$t('扩展')">
              <mt-multi-select
                :data-source="form.dataSource.expandSiteList"
                :item-template="selectItemTemplate"
                @change="handleExpand($event)"
                :show-clear-button="false"
                :placeholder="$t('扩展')"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="purId" :label="$t('采购员')">
              <debounce-filter-select
                v-model="form.data.purId"
                :request="getCurrentEmployees"
                :data-source="form.dataSource.personnel"
                @change="inputPricing($event, 'purId')"
                :show-clear-button="false"
                :fields="{ text: 'text', value: 'uid' }"
                :placeholder="$t('请选择采购员')"
              ></debounce-filter-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="decidePriceType" :label="$t('单据类型')">
              <mt-select
                :value="form.data.decidePriceType"
                :data-source="form.dataSource.pricingType"
                :placeholder="$t('请选择单据类型')"
                @input="inputPricing($event, 'decidePriceType')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="priceClassification" :label="$t('价格分类')">
              <mt-select
                :value="form.data.priceClassification"
                :data-source="form.dataSource.priceClassification"
                :placeholder="$t('请选择价格分类')"
                @input="inputPricing($event, 'priceClassification')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="remark" :label="$t('备注')">
              <mt-input
                type="text"
                v-model="form.data.remark"
                :placeholder="$t('请输入备注')"
                maxlength="200"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row>
          <mt-col :span="12">
            <mt-form-item prop="outsourceMethod" :label="$t('工序委外')">
              <mt-select
                :value="form.data.outsourceMethod"
                :data-source="form.dataSource.outsourceMethodList"
                :placeholder="$t('委外必填')"
                :fields="{ text: 'theCodeName', value: 'outsourceMethod' }"
                :show-clear-button="true"
                @change="outsourcedClick"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
import debounceFilterSelect from 'ROUTER_PURCHASE_RFX/components/debounceFilterSelect'
import ItemTemplate from './ItemTemplate'
export default {
  components: {
    debounceFilterSelect
  },
  data() {
    return {
      selectItemTemplate: () => {
        return {
          template: ItemTemplate
        }
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.saveRule,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      expandSiteList: [],
      form: {
        data: {
          companyName: '', // 公司
          companyCode: '',
          companyId: '',
          priceObjectName: '', // 定价对象
          priceObjectCode: '',
          priceObjectId: '',
          purOrgName: '', // 	采购组织
          siteName: '', //工厂
          siteId: '',
          siteCode: '',
          sourcingExpand: [],
          purOrgCode: '',
          purOrgId: '',
          decidePriceType: 6, //单据类型
          id: '',
          pointNo: '', //定价单号
          remark: '',
          title: '',
          purId: '', //采购员
          purName: '',
          outsourceMethod: '', // 工序委外
          priceClassification: '',
          expandSiteList: [],
          sourcingType: ''
        },
        rules: {
          //单据类型
          decidePriceType: {
            required: true,
            message: this.$t('请选择单据类型'),
            trigger: 'blur,change'
          },
          // 采购员
          purId: {
            required: true,
            message: this.$t('请选择采购员'),
            trigger: 'blur'
          },
          // 公司
          companyName: {
            required: true,
            message: this.$t('请选择公司'),
            trigger: 'blur'
          },
          // 	采购组织
          purOrgName: {
            required: true,
            message: this.$t('请选择采购组织'),
            trigger: 'blur'
          },
          // 	工厂
          siteName: {
            required: true,
            message: this.$t('请选择工厂'),
            trigger: 'blur'
          },
          // 定价对对像
          priceObjectName: {
            required: true,
            message: this.$t('请选择定价对象'),
            trigger: 'blur'
          },
          priceClassification: {
            required: true,
            message: this.$t('请选择价格分类'),
            trigger: 'blur'
          },
          title: {
            required: true,
            message: this.$t('请输入标题'),
            trigger: 'blur'
          },
          sourcingType: {
            required: true,
            message: this.$t('请输入定价类型'),
            trigger: 'blur'
          }
        },
        dataSource: {
          pricing: [],
          procurement: [],
          siteNameList: [],
          expandSiteList: [],
          personnel: [],
          outsourceMethodList: [],
          firm: [],
          pricingType: [
            { text: this.$t('白电直接定价'), value: 6 }
            // { text: this.$t("寻源结果定价"), value: 1 },
          ],
          priceClassification: [
            { text: this.$t('暂估价格'), value: 3 },
            { text: this.$t('执行价格'), value: 4 }
          ],
          sourcingTypeList: [
            { text: this.$t('新品'), value: 'new_products' },
            { text: this.$t('二次'), value: 'second_inquiry' },
            { text: this.$t('已有'), value: 'exist' }
          ]
        }
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  beforeMount() {
    this.mergeFormData()
  },
  mounted() {
    this.getConfigList()
    // this.getUserPageList();
    this.getCurrentEmployees({ text: '' })
    this.getSpecifiedChildrenLevelOrgs()
    this.$refs['dialog'].ejsRef.show()
    // 获取工序委外下拉枚举
    this.getOutSourceMethod()
  },
  methods: {
    // 委外变更
    outsourcedClick(e) {
      if (e.itemData) {
        this.form.data.outsourceMethod = e.itemData.outsourceMethod
        this.form.data.outsourceMethodDesc = e.itemData.outsourceMethodDesc
      } else {
        this.form.data.outsourceMethod = null
        this.form.data.outsourceMethodDesc = null
      }
    },
    // 获取工序委外枚举值
    getOutSourceMethod() {
      this.$API.whitePoint.getOutSourceMethodList().then((res) => {
        this.form.dataSource.outsourceMethodList = (res.data || []).map((item) => {
          item.theCodeName = item.outsourceMethod + '-' + item.outsourceMethodDesc
          return item
        })
      })
    },
    inputPricing(val, type) {
      if (type == 'priceObjectName') {
        const arr = this.form.dataSource.pricing.find((e) => e.templateCode === val)
        if (arr) {
          this.form.data.priceObjectName = arr.sourcingObj
          this.form.data.priceObjectId = arr.id
          this.form.data.priceObjectCode = arr.templateCode
        }
      } else if (type == 'purOrgName') {
        const arr = this.form.dataSource.procurement.find((e) => {
          return e.organizationTypeCode == val
        })
        if (arr) {
          this.form.data.purOrgName = arr.organizationName
          this.form.data.purOrgId = arr.id
          this.form.data.purOrgCode = arr.organizationTypeCode
        }
        this.getSiteNameList(arr.id)
      } else if (type == 'purId') {
        this.form.data.purName = val.itemData.employeeName
        this.form.data.purId = val.itemData.employeeId
      } else if (type == 'companyName') {
        const arr = this.form.dataSource.firm.find((e) => e.orgTypeCode === val)
        if (arr) {
          this.form.data.companyName = arr.orgName
          this.form.data.companyId = arr.id
          this.form.data.companyCode = arr.orgTypeCode
        }
      } else if (type == 'decidePriceType') {
        const arr = this.form.dataSource.pricingType.find((e) => e.value === val)
        console.log(224, arr)
        if (arr) {
          this.form.data.decidePriceType = arr.value
        }
      } else if (type == 'priceClassification') {
        const arr = this.form.dataSource.priceClassification.find((e) => e.value === val)
        if (arr) {
          this.form.data.priceClassification = arr.value
          console.error(this.form.data.priceClassification)
        }
      } else if (type == 'siteName') {
        this.form.dataSource.expandSiteList = this.expandSiteList
        const arr = this.form.dataSource.siteNameList.find((e) => e.text === val)
        if (arr) {
          this.form.data.siteName = arr.data.siteName
          this.form.data.siteCode = arr.data.siteCode
          this.form.data.siteId = arr.data.siteId
        }
        let info = this.form.data.purOrgName + '+' + this.form.data.siteName
        this.form.dataSource.expandSiteList = this.form.dataSource.expandSiteList.filter((x) => {
          return x.text !== info
        })
      }
    },
    handleExpand(e) {
      let expandSaveRequestList = []
      e.value.forEach((v) => {
        this.form.dataSource.expandSiteList.forEach((x) => {
          if (v == x.value) {
            expandSaveRequestList.push(x.data)
          }
        })
      })
      this.form.data.expandSiteList = expandSaveRequestList
    },
    changeTaxItem(e) {
      this.form.data.companyName = e.itemData.orgName
      this.form.data.companyId = e.itemData.id
      this.form.data.companyCode = e.itemData.orgCode
      this.form.data.purOrgName = ''
      this.form.data.purOrgCode = ''
      this.form.data.purOrgId = ''
      this.getMainData(e.itemData.id)
      this.updateSourcingExpand()
    },
    changePurOrgName(e) {
      this.form.data.purOrgName = e.itemData.organizationName
      this.form.data.purOrgId = e.itemData.id
      this.form.data.purOrgCode = e.itemData.organizationCode
      this.getSiteNameList(this.form.data.purOrgId)
    },
    changeSourcingType(e) {
      this.form.data.sourcingType = e.itemData.value
    },
    mergeFormData() {
      const formKeys = Object.keys(this.form.data)
      const pData = this.modalData.data
      if (!pData) return
      for (let i = 0; i < formKeys.length; i++) {
        const formKey = formKeys[i]
        if (typeof pData[formKey] !== 'undefined') {
          this.form.data[formKey] = pData[formKey]
        }
      }
    },
    async saveRule() {
      const validate = await this.asyncFormValidate('appForm')
      if (!validate) {
        return
      }
      this.$API.whitePoint.saveHeader(this.form.data).then((res) => {
        if (res.code == 200) {
          this.emitConfirm(res.data)
        }
      })
    },

    asyncFormValidate(el) {
      return new Promise((c) => {
        this.$refs[el].validate((valid) => c(valid))
      })
    },
    cancel() {
      this.emitConfirm()
    },
    emitConfirm(...arg) {
      this.$emit('confirm-function', ...arg)
    },
    //定价对象列表
    getConfigList() {
      this.$API.businessConfig
        .getConfigList({
          page: {
            current: 1,
            size: 1000
          },
          condition: 'and',
          rules: [
            {
              field: 'sourcingMode',
              type: 'string',
              operator: 'equal',
              value: 'rfq'
            }
          ]
        })
        .then((res) => {
          this.form.dataSource.pricing = res.data.records
        })
    },

    //采购组织
    getMainData(companyId) {
      // 数据源 purGroupName
      // this.$API.masterData
      //   .purchaseOraginaze({
      //     organizationTypeCode: "BUORG002ADM",
      //   })
      //   .then((res) => {
      //     this.form.dataSource.procurement = res.data;
      //   });
      this.$API.masterData
        .permissionOrgList({
          orgId: companyId
        })
        .then((res) => {
          this.form.dataSource.procurement = res.data
        })
    },
    //采购员
    // getUserPageList() {
    //   const DEFAULTPARAM = {
    //     condition: "",
    //     page: {
    //       current: 1,
    //       size: 200,
    //     },
    //     pageFlag: false,
    //   };
    //   this.$API.masterData.getUserPageList(DEFAULTPARAM).then((res) => {
    //     this.form.dataSource.personnel = res.data.records;
    //   });
    // },
    // 获取 用户列表
    getCurrentEmployees(e) {
      const { text: fuzzyName } = e
      this.form.dataSource.personnel = []
      this.$API.masterData.getCurrentTenantEmployees({ fuzzyName }).then((res) => {
        const tmp = []
        res.data.forEach((item) => {
          tmp.push({
            ...item,
            text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`
          })
        })
        this.form.dataSource.personnel = tmp
        if (fuzzyName == '') {
          this.form.data.purId = this.form.dataSource.personnel[0].uid
        }
      })
    },
    filteringPurId(e) {
      if (typeof e.text === 'string' && e.text) {
        e.updateData(
          this.form.dataSource.personnel.filter((f) => f?.employeeName.indexOf(e.text) > -1)
        )
      } else {
        e.updateData(this.form.dataSource.personnel)
      }
    },
    //获取公司
    getSpecifiedChildrenLevelOrgs() {
      // this.$API.masterData
      //   .findSpecifiedChildrenLevelOrgs({
      //     organizationLevelCodes: ["ORG02"],
      //     orgType: "ORG001PRO",
      //     includeItself: false,
      //     organizationIds: [],
      //   })
      //   .then((res) => {
      //     this.form.dataSource.firm = res.data;
      //   });
      this.$API.masterData.permissionCompanyList().then((res) => {
        this.form.dataSource.firm = res?.data
      })
    },
    async getSiteNameList(organizationId) {
      const res = await this.$API.masterData
        .permissionSiteList({
          buOrgId: organizationId,
          companyId: this.form.data.companyId,
          orgLevelTypeCode: 'ORG06'
        })
        .catch(() => {})
      if (res) {
        this.form.dataSource.siteNameList = res.data.map((item) => ({
          text: item.orgName,
          value: item.orgName,
          data: {
            siteName: item.orgName,
            siteId: item.id,
            siteCode: item.orgCode
          }
        }))
      }
    },
    // 扩展
    async updateSourcingExpand() {
      const res = await this.$API.rfxDetail
        .purOrgWithSite({
          companyId: this.form.data.companyId
        })
        .catch(() => {})
      if (res && res.data) {
        let dataSource = []
        res.data.forEach((v) => {
          v.siteOrgs?.forEach((x) => {
            dataSource.push({
              text: v.businessOrganizationName + '+' + x.orgName,
              value: v.businessOrganizationCode + '+' + x.orgCode,
              data: {
                purOrgCode: v.businessOrganizationCode,
                purOrgId: v.id,
                purOrgName: v.businessOrganizationName,
                siteCode: x.orgCode,
                siteId: x.id,
                siteName: x.orgName,
                companyId: this.form.data.companyId,
                companyName: this.form.data.companyName,
                companyCode: this.form.data.companyCode
              }
            })
          })
        })
        this.expandSiteList = dataSource
        this.form.dataSource.expandSiteList = dataSource
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  padding-top: 20px;
}
</style>
