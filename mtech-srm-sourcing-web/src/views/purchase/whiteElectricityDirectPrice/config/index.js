import { i18n } from '@/main.js'
export const todoListToolBar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'del', icon: 'icon_solid_Cancel', title: i18n.t('删除') },
  { id: 'Submit', icon: 'icon_solid_Submit', title: i18n.t('下发供应商确认') },
  { id: 'close', icon: 'icon_solid_Closeorder', title: i18n.t('关闭') }
]
function formatDateTime(inputTime) {
  var date = new Date(inputTime)
  var y = date.getFullYear()
  var m = date.getMonth() + 1
  m = m < 10 ? '0' + m : m
  var d = date.getDate()
  d = d < 10 ? '0' + d : d
  var h = date.getHours()
  h = h < 10 ? '0' + h : h
  var minute = date.getMinutes()
  var second = date.getSeconds()
  minute = minute < 10 ? '0' + minute : minute
  second = second < 10 ? '0' + second : second
  return y + '-' + m + '-' + d + ' ' + h + ':' + minute + ':' + second
}
export const todoListColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('草稿'),
        1: i18n.t('供应商确认中'),
        2: i18n.t('供应商部分确认'),
        3: i18n.t('供应商部分驳回'),
        4: i18n.t('供应商已确认'),
        5: i18n.t('供应商已驳回'),
        6: i18n.t('审批中'),
        7: i18n.t('审批拒绝'),
        8: i18n.t('SAP处理中'),
        9: i18n.t('推送SAP失败'),
        10: i18n.t('已完成'),
        11: i18n.t('审批通过'),
        13: i18n.t('已关闭')
      }
    }
  },
  {
    field: 'pointNo',
    headerText: i18n.t('定价单号'),
    cssClass: 'field-content1'
  },
  {
    field: 'title',
    headerText: i18n.t('标题')
  },
  {
    field: 'decidePriceType',
    headerText: i18n.t('定价单类型'),
    valueConverter: {
      type: 'map',
      map: { 6: i18n.t('白电直接定价'), 1: i18n.t('寻源结果定价') }
    }
  },
  {
    field: 'priceClassification',
    headerText: i18n.t('价格分类'),
    valueConverter: {
      type: 'map',
      map: { 3: i18n.t('暂估价格'), 4: i18n.t('执行价格') }
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司')
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织')
  },
  {
    field: 'purName',
    headerText: i18n.t('采购员')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('时间'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (data) {
          return formatDateTime(parseInt(data))
        } else {
          return '-'
        }
      }
    }
  }
]
