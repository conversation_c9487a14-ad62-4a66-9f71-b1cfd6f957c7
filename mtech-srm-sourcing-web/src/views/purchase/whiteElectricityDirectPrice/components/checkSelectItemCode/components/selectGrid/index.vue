<template>
  <mt-dialog
    ref="dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
    :loading="loading"
  >
    <div class="dialog-content">
      <mt-template-page ref="templateRef" :template-config="pageConfig" />
    </div>
  </mt-dialog>
</template>

<script>
import { utils } from '@mtech-common/utils'
import { columnData } from './config'
import { addArrTextField } from '@/views/common/columnData/utils'
export default {
  data() {
    return {
      loading: false,
      selectRowsCache: [],
      data: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: [],
          grid: {
            allowFiltering: true,
            ignoreFields: ['isPriceRecord'],
            columnData: columnData,
            allowSorting: false,
            allowSelection: true,
            selectionSettings: {
              type: 'Single',
              checkboxOnly: false
            },
            rowSelected: this.rowSelecting,
            rowDataBound: (args) => {
              if (
                this.modalData.sourcingType == '' ||
                this.modalData.sourcingType == null ||
                this.modalData.sourcingType == undefined
              ) {
                return
              } else if (this.modalData.sourcingType == 'new_products') {
                if (this.modalData.priceClassification == 3) {
                  // 暂估价下执行价和暂估价类型为否
                  if (
                    args.data.isPriceRecord == 1 &&
                    (args.data.predictPrice != 0 || args.data.executePrice != 0)
                  ) {
                    args.row.classList.add('backgroundRed')
                  }
                } else if (this.modalData.priceClassification == 4) {
                  // 执行价判断执行类型
                  if (args.data.isPriceRecord == 1 && args.data.executePrice != 0) {
                    args.row.classList.add('backgroundRed')
                  }
                }
              } else if (
                this.modalData.sourcingType == 'second_inquiry' ||
                this.modalData.sourcingType == 'exist'
              ) {
                if (args.data.isPriceRecord == 0) {
                  args.row.classList.add('backgroundRed')
                }
              }
            },
            recordDoubleClick: this.recordDoubleClick,
            asyncConfig: {
              // url: this.$API.masterData.getItemListUrlPage,
              url: this.$API.comparativePrice.queryItems,
              afterAsyncData: () => {
                this.selectRowsCache = []
              },
              serializeList: (list) => {
                list.forEach((item) => {
                  if (item.categoryResponse == null) {
                    item.categoryCode = ''
                    item.categoryName = ''
                    item.categoryId = ''
                  } else {
                    item.categoryCode = item.categoryResponse.categoryCode
                    item.categoryName = item.categoryResponse.categoryName
                    item.categoryId = item.categoryResponse.id
                  }
                })
                return list
              },
              params: {
                organizationId: this.modalData.siteParam.organizationId,
                siteCode: this.modalData.siteParam.siteCode,
                rfxId: this.modalData.rfxId
              }
            }
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData.multiple) {
      let columnDataArr = utils.cloneDeep(columnData)
      columnDataArr.unshift({
        width: '50',
        type: 'checkbox'
      })
      this.$set(this.pageConfig[0].grid, 'columnData', columnDataArr)
      this.$set(this.pageConfig[0].grid, 'recordDoubleClick', null)
    } else {
      this.$set(this.pageConfig[0].grid, 'columnData', columnData)
    }
  },
  methods: {
    rowSelecting(e) {
      if (
        e.data.length == 0 ||
        e.isHeaderCheckboxClicked == undefined ||
        e.target == null ||
        this.modalData.multiple
      ) {
        return
      }
      if (!this.modalData.sourcingType) {
        this.selectRowsCache = []
        return
      }
      let grid = this.$refs.templateRef.getCurrentTabRef().grid
      if (e.isHeaderCheckboxClicked) {
        let data = utils.cloneDeep(e.data)
        let rows = []
        for (let index = 0; index < data.length; index++) {
          if (this.modalData.sourcingType == 'new_products') {
            if (data[index].isPriceRecord == 0) {
              rows.push(index)
            }
          } else if (
            this.modalData.sourcingType == 'second_inquiry' ||
            this.modalData.sourcingType == 'exist'
          ) {
            if (data[index].isPriceRecord == 1) {
              rows.push(index)
            }
          }
        }
        this.$nextTick(() => {
          if (JSON.stringify(rows.sort()) == JSON.stringify(this.selectRowsCache.sort())) {
            this.selectRowsCache = []
            grid.selectRows([])
          } else {
            this.selectRowsCache = rows
            grid.selectRows(rows)
          }
        })
        return
      }
      if (this.modalData.sourcingType == 'new_products') {
        if (e.data.isPriceRecord == 1) {
          this.$toast({
            content: this.$t('请选择没有价格记录的物料'),
            type: 'warning'
          })

          this.$nextTick(() => {
            let rowSelected = utils.cloneDeep(e.rowIndexes)
            rowSelected.splice(e.rowIndexes.indexOf(e.rowIndex), 1)
            this.selectRowsCache = rowSelected
            grid.selectRows(rowSelected)
          })
        } else {
          this.selectRowsCache = e.rowIndexes
        }
      } else if (
        this.modalData.sourcingType == 'second_inquiry' ||
        this.modalData.sourcingType == 'exist'
      ) {
        if (e.data.isPriceRecord == 0) {
          this.$toast({
            content: this.$t('请选择有价格记录的物料'),
            type: 'warning'
          })

          this.$nextTick(() => {
            let rowSelected = utils.cloneDeep(e.rowIndexes)
            rowSelected.splice(e.rowIndexes.indexOf(e.rowIndex), 1)
            this.selectRowsCache = rowSelected
            grid.selectRows(rowSelected)
          })
        } else {
          this.selectRowsCache = e.rowIndexes
        }
      }
    },
    recordDoubleClick(e) {
      if (this.modalData.sourcingType == 'new_products') {
        if (this.modalData.priceClassification == 3) {
          if (
            e.rowData.isPriceRecord == 1 &&
            (e.rowData.predictPrice != 0 || e.rowData.executePrice != 0)
          ) {
            this.$toast({
              content: this.$t('请选择没有价格记录且执行价类型和暂估价类型为否的物料'),
              type: 'warning'
            })
            return
          }
        } else if (this.modalData.priceClassification == 4) {
          // 执行价判断执行类型
          if (e.rowData.isPriceRecord == 1 && e.rowData.executePrice != 0) {
            this.$toast({
              content: this.$t('请选择没有价格记录且执行价类型为否的物料'),
              type: 'warning'
            })
            return
          }
        }
      } else if (
        this.modalData.sourcingType == 'second_inquiry' ||
        this.modalData.sourcingType == 'exist'
      ) {
        if (e.rowData.isPriceRecord == 0) {
          this.$toast({
            content: this.$t('请选择有价格记录的物料'),
            type: 'warning'
          })
          return
        }
      }
      this.getNewSupplierList([e.rowData])
      // this.confirmEvent([e.rowData]);
    },
    confirm() {
      if (this.modalData.multiple) {
        this.multipleConfirm()
        return
      }
      let _records = []
      _records = this.$refs.templateRef.getCurrentTabRef().grid.getSelectedRecords()

      if (_records.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (this.modalData.sourcingType == 'new_products') {
        if (this.modalData.priceClassification == 3) {
          if (
            _records[0].isPriceRecord == 1 &&
            (_records[0].predictPrice != 0 || _records[0].executePrice != 0)
          ) {
            this.$toast({
              content: this.$t('请选择没有价格记录且执行价类型和暂估价类型为否的物料'),
              type: 'warning'
            })
            return
          }
        } else if (this.modalData.priceClassification == 4) {
          // 执行价判断执行类型
          if (_records[0].isPriceRecord == 1 && _records[0].executePrice != 0) {
            this.$toast({
              content: this.$t('请选择没有价格记录且执行价类型为否的物料'),
              type: 'warning'
            })
          }
        }
      } else if (
        this.modalData.sourcingType == 'second_inquiry' ||
        this.modalData.sourcingType == 'exist'
      ) {
        if (_records[0].isPriceRecord == 0) {
          this.$toast({
            content: this.$t('请选择有价格记录的物料'),
            type: 'warning'
          })
          return
        }
      }
      this.getNewSupplierList(_records)
      // this.confirmEvent(_records);
    },
    multipleConfirm() {
      let _records = []
      _records = this.$refs.templateRef.getCurrentTabRef().gridRef.getMtechGridRecords()

      if (_records.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let msg = ''
      if (this.modalData.sourcingType == 'new_products') {
        if (this.modalData.priceClassification == 3) {
          _records.forEach((item) => {
            if (item.isPriceRecord == 1 && (item.predictPrice != 0 || item.executePrice != 0)) {
              msg = this.$t('请选择没有价格记录且执行价类型和暂估价类型为否的物料')
            }
          })
        } else if (this.modalData.priceClassification == 4) {
          // 执行价判断执行类型
          _records.forEach((item) => {
            if (item.isPriceRecord == 1 && item.executePrice != 0) {
              msg = this.$t('请选择没有价格记录且执行价类型为否的物料')
            }
          })
        }
      } else if (
        this.modalData.sourcingType == 'second_inquiry' ||
        this.modalData.sourcingType == 'exist'
      ) {
        _records.forEach((item) => {
          if (item.isPriceRecord == 0) {
            msg = this.$t('请选择有价格记录的物料')
          }
        })
      }
      if (msg) {
        this.$toast({
          content: msg,
          type: 'warning'
        })
        return
      }
      this.batchGetNewSupplierList(_records)
    },
    getNewSupplierList(_records) {
      let params = {
        pointId: this.modalData.rfxId,
        categoryCode: _records[0].categoryCode,
        itemCode: _records[0].itemCode,
        siteCode: this.modalData.siteParam.siteCode
      }
      this.$API.whitePoint.supplierDropdownList(params).then((res) => {
        if (res.data.length > 0) {
          let arr = addArrTextField(res.data, 'supplierCode', 'supplierName')
          this.$bus.$emit('supplierDropdownList', JSON.stringify(arr))
          this.confirmEvent(_records)
        } else if (res.data.length == 0) {
          this.$toast({
            content: this.$t('无关联供应商,请重新选择!'),
            type: 'warning'
          })
        }
      })
    },
    async batchGetNewSupplierList(_records) {
      this.loading = true
      let msg = ''
      for (let i = 0; i < _records.length; i++) {
        let params = {
          pointId: this.modalData.rfxId,
          categoryCode: _records[i].categoryCode,
          itemCode: _records[i].itemCode,
          siteCode: this.modalData.siteParam.siteCode
        }
        let res = await this.$API.whitePoint.supplierDropdownList(params)
        if (res.data.length == 0) {
          msg = `第${i + 1}条物料无关联供应商,请重新选择!`
        }
      }
      this.loading = false
      if (msg) {
        this.$toast({
          content: msg,
          type: 'warning'
        })
        return
      }
      this.$bus.$emit('batchAdd', _records)
      this.$emit('confirm-function')
    },
    confirmEvent(_records) {
      this.$emit('confirm-function', _records)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
