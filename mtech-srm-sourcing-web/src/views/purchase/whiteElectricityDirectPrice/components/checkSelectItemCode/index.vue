<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <mt-input
        :id="fieldName"
        :value="value"
        disabled
        :width="130"
        :placeholder="headerTxt"
      ></mt-input>
      <mt-icon
        style="width: 20px"
        name="icon_list_refuse"
        @click.native="handleClear"
        v-if="allowEditing"
      ></mt-icon>
      <mt-icon
        v-if="allowEditing"
        style="width: 20px"
        name="icon_input_search"
        @click.native="showDialog"
      ></mt-icon>
    </div>
  </div>
</template>

<script>
import { getValueByPath } from '@/utils/obj'

export default {
  props: [
    // eslint-disable-next-line vue/require-prop-types
    'field',
    // eslint-disable-next-line vue/require-prop-types
    'value',
    // eslint-disable-next-line vue/require-prop-types
    'rfxId',
    // eslint-disable-next-line vue/require-prop-types
    'submitTableData',
    // eslint-disable-next-line vue/require-prop-types
    'sourcingType',
    // eslint-disable-next-line vue/require-prop-types
    'siteParam',
    // eslint-disable-next-line vue/require-prop-types
    'priceClassification'
  ],
  data() {
    return {
      // data: {},
      fieldName: '',
      headerTxt: '',
      allowEditing: true
    }
  },
  mounted() {
    this.$bus.$on('hidebotton', () => {
      this.allowEditing = false
    })
    this.fieldName = this.field
    if (!this.allowEditing) return
  },
  methods: {
    handleClear() {
      this.$emit('input', null)
      // const fieldMap = {
      //   itemId: "id",
      //   itemCode: "itemCode",
      //   itemName: "itemName",
      //   spec: "itemDescription", // 规格描述
      //   material: "materialTypeName", // 材质
      //   unitName: "baseMeasureUnitName", // 单位
      // };
      // this.$bus.$emit(`categoryResponseChange`, null);

      // Object.entries(fieldMap).map(([field]) => {
      //   // this.$bus.$emit(`${field}Change`, null);
      //   if (
      //     field === "itemName" ||
      //     field === "itemCode" ||
      //     field === "spec" ||
      //     field === "material" ||
      //     field === "unitName" ||
      //     field === "categoryCode" ||
      //     field === "categoryName"
      //   ) {
      //     this.$bus.$emit(`${field}Change`, "");
      //   }
      // });
      this.$bus.$emit('clearChange')
    },
    showDialog() {
      this.$dialog({
        modal: () => import('./components/selectGrid'),
        data: {
          title: this.$t('选择物料'),
          rfxId: this.rfxId,
          itemCode: this.value,
          submitTableData: this.submitTableData,
          siteParam: this.siteParam,
          sourcingType: this.sourcingType,
          priceClassification: this.priceClassification
        },
        success: (data) => {
          this.$emit('input', data[0]['itemCode'])
          const fieldMap = {
            itemId: 'id',
            itemCode: 'itemCode',
            itemName: 'itemName',
            spec: 'itemDescription', // 规格描述
            material: 'materialTypeName', // 材质
            unitName: 'baseMeasureUnitName', // 基本单位
            unitId: 'baseMeasureUnitId', // 基本单位Id
            unitCode: 'baseMeasureUnitCode' // 基本单位Code
            // priceUnitName: "purchaseUnitName",
          }
          this.$bus.$emit(`categoryResponseChange`, data[0].categoryResponse)
          this.$bus.$emit(`getDefaultValue`)
          Object.entries(fieldMap).map(([field, key]) => {
            this.$bus.$emit(`${field}Change`, getValueByPath(data[0], key))
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    // justify-content: space-between;

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
</style>
