<template>
  <div class="full-height mt-flex-direction-column">
    <div class="miam-container">
      <div class="operate-bar">
        <div class="operate-content">
          <!-- <div class="operate-bars" @click="comeBack">
            <i class="mt-icons mt-icon-icon_input_arrow"></i>
          </div> -->
          <p class="operate-input">{{ this.form.pointNo }}</p>
          <div class="btns-wrap" v-show="!isView">
            <mt-button flat @click="comeBack()">{{ $t('返回') }}</mt-button>
            <mt-button
              v-show="(form.status < 6 || form.status == 7) && showHeaderForm"
              @click="save()"
              >{{ $t('保存') }}</mt-button
            >
            <mt-button flat v-show="showSubmit" @click="submit()">{{
              $t('下发供应商确认')
            }}</mt-button>
            <mt-button
              flat
              v-show="showPush && (form.status == 10 || form.status == 11)"
              @click="pushPrice()"
              >{{ $t('重新推送价格') }}</mt-button
            >
          </div>
        </div>
      </div>
      <!-- 顶部主要信息 -->
      <div class="mian-info" v-if="showHeaderForm">
        <mt-form ref="ruleForm" :model="form">
          <mt-form-item ref="pricingCode" :label="$t('定价单号')">
            <mt-input
              width="300"
              disabled
              float-label-type="Never"
              v-model="form.pointNo"
              :placeholder="$t('请输入定价单号')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item :label="$t('标题')">
            <mt-input
              width="300"
              float-label-type="Never"
              v-model="form.title"
              :placeholder="$t('请输入标题')"
              :disabled="statusJudge"
              maxlength="20"
            ></mt-input>
          </mt-form-item>
          <mt-form-item :label="$t('定价类型')">
            <mt-select
              :width="300"
              :value="form.sourcingType"
              :data-source="sourcingTypeList"
              :placeholder="$t('定价类型')"
              @change="changeSourcingType"
              :disabled="itemDisabled"
            ></mt-select>
          </mt-form-item>
          <mt-form-item :label="$t('公司')">
            <mt-select
              :width="300"
              v-model="form.companyName"
              float-label-type="Never"
              :data-source="companyNameList"
              :fields="{ text: 'orgName', value: 'orgName' }"
              @change="changeCompanyName"
              :placeholder="$t('请选择公司')"
              :disabled="itemDisabled"
            ></mt-select>
          </mt-form-item>
          <mt-form-item :label="$t('采购组织')">
            <mt-select
              :width="300"
              v-model="form.purOrgName"
              :fields="{
                text: 'organizationName',
                value: 'organizationName'
              }"
              :data-source="purOrgNameList"
              :placeholder="$t('采购组织')"
              @change="changePurOrgName"
              :disabled="itemDisabled"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="siteName" :label="$t('工厂')">
            <mt-select
              :width="300"
              v-model="form.siteName"
              :fields="{
                text: 'text',
                value: 'text'
              }"
              :data-source="this.siteNameList"
              :placeholder="$t('工厂')"
              @change="changeSiteName"
              :disabled="itemDisabled"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="expand" :label="$t('扩展')">
            <mt-multi-select
              :width="300"
              v-model="form.expand"
              :data-source="expandSiteList"
              :item-template="selectItemTemplate"
              @change="handleExpand($event)"
              :show-clear-button="false"
              :placeholder="$t('扩展')"
              :disabled="statusJudge"
              v-show="!statusJudge"
            />
            <mt-tooltip :content="expandInfo" target="#box">
              <div id="container">
                <div id="box" style="display: inline-block">
                  <mt-input
                    width="300"
                    disabled
                    float-label-type="Never"
                    v-model="expandInfo"
                    v-show="statusJudge"
                  ></mt-input>
                </div>
              </div>
            </mt-tooltip>
          </mt-form-item>
          <mt-form-item ref="personnel" :label="$t('采购人员')">
            <debounce-filter-select
              :width="300"
              v-model="form.purId"
              :request="getCurrentEmployees"
              :data-source="personnel"
              :show-clear-button="false"
              :fields="{ text: 'text', value: 'uid' }"
              :placeholder="$t('采购员')"
              :disabled="itemDisabled"
            ></debounce-filter-select>
          </mt-form-item>
          <mt-form-item ref="decidePriceType" :label="$t('定价单类型')">
            <mt-select
              :width="300"
              :data-source="[{ text: $t('白电直接定价'), value: 6 }]"
              v-model="form.decidePriceType"
              :placeholder="$t('请输入定价单类型')"
              :disabled="itemDisabled"
            ></mt-select>
          </mt-form-item>
          <mt-form-item :label="$t('价格分类')">
            <mt-select
              :width="300"
              :value="form.priceClassification"
              :data-source="priceClassification"
              :placeholder="$t('请选择价格分类')"
              :show-clear-button="true"
              @change="handlePriceClassification($event)"
              :disabled="itemDisabled"
            ></mt-select>
          </mt-form-item>
          <mt-form-item :label="$t('状态')">
            <mt-input width="300" disabled v-model="status"></mt-input>
          </mt-form-item>
          <mt-form-item ref="remark" :label="$t('备注')">
            <mt-input
              :width="300"
              v-model="form.remark"
              :placeholder="$t('字数不超过500字')"
              :disabled="statusJudge"
              maxlength="500"
              :multiline="true"
            ></mt-input>
          </mt-form-item>
          <mt-form-item :label="$t('工序委外')">
            <mt-select
              :width="300"
              :value="form.outsourceMethod"
              :data-source="outsourceMethodList"
              :placeholder="$t(' ')"
              :fields="{ text: 'theCodeName', value: 'outsourceMethod' }"
              :show-clear-button="true"
              :disabled="itemDisabled"
            ></mt-select>
          </mt-form-item>
        </mt-form>
      </div>
    </div>
    <div class="top-shrink" @click="showHeaderForm = !showHeaderForm">
      <mt-icon v-show="!showHeaderForm" name="a-iconxl"></mt-icon>
      <mt-icon v-show="showHeaderForm" name="a-iconsq"></mt-icon>
    </div>
    <div class="relation-ships">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
        v-if="pageConfig[0].grid.columnData.length > 0"
        @actionComplete="actionComplete"
        @handleSelectTab="handleSelectTab"
        @actionBegin="actionBegin"
      >
        <div class="form-design" slot="slot-2"><accessory></accessory></div>
      </mt-template-page>
    </div>
    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadInitBalanceRef"
      :down-template-name="downTemplateName"
      :request-urls="requestUrls"
      :down-template-params="downTemplateParams"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
    <upload-excel-dialog2
      ref="uploadInitBalanceRef2"
      :request-urls="requestUrls2"
      @closeUploadExcel="showUploadExcel2(false)"
      @upExcelConfirm="upExcelConfirm2"
    ></upload-excel-dialog2>
  </div>
</template>

<script>
import { pageConfig, columnDataOne, columnDataTwo, toolbar, toolbarTwo } from './config'
import debounceFilterSelect from 'ROUTER_PURCHASE_RFX/components/debounceFilterSelect'
import { download, getHeadersFileName } from '@/utils/utils'
import ItemTemplate from '../newAddComponents/ItemTemplate.vue'
import { v1 as uuidv1 } from 'uuid'
import { addArrTextField } from '@/views/common/columnData/utils'
export default {
  name: 'ExpertRating',
  data() {
    return {
      selectItemTemplate: () => {
        return {
          template: ItemTemplate
        }
      },
      size: '',
      openOA: '',
      WhetherOpenOA: false,
      pageConfig: pageConfig(
        this.$API.whitePoint.queryItemByPage,
        this.$route.query.id,
        this.serializeList,
        this.recordDoubleClick,
        this.$API.whitePoint.queryBuilder,
        this.rowDataBound,
        this.afterAsyncData
      ),
      form: {
        pointNo: '', //定价单号
        companyName: '', // 公司
        companyCode: '',
        companyId: '',
        purOrgName: '', // 	采购组织
        siteName: '', //工厂
        siteId: '',
        siteCode: '',
        expand: [],
        purOrgCode: '',
        purOrgId: '',
        decidePriceType: '', //定价单类型
        id: '',
        remark: '',
        title: '',
        purId: '', //采购员
        purName: '',
        priceClassification: '',
        sourcingType: '',
        expandSiteList: [],
        status: ''
      },
      oldForm: {},
      busEvent: new Set(), // 事件收集
      siteNameDataSource: [],
      showHeaderForm: false,
      siteNameList: [],
      expandSiteList: [],
      sourcingTypeList: [
        { text: this.$t('新品'), value: 'new_products' },
        { text: this.$t('二次'), value: 'second_inquiry' },
        { text: this.$t('已有'), value: 'exist' }
      ],
      priceClassification: [
        { text: this.$t('暂估价格'), value: 3 },
        { text: this.$t('执行价格'), value: 4 }
      ],
      companyNameList: [],
      purOrgNameList: [],
      personnel: [],
      outsourceMethodList: [],
      downTemplateName: this.$t('白电直接定价模板'),
      requestUrls: {},
      requestUrls2: {},
      downTemplateParams: {
        page: {
          current: 1,
          size: 10
        },
        rules: []
      },
      status: this.$t('草稿'),
      statusList: [
        {
          text: this.$t('草稿'),
          value: 0
        },
        {
          text: this.$t('供应商确认中'),
          value: 1
        },
        {
          text: this.$t('供应商部分确认'),
          value: 2
        },
        {
          text: this.$t('供应商部分驳回'),
          value: 3
        },
        {
          text: this.$t('供应商已确认'),
          value: 4
        },
        {
          text: this.$t('供应商已驳回'),
          value: 5
        },
        {
          text: this.$t('审批中'),
          value: 6
        },
        {
          text: this.$t('审批拒绝'),
          value: 7
        },
        {
          text: this.$t('SAP处理中'),
          value: 8
        },
        {
          text: this.$t('推送SAP失败'),
          value: 9
        },
        {
          text: this.$t('已完成'),
          value: 10
        },
        {
          text: this.$t('审批通过'),
          value: 11
        },
        {
          text: this.$t('审批废弃'),
          value: 12
        }
      ],
      tabIndex: 0,
      editList: [0, 1, 2, 3, 5, 7],
      haveList: false,
      expandInfo: '',
      addList: [0, 7], //草稿和拒绝可编辑所有
      middleEditList: [3, 5], //驳回和部分驳回 根据状态可以编辑列表和保存按钮
      middleForbidList: [1, 2, 4, 6, 8, 9, 10, 11], //确认中 部分确认 已确认 审批中 sap处理中 推送SAP失败 已完成和审批通过禁止双击编辑和表头操作
      priceChangeType: [
        { text: this.$t('涨'), value: '1' },
        { text: this.$t('平'), value: '0' },
        { text: this.$t('跌'), value: '-1' }
      ],
      showPush: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  watch: {
    modalData: {
      handler(newVal) {
        console.log(5050505, newVal)
      }
    }
  },
  components: {
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default,
    uploadExcelDialog2: require('@/components/Upload/uploadExcelDialog.vue').default,
    accessory: () => import('./accessory.vue'),
    debounceFilterSelect
  },

  computed: {
    propsData() {
      return {
        ...this.modalData
      }
    },
    isView() {
      return this.$route.query.isView ? this.$route.query.isView : false
    },
    showSubmit() {
      let arr = [0, 1, 2, 3, 5, 7, 8, 9]
      return arr.indexOf(this.form.status) > -1 && this.form.priceClassification == 4
    },
    itemDisabled() {
      return this.form.status !== 0 || this.haveList || this.isView
    },
    statusJudge() {
      return (this.form.status > 5 && this.form.status != 7) || this.isView
    }
  },
  // created() {
  //   this.getOaLink()
  // },
  async mounted() {
    // this.getOaLink()
    this.$store.commit('startLoading')
    await this.detailPoint()
    // 获取工序委外下拉枚举
    this.getOutSourceMethod()

    const direct = await this.$API.priceService.getDeliveryPlace().catch(() => {})
    let directDelivery = []
    if (direct) {
      directDelivery = direct.data.map(({ itemName }) => ({
        value: itemName,
        text: itemName
      }))
    }
    const Currency = await this.$API.masterData.queryAllCurrency().catch(() => {})
    let allCurrency = []
    if (Currency) {
      allCurrency = Currency.data.map(({ currencyName, currencyCode }) => ({
        value: currencyName,
        text: currencyName,
        code: currencyCode
      }))
    }
    const TaxItem = await this.$API.masterData.queryAllTaxItem().catch(() => {})
    const taxList = TaxItem.data
    const taxRateNameListCN = addArrTextField(taxList, 'taxItemCode', 'taxItemName')
    let unitNameList = []
    let unitRes = await this.$API.masterData.pagedQueryUnit().catch(() => {})
    if (unitRes) {
      unitNameList = unitRes?.data?.records || []
    }
    const unitNameListCN = addArrTextField(unitNameList, 'unitCode', 'unitName')
    let dataOne = columnDataOne(
      {
        siteNameDataSource: this.siteNameDataSource,
        allCurrency,
        directDelivery,
        taxList: taxRateNameListCN,
        getSupplierList: (e) => {
          return this.$API.masterData.getSupplierList(e)
        },
        sourcingType: this.form.sourcingType,
        rfxId: this.$route.query.id,
        siteParam: {
          organizationId: this.form.siteId,
          siteCode: this.form.siteCode
        },
        unitNameList: unitNameListCN,
        defaultValue: (e) => {
          return this.$API.whitePoint.defaultValue(e)
        },
        newSupplierList: (e) => {
          return this.$API.whitePoint.supplierDropdownList(e)
        },
        priceClassification: this.form.priceClassification,
        pagedQueryUnit: this.$API.masterData.pagedQueryUnit,
        priceChangeType: this.priceChangeType
      },
      {
        $on: (event, fn) => {
          this.busEvent.add(event)
          this.$bus.$on(event, fn)
        },
        $emit: (event, fn) => {
          this.busEvent.add(event)
          this.$bus.$emit(event, fn)
        }
      }
    )
    this.$set(this.pageConfig[0].grid, 'columnData', dataOne)
    this.$set(
      this.pageConfig[1].grid,
      'columnData',
      columnDataTwo({
        directDelivery,
        allCurrency
      })
    )
    this.getReadOnly()
    this.$store.commit('endLoading')

    this.$bus.$on('batchAdd', (data) => {
      this.batchAddList(data)
    })
  },
  beforeDestroy() {
    this.busEvent.forEach((event) => {
      this.$bus.$off(event)
    })
  },
  deactivated() {
    this.busEvent.forEach((event) => {
      this.$bus.$off(event)
    })
  },
  methods: {
    getOutSourceMethod() {
      this.$API.whitePoint.getOutSourceMethodList().then((res) => {
        this.outsourceMethodList = (res.data || []).map((item) => {
          item.theCodeName = item.outsourceMethod + '-' + item.outsourceMethodDesc
          return item
        })
      })
    },
    afterAsyncData(e) {
      if (this.tabIndex == 1) {
        this.size = e.data.total
      } else if (e.data.records.length > 0) {
        this.haveList = true
      } else {
        this.haveList = false
      }
    },
    handleSelectTab(e) {
      this.tabIndex = e
    },
    rowDataBound(args) {
      if (args.data.isGray == 1) {
        args.row.classList.add('backgroundRed')
      }
    },
    // 上传（显示弹窗）
    handleUpload() {
      this.requestUrls = {
        templateUrlPre: 'whitePoint',
        templateUrl: 'downloadImportTemplate',
        uploadUrl: 'importItem',
        pointNo: this.$route.query.pointNo
      }
      this.showUploadExcel(true)
    },
    // 上传（显示弹窗）
    handleUpload2() {
      this.requestUrls2 = {
        templateUrlPre: 'whitePoint',
        uploadUrl: 'importItemQuota',
        noDown: true
      }
      this.showUploadExcel2(true)
    },
    // 展示/不展示 上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadInitBalanceRef.uploadData = [] // 清空数据
        this.$refs.uploadInitBalanceRef.$refs.uploader.files = []
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 展示/不展示 上传弹窗
    showUploadExcel2(flag) {
      if (flag) {
        this.$refs.uploadInitBalanceRef2.uploadData = [] // 清空数据
        this.$refs.uploadInitBalanceRef2.$refs.uploader.files = []
        this.$refs.uploadInitBalanceRef2.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadInitBalanceRef2.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    recordDoubleClick(row) {
      const info = this.siteNameDataSource.find((e) => e.id === row.rowData.siteId)
      if (info) {
        sessionStorage.setItem('organizationId', info.id)
      }
    },
    actionBegin(args) {
      if (args.requestType === 'add') {
        args.data.addId = uuidv1()
        // args.data.siteName = this.form.siteName;
        // args.data.siteCode = this.form.siteCode;
        // args.data.siteId = this.form.siteId;
        // args.data.deliveryPlace = "合肥";
        // // 暂估价格默认结束日期9999-12-31
        // if (this.form.priceClassification == 3) {
        //   args.data.effectiveEndDate = 253402185600000;
        // }
      }
    },
    actionComplete(e) {
      if (e.requestType === 'beginEdit') {
        if (this.middleForbidList.indexOf(this.form.status) > -1) {
          e.row.editInstance.setOptionsAll({
            disabled: true,
            readonly: true
          })
        }
        if (
          this.middleEditList.indexOf(this.form.status) > -1 &&
          (e.rowData.supplierConfirmStatus == 2 || e.rowData.supplierConfirmStatus === '0')
        ) {
          // 驳回或者部分驳回时 供应商驳回才能编辑
          e.row.editInstance.setOptionsAll({
            disabled: true,
            readonly: true
          })
        }

        if (e.rowData.id) {
          this.$bus.$emit('hidebotton')
        }
        let params = {
          pointId: this.$route.query.id,
          categoryCode: e.rowData.categoryCode || '',
          itemCode: e.rowData.itemCode || '',
          siteCode: this.form.siteCode
        }
        this.$API.whitePoint.supplierDropdownList(params).then((res) => {
          let arr = addArrTextField(res.data, 'supplierCode', 'supplierName')
          this.$bus.$emit('supplierDropdownListNew', JSON.stringify(arr))
        })
      }
      if (e.requestType == 'save') {
        //数据存储
        if (e.action == 'add') {
          //新增操作
          this.handleAddRow(e)
        }
      }
    },
    // 新增的完成事件
    handleAddRow(args) {
      args.data.addId = args.data.addId || uuidv1() // 保证主数据的值存在
      let _current = this.$refs.templateRef.getCurrentTabRef()
      _current?.grid.clearRowSelection()
    },
    // 结束编辑状态
    endEdit() {
      let _current = this.$refs.templateRef.getCurrentTabRef()
      _current?.grid.endEdit()
      _current?.grid.hideSpinner()
    },
    getReadOnly() {
      if (this.editList.indexOf(this.form.status) == -1 && this.form.priceClassification == 3) {
        // this.pageConfig[0].grid.editSettings.allowEditing = false;
        // this.pageConfig[1].grid.editSettings.allowEditing = false;
      }
    },
    comeBack() {
      this.$router.go(-1)
    },
    save() {
      let params = this.form
      if (this.form.title == '') {
        this.$toast({
          content: this.$t('标题不能为空'),
          type: 'warning'
        })
        return
      }
      if (this.form.status == 0) {
        this.$API.whitePoint.saveHeader(params).then((res) => {
          if (res.code == 200) {
            this.$toast({
              content: res.msg,
              type: 'success'
            })
            this.oldForm = JSON.stringify(this.form)
            this.$router.go(0)
          }
        })
      } else if ((this.form.status > 0 && this.form.status <= 5) || this.form.status == 7) {
        let params2 = {
          expandSiteList: this.form.expandSiteList,
          pointId: this.$route.query.id,
          remark: this.form.remark,
          title: this.form.title
        }
        this.$API.whitePoint.saveHeaderExpand(params2).then((res) => {
          if (res.code == 200) {
            this.$toast({
              content: res.msg,
              type: 'success'
            })
            this.oldForm = JSON.stringify(this.form)
            // this.$router.go(0);
          }
        })
      }
    },
    submit() {
      this.$dialog({
        data: {
          title: this.$t('提交'),
          message: this.$t('是否确认下发供应商？')
        },
        success: () => {
          let params = {
            id: this.form.id
          }
          this.$API.whitePoint.sendToSupplier(params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('提交成功'), type: 'success' })
              this.$router.go(0)
            }
          })
        }
      })
    },

    //保存
    handleSaveFn(e) {
      let _selectGridRecords = e.grid.getCurrentViewRecords()
      if (_selectGridRecords.length == 0) {
        this.$toast({
          type: 'warning',
          content: this.$t('请先新增定价物料信息')
        })
        return
      }
      let msg = ''
      _selectGridRecords.forEach((e) => {
        e.pointId = this.$route.query.id
      })
      for (const value of _selectGridRecords) {
        if (value.effectiveStartDate > value.effectiveEndDate) {
          msg = this.$t('生效日期不能大于失效日期')
        }
      }
      // for (const value of _selectGridRecords) {
      //   if (value.unconditionalLeadTime < value.leadTime) {
      //     msg = this.$t("无条件L/T必须>=L/T");
      //   }
      // }
      for (const value of _selectGridRecords) {
        let number = value.minPurQuantity / value.minPackageQuantity
        if (Math.floor(number) !== number) {
          msg = this.$t('最小采购量必须为最小包装量的整数倍')
        }
      }

      if (msg) {
        this.$toast({
          content: msg,
          type: 'warning'
        })
      } else {
        this.$API.whitePoint.saveItem(_selectGridRecords).then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    },
    //保存
    handleSaveTwoFn(e) {
      let _selectGridRecords = e.grid.getCurrentViewRecords()
      if (_selectGridRecords.length == 0) {
        this.$toast({
          type: 'warning',
          content: this.$t('请先维护定价物料')
        })
        return
      }
      let str = []
      _selectGridRecords.forEach((item, index) => {
        if (item.allocationRatio < 0 || item.allocationRatio > 100) {
          str.push(this.$t(`第${index + 1}行`))
        }
      })
      if (str.length != 0) {
        this.$toast({
          content: str.join(',') + '的配额字段，请输入（0-100）的数字',
          type: 'warning'
        })
        return
      }
      let ids = _selectGridRecords.map((x) => {
        return {
          allocationQuantity: x.allocationQuantity,
          allocationRatio: (x.allocationRatio / 100).toFixed(2),
          minSplitQuantity: x.minSplitQuantity,
          id: x.id
        }
      })
      this.$API.whitePoint
        .saveItemQuota({
          pointId: this.$route.query.id,
          quotaDTOList: ids
        })
        .then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
    },

    //删除
    handleDelFn(_selectGridRecords, idList) {
      if (_selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else {
        let params = {
          pointId: this.$route.query.id,
          pointItemIdList: idList
        }
        this.$dialog({
          data: {
            title: this.$t('删除'),
            message: this.$t('是否确认删除？')
          },
          success: () => {
            let _current = this.$refs.templateRef.getCurrentTabRef()
            // if (params.pointItemIdList.length) {
            //   this.$API.whitePoint.deleteItem(params).then((res) => {
            //     if (res.code == 200) {
            //       this.$refs.templateRef.refreshCurrentGridData();
            //     }
            //   });
            // } else {
            //   _current?.grid.deleteRecord();
            // }
            // let list = _current.getCurrentViewRecords();
            // if (list.length > 0) {
            //   this.haveList = true;
            // } else {
            //   this.haveList = false;
            // }
            return new Promise((resolve) => {
              if (params.pointItemIdList.length) {
                resolve(this.$API.whitePoint.deleteItem(params))
              } else {
                resolve({ message: null })
              }
              _current?.grid.deleteRecord()
              let list = _current.grid.getCurrentViewRecords()
              if (list.length > 0) {
                this.haveList = true
              } else {
                this.haveList = false
              }
            })
          }
        })
      }
    },

    //新增
    handleAddFn(e) {
      let row = {
        siteName: this.form.siteName,
        siteCode: this.form.siteCode,
        siteId: this.form.siteId,
        deliveryPlace: this.$t('合肥'),
        priceUnitName: 1000
      }
      // 暂估价格默认结束日期9999-12-31
      if (this.form.priceClassification == 3) {
        row.effectiveEndDate = 4102329600000
      }
      this.endEdit()
      e.grid.addRecord(row)
      // this.endEdit();
      // e.grid.addRecord();
      this.haveList = true
    },
    handlePriceClassification(e) {
      this.form.priceClassification = e.itemData.value
    },
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      let idList = []
      _selectGridRecords.map((item) => {
        idList.push(item.id)
      })
      if (e.toolbar.id == 'Save') {
        // if (JSON.stringify(this.form) !== this.oldForm) {
        //   this.$toast({
        //     type: "warning",
        //     content: this.$t("请先保存头部信息"),
        //   });
        //   return;
        // }
        this.endEdit()
        setTimeout(() => {
          this.handleSaveFn(e) //保存
        }, 100)
      } else if (e.toolbar.id == 'del') {
        this.handleDelFn(_selectGridRecords, idList) //删除
      } else if (e.toolbar.id == 'SaveTwo') {
        this.endEdit()
        setTimeout(() => {
          this.handleSaveTwoFn(e) //保存
        }, 100)
      } else if (e.toolbar.id == 'upload') {
        // if (JSON.stringify(this.form) !== this.oldForm) {
        //   this.$toast({
        //     type: "warning",
        //     content: this.$t("请先保存头部信息"),
        //   });
        //   return;
        // }
        this.handleUpload()
      } else if (e.toolbar.id == 'upload2') {
        let dataSource = this.$refs.templateRef.getCurrentTabRef().grid.getCurrentViewRecords()
        if (dataSource.length == 0) {
          this.$toast({
            type: 'warning',
            content: this.$t('没有配额数据不能导入')
          })
        } else {
          this.handleUpload2()
        }
      } else if (e.toolbar.id == 'Add') {
        // if (JSON.stringify(this.form) !== this.oldForm) {
        //   this.$toast({
        //     type: "warning",
        //     content: this.$t("请先保存头部信息"),
        //   });
        //   return;
        // }
        this.handleAddFn(e) //新增
      } else if (e.toolbar.id == 'Export_a') {
        this.$API.whitePoint
          .exportItem({
            page: {
              current: 1,
              size: 1000
            },
            defaultRules: [
              {
                label: this.$t('定点推荐id'),
                field: 'point_id',
                type: 'string',
                operator: 'equal',
                value: this.$route.query.id
              }
            ]
          })
          .then((res) => {
            this.$toast({
              type: 'success',
              content: this.$t('正在导出，请稍后！')
            })
            let fileName = getHeadersFileName(res)
            download({ fileName: fileName, blob: res.data })
          })
      } else if (e.toolbar.id == 'Export_b') {
        this.$API.whitePoint
          .exportItemQuota({
            page: {
              current: 1,
              size: this.size
            },
            defaultRules: [
              {
                label: this.$t('定点推荐id'),
                field: 'pointId',
                type: 'string',
                operator: 'equal',
                value: this.$route.query.id
              },
              {
                label: this.$t('定点推荐type'),
                field: 'sourceDocType',
                type: 'string',
                operator: 'equal',
                value: 'white_point'
              }
            ]
          })
          .then((res) => {
            this.$toast({
              type: 'success',
              content: this.$t('正在导出，请稍后！')
            })
            let fileName = getHeadersFileName(res)
            download({ fileName: fileName, blob: res.data })
          })
      } else if (e.toolbar.id == 'redirectOA') {
        this.$dialog({
          data: {
            title: this.$t('提交'),
            message: this.$t('是否确认提交oa审批？')
          },
          success: () => {
            this.$API.whitePoint.submitOA({ id: this.$route.query.id }).then((res) => {
              if (res.code == 200) {
                this.$toast({
                  type: 'success',
                  content: this.$t('提交oa审批成功')
                })
                this.$router.go(0)
              }
            })
          }
        })
      } else if (e.toolbar.id == 'progressOA') {
        window.open(this.openOA)
      } else if (e.toolbar.id == 'batchAdd') {
        this.batchAdd()
      }
    },
    batchAdd() {
      this.$dialog({
        modal: () => import('../components/checkSelectItemCode/components/selectGrid/index.vue'),
        data: {
          title: this.$t('批量选择物料'),
          rfxId: this.$route.query.id,
          sourcingType: this.form.sourcingType,
          siteParam: {
            organizationId: this.form.siteId,
            siteCode: this.form.siteCode
          },
          multiple: true,
          priceClassification: this.form.priceClassification
        }
      })
    },
    async detailPoint() {
      let WhetherOpenOA = true
      this.$API.whitePoint.getOaLink({ pointId: this.$route.query.id }).then((res) => {
        if (!res.data) {
          WhetherOpenOA = false
        } else {
          this.openOA = res.data
        }
      })
      let param = {
        id: this.$route.query.id
      }
      const res = await this.$API.whitePoint.viewHeaderDetailById(param).catch(() => {})
      let status = false
      if (res.data.status == 4) {
        status = true
      }
      if (res.data.priceClassification == 3) {
        status = true
      }
      if (res.data.status == 6 || res.data.status == 10 || res.data.status == 11) {
        status = false
      }
      if (res.code == 200) {
        sessionStorage.setItem('isKtFlag', res.data.isKtFlag)

        this.$set(
          this.pageConfig[0],
          'toolbar',
          toolbar(this.middleForbidList.indexOf(res.data.status) == -1 && !this.isView)
        )
        this.$set(
          this.pageConfig[1],
          'toolbar',
          toolbarTwo(
            this.middleForbidList.indexOf(res.data.status) == -1 && !this.isView,
            status,
            WhetherOpenOA
          )
        )
        if (this.middleEditList.indexOf(res.data.status) > -1) {
          this.$set(this.pageConfig[0], 'toolbar', [
            {
              id: 'Save',
              icon: 'icon_solid_Save',
              title: this.$t('保存')
            }
          ])
        }
        this.updateSourcingExpand({
          companyId: res.data.companyId,
          companyCode: res.data.companyCode,
          companyName: res.data.companyName
        })
        this.getSiteNameList({
          purOrgId: res.data.purOrgId,
          companyId: res.data.companyId
        })
        this.getSpecifiedChildrenLevelOrgs()
        this.getMainData(res.data.companyId)
        this.getCurrentEmployees({ text: '' })
        this.form.pointNo = res.data.pointNo
        this.form.companyName = res.data.companyName
        this.form.companyCode = res.data.companyCode
        this.form.companyId = res.data.companyId
        this.form.purOrgName = res.data.purOrgName
        this.form.purOrgCode = res.data.purOrgCode
        this.form.purOrgId = res.data.purOrgId
        this.form.siteName = res.data.siteName
        this.form.siteCode = res.data.siteCode
        this.form.siteId = res.data.siteId
        this.form.decidePriceType = res.data.decidePriceType
        this.form.id = res.data.id
        this.form.remark = res.data.remark
        this.form.title = res.data.title
        this.form.purId = res.data.purId
        this.form.purName = res.data.purName
        this.form.priceClassification = res.data.priceClassification
        this.form.sourcingType = res.data.sourcingType
        this.form.status = res.data.status
        this.form.outsourceMethod = res.data.outsourceMethod
        this.getStatus(res.data.status)

        let arr = []
        let arr2 = []
        res.data.expandSiteList.forEach((x) => {
          arr.push(x.purOrgCode + '+' + x.siteCode)
          arr2.push(x.purOrgName + '+' + x.siteName)
        })
        this.form.expand = arr
        this.expandInfo = arr2.toString()

        setTimeout(() => {
          this.oldForm = JSON.stringify(this.form)
        }, 3000)
        if (
          res.data.pushPriceStatus === -1 ||
          res.data.pushPriceStatus === 0 ||
          res.data.pushQuotaStatus === -1 ||
          res.data.pushQuotaStatus === -1
        ) {
          this.showPush = true
        }
      }
    },
    getStatus(status) {
      let info = this.statusList.find((e) => {
        return e.value == status
      })
      // this.status = info.text
      this.status = info && info.text ? info.text : this.$t('未匹配')
    },
    serializeList(list) {
      let arr = []
      list.forEach((x) => {
        x.allocationRatio = (x.allocationRatio * 100).toFixed(2)
        arr.push(
          JSON.stringify({
            itemCode: x.itemCode,
            siteCode: x.siteCode
          })
        )
      })
      arr = [...new Set(arr)]
      arr = arr.filter((item, index) => index % 2 !== 0)
      list.forEach((x) => {
        arr.forEach((v) => {
          if (x.itemCode == JSON.parse(v).itemCode && x.siteCode == JSON.parse(v).siteCode) {
            x.isGray = true
          }
        })
      })

      return list
    },
    async getSiteNameList(info) {
      const res = await this.$API.masterData
        .permissionSiteList({
          buOrgId: info.purOrgId,
          companyId: info.companyId,
          orgLevelTypeCode: 'ORG06'
        })
        .catch(() => {})
      if (res) {
        this.siteNameDataSource = res?.data || []
        this.siteNameList = res.data.map((item) => ({
          text: item.orgName,
          value: item.orgName,
          data: {
            siteName: item.orgName,
            siteId: item.id,
            siteCode: item.orgCode
          }
        }))
      }
    },
    // 扩展
    async updateSourcingExpand(info) {
      const res = await this.$API.rfxDetail
        .purOrgWithSite({
          companyId: info.companyId
        })
        .catch(() => {})
      if (res && res.data) {
        let dataSource = []
        res.data.forEach((v) => {
          v.siteOrgs?.forEach((x) => {
            dataSource.push({
              text: v.businessOrganizationName + '+' + x.orgName,
              value: v.businessOrganizationCode + '+' + x.orgCode,
              data: {
                purOrgCode: v.businessOrganizationCode,
                purOrgId: v.id,
                purOrgName: v.businessOrganizationName,
                siteCode: x.orgCode,
                siteId: x.id,
                siteName: x.orgName,
                ...info
              }
            })
          })
        })
        this.expandSiteList = dataSource
      }
    },
    //获取公司
    getSpecifiedChildrenLevelOrgs() {
      this.$API.masterData
        .findSpecifiedChildrenLevelOrgs({
          organizationLevelCodes: ['ORG02'],
          orgType: 'ORG001PRO',
          includeItself: false,
          organizationIds: []
        })
        .then((res) => {
          this.companyNameList = res.data
        })
    },
    //采购组织
    getMainData(id) {
      console.error()
      this.$API.masterData
        .permissionOrgList({
          orgId: id
        })
        .then((res) => {
          this.purOrgNameList = res.data
        })
    },
    // 获取 用户列表
    getCurrentEmployees(e) {
      const { text: fuzzyName } = e
      this.personnel = []
      this.$API.masterData.getCurrentTenantEmployees({ fuzzyName }).then((res) => {
        const tmp = []
        res.data.forEach((item) => {
          tmp.push({
            ...item,
            text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`
          })
        })
        this.personnel = tmp
        if (fuzzyName == '') {
          this.form.purId = this.personnel[0].uid
        }
      })
    },
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.$refs.templateRef.refreshCurrentGridData()
    },
    upExcelConfirm2() {
      this.showUploadExcel2(false)
      this.$refs.templateRef.refreshCurrentGridData()
    },
    changeSourcingType(e) {
      this.form.sourcingType = e.itemData.value
    },
    changeCompanyName(e) {
      this.form.companyName = e.itemData.orgName
      this.form.companyCode = e.itemData.orgCode
      this.form.companyId = e.itemData.id
      console.error(e, 1234)
      // this.form.purOrgName = "";
      // this.form.purOrgCode = "";
      // this.form.purOrgId = "";
      // this.form.siteName = "";
      // this.form.siteCode = "";
      // this.form.siteId = "";
      this.getMainData(this.form.companyId)
      this.updateSourcingExpand({
        companyId: this.form.companyId,
        companyCode: this.form.companyCode,
        companyName: this.form.companyName
      })
    },
    changePurOrgName(e) {
      this.form.purOrgName = e.itemData.organizationName
      this.form.purOrgCode = e.itemData.organizationCode
      this.form.purOrgId = e.itemData.id
      // this.form.siteName = "";
      // this.form.siteCode = "";
      // this.form.siteId = "";
      this.getSiteNameList({
        purOrgId: this.form.purOrgId,
        companyId: this.form.companyId
      })
    },
    changeSiteName(e) {
      this.form.siteName = e.itemData.data.siteName
      this.form.siteCode = e.itemData.data.siteCode
      this.form.siteId = e.itemData.data.siteId
    },
    handleExpand(e) {
      let expandSaveRequestList = []
      e.value.forEach((v) => {
        this.expandSiteList.forEach((x) => {
          if (v == x.value) {
            expandSaveRequestList.push(x.data)
          }
        })
      })
      this.form.expandSiteList = expandSaveRequestList
    },
    batchAddList(data) {
      let e = this.$refs.templateRef.getCurrentTabRef()
      data.forEach((item) => {
        let row = {
          siteName: this.form.siteName,
          siteCode: this.form.siteCode,
          siteId: this.form.siteId,
          deliveryPlace: this.$t('合肥'),
          priceUnitName: 1000,
          itemId: item.id,
          itemCode: item.itemCode,
          itemName: item.itemName,
          spec: item.itemDescription,
          material: item.materialTypeName,
          unitName: item.baseMeasureUnitName,
          unitId: item.baseMeasureUnitId,
          unitCode: item.baseMeasureUnitCode,
          categoryName: item.categoryResponse.categoryName,
          categoryCode: item.categoryResponse.categoryCode,
          categoryId: item.categoryResponse.id,
          purUnitName: item.baseMeasureUnitName,
          purUnitCode: item.baseMeasureUnitCode
        }
        // 暂估价格默认结束日期
        if (this.form.priceClassification == 3) {
          // row.effectiveEndDate = 253402185600000 // 9999-12-31
          row.effectiveEndDate = 4102329600000 // 2099-12-31
        }
        this.endEdit()
        e.grid.addRecord(row)
        // this.endEdit();
        // e.grid.addRecord();
        this.haveList = true
      })
    },
    pushPrice() {
      this.$dialog({
        data: {
          title: this.$t('提交'),
          message: this.$t('是否重新推送价格？')
        },
        success: () => {
          this.$store.commit('startLoading')
          let params = {
            id: this.form.id
          }
          this.$API.whitePoint.manualPushPriceAndQuota(params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('推送成功'), type: 'success' })
              this.$store.commit('endLoading')
              this.$router.go(0)
            }
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.operate-content {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
}
.mt-form {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
  .grid_flex {
    width: 679px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .mt-form-item {
    margin: 0 10px 10px 10px;
  }
}
.operate-bar {
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #4f5b6d;
  font-size: 14px;
  padding: 20px;
}
.operate-bars {
  width: 20px;
  height: 20px;
  background: rgba(0, 70, 156, 0.1);
  border: 1px solid rgba(0, 70, 156, 0.9);
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;

  i {
    width: 6px;
    height: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: rotate(90deg);
  }
}
.miam-container {
  background: #fff;
  padding: 0 20px;

  .mian-info {
    // height: 300px;
    background: rgba(255, 255, 255, 1);
    // border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    padding: 20px;
    min-width: 1300px;
    .normal-title {
      width: 100%;
      height: 14px;
      line-height: 14px;
      font-size: 14px;
      color: #292929;
      font-family: PingFangSC;
      font-weight: 500;

      &:before {
        content: ' ';
        display: inline-block;
        vertical-align: middle;
        width: 2px;
        height: 10px;
        background: rgba(0, 70, 156, 1);
        border-radius: 1px;
        margin-right: 10px;
      }
    }

    .input-item {
      margin-top: 20px;
      padding-right: 50px;
      .label-txt {
        height: 40px;
        width: 130px;
        font-size: 14px;
        // line-height: 40px;
        color: #292929;
      }
      .label-value {
        height: 40px;
        width: 130px;
        font-size: 14px;
        // line-height: 40px;
        color: #35404e;
      }
      .select-container {
        height: 40px;
      }
      .e-label {
        color: #35404e;
      }
      .label-text {
        color: #35404e;
      }
    }
    .input-item /deep/ .normal-width {
      width: 240px;
    }
    .input-item /deep/ .e-radio + label .e-label {
      color: #35404e;
    }
  }
}
.relation-ships {
  flex: 1;
  background: rgba(255, 255, 255, 1);
}
.btns-wrap {
  /deep/ .mt-button {
    margin-right: 0;
    button {
      background: transparent;
      //border: 1px solid rgba(0, 70, 156, 0.1);
      border-radius: 4px;
      box-shadow: unset;
      padding: 6px 12px 4px;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(0, 70, 156, 1);
    }
  }
}
.top-shrink {
  color: #9bb0cb;
  display: flex;
  justify-content: center;
}
</style>
