import { i18n } from '@/main.js'
import Vue from 'vue'
export const rdToolBar = [
  { id: 'Add', icon: 'icon_solid_upload', title: i18n.t('上传') },
  { id: 'pull', icon: 'icon_solid_Download', title: i18n.t('下载') },
  { id: 'delete', icon: 'icon_solid_Delete', title: i18n.t('删除') }
]
const iconSetting = {
  '.ppt': 'mt-icon-icon_ppt',
  '.docx': 'mt-icon-icon_word',
  '.pdf': 'mt-icon-icon_pdf',
  '.xls': 'mt-icon-icon_excel',
  '.png': 'mt-icon-icon_File1'
}
export const rdColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'fileName',
    cssClass: 'field-content',
    headerText: i18n.t('文件名称'),
    cellTools: [
      { id: 'download', icon: 'icon_solid_Download', title: i18n.t('下载') },
      { id: 'delete', icon: 'icon_solid_Delete', title: i18n.t('删除') }
    ]
  },
  {
    field: 'fileSize',
    headerText: i18n.t('文件大小')
  },
  {
    field: 'fileType',
    headerText: i18n.t('文件类型'),
    template: function () {
      return {
        template: Vue.component('fileTypeOption', {
          template: `<div><i :class="['mt-icons', icon]"></i><span style="margin-left: 5px">{{data.fileType}}</span></div>`,
          data() {
            return { data: { data: {} } }
          },
          computed: {
            icon() {
              const { fileType } = this.data
              return fileType ? iconSetting[fileType] : ''
            }
          }
        })
      }
    }
    // template: function () {
    //   return {
    //     template: Vue.component("actionOption", {
    //       template: `<div class="action-boxs">
    //               <template v-if="data.fileType == '.xls'">
    //                 <mt-icon name="Pdf_Export" class="checked"></mt-icon>
    //                 <span>ppt</span>
    //               </template>
    //               <template v-if="data.fileType == '.docx'">
    //                 <mt-icon name="CSV_Export" class="checked"></mt-icon>
    //                 <span>ppt</span>
    //               </template>
    //               <template v-if="data.fileType == '.jpg'">
    //                 <mt-icon name="Excel_Export" class="checked"></mt-icon>
    //                 <span>ppt</span>
    //               </template>
    //               <template v-if="data.fileType == '.png'">
    //                 <mt-icon name="MT_Daterange" class="checked"></mt-icon>
    //                 <span>ppt</span>
    //               </template>
    //               <template v-if="data.fileType == '.pdf'">
    //                 <mt-icon name="Pdf_Export" class="checked"></mt-icon>
    //                 <span>ppt</span>
    //               </template>
    //           </div>`,
    //     }),
    //   };
    // },
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  }
]
