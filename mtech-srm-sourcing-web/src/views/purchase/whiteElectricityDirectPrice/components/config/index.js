import { getValueByPath } from '@/utils/obj'
import { createEditInstance, Formatter } from '@/utils/ej/dataGrid/index'
import checkSelectedItemCode from '../checkSelectItemCode' // 物料
import cellChanged from 'COMPONENTS/NormalEdit/cellChanged' // 单元格被改变（纯展示）
import { i18n } from '@/main.js'
import { useFiltering } from '@/utils/ej/select'
import Decimal from 'decimal.js'
import { makeTextFields, addArrTextField } from '@/views/common/columnData/utils'
import { mergeDataSource } from '@/utils/ej/dataGrid/utils'
const _isKtFlag = sessionStorage.getItem('isKtFlag')
export const columnDataOne = (
  {
    siteNameDataSource = [],
    directDelivery,
    allCurrency = [],
    taxList = [],
    getSupplierList,
    sourcingType = '',
    rfxId = '',
    siteParam = {},
    unitNameList = [],
    defaultValue,
    priceClassification,
    pagedQueryUnit,
    priceChangeType
  } = {},
  bus
) => {
  const quoteAttributeList = [
    {
      text: i18n.t('标准价'),
      value: 'standard_price'
    },
    {
      text: i18n.t('寄售价'),
      value: 'mailing_price'
    },
    {
      text: i18n.t('委外价'),
      value: 'outsource'
    }
  ]
  const quoteModeList = [
    {
      text: i18n.t('按照入库'),
      value: 'in_warehouse'
    },
    {
      text: i18n.t('按出库'),
      value: 'out_warehouse'
    },
    {
      text: i18n.t('按订单日期'),
      value: 'order_date'
    }
  ]
  const statusData = [
    {
      text: i18n.t('未确认'),
      value: '0'
    },
    {
      text: i18n.t('已驳回'),
      value: '1'
    },
    {
      text: i18n.t('已确认'),
      value: '2'
    }
  ]
  const editInstance = createEditInstance()
    .component('checkSelectedItemCode', checkSelectedItemCode)
    .onInput((ctx, { field, rowData, value }) => {
      console.log('onInput', { ctx, rowData, value, field })
      if (field === 'siteName') {
        // 工厂被编辑
        const { dataSource, fields } = ctx.getOptions(field)
        const row = dataSource.find((e) => e[fields?.value || 'value'] === value)
        if (row) {
          ctx.setValueByField('siteCode', row.orgCode)
          ctx.setValueByField('siteId', row.id)
          sessionStorage.setItem('organizationId', row.id)
        }
        getDefaultValue()
      } else if (field == 'untaxedUnitPrice') {
        setTaxedUnitPrice()
      } else if (field == 'taxedUnitPrice') {
        setUnTaxedUnitPrice()
      } else if (field == 'referChannel') {
        if (value == '2' && _isKtFlag == 0) {
          ctx.setOptions('taxedUnitPrice', {
            readonly: true,
            disabled: true
          })
          ctx.setOptions('untaxedUnitPrice', {
            readonly: true,
            disabled: true
          })
        } else {
          ctx.setOptions('taxedUnitPrice', {
            readonly: false,
            disabled: false
          })
          ctx.setOptions('untaxedUnitPrice', {
            readonly: false,
            disabled: false
          })
        }
      }
    })
    .onChange(async (ctx, { field }, event) => {
      const val = event.value === null ? '' : event.value
      if (field === 'referChannel') {
        bus.$emit('changeReferChannel', val)
      } else if (field === 'bidTaxRateName') {
        let taxRateInfo = taxList.find((e) => e.taxItemName == val)
        editInstance.setValueByField('bidTaxRateValue', taxRateInfo?.taxRate)
        editInstance.setValueByField('bidTaxRateCode', taxRateInfo?.taxItemCode)
        setTaxedUnitPrice()
        setUnTaxedUnitPrice()
      } else if (field === 'supplierCode') {
        if (event.itemData?.supplierTenantId) {
          editInstance.setValueByField('supTenantId', event.itemData?.supplierTenantId)
        }
        editInstance.setValueByField('supplierName', event.itemData?.supplierName)
        editInstance.setValueByField('supplierCode', event.itemData?.supplierCode)
        editInstance.setValueByField('supplierId', event.itemData?.supplierId)
        editInstance.setValueByField('partnerCode', event.itemData?.supplierPartnerCode)
        getDefaultValue()
      } else if (field === 'purUnitName') {
        console.error(event, 123)
        editInstance.setValueByField('purUnitCode', event.itemData?.unitCode)
        editInstance.setValueByField('purUnitId', event.itemData?.id)
        setConversionRate()
      } else if (field === 'bidCurrencyName') {
        ctx.setValueByField('bidCurrencyCode', event.itemData?.code)
      }
    })
  const busFields = [
    'bidTaxRateCode',
    'bidTaxRateName',
    'bidTaxRateValue',
    'supplierCode',
    'pointBiddingItemStage',
    'supplierId',
    'supplierName',
    'deliveryPlace',
    'itemName',
    'taxedUnitPrice',
    'untaxedUnitPrice',
    'purUnitName',
    'unitName',
    'unitId',
    'unitCode',
    'categoryName',
    'categoryCode',
    'categoryId',
    'itemCode',
    'categoryResponse'
  ]
  busFields.forEach((busField) => {
    bus.$on(`${busField}Change`, async (data) => {
      if (busField == 'supplierCode') {
        getSupplierList({
          fuzzyNameOrCode: data
          // organizationCode: sessionStorage.getItem("organizationCode"),
        }).then((r) => {
          let arr = addArrTextField(r.data, 'supplierCode', 'supplierName')
          editInstance.setOptions('supplierCode', {
            dataSource: Array.isArray(arr) ? arr : []
          })
        })
        editInstance.setValueByField(busField, data)
      } else if (busField === 'categoryResponse') {
        if (data && typeof data === 'object') {
          editInstance.setValueByField('categoryName', data.categoryName)
          editInstance.setValueByField('categoryCode', data.categoryCode)
          editInstance.setValueByField('categoryId', data.id)
        }
      } else if (busField === 'unitCode') {
        editInstance.setValueByField('unitCode', data)
        editInstance.setValueByField('purUnitCode', data)
        unitNameList.forEach((e) => {
          if (e.unitCode == data) {
            editInstance.setValueByField('purUnitName', e.id)
          }
        })
        setConversionRate()
      } else {
        editInstance.setValueByField(busField, data)
      }
    })
  })
  bus.$on('getDefaultValue', () => {
    getDefaultValue()
  })
  let supplierList = []
  bus.$on('supplierDropdownList', (data) => {
    clearChangeFuc()
    editInstance.setOptions('supplierCode', {
      dataSource: JSON.parse(data)
    })
    supplierList = JSON.parse(data)
  })
  bus.$on('supplierDropdownListNew', (data) => {
    editInstance.setOptions('supplierCode', {
      dataSource: JSON.parse(data)
    })
    supplierList = JSON.parse(data)
  })
  bus.$on('clearChange', () => {
    clearChangeFuc()
  })
  const clearChangeFuc = () => {
    const fieldMap = [
      'itemCode',
      'itemName',
      'itemId',
      'categoryName',
      'categoryCode',
      'categoryId',
      'conversionRate',
      'historyUntaxedUnitPrice',
      'bidCurrencyCode',
      'bidCurrencyName',
      'leadTime',
      'minPackageQuantity',
      'minPurQuantity',
      'purUnitName',
      'purUnitCode',
      'quoteAttribute',
      'quoteMode',
      'bidTaxRateCode',
      'bidTaxRateName',
      'bidTaxRateValue',
      'unconditionalLeadTime',
      'supplierName',
      'supplierCOde',
      'supplierId',
      'unitName',
      'unitCode'
    ]
    fieldMap.forEach((x) => {
      editInstance.setValueByField(x, '')
    })
    editInstance.setOptions('supplierCode', {
      dataSource: []
    })
  }
  const setTaxedUnitPrice = () => {
    let untaxedUnitPrice = editInstance.getValueByField('untaxedUnitPrice')
    let bidTaxRateValue = editInstance.getValueByField('bidTaxRateValue')
    let taxedUnitPrice = new Decimal(untaxedUnitPrice)
      .mul(new Decimal(bidTaxRateValue ? bidTaxRateValue : 0).add(1))
      .toNumber()
      .toFixed(2)
    editInstance.setValueByField('taxedUnitPrice', taxedUnitPrice)
  }
  const setUnTaxedUnitPrice = () => {
    let taxedUnitPrice = editInstance.getValueByField('taxedUnitPrice')
    let bidTaxRateValue = editInstance.getValueByField('bidTaxRateValue')
    let untaxedUnitPrice = new Decimal(taxedUnitPrice)
      .div(new Decimal(bidTaxRateValue ? bidTaxRateValue : 0).add(1))
      .toNumber()
      .toFixed(2)
    editInstance.setValueByField('untaxedUnitPrice', untaxedUnitPrice)
  }
  const getDefaultValue = () => {
    let loading = false
    if (loading) return

    let categoryCode = editInstance.getValueByField('categoryCode')
    let itemCode = editInstance.getValueByField('itemCode')
    let siteCode = editInstance.getValueByField('siteCode')
    let supplierCode = editInstance.getValueByField('supplierCode')
    let partnerCode = editInstance.getValueByField('partnerCode')
    let params = {
      pointId: rfxId,
      reqItemList: [
        {
          categoryCode: categoryCode || '',
          itemCode: itemCode || '',
          siteCode: siteCode || '',
          supplierCode: supplierCode || '',
          partnerCode: partnerCode || ''
        }
      ]
    }
    defaultValue(params).then((res) => {
      loading = true
      setTimeout(() => {
        loading = false
      }, 5000)
      if (res.code == 200) {
        let defaultValueDTO = res.data[0].defaultValueDTO
        console.error(defaultValueDTO.bidHistoryPrice, 'xx123')
        if (defaultValueDTO.bidConversionRate) {
          // 转换率
          editInstance.setValueByField('conversionRate', defaultValueDTO.bidConversionRate)
        }
        if (defaultValueDTO.bidHistoryPrice) {
          // 历史价格
          editInstance.setValueByField('historyUntaxedUnitPrice', defaultValueDTO.bidHistoryPrice)
        }
        if (defaultValueDTO.currencyCode) {
          //币种
          editInstance.setValueByField('bidCurrencyCode', defaultValueDTO.currencyCode)
          let bidCurrencyName =
            allCurrency.find((e) => e.code === defaultValueDTO.currencyCode)?.text ??
            defaultValueDTO.currencyCode
          editInstance.setValueByField('bidCurrencyName', bidCurrencyName)
        }
        if (defaultValueDTO.leadTime) {
          //L/T
          editInstance.setValueByField('leadTime', Number(defaultValueDTO.leadTime))
        }
        if (defaultValueDTO.minPackageQuantity) {
          //最小包装量
          editInstance.setValueByField(
            'minPackageQuantity',
            Number(defaultValueDTO.minPackageQuantity)
          )
        }
        if (defaultValueDTO.minPurchaseQuantity) {
          //最小采购量
          editInstance.setValueByField(
            'minPurQuantity',
            Number(defaultValueDTO.minPurchaseQuantity)
          )
        }
        if (defaultValueDTO.purUnitName) {
          //订单单位
          editInstance.setValueByField('purUnitName', defaultValueDTO.purUnitName)
        }
        if (defaultValueDTO.purUnitCode) {
          //订单单位
          editInstance.setValueByField('purUnitCode', defaultValueDTO.purUnitCode)
          unitNameList.forEach((e) => {
            if (e.unitCode == defaultValueDTO.purUnitCode) {
              editInstance.setValueByField('purUnitName', e.id)
            }
          })

          setConversionRate()
        }
        if (defaultValueDTO.quoteAttribute) {
          //报价属性
          editInstance.setValueByField('quoteAttribute', defaultValueDTO.quoteAttribute)
        }
        if (defaultValueDTO.quoteMode) {
          //报价方式
          editInstance.setValueByField('quoteMode', defaultValueDTO.quoteMode)
        }
        if (defaultValueDTO.taxCode) {
          //税率
          editInstance.setValueByField('bidTaxRateCode', defaultValueDTO.taxCode)
          let taxInfo = taxList.find((e) => e.taxItemCode === defaultValueDTO.taxCode)
          let bidTaxRateName = taxInfo.taxItemName
          let bidTaxRateValue = taxInfo.taxRate
          editInstance.setValueByField('bidTaxRateName', bidTaxRateName)
          editInstance.setValueByField('bidTaxRateValue', bidTaxRateValue)
        }
        // if (defaultValueDTO.taxName) {
        //   //税率
        //   editInstance.setValueByField(
        //     "bidTaxRateName",
        //     defaultValueDTO.taxName
        //   );
        // }
        // if (defaultValueDTO.taxRate) {
        //   //税率
        //   editInstance.setValueByField(
        //     "bidTaxRateValue",
        //     defaultValueDTO.taxRate
        //   );
        // }
        if (defaultValueDTO.unconditionalLeadTime) {
          //无条件L/T
          editInstance.setValueByField(
            'unconditionalLeadTime',
            defaultValueDTO.unconditionalLeadTime
          )
        }
      }
    })
  }
  const setConversionRate = () => {
    let unitCode = editInstance.getValueByField('unitCode')
    let purUnitCode = editInstance.getValueByField('purUnitCode')
    if (unitCode == purUnitCode) {
      editInstance.setValueByField('conversionRate', 1)
    }
  }

  return [
    {
      width: '60',
      type: 'checkbox',
      allowEditing: false,
      showInColumnChooser: false
    },
    {
      width: '150',
      field: 'addId', // 隐藏的主键，获取到数据源时，需要把id赋值给它
      headerText: i18n.t('addId主键'),
      visible: false,
      isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
      isPrimaryKey: true, // 一定要有主键，不然已有的行会被修改掉。。
      allowEditing: false
    },
    {
      field: 'siteName',
      headerText: i18n.t('工厂'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          dataSource: siteNameDataSource,
          fields: { value: 'orgName', text: 'orgName' },
          placeholder: i18n.t('请选择工厂'),
          disabled: true,
          readonly: true
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return siteNameDataSource.find((e) => e.orgName === cellVal)?.orgName ?? cellVal
      }
    },
    {
      field: 'siteCode',
      headerText: i18n.t('工厂编码'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'siteId',
      headerText: i18n.t('工厂Id'),
      width: 1,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'deliveryPlace',
      headerText: i18n.t('直送地'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          dataSource: directDelivery,
          placeholder: i18n.t('直送地')
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return directDelivery.find((e) => e.text === cellVal)?.text ?? cellVal
      }
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      width: 200,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'checkSelectedItemCode',
          field: 'itemCode',
          rfxId: rfxId,
          sourcingType: sourcingType,
          siteParam,
          priceClassification
        })
      })
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料名称'),
      width: 200,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'itemId',
      headerText: i18n.t('物料Id'),
      width: 1,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'categoryName',
      headerText: i18n.t('品类名称'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'categoryId',
      headerText: i18n.t('品类ID'),
      width: 1,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'categoryCode',
      headerText: i18n.t('品类编码'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      width: 350,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          dataSource: supplierList,
          fields: makeTextFields('supplierCode'),
          placeholder: i18n.t('供应商'),
          'allow-filtering': true,
          filtering: useFiltering(function (e) {
            if (typeof e.text === 'string' && e.text) {
              e.updateData(supplierList.filter((f) => f?.__text.indexOf(e.text) > -1))
            } else {
              e.updateData(supplierList)
            }
          })
        })
      })
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'supplierId',
      headerText: i18n.t('供应商ID'),
      width: 1,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'supTenantId',
      headerText: i18n.t('供应商租户ID'),
      width: 1,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'taxedUnitPrice',
      headerText: i18n.t('单价(含税)'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          readonly: true,
          disabled: true,
          min: 0
        })
      })
    },
    {
      field: 'untaxedUnitPrice',
      headerText: i18n.t('单价(未税)'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          min: 0
        })
      })
    },
    {
      field: 'priceUnitName',
      headerText: i18n.t('价格单位'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text'
        })
      })
    },
    {
      field: 'historyUntaxedUnitPrice',
      headerText: i18n.t('历史价格'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'quoteMode',
      headerText: i18n.t('价格生效方式'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          dataSource: quoteModeList,
          placeholder: i18n.t('请选择价格生效方式')
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return quoteModeList.find((e) => e.value === cellVal)?.text ?? cellVal
      }
    },
    {
      field: 'quoteAttribute',
      headerText: i18n.t('报价属性'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          dataSource: quoteAttributeList,
          placeholder: i18n.t('请选择报价属性')
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return quoteAttributeList.find((e) => e.value === cellVal)?.text ?? cellVal
      }
    },

    {
      field: 'bidCurrencyName',
      headerText: i18n.t('币种'),
      width: 250,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          dataSource: allCurrency,
          placeholder: i18n.t('请选择币种'),
          'allow-filtering': true,
          filtering: useFiltering(function (e) {
            if (typeof e.text === 'string' && e.text) {
              e.updateData(allCurrency.filter((f) => f?.text.indexOf(e.text) > -1))
            } else {
              e.updateData(allCurrency)
            }
          })
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return allCurrency.find((e) => e.value === cellVal)?.text ?? cellVal
      }
    },
    {
      field: 'bidCurrencyCode',
      headerText: i18n.t('币种编码'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'bidTaxRateName',
      headerText: i18n.t('税率名称'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          dataSource: taxList,
          placeholder: i18n.t('请选择税率'),
          'allow-filtering': true,
          fields: makeTextFields('taxItemName'),
          filtering: useFiltering(function (e) {
            if (typeof e.text === 'string' && e.text) {
              e.updateData(this.dataSource.filter((f) => f?.__text.indexOf(e.text) > -1))
            } else {
              e.updateData(this.dataSource)
            }
          })
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return taxList.find((e) => e.taxItemCode === cellVal)?.taxItemName ?? cellVal
      }
    },
    {
      field: 'bidTaxRateCode',
      headerText: i18n.t('税率编码'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'bidTaxRateValue',
      headerText: i18n.t('税率值'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'unitName',
      headerText: i18n.t('基本单位'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'unitCode',
      headerText: i18n.t('基本单位编码'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'unitId',
      headerText: i18n.t('单位ID'),
      width: 1,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    // {
    //   field: "purUnitName",
    //   headerText: i18n.t("订单单位"),
    //   edit: editInstance.create({
    //     getEditConfig: () => ({
    //       type: "mt-select",
    //       dataSource: unitNameList,
    //       placeholder: i18n.t("请选择订单单位"),
    //       "allow-filtering": true,
    //       // fields: { text: "unitName", value: "unitName" },
    //       fields: makeTextFields("unitName"),
    //       filtering: useFiltering(function (e) {
    //         if (typeof e.text === "string" && e.text) {
    //           e.updateData(
    //             this.dataSource.filter((f) => f?.unitName.indexOf(e.text) > -1)
    //           );
    //           console.error(
    //             this.dataSource.filter((f) => f?.unitName.indexOf(e.text) > -1),
    //             123
    //           );
    //         } else {
    //           e.updateData(this.dataSource);
    //         }
    //       }),
    //     }),
    //   }),
    //   formatter: ({ field }, item) => {
    //     const cellVal = getValueByPath(item, field);
    //     return (
    //       unitNameList.find((e) => e.unitName === cellVal)?.unitName ?? cellVal
    //     );
    //   },
    // },
    {
      field: 'purUnitName',
      headerText: i18n.t('订单单位'),
      edit: editInstance.create({
        valueConvert: (val, { options, column }) => {
          const dataSource = options.dataSource || []
          const row = dataSource.find((e) => e.id === val)
          const purUnitName = row?.unitName || ''
          editInstance.setValueByField(column.field, purUnitName)
          return purUnitName
        },
        getEditConfig: ({ rowData }) => ({
          type: 'select',
          'show-clear-button': true,
          fields: makeTextFields('id'),
          dataSource: unitNameList,
          placeholder: rowData.purUnitName,
          'allow-filtering': true,
          filtering: useFiltering(function (e) {
            pagedQueryUnit({
              condition: 'or',
              page: { current: 1, size: 20 },
              rules: [
                {
                  field: 'unitName',
                  type: 'string',
                  operator: 'contains',
                  value: e.text
                },
                {
                  field: 'unitCode',
                  type: 'string',
                  operator: 'contains',
                  value: e.text
                }
              ]
            }).then((res) => {
              if (Array.isArray(res?.data?.records)) {
                const records = addArrTextField(res.data.records, 'unitCode', 'unitName')
                mergeDataSource(editInstance, 'purUnitName', records)
                e.updateData(records)
              }
            })
          }),
          created: () => {
            let row
            if (rowData.purUnitCode) {
              row = unitNameList.find((e) => e.unitCode === rowData.purUnitCode)
            } else if (rowData.purUnitName) {
              row = unitNameList.find((e) => e.unitName === rowData.purUnitName)
            }
            if (row) {
              setTimeout(() => {
                editInstance.setValueByField('purUnitName', row.id)
              }, 0)
            }
          }
        })
      })
    },
    {
      field: 'purUnitCode',
      headerText: i18n.t('订单单位编码'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'conversionRate',
      headerText: i18n.t('转换率'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          min: 0
        })
      })
    },
    {
      field: 'minPurQuantity',
      headerText: i18n.t('最小采购量/MOQ'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          min: 0
        })
      })
    },
    {
      field: 'minPackageQuantity',
      headerText: i18n.t('最小包装量/MPQ'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          min: 0
        })
      })
    },

    {
      field: 'leadTime',
      headerText: i18n.t('L/T'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          min: 0
        })
      })
    },
    {
      field: 'unconditionalLeadTime',
      headerText: i18n.t('无条件L/T'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          min: 0
        })
      })
    },
    {
      field: 'effectiveStartDate',
      headerText: i18n.t('生效日期'),
      formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'date',
          format: 'yyyy-MM-dd',
          'time-stamp': true,
          // readonly: getReadOnly(),
          placeholder: i18n.t('请输入生效日期')
        })
      })
      // format: "yyyy-MM-dd HH:mm:ss",
      // type: "date",
    },
    {
      field: 'effectiveEndDate',
      headerText: i18n.t('失效日期'),
      formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'date',
          format: 'yyyy-MM-dd',
          'time-stamp': true,
          // readonly: getReadOnly(),
          placeholder: i18n.t('请输入失效日期')
        })
      })
      // format: "yyyy-MM-dd HH:mm:ss",
      // type: "date",
    },
    {
      field: 'supplierResponse',
      headerText: i18n.t('供方意见'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'supplierConfirmStatus',
      headerText: i18n.t('供应商确认状态'),
      visible: priceClassification == 4,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'select',
          dataSource: statusData,
          disabled: true,
          readonly: true
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return statusData.find((e) => e.value == cellVal)?.text ?? cellVal
      }
    },
    {
      field: 'priceChangeType',
      headerText: i18n.t('涨/跌'),
      ignore: true,
      visible:
        priceClassification == 4 && (sourcingType == 'second_inquiry' || sourcingType == 'exist'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'select',
          dataSource: priceChangeType,
          disabled: true,
          readonly: true
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return priceChangeType.find((e) => e.value == cellVal)?.text ?? cellVal
      }
    }
  ]
}

export const columnDataTwo = ({ siteNameDataSource = [] } = {}) => {
  const editInstance = createEditInstance()
  return [
    {
      width: '60',
      type: 'checkbox',
      allowEditing: false
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商'),
      allowEditing: false
    },
    {
      field: 'siteName',
      headerText: i18n.t('工厂'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          dataSource: siteNameDataSource,
          fields: { value: 'siteName', text: 'siteName' },
          placeholder: i18n.t('请选择工厂'),
          disabled: true,
          readonly: true
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return siteNameDataSource.find((e) => e.supplierName === cellVal)?.text ?? cellVal
      }
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      width: 200,
      allowEditing: false
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料名称'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
      // valueConverter: {
      //   type: "map",
      //   map: { 0: i18n.t("评分中"), 1: i18n.t("已提交"), 2: i18n.t("评分汇总") },
      // },
    },
    {
      field: 'minSplitQuantity',
      headerText: i18n.t('最小起拆点'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          min: 0
        })
      })
    },
    {
      field: 'allocationRatio',
      headerText: i18n.t('配额比例（%）'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          min: 0
        })
      }),
      formatter: ({ field }, item) => {
        let val = item[field] || 0
        return val + '%'
      }
    },
    {
      field: 'taxedUnitPrice',
      headerText: i18n.t('单价(含税)'),
      allowEditing: false
    },
    {
      field: 'untaxedUnitPrice',
      headerText: i18n.t('单价(不含税)'),
      allowEditing: false
    },
    {
      field: 'minPackageQuantity',
      headerText: i18n.t('最小包装量/MPQ'),
      allowEditing: false
    },
    {
      field: 'unitName',
      headerText: i18n.t('基本单位'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    // {
    //   field: 'purUnitName',
    //   headerText: i18n.t('订单单位'),
    //   allowEditing: false,
    //   editTemplate: () => {
    //     return {
    //       template: cellChanged
    //     }
    //   }
    // },
    {
      field: 'minPurQuantity',
      headerText: i18n.t('最小采购量/MOQ'),
      allowEditing: false
    },
    {
      field: 'quotaEffectiveStartDate',
      headerText: i18n.t('生效日期'),
      formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'date',
          format: 'yyyy-MM-dd',
          disabled: true,
          readonly: true,
          placeholder: i18n.t('请输入生效日期')
        })
      }),
      format: 'yyyy-MM-dd HH:mm:ss',
      type: 'date'
    },
    {
      field: 'quotaEffectiveEndDate',
      headerText: i18n.t('失效日期'),
      formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'date',
          format: 'yyyy-MM-dd',
          disabled: true,
          readonly: true,
          placeholder: i18n.t('请输入失效日期')
        })
      }),
      format: 'yyyy-MM-dd HH:mm:ss',
      type: 'date'
    },
    {
      field: 'priceUnitName',
      headerText: i18n.t('价格单位'),
      allowEditing: false
    },
    {
      field: 'sourceType',
      headerText: i18n.t('价格来源'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          dataSource: [
            { status: 0, label: i18n.t('内部'), cssClass: '' },
            { status: 1, label: i18n.t('外部'), cssClass: '' }
          ],
          fields: { value: 'status', text: 'label' },
          disabled: true,
          readonly: true
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        let arr = [
          { status: 0, label: i18n.t('内部'), cssClass: '' },
          { status: 1, label: i18n.t('外部'), cssClass: '' }
        ]
        return arr.find((e) => e.status === cellVal)?.label ?? cellVal
      }
    },
    {
      field: 'quoteAttribute',
      headerText: i18n.t('报价属性'),
      allowEditing: false,
      valueConverter: {
        type: 'map',
        map: {
          mailing_price: i18n.t('寄售价'),
          standard_price: i18n.t('标准价'),
          K: i18n.t('寄售价'),
          '': i18n.t('标准价'),
          outsource: i18n.t('委外价')
        }
      }
    }
  ]
}
export const toolbar = (enabledAdd = false) => [
  {
    id: 'Add',
    icon: 'icon_solid_Createorder',
    title: i18n.t('新增'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'batchAdd',
    icon: 'icon_solid_Createorder',
    title: i18n.t('批量添加'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'Save',
    icon: 'icon_solid_Save',
    title: i18n.t('保存'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'del',
    icon: 'icon_solid_Cancel',
    title: i18n.t('删除'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'upload',
    icon: 'icon_solid_Createproject',
    title: i18n.t('导入'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'Export_a',
    icon: 'icon_solid_Createorder',
    title: i18n.t('导出')
  }
]
export const toolbarTwo = (enabledAdd = false, status, WhetherOpenOA) => [
  {
    id: 'SaveTwo',
    icon: 'icon_solid_Save',
    title: i18n.t('保存'),
    visibleCondition: () => enabledAdd
  },
  {
    id: 'redirectOA',
    icon: 'icon_solid_Save',
    title: i18n.t('提交OA审批'),
    visibleCondition: () => status
  },
  {
    id: 'progressOA',
    icon: 'icon_solid_Save',
    title: i18n.t('OA审批进度'),
    visibleCondition: () => WhetherOpenOA
  },
  {
    id: 'Export_b',
    icon: 'icon_solid_Createorder',
    title: i18n.t('导出')
  },
  {
    id: 'upload2',
    icon: 'icon_solid_Createproject',
    title: i18n.t('导入'),
    visibleCondition: () => enabledAdd
  }
]
export const pageConfig = (
  url,
  id,
  serializeList,
  recordDoubleClick,
  url2,
  rowDataBound,
  afterAsyncData
) => [
  {
    title: i18n.t('定价物料'),
    useToolTemplate: false,
    toolbar: toolbar(),
    grid: {
      allowFiltering: false,
      lineIndex: true,
      columnData: [],
      dataSource: [],
      asyncConfig: {
        url,
        defaultRules: [
          {
            label: i18n.t('定点推荐id'),
            field: 'point_id',
            type: 'string',
            operator: 'equal',
            value: id
          }
        ],
        afterAsyncData
      },
      recordDoubleClick,
      editSettings: {
        allowAdding: true,
        allowEditing: true,
        allowDeleting: true,
        mode: 'Normal',
        allowEditOnDblClick: true,
        showConfirmDialog: false,
        showDeleteConfirmDialog: false,
        newRowPosition: 'Top'
      }
    }
  },
  {
    title: i18n.t('配额分配'),
    useToolTemplate: false,
    toolbar: toolbarTwo(),
    grid: {
      allowFiltering: false,
      lineIndex: true,
      columnData: [],
      // dataSource: [],
      rowDataBound,
      asyncConfig: {
        url: url2,
        defaultRules: [
          {
            label: i18n.t('定点推荐id'),
            field: 'pointId',
            type: 'string',
            operator: 'equal',
            value: id
          },
          {
            label: i18n.t('定点推荐type'),
            field: 'sourceDocType',
            type: 'string',
            operator: 'equal',
            value: 'white_point'
          }
        ],
        serializeList,
        afterAsyncData
      },
      recordDoubleClick,
      editSettings: {
        allowAdding: true,
        allowEditing: true,
        allowDeleting: true,
        mode: 'Normal',
        allowEditOnDblClick: true,
        showConfirmDialog: false,
        showDeleteConfirmDialog: false,
        newRowPosition: 'Top'
      }
    }
  },
  {
    title: i18n.t('附件') //该项配置，只配置了标题，实际是slot="slot-1"中的内容
  }
]
