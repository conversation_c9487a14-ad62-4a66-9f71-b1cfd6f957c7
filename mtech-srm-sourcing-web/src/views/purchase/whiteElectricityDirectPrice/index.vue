<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { todoListToolBar, todoListColumnData } from './config'

export default {
  data() {
    return {
      pageConfig: [
        {
          toolbar: todoListToolBar,
          useToolTemplate: false,
          gridId: this.$permission.gridId['purchase']['whiteElectricityDirectPrice'],
          grid: {
            allowFiltering: true,
            asyncConfig: {
              url: this.$API.whitePoint.queryHeaderByPage,
              afterAsyncData: this.updateTabTitle
            },
            lineIndex: true,
            columnData: todoListColumnData,
            dataSource: []
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    updateTabTitle() {},

    //新增
    handleAddFn() {
      this.$dialog({
        modal: () => import('./newAddComponents/index.vue'),
        data: {
          title: this.$t('创建定价')
        },
        success: (data) => {
          // this.$refs.templateRef.refreshCurrentGridData()
          this.$router.push({
            name: 'direct-price',
            query: {
              id: data.id,
              decidePriceType: 0,
              status: data.status,
              pointNo: data.pointNo
            }
          })
        }
      })
    },

    //删除
    handleDelFn(selectGridRecords, idList) {
      if (selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            let params = {
              idList: idList
            }
            this.$API.whitePoint.deleteHeader(params).then(() => {
              this.$toast({ content: this.$t('删除成功'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
            })
          }
        })
      }
    },

    //提交
    handleSubmitFn(selectGridRecords) {
      if (selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else if (selectGridRecords.length > 1) {
        this.$toast({ content: this.$t('只选择一行提交'), type: 'warning' })
      } else {
        let params = {
          id: selectGridRecords[0].id
        }
        this.$API.whitePoint.sendToSupplier(params).then(() => {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        })
      }
    },

    //表格按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      let idList = []
      _selectGridRecords.map((item) => {
        idList.push(item.id)
      })
      if (e.toolbar.id == 'Add') {
        this.handleAddFn() //新增
      } else if (e.toolbar.id == 'del') {
        this.handleDelFn(_selectGridRecords, idList) //删除
      } else if (e.toolbar.id == 'close') {
        this.handleClose(idList, _selectGridRecords)
      } else {
        this.handleSubmitFn(_selectGridRecords) //提交
      }
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
    },
    //单元格标题点击操作
    handleClickCellTitle(e) {
      const { field, data } = e
      if (field == 'pointNo') {
        if (['13', 13].includes(data.status)) {
          this.$router.push({
            name: 'direct-price-detail',
            query: {
              id: e.data.id,
              decidePriceType: 0,
              status: e.data.status,
              pointNo: e.data.pointNo,
              isView: true
            }
          })
          return
        }
        this.$router.push({
          name: 'direct-price',
          query: {
            id: e.data.id,
            decidePriceType: 0,
            status: e.data.status,
            pointNo: e.data.pointNo
          }
        })
      }
    },
    handleClose(idlist = [], records) {
      if (idlist.length <= 0) {
        this.$toast({
          type: 'warning',
          content: this.$t('请先选择一行数据')
        })
        return
      }
      for (let key = 0; key < records.length; key++) {
        if (!['5', 5, '7', 7].includes(records[key].status)) {
          this.$toast({
            type: 'warning',
            content: this.$t('只能关闭状态为 "供应商已驳回" 或者 "审批拒绝" 的数据')
          })
          return
        }
      }
      this.$store.commit('startLoading')
      this.$API.whitePoint
        .directPricing({ idList: idlist })
        .then((res) => {
          if (res && res.code === 200) {
            this.$toast({
              type: 'success',
              content: this.$t('操作成功')
            })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    }
  }
}
</script>
<style scoped lang="scss">
/deep/ .field-content1 {
  color: rgb(63, 81, 181);
  cursor: pointer;
}
</style>
