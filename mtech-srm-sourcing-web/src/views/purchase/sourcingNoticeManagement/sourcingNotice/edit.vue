<template>
  <div class="full-height pt20 vertical-flex-box">
    <div class="top">
      <!-- 右侧各种操作按钮 -->
      <mt-button css-class="e-flat invite-btn" :is-primary="true" @click="goBack">{{
        $t('返回')
      }}</mt-button>
      <mt-button css-class="e-flat" v-if="isEdit" :is-primary="true" @click="handleSave">{{
        $t('保存')
      }}</mt-button>
      <mt-button css-class="e-flat" v-if="isEdit" :is-primary="true" @click="handleRelease">{{
        $t('发布')
      }}</mt-button>
    </div>

    <div class="notice-add-form">
      <mt-form ref="ruleForm" :model="noticeForm" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
          <mt-select
            v-model="noticeForm.companyCode"
            :data-source="companyList"
            :fields="{ text: 'text', value: 'orgCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择公司')"
            @change="handleCompanyChange"
            :disabled="!isEdit"
          />
        </mt-form-item>
        <mt-form-item prop="announceType" :label="$t('公告类型')" label-style="top">
          <mt-select
            v-model="noticeForm.announceType"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :data-source="announceTypeList"
            :show-clear-button="true"
            :placeholder="$t('请选择公告类型')"
            :disabled="!isEdit"
          />
        </mt-form-item>
        <mt-form-item prop="announceName" :label="$t('公告名称')">
          <mt-input
            v-model="noticeForm.announceName"
            maxlength="256"
            :disabled="!isEdit"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="place" :label="$t('需求地点')">
          <mt-input v-model="noticeForm.place" :disabled="!isEdit"></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('截止时间')" prop="bidDeadlineTime">
          <mt-date-picker
            v-model="noticeForm.bidDeadlineTime"
            :show-clear-button="true"
            :disabled="!isEdit"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item
          prop="announceContent"
          class="full-width notice-content"
          :label="$t('公告内容')"
        >
          <rich-text-editor
            class="rich-text"
            ref="MtRichTextEditor"
            v-model="noticeForm.announceContent"
            :readonly="!isEdit"
          />
        </mt-form-item>
      </mt-form>
      <div>
        <p
          style="cursor: pointer; color: rgba(0, 0, 0, 0.87); font-size: 14px; font-weight: 600"
          @click="handleUpload()"
        >
          {{
            noticeForm.fileList.length
              ? `${$t('上传附件' + ', ' + $t('文件数'))}: ${noticeForm.fileList.length},${$t(
                  '点此操操作'
                )}`
              : `${$t('上传附件')}, ${$t('点此操操作')}`
          }}
        </p>
        <mt-common-uploader
          ref="uploader"
          class="common-uploader"
          :save-url="saveUrl"
          :download-url="downloadUrl"
          :is-single-file="false"
          type="line"
          v-model="noticeForm.fileList"
        ></mt-common-uploader>
      </div>
    </div>
  </div>
</template>

<script>
import { formateTime } from '@/utils/utils'
import RichTextEditor from '@/components/RichTextEditor/RichTextEditor'
import { cloneDeep } from 'lodash'
import MtCommonUploader from '@/components/mtech-common-uploader'
export default {
  components: {
    RichTextEditor,
    MtCommonUploader
  },
  data() {
    return {
      announceTypeList: [
        // { text: '寻源公告', value: 1 },
        // { text: '招标公共', value: 2 },
        // { text: '中标公示', value: 3 },
        // { text: '其它', value: 4 }
      ],
      noticeForm: {
        fileList: []
      },
      rules: {
        companyCode: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        announceType: [{ required: true, message: this.$t('请选择公告类型'), trigger: 'blur' }],
        announceName: [{ required: true, message: this.$t('请填写公告名称'), trigger: 'blur' }],
        // place: [{ required: true, message: this.$t('请填写需求地点'), trigger: 'blur' }],
        bidDeadlineTime: [{ required: true, message: this.$t('请填写截止时间'), trigger: 'blur' }],
        announceContent: [{ required: true, message: this.$t('请填写公告内容'), trigger: 'blur' }]
      },
      companyList: [],
      saveUrl: '/api/file/user/file/uploadPublic?useType=1', // 文件上传路径待
      downloadUrl: '/api/file/user/file/downloadPublicFile', //文件下载
      status: 0
    }
  },
  computed: {
    tenantId() {
      return this.$store.state.user.tenantId
    },
    isEdit() {
      return this.status === 0 || this.status === 2
    }
  },
  watch: {
    isEdit(newVal) {
      this.$refs.MtRichTextEditor.setReadOnly(!newVal)
    }
  },
  async mounted() {
    await this.getAnnounceTypeList()
    await this.getCompanyList()
    await this.getPageData()
  },
  methods: {
    // 获取公司下拉列表
    async getCompanyList() {
      const res = await this.$API.masterData.permissionCompanyList()
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.companyList = res.data || []
      }
    },
    // 获取公告类型下拉数据
    async getAnnounceTypeList() {
      const res = await this.$API.masterData.dictionaryGetList({
        dictCode: 'PORTAL_ANNOUNCE_TYPE'
      })
      this.announceTypeList = res.data
    },
    // 获取页面数据
    async getPageData() {
      if (!this.$route.query.id) return
      const res = await this.$API.notice.queryAnnounce({ id: this.$route.query.id })
      if (res.code === 200) {
        const {
          companyCode,
          companyName,
          announceType,
          announceCode,
          announceName,
          place,
          bidDeadlineTime,
          announceContent,
          fileList,
          status
        } = { ...res.data }
        this.noticeForm = {
          companyCode,
          companyName,
          announceType: String(announceType),
          announceCode,
          announceName,
          place,
          bidDeadlineTime,
          announceContent,
          fileList
        }
        this.status = status
      }
    },
    handleCompanyChange(value) {
      this.noticeForm.companyName = value.itemData?.orgName
    },
    handleUpload() {
      if (!this.isEdit) return
      this.$refs.uploader.$children[1].showFileBaseInfo()
    },

    goBack() {
      this.$router.push({
        path: '/sourcing/sourcing-notice'
      })
      this.noticeForm = Object.assign({}, this.noticeForm, this.copyNoticeForm)
    },
    // 保存
    handleSave(type) {
      this.$refs.ruleForm.validate((flag) => {
        if (!flag) return
        const params = {
          ...this.noticeForm,
          // fileList: this.transDataId(this.noticeForm.fileList),
          fileList: this.noticeForm.fileList,
          id: this.$route.query?.id
        }
        this.$API.notice.noticeSave(params).then((res) => {
          if (res.code === 200) {
            if (type === 'release') {
              this.release(res.data)
            } else {
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              this.$router.push({
                path: this.$route.path,
                query: {
                  ...this.$route.query,
                  id: res.data,
                  timeStamp: new Date().getTime()
                }
              })
            }
          }
        })
      })
    },
    // 发布
    handleRelease() {
      this.handleSave('release')
    },
    // 发布请求
    release(id) {
      this.$API.notice.noticePublish({ ids: [id] }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('发布成功'), type: 'success' })
          this.goBack()
        }
      })
    },
    // 获取页面公告信息
    getNoticeData() {
      const {
        title,
        noticeType,
        targetSystem,
        supplierStatus,
        body,
        startTime,
        endTime,
        noticeExtList
      } = this.noticeForm
      const fileList = this.transDataId(cloneDeep(this.noticeForm.fileList), 'file')
      const supplierList = this.transDataId(cloneDeep(this.noticeForm.supplierList), 'supplier')
      const baseInfo = {
        title,
        noticeType,
        targetSystem,
        body,
        fileList,
        startTime: `${formateTime(new Date(startTime), 'yyyy-MM-dd')} 00:00:00`,
        endTime: `${formateTime(new Date(endTime), 'yyyy-MM-dd')} 23:59:59`,
        noticeExtList:
          noticeType === 'ITEM_NOTICE' && this.hasSupplier && noticeExtList.length
            ? this.noticeExtList
            : []
      }
      if (this.isEdit) {
        Object.assign(baseInfo, {
          id: this.otherNoticeForm.id,
          noticeCode: this.otherNoticeForm.noticeCode
        })
      }

      if (noticeType === 'ITEM_NOTICE' && this.hasSupplier && this.radioType === '0') {
        return {
          ...baseInfo,
          supplierStatus,
          supplierList: null
        }
      }
      if (noticeType === 'ITEM_NOTICE' && this.hasSupplier && this.radioType === '1') {
        return {
          ...baseInfo,
          supplierList: supplierList,
          supplierStatus: null
        }
      }
      return baseInfo
    },
    // 初始化渲染编辑页面数据
    renderEditData(id) {
      this.$api.messageCenter
        .getEditNotice(id ? id : this.$route.query?.id)
        .then((res) => {
          if (res.code === 200) {
            const {
              title,
              noticeType,
              targetSystem,
              supplierStatus,
              supplierList,
              body,
              fileList,
              id,
              noticeCode,
              startTime,
              endTime,
              noticeExtList
            } = res.data

            this.noticeForm = Object.assign({}, this.noticeForm, {
              title,
              noticeType,
              targetSystem,
              supplierStatus,
              supplierList,
              body,
              fileList: fileList || [],
              startTime: new Date(startTime) || null,
              endTime: new Date(endTime) || null,
              noticeExtList: noticeExtList?.map((i) => i.companyCode) || []
            })
            this.noticeExtList = noticeExtList
            this.otherNoticeForm.id = id
            this.otherNoticeForm.noticeCode = noticeCode

            this.supplierData = supplierList
            if (supplierList?.length > 0) {
              this.radioType = '1'
            }
            this.isSaving = false
          }
        })
        .catch(() => {
          this.isSaving = false
        })
    },
    // 处理noticeFileList数据 上传附件的id数据 type : supplier | file
    transDataId(data) {
      if (!data || !Array.isArray(data)) return
      return data.map((item) => {
        const _id = item.id
        delete item.id
        return {
          fileId: _id,
          ...item
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.top {
  width: 100%;
  text-align: right;
  margin-bottom: 16px;
}
.bottom-tables {
  height: 100%;
}
.notice-add-form {
  /deep/ .mt-form-item {
    width: calc(25% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;

    &.full-width {
      width: calc(100% - 20px) !important;
    }
    &.notice-content {
      height: calc(100vh - 365px);
    }
    .mt-form-item-topLabel .label {
      margin-bottom: 4px;
      margin-top: 4px;
    }
    &.notice-content .mt-form-item-topLabel .label {
      margin-bottom: 8px;
    }
  }
}
/deep/ .tox-tinymce {
  height: calc(100vh - 400px) !important;
}
.common-uploader {
  /deep/ .cell-operable-title {
    display: none;
  }
}
.supplier-list > div {
  margin-right: 10px;
  color: rgba(0, 0, 0, 0.87);
  font-size: 14px;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
}
.supplier-item {
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 4px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 30px;
  margin-bottom: 16px;
  /deep/ .mt-form-item {
    width: calc(28% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;
  }
}

.search-btn {
  display: inline-block;
  vertical-align: bottom;
  margin-bottom: 20px;
  margin-left: 50px;
}

// .rich-text {
//   height: calc(100% -500px);
// }
</style>
