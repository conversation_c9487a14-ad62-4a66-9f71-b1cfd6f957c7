<!-- 消息接收配置 -->
<template>
  <div class="notice-handler">
    <mt-local-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="announceCode" :label="$t('公告编号')" label-style="top">
              <mt-input
                v-model="searchFormModel.announceCode"
                :show-clear-button="true"
                :placeholder="$t('请输入公告编号')"
              />
            </mt-form-item>
            <mt-form-item prop="announceName" :label="$t('公告名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.announceName"
                :show-clear-button="true"
                :placeholder="$t('请输入公告名称')"
              />
            </mt-form-item>
            <mt-form-item prop="announceType" :label="$t('公告类型')" label-style="top">
              <mt-select
                v-model="searchFormModel.announceType"
                :data-source="announceTypeList"
                :show-clear-button="true"
                :placeholder="$t('请选择公告类型')"
              />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
              <mt-select
                v-model="searchFormModel.companyCode"
                :data-source="companyList"
                :fields="{ text: 'text', value: 'orgCode' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择公司')"
              />
            </mt-form-item>

            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-select
                v-model="searchFormModel.status"
                :data-source="statusList"
                :show-clear-button="true"
                :placeholder="$t('请选择状态')"
              />
            </mt-form-item>
            <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
              <mt-input
                v-model="searchFormModel.createUserName"
                :show-clear-button="true"
                :placeholder="$t('请输入创建人')"
              />
            </mt-form-item>
            <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.createTime"
                :open-on-focus="true"
                :allow-edit="false"
                format="yyyy-MM-dd"
                :placeholder="$t('请选择创建时间')"
                @change="(e) => handleDateRangeChange('createTime', e)"
              />
            </mt-form-item>
            <mt-form-item prop="publicTime" :label="$t('发布时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.publicTime"
                :open-on-focus="true"
                :allow-edit="false"
                format="yyyy-MM-dd"
                :placeholder="$t('请选择发布时间')"
                @change="(e) => handleDateRangeChange('publicTime', e)"
              />
            </mt-form-item>
            <mt-form-item prop="bidDeadlineTime" :label="$t('截至时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.bidDeadlineTime"
                :open-on-focus="true"
                :allow-edit="false"
                format="yyyy-MM-dd"
                :placeholder="$t('请选择截至时间')"
                @change="(e) => handleDateRangeChange('bidDeadlineTime', e)"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-local-template-page>
  </div>
</template>
<script>
import MtLocalTemplatePage from '@/components/template-page'
import { pageConfig, statusList } from './config'
export default {
  components: {
    MtLocalTemplatePage
  },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      announceTypeList: [
        { text: '寻源公告', value: 1 },
        { text: '招标公共', value: 2 },
        { text: '中标公示', value: 3 },
        { text: '其它', value: 4 }
      ],
      companyList: [],
      statusList,
      searchFormModel: {},
      pageConfig
    }
  },
  mounted() {
    this.getCompanyList()
  },
  methods: {
    // 获取公司下拉列表
    async getCompanyList() {
      const res = await this.$API.masterData.permissionCompanyList()
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.companyList = res.data || []
      }
    },
    // 日期范围格式化
    handleDateRangeChange(key, e) {
      const { startDate, endDate } = e
      this.searchFormModel[key + 'From'] = startDate ? startDate.valueOf() : null
      this.searchFormModel[key + 'To'] = endDate ? endDate.valueOf() + 86400000 : null
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.tempForm = {}
    },

    // 表头操作
    handleClickToolBar(e) {
      const _selectRows = e.grid.getSelectedRecords()
      if (
        _selectRows.length <= 0 &&
        (e.toolbar.id === 'Delete' || e.toolbar.id === 'Revoke' || e.toolbar.id === 'Publish')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id === 'Delete') {
        this.handleDelete(_selectRows)
      } else if (e.toolbar.id === 'Revoke') {
        this.handleRevoke(_selectRows)
      } else if (e.toolbar.id === 'Publish') {
        this.handlePublish(_selectRows)
      }
    },
    // 新增
    handleAdd() {
      this.$router.push({
        path: '/sourcing/sourcing-notice-edit',
        query: {
          timeStamp: new Date().getTime()
        }
      })
    },
    // 发布
    handlePublish(_selectRows) {
      const ids = _selectRows.map((item) => item.id)
      const isNotDraft = _selectRows.find((item) => item.status !== 0 && item.status !== 2)
      if (isNotDraft) {
        this.$toast({ content: this.$t('只能发布草稿或者已撤回状态数据'), type: 'warning' })
        return
      }
      this.$API.notice.noticePublish({ ids }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('发布成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 撤回
    handleRevoke(_selectRows) {
      const ids = _selectRows.map((item) => item.id)
      const isNotPublish = _selectRows.find((item) => item.status !== 1)
      if (isNotPublish) {
        this.$toast({ content: this.$t('只能撤回已发布数据'), type: 'warning' })
        return
      }
      this.$API.notice.noticeRevoke({ ids }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('撤回成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    // 删除
    handleDelete(_selectRows) {
      const ids = _selectRows.map((item) => item.id)
      const isNotDraft = _selectRows.find((item) => item.status !== 0)
      if (isNotDraft) {
        this.$toast({ content: this.$t('只能删除草稿状态数据'), type: 'warning' })
        return
      }
      this.$API.notice.noticeDel({ ids }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    // 跳转详情页
    handleClickCellTitle(e) {
      const { data } = e
      // 自动触发公告直接跳转对应连接
      if (data.detailLink) {
        window.open(data.detailLink, '_blank')
        return
      }
      this.$router.push({
        path: '/sourcing/sourcing-notice-edit',
        query: {
          id: data.id,
          timeStamp: new Date().getTime()
        }
      })
    }
  }
}
</script>
<style>
.notice-handler .mt-select-index {
  display: inline-block;
}
</style>
<style lang="scss" scope>
.hander {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .template-height {
    flex: 1;
    .sendChannel {
      display: inline-block;
      padding: 0 5px;
      height: 20px;
      background: #eff2f8;
      border-radius: 2px;
      text-align: center;
      line-height: 20px;
      vertical-align: middle;
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      margin: 0 10px;
    }
  }
  .e-content {
    height: calc(100vh - 230px) !important;
  }
}
</style>
