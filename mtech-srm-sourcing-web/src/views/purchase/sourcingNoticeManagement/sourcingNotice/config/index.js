import { i18n } from '@/main.js'

export const statusList = [
  { value: 0, text: i18n.t('草稿'), cssClass: '' },
  { value: 1, text: i18n.t('发布'), cssClass: '' },
  { value: 2, text: i18n.t('撤回'), cssClass: '' },
  { value: 3, text: i18n.t('废弃'), cssClass: '' },
  { value: 4, text: i18n.t('关闭'), cssClass: '' }
]
export const announceTypeList = [
  { value: 1, text: i18n.t('寻源公告'), cssClass: '' },
  { value: 2, text: i18n.t('招标公告'), cssClass: '' },
  { value: 3, text: i18n.t('中标公示'), cssClass: '' },
  { value: 4, text: i18n.t('其他'), cssClass: '' }
]

export const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: '新增' },
  { id: 'Publish', icon: 'icon_solid_Createorder', title: '发布' },
  { id: 'Revoke', icon: 'icon_solid_Createorder', title: '撤回' },
  { id: 'Delete', icon: 'icon_solid_Delete', title: '删除' }
]

export const columnData = [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    field: 'announceCode',
    headerText: i18n.t('公告编号'),
    cellTools: []
  },
  {
    field: 'announceName',
    headerText: i18n.t('公告名称')
  },
  {
    field: 'announceType',
    headerText: i18n.t('公告类型'),
    valueConverter: {
      type: 'map',
      map: announceTypeList
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司'),
    formatter: function (_, item) {
      return item.companyCode + '-' + item.companyName
    }
  },
  {
    field: 'status',
    headerText: '状态',
    valueConverter: {
      type: 'map',
      map: statusList
    }
  },

  {
    field: 'createUserName',
    headerText: '创建人'
  },
  {
    field: 'createTime',
    headerText: '创建时间'
  },
  {
    field: 'publicTime',
    headerText: '发布时间'
  },
  {
    field: 'bidDeadlineTime',
    headerText: '截至时间'
  }
]

export const pageConfig = [
  {
    gridId: 'cb6163c9-c596-4740-8d34-92a3853ee849',
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    useToolTemplate: false,
    toolbar: {
      useBaseConfig: false,
      tools: [toolbar, ['Refresh', 'Setting']]
    },
    grid: {
      columnData,
      asyncConfig: {
        url: '/sourcing/tenant/portal/announce/pageQuery'
      }
    }
  }
]
