import { i18n } from '@/main.js'

const columnData = () => [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  // {
  //   width: '100',
  //   headerText: i18n.t('招标公示'),
  //   field: i18n.t('招标公示'),
  //   cssClass: 'field-content',
  //   valueConverter: {
  //     type: 'function',
  //     filter: () => {
  //       return i18n.t('招标公示')
  //     }
  //   }
  // },
  {
    width: '180',
    field: 'rfxName',
    headerText: i18n.t('标题')
  },
  {
    width: '100',
    field: 'statusName',
    headerText: i18n.t('状态')
  },
  {
    width: '180',
    field: 'rfxCode',
    headerText: i18n.t('寻源单号')
  },
  {
    width: '150',
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
  },

  {
    width: '120',
    field: 'sourcingMode',
    headerText: i18n.t('招标/竞价')
  },

  {
    width: '120',
    field: 'biddingMode',
    headerText: i18n.t('招标方式')
  },

  {
    width: '150',
    field: 'companyName',
    headerText: i18n.t('公司')
  },
  {
    width: '100',
    field: 'tenderer',
    headerText: i18n.t('招标员')
  },
  {
    width: '120',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

export const pageConfig = (url) => [
  {
    useToolTemplate: false,
    useBaseConfig: false,
    toolbar: {
      useBaseConfig: false,
      tools: [
        [
          { id: 'submit', icon: 'icon_solid_edit', title: i18n.t('提交') },
          { id: 'ExportRfx', icon: 'icon_solid_edit', title: i18n.t('导出') }
        ],
        ['Filter', 'Refresh', 'Setting']
      ]
    },
    gridId: '402a474a-e134-4db2-8ab7-bf8991594dc3',
    grid: {
      allowFiltering: true,
      allowSorting: false,
      columnData: columnData(),
      frozenColumns: 2,
      asyncConfig: {
        url
      }
    }
  }
]
