<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="modalData.title"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="appForm" :model="form.data" :rules="form.rules">
        <mt-row :gutter="20">
          <mt-col :span="12">
            <mt-form-item prop="noticeType" :label="$t('公示方式')">
              <mt-select
                :disabled="disabledForm"
                @change="changeNoticeType"
                v-model="form.data.noticeType"
                :data-source="form.dataSource.noticeType"
                :placeholder="$t('请选择公示方式')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="noticeScope" :label="$t('投标公示范围')">
              <mt-select
                :disabled="disabledForm"
                v-model="form.data.noticeScope"
                :data-source="form.dataSource.noticeScope"
                :placeholder="$t('请选择投标公示范围')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="isNoticeIdentity" :label="$t('公开身份')">
              <mt-select
                :disabled="disabledForm || form.data.noticeType === 0"
                v-model="form.data.isNoticeIdentity"
                :data-source="form.dataSource.isNoticeIdentity"
                :placeholder="$t('是否公开身份')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="isNoticePrice" :label="$t('公开价格')">
              <mt-select
                :disabled="disabledForm || form.data.noticeType === 0"
                v-model="form.data.isNoticePrice"
                :data-source="form.dataSource.isNoticePrice"
                :placeholder="$t('是否公开价格')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="isNoticeAllocation" :label="$t('公开中标配额')">
              <mt-select
                :disabled="disabledForm || form.data.noticeType === 0"
                v-model="form.data.isNoticeAllocation"
                :data-source="form.dataSource.isNoticeAllocation"
                :placeholder="$t('是否公开中标配额')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
export default {
  data() {
    return {
      buttons: [],
      form: {
        data: {
          id: null,
          rfxId: null,
          isNoticeAllocation: null, // 公开中标配额
          isNoticeIdentity: null, // 公开身份
          isNoticePrice: null, // 	公开价格
          noticeScope: null, // 公示范围
          noticeType: null // 公示方式
        },
        rules: {
          isNoticeAllocation: {
            required: true,
            message: this.$t('请输入公开中标配额'),
            trigger: 'blur'
          }, // 公开中标配额
          isNoticeIdentity: {
            required: true,
            message: this.$t('请输入公开身份'),
            trigger: 'blur'
          }, // 公开身份
          isNoticePrice: {
            required: true,
            message: this.$t('请输入公开价格'),
            trigger: 'blur'
          }, // 	公开价格
          noticeScope: {
            required: true,
            message: this.$t('请输入公示范围'),
            trigger: 'blur'
          }, // 公示范围
          noticeType: {
            required: true,
            message: this.$t('请输入公示方式'),
            trigger: 'blur'
          } // 公示方式
        },
        dataSource: {
          noticeType: [
            { text: this.$t('平台公示'), value: 0 },
            { text: this.$t('按规则公示'), value: 1 }
          ],
          noticeScope: [
            { text: this.$t('仅中标供应商'), value: 0 },
            { text: this.$t('全部'), value: 1 }
          ],
          isNoticeIdentity: [
            { text: this.$t('是'), value: true },
            { text: this.$t('否'), value: false }
          ],
          isNoticePrice: [
            { text: this.$t('是'), value: true },
            { text: this.$t('否'), value: false }
          ],
          isNoticeAllocation: [
            { text: this.$t('是'), value: true },
            { text: this.$t('否'), value: false }
          ]
        }
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    disabledForm() {
      return !this.buttons.find((e) => e.buttonModel.content === this.$t('保存'))
    }
  },
  beforeMount() {
    this.gnButtons()
    this.mergeFormData()
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    mergeFormData() {
      const formKeys = Object.keys(this.form.data)
      const pData = this.modalData.data
      for (let i = 0; i < formKeys.length; i++) {
        const formKey = formKeys[i]
        if (typeof pData[formKey] !== 'undefined') {
          this.form.data[formKey] = pData[formKey]
        }
      }
    },
    gnButtons(status) {
      if (typeof status === 'undefined') {
        status = this.modalData.data?.status
      }

      const btnMap = {
        cancel: {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        save: {
          click: this.saveRule,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        },
        preview: {
          click: this.preview,
          buttonModel: { isPrimary: 'true', content: this.$t('公告预览') }
        },
        submit: {
          click: this.submitNotice,
          buttonModel: { isPrimary: 'true', content: this.$t('提交') }
        }
      }

      if (status === 0) {
        // 已保存未提交: 保存 提交 预览
        this.buttons = [btnMap.save, btnMap.submit, btnMap.preview]
      } else if (status === 1) {
        // 已保存已提交: 预览
        this.buttons = [btnMap.preview]
      } else {
        // 保存前: 保存
        this.buttons = [btnMap.save]
      }
    },
    changeNoticeType({ value }) {
      // 公示方式为平台公示，公开身份、公示价格、公开中标配额默认为是，只读；
      // 公示方式为按规则公开，则可编辑，默认为空。
      if (value === 0) {
        this.form.data.isNoticeIdentity = true
        this.form.data.isNoticePrice = true
        this.form.data.isNoticeAllocation = true
        this.asyncFormValidate('appForm')
      }
    },
    /**
     * 保存公示规则
     */
    async saveRule() {
      const validate = await this.asyncFormValidate('appForm')
      if (!validate) {
        return
      }
      // 获取公示规则详情
      const ruleRes = await this.$API.rfxNotice.saveRule(this.form.data).catch(() => {})
      if (ruleRes) {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        this.form.data.id = ruleRes.data
        this.gnButtons(0)
      }
    },
    /**
     * 预览公示
     */
    async preview() {
      let buffer = await this.$API.rfxNotice
        .previewNotice({
          noticeRuleId: this.form.data.id
        })
        .catch(() => {})
      buffer?.data && (buffer = buffer.data) // "@mtech-common/http" 1.0.0 版本被挂载到 data, 0.15.1 版本是直接返回
      if (buffer instanceof Blob) {
        if (buffer.type === 'application/json') {
          try {
            const json = JSON.parse(await buffer.text())
            if (json.msg) {
              this.$toast({ content: json.msg, type: 'warning' })
            }
          } catch (error) {
            console.warn(error)
          }
        } else if (buffer.type === 'text/html') {
          const url = URL.createObjectURL(buffer)
          window.open(url)
        }
      }
    },
    /**
     * 提交公示
     */
    async submitNotice() {
      const previewNoticeRes = await this.$API.rfxNotice
        .submitNotice({
          noticeRuleId: this.form.data.id
        })
        .catch(() => {})
      if (previewNoticeRes) {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        this.gnButtons(1)
      }
    },
    asyncFormValidate(el) {
      return new Promise((c) => {
        this.$refs[el].validate((valid) => c(valid))
      })
    },
    cancel() {
      this.emitConfirm()
    },
    emitConfirm(...arg) {
      this.$emit('confirm-function', ...arg)
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  padding-top: 20px;
}
</style>
