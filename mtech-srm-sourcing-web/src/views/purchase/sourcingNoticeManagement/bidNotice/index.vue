<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :padding-top="true"
      :template-config="pageConfig"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickToolBar="handleClickToolBar"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  data() {
    return {
      pageConfig: pageConfig(this.$API.rfxNotice.listRfxBiddingResult)
    }
  },
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      if (e.toolbar.id === 'ExportRfx') {
        this.handleExport()
      }
      if (e.toolbar.id === 'submit') {
        this.handleSubmit()
      }
    },
    handleSubmit() {
      const selectRows = this.$refs.templateRef.getCurrentTabRef().grid.getSelectedRecords()
      if (!selectRows.length) {
        this.$toast({
          content: this.$t('请选择要提交的行'),
          type: 'warning'
        })
        return
      }
      const rfxIds = selectRows.map((item) => item.rfxId)
      this.$API.rfxNotice.submitNoticex({ rfxIds }).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('提交成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    handleExport() {
      // 先要点击“查询”按钮才能取得queryBuilderRules
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules,
        defaultRules: [
          // {
          //   condition: 'and',
          //   field: 'rfx_item.rfxHeaderId',
          //   operator: 'equal',
          //   value: this.rfxId
          // }
        ].concat(queryBuilderRules.rules || [])
      } // 筛选条件
      this.$API.rfxNotice.exportNotice(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleClickCellTitle(params) {
      const { field, data } = params
      if (field === this.$t('结果公示')) {
        this.showpPublicityRule(data)
      }
    },
    // 公示规则
    async showpPublicityRule(data) {
      const { noticeRuleId } = data
      let modalData

      if (noticeRuleId) {
        this.$store.commit('startLoading')
        // 获取公示规则详情
        const detailRule = await this.$API.rfxNotice
          .getDetailRule({
            noticeRuleId
          })
          .catch(() => {})
        this.$store.commit('endLoading')
        // 放行无公示规则
        if (typeof detailRule?.data !== 'object') {
          return
        }
        modalData = detailRule.data
      }

      const modal = () =>
        import(
          /* webpackChunkName: "router/purchase/rfx/rfxNotice/list/components/publicityRule" */ './components/publicityRule.vue'
        )
      modalData = {
        ...modalData,
        id: data.noticeRuleId,
        rfxId: data.rfxId
      }
      this.$dialog({
        modal,
        data: {
          title: this.$t('公示规则'),
          data: modalData
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  /deep/.e-headercelldiv {
    padding: 0 0.6em 0 0.6em !important;
  }
}
</style>
