<template>
  <html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${(announce.companyName)!''}-寻源公告</title>
  <style>
    * {
      margin: 0;
      padding: 0;
    }
  </style>
</head>
<body style="width:100%;padding:15px;background: #f0f2f5;box-sizing: border-box;font-family: 微软雅黑;">
  <div style="width:100%;min-height:calc(100vh - 30px);box-sizing: border-box;background: #fff;padding:20px 5%;border-radius:4px;">
    <div style="text-align: center;font-weight:bold;border-bottom: 1px solid #e6e7eb;padding-bottom: 20px;">
      <h4>${(announce.announceName)!''}</h4>
      <p style="margin:0;padding:0;font-size: 16px;margin-top: 8px;">${(createTime)!''}</p>
    </div>
    <div style="font-size: 14px;line-height: 200%;font-family: 微软雅黑;padding:20px 0;">
      <div style="display: flex; flex-wrap: wrap; gap: 16px; margin: 10px 0;">
        <span>公告名称：${(announce.announceName)!''}</span>
        <span>公告编码：${(announce.announceCode)!''}</span>
        <span>招标公司：${(announce.companyName)!''}</span>
        <span>需求地点：${(announce.place)!''}</span>
        <span>应标截至时间：${(bidDeadlineTime)!''}</span>
      </div>
      <div style="margin: 30px 0;">
        ${(announce.announceContent)!''}
      </div>
      <p style="border-top: 1px dashed #e6e7eb;padding-top: 20px;">附件下载：</p>
      <#if fileList?exists>
        <#list fileList as value>
          <a style="display: block;margin-bottom:12px;color:#4fb9ff;" href="${value.remoteUrl!''}">${value.fileName!''}</a>
        </#list>
      </#if>
    </div>
  </div>
</body>
</html>
</template>
