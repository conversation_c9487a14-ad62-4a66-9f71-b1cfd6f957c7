import { i18n } from '@/main.js'
import { searchOptionsList } from '@/constants'
export const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'delete', icon: 'icon_solid_Delete', title: i18n.t('删除') },
  { id: 'submit', icon: 'icon_solid_Submit', title: i18n.t('提交') },
  { id: 'import', icon: 'icon_solid_upload', title: i18n.t('导入') },
  { id: 'export', icon: 'icon_solid_Download', title: i18n.t('导出') }
]
// 定额维护
export const maintainColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'quotaNo',
    headerText: i18n.t('定额单号'),
    cssClass: 'field-content'
  },
  {
    field: 'oaUrl',
    headerText: i18n.t('OA审批查看链接'),
    cssClass: 'field-content',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e == '0' || e == '3') {
          return '--'
        } else {
          return i18n.t('OA审批查看')
        }
      }
    }
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('草稿'),
        1: i18n.t('审批中'),
        2: i18n.t('审批通过'),
        3: i18n.t('审批驳回'),
        4: i18n.t('审批废弃'),
        5: i18n.t('审批撤回')
      }
    }
  },
  {
    field: 'isNotHvac',
    headerText: i18n.t('是否暖通'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    allowGlobalSorting: true,
    searchOptions: {
      ...searchOptionsList.timeRange
    }
  }
]
// 定额查询
export const queryColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  //detailNo --明细单号   quotaNo --主单单号
  {
    field: 'quotaNo',
    headerText: i18n.t('定额单号'),
    cssClass: 'field-content',
    searchOptions: {
      renameField: 'i.quota_no'
    }
  },
  {
    field: 'detailNo',
    headerText: i18n.t('定额明细单号')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('草稿'),
        1: i18n.t('审批中'),
        2: i18n.t('审批通过'),
        3: i18n.t('审批驳回')
      }
    },
    searchOptions: {
      renameField: 'h.status'
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'costFactorCode',
    headerText: i18n.t('成本因子编码')
  },
  {
    field: 'costFactorName',
    headerText: i18n.t('成本因子名称')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'quotaCode',
    headerText: i18n.t('定额编码')
  },
  {
    field: 'quotaName',
    headerText: i18n.t('定额名称')
  },
  {
    field: 'quotaUnit',
    headerText: i18n.t('定额单位')
  },
  {
    field: 'quotaValue',
    headerText: i18n.t('定额值')
  },
  {
    field: 'validDateStart',
    headerText: i18n.t('有效期从'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (isNaN(parseInt(e))) {
          return '--'
        } else if (typeof e == 'string' && e.includes('-')) {
          return e.substr(0, 10)
        } else {
          let date = new Date(Number(e))
          let year = date.getFullYear()
          let month = date.getMonth() + 1
          let day = date.getDate()
          month = month < 10 ? '0' + month : month
          day = day < 10 ? '0' + day : day
          return year + '-' + month + '-' + day
        }
      }
    },
    searchOptions: {
      ...searchOptionsList.timeRange
    }
  },
  {
    field: 'validDateEnd',
    headerText: i18n.t('有效期至'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (isNaN(parseInt(e))) {
          return '--'
        } else if (typeof e == 'string' && e.includes('-')) {
          return e.substr(0, 10)
        } else {
          let date = new Date(Number(e))
          let year = date.getFullYear()
          let month = date.getMonth() + 1
          let day = date.getDate()
          month = month < 10 ? '0' + month : month
          day = day < 10 ? '0' + day : day
          return year + '-' + month + '-' + day
        }
      }
    },
    searchOptions: {
      ...searchOptionsList.timeRange
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

// export const pageConfig = () => [
//   {
//     title: i18n.t("定额维护"),
//     toolbar,
//     grid: {
//       useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
//       columnData: maintainColumnData,
//       asyncConfig: {
//         url: "/sourcing/tenant/quotaMaintenanceHeader/queryBuilder",
//         params: {},
//       },
//     },
//   },
//   {
//     title: i18n.t("定额查询"),
//     toolbar: [],
//     useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
//     grid: {
//       columnData: queryColumnData,
//       asyncConfig: {
//         url: "/sourcing/tenant/quotaMaintenanceItem/queryBuilder",
//         params: {},
//       },
//     },
//   },
// ];
