<!-- 定额查询 -->
<template>
  <div class="full-height">
    <mt-template-page
      ref="tepPage"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { queryColumnData } from './config'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {},
  data() {
    return {
      pageConfig: [
        {
          toolbar: [{ id: 'export', icon: 'icon_solid_Download', title: this.$t('导出') }],
          gridId: '54ab4111-1d19-4e7a-9ef4-0558d14e2950',
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          grid: {
            columnData: queryColumnData,
            asyncConfig: {
              url: '/sourcing/tenant/quotaMaintenanceItem/queryBuilder',
              defaultRules: [
                {
                  label: this.$t('状态'),
                  field: 'h.status',
                  type: 'string',
                  operator: 'equal',
                  value: '2'
                }
              ]
            }
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(e) {
      if (e.toolbar.id === 'export') this.handleExport()
    },
    //点击行内
    handleClickCellTitle(e) {
      if (e.field == 'quotaNo') {
        this.$router.push({
          path: 'purchase-quotaConfig-detail',
          query: {
            id: e.data.headerId,
            key: this.$utils.randomString()
          }
        })
      }
    },
    //导出
    handleExport() {
      this.$store.commit('startLoading')
      // 先要点击“查询”按钮才能取得queryBuilderRules
      const queryBuilderRules =
        this.$refs.tepPage.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 500000 },
        ...queryBuilderRules,
        defaultRules: [
          {
            label: this.$t('状态'),
            field: 'h.status',
            type: 'string',
            operator: 'equal',
            value: '2'
          }
        ]
      } // 筛选条件

      this.$API.configuration
        .quotaCfgDetailExport(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
          this.$store.commit('endLoading')
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    }
  }
}
</script>
