<!-- 定额 -->
<template>
  <div id="app">
    <mt-template-page
      ref="tepPage"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { toolbar, maintainColumnData, queryColumnData } from './config'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {},
  data() {
    return {
      // pageConfig: pageConfig(),
      pageConfig: [
        {
          title: this.$t('定额维护'),
          toolbar,
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          gridId: '66529d48-62ff-4054-b4b0-a6bf0f2b3ffd',
          grid: {
            columnData: maintainColumnData,
            asyncConfig: {
              url: '/sourcing/tenant/quotaMaintenanceHeader/queryBuilder',
              params: {},
              serializeList: (list) => {
                list.forEach((e) => {
                  e.oaUrl = e.status
                })
                return list
              }
            }
          }
        },
        {
          title: this.$t('定额查询'),
          toolbar: [{ id: 'export', icon: 'icon_solid_Download', title: this.$t('导出') }],
          gridId: '1599933e-c224-4359-a1c4-b645e8ec0e66',
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          grid: {
            columnData: queryColumnData,
            asyncConfig: {
              url: '/sourcing/tenant/quotaMaintenanceItem/queryBuilder',
              params: {}
            }
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      const _selectRows = e.gridRef.getMtechGridRecords()

      if (e.toolbar.id == 'Add') {
        this.handleClickToolBarAdd()
      }
      if (_selectRows.length == 0 && (e.toolbar.id == 'delete' || e.toolbar.id == 'submit')) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'delete') {
        for (let item of _selectRows) {
          if (item.status != 0 && item.status != 4 && item.status != 5) {
            this.$toast({
              content: this.$t('只能删除草稿、撤回、废弃状态的单据'),
              type: 'warning'
            })
            return
          }
        }
        this.handleClickToolBarDelete(_selectRows)
      }
      if (e.toolbar.id == 'submit') {
        this.handleClickToolBarSubmit(_selectRows)
      }
      if (e.toolbar.id === 'import') this.handleImport()
      if (e.toolbar.id === 'export') this.handleExport(e)
    },
    //新增
    handleClickToolBarAdd() {
      this.$router.push({
        path: 'purchase-quotaConfig-detail',
        query: { key: this.$utils.randomString(), type: 1 }
      })
    },
    //删除
    handleClickToolBarDelete(_selectRows) {
      let ids = _selectRows.map((item) => item.id)
      let parameter = {
        ids: ids
      }
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选数据？'),
          confirm: () => this.$API.configuration.deleteHeader(parameter)
        },
        success: () => {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.tepPage.refreshCurrentGridData()
        }
      })
    },
    //提交
    handleClickToolBarSubmit(_selectRows) {
      let ids = _selectRows.map((item) => item.id)
      let parameter = {
        ids: ids
      }
      this.$store.commit('startLoading')
      this.$API.configuration
        .submit(parameter)
        .then(() => {
          this.$store.commit('endLoading')
          this.$refs.tepPage.refreshCurrentGridData()
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    //点击行内
    handleClickCellTitle(e) {
      if (e.field == 'quotaNo') {
        if (e.tabIndex == 0) {
          this.handleClickCellTitleFieldContent(e.data)
        } else {
          this.$router.push({
            path: 'purchase-quotaConfig-detail',
            query: {
              id: e.data.headerId,
              key: this.$utils.randomString()
            }
          })
        }
      }
      //oa
      if (e.field == 'oaUrl') {
        this.handleClickCellTitleOaUrl(e)
      }
    },
    handleClickCellTitleOaUrl(e) {
      if (e.data.oaUrl == '0' || e.data.oaUrl == '3') return
      let parameter = {
        docId: e.data.id
      }
      this.$API.configuration.getQuotaOaLink(parameter).then(({ data }) => {
        window.open(data)
        // var a = document.createElement("a");
        // a.href = data;
        // a.id = "oaUrl";
        // a.download = "a.pdf";
        // a.style = "display:none";
        // document.body.appendChild(a);
        // document.getElementById("oaUrl").click();
        // setTimeout(function () {
        //   document.getElementById("oaUrl").remove();
        // }, 600);
      })
    },
    //
    handleClickCellTitleFieldContent(data) {
      console.log(data)
      this.$router.push({
        path: 'purchase-quotaConfig-detail',
        query: {
          id: data.id,
          key: this.$utils.randomString(),
          type: 1
        }
      })
    },
    //导出
    handleExport(e) {
      this.$store.commit('startLoading')
      // 先要点击“查询”按钮才能取得queryBuilderRules
      const queryBuilderRules =
        this.$refs.tepPage.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 500000 },
        ...queryBuilderRules
        // defaultRules: [
        // {
        //   field: 'decidePriceType',
        //   operator: 'equal',
        //   value: 7
        // }
        // ].concat(queryBuilderRules.rules || [])
      } // 筛选条件
      if (e.tabIndex === 0) {
        this.$API.configuration
          .quotaCfgExport(params)
          .then((res) => {
            const fileName = getHeadersFileName(res)
            download({ fileName: `${fileName}`, blob: res.data })
            this.$store.commit('endLoading')
          })
          .catch(() => {
            this.$store.commit('endLoading')
          })
      } else {
        this.$API.configuration
          .quotaCfgDetailExport(params)
          .then((res) => {
            const fileName = getHeadersFileName(res)
            download({ fileName: `${fileName}`, blob: res.data })
            this.$store.commit('endLoading')
          })
          .catch(() => {
            this.$store.commit('endLoading')
          })
      }
    },
    //导入
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('上传/导入'),
          importApi: this.$API.configuration.excelimport,
          asyncParams: {},
          downloadTemplateApi: this.$API.configuration.exportTpl
        },
        success: () => {
          //刷新列表
          this.$refs.tepPage.refreshCurrentGridData()
        }
      })
    }
  }
}
</script>
<style>
.OfferBid-status-class {
  margin-bottom: 5px;
}
.OfferBid-status0 {
  width: 56px;
  height: 20px;
  background: #f4f4f4;
  border-radius: 2px;
  padding: 4px;
  color: #9a9a9a;
}
.OfferBid-status1 {
  width: 56px;
  height: 20px;
  background: #eef2f9;
  border-radius: 2px;
  padding: 4px;
  color: #6386c1;
}
.OfferBid-status2 {
  width: 56px;
  height: 20px;
  background: #eef2f9;
  border-radius: 2px;
  padding: 4px;
  color: #6386c1;
}
</style>
<style>
.user-content {
  color: red;
}
body,
html {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
}
#app {
  padding: 0;
  height: 100%;
  width: 100%;
}
.test-btn {
  position: fixed;
  bottom: 10px;
  z-index: 2;
  left: 50px;
}
</style>
