<template>
  <div>
    <mt-dialog
      ref="dialog"
      css-class="dialog-main"
      :buttons="buttons"
      :header="headers"
      @beforeClose="cancel"
    >
      <div class="dialog-content" v-show="!dialogShow">
        <mt-form class="dialog-mt-form" ref="dialogRef" :model="formObject" :rules="formRules">
          <mt-form-item v-if="headStates == 'Add'" prop="organizationCode" :label="$t('公司名称')">
            <mt-select
              v-model="formObject.organizationCode"
              float-label-type="Never"
              :data-source="companySelect"
              :fields="{ text: 'orgName', value: 'orgCode' }"
              :disabled="weight <= 0 && headStates == 'edit'"
              @change="changecompanySelect"
              :placeholder="$t('请选择公司')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item v-else prop="organizationName" :label="$t('公司名称')">
            <mt-input
              v-model="formObject.organizationName"
              :disabled="weight <= 0 && headStates == 'edit'"
              :placeholder="$t('请选择公司')"
            ></mt-input>
            <!--             :data-source="companyData"
            :fields="{ text: 'organizationName', value: 'organizationName' }" -->
          </mt-form-item>
          <mt-form-item v-if="headStates == 'Add'" prop="supplierCode" :label="$t('供应商名称')">
            <mt-select
              v-model="formObject.supplierCode"
              float-label-type="Never"
              :data-source="supplierArr"
              :fields="{ text: 'supplierName', value: 'supplierCode' }"
              :disabled="weight <= 1 || headStates == 'edit'"
              @change="changesupplierSelect"
              :placeholder="$t('请选择供应商')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item v-else prop="supplierName" :label="$t('供应商名称')">
            <mt-input
              v-model="formObject.supplierName"
              float-label-type="Never"
              :disabled="weight <= 1 || headStates == 'edit'"
              :placeholder="$t('请先选择公司')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item v-if="headStates == 'Add'" prop="siteName" :label="$t('工厂名称')">
            <mt-select
              v-model="formObject.siteCode"
              float-label-type="Never"
              :data-source="siteSelect"
              :fields="{ text: 'siteName', value: 'siteCode' }"
              :disabled="weight <= 1 || headStates == 'edit'"
              @change="changesiteSelect"
              :placeholder="$t('请先选择公司')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item v-else prop="siteName" :label="$t('工厂名称')">
            <mt-input
              v-model="formObject.siteName"
              float-label-type="Never"
              :disabled="weight <= 1 || headStates == 'edit'"
              :placeholder="$t('请先选择公司')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item v-if="headStates == 'Add'" prop="itemCode" :label="$t('物料名称')">
            <mt-input
              :width="370"
              style="display: inline-block"
              v-model="formObject.itemName"
              float-label-type="Never"
              :disabled="true"
              :placeholder="$t('请选择物料')"
            ></mt-input>
            <mt-icon
              :class="[weight == 3 ? '' : 'allowed']"
              style="width: 20px; margin-left: 10px"
              name="icon_input_search"
              @click.native="showDialog"
            ></mt-icon>
          </mt-form-item>
          <mt-form-item v-else prop="itemName" :label="$t('物料名称')">
            <mt-input
              v-model="formObject.itemName"
              float-label-type="Never"
              :disabled="weight <= 1 || headStates == 'edit'"
              :placeholder="$t('请先选择工厂')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
      <mt-template-page
        v-show="dialogShow"
        ref="templateRef"
        :hidden-tabs="true"
        :template-config="pageConfig"
      >
        <!-- @recordDoubleClick="recordDoubleClick"双击物料确认数据 -->
      </mt-template-page>
    </mt-dialog>
  </div>
</template>
<script>
import { utils } from '@mtech-common/utils'
import { pageConfig } from './config/index'
// import { quotaConfigValues, quotaConfigValuehHanders } from "./config/module";
export default {
  data() {
    return {
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      //v-model获取的值
      formObject: {
        organizationName: '', //公司名称
        supplierName: '', // 供应商名称
        siteName: '', // 工厂名称
        itemName: '' //物料名称
      },
      //必填项
      formRules: {
        organizationName: [
          {
            required: true,
            message: this.$t('公司名称'),
            trigger: 'blur'
          }
        ],
        supplierName: [
          {
            required: true,
            message: this.$t('供应商名称'),
            trigger: 'blur'
          }
        ],
        siteName: [
          {
            required: true,
            message: this.$t('工厂名称'),
            trigger: 'blur'
          }
        ],
        itemName: [
          {
            required: true,
            message: this.$t('物料名称'),
            trigger: 'blur'
          }
        ]
      },
      //公司下拉框
      companySelect: [],
      //供应商
      supplierArr: [],
      //工厂下拉框
      siteSelect: [],
      //物料下拉框
      itemSelect: [],
      weight: 1, //权重值判断那些下拉框可以编辑
      //--------------物料弹框
      headerTxt: this.$t('选择物料'),
      dialogShow: false,
      pageConfig: pageConfig
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    JITdata() {
      return this.modalData.data
    },
    headStates() {
      return this.modalData.headStates
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    ;(this.headers = this.header),
      (this.inputPersonnelitem = utils.debounce(this.inputPersonnelitem, 1000))
    if (this.headStates == 'Add') {
      this.initialCallInterface()
    } else if (this.headStates == 'edit') {
      this.editCallInterface()
    }
  },
  methods: {
    //新增调用接口
    initialCallInterface() {
      let parameter = {
        fuzzyParam: '',
        organizationLevelCodes: ['ORG02'],
        orgType: 'ORG001PRO'
      }
      this.$API.configuration.findSpecifiedChildrenLevelOrgs(parameter).then((res) => {
        this.companySelect = res.data
      })
    },
    //编辑回选
    editCallInterface() {},
    //公司下拉框事件
    changecompanySelect(e) {
      if (this.headStates == 'Add') {
        console.log(e.itemData)
        let id = e.itemData.id
        this.formObject.organizationCode = e.itemData.orgCode
        this.formObject.organizationName = e.itemData.orgName
        this.formObject.siteName = ''
        this.formObject.siteCode = ''
        this.formObject.supplierName = ''
        this.formObject.supplierCode = ''
        this.formObject.itemName = ''
        this.formObject.itemCode = ''
        let parameter = {
          parentId: id,
          tenantId: 10000
        }
        this.weight = 2 //让工厂下拉框可以选择
        this.$API.configuration.findSiteInfoByParentId(parameter).then((res) => {
          // let res = factorySelects;
          this.siteSelect = res.data
        })
        //加工商
        let data = {
          organizationCode: e.itemData.orgCode,
          fuzzyNameOrCode: ''
        }
        this.$API.configuration.criteriaQuery(data).then((res) => {
          this.supplierArr = res.data
        })
      }
    },
    //供应商
    changesupplierSelect(e) {
      if (this.headStates == 'Add') {
        this.formObject.supplierName = e.itemData.supplierName
        this.formObject.supplierCode = e.itemData.supplierCode
      }
    },
    //工厂下拉框事件
    changesiteSelect(e) {
      if (this.headStates == 'Add') {
        // console.log(e);
        let id = e.itemData.organizationId
        this.formObject.organizationId = id
        this.formObject.siteCode = e.itemData.siteCode
        this.formObject.siteName = e.itemData.siteName
        this.formObject.itemName = ''
        this.formObject.itemCode = ''
        this.weight = 3
        this.$set(this.pageConfig[0].grid, 'asyncConfig', {
          url: `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
          // recordsPosition: "data", 更改指向
          params: {
            condition: 'and',
            tenantId: '10000',
            defaultRules: [
              {
                field: 'organizationId',
                operator: 'equal',
                type: 'long',
                value: id
              }
            ]
          },
          serializeList: (list) => {
            return list.map((item) => {
              return {
                ...item,
                categoryCode: item?.categoryResponse?.categoryCode ?? ''
              }
            })
          }
        })
      }
    },
    //物料---弹框显示
    showDialog() {
      if (this.weight == '3') {
        this.dialogShow = true
        this.headers = this.$t('请选择物料/品项编码')
        this.buttons = [
          {
            click: this.itemCancel,
            buttonModel: { content: this.$t('取消') }
          },
          {
            click: this.itemConfirm,
            buttonModel: { isPrimary: 'true', content: this.$t('确定') }
          }
        ]
      }
    },
    //点击确认
    confirm() {
      // this.$emit("confirm-function");
      // console.log(this.formObject);
      // let formObject = this.formObject;
      // if (formObject.organizationName == "") {
      //   this.$toast({
      //     content: this.$t("公司名称不能为空"),
      //     type: "warning",
      //   });
      //   return;
      // }
      // if (formObject.siteName == "") {
      //   this.$toast({
      //     content: this.$t("工厂名称不能为空"),
      //     type: "warning",
      //   });
      //   return;
      // }
      // if (formObject.itemName == "") {
      //   this.$toast({
      //     content: this.$t("品项名称不能为空"),
      //     type: "warning",
      //   });
      //   return;
      // }

      // let parameter = {
      //   itemCode: formObject.itemCode,
      //   // itemName: formObject.itemName,
      //   itemIdentification: formObject.itemIdentification,
      //   organizationCode: formObject.organizationCode,
      //   // organizationName: formObject.organizationName,
      //   // siteName: formObject.siteName,
      //   siteCode: formObject.siteCode,
      // };
      // console.log(
      //   parameter.organizationName,
      //   parameter.organizationCode,
      //   "formObject.organizationName"
      // );
      // if (this.headStates == "Add") {
      //   this.$API.material.JITadd(parameter).then((res) => {
      //     this.$emit("confirm-function");
      //   });
      // }
      // if (this.headStates == "edit") {
      //   parameter.id = formObject.id;
      //   this.$API.material.JITupdate(parameter).then((res) => {
      //     this.$emit("confirm-function");
      //   });
      // }
      let _records = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords()
      this.queryByCompanyAndRelfunction(_records[0])
    },
    //物料确认
    itemConfirm() {
      let _records = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords()
      this.formObject.itemCode = _records[0].itemCode
      this.formObject.itemName = _records[0].itemName
      this.formObject.categoryCode = _records[0].categoryCode
      this.itemCancel()
    },
    async queryByCompanyAndRelfunction(data) {
      let parameter = {
        itemCode: data.itemCode
      }
      let quotaConfigValue = null
      // 值
      await this.$API.configuration.queryLeafNodeDataQuota(parameter).then((res) => {
        quotaConfigValue = res.data
        let { itemCode, itemName, supplierCode, supplierName } = this.formObject
        quotaConfigValue.map((item) => {
          item.itemCode = itemCode
          item.itemName = itemName
          item.supplierCode = supplierCode
          item.supplierName = supplierName
        })
      })
      await this.$emit('confirm-function', quotaConfigValue)
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    },
    //物料取消
    itemCancel() {
      this.dialogShow = false
      this.newdialogheader()
    },
    newdialogheader() {
      this.headers = this.$t('新建')
      this.buttons = [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  min-height: 350px;
  .e-dlg-content {
    padding: 0;
    .dialog-content {
      padding: 40px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 400px;
          .mt-icons {
            cursor: pointer;
          }
          .allowed {
            cursor: no-drop;
          }
        }
      }
    }
    .common-template-page {
      height: 100%;
      .e-gridcontent {
        max-height: 340px;
      }
      .mt-pagertemplate {
        overflow: inherit !important;
      }
    }
  }
}
</style>
