export const quotaConfigValues = {
  code: 200,
  msg: '执行成功',
  data: [
    {
      1006: '',
      parentName: '人工',
      1005: '',
      1004: '',
      costFactorCode: '',
      1003: '',
      costFactorName: '研发',
      1002: '功能开发',
      calculationFormula: '0',
      businessAttribute: '',
      rowKey: '213068678967451677'
    },
    {
      1006: '',
      parentName: '材料成本',
      1005: '',
      1004: '',
      costFactorCode: '',
      1003: '',
      costFactorName: '塑性材料',
      1002: 'PVC',
      calculationFormula: '0',
      businessAttribute: '',
      rowKey: '213068222430044225'
    },
    {
      1006: '',
      parentName: '人工',
      1005: '',
      1004: '',
      costFactorCode: '',
      1003: '',
      costFactorName: '研发',
      1002: '产品设计',
      calculationFormula: '0',
      businessAttribute: '',
      rowKey: '213068631949303842'
    },
    {
      1006: '',
      parentName: '材料成本',
      1005: '',
      1004: '',
      costFactorCode: '',
      1003: '',
      costFactorName: '塑性材料',
      1002: 'PP',
      calculationFormula: '0',
      businessAttribute: '',
      rowKey: '213068482703384672'
    }
  ],
  errorStackTrace: null
}
export const quotaConfigValuehHanders = {
  code: 200,
  msg: '执行成功',
  data: [
    {
      id: '213068047393349664',
      costModelId: '213066397886767142',
      costModelItemId: '213067953575157790',
      baseFieldId: '1002',
      columnCode: '1002',
      columnName: '成本项名称',
      columnAlias: '成本项名称',
      sortValue: 2,
      builtIn: 1,
      columnType: 1,
      calculationFormula: '',
      valueSet: '',
      columnAttribute: ''
    },
    // {
    //   id: "213068047426904117",
    //   costModelId: "213066397886767142",
    //   costModelItemId: "213067953575157790",
    //   baseFieldId: "1003",
    //   columnCode: "1003",
    //   columnName: "数量",
    //   columnAlias: "数量",
    //   sortValue: 3,
    //   builtIn: 1,
    //   columnType: 0,
    //   calculationFormula: "",
    //   valueSet: "",
    //   columnAttribute: "",
    // },
    {
      id: '213068047452069934',
      costModelId: '213066397886767142',
      costModelItemId: '213067953575157790',
      baseFieldId: '1004',
      columnCode: '1004',
      columnName: '单位',
      columnAlias: '单位',
      sortValue: 4,
      builtIn: 1,
      columnType: 2,
      calculationFormula: '',
      valueSet: '',
      columnAttribute: ''
    },
    {
      id: '213068047481430017',
      costModelId: '213066397886767142',
      costModelItemId: '213067953575157790',
      baseFieldId: '1005',
      columnCode: '1005',
      columnName: '单价',
      columnAlias: '单价',
      sortValue: 5,
      builtIn: 1,
      columnType: 0,
      calculationFormula: '',
      valueSet: '',
      columnAttribute: ''
    },
    {
      id: '213068047510790199',
      costModelId: '213066397886767142',
      costModelItemId: '213067953575157790',
      baseFieldId: '1006',
      columnCode: '1006',
      columnName: '金额',
      columnAlias: '金额',
      sortValue: 6,
      builtIn: 1,
      columnType: 0,
      calculationFormula: '',
      valueSet: '',
      columnAttribute: ''
    }
  ],
  errorStackTrace: null
}
