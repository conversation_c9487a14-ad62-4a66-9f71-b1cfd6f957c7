import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-09-29 09:58:01
 * @LastEditTime: 2021-12-30 13:54:42
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\material\config\materialSite.config.js
 */
// import Vue from "vue";
// import { i18n } from "@/main.js";
const columnData = [
  // {
  //   width: "50",
  //   type: "checkbox",
  // },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
    // width: "350",
    // cellTools: ["edit", "delete", "preview"],
  },
  {
    field: 'itemName',
    // width: "200",
    headerText: i18n.t('物料名称')
  },
  {
    field: 'categoryCode',
    // width: "200",
    headerText: i18n.t('品类')
  },
  {
    field: 'itemDescription',
    // width: "200",
    headerText: i18n.t('规格型号')
  },
  {
    field: 'oldItemCode',
    // width: "200",
    headerText: i18n.t('旧物料编号')
  },
  {
    field: 'manufacturerName',
    // width: "200",
    headerText: i18n.t('制造商')
  }
]
// export const pageConfig = [
//   {
//     toolbar: [],
//     useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
//     grid: {
//       height: 352,
//       allowPaging: true,
//       allowSelection: true,
//       selectionSettings: {
//         checkboxOnly: false,
//       },
//       columnData,
//       asyncConfig: {
//         url: "/masterDataManagement/tenant/item-org-rel/fuzzy-query?BU_CODE=${localStorage.getItem('currentBu')}",
//         params: {
//           organizationId: "1486316872832323585",
//           itemQueryKeyword: "",
//         },
//       },
//     },
//   },
// ];
export const pageConfig = [
  // {
  //   toolbar: [],
  //   height: 352,
  //   grid: {
  //     columnData,
  //     asyncConfig: {},
  //   },
  // },
  {
    toolbar: [],
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      height: 350,
      allowPaging: true,
      allowSelection: true,
      selectionSettings: {
        checkboxOnly: false
      },
      columnData,
      asyncConfig: {}
    }
  }
]
