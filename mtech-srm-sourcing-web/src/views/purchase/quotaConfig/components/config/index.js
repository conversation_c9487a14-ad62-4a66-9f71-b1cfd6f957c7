import { i18n } from '@/main.js'
import Vue from 'vue'
import cellChanged from 'COMPONENTS/NormalEdit/cellChanged' // 单元格被改变（纯展示）
import Select from '../editComponents/Select.vue'
import selectedAllItemCode from 'COMPONENTS/NormalEdit/selectAllItemCode' // 物料
import selectAllCostFactorCode from 'COMPONENTS/NormalEdit/selectAllCostFactorCode' // 成本因子
import checkSelectedItemCode from 'COMPONENTS/NormalEdit/checkSelectItemCode'
import selectAllAttrCode from 'COMPONENTS/NormalEdit/selectAllAttrCode' // 属性
import selectAllUnitCode from 'COMPONENTS/NormalEdit/selectAllUnitCode' // 属性
import { createEditInstance, Formatter } from '@/utils/ej/dataGrid/index'
import bus from '@/utils/bus'
import { formatTime } from '@/utils/utils'
export const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'Cancel', icon: 'icon_solid_Cancel', title: i18n.t('取消') },
  { id: 'delete', icon: 'icon_solid_Delete', title: i18n.t('删除') },
  { id: 'save', icon: 'icon_solid_Save', title: i18n.t('保存') },
  { id: 'export', icon: 'icon_solid_Download', title: i18n.t('导出') }
]
export const columnData = () => {
  const editInstance = createEditInstance()
    .component('checkSelectedItemCode', checkSelectedItemCode)
    .onInput((ctx, { field, value }) => {
      console.log('createEditInstance----', ctx, field, value)
      if (field === 'validDateEnd') {
        if (value && value != '0') {
          let _tempDate = formatTime(new Date(value), 'YYYY-mm-dd')
          _tempDate = Number(new Date(_tempDate + ' 23:59:59'))
          editInstance.setValueByField('validDateEnd', _tempDate)
        }
      }
    })
    .onChange(async (ctx, { field }) => {
      console.log('==onChange==')
      if (field === 'itemCode') {
        // ctx.setValueByField("stepQuoteType", "");
      }
    })
  const busFields = ['itemName', 'costFactorName', 'quotaUnitCode', 'quotaName', 'quotaType']
  busFields.forEach((busField) => {
    bus.$on(`${busField}Change`, async (data) => {
      if (busField == 'quotaType') {
        editInstance.setValueByField(busField, data)
        if (data == 1) {
          editInstance.setOptions('quotaValue', {
            value: null,
            disabled: true,
            readonly: true
          })
        } else {
          editInstance.setOptions('quotaValue', {
            value: 0,
            min: 0,
            precision: '2',
            disabled: false,
            readonly: false
          })
        }
      } else {
        editInstance.setValueByField(busField, data)
      }
    })
  })
  return [
    {
      width: '50',
      type: 'checkbox',
      allowEditing: false
    },
    // {
    //   field: "lineNo",
    //   width: "100",
    //   headerText: i18n.t("行号"),
    //   allowEditing: false,
    // },
    {
      field: 'itemCode',
      width: '200',
      headerText: i18n.t('物料编码'),
      headerTemplate: () => {
        return {
          template: Vue.component('requiredCell', {
            template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{fieldName}}</span>
                  </div>
                `,
            data() {
              return {
                data: {},
                fieldName: ''
              }
            },
            mounted() {
              this.fieldName = i18n.t('物料编码')
            }
          })
        }
      },
      allowEditing: true,
      editTemplate: () => {
        return {
          template: selectedAllItemCode
        }
      }
    },
    {
      field: 'itemName',
      width: '150',
      headerText: i18n.t('物料名称'),
      headerTemplate: () => {
        return {
          template: Vue.component('requiredCell', {
            template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{fieldName}}</span>
                  </div>
                `,
            data() {
              return {
                data: {},
                fieldName: ''
              }
            },
            mounted() {
              this.fieldName = i18n.t('物料名称')
            }
          })
        }
      },
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'costFactorCode',
      width: '200',
      headerText: i18n.t('成本因子编码'),
      allowEditing: true,
      editTemplate: () => {
        return {
          template: selectAllCostFactorCode
        }
      }
    },
    {
      field: 'costFactorName',
      width: '150',
      headerText: i18n.t('成本因子名称'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'supplierCode',
      width: '250',
      headerText: i18n.t('供应商编码'),
      // headerTemplate: () => {
      //   return {
      //     template: Vue.component('requiredCell', {
      //       template: `
      //             <div class="headers">
      //               <span style="color: red">*</span>
      //               <span class="e-headertext">{{fieldName}}</span>
      //             </div>
      //           `,
      //       data() {
      //         return {
      //           data: {},
      //           fieldName: ''
      //         }
      //       },
      //       mounted() {
      //         this.fieldName = i18n.t('供应商编码')
      //       }
      //     })
      //   }
      // },
      editTemplate: () => {
        return {
          template: Select
        }
      }
    },
    {
      field: 'supplierName',
      width: '250',
      headerText: i18n.t('供应商名称'),
      // headerTemplate: () => {
      //   return {
      //     template: Vue.component('requiredCell', {
      //       template: `
      //             <div class="headers">
      //               <span style="color: red">*</span>
      //               <span class="e-headertext">{{fieldName}}</span>
      //             </div>
      //           `,
      //       data() {
      //         return {
      //           data: {},
      //           fieldName: ''
      //         }
      //       },
      //       mounted() {
      //         this.fieldName = i18n.t('供应商名称')
      //       }
      //     })
      //   }
      // },
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'quotaCode',
      width: '200',
      headerText: i18n.t('定额编码'),
      allowEditing: true,
      headerTemplate: () => {
        return {
          template: Vue.component('requiredCell', {
            template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{fieldName}}</span>
                  </div>
                `,
            data() {
              return {
                data: {},
                fieldName: ''
              }
            },
            mounted() {
              this.fieldName = i18n.t('定额编码')
            }
          })
        }
      },
      editTemplate: () => {
        return {
          template: selectAllAttrCode
        }
      }
    },
    {
      field: 'quotaName',
      width: '150',
      headerText: i18n.t('定额名称'),
      headerTemplate: () => {
        return {
          template: Vue.component('requiredCell', {
            template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{fieldName}}</span>
                  </div>
                `,
            data() {
              return {
                data: {},
                fieldName: ''
              }
            },
            mounted() {
              this.fieldName = i18n.t('定额名称')
            }
          })
        }
      },
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'parentCode',
      width: 0,
      allowResizing: false,
      headerText: i18n.t('定额父类编码'),
      ignore: true,
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'parentName',
      width: 0,
      allowResizing: false,
      headerText: i18n.t('定额父类名称'),
      ignore: true,
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'quotaType',
      width: 0,
      allowResizing: false,
      headerText: i18n.t('定额父类类型'),
      ignore: true,
      allowEditing: false,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'quotaUnit',
      width: '200',
      headerText: i18n.t('定额单位'),
      allowEditing: true,
      headerTemplate: () => {
        return {
          template: Vue.component('requiredCell', {
            template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{fieldName}}</span>
                  </div>
                `,
            data() {
              return {
                data: {},
                fieldName: ''
              }
            },
            mounted() {
              this.fieldName = i18n.t('定额单位')
            }
          })
        }
      },
      editTemplate: () => {
        return {
          template: selectAllUnitCode
        }
      }
    },
    {
      field: 'quotaUnitCode',
      width: 145,
      headerText: i18n.t('定额单位编码'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'quotaValue',
      width: '150',
      headerText: i18n.t('定额值'),
      edit: editInstance.create({
        getEditConfig: ({ rowData }) => ({
          type: 'number',
          min: 0,
          precision: '2',
          disabled: rowData.quotaType != 0,
          readonly: rowData.quotaType != 0
        })
      })
    },
    {
      field: 'validDateStart',
      width: '150',
      headerText: i18n.t('有效期从'),
      allowEditing: true,
      headerTemplate: () => {
        return {
          template: Vue.component('requiredCell', {
            template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{fieldName}}</span>
                  </div>
                `,
            data() {
              return {
                data: {},
                fieldName: ''
              }
            },
            mounted() {
              this.fieldName = i18n.t('有效期从')
            }
          })
        }
      },
      formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
      edit: editInstance.create({
        getEditConfig: ({ rowData }) => ({
          type: 'date',
          format: 'yyyy-MM-dd',
          'time-stamp': true,
          'show-clear-button': false,
          'allow-edit': false,
          disabled: !!rowData.completeFlag,
          readonly: !!rowData.completeFlag
        })
      })
    },
    {
      field: 'validDateEnd',
      headerText: i18n.t('有效期至'),
      width: '150',
      allowEditing: true,
      headerTemplate: () => {
        return {
          template: Vue.component('requiredCell', {
            template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{fieldName}}</span>
                  </div>
                `,
            data() {
              return {
                data: {},
                fieldName: ''
              }
            },
            mounted() {
              this.fieldName = i18n.t('有效期至')
            }
          })
        }
      },
      formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
      edit: editInstance.create({
        getEditConfig: ({ rowData }) => ({
          type: 'date',
          format: 'yyyy-MM-dd',
          'time-stamp': true,
          'show-clear-button': false,
          'allow-edit': false,
          disabled: !!rowData.completeFlag,
          readonly: !!rowData.completeFlag
        })
      })
    },
    {
      field: 'remark',
      width: '150',
      headerText: i18n.t('备注')
    }
    // {
    //   field: "quotaCode",
    //   headerText: i18n.t("成本项编码"),
    //   allowEditing: false,
    // },
    // {
    //   field: "quotaName",
    //   headerText: i18n.t("成本项名称"),
    //   allowEditing: false,
    // },
    // {
    //   field: "quotaValue",
    //   headerText: i18n.t("数量"),
    //   allowEditing: true,
    //   headerTemplate: () => {
    //     return {
    //       template: Vue.component("requiredCell", {
    //         template: `
    //       <div class="headers">
    //         <span style="color: red">*</span>
    //         <span class="e-headertext">{{fieldName}}</span>
    //       </div>
    //     `,
    //         data() {
    //           return {
    //             data: {},
    //             fieldName: "",
    //           };
    //         },
    //         mounted() {
    //           this.fieldName = this.$t("数量");
    //         },
    //       }),
    //     };
    //   },
    //   edit: editInstance.create({
    //     getEditConfig: ({ rowData }) => ({
    //       type: "number",
    //       min: 1,
    //       disabled: !!rowData.completeFlag,
    //       readonly: !!rowData.completeFlag,
    //     }),
    //   }),
    // },
  ]
}
// export const columnData = [
//   {
//     width: "50",
//     type: "checkbox",
//   },
//   {
//     field: "itemCode",
//     headerText: i18n.t("物料编码"),
//   },
//   {
//     field: "itemName",
//     headerText: i18n.t("物料名称"),
//   },
//   {
//     field: "costFactorCode",
//     headerText: i18n.t("成本因子编码"),
//   },
//   {
//     field: "costFactorName",
//     headerText: i18n.t("成本因子名称"),
//   },
//   {
//     field: "supplierCode",
//     headerText: i18n.t("供应商编码"),
//   },
//   {
//     field: "supplierName",
//     headerText: i18n.t("供应商名称"),
//   },
//   // {
//   //   field: "quotaCode",
//   //   headerText: i18n.t("定额编码"),
//   // },
//   // {
//   //   field: "quotaName",
//   //   headerText: i18n.t("定额名称"),
//   // },
//   // {
//   //   field: "quotaUnit",
//   //   headerText: i18n.t("定额单位"),
//   // },
//   // {
//   //   field: "quotaValue",
//   //   headerText: i18n.t("定额值"),
//   // },
//   {
//     field: "validDateStart",
//     headerText: i18n.t("有效期从"),
//     allowEditing: true,
//     headerTemplate: () => {
//       return {
//         template: Vue.component("requiredCell", {
//           template: `
//                 <div class="headers">
//                   <span style="color: red">*</span>
//                   <span class="e-headertext">{{fieldName}}</span>
//                 </div>
//               `,
//           data() {
//             return {
//               data: {},
//               fieldName: "",
//             };
//           },
//           mounted() {
//             this.fieldName = i18n.t("有效期从");
//           },
//         }),
//       };
//     },
//     formatter: Formatter.createFmtDatetime("YYYY-MM-DD"),
//     edit: editInstance.create({
//       getEditConfig: ({ rowData }) => ({
//         type: "date",
//         format: "yyyy-MM-dd",
//         "time-stamp": true,
//         "show-clear-button": false,
//         disabled: !!rowData.completeFlag,
//         readonly: !!rowData.completeFlag,
//       }),
//     }),
//   },
//   // {
//   //   field: "validDateEnd",
//   //   headerText: i18n.t("有效期至"),
//   // },
//   // {
//   //   field: "remark",
//   //   headerText: i18n.t("备注"),
//   // },
// ];
