/* eslint-disable no-prototype-builtins */
<template>
  <div class="top-info">
    <!-- 顶部信息 -->
    <nav>
      <div class="detail-info">
        <div class="name-wrap"></div>
        <div class="btns-wrap">
          <mt-button
            class="e-flat"
            v-if="routeId != '' && (condition == '0' || condition == '4' || condition == '5')"
            @click="clickButtonDelete"
            >{{ $t('删除') }}</mt-button
          >
          <mt-button
            class="e-flat"
            v-if="
              !routeId ||
              condition === '0' ||
              condition == '3' ||
              condition == '4' ||
              condition == '5'
            "
            @click="clickButtonSave"
            >{{ $t('保存') }}</mt-button
          >
          <mt-button
            class="e-flat"
            v-if="
              (!routeId ||
                condition === '0' ||
                condition == '3' ||
                condition == '4' ||
                condition == '5') &&
              allowSubmit
            "
            @click="clickButtonSubmit"
            >{{ $t('提交') }}</mt-button
          >
          <mt-button class="e-flat" @click="returnBtn">{{ $t('返回') }}</mt-button>
        </div>
      </div>
      <div class="formInput">
        <mt-form ref="dialogRef" class="dialogRef" :model="formObject" :rules="formRules">
          <mt-form-item prop="quotaNo" :label="$t('定额单号')" label-style="top" label-width="80px">
            <mt-input
              v-model="formObject.quotaNo"
              float-label-type="Never"
              :disabled="true"
              :placeholder="$t('定额单号')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            prop="createUserName"
            :label="$t('创建人')"
            label-style="top"
            label-width="80px"
          >
            <mt-input
              v-model="formObject.createUserName"
              float-label-type="Never"
              :disabled="true"
              :placeholder="$t('创建人')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            prop="createTime"
            :label="$t('创建日期')"
            label-style="top"
            label-width="80px"
          >
            <mt-input
              v-model="formObject.createTime"
              float-label-type="Never"
              :disabled="true"
              :placeholder="$t('创建日期')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="status" :label="$t('状态')" label-style="top" label-width="80px">
            <mt-input
              v-model="formObject.status"
              float-label-type="Never"
              :disabled="true"
              :placeholder="$t('状态')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            prop="isNotHvac"
            :label="$t('是否暖通')"
            label-style="top"
            label-width="80px"
          >
            <mt-select
              :disabled="condition == 1 || condition == 2"
              v-model="formObject.isNotHvac"
              float-label-type="Never"
              :data-source="isNotHvacArr"
              @change="changeIsNotHvac"
              :placeholder="$t('是否暖通')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="isNew" :label="$t('是否新建')" label-style="top" label-width="80px">
            <mt-select
              :disabled="condition == 1 || condition == 2"
              v-model="formObject.isNew"
              float-label-type="Never"
              :data-source="isNotHvacArr"
              :placeholder="$t('是否暖通')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item ref="state" :label="$t('附件')" label-style="top" label-width="80px">
            <mt-common-uploader
              v-if="routeId"
              ref="uploadFile"
              :is-view="condition == 1 || condition == 2"
              :is-single-file="false"
              :save-url="saveUrl"
              :download-url="downloadUrl"
              type="line"
              v-model="fileList"
              @confirm="setFile"
            ></mt-common-uploader>
            <mt-input
              v-else
              float-label-type="Never"
              :disabled="true"
              :placeholder="$t('请先保存后再上传')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </nav>

    <mt-template-page
      ref="tepPage"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    ></mt-template-page>
  </div>
</template>
<script>
import { toolbar, columnData } from './config/index'
import { utils } from '@mtech-common/utils'
import cloneDeep from 'lodash/cloneDeep'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  data() {
    return {
      fileList: [],
      saveUrl: '/api/file/user/file/uploadPublic?useType=1',
      downloadUrl: '/api/file/user/file/downloadPrivateFile?useType=1',
      allowSubmit: true,
      pageConfig: [],
      formObject: {
        quotaNo: '', //定额单号
        createUserName: '', //创建人
        createTime: '', //创建日期
        status: '', //状态
        isNotHvac: '', //是否暖通
        isNew: '' //是否新建
        // remark: "", //备注
      },
      formRules: {
        isNotHvac: [
          {
            required: true,
            message: this.$t('请选择是否暖通'),
            trigger: 'blur'
          }
        ],
        isNew: [
          {
            required: true,
            message: this.$t('请选择是否新建'),
            trigger: 'blur'
          }
        ]
      },
      isNotHvacArr: [
        { text: this.$t('否'), value: '0' },
        { text: this.$t('是'), value: '1' }
      ],
      // 状态
      statuArr: [
        { text: this.$t('草稿'), value: '0' },
        { text: this.$t('审批'), value: '1' },
        { text: this.$t('审批通过'), value: '2' },
        { text: this.$t('审批驳回'), value: '3' },
        { text: this.$t('审批废弃'), value: '4' },
        { text: this.$t('审批撤回'), value: '5' }
      ],
      routeId: '',
      condition: null, //状态
      quotaMaintenanceItemRequests: []
    }
  },
  mounted() {
    this.initData()
  },
  activated() {
    this.initData()
  },
  methods: {
    async initData() {
      this.routeId = this.$route?.query?.id ?? ''
      //新增
      if (this.routeId == '') {
        let _cols = columnData()
        console.log('_columnData', _cols)
        // this.initialInterfaceCall();
        this.pageConfig = [
          {
            toolbar: toolbar,
            useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
            grid: {
              allowFiltering: true,
              editSettings: {
                allowEditing: true,
                allowAdding: true,
                allowDeleting: true,
                showConfirmDialog: false,
                newRowPosition: 'Top',
                mode: 'Normal' // 选择默认模式，双击整行可以进行编辑
              },
              columnData: _cols,
              dataSource: [],
              // queryCellInfo: this.customiseCell,
              class: 'pe-edit-grid custom-toolbar-grid'
            }
          }
        ]
      }
      //编辑
      else {
        await this.getFile()
        this.editortochoose()
      }
    },
    setFile() {
      console.log('setFile--', this.fileList)
      let _records = []
      this.fileList.forEach((item) =>
        _records.push({
          ...item,
          docId: this.$route.query.id,
          sysFileId: item?.sysFileId || item.id
        })
      )
      this.$API.rfxList.saveFiles(_records).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
        }
      })
    },
    getFile() {
      this.$API.rfxList.getFile({ docId: this.$route.query.id }).then((res) => {
        if (res.code == 200) {
          console.log('getFile', res)
          this.fileList = res.data
        }
      })
    },
    //新增
    initialInterfaceCall() {
      this.pageConfig[0].toolbar = toolbar
    },
    //编辑
    editortochoose() {
      let parameter = {
        requestId: this.routeId
      }
      this.$API.configuration.queryDetail(parameter).then((res) => {
        this.formObject.quotaNo = res.data.quotaNo
        this.formObject.createTime = res.data.createTime
        this.formObject.createUserName = res.data.createUserName
        this.formObject.remark = res.data.remark
        this.formObject.status = this.statuArr[res.data.status].text
        this.condition = String(res.data.status) //状态
        this.formObject.isNotHvac = String(res.data.isNotHvac)
        this.formObject.isNew = String(res.data.isNew)
        this.quotaMaintenanceItemRequests = cloneDeep(res.data.quotaMaintenanceItemRequests)
        let _toolbar =
          this.condition == '0' ||
          this.condition == '3' ||
          this.condition == '4' ||
          this.condition == '5'
            ? toolbar
            : [{ id: 'export', icon: 'icon_solid_Download', title: this.$t('导出') }]
        let _columnData
        if (this.formObject.status != '0') {
          _columnData = columnData()
          _columnData.map(() => {
            // item.allowEditing = false;
          })
        }
        console.log('_columnData', _columnData)
        let _config = [
          {
            toolbar: _toolbar,
            useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
            grid: {
              lineIndex: 1,
              allowFiltering: true,
              editSettings: {
                allowEditing:
                  res.data.status != '0' &&
                  res.data.status != '3' &&
                  res.data.status != '4' &&
                  res.data.status != '5'
                    ? false
                    : true,
                allowAdding: true,
                allowDeleting: true,
                showConfirmDialog: false,
                newRowPosition: 'Top',
                mode: 'Normal' // 选择默认模式，双击整行可以进行编辑
              },
              columnData: _columnData,
              asyncConfig: {
                url: '/sourcing/tenant/quotaMaintenanceHeader/queryItem',
                defaultRules: [
                  {
                    field: 'headerId',
                    operator: 'equal',
                    value: this.$route.query.id
                  }
                ]
              },
              // dataSource: res.data.quotaMaintenanceItemRequests,
              // queryCellInfo: this.customiseCell,
              class: 'pe-edit-grid custom-toolbar-grid'
            }
          }
        ]
        this.pageConfig = _config
      })
    },
    //删除
    clickButtonDelete() {
      let ids = [this.routeId]
      let parameter = {
        ids: ids
      }
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除所选数据？'),
          confirm: () => this.$API.configuration.deleteHeader(parameter)
        },
        success: () => {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$router.push({ name: `purchase-quotaConfig` })
        }
      })
    },
    //保存
    clickButtonSave(type = 'save') {
      this.endEdit()
      if (!this.formObject.isNotHvac) {
        this.$toast({
          content: this.$t('是否暖通不能为空'),
          type: 'warning'
        })
        return
      }
      if (!this.formObject.isNew) {
        this.$toast({
          content: this.$t('是否新建不能为空'),
          type: 'warning'
        })
        return
      }
      let parameter = {
        button: '0',
        id: this.routeId,
        isNotHvac: this.formObject.isNotHvac,
        isNew: this.formObject.isNew,
        quotaNo: '',
        remark: this.formObject.remark
      }

      this.$API.configuration.saveQuotaHeader(parameter).then((res) => {
        if (res.code == 200) {
          if (type == 'save') {
            this.$router.push({
              name: `purchase-quotaConfig-detail`,
              query: {
                id: res.data,
                key: this.$utils.randomString(),
                type: 1
              }
            })
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
          } else {
            this.$store.commit('startLoading')
            this.$API.configuration
              .submit({ ids: [this.routeId] })
              .then((res) => {
                this.$store.commit('endLoading')
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('操作成功'),
                    type: 'success'
                  })
                  this.$router.push({ name: 'purchase-quotaConfig' })
                }
              })
              .catch(() => {
                this.$store.commit('endLoading')
              })
          }
        }
      })
    },
    //提交
    clickButtonSubmit() {
      this.clickButtonSave('submit')
    },
    returnBtn() {
      if (this.$route?.query?.type == 1) {
        this.$router.push('purchase-quotaConfig')
      } else {
        this.$router.go(-1)
      }
    },
    //是否暖通
    changeIsNotHvac(e) {
      console.log(e, '暖通')
    },
    //备注
    changeRemark(e) {
      if (e.length >= 10) {
        this.$toast({
          content: this.$t('字数不得超过10字'),
          type: 'warning'
        })
      } else {
        this.formObject.remark = e
      }
    },
    //点击表头
    handleClickToolBar(e) {
      if (this.$route.query?.id) {
        if (e.toolbar.id == 'Cancel') {
          e.grid.closeEdit()
          this.allowSubmit = true
          setTimeout(() => {
            this.$refs.tepPage.refreshCurrentGridData()
          }, 1)
          return
        }
        const _selectRows = e.gridRef.getMtechGridRecords()
        if (_selectRows.length <= 0 && e.toolbar.id == 'delete') {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        if (e.toolbar.id == 'Add') {
          e.grid.addRecord()
        } else if (e.toolbar.id == 'delete') {
          let idList = []
          _selectRows.map((item) => {
            if (item?.id) {
              idList.push(item.id)
            }
          })
          if (idList.length > 0) {
            this.deleteDetail(idList)
          } else {
            this.$refs.tepPage.refreshCurrentGridData()
          }
        } else if (e.toolbar.id == 'save') {
          this.endEdit()
        } else if (e.toolbar.id == 'export') {
          this.handleExport()
        }
      } else if (e.toolbar.id != 'refreshDataByLocal' && e.toolbar.id != 'ResetSearch') {
        this.$toast({
          content: this.$t('请先保存头部信息！'),
          type: 'warning'
        })
      }
    },
    //导出
    handleExport() {
      const params = {
        page: { current: 1, size: 10000 },
        defaultRules: [
          {
            field: 'id',
            operator: 'equal',
            value: this.$route.query.id
          }
        ]
      } // 筛选条件
      this.$API.configuration.quotaCfgExport(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    //渲染值
    processHeader(configValue) {
      if (configValue && configValue.length > 0) {
        let _dataSource = utils.cloneDeep(this.pageConfig[0].grid.dataSource)
        _dataSource.unshift(...configValue)
        this.$set(this.pageConfig[0].grid, 'dataSource', _dataSource)
      }
    },
    // 不可修改的单元格，修改背景色
    customiseCell(args) {
      if (!args.column.allowEditing) {
        args.cell.classList.add('bg-grey')
      } else {
        args.cell.classList.add('bg-orange')
      }
    },
    actionBegin(args) {
      console.log(args)
      if (args.requestType === 'add') {
        this.allowSubmit = false
        args.data.validDateEnd = '253402185600000'
      }
      if (args.requestType === 'beginEdit') {
        this.allowSubmit = false
      } else if (args.requestType === 'sorting') {
        if (this.allowSubmit == false) {
          args.cancel = true
        }
      }
    },
    actionComplete(e) {
      const { requestType, rowIndex } = e
      if (requestType === 'save') {
        if (!this.isValidSaveData(e.data)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.tepPage.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          this.$nextTick(() => {
            this.handleSaveFn(e.data)
            this.allowSubmit = true
          })
        }
      }
      if (e.requestType === 'refresh' || requestType === 'cancel') {
        this.allowSubmit = true
      }
    },
    // 校验数据
    isValidSaveData(data) {
      const {
        itemCode,
        quotaCode,
        quotaUnit,
        validDateStart,
        validDateEnd,
        quotaType,
        // supplierCode,
        quotaValue
      } = data
      let valid = true
      if (!itemCode) {
        this.$toast({ content: this.$t('请选择物料'), type: 'warning' })
        valid = false
        // } else if (!supplierCode) {
        //   this.$toast({ content: this.$t('请选择供应商'), type: 'warning' })
        //   valid = false
      } else if (!quotaCode) {
        this.$toast({
          content: this.$t('请选择定额编码'),
          type: 'warning'
        })
        valid = false
      } else if (!quotaUnit) {
        this.$toast({ content: this.$t('请选择定额单位'), type: 'warning' })
        valid = false
      } else if (!validDateStart || !validDateEnd) {
        this.$toast({ content: this.$t('请选择有效期'), type: 'warning' })
        valid = false
      } else if (quotaType == 0 && quotaValue == null) {
        this.$toast({ content: this.$t('请输入定额值'), type: 'warning' })
        valid = false
      }
      return valid
    },
    handleSaveFn(arg) {
      let _selectGridRecords = this.$refs.tepPage
        .getCurrentUsefulRef()
        .ejsRef.getCurrentViewRecords()
        .filter((e) => e.id == arg.id)
      let flag = false
      _selectGridRecords.forEach((e) => {
        e.headerId = this.$route.query.id
        e.quotaNo = this.formObject.quotaNo
      })
      for (const value of _selectGridRecords) {
        if (value.validDateStart > value.validDateEnd) {
          flag = true
        }
      }
      if (flag) {
        this.$toast({
          content: this.$t('开始日期不能大于失效日期'),
          type: 'warning'
        })
      } else {
        this.$API.configuration.saveQuotaDetail(_selectGridRecords).then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
            this.$refs.tepPage.refreshCurrentGridData()
          }
        })
      }
    },
    deleteDetail(ids) {
      // let ids = rows.map((e) => e.id)
      this.$API.configuration
        .deleteQuotaDetail(ids)
        .then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.tepPage.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.$refs.tepPage.refreshCurrentGridData()
        })
    },
    // 结束编辑状态
    endEdit() {
      let _current = this.$refs.tepPage.getCurrentTabRef()
      _current?.grid.endEdit()
    }
  }
}
</script>
<style lang="scss" scoped>
.top-info {
  height: 100%;
  width: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  nav {
    flex-shrink: 0;
    //按钮
    .detail-info {
      padding: 20px 20px 3px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .btns-wrap {
        /deep/ .mt-button {
          margin-right: 0;
          button {
            background: transparent;
            //border: 1px solid rgba(0, 70, 156, 0.1);
            border-radius: 4px;
            box-shadow: unset;
            padding: 6px 12px 4px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: 500;
            color: rgba(0, 70, 156, 1);
          }
        }
        // .pack-up {
        //   display: inline-block;
        //   position: relative;
        //   .rotate {
        //     position: absolute;
        //     right: -5px;
        //     top: 5px;
        //     transform: rotate(180deg);
        //     .mt-icons {
        //       color: #c8d5e9;
        //     }
        //   }
        // }
      }
    }
    //表单
    .formInput {
      width: 100%;
      display: flex;
      flex-direction: column;
      padding: 0 10px;
      // .mt-form {
      //   width: 100%;
      //   padding: 20px;
      //   box-sizing: border-box;
      //   display: flex;
      //   align-items: center;
      //   // justify-content: space-between;
      //   justify-content: flex-start;
      //   flex-wrap: wrap;
      //   .mt-form-item {
      //     width: 360px;
      //   }
      //   .mt-form-item:nth-last-of-type(1) {
      //     width: 100%;
      //   }
      // }
      .dialogRef {
        width: 100%;
        display: grid;
        justify-content: space-between;
        flex-wrap: wrap;
        grid-template-columns: repeat(auto-fill, 300px);
        grid-gap: 10px;
        .mt-form-item {
          width: 300px;
        }
      }
    }
  }
  .mt-template-page {
    flex: 1;
    height: 100%;
  }
}
</style>
