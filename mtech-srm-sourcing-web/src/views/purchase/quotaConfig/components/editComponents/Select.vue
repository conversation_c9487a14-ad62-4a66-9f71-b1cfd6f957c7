<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <!-- 选择供应商 -->
      <mt-input
        :id="fieldName"
        v-model="data[fieldName]"
        disabled
        width="200"
        :placeholder="headerTxt"
      ></mt-input>
      <mt-icon style="width: 20px" name="icon_input_search" @click.native="showDialog"></mt-icon>
    </div>

    <mt-dialog
      ref="dialog"
      css-class="pc-item-dialog"
      :header="headerTxt"
      :buttons="buttons"
      @close="handleClose"
    >
      <mt-template-page
        v-show="dialogShow"
        ref="templateRef"
        :hidden-tabs="true"
        :template-config="pageConfig"
        @recordDoubleClick="recordDoubleClick"
      >
      </mt-template-page>
    </mt-dialog>
  </div>
</template>

<script>
import { columnData } from './config/index' // 命名要与field code一致
export default {
  data() {
    return {
      data: {},
      fieldName: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [],
          gridId: '7db7f524-08fd-4996-ba0c-433b0cfab4c7',
          grid: {
            allowPaging: true,
            allowSelection: true,
            allowFiltering: true,
            selectionSettings: {
              checkboxOnly: false
            },
            columnData,
            asyncConfig: {
              url: '/masterDataManagement/tenant/supplier/paged-query'
            }
          }
        }
      ],
      fields: {},
      headerTxt: this.$t('选择供应商'),
      dialogShow: false,
      itemCode: ''
    }
  },
  mounted() {
    this.fieldName = this.data.column.field
    this.itemCode = this.data.itemCode
  },

  methods: {
    // 双击行，也进行提交
    recordDoubleClick(args) {
      console.log('recordDoubleClick', args)
      this.confirm([args.rowData])
    },
    // 提交
    confirm(records) {
      let _records = records
      if (!_records || !_records.length) {
        _records = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords()
      }
      if (!_records.length) return
      console.log('选择到的物料信息：', _records[0])
      if (_records[0]) {
        let selectedRowInfo = _records[0]
        selectedRowInfo = {
          supplierId: selectedRowInfo?.id,
          supplierCode: selectedRowInfo?.supplierCode,
          supplierName: selectedRowInfo?.supplierName
        }
        this.data[this.fieldName] = selectedRowInfo[this.fieldName]
        this.$bus.$emit('supplierNameChange', selectedRowInfo.supplierName)
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'itemCode',
          itemInfo: {
            supplierName: selectedRowInfo.supplierName,
            supplierId: selectedRowInfo.id
          }
        })
        this.handleClose()
      }
    },
    showDialog() {
      this.dialogShow = true
      this.$refs.dialog.ejsRef.show()
    },
    handleClose() {
      this.dialogShow = false
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
</style>

<style lang="scss">
.pc-item-dialog {
  .mt-data-grid {
    height: 100%;
    > .e-grid {
      height: calc(100% - 40px) !important;
      // display: flex;

      > .e-gridcontent {
        flex: 1;
        overflow: auto;
      }
    }

    .e-rowcell.e-active {
      background: #e0e0e0 !important;
    }
  }
}
</style>
