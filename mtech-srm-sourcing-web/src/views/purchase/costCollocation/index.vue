<template>
  <div class="strategy-maps-page">
    <mt-template-page
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { listToolBar, listColumnData, getListGridData } from './config'

export default {
  data() {
    return {
      pageConfig: [
        {
          toolbar: listToolBar,
          grid: {
            allowFiltering: true,
            columnData: listColumnData,
            dataSource: getListGridData(5)
          }
        }
      ]
    }
  },
  methods: {
    handleClickCellTitle(e) {
      if (e.field == 'column2') {
        this.$router.push({
          path: `/sourcing/cost-collocation-detail`
        })
      }
    }
  }
}
</script>
