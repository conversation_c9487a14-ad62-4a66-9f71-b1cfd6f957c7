<template>
  <div class="cost-detail">
    <!-- 顶部设置 -->
    <div class="setting-box mt-flex">
      <div class="setting-msg flex1">
        <div class="title-line">
          <div class="mt-flex normal-line">
            <mt-input
              :disabled="isDisableName"
              class="input-item"
              v-model="moduleName"
              type="text"
              placeholder="$t('请输入成本项模板编号')"
            ></mt-input>
            <div class="edit-box" @click="changeDisable">
              <mt-icon class="icon_solid_editsvg" name="edit-06" />
            </div>

            <div class="tips-module">{{ $t('草稿') }}</div>
            <div class="version-module">V.0.0.1</div>
          </div>

          <div class="mt-flex sec-line normal-line mr-10">
            <mt-input
              :disabled="isDisableProject"
              class="input-item"
              v-model="projectName"
              type="text"
              placeholder="$t('成本项名称')"
            ></mt-input>

            <div class="edit-box" @click="changeDisableProject">
              <mt-icon class="icon_solid_editsvg" name="edit-06" />
            </div>

            <div class="create-man"><mt-icon name="icon_Creator" /> {{ $t('创建人：宋乐宇') }}</div>
            <div class="create-time">
              <mt-icon name="icon_Require" />
              {{ $t('创建时间：') }}2021/12/03 12:00
            </div>
          </div>

          <div class="tab-box">
            <div class="tab-item" :class="{ active: tabId === 0 }" @click="changTab(0)">
              {{ $t('关联物料') }}
            </div>
            <div class="tab-item" :class="{ active: tabId === 1 }" @click="changTab(1)">
              {{ $t('成本项') }}
            </div>
          </div>
        </div>
      </div>
      <div class="setting-btn">
        <mt-switch v-model="isOpen" on-label="$t('启用')" off-label="$t('关闭')"></mt-switch>
      </div>
    </div>

    <div class="tab-grid">
      <template v-if="tabId === 0">
        <related-materials></related-materials>
      </template>
      <template v-if="tabId === 1">
        <cost-item></cost-item>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  components: {
    costItem: () =>
      import(
        /* webpackChunkName: "router/purchase/costCollocation/components/costItem" */ './components/costItem.vue'
      ),
    relatedMaterials: () =>
      import(
        /* webpackChunkName: "router/purchase/costCollocation/components/relatedMaterials" */ './components/relatedMaterials.vue'
      )
  },
  data() {
    return {
      isDisableName: true,
      isDisableProject: true,
      moduleName: this.$t('成本项模板编号'),
      projectName: this.$t('成本项名称'),
      isOpen: false,

      tabId: 0
    }
  },
  methods: {
    changeDisable() {
      this.isDisableName = !this.isDisableName
    },
    changeDisableProject() {
      this.isDisableProject = !this.isDisableProject
    },
    changTab(id) {
      this.tabId = id
    }
  }
}
</script>

<style lang="scss" scoped>
.mr-10 {
  margin-top: 10px;
}
.cost-detail {
  .setting-box {
    width: 100%;
    box-sizing: border-box;
    justify-content: space-between;
    margin-top: 20px;
    padding: 20px;
    background: linear-gradient(270deg, rgba(99, 134, 193, 0.04) 0%, rgba(99, 134, 193, 0.08) 100%),
      linear-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 1));
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px 8px 0 0;

    .normal-line {
      display: flex;
      align-items: center;

      .edit-box {
        width: 20px;
        height: 20px;
      }

      .tips-module {
        margin-left: 10px;
        padding: 0 4px;
        height: 20px;
        line-height: 20px;
        background: rgba(237, 161, 51, 0.1);
        border-radius: 2px;
        font-size: 12px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(237, 161, 51, 1);
      }
      .version-module {
        height: 20px;
        padding: 0 4px;
        margin-left: 10px;
        line-height: 20px;
        background: rgba(99, 134, 193, 0.1);
        border-radius: 2px;
        font-size: 12px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(99, 134, 193, 1);
      }

      .create-man {
        display: flex;
        align-items: center;
        margin-left: 30px;
        font-size: 14px;
        line-height: 16px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(157, 170, 191, 1);
        i {
          margin-right: 4px;
          display: inline-block;
          width: 12px;
          height: 12px;
          font-size: 12px;
        }
      }
      .create-time {
        margin-left: 30px;
        font-size: 14px;
        line-height: 16px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(157, 170, 191, 1);
        i {
          margin-right: 4px;
          display: inline-block;
          width: 12px;
          height: 12px;
          font-size: 12px;
        }
      }
    }

    .setting-btn {
      width: 100px;
      display: flex;
      align-items: center;
    }

    .tab-box {
      height: 26px;
      margin-top: 20px;
      display: flex;
      .tab-item {
        font-size: 14px;
        height: 26px;
        line-height: 26px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(154, 154, 154, 1);
        margin-left: 50px;
        cursor: pointer;
      }
      .tab-item:nth-child(1) {
        margin-left: 12px;
      }
      .active {
        padding: 0 10px;
        height: 26px;
        line-height: 26px;
        background: rgba(0, 70, 156, 0.06);
        border: 1px solid rgba(0, 70, 156, 0.1);
        border-radius: 4px;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
      }
    }
  }
}
</style>

<style lang="scss">
.normal-line {
  .mt-input {
    display: block;
    width: 160px;
    height: 20px;
    .e-input-group {
      width: 160px;
      height: 20px !important;
      min-height: 20px !important;
      background: transparent !important;
      border: none;
      .e-control,
      .e-input {
        width: 160px;
        height: 20px !important;
        padding: 0 !important;
        min-height: 20px !important;
        background: transparent !important;
      }
    }
  }
}
.sec-line {
  .mt-input {
    display: block;
    width: 70px;
    height: 20px;
    .e-input-group {
      width: 70px;
      height: 20px !important;
      min-height: 20px !important;
      background: transparent !important;
      border: none;
      .e-control,
      .e-input {
        width: 70px;
        height: 20px !important;
        padding: 0 !important;
        min-height: 20px !important;
        background: transparent !important;
      }
    }
  }
}
.edit-btn {
  display: inline-block;
  width: 12px;
  height: 12px;
  color: #6386c1;
  padding: 4px;
  margin-left: 4px;
  cursor: pointer;
}
</style>
