<!-- 关联物料 grid -->
<template>
  <div class="RM-box">
    <mt-template-page ref="templateRef" :padding-top="true" :template-config="pageConfig" />
  </div>
</template>

<script>
import { RMToolBar, RMColumnData, RMListGridData } from '../config'
export default {
  data() {
    return {
      pageConfig: [
        {
          toolbar: RMToolBar,
          grid: {
            allowFiltering: true,
            columnData: RMColumnData,
            dataSource: RMListGridData(5)
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped></style>
