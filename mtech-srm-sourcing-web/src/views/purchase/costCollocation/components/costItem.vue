<template>
  <div class="cost-box mt-flex">
    <div class="left-tree">
      <div class="banner mt-flex">
        <mt-icon class="createproject" name="a-icon_solid_Createproject" />
        <span>{{ $t('新增一级') }}</span>
      </div>

      <div class="tree-box">
        <mt-treeView :fields="filedsTemplate" :node-template="Template"></mt-treeView>
      </div>
    </div>

    <div class="right-grid">
      <div class="banner-top">
        <div class="banner-title">000001</div>
        <div class="sec-title mt-flex">
          <div class="desc">{{ $t('人力成本') }}</div>
          <div class="desc">{{ $t('这里是成本项描述') }}</div>
          <div class="create-man"><mt-icon name="icon_Creator" /> {{ $t('创建人：宋乐宇') }}</div>
          <div class="create-time">
            <mt-icon name="icon_Require" />
            {{ $t('创建时间：') }}2021/12/03 12:00
          </div>
        </div>
      </div>
      <div class="banner-grid">
        <mt-template-page
          ref="templateRef"
          :template-config="pageConfig"
          @handleClickToolBar="handleClickToolBar"
          @handleClickCellTool="handleClickCellTool"
          @handleClickCellTitle="handleClickCellTitle"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { CSToolBar, CSColumnData, CSListGridData } from '../config'
export default {
  data() {
    let _this = this
    return {
      pageConfig: [
        {
          toolbar: CSToolBar,
          grid: {
            allowFiltering: true,
            columnData: CSColumnData,
            dataSource: CSListGridData(5)
          }
        }
      ],
      filedsTemplate: {
        dataSource: [
          { id: 1, name: 'Favorites', hasChild: true },
          { id: 2, pid: 1, name: 'Sales Reports', count: '4' },
          { id: 3, pid: 1, name: 'Sent Items' },
          { id: 4, pid: 1, name: 'Marketing Reports ', count: '6' },
          {
            id: 5,
            name: 'My Folder',
            hasChild: true,
            expanded: true
          },
          { id: 6, pid: 5, name: 'Inbox', count: '20' },
          {
            id: 7,
            pid: 5,
            name: 'Drafts',
            selected: true,
            count: '5'
          },
          { id: 8, pid: 5, name: 'Deleted Items' },
          { id: 9, pid: 5, name: 'Sent Items' },
          { id: 10, pid: 5, name: 'Sales Reports', count: '4' },
          { id: 11, pid: 5, name: 'Marketing Reports', count: '6' },
          { id: 12, pid: 5, name: 'Outbox' }
        ],
        id: 'id',
        parentID: 'pid',
        text: 'name',
        hasChildren: 'hasChild'
      },
      Template: function () {
        return {
          template: Vue.component('minPlusBtns', {
            template: `<div class="action-boxs">
                            <div class="name-box">{{ data.name }}</div>
                            <div class="btn-box">
                                <div class="plus" @click="plus(data)">
                                <mt-icon
                                    class="createproject"
                                    name="icon_solid_add"
                                /></div>
                                <div class="minus" @click="minus(data)">
                                    <mt-icon
                                        class="createproject"
                                        name="icon_solid_delete_2"
                                    />
                                </div>
                            </div>
                        </div>`,
            data() {
              return { data: {} }
            },
            methods: {
              minus(data) {
                _this.minus(data)
              },
              plus(data) {
                _this.plus(data)
              }
            }
          })
        }
      }
    }
  },
  methods: {
    minus() {
      this.$toast({ content: '警告 - 删除', type: 'warning' })
    },
    plus() {
      this.$toast({ content: '警告 - 添加', type: 'warning' })
    }
  }
}
</script>

<style lang="scss" scoped>
.cost-box {
  .left-tree {
    width: 400px;
    background: #fff;
    .banner {
      height: 50px;
      border-bottom: 1px solid rgba(232, 232, 232, 1);
      background: linear-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)),
        linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 1));
      i {
        margin-right: 4px;
      }
      height: 50px;
      align-items: center;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(79, 91, 109, 1);
      line-height: 50px;
      padding: 0 40px;
    }
  }

  .right-grid {
    width: 100%;
    margin: 10px 0 0 10px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 4px;

    .banner-top {
      padding: 20px;
      background: linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 1) 100%);
      border-bottom: 1px solid rgba(232, 232, 232, 1);

      .banner-title {
        font-size: 20px;
        font-family: PingFangSC;
        font-weight: 600;
        color: rgba(41, 41, 41, 1);
      }

      .sec-title {
        margin-top: 10px;
        .desc {
          margin-right: 30px;
        }
        .create-man {
          display: flex;
          align-items: center;
          font-size: 14px;
          line-height: 16px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(157, 170, 191, 1);
          i {
            margin-right: 4px;
            display: inline-block;
            width: 12px;
            height: 12px;
            font-size: 12px;
          }
        }
        .create-time {
          margin-left: 30px;
          font-size: 14px;
          line-height: 16px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(157, 170, 191, 1);
          i {
            margin-right: 4px;
            display: inline-block;
            width: 12px;
            height: 12px;
            font-size: 12px;
          }
        }
      }
    }
  }
}
</style>

<style lang="scss">
.cost-box {
  .mt-tree-view {
    width: 100%;
  }
  .e-icon-wrapper {
    display: flex;
    .e-list-text {
      flex: 1;
      display: flex;
    }
  }
  .action-boxs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .name-box {
      height: 30px;
      line-height: 30px;
      font-size: 14px;
      font-family: 'PingFangSC';
      font-weight: normal;
      color: #232b39;
    }

    .btn-box {
      width: 60px;
      display: flex;
      .minus {
        width: 16px;
        height: 16px;
        line-height: 16px;
        font-size: 16px;
        i {
          width: 16px;
          height: 16px;
          line-height: 16px;
          font-size: 16px;
          color: rgba(99, 134, 193, 1);
        }
      }
      .plus {
        width: 16px;
        height: 16px;
        font-size: 16px;
        line-height: 16px;
        margin-right: 20px;
        i {
          width: 16px;
          height: 16px;
          line-height: 16px;
          font-size: 16px;
          color: #ed5633;
        }
      }
    }
  }
  .e-list-text {
    width: 100% !important;
  }
}
</style>
