import { i18n } from '@/main.js'
import Vue from 'vue'
import MtRadio from '@mtech-ui/radio'
Vue.use(MtRadio)

export const listToolBar = [{ id: 'Paste', icon: 'icon_solid_edit', title: i18n.t('设为默认模板') }]
export const listColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'column2',
    cssClass: 'field-content',
    headerText: i18n.t('模板代码')
  },
  {
    field: 'column3',
    headerText: i18n.t('模板名称')
  },
  {
    field: 'column4',
    headerText: i18n.t('状态'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div class="action-boxs"><span class="status-text isStatus" v-if="data.column7 == 1">{{ $t('已发布') }}</span><span class="status-text unStatus" v-else>{{ $t('已停用') }}</span></div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'column5',
    headerText: i18n.t('创建人')
  },
  {
    field: 'column6',
    headerText: i18n.t('是否为默认模板'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div class="is-boxs"><span class="status-text isStatus" v-if="data.column7 == 1">{{ $t('已发布') }}</span><span class="status-text unStatus" v-else>{{ $t('是') }}</span></div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'column7',
    headerText: i18n.t('备注')
  }
]
export const getListGridData = (len = 10) => {
  let res = []
  for (let i = 0; i < len; i++) {
    res.push({
      column1: 'Code-' + i,
      column2: '模板代码-' + i,
      column3: '名称-' + i,
      column4: '模板-' + i,
      column5: '创建人-' + i,
      column6: '是' + i,
      column7: '备注' + i
    })
  }
  return res
}

export const RMToolBar = [
  [
    { id: 'Add', icon: 'a-icon_solid_Createproject', title: i18n.t('新增') },
    { id: 'Add', icon: 'icon_solid_Delete', title: i18n.t('删除') },
    { id: 'Paste', icon: 'icon_solid_upload', title: i18n.t('上传') },
    { id: 'Paste', icon: 'icon_solid_Download', title: i18n.t('下载') }
  ],
  ['Setting']
]

export const CSToolBar = [
  [
    { id: 'Add', icon: 'a-icon_solid_Createproject', title: i18n.t('新增') },
    { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') }
  ],
  ['Refresh']
]

export const RMColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'column2',
    cssClass: 'field-content',
    headerText: i18n.t('关联方式')
  },
  {
    field: 'column3',
    headerText: i18n.t('品类类型')
  },
  {
    field: 'column4',
    headerText: i18n.t('品类')
  },
  {
    field: 'column5',
    headerText: '零件/品项编码'
  },
  {
    field: 'column6',
    headerText: '工厂/地点'
  },
  {
    field: 'column7',
    headerText: i18n.t('创建人')
  },
  {
    field: 'column8',
    headerText: i18n.t('创建时间')
  },
  {
    field: 'column9',
    headerText: i18n.t('备注')
  }
]
export const CSColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'column2',
    cssClass: 'field-content',
    headerText: i18n.t('字段名')
  },
  {
    field: 'column3',
    headerText: i18n.t('字段类型')
  },
  {
    field: 'column4',
    headerText: i18n.t('是否必填'),
    template: () => {
      return {
        template: Vue.component('checkbox-list', {
          template: `
                      <div class="checkbox-list" v-if="!!data.list && data.list.length > 0">
                        <mt-radio v-model="radioVal" :dataSource="data.list"></mt-radio>
                      </div>`,
          data: function () {
            return {
              radioVal: ''
            }
          }
        })
      }
    }
  },
  {
    field: 'column5',
    headerText: i18n.t('限制值')
  },
  {
    field: 'column6',
    headerText: i18n.t('默认值')
  }
]

export const RMListGridData = (len = 10) => {
  let res = []
  for (let i = 0; i < len; i++) {
    res.push({
      column1: i18n.t('按品类') + i,
      column2: '按品类-' + i,
      column3: '名称-' + i,
      column4: '模板-' + i,
      column5: '创建人-' + i,
      column6: i18n.t('是') + i,
      column7: i18n.t('备注') + i,
      column8: i18n.t('备注') + i,
      column9: i18n.t('备注') + i
    })
  }
  return res
}
export const CSListGridData = (len = 10) => {
  let res = []
  for (let i = 0; i < len; i++) {
    res.push({
      column1: i18n.t('成本项名称') + i,
      column2: '成本项名称-' + i,
      column3: '输入型-' + i,
      column4: '是-' + i,
      column5: '个；包-' + i,
      column6: i18n.t('个') + i,
      list: [
        {
          label: i18n.t('是'),
          value: '0'
        },
        {
          label: i18n.t('否'),
          value: '1'
        }
      ]
    })
  }
  return res
}

export const detailListToolBar = [
  [{ id: 'Add', icon: 'a-icon_solid_Createproject', title: i18n.t('添加行') }]
]

export const detailListColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'column1',
    headerText: i18n.t('序号'),
    cellTools: [
      { id: 'Edit', icon: 'icon_Editor', title: i18n.t('编辑') },
      { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') }
    ]
  },
  {
    field: 'column2',
    headerText: i18n.t('关联方式')
  },
  {
    field: 'column3',
    headerText: i18n.t('品类类型')
  },
  {
    field: 'column4',
    headerText: i18n.t('品类')
  },
  {
    field: 'column5',
    headerText: i18n.t('物料编号')
  },
  {
    field: 'column6',
    headerText: i18n.t('业务类型')
  },
  {
    field: 'column7',
    headerText: i18n.t('更新人')
  },
  {
    field: 'column8',
    headerText: i18n.t('更新时间')
  }
]
export const getDetailListGridData = (len = 10) => {
  let res = []
  for (let i = 0; i < len; i++) {
    res.push({
      column1: 'Code-' + i,
      column2: '名称-' + i,
      column3: '描述-' + i,
      column4: '模板-' + i,
      column5: '模板-' + i,
      column6: '版本-' + i,
      column7: i % 2,
      column8: '2021-07-16 12:00:00',
      column9: '王晓-' + i
    })
  }
  return res
}
