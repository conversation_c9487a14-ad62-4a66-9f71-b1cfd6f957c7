<template>
  <div>
    <mt-tabs
      ref="mtTabsRef"
      :tab-id="$utils.randomString()"
      :e-tab="false"
      :data-source="tabs"
      :halt-select="false"
      @handleSelectTab="handleTabChange"
    />
    <keep-alive>
      <component ref="mainContent" :is="activeComponent" />
    </keep-alive>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tabList: [],
      tabs: [
        {
          title: this.$t('物料价格库'),
          tabCode: 'material'
          // permission: 'T_02_0205'
        },
        {
          title: this.$t('系列物料价格'),
          tabCode: 'series'
          // permission: 'T_02_0207'
        },
        {
          title: this.$t('模具'),
          tabCode: 'mold'
          // permission: 'T_02_0207'
        },
        {
          title: this.$t('结构阶梯'),
          tabCode: 'structure'
          // permission: 'T_02_0207'
        },
        {
          title: this.$t('美工阶梯'),
          tabCode: 'art'
          // permission: 'T_02_0207'
        },
        {
          title: this.$t('部品+模具'),
          tabCode: 'partMold'
          // permission: 'T_02_0207'
        },
        {
          title: this.$t('外发阶梯'),
          tabCode: 'outgoing'
          // permission: 'T_02_0207'
        },
        // {
        //   title: this.$t('物流价格记录'),
        //   tabCode: 'logistics'
        //   // permission: 'T_02_0206'
        // },
        {
          title: this.$t('ODMIN价格记录'),
          tabCode: 'odmin'
          // permission: 'T_02_0207'
        }
      ],
      activeTabIndex: 0,
      activeTabCode: 'material'
    }
  },
  computed: {
    activeComponent() {
      let comp = ''
      switch (this.activeTabCode) {
        case 'material':
          // 物料价格库
          comp = () => import('./tabs/materialPriceWarehouse.vue')
          break
        case 'series':
          // 系列物料价格
          comp = () => import('./tabs/seriesMaterialPrice.vue')
          break
        case 'mold':
          // 模具
          comp = () => import('./tabs/mould.vue')
          break
        case 'structure':
          // 结构阶梯
          comp = () => import('./tabs/structureLadder.vue')
          break
        case 'art':
          // 美工阶梯
          comp = () => import('./tabs/artLadder.vue')
          break
        case 'partMold':
          // 部品+模具
          comp = () => import('./tabs/partMold.vue')
          break
        case 'outgoing':
          // 外发阶梯
          comp = () => import('./tabs/outgoingLadder.vue')
          break
        // case 'logistics':
        //   // 物流价格记录
        //   comp = () => import('./tabs/logisticsPrice.vue')
        //   break
        case 'odmin':
          // ODMIN 价格记录
          comp = () => import('./tabs/odminPricing.vue')
          break
        case 'smt':
          // SMT 价格记录
          comp = () => import('./tabs/smtPricing.vue')
          break
        default:
          break
      }
      return comp
    }
  },
  mounted() {
    // let userPermission = window.elementPermissionSet
    // this.tabList = this.tabs.filter((item) => {
    //   return userPermission.includes(item.permission)
    // })
    // this.activeTabCode = this.tabList[0]?.tabCode
  },
  methods: {
    // 切换tab页签
    handleTabChange(index, item) {
      this.$refs.mtTabsRef.activeTab = index
      this.activeTabIndex = index
      this.activeTabCode = item.tabCode
    }
  }
}
</script>
<style lang="scss" scoped></style>
