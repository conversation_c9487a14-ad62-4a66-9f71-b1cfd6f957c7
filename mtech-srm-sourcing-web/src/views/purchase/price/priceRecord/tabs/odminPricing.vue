<template>
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="sourceCode" :label="$t('询价单号')" label-style="top">
          <mt-input
            v-model="searchFormModel.sourceCode"
            :show-clear-button="true"
            :placeholder="$t('请输入询价单号')"
          />
        </mt-form-item>
        <mt-form-item prop="sourceName" :label="$t('单据名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.sourceName"
            :show-clear-button="true"
            :placeholder="$t('单据名称')"
          />
        </mt-form-item>
        <mt-form-item prop="companyCodeList" :label="$t('公司编码')" label-style="top">
          <custom-select
            v-model="searchFormModel.companyCodeList"
            :label-name="$t('公司编码')"
            :placeholder="$t('请输入公司编码')"
          />
        </mt-form-item>
        <!-- <mt-form-item prop="companyName" :label="$t('公司名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.companyName"
            :show-clear-button="true"
            :placeholder="$t('请输入公司名称')"
          />
        </mt-form-item> -->
        <mt-form-item prop="factoryCodeList" :label="$t('工厂编码')" label-style="top">
          <custom-select
            v-model="searchFormModel.factoryCodeList"
            :label-name="$t('工厂编码')"
            :placeholder="$t('请输入工厂编码')"
          />
        </mt-form-item>
        <!-- <mt-form-item prop="siteName" :label="$t('工厂名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.siteName"
            :show-clear-button="true"
            :placeholder="$t('请输入工厂名称')"
          />
        </mt-form-item> -->
        <mt-form-item prop="purOrgCodeList" :label="$t('采购组织编码')" label-style="top">
          <custom-select
            v-model="searchFormModel.purOrgCodeList"
            :label-name="$t('采购组织编码')"
            :placeholder="$t('请输入采购组织编码')"
          />
        </mt-form-item>
        <!-- <mt-form-item prop="purOrgName" :label="$t('采购组织名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.purOrgName"
            :show-clear-button="true"
            :placeholder="$t('请输入采购组织名称')"
          />
        </mt-form-item> -->
        <mt-form-item prop="supplierCodeList" :label="$t('供应商编码')" label-style="top">
          <custom-select
            v-model="searchFormModel.supplierCodeList"
            :label-name="$t('供应商编码')"
            :placeholder="$t('请输入供应商编码')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.supplierName"
            :show-clear-button="true"
            :placeholder="$t('请输入供应商名称')"
          />
        </mt-form-item>
        <mt-form-item prop="materialCodeList" :label="$t('物料编码')" label-style="top">
          <custom-select
            v-model="searchFormModel.materialCodeList"
            :label-name="$t('物料编码')"
            :placeholder="$t('请输入物料编码')"
          />
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.itemName"
            :show-clear-button="true"
            :placeholder="$t('请输入物料名称')"
          />
        </mt-form-item>
        <mt-form-item prop="odfCode" :label="$t('ODF号')" label-style="top">
          <mt-input
            v-model="searchFormModel.odfCode"
            :show-clear-button="true"
            :placeholder="$t('请输入物料名称')"
          />
        </mt-form-item>
        <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
          <mt-input
            v-model="searchFormModel.createUserName"
            :show-clear-button="true"
            :placeholder="$t('请输入创建人')"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :allow-edit="false"
            :placeholder="$t('请选择创建时间')"
            :open-on-focus="true"
            @change="handleDateChange"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      grid-id="f976dd8f-d024-4a67-8bab-21c7f624b485"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="listColumns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import { getTimeList } from '@/utils/obj'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import { listToolbar, syncStatusList } from './config/index'
import XEUtils from 'xe-utils'
import CustomSelect from '@/components/customSelect'
export default {
  components: {
    ScTable,
    CollapseSearch,
    CustomSelect
  },
  mixins: [mixin],
  data() {
    return {
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 20,
        pageSizes: [20, 50, 200, 500, 1000, 2000]
      },
      pageInfo: {
        size: 20,
        current: 1
      },

      syncStatusList,
      toolbar: listToolbar,
      searchFormModel: {
        createUserName: JSON.parse(sessionStorage.getItem('userInfo'))?.username || null,
        createTime: getTimeList(3)
      },
      tableData: [],
      loading: false,
      type: 'list'
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    // 列配置
    listColumns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          width: 50,
          field: 'index',
          title: this.$t('序号')
        },
        {
          field: 'syncStatus',
          title: this.$t('同步状态'),
          minWidth: 140,
          slots: {
            default: ({ row }) => {
              const selectItem = this.syncStatusList.find((item) => item.value === row.syncStatus)
              const syncStatus = selectItem?.text
              return [<div>{syncStatus}</div>]
            }
          }
        },
        {
          field: 'syncMsg',
          title: this.$t('同步信息'),
          minWidth: 140
        },
        {
          field: 'sourceCode',
          title: this.$t('询价单号'),
          minWidth: 180,
          slots: {
            default: ({ row, column }) => {
              return [
                <a on-click={() => this.handleClickCellTitle(row, column)}>{row.sourceCode}</a>
              ]
            }
          }
        },
        {
          field: 'sourceName',
          title: this.$t('单据名称'),
          minWidth: 140
        },
        {
          field: 'companyCode',
          title: this.$t('公司'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [<span>{row.companyCode + '-' + row.companyName}</span>]
            }
          }
        },
        {
          field: 'factoryCode',
          title: this.$t('工厂'),
          minWidth: 200,
          slots: {
            default: ({ row }) => {
              return [<span>{row.factoryCode + '-' + row.factoryName}</span>]
            }
          }
        },
        {
          field: 'purchaseOrgCode',
          title: this.$t('采购组织'),
          minWidth: 200,
          slots: {
            default: ({ row }) => {
              return [<span>{row.purchaseOrgCode + '-' + row.purchaseOrgName}</span>]
            }
          }
        },
        {
          field: 'materialCode',
          title: this.$t('物料编号'),
          minWidth: 120
        },
        {
          field: 'materialName',
          title: this.$t('物料描述'),
          minWidth: 120
        },
        {
          field: 'categoryCode',
          title: this.$t('品类编码'),
          minWidth: 120
        },
        {
          field: 'categoryName',
          title: this.$t('品类名称'),
          minWidth: 120
        },
        {
          field: 'odfCode',
          title: this.$t('ODF号'),
          minWidth: 120
        },
        {
          field: 'purOrderNo',
          title: this.$t('合同编号'),
          minWidth: 120
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商编码'),
          minWidth: 120
        },
        {
          field: 'supplierName',
          title: this.$t('供应商名称')
        },
        {
          field: 'currencyCode',
          title: this.$t('币种'),
          minWidth: 200,
          slots: {
            default: ({ row }) => {
              return [<span>{row.currencyCode + '-' + row.currencyName}</span>]
            }
          }
        },
        {
          field: 'priceUnitName',
          title: this.$t('价格单位')
        },
        {
          field: 'unitPriceTaxed',
          title: this.$t('单价(含税)')
        },
        {
          field: 'unitPriceUntaxed',
          title: this.$t('单价(未税)')
        },
        {
          field: 'screenPrice',
          title: this.$t('屏价格')
        },
        {
          field: 'totalPrice',
          title: this.$t('汇总价格(未税)'),
          minWidth: 200
        },
        {
          field: 'refUntaxedUnitPrice',
          title: this.$t('参考单价未税')
        },
        {
          field: 'exchangeRateCode',
          title: this.$t('汇率'),
          minWidth: 200,
          slots: {
            default: ({ row }) => {
              return [<span>{row.exchangeRateCode + '-' + row.exchangeRateName}</span>]
            }
          }
        },
        {
          field: 'exchangeRateValue',
          title: this.$t('汇率值')
        },
        {
          field: 'taxRateCode',
          title: this.$t('税率'),
          minWidth: 200,
          slots: {
            default: ({ row }) => {
              return [<span>{row.taxRateCode + '-' + row.taxRateName}</span>]
            }
          }
        },
        {
          field: 'syncTime',
          title: this.$t('同步SAP完成时间'),
          minWidth: 200
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          minWidth: 140
        }
      ]
    }
  },
  mounted() {
    this.handleSearch()
  },
  activated() {
    this.handleSearch()
  },
  methods: {
    // 日期格式化
    handleDateChange(e) {
      if (e.startDate) {
        const startDate = XEUtils.toDateString(e.startDate, 'yyyy-MM-dd') + ' 00:00:00'
        const endDate = XEUtils.toDateString(e.endDate, 'yyyy-MM-dd') + ' 23:59:59'
        this.searchFormModel['createStartTime'] = XEUtils.timestamp(startDate)
        this.searchFormModel['createEndTime'] = XEUtils.timestamp(endDate)
      } else {
        this.searchFormModel['createStartTime'] = null
        this.searchFormModel['createEndTime'] = null
      }
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    // 查询
    async handleSearch(type) {
      // 解决修改查询条件后，查询结果有数据但未正常显示的问题（type: pageChange/refresh）
      if (!type) {
        this.pageInfo.current = 1
        this.$refs.pageRef.jumpNum = 1
        this.$refs.pageRef.currentPage = 1
      }
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      delete params.submitTime
      this.loading = true
      const res = await this.$API.priceService
        .getOdminPricingRecords(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.tableData?.forEach((item, index) => {
          item.index = index + 1
        }) || []
        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (['importSAP'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      switch (e.code) {
        case 'importSAP':
          this.handleImportSAP(selectedRecords)
          break
        case 'export':
          this.handleExport()
          break
        default:
          break
      }
    },
    // 导入SAP
    async handleImportSAP(list) {
      const ids = list.map((i) => i.id)
      const res = await this.$API.priceService.odminSyncSap({ ids })
      if (res.code === 200) {
        this.$toast({ content: res.msg, type: 'success' })
        this.$refs.templateRef.refreshCurrentGridData()
      }
    },
    // 导出
    async handleExport() {
      const params = {
        page: { current: 1, size: 10000 },
        ...this.searchFormModel
      }
      const res = await this.$API.priceService.odminExport(params)
      if (res.data) {
        this.$toast({
          type: 'success',
          content: this.$t('正在导出，请稍后！')
        })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      }
    },
    // 点击单元格
    handleClickCellTitle(row, column) {
      if (column.field === 'sourceCode') {
        this.$router.push({
          path: '/sourcing/bid-hall/hall-detail',
          query: {
            source: 'rfq',
            rfxId: row.sourceId,
            status: '',
            companyCode: row.companyCode,
            key: this.$utils.randomString(),
            calculation: 0,
            sourcingObjType: 'odmin',
            rfxGeneralType: '',
            sourceType: 'price-record'
          }
        })
      }
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.handleSearch('pageChange')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.handleSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
