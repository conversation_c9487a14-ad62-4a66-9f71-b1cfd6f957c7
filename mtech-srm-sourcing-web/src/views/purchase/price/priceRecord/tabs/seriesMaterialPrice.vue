<template>
  <div class="full-height">
    <mt-local-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="materialCodeList" :label="$t('物料编码')" label-style="top">
              <custom-select
                v-model="searchFormModel.materialCodeList"
                :label-name="$t('物料编码')"
                :placeholder="$t('请输入物料编码')"
              />
            </mt-form-item>
            <mt-form-item prop="categoryCodeList" :label="$t('品类编码')" label-style="top">
              <custom-select
                v-model="searchFormModel.categoryCodeList"
                :label-name="$t('品类编码')"
                :placeholder="$t('请输入品类编码')"
              />
            </mt-form-item>
            <mt-form-item prop="supplierCodeList" :label="$t('供应商编码')" label-style="top">
              <custom-select
                v-model="searchFormModel.supplierCodeList"
                :label-name="$t('供应商编码')"
                :placeholder="$t('请输入供应商编码')"
              />
            </mt-form-item>
            <mt-form-item prop="companyCodeList" :label="$t('公司编码')" label-style="top">
              <custom-select
                v-model="searchFormModel.companyCodeList"
                :label-name="$t('公司编码')"
                :placeholder="$t('请输入公司编码')"
              />
            </mt-form-item>
            <mt-form-item prop="factoryCodeList" :label="$t('工厂编码')" label-style="top">
              <custom-select
                v-model="searchFormModel.factoryCodeList"
                :label-name="$t('工厂编码')"
                :placeholder="$t('请输入工厂编码')"
              />
            </mt-form-item>
            <mt-form-item prop="purOrgCodeList" :label="$t('采购组织编码')" label-style="top">
              <custom-select
                v-model="searchFormModel.purOrgCodeList"
                :label-name="$t('采购组织编码')"
                :placeholder="$t('请输入采购组织编码')"
              />
            </mt-form-item>
            <mt-form-item prop="sourceCodeList" :label="$t('来源单号')" label-style="top">
              <custom-select
                v-model="searchFormModel.sourceCodeList"
                :label-name="$t('来源单号')"
                :placeholder="$t('请输入来源单号')"
              />
            </mt-form-item>
            <mt-form-item prop="priceRecordCode" :label="$t('价格编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.priceRecordCode"
                :show-clear-button="true"
                :placeholder="$t('请输入价格编码')"
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-select
                v-model="searchFormModel.status"
                :data-source="statusList"
                :show-clear-button="true"
                :placeholder="$t('请选择状态')"
              />
            </mt-form-item>
            <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.itemName"
                :show-clear-button="true"
                :placeholder="$t('请输入物料名称')"
              />
            </mt-form-item>
            <mt-form-item prop="material" :label="$t('物料材质')" label-style="top">
              <mt-input
                v-model="searchFormModel.material"
                :show-clear-button="true"
                :placeholder="$t('请输入物料材质')"
              />
            </mt-form-item>
            <mt-form-item prop="categoryName" :label="$t('品类名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.categoryName"
                :show-clear-button="true"
                :placeholder="$t('请输入品类名称')"
              />
            </mt-form-item>
            <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.supplierName"
                :show-clear-button="true"
                :placeholder="$t('请输入供应商名称')"
              />
            </mt-form-item>
            <mt-form-item prop="companyName" :label="$t('公司名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.companyName"
                :show-clear-button="true"
                :placeholder="$t('请输入公司名称')"
              />
            </mt-form-item>
            <mt-form-item prop="siteName" :label="$t('工厂名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.siteName"
                :show-clear-button="true"
                :placeholder="$t('请输入工厂名称')"
              />
            </mt-form-item>
            <mt-form-item prop="priceValueType" :label="$t('价格类别')" label-style="top">
              <mt-select
                v-model="searchFormModel.priceValueType"
                :data-source="priceCategoryList"
                :show-clear-button="true"
                :placeholder="$t('请选择价格类别')"
              />
            </mt-form-item>
            <mt-form-item prop="deliveryPlace" :label="$t('直送地')" label-style="top">
              <mt-input
                v-model="searchFormModel.deliveryPlace"
                :show-clear-button="true"
                :placeholder="$t('请输入直送地')"
              />
            </mt-form-item>
            <mt-form-item prop="validStartTime" :label="$t('生效日期')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.validStartTime"
                :open-on-focus="true"
                :allow-edit="false"
                format="yyyy-MM-dd"
                :placeholder="$t('请选择生效日期')"
                @change="(e) => handleDateRangeChange('validStartTime', e)"
              />
            </mt-form-item>
            <mt-form-item prop="validEndTime" :label="$t('失效日期')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.validEndTime"
                :open-on-focus="true"
                :allow-edit="false"
                format="yyyy-MM-dd"
                :placeholder="$t('请选择失效日期')"
                @change="(e) => handleDateRangeChange('validEndTime', e)"
              />
            </mt-form-item>
            <mt-form-item prop="purchaseAgentName" :label="$t('创建人')" label-style="top">
              <mt-input
                v-model="searchFormModel.purchaseAgentName"
                :show-clear-button="true"
                :placeholder="$t('请输入创建人')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-local-template-page>
  </div>
</template>

<script>
// 引入本地的emplate-page组件
import MtLocalTemplatePage from '@/components/template-page'
import CustomSelect from '@/components/customSelect'
import { seriesMaterialPricePageConfig } from './config/index'
import { download, getHeadersFileName } from '@/utils/utils'
import mixin from './config/mixin'

export default {
  components: {
    MtLocalTemplatePage,
    CustomSelect
  },
  mixins: [mixin],
  data() {
    return {
      pageConfig: seriesMaterialPricePageConfig,
      tempForm: []
    }
  },
  mounted() {},
  methods: {
    // 点击按钮工具栏
    handleClickToolBar(e) {
      if (e.toolbar.id === 'download') {
        this.handleExport()
      }
    },
    // 点击单元格内容
    handleClickCellTitle(e) {
      if (e.field == 'detail') {
        this.handleViewDetail(e.data)
      }
    },
    // 导出
    handleExport() {
      const visibleColumns =
        JSON.parse(sessionStorage.getItem(this.pageConfig[0]?.gridId))?.visibleCols || []
      const includeColumnFiledNames = []
      visibleColumns?.forEach((column) => {
        includeColumnFiledNames.push(column.field)
      })
      const params = {
        page: { current: 1, size: 10000 },
        includeColumnFiledNames: includeColumnFiledNames?.length
          ? includeColumnFiledNames
          : this.getColumns(),
        priceTypeList: [7],
        ...this.searchFormModel
      }
      this.$API.priceService.exportPriceRecordNew(params).then((res) => {
        this.$toast({ type: 'success', content: this.$t('正在导出，请稍后！') })
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 查看明细
    handleViewDetail(row) {
      this.$API.seriesItem
        .itemRangeQuery({ itemCode: row.itemCode, itemId: row.itemId })
        .then((res) => {
          this.$dialog({
            modal: () =>
              import('@/views/purchase/price/list/list/components/applyItemRanageDialog/index.vue'),
            data: {
              title: this.$t('适用物料范围'),
              suitItemDTOList: res.data.suitItemDTOList,
              seriesItemCode: row.itemCode,
              seriesItemId: row.itemId,
              seriesItemName: row.itemName,
              organizationId: row.siteList[0].siteId,
              siteCode: row.siteList[0].siteCode
            }
          })
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  .hidden {
    display: none;
  }
  /deep/.e-headercelldiv {
    padding: 0 0.6em 0 0.6em !important;
  }
}
</style>
