<template>
  <div class="full-height">
    <mt-tabs
      ref="mtTabsRef"
      :tab-id="$utils.randomString()"
      :e-tab="false"
      :data-source="logisticsTabConfig"
      :halt-select="false"
      @handleSelectTab="handleTabChange"
    />
    <keep-alive>
      <mt-local-template-page
        v-if="activeTabIndex < 3"
        ref="templateRef"
        :hidden-tabs="true"
        :template-config="logisticsPricePageConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleCustomReset="handleCustomReset"
      >
        <template v-slot:quick-search-form>
          <div class="custom-form-box">
            <mt-form ref="searchFormRef" :model="searchFormModel">
              <mt-form-item prop="categoryCodeList" :label="$t('品类编码')">
                <custom-select
                  v-model="searchFormModel.categoryCodeList"
                  :label-name="$t('品类编码')"
                  :placeholder="$t('请输入品类编码')"
                />
              </mt-form-item>
              <mt-form-item prop="companyCodeList" :label="$t('公司编码')">
                <custom-select
                  v-model="searchFormModel.companyCodeList"
                  :label-name="$t('公司编码')"
                  :placeholder="$t('请输入公司编码')"
                />
              </mt-form-item>
              <mt-form-item prop="department" :label="$t('事业部')">
                <mt-input
                  v-model="searchFormModel.department"
                  :label-name="$t('事业部')"
                  :placeholder="$t('请输入事业部')"
                />
              </mt-form-item>
              <mt-form-item prop="factoryCodeList" :label="$t('工厂编码')">
                <custom-select
                  v-model="searchFormModel.factoryCodeList"
                  :label-name="$t('工厂编码')"
                  :placeholder="$t('请输入工厂编码')"
                />
              </mt-form-item>
              <mt-form-item prop="logisticsCode" :label="$t('物流编码')">
                <mt-input
                  v-model="searchFormModel.logisticsCode"
                  :show-clear-button="true"
                  :placeholder="$t('请输入物流编码')"
                />
              </mt-form-item>
              <mt-form-item prop="materialCodeList" :label="$t('物料编码')">
                <custom-select
                  v-model="searchFormModel.materialCodeList"
                  :label-name="$t('物料编码')"
                  :placeholder="$t('请输入物料编码')"
                />
              </mt-form-item>
              <mt-form-item prop="priceRecordCode" :label="$t('价格记录编码')">
                <mt-input
                  v-model="searchFormModel.priceRecordCode"
                  :show-clear-button="true"
                  :placeholder="$t('请输入价格记录编码')"
                />
              </mt-form-item>
              <mt-form-item prop="priceValidStartTime" :label="$t('价格生效日期')">
                <mt-date-picker
                  v-model="tempForm.priceValidStartTime"
                  :open-on-focus="true"
                  :allow-edit="false"
                  time-stamp
                  format="yyyy-MM-dd"
                  :placeholder="$t('请选择生效日期')"
                  @change="(e) => handleDateChange('priceValidStartTime', e)"
                />
              </mt-form-item>
              <mt-form-item prop="priceValidEndTime" :label="$t('价格失效日期')">
                <mt-date-picker
                  v-model="tempForm.priceValidEndTime"
                  :open-on-focus="true"
                  :allow-edit="false"
                  time-stamp
                  format="yyyy-MM-dd"
                  :placeholder="$t('请选择价格失效日期')"
                  @change="(e) => handleDateChange('priceValidEndTime', e)"
                />
              </mt-form-item>

              <mt-form-item prop="productName" :label="$t('品名')">
                <mt-input
                  v-model="searchFormModel.productName"
                  :label-name="$t('品名')"
                  :placeholder="$t('请输入品名')"
                />
              </mt-form-item>
              <mt-form-item prop="purOrgCodeList" :label="$t('采购组织编码')">
                <custom-select
                  v-model="searchFormModel.purOrgCodeList"
                  :label-name="$t('采购组织编码')"
                  :placeholder="$t('请输入采购组织编码')"
                />
              </mt-form-item>
              <mt-form-item prop="requirementName" :label="$t('需求名称')">
                <mt-input
                  v-model="searchFormModel.requirementName"
                  :label-name="$t('需求名称')"
                  :placeholder="$t('请输入需求名称')"
                />
              </mt-form-item>
              <mt-form-item prop="requirementNumber" :label="$t('需求编号')">
                <mt-input
                  v-model="searchFormModel.requirementNumber"
                  :label-name="$t('需求编号')"
                  :placeholder="$t('请输入需求编号')"
                />
              </mt-form-item>
              <mt-form-item prop="requirementType" :label="$t('需求类型')">
                <mt-input
                  v-model="searchFormModel.requirementType"
                  :label-name="$t('需求类型')"
                  :placeholder="$t('请输入需求类型')"
                />
              </mt-form-item>
              <mt-form-item prop="sourceCode" :label="$t('来源编码')">
                <mt-input
                  v-model="searchFormModel.sourceCode"
                  :label-name="$t('来源编码')"
                  :placeholder="$t('请输入来源编码')"
                />
              </mt-form-item>
              <mt-form-item prop="supplierCodeList" :label="$t('供应商编码')">
                <custom-select
                  v-model="searchFormModel.supplierCodeList"
                  :label-name="$t('供应商编码')"
                  :placeholder="$t('请输入供应商编码')"
                />
              </mt-form-item>
              <mt-form-item prop="status" :label="$t('状态')">
                <mt-select
                  v-model="searchFormModel.status"
                  :data-source="statusList"
                  :show-clear-button="true"
                  :placeholder="$t('请选择状态')"
                />
              </mt-form-item>
              <mt-form-item prop="itemName" :label="$t('物料名称')">
                <mt-input
                  v-model="searchFormModel.itemName"
                  :show-clear-button="true"
                  :placeholder="$t('请输入物料名称')"
                />
              </mt-form-item>
              <mt-form-item prop="categoryName" :label="$t('品类名称')">
                <mt-input
                  v-model="searchFormModel.categoryName"
                  :show-clear-button="true"
                  :placeholder="$t('请输入品类名称')"
                />
              </mt-form-item>
              <mt-form-item prop="supplierName" :label="$t('供应商名称')">
                <mt-input
                  v-model="searchFormModel.supplierName"
                  :show-clear-button="true"
                  :placeholder="$t('请输入供应商名称')"
                />
              </mt-form-item>
              <mt-form-item prop="syncStatus" :label="$t('同步状态')">
                <mt-select
                  v-model="searchFormModel.syncStatus"
                  :data-source="syncStatusList"
                  :show-clear-button="true"
                  :placeholder="$t('请选择同步状态')"
                />
              </mt-form-item>
              <mt-form-item prop="validStartTime" :label="$t('生效日期')">
                <mt-date-picker
                  v-model="tempForm.validStartTime"
                  :open-on-focus="true"
                  :allow-edit="false"
                  time-stamp
                  format="yyyy-MM-dd"
                  :placeholder="$t('请选择生效日期')"
                  @change="(e) => handleDateChange('validStartTime', e)"
                />
              </mt-form-item>
              <mt-form-item prop="validEndTime" :label="$t('失效日期')">
                <mt-date-picker
                  v-model="tempForm.validEndTime"
                  :open-on-focus="true"
                  :allow-edit="false"
                  time-stamp
                  format="yyyy-MM-dd"
                  :placeholder="$t('请选择失效日期')"
                  @change="(e) => handleDateChange('validEndTime', e)"
                />
              </mt-form-item>
              <mt-form-item prop="purchaseAgentName" :label="$t('创建人')">
                <mt-input
                  v-model="searchFormModel.purchaseAgentName"
                  :show-clear-button="true"
                  :placeholder="$t('请输入创建人')"
                />
              </mt-form-item>
            </mt-form>
          </div>
        </template>
      </mt-local-template-page>
      <LogisticsSeaPriceAnnual v-else-if="activeTabIndex === 3" />
      <LogisticsTrunkPriceAnnual v-else-if="activeTabIndex === 4" />
      <LogisticsRailwayPriceAnnual v-else-if="activeTabIndex === 5" />
      <LogisticsRailwayBPriceAnnual v-else-if="activeTabIndex === 6" />
    </keep-alive>
  </div>
</template>

<script>
// 引入本地的emplate-page组件
import MtLocalTemplatePage from '@/components/template-page'
// 物流价格记录年约需求
import LogisticsSeaPriceAnnual from './logisticsSeaPriceAnnual'
import LogisticsTrunkPriceAnnual from './logisticsTrunkPriceAnnual' //干线
import LogisticsRailwayPriceAnnual from './logisticsRailwayPriceAnnual' //铁运
import LogisticsRailwayBPriceAnnual from './logisticsRailwayBPriceAnnual' //铁运班列
import CustomSelect from '@/components/customSelect'
import { logisticsTabConfig, logisticsPageConfigFn, logisticsTypeArr } from './config/index'
import { download, getHeadersFileName } from '@/utils/utils'
import mixin from './config/mixin'

export default {
  components: {
    MtLocalTemplatePage,
    CustomSelect,
    LogisticsSeaPriceAnnual,
    LogisticsTrunkPriceAnnual,
    LogisticsRailwayPriceAnnual,
    LogisticsRailwayBPriceAnnual
  },
  mixins: [mixin],
  data() {
    return {
      activeTabIndex: 0,
      logisticsTabConfig
    }
  },
  computed: {
    logisticsPricePageConfig() {
      return logisticsPageConfigFn(this.activeTabIndex)
    },
    logisticsType() {
      return logisticsTypeArr[this.activeTabIndex]
    }
  },
  mounted() {},
  methods: {
    handleTabChange(index) {
      this.$refs.mtTabsRef.activeTab = index
      this.activeTabIndex = index
    },
    // 点击按钮工具栏
    handleClickToolBar(e) {
      const { toolbar } = e
      if (toolbar.id === 'export') {
        this.handleExport()
      }
    },
    // 导出
    handleExport() {
      const visibleColumns =
        JSON.parse(sessionStorage.getItem(this.logisticsPricePageConfig[0]?.gridId))?.visibleCols ||
        []
      const includeColumnFiledNames = []
      visibleColumns?.forEach((column) => {
        includeColumnFiledNames.push(column.field)
      })
      const params = {
        page: { current: 1, size: 10000 },
        includeColumnFiledNames,
        ...this.searchFormModel,
        logisticsType: this.logisticsType
      }
      this.$API.priceService.exportLogistics(params).then((res) => {
        this.$toast({ type: 'success', content: this.$t('正在导出，请稍后！') })
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  .hidden {
    display: none;
  }
  /deep/.e-headercelldiv {
    padding: 0 0.6em 0 0.6em !important;
  }
}
</style>
