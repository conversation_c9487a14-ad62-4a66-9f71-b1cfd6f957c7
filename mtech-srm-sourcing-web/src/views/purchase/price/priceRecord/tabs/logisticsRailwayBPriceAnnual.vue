<template>
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.status"
            :data-source="logisticsSeaStatusList"
            :show-clear-button="true"
            :placeholder="$t('请选择状态')"
          />
        </mt-form-item>
        <mt-form-item prop="priceRecordCode" :label="$t('价格记录编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.priceRecordCode"
            :show-clear-button="true"
            :placeholder="$t('请输入价格记录编码')"
          />
        </mt-form-item>
        <mt-form-item prop="sourceCode" :label="$t('询价单号')" label-style="top">
          <mt-input
            v-model="searchFormModel.sourceCode"
            :show-clear-button="true"
            :placeholder="$t('请输入询价单号')"
          />
        </mt-form-item>
        <mt-form-item prop="sourceName" :label="$t('单据名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.sourceName"
            :show-clear-button="true"
            :placeholder="$t('单据名称')"
          />
        </mt-form-item>
        <mt-form-item prop="syncStatus" :label="$t('同步状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.syncStatus"
            :data-source="syncStatusList"
            :show-clear-button="true"
            :placeholder="$t('请选择同步状态')"
          />
        </mt-form-item>

        <mt-form-item prop="companyCodeList" :label="$t('公司编码')" label-style="top">
          <custom-select
            v-model="searchFormModel.companyCodeList"
            :label-name="$t('公司编码')"
            :placeholder="$t('请输入公司编码')"
          />
        </mt-form-item>
        <!-- <mt-form-item prop="companyName" :label="$t('公司名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.companyName"
            :show-clear-button="true"
            :placeholder="$t('请输入公司名称')"
          />
        </mt-form-item> -->
        <mt-form-item prop="categoryCodeList" :label="$t('品类编码')" label-style="top">
          <custom-select
            v-model="searchFormModel.categoryCodeList"
            :label-name="$t('品类编码')"
            :placeholder="$t('请输入品类编码')"
          />
        </mt-form-item>
        <mt-form-item
          prop="internationalSupplierCode"
          :label="$t('国际段-供应商编码')"
          label-style="top"
        >
          <mt-input
            v-model="searchFormModel.internationalSupplierCode"
            :show-clear-button="true"
            :placeholder="$t('请输入国际段供应商编码')"
          />
        </mt-form-item>
        <mt-form-item
          prop="internationalSupplierName"
          :label="$t('国际段-供应商名称')"
          label-style="top"
        >
          <mt-input
            v-model="searchFormModel.internationalSupplierName"
            :show-clear-button="true"
            :placeholder="$t('请输入供应商名称')"
          />
        </mt-form-item>
        <mt-form-item
          prop="domesticSupplierCode"
          :label="$t('国内段-供应商编码')"
          label-style="top"
        >
          <mt-input
            v-model="searchFormModel.domesticSupplierCode"
            :show-clear-button="true"
            :placeholder="$t('请输入国内段供应商编码')"
          />
        </mt-form-item>
        <mt-form-item
          prop="domesticSupplierName"
          :label="$t('国内段-供应商名称')"
          label-style="top"
        >
          <mt-input
            v-model="searchFormModel.domesticSupplierName"
            :show-clear-button="true"
            :placeholder="$t('请输入国内段供应商名称')"
          />
        </mt-form-item>

        <mt-form-item prop="railwayModeName" :label="$t('铁运模式')" label-style="top">
          <mt-input
            v-model="searchFormModel.railwayModeName"
            :show-clear-button="true"
            :placeholder="$t('请输入铁运模式')"
          />
        </mt-form-item>
        <mt-form-item prop="tradeTermsName" :label="$t('贸易条款')" label-style="top">
          <mt-input
            v-model="searchFormModel.tradeTermsName"
            :show-clear-button="true"
            :placeholder="$t('请输入贸易条款')"
          />
        </mt-form-item>
        <mt-form-item prop="transportTermsName" :label="$t('运输条款')" label-style="top">
          <mt-input
            v-model="searchFormModel.transportTermsName"
            :show-clear-button="true"
            :placeholder="$t('请输入运输条款')"
          />
        </mt-form-item>
        <mt-form-item prop="startAddress" :label="$t('起始地')" label-style="top">
          <mt-input
            v-model="searchFormModel.startAddress"
            :show-clear-button="true"
            :placeholder="$t('请输入起始地')"
          />
        </mt-form-item>
        <mt-form-item prop="endAddress" :label="$t('目的地')" label-style="top">
          <mt-input
            v-model="searchFormModel.endAddress"
            :show-clear-button="true"
            :placeholder="$t('请输入目的地')"
          />
        </mt-form-item>
        <mt-form-item prop="validStartTime" :label="$t('价格生效日期')">
          <mt-date-picker
            v-model="searchFormModel.validStartTime"
            :open-on-focus="true"
            :allow-edit="false"
            time-stamp
            format="yyyy-MM-dd"
            :placeholder="$t('请选择生效日期')"
            @change="(e) => handleDateChange('validStartTime', e)"
          />
        </mt-form-item>
        <mt-form-item prop="validEndTime" :label="$t('价格失效日期')">
          <mt-date-picker
            v-model="searchFormModel.validEndTime"
            :open-on-focus="true"
            :allow-edit="false"
            time-stamp
            format="yyyy-MM-dd"
            :placeholder="$t('请选择价格失效日期')"
            @change="(e) => handleDateChange('validEndTime', e)"
          />
        </mt-form-item>
        <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
          <mt-input
            v-model="searchFormModel.createUserName"
            :show-clear-button="true"
            :placeholder="$t('请输入创建人')"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :allow-edit="false"
            :placeholder="$t('请选择创建时间')"
            :open-on-focus="true"
            @change="handleDateChange"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      grid-id="5de4fe6a-d863-4449-a221-007d3495dd75"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="listColumns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
// import { getTimeList } from '@/utils/obj'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import { logisticsToolbar, syncStatusList, logisticsSeaStatusList } from './config/index'
import XEUtils from 'xe-utils'
import CustomSelect from '@/components/customSelect'
export default {
  components: {
    ScTable,
    CollapseSearch,
    CustomSelect
  },
  mixins: [mixin],
  data() {
    return {
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 20,
        pageSizes: [20, 50, 200, 500, 1000, 2000]
      },
      pageInfo: {
        size: 20,
        current: 1
      },

      syncStatusList,
      logisticsSeaStatusList,
      toolbar: logisticsToolbar,
      searchFormModel: {
        createUserName: JSON.parse(sessionStorage.getItem('userInfo'))?.username || null
        // createTime: getTimeList(3)
      },
      tableData: [],
      loading: false,
      type: 'list',
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.cancel,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 使用组件中的toolbar配置
          toolbar: [],
          grid: {
            columnData: [
              {
                width: '150',
                field: 'fieldCode',
                headerText: this.$t('月份')
              },
              {
                width: '150',
                field: 'fieldData',
                headerText: this.$t('柜量')
              }
            ],
            dataSource: [],
            allowPaging: false,
            asyncConfig: {}
          }
        }
      ]
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    // 列配置
    listColumns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          width: 80,
          field: 'index',
          title: this.$t('序号')
        },
        {
          field: 'status',
          title: this.$t('状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = this.logisticsSeaStatusList.find(
                (item) => item.value === row.status
              )
              const status = selectItem?.text
              return [<div>{status}</div>]
            }
          }
        },
        {
          field: 'priceRecordCode',
          title: this.$t('价格记录编码'),
          minWidth: 120
        },
        {
          field: 'syncStatus',
          title: this.$t('同步状态'),
          minWidth: 100,
          slots: {
            default: ({ row }) => {
              const selectItem = this.syncStatusList.find((item) => item.value === row.syncStatus)
              const syncStatus = selectItem?.text
              return [<div>{syncStatus}</div>]
            }
          }
        },
        {
          field: 'syncInfo',
          title: this.$t('同步信息'),
          minWidth: 100
        },
        {
          field: 'sourceCode',
          title: this.$t('询价单号'),
          minWidth: 180,
          slots: {
            default: ({ row, column }) => {
              return [
                <a on-click={() => this.handleClickCellTitle(row, column)}>{row.sourceCode}</a>
              ]
            }
          }
        },
        {
          field: 'sourceCode',
          title: this.$t('定价单号'),
          minWidth: 120
        },
        {
          field: 'sourceName',
          title: this.$t('单据名称'),
          minWidth: 140
        },
        {
          field: 'companyCode',
          title: this.$t('公司'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [<span>{row.companyCode + '-' + row.companyName}</span>]
            }
          }
        },
        {
          field: 'categoryCode',
          title: this.$t('品类编码'),
          minWidth: 120
        },
        {
          field: 'categoryName',
          title: this.$t('品类名称'),
          minWidth: 120
        },
        {
          field: 'sourceCode',
          title: this.$t('定价单号'),
          minWidth: 120
        },
        {
          field: 'railwayModeName',
          title: this.$t('铁运模式'),
          minWidth: 120
        },
        {
          field: 'tradeTermsName',
          title: this.$t('贸易条款'),
          minWidth: 120
        },
        {
          field: 'transportTermsName',
          title: this.$t('运输条款'),
          minWidth: 120
        },
        {
          field: 'loadingAddr',
          title: this.$t('装货地'),
          minWidth: 120
        },
        {
          field: 'startAddress',
          title: this.$t('起始地'),
          minWidth: 120
        },
        {
          field: 'endAddress',
          title: this.$t('目的地'),
          minWidth: 120
        },
        {
          field: 'requireQuantity',
          title: this.$t('需求量（FEU）'),
          minWidth: 140
        },
        {
          field: 'goodsFinishTime',
          title: this.$t('货好时间'),
          minWidth: 120
        },
        {
          field: 'demandStartTime',
          title: this.$t('需求起运时间'),
          minWidth: 140
        },
        {
          field: 'demandDeliveryTime',
          title: this.$t('送达时间'),
          minWidth: 140
        },
        {
          field: 'cabinetQuantity',
          title: this.$t('可提供柜量'),
          minWidth: 140
        },
        {
          field: 'internationalSupplierCode',
          title: this.$t('国际段-供应商编码'),
          minWidth: 160
        },
        {
          field: 'internationalSupplierName',
          title: this.$t('国际段-供应商名称'),
          minWidth: 160
        },
        {
          field: 'internationalPrice',
          title: this.$t('国际段-单价'),
          minWidth: 140
        },
        {
          field: 'domesticSupplierCode',
          title: this.$t('国内段-供应商编码'),
          minWidth: 160
        },
        {
          field: 'domesticSupplierName',
          title: this.$t('国内段-供应商名称'),
          minWidth: 160
        },
        {
          field: 'domesticPrice',
          title: this.$t('国内段-单价'),
          minWidth: 140
        },
        {
          field: 'totalPrice',
          title: this.$t('合计单价'),
          minWidth: 140
        },
        {
          field: 'currencyCode',
          title: this.$t('币种'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [<span>{row.currencyCode + '-' + row.currencyName}</span>]
            }
          }
        },
        {
          field: 'exchangeRateValue',
          title: this.$t('汇率(USD兑CNY)'),
          minWidth: 160
        },
        {
          field: 'validStartTime',
          title: this.$t('价格开始日期'),
          minWidth: 140
        },
        {
          field: 'validEndTime',
          title: this.$t('价格结束日期'),
          minWidth: 140
        },
        {
          field: 'remark',
          title: this.$t('备注'),
          minWidth: 140
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          minWidth: 140
        }
      ]
    }
  },
  mounted() {
    this.handleSearch()
  },
  activated() {
    this.handleSearch()
  },
  methods: {
    // 日期格式化
    handleDateChange(e) {
      if (e.startDate) {
        const startDate = XEUtils.toDateString(e.startDate, 'yyyy-MM-dd') + ' 00:00:00'
        const endDate = XEUtils.toDateString(e.endDate, 'yyyy-MM-dd') + ' 23:59:59'
        this.searchFormModel['createStartTime'] = XEUtils.timestamp(startDate)
        this.searchFormModel['createEndTime'] = XEUtils.timestamp(endDate)
      } else {
        this.searchFormModel['createStartTime'] = null
        this.searchFormModel['createEndTime'] = null
      }
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    // 查询
    async handleSearch(type) {
      // 解决修改查询条件后，查询结果有数据但未正常显示的问题（type: pageChange/refresh）
      if (!type) {
        this.pageInfo.current = 1
        this.$refs.pageRef.jumpNum = 1
        this.$refs.pageRef.currentPage = 1
      }
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      delete params.submitTime
      this.loading = true
      const res = await this.$API.priceService
        .getRailwayBAnnualPricingRecords(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.tableData?.forEach((item, index) => {
          item.index = index + 1
        }) || []
        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (['syncTms'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      switch (e.code) {
        case 'export':
          this.handleExport()
          break
        case 'syncTms':
          this.syncTms(selectedRecords)
          break
        default:
          break
      }
    },

    // 导出
    async handleExport() {
      const params = {
        page: { current: 1, size: 10000 },
        ...this.searchFormModel
      }
      const res = await this.$API.priceService.exportRailwayBAnnual(params)
      if (res.data) {
        this.$toast({
          type: 'success',
          content: this.$t('正在导出，请稍后！')
        })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      }
    },
    // 同步TMS
    async syncTms(list) {
      const ids = list.map((i) => i.id)
      const res = await this.$API.priceService.railwayBAnnualSyncTMS({ ids })
      if (res.code === 200) {
        this.$toast({ content: res.msg, type: 'success' })
        this.handleSearch()
      }
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.handleSearch('pageChange')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.handleSearch()
    },
    // 显示柜量明细
    showFiledVolist(data) {
      this.$refs.filedVolistRef.ejsRef.show()
      this.$set(this.pageConfig[0].grid, 'dataSource', data?.cabinetQuantity || [])
    },
    cancel() {
      this.$refs.filedVolistRef.ejsRef.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
