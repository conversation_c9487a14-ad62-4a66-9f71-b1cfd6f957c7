<template>
  <div class="full-height">
    <mt-local-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="dieCode" :label="$t('模具编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.dieCode"
                :show-clear-button="true"
                :placeholder="$t('请输入模具编码')"
              />
            </mt-form-item>
            <mt-form-item prop="supplierCodeList" :label="$t('供应商编码')" label-style="top">
              <custom-select
                v-model="searchFormModel.supplierCodeList"
                :label-name="$t('供应商编码')"
                :placeholder="$t('请输入供应商编码')"
              />
            </mt-form-item>
            <mt-form-item prop="companyCodeList" :label="$t('公司编码')" label-style="top">
              <custom-select
                v-model="searchFormModel.companyCodeList"
                :label-name="$t('公司编码')"
                :placeholder="$t('请输入公司编码')"
              />
            </mt-form-item>
            <mt-form-item prop="factoryCodeList" :label="$t('工厂编码')" label-style="top">
              <custom-select
                v-model="searchFormModel.factoryCodeList"
                :label-name="$t('工厂编码')"
                :placeholder="$t('请输入工厂编码')"
              />
            </mt-form-item>
            <mt-form-item prop="sourceCodeList" :label="$t('来源单号')" label-style="top">
              <custom-select
                v-model="searchFormModel.sourceCodeList"
                :label-name="$t('来源单号')"
                :placeholder="$t('请输入来源单号')"
              />
            </mt-form-item>
            <mt-form-item prop="dieName" :label="$t('模具名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.dieName"
                :show-clear-button="true"
                :placeholder="$t('请输入模具名称')"
              />
            </mt-form-item>
            <mt-form-item prop="dieType" :label="$t('模具类型')" label-style="top">
              <mt-select
                v-model="searchFormModel.dieType"
                :data-source="mouldTypeList"
                :show-clear-button="true"
                :placeholder="$t('请选择模具类型')"
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-select
                v-model="searchFormModel.status"
                :data-source="statusList"
                :show-clear-button="true"
                :placeholder="$t('请选择状态')"
              />
            </mt-form-item>
            <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.supplierName"
                :show-clear-button="true"
                :placeholder="$t('请输入供应商名称')"
              />
            </mt-form-item>
            <mt-form-item prop="companyName" :label="$t('公司名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.companyName"
                :show-clear-button="true"
                :placeholder="$t('请输入公司名称')"
              />
            </mt-form-item>
            <mt-form-item prop="siteName" :label="$t('工厂名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.siteName"
                :show-clear-button="true"
                :placeholder="$t('请输入工厂名称')"
              />
            </mt-form-item>
            <mt-form-item prop="deliveryPlace" :label="$t('直送地')" label-style="top">
              <mt-input
                v-model="searchFormModel.deliveryPlace"
                :show-clear-button="true"
                :placeholder="$t('请输入直送地')"
              />
            </mt-form-item>
            <mt-form-item prop="priceRecordCode" :label="$t('价格编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.priceRecordCode"
                :show-clear-button="true"
                :placeholder="$t('请输入价格编码')"
              />
            </mt-form-item>
            <mt-form-item prop="validStartTime" :label="$t('价格生效日期')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.validStartTime"
                :open-on-focus="true"
                :allow-edit="false"
                format="yyyy-MM-dd"
                :placeholder="$t('请选择价格生效日期')"
                @change="(e) => handleDateRangeChange('validStartTime', e)"
              />
            </mt-form-item>
            <mt-form-item prop="validEndTime" :label="$t('价格失效日期')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.validEndTime"
                :open-on-focus="true"
                :allow-edit="false"
                format="yyyy-MM-dd"
                :placeholder="$t('请选择价格失效日期')"
                @change="(e) => handleDateRangeChange('validEndTime', e)"
              />
            </mt-form-item>
            <mt-form-item prop="purchaseAgentName" :label="$t('创建人')" label-style="top">
              <mt-input
                v-model="searchFormModel.purchaseAgentName"
                :show-clear-button="true"
                :placeholder="$t('请输入创建人')"
              />
            </mt-form-item>
            <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.createTime"
                :open-on-focus="true"
                :allow-edit="false"
                format="yyyy-MM-dd"
                :placeholder="$t('请选择创建时间')"
                @change="(e) => handleDateRangeChange('createTime', e)"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-local-template-page>
  </div>
</template>

<script>
// 引入本地的emplate-page组件
import MtLocalTemplatePage from '@/components/template-page'
import CustomSelect from '@/components/customSelect'
import { mouldPageConfig } from './config/index'
import { download, getHeadersFileName } from '@/utils/utils'
import mixin from './config/mixin'

export default {
  components: {
    MtLocalTemplatePage,
    CustomSelect
  },
  mixins: [mixin],
  data() {
    return {
      pageConfig: mouldPageConfig
    }
  },
  mounted() {},
  methods: {
    // 点击按钮工具栏
    handleClickToolBar(e) {
      if (e.toolbar.id === 'download') {
        this.handleExport()
      }
    },
    // 导出
    handleExport() {
      const visibleColumns =
        JSON.parse(sessionStorage.getItem(this.pageConfig[0]?.gridId))?.visibleCols || []
      const includeColumnFiledNames = []
      visibleColumns?.forEach((column) => {
        includeColumnFiledNames.push(column.field)
      })
      const params = {
        page: { current: 1, size: 10000 },
        includeColumnFiledNames: includeColumnFiledNames?.length
          ? includeColumnFiledNames
          : this.getColumns(),
        priceTypeList: [8],
        ...this.searchFormModel
      }
      this.$API.priceService.exportPriceRecordNew(params).then((res) => {
        this.$toast({ type: 'success', content: this.$t('正在导出，请稍后！') })
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  .hidden {
    display: none;
  }
  /deep/.e-headercelldiv {
    padding: 0 0.6em 0 0.6em !important;
  }
}
</style>
