import {
  syncStatusList,
  priceCategoryList,
  priceTypeList,
  statusList,
  mouldTypeList
} from './index'

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {
        status: 1,
        purchaseAgentName: JSON.parse(sessionStorage.getItem('userInfo'))?.username || null
      },
      tempForm: {},
      syncStatusList,
      priceCategoryList,
      priceTypeList,
      statusList,
      mouldTypeList
    }
  },
  mounted() {},
  methods: {
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      for (const key in this.tempForm) {
        if (Object.hasOwnProperty.call(this.tempForm, key)) {
          this.tempForm[key] = null
        }
      }
    },
    // 日期格式化
    handleDateChange(key, val) {
      this.searchFormModel[key] = val?.valueOf()
    },
    // 日期范围格式化
    handleDateRangeChange(key, e) {
      const { startDate, endDate } = e
      this.searchFormModel[key + 'Start'] = startDate ? startDate.valueOf() : null
      this.searchFormModel[key + 'End'] = endDate ? endDate.valueOf() + 86400000 : null
      this.searchFormModel[key] = null
    },
    // 获取所有列数据
    getColumns() {
      let list = []
      const columns = this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.getColumns()
      columns.forEach((item) => {
        list.push(item.field)
      })
      return list
    }
  }
}
