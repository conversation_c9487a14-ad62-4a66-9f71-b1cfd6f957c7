<template>
  <mt-dialog
    ref="dialog"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    height="75%"
    width="85%"
    @beforeOpen="beforeOpen"
    @close="handleClose"
  >
    <div class="dialog-content" style="margin-top: 10px">
      <sc-table
        ref="sctableRef"
        grid-id="2321bbb3-2270-4258-997b-19b58cf9b7dd"
        :loading="loading"
        :is-show-refresh-bth="false"
        :columns="columns"
        :table-data="tableData"
        :height="560"
      />
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import XEUtils from 'xe-utils'
export default {
  components: { ScTable },
  data() {
    const quoteAttrOptions = [
      { value: 'mailing_price', text: this.$t('寄售价') },
      { value: 'standard_price', text: this.$t('标准价') },
      { value: 'outsource', text: this.$t('委外价') }
    ]
    const priceEffectiveMethodOptions = [
      { value: 'in_warehouse', text: this.$t('按照入库') },
      { value: 'out_warehouse', text: this.$t('按出库') },
      { value: 'order_date', text: this.$t('按订单日期') }
    ]
    return {
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('关闭') }
        }
      ],
      params: null,
      columns: [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'companyCode',
          title: this.$t('公司'),
          minWidth: 160,
          slots: {
            default: ({ row }) => {
              return [<span>{(row.companyCode || '') + '-' + (row.companyName || '')}</span>]
            }
          }
        },
        {
          field: 'factoryCode',
          title: this.$t('工厂'),
          minWidth: 160,
          slots: {
            default: ({ row }) => {
              return [<span>{(row.factoryCode || '') + '-' + (row.factoryName || '')}</span>]
            }
          }
        },
        {
          field: 'purchaseOrgCode',
          title: this.$t('采购组织'),
          minWidth: 160,
          slots: {
            default: ({ row }) => {
              return [
                <span>{(row.purchaseOrgCode || '') + '-' + (row.purchaseOrgName || '')}</span>
              ]
            }
          }
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商编码'),
          minWidth: 120
        },
        {
          field: 'supplierName',
          title: this.$t('供应商名称'),
          minWidth: 160
        },
        {
          field: 'materialCode',
          title: this.$t('物料编码'),
          minWidth: 120
        },
        {
          field: 'materialName',
          title: this.$t('物料名称'),
          minWidth: 160
        },
        {
          field: 'currencyCode',
          title: this.$t('币种'),
          minWidth: 160,
          slots: {
            default: ({ row }) => {
              return [<span>{(row.currencyCode || '') + '-' + (row.currencyName || '')}</span>]
            }
          }
        },
        {
          field: 'quoteAttr',
          title: this.$t('报价属性'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const quoteAttrLabel = quoteAttrOptions.find(
                (item) => item.value === row.quoteAttr
              )?.text
              return [<span>{quoteAttrLabel}</span>]
            }
          }
        },
        {
          field: 'priceEffectiveMethod',
          title: this.$t('价格生效方式'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const priceEffectiveMethodLabel = priceEffectiveMethodOptions.find(
                (item) => item.value === row.priceEffectiveMethod
              )?.text
              return [<span>{priceEffectiveMethodLabel}</span>]
            }
          }
        },
        {
          field: 'stepValue',
          title: this.$t('阶梯数量'),
          minWidth: 120
        },
        {
          field: 'validStartTime',
          title: this.$t('生效日期'),
          slots: {
            default: ({ row }) => {
              const validStartTime = XEUtils.toDateString(row.validStartTime, 'yyyy-MM-dd HH:mm:ss')
              return [<span>{validStartTime}</span>]
            }
          },
          minWidth: 160
        },
        {
          field: 'validEndTime',
          title: this.$t('截止日期'),
          slots: {
            default: ({ row }) => {
              const validEndTime = XEUtils.toDateString(row.validEndTime, 'yyyy-MM-dd HH:mm:ss')
              return [<span>{validEndTime}</span>]
            }
          },
          minWidth: 160
        },
        {
          field: 'srmUntaxedUnitPrice',
          title: this.$t('SRM未税单价'),
          minWidth: 120
        },
        {
          field: 'sapUntaxedUnitPrice',
          title: this.$t('SAP未税单价'),
          minWidth: 120
        },
        {
          field: 'priceUntaxedDiff',
          title: this.$t('未税单价差异'),
          minWidth: 120
        }
      ],
      loading: false,
      tableData: []
    }
  },
  mounted() {},
  methods: {
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { title, params } = args
      this.dialogTitle = title
      this.params = params
      this.getTableData()
    },
    async getTableData() {
      const params = this.params
      this.loading = true
      const res = await this.$API.priceService
        .codesDiffApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const records = res?.data || []
        this.tableData = records
      }
    },
    beforeOpen() {
      this.tableData = []
    },
    onOpen(args) {
      args.preventFocus = true
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
