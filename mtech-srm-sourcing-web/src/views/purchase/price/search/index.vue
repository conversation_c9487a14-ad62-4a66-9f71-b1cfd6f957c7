<template>
  <div class="full-height">
    <mt-template-page ref="templateRef" :template-config="pageConfig"></mt-template-page>
  </div>
</template>

<script>
import { columnData } from './config/index'
export default {
  data() {
    return {
      pageConfig: [
        {
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [[], ['Filter', 'Refresh', 'Setting']]
          },
          useToolTemplate: false,
          grid: {
            allowFiltering: true,
            columnData: columnData,
            dataSource: [],
            asyncConfig: {
              url: '/price/tenant/pricerecord/query/valid/item'
            }
          }
        }
      ]
    }
  },
  methods: {}
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
</style>
