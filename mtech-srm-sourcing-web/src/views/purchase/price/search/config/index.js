import { i18n } from '@/main.js'
export const columnData = [
  {
    field: 'supplierName',
    headerText: i18n.t('供应商')
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    valueAccessor: function (field, data) {
      if (data.siteList.length > 0) {
        return data.siteList.map((e) => e.siteName).toString()
      }
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('价格')
  },
  {
    field: 'priceUnit',
    headerText: i18n.t('价格单位')
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织'),
    valueAccessor: function (field, data) {
      if (data.siteList.length > 0) {
        return data.siteList.map((e) => e.purOrgName).toString()
      }
    }
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种')
  }
]
