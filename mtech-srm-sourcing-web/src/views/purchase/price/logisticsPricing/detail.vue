<template>
  <div class="logistics-full-height">
    <div class="top-container toggle-container" :class="[!isExpand && 'top-hidden']">
      <div class="top-content">
        <div class="top-info">
          <div class="title">
            <div v-show="!isExpand">
              <span>{{ dataForm.pointNo }}</span>
              <span class="sub-title">{{ dataForm.title }}</span>
            </div>
          </div>
          <div class="top-btns">
            <vxe-button
              v-for="(item, index) in detailToolbar"
              v-show="!item.isHidden"
              :key="index"
              :icon="item.icon"
              :status="item.status"
              :disabled="item.disabled"
              size="small"
              @click="handleClickToolBar(item)"
              >{{ item.name }}</vxe-button
            >
          </div>
        </div>
        <div class="top-form">
          <mt-form ref="dataFormRef" :model="dataForm" :rules="formRules">
            <mt-form-item prop="pointNo" :label="$t('定价单编码')" label-style="top">
              <vxe-input v-model="dataForm.pointNo" disabled />
            </mt-form-item>
            <mt-form-item prop="title" :label="$t('定价单标题')" label-style="top">
              <vxe-input
                v-model="dataForm.title"
                clearable
                :disabled="!editable"
                :placeholder="$t('请输入定价单标题')"
              />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
              <vxe-select
                v-model="dataForm.companyCode"
                :options="companyList"
                :option-props="{ label: 'text', value: 'orgCode' }"
                clearable
                filterable
                :disabled="!editable || itemLength !== 0"
                :placeholder="$t('请选择公司')"
                @change="(e) => handleValueChange('company', e)"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </div>
      <div class="top-toggle-btn">
        <div @click="handleToggle"><i class="vxe-icon-arrow-up" /></div>
      </div>
    </div>
    <div class="body-container" v-show="$route.query.type === 'edit'">
      <mt-tabs
        ref="mtTabsRef"
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabList"
        :halt-select="false"
        @handleSelectTab="(index, item) => handleTabChange(index)"
        style="background-color: #fff"
      />
      <keep-alive>
        <component
          ref="mainContent"
          style="height: calc(100% - 50px)"
          :is="activeComponent"
          :data-info="dataForm"
          :detail-info="dataForm"
          @updateDetail="init"
          @itemDataChange="itemDataChange"
        />
      </keep-alive>
    </div>
  </div>
</template>

<script>
import {
  Input as VxeInput,
  Button as VxeButton,
  Select as VxeSelect,
  Textarea as VxeTextarea,
  Pulldown as VxePulldown
} from 'vxe-table'
import { docTypeCodeList, statusList } from './config/index'
import debounce from 'lodash.debounce'

export default {
  name: 'ExpertRating',
  components: {
    VxeInput,
    VxeButton,
    VxeSelect,
    VxeTextarea,
    VxePulldown
  },
  data() {
    return {
      statusList,
      docTypeCodeList,
      dataForm: {},
      activeTabIndex: 0,
      isExpand: true,
      dataList: [[], []],
      itemLength: 0,
      type: 'detail',
      isInit: true,
      purchaserSearcValue: null,
      companyList: [],
      currencyList: []
    }
  },
  computed: {
    editable() {
      return this.$route.query.type === 'create' || [0, 5, 9].includes(this.dataForm.status)
    },
    tabList() {
      const tabs = [
        { title: this.$t('定价明细'), compName: 'ItemTab' }
        // { title: this.$t('附件'), compName: 'AttachmentTab' }
      ]
      return tabs
    },
    activeComponent() {
      let comp = ''
      switch (this.activeTabIndex) {
        case 0:
          // 物料明细
          comp = () => import('./components/itemTab.vue')
          break
        default:
          return
      }
      return comp
    },
    formRules() {
      return {
        title: [{ required: true, message: this.$t('请输入定价单名称'), trigger: 'blur' }],
        companyCode: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }]
      }
    },
    detailToolbar() {
      return [
        {
          code: 'back',
          name: this.$t('返回')
        },
        {
          code: 'viewOA',
          name: this.$t('OA审批进度'),
          status: '',
          isHidden: !this.dataForm?.oaApproveLink
        },
        {
          code: 'save',
          name: this.$t('保存'),
          isHidden: this.$route.query.type === 'edit' && [1, 2].includes(this.dataForm.status),
          status: 'primary'
        },
        {
          code: 'submit',
          name: this.$t('提交'),
          isHidden:
            this.$route.query.type === 'create' ||
            (this.$route.query.type === 'edit' && [1, 2].includes(this.dataForm.status)),
          status: 'primary'
        }
      ]
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    // 初始化
    async init() {
      await this.getCompanyList()
      if (this.$route.query.type === 'edit') {
        this.isExpand = false
        this.getHeaderInfo()
      }
    },

    // 获取公司下拉列表
    async getCompanyList() {
      const res = await this.$API.masterData.permissionCompanyList()
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.companyList = res.data
      }
    },
    // 监听定价明细数据
    itemDataChange(v) {
      this.itemLength = v
    },
    // 获取头部基础信息
    async getHeaderInfo() {
      const res = await this.$API.logistics.queryHeader({
        id: this.$route.query.id
      })
      if (res.code === 200) {
        this.dataForm = { ...res.data }
      }
    },

    // 下拉列表选中值修改
    handleValueChange(prefix, e) {
      const { value } = e
      switch (prefix) {
        // 选择公司
        case 'company':
          if (value) {
            const selectedItem = this.companyList.find((item) => item.orgCode === value)
            this.dataForm.companyId = selectedItem ? selectedItem.id : null
            this.dataForm.companyName = selectedItem ? selectedItem.orgName : null
          } else {
            this.dataForm.companyId = null
            this.dataForm.companyName = null
          }
          break
        default:
          break
      }
    },
    // 远程搜索查询-展开面板
    handlePulldown() {
      this.$refs.pulldownRef?.showPanel()
      this.$nextTick(() => {
        this.$refs.purchaserSearcInputRef?.focus()
      })
    },
    // 远程搜索查询-查询
    handlePulldownSearchInput: debounce(function (e) {
      this.getPurchaserList(e?.value)
    }, 500),
    // 远程搜索查询-选中
    handlePulldownItemSelected(item) {
      if (this.$refs.pulldownRef) {
        this.$set(this.dataForm, 'purchaserId', item.employeeId)
        this.$set(this.dataForm, 'purchaserName', item.employeeName)
        this.$refs.pulldownRef.hidePanel()
        this.purchaserSearcValue = null
      }
    },
    // 点击工具栏按钮
    handleClickToolBar(e) {
      switch (e.code) {
        case 'back':
          this.handleBack()
          break
        case 'save':
          this.handleSave()
          break
        case 'submit':
          this.handleSubmit(e.code)
          break
        case 'viewOA':
          window.open(this.dataForm?.oaApproveLink)
          break
        default:
          break
      }
    },
    // 切换tab页签
    handleTabChange(index) {
      this.$refs.mtTabsRef.activeTab = index
      this.activeTabIndex = index
    },
    // 返回
    handleBack() {
      this.$router.go(-1)
    },
    // 处理提交数据
    getSubmitData() {
      let params = {}
      this.$refs.dataFormRef.validate(async (valid) => {
        if (!valid) {
          //展开头部表单数据，显示报错信息
          this.isExpand = true
          return
        }
        params = {
          ...this.dataForm
        }
      })
      return params
    },
    // 保存
    async handleSave() {
      let params = this.getSubmitData()
      if (Object.entries(params).length === 0) return
      this.$store.commit('startLoading')
      const res = await this.$API.logistics.saveLogistics(params)
      this.$store.commit('endLoading')
      if (res.code === 200) {
        if (this.$route.query.type === 'create') {
          this.$toast({ content: this.$t('操作成功！'), type: 'success' })
          this.$router.replace({
            name: 'logistics-pricing-detail',
            query: {
              type: 'edit',
              id: res.data?.id
            }
          })
        } else {
          this.$refs.mainContent.handleSave(true)
        }
      }
    },

    // 提交
    async handleSubmit() {
      this.$store.commit('startLoading')
      const res = await this.$API.logistics.submitLogistics({ id: this.$route.query?.id })
      this.$store.commit('endLoading')
      if (res.code === 200) {
        this.$toast({ content: this.$t('操作成功！'), type: 'success' })
        this.getHeaderInfo()
        this.$refs.mainContent?.initTableData()
      }
    },
    // 弹框展示
    async showDialog(message, cssClass) {
      return new Promise((resolve) => {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t(message),
            cssClass
          },
          success: resolve
        })
      })
    },
    // 展开/收缩
    handleToggle() {
      this.isExpand = !this.isExpand
    }
  }
}
</script>

<style scoped lang="scss">
.logistics-full-height {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 8px 8px 0;
  background: #fff;
}
.body-container {
  height: calc(100% - 80px);
}
.top-container {
  flex: 1;
  width: 100%;
  .top-content {
    padding: 8px 8px 0 8px;
    background-color: #f9f9f9;
    border-radius: 8px;
    .top-info {
      display: flex;
      justify-content: space-between;
      .title {
        font-size: 20px;
        font-weight: 700;
        color: #292929;
        span {
          margin-right: 15px;
        }
        .sub-title {
          font-size: 15px;
          color: #777;
        }
      }
      .top-btns {
        text-align: right;
        margin-bottom: 10px;
      }
    }
    .top-form {
      .mt-form {
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
        justify-content: space-between;
        grid-gap: 5px;
      }
      .vxe-input,
      .vxe-select,
      .vxe-pulldown {
        width: 100%;
        height: 34px;
        line-height: 34px;
      }
      /deep/.vxe-pulldown--panel {
        min-width: unset !important;
        width: 100%;
      }
    }
  }
  .top-toggle-btn {
    width: 100%;
    div {
      width: 80px;
      height: 15px;
      line-height: 15px;
      margin: auto;
      text-align: center;
      background-color: #4a556b;
      color: #fff;
      font-weight: bold;
      border-radius: 0 0 6px 6px;
    }
  }
}

.top-hidden {
  .top-form {
    display: none;
  }
  .top-toggle-btn {
    div {
      transform: rotate(180deg);
      border-radius: 6px 6px 0 0;
    }
  }
}

.my-dropdown2 {
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 4px 0;
  max-height: 200px;
  border-radius: 4px;
  border: 1px solid #dadce0;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}
.list-item2 {
  line-height: 30px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 0 4px;

  .isSelected {
    font-weight: 700;
    color: #409eff;
  }
}
.list-item2:hover {
  background-color: #f5f7fa;
}
.empty-tip {
  width: 100%;
  line-height: 30px;
  padding: 0 4px;
  text-align: center;
  color: #c0c4cc;
}

::v-deep {
  .mt-form-item {
    margin-bottom: 15px;
    .label {
      margin: 0 10px 5px 0;
    }
  }
  .mt-tabs-container {
    background-color: #fff;
  }

  .mt-tabs-container ul.tab-container li.tab-item2.active {
    border-bottom: 2px solid #409eff;
  }
  .mt-tabs-container ul.tab-container li.tab-item2.active,
  .mt-tabs-container ul.tab-container li.tab-item2:hover {
    color: #31374e;
  }

  .vxe-textarea--inner {
    min-height: 34px;
    height: 100%;
  }
}
</style>
