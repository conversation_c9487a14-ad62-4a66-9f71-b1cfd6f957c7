<template>
  <div>
    <mt-tabs
      ref="mtTabsRef"
      :tab-id="$utils.randomString()"
      :e-tab="false"
      :data-source="tabList"
      :halt-select="false"
    />
    <div class="full-height vertical-flex-box">
      <!-- 自定义查询条件 -->
      <collapse-search
        class="toggle-container"
        :is-grid-display="true"
        :default-expand="true"
        @reset="handleReset"
        @search="handleSearch"
      >
        <mt-form ref="searchFormRef" :model="searchFormModel">
          <mt-form-item prop="status" :label="$t('状态')" label-style="top">
            <mt-select
              v-model="searchFormModel.status"
              css-class="rule-element"
              :data-source="statusList"
              :fields="{ text: 'text', value: 'value' }"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择状态')"
            />
          </mt-form-item>
          <mt-form-item prop="pointNo" :label="$t('定价单号')" label-style="top">
            <mt-input
              v-model="searchFormModel.pointNo"
              :show-clear-button="true"
              :placeholder="$t('请输入定价单号')"
            />
          </mt-form-item>
          <mt-form-item prop="title" :label="$t('标题')" label-style="top">
            <mt-input
              v-model="searchFormModel.title"
              :show-clear-button="true"
              :placeholder="$t('请输入标题')"
            />
          </mt-form-item>
          <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
            <mt-select
              v-model="searchFormModel.companyCode"
              css-class="rule-element"
              :data-source="companyList"
              :fields="{ text: 'text', value: 'orgCode' }"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择公司')"
              @change="handleCompanyChange"
            />
          </mt-form-item>
          <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
            <mt-input
              v-model="searchFormModel.createUserName"
              :show-clear-button="true"
              :placeholder="$t('请输入创建人')"
            />
          </mt-form-item>
          <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
            <mt-date-range-picker
              v-model="searchFormModel.createTime"
              :allow-edit="false"
              :placeholder="$t('请选择创建时间')"
              :open-on-focus="true"
              @change="handleDateChange"
            />
          </mt-form-item>
        </mt-form>
      </collapse-search>
      <!-- 表格 -->
      <sc-table
        ref="sctableRef"
        grid-id="cd7b81d5-2368-4b6b-b98e-396bbf702925"
        :loading="loading"
        :is-show-refresh-bth="true"
        :columns="listColumns"
        :table-data="tableData"
        @refresh="handleSearch"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            size="small"
            @click="handleClickToolBar(item)"
            >{{ item.name }}</vxe-button
          >
        </template>
      </sc-table>
      <!-- 分页 -->
      <mt-page
        ref="pageRef"
        class="flex-keep custom-page"
        :page-settings="pageSettings"
        :total-pages="pageSettings.totalPages"
        @currentChange="handleCurrentChange"
        @sizeChange="handleSizeChange"
      />
    </div>
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import { getTimeList } from '@/utils/obj'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { statusList, listToolbar } from './config/index'
import XEUtils from 'xe-utils'

export default {
  components: {
    ScTable,
    CollapseSearch
  },
  data() {
    return {
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 20,
        pageSizes: [20, 50, 200, 500, 1000, 2000]
      },
      pageInfo: {
        size: 20,
        current: 1
      },
      statusList,
      toolbar: listToolbar,
      searchFormModel: {
        createUserName: JSON.parse(sessionStorage.getItem('userInfo'))?.username || null,
        createTime: getTimeList(3)
      },
      tableData: [],
      loading: false,
      type: 'list',
      companyList: [],
      tabList: [{ title: this.$t('铁运(班列)') }]
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    // 列配置
    listColumns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          minWidth: 70,
          field: 'index',
          title: this.$t('序号')
        },
        {
          field: 'status',
          title: this.$t('状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = statusList.find((item) => item.value === row.status)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'pointNo',
          title: this.$t('定价单号'),
          minWidth: 180,
          slots: {
            default: ({ row, column }) => {
              return [<a on-click={() => this.handleClickCellTitle(row, column)}>{row.pointNo}</a>]
            }
          }
        },
        {
          field: 'title',
          title: this.$t('标题'),
          minWidth: 140
        },

        {
          field: 'companyCode',
          title: this.$t('公司'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [<span>{row.companyCode + '-' + row.companyName}</span>]
            }
          }
        },

        {
          field: 'oaApproveLink',
          title: this.$t('OA申请单查看'),
          minWidth: 130,
          slots: {
            default: ({ row, column }) => {
              return [
                <div>
                  <a
                    v-show={row.oaApproveLink}
                    on-click={() => this.handleClickCellTitle(row, column)}>
                    {this.$t('查看')}
                  </a>
                  <span v-show={!row.oaApproveLink}>-</span>
                </div>
              ]
            }
          }
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          minWidth: 140
        },
        {
          field: 'updateUserName',
          title: this.$t('最后更新人'),
          minWidth: 130
        },
        {
          field: 'updateTime',
          title: this.$t('最后更新时间'),
          minWidth: 140
        }
      ]
    }
  },
  mounted() {
    this.handleSearch()
    this.getCompanyList()
  },
  activated() {
    this.handleSearch()
  },
  methods: {
    // 获取公司下拉列表
    async getCompanyList(isInit) {
      const res = await this.$API.masterData.permissionCompanyList()
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.companyList = res.data

        if (isInit) {
          const selectItem = this.companyList?.find(
            (item) => item.orgCode === this.dataForm?.companyCode
          )
          selectItem && this.getPurchaseOrgList(selectItem.id, true)
          !this.dataForm.companyId && (this.dataForm.companyId = selectItem.id)
        }
      }
    },
    // 选择公司
    handleCompanyChange(e) {
      this.$set(this.searchFormModel, 'purchaseOrgCode', null)
      if (e.itemData.id) {
        this.getPurchaseOrgList(e.itemData.id)
      } else {
        this.purchaseOrgList = []
      }
    },
    // 选择定价对象
    handleFixPriceObjectChange(e) {
      this.$set(
        this.searchFormModel,
        'fixPriceObjectType',
        e.value ? e.itemData.sourcingObjType : null
      )
    },
    // 日期格式化
    handleDateChange(e) {
      if (e.startDate) {
        const startDate = XEUtils.toDateString(e.startDate, 'yyyy-MM-dd') + ' 00:00:00'
        const endDate = XEUtils.toDateString(e.endDate, 'yyyy-MM-dd') + ' 23:59:59'
        this.searchFormModel['createStartTime'] = XEUtils.timestamp(startDate)
        this.searchFormModel['createEndTime'] = XEUtils.timestamp(endDate)
      } else {
        this.searchFormModel['createStartTime'] = null
        this.searchFormModel['createEndTime'] = null
      }
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    // 查询
    async handleSearch(type) {
      // 解决修改查询条件后，查询结果有数据但未正常显示的问题（type: pageChange/refresh）
      if (!type) {
        this.pageInfo.current = 1
        this.$refs.pageRef.jumpNum = 1
        this.$refs.pageRef.currentPage = 1
      }
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      delete params.submitTime
      this.loading = true
      const res = await this.$API.logistics
        .queryLogisticsList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.tableData?.forEach((item, index) => {
          item.index = index + 1
        }) || []
        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (['delete', 'batchSubmit'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete': // 删除
          this.handleOperate(selectedRecords, e.code)
          break
        case 'export':
          this.handleExport()
          break
        default:
          break
      }
    },
    handleExport() {
      const params = { page: { size: 9999, current: 1 }, ...this.searchFormModel } // 筛选条件
      this.$API.logistics.exportLogisticsList(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 点击单元格
    handleClickCellTitle(row, column) {
      if (column.field === 'pointNo') {
        this.$router.push({
          name: 'logistics-pricing-detail',
          query: {
            type: 'edit',
            id: row.id,
            refreshId: Date.now()
          }
        })
      } else if (column.field === 'oaApproveLink') {
        window.open(row.oaApproveLink)
      }
    },
    // 新增
    handleAdd() {
      this.$router.push({
        name: 'logistics-pricing-detail',
        query: {
          type: 'create',
          refreshId: Date.now()
        }
      })
    },
    // 删除、提交
    handleOperate(list, type) {
      const tipMap = {
        delete: this.$t('删除')
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认${tipMap[type]}选中的数据？`)
        },
        success: async () => {
          const idList = []
          list.forEach((item) => idList.push(item.id))
          const res = await this.$API.logistics[type + 'Logistics']({ idList })
          if (res.code === 200) {
            this.$toast({ content: this.$t(`${tipMap[type]}成功！`), type: 'success' })
            this.handleSearch()
          }
        }
      })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.handleSearch('pageChange')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.handleSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
