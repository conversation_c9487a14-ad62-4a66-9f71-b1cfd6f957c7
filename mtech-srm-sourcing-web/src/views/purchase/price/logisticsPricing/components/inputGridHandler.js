const fieldMap = {
  // 下拉列表中字段和映射表中字段的映射值
  railwayMode: 'dictCode',
  tradeTermsCode: 'dictCode',
  transportTermsCode: 'dictCode',
  startAddressCode: 'dictCode',
  endAddressCode: 'dictCode',
  currencyCode: 'currencyCode'
}
/**
 * 数据联动
 *
 */

export const inputLink = (params) => {
  const mapData = {
    railwayMode: {
      railwayModeName: 'dictName'
    },
    tradeTermsCode: {
      tradeTermsName: 'dictName'
    },
    transportTermsCode: {
      transportTermsName: 'dictName'
    },
    startAddressCode: {
      startAddress: 'dictName'
    },
    endAddressCode: {
      endAddress: 'dictName'
    },
    currencyCode: {
      currencyName: 'currencyName'
    }
  }
  linkUpdate(mapData, params)
}

const linkUpdate = (mapData, params) => {
  const field = params.column.colId
  if (!Object.keys(mapData).includes(field)) return
  const { row } = getOptions(params)
  let obj = {}
  for (const linkField in mapData[field]) {
    obj[linkField] = row[mapData[field][linkField]]
  }
  // obj[field] = params.value
  // 设置数据
  setData(params, obj)
}
const getOptions = (params) => {
  const field = params.column.colId
  const value = params.value
  let dataSource = params.colDef?.cellEditorParams?.editConfig?.props?.dataSource
    ? params.colDef?.cellEditorParams?.editConfig?.props?.dataSource
    : params.colDef?.cellEditorParams?.editConfig?.dataSource
  dataSource = dataSource ? dataSource : []
  let row = null
  if (dataSource?.length) {
    row = dataSource.find((e) => e[fieldMap[field]] === value)
  }
  return {
    field,
    value,
    row
  }
}
/**
 * 数据设置
 * @param {} params
 * @returns
 */
const setData = (params, data) => {
  const rowData = Object.assign({}, params.node.data, data)
  params.node.setData(rowData)
}
