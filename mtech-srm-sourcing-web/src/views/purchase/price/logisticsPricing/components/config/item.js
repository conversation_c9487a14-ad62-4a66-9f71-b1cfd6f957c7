import { i18n } from '@/main'

//详情 - 物料明细 - 列表字段
export const itemColumns = (dictItems) => [
  { option: 'checkboxSelection', width: 55 },
  {
    field: 'categoryCode',
    headerName: i18n.t('品类编码'),
    width: 155
  },
  {
    field: 'categoryName',
    headerName: i18n.t('品类名称'),
    option: 'customEdit',
    editable: true,
    editConfig: {
      type: 'cellRemoteSelect',
      props: {
        url: '/sourcing/tenant/permission/queryCategorys',
        searchFields: ['categoryName', 'categoryCode'],
        fields: { text: 'categoryCode-categoryName', value: 'categoryName' }
      }
    },
    valueFormatter: (params) => {
      return params.data.categoryCode
        ? params.data.categoryCode + '-' + params.data.categoryName
        : params.data.categoryName
        ? params.data.categoryName
        : ''
    }
  },
  {
    field: 'railwayModeName',
    headerName: i18n.t('铁运模式名称'),
    hide: true
  },
  {
    field: 'railwayMode',
    headerName: i18n.t('铁运模式'),
    option: 'customEdit',
    editable: true,
    width: 118,
    editConfig: {
      type: 'select',
      props: {
        'show-clear-button': true,
        dataSource: dictItems.LOGISTICS_POINT_RAILWAY_RAILWAYMODE,
        fields: { text: 'dictName', value: 'dictCode' }
      }
    }
  },
  {
    field: 'tradeTermsName',
    headerName: i18n.t('贸易条款名称'),
    hide: true
  },
  {
    field: 'tradeTermsCode',
    headerName: i18n.t('贸易条款'),
    width: 160,
    option: 'customEdit',
    editable: true,
    editConfig: {
      type: 'select',
      props: {
        'show-clear-button': true,
        dataSource: dictItems.LOGISTICS_POINT_RAILWAY_TRADETERMS,
        fields: { text: 'dictName', value: 'dictCode' }
      }
    }
  },
  {
    field: 'transportTermsName',
    headerName: i18n.t('运输条款名称'),
    hide: true
  },
  {
    field: 'transportTermsCode',
    headerName: i18n.t('运输条款'),
    width: 160,
    option: 'customEdit',
    editable: true,
    editConfig: {
      type: 'select',
      props: {
        'show-clear-button': true,
        dataSource: dictItems.LOGISTICS_POINT_RAILWAY_TRANSPORTTERMS,
        fields: { text: 'dictName', value: 'dictCode' }
      }
    }
  },

  {
    field: 'loadingAddr',
    headerName: i18n.t('装货地'),
    width: 130,
    editable: true
  },
  {
    field: 'startAddress',
    headerName: i18n.t('起始地名称'),
    hide: true
  },
  {
    field: 'startAddressCode',
    headerName: i18n.t('起始地'),
    width: 160,
    option: 'customEdit',
    editable: true,
    editConfig: {
      type: 'select',
      props: {
        'show-clear-button': true,
        dataSource: dictItems.LOGISTICS_POINT_RAILWAY_STARTADDRESS,
        fields: { text: 'dictName', value: 'dictCode' }
      }
    }
  },
  {
    field: 'endAddress',
    headerName: i18n.t('目的地名称'),
    hide: true
  },
  {
    field: 'endAddressCode',
    headerName: i18n.t('目的地'),
    width: 140,
    option: 'customEdit',
    editable: true,
    editConfig: {
      type: 'select',
      props: {
        'show-clear-button': true,
        dataSource: dictItems.LOGISTICS_POINT_RAILWAY_ENDADDRESS,
        fields: { text: 'dictName', value: 'dictCode' }
      }
    }
  },
  {
    field: 'requireQuantity',
    headerName: i18n.t('需求量'),
    width: 110,
    editable: true,
    option: 'customEdit',
    editConfig: {
      type: 'number',
      props: {
        min: 0
      }
    }
  },
  {
    field: 'goodsFinishTime',
    headerName: i18n.t('货好时间'),
    editable: true,
    width: 110,
    option: 'customEdit',
    editConfig: {
      type: 'date',
      props: {
        format: 'yyyy-MM-dd',
        'show-clear-button': false
      }
    }
  },
  {
    field: 'demandStartTime',
    headerName: i18n.t('需求起运时间'),
    editable: true,
    width: 110,
    option: 'customEdit',
    editConfig: {
      type: 'date',
      props: {
        format: 'yyyy-MM-dd',
        'show-clear-button': false
      }
    }
  },
  {
    field: 'demandDeliveryTime',
    headerName: i18n.t('送达时间'),
    editable: true,
    width: 110,
    option: 'customEdit',
    editConfig: {
      type: 'date',
      props: {
        format: 'yyyy-MM-dd',
        'show-clear-button': false
      }
    }
  },
  {
    field: 'cabinetQuantity',
    headerName: i18n.t('可提供柜量'),
    editable: true,
    width: 110,
    option: 'customEdit',
    editConfig: {
      type: 'number',
      props: {
        min: 0
      }
    }
  },
  {
    field: 'internationalSupplierCode',
    headerName: i18n.t('国际段-供应商编码'),
    width: 110
  },
  {
    field: 'internationalSupplierName',
    headerName: i18n.t('国际段-供应商名称'),
    editable: true,
    option: 'customEdit',
    editConfig: {
      type: 'cellRemoteSelect',
      props: {
        url: '/masterDataManagement/tenant/supplier/criteria-query',
        searchFields: ['supplierCode', 'supplierName'],
        fields: { text: 'supplierCode-supplierName', value: 'supplierName' },
        recordsPosition: 'data'
      }
    }
  },
  {
    field: 'internationalPrice',
    headerName: i18n.t('国际段-单价'),
    editable: true,
    width: 110,
    option: 'customEdit',
    editConfig: {
      type: 'number',
      props: {
        min: 0
      }
    }
  },
  {
    field: 'domesticSupplierCode',
    headerName: i18n.t('国内段-供应商编码'),
    width: 90
  },
  {
    field: 'domesticSupplierName',
    headerName: i18n.t('国内段-供应商名称'),
    editable: true,
    option: 'customEdit',
    editConfig: {
      type: 'cellRemoteSelect',
      props: {
        url: '/masterDataManagement/tenant/supplier/criteria-query',
        searchFields: ['supplierCode', 'supplierName'],
        fields: { text: 'supplierCode-supplierName', value: 'supplierName' },
        recordsPosition: 'data'
      }
    }
  },
  {
    field: 'domesticPrice',
    headerName: i18n.t('国内段-单价'),
    editable: true,
    option: 'customEdit',
    width: 90,
    editConfig: {
      type: 'number',
      props: {
        min: 0
      }
    }
  },
  {
    field: 'totalPrice',
    headerName: i18n.t('合计单价'),
    width: 90
  },
  {
    field: 'currencyName',
    headerName: i18n.t('币种名称'),
    width: 120
  },
  {
    field: 'currencyCode',
    headerName: i18n.t('币种编码'),
    option: 'customEdit',
    editable: true,
    width: 140,
    editConfig: {
      type: 'select',
      props: {
        'show-clear-button': true,
        allowFiltering: true,
        dataSource: dictItems.currencyList,
        fields: { text: 'text', value: 'currencyCode' }
      }
    },
    valueFormatter(row) {
      return row.currencyCode
    }
  },
  {
    field: 'exchangeRateValue',
    headerName: i18n.t('汇率（USD兑CNY）'),
    option: 'customEdit',
    editable: true,
    width: 120,
    editConfig: {
      type: 'number',
      props: {
        min: 0
      }
    }
  },
  {
    field: 'validStartTime',
    headerName: i18n.t('价格开始时间'),
    width: 110,
    editable: true,
    option: 'customEdit',
    editConfig: {
      type: 'date',
      props: {
        format: 'yyyy-MM-dd',
        'show-clear-button': false
      }
    }
  },
  {
    field: 'validEndTime',
    headerName: i18n.t('价格结束时间'),
    width: 110,
    editable: true,
    option: 'customEdit',
    editConfig: {
      type: 'date',
      props: {
        format: 'yyyy-MM-dd',
        'show-clear-button': false
      }
    }
  },
  {
    field: 'remark',
    editable: true,
    headerName: i18n.t('备注')
  }
]

//详情 - 物料明细 - 操作按钮
export const itemToolbar = (status) => [
  {
    id: 'add',
    title: i18n.t('新增'),
    hide: ![0, 3, 7, 8].includes(status) //草稿、审批驳回
  },
  {
    id: 'save',
    title: i18n.t('保存'),
    hide: ![0, 3, 7, 8].includes(status) //草稿、审批驳回、审批废弃、审批撤回
  },
  {
    id: 'delete',
    title: i18n.t('删除'),
    hide: ![0, 3, 7, 8].includes(status) //草稿、审批驳回、审批废弃、审批撤回
  },
  {
    id: 'import',
    title: i18n.t('导入'),
    hide: ![0, 3, 7, 8].includes(status) //草稿、审批驳回、审批废弃、审批撤回
  },
  {
    id: 'export',
    title: i18n.t('导出')
  }
]
