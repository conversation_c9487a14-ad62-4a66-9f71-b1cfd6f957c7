/**
 * 公共接口查询
 */
export default {
  data() {
    return {
      companyList: [],
      purchaseOrgList: [],
      factoryList: [],
      sourcingExpandList: [],
      purchaserList: [],
      fixPriceObjectTypeList: [] // 定价对象
    }
  },
  mounted() {
    if (this.type === 'list') {
      this.getFactoryList()
      this.getFixPriceObjectTypeList()
    }
    if (this.$route.query.type !== 'edit') {
      this.getCompanyList()
    }
  },
  methods: {
    // 获取公司下拉列表
    async getCompanyList(isInit) {
      const res = await this.$API.masterData.permissionCompanyList()
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.companyList = res.data

        if (isInit) {
          const selectItem = this.companyList?.find(
            (item) => item.orgCode === this.dataForm?.companyCode
          )
          selectItem && this.getPurchaseOrgList(selectItem.id, true)
          !this.dataForm.companyId && (this.dataForm.companyId = selectItem.id)
        }
      }
    },
    // 获取采购组织下拉列表
    getPurchaseOrgList(companyId, isInit) {
      if (!companyId) {
        this.purchaseOrgList = []
        return
      }
      this.$API.masterData
        .permissionOrgList({
          orgId: companyId
        })
        .then((res) => {
          res.data.forEach((item) => {
            item.text = item.organizationCode + '-' + item.organizationName
          })
          this.purchaseOrgList = res.data

          if (isInit) {
            const selectItem = this.purchaseOrgList?.find(
              (item) => item.organizationCode === this.dataForm?.purchaseOrgCode
            )
            selectItem && this.getFactoryListByCompanyAndPur(companyId, selectItem.id, true)
          }
        })
    },
    // 获取工厂下拉列表
    async getFactoryList() {
      const res = await this.$API.customization.getPermissionSiteList({})
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.factoryList = res.data
      }
    },
    // 获取定价对象数据
    async getFixPriceObjectTypeList() {
      this.$API.businessConfig
        .getPostponeConfigList({
          page: {
            current: 1,
            size: 1000
          },
          condition: 'and',
          rules: [
            {
              field: 'sourcingMode',
              type: 'string',
              operator: 'equal',
              value: 'rfq'
            },
            {
              field: 'businessTypeCode',
              operator: 'equal',
              type: 'string',
              value: 'BTTCL004'
            },
            {
              field: 'status',
              operator: 'equal',
              type: 'string',
              value: 1
            }
          ]
        })
        .then((res) => {
          this.fixPriceObjectTypeList = res.data.records
        })
    },
    // 根据公司id、采购组织id获取工厂下拉列表
    async getFactoryListByCompanyAndPur(companyId, purOrgId, isInit) {
      const res = await this.$API.masterData.permissionSiteList({
        companyId,
        buOrgId: purOrgId
      })
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.factoryList = res.data

        // 采购组织下只有一个工厂，选择采购组织后自动带出
        if (!isInit && res.data?.length === 1) {
          const selectItem = res.data[0]
          this.$set(this.dataForm, 'factoryCode', selectItem.orgCode)
          this.$set(this.dataForm, 'factoryName', selectItem.orgName)
          this.getSourcingExpandList(selectItem.orgCode, this.dataForm.purchaseOrgCode)
        }
      }
    },
    // 拓展
    async getSourcingExpandList(factoryCode, purOrgCode) {
      if (!factoryCode) {
        this.sourcingExpandList = []
        return
      }
      const res = await this.$API.rfxDetail.purAllOrgWithSite({
        fuzzyParam: factoryCode,
        purOrgCode
      })
      if (res.code === 200 && res.data) {
        let dataSource = []
        res.data.forEach((v) => {
          v.siteOrgs?.forEach((x) => {
            dataSource.push({
              text: `${v.companyCode}-${v.businessOrganizationCode}-${v.businessOrganizationName}-${x.orgCode}-${x.orgName}`,
              value: v.companyCode + '+' + v.businessOrganizationCode + '+' + x.orgCode,
              data: {
                companyId: v.companyId,
                companyCode: v.companyCode,
                companyName: v.companyName,
                purchaseOrgId: v.id,
                purchaseOrgCode: v.businessOrganizationCode,
                purchaseOrgName: v.businessOrganizationName,
                factoryId: x.id,
                factoryCode: x.orgCode,
                factoryName: x.orgName
              }
            })
          })
        })
        this.sourcingExpandList = dataSource
      }
    },
    // 获取采购员下拉列表
    async getPurchaserList(searchText) {
      const res = await this.$API.masterData.getCurrentTenantEmployees({ fuzzyName: searchText })
      if (res.code === 200) {
        const tmp = []
        res.data.forEach((item) => {
          tmp.push({
            ...item,
            text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`
          })
        })
        this.purchaserList = tmp

        if (this.dataForm && tmp?.length) {
          const { employeeId, employeeName } = tmp[0]
          this.$set(this.dataForm, 'purchaserId', employeeId)
          this.$set(this.dataForm, 'purchaserName', employeeName)
        }
      }
    }
  }
}
