import { i18n } from '@/main'

// 状态列表
export const statusList = [
  { value: -1, text: i18n.t('已作废') },
  { value: 0, text: i18n.t('草稿') },
  { value: 1, text: i18n.t('审批中') },
  { value: 2, text: i18n.t('审批通过') },
  { value: 3, text: i18n.t('审批驳回') }
]

// 查询列表-操作按钮
export const listToolbar = [
  { code: 'add', name: i18n.t('新增'), status: 'info' },
  { code: 'delete', name: i18n.t('删除'), status: 'info' },
  { code: 'export', name: i18n.t('导出'), status: 'info' }
]
