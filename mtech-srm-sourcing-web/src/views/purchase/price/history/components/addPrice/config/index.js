import { i18n } from '@/main.js'
//按照数量设置阶梯
export const quantityStageConfig = (cellSaveEvent) => [
  {
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [['Add']]
    },
    grid: {
      allowFiltering: true,
      allowPaging: false,
      allowEditting: true,
      editSettings: {
        allowEditing: true,
        mode: 'Batch'
      },
      cellSave: cellSaveEvent,
      columnData: [
        // {
        //   width: "50",
        //   type: "checkbox",
        // },
        {
          field: 'stageRange',
          headerText: i18n.t('数量'),
          cssClass: '',
          allowEditing: false,
          cellTools: [
            { id: 'Edit', icon: 'icon_Editor', title: i18n.t('编辑') },
            { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') }
          ]
        },
        {
          field: 'taxedUnitPrice',
          headerText: i18n.t('含税价'),
          allowEditing: true
        },
        {
          field: 'untaxedUnitPrice',
          headerText: i18n.t('未含税'),
          allowEditing: false
        },
        {
          field: 'discountRate',
          headerText: i18n.t('折扣'),
          allowEditing: true
        }
      ],
      dataSource: []
    }
  }
]
//按照时间设置阶梯
export const timeStageConfig = (cellSaveEvent) => [
  {
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [['Add']]
    },
    grid: {
      allowFiltering: true,
      allowPaging: false,
      allowEditting: true,
      editSettings: {
        allowEditing: true,
        mode: 'Batch'
      },
      cellSave: cellSaveEvent,
      columnData: [
        // {
        //   width: "50",
        //   type: "checkbox",
        // },
        {
          field: 'stageRange',
          headerText: i18n.t('时间'),
          cssClass: '',
          allowEditing: false,
          cellTools: [
            { id: 'Edit', icon: 'icon_Editor', title: i18n.t('编辑') },
            { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') }
          ]
        },
        {
          field: 'taxedUnitPrice',
          headerText: i18n.t('含税价'),
          allowEditing: true
        },
        {
          field: 'untaxedUnitPrice',
          headerText: i18n.t('未含税'),
          allowEditing: false
        },
        {
          field: 'discountRate',
          headerText: i18n.t('折扣'),
          allowEditing: true
        }
      ],
      dataSource: []
    }
  }
]
//按照金额设置阶梯
export const amountStageConfig = (cellSaveEvent) => [
  {
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [['Add']]
    },
    grid: {
      allowFiltering: true,
      allowPaging: false,
      allowEditting: true,
      editSettings: {
        allowEditing: true,
        mode: 'Batch'
      },
      cellSave: cellSaveEvent,
      columnData: [
        // {
        //   width: "50",
        //   type: "checkbox",
        // },
        {
          field: 'stageRange',
          headerText: i18n.t('金额'),
          cssClass: '',
          allowEditing: false,
          cellTools: [
            { id: 'Edit', icon: 'icon_Editor', title: i18n.t('编辑') },
            { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') }
          ]
        },
        {
          field: 'taxedUnitPrice',
          headerText: i18n.t('含税价'),
          allowEditing: true
        },
        {
          field: 'untaxedUnitPrice',
          headerText: i18n.t('未含税'),
          allowEditing: false
        },
        {
          field: 'discountRate',
          headerText: i18n.t('折扣'),
          allowEditing: true
        }
      ],
      dataSource: []
    }
  }
]

import masterData from '@/apis/modules/service/masterData'

export const SKUTemplateConfig = [
  {
    useToolTemplate: false,
    toolbar: [],
    grid: {
      allowFiltering: true,
      height: 300,
      allowSelection: true,
      selectionSettings: {
        checkboxOnly: false
      },
      columnData: [
        {
          width: '150',
          field: 'itemId',
          headerText: i18n.t('SKU编号')
        },
        { width: '150', field: 'name', headerText: i18n.t('SKU名称') },
        {
          width: '150',
          field: 'itemCode',
          headerText: i18n.t('商品SPU/物料编号')
        },
        {
          width: '150',
          field: 'itemName',
          headerText: i18n.t('商品SPU/物料名称')
        },
        {
          width: '150',
          field: 'headImgId',
          headerText: i18n.t('SKU图片')
        },
        {
          width: '150',
          field: 'specificationModel',
          headerText: i18n.t('规格型号')
        },
        {
          width: '150',
          field: 'manufacturerName',
          headerText: i18n.t('制造商')
        },
        {
          width: '150',
          field: 'statusDescription',
          headerText: i18n.t('状态')
        }
      ],
      // dataSource: [],
      asyncConfig: {
        url: masterData.APIS.getSKUListUrl
      }
    }
  }
]
export const categoryTemplateConfig = [
  {
    useToolTemplate: false,
    toolbar: [],
    grid: {
      allowFiltering: true,
      height: 300,
      allowSelection: true,
      selectionSettings: {
        checkboxOnly: false
      },
      columnData: [
        {
          width: '150',
          field: 'itemCode',
          headerText: i18n.t('物料编号')
        },
        { width: '150', field: 'itemName', headerText: i18n.t('物料名称') },
        {
          width: '150',
          field: 'categoryResponse.categoryCode',
          headerText: i18n.t('品类')
        },
        {
          width: '150',
          field: 'itemDescription',
          headerText: i18n.t('规格型号')
        },
        {
          width: '150',
          field: 'oldItemCode',
          headerText: i18n.t('旧物料编号')
        },
        {
          width: '150',
          field: 'manufacturerName',
          headerText: i18n.t('制造商')
        }
      ],
      // dataSource: [],
      asyncConfig: {
        url: masterData.APIS.getItemListUrl,
        recordsPosition: 'data'
      }
    }
  }
]
