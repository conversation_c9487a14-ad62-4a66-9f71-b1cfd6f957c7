<template>
  <mt-dialog
    ref="dialog"
    class="aaa"
    css-class="price-details-dialog-container"
    :buttons="buttons"
    height="100%"
    :enable-resize="false"
    :position="{ X: 'right', Y: 'top' }"
    :header="modalData.title"
    @beforeClose="cancel"
  >
    <mt-form>
      <div class="flex">
        <mt-form-item :label="$t('价格条款')" style="width: 48%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.priceRecordCode"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('价格类型')" style="width: 48%">
          <mt-input
            float-label-type="Never"
            disabled
            :value="priceTypeMap[modalData.data.priceType]"
          ></mt-input>
        </mt-form-item>
      </div>

      <mt-form-item :label="$t('公司名称')">
        <mt-input
          float-label-type="Never"
          disabled
          :value="modalData.data.companyList.map((item) => item.companyName).join('、')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item :label="$t('工厂/地点')" style="100%">
        <mt-input
          float-label-type="Never"
          disabled
          :value="modalData.data.siteList.map((item) => item.siteName).join('、')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
    <h3 style="font-weight: bolder; padding: 16px 0">
      {{ $t('物料/品类信息：') }}
    </h3>
    <mt-form>
      <div class="flex">
        <mt-form-item :label="$t('品类')" style="width: 48%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.categoryName"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('物料名称')" style="width: 48%">
          <mt-input float-label-type="Never" disabled :value="modalData.data.itemName"></mt-input>
        </mt-form-item>
      </div>
      <div class="flex">
        <mt-form-item :label="$t('物料编码')" style="width: 48%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.itemCode"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('适用税率')" style="width: 48%">
          <mt-input float-label-type="Never" disabled :value="modalData.data.taxName"></mt-input>
        </mt-form-item>
      </div>
      <div class="flex">
        <mt-form-item :label="$t('单位')" style="width: 48%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.unitName"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('币种')" style="width: 48%">
          <mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.currencyName"
          ></mt-input>
        </mt-form-item>
      </div>
      <div class="flex">
        <mt-form-item label="SKU" style="width: 100%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.skuName"
          ></mt-input>
        </mt-form-item>
      </div>
    </mt-form>
    <h3 style="font-weight: bolder; padding: 16px 0">
      {{ $t('供应商选择：') }}
    </h3>
    <mt-form>
      <div class="flex">
        <mt-form-item :label="$t('供应商名称')" style="width: 48%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.supplierName"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('供应商编码')" style="width: 48%">
          <mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.supplierCode"
          ></mt-input>
        </mt-form-item>
      </div>
      <div class="flex">
        <mt-form-item :label="$t('归属合同')" style="width: 48%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.contractName"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('合同协议编号')" style="width: 48%">
          <mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.contractCode"
          ></mt-input>
        </mt-form-item>
      </div>
      <div class="flex">
        <mt-form-item :label="$t('有效起始')" style="width: 48%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.validStartTime"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('有效截至')" style="width: 48%">
          <mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.validEndTime"
          ></mt-input>
        </mt-form-item>
      </div>
      <div class="flex">
        <mt-form-item :label="$t('阶梯类型')" style="width: 100%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="differentialPricingTypeMap[modalData.data.stageType]"
          ></mt-input>
        </mt-form-item>
      </div>
    </mt-form>
    <h3 style="font-weight: bolder; padding: 16px 0">{{ $t('阶梯维护：') }}</h3>
    <mt-template-page
      :hidden-tabs="true"
      style="height: fit-content"
      :template-config="differentialPricingConfig"
    />
  </mt-dialog>
</template>

<script>
import Vue from 'vue'
import { PriceTypeMap, DifferentialPricingTypeMap } from '@/constants'
export default {
  name: 'PriceDetailsDialog',
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('确定') }
        }
      ],
      priceTypeMap: PriceTypeMap,
      differentialPricingTypeMap: DifferentialPricingTypeMap,
      differentialPricingConfig: [
        {
          grid: {
            allowFiltering: true,
            allowPaging: false,
            columnData: [
              {
                field: 'stageFrom&stageTo',
                headerText: this.$t('数量'),
                template: function () {
                  return {
                    template: Vue.component('stage', {
                      template: `<span>{{data.stageFrom}}-{{data.stageTo}}</span>`,
                      data() {
                        return { data: {} }
                      }
                    })
                  }
                }
              },
              {
                field: 'taxedUnitPrice',
                headerText: this.$t('含税价')
              },
              {
                field: 'untaxedUnitPrice',
                headerText: this.$t('未税价')
              },
              {
                field: 'discountRate',
                headerText: this.$t('折扣')
              }
            ],
            dataSource: [{}]
          }
        }
      ]
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.differentialPricingConfig[0].grid.dataSource = this.modalData.data.stageList
  },
  methods: {
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.price-details-dialog-container {
  &.e-dialog .e-dlg-content {
    padding: 40px;
  }

  .flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
