import { i18n } from '@/main.js'
export const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  // { id: "Edit", icon: "icon_solid_edit", title:  i18n.t("修改") },
  // { id: "Delete", icon: "icon_solid_Delete", title:  i18n.t("删除") },
  { id: 'ImportEXCEL', icon: 'icon_solid_upload', title: i18n.t('导入Excel') }
]

export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'priceRecordCode',
    headerText: i18n.t('条款编码'),
    width: '250',
    cssClass: 'field-content'
    // cellTools: [],
    // cellTools: ["edit", "delete"],
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('草稿'), 1: i18n.t('启用'), 2: i18n.t('禁用') }
    }
    // cellTools: [
    //   {
    //     id: "start",
    //     icon: "icon_solid_Activateorder",
    //     title: i18n.t("启用"),
    //     visibleCondition: (data) => {
    //       return data["status"] == 0;
    //     },
    //   },
    //   {
    //     id: "stop",
    //     icon: "icon_solid_Cancel",
    //     title: i18n.t("禁用"),
    //     visibleCondition: (data) => {
    //       return data["status"] == 1;
    //     },
    //   },
    // ],
  },
  {
    field: 'priceType',
    headerText: i18n.t('价格类型'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('到物料'),
        1: i18n.t('到SKU'),
        2: i18n.t('到品类'),
        3: i18n.t('到供应商')
      }
    }
  },
  {
    field: 'companyList',
    headerText: i18n.t('公司名称'),
    width: '200',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['companyName']
        } else {
          return '-'
        }
      }
    }
    // template: function () {
    //   return {
    //     template: Vue.component("actionOption", {
    //       template: `<div><span>{{data.companyList[0]?data.companyList[0].companyName:'--'}}</span>
    //                   <mt-tooltip
    //                   target="#title"
    //                   cssClass="e-tooltip-css"
    //                   ref="tooltipTitle"
    //                   position="BottomCenter"
    //                   opensOn="Click"
    //                   cssClass="field-content"
    //                   v-if='data.companyList&&data.companyList.length>1'
    //                   :content="content" >
    //                   <div id="container" >
    //                     <div id="tooltipContent">
    //                       <div class="content">
    //                         <MtIcon name="icon_Activation" id="title" />
    //                       </div>
    //                     </div>
    //                   </div>
    //                 </mt-tooltip>
    //                </div>
    //                `,
    //       data() {
    //         return { data: {} };
    //       },
    //       computed: {
    //         returnData() {
    //           return this.data;
    //         },
    //       },
    //       methods: {
    //         content() {
    //           const _data = this.returnData.companyList;
    //           console.warn(this.returnData);
    //           return {
    //             template: Vue.component("demo", {
    //               template: `
    //                         <div id="tooltip" ref="content">
    //                           <h2 v-for='items in item'>{{items.companyName}}</h2>
    //                         </div>
    //                         `,
    //               data() {
    //                 return {
    //                   item: _data,
    //                 };
    //               },
    //             }),
    //           };
    //         },
    //       },
    //     }),
    //   };
    // },
  },
  {
    field: 'siteList',
    headerText: i18n.t('工厂/地点'),
    width: '200',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['siteName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'priceRecordId',
    headerText: i18n.t('库存组织编码')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类(物料类别)')
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'skuCode',
    headerText: i18n.t('SKU编码')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'unitName',
    headerText: i18n.t('单位')
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种')
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('含税价')
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('未含税')
  },
  {
    field: 'stageFrom',
    headerText: i18n.t('阶梯价格'),
    cellTools: [
      {
        id: 'check',
        title: i18n.t('查看'),
        visibleCondition: (data) => {
          return data
        }
      }
    ]
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('有效起始')
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('有效截至')
  },
  {
    field: 'contractCode',
    headerText: i18n.t('来源合同编码')
  },
  {
    field: 'unkown',
    headerText: i18n.t('合同起始'),
    valueConverter: { type: 'placeholder', placeholder: '-' }
  },
  {
    field: 'unkown',
    headerText: i18n.t('合同截至'),
    valueConverter: { type: 'placeholder', placeholder: '-' }
  },
  {
    field: 'unkown',
    headerText: i18n.t('协议编码'),
    valueConverter: { type: 'placeholder', placeholder: '-' }
  },
  {
    field: 'unkown',
    headerText: i18n.t('供应商物料编码'),
    valueConverter: { type: 'placeholder', placeholder: '-' }
  }
]
