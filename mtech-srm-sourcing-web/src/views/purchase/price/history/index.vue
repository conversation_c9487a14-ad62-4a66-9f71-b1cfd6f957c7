<template>
  <div class="full-height">
    <input type="file" accept=".xlsx" hidden ref="excel-uploader" />
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :padding-top="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { toolbar, columnData } from './config'

export default {
  data() {
    return {
      pageConfig: [
        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          toolbar,
          grid: {
            allowFiltering: true,
            columnData,
            asyncConfig: {
              url: this.$API.priceService.getPriceRecords
            }
          }
        }
      ]
    }
  },
  mounted() {
    this.$refs['excel-uploader'].addEventListener('change', (event) => {
      let file = event.target.files[0]
      if (file) {
        const formData = new FormData()
        formData.append('file', file)
        this.$API.priceService.importExcel(formData).then(() => {
          this.$toast({ type: 'success', content: this.$t('导入成功') })
          this.$refs.templateRef.refreshCurrentGridData()
        })
      }
    })
  },
  methods: {
    //表格按钮-点击事件
    handleClickToolBar(ejsEventObject) {
      let _selectGridRecords = ejsEventObject.grid.getSelectedRecords()
      console.log('use-handleClickToolBar', ejsEventObject, _selectGridRecords)
      if (ejsEventObject.toolbar.id == 'Add') {
        this.handleAddPrice()
      } else if (ejsEventObject.toolbar.id == 'ImportEXCEL') {
        this.$refs['excel-uploader'].click()
      }
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      if (e.tool.id == 'start') {
        //启用操作
        this.handleUpdateConfigStatus([e.data.id], 1)
      } else if (e.tool.id == 'stop') {
        //停用操作
        this.handleUpdateConfigStatus([e.data.id], 2)
      } else if (e.tool.id == 'check') {
        this.handleCheckPrice(e.data.stageList, e.data.taxRate)
      }
    },
    handleUpdateConfigStatus(ids, status) {
      //enableStatus状态 0 未启用 1 启用 2 禁用
      let _statusMap = [this.$t('草稿'), this.$t('启用'), this.$t('禁用')]
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: `${this.$t('确认更新状态为')}${_statusMap[status]}？`
        },
        success: () => {}
      })
    },
    //单元格标题点击操作
    handleClickCellTitle(ejsEventObject) {
      console.log('use-handleClickCellTitle', ejsEventObject)

      if (ejsEventObject.field === 'priceRecordCode') {
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "router/purchase/price/list/components/addPrice" */ './components/details/index.vue'
            ),
          data: {
            title: this.$t('价格条款明细'),
            data: ejsEventObject.data
          }
        })
      }
    },
    handleAddPrice() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/price/list/components/addPrice" */ './components/addPrice/index.vue'
          ),
        data: {
          title: this.$t('新增价格记录')
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    handleCheckPrice(stageList, taxRate) {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/price/list/princeDialog" */ './princeDialog.vue'
          ),
        data: {
          title: this.$t('阶梯价格'),
          stageList: stageList.map((item) => {
            item.taxRate = taxRate
            return item
          })
        },
        success: () => {
          // this.$refs.templateRef.refreshCurrentGridData();
        }
      })
    }
  }
}
</script>
