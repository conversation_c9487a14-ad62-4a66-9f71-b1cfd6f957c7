<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickCellTool="handleClickCellTool"
    />
  </div>
</template>

<script>
import { columnData } from './config/index'
export default {
  data() {
    return {
      pageConfig: [
        {
          // freezeOperationColumn: true,
          toolbar: ['Add', 'Delete'],
          gridId: 'dad8c54c-7a94-41d3-af5f-64b96e57fc7c',
          grid: {
            allowFiltering: true,
            allowSorting: false,
            columnData: columnData,
            asyncConfig: {
              url: 'sourcing/tenant/evaluate/query'
            }
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      if (toolbar.id == 'Add') {
        this.$router.push({
          path: 'pur-valuation-management-detail',
          query: {
            type: 0,
            key: this.$utils.randomString()
          }
        })
      }
      const selectRows = gridRef.ejsRef.getSelectedRecords()
      if (selectRows.length === 0 && toolbar.id == 'Delete') {
        this.$toast({
          content: this.$t('请先选择需要删除的数据'),
          type: 'warning'
        })
        return
      }

      let ids = selectRows.map((item) => item.id)
      if (toolbar.id == 'Delete') {
        if (selectRows.some((item) => item.evaluateStatus == 1 || item.evaluateStatus == 2)) {
          this.$toast({
            content: this.$t('存在审批中和已完成状态的数据，不可删除！'),
            type: 'warning'
          })
          return
        }
        this.handleDelete(ids)
      }
    },
    handleClickCellTitle(e) {
      if (e.field == 'evaluateCode') {
        this.$router.push({
          name: `pur-valuation-management-detail`,
          query: {
            id: e.data.id,
            type: 1,
            key: this.$utils.randomString()
          }
        })
      }
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'delete') {
        this.handleDelete([e.data.id])
      }
      if (e.tool.id == 'edit') {
        this.$router.push({
          name: `pur-valuation-management-detail`,
          query: {
            id: e.data.id,
            type: 1,
            key: this.$utils.randomString()
          }
        })
      }
    },
    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          this.$API.priceService.delEvaluate(ids).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped></style>
