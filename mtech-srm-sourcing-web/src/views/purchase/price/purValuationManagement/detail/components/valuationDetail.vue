<template>
  <div class="full-height">
    <mt-template-page
      ref="valuationDetailRef"
      :padding-top="true"
      :template-config="pageConfig"
      @handleSelectTab="handleSelectTab"
      @actionBegin="valuationActionBegin"
      @actionComplete="valuationActionComplete"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @selectedChanged="selectedChanged"
    />
    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-name="downTemplateName"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import { detailColumn, attachColumn } from '../config/index'
import { cloneDeep } from 'lodash'
export default {
  components: {
    UploadExcelDialog: () => import('@/components/Upload/uploadExcelDialog.vue')
  },
  props: {
    detailInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      type: 'view',
      delValuationRecord: [],
      delAttachmentRecord: [],
      downTemplateName: null, // 下载模板文件名
      downTemplateParams: {}, // 下载模板参数
      requestUrls: {}, // 上传下载接口地址
      uploadParams: {},
      siteInfo: {},
      tabIndex: 0,
      valuationAddId: '1',
      attachmentAddId: '1',
      valuationNowEditRowFlag: '', //当前编辑的行id
      attachmentNowEditRowFlag: '', //当前编辑的行id
      selectedValuationOtherInfo: {},
      isInner: '',
      pageConfig: [
        {
          // freezeOperationColumn: true,
          useToolTemplate: false,
          title: this.$t('估价明细'),
          toolbar: {
            tools: [
              [
                {
                  id: 'Add',
                  icon: 'icon_solid_add',
                  title: this.$t('新增'),
                  visibleCondition: () => this.type == 'edit'
                },
                // {
                //   id: "save",
                //   icon: "icon_table_save",
                //   title: this.$t("保存"),
                //   visibleCondition: () => this.type == "edit",
                // },
                {
                  id: 'Delete',
                  icon: 'icon_solid_Delete',
                  title: this.$t('删除'),
                  visibleCondition: () => this.type == 'edit'
                },
                {
                  id: 'Cancel',
                  icon: 'icon_solid_Cancel',
                  title: this.$t('取消'),
                  visibleCondition: () => this.type == 'edit'
                },
                {
                  id: 'import',
                  icon: 'icon_solid_Import',
                  title: this.$t('导入'),
                  visibleCondition: () => this.type == 'edit'
                },
                {
                  id: 'export',
                  icon: 'icon_solid_export',
                  title: this.$t('导出'),
                  visibleCondition: () => this.type == 'edit'
                }
              ]
            ]
          },
          // gridId: ,
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              // showDeleteConfirmDialog: true,
              newRowPosition: 'Top'
            },
            lineSelection: true,
            allowPaging: false,
            columnData: detailColumn,
            dataSource: []
          }
        },
        {
          title: this.$t('附件信息'),
          // freezeOperationColumn: true,
          useToolTemplate: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'Add',
                  icon: 'icon_solid_add',
                  title: this.$t('新增'),
                  visibleCondition: () => this.type == 'edit'
                },
                // {
                //   id: "save",
                //   icon: "icon_table_save",
                //   title: this.$t("保存"),
                //   visibleCondition: () => this.type == "edit",
                // },
                {
                  id: 'Delete',
                  icon: 'icon_solid_Delete',
                  title: this.$t('删除'),
                  visibleCondition: () => this.type == 'edit'
                },
                {
                  id: 'Cancel',
                  icon: 'icon_solid_Cancel',
                  title: this.$t('取消'),
                  visibleCondition: () => this.type == 'edit'
                },
                {
                  id: 'download',
                  icon: 'icon_solid_Download',
                  title: this.$t('下载'),
                  visibleCondition: () => this.type == 'edit'
                }
              ]
            ]
          },
          // gridId: ,
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              // showDeleteConfirmDialog: true,
              newRowPosition: 'Top'
            },
            lineSelection: true,
            allowPaging: false,
            allowSorting: false,
            columnData: attachColumn,
            dataSource: []
          }
        }
      ]
    }
  },
  watch: {
    siteInfo: {
      handler(value) {
        console.log('siteInfoChange---', value)
        // this.privateValue = value;
        if (this.pageConfig[0].grid.dataSource.length > 0) {
          let _tempRecords = cloneDeep(this.pageConfig[0].grid.dataSource)
          _tempRecords.forEach((item) => {
            if (item.siteCode != value.orgCode) {
              item.siteName = value.orgName
              item.siteCode = value.orgCode
              item.itemCode = null
              item.itemName = null
              item.categoryName = null
              item.unitName = null
            }
          })
          this.$set(this.pageConfig[0].grid, 'dataSource', _tempRecords)
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    if (
      !this.detailInfo?.rfxEvaluate ||
      this.detailInfo?.rfxEvaluate?.evaluateStatus == 0 ||
      this.detailInfo?.rfxEvaluate?.evaluateStatus == 3 ||
      this.detailInfo?.rfxEvaluate?.evaluateStatus == 4
    ) {
      this.type = 'edit'
    } else {
      this.type = 'view'
    }
    if (this.detailInfo?.detailEntities?.length > 0) {
      this.detailInfo.detailEntities.forEach((element) => {
        this.pageConfig[0].grid.dataSource.push({
          ...element,
          categoryName: element.typeDesc,
          unitName: element.unit,
          valuationAddId: this.valuationAddId++
        })
      })
    }
    if (this.detailInfo?.sourcingFiles?.length > 0) {
      this.detailInfo.sourcingFiles.forEach((element) => {
        this.pageConfig[1].grid.dataSource.push({
          drawing: JSON.stringify([{ ...element }]),
          ...element,
          attachmentAddId: this.attachmentAddId++
        })
      })
    }
  },
  methods: {
    selectedChanged(val) {
      this.isInner = val.itemInfo.isInner
      Object.assign(this.selectedValuationOtherInfo, val.itemInfo || {})
      console.log(this.selectedValuationOtherInfo, '最新的额外数据导入的')
    },
    handleSelectTab(e) {
      this.tabIndex = e
    },
    handleClickToolBar(e) {
      if (e.toolbar.id == 'Cancel') {
        e.grid.closeEdit()
      }
      if (e.toolbar.id == 'import') {
        if (!this.$route.query?.id && !this.detailInfo.rfxEvaluate?.id) {
          this.$toast({
            content: this.$t('请先填写并保存头部主单信息'),
            type: 'warning'
          })
          return
        } else {
          this.handleUpload()
        }
      } else if (e.toolbar.id === 'export') {
        if (!this.$route.query?.id && !this.detailInfo.rfxEvaluate?.id) {
          this.$toast({
            content: this.$t('未保存的单据无法进行此操作'),
            type: 'warning'
          })
          return
        } else {
          this.handleDownload()
        }
      }
      const { toolbar, gridRef, tabIndex } = e
      console.log('gridRef', e, this.siteInfo)
      if (toolbar.id == 'Add') {
        if (Object.keys(this.siteInfo).length == 0 && tabIndex == 0) {
          this.$toast({
            content: this.$t('请先选择工厂'),
            type: 'warning'
          })
          return
        } else {
          gridRef.ejsRef.addRecord()
        }
        // this.pageConfig[this.tabIndex].grid.dataSource.push({});
      }
      const selectRows = gridRef.ejsRef.getSelectedRecords()
      if (selectRows.length === 0 && (toolbar.id == 'Delete' || toolbar.id == 'download')) {
        this.$toast({
          content: this.$t('请先选择数据'),
          type: 'warning'
        })
        return
      }
      console.log('selectRows--', selectRows)
      if (toolbar.id == 'Delete') {
        if (this.tabIndex == 0) {
          let _valuationAddIds = selectRows.map((e) => e.valuationAddId)
          selectRows.forEach((item) => {
            if (item?.id) {
              this.delValuationRecord.push({ ...item, flag: 2 })
            }
          })
          this.pageConfig[0].grid.dataSource = this.pageConfig[0].grid.dataSource.filter(
            (item) => !_valuationAddIds.includes(item.valuationAddId)
          )
        } else {
          let _attachmentAddIds = selectRows.map((e) => e.attachmentAddId)
          selectRows.forEach((item) => {
            if (item?.id) {
              this.delAttachmentRecord.push({ ...item, flag: 2 })
            }
          })
          this.pageConfig[0].grid.dataSource = this.pageConfig[0].grid.dataSource.filter(
            (item) => !_attachmentAddIds.includes(item.attachmentAddId)
          )
        }
      }
      if (toolbar.id == 'download') {
        this.downloadFiles(selectRows)
      }
    },
    handleClickCellTool() {},
    handleClickCellTitle() {},
    valuationActionBegin(args) {
      let { data, requestType } = args
      // 行内数据新增
      console.log('valuationActionBegin--', args)
      if (this.tabIndex === 0) {
        if (requestType == 'add') {
          let newDate = new Date()
          args.data.valuationAddId = this.valuationAddId++
          this.valuationNowEditRowFlag = args.data.valuationAddId
          Object.assign(args.data, {
            siteName: this.siteInfo.orgName,
            siteCode: this.siteInfo.orgCode,
            startTime: `${newDate.getUTCFullYear()}-${newDate.getMonth() + 1}-1`,
            endTime: '9999-12-31'
          })
        } else if (requestType == 'beginEdit') {
          this.valuationNowEditRowFlag = args.rowData.valuationAddId
          this.selectedValuationOtherInfo = {}
          Object.assign(this.selectedValuationOtherInfo, {
            siteName: this.siteInfo.orgName,
            siteCode: this.siteInfo.orgCode
          })
        } else if (requestType == 'save') {
          data.isInner = this.isInner
        }
      } else {
        if (requestType == 'add') {
          args.data.attachmentAddId = this.attachmentAddId++
          this.attachmentNowEditRowFlag = args.data.attachmentAddId
        } else if (requestType == 'beginEdit') {
          this.attachmentNowEditRowFlag = args.rowData.attachmentAddId
        } else if (requestType == 'save') {
          data.isInner = this.isInner
        }
      }
    },
    valuationActionComplete(item) {
      let { data, rowIndex, requestType, index } = item
      if (this.tabIndex == 0) {
        let row = this.getValuationRow()
        if (requestType == 'save') {
          // 验证必输
          if (!data.itemCode) {
            this.$toast({
              content: this.$t('未选择物料'),
              type: 'warning'
            })
            let _index
            if (Object.prototype.hasOwnProperty.call(item, 'index')) {
              _index = index
            } else {
              _index = rowIndex
            }
            console.log('_index---', _index)
            this.$refs.valuationDetailRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(_index)
            this.$refs.valuationDetailRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
            return
          }
          let _addIdList = this.pageConfig[0].grid.dataSource.map((e) => e.valuationAddId)
          if (row && _addIdList.includes(row.valuationAddId)) {
            this.pageConfig[0].grid.dataSource.some((item) => {
              if (item.valuationAddId == row.valuationAddId) {
                Object.assign(item, row)
              }
            })
          } else if (row && !_addIdList.includes(row.valuationAddId)) {
            this.pageConfig[0].grid.dataSource.push(row)
          }
        }
        this.$refs.valuationDetailRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
      } else {
        let row = this.getValuationRow()
        if (requestType == 'save') {
          // 验证必输
          console.log('data---', data, row, item)
          if (!data.drawing || JSON.parse(data.drawing).length == 0) {
            this.$toast({
              content: this.$t('未上传文件'),
              type: 'warning'
            })
            let _index
            if (Object.prototype.hasOwnProperty.call(item, 'index')) {
              _index = index
            } else {
              _index = rowIndex
            }
            console.log('_index---', _index)
            this.$refs.valuationDetailRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(_index)
            this.$refs.valuationDetailRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
            return
          }
          let _addIdList = this.pageConfig[1].grid.dataSource.map((e) => e.attachmentAddId)
          if (row && _addIdList.includes(row.attachmentAddId)) {
            this.pageConfig[1].grid.dataSource.some((item) => {
              if (item.attachmentAddId == row.attachmentAddId) {
                Object.assign(item, row)
              }
            })
          } else if (row && !_addIdList.includes(row.attachmentAddId)) {
            this.pageConfig[1].grid.dataSource.push(row)
          }
        }
        this.$refs.valuationDetailRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
      }
    },
    getValuationRow() {
      //获取编辑或者新增的数据
      let currentRecords = this.$refs.valuationDetailRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      let row = cloneDeep(this.selectedValuationOtherInfo)
      let info = {}
      if (this.tabIndex == 0) {
        currentRecords.some((item) => {
          if (item.valuationAddId == this.valuationNowEditRowFlag) {
            Object.assign(item, row)
            info = item
          }
        })
      } else {
        currentRecords.some((item) => {
          if (item.attachmentAddId == this.attachmentNowEditRowFlag) {
            Object.assign(item, row)
            info = item
          }
        })
      }
      return info
    },
    // 上传（显示弹窗）
    handleUpload() {
      this.requestUrls = {
        templateUrlPre: 'priceService',
        templateUrl: 'exportExcel',
        uploadUrl: 'uploadEvaluateDetail',
        rfxId: this.$route.query?.id || this.detailInfo.rfxEvaluate.id
      }
      this.downTemplateName = this.$t('导入模板')
      this.showUploadExcel(true)
    },
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = [] // 清空数据
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    downloadFiles(rows) {
      rows.forEach((itemFiles) => {
        this.handlerDownloadPrivateFile(itemFiles)
      })
    },
    handlerDownloadPrivateFile(data) {
      let tempRecord = JSON.parse(data.drawing)[0]
      this.$API.fileService
        .downloadPrivateFile({
          id: tempRecord?.sysFileId || tempRecord.id
        })
        .then((res) => {
          download({
            fileName: data?.fileName || tempRecord.fileName,
            blob: res.data
          })
        })
    },
    // 导出
    handleDownload() {
      let params = new URLSearchParams()
      params.append('rfxId', this.$route.query?.id || this.detailInfo.rfxEvaluate.id)
      this.$API.priceService.exportEvaluateDetail(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 上传成功后，获取到的数据
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.$parent.isShow = false
      this.$parent.getDetail(this.$route.query?.id || this.detailInfo.rfxEvaluate.id)
      this.$toast({
        content: this.$t('导入成功'),
        type: 'success'
      })
    }
  }
}
</script>
<style lang="scss" scoped></style>
