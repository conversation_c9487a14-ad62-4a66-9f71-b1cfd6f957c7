<template>
  <div>
    <div class="top-info">
      <div class="btns-wrap">
        <mt-button @click.native="goBack">{{ $t('返回') }}</mt-button>
        <mt-button v-if="type == 'edit'" @click.native="editProject">{{ $t('保存') }}</mt-button>
        <mt-button v-if="type == 'edit'" @click.native="saveProject">{{
          $t('保存并提交')
        }}</mt-button>
      </div>
    </div>
    <div class="top-form">
      <mt-form ref="formInfo" :model="formInfo" :rules="rules" :auto-complete="false">
        <mt-form-item
          class="form-item"
          :label="$t('估价单编码')"
          label-style="left"
          label-width="74px"
        >
          <mt-input
            :disabled="true"
            v-model="formInfo.evaluateCode"
            :rows="2"
            maxlength="200"
            float-label-type="Never"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('估价单标题')"
          label-style="left"
          label-width="74px"
          prop="evaluateTitle"
        >
          <mt-input
            :disabled="type != 'edit'"
            v-model="formInfo.evaluateTitle"
            maxlength="50"
            float-label-type="Never"
            :placeholder="$t('请输入')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('公司')"
          label-style="left"
          label-width="74px"
          prop="companyCode"
        >
          <mt-select
            :disabled="type != 'edit'"
            v-model="formInfo.companyCode"
            :data-source="companyList"
            :show-clear-button="true"
            :allow-filtering="true"
            :placeholder="$t('请选择')"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            @change="companyChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('采购组织')"
          label-style="left"
          label-width="74px"
          prop="purOrgCode"
        >
          <mt-select
            :disabled="type != 'edit'"
            v-model="formInfo.purOrgCode"
            :data-source="purOrgList"
            :show-clear-button="true"
            :allow-filtering="true"
            :placeholder="$t('请选择')"
            :fields="{ text: 'organizationName', value: 'organizationCode' }"
            @change="purOrgCodeChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('工厂')"
          label-style="left"
          label-width="74px"
          prop="siteId"
        >
          <mt-select
            :disabled="type != 'edit'"
            v-model="formInfo.siteId"
            :data-source="siteList"
            :fields="{ text: 'orgName', value: 'id' }"
            :show-clear-button="true"
            :allow-filtering="true"
            :placeholder="$t('请选择')"
            @change="siteChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('采购组')" label-style="left" label-width="74px">
          <mt-select
            :disabled="type != 'edit'"
            v-model="formInfo.groupCode"
            :data-source="purGroupList"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
            :fields="{ text: 'label', value: 'groupCode' }"
            @change="purGroupChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('价格类型')"
          label-style="left"
          label-width="74px"
        >
          <mt-select
            :disabled="true"
            v-model="formInfo.priceType"
            :data-source="priceTypeList"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('单据状态')"
          label-style="left"
          label-width="74px"
        >
          <mt-select
            :disabled="true"
            v-model="formInfo.evaluateStatus"
            :data-source="statusList"
          ></mt-select>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('扩展')" label-style="left" label-width="74px">
          <mt-multi-select
            :disabled="type != 'edit'"
            v-model="formInfo.other"
            :data-source="sourcingExpandList"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
            @change="sourcingExpandChange"
            width="395"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('创建人')" label-style="left" label-width="74px">
          <mt-input :disabled="true" v-model="formInfo.creater" float-label-type="Never"></mt-input>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('创建日期')"
          label-style="left"
          label-width="74px"
        >
          <mt-input
            :disabled="true"
            v-model="createTime"
            maxlength="200"
            float-label-type="Never"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
import bus from '@/utils/bus'
export default {
  props: {
    detailInfo: {
      type: Object,
      required: true
    }
  },
  computed: {
    editType() {
      return this.$route.query.type
    }
  },
  data() {
    return {
      createTime: null,
      type: 'edit',
      siteList: [],
      companyList: [],
      purOrgList: [],
      purGroupList: [],
      priceTypeList: [{ text: this.$t('新品估价'), value: '0' }],
      sourcingExpandList: [],
      statusList: [
        { text: this.$t('草稿'), value: '0' },
        { text: this.$t('审批中'), value: '1' },
        { text: this.$t('已完成'), value: '2' },
        { text: this.$t('审批拒绝'), value: '3' }
      ],
      formInfo: {
        priceType: '0',
        creater: userInfo.accountName,
        evaluateStatus: '0'
      },
      rules: {
        evaluateTitle: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }],
        companyCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        purOrgCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        siteId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      }
    }
  },
  async mounted() {
    await this.getOrgs()
    await this.getBuyerOrgList()
    console.log('rfxEvaluate---', this.detailInfo)
    if (this.detailInfo?.rfxEvaluate) {
      this.formInfo = { ...this.detailInfo.rfxEvaluate }
      if (
        this.formInfo.evaluateStatus == 0 ||
        this.formInfo.evaluateStatus == 3 ||
        this.formInfo.evaluateStatus == 4
      ) {
        this.type = 'edit'
      } else {
        this.type = 'view'
      }
      this.$nextTick(() => {
        this.formInfo.purOrgCode = this.detailInfo.rfxEvaluate.purOrgCode
        this.formInfo.siteId = this.detailInfo.rfxEvaluate.siteId
        this.formInfo.evaluateStatus = this.detailInfo.rfxEvaluate.evaluateStatus.toString()
        this.formInfo.other = this?.detailInfo?.rfxEvaluate?.other?.split(',')
      })
    }
    if (this?.formInfo?.siteId) {
      sessionStorage.setItem('organizationId', this.formInfo.siteId)
    } else {
      sessionStorage.removeItem('organizationId')
    }
    if (this?.formInfo?.companyId) {
      this.getPurOrg()
      this.getSourcingExpand()
    }
    if (this?.formInfo?.companyId && this?.formInfo?.purOrgId) {
      this.getSite()
    }
    if (this.formInfo?.createTime) {
      this.createTime = this.formatDateTime(new Date(Number(this.formInfo.createTime)))
    } else {
      let date = new Date()
      this.createTime = this.formatDateTime(date)
      this.formInfo.createTime = Number(date)
    }
  },
  methods: {
    getSourcingExpand() {
      this.$API.rfxDetail.purOrgWithSite({ companyId: this.formInfo.companyId }).then((res) => {
        if (res && res.data) {
          let dataSource = []
          res.data.forEach((v) => {
            v.siteOrgs?.forEach((x) => {
              let _key = `${v.businessOrganizationCode}-${v.businessOrganizationName}+${x.orgCode}-${x.orgName}`
              dataSource.push({
                text: _key,
                value: `${v.businessOrganizationCode}+${x.orgCode}`,
                data: {
                  purOrgCode: v.businessOrganizationCode,
                  purOrgId: v.id,
                  purOrgName: v.businessOrganizationName,
                  siteCode: x.orgCode,
                  siteId: x.id,
                  siteName: x.orgName
                }
              })
            })
          })
          this.sourcingExpandList = dataSource
        }
      })
    },
    formatDateTime(inputTime) {
      var date = new Date(inputTime)
      var y = date.getFullYear()
      var m = date.getMonth() + 1
      m = m < 10 ? '0' + m : m
      var d = date.getDate()
      d = d < 10 ? '0' + d : d
      return y + '-' + m + '-' + d
    },
    goBack() {
      // 返回
      this.$router.push({
        name: 'pur-valuation-management'
      })
    },
    //获取公司
    async getOrgs() {
      await this.$API.masterData.permissionCompanyList().then((res) => {
        this.companyList = res.data
      })
    },
    //采购组织
    getPurOrg() {
      this.$API.masterData
        .permissionOrgList({
          orgId: this.formInfo.companyId
        })
        .then((res) => {
          this.purOrgList = res.data
        })
    },
    // 获取主数据-采购组
    getBuyerOrgList() {
      this.$API.masterData
        .getbussinessGroup({
          groupTypeCode: 'BG001CG'
        })
        .then((res) => {
          let list = res.data || []
          list.forEach((item) => {
            item.label = `${item.groupCode}-${item.groupName}`
          })
          this.purGroupList = list
        })
    },
    companyChange(e) {
      if (e.value) {
        this.formInfo.companyId = e.itemData.id
        this.formInfo.companyName = e.itemData.orgName
        this.getPurOrg()
        this.getSourcingExpand()
      } else {
        this.formInfo.companyId = null
        this.formInfo.companyName = null
      }
      this.formInfo.purOrgId = null
      this.formInfo.purOrgName = null
      this.formInfo.purOrgCode = null
      this.formInfo.siteName = null
      this.formInfo.siteId = null
      this.formInfo.siteName = null
      this.formInfo.other = null
    },
    purOrgCodeChange(e) {
      if (e.value) {
        this.formInfo.purOrgId = e.itemData.id
        this.formInfo.purOrgName = e.itemData.organizationName
        this.getSite()
      }
    },
    getSite() {
      this.$API.masterData
        .permissionSiteList({
          buOrgId: this.formInfo.purOrgId,
          companyId: this.formInfo.companyId,
          orgLevelTypeCode: 'ORG06'
        })
        .then((res) => {
          this.siteList = res.data
        })
    },
    siteChange(e) {
      if (e.value) {
        this.formInfo.siteCode = e.itemData.orgCode
        this.formInfo.siteName = e.itemData.orgName
        sessionStorage.setItem('organizationId', e.value)
        bus.$emit('siteCodeChange', e.itemData.orgCode)
        bus.$emit('siteNameChange', e.itemData.orgName)
        this.$parent.$refs.tabRef.siteInfo = e.itemData
      } else {
        this.formInfo.siteCode = null
        this.formInfo.siteName = null
        sessionStorage.removeItem('organizationId')
        bus.$emit('siteCodeChange', null)
        bus.$emit('siteNameChange', null)
        this.$parent.$refs.tabRef.siteInfo = {}
      }
      bus.$emit('itemCodeClear')
    },
    purGroupChange(e) {
      if (e.value) {
        this.formInfo.groupName = e.itemData.groupName
        this.formInfo.groupId = e.itemData.id
      } else {
        this.formInfo.groupName = null
        this.formInfo.groupId = null
      }
    },
    sourcingExpandChange(e) {
      console.log('sourcingExpandChange--', e)
      if (e.value && e.value.length > 0) {
        let _otherName = []
        this.sourcingExpandList.filter((item) => {
          if (e.value.includes(item.value)) {
            _otherName.push(item.text)
          }
        })
        this.formInfo.otherName = _otherName.toString()
      } else {
        this.formInfo.otherName = ''
      }
    },
    //提交立项
    saveProject() {
      this.$emit('saveProject')
    },
    //保存
    editProject() {
      this.$emit('editProject')
    }
  }
}
</script>

<style lang="scss" scoped>
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.top-info {
  height: 22px;

  .btns-wrap {
    float: right;
    /deep/ .mt-button {
      margin-right: 0;
      height: 30px;
      button {
        background: transparent;
        border-radius: 4px;
        box-shadow: unset;
        padding: 6px 12px 4px;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
      }
    }
  }
}

.top-form {
  /deep/ .mt-form {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    .mt-form-item {
      margin: 0 10px 20px 10px;
      width: 23%;
      .mt-form-item-label {
        .label {
          flex: none;
        }
        .mt-input {
          width: 100%;
        }
      }
    }
    justify-content: space-between;
  }
}
</style>
