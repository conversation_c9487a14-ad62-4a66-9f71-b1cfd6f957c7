<template>
  <div class="hall-detail full-height" v-if="isShow">
    <top-info-view
      ref="topInfoRef"
      :detail-info="detailInfo"
      @saveProject="saveProject"
      @editProject="editProject"
    />
    <div class="table">
      <valuationDetail ref="tabRef" :detail-info="detailInfo"></valuationDetail>
    </div>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
export default {
  components: {
    //顶部详情
    topInfoView: () => import('./components/topInfo.vue'),
    valuationDetail: () => import('./components/valuationDetail.vue')
  },
  data() {
    return {
      isShow: false,
      detailInfo: {},
      formObject: {}
    }
  },
  async mounted() {
    if (this.$route.query?.id) {
      await this.getDetail(this.$route.query.id)
    } else {
      this.isShow = true
    }
  },
  methods: {
    async getDetail(id) {
      await this.$API.priceService.queryEvaluateDetail({ id }).then((res) => {
        this.detailInfo = res.data
        this.isShow = true
      })
    },
    //保存
    editProject() {
      this.$refs.tabRef.$refs.valuationDetailRef.getCurrentTabRef().grid.endEdit()
      setTimeout(() => {
        this.save(false)
      }, 500)
    },
    //发布操作
    saveProject() {
      this.$refs.tabRef.$refs.valuationDetailRef.getCurrentTabRef().grid.endEdit()
      setTimeout(() => {
        this.save(true)
      }, 500)
    },
    save(commitFlag) {
      console.log('保存--', this.$refs.topInfoRef, this.$refs.tabRef)
      let detailEntities = cloneDeep(this.$refs.tabRef.pageConfig[0].grid.dataSource)
      let sourcingFiles = cloneDeep(this.$refs.tabRef.pageConfig[1].grid.dataSource)
      let _delValuationRecord = cloneDeep(this.$refs.tabRef.delValuationRecord)
      let _delAttachmentRecord = cloneDeep(this.$refs.tabRef.delAttachmentRecord)
      let _itemCodeLisst = detailEntities.map((e) => e.itemCode)
      if (detailEntities.length > 0) {
        let _flag = true
        let isdulp = false
        detailEntities.forEach((element, index) => {
          if (element?.id) {
            element.flag = 1
          } else {
            element.flag = 0
          }
          if (!element.itemCode) {
            _flag = false
            return
          } else if (_itemCodeLisst.indexOf(element.itemCode) != index) {
            isdulp = true
          }
          element.unit = element.unitName
          element.typeDesc = element.categoryName
        })
        if (!_flag) {
          this.$toast({
            content: this.$t('物料编码不能为空'),
            type: 'warning'
          })
          return
        }
        if (isdulp) {
          this.$toast({
            content: this.$t('物料编码不能重复'),
            type: 'warning'
          })
          return
        }
      }
      detailEntities.push.apply(detailEntities, _delValuationRecord)
      let _sourcingFiles = []
      if (sourcingFiles.length > 0) {
        sourcingFiles.forEach((item) => {
          let fileInfo = JSON.parse(item.drawing)[0]
          fileInfo.sysFileId = fileInfo?.sysFileId || fileInfo.id
          delete fileInfo.id
          let _item = {
            ...item,
            ...fileInfo,
            docId: item?.docId || this.$route.query?.id
          }
          if (_item?.id) {
            _item.flag = 1
          } else {
            _item.flag = 0
          }
          delete _item.drawing
          _sourcingFiles.push(_item)
        })
      }
      _sourcingFiles.push.apply(_sourcingFiles, _delAttachmentRecord)
      this.$refs.topInfoRef.$refs.formInfo.validate((valid) => {
        if (valid) {
          console.log('校验通过---', _sourcingFiles)
          if (this.$route.query.type == 0 && !this.$refs.topInfoRef?.formInfo?.id) {
            this.$API.priceService
              .addEvaluate({
                rfxEvaluate: {
                  ...this.$refs.topInfoRef.formInfo,
                  other: this.$refs?.topInfoRef?.formInfo?.other?.toString()
                },
                detailEntities,
                sourcingFiles: _sourcingFiles,
                submitFlag: commitFlag
              })
              .then((res) => {
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('操作成功'),
                    type: 'success'
                  })
                  if (commitFlag) {
                    this.$router.push({
                      name: `pur-valuation-management`
                    })
                  } else {
                    this.isShow = false
                    let _id = this.$route.query?.id || res.data
                    this.getDetail(_id)
                  }
                }
              })
          } else {
            this.$API.priceService
              .updateEvaluate({
                rfxEvaluate: {
                  ...this.$refs.topInfoRef.formInfo,
                  other: this.$refs?.topInfoRef?.formInfo?.other?.toString()
                },
                detailEntities,
                sourcingFiles: _sourcingFiles,
                submitFlag: commitFlag
              })
              .then((res) => {
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('操作成功'),
                    type: 'success'
                  })
                  if (commitFlag) {
                    this.$router.push({
                      name: `pur-valuation-management`
                    })
                  } else {
                    this.isShow = false
                    let _id = this.$route.query?.id || this.$refs.topInfoRef.formInfo.id
                    this.getDetail(_id)
                  }
                }
              })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.hall-detail {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  .table {
    height: calc(100% - 100px);
  }
}
/deep/ .common-template-page.template-hidden-tabs {
  padding-top: 0px;
}
</style>
