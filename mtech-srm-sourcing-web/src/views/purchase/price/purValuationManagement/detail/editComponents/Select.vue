<template>
  <div>
    <mt-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @open="startOpen"
      @change="selectChange"
      :open-dispatch-change="false"
      :disabled="isDisabled"
      :allow-filtering="true"
      :filtering="serchText"
    ></mt-select>
  </div>
</template>
<script>
export default {
  data() {
    return {
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'label', value: 'value' },
      dataSource: [],
      itemCode: '',
      itemId: '',
      costCenterCode: '',
      isDisabled: false,
      siteName: ''
    }
  },
  async mounted() {
    if (this.data.column.field === 'taxName') {
      console.log('this.data.taxInclusiveName', this.data)
      this.getTaxItemList()
      this.fields = { text: 'taxItemName', value: 'taxItemName' }
    }
    if (this.data.column.field === 'currencyName') {
      this.getCurrencyList()
      this.fields = { text: 'currencyName', value: 'currencyName' }
    }
  },
  methods: {
    getTaxItemList() {
      this.$API.masterData.queryAllTaxItem().then((res) => {
        this.dataSource = res.data
        if (!this.data?.taxCode) {
          let _record = res.data.find((e) => e.taxItemCode == 'J2')
          if (_record) {
            this.data.taxCode = _record.taxItemCode
            this.data.taxName = _record.taxItemName
            this.$parent.$emit('selectedChanged', {
              fieldCode: 'taxName',
              itemInfo: {
                taxCode: _record.taxItemCode
              }
            })
          }
        }
      })
    },
    getCurrencyList() {
      this.$API.masterData.queryAllCurrency().then((res) => {
        this.dataSource = res.data
        if (!this.data?.curreny) {
          let _record = res.data.find((e) => e.currencyCode == 'CNY')
          if (_record) {
            this.data.curreny = _record.currencyCode
            this.data.currencyName = _record.currencyName
            this.$parent.$emit('selectedChanged', {
              fieldCode: 'currencyName',
              itemInfo: {
                curreny: _record.currencyCode
              }
            })
          }
        }
      })
    },
    serchText(val) {
      console.log('搜索值', val, this.data.column.field)
      val.updateData(this.dataSource.filter((e) => e[this.fields.value].includes(val.text)))
    },
    startOpen() {},
    selectChange(val) {
      console.log(val.itemData, '下拉数据的信息')
      console.log('val', val, this.data)
      if (this.data.column.field === 'taxName') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'taxName',
          itemInfo: {
            taxCode: val.itemData.taxItemCode
          }
        })
      }
      if (this.data.column.field === 'currencyName') {
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'currencyName',
          itemInfo: {
            curreny: val.itemData.currencyCode
          }
        })
      }
    }
  }
}
</script>
