import { i18n } from '@/main.js'
import Vue from 'vue'
import cellChanged from 'COMPONENTS/NormalEdit/cellChanged' // 单元格被改变（纯展示）
import cellUpload from '../editComponents/cellUpload' // 单元格上传
import Select from '../editComponents/Select.vue'
import selectedItemCode from '@/components/NormalEdit/selectItemCode' // 物料
import cellFileView from '../editComponents/cellFileView' // 单元格附件查看
export const detailColumn = [
  {
    field: 'siteCode',
    headerText: i18n.t('工厂编码'),
    allowEditing: false,
    editTemplate: () => {
      return {
        template: cellChanged
      }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂名称'),
    allowEditing: false,
    editTemplate: () => {
      return {
        template: cellChanged
      }
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('物料编码')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: () => {
      return {
        template: selectedItemCode
      }
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料描述'),
    allowEditing: false,
    editTemplate: () => {
      return {
        template: cellChanged
      }
    }
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类描述'),
    allowEditing: false,
    editTemplate: () => {
      return {
        template: cellChanged
      }
    }
  },
  {
    field: 'unitName',
    headerText: i18n.t('单位'),
    allowEditing: false,
    editTemplate: () => {
      return {
        template: cellChanged
      }
    }
  },
  {
    field: 'price',
    headerText: i18n.t('估价（未税）'),
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('估价（未税）')}}</span>
              </div>
            `
        })
      }
    },
    editType: 'numericedit',
    validationRules: {
      required: true
    },
    edit: {
      params: {
        min: 0,
        max: 999999999999,
        decimals: 2,
        validateDecimalOnType: true,
        showSpinButton: false
      }
    }
  },
  {
    width: '200',
    field: 'currencyName',
    headerText: i18n.t('币种'),
    editTemplate: () => {
      return {
        template: Select
      }
    }
  },
  {
    field: 'taxName',
    headerText: i18n.t('税码'),
    editTemplate: () => {
      return {
        template: Select
      }
    }
  },
  {
    field: 'priceUnit',
    headerText: i18n.t('价格单位'),
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('价格单位')}}</span>
              </div>
            `
        })
      }
    },
    editType: 'numericedit',
    validationRules: {
      required: true
    },
    edit: {
      params: {
        min: 0,
        max: 999999999999,
        decimals: 0,
        format: '###',
        validateDecimalOnType: true,
        showSpinButton: false
      }
    }
  },
  {
    field: 'startTime',
    headerText: i18n.t('生效日期'),
    allowEditing: false
  },
  {
    field: 'endTime',
    headerText: i18n.t('失效日期'),
    allowEditing: false
  },
  {
    field: 'remark',
    headerText: i18n.t('备注（类似P/N）')
  }
]
export const attachColumn = [
  {
    field: 'drawing',
    headerText: i18n.t('文件名称'),
    template: function () {
      return {
        template: cellFileView
      }
    },
    editTemplate: () => {
      return {
        template: cellUpload
      }
    }
  },
  {
    field: 'roundNo',
    headerText: i18n.t('文件描述'),
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('文件描述')}}</span>
              </div>
            `
        })
      }
    },
    validationRules: {
      required: true,
      maxLength: [25, i18n.t('只允许输入1-25个字母和数字')]
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    allowEditing: false,
    editTemplate: () => {
      return {
        template: cellChanged
      }
    }
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    allowEditing: false,
    editTemplate: () => {
      return {
        template: cellChanged
      }
    }
  },
  {
    field: 'fileDetailInfo',
    headerText: i18n.t('备注')
  }
]
