import { i18n } from '@/main.js'

export const columnData = [
  {
    type: 'checkbox',
    width: '50'
  },
  {
    field: 'evaluateCode',
    headerText: i18n.t('估价单号'),
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data.evaluateStatus != 1 && data.evaluateStatus != 2
        }
      },
      {
        id: 'delete',
        icon: 'icon_list_delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data.evaluateStatus != 1 && data.evaluateStatus != 2
        }
      }
    ]
  },
  {
    field: 'evaluateTitle',
    headerText: i18n.t('估价单标题')
  },
  {
    field: 'evaluateStatus',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('草稿'),
        1: i18n.t('审批中'),
        2: i18n.t('已完成'),
        3: i18n.t('审批拒绝'),
        4: i18n.t('审批废弃')
      }
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司')
  },
  {
    field: 'creater',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    field: 'submitTime',
    headerText: i18n.t('提交时间')
  }
]
