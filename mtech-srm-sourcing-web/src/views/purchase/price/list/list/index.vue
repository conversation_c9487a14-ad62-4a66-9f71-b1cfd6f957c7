<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'

export default {
  data() {
    return {
      pageConfig: pageConfig(this.$API.priceService.getPriceRecords)
    }
  },
  mounted() {},
  methods: {
    viewDetail(row) {
      this.$API.seriesItem
        .itemRangeQuery({ itemCode: row.itemCode, itemId: row.itemId })
        .then((res) => {
          this.$dialog({
            modal: () => import('./components/applyItemRanageDialog/index.vue'),
            data: {
              title: this.$t('适用物料范围'),
              suitItemDTOList: res.data.suitItemDTOList,
              seriesItemCode: row.itemCode,
              seriesItemId: row.itemId,
              seriesItemName: row.itemName,
              organizationId: row.siteList[0].siteId,
              siteCode: row.siteList[0].siteCode
            },
            success: () => {
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        })
    },
    handleClickCellTitle(e) {
      if (e.field == 'detail') {
        this.viewDetail(e.data)
      }
    }
  }
}
</script>
