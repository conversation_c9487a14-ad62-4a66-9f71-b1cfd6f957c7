import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'itemDescription',
    headerText: i18n.t('规格型号'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'oldItemCode',
    headerText: i18n.t('旧物料编号'),
    cssClass: 'click'
  },
  {
    width: '150',
    field: 'manufacturerName',
    headerText: i18n.t('制造商'),
    cssClass: 'click'
  }
]
