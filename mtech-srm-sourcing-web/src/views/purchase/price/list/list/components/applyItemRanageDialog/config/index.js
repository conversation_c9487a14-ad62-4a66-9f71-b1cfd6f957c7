import { i18n } from '@/main.js'

export const pageConfig = () => [
  {
    toolbar: {
      useBaseConfig: false,
      tools: [
        [
          { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
          { id: 'Del', icon: 'icon_solid_Delete', title: i18n.t('删除') },
          {
            id: 'upload',
            icon: 'icon_solid_Createproject',
            title: i18n.t('导入')
          }
        ],
        []
      ]
    },
    isUseCustomSearch: true,
    // isCustomSearchRules: true,
    isCustomSearchHandle: true,
    gridId: '8f3e0526-216e-410c-a6ef-537f43951827',
    grid: {
      allowPaging: false, // 不分页
      editSettings: {
        allowEditing: true,
        allowAdding: true,
        allowDeleting: true,
        mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
        showConfirmDialog: false,
        showDeleteConfirmDialog: false,
        newRowPosition: 'Bottom'
      },
      dataSource: []
    }
  }
]
