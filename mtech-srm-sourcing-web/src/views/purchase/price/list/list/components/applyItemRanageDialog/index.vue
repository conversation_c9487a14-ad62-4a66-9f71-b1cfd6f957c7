<template>
  <mt-dialog ref="dialog" :header="header" :buttons="buttons">
    <div class="dialog-content mt-flex-direction-column">
      <div class="grid-wrap">
        <mt-local-template-page
          ref="templateRef"
          :template-config="pageConfig"
          @handleClickToolBar="handleClickToolBar"
          @handleCustomReset="handleCustomReset"
          @handleCustomSearch="handleCustomSearch"
        >
          <template v-slot:quick-search-form>
            <div class="custom-form-box">
              <mt-form ref="searchFormRef" :model="searchFormModel">
                <mt-form-item prop="suitItemCode" :label="$t('物料编码')" label-style="left">
                  <mt-input v-model="searchFormModel.suitItemCode"></mt-input>
                </mt-form-item>
                <mt-form-item prop="suitItemName" :label="$t('物料名称')" label-style="left">
                  <mt-input v-model="searchFormModel.suitItemName"></mt-input>
                </mt-form-item>
              </mt-form>
            </div>
          </template>
        </mt-local-template-page>
      </div>
    </div>
    <upload-excel-dialog
      ref="uploadInitBalanceRef"
      :down-template-name="downTemplateName"
      :request-urls="requestUrls"
      :down-template-params="downTemplateParams"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </mt-dialog>
</template>

<script>
import { pageConfig } from './config'
import { utils } from '@mtech-common/utils'
import { createEditInstance } from '@/utils/ej/dataGrid/index'
import MtLocalTemplatePage from '@/components/template-page'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {
        suitItemCode: '',
        suitItemName: ''
      },
      pageConfig: pageConfig(),
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      downTemplateName: this.$t('物料范围模板'),
      requestUrls: {},
      downTemplateParams: {
        page: {
          current: 1,
          size: 10
        },
        rules: []
      }
    }
  },
  components: {
    MtLocalTemplatePage,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.setColumnData()
  },
  created() {
    this.$set(this.pageConfig[0].grid, 'dataSource', this.modalData.suitItemDTOList)
  },
  methods: {
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    // 查询
    handleCustomSearch() {
      this.$API.seriesItem
        .itemRangeQuery({
          itemCode: this.modalData.seriesItemCode,
          itemId: this.modalData.seriesItemId,
          ...this.searchFormModel
        })
        .then((res) => {
          if (res.code === 200) {
            this.$set(this.pageConfig[0].grid, 'dataSource', res.data.suitItemDTOList)
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
    },
    // 上传（显示弹窗）
    handleUpload() {
      this.requestUrls = {
        templateUrlPre: 'seriesItem',
        templateUrl: 'downloadImportTemplate',
        uploadUrl: 'importItem',
        itemCode: this.modalData.seriesItemCode
      }
      this.showUploadExcel(true)
    },
    // 展示/不展示 上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadInitBalanceRef.uploadData = [] // 清空数据
        this.$refs.uploadInitBalanceRef.$refs.uploader.files = []
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    upExcelConfirm(data) {
      let msg = ''
      data.forEach((x) => {
        if (x.errorMsg && !msg) {
          msg = x.errorMsg
        }
        // if (
        //   this.$refs.templateRef.getCurrentTabRef().grid.dataSource.findIndex((e) => {
        //     return e.suitItemCode === x.itemCode
        //   }) != -1
        // ) {
        //   isHave = true
        //   indexHave = i + 1
        // }
      })
      if (msg) {
        this.$toast({ content: this.$t(msg), type: 'warning' })
        return
      }
      // if (isHave) {
      //   this.$toast({
      //     content: this.$t(`导入数据第${indexHave}行,已存在相同物料`),
      //     type: 'warning'
      //   })
      //   return
      // }

      this.showUploadExcel(false)
      // for (let item of data) {
      //   dataSource.push({
      //     tempId: item.id,
      //     categoryCode: item.categoryCode,
      //     categoryId: item.categoryId,
      //     categoryName: item.categoryName,
      //     formulaCode: item.formulaCode,
      //     formulaContent: item.formulaContent,
      //     // id: item.id,
      //     suitItemCode: item.seriesItemCode,
      //     suitItemId: item.suitItemId,
      //     suitItemName: item.suitItemName,
      //     suitItemSpec: item.suitItemSpec
      //     // isNew: true,
      //   })
      // }
      this.$set(this.pageConfig[0].grid, 'dataSource', data)
    },
    setColumnData() {
      let _this = this
      const listColumnData = () => {
        const editInstance = createEditInstance().onChange(
          (ctx, { field, rowData, value }, event) => {
            rowData.formulaCode = event.itemData.formulaCode
            rowData.formulaName = event.itemData.formulaName
            let dataSource =
              this.$refs.templateRef.getCurrentTabRef().grid.$options.propsData.dataSource
            dataSource[
              dataSource.findIndex((e) => {
                if (e.tempId) {
                  return e.tempId == rowData.tempId
                } else {
                  return e.id == rowData.id
                }
              })
            ] = rowData
            console.log('onInput', { ctx, rowData, value, field, event })
          }
        )
        return [
          {
            width: '50',
            type: 'checkbox',
            allowEditing: false
          },
          {
            width: '150',
            field: 'suitItemCode',
            headerText: this.$t('物料编码'),
            allowEditing: false
          },
          {
            width: '150',
            field: 'suitItemName',
            headerText: this.$t('物料名称'),
            allowEditing: false
          },
          {
            width: '150',
            field: 'suitItemSpec',
            headerText: this.$t('物料规格'),
            allowEditing: false
          },
          {
            field: 'formulaContent',
            headerText: this.$t('计算公式'),
            width: '300',
            edit: editInstance.create({
              getEditConfig: ({ rowData }) => ({
                type: 'mt-select',
                fields: { value: 'formula', text: 'formulaName' },
                'show-clear-button': true,
                created: function () {
                  console.error(rowData, 'rowData123')
                  _this.$API.seriesItem
                    .formulaQuery({
                      // categoryCode: "",
                      categoryId: rowData.categoryId
                    })
                    .then((res) => {
                      if (res.data) {
                        editInstance.setOptions('formulaContent', {
                          dataSource: res.data
                        })
                      } else {
                        editInstance.setOptions('formulaContent', {
                          dataSource: []
                        })
                      }
                    })
                },
                dataSource: []
              })
            })
          }
        ]
      }
      this.$set(this.pageConfig[0].grid, 'columnData', listColumnData())
    },
    handleClickToolBar(e) {
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'Del') {
        let _selectGridRecords = e.gridRef.getMtechGridRecords()
        if (_selectGridRecords.length < 1) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        this.handleDel(_selectGridRecords)
      } else if (e.toolbar.id == 'upload') {
        this.handleUpload()
      }
    },
    handleDel(_selectGridRecords) {
      let selectGridRecords = utils.cloneDeep(_selectGridRecords)
      for (let item of selectGridRecords) {
        let index = this.$refs.templateRef.getCurrentTabRef().grid.dataSource.findIndex((e) => {
          return e.id ? e.id === item.id : e.tempId === item.tempId
        })
        this.$refs.templateRef.getCurrentTabRef().grid.dataSource.splice(index, 1)

        this.$refs.templateRef.getCurrentTabRef().gridRef.selectIdRecords.splice(
          this.$refs.templateRef.getCurrentTabRef().gridRef.selectIdRecords.findIndex((e) => {
            return e.id ? e.id === item.id : e.tempId === item.tempId
          }),
          1
        )
      }
      this.$refs.templateRef.getCurrentTabRef().grid.refresh()
    },
    handleAdd() {
      this.$dialog({
        modal: () => import('./components/selectItemDialog/index.vue'),
        data: {
          title: this.$t('选择物料'),
          siteParam: {
            organizationId: this.modalData.organizationId,
            siteCode: this.modalData.siteCode
          }
        },
        success: (data) => {
          console.log('data123', data)
          let existItemName = []
          for (let item of data) {
            if (
              this.$refs.templateRef.getCurrentTabRef().grid.dataSource.findIndex((e) => {
                return e.suitItemId === item.id
              }) != -1
            ) {
              existItemName.push(item.itemName)
            } else {
              this.$refs.templateRef.getCurrentTabRef().grid.dataSource.unshift({
                tempId: item.id,
                categoryCode: item.categoryResponse?.categoryCode,
                categoryId: item.categoryResponse?.id,
                categoryName: item.categoryResponse?.categoryName,
                formulaCode: '',
                formulaName: '',
                formulaContent: '',
                // id: item.id,
                suitItemCode: item.itemCode,
                suitItemId: item.id,
                suitItemName: item.itemName,
                suitItemSpec: item.itemDescription
                // isNew: true,
              })
            }
          }
          this.$refs.templateRef.getCurrentTabRef().grid.refresh()
          if (existItemName.length > 0) {
            this.$toast({
              content: this.$t('物料:' + existItemName.join(',') + '已存在,不能重复添加'),
              type: 'warning'
            })
          }
        }
      })
    },
    confirm() {
      let dataSource = utils.cloneDeep(
        this.$refs.templateRef.getCurrentTabRef().grid.$options.propsData.dataSource
      )
      let str = []
      dataSource.forEach((item, index) => {
        if (!item.formulaContent) {
          str.push('第' + (index + 1) + '行')
        }
      })
      if (str.length != 0) {
        this.$toast({
          content: '请选择' + str.join(',') + '的计算公式',
          type: 'warning'
        })
        return
      }
      // for (let item of dataSource) {
      //   if (item.isNew) {
      //     item.id = undefined;
      //   }
      // }
      let param = {
        seriesItemCode: this.modalData.seriesItemCode,
        seriesItemId: this.modalData.seriesItemId,
        seriesItemName: this.modalData.seriesItemName,
        suitItemDTOList: dataSource
      }
      this.$API.seriesItem.itemRangeSave(param).then((res) => {
        this.$toast({
          content: res.msg,
          type: 'success'
        })
        this.$emit('confirm-function')
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  height: 100%;
  .grid-wrap {
    flex: 1;
  }
}
</style>
