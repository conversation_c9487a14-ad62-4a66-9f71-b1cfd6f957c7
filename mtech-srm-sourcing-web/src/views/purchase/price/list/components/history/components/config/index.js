import { i18n } from '@/main.js'
export const columnData = [
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    width: '200'
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    width: '200'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('草稿'),
        1: i18n.t('启用'),
        2: i18n.t('禁用')
      }
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    width: '200'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: '200'
  },
  {
    field: 'siteList',
    headerText: i18n.t('采购组织'),
    width: '200',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['purOrgName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'quoteAttribute',
    headerText: i18n.t('报价属性'),
    width: '200',
    valueConverter: {
      type: 'map',
      map: {
        mailing_price: i18n.t('寄售价'),
        standard_price: i18n.t('标准价'),
        outsource: i18n.t('委外价')
      }
    }
  },
  {
    field: 'quoteMode',
    headerText: i18n.t('价格生效方式'),
    width: '200',
    valueConverter: {
      type: 'map',
      map: {
        in_warehouse: i18n.t('按照入库'),
        out_warehouse: i18n.t('按出库'),
        order_date: i18n.t('按订单日期')
      }
    }
  },
  {
    field: 'deliveryPlace',
    headerText: i18n.t('直送地')
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种')
  },
  {
    field: 'sourceCode',
    headerText: i18n.t('来源询价单号'),
    width: '200'
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('当前价格')
  },
  {
    field: 'priceUnit',
    headerText: i18n.t('价格单位')
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('生效日期'),
    width: '200'
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('失效日期'),
    width: '200'
  },
  {
    field: 'minPurchaseQuantity',
    headerText: i18n.t('最小采购量')
  },

  {
    field: 'leadTime',
    headerText: 'L/T'
  },
  {
    field: 'unconditionalLeadTime',
    headerText: i18n.t('无条件L/T')
  },
  {
    field: 'taxRate',
    headerText: i18n.t('税率')
  },
  {
    field: 'paymentCondition',
    headerText: i18n.t('付款条件')
  }
]
