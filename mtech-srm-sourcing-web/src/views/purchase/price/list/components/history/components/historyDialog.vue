<template>
  <mt-dialog ref="dialog" :close="cancel" :buttons="button" :header="header">
    <mt-template-page ref="templateRef" :hidden-tabs="true" :template-config="pageConfig" />
  </mt-dialog>
</template>
<script>
import { columnData } from './config'
export default {
  data() {
    return {
      button: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          toolbar: { useBaseConfig: false, tools: [[], []] },
          grid: {
            allowFiltering: true,
            columnData,
            asyncConfig: {
              url: this.$API.priceService.getHistoryRecords,
              //  this.getParamByPriceType()[this.modalData.data.priceType],
              params: this.getParamByPriceType()
            }
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    cancel() {
      this.$emit('confirm-function')
    },
    getParamByPriceType() {
      const {
        priceType,
        bizCode,
        categoryCode,
        categoryId,
        itemCode,
        itemId,
        logisticsEndPlace,
        logisticsStartPlace,
        logisticsType,
        marketFactorCode,
        marketFactorId,
        skuCode,
        skuId,
        supplierCode,
        supplierId
      } = this.modalData.data
      return {
        priceType,
        bizCode,
        categoryCode,
        categoryId,
        itemCode,
        itemId,
        logisticsEndPlace,
        logisticsStartPlace,
        logisticsType,
        marketFactorCode,
        marketFactorId,
        skuCode,
        skuId,
        supplierCode,
        supplierId
      }
      //  [
      //   { itemCode, itemId, priceType },
      //   { skuCode, skuId, priceType },
      //   { categoryCode, categoryId, priceType },
      //   { supplierCode, supplierId, priceType },
      //   { bizCode },
      //   { marketFactorCode, marketFactorId, priceType },
      //   { logisticsEndPlace, logisticsStartPlace, logisticsType, priceType },
      // ];
    }
  },
  created() {}
}
</script>
<style lang="scss" scoped></style>
