<template>
  <span class="priew" @click="openDialog">{{ $t('查看') }}</span>
</template>
<script>
export default {
  data() {
    return {}
  },
  methods: {
    openDialog() {
      this.$dialog({
        modal: () => import('./components/historyDialog'),
        data: {
          title: this.$t('历史记录'),
          data: this.data,
          parentVm: this
        },
        success: () => {},
        close: () => {}
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.priew {
  color: #2783fe;
  cursor: pointer;
}
</style>
