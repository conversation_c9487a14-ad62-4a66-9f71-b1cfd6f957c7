<template>
  <mt-dialog
    ref="dialog"
    class="aaa"
    css-class="price-details-dialog-container"
    :buttons="buttons"
    height="100%"
    :enable-resize="false"
    :position="{ X: 'right', Y: 'top' }"
    :header="modalData.title"
    @beforeClose="cancel"
  >
    <mt-form>
      <div class="flex">
        <mt-form-item :label="$t('价格条款')" style="width: 48%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.priceRecordCode"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('价格类型')" style="width: 48%">
          <mt-input
            float-label-type="Never"
            disabled
            :value="priceTypeMap[modalData.data.priceType]"
          ></mt-input>
        </mt-form-item>
      </div>

      <mt-form-item :label="$t('公司名称')">
        <mt-input
          float-label-type="Never"
          disabled
          :value="modalData.data.companyList.map((item) => item.companyName).join('、')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item :label="$t('工厂/地点')" style="100%">
        <mt-input
          float-label-type="Never"
          :multiline="true"
          :rows="3"
          disabled
          :value="
            modalData.data.siteList.map((item) => `${item.siteName}/${item.purOrgName}`).join('、')
          "
        ></mt-input>
      </mt-form-item>
    </mt-form>
    <h3 style="font-weight: bolder; padding: 16px 0">
      {{ $t('物料/品类信息：') }}
    </h3>
    <mt-form>
      <div class="flex">
        <mt-form-item :label="$t('品类')" style="width: 48%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.categoryName"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('物料名称')" style="width: 48%">
          <mt-input float-label-type="Never" disabled :value="modalData.data.itemName"></mt-input>
        </mt-form-item>
      </div>
      <div class="flex">
        <mt-form-item :label="$t('物料编码')" style="width: 48%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.itemCode"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('适用税率')" style="width: 48%">
          <mt-input float-label-type="Never" disabled :value="modalData.data.taxName"></mt-input>
        </mt-form-item>
      </div>
      <div class="flex">
        <mt-form-item :label="$t('模具类型')" style="width: 48%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="dieValue[modalData.data.dieType]"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('模具编码')" style="width: 48%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.dieCode"
          ></mt-input>
        </mt-form-item>
      </div>
      <div class="flex">
        <mt-form-item :label="$t('父级物料编码')" style="width: 48%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.parentItemCode"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('父级物料名称')" style="width: 48%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.parentItemName"
          ></mt-input>
        </mt-form-item>
      </div>
      <div class="flex">
        <mt-form-item :label="$t('单位')" style="width: 48%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.unitName"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('币种')" style="width: 48%">
          <mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.currencyName"
          ></mt-input>
        </mt-form-item>
      </div>
      <div class="flex">
        <mt-form-item label="SKU" style="width: 100%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.skuName"
          ></mt-input>
        </mt-form-item>
      </div>
    </mt-form>
    <h3 style="font-weight: bolder; padding: 16px 0">
      {{ $t('供应商选择：') }}
    </h3>
    <mt-form>
      <div class="flex">
        <mt-form-item :label="$t('供应商名称')" style="width: 48%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.supplierName"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('供应商编码')" style="width: 48%">
          <mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.supplierCode"
          ></mt-input>
        </mt-form-item>
      </div>
      <!-- <div class="flex">
        <mt-form-item :label="$t('归属合同')" style="width: 48%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.contractName"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('合同协议编号')" style="width: 48%">
          <mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.contractCode"
          ></mt-input>
        </mt-form-item>
      </div> -->
      <div class="flex">
        <mt-form-item :label="$t('有效起始')" style="width: 48%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.validStartTime"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('有效截至')" style="width: 48%">
          <mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.validEndTime"
          ></mt-input>
        </mt-form-item>
      </div>
      <div class="flex">
        <mt-form-item :label="$t('价格类型')" style="width: 48%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.priceValueName"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('报价属性')" style="width: 48%">
          <mt-input
            float-label-type="Never"
            disabled
            :value="quoteAttributeValue[modalData.data.quoteAttribute]"
          ></mt-input>
        </mt-form-item>
      </div>
      <div class="flex">
        <mt-form-item :label="$t('价格生效方式')" style="width: 48%">
          <mt-input
            float-label-type="Never"
            disabled
            :value="quoteModeValue[modalData.data.quoteMode]"
          ></mt-input>
        </mt-form-item>
      </div>
      <div class="flex">
        <mt-form-item :label="$t('阶梯类型')" style="width: 100%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="differentialPricingTypeMap[modalData.data.stageType]"
          ></mt-input>
        </mt-form-item>
      </div>

      <div class="flex" v-if="modalData.data.stageType === -1">
        <mt-form-item :label="$t('含税价格')" style="width: 48%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.taxedUnitPrice"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('未税价格')" style="width: 48%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.untaxedUnitPrice"
          ></mt-input>
        </mt-form-item>
      </div>
      <template v-if="modalData.data.stageType === -1 && modalData.data.priceType === 0">
        <div class="flex">
          <mt-form-item :label="$t('规划量')" style="width: 48%">
            <mt-input
              mt-input
              float-label-type="Never"
              disabled
              :value="modalData.data.planQuantity"
            ></mt-input>
          </mt-form-item>
          <mt-form-item :label="$t('实际分摊量')" style="width: 48%">
            <mt-input
              mt-input
              float-label-type="Never"
              disabled
              :value="modalData.data.shareQuantity"
            ></mt-input>
          </mt-form-item>
        </div>
        <div class="flex">
          <mt-form-item :label="$t('规划分摊价（含税）')" style="width: 48%">
            <mt-input
              mt-input
              float-label-type="Never"
              disabled
              :value="modalData.data.planSharePriceTaxed"
            ></mt-input>
          </mt-form-item>
          <mt-form-item :label="$t('规划分摊价（未税）')" style="width: 48%">
            <mt-input
              mt-input
              float-label-type="Never"
              disabled
              :value="modalData.data.planSharePriceUntaxed"
            ></mt-input>
          </mt-form-item>
        </div>
        <div class="flex">
          <mt-form-item :label="$t('实际分摊价（含税）')" style="width: 48%">
            <mt-input
              mt-input
              float-label-type="Never"
              disabled
              :value="modalData.data.realSharePriceTaxed"
            ></mt-input>
          </mt-form-item>
          <mt-form-item :label="$t('实际分摊价（未税）')" style="width: 48%">
            <mt-input
              mt-input
              float-label-type="Never"
              disabled
              :value="modalData.data.realSharePriceUntaxed"
            ></mt-input>
          </mt-form-item>
        </div>
        <div class="flex">
          <mt-form-item :label="$t('分摊后单价（含税）')" style="width: 48%">
            <mt-input
              mt-input
              float-label-type="Never"
              disabled
              :value="modalData.data.sharePriceTaxed"
            ></mt-input>
          </mt-form-item>
          <mt-form-item :label="$t('分摊后单价（未税）')" style="width: 48%">
            <mt-input
              mt-input
              float-label-type="Never"
              disabled
              :value="modalData.data.sharePriceUntaxed"
            ></mt-input>
          </mt-form-item>
        </div>
      </template>
    </mt-form>
    <h3 style="font-weight: bolder; padding: 16px 0">{{ $t('其他信息：') }}</h3>
    <mt-form>
      <div class="flex">
        <mt-form-item :label="$t('价格单位')" style="width: 48%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.priceUnit"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('最小包装数量')" style="width: 48%">
          <mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.minPackageQuantity"
          ></mt-input>
        </mt-form-item>
      </div>
      <div class="flex">
        <mt-form-item :label="$t('最小采购量')" style="width: 48%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.minPurchaseQuantity"
          ></mt-input>
        </mt-form-item>
        <mt-form-item label="L/T" style="width: 48%">
          <mt-input float-label-type="Never" disabled :value="modalData.data.leadTime"></mt-input>
        </mt-form-item>
      </div>
      <div class="flex">
        <mt-form-item :label="$t('无条件L/T')" style="width: 48%">
          <mt-input
            mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.unconditionalLeadTime"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('直送地')" style="width: 48%">
          <mt-input
            float-label-type="Never"
            disabled
            :value="modalData.data.deliveryPlace"
          ></mt-input>
        </mt-form-item>
      </div>
    </mt-form>
    <h3 style="font-weight: bolder; padding: 16px 0">{{ $t('阶梯维护：') }}</h3>
    <mt-template-page
      :hidden-tabs="true"
      style="height: fit-content"
      :template-config="differentialPricingConfig"
    />
  </mt-dialog>
</template>

<script>
import Vue from 'vue'
import { PriceTypeMap } from '@/constants'
export default {
  name: 'PriceDetailsDialog',
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('确定') }
        }
      ],
      priceTypeMap: PriceTypeMap,
      differentialPricingTypeMap: {
        '-1': this.$t('无阶梯'),
        0: this.$t('数量累计'),
        1: this.$t('时间'),
        2: this.$t('金额')
      },
      differentialPricingConfig: [
        {
          grid: {
            allowFiltering: true,
            allowPaging: false,
            columnData: [
              {
                field: 'stageFrom&stageTo',
                headerText: this.$t('数量'),
                template: function () {
                  return {
                    template: Vue.component('stage', {
                      template: `<span>{{data.stageFrom}}-{{data.stageTo}}</span>`,
                      data() {
                        return { data: {} }
                      }
                    })
                  }
                }
              },
              {
                field: 'taxedUnitPrice',
                headerText: this.$t('含税价')
              },
              {
                field: 'untaxedUnitPrice',
                headerText: this.$t('未税价')
              },
              {
                field: 'discountRate',
                headerText: this.$t('折扣')
              }
            ],
            dataSource: [{}]
          }
        }
      ],
      quoteAttributeValue: {
        mailing_price: this.$t('寄售价'),
        standard_price: this.$t('标准价')
      },
      quoteModeValue: {
        in_warehouse: this.$t('按照入库'),
        out_warehouse: this.$t('按出库'),
        order_date: this.$t('按订单日期')
      },
      dieValue: {
        1: this.$t('基础模具'),
        2: this.$t('复制模具'),
        3: this.$t('基础模改模'),
        4: this.$t('复制模改模')
      }
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    console.log(this.modalData.data)
    this.differentialPricingConfig[0].grid.dataSource = this.modalData.data.stageList
  },
  methods: {
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.price-details-dialog-container {
  &.e-dialog .e-dlg-content {
    padding: 40px;
  }

  .flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
