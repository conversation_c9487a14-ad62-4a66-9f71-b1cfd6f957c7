<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog right-wrapper"
    :enable-resize="false"
    :position="{ X: 'right', Y: 'top' }"
    height="100%"
    :close="cancel"
    :buttons="button"
    :header="header"
  >
    <div class="dialog-content" style="height: 100%">
      <div class="dialog-panel price-type-panel" style="height: 100%">
        <mt-template-page ref="templateRef" :hidden-tabs="true" :template-config="pageConfig" />
      </div>
    </div>
  </mt-dialog>
</template>
<script>
import { columnData } from '../config/index'
export default {
  data() {
    return {
      button: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          toolbar: [],
          gridId: this.$permission.gridId['purchase']['price']['child_material'],
          grid: {
            allowFiltering: true,
            columnData,
            asyncConfig: {}
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    Object.assign(
      this.pageConfig[0].grid.asyncConfig,
      {},
      {
        defaultRules: [
          {
            label: this.$t('父级物料编码'),
            field: 'parentCode',
            type: 'string',
            operator: 'contains',
            value: this.modalData.data.priceRecordCode
          }
        ],
        url: this.$API.priceService.getPriceRecords
      }
    )
  },
  methods: {
    cancel() {
      this.$emit('confirm-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.e-dlg-container > .right-wrapper {
  height: 100% !important;
  .dialog-panel {
    .panel-title {
      font-size: 16px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(41, 41, 41, 1);
      margin-bottom: 18px;
      &.price-title {
        margin-bottom: 0;
      }
    }
    .full-width {
      width: 100% !important;
    }
  }
}
</style>
