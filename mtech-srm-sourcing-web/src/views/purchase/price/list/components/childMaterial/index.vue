<template>
  <span class="priew" @click="openDialog" v-if="data.sonRelList">{{ $t('详情') }}</span>
  <span v-else>-</span>
</template>
<script>
export default {
  data() {
    return {}
  },
  methods: {
    openDialog() {
      this.$dialog({
        modal: () => import('./components/childMaterialDialog'),
        data: {
          title: this.$t('子物料'),
          data: this.data,
          parentVm: this
        },
        success: () => {},
        close: () => {}
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.priew {
  color: #00469c;
  cursor: pointer;
}
</style>
