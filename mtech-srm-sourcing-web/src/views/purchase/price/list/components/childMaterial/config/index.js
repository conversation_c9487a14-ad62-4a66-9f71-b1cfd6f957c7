import { i18n } from '@/main.js'
export const columnData = [
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    width: '200'
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    width: '200'
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种'),
    width: '200'
  },
  {
    field: 'taxRate',
    headerText: i18n.t('税率'),
    width: '200'
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('未税单价'),
    width: '200'
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('含税单价'),
    width: '200'
  }
]
