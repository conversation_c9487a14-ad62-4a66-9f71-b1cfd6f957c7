<template>
  <div>
    <span @click="open" class="open">{{ $t('集中箱费用') }}</span>
  </div>
</template>
<script>
export default {
  data() {
    return {}
  },
  methods: {
    open() {
      console.log(this.data)
      let type = 1
      if (this.data.logisticsType == this.$t('海运')) {
        type = 1
      } else {
        type = 2
      }
      this.$dialog({
        modal: () => import('./index.vue'),
        data: {
          title: this.$t('集中箱费用'),
          data: {
            type,
            data: this.data.logisticsDto.specification
          }
        },
        success: () => {}
      })
    }
  }
}
</script>
<style scoped>
.open {
  cursor: pointer;
  color: rgb(0, 70, 156);
}
</style>
