<template>
  <mt-dialog
    ref="dialog"
    :enable-resize="false"
    :position="{ X: 'right', Y: 'top' }"
    css-class="create-proj-dialog right-wrapper"
    :buttons="buttons"
    :header="modalData.title"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <div class="dialog-panel price-type-panel">
        <mt-form ref="dialogRef" :model="formObject">
          <mt-form-item prop="priceType" :label="$t('价格类型')" class="full-width">
            <mt-select
              ref="priceTypeRef"
              v-model="formObject.priceType"
              float-label-type="Never"
              @change="priceTypeHandle"
              :data-source="priceTypeList"
              :placeholder="$t('请选择价格类型')"
            ></mt-select>
          </mt-form-item>
        </mt-form>
      </div>

      <div
        class="dialog-panel category-panel"
        style="display: flex; justify-content: space-between"
      >
        <div class="panel-title" style="width: 60%">
          <span class="label-span"> {{ $t('请选择公司-工厂') }} </span>
          <div style="display: flex; justify-content: space-between">
            <div style="width: 50%">
              <mt-input
                class="input-item"
                css-class="e-outline"
                type="text"
                :placeholder="$t('请输入搜索内容')"
                @change="handleCompanyListSearch"
              ></mt-input>
              <mt-listbox
                class="listbox-view-outside"
                height="300px"
                :fields="{ text: 'companyName', value: 'organizationId' }"
                :data-source="companyCloneList"
                @change="handleCompanyList"
              ></mt-listbox>
            </div>
            <div style="width: 50%">
              <mt-input
                class="input-item"
                css-class="e-outline"
                type="text"
                @change="handlePlantListSearch"
                :placeholder="$t('请输入搜索内容')"
              ></mt-input>
              <mt-listbox
                class="listbox-view-outside"
                height="300px"
                :data-source="plantList"
                :fields="{ text: 'orgName', value: 'id' }"
                :selection-settings="selectionSettings"
                @change="handlePlantList"
              ></mt-listbox>
            </div>
          </div>
        </div>
        <div style="width: 30%">
          <span class="label-span"> {{ $t('已选公司-工厂') }} </span>
          <div style="position: relative">
            <mt-input
              class="input-item"
              css-class="e-outline"
              type="text"
              :placeholder="$t('请输入搜索内容')"
              @change="handleFiledsTemplateSearch"
            ></mt-input>
            <div class="tree-view-outside">
              <mt-treeView class="tree-view-container" :fields="filedsTemplate"></mt-treeView>
              <div class="tree-view-button">
                <mt-button css-class="e-flat" :is-primary="true" @click="handleClearTreeList">{{
                  $t('清空选择')
                }}</mt-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="dialog-panel site-panel">
        <div class="panel-title">{{ $t('采购组织：') }}</div>
        <mt-form ref="dialogRef" :model="formObject">
          <template v-for="_item in siteList_">
            <mt-form-item :label="_item.siteName" :key="_item.siteId">
              <div style="display: flex">
                <mt-select
                  :ref="`orgRef${_item.siteId}`"
                  v-model="_item.purOrgId"
                  :allow-filtering="true"
                  :fields="{ text: 'organizationName', value: 'id' }"
                  float-label-type="Never"
                  @change="orgHandle($event, _item)"
                  :data-source="_item.orgList"
                  :placeholder="$t('请选择采购组织')"
                ></mt-select>
              </div>
            </mt-form-item>
          </template>
        </mt-form>
      </div>
      <!-- <div class="dialog-panel site-panel">
        <mt-form ref="dialogRef" :model="formObject">
          <mt-form-item prop="companyIds" :label="$t('公司名称')">
            <mt-multi-select
              ref="companyIdsRef"
              v-model="formObject.companyIds"
              float-label-type="Never"
              :data-source="companyList"
              :fields="{ text: 'companyName', value: 'id' }"
              :placeholder="$t('请选择公司')"
            ></mt-multi-select>
          </mt-form-item>
          <mt-form-item prop="siteIds" label="工厂/地点">
            <mt-multi-select
              ref="siteIdsRef"
              v-model="formObject.siteIds"
              float-label-type="Never"
              :data-source="siteList"
              :fields="{ text: 'siteName', value: 'id' }"
              placeholder="请选择工厂/地点"
            ></mt-multi-select>
          </mt-form-item>
        </mt-form>
      </div> -->
      <div class="dialog-panel category-panel">
        <div class="panel-title">{{ $t('物料/品类信息：') }}</div>
        <mt-form ref="dialogRef" :model="formObject">
          <mt-form-item
            prop="categoryId"
            :label="$t('品类')"
            v-show="formObject.priceType === 0 || formObject.priceType === 2"
          >
            <mt-select
              ref="categoryRef"
              v-show="formObject.priceType === 2"
              v-model="formObject.categoryId"
              float-label-type="Never"
              :allow-filtering="true"
              :data-source="categoryList"
              @change="handleCategoryIdChange"
              :fields="{ text: 'categoryName', value: 'id' }"
              :placeholder="$t('请选择品类')"
            ></mt-select>
            <mt-input
              :readonly="true"
              v-show="formObject.priceType === 0"
              v-model="formObject.categoryName"
              :show-clear-button="false"
              :placeholder="$t('请先选择物料同步品类')"
              float-label-type="Never"
            ></mt-input>
          </mt-form-item>
          <!-- { 0: this.$t("到物料"), 1: "到SKU", 2: this.$t("到品类"), 3: "到供应商" } -->
          <mt-form-item prop="itemId" :label="$t('物料编码')" v-show="formObject.priceType === 0">
            <div style="display: flex">
              <VirtualSelect
                row-key="id"
                style="flex: 1"
                row-name="itemCode"
                :selected-value="selectedMaterialItem"
                :search-keys="['itemCode']"
                @change="handleMaterialItemChange"
                :display-keys="['itemCode']"
                :remote-method="materialItemRemoteMethod"
                :min-keywords-length="1"
                filter-type="local"
                float-label-type="Never"
              />
              <mt-button
                style="width: 28px"
                css-class="e-flat"
                @click="showInnerDialog($t('物料'))"
                icon-css="mt-icons mt-icon-icon_search"
              ></mt-button>
            </div>
          </mt-form-item>
          <mt-form-item prop="itemName" :label="$t('物料名称')" v-show="formObject.priceType === 0">
            <mt-input
              :readonly="true"
              v-model="formObject.itemName"
              :show-clear-button="false"
              float-label-type="Never"
              :placeholder="$t('物料名称')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="itemName" :label="$t('模具类型')" v-show="formObject.priceType === 0">
            <mt-select
              v-model="formObject.dieType"
              float-label-type="Never"
              :data-source="dieTypeList"
              :placeholder="$t('模具类型')"
            >
            </mt-select>
            <!-- <mt-input
              :readonly="true"
              v-model="formObject.dieType"
              :show-clear-button="false"
              float-label-type="Never"
              :placeholder="$t('模具类型')"
            ></mt-input> -->
          </mt-form-item>
          <mt-form-item prop="itemName" :label="$t('模具编码')" v-show="formObject.priceType === 0">
            <mt-input
              v-model="formObject.dieCode"
              :show-clear-button="false"
              float-label-type="Never"
              :placeholder="$t('模具编码')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            prop="parentItemId"
            :label="$t('父级物料编码')"
            v-show="formObject.priceType === 0"
          >
            <div style="display: flex">
              <VirtualSelect
                row-key="id"
                style="flex: 1"
                row-name="itemCode"
                :selected-value="selectedMaterialItemParent"
                :search-keys="['itemCode']"
                @change="handleParentMaterialItemChange"
                :display-keys="['itemCode']"
                :remote-method="materialItemRemoteMethod"
                :min-keywords-length="1"
                filter-type="local"
                float-label-type="Never"
              />
              <mt-button
                style="width: 28px"
                css-class="e-flat"
                @click="showInnerDialog($t('物料'), 'parent')"
                icon-css="mt-icons mt-icon-icon_search"
              ></mt-button>
            </div>
          </mt-form-item>

          <mt-form-item
            prop="parentItemName"
            :label="$t('父级物料名称')"
            v-show="formObject.priceType === 0"
          >
            <mt-input
              :readonly="true"
              v-model="formObject.parentItemName"
              :show-clear-button="false"
              float-label-type="Never"
              :placeholder="$t('父级物料名称')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="taxId" :label="$t('适用税率')">
            <mt-select
              ref="taxRef"
              v-model="formObject.taxId"
              float-label-type="Never"
              :data-source="taxItemList"
              :fields="{ text: 'taxItemName', value: 'id' }"
              :placeholder="$t('请选择适用税率')"
              @change="changeTaxItem"
              :allow-filtering="true"
              :filtering="filteringTaxItem"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="unitId" :label="$t('单位')">
            <mt-select
              ref="unitRef"
              v-model="formObject.unitId"
              float-label-type="Never"
              :allow-filtering="true"
              :data-source="unitList"
              :fields="{ text: 'unitName', value: 'id' }"
              :placeholder="$t('请选择单位')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="currencyId" :label="$t('币种')">
            <mt-select
              ref="currencyRef"
              v-model="formObject.currencyId"
              :allow-filtering="true"
              float-label-type="Never"
              :data-source="currencyList"
              :fields="{ text: 'currencyName', value: 'id' }"
              :placeholder="$t('请选择币种')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="skuId" label="SKU" v-show="formObject.priceType === 1">
            <div style="display: flex">
              <VirtualSelect
                row-key="id"
                style="flex: 1"
                row-name="name"
                :selected-value="selectedSku"
                :search-keys="['name']"
                @change="handleSKUChange"
                :display-keys="['name']"
                :remote-method="skuRemoteMethod"
                filter-type="local"
                float-label-type="Never"
              />
              <mt-button
                style="width: 28px"
                css-class="e-flat"
                @click="showInnerDialog('sku')"
                icon-css="mt-icons mt-icon-icon_search"
              ></mt-button>
            </div>
          </mt-form-item>
        </mt-form>
      </div>
      <div class="dialog-panel supplier-panel">
        <div class="panel-title">{{ $t('供应商选择：') }}</div>
        <mt-form ref="dialogRef" :model="formObject">
          <mt-form-item prop="supplierId" :label="$t('供应商编码')">
            <mt-select
              ref="supplierRef"
              v-model="formObject.supplierId"
              float-label-type="Never"
              allow-filtering="true"
              :data-source="supplierList"
              :fields="{ text: 'supplierCode', value: 'id' }"
              @change="handleSelectChange($event, 'supplierCode')"
              :filtering="filteringCompany"
              :placeholder="$t('请选择供应商编码')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="supplierCode" :label="$t('供应商名称')">
            <mt-input
              :readonly="true"
              v-model="formObject.supplierName"
              :show-clear-button="false"
              float-label-type="Never"
              :placeholder="$t('供应商名称')"
            ></mt-input>
          </mt-form-item>

          <mt-form-item :label="$t('价格类别')">
            <mt-select
              ref="priceValueTypeRef"
              v-model="formObject.priceValueType"
              float-label-type="Never"
              :fields="{
                text: 'priceValueName',
                value: 'priceValueType'
              }"
              :data-source="priceValueList"
              :placeholder="$t('价格类别')"
            ></mt-select>
          </mt-form-item>
          <!-- <mt-form-item
            prop="leadTime"
            :label="$t('报价属性')"
            v-show="formObject.supplierId"
          > -->
          <mt-form-item prop="leadTime" :label="$t('报价属性')">
            <!-- <mt-select
              ref="quoteAttributeRef"
              v-model="sourcePriceItem"
              float-label-type="Never"
              :data-source="sourceInfoList"
              :fields="{ text: 'offerAttribute', value: 'id' }"
              :placeholder="$t('价格属性')"
            ></mt-select> -->
            <mt-select
              ref="quoteAttributeRef"
              v-model="formObject.quoteAttribute"
              float-label-type="Never"
              :data-source="quoteAttributeList"
              :placeholder="$t('价格属性')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="leadTime" :label="$t('价格生效方式')">
            <!-- <mt-select
              ref="quoteModeRef"
              v-model="sourcePriceItem"
              float-label-type="Never"
              :data-source="sourceInfoList"
              :fields="{ text: 'priceEffectiveMode', value: 'id' }"
              :placeholder="$t('价格生效方式')"
            ></mt-select> -->
            <mt-select
              ref="quoteModeRef"
              v-model="formObject.quoteMode"
              float-label-type="Never"
              :data-source="quoteModeList"
              :placeholder="$t('价格生效方式')"
            ></mt-select>
          </mt-form-item>
          <!-- <mt-form-item prop="contractId" label="归属合同/协议">
            <mt-select
              ref="contractRef"
              v-model="formObject.contractId"
              float-label-type="Never"
              :data-source="recContractList"
              @change="handleSelectChange($event, 'contractCode', 'itemCode')"
              :fields="{ text: 'itemName', value: 'id' }"
              placeholder="请选择合同/协议"
            ></mt-select>
          </mt-form-item> -->
          <!-- <mt-form-item prop="contractCode" :label="$t('合同协议编号')">
            <mt-input
              :readonly="true"
              v-model="formObject.contractCode"
              :show-clear-button="false"
              float-label-type="Never"
              :placeholder="$t('合同协议编号')"
            ></mt-input>
          </mt-form-item> -->
          <mt-form-item prop="validTime" :label="$t('有效期限')" class="full-width">
            <mt-date-range-picker
              ref="validTimeRef"
              v-model="formObject.validTime"
              :placeholder="$t('请选择有效期限')"
              :open-on-focus="true"
            ></mt-date-range-picker>
          </mt-form-item>
        </mt-form>
      </div>
      <div class="dialog-panel">
        <div class="panel-title">{{ $t('其他：') }}</div>
        <mt-form ref="dialogRef" :model="formObject">
          <mt-form-item prop="priceUnit" :label="$t('价格单位')">
            <mt-inputNumber
              v-model="formObject.priceUnit"
              :min="0"
              class="number-item"
            ></mt-inputNumber>
          </mt-form-item>
          <mt-form-item prop="minPackageQuantity" :label="$t('最小包装数量')">
            <mt-inputNumber
              v-model="formObject.minPackageQuantity"
              :min="0"
              class="number-item"
            ></mt-inputNumber>
          </mt-form-item>
          <mt-form-item prop="minPurchaseQuantity" :label="$t('最小采购量')">
            <mt-inputNumber
              v-model="formObject.minPurchaseQuantity"
              :min="0"
              class="number-item"
            ></mt-inputNumber>
          </mt-form-item>
          <mt-form-item prop="leadTime" label="L/T">
            <mt-inputNumber v-model="formObject.leadTime" class="number-item"></mt-inputNumber>
          </mt-form-item>
          <mt-form-item prop="unconditionalLeadTime" :label="$t('无条件L/T')">
            <mt-inputNumber
              v-model="formObject.unconditionalLeadTime"
              class="number-item"
            ></mt-inputNumber>
          </mt-form-item>
          <mt-form-item prop="dieT1" :label="$t('T1时间')">
            <mt-date-picker :placeholder="$t('T1时间')" v-model="formObject.dieT1"></mt-date-picker>
          </mt-form-item>
          <mt-form-item prop="deliveryPlace" :label="$t('直送地')">
            <mt-select
              v-model="formObject.deliveryPlace"
              float-label-type="Never"
              :data-source="deliveryPlaceList"
              :placeholder="$t('直送地')"
            ></mt-select>
          </mt-form-item>
        </mt-form>
      </div>
      <div class="dialog-panel stages-panel">
        <StageQuotation
          @stage="steps"
          :price-type="formObject.priceType"
          :tax-rate="{
            text: taxItem.taxItemName,
            value: taxItem.taxRate,
            taxId: taxItem.taxId
          }"
          v-model="stageList"
        />
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import VirtualSelect from 'COMPONENTS/VirtualSelect'
import StageQuotation from 'COMPONENTS/StageQuotation'
import debounce from 'lodash.debounce'
export default {
  name: 'AddPriceDialog',
  components: { VirtualSelect, StageQuotation },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      stageList: [], //	阶梯列表
      formObject: {
        categoryCode: null, //品类编码  ok
        categoryId: null, //品类ID  ok
        categoryName: null, //品类名称  ok
        commitDeptCode: null, //提交人所在部门code
        commitDeptId: null, //	提交人所在部门ID
        commitDeptName: null, //提交人所在部门名称
        companyList: [], //	公司列表
        contractCode: null, //归属合同编码  暂时用的字典合同
        contractId: null, //	归属合同ID  暂时用的字典合同
        contractName: null, //归属合同名称  暂时用的字典合同
        currencyId: null, //币种ID  ok
        currencyName: null, //币种名称  ok
        // id: 0, //	ID
        itemCode: null, //物料编码  ok
        itemId: null, //物料ID   ok
        itemName: null, //物料名称   ok
        priceRecordCode: null, //价格记录编码
        priceType: 0, //价格类型 ok { 0: "到物料", 1: "到SKU", 2: "到品类", 3: "到供应商" }
        remark: null, //备注
        siteList: [], //地点/工厂列表
        skuCode: null, //	SKU编码  字段需要核实
        skuId: null, //	sku ID
        skuName: null, //SKU名称
        sourceCode: null, //来源编码
        sourceId: null, //来源ID
        sourceName: null, //来源名称
        sourceType: 0, //来源类型 0手动 1寻源
        spec: null, //	规格/描述
        status: 1, //	状态 0草稿 1启用 2禁用
        supplierCode: null, //供应商编码  ok
        supplierId: null, //	供应商ID   ok
        supplierName: null, //供应商名称   ok
        taxCode: null, //	税码编码  ok
        taxId: null, //税率ID  ok
        taxName: null, //税率名称  ok
        taxRate: null, //税率  ??
        // tenantId: null, //租户ID
        unitId: null, //	单位ID
        unitName: null, //单位名称
        validEndTime: null, //有效结束时间   ok
        validStartTime: null, //有效开始时间  ok
        validTime: null,
        priceUnit: 0, //价格单位
        leadTime: 0, //L/T
        unconditionalLeadTime: 0, //无条件L/T
        quoteAttribute: null, //报价属性
        quoteMode: null, //生效方式
        deliveryPlace: null, //直送地
        minPackageQuantity: 0, //最小包装量
        minPurchaseQuantity: 0, //最小采购量
        priceValueType: null
      },
      //价格类型列表 { 0: "到物料", 1: "到SKU", 2: "到品类", 3: "到供应商" }
      priceTypeList: [
        { text: this.$t('到物料'), value: 0 },
        { text: this.$t('到SKU'), value: 1 },
        { text: this.$t('到品类'), value: 2 },
        { text: this.$t('到供应商'), value: 3 }
      ],
      companyObject: {
        companyCode: '', //所属公司编码
        companyId: 0, //所属公司ID
        companyName: '' //所属公司名称
      },
      siteObject: {
        companyCode: '', //所属公司编码
        companyId: 0, //所属公司ID
        companyName: '', //	所属公司名称
        siteCode: '', //	工厂 库存组织编码
        siteId: 0, //	工厂 库存组织ID
        siteName: '' //工厂 库存组织名称
      },
      selectedSku: {},
      selectedMaterialItem: {},
      selectedMaterialItemParent: {}, //父级
      selectionSettings: { showCheckbox: true },
      plantList: [],
      filedsTemplate: {
        dataSource: [],
        id: 'treeId',
        text: 'treeName',
        child: 'subChild'
      },
      companyList: [], //公司列表
      supplierList: [], //供应商列表
      categoryList: [], //品类列表
      taxItem: {},
      taxItemList: [], //税目税率
      currencyList: [], //货币币种
      siteList: [], //地点/工厂列表
      recContractList: [], //推荐合同类型
      unitList: [], //基本单位列表
      formRules: {},
      companyCloneList: [],
      specifiedParentLevelOrgs: '', //当前所选公司
      deliveryPlaceList: [], //直送地
      sourceInfoList: [], //价格属性 和 生效方式
      quoteAttributeList: [
        {
          text: this.$t('寄售价'),
          value: 'mailing_price'
        },
        {
          text: this.$t('标准价'),
          value: 'standard_price'
        }
      ],
      quoteModeList: [
        {
          text: this.$t('按照入库'),
          value: 'in_warehouse'
        },
        {
          text: this.$t('按出库'),
          value: 'out_warehouse'
        },
        {
          text: this.$t('按订单日期'),
          value: 'order_date'
        }
      ],
      sourcePriceItem: null,
      siteList_: [],
      priceValueList: [
        { priceValueName: this.$t('基价'), priceValueType: '1' },
        { priceValueName: this.$t('SRM价'), priceValueType: '2' },
        { priceValueName: this.$t('暂估价格'), priceValueType: '3' },
        { priceValueName: this.$t('执行价'), priceValueType: '4' }
      ],
      plantCloneList: [],
      dieTypeList: [
        { text: this.$t('基础模具'), value: 1 },
        { text: this.$t('复制模具'), value: 2 },
        { text: this.$t('基础模改模'), value: 3 },
        { text: this.$t('复制模改模'), value: 4 }
      ],
      itemCodeInputShow: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.getCommonList()
    this.getUserInfoDetail()
    this.getDeliveryPlace()
  },
  methods: {
    filteringCompany: debounce(function (e) {
      let { text } = e
      this.$API.quotaConfig
        .getSupplierList({
          supplierCode: text
        })
        .then((res) => {
          this.supplierList = res.data
        })
    }, 1000),
    /** @param {'sku', '物料'} type*/
    showInnerDialog(type, parent) {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/price/list/components/addPrice" */ './components/selectSKUOrCategory.vue'
          ),
        data: { type },
        success: (items) => {
          if (type === 'sku') {
            this.selectedSku = items[0]
            const item = this.selectedSku
            this.formObject.skuId = item.id
            this.formObject.skuName = item.name
            this.formObject.skuCode = item.barCode
          } else {
            if (!parent) {
              this.selectedMaterialItem = items[0]
              this.formObject.itemCode = items[0].itemCode
              this.formObject.itemName = items[0].itemName
              this.formObject.itemId = items[0].id
              this.itemCodeInputShow = true
              const categoryId = items[0].categoryResponse?.id ?? ''
              const categoryCode = items[0].categoryResponse?.categoryCode ?? ''
              const categoryName = items[0].categoryResponse?.categoryName ?? ''
              this.formObject.categoryId = categoryId
              this.formObject.categoryName = categoryName
              this.formObject.categoryCode = categoryCode
            } else {
              this.selectedMaterialItemParent = items[0]
              this.formObject.parentItemCode = items[0].itemCode
              this.formObject.parentItemName = items[0].itemName
              this.formObject.parentItemId = items[0].id
            }
          }
        }
      })
    },
    // sku select change
    handleSKUChange(item) {
      this.selectedSku = item
      this.formObject.skuId = item.id
      this.formObject.skuName = item.name
      this.formObject.skuCode = item.barCode
    },
    handleMaterialItemChange(item) {
      this.selectedMaterialItem = item
      this.formObject.itemCode = item.itemCode
    },
    handleParentMaterialItemChange(item) {
      this.selectedMaterialItemParent = item
      this.formObject.parentItemCode = item.itemCode
    },
    skuRemoteMethod() {
      return this.$API.masterData.getSKUList().then((res) => res.data.records)
    },
    materialItemRemoteMethod() {
      return this.$API.masterData.getItemList().then((res) => res.data)
    },

    getUserInfoDetail() {
      this.$API.iamService.getUserDetail().then((res) => {
        let { data } = res
        let { department } = data
        this.formObject.commitDeptId = department.id
        this.formObject.commitDeptCode = department.orgCode
        this.formObject.commitDeptName = department.orgName
      })
    },
    orgHandle(e, _item) {
      if (e.itemData) {
        _item.purOrgCode = e.itemData.organizationCode
        _item.purOrgName = e.itemData.organizationName
      }
    },
    //工厂选择变化
    handlePlantList(e) {
      this.filedsTemplate = {
        dataSource: [],
        id: 'treeId',
        text: 'treeName',
        child: 'subChild'
      }
      this.companyList.forEach((item) => {
        if (item.organizationId == this.specifiedParentLevelOrgs) {
          item.subChild = e.items.length ? e.items : []
        }
      })
      let data = this.companyList.filter((item) => item?.subChild?.length)
      this.filedsTemplate.dataSource.push(...data)
      this.resizeSiteList(data)
      //
    },

    // 工厂变化 生成工厂列表
    resizeSiteList(data) {
      const _list = data
      const curSite = []
      if (!data.length) this.siteList_ = []
      _list.forEach((c) => {
        if (Array.isArray(c.subChild) && c.subChild.length) {
          c.subChild.forEach(async (s) => {
            let c_data = await this.getOrgList({ orgId: s.id })
            curSite.push({
              companyCode: c.companyCode, //所属公司编码
              companyId: c.id, //所属公司ID
              companyName: c.companyName, //所属公司名称
              siteCode: s.orgCode, //	工厂 库存组织编码
              siteId: s.id, //	工厂 库存组织ID
              siteName: s.orgName, //工厂 库存组织名称
              orgList: c_data
            })
          })
        }
      })
      this.siteList_ = curSite
    },
    //公司选择变化
    handleCompanyList(e) {
      this.getSpecifiedChildrenLevelOrgs(e.value)
      this.specifiedParentLevelOrgs = e.value
    },
    //获取公司下级工厂
    getSpecifiedChildrenLevelOrgs(ids) {
      this.$API.masterData
        .findSpecifiedChildrenLevelOrgs({
          organizationLevelCodes: ['ORG06'],
          orgType: 'ORG001PRO',
          includeItself: false,
          organzationIds: ids
        })
        .then((res) => {
          this.plantList = res.data
          this.plantList.forEach((item) => {
            item.treePid = ids[0]
            item.treeName = item.orgName
            item.treeId = item.id
          })
          this.plantCloneList = JSON.parse(JSON.stringify(this.plantList))
        })
    },
    handleCompanyListSearch(e) {
      this.companyCloneList = this.companyList.filter((item) => {
        return item.companyName.indexOf(e) !== -1 ? item : null
      })
    },
    handlePlantListSearch(e) {
      if (!e) {
        this.plantList = JSON.parse(JSON.stringify(this.plantCloneList))
        return false
      }
      this.plantList = this.plantList.filter((item) => {
        return item.orgName.indexOf(e) !== -1 ? item : null
      })
    },
    //获取价格属性和生效类型
    getSourceInfo({ supplierCode }) {
      // 获取 价格类型和生效方式
      this.$API.masterData
        .getSupplySourceInfo({
          supplierCode
        })
        .then((res) => {
          this.sourceInfoList = res.data
        })
    },
    //清空公司-工厂组件树
    handleClearTreeList() {
      this.filedsTemplate = {
        dataSource: [],
        id: 'treeId',
        text: 'treeName',
        child: 'subChild'
      }
      this.companyList.forEach((item) => {
        item.subChild = []
      })
    },
    handleFiledsTemplateSearch(e) {
      this.filedsTemplate = {
        dataSource: [],
        id: 'treeId',
        text: 'treeName',
        child: 'subChild'
      }
      //没有返回原数组
      if (!e) {
        let data = this.companyList.filter((item) => item?.subChild?.length)
        this.filedsTemplate.dataSource.push(...data)
        return
      }
      //匹配
      const companyLists = JSON.parse(JSON.stringify(this.companyList))
      let data = companyLists.filter((item) => {
        if (item.subChild?.length) {
          item.subChild = item.subChild.filter((_item) => _item.treeName.indexOf(e) >= 0)
          return item
        }
        return false
      })
      this.filedsTemplate.dataSource.push(...data)
    },
    changeTaxItem(e) {
      this.taxItem = e?.itemData ?? {}
    },
    //获取弹框中，需要的一些列表数据
    getCommonList() {
      // 获取公司编码、公司名称  {companyCode、companyName、id}
      this.$API.masterData
        .getCompanyList({
          companyCode: '',
          companyName: '',
          orgType: 'ORG001PRO'
        })
        .then((res) => {
          this.companyList = res.data
          this.companyList.forEach((item) => {
            item.treeId = item.organizationId
            item.treeName = item.companyName
          })
          this.companyCloneList = this.companyList
        })
      // 品类列表 categoryList   {categoryCode、categoryName、id}
      this.$API.masterData.getCategoryList().then((res) => {
        this.categoryList = res.data
      })

      // SKU列表 skuList   {barCode、name、id}
      // this.$API.masterData.getSKUList().then((res) => {
      //   this.skuList = res.data.records;
      // });

      // 供应商列表 supplierList   {supplierCode、supplierName、id}
      this.$API.masterData.getSupplierList().then((res) => {
        this.supplierList = res.data
      })

      //税目税率  taxItemList  {taxItemCode、taxItemName、id}
      this.$API.masterData.queryAllTaxItem().then((res) => {
        this.taxItemList = res.data
      })

      //货币币种  currencyList  {currencyCode、currencyName、id}
      this.$API.masterData.queryAllCurrency().then((res) => {
        this.currencyList = res.data
      })

      // 地点/工厂 siteList  {siteCode、siteName、id}
      this.$API.masterData.getSiteList().then((res) => {
        this.siteList = res.data.records
      })
      // 推荐合同类型 - recContractList   {itemCode、itemName、id}
      this.$API.masterData
        .dictionaryGetList({
          dictCode: 'contractType'
        })
        .then((res) => {
          this.recContractList = res.data
        })

      // todo 有问题的字段列表
      //基本单位  字段： unitList  维护数据有误
      this.$API.masterData.pagedQueryUnit().then((res) => {
        this.unitList = res.data.records
      })
    },
    handleSelectChange(e, formField, sourceField = null) {
      console.log('handleSelectChange--', formField, sourceField)
      let _data = e?.itemData
      this.$set(this.formObject, formField, _data[sourceField ? sourceField : formField])
      console.log('下拉列表切换--', this.formObject)
      if (formField === 'supplierCode') {
        this.$set(this.formObject, 'supplierName', _data?.supplierName)
        this.getSourceInfo(_data)
      }
    },
    // 物料change
    handleCategoryIdChange() {
      this.sourceInfoList = []
      this.sourcePriceItem = null
    },
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          const formObjectSet = JSON.parse(JSON.stringify(this.formObject))
          let params = { ...formObjectSet }
          if (params.dieT1) {
            params.dieT1 = new Date(params.dieT1).valueOf()
          }

          //处理起始、结束时间：
          if (Array.isArray(params.validTime) && params.validTime.length === 2) {
            params.validStartTime = this.$utils.formatTime(new Date(formObjectSet.validTime[0]))
            params.validEndTime = this.$utils.formatTime(new Date(formObjectSet.validTime[1]))
          }
          delete params.validTime
          params.stageList = this.stageList
          if (this.stageList.length > 0) {
            params.stageType = this.stageList[0]['stageType']
            delete params.taxedUnitPrice
            delete params.untaxedUnitPrice
            delete params.planQuantity
            delete params.shareQuantity
            delete params.planSharePriceTaxed
            delete params.planSharePriceUntaxed
            delete params.realSharePriceTaxed
            delete params.realSharePriceUntaxed
            delete params.sharePriceTaxed
            delete params.sharePriceUntaxed
          } else {
            params.stageType = -1
          }

          // //处理多选下拉框，根据当前选择的companyIds，获取company对象数组
          // let _companyIds = params.companyIds;
          // if (Array.isArray(_companyIds) && _companyIds.length) {
          //   let _list = [];
          //   for (let i in _companyIds) {
          //     let _find = this.$refs.companyIdsRef.ejsRef.getDataByValue(
          //       _companyIds[i]
          //     );
          //     _list.push(_find);
          //   }
          //   params.companyList = _list;
          // }
          // delete params.companyIds;

          // //处理多选下拉框，根据当前选择的siteIds，获取site对象数组
          // let _siteIds = params.siteIds;
          // if (Array.isArray(_siteIds) && _siteIds.length) {
          //   let _list = [];
          //   for (let i in _siteIds) {
          //     let _find = this.$refs.siteIdsRef.ejsRef.getDataByValue(
          //       _siteIds[i]
          //     );
          //     _list.push(_find);
          //   }
          //   params.siteList = _list;
          // }
          // delete params.siteIds;
          let categoryRef = [
            {
              key: 'categoryId', //品类categoryId下拉框数据
              ref: this.$refs?.categoryRef?.ejsRef,
              fields: ['categoryName', 'categoryCode']
            }
          ]
          if (this.itemCodeInputShow) {
            categoryRef = []
          }
          this.$utils.assignDataFromRefs(params, [
            ...categoryRef,
            // {
            //   key: "contractId", //合同contractId下拉框数据
            //   ref: this.$refs.contractRef.ejsRef,
            //   fields: {
            //     contractCode: "itemCode",
            //     contractName: "itemName",
            //   },
            // },
            {
              key: 'currencyId', //货币currencyId下拉框数据
              ref: this.$refs.currencyRef.ejsRef,
              fields: ['currencyName', 'currencyCode']
            },
            // {
            //   key: "itemId", //物料itemId下拉框数据
            //   ref: this.$refs.itemRef.ejsRef,
            //   fields: ["itemName", "itemCode"],
            // },
            {
              key: 'supplierId', //供应商supplierId下拉框数据
              ref: this.$refs.supplierRef.ejsRef,
              fields: ['supplierName', 'supplierCode']
            },
            {
              key: 'taxId', //适用税率taxId下拉框数据
              ref: this.$refs.taxRef.ejsRef,
              fields: {
                taxRate: 'taxRate',
                taxCode: 'taxItemCode',
                taxName: 'taxItemName'
              }
            },
            {
              key: 'unitId', //单位unitId下拉框数据
              ref: this.$refs.unitRef.ejsRef,
              fields: ['unitName', 'unitCode']
            },
            {
              key: 'priceValueType', //价格类型下拉框数据
              ref: this.$refs.priceValueTypeRef.ejsRef,
              fields: 'priceValueName'
            }
          ])
          let _compList = [],
            _list = [...this.filedsTemplate.dataSource]
          _list.forEach((c) => {
            _compList.push({
              companyCode: c.companyCode, //所属公司编码
              companyId: c.id, //所属公司ID
              companyName: c.companyName //所属公司名称
            })
            // if (Array.isArray(c.subChild) && c.subChild.length) {
            //   c.subChild.forEach((s) => {
            //     _siteList.push({
            //       companyCode: c.companyCode, //所属公司编码
            //       companyId: c.id, //所属公司ID
            //       companyName: c.companyName, //所属公司名称
            //       siteCode: s.orgCode, //	工厂 库存组织编码
            //       siteId: s.id, //	工厂 库存组织ID
            //       siteName: s.orgName, //工厂 库存组织名称
            //     });
            //   });
            // }
          })
          // this.siteList_.forEach((_t) => {
          //   delete _t.orgList;
          // });
          params.siteList = this.siteList_
          params.companyList = _compList
          const [sourceInfos] = this.sourceInfoList.filter((i) => i.id == this.sourcePriceItem)
          if (sourceInfos) {
            params.quoteMode = sourceInfos.priceEffectiveMode
            params.quoteAttribute = sourceInfos.offerAttribute
          }
          //{ 0: "到物料", 1: "到SKU", 2: "到品类", 3: "到供应商" }
          if (this.formObject.priceType === 0) {
            //移除sku字段
            delete params.skuId
            delete params.skuCode
            delete params.skuName
          } else if (this.formObject.priceType === 1) {
            //移除物料字段
            delete params.itemId
            delete params.itemCode
            delete params.itemName
            delete params.dieType
            delete params.dieCode
            delete params.parentItemCode
            delete params.parentItemName
            delete params.parentItemId

            //移除品类字段
            delete params.categoryId
            delete params.categoryCode
            delete params.categoryName
          } else if (this.formObject.priceType === 2) {
            //移除sku字段
            delete params.skuId
            delete params.skuCode
            delete params.skuName

            //移除物料字段
            delete params.itemId
            delete params.itemCode
            delete params.itemName
            delete params.dieType
            delete params.dieCode
            delete params.parentItemCode
            delete params.parentItemName
            delete params.parentItemId
          } else if (this.formObject.priceType === 3) {
            //移除sku字段
            delete params.skuId
            delete params.skuCode
            delete params.skuName

            //移除物料字段
            delete params.itemId
            delete params.itemCode
            delete params.itemName
            delete params.dieType
            delete params.dieCode
            delete params.parentItemCode
            delete params.parentItemName
            delete params.parentItemId

            //移除品类字段
            delete params.categoryId
            delete params.categoryCode
            delete params.categoryName
          }
          console.log('新增-价格记录', params)
          // 必填校验
          if (!params.companyList.length) {
            this.$toast({
              content: this.$t('请选择公司信息'),
              type: 'warning'
            })
            return
          }
          if (params.priceType != 1 && params.priceType != 3) {
            if (!params.categoryId) {
              this.$toast({
                content: this.$t('请选择品类信息'),
                type: 'warning'
              })
              return
            }
          }

          if (!params.taxId) {
            this.$toast({
              content: this.$t('请选择适用税率'),
              type: 'warning'
            })
            return
          }
          if (!params.supplierId) {
            this.$toast({
              content: this.$t('请选择供应商'),
              type: 'warning'
            })
            return
          }
          if (!params.unitId) {
            this.$toast({
              content: this.$t('请选择单位'),
              type: 'warning'
            })
            return
          }
          if (!params.currencyId) {
            this.$toast({
              content: this.$t('请选择币种'),
              type: 'warning'
            })
            return
          }
          if (!params.validStartTime) {
            this.$toast({
              content: '请选择有效期   ',
              type: 'warning'
            })
            return
          }
          if (!params.siteList.length) {
            this.$toast({
              content: this.$t('请选择工厂信息'),
              type: 'warning'
            })
            return
          }
          let isEnable = false
          params.siteList.forEach((item) => {
            if (!item.purOrgId) {
              isEnable = true
            }
          })
          if (isEnable) {
            this.$toast({
              content: this.$t('有工厂没有选择采购组织'),
              type: 'warning'
            })
            return
          }
          if (params.stageType == -1) {
            if (
              params.taxedUnitPrice === '' ||
              params.taxedUnitPrice === null ||
              params.taxedUnitPrice === undefined
            ) {
              this.$toast({
                content: this.$t('请填写未税价格'),
                type: 'warning'
              })
              return
            }
          }
          params.siteList.forEach((_t) => {
            delete _t.orgList
          })
          this.$API.priceService.singleSavePrice(params).then((res) => {
            if (res.code === 200) {
              this.$emit('confirm-function')
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    getDeliveryPlace() {
      this.$API.priceService.getDeliveryPlace().then((r) => {
        const d = r.data.map((i) => {
          return {
            text: i.itemName,
            value: i.itemName
          }
        })
        this.deliveryPlaceList = d
      })
    },
    steps(e) {
      if (e) {
        for (let i in e) {
          this.formObject[i] = e[i]
        }
      }
    },
    async getOrgList(param) {
      let data = []
      await this.$API.masterData.getOrgListByCid(param).then((res) => {
        data = res.data
      })
      return data
    },
    filteringCurrency(e) {
      if (typeof e.text === 'string' && e.text) {
        e.updateData(this.currencyList.filter((f) => f?.currencyName.indexOf(e.text) > -1))
      } else {
        e.updateData(this.currencyList)
      }
    },
    filteringTaxItem(e) {
      if (typeof e.text === 'string' && e.text) {
        e.updateData(this.taxItemList.filter((f) => f?.taxItemName.indexOf(e.text) > -1))
      } else {
        e.updateData(this.taxItemList)
      }
    },
    priceTypeHandle() {
      this.itemCodeInputShow = false
      this.formObject.categoryId = ''
      this.formObject.categoryCode = ''
      this.formObject.categoryName = ''
      if (this.formObject.priceType === 0) {
        this.itemCodeInputShow = true
      }
    }
  }
}
</script>
<style lang="scss">
.e-dlg-container > .right-wrapper {
  height: 100% !important;
  .dialog-panel {
    .panel-title {
      font-size: 16px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(41, 41, 41, 1);
      margin-bottom: 18px;
      &.price-title {
        margin-bottom: 0;
      }
    }
    .full-width {
      width: 100% !important;
    }
  }
}
</style>

<style lang="scss" scoped>
.dialog-content {
  .tree-view-outside {
    box-shadow: inset 0 0 0 1px rgba(232, 232, 232, 1);
    height: 300px;
    width: 100%;
    margin-top: -5px;
  }
  /deep/ .mt-listbox {
    margin-top: -5px;
  }
  /deep/ .tree-view-container {
    width: 100%;
    height: 250px;
    overflow: auto;
    ::-webkit-scrollbar {
      width: 0 !important;
    }
  }
  .tree-view-button {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .label-span {
    color: #465b73;
    font-weight: 600;
    font-size: 14px;
  }
}
</style>
