<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :style="{ width: '940px' }"
    :header="$t('选择') + modalData.type"
    @beforeClose="cancel"
  >
    <div class="full-height">
      <mt-template-page
        class="template-height has-page"
        ref="table"
        :template-config="pageConfig"
      />
    </div>
  </mt-dialog>
</template>

<script>
import { SKUTemplateConfig, categoryTemplateConfig } from '../config'
export default {
  name: 'SelectSKUOrCategory',
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ]
    }
  },
  computed: {
    pageConfig() {
      return this.modalData.type === 'sku' ? SKUTemplateConfig : categoryTemplateConfig
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    confirm() {
      this.$emit(
        'confirm-function',
        this.$refs.table.getCurrentUsefulRef().ejsRef.getSelectedRecords()
      )
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style scoped lang="scss">
.full-height {
  height: 100%;
}
/deep/.template-height {
  .mt-data-grid {
    height: 100%;
    > .e-grid {
      height: calc(100% - 40px);
      .e-content {
        height: 100% !important;
      }
    }
  }
}
/deep/.template-height.has-page {
  .repeat-template .mt-data-grid > .e-control {
    height: calc(100% - 40px) !important;
  }
}
</style>
