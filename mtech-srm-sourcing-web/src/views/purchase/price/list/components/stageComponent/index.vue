<template>
  <div class="stage-warper">
    <div class="e-table-wrap" v-if="data.stageList && data.stageList.length">
      <div class="th bg">
        <div class="td col2">{{ $t('序号') }}</div>
        <div class="td col1">{{ $t('起始值（≥）') }}</div>
        <div class="td col1">{{ $t('结束值（＜）') }}</div>
        <div class="td col1">{{ $t('说明') }}</div>
        <div class="td col1">{{ $t('未税单价') }}</div>
        <div class="td col1">{{ $t('含税单价') }}</div>
      </div>
      <div class="tbody">
        <template v-if="data.stageList && data.stageList.length">
          <div class="tr" v-for="(item, index) in data.stageList" :key="'a' + index">
            <div class="td col2">{{ index + 1 }}</div>
            <div class="td col1">{{ item.stageFrom }}</div>
            <div class="td col1">{{ item.stageTo }}</div>
            <div class="td col1">
              <span> {{ item.remark }}</span>
            </div>
            <div class="td col1">{{ item.untaxedUnitPrice }}</div>
            <div class="td col1">{{ item.taxedUnitPrice }}</div>
          </div>
        </template>

        <div class="tr empty" v-if="!data.stageList || !data.stageList.length">
          {{ $t('暂无数据') }}
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {}
  }
}
</script>

<style lang="scss" scoped>
.stage-warper {
  padding: 20px;
}
.e-table-wrap {
  width: 100%;
  font-size: 12px;
  border: 1px solid #eaeaea;
  background-color: #fff;
  border-right: none;
  border-bottom: none;
  .th,
  .tr {
    display: flex;
    min-height: 30px;

    justify-content: center;
    div {
      text-align: center;
    }
    .col2 {
      width: 10%;
    }
    .col1 {
      width: 18%;
    }
    .col5 {
      width: 80%;
    }
    .col32 {
      width: 32%;
    }
    .td {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;
      padding: 5px;
      line-height: normal;
      text-align: center;
      border-right: 1px solid #eaeaea;
      border-bottom: 1px solid #eaeaea;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .bg {
    background-color: #fafafa;
  }
}
.empty {
  line-height: 30px;
  padding-left: 20px;
  border-right: 1px solid #eaeaea;
  border-bottom: 1px solid #eaeaea;
}
.tbody {
  max-height: 150px;
  overflow: auto;
}
</style>
