import { i18n, permission } from '@/main.js'
import historyes from '../components/history/index.vue'
import priceHistory from '../components/priceHistory/index.vue'
import APIS from '@/apis/modules/purchase/price/index'
import Vue from 'vue'
import specifiCation from '../components/logisticsDto/specifiCation'
export const toolbar = [
  { id: 'ImportSAP', icon: 'icon_solid_upload', title: i18n.t('导入SAP') },
  { id: 'export', icon: 'icon_solid_Download ', title: i18n.t('导出'), permission: ['O_02_1328'] },
  {
    id: 'transfer',
    icon: 'icon_solid_Download ',
    title: i18n.t('定价后转合同'),
    permission: ['O_02_1845']
  }
]
export const toolbarCost = [{ id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') }]
export const toolbarLogic = []
export const toolbarCom = [
  { id: 'ImportSAP', icon: 'icon_solid_upload', title: i18n.t('导入SAP') },
  { id: 'ComExport', icon: 'icon_solid_Download ', title: i18n.t('导出') }
]
const syncStatus = {
  0: i18n.t('无需同步'),
  1: i18n.t('未同步'),
  2: i18n.t('同步中'),
  3: i18n.t('同步成功'),
  4: i18n.t('同步失败')
}
const quoteMode = {
  in_warehouse: i18n.t('按照入库'),
  out_warehouse: i18n.t('按出库'),
  order_date: i18n.t('按订单日期')
}
const quoteAttribute = {
  mailing_price: i18n.t('寄售价'),
  standard_price: i18n.t('标准价'),
  outsource: i18n.t('委外价')
}

export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: syncStatus
    }
  },
  {
    field: 'syncMsg',
    headerText: i18n.t('同步信息')
  },
  {
    field: 'orderNo',
    with: 200,
    headerText: i18n.t('采购订单号')
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    width: '200',
    searchOptions: {
      operator: 'equal',
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    width: '200'
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    width: '200',
    searchOptions: {
      operator: 'equal',
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    width: '200'
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    width: '150',
    searchOptions: {
      operator: 'equal',
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: '200'
  },

  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('含税单价'),
    ignore: true,
    searchOptions: {
      serializeValue: (e) => {
        //自定义搜索值，规则
        return +e
      },
      operator: 'equal',
      type: 'number'
    }
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('未税单价'),
    ignore: true,
    searchOptions: {
      serializeValue: (e) => {
        //自定义搜索值，规则
        return +e
      },
      operator: 'equal',
      type: 'number'
    }
  },
  {
    field: 'priceUnit',
    headerText: i18n.t('价格单位'),
    ignore: true
  },
  {
    field: 'priceValueName',
    headerText: i18n.t('价格类别名称'),
    ignore: true,
    width: 0
  },
  {
    field: 'priceValueType',
    headerText: i18n.t('价格类别'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('基价'),
        2: i18n.t('SRM价'),
        3: i18n.t('暂估价格'),
        4: i18n.t('执行价')
      }
    }
  },
  {
    field: 'priceType',
    headerText: i18n.t('价格类型'),
    ignore: true,
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('到物料'),
        1: i18n.t('到SKU'),
        2: i18n.t('到品类'),
        3: i18n.t('到供应商'),
        4: i18n.t('业务单号'),
        5: i18n.t('行情因子'),
        6: i18n.t('物流'),
        8: i18n.t('模具'),
        9: i18n.t('结构阶梯'),
        10: i18n.t('美工阶梯')
      }
    }
  },
  {
    field: 'companyList',
    headerText: i18n.t('公司名称'),
    width: '200',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['companyName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司编码'),
    width: '0',
    searchOptions: {
      operator: 'equal',
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂/地点'),
    width: '200',
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.siteList[0]['siteName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂编码'),
    width: '0',
    searchOptions: {
      operator: 'equal',
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织'),
    ignore: true,
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.siteList[0]['purOrgName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'purOrgCode',
    headerText: i18n.t('采购组织编码'),
    width: '0',
    searchOptions: {
      operator: 'equal',
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'quoteAttribute',
    headerText: i18n.t('报价属性'),
    valueConverter: {
      type: 'map',
      map: quoteAttribute
    }
  },
  {
    field: 'quoteMode',
    headerText: i18n.t('价格生效方式'),
    valueConverter: {
      type: 'map',
      map: quoteMode
    }
  },
  {
    field: 'deliveryPlace',
    headerText: i18n.t('直送地'),
    ignore: true
  },
  {
    field: 'unitName',
    headerText: i18n.t('单位'),
    ignore: true
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种'),
    ignore: true
  },
  {
    field: 'taxRate',
    headerText: i18n.t('税率'),
    ignore: true
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('生效日期'),
    type: 'date',
    format: 'yyyy-MM-dd',
    searchOptions: {
      operator: 'greaterthanorequal',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return new Date(e).valueOf()
      }
    }
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('失效日期'),
    type: 'date',
    format: 'yyyy-MM-dd',
    searchOptions: {
      operator: 'lessthanorequal',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return new Date(e).valueOf()
      }
    }
  },
  {
    field: 'priceRecordCode',
    headerText: i18n.t('价格编码'),
    width: 200,
    cssClass: 'field-content',
    searchOptions: {
      operator: 'equal',
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'sourceCode',
    headerText: i18n.t('来源单号'),
    width: 200,
    searchOptions: {
      operator: 'equal',
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'minPackageQuantity',
    headerText: i18n.t('最小包装数量'),
    ignore: true
  },
  {
    field: 'minPurchaseQuantity',
    headerText: i18n.t('最小采购量'),
    ignore: true
  },
  {
    field: 'leadTime',
    headerText: 'L/T',
    ignore: true
  },
  {
    field: 'unconditionalLeadTime',
    headerText: i18n.t('无条件L/T'),
    ignore: true
  },
  {
    field: 'paymentCondition',
    headerText: i18n.t('付款条件'),
    ignore: true
  },
  {
    field: 'historyRecord',
    headerText: i18n.t('历史记录'),
    ignore: true,
    template: function () {
      return {
        template: historyes
      }
    }
  },
  {
    field: 'priceRecordId',
    headerText: i18n.t('历史价格趋势'),
    ignore: true,
    template: function () {
      return {
        template: priceHistory
      }
    }
  },

  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('草稿'), 1: i18n.t('启用'), 2: i18n.t('禁用'), 3: i18n.t('未合格') }
    }
  },
  {
    field: 'purchaseAgentName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    ignore: true
  },
  {
    field: 'sourceCreateTime',
    headerText: i18n.t('单据创建时间'),
    ignore: true
  },
  {
    field: 'oaApprovalFinishTime',
    headerText: i18n.t('OA审批完成时间'),
    ignore: true
  },
  {
    field: 'syncFinishTime',
    headerText: i18n.t('同步SAP完成时间'),
    ignore: true
  }
]

export const columnDataCb = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'historyRecord',
    headerText: i18n.t('历史记录'),
    template: function () {
      return {
        template: historyes
      }
    }
  },
  {
    field: 'priceRecordId',
    headerText: i18n.t('历史价格趋势'),
    template: function () {
      return {
        template: priceHistory
      }
    }
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('草稿'), 1: i18n.t('启用'), 2: i18n.t('禁用') }
    }
  },
  {
    field: 'marketFactorCode',
    headerText: i18n.t('成本因子编码'),
    searchOptions: {
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'marketFactorName',
    headerText: i18n.t('成本因子名称')
  },
  {
    field: 'marketFactorBrand',
    headerText: i18n.t('品牌')
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('单价(含税)')
  },
  {
    field: 'taxRate',
    headerText: i18n.t('税率')
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    searchOptions: {
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'companyList',
    headerText: i18n.t('公司名称'),
    width: '200',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['companyName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂/地点'),
    width: '200',
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.siteList[0]['siteName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织'),
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.siteList[0]['purOrgName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'sourceId',
    headerText: i18n.t('来源单号'),
    searchOptions: {
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('生效日期'),
    type: 'date',
    format: 'yyyy-MM-dd',
    searchOptions: {
      operator: 'equal',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return new Date(e).valueOf()
      }
    }
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('失效日期'),
    type: 'date',
    format: 'yyyy-MM-dd',
    searchOptions: {
      operator: 'equal',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return new Date(e).valueOf()
      }
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期')
  }
]

export const listColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    searchOptions: {
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'material',
    headerText: i18n.t('物料材质')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    searchOptions: {
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '200',
    field: 'detail',
    headerText: i18n.t('明细'),
    cssClass: 'field-content',
    valueConverter: {
      type: 'placeholder',
      placeholder: i18n.t('明细')
    },
    allowFiltering: false,
    ignore: true
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('草稿'), 1: i18n.t('启用'), 2: i18n.t('禁用') }
    }
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('含税单价'),
    type: 'number',
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('未税单价'),
    type: 'number',
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    field: 'taxRate',
    headerText: i18n.t('税率'),
    type: 'number',
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称'),
    allowFiltering: false,
    ignore: true
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织')
  },
  {
    field: 'quoteAttribute',
    headerText: i18n.t('报价属性'),
    valueConverter: {
      type: 'map',
      map: quoteAttribute
    }
  },
  {
    field: 'quoteMode',
    headerText: i18n.t('价格生效方式'),
    valueConverter: {
      type: 'map',
      map: quoteMode
    }
  },
  {
    field: 'deliveryPlace',
    headerText: i18n.t('直送地')
  },
  {
    field: 'priceRecordCode',
    headerText: i18n.t('价格编码'),
    searchOptions: {
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种')
  },
  {
    field: 'sourceCode',
    headerText: i18n.t('来源单号'),
    searchOptions: {
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    searchOptions: {
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('失效日期'),
    type: 'date',
    format: 'yyyy-MM-dd',
    searchOptions: {
      operator: 'equal',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return new Date(e).valueOf()
      }
    }
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('生效日期'),
    type: 'date',
    format: 'yyyy-MM-dd',
    searchOptions: {
      operator: 'equal',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return new Date(e).valueOf()
      }
    }
  },
  {
    field: 'minPackageQuantity',
    headerText: i18n.t('最小包装量')
  },
  {
    field: 'minPurchaseQuantity',
    headerText: i18n.t('最小采购量')
  },
  {
    field: 'leadTime',
    headerText: i18n.t('L/T')
  },
  {
    field: 'unconditionalLeadTime',
    headerText: i18n.t('无条件L/T')
  },
  {
    field: 'paymentCondition',
    headerText: i18n.t('付款条件')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    type: 'date',
    format: 'yyyy-MM-dd',
    allowFiltering: false,
    ignore: true
    // searchOptions: {
    //   serializeValue: (e) => {
    //     //自定义搜索值，规则
    //     return new Date(e).valueOf();
    //   },
    // },
  }
]

export const columnDataLogic = [
  {
    field: 'companyList',
    headerText: i18n.t('公司名称'),
    width: '200',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['companyName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'purchaseAgentName',
    headerText: i18n.t('采购员')
  },
  {
    field: 'logisticsType',
    headerText: i18n.t('运输方式')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  }
]

export const pageConfigOuter = [
  {
    title: i18n.t('物料价格库')
  },
  {
    title: i18n.t('成本因子')
  },
  {
    title: i18n.t('物流价格记录')
  },
  {
    title: i18n.t('系列物料价格')
  },
  {
    title: i18n.t('模具')
  },
  {
    title: i18n.t('结构阶梯')
  },
  {
    title: i18n.t('美工阶梯')
  },
  {
    title: i18n.t('部品+模具')
  },
  {
    title: i18n.t('外发阶梯')
  }
]

export const logisticsConfig = [
  {
    title: i18n.t('海运'),
    id: 1
  },
  {
    title: i18n.t('空运'),
    id: 2
  },
  {
    title: i18n.t('铁路'),
    id: 3
  },
  {
    title: i18n.t('陆运'),
    id: 4
  },
  {
    title: i18n.t('拖车'),
    id: 5
  }
]
const columnDataLo = {}
// 托运
columnDataLo['5'] = [
  {
    type: 'checkbox',
    width: '50'
  },
  {
    field: 'companyList',
    headerText: i18n.t('公司名称'),
    width: '200',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['companyName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'purchaseAgentName',
    headerText: i18n.t('采购员')
  },
  {
    field: 'logisticsDto.transportType',
    headerText: i18n.t('运输方式'),
    width: '200'
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    width: '200'
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    width: '200'
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    width: '200'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: '200'
  },
  {
    field: 'siteList',
    headerText: i18n.t('采购组织'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['purOrgName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'logisticsDto.startPlace',
    headerText: i18n.t('发运地'),
    width: '200'
  },
  {
    field: 'logisticsDto.endPlace',
    headerText: i18n.t('目的地'),
    width: '200'
  },
  {
    field: 'logisticsDto.trailerItem',
    headerText: i18n.t('项目'),
    width: '300'
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('运费'),
    width: '200'
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('报价有效期从'),
    width: '200'
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('报价有效期至'),
    width: '200'
  },
  {
    field: 'remark',
    headerText: i18n.t('费用备注'),
    width: '200',
    template: () => {
      return {
        template: Vue.component('priceMark', {
          template: `
          <div style="padding:5px">
              <p v-if="data.doc">doc费用：{{data.doc}}</p>
              <p v-if="data.seal">seal费用：{{data.seal}}</p>
              <p v-if="data.eir">eir费用：{{data.eir}}</p>
              <p>其他费用：{{data.otherFee}}</p>
          </div>`
        })
      }
    }
  }
]

// 陆运
columnDataLo['4'] = [
  {
    type: 'checkbox',
    width: '50'
  },
  {
    field: 'companyList',
    headerText: i18n.t('公司名称'),
    width: '200',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['companyName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'purchaseAgentName',
    headerText: i18n.t('采购员')
  },
  {
    field: 'logisticsDto.transportType',
    headerText: i18n.t('运输方式'),
    width: '200'
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    width: '200'
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    width: '200'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: '200'
  },
  {
    field: 'siteList',
    headerText: i18n.t('采购组织'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['purOrgName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'logisticsDto.startPlace',
    headerText: i18n.t('始发地'),
    width: '200'
  },
  {
    field: 'logisticsDto.endPlace',
    headerText: i18n.t('目的地'),
    width: '200'
  },
  {
    field: 'logisticsDto.carType',
    headerText: i18n.t('载重车型'),
    width: '200'
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种'),
    width: '200'
  },
  {
    field: 'logisticsDto.landTransportMode',
    headerText: i18n.t('陆运模式'),
    width: '200'
  },
  {
    field: 'logisticsDto.transportEffective',
    headerText: i18n.t('运输时效'),
    width: '200'
  },
  {
    field: 'logisticsDto.loadingUnloadingMode',
    headerText: i18n.t('装卸方式'),
    width: '200'
  },
  {
    field: 'logisticsDto.completeVehiclePrice',
    headerText: i18n.t('整车总价（假设1天压夜）'),
    width: '200'
  },
  {
    field: 'remark',
    headerText: i18n.t('费用备注'),
    width: '200',
    template: () => {
      return {
        template: Vue.component('priceMark', {
          template: `
          <div  style="padding:5px">
              <p v-if="data.doc">doc费用：{{data.doc}}</p>
              <p v-if="data.seal">seal费用：{{data.seal}}</p>
              <p v-if="data.eir">eir费用：{{data.eir}}</p>
              <p>其他费用：{{data.otherFee}}</p>
          </div>`
        })
      }
    }
  }
]
// 铁路
columnDataLo['3'] = [
  {
    type: 'checkbox',
    width: '50'
  },
  {
    field: 'companyList',
    headerText: i18n.t('公司名称'),
    width: '200',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['companyName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'purchaseAgentName',
    headerText: i18n.t('采购员')
  },
  {
    field: 'logisticsDto.transportType',
    headerText: i18n.t('运输方式')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    width: '200'
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    width: '200'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: '200'
  },
  {
    field: 'siteList',
    headerText: i18n.t('采购组织'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['purOrgName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'logisticsDto.startPlace',
    headerText: i18n.t('始发地')
  },
  {
    field: 'logisticsDto.endPlace',
    headerText: i18n.t('目的地')
  },
  {
    field: 'logisticsDto.serviceMode',
    headerText: i18n.t('服务模式'),
    width: '200'
  },
  {
    field: 'logisticsDto.loadingAddress',
    headerText: i18n.t('装货地'),
    width: '200'
  },
  {
    field: 'logisticsDto.expectLoadingTime',
    headerText: i18n.t('期望装货时间'),
    width: '200'
  },
  {
    field: 'logisticsDto.logisticsTerm',
    headerText: i18n.t('贸易条款'),
    width: '200'
  },
  {
    field: 'logisticsDto.lastDeliveryTime',
    headerText: i18n.t('最晚发运时间'),
    width: '200'
  },
  {
    field: 'logisticsDto.expectArriveTime',
    headerText: i18n.t('期望到达时间'),
    width: '200'
  },
  {
    field: 'logisticsDto.specification',
    headerText: i18n.t('集中箱费用'),
    template: () => {
      return {
        template: specifiCation
      }
    }
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种')
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('报价有效期从'),
    width: '200'
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('报价有效期至'),
    width: '200'
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
// 空运
columnDataLo['2'] = [
  {
    type: 'checkbox',
    width: '50'
  },
  {
    field: 'companyList',
    headerText: i18n.t('公司名称'),
    width: '200',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['companyName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'purchaseAgentName',
    headerText: i18n.t('采购员')
  },
  {
    field: 'logisticsDto.transportType',
    headerText: i18n.t('运输方式')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    width: '200'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: '200'
  },
  {
    field: 'siteList',
    headerText: i18n.t('采购组织'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['purOrgName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'logisticsDto.startPort',
    headerText: i18n.t('起运港'),
    width: '200'
  },
  {
    field: 'logisticsDto.endPort',
    headerText: i18n.t('目的港'),
    width: '200'
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品名'),
    width: '200'
  },
  {
    field: 'logisticsDto.goodsFinishTime',
    headerText: i18n.t('货好时间')
  },
  {
    field: 'logisticsDto.logisticsTerm',
    headerText: i18n.t('贸易条款'),
    width: '200'
  },
  {
    field: 'logisticsDto.declareMode',
    headerText: i18n.t('报关方式'),
    width: '200'
  },
  {
    field: 'logisticsDto.arriveAddress',
    headerText: i18n.t('送货地址'),
    width: '200'
  },
  {
    field: 'logisticsDto.specSize',
    headerText: i18n.t('货品规格（尺寸）'),
    width: '200'
  },
  {
    field: 'logisticsDto.specGrossWeight',
    headerText: i18n.t('货品规格（毛重（KG））'),
    width: '300'
  },
  {
    field: 'logisticsDto.specVolume',
    headerText: i18n.t('货品规格（体积（CBM）'),
    width: '300'
  },
  {
    field: 'logisticsDto.specVolumeWeight',
    headerText: i18n.t('货品规格（体积重（KG））'),
    width: '300'
  },
  {
    field: 'logisticsDto.startAirport',
    headerText: i18n.t('起运机场'),
    width: '200'
  },
  {
    field: 'logisticsDto.eta',
    headerText: 'ETA',
    width: '200'
  },
  {
    field: 'logisticsDto.etd',
    headerText: 'ETD',
    width: '200'
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('总计费用'),
    width: '200'
  },
  {
    field: 'remark',
    headerText: i18n.t('费用备注'),
    width: '200',
    template: () => {
      return {
        template: Vue.component('priceMark', {
          template: `
          <div style="padding:5px">
              <p v-if="data.doc">doc费用：{{data.doc}}</p>
              <p v-if="data.seal">seal费用：{{data.seal}}</p>
              <p v-if="data.eir">eir费用：{{data.eir}}</p>
              <p>其他费用：{{data.otherFee}}</p>
          </div>`
        })
      }
    }
  }
]
// 海运
columnDataLo['1'] = [
  {
    type: 'checkbox',
    width: '50'
  },
  {
    field: 'companyList',
    headerText: i18n.t('公司名称'),
    width: '200',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['companyName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'purchaseAgentName',
    headerText: i18n.t('采购员')
  },
  {
    field: 'logisticsDto.transportType',
    headerText: i18n.t('运输方式')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: '200'
  },
  {
    field: 'siteList',
    headerText: i18n.t('采购组织'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['purOrgName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'logisticsDto.startPort',
    headerText: i18n.t('起运港')
  },
  {
    field: 'logisticsDto.endPort',
    headerText: i18n.t('目的港')
  },
  {
    field: 'itemName',
    headerText: i18n.t('品名')
  },
  {
    field: 'logisticsDto.goodsFinishTime',
    headerText: i18n.t('货好时间')
  },
  {
    field: 'logisticsDto.logisticsTerm',
    headerText: i18n.t('贸易条款')
  },
  {
    field: 'logisticsDto.specification',
    headerText: i18n.t('集中箱费用'),
    template: () => {
      return {
        template: specifiCation
      }
    }
  },
  {
    field: 'logisticsDto.eta',
    headerText: i18n.t('船期（ETA）'),
    width: '200'
  },

  {
    field: 'logisticsDto.etd',
    headerText: i18n.t('船期（ETD）'),
    width: '200'
  },

  {
    field: 'logisticsDto.seaTransportFeeTotal',
    headerText: 'USD',
    width: '200'
  },

  {
    field: 'taxedUnitPrice',
    headerText: 'RMB'
  },

  {
    field: 'remark',
    headerText: i18n.t('费用备注'),
    width: '200',
    template: () => {
      return {
        template: Vue.component('priceMark', {
          template: `
          <div style="padding:5px">
              <p v-if="data.logisticsDto.doc">DOC费用：{{data.logisticsDto.doc}}</p>
              <p v-if="data.logisticsDto.seal">SEAL费用：{{data.logisticsDto.seal}}</p>
              <p v-if="data.logisticsDto.eir">EIR费用：{{data.logisticsDto.eir}}</p>
              <p>其他费用：{{data.logisticsDto.otherFee}}</p>
          </div>`
        })
      }
    }
  }
]

export const moldColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: syncStatus
    }
  },
  {
    field: 'syncMsg',
    headerText: i18n.t('同步信息')
  },
  {
    field: 'dieCode',
    headerText: i18n.t('模具编码'),
    searchOptions: {
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'dieName',
    headerText: i18n.t('模具名称')
  },
  {
    field: 'dieType',
    headerText: i18n.t('模具类型'),
    valueConverter: {
      type: 'map',
      map: {
        0: '',
        1: i18n.t('基础模具'),
        2: i18n.t('复制模具'),
        3: i18n.t('基础模改模'),
        4: i18n.t('复制模改模')
      }
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    searchOptions: {
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'companyList',
    headerText: i18n.t('公司名称'),
    width: '200',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['companyName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    field: 'quoteAttribute',
    headerText: i18n.t('报价属性'),
    valueConverter: {
      type: 'map',
      map: quoteAttribute
    }
  },
  {
    field: 'quoteMode',
    headerText: i18n.t('价格生效方式'),
    valueConverter: {
      type: 'map',
      map: quoteMode
    }
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('单价(未税)')
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('单价(含税)')
  },
  {
    field: 'deliveryPlace',
    headerText: i18n.t('直送地')
  },
  {
    field: 'unitCode',
    headerText: i18n.t('基本单位编码')
  },
  {
    field: 'unitName',
    headerText: i18n.t('基本单位名称')
  },
  {
    field: 'purUnitName',
    headerText: i18n.t('订单单位名称')
  },
  {
    field: 'currencyCode',
    headerText: i18n.t('币种编码')
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种')
  },
  {
    field: 'taxCode',
    headerText: i18n.t('税率编码')
  },
  {
    field: 'taxName',
    headerText: i18n.t('税率名称')
  },
  {
    field: 'priceUnit',
    headerText: i18n.t('价格单位'),
    ignore: true
  },
  {
    field: 'priceRecordCode',
    headerText: i18n.t('价格编码'),
    searchOptions: {
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'sourceCode',
    headerText: i18n.t('来源单号'),
    searchOptions: {
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('价格生效日期'),
    type: 'date',
    format: 'yyyy-MM-dd',
    searchOptions: {
      operator: 'equal',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return new Date(e).valueOf()
      }
    }
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('价格失效日期'),
    type: 'date',
    format: 'yyyy-MM-dd',
    searchOptions: {
      operator: 'equal',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return new Date(e).valueOf()
      }
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    type: 'date',
    format: 'yyyy-MM-dd',
    searchOptions: {
      operator: 'equal',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return new Date(e).valueOf()
      }
    }
  }
]
export const itemMoldColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: syncStatus
    }
  },
  {
    field: 'syncMsg',
    headerText: i18n.t('同步信息')
  },
  {
    field: 'sourceCode',
    headerText: i18n.t('来源单号'),
    searchOptions: {
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'priceRecordCode',
    headerText: i18n.t('价格编码'),
    searchOptions: {
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    width: '200',
    searchOptions: {
      operator: 'equal',
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    width: '200'
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    width: '200',
    searchOptions: {
      operator: 'equal',
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    width: '200'
  },

  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    searchOptions: {
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'companyList',
    headerText: i18n.t('公司'),
    width: '200',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['companyName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织')
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂')
  },
  {
    field: 'deliveryPlace',
    headerText: i18n.t('直送地'),
    ignore: true
  },
  {
    field: 'priceUnit',
    headerText: i18n.t('价格单位'),
    ignore: true
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('单价(未税)')
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('单价(含税)')
  },
  {
    field: 'sharePriceUntaxed',
    headerText: i18n.t('分摊后单价(未税)')
  },
  {
    field: 'sharePriceTaxed',
    headerText: i18n.t('分摊后单价(含税)')
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种'),
    width: '200'
  },
  {
    field: 'taxRate',
    headerText: i18n.t('税率')
  },
  {
    field: 'quoteAttribute',
    headerText: i18n.t('报价属性'),
    valueConverter: {
      type: 'map',
      map: quoteAttribute
    }
  },
  {
    field: 'quoteMode',
    headerText: i18n.t('报价生效方式'),
    valueConverter: {
      type: 'map',
      map: quoteMode
    }
  },
  {
    field: 'priceRecordCode',
    headerText: i18n.t('价格编码')
  },
  {
    field: 'dieType',
    headerText: i18n.t('模具类型'),
    valueConverter: {
      type: 'map',
      map: {
        0: '',
        1: i18n.t('基础模具'),
        2: i18n.t('复制模具'),
        3: i18n.t('基础模改模'),
        4: i18n.t('复制模改模')
      }
    },
    ignore: true
  },
  {
    field: 'baseMouldUntaxedPrice',
    headerText: i18n.t('基础模/复制模单价（不含税'),
    ignore: true
  },
  {
    field: 'baseMouldTaxedPrice',
    headerText: i18n.t('基础模/复制模单价（含税'),
    ignore: true
  },
  {
    field: 'baseTaxRate',
    headerText: i18n.t('基础模/复制模税率'),
    ignore: true
  },
  {
    field: 'basePlanQuantity',
    headerText: i18n.t('基础模/复制模规划量'),
    ignore: true
  },
  {
    field: 'baseShareQuantity',
    headerText: i18n.t('基础模/复制模实际分摊量'),
    ignore: true
  },
  {
    field: 'baseSharePriceUntaxed',
    headerText: i18n.t('基础模/复制模实际分摊价（未税）'),
    ignore: true
  },
  {
    field: 'basePlanSharePriceUntaxed',
    headerText: i18n.t('基础模/复制模规划分摊价（未税）'),
    ignore: true
  },
  {
    field: 'modifyMouldUntaxedPrice',
    headerText: i18n.t('改模单价（未税）'),
    ignore: true
  },
  {
    field: 'modifyMouldTaxedPrice',
    headerText: i18n.t('改模单价（含税）'),
    ignore: true
  },
  {
    field: 'modifyTaxRate',
    headerText: i18n.t('改模税率'),
    ignore: true
  },
  {
    field: 'modifyPlanQuantity',
    headerText: i18n.t('改模规划量'),
    ignore: true
  },
  {
    field: 'modifyShareQuantity',
    headerText: i18n.t('改模实际分摊量'),
    ignore: true
  },
  {
    field: 'modifySharePriceUntaxed',
    headerText: i18n.t('改模实际分摊价（未税）'),
    ignore: true
  },
  {
    field: 'modifyPlanSharePriceUntaxed',
    headerText: i18n.t('改模规划分摊价（未税）'),
    ignore: true
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('生效日期'),
    type: 'date',
    format: 'yyyy-MM-dd',
    searchOptions: {
      operator: 'equal',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return new Date(e).valueOf()
      }
    }
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('失效日期'),
    type: 'date',
    format: 'yyyy-MM-dd',
    searchOptions: {
      operator: 'equal',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return new Date(e).valueOf()
      }
    }
  },
  {
    field: 'purchaseAgentName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    type: 'date',
    format: 'yyyy-MM-dd',
    searchOptions: {
      operator: 'equal',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return new Date(e).valueOf()
      }
    }
  }
]

// 结构件 美工件 columnData
export const structureAndArtColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: syncStatus
    }
  },
  {
    field: 'syncMsg',
    headerText: i18n.t('同步信息')
  },
  {
    field: 'priceValueType',
    headerText: i18n.t('价格类别'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('基价'),
        2: i18n.t('SRM价'),
        3: i18n.t('暂估价格'),
        4: i18n.t('执行价')
      }
    }
  },
  {
    field: 'priceType',
    headerText: i18n.t('价格类型'),
    ignore: true,
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('到物料'),
        1: i18n.t('到SKU'),
        2: i18n.t('到品类'),
        3: i18n.t('到供应商'),
        4: i18n.t('业务单号'),
        5: i18n.t('行情因子'),
        6: i18n.t('物流'),
        8: i18n.t('模具'),
        10: i18n.t('结构阶梯'),
        11: i18n.t('美工阶梯')
      }
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    searchOptions: {
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    width: '200',
    searchOptions: {
      operator: 'equal',
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    width: '200'
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    width: '150',
    searchOptions: {
      operator: 'equal',
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: '200'
  },
  {
    field: 'companyList',
    headerText: i18n.t('公司名称'),
    width: '200',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (Array.isArray(e) && e.length) {
          return e[0]['companyName']
        } else {
          return '-'
        }
      }
    }
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织'),
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.siteList[0]['purOrgName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂')
  },
  {
    field: 'stepValue',
    headerText: i18n.t('阶梯数量')
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('单价(未税)')
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('单价(含税)')
  },
  {
    field: 'unitCode',
    headerText: i18n.t('基本单位编码')
  },
  // {
  //   field: 'purUnitName',
  //   headerText: i18n.t('基本单位名称')
  // },
  {
    field: 'purUnitCode',
    headerText: i18n.t('订单单位编码')
  },
  {
    field: 'deliveryPlace',
    headerText: i18n.t('直送地')
  },
  {
    field: 'priceUnit',
    headerText: i18n.t('价格单位'),
    ignore: true
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种')
  },
  {
    field: 'taxName',
    headerText: i18n.t('税率名称')
  },
  {
    field: 'quoteAttribute',
    headerText: i18n.t('报价属性'),
    valueConverter: {
      type: 'map',
      map: quoteAttribute
    }
  },
  {
    field: 'quoteMode',
    headerText: i18n.t('报价生效方式'),
    valueConverter: {
      type: 'map',
      map: quoteMode
    }
  },
  {
    field: 'leadTime',
    headerText: 'L/T',
    ignore: true
  },
  {
    field: 'unconditionalLeadTime',
    headerText: i18n.t('无条件L/T'),
    ignore: true
  },
  {
    field: 'minPurchaseQuantity',
    headerText: i18n.t('最小采购量')
  },
  {
    field: 'minPackageQuantity',
    headerText: i18n.t('最小包装量')
  },

  {
    field: 'validStartTime',
    headerText: i18n.t('生效日期'),
    type: 'date',
    format: 'yyyy-MM-dd',
    searchOptions: {
      operator: 'equal',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return new Date(e).valueOf()
      }
    }
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('失效日期'),
    type: 'date',
    format: 'yyyy-MM-dd',
    searchOptions: {
      operator: 'equal',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return new Date(e).valueOf()
      }
    }
  },
  {
    field: 'sourceCode',
    headerText: i18n.t('来源单号'),
    width: 200,
    searchOptions: {
      operator: 'equal',
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'priceRecordCode',
    headerText: i18n.t('价格编码'),
    searchOptions: {
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'purchaseAgentName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    type: 'date',
    format: 'yyyy-MM-dd',
    searchOptions: {
      operator: 'equal',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return new Date(e).valueOf()
      }
    }
  }
]

export const columnDataDialogThc = [
  {
    field: 'specification',
    headerText: i18n.t('规格')
  },
  {
    field: 'requiredQuantity',
    headerText: i18n.t('需求数量')
  },
  {
    field: 'price',
    headerText: i18n.t('海运费')
  },
  {
    field: 'thc',
    headerText: 'THC'
  }
]

export const columnDataDialogQua = [
  {
    field: 'specification',
    headerText: i18n.t('规格')
  },
  {
    field: 'requiredQuantity',
    headerText: i18n.t('需求数量')
  },
  {
    field: 'containerQuantity',
    headerText: i18n.t('可提供柜量')
  },
  {
    field: 'price',
    headerText: i18n.t('报价单价')
  }
]
const gridIdMap = {}
gridIdMap['1'] = permission.gridId['purchase']['price']['logistics_sea'] //海运
gridIdMap['2'] = permission.gridId['purchase']['price']['logistics_air'] //空运
gridIdMap['3'] = permission.gridId['purchase']['price']['logistics_railway'] //铁路
gridIdMap['4'] = permission.gridId['purchase']['price']['logistics_land'] //陆运
gridIdMap['5'] = permission.gridId['purchase']['price']['logistics_trailer'] //拖车
export const pageConfigLoFn = (i, str) => {
  return [
    {
      useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
      toolbar: { useBaseConfig: false, tools: [[], ['Refresh']] },
      gridId: gridIdMap[i],
      grid: {
        allowFiltering: true,
        columnData: columnDataLo[i],
        asyncConfig: {
          url: APIS.APIS.getLogicRecords,
          params: {
            logisticsType: str
          }
        }
      }
    }
  ]
}

export const pageConfigStructureAndArt = (type) => {
  return [
    {
      useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
      toolbar: type === 'structure' ? [] : toolbarCom,
      gridId: permission.gridId['purchase']['price'][type],
      grid: {
        allowFiltering: true,
        columnData: structureAndArtColumnData,
        asyncConfig: {
          url: APIS.APIS.getPriceRecordsNew,
          params: {
            queryCode: 'batchCodeQuery'
          },
          defaultRules: [
            {
              label: '',
              field: 'priceType',
              type: 'string',
              operator: 'equal',
              value: type === 'structure' ? 10 : 11
            }
          ],
          serializeList: (list) => {
            list.forEach((item) => {
              if (item.siteList) {
                item.companyName = item.siteList[0].companyName
                item.siteName = item.siteList[0].siteName
                item.purOrgName = item.siteList[0].purOrgName
              }
            })
            return list
          }
        }
      }
    }
  ]
}

// 外发阶梯
export const columnDataWf = [
  {
    width: 50,
    type: 'checkbox'
  },
  {
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: syncStatus
    }
  },
  {
    field: 'syncMsg',
    headerText: i18n.t('同步信息')
  },
  {
    field: 'priceRecordCode',
    headerText: i18n.t('价格编码'),
    cssClass: 'field-content',
    searchOptions: {
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    searchOptions: {
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料描述')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    searchOptions: {
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商描述')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称')
    // valueConverter: {
    //   type: 'function',
    //   filter: (e) => {
    //     if (Array.isArray(e) && e.length) {
    //       return e[0]['companyName']
    //     } else {
    //       return '-'
    //     }
    //   }
    // }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织名称')
    // valueAccessor: (field, data) => {
    //   if (Array.isArray(data.siteList) && data.siteList.length) {
    //     return data.siteList[0]['purOrgName']
    //   } else {
    //     return '-'
    //   }
    // }
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('草稿'), 1: i18n.t('启用'), 2: i18n.t('禁用'), 3: i18n.t('未合格') }
    }
  },
  {
    field: 'quoteAttribute',
    headerText: i18n.t('报价属性'),
    valueConverter: {
      type: 'map',
      map: quoteAttribute
    }
  },
  {
    field: 'quoteMode',
    headerText: i18n.t('价格生效方式'),
    valueConverter: {
      type: 'map',
      map: quoteMode
    }
  },
  {
    field: 'deliveryPlace',
    headerText: i18n.t('直送地'),
    ignore: true
  },
  {
    field: 'stepValue',
    headerText: i18n.t('阶梯数量')
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('单价(未税)')
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('单价(含税)')
  },
  {
    field: 'unitName',
    headerText: i18n.t('基本单位')
  },
  {
    field: 'purUnitName',
    headerText: i18n.t('订单单位')
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种')
  },
  {
    field: 'taxCode',
    headerText: i18n.t('税码')
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('生效日期'),
    type: 'date',
    format: 'yyyy-MM-dd',
    searchOptions: {
      operator: 'greaterthanorequal',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return new Date(e).valueOf()
      }
    }
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('失效日期'),
    type: 'date',
    format: 'yyyy-MM-dd',
    searchOptions: {
      operator: 'lessthanorequal',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return new Date(e).valueOf()
      }
    }
  },
  {
    field: 'sourceCode',
    headerText: i18n.t('来源单号'),
    searchOptions: {
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'purchaseAgentName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    type: 'date',
    format: 'yyyy-MM-dd',
    searchOptions: {
      operator: 'greaterthanorequal',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return new Date(e).valueOf()
      }
    }
  },
  {
    field: 'purchaseInfoRecordNo',
    headerText: i18n.t('外发信息记录编号')
  },
  {
    field: 'innerCounter',
    headerText: i18n.t('序列号'),
    ignore: true
  }
]
