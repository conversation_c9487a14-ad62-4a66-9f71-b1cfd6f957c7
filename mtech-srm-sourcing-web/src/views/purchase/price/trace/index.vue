<!--价格追溯-->
<template>
  <div class="full-height">
    <!-- :hidden-tabs="true" -->
    <mt-template-page
      ref="tepPage"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>
  </div>
</template>

<script>
import { pageConfig } from './config/index'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  data() {
    return {
      pageConfig: pageConfig
    }
  },
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      const _selectRows = e.grid.getSelectedRecords()
      if (_selectRows.length <= 0 && e.toolbar.id == 'SAP') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'SAP') {
        this.handleClickToolBarSAP(_selectRows)
      }
      if (e.toolbar.id == 'download') {
        this.handleClickToolBarDownload()
      }
    },
    //推送SAP
    handleClickToolBarSAP(_selectRows) {
      let arr = []
      arr = _selectRows.filter((item) => {
        return item.syncSAPStatus != '1'
      })
      if (arr.length < 1) {
        this.$toast({ content: this.$t('请勿重复推送SAP'), type: 'warning' })
        return
      }
      let ids = arr.map((item) => item.id)
      this.$API.priceService.syncSap(ids).then((res) => {
        if (res.data == 'Update Success') {
          this.$refs.tepPage.refreshCurrentGridData()
          this.$toast({
            content: this.$t('推送成功'),
            type: 'success'
          })
        } else if (res.data == 'Update Fail') {
          this.$refs.tepPage.refreshCurrentGridData()
          this.$toast({ content: this.$t('推送失败'), type: 'warning' })
        }
      })
    },
    //导出
    handleClickToolBarDownload() {
      let params = { current: 1, size: 20 }
      this.$API.priceService.traceExport(params).then((res) => {
        this.$toast({
          type: 'success',
          content: this.$t('正在导出，请稍后！')
        })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      })
    }
  }
}
</script>
<style lang="scss" scoped></style>
