import { i18n } from '@/main.js'
export const toolbar = [
  { id: 'SAP', icon: 'icon_solid_Createorder', title: i18n.t('推送SAP') },
  { id: 'download', icon: 'icon_solid_upload', title: i18n.t('导出') }
]

const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'syncSAPStatus',
    headerText: i18n.t('推送SAP状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('待处理'),
        1: i18n.t('已同步SAP')
      }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },

  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    // width: "200",
    headerText: i18n.t('物料描述')
  },
  {
    field: 'ext2',
    // width: "200",
    headerText: i18n.t('追溯开始日期')
  },
  {
    field: 'ext3',
    headerText: i18n.t('追溯截止日期')
  },
  {
    field: 'year',
    headerText: i18n.t('核算年')
  },
  {
    field: 'month',
    headerText: i18n.t('核算月')
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织')
  },
  {
    field: 'purOrgCode',
    headerText: i18n.t('采购组织编码')
  },
  {
    field: 'quoteAttribute',
    headerText: i18n.t('报价属性')
  },
  {
    field: 'quoteMode',
    headerText: i18n.t('价格生效方式')
  },
  {
    field: 'deliveryPlace',
    headerText: i18n.t('直送地')
  },
  {
    field: 'quoteTime',
    headerText: i18n.t('价格生效日期')
  },
  {
    field: 'createTime',
    headerText: i18n.t('追溯创建日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (isNaN(parseInt(e))) {
          return '--'
        } else {
          let date = new Date(Number(e))
          let year = date.getFullYear()
          let month = date.getMonth() + 1
          let day = date.getDate()
          month = month < 10 ? '0' + month : month
          day = day < 10 ? '0' + day : day
          return year + '-' + month + '-' + day
        }
      }
    }
  },
  {
    field: 'auditDate',
    headerText: i18n.t('SAP审核通过日期')
  },
  {
    field: 'tracePrice',
    headerText: i18n.t('追溯价格')
  },
  {
    field: 'lastPrice',
    headerText: i18n.t('上次价格')
  }
]
export const pageConfig = [
  {
    toolbar,
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      allowFiltering: true,
      frozenColumns: 3,
      columnData,
      asyncConfig: {
        url: '/price/price/trace/list',
        params: {}
      }
    }
  }
]
