import { i18n, permission } from '@/main.js'

const columnData = [
  {
    field: 'screenCode',
    headerText: i18n.t('TCL编码'),
    width: 120
  },
  {
    field: 'spec',
    headerText: i18n.t('规格'),
    width: 100
  },
  {
    field: 'qty',
    headerText: i18n.t('数量(片)'),
    width: 100
  },
  {
    field: 'screenPrice',
    headerText: i18n.t('销售金品的不含税报价'),
    width: 150
  },
  {
    field: 'screenPriceTaxed',
    headerText: i18n.t('销售金品的含税报价'),
    width: 150
  },
  {
    field: 'totalPriceTaxed',
    headerText: i18n.t('金额RMB(含税13%)'),
    width: 150
  }
]
export const pageConfig = (that) => {
  return [
    {
      gridId: permission.gridId['purchase']['lcdScreenSaleAgreement'],
      isUseCustomSearch: true,
      isCustomSearchRules: true,
      useToolTemplate: false,
      toolbar: {
        useBaseConfig: false,
        tools: [
          [
            { id: 'xsPrint', icon: 'icon_solid_Createorder', title: i18n.t('屏销售协议打印') },
            { id: 'dzPrint', icon: 'icon_solid_export', title: i18n.t('抵账协议打印') }
          ],
          ['Refresh', 'Setting']
        ]
      },
      grid: {
        columnData,
        asyncConfig: {
          url: that.$API.priceService.getLcdScreenSaleAgreementList,
          afterAsyncData: () => {
            that.searchFormModel.opearatorType = null
          }
        }
      }
    }
  ]
}
