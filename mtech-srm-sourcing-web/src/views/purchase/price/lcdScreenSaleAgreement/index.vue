<template>
  <div class="full-height">
    <mt-local-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="approveDate" :label="$t('记账日期')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.approveDate"
                :allow-edit="false"
                :placeholder="$t('请选择记账日期')"
                @change="handleApproveDateChange"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-local-template-page>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { pageConfig } from './config/index'
// 引入本地的emplate-page组件
import MtLocalTemplatePage from '@/components/template-page'

export default {
  components: {
    MtLocalTemplatePage
  },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {
        opearatorType: '0'
      },
      pageConfig: pageConfig(this)
    }
  },
  methods: {
    // 日期格式化
    handleApproveDateChange(e) {
      if (e.startDate) {
        this.searchFormModel['startDate'] = dayjs(e.startDate).format('YYYYMMDD')
        this.searchFormModel['endDate'] = dayjs(e.endDate).format('YYYYMMDD')
      } else {
        this.searchFormModel['startDate'] = null
        this.searchFormModel['endDate'] = null
      }
      this.searchFormModel.opearatorType = '0'
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    // 点击按钮工具栏
    handleClickToolBar(e) {
      const { toolbar } = e
      if (toolbar.id === 'xsPrint') {
        this.handlePrint('xs')
      } else if (toolbar.id === 'dzPrint') {
        this.handlePrint('dz')
      }
    },
    // xs-屏销售协议打印 dz-抵账协议打印
    async handlePrint(type) {
      const page = this.$refs.templateRef.getCurrentUsefulRef().ejsRef.pageSettings
      const params = {
        ...this.searchFormModel,
        page: {
          current: page.currentPage,
          size: page.pageSize
        }
      }
      const _this = this
      const res = await this.$API.priceService[type + 'AgreementPrint'](params)
      // 处理异常
      if (res?.data?.type === 'application/json') {
        const reader = new FileReader()
        reader.readAsText(res?.data, 'utf-8')
        reader.onload = function () {
          const readerRes = reader.result
          const resObj = JSON.parse(readerRes)
          _this.$toast({
            content: resObj.msg,
            type: 'error'
          })
        }
        return
      }
      const pdfUrl = window.URL.createObjectURL(new Blob([res.data], { type: 'application/pdf' }))
      const date = new Date().getTime()
      const ifr = document.createElement('iframe')
      ifr.style.frameborder = 'no'
      ifr.style.display = 'none'
      ifr.style.pageBreakBefore = 'always'
      ifr.setAttribute('id', 'printPdf' + date)
      ifr.setAttribute('name', 'printPdf' + date)
      ifr.src = pdfUrl
      document.body.appendChild(ifr)
      this.doPrint('printPdf' + date)
      window.URL.revokeObjectURL(ifr.src)
    },
    // 打印
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
      }, 100)
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  /deep/.e-headercelldiv {
    padding: 0 0.6em 0 0.6em !important;
  }
}
</style>
