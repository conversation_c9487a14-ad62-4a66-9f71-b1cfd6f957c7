<template>
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.status"
            :data-source="logisticsSeaStatusList"
            :show-clear-button="true"
            :placeholder="$t('请选择状态')"
          />
        </mt-form-item>
        <mt-form-item prop="priceRecordCode" :label="$t('价格记录编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.priceRecordCode"
            :show-clear-button="true"
            :placeholder="$t('请输入价格记录编码')"
          />
        </mt-form-item>
        <mt-form-item prop="sourceCode" :label="$t('询价单号')" label-style="top">
          <mt-input
            v-model="searchFormModel.sourceCode"
            :show-clear-button="true"
            :placeholder="$t('请输入询价单号')"
          />
        </mt-form-item>
        <mt-form-item prop="sourceName" :label="$t('单据名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.sourceName"
            :show-clear-button="true"
            :placeholder="$t('单据名称')"
          />
        </mt-form-item>
        <mt-form-item prop="syncStatus" :label="$t('同步状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.syncStatus"
            :data-source="syncStatusList"
            :show-clear-button="true"
            :placeholder="$t('请选择同步状态')"
          />
        </mt-form-item>

        <mt-form-item prop="companyCodeList" :label="$t('公司编码')" label-style="top">
          <custom-select
            v-model="searchFormModel.companyCodeList"
            :label-name="$t('公司编码')"
            :placeholder="$t('请输入公司编码')"
          />
        </mt-form-item>
        <!-- <mt-form-item prop="companyName" :label="$t('公司名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.companyName"
            :show-clear-button="true"
            :placeholder="$t('请输入公司名称')"
          />
        </mt-form-item> -->
        <mt-form-item prop="factoryCodeList" :label="$t('品类编码')" label-style="top">
          <custom-select
            v-model="searchFormModel.categoryCodeList"
            :label-name="$t('品类编码')"
            :placeholder="$t('请输入品类编码')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierCodeList" :label="$t('供应商编码')" label-style="top">
          <custom-select
            v-model="searchFormModel.supplierCodeList"
            :label-name="$t('供应商编码')"
            :placeholder="$t('请输入供应商编码')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.supplierName"
            :show-clear-button="true"
            :placeholder="$t('请输入供应商名称')"
          />
        </mt-form-item>

        <mt-form-item prop="validStartTime" :label="$t('价格生效日期')">
          <mt-date-picker
            v-model="searchFormModel.validStartTime"
            :open-on-focus="true"
            :allow-edit="false"
            time-stamp
            format="yyyy-MM-dd"
            :placeholder="$t('请选择生效日期')"
            @change="(e) => handleDateChange('validStartTime', e)"
          />
        </mt-form-item>
        <mt-form-item prop="validEndTime" :label="$t('价格失效日期')">
          <mt-date-picker
            v-model="searchFormModel.validEndTime"
            :open-on-focus="true"
            :allow-edit="false"
            time-stamp
            format="yyyy-MM-dd"
            :placeholder="$t('请选择价格失效日期')"
            @change="(e) => handleDateChange('validEndTime', e)"
          />
        </mt-form-item>

        <mt-form-item prop="deliverAddr" :label="$t('发货站')" label-style="top">
          <mt-input
            v-model="searchFormModel.deliverAddr"
            :show-clear-button="true"
            :placeholder="$t('请输入发货站')"
          />
        </mt-form-item>
        <mt-form-item prop="productline" :label="$t('产品线')" label-style="top">
          <mt-input
            v-model="searchFormModel.productline"
            :show-clear-button="true"
            :placeholder="$t('请输入产品线')"
          />
        </mt-form-item>
        <mt-form-item prop="routeName" :label="$t('线路')" label-style="top">
          <mt-input
            v-model="searchFormModel.routeName"
            :show-clear-button="true"
            :placeholder="$t('请输入线路')"
          />
        </mt-form-item>
        <mt-form-item prop="destProvinceName" :label="$t('目的地省')" label-style="top">
          <mt-input
            v-model="searchFormModel.destProvinceName"
            :show-clear-button="true"
            :placeholder="$t('请输入目的地省')"
          />
        </mt-form-item>
        <mt-form-item prop="destCityName" :label="$t('目的地市')" label-style="top">
          <mt-input
            v-model="searchFormModel.destCityName"
            :show-clear-button="true"
            :placeholder="$t('请输入目的地市')"
          />
        </mt-form-item>
        <mt-form-item prop="destAreaName" :label="$t('目的地区/县')" label-style="top">
          <mt-input
            v-model="searchFormModel.destAreaName"
            :show-clear-button="true"
            :placeholder="$t('请输入目的地区/县')"
          />
        </mt-form-item>
        <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
          <mt-input
            v-model="searchFormModel.createUserName"
            :show-clear-button="true"
            :placeholder="$t('请输入创建人')"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :allow-edit="false"
            :placeholder="$t('请选择创建时间')"
            :open-on-focus="true"
            @change="handleDateChange"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      grid-id="42d30a57-54a8-41d0-92ed-e5d7c324e7cd"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="listColumns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
// import { getTimeList } from '@/utils/obj'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import {
  logisticsToolbar,
  syncStatusList,
  logisticsSeaStatusList,
  requirementTypeList
} from './config/index'
import XEUtils from 'xe-utils'
import CustomSelect from '@/components/customSelect'
export default {
  components: {
    ScTable,
    CollapseSearch,
    CustomSelect
  },
  mixins: [mixin],
  data() {
    return {
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 20,
        pageSizes: [20, 50, 200, 500, 1000, 2000]
      },
      pageInfo: {
        size: 20,
        current: 1
      },
      syncStatusList,
      logisticsSeaStatusList,
      requirementTypeList,
      toolbar: logisticsToolbar,
      searchFormModel: {
        createUserName: JSON.parse(sessionStorage.getItem('userInfo'))?.username || null
        // createTime: getTimeList(3)
      },
      tableData: [],
      loading: false,
      type: 'list',
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.cancel,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 使用组件中的toolbar配置
          toolbar: [],
          grid: {
            columnData: [
              {
                width: '150',
                field: 'fieldCode',
                headerText: this.$t('月份')
              },
              {
                width: '150',
                field: 'fieldData',
                headerText: this.$t('柜量')
              }
            ],
            dataSource: [],
            allowPaging: false,
            asyncConfig: {}
          }
        }
      ]
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    // 列配置
    listColumns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          width: 80,
          field: 'index',
          title: this.$t('序号')
        },
        {
          field: 'status',
          title: this.$t('状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = this.logisticsSeaStatusList.find(
                (item) => item.value === row.status
              )
              const status = selectItem?.text
              return [<div>{status}</div>]
            }
          }
        },
        {
          field: 'priceRecordCode',
          title: this.$t('价格记录编码'),
          minWidth: 120
        },
        {
          field: 'syncStatus',
          title: this.$t('同步状态'),
          minWidth: 100,
          slots: {
            default: ({ row }) => {
              const selectItem = this.syncStatusList.find((item) => item.value === row.syncStatus)
              const syncStatus = selectItem?.text
              return [<div>{syncStatus}</div>]
            }
          }
        },
        {
          field: 'syncInfo',
          title: this.$t('同步信息'),
          minWidth: 100
        },
        {
          field: 'sourceCode',
          title: this.$t('询价单号'),
          minWidth: 180,
          slots: {
            default: ({ row, column }) => {
              return [
                <a on-click={() => this.handleClickCellTitle(row, column)}>{row.sourceCode}</a>
              ]
            }
          }
        },
        {
          field: 'sourceName',
          title: this.$t('单据名称'),
          minWidth: 140
        },
        {
          field: 'companyCode',
          title: this.$t('公司'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [<span>{row.companyCode + '-' + row.companyName}</span>]
            }
          }
        },
        {
          field: 'deliverAddr',
          title: this.$t('发货站'),
          minWidth: 120
        },
        {
          field: 'productline',
          title: this.$t('产品线'),
          minWidth: 120
        },
        {
          field: 'routeName',
          title: this.$t('线路'),
          minWidth: 120
        },
        {
          field: 'destProvinceName',
          title: this.$t('目的地省'),
          minWidth: 120
        },
        {
          field: 'destCityName',
          title: this.$t('目的地市'),
          minWidth: 120
        },
        {
          field: 'destAreaName',
          title: this.$t('目的地区/县'),
          minWidth: 120
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商编码'),
          minWidth: 120
        },
        {
          minWidth: 120,
          field: 'supplierName',
          title: this.$t('供应商名称')
        },
        {
          field: 'validStartTime',
          title: this.$t('价格开始日期'),
          minWidth: 140
        },
        {
          field: 'validEndTime',
          title: this.$t('价格结束日期'),
          minWidth: 140
        },
        {
          field: 'untaxedPriceOne',
          title: this.$t('阶梯单价1'),
          minWidth: 120
        },
        {
          field: 'untaxedPriceTwo',
          title: this.$t('阶梯单价2'),
          minWidth: 120
        },
        {
          field: 'untaxedPriceThree',
          title: this.$t('阶梯单价3'),
          minWidth: 120
        },
        {
          field: 'untaxedPriceFour',
          title: this.$t('阶梯单价4'),
          minWidth: 120
        },
        {
          field: 'untaxedPriceFive',
          title: this.$t('阶梯单价5'),
          minWidth: 120
        },
        {
          field: 'untaxedPriceSix',
          title: this.$t('阶梯单价6'),
          minWidth: 120
        },
        {
          field: 'untaxedPriceSeven',
          title: this.$t('阶梯单价7'),
          minWidth: 120
        },
        {
          field: 'untaxedPriceEight',
          title: this.$t('阶梯单价8'),
          minWidth: 120
        },
        {
          field: 'untaxedPriceNine',
          title: this.$t('阶梯单价9'),
          minWidth: 120
        },
        {
          field: 'untaxedPriceTen',
          title: this.$t('阶梯单价10'),
          minWidth: 120
        },
        {
          field: 'totalQty',
          title: this.$t('汇总数量'),
          minWidth: 140
        },
        {
          field: 'untaxedTotalPrice',
          title: this.$t('总价（未税）'),
          minWidth: 140
        },
        {
          field: 'taxedTotalPrice',
          title: this.$t('总价（含税）'),
          minWidth: 140
        },
        {
          field: 'currencyCode',
          title: this.$t('币种'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [<span>{row.currencyCode + '-' + row.currencyName}</span>]
            }
          }
        },
        {
          field: 'taxRateCode',
          title: this.$t('税率'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [<span>{row.taxRateCode + '-' + row.taxRateName}</span>]
            }
          }
        },
        {
          field: 'requirementType',
          title: this.$t('需求类型'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = this.requirementTypeList.find(
                (item) => item.value === row.requirementType
              )
              const requirementType = selectItem?.text
              return [<div>{requirementType}</div>]
            }
          }
        },
        {
          field: 'logisticsMethodName',
          title: this.$t('运输方式'),
          minWidth: 120
        },

        {
          field: 'categoryCode',
          title: this.$t('品类编码'),
          minWidth: 120
        },
        {
          field: 'categoryName',
          title: this.$t('品类名称')
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          minWidth: 140
        }
      ]
    }
  },
  mounted() {
    this.handleSearch()
  },
  activated() {
    this.handleSearch()
  },
  methods: {
    // 日期格式化
    handleDateChange(e) {
      if (e.startDate) {
        const startDate = XEUtils.toDateString(e.startDate, 'yyyy-MM-dd') + ' 00:00:00'
        const endDate = XEUtils.toDateString(e.endDate, 'yyyy-MM-dd') + ' 23:59:59'
        this.searchFormModel['createStartTime'] = XEUtils.timestamp(startDate)
        this.searchFormModel['createEndTime'] = XEUtils.timestamp(endDate)
      } else {
        this.searchFormModel['createStartTime'] = null
        this.searchFormModel['createEndTime'] = null
      }
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    // 查询
    async handleSearch(type) {
      // 解决修改查询条件后，查询结果有数据但未正常显示的问题（type: pageChange/refresh）
      if (!type) {
        this.pageInfo.current = 1
        this.$refs.pageRef.jumpNum = 1
        this.$refs.pageRef.currentPage = 1
      }
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      delete params.submitTime
      this.loading = true
      const res = await this.$API.priceService
        .getTrunkAnnualPricingRecords(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.tableData?.forEach((item, index) => {
          item.index = index + 1
        }) || []
        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (['syncTms'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      switch (e.code) {
        case 'export':
          this.handleExport()
          break
        case 'syncTms':
          this.syncTms(selectedRecords)
          break
        default:
          break
      }
    },

    // 导出
    async handleExport() {
      const params = {
        page: { current: 1, size: 10000 },
        ...this.searchFormModel
      }
      const res = await this.$API.priceService.exportTrunkAnnual(params)
      if (res.data) {
        this.$toast({
          type: 'success',
          content: this.$t('正在导出，请稍后！')
        })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      }
    },
    // 同步TMS
    async syncTms(list) {
      const ids = list.map((i) => i.id)
      const res = await this.$API.priceService.trunkAnnualSyncTMS({ ids })
      if (res.code === 200) {
        this.$toast({ content: res.msg, type: 'success' })
        this.handleSearch()
      }
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.handleSearch('pageChange')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.handleSearch()
    },
    // 显示柜量明细
    showFiledVolist(data) {
      this.$refs.filedVolistRef.ejsRef.show()
      this.$set(this.pageConfig[0].grid, 'dataSource', data?.cabinetQuantity || [])
    },
    cancel() {
      this.$refs.filedVolistRef.ejsRef.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
