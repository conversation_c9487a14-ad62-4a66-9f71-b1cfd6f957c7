import { i18n } from '@/main.js'
import historyes from '@/views/purchase/price/list/components/history/index.vue'
import priceHistory from '@/views/purchase/price/list/components/priceHistory/index.vue'

// 同步状态
export const syncStatusList = [
  { value: 0, text: i18n.t('无需同步'), cssClass: 'title-#9baac1' },
  { value: 1, text: i18n.t('未同步'), cssClass: 'title-#6386c1' },
  { value: 2, text: i18n.t('同步中'), cssClass: 'title-#6386c1' },
  { value: 3, text: i18n.t('同步成功'), cssClass: 'title-#6386c1' },
  { value: 4, text: i18n.t('同步失败'), cssClass: 'title-#9baac1' }
]
// 需求类型
export const requirementTypeList = [
  { value: '0', text: i18n.t('年约需求'), cssClass: 'title-#9baac1' },
  { value: '1', text: i18n.t('滚动需求'), cssClass: 'title-#6386c1' },
  { value: '2', text: i18n.t('临时需求'), cssClass: 'title-#6386c1' }
]

// smt
export const ckdFlagList = [
  { value: 0, text: i18n.t('未同步'), cssClass: 'title-#6386c1' },
  { value: 1, text: i18n.t('同步中'), cssClass: 'title-#6386c1' },
  { value: 2, text: i18n.t('同步成功'), cssClass: 'title-#6386c1' },
  { value: 3, text: i18n.t('同步失败'), cssClass: 'title-#9baac1' }
]
// odmin操作按钮
export const listToolbar = [
  { code: 'importSAP', name: i18n.t('导入SAP'), status: 'info' },
  { code: 'export', name: i18n.t('导出'), status: 'info' }
]

// 物流
export const logisticsToolbar = [
  { code: 'syncTms', name: i18n.t('同步至TMS'), status: 'info' },
  { code: 'export', name: i18n.t('导出'), status: 'info' }
]

// 报价属性
export const quoteAttributeList = [
  { value: 'mailing_price', text: i18n.t('寄售价'), cssClass: '' },
  { value: 'standard_price', text: i18n.t('标准价'), cssClass: '' },
  { value: 'outsource', text: i18n.t('委外价'), cssClass: '' }
]
// 价格生效方式
export const quoteModeList = [
  { value: 'in_warehouse', text: i18n.t('按照入库'), cssClass: '' },
  { value: 'out_warehouse', text: i18n.t('按出库'), cssClass: '' },
  { value: 'order_date', text: i18n.t('按订单日期'), cssClass: '' }
]
// 价格类别
export const priceCategoryList = [
  { value: 1, text: i18n.t('基价'), cssClass: '' },
  { value: 2, text: i18n.t('SRM价'), cssClass: '' },
  { value: 3, text: i18n.t('暂估价格'), cssClass: '' },
  { value: 4, text: i18n.t('执行价'), cssClass: '' }
]
// 价格类型
export const priceTypeList = [
  { value: 0, text: i18n.t('到物料'), cssClass: '' },
  { value: 1, text: i18n.t('到SKU'), cssClass: '' },
  { value: 2, text: i18n.t('到品类'), cssClass: '' },
  { value: 3, text: i18n.t('到供应商'), cssClass: '' },
  { value: 4, text: i18n.t('业务单号'), cssClass: '' },
  { value: 5, text: i18n.t('成本因子'), cssClass: '' },
  { value: 6, text: i18n.t('物流'), cssClass: '' },
  { value: 8, text: i18n.t('模具'), cssClass: '' },
  { value: 10, text: i18n.t('结构阶梯'), cssClass: '' },
  { value: 11, text: i18n.t('美工阶梯'), cssClass: '' }
]
// 状态
export const statusList = [
  { value: 0, text: i18n.t('草稿'), cssClass: '' },
  { value: 1, text: i18n.t('启用'), cssClass: '' },
  { value: 2, text: i18n.t('禁用'), cssClass: '' },
  { value: 3, text: i18n.t('未合格'), cssClass: '' }
]
// 物流海运状态
export const logisticsSeaStatusList = [
  { value: 0, text: i18n.t('草稿'), cssClass: '' },
  { value: 1, text: i18n.t('启用'), cssClass: '' },
  { value: 2, text: i18n.t('禁用'), cssClass: '' }
]

// 模具类型
export const mouldTypeList = [
  { value: 1, text: i18n.t('基础模具'), cssClass: '' },
  { value: 2, text: i18n.t('复制模具'), cssClass: '' },
  { value: 3, text: i18n.t('基础模改模'), cssClass: '' },
  { value: 4, text: i18n.t('复制模改模'), cssClass: '' }
]

// 物料价格库
const materialPriceWarehouseColumnData = [
  {
    field: 'id',
    width: 0,
    isPrimaryKey: true
  },
  {
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: syncStatusList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'syncMsg',
    headerText: i18n.t('同步信息')
  },
  {
    field: 'orderNo',
    headerText: i18n.t('采购订单号')
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('含税单价')
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('未税单价')
  },
  {
    field: 'priceUnit',
    headerText: i18n.t('价格单位')
  },
  {
    field: 'priceValueType',
    headerText: i18n.t('价格类别'),
    valueConverter: {
      type: 'map',
      map: priceCategoryList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'priceType',
    headerText: i18n.t('价格类型'),
    valueConverter: {
      type: 'map',
      map: priceTypeList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司'),
    width: '200',
    valueAccessor: (field, data) => {
      if (Array.isArray(data.companyList) && data.companyList.length) {
        return data.companyList[0]['companyCode'] + '-' + data.companyList[0]['companyName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: '200',
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.siteList[0]['siteCode'] + '-' + data.siteList[0]['siteName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织'),
    ignore: true,
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.siteList[0]['purOrgCode'] + '-' + data.siteList[0]['purOrgName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'quoteAttribute',
    headerText: i18n.t('报价属性'),
    valueConverter: {
      type: 'map',
      map: quoteAttributeList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'quoteMode',
    headerText: i18n.t('价格生效方式'),
    valueConverter: {
      type: 'map',
      map: quoteModeList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'deliveryPlace',
    headerText: i18n.t('直送地')
  },
  {
    field: 'unitName',
    headerText: i18n.t('单位')
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种'),
    valueAccessor: (field, row) => {
      return row.currencyCode ? row.currencyCode + '-' + row.currencyName : ''
    }
  },
  {
    field: 'taxRate',
    headerText: i18n.t('税率')
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('生效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('失效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'priceRecordCode',
    headerText: i18n.t('价格编码')
  },
  {
    field: 'sourceCode',
    headerText: i18n.t('来源单号')
  },
  {
    field: 'purchaseInfoRecordNo',
    headerText: i18n.t('外发信息记录编号')
  },
  {
    field: 'minPackageQuantity',
    headerText: i18n.t('最小包装数量')
  },
  {
    field: 'minPurchaseQuantity',
    headerText: i18n.t('最小采购量')
  },
  {
    field: 'leadTime',
    headerText: 'L/T'
  },
  {
    field: 'unconditionalLeadTime',
    headerText: i18n.t('无条件L/T')
  },
  {
    field: 'paymentCondition',
    headerText: i18n.t('付款条件')
  },
  {
    field: 'historyRecord',
    headerText: i18n.t('历史记录'),
    template: function () {
      return {
        template: historyes
      }
    }
  },
  {
    field: 'priceRecordId',
    headerText: i18n.t('历史价格趋势'),
    template: function () {
      return {
        template: priceHistory
      }
    }
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: statusList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'purchaseAgentName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    field: 'sourceCreateTime',
    headerText: i18n.t('单据创建时间')
  },
  {
    field: 'oaApprovalFinishTime',
    headerText: i18n.t('OA审批完成时间')
  },
  {
    field: 'syncFinishTime',
    headerText: i18n.t('同步SAP完成时间')
  },
  {
    field: 'priceValidStartTime',
    headerText: i18n.t('初始有效开始时间')
  },
  {
    field: 'priceValidEndTime',
    headerText: i18n.t('初始有效结束时间')
  }
]
export const materialPriceWarehousePageConfig = [
  {
    title: i18n.t('物料价格库'),
    gridId: 'a4cf97d0-e9fe-4365-a49f-cbfb732a626e',
    activatedRefresh: false,
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    useToolTemplate: false,
    customSearchExpandMinRows: 2,
    toolbar: {
      useBaseConfig: false,
      tools: [
        [
          { id: 'importSAP', icon: 'icon_solid_upload', title: i18n.t('导入SAP') },
          {
            id: 'download',
            icon: 'icon_solid_export',
            title: i18n.t('导出'),
            permission: ['O_02_1708']
          },
          { id: 'diffCompare', title: i18n.t('差异对比') }
        ],
        ['Refresh', 'Setting']
      ]
    },
    grid: {
      virtualPageSize: 30,
      enableVirtualization: true,
      customSelection: true,
      selectionSettings: {
        persistSelection: true,
        type: 'Multiple',
        checkboxOnly: true
      },
      pageSettings: {
        currentPage: 1,
        pageSize: 20,
        pageSizes: [20, 50, 100, 200, 1000],
        totalRecordsCount: 0
      },
      allowFiltering: true,
      showSelected: false,
      columnData: materialPriceWarehouseColumnData,
      asyncConfig: {
        url: `/price/tenant/priceRecord/new/pageQuery?BU_CODE=${localStorage.getItem('currentBu')}`
      }
    }
  }
]

// 系列物料价格
const seriesMaterialPriceColumnData = [
  {
    field: 'id',
    width: 0,
    isPrimaryKey: true
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'material',
    headerText: i18n.t('物料材质')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'detail',
    headerText: i18n.t('明细'),
    cssClass: 'field-content',
    valueConverter: {
      type: 'placeholder',
      placeholder: i18n.t('明细')
    }
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: statusList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('含税单价')
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('未税单价')
  },
  {
    field: 'taxRate',
    headerText: i18n.t('税率')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司'),
    width: '200',
    valueAccessor: (field, data) => {
      if (Array.isArray(data.companyList) && data.companyList.length) {
        return data.companyList[0]['companyCode'] + '-' + data.companyList[0]['companyName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: '200',
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.siteList[0]['siteCode'] + '-' + data.siteList[0]['siteName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织'),
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.siteList[0]['purOrgCode'] + '-' + data.siteList[0]['purOrgName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'quoteAttribute',
    headerText: i18n.t('报价属性'),
    valueConverter: {
      type: 'map',
      map: quoteAttributeList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'quoteMode',
    headerText: i18n.t('价格生效方式'),
    valueConverter: {
      type: 'map',
      map: quoteModeList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'deliveryPlace',
    headerText: i18n.t('直送地')
  },
  {
    field: 'priceRecordCode',
    headerText: i18n.t('价格编码')
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种'),
    valueAccessor: (field, row) => {
      return row.currencyCode ? row.currencyCode + '-' + row.currencyName : ''
    }
  },
  {
    field: 'sourceCode',
    headerText: i18n.t('来源单号')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('生效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('失效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'unitCode',
    headerText: i18n.t('基本单位编码')
  },
  {
    field: 'unitName',
    headerText: i18n.t('基本单位名称')
  },
  {
    field: 'purUnitCode',
    headerText: i18n.t('订单单位编码')
  },
  {
    field: 'purUnitName',
    headerText: i18n.t('订单单位名称')
  },
  {
    field: 'minPackageQuantity',
    headerText: i18n.t('最小包装量')
  },
  {
    field: 'minPurchaseQuantity',
    headerText: i18n.t('最小采购量')
  },
  {
    field: 'leadTime',
    headerText: 'L/T'
  },
  {
    field: 'unconditionalLeadTime',
    headerText: i18n.t('无条件L/T')
  },
  {
    field: 'paymentCondition',
    headerText: i18n.t('付款条件')
  },
  {
    field: 'purchaseAgentName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    field: 'priceValidStartTime',
    headerText: i18n.t('初始有效开始时间')
  },
  {
    field: 'priceValidEndTime',
    headerText: i18n.t('初始有效结束时间')
  }
]
export const seriesMaterialPricePageConfig = [
  {
    title: i18n.t('系列物料价格'),
    gridId: '1e368091-25ac-43ee-8eb8-a9cb9fef61b1',
    activatedRefresh: false,
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    useToolTemplate: false,
    customSearchExpandMinRows: 2,
    toolbar: {
      useBaseConfig: false,
      tools: [
        [
          {
            id: 'download',
            icon: 'icon_solid_export',
            title: i18n.t('导出'),
            permission: ['O_02_1708']
          }
        ],
        ['Refresh', 'Setting']
      ]
    },
    grid: {
      virtualPageSize: 30,
      enableVirtualization: true,
      allowSelection: false,
      allowFiltering: true,
      showSelected: false,
      pageSettings: {
        currentPage: 1,
        pageSize: 20,
        pageSizes: [20, 50, 100, 200, 1000],
        totalRecordsCount: 0
      },
      columnData: seriesMaterialPriceColumnData,
      asyncConfig: {
        url: `/price/tenant/priceRecord/new/pageQueryByPriceType?BU_CODE=${localStorage.getItem('currentBu')}`,
        params: {
          priceType: 7
        }
      }
    }
  }
]

// 模具
const mouldColumnData = [
  {
    field: 'id',
    width: 0,
    isPrimaryKey: true
  },
  {
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: syncStatusList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'syncMsg',
    headerText: i18n.t('同步信息')
  },
  {
    field: 'dieCode',
    headerText: i18n.t('模具编码')
  },
  {
    field: 'dieName',
    headerText: i18n.t('模具名称')
  },
  {
    field: 'dieType',
    headerText: i18n.t('模具类型'),
    valueConverter: {
      type: 'map',
      map: {
        0: '',
        1: i18n.t('基础模具'),
        2: i18n.t('复制模具'),
        3: i18n.t('基础模改模'),
        4: i18n.t('复制模改模')
      }
    }
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: statusList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司'),
    width: '200',
    valueAccessor: (field, data) => {
      if (Array.isArray(data.companyList) && data.companyList.length) {
        return data.companyList[0]['companyCode'] + '-' + data.companyList[0]['companyName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: '200',
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.siteList[0]['siteCode'] + '-' + data.siteList[0]['siteName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'quoteAttribute',
    headerText: i18n.t('报价属性'),
    valueConverter: {
      type: 'map',
      map: quoteAttributeList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'quoteMode',
    headerText: i18n.t('价格生效方式'),
    valueConverter: {
      type: 'map',
      map: quoteModeList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('单价(未税)')
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('单价(含税)')
  },
  {
    field: 'deliveryPlace',
    headerText: i18n.t('直送地')
  },
  {
    field: 'unitCode',
    headerText: i18n.t('基本单位编码')
  },
  {
    field: 'unitName',
    headerText: i18n.t('基本单位名称')
  },
  {
    field: 'purUnitName',
    headerText: i18n.t('订单单位名称')
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种'),
    valueAccessor: (field, row) => {
      return row.currencyCode ? row.currencyCode + '-' + row.currencyName : ''
    }
  },
  {
    field: 'taxCode',
    headerText: i18n.t('税率编码')
  },
  {
    field: 'taxName',
    headerText: i18n.t('税率名称')
  },
  {
    field: 'priceUnit',
    headerText: i18n.t('价格单位'),
    ignore: true
  },
  {
    field: 'priceRecordCode',
    headerText: i18n.t('价格编码')
  },
  {
    field: 'sourceCode',
    headerText: i18n.t('来源单号')
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('价格生效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('价格失效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'purchaseAgentName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    field: 'priceValidStartTime',
    headerText: i18n.t('初始有效开始时间')
  },
  {
    field: 'priceValidEndTime',
    headerText: i18n.t('初始有效结束时间')
  }
]
export const mouldPageConfig = [
  {
    title: i18n.t('模具'),
    gridId: '9e0bfa9d-0ee3-4513-b0ac-80b5548ef6be',
    activatedRefresh: false,
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    useToolTemplate: false,
    customSearchExpandMinRows: 2,
    toolbar: {
      useBaseConfig: false,
      tools: [
        [
          {
            id: 'download',
            icon: 'icon_solid_export',
            title: i18n.t('导出'),
            permission: ['O_02_1708']
          }
        ],
        ['Refresh', 'Setting']
      ]
    },
    grid: {
      virtualPageSize: 30,
      enableVirtualization: true,
      allowSelection: false,
      allowFiltering: true,
      showSelected: false,
      pageSettings: {
        currentPage: 1,
        pageSize: 20,
        pageSizes: [20, 50, 100, 200, 1000],
        totalRecordsCount: 0
      },
      columnData: mouldColumnData,
      asyncConfig: {
        url: `/price/tenant/priceRecord/new/pageQueryByPriceType?BU_CODE=${localStorage.getItem('currentBu')}`,
        params: {
          priceType: 8
        }
      }
    }
  }
]

// 结构阶梯
const structureLadderColumnData = [
  {
    field: 'id',
    width: 0,
    isPrimaryKey: true
  },
  {
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: syncStatusList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'syncMsg',
    headerText: i18n.t('同步信息')
  },
  {
    field: 'priceValueType',
    headerText: i18n.t('价格类别'),
    valueConverter: {
      type: 'map',
      map: priceCategoryList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'priceType',
    headerText: i18n.t('价格类型'),
    valueConverter: {
      type: 'map',
      map: priceTypeList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: statusList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司'),
    width: '200',
    valueAccessor: (field, data) => {
      if (Array.isArray(data.companyList) && data.companyList.length) {
        return data.companyList[0]['companyCode'] + '-' + data.companyList[0]['companyName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织'),
    ignore: true,
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.siteList[0]['purOrgCode'] + '-' + data.siteList[0]['purOrgName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: '200',
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.siteList[0]['siteCode'] + '-' + data.siteList[0]['siteName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'stepValue',
    headerText: i18n.t('阶梯数量')
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('单价(未税)')
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('单价(含税)')
  },
  {
    field: 'unitCode',
    headerText: i18n.t('基本单位编码')
  },
  {
    field: 'purUnitCode',
    headerText: i18n.t('订单单位编码')
  },
  {
    field: 'deliveryPlace',
    headerText: i18n.t('直送地')
  },
  {
    field: 'priceUnit',
    headerText: i18n.t('价格单位')
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种'),
    valueAccessor: (field, row) => {
      return row.currencyCode ? row.currencyCode + '-' + row.currencyName : ''
    }
  },
  {
    field: 'taxName',
    headerText: i18n.t('税率名称')
  },
  {
    field: 'quoteAttribute',
    headerText: i18n.t('报价属性'),
    valueConverter: {
      type: 'map',
      map: quoteAttributeList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'quoteMode',
    headerText: i18n.t('价格生效方式'),
    valueConverter: {
      type: 'map',
      map: quoteModeList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'leadTime',
    headerText: 'L/T'
  },
  {
    field: 'unconditionalLeadTime',
    headerText: i18n.t('无条件L/T')
  },
  {
    field: 'minPurchaseQuantity',
    headerText: i18n.t('最小采购量')
  },
  {
    field: 'minPackageQuantity',
    headerText: i18n.t('最小包装量')
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('生效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('失效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'sourceCode',
    headerText: i18n.t('来源单号')
  },
  {
    field: 'priceRecordCode',
    headerText: i18n.t('价格编码')
  },
  {
    field: 'purchaseAgentName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    field: 'priceValidStartTime',
    headerText: i18n.t('初始有效开始时间')
  },
  {
    field: 'priceValidEndTime',
    headerText: i18n.t('初始有效结束时间')
  }
]
export const structureLadderPageConfig = [
  {
    title: i18n.t('结构阶梯'),
    gridId: 'd600b009-2f3d-4109-bee2-9a42b851c58b',
    activatedRefresh: false,
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    useToolTemplate: false,
    customSearchExpandMinRows: 2,
    toolbar: {
      useBaseConfig: false,
      tools: [
        [
          {
            id: 'download',
            icon: 'icon_solid_export',
            title: i18n.t('导出'),
            permission: ['O_02_1708']
          }
        ],
        ['Refresh', 'Setting']
      ]
    },
    grid: {
      virtualPageSize: 30,
      enableVirtualization: true,
      allowSelection: false,
      allowFiltering: true,
      showSelected: false,
      pageSettings: {
        currentPage: 1,
        pageSize: 20,
        pageSizes: [20, 50, 100, 200, 1000],
        totalRecordsCount: 0
      },
      columnData: structureLadderColumnData,
      asyncConfig: {
        url: `/price/tenant/priceRecord/new/pageQueryByPriceType?BU_CODE=${localStorage.getItem('currentBu')}`,
        params: {
          priceType: 10
        }
      }
    }
  }
]

// 美工阶梯
const artLadderColumnData = [...structureLadderColumnData]
export const artLadderPageConfig = [
  {
    title: i18n.t('美工阶梯'),
    gridId: '912dbcdc-8136-46e2-8884-177a84d29339',
    activatedRefresh: false,
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    useToolTemplate: false,
    customSearchExpandMinRows: 2,
    toolbar: {
      useBaseConfig: false,
      tools: [
        [
          { id: 'importSAP', icon: 'icon_solid_upload', title: i18n.t('导入SAP') },
          {
            id: 'download',
            icon: 'icon_solid_export',
            title: i18n.t('导出'),
            permission: ['O_02_1708']
          },
          { id: 'diffCompare', title: i18n.t('差异对比') }
        ],
        ['Refresh', 'Setting']
      ]
    },
    grid: {
      virtualPageSize: 30,
      enableVirtualization: true,
      customSelection: true,
      selectionSettings: {
        persistSelection: true,
        type: 'Multiple',
        checkboxOnly: true
      },
      pageSettings: {
        currentPage: 1,
        pageSize: 20,
        pageSizes: [20, 50, 100, 200, 1000],
        totalRecordsCount: 0
      },
      allowFiltering: true,
      showSelected: false,
      columnData: artLadderColumnData,
      asyncConfig: {
        url: `/price/tenant/priceRecord/new/pageQueryByPriceType?BU_CODE=${localStorage.getItem('currentBu')}`,
        params: {
          priceType: 11
        }
      }
    }
  }
]

// 部品+模具
const partMoldColumnData = [
  {
    field: 'id',
    width: 0,
    isPrimaryKey: true
  },
  {
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: syncStatusList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'syncMsg',
    headerText: i18n.t('同步信息')
  },
  {
    field: 'sourceCode',
    headerText: i18n.t('来源单号')
  },
  {
    field: 'priceRecordCode',
    headerText: i18n.t('价格编码')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: statusList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司'),
    width: '200',
    valueAccessor: (field, data) => {
      if (Array.isArray(data.companyList) && data.companyList.length) {
        return data.companyList[0]['companyCode'] + '-' + data.companyList[0]['companyName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织'),
    ignore: true,
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.siteList[0]['purOrgCode'] + '-' + data.siteList[0]['purOrgName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: '200',
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.siteList[0]['siteCode'] + '-' + data.siteList[0]['siteName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'deliveryPlace',
    headerText: i18n.t('直送地')
  },
  {
    field: 'priceUnit',
    headerText: i18n.t('价格单位')
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('单价(未税)')
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('单价(含税)')
  },
  {
    field: 'sharePriceUntaxed',
    headerText: i18n.t('分摊后单价(未税)')
  },
  {
    field: 'sharePriceTaxed',
    headerText: i18n.t('分摊后单价(含税)')
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种'),
    valueAccessor: (field, row) => {
      return row.currencyCode ? row.currencyCode + '-' + row.currencyName : ''
    }
  },
  {
    field: 'taxRate',
    headerText: i18n.t('税率')
  },
  {
    field: 'quoteAttribute',
    headerText: i18n.t('报价属性'),
    valueConverter: {
      type: 'map',
      map: quoteAttributeList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'quoteMode',
    headerText: i18n.t('价格生效方式'),
    valueConverter: {
      type: 'map',
      map: quoteModeList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'dieType',
    headerText: i18n.t('模具类型'),
    valueConverter: {
      type: 'map',
      map: {
        0: '',
        1: i18n.t('基础模具'),
        2: i18n.t('复制模具'),
        3: i18n.t('基础模改模'),
        4: i18n.t('复制模改模')
      }
    }
  },
  {
    field: 'baseMouldUntaxedPrice',
    headerText: i18n.t('基础模/复制模单价（未税）')
  },
  {
    field: 'baseMouldTaxedPrice',
    headerText: i18n.t('基础模/复制模单价（含税）')
  },
  {
    field: 'baseTaxRate',
    headerText: i18n.t('基础模/复制模税率')
  },
  {
    field: 'basePlanQuantity',
    headerText: i18n.t('基础模/复制模规划量')
  },
  {
    field: 'baseShareQuantity',
    headerText: i18n.t('基础模/复制模实际分摊量')
  },
  {
    field: 'baseSharePriceUntaxed',
    headerText: i18n.t('基础模/复制模实际分摊价（未税）')
  },
  {
    field: 'basePlanSharePriceUntaxed',
    headerText: i18n.t('基础模/复制模规划分摊价（未税）')
  },
  {
    field: 'modifyMouldUntaxedPrice',
    headerText: i18n.t('改模单价（未税）')
  },
  {
    field: 'modifyMouldTaxedPrice',
    headerText: i18n.t('改模单价（含税）')
  },
  {
    field: 'modifyTaxRate',
    headerText: i18n.t('改模税率')
  },
  {
    field: 'modifyPlanQuantity',
    headerText: i18n.t('改模规划量')
  },
  {
    field: 'modifyShareQuantity',
    headerText: i18n.t('改模实际分摊量')
  },
  {
    field: 'modifySharePriceUntaxed',
    headerText: i18n.t('改模实际分摊价（未税）')
  },
  {
    field: 'modifyPlanSharePriceUntaxed',
    headerText: i18n.t('改模规划分摊价（未税）')
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('生效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('失效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'purchaseAgentName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    field: 'priceValidStartTime',
    headerText: i18n.t('初始有效开始时间')
  },
  {
    field: 'priceValidEndTime',
    headerText: i18n.t('初始有效结束时间')
  }
]
export const partMoldPageConfig = [
  {
    title: i18n.t('部品+模具'),
    gridId: '6c485f4d-29dc-488b-8bd6-3960eb1de63a',
    activatedRefresh: false,
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    useToolTemplate: false,
    customSearchExpandMinRows: 2,
    toolbar: {
      useBaseConfig: false,
      tools: [
        [
          { id: 'importSAP', icon: 'icon_solid_upload', title: i18n.t('导入SAP') },
          {
            id: 'download',
            icon: 'icon_solid_export',
            title: i18n.t('导出'),
            permission: ['O_02_1708']
          },
          { id: 'diffCompare', title: i18n.t('差异对比') }
        ],
        ['Refresh', 'Setting']
      ]
    },
    grid: {
      virtualPageSize: 30,
      enableVirtualization: true,
      customSelection: true,
      selectionSettings: {
        persistSelection: true,
        type: 'Multiple',
        checkboxOnly: true
      },
      pageSettings: {
        currentPage: 1,
        pageSize: 20,
        pageSizes: [20, 50, 100, 200, 1000],
        totalRecordsCount: 0
      },
      allowFiltering: true,
      showSelected: false,
      columnData: partMoldColumnData,
      asyncConfig: {
        url: `/price/tenant/priceRecord/new/pageQueryByPriceType?BU_CODE=${localStorage.getItem('currentBu')}`,
        params: {
          priceType: 9
        }
      }
    }
  }
]

// 外发阶梯
export const outgoingLadderColumnData = [
  {
    field: 'id',
    width: 0,
    isPrimaryKey: true
  },
  {
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: syncStatusList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'syncMsg',
    headerText: i18n.t('同步信息')
  },
  {
    field: 'priceRecordCode',
    headerText: i18n.t('价格编码')
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料描述')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司'),
    width: '200',
    valueAccessor: (field, data) => {
      if (Array.isArray(data.companyList) && data.companyList.length) {
        return data.companyList[0]['companyCode'] + '-' + data.companyList[0]['companyName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: '200',
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.siteList[0]['siteCode'] + '-' + data.siteList[0]['siteName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织'),
    ignore: true,
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.siteList[0]['purOrgCode'] + '-' + data.siteList[0]['purOrgName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: statusList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'quoteAttribute',
    headerText: i18n.t('报价属性'),
    valueConverter: {
      type: 'map',
      map: quoteAttributeList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'quoteMode',
    headerText: i18n.t('价格生效方式'),
    valueConverter: {
      type: 'map',
      map: quoteModeList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'deliveryPlace',
    headerText: i18n.t('直送地'),
    ignore: true
  },
  {
    field: 'stepValue',
    headerText: i18n.t('阶梯数量')
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('单价(未税)')
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('单价(含税)')
  },
  {
    field: 'unitName',
    headerText: i18n.t('基本单位')
  },
  {
    field: 'purUnitName',
    headerText: i18n.t('订单单位')
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种'),
    valueAccessor: (field, row) => {
      return row.currencyCode ? row.currencyCode + '-' + row.currencyName : ''
    }
  },
  {
    field: 'taxCode',
    headerText: i18n.t('税率编码')
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('生效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('失效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'sourceCode',
    headerText: i18n.t('来源单号')
  },
  {
    field: 'purchaseAgentName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    field: 'purchaseInfoRecordNo',
    headerText: i18n.t('外发信息记录编号')
  },
  {
    field: 'innerCounter',
    headerText: i18n.t('序列号')
  },
  {
    field: 'priceValidStartTime',
    headerText: i18n.t('初始有效开始时间')
  },
  {
    field: 'priceValidEndTime',
    headerText: i18n.t('初始有效结束时间')
  }
]
export const outgoingLadderPageConfig = [
  {
    title: i18n.t('外发阶梯'),
    gridId: 'c6c445ec-03b9-4d9c-ad39-fe46413cecdf',
    activatedRefresh: false,
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    useToolTemplate: false,
    customSearchExpandMinRows: 2,
    toolbar: {
      useBaseConfig: false,
      tools: [
        [
          { id: 'importSAP', icon: 'icon_solid_upload', title: i18n.t('导入SAP') },
          {
            id: 'download',
            icon: 'icon_solid_export',
            title: i18n.t('导出'),
            permission: ['O_02_1708']
          },
          { id: 'diffCompare', title: i18n.t('差异对比') }
        ],
        ['Refresh', 'Setting']
      ]
    },
    grid: {
      virtualPageSize: 30,
      enableVirtualization: true,
      customSelection: true,
      selectionSettings: {
        persistSelection: true,
        type: 'Multiple',
        checkboxOnly: true
      },
      pageSettings: {
        currentPage: 1,
        pageSize: 20,
        pageSizes: [20, 50, 100, 200, 1000],
        totalRecordsCount: 0
      },
      allowFiltering: true,
      showSelected: false,
      columnData: outgoingLadderColumnData,
      asyncConfig: {
        url: `/price/tenant/priceRecord/new/pageQueryStage?BU_CODE=${localStorage.getItem('currentBu')}`
      }
    }
  }
]

// 物流价格记录 - tab页签
export const logisticsTabConfig = [
  {
    title: i18n.t('海运（临时需求）'),
    tabCode: 'temporary',
    permissionCode: 'O_02_1785'
  },
  {
    title: i18n.t('铁路&陆运（临时需求）'),
    tabCode: 'temporary',
    permissionCode: 'O_02_1785'
  },
  {
    title: i18n.t('空运（临时需求）'),
    tabCode: 'temporary',
    permissionCode: 'O_02_1785'
  },
  {
    title: i18n.t('海运（年约需求）'),
    tabCode: 'sea',
    permissionCode: 'O_02_1786'
  },
  {
    title: i18n.t('公路（年约需求）'),
    tabCode: 'trunk',
    permissionCode: 'O_02_1787'
  },
  {
    title: i18n.t('铁运（年约需求）'),
    tabCode: 'rail',
    permissionCode: 'O_02_1788'
  },
  {
    title: i18n.t('铁运班列（年约需求）'),
    tabCode: 'railLine',
    permissionCode: 'O_02_1789'
  }
]
// 物流价格记录 - 物流类型
export const logisticsTypeArr = ['sea_transport', 'land_transport', 'air_transport']
// 物流价格记录 - 列信息
const logisticsColumnData = [
  [
    {
      field: 'id',
      width: 0,
      isPrimaryKey: true
    },
    {
      field: 'syncStatus',
      headerText: i18n.t('同步状态'),
      valueConverter: {
        type: 'map',
        map: syncStatusList,
        fields: { text: 'text', value: 'value' }
      }
    },
    {
      field: 'syncMsg',
      headerText: i18n.t('同步信息')
    },
    {
      field: 'requirementNumber',
      headerText: i18n.t('需求编号')
    },
    {
      field: 'requirementName',
      headerText: i18n.t('需求名称')
    },
    {
      field: 'department',
      headerText: i18n.t('事业部')
    },
    {
      field: 'companyName',
      headerText: i18n.t('公司'),
      width: '200',
      valueAccessor: (field, data) => {
        return data['companyCode'] + '-' + data['companyName']
      }
    },
    {
      field: 'requirementType',
      headerText: i18n.t('需求类型')
    },
    {
      field: 'transportType',
      headerText: i18n.t('运输方式')
    },
    {
      field: 'startPort',
      headerText: i18n.t('起运港')
    },
    {
      field: 'endPort',
      headerText: i18n.t('目的港')
    },
    {
      field: 'productName',
      headerText: i18n.t('品名')
    },
    {
      field: 'cabinetType',
      headerText: i18n.t('柜型')
    },
    {
      field: 'demandQuantity',
      headerText: i18n.t('需求数量')
    },
    {
      field: 'goodsFinishTime',
      headerText: i18n.t('货好时间')
    },
    {
      field: 'logisticsTerm',
      headerText: i18n.t('条款')
    },
    {
      field: 'serviceMode',
      headerText: i18n.t('服务模式')
    },
    {
      field: 'startAddress',
      headerText: i18n.t('起运地址')
    },
    {
      field: 'endAddress',
      headerText: i18n.t('目的地址')
    },
    {
      field: 'categoryCode',
      headerText: i18n.t('品类编码')
    },
    {
      field: 'categoryName',
      headerText: i18n.t('品类名称')
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商'),
      valueAccessor: (field, data) => {
        return data['supplierCode'] + '-' + data['supplierName']
      }
    },
    {
      field: 'totalFee',
      headerText: i18n.t('费用USD')
    },
    {
      field: 'priceValidStartTime',
      headerText: i18n.t('价格有效期起')
    },
    {
      field: 'priceValidEndTime',
      headerText: i18n.t('价格有效期止')
    },
    {
      field: 'etd',
      headerText: i18n.t('ETD')
    },
    {
      field: 'eta',
      headerText: i18n.t('ETA')
    },
    {
      field: 'distanceVoy',
      headerText: i18n.t('航程')
    },
    {
      field: 'transportFeeCurrencyName',
      headerText: i18n.t('运费币种'),
      valueAccessor: (field, data) => {
        return data['transportFeeCurrencyCode'] + '-' + data['transportFeeCurrencyName']
      }
    },
    {
      field: 'transportFee',
      headerText: i18n.t('运费')
    },
    {
      field: 'startPortFeeName',
      headerText: i18n.t('起运港费用币种'),
      valueAccessor: (field, data) => {
        return data['startPortFeeCode'] + '-' + data['startPortFeeName']
      }
    },
    {
      field: 'startPortTrailerFee',
      headerText: i18n.t('起运港拖车费用')
    },
    {
      field: 'thcFee',
      headerText: i18n.t('THC费用')
    },
    {
      field: 'doc',
      headerText: i18n.t('DOC')
    },
    {
      field: 'seal',
      headerText: i18n.t('SEAL')
    },
    {
      field: 'eir',
      headerText: i18n.t('EIR')
    },
    {
      field: 'endPortFeeName',
      headerText: i18n.t('目的港费用币种'),
      valueAccessor: (field, data) => {
        return data['endPortFeeCode'] + '-' + data['endPortFeeName']
      }
    },
    {
      field: 'endPortTrailerFee',
      headerText: i18n.t('目的港拖车费用')
    },
    {
      field: 'endPortFee',
      headerText: i18n.t('目的港费用')
    },
    {
      field: 'otherFeeCurrencyName',
      headerText: i18n.t('其他费用币种'),
      valueAccessor: (field, data) => {
        return data['otherFeeCurrencyCode'] + '-' + data['otherFeeCurrencyName']
      }
    },
    {
      field: 'bookingFee',
      headerText: i18n.t('订舱费')
    },
    {
      field: 'usdTotalFeeSum',
      headerText: i18n.t('总计费用USD')
    },
    {
      field: 'cnyTotalFeeSum',
      headerText: i18n.t('总计费用RMB')
    },
    {
      field: 'exchangeRate',
      headerText: i18n.t('汇率')
    },
    {
      field: 'sourceCode',
      headerText: i18n.t('来源单号')
    },
    {
      field: 'purchaseAgentName',
      headerText: i18n.t('创建人')
    },
    {
      field: 'createTime',
      headerText: i18n.t('创建时间')
    }
  ],
  [
    {
      field: 'id',
      width: 0,
      isPrimaryKey: true
    },
    {
      field: 'syncStatus',
      headerText: i18n.t('同步状态'),
      valueConverter: {
        type: 'map',
        map: syncStatusList,
        fields: { text: 'text', value: 'value' }
      }
    },
    {
      field: 'syncMsg',
      headerText: i18n.t('同步信息')
    },
    {
      field: 'requirementNumber',
      headerText: i18n.t('需求编号')
    },
    {
      field: 'requirementName',
      headerText: i18n.t('需求名称')
    },
    {
      field: 'department',
      headerText: i18n.t('事业部')
    },
    {
      field: 'companyName',
      headerText: i18n.t('公司'),
      width: '200',
      valueAccessor: (field, data) => {
        return data['companyCode'] + '-' + data['companyName']
      }
    },
    {
      field: 'requirementType',
      headerText: i18n.t('需求类型')
    },
    {
      field: 'transportType',
      headerText: i18n.t('运输方式')
    },
    {
      field: 'startPort',
      headerText: i18n.t('起运港')
    },
    {
      field: 'endPort',
      headerText: i18n.t('目的港')
    },
    {
      field: 'productName',
      headerText: i18n.t('品名')
    },
    {
      field: 'demandQuantity',
      headerText: i18n.t('需求数量')
    },
    {
      field: 'goodsFinishTime',
      headerText: i18n.t('货好时间')
    },
    {
      field: 'logisticsTerm',
      headerText: i18n.t('条款')
    },
    {
      field: 'serviceMode',
      headerText: i18n.t('服务模式')
    },
    {
      field: 'startAddress',
      headerText: i18n.t('起运地址')
    },
    {
      field: 'endAddress',
      headerText: i18n.t('目的地址')
    },
    {
      field: 'categoryCode',
      headerText: i18n.t('品类编码')
    },
    {
      field: 'categoryName',
      headerText: i18n.t('品类名称')
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商'),
      valueAccessor: (field, data) => {
        return data['supplierCode'] + '-' + data['supplierName']
      }
    },
    {
      field: 'totalFee',
      headerText: i18n.t('费用USD')
    },
    {
      field: 'cabinetQuantity',
      headerText: i18n.t('可提供柜量')
    },
    {
      field: 'untaxedUnitPrice',
      headerText: i18n.t('单价（All in）USD/HQ')
    },
    {
      field: 'otherFee',
      headerText: i18n.t('其他费用（压夜费、查验费等）USD/HQ')
    },
    {
      field: 'priceValidStartTime',
      headerText: i18n.t('价格有效期起')
    },
    {
      field: 'priceValidEndTime',
      headerText: i18n.t('价格有效期止')
    },
    {
      field: 'sourceCode',
      headerText: i18n.t('来源单号')
    },
    {
      field: 'purchaseAgentName',
      headerText: i18n.t('创建人')
    },
    {
      field: 'createTime',
      headerText: i18n.t('创建时间')
    }
  ],
  [
    {
      field: 'id',
      width: 0,
      isPrimaryKey: true
    },
    {
      field: 'syncStatus',
      headerText: i18n.t('同步状态'),
      valueConverter: {
        type: 'map',
        map: syncStatusList,
        fields: { text: 'text', value: 'value' }
      }
    },
    {
      field: 'syncMsg',
      headerText: i18n.t('同步信息')
    },
    {
      field: 'requirementNumber',
      headerText: i18n.t('需求编号')
    },
    {
      field: 'requirementName',
      headerText: i18n.t('需求名称')
    },
    {
      field: 'department',
      headerText: i18n.t('事业部')
    },
    {
      field: 'companyName',
      headerText: i18n.t('公司'),
      width: '200',
      valueAccessor: (field, data) => {
        return data['companyCode'] + '-' + data['companyName']
      }
    },
    {
      field: 'requirementType',
      headerText: i18n.t('需求类型')
    },
    {
      field: 'transportType',
      headerText: i18n.t('运输方式')
    },
    {
      field: 'startPort',
      headerText: i18n.t('起运港')
    },
    {
      field: 'endPort',
      headerText: i18n.t('目的港')
    },
    {
      field: 'productName',
      headerText: i18n.t('品名')
    },
    {
      field: 'goodsFinishTime',
      headerText: i18n.t('货好时间')
    },
    {
      field: 'logisticsTerm',
      headerText: i18n.t('条款')
    },
    {
      field: 'serviceMode',
      headerText: i18n.t('服务模式')
    },
    {
      field: 'startAddress',
      headerText: i18n.t('起运地址')
    },
    {
      field: 'endAddress',
      headerText: i18n.t('目的地址')
    },
    {
      field: 'specVolume',
      headerText: i18n.t('长*宽*高')
    },
    {
      field: 'specGrossWeight',
      headerText: i18n.t('总毛重')
    },
    {
      field: 'specVolume',
      headerText: i18n.t('总体积')
    },
    {
      field: 'specVolumeWeight',
      headerText: i18n.t('总体积重')
    },
    {
      field: 'transportQuantity',
      headerText: i18n.t('件数（包装）')
    },
    {
      field: 'declareMode',
      headerText: i18n.t('报关方式')
    },
    {
      field: 'categoryCode',
      headerText: i18n.t('品类编码')
    },
    {
      field: 'categoryName',
      headerText: i18n.t('品类名称')
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商'),
      valueAccessor: (field, data) => {
        return data['supplierCode'] + '-' + data['supplierName']
      }
    },
    {
      field: 'totalFee',
      headerText: i18n.t('费用USD')
    },
    {
      field: 'priceValidStartTime',
      headerText: i18n.t('价格有效期起')
    },
    {
      field: 'priceValidEndTime',
      headerText: i18n.t('价格有效期止')
    },
    {
      field: 'startAirport',
      headerText: i18n.t('起运机场')
    },
    {
      field: 'etd',
      headerText: i18n.t('ETD')
    },
    {
      field: 'eta',
      headerText: i18n.t('ETA')
    },
    {
      field: 'transportFeeCurrencyName',
      headerText: i18n.t('运费币种'),
      valueAccessor: (field, data) => {
        return data['transportFeeCurrencyCode'] + '-' + data['transportFeeCurrencyName']
      }
    },
    {
      field: 'transportFee',
      headerText: i18n.t('运费')
    },
    {
      field: 'startPortFeeName',
      headerText: i18n.t('起运港费用币种'),
      valueAccessor: (field, data) => {
        return data['startPortFeeCode'] + '-' + data['startPortFeeName']
      }
    },
    {
      field: 'startPortTrailerFee',
      headerText: i18n.t('起运港拖车费用')
    },
    {
      field: 'endAirportHandleFee',
      headerText: i18n.t('机场操作费')
    },
    {
      field: 'manifestFee',
      headerText: i18n.t('舱单录入费')
    },
    {
      field: 'billDeclarationFee',
      headerText: i18n.t('买单报关费')
    },
    {
      field: 'endPortFeeName',
      headerText: i18n.t('目的港费用币种'),
      valueAccessor: (field, data) => {
        return data['endPortFeeCode'] + '-' + data['endPortFeeName']
      }
    },
    {
      field: 'customsClearanceFee',
      headerText: i18n.t('清关')
    },
    {
      field: 'distributionFee',
      headerText: i18n.t('配送费')
    },
    {
      field: 'otherFeeCurrencyName',
      headerText: i18n.t('其他费用币种'),
      valueAccessor: (field, data) => {
        return data['otherFeeCurrencyCode'] + '-' + data['otherFeeCurrencyName']
      }
    },
    {
      field: 'agencyFee',
      headerText: i18n.t('代理费')
    },
    {
      field: 'usdTotalFeeSum',
      headerText: i18n.t('总计费用USD')
    },
    {
      field: 'cnyTotalFeeSum',
      headerText: i18n.t('总计费用RMB')
    },
    {
      field: 'exchangeRate',
      headerText: i18n.t('汇率')
    },
    {
      field: 'sourceCode',
      headerText: i18n.t('来源单号')
    },
    {
      field: 'purchaseAgentName',
      headerText: i18n.t('创建人')
    },
    {
      field: 'createTime',
      headerText: i18n.t('创建时间')
    }
  ]
]
// 物流价格记录 - 配置
export const logisticsPageConfigFn = (i) => [
  {
    title: i18n.t('物流价格库'),
    // gridId: '49cde155-0bda-44d6-83ff-8fada05dc362',
    activatedRefresh: false,
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    useToolTemplate: false,
    customSearchExpandMinRows: 2,
    toolbar: {
      useBaseConfig: false,
      tools: [
        [{ id: 'export', icon: 'icon_solid_export', title: i18n.t('导出') }],
        ['Refresh', 'Setting']
      ]
    },
    grid: {
      virtualPageSize: 30,
      enableVirtualization: true,
      customSelection: true,
      selectionSettings: {
        persistSelection: true,
        type: 'Multiple',
        checkboxOnly: true
      },
      pageSettings: {
        currentPage: 1,
        pageSize: 20,
        pageSizes: [20, 50, 100, 200, 1000],
        totalRecordsCount: 0
      },
      allowFiltering: true,
      showSelected: false,
      columnData: logisticsColumnData[i],
      asyncConfig: {
        url: '/price/tenant/price/logistics/pageQuery',
        params: { logisticsType: logisticsTypeArr[i] }
      }
    }
  }
]
// odmin价格记录 - 列信息
export const odminColumnData = [
  {
    field: 'id',
    width: 0,
    isPrimaryKey: true
  },
  {
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: syncStatusList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'syncMsg',
    headerText: i18n.t('同步信息')
  },
  {
    field: 'priceRecordCode',
    headerText: i18n.t('询价单号')
  },
  {
    field: 'itemCode',
    headerText: i18n.t('单据名称')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司'),
    width: '200',
    valueAccessor: (field, data) => {
      if (Array.isArray(data.companyList) && data.companyList.length) {
        return data.companyList[0]['companyCode'] + '-' + data.companyList[0]['companyName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: '200',
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.siteList[0]['siteCode'] + '-' + data.siteList[0]['siteName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织'),
    ignore: true,
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.siteList[0]['purOrgCode'] + '-' + data.siteList[0]['purOrgName']
      } else {
        return '-'
      }
    }
  },

  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料描述')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'odf',
    headerText: i18n.t('ODF号')
  },
  {
    field: 'htbh',
    headerText: i18n.t('合同编号')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.supplierCode + '-' + data.supplierName
      } else {
        return '-'
      }
    }
  },
  // {
  //   field: 'status',
  //   headerText: i18n.t('状态'),
  //   valueConverter: {
  //     type: 'map',
  //     map: statusList,
  //     fields: { text: 'text', value: 'value' }
  //   }
  // },
  {
    field: 'purchaseAgentName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  }
]
// ODMIN价格记录 - 配置
export const odminPageConfigFn = (i) => [
  {
    title: i18n.t('物流价格库'),
    // gridId: '49cde155-0bda-44d6-83ff-8fada05dc362',
    activatedRefresh: false,
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    useToolTemplate: false,
    customSearchExpandMinRows: 2,
    toolbar: {
      useBaseConfig: false,
      tools: [
        [{ id: 'export', icon: 'icon_solid_export', title: i18n.t('导出') }],
        ['Refresh', 'Setting']
      ]
    },
    grid: {
      virtualPageSize: 30,
      enableVirtualization: true,
      customSelection: true,
      selectionSettings: {
        persistSelection: true,
        type: 'Multiple',
        checkboxOnly: true
      },
      pageSettings: {
        currentPage: 1,
        pageSize: 20,
        pageSizes: [20, 50, 100, 200, 1000],
        totalRecordsCount: 0
      },
      allowFiltering: true,
      showSelected: false,
      columnData: odminColumnData,
      asyncConfig: {
        url: '/price/tenant/odmin/priceRecord/pageQuery',
        params: { logisticsType: logisticsTypeArr[i] }
      }
    }
  }
]
// smt价格记录
export const smtPriceColumnData = [
  {
    field: 'id',
    width: 0,
    isPrimaryKey: true
  },
  {
    field: 'ckdFlag',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: ckdFlagList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'ckdStSyncMsg',
    headerText: i18n.t('同步信息')
  },
  {
    field: 'ckdStRecordNo',
    headerText: i18n.t('信息记录号')
  },
  {
    field: 'priceRecordCode',
    headerText: i18n.t('价格编码')
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料描述')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司'),
    width: '200',
    valueAccessor: (field, data) => {
      if (Array.isArray(data.companyList) && data.companyList.length) {
        return data.companyList[0]['companyCode'] + '-' + data.companyList[0]['companyName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: '200',
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.siteList[0]['siteCode'] + '-' + data.siteList[0]['siteName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织'),
    ignore: true,
    valueAccessor: (field, data) => {
      if (Array.isArray(data.siteList) && data.siteList.length) {
        return data.siteList[0]['purOrgCode'] + '-' + data.siteList[0]['purOrgName']
      } else {
        return '-'
      }
    }
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: statusList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'quoteAttribute',
    headerText: i18n.t('报价属性'),
    valueConverter: {
      type: 'map',
      map: quoteAttributeList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'quoteMode',
    headerText: i18n.t('价格生效方式'),
    valueConverter: {
      type: 'map',
      map: quoteModeList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'deliveryPlace',
    headerText: i18n.t('直送地'),
    ignore: true
  },
  {
    field: 'stepValue',
    headerText: i18n.t('阶梯数量')
  },
  {
    field: 'ckdStPrice',
    headerText: i18n.t('单价(未税)')
  },
  {
    field: 'ckdStPriceTaxed',
    headerText: i18n.t('单价(含税)')
  },
  {
    field: 'unitName',
    headerText: i18n.t('基本单位')
  },
  {
    field: 'purUnitName',
    headerText: i18n.t('订单单位')
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种'),
    valueAccessor: (field, row) => {
      return row.currencyCode ? row.currencyCode + '-' + row.currencyName : ''
    }
  },
  {
    field: 'taxCode',
    headerText: i18n.t('税率编码')
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('生效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('失效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'sourceCode',
    headerText: i18n.t('来源单号')
  },
  {
    field: 'purchaseAgentName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    field: 'purchaseInfoRecordNo',
    headerText: i18n.t('外发信息记录编号')
  },
  {
    field: 'innerCounter',
    headerText: i18n.t('序列号')
  },
  {
    field: 'priceValidStartTime',
    headerText: i18n.t('初始有效开始时间')
  },
  {
    field: 'priceValidEndTime',
    headerText: i18n.t('初始有效结束时间')
  }
]
// SMT价格记录 - 配置
export const smtPageConfig = [
  {
    title: i18n.t('外发阶梯'),
    gridId: 'c6c445ec-03b9-4d9c-ad39-fe46413cecd8',
    activatedRefresh: false,
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    useToolTemplate: false,
    customSearchExpandMinRows: 2,
    toolbar: {
      useBaseConfig: false,
      tools: [
        [
          { id: 'importSAP', icon: 'icon_solid_upload', title: i18n.t('导入SAP') },
          {
            id: 'download',
            icon: 'icon_solid_export',
            title: i18n.t('导出'),
            permission: ['O_02_1708']
          }
        ],
        ['Refresh', 'Setting']
      ]
    },
    grid: {
      virtualPageSize: 30,
      enableVirtualization: true,
      customSelection: true,
      selectionSettings: {
        persistSelection: true,
        type: 'Multiple',
        checkboxOnly: true
      },
      pageSettings: {
        currentPage: 1,
        pageSize: 20,
        pageSizes: [20, 50, 100, 200, 1000],
        totalRecordsCount: 0
      },
      allowFiltering: true,
      showSelected: false,
      columnData: smtPriceColumnData,
      asyncConfig: {
        url: '/price/tenant/priceRecord/new/pageQuerySmtCkdPackage'
      }
    }
  }
]
