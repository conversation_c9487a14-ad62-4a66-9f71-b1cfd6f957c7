<template>
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.status"
            :data-source="logisticsSeaStatusList"
            :show-clear-button="true"
            :placeholder="$t('请选择状态')"
          />
        </mt-form-item>
        <mt-form-item prop="priceRecordCode" :label="$t('价格记录编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.priceRecordCode"
            :show-clear-button="true"
            :placeholder="$t('请输入价格记录编码')"
          />
        </mt-form-item>
        <mt-form-item prop="sourceCode" :label="$t('询价单号')" label-style="top">
          <mt-input
            v-model="searchFormModel.sourceCode"
            :show-clear-button="true"
            :placeholder="$t('请输入询价单号')"
          />
        </mt-form-item>
        <mt-form-item prop="sourceName" :label="$t('单据名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.sourceName"
            :show-clear-button="true"
            :placeholder="$t('单据名称')"
          />
        </mt-form-item>
        <mt-form-item prop="syncStatus" :label="$t('同步状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.syncStatus"
            :data-source="syncStatusList"
            :show-clear-button="true"
            :placeholder="$t('请选择同步状态')"
          />
        </mt-form-item>

        <mt-form-item prop="companyCodeList" :label="$t('公司编码')" label-style="top">
          <custom-select
            v-model="searchFormModel.companyCodeList"
            :label-name="$t('公司编码')"
            :placeholder="$t('请输入公司编码')"
          />
        </mt-form-item>
        <!-- <mt-form-item prop="companyName" :label="$t('公司名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.companyName"
            :show-clear-button="true"
            :placeholder="$t('请输入公司名称')"
          />
        </mt-form-item> -->
        <mt-form-item prop="categoryCodeList" :label="$t('品类编码')" label-style="top">
          <custom-select
            v-model="searchFormModel.categoryCodeList"
            :label-name="$t('品类编码')"
            :placeholder="$t('请输入品类编码')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierCodeList" :label="$t('供应商编码')" label-style="top">
          <custom-select
            v-model="searchFormModel.supplierCodeList"
            :label-name="$t('供应商编码')"
            :placeholder="$t('请输入供应商编码')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.supplierName"
            :show-clear-button="true"
            :placeholder="$t('请输入供应商名称')"
          />
        </mt-form-item>
        <mt-form-item prop="syncStatus" :label="$t('铁运模式')" label-style="top">
          <mt-input
            v-model="searchFormModel.priceRecordCode"
            :show-clear-button="true"
            :placeholder="$t('请输入铁运模式')"
          />
        </mt-form-item>
        <mt-form-item prop="validStartTime" :label="$t('价格生效日期')">
          <mt-date-picker
            v-model="searchFormModel.validStartTime"
            :open-on-focus="true"
            :allow-edit="false"
            time-stamp
            format="yyyy-MM-dd"
            :placeholder="$t('请选择生效日期')"
            @change="(e) => handleDateChange('validStartTime', e)"
          />
        </mt-form-item>
        <mt-form-item prop="validEndTime" :label="$t('价格失效日期')">
          <mt-date-picker
            v-model="searchFormModel.validEndTime"
            :open-on-focus="true"
            :allow-edit="false"
            time-stamp
            format="yyyy-MM-dd"
            :placeholder="$t('请选择价格失效日期')"
            @change="(e) => handleDateChange('validEndTime', e)"
          />
        </mt-form-item>

        <mt-form-item prop="startAddr" :label="$t('起运地')" label-style="top">
          <mt-input
            v-model="searchFormModel.startAddr"
            :show-clear-button="true"
            :placeholder="$t('请输入起运地')"
          />
        </mt-form-item>
        <mt-form-item prop="loadingAddr" :label="$t('装货地')" label-style="top">
          <mt-input
            v-model="searchFormModel.loadingAddr"
            :show-clear-button="true"
            :placeholder="$t('请输入装货地')"
          />
        </mt-form-item>
        <mt-form-item prop="domesticOriginStation" :label="$t('国内始发站')" label-style="top">
          <mt-input
            v-model="searchFormModel.domesticOriginStation"
            :show-clear-button="true"
            :placeholder="$t('请输入国内始发站')"
          />
        </mt-form-item>
        <mt-form-item prop="domesticDestinationStation" :label="$t('国内目的站')" label-style="top">
          <mt-input
            v-model="searchFormModel.domesticDestinationStation"
            :show-clear-button="true"
            :placeholder="$t('请输入国内目的站')"
          />
        </mt-form-item>
        <mt-form-item prop="destSite" :label="$t('目的站')" label-style="top">
          <mt-input
            v-model="searchFormModel.destSite"
            :show-clear-button="true"
            :placeholder="$t('请输入目的站')"
          />
        </mt-form-item>
        <mt-form-item prop="containersSite" :label="$t('提箱地点')" label-style="top">
          <mt-input
            v-model="searchFormModel.containersSite"
            :show-clear-button="true"
            :placeholder="$t('请输入提箱地点')"
          />
        </mt-form-item>
        <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
          <mt-input
            v-model="searchFormModel.createUserName"
            :show-clear-button="true"
            :placeholder="$t('请输入创建人')"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :allow-edit="false"
            :placeholder="$t('请选择创建时间')"
            :open-on-focus="true"
            @change="handleDateChange"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      grid-id="f24872aa-f8ee-44c9-8797-90032f1517fe"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="listColumns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
// import { getTimeList } from '@/utils/obj'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import {
  logisticsToolbar,
  syncStatusList,
  logisticsSeaStatusList,
  requirementTypeList
} from './config/index'
import XEUtils from 'xe-utils'
import CustomSelect from '@/components/customSelect'
export default {
  components: {
    ScTable,
    CollapseSearch,
    CustomSelect
  },
  mixins: [mixin],
  data() {
    return {
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 20,
        pageSizes: [20, 50, 200, 500, 1000, 2000]
      },
      pageInfo: {
        size: 20,
        current: 1
      },

      syncStatusList,
      logisticsSeaStatusList,
      requirementTypeList,
      toolbar: logisticsToolbar,
      searchFormModel: {
        createUserName: JSON.parse(sessionStorage.getItem('userInfo'))?.username || null
        // createTime: getTimeList(3)
      },
      tableData: [],
      loading: false,
      type: 'list',
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.cancel,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 使用组件中的toolbar配置
          toolbar: [],
          grid: {
            columnData: [
              {
                width: '150',
                field: 'fieldCode',
                headerText: this.$t('月份')
              },
              {
                width: '150',
                field: 'fieldData',
                headerText: this.$t('柜量')
              }
            ],
            dataSource: [],
            allowPaging: false,
            asyncConfig: {}
          }
        }
      ]
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    // 列配置
    listColumns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          width: 80,
          field: 'index',
          title: this.$t('序号')
        },
        {
          field: 'status',
          title: this.$t('状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = this.logisticsSeaStatusList.find(
                (item) => item.value === row.status
              )
              const status = selectItem?.text
              return [<div>{status}</div>]
            }
          }
        },
        {
          field: 'priceRecordCode',
          title: this.$t('价格记录编码'),
          minWidth: 120
        },
        {
          field: 'syncStatus',
          title: this.$t('同步状态'),
          minWidth: 100,
          slots: {
            default: ({ row }) => {
              const selectItem = this.syncStatusList.find((item) => item.value === row.syncStatus)
              const syncStatus = selectItem?.text
              return [<div>{syncStatus}</div>]
            }
          }
        },
        {
          field: 'syncInfo',
          title: this.$t('同步信息'),
          minWidth: 100
        },
        {
          field: 'sourceCode',
          title: this.$t('询价单号'),
          minWidth: 180,
          slots: {
            default: ({ row, column }) => {
              return [
                <a on-click={() => this.handleClickCellTitle(row, column)}>{row.sourceCode}</a>
              ]
            }
          }
        },
        {
          field: 'sourceName',
          title: this.$t('单据名称'),
          minWidth: 140
        },
        {
          field: 'companyCode',
          title: this.$t('公司'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [<span>{row.companyCode + '-' + row.companyName}</span>]
            }
          }
        },
        {
          field: 'railwayModeName',
          title: this.$t('铁运模式'),
          minWidth: 120
        },
        {
          field: 'startAddr',
          title: this.$t('起运地'),
          minWidth: 120
        },
        {
          field: 'loadingAddr',
          title: this.$t('装货地'),
          minWidth: 120
        },
        {
          field: 'domesticOriginStation',
          title: this.$t('国内始发站'),
          minWidth: 120
        },
        {
          field: 'domesticDestinationStation',
          title: this.$t('国内目的站'),
          minWidth: 120
        },
        {
          field: 'destSite',
          title: this.$t('目的站'),
          minWidth: 120
        },
        {
          field: 'estimateContainerQty',
          title: this.$t('预估柜量'),
          minWidth: 120
        },
        {
          field: 'containersSite',
          title: this.$t('提箱地点'),
          minWidth: 120
        },
        {
          field: 'requirementType',
          title: this.$t('需求类型'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = this.requirementTypeList.find(
                (item) => item.value === row.requirementType
              )
              const requirementType = selectItem?.text
              return [<div>{requirementType}</div>]
            }
          }
        },
        {
          field: 'logisticsMethodName',
          title: this.$t('运输方式'),
          minWidth: 120
        },
        {
          field: 'requireTimeliness',
          title: this.$t('需求时效性'),
          minWidth: 120
        },
        {
          field: 'categoryCode',
          title: this.$t('品类编码'),
          minWidth: 120
        },
        {
          field: 'categoryName',
          title: this.$t('品类名称')
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商编码'),
          minWidth: 120
        },
        {
          minWidth: 120,
          field: 'supplierName',
          title: this.$t('供应商名称')
        },
        {
          field: 'validStartTime',
          title: this.$t('价格开始日期'),
          minWidth: 140
        },
        {
          field: 'validEndTime',
          title: this.$t('价格结束日期'),
          minWidth: 140
        },
        {
          field: 'actualTimeliness',
          title: this.$t('实际时效性'),
          minWidth: 140
        },
        {
          field: 'currencyCode',
          title: this.$t('币种'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [<span>{row.currencyCode + '-' + row.currencyName}</span>]
            }
          }
        },
        {
          field: 'rentalContainerFee',
          title: this.$t('租箱费'),
          minWidth: 140
        },
        {
          field: 'trailerFee',
          title: this.$t('拖车费'),
          minWidth: 140
        },
        {
          field: 'domesticRailwayFreightFee',
          title: this.$t('内贸铁路运费'),
          minWidth: 140
        },
        {
          field: 'railwayFreightFee',
          title: this.$t('铁路运费'),
          minWidth: 140
        },
        {
          field: 'replacementStorageFee',
          title: this.$t('换装入库费'),
          minWidth: 140
        },
        {
          field: 'qbjAroundFee',
          title: this.$t('青白江绕园费'),
          minWidth: 140
        },
        {
          field: 'declareCustomsFee',
          title: this.$t('报关费'),
          minWidth: 140
        },
        {
          field: 'insurance',
          title: this.$t('保险费'),
          minWidth: 140
        },
        {
          field: 'totalFee',
          title: this.$t('总计'),
          minWidth: 140
        },
        {
          field: 'detentionOverSixtyDayFee',
          title: this.$t('SOC超免箱期60天后费用标准'),
          minWidth: 140
        },
        {
          field: 'vehicleFee',
          title: this.$t('压车费'),
          minWidth: 140
        },
        {
          field: 'returnFee',
          title: this.$t('返空费'),
          minWidth: 140
        },
        {
          field: 'otherFee',
          title: this.$t('其他费用'),
          minWidth: 140
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          minWidth: 140
        }
      ]
    }
  },
  mounted() {
    this.handleSearch()
  },
  activated() {
    this.handleSearch()
  },
  methods: {
    // 日期格式化
    handleDateChange(e) {
      if (e.startDate) {
        const startDate = XEUtils.toDateString(e.startDate, 'yyyy-MM-dd') + ' 00:00:00'
        const endDate = XEUtils.toDateString(e.endDate, 'yyyy-MM-dd') + ' 23:59:59'
        this.searchFormModel['createStartTime'] = XEUtils.timestamp(startDate)
        this.searchFormModel['createEndTime'] = XEUtils.timestamp(endDate)
      } else {
        this.searchFormModel['createStartTime'] = null
        this.searchFormModel['createEndTime'] = null
      }
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    // 查询
    async handleSearch(type) {
      // 解决修改查询条件后，查询结果有数据但未正常显示的问题（type: pageChange/refresh）
      if (!type) {
        this.pageInfo.current = 1
        this.$refs.pageRef.jumpNum = 1
        this.$refs.pageRef.currentPage = 1
      }
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      delete params.submitTime
      this.loading = true
      const res = await this.$API.priceService
        .getRailwayAnnualPricingRecords(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.tableData?.forEach((item, index) => {
          item.index = index + 1
        }) || []
        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (['syncTms'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      switch (e.code) {
        case 'export':
          this.handleExport()
          break
        case 'syncTms':
          this.syncTms(selectedRecords)
          break
        default:
          break
      }
    },

    // 导出
    async handleExport() {
      const params = {
        page: { current: 1, size: 10000 },
        ...this.searchFormModel
      }
      const res = await this.$API.priceService.exportRailwayAnnual(params)
      if (res.data) {
        this.$toast({
          type: 'success',
          content: this.$t('正在导出，请稍后！')
        })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      }
    },
    // 同步TMS
    async syncTms(list) {
      const ids = list.map((i) => i.id)
      const res = await this.$API.priceService.railwayAnnualSyncTMS({ ids })
      if (res.code === 200) {
        this.$toast({ content: res.msg, type: 'success' })
        this.handleSearch()
      }
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.handleSearch('pageChange')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.handleSearch()
    },
    // 显示柜量明细
    showFiledVolist(data) {
      this.$refs.filedVolistRef.ejsRef.show()
      this.$set(this.pageConfig[0].grid, 'dataSource', data?.cabinetQuantity || [])
    },
    cancel() {
      this.$refs.filedVolistRef.ejsRef.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
