<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    header="申请"
    :style="{ width: '50vw' }"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="productLineCode" :label="$t('产品线')">
          <mt-select
            v-model="formObject.productLineCode"
            :data-source="productLineList"
            @change="productLineChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="companyCode" :label="$t('公司')">
          <remote-autocomplete
            v-model="formObject.companyCode"
            url="/masterDataManagement/tenant/organization/specified-level-paged-query"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            :params="{
              organizationLevelCodes: ['ORG01', 'ORG02']
            }"
            @change="companyChange"
            @getOptions="getCompanyOptions"
          />
        </mt-form-item>

        <!-- <mt-form-item prop="materialCode" :label="$t('物料编码')">
          <mt-select
            v-model="formObject.materialCode"
            :data-source="itemOptions"
            :allow-filtering="true"
            :show-clear-button="true"
            filter-type="Contains"
            :fields="{ text: 'text', value: 'value' }"
            @change="materialChange"
          ></mt-select>
        </mt-form-item> -->

        <!-- <mt-form-item prop="materialName" :label="$t('物料描述')">
          <mt-input v-model="formObject.materialName" :disabled="true" />
        </mt-form-item> -->
        <!-- 
        <mt-form-item prop="supplierCode" :label="$t('供应商')">
          <mt-select
            v-model="formObject.supplierCode"
            :data-source="supplierOptions"
            :allow-filtering="true"
            filter-type="Contains"
            :fields="{ text: 'text', value: 'value' }"
            @change="supplierChange"
          ></mt-select>
        </mt-form-item> -->

        <mt-form-item prop="applyPermissions" :label="$t('申请权限') + '(多选)'">
          <mt-multi-select
            v-model="formObject.applyPermissions"
            :data-source="permissionOptions"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>

        <mt-form-item prop="date" :label="$t('申请日期')">
          <mt-input v-model="formObject.date" :disabled="true" />
        </mt-form-item>

        <mt-form-item prop="applicantName" :label="$t('申请人')">
          <mt-input v-model="formObject.applicantName" :disabled="true" />
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import dayjs from 'dayjs'
import { utils } from '@mtech-common/utils'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
export default {
  components: { RemoteAutocomplete },
  data() {
    return {
      productLineList: [
        { text: this.$t('电冰箱'), value: '1' },
        { text: this.$t('洗衣机'), value: '2' }
      ],
      itemOptions: [],
      supplierOptions: [],
      companyOptions: [],
      permissionOptions: [
        { text: this.$t('上传'), value: 1 },
        { text: this.$t('查看'), value: 2 },
        { text: this.$t('删除'), value: 3 },
        { text: this.$t('导出'), value: 4 }
      ],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: true, content: this.$t('确定') }
        }
      ],
      formObject: {
        applicantCode: null,
        applicantName: null,
        applyPermissions: [1, 2, 3, 4],
        companyCodeAndNames: null,
        supplierCode: null,
        supplierName: null,
        productLineName: '',
        productLineCode: '',
        materialCode: null,
        materialName: null,
        // materialDesc: null,
        date: null
      },
      formRules: {
        productLineCode: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ],
        companyCode: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ],
        applyPermissions: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ]
      },
      selectedMaterialItem: {},
      editStatus: false
    }
  },
  mounted() {
    this.getItemDataSource = utils.debounce(this.getItemCodeList, 1000)
    this.$refs['dialog'].ejsRef.show()

    this.formObject.date = dayjs().format('YYYY-MM-DD')
    this.formObject.applicantName = this.$store.state.userInfo.accountName
    this.formObject.applicantCode = this.$store.state.userInfo.employeeCode

    // this.getItemCodeList()
    this.getSupplierList()
  },
  methods: {
    materialItemRemoteMethod() {
      return this.$API.masterData.getItemList().then((res) => res.data)
    },
    materialChange(e) {
      if (e.itemData) {
        this.formObject['materialCode'] = e.itemData.itemCode
        this.formObject['materialName'] = e.itemData.itemName
        // this.formObject['materialDesc'] = e.itemData.itemDescription
      } else {
        this.formObject['materialCode'] = null
        this.formObject['materialName'] = null
        this.formObject['materialDesc'] = null
      }
    },
    supplierChange(e) {
      if (e.itemData) {
        this.formObject['supplierCode'] = e.itemData.supplierCode
        this.formObject['supplierName'] = e.itemData.supplierName
      } else {
        this.formObject['supplierCode'] = null
        this.formObject['supplierName'] = null
      }
      console.log(this.formObject, 'formObject')
    },
    companyChange(e) {
      const values = e.value
      if (values && values.length) {
        this.formObject.companyCodeAndNames = []
        this.companyOptions.forEach((i) => {
          if (values.includes(i.orgCode)) {
            this.formObject.companyCodeAndNames.push(i.orgCode + ':' + i.orgName)
          }
        })
        console.log(this.formObject.companyCodeAndNames, 'this.formObject.companyCodeAndNames')
      } else {
        this.formObject.companyCodeAndNames = []
      }
    },
    productLineChange(e) {
      if (e.itemData) {
        this.formObject['productLineCode'] = e.itemData.value
        this.formObject['productLineName'] = e.itemData.text
      } else {
        this.formObject['productLineCode'] = null
        this.formObject['productLineName'] = null
      }
    },
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = { ...this.formObject }
          delete params.date
          delete params.companyCode
          this.$API.priceService.applyPermissionApi(params).then((res) => {
            if (res.code == 200) {
              this.$emit('confirm-function')
              this.$toast({ content: this.$t('权限申请成功'), type: 'success' })
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    getCompanyOptions(list) {
      this.companyOptions = list
    },
    // 物料下拉
    getItemCodeList() {
      this.$API.masterData.getItemList().then((res) => {
        const list = res.data || []
        const newData = []
        list.forEach((element) => {
          let repeat = newData.find((i) => i.value === element.itemCode)
          if (!repeat) {
            newData.push({
              ...element,
              text: `${element.itemCode}-${element.itemName}`,
              value: element.itemCode
            })
          }
        })
        this.itemOptions = [...newData]
      })
    },
    getSupplierList() {
      this.$API.masterData.getSupplierList().then((res) => {
        const list = res.data || []
        const newData = []
        list.forEach((element) => {
          let repeat = newData.find((i) => i.value === element.itemCode)
          if (!repeat) {
            newData.push({
              ...element,
              text: element.supplierName,
              value: element.supplierCode
            })
          }
        })
        this.supplierOptions = [...newData]
      })
    }
  }
}
</script>

<style scoped>
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
.predict-vxe-list-empty-item {
  text-align: center;
  padding: 10px;
}
</style>
