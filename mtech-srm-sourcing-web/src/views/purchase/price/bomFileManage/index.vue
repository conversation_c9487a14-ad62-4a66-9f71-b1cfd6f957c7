<!-- BOM文件管理 -->
<template>
  <div>
    <div v-if="notCtrl && !checkPermission">
      <div class="not-ctrl">
        当前页面未配置权限，请填写<span class="click-text" @click="handleShowDialog"
          >开通权限申请</span
        >，谢谢！
      </div>
    </div>
    <collapse-search
      v-if="!notCtrl && !checkPermission"
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item prop="materialCode" :label="$t('物料编码')">
          <mt-input
            v-model="searchFormModel.materialCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>

        <mt-form-item prop="device" :label="$t('器件名称')">
          <mt-input
            v-model="searchFormModel.device"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>

        <mt-form-item prop="device" :label="$t('产品线')">
          <mt-select
            v-model="searchFormModel.productLineCode"
            :data-source="productLineList"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>

        <mt-form-item prop="fileName" :label="$t('文件名')">
          <mt-input
            v-model="searchFormModel.fileName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>

        <mt-form-item prop="supplierCode" :label="$t('供应商代码')">
          <mt-input
            v-model="searchFormModel.supplierCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>

        <mt-form-item prop="factory" :label="$t('工厂编码')">
          <mt-input
            v-model="searchFormModel.factory"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>

        <mt-form-item :label="$t('品牌第一供方')" prop="supplierName1">
          <mt-input
            v-model="searchFormModel.supplierName1"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>

        <mt-form-item :label="$t('品牌第二供方')" prop="supplierName2">
          <mt-input
            v-model="searchFormModel.supplierName2"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      v-if="!notCtrl && !checkPermission"
      ref="sctableRef"
      grid-id="b75742b8-da36-4f28-b668-5d7fee070817"
      class="myTable-class"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      keep-source
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          v-permission="item.permission"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
    <mt-page
      v-if="!notCtrl && !checkPermission"
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
// import { API } from '@mtech-common/http'
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, productLineList } from './config'
import { getHeadersFileName, download } from '@/utils/utils'
// import { cloneDeep } from 'lodash'

export default {
  components: { CollapseSearch, ScTable },
  data() {
    return {
      checkPermission: true,
      notCtrl: false,
      searchFormModel: {},
      searchFormRules: {},
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],
      editRules: {},
      rulesCfgData: [], // 类型定义配置
      siteOptions: [], // 工厂下拉选项
      itemOptions: [],
      personOptions: [],
      getItemDataSource: () => {},
      productLineList
    }
  },
  computed: {
    toolbar() {
      return [
        {
          code: 'import',
          name: this.$t('上传文件'),
          status: 'info',
          loading: false,
          permission: ['O_02_1760']
        },
        {
          code: 'delete',
          name: this.$t('删除'),
          status: 'info',
          loading: false,
          permission: ['O_02_1761']
        },
        {
          code: 'export',
          name: this.$t('导出'),
          status: 'info',
          loading: false,
          permission: ['O_02_1762']
        }
      ]
    },
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.checkPermission = true
    this.getTableData()
  },
  methods: {
    dateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[field + 'End'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          this.currentPage = 1
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    handleShowDialog() {
      this.$dialog({
        modal: () => import('./components/applyPermission.vue'),
        data: {
          data: {},
          title: this.$t('权限申请')
        },
        success: () => {
          this.notCtrl = false
          this.checkPermission = true
          this.getTableData()
        }
      })
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.priceService.pageDataApi(params).catch((err) => {
        this.loading = false
        if (err.msg && Object.prototype.toString.call(err.msg) === '[object String]') {
          if (err.msg.indexOf('权限不足') > -1) {
            this.notCtrl = true
          }
        }
      })
      this.checkPermission = false
      this.loading = false
      if (res && res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()

      const updateRecords = this.tableRef.getUpdateRecords()
      const insertRecords = this.tableRef.getInsertRecords()
      if (e.code === 'save' && !updateRecords.length && !insertRecords.length) {
        this.$toast({ content: this.$t('暂无需要保存的数据'), type: 'warning' })
        return
      }

      // if (e.code === 'closeEdit' && !updateRecords.length && !insertRecords.length) {
      //   this.$toast({ content: this.$t('暂未编辑任何数据'), type: 'warning' })
      //   return
      // }

      switch (e.code) {
        case 'delete':
          this.handleDelete(selectedRecords)
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.priceService.importDataApi,
          downloadTemplateApi: this.$API.priceService.templateDownloadApi,
          paramsKey: 'file'
        },
        success: () => {
          this.handleSearch()
        }
      })
    },
    handleExport(e) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$store.commit('startLoading')
      this.$API.priceService
        .exportDataApi(params)
        .then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    },
    handleDelete(selected) {
      if (!selected || !selected.length) {
        return this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确定删除？')
        },
        success: async () => {
          this.$store.commit('startLoading')
          let ids = selected.map((i) => i.id)
          this.$API.priceService.deleteDataApi(ids).then((res) => {
            this.$store.commit('endLoading')
            if (res.code === 200) {
              this.$toast({ content: this.$t('删除成功'), type: 'success' })
              this.handleSearch()
            }
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.not-ctrl {
  margin-top: 50px;
  padding: 30px;
}
.click-text {
  color: #409eff;
  cursor: pointer;
  text-decoration: underline;
}
</style>
