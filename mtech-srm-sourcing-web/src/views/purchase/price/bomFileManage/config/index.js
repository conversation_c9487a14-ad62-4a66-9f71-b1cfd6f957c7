import { i18n } from '@/main.js'
export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left'
  },
  {
    field: 'createUserName',
    title: i18n.t('上传人'),
    minWidth: 100
  },
  {
    field: 'createTime',
    title: i18n.t('上传时间'),
    minWidth: 150
  },
  {
    field: 'productLineName',
    title: i18n.t('产品线'),
    minWidth: 130
  },
  {
    field: 'fileName',
    title: i18n.t('来源文件名'),
    minWidth: 160
  },
  {
    field: 'materialCode',
    title: i18n.t('物料编码'),
    minWidth: 130
  },
  {
    field: 'materialName',
    title: i18n.t('物料描述'),
    minWidth: 150
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商代码'),
    minWidth: 130
  },
  {
    field: 'bitNumber',
    title: i18n.t('位号'),
    minWidth: 130
  },
  {
    field: 'device',
    title: i18n.t('器件名称'),
    minWidth: 150
  },
  {
    field: 'specModel',
    title: i18n.t('规格型号'),
    minWidth: 100
  },
  {
    field: 'factory',
    title: i18n.t('工厂编码'),
    minWidth: 130
  },
  {
    field: 'supplierName1',
    title: i18n.t('品牌-第一供方'),
    minWidth: 150
  },
  {
    field: 'supplierName2',
    title: i18n.t('品牌-第二供方'),
    minWidth: 150
  },
  {
    field: 'supplierName3',
    title: i18n.t('品牌-第三供方'),
    minWidth: 150
  },
  {
    field: 'dosage',
    title: i18n.t('用量'),
    minWidth: 100
  },
  {
    field: 'unit',
    title: i18n.t('单位'),
    minWidth: 100
  },
  {
    field: 'workProcessName',
    title: i18n.t('工序'),
    minWidth: 130
  },
  {
    field: 'mainAuxiliaryMaterials',
    title: i18n.t('主/辅料'),
    minWidth: 130
  },
  {
    field: 'describe',
    title: i18n.t('L/T'),
    minWidth: 100
  },
  {
    field: 'currency',
    title: i18n.t('通用程度'),
    minWidth: 100
  },
  {
    field: 'estimatedPrice',
    title: i18n.t('预估未税单价'),
    minWidth: 130
  },
  {
    field: 'remark',
    title: i18n.t('备注'),
    minWidth: 150
  }
]

export const productLineList = [
  { value: '1', text: i18n.t('冰箱') },
  { value: '2', text: i18n.t('洗衣机') }
]
