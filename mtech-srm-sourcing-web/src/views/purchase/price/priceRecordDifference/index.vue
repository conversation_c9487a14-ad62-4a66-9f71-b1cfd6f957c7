<!-- 价格记录差异 -->
<template>
  <div class="full-height">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item prop="diffDate" :label="$t('差异日期')" label-style="top">
          <mt-date-picker
            v-model="searchFormModel.diffDate"
            :open-on-focus="true"
            :allow-edit="false"
            format="yyyy-MM-dd"
            :placeholder="$t('请选择差异日期')"
          />
        </mt-form-item>
        <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.companyCode"
            url="/masterDataManagement/auth/company/auth-fuzzy"
            :params="{
              organizationLevelCodes: ['ORG02', 'ORG01'],
              orgType: 'ORG001PRO',
              includeItself: true
            }"
            :placeholder="$t('请选择公司')"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            records-position="data"
          />
        </mt-form-item>
        <mt-form-item prop="factoryCode" :label="$t('工厂')" label-style="top">
          <mt-select
            v-model="searchFormModel.factoryCode"
            css-class="rule-element"
            :data-source="factoryList"
            :fields="{ text: 'text', value: 'siteCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :filtering="onFiltering"
            :placeholder="$t('请选择工厂')"
          />
        </mt-form-item>
        <mt-form-item prop="purchaseOrgCode" :label="$t('采购组织')" label-style="top">
          <mt-input
            v-model="searchFormModel.purchaseOrgCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商编码')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCode"
            url="/masterDataManagement/tenant/supplier/paged-query"
            :placeholder="$t('请选择供应商')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          />
        </mt-form-item>
        <mt-form-item prop="materialCode" :label="$t('物料编码')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.materialCode"
            :url="$API.masterData.getItemListUrlPage"
            :placeholder="$t('请选择物料')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :search-fields="['itemName', 'itemCode']"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      row-id="id"
      grid-id="326e1534-eff2-49c8-b0c3-24f356541ff7"
      :keep-source="true"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <!-- <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    /> -->
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import mixin from './config/mixin'
import pagingMixin from '@/mixins/paging.js'
import { download, getHeadersFileName } from '@/utils/utils'
import debounce from 'lodash.debounce'

export default {
  components: {
    CollapseSearch,
    ScTable,
    RemoteAutocomplete
  },
  mixins: [mixin, pagingMixin],
  data() {
    return {
      searchFormModel: {
        diffDate: new Date()
      },
      searchFormRules: {
        diffDate: [{ required: true, message: this.$t('请选择差异日期'), trigger: 'blur' }]
      },
      tableData: [],
      loading: false
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  methods: {
    // 模糊查询-工厂
    onFiltering: debounce(function (e) {
      this.getFactoryList(e?.text)
    }, 800),
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = ['startCreatTime', 'endCreatTime'].includes(key) ? 0 : null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          this.currentPage = 1
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    // 获取table数据
    async getTableData() {
      const params = {
        ...this.searchFormModel
      }
      params.diffDate = dayjs(params.diffDate).format('YYYYMMDD')
      this.loading = true
      const res = await this.$API.priceService
        .pageDiffApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      switch (e.code) {
        case 'export':
          this.$refs.searchFormRef.validate((valid) => {
            if (valid) {
              this.handleExport()
            } else {
              this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
            }
          })
          break
        default:
          break
      }
    },
    // 导出
    async handleExport() {
      const params = {
        ...this.searchFormModel
      }
      params.diffDate = dayjs(params.diffDate).format('YYYYMMDD')
      const res = await this.$API.priceService.exportDiffApi(params)
      if (res.data) {
        this.$toast({ type: 'success', content: this.$t('正在导出，请稍后！') })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
