import XEUtils from 'xe-utils'

export default {
  data() {
    return {
      toolbar: [{ code: 'export', name: this.$t('导出'), status: 'info' }],
      factoryList: []
    }
  },
  computed: {
    columns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50
        },
        {
          field: 'diffDate',
          title: this.$t('差异日期'),
          minWidth: 160
        },
        {
          field: 'companyCode',
          title: this.$t('公司'),
          minWidth: 160,
          slots: {
            default: ({ row }) => {
              return [<span>{(row.companyCode || '') + '-' + (row.companyName || '')}</span>]
            }
          }
        },
        {
          field: 'factoryCode',
          title: this.$t('工厂'),
          minWidth: 160,
          slots: {
            default: ({ row }) => {
              return [<span>{(row.factoryCode || '') + '-' + (row.factoryName || '')}</span>]
            }
          }
        },
        {
          field: 'purchaseOrgCode',
          title: this.$t('采购组织'),
          minWidth: 160,
          slots: {
            default: ({ row }) => {
              return [
                <span>{(row.purchaseOrgCode || '') + '-' + (row.purchaseOrgName || '')}</span>
              ]
            }
          }
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商编码'),
          minWidth: 120
        },
        {
          field: 'supplierName',
          title: this.$t('供应商名称'),
          minWidth: 160
        },
        {
          field: 'materialCode',
          title: this.$t('物料编码'),
          minWidth: 120
        },
        {
          field: 'materialName',
          title: this.$t('物料名称'),
          minWidth: 160
        },
        {
          field: 'currencyCode',
          title: this.$t('币种'),
          minWidth: 160,
          slots: {
            default: ({ row }) => {
              return [<span>{(row.currencyCode || '') + '-' + (row.currencyName || '')}</span>]
            }
          }
        },
        {
          field: 'quoteAttr',
          title: this.$t('报价属性'),
          minWidth: 120
        },
        {
          field: 'priceEffectiveMethod',
          title: this.$t('价格生效方式'),
          minWidth: 120
        },
        {
          field: 'stepValue',
          title: this.$t('阶梯数量'),
          minWidth: 120
        },
        {
          field: 'validStartTime',
          title: this.$t('生效日期'),
          slots: {
            default: ({ row }) => {
              const validStartTime = XEUtils.toDateString(row.validStartTime, 'yyyy-MM-dd HH:mm:ss')
              return [<span>{validStartTime}</span>]
            }
          },
          minWidth: 160
        },
        {
          field: 'validEndTime',
          title: this.$t('截止日期'),
          slots: {
            default: ({ row }) => {
              const validEndTime = XEUtils.toDateString(row.validEndTime, 'yyyy-MM-dd HH:mm:ss')
              return [<span>{validEndTime}</span>]
            }
          },
          minWidth: 160
        },
        {
          field: 'srmUntaxedUnitPrice',
          title: this.$t('SRM未税单价'),
          minWidth: 120
        },
        {
          field: 'sapUntaxedUnitPrice',
          title: this.$t('SAP未税单价'),
          minWidth: 120
        },
        {
          field: 'priceUntaxedDiff',
          title: this.$t('未税单价差异'),
          minWidth: 120
        }
        // {
        //   field: 'srmTaxedUnitPrice',
        //   title: this.$t('SRM含税单价'),
        //   minWidth: 120
        // },
        // {
        //   field: 'sapTaxedUnitPrice',
        //   title: this.$t('SAP含税单价'),
        //   minWidth: 120
        // },
        // {
        //   field: 'priceTaxedDiff',
        //   title: this.$t('含税单价差异'),
        //   minWidth: 120
        // }
      ]
    }
  },
  mounted() {
    this.getFactoryList()
  },
  methods: {
    // 获取工厂下拉列表
    async getFactoryList(fuzzyParam) {
      const res = await this.$API.assessManage.fuzzySiteQuery({ fuzzyParam })
      if (res.code === 200) {
        res.data?.forEach((item) => {
          item.text = item.siteCode + '-' + item.siteName
        })
        this.factoryList = res.data || []
      }
    }
  }
}
