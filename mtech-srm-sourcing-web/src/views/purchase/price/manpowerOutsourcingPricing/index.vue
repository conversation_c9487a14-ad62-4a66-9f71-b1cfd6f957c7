<template>
  <div>
    <mt-tabs
      ref="mtTabsRef"
      :tab-id="$utils.randomString()"
      :e-tab="false"
      :data-source="tabList"
      :halt-select="false"
      @handleSelectTab="handleTabChange"
    />
    <keep-alive>
      <component ref="mainContent" :is="activeComponent" />
    </keep-alive>
  </div>
</template>

<script>
export default {
  data() {
    return {
      activeTabIndex: 0,
      tabList: [
        { title: this.$t('生产线机型时产关系表') },
        { title: this.$t('人力工时单价') },
        { title: this.$t('人力外包定价') },
        { title: this.$t('人力外包结算价') }
      ]
    }
  },
  computed: {
    activeComponent() {
      let comp = ''
      switch (this.activeTabIndex) {
        case 0:
          comp = () => import('./tabs/productionLineModelHourProductionRelationReport/index.vue')
          break
        case 1:
          comp = () => import('./tabs/manHourUnitPrice/index.vue')
          break
        case 2:
          comp = () => import('./tabs/pricing/index.vue')
          break
        case 3:
          comp = () => import('./tabs/manpowerOutsourcingSettlePrice/index.vue')
          break
        default:
          break
      }
      return comp
    }
  },
  methods: {
    // 切换tab页签
    handleTabChange(index) {
      this.$refs.mtTabsRef.activeTab = index
      this.activeTabIndex = index
    }
  }
}
</script>
<style lang="scss" scoped></style>
