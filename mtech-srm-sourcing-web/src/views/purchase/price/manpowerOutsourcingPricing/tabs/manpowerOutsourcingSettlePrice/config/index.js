import { i18n } from '@/main.js'
// 人力外包结算价
const manpowerOutsourcingSettlePriceColumnData = [
  {
    field: 'pointNo',
    headerText: i18n.t('来源单号')
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司'),
    valueAccessor: (field, row) => {
      return row.companyCode ? row.companyCode + '-' + row.companyName : ''
    }
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂'),
    valueAccessor: (field, row) => {
      return row.siteCode ? row.siteCode + '-' + row.siteName : ''
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    valueAccessor: (field, row) => {
      return row.supplierCode ? row.supplierCode + '-' + row.supplierName : ''
    }
  },
  {
    field: 'prodtLine',
    headerText: i18n.t('生产线')
  },
  {
    field: 'modelCode',
    headerText: i18n.t('ID（机型编码）')
  },
  {
    field: 'settleModel',
    headerText: i18n.t('结算机型')
  },
  {
    field: 'perCapitaHourOutput',
    headerText: i18n.t('人均时产')
  },
  {
    field: 'moduleHourOutput',
    headerText: i18n.t('模组时产')
  },
  {
    field: 'cbuHourOutput',
    headerText: i18n.t('整机时产')
  },
  {
    field: 'domainRentalFee',
    headerText: i18n.t('场地租赁费单价')
  },
  {
    field: 'equipmentRentalFee',
    headerText: i18n.t('设备租赁费单价')
  },
  {
    field: 'moduleDomainRentalFee',
    headerText: i18n.t('模组场地租赁费单价')
  },
  {
    field: 'cbuDomainRentalFee',
    headerText: i18n.t('整机场地租赁费单价')
  },
  {
    field: 'moduleEquipmentRentalFee',
    headerText: i18n.t('模组设备租赁费单价')
  },
  {
    field: 'cbuEquipmentRentalFee',
    headerText: i18n.t('整机设备租赁费单价')
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('结算价（未税）')
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('结算价（含税）')
  },
  {
    field: 'priceType',
    headerText: i18n.t('价格类型')
  },
  {
    field: 'currencyCode',
    headerText: i18n.t('币种'),
    valueAccessor: (field, row) => {
      return row.currencyCode ? row.currencyCode + '-' + row.currencyName : ''
    }
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('生效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('失效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  }
]
export const manpowerOutsourcingSettlePricePageConfig = [
  {
    title: i18n.t('人力外包结算价'),
    gridId: 'c34f603a-ad65-464a-baec-754fd4eb6550',
    activatedRefresh: false,
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    useToolTemplate: false,
    toolbar: {
      useBaseConfig: false,
      tools: [[], ['Refresh', 'Setting']]
    },
    grid: {
      virtualPageSize: 30,
      enableVirtualization: true,
      allowSelection: false,
      allowFiltering: true,
      showSelected: false,
      lineIndex: true,
      columnData: manpowerOutsourcingSettlePriceColumnData,
      asyncConfig: {
        url: '/sourcing//tenant/hro/price/query'
      }
    }
  }
]
