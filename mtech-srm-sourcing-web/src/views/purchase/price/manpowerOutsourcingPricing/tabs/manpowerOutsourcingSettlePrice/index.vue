<template>
  <div class="full-height">
    <mt-local-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
              <mt-select
                v-model="searchFormModel.companyCode"
                :data-source="companyList"
                :fields="{ text: 'text', value: 'orgCode' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择公司')"
              />
            </mt-form-item>
            <mt-form-item prop="siteCode" :label="$t('工厂')">
              <RemoteAutocomplete
                v-model="searchFormModel.siteCode"
                :url="$API.masterData.getSiteListUrl"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                :search-fields="['siteName', 'siteCode']"
                params-key="fuzzyParam"
                :placeholder="$t('请选择工厂')"
              />
            </mt-form-item>
            <mt-form-item prop="settleModel" :label="$t('结算机型')" label-style="top">
              <mt-input
                v-model="searchFormModel.settleModel"
                :show-clear-button="true"
                :placeholder="$t('请输入结算机型')"
              />
            </mt-form-item>
            <mt-form-item prop="pointNo" :label="$t('来源单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.pointNo"
                :show-clear-button="true"
                :placeholder="$t('请输入来源单号')"
              />
            </mt-form-item>
            <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
              <mt-date-range-picker
                v-model="tempForm.createTime"
                :open-on-focus="true"
                :allow-edit="false"
                format="yyyy-MM-dd"
                :placeholder="$t('请选择创建时间')"
                @change="(e) => handleDateRangeChange('CreateTime', e)"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-local-template-page>
  </div>
</template>

<script>
// 引入本地的emplate-page组件
import MtLocalTemplatePage from '@/components/template-page'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import { manpowerOutsourcingSettlePricePageConfig } from './config/index'

export default {
  components: { MtLocalTemplatePage, RemoteAutocomplete },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {
        status: null,
        purchaseAgentName: null
      },
      tempForm: {},
      pageConfig: manpowerOutsourcingSettlePricePageConfig,
      companyList: []
    }
  },
  mounted() {
    this.getCompanyList()
  },
  methods: {
    // 获取公司下拉列表
    async getCompanyList() {
      const res = await this.$API.masterData.permissionCompanyList()
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.companyList = res.data || []
      }
    },
    // 日期范围格式化
    handleDateRangeChange(key, e) {
      const { startDate, endDate } = e
      this.searchFormModel['min' + key] = startDate ? startDate.valueOf() : null
      this.searchFormModel['max' + key] = endDate ? endDate.valueOf() + 86400000 : null
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.tempForm = {}
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  .hidden {
    display: none;
  }
  /deep/.e-headercelldiv {
    padding: 0 0.6em 0 0.6em !important;
  }
}
</style>
