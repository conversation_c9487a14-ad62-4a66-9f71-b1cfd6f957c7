<template>
  <mt-dialog ref="dialogRef" :buttons="buttons" :header="modalData.title" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-local-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleCustomReset="handleCustomReset"
      >
        <template v-slot:quick-search-form>
          <div class="custom-form-box">
            <mt-form ref="searchFormRef" :model="searchFormModel">
              <mt-form-item prop="modelCode" :label="$t('ID')" label-style="top">
                <mt-input
                  v-model="searchFormModel.modelCode"
                  :show-clear-button="true"
                  :placeholder="$t('请输入ID')"
                />
              </mt-form-item>
              <mt-form-item prop="siteCode" :label="$t('工厂')" label-style="top">
                <mt-select
                  v-model="searchFormModel.siteCode"
                  css-class="rule-element"
                  :data-source="factoryList"
                  :fields="{ text: 'text', value: 'siteCode' }"
                  :show-clear-button="true"
                  :allow-filtering="true"
                  filter-type="Contains"
                  :filtering="onFiltering"
                  :placeholder="$t('请选择工厂')"
                />
              </mt-form-item>
              <mt-form-item prop="prodtLine" :label="$t('生产线')" label-style="top">
                <mt-input
                  v-model="searchFormModel.prodtLine"
                  :show-clear-button="true"
                  :placeholder="$t('请输入生产线')"
                />
              </mt-form-item>
              <mt-form-item prop="settleModel" :label="$t('结算机型')" label-style="top">
                <mt-input
                  v-model="searchFormModel.settleModel"
                  :show-clear-button="true"
                  :placeholder="$t('请输入结算机型')"
                />
              </mt-form-item>
              <mt-form-item prop="priceType" :label="$t('价格类型')" label-style="top">
                <mt-select
                  v-model="searchFormModel.priceType"
                  :data-source="priceTypeList"
                  :show-clear-button="true"
                  :fields="{ value: 'itemName', text: 'itemName' }"
                  :placeholder="$t('请选择价格类型')"
                />
              </mt-form-item>
            </mt-form>
          </div>
        </template>
      </mt-local-template-page>
    </div>
  </mt-dialog>
</template>

<script>
import MtLocalTemplatePage from '@/components/template-page'
import { clearModelPageConfig } from './config'
import debounce from 'lodash.debounce'

export default {
  components: { MtLocalTemplatePage },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      pageConfig: clearModelPageConfig(this),
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      factoryList: [],
      priceTypeList: []
    }
  },
  mounted() {
    this.$refs.dialogRef.ejsRef.show()
    this.getFactoryList('')
    this.getPriceTypelist()
  },
  methods: {
    // 模糊查询-工厂
    onFiltering: debounce(function (e) {
      this.getFactoryList(e?.text)
    }, 800),
    // 获取工厂下拉列表
    async getFactoryList(fuzzyParam) {
      const res = await this.$API.assessManage.fuzzySiteQuery({ fuzzyParam })
      if (res.code === 200) {
        res.data?.forEach((item) => {
          item.text = item.siteCode + '-' + item.siteName
        })
        this.factoryList = res.data || []
      }
    },
    // 获取价格类型下拉列表
    async getPriceTypelist() {
      const res = await this.$API.masterData.dictionaryGetList({ dictCode: 'PRICETYPE' })
      if (res.code === 200) {
        this.priceTypeList = res.data || []
      }
    },
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    async confirm() {
      let records = this.$refs.templateRef.getCurrentTabRef().gridRef.getMtechGridRecords()
      if (records.length === 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const suitModelRangeIds = []
      records.forEach((item) => suitModelRangeIds.push(item.id))
      const { id, pointNo } = this.modalData.data
      const params = {
        hroPointId: id,
        pointNo,
        suitModelRangeIds
      }
      const res = await this.$API.manpowerOutsourcing.addPricingClearModel(params)
      if (res.code === 200) {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        this.$emit('confirm-function')
      }
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
