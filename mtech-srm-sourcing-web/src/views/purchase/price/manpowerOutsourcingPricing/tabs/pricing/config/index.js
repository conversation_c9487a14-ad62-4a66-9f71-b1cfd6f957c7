import { i18n } from '@/main.js'

// 状态列表
export const statusList = [
  { value: 0, text: i18n.t('草稿'), cssClass: '' },
  { value: 1, text: i18n.t('已保存'), cssClass: '' },
  { value: 2, text: i18n.t('已提交'), cssClass: '' },
  { value: 3, text: i18n.t('审批通过'), cssClass: '' },
  { value: 4, text: i18n.t('审批驳回'), cssClass: '' },
  { value: 5, text: i18n.t('审批废弃'), cssClass: '' }
]
// 定价类型列表
export const pricingTypeList = [
  { value: 0, text: i18n.t('新品'), cssClass: '' },
  { value: 1, text: i18n.t('二次'), cssClass: '' },
  { value: 2, text: i18n.t('已有'), cssClass: '' }
]
// 价格分类
export const priceClassifyList = [
  { value: 1, text: i18n.t('基价'), cssClass: '' },
  { value: 2, text: i18n.t('SRM价格'), cssClass: '' },
  { value: 3, text: i18n.t('暂估价格'), cssClass: '' },
  { value: 4, text: i18n.t('执行价格'), cssClass: '' }
]
// 发布状态列表
export const publishStatusList = [
  { value: 0, text: i18n.t('未发布'), cssClass: '' },
  { value: 1, text: i18n.t('确认中'), cssClass: '' },
  { value: 2, text: i18n.t('反馈正常'), cssClass: '' },
  { value: 3, text: i18n.t('反馈异常'), cssClass: '' }
]
// 定价单类型列表
export const decidePriceTypeList = [{ value: 8, text: i18n.t('人力外包定价'), cssClass: '' }]

const columnData = [
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: statusList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'publishStatus',
    headerText: i18n.t('供应商确认状态'),
    valueConverter: {
      type: 'map',
      map: publishStatusList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'pointNo',
    headerText: i18n.t('定价单号'),
    cssClass: 'field-content'
  },
  {
    field: 'title',
    headerText: i18n.t('标题')
  },
  {
    field: 'decidePriceType',
    headerText: i18n.t('定价单类型'),
    valueConverter: {
      type: 'map',
      map: {
        8: i18n.t('人力外包定价')
      }
    }
  },
  {
    field: 'priceObjectName',
    headerText: i18n.t('定价对象')
  },
  {
    field: 'viewOa',
    headerText: i18n.t('OA申请单查看'),
    cssClass: 'field-content',
    valueConverter: {
      type: 'function',
      filter: () => {
        return i18n.t('查看')
      }
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司'),
    // eslint-disable-next-line no-empty-pattern
    formatter: function ({}, item) {
      return item.companyCode + '-' + item.companyName
    }
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织'),
    // eslint-disable-next-line no-empty-pattern
    formatter: function ({}, item) {
      return item.purOrgCode + '-' + item.purOrgName
    }
  },
  {
    field: 'purName',
    headerText: i18n.t('采购员')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  }
]

export const pageConfig = [
  {
    title: i18n.t('人力外包定价'),
    gridId: 'f1706f4d-fb3f-41a2-83ae-47502dff7648',
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    useToolTemplate: false,
    toolbar: {
      useBaseConfig: false,
      tools: [
        [
          { id: 'add', title: i18n.t('新增') },
          { id: 'delete', title: i18n.t('删除') },
          { id: 'submit', title: i18n.t('提交') },
          { id: 'publish', title: i18n.t('发布') }
        ],
        ['Refresh', 'Setting']
      ]
    },
    grid: {
      virtualPageSize: 30,
      enableVirtualization: true,
      customSelection: true,
      selectionSettings: {
        persistSelection: true,
        type: 'Multiple',
        checkboxOnly: true
      },
      allowFiltering: true,
      lineIndex: true,
      showSelected: false,
      columnData,
      asyncConfig: {
        url: '/sourcing/tenant/hro/point/buyer/query'
      }
    }
  }
]
