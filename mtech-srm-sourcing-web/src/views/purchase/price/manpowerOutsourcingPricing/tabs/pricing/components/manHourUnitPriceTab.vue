<template>
  <div>
    <sc-table
      ref="sctableRef"
      row-id="customId"
      grid-id="9ee4080c-9ef2-41cd-b3e4-bbf0d57a9e2f"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      :loading="loading"
      @refresh="getTableData"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          v-show="!item.isHidden"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :disabled="item.disabled"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'

export default {
  name: 'MaterialDetailTab',
  components: {
    ScTable
  },
  mixins: [mixin],
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      type: 'unitPrice'
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    async getTableData() {
      this.loading = true
      const res = await this.$API.manpowerOutsourcing
        .queryManHourUnitPriceList({ hroPointId: this.$route.query.id })
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data || []
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (e.code === 'delete' && !selectedRecords.length) {
        this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          this.handleDelete(selectedRecords)
          break
        default:
          break
      }
    },
    // 新增
    handleAdd() {
      this.$dialog({
        modal: () => import('../dialog/addManHourUnitPriceDialog.vue'),
        data: {
          title: this.$t('选择价格记录'),
          data: {
            ...this.dataInfo
          }
        },
        success: () => {
          this.getTableData()
          this.$emit('refreshHeader')
        }
      })
    },
    // 删除
    handleDelete(list) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: async () => {
          const hroPriceRecordRelIds = []
          list.forEach((item) => hroPriceRecordRelIds.push(item.id))
          const params = {
            hroPointId: this.dataInfo.id,
            hroPriceRecordRelIds
          }
          const res = await this.$API.manpowerOutsourcing.deleteManHourUnitPrice(params)
          if (res.code === 200) {
            this.$toast({ content: this.$t('删除成功'), type: 'success' })
            this.getTableData()
            this.$emit('refreshHeader')
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 0px 8px;
  background: #fff;
}
</style>
