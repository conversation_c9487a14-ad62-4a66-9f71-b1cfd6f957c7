import { i18n } from '@/main.js'

// 状态
const statusList = [
  { value: 0, text: i18n.t('草稿'), cssClass: '' },
  { value: 1, text: i18n.t('启用'), cssClass: '' },
  { value: 2, text: i18n.t('禁用'), cssClass: '' },
  { value: 3, text: i18n.t('未合格'), cssClass: '' }
]

// 人力工时单价
export const priceRecordColumnData = [
  {
    width: '60',
    type: 'checkbox'
  },
  {
    field: 'itemCode',
    headerText: i18n.t('生产线编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('生产线名称')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: statusList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('含税单价')
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('未税单价')
  },
  {
    field: 'taxRate',
    headerText: i18n.t('税率')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司'),
    width: '200',
    valueAccessor: (field, data) => {
      return data.companyCode ? data.companyCode + '-' + data.companyName : '-'
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: '200',
    valueAccessor: (field, data) => {
      return data.siteCode ? data.siteCode + '-' + data.siteName : '-'
    }
  },
  {
    field: 'sourceCode',
    headerText: i18n.t('来源单号')
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('生效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('失效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  }
]
export const priceRecordPageConfig = (that) => [
  {
    gridId: 'e449770a-bf8e-4c52-be8c-fe05d5363b65',
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    toolbar: [],
    grid: {
      columnData: priceRecordColumnData,
      asyncConfig: {
        url: '/price/tenant/hro/price/record/query',
        params: {
          valid: true,
          siteCodes: that.modalData.data?.siteCode ? [that.modalData.data?.siteCode] : []
        }
      }
    }
  }
]

// 定价结算机型
export const clearModelColumnData = [
  {
    width: 50,
    type: 'checkbox'
  },
  {
    field: 'modelCode',
    headerText: i18n.t('ID')
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂'),
    // eslint-disable-next-line no-empty-pattern
    formatter: function ({}, item) {
      return item.siteCode + '-' + item.siteName
    }
  },
  {
    field: 'prodtLine',
    headerText: i18n.t('生产线')
  },
  {
    field: 'settleModel',
    headerText: i18n.t('结算机型')
  },
  {
    field: 'calcFormula',
    headerText: i18n.t('计算公式')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    // eslint-disable-next-line no-empty-pattern
    formatter: function ({}, item) {
      return item.supplierCode + '-' + item.supplierName
    }
  },
  {
    field: 'havePrice',
    headerText: i18n.t('是否有价格记录'),
    // eslint-disable-next-line no-empty-pattern
    formatter: function ({}, item) {
      return item.havePrice ? i18n.t('是') : i18n.t('否')
    }
  },
  {
    field: 'haveExecutePrice',
    headerText: i18n.t('是否有执行价'),
    // eslint-disable-next-line no-empty-pattern
    formatter: function ({}, item) {
      return item.haveExecutePrice ? i18n.t('是') : i18n.t('否')
    }
  },
  {
    field: 'priceType',
    headerText: i18n.t('价格类型')
  }
]
export const clearModelPageConfig = (that) => [
  {
    gridId: '8601b693-29f6-4989-a661-a1186d108106',
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    toolbar: [],
    grid: {
      columnData: clearModelColumnData,
      allowSorting: false,
      allowSelection: true,
      selectionSettings: {
        type: 'Multiple',
        mode: 'Row'
      },
      asyncConfig: {
        url: '/sourcing/tenant/hro/suit/model/range/query',
        params: {
          hroPointId: that.modalData.data?.id || null
        }
      }
    }
  }
]
