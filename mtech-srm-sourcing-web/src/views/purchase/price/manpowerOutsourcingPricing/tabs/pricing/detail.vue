<template>
  <div class="full-height">
    <div class="top-container toggle-container" :class="[!isExpand && 'top-hidden']">
      <div class="top-content">
        <div class="top-info">
          <div class="title">
            <div v-show="!isExpand">
              <span>{{ dataForm.pointNo }}</span>
              <span class="sub-title">{{ dataForm.title }}</span>
            </div>
          </div>
          <div class="top-btns">
            <vxe-button
              v-for="(item, index) in detailToolbar"
              v-show="!item.isHidden"
              :key="index"
              :icon="item.icon"
              :status="item.status"
              :disabled="item.disabled"
              size="small"
              @click="handleClickToolBar(item)"
              >{{ item.name }}</vxe-button
            >
          </div>
        </div>
        <div class="top-form">
          <mt-form ref="dataFormRef" :model="dataForm">
            <mt-form-item prop="pointNo" :label="$t('定价单号')" label-style="top">
              <vxe-input v-model="dataForm.pointNo" disabled />
            </mt-form-item>
            <mt-form-item prop="title" :label="$t('标题')" label-style="top">
              <vxe-input v-model="dataForm.title" disabled />
            </mt-form-item>
            <mt-form-item prop="priceObjectName" :label="$t('定价对象')" label-style="top">
              <vxe-input v-model="dataForm.priceObjectName" disabled />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
              <vxe-select
                v-model="dataForm.companyCode"
                :options="companyList"
                :option-props="{ label: 'text', value: 'orgCode' }"
                disabled
              />
            </mt-form-item>
            <mt-form-item prop="purOrgCode" :label="$t('采购组织')">
              <vxe-select
                v-model="dataForm.purOrgCode"
                :options="purchaseOrgList"
                :option-props="{ label: 'text', value: 'organizationCode' }"
                disabled
              />
            </mt-form-item>
            <mt-form-item prop="siteCode" :label="$t('工厂')" label-style="top">
              <vxe-select
                v-model="dataForm.siteCode"
                :options="siteList"
                :option-props="{ label: 'text', value: 'orgCode' }"
                disabled
              />
            </mt-form-item>
            <mt-form-item prop="decidePriceType" :label="$t('定价单类型')">
              <vxe-select
                v-model="dataForm.decidePriceType"
                :options="decidePriceTypeList"
                :option-props="{ label: 'text', value: 'value' }"
                disabled
              />
            </mt-form-item>
            <mt-form-item prop="priceClassification" :label="$t('价格分类')" label-style="top">
              <vxe-select
                v-model="dataForm.priceClassification"
                :options="priceClassifyList"
                :option-props="{ label: 'text', value: 'value' }"
                disabled
              />
            </mt-form-item>
            <mt-form-item prop="pointType" :label="$t('定价类型')" label-style="top">
              <vxe-select
                v-model="dataForm.pointType"
                :options="pricingTypeList"
                :option-props="{ label: 'text', value: 'value' }"
                disabled
              />
            </mt-form-item>
            <mt-form-item prop="purName" :label="$t('采购员')" label-style="top">
              <vxe-input v-model="dataForm.purName" disabled />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <vxe-select
                v-model="dataForm.status"
                :options="statusList"
                :option-props="{ label: 'text', value: 'value' }"
                disabled
              />
            </mt-form-item>
            <mt-form-item prop="purRemark" :label="$t('采方备注')" label-style="top">
              <vxe-textarea
                v-model="dataForm.purRemark"
                :disabled="[2, 3, 4].includes(dataForm.status)"
                :rows="1"
                :maxlength="500"
              />
            </mt-form-item>
            <mt-form-item prop="supRemark" :label="$t('供方备注')" label-style="top">
              <vxe-textarea v-model="dataForm.supRemark" disabled :rows="1" :maxlength="500" />
            </mt-form-item>
          </mt-form>
        </div>
      </div>
      <div class="top-toggle-btn">
        <div @click="handleToggle"><i class="vxe-icon-arrow-up" /></div>
      </div>
    </div>
    <div class="body-container">
      <mt-tabs
        ref="mtTabsRef"
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabList"
        :halt-select="false"
        @handleSelectTab="(index, item) => handleTabChange(index)"
        style="background-color: #fff"
      />
      <keep-alive>
        <component
          ref="mainContent"
          :is="activeComponent"
          :data-info="dataForm"
          @refreshHeader="getHeaderInfo"
        />
      </keep-alive>
    </div>
  </div>
</template>

<script>
import {
  Input as VxeInput,
  Button as VxeButton,
  Select as VxeSelect,
  Textarea as VxeTextarea
} from 'vxe-table'
import mixin from './config/mixin'
import { priceClassifyList, statusList, pricingTypeList, decidePriceTypeList } from './config/index'

export default {
  components: { VxeInput, VxeButton, VxeSelect, VxeTextarea },
  mixins: [mixin],
  data() {
    return {
      type: 'detail',
      priceClassifyList,
      statusList,
      pricingTypeList,
      decidePriceTypeList,
      dataForm: {},
      activeTabIndex: 0,
      isExpand: true,
      tabList: [
        { title: this.$t('人力工时单价') },
        { title: this.$t('定价结算机型') },
        { title: this.$t('附件') }
      ]
    }
  },
  computed: {
    activeComponent() {
      let comp = ''
      switch (this.activeTabIndex) {
        case 0:
          // 人力工时单价
          comp = () => import('./components/manHourUnitPriceTab.vue')
          break
        case 1:
          // 定价结算机型
          comp = () => import('./components/pricingClearModelTab.vue')
          break
        case 2:
          // 附件
          comp = () => import('./components/attachmentTab.vue')
          break
        default:
          return
      }
      return comp
    },
    detailToolbar() {
      return [
        {
          code: 'save',
          name: this.$t('保存'),
          status: '',
          isHidden: [2, 3, 4].includes(this.dataForm.status)
        },
        {
          code: 'submit',
          name: this.$t('提交'),
          status: '',
          isHidden: this.dataForm.status !== 1
        },
        {
          code: 'publish',
          name: this.$t('发布'),
          status: '',
          isHidden: this.dataForm.status !== 3
        },
        {
          code: 'viewOa',
          name: this.$t('查看OA审批'),
          status: '',
          isHidden: [0, 1].includes(this.dataForm.status)
        },
        { code: 'back', name: this.$t('返回'), status: 'primary' }
      ]
    }
  },
  mounted() {
    this.getHeaderInfo()
  },
  methods: {
    // 获取头部基础信息
    async getHeaderInfo() {
      const params = {
        id: this.$route.query.id,
        page: { current: 1, size: 20 }
      }
      const res = await this.$API.manpowerOutsourcing.queryManOutPricingList(params)
      if (res.code === 200) {
        const { companyId, purOrgId } = res.data?.records[0]
        this.getSiteListById(companyId, purOrgId)
        this.dataForm = res.data?.records[0]
      }
    },
    // 切换tab页签
    handleTabChange(index) {
      this.$refs.mtTabsRef.activeTab = index
      this.activeTabIndex = index
    },
    // 展开/收缩
    handleToggle() {
      this.isExpand = !this.isExpand
    },
    // 点击工具栏按钮
    handleClickToolBar(e) {
      switch (e.code) {
        case 'back':
          this.handleBack()
          break
        case 'save':
          this.handleSave()
          break
        case 'submit':
        case 'publish':
          this.handleOperate(e.code)
          break
        case 'viewOa':
          this.handleViewOa()
          break
        default:
          break
      }
    },
    // 返回
    handleBack() {
      this.$router.go(-1)
    },
    // 保存
    async handleSave() {
      const params = {
        id: this.dataForm.id,
        buyerRemark: this.dataForm.purRemark
      }
      const res = await this.$API.manpowerOutsourcing.saveManOutPricing(params)
      if (res.code === 200) {
        this.$toast({ content: this.$t('保存成功'), type: 'success' })
        this.getHeaderInfo()
      }
    },
    // 提交、发布
    handleOperate(type) {
      const operateMap = {
        submit: this.$t('提交'),
        publish: this.$t('发布')
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`${this.$t('确定') + operateMap[type]}？`)
        },
        success: async () => {
          const { id, pointNo } = this.dataForm
          const res = await this.$API.manpowerOutsourcing[type + 'ManOutPricing']([id])
          if (res.code === 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$router.replace({
              name: 'manpower-outsourcing-pricing-detail',
              query: {
                id,
                pointNo,
                refreshId: Date.now()
              }
            })
          }
        }
      })
    },
    // 查看OA审批
    async handleViewOa() {
      const params = {
        docId: this.dataForm.id,
        operationType: 'hro_price'
      }
      const res = await this.$API.manpowerOutsourcing.getOaLink(params)
      if (res.code === 200) {
        res.data
          ? window.open(res.data)
          : this.$toast({ content: this.$t('暂无申请单'), type: 'error' })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.full-height {
  height: 100%;
  padding: 8px;
  background: #fff;
}

.top-container {
  width: 100%;
  .top-content {
    padding: 8px 8px 0 8px;
    background-color: #f9f9f9;
    border-radius: 8px;
    .top-info {
      display: flex;
      justify-content: space-between;
      .title {
        font-size: 20px;
        font-weight: 700;
        color: #292929;
        span {
          margin-right: 15px;
        }
        .sub-title {
          font-size: 15px;
          color: #777;
        }
      }
      .top-btns {
        text-align: right;
        margin-bottom: 10px;
      }
    }
    .top-form {
      .mt-form {
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
        justify-content: space-between;
        grid-gap: 5px;
      }
      .vxe-input,
      .vxe-select,
      .vxe-pulldown {
        width: 100%;
        height: 34px;
        line-height: 34px;
      }
      /deep/.vxe-pulldown--panel {
        min-width: unset !important;
        width: 100%;
      }
    }
  }
  .top-toggle-btn {
    width: 100%;
    div {
      width: 80px;
      height: 15px;
      line-height: 15px;
      margin: auto;
      text-align: center;
      background-color: #4a556b;
      color: #fff;
      font-weight: bold;
      border-radius: 0 0 6px 6px;
    }
  }
}

.top-hidden {
  .top-form {
    display: none;
  }
  .top-toggle-btn {
    div {
      transform: rotate(180deg);
      border-radius: 6px 6px 0 0;
    }
  }
}

.my-dropdown2 {
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 4px 0;
  max-height: 200px;
  border-radius: 4px;
  border: 1px solid #dadce0;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}
.list-item2 {
  line-height: 30px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 0 4px;

  .isSelected {
    font-weight: 700;
    color: #409eff;
  }
}
.list-item2:hover {
  background-color: #f5f7fa;
}
.empty-tip {
  width: 100%;
  line-height: 30px;
  padding: 0 4px;
  text-align: center;
  color: #c0c4cc;
}

::v-deep {
  .mt-form-item {
    margin-bottom: 15px;
    .label {
      margin: 0 10px 5px 0;
    }
  }
  .mt-tabs-container {
    background-color: #fff;
  }

  .mt-tabs-container ul.tab-container li.tab-item2.active {
    border-bottom: 2px solid #409eff;
  }
  .mt-tabs-container ul.tab-container li.tab-item2.active,
  .mt-tabs-container ul.tab-container li.tab-item2:hover {
    color: #31374e;
  }

  .vxe-textarea--inner {
    min-height: 34px;
    height: 100%;
  }
}
</style>
