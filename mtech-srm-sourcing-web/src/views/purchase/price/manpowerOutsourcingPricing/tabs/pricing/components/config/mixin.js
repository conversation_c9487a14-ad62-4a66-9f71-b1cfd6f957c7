import { i18n } from '@/main.js'
export default {
  data() {
    return {
      tableData: [],
      loading: false,
      quoteAttributeList: [
        { value: 'mailing_price', text: i18n.t('寄售价') },
        { value: 'standard_price', text: i18n.t('标准价') },
        { value: 'outsource', text: i18n.t('委外价') }
      ],
      priceTypeList: [],
      priceUnitList: [
        { value: 0.0001, text: i18n.t('万元') },
        { value: 1, text: i18n.t('元') }
      ]
    }
  },
  computed: {
    editable() {
      return this.$route.query.type === 'create' || [0, 5, 9].includes(this.dataInfo.status)
    },
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    toolbar() {
      switch (this.type) {
        case 'unitPrice':
          return [
            {
              code: 'add',
              name: this.$t('新增'),
              status: 'info',
              isHidden: ![0, 1, 5].includes(this.dataInfo.status)
            },
            {
              code: 'delete',
              name: this.$t('删除'),
              status: 'info',
              isHidden: ![0, 1, 5].includes(this.dataInfo.status)
            }
          ]
        case 'clearModel':
          return [
            {
              code: 'add',
              name: this.$t('新增'),
              status: 'info',
              isHidden: ![0, 1, 5].includes(this.dataInfo.status)
            },
            {
              code: 'save',
              name: this.$t('保存'),
              status: 'info',
              isHidden: ![0, 1, 5].includes(this.dataInfo.status)
            },
            {
              code: 'delete',
              name: this.$t('删除'),
              status: 'info',
              isHidden: ![0, 1, 5].includes(this.dataInfo.status)
            }
          ]
        default:
          return []
      }
    },
    columns() {
      switch (this.type) {
        case 'unitPrice':
          return [
            {
              width: 50,
              type: 'checkbox'
            },
            {
              field: 'itemCode',
              title: this.$t('生产线编码'),
              minWidth: 150
            },
            {
              field: 'itemName',
              title: this.$t('生产线名称')
            },
            {
              field: 'supplierCode',
              title: this.$t('供应商编码')
            },
            {
              field: 'supplierName',
              title: this.$t('供应商名称')
            },
            {
              field: 'siteCode',
              title: this.$t('工厂编码')
            },
            {
              field: 'siteName',
              title: this.$t('工厂名称')
            },
            {
              field: 'taxedUnitPrice',
              title: this.$t('单价（含税）')
            },
            {
              field: 'untaxedUnitPrice',
              title: this.$t('单价（未税）')
            },
            {
              field: 'unitCode',
              title: this.$t('基本单位'),
              slots: {
                default: ({ row }) => {
                  return [<span>{(row.unitCode || '') + '-' + (row.unitName || '')}</span>]
                }
              }
            },
            {
              field: 'priceUnit',
              title: this.$t('价格单位'),
              slots: {
                default: ({ row }) => {
                  const selectItem = this.priceUnitList.find((item) => item.value == row.priceUnit)
                  return [<span>{selectItem?.text || ''}</span>]
                }
              }
            },
            {
              field: 'sourceCode',
              title: this.$t('来源单号')
            },
            {
              field: 'validStartTime',
              title: this.$t('生效日期'),
              minWidth: 140,
              slots: {
                default: ({ row }) => {
                  const validStartTime = row.validStartTime?.split(' ')[0]
                  return [<span>{validStartTime}</span>]
                }
              }
            },
            {
              field: 'validEndTime',
              title: this.$t('失效日期'),
              minWidth: 140,
              slots: {
                default: ({ row }) => {
                  const validEndTime = row.validEndTime?.split(' ')[0]
                  return [<span>{validEndTime}</span>]
                }
              }
            }
          ]
        case 'clearModel':
          return [
            {
              width: 50,
              type: 'checkbox'
            },
            {
              field: 'calcFormula',
              title: this.$t('计算公式')
            },
            {
              field: 'taxedUnitPrice',
              title: this.$t('单价（含税）')
            },
            {
              field: 'untaxedUnitPrice',
              title: this.$t('单价（未税）')
            },
            {
              field: 'supplierCode',
              title: this.$t('供应商编码')
            },
            {
              field: 'supplierName',
              title: this.$t('供应商名称')
            },
            {
              field: 'siteCode',
              title: this.$t('工厂编码')
            },
            {
              field: 'siteName',
              title: this.$t('工厂名称')
            },
            {
              field: 'modelCode',
              title: this.$t('ID（机型编码）'),
              minWidth: 150
            },
            {
              field: 'settleModel',
              title: this.$t('结算机型')
            },
            {
              field: 'historyPrice',
              title: this.$t('历史价格')
            },
            {
              field: 'priceUnit',
              title: this.$t('价格单位'),
              slots: {
                default: ({ row }) => {
                  const selectItem = this.priceUnitList.find((item) => item.value == row.priceUnit)
                  return [<span>{selectItem?.text || ''}</span>]
                }
              }
            },
            {
              field: 'quoteAttribute',
              title: this.$t('报价属性'),
              slots: {
                default: ({ row }) => {
                  const selectItem = this.quoteAttributeList.find(
                    (item) => item.value === row.quoteAttribute
                  )
                  return [<span>{selectItem?.text || ''}</span>]
                }
              }
            },
            {
              field: 'currencyCode',
              title: this.$t('币种'),
              slots: {
                default: ({ row }) => {
                  return [<span>{(row.currencyCode || '') + '-' + (row.currencyName || '')}</span>]
                }
              }
            },
            {
              field: 'validStartTime',
              title: this.$t('生效日期'),
              minWidth: 140,
              editRender: {},
              slots: {
                default: ({ row }) => {
                  const validStartTime = row.validStartTime?.split(' ')[0]
                  return [<span>{validStartTime}</span>]
                },
                edit: ({ row }) => {
                  return [
                    <vxe-input
                      v-model={row.validStartTime}
                      clearable
                      type='date'
                      editable={false}
                      transfer
                      disabled-method={(e) => this.disabledDateMethod('start', row, e)}
                    />
                  ]
                }
              }
            },
            {
              field: 'validEndTime',
              title: this.$t('失效日期'),
              minWidth: 140,
              editRender: {},
              slots: {
                default: ({ row }) => {
                  const validEndTime = row.validEndTime?.split(' ')[0]
                  return [<span>{validEndTime}</span>]
                },
                edit: ({ row }) => {
                  return [
                    <vxe-input
                      v-model={row.validEndTime}
                      clearable
                      type='date'
                      editable={false}
                      transfer
                      disabled-method={(e) => this.disabledDateMethod('end', row, e)}
                    />
                  ]
                }
              }
            },
            {
              field: 'taxCode',
              title: this.$t('税率'),
              slots: {
                default: ({ row }) => {
                  return [<span>{(row.taxCode || '') + '-' + (row.taxName || '')}</span>]
                }
              }
            },
            {
              field: 'taxRate',
              title: this.$t('税率值')
            },
            {
              field: 'remark',
              title: this.$t('备注'),
              minWidth: 150,
              editRender: {},
              slots: {
                edit: ({ row }) => {
                  return [<vxe-input v-model={row.remark} maxlength={500} />]
                }
              }
            },
            {
              field: 'moduleHourOutput',
              title: this.$t('模组时产')
            },
            {
              field: 'cbuHourOutput',
              title: this.$t('整机时产')
            },
            {
              field: 'perCapitaHourOutput',
              title: this.$t('人均时产')
            },
            {
              field: 'domainRentalFee',
              title: this.$t('场地租赁费')
            },
            {
              field: 'equipmentRentalFee',
              title: this.$t('设备租赁费')
            },
            {
              field: 'moduleDomainRentalFee',
              title: this.$t('模组场地租赁费')
            },
            {
              field: 'moduleEquipmentRentalFee',
              title: this.$t('模组设备租赁费')
            },
            {
              field: 'cbuDomainRentalFee',
              title: this.$t('整机场地租赁费')
            },
            {
              field: 'cbuEquipmentRentalFee',
              title: this.$t('整机设备租赁费')
            },
            {
              field: 'priceType',
              title: this.$t('价格类型')
            }
          ]
        default:
          return []
      }
    }
  },
  mounted() {
    this.type === 'clearModel' && this.getPriceTypelist()
  },
  methods: {
    async getPriceTypelist() {
      const res = await this.$API.masterData.dictionaryGetList({ dictCode: 'PRICETYPE' })
      if (res.code === 200) {
        this.priceTypeList = res.data || []
      }
    },
    // 限制日期选择
    disabledDateMethod(type, row, e) {
      const { date } = e
      if (type === 'start') {
        const endTime = row.validEndTime ? new Date(row.validEndTime).getTime() : null
        return endTime ? endTime < new Date(date).getTime() : false
      }
      if (type === 'end') {
        const startTime = row.validStartTime ? new Date(row.validStartTime).getTime() : null
        return startTime ? startTime - 86400000 > new Date(date).getTime() : false
      }
    }
  }
}
