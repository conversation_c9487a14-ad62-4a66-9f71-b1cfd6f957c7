export default {
  data() {
    return {
      companyList: [],
      purchaseOrgList: [],
      siteList: [],
      expandList: [],
      purchaserList: []
    }
  },
  mounted() {
    this.getCompanyList()
    this.type !== 'add' && this.getPurchaseOrgList()
    this.type === 'add' && this.getPurchaserList({ text: '' })
  },
  methods: {
    // 获取公司下拉列表
    async getCompanyList() {
      const res = await this.$API.masterData.permissionCompanyList()
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.companyList = res.data || []
      }
    },
    // 获取采购组织下拉列表
    async getPurchaseOrgList() {
      const res = await this.$API.masterData.purchaseOraginaze({
        organizationTypeCode: 'BUORG002ADM'
      })
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.organizationCode + '-' + item.organizationName
        })
        this.purchaseOrgList = res.data || []
      }
    },
    // 根据公司id获取采购组织下拉列表
    async getPurchaseOrgListByCompanyId(companyId) {
      if (!companyId) {
        this.purchaseOrgList = []
        return
      }
      const res = await this.$API.masterData.permissionOrgList({
        orgId: companyId
      })
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.organizationCode + '-' + item.organizationName
        })
        this.purchaseOrgList = res.data
      }
    },
    // 根据公司i采购组织id获取工厂下拉列表
    async getSiteListById(companyId, purOrgId) {
      if (!companyId || !purOrgId) {
        this.siteList = []
        return
      }
      const res = await this.$API.masterData.permissionSiteList({
        companyId,
        buOrgId: purOrgId,
        orgLevelTypeCode: 'ORG06'
      })
      if (res.code === 200) {
        res.data?.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.siteList = res.data || []
      }
    },
    // 获取采购员下拉列表
    async getPurchaserList(e) {
      const { text: fuzzyName } = e
      const res = await this.$API.masterData.getCurrentTenantEmployees({ fuzzyName })
      if (res.code === 200) {
        res.data?.forEach((item) => {
          item.text = `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`
        })
        this.purchaserList = res.data || []
        if (fuzzyName == '' && this.purchaserList.length) {
          this.formData.purId = this.purchaserList[0].employeeId
        }
      }
    }
  }
}
