<template>
  <div>
    <sc-table
      ref="sctableRef"
      row-id="customId"
      grid-id="39b19ba8-e4d2-403f-9790-a95a2f4997b7"
      keep-source
      :edit-config="editConfig"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      :loading="loading"
      @refresh="getTableData"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          v-show="!item.isHidden"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :disabled="item.disabled"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import cloneDeep from 'lodash/cloneDeep'

export default {
  name: 'MaterialDetailTab',
  components: {
    ScTable
  },
  mixins: [mixin],
  props: {
    dataInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      type: 'clearModel'
    }
  },
  computed: {
    editConfig() {
      return {
        enable: [0, 1, 5].includes(this.dataInfo.status),
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    dataList() {
      const list = cloneDeep(this.tableData)
      const dataList = []
      list.forEach((item) => {
        this.tableRef.isUpdateByRow(item) && dataList.push(item)
      })
      return dataList
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    async getTableData() {
      this.loading = true
      const res = await this.$API.manpowerOutsourcing
        .queryPricingClearModelList({ hroPointId: this.$route.query.id })
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data || []
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (e.code === 'delete' && !selectedRecords.length) {
        this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'save':
          this.handleSave()
          break
        case 'delete':
          this.handleDelete(selectedRecords)
          break
        default:
          break
      }
    },
    // 新增
    handleAdd() {
      this.$dialog({
        modal: () => import('../dialog/addPricingClearModelDialog.vue'),
        data: {
          title: this.$t('选择机型'),
          data: {
            ...this.dataInfo
          }
        },
        success: () => {
          this.getTableData()
          this.$emit('refreshHeader')
        }
      })
    },
    // 保存
    async handleSave() {
      // 有修改数据，无需保存
      if (!this.dataList.length) {
        this.getTableData()
        return
      }
      const list = []
      this.dataList.forEach((item) => {
        const { id, validStartTime, validEndTime, remark } = item
        list.push({
          hroPointId: this.dataInfo.id,
          pointNo: this.dataInfo.pointNo,
          id,
          validStartTime: validStartTime
            ? new Date(validStartTime.split(' ')[0] + ' 00:00:00').valueOf() + ''
            : null,
          validEndTime: validEndTime
            ? new Date(validEndTime.split(' ')[0] + ' 23:59:59').valueOf() + ''
            : null,
          remark
        })
      })
      const res = await this.$API.manpowerOutsourcing.savePricingClearModel(list)
      if (res.code === 200) {
        this.$toast({ content: this.$t('保存成功'), type: 'success' })
        this.getTableData()
      }
    },
    // 删除
    handleDelete(list) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: async () => {
          const hroPointSettleModeIds = []
          list.forEach((item) => hroPointSettleModeIds.push(item.id))
          const params = {
            hroPointId: this.dataInfo.id,
            hroPointSettleModeIds
          }
          const res = await this.$API.manpowerOutsourcing.deletePricingClearModel(params)
          if (res.code === 200) {
            this.$toast({ content: this.$t('删除成功'), type: 'success' })
            this.getTableData()
            this.$emit('refreshHeader')
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 0px 8px;
  background: #fff;
}
</style>
