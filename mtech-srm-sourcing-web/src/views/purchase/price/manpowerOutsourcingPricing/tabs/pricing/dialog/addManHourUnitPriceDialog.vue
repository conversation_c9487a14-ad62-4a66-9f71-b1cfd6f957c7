<template>
  <mt-dialog ref="dialogRef" :buttons="buttons" :header="modalData.title" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-local-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleCustomReset="handleCustomReset"
      >
        <template v-slot:quick-search-form>
          <div class="custom-form-box">
            <mt-form ref="searchFormRef" :model="searchFormModel">
              <mt-form-item prop="itemCode" :label="$t('生产线编码')" label-style="top">
                <mt-input
                  v-model="searchFormModel.itemCode"
                  :show-clear-button="true"
                  :placeholder="$t('请输入生产线编码')"
                  @change="
                    (val) => {
                      searchFormModel.itemCodes = val ? [val] : []
                    }
                  "
                />
              </mt-form-item>
              <mt-form-item prop="itemName" :label="$t('生产线名称')" label-style="top">
                <mt-input
                  v-model="searchFormModel.itemName"
                  :show-clear-button="true"
                  :placeholder="$t('请输入生产线名称')"
                />
              </mt-form-item>
              <mt-form-item prop="supplierCode" :label="$t('供应商编码')" label-style="top">
                <mt-input
                  v-model="searchFormModel.supplierCode"
                  :show-clear-button="true"
                  :placeholder="$t('请输入供应商编码')"
                  @change="
                    (val) => {
                      searchFormModel.supplierCodes = val ? [val] : []
                    }
                  "
                />
              </mt-form-item>
              <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
                <mt-input
                  v-model="searchFormModel.supplierName"
                  :show-clear-button="true"
                  :placeholder="$t('请输入供应商名称')"
                />
              </mt-form-item>
              <mt-form-item prop="sourceCode" :label="$t('来源单号')" label-style="top">
                <mt-input
                  v-model="searchFormModel.sourceCode"
                  :show-clear-button="true"
                  :placeholder="$t('请输入来源单号')"
                  @change="
                    (val) => {
                      searchFormModel.sourceCodes = val ? [val] : []
                    }
                  "
                />
              </mt-form-item>
            </mt-form>
          </div>
        </template>
      </mt-local-template-page>
    </div>
  </mt-dialog>
</template>

<script>
import MtLocalTemplatePage from '@/components/template-page'
import { priceRecordPageConfig } from './config'

export default {
  components: { MtLocalTemplatePage },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      pageConfig: priceRecordPageConfig(this),
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      factoryList: []
    }
  },
  mounted() {
    this.$refs.dialogRef.ejsRef.show()
  },
  methods: {
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    async confirm() {
      let records = this.$refs.templateRef.getCurrentTabRef().gridRef.getMtechGridRecords()
      if (records.length === 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const hroPriceRecordIds = []
      records.forEach((item) => hroPriceRecordIds.push(item.id))
      const { id, pointNo } = this.modalData.data
      const params = {
        hroPointId: id,
        pointNo,
        hroPriceRecordIds
      }
      const res = await this.$API.manpowerOutsourcing.addManHourUnitPrice(params)
      if (res.code === 200) {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        this.$emit('confirm-function')
      }
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
