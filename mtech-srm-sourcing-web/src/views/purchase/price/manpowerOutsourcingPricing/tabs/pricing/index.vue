<template>
  <div class="full-height">
    <mt-local-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-select
                v-model="searchFormModel.status"
                :data-source="statusList"
                :show-clear-button="true"
                :placeholder="$t('请选择状态')"
              />
            </mt-form-item>
            <mt-form-item prop="publishStatus" :label="$t('供应商确认状态')" label-style="top">
              <mt-select
                v-model="searchFormModel.publishStatus"
                :data-source="publishStatusList"
                :show-clear-button="true"
                :placeholder="$t('请选择供应商确认状态')"
              />
            </mt-form-item>
            <mt-form-item prop="pointNo" :label="$t('定价单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.pointNo"
                :show-clear-button="true"
                :placeholder="$t('请输入定价单号')"
              />
            </mt-form-item>
            <mt-form-item prop="title" :label="$t('标题')" label-style="top">
              <mt-input
                v-model="searchFormModel.title"
                :show-clear-button="true"
                :placeholder="$t('请输入标题')"
              />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
              <mt-select
                v-model="searchFormModel.companyCode"
                :data-source="companyList"
                :fields="{ text: 'text', value: 'orgCode' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择公司')"
              />
            </mt-form-item>
            <mt-form-item prop="purOrgCode" :label="$t('采购组织')" label-style="top">
              <mt-select
                v-model="searchFormModel.purOrgCode"
                :data-source="purchaseOrgList"
                :fields="{ text: 'text', value: 'organizationCode' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择采购组织')"
              />
            </mt-form-item>
            <mt-form-item prop="purName" :label="$t('采购员')" label-style="top">
              <mt-input
                v-model="searchFormModel.purName"
                :show-clear-button="true"
                :placeholder="$t('请输入采购员')"
              />
            </mt-form-item>
            <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
              <mt-input
                v-model="searchFormModel.createUserName"
                :show-clear-button="true"
                :placeholder="$t('请输入创建人')"
              />
            </mt-form-item>
            <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
              <mt-date-range-picker
                v-model="tempForm.createTime"
                :open-on-focus="true"
                :allow-edit="false"
                format="yyyy-MM-dd"
                :placeholder="$t('请选择创建时间')"
                @change="(e) => handleDateRangeChange('CreateTime', e)"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-local-template-page>
  </div>
</template>

<script>
import MtLocalTemplatePage from '@/components/template-page'
import { statusList, publishStatusList, pageConfig } from './config'
import mixin from './config/mixin'

export default {
  components: { MtLocalTemplatePage },
  mixins: [mixin],
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      type: 'list',
      searchFormModel: {},
      tempForm: {},
      pageConfig,
      statusList,
      publishStatusList
    }
  },
  methods: {
    // 日期范围格式化
    handleDateRangeChange(key, e) {
      const { startDate, endDate } = e
      this.searchFormModel['min' + key] = startDate ? startDate.valueOf() : null
      this.searchFormModel['max' + key] = endDate ? endDate.valueOf() + 86400000 : null
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.tempForm = {}
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      const selectedRecords = gridRef.getCustomSelectedRows()
      if (['delete', 'submit', 'publish'].includes(toolbar.id) && !selectedRecords.length) {
        this.$toast({ content: this.$t('请选择一行数据'), type: 'warning' })
        return
      }
      let idList = []
      selectedRecords.map((item) => {
        idList.push(item.id)
      })
      switch (toolbar.id) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
        case 'submit':
        case 'publish':
          this.handleOperate(toolbar.id, selectedRecords)
          break
        default:
          break
      }
    },
    // 单元格标题点击操作
    async handleClickCellTitle(e) {
      const { field, data } = e
      if (field == 'pointNo') {
        this.$router.push({
          name: 'manpower-outsourcing-pricing-detail',
          query: {
            id: data.id,
            pointNo: data.pointNo,
            refreshId: Date.now()
          }
        })
      } else if (field === 'viewOa') {
        const params = {
          docId: data.id,
          operationType: 'hro_price'
        }
        const res = await this.$API.manpowerOutsourcing.getOaLink(params)
        if (res.code === 200) {
          res.data
            ? window.open(res.data)
            : this.$toast({ content: this.$t('暂无申请单'), type: 'error' })
        }
      }
    },
    // 新增
    handleAdd() {
      this.$dialog({
        modal: () => import('./dialog/addDialog.vue'),
        data: {
          title: this.$t('创建定价')
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 删除、提交、发布
    handleOperate(type, selectedRecords) {
      const operateMap = {
        delete: this.$t('删除'),
        submit: this.$t('提交'),
        publish: this.$t('发布')
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`${this.$t('确定') + operateMap[type]}？`)
        },
        success: async () => {
          const idList = []
          selectedRecords.forEach((item) => idList.push(item.id))
          const res = await this.$API.manpowerOutsourcing[type + 'ManOutPricing'](idList)
          if (res.code === 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        }
      })
    }
  }
}
</script>
