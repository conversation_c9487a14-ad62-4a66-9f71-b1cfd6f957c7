<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="modalData.title"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dataForm" :model="formData" :rules="formRules">
        <mt-form-item prop="priceObjectName" :label="$t('定价对象')">
          <mt-select
            v-model="formData.priceObjectName"
            :data-source="[{ text: $t('人力外包定价'), value: $t('人力外包定价') }]"
            :disabled="true"
            :placeholder="$t('请选择定价对象')"
          />
        </mt-form-item>
        <mt-form-item prop="decidePriceType" :label="$t('定价单类型')">
          <mt-select
            v-model="formData.decidePriceType"
            :data-source="decidePriceTypeList"
            :disabled="true"
            :placeholder="$t('请选择定价单类型')"
          />
        </mt-form-item>
        <mt-form-item prop="title" :label="$t('标题')">
          <mt-input
            v-model="formData.title"
            maxlength="50"
            :show-clear-button="true"
            :placeholder="$t('请输入标题')"
          />
        </mt-form-item>
        <mt-form-item prop="companyCode" :label="$t('公司')">
          <mt-select
            v-model="formData.companyCode"
            :data-source="companyList"
            :fields="{ text: 'text', value: 'orgCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择公司')"
            @change="(e) => hanldeChange('company', e)"
          />
        </mt-form-item>
        <mt-form-item prop="purOrgCode" :label="$t('采购组织')">
          <mt-select
            v-model="formData.purOrgCode"
            :data-source="purchaseOrgList"
            :fields="{ text: 'text', value: 'organizationCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :disabled="!formData.companyCode"
            :placeholder="$t('请选择采购组织')"
            @change="(e) => hanldeChange('purOrg', e)"
          />
        </mt-form-item>
        <mt-form-item prop="siteCode" :label="$t('工厂')">
          <mt-select
            v-model="formData.siteCode"
            :data-source="siteList"
            :fields="{ text: 'text', value: 'orgCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :disabled="!formData.purOrgCode"
            :placeholder="$t('请选择工厂')"
            @change="(e) => hanldeChange('site', e)"
          />
        </mt-form-item>
        <mt-form-item prop="purId" :label="$t('采购员')">
          <debounce-filter-select
            v-model="formData.purId"
            :request="getPurchaserList"
            :data-source="purchaserList"
            :fields="{ text: 'text', value: 'employeeId' }"
            :show-clear-button="false"
            :placeholder="$t('请选择采购员')"
            @change="(e) => hanldeChange('purchaser', e)"
          />
        </mt-form-item>
        <mt-form-item prop="priceClassification" :label="$t('价格分类')">
          <mt-select
            v-model="formData.priceClassification"
            :data-source="priceClassifyList"
            :show-clear-button="true"
            :placeholder="$t('请选择价格分类')"
          />
        </mt-form-item>
        <mt-form-item prop="pointType" :label="$t('定价类型')">
          <mt-select
            v-model="formData.pointType"
            :data-source="pricingTypeList"
            :placeholder="$t('请选择定价类型')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="purRemark" :label="$t('备注')">
          <mt-input
            v-model="formData.purRemark"
            :show-clear-button="true"
            :maxlength="500"
            :placeholder="$t('请输入备注')"
          />
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
import debounceFilterSelect from 'ROUTER_PURCHASE_RFX/components/debounceFilterSelect'
import mixin from '../config/mixin'
import { pricingTypeList, priceClassifyList, decidePriceTypeList } from '../config/index'

export default {
  components: { debounceFilterSelect },
  mixins: [mixin],
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      type: 'add',
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {
        priceObjectName: this.$t('人力外包定价'),
        decidePriceType: 8,
        priceClassification: 4
      },
      pricingTypeList,
      priceClassifyList,
      decidePriceTypeList,
      formRules: {
        //定价单类型
        decidePriceType: {
          required: true,
          message: this.$t('请输入定价单类型'),
          trigger: 'blur'
        },
        // 采购员
        purId: {
          required: true,
          message: this.$t('请输入采购员'),
          trigger: 'blur'
        },
        // 标题
        title: {
          required: true,
          message: this.$t('请输入标题'),
          trigger: 'blur'
        },
        // 公司
        companyCode: {
          required: true,
          message: this.$t('请输入公司'),
          trigger: 'blur'
        },
        // 	采购组织
        purOrgCode: {
          required: true,
          message: this.$t('请输入采购组织'),
          trigger: 'blur'
        },
        // 定价对对像
        priceObjectName: {
          required: true,
          message: this.$t('请输入定价对象'),
          trigger: 'blur'
        },
        priceClassification: {
          required: true,
          message: this.$t('请选择价格分类'),
          trigger: 'blur'
        },
        PricingType: {
          required: true,
          message: this.$t('请选择定价类型'),
          trigger: 'blur'
        },
        siteName: {
          required: true,
          message: this.$t('请输入工厂'),
          trigger: 'blur'
        },
        pointType: {
          required: true,
          message: this.$t('请输入定价类型'),
          trigger: 'blur'
        }
      }
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    // 下拉选择值改变触发
    hanldeChange(key, e) {
      const {
        id,
        orgCode,
        orgName,
        organizationCode,
        organizationName,
        employeeId,
        employeeCode,
        employeeName
      } = e.itemData || {}
      switch (key) {
        // 公司
        case 'company':
          this.formData = {
            ...this.formData,
            companyId: id,
            companyCode: orgCode,
            companyName: orgName,
            purOrgId: null,
            purOrgCode: null,
            purOrgName: null,
            siteId: null,
            siteCode: null,
            siteName: null
          }
          this.getPurchaseOrgListByCompanyId(id)
          break
        // 采购组织
        case 'purOrg':
          this.formData = {
            ...this.formData,
            purOrgId: id,
            purOrgCode: organizationCode,
            purOrgName: organizationName
          }
          this.getSiteListById(this.formData.companyId, id)
          break
        // 工厂
        case 'site':
          this.formData = {
            ...this.formData,
            siteId: id,
            siteCode: orgCode,
            siteName: orgName
          }
          break
        // 采购员
        case 'purchaser':
          this.formData = {
            ...this.formData,
            purId: employeeId,
            purCode: employeeCode,
            purName: employeeName
          }
          break
        default:
          break
      }
    },
    // 取消
    cancel() {
      this.$emit('cancel-function')
    },
    // 确定
    confirm() {
      this.$refs.dataForm.validate(async (valid) => {
        if (valid) {
          const res = await this.$API.manpowerOutsourcing.addManOutPricing(this.formData)
          if (res.code === 200) {
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
            this.$emit('confirm-function')
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  padding-top: 20px;
}
</style>
