<template>
  <div class="full-height">
    <mt-local-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickCellTitle="handleClickCellTitle"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="itemCodes" :label="$t('生产线编码')" label-style="top">
              <custom-select
                v-model="searchFormModel.itemCodes"
                :label-name="$t('生产线编码')"
                :placeholder="$t('请输入生产线编码')"
              />
            </mt-form-item>
            <mt-form-item prop="categoryCodes" :label="$t('品类编码')" label-style="top">
              <custom-select
                v-model="searchFormModel.categoryCodes"
                :label-name="$t('品类编码')"
                :placeholder="$t('请输入品类编码')"
              />
            </mt-form-item>
            <mt-form-item prop="supplierCodes" :label="$t('供应商编码')" label-style="top">
              <custom-select
                v-model="searchFormModel.supplierCodes"
                :label-name="$t('供应商编码')"
                :placeholder="$t('请输入供应商编码')"
              />
            </mt-form-item>
            <mt-form-item prop="supplierCodes" :label="$t('公司编码')" label-style="top">
              <custom-select
                v-model="searchFormModel.supplierCodes"
                :label-name="$t('公司编码')"
                :placeholder="$t('请输入公司编码')"
              />
            </mt-form-item>
            <mt-form-item prop="siteCodes" :label="$t('工厂编码')" label-style="top">
              <custom-select
                v-model="searchFormModel.siteCodes"
                :label-name="$t('工厂编码')"
                :placeholder="$t('请输入工厂编码')"
              />
            </mt-form-item>
            <mt-form-item prop="purOrgCodes" :label="$t('采购组织编码')" label-style="top">
              <custom-select
                v-model="searchFormModel.purOrgCodes"
                :label-name="$t('采购组织编码')"
                :placeholder="$t('请输入采购组织编码')"
              />
            </mt-form-item>
            <mt-form-item prop="sourceCodes" :label="$t('来源单号')" label-style="top">
              <custom-select
                v-model="searchFormModel.sourceCodes"
                :label-name="$t('来源单号')"
                :placeholder="$t('请输入来源单号')"
              />
            </mt-form-item>
            <mt-form-item prop="priceRecordCode" :label="$t('价格记录编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.priceRecordCode"
                :show-clear-button="true"
                :placeholder="$t('请输入价格记录编码')"
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-select
                v-model="searchFormModel.status"
                :data-source="statusList"
                :show-clear-button="true"
                :placeholder="$t('请选择状态')"
              />
            </mt-form-item>
            <mt-form-item prop="itemName" :label="$t('生产线名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.itemName"
                :show-clear-button="true"
                :placeholder="$t('请输入生产线名称')"
              />
            </mt-form-item>
            <mt-form-item prop="categoryName" :label="$t('品类名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.categoryName"
                :show-clear-button="true"
                :placeholder="$t('请输入品类名称')"
              />
            </mt-form-item>
            <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.supplierName"
                :show-clear-button="true"
                :placeholder="$t('请输入供应商名称')"
              />
            </mt-form-item>
            <mt-form-item prop="companyName" :label="$t('公司名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.companyName"
                :show-clear-button="true"
                :placeholder="$t('请输入公司名称')"
              />
            </mt-form-item>
            <mt-form-item prop="siteName" :label="$t('工厂名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.siteName"
                :show-clear-button="true"
                :placeholder="$t('请输入工厂名称')"
              />
            </mt-form-item>
            <mt-form-item prop="validStartTime" :label="$t('生效日期')" label-style="top">
              <mt-date-picker
                v-model="tempForm.validStartTime"
                :open-on-focus="true"
                :allow-edit="false"
                format="yyyy-MM-dd"
                :placeholder="$t('请选择生效日期')"
                @change="(e) => handleDateChange('validStartTime', e)"
              />
            </mt-form-item>
            <mt-form-item prop="validEndTime" :label="$t('失效日期')" label-style="top">
              <mt-date-picker
                v-model="tempForm.validEndTime"
                :open-on-focus="true"
                :allow-edit="false"
                format="yyyy-MM-dd"
                :placeholder="$t('请选择失效日期')"
                @change="(e) => handleDateChange('validEndTime', e)"
              />
            </mt-form-item>
            <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
              <mt-input
                v-model="searchFormModel.createUserName"
                :show-clear-button="true"
                :placeholder="$t('请输入创建人')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-local-template-page>
  </div>
</template>

<script>
// 引入本地的emplate-page组件
import MtLocalTemplatePage from '@/components/template-page'
import CustomSelect from '@/components/customSelect'
import { manHourUnitPricePageConfig, statusList } from './config/index'
// import { statusList } from './index'

export default {
  components: { MtLocalTemplatePage, CustomSelect },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {
        status: null,
        purchaseAgentName: null
      },
      tempForm: {},
      pageConfig: manHourUnitPricePageConfig,
      statusList
    }
  },
  mounted() {},
  methods: {
    // 日期格式化
    handleDateChange(key, val) {
      this.searchFormModel[key] = val?.valueOf()
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.tempForm = {}
    },
    // 点击单元格内容
    handleClickCellTitle(e) {
      if (e.field == 'detail') {
        this.handleViewDetail(e.data)
      }
    },
    // 查看明细
    handleViewDetail(row) {
      const { id, itemCode, siteCode } = row
      this.$dialog({
        modal: () => import('./dialog/applyModelRangeDialog.vue'),
        data: {
          title: this.$t('适用机型范围'),
          id,
          itemCode,
          siteCode
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  .hidden {
    display: none;
  }
  /deep/.e-headercelldiv {
    padding: 0 0.6em 0 0.6em !important;
  }
}
</style>
