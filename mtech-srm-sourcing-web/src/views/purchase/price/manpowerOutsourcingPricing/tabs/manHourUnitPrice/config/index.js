import { i18n } from '@/main.js'

// 状态
export const statusList = [
  { value: 0, text: i18n.t('草稿'), cssClass: '' },
  { value: 1, text: i18n.t('启用'), cssClass: '' },
  { value: 2, text: i18n.t('禁用'), cssClass: '' },
  { value: 3, text: i18n.t('未合格'), cssClass: '' }
]
// 报价属性
export const quoteAttributeList = [
  { value: 'mailing_price', text: i18n.t('寄售价'), cssClass: '' },
  { value: 'standard_price', text: i18n.t('标准价'), cssClass: '' },
  { value: 'outsource', text: i18n.t('委外价'), cssClass: '' }
]
// 价格生效方式
export const quoteModeList = [
  { value: 'in_warehouse', text: i18n.t('按照入库'), cssClass: '' },
  { value: 'out_warehouse', text: i18n.t('按出库'), cssClass: '' },
  { value: 'order_date', text: i18n.t('按订单日期'), cssClass: '' }
]

// 人力工时单价
const manHourUnitPriceColumnData = [
  {
    field: 'id',
    width: 0,
    isPrimaryKey: true
  },
  {
    field: 'itemCode',
    headerText: i18n.t('生产线编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('生产线名称')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'detail',
    headerText: i18n.t('明细'),
    cssClass: 'field-content',
    valueConverter: {
      type: 'placeholder',
      placeholder: i18n.t('明细')
    }
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: statusList,
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('含税单价')
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('未税单价')
  },
  {
    field: 'taxRate',
    headerText: i18n.t('税率')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司'),
    width: '200',
    valueAccessor: (field, data) => {
      return data.companyCode ? data.companyCode + '-' + data.companyName : '-'
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: '200',
    valueAccessor: (field, data) => {
      return data.siteCode ? data.siteCode + '-' + data.siteName : '-'
    }
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织'),
    valueAccessor: (field, data) => {
      return data.purOrgCode ? data.purOrgCode + '-' + data.purOrgName : '-'
    }
  },
  {
    field: 'quoteAttribute',
    headerText: i18n.t('报价属性'),
    valueConverter: {
      type: 'map',
      map: [...quoteAttributeList, { value: '', text: '-', cssClass: '' }],
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'quoteMode',
    headerText: i18n.t('价格生效方式'),
    valueConverter: {
      type: 'map',
      map: [...quoteModeList, { value: '', text: '-', cssClass: '' }],
      fields: { text: 'text', value: 'value' }
    }
  },
  {
    field: 'priceRecordCode',
    headerText: i18n.t('价格记录编码')
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种'),
    valueAccessor: (field, row) => {
      return row.currencyCode ? row.currencyCode + '-' + row.currencyName : ''
    }
  },
  {
    field: 'sourceCode',
    headerText: i18n.t('来源单号')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    field: 'validStartTime',
    headerText: i18n.t('生效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'validEndTime',
    headerText: i18n.t('失效日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  }
]
export const manHourUnitPricePageConfig = [
  {
    title: i18n.t('人力工时单价'),
    gridId: '99482867-253c-4e27-b2fc-5f6a407127ce',
    activatedRefresh: false,
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    useToolTemplate: false,
    toolbar: {
      useBaseConfig: false,
      tools: [
        [],
        // [{ id: 'download', icon: 'icon_solid_export', title: i18n.t('导出') }],
        ['Refresh', 'Setting']
      ]
    },
    grid: {
      virtualPageSize: 30,
      enableVirtualization: true,
      allowSelection: false,
      allowFiltering: true,
      showSelected: false,
      columnData: manHourUnitPriceColumnData,
      asyncConfig: {
        url: '/price/tenant/hro/price/record/query'
      }
    }
  }
]
