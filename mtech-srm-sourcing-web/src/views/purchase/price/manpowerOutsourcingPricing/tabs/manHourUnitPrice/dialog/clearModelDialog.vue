<template>
  <mt-dialog ref="dialogRef" :buttons="buttons" :header="modalData.title" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-local-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleCustomReset="handleCustomReset"
      >
        <template v-slot:quick-search-form>
          <div class="custom-form-box">
            <mt-form ref="searchFormRef" :model="searchFormModel">
              <mt-form-item prop="modelCode" :label="$t('ID')" label-style="top">
                <mt-input
                  v-model="searchFormModel.modelCode"
                  :show-clear-button="true"
                  :placeholder="$t('请输入ID')"
                />
              </mt-form-item>
              <mt-form-item prop="prodtLine" :label="$t('生产线')" label-style="top">
                <mt-input
                  v-model="searchFormModel.prodtLine"
                  :show-clear-button="true"
                  :placeholder="$t('请输入生产线')"
                />
              </mt-form-item>
              <mt-form-item prop="settleModel" :label="$t('结算机型')" label-style="top">
                <mt-input
                  v-model="searchFormModel.settleModel"
                  :show-clear-button="true"
                  :placeholder="$t('请输入结算机型')"
                />
              </mt-form-item>
            </mt-form>
          </div>
        </template>
      </mt-local-template-page>
    </div>
  </mt-dialog>
</template>

<script>
import MtLocalTemplatePage from '@/components/template-page'
import { clearModelPageConfig } from './config'

export default {
  components: { MtLocalTemplatePage },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      pageConfig: [],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  mounted() {
    this.$refs.dialogRef.ejsRef.show()
    this.pageConfig = clearModelPageConfig(this)
  },
  methods: {
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = ['startCreatTime', 'endCreatTime'].includes(key) ? 0 : null
        }
      }
      this.handleSearch()
    },
    async confirm() {
      let records = this.$refs.templateRef.getCurrentTabRef().gridRef.getMtechGridRecords()
      if (records.length === 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      this.$emit('confirm-function', records)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
