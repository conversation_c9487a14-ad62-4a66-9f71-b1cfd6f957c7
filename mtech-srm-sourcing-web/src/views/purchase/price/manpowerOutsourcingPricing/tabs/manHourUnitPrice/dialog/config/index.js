import { i18n } from '@/main.js'

// 结算机型
export const clearModelColumnData = [
  {
    width: 50,
    type: 'checkbox'
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    // eslint-disable-next-line no-empty-pattern
    formatter: function ({}, item) {
      return item.siteCode + '-' + item.siteName
    }
  },
  {
    field: 'modelCode',
    headerText: i18n.t('ID')
  },
  {
    field: 'prodtLine',
    headerText: i18n.t('生产线')
  },
  {
    field: 'settleModel',
    headerText: i18n.t('结算机型')
  }
]
export const clearModelPageConfig = (that) => [
  {
    gridId: '736d3925-d1fb-4f18-8c71-316bf87bdd9a',
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    toolbar: [],
    grid: {
      columnData: clearModelColumnData,
      allowSorting: false,
      allowSelection: true,
      selectionSettings: {
        type: 'Multiple',
        mode: 'Row'
      },
      asyncConfig: {
        url: '/sourcing/tenant/hro/beltlineModel_yieldRel/query',
        params: {
          prodtLineCode: that.modalData?.itemCode,
          siteCode: that.modalData?.siteCode,
          startCreatTime: 0,
          endCreatTime: 0
        }
      }
    }
  }
]
