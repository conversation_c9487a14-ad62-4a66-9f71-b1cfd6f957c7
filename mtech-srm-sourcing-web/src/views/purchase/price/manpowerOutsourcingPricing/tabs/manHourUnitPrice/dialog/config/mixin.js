import cloneDeep from 'lodash/cloneDeep'

export default {
  data() {
    return {
      loading: false,
      tableData: [],
      priceTypeList: [],
      formulaList: [],
      toolbar: [
        { code: 'add', name: this.$t('新增'), status: 'info' },
        { code: 'delete', name: this.$t('删除'), status: 'info' },
        { code: 'import', name: this.$t('导入'), status: 'info' }
      ]
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'modelCode',
          title: this.$t('ID（机型编码）')
        },
        {
          field: 'prodtLine',
          title: this.$t('生产线')
        },
        {
          field: 'settleModel',
          title: this.$t('结算机型')
        },
        {
          field: 'calcFormula',
          title: this.$t('计算公式'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.calcFormula}
                  options={this.formulaList}
                  filterable
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'priceType',
          title: this.$t('价格类型'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.priceType}
                  options={this.priceTypeList}
                  option-props={{ value: 'itemName', label: 'itemName' }}
                  transfer
                />
              ]
            }
          }
        }
      ]
    },
    dataList() {
      const list = cloneDeep(this.tableData)
      const dataList = []
      list.forEach((item) => {
        !item.id
          ? dataList.push(item)
          : this.tableRef.isUpdateByRow(item) && (this.editObj[item.id] = item)
      })
      for (const id in this.editObj) {
        dataList.push(this.editObj[id])
      }
      return dataList
    }
  },
  mounted() {
    this.getFormulaList()
    this.getPriceTypelist()
  },
  methods: {
    async getPriceTypelist() {
      const res = await this.$API.masterData.dictionaryGetList({ dictCode: 'PRICETYPE' })
      if (res.code === 200) {
        this.priceTypeList = res.data || []
      }
    },
    async getFormulaList() {
      const params = {
        hroPriceRecordId: this.modalData?.id
      }
      const res = await this.$API.manpowerOutsourcing.queryFormulaList(params)
      if (res.code === 200) {
        this.formulaList = []
        res.data?.forEach((item) => {
          this.formulaList.push({ value: item, label: item })
        })
      }
    }
  }
}
