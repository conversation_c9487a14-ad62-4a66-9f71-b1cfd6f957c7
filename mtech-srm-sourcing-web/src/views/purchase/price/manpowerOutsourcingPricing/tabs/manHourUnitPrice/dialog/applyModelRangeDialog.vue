<template>
  <vxe-modal
    ref="modalRef"
    :title="modalData.title"
    width="900"
    height="600"
    destroy-on-close
    show-footer
    resize
    transfer
  >
    <template #default>
      <div class="dialog-content">
        <!-- 自定义查询条件 -->
        <collapse-search
          class="toggle-container"
          :default-expand="false"
          @reset="handleReset"
          @search="handleSearch"
        >
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="modelCode" :label="$t('ID（机型编码）')" label-style="top">
              <mt-input
                v-model="searchFormModel.modelCode"
                :show-clear-button="true"
                :placeholder="$t('请输入ID（机型编码）')"
              />
            </mt-form-item>
            <mt-form-item prop="prodtLine" :label="$t('生产线')" label-style="top">
              <mt-input
                v-model="searchFormModel.prodtLine"
                :show-clear-button="true"
                :placeholder="$t('请输入生产线')"
              />
            </mt-form-item>
            <mt-form-item prop="settleModel" :label="$t('结算机型')" label-style="top">
              <mt-input
                v-model="searchFormModel.settleModel"
                :show-clear-button="true"
                :placeholder="$t('请输入结算机型')"
              />
            </mt-form-item>
            <mt-form-item prop="calcFormula" :label="$t('计算公式')" label-style="top">
              <mt-input
                v-model="searchFormModel.calcFormula"
                :show-clear-button="true"
                :placeholder="$t('请输入计算公式')"
              />
            </mt-form-item>
            <mt-form-item prop="priceType" :label="$t('价格类型')" label-style="top">
              <mt-select
                v-model="searchFormModel.priceType"
                :data-source="priceTypeList"
                :show-clear-button="true"
                :fields="{ value: 'itemName', text: 'itemName' }"
                :placeholder="$t('请选择价格类型')"
              />
            </mt-form-item>
          </mt-form>
        </collapse-search>
        <!-- 表格 -->
        <sc-table
          ref="sctableRef"
          row-id="customId"
          grid-id="4ae262b1-873a-440c-b001-4528ce800f8e"
          keep-source
          :loading="loading"
          :is-show-refresh-bth="true"
          :columns="columns"
          :table-data="tableData"
          :edit-config="editConfig"
          @refresh="handleSearch"
        >
          <template slot="custom-tools">
            <vxe-button
              v-for="item in toolbar"
              :key="item.code"
              :status="item.status"
              :icon="item.icon"
              size="small"
              @click="handleClickToolBar(item)"
              >{{ item.name }}</vxe-button
            >
          </template>
        </sc-table>
        <!-- 分页 -->
        <mt-page
          ref="pageRef"
          class="flex-keep custom-page"
          :page-settings="pageSettings"
          :total-pages="pageSettings.totalPages"
          @currentChange="handleCurrentChange"
          @sizeChange="handleSizeChange"
        />
      </div>
      <upload-excel-dialog
        ref="uploadInitBalanceRef"
        :down-template-name="downTemplateName"
        :request-urls="requestUrls"
        :down-template-params="downTemplateParams"
        :upload-params="uploadParams"
        file-key="excel"
        @closeUploadExcel="showUploadExcel(false)"
        @upExcelConfirm="upExcelConfirm"
      />
    </template>
    <template #footer>
      <vxe-button type="text" @click="cancel">{{ $t('取消') }}</vxe-button>
      <vxe-button type="text" @click="confirm">{{ $t('确定') }}</vxe-button>
    </template>
  </vxe-modal>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { Modal as VxeModal } from 'vxe-table'
import mixin from './config/mixin'
import pagingMixin from '@/mixins/paging.js'

export default {
  components: {
    ScTable,
    CollapseSearch,
    VxeModal,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  mixins: [mixin, pagingMixin],
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      editConfig: {
        trigger: 'click',
        mode: 'row',
        showStatus: true
      },
      downTemplateName: '',
      requestUrls: {},
      downTemplateParams: {},
      uploadParams: {},
      editObj: {}
    }
  },
  mounted() {
    this.$refs.modalRef.open()
  },
  methods: {
    // 查询
    async getTableData() {
      const params = {
        page: this.pageInfo,
        hroPriceRecordIds: [this.modalData?.id],
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.manpowerOutsourcing
        .queryModelRangeList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.total = res.data?.total
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          if (!selectedRecords.length) {
            this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
            return
          }
          this.handleDelete(selectedRecords)
          break
        case 'import':
          this.handleImport()
          break
        default:
          break
      }
    },
    // 新增
    handleAdd() {
      // 记录编辑的行
      const list = this.tableRef.getUpdateRecords()
      list.forEach((item) => {
        item.id && (this.editObj[item.id] = item)
      })
      this.$dialog({
        modal: () => import('./clearModelDialog.vue'),
        data: {
          title: this.$t('选择结算机型'),
          itemCode: this.modalData.itemCode,
          siteCode: this.modalData.siteCode
        },
        success: (list) => {
          list.forEach(async (item) => {
            const { modelCode, prodtLineCode, prodtLine, settleModel } = item
            const newRowData = {
              hroPriceRecordId: this.modalData.id,
              modelCode,
              prodtLineCode,
              prodtLine,
              settleModel
            }
            const { row: newRow } = await this.tableRef.insertAt(newRowData)
            this.tableData.unshift(newRow)
            this.tableRef.setEditRow(newRow)
          })
        }
      })
    },
    // 删除
    handleDelete(list) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中数据？')
        },
        success: async () => {
          const idList = []
          list.forEach(async (item) => {
            !item.id ? await this.tableRef.remove(item) : idList.push(item.id)
          })
          if (idList.length === 0) {
            this.tableData = this.tableRef.getTableData().fullData
            return
          }
          const res = await this.$API.manpowerOutsourcing.deleteModelRangeList(idList)
          if (res.code === 200) {
            this.$toast({ content: this.$t('删除成功'), type: 'success' })
            this.handleSearch()
          }
        }
      })
    },
    // 上传（显示弹窗）
    handleImport() {
      this.requestUrls = {
        templateUrlPre: 'manpowerOutsourcing',
        templateUrl: 'downloadModelRangeTemplate',
        uploadUrl: 'importModelRangeList'
      }
      this.downTemplateParams = {
        hroPriceRecordId: this.modalData.id
      }
      this.uploadParams = {
        hroPriceRecordId: this.modalData.id
      }
      this.showUploadExcel(true)
    },
    // 展示/不展示 上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadInitBalanceRef.uploadData = [] // 清空数据
        this.$refs.uploadInitBalanceRef.$refs.uploader.files = []
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.handleSearch()
    },
    // 确定
    async confirm() {
      if (this.dataList?.length === 0) {
        this.$emit('confirm-function')
        return
      }
      const res = await this.$API.manpowerOutsourcing.saveModelRangeList(this.dataList)
      if (res.code === 200) {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        this.$emit('confirm-function')
      }
    },
    // 取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 5px 0 0 0;
  height: 100%;
  width: 100%;
}
::v-deep {
  .collapse-search-container .search-area {
    padding-left: 12px;
  }
  .vxe-modal--box {
    border: none !important;
  }
  .vxe-modal--header {
    background-color: #31374e !important;
    color: #fff !important;
  }
  .vxe-modal--content {
    white-space: unset !important;
  }
}
</style>
