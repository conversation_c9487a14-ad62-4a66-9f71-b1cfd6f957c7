<template>
  <div class="full-height">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="siteCode" :label="$t('工厂')" label-style="top">
          <mt-select
            v-model="searchFormModel.siteCode"
            css-class="rule-element"
            :data-source="factoryList"
            :fields="{ text: 'text', value: 'siteCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :filtering="onFiltering"
            :placeholder="$t('请选择工厂')"
          />
        </mt-form-item>
        <mt-form-item prop="prodtLineCode" :label="$t('生产线编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.prodtLineCode"
            :show-clear-button="true"
            :placeholder="$t('请输入生产线编码')"
          />
        </mt-form-item>
        <mt-form-item prop="prodtLine" :label="$t('生产线（资源）')" label-style="top">
          <mt-input
            v-model="searchFormModel.prodtLine"
            :show-clear-button="true"
            :placeholder="$t('请输入生产线（资源）')"
          />
        </mt-form-item>
        <mt-form-item prop="modelCode" :label="$t('ID（机型编码）')" label-style="top">
          <mt-input
            v-model="searchFormModel.modelCode"
            :show-clear-button="true"
            :placeholder="$t('请输入ID（机型编码）')"
          />
        </mt-form-item>
        <mt-form-item prop="prodtModel" :label="$t('生产机型')" label-style="top">
          <mt-input
            v-model="searchFormModel.prodtModel"
            :show-clear-button="true"
            :placeholder="$t('请输入生产机型')"
          />
        </mt-form-item>
        <mt-form-item prop="settleModel" :label="$t('结算机型')" label-style="top">
          <mt-input
            v-model="searchFormModel.settleModel"
            :show-clear-button="true"
            :placeholder="$t('请输入结算机型')"
          />
        </mt-form-item>
        <mt-form-item prop="createDateRange" :label="$t('创建日期')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.createDateRange"
            :show-clear-button="true"
            :allow-edit="false"
            :open-on-focus="true"
            :placeholder="$t('请选择创建日期')"
            @change="handleDateChange"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      row-id="id"
      grid-id="a5182c02-33a9-47a4-96ed-7d9679847d53"
      :keep-source="true"
      :edit-config="editConfig"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />

    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-name="downTemplateName"
      :down-template-params="downTemplateParams"
      :request-urls="requestUrls"
      file-key="importFile"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import pagingMixin from '@/mixins/paging.js'
import { download, getHeadersFileName } from '@/utils/utils'
import debounce from 'lodash.debounce'

export default {
  components: {
    ScTable,
    CollapseSearch,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  mixins: [mixin, pagingMixin],
  data() {
    return {
      searchFormModel: {
        startCreatTime: 0,
        endCreatTime: 0
      },
      tableData: [],
      loading: false,
      editConfig: {
        trigger: 'click',
        mode: 'row',
        showStatus: true
      },
      downTemplateName: '',
      downTemplateParams: {},
      requestUrls: {}
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  methods: {
    // 模糊查询-工厂
    onFiltering: debounce(function (e) {
      this.getFactoryList(e?.text)
    }, 800),
    // 处理日期
    handleDateChange(e) {
      const { startDate, endDate } = e
      this.searchFormModel['startCreatTime'] = startDate ? startDate.getTime() : 0
      this.searchFormModel['endCreatTime'] = endDate ? endDate.getTime() + 86400000 : 0
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = ['startCreatTime', 'endCreatTime'].includes(key) ? 0 : null
        }
      }
      this.handleSearch()
    },
    // 获取table数据
    async getTableData() {
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.plmhpRelationReport
        .queryPlmhprrList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.total = res.data.total
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      switch (e.code) {
        case 'save':
          this.handleSave()
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          this.handleExport()
          break
        case 'update':
          this.handleUpdate()
          break
        default:
          break
      }
    },
    // 保存
    async handleSave() {
      const updatedList = this.tableRef.getUpdateRecords()
      const res = await this.$API.plmhpRelationReport.savePlmhprrList(updatedList)
      if (res.code === 200) {
        this.$toast({ content: this.$t('保存成功'), type: 'success' })
        this.getTableData()
      }
    },
    // 导入
    handleImport() {
      this.requestUrls = {
        templateUrlPre: 'plmhpRelationReport',
        templateUrl: 'downloadPlmhprrTemplate',
        uploadUrl: 'importPlmhprr'
      }
      this.showUploadExcel(true)
    },
    // 导出
    async handleExport() {
      const params = {
        page: {
          current: 1,
          size: 10000
        },
        ...this.searchFormModel
      }
      const res = await this.$API.plmhpRelationReport.exportPlmhprr(params)
      if (res.data) {
        this.$toast({ type: 'success', content: this.$t('正在导出，请稍后！') })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      }
    },
    // 更新
    async handleUpdate() {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const idList = []
      selectedRecords.forEach((item) => idList.push(item.id))

      const params = {
        idList,
        pageSize: this.pageInfo.size
      }
      const res = await this.$API.plmhpRelationReport.updatePlmhprrList(params)
      if (res.code === 200) {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        this.getTableData()
      }
    },
    // 展示/不展示 上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = [] // 清空数据
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 导入成功
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.handleSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
