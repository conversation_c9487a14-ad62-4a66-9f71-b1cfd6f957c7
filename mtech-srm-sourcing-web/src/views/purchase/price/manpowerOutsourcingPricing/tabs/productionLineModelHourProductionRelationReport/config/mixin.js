import XEUtils from 'xe-utils'

export default {
  data() {
    return {
      toolbar: [
        { code: 'save', name: this.$t('保存'), status: 'info' },
        { code: 'import', name: this.$t('导入'), status: 'info' },
        { code: 'export', name: this.$t('导出'), status: 'info' },
        { code: 'update', name: this.$t('更新'), status: 'info' }
      ],
      factoryList: [],
      productionLineList: []
    }
  },
  computed: {
    columns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'siteCode',
          title: this.$t('工厂'),
          minWidth: 150,
          slots: {
            default: ({ row }) => {
              return [<span>{(row.siteCode || '') + '-' + (row.siteName || '')}</span>]
            }
          }
        },
        {
          field: 'prodtLineCode',
          title: this.$t('生产线编码'),
          minWidth: 150
        },
        {
          field: 'prodtLine',
          title: this.$t('生产线（资源）'),
          minWidth: 120
        },
        {
          field: 'modelCode',
          title: this.$t('ID（机型编码）'),
          minWidth: 120
        },
        {
          field: 'prodtModel',
          title: this.$t('生产机型')
        },
        {
          field: 'settleModel',
          title: this.$t('结算机型')
        },
        {
          field: 'moduleHourOutput',
          title: this.$t('模组时产'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.moduleHourOutput}
                  clearable
                  type='number'
                  placeholder={this.$t('请输入')}
                />
              ]
            }
          }
        },
        {
          field: 'cbuHourOutput',
          title: this.$t('整机时产'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.cbuHourOutput}
                  clearable
                  type='number'
                  placeholder={this.$t('请输入')}
                />
              ]
            }
          }
        },
        {
          field: 'perCapitaHourOutput',
          title: this.$t('人均时产'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.perCapitaHourOutput}
                  clearable
                  type='number'
                  placeholder={this.$t('请输入')}
                />
              ]
            }
          }
        },
        {
          field: 'domainRentalFee',
          title: this.$t('场地租赁费'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.domainRentalFee}
                  clearable
                  type='number'
                  placeholder={this.$t('请输入')}
                />
              ]
            }
          }
        },
        {
          field: 'equipmentRentalFee',
          title: this.$t('设备租赁费'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.equipmentRentalFee}
                  clearable
                  type='number'
                  placeholder={this.$t('请输入')}
                />
              ]
            }
          }
        },
        {
          field: 'moduleDomainRentalFee',
          title: this.$t('模组场地租赁费'),
          minWidth: 150,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.moduleDomainRentalFee}
                  clearable
                  type='number'
                  placeholder={this.$t('请输入')}
                />
              ]
            }
          }
        },
        {
          field: 'moduleEquipmentRentalFee',
          title: this.$t('模组设备租赁费'),
          minWidth: 150,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.moduleEquipmentRentalFee}
                  clearable
                  type='number'
                  placeholder={this.$t('请输入')}
                />
              ]
            }
          }
        },
        {
          field: 'cbuDomainRentalFee',
          title: this.$t('整机场地租赁费'),
          minWidth: 150,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.cbuDomainRentalFee}
                  clearable
                  type='number'
                  placeholder={this.$t('请输入')}
                />
              ]
            }
          }
        },
        {
          field: 'cbuEquipmentRentalFee',
          title: this.$t('整机设备租赁费'),
          minWidth: 150,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.cbuEquipmentRentalFee}
                  clearable
                  type='number'
                  placeholder={this.$t('请输入')}
                />
              ]
            }
          }
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          slots: {
            default: ({ row }) => {
              const createTime = XEUtils.toDateString(row.createTime, 'yyyy-MM-dd HH:mm:ss')
              return [<span>{createTime}</span>]
            }
          }
        },
        {
          field: 'updateUserName',
          title: this.$t('更新人')
        },
        {
          field: 'updateTime',
          title: this.$t('更新时间'),
          slots: {
            default: ({ row }) => {
              const updateTime = XEUtils.toDateString(row.updateTime, 'yyyy-MM-dd HH:mm:ss')
              return [<span>{updateTime}</span>]
            }
          }
        }
      ]
    }
  },
  mounted() {
    this.getFactoryList()
  },
  methods: {
    // 获取工厂下拉列表
    async getFactoryList(fuzzyParam) {
      const res = await this.$API.assessManage.fuzzySiteQuery({ fuzzyParam })
      if (res.code === 200) {
        res.data?.forEach((item) => {
          item.text = item.siteCode + '-' + item.siteName
        })
        this.factoryList = res.data || []
      }
    }
  }
}
