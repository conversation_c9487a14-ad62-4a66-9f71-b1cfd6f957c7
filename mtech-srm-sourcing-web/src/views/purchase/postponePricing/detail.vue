<template>
  <div class="postpone-full-height">
    <div class="top-container toggle-container" :class="[!isExpand && 'top-hidden']">
      <div class="top-content">
        <div class="top-info">
          <div class="title">
            <div v-show="!isExpand">
              <span>{{ dataForm.rfxCode }}</span>
              <span class="sub-title">{{ dataForm.rfxName }}</span>
            </div>
          </div>
          <div class="top-btns">
            <vxe-button
              v-for="(item, index) in detailToolbar"
              v-show="!item.isHidden"
              :key="index"
              :icon="item.icon"
              :status="item.status"
              :disabled="item.disabled"
              size="small"
              @click="handleClickToolBar(item)"
              >{{ item.name }}</vxe-button
            >
          </div>
        </div>
        <div class="top-form">
          <mt-form ref="dataFormRef" :model="dataForm" :rules="formRules">
            <mt-form-item prop="rfxCode" :label="$t('定价单编码')" label-style="top">
              <vxe-input v-model="dataForm.rfxCode" disabled />
            </mt-form-item>
            <mt-form-item prop="rfxName" :label="$t('定价单标题')" label-style="top">
              <vxe-input
                v-model="dataForm.rfxName"
                clearable
                :disabled="!editable"
                :placeholder="$t('请输入定价单标题')"
              />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
              <vxe-select
                v-model="dataForm.companyCode"
                :options="companyList"
                :option-props="{ label: 'text', value: 'orgCode' }"
                clearable
                filterable
                :disabled="!editable || itemLength !== 0"
                :placeholder="$t('请选择公司')"
                @change="(e) => handleValueChange('company', e)"
              />
            </mt-form-item>
            <mt-form-item prop="purchaseOrgCode" :label="$t('采购组织')">
              <vxe-select
                v-model="dataForm.purchaseOrgCode"
                :options="purchaseOrgList"
                :option-props="{ label: 'text', value: 'organizationCode' }"
                clearable
                filterable
                :disabled="!editable || !dataForm.companyCode || itemLength !== 0"
                :placeholder="$t('请选择采购组织')"
                @change="(e) => handleValueChange('purchaseOrg', e)"
              />
            </mt-form-item>
            <mt-form-item prop="factoryCode" :label="$t('工厂')" label-style="top">
              <vxe-select
                v-model="dataForm.factoryCode"
                :options="factoryList"
                :option-props="{ label: 'text', value: 'orgCode' }"
                clearable
                filterable
                :disabled="!editable || !dataForm.purchaseOrgCode || itemLength !== 0"
                :placeholder="$t('请选择工厂')"
                @change="(e) => handleValueChange('factory', e)"
              />
            </mt-form-item>
            <mt-form-item prop="extendFactoryList" :label="$t('工厂拓展')" label-style="top">
              <vxe-select
                v-model="dataForm.extendFactoryList"
                :options="sourcingExpandList"
                :option-props="{ label: 'text', value: 'value' }"
                :multi-char-overflow="
                  dataForm.extendFactoryList && dataForm.extendFactoryList.length > 1 ? 8 : -1
                "
                clearable
                filterable
                multiple
                :disabled="[1, 2].includes(dataForm.status) || !dataForm.factoryCode"
                :placeholder="$t('请选择工厂拓展')"
                :title="extendFactoryTitle"
                @change="(e) => handleValueChange('expand', e)"
              />
            </mt-form-item>
            <mt-form-item prop="fixPriceObject" :label="$t('定价对象')" label-style="top">
              <vxe-select
                v-model="dataForm.fixPriceObject"
                :options="fixPriceObjectTypeList"
                :option-props="{ label: 'sourcingObj', value: 'sourcingObj' }"
                :disabled="!editable || itemLength !== 0"
                :placeholder="$t('请选择定价对象')"
                @change="(e) => handleValueChange('fixPriceObject', e)"
              />
            </mt-form-item>
            <mt-form-item prop="fixPriceType" :label="$t('定价类型')" label-style="top">
              <vxe-select
                v-model="dataForm.fixPriceType"
                :options="fixPriceTypeList"
                :option-props="{ label: 'text', value: 'value' }"
                :disabled="!editable || itemLength !== 0"
                :placeholder="$t('请选择定价类型')"
              />
            </mt-form-item>
            <mt-form-item prop="priceClassify" :label="$t('价格类型')" label-style="top">
              <vxe-select
                v-model="dataForm.priceClassify"
                :options="priceClassifyList"
                :option-props="{ label: 'text', value: 'value' }"
                :disabled="!editable || itemLength !== 0"
                :placeholder="$t('请选择价格类型')"
              />
            </mt-form-item>
            <mt-form-item prop="docTypeCode" :label="$t('单据类型')" label-style="top">
              <vxe-select
                v-model="dataForm.docTypeCode"
                :options="docTypeCodeList"
                :option-props="{
                  label: 'text',
                  value: 'value'
                }"
                :disabled="!editable || itemLength !== 0"
                :placeholder="$t('请选择单据类型')"
                @change="(e) => handleValueChange('docTypeCode', e)"
              />
            </mt-form-item>
            <mt-form-item prop="stepQuote" :label="$t('是否阶梯报价')" label-style="top">
              <vxe-select
                v-model="dataForm.stepQuote"
                :options="stepQuoteList"
                :option-props="{ label: 'text', value: 'value' }"
                :disabled="!editable || itemLength !== 0"
                :placeholder="$t('请选择是否阶梯报价')"
              />
            </mt-form-item>
            <mt-form-item prop="purchaserName" :label="$t('创建人')" label-style="top">
              <vxe-pulldown ref="pulldownRef" destroy-on-close>
                <template #default>
                  <vxe-input
                    v-model="dataForm.purchaserName"
                    suffix-icon="vxe-icon-caret-down"
                    readonly
                    :disabled="!editable"
                    :placeholder="$t('请选择定创建人')"
                    @click="handlePulldown"
                  />
                </template>
                <template #dropdown>
                  <vxe-input
                    ref="purchaserSearcInputRef"
                    v-model="purchaserSearcValue"
                    :prefix-icon="'vxe-icon-search'"
                    clearable
                    :placeholder="$t('搜索')"
                    @input="handlePulldownSearchInput"
                  />
                  <vxe-list class="my-dropdown2" :data="purchaserList" auto-resize height="auto">
                    <template #default="{ items }">
                      <div v-if="items.length">
                        <div
                          class="list-item2"
                          v-for="item in items"
                          :key="item.employeeId"
                          @click="handlePulldownItemSelected(item)"
                        >
                          <span :class="{ isSelected: item.employeeId === dataForm.purchaserId }">{{
                            item.text
                          }}</span>
                        </div>
                      </div>
                      <div v-else class="empty-tip">
                        {{ $t('暂无数据') }}
                      </div>
                    </template>
                  </vxe-list>
                </template>
              </vxe-pulldown>
            </mt-form-item>
            <mt-form-item prop="createTime" :label="$t('创建日期')" label-style="top">
              <vxe-input
                v-model="dataForm.createTime"
                clearable
                :editable="false"
                :disabled="true"
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('单据状态')" label-style="top">
              <vxe-select
                v-model="dataForm.status"
                :options="statusList"
                :option-props="{ label: 'text', value: 'value' }"
                disabled
              />
            </mt-form-item>
            <mt-form-item prop="remark" :label="$t('延期原因')" label-style="top">
              <vxe-textarea
                v-model="dataForm.remark"
                clearable
                :disabled="[1, 2].includes(dataForm.status)"
                :rows="1"
                :placeholder="$t('请输入延期原因')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </div>
      <div class="top-toggle-btn">
        <div @click="handleToggle"><i class="vxe-icon-arrow-up" /></div>
      </div>
    </div>
    <div class="body-container" v-show="$route.query.type === 'edit'">
      <mt-tabs
        ref="mtTabsRef"
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabList"
        :halt-select="false"
        @handleSelectTab="(index, item) => handleTabChange(index)"
        style="background-color: #fff"
      />
      <keep-alive :include="keepArr">
        <component
          ref="mainContent"
          style="height: calc(100% - 50px)"
          :is="activeComponent"
          :data-info="dataForm"
          :detail-info="dataForm"
          @updateDetail="init"
          @itemDataChange="itemDataChange"
        />
      </keep-alive>
    </div>
  </div>
</template>

<script>
import {
  Input as VxeInput,
  Button as VxeButton,
  Select as VxeSelect,
  Textarea as VxeTextarea,
  Pulldown as VxePulldown
} from 'vxe-table'
import mixin from './config/mixin'
import {
  priceClassifyList,
  docTypeCodeList,
  fixPriceTypeList,
  statusList,
  sourceList,
  stepQuoteList
} from './config/index'
import debounce from 'lodash.debounce'

export default {
  name: 'ExpertRating',
  components: {
    VxeInput,
    VxeButton,
    VxeSelect,
    VxeTextarea,
    VxePulldown
  },
  mixins: [mixin],
  data() {
    return {
      priceClassifyList,
      fixPriceTypeList, //定价类型
      statusList,
      docTypeCodeList,
      sourceList,
      stepQuoteList,
      dataForm: {},
      activeTabIndex: 0,
      isExpand: true,
      dataList: [[], []],
      itemLength: 0,
      type: 'detail',
      isInit: true,
      purchaserSearcValue: null,
      keepArr: ['CostFactorTab', 'MaterialDetailTab', 'QuotaDetailTab', 'AttachmentTab']
    }
  },
  computed: {
    extendFactoryTitle() {
      let _list = []
      this.dataForm?.extendFactoryList?.forEach((item) => {
        const selectedItem = this.sourcingExpandList.find((t) => t.value === item)
        _list.push(selectedItem?.text)
      })
      return _list.length ? _list.join(',') : ''
    },
    editable() {
      return this.$route.query.type === 'create' || [0, 5, 9].includes(this.dataForm.status)
    },
    tabList() {
      const tabs = [
        { title: this.$t('定价明细'), compName: 'ItemTab' },
        { title: this.$t('附件'), compName: 'AttachmentTab' }
      ]
      return tabs
    },
    activeComponent() {
      let comp = ''
      switch (this.activeTabIndex) {
        case 0:
          // 物料明细
          comp = () => import('./components/itemTab.vue')
          break
        case 1:
          // 附件
          comp = () => import('./components/attachmentTab.vue')
          break
        default:
          return
      }
      return comp
    },
    formRules() {
      return {
        rfxCode: [
          {
            required: this.$route.query.type === 'edit',
            message: this.$t('请输入定价单名称'),
            trigger: 'blur'
          }
        ],
        rfxName: [{ required: true, message: this.$t('请输入定价单名称'), trigger: 'blur' }],
        companyCode: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        purchaseOrgCode: [{ required: true, message: this.$t('请选择采购组织'), trigger: 'blur' }],
        factoryCode: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }],
        fixPriceObject: [{ required: true, message: this.$t('请选择定价对象'), trigger: 'blur' }],
        priceClassify: [{ required: true, message: this.$t('请选择价格类型'), trigger: 'blur' }],
        docTypeCode: [{ required: true, message: this.$t('请选择单据类型'), trigger: 'blur' }],
        stepQuote: [{ required: true, message: this.$t('请选择是否阶梯'), trigger: 'blur' }],
        remark: [{ required: true, message: this.$t('请输入延期原因'), trigger: 'blur' }]
      }
    },
    detailToolbar() {
      return [
        {
          code: 'back',
          name: this.$t('返回')
        },
        {
          code: 'viewOA',
          name: this.$t('OA审批进度'),
          status: '',
          isHidden: !this.dataForm?.oaApproveLink
        },
        {
          code: 'save',
          name: this.$t('保存'),
          isHidden: this.$route.query.type === 'edit' && [1, 2].includes(this.dataForm.status),
          status: 'primary'
        },
        {
          code: 'submit',
          name: this.$t('提交'),
          isHidden:
            this.$route.query.type === 'create' ||
            (this.$route.query.type === 'edit' && [1, 2].includes(this.dataForm.status)),
          status: 'primary'
        }
      ]
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    // 初始化
    init() {
      if (this.$route.query.type === 'edit') {
        this.getHeaderInfo()
        this.isExpand = false
      } else {
        this.getPurchaserList()
        // 获取定价对象
        this.getFixPriceObjectTypeList()
        // 设置默认值
        this.dataForm = {
          companyCode: null,
          fixPriceType: '2', // 默认已有
          priceClassify: '4', // 默认执行价
          docTypeCode: '0', //默认价格延期
          docTypeName: '价格延期', //默认价格延期
          stepQuote: 0, //是否阶梯 否
          status: 0 //单据状态 暂存|草菇
        }
      }
    },

    // 监听定价明细数据
    itemDataChange(v) {
      this.itemLength = v
    },
    // 获取头部基础信息
    async getHeaderInfo() {
      const res = await this.$API.postponePricing.queryHeader({
        id: this.$route.query.id
      })
      if (res.code === 200) {
        const extendFactoryList = []
        res.data?.extendFactoryList?.forEach((t) => {
          extendFactoryList.push(t.companyCode + '+' + t.purchaseOrgCode + '+' + t.factoryCode)
        })
        this.dataForm = { ...res.data, extendFactoryList }
        this.getCompanyList(true)
        this.getSourcingExpandList(res.data.factoryCode, res.data.purchaseOrgCode)
        this.getPurchaserList(res.data?.purchaserName)
        this.getFixPriceObjectTypeList()
      }
    },

    // 下拉列表选中值修改
    handleValueChange(prefix, e) {
      const { value } = e
      switch (prefix) {
        // 选择公司
        case 'company':
          this.$set(this.dataForm, 'purchaseOrgCode', null)
          this.$set(this.dataForm, 'factoryCode', null)
          this.$set(this.dataForm, 'extendFactoryList', [])

          if (value) {
            const selectedItem = this.companyList.find((item) => item.orgCode === value)
            this.dataForm.companyId = selectedItem ? selectedItem.id : null
            this.dataForm.companyName = selectedItem ? selectedItem.orgName : null

            this.getPurchaseOrgList(selectedItem?.id)
          } else {
            this.dataForm.companyId = null
            this.dataForm.companyName = null
          }
          break
        // 选择采购组织
        case 'purchaseOrg':
          this.$set(this.dataForm, 'factoryCode', null)
          this.$set(this.dataForm, 'extendFactoryList', [])
          if (value) {
            const selectedItem = this.purchaseOrgList.find(
              (item) => item.organizationCode === value
            )
            this.dataForm.purchaseOrgId = selectedItem ? selectedItem.id : null
            this.dataForm.purchaseOrgName = selectedItem ? selectedItem.organizationName : null

            const { companyId, purchaseOrgId } = this.dataForm
            this.getFactoryListByCompanyAndPur(companyId, purchaseOrgId)
          } else {
            this.dataForm.purchaseOrgId = null
            this.dataForm.purchaseOrgName = null

            this.dataForm.factoryCode = null
            this.dataForm.factoryName = null
          }
          break
        // 选择工厂
        case 'factory':
          this.$set(this.dataForm, 'extendFactoryList', [])
          if (value) {
            const selectedItem = this.factoryList.find((item) => item.orgCode === value)
            this.dataForm.factoryName = selectedItem ? selectedItem.orgName : null
          } else {
            this.dataForm.factoryName = null
          }
          this.getSourcingExpandList(value, this.dataForm.purchaseOrgCode)
          break
        // 选择定价对象
        case 'fixPriceObject':
          {
            let selectedItem = this.fixPriceObjectTypeList.find(
              (item) => item.sourcingObj === value
            )
            this.dataForm.fixPriceObjectType =
              selectedItem && value ? selectedItem.sourcingObjType : null
          }
          break
        // 选择单据类型
        case 'docTypeCode':
          {
            let selectedItem = this.docTypeCodeList.find((item) => item.value === value)
            this.dataForm.docTypeName = selectedItem && value ? selectedItem.text : null
          }
          break
        default:
          break
      }
    },
    // 远程搜索查询-展开面板
    handlePulldown() {
      this.$refs.pulldownRef?.showPanel()
      this.$nextTick(() => {
        this.$refs.purchaserSearcInputRef?.focus()
      })
    },
    // 远程搜索查询-查询
    handlePulldownSearchInput: debounce(function (e) {
      this.getPurchaserList(e?.value)
    }, 500),
    // 远程搜索查询-选中
    handlePulldownItemSelected(item) {
      if (this.$refs.pulldownRef) {
        this.$set(this.dataForm, 'purchaserId', item.employeeId)
        this.$set(this.dataForm, 'purchaserName', item.employeeName)
        this.$refs.pulldownRef.hidePanel()
        this.purchaserSearcValue = null
      }
    },
    // 点击工具栏按钮
    handleClickToolBar(e) {
      switch (e.code) {
        case 'back':
          this.handleBack()
          break
        case 'save':
          this.handleSave()
          break
        case 'submit':
          this.handleSubmit(e.code)
          break
        case 'viewOA':
          window.open(this.dataForm?.oaApproveLink)
          break
        default:
          break
      }
    },
    // 切换tab页签
    handleTabChange(index) {
      this.$refs.mtTabsRef.activeTab = index
      this.activeTabIndex = index
    },
    // 返回
    handleBack() {
      this.$router.go(-1)
    },
    // 处理提交数据
    getSubmitData() {
      let params = {}
      this.$refs.dataFormRef.validate(async (valid) => {
        if (!valid) {
          //展开头部表单数据，显示报错信息
          this.isExpand = true
          return
        }
        // 定价明细
        let itemList = this.$refs.mainContent?.tableData

        // 工厂拓展列表
        let extendFactoryList = []
        this.dataForm.extendFactoryList?.forEach((item) => {
          const selectedItem = this.sourcingExpandList.find((t) => t.value === item)
          extendFactoryList.push(selectedItem?.data)
        })
        params = {
          ...this.dataForm,
          extendFactoryList,
          sourceCode: 0,
          sourceName: '手动创建',
          itemList
        }
      })
      return params
    },
    // 保存
    async handleSave() {
      let params = this.getSubmitData()
      if (Object.entries(params).length === 0) return
      this.$store.commit('startLoading')
      const res = await this.$API.postponePricing.savePostpone(params)
      this.$store.commit('endLoading')
      if (res.code === 200) {
        this.$toast({ content: this.$t('操作成功！'), type: 'success' })
        if (this.$route.query.type === 'create') {
          this.$router.replace({
            name: 'postpone-pricing-detail',
            query: {
              type: 'edit',
              id: res.data
            }
          })
        } else {
          this.getHeaderInfo()
          if (this.activeComponent?.name !== 'AttachmentTab') {
            this.$refs.mainContent?.initTableData()
          }
        }
      }
    },

    // 提交
    async handleSubmit(type) {
      let params = this.getSubmitData(type)
      // 物料校验
      if (!params?.itemList?.length) {
        this.$toast({ content: this.$t('无物料，不允许提交！'), type: 'warning' })
        return
      }

      // 工厂拓展为空
      if (!params?.extendFactoryList?.length) {
        await this.showDialog('工厂拓展为空，请确认！')
      } else {
        await this.showDialog('确定提交？')
      }
      this.$store.commit('startLoading')
      const res = await this.$API.postponePricing.submitPostpone(params)
      this.$store.commit('endLoading')
      if (res.code === 200) {
        this.$toast({ content: this.$t('操作成功！'), type: 'success' })
        this.getHeaderInfo()
        if (this.activeComponent?.name !== 'AttachmentTab') {
          this.$refs.mainContent?.initTableData()
        }
      }
    },
    // 弹框展示
    async showDialog(message, cssClass) {
      return new Promise((resolve) => {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t(message),
            cssClass
          },
          success: resolve
        })
      })
    },
    // 展开/收缩
    handleToggle() {
      this.isExpand = !this.isExpand
    }
  }
}
</script>

<style scoped lang="scss">
.postpone-full-height {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 8px 8px 0;
  background: #fff;
}
.body-container {
  height: calc(100% - 80px);
}
.top-container {
  flex: 1;
  width: 100%;
  .top-content {
    padding: 8px 8px 0 8px;
    background-color: #f9f9f9;
    border-radius: 8px;
    .top-info {
      display: flex;
      justify-content: space-between;
      .title {
        font-size: 20px;
        font-weight: 700;
        color: #292929;
        span {
          margin-right: 15px;
        }
        .sub-title {
          font-size: 15px;
          color: #777;
        }
      }
      .top-btns {
        text-align: right;
        margin-bottom: 10px;
      }
    }
    .top-form {
      .mt-form {
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
        justify-content: space-between;
        grid-gap: 5px;
      }
      .vxe-input,
      .vxe-select,
      .vxe-pulldown {
        width: 100%;
        height: 34px;
        line-height: 34px;
      }
      /deep/.vxe-pulldown--panel {
        min-width: unset !important;
        width: 100%;
      }
    }
  }
  .top-toggle-btn {
    width: 100%;
    div {
      width: 80px;
      height: 15px;
      line-height: 15px;
      margin: auto;
      text-align: center;
      background-color: #4a556b;
      color: #fff;
      font-weight: bold;
      border-radius: 0 0 6px 6px;
    }
  }
}

.top-hidden {
  .top-form {
    display: none;
  }
  .top-toggle-btn {
    div {
      transform: rotate(180deg);
      border-radius: 6px 6px 0 0;
    }
  }
}

.my-dropdown2 {
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 4px 0;
  max-height: 200px;
  border-radius: 4px;
  border: 1px solid #dadce0;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}
.list-item2 {
  line-height: 30px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 0 4px;

  .isSelected {
    font-weight: 700;
    color: #409eff;
  }
}
.list-item2:hover {
  background-color: #f5f7fa;
}
.empty-tip {
  width: 100%;
  line-height: 30px;
  padding: 0 4px;
  text-align: center;
  color: #c0c4cc;
}

::v-deep {
  .mt-form-item {
    margin-bottom: 15px;
    .label {
      margin: 0 10px 5px 0;
    }
  }
  .mt-tabs-container {
    background-color: #fff;
  }

  .mt-tabs-container ul.tab-container li.tab-item2.active {
    border-bottom: 2px solid #409eff;
  }
  .mt-tabs-container ul.tab-container li.tab-item2.active,
  .mt-tabs-container ul.tab-container li.tab-item2:hover {
    color: #31374e;
  }

  .vxe-textarea--inner {
    min-height: 34px;
    height: 100%;
  }
}
</style>
