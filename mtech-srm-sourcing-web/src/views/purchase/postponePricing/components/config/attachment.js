import { i18n } from '@/main.js'
import Vue from 'vue'

export const defaultToolbar = [
  { id: 'download', icon: 'icon_solid_Download', title: i18n.t('下载') }
]

export const toolObj = {
  download: { id: 'download', icon: 'icon_solid_Download', title: i18n.t('下载') },
  upload: { id: 'upload', icon: 'icon_solid_Upload', title: i18n.t('上传') },
  delete: { id: 'delete', icon: 'icon_solid_Delete', title: i18n.t('删除') }
}

const iconSetting = {
  '.ppt': 'mt-icon-icon_ppt',
  '.docx': 'mt-icon-icon_word',
  '.pdf': 'mt-icon-icon_pdf',
  '.xls': 'mt-icon-icon_excel',
  '.xlsx': 'mt-icon-icon_excel',
  '.png': 'mt-icon-icon_File1'
}

export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'fileName',
    cssClass: 'field-content',
    headerText: i18n.t('文件名称'),
    cellTools: [
      { id: 'download', icon: 'icon_solid_Download', title: i18n.t('下载') },
      { id: 'delete', icon: 'icon_solid_Delete', title: i18n.t('删除') }
    ]
  },
  {
    field: 'fileSize',
    headerText: i18n.t('文件大小'),
    template: function () {
      return {
        template: Vue.component('fileSize', {
          template: `<span>{{getFormFileText}}</span>`,
          data() {
            return { data: {} }
          },
          computed: {
            getFormFileText(e) {
              return `${Number(((e.data.fileSize / 1024) * 100) / 100).toFixed(2)}KB`
            }
          }
        })
      }
    }
  },
  {
    field: 'fileType',
    headerText: i18n.t('文件类型'),
    template: function () {
      return {
        template: Vue.component('fileTypeOption', {
          template: `<div><i :class="['mt-icons', icon]"></i><span style="margin-left: 5px">{{data.fileType}}</span></div>`,
          data() {
            return { data: { data: {} } }
          },
          computed: {
            icon() {
              const { fileType } = this.data
              return fileType ? iconSetting[fileType] : ''
            }
          }
        })
      }
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  }
]

export const pageConfig = [
  {
    useToolTemplate: false,
    toolbar: defaultToolbar,
    grid: {
      allowFiltering: true,
      allowPaging: false,
      columnData,
      dataSource: []
    }
  }
]

export const treeViewData = {
  //相关文件-目录结构-原数据
  nodeTemplate: function () {
    return {
      template: Vue.component('common', {
        template: `<div class="action-boxs">
                      <div>{{getNodeName}}</div>
                    </div>`,
        data() {
          return { data: {} }
        },
        computed: {
          getNodeName() {
            return i18n.t(this?.mtData?.nodeName)
          }
        },
        props: {
          mtData: {
            type: Object,
            default: () => {}
          }
        }
      })
    }
  },
  dataSource: [],
  id: 'id',
  text: 'nodeName',
  child: 'fileNodeResponseList'
}
