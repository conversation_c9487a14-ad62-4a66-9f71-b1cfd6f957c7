<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-local-template-page ref="templateRef" :template-config="pageConfig">
        <template v-slot:quick-search-form>
          <div class="custom-form-box">
            <mt-form ref="searchFormRef" :model="searchFormModel">
              <mt-form-item prop="materialCode" :label="$t('物料编码')">
                <mt-input v-model="searchFormModel.materialCode" show-clear-button></mt-input>
              </mt-form-item>

              <mt-form-item prop="supplierCode" :label="$t('供应商编码')">
                <mt-input v-model="searchFormModel.supplierCode" show-clear-button></mt-input>
              </mt-form-item>
              <mt-form-item prop="validStartTime" :label="$t('价格有效期从')">
                <mt-date-picker
                  v-model="searchFormModel.validStartTime"
                  :open-on-focus="true"
                  time-stamp
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  show-clear-button
                  :allow-edit="false"
                />
              </mt-form-item>
              <mt-form-item prop="validEndTime" :label="$t('价格有效期至')">
                <mt-date-picker
                  v-model="searchFormModel.validEndTime"
                  :open-on-focus="true"
                  time-stamp
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  show-clear-button
                  :allow-edit="false"
                />
              </mt-form-item>
              <mt-form-item prop="sourceCode" :label="$t('来源单号')">
                <mt-input v-model="searchFormModel.sourceCode"></mt-input>
              </mt-form-item>
            </mt-form>
          </div>
        </template>
      </mt-local-template-page>
    </div>
  </mt-dialog>
</template>

<script>
import MtLocalTemplatePage from '@/components/template-page'
import { columnData } from './config/priceRecord'
import { v4 as uuidv4 } from 'uuid'
export default {
  components: {
    MtLocalTemplatePage
  },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      pageConfig: [],
      searchFormModel: {},
      data: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.modalData.isShow ? [] : this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.initData()
  },
  methods: {
    // 时间选择
    handleValidTime() {},
    initData() {
      this.pageConfig = [
        {
          useToolTemplate: false,
          toolbar: [],
          gridId: 'b9674431-2077-4ddb-ba61-7f365fcce0e9',
          showSelected: true,
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          grid: {
            allowFiltering: true,
            columnData: columnData,
            allowSorting: false,
            allowSelection: true,
            selectionSettings: {
              type: 'Multiple',
              mode: 'Row'
            },
            dataSource: [],
            asyncConfig: {
              url: '/sourcing/tenant/price/postpone/queryPriceRecordPage',
              params: {
                rfxId: this.modalData.rfxId,
                queryCode: 'batchCodeQuery',
                priceTypeList: this.modalData.stepQuote ? [10, 11] : [0, 7, 8, 9, 12, 13]
              },
              serializeList: (list) => {
                return this.modalData.stepQuote ? this.groupData(list) : list
              }
            },
            queryCellInfo: this.queryCellInfo
          }
        }
      ]
    },
    confirm() {
      let _records = this.$refs.templateRef.getCurrentTabRef().gridRef.getMtechGridRecords()
      let _tabList = this.$refs.templateRef.getCurrentUsefulRef().ejsRef.getCurrentViewRecords()
      if (_records.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _groupCodes = _records.map((item) => this.generateGroupKey(item))
      _groupCodes = Array.from(new Set(_groupCodes))
      let list = this.modalData.stepQuote
        ? _tabList.filter((item) => _groupCodes.includes(this.generateGroupKey(item)))
        : _records
      // 数据校验
      let flag = this.validData(list)
      if (!flag) {
        this.$toast({ content: this.$t('存在重复的明细行'), type: 'warning' })
        return
      }
      this.$emit('confirm-function', this.covertData(list))
    },
    //数据校验
    validData(list) {
      let flag = true
      if (!this.modalData.stepQuote) return true //非阶梯不作校验

      let itemGroupIdList = []
      this.modalData?.tableData?.forEach((item) => {
        itemGroupIdList.push(item.itemGroupId)
      })
      itemGroupIdList = Array.from(new Set(itemGroupIdList))
      list.forEach((item) => {
        if (itemGroupIdList.includes(item.itemGroupId)) {
          flag = false
        }
      })
      return flag
    },
    generateGroupKey(row) {
      let id = row.supplierId || '0'
      let code = row.supplierCode || 'a'
      return row.itemGroupId + '__' + id + '__' + code
    },
    cancel() {
      this.$emit('cancel-function')
    },
    // 对数据进行分组
    groupData(list) {
      const kindList = []
      const tempList = []
      const resList = []
      list.forEach((item) => {
        const groupByKey = this.generateGroupKey(item)
        if (!kindList.includes(groupByKey)) {
          kindList.push(groupByKey)
          tempList.push([])
        }
        const i = kindList.indexOf(groupByKey)
        tempList[i].push(item)
      })
      tempList.forEach((item) => {
        item[0].rowSpan = item.length
        resList.push(...item)
      })
      return resList
    },
    // 合并单元格
    queryCellInfo(args) {
      // 不合并的单元格
      const arr = ['stepValue', 'unitPriceTaxed', 'unitPriceUnTaxed']
      if (args.data.rowSpan && !arr.includes(args.column.field)) {
        args.rowSpan = args.data.rowSpan
      }
      args.column.field !== 'companyName' && (args.cell.style.borderLeft = '1px solid #e0e0e0')
    },
    // 数据转换、提供给定价明细表使用
    covertData(data) {
      if (!data || data?.length < 1) return []
      let list = []
      data.forEach((item) => {
        item.customId = uuidv4()
        delete item.id
        list.push({ ...item })
      })
      return list
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
