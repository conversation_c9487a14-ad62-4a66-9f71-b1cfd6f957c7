<template>
  <div>
    <vxe-button status="primary" @click="printTableData">{{ $t('获取表格数据') }}</vxe-button>
    <sc-table
      show-overflow
      ref="xTable"
      height="500"
      :merge-cells="mergeCells"
      :data="tableData"
      :columns="columns"
      :edit-rules="validRules"
      :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true }"
      :tree-config="null"
      :row-config="{ isCurrent: false }"
      :scroll-y="{ gt: 50 }"
      @edit-closed="handleEditClose"
      keep-source
    />
  </div>
</template>
<script>
import ScTable from '@/components/ScTable/src'

export default {
  components: {
    ScTable
  },
  data() {
    return {
      tableData: [],
      columns: [
        { type: 'seq', width: 60 },
        { field: 'role', title: this.$t('角色'), editRender: { name: 'input' } },
        { field: 'name', title: this.$t('名字') },
        {
          field: 'nickname',
          title: this.$t('地址'),
          editRender: { name: 'input' }
        }
      ],
      validRules: {
        role: [{ required: true, message: this.$t('角色值必须填写') }],
        name: [{ required: true, message: this.$t('名称必须填写') }],
        address: [{ required: true, message: this.$t('地址必须填写') }]
      },
      mergeCells: []
    }
  },
  mounted() {
    for (let i = 0; i < 100; i++) {
      this.tableData.push({ id: 'id_' + i, name: 'Test' + i, nickname: 'T' + i, role: 'Designer' })
      if (i % 4 === 0) {
        this.mergeCells.push({
          row: i,
          col: 1,
          rowspan: 4,
          colspan: 1
        })
      }
    }
  },
  methods: {
    printTableData() {
      console.log(this.tableData)
    },
    handleEditClose({ row, column }) {
      this.tableData[1]['role'] = ''
      if (column.property === 'role') {
        let lastValue = column.model.value
        let newValue = row[column.property]

        console.log('last: ', lastValue, ', new: ', newValue)

        this.tableData.forEach((item) => {
          if (item[column.property] === lastValue) {
            item[column.property] = newValue
          }
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped></style>
