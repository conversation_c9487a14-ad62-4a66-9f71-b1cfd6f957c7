import { i18n } from '@/main.js'
import Vue from 'vue'
import { fileData } from '../mock'
import editCpnt from '../editCpnt.vue'
import eInput from '../components/exampleInput.vue'

const iconSetting = {
  PPT: 'mt-icon-icon_ppt',
  WORD: 'mt-icon-icon_word',
  PDF: 'mt-icon-icon_pdf',
  EXCEL: 'mt-icon-icon_excel'
}
const belongDataSource = [
  { text: '归属1', value: '111' },
  { text: '归属2', value: '222' },
  { text: '归属3', value: '333' }
]

export const tempColumns = function (editInstance) {
  editInstance.component('custom-editor', editCpnt)
  editInstance.component('custom-input', eInput)
  return [
    {
      field: 'id',
      headerText: 'ID',
      width: 100,
      isPrimaryKey: true,
      visible: false
    },
    {
      field: 'belongTo',
      headerText: i18n.t('editInstance内置下拉'),
      width: '150',
      valueAccessor: (field, data) => {
        return belongDataSource.find((item) => item.value === data[field])?.text
      },
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'select',
          'show-clear-button': true,
          fields: { value: 'value', text: 'text' },
          dataSource: belongDataSource
        })
      })
    },
    {
      field: 'customName',
      headerText: i18n.t('editInstance自定义组件'),
      width: '150',
      valueAccessor: (field, data) => {
        return data[field]
      },
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'custom-editor',
          'show-clear-button': true
        })
      })
    },
    {
      field: 'fileType',
      width: '120',
      headerText: i18n.t('自定展示模板'),
      frozenRight: true,
      template: function () {
        return {
          template: Vue.component('fileTypeOption', {
            template: `<div><i :class="['mt-icons', icon]"></i><span style="margin-left: 5px">{{data.fileType}}</span></div>`,
            data() {
              return { data: { data: {} } }
            },
            computed: {
              icon() {
                const { fileType } = this.data
                return fileType ? iconSetting[fileType] : ''
              }
            }
          })
        }
      }
    },
    {
      field: 'fileName',
      width: '150',
      headerText: i18n.t('文件名称')
    },
    {
      field: 'version',
      width: '150',
      headerText: i18n.t('日期')
    }
  ]
}
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'CreateNew', icon: 'icon_solid_Cancel', title: i18n.t('创建新版本') },
  { id: 'ExportFile', icon: 'icon_solid_Createorder', title: i18n.t('导出') }
]
export const pageConfig = (editInstance) => [
  {
    toolbar,
    useToolTemplate: false,
    gridId: 'f8433fad-e74b-4271-9fe3-331a886fe2bd',
    grid: {
      columnData: tempColumns(editInstance),
      dataSource: fileData,
      // frozenColumns: 1,
      // asyncConfig: { url },
      editSettings: {
        allowEditing: true,
        allowAdding: true,
        allowDeleting: true,
        newRowPosition: 'Top',
        mode: 'Batch'
      },
      queryCellInfo: (args) => {
        if (args.column.field === 'fileType' && args.data.id === '111111') {
          args.rowSpan = 2
        }
      }
    }
  }
]
