export const fileData = [
  {
    id: '111111',
    belongTo: '222',
    fileType: 'PPT',
    fileName: '111',
    customName: 'name1',
    version: '1997-11-26',
    renderText: '333',
    opList: [
      { text: '归属1', value: '111' },
      { text: '归属2', value: '222' },
      { text: '归属3', value: '333' }
    ]
  },
  {
    id: '222222',
    belongTo: '111',
    fileType: 'WORD',
    fileName: '222',
    customName: 'name2',
    version: '1997-11-26',
    renderText: '333'
  },
  {
    id: getRandom(),
    belongTo: '222',
    fileType: 'PDF',
    fileName: '333',
    customName: 'name3',
    version: '1997-11-26',
    renderText: '333'
  },
  {
    id: getRandom(),
    belongTo: '333',
    fileType: 'EXCEL',
    fileName: '111',
    customName: 'name4',
    version: '1997-11-26',
    renderText: '333'
  }
]

function getRandom() {
  return Math.floor(Math.random() * 1000000)
}
