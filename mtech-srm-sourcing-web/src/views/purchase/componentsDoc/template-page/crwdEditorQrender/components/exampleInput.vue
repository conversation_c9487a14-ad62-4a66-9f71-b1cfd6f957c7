<template>
  <div class="container">
    <mt-input
      type="text"
      :readonly="true"
      v-model="initValue"
      :open-dispatch-change="false"
      @change="change"
      :placeholder="$t('自定义输入框')"
    />
  </div>
</template>
<script>
export default {
  name: 'InputEdit',
  props: {
    value: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      initValue: null,
      initFlag: true
    }
  },
  watch: {
    value: {
      handler(val) {
        this.initValue = val
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.initFlag = true
  },
  methods: {
    change(e) {
      if (!this.initFlag) {
        this.$emit('change', e)
        this.$emit('input', this.initValue)
      }
      this.initFlag = false
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: 32px;
  display: flex;
  align-items: center;
}
</style>
