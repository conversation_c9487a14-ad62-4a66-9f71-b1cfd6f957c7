<template>
  <mt-dialog
    css-class="coustom-editor-pop"
    ref="dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-input type="text" v-model="dialogVal" />
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      dialogVal: null
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.dialogVal = this.modalData.value
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    confirm() {
      this.$emit('confirm-function', this.dialogVal)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  // padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
/deep/ .mt-data-grid {
  .e-grid {
    th.e-headercell,
    td.e-rowcell {
      font-size: 8px !important;
      height: 30px;
    }
  }
}
/deep/ .e-headertext {
  font-size: 8px !important;
}
/deep/ .e-headercelldiv {
  height: 12px !important;
  line-height: 12px !important;
  padding: 0 !important;
}
</style>
