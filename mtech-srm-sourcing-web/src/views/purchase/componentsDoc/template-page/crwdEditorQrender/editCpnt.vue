<template>
  <div class="container">
    <mt-input
      type="text"
      :readonly="true"
      v-model="initValue"
      :open-dispatch-change="false"
      @change="change"
      :placeholder="$t('自定义输入框')"
    />
    <i class="mt-icons mt-icon-icon_card_plus" @click="showDialog" />
  </div>
</template>
<script>
export default {
  name: 'DeepEdit',
  props: {
    value: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      initValue: null,
      initFlag: true
    }
  },
  watch: {
    value: {
      handler(val) {
        this.initValue = val
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.initFlag = true
  },
  methods: {
    change(e) {
      if (!this.initFlag) {
        this.$emit('change', e)
      }
      this.initFlag = false
    },
    showDialog() {
      this.$dialog({
        modal: () => import('./components/dialogChange.vue'),
        data: {
          title: this.$t('弹窗编辑测试'),
          value: this.initValue
        },
        success: (data) => {
          this.initValue = data
          this.$emit('input', this.initValue)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: 32px;
  display: flex;
  align-items: center;
}
</style>
