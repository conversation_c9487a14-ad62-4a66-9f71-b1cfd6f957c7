<template>
  <!-- 预设配额 :is-custom-search-handle="true"-->
  <div class="full-height">
    <div>
      <b>起用版本：1.8.22</b>
      <br />
    </div>
    <mt-template-page
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleCustomSearch="handleCustomSearch"
      @cellSave="cellSave"
      @cellEdit="cellEdit"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { pageConfig } from './config'
import { download, getHeadersFileName } from '@/utils/utils'
import { createEditInstance } from '@/utils/ej/dataGrid/index'
import { setCurrentCell, getCancelEditState } from '@/utils/ej/dataGrid/endCellEdit.js'

const editInstance = createEditInstance()

export default {
  data() {
    return {
      searchFormModel: {
        supplierList: ['100007', '100043'] // 当为空时不要写null 需要写成空数组，不然在全选时会报错
        // supplierList: []
      },
      pageConfig: [],
      searchRules: {
        supplierList: [{ required: true, message: this.$t('必填') }]
      },
      opList: [
        { text: '归属1', value: '111' },
        { text: '归属2', value: '222' },
        { text: '归属3', value: '333' },
        { text: '归属4', value: '444' },
        { text: '归属5', value: '555' },
        { text: '归属6', value: '666' }
      ],
      supplierSearchFields: ['supplierCode', 'supplierName']
    }
  },
  computed: {
    gridCpnt() {
      const cpnt = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.ej2Instances
      // cpnt.updateCell = function (info) {
      //   const { rowIndex, colIndex, field, value, rowObj } = info
      //   const gObj = this
      //   const col = gObj.getColumnByField(field)
      //   // var index = gObj.getColumnIndexByField(field)
      //   if (col && !col.isPrimaryKey && col.allowEditing) {
      //     const td = cpnt.getCellFromIndex(rowIndex, colIndex)
      //     // var td = getCellByColAndRowIndex(this.parent, col, rowIndex, colIndex)
      //     // var rowObj =
      //     //   col.getFreezeTableName() === 'movable'
      //     //     ? this.parent.getMovableRowsObject()[rowIndex]
      //     //     : col.getFreezeTableName() === literals.frozenRight
      //     //     ? gObj.getFrozenRightRowsObject()[rowIndex]
      //     //     : gObj.getRowObjectFromUID(td.parentElement.getAttribute('data-uid'))
      //     gObj.refreshTD(td, col, rowObj, value)
      //     gObj.trigger(this.queryCellInfo, {
      //       cell: this.newReactTd || td,
      //       column: col,
      //       data: rowObj.changes
      //     })
      //   }
      // }
      return cpnt
    }
  },
  created() {
    this.init()
    this.$bus.$on('refreshQuotaDefualtList', () => {
      this.$refs.templateRef.refreshCurrentGridData()
    })
    // this.$bus.$on('changeProvidedEditorData', (arr) => {
    //   debugger
    //   this.pageConfig[0].grid.dataSource = arr
    // })
    editInstance.onChange((ctx, { field, row }) => {
      if (field === 'belongTo' || field === 'customName') {
        const rowIdx = this.gridCpnt?.getRowInfo(row).rowIndex

        const colIdx = 2
        const targetRow = this.gridCpnt.getDataRows()[rowIdx + 1].childNodes
        const targetTd = targetRow[colIdx]
        debugger
        const mistackIndex = parseInt(targetTd.getAttribute('aria-colindex'))
        const mistackTd = targetRow[mistackIndex]
        const mistackOrgInner = mistackTd.innerHTML

        this.gridCpnt && this.gridCpnt.updateCell(rowIdx, 'fileType', '111')
        this.gridCpnt.setCellValue('222222', 'fileName', '555')
        this.$nextTick(() => {
          targetTd.innerHTML = '<span>555</span>'
          mistackTd.innerHTML = mistackOrgInner
        })
      }
    })
  },
  methods: {
    async init() {
      this.pageConfig = pageConfig(editInstance)
    },
    //新增
    handleAdd() {
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
    },
    //创建新版本
    handleCreate(row) {
      if (row.status != 3) {
        this.$toast({ content: this.$t('审核通过的单据才能创建新版本'), type: 'warning' })
        return
      }
      this.$router.push({
        name: `quota-strategy-default-detail`,
        query: {
          type: 'update',
          id: row.id,
          flashKey: new Date().getTime()
        }
      })
    },
    //导出
    handleExport() {
      // 先要点击“查询”按钮才能取得queryBuilderRules
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules,
        defaultRules: [
          // {
          //   condition: 'and',
          //   field: 'rfx_item.rfxHeaderId',
          //   operator: 'equal',
          //   value: this.rfxId
          // }
        ].concat(queryBuilderRules.rules || [])
      } // 筛选条件
      this.$API.quotaDefault.excelExport(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    //表格头部按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.grid.getSelectedRecords()
      if (_selectGridRecords.length != 1 && e.toolbar.id == 'CreateNew') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'CreateNew') {
        this.handleCreate(_selectGridRecords[0])
      } else if (e.toolbar.id == 'ExportFile') {
        this.handleExport()
      }
    },
    handleClickCellTool(e) {
      const { tool, data } = e
      if (tool.id === 'edit') {
        this.$router.push({
          name: `quota-strategy-default-detail`,
          query: {
            type: 'Edit',
            id: data.id,
            flashKey: new Date().getTime()
          }
        })
      } else if (tool.id === 'delete') {
        this.quotaDefaultDelete([data])
      } else if (tool.id === 'detail') {
        this.$router.push({
          name: `quota-strategy-default-detail`,
          query: {
            type: 'detail',
            id: data.id,
            flashKey: new Date().getTime()
          }
        })
      }
    },
    cellEdit(args) {
      setCurrentCell(args.cell)
    },
    cellSave(args) {
      args.cancel = getCancelEditState()
      // const changedData = this.gridCpnt?.getBatchChanges()
      // console.log('aaaaaaaa', changedData)
      if (!args.cancel) setCurrentCell(null)
    },
    // 删除
    quotaDefaultDelete(rows) {
      // 调对应接口后刷新列表
      let idList = rows.map((v) => {
        return v.id
      })
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.quotaDefault.batchDelete({ idList }).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    handleCustomSearch(model) {
      console.log('1111111111', model)
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-form-box {
}
</style>
