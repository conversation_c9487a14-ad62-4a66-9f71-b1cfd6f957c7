import { i18n } from '@/main.js'
import { cloneDeep } from 'lodash'
import Vue from 'vue'
import { fileData } from '../mock'

const iconSetting = {
  PPT: 'mt-icon-icon_ppt',
  WORD: 'mt-icon-icon_word',
  PDF: 'mt-icon-icon_pdf',
  EXCEL: 'mt-icon-icon_excel'
}
const belongDataSource = [
  { text: '归属1', value: '111' },
  { text: '归属2', value: '222' },
  { text: '归属3', value: '333' }
]
const opList = [
  { text: '归属1', value: '111' },
  { text: '归属2', value: '222' },
  { text: '归属3', value: '333' }
]

export const tempColumns = function () {
  return [
    {
      field: 'belongTo',
      headerText: i18n.t('自定义下拉选择'),
      width: '150',
      valueAccessor: (field, data) => {
        return belongDataSource.find((item) => item.value === data[field])?.text
      },
      providedEditor: true,
      editorParams: {
        type: 'select',
        fields: { text: 'text', value: 'value' },
        dataSource: belongDataSource
      }
    },
    {
      field: 'fileType',
      width: '120',
      headerText: i18n.t('自定义输入框'),
      template: function () {
        return {
          template: Vue.component('fileTypeOption', {
            template: `<div><i :class="['mt-icons', icon]"></i><span style="margin-left: 5px">{{data.fileType}}</span></div>`,
            data() {
              return { data: { data: {} } }
            },
            computed: {
              icon() {
                const { fileType } = this.data
                return fileType ? iconSetting[fileType] : ''
              }
            }
          })
        }
      }
      // providedEditor: true
    },
    {
      field: 'fileName',
      width: '150',
      headerText: i18n.t('自定义render'),
      valueAccessor: (field, data) => {
        return belongDataSource.find((item) => item.value === data[field])?.text
      },
      editorRender(h, scoped) {
        return (
          <div>
            <mt-select
              v-model={scoped.fileName}
              fields={{ text: 'text', value: 'value' }}
              dataSource={belongDataSource}
              onChange={() => {
                console.log('22222222', scoped)
                const obj = { text: '归属4', value: '444' }
                const arr = cloneDeep(scoped.opList)
                arr.push(obj)
                scoped.opList = arr
              }}
            />
          </div>
        )
      }
    },
    {
      field: 'version',
      width: '100',
      headerText: i18n.t('自定义日期选择'),
      providedEditor: true,
      editorParams: {
        type: 'datePicker'
      }
    },
    {
      field: 'identityVal',
      width: 100,
      headerText: '非自定义编辑',
      formatter: ({ field }, data) => {
        return opList.find((item) => item.value === data[field])?.text
      },
      isIdentity: true
    },
    {
      field: 'operator',
      width: '100',
      headerText: '自定义editor联动',
      template: function () {
        return {
          template: Vue.component('operatorOption', {
            template: `<span style="color: #6386C1; cursor: pointer;" @click="downloadFile(data)">{{ $t('下载') }}</span>`,
            data() {
              return { data: { data: {} } }
            },
            methods: {
              downloadFile(e) {
                console.log(i18n.t('下载'), e)
              }
            }
          })
        }
      },
      editorRender(h, scoped) {
        return (
          <div>
            <mt-select
              v-model={scoped.renderText}
              fields={{ text: 'text', value: 'value' }}
              dataSource={scoped.opList}
            />
          </div>
        )
      }
    }
  ]
}
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'CreateNew', icon: 'icon_solid_Cancel', title: i18n.t('创建新版本') },
  { id: 'ExportFile', icon: 'icon_solid_Createorder', title: i18n.t('导出') }
]
export const pageConfig = (url, searchFormModel) => [
  {
    toolbar,
    useToolTemplate: false,
    isUseCustomSearch: true,
    isCustomSearchHandle: true,
    searchFormModel,
    isUseCustomEditor: true,
    // oprator: contains/equl; type: string/number
    customMatchRules: {
      orgId: 'equl'
    },
    gridId: 'f8433fad-e74b-4271-9fe3-331a886fe2bd',
    grid: {
      columnData: tempColumns(),
      dataSource: fileData,
      // asyncConfig: { url },
      editSettings: {
        allowEditing: true,
        allowAdding: true,
        allowDeleting: true,
        newRowPosition: 'Top'
      }
    }
  }
]
