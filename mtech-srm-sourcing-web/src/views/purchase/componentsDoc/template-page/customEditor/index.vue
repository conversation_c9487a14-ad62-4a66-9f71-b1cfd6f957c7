<template>
  <!-- 预设配额 :is-custom-search-handle="true"-->
  <div class="full-height">
    <div>
      <b>起用版本：1.8.22</b>
      <br />
    </div>
    <mt-template-page
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleCustomSearch="handleCustomSearch"
    >
      <template v-slot:quick-search-form="{ searchFormModel }">
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchRules">
            <mt-form-item prop="orgId" :label="$t('事业部')" label-style="top">
              <mt-input v-model="searchFormModel.orgId"></mt-input>
            </mt-form-item>
            <mt-form-item prop="code" :label="$t('预设配额单号')" label-style="top">
              <mt-input v-model="searchFormModel.code"></mt-input>
            </mt-form-item>
            <mt-form-item prop="title" :label="$t('预设配额标题')" label-style="top">
              <mt-input v-model="searchFormModel.title"></mt-input>
            </mt-form-item>
            <mt-form-item prop="sourceCode" :label="$t('历史单据号')" label-style="top">
              <mt-input v-model="searchFormModel.sourceCode"></mt-input>
            </mt-form-item>
            <mt-form-item prop="supplierList" :label="$t('供应商')" label-style="top">
              <!-- <mt-input v-model="searchFormModel.supplierCode"></mt-input> -->
              <RemoteAutocomplete
                v-model="searchFormModel.supplierList"
                url="/masterDataManagement/tenant/supplier/paged-query"
                multiple
                :placeholder="$t('请选择')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="supplierSearchFields"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="itemCode" :label="$t('物料选择')" label-style="top">
              <!-- <MasterdataDropdownSelects
                :remote-search="true"
                v-model="searchFormModel.itemCode"
                operator="in"
                :fields="{ text: 'title', value: 'itemCode' }"
                :title-switch="false"
                :multiple="true"
                :placeholder="$t('请选择物料')"
                select-type="material"
              ></MasterdataDropdownSelects> -->
            </mt-form-item>
            <mt-form-item prop="multiVal" :label="$t('多选测试')" label-style="top">
              <mt-multi-select
                v-model="searchFormModel.multiVal"
                :allow-filtering="true"
                :data-source="opList"
                :fields="{ text: 'text', value: 'value' }"
                :show-clear-button="true"
                :show-select-all="false"
              >
                <div>multiSelTest</div>
              </mt-multi-select>
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { pageConfig } from './config'
// import { download, getHeadersFileName } from '@/utils/utils'
// import MasterdataDropdownSelects from '@/components/masterdataSelects/masterdata-dropdown-selects/src/main.vue'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  components: {
    // MasterdataDropdownSelects,
    RemoteAutocomplete
  },
  data() {
    return {
      searchFormModel: {
        supplierList: ['100007', '100043'] // 当为空时不要写null 需要写成空数组，不然在全选时会报错
        // supplierList: []
      },
      pageConfig: [],
      searchRules: {
        supplierList: [{ required: true, message: this.$t('必填') }]
      },
      opList: [
        { text: '归属1', value: '111' },
        { text: '归属2', value: '222' },
        { text: '归属3', value: '333' },
        { text: '归属4', value: '444' },
        { text: '归属5', value: '555' },
        { text: '归属6', value: '666' }
      ],
      supplierSearchFields: ['supplierCode', 'supplierName']
    }
  },
  created() {
    this.init()
    this.$bus.$on('refreshQuotaDefualtList', () => {
      this.$refs.templateRef.refreshCurrentGridData()
    })
    // this.$bus.$on('changeProvidedEditorData', (arr) => {
    //   debugger
    //   this.pageConfig[0].grid.dataSource = arr
    // })
  },
  methods: {
    async init() {
      this.pageConfig = pageConfig(this.$API.quotaDefault.queryBuilder, this.searchFormModel)
    },
    //新增
    handleAdd(e) {
      e.grid.addRecord()
    },
    //创建新版本
    handleCreate(row) {
      if (row.status != 3) {
        this.$toast({ content: this.$t('审核通过的单据才能创建新版本'), type: 'warning' })
        return
      }
      this.$router.push({
        name: `quota-strategy-default-detail`,
        query: {
          type: 'update',
          id: row.id,
          flashKey: new Date().getTime()
        }
      })
    },
    //导出
    handleExport() {
      console.log(
        '11111111',
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.getCurrentViewRecords()
      )
    },
    //表格头部按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.grid.getSelectedRecords()
      if (_selectGridRecords.length != 1 && e.toolbar.id == 'CreateNew') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        this.handleAdd(e)
      } else if (e.toolbar.id == 'CreateNew') {
        this.handleCreate(_selectGridRecords[0])
      } else if (e.toolbar.id == 'ExportFile') {
        this.handleExport()
      }
    },
    handleClickCellTool(e) {
      const { tool, data } = e
      if (tool.id === 'edit') {
        this.$router.push({
          name: `quota-strategy-default-detail`,
          query: {
            type: 'Edit',
            id: data.id,
            flashKey: new Date().getTime()
          }
        })
      } else if (tool.id === 'delete') {
        this.quotaDefaultDelete([data])
      } else if (tool.id === 'detail') {
        this.$router.push({
          name: `quota-strategy-default-detail`,
          query: {
            type: 'detail',
            id: data.id,
            flashKey: new Date().getTime()
          }
        })
      }
    },
    // 删除
    quotaDefaultDelete(rows) {
      // 调对应接口后刷新列表
      let idList = rows.map((v) => {
        return v.id
      })
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.quotaDefault.batchDelete({ idList }).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    handleCustomSearch(model) {
      console.log('1111111111', model)
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-form-box {
}
</style>
