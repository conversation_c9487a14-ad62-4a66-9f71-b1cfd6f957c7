<template>
  <div>
    <mt-input data-falg="myFlag" type="text" v-model="scoped[fieldKey]" @change="change" />
  </div>
</template>
<script>
import cloneDeep from 'lodash/cloneDeep'

export default {
  name: 'DeepEdit',
  props: {
    scoped: {
      type: Object,
      default: () => {
        return {}
      }
    },
    dataSource: {
      type: Object,
      default: () => {
        return {}
      }
    },
    fieldKey: {
      type: String,
      default: ''
    },
    parentInstance: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      isChange: false
      // fieldValue: null
    }
  },
  computed: {
    // fieldValue: {
    //   get() {
    //     return this.scoped[this.fieldKey]
    //   },
    //   set(val) {
    //     this.$set(this.scoped, this.fieldKey, val)
    //     this.isChange = true
    //   }
    // }
  },
  watch: {
    scoped: {
      handler() {
        this.isChange = true
      },
      deep: true
    }
  },
  created() {
    this.$bus.$on('actionCompleteBySave', (args) => {
      if (this.isChange) {
        args[this.fieldKey] = this.scoped[this.fieldKey]
        const tempArr = cloneDeep(this.dataSource.tempSource)
        tempArr[this.scoped.index] = args
        this.$set(this.dataSource, 'tempSource', tempArr)
        this.isChange = false
      }
    })
  },
  destroyed() {
    this.$nextTick(() => {
      this.$bus.$off('actionCompleteBySave')
    })
  },
  methods: {
    change() {
      // this.$set(this.scoped, this.fieldKey, this.fieldValue)
      // this.isChange = true
    }
  }
}
</script>
