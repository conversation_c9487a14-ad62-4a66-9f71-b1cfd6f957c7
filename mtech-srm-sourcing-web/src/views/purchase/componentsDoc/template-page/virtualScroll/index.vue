<template>
  <mt-template-page :template-config="templateConfig" @handleClickToolBar="handleClickToolBar" />
</template>
<script>
import { columnData } from './config'

export default {
  name: 'VirtualScroll',
  data() {
    return {
      templateConfig: [
        {
          isUseCustomEditor: true,
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 使用组件中的toolbar配置
          toolbar: [
            [{ id: 'pending', icon: 'icon_solid_edit', title: this.$t('审批') }],
            ['Filter', 'Setting']
          ], // 此页面为动态数据关联的动态表格，故不可设置字段显示隐藏
          // gridId: '35669a0d-d228-4850-97f2-fc7bb9622bf8',
          grid: {
            height: 556,
            virtualPageSize: 30,
            // rowHeight: 40,
            enableVirtualization: true,
            // showSelected: false,
            // selectionSettings: {
            //   persistSelection: true, // 这个选项很重要，影响到虚拟滚动下能不能操作全选按钮
            //   type: 'Multiple',
            //   checkboxOnly: true
            // },
            customSelection: true,
            editSettings: {
              allowAdding: true,
              allowEditing: true,
              allowDeleting: true,
              mode: 'Normal', // 默认normal模式
              newRowPosition: 'Top'
            },
            columnData,
            gridLines: 'Both',
            allowSorting: false,
            allowPaging: false, // 不分页/
            allowReordering: false, // 不可拖动排序 因为预测数据是动态获取到的，表格记忆排序不能处理
            dataSource: []
          }
        }
      ]
    }
  },
  mounted() {
    const dataSource = []
    for (let i = 0; i < 50; i++) {
      dataSource.push(this.createDataItem(i))
    }
    this.templateConfig[0]['grid']['dataSource'] = dataSource
  },
  methods: {
    createDataItem(rowIndex) {
      const row = {}
      row.rowIndex = rowIndex + 1
      for (let i = 1; i < 31; i++) {
        row['column' + i] = '' + i + i + i
      }
      return Object.assign({}, row)
    },
    handleClickToolBar(args) {
      if (args.toolbar.id === 'pending') {
        debugger
      }
    }
  }
}
</script>
<style lang="scss" scoped></style>
