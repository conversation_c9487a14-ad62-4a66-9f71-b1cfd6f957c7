<template>
  <!-- 预设配额 :is-custom-search-handle="true"-->
  <div class="full-height">
    <div>
      <b>起用版本：1.8.14</b>
      <br />
      <b>属性配置（组件的template-config中配置）:</b>
      <br /><br />
      <ol>
        <li>1. isUseCustomSearch - 开启自定义快捷查询</li>
        <li>
          2. isCustomSearchHandle -
          开启自定义查询操作，当置为true时点击“查询”按钮将不再走组件内部定义的查询逻辑，而是抛出一个handleCustomSearch事件供开发者使用
        </li>
        <li>
          3. customMatchRules -
          字段匹配（后端用来确定字段是模糊匹配(contains)还是精确匹配(equal)的）规则设置对象，字段如果不设置就默认为contains
        </li>
      </ol>
      <br />
      <b>事件：</b>
      <br /><br />
      <ol>
        <li>1. handleCustomSearch</li>
      </ol>
      <br />
      <b>注意：</b>
      <br /><br />
      <ol>
        <li>
          1.
          自定义form绑定的model是作用域插槽传出来的searchFormModel，不需要开发者在引用mt-template-page组件的父组件中定义，
          后果后期有需要可以改成由父组件定义然后传入mt-template-page中去
        </li>
      </ol>
    </div>
    <TemplatePage
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleCustomSearch="handleCustomSearch"
    >
      <template v-slot:quick-search-form="{ searchFormModel }">
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchRules">
            <mt-form-item prop="orgId" :label="$t('事业部')" label-style="left">
              <mt-input v-model="searchFormModel.orgId"></mt-input>
            </mt-form-item>
            <mt-form-item prop="code" :label="$t('预设配额单号')" label-style="left">
              <mt-input v-model="searchFormModel.code"></mt-input>
            </mt-form-item>
            <mt-form-item prop="title" :label="$t('预设配额标题')" label-style="left">
              <mt-input v-model="searchFormModel.title"></mt-input>
            </mt-form-item>
            <mt-form-item prop="sourceCode" :label="$t('历史单据号')" label-style="left">
              <mt-input v-model="searchFormModel.sourceCode"></mt-input>
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('供应商')" label-style="left">
              <mt-input v-model="searchFormModel.supplierCode"></mt-input>
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </TemplatePage>
  </div>
</template>

<script>
import { pageConfig } from './config'
import { download, getHeadersFileName } from '@/utils/utils'
import TemplatePage from '@/components/template-page'

export default {
  components: {
    TemplatePage
  },
  data() {
    return {
      searchFormModel: {
        orgId: 111
      },
      pageConfig: [],
      searchRules: {
        supplierCode: [{ required: true, message: this.$t('必填') }]
      }
    }
  },
  created() {
    this.init()
    this.$bus.$on('refreshQuotaDefualtList', () => {
      this.$refs.templateRef.refreshCurrentGridData()
    })
  },
  methods: {
    async init() {
      let orgData = []
      const { code, data } = await this.$API.quotaDefault.queryOrg()
      if (code === 200) {
        orgData = data
        Array.isArray(orgData) &&
          orgData.forEach((item) => {
            item.orgId = item.id
            item.cssClass = 'title-#9baac1'
          })
      }
      //获取到当前登录用户信息
      let userInfo = JSON.parse(sessionStorage.getItem('userInfo')) || {}
      this.pageConfig = pageConfig(
        this.$API.quotaDefault.queryBuilder,
        orgData,
        userInfo?.uid,
        this.searchFormModel
      )
    },
    //新增
    handleAdd() {
      this.$router.push({
        name: `quota-strategy-default-detail`,
        query: {
          type: 'add',
          flashKey: new Date().getTime()
        }
      })
    },
    //创建新版本
    handleCreate(row) {
      if (row.status != 3) {
        this.$toast({ content: this.$t('审核通过的单据才能创建新版本'), type: 'warning' })
        return
      }
      this.$router.push({
        name: `quota-strategy-default-detail`,
        query: {
          type: 'update',
          id: row.id,
          flashKey: new Date().getTime()
        }
      })
    },
    //导出
    handleExport() {
      // 先要点击“查询”按钮才能取得queryBuilderRules
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules,
        defaultRules: [
          // {
          //   condition: 'and',
          //   field: 'rfx_item.rfxHeaderId',
          //   operator: 'equal',
          //   value: this.rfxId
          // }
        ].concat(queryBuilderRules.rules || [])
      } // 筛选条件
      this.$API.quotaDefault.excelExport(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    //表格头部按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.grid.getSelectedRecords()
      if (_selectGridRecords.length != 1 && e.toolbar.id == 'CreateNew') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'CreateNew') {
        this.handleCreate(_selectGridRecords[0])
      } else if (e.toolbar.id == 'ExportFile') {
        this.handleExport()
      }
    },
    handleClickCellTool(e) {
      const { tool, data } = e
      if (tool.id === 'edit') {
        this.$router.push({
          name: `quota-strategy-default-detail`,
          query: {
            type: 'Edit',
            id: data.id,
            flashKey: new Date().getTime()
          }
        })
      } else if (tool.id === 'delete') {
        this.quotaDefaultDelete([data])
      } else if (tool.id === 'detail') {
        this.$router.push({
          name: `quota-strategy-default-detail`,
          query: {
            type: 'detail',
            id: data.id,
            flashKey: new Date().getTime()
          }
        })
      }
    },
    // 删除
    quotaDefaultDelete(rows) {
      // 调对应接口后刷新列表
      let idList = rows.map((v) => {
        return v.id
      })
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.quotaDefault.batchDelete({ idList }).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    handleCustomSearch(model) {
      console.log('1111111111', model)
    }
  }
}
</script>

<style></style>
