<template>
  <div>
    <div>
      <button @click="showFirstTable = true">展示第一个</button>
      <button @click="showFirstTable = false">展示第二个</button>
    </div>
    <mt-template-page
      :class="[showFirstTable ? '' : 'hideTable']"
      :template-config="templateConfig1"
    />
    <mt-template-page
      :class="[showFirstTable ? 'hideTable' : '']"
      :template-config="templateConfig2"
    />
  </div>
</template>
<script>
import { columnData } from './config'

export default {
  name: 'VirtualScroll',
  data() {
    return {
      templateConfig1: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 使用组件中的toolbar配置
          toolbar: [[], ['Filter', 'Setting']],
          // gridId: '35669a0d-d228-4850-97f2-fc7bb9622bf8',
          grid: {
            height: 400,
            virtualPageSize: 30,
            // rowHeight: 40,
            enableVirtualization: true,
            // enableColumnVirtualization: true,
            showSelected: false,
            selectionSettings: {
              persistSelection: true, // 这个选项很重要，影响到虚拟滚动下能不能操作全选按钮
              type: 'Multiple',
              checkboxOnly: true
            },
            columnData,
            // gridLines: 'Both',
            // allowPaging: false, // 不分页
            allowReordering: false, // 不可拖动排序 因为预测数据是动态获取到的，表格记忆排序不能处理
            // dataSource: []
            asyncConfig: {
              methods: 'get',
              url: '/mock-api/virtualScrollData.json'
            }
          }
        }
      ],
      templateConfig2: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 使用组件中的toolbar配置
          toolbar: [],
          // gridId: '35669a0d-d228-4850-97f2-fc7bb9622bf8',
          grid: {
            height: 400,
            virtualPageSize: 30,
            // rowHeight: 40,
            enableVirtualization: true,
            // enableColumnVirtualization: true,
            showSelected: false,
            selectionSettings: {
              persistSelection: true, // 这个选项很重要，影响到虚拟滚动下能不能操作全选按钮
              type: 'Multiple',
              checkboxOnly: true
            },
            columnData,
            // gridLines: 'Both',
            // allowPaging: false, // 不分页
            allowReordering: false, // 不可拖动排序 因为预测数据是动态获取到的，表格记忆排序不能处理
            // dataSource: []
            asyncConfig: {
              methods: 'get',
              url: '/mock-api/virtualScrollData.json'
            }
          }
        }
      ],
      showFirstTable: true
    }
  },
  mounted() {
    // const dataSource = []
    // for (let i = 0; i < 200; i++) {
    //   dataSource.push(this.createDataItem(i))
    // }
    // this.templateConfig[0]['grid']['dataSource'] = dataSource
  },
  methods: {
    createDataItem(rowIndex) {
      const row = {}
      row.rowIndex = rowIndex + 1
      for (let i = 1; i < 31; i++) {
        row['column' + i] = '' + i + i + i
      }
      return Object.assign({}, row)
    }
  }
}
</script>
<style lang="scss">
.hideTable {
  height: 0px !important;
}
</style>
