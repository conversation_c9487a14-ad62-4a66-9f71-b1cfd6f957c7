<template>
  <div class="topStep">
    <div
      v-for="(step, index) in stepData"
      :key="index"
      :style="{ flexBasis: flexBasis }"
      :class="['stepItem', step.type]"
    >
      <div class="step-dot-container">
        <span class="step-dot">{{ index + 1 }}</span>
        <div class="step-line"></div>
      </div>
      <div class="step-content">
        <div class="title">{{ step.title }}</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'ExpertRating',
  props: {
    status: {
      type: [String, Number],
      default: 0
    }
  },
  watch: {
    status: {
      handler(value) {
        let _value = Number(value)
        if (_value < 0) {
          return
        }
        let _isEqualCurrentIndex = false
        let _data = this.stepData.map((item) => {
          if (item.status.includes(_value)) {
            item.type = 'pending'
            _isEqualCurrentIndex = true
          } else {
            item.type = _isEqualCurrentIndex ? '' : 'finished'
          }
          return item
        })
        this.flexBasis = 100 / this.stepData.length + '%'
        this.stepData = _data
      },
      immediate: true
    }
  },
  data() {
    return {
      stepData: [
        {
          title: this.$t('草稿'),
          status: [0],
          type: ''
        },
        {
          title: this.$t('寻源审批中'),
          status: [1],
          type: ''
        },
        {
          title: this.$t('寻源需求已发布'),
          status: [2],
          type: ''
        },
        {
          title: this.$t('入围评定'),
          status: [3, 5],
          type: ''
        },
        {
          title: this.$t('寻源结果审批'),
          status: [4],
          type: ''
        },
        {
          title: this.$t('寻源结果已发布'),
          status: [6],
          type: ''
        }
      ],
      flexBasis: '100%'
    }
  },
  mounted() {}
}
</script>
<style lang="scss">
.topStep {
  display: flex;
  .stepItem {
    display: flex;
    flex-direction: column;
    flex-shrink: 1;
    text-align: center;
    .step-dot-container {
      position: relative;
      .step-dot {
        display: inline-flex;
        justify-content: center;
        align-items: center;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: var(--devui-brand-foil, #f2f2f3);
        color: var(--devui-text, #252b3a);
      }
      .step-line {
        position: absolute;
        top: 12px;
        left: calc(50% + 12px);
        width: calc(100% - 24px);
        height: 1px;
        background-color: var(--devui-line, #d7d8da);
      }
    }
    &:last-child .step-line {
      display: none;
    }
    .step-content {
      .title {
        font-size: 14px;
        line-height: 32px;
        font-weight: bold;
        color: #babbc0;
      }
    }
    &.finished {
      .step-dot {
        background: #50d4ab;
        color: #ffffff;
      }
      .step-content .title {
        color: #50d4ab;
      }
    }
    &.pending {
      .step-dot {
        background: #5e7ce0;
        color: #ffffff;
      }
      .step-content .title {
        color: #5e7ce0;
      }
    }
  }
}
</style>
