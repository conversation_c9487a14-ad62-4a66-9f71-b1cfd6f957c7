<template>
  <!-- 需求采购明细 -->
  <div class="purchaseItem">
    <CustomAgGrid
      ref="CustomAgGrid"
      :toolbar="toolbar"
      :columns="columns"
      :suppress-row-transform="true"
      :row-data="tableData"
      :animate-rows="false"
      :params-type="2"
      @handleClickToolbar="handleClickToolbar"
      @onGridReady="onGridReady"
      @row-selected="onRowSelected"
      @cell-value-changed="cellValueChanged"
      @refresh="refresh"
      @search="search"
    >
    </CustomAgGrid>
  </div>
</template>
<script>
import CustomAgGrid from '@/components/CustomAgGrid'
import { columns, toolbar } from './config/tclFormerTable'
import cellFile from '@/components/AgCellComponents/cellFile'
import { v4 as uuidv4 } from 'uuid'
export default {
  name: 'BaseInfoTab',
  components: {
    CustomAgGrid,
    // eslint-disable-next-line vue/no-unused-components
    cellFile
  },
  data() {
    return {
      tableData: [],
      agGrid: null,
      columns: null
    }
  },
  computed: {
    toolbar() {
      return this.editable ? toolbar : []
    }
  },
  props: {
    // 初始化数据
    dataSource: {
      type: Array,
      default: () => []
    },
    editable: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    dataSource: {
      handler(val) {
        val.length && this.initTableData()
      }
    },
    editable: {
      handler() {
        this.initColumns()
      },
      immediate: true
    }
  },
  mounted() {},
  methods: {
    initColumns() {
      this.columns = columns.map((item) => {
        let _item = { ...item }
        //如果页面不可编辑，清空编辑选项
        if (!this.editable) {
          _item.editable = false
          _item.cellClass = ''
        }
        return { ..._item }
      })
    },
    // 初始化渲染表格
    initTableData() {
      this.tableData = this.dataSource.map((item, index) => {
        item.lineNo = index + 1
        item.rowId = uuidv4()
        return item
      })
    },
    handleClickToolbar(args) {
      let _id = args.toolbar.id
      switch (_id) {
        case 'Add':
          this.handleAdd() // 新增
          break
        case 'Delete':
          this.handleDelete() // 删除
          break
      }
    },

    handleAdd() {
      this.tableData.push({})
    },
    handleDelete() {
      const rowSelections = this.agGrid.api.getSelectedRows()
      const selctionsArr = rowSelections.map((item) => item.rowId)
      this.tableData.filter((item) => selctionsArr.includes(item.rowId))
    },
    onGridReady(params) {
      this.agGrid = params
    },
    onRowSelected() {},
    cellValueChanged() {},
    refresh() {},
    search() {}
  }
}
</script>
<style lang="scss" scoped>
.purchaseItem {
  height: 300px;
  .grid-container {
    height: 100%;
  }
}
</style>
