import { i18n } from '@/main.js'

export const toolbar = [
  { id: 'Add', icon: '', title: i18n.t('新增') },
  { id: 'Delete', icon: '', title: i18n.t('删除') }
]

export const columns = [
  { option: 'checkboxSelection', width: 55 },
  {
    headerName: '相关人员在TCL工作经历',
    children: [
      {
        field: 'employeeName',
        headerName: i18n.t('曾在TCL任职人员姓名'),
        width: 120,
        editable: true
      },
      {
        field: 'idcardNo',
        headerName: i18n.t('证件号码'),
        width: 120,
        editable: true
      },
      {
        field: 'belongBuName',
        headerName: i18n.t('离职前所在事业部'),
        editable: true
      },
      {
        field: 'jobTitleName',
        headerName: i18n.t('TCL职务'),
        width: 120,
        editable: true
      },
      {
        field: 'workstage',
        headerName: i18n.t('TCL职级'),
        width: 120,
        editable: true
      },
      {
        field: 'resignTime',
        headerName: i18n.t('从TCL离职时间'),
        width: 150,
        editable: true,
        option: 'customEdit',
        editConfig: {
          type: 'date',
          format: 'yyyy-MM-dd',
          'time-stamp': true,
          'show-clear-button': false
        }
      },
      {
        field: 'isBlacklist',
        headerName: i18n.t('是否黑名单员工'),
        width: 100,
        editable: true,
        option: 'customEdit',
        editConfig: {
          type: 'select',
          props: {
            'show-clear-button': true,
            fields: { value: 'value', text: 'text' },
            dataSource: [
              { value: 0, text: i18n.t('否') },
              { value: 1, text: i18n.t('是') }
            ]
          }
        }
      }
    ]
  },
  {
    headerName: '相关人员在供应商工作经历',
    children: [
      {
        field: 'inductionTime',
        headerName: i18n.t('在供应商入职时间'),
        editable: true,
        option: 'customEdit',
        editConfig: {
          type: 'date',
          format: 'yyyy-MM-dd',
          'time-stamp': true,
          'show-clear-button': false
        }
      },
      {
        field: 'supplierJobTitleName',
        headerName: i18n.t('担任职务'),
        editable: true
      },
      {
        field: 'isSharehold',
        headerName: i18n.t('是否有股份'),
        editable: true,
        option: 'customEdit',
        editConfig: {
          type: 'select',
          props: {
            'show-clear-button': true,
            fields: { value: 'value', text: 'text' },
            dataSource: [
              { value: 0, text: i18n.t('否') },
              { value: 1, text: i18n.t('是') }
            ]
          }
        }
      },
      {
        field: 'shareholdRatio',
        headerName: i18n.t('持股比例（%）'),
        editable: true,
        option: 'customEdit',
        editConfig: {
          type: 'number',
          props: {
            min: 0,
            max: 100,
            precision: '0'
          }
        }
      },
      {
        field: 'isPost',
        headerName: i18n.t('是否在岗'),
        editable: true,
        option: 'customEdit',
        editConfig: {
          type: 'select',
          props: {
            'show-clear-button': true,
            fields: { value: 'value', text: 'text' },
            dataSource: [
              { value: 0, text: i18n.t('否') },
              { value: 1, text: i18n.t('是') }
            ]
          }
        }
      }
    ]
  }
]
