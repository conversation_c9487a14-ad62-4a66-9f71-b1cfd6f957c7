<template>
  <!-- 邀请供应商 -->
  <div class="inviteSupplier">
    <CustomAgGrid
      ref="CustomAgGrid"
      :toolbar="toolbar"
      :columns="columns"
      :suppress-row-transform="true"
      :row-data="tableData"
      :animate-rows="false"
      :params-type="2"
      @handleClickToolbar="handleClickToolbar"
      @onGridReady="onGridReady"
      @refresh="refresh"
    >
    </CustomAgGrid>
  </div>
</template>
<script>
import CustomAgGrid from '@/components/CustomAgGrid'
import { columns, toolbar } from './config/affiliatEnterpriseTable'
import { v4 as uuidv4 } from 'uuid'
import { cloneDeep } from 'lodash'
export default {
  name: 'BaseInfoTab',
  components: {
    CustomAgGrid
  },
  props: {
    // 初始化数据
    dataSource: {
      type: Array,
      default: () => []
    },
    // 是否可编辑
    editable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      columns: null,
      tableData: []
    }
  },
  watch: {
    dataSource: {
      handler(val) {
        val?.length && this.initTableData()
      }
    },
    editable: {
      handler() {
        this.initColumns()
      },
      immediate: true
    }
  },
  computed: {
    toolbar() {
      return this.editable ? toolbar : []
    }
  },
  methods: {
    initColumns() {
      this.columns = columns.map((item) => {
        let _item = { ...item }
        //如果页面不可编辑，清空编辑选项
        if (!this.editable) {
          _item.editable = false
          _item.cellClass = ''
        }
        return { ..._item }
      })
    },
    // 初始化渲染表格
    initTableData() {
      this.tableData = this.dataSource.map((item) => {
        item.rowId = uuidv4()
        return item
      })
    },
    onGridReady(params) {
      this.agGrid = params
    },
    handleClickToolbar(args) {
      let _id = args.toolbar.id
      switch (_id) {
        case 'Add':
          this.handleAdd() // 新增
          break
        case 'Delete':
          this.handleDelete() // 删除
          break
      }
    },
    handleAdd() {
      this.tableData.push({})
    },
    handleDelete() {
      const rowSelections = this.agGrid.api.getSelectedRows()
      const selctionsArr = rowSelections.map((item) => item.rowId)
      this.tableData = this.tableData.filter((item) => !selctionsArr.includes(item.rowId))
    },
    // 表格 - 工具 - 获取表格数据
    getRowData() {
      const rowData = []
      this.agGrid.api.forEachNode(function (node) {
        rowData.push(node.data)
      })
      return rowData
    },
    refresh() {
      this.tableData = cloneDeep(this.initTableData)
    }
  }
}
</script>
<style lang="scss" scoped>
.inviteSupplier {
  height: 300px;
  .grid-container {
    height: 100%;
  }
}
</style>
