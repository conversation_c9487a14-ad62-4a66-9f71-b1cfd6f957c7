<template>
  <!-- 报价明细 -->
  <div class="quotationItem">
    <CustomAgGrid
      ref="CustomAgGrid"
      :toolbar="toolbar"
      :columns="columns"
      :suppress-row-transform="true"
      :row-data="tableData"
      :animate-rows="false"
      :params-type="2"
      @onGridReady="onGridReady"
      @refresh="refresh"
      @search="search"
    >
    </CustomAgGrid>
  </div>
</template>
<script>
import CustomAgGrid from '@/components/CustomAgGrid'
import { columns } from './config'
import cellFile from '@/components/AgCellComponents/cellFile'
import { v4 as uuidv4 } from 'uuid'
export default {
  name: 'QuotationTab',
  components: {
    CustomAgGrid,
    // eslint-disable-next-line vue/no-unused-components
    cellFile
  },
  props: {
    // 初始化数据
    dataSource: {
      type: Array,
      default: () => []
    },
    editable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      toolbar: [],
      tableData: [],
      agGrid: null,
      columns: null
    }
  },
  watch: {
    editable: {
      handler() {
        this.initColumns()
      },
      immediate: true
    }
  },
  mounted() {
    this.initTableData()
  },
  methods: {
    initColumns() {
      this.columns = columns.map((item) => {
        let _item = { ...item }
        if (item.field === 'drawingAttachId') {
          _item.cellRenderer = 'cellFile'
          _item.cellRendererParams = {
            handleable: false
          }
          _item.editable = false
        }
        //如果页面不可编辑，清空编辑选项
        if (!this.editable) {
          _item.editable = false
          _item.cellClass = ''
        }
        return { ..._item }
      })
    },
    // 初始化渲染表格
    async initTableData() {
      let res = await this.$API.publicSourcing.queryBiddingItem({
        id: this.$route.query.rfxId,
        supplierTenantId: this.$route.query.sId
      })
      if (res.code === 200) {
        this.tableData = res.data?.supplierBiddingItems?.map((item, index) => {
          item.lineNo = index + 1
          item.rowId = uuidv4()
          item.untaxedUnitPrice = item.untaxedUnitPrice === -999 ? '***' : item.untaxedUnitPrice
          return item
        })
      }
    },

    onGridReady(params) {
      this.agGrid = params
    },
    refresh() {
      this.initTableData()
    },
    search() {}
    // 表格 - 工具 - 获取表格数据
  }
}
</script>
<style lang="scss" scoped>
.quotationItem {
  height: 500px;
  .grid-container {
    height: 100%;
  }
}
</style>
