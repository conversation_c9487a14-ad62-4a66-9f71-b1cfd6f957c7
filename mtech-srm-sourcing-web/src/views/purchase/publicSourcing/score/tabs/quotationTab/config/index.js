import { i18n } from '@/main.js'
export const sourceMethodList = {
  open: i18n.t('公开'),
  target: i18n.t('邀请')
}
export const columns = [
  {
    field: 'lineNo',
    headerName: i18n.t('行号'),
    width: 65
  },
  {
    field: 'itemCode',
    headerName: i18n.t('物料编号')
  },
  {
    field: 'itemName',
    headerName: i18n.t('物料名称')
  },
  {
    field: 'demandQty',
    headerName: i18n.t('预计年需求量'),
    width: 120
  },
  {
    field: 'unitCode',
    headerName: i18n.t('单位'),
    width: 80
  },
  {
    field: 'currencyCode',
    headerName: i18n.t('币种'),
    width: 80
  },
  {
    field: 'untaxedUnitPrice',
    headerName: i18n.t('单价（未税）'),
    option: 'customEdit',
    editConfig: {
      type: 'number'
    },
    cellClass: 'cellEditable',
    width: 120
  },
  {
    field: 'materialUsage',
    headerName: i18n.t('物料用途')
  },
  {
    field: 'remark',
    headerName: i18n.t('备注')
  },
  {
    field: 'drawingAttachId',
    headerName: i18n.t('规格图纸')
  }
]
