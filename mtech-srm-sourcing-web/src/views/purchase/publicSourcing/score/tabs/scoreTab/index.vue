<template>
  <!-- 需求采购明细 -->
  <div class="requireWrapper">
    <CustomAgGrid
      ref="CustomAgGrid"
      :toolbar="toolbar"
      :columns="columns"
      :suppress-row-transform="true"
      :row-data="tableData"
      :animate-rows="false"
      :params-type="2"
      :row-height="40"
      @handleClickToolbar="handleClickToolbar"
      @onGridReady="onGridReady"
      @row-selected="onRowSelected"
      @cell-value-changed="cellValueChanged"
      @refresh="refresh"
      @search="search"
    >
    </CustomAgGrid>
  </div>
</template>
<script>
import CustomAgGrid from '@/components/CustomAgGrid'
import { columns, currentDescMap } from './config'
import cellFile from '@/components/AgCellComponents/cellFile'
import cellDescView from '@/components/AgCellComponents/cellDescView'
import cellSupplier from '@/components/AgCellComponents/cellSupplier' // 供应商现状列
import { v4 as uuidv4 } from 'uuid'
import { cloneDeep } from 'lodash'
export default {
  name: 'BaseInfoTab',
  inject: ['reload'],
  components: {
    CustomAgGrid,
    // eslint-disable-next-line vue/no-unused-components
    cellFile,
    // eslint-disable-next-line vue/no-unused-components
    cellDescView,
    // eslint-disable-next-line vue/no-unused-components
    cellSupplier
  },
  data() {
    return {
      toolbar: [],
      tableData: [],
      agGrid: null,
      columns: null
    }
  },
  props: {
    basicInfo: {
      type: Object,
      default: null
    },
    editable: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    editable: {
      handler() {
        this.initColumns()
      },
      immediate: true
    }
  },
  mounted() {
    this.initTableData()
    // 字典数据
    // this.initDictItems()
  },

  methods: {
    // 初始化 - 列表
    initColumns() {
      this.columns = columns.map((item) => {
        let _item = { ...item }
        if (_item.field === 'scoreStandardCode') {
          _item.cellRenderer = 'cellSupplier'
        }

        //如果页面不可编辑，清空编辑选项
        if (!this.editable) {
          _item.editable = false
          _item.cellClass = ''
        }
        return { ..._item }
      })
    },
    // 初始化 - 字典数据
    async initDictItems() {
      let codeList = [
        { code: 'PAY_METHOD', type: 'string' } // 现有客户群体
      ]
      await this.$API.masterData.getNumDictListAllByCode(codeList).then((res) => {
        if (res.code === 200) {
          this.dictItems = res.data
        }
      })
    },

    // 初始化渲染表格
    async initTableData() {
      let res = await this.$API.publicSourcing.queryScoreList({
        rfxId: this.$route.query?.rfxId,
        supplierTenantId: this.$route.query.sId
      })

      if (res.code === 200) {
        this.tableData = res.data?.map((item, index) => {
          item.rowId = uuidv4()
          item.lineNo = index + 1
          return item
        })
        this.cacheTableData = cloneDeep(this.tableData)
      }
    },
    handleClickToolbar(args) {
      let _id = args.toolbar.id
      switch (_id) {
        case 'Save':
          this.handleSave() // 保存
          break
      }
    },
    // 保存数据
    async handleSave(isCurrentPage) {
      let _tableData = this.getRowData()
      let flag = this.validData(_tableData)
      if (!flag) return false
      let res = await this.$API.publicSourcing.saveScoreInfo(_tableData)
      if (!isCurrentPage && res.code === 200) {
        this.$toast({ content: this.$t('保存成功！'), type: 'success' })
      }
      return res
    },
    // 数据校验
    validData(data) {
      if (!data?.length) return false
      for (let i = 0; i < data.length; i++) {
        if (data[i].score > data[i].scoreWeight) {
          this.$toast({ content: this.$t('得分不能超过权重'), type: 'warning' })
          return false
        }
      }
      return true
    },
    // 表格- 提交 - 数据处理
    formatSubmitData(data) {
      if (!data) return []
      data.forEach((item) => {
        if ('supplierSourcingFiles' in item || 'sourcingFiles' in item) {
          let _supplierSourcingFiles = JSON.parse(item.supplierSourcingFiles) || []
          item.supplierSourcingFiles = _supplierSourcingFiles
        }
      })
      return data
    },
    // 需求项目联动
    inputRequireType() {
      // 如果寻源需求列表中已经存在【品质要求、技术要求、其它要求】则dataSource中给去掉
    },
    onGridReady(params) {
      this.agGrid = params
    },
    onRowSelected() {},
    cellValueChanged(params) {
      if (
        params.colDef.field === 'scoreStandardCode' &&
        params.data.ratingDimensionCode === 'technical_bonus_points'
      ) {
        const rowData = Object.assign({}, params.node.data, {
          currentDescText: currentDescMap[params.value]
        })
        params.node.setData(rowData)
      }
      // 如果是得分,则其中的值不能大于权重
      if (params.colDef.field === 'score') {
        if (params.value > params.data.scoreWeight) {
          this.$toast({ content: this.$t('得分不能超过权重'), type: 'warning' })
        }
      }
    },
    // 表格 - 工具 - 获取表格数据
    getRowData() {
      const rowData = []
      this.agGrid.api.forEachNode(function (node) {
        rowData.push(node.data)
      })
      return rowData
    },
    refresh() {
      this.initTableData()
    },
    search() {}
  }
}
</script>
<style lang="scss">
.requireWrapper {
  flex-grow: 1;
  .grid-container {
    height: 100%;
  }
  .ag-theme-alpine .ag-cell-inline-editing {
    height: 40px !important;
  }
  .mt-input-number {
    width: 100px;
  }
  .mt-input-number .step-box .one-icon {
    display: none;
  }
  .mt-input-number input[type='number'] {
    padding-left: 10px;
    padding-right: 10px;
  }
  .mt-input-number .icon-clear {
    margin-top: 6px;
    right: 10px;
  }
  .mt-input {
    width: 100%;
  }
}
</style>
