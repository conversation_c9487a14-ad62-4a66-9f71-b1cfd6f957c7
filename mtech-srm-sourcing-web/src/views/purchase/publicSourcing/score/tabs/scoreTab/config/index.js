import { i18n } from '@/main.js'
export const currentDescList = [
  {
    text: i18n.t('供方参与过三新物料(创新型)技术开发或提供三新物料关键技术(含专利授权)'),
    value: 'ZLPFONE'
  }
]
export const currentDescMap = {
  ZLPFONE: i18n.t('供方参与过三新物料(创新型)技术开发或提供三新物料关键技术(含专利授权)')
}

export const toolbar = [{ id: 'Save', icon: '', title: i18n.t('保存') }]

export const columns = [
  {
    field: 'lineNo',
    headerName: i18n.t('行号'),
    width: 65
  },
  {
    field: 'ratingDimension',
    headerName: i18n.t('评估类别'),
    width: 110
  },
  {
    field: 'scoreIndex',
    headerName: i18n.t('评估内容'),
    width: 280
  },
  {
    field: 'scoreStandardCode',
    headerName: i18n.t('供应商现状'),
    width: 220,
    option: 'customEdit',
    editable: (params) => {
      return params.data.ratingDimensionCode === 'technical_bonus_points'
    },
    editConfig: {
      type: 'select',
      props: {
        'show-clear-button': true,
        dataSource: currentDescList
      }
    },
    cellClass: (params) => {
      return params.data.ratingDimensionCode === 'technical_bonus_points' ? 'cellEditable' : ''
    }
    // valueFormatter: (params) => {
    //   return params.data.ratingDimensionCode === 'technical_bonus_points'
    //     ? currentDescMap(params.value)
    //     : params.value
    // }
  },
  {
    field: 'scoreWeight',
    headerName: i18n.t('权重'),
    width: 120
  },
  {
    field: 'score',
    headerName: i18n.t('得分'),
    width: 120,
    option: 'customEdit',
    editable: (params) => {
      return params.data.scoreMethodCode === 'select_by_myself'
    },
    cellClass: (params) => {
      return params.data.scoreMethodCode === 'select_by_myself' ? 'cellEditable' : ''
    },
    editConfig: {
      type: 'number',
      props: {
        min: 0,
        max: 100,
        precision: 2
      }
    }
  },
  {
    field: 'remark',
    headerName: i18n.t('备注'),
    width: 120,
    editable: true,
    cellClass: 'cellEditable'
  },
  {
    field: 'respUser',
    headerName: i18n.t('责任人'),
    width: 120
  }
]
