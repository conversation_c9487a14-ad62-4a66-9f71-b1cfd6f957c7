<template>
  <!-- 需求采购明细 -->
  <div class="requireWrapper">
    <CustomAgGrid
      ref="CustomAgGrid"
      :toolbar="toolbar"
      :columns="columns"
      :suppress-row-transform="true"
      :row-data="tableData"
      :animate-rows="false"
      :params-type="2"
      :row-height="40"
      :is-row-selectable="isRowSelectable"
      @handleClickToolbar="handleClickToolbar"
      @onGridReady="onGridReady"
      @row-selected="onRowSelected"
      @cell-value-changed="cellValueChanged"
      @refresh="refresh"
      @search="search"
    >
    </CustomAgGrid>
  </div>
</template>
<script>
import CustomAgGrid from '@/components/CustomAgGrid'
import { columns, requireTypeDescList } from './config'
import cellFile from '@/components/AgCellComponents/cellFile'
import cellDescView from '@/components/AgCellComponents/cellDescView'
import { v4 as uuidv4 } from 'uuid'
import { cloneDeep } from 'lodash'
export default {
  name: 'BaseInfoTab',
  inject: ['reload'],
  components: {
    CustomAgGrid,
    // eslint-disable-next-line vue/no-unused-components
    cellFile,
    // eslint-disable-next-line vue/no-unused-components
    cellDescView
  },
  data() {
    return {
      toolbar: [],
      tableData: [],
      agGrid: null,
      columns: null
    }
  },
  props: {
    basicInfo: {
      type: Object,
      default: null
    },
    editable: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    editable: {
      handler() {
        this.initColumns()
      },
      immediate: true
    }
  },
  mounted() {
    this.initTableData()
    // 字典数据
    this.initDictItems()
  },

  methods: {
    // 初始化 - 列表
    initColumns() {
      this.columns = columns.map((item) => {
        let _item = { ...item }
        if (['sourcingFiles', 'supplierSourcingFiles'].includes(item.field)) {
          _item.cellRenderer = 'cellFile'
          _item.editable = false
          _item.cellRendererParams = {
            handleable: item.field === 'sourcingFiles' || !this.editable ? false : this.editable,
            isTransform: true
          }
        }
        if (['requireTypeDesc', 'supplierReplyOpinion'].includes(item.field)) {
          _item.cellRenderer = 'cellDescView'
          _item.cellRendererParams = {
            rfxId: this.$route.query.rfxId
          }
        }
        //如果页面不可编辑，清空编辑选项

        return { ..._item }
      })
    },
    // 初始化 - 字典数据
    async initDictItems() {
      let codeList = [
        { code: 'PAY_METHOD', type: 'string' } // 现有客户群体
      ]
      await this.$API.masterData.getNumDictListAllByCode(codeList).then((res) => {
        if (res.code === 200) {
          this.dictItems = res.data
        }
      })
    },
    isRowSelectable(params) {
      return !['CXZG', 'JYNX', 'ZLTX', 'HPZG', 'XSJE', 'CNYQ'].includes(params.data.requireType)
    },
    // 初始化渲染表格
    async initTableData() {
      let res = await this.$API.publicSourcing.queryRequireList({
        rfxId: this.$route.query?.rfxId,
        supplierTenantId: this.$route.query?.sId
      })
      if (res.code === 200) {
        this.tableData = res.data?.map((item, index) => {
          item.rowId = uuidv4()
          item.lineNo = index + 1
          return item
        })
        this.cacheTableData = cloneDeep(this.tableData)
      }
    },
    handleClickToolbar(args) {
      let _id = args.toolbar.id
      switch (_id) {
        case 'Save':
          this.handleSave() // 保存
          break
      }
    },
    // 保存数据
    async handleSave(isCurrentPage) {
      let _tableData = this.getRowData()
      let _submitData = {
        requireConfigList: _tableData,
        rfxId: this.$route.query?.rfxId,
        supplierTenantId: this.$route.query?.sId
      }
      let res = await this.$API.publicSourcing.reviewSave(_submitData)
      if (!isCurrentPage && res.code === 200) {
        this.$toast({ content: this.$t('保存成功！'), type: 'success' })
      }
      return res
    },
    // 表格- 提交 - 数据处理
    formatSubmitData(data) {
      if (!data) return []
      data.forEach((item) => {
        if ('supplierSourcingFiles' in item || 'sourcingFiles' in item) {
          let _supplierSourcingFiles = JSON.parse(item.supplierSourcingFiles) || []
          item.supplierSourcingFiles = _supplierSourcingFiles
        }
      })
      return data
    },
    // 需求项目联动
    inputRequireType() {
      // 如果寻源需求列表中已经存在【品质要求、技术要求、其它要求】则dataSource中给去掉
    },
    onGridReady(params) {
      this.agGrid = params
    },
    onRowSelected() {},
    cellValueChanged(params) {
      if (params.colDef.field === 'requireType') {
        const rowData = Object.assign({}, params.node.data, {
          requireTypeDesc: requireTypeDescList[params.value]
        })
        params.node.setData(rowData)
      }
    },
    // 表格 - 工具 - 获取表格数据
    getRowData() {
      const rowData = []
      this.agGrid.api.forEachNode(function (node) {
        rowData.push(node.data)
      })
      return rowData
    },
    refresh() {
      this.initTableData()
    },
    search() {}
  }
}
</script>
<style lang="scss">
.requireWrapper {
  flex-grow: 1;
  .grid-container {
    height: 100%;
  }
  .ag-theme-alpine .ag-cell-inline-editing {
    height: 40px !important;
  }
  .mt-input-number {
    width: 100px;
  }
  .mt-input-number .step-box .one-icon {
    display: none;
  }
  .mt-input-number input[type='number'] {
    padding-left: 10px;
    padding-right: 10px;
  }
  .mt-input-number .icon-clear {
    margin-top: 6px;
    right: 10px;
  }
  .mt-input {
    width: 100%;
  }
}
</style>
