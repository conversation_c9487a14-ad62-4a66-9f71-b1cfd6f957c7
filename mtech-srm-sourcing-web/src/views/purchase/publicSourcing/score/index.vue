<template>
  <div class="publicSourcing-full-height">
    <div class="top-container">
      <div class="top-content">
        <div class="top-info">
          <div class="title">
            <div>
              <span>{{ supplierName }}</span>
              <span class="sub-title">{{ supplierCode }}</span>
            </div>
          </div>
          <div class="top-btns">
            <vxe-button
              v-for="(item, index) in detailToolbar"
              v-show="!item.isHidden"
              :key="index"
              :icon="item.icon"
              :status="item.status"
              :disabled="item.disabled"
              size="small"
              @click="handleClickToolBar(item)"
              >{{ item.name }}</vxe-button
            >
          </div>
        </div>
      </div>
    </div>
    <div class="body-container">
      <mt-tabs
        ref="mtTabsRef"
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabList"
        :halt-select="false"
        @handleSelectTab="(index, item) => handleTabChange(index, item)"
        style="background-color: #fff"
      />
      <quotationTab v-if="activeTabCode === 'quotationTab'"></quotationTab>
      <scoreTab
        ref="scoreRef"
        :detail-info="detailInfo"
        :status="status"
        :editable="editable"
        v-show="activeTabCode === 'scoreTab'"
      ></scoreTab>
      <requireTab
        ref="requireRef"
        v-if="activeTabCode === 'requireTab'"
        :editable="editable"
      ></requireTab>
      <cooperationTab
        ref="cooperationRef"
        v-if="activeTabCode === 'cooperationTab'"
        :editable="editable"
      ></cooperationTab>
      <supplierInfoTab
        ref="supplierInfoRef"
        v-if="activeTabCode === 'supplierInfoTab'"
        :editable="editable"
      ></supplierInfoTab>
      <attachmentTab
        ref="attachmentRef"
        v-if="activeTabCode === 'AttachmentTab'"
        :editable="editable"
      ></attachmentTab>
    </div>
  </div>
</template>

<script>
import { Button as VxeButton } from 'vxe-table'
import scoreTab from './tabs/scoreTab' // 入围评分
import quotationTab from './tabs/quotationTab' // 报价明细
import requireTab from './tabs/requireTab' // 寻源要求
import cooperationTab from './tabs/cooperationTab' // 合作承诺
import supplierInfoTab from './tabs/supplierInfoTab' // 供应商附件
import attachmentTab from './tabs/attachmentTab' // 附件
export default {
  name: 'InitiatedDetail',
  inject: ['reload'],
  components: {
    VxeButton,
    scoreTab,
    requireTab,
    attachmentTab,
    cooperationTab,
    supplierInfoTab,
    quotationTab
  },
  data() {
    return {
      rfxCode: '',
      rfxName: '',
      status: 0,
      partcpStatus: 0,
      detailInfo: {},
      activeTabIndex: 0,
      activeTabCode: '',
      dataList: [[], []],
      itemLength: 0,
      type: 'detail',
      isInit: true,
      purchaserSearcValue: null,
      supplierCode: '',
      supplierName: ''
    }
  },
  computed: {
    detailToolbar() {
      const toolbar = [
        {
          code: 'back',
          name: this.$t('返回'),
          status: 'primary'
        },
        {
          code: 'save',
          name: this.$t('保存'),
          status: '',
          isHidden: !this.editable
        }
      ]
      return toolbar
    },

    tabList() {
      let tabs = [
        { title: this.$t('报价明细'), code: 'quotationTab' },
        { title: this.$t('入围评分'), code: 'scoreTab' },
        { title: this.$t('寻源要求'), code: 'requireTab' },
        { title: this.$t('合作承诺'), code: 'cooperationTab' },
        { title: this.$t('企业基本信息'), code: 'supplierInfoTab' },
        { title: this.$t('附件'), code: 'AttachmentTab' }
      ]
      if (this.status === 2) {
        tabs.splice(1, 1)
      } else {
        tabs.splice(0, 1)
      }
      return tabs
    },
    editable() {
      return this.status === 3
    }
  },
  mounted() {
    this.status = this.$route.query?.status
    this.supplierCode = this.$route.query?.supplierCode
    this.supplierName = this.$route.query?.supplierName
    this.activeTabCode = this.status === 2 ? 'quotationTab' : 'scoreTab'
  },
  created() {},
  activted() {},
  methods: {
    // 点击工具栏按钮
    handleClickToolBar(e) {
      switch (e.code) {
        case 'back':
          this.handleBack()
          break
        case 'save':
          this.handleSave()
          break
        default:
          break
      }
    },
    // 切换tab页签
    handleTabChange(index, item) {
      if (this.editable) {
        if (this.activeTabCode === 'scoreTab') {
          this.$refs.scoreRef.handleSave(true)
        } else if (this.activeTabCode === 'requireTab') {
          this.$refs.requireRef.handleSave(true)
        }
      }

      this.$refs.mtTabsRef.activeTab = index
      this.activeTabIndex = index
      this.activeTabCode = item.code
    },
    // 返回
    handleBack() {
      this.$router.go(-1)
    },
    // 保存
    handleSave() {
      if (this.activeTabCode === 'scoreTab') {
        this.$refs.scoreRef.handleSave()
      } else if (this.activeTabCode === 'requireTab') {
        this.$refs.requireRef.handleSave()
      }
    }
  }
}
</script>

<style scoped lang="scss">
.publicSourcing-full-height {
  padding: 8px 8px 0;
  background: #fff;
  height: calc(100% - 20px);
  display: flex;
  flex-direction: column;
}
.publicSourcing-full-height.isBasic {
  height: auto;
}
.body-container {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}
.top-container {
  height: 40px;
  width: 100%;
  .top-content {
    padding: 8px 8px 0 8px;
    // background-color: #f9f9f9;
    border-radius: 8px;
    .top-info {
      display: flex;
      justify-content: space-between;
      .title {
        font-size: 20px;
        font-weight: 700;
        color: #292929;
        span {
          margin-right: 15px;
        }
        .sub-title {
          font-size: 15px;
          color: #777;
        }
      }
      .top-btns {
        text-align: right;
        margin-bottom: 10px;
      }
    }
    .top-form {
      .mt-form {
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
        justify-content: space-between;
        grid-gap: 5px;
      }
      .vxe-input,
      .vxe-select,
      .vxe-pulldown {
        width: 100%;
        height: 34px;
        line-height: 34px;
      }
      /deep/.vxe-pulldown--panel {
        min-width: unset !important;
        width: 100%;
      }
    }
  }
  .top-toggle-btn {
    width: 100%;
    div {
      width: 80px;
      height: 15px;
      line-height: 15px;
      margin: auto;
      text-align: center;
      background-color: #4a556b;
      color: #fff;
      font-weight: bold;
      border-radius: 0 0 6px 6px;
    }
  }
}

.top-hidden {
  .top-form {
    display: none;
  }
  .top-toggle-btn {
    div {
      transform: rotate(180deg);
      border-radius: 6px 6px 0 0;
    }
  }
}

.my-dropdown2 {
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 4px 0;
  max-height: 200px;
  border-radius: 4px;
  border: 1px solid #dadce0;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}
.list-item2 {
  line-height: 30px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 0 4px;

  .isSelected {
    font-weight: 700;
    color: #409eff;
  }
}
.list-item2:hover {
  background-color: #f5f7fa;
}
.empty-tip {
  width: 100%;
  line-height: 30px;
  padding: 0 4px;
  text-align: center;
  color: #c0c4cc;
}

::v-deep {
  .mt-form-item {
    margin-bottom: 15px;
    .label {
      margin: 0 10px 5px 0;
    }
  }
  .mt-tabs-container {
    background-color: #fff;
  }

  .mt-tabs-container ul.tab-container li.tab-item2.active {
    border-bottom: 2px solid #409eff;
  }
  .mt-tabs-container ul.tab-container li.tab-item2.active,
  .mt-tabs-container ul.tab-container li.tab-item2:hover {
    color: #31374e;
  }

  .vxe-textarea--inner {
    min-height: 34px;
    height: 100%;
  }
}
</style>
