<template>
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.status"
            css-class="rule-element"
            :data-source="statusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择状态')"
          />
        </mt-form-item>
        <mt-form-item prop="rfxCode" :label="$t('单据号')" label-style="top">
          <mt-input
            v-model="searchFormModel.rfxCode"
            :show-clear-button="true"
            :placeholder="$t('请输入单据号')"
          />
        </mt-form-item>
        <mt-form-item prop="rfxName" :label="$t('单据名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.rfxName"
            :show-clear-button="true"
            :placeholder="$t('请输入单据名称')"
          />
        </mt-form-item>
        <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
          <mt-select
            v-model="searchFormModel.companyCode"
            css-class="rule-element"
            :data-source="companyList"
            :fields="{ text: 'text', value: 'orgCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择公司')"
            @change="handleCompanyChange"
          />
        </mt-form-item>
        <mt-form-item prop="categoryCode" :label="$t('品类')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.categoryCode"
            url="/sourcing/tenant/permission/queryCategorys"
            :search-fields="['categoryName', 'categoryCode']"
            :fields="{ text: 'categoryCode-categoryName', value: 'categoryCode' }"
            :placeholder="$t('请选择品类')"
          />
        </mt-form-item>
        <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
          <mt-input
            v-model="searchFormModel.createUserName"
            :show-clear-button="true"
            :placeholder="$t('请输入创建人')"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :allow-edit="false"
            :placeholder="$t('请选择创建时间')"
            :open-on-focus="true"
            @change="handleDateChange"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      grid-id="f976dd8f-d024-4a67-8bab-21c7f624b485"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="listColumns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
    <!-- 修改截止时间 -->
    <mt-dialog ref="timeDialog" :buttons="dateButtons" :header="$t('截止时间设置')" size="small">
      <div class="dialog-content">
        <mt-form style="margin-top: 20px">
          <mt-form-item prop="validEndDate" :label="$t('截止时间：')">
            <mt-date-time-picker
              v-model="deadlineDate"
              float-label-type="Never"
              width="100%"
              :placeholder="$t('请选择截止时间')"
            ></mt-date-time-picker>
          </mt-form-item>
        </mt-form>
      </div>
    </mt-dialog>
    <!-- 作废 -->
    <mt-dialog ref="voidDialog" :buttons="buttons" :header="$t('作废')" size="small">
      <div class="dialog-content">
        <mt-form style="margin-top: 20px">
          <mt-form-item prop="abandonReason" :label="$t('作废原因：')">
            <mt-input
              class="ab"
              v-model="abandonReason"
              :multiline="true"
              :rows="20"
              :height="150"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </mt-dialog>
    <!-- 分享 -->
    <mt-dialog ref="shareDialog" :header="$t('扫码分享朋友圈')" size="small" :height="420">
      <div class="dialog-content share-dialog-content" style="text-align: center">
        <img :src="shareImg" />
        <p>{{ scanTips }}</p>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import { getTimeList } from '@/utils/obj'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import XEUtils from 'xe-utils'

export default {
  components: {
    ScTable,
    CollapseSearch,
    RemoteAutocomplete
  },
  data() {
    return {
      companyList: [],
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 20,
        pageSizes: [20, 50, 200, 500, 1000, 2000]
      },
      pageInfo: {
        size: 20,
        current: 1
      },
      statusList: [
        { value: -4, text: this.$t('需求已驳回') },
        { value: -3, text: this.$t('作废审批中') },
        { value: -2, text: this.$t('已作废') },
        { value: -1, text: this.$t('已删除') },
        { value: 0, text: this.$t('草稿') },
        { value: 1, text: this.$t('需求审批中') },
        { value: 2, text: this.$t('已发布') },
        { value: 3, text: this.$t('待评定') },
        { value: 4, text: this.$t('入围审批中') },
        { value: 5, text: this.$t('入围已驳回') },
        { value: 6, text: this.$t('已完成') }
      ],
      toolbar: [
        { code: 'add', name: this.$t('新增'), status: 'info' },
        { code: 'delete', name: this.$t('删除'), status: 'info' }, // 草稿
        { code: 'void', name: this.$t('作废'), status: 'info' }, // 已发布 待评定 需求已驳回 入围已驳回
        { code: 'updateDeclineDate', name: this.$t('修改截止时间'), status: 'info' }, // 草稿
        { code: 'share', name: this.$t('分享'), status: 'info' }
      ],
      searchFormModel: {
        createUserName: JSON.parse(sessionStorage.getItem('userInfo'))?.username || null,
        createTime: getTimeList(3)
      },
      tableData: [],
      loading: false,
      type: 'list',
      buttons: [
        {
          click: this.voidCancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.voidConfirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      dateButtons: [
        {
          click: this.timeCancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.updateDeclineDateConfirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      abandonReason: '',
      deadlineDate: '',
      shareImg: null,
      scanTips: ''
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    // 列配置
    listColumns() {
      return [
        {
          width: 50,
          type: 'checkbox',
          fixed: 'left'
        },
        {
          width: 50,
          field: 'index',
          title: this.$t('序号')
        },
        {
          field: 'status',
          title: this.$t('状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = this.statusList.find((item) => item.value === row.status)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'rfxCode',
          title: this.$t('单据号'),
          minWidth: 180,
          slots: {
            default: ({ row, column }) => {
              return [<a on-click={() => this.handleClickCellTitle(row, column)}>{row.rfxCode}</a>]
            }
          }
        },
        {
          field: 'rfxName',
          title: this.$t('单据名称'),
          minWidth: 140
        },
        {
          field: 'companyCode',
          title: this.$t('公司'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [<span>{row.companyCode + '-' + row.companyName}</span>]
            }
          }
        },
        {
          field: 'categoryCode',
          title: this.$t('品类'),
          minWidth: 200,
          slots: {
            default: ({ row }) => {
              return [<span>{row.categoryCode + '-' + row.categoryName}</span>]
            }
          }
        },
        {
          field: 'deliveryPlaceName',
          title: this.$t('交货地点'),
          minWidth: 200
        },
        {
          field: 'deadlineDate',
          title: this.$t('截止日期'),
          minWidth: 140
        },
        {
          field: 'expectSupplierQty',
          title: this.$t('期望寻源数量'),
          minWidth: 140
        },
        {
          field: 'responseSupplierQty',
          title: this.$t('已响应供方数量'),
          minWidth: 140
          // slots: {
          //   default: ({ row, column }) => {
          //     return [
          //       <div>
          //         <a
          //           v-show={row.oaApproveLink}
          //           on-click={() => this.handleClickCellTitle(row, column)}>
          //           {row.responseSupplierQty}
          //         </a>
          //         <span v-show={!row.responseSupplierQty}>-</span>
          //       </div>
          //     ]
          //   }
          // }
        },
        {
          field: 'remark',
          title: this.$t('备注'),
          minWidth: 140
        },
        // {
        //   field: 'oaApproveLink',
        //   title: this.$t('OA申请单查看'),
        //   minWidth: 120,
        //   slots: {
        //     default: ({ row, column }) => {
        //       return [
        //         <div>
        //           <a
        //             v-show={row.oaApproveLink}
        //             on-click={() => this.handleClickCellTitle(row, column)}>
        //             {this.$t('查看')}
        //           </a>
        //           <span v-show={!row.oaApproveLink}>-</span>
        //         </div>
        //       ]
        //     }
        //   }
        // },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          minWidth: 140
        },
        {
          field: 'remainingDateStr',
          title: this.$t('剩余时间'),
          minWidth: 140
        },
        {
          field: 'hanlder',
          title: this.$t('操作'),
          minWidth: 80,
          fixed: 'right',
          slots: {
            default: ({ row, column }) => {
              return [
                <div>
                  <a
                    style='margin-right:12px;'
                    on-click={() => this.handleClickCellTitle(row, column)}>
                    {this.$t('入围筛选')}
                  </a>
                  {/* <a on-click={() => this.handleClickCellTitle(row, column)}>
                    {this.$t('结果查询')}
                  </a> */}
                </div>
              ]
            }
          }
        }
      ]
    }
  },
  mounted() {
    this.getCompanyList()
    this.handleSearch()
  },
  methods: {
    // 获取公司下拉列表
    async getCompanyList(isInit) {
      const res = await this.$API.masterData.permissionCompanyList()
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.companyList = res.data

        if (isInit) {
          const selectItem = this.companyList?.find(
            (item) => item.orgCode === this.dataForm?.companyCode
          )
          selectItem && this.getPurchaseOrgList(selectItem.id, true)
          !this.dataForm.companyId && (this.dataForm.companyId = selectItem.id)
        }
      }
    },
    // 选择公司
    handleCompanyChange(e) {
      console.log('test lint')
      this.$set(this.searchFormModel, 'purchaseOrgCode', null)
      if (e.itemData.id) {
        this.getPurchaseOrgList(e.itemData.id)
      } else {
        this.purchaseOrgList = []
      }
    },
    // 日期格式化
    handleDateChange(e) {
      if (e.startDate) {
        const startDate = XEUtils.toDateString(e.startDate, 'yyyy-MM-dd') + ' 00:00:00'
        const endDate = XEUtils.toDateString(e.endDate, 'yyyy-MM-dd') + ' 23:59:59'
        this.searchFormModel['createStartTime'] = XEUtils.timestamp(startDate)
        this.searchFormModel['createEndTime'] = XEUtils.timestamp(endDate)
      } else {
        this.searchFormModel['createStartTime'] = null
        this.searchFormModel['createEndTime'] = null
      }
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    // 查询
    async handleSearch(type) {
      // 解决修改查询条件后，查询结果有数据但未正常显示的问题（type: pageChange/refresh）
      if (!type) {
        this.pageInfo.current = 1
        this.$refs.pageRef.jumpNum = 1
        this.$refs.pageRef.currentPage = 1
      }
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      delete params.submitTime
      this.loading = true
      const res = await this.$API.publicSourcing
        .queryInitiatedList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.tableData?.forEach((item, index) => {
          item.index = index + 1
        }) || []
        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (['delete'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      if (['void', 'updateDeclineDate', 'share'].includes(e.code)) {
        if (selectedRecords?.length !== 1) {
          this.$toast({ content: this.$t('请选择一条数据！'), type: 'warning' })
          return
        }
      }

      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete': // 删除
          this.handleDelete(selectedRecords)
          break
        case 'void': // 作废
          this.handleVoid()
          break
        case 'updateDeclineDate': // 修改截止时间
          this.handleUpdateDeclineDate()
          break
        case 'share': // 分享
          this.handleShare(selectedRecords)
          break
        default:
          break
      }
    },
    // 点击单元格
    handleClickCellTitle(row, column) {
      if (column.field === 'rfxCode') {
        this.$router.push({
          name: 'public-sourcing-initiated-detail',
          query: {
            id: row.id,
            refreshId: Date.now()
          }
        })
      } else if (column.field === 'hanlder') {
        if (row?.status !== 3) {
          this.$toast({ content: this.$t('只能查看待评定数据'), type: 'warning' })
          return
        }
        this.$router.push({
          name: 'public-sourcing-initiated-detail',
          query: {
            id: row.id,
            refreshId: Date.now()
          }
        })
      }
    },
    // 新增
    handleAdd() {
      this.$router.push({
        name: 'public-sourcing-initiated-detail',
        query: {
          refreshId: Date.now()
        }
      })
    },
    // 删除
    handleDelete(list) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认删除选中的数据？`)
        },
        success: async () => {
          const ids = []
          list.forEach((item) => ids.push(item.id))
          const res = await this.$API.publicSourcing.deleteInitiatedList(ids)
          if (res.code === 200) {
            this.$toast({ content: this.$t(`删除成功！`), type: 'success' })
            this.handleSearch()
          }
        }
      })
    },

    // 作废
    handleVoid() {
      this.$refs.voidDialog.ejsRef.show()
    },
    // 作废确认
    async voidConfirm() {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const res = await this.$API.publicSourcing.voidInitiatedList({
        id: selectedRecords[0]?.id,
        abandonReason: this.abandonReason
      })
      if (res.code === 200) {
        this.$toast({ content: this.$t(`作废成功`), type: 'success' })
        this.handleSearch()
        this.$refs.voidDialog.ejsRef.hide()
      }
    },
    // 修改截止时间
    handleUpdateDeclineDate() {
      this.$refs.timeDialog.ejsRef.show()
    },
    // 修改截止时间确认
    async updateDeclineDateConfirm() {
      if (!this.deadlineDate) {
        this.$toast({ content: this.$t('请选择截止日期！'), type: 'warning' })
        return
      }

      const selectedRecords = this.tableRef.getCheckboxRecords()
      const res = await this.$API.publicSourcing.updateDeclineDate({
        id: selectedRecords[0]?.id,
        deadlineDate: new Date(this.deadlineDate).getTime()
      })
      if (res.code === 200) {
        this.$toast({ content: this.$t(`提交成功`), type: 'success' })
        this.handleSearch()
        this.$refs.timeDialog.ejsRef.hide()
      }
    },
    // 截止时间取消
    timeCancel() {
      this.$refs.timeDialog.ejsRef.hide()
    },
    // 作废取消
    voidCancel() {
      this.$refs.voidDialog.ejsRef.hide()
    },
    // 分享
    async handleShare(selectedRecords) {
      if (selectedRecords[0]?.status < 2) {
        this.$toast({ content: this.$t('只能分享已经发布的的数据！'), type: 'warning' })
        return
      }
      this.$store.commit('startLoading')
      const res = await this.$API.publicSourcing.getSharePicture({
        rfxId: selectedRecords[0]?.id
      })
      if (res.code === 200) {
        this.$store.commit('endLoading')
        this.scanTips = this.$t(
          `扫描二维码生成"${selectedRecords[0].rfxName}"海报信息，可分享海报至朋友圈`
        )
        this.$refs.shareDialog.ejsRef.show()
        this.shareImg = res.data
      }
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.handleSearch('pageChange')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.handleSearch()
    }
  }
}
</script>
<style lang="scss">
.share-dialog-content {
  p {
    font-size: 18px;
    padding: 0 80px;
    margin-top: -15px;
  }
}
</style>
<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
