<template>
  <div class="publicSourcing-full-height" :class="{ isBasic: activeTabCode === 'baseInfo' }">
    <div class="top-container">
      <div class="top-content">
        <div class="top-info">
          <div class="title">
            <div>
              <span>{{ rfxCode }}</span>
              <span class="sub-title" :title="rfxName">{{ rfxName }}</span>
            </div>
          </div>
          <div class="top-btns">
            <vxe-button
              v-for="(item, index) in detailToolbar"
              v-show="!item.isHidden"
              :key="index"
              :icon="item.icon"
              :status="item.status"
              :disabled="item.disabled"
              size="small"
              @click="handleClickToolBar(item)"
              >{{ item.name }}</vxe-button
            >
          </div>
        </div>
      </div>
      <topStep v-if="status >= 0" :status="status" />
    </div>
    <div class="body-container">
      <mt-tabs
        ref="mtTabsRef"
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabList"
        :halt-select="false"
        @handleSelectTab="(index, item) => handleTabChange(index, item)"
        style="background-color: #fff"
      />
      <evaluateTab
        ref="evaluateRef"
        :detail-info="detailInfo"
        :status="status"
        v-if="activeTabCode === 'evaluate'"
      ></evaluateTab>
      <baseInfoTab
        ref="baseInfoRef"
        :detail-info="detailInfo"
        :status="status"
        v-show="activeTabCode === 'baseInfo'"
      ></baseInfoTab>
      <requireTab ref="requireRef" v-if="activeTabCode === 'require'" :status="status"></requireTab>
      <logTab ref="logRef" v-if="activeTabCode === 'log'"></logTab>
      <attachmentTab
        ref="attachmentRef"
        v-if="activeTabCode === 'attachment'"
        :status="status"
      ></attachmentTab>
    </div>
    <!-- 作废 -->
    <mt-dialog ref="voidDialog" :buttons="buttons" :header="$t('作废')" size="small">
      <div class="dialog-content">
        <mt-form style="margin-top: 20px">
          <mt-form-item prop="abandonReason" :label="$t('作废原因：')">
            <mt-input
              class="ab"
              v-model="abandonReason"
              :multiline="true"
              :rows="20"
              :height="150"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import topStep from './components/topStep'
import { Button as VxeButton } from 'vxe-table'
import evaluateTab from './tabs/evaluateTab' // 入围评定
import baseInfoTab from './tabs/baseInfoTab' // 基本信息
import requireTab from './tabs/requireTab' // 寻源要求
import logTab from './tabs/logTab' // 日志
import attachmentTab from './tabs/attachmentTab' // 附件
import { cloneDeep } from 'lodash'
export default {
  name: 'InitiatedDetail',
  components: {
    VxeButton,
    topStep,
    evaluateTab,
    baseInfoTab,
    requireTab,
    logTab,
    attachmentTab
  },
  data() {
    return {
      rfxCode: '',
      rfxName: '',
      status: 0,
      detailInfo: {},
      activeTabIndex: 0,
      activeTabCode: 'baseInfo',
      dataList: [[], []],
      itemLength: 0,
      type: 'detail',
      isInit: true,
      purchaserSearcValue: null,
      abandonReason: '',
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.voidConfirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  computed: {
    /**
     *  -4: 需求已驳回
        -3: 作废审批中
        -2: 已作废
        -1: 已删除
        0:  草稿
        1:  需求审批中
        2:  已发布
        3:  待评定
        4:  入围审批中
        5:  入围已驳回
        6:  已完
     */
    detailToolbar() {
      const toolbar = [
        {
          code: 'back',
          name: this.$t('返回'),
          status: 'primary'
        },
        {
          code: 'save',
          name: this.$t('保存'),
          status: '',
          isHidden: this.status > 0
        },
        {
          code: 'submitOA',
          name: this.$t('提交审批'),
          status: '',
          isHidden: this.status > 0 && this.status != 3
        },
        {
          code: 'void',
          name: this.$t('作废'),
          status: '',
          isHidden: ![-4, 2, 5].includes(this.status)
        },
        {
          code: 'viewOA',
          name: this.$t('OA审批进度'),
          status: '',
          isHidden: ![-3, 1, 4].includes(this.status)
        }
      ]
      return toolbar
    },

    tabList() {
      let tabs = [
        { title: this.$t('寻源基本信息'), code: 'baseInfo' },
        { title: this.$t('寻源要求'), code: 'require' },
        { title: this.$t('操作日志'), code: 'log' },
        { title: this.$t('附件'), code: 'attachment' }
      ]
      return tabs
    }
  },
  mounted() {},
  created() {
    this.init()
  },
  methods: {
    // 初始化
    init() {
      // 如果存在id 则请求
      if (this.$route.query?.id) {
        this.queryBaseInfo()
      }
    },
    // 获取详情页数据
    async queryBaseInfo() {
      const res = await this.$API.publicSourcing.queryBaseInfo({ id: this.$route.query?.id })
      if (res.code !== 200) return
      this.detailInfo = cloneDeep(res.data)
      const { rfxCode, rfxName, status } = res.data
      // 渲染基本信息
      this.rfxCode = rfxCode
      this.rfxName = rfxName
      this.status = status
      if (this.status >= 2) {
        // 如果status>=2（寻源需求发布后）时，添加入围评定
        this.tabList.unshift({ title: this.$t('入围评定'), code: 'evaluate' })
        this.activeTabCode = 'evaluate'
      }
    },
    // 点击工具栏按钮
    handleClickToolBar(e) {
      switch (e.code) {
        case 'back':
          this.handleBack()
          break
        case 'save':
          this.handleSave()
          break
        case 'void':
          this.handleVoid()
          break
        case 'submitOA':
          this.handleSubmit(e.code)
          break
        case 'viewOA':
          this.handleViewOa()
          break
        default:
          break
      }
    },
    // 切换tab页签
    handleTabChange(index, item) {
      if (this.$refs.mtTabsRef.activeTab === 0 && !this.$route.query?.id) {
        this.$toast({ content: this.$t('请先保存寻源基本信息！'), type: 'warning' })
        return false
      }
      if (this.activeTabCode === 'require') {
        this.$refs.requireRef.handleSave(true) // 切换寻源要求保存
      }
      this.$refs.mtTabsRef.activeTab = index
      this.activeTabIndex = index
      this.activeTabCode = item.code
    },
    // 返回
    handleBack() {
      this.$router.push({
        name: 'public-sourcing-initiated',
        query: {
          refreshId: Date.now()
        }
      })
    },
    // 保存
    async handleSave() {
      if (this.activeTabCode === 'require') {
        this.$refs.requireRef.handleSave()
      } else {
        // 基础信息保留
        this.$refs.baseInfoRef.handleSave()
      }
    },
    // 作废
    handleVoid() {
      this.$refs.voidDialog.ejsRef.show()
    },
    // 作废确认
    async voidConfirm() {
      const res = await this.$API.publicSourcing.voidInitiatedList({
        id: this.$route.query?.id,
        abandonReason: this.abandonReason
      })
      if (res.code === 200) {
        this.$toast({ content: this.$t(`作废成功`), type: 'success' })
        this.$refs.voidDialog.ejsRef.hide()
        // 返回列表页
        this.handleBack()
      }
    },
    // 提交
    async handleSubmit() {
      if (this.status === 3) {
        // 入围评定提交审批
        this.$refs.evaluateRef.handleSubmit()
      } else {
        if (this.activeTabCode === 'require') {
          this.$refs.requireRef.handleSave(true).then((res) => {
            if (res.code === 200) this.$refs.baseInfoRef.handleSubmit()
          })
        } else {
          this.$refs.baseInfoRef.handleSubmit()
        }
      }
    },

    // oa审批
    async handleViewOa() {
      const res = await this.$API.publicSourcing.getOaLink({
        rfxId: this.$route.query?.id
      })
      if (res.code === 200) {
        window.open(res.data, '_blank')
      }
    },
    // 弹框展示
    async showDialog(message, cssClass) {
      return new Promise((resolve) => {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t(message),
            cssClass
          },
          success: resolve
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.publicSourcing-full-height {
  padding: 8px 8px 0;
  background: #fff;
  height: calc(100% - 20px);
  display: flex;
  flex-direction: column;
}
.publicSourcing-full-height.isBasic {
  height: auto;
}
.body-container {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}
.top-container {
  height: 95px;
  width: 100%;
  .top-content {
    padding: 8px 8px 0 8px;
    // background-color: #f9f9f9;
    border-radius: 8px;
    .top-info {
      display: flex;
      justify-content: space-between;
      .title {
        max-width: 80%;
        font-size: 20px;
        font-weight: 700;
        color: #292929;
        span {
          margin-right: 15px;
        }
        .sub-title {
          display: inline-block;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 75%;
          font-size: 15px;
          color: #777;
        }
      }
      .top-btns {
        text-align: right;
        margin-bottom: 10px;
      }
    }
    .top-form {
      .mt-form {
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
        justify-content: space-between;
        grid-gap: 5px;
      }
      .vxe-input,
      .vxe-select,
      .vxe-pulldown {
        width: 100%;
        height: 34px;
        line-height: 34px;
      }
      /deep/.vxe-pulldown--panel {
        min-width: unset !important;
        width: 100%;
      }
    }
  }
  .top-toggle-btn {
    width: 100%;
    div {
      width: 80px;
      height: 15px;
      line-height: 15px;
      margin: auto;
      text-align: center;
      background-color: #4a556b;
      color: #fff;
      font-weight: bold;
      border-radius: 0 0 6px 6px;
    }
  }
}

.top-hidden {
  .top-form {
    display: none;
  }
  .top-toggle-btn {
    div {
      transform: rotate(180deg);
      border-radius: 6px 6px 0 0;
    }
  }
}

.my-dropdown2 {
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 4px 0;
  max-height: 200px;
  border-radius: 4px;
  border: 1px solid #dadce0;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}
.list-item2 {
  line-height: 30px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 0 4px;

  .isSelected {
    font-weight: 700;
    color: #409eff;
  }
}
.list-item2:hover {
  background-color: #f5f7fa;
}
.empty-tip {
  width: 100%;
  line-height: 30px;
  padding: 0 4px;
  text-align: center;
  color: #c0c4cc;
}

::v-deep {
  .mt-form-item {
    margin-bottom: 15px;
    .label {
      margin: 0 10px 5px 0;
    }
  }
  .mt-tabs-container {
    background-color: #fff;
  }

  .mt-tabs-container ul.tab-container li.tab-item2.active {
    border-bottom: 2px solid #409eff;
  }
  .mt-tabs-container ul.tab-container li.tab-item2.active,
  .mt-tabs-container ul.tab-container li.tab-item2:hover {
    color: #31374e;
  }

  .vxe-textarea--inner {
    min-height: 34px;
    height: 100%;
  }
}
</style>
