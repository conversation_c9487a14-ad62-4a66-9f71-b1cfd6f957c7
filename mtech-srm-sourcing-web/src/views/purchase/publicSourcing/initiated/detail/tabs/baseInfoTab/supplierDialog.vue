<template>
  <!-- 供应商弹框 -->
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-local-template-page ref="templateRef" :template-config="pageConfig">
        <template v-slot:quick-search-form>
          <div class="custom-form-box">
            <mt-form ref="searchFormRef" :model="searchFormModel">
              <mt-form-item prop="supplierCode" :label="$t('供应商代码')">
                <mt-input v-model="searchFormModel.supplierCode" show-clear-button></mt-input>
              </mt-form-item>
              <mt-form-item prop="fuzzyNameOrCode" :label="$t('供应商名称')">
                <mt-input v-model="searchFormModel.fuzzyNameOrCode" show-clear-button></mt-input>
              </mt-form-item>
              <mt-form-item prop="companyCode" :label="$t('公司编码')">
                <mt-input v-model="searchFormModel.companyCode" show-clear-button></mt-input>
              </mt-form-item>
              <mt-form-item prop="companyName" :label="$t('公司名称')">
                <mt-input v-model="searchFormModel.companyName" show-clear-button></mt-input>
              </mt-form-item>
              <mt-form-item prop="categoryCode" :label="$t('品类编码')">
                <mt-input v-model="searchFormModel.categoryCode" show-clear-button></mt-input>
              </mt-form-item>
              <mt-form-item prop="categoryName" :label="$t('品类名称')">
                <mt-input v-model="searchFormModel.categoryName" show-clear-button></mt-input>
              </mt-form-item>
            </mt-form>
          </div>
        </template>
      </mt-local-template-page>
    </div>
  </mt-dialog>
</template>

<script>
import MtLocalTemplatePage from '@/components/template-page'
import { pageConfig } from './config/supplierDialog'
export default {
  components: {
    MtLocalTemplatePage
  },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      pageConfig: [],
      searchFormModel: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.pageConfig = pageConfig(this)
  },
  methods: {
    confirm() {
      const selectedRecords = this.$refs.templateRef
        .getCurrentTabRef()
        .gridRef.getMtechGridRecords()
      if (selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      this.$emit('confirm-function', selectedRecords)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  height: 100%;
  width: 100%;
}
</style>
