<template>
  <!-- 需求采购明细 -->
  <div class="requireWrapper">
    <CustomAgGrid
      ref="CustomAgGrid"
      :toolbar="toolbar"
      :columns="columns"
      :suppress-row-transform="true"
      :row-data="tableData"
      :animate-rows="false"
      :params-type="2"
      :row-height="40"
      :is-row-selectable="isRowSelectable"
      @handleClickToolbar="handleClickToolbar"
      @onGridReady="onGridReady"
      @cell-value-changed="cellValueChanged"
      @refresh="refresh"
      @search="search"
    >
    </CustomAgGrid>
  </div>
</template>
<script>
import CustomAgGrid from '@/components/CustomAgGrid'
import { columns, toolbar, requireTypeList, requireTypeDescList } from './config'
import cellFile from '@/components/AgCellComponents/cellFile'
import cellDescView from '@/components/AgCellComponents/cellDescView'
import { v4 as uuidv4 } from 'uuid'
import { cloneDeep } from 'lodash'
export default {
  name: 'BaseInfoTab',
  inject: ['reload'],
  components: {
    CustomAgGrid,
    // eslint-disable-next-line vue/no-unused-components
    cellFile,
    // eslint-disable-next-line vue/no-unused-components
    cellDescView
  },
  data() {
    return {
      tableData: [],
      agGrid: null
    }
  },
  props: {
    status: {
      type: [String, Number],
      default: 0
    }
  },
  computed: {
    editable() {
      return this.status <= 0
    },
    toolbar() {
      return this.editable ? toolbar : []
    },
    columns() {
      let requireTypeData = this.tableData.map((item) => item.requireType)
      return columns().map((item) => {
        if (item.field === 'requireType') {
          item.editConfig = {
            type: 'select',
            props: {
              'show-clear-button': true,
              dataSource: requireTypeList.filter((item) => !requireTypeData.includes(item.value))
            }
          }
        } else if (item.field === 'sourcingFiles') {
          item.cellRenderer = 'cellFile'
          item.editable = false
          item.cellRendererParams = {
            handleable: this.editable
          }
        } else if (item.field === 'requireTypeDesc') {
          item.cellRenderer = 'cellDescView'
          item.cellRendererParams = {
            rfxId: this.$route.query.id
          }
        }
        if (!this.editable) {
          item.editable = false
          item.cellClass = ''
        }
        return item
      })
    }
  },

  watch: {},
  mounted() {
    this.initTableData()
  },
  methods: {
    isRowSelectable(params) {
      return !['CXZG', 'JYNX', 'ZLTX', 'HPZG', 'XSJE', 'CNYQ'].includes(params.data.requireType)
    },
    // 初始化渲染表格
    async initTableData() {
      let res = await this.$API.publicSourcing.queryRequireList({ rfxId: this.$route.query?.id })
      if (res.code === 200) {
        this.tableData = res.data?.map((item, index) => {
          item.rowId = uuidv4()
          item.lineNo = index + 1
          item.sourcingFiles = item.sourcingFiles?.length
            ? JSON.stringify(item.sourcingFiles)
            : item.sourcingFiles
          return item
        })
        this.cacheTableData = cloneDeep(this.tableData)
      }
    },

    handleClickToolbar(args) {
      let _id = args.toolbar.id
      switch (_id) {
        case 'Add':
          this.handleAdd() // 新增
          break
        case 'Delete':
          this.handleDelete() // 删除
          break
      }
    },

    handleAdd() {
      // 基本信息校验
      let requireTypeData = this.tableData.map((item) => item.requireType)
      let hasFixItem = requireTypeList.filter((item) => !requireTypeData.includes(item.value))
      if (hasFixItem.length <= 0) {
        this.$toast({
          content: this.$t('技术要求、品质要求、其它要求已添加,请勿重复添加'),
          type: 'warning'
        })
        return
      }
      let hasNullItem = this.tableData.filter((item) => !item.requireType)
      if (hasNullItem.length) {
        this.$toast({
          content: this.$t('请先完善需求项目信息再添加'),
          type: 'warning'
        })
        return
      }
      this.tableData = this.getRowData()
      this.tableData.push({ rowId: uuidv4() })
    },
    handleDelete() {
      const rowSelections = this.agGrid.api.getSelectedRows()
      const selctionsArr = rowSelections.map((item) => item.rowId)
      const _tableData = this.getRowData()
      this.tableData = _tableData.filter((item) => !selctionsArr.includes(item.rowId))
    },
    // 保存数据
    async handleSave(noTips) {
      let _tableData = this.getRowData()
      _tableData = this.formatSubmitData(_tableData)
      let res = await this.$API.publicSourcing.saveRequireList({
        requireConfigDTOS: _tableData,
        rfxId: this.$route.query?.id
      })
      if (res.code === 200) {
        if (!noTips) {
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
        }
        this.initTableData()
        return res
      }
      return null
    },
    onGridReady(params) {
      this.agGrid = params
    },
    cellValueChanged(params) {
      if (params.colDef.field === 'requireType') {
        const rowData = Object.assign({}, params.node.data, {
          requireTypeDesc: requireTypeDescList[params.value]
        })
        params.node.setData(rowData)
      }
    },
    // 表格 - 工具 - 获取表格数据
    getRowData() {
      const rowData = []
      this.agGrid.api.forEachNode(function (node) {
        rowData.push(node.data)
      })
      return rowData
    },
    // 表格- 提交 - 数据处理
    formatSubmitData(data) {
      if (!data) return []
      data.forEach((item) => {
        if ('sourcingFiles' in item) {
          let _sourcingFiles = JSON.parse(item.sourcingFiles) || []
          _sourcingFiles.forEach((file) => {
            if (!file.syFileId) {
              file.sysFileId = file.id
              delete file.id
            }
          })
          item.sourcingFiles = _sourcingFiles
        }
      })
      return data
    },
    refresh() {
      this.tableData = cloneDeep(this.cacheTableData)
    },
    search() {}
  }
}
</script>
<style lang="scss">
.requireWrapper {
  flex-grow: 1;
  .grid-container {
    height: 100%;
  }
  .ag-theme-alpine .ag-cell-inline-editing {
    height: 40px !important;
  }
  .mt-input-number {
    width: 100px;
  }
  .mt-input-number .step-box .one-icon {
    display: none;
  }
  .mt-input-number input[type='number'] {
    padding-left: 10px;
    padding-right: 10px;
  }
  .mt-input-number .icon-clear {
    margin-top: 6px;
    right: 10px;
  }
  .mt-input {
    width: 100%;
  }
}
</style>
