<template>
  <!-- 需求采购明细 -->
  <div class="purchaseItem">
    <CustomAgGrid
      ref="CustomAgGrid"
      :toolbar="toolbar"
      :columns="columns"
      :suppress-row-transform="true"
      :row-data="tableData"
      :animate-rows="false"
      :params-type="2"
      @handleClickToolbar="handleClickToolbar"
      @onGridReady="onGridReady"
      @refresh="refresh"
    >
    </CustomAgGrid>
  </div>
</template>
<script>
import CustomAgGrid from '@/components/CustomAgGrid'
import { columns, toolbar } from './config/purchaseItemTable'
import cellFile from '@/components/AgCellComponents/cellFile'
import { v4 as uuidv4 } from 'uuid'
import { cloneDeep } from 'lodash'
export default {
  name: 'BaseInfoTab',
  components: {
    CustomAgGrid,
    // eslint-disable-next-line vue/no-unused-components
    cellFile
  },
  data() {
    return {
      tableData: [],
      cacheTableData: [],
      agGrid: null
    }
  },
  computed: {
    toolbar() {
      return this.editable ? toolbar : []
    },
    columns() {
      return columns.map((item) => {
        const _item = { ...item }
        if (_item.field === 'sourcingFiles') {
          _item.cellRenderer = 'cellFile'
          _item.cellRendererParams = {
            handleable: this.editable,
            isTransform: true
          }
          _item.editable = false
        }
        if (!this.editable) {
          _item.editable = false
          _item.cellClass = ''
        }
        return { ..._item }
      })
    }
  },
  props: {
    // 初始化数据
    dataSource: {
      type: Array,
      default: () => []
    },
    basicInfo: {
      type: Object,
      default: null
    },
    editable: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    dataSource: {
      handler(val) {
        val.length && this.initTableData()
      }
    }
  },
  methods: {
    // 初始化渲染表格
    initTableData() {
      this.tableData = this.dataSource.map((item, index) => {
        item.lineNo = index + 1
        item.rowId = uuidv4()
        return item
      })
      this.cacheTableData = cloneDeep(this.tableData)
    },
    handleClickToolbar(args) {
      let _id = args.toolbar.id
      switch (_id) {
        case 'Add':
          this.handleAdd() // 新增
          break
        case 'Delete':
          this.handleDelete() // 删除
          break
      }
    },

    handleAdd() {
      // 基本信息校验
      if (!this.basicInfo?.categoryCode || !this.basicInfo?.companyCode) {
        this.$toast({ content: this.$t('请先选择品类和需求公司！'), type: 'warning' })
        return
      }
      const { categoryCode, companyCode } = this.basicInfo
      this.$dialog({
        modal: () => import('./itemDialog.vue'),
        data: {
          title: this.$t('价格记录'),
          categoryCode,
          companyCode
        },
        success: async (list) => {
          let _list = list.map((item, index) => {
            return {
              rowId: uuidv4(),
              lineNo: index + 1,
              itemCode: item.itemCode,
              itemName: item.itemName,
              unitCode: item.unitCode,
              priceRecordCode: item.priceRecordCode,
              priceUnit: item.priceUnit,
              untaxedUnitPrice: item.untaxedPrice
            }
          })
          this.tableData = [...this.tableData, ..._list]
        }
      })
    },
    handleDelete() {
      const rowSelections = this.agGrid.api.getSelectedRows()
      if (!rowSelections?.length) {
        this.$toast({
          content: this.$t('请先选择一行'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认删除选中的数据？`)
        },
        success: async () => {
          const selctionsArr = rowSelections.map((item) => item.rowId)
          const _tableData = cloneDeep(this.tableData)
          this.tableData = _tableData.filter((item) => !selctionsArr.includes(item.rowId))
        }
      })
    },
    onGridReady(params) {
      this.agGrid = params
    },
    refresh() {
      this.tableData = cloneDeep(this.cacheTableData)
    },
    // 清空数据
    clearData() {
      this.tableData = []
    }
  }
}
</script>
<style lang="scss" scoped>
.purchaseItem {
  height: 300px;
  .grid-container {
    height: 100%;
  }
}
</style>
