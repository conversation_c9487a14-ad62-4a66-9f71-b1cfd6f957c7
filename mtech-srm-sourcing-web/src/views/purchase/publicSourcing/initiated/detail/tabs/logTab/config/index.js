import { i18n } from '@/main.js'
// 部品编码、部品名称、模具编码、模具类型、规划量、实际分摊量
export const columnData = [
  {
    width: '150',
    field: 'operateTypeName',
    headerText: i18n.t('操作类型')
  },
  {
    width: '150',
    field: 'operateTypeCnt',
    headerText: i18n.t('操作内容'),
    valueAccessor: (field, data) => {
      return data.operateTypeName
    }
  },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('操作人')
  },
  {
    width: '150',
    field: 'createTime',
    ignore: true,
    headerText: i18n.t('操作时间')
  },
  {
    width: '300',
    field: 'remark',
    ignore: true,
    headerText: i18n.t('备注')
  }
]
