<template>
  <div class="log-wrapper">
    <mt-local-template-page ref="tepPage" :hidden-tabs="true" :template-config="pageConfig">
    </mt-local-template-page>
  </div>
</template>

<script>
import MtLocalTemplatePage from '@/components/template-page'
import { columnData } from './config'
export default {
  components: {
    MtLocalTemplatePage
  },
  data() {
    return {
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false,
            tools: [[], ['Refresh', 'Setting']]
          },
          gridId: '7770c823-be51-4ed3-b7a5-ec94ac00dfb5',
          grid: {
            allowFiltering: false,
            columnData: columnData,
            allowSorting: false,
            allowSelection: true,
            selectionSettings: {
              type: 'Multiple',
              mode: 'Row'
            },
            asyncConfig: {
              url: '/sourcing/tenant/public/OperationLog/queryPage',
              params: {
                dataId: this.$route.query?.id
              }
            }
          }
        }
      ]
    }
  },

  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
.log-wrapper {
  flex-grow: 1;
}
</style>
