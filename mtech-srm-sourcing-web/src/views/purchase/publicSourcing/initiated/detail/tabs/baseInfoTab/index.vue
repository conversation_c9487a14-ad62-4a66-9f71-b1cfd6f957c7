<template>
  <div class="flex-full-height">
    <!-- 基本信息 -->
    <collapse :title="$t('基本信息')">
      <div slot="content" class="form-content">
        <mt-form ref="dataFormRef" :model="dataForm" :rules="formRules">
          <mt-form-item prop="rfxCode" :label="$t('寻源单号')" label-style="top">
            <vxe-input v-model="dataForm.rfxCode" disabled />
          </mt-form-item>
          <mt-form-item prop="rfxName" :label="$t('寻源标题')" label-style="top">
            <vxe-input
              v-model="dataForm.rfxName"
              maxlength="200"
              :disabled="!editable"
              clearable
              :placeholder="$t('请输入寻源标题')"
            />
          </mt-form-item>
          <mt-form-item prop="sourceMethod" :label="$t('寻源方式')" label-style="top">
            <vxe-select
              v-model="dataForm.sourceMethod"
              :options="sourceMethodList"
              :disabled="!editable"
              :placeholder="$t('请选择寻源方式')"
            />
          </mt-form-item>
          <mt-form-item prop="deadlineDate" :label="$t('报名截止日期')" label-style="top">
            <vxe-input
              type="datetime"
              v-model="dataForm.deadlineDate"
              :disabled="!editable"
              :disabled-method="disableDates"
              value-format="yyyy-MM-dd HH:mm"
              :placeholder="$t('请选择报名截止日期')"
            />
          </mt-form-item>
          <mt-form-item prop="categoryCode" :label="$t('品类')">
            <RemoteSelect
              v-model="dataForm.categoryCode"
              :disabled="!editable"
              url="/sourcing/tenant/permission/queryCategorys"
              :search-fields="['categoryName', 'categoryCode']"
              :fields="{ text: 'categoryCode-categoryName', value: 'categoryCode' }"
              @change="(e) => handleSelectChange(e, 'category')"
              :is-loaded="isLoaded"
              :default-search-text="categoryCode"
            ></RemoteSelect>
          </mt-form-item>
          <mt-form-item prop="companyCode" :label="$t('需求公司')" label-style="top">
            <vxe-select
              v-model="dataForm.companyCode"
              :disabled="!editable"
              :options="companyList"
              :option-props="{ label: 'text', value: 'orgCode' }"
              clearable
              filterable
              :placeholder="$t('请选择需求公司')"
              @change="(e) => handleSelectChange(e, 'company')"
            />
          </mt-form-item>
          <mt-form-item prop="payMethodCode" :label="$t('付款方式')" label-style="top">
            <vxe-select
              v-model="dataForm.payMethodCode"
              :disabled="!editable"
              filterable
              :options="dictItems.payMethod"
              :option-props="{ label: 'dictName', value: 'dictCode' }"
              :placeholder="$t('请选择付款方式')"
              @change="(e) => handleSelectChange(e, 'payMethod')"
            />
          </mt-form-item>
          <mt-form-item prop="paymentTermsCode" :label="$t('付款条件')" label-style="top">
            <vxe-select
              v-model="dataForm.paymentTermsCode"
              :disabled="!editable"
              :options="paymentTermsList"
              :option-props="{ label: 'paymentTermsName', value: 'paymentTermsCode' }"
              :placeholder="$t('请选择付款条件')"
              @change="(e) => handleSelectChange(e, 'paymentTerms')"
            />
          </mt-form-item>
          <mt-form-item prop="deliveryPlaceList" :label="$t('交货地址')" label-style="top">
            <vxe-select
              v-model="dataForm.deliveryPlaceList"
              :disabled="!editable"
              :options="dictItems.PUBLIC_SOURCING_DELIVERY_PLACE"
              :option-props="{ label: 'dictName', value: 'dictCode' }"
              :multi-char-overflow="
                dataForm.deliveryPlaceList && dataForm.deliveryPlaceList.length > 1 ? 8 : -1
              "
              clearable
              filterable
              multiple
              :placeholder="$t('请选择交货地址')"
              :title="deliveryPlaceTitle"
            />
          </mt-form-item>
          <mt-form-item prop="cycleTime" :label="$t('交货周期（天）')" label-style="top">
            <vxe-input
              type="integer"
              :min="1"
              :maxlength="8"
              v-model="dataForm.cycleTime"
              :disabled="!editable"
              clearable
              :placeholder="$t('请输入交货周期')"
            />
          </mt-form-item>
          <mt-form-item prop="expectSupplierQty" :label="$t('期望寻源数量')" label-style="top">
            <vxe-input
              type="integer"
              :min="1"
              :maxlength="8"
              v-model="dataForm.expectSupplierQty"
              :disabled="!editable"
              clearable
              :placeholder="$t('请输入期望寻源数量')"
            />
          </mt-form-item>
          <mt-form-item prop="sourcingFiles" :label="$t('图片上传（部品图片）')" label-style="top">
            <UploadInput
              ref="uploader"
              v-model="dataForm.sourcingFiles"
              :placeholder="$t('请上传部品图片')"
              save-url="/file/user/file/publicUpload?useType=2&businessCode=13"
              :is-image="true"
              :disabled="!editable"
            ></UploadInput>
          </mt-form-item>
          <mt-form-item prop="reasonCode" :label="$t('寻源原因')" label-style="top">
            <vxe-select
              v-model="dataForm.reasonCode"
              :disabled="!editable"
              :options="dictItems.PUBLIC_SOURCING_REASON"
              :option-props="{ label: 'dictName', value: 'dictCode' }"
              :placeholder="$t('请选择寻源原因')"
            />
          </mt-form-item>
          <mt-form-item prop="reasonSpecificDesc" :label="$t('具体原因描述')" label-style="top">
            <vxe-textarea
              v-model="dataForm.reasonSpecificDesc"
              :disabled="!editable"
              maxlength="1000"
              clearable
              :rows="1"
              :placeholder="$t('请输入原因描述')"
            />
          </mt-form-item>
        </mt-form>
      </div>
    </collapse>
    <!-- 邀请供应商 -->
    <collapse :title="$t('邀请供应商')">
      <div slot="content">
        <InviteSupplier
          ref="inviteSupplierRef"
          :editable="editable"
          :data-source="supplierInvitationData"
        ></InviteSupplier>
      </div>
    </collapse>
    <!-- 采购需求明细 -->
    <collapse :title="$t('采购需求明细')">
      <div slot="content">
        <PurchaseItem
          :basic-info="dataForm"
          :data-source="publicSourcingItemData"
          :editable="editable"
          ref="purchaseItemRef"
        ></PurchaseItem>
      </div>
    </collapse>
    <collapse :title="$t('展示给供方的信息')">
      <div slot="content" class="form-content">
        <mt-form ref="contactDataFormRef" :model="contactDataForm" :rules="contactFormRules">
          <mt-form-item prop="contactUserId" :label="$t('联系人姓名')" label-style="top">
            <RemoteSelect
              v-model="contactDataForm.contactUserId"
              url="/masterDataManagement/tenant/employee/currentTenantEmployees"
              method="get"
              records-position="data"
              params-key="fuzzyName"
              :disabled="!editable"
              :list-width="400"
              :is-loaded="isLoaded"
              :fields="{
                text: 'companyOrgName-departmentOrgName-employeeCode-employeeName',
                value: 'uid'
              }"
              :default-search-text="contactUserName"
              @change="(e) => handleSelectChange(e, 'contact')"
            ></RemoteSelect>
          </mt-form-item>
          <mt-form-item prop="contactMobile" :label="$t('手机号')" label-style="top">
            <vxe-input
              v-model="contactDataForm.contactMobile"
              clearable
              :disabled="!editable"
              :placeholder="$t('请输入手机号')"
            />
          </mt-form-item>
          <mt-form-item prop="contactUserEmail" :label="$t('邮箱')" label-style="top">
            <vxe-input
              v-model="contactDataForm.contactUserEmail"
              clearable
              :disabled="!editable"
              :placeholder="$t('请输入邮箱')"
            />
          </mt-form-item>
        </mt-form>
      </div>
    </collapse>
  </div>
</template>

<script>
import { Input as VxeInput, Textarea as VxeTextarea, Select as VxeSelect } from 'vxe-table'
import UploadInput from '@/components/VxeComponents/uploadInput'
import RemoteSelect from '@/components/VxeComponents/remoteSelect'
import collapse from '../../components/collapse'
import InviteSupplier from './inviteSupplierTable'
import PurchaseItem from './purchaseItemTable'
import { cloneDeep, isEmpty } from 'lodash'
export default {
  name: 'BaseInfoTab',
  inject: ['reload'],
  components: {
    VxeInput,
    VxeTextarea,
    VxeSelect,
    UploadInput,
    collapse,
    RemoteSelect,
    InviteSupplier,
    PurchaseItem
  },
  props: {
    detailInfo: {
      type: Object,
      default: () => null
    },
    status: {
      type: [String, Number],
      default: () => 0
    }
  },
  data() {
    return {
      dataForm: {}, // 基本信息表单数据
      contactDataForm: {
        // contactUserId: JSON.parse(sessionStorage.getItem('userInfo'))?.accountId || null
      },
      sourceMethodList: [
        { label: this.$t('公开'), value: 'open' },
        { label: this.$t('邀请'), value: 'target' }
      ],
      companyList: [],
      dictItems: {}, // 字典数据
      paymentTermsList: [], // 付款条件
      supplierInvitationData: null,
      publicSourcingItemData: null,
      isLoaded: false,
      minDate: new Date(new Date().getTime() + 24 * 60 * 60 * 1000), // 设置最小日期为明天
      contactUserName: '',
      categoryCode: ''
    }
  },
  computed: {
    // 交货地址
    deliveryPlaceTitle() {
      let _list = []
      this.dataForm?.extendFactoryList?.forEach((item) => {
        const selectedItem = this.dictItems.PUBLIC_SOURCING_DELIVERY_PLACE.find(
          (t) => t.dictCode === item
        )
        _list.push(selectedItem?.dictName)
      })
      return _list.length ? _list.join(',') : ''
    },
    // 基本信息校验规则
    formRules() {
      return {
        rfxName: [{ required: true, message: this.$t('请输入寻源标题'), trigger: 'blur' }],
        sourceMethod: [{ required: true, message: this.$t('请选择寻源方式'), trigger: 'blur' }],
        deadlineDate: [{ required: true, message: this.$t('请选择截止时间'), trigger: 'blur' }],
        categoryCode: [{ required: true, message: this.$t('请选择品类'), trigger: 'blur' }],
        companyCode: [{ required: true, message: this.$t('请选择需求公司'), trigger: 'blur' }],
        payMethodCode: [{ required: true, message: this.$t('请选择付款方式'), trigger: 'blur' }],
        paymentTermsCode: [{ required: true, message: this.$t('请选择付款条件'), trigger: 'blur' }],
        deliveryPlaceList: [
          { required: true, message: this.$t('请选择交货地址'), trigger: 'blur' }
        ],
        cycleTime: [{ required: true, message: this.$t('请输入交货周期'), trigger: 'blur' }],
        expectSupplierQty: [
          { required: true, message: this.$t('请输入期望寻源数量'), trigger: 'blur' }
        ],
        sourcingFiles: [{ required: true, message: this.$t('请上传部品图片'), trigger: 'blur' }],
        reasonCode: [{ required: true, message: this.$t('请选择寻源原因'), trigger: 'blur' }],
        reasonSpecificDesc: [
          { required: true, message: this.$t('请输入具体原因描述'), trigger: 'blur' }
        ]
      }
    },
    contactFormRules() {
      return {
        contactUserId: [{ required: true, message: this.$t('请输入联系人姓名'), trigger: 'blur' }],
        contactMobile: [{ required: true, message: this.$t('请输入手机号'), trigger: 'blur' }],
        contactUserEmail: [{ required: true, message: this.$t('请输入邮箱'), trigger: 'blur' }]
      }
    },
    editable() {
      return this.status <= 0
    }
  },
  watch: {
    detailInfo: {
      handler(v) {
        // 渲染接口请求基本数据
        if (!isEmpty(v)) {
          this.initBaseData(v)
        }
      },
      immediate: true,
      deep: true
    }
  },

  mounted() {
    // 需求公司数据列表
    this.getCompanyList()
    // 付款条件数据列表
    this.queryPaymentTerms()
    // 字典数据
    this.initDictItems()
    if (!this.$route.query.id) {
      this.initContact()
    }
  },

  methods: {
    disableDates(params) {
      const currentDate = new Date()
      // currentDate.setHours(0, 0, 0, 0) // 设置时间为当天的午夜
      const { date } = params
      return new Date(date).getTime() < currentDate.getTime()
    },
    initBaseData(data) {
      const {
        rfxCode,
        rfxName,
        sourceMethod,
        deadlineDate,
        categoryCode,
        categoryName,
        companyCode,
        companyName,
        payMethodCode,
        payMethodName,
        paymentTermsCode,
        paymentTermsName,
        deliveryPlaceList,
        cycleTime,
        expectSupplierQty,
        sourcingFiles,
        reasonCode,
        reasonSpecificDesc,
        contactUserId,
        contactUserName,
        contactMobile,
        contactUserEmail,
        supplierInvitationDTOS,
        publicSourcingItemDTOS
      } = data
      // 渲染基本信息
      this.dataForm = {
        rfxCode,
        rfxName,
        sourceMethod,
        deadlineDate,
        categoryCode,
        categoryName,
        companyCode,
        companyName,
        payMethodCode,
        payMethodName,
        paymentTermsCode,
        paymentTermsName,
        deliveryPlaceList,
        cycleTime,
        expectSupplierQty,
        sourcingFiles,
        reasonCode,
        reasonSpecificDesc
      }
      if (contactUserId) {
        // 渲染联系方式等信息
        this.contactDataForm = {
          contactUserId,
          contactUserName,
          contactMobile,
          contactUserEmail
        }
        this.categoryCode = categoryCode
        this.contactUserName = contactUserName
        this.isLoaded = true
      }

      // 邀请供应商
      this.supplierInvitationData = cloneDeep(supplierInvitationDTOS)
      // 采购需求明细
      this.publicSourcingItemData = cloneDeep(publicSourcingItemDTOS)
    },
    initContact() {
      const { username, accountId, mail, mobile } = JSON.parse(sessionStorage.getItem('userInfo'))
      this.contactDataForm = Object.assign(
        {},
        {
          contactUserId: accountId,
          contactUserName: username,
          contactMobile: mobile,
          contactUserEmail: mail
        }
      )
      this.contactUserName = username
      this.isLoaded = true
    },
    // 获取公司下拉列表
    async getCompanyList() {
      const res = await this.$API.masterData.permissionCompanyList()
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.companyList = res.data
      }
    },
    // 获取付款条件
    async queryPaymentTerms() {
      const res = await this.$API.publicSourcing.queryPaymentTerms()
      if (res.code === 200) {
        this.paymentTermsList = res.data
      }
    },
    // 初始化 - 字典数据
    async initDictItems() {
      let codeList = [
        { code: 'payMethod', type: 'string' }, // 付款方式
        { code: 'PUBLIC_SOURCING_REASON', type: 'string' }, // 寻源原因
        { code: 'PUBLIC_SOURCING_DELIVERY_PLACE', type: 'string' } // 交货地址
      ]
      await this.$API.masterData.getNumDictListAllByCode(codeList).then((res) => {
        if (res.code === 200) {
          this.dictItems = res.data
        }
      })
    },
    // 下拉框选择监听 -
    handleSelectChange(e, type) {
      if (type === 'category') {
        this.$set(this.dataForm, 'categoryName', e.categoryName)
        this.$set(this.dataForm, 'categoryId', e.id)
        this.$refs.purchaseItemRef.clearData()
      } else if (type === 'company') {
        const _find = this.companyList.find((item) => item.orgCode === e.value)
        this.$set(this.dataForm, 'companyName', _find.orgName)
        this.$set(this.dataForm, 'companyId', _find.id)
        this.$refs.purchaseItemRef.clearData()
      } else if (type === 'contact') {
        this.$set(this.contactDataForm, 'contactUserName', e.employeeName)
        this.$set(this.contactDataForm, 'contactMobile', e.phoneNum)
        this.$set(this.contactDataForm, 'contactUserEmail', e.email)
      } else if (type === 'payMethod') {
        const _find = this.dictItems.PAY_METHOD.find((item) => item.dictCode === e.value)
        this.$set(this.dataForm, 'payMethodName', _find.dictName)
      } else if (type === 'paymentTerms') {
        const _find = this.paymentTermsList.find((item) => item.paymentTermsCode === e.value)
        this.$set(this.dataForm, 'paymentTermsName', _find.paymentTermsName)
      }
    },
    // dialog - cancel
    cancel() {
      this.$refs.dialog.ejsRef.hide()
    },
    // dialog - confirm
    confirm() {
      // 保存数据
      this.handleSave()
      this.$refs.dialog.ejsRef.hide()
    },
    // 保存当前信息页
    handleSave() {
      this.validFormData(async () => {
        let _submitData = this.getSubmitData()
        let res = await this.$API.publicSourcing.saveBaseInfo(_submitData)
        if (res.code === 200) {
          this.$toast({ content: this.$t('保存成功！'), type: 'success' })
          if (!this.$route.query?.id) {
            this.$router.push({
              path: '/sourcing/public-sourcing-initiated/detail',
              query: { id: res.data, refreshId: Date.now() }
            })
          } else {
            this.reload()
          }
        }
      })
    },
    // 校验表单数据
    async validFormData(fn) {
      try {
        const result = await this.$refs.dataFormRef.validate()
        const contactResult = await this.$refs.contactDataFormRef.validate()
        if (this.$refs.purchaseItemRef.tableData?.length <= 0) {
          this.$toast({ content: this.$t('请填写采购需求明细！'), type: 'warning' })
          return
        }
        if (result && contactResult) {
          fn()
        }
      } catch (error) {
        console.error(error)
      }
    },
    // 提交审批
    async handleSubmit() {
      this.validFormData(async () => {
        let _submitData = this.getSubmitData()
        let res = await this.$API.publicSourcing.submitInitiatedList(_submitData)
        if (res.code === 200) {
          this.$toast({ content: this.$t('提交成功！'), type: 'success' })
          if (!this.$route.query?.id) {
            this.$router.push({
              path: '/sourcing/public-sourcing-initiated/detail',
              query: { id: res.data, refreshId: Date.now() }
            })
          } else {
            this.reload()
          }
        }
      })
    },
    // 获取信息
    getSubmitData() {
      let inviteSupplierData = this.$refs.inviteSupplierRef.tableData
      let purchaseItemData = this.$refs.purchaseItemRef.tableData
      let _deadlineDate = this.dataForm.deadlineDate
      let params = {
        ...this.dataForm,
        ...this.contactDataForm,
        publicSourcingItemDTOS: cloneDeep(purchaseItemData),
        supplierInvitationDTOS: cloneDeep(inviteSupplierData),
        id: this.$route.query?.id,
        deadlineDate: _deadlineDate ? _deadlineDate.replace(/:\d{2}$/, ':00') : _deadlineDate
      }
      return params
    }
  }
}
</script>

<style scoped lang="scss">
.form-content {
  .mt-form {
    width: 100%;
    box-sizing: border-box;
    display: grid;
    grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
    justify-content: space-between;
    grid-gap: 5px;
  }
  .vxe-input,
  .vxe-select,
  .vxe-pulldown {
    width: 100%;
    height: 34px;
    line-height: 34px;
  }
  /deep/.vxe-pulldown--panel {
    min-width: unset !important;
    width: 100%;
  }
}

::v-deep {
  .mt-form-item {
    margin-bottom: 15px;
    .label {
      margin: 0 10px 5px 0;
    }
  }
  .mt-tabs-container {
    background-color: #fff;
  }

  .mt-tabs-container ul.tab-container li.tab-item2.active {
    border-bottom: 2px solid #409eff;
  }
  .mt-tabs-container ul.tab-container li.tab-item2.active,
  .mt-tabs-container ul.tab-container li.tab-item2:hover {
    color: #31374e;
  }

  .vxe-textarea--inner {
    min-height: 34px;
    height: 100%;
  }
}
</style>
