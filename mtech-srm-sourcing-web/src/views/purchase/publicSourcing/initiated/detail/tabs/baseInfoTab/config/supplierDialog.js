import { i18n } from '@/main.js'

const columns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'organizationCode',
    headerText: i18n.t('公司'),
    valueAccessor: (field, data) => {
      return data.organizationCode + '-' + data.organizationName
    }
  },
  {
    field: 'statusDescription',
    headerText: i18n.t('供应商状态')
  }
]

export const pageConfig = () => {
  return [
    {
      gridId: '74713cd9-0fce-4f4a-a4d8-b190f36a82ab',
      useToolTemplate: false,
      toolbar: [],
      showSelected: true,
      isUseCustomSearch: true,
      isCustomSearchRules: true,
      grid: {
        allowFiltering: true,
        columnData: columns,
        allowSorting: false,
        allowSelection: true,
        selectionSettings: {
          type: 'Multiple',
          mode: 'Row'
        },
        asyncConfig: {
          url: '/masterDataManagement/tenant/mdm/supplier/paged-query',
          params: {
            deduplicateSupplierCode: false,
            statusList: [1, 2],
            fillContactFlag: true
          }
        }
      }
    }
  ]
}
