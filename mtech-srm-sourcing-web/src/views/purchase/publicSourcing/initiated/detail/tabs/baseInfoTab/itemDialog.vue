<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-local-template-page ref="templateRef" :template-config="pageConfig">
        <template v-slot:quick-search-form>
          <div class="custom-form-box">
            <mt-form ref="searchFormRef" :model="searchFormModel">
              <mt-form-item prop="itemCode" :label="$t('物料编码')">
                <mt-input v-model="searchFormModel.itemCode" show-clear-button></mt-input>
              </mt-form-item>
              <mt-form-item prop="itemName" :label="$t('物料名称')">
                <mt-input v-model="searchFormModel.itemName" show-clear-button></mt-input>
              </mt-form-item>
            </mt-form>
          </div>
        </template>
      </mt-local-template-page>
    </div>
  </mt-dialog>
</template>

<script>
import MtLocalTemplatePage from '@/components/template-page'
import { pageConfig } from './config/itemDialog'
export default {
  components: {
    MtLocalTemplatePage
  },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      pageConfig: [],
      searchFormModel: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },

  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.modalData.isShow ? [] : this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.pageConfig = pageConfig(this)
  },
  methods: {
    confirm() {
      const selectedRecords = this.$refs.templateRef
        .getCurrentTabRef()
        .gridRef.getMtechGridRecords()
      if (selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      this.$emit('confirm-function', selectedRecords)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
