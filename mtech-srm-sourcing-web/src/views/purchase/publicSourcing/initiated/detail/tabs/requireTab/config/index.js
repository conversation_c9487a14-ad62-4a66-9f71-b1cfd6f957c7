import { i18n } from '@/main.js'

export const toolbar = [
  { id: 'Add', icon: '', title: i18n.t('新增') },
  { id: 'Delete', icon: '', title: i18n.t('删除') }
]
// 需求项目类型

export const requireTypeList = [
  // { text: i18n.t('产/销资格'), value: 'CXZG' },
  // { text: i18n.t('经营年限'), value: 'JYNX' },
  // { text: i18n.t('质量体系'), value: 'ZLTX' },
  // { text: i18n.t('环评资格'), value: 'HPZG' },
  // { text: i18n.t('销售金额'), value: 'XSJE' },
  // { text: i18n.t('产能要求'), value: 'CNYQ' },
  { text: i18n.t('品质要求'), value: 'PZYQ' },
  { text: i18n.t('技术要求'), value: 'JSYQ' },
  { text: i18n.t('其它要求'), value: 'QTYQ' }
]
export const requireTypeDescList = {
  PZYQ: i18n.t('拥有附件中所列出的品质检测设备或相关品质要求'),
  JSYQ: i18n.t('符合附件中所列出的相关要求'),
  QTYQ: i18n.t('@input{}')
}
const requireTypeMap = {
  CXZG: i18n.t('产/销资格'),
  JYNX: i18n.t('经营年限'),
  ZLTX: i18n.t('质量体系'),
  HPZG: i18n.t('环评资格'),
  XSJE: i18n.t('销售金额'),
  CNYQ: i18n.t('产能要求'),
  PZYQ: i18n.t('品质要求'),
  JSYQ: i18n.t('技术要求'),
  QTYQ: i18n.t('其它要求')
}
export const columns = () => {
  let defaultColumns = [
    { option: 'checkboxSelection', width: 55, showDisabledCheckboxes: true },
    {
      field: 'lineNo',
      headerName: i18n.t('行号'),
      width: 65
    },
    {
      field: 'requireType',
      headerName: i18n.t('需求项目'),
      editable: (params) => {
        return !['CXZG', 'JYNX', 'ZLTX', 'HPZG', 'XSJE', 'CNYQ'].includes(params.data.requireType)
      },
      option: 'customEdit',
      width: 200,
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          dataSource: requireTypeList
        }
      },
      valueFormatter: (params) => {
        return requireTypeMap[params.value]
      },
      cellClass: (params) => {
        return ['CXZG', 'JYNX', 'ZLTX', 'HPZG', 'XSJE', 'CNYQ'].includes(params.data.requireType)
          ? 'singleCellDisable'
          : ''
      }
    },
    {
      field: 'requireTypeDesc',
      headerName: i18n.t('需求说明'),
      required: true,
      editable: (params) => {
        return (
          params.data.requireTypeDesc?.indexOf('@input') >= 0 ||
          params.data.requireTypeDesc?.indexOf('@number') >= 0
        )
      },
      option: 'customEdit',
      width: 800,
      editConfig: {
        type: 'cellDesc',
        props: {
          // rfxId: args?.rfxId,
          // detailInfo: args?.detailInfo
        }
      },
      cellClass: (params) => {
        return params.data.requireTypeDesc?.indexOf('@input') === -1 &&
          params.data.requireTypeDesc?.indexOf('@number') === -1
          ? 'singleCellDisable'
          : ''
      }
    },
    {
      field: 'sourcingFiles',
      headerName: i18n.t('参考附件'),
      width: 300
    }
  ]

  return defaultColumns
}
