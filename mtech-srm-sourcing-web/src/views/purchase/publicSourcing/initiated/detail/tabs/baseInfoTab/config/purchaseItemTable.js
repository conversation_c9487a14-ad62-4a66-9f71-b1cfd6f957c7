import { i18n } from '@/main.js'

export const toolbar = [
  { id: 'Add', icon: '', title: i18n.t('新增') },
  { id: 'Delete', icon: '', title: i18n.t('删除') }
]

export const columns = [
  { option: 'checkboxSelection', width: 55 },
  {
    field: 'itemCode',
    headerName: i18n.t('物料编号')
  },
  {
    field: 'itemName',
    headerName: i18n.t('物料名称')
  },
  {
    field: 'unitCode',
    headerName: i18n.t('单位')
  },
  {
    field: 'priceUnit',
    headerName: i18n.t('价格单位'),
    option: 'customEdit',
    required: true,
    editable: true,
    editConfig: {
      type: 'select',
      props: {
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { text: '1', value: '1' },
          { text: '1000', value: '1000' }
        ]
      }
    },
    cellClass: 'cellEditable'
  },
  {
    field: 'demandQty',
    headerName: i18n.t('预计年需求量'),
    option: 'customEdit',
    required: true,
    editable: true,
    // editConfig: {
    //   type: 'number',
    //   min: 0.01,
    //   precision: 2,
    //   max: 99999999
    // },
    editConfig: {
      type: 'number',
      min: 1,
      precision: '0',
      max: 99999999
    },
    cellClass: 'cellEditable'
  },
  {
    field: 'materialUsage',
    headerName: i18n.t('物料用途'),
    editable: true,
    cellClass: 'cellEditable'
  },
  {
    field: 'remark',
    headerName: i18n.t('备注'),
    editable: true,
    cellClass: 'cellEditable'
  },
  {
    field: 'sourcingFiles',
    headerName: i18n.t('规格图纸'),
    required: true
  }
]
