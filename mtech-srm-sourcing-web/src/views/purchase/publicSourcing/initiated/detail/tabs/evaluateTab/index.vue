<template>
  <!-- 需求采购明细 -->
  <div class="requireWrapper">
    <CustomAgGrid
      ref="CustomAgGrid"
      :columns="columns"
      :suppress-row-transform="true"
      :row-data="tableData"
      :animate-rows="false"
      :params-type="2"
      :row-height="40"
      @onGridReady="onGridReady"
      @row-selected="onRowSelected"
      @refresh="refresh"
      @search="search"
    >
    </CustomAgGrid>
    <!-- 驳回 -->
    <mt-dialog ref="rejectDialog" :buttons="buttons" :header="$t('原因')" size="small">
      <div class="dialog-content">
        <mt-form style="margin-top: 20px">
          <mt-form-item prop="remark" :label="$t('驳回原因')">
            <mt-input
              class="reasonInput"
              v-model="remark"
              :multiline="true"
              :rows="20"
              :placeholder="$t('请输入驳回原因')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </mt-dialog>
    <!-- 淘汰 -->
    <mt-dialog ref="eliminateDialog" :buttons="eliminateButtons" :header="$t('原因')" size="small">
      <div class="dialog-content">
        <mt-form style="margin-top: 20px">
          <mt-form-item prop="eleminateRemark" :label="$t('淘汰原因')">
            <mt-input
              class="reasonInput"
              v-model="eleminateRemark"
              :multiline="true"
              :rows="20"
              :placeholder="$t('请输入淘汰原因')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </mt-dialog>
  </div>
</template>
<script>
import CustomAgGrid from '@/components/CustomAgGrid'
import { columns } from './config'
import cellFile from '@/components/AgCellComponents/cellFile'
import cellDescView from '@/components/AgCellComponents/cellDescView'
import { v4 as uuidv4 } from 'uuid'
import { cloneDeep } from 'lodash'
export default {
  name: 'BaseInfoTab',
  inject: ['reload'],
  components: {
    CustomAgGrid,
    // eslint-disable-next-line vue/no-unused-components
    cellFile,
    // eslint-disable-next-line vue/no-unused-components
    cellDescView
  },
  data() {
    return {
      tableData: [],
      columns: [],
      agGrid: null,
      remark: '',
      eleminateRemark: '',
      rowId: '',
      eliminateRowId: '',
      buttons: [
        {
          click: this.rejectCancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.rejectConfirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      eliminateButtons: [
        {
          click: this.eliminateCanCel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.eliminateConfirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  props: {
    status: {
      type: [Number, String],
      default: 2 // 已发布
    },
    editable: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    $route(to, from) {
      if (from.name === 'public-sourcing-score') {
        // 由入围评分页面返回，则刷新当前页面
        this.refresh()
      }
    }
  },
  created() {
    this.initColumns()
    this.initTableData()
  },
  methods: {
    initColumns() {
      this.columns = columns.map((item) => {
        if (item.field === 'sourcingFile') {
          item.cellRenderer = 'cellFile'
          item.editable = false
          item.cellRendererParams = {
            handleable: this.editable
          }
        } else if (item.field === 'requireTypeDesc') {
          item.cellRenderer = 'cellDescView'
          item.cellRendererParams = {
            rfxId: this.$route.query.id
          }
        } else if (item.field === 'handler') {
          item.render = (h, scope) => {
            return this.status === 2 ? (
              <div class='operate-btns'>
                <span
                  class='operate-btn'
                  onClick={() => {
                    this.handleReject(scope.row)
                  }}>
                  {this.$t('驳回')}
                </span>
                <span
                  class='operate-btn'
                  onClick={() => {
                    this.handleScore(scope.row)
                  }}>
                  {this.$t('查看详细信息')}
                </span>
              </div>
            ) : (
              <div class='operate-btns'>
                <span
                  class='operate-btn'
                  onClick={() => {
                    this.handleScore(scope.row)
                  }}>
                  {this.$t('评分')}
                </span>
                <span
                  class='operate-btn'
                  onClick={() => {
                    this.handleEntry(scope.row)
                  }}>
                  {this.$t('入围')}
                </span>
                <span
                  class='operate-btn'
                  onClick={() => {
                    this.handleEliminate(scope.row)
                  }}>
                  {this.$t('淘汰')}
                </span>
              </div>
            )
          }
          if (this.status === 2) {
            item.width = 200
          } else {
            item.width = 180
          }
        }
        return item
      })
    },
    // 评分
    handleScore(row) {
      this.$router.push({
        name: 'public-sourcing-score',
        query: {
          rfxId: row.rfxId,
          sId: row.supplierTenantId, // 供应商id
          status: this.status,
          supplierCode: row.supplierCode,
          supplierName: row.supplierName,
          refreshId: Date.now()
        }
      })
    },
    // 入围
    handleEntry(row) {
      if (row.supplierStatus === 1) {
        this.$toast({ content: this.$t(`该供应商已入围！`), type: 'warning' })
        return
      }
      // 如果黑名单供应商，直接禁止入围
      if (row.isBlacklist) {
        this.$toast({ content: this.$t(`黑名单供应商禁止入围！`), type: 'warning' })
        return
      }
      let _tips =
        row.supplierStatus === 2
          ? this.$t(`是否确定入围淘汰供应商？`)
          : this.$t(`确认该供应商入围？`)
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: _tips
        },
        success: async () => {
          const res = await this.$API.publicSourcing.passEvaluationList({ id: row.id })
          if (res.code === 200) {
            this.$toast({ content: this.$t(`操作成功！`), type: 'success' })
            this.initTableData()
          }
        }
      })
    },
    // 驳回
    handleReject(row) {
      this.rowId = row.id
      this.$refs.rejectDialog.ejsRef.show()
    },
    // 驳回确认
    async rejectConfirm() {
      if (!this.remark) {
        this.$toast({ content: this.$t(`请输入驳回原因`), type: 'warning' })
        return
      }
      const res = await this.$API.publicSourcing.rejectEvaluationList({
        id: this.rowId,
        remark: this.remark
      })
      if (res.code === 200) {
        this.$toast({ content: this.$t(`操作成功！`), type: 'success' })
        this.$refs.rejectDialog.ejsRef.hide()
        this.initTableData()
      }
    },
    // 淘汰确认
    async eliminateConfirm() {
      if (!this.eleminateRemark) {
        this.$toast({ content: this.$t(`请输入淘汰原因`), type: 'warning' })
        return
      }
      const res = await this.$API.publicSourcing.obsoleteEvaluationList({
        id: this.eliminateRowId,
        remark: this.eleminateRemark
      })
      if (res.code === 200) {
        this.$toast({ content: this.$t(`操作成功！`), type: 'success' })
        this.$refs.eliminateDialog.ejsRef.hide()
        this.initTableData()
      }
    },
    eliminateConCel() {
      this.$refs.eliminateDialog.ejsRef.hide()
    },
    rejectCancel() {
      this.$refs.rejectDialog.ejsRef.hide()
    },
    // 淘汰
    handleEliminate(row) {
      this.eliminateRowId = row.id
      this.$refs.eliminateDialog.ejsRef.show()
    },
    // 提交审批
    async handleSubmit() {
      let res = await this.$API.publicSourcing.checkEvaluationList({ id: this.$route.query?.id })
      if (res.code === 200) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: res.data
          },
          success: async () => {
            let submitRes = await this.$API.publicSourcing.submitOaEvaluationList({
              id: this.$route.query?.id
            })
            if (submitRes.code === 200) {
              this.reload()
            }
          }
        })
      }
    },
    // 初始化渲染表格
    async initTableData() {
      let res = await this.$API.publicSourcing.queryEvaluationList({
        rfxId: this.$route.query?.id,
        page: { current: 1, size: 500 }
      })
      if (res.code === 200) {
        this.tableData = res.data?.records?.map((item, index) => {
          item.rowId = uuidv4()
          item.lineNo = index + 1
          return item
        })
        this.cacheTableData = cloneDeep(this.tableData)
      }
    },
    onGridReady(params) {
      this.agGrid = params
    },
    onRowSelected() {},
    // 表格 - 工具 - 获取表格数据
    getRowData() {
      const rowData = []
      this.agGrid.api.forEachNode(function (node) {
        rowData.push(node.data)
      })
      return rowData
    },
    refresh() {
      this.initTableData()
    },
    search() {}
  }
}
</script>
<style lang="scss">
.requireWrapper {
  flex-grow: 1;
  .grid-container {
    height: 100%;
  }
  .ag-theme-alpine .ag-cell-inline-editing {
    height: 40px !important;
  }
  .mt-input-number {
    width: 100px;
  }
  .mt-input-number .step-box .one-icon {
    display: none;
  }
  .mt-input-number input[type='number'] {
    padding-left: 10px;
    padding-right: 10px;
  }
  .mt-input-number .icon-clear {
    margin-top: 6px;
    right: 10px;
  }
  .mt-input {
    width: 100%;
  }
}
.mt-form .reasonInput .e-input-group textarea.e-input,
.mt-form .reasonInput .e-input-group.e-control-wrapper textarea.e-input {
  height: 145px !important;
}
</style>
