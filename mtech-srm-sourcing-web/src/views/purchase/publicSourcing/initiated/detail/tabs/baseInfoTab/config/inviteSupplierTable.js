import { i18n } from '@/main.js'

export const toolbar = [
  { id: 'Add', icon: '', title: i18n.t('添加供应商') },
  { id: 'Delete', icon: '', title: i18n.t('删除') }
]

export const columns = [
  { option: 'checkboxSelection', width: 55 },
  {
    field: 'supplierCode',
    headerName: i18n.t('供应商代码'),
    width: 110
  },
  {
    field: 'supplierName',
    headerName: i18n.t('供应商名称')
  },
  {
    field: 'companyCode',
    headerName: i18n.t('公司'),
    valueFormatter: (params) => {
      return params.data.companyCode + '-' + params.data.companyName
    },
    width: 230
  },
  {
    field: 'categoryCode',
    headerName: i18n.t('品类'),
    valueFormatter: (params) => {
      return params.data.categoryCode + '-' + params.data.categoryName
    }
  },
  {
    field: 'supplierContactUserName',
    headerName: i18n.t('联系人'),
    width: 100
  },
  {
    field: 'supplierContactUserTel',
    headerName: i18n.t('电话'),
    width: 150
  },
  {
    field: 'supplierContactUserMailbox',
    headerName: i18n.t('邮箱'),
    width: 200
  }
]
