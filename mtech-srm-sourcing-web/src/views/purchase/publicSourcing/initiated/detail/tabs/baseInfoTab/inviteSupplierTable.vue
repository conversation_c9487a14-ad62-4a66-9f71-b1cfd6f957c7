<template>
  <!-- 邀请供应商 -->
  <div class="inviteSupplier">
    <CustomAgGrid
      ref="CustomAgGrid"
      :toolbar="toolbar"
      :columns="columns"
      :suppress-row-transform="true"
      :row-data="tableData"
      :animate-rows="false"
      :params-type="2"
      @handleClickToolbar="handleClickToolbar"
      @onGridReady="onGridReady"
      @refresh="refresh"
    >
    </CustomAgGrid>
  </div>
</template>
<script>
import CustomAgGrid from '@/components/CustomAgGrid'
import { columns, toolbar } from './config/inviteSupplierTable'
import { v4 as uuidv4 } from 'uuid'
import { cloneDeep } from 'lodash'
export default {
  name: 'BaseInfoTab',
  components: {
    CustomAgGrid
  },
  props: {
    // 初始化数据
    dataSource: {
      type: Array,
      default: () => []
    },
    // 是否可编辑
    editable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      columns,
      tableData: [],
      cacheTableData: []
    }
  },
  watch: {
    dataSource: {
      handler(val) {
        val.length && this.initTableData()
      }
    }
  },
  computed: {
    toolbar() {
      return this.editable ? toolbar : []
    }
  },
  methods: {
    // 初始化渲染表格
    initTableData() {
      this.tableData = this.dataSource.map((item) => {
        item.rowId = uuidv4()
        return item
      })
      this.cacheTableData = cloneDeep(this.tableData)
    },
    onGridReady(params) {
      this.agGrid = params
    },
    handleClickToolbar(args) {
      let _id = args.toolbar.id
      switch (_id) {
        case 'Add':
          this.handleAdd() // 新增
          break
        case 'Delete':
          this.handleDelete() // 删除
          break
      }
    },
    handleAdd() {
      this.$dialog({
        modal: () => import('./supplierDialog.vue'),
        data: {
          title: this.$t('添加供应商'),
          multiple: true
        },
        success: async (list) => {
          let _list = list.map((item) => {
            return {
              rowId: uuidv4(),
              supplierCode: item.supplierCode,
              supplierName: item.supplierName,
              companyCode: item.organizationCode,
              companyName: item.organizationName,
              categoryCode: item.categoryCode,
              categoryName: item.categoryName,
              supplierContactUserName: item.contactUserName,
              supplierContactUserTel: item.contactMobile,
              supplierContactUserMailbox: item.contactUserEmail,
              supplierTenantId: item.supplierTenantId
            }
          })
          this.tableData = [...this.tableData, ..._list]
        }
      })
    },
    handleDelete() {
      const rowSelections = this.agGrid.api.getSelectedRows()
      if (!rowSelections?.length) {
        this.$toast({
          content: this.$t('请先选择一行'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认删除选中的数据？`)
        },
        success: async () => {
          const selctionsArr = rowSelections.map((item) => item.rowId)
          this.tableData = this.tableData.filter((item) => !selctionsArr.includes(item.rowId))
        }
      })
    },
    // 表格 - 工具 - 获取表格数据
    getRowData() {
      const rowData = []
      this.agGrid.api.forEachNode(function (node) {
        rowData.push(node.data)
      })
      return rowData
    },
    refresh() {
      this.tableData = cloneDeep(this.cacheTableData)
    }
  }
}
</script>
<style lang="scss" scoped>
.inviteSupplier {
  height: 300px;
  .grid-container {
    height: 100%;
  }
}
</style>
