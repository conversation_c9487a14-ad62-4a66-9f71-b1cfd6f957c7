import { i18n } from '@/main.js'

const columns = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料明细')
  },
  {
    field: 'currencyCode',
    headerText: i18n.t('币种')
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('未税单价')
  },
  {
    field: 'priceUnit',
    headerText: i18n.t('价格单位')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类')
  },
  {
    field: 'validStartDate',
    headerText: i18n.t('生效日期')
  },
  {
    field: 'validEndDate',
    headerText: i18n.t('失效日期')
  }
]

export const pageConfig = (that) => [
  {
    useToolTemplate: false,
    toolbar: [],
    gridId: 'b9674431-2077-4ddb-ba61-7f365fcce2e1',
    showSelected: true,
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    grid: {
      allowFiltering: true,
      columnData: columns,
      allowSorting: false,
      allowSelection: true,
      selectionSettings: {
        type: 'Multiple',
        mode: 'Row'
      },
      dataSource: [],
      asyncConfig: {
        url: '/price/tenant/priceRecord/new/record/queryPriceItem',
        params: {
          categoryCode: that.modalData.categoryCode,
          companyCode: that.modalData.companyCode
        }
      }
    }
  }
]
