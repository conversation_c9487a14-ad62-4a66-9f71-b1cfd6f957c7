import { i18n } from '@/main.js'

const whetherMap = {
  0: i18n.t('否'),
  1: i18n.t('是')
}
const supplierStatus = {
  0: i18n.t('待入围'),
  1: i18n.t('已入围'),
  2: i18n.t('已淘汰')
}
export const columns = [
  // { option: 'checkboxSelection', width: 55, showDisabledCheckboxes: true },
  {
    field: 'lineNo',
    headerName: i18n.t('行号'),
    width: 65
  },
  {
    field: 'supplierCode',
    headerName: i18n.t('供应商'),
    valueFormatter: (params) => {
      return params.data.supplierCode + '-' + params.data.supplierName
    }
  },
  {
    field: 'partcpTime',
    headerName: i18n.t('报名时间')
  },
  {
    field: 'isBlacklist',
    headerName: i18n.t('是否黑名单'),
    valueFormatter: (params) => {
      return whetherMap[params.value]
    }
  },
  {
    field: 'hasRelCompany',
    headerName: i18n.t('是否存在关联企业'),
    valueFormatter: (params) => {
      return whetherMap[params.value]
    }
  },
  {
    field: 'hasResignedEmployees',
    headerName: i18n.t('是否聘用TCL离职员工'),
    valueFormatter: (params) => {
      return whetherMap[params.value]
    }
  },
  {
    field: 'isSatisfy',
    headerName: i18n.t('寻源要求是否全部满足'),
    valueFormatter: (params) => {
      return whetherMap[params.value]
    }
  },
  {
    field: 'source',
    headerName: i18n.t('供应商来源')
  },
  {
    field: 'supplierStatus',
    headerName: i18n.t('入围状态'),
    valueFormatter: (params) => {
      return supplierStatus[params.value]
    }
  },
  {
    field: 'siftedOutReason',
    headerName: i18n.t('淘汰原因')
  },
  {
    field: 'totalScore',
    headerName: i18n.t('总得分')
  },
  {
    field: 'handler',
    option: 'customOption',
    headerName: i18n.t('操作'),
    pinned: 'right'
  }
]
