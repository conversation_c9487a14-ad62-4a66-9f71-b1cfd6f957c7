<template>
  <!-- 大厅详情页面 -->
  <div class="hall-detail full-height">
    <!-- 头部信息 -->
    <top-info-view
      ref="topInfoRef"
      :module-type="moduleType"
      :detail-info="detailInfo"
      @saveProject="saveProject"
      @cancelProject="cancelProject"
      @editProject="editProject"
      @publishProject="publishProject"
      @failBidProject="failBidProject"
      @copyProject="copyProject"
      @pushPriceProject="pushPriceProject"
      @countDown="getCountDown"
    />
    <div class="roundBox" v-if="showRound && !calculation">
      <MtIcon name="icon_solid_inform" />
      {{ roundInfo }}
    </div>
    <!-- 中间内容 -->
    <div class="main-context" :key="keyIndex">
      <!-- 流程看板 -->
      <process-kanban :progress="detailInfo.rfxPhaseCode" v-show="!calculation" />
      <div class="main-context-right">
        <mt-tabs
          ref="mtTabsRef"
          :tab-id="$utils.randomString()"
          :e-tab="false"
          :data-source="RFXModuleTabList"
          :halt-select="false"
          @handleSelectTab="(index, item) => handleTabChange(index, item, 'click')"
          style="background-color: #fff"
          v-show="!calculation"
        />
        <keep-alive>
          <component
            :is="getCurrentModule"
            :field-defines="getModulesFields(moduleType, 'parentFields')"
            :child-fields="getModulesFields(moduleType, 'childFields')"
            :field-structures="getModulesFields(moduleType, 'structures')"
            :detail-info="detailInfo"
            :kt-flag="ktFlag"
            :has-count-down="hasCountDown"
            :is-logistics-trunk="this.detailInfo.sourcingScenarios === 'trunk_transport_annual'"
            @goToTab="goToTab"
            @reGetDetail="getDetail(false, false)"
            @preview="preview"
            ref="mainContent"
            :module-type="moduleType"
            @editProject="editProject"
          ></component>
        </keep-alive>
      </div>
    </div>
  </div>
</template>

<script>
import { isEmpty } from 'lodash'
import cloneDeep from 'lodash/cloneDeep'
import MixIn from './config/mixin.js'

// 默认看板小，中间内容大
export default {
  mixins: [MixIn],
  inject: ['reload'],
  components: {
    //顶部详情
    topInfoView: () =>
      import(
        /* webpackChunkName: "router/purchase/rfx/detail/components/top-info" */ './components/topInfo.vue'
      ),
    //流程看板
    processKanban: () =>
      import(
        /* webpackChunkName: "router/purchase/rfx/detail/components/process-kanban" */ './components/processKanban.vue'
      )
  },
  data() {
    return {
      ktFlag: '',
      loading: false,
      hallStatus: 1, // 大厅状态
      moduleType: -1, // 当前选中的tab
      detailInfo: {
        rfxPhaseCode: 'PROJECT_PREPARE'
      },
      progressList: [], // 流程列表
      RFXModuleTabList: [], // tabs列表
      tagPageModules: [], // 当前已有页面的tab moduleId列表（用于去掉没有子组件的tab名）
      formObject: {},
      rfxScoreModel: null, //rfx评分模式 0同时评分，1先技术后商务，2先商务后技术，3纯商务（没有技术标）
      roundInfo: '',
      keyIndex: 0,
      calculation: this.$route.query.calculation == 1 ? true : false,
      currentTabIndex: 0,
      hasCountDown: false,
      timer: null,
      firstInit: true,
      isLogisticsTrunk: false
    }
  },
  computed: {
    showRound() {
      return this.$route.query.source == 'bidding_price' && this.moduleType == 4 && this.roundInfo
    },
    getCurrentModule() {
      // 解决返回时候调用该方法导致加载页面请求接口找不到参数情况，会导致页面报错
      if (!this.$route.query?.source) return ''
      //todo 渲染逻辑待优化
      if (this.calculation) {
        if (this.moduleType !== 14) return ''
        if (this.detailInfo.sourcingScenarios === 'STAGE') {
          // 阶梯格式
          return () => import('./tabs/purchaseDetail/modules/step/index.vue')
        } else {
          return () => import('./tabs/purchaseDetail/index.vue')
        }
      } else {
        switch (this.moduleType) {
          case 4:
            //大厅
            console.log('渲染 大厅  Tab')
            if (this.$route.query.source == 'bidding_price') {
              return () => import('ROUTER_COMMON/tabs/realTimeHall')
            } else {
              return () =>
                import(
                  /* webpackChunkName: "router/common/tabs/hall" */
                  'ROUTER_COMMON/tabs/hall'
                )
            }

          case 14:
            //采购明细
            console.log('渲染 采购明细  Tab')
            // 询报价切换新UI
            if (this.$route.query.source == 'rfq') {
              if (
                this.detailInfo?.transferStatus > 1 &&
                this.detailInfo.sourcingScenarios === 'STAGE'
              ) {
                // 阶梯格式 - 发布后
                return () => import('./tabs/purchaseDetail/modules/step/index.vue')
              } else if (this.detailInfo.sourcingScenarios === 'LOGISTICS') {
                // 物流
                return () => import('./tabs/purchaseDetail/modules/logistics/index.vue')
              } else if (
                ['HIERARCHY_CKD', 'HIERARCHY_MOLD', 'cost_factor'].includes(
                  this.detailInfo.sourcingScenarios
                )
              ) {
                return () => import('./tabs/purchaseDetail/index.vue')
              } else if (
                [
                  'odmin',
                  'smt',
                  'COMMON',
                  'sea_transport_annual',
                  'railway_transport_annual'
                ].includes(this.detailInfo.sourcingScenarios)
              ) {
                return () => import('./tabs/purchaseDetail/modules/general/index.vue')
              }
            }
            if (
              this.$route.query.source == 'bidding_price' &&
              ['trunk_transport_annual'].includes(this.detailInfo.sourcingScenarios)
            ) {
              // 目前竞价 干线使用新ui
              return () => import('./tabs/purchaseDetail/modules/general/index.vue')
            }
            if (
              this.$route.query.source == 'invite_bids' &&
              ['GF'].includes(this.detailInfo.buType)
            ) {
              // 目前招标 光伏事业部使用新ui
              return () => import('./tabs/purchaseDetail/modules/general/index.vue')
            }
            return () => import('./tabs/purchaseDetail/index.vue') //333
          case 5:
            //候选供方
            console.log('渲染 候选供方  Tab')
            return () => import('./tabs/candidateSupplier/index.vue')
          case 6:
            //寻源团队
            console.log('渲染 寻源团队  Tab')
            return () => import('./tabs/teams/index.vue')
          case 3:
            //描述说明
            console.log('渲染 描述说明  Tab')
            return () => import('./tabs/description/index.vue')
          case 2:
            //相关文件
            console.log('渲染 相关文件  Tab')
            return () => import('./tabs/documents/index.vue')
          case 15:
            //策略报告
            console.log('渲染 策略报告  Tab')
            return () => import('./tabs/strategy/strategy.vue')
          case 10:
            //操作日志
            console.log('渲染 操作日志  Tab')
            return () => import('./tabs/logs/index.vue')
          case 9:
            //评标zxr
            console.log('渲染 评标zxr  Tab')
            // 询报价切换新UI
            if (this.$route.query.source == 'rfq') {
              if (this.detailInfo.sourcingScenarios === 'STAGE') {
                // 阶梯格式
                return () => import('./tabs/quotationDetails/modules/step/index.vue')
              } else if (this.detailInfo.sourcingScenarios === 'LOGISTICS') {
                return () => import('./tabs/quotationDetails/modules/logistics/index.vue')
              } else if (
                ['HIERARCHY_CKD', 'HIERARCHY_MOLD', 'cost_factor'].includes(
                  this.detailInfo.sourcingScenarios
                )
              ) {
                return () => import('./tabs/quotationDetails/index.vue')
              } else if (
                [
                  'odmin',
                  'smt',
                  'COMMON',
                  'sea_transport_annual',
                  'railway_transport_annual'
                ].includes(this.detailInfo.sourcingScenarios)
              ) {
                return () => import('./tabs/quotationDetails/modules/general/index.vue')
              }
            }
            if (
              this.$route.query.source == 'bidding_price' &&
              ['trunk_transport_annual'].includes(this.detailInfo.sourcingScenarios)
            ) {
              // 目前竞价 干线使用新ui
              return () => import('./tabs/quotationDetails/modules/general/index.vue')
            }
            if (
              this.$route.query.source == 'invite_bids' &&
              ['GF'].includes(this.detailInfo.buType)
            ) {
              // 目前招标 光伏事业部使用新ui
              return () => import('./tabs/quotationDetails/modules/general/index.vue')
            }
            return () => import('./tabs/quotationDetails/index.vue') //333
          case 20:
            //定标
            console.log('渲染 定标  Tab')
            if (this.$route.query.source == 'rfq') {
              if (this.detailInfo.sourcingScenarios === 'STAGE') {
                return () => import('./tabs/pricing/modules/step/index.vue')
              } else if (this.detailInfo.sourcingScenarios === 'LOGISTICS') {
                return () => import('./tabs/pricing/modules/logistics/index.vue')
              } else if (
                ['HIERARCHY_CKD', 'HIERARCHY_MOLD', 'cost_factor'].includes(
                  this.detailInfo.sourcingScenarios
                )
              ) {
                return () => import('./tabs/pricing/index.vue')
              } else if (
                [
                  'odmin',
                  'smt',
                  'COMMON',
                  'sea_transport_annual',
                  'railway_transport_annual'
                ].includes(this.detailInfo.sourcingScenarios)
              ) {
                return () => import('./tabs/pricing/modules/general/index.vue')
              }
            }
            if (
              this.$route.query.source == 'bidding_price' &&
              ['trunk_transport_annual'].includes(this.detailInfo.sourcingScenarios)
            ) {
              // 目前竞价 干线使用新ui
              return () => import('./tabs/pricing/modules/general/index.vue')
            }
            if (
              this.$route.query.source == 'invite_bids' &&
              ['GF'].includes(this.detailInfo.buType)
            ) {
              // 目前招标 光伏事业部使用新ui
              return () => import('./tabs/pricing/modules/general/index.vue')
            }
            return () => import('./tabs/pricing/index.vue') // 333
          case 21:
            //技术标
            console.log('渲染 技术标  Tab')
            return () => import('./tabs/technicalBid/list/index.vue')
          case 22:
            //商务标
            console.log('渲染 商务标  Tab')
            return () => import('./tabs/commercialBid/list/index.vue')
          case 25:
            //评分规则
            console.log('渲染 评分规则  Tab')
            return () => import('./tabs/ratingRules/index.vue')
          // 资质审查
          case 24:
            return () => import('@/views/purchase/rfx/detail/tabs/qualification/index.vue')
          // 核价
          case 70:
            return () => import('@/views/purchase/rfx/detail/tabs/corePrice/index.vue')
          // 基础信息
          case 999:
            return () => import('./tabs/basicsInfo/index.vue')

          // case 7:
          //   //任务计划 当前版本已弃用
          //   return () => import("./tabs/schedule/index.vue");
          // case 8:
          //   //核价 当前版本已弃用
          //   return () => import("./tabs/checkPricing/index.vue");
          // case 16:
          //   //询价招标 当前版本已弃用
          //   return () => import("./tabs/inquiryBidding/index.vue");
          // case 17:
          //   // 比价 当前版本已弃用
          //   return () => import("./tabs/comparePrice/index.vue");
          // case 18:
          //   // 竞价 当前版本已弃用
          //   return () => import("./tabs/bidding/index.vue");
          default:
            console.log('默认渲染 大厅  Tab')
            // 优化 - 为-1时候不渲染大厅
            if (this.moduleType === -1) return ''
            if (this.$route.query.source == 'bidding_price') {
              return () => import('ROUTER_COMMON/tabs/realTimeHall')
            } else {
              return () =>
                import(
                  /* webpackChunkName: "router/common/tabs/hall" */
                  'ROUTER_COMMON/tabs/hall'
                )
            }
        }
      }
    }
  },
  mounted() {
    this.getKtFlag()
    this.initModuleArray()
    this.getDetail(true)
    this.getPhaseById()
    this.$bus.$on(`updateRound`, (info) => {
      if (info) {
        this.roundInfo = info
      }
    })
  },
  beforeDestroy() {
    this.timer && clearInterval(this.timer)
  },
  deactivated() {
    this.timer && clearInterval(this.timer)
  },
  methods: {
    getNoticeSameIp() {
      this.timer && clearInterval(this.timer)
      this.$API.rfxList.noticeSameIpApi({ rfxId: this.$route.query.rfxId }).then((res) => {
        if (res.code == 200) {
          if (res.data?.showNotice) {
            let list = res.data.sameIpAddressDetailResponseList
            this.showNoticeDialog(list)
          } else {
            this.timer = setInterval(() => {
              this.pollingNoticeSameIp()
            }, 20 * 1000)
          }
        }
      })
    },
    getMessageSupplierInfo() {
      this.$API.supplyQdetail
        .getMessageSupplierInfoApi({ rfxId: this.$route.query.rfxId })
        .then((res) => {
          if (res.code == 200) {
            if (res.data.length > 0) {
              let list = res.data
              this.$dialog({
                data: {
                  title: this.$t('供方报价错误'),
                  list
                },
                modal: () => import('../components/MessageDialog.vue')
              })
            }
          }
        })
    },
    showNoticeDialog(list) {
      this.$dialog({
        data: {
          title: this.$t('供应商IP重复提示'),
          list
        },
        modal: () => import('../components/NoticeDialog.vue'),
        close: () => {
          this.cancelNoticeSameIp()
        }
      })
    },
    cancelNoticeSameIp() {
      this.$API.rfxList.cancelNoticeSameIpApi({ rfxId: this.$route.query.rfxId }).then((res) => {
        if (res.code === 200) {
          this.timer = setInterval(() => {
            this.pollingNoticeSameIp()
          }, 20 * 1000)
        }
      })
    },
    pollingNoticeSameIp() {
      this.$API.rfxList.pollingNoticeSameIpApi({ rfxId: this.$route.query.rfxId }).then((res) => {
        if (res.code === 200) {
          if (res.data?.showNotice) {
            this.timer && clearInterval(this.timer)
            let list = res.data.sameIpAddressDetailResponseList
            this.showNoticeDialog(list)
          }
        }
      })
    },
    getCountDown(v) {
      this.hasCountDown = v
    },
    getKtFlag() {
      this.$API.rfxList
        .supplierGetKtFlag({ companyCode: this.$route.query.companyCode })
        .then((res) => {
          if (res.code == 200) {
            this.ktFlag = res.data
          }
        })
    },
    goToTab(moduleType) {
      for (let index in this.RFXModuleTabList) {
        if (this.RFXModuleTabList[index].moduleType == moduleType) {
          this.handleTabChange(index, this.RFXModuleTabList[index])
        }
      }
    },
    async handleTabChange(index, item, type) {
      if (type === 'click') {
        // 只在点击时候判断，初始进来不判断
        this.diffTabData(index, item)
      } else {
        this.setCurrentTab(index, item)
      }
    },
    // 比较页签数据，提示保存
    async diffTabData(index, item) {
      let flag = false //内容是否变动
      // 判断基础信息页签、采购明细、策略报告是否变动
      if ([999, 14, 15].includes(this.moduleType) && this.$refs.mainContent?.isContentChange) {
        flag = await this.$refs.mainContent.isContentChange()
      }
      if (flag) {
        this.dialogTips(index, item)
      } else {
        this.setCurrentTab(index, item)
      }
    },
    // 提示
    dialogTips(index, item) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`当前表格内容未保存，是否继续？`)
        },
        success: () => {
          let methodName = this.getSaveMethodName()
          this.$refs.mainContent[methodName]().then(async (res) => {
            if (res.code === 200) {
              if (this.moduleType === 14) await this.delay(700) // 此处延时跳转是处理高度切换后表格未刷新问题
              this.setCurrentTab(index, item)
            }
          })
        }
      })
    },
    // 延时处理
    delay(ms) {
      return new Promise((resolve) => setTimeout(resolve, ms))
    },
    // 获取保存方法名称（此处未统一保存名称，保留之前命名）
    getSaveMethodName() {
      let _name = ''
      switch (this.moduleType) {
        case 999:
          _name = 'editProject'
          break
        case 14:
          _name = 'handleSaveDetails'
          break
        case 15:
          _name = 'save'
          break
        default:
          _name = ''
      }
      return _name
    },
    // 手动设置tab
    setCurrentTab(index, item) {
      this.$refs.mtTabsRef.activeTab = index
      this.moduleType = item.moduleType
    },
    getDetail(init = false, reRender = false) {
      this.$API.rfxDetail.getRFXDetailById({ id: this.$route.query.rfxId }).then((res) => {
        let rfxHeaderExpandResponseList = []
        res.data.rfxHeaderExpandResponseList?.forEach((v) => {
          rfxHeaderExpandResponseList.push(v.companyCode + '+' + v.purOrgCode + '+' + v.siteCode)
        })
        res.data.sourcingExpand = rfxHeaderExpandResponseList
        this.detailInfo = res.data
        // 判断白电非采
        if (
          this.firstInit &&
          ['BTTCL001', 'BTTCL002', 'BTTCL003'].includes(this.detailInfo.businessTypeCode)
        ) {
          this.firstInit = false
          this.getNoticeSameIp()
          this.getMessageSupplierInfo()
        }
        if (reRender) {
          this.keyIndex++
          this.$nextTick(() => {
            this.goToTab(this.moduleType)
          })
        }
        sessionStorage.setItem('selectCompanyId', res.data.companyId)
        sessionStorage.setItem('selectOrgId', res.data.purOrgId)
        //毛正文修改
        sessionStorage.setItem('rfxTransferStatus', res.data.transferStatus)
        sessionStorage.setItem('rfxGeneralType', res.data.rfxGeneralType)
        if (init) {
          this.serializeRFXModules()
          this.keyIndex++
          this.$nextTick(() => {
            this.goToTab(this.moduleType)
          })
        }
      })
    },
    async serializeRFXModules() {
      let params = {
        rfxCode: this.detailInfo.rfxCode
      }
      //评分规则
      await this.$API.rfxDetailTabRatingRules.ruleQuery(params).then((res) => {
        this.rfxScoreModel = res?.data?.scoreModel
      })
      this.getRFXModules()
    },
    getRFXModules() {
      this.$API.rfxDetail
        .getRFXModules({
          docId: this.$route.query.rfxId,
          docType: 'rfx'
        })
        .then((res) => {
          if (!res.data.moduleItems || res.data.moduleItems.length <= 0) {
            return
          }
          let _pageModules = JSON.parse(JSON.stringify(this.tagPageModules))
          if (this.rfxScoreModel === 3) {
            // 纯商务的评分规则  隐藏技术标
            let _index = _pageModules.indexOf(21)
            if (_index > -1) {
              _pageModules.splice(_index, 1)
            }
          }
          let _RFXModuleTabList = []
          res.data.moduleItems.forEach((item) => {
            // 如果有该tab页面
            if (_pageModules.includes(item.moduleType)) {
              // 处理采购明细中的动态列
              if (
                (item.moduleType == 4 && window.elementPermissionSet.includes('T_02_0150')) ||
                (item.moduleType == 9 &&
                  ((window.elementPermissionSet.includes('T_02_0152') &&
                    this.$route.query.source === 'rfq') ||
                    this.$route.query.source !== 'rfq')) ||
                (item.moduleType == 70 && window.elementPermissionSet.includes('T_02_0151'))
              ) {
                this.defineModuleFields(item)
                _RFXModuleTabList.push({
                  title: this.$t(item.moduleName),
                  ...item
                })
              } else if (![4, 9, 70].includes(item.moduleType)) {
                this.defineModuleFields(item)
                _RFXModuleTabList.push({
                  title: this.$t(item.moduleName),
                  ...item
                })
              }
            }
          })
          console.log('Tab  顺序', _RFXModuleTabList)
          _RFXModuleTabList.unshift({
            title: this.$t('基础信息'),
            moduleType: 999
          })
          // 商务标技术标放到基础信息前
          _RFXModuleTabList = this.moveBusinessAndTecItem(_RFXModuleTabList)

          let _source = this.$route.query.source
          if (_source == 'rfq') {
            //最初是‘询比价’时有这个要求，现在每个大厅都需要
            // let _findIndex = _RFXModuleTabList.findIndex(
            //   (e) => e.moduleType == 20
            // );
            // if (_findIndex > -1) {
            //   _RFXModuleTabList.forEach((e) => {
            //     e.moduleType = "定价";
            //   });
            // }
            _RFXModuleTabList.forEach((e) => {
              if (e.moduleType == 20) {
                e.title = this.$t('定价')
              }
            })
          }
          let _code = this.detailInfo.rfxPhaseCode
          if (_code == 'QUOTE' || _code == 'EVALUATION_BID') {
            if (this.detailInfo?.transferStatus > 43 && this.detailInfo?.transferStatus < 47) {
              // 核价阶段tab置顶
              let _findIndex = _RFXModuleTabList.findIndex((e) => e.moduleType == 70)
              if (_findIndex > -1) {
                _RFXModuleTabList = _RFXModuleTabList
                  .splice(_findIndex, 1)
                  .concat(_RFXModuleTabList)
              }
            } else {
              //询比价、评标   当前阶段为‘询比价、评标’，置顶
              let _findIndex = _RFXModuleTabList.findIndex((e) => e.moduleType == 9)
              if (_findIndex > -1) {
                _RFXModuleTabList = _RFXModuleTabList
                  .splice(_findIndex, 1)
                  .concat(_RFXModuleTabList)
              }
            }
          } else if (_code == 'DECIDE_BID') {
            //定价、定标   当前阶段为‘定标’，置顶
            let _findIndex = _RFXModuleTabList.findIndex((e) => e.moduleType == 20)
            if (_findIndex > -1) {
              _RFXModuleTabList = _RFXModuleTabList.splice(_findIndex, 1).concat(_RFXModuleTabList)
            }
          } else {
            let _findIndex = _RFXModuleTabList.findIndex((e) => e.moduleType == 4)
            if (_findIndex > -1) {
              _RFXModuleTabList = _RFXModuleTabList.splice(_findIndex, 1).concat(_RFXModuleTabList)
            }
          }
          // }
          this.RFXModuleTabList = _RFXModuleTabList
          this.moduleType = _RFXModuleTabList[0].moduleType
          // 测算入口肯定为14
          if (this.calculation == 1) {
            this.moduleType = 14
          }
          let approveList = [-4, -3, -2, 0, 1, 10]
          // 新建单据(this.$route.query.isNew)不能代表新建单据，如果不退出页面会导致isNew不刷新
          if (
            (this.$route.query.isNew && this.detailInfo.rfxPhaseCode === 'PROJECT_PREPARE') ||
            approveList.indexOf(this.detailInfo.approveStatus) > -1
          ) {
            this.goToTab(14)
          }
          if (
            this.detailInfo?.transferStatus > 43 &&
            this.detailInfo?.transferStatus < 47 &&
            window.elementPermissionSet.includes('T_02_0151')
          ) {
            this.goToTab(70)
          }
        })
    },
    // 商务标技术标移动到基础信息前面
    moveBusinessAndTecItem(list) {
      let _arr = cloneDeep(list)
      _arr = this.moveArrItem(_arr, 22)
      _arr = this.moveArrItem(_arr, 21)
      return _arr
    },
    // 移动数组数据到指定位置
    moveArrItem(list, moduleType) {
      const _findIndex = list.findIndex((item) => item.moduleType === moduleType)
      const _baseIndex = list.findIndex((item) => item.moduleType === 999)
      if (_findIndex === -1) return list
      const _find = list.splice(_findIndex, 1)[0]
      list.splice(_baseIndex, 0, _find)
      return list
    },
    // 获取看板流程
    getPhaseById() {
      this.$API.rfxSchedule
        .queryPlanPhaseByRfxId({ docId: this.$route.query.rfxId })
        .then((res) => {
          this.progressList = res.data || []
        })
    },
    //提交立项
    async saveProject() {
      // 判断采购明细、评分规则是否有列表未保存操作
      let newList = []
      let newFormList = []
      if (this.moduleType == 14 || this.moduleType == 25) {
        newList =
          this.$refs.mainContent.$refs.templateRef
            .getCurrentTabRef()
            .grid?.getCurrentViewRecords() ?? []
        if (JSON.stringify(this.$refs.mainContent.oldList) !== JSON.stringify(newList)) {
          this.dialogReminder(true)
        } else {
          this.dialogReminder()
        }
      } else if (this.moduleType == 15) {
        //判断策略报告是否有表单未保存数据
        newFormList = await this.$refs.mainContent.getFormsData()
        if (JSON.stringify(this.$refs.mainContent.formList) !== JSON.stringify(newFormList)) {
          this.dialogReminder(true)
        } else {
          this.dialogReminder()
        }
      } else {
        this.dialogReminder()
      }
    },
    dialogReminder(isNotSave) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: isNotSave
            ? this.$t(`当前表格内容未保存，是否继续？`)
            : this.$t("确认执行'提交立项'操作？")
        },
        success: () => {
          if (this.ktFlag) {
            this.saveProject2()
          } else {
            this.$API.rfxList
              .checkItemCodeSupplierCode({ rfxId: this.$route.query.rfxId })
              .then((res) => {
                if (res.data == 'success') {
                  this.saveProject2()
                } else {
                  this.$toast({
                    content: res.data,
                    type: 'warning'
                  })
                }
              })
          }
        }
      })
    },
    saveProject2() {
      if (this.loading) return
      this.loading = true
      this.$store.commit('startLoading')
      this.$API.rfxList
        .saveProject({
          rfxId: this.$route.query.rfxId,
          idList: [this.detailInfo.id]
        })
        .then(() => {
          this.$toast({
            content: this.$t('成功执行‘提交立项’操作'),
            type: 'success'
          })
          this.loading = false
          this.$store.commit('endLoading')
          this.reload()
          // location.reload();
        })
        .finally(() => {
          this.$store.commit('endLoading')
          this.loading = false
        })
    },
    //取消
    cancelProject() {
      this.$API.rfxList.cancelProject({ rfxId: this.$route.query.rfxId }).then(() => {
        this.$toast({
          content: this.$t('成功执行‘取消单据’操作'),
          type: 'success'
        })
        this.getDetail(true)
        this.getPhaseById()
      })
    },
    //重新推送价格
    pushPriceProject() {
      this.$API.rfxDetail.syncPriceQuota({ rfxId: this.$route.query.rfxId }).then(() => {
        this.$toast({
          content: this.$t('成功执行‘重新推送价格’操作'),
          type: 'success'
        })
        this.getDetail(true)
        this.getPhaseById()
      })
    },
    //保存
    async editProject() {
      if (this.moduleType === 24) {
        if (await this.$refs.mainContent.editProject()) {
          this.$toast({
            content: this.$t('成功执行‘保存’操作'),
            type: 'success'
          })
        }
      } else {
        this.$toast({
          content: this.$t('成功执行‘保存’操作'),
          type: 'success'
        })
      }
      this.getDetail(false, true)
      this.getPhaseById()
    },
    isAgGridComponent() {
      // 判断采购明细tab是否使用的是aggrid组件
      let flag = false
      if (
        (this.$route.query.source == 'rfq' &&
          ((this.detailInfo?.transferStatus > 1 && this.detailInfo.sourcingScenarios === 'STAGE') ||
            this.detailInfo.sourcingScenarios === 'LOGISTICS' ||
            ['odmin', 'smt', 'COMMON', 'sea_transport_annual', 'railway_transport_annual'].includes(
              this.detailInfo.sourcingScenarios
            ))) ||
        (this.$route.query.source == 'bidding_price' &&
          ['trunk_transport_annual'].includes(this.detailInfo.sourcingScenarios))
      ) {
        flag = true
      }
      return flag
    },
    //发布操作
    publishProject() {
      let hasItemTab = false
      let itemTabIndex = 0
      let itemTabItem = null
      const itemRequiredCols = []
      this.RFXModuleTabList.forEach((item, index) => {
        if (item.moduleType === 14) {
          hasItemTab = true
          itemTabIndex = index
          itemTabItem = item
          item.fieldDefines.forEach((i) => {
            if (i.required) {
              itemRequiredCols.push({ fieldCode: i.fieldCode, fieldName: i.fieldName })
            }
          })
        }
      })
      if (hasItemTab && this.isAgGridComponent() && this.detailInfo.buType === 'GF') {
        this.handleTabChange(itemTabIndex, itemTabItem, 'click')
        setTimeout(() => {
          // let submitItemTable = []
          // try {
          //   submitItemTable = this.$refs.mainContent?.getRowData()
          // } catch {
          //   submitItemTable = this.$refs.mainContent.$refs.templateRef
          //     .getCurrentTabRef()
          //     .grid.getCurrentViewRecords()
          // }
          const submitItemTable = this.$refs.mainContent?.getRowData()
          for (let i = 0; i < submitItemTable.length; i++) {
            const item = submitItemTable[i]
            const itemExtMapKeys = Object.keys(item.itemExtMap)
            for (let j = 0; j < itemRequiredCols.length; j++) {
              const requiredCol = itemRequiredCols[j]
              // 这仨在itemExtMap对象中
              if (itemExtMapKeys.includes(requiredCol.fieldCode)) {
                if (
                  (!item.itemExtMap[requiredCol.fieldCode] &&
                    item.itemExtMap[requiredCol.fieldCode] !== 0) ||
                  (Array.isArray(item.itemExtMap[requiredCol.fieldCode]) &&
                    item.itemExtMap[requiredCol.fieldCode].length === 0)
                ) {
                  this.$toast({
                    content: this.$t(`请填写第${Number(i + 1)}行的${requiredCol.fieldName}`),
                    type: 'error'
                  })
                  return
                }
              } else {
                if (
                  (!item[requiredCol.fieldCode] && item[requiredCol.fieldCode] !== 0) ||
                  (Array.isArray(item[requiredCol.fieldCode]) &&
                    item[requiredCol.fieldCode].length === 0)
                ) {
                  this.$toast({
                    content: this.$t(`请填写第${Number(i + 1)}行的${requiredCol.fieldName}`),
                    type: 'error'
                  })
                  return
                }
              }
            }
          }
          if (this.ktFlag) {
            this.fetchPublish()
          } else {
            this.$API.rfxList
              .checkItemCodeSupplierCode({ rfxId: this.$route.query.rfxId })
              .then((res) => {
                if (res.data == 'success') {
                  this.fetchPublish()
                } else {
                  this.$toast({
                    content: res.data,
                    type: 'warning'
                  })
                }
              })
          }
        }, 100)
        return
      }
      if (this.ktFlag) {
        this.fetchPublish()
      } else {
        this.$API.rfxList
          .checkItemCodeSupplierCode({ rfxId: this.$route.query.rfxId })
          .then((res) => {
            if (res.data == 'success') {
              this.fetchPublish()
            } else {
              this.$toast({
                content: res.data,
                type: 'warning'
              })
            }
          })
      }
    },
    fetchPublish() {
      this.$store.commit('startLoading')
      this.$API.rfxList
        .publishBid({
          rfxId: this.$route.query.rfxId,
          idList: [this.detailInfo.id]
        })
        .then(() => {
          this.$store.commit('endLoading')
          this.$toast({
            content: this.$t('成功执行‘发布’操作'),
            type: 'success'
          })
          this.reload()
        })
    },
    //流标操作
    failBidProject(failReason) {
      this.$API.rfxList
        .failBidById({
          failReason,
          rfxId: this.$route.query.rfxId,
          idList: [this.detailInfo.id]
        })
        .then(() => {
          this.$toast({
            content: this.$t('成功执行‘流标’操作'),
            type: 'success'
          })
          this.getDetail(true)
          this.getPhaseById()
        })
    },
    //复制操作
    copyProject() {
      this.$API.rfxList
        .copyAdd({
          rfxId: this.$route.query.rfxId
        })
        .then(() => {
          this.$toast({
            content: this.$t('成功执行‘复制’操作'),
            type: 'success'
          })
        })
    },
    initModuleArray() {
      // POR_ITEM(0, "需求明细"),
      // RECOMMEND_SUPPLIER(1, "推荐供方"),
      // RELATION_FILE(2, "相关文件"),
      // DESCRIPTION(3, "描述说明"),
      // CUSTOM(99,"自定义"),
      // HALL(4,"大厅"),
      // CANDIDATE_SUPPLIER(5,"候选供方"),
      // SOURCING_TEAM(6,"寻源团队"),
      // PLAN(7,"任务计划"),
      // CHECK_PRICE(8,"核价"),
      // BID_EVALUATION(9,"评标"),
      // OPERATE_LOG(10,"操作日志"),
      // GOODS_LIST(11,"货源清单"),
      // SUPPLIER_LIST(12,"供应商清单"),
      // INVITE_SUPPLIER(13,"供应商招募"),
      // RFX_ITEM(14,"采购明细"),
      // RFX_STRATEGY(15,"策略报告"),
      // RFX_TENDER(16,"询价招标"),
      // RFX_PRICE(17,"定价"),
      // RFX_BIDDING_VIEW(18,"竞价"),
      // RFX_HEADER(19,"RFX头部"),
      // DECIDE_BID(20,"定标"),
      // SKILL_BID(21,"技术标"),
      // BUSINESS_BID(22,"商务标"),
      // SOURCING_BID_EVALUATION(23,"询比价-评标"),
      // EVALUATION_RULE(25,"评分规则"),
      let _source = this.$route.query.source
      let _useModules = []
      if (_source === 'invite_bids') {
        //招投标
        _useModules = [
          4, //大厅
          14, //采购明细
          5, //候选供方
          6, //寻源团队
          3, //描述说明
          2, //相关文件
          15, //策略报告
          10, //操作日志
          9, //评标
          20, //定标
          21, //技术标
          22, //商务标
          25, //评分规则
          24 // 资质审查
        ]
      } else if (_source === 'bidding_price') {
        //竞价
        _useModules = [
          4, //大厅
          14, //采购明细
          5, //候选供方
          6, //寻源团队
          3, //描述说明
          2, //相关文件
          15, //策略报告
          10, //操作日志
          9, //评标
          20, //定标
          21, //技术标
          22, //商务标
          25, //评分规则
          24 // 资质审查
        ]
      } else {
        //询报价
        _useModules = [
          14, //采购明细
          70, //核价
          5, //候选供方
          6, //寻源团队
          3, //描述说明
          2, //相关文件
          15, //策略报告
          10, //操作日志
          9, //评标
          20, //定标
          24 // 资质审查
        ]
      }
      this.tagPageModules = _useModules
    },
    /**
     * 开标一览表
     */
    async preview() {
      let buffer = await this.$API.rfxPricing
        .previewSign({
          id: this.$route.query.rfxId
        })
        .catch(() => {})
      buffer?.data && (buffer = buffer.data)
      if (buffer instanceof Blob) {
        if (buffer.type === 'application/json') {
          try {
            const json = JSON.parse(await buffer.text())
            if (json.msg) {
              this.$toast({ content: json.msg, type: 'warning' })
            }
          } catch (error) {
            console.warn(error)
          }
        } else if (buffer.type === 'text/html') {
          const url = URL.createObjectURL(buffer)
          window.open(url)
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.hall-detail {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  min-width: 1200px;
  // height: 100vh;

  .main-context {
    flex: 1;
    transition: all 0.5s ease-in-out;
    display: flex;

    .main-context-right {
      flex: 1;
      overflow: auto;
      display: flex;
      flex-direction: column;

      /deep/ .tab-container {
        background: #fff;
      }
    }
    //.tab-hall {
    //  height: 100%;
    //  min-height: $mainMinHeight;
    //}

    //.middle-wrap {
    //  width: 100%;
    //  min-width: $mainMinWidth;
    //  flex: 1;
    //  padding: 10px;
    //  overflow-y: auto;
    //}
  }
}
.roundBox {
  width: 100%;
  padding: 10px;
  background: rgba(237, 161, 51, 1);
  border-radius: 4px;
  color: white;
}
</style>
