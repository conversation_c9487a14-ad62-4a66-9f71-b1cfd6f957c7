import { i18n } from '@/main.js'
import detailTemplate from '../components/detailTemplate.vue'

const columnData = () => {
  return [
    {
      field: 'nodeName',
      headerText: i18n.t('成本项明细'),
      width: 1000,
      cssClass: ''
    },
    {
      field: 'price',
      width: 200,
      headerText: i18n.t('总价'),
      valueConverter: {
        type: 'function',
        filter: (e) => {
          if (e == -999) {
            return '******'
          } else {
            return e
          }
        }
      }
    },
    {
      field: 'id',
      isPrimaryKey: true,
      visible: false
    },
    {
      field: 'id',
      visible: false
    },
    {
      field: 'enableItem',
      visible: false
    },
    {
      field: 'childrenList',
      visible: false
    },
    {
      field: 'leafNode',
      visible: false
    }
  ]
}
// 树状表
export const pageConfig = (params, url) => {
  return [
    {
      // gridId: "8e7bf6a0-d23f-43f2-90d6-01ad41ac3b1c",
      useToolTemplate: false,
      toolbar: {
        useBaseConfig: false,
        tools: [
          [
            {
              id: 'excelExport',
              icon: 'icon_solid_pushorder',
              title: i18n.t('导出')
            }
          ]
        ]
      },

      treeGrid: {
        useRowSelect: false,
        detailTemplate: () => {
          return { template: detailTemplate }
        },
        allowPaging: false,
        columnData: columnData(),
        childMapping: 'childrenList',
        // rowSelected: (e) => {
        //   _this.rowSelected(e);
        // },
        editSettings: {
          allowEditing: false,
          allowAdding: true,
          allowDeleting: true,
          mode: 'Row'
        },
        selectionSettings: { type: 'Single', checkboxOnly: false },
        // dataSource: [],

        asyncConfig: {
          url,
          queryBuilderWrap: 'queryBuilderDTO',
          params: params,
          recordsPosition: 'data',
          methods: 'get'
        }
      }
    }
  ]
}
