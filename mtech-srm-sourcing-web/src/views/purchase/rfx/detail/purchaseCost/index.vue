<template>
  <div class="full-height">
    <div class="top-container">
      <div class="top-info">
        <div class="left">
          <div class="title">
            {{ tempForm.costModelCode }}
            <span class="sub-title" :style="tempForm.costModelCode && 'margin-left: 20px;'">{{
              tempForm.costModelName
            }}</span>
          </div>
          <div class="info">
            <div class="item">{{ $t('版本号：') }}{{ tempForm.costModelVersionCode || '--' }}</div>
            <div class="item">{{ $t('物料编码：') }}{{ tempForm.materialCode || '--' }}</div>
            <div class="item">{{ $t('临时物编：') }}{{ tempForm.tempMaterialCode || '--' }}</div>
            <div class="item">{{ $t('阶梯数量：') }}{{ tempForm.stepValue || '--' }}</div>
            <div class="item">{{ $t('直送地：') }}{{ tempForm.deliveryPlace || '--' }}</div>
            <div class="item">{{ $t('业务方：采购方') }}</div>
          </div>
        </div>
        <div class="right">
          <vxe-button size="small" @click="handleExport(null)">{{ $t('导出') }}</vxe-button>
          <vxe-button size="small" @click="$router.go(-1)">{{ $t('返回') }}</vxe-button>
        </div>
      </div>
    </div>
    <div
      class="body-container"
      :class="[!leftExpand && 'left-hidden', !rightExpand && 'right-hidden']"
    >
      <!-- 左侧内容 -->
      <div class="letf container">
        <div class="content">
          <div class="body">
            <mt-form ref="leftTempFormRef" :model="tempForm" :rules="tempFormRules">
              <mt-row :gutter="10">
                <mt-col :span="6">
                  <mt-form-item prop="leftCurShowCode" :label="$t('当前显示')" label-style="left">
                    <vxe-select
                      v-model="tempForm.leftCurShowCode"
                      :options="curShowList"
                      :option-props="{ label: 'text', value: 'value' }"
                      clearable
                      :placeholder="$t('请选择')"
                      @change="(e) => handleCurShowChange('left', e)"
                    />
                  </mt-form-item>
                </mt-col>
                <mt-col :span="15">
                  <mt-form-item
                    prop="leftHistoryId"
                    :label="$t('物料+供应商+次数')"
                    label-style="left"
                  >
                    <vxe-input
                      v-model="tempForm.leftHistoryInfo"
                      readonly
                      clearable
                      type="search"
                      :placeholder="$t('请选择')"
                      @search-click="handleHistoryRecordChange('left')"
                      @clear="handleHistoryRecordClear('left')"
                    />
                  </mt-form-item>
                </mt-col>
                <mt-col :span="3">
                  <mt-form-item style="text-align: right">
                    <vxe-button
                      size="small"
                      :disabled="!tempForm.leftHistoryId"
                      @click="handleExport('left')"
                      >{{ $t('导出') }}</vxe-button
                    >
                  </mt-form-item>
                </mt-col>
              </mt-row>
            </mt-form>
          </div>
          <div class="form-info" v-if="extraFormItems.length || commonFormItems.length">
            <div class="title" @click="handleToggle('leftFormInfoShow')">
              <i class="vxe-icon-caret-down" v-show="!leftFormInfoShow" />
              <i class="vxe-icon-caret-up" v-show="leftFormInfoShow" />
              <span>{{ $t('报价信息') }}</span>
            </div>
            <div v-show="leftFormInfoShow" class="body">
              <custom-form :data="leftExtraFormData" :form-items="extraFormItems" type="extra" />
              <div class="tip" v-if="extraFormItems.length">{{ priceTipInfo }}</div>
              <custom-form :data="leftCommonFormData" :form-items="commonFormItems" type="common" />
            </div>
          </div>
          <div class="table-info" v-if="leftExpand">
            <div class="title" @click="handleToggle('leftTableShow')">
              <i class="vxe-icon-caret-down" v-show="!leftTableShow" />
              <i class="vxe-icon-caret-up" v-show="leftTableShow" />
              <span>{{ $t('其他信息') }}</span>
            </div>
            <div class="body" v-show="leftTableShow">
              <sc-table
                ref="leftTableRef"
                row-id="id"
                :keep-source="true"
                :tree-config="treeConfig"
                :is-show-right-btn="false"
                :loading="leftLoading"
                :columns="columns"
                :table-data="leftTableData"
                :cell-style="(e) => handleCellStyle('left', e)"
                @scroll="(e) => handleScroll('left', e)"
              />
            </div>
          </div>
        </div>
        <div v-show="rightExpand" class="operate left-operate" @click="handleToggle('leftExpand')">
          <i class="vxe-icon-arrow-left" />
        </div>
      </div>
      <!-- 右侧内容 -->
      <div class="right container">
        <div v-show="leftExpand" class="operate right-operate" @click="handleToggle('rightExpand')">
          <i class="vxe-icon-arrow-right" />
        </div>
        <div class="content" v-if="rightExpand">
          <div class="body">
            <mt-form ref="rightTempFormRef" :model="tempForm" :rules="tempFormRules">
              <mt-row :gutter="10">
                <mt-col :span="6">
                  <mt-form-item prop="rightCurShowCode" :label="$t('当前显示')" label-style="left">
                    <vxe-select
                      v-model="tempForm.rightCurShowCode"
                      :options="curShowList"
                      :option-props="{ label: 'text', value: 'value' }"
                      clearable
                      :placeholder="$t('请选择')"
                      @change="(e) => handleCurShowChange('right', e)"
                    />
                  </mt-form-item>
                </mt-col>
                <mt-col :span="15">
                  <mt-form-item
                    prop="rightHistoryId"
                    :label="$t('物料+供应商+次数')"
                    label-style="left"
                  >
                    <vxe-input
                      v-model="tempForm.rightHistoryInfo"
                      readonly
                      clearable
                      type="search"
                      :placeholder="$t('请选择')"
                      @search-click="handleHistoryRecordChange('right')"
                      @clear="handleHistoryRecordClear('right')"
                    />
                  </mt-form-item>
                </mt-col>
                <mt-col :span="3">
                  <mt-form-item style="text-align: right">
                    <vxe-button
                      size="small"
                      :disabled="!tempForm.rightHistoryId"
                      @click="handleExport('right')"
                      >{{ $t('导出') }}</vxe-button
                    >
                  </mt-form-item>
                </mt-col>
              </mt-row>
            </mt-form>
          </div>
          <div class="form-info" v-if="extraFormItems.length || commonFormItems.length">
            <div class="title" @click="handleToggle('rightFormInfoShow')">
              <i class="vxe-icon-caret-down" v-show="!rightFormInfoShow" />
              <i class="vxe-icon-caret-up" v-show="rightFormInfoShow" />
              <span>{{ $t('报价信息') }}</span>
            </div>
            <div class="body" v-show="rightFormInfoShow">
              <custom-form :data="rightExtraFormData" :form-items="extraFormItems" type="extra" />
              <div class="tip" v-if="extraFormItems.length">{{ priceTipInfo }}</div>
              <custom-form
                :data="rightCommonFormData"
                :form-items="commonFormItems"
                type="common"
              />
            </div>
          </div>
          <div class="table-info">
            <div class="title" @click="handleToggle('rightTableShow')">
              <i class="vxe-icon-caret-down" v-show="!rightTableShow" />
              <i class="vxe-icon-caret-up" v-show="rightTableShow" />
              <span>{{ $t('其他信息') }}</span>
            </div>
            <div class="body" v-show="rightTableShow">
              <sc-table
                ref="rightTableRef"
                row-id="id"
                :keep-source="true"
                :tree-config="treeConfig"
                :is-show-right-btn="false"
                :loading="rightLoading"
                :columns="columns"
                :table-data="rightTableData"
                :cell-style="(e) => handleCellStyle('right', e)"
                @scroll="(e) => handleScroll('right', e)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'
import CustomForm from './components/customForm.vue'
import mixin from './config/mixin'
import selectMixin from './config/selectMixin.js'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: { ScTable, CustomForm },
  mixins: [mixin, selectMixin],
  data() {
    return {
      treeConfig: {
        children: 'itemList',
        expandAll: true
      },
      columns: [],
      dynamicColumns: [],
      leftTableData: [],
      rightTableData: [],
      leftLoading: false,
      rightLoading: false,
      leftExpand: true,
      rightExpand: true,
      extraFormItems: [],
      extraFormula: [],
      leftExtraFormData: {},
      rightExtraFormData: {},
      commonFormItems: [],
      leftCommonFormData: {},
      rightCommonFormData: {},
      tempForm: {
        leftCurShowCode: 'Analysis',
        rightCurShowCode: 'Estimate'
      },
      leftSelectInfo: null,
      rightSelectInfo: null,
      leftFormInfoShow: true,
      leftTableShow: true,
      rightFormInfoShow: true,
      rightTableShow: true
    }
  },
  computed: {
    leftTableRef() {
      return this.$refs.leftTableRef.$refs.xGrid
    },
    rightTableRef() {
      return this.$refs.rightTableRef.$refs.xGrid
    },
    priceTipInfo() {
      const arr = []
      this.extraFormula.forEach((item) => {
        const { columnName, calculationFormulaSpec } = item
        const spec =
          !calculationFormulaSpec && calculationFormulaSpec !== 0 ? '' : calculationFormulaSpec
        arr.push(columnName + '=' + spec)
      })
      return arr.join(';')
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.getCostModelDetailColumns()
      this.getDefaultCostModelPrice('left', 'Analysis')
      this.getDefaultCostModelPrice('right', 'Estimate')
    },
    // 选择报价
    handleCurShowChange(type, e) {
      e.value && this.$refs[type + 'TempFormRef'].validateField(type + 'CurShowCode')
      this.handleHistoryRecordClear(type)
    },
    // 选择历史记录
    handleHistoryRecordChange(prefix) {
      this.$refs[prefix + 'TempFormRef'].validateField(prefix + 'CurShowCode', (valid) => {
        if (valid) {
          const { rfxId, biddingItemId } = this.$route.query
          this.$dialog({
            modal: () => import('./dialog/historyRecordsDialog.vue'),
            data: {
              title: this.$t('选择【物料+供应商+次数】'),
              type: this.tempForm[prefix + 'CurShowCode'],
              rfxHeaderId: rfxId,
              biddingItemId
            },
            success: (data) => {
              this.serializeTempForm(prefix, data || {})
              this.getDataListById(this.tempForm[prefix + 'CurShowCode'], prefix, data.id)
            }
          })
        }
      })
    },
    // 清空历史记录
    handleHistoryRecordClear(prefix) {
      this.$set(this.tempForm, prefix + 'HistoryId', null)
      this.$set(this.tempForm, prefix + 'HistoryInfo', null)

      this[prefix + 'ExtraFormData'] = this.serializeFormData([], 'extra')
      this[prefix + 'CommonFormData'] = this.serializeFormData([], 'common')
      this[prefix + 'TableData'] = this.serializeItemList([])
      this.$nextTick(() => this[prefix + 'TableRef'].setAllTreeExpand(true))
    },
    // 获取配置列、表单信息
    async getCostModelDetailColumns() {
      const params = {
        costModelId: this.$route.query.costModelId
      }
      const res = await this.$API.costModel.getCostModelDetailColumns(params)
      if (res.code === 200 && res.data) {
        this.formateColumns(res.data)
        this.$refs.leftTableRef.handleResize()
        this.$refs.rightTableRef.handleResize()
      }
    },
    // 查询默认成本分析报价信息
    async getDefaultCostModelPrice(prefix, type) {
      const params = {
        rfxHeaderId: this.$route.query.rfxId,
        biddingItemId: this.$route.query.biddingItemId,
        page: { current: 1, size: 20 }
      }
      this[prefix + 'Loading'] = true
      const res = await this.$API.rfxCostModel['queryDefaultCostModel' + type](params).catch(() => {
        this[prefix + 'Loading'] = false
      })
      this[prefix + 'Loading'] = false
      if (res.code === 200) {
        this.serializeTempForm(prefix, res.data || {})
        this.getDataListById(type, prefix, res.data.id)
      }
    },
    // 根据id查询成本分析、成本测算列表
    async getDataListById(type, prefix, id) {
      this[prefix + 'Loading'] = true
      const res = await this.$API.rfxCostModel['query' + type + 'ById']({ id }).catch(
        () => (this[prefix + 'Loading'] = false)
      )
      this[prefix + 'Loading'] = false
      if (res.code === 200) {
        this[prefix + 'ExtraFormData'] = this.serializeFormData(res.data?.extraList || [], 'extra')
        this[prefix + 'CommonFormData'] = this.serializeFormData(
          res.data?.formValueList || [],
          'common'
        )
        this[prefix + 'TableData'] = this.serializeItemList(res.data?.itemList || [])
        this.$nextTick(() => this[prefix + 'TableRef'].setAllTreeExpand(true))
      }
    },
    // 导出
    async handleExport(prefix) {
      if (prefix) {
        const params = {
          id: this.tempForm[prefix + 'HistoryId']
        }
        const type = this.tempForm[prefix + 'CurShowCode']
        const res = await this.$API.rfxCostModel['export' + type](params)
        if (res.data) {
          this.$toast({ type: 'success', content: this.$t('正在导出，请稍后！') })
          download({ fileName: getHeadersFileName(res), blob: res.data })
        }
      } else {
        const { leftCurShowCode, leftHistoryId, rightCurShowCode, rightHistoryId } = this.tempForm
        const costModelPriceIdList = [] // 成本分析ID列表
        const estimateIdList = [] // 成本测算ID列表
        leftCurShowCode === 'Analysis' && leftHistoryId && costModelPriceIdList.push(leftHistoryId)
        rightCurShowCode === 'Analysis' &&
          rightHistoryId &&
          costModelPriceIdList.push(rightHistoryId)

        leftCurShowCode === 'Estimate' && leftHistoryId && estimateIdList.push(leftHistoryId)
        rightCurShowCode === 'Estimate' && rightHistoryId && estimateIdList.push(rightHistoryId)

        const { rfxId, biddingItemId } = this.$route.query
        const params = {
          rfxHeaderId: rfxId,
          biddingItemId,
          costModelPriceIdList,
          estimateIdList
        }
        const res = await this.$API.rfxCostModel.exportCostModelContrast(params)
        if (res.data) {
          this.$toast({ type: 'success', content: this.$t('正在导出，请稍后！') })
          download({ fileName: getHeadersFileName(res), blob: res.data })
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;

  .top-container {
    height: 80px;
    background-color: #fff;
    padding: 10px;
  }
  .body-container {
    display: flex;
    justify-content: space-between;
    height: calc(100% - 80px);
  }
}
.top-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 10px;
  height: 100%;
  background-color: rgba(99, 134, 193, 0.08);
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  box-shadow: 0 0 10px 0 hsla(0, 7%, 50%, 0.06);

  .left {
    .title {
      height: 28px;
      line-height: 28px;
      font-size: 20px;
      font-weight: 600;
      .sub-title {
        color: #9a9a9a;
        font-size: 16px;
        font-weight: 500;
      }
    }
    .info {
      display: flex;
      height: 22px;
      line-height: 22px;
      .item {
        margin-right: 20px;
      }
    }
  }
}
.container {
  display: flex;
  height: 100%;
  width: 100%;
  background-color: #fff;

  .title {
    height: 35px;
    line-height: 35px;
    font-weight: bold;
    color: #fff;
    background-color: #3c435e;
    i {
      margin: auto 8px;
    }
  }
  .body {
    padding: 10px 15px;
    .tip {
      line-height: 22px;
      color: red;
      font-weight: bold;
    }
  }

  .content {
    width: calc(100% - 20px);
    border: 1px solid #e8e8e8;
    margin-bottom: 10px;
    .form-info {
      margin-bottom: 5px;
      .mt-form {
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 4 - 10px));
        justify-content: space-between;
        grid-gap: 5px;
      }
    }
  }
  .operate {
    margin: auto;
    width: 15px;
    height: 80px;
    line-height: 80px;
    border: 1px solid #e8e8e8;
    color: #3c435e;
    font-weight: bold;
    text-align: center;
  }
  .left-operate {
    border-radius: 0 8px 8px 0;
    margin-right: 5px;
  }
  .right-operate {
    border-radius: 8px 0 0 8px;
    margin-left: 5px;
  }
}
.left-hidden {
  .container:first-child {
    width: 20px;
    overflow: hidden;
    .content {
      display: none;
    }
    .left-operate {
      transform: rotate(180deg);
      border-radius: 8px 0 0 8px;
    }
  }
  .container:last-child {
    .content {
      width: 100%;
    }
    .operate {
      display: none;
    }
  }
}
.right-hidden {
  .container:first-child {
    .content {
      width: 100%;
    }
    .operate {
      display: none;
    }
  }
  .container:last-child {
    width: 20px;
    overflow: hidden;
    .content {
      display: none;
    }
    .right-operate {
      transform: rotate(180deg);
      border-radius: 0 8px 8px 0;
    }
  }
}
::v-deep {
  .table-tool-bar {
    height: 0;
  }
  .mt-form-item {
    margin-bottom: 0;
  }
  .item-label {
    .label {
      max-width: 200px;
      margin: 0 5px 0 5px;
      white-space: wrap;
      font-size: 12px;
    }
  }
  .vxe-input {
    width: 100%;
    height: 30px;
    line-height: 30px;
  }
}
</style>
