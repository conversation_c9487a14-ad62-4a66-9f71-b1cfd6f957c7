<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-template-page ref="table" :template-config="pageConfig" />
    </div>
  </mt-dialog>
</template>

<script>
import { historyRecordsColumnData } from './config'

export default {
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  computed: {
    pageConfig() {
      const { rfxHeaderId, biddingItemId, type } = this.modalData
      const urlName =
        type === 'Analysis' ? 'queryHistoryCostModelPriceUrl' : 'queryHistoryCostModelEstimateUrl'
      const config = [
        {
          useToolTemplate: false,
          toolbar: [],
          grid: {
            allowFiltering: true,
            allowSelection: true,
            selectionSettings: {
              checkboxOnly: false
            },
            columnData: historyRecordsColumnData,
            asyncConfig: {
              url: this.$API.rfxCostModel[urlName],
              params: {
                rfxHeaderId,
                biddingItemId
              }
            },
            recordDoubleClick: this.recordDoubleClick
          }
        }
      ]
      return config
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    recordDoubleClick(e) {
      this.$emit('confirm-function', e.rowData)
    },
    confirm() {
      const _selectRecords = this.$refs.table.getCurrentUsefulRef().ejsRef.getSelectedRecords()
      if (!_selectRecords.length) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        return
      }
      this.$emit('confirm-function', _selectRecords[0])
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
