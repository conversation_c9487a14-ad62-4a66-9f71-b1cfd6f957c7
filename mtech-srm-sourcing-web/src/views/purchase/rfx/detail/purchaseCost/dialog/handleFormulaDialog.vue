<template>
  <div>
    <mt-dialog ref="dialog" css-class="create-proj-dialog" :header="header" @beforeClose="cancel">
      <div class="dialog-content">
        <Calculator :item-list="items" @success="success" @error="error" />
      </div>
    </mt-dialog>
  </div>
</template>
<script>
import Calculator from 'COMPONENTS/Calculator'
export default {
  components: {
    Calculator
  },
  data() {
    return {
      items: this.modalData.items,
      editStatus: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    success(formula) {
      this.$emit('confirm-function', { calculationFormula: formula })
    },
    error() {
      this.$toast({ type: 'error', content: this.$t('公式错误') })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  padding-top: 20px;
}
</style>
