<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel" size="small">
    <div class="dialog-content">
      <mt-form>
        <mt-form-item
          prop="tecOpenBidTime"
          :label="$t('技术开标时间：')"
          v-if="type === 'tec' && fieldShow('tecOpenBidTime')"
        >
          <mt-date-time-picker
            v-model="dateTimeForm.tecOpenBidTime"
            :min="minTime"
            float-label-type="Never"
            width="100%"
            :placeholder="$t('请选择技术开标时间')"
          ></mt-date-time-picker>
        </mt-form-item>
        <mt-form-item
          prop="tecScoreEndTime"
          :label="$t('技术评分结束时间：')"
          v-if="type === 'tec' && fieldShow('tecScoreEndTime')"
        >
          <mt-date-time-picker
            v-model="dateTimeForm.tecScoreEndTime"
            :min="minTime"
            float-label-type="Never"
            width="100%"
            :placeholder="$t('请选择技术评分结束时间')"
          ></mt-date-time-picker>
        </mt-form-item>
        <mt-form-item
          prop="tenderStartTime"
          :label="$t('商务投标时间：')"
          v-if="type === 'business' && fieldShow('tenderStartTime')"
        >
          <mt-date-time-picker
            v-model="dateTimeForm.tenderStartTime"
            :min="minTime"
            float-label-type="Never"
            width="100%"
            :placeholder="$t('请选择商务投标时间')"
          ></mt-date-time-picker>
        </mt-form-item>
        <mt-form-item
          prop="openBidTime"
          :label="$t('商务开标时间：')"
          v-if="type === 'business' && fieldShow('openBidTime')"
        >
          <mt-date-time-picker
            v-model="dateTimeForm.openBidTime"
            :min="minTime"
            float-label-type="Never"
            width="100%"
            :placeholder="$t('请选择商务开标时间')"
          ></mt-date-time-picker>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      dateTimeForm: {
        tecOpenBidTime: null,
        tecScoreEndTime: null,
        tenderStartTime: null,
        openBidTime: null
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },

  computed: {
    header() {
      return this.modalData.title
    },
    // 技术投标 || 商务投标
    type() {
      return this.modalData.type
    },
    minTime() {
      return new Date()
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    // 商务标设置默认事件
    if (this.type === 'business') {
      this.dateTimeForm = Object.assign({}, this.dateTimeForm, {
        tenderStartTime: this.modalData.strategyResponse.tenderStartTime,
        openBidTime: this.modalData.strategyResponse.openBidTime
      })
    }
  },
  methods: {
    fieldShow(field) {
      if (field) {
        let _find = this.modalData?.rfxStrategyConfig.find((item) => item.strategyCode === field)
        return _find ? true : false
      }
      return true
    },
    confirm() {
      this.validTime()
      if (!this.validTime()) return
      let _data =
        this.type === 'business'
          ? {
              tenderStartTime: new Date(this.dateTimeForm.tenderStartTime).getTime(),
              openBidTime: new Date(this.dateTimeForm.openBidTime).getTime()
            }
          : {
              tecBidStartTime: new Date().getTime(),
              tecOpenBidTime: new Date(this.dateTimeForm.tecOpenBidTime).getTime(),
              tecScoreEndTime: new Date(this.dateTimeForm.tecScoreEndTime).getTime()
            }
      _data.rfxCode = this.modalData.rfxCode
      this.$API.rfxDetail[this.type === 'business' ? 'settingTenderTime' : 'settingTecTime']({
        ..._data
      }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: res.msg, type: 'success' })
          this.$emit('confirm-function')
        }
      })
    },

    validTime() {
      let flag = true
      let validateMap = {
        tecOpenBidTime: {
          value: this.dateTimeForm.tecOpenBidTime,
          msg: this.$t('技术开标时间不能为空')
        },
        tecScoreEndTime: {
          value: this.dateTimeForm.tecScoreEndTime,
          msg: this.$t('技术评分结束时间不能为空')
        }
      }
      if (this.type === 'business') {
        validateMap = {
          tenderStartTime: {
            value: this.dateTimeForm.tenderStartTime,
            msg: this.$t('商务投标时间不能为空')
          },
          openBidTime: {
            value: this.dateTimeForm.openBidTime,
            msg: this.$t('商务开标时间不能为空')
          }
        }
      }
      for (const key in validateMap) {
        if (Object.hasOwnProperty.call(validateMap, key)) {
          const element = validateMap[key]
          if (this.fieldShow(key) && !element.value && element.value !== 0) {
            this.$toast({ content: element.msg, type: 'warning' })
            flag = false
            break
          }
        }
      }
      return flag
    },

    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  width: 100%;
  p {
    margin-bottom: 20px;
  }
}
</style>
