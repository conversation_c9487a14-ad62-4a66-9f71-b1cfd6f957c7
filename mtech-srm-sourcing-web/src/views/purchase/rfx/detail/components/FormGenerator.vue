<template>
  <mt-form :model="form" :rules="rules" ref="form" class="form-generator-form">
    <template v-for="(formItem, index) in fieldDefines">
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'text'"
      >
        <mt-input
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          :show-clear-button="false"
          @change="handleFormItemChange($event, formItem)"
          :placeholder="$t(formItem.form.label)"
          :label="$t(formItem.form.label)"
          type="text"
          :maxlength="formItem.form.maxlength"
          :multiline="formItem.form.multiline"
        />
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'number'"
      >
        <mt-input-number
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          @change="handleFormItemChange($event, formItem)"
          :placeholder="$t(formItem.form.label)"
          :min="typeof formItem.form.min === 'number' ? formItem.form.min : 0"
          :max="formItem.form.max"
          :show-clear-button="false"
        />
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'select'"
      >
        <mt-select
          v-if="formItem.form.modalName == 'currencyName'"
          :data-source="formItem.form.dataSource"
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          :show-clear-button="true"
          @select="handleFormSelectItemChange($event, formItem)"
          @change="handleFormItemChange($event, formItem)"
          :fields="{ text: 'text', value: 'value' }"
          :placeholder="$t(formItem.form.label)"
          :allow-filtering="formItem.form.modalName == 'currencyName'"
          :filtering="formItem.form.modalName == 'currencyName' ? filteringCurrency : ''"
        />
        <mt-select
          v-else-if="formItem.form.modalName == 'exchangeRateName'"
          :data-source="formItem.form.dataSource"
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          :show-clear-button="true"
          @select="handleFormSelectItemChange($event, formItem)"
          @change="handleFormItemChange($event, formItem)"
          :fields="{ text: 'text', value: 'value' }"
          :placeholder="$t(formItem.form.label)"
          :allow-filtering="formItem.form.allowFiltering"
          filter-type="Contains"
        />
        <mt-select
          v-else
          :data-source="formItem.form.dataSource"
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          :show-clear-button="true"
          @select="handleFormSelectItemChange($event, formItem)"
          @change="handleFormItemChange($event, formItem)"
          :fields="{ text: 'text', value: 'value' }"
          :placeholder="$t(formItem.form.label)"
          :allow-filtering="formItem.form.allowFiltering"
          :filtering="formItem.form.filtering"
        />
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'multiSelect'"
      >
        <mt-tooltip :content="form[formItem.form.modalName]">
          <mt-multi-select
            :data-source="formItem.form.dataSource"
            v-model="form[formItem.form.modalName]"
            :disabled="formItem.form.readonly"
            :show-clear-button="false"
            @change="handleFormItemChange($event, formItem)"
            :placeholder="$t(formItem.form.label)"
            v-if="formItem.form.readonly"
          />
        </mt-tooltip>
        <mt-multi-select
          :data-source="formItem.form.dataSource"
          v-model="form[formItem.form.modalName]"
          :show-clear-button="false"
          :allow-filtering="true"
          filter-type="Contains"
          @change="handleFormItemChange($event, formItem)"
          :placeholder="$t(formItem.form.label)"
          v-if="!formItem.form.readonly"
        />
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :label="$t(formItem.form.interactionLabel)"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        :key="formItem.form.interactionModel"
        v-if="formItem.form.interactionModel"
      >
        <mt-input
          v-model="form[formItem.form.interactionModel]"
          :show-clear-button="false"
          :placeholder="$t(formItem.form.interactionLabel)"
          disabled
        />
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'date'"
      >
        <mt-date-picker
          @change="handleFormItemChange($event, formItem)"
          :open-on-focus="true"
          format="yyyy-MM-dd"
          time-stamp
          :show-clear-button="false"
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          :placeholder="$t(formItem.form.label)"
          :min="formItem.form.min"
          :max="formItem.form.max"
        />
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'time'"
      >
        <mt-time-picker
          @change="handleFormItemChange($event, formItem)"
          :open-on-focus="true"
          :show-clear-button="false"
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          :placeholder="$t(formItem.form.label)"
      /></mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'datetime'"
      >
        <mt-date-time-picker
          @change="handleFormItemChange($event, formItem)"
          :open-on-focus="true"
          format="yyyy-MM-dd HH:mm:ss"
          time-stamp
          :show-clear-button="false"
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          :placeholder="$t(formItem.form.label)"
          :min="formItem.form.min"
          :max="formItem.form.max"
        />
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'dropdown-tree'"
      >
        <mt-drop-down-tree
          @change="handleFormItemChange($event, formItem)"
          :open-on-focus="true"
          :show-clear-button="false"
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          :placeholder="$t(formItem.form.label)"
          :fields="[]"
        />
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'checkbox'"
      >
        <mt-select
          :data-source="[
            { text: $t('是'), value: 1 },
            { text: $t('否'), value: 0 }
          ]"
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          :show-clear-button="true"
          @change="handleFormItemChange($event, formItem)"
          :fields="{ text: 'text', value: 'value' }"
          :placeholder="$t(formItem.form.label)"
        />
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.field"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'allocationRatio'"
      >
        <AllocationRatio
          :value="form[formItem.form.modalName]"
          @input="(v) => (form[formItem.fieldCode] = v)"
          :disabled="formItem.form.readonly"
        />
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.field"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'selectedItemCode'"
        label-style="top"
        label-width="170px"
      >
        <SelectedItemCode
          :value="form[formItem.form.modalName]"
          @input="(v) => (form[formItem.fieldCode] = v)"
          @change="pcbChange"
          :readonly="formItem.form.readonly"
        />
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.field"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'getComponentCode'"
        label-style="top"
        label-width="170px"
      >
        <div class="get-component-code" v-if="showAueryItemBom" @click="queryItemBom">
          {{ $t('获取组件号') }}
        </div>
        <div class="get-component-code" v-else>{{ $t('已获取') }}</div>
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.field"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'debounce-select'"
      >
        <debounce-filter-select
          v-model="form[formItem.form.modalName]"
          :request="getCurrentEmployees"
          :data-source="currentEmployees"
          :disabled="formItem.form.readonly"
          @change="handleFormItemChange($event, formItem)"
          :show-clear-button="false"
          :fields="{ text: 'text', value: 'uid' }"
          :placeholder="$t(formItem.form.label)"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :key="index"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'null'"
      >
        <!-- 为了布局，空的占位符-->
      </mt-form-item>
      <!-- 做存展示的input框，查看超出部分内容-->
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'span'"
      >
        <mt-input
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          :placeholder="$t(formItem.form.label)"
          :label="$t(formItem.form.label)"
          type="text"
          :title="form[formItem.form.modalName]"
        />
      </mt-form-item>
    </template>
    <slot :records="fieldDefines"></slot>
  </mt-form>
</template>

<script>
import { cloneDeep } from 'lodash'
// 这个组件跟着需求一步步做的，到后面就非常恶了心，并不比直接页面写死然后if-else好
import AllocationRatio from 'ROUTER_PURCHASE_RFX/components/AllocationRatio'
import debounceFilterSelect from 'ROUTER_PURCHASE_RFX/components/debounceFilterSelect'
import SelectedItemCode from 'COMPONENTS/NormalEdit/selectPcbCode'
export default {
  name: 'FormGenerator',
  props: {
    fieldDefines: {
      type: Array,
      required: true
    },
    formFieldConfig: {
      type: Object,
      required: true
    },
    userSelect: {
      type: Boolean,
      default: false
    },
    sourcingExpandList: {
      type: Array,
      // required: true,
      default: () => {
        return []
      }
    }
  },
  components: {
    AllocationRatio,
    debounceFilterSelect,
    SelectedItemCode
  },
  data() {
    return {
      showAueryItemBom: true,
      callBackArrAmount: 0,
      form: {},
      rules: {},
      currentEmployees: [],
      oldForm: {},
      pcbData: {
        siteCode: ''
      }
    }
  },
  watch: {
    fieldDefines: {
      immediate: true,
      handler() {
        this.handlePropsChange()
      }
    }
  },
  mounted() {
    if (this.userSelect) {
      this.getCurrentEmployees({ text: '' })
    }
  },
  methods: {
    queryItemBom() {
      if (!this.form.pcbCode) {
        this.$toast({
          content: this.$t('请先选择PCB！'),
          type: 'warning'
        })
        return
      }
      const params = {
        pcbCode: this.form.pcbCode,
        siteCode: this.pcbData.siteCode
      }
      this.$store.commit('startLoading')
      this.$API.rfxDetail
        .getMaterialInfoByPCB(params)
        .then((r) => {
          this.$store.commit('endLoading')
          if (r.code === 200) {
            this.showAueryItemBom = false
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    pcbChange() {
      this.showAueryItemBom = true
    },
    handlePropsChange() {
      if (this.fieldDefines.length && this.formFieldConfig) {
        let _form = cloneDeep(this.form)
        for (const field of this.fieldDefines) {
          const config = this.formFieldConfig[field.fieldCode]
          if (config) {
            this.$set(field, 'form', {
              col: 4,
              handler: () => {},
              readonly: field.readonly,
              ...config,
              required: !!field.required,
              label: field.fieldName,
              modalName: field.fieldCode,
              dataSource: config.dataSource || []
            })
            if (config.defaultValue) {
              if (config.type === 'select' && !config.api) {
                config.dataSource = Array.isArray(config?.dataSource) ? config?.dataSource : []
                this.form[field.fieldCode] = config.dataSource.find(
                  (item) => item.value === config.defaultValue()
                )?.value
              } else {
                this.form[field.fieldCode] = config.defaultValue()
              }
            }
            if (config.api) {
              this.callBackArrAmount++
              this.$store.commit('startLoading')
              config.api
                .then((data) => {
                  this.callBackArrAmount--
                  if (this.callBackArrAmount == 0) {
                    this.$store.commit('endLoading')
                  }
                  this.$set(field.form, 'dataSource', data)
                  if (config.defaultValue && config.type !== 'multiSelect') {
                    this.form[field.fieldCode] = data.find(
                      (item) => item.value === config.defaultValue()
                    )?.value
                  }
                })
                .catch(() => {
                  this.callBackArrAmount--
                  this.$store.commit('endLoading')
                })
            }
            if (field.required) {
              this.$set(this.rules, field.fieldCode, [
                {
                  required: true,
                  message: this.$t('请输入') + field.fieldName
                }
              ])
            }
            // if (config.valid) {
            //   this.$set(this.rules, field.fieldCode, config.valid);
            // }
            _form[field.fieldCode] = config.defaultValue ? config.defaultValue() : ''
            // this.$set(
            //   this.form,
            //   field.fieldCode,
            //   config.defaultValue ? config.defaultValue() : ""
            // );
          } else {
            field.form = {}
          }
        }
        this.form = _form
        this.oldForm = { ..._form }
      }
    },
    parentGetFormData() {
      return new Promise((resolve, reject) => {
        this.$refs.form.validate((valid) => {
          valid ? resolve(this.form) : reject()
        })
      })
    },
    handleFormItemChange(event, formItem) {
      if (formItem.form.interactionModel && formItem.form.dataSource.length) {
        this.$set(
          this.form,
          formItem.form.interactionModel,
          formItem.form.dataSource.find((item) => item.value === event.value)[
            formItem.form.interactionModel
          ]
        )
      }
      formItem.form.handler(event, formItem, this)
      if (formItem.fieldCode == 'sourcingExpand') {
        let expandSaveRequestList = []
        event.value?.forEach((v) => {
          formItem.form.dataSource.forEach((x) => {
            if (v == x.value) {
              expandSaveRequestList.push(x.data)
            }
          })
        })
        this.form.expandSaveRequestList = expandSaveRequestList
        formItem.form.handler(expandSaveRequestList)
      } else if (formItem.fieldCode == 'siteName') {
        this.pcbData.siteCode = event.itemData?.data?.siteCode
      }
    },
    handleFormSelectItemChange(event, formItem) {
      if (this[formItem.fieldCode + 'ChangeHandler']) {
        this[formItem.fieldCode + 'ChangeHandler'](event.itemData)
      }
    },
    async companyNameChangeHandler(itemData) {
      this.updatePurOrgName(itemData)
    },
    async purOrgNameChangeHandler(itemData) {
      this.updateSiteName(itemData)
    },
    siteNameChangeHandler(itemData) {
      const sourcingExpandConf = this.fieldDefines.find(
        (item) => item.fieldCode === 'sourcingExpand'
      )
      const purOrgNameConf = this.fieldDefines.find((item) => item.fieldCode === 'purOrgName')
      if (sourcingExpandConf) {
        this.updateSourcingExpand(itemData?.data?.siteCode, purOrgNameConf.form.dataSource[0]?.data?.purOrgCode)
        // sourcingExpandConf.form.dataSource = this.sourcingExpandList.filter((x) => x.text !== text)
      }
    },
    async updatePurOrgName(itemData) {
      this.form.purOrgName = ''
      this.form.siteName = ''
      const purOrgNameConf = this.fieldDefines.find((item) => item.fieldCode === 'purOrgName')
      sessionStorage.setItem('selectCompanyId', itemData.data.companyId)
      if (purOrgNameConf) {
        const res = await this.$API.masterData
          .permissionOrgList({
            orgId: itemData.data.companyId
          })
          .catch(() => {})
        if (res && res.data) {
          purOrgNameConf.form.dataSource = res.data.map((item) => ({
            text: item.organizationCode + '-' + item.organizationName,
            value: item.organizationName,
            data: {
              purOrgName: item.organizationName,
              purOrgCode: item.organizationCode,
              purOrgId: item.id
            }
          }))
        }
      }
      const siteNameConf = this.fieldDefines.find((item) => item.fieldCode === 'siteName')
      if (siteNameConf) {
        siteNameConf.form.dataSource = []
      }
    },
    async updateSiteName(itemData) {
      this.form.siteName = ''
      const siteNameConf = this.fieldDefines.find((item) => item.fieldCode === 'siteName')
      if (siteNameConf) {
        siteNameConf.form.dataSource = await this.getSiteNameDataSource(itemData.data.purOrgId)
      }
    },
    async getSiteNameDataSource(organizationId) {
      const res = await this.$API.masterData
        .permissionSiteList({
          buOrgId: organizationId,
          companyId: sessionStorage.getItem('selectCompanyId'),
          orgLevelTypeCode: 'ORG06'
        })
        .catch(() => {})
      if (res.data == null) {
        return []
      }
      return res.data
        .filter(() => {
          return true
        }) // 过滤失效工厂
        .map((item) => ({
          text: item.orgCode + '-' + item.orgName,
          value: item.orgName,
          data: {
            siteName: item.orgName,
            siteId: item.id,
            siteCode: item.orgCode
          }
        }))
    },
    // 获取 用户列表
    getCurrentEmployees(e) {
      const { text: fuzzyName } = e
      this.currentEmployees = []
      this.$API.masterData.getCurrentTenantEmployees({ fuzzyName }).then((res) => {
        const tmp = []
        res.data.forEach((item) => {
          tmp.push({
            ...item,
            text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`
          })
        })
        this.currentEmployees = tmp
      })
    },
    filteringCurrency(e) {
      let currencyInfo = this.fieldDefines.find((v) => {
        return v.fieldCode == 'currencyName'
      })
      if (typeof e.text === 'string' && e.text) {
        e.updateData(currencyInfo.form.dataSource.filter((f) => f?.text.indexOf(e.text) > -1))
      } else {
        e.updateData(currencyInfo.form.dataSource)
      }
    },
    // 扩展
    async updateSourcingExpand(siteCode, purOrgCode) {
      this.form.sourcingExpand = []
      this.form.expandSaveRequestList = []
      const sourcingExpandConf = this.fieldDefines.find(
        (item) => item.fieldCode === 'sourcingExpand'
      )
      if (sourcingExpandConf) {
        const res = await this.$API.rfxDetail
          .purAllOrgWithSite({ fuzzyParam: siteCode, purOrgCode })
          .catch(() => {})
        if (res && res.data) {
          let dataSource = []
          res.data.forEach((v) => {
            v.siteOrgs.forEach((x) => {
              dataSource.push({
                text:
                  v.companyCode +
                  '-' +
                  v.businessOrganizationCode +
                  '-' +
                  v.businessOrganizationName +
                  '+' +
                  x.orgCode +
                  '-' +
                  x.orgName,
                value: v.companyCode + '+' + v.businessOrganizationCode + '+' + x.orgCode,
                data: {
                  companyCode: v.companyCode,
                  companyId: v.companyId,
                  companyName: v.companyName,
                  purOrgCode: v.businessOrganizationCode,
                  purOrgId: v.id,
                  purOrgName: v.businessOrganizationName,
                  siteCode: x.orgCode,
                  siteId: x.id,
                  siteName: x.orgName
                }
              })
            })
          })
          sourcingExpandConf.form.dataSource = dataSource
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.form-generator-form {
  display: flex;
  flex-flow: row wrap;
  row-gap: 20px;
  margin: 0 -10px;
  .get-component-code {
    color: #00469c;
    cursor: pointer;
    line-height: 60px;
    font-size: 14px;
    font-weight: 600;
  }
  .form-generator-form-item {
    padding-left: 10px;
    padding-right: 10px;
    margin-bottom: 0;
  }

  .form-generator-item-col-4 {
    display: block;
    flex: 0 0 25%;
    max-width: 25%;
  }
  .form-generator-item-col-2 {
    display: block;
    flex: 0 0 50%;
    max-width: 50%;
  }
  .form-generator-item-col-1 {
    display: block;
    flex: 0 0 100%;
    max-width: 100%;
  }
}

// .mt-form-item {
//   /deep/ .label {
//     font-weight: normal;
//   }
// }

// .mt-form-item[disabled] {
//   /deep/ .label {
//     color: #9a9a9a !important;
//   }

//   /deep/ input,
//   /deep/ .e-disabled {
//     background: transparent !important;
//   }

//   /deep/ input {
//     color: #9a9a9a;
//     border-bottom-color: #9a9a9a !important;
//   }
// }
</style>
<style>
::placeholder {
  color: #9a9a9a;
  font-size: 12px;
}
</style>
