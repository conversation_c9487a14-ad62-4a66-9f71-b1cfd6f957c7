<template>
  <div :class="['full-height', 'kanban-process', isKanbanBigger ? 'bigger' : 'small']">
    <div class="kanban-wrap">
      <div class="kanban-box">
        <title-name icon-name="icon_solid_Progress" :name="$t('流程看板')" v-if="isKanbanBigger" />
        <div class="titles" v-else>{{ $t('流程看板') }}</div>
        <ul>
          <li
            v-for="(item, itemIndex) in progressList"
            :key="item.label"
            :class="[item.code === progress && 'active-li']"
          >
            <mt-icon :name="item.icon" class="icon"></mt-icon>
            <div class="label">{{ item.label }}</div>
            <div class="split">
              <span class="cur" v-if="item.code === progress"></span>
              <span :class="['dot', item.code === progress && 'active']"></span>
              <span class="line" v-if="itemIndex < progressList.length - 1"></span>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <div :class="['message-wrap', isKanbanBigger && 'message-wrap-bigger']">
      <div class="message-box" v-if="isKanbanBigger">
        <title-name icon-name="icon_solid_Notice" :name="$t('消息通知')"></title-name>
        <ul class="message-list">
          <li
            v-for="(item, messageIndex) in messageList"
            :key="messageIndex"
            :class="[messageIndex === 0 && 'first-li']"
          >
            <div class="first-line">
              <span class="company" v-if="item.entityName">[{{ item.entityName }}]</span>
              <span class="title" :title="item.content">{{ item.content }}</span>
            </div>
            <div class="second-line">{{ item.msgTime }}</div>
          </li>
        </ul>
      </div>
      <div class="message-box" v-else>
        <div class="small-message">
          <div class="laba-box">
            <mt-icon name="icon_solid_Notice"></mt-icon>
            <mt-badge
              v-if="messageList.length > 0"
              :value="messageList.length"
              styles="primary"
            ></mt-badge>
          </div>
          <div class="mess">{{ $t('消息通知') }}</div>
        </div>
      </div>
    </div>

    <div class="shrink" @click="isKanbanBigger = !isKanbanBigger">
      <mt-icon v-show="isKanbanBigger" name="icon_arrow_left"></mt-icon>
      <mt-icon v-show="!isKanbanBigger" name="icon_arrow_right"></mt-icon>
    </div>
  </div>
</template>

<script>
import { i18n } from '@/main'
import utils from '@/utils/utils'
import TitleName from 'COMPONENTS/SourcingProject/panelTitle.vue'
import Vue from 'vue'
import MtBadge from '@mtech-ui/badge'
Vue.use(MtBadge)

const config = {
  RFQ: [
    {
      code: 'PROJECT_PREPARE',
      label: i18n.t('立项'),
      icon: 'lxzb'
    },
    {
      code: 'QUOTE',
      label: i18n.t('询比价'),
      icon: 'icon_bj_yj'
    },
    {
      code: 'DECIDE_BID',
      label: i18n.t('定价'),
      icon: 'icon_jj_tb'
    }
  ],
  bid: [
    {
      code: 'PROJECT_PREPARE',
      label: i18n.t('立项'),
      icon: 'lxzb'
    },
    {
      code: 'BID_PREPARE',
      label: i18n.t('标前准备'),
      icon: 'icon_xbj_bj'
    },
    {
      code: 'BID_MANAGE',
      label: i18n.t('投标管理'),
      icon: 'a-iconztbswb'
    },
    {
      code: 'EVALUATION_BID',
      label: i18n.t('评标'),
      icon: 'a-iconztbpb'
    },
    {
      code: 'DECIDE_BID',
      label: i18n.t('定标'),
      icon: 'icon_xbj_db'
    }
  ]
}
export default {
  name: 'ProcessKanban',
  components: {
    TitleName
  },
  props: {
    progress: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      formatTime: utils.formatTime,
      isKanbanBigger: false,
      messageList: []
    }
  },
  mounted() {
    this.getMessage()
  },
  methods: {
    getMessage() {
      this.$API.rfxExt.geRFXMessage({ rfxId: this.$route.query.rfxId }).then((res) => {
        this.messageList = res.data
      })
    }
  },
  computed: {
    progressList() {
      return config[this.$route.query.source === 'rfq' ? 'RFQ' : 'bid']
    }
  }
}
</script>

<style lang="scss" scoped>
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

$bigWidth: 200px;
$smallWidth: 96px;

.kanban-process {
  font-family: PingFangSC;
  min-width: $smallWidth;
  padding-top: 20px;
  position: relative;
  border-right: 1px solid #e8e8e8;
  margin-right: 20px;

  &.bigger {
    width: $bigWidth;
    padding-left: 10px;
    .kanban-wrap .kanban-box ul {
      display: flex;
      flex-direction: column;
      align-items: center;
      li {
        width: 140px;
        height: 60px;
        text-align: left;
        padding-left: 40px;
        icon {
          font-size: 14px;
        }
        .label {
          display: inline-block;
          margin: 0 0 0 10px;
          vertical-align: text-top;
        }
        .split {
          .line {
            height: 60px;
          }
        }
      }
    }
  }
  &.small {
    width: $smallWidth;
  }
  .shrink {
    cursor: pointer;
    width: 12px;
    height: 60px;
    border-radius: 0 4px 4px 0;
    position: absolute;
    right: -12px;
    top: 250px;
    display: flex;
    color: #fff;
    align-items: center;
    background-color: #9bb0cb;
    justify-content: flex-end;

    .mt-icons {
      transform: scale(0.6);
    }
  }
  .kanban-wrap {
    flex: 1;
    overflow: hidden;

    .kanban-box {
      height: 100%;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      .titles {
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 600;
        color: rgba(41, 41, 41, 1);
        text-align: center;
        white-space: nowrap;
        margin-bottom: 20px;
      }
      ul {
        flex: 1;
        padding-top: 10px;
        li {
          position: relative;
          color: #979797;
          margin-left: 20px;
          text-align: center;
          height: 70px;

          .icon {
            font-size: 24px;
          }
          .label {
            font-weight: normal;
            color: #9a9a9a;
            margin: 5px 0 0;
            text-align: center;
          }

          .split {
            position: absolute;
            left: 0;
            top: 0;
            .cur {
              position: absolute;
              left: -6px;
              top: -2.5px;
              width: 0;
              height: 0;
              border-top: 4px solid transparent;
              border-bottom: 4px solid transparent;

              border-left: 4px solid #ed5633;
            }
            .dot {
              position: absolute;
              left: -2px;
              top: 0;
              width: 4px;
              background-color: #bfbfbf;
              height: 4px;
              border-radius: 50%;
              &.active {
                background-color: #00469c;
              }
            }
            .line {
              height: 72px;
              width: 0;
              display: inline-block;
              border-right: 1px dotted #bfbfbf;
            }
          }
          &.active-li {
            /deep/ .mt-icons {
              color: #6386c1;
            }
            color: #292929;
          }
        }
      }
    }
  }

  .message-wrap {
    padding: 20px;

    .message-box {
      height: 100%;
      overflow: visible;
      display: flex;
      flex-direction: column;

      .small-message {
        text-align: center;

        .laba-box {
          position: relative;
          .mt-icons {
            color: #6386c1;
            font-size: 30px;
          }
          .e-badge {
            width: 16px;
            height: 16px;
            line-height: 16px;
            font-size: 12px;
            background: #ed5633;
          }
        }
        .mess {
          font-size: 12px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(99, 134, 193, 1);
          text-align: center;
          margin-top: 10px;
        }
      }

      .message-list {
        flex: 1;
        overflow-y: auto;
        li {
          // width: calc(100% - 80px);
          width: 170px;
          margin: 10px auto 20px;
          font-size: 12px;

          &.first-li {
            position: relative;
            &::before {
              content: '';
              position: absolute;
              left: -10px;
              width: 3px;
              height: 100%;
              background: rgba(99, 134, 193, 1);
              border-radius: 0 4px 4px 0;
            }
          }

          .first-line {
            line-height: 1.3;
            @extend .text-ellipsis;
          }

          .company {
            color: #eda133;
            font-weight: 500;
          }

          .title {
            color: #292929;
            font-weight: 500;
          }

          .second-line {
            font-weight: bold;
            color: rgba(154, 154, 154, 1);
            margin-top: 4px;
          }
        }
      }
    }

    &-bigger {
      padding: 20px 0 0 0;
      overflow: hidden;

      /deep/ .title-name {
        padding: 0 20px 10px 20px;
        margin-bottom: 0;
        box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.2);
      }

      .message-box {
        overflow: hidden;
      }
    }
  }
}
</style>
