<template>
  <mt-dialog
    ref="dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
    size="small"
    min-height="150"
    :height="158"
  >
    <div class="dialog-content">
      <div style="display: flex">
        <p style="display: inline-block; line-height: 28px">{{ $t('复制次数：') }}</p>
        <mt-inputNumber v-model="times"></mt-inputNumber>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      times: null,
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    confirm() {
      if (!this.times) {
        this.$toast({ content: this.$t('请先填写复制次数'), type: 'warning' })
        return
      } else {
        this.$emit('confirm-function', this.times)
      }
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  height: 60px;
  width: 100%;
  p {
    margin-bottom: 20px;
  }
}
</style>
