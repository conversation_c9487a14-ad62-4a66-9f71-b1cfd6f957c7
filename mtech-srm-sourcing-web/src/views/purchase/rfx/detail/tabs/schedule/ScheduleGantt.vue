<template>
  <div class="schedule-gantt">
    <mt-gantt
      v-if="ganttData.length > 0"
      :data-source="ganttData"
      :task-fields="taskFields"
      :columns="columns"
      :grid-lines="'Both'"
      :include-weekend="true"
      :splitter-settings="splitterSettings"
      :timeline-settings="timelineSettingsModel"
      :tooltip-settings="tooltipSettingsModel"
      :query-cell-info="queryCellInfoEvent"
    ></mt-gantt>
  </div>
</template>
<script>
import Vue from 'vue'
import ScheduleCard from './ScheduleCard.vue'
import MtGantt from '@mtech-ui/gantt'
Vue.use(MtGantt)

export default {
  data() {
    return {
      splitterSettings: {
        columnIndex: 4
      },
      // 时间线
      timelineSettingsModel: {
        // updateTimescaleView: true,
        timelineViewMode: 'Day',
        timelineUnitSize: 60,
        topTier: {
          unit: 'Month'
        },
        bottomTier: {
          unit: 'Day',
          format: 'd'
        }
      },
      tooltipSettingsModel: {
        showTooltip: true,
        taskbar: function () {
          return {
            template: ScheduleCard
          }
        }
      },
      taskFields: {
        id: 'id',
        name: 'linkTaskName',
        startDate: 'planStartTime',
        endDate: 'EndplanEndTimeDate'
      },
      columns: [
        {
          field: 'planPhaseName',
          headerTemplate: function () {
            return {
              template: Vue.component('actionOption', {
                template: `<div></div>`
              })
            }
          },
          template: function () {
            return {
              template: Vue.component('actionOption', {
                template: `<div class="cell-schedule-name mt-flex-direction-column" :style="{ '--height': nameHeight }">
                              <div class="line"></div>
                              <div class="vertical-line"></div>
                              <div class="line"></div>
                              <div class="schedule-name">{{data.planPhaseName}}</div>
                            </div>`,
                data() {
                  return { data: {} }
                },
                computed: {
                  nameHeight() {
                    if (this.data.taskData.hasRowSpan) {
                      return `${this.data.taskData.rowSpan * 50}px`
                    } else {
                      return `50px`
                    }
                  }
                }
              })
            }
          }
        },
        {
          field: 'id',
          headerTemplate: function () {
            return {
              template: Vue.component('actionOption', {
                template: `<div></div>`
              })
            }
          },
          template: function () {
            return {
              template: Vue.component('actionOption', {
                template: `<div class="cell-tags mt-flex">
                              <span :class="['tag', {'label':!planIsTask}]">{{planIsTask ? '任务':'里程碑'}}</span>
                              <div class="tag-name mt-flex-direction-column">
                                <span class="name">{{data.taskData.planName}}</span>
                                <span class="desc">{{data.taskData.planPhase}}</span>
                              </div>
                            </div>`,
                data() {
                  return { data: {} }
                },
                computed: {
                  planIsTask() {
                    return this.data.taskData.planType == '0'
                  }
                }
              })
            }
          }
        },
        {
          field: 'TaskName',
          headerTemplate: function () {
            return {
              template: Vue.component('actionOption', {
                template: `<div></div>`
              })
            }
          },
          template: function () {
            return {
              template: Vue.component('actionOption', {
                template: `<div class="cell-task mt-flex">
                            <span :class="['task',{'no-task':!getLinkNames}]">{{getLinkNames}}</span>
                          </div>`,
                data() {
                  return { data: {} }
                },
                computed: {
                  getLinkNames() {
                    let _list = this.data?.taskData?.taskRelList || [],
                      _res = []
                    _list.forEach((e) => {
                      _res.push(e.linkTaskName)
                    })
                    return _res.join(',')
                  }
                }
              })
            }
          }
        },
        {
          field: 'StartDate',
          headerTemplate: function () {
            return {
              template: Vue.component('actionOption', {
                template: `<div></div>`
              })
            }
          },
          template: function () {
            return {
              template: Vue.component('actionOption', {
                template: `  <div class="cell-percent mt-flex-direction-column" :style="{ '--width': percentWidth }">
                                <span class="percent">{{cardProgress}}%</span>
                                  <div :class="['percent-line', getPercentStyle]"></div>
                              </div>`,
                data() {
                  return {
                    data: {
                      data: {}
                    }
                  }
                },
                computed: {
                  cardProgress() {
                    // 计划类型0未开始1进行中2已完成
                    let _progress = 0
                    switch (this.data.taskData.status) {
                      case 0: //未开始
                        _progress = 0
                        break
                      case 1: //进行中
                        _progress = 0
                        break
                      case 2: //已完成
                        _progress = 100
                        break
                    }
                    return _progress
                  },
                  percentWidth() {
                    return `${(this.cardProgress * 60) / 100}px`
                  },
                  getPercentStyle() {
                    if (this.data.cardProgress < 40) {
                      return 'warning'
                    } else if (this.data.taskData.progress < 99) {
                      return 'process'
                    } else {
                      return 'success'
                    }
                  }
                }
              })
            }
          }
        },
        {
          field: 'rowSpanNumber',
          headerText: 'Job Name',
          headerTemplate: function () {
            return {
              template: Vue.component('actionOption', {
                template: `<div></div>`
              })
            }
          }
        }
      ],
      ganttData: [],
      phaseDataList: []
    }
  },
  mounted() {
    this.planPhaseListGet()
  },
  methods: {
    planPhaseListGet() {
      const query = { docId: this.$route.query.rfxId }
      this.$store.commit('startLoading')
      this.$API.rfxSchedule.queryPlanPhaseByRfxId(query).then((res) => {
        this.$store.commit('endLoading')
        this.phaseDataList = res.data
        let _list = []
        this.phaseDataList.forEach((p) => {
          let _plans = p.planResponseList
          if (Array.isArray(_plans) && _plans.length) {
            _plans.forEach((e, index) => {
              if (index < 1) {
                e.hasRowSpan = true
                e.rowSpan = _plans.length
              } else {
                e.hasRowSpan = false
                e.rowSpan = 1
              }
              _list.push(e)
            })
          }
        })
        this.ganttData = []
        this.$nextTick(() => {
          this.$set(this, 'ganttData', _list)
        })
      })
    },
    queryCellInfoEvent(args) {
      if (args.data.taskData.hasRowSpan && args.column.field == 'planPhaseName') {
        args.rowSpan = args.data.taskData.rowSpan
      }
    }
  }
}
</script>
<style>
.e-gantt-tooltip {
  background: transparent !important;
  border: none !important;
}
</style>
<style lang="scss" scoped>
.schedule-gantt {
  padding: 0;
  flex: 1;
  overflow: auto;
  background: #f8f8f8;
  /deep/ .mt-gantt {
    .e-grid {
      td.e-rowcell {
        &:first-of-type {
          padding: 0 10px !important;
        }
        &.e-active {
          .schedule-name {
            background-color: rgb(226, 229, 244);
          }
        }
      }
    }
    .e-control {
      height: 100% !important;
    }
    .e-gantt-splitter {
      border-radius: 8px 0 0 0;
    }
    th.e-headercell {
      border-left: 0;
      border-right: 0;
    }
    .e-gridcontent {
      .e-content {
        overflow-x: hidden !important;
      }
    }
    .e-rowcell {
      border-left-width: 0px;
      border-right-width: 1px;
    }
    .e-none {
      display: none !important;
    }
    .e-rowcell {
      padding: 0;
    }
    .e-chart-rows-container {
      height: auto !important;
      overflow: initial !important;
      .e-chart-row {
        height: 60px !important;
      }
    }
    .e-gantt-chart-pane {
      border-left: 1px solid #e0e0e0;
      #GanttContainerGanttTaskTableBody {
        td {
          border: unset !important;
        }
        .e-gantt-child-taskbar-inner-div {
          height: 14px !important;
          background: rgba(99, 134, 193, 0.8);
          border: 1px solid rgba(99, 134, 193, 1);
          border-radius: 2px;
        }
      }
    }
    .e-gantt-tooltip {
      background: transparent !important;
      border: none !important;
    }

    .e-split-bar {
      display: none;
    }
    .mt-flex {
      display: flex;
      position: relative;
    }
    .mt-flex-direction-column {
      display: flex;
      flex-direction: column;
      position: relative;
    }
    .cell-tags {
      // border: 1px solid red;
      width: 200px;
      height: 50px;
      justify-content: space-between;
      align-items: center;
      padding: 10px 20px;
      background: transparent;
      .tag {
        line-height: 1;
        width: 44px;
        height: 20px;
        line-height: 20px;
        border: 1px solid #6386c1;
        border-radius: 2px;
        font-size: 12px;
        font-family: PingFangSC;
        font-weight: 500;
        color: #6386c1;
        display: flex;
        align-items: center;
        justify-content: center;

        &.label {
          border: 1px solid rgba(241, 178, 87, 1);
          background: rgba(241, 178, 87, 0.1);
          color: rgba(241, 178, 87, 1);
        }
      }
      .tag-name {
        .name {
          line-height: 1;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(0, 70, 156, 1);
        }
        .desc {
          line-height: 1;
          margin-top: 4px;
          font-size: 12px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(41, 41, 41, 1);
        }
      }
    }
    .cell-task {
      // border: 1px solid red;
      width: 200px;
      height: 50px;
      align-items: center;
      padding: 10px 20px;
      background: transparent;
      .task {
        padding-left: 20px;
        position: relative;
        line-height: 1;
        color: #6386c1;
        &:not(:first-of-type) {
          margin-left: 10px;
        }
        &:before {
          content: '';
          width: 8px;
          height: 8px;
          border-radius: 50%;
          border: 1px solid #9daabf;
          background: #fafafa;
          position: absolute;
          left: 0;
          top: 3px;
          z-index: 3;
        }
        &:after {
          content: '';
          width: 14px;
          height: 0px;
          border-top: 1px solid #9daabf;
          background: #fafafa;
          position: absolute;
          left: 0;
          top: 7px;
          z-index: 2;
        }
        &.no-task {
          &:before,
          &:after {
            display: none;
          }
        }
      }
    }
    .cell-percent {
      // border: 1px solid red;
      width: 100px;
      height: 50px;
      justify-content: space-between;
      align-items: center;
      padding: 10px 20px;
      background: transparent;
      .percent {
        line-height: 1;
        font-size: 12px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(154, 154, 154, 1);
      }
      .percent-line {
        margin-top: 6px;
        position: relative;
        width: 60px;
        height: 6px;
        background: rgba(232, 232, 232, 1);
        border-radius: 3px;
        &:before {
          content: '';
          width: var(--width);
          height: 6px;
          left: 0;
          position: absolute;
          z-index: 2;
          border-radius: 3px;
        }
        &.process {
          &:before {
            background: linear-gradient(
              90deg,
              rgba(247, 207, 98, 1) 0%,
              rgba(237, 161, 51, 1) 100%
            );
          }
        }
        &.warning {
          &:before {
            background: linear-gradient(90deg, rgba(247, 143, 98, 1) 0%, rgba(237, 86, 51, 1) 100%);
          }
        }
        &.success {
          &:before {
            background: linear-gradient(
              90deg,
              rgba(190, 232, 116, 1) 0%,
              rgba(138, 204, 64, 1) 100%
            );
          }
        }
      }
    }

    .cell-schedule-name {
      width: 82px;
      height: var(--height);
      background: transparent;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      .line {
        width: 20px;
        height: 0;
        border-top: 1px solid #f1b257;
        position: relative;
        &:before,
        &:after {
          content: '';
          width: 3px;
          height: 3px;
          border-radius: 50%;
          border: 1px solid #f1b257;
          background: #fafafa;
          position: absolute;
          top: -2px;
          z-index: 3;
        }
        &:before {
          left: -2px;
        }
        &:after {
          right: -2px;
        }
      }
      .vertical-line {
        flex: 1;
        border-left: 1px solid #f1b257;
      }
      .schedule-name {
        position: absolute;
        height: 26px;
        line-height: 26px;
        top: calc(50% - 13px);
        background: #ffffff;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 500;
        color: #f1b257;
        z-index: 2;
      }
    }
  }
}
</style>
