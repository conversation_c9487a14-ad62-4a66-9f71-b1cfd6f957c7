<!--
 * @Author: your name
 * @Date: 2021-07-26 11:17:04
 * @LastEditTime: 2021-07-28 18:36:35
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-sourcing-web\src\views\SourcingProject\BiddingHall\HallDetail\pages\tabSchedule\index.vue
-->
<template>
  <div class="full-height schedule-page mt-flex-direction-column">
    <div class="mt-flex">
      <mt-tabs
        :tab-id="$utils.randomString()"
        :data-source="tabList"
        :selected-item="activeTab"
        @selected="selectTab"
      >
      </mt-tabs>
      <div class="schedule-toolbar mt-flex" v-if="activeTab == 0">
        <div class="svg-option-item" @click="doForwardPlan">
          <mt-icon name="icon_solid_edit" />
          <span>{{ $t('正推生成计划') }}</span>
        </div>
        <div class="svg-option-item" @click="doBackwardPlan">
          <mt-icon name="icon_solid_edit" />
          <span>{{ $t('倒推生成计划') }}</span>
        </div>
        <div class="option-item">
          <span class="option-label">{{ $t('参考模板') }}</span>
          <mt-select
            css-class="strategy-element"
            v-model="docId"
            :data-source="planTemplateList"
            :placeholder="$t('选择模板')"
            :fields="{ text: 'templateName', value: 'id' }"
            @change="handleSelectTemplate"
          ></mt-select>
        </div>
      </div>
    </div>
    <schedule-container v-if="activeTab == 0" ref="cardViewRef" />
    <schedule-gantt v-if="activeTab == 1" ref="ganttViewRef" />
  </div>
</template>
<script>
import ScheduleContainer from './ScheduleContainer.vue'
import ScheduleGantt from './ScheduleGantt.vue'
export default {
  components: { ScheduleContainer, ScheduleGantt },
  data() {
    return {
      activeTab: 0,
      tabList: [
        {
          header: { text: this.$t('图表视图') }
        },
        {
          header: { text: this.$t('甘特视图') }
        }
      ],
      selectTemplatePlan: null, //当前选择的任务计划模板--对象
      docId: null, //当前选择的任务计划模板--Id
      planTemplateList: [], //任务计划模板列表
      planDetail: [] //模板任务计划-详情
    }
  },
  mounted() {
    this.getPlanTempalte()
  },
  methods: {
    selectTab(e) {
      this.activeTab = e.selectedIndex
    },
    getPlanTempalte() {
      this.$API.scheduleConfig
        .getSourcingPlanTemplateByQueryBuilder({
          page: {
            current: 1,
            size: 100
          }
        })
        .then((res) => {
          if (Array.isArray(res?.data?.records) && res.data.records.length > 0) {
            //返回的任务计划模板列表，存在数据
            this.planTemplateList = res.data.records
          }
          // console.log(this.planTemplateList);
        })
    },
    //切换模板，获取模板详情
    handleSelectTemplate(e) {
      const query = { templateId: this.$route.query.rfxId }
      console.log(query)
      console.log('handleSelectTemplate--', e)
      if (e?.itemData) {
        this.selectTemplatePlan = e.itemData
        let _temp = e.itemData
        this.$API.scheduleConfig.querySourcingPlanTemplateTemplateId({
          templateId: _temp.id,
          docId: query.templateId
        })
      }
    },
    doBackwardPlan() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/rfx/detail/tabs/schedule/components/backwardPlanDialog" */ './components/backwardPlanDialog.vue'
          ),
        data: {
          title: this.$t('倒推生成计划'),
          docId: this.$route.query.rfxId,
          phaseDataList: this.$refs.cardViewRef.phaseDataList
        },
        success: () => {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.$refs.cardViewRef.planPhaseListGet()
        }
      })
    },
    doForwardPlan() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/rfx/detail/tabs/schedule/components/forwardPlanDialog" */ './components/forwardPlanDialog.vue'
          ),
        data: {
          title: this.$t('正推生成计划'),
          docId: this.$route.query.rfxId
        },
        success: () => {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.$refs.cardViewRef.planPhaseListGet()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.schedule-page {
  width: 100%;
  background: #f8f8f8;
  /deep/ .mt-tabs {
    width: 100%;
    background: #f8f8f8;
    padding-left: 15px;
    .e-tab .e-tab-header .e-toolbar-item.e-active .e-tab-text {
      font-weight: 600;
    }
    .e-toolbar-item {
      min-width: 100px;
      text-align: center;
    }
    .e-tab-header {
      background: #f8f8f8;
    }
  }
  .schedule-toolbar {
    align-items: center;
    .svg-option-item {
      &:not(:first-of-type) {
        margin-left: 20px;
      }
    }
  }
  .option-item {
    margin-left: 20px;
    display: flex;
    align-items: center;
    .option-label {
      font-size: 14px;
      font-weight: 500;
      color: #465b73;
      text-align: right;
      display: inline-block;
      margin-right: 20px;
      position: relative;
      word-break: keep-all;
    }
    /deep/ .strategy-element {
      width: 150px !important;
      border: none;
      border-color: transparent !important;
      position: relative;
      top: 4px;
      &:before,
      &:after {
        display: none;
      }
      .e-float-line {
        display: none;
      }
      .e-control {
        color: #292929;
        border: none;
        border-color: transparent !important;
      }
      .e-spin-down {
        position: absolute;
        right: -5px;
        top: 9px;
      }
      .e-spin-up {
        position: absolute;
        right: -1px;
        top: -4px;
      }
      .e-ddl-icon,
      .e-date-icon {
        position: absolute;
        right: 0;
        top: 3px;
      }
    }
  }
}
</style>
