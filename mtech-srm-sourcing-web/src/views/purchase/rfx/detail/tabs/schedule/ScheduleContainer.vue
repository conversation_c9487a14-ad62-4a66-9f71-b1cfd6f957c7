<template>
  <div class="schedule-container mt-flex">
    <div
      v-for="(phase, index) in phaseDataList"
      :key="phase.planPhase"
      class="schedule-item mt-flex-direction-column"
    >
      <div class="item-title mt-flex">
        <span class="title">{{ phase.phaseName }}</span>
        <span
          class="option"
          :tabIndex="index"
          @click="openOptionList(index)"
          @blur="blurOptionList(index)"
          >...

          <div class="card-option" v-show="phase.showOption">
            <div class="option-item option-edit" @click="editPhase(phase)">
              {{ $t('编辑') }}
            </div>
            <div class="option-item option-delete" @click="deletePhase(phase)">
              {{ $t('删除') }}
            </div>
          </div>
        </span>
      </div>
      <div
        class="item-content mt-flex-direction-column"
        :id="'schedule-container-' + phase.id"
        :tabIndex="index"
        :phaseName="phase.phaseName"
        :phaseId="phase.id"
      >
        <schedule-card
          v-for="task in phase.planResponseList"
          :key="task.id"
          :tab-index="index"
          @click="activeScheduleId = task.id"
          :card-data="task"
          :active="task.id == activeScheduleId"
          @handleClickNode="editPlan($event, phase)"
          @handleClickTask="clickTaskNode($event, phase)"
        ></schedule-card>
      </div>
      <div class="add-card mt-flex" @click="createPlan(phase)">
        <mt-icon name="icon_Close_1"></mt-icon>
      </div>
    </div>
    <div class="schedule-item mt-flex-direction-column empty-item">
      <div class="item-title mt-flex">
        <span class="title">{{ $t('待新建阶段') }}</span>
        <span class="option" @click="createPhase">... </span>
      </div>
      <div class="item-content mt-flex-direction-column">
        <div class="add-card mt-flex" @click="createPhase">
          <mt-icon name="icon_Close_1"></mt-icon>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import ScheduleCard from './ScheduleCard.vue'
import Sortable from 'sortablejs'
//todo 拖拽保存。。
export default {
  components: { ScheduleCard },
  data() {
    return {
      phaseDataList: [],
      activeScheduleId: null
    }
  },
  async mounted() {
    this.resetListAndSortable()
  },
  methods: {
    async resetListAndSortable() {
      await this.planPhaseListGet()
      let _this = this
      this.$nextTick(() => {
        this.phaseDataList.map((e) => {
          if (document.getElementById(`schedule-container-${e.id}`)) {
            Sortable.create(document.getElementById(`schedule-container-${e.id}`), {
              handle: '.card-header',
              group: 'schedule',
              disabled: false,
              // 元素从一个列表拖拽到另一个列表
              onAdd: function (/**Event*/ { oldIndex, newIndex, item, from, to }) {
                let _fromList = [..._this.phaseDataList[from.tabIndex]['planResponseList']]
                let _toList = [..._this.phaseDataList[to.tabIndex]['planResponseList']]
                let _toPhase = { ..._this.phaseDataList[to.tabIndex] }
                const targetRow = _fromList.splice(oldIndex, 1)[0]
                _toList.splice(newIndex, 0, targetRow)
                let _currentIndex = _toList.findIndex((e) => {
                  return e.id === item.id
                })
                let _plan = _toList[_currentIndex]
                let _frontId = _currentIndex === 0 ? 0 : _toList[_currentIndex - 1]['id']
                let _nextId =
                  _currentIndex === _toList.length - 1 ? 100 : _toList[_currentIndex + 1]['id']
                _plan.beforePlanId = _frontId
                _plan.afterPlanId = _nextId
                _plan.planPhaseId = _toPhase.id //	计划阶段id
                _plan.planPhaseName = _toPhase.phaseName //	计划阶段名称
                _this.handleSortPlanList(_plan)
              },

              // 列表内元素顺序更新的时候触发
              onUpdate: function (/**Event*/ { oldIndex, newIndex, item }) {
                let _list = [..._this.phaseDataList[item.tabIndex]['planResponseList']]
                const targetRow = _list.splice(oldIndex, 1)[0]
                _list.splice(newIndex, 0, targetRow)
                let _currentIndex = _list.findIndex((e) => {
                  return e.id === item.id
                })
                let _plan = _list[_currentIndex]
                let _frontId = _currentIndex === 0 ? 0 : _list[_currentIndex - 1]['id']
                let _nextId =
                  _currentIndex === _list.length - 1 ? 100 : _list[_currentIndex + 1]['id']
                _plan.beforePlanId = _frontId
                _plan.afterPlanId = _nextId
                _this.handleSortPlanList(_plan)
              }
            })
          }
        })
      })
    },
    async planPhaseListGet() {
      const query = { docId: this.$route.query.rfxId }
      this.$store.commit('startLoading')
      await this.$API.rfxSchedule.queryPlanPhaseByRfxId(query).then((res) => {
        this.$store.commit('endLoading')
        if (Array.isArray(res?.data) && res.data.length > 0) {
          //返回的任务计划列表，存在数据 planResponseList
          let _phase = res.data[0]
          if (Array.isArray(_phase?.planResponseList) && _phase.planResponseList.length > 0) {
            this.activeScheduleId = _phase.planResponseList[0].id
          }

          this.phaseDataList = res.data.map((e) => {
            e.showOption = false
            return e
          })
        } else {
          this.activeScheduleId = null
          this.phaseDataList = []
        }
      })
    },
    openOptionList(i) {
      this.phaseDataList[i]['showOption'] = true
    },
    blurOptionList(i) {
      this.phaseDataList[i]['showOption'] = false
    },
    createPhase() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/rfx/detail/tabs/schedule/components/phaseDialog" */ './components/phaseDialog.vue'
          ),
        data: {
          title: this.$t('阶段新增'),
          docId: this.$route.query.rfxId
        },
        success: () => {
          this.$toast({
            content: this.$t('成功新增阶段'),
            type: 'success'
          })
          // this.planPhaseListGet();
          this.resetListAndSortable()
        }
      })
    },
    editPhase(phase) {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/rfx/detail/tabs/schedule/components/phaseDialog" */ './components/phaseDialog.vue'
          ),
        data: {
          title: this.$t('阶段编辑'),
          data: phase,
          docId: this.$route.query.rfxId
        },
        success: () => {
          this.$toast({
            content: this.$t('成功编辑阶段'),
            type: 'success'
          })
          this.planPhaseListGet()
        }
      })
    },
    deletePhase(phase) {
      this.$dialog({
        data: {
          title: this.$t('删除阶段'),
          message: this.$t('删除阶段将清空该阶段下的所有任务，请谨慎操作')
        },
        success: () => {
          this.$API.scheduleConfig.deleteSourcingPlanPhaseById({ idList: [phase.id] }).then(() => {
            this.$toast({
              content: this.$t('成功删除阶段'),
              type: 'success'
            })
            // this.planPhaseListGet();
            this.resetListAndSortable()
          })
        }
      })
    },
    createPlan(phase) {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/rfx/detail/tabs/schedule/components/planDialog" */ './components/planDialog.vue'
          ),
        data: {
          title: this.$t('新增节点'),
          docId: this.$route.query.rfxId,
          phase: phase,
          frontPlans: this.getFrontPlanLists()
        },
        success: () => {
          this.$toast({
            content: this.$t('成功新增任务节点'),
            type: 'success'
          })
          this.planPhaseListGet()
        }
      })
    },
    editPlan(node, phase) {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/rfx/detail/tabs/schedule/components/planDialog" */ './components/planDialog.vue'
          ),
        data: {
          title: this.$t('编辑节点'),
          docId: this.$route.query.rfxId,
          phase: phase,
          data: node,
          frontPlans: this.getFrontPlanLists(node)
        },
        success: (e) => {
          this.$toast({
            content: e?.type === 'edit' ? this.$t('成功编辑任务节点') : this.$t('成功删除任务节点'),
            type: 'success'
          })
          this.planPhaseListGet()
        }
      })
    },
    clickTaskNode() {},
    getFrontPlanLists(node = {}) {
      let _list = [...this.phaseDataList],
        res = []
      _list.forEach((e) => {
        if (Array.isArray(e.planResponseList)) {
          res = res.concat(e.planResponseList)
        }
      })
      if (node?.id) {
        res = res.filter((e) => {
          return e.id !== node.id
        })
      }
      return res
    },
    handleSortPlanList(plan) {
      this.$API.rfxSchedule.saveRfxHeaderPlan(plan).then(() => {
        this.$toast({
          content: this.$t(`成功编辑任务节点`),
          type: 'success'
        })
        // this.planPhaseListGet();
        this.resetListAndSortable()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.schedule-container {
  background: #f8f8f8;
  padding: 20px;
  flex: 1;
  overflow: auto;
  .schedule-item {
    &:not(:first-of-type) {
      margin-left: 30px;
    }
    &.empty-item {
      .item-title {
        color: #9daabf;
      }
    }
    .item-title {
      flex-shrink: 0;
      height: 36px;
      padding: 0 10px;
      justify-content: space-between;
      .title {
        font-size: 16px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(41, 41, 41, 1);
      }
      .option {
        cursor: pointer;
        font-size: 16px;
        line-height: 16px;
        position: relative;
        .card-option {
          position: absolute;
          width: 68px;
          height: 76px;
          background: rgba(255, 255, 255, 1);
          border: 1px solid rgba(232, 232, 232, 1);
          border-radius: 2px;
          box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.2);
          padding: 0;
          right: 0;
          top: 20px;
          z-index: 3;
          .option-item {
            line-height: 34px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: normal;
            text-align: center;
          }
          .option-edit {
            color: rgba(41, 41, 41, 1);
          }
          .option-delete {
            color: rgba(237, 86, 51, 1);
          }
        }
      }
    }

    .item-content {
      // flex: 1;
    }
    .add-card {
      width: 300px;
      height: 30px;
      flex-shrink: 0;
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(232, 232, 232, 1);
      border-radius: 4px;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      color: #9daabf;
      cursor: pointer;
      .mt-icons {
        transform: rotate(45deg);
      }
      &:hover {
        color: #6386c1;
      }
    }
  }
}
</style>
