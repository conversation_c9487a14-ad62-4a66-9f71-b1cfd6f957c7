// 任务计划-阶段
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="phaseObject" :rules="formRules">
        <mt-form-item prop="phaseName" :label="$t('阶段名称')">
          <mt-input
            v-model="phaseObject.phaseName"
            float-label-type="Never"
            :placeholder="$t('请输入阶段名称')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="planStartTime" :label="$t('计划开始时间')">
          <mt-date-time-picker
            v-model="phaseObject.planStartTime"
            float-label-type="Never"
            :allow-filtering="true"
            :open-on-focus="true"
            :placeholder="$t('请选择计划开始时间')"
          ></mt-date-time-picker>
        </mt-form-item>

        <mt-form-item prop="planEndTime" :label="$t('计划结束时间')">
          <mt-date-time-picker
            v-model="phaseObject.planEndTime"
            float-label-type="Never"
            :allow-filtering="true"
            :open-on-focus="true"
            :placeholder="$t('请选择计划结束时间')"
          ></mt-date-time-picker>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      phaseObject: {
        docId: '',
        id: 0,
        phaseName: '',
        planStartTime: '',
        planEndTime: ''
      },
      formRules: {},
      editStatus: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    console.log('this.propsData', this.propsData)
    if (this.propsData?.data) {
      this.editStatus = true
      let _data = { ...this.propsData.data }
      this.phaseObject = {
        docId: _data.docId,
        id: _data.id,
        phaseName: _data.phaseName,
        planStartTime: _data.planStartTime,
        planEndTime: _data.planEndTime
      }
    }
    this.getFormValidRules('formRules', this.$API.rfxSchedule.saveRfxHeaderPlanPhaseValid)
  },
  methods: {
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = { ...this.phaseObject }
          if (!this.editStatus) {
            //新增场景，不传id，docID读取弹框传值rfxId
            delete params.id
            params.docId = this.propsData.docId
          }
          this.$API.rfxSchedule.saveRfxHeaderPlanPhase(params).then((res) => {
            if (res.code == 200) {
              this.$emit('confirm-function')
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
