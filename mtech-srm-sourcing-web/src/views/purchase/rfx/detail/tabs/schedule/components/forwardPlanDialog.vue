// 正推生成计划
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="scheduleObject" :rules="formRules">
        <mt-form-item prop="planStartTime" :label="$t('开始时间点')">
          <mt-date-time-picker
            v-model="scheduleObject.planStartTime"
            float-label-type="Never"
            :allow-filtering="true"
            :show-clear-button="false"
            :placeholder="$t('请选择开始时间点')"
          ></mt-date-time-picker>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      scheduleObject: {
        planStartTime: new Date()
      },
      formRules: {}
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },

    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    console.log('this.propsData', this.propsData)
    this.getFormValidRules('formRules', this.$API.rfxSchedule.forwardPushPlanByRfxIdValid)
  },
  methods: {
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = {
            startTime: this.scheduleObject.planStartTime,
            docId: this.propsData.docId
          }
          this.$API.rfxSchedule.forwardPushPlanByRfxId(params).then((res) => {
            if (res.code == 200) {
              this.$emit('confirm-function')
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
