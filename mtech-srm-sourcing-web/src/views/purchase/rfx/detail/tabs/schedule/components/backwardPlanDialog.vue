// 倒推生成计划
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="scheduleObject" :rules="formRules">
        <mt-form-item prop="planEndTime" :label="$t('完成时间点')">
          <mt-date-time-picker
            v-model="scheduleObject.planEndTime"
            float-label-type="Never"
            :allow-filtering="true"
            :show-clear-button="false"
            :placeholder="$t('请选择完成时间点')"
          ></mt-date-time-picker>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      scheduleObject: {
        planEndTime: ''
      },
      formRules: {}
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.getFormValidRules('formRules', this.$API.rfxSchedule.backwardPushPlanByRfxIdValid)
    console.log('this.propsData', this.propsData)
    if (Array.isArray(this.propsData?.phaseDataList)) {
      if (this.propsData.phaseDataList.length > 0) {
        let _phase = this.propsData.phaseDataList.pop()
        this.scheduleObject.planEndTime = _phase.planEndTime
      }
    }
  },
  methods: {
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = {
            startTime: this.scheduleObject.planEndTime,
            docId: this.propsData.docId
          }
          this.$API.rfxSchedule.backwardPushPlanByRfxId(params).then((res) => {
            if (res.code == 200) {
              this.$emit('confirm-function')
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
