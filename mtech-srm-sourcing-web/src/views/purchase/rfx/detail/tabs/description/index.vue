<template>
  <div class="full-height mt-flex-direction-column">
    <mt-tabs
      :tab-id="$utils.randomString()"
      :e-tab="false"
      :data-source="tabSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <div v-if="[0, 1].includes(tabIndex)" class="intro-box mt-flex-direction-column">
      <div class="setting-banner">
        <mt-icon name="icon_solid_Save" />
        <span @click="saveTxt">{{ $t('保存') }}</span>
      </div>
      <div class="edit-box">
        <span style="display: none">{{ richValueCopy }}</span>
        <mt-rich-text-editor
          ref="MtRichTextEditor"
          css-class="rich-editor"
          :toolbar-settings="toolbarSettings"
          :background-color="backgroundColor"
          :disabled="isDisabled"
          :insert-image-settings="insertImageSettings"
          v-model="richValue"
          :format="format"
          @imageUploading="imageUploading"
          maxlength="65503"
          :enable-html-encode="true"
        >
        </mt-rich-text-editor>
      </div>
    </div>
    <div v-if="[2].includes(tabIndex)">
      <ScTable ref="sctableRef" :columns="columns" :table-data="tableData" />
    </div>
  </div>
</template>
<script>
import ScTable from '@/components/ScTable/src/index.js'
import { HEADER_TOKEN_KEY, getToken } from '@mtech-sso/single-sign-on'
import Vue from 'vue'
import MtRichTextEditor from '@mtech-ui/rich-text-editor'
Vue.use(MtRichTextEditor)
import { judgeDataIsSame } from '@/views/common/columnData/utils'

export default {
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    }
  },
  components: { ScTable },
  data() {
    return {
      tabIndex: 0,
      tabSource: [
        // {
        //   title: this.$t('招标公告')
        // },
        {
          title: this.$t('内部备注')
        },
        {
          title: this.$t('外部备注')
        },
        {
          title: this.$t('供方说明')
        }
      ],
      richInfo: null,
      richValue: '',
      richValueCopy: '',
      toolbarSettings: {
        enable: true,
        enableFloating: true,
        type: 'Expand',
        items: [
          'Bold',
          'Italic',
          'Underline',
          '|',
          'Formats',
          'Alignments',
          'OrderedList',
          'UnorderedList',
          '|',
          'CreateLink',
          'Image',
          'backgroundColor',
          '|',
          'SourceCode',
          'Undo',
          'Redo'
        ],
        itemConfigs: {}
      },
      backgroundColor: {
        columns: 10,
        modeSwitcher: true,
        colorCode: {
          Custom: [
            '#ffff00',
            '#00ff00',
            '#00ffff',
            '#ff00ff',
            '#0000ff',
            '#ff0000',
            '#000080',
            '#008080',
            '#008000',
            '#800080',
            '#800000',
            '#808000',
            '#c0c0c0',
            '#000000',
            '#000000'
          ]
        }
      },
      insertImageSettings: {
        allowedTypes: ['.jpeg', '.jpg', '.png'],
        display: 'inline',
        width: 'auto',
        height: 'auto',
        saveUrl: '/api/file/user/file/uploadPrivate?useType=2',
        // path: "http://k8s-oss-test.s3.cn-east-2.jdcloud-oss.com/file/srm/tenant/17706479458443265/image/d62bc4f4-ba54-452e-b39f-5c664ab21153.png",
        path: ''
      },
      rfx_bid_notice_Object: null,
      rfx_in_remark_Object: null,
      rfx_out_remark_Object: null,
      format: {
        default: 'Heading 4',
        width: '65px',
        types: [
          { text: 'Paragraph' },
          { text: 'Code' },
          { text: 'Quotation' },
          { text: 'Heading 1' },
          { text: 'Heading 2' },
          { text: 'Heading 3' },
          { text: 'Heading 4' },
          { text: 'Heading 5' },
          { text: 'Heading 6' }
        ]
      },
      oldValue: null,

      tableData: []
    }
  },
  computed: {
    isDisabled() {
      // 非草稿状态都要禁止编辑
      return this.detailInfo?.status !== 0 && this.detailInfo.status !== -2
    },
    // 内容是否修改
    isContentChange() {
      return !judgeDataIsSame(this.oldValue, this.$refs.MtRichTextEditor.ejsRef.getHtml())
    },
    columns() {
      return [
        {
          field: 'supplierName',
          title: this.$t('供应商')
        },
        {
          field: 'content',
          title: this.$t('留言内容')
        },
        {
          field: 'msgDetail',
          title: this.$t('实际价格')
        }
      ]
    }
  },
  mounted() {
    this.$refs.MtRichTextEditor.ejsInstances.height = '100%'
    this.getExplain()
    window.MtRichTextEditor = this.$refs.MtRichTextEditor
    this.getTableData()
  },
  methods: {
    getTableData() {
      this.$API.supplyQdetail.getByRfxIdApi({ rfxId: this.$route.query.rfxId }).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.map((item) => {
            return {
              content: this.$t('报价错误'),
              ...item
            }
          })
        }
      })
    },
    imageUploading(event) {
      console.log('imageUploading', event)
      const self = this
      event.currentRequest.setRequestHeader(HEADER_TOKEN_KEY, getToken())
      const loadfunction = event.currentRequest.onload

      // event.currentRequest.onreadystatechange = function () {
      //   console.log("onreadystatechange", arguments);
      //   debugger;
      // };

      event.currentRequest.onload = function () {
        console.log('event.currentRequest.onloadend')
        const response = JSON.parse(event.currentRequest.response || '{}')
        event.fileData.name = response.data?.url
        self.$set(self.insertImageSettings, 'path', response.data?.url)
        loadfunction.apply(this, arguments)
      }
    },

    handleSelectTab(e) {
      // 如果没有点击保存，切换的时候保存数据。
      // 如果没有修改内容也不需要保存
      // 非草稿状态下不请求保存接口
      if (!this.isDisabled) {
        this.AddSave(false)
      }
      this.tabIndex = e
      this.defineRichObject()
    },
    defineRichObject() {
      // if (this.tabIndex === 0) {
      //   this.richInfo = this.rfx_bid_notice_Object
      // }
      if (this.tabIndex === 0) {
        this.richInfo = this.rfx_in_remark_Object
      } else if (this.tabIndex === 1) {
        this.richInfo = this.rfx_out_remark_Object
      }
      this.richValue = this.richInfo?.content || ''
      this.richValueCopy = this.richValue

      this.$nextTick(() => {
        this.oldValue = this.$refs.MtRichTextEditor.ejsRef.getHtml()
      })
    },
    getExplain() {
      this.$API.rfxExt.getExplain({ docId: this.$route.query.rfxId }).then((res) => {
        if (Array.isArray(res?.data) && res.data.length) {
          res.data.forEach((e) => {
            if (e.fieldKey === 'rfx_bid_notice') {
              this.rfx_bid_notice_Object = e
            } else if (e.fieldKey === 'rfx_in_remark') {
              this.rfx_in_remark_Object = e
            } else if (e.fieldKey === 'rfx_out_remark') {
              this.rfx_out_remark_Object = e
            }
          })
        }
        this.defineRichObject()
      })
    },
    saveTxt() {
      if (this.isDisabled) {
        this.$toast({
          content: this.$t('非草稿状态，不能执行此操作'),
          type: 'warning'
        })
        return
      }
      this.AddSave()
    },
    AddSave(toast = true) {
      let fieldKey = ''
      // if (this.tabIndex === 0) {
      //   fieldKey = 'rfx_bid_notice'
      // } else
      if (this.tabIndex === 0) {
        fieldKey = 'rfx_in_remark'
      } else if (this.tabIndex === 1) {
        fieldKey = 'rfx_out_remark'
      }
      let params = {
        docId: this.$route.query.rfxId,
        content: this.$refs.MtRichTextEditor.ejsRef.getHtml(),
        id: this?.richInfo?.id || '',
        fieldKey
      }
      console.log('保存说明：', params)
      this.$API.rfxExt.submitExplain(params).then(() => {
        toast && this.$toast({ content: this.$t('保存成功'), type: 'success' })
        this.getExplain()
      })
    }
  }
}
</script>
<style lang="scss">
.mt-rich-text-editor {
  ul {
    list-style-type: disc;
  }
  ol {
    list-style-type: decimal;
  }
  em {
    font-style: italic;
  }
  strong {
    font-weight: bold;
  }
}
</style>

<style lang="scss" scoped>
.full-height {
  display: flex;
  flex-direction: column;
  .intro-box {
    // width: 100%;
    // height: 100%;
    flex: 1;
    background: #fff;
    .rich-editor {
      height: 100%;
    }
    .mt-rich-text-editor {
      height: 100%;
    }
    .setting-banner {
      width: 100%;
      height: 50px;
      padding: 0 20px;
      display: flex;
      flex-shrink: 0;
      align-items: center;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(79, 91, 109, 1);

      i {
        font-size: 16px;
        line-height: 14px;
        display: inline-block;
        height: 16px;
        width: 16px;
        cursor: pointer;
      }

      span {
        display: inline-block;
        margin-left: 6px;
        cursor: pointer;
      }
    }

    .edit-box {
      padding: 0 20px;
      // width: 100%;
      // height: 100%;
      flex: 1;
      background: #fff;
    }
  }

  /deep/.mt-tabs {
    background: #fafafa;
    flex-shrink: 0;
    height: 50px;
  }
  /deep/ .tab-container {
    background: #fafafa !important;
  }
}
.fields-config-page {
  padding-top: 20px;
  width: 100%;
}
/deep/.mt-rich-text-editor {
  height: 100%;
  .e-rte-content {
    height: 100%;
  }
}
</style>
