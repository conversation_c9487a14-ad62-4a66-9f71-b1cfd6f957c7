<template>
  <div class="evaluation-container">
    <!-- 顶部banner -->
    <div class="banner-line mt-flex">
      <div class="left-part mt-flex">
        <div class="label-title">{{ $t('当前轮次：') }}</div>
        <div class="label-select">
          <mt-select
            :width="200"
            css-class="e-outline"
            float-label-type="Never"
            :data-source="evaluationArr"
            :show-clear-button="true"
            :value="evStep"
            @change="changeEV"
            placeholder="$t('请选择阶段')"
          ></mt-select>
        </div>
      </div>
    </div>
    <!-- 底部内容 -->
    <div class="bt-detail mt-flex">
      <div class="ev-item-box">
        <!-- 左侧的单个item -->
        <div class="ev-item" :class="{ active: productId === 0 }" @click="selectProduct(0)">
          <div class="tp-line mt-flex">
            <div class="title-left flex1">10-10006152</div>
            <div class="title-right flex1">{{ $t('产品名称') }}</div>
          </div>
          <div class="tp-bottom mb-10 mt-flex">
            <div class="title-left flex1">{{ $t('描述') }}</div>
            <div class="title-right flex1">0001</div>
          </div>

          <div class="tp-line mr-10 border-top mt-flex">
            <div class="title-left flex1">9,000</div>
            <div class="title-center flex1">4,000.00</div>
            <div class="title-right flex1">{{ $t('公司名称') }}</div>
          </div>
          <div class="tp-f-bottom mt-flex">
            <div class="title-left flex1">{{ $t('采购数量') }}</div>
            <div class="title-center flex1">{{ $t('采购预估价/单位') }}</div>
            <div class="title-right flex1">{{ $t('采购公司') }}</div>
          </div>
        </div>

        <div class="ev-item" :class="{ active: productId === 1 }" @click="selectProduct(1)">
          <div class="tp-line mt-flex">
            <div class="title-left flex1">10-10006152</div>
            <div class="title-right flex1">{{ $t('产品名称') }}</div>
          </div>
          <div class="tp-bottom mb-10 mt-flex">
            <div class="title-left flex1">{{ $t('描述') }}</div>
            <div class="title-right flex1">0001</div>
          </div>

          <div class="tp-line mr-10 border-top mt-flex">
            <div class="title-left flex1">9,000</div>
            <div class="title-center flex1">4,000.00</div>
            <div class="title-right flex1">{{ $t('公司名称') }}</div>
          </div>
          <div class="tp-f-bottom mt-flex">
            <div class="title-left flex1">{{ $t('采购数量') }}</div>
            <div class="title-center flex1">{{ $t('采购预估价/单位') }}</div>
            <div class="title-right flex1">{{ $t('采购公司') }}</div>
          </div>
        </div>
      </div>
      <div class="ev-detail">
        <!-- tab内容 -->
        <div class="detail-banner">
          <div class="title-banner">BH000189757-B_10</div>
          <div class="detail-info mt-flex">
            <div class="sub-title">{{ $t('办公电脑大批量') }}</div>
            <div class="info-item">
              <mt-icon name="MT_Description"></mt-icon>
              {{ $t('采购总价：') }}¥ 36,000,000.00
            </div>
            <div class="info-item">
              <mt-icon name="MT_Description"></mt-icon>
              {{ $t('采购数量：') }}9,000
            </div>
            <div class="info-item">
              <mt-icon name="MT_Description"></mt-icon>
              {{ $t('采购预估价/kg：') }}¥ 4,000.00
            </div>
            <div class="info-item">
              <mt-icon name="MT_Description"></mt-icon>
              {{ $t('采购组织：') }} {{ $t('研发部') }}
            </div>
          </div>

          <div class="right-part mt-flex">
            <div class="tab-item" :class="{ active: tabId === 0 }" @click="changTab(0)">
              {{ $t('核价明细') }}
            </div>
            <div class="tab-item" :class="{ active: tabId === 1 }" @click="changTab(1)">
              {{ $t('报价结果') }}
            </div>
          </div>
        </div>

        <!-- grid 1  tab的树形grid-->
        <div class="grid-box" v-show="tabId === 0">
          <div class="setting-banner">
            <mt-icon name="icon_solid_Createorder" />
            <span>{{ $t('新增行') }}</span>
          </div>

          <div class="main-grid flex1">
            <mt-tree-grid
              :data-source="sampleData"
              :columns="editColumns"
              :auto-check-hierarchy="true"
              child-mapping="subtasks"
              :tree-column-index="1"
              :allow-filtering="false"
              :show-column-chooser="true"
              :allow-sorting="true"
            ></mt-tree-grid>
          </div>
        </div>

        <!-- grid 2  tab的grid-->
        <div class="grid-box" v-show="tabId === 1">
          <mt-template-page ref="templateRef" :template-config="pageConfig" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { historyListColumnData, getListGridData } from './config'
export default {
  data() {
    return {
      pageConfig: [
        {
          grid: {
            allowFiltering: true,
            rowDataBound: (args) => {
              if (args.data['isBetter']) {
                args.row.classList.add('top-left-tag')
              }
            },
            columnData: historyListColumnData,
            dataSource: getListGridData(22)
          }
        }
      ],
      tabId: 0,
      productId: 0,
      evStep: 1,
      evaluationArr: [
        { text: this.$t('第一轮'), value: 1 },
        { text: this.$t('第二轮'), value: 2 },
        { text: this.$t('第三轮'), value: 3 }
      ],
      tabSource: [
        // statusList 如果hallStatus在这个状态array中的就显示
        {
          title: '',
          rank: 99,
          name: this.$t('宋军'),
          type: this.$t('商')
        },
        {
          title: '',
          rank: 66,
          name: this.$t('张杰'),
          type: this.$t('技')
        }
      ],
      sampleData: [
        {
          c1: 'SUP202108102319',
          c2: this.$t('材料成本'),
          c3: '￥300/KG',
          c4: '13%',
          c5: '339',
          c6: '1',
          c7: 'ceshiceshi',
          subtasks: [
            {
              c1: 'SUP202108102200',
              c2: this.$t('直接材料'),
              c3: '￥100/KG',
              c4: '13%',
              c5: '113',
              c6: '1',
              c7: 'ceshiceshi'
            },
            {
              c1: 'SUP202108102201',
              c2: this.$t('简介材料'),
              c3: '￥200/KG',
              c4: '13%',
              c5: '226',
              c6: '1',
              c7: 'ceshiceshi'
            }
          ]
        },
        {
          c1: 'SUP202108102320',
          c2: this.$t('人工成本'),
          c3: '￥100/人',
          c4: '13%',
          c5: '113',
          c6: '10',
          c7: 'ceshiceshi'
        },
        {
          c1: 'SUP202108102321',
          c2: this.$t('仓储成本'),
          c3: '￥100/平方',
          c4: '13%',
          c5: '113',
          c6: '10',
          c7: 'ceshiceshi'
        },
        {
          c1: 'SUP202108102322',
          c2: this.$t('运输费用'),
          c3: '￥10/个',
          c4: '13%',
          c5: '11.3',
          c6: '1',
          c7: 'ceshiceshi'
        }
      ],
      editColumns: [
        {
          type: 'checkbox',
          width: '50'
        },
        {
          field: 'c1',
          headerText: this.$t('成本项编号'),
          textAlign: 'left',
          template: () => {
            return {
              template: Vue.component('todo-column-template', {
                template: `
                                  <div class="mian-txt">
                                    <div class="mian-title">{{data.c1}}</div>
                                    <div class="mian-icon"> <mt-icon name="icon_solid_Delete1"/><span>删除</span></div>
                                  </div>`,
                data: function () {
                  return { data: {} }
                }
              })
            }
          }
        },
        {
          field: 'c2',
          headerText: this.$t('成本项名称'),
          textAlign: 'left',
          showColumnChooser: true
        },
        {
          field: 'c3',
          headerText: this.$t('未税单价'),
          textAlign: 'left',
          showColumnChooser: true
        },
        {
          field: 'c4',
          headerText: this.$t('税率'),
          textAlign: 'left',
          showColumnChooser: true
        },
        {
          field: 'c5',
          headerText: this.$t('含税单价'),
          textAlign: 'left',
          showColumnChooser: true
        },
        {
          field: 'c6',
          headerText: this.$t('数量'),
          textAlign: 'left',
          showColumnChooser: true
        }
      ],
      pageSettings: {
        pageSize: 10
      }
    }
  },
  methods: {
    changTab(id) {
      this.tabId = id
    },
    selectProduct(id) {
      this.productId = id
    },
    changeEV() {}
  }
}
</script>

<style lang="scss" scoped>
.flex1 {
  flex: 1;
}
.mr-10 {
  margin-top: 10px;
}
.mb-10 {
  padding-bottom: 10px;
}
.border-top {
  position: relative;
  &:before {
    content: ' ';
    display: inline-block;
    width: 100%;
    height: 1px;
    background: #e8e8e8;
    position: absolute;
    left: 0;
    top: -10px;
  }
}

.evaluation-container {
  width: 100%;
  min-height: 100%;
  background: #fff;
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 4px;

  .banner-line {
    height: 50px;
    line-height: 50px;
    background: rgba(250, 250, 250, 1);
    border-radius: 4px 4px 0 0;
    border-bottom: 1px solid rgba(232, 232, 232, 1);

    .left-part {
      padding: 0 20px;
      width: 357px;
      align-items: center;
    }
  }

  .bt-detail {
    width: 100%;
    flex: 1;

    .ev-item-box {
      width: 357px;
      box-sizing: border-box;
      padding: 20px;

      .ev-item {
        padding: 10px 20px;
        background: rgba(255, 255, 255, 1);
        border: 1px solid rgba(232, 232, 232, 1);
        border-radius: 4px;
        box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
        margin-bottom: 20px;
        cursor: pointer;

        .tp-line {
          justify-content: space-between;

          .title-left {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            height: 14px;
            font-size: 12px;
            font-family: PingFangSC;

            color: rgba(35, 43, 57, 1);
            text-align: left;
          }
          .title-right {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            height: 14px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: normal;
            color: rgba(35, 43, 57, 1);
            text-align: right;
          }
        }
        .tp-bottom {
          margin-top: 6px;
          justify-content: space-between;
          .title-left {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            height: 12px;
            font-size: 12px;
            font-family: PingFangSC;

            color: #9a9a9a;
            text-align: left;
          }
          .title-right {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            height: 12px;
            font-size: 12px;
            font-family: PingFangSC;
            font-weight: normal;
            color: #6386c1;
            text-align: right;
          }
        }
        .tp-f-bottom {
          margin-top: 10px;
          font-size: 10px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(154, 154, 154, 1);
          justify-content: space-between;
          .title-center {
            text-align: center;
          }
          .title-right {
            text-align: right;
          }
        }
      }

      .active {
        background: rgba(245, 246, 249, 1);
        border: 1px solid rgba(232, 232, 232, 1);
        border-radius: 4px;
        box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
        position: relative;
        &::before {
          content: ' ';
          display: inline-block;
          width: 3px;
          height: 100%;
          position: absolute;
          left: 0;
          top: 0;
          background: rgba(99, 134, 193, 1);
          border-radius: 4px 0 0 4px;
        }
        &::after {
          content: '';
          width: 6px;
          height: 6px;
          background: 0 0;
          border-left: none;
          border-top: none;
          border-right: 1px solid #6386c1;
          border-bottom: 1px solid #6386c1;
          position: absolute;
          transform: rotate(-45deg);
          cursor: pointer;
          right: -10px;
          top: calc(50% - 3px);
        }
      }
    }
    .ev-detail {
      flex: 1;
      border-left: 10px solid rgba(232, 232, 232, 1);
      overflow-x: auto;

      .detail-banner {
        padding: 20px;
        border: 1px solid rgba(232, 232, 232, 1);

        .title-banner {
          height: 20px;
          font-size: 20px;
          font-family: DINAlternate;
          font-weight: bold;
          color: rgba(41, 41, 41, 1);
        }
        .detail-info {
          height: 14px;
          margin-top: 10px;
          font-size: 14px;
          line-height: 14px;
          color: rgba(157, 170, 191, 1);
          flex-wrap: wrap;

          .sub-title {
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: normal;
            color: rgba(41, 41, 41, 1);
            white-space: nowrap;
            margin-right: 30px;
          }

          .info-item {
            white-space: nowrap;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: normal;
            color: rgba(157, 170, 191, 1);
            margin-right: 20px;
            i {
              color: rgba(157, 170, 191, 1);
            }
          }
        }
      }
    }

    .right-part {
      margin-top: 20px;

      .tab-item {
        cursor: pointer;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(154, 154, 154, 1);
        padding: 0 20px;
      }
      .active {
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        position: relative;

        &:after {
          content: ' ';
          display: inline-block;
          width: calc(100% - 56px);
          height: 2px;
          background: rgba(99, 134, 193, 1);
          border: 1px solid rgba(0, 70, 156, 0.1);
          border-radius: 4px;
          text-align: center;
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          top: 26px;
        }
      }
    }

    .grid-box {
      width: 100%;
      .main-grid {
        padding: 0 20px;
      }
      // height: 100%;
      .setting-banner {
        width: 100%;
        height: 50px;
        padding: 0 20px;
        display: flex;
        align-items: center;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(79, 91, 109, 1);
        // border-left: 1px solid #e8e8e8;
        // border-right: 1px solid #e8e8e8;

        i {
          font-size: 16px;
          line-height: 14px;
          display: inline-block;
          height: 16px;
          width: 16px;
          cursor: pointer;
        }

        span {
          display: inline-block;
          margin-left: 6px;
          cursor: pointer;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.evaluation-container {
  display: flex;
  flex-direction: column;

  .page-content {
    flex: 1;
    min-height: 0;
    .grid-no-toolbar {
      padding-top: 20px;
    }
  }
  .custom-price-td {
    position: relative;
    left: -10px;
    border-left: 1px solid #e8e8e8;
    .price-item {
      list-style: none;
      width: 197px;
      height: 50px;
      background: rgba(255, 255, 255, 1);
      line-height: 50px;
      position: relative;
      padding-left: 26px;
      div {
        box-shadow: inset 0 -1px 0 0 #e8e8e8;
      }
      &:hover {
        background: rgba(238, 238, 238, 1);
      }
      &:before {
        content: '';
        width: 6px;
        height: 6px;
        border: 1px solid #e8e8e8;
        position: absolute;
        left: 10px;
        top: calc(50% - 3px);
        background: #ffffff;
        z-index: 2;
      }
      &:after {
        content: '';
        width: 0;
        height: 100%;
        position: absolute;
        border-left: 1px solid #e8e8e8;
        left: 13px;
        top: 0;
        z-index: 1;
      }
      &:first-of-type {
        &:after {
          height: 50%;
          top: 50%;
          bottom: auto;
        }
      }
      &:last-of-type {
        div {
          box-shadow: none;
        }
        &:after {
          height: 50%;
          top: auto;
          bottom: 50%;
        }
      }
    }
    &.single-item {
      border-left: none;
      .price-item {
        &:after {
          display: none;
        }
      }
    }
  }
  .e-templatecell {
    position: relative;
  }
  .e-gridcontent {
    .e-content {
      overflow-y: auto;
    }
  }
  .top-left-tag {
    td {
      &:first-of-type {
        padding-left: 5px;
        position: relative;
        &:before {
          content: '';
          height: 0;
          width: 0;
          border-right: 30px solid transparent;
          border-top: 30px solid #eda133;
          position: absolute;
          left: 0;
          top: 0;
        }
        &:after {
          content: '最优';
          height: 0;
          width: 0;
          font-size: 12px;
          transform: scale(0.8) rotate(-45deg);
          color: #ffffff;
          position: absolute;
          left: -2px;
          top: 11px;
          white-space: nowrap;
        }
      }
    }
  }
  .mt-tabs-container {
    width: 100%;
    .tab-wrap {
      padding: 0 !important;
    }
  }
  .custom-td {
    position: absolute;
    left: -10px;
    top: 0;
    li {
      list-style: none;
      width: 197px;
      height: 50px;
      background: rgba(255, 255, 255, 1);
      line-height: 50px;
      position: relative;
      padding-left: 26px;
      box-shadow: inset 0 -1px 0 0 #e8e8e8;
      &:last-of-type {
        box-shadow: none;
      }
      &:hover {
        background: rgba(238, 238, 238, 1);
      }
    }
  }
  .custom-price-td {
    position: relative;
    left: -10px;
    border-left: 1px solid #e8e8e8;
    .price-item {
      list-style: none;
      width: 197px;
      height: 50px;
      background: rgba(255, 255, 255, 1);
      line-height: 50px;
      position: relative;
      padding-left: 26px;
      div {
        box-shadow: inset 0 -1px 0 0 #e8e8e8;
      }
      &:hover {
        background: rgba(238, 238, 238, 1);
      }
      &:before {
        content: '';
        width: 6px;
        height: 6px;
        border: 1px solid #e8e8e8;
        position: absolute;
        left: 10px;
        top: calc(50% - 3px);
        background: #ffffff;
        z-index: 2;
      }
      &:after {
        content: '';
        width: 0;
        height: 100%;
        position: absolute;
        border-left: 1px solid #e8e8e8;
        left: 13px;
        top: 0;
        z-index: 1;
      }
      &:first-of-type {
        &:after {
          height: 50%;
          top: 50%;
          bottom: auto;
        }
      }
      &:last-of-type {
        div {
          box-shadow: none;
        }
        &:after {
          height: 50%;
          top: auto;
          bottom: 50%;
        }
      }
    }
    &.single-item {
      border-left: none;
      .price-item {
        &:after {
          display: none;
        }
      }
    }
  }
  .e-templatecell {
    position: relative;
  }
  .e-gridcontent {
    .e-content {
      overflow-y: auto;
    }
  }
  .top-left-tag {
    td {
      &:first-of-type {
        padding-left: 5px;
        position: relative;
        &:before {
          content: '';
          height: 0;
          width: 0;
          border-right: 30px solid transparent;
          border-top: 30px solid #eda133;
          position: absolute;
          left: 0;
          top: 0;
        }
        &:after {
          content: '最优';
          height: 0;
          width: 0;
          font-size: 12px;
          transform: scale(0.8) rotate(-45deg);
          color: #ffffff;
          position: absolute;
          left: -2px;
          top: 11px;
          white-space: nowrap;
        }
      }
    }
  }
}
.tab-item {
  .prop-box {
    text-align: center;
    .rank-name {
      height: 16px;
      font-size: 16px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(154, 154, 154, 1);
    }
    .rank-desc {
      height: 12px;
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(41, 41, 41, 1);
      margin-top: 8px;
      display: flex;
      justify-content: center;

      .type-txt {
        width: 12px;
        height: 12px;
        background: rgba(237, 161, 51, 0.1);
        border-radius: 1px;
        font-size: 8px;
        font-family: PingFangSC;
        font-weight: 500;
        margin-left: 6px;
        display: inline-block;
        i {
          transform: scale(0.8);
        }
      }
      .type-1 {
        color: rgba(99, 134, 193, 1);
        background: rgba(99, 134, 193, 0.3);
        border-radius: 1px;
      }
      .type-2 {
        background: rgba(237, 161, 51, 0.3);
        color: rgba(237, 161, 51, 1);
      }
    }
  }
}
.evaluation-container {
  .e-grid {
    // border-right: 0;
    // border-left: 0;
    border-radius: 8px 8px 8px 8px !important;
  }
  .grid-wraps {
    display: flex;
    flex-direction: column;
    height: 100%;
    .e-treegrid {
      flex: 1;
      border: 1px solid #e0e0e0;
      border-radius: 8px 8px 8px 8px;
    }
    .e-grid {
      border-right: 0;
      border-left: 0;
      border-radius: 8px 8px 8px 8px;
    }
    .e-gridheader {
      border-left: none;
      border-right: none;
      border-top: none;
    }
    .mt-pagertemplate {
      margin: 0;
      padding: 10px 0;
      border-top: 1px solid #e8e8e8;
    }
  }

  .e-grid .e-gridheader,
  .e-grid .e-gridheader tr:first-child th {
    background: #fff;
  }

  .e-grid .e-gridheader,
  .e-grid .e-gridheader tr:first-child th {
    border-radius: 8px 8px 0 0;
  }

  .e-grid th.e-headercell:nth-child(1) {
    background: #fff !important;
    border-right: 0 !important;
  }

  .e-treegridexpand:hover::before,
  .e-treegridcollapse:hover::before {
    color: #9daabf;
  }

  .e-treegridexpand,
  .e-treegridcollapse {
    color: #9daabf;
  }

  .e-checkbox-wrapper .e-frame.e-check,
  .e-css.e-checkbox-wrapper .e-frame.e-check {
    background-color: #00469c !important;
  }

  .e-grid .e-headercell {
    color: #161f2b;
    font-weight: 500;
  }

  .e-grid .e-rowcell {
    color: #35404e;
  }

  .e-grid .e-frame {
    width: 16px !important;
    height: 16px !important;
    border: 1px solid #9daabf !important;
  }

  .e-grid .e-headercell {
    font-weight: 500;
    color: #161f2b;
  }

  .e-grid .e-row {
    height: 50px !important;
  }

  .e-grid .e-treecolumn-container {
    display: flex !important;
    color: #35404e;
  }

  .e-grid .e-treecolumn-container .e-treecell .mian-title {
    color: #00469c !important;
  }
  .e-grid .e-treecolumn-container .e-treecell .mian-icon {
    i {
      font-size: 12px;
      color: #9daabf;
    }
    font-size: 12px;
    color: #9daabf;
  }

  .e-grid .e-headertext {
    font-weight: 500;
    color: #161f2b;
  }
}

.disabled {
  align-items: center;
  color: #b9babc;
  .green {
    display: block;
    width: 6px;
    height: 6px;
    background: #b9babc;
    border-radius: 100%;
    margin-right: 4px;
  }
}

.block-item {
  display: inline-block;
  position: relative;
  left: -20px;
}

.block-item i.mt-icon-arrowhead-right {
  display: inline-block;
  vertical-align: middle;
  color: #9daabf;
}

.openly {
  align-items: center;
  color: #59d83b;
  .green {
    display: block;
    width: 6px;
    height: 6px;
    background: #59d83b;
    color: #161f2b;
    border-radius: 100%;
    margin-right: 4px;
  }
}

.todo-list-column {
  color: #6386c1;
  font-size: 14px;
}

.operator-btn {
  color: #6386c1;
  font-size: 14px;
  padding-right: 20px;
}
.evaluation-container .grid-options-bar {
  display: none !important;
}
</style>
