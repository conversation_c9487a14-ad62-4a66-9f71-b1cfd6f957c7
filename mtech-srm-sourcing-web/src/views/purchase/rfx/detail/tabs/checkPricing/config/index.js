import { i18n } from '@/main.js'
import Vue from 'vue'
export const historyListColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    width: '200',
    field: 'column1',
    headerText: i18n.t('供应商编号'),
    cellTools: [{ id: 'History', icon: 'icon_Editor', title: i18n.t('查看历史') }]
  },
  {
    field: 'column2',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'column3',
    headerText: i18n.t('报价次数')
  },
  {
    field: 'column4',
    headerText: i18n.t('状态'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div class="action-boxs"><span class="status-text isStatus" v-if="data.column4== 1">{{ $t('有效') }}</span><span class="status-text unStatus" v-else>{{ $t('已过期') }}</span></div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'column5',
    headerText: i18n.t('供应商阶段')
  },
  {
    field: 'column6',
    headerText: i18n.t('是否阶梯报价')
  },
  {
    field: 'column7',
    headerText: i18n.t('阶梯方式')
  },
  {
    field: 'column8',
    headerText: i18n.t('阶梯范围'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<ul :class="['custom-price-td', {'single-item':data.items.length<2}]">
          <li class="price-item" v-for="(item, index) in data.items" :key="index"><div>{{item.column8}}</div></li>
        </ul>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'column9',
    headerText: i18n.t('未税单价'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<ul  class="custom-td">
                      <li v-for="(item, index) in data.items" :key="index"><div>{{item.column9}}</div></li>
                    </ul>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'column10',
    headerText: i18n.t('含税单价'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<ul  class="custom-td">
                      <li v-for="(item, index) in data.items" :key="index"><div>{{item.column10}}</div></li>
                    </ul>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'column11',
    headerText: i18n.t('报价数量'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<ul  class="custom-td">
                      <li v-for="(item, index) in data.items" :key="index"><div>{{item.column11}}</div></li>
                    </ul>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'column12',
    headerText: i18n.t('税率'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<ul  class="custom-td">
                      <li v-for="(item, index) in data.items" :key="index"><div>{{item.column12}}</div></li>
                    </ul>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'column13',
    headerText: i18n.t('含税单价'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<ul  class="custom-td">
                      <li v-for="(item, index) in data.items" :key="index"><div>{{item.column13}}</div></li>
                    </ul>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'column14',
    headerText: i18n.t('含税总价'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<ul  class="custom-td">
                      <li v-for="(item, index) in data.items" :key="index"><div>{{item.column14}}</div></li>
                    </ul>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'column15',
    headerText: i18n.t('备注'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<ul  class="custom-td">
                      <li v-for="(item, index) in data.items" :key="index"><div>{{item.column15}}</div></li>
                    </ul>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'column16',
    headerText: i18n.t('明细报价'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<ul  class="custom-td">
                      <li v-for="(item, index) in data.items" :key="index"><div>{{item.column16}}</div></li>
                    </ul>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'column17',
    headerText: i18n.t('附件'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<ul  class="custom-td">
                      <li v-for="(item, index) in data.items" :key="index"><div>{{item.column17}}</div></li>
                    </ul>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'column18',
    headerText: i18n.t('报价历史'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<ul  class="custom-td">
                      <li v-for="(item, index) in data.items" :key="index"><div>{{item.column18}}</div></li>
                    </ul>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  }
]

export const getDetailGridData = (len = 3) => {
  let res = []
  for (let i = 0; i < len; i++) {
    res.push({
      column8: `${i * 100 + 1}-${(i + 1) * 100}`,
      column9: 'column9-' + i,
      column10: 'column10-' + i,
      column11: 'column11-' + i,
      column12: 'column12-' + i,
      column13: 'column13-' + i,
      column14: 'column14-' + i,
      column15: 'column15-' + i,
      column16: 'column16-' + i,
      column17: 'column17-' + i,
      column18: 'column19-' + i
    })
  }
  return res
}

export const getListGridData = (len = 10) => {
  let res = []
  for (let i = 0; i < len; i++) {
    //第一行有4个报价，其他行，随机
    let _ramdom = i == 0 ? 4 : (parseInt(Math.random(10) * 10) % 3) + 1
    let _details = getDetailGridData(_ramdom)
    res.push({
      column1: 'SUPC2021080122135' + i,
      column2: 'SUPC_NAME_20210801-' + i,
      column3: i,
      column4: i % 2,
      column5: [i18n.t('潜在'), i18n.t('正式'), i18n.t('准入')][i % 3],
      column6: [i18n.t('是'), i18n.t('否')][i % 2],
      column7: [i18n.t('按数量')][i % 1],
      items: _details,
      column9: 'column9-' + i,
      column10: 'column10-' + i,
      column11: 'column11-' + i,
      column12: 'column12-' + i,
      column13: 'column13-' + i,
      column14: 'column14-' + i,
      column15: 'column15-' + i,
      column16: 'column16-' + i,
      column17: 'column17-' + i,
      column18: 'column19-' + i,
      isBetter: i < 3
    })
  }
  return res
}
