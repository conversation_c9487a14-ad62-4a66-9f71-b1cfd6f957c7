<template>
  <div class="strategy-form">
    <slot v-if="rfxStrategyDetailItems.length" />
    <FormGenerator
      v-if="rfxStrategyDetailItems.length"
      :field-defines="formGenerator.fieldDefines"
      :form-field-config="formGenerator.formFieldConfig"
      ref="appForm"
    />
  </div>
</template>

<script>
import FormGenerator from 'ROUTER_PURCHASE_RFX/components/FormGenerator'
import { utils } from '@mtech-common/utils'

export default {
  components: {
    FormGenerator
  },
  props: {
    formFieldConfig: {
      type: Object,
      default: () => {}
    },
    strategyConfig: {
      type: Object,
      default: () => {}
    },
    rfxStrategyDetailItems: {
      type: Array,
      default: () => []
    },
    isDisabled: {
      type: Boolean,
      default: undefined
    }
  },
  data() {
    return {
      formGenerator: {
        fieldDefines: [],
        formFieldConfig: utils.cloneDeep(this.formFieldConfig)
      }
    }
  },
  watch: {
    rfxStrategyDetailItems: {
      deep: true,
      handler: function () {
        this.$nextTick(this.gnFieldDefines)
      }
    },
    formFieldConfig: {
      deep: true,
      immediate: true,
      handler: function () {
        this.formGenerator.formFieldConfig = utils.cloneDeep(this.formFieldConfig)
      }
    },
    strategyConfig: {
      deep: true,
      handler: function () {
        this.$nextTick(this.gnFieldDefines)
      }
    }
  },
  mounted() {
    this.gnFieldDefines()
  },
  methods: {
    parentGetFormData() {
      return this.$refs?.appForm?.parentGetFormData() || Promise.resolve({})
    },
    gnFieldDefines() {
      const formFieldConfigKeys = Object.keys(this.formGenerator.formFieldConfig)
      this.formGenerator.fieldDefines = this.rfxStrategyDetailItems
        .filter((detail) => {
          if (!formFieldConfigKeys.includes(detail.strategyCode)) {
            console.warn('未定义表单:' + detail.strategyCode)
            return false
          }
          return true
        })
        .map((item) => ({
          fieldCode: item.strategyCode,
          fieldName: item.strategyName,
          readonly: !item.editEnable || this.isDisabled
        }))
      formFieldConfigKeys.forEach((formFieldConfigKey) => {
        const val = this.strategyConfig?.[formFieldConfigKey]
        if (typeof val !== 'undefined') {
          this.formGenerator.formFieldConfig[formFieldConfigKey].defaultValue = () => val
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.strategy-form {
  padding: 20px;
  background-color: #fff;
}
</style>
