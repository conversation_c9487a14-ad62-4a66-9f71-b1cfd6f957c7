<template>
  <div class="full-height">
    <div class="toolbar">
      <div
        :class="{
          'svg-option-item': true,
          disabled: isDisabled
        }"
        @click="save"
      >
        <mt-icon name="icon_solid_Save" />
        <span>{{ $t('保存') }}</span>
      </div>
    </div>
    <!-- 多轮 -->
    <mt-loading v-if="loading" />
    <div
      class="round-container"
      v-show="!loading"
      v-for="(strategyConfigRow, index) in strategyConfigInfo.strategyConfigList"
      :key="index"
    >
      <!-- headerForm -->
      <strategyForm
        v-if="showTagCurrentRound && index === 0"
        :ref="'headerForm' + index"
        :strategy-config="strategyConfigRow"
        :rfx-strategy-detail-items="headerForm"
        :form-field-config="strategyConfigRow.formFieldConfig"
      />
      <strategyForm
        :ref="'appForm' + index"
        :strategy-config="strategyConfigRow"
        :rfx-strategy-detail-items="bodyForm"
        :form-field-config="strategyConfigRow.formFieldConfig"
      >
        <span class="tag-current-round" v-if="showTagCurrentRound">
          {{ $t(`第${strategyConfigRow.roundNo || 1}轮`) }}
        </span>
      </strategyForm>
    </div>
  </div>
</template>

<script>
import { HEADER_FIELD, getStrategyConfig } from 'ROUTER_PURCHASE_RFX/config/strategyConfig'
import { utils } from '@mtech-common/utils'
import { isEqual } from 'lodash'

export default {
  components: {
    strategyForm: () => import('./components/strategyForm.vue')
  },
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      loading: false,
      strategyConfigInfo: {
        strategyConfigList: []
      },
      rfxStrategyDetailItems: [], // 用于表单生成
      cacheRfxStrategyDetailItems: [],
      formList: [], //判断新旧数据
      oldFormData: null
    }
  },
  computed: {
    isDisabled() {
      // 非草稿状态和驳回都要禁止编辑
      return (
        (this.$route.query.source !== 'rfq' && ![-2, 0].includes(this.detailInfo.approveStatus)) ||
        [8, -1].includes(this.detailInfo.status) ||
        [20, 100].includes(this.detailInfo.transferStatus)
      )
    },
    /**
     * 不是所有的策略报告都有轮次信息
     */
    showTagCurrentRound() {
      return !!this.rfxStrategyDetailItems.find((e) => e.strategyCode === 'roundCount')
    },
    isRfq() {
      return !!['rfq'].includes(this.$route.query.source)
    },
    strategyConfigId() {
      return this.detailInfo?.strategyConfigId // props
    },
    headerForm() {
      let result = this.rfxStrategyDetailItems
      return result.filter((e) => HEADER_FIELD.includes(e.strategyCode))
    },
    bodyForm() {
      if (this.showTagCurrentRound) {
        return this.cacheRfxStrategyDetailItems.filter(
          (e) => !HEADER_FIELD.includes(e.strategyCode)
        )
      }
      return this.cacheRfxStrategyDetailItems
    },
    eliminateRuleForm() {
      return this.rfxStrategyDetailItems.filter((e) => e.strategyCode === 'eliminateRule')
    }
  },
  async mounted() {
    sessionStorage.removeItem('allocationRatio')
    this.loading = true
    // 根据rfxId查询策略信息
    await this.initStrategyConfigInfo()
    //获取表单配置信息
    await this.initRfxStrategyDetailItemsRes()
    // 合并默认值
    this.mergeDefaultStrategyConfig()
    // 重新计算轮次信息
    this.resetRoundCount()
    this.loading = false
    this.$nextTick(async () => {
      this.oldFormData = await this.getFormsData()
    })
  },

  methods: {
    // 内容是否修改
    async isContentChange() {
      const newFormData = await this.getFormsData()
      let needToDiffHeaderFields = this.headerForm?.map((item) => item.strategyCode) || []
      let needToDiffBodyFields = this.bodyForm?.map((item) => item.strategyCode) || []
      let needToDiffFields = [...needToDiffHeaderFields, ...needToDiffBodyFields]
      let oldData = this.needToDiff(this.oldFormData, needToDiffFields)
      let newData = this.needToDiff(newFormData, needToDiffFields)
      return !isEqual(oldData, newData)
    },
    // 取出需要对比的数据
    needToDiff(data, fields) {
      const numFields = [
        'expansionNum',
        'minSupplierBiddingQuantity',
        'nonDirectionalSupNum',
        'tenderExtendTime',
        'triggerExtendRemainingTime',
        'roundInterval'
      ]
      const nullFields = ['totalRanking', 'pointMode', 'needExamAudit'] //特殊处理数据有些数据保存后返回为null，此时对比会有问题
      let list = data?.map((item) => {
        let _obj = {}
        for (let i in item) {
          if (nullFields.includes(i)) {
            // 特殊处理字段
            item[i] && (_obj[i] = item[i])
          } else if (fields.includes(i)) {
            _obj[i] = numFields.includes(i)
              ? item[i] || item[i] == '0'
                ? Number(item[i])
                : 0
              : item[i]
          }
        }
        return _obj
      })
      return list
    },
    getFieldConfig(strategyConfigRow) {
      const roundNo = strategyConfigRow.roundNo || 1
      let result = getStrategyConfig()
      // 如果 非采、竞价、草稿状态隐藏时间 不作隐藏判断，直接禁用
      // if (
      //   this.detailInfo.businessTypeCode !== 'BTTCL004' &&
      //   this.detailInfo.sourcingMode === 'bidding_price'
      // ) {
      //   let timeFields = null
      //   if (
      //     this.detailInfo.status === 0 ||
      //     (this.detailInfo.transferStatus <= 38 &&
      //       !this.detailInfo?.strategyResponse?.tecOpenBidTime &&
      //       !this.detailInfo?.strategyResponse?.tecScoreEndTime &&
      //       !this.detailInfo?.strategyResponse?.tecBidStartTime)
      //   ) {
      //     timeFields = [
      //       'tecOpenBidTime',
      //       'tecScoreEndTime',
      //       'tecBidStartTime',
      //       'tenderStartTime',
      //       'openBidTime'
      //     ]
      //   } else if (this.detailInfo.transferStatus < 41) {
      //     timeFields = ['tenderStartTime', 'openBidTime']
      //   }
      //   timeFields?.forEach((key) => {
      //     if (Object.prototype.hasOwnProperty.call(result, key)) {
      //       delete result[key]
      //     }
      //   })
      // }

      if (result.supplierSelectionRange) {
        result.supplierSelectionRange.api = this.$API.masterData
          .dictionaryGetList({
            dictCode: 'SupplierRange'
          })
          .then((res) => {
            return res.data.map((item) => ({
              text: item.itemName,
              value: item.itemCode
            }))
          })
      }
      if (result.biddingMode) {
        result.biddingMode.api = this.$API.masterData
          .dictionaryGetList({
            dictCode: 'biddingMode'
          })
          .then((res) => {
            return res.data.map((item) => ({
              text: item.itemName,
              value: item.itemCode
            }))
          })
      }
      if (result.directionalBargaining) {
        result.directionalBargaining.handler = (event, _, formVm) => {
          const { value } = event
          this.setDirectionalBargaining(value, _, formVm)
        }
      }

      // 只有一轮 商务投标开始时间
      if (result.tenderStartTime) {
        result.tenderStartTime.handler = async (event) => {
          const handlerRoundTime = this.strategyConfigInfo.strategyConfigList.length === 1
          if (!handlerRoundTime) return
          const { value } = event
          const appFormVm = this.$refs['appForm0'][0].$refs.appForm
          if (!appFormVm.form) appFormVm.form = {}
          this.$set(appFormVm.form, 'roundStartTime', value)
        }
      }

      // 只有一轮 商务开标时间
      if (result.openBidTime) {
        result.openBidTime.handler = async (event) => {
          const handlerRoundTime = this.strategyConfigInfo.strategyConfigList.length === 1
          if (!handlerRoundTime) return
          const { value } = event
          const appFormVm = this.$refs['appForm0'][0].$refs.appForm
          if (!appFormVm.form) appFormVm.form = {}
          this.$set(appFormVm.form, 'roundEndTime', value)
        }
      }

      // 第一轮开始时间为商务投标开始时间，最后一轮结束时间为商务开标时间；
      if (result.roundStartTime) {
        result.roundStartTime.handler = async (event) => {
          const handlerRoundTime =
            this.strategyConfigInfo.strategyConfigList.length > 0 && Number(roundNo) === 1
          if (!handlerRoundTime) return
          const { value } = event
          const headerFormVm = this.$refs['headerForm0']
            ? this.$refs['headerForm0'][0].$refs.appForm
            : {}
          if (!headerFormVm?.form) headerFormVm.form = {}
          this.$set(headerFormVm.form, 'tenderStartTime', value)
          this.calculateRoundTime(await this.getFormsData())
        }
      }

      if (result.roundEndTime) {
        result.roundEndTime.handler = async (event) => {
          const handlerRoundTime = this.strategyConfigInfo.strategyConfigList.length - roundNo === 0
          if (!handlerRoundTime) return
          const { value } = event

          const headerFormVm = this.$refs['headerForm0'][0].$refs.appForm
          if (!headerFormVm.form) headerFormVm.form = {}
          this.$set(headerFormVm.form, 'openBidTime', value)
          this.calculateRoundTime(await this.getFormsData())
        }
      }

      // 整单中标控制整单报价
      if (result.accountingMode) {
        result.accountingMode.handler = (event, _, formVm) => {
          let { value } = event
          if (value === 'single_item') {
            formVm.form['priceControl'] = 'all'
          }
        }
      }

      if (this.showTagCurrentRound && result.roundCount) {
        result.roundCount = {
          type: 'select',
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { text: '1', value: '1' },
            { text: '2', value: '2' },
            { text: '3', value: '3' },
            { text: '4', value: '4' },
            { text: '5', value: '5' }
          ],
          handler: (event, formItem) => {
            const { value } = event
            const { fieldCode } = formItem
            if (fieldCode === 'roundCount') {
              this.setRoundCount(value)
            }
          }
        }
      }

      if (result.needTecBid) {
        const tecFields = [
          'tecOpenBidTime',
          'tecScoreEndTime',
          'tecBidStartTime',
          'biddingPromotion'
        ]
        result.needTecBid.handler = (event) => {
          const { value } = event
          const hideFormItem = !(Number(value) === 1)
          const headerFormVm = this.$refs['headerForm0'] // 暂不确认取值headerForm场景，兼容处理报错
            ? this.$refs['headerForm0'][0].$refs.appForm
            : null
          const fieldDefines = headerFormVm?.fieldDefines
          if (fieldDefines) {
            const findField = (fieldCode) => fieldDefines.find((e) => e.fieldCode === fieldCode)
            for (const tecField of tecFields) {
              const row = findField(tecField)
              if (row) {
                row.hideFormItem = hideFormItem
                row.form && (row.form.hideFormItem = hideFormItem)
                if (hideFormItem) {
                  headerFormVm.form = {
                    ...headerFormVm.form,
                    [tecField]: null
                  }
                }
              }
            }
          }
        }
        // 默认值
        const hideFormItem = !(Number(strategyConfigRow.needTecBid) === 1)
        for (const tecField of tecFields) {
          const row = result[tecField]
          if (row) {
            row.hideFormItem = hideFormItem
          }
        }
      }

      if (result.roundInterval) {
        result.roundInterval.handler = async () => {
          this.calculateRoundTime(await this.getFormsData())
        }
      }

      if (result.eliminateFlag) {
        result.eliminateFlag.handler = (event, _, formVm) => {
          const { value } = event
          this.setSupplierNoLimit(value, _, formVm)
        }
      }
      return result
    },
    hasForm(strategyCode, rfxStrategyDetailItems) {
      const strategyCodes = Array.isArray(strategyCode) ? strategyCode : [strategyCode]
      const result = strategyCodes.every((code) => {
        return !!(rfxStrategyDetailItems || this.rfxStrategyDetailItems).find(
          (e) => e.strategyCode === code
        )
      })
      return result
    },
    setSupplierNoLimit(value, _, formVm) {
      const supplierNoLimit = formVm.fieldDefines.find((e) => e.fieldCode === 'supplierNoLimit')
      if (supplierNoLimit) {
        const readonly = !!(Number(value) === 1)
        if (readonly) {
          formVm.form = {
            ...formVm.form,
            supplierNoLimit: 0
          }
        }
        supplierNoLimit.readonly = readonly
        supplierNoLimit.form.readonly = readonly
        if (!readonly) {
          this.cacheRfxStrategyDetailItems = this.cacheRfxStrategyDetailItems.filter(
            (item) => item.strategyCode !== 'eliminateRule'
          )
        } else {
          this.cacheRfxStrategyDetailItems = [
            ...this.cacheRfxStrategyDetailItems,
            ...this.eliminateRuleForm
          ]
        }
      }
    },
    setDirectionalBargaining(value, _, formVm) {
      const nonDirectionalSupNum = formVm.fieldDefines.find(
        (e) => e.fieldCode === 'nonDirectionalSupNum'
      )
      if (nonDirectionalSupNum) {
        const readonly = !!(Number(value) === 1)
        if (readonly) {
          formVm.form = {
            ...formVm.form,
            nonDirectionalSupNum: 0
          }
        }
        nonDirectionalSupNum.readonly = readonly
        nonDirectionalSupNum.form.readonly = readonly
      }
    },
    /**
     * 设置轮次
     * 修改 strategyConfigList
     */
    async setRoundCount(roundCount) {
      const strategyConfigList = await this.getFormsData()

      const oldRoundCount = strategyConfigList.length
      if (+roundCount === oldRoundCount) {
        return
      }

      if (roundCount < oldRoundCount) {
        strategyConfigList.splice(roundCount)
      } else if (roundCount > oldRoundCount) {
        let defaultConfig = Object.keys(strategyConfigList[0]).reduce((p, v) => {
          p[v] = null
          return p
        }, {})
        // 获取默认值
        defaultConfig = this.makeDefaultStrategyConfig(defaultConfig)
        const addData = new Array(roundCount - oldRoundCount).fill(null).map(() => ({
          ...defaultConfig
        }))
        strategyConfigList.push(...addData)
        // 补充轮次数量信息
        strategyConfigList.forEach((row, i) => {
          row.roundCount = roundCount
          row.roundNo = i + 1
          row.formFieldConfig = this.getFieldConfig(row)
        })
      }
      this.calculateRoundTime(strategyConfigList, true)
    },
    // 计算轮次时间
    async calculateRoundTime(strategyConfigList, resetTime = false) {
      const roundCount = strategyConfigList.length
      // 根据第一轮轮次时间,重置末轮轮次时间
      if (strategyConfigList.length >= 1) {
        // 第一轮开始时间为商务投标开始时间，最后一轮结束时间为商务开标时间；
        strategyConfigList[0].roundStartTime = strategyConfigList[0].tenderStartTime
        strategyConfigList[strategyConfigList.length - 1].roundEndTime =
          strategyConfigList[0].openBidTime

        // 有轮次间隔
        if (this.hasForm(['roundInterval'])) {
          const roundInterval = (+strategyConfigList[0].roundInterval || 0) * 60000
          // 总时间
          const totalRoundTime =
            +strategyConfigList[strategyConfigList.length - 1].roundEndTime -
            +strategyConfigList[0].roundStartTime
          // 每一轮时间
          const oneRoundTime =
            roundCount > 1
              ? (totalRoundTime - roundInterval * (roundCount - 1)) / roundCount
              : totalRoundTime
          for (let i = 0; i < strategyConfigList.length; i++) {
            if (i > 0) {
              strategyConfigList[i].roundStartTime =
                +strategyConfigList[i - 1].roundEndTime + roundInterval
            }

            if (i < strategyConfigList.length - 1) {
              strategyConfigList[i].roundEndTime =
                +strategyConfigList[i].roundStartTime + oneRoundTime
            }
          }
        } else if (resetTime) {
          // 没有轮次间隔
          for (let i = 0; i < strategyConfigList.length - 1; i++) {
            strategyConfigList[i].roundStartTime = null
            strategyConfigList[i].roundEndTime = null
            if (i === 0) {
              strategyConfigList[i].roundStartTime = strategyConfigList[i].tenderStartTime
            }
            if (i === strategyConfigList.length - 1) {
              strategyConfigList[i].roundEndTime = strategyConfigList[i].openBidTime
            }
          }
        }
      }
      this.strategyConfigInfo.strategyConfigList = strategyConfigList
    },
    /**
     * 根据rfxId查询策略信息
     */
    async initStrategyConfigInfo() {
      const strategyConfigRes = await this.$API.strategyConfig
        .findByRfxId({
          sourcingMode: this.$route.query.source,
          rfxId: this.$route.query.rfxId
        })
        .catch(() => {})
      if (strategyConfigRes?.data) {
        const strategyConfigInfo = this.strategyConverter(strategyConfigRes.data)
        if (Array.isArray(strategyConfigInfo.strategyConfigList)) {
          strategyConfigInfo.strategyConfigList.forEach((e) => {
            e.formFieldConfig = {}
          })
        }
        this.strategyConfigInfo = strategyConfigInfo
        this.formList = utils.cloneDeep(
          this.formatDateFields(this.strategyConfigInfo.strategyConfigList)
        )
      }
      if (strategyConfigRes?.data?.strategyConfigList[0].allocationRatio) {
        sessionStorage.setItem(
          'allocationRatio',
          strategyConfigRes?.data?.strategyConfigList[0].allocationRatio
        )
      }
    },
    // 某些情况下, 接口返回的轮次数量并不等于轮次数据数量, 需要补充轮次数据
    resetRoundCount() {
      if (
        this.showTagCurrentRound &&
        this.strategyConfigInfo?.strategyConfigList?.[0]?.roundCount > 0 &&
        Number(this.strategyConfigInfo.strategyConfigList[0].roundCount) !==
          this.strategyConfigInfo.strategyConfigList.length
      ) {
        this.setRoundCount(this.strategyConfigInfo.strategyConfigList[0].roundCount)
      }
      this.formList = utils.cloneDeep(
        this.formatDateFields(this.strategyConfigInfo.strategyConfigList)
      )
    },
    // 统一转化为 string 类型, 因为 defaultValue 也是 string
    strategyConverter(strategyConfigInfo) {
      if (strategyConfigInfo && Array.isArray(strategyConfigInfo.strategyConfigList)) {
        const timeFields = this.getTimeFields()
        strategyConfigInfo.strategyConfigList.forEach((obj) => {
          for (const key of Object.keys(obj)) {
            // 后端空的时间戳会返回 0 而不是 null
            if (timeFields.includes(key) && Number(obj[key]) === 0) {
              obj[key] = null
            }
            if (typeof obj[key] === 'number' && key !== 'eliminateRule') {
              obj[key] = '' + obj[key]
            }
          }
        })
      }
      return strategyConfigInfo
    },
    /**
     * 合并默认值显示
     */
    mergeDefaultStrategyConfig() {
      let { strategyConfigList } = this.strategyConfigInfo
      if (strategyConfigList) {
        strategyConfigList = [...strategyConfigList].map((row) =>
          this.makeDefaultStrategyConfig(row)
        )
        strategyConfigList.forEach((e) => {
          e.formFieldConfig = this.getFieldConfig(e)
        })
        this.strategyConfigInfo.strategyConfigList = strategyConfigList
      }
    },
    // 合并默认值
    makeDefaultStrategyConfig(row) {
      const rowKeys = Object.keys(row)
      const pk = 'strategyCode'
      for (const key of rowKeys) {
        const formConf = this.rfxStrategyDetailItems.find((e) => e[pk] === key)
        if (formConf?.defaultValue && (row[key] === null || row[key] === undefined)) {
          row[key] = formConf.defaultValue
        }
      }
      return row
    },
    // 轮次相关字段
    getRoundFields() {
      return Object.keys(getStrategyConfig()).filter((field) => !HEADER_FIELD.includes(field))
    },
    // 获取字段可编辑状态，覆盖配置
    getFiledEditStatus() {
      const fiedlsMap = {
        // ①技术投标开始时间、技术开标时间 tecBidStartTime tecOpenBidTime
        1: ['tecBidStartTime', 'tecOpenBidTime'],
        // ②商务标投标开始时间、商务开标时间、轮次（轮次中的信息）； tenderStartTime openBidTime
        2: ['tenderStartTime', 'openBidTime', ...this.getRoundFields(), 'roundCount']
      }
      let allowFields = []

      const transferStatus = this.detailInfo.transferStatus
      switch (Number(transferStatus)) {
        // （1）报名中：①②  35
        // （2）技术投标阶段：①② 38
        case 35:
        case 38:
          allowFields = [...fiedlsMap['1'], ...fiedlsMap['2']]
          break
        // （3）技术开标中：②  39
        // （4）技术评分阶段：② 40
        // （5）商务投标阶段：② 41
        case 39:
        case 40:
        case 41:
          allowFields = [...fiedlsMap['2']]
          break
        default:
          allowFields = []
          break
      }

      return {
        allowFields // 可以编辑的字段
      }
    },
    // 设置字段可编辑状态
    setAllowEdit(rfxStrategyDetailItems) {
      const { allowFields } = this.getFiledEditStatus()

      return rfxStrategyDetailItems.map((e) => {
        if (e.strategyCode === 'strategyCode') {
          e.strategyName = this.$t('投标时间扩展(单位：分钟)')
        }
        const editEnable0 = [
          // 草稿状态都可以编辑
          e.editEnable === 1 &&
            !allowFields.includes(e.strategyCode) &&
            Number(this.detailInfo.transferStatus) !== 0,
          // 不是草稿状态 不能修改轮次次数
          Number(this.detailInfo.transferStatus) !== 0 && e.strategyCode === 'roundCount'
        ]

        if (editEnable0.includes(true)) {
          e.editEnable = 0
        }

        // private Integer rfxGeneralType; 单据非采类型  1:通采 2:非采
        // 【非采】策略修改逻辑不变
        // 【通采】 立项后不支持修改策略信息-时间信息 (立项后所有信息都不支持修改)
        if (
          Number(this.detailInfo.rfxGeneralType) === 1 &&
          Number(this.detailInfo.transferStatus) !== 0
        ) {
          e.editEnable = 0
        }

        // 询报价-待发布
        if (this.isRfq && Number(this.detailInfo.transferStatus) === 1) {
          // 只可以编辑报价截止时间
          if (e.strategyCode === 'quotationEndTime') {
            e.editEnable = 1
          } else {
            e.editEnable = 0
          }
        }
        if (e.strategyCode === 'tecScoreEndTime' && this.detailInfo.transferStatus == 38) {
          e.editEnable = 1
        }

        // 非采待发布状态可以修改应标截止时间
        if (
          Number(this.detailInfo.rfxGeneralType) === 2 &&
          Number(this.detailInfo.transferStatus) === 1 &&
          e.strategyCode === 'responseBidEndTime'
        ) {
          e.editEnable = 1
        }
        // approveStatus 1：待审批 2：审批通过    禁止编辑 通采非采保持一致
        if (
          Number(this.detailInfo.rfxGeneralType) === 1 ||
          Number(this.detailInfo.rfxGeneralType) === 2
        ) {
          // 通采
          if ([0, 1].includes(Number(this.detailInfo.transferStatus))) {
            // 立项准备 和 待发布

            // 通采 且 立项准备 和 待发布 且 审批状态是待审批或审批通过，全部只读；
            if (
              [1, 2].includes(Number(this.detailInfo.approveStatus)) &&
              ['invite_bids', 'bidding_price'].includes(this.$route.query.source)
            ) {
              e.editEnable = 0
            }
            // 审批通过和审批驳回，按照阶段控制单据的编辑（就是现在系统的逻辑）；
          }
        }
        // 非采竞价事件禁止编辑
        if (
          Number(this.detailInfo.rfxGeneralType) === 2 &&
          this.detailInfo.sourcingMode === 'bidding_price' &&
          [
            'tecOpenBidTime',
            'tecScoreEndTime',
            'tecBidStartTime',
            'tenderStartTime',
            'openBidTime'
          ].includes(e.strategyCode)
        ) {
          e.editEnable = 0
        }
        return e
      })
    },
    /**
     * 获取表单配置信息
     */
    async initRfxStrategyDetailItemsRes() {
      const rfxStrategyDetailItemsRes = await this.$API.strategyConfig
        .getRfxStrategyDetailItems({
          strategyConfigId: this.strategyConfigId
        })
        .catch(() => {})
      if (rfxStrategyDetailItemsRes?.data) {
        rfxStrategyDetailItemsRes.data.forEach((x) => {
          if (x.strategyCode == 'allocationRatio') {
            let allocationRatio = sessionStorage.getItem('allocationRatio')
            if (!allocationRatio) {
              sessionStorage.setItem('allocationRatio', x.defaultValue)
            }
          }
        })
        const rfxStrategyDetailItems = rfxStrategyDetailItemsRes.data
        rfxStrategyDetailItems.sort((a, b) => b.sortValue - a.sortValue)
        if (this.strategyConfigInfo.strategyConfigList?.length > 0) {
          const directionalBargaining =
            this.strategyConfigInfo.strategyConfigList[0]?.directionalBargaining
          if (directionalBargaining !== undefined && directionalBargaining !== null) {
            const nonDirectionalSupNum = rfxStrategyDetailItems.find(
              (e) => e.strategyCode === 'nonDirectionalSupNum'
            )
            if (nonDirectionalSupNum) {
              const readonly = !!(Number(directionalBargaining) === 1)
              nonDirectionalSupNum.editEnable = readonly ? 0 : 1
            }
          }
        }
        this.rfxStrategyDetailItems = this.setAllowEdit(rfxStrategyDetailItems)
        this.cacheRfxStrategyDetailItems = utils.cloneDeep(this.rfxStrategyDetailItems)
      }
    },
    /**
     * 获取头部表单数据
     */
    async getHeaderFormData() {
      let result = {}
      const strategyConfigList = this.strategyConfigInfo.strategyConfigList
      if (strategyConfigList?.length > 0) {
        const row = strategyConfigList[0]
        let rf = this.$refs['headerForm0']
        rf = Array.isArray(rf) ? rf[0] : rf
        const newRow = await rf.parentGetFormData()
        result = {
          ...row,
          ...newRow
        }
      }
      // 字段过滤
      const headerFormData = this.headerForm
        .map((e) => e.strategyCode)
        .reduce((p, v) => {
          p[v] = result[v]
          return p
        }, {})

      return headerFormData
    },
    // 获取时间字段
    getTimeFields() {
      const strategyConfig = getStrategyConfig()
      return Object.keys(strategyConfig).filter(
        (e) => strategyConfig[e].type === 'datetime' && strategyConfig[e]['time-stamp']
      )
    },
    /**
     * 获取表单数据
     */
    async getFormsData() {
      const headerFormData = await this.getHeaderFormData().catch(() => {})
      const result = []
      const strategyConfigList = this.strategyConfigInfo.strategyConfigList
      for (let i = 0; i < strategyConfigList.length; i++) {
        const row = strategyConfigList[i]
        let rf = this.$refs['appForm' + i]
        rf = Array.isArray(rf) ? rf[0] : rf
        const newRow = await rf.parentGetFormData()
        const data = {
          ...row,
          ...headerFormData,
          ...newRow
        }
        this.getTimeFields().forEach((e) => {
          data[e] && (data[e] = +data[e])
        })
        if (data.roundNo === undefined || data.roundNo === null) {
          data.roundNo = 1
        }
        if (data.roundCount === undefined || data.roundCount === null) {
          data.roundCount = '1'
        }
        result.push(data)
      }
      return result
    },
    // 验证提交数据
    validateParams(strategyConfigList) {
      const rules = [
        {
          message: () => this.$t('非定向议价供应商数量不能为空'),
          handler: (row) => {
            return (
              this.hasForm(['directionalBargaining', 'nonDirectionalSupNum']) &&
              Number(row?.directionalBargaining) === 0 &&
              !row?.nonDirectionalSupNum
            )
          }
        },
        {
          message: () => this.$t('轮次开始时间不能为空'),
          handler: (row) => {
            return this.hasForm(['roundStartTime']) && !row?.roundStartTime
          }
        },
        {
          message: () => this.$t('商务投标开始时间不能大于商务开标时间'),
          handler: () => {
            return (
              this.hasForm(['tenderStartTime', 'openBidTime']) &&
              strategyConfigList?.[0]?.tenderStartTime &&
              strategyConfigList?.[0]?.openBidTime &&
              +strategyConfigList[0].tenderStartTime > +strategyConfigList[0].openBidTime
            )
          }
        },
        {
          message: () => this.$t('轮次开始时间不能小于商务投标开始时间'),
          handler: (row) => {
            return (
              this.hasForm(['roundStartTime', 'roundEndTime', 'tenderStartTime']) &&
              row?.roundStartTime &&
              row?.roundEndTime &&
              strategyConfigList[0].tenderStartTime &&
              +row.roundStartTime < +strategyConfigList[0].tenderStartTime
            )
          }
        },
        {
          message: () => this.$t('轮次结束时间不能为空'),
          handler: (row) => {
            return this.hasForm(['roundEndTime']) && !row?.roundEndTime
          }
        },
        {
          message: () => this.$t('轮次结束时间不能大于商务开标时间'),
          handler: (row) => {
            return (
              this.hasForm(['roundStartTime', 'roundEndTime', 'openBidTime']) &&
              row?.roundStartTime &&
              row?.roundEndTime &&
              strategyConfigList[0].openBidTime &&
              +row.roundEndTime > +strategyConfigList[0].openBidTime
            )
          }
        },
        {
          message: (row) => this.$t(`第${row.roundNo}轮，轮次开始不能大于结束时间`),
          handler: (row) => {
            return (
              this.hasForm(['roundStartTime', 'roundEndTime']) &&
              row?.roundStartTime &&
              row?.roundEndTime &&
              row?.roundNo &&
              +row.roundStartTime > +row.roundEndTime
            )
          }
        },
        // 应标截止时间 responseBidEndTime < 技术投标开始时间 tecBidStartTime
        {
          message: () => this.$t('应标截止时间不能大于技术投标开始时间'),
          handler: (row) => {
            return (
              this.hasForm(['responseBidEndTime', 'tecBidStartTime']) &&
              row.responseBidEndTime &&
              row.tecBidStartTime &&
              +row.responseBidEndTime > +row.tecBidStartTime
            )
          }
        },
        // 技术投标开始时间 tecBidStartTime < 技术开标时间 tecOpenBidTime
        {
          message: () => this.$t('技术投标开始时间不能大于技术开标时间'),
          handler: (row) => {
            return (
              this.hasForm(['tecBidStartTime', 'tecOpenBidTime']) &&
              row.tecBidStartTime &&
              row.tecOpenBidTime &&
              +row.tecBidStartTime > +row.tecOpenBidTime
            )
          }
        },
        // 技术开标时间 tecOpenBidTime < 技术评分截止时间 tecScoreEndTime
        {
          message: () => this.$t('技术开标时间不能大于技术评分截止时间'),
          handler: (row) => {
            return (
              this.hasForm(['tecOpenBidTime', 'tecScoreEndTime']) &&
              row.tecOpenBidTime &&
              row.tecScoreEndTime &&
              +row.tecOpenBidTime > +row.tecScoreEndTime
            )
          }
        },
        // 技术评分截止时间 tecScoreEndTime < 商务投标开始时间 tenderStartTime
        {
          message: () => this.$t('技术评分截止时间不能大于商务投标开始时间'),
          handler: (row) => {
            return (
              this.hasForm(['tecScoreEndTime', 'tenderStartTime']) &&
              row.tecScoreEndTime &&
              row.tenderStartTime &&
              +row.tecScoreEndTime > +row.tenderStartTime
            )
          }
        }
      ]
      if (Array.isArray(strategyConfigList)) {
        for (const row of strategyConfigList) {
          for (const rule of rules) {
            if (rule.handler(row)) {
              this.$toast({ type: 'warning', content: rule.message(row) })
              console.warn(row)
              return false
            }
          }
        }
      }
      return true
    },
    /**
     * 保存
     */
    async save() {
      if (this.isDisabled) {
        return
      }
      let strategyConfigList = await this.getFormsData().catch(() => {})
      if (!strategyConfigList) {
        this.$toast({
          content: this.$t('操作失败'),
          type: 'warning'
        })
        return
      }
      if (!this.validateParams(strategyConfigList)) {
        return
      }
      // 下面的判断是这样的场景导致的，先将是否需要技术标选择是，是否晋级会被带出（默认为是），再将是否需要技术标选择为否时是否晋级字段隐藏，但是值还是1
      if (
        strategyConfigList[0]?.needTecBid == '0' &&
        strategyConfigList[0]?.biddingPromotion == '1'
      ) {
        strategyConfigList[0].biddingPromotion = '0'
      }
      this.loading = true
      const saveRes = await this.$API.strategyConfig
        .save({
          ...this.strategyConfigInfo,
          strategyConfigList
        })
        .catch(() => {})
      this.loading = false
      if (saveRes) {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        // 根据rfxId查询策略信息
        await this.initStrategyConfigInfo()
        // 获取表单配置信息
        await this.initRfxStrategyDetailItemsRes()
        // 合并默认值
        this.mergeDefaultStrategyConfig()
        // 重新计算轮次信息
        this.resetRoundCount()
        // 重新设置oldFormData数据
        this.oldFormData = await this.getFormsData()
        this.$emit('reGetDetail')
      }
      return saveRes
    },
    //点击提交立项时，校验formList 与 getFormsData 是否一致
    formatDateFields(_list) {
      const result = []
      for (let i = 0; i < _list.length; i++) {
        const data = _list[i]
        this.getTimeFields().forEach((e) => {
          data[e] && (data[e] = +data[e])
        })
        result.push(data)
      }
      return result
    }
  }
}
</script>

<style scoped lang="scss">
.toolbar {
  padding: 20px;
  display: flex;
  .svg-option-item {
    &.disabled {
      i,
      span {
        color: var(--plugin-tb-tool-item-disable-color);
      }

      &:hover {
        cursor: not-allowed !important;
      }
    }

    &:hover {
      color: #707b8b;
      color: var(--plugin-tb-tool-item-hover-color);
    }
  }
}
.round-container {
  background: #fff;
  border: 1px solid #e8e8e8;

  .tag-current-round {
    background-color: #6386c1;
    border-radius: 10px;
    padding: 5px 20px;
    color: #fff;
    display: inline-block;
    margin-bottom: 20px;
  }

  /deep/ .strategy-form:not(:first-child) {
    border-top: solid 1px rgb(232, 232, 232);
  }

  /deep/ .e-input-group.e-error {
    border-bottom-color: rgba(0, 0, 0, 0.42);
    &::before,
    &::after {
      background: transparent !important;
    }
  }
}
</style>
