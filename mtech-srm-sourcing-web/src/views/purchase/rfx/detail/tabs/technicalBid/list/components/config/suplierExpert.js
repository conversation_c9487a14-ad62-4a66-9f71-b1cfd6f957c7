import { i18n } from '@/main.js'
const columnData = [
  {
    field: 'scoreDetailCode',
    headerText: i18n.t('评分项编码')
  },
  {
    field: 'scoreDetailName',
    headerText: i18n.t('评分项名称')
  },
  {
    field: 'scoreItem',
    headerText: i18n.t('评分细则')
  },
  {
    field: 'minScore',
    headerText: i18n.t('最低分')
  },
  {
    field: 'maxScore',
    headerText: i18n.t('最高分')
  },
  {
    field: 'detailWeight',
    headerText: i18n.t('权重')
  }
]
export const pageConfig = [
  {
    useToolTemplate: false,
    grid: { allowFiltering: true, lineIndex: true, columnData, dataSource: [] }
  }
]
