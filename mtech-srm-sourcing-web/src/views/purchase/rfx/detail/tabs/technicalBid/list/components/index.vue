<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content full-height">
      <div class="full-height">
        <div class="expert-input">
          <div class="expert_box">{{ $t('供应商名称') }}：{{ modalData.supplierName }}</div>
        </div>
        <mt-template-page ref="templateRef" :template-config="pageConfig" />
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { pageConfig } from './config/index.js'
import { cloneDeep } from 'lodash'
export default {
  data() {
    return {
      pageConfig: pageConfig,
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.formObject = { ...this.modalData.data }
    }
    this.getList()
  },
  methods: {
    getList() {
      let param = {
        bidType: 0,
        rfxCode: this.modalData.rfxCode,
        supplierCode: this.modalData.supplierCode,
        supplierId: this.modalData.supplierId,
        requestParams: {
          page: { current: 1, size: 10000 }
        }
      }
      let expertCols = []
      let _dataSource = []
      this.$API.rfxExperttechnicalBid.getSaveRfxTexpert(param).then((res) => {
        if (
          res.code === 200 &&
          res.data?.bidScoreSupplierDetailBySupplierDTOIPage?.records.length > 0
        ) {
          let records = res.data.bidScoreSupplierDetailBySupplierDTOIPage.records
          _dataSource = cloneDeep(records)
          _dataSource.forEach((item, index) => {
            if (index === 0) {
              if (Array.isArray(item.bidExpertScoreBySupplierDetailDTOS)) {
                for (let expert of item.bidExpertScoreBySupplierDetailDTOS) {
                  expertCols.push({
                    field: expert.expertCode,
                    headerText: this.$t('专家') + expert.expertName + this.$t('评分'),
                    valueConverter: {
                      type: 'placeholder',
                      placeholder: '--'
                    }
                  })
                }
              }
            }
            if (Array.isArray(item.bidExpertScoreBySupplierDetailDTOS)) {
              for (let expert of item.bidExpertScoreBySupplierDetailDTOS) {
                item[expert.expertCode] = expert.score
              }
            }
          })
        }
        let columnData = this.pageConfig[0].grid.columnData.slice(0, 5)
        this.$set(this.pageConfig[0].grid, 'columnData', columnData.concat(expertCols))
        this.$set(this.pageConfig[0].grid, 'dataSource', _dataSource)
      })
    },
    confirm() {
      this.$emit('confirm-function')
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-box {
  display: flex;
  align-content: center;
  justify-content: space-between;
  .expert_box {
    display: flex;
    align-content: center;
    justify-content: center;
  }
}
.box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.expert-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .expert_box {
    display: flex;
    align-content: center;
    justify-content: center;
  }
}
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/ .div-auth {
    background: #fafafa;
    height: 35px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    color: #0f0f0f;
    font-size: 12px;
    cursor: pointer;
    border-radius: 5px;
  }
}
.op-item {
  cursor: pointer;
  color: #4f5b6d;
  align-items: center;
  margin-right: 20px;
  align-items: center;
}
</style>
