import { i18n } from '@/main.js'
const columnData = [
  {
    field: 'scoreDetailCode',
    headerText: i18n.t('评分项编码')
  },
  {
    field: 'scoreDetailName',
    headerText: i18n.t('评分项名称')
  },
  {
    field: 'scoreItem',
    headerText: i18n.t('评分细则')
  },
  {
    field: 'minScore',
    headerText: i18n.t('最低分')
  },
  {
    field: 'maxScore',
    headerText: i18n.t('最高分')
  },
  {
    field: 'detailWeight',
    headerText: i18n.t('权重')
  }
]
export const pageConfig = [
  {
    useToolTemplate: false,
    grid: { allowFiltering: true, lineIndex: true, columnData, dataSource: [] }
  }
]

const scoreFileCols = [
  {
    field: 'fileName',
    headerText: i18n.t('文件名称'),
    cssClass: 'field-content'
  },
  {
    field: 'opration',
    headerText: i18n.t('操作'),
    cellTools: [
      {
        id: 'download',
        title: i18n.t('下载')
      }
    ]
  }
]
export const scoreFileConfig = (url, modalData) => [
  {
    useToolTemplate: false,
    grid: {
      lineIndex: true,
      columnData: scoreFileCols,
      height: 'auto',
      allowPaging: false,
      dataSource: [],
      asyncConfig: {
        url: url,
        recordsPosition: 'data',
        params: {
          rfxCode: modalData.rfxCode,
          supplierCode: modalData.supplierCode,
          supplierId: modalData.supplierId
        }
      }
    }
  }
]
