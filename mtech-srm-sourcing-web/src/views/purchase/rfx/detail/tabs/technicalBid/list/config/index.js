import { i18n, permission } from '@/main.js'
const promotionTemplate = (args) => {
  return {
    template: `<div id="biddingPromotion">
      <span v-if="transferStatus != 40">{{ promotion }}</span>
      <div v-else>
        <p v-if="!isEdit" @click="startEdit">{{ promotion }}</p>
        <mt-select v-else v-model="data.biddingPromotion" :data-source="promotionSource" @change="change" />
      </div>
    </div>`,
    data() {
      return {
        data: {},
        transferStatus: args.transferStatus,
        promotionMap: [
          { text: i18n.t('否'), value: 0 },
          { text: i18n.t('是'), value: 1 },
          { text: i18n.t('审批中'), value: 2 }
        ],
        promotionSource: [
          { text: i18n.t('否'), value: 0 },
          { text: i18n.t('是'), value: 1 }
        ],
        isEdit: false
      }
    },
    computed: {
      promotion() {
        return this.promotionMap[this.data.biddingPromotion || 0].text
      }
    },
    mounted() {
      args.self.promotionArr[this.data.index] = this.data.biddingPromotion
    },
    methods: {
      change(e) {
        args.self.promotionArr[this.data.index] = e.value
        this.isEdit = false
        const params = {
          bidType: 0,
          biddingPromotion: e.value,
          rfxCode: args.rfxCode,
          supplierCode: this.data.supplierCode,
          supplierId: this.data.supplierId
        }
        args.self.$API.rfxExperttechnicalBid
          .updatePromotion(params)
          .then((res) => {
            console.log(res)
          })
          .catch(() => {
            this.data.biddingPromotion = e.value ? 0 : 1
          })
      },
      startEdit() {
        this.isEdit = true
      }
    }
  }
}
const columnData = (args) => [
  // {
  //   width: '60',
  //   type: 'checkbox'
  // },
  {
    // 招标单是否晋级(0否/1是)
    field: 'biddingPromotion',
    headerText: i18n.t('晋级'),
    // valueConverter: {
    //   type: 'map',
    //   map: { 0: i18n.t('否'), 1: i18n.t('是'), 2: i18n.t('审批中') }
    // },
    template() {
      return {
        template: promotionTemplate(args)
      }
    },
    visible: args.rfxGeneralType !== 1 ? true : false
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
    // valueConverter: {
    //   type: "map",
    //   map: { 0: i18n.t("技术组"), 1:  i18n.t("商务组") },
    // },
  },
  {
    field: 'biddingCode',
    headerText: i18n.t('投标编号')
  },
  {
    field: 'biddingStatus',
    headerText: i18n.t('投标状态'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('未参与'), 1: i18n.t('已参与'), 3: i18n.t('已拒绝') }
    }
  },
  {
    field: 'score',
    headerText: i18n.t('技术总得分')
  },
  {
    field: 'technicalAttachment',
    headerText: i18n.t('技术附件'),
    cssClass: 'field-content',
    valueConverter: {
      type: 'placeholder',
      placeholder: i18n.t('查看')
    }
  },
  {
    field: 'viewScore',
    headerText: i18n.t('查看评分'),
    cssClass: 'field-content',
    valueConverter: {
      type: 'placeholder',
      placeholder: i18n.t('查看')
    }
  }
]
const toolbar = [{ id: 'Submit', icon: 'icon_solid_Submit', title: i18n.t('提交') }]
export const columnData1 = [
  {
    width: '60',
    type: 'checkbox'
  },
  {
    field: 'expertName',
    headerText: i18n.t('评分人')
  },
  {
    field: 'mobilePhone',
    headerText: i18n.t('电话')
  },
  {
    field: 'email',
    headerText: i18n.t('邮箱')
  },
  {
    field: 'scoreProcess',
    headerText: i18n.t('评分进度')
  }
]
export const scoreDetailCol = [
  {
    field: 'viewScoreDetail',
    headerText: i18n.t('查看评分明细'),
    cssClass: 'field-content',
    valueConverter: {
      type: 'placeholder',
      placeholder: i18n.t('明细')
    }
  }
]
export const pageConfigQuote = (args) => [
  {
    // title: i18n.t("报价明细"),
    useToolTemplate: false,
    toolbar: args.rfxGeneralType !== 1 ? toolbar : [], // 非采有提交晋级
    gridId: permission.gridId['purchase'][args.source]['detail']['tabs']['techBid']['list'],
    grid: {
      allowFiltering: true,
      allowSorting: false,
      lineIndex: true,
      columnData: columnData(args),
      dataSource: [],
      asyncConfig: {
        url: args.url,
        queryBuilderWrap: 'requestParams',
        params: {
          bidType: 0,
          rfxCode: args.rfxCode
        },
        serializeList: (list) => {
          list.forEach((el, index) => {
            el.index = index
          })
          return list
        }
      }
    }
  }
]
export const columnDataSupplier = [
  {
    width: '60',
    type: 'checkbox'
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'biddingCode',
    headerText: i18n.t('投标编号')
  },
  {
    field: 'totalScore',
    headerText: i18n.t('技术总得分')
  },
  {
    field: 'viewLeaderEvaluation',
    headerText: i18n.t('专家组长综合评价'),
    cssClass: 'field-content',
    valueConverter: {
      type: 'placeholder',
      placeholder: i18n.t('查看')
    }
  },
  {
    field: 'expertLevel',
    headerText: i18n.t('评分'),
    cssClass: 'field-content',
    valueConverter: {
      type: 'placeholder',
      placeholder: i18n.t('查看')
    }
  }
]
