<template>
  <!-- @beforeClose="cancel" -->
  <mt-dialog ref="dialog" css-class="create-proj-dialog" :header="header" @beforeClose="cancel">
    <div class="dialog-content mt-flex-direction-column">
      <div class="expert-input">
        <div class="expert_box">
          <mt-select
            v-if="currentSupplierCode"
            width="200"
            float-label-type="Never"
            v-model="currentSupplierCode"
            :data-source="modalData.supplierList"
            @change="supplierChange"
            :fields="{
              text: 'supplierName',
              value: 'supplierCode'
            }"
            :placeholder="$t('请选择供应商')"
            :show-clear-button="true"
          ></mt-select>
          <mt-select
            v-else
            width="200"
            float-label-type="Never"
            v-model="currentSupplierId"
            :data-source="modalData.supplierList"
            @change="supplierChange"
            :fields="{
              text: 'supplierName',
              value: 'supplierId'
            }"
            :placeholder="$t('请选择供应商')"
            :show-clear-button="true"
          ></mt-select>
        </div>
        <div class="expert_box">
          <p>{{ $t('得分') }}：</p>
          <mt-input
            width="150"
            float-label-type="Never"
            disabled
            v-model="currentTotalScore"
          ></mt-input>
        </div>
      </div>
      <div class="dialog-box">
        <div class="expert_box" style="width: 100%">
          <div style="width: 105px">
            <p>{{ $t('综合评价') }}：</p>
          </div>
          <div style="width: 100%">
            <mt-input
              :multiline="true"
              :rows="3"
              style="width: 100%"
              maxlength="1000"
              v-model="remark"
              float-label-type="Never"
              :disabled="true"
              :placeholder="$t('请输入1000字以内')"
            ></mt-input>
          </div>
        </div>
      </div>
      <div v-if="modalData.scoreProcess === $t('已提交')" class="score-file-container">
        <p>{{ $t('专家评分附件') }}</p>
        <mt-template-page
          :template-config="scoreFileConfig"
          @handleClickCellTool="handleScoreCellTool"
        />
      </div>
      <div class="score-detail-container">
        <p>{{ $t('专家评分明细') }}</p>
        <mt-template-page ref="templateRef" :template-config="pageConfig" />
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { pageConfig, scoreFileConfig } from './config/expertPricing'
import { download } from '@/utils/utils'

export default {
  data() {
    return {
      remark: '',
      currentRfxCode: '',
      currentExpertCode: '',
      currentSupplierCode: '',
      currentSupplierId: '',
      currentTotalScore: '',
      pageConfig: pageConfig,
      scoreFileConfig: {}
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.currentRfxCode = this.modalData.supplierList[0].rfxCode
    this.currentExpertCode = this.modalData.supplierList[0].expertCode
    this.currentSupplierCode = this.modalData.supplierList[0].supplierCode
    this.currentSupplierId = this.modalData.supplierList[0].supplierId
    this.modalData.supplierId = this.modalData.supplierList[0].supplierId
    this.currentTotalScore = this.modalData.supplierList[0].totalScore
    // this.getList()
    // this.scoreFileConfig = scoreFileConfig(this.$API.rfxExpert.getScoreFileListUrl, this.modalData)
  },
  methods: {
    supplierChange(value) {
      this.currentRfxCode = value.itemData.rfxCode
      this.currentExpertCode = value.itemData.expertCode
      this.currentSupplierCode = value.itemData.supplierCode
      this.currentSupplierId = value.itemData.supplierId
      this.currentTotalScore = value.itemData.totalScore
      this.modalData.supplierCode = value.itemData.supplierCode
      this.modalData.supplierId = value.itemData.supplierId
      this.$nextTick(() => {
        this.getList()
        this.scoreFileConfig = scoreFileConfig(
          this.$API.rfxExpert.getScoreFileListUrl,
          this.modalData
        )
      })
    },
    getList() {
      this.$set(this.pageConfig[0].grid, 'asyncConfig', {
        url: this.$API.rfxExperttechnicalBid.getComputedStyle,
        queryBuilderWrap: 'requestParams',
        recordsPosition: 'data.bidScoreSupplierDetailDTOIPage.records',
        afterAsyncData: (res) => {
          this.remark = res.data?.remark
        },
        params: {
          bidType: 0,
          rfxCode: this.currentRfxCode,
          supplierCode: this.currentSupplierCode,
          supplierId: this.currentSupplierId,
          expertCode: this.currentExpertCode
        }
      })
    },
    handleScoreCellTool(e) {
      if (e.tool.id === 'download') {
        this.$API.fileService.downloadPrivateFile({ id: e.data.sysFileId }).then((res) => {
          download({
            fileName: e.data.fileName,
            blob: new Blob([res.data])
          })
        })
      }
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-box {
  display: flex;
  align-content: center;
  justify-content: space-between;
  .expert_box {
    display: flex;
    align-content: center;
    justify-content: center;
  }
}
.box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.expert-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .expert_box {
    display: flex;
    align-content: center;
    justify-content: center;
  }
}
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  height: 100%;
  /deep/ .div-auth {
    background: #fafafa;
    height: 35px;
    padding: 0 10px;
    color: #0f0f0f;
    font-size: 12px;
    cursor: pointer;
    border-radius: 5px;
  }
}
.op-item {
  cursor: pointer;
  color: #4f5b6d;
  align-items: center;
  margin-right: 20px;
  align-items: center;
}
.score-file-container,
.score-detail-container {
  margin-top: 40px;
  p {
    margin-bottom: 8px;
  }
}
</style>
