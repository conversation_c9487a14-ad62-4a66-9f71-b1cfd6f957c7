<template>
  <div class="mt-flex-direction-column" style="height: 100%">
    <div class="top-info">
      <mt-tabs :e-tab="false" :data-source="tabSource" @handleSelectTab="handleSelectTab"></mt-tabs>
      <div class="expert_box" v-show="tabIndex == 1">
        <div class="dimensionButton" v-show="dimension == 'expertDimension'">
          <img src="@/assets/iconSvg/expertDimension.svg" /><span
            style="color: #00469c; font-weight: bold"
            >{{ $t('专家维度') }}</span
          >
        </div>
        <div
          class="dimensionButton"
          v-show="dimension != 'expertDimension'"
          @click="switchDimension('expertDimension')"
        >
          <img src="@/assets/iconSvg/expertDimensionGrey.svg" /><span>{{ $t('专家维度') }}</span>
        </div>
        <div class="dimensionButton" v-show="dimension == 'supplierDimension'">
          <img src="@/assets/iconSvg/supplierDimension.svg" /><span
            style="color: #00469c; font-weight: bold"
            >{{ $t('供应商维度') }}</span
          >
        </div>
        <div
          class="dimensionButton"
          @click="switchDimension('supplierDimension')"
          v-show="dimension != 'supplierDimension'"
        >
          <img src="@/assets/iconSvg/supplierDimensionGrey.svg" /><span>{{
            $t('供应商维度')
          }}</span>
        </div>
      </div>
    </div>
    <!-- <div class="form-design" slot="slot-filter">
        <mt-button
          @click="bidOpening"
          style="margin-right: 15px"
          :disabled="isDisabled"
          >{{ $t("开标") }}</mt-button
        >
        <mt-button @click="sendExpertRateScore" :disabled="isDisabled">{{
          $t("下发专家评分")
        }}</mt-button>
        <mt-button
          @click="endExpertRate"
          style="margin-left: 15px"
          :disabled="isDisabled"
          >{{ $t("结束投标") }}</mt-button
        >
      </div> -->
    <div style="flex: 1">
      <mt-template-page
        ref="template-0"
        :template-config="pageConfigQuote"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTitle="handleClickCellTitle"
        v-if="tabIndex == 0"
      >
      </mt-template-page>
      <mt-template-page
        ref="template-1"
        :template-config="pageConfigExpertScore"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTitle="handleClickCellTitle"
        v-if="
          tabIndex == 1 &&
          dimension == 'expertDimension' &&
          pageConfigExpertScore[0].grid.columnData.length > 0
        "
      >
      </mt-template-page>
      <mt-template-page
        ref="template-1"
        :template-config="pageConfigExpertScoreSupplier"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTitle="handleClickCellTitle"
        v-if="
          tabIndex == 1 &&
          dimension == 'supplierDimension' &&
          pageConfigExpertScoreSupplier[0].grid.columnData.length > 0
        "
      >
      </mt-template-page>
    </div>
  </div>
</template>

<script>
// import TemplatePage from '@/components/template-page'
import { pageConfigQuote, columnData1, scoreDetailCol, columnDataSupplier } from './config'
import { utils } from '@mtech-common/utils'
import { cloneDeep } from 'lodash'
export default {
  name: 'TechnicalBid',
  // components: {
  //   TemplatePage
  // },
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dimension: 'expertDimension',
      supplierList: [],
      tabIndex: 0,
      tabSource: [{ title: this.$t('报价明细') }, { title: this.$t('专家评分') }],
      pageConfigQuote: pageConfigQuote({
        source: this.$route?.query?.source,
        url: this.$API.rfxExperttechnicalBid.getRfxExperttechnicalBid,
        rfxCode: this.detailInfo.rfxCode,
        rfxGeneralType: this.detailInfo.rfxGeneralType,
        transferStatus: this.detailInfo.transferStatus,
        status: this.detailInfo.status,
        self: this
      }),
      pageConfigExpertScore: [
        {
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              // [
              //   {
              //     id: "Submit",
              //     icon: "icon_solid_Submit",
              //     title: this.$t("提交"),
              //   },
              // ],
              // ["Filter", "Refresh", "Setting"],
            ]
          },
          gridId:
            this.$permission.gridId['purchase'][this.$route?.query?.source]['detail']['tabs'][
              'techBid'
            ]['expert'],
          grid: {
            allowFiltering: true,
            lineIndex: true,
            columnData: [], // 还要加上自定义字段
            dataSource: []
          }
        }
      ],
      pageConfigExpertScoreSupplier: [
        {
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              // [
              //   {
              //     id: "Submit",
              //     icon: "icon_solid_Submit",
              //     title: this.$t("提交"),
              //   },
              // ],
              // ["Filter", "Refresh", "Setting"],
            ]
          },
          grid: {
            allowFiltering: true,
            lineIndex: true,
            columnData: columnDataSupplier,
            dataSource: []
          }
        }
      ],
      promotionArr: []
    }
  },
  computed: {
    isDisabled() {
      // 已完成和关闭要禁止编辑
      return this.detailInfo?.status === 8 || this.detailInfo?.status === -1
    }
  },
  mounted() {
    this.getSupplierDimension()
  },
  methods: {
    switchDimension(type) {
      this.dimension = type
      if (type == 'expertDimension') {
        this.handleSelectTab(1)
      }
    },
    getSupplierDimension() {
      this.$set(this.pageConfigExpertScoreSupplier[0].grid, 'asyncConfig', {
        url: this.$API.rfxExperttechnicalBid.getExpertScoreList,
        queryBuilderWrap: 'requestParams',
        afterAsyncData: (res) => {
          this.supplierList = res.data.records
        },
        params: {
          rfxCode: this.detailInfo.rfxCode,
          bidType: 0
        }
      })
    },
    handleClickToolBar(e) {
      let _selectGridRecords = e.grid.getCurrentViewRecords()
      if (_selectGridRecords.length < 1) {
        return
      }

      if (e.toolbar.id == 'Submit') {
        this.handleAddExpertSumbit()
      }
    },
    handleAddExpertSumbit() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认执行提交操作？`)
        },
        success: () => {
          // let idList = [];
          // _selectGridRecords.map((item) => {
          //   idList.push(item.id);
          // });
          let _ref = this.tabIndex == 0 ? 'template-0' : 'template-1'
          let _currentTabRef = this.$refs[_ref].getCurrentTabRef()
          let _selectRecords = _currentTabRef?.gridRef.getMtechGridRecords() ?? []
          let supplierCodeList = []
          // 不确定tabIndex1逻辑，兼容处理
          if ((_selectRecords.length && this.tabIndex == 1) || this.tabIndex == 0) {
            _selectRecords.map((e) => {
              if (e.supplierCode) {
                supplierCodeList.push(
                  // supplierId: e.id,
                  e.supplierCode
                  // supplierName: e.supplierName,
                  // stageName: e.stageName,
                  // supplierTenantId: e.supplierTenantId,
                  // stageId: e.stageId,
                )
              }
            })
            // this.queryConfigId = this.modalData.rfxId;
            // const rfxId = this.queryConfigId;
            let params = {
              bidType: 0,
              // rfxCode: this.$route.query.rfxId,
              rfxCode: this.detailInfo.rfxCode
              // supplierCodeList
            }
            // if (this.tabIndex == 0) {
            //   params.biddingPromotionRequestList = []
            //   _selectRecords.forEach((item) => {
            //     params.biddingPromotionRequestList.push({
            //       biddingPromotion: this.promotionArr[item.index],
            //       supplierCode: item.supplierCode
            //     })
            //   })
            // }
            this.$API.rfxExperttechnicalBid.getRfxSubmitExpert(params).then((res) => {
              if (res.code == 200) {
                this.$refs[_ref].refreshCurrentGridData()
                this.$toast({
                  content: res.msg,
                  type: 'success'
                })
              }
            })
          }
        }
      })
    },
    sendExpertRateScore() {
      // if (this.detailInfo.transferStatus != 37) {
      //   this.$toast({ content: this.$t("该单据暂未开标"), type: "warning" });
      //   return;
      // }
      let params = {
        rfxCode: this.detailInfo.rfxCode
      }
      this.$API.rfxExpert.getExpertIssue(params).then(() => {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        return
      })
    },
    endExpertRate() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t("确认执行'结束投标'操作？")
        },
        success: () => {
          let params = {
            rfxId: this.$route.query.rfxId
          }
          this.$API.rfxTask.closeTecBid(params).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
            }
          })
        }
      })
    },
    bidOpening() {
      let params = {
        openBiddingType: 0,
        rfxId: this.$route.query.rfxId
      }
      this.$store.commit('startLoading')
      this.$API.rfxExpert
        .getExpertAdd(params)
        .then(() => {
          this.$store.commit('endLoading')
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$emit('reGetDetail')
          return
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    // handleClickCellTool(eventObject) {
    //   this.$dialog({
    //     data: {},
    //     modal: () => import("./components/ratingDetailDialog.vue"),
    //   });
    // },

    handleClickCellTitle(e) {
      if (e.field == 'viewScore') {
        // 打分明细弹框
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "components/market/companyAuth" */ './components/index.vue'
            ),
          data: {
            title: this.$t('打分明细'),
            supplierCode: e.data.supplierCode,
            supplierId: e.data.supplierId,
            supplierName: e.data.supplierName,
            rfxCode: this.detailInfo.rfxCode,
            scoreProcess: e.data.scoreProcess
          },
          success: () => {
            // this.$refs.templateRef.refreshCurrentGridData();
          }
        })
      } else if (e.field == 'viewScoreDetail') {
        // 打分明细弹框
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "components/market/companyAuth" */ './components/expertPricing.vue'
            ),
          data: {
            title: this.$t('评分'),
            supplierList: e.data.bidScoreSupplierDTOList,
            rfxCode: this.detailInfo.rfxCode,
            scoreProcess: e.data.scoreProcess
          },
          success: () => {
            // this.$refs.templateRef.refreshCurrentGridData();
          }
        })
      } else if (e.field == 'technicalAttachment') {
        this.$API.rfxExpert
          .getSkillFileList({
            bidType: 0,
            supplierCode: e.data.supplierCode,
            supplierId: e.data.supplierId,
            rfxCode: this.detailInfo.rfxCode
          })
          .then((res) => {
            this.$dialog({
              modal: () =>
                import(
                  /* webpackChunkName: "components/Upload/uploaderDialog" */ 'COMPONENTS/Upload/uploaderDialog.vue'
                ),
              data: {
                fileData: utils.cloneDeep(res.data),
                isView: true, // 是否为预览
                required: false, // 是否必须
                needButton: false,
                title: this.$t('查看附件')
              }
            })
          })
      } else if (e.field == 'expertLevel') {
        // 打分明细弹框
        let supplierArr = []
        for (let item of this.supplierList) {
          supplierArr.push({
            supplierName: item.supplierName,
            supplierCode: item.supplierCode,
            supplierId: item.supplierId,
            totalScore: item.totalScore
          })
        }
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "components/market/companyAuth" */ './components/suplierExpert.vue'
            ),
          data: {
            title: this.$t('评分明细'),
            rfxCode: this.detailInfo.rfxCode,
            dataInfo: e.data,
            supplierArr: supplierArr
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      } else if (e.field == 'viewLeaderEvaluation') {
        this.$dialog({
          modal: () => import('./components/comprehensiveAssessment.vue'),
          data: {
            title: this.$t('综合评价'),
            leaderEvaluation: e.data.leaderEvaluation,
            needButton: false
          },
          success: () => {}
        })
      }
    },
    // tab切换
    handleSelectTab(e) {
      if (e === 1) {
        let param = {
          bidType: 0,
          rfxCode: this.detailInfo.rfxCode,
          requestParams: { page: { current: 1, size: 1000 } }
        }
        let supplierCols = []
        let _dataSource = []
        this.$API.rfxExperttechnicalBid.getRfxExpert(param).then((res) => {
          if (res.code === 200 && res.data?.records.length > 0) {
            _dataSource = cloneDeep(res.data.records)

            if (res.data.records[0]?.bidScoreSupplierDTOList.length > 0) {
              res.data.records[0]?.bidScoreSupplierDTOList.forEach((e, index) => {
                let _field = `totalScore${index}`
                supplierCols.push({
                  field: _field,
                  headerText: `${e.supplierName}得分`
                })
              })
              _dataSource.forEach((element) => {
                supplierCols.forEach((x, i) => {
                  element[x.field] = element.bidScoreSupplierDTOList[i].totalScore
                })
              })
            }

            // this.pageConfig[1].grid.columnData = columnData1.concat(
            //   supplierCols,
            //   scoreDetailCol
            // );
          }
          this.$set(
            this.pageConfigExpertScore[0].grid,
            'columnData',
            columnData1.concat(supplierCols, scoreDetailCol)
          )
          this.$set(this.pageConfigExpertScore[0].grid, 'dataSource', _dataSource)
        })
      }
      this.tabIndex = e
    }
  }
}
</script>

<style lang="scss" scoped>
.expert_box {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  padding-right: 20px;
  .dimensionButton {
    height: 28px;
    line-height: 24px;
    width: 100px;
    border: 1px solid #e8e8e8;
    border-radius: 4px 0 0 4px;
    text-align: center;
    cursor: pointer;
    img {
      vertical-align: middle;
    }
    span {
      margin-left: 5px;
      font-size: 12px;
    }
  }
}
.top-info {
  width: 100%;
  border-radius: 0 8px 0 0;
  display: flex;
  align-items: center;

  /deep/ .mt-tabs-container {
    background: #fff;
    .tabs-arrow {
      display: none;
    }
    .tab-wrap {
      padding: 0;
      height: 50px;
      .tab-item {
        padding: 6px 10px;
        span {
          line-height: 1;
        }
      }
    }
  }
}
</style>
