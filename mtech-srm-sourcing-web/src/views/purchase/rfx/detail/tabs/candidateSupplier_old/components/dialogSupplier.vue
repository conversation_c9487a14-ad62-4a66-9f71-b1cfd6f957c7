// 选择货源
<template>
  <mt-dialog
    ref="dialog"
    css-class="choose-supplier-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-template-page ref="templateRef" :template-config="pageConfig" />
    </div>
  </mt-dialog>
</template>

<script>
import { columnChooseSupplier } from '../config'
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          grid: {
            allowFiltering: true,
            columnData: columnChooseSupplier,
            dataSource: []
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        width: '900px',
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    confirm() {
      this.$emit('confirm-function')
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.choose-supplier-dialog {
  .dialog-content {
    padding-top: 18px;
    font-size: 16px;
  }

  .common-template-page {
    background: #fff;

    .page-grid-container {
      padding: 0 !important;
      box-shadow: unset !important;
    }
  }
}
</style>
