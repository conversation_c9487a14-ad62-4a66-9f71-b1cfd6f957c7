import { i18n } from '@/main.js'
// 选择货源
export const columnChooseSource = [
  {
    type: 'checkbox',
    width: '50'
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商代码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'approveStatus',
    headerText: i18n.t('评审状态'),
    valueConverter: {
      type: 'map',
      map: [
        {
          status: -3,
          label: i18n.t('取消审批'),
          cssClass: ['status-label', 'status-0']
        },
        {
          status: -2,
          label: i18n.t('驳回'),
          cssClass: ['status-label', 'status-0']
        },
        {
          status: -1,
          label: i18n.t('关闭'),
          cssClass: ['status-label', 'status-0']
        },
        {
          status: 0,
          label: i18n.t('草稿'),
          cssClass: ['status-label', 'status-0']
        },
        {
          status: 1,
          label: i18n.t('待审批'),
          cssClass: ['status-label', 'status-1']
        },
        {
          status: 2,
          label: i18n.t('审批通过'),
          cssClass: ['status-label', 'status-1']
        },
        {
          status: 3,
          label: i18n.t('控制'),
          cssClass: ['status-label', 'status-1']
        }
      ],
      fields: { text: 'label', value: 'status' }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂')
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编号')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'remark',
    headerText: i18n.t('描述')
  }
]

// 选择供应商
export const columnChooseSupplier = [
  {
    type: 'checkbox',
    width: '50'
  },
  {
    field: 'field1',
    headerText: i18n.t('供应商编号')
  },
  {
    field: 'field2',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'field3',
    headerText: i18n.t('阶段')
  },
  {
    field: 'field4',
    headerText: i18n.t('评审状态')
  },
  {
    field: 'field5',
    headerText: i18n.t('供应商名称')
  }
]
//货源清单
export const sourceListColumnData = [
  {
    type: 'checkbox',
    width: '50',
    showInColumnChooser: false
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商代码'),
    cellTools: [{ id: 'delete', icon: 'icon_solid_Delete1', title: i18n.t('删除') }]
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'enabled', // 0否1是
    headerText: i18n.t('启用/禁用状态'),
    valueConverter: {
      type: 'map',
      map: [
        {
          status: 0,
          label: i18n.t('禁用'),
          cssClass: ['status-label', 'status-0']
        },
        {
          status: 1,
          label: i18n.t('启用'),
          cssClass: ['status-label', 'status-1']
        }
      ],
      fields: { text: 'label', value: 'status' }
    },
    queryType: 'number',
    queryTemplate: {
      type: 'select',
      options: {
        ds: [
          { label: i18n.t('禁用'), value: 0 },
          { label: i18n.t('启用'), value: 1 }
        ]
      }
    }
  },
  {
    field: 'approveStatus', // -3:取消审批 -2:驳回 -1关闭 0:草稿 1:待审批 2:审批通过 3:控制
    headerText: i18n.t('评审状态'),
    valueConverter: {
      type: 'map',
      map: [
        {
          status: -3,
          label: i18n.t('取消审批'),
          cssClass: ['status-label', 'status-0']
        },
        {
          status: -2,
          label: i18n.t('驳回'),
          cssClass: ['status-label', 'status-0']
        },
        {
          status: -1,
          label: i18n.t('关闭'),
          cssClass: ['status-label', 'status-0']
        },
        {
          status: 0,
          label: i18n.t('草稿'),
          cssClass: ['status-label', 'status-0']
        },
        {
          status: 1,
          label: i18n.t('待审批'),
          cssClass: ['status-label', 'status-1']
        },
        {
          status: 2,
          label: i18n.t('审批通过'),
          cssClass: ['status-label', 'status-1']
        },
        {
          status: 3,
          label: i18n.t('控制'),
          cssClass: ['status-label', 'status-1']
        }
      ],
      fields: { text: 'label', value: 'status' }
    },
    queryType: 'number',
    queryTemplate: {
      type: 'select',
      options: {
        ds: [
          { label: i18n.t('取消审批'), value: -3 },
          { label: i18n.t('驳回'), value: -2 },
          { label: i18n.t('关闭'), value: -1 },
          { label: i18n.t('草稿'), value: 0 },
          { label: i18n.t('待审批'), value: 1 },
          { label: i18n.t('审批通过'), value: 2 },
          { label: i18n.t('控制'), value: 3 }
        ]
      }
    }
  },
  {
    field: 'stageName',
    headerText: i18n.t('供应商阶段')
  },
  {
    field: 'itemName',
    headerText: i18n.t('品类')
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂/地点编号')
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂/地点名称')
  }
]

// 供应商清单
export const supplierListColumnData = [
  {
    type: 'checkbox',
    width: '50',
    showInColumnChooser: false
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商代码'),
    cellTools: [{ id: 'delete', icon: 'icon_solid_Delete1', title: i18n.t('删除') }]
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'enabled', // 0否1是
    headerText: i18n.t('启用/禁用状态'),
    valueConverter: {
      type: 'map',
      map: [
        {
          status: 0,
          label: i18n.t('禁用'),
          cssClass: ['status-label', 'status-0']
        },
        {
          status: 1,
          label: i18n.t('启用'),
          cssClass: ['status-label', 'status-1']
        }
      ],
      fields: { text: 'label', value: 'status' }
    },
    queryType: 'number',
    queryTemplate: {
      type: 'select',
      options: {
        ds: [
          { label: i18n.t('禁用'), value: 0 },
          { label: i18n.t('启用'), value: 1 }
        ]
      }
    }
  },
  {
    field: 'approveStatus', // -3:取消审批 -2:驳回 -1关闭 0:草稿 1:待审批 2:审批通过 3:控制
    headerText: i18n.t('评审状态'),
    valueConverter: {
      type: 'map',
      map: [
        {
          status: -3,
          label: i18n.t('取消审批'),
          cssClass: ['status-label', 'status-0']
        },
        {
          status: -2,
          label: i18n.t('驳回'),
          cssClass: ['status-label', 'status-0']
        },
        {
          status: -1,
          label: i18n.t('关闭'),
          cssClass: ['status-label', 'status-0']
        },
        {
          status: 0,
          label: i18n.t('草稿'),
          cssClass: ['status-label', 'status-0']
        },
        {
          status: 1,
          label: i18n.t('待审批'),
          cssClass: ['status-label', 'status-1']
        },
        {
          status: 2,
          label: i18n.t('审批通过'),
          cssClass: ['status-label', 'status-1']
        },
        {
          status: 3,
          label: i18n.t('控制'),
          cssClass: ['status-label', 'status-1']
        }
      ],
      fields: { text: 'label', value: 'status' }
    },
    queryType: 'number',
    queryTemplate: {
      type: 'select',
      options: {
        ds: [
          { label: i18n.t('取消审批'), value: -3 },
          { label: i18n.t('驳回'), value: -2 },
          { label: i18n.t('关闭'), value: -1 },
          { label: i18n.t('草稿'), value: 0 },
          { label: i18n.t('待审批'), value: 1 },
          { label: i18n.t('审批通过'), value: 2 },
          { label: i18n.t('控制'), value: 3 }
        ]
      }
    }
  },
  {
    field: 'joinStatus',
    headerText: i18n.t('参与状态'),
    valueConverter: {
      type: 'map',
      map: [
        {
          status: 0,
          label: i18n.t('未参与'),
          cssClass: ['status-label', 'status-0']
        },
        {
          status: 1,
          label: i18n.t('参与'),
          cssClass: ['status-label', 'status-1']
        }
      ],
      fields: { text: 'label', value: 'status' }
    }
  },
  {
    field: 'deposit',
    headerText: i18n.t('保证金缴纳'),
    valueConverter: {
      type: 'map',
      map: [
        {
          status: 0,
          label: i18n.t('未缴纳'),
          cssClass: ['status-label', 'status-0']
        },
        {
          status: 1,
          label: i18n.t('已缴纳'),
          cssClass: ['status-label', 'status-1']
        }
      ],
      fields: { text: 'label', value: 'status' }
    },
    queryType: 'number',
    queryTemplate: {
      type: 'select',
      options: {
        ds: [
          { label: i18n.t('未缴纳'), value: 0 },
          { label: i18n.t('已缴纳'), value: 1 }
        ]
      }
    }
  },
  {
    field: 'stageName',
    headerText: i18n.t('供应商阶段')
  }
]
