<template>
  <div class="full-height candidate-supplier">
    <div class="top-box">
      <mt-tabs
        :tab-id="$utils.randomString()"
        :data-source="tabList"
        :selected-item="activeTab"
        @selected="selectTab"
      >
      </mt-tabs>
      <select-round @roundSelect="roundSelect"></select-round>
    </div>

    <source-list
      :round-id="roundId"
      :round-info="roundInfo"
      :rfx-status="rfxStatus"
      v-if="activeTab == 0"
    ></source-list>
    <supplier-list :round-id="roundId" :rfx-status="rfxStatus" v-else></supplier-list>
  </div>
</template>

<script>
export default {
  components: {
    sourceList: () =>
      import(
        /* webpackChunkName: "router/purchase/rfx/detail/tabs/candidate-supplier/components/source-list" */ './components/sourceList.vue'
      ),
    supplierList: () =>
      import(
        /* webpackChunkName: "router/purchase/rfx/detail/tabs/candidate-supplier/components/supplier-list" */ './components/supplierList.vue'
      ),
    //切换轮次
    selectRound: () =>
      import(
        /* webpackChunkName: "components/sourcing-project/select-round" */ 'COMPONENTS/SourcingProject/selectRound.vue'
      )
  },
  props: {
    turnsList: {
      type: Array,
      default: () => {
        return []
      }
    },
    currentTurnInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    rfxStatus: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      activeTab: 0,
      tabList: [
        {
          header: { text: this.$t('货源清单') }
        },
        {
          header: { text: this.$t('供应商清单') }
        }
      ],
      roundId: null,
      roundInfo: null
    }
  },
  methods: {
    selectTab(e) {
      this.activeTab = e.selectedIndex
    },
    roundSelect(roundId, roundInfo) {
      this.roundId = roundId
      this.roundInfo = roundInfo
    }
  }
}
</script>

<style lang="scss" scoped>
.candidate-supplier {
  display: flex;
  flex-direction: column;

  .source-list,
  .supplier-list {
    flex: 1;
  }
}
.top-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 20px;
  /deep/ .e-tab-header {
    background: transparent;
  }
}

/deep/ .mt-data-grid {
  .status-label {
    font-size: 12px;
    padding: 4px;
    border-radius: 2px;
    &.status-0 {
      color: #9baac1;
      background: rgba(155, 170, 193, 0.1);
    }
    &.status-1 {
      color: #6386c1;
      background: rgba(99, 134, 193, 0.1);
    }
  }
}
</style>
