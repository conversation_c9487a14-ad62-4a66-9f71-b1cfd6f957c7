// 选择供应商
<template>
  <mt-dialog
    ref="dialog"
    css-class="choose-source-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <div class="choose-pinlei">
        <div class="left-label">{{ $t('同时提供多种品类的供应商') }}</div>
        <mt-switch
          v-model="switchVal"
          :on-label="$t('已启用')"
          :off-label="$t('未启用')"
          @change="changeSwitch"
        ></mt-switch>
      </div>

      <mt-multi-select
        v-if="switchVal"
        v-model="categoryValue"
        :data-source="categoryList"
        :fields="{ text: 'categoryName', value: 'categoryId' }"
        :show-clear-button="false"
        :allow-filtering="true"
        :show-drop-down-icon="true"
        :placeholder="$t('请选择')"
        @change="handleChangeCategory"
      ></mt-multi-select>
      <div class="table-content">
        <mt-template-page ref="templateRef" :template-config="pageConfig" />
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { columnChooseSource } from '../config'
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      switchVal: false,
      categoryValue: [],
      categoryList: [],
      pageConfig: [
        {
          grid: {
            allowFiltering: true,
            columnData: columnChooseSource,
            dataSource: []
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        width: '900px',
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.getCateList()
  },
  methods: {
    confirm() {
      let _records = this.$refs.templateRef.getCurrentTabRef().grid.getSelectedRecords()

      if (_records.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      _records.forEach((item) => {
        delete item.id
      })
      this.$API.rfxSupplier
        .saveRFXSource({
          rfxId: this.modalData.rfxId,
          rfxRoundSupplierSaveRequestList: _records,
          roundId: this.modalData.roundId
        })
        .then(() => {
          this.$toast({ content: this.$t('新增成功'), type: 'success' })
          this.$emit('confirm-function')
        })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    changeSwitch() {
      this.categoryValue = []
    },
    handleChangeCategory() {
      this.resetAsyncConfigParams()
    },
    // 获取所有品类
    getCateList() {
      this.$API.rfxSupplier.getRFXCate({ id: this.modalData.rfxId }).then((res) => {
        this.categoryList = res.data
        this.resetAsyncConfigParams()
      })
    },
    //列表参数重新赋值
    resetAsyncConfigParams() {
      this.$set(this.pageConfig[0].grid, 'asyncConfig', {
        url: this.$API.rfxSupplier.chooseRFXSource,
        params: {
          rfxId: this.modalData.rfxId,
          categoryIdList: this.categoryValue,
          roundId: this.modalData.roundId
        },
        recordsPosition: 'data'
      })
    }
  }
}
</script>
<style lang="scss">
.choose-source-dialog {
  .dialog-content {
    width: 100%;
    height: 100%;
    padding-top: 18px;
    font-size: 16px;
    display: flex;
    flex-direction: column;
    .choose-pinlei {
      width: 100%;
      display: flex;
      justify-content: space-between;
      .left-label {
        font-size: 14px;
        color: #465b73;
      }
      .mt-switch {
        .switch-box {
          width: 32px;
          height: 18px;
          .switch-circle {
            width: 14px;
            height: 14px;
          }
          &.is-active .switch-circle {
            left: 18px;
          }
        }
      }
    }
    .mutliselect-container {
      margin-top: 20px;

      .e-multiselect {
        border: unset !important;
        &::before,
        &::after {
          display: none !important;
        }
      }
      .e-multi-select-wrapper {
        background: rgba(250, 250, 250, 1);
        border: 1px solid rgba(232, 232, 232, 1);
        border-radius: 4px;
        padding: 6px 0 6px 16px;
        .e-dropdownbase {
          // padding-left: 16px;
        }
      }
    }
    .table-content {
      flex: 1;
    }
  }
}
</style>
