<template>
  <div class="source-list">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    />
  </div>
</template>

<script>
import { sourceListColumnData } from '../config'
export default {
  props: {
    rfxStatus: {
      type: Number,
      default: 0
    },
    roundId: {
      type: String,
      default: ''
    },
    roundInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      pageConfig: [
        {
          toolbar: [],
          grid: {
            allowFiltering: true,
            columnData: sourceListColumnData,
            frozenColumns: 2,
            dataSource: []
          }
        }
      ]
    }
  },
  watch: {
    roundId(n, o) {
      if (n !== o) {
        this.resetAsyncConfigParams()
      }
    }
  },
  mounted() {
    this.judgeToolbar()
    if (this.roundId) {
      this.resetAsyncConfigParams()
    }
  },
  methods: {
    judgeToolbar() {
      let _arr = [
        { id: 'add', icon: 'icon_solid_Createproject', title: this.$t('新增') },
        { id: 'delete', icon: 'icon_solid_Delete1', title: this.$t('删除') },
        {
          id: 'active',
          icon: 'icon_solid_Activateorder',
          title: this.$t('启用')
        },
        { id: 'inactive', icon: 'icon_solid_Cancel', title: this.$t('禁用') }
      ]
      if (this.rfxStatus == 1) {
        _arr = [
          {
            id: 'active',
            icon: 'icon_solid_Activateorder',
            title: this.$t('启用')
          },
          { id: 'inactive', icon: 'icon_solid_Cancel', title: this.$t('禁用') }
        ]
      }
      this.$set(this.pageConfig[0], 'toolbar', _arr)
    },

    handleClickToolBar(e) {
      let _selectGridRecords = e.grid.getSelectedRecords()
      console.log('use-handleClickToolBar', e, _selectGridRecords)

      if (e.toolbar.id == 'add') {
        // 新增时判断当前的轮次状态，如果status为1，就不能新增
        if (this.roundInfo.status == 1) {
          this.$toast({
            content: this.$t('当前无未发布轮次'),
            type: 'warning'
          })
          return
        }
        this.handleAdd()
        return
      }
      if (_selectGridRecords.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _id = []
      _selectGridRecords.map((item) => {
        _id.push(item.id)
      })

      if (e.toolbar.id == 'delete') {
        this.handleDelete(_id)
      } else if (e.toolbar.id == 'active') {
        this.handleActive(_id, 'activeRFXSource')
      } else if (e.toolbar.id == 'inactive') {
        this.handleActive(_id, 'inActiveRFXSource')
      }
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'delete') {
        this.handleDelete([e.data.id])
      } else if (e.tool.id == 'Distribute') {
        this.handleDistribute([e.data.id])
      }
    },
    // 操作：新增
    handleAdd() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/rfx/detail/tabs/candidate-supplier/components/dialogSource" */ './dialogSource.vue'
          ),
        data: {
          title: this.$t('选择货源'),
          requestUrl: 'distributeByIdDetail',
          rfxId: this.$route.query.rfxId,
          roundId: this.roundId
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 操作：删除
    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除？'),
          confirm: () => this.$API.rfxSupplier.deleteRFXSource({ idList: ids })
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 启禁用货源
    handleActive(ids, url) {
      this.$API.rfxSupplier[url]({ idList: ids }).then(() => {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
      })
    },
    //列表参数重新赋值
    resetAsyncConfigParams() {
      this.$set(this.pageConfig[0].grid, 'asyncConfig', {
        url: this.$API.rfxSupplier.getRFXRoundSourceList,
        defaultRules: [
          {
            field: 'roundId',
            operator: 'equal',
            type: 'string',
            value: this.roundId
          }
        ]
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .mt-data-grid .status-text {
  font-size: 12px;
  line-height: 1;
  padding: 4px;
  border-radius: 2px;
  &.isStatus {
    color: #54bf00;
    background: rgba(84, 191, 0, 0.1);
  }
  &.unStatus {
    color: #ed5633;
    background: rgba(237, 86, 51, 0.1);
  }
  &.unping {
    color: #9a9a9a;
    background: rgba(154, 154, 154, 0.1);
  }
}
</style>
