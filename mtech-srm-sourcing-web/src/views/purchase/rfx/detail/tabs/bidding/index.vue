<template>
  <div class="full-height detail-container mt-flex-direction-column">
    <div class="detail-toolbar mt-flex">
      <select-round @roundSelect="roundSelect"></select-round>
    </div>
    <div class="content mt-flex">
      <div class="content-card-panel">
        <div
          :class="['content-card', 'mt-flex-direction-column', { active: currentIndex == index }]"
          @click="changeWuliao(index)"
          v-for="(item, index) in matierialList"
          :key="index"
        >
          <div class="content-panel mt-flex">
            <div class="content-item">
              <div class="field-title">{{ item.itemCode }}</div>
              <div class="field-value">{{ $t('描述信息') }}</div>
            </div>
            <div class="content-item">
              <div class="field-title">{{ $t('产品名称') }}</div>
              <div class="field-value value-link">{{ item.itemName }}</div>
            </div>
          </div>
          <div class="content-panel mt-flex">
            <div class="content-item">
              <div class="field-title">{{ item.bidQuantity }}</div>
              <div class="field-value">{{ $t('采购数量') }}</div>
            </div>
            <div class="content-item">
              <div class="field-title">
                {{ item.fieldData }}
              </div>
              <div class="field-value">{{ $t('采购预估价/KG') }}</div>
            </div>
            <div class="content-item">
              <div class="field-title">{{ $t('公司名称') }}</div>
              <div class="field-value">{{ item.companyName }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="content-table-panel">
        <div class="titles-box list-item-active" v-if="matierialList && matierialList.length > 0">
          <div class="label">{{ matierialList[currentIndex].itemName }}</div>
          <ul>
            <li>
              <div class="left-label">{{ $t('采购组织：') }}</div>
              <div class="right-val">
                {{ matierialList[currentIndex].purOrgName }}
              </div>
            </li>
            <li>
              <div class="left-label">{{ $t('采购数量：') }}</div>
              <div class="right-val">
                {{ matierialList[currentIndex].bidQuantity }}
              </div>
            </li>
            <li>
              <div class="left-label">{{ $t('采购预估价/KG：') }}</div>
              <div class="right-val">
                {{ matierialList[currentIndex].fieldData }}
              </div>
            </li>
          </ul>
        </div>

        <mt-chart
          ref="lineChart"
          :series-data-source="seriesDataSource"
          :primary-x-axis="primaryXAxis"
          :primary-y-axis="primaryYAxis"
          :zoom-settings="zoom"
          align="center"
          :tooltip="tooltip"
          :chart-area="chartArea"
          :width="chartSize + '%'"
          :height="chartSize + '%'"
          :legend-settings="legendSettings"
        >
        </mt-chart>
      </div>
    </div>
  </div>
</template>
<script>
import Vue from 'vue'
import MtChart from '@mtech-ui/chart'
Vue.use(MtChart)
import utils from '@/utils/utils'
export default {
  components: {
    //切换轮次
    selectRound: () =>
      import(
        /* webpackChunkName: "components/sourcing-project/select-round" */ 'COMPONENTS/SourcingProject/selectRound.vue'
      )
  },
  data() {
    return {
      utils,
      roundId: null,
      currentIndex: 0,
      matierialList: [],
      supplierNowPriceParentDTOListMap: new Map(),
      chartColors: ['#6386C1', '#EDA133', '#54BF00', '#ED5633', '#9963FD'],
      chartSize: 90,
      zoom: {
        //缩放
        enableMouseWheelZooming: true,
        enableSelectionZooming: true,
        enablePan: true,
        enablePinchZooming: true,
        enableScrollbar: true
      },
      primaryXAxis: {
        valueType: 'Category',
        interval: 1,
        majorGridLines: { width: 1 },
        labelIntersectAction: 'Rotate90'
      },
      primaryYAxis: {
        labelFormat: '{value}'
        // lineStyle: { width: 1 },
        // majorTickLines: { width: 0 },
        // minorTickLines: { width: 0 }
      },
      tooltip: {
        enable: true
      },
      chartArea: {
        border: {
          width: 0
        }
      },
      legendSettings: { visible: true },
      seriesDataSource: []
    }
  },
  mounted() {
    this.$bus.$on('changeKanban', this.changeChart)
    this.getData()
  },
  methods: {
    roundSelect(roundId, roundInfo) {
      this.roundNo = roundInfo.roundNo
      this.getData()
    },

    getData() {
      let params = {
        rfxId: this.$route.query.rfxId,
        roundNo: this.roundNo
      }
      this.$API.rfxDetail.getRFXPriceInfo(params).then((res) => {
        this.matierialList = res.data.rfxPurPackageDTOList || []

        this.supplierNowPriceParentDTOListMap = new Map()
        let _supplierNowPriceParentDTOList = res.data.supplierNowPriceParentDTOList || []
        _supplierNowPriceParentDTOList.forEach((item) => {
          this.supplierNowPriceParentDTOListMap.set(item.packageId, item)
        })
        this.setData()
      })
    },

    setData() {
      if (this.matierialList.length <= 0) {
        return
      }
      let _seriesDataSource = []
      let _priceList = this.supplierNowPriceParentDTOListMap.get(
        this.matierialList[this.currentIndex].packageId
      )
      if (!_priceList || (_priceList && _priceList.supplierNowPriceDTOList.length <= 0)) {
        this.seriesDataSource = [{ dataSource: [] }]
        return
      }
      _priceList.supplierNowPriceDTOList.forEach((item, index) => {
        let _oneline = {
          type: 'Spline',
          xName: 'XValue',
          yName: 'YValue',
          name: item[0] && item[0].supplierName,
          fill: this.chartColors[index % 5],
          width: 2,
          dataSource: [],
          marker: {
            visible: true,
            height: 10,
            width: 10
          }
        }
        item.forEach((pointItem) => {
          _oneline.dataSource.push({
            XValue: utils.formatTime(new Date(pointItem.bidTime), 'mm-dd HH:MM'), // "07-21 15:30"
            YValue: pointItem.taxedUnitPrice // 15623
          })
        })
        _seriesDataSource.push(_oneline)
      })
      this.seriesDataSource = _seriesDataSource
    },

    changeWuliao(index) {
      this.currentIndex = index
      this.setData()
    },

    changeChart() {
      var myEvent = new Event('resize')
      window.dispatchEvent(myEvent)
    }
  }
}
</script>
<style lang="scss" scoped>
.wihe100 {
  width: 100%;
  height: 100%;
}

.detail-container {
  border-radius: 4px 4px 0 0;
  border: 1px solid #e8e8e8;
  .detail-toolbar {
    height: 50px;
    flex-shrink: 0;
    box-shadow: inset 0 -1px 0 0 rgba(237, 239, 243, 1);
    padding: 0 20px;
    align-items: center;
    justify-content: space-between;
    .option-panel {
      .option-item {
        display: inline-block;
        .strategy-common-label {
          width: 70px;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          color: #465b73;
          text-align: right;
          display: inline-block;
          margin-right: 20px;
          position: relative;
        }
        .strategy-common-container {
          width: 120px;
          display: inline-block;
          height: 40px;
          background: #fafafa;
          border: 1px solid #e8e8e8;
          border-radius: 4px;
          padding: 0 20px;
        }
        /deep/ .strategy-element {
          border: none;
          border-color: transparent !important;
          position: relative;
          top: 4px;
          &:before,
          &:after {
            display: none;
          }
          .e-float-line {
            display: none;
          }
          .e-control {
            color: #292929;
            border: none;
            border-color: transparent !important;
          }
        }
      }
    }
  }
  .content {
    flex: 1;
    min-height: 0;
    background: #e8e8e8;
    .content-card-panel {
      width: 360px;
      background: #ffffff;
      flex-shrink: 0;
      padding: 20px;
      overflow: auto;
      .content-card {
        width: 318px;
        height: 104px;
        margin-bottom: 20px;
        cursor: pointer;
        justify-content: space-between;
        background: rgba(255, 255, 255, 1);
        border: 1px solid rgba(232, 232, 232, 1);
        border-radius: 4px;
        box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
        padding: 10px 20px;
        &:before {
          content: '';
          width: 80%;
          height: 0;
          left: 10%;
          top: 50%;
          position: absolute;
          border-top: 1px solid #e8e8e8;
        }
        &.active {
          background: #f5f6f9;
          border-left: none;
          position: relative;
          &:before {
            content: '';
            height: 100%;
            width: 0;
            position: absolute;
            border-left: 3px solid #00469c;
            border-radius: 4px 0 0 8px;
            left: 0;
            top: 0;
            z-index: 2;
            animation: active-animation 0.2s ease;
          }
          @keyframes active-animation {
            0% {
              top: 50%;
              height: 0;
            }
            100% {
              top: 0;
              height: 100%;
            }
          }
          &:after {
            content: '';
            width: 6px;
            height: 6px;
            background: 0 0;
            border-left: none;
            border-top: none;
            border-right: 1px solid #6386c1;
            border-bottom: 1px solid #6386c1;
            position: absolute;
            transform: rotate(-45deg);
            cursor: pointer;
            right: -10px;
            top: calc(50% - 3px);
          }
        }
        .content-panel {
          justify-content: space-between;
        }
        .content-item {
          .field-title {
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: 600;
            color: rgba(35, 43, 57, 1);
          }
          .field-value {
            margin-top: 6px;
            font-size: 12px;
            font-family: PingFangSC;
            font-weight: normal;
            color: rgba(154, 154, 154, 1);
            &.value-link {
              color: rgba(99, 134, 193, 1);
            }
          }
        }
      }
    }
    .content-table-panel {
      flex: 1;
      margin-left: 10px;
      background: #ffffff;
      overflow: auto;
      position: relative;

      .titles-box {
        position: absolute;
        left: 120px;
        top: 60px;
        z-index: 10;
        background: rgba(255, 255, 255, 1);
        border-radius: 4px;
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.06);
        padding: 10px 20px;
        overflow: hidden;
        .label {
          font-size: 16px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(46, 46, 46, 1);
        }
        ul li {
          display: flex;
          align-items: center;
          margin: 10px 0;

          &:last-child {
            margin-bottom: 0;
          }
          .left-label {
            width: 120px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: normal;
            color: rgba(70, 91, 115, 1);
          }
          .right-val {
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: normal;
            color: rgba(35, 43, 57, 1);
          }
        }
      }

      /deep/ .mt-chart {
        @extend .wihe100;
        .e-chart {
          @extend .wihe100;

          > svg {
            margin-top: 5%;
          }
        }
      }
    }
  }
}
</style>
