<template>
  <div class="supply-manage">
    <title-name
      icon-name="icon_solid_Personal1"
      name="$t('供应商管理')"
      :tab-index="2"
    ></title-name>
    <mt-template-page ref="templateRef" :template-config="pageConfig" />
  </div>
</template>

<script>
import { supplyColumnData } from '../config'
export default {
  components: {
    titleName: () =>
      import(
        /* webpackChunkName: "components/sourcing-project/panel-title" */ 'COMPONENTS/SourcingProject/panelTitle.vue'
      )
  },
  data() {
    return {
      pageConfig: [
        {
          useToolTemplate: false,
          grid: {
            allowFiltering: true,
            columnData: supplyColumnData,
            asyncConfig: {
              url: this.$API.rfxDetail.getRFXSupInfo,
              params: { rfxId: this.$route.query.rfxId },
              recordsPosition: 'data.supplierList.records'
            }
          }
        }
      ]
    }
  }
}
</script>
