<template>
  <div class="progress-summary">
    <div class="title-name">
      <div class="left-name">
        <mt-icon name="icon_solid_Progress1"></mt-icon>
        <span class="name">{{ $t('进度汇总') }}</span>
      </div>
      <div class="right-box">
        <mt-select
          width="60"
          v-model="dateId"
          :data-source="dateArray"
          :fields="{ value: 'id', text: 'date' }"
          css-class="strategy-element"
          @select="handleSelect"
        ></mt-select>
      </div>
    </div>
    <mt-gantt
      :data-source="ganttData"
      :task-fields="taskFields"
      :row-height="24"
      :allow-selection="false"
      :allow-resizing="true"
      :include-weekend="true"
      :timeline-settings="timelineSettingsModel"
      :tooltip-settings="tooltipSettingsModel"
    >
      <e-columns>
        <e-column field="TaskName" width="80" header-text=""></e-column>
      </e-columns>
    </mt-gantt>
  </div>
</template>

<script>
import Vue from 'vue'
import utils from '@/utils/utils'
import MtGantt from '@mtech-ui/gantt'
Vue.use(MtGantt)

export default {
  components: {},
  props: {
    progressList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      dateArray: [
        { date: this.$t('日'), id: 'Date' },
        { date: this.$t('周'), id: 'Week' },
        { date: this.$t('月'), id: 'Month' }
      ],
      dateId: 'Month',
      utils,
      // 时间线
      timelineSettingsModel: {
        updateTimescaleView: true,
        timelineViewMode: 'Day',
        timelineUnitSize: 22,
        topTier: {
          unit: 'Month'
        },
        bottomTier: {
          unit: 'Day',
          format: 'd'
        }
      },
      tooltipSettingsModel: {
        showTooltip: true,
        taskbar: function () {
          return {
            template: Vue.component('taskbarTemplate', {
              // template: `<div><div>{{data.TaskName}}:</div> <div>{{formatTime(data.StartDate)}} ~ {{formatTime(data.EndDate)}}</div></div>`,
              template: `<div class="hall-gantt list-item-active"><div class="title">{{data.TaskName}} {{data.TaskID%2 == 0 ? '实际时间': '计划时间'}}:</div><div class="time">{{formatTime(data.StartDate)}} ~ {{formatTime(data.EndDate)}}</div></div>`,
              data: function () {
                return {
                  data: {}
                }
              },
              mounted() {},
              methods: {
                formatTime(date) {
                  return utils.formatTime(date)
                }
              }
            })
          }
        }
      },
      taskFields: {
        id: 'TaskID',
        name: 'TaskName',
        startDate: 'StartDate',
        endDate: 'EndDate',
        cssClass: 'CssClass'
      },
      labelSettings: {
        leftLabel: '${StartDate}',
        rightLabel: '${EndDate}'
        // taskLabel: "TaskID",
      },
      data: [
        {
          TaskID: 1,
          TaskName: this.$t('立项准备'),
          StartDate: new Date('07/01/2021'),
          EndDate: new Date('07/08/2021')
        },
        {
          TaskID: 2,
          TaskName: this.$t('立项准备'),
          StartDate: new Date('07/02/2021'),
          EndDate: new Date('07/05/2021')
        },
        {
          TaskID: 3,
          TaskName: this.$t('询价投标'),
          StartDate: new Date('07/01/2021'),
          EndDate: new Date('07/05/2021')
        },
        {
          TaskID: 4,
          TaskName: this.$t('询价投标'),
          StartDate: new Date('07/01/2021'),
          EndDate: new Date('07/05/2021')
        },
        {
          TaskID: 5,
          TaskName: this.$t('报价投标'),
          StartDate: new Date('07/02/2021'),
          EndDate: new Date('07/015/2021')
        },
        {
          TaskID: 6,
          TaskName: this.$t('报价投标'),
          StartDate: new Date('07/02/2021'),
          EndDate: new Date('07/10/2021')
        },
        {
          TaskID: 7,
          TaskName: this.$t('评标'),
          StartDate: new Date('07/07/2021'),
          EndDate: new Date('07/08/2021')
        },
        {
          TaskID: 8,
          TaskName: this.$t('评标'),
          StartDate: new Date('07/07/2021'),
          EndDate: new Date('07/010/2021')
        },
        {
          TaskID: 9,
          TaskName: this.$t('决标'),
          StartDate: new Date('07/05/2021'),
          EndDate: new Date('07/10/2021')
        },
        {
          TaskID: 10,
          TaskName: this.$t('决标'),
          StartDate: new Date('07/05/2021'),
          EndDate: new Date('07/16/2021')
        }
      ]
    }
  },

  computed: {
    ganttData() {
      if (!this.progressList) {
        return
      }
      let _data = [],
        _index = 1
      this.progressList.forEach((item) => {
        // 计划时间
        _data.push({
          TaskID: _index,
          TaskName: item.phaseName,
          StartDate: utils.formatTime(new Date(item.planStartTime), 'YYYY/mm/dd'),
          EndDate: utils.formatTime(new Date(item.planEndTime), 'YYYY/mm/dd')
        })
        // 实际时间
        _data.push({
          TaskID: _index + 1,
          TaskName: item.phaseName,
          StartDate: utils.formatTime(new Date(item.realStartTime), 'YYYY/mm/dd'),
          EndDate: utils.formatTime(new Date(item.realEndTime), 'YYYY/mm/dd'),
          CssClass: `${!item.realStartTime && 'noData'}`
          // StartDate: new Date("2021/09/30"),
          // EndDate: new Date("2021/09/30"),
        })
        _index++
      })
      return _data
    }
  },
  methods: {
    handleSelect(e) {
      this.$nextTick(() => {
        this.timelineSettingsModel = {
          updateTimescaleView: true,
          timelineViewMode: 'Day',
          timelineUnitSize: 22,
          topTier: {
            unit: e.itemData.id
          },
          bottomTier: {
            unit: 'Day',
            format: 'd'
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .mt-gantt {
  // margin-top: -21px;
  position: relative;
  z-index: 1;
  flex: 1;
  // overflow-y: auto;
  #GanttContainer,
  .e-gantt-splitter,
  .e-gantt-tree-grid-pane,
  .e-gantt-chart-pane {
    height: 100% !important;
  }
  .e-gantt-splitter {
    border: unset !important;
    .e-pane {
      overflow: auto !important;
    }
  }
  .e-gantt-tree-grid-pane {
    flex-basis: 12% !important;
    .e-gridheader {
      background: #fff;
      .e-headercell {
        padding-right: 0 !important;
        .e-headercelldiv {
          text-align: right;
          margin-top: 28px;
          padding-right: 10px;
        }
      }
      tr:first-child th {
        background: #fff !important;
      }
      // visibility: hidden;
    }
    .e-treecolumn-container {
      .e-icons {
        display: none !important;
      }
    }
    td {
      border: unset !important;
    }
    .e-grid {
      .e-rowcell:last-child,
      .e-summarycell:last-child,
      .e-rowcell:first-child,
      .e-summarycell:first-child {
        padding: 0 !important;
      }
    }

    .e-gridcontent .e-table tbody {
      tr:nth-child(2n) {
        visibility: hidden;
      }
      tr:nth-child(2n + 1) td {
        padding-top: 10px !important;
      }
    }
  }

  .e-gantt-chart-pane {
    height: 100%;
    border-left: 1px solid #e0e0e0;
    .e-chart-root-container {
      overflow: auto !important;
      .e-content {
        overflow: hidden;
      }
      .e-content:hover {
        overflow: auto;
      }
      .noData {
        visibility: hidden;
      }
    }
    .e-timeline-header-container {
      .e-timeline-header-table-body th:not(.e-timeline-top-header-cell) {
        border: none;
      }
    }

    #GanttContainerGanttTaskTableBody {
      td {
        border: unset !important;
      }
      tr:nth-child(2n) {
        .e-gantt-child-taskbar-inner-div {
          background-color: #eda133;
          border-color: #eda133;
          border-radius: 4px 100px 100px 4px;
          margin-top: -10px;
        }
      }
      tr:nth-child(2n + 1) {
        .e-gantt-child-taskbar-inner-div {
          background-color: #6386c1;
          border-color: #6386c1;
          border-radius: 4px 100px 100px 4px;
        }
      }
    }
  }

  .e-split-bar {
    display: none;
  }
}

.title-name {
  width: 100%;
  height: 60px;
  flex-shrink: 0;
  background: rgba(255, 255, 255, 1);
  border-radius: 6px 6px 0 0;
  box-shadow: 0 0 5px 0 rgba(137, 120, 120, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 10;

  .left-name {
    padding-left: 20px;
    display: flex;
    align-items: center;

    .mt-icons {
      color: #6386c1;
    }
    .name {
      font-size: 20px;
      margin-left: 10px;
      margin-right: 10px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(41, 41, 41, 1);
    }

    .timer {
      font-size: 12px;
      font-family: DINAlternate;
      font-weight: bold;
      color: rgba(154, 154, 154, 1);
    }
  }

  .right-box {
    margin-right: 30px;
  }
  .right-link {
    cursor: pointer;
    padding-right: 15px;
    .mt-icons {
      transform: rotate(-90deg) scale(0.5);
      display: inline-block;
      color: #9baac1;
      font-size: 12px;
    }
  }

  /deep/ .strategy-element {
    border: unset !important;
    border: 1px solid #e8e8e8 !important;
    border-radius: 2px;
    position: relative;
    top: 4px;
    &:before,
    &:after {
      display: none;
    }
    .e-float-line {
      display: none;
    }
    .e-control {
      color: #292929;
      border: none;
      border-color: transparent !important;
    }
  }
}
</style>

<style lang="scss">
.e-gantt-tooltip .hall-gantt {
  border-radius: 4px 4px 0 0;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 10px 14px;
  background: rgba(250, 250, 250, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-left: 0;
  overflow: hidden;

  .title {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 600;
    color: rgba(35, 43, 57, 1);
    margin-bottom: 8px;
  }
  .time {
    font-size: 12px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(154, 154, 154, 1);
    margin-left: 8px;
  }
}
</style>
