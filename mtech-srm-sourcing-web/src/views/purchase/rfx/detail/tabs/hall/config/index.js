import { i18n } from '@/main.js'
import Vue from 'vue'
export const supplyColumnData = [
  {
    field: 'supplierCode',
    headerText: i18n.t('编号')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('名称')
  },
  {
    field: 'deposit',
    headerText: i18n.t('保障金'),
    valueConverter: {
      type: 'map',
      map: [
        { status: 0, label: i18n.t('未缴纳'), cssClass: ['pay-text', 'unPay'] },
        { status: 1, label: i18n.t('已缴纳'), cssClass: ['pay-text', 'isPay'] }
      ],
      fields: { text: 'label', value: 'status' }
    },
    queryType: 'number',
    queryTemplate: {
      type: 'select',
      options: {
        ds: [
          { label: i18n.t('未缴纳'), value: 0 },
          { label: i18n.t('已缴纳'), value: 1 }
        ]
      }
    }
  },
  {
    field: 'approved',
    headerText: i18n.t('审核'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div class="action-boxs"><mt-icon name="icon_V" class="checked" v-if="data.approved == 1" src=""></mt-icon><mt-icon name="icon_X" class="uncheck" v-else></mt-icon></div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'stageName',
    headerText: i18n.t('阶段'),
    valueConverter: { type: 'placeholder', placeholder: '-' }
  },
  {
    field: 'bidStatus',
    headerText: i18n.t('状态'), // 0 未响应 1 已参与 2 已报价 3 被拒绝
    valueConverter: {
      type: 'map',
      map: [
        {
          status: 0,
          label: i18n.t('未报价'),
          cssClass: ['status-label', 'status-0']
        },
        {
          status: 1,
          label: i18n.t('未报价'),
          cssClass: ['status-label', 'status-0']
        },
        {
          status: 2,
          label: i18n.t('已报价'),
          cssClass: ['status-label', 'status-1']
        },
        {
          status: 3,
          label: i18n.t('已报价'),
          cssClass: ['status-label', 'status-1']
        }
      ],
      fields: { text: 'label', value: 'status' }
    }
  },
  {
    field: 'bidTimes',
    headerText: i18n.t('报价次数')
  }
]
