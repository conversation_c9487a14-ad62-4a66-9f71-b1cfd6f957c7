import { i18n } from '@/main.js'
export const sampleColumn = [
  {
    field: 'rfxBidding.supplierStage',
    headerText: i18n.t('供应商阶段')
  },
  {
    field: 'rfxBidding.bidTimes',
    headerText: i18n.t('报价次数')
  },
  {
    field: 'stageType',
    headerText: i18n.t('阶梯方式'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('按数据'), 1: i18n.t('按时间') }
    }
  },
  {
    field: 'stages',
    headerText: i18n.t('阶梯报价'),
    cssClass: 'field-content',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return e.length ? i18n.t('阶梯报价') : ''
      }
    }
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('未税单价')
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('含税单价')
  },
  {
    field: 'supplyQuantity',
    headerText: i18n.t('报价数量')
  },
  {
    field: 'taxRate',
    headerText: i18n.t('税率')
  },
  {
    field: 'untaxedTotalPrice',
    headerText: i18n.t('未税总价')
  },
  {
    field: 'taxedTotalPrice',
    headerText: i18n.t('含税总价')
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  },
  {
    field: 'unkown',
    headerText: i18n.t('明细报价')
  },
  {
    field: 'unkown',
    headerText: i18n.t('附件')
  },
  {
    field: 'bidTimes',
    headerText: i18n.t('报价历史'),
    cssClass: 'field-content',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return e ? i18n.t('报价历史') : ''
      }
    }
  }
]
export const valueConverter = {
  //报价状态 0 未提交 1 已报 2 已接受 3 已拒绝 4 已还价 5 已中标
  type: 'map',
  map: {
    0: i18n.t('未提交'),
    1: i18n.t('已报'),
    2: i18n.t('已接受'),
    3: i18n.t('已拒绝'),
    4: i18n.t('已还价'),
    5: i18n.t('已中标')
  }
}
