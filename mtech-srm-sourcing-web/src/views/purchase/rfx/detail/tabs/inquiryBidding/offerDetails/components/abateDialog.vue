<template>
  <mt-dialog
    ref="dialog"
    css-class="small-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="row">
      <div class="col-12">
        <div class="title-box">{{ $t('还价单价') }}</div>
        <mt-input
          v-model="abateForm.abatePrice"
          type="text"
          :placeholder="$t('请输入还价单价')"
        ></mt-input>
      </div>
      <div class="col-12 m-mt-md">
        <div class="title-box">{{ $t('还价理由') }}</div>
        <mt-input
          v-model="abateForm.abateReason"
          type="text"
          :placeholder="$t('请输入还价理由')"
        ></mt-input>
      </div>
      <!-- <div class="col-12 m-mt-md">
          <div class="title-box">{{ $t("还价附件") }}</div>
          <mt-input
            v-model="abateForm.abatePrice"
            type="text"
            :placeholder="$t('点击上传附件')"
          ></mt-input>
        </div> -->
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      abateForm: {
        abatePrice: '',
        abateReason: '',
        attachmentFiles: [],
        rfxBiddingId: '',
        rfxBiddingItemId: ''
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this?.modalData?.data) {
      let _data = this.modalData.data
      this.abateForm = {
        abatePrice: '',
        abateReason: '',
        attachmentFiles: [],
        rfxBiddingId: _data.biddingId,
        rfxBiddingItemId: _data.id
      }
    }
  },
  methods: {
    confirm() {
      this.$store.commit('startLoading')
      this.$API.inquiryBidding.doRfxBiddingAbate(this.abateForm).then(() => {
        this.$store.commit('endLoading')
        this.$emit('confirm-function')
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
