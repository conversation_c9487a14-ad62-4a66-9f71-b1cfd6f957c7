import { i18n } from '@/main.js'
export const ladderColumnData = [
  {
    field: 'stageFrom',
    headerText: i18n.t('阶梯开始')
  },
  {
    field: 'stageTo',
    headerText: i18n.t('阶梯结束')
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('未税单价')
  },
  {
    field: 'taxRate',
    headerText: i18n.t('税率')
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('含税单价')
  },
  {
    field: 'column4',
    headerText: i18n.t('是否含运费？')
  },
  {
    field: 'column5',
    headerText: i18n.t('运费？')
  }
]
export const detailColumnData = [
  {
    field: 'column1',
    headerText: i18n.t('明细开始')
  },
  {
    field: 'column2',
    headerText: i18n.t('明细结束')
  },
  {
    field: 'column3',
    headerText: i18n.t('未税单价')
  },
  {
    field: 'column4',
    headerText: i18n.t('税率')
  },
  {
    field: 'column5',
    headerText: i18n.t('含税单价')
  }
]
export const getDialogListGridData = (len = 10) => {
  let res = []
  for (let i = 0; i < len; i++) {
    res.push({
      column1: 'column1-' + i,
      column2: 'column2-' + i,
      column3: 'column3-' + i,
      column4: 'column4-' + i,
      column5: 'column5-' + i
    })
  }
  return res
}
