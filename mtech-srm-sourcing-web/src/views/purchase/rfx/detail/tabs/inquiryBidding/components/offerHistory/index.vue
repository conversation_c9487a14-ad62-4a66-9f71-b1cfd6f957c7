<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-tabs
        v-if="dataList.length > 0"
        :e-tab="false"
        :data-source="tabData"
        :selected-item="0"
        @handleSelectTab="handleSelectTab"
      >
      </mt-tabs>
      <div v-for="(item, index) in dataList" :key="item.id">
        <div v-if="activeTab === index">
          <div class="price-info-panel mt-flex-direction-column">
            <div class="info-header mt-flex-direction-column">
              <span class="info-title">{{ item.rfxBidding.rfxCode }}</span>
              <div class="info-desc mt-flex">
                <span class="main-desc">{{ item.packageName }}</span>
                <div class="desc-item">
                  <mt-icon name="icon_Department"></mt-icon>
                  <span class="desc-title">{{ $t('采购总价') }}</span>
                  <span class="desc-value">￥{{ item.taxedTotalPrice }}</span>
                </div>
                <div class="desc-item">
                  <mt-icon name="icon_Department"></mt-icon>
                  <span class="desc-title">{{ $t('采购数量') }}</span>
                  <span class="desc-value">{{ item.supplyQuantity }}</span>
                </div>
                <div class="desc-item">
                  <mt-icon name="icon_Department"></mt-icon>
                  <span class="desc-title">{{ $t('采购预估价/kg') }}</span>
                  <span class="desc-value">￥{{ item.unkown }}</span>
                </div>
              </div>
            </div>
            <div class="info-detail">
              <div class="detail-title">{{ $t('报价信息') }}</div>
              <div class="detail-fields mt-flex">
                <div class="field-item" v-for="(column, index2) in fieldColumn" :key="index2">
                  <div class="field-title">{{ column.headerText }}</div>
                  <div class="field-value">{{ item[column.field] }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="detail-tabs-container mt-flex">
        <div class="left-panel">
          <mt-tabs
            :tab-id="$utils.randomString()"
            :e-tab="false"
            :data-source="tableTabs"
            @handleSelectTab="handleSelectTableTab"
          ></mt-tabs>
        </div>

        <div class="right-panel">
          <!-- <div class="option-item">
            <select-round @roundSelect="roundSelect"></select-round>
          </div> -->
        </div>
      </div>
      <div class="ladder-list" v-if="listTab == 0">
        <mt-template-page :template-config="ladderListConfig" />
      </div>
      <div class="price-list" v-if="listTab == 1">
        <mt-template-page :template-config="priceListConfig" />
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { ladderColumnData, detailColumnData } from './config'
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      activeTab: 0,
      listTab: 0,
      tableTabs: [
        {
          title: this.$t('阶梯报价')
        },
        {
          title: this.$t('明细报价')
        }
      ],
      fieldColumn: [
        { field: 'untaxedUnitPrice', headerText: this.$t('未税单价') },
        { field: 'taxRate', headerText: this.$t('税率') },
        { field: 'taxedUnitPrice', headerText: this.$t('含税单价') },
        { field: 'validStartTime', headerText: this.$t('报价有效期从') },
        { field: 'validEndTime', headerText: this.$t('报价有效期止') },
        { field: 'supplyQuantity', headerText: this.$t('可供数量') },
        {
          field: 'deliveryCommitmentDate',
          headerText: this.$t('承诺交货日期')
        },
        { field: 'deliveryPreTime', headerText: this.$t('供货提前期') },
        { field: 'minPurchaseQuantity', headerText: this.$t('最小采购量') },
        { field: 'minPackageQuantity', headerText: this.$t('最小包装数量') },
        { field: 'column11', headerText: this.$t('供应商附件') }
      ],
      ladderListConfig: [
        {
          grid: {
            allowFiltering: true,
            allowPaging: false,
            columnData: ladderColumnData,
            dataSource: []
          }
        }
      ],
      priceListConfig: [
        {
          grid: {
            allowFiltering: true,
            allowPaging: false,
            columnData: detailColumnData,
            dataSource: []
          }
        }
      ],
      dataList: [],
      tabData: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.historyDataGet()
  },
  methods: {
    // 初始化数据
    // FIXME: this.$dialog导致该组件及子组件无法访问this.$route,需查明原因修复  --7.28
    historyDataGet() {
      let query = { ...this.modalData.data }
      query.queryBuilderDTO = {
        page: {
          size: 10,
          current: 1
        }
      }
      this.$API.inquiryBidding.historyView(query).then((res) => {
        this.tabData = res.data.records.map((e) => {
          return {
            title: `第${e.bidTimes}次`
          }
        })
        this.dataList = res.data.records
        this.resetGridDataSource()
      })
    },
    resetGridDataSource() {
      this.$set(
        this.ladderListConfig[0]['grid'],
        'dataSource',
        this.dataList[this.activeTab]['stages']
      )
    },
    handleSelectTab(e) {
      this.activeTab = e
      this.resetGridDataSource()
    },
    handleSelectTableTab(e) {
      this.listTab = e
    },
    confirm() {
      this.$emit('confirm-function')
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  /deep/ .mt-tabs-container {
    height: 68px;
    background: #ffffff;
    .tabs-arrow {
      display: none;
    }
    .tab-wrap {
      padding: 0;
      .tab-item {
        padding: 6px 10px;
        span {
          line-height: 1;
        }
      }
    }
  }
  .price-info-panel {
    width: 860px;
    height: 393px;
    background: #ffffff;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    .info-header {
      height: 84px;
      background: #ffffff;
      border-bottom: 1px solid #e8e8e8;
      border-radius: 4px 4px 0 0;
      padding: 20px;
      justify-content: space-between;
      .info-title {
        font-size: 20px;
        font-family: DINAlternate;
        font-weight: bold;
        color: rgba(41, 41, 41, 1);
      }
      .info-desc {
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: normal;

        .main-desc {
          color: rgba(41, 41, 41, 1);
        }
        .desc-item {
          color: rgba(157, 170, 191, 1);
          margin-left: 30px;
          .mt-icons {
            position: relative;
            top: -2px;
            margin-right: 5px;
          }
          .desc-title {
            &:after {
              content: '：';
            }
          }
        }
      }
    }
    .info-detail {
      flex: 1;
      padding: 20px;
      .detail-title {
        font-size: 16px;
        color: #292929;
        display: inline-block;
        padding-left: 13px;
        position: relative;

        &:before {
          content: '';
          position: absolute;
          width: 3px;
          height: 14px;
          background: #6386c1;
          border-radius: 0 2px 2px 0;
          left: 0;
          top: 2px;
        }
      }
      .detail-fields {
        flex-wrap: wrap;
        .field-item {
          width: 33%;
          margin-top: 40px;
          display: flex;
          .field-title {
            width: 100px;
            text-align: right;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: 500;
            color: rgba(70, 91, 115, 1);
            flex-shrink: 0;
            &:after {
              content: '：';
            }
          }
          .field-value {
            flex: 1;
            margin-left: 10px;
            width: 90px;
            text-align: left;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: normal;
            color: rgba(35, 43, 57, 1);
          }
        }
      }
    }
  }
  .detail-tabs-container {
    box-shadow: inset 0 -1px 0 0 rgba(237, 239, 243, 1);
    align-items: center;
    justify-content: space-between;
    .right-panel {
      .option-item {
        display: flex;
        align-items: center;
        .strategy-common-label {
          width: 70px;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          color: #465b73;
          text-align: right;
          display: inline-block;
          margin-right: 20px;
          position: relative;
        }
        .strategy-common-container {
          width: 120px;
          display: inline-block;
          height: 34px;
          background: #fafafa;
          border: 1px solid #e8e8e8;
          border-radius: 4px;
          padding: 0 20px;
        }
        /deep/ .strategy-element {
          border: none;
          border-color: transparent !important;
          position: relative;
          top: 0;
          &:before,
          &:after {
            display: none;
          }
          .e-float-line {
            display: none;
          }
          .e-control {
            color: #292929;
            border: none;
            border-color: transparent !important;
          }
        }
      }
    }
  }
}
</style>
