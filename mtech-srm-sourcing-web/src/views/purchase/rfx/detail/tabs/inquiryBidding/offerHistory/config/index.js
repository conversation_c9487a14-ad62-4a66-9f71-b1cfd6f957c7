import { i18n } from '@/main.js'
import Vue from 'vue'
export const historyListColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'rfxBidding.supplierCode',
    headerText: i18n.t('供应商编号'),
    cellTools: [{ id: 'History', icon: 'icon_Editor', title: i18n.t('查看历史') }]
  },
  {
    field: 'rfxBidding.supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'rfxBidding.bidTimes',
    headerText: i18n.t('报价次数')
  },
  {
    field: 'rfxBidding.status',
    headerText: i18n.t('状态'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<span >{{statusMap[+data.rfxBidding.status]}}</span>`,
          data() {
            return {
              data: {},
              statusMap: [i18n.t('未响应'), i18n.t('已参与'), i18n.t('已报价'), i18n.t('被拒绝')]
            }
          }
        })
      }
    }
  },
  {
    field: 'rfxBidding.supplierStage',
    headerText: i18n.t('供应商阶段')
  },
  {
    field: 'supplyQuantity',
    headerText: i18n.t('报价数量')
  },
  {
    field: 'stageBidStatus',
    headerText: i18n.t('是否阶梯报价'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('未启用'), 1: i18n.t('启用') }
    }
  },
  {
    field: 'stageType',
    headerText: i18n.t('阶梯方式'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('按数据'), 1: i18n.t('按时间') }
    }
  },
  {
    field: 'stages', //遍历stages数组
    headerText: i18n.t('阶梯范围'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<ul :class="['custom-price-td', {'single-item':data.stages.length<2}]">
          <li class="price-item" v-for="(item, index) in data.stages" :key="index"><div>{{item.stageFrom}}—{{item.stageTo}}</div></li>
        </ul>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('未税单价'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<ul  class="custom-td">
                      <li v-for="(item, index) in data.stages" :key="index"><div>{{item.untaxedUnitPrice}}</div></li>
                    </ul>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('含税单价'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<ul  class="custom-td">
                      <li v-for="(item, index) in data.stages" :key="index"><div>{{item.taxedUnitPrice}}</div></li>
                    </ul>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'taxName',
    headerText: i18n.t('税率'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<ul  class="custom-td">
                      <li v-for="(item, index) in data.stages" :key="index"><div>{{item.taxName}}</div></li>
                    </ul>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('含税单价')
  },
  {
    field: 'taxedTotalPrice',
    headerText: i18n.t('含税总价')
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  },
  {
    field: 'column16',
    headerText: i18n.t('明细报价'),
    valueConverter: { type: 'placeholder', placeholder: '-' }
  },
  {
    field: 'column17',
    headerText: i18n.t('附件')
  },
  {
    field: 'bidTimes',
    headerText: i18n.t('报价历史'),
    cssClass: 'field-content',
    valueConverter: {
      type: 'map',
      map: { 0: '', 1: i18n.t('报价历史') }
    }
  }
]
