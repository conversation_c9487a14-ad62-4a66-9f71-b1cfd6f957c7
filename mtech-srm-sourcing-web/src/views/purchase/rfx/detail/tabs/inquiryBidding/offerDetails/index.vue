<template>
  <div class="full-height detail-container mt-flex-direction-column">
    <div class="detail-toolbar mt-flex">
      <div class="left-panel mt-flex">
        <div class="svg-option-item" @click="handleFinishCurrentTurn">
          <mt-icon name="icon_Auction" />
          <span>{{ $t('结束本轮') }}</span>
        </div>
        <div class="svg-option-item" @click="handleFinishAllTurn">
          <mt-icon name="icon_Auction" />
          <span>{{ $t('结束全部轮') }}</span>
        </div>
        <div class="svg-option-item" @click="handleStartNextTurn">
          <mt-icon name="icon_Auction" />
          <span>{{ $t('开启下一轮') }}</span>
        </div>
        <div class="svg-option-item" @click="handleSendOutBidding">
          <mt-icon name="icon_Auction" />
          <span>{{ $t('发布') }}</span>
        </div>
        <div class="svg-option-item custom-split" @click="handleAcceptBidding">
          <mt-icon name="icon_solid_edit" />
          <span>{{ $t('接收报价') }}</span>
        </div>
        <div class="svg-option-item" @click="handleRejectQuote">
          <mt-icon name="icon_solid_edit" />
          <span>{{ $t('拒绝报价') }}</span>
        </div>
        <div class="svg-option-item" @click="handleAbateShow">
          <mt-icon name="icon_solid_edit" />
          <span>{{ $t('还价') }}</span>
        </div>
      </div>
      <div class="right-panel">
        <div class="option-item">
          <div class="select-round">
            <div class="strategy-common-label">{{ $t('当前轮次:') }}</div>
            <div class="strategy-common-container">
              <mt-select
                css-class="strategy-element"
                v-model="activeRound.id"
                :data-source="getTurnsList"
                :fields="{ value: 'id', text: 'label' }"
                @select="handleChangeRound"
              ></mt-select>
            </div>
          </div>
        </div>
        <div class="svg-option-item" @click="resetListView" v-if="activeSupplier || activePackage">
          <mt-icon name="icon_solid_edit" />
          <span>{{ $t('切换列表') }}</span>
        </div>
      </div>
    </div>
    <div class="content mt-flex">
      <list-view
        ref="listViewRef"
        @handleChooseListData="handleChooseListData"
        :active-round="activeRound"
        v-if="!activeSupplier && !activePackage"
      ></list-view>
      <package-view
        ref="packageViewRef"
        :active-round="activeRound"
        :active-package="activePackage"
        v-if="activePackage"
      ></package-view>
      <supply-view
        ref="supplyViewRef"
        :active-round="activeRound"
        :active-supplier="activeSupplier"
        v-if="activeSupplier"
      ></supply-view>
    </div>
  </div>
</template>
<script>
export default {
  components: {
    packageView: () =>
      import(
        /* webpackChunkName: "router/purchase/rfx/detail/tabs/inquiry-bidding/details/package" */ './packageView/index.vue'
      ),
    supplyView: () =>
      import(
        /* webpackChunkName: "router/purchase/rfx/detail/tabs/inquiry-bidding/details/supply" */ './supplyView/index.vue'
      ),
    listView: () =>
      import(
        /* webpackChunkName: "router/purchase/rfx/detail/tabs/inquiry-bidding/details/list" */ './listView/index.vue'
      )
  },
  props: {
    turnsList: {
      type: Array,
      default: () => {
        return []
      }
    },
    currentTurnInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      activeRound: this.currentTurnInfo, //当前轮次
      activeSupplier: null, //当前选择的供应商
      activePackage: null //当前选择的物料信息
    }
  },
  computed: {
    getTurnsList() {
      return this.turnsList
    }
  },
  mounted() {},
  methods: {
    handleChangeRound(e) {
      let round = e?.itemData
      this.activeRound = round
      this.$emit('offerRoundSelect', round)
    },
    //列表模式下--点击单元格数据
    handleChooseListData(e) {
      if (e.type === 'package') {
        this.activeSupplier = null
        this.activePackage = e?.data?.packageId
      } else if (e.type === 'supply') {
        this.activeSupplier = e?.data?.rfxBidding?.supplierId
        this.activePackage = null
      }
    },
    //切换回列表模式
    resetListView() {
      this.activeSupplier = null
      this.activePackage = null
    },
    // 结束当前轮次
    handleFinishCurrentTurn() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t("确认执行'结束当前轮次'？")
          // confirm: () => {},
        },
        success: () => {
          const query = { id: this.$route.query.rfxId }
          this.$store.commit('startLoading')
          this.$API.inquiryBidding.currentTurnFinish(query).then(() => {
            this.$store.commit('endLoading')
            this.$toast({
              content: this.$t("'结束当前轮次'，操作成功"),
              type: 'success'
            })
            this.refreshCurrentTabGridData()
          })
        }
      })
    },
    // 结束所有轮次
    handleFinishAllTurn() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t("确认执行'结束所有轮次'？")
          // confirm: () => {},
        },
        success: () => {
          const query = { id: this.$route.query.rfxId }
          this.$store.commit('startLoading')
          this.$API.inquiryBidding.currentTurnFinish(query).then(() => {
            this.$store.commit('endLoading')
            this.$toast({
              content: this.$t("'结束所有轮次'，操作成功"),
              type: 'success'
            })
            this.refreshCurrentTabGridData()
          })
        }
      })
    },
    // 开启下一轮
    handleStartNextTurn() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t("确认执行'开启下一轮'？")
          // confirm: () => {},
        },
        success: () => {
          const query = { rfxId: this.$route.query.rfxId }
          this.$store.commit('startLoading')
          this.$API.inquiryBidding.nextTurnStart(query).then(() => {
            this.$store.commit('endLoading')
            this.$toast({
              content: this.$t("'开启下一轮'，操作成功"),
              type: 'success'
            })
            this.$refs.selectRoundRef.getTurns()
            this.$refs.selectRoundRef.getCurrentTurnsInfo()
            this.refreshCurrentTabGridData()
          })
        }
      })
    },
    //发布操作
    handleSendOutBidding() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t("确认执行'发布'？")
        },
        success: () => {
          const query = { id: this.$route.query.rfxId }
          this.$store.commit('startLoading')
          this.$API.inquiryBidding.sendOut(query).then(() => {
            this.$store.commit('endLoading')
            this.$toast({
              content: this.$t('发布成功'),
              type: 'success'
            })
            this.refreshCurrentTabGridData()
          })
        }
      })
    },
    //接收报价操作
    handleAcceptBidding() {
      const _currentTabRef = this.getCurrentTabGridRef()
      const _selectRecords = _currentTabRef?.grid?.getSelectedRecords()
      if (_selectRecords.length > 0) {
        const ids = _selectRecords.map((e) => e.id)
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t("确认执行'接收报价'？")
          },
          success: () => {
            let _query = {
              rfxId: this.$route.query.rfxId,
              rfxBiddingItemIds: ids
            }
            this.$store.commit('startLoading')
            this.$API.inquiryBidding.doRfxBiddingAccept(_query).then((res) => {
              console.log('doRfxBiddingAccept----', res)
              this.$store.commit('endLoading')
              this.$toast({
                content: this.$t("'接收报价'，操作成功"),
                type: 'success'
              })
              this.refreshCurrentTabGridData()
            })
          }
        })
      } else {
        this.$toast({
          type: 'warning',
          content: this.$t('先勾选，再执行该操作！')
        })
        return false
      }
    },
    // 退回报价
    handleRejectQuote() {
      const _currentTabRef = this.getCurrentTabGridRef()
      const _selectRecords = _currentTabRef?.grid?.getSelectedRecords()
      if (_selectRecords.length < 1) {
        this.$toast({
          type: 'warning',
          content: this.$t('先勾选，再执行该操作！')
        })
        return false
      } else {
        this.handleRejectOption(_selectRecords)
      }
    },
    //执行退回报价
    handleRejectOption(_records) {
      const ids = _records.map((s) => s.id)
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/rfx/detail/tabs/inquiry-bidding/details/components/rejectQuote" */ '../components/rejectQuote.vue'
          ),
        data: {
          title: this.$t('退回报价'),
          data: { rfxId: this.$route.query.rfxId, rfxBiddingItemIds: ids }
        },
        success: () => {
          this.refreshCurrentTabGridData()
        }
      })
    },
    //还价操作
    handleAbateShow() {
      const _currentTabRef = this.getCurrentTabGridRef()
      const _selectRecords = _currentTabRef?.grid?.getSelectedRecords()
      if (_selectRecords.length < 1) {
        this.$toast({
          type: 'warning',
          content: this.$t('先勾选，再执行该操作！')
        })
        return false
      } else if (_selectRecords.length > 1) {
        this.$toast({
          type: 'warning',
          content: this.$t('仅支持操作单条数据！')
        })
        return false
      } else {
        this.handleAbateOption(_selectRecords[0])
      }
    },
    //执行还价操作
    handleAbateOption(data) {
      if (data?.id) {
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "router/purchase/rfx/detail/tabs/inquiry-bidding/details/components/abateDialog" */ './components/abateDialog.vue'
            ),
          data: {
            title: this.$t('还价'),
            data: data
          },
          success: () => {
            this.refreshCurrentTabGridData()
          }
        })
      }
    },
    //获取当前Active状态下的Grid对象
    getCurrentTabGridRef() {
      let _parentRef = this.getCurrentParentRef()
      return _parentRef?.$refs?.templateRef?.getCurrentTabRef()
    },
    //刷新Grid数据
    refreshCurrentTabGridData() {
      let _parentRef = this.getCurrentParentRef()
      _parentRef?.$refs?.templateRef?.refreshCurrentGridData()
    },
    //根据当前activeSupplier、activePackage获取当前grid父级容器
    getCurrentParentRef() {
      let _parentRef = null
      if (!this.activeSupplier && !this.activePackage) {
        //listView
        _parentRef = this.$refs.listViewRef
      } else if (this.activePackage) {
        //packageView
        _parentRef = this.$refs.packageViewRef
      } else if (this.activeSupplier) {
        //supplyView
        _parentRef = this.$refs.supplyViewRef
      }
      return _parentRef
    }
  }
}
</script>
<style>
.m-mt-md {
  margin-top: 8px;
}
</style>
<style lang="scss" scoped>
.detail-container {
  .detail-toolbar {
    height: 50px;
    flex-shrink: 0;
    box-shadow: inset 0 -1px 0 0 rgba(237, 239, 243, 1);
    padding: 0 20px;
    align-items: center;
    justify-content: space-between;
    .left-panel {
      .svg-option-item {
        &:not(:first-of-type) {
          margin-left: 20px;
        }
        &.custom-split {
          margin-left: 75px;
        }
      }
    }
    .right-panel {
      display: flex;
      .option-item {
        display: inline-block;
        .strategy-common-label {
          width: 70px;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          color: #465b73;
          text-align: right;
          display: inline-block;
          margin-right: 20px;
          position: relative;
        }
        .strategy-common-container {
          width: 120px;
          display: inline-block;
          height: 40px;
          background: #fafafa;
          border: 1px solid #e8e8e8;
          border-radius: 4px;
          padding: 0 20px;
        }
        /deep/ .strategy-element {
          border: none;
          border-color: transparent !important;
          position: relative;
          top: 4px;
          &:before,
          &:after {
            display: none;
          }
          .e-float-line {
            display: none;
          }
          .e-control {
            color: #292929;
            border: none;
            border-color: transparent !important;
          }
        }
        .select-round {
          display: inline-block;
          .strategy-common-label {
            width: 70px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: 500;
            color: #465b73;
            text-align: right;
            display: inline-block;
            margin-right: 20px;
            position: relative;
          }
          .strategy-common-container {
            width: 120px;
            display: inline-block;
            height: 40px;
            background: #fafafa;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            padding: 0 20px;
          }
          /deep/ .strategy-element {
            border: none;
            border-color: transparent !important;
            position: relative;
            top: 4px;
            &:before,
            &:after {
              display: none;
            }
            .e-float-line {
              display: none;
            }
            .e-control {
              color: #292929;
              border: none;
              border-color: transparent !important;
            }
          }
        }
      }
      .svg-option-item {
        margin-left: 20px;
      }
    }
  }
}
</style>
