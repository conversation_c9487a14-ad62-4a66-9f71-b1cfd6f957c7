<template>
  <div class="content-container mt-flex">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>
<script>
import { columnData } from './config'
import MixIn from '../../config/mixin'
export default {
  mixins: [MixIn],
  data() {
    return {
      biddingListAPI: this.$API.inquiryBidding.getRfxBiddingAcceptItem,
      pageConfig: [
        {
          toolbar: [],
          grid: {
            allowFiltering: true,
            columnData,
            dataSource: [],
            rowDataBound: (args) => {
              if (args.data['bestFlag'] == 1) {
                args.row.classList.add('top-left-tag')
              }
            }
          }
        }
      ]
    }
  },
  mounted() {
    this.resetAsyncConfigParams({})
  },
  methods: {
    handleClickCellTitle(e) {
      if (e.field == 'packageId' || e.field == 'packageName') {
        this.$emit('handleChooseListData', { type: 'package', data: e.data })
      } else if (e.field == 'rfxBidding.supplierCode' || e.field == 'rfxBidding.supplierName') {
        this.$emit('handleChooseListData', { type: 'supply', data: e.data })
      } else if (e.field == 'bidTimes') {
        if (e.data['bidTimes'] > 0) {
          this.handleShowHistoryDialog(e)
        }
      } else if (e.field == 'stages') {
        this.handleShowStagesDialog(e)
      }
    }
  }
}
</script>
