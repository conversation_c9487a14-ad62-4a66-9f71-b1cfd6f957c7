import { i18n } from '@/main.js'
export default {
  methods: {
    handleClickCellTitle(e) {
      if (e.field == 'bidTimes') {
        if (e.data['bidTimes'] > 0) {
          this.handleShowHistoryDialog(e)
        }
      } else if (e.field == 'stages') {
        this.handleShowStagesDialog(e)
      }
    },
    //报价历史弹框
    handleShowHistoryDialog(e) {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/rfx/detail/tabs/inquiry-bidding/components/offerHistory" */ '../components/offerHistory/index.vue'
          ),
        data: {
          title: i18n.t('查看历史'),
          data: {
            packageId: e.data.packageId,
            roundNo: this.currentRound?.roundNo, //	轮次
            supplierId: e.data.rfxBidding.supplierId,
            rfxId: this.$route.query.rfxId
          }
        }
      })
    },
    //阶梯报价弹框
    handleShowStagesDialog(e) {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/rfx/detail/tabs/inquiry-bidding/components/offerStages" */ '../components/offerStages/index.vue'
          ),
        data: {
          title: i18n.t('阶梯报价'),
          data: e.data
        }
      })
    },
    //列表参数重新赋值
    resetAsyncConfigParams({ roundNo, supplierId, packageId }) {
      let params = { rfxId: this.$route.query.rfxId }
      if (roundNo) params.roundNo = roundNo
      if (packageId) params.packageId = packageId
      if (supplierId) params.supplierId = supplierId
      this.$set(this.pageConfig[0].grid, 'asyncConfig', {
        url: this.biddingListAPI,
        queryBuilderWrap: 'queryBuilderDTO',
        params
      })
    }
  }
}
