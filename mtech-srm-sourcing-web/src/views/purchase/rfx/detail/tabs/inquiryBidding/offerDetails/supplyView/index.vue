<template>
  <div class="content-container mt-flex">
    <div class="content-card-panel">
      <div
        :class="[
          'content-card supply-card',
          'mt-flex-direction-column',
          { active: currentSupplier == item.supplierId }
        ]"
        @click="changeCurrentPackage(item)"
        v-for="(item, index) in rfxItemSupplierList"
        :key="index"
      >
        <div class="content-panel mt-flex">
          <div class="content-item">
            <div class="field-title">{{ item.supplierCode }}</div>
            <div class="field-value">{{ item.supplierName }}</div>
          </div>
          <div class="content-item">
            <div class="field-title">{{ item.packageCount }}</div>
            <div class="field-value">{{ $t('物料行数') }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="content-table-panel">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickCellTitle="handleClickCellTitle"
      />
    </div>
  </div>
</template>
<script>
import { columnData } from './config'
import MixIn from '../../config/mixin'
export default {
  props: {
    activeRound: {
      type: Object,
      default: () => {
        return {}
      }
    },
    activeSupplier: {
      type: String,
      default: ''
    }
  },
  mixins: [MixIn],
  data() {
    return {
      currentRound: this.activeRound, //当前轮次
      currentSupplier: this.activeSupplier, //当前选择的供应商
      rfxItemSupplierList: [],
      biddingListAPI: this.$API.inquiryBidding.getRfxBiddingNewestItem,
      pageConfig: [
        {
          toolbar: [],
          grid: { allowFiltering: true, columnData, dataSource: [] }
        }
      ]
    }
  },
  mounted() {
    this.getRfxItemSuppliers() //获取左侧供应商列表
  },
  methods: {
    //获取左侧供应商列表
    getRfxItemSuppliers() {
      let _query = {
        queryBuilderDTO: {
          page: {
            current: 1,
            size: 100
          }
        },
        rfxId: this.$route.query.rfxId
        // roundId: this.currentRound?.id,
        // roundNo: this.currentRound?.roundNo,
      }
      this.$API.inquiryBidding.getRfxItemSupplierList(_query).then((res) => {
        this.$set(this, 'rfxItemSupplierList', res?.data?.records || [])
      })
    },
    //切换当前选中的询价包
    changeCurrentPackage(item) {
      if (this.currentSupplier !== item.supplierId) {
        this.currentSupplier = item.supplierId
        this.resetAsyncConfigParams({
          roundNo: this?.currentRound?.roundNo,
          supplierId: this.currentSupplier
        })
      }
    }
  },
  watch: {
    activeRound: {
      handler(n) {
        if (n?.roundNo) {
          this.currentRound = n
          this.resetAsyncConfigParams({
            roundNo: n?.roundNo,
            supplierId: this.currentSupplier
          })
        }
      },
      immediate: true,
      deep: true
    }
  }
}
</script>
