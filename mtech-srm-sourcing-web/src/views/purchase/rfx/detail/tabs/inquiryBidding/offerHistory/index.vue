<template>
  <div class="full-height history-container mt-flex-direction-column">
    <div class="detail-toolbar mt-flex">
      <div class="left-panel mt-flex">
        <div class="svg-option-item" @click="handleRejectQuote">
          <mt-icon name="icon_Auction" />
          <span>{{ $t('退回报价') }}</span>
        </div>
        <div class="svg-option-item" @click="handleSetBestPriceFlag">
          <mt-icon name="icon_Auction" />
          <span>{{ $t('标记最优') }}</span>
        </div>
        <div class="svg-option-item" @click="handleClearBestPriceFlag">
          <mt-icon name="icon_Auction" />
          <span>{{ $t('取消标记') }}</span>
        </div>
        <div class="svg-option-item" @click="handleCompleteAllPriceFlag">
          <mt-icon name="icon_solid_edit" />
          <span>{{ $t('提交全部数据') }}</span>
        </div>
      </div>
      <div class="right-panel">
        <div class="svg-option-item" @click="resetListView" v-if="activeSupplier || activePackage">
          <mt-icon name="icon_solid_edit" />
          <span>{{ $t('切换列表') }}</span>
        </div>
      </div>
    </div>
    <div class="content mt-flex">
      <list-view
        ref="listViewRef"
        @handleChooseListData="handleChooseListData"
        v-if="!activeSupplier && !activePackage"
      ></list-view>
      <package-view
        ref="packageViewRef"
        :active-package="activePackage"
        v-if="activePackage"
      ></package-view>
      <supply-view
        ref="supplyViewRef"
        :active-supplier="activeSupplier"
        v-if="activeSupplier"
      ></supply-view>
    </div>
  </div>
</template>
<script>
export default {
  components: {
    packageView: () =>
      import(
        /* webpackChunkName: "router/purchase/rfx/detail/tabs/inquiry-bidding/history/package" */ './packageView/index.vue'
      ),
    supplyView: () =>
      import(
        /* webpackChunkName: "router/purchase/rfx/detail/tabs/inquiry-bidding/history/supply" */ './supplyView/index.vue'
      ),
    listView: () =>
      import(
        /* webpackChunkName: "router/purchase/rfx/detail/tabs/inquiry-bidding/history/list" */ './listView/index.vue'
      )
  },
  props: {
    currentTurnInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      activeRound: this.currentTurnInfo, //当前轮次
      activeSupplier: null, //当前选择的供应商
      activePackage: null //当前选择的物料信息
    }
  },
  mounted() {},
  methods: {
    //列表模式下--点击单元格数据
    handleChooseListData(e) {
      if (e.type === 'package') {
        this.activeSupplier = null
        this.activePackage = e?.data?.packageId
      } else if (e.type === 'supply') {
        this.activeSupplier = e?.data?.rfxBidding?.supplierId
        this.activePackage = null
      }
    },
    //切换回列表模式
    resetListView() {
      this.activeSupplier = null
      this.activePackage = null
    },
    // 退回报价
    handleRejectQuote() {
      const _currentTabRef = this.getCurrentTabGridRef()
      const _selectRecords = _currentTabRef?.grid?.getSelectedRecords()
      if (_selectRecords.length < 1) {
        this.$toast({
          type: 'warning',
          content: this.$t('先勾选，再执行该操作！')
        })
        return false
      } else {
        this.handleRejectOption(_selectRecords)
      }
    },
    //执行退回报价
    handleRejectOption(_records) {
      const ids = _records.map((s) => s.id)
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/rfx/detail/tabs/inquiry-bidding/rejectQuote" */ '../components/rejectQuote.vue'
          ),
        data: {
          title: this.$t('退回报价'),
          data: { rfxId: this.$route.query.rfxId, rfxBiddingItemIds: ids }
        },
        success: () => {
          this.refreshCurrentTabGridData()
        }
      })
    },
    //标记最优操作
    handleSetBestPriceFlag() {
      const _currentTabRef = this.getCurrentTabGridRef()
      let _selectRecords = _currentTabRef.grid.getSelectedRecords()
      if (_selectRecords.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      } else {
        let _alreadySetBestList = _selectRecords.filter((e) => {
          return e.bestFlag == 1
        })
        if (_alreadySetBestList.length > 0) {
          this.$toast({
            content: this.$t('您勾选的数据中，已存在最优标记'),
            type: 'warning'
          })
          return
        }
      }
      let _ids = []
      _selectRecords.forEach((e) => {
        _ids.push(e.id)
      })
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t("确认将勾选的数据设置为'最优'？")
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.inquiryBidding
            .setRfxBiddingBestPriceFlag(this.$route.query.rfxId, _ids)
            .then(() => {
              this.$store.commit('endLoading')
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
              this.refreshCurrentTabGridData()
            })
        }
      })
    },
    //取消提交标记
    handleClearBestPriceFlag() {
      const _currentTabRef = this.getCurrentTabGridRef()
      let _selectRecords = _currentTabRef.grid.getSelectedRecords()
      if (_selectRecords.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      } else {
        let _alreadySetBestList = _selectRecords.filter((e) => {
          return e.bestFlag == 1
        })
        if (_alreadySetBestList.length < _selectRecords.length) {
          this.$toast({
            content: this.$t("您勾选的数据中，存在'非最优标记'数据"),
            type: 'warning'
          })
          return
        }
      }
      let _ids = []
      _selectRecords.forEach((e) => {
        _ids.push(e.id)
      })
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t("确认取消'最优'标记？")
          // confirm: () => {},
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.inquiryBidding
            .clearRfxBiddingBestPriceFlag(this.$route.query.rfxId, _ids)
            .then(() => {
              this.$store.commit('endLoading')
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
              this.refreshCurrentTabGridData()
            })
        }
      })
    },
    //提交全部数据
    handleCompleteAllPriceFlag() {
      const _currentTabRef = this.getCurrentTabGridRef()
      let _selectRecords = _currentTabRef.grid.getSelectedRecords()
      if (_selectRecords.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _ids = []
      _selectRecords.forEach((e) => {
        _ids.push(e.id)
      })
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t("确认执行'提交全部数据'？")
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.inquiryBidding
            .doSubmitAllRfxFlag(this.$route.query.rfxId, this.activeRound?.id, _ids)
            .then(() => {
              this.$store.commit('endLoading')
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
              this.refreshCurrentTabGridData()
            })
        }
      })
    },
    //获取当前Active状态下的Grid对象
    getCurrentTabGridRef() {
      let _parentRef = this.getCurrentParentRef()
      return _parentRef?.$refs?.templateRef?.getCurrentTabRef()
    },
    //刷新Grid数据
    refreshCurrentTabGridData() {
      let _parentRef = this.getCurrentParentRef()
      _parentRef?.$refs?.templateRef?.refreshCurrentGridData()
    },
    //根据当前activeSupplier、activePackage获取当前grid父级容器
    getCurrentParentRef() {
      let _parentRef = null
      if (!this.activeSupplier && !this.activePackage) {
        //listView
        _parentRef = this.$refs.listViewRef
      } else if (this.activePackage) {
        //packageView
        _parentRef = this.$refs.packageViewRef
      } else if (this.activeSupplier) {
        //supplyView
        _parentRef = this.$refs.supplyViewRef
      }
      return _parentRef
    }
  }
}
</script>
<style lang="scss" scoped>
.history-container {
  .detail-toolbar {
    height: 50px;
    flex-shrink: 0;
    box-shadow: inset 0 -1px 0 0 rgba(237, 239, 243, 1);
    padding: 0 20px;
    align-items: center;
    justify-content: space-between;
    .left-panel {
      .svg-option-item {
        &:not(:first-of-type) {
          margin-left: 20px;
        }
        &.custom-split {
          margin-left: 75px;
        }
      }
    }
  }
}
</style>
