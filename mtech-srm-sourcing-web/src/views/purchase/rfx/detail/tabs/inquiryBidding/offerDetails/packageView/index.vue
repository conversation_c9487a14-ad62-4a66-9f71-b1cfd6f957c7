<template>
  <div class="content-container mt-flex">
    <div class="content-card-panel">
      <div
        :class="[
          'content-card package-card',
          'mt-flex-direction-column',
          { active: currentPackage == item.packageId }
        ]"
        @click="changeCurrentPackage(item)"
        v-for="(item, index) in rfxItemPackageList"
        :key="index"
      >
        <div class="content-panel mt-flex">
          <div class="content-item">
            <div class="field-title">{{ item.itemCode }}</div>
            <div class="field-value">{{ $t('描述信息') }}</div>
          </div>
          <div class="content-item">
            <div class="field-title">{{ $t('产品名称') }}</div>
            <div class="field-value value-link">{{ item.itemName }}</div>
          </div>
        </div>
        <div class="content-panel mt-flex">
          <div class="content-item">
            <div class="field-title">{{ item.bidQuantity }}</div>
            <div class="field-value">{{ $t('采购数量') }}</div>
          </div>
          <div class="content-item">
            <div class="field-title">{{ item.bidUnitId }}</div>
            <div class="field-value">{{ $t('采购预估价/单位') }}</div>
          </div>
          <div class="content-item">
            <div class="field-title">{{ $t('公司名称') }}</div>
            <div class="field-value">{{ item.companyName }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="content-table-panel">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickCellTitle="handleClickCellTitle"
      />
    </div>
  </div>
</template>
<script>
import { columnData } from './config'
import MixIn from '../../config/mixin'
export default {
  props: {
    activeRound: {
      type: Object,
      default: () => {
        return {}
      }
    },
    activePackage: {
      type: String,
      default: ''
    }
  },
  mixins: [MixIn],
  data() {
    return {
      currentRound: this.activeRound,
      currentPackage: this.activePackage, //当前选择的物料信息
      rfxItemPackageList: [],
      biddingListAPI: this.$API.inquiryBidding.getRfxBiddingNewestItem,
      pageConfig: [
        {
          toolbar: [],
          grid: { allowFiltering: true, columnData, dataSource: [] }
        }
      ]
    }
  },
  mounted() {
    this.getRfxItemPackages() //获取左侧物料信息列表
  },
  methods: {
    //获取左侧物料信息列表
    getRfxItemPackages() {
      let _query = {
        rfxId: this.$route.query.rfxId
      }
      this.$API.inquiryBidding.getRfxItemPackageByRfxId(_query).then((res) => {
        let _list = []
        if (Array.isArray(res?.data)) {
          res.data.forEach((d) => {
            if (Array.isArray(d['rfxItems'])) {
              d['rfxItems'].forEach((e) => {
                _list.push(e)
              })
            }
          })
        }
        this.$set(this, 'rfxItemPackageList', _list)
      })
    },
    //切换当前选中的询价包
    changeCurrentPackage(item) {
      if (this.currentPackage !== item.packageId) {
        this.currentPackage = item.packageId
        this.resetAsyncConfigParams({
          roundNo: this?.currentRound?.roundNo,
          packageId: this.currentPackage
        })
      }
    }
  },
  watch: {
    activeRound: {
      handler(n) {
        if (n?.roundNo) {
          this.currentRound = n
          this.resetAsyncConfigParams({
            roundNo: n?.roundNo,
            packageId: this.currentPackage
          })
        }
      },
      immediate: true,
      deep: true
    }
  }
}
</script>
