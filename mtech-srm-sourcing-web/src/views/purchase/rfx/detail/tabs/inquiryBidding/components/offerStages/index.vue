<template>
  <mt-dialog
    ref="dialog"
    css-class="choose-supplier-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <div class="stage-title">
        {{ $t('阶梯报价方式') }}
        <span class="stage-type">{{ stageStatus }}</span>
      </div>
      <mt-template-page ref="templateRef" :template-config="pageConfig" />
    </div>
  </mt-dialog>
</template>

<script>
import { columnData } from './config'
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false,
          grid: { allowFiltering: true, columnData, dataSource: [] }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      this.$set(this.pageConfig[0].grid, 'dataSource', this.modalData.data.stages)
      return this.modalData.title
    },
    stageStatus() {
      return this.modalData.data.stageType === 0 ? this.$t('按数据') : this.$t('按时间')
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    confirm() {
      this.$emit('confirm-function')
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.choose-supplier-dialog {
  .dialog-content {
    padding-top: 18px;
    font-size: 16px;
  }

  .stage-title {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
    padding: 0 0 20px;
    .stage-type {
      background: rgba(237, 161, 51, 0.1);
      border-radius: 2px;
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: #eda133;
      padding: 2px 5px;
      margin-left: 6px;
    }
  }
}
</style>
