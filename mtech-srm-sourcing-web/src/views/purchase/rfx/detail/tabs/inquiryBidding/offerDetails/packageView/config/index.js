import { i18n } from '@/main.js'
import { sampleColumn, valueConverter } from '../../../config'
export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'priceStatus',
    headerText: i18n.t('状态'),
    valueConverter
  },
  {
    field: 'rfxBidding.supplierCode',
    headerText: i18n.t('供应商编号')
  },
  {
    field: 'rfxBidding.supplierName',
    headerText: i18n.t('供应商名称')
  }
].concat(sampleColumn)
