<template>
  <mt-dialog
    ref="dialog"
    css-class="small-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="row">
      <div class="col-12">
        <div class="title-box">{{ $t('退回原因') }}</div>
        <mt-input
          v-model="rejectForm.denyReason"
          type="text"
          :multiline="true"
          :rows="3"
          :placeholder="$t('请输入退回原因')"
        ></mt-input>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      rejectForm: {
        denyReason: '',
        rfxId: '',
        rfxBiddingItemIds: []
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this?.modalData?.data) {
      let _data = this.modalData.data
      this.rejectForm = {
        denyReason: '',
        rfxId: _data.rfxId,
        rfxBiddingItemIds: _data.rfxBiddingItemIds
      }
    }
  },
  methods: {
    confirm() {
      this.$store.commit('startLoading')
      this.$API.inquiryBidding.quoteReject(this.rejectForm).then(() => {
        this.$store.commit('endLoading')
        this.$emit('confirm-function')
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
