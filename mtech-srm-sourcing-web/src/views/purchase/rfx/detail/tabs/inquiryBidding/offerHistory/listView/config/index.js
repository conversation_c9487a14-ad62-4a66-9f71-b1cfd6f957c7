import { i18n } from '@/main.js'
import { sampleColumn, valueConverter } from '../../../config'
export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'lineNo',
    headerText: i18n.t('行号')
  },
  {
    field: 'packageId',
    headerText: i18n.t('品项/物料编号'),
    cssClass: 'field-content'
  },
  {
    field: 'packageName',
    headerText: i18n.t('品项/物料名称'),
    cssClass: 'field-content'
  },
  {
    field: 'spec',
    headerText: i18n.t('品项/物料描述')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司')
  },
  {
    field: 'bidQuantity',
    headerText: i18n.t('采购数量')
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织')
  },
  {
    field: 'field1',
    headerText: i18n.t('采购预估价？')
  },
  {
    field: 'priceStatus',
    headerText: i18n.t('状态'),
    valueConverter
  },
  {
    field: 'rfxBidding.supplierCode',
    headerText: i18n.t('供应商编号'),
    cssClass: 'field-content'
  },
  {
    field: 'rfxBidding.supplierName',
    headerText: i18n.t('供应商名称'),
    cssClass: 'field-content'
  }
].concat(sampleColumn)
