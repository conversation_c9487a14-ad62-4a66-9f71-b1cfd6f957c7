<template>
  <div class="full-height inquiry-bidding-page mt-flex-direction-column">
    <div class="page-header mt-flex" v-if="alreadyOpen">
      <mt-tabs
        :tab-id="$utils.randomString()"
        :data-source="tabList"
        :selected-item="activeTab"
        @selected="selectTab"
      >
      </mt-tabs>
      <div class="header-right mt-flex">
        <div class="header-time mt-flex-direction-column">
          <span class="time">{{ currentTurnInfo.endTime }}</span>
          <span class="header-label">{{ $t('报价结束时间') }}</span>
        </div>
      </div>
    </div>
    <div class="page-content" v-if="alreadyOpen">
      <offer-details
        v-if="activeTab == 0"
        @offerRoundSelect="changeSelectRound"
        :turns-list="turnsList"
        :current-turn-info="currentTurnInfo"
      ></offer-details>
      <offer-history
        v-if="activeTab == 1"
        :current-turn-info="currentTurnInfo"
        @offerRoundSelect="changeSelectRound"
      ></offer-history>
    </div>

    <mt-dialog
      ref="dialog"
      css-class="small-dialog"
      :buttons="buttons"
      :header="header"
      @beforeClose="biddingCancel"
    >
      <div class="row" v-if="checkShowBidPwd">
        <div class="col-12">
          <div class="title-box">{{ $t('开标密码') }}</div>
          <mt-input
            v-model="openForm.openPwd"
            type="text"
            :placeholder="$t('请输入开标密码')"
          ></mt-input>
        </div>
      </div>
    </mt-dialog>
    <!-- 未开标 -->
    <div class="biding-open-mask mt-flex" v-if="!alreadyOpen">
      <div class="select-round">
        <div class="strategy-common-label">{{ $t('当前轮次') }}:</div>
        <div class="strategy-common-container">
          <mt-select
            css-class="strategy-element"
            v-model="roundId"
            :data-source="turnsList"
            :fields="{ value: 'id', text: 'label' }"
            @select="handleSelect"
          ></mt-select>
        </div>
      </div>
      <!-- 未达到条件 -->
      <div class="not-allowed-open" v-if="!allowedOpen">
        <!-- 当前时开标人 -->
        <span class="open-desc" v-if="isOpenUser">{{ $t('未达到开标条件，请稍后再试！') }}</span>
        <!-- 当前不是开标人 -->
        <span class="open-desc" v-else>{{ $t('开标条件需要开标人才能开标！') }}</span>
      </div>
      <!-- 可以开标 -->
      <div class="allowed-open" v-if="allowedOpen" @click="biddingOpen"></div>
    </div>
  </div>
</template>
<script>
import utils from '@/utils/utils'
export default {
  components: {
    OfferDetails: () =>
      import(
        /* webpackChunkName: "router/purchase/rfx/detail/tabs/inquiry-bidding/details" */ './offerDetails/index.vue'
      ),
    OfferHistory: () =>
      import(
        /* webpackChunkName: "router/purchase/rfx/detail/tabs/inquiry-bidding/history" */ './offerHistory/index.vue'
      )
  },
  data() {
    return {
      roundId: null,
      turnsList: [], // 轮次列表
      currentTurnInfo: {}, // 当前轮次信息
      strategyConfig: {
        openBiddingFlag: 0,
        openBiddingConditionFlag: 0,
        openBiddingUserFlag: 0
      },
      activeTab: 0,
      tabList: [
        {
          header: { text: this.$t('报价详情') }
        },
        {
          header: { text: this.$t('接收历史') }
        }
      ],
      openForm: {
        openPwd: '',
        rfxRoundId: '',
        rfxId: ''
      },
      buttons: [
        {
          click: this.biddingCancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.biddingSave,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  computed: {
    header() {
      let _smallHeader = this.$t('开标')
      return _smallHeader
    },
    alreadyOpen() {
      //openBiddingFlag  开标标志 默认为未开标0, 已开标1
      return this.strategyConfig?.openBiddingFlag > 0
    },
    allowedOpen() {
      //openBiddingConditionFlag  是否满足开标条件 0 否 1 是
      return (
        this.strategyConfig?.openBiddingFlag > 0 &&
        this.strategyConfig?.openBiddingConditionFlag > 0
      )
    },
    isOpenUser() {
      //openBiddingUserFlag  是否开标人 0 否 1 是
      return (
        this.strategyConfig?.openBiddingFlag > 0 && this.strategyConfig?.openBiddingUserFlag > 0
      )
    },
    checkShowBidPwd() {
      return this.strategyConfig?.strategyConfig?.bidOpening?.bidOpenPwd
    }
  },
  mounted() {
    this.getTurns()
    this.getCurrentTurnsInfo()
  },
  methods: {
    selectTab(e) {
      this.activeTab = e.selectedIndex
    },
    biddingOpen() {
      this.openForm = {
        openPwd: '',
        rfxRoundId: this.currentTurnInfo.id,
        rfxId: this.$route.query.rfxId
      }
      this.$refs.dialog.ejsRef.show()
    },
    biddingSave() {
      let _info = { ...this.openForm }
      if (!this.strategyConfig.strategyConfig.bidOpening.bidOpenPwd) {
        delete _info.openPwd
      }
      this.$API.inquiryBidding.biddingOpen(this.openForm).then(() => {
        this.$refs.dialog.ejsRef.hide()
      })
    },
    biddingCancel() {
      this.$refs.dialog.ejsRef.hide()
    },
    //通过rfxRd、roundId获取当前RFX使用的策略信息
    getCurrentRFXStrategInfo() {
      let _query = {
        rfxId: this.$route.query.rfxId,
        roundId: this.currentTurnInfo.id
      }
      this.$store.commit('startLoading')
      this.$API.strategyConfig
        .queryByRfxIdAndRoundId(_query)
        .then((res) => {
          this.$store.commit('endLoading')
          if (res && res.data) {
            this.strategyConfig = res.data
          } else {
            //如果没有查到strategy信息，重置开标状态。
            this.strategyConfig = {
              openBiddingFlag: 1,
              openBiddingConditionFlag: 1,
              openBiddingUserFlag: 1
            }
          }
        })
        .catch(() => {
          //如果没有查到strategy信息，重置开标状态。
          this.strategyConfig = {
            openBiddingFlag: 1,
            openBiddingConditionFlag: 1,
            openBiddingUserFlag: 1
          }
        })
    },
    changeSelectRound(round) {
      if (round?.id !== this.currentTurnInfo?.id) {
        this.currentTurnInfo = round
        this.roundId = round.id
        if (this.currentTurnInfo.id) {
          //存在roundId
          this.getCurrentRFXStrategInfo()
        }
      }
    },
    getTurns() {
      this.$API.rfxDetail
        .getTurnList({
          rfxId: this.$route.query.rfxId
        })
        .then((res) => {
          this.turnsList = res.data.map((item) => {
            return {
              ...item,
              label: this.$t('第') + utils.toChinesNum(item.roundNo) + this.$t('轮')
            }
          })
        })
    },

    getCurrentTurnsInfo() {
      this.$API.rfxDetail
        .getCurrentTurn({
          rfxId: this.$route.query.rfxId
        })
        .then((res) => {
          this.currentTurnInfo = res.data
          this.roundId = res.data.id
          if (this.currentTurnInfo.id) {
            this.getCurrentRFXStrategInfo()
          }
        })
    },

    handleSelect(e) {
      let round = e.itemData
      this.currentTurnInfo = round
      if (this.currentTurnInfo.id) {
        //存在roundId
        this.getCurrentRFXStrategInfo()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.inquiry-bidding-page {
  width: 100%;
  background: #fafafa;
  .biding-open-mask {
    background: #fff;
    margin-top: 20px;
    height: 100%;
    width: 100%;
    min-height: 700px;
    min-width: 700px;
    border: 1px solid rgba(232, 232, 232, 1);
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    align-items: center;
    justify-content: center;
    .select-round {
      position: absolute;
      top: 20px;
      right: 20px;
    }
    .not-allowed-open {
      width: 280px;
      height: 311px;
      display: flex;
      align-items: flex-end;
      background: url('~@/assets/images/biding-open-not-allowed.png') center no-repeat;
      .open-desc {
        font-size: 20px;
        font-weight: 500;
        color: rgba(198, 199, 198, 1);
      }
    }
    .allowed-open {
      width: 700px;
      height: 700px;
      background: url('~@/assets/images/biding-open-allowed.png') center no-repeat;
    }
    .select-round {
      display: inline-block;
      .strategy-common-label {
        width: 70px;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 500;
        color: #465b73;
        text-align: right;
        display: inline-block;
        margin-right: 20px;
        position: relative;
      }
      .strategy-common-container {
        width: 120px;
        display: inline-block;
        height: 40px;
        background: #fafafa;
        border: 1px solid #e8e8e8;
        border-radius: 4px;
        padding: 0 20px;
      }
      /deep/ .strategy-element {
        border: none;
        border-color: transparent !important;
        position: relative;
        top: 4px;
        &:before,
        &:after {
          display: none;
        }
        .e-float-line {
          display: none;
        }
        .e-control {
          color: #292929;
          border: none;
          border-color: transparent !important;
        }
      }
    }
  }
  .page-header {
    background: #fafafa;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    height: 50px;
    /deep/ .mt-tabs {
      width: 100%;
      background: #fafafa;
      padding: 0;
      .e-tab .e-tab-header .e-toolbar-item.e-active .e-tab-text {
        font-weight: 600;
      }
      .e-toolbar-item {
        min-width: 100px;
        text-align: center;
      }
      .e-tab-header {
        background: #fafafa;
      }
    }
    .header-right {
      align-items: center;
      .header-time {
        width: 160px;
        justify-content: space-between;
        padding: 10px 0;
        text-align: right;
        .time {
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(35, 43, 57, 1);
        }
        .header-label {
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(70, 91, 115, 1);
        }
      }
      /deep/ .mt-button {
        button {
          margin-left: 20px;
          width: 68px;
          height: 34px;
          background: rgba(0, 70, 156, 0.06);
          border: 1px solid rgba(0, 70, 156, 0.1);
          border-radius: 2px;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(99, 134, 193, 1);
        }
      }
    }
  }
  .page-content {
    flex: 1;
    min-height: 0;
    /deep/ .grid-no-toolbar {
      padding-top: 20px;
    }
  }
  /deep/ .status-text {
    font-size: 12px;
    line-height: 1;
    padding: 4px;
    border-radius: 2px;
    &.isStatus {
      color: #54bf00;
      background: rgba(84, 191, 0, 0.1);
    }
    &.unStatus {
      color: #ed5633;
      background: rgba(237, 86, 51, 0.1);
    }
  }
  /deep/ .custom-td {
    position: absolute;
    left: -10px;
    top: 0;
    li {
      list-style: none;
      width: 197px;
      height: 50px;
      background: rgba(255, 255, 255, 1);
      line-height: 50px;
      position: relative;
      padding-left: 26px;
      box-shadow: inset 0 -1px 0 0 #e8e8e8;
      &:last-of-type {
        box-shadow: none;
      }
      &:hover {
        background: rgba(238, 238, 238, 1);
      }
    }
  }
  /deep/ .custom-price-td {
    position: relative;
    left: -10px;
    border-left: 1px solid #e8e8e8;
    .price-item {
      list-style: none;
      width: 197px;
      height: 50px;
      background: rgba(255, 255, 255, 1);
      line-height: 50px;
      position: relative;
      padding-left: 26px;
      div {
        box-shadow: inset 0 -1px 0 0 #e8e8e8;
      }
      &:hover {
        background: rgba(238, 238, 238, 1);
      }
      &:before {
        content: '';
        width: 6px;
        height: 6px;
        border: 1px solid #e8e8e8;
        position: absolute;
        left: 10px;
        top: calc(50% - 3px);
        background: #ffffff;
        z-index: 2;
      }
      &:after {
        content: '';
        width: 0;
        height: 100%;
        position: absolute;
        border-left: 1px solid #e8e8e8;
        left: 13px;
        top: 0;
        z-index: 1;
      }
      &:first-of-type {
        &:after {
          height: 50%;
          top: 50%;
          bottom: auto;
        }
      }
      &:last-of-type {
        div {
          box-shadow: none;
        }
        &:after {
          height: 50%;
          top: auto;
          bottom: 50%;
        }
      }
    }
    &.single-item {
      border-left: none;
      .price-item {
        &:after {
          display: none;
        }
      }
    }
  }
  /deep/ .e-templatecell {
    position: relative;
  }
  /deep/ .e-gridcontent {
    .e-content {
      overflow-y: auto;
    }
  }
  /deep/ .top-left-tag {
    td {
      &:first-of-type {
        padding-left: 5px;
        position: relative;
        &:before {
          content: '';
          height: 0;
          width: 0;
          border-right: 30px solid transparent;
          border-top: 30px solid #eda133;
          position: absolute;
          left: 0;
          top: 0;
        }
        &:after {
          content: '最优';
          height: 0;
          width: 0;
          font-size: 12px;
          transform: scale(0.8) rotate(-45deg);
          color: #ffffff;
          position: absolute;
          left: -2px;
          top: 11px;
          white-space: nowrap;
        }
      }
    }
  }
  /deep/.content {
    flex: 1;
    min-height: 0;
    background: #e8e8e8;
    .content-container {
      width: 100%;
      height: 100%;
      min-height: 0;
      overflow: auto;
    }
    .content-card-panel {
      width: 360px;
      background: #ffffff;
      flex-shrink: 0;
      padding: 20px;
      overflow: auto;
      box-shadow: inset -1px 0 0 0 rgba(232, 232, 232, 1);
      .content-card {
        width: 318px;
        margin-bottom: 20px;
        cursor: pointer;
        justify-content: space-between;
        background: rgba(255, 255, 255, 1);
        border: 1px solid rgba(232, 232, 232, 1);
        border-radius: 4px;
        box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
        padding: 10px 20px;
        &.package-card {
          height: 104px;
        }
        &.supply-card {
          height: 52px;
        }
        &:before {
          content: '';
          width: 80%;
          height: 0;
          left: 10%;
          top: 50%;
          position: absolute;
          border-top: 1px solid #e8e8e8;
        }
        &.active {
          background: #f5f6f9;
          border-left: none;
          position: relative;
          &:before {
            content: '';
            height: 100%;
            width: 0;
            position: absolute;
            border-left: 3px solid #00469c;
            border-radius: 4px 0 0 8px;
            left: 0;
            top: 0;
            z-index: 2;
            animation: active-animation 0.2s ease;
          }
          @keyframes active-animation {
            0% {
              top: 50%;
              height: 0;
            }
            100% {
              top: 0;
              height: 100%;
            }
          }
          &:after {
            content: '';
            width: 6px;
            height: 6px;
            background: 0 0;
            border-left: none;
            border-top: none;
            border-right: 1px solid #6386c1;
            border-bottom: 1px solid #6386c1;
            position: absolute;
            transform: rotate(-45deg);
            cursor: pointer;
            right: -10px;
            top: calc(50% - 3px);
          }
        }
        .content-panel {
          justify-content: space-between;
        }
        .content-item {
          .field-title {
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: 600;
            color: rgba(35, 43, 57, 1);
          }
          .field-value {
            margin-top: 6px;
            font-size: 12px;
            font-family: PingFangSC;
            font-weight: normal;
            color: rgba(154, 154, 154, 1);
            &.value-link {
              color: rgba(99, 134, 193, 1);
            }
          }
        }
      }
    }
    .content-table-panel {
      flex: 1;
      background: #ffffff;
      overflow: auto;
    }
  }
}
</style>
