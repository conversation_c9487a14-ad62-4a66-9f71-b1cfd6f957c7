import { i18n } from '@/main.js'
export const columnData = [
  {
    field: 'stageFrom',
    headerText: i18n.t('阶梯开始')
  },
  {
    field: 'stageTo',
    headerText: i18n.t('阶梯结束')
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('未税单价')
  },
  {
    field: 'taxRate',
    headerText: i18n.t('税率')
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('含税单价')
  },
  {
    field: 'column4',
    headerText: i18n.t('是否含运费？')
  },
  {
    field: 'column5',
    headerText: i18n.t('运费？')
  }
]
