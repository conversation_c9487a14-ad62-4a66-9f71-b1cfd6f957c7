<template>
  <div class="content-container mt-flex">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>
<script>
import { columnData } from './config'
import MixIn from '../../config/mixin'
export default {
  props: {
    activeRound: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  mixins: [MixIn],
  data() {
    return {
      biddingListAPI: this.$API.inquiryBidding.getRfxBiddingNewestItem,
      currentRound: this.activeRound,
      pageConfig: [
        {
          toolbar: [],
          grid: { allowFiltering: true, columnData, dataSource: [] }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    handleClickCellTitle(e) {
      if (e.field == 'packageId' || e.field == 'packageName') {
        this.$emit('handleChooseListData', { type: 'package', data: e.data })
      } else if (e.field == 'rfxBidding.supplierCode' || e.field == 'rfxBidding.supplierName') {
        this.$emit('handleChooseListData', { type: 'supply', data: e.data })
      } else if (e.field == 'bidTimes') {
        if (e.data['bidTimes'] > 0) {
          this.handleShowHistoryDialog(e)
        }
      } else if (e.field == 'stages') {
        this.handleShowStagesDialog(e)
      }
    }
  },
  watch: {
    activeRound: {
      handler(n) {
        if (n?.roundNo) {
          this.currentRound = n
          this.resetAsyncConfigParams({ roundNo: n?.roundNo })
        }
      },
      immediate: true,
      deep: true
    }
  }
}
</script>
