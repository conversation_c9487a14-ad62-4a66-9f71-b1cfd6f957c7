import { i18n } from '@/main.js'
const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'applyCode',
    headerText: i18n.t('证书名称')
  },
  {
    field: 'applyType',
    headerText: i18n.t('证书附件')
  },
  {
    field: 'expertName',
    headerText: i18n.t('证书有效时间')
  }
]

export const pageConfig = (url) => [
  {
    useToolTemplate: false,
    grid: { allowFiltering: true, columnData, asyncConfig: { url } }
  }
]
