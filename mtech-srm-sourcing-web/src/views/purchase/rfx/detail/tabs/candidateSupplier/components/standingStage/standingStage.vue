<template>
  <div class="supplier-box">
    <div class="miam-container">
      <div class="operate-bar">
        <div class="operate-bars">
          <!-- <MtIcon name="icon_Packup" /> -->
          <i class="mt-icons mt-icon-icon_Packup"></i>
        </div>
        <div class="operate-box"></div>
        <p class="operate-input">{{ $t('供应商资质信息') }}</p>
      </div>
      <!-- 顶部主要信息 -->
      <div class="mian-info">
        <mt-form ref="ruleForm">
          <mt-form-item ref="formItemarea" prop="applycode" :label="$t('供应商名称：')">
            <mt-input
              float-label-type="Never"
              :width="400"
              :placeholder="$t('请输入供应商名称')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item ref="formItemFullname" prop="expertLevel" :label="$t('总部地址：')">
            <mt-input
              float-label-type="Never"
              :width="400"
              :placeholder="$t('请输入总部地址')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="expertSource" :label="$t('注册资本：')">
            <mt-input
              float-label-type="Never"
              :width="400"
              :placeholder="$t('请输入注册资本')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="expertCategory" :label="$t('成立时间：')">
            <mt-date-picker
              :open-on-focus="true"
              float-label-type="Never"
              :width="400"
              :placeholder="$t('请选择成立日期')"
            ></mt-date-picker>
          </mt-form-item>
          <mt-form-item prop="applyType" :label="$t('公司地址：')">
            <mt-input
              float-label-type="Never"
              :width="400"
              :placeholder="$t('请输入公司地址')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="expertName" ref="formItemphone" :label="$t('公司员工总数（数量）：')">
            <mt-input
              float-label-type="Never"
              :width="400"
              :placeholder="$t('请输入公司员工总数')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="gender" :label="$t('行业经验：')">
            <mt-input
              float-label-type="Never"
              :width="400"
              :placeholder="$t('请输入行业经验')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="fixedPhone" :label="$t('行业排名：')">
            <mt-input
              :width="400"
              float-label-type="Never"
              :placeholder="$t('请输入行业排名')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="subAccount" :label="$t('技术和专业人员（数量）：')">
            <mt-input
              :width="400"
              float-label-type="Never"
              :placeholder="$t('请输入技术和专业人员')"
            ></mt-input>
          </mt-form-item>
          <div class="mian-infos">
            <mt-form-item prop="certificateType" :label="$t('近三年营业额：')">
              <mt-input
                :width="400"
                float-label-type="Never"
                :placeholder="$t('请输入近三年营业额')"
              ></mt-input>
            </mt-form-item>
            <mt-form-item prop="email" :label="$t('财务状态：')">
              <mt-input
                :width="400"
                float-label-type="Never"
                :placeholder="$t('请输入财务状态')"
              ></mt-input>
            </mt-form-item>
          </div>
        </mt-form>
      </div>
      <div class="operta_boxs">
        <div class="operta_never">
          <p class="porer">{{ $t('同行业主力客户：') }}</p>
        </div>
        <div class="operta_nevers">
          <div class="operta_color">
            <div class="operta_colors"></div>
            <p class="normal-title">TOP1：</p>
            <p class="everyday">{{ $t('这里是top1排名客户名称字段') }}</p>
          </div>
          <div class="operta_color">
            <div class="normal_color"></div>
            <p class="normal-title">TOP2：</p>
            <p class="everyday">{{ $t('这里是top2排名客户名称字段') }}</p>
          </div>
          <div class="operta_color">
            <div class="normal_never"></div>
            <p class="normal-title">TOP3：</p>
            <p class="everyday">{{ $t('这里是top3排名客户名称字段') }}</p>
          </div>
        </div>
      </div>
      <div class="operta_body">
        <div class="operta_bodys">
          <div class="body_box"></div>
          <div class="body_boxs">
            <div class="body_header">
              <span class="operta_header">{{ $t('赵文') }}</span>
              <div class="operta_headers">{{ $t('上海元宇宙') }}123456</div>
            </div>
            <div class="operta_config">
              <span class="operta_hander">{{ $t('联系电话：') }}</span>
              <span class="operta_handers">{{ $t('邮箱：') }}</span>
            </div>
          </div>
        </div>
        <div class="operta_bodys">
          <div class="body_config"></div>
          <div class="body_boxs">
            <div class="body_header">
              <span class="operta_header">{{ $t('赵文') }}</span>
              <div class="operta_headers">{{ $t('上海元宇宙') }}123456</div>
            </div>
            <div class="operta_config">
              <span class="operta_hander">{{ $t('联系电话：') }}</span>
              <span class="operta_handers">{{ $t('邮箱：') }}</span>
            </div>
          </div>
        </div>
        <div class="operta_bodys">
          <div class="body_configs"></div>
          <div class="body_boxs">
            <div class="body_header">
              <span class="operta_header">{{ $t('赵文') }}</span>
              <div class="operta_headers">{{ $t('上海元宇宙') }}123456</div>
            </div>
            <div class="operta_config">
              <span class="operta_hander">{{ $t('联系电话：') }}</span>
              <span class="operta_handers">{{ $t('邮箱：') }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="box_body">{{ $t('资质证书') }}</div>
      <div class="relation-ships">
        <mt-template-page ref="templateRef" :padding-top="true" :template-config="pageConfig" />
      </div>
    </div>
  </div>
</template>

<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      pageConfig: pageConfig()
    }
  },
  mounted() {}
}
</script>

<style lang="scss" scoped>
.box_body {
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: normal;
  color: rgba(41, 41, 41, 1);
  padding: 25px 20px;
}
.body_configs {
  width: 4px;
  height: 70px;
  background: rgba(147, 169, 252, 1);
  border-radius: 6px 0 0 6px;
}
.body_config {
  width: 4px;
  height: 70px;
  background: rgba(253, 181, 78, 1);
  border-radius: 6px 0 0 6px;
}
.operta_config {
  display: flex;
  justify-content: space-between;
  padding: 0px 20px;
}
.operta_hander {
  width: 161px;
  height: 20px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: normal;
  color: rgba(154, 154, 154, 1);
}
.operta_handers {
  width: 201px;
  height: 20px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: normal;
  color: rgba(154, 154, 154, 1);
}
.body_header {
  width: 160px;
  padding: 13px 20px;
  display: flex;
  justify-content: space-between;
}
.operta_header {
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
}
.operta_headers {
  width: 72px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 12px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(99, 134, 193, 1);
}
.operta_body {
  padding: 0 20px;
  margin-top: 43px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .operta_bodys {
    width: 460px;
    height: 70px;
    background: rgba(255, 255, 255, 1);
    border-radius: 6px;
    box-shadow: 2px 2px 10px 0 rgba(0, 0, 0, 0.16);
    display: flex;
    .body_box {
      width: 4px;
      height: 70px;
      background: rgba(99, 134, 193, 1);
      border-radius: 6px 0 0 6px;
      display: flex;
      flex-direction: row;
    }
  }
}
.operta_boxs {
  padding: 0 20px;
  display: flex;
  align-items: center;
  // justify-content: space-between;
  .operta_nevers {
    width: 1088px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .operta_color {
      width: 248px;
      height: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .everyday {
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(154, 154, 154, 1);
      }
      .normal_color {
        width: 8px;
        height: 8px;
        background: rgba(152, 170, 195, 1);
        border-radius: 1px;
      }
      .normal_never {
        width: 8px;
        height: 8px;
        background: rgba(212, 222, 237, 1);
        border-radius: 1px;
      }
    }
    .operta_colors {
      width: 8px;
      height: 8px;
      background: rgba(237, 161, 51, 1);
      border-radius: 1px;
    }
    .normal-title {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(41, 41, 41, 1);
    }
  }
  .operta_never {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .porer {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(41, 41, 41, 1);
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
.mian-infos {
  width: 930px;
  display: flex;
  justify-content: space-between;
}
.mt-form {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  .grid_flex {
    width: 679px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.operate-box {
  width: 2px;
  height: 20px;
  background: rgba(0, 70, 156, 1);
  border-radius: 2px;
}
.operate-input {
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
}
.operate-bars {
  width: 20px;
  height: 20px;
  background: rgba(0, 70, 156, 0.1);
  border: 1px solid rgba(0, 70, 156, 0.9);
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  i {
    width: 6px;
    height: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: rotate(180deg);
  }
}
.mt-form {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  .grid_flex {
    width: 679px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.supplier-box {
  position: absolute;
  width: 100%;
  min-height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: #fff;
  overflow: auto;
  -webkit-overflow-scrolling: scroll;

  .flex1 {
    flex: 1;
  }

  .miam-container {
    flex: 1;
    background: #fff;
    padding: 0 20px;

    .operate-bar {
      width: 200px;
      height: 60px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #4f5b6d;
      font-size: 14px;
      padding: 20px;
    }

    .flex-d-c {
      flex-direction: column;
    }

    .mian-info {
      height: 300px;
      background: rgba(255, 255, 255, 1);
      // border: 1px solid rgba(232, 232, 232, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      padding: 20px;
      min-width: 1300px;
      .normal-title {
        width: 100%;
        height: 14px;
        line-height: 14px;
        font-size: 14px;
        color: #292929;
        font-family: PingFangSC;
        font-weight: 500;

        &:before {
          content: ' ';
          display: inline-block;
          vertical-align: middle;
          width: 2px;
          height: 10px;
          background: rgba(0, 70, 156, 1);
          border-radius: 1px;
          margin-right: 10px;
        }
      }

      .flex-d-c {
        flex-direction: column;
      }

      .input-item {
        margin-top: 20px;
        padding-right: 50px;
        .label-txt {
          height: 40px;
          width: 130px;
          font-size: 14px;
          // line-height: 40px;
          color: #292929;
        }
        .label-value {
          height: 40px;
          width: 130px;
          font-size: 14px;
          // line-height: 40px;
          color: #35404e;
        }
        .select-container {
          height: 40px;
        }
        .e-label {
          color: #35404e;
        }
        .label-text {
          color: #35404e;
        }
      }
      .input-item /deep/ .normal-width {
        width: 240px;
      }
      .input-item /deep/ .e-radio + label .e-label {
        color: #35404e;
      }
    }

    .relation-ships {
      flex: 1;
      background: rgba(255, 255, 255, 1);
      // border: 1px solid rgba(232, 232, 232, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      padding: 20px;
      // margin-top: 20px;

      .tab-box {
        width: 100%;
        height: 40px;
        border-bottom: 1px solid #e8e8e8;
        position: relative;

        .tab-item {
          font-size: 14px;
          height: 40px;
          line-height: 40px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(40, 41, 41, 1);
          padding: 0 38px;
          cursor: pointer;
        }
        .active {
          font-size: 16px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(0, 70, 156, 1);
          border-bottom: 4px solid #00469c;
        }

        .right-btn {
          height: 40px;
          position: absolute;
          right: 0;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          transform: all 0.6s ease-in;
          i {
            margin-right: 4px;
            color: #4f5b6d;
          }
          .op-item {
            color: #4f5b6d;
            align-items: center;
            margin-right: 20px;
            align-items: center;
            cursor: pointer;
          }
          .add-new {
            i {
              color: #6386c1;
            }
            color: #6386c1;
          }
        }
      }
      .tab-content {
        .grid-search {
          height: 60px;
          line-height: 60px;
          justify-content: flex-end;
          .search-box {
            .label-txt {
              font-size: 14px;
              font-family: PingFangSC;
              font-weight: normal;
            }
          }
        }
        /deep/ .common-template-page .page-grid-container {
          padding: 0 !important;
        }
      }
    }
  }

  .grid-content {
    padding-top: 20px;
  }
}
</style>
