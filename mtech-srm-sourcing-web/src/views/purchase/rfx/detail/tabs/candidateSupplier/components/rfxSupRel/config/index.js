import { i18n } from '@/main.js'
import cellFileView from 'COMPONENTS/NormalEdit/cellFileView' // 单元格附件查看
import { getValueByPath } from '@/utils/obj'
const toolbar = [
  // { id: "Add", icon: "icon_solid_Createorder", title:  i18n.t("新增") },
  // { id: "Delete", icon: "icon_solid_Delete", title:  i18n.t("删除") },
  // { id: "Save", icon: "icon_solid_Save", title:  i18n.t("保存") },
  // { id: "Stop", icon: "icon_solid_Cancel", title:  i18n.t("停用") },
  // { id: "Submit", icon: "icon_solid_Submit", title:  i18n.t("提交")},
]
const columnData = [
  {
    type: 'checkbox',
    width: '60',
    allowEditing: false
  },
  {
    field: 'examineItemCode',
    headerText: i18n.t('审查代码'),
    allowEditing: false
  },
  {
    field: 'examineItemName',
    headerText: i18n.t('审查项目名称'),
    allowEditing: false
  },
  {
    field: 'examineItemSpec',
    headerText: i18n.t('审查项目说明'),
    allowEditing: false
  },
  {
    field: 'purOpinion',
    headerText: i18n.t('供应商回复'),
    allowEditing: false
  },
  {
    field: 'examineItemCheck',
    headerText: i18n.t('资质审查细项状态'),
    allowEditing: false,
    formatter: ({ field }, item) => {
      const cellVal = getValueByPath(item, field)
      switch (Number(cellVal)) {
        case 0:
          return i18n.t('未通过')
        case 1:
          return i18n.t('待审核')
        case 2:
          return i18n.t('已通过')
        default:
          return cellVal
      }
    }
  },
  {
    field: 'supplierReply',
    headerText: i18n.t('采访审查意见'),
    allowEditing: true
  },
  {
    field: 'drawing',
    headerText: i18n.t('附件'),
    allowEditing: false,
    template: function () {
      return {
        template: cellFileView
      }
    },
    editTemplate: function () {
      return {
        template: cellFileView
      }
    }
  },
  {
    field: 'needFile',
    headerText: i18n.t('是否必须上传附件'),
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  }
]
export const pageConfig = [
  {
    toolbar,
    grid: {
      allowFiltering: true,
      columnData,
      dataSource: [],
      editSettings: {
        allowAdding: false,
        allowEditing: true,
        allowDeleting: false,
        mode: 'Normal', // 默认normal模式
        allowEditOnDblClick: true,
        showConfirmDialog: false,
        showDeleteConfirmDialog: false
      }
    }
  }
]
