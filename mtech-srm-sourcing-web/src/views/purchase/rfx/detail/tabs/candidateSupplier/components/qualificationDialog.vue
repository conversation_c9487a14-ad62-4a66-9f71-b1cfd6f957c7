<template>
  <mt-dialog
    ref="dialog"
    css-class="small-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="close"
  >
    <div class="dialog-content">{{ $t('请确认是否审查通过') }}</div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      //底部按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('不通过') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('通过') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    confirm() {
      let params = {
        idList: this.modalData.idList,
        rfxId: this.modalData.rfxId
      }
      this.$API.rfxSupRel.getPackageById(params).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.$emit('confirm-function')
        }
      })
    },
    cancel() {
      const params = {
        idList: this.modalData.idList,
        rfxId: this.modalData.rfxId
      }
      this.$API.rfxSupRel.rejectRfxSupRel(params).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.$emit('confirm-function')
        }
      })
    },
    close() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
}
</style>
