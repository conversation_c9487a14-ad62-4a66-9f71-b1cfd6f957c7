import { i18n } from '@/main.js'
// const toolbar = [
//   { id: "Adds", icon: "icon_solid_Createorder", title:  i18n.t("新增") },
//   // { id: "Save", icon: "icon_solid_Save", title:  i18n.t("保存") },
//   { id: "Delete", icon: "icon_solid_Delete", title:  i18n.t("删除") },
//   { id: "MarginAudit", icon: "icon_solid_Submit", title:  i18n.t("保证金审核") },
//   { id: "Review", icon: "icon_solid_Submit", title:  i18n.t("资质审查") },
// ];

const columnData = [
  {
    type: 'checkbox',
    width: '60',
    title: i18n.t('行号')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商代码'),
    cssClass: 'field-content'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'stageName',
    headerText: i18n.t('供应商阶段')
  },
  {
    field: 'joinStatus',
    headerText: i18n.t('参与状态'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('未参与'), 1: i18n.t('已参与') }
    }
  },
  // {
  //   field: "linkmanPhone",
  //   headerText: i18n.t("资质审查"),
  // },
  {
    field: 'linkmanMail',
    headerText: i18n.t('保证金缴纳')
  },
  {
    field: 'shortList',
    headerText: i18n.t('保证金附件')
  },
  {
    field: 'shortListRemark',
    headerText: i18n.t('资质审查信息'),
    cssClass: 'field-content'
  },
  {
    field: 'supplierLinkman',
    headerText: i18n.t('供应商联系人')
  },
  {
    field: 'linkmanPhone',
    headerText: i18n.t('联系人电话')
  },
  {
    field: 'linkmanMail',
    headerText: i18n.t('联系人邮箱')
  },
  {
    field: 'shortList',
    headerText: i18n.t('短名单'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    field: 'shortListRemark',
    headerText: i18n.t('短名单说明')
  },
  {
    field: 'taxRateName',
    headerText: i18n.t('税率')
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种')
  },
  {
    field: 'currencyName',
    headerText: i18n.t('供应商信息')
  }
]

export const pageConfig = [
  {
    // useToolTemplate: false,
    toolbar: [],
    grid: { allowFiltering: true, columnData, dataSource: [] }
  }
]
