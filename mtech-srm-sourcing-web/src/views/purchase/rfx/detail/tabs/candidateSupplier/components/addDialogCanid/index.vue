<template>
  <!-- @beforeClose="cancel" -->
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <div class="full-height">
        <mt-template-page ref="templateRef" :padding-top="true" :template-config="pageConfig" />
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      pageConfig: pageConfig,
      //底部按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确认') }
        }
      ],

      formRules: {},
      queryConfigId: '',
      // 货源清单
      sourceList: [
        {
          text: this.$t('有'),
          value: 1
        },
        {
          text: this.$t('无'),
          value: 2
        }
        // {
        //   text: this.$t("审批晋级"),
        //   value: 3,
        // },
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.getCandiDates()
  },
  methods: {
    getCandiDates() {
      this.$set(this.pageConfig[0].grid, 'asyncConfig', {
        url: this.$API.candiDate.candiDateSite,
        defaultRules: [
          {
            label: '',
            field: 'rfxId',
            type: 'number',
            operator: 'equal',
            value: this.modalData.rfxId
          }
        ],
        params: {
          rfxId: this.modalData.rfxId
        }
      })
    },
    //点击确定
    confirm() {
      let _currentTabRef = this.$refs.templateRef.getCurrentTabRef()
      let _selectRecords = _currentTabRef?.gridRef?.getMtechGridRecords() ?? []
      if (_selectRecords.length) {
        let rfxSupplierSaveRequestList = []
        _selectRecords.forEach((e) => {
          rfxSupplierSaveRequestList.push({
            supplierId: e.id,
            supplierCode: e.supplierCode,
            supplierName: e.supplierName,
            stageName: e.stageName,
            supplierTenantId: e.supplierTenantId,
            stageId: e.stageId,
            supplierPartnerCode: e.partnerCode,
            supplierEnterpriseId: e.supplierEnterpriseId
            // stageName: e.stageName,
          })
        })
        this.queryConfigId = this.modalData.rfxId
        const rfxId = this.queryConfigId
        let params = {
          rfxId,
          // rfxId: "this.$route.query.id",
          rfxSupplierSaveRequestList
        }
        this.$API.candiDate.candiDateSaves(params).then((res) => {
          if (res.code == 200) {
            this.$emit('confirm-function')
          }
        })
      }
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  padding-top: 18px;
  font-size: 16px;
  .dialog-Top {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .form-item {
      width: 35%;
    }
    .dialog-remark {
      width: 20%;
      display: flex;
      justify-content: space-around;
      align-items: center;
    }
  }
}
.full-height {
  height: 100%;
}
.relation-ships {
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 20px;
  margin-top: 20px;

  .tab-box {
    width: 100%;
    height: 40px;
    border-bottom: 1px solid #e8e8e8;
    position: relative;

    .tab-item {
      font-size: 14px;
      height: 40px;
      line-height: 40px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(40, 41, 41, 1);
      padding: 0 38px;
      cursor: pointer;
    }
    .active {
      font-size: 16px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(0, 70, 156, 1);
      border-bottom: 4px solid #00469c;
    }

    .right-btn {
      height: 40px;
      position: absolute;
      right: 0;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      transform: all 0.6s ease-in;
      i {
        margin-right: 4px;
        color: #4f5b6d;
      }
      .op-item {
        color: #4f5b6d;
        align-items: center;
        margin-right: 20px;
        align-items: center;
        cursor: pointer;
      }
      .add-new {
        i {
          color: #6386c1;
        }
        color: #6386c1;
      }
    }
  }
  .tab-content {
    .grid-search {
      height: 60px;
      line-height: 60px;
      justify-content: flex-end;
      .search-box {
        .label-txt {
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: normal;
        }
      }
    }
    /deep/ .common-template-page .page-grid-container {
      padding: 0 !important;
    }
  }
}
</style>
