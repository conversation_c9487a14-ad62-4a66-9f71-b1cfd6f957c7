import { i18n } from '@/main.js'
import cellFileView from 'COMPONENTS/NormalEdit/cellFileView' // 单元格附件查看
import { Query } from '@syncfusion/ej2-data'
import { createEditInstance } from '@/utils/ej/dataGrid/index'
import { SUMMARY_STATUS } from '@/constants'
import inputView from '../components/inputView.vue'
// import { getValueByPath } from "@/utils/obj";
//rfq对应的表格按钮  无资质审查 相关信息
export const rfqToolBar = [
  // {
  //   id: "Add",
  //   icon: "icon_solid_Createorder",
  //   title: i18n.t("新增"),
  // },
  // { id: "Delete1", icon: "icon_solid_Delete", title: i18n.t("删除") },
  // {
  //   id: "overApply",
  //   icon: "icon_table_disable",
  //   title: i18n.t("结束报名"),
  // },
]

export const unRfqToolbar = [
  // { id: "Add", icon: "icon_solid_Createorder", title: i18n.t("新增") },
  // // { id: "Save", icon: "icon_solid_Save", title: i18n.t("保存") },
  // { id: "Delete1", icon: "icon_solid_Delete", title: i18n.t("删除") },
  // { id: "overApply", icon: "icon_table_disable ", title: i18n.t("结束报名") },
  // {
  //   id: "closeAudit",
  //   icon: "icon_table_disable",
  //   title: i18n.t("结束供应商评审"),
  // },
  // { id: "MarginAudit", icon: "icon_solid_Submit", title: i18n.t("保证金审核") },
  // {
  //   id: "MarginRefund",
  //   icon: "icon_solid_Submit",
  //   title: i18n.t("保证金退款"),
  // },
  // {
  //   id: "MarginStatus",
  //   icon: "icon_solid_Submit",
  //   title: i18n.t("退款状态查询"),
  // },
]

//rfq对应的表格按钮  无资质审查 相关信息
export const rfqColumnData = () => {
  const editInstance = createEditInstance()
  return [
    {
      type: 'checkbox',
      width: '60',
      allowEditing: false
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商代码'),
      cssClass: 'field-content',
      allowEditing: false
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      allowEditing: false
    },
    {
      field: 'statusDescription',
      headerText: i18n.t('供应商状态'),
      allowEditing: false
    },
    {
      field: 'joinStatus',
      headerText: i18n.t('参与状态'),
      allowEditing: false,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'select',
          fields: { text: 'text', value: 'value' },
          readonly: true,
          dataSource: [
            { text: i18n.t('未参与'), value: 0 },
            { text: i18n.t('已参与'), value: 1 },
            { text: i18n.t('已放弃'), value: -1 },
            { text: i18n.t('不参与'), value: 2 }
          ]
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = item[field]
        switch (Number(cellVal)) {
          case 0:
            return i18n.t('未参与')
          case 1:
            return i18n.t('已参与')
          case -1:
            return i18n.t('已放弃')
          case 2:
            return i18n.t('不参与')
          default:
            return cellVal
        }
      }
    },
    {
      field: 'refusedReason',
      headerText: i18n.t('不参与原因'),
      allowEditing: false
    },
    {
      field: 'summaryStatus',
      headerText: i18n.t('报价状态'),
      allowEditing: false,
      formatter: ({ field }, item) => {
        const cellVal = item[field]
        return SUMMARY_STATUS[cellVal]
      }
    },
    {
      field: 'supplierLinkman',
      headerText: i18n.t('供应商联系人'),
      allowEditing: false
    },
    {
      field: 'linkmanPhone',
      headerText: i18n.t('联系人电话'),
      allowEditing: false,
      template: () => ({ template: inputView })
    },
    {
      field: 'linkmanMail',
      headerText: i18n.t('联系人邮箱'),
      width: 200,
      allowEditing: false,
      template: () => ({ template: inputView })
    },
    {
      field: 'taxRateCode',
      headerText: i18n.t('税率'),
      allowEditing: false
    },
    {
      field: 'currencyCode',
      headerText: i18n.t('币种'),
      allowEditing: false
    }
  ]
}

export const unRfqColumnData = () => {
  const editInstance = createEditInstance()

  return [
    {
      type: 'checkbox',
      width: '60',
      allowEditing: false
    },
    {
      field: 'shortList',
      headerText: i18n.t('是否晋级短名单'),
      valueConverter: {
        type: 'map',
        map: { 0: i18n.t('否'), 1: i18n.t('是') }
      },
      allowEditing: true,
      editType: 'dropdownedit',
      edit: {
        params: {
          dataSource: [
            { value: 1, text: i18n.t('是') },
            { value: 0, text: i18n.t('否') }
          ],
          fields: { value: 'value', text: 'text' },
          query: new Query()
        }
      }
    },
    {
      field: 'shortListRemark',
      headerText: i18n.t('短名单评审说明'),
      allowEditing: true
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商代码'),
      cssClass: 'field-content',
      allowEditing: false
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      allowEditing: false
    },
    {
      field: 'statusDescription',
      headerText: i18n.t('供应商状态'),
      allowEditing: false
    },
    {
      field: 'joinStatus',
      headerText: i18n.t('参与状态'),
      allowEditing: false,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'select',
          fields: { text: 'text', value: 'value' },
          readonly: true,
          dataSource: [
            { text: i18n.t('未参与'), value: 0 },
            { text: i18n.t('已参与'), value: 1 },
            { text: i18n.t('已放弃'), value: -1 },
            { text: i18n.t('不参与'), value: 2 }
          ]
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = item[field]
        switch (Number(cellVal)) {
          case 0:
            return i18n.t('未参与')
          case 1:
            return i18n.t('已参与')
          case -1:
            return i18n.t('已放弃')
          case 2:
            return i18n.t('不参与')
          default:
            return cellVal
        }
      }
    },
    {
      field: 'refusedReason',
      headerText: i18n.t('不参与原因'),
      allowEditing: false
    },
    {
      field: 'examineStatus',
      headerText: i18n.t('资质审查'),
      allowEditing: false,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'select',
          fields: { text: 'text', value: 'value' },
          readonly: true,
          dataSource: [
            { text: i18n.t('未通过'), value: 0 },
            { text: i18n.t('待审核'), value: 1 },
            { text: i18n.t('已通过'), value: 2 }
          ]
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = item[field]
        switch (Number(cellVal)) {
          case 0:
            return i18n.t('未通过')
          case 1:
            return i18n.t('待审核')
          case 2:
            return i18n.t('已通过')
          default:
            return cellVal
        }
      }
    },
    {
      field: 'moneyStatus',
      headerText: i18n.t('保证金缴纳'),
      allowEditing: false,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'select',
          fields: { text: 'text', value: 'value' },
          readonly: true,
          dataSource: [
            { text: i18n.t('已提交凭证'), value: 0 },
            { text: i18n.t('未缴纳'), value: 1 },
            { text: i18n.t('已缴纳'), value: 2 },
            { text: i18n.t('退款中'), value: 3 },
            { text: i18n.t('已全部退款'), value: 4 },
            { text: i18n.t('无需保证金'), value: 5 },
            { text: i18n.t('部分退款'), value: 6 },
            { text: i18n.t('发起退款中'), value: 7 },
            { text: i18n.t('保证金已驳回'), value: 8 },
            { text: i18n.t('保证金已退回'), value: 9 }
          ]
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = item[field]
        switch (Number(cellVal)) {
          case 0:
            return i18n.t('已提交凭证')
          case 1:
            return i18n.t('未缴纳')
          case 2:
            return i18n.t('已缴纳')
          case 3:
            return i18n.t('退款中')
          case 4:
            return i18n.t('已退款')
          case 5:
            return i18n.t('无需保证金')
          case 6:
            return i18n.t('部分退款')
          case 7:
            return i18n.t('发起退款中')
          case 8:
            return i18n.t('保证金已驳回')
          case 9:
            return i18n.t('保证金已退回')
          default:
            return cellVal
        }
      }
    },
    {
      field: 'drawing',
      headerText: i18n.t('保证金附件'),
      allowEditing: false,
      template: function () {
        return {
          template: cellFileView
        }
      },
      editTemplate: function () {
        return {
          template: cellFileView
        }
      }
      // cssClass: "field-content",
      // valueConverter: {
      //   type: "placeholder",
      //   placeholder: i18n.t("保证金附件"),
      // },
    },
    // {
    //   field: "subExamine",
    //   headerText: i18n.t("资质审查信息"),
    //   cssClass: "field-content",
    //   // formatter: ({ field }, item) => {
    //   //   const cellVal = getValueByPath(item, field);
    //   //   switch (Number(cellVal)) {
    //   //     case 0:
    //   //       return i18n.t("资质信息未提交");
    //   //     case 1:
    //   //       return i18n.t("资质审查信息");
    //   //     default:
    //   //       return cellVal;
    //   //   }
    //   // },
    //   valueConverter: {
    //     type: "map",
    //     map: { 0: i18n.t("资质信息未提交"), 1: i18n.t("资质审查信息") },
    //   },
    // },
    {
      field: 'supplierLinkman',
      headerText: i18n.t('供应商联系人'),
      allowEditing: false
    },
    {
      field: 'linkmanPhone',
      headerText: i18n.t('联系人电话'),
      allowEditing: false,
      template: () => ({ template: inputView })
    },
    {
      field: 'linkmanMail',
      headerText: i18n.t('联系人邮箱'),
      width: 200,
      allowEditing: false,
      template: () => ({ template: inputView })
    },
    {
      field: 'taxRateCode',
      headerText: i18n.t('税率'),
      allowEditing: false
    },
    {
      field: 'currencyCode',
      headerText: i18n.t('币种'),
      allowEditing: false
    },
    {
      field: 'viewSupplierInfo',
      headerText: i18n.t('供应商信息'),
      allowEditing: false,
      cssClass: 'field-content',
      valueConverter: {
        type: 'placeholder',
        placeholder: i18n.t('查看')
      }
    }
  ]
}
