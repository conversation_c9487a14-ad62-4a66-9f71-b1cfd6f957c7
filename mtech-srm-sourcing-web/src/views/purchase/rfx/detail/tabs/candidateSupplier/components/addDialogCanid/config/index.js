import { i18n } from '@/main.js'
const columnData = [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商代码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'stageName',
    headerText: i18n.t('供应商阶段')
  }
]
export const pageConfig = [
  {
    useToolTemplate: false,
    grid: {
      allowFiltering: true,
      columnData,
      // allowPaging: false,
      dataSource: []
    }
  }
]
