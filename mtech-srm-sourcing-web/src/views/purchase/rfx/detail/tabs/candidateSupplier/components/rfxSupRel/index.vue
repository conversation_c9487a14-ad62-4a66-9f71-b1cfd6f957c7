<template>
  <mt-dialog
    ref="dialog"
    css-class="candidate-supplier-qualification create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="close"
  >
    <div class="dialog-content full-height">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item class="form-item" :label="$t('审查方法')" label-style="top">
          <mt-select
            disabled
            v-model="formObject.examineMethod"
            :placeholder="$t('合格制')"
            float-label-type="Never"
            :data-source="upgradeRuleTypeList"
            :show-clear-button="true"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('合格上限')">
          <mt-input
            disabled
            v-model="formObject.qualifiedLimit"
            float-label-type="Never"
            placeholder=""
          ></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('是否需要保证金')" label-style="top">
          <mt-select
            disabled
            v-model="formObject.needEarnestMoney"
            :placeholder="$t('请选择是否需要保证金')"
            float-label-type="Never"
            :data-source="upgradeRuleTypeLists"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('保证金金额')">
          <mt-input disabled v-model="formObject.earnestMoney" float-label-type="Never"></mt-input>
        </mt-form-item>
        <div class="dialog-remark">
          <mt-form-item class="form-item" :label="$t('备注：')" label-style="top">
            <mt-input
              disabled
              v-model="formObject.remark"
              :multiline="true"
              :rows="2"
              maxlength="200"
              float-label-type="Never"
              :placeholder="$t('字数不超过500字')"
            ></mt-input>
          </mt-form-item>
        </div>
      </mt-form>
      <div class="grid-wrap">
        <mt-template-page
          ref="templateRef"
          :padding-top="true"
          :template-config="pageConfig"
          @handleClickToolBar="handleClickToolBar"
        />
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { pageConfig } from './config'
// import { utils } from "@mtech-common/utils";
export default {
  data() {
    return {
      pageConfig: pageConfig,
      dataSource: [],
      //底部按钮
      buttons: [
        {
          click: this.getExamineReject,
          buttonModel: { content: this.$t('审核不通过') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('审核通过') }
        }
      ],
      //对象内的值
      formObject: {
        examineMethod: '', //	审查方法（0合格制 1数量制）
        earnestMoney: 0,
        examineCode: '',
        id: 0,
        needEarnestMoney: 0,
        qualifiedLimit: 0,
        remark: '',
        rfxCode: '',
        rfxId: 0
      },
      records: [],
      formRules: {},
      // 审查方法
      upgradeRuleTypeList: [
        {
          text: this.$t('数量制'),
          value: 1
        },
        {
          text: this.$t('合格制'),
          value: 2
        }
        // {
        //   text: this.$t("审批晋级"),
        //   value: 3,
        // },
      ],
      upgradeRuleTypeLists: [
        {
          text: this.$t('是'),
          value: 1
        },
        {
          text: this.$t('否'),
          value: 0
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.getRfxSuperl()
    this.getSupRelList()
    this.updateTabTitle()
    // console.log(1111, this.modalData);
  },
  beforeDestroy() {
    this.$set(this.pageConfig[0].grid, 'asyncConfig', {})
  },
  methods: {
    updateTabTitle(res) {
      if (res?.code == 200) {
        this.dataSource = res.data?.records ?? []
      }
    },
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (_selectGridRecords.length <= 0 && e.toolbar.id == 'Delete') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        this.handleAddSulRel()
      } else if (e.toolbar.id == 'Delete') {
        this.handleBatchDeleteTab0(_selectGridRecords)
      }
    },
    getSupRelList() {
      let params = {
        supplierId: this.modalData.supplierId,
        rfxId: this.modalData.query.rfxId
      }
      this.$API.rfxSupRel.getRfxSupRelList(params).then((res) => {
        this.formObject.examineMethod = res.data.examineMethod
        this.formObject.earnestMoney = res.data.earnestMoney
        this.formObject.examineCode = res.data.examineCode
        this.formObject.needEarnestMoney = res.data.needEarnestMoney
        this.formObject.qualifiedLimit = res.data.qualifiedLimit
        this.formObject.remark = res.data.remark
      })
    },
    getRfxSuperl() {
      this.$set(this.pageConfig[0].grid, 'asyncConfig', {
        url: this.$API.rfxSupRel.rfxSupRelList,
        afterAsyncData: this.updateTabTitle,
        queryBuilderWrap: 'queryBuilderDTO',
        params: {
          rfxId: this.modalData.query.rfxId,
          supplierId: this.modalData.supplierId
        },
        serializeList: (list) => {
          list.forEach((e) => {
            e.drawing = e.sourcingFileResponseList
              ? JSON.stringify(e.sourcingFileResponseList)
              : null //单独处理附件字段
          })
          return list
        }
      })
    },

    confirm() {
      // let _currentTabRef = this.$refs.templateRef.getCurrentTabRef();
      // let _selectRecords = _currentTabRef?.grid?.getSelectedRecords() ?? [];
      let _selectIds = this.dataSource[0].supplierId
      // this.dataSource.map((item) => {
      //   _selectIds.push(item.supplierId);
      // });
      let params = {
        idList: [_selectIds],
        rfxId: this.modalData.query.rfxId
      }
      this.$API.rfxSupRel.getPackageById(params).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.$emit('confirm-function')
        }
      })
    },

    //关闭弹框
    close() {
      this.$emit('cancel-function')
    },

    //审核拒绝接口
    getExamineReject() {
      let examineItemCodes = []
      this.dataSource.map((e) => {
        examineItemCodes.push(e.examineItemCode)
      })
      let params = {
        examineItemCodes: examineItemCodes,
        supplierId: this.modalData.supplierId,
        rfxId: this.modalData.query.rfxId
      }
      this.$API.rfxSupRel
        .examineReject(params)
        .then(() => {
          this.$toast({ content: this.$t('执行成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        })
        .finally(() => {
          this.$emit('cancel-function')
          this.$refs.templateRef.refreshCurrentGridData()
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  .grid-wrap {
    flex: 1;
  }
}
</style>
