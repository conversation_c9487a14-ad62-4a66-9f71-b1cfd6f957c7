<template>
  <!-- @beforeClose="cancel" -->
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog supplier-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <div class="full-height">
        <mt-template-page ref="templateRef" :template-config="pageConfig" />
      </div>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: [],
          grid: { allowFiltering: true, columnData: [], dataSource: [] }
        }
      ],
      //底部按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确认') }
        }
      ],

      formRules: {},
      queryConfigId: '',
      // 货源清单
      sourceList: [
        {
          text: this.$t('有'),
          value: 1
        },
        {
          text: this.$t('无'),
          value: 2
        }
        // {
        //   text: this.$t("审批晋级"),
        //   value: 3,
        // },
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  beforeMount() {
    this.getCandiDate()
  },
  methods: {
    getCandiDate() {
      if (
        this.modalData.supplierRange == 'all_supplier' ||
        this.modalData.sourcingObjType == 'cost_factor'
      ) {
        this.$set(this.pageConfig[0].grid, 'columnData', [
          {
            type: 'checkbox',
            width: '60'
          },
          {
            field: 'supplierCode',
            headerText: this.$t('供应商代码')
          },
          {
            field: 'supplierName',
            headerText: this.$t('供应商名称')
          },
          {
            field: 'statusDescription',
            headerText: this.$t('供应商状态')
          }
        ])
        this.$set(this.pageConfig[0], 'toolbar', [])
        this.$set(
          this.pageConfig[0],
          'gridId',
          this.$permission.gridId['purchase'][this.modalData.source]['detail']['tabs']['supplier'][
            'dialog_supplier'
          ]
        )
        this.$set(this.pageConfig[0].grid, 'asyncConfig', {
          url: this.$API.masterData.getItemListUrls,
          defaultRules: [
            {
              condition: 'and',
              field: 'organizationCode',
              operator: 'equal',
              value: this.modalData.companyCode
            }
          ]
          // defaultRules: [
          //   {
          //     companyCode: this.modalData.companyCode,
          //   },
          // ],
        })
      } else {
        this.$set(this.pageConfig[0].grid, 'columnData', [
          {
            type: 'checkbox',
            width: '60'
          },
          {
            field: 'supplierCode',
            headerText: this.$t('供应商代码')
          },
          {
            field: 'supplierName',
            headerText: this.$t('供应商名称')
          },
          {
            field: 'statusId',
            headerText: this.$t('供应商状态'),
            valueConverter: {
              type: 'map',
              map: {
                1: this.$t('注册'),
                2: this.$t('潜在'),
                10: this.$t('合格'),
                11: this.$t('预合格'),
                20: this.$t('冻结'),
                40: this.$t('退出'),
                3: this.$t('新合格'),
                4: this.$t('临时')
              }
            },
            ignore: true
          },
          {
            field: 'isPriceRecord',
            headerText: this.$t('是否有价格记录'),
            valueConverter: {
              type: 'map',
              map: { 0: this.$t('否'), 1: this.$t('是') }
            },
            ignore: true
          }
        ])
        this.$set(this.pageConfig[0], 'gridId', '96cc83a1-9939-4d5f-a08f-ce43f2efede9')
        this.$set(this.pageConfig[0].grid, 'asyncConfig', {
          url: `/sourcing/tenant/rfxItemRoundSupplier/queryItemSiteSourcePage?BU_CODE=${localStorage.getItem('currentBu')}`,
          params: {
            rfxId: this.modalData.rfxId
          },
          recordsPosition: 'data.records'
        })
        // this.$API.candiDate.queryItemSiteSource({ rfxId: this.modalData.rfxId }).then((res) => {
        //   this.$set(this.pageConfig[0].grid, 'dataSource', res.data)
        // })
        // this.$set(this.pageConfig[0].grid, "asyncConfig", {
        //   url: this.$API.candiDate.candiDateSite,
        //   params: {
        //     rfxId: this.modalData.rfxId,
        //   },
        // });
      }
    },
    //点击确定
    confirm() {
      let _currentTabRef = this.$refs.templateRef.getCurrentTabRef()
      let _selectRecords = _currentTabRef?.gridRef?.getMtechGridRecords() ?? []
      let rfxSupplierSaveRequestList = []
      if (_selectRecords.length) {
        _selectRecords.forEach((e) => {
          if (this.modalData.supplierRange == 'all_supplier') {
            rfxSupplierSaveRequestList.push({
              supplierId: e.id,
              supplierCode: e.supplierCode,
              supplierName: e.supplierName,
              supplierTenantId: e.supplierTenantId,
              stageId: e.stageId,
              supplierPartnerCode: e.partnerCode,
              supplierEnterpriseId: e.supplierEnterpriseId,
              statusDescription: e.statusDescription
            })
          } else {
            rfxSupplierSaveRequestList.push({
              supplierId: e.id,
              supplierCode: e.supplierCode,
              supplierName: e.supplierName,
              supplierTenantId: e.supplierTenantId,
              stageId: e.stageId,
              supplierPartnerCode: e.supplierPartnerCode,
              supplierEnterpriseId: e.supplierEnterpriseId,
              statusDescription: e.statusDescription
            })
          }
        })
        this.queryConfigId = this.modalData.rfxId
        const rfxId = this.queryConfigId

        if (this.modalData.supplierRange == 'all_supplier') {
          let params = {
            rfxId: rfxId,
            rfxSupplierSaveRequestList: rfxSupplierSaveRequestList
          }
          this.$API.candiDate.candiDateSave(params).then((res) => {
            if (res.code == 200) {
              this.$emit('confirm-function')
            }
          })
        } else {
          let params = {
            rfxId: rfxId,
            rfxRoundSupplierSaveRequestList: rfxSupplierSaveRequestList
          }
          this.$API.candiDate.rfxItemRoundSupplierSave(params).then((res) => {
            if (res.code == 200) {
              this.$emit('confirm-function')
            }
          })
        }
      }
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss">
.create-proj-dialog.supplier-dialog .e-dlg-content {
  padding: 0px 16px !important;
}
</style>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  font-size: 16px;
  .dialog-Top {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .form-item {
      width: 35%;
    }
    .dialog-remark {
      width: 20%;
      display: flex;
      justify-content: space-around;
      align-items: center;
    }
  }
}
.full-height {
  height: 100%;
}
.relation-ships {
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 20px;
  margin-top: 20px;

  .tab-box {
    width: 100%;
    height: 40px;
    border-bottom: 1px solid #e8e8e8;
    position: relative;

    .tab-item {
      font-size: 14px;
      height: 40px;
      line-height: 40px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(40, 41, 41, 1);
      padding: 0 38px;
      cursor: pointer;
    }
    .active {
      font-size: 16px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(0, 70, 156, 1);
      border-bottom: 4px solid #00469c;
    }

    .right-btn {
      height: 40px;
      position: absolute;
      right: 0;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      transform: all 0.6s ease-in;
      i {
        margin-right: 4px;
        color: #4f5b6d;
      }
      .op-item {
        color: #4f5b6d;
        align-items: center;
        margin-right: 20px;
        align-items: center;
        cursor: pointer;
      }
      .add-new {
        i {
          color: #6386c1;
        }
        color: #6386c1;
      }
    }
  }
  .tab-content {
    .grid-search {
      height: 60px;
      line-height: 60px;
      justify-content: flex-end;
      .search-box {
        .label-txt {
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: normal;
        }
      }
    }
    /deep/ .common-template-page .page-grid-container {
      padding: 0 !important;
    }
  }
}
</style>
