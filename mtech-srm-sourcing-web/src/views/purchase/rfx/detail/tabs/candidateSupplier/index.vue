<template>
  <div class="full-height mt-flex-direction-column">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>
<script>
import { utils } from '@mtech-common/utils'
import { rfqToolBar, unRfqToolbar, rfqColumnData, unRfqColumnData } from './config/index'
export default {
  data() {
    let isQualificationView = true
    if (this.$route.query.status == 0) {
      // 单据状态 0:草稿 1:进行中 2:已暂停 3:已关闭，非草稿状态仅可查看
      isQualificationView = false
    }
    return {
      detailInfo: {}, //单据详情
      isQualificationView, // 资质审查弹框是否仅可查看，不可编辑
      pageConfig: [
        {
          toolbar: rfqToolBar,
          useToolTemplate: false,
          gridId:
            this.$permission.gridId['purchase'][this.$route?.query?.source]['detail']['tabs'][
              'supplier'
            ]['list'],
          grid: {
            allowFiltering: true,
            columnData: rfqColumnData(),
            dataSource: [],
            actionComplete: this.actionComplete,
            editSettings: {
              allowAdding: false,
              allowEditing: true,
              allowDeleting: false,
              mode: 'Normal', // 默认normal模式
              allowEditOnDblClick: true,
              showConfirmDialog: false,
              showDeleteConfirmDialog: false
            }
          }
        }
      ],
      defaultOn: true,
      examineId: '',
      examineStatus: null
    }
  },
  computed: {
    showQualification() {
      return this.$route?.query?.source !== 'rfq'
    }
  },
  async mounted() {
    this.initPageData()
  },
  async activated() {
    this.initPageData()
  },
  methods: {
    async initPageData() {
      // 进入候选供方后，重新获取 当前单据详情。  主要关注‘供应商选择范围’
      await this.$API.rfxDetail.getRFXDetailById({ id: this.$route.query.rfxId }).then((res) => {
        this.detailInfo = res.data
      })

      this.getCandidateList() //获取列表
      this.queryExamineStatus()
    },
    actionComplete(args) {
      if (args.requestType == 'save' && args.action == 'edit') {
        if (
          args.data.shortList != args.previousData.shortList ||
          args.data.shortListRemark != args.previousData.shortListRemark
        ) {
          let shortRequestList = []
          shortRequestList.push({
            shortList: args.data.shortList,
            shortListRemark: args.data.shortListRemark,
            supplierId: args.data.supplierId
          })
          this.$API.candiDate
            .updateShortListBySupplierId({
              rfxId: this.$route.query.rfxId,
              shortRequestList: shortRequestList
            })
            .then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('修改成功'),
                  type: 'success'
                })
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
            .catch(() => {
              this.$refs.templateRef.refreshCurrentGridData()
            })
        }
      }
    },
    // 切换资质审查开启状态
    async changeExamineStatus() {
      this.$store.commit('startLoading')
      await this.$API.rfxSupRel
        .openExamine({
          rfxId: this.$route.query.rfxId
        })
        .catch(() => {})
      await this.queryExamineStatus()
      this.$store.commit('endLoading')
    },
    // 查询资质审查开启状态
    async queryExamineStatus() {
      const res = await this.$API.rfxSupRel
        .queryExamineStatus({
          rfxId: this.$route.query.rfxId
        })
        .catch(() => {})
      if (res) {
        this.examineStatus = !!(res.data === 1)
      }
    },
    setToolbar() {
      let toolbar = utils.cloneDeep(rfqToolBar)
      let columnData = rfqColumnData()

      if (this.$route?.query?.source !== 'rfq') {
        toolbar = utils.cloneDeep(unRfqToolbar)
        columnData = unRfqColumnData()
      }
      // 0 草稿   -2 撤回
      if (this.detailInfo.status === 0 || this.detailInfo.status === -2) {
        toolbar.unshift(
          {
            id: 'Add',
            icon: 'icon_solid_Createorder',
            title: this.$t('新增')
          },
          { id: 'Delete1', icon: 'icon_solid_Delete', title: this.$t('删除') }
        )
      }
      if (this.detailInfo.status === 8 || this.detailInfo.status === -1) {
        toolbar = toolbar.map((v) => ({
          ...v,
          visibleCondition: () => false
        }))
      }
      // 公开招标隐藏新增按钮
      if (this.detailInfo.biddingMode === 'open') {
        toolbar.forEach((item, index) => {
          if (item.id == 'Add') {
            toolbar.splice(index, 1)
          }
        })
      }

      return {
        toolbar,
        columnData
      }
    },
    getCandidateList() {
      if (this.detailInfo.supplierRange == 'all_supplier') {
        this.getCanidDate()
      } else {
        this.getCanidDates()
      }
    },
    getCanidDate() {
      const config = this.setToolbar()
      this.$set(this.pageConfig[0], 'toolbar', config.toolbar)
      this.$set(this.pageConfig[0].grid, 'columnData', config.columnData)
      this.$set(this.pageConfig[0].grid, 'asyncConfig', {
        url: this.$API.candiDate.candiDateByRfxId,
        defaultRules: [
          {
            label: '',
            field: 'rfxId',
            type: 'number',
            operator: 'equal',
            value: this.$route.query.rfxId
          }
        ],
        serializeList: (list) => {
          list.forEach((e) => {
            e.examineStatus = e.examineStatus ?? 0
            e.moneyStatus = e.moneyStatus ?? 1
            e.drawing = e.sourcingFileResponseList
              ? JSON.stringify(e.sourcingFileResponseList)
              : null //单独处理附件字段
          })
          return list
        }
      })
    },
    getCanidDates() {
      const config = this.setToolbar()

      this.$set(this.pageConfig[0], 'toolbar', config.toolbar)
      this.$set(this.pageConfig[0].grid, 'columnData', config.columnData)

      this.$set(this.pageConfig[0].grid, 'asyncConfig', {
        url:
          this.detailInfo.supplierRange == 'all_supplier'
            ? this.$API.candiDate.candiDateByRfxId
            : this.$API.candiDate.candiDateByRfxIds,
        defaultRules: [
          {
            label: '',
            field: 'rfxId',
            type: 'string',
            operator: 'equal',
            value: this.$route.query.rfxId
          }
        ],
        serializeList: (list) => {
          list.forEach((e) => {
            e.examineStatus = e.examineStatus ?? 0
            e.moneyStatus = e.moneyStatus ?? 1
            e.drawing = e.sourcingFileResponseList
              ? JSON.stringify(e.sourcingFileResponseList)
              : null //单独处理附件字段
          })
          return list
        }
      })
    },
    //表格头部按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (_selectGridRecords.length < 1) {
        if (
          e.toolbar.id == 'Delete1' ||
          e.toolbar.id == 'MarginAudit' ||
          e.toolbar.id == 'MarginRefund'
        ) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
      }
      if (e.toolbar.id == 'Add') {
        // 新增
        this.beforeHandleAddSupplier()
      } else if (e.toolbar.id == 'Delete1') {
        // 删除
        this.handleBatchDelete(_selectGridRecords)
      } else if (e.toolbar.id == 'MarginAudit') {
        // 保证金审核
        this.handleBatchMarginAudit(_selectGridRecords)
      } else if (e.toolbar.id == 'MarginRefund') {
        // 保证金退款
        this.handleBatchMarginRefund(_selectGridRecords)
      } else if (e.toolbar.id == 'MarginStatus') {
        // 退款状态 查询
        this.handleBatchMarginStatus()
      } else if (e.toolbar.id == 'overApply') {
        // 结束报名
        this.handleOverApplyConfigs()
      } else if (e.toolbar.id == 'closeAudit') {
        // 结束供应商评审
        this.handleCloseAudit()
      } else if (e.toolbar.id == 'Adds') {
        this.handleAddCaind()
      }
    },
    //单元格按钮
    handleClickCellTool(e) {
      if (e.tool.id == 'Stop') {
        this.handleStopModel([e.data.id], 0)
      }
    },
    // 新增跳转货源的弹框
    handleAddCaind() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/supply/candidateSupplier/components/addDialog/index.vue" */ './components/addDialogCanid/index.vue'
          ),
        data: {
          title: this.$t('待选供应商'),
          rfxId: this.$route.query.rfxId,
          supplierRange: this.detailInfo.supplierRange
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //批量删除操作
    handleBatchDelete(_selectGridRecords) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      this.handleStopModel(_selectIds)
    },
    //执行删除操作
    handleStopModel(idList) {
      if (this.detailInfo.supplierRange == 'all_supplier') {
        let params = {
          idList,
          rfxId: this.$route.query.rfxId
        }
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t(`确认执行删除操作？`)
          },
          success: () => {
            this.$API.candiDate.candiDateDelete(params).then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('操作成功'),
                  type: 'success'
                })
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
          }
        })
      } else {
        let params = {
          idList,
          rfxId: this.$route.query.rfxId
        }
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t(`确认执行删除操作？`)
          },
          success: () => {
            this.$API.candiDate.candiDateDeleteNo(params).then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('操作成功'),
                  type: 'success'
                })
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
          }
        })
      }
    },
    //保证金审核
    handleBatchMarginAudit(_selectGridRecords) {
      let supplierIdList = []
      _selectGridRecords.map((item) => {
        supplierIdList.push(item.supplierId)
      })
      this.handleAudit(supplierIdList)
    },
    handleAudit(supplierIdList) {
      const query = { rfxId: this.$route.query.rfxId }
      const rfxId = query.rfxId
      let params = {
        rfxId,
        supplierIdList
      }
      this.$API.rfxSupRel.earnestPass(params).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: res.msg,
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //保证金退款
    handleBatchMarginRefund(_selectGridRecords) {
      let supplierIdList = []
      _selectGridRecords.map((item) => {
        supplierIdList.push(item.supplierId)
      })
      this.handleRefund(supplierIdList)
    },
    handleRefund(supplierIdList) {
      const query = { rfxId: this.$route.query.rfxId }
      const rfxId = query.rfxId
      let params = {
        rfxId,
        supplierIdList
      }
      this.$API.rfxSupRel.earnestRefund(params).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //退款状态查询
    handleBatchMarginStatus() {
      this.$API.rfxSupRel.paymentStatus({ rfxId: this.$route.query.rfxId }).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //结束报名
    handleOverApplyConfigs() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t("确认执行'结束报名'操作？")
        },
        success: () => {
          let params = {
            rfxId: this.$route.query.rfxId
          }
          this.$API.rfxTask.closeJoin(params).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
            }
          })
        }
      })
    },
    //结束供应商评审
    handleCloseAudit() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t("确认执行'结束供应商评审'操作？")
        },
        success: () => {
          let params = {
            rfxId: this.$route.query.rfxId
          }
          this.$API.rfxTask.closeAudit(params).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
            }
          })
        }
      })
    },
    // 资质审查
    handleReview(idList) {
      this.$dialog({
        modal: () => import('./components/qualificationDialog.vue'),
        data: {
          title: this.$t('审查是否通过'),
          rfxId: this.$route.query.rfxId,
          idList
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 新增供应商之前，校验采购明细存在有效数据
    beforeHandleAddSupplier() {
      let flag = false
      let fieldCode = ''
      // let required= ""
      // fieldCodeSourcingType = ""
      this.$API.rfxList
        .getUserConfigFields({
          sourcingObj: this.detailInfo.sourcingObj,
          sourcingMode: this.detailInfo.sourcingMode,
          businessTypeCode: this.detailInfo.businessTypeCode
        })
        .then((res) => {
          res.data.fieldDefines.find((e) => {
            if (e.fieldCode == 'sourcingType') {
              fieldCode = 'sourcingType'
              if (e.required) {
                flag = true
              }
            }
          })
        })
      // if (flag && this.detailInfo.sourcingType !== '') {
      //   isNewSupplier = true
      // }
      let params = {
        condition: '',
        defaultRules: [
          {
            field: 'rfx_item.rfxHeaderId',
            operator: 'equal',
            type: 'string',
            value: this.$route.query.rfxId
          }
        ],
        page: {
          current: 1,
          size: 5
        }
      }
      this.$API.rfxRequireDetail.getRFXItem(params).then((res) => {
        let _r = res?.data?.records
        if (Array.isArray(_r) && _r.length) {
          if (flag && this.detailInfo.sourcingType) {
            this.handleAddCost()
          } else if (!fieldCode) {
            this.handleAddCost()
          } else {
            this.$toast({
              content: this.$t('请维护基础信息的询价类型.'),
              type: 'warning'
            })
          }
        } else {
          this.$toast({
            content: this.$t('采购明细Tab未维护数据.'),
            type: 'warning'
          })
        }
      })
    },
    //新增所有供应商
    handleAddCost() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/supply/candidateSupplier/components/addDialog/index.vue" */ './components/addDialog/index.vue'
          ),
        data: {
          title: this.$t('待选供应商'),
          rfxId: this.$route.query.rfxId,
          supplierRange: this.detailInfo.supplierRange,
          companyCode: this.detailInfo.companyCode,
          source: this.$route?.query?.source,
          sourcingObjType: this.detailInfo.sourcingObjType
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //单元格标题
    handleClickCellTitle(e) {
      if (e.field == 'viewSupplierInfo') {
        let selectIds = []
        selectIds.push(e.data.supplierId)
        this.$router.push({
          path: '/supplier/pur/profileDetail',
          query: {
            type: 'archive',
            orgId: this.detailInfo.companyId,
            supplierInternalCode: e.data.supplierPartnerCode,
            rfxId: this.$route.query.rfxId, //资质审核传参
            idList: selectIds
          }
        })
      }
      if (e.field == 'subExamine') {
        if (e.data.subExamine == 0) {
          this.$toast({
            content: this.$t('供应商未提交资质审查信息'),
            type: 'warning'
          })
        } else {
          this.$dialog({
            modal: () =>
              import(
                /* webpackChunkName: "router/supply/candidateSupplier/components/addDialog/index.vue" */ './components/rfxSupRel/index.vue'
              ),
            data: {
              title: this.$t('资质审查信息'),
              supplierId: e.data.supplierId,
              query: {
                rfxId: this.$route.query.rfxId
              }
            },
            success: () => {
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.toolbar-supplier {
  padding: 10px 20px 0 20px;
  display: flex;
  flex-shrink: 0;
  height: 50px;

  .toolbar-item {
    margin-right: 10px;
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: var(--plugin-tb-tool-item-color);
    i {
      margin-right: 5px;
    }

    &.disabled {
      cursor: no-drop;
      span,
      i {
        color: var(--plugin-tb-tool-item-disable-color);
      }
    }
  }

  .toolbar-item:hover {
    color: #707b8b;
    color: var(--plugin-tb-tool-item-hover-color);
  }
}
</style>
