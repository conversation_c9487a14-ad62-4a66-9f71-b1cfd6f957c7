<template>
  <div class="supplier-list">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    />
  </div>
</template>

<script>
import { supplierListColumnData } from '../config'
export default {
  props: {
    rfxStatus: {
      type: Number,
      default: 0
    },
    roundId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      pageConfig: [
        {
          toolbar: [],
          grid: {
            allowFiltering: true,
            columnData: supplierListColumnData,
            dataSource: [],
            frozenColumns: 2
          }
        }
      ]
    }
  },
  watch: {
    roundId(n, o) {
      if (n !== o) {
        this.resetAsyncConfigParams()
      }
    }
  },
  mounted() {
    this.judgeToolbar()
    if (this.roundId) {
      this.resetAsyncConfigParams()
    }
  },
  methods: {
    judgeToolbar() {
      let _arr = [
        // { id: "add", icon: "icon_solid_Createproject", title: this.$t("新增") },
        { id: 'delete', icon: 'icon_solid_Delete1', title: this.$t('删除') },
        {
          id: 'edit_pay',
          icon: 'icon_solid_edit',
          title: this.$t('变更缴纳状态')
        },
        {
          id: 'edit_review',
          icon: 'icon_solid_edit',
          title: this.$t('变更评审状态')
        }
      ]
      if (this.rfxStatus == 1) {
        _arr = [
          {
            id: 'active',
            icon: 'icon_solid_Activateorder',
            title: this.$t('启用')
          },
          { id: 'inactive', icon: 'icon_solid_Cancel', title: this.$t('禁用') },
          {
            id: 'edit_pay',
            icon: 'icon_solid_edit',
            title: this.$t('变更缴纳状态')
          },
          {
            id: 'edit_review',
            icon: 'icon_solid_edit',
            title: this.$t('变更评审状态')
          }
        ]
      }
      this.$set(this.pageConfig[0], 'toolbar', _arr)
    },
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      console.log('use-handleClickToolBar', e, _selectGridRecords)
      if (e.toolbar.id == 'add') {
        this.handleAdd()
        return
      }
      if (_selectGridRecords.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _id = [],
        _status = []
      _selectGridRecords.map((item) => {
        _id.push(item.supplierId)
        _status.push(item.deposit)
      })

      if (e.toolbar.id == 'delete') {
        this.handleDelete(_id)
      } else if (e.toolbar.id == 'active') {
        this.handleActive(_id, 'activeRFXSupplier')
      } else if (e.toolbar.id == 'inactive') {
        this.handleActive(_id, 'inActiveRFXSupplier')
      } else if (e.toolbar.id == 'edit_pay') {
        this.handleDeposit(_id, 'updateDeposit')
      } else if (e.toolbar.id == 'edit_review') {
        this.handleReview(_id, 'updateReview')
      }
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'delete') {
        this.handleDelete([e.data.id])
      }
    },
    // 操作：新增
    handleAdd() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/rfx/detail/tabs/candidate-supplier/components/dialogSource" */ './dialogSource.vue'
          ),
        data: {
          title: this.$t('选择货源'),
          requestUrl: 'distributeByIdDetail'
        },
        success: () => {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 操作：删除
    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除？'),
          confirm: () =>
            this.$API.rfxSupplier.deleteRFXSupplier({
              idList: ids,
              rfxId: this.$route.query.rfxId
            })
        },
        success: () => {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 操作：启禁用供应商
    handleActive(ids, url) {
      this.$API.rfxSupplier[url]({
        idList: ids,
        rfxId: this.$route.query.rfxId
      }).then(() => {
        this.$refs.templateRef.refreshCurrentGridData()
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
      })
    },
    // 操作：更改缴纳状态
    handleDeposit(ids) {
      this.$API.rfxSupplier
        .updateDeposit({
          idList: ids,
          roundId: this.roundId,
          rfxId: this.$route.query.rfxId
        })
        .then(() => {
          this.$refs.templateRef.refreshCurrentGridData()
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
        })
    },
    // 操作：更新评审状态
    handleReview(ids) {
      this.$API.rfxSupplier
        .updateReview({
          idList: ids,
          roundId: this.roundId,
          rfxId: this.$route.query.rfxId
        })
        .then(() => {
          this.$refs.templateRef.refreshCurrentGridData()
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
        })
    },
    //列表参数重新赋值
    resetAsyncConfigParams() {
      this.$set(this.pageConfig[0].grid, 'asyncConfig', {
        url: this.$API.rfxSupplier.getRFXRoundDistincList,
        defaultRules: [
          {
            field: 'roundId',
            operator: 'equal',
            type: 'string',
            value: this.roundId
          }
        ]
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .mt-data-grid .status-text {
  font-size: 12px;
  line-height: 1;
  padding: 4px;
  border-radius: 2px;
  &.isStatus {
    color: #54bf00;
    background: rgba(84, 191, 0, 0.1);
  }
  &.unStatus {
    color: #ed5633;
    background: rgba(237, 86, 51, 0.1);
  }
  &.unping {
    color: #9a9a9a;
    background: rgba(154, 154, 154, 0.1);
  }
}
</style>
