<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="createRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="roleName" :label="$t('寻源角色')">
          <mt-select
            v-model="formObject.roleName"
            float-label-type="Never"
            :data-source="expertRuleList"
            :show-clear-button="true"
            :placeholder="$t('请选择寻源角色')"
            @change="changeSelectRole"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="userCode" :label="$t('账号')">
          <mt-select
            v-model="formObject.userCode"
            float-label-type="Never"
            :data-source="tenantUserList"
            :show-clear-button="true"
            :allow-filtering="true"
            :filtering="filterUserList"
            :placeholder="userCodePlaceholder"
            @change="changeSelectUser"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          v-if="formObject.roleName.includes('expert')"
          prop="expertType"
          :label="$t('专家来源')"
        >
          <mt-select
            ref="companyRef"
            :disabled="true"
            v-model="formObject.expertType"
            float-label-type="Never"
            :data-source="companyList"
            :show-clear-button="true"
            :placeholder="$t('请选择专家来源')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="userName" :label="$t('姓名')">
          <mt-input
            :disabled="true"
            v-model="formObject.userName"
            :show-clear-button="false"
            float-label-type="Never"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="userPhone" :label="$t('手机号')">
          <mt-input
            :disabled="true"
            v-model="formObject.userPhone"
            :show-clear-button="false"
            float-label-type="Never"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="userMail" :label="$t('电子邮箱')">
          <mt-input
            :disabled="true"
            v-model="formObject.userMail"
            :show-clear-button="false"
            float-label-type="Never"
          ></mt-input>
        </mt-form-item>

        <mt-form-item
          v-if="formObject.roleName.includes('expert')"
          prop="expertLevel"
          :label="$t('专家级别')"
        >
          <mt-select
            ref="companyRef"
            :disabled="true"
            v-model="formObject.expertLevel"
            float-label-type="Never"
            :data-source="expertList"
            :show-clear-button="true"
            :placeholder="$t('请选择专家级别')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="groupLeader" :label="$t('是否专家组长')">
          <mt-select
            ref="companyRef"
            :disabled="formObject.groupLeaderDisabled"
            v-model="formObject.groupLeader"
            float-label-type="Never"
            :data-source="expertLists"
            :show-clear-button="true"
            :placeholder="$t('请选择是否专家组长')"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
import { utils } from '@mtech-common/utils'
import { expertRuleList } from '@/constants'
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      expertRuleList,
      formObject: {
        companyCode: null, //公司编码
        companyId: null, //公司Id
        companyName: null, //公司名
        depId: null, //部门Id
        depName: null, //部门名称
        docId: null, //	业务对象id
        docType: null, //单据类型
        id: null,
        remark: null, //	备注
        roleName: '', //	寻源角色
        userId: null, //用户Id
        userMail: null, //	邮箱
        userName: null, //	用户名
        userPhone: null, //	电话
        expertLevel: null, //级别 级别：0：初级、1：中级、2：高级
        expertType: null, //专家来源类型（0内部1外部）
        groupLeader: 0, //专家组长（0否1是）
        sourceType: null, //来源类型
        userCode: null, //用户编码
        groupLeaderDisabled: false //是否专家可编辑
      },
      tenantUserList: [],
      formRules: {
        roleName: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        userCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      editStatus: false,
      companyList: [
        { text: this.$t('内部'), value: 0 },
        { text: this.$t('外部'), value: 1 }
      ],
      expertList: [
        { text: this.$t('初级'), value: 0 },
        { text: this.$t('中级'), value: 1 },
        { text: this.$t('高级'), value: 2 }
      ],
      expertLists: [
        { text: this.$t('否'), value: 0 },
        { text: this.$t('是'), value: 1 }
      ],
      userInfo: {},
      userCodePlaceholder: this.$t('请选择账号')
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        width: '500px',
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    dataArr() {
      return this.modalData.distributeArr || []
    },
    enableFilter() {
      return this?.formObject?.roleName.includes('expert')
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.getUserInfo()
    if (this.propsData?.data) {
      this.editStatus = true
    }
    this.filterUserList = utils.debounce(this.filterUserList, 1000)
    // this.getUserAdd();
    // this.getFormValidRules("formRules", this.$API.rfxExt.saveRfxTeamValid);
  },
  methods: {
    changeSelectUser(e) {
      if (this.formObject.roleName.includes('expert')) {
        if (e?.itemData) {
          this.formObject = {
            ...this.formObject,
            // groupLeader: 0,
            userName: e?.itemData?.userName,
            userId: e?.itemData?.userId,
            userCode: e?.itemData?.userCode,
            userMail: e?.itemData?.userMail,
            userPhone: e?.itemData?.userPhone,
            expertLevel: e?.itemData?.expertLevel,
            expertType: e?.itemData?.expertSource
          }
        }
      } else {
        if (e?.itemData) {
          this.formObject = {
            ...this.formObject,
            userName: e?.itemData?.userName,
            userId: e?.itemData?.id,
            userCode: e?.itemData?.userCode,
            userMail: e?.itemData?.email,
            userPhone: e?.itemData?.telephone
            // groupLeader: 0,
          }
        }
      }
    },
    changeSelectRole(e) {
      this.getUserAdd(e.value)
      this.formObject.expertType = null
      this.formObject.expertLevel = null
      this.formObject.userCode = null
      this.formObject.userName = null
      this.formObject.userPhone = null
      this.formObject.userMail = null
      if (e.value == 'bus_expert' || e.value == 'tech_expert') {
        this.formObject.groupLeader = 0
      } else {
        this.formObject.groupLeader = null
        this.formObject.groupLeaderDisabled = true
      }
    },
    getUserAdd(e) {
      // 获取弹窗中的人员列表
      if (e.includes('expert')) {
        this.userCodePlaceholder = this.$t('请选择账号')
        let _expertCategory = e === 'bus_expert' ? '0' : e === 'tech_expert' ? '1' : '2'
        this.$API.rfxExt.getAccountClassify({ expertCategory: _expertCategory }).then((res) => {
          res.data.forEach((u) => {
            u.text = u.userCode ? u.userName + `(${u.userCode})` : u.userName
            u.value = u.userCode
          })
          this.tenantUserList = res.data
        })
      } else {
        this.userCodePlaceholder = this.$t('请输入账号或姓名搜索')
        this.$API.masterData.getUserList({ userName: '' }).then((res) => {
          res.data.forEach((u) => {
            u.text = u.userCode ? u.userName + `(${u.userCode})` : u.userName
            u.value = u.externalCode
          })
          this.tenantUserList = res.data
        })
      }
    },
    filterUserList(e = { text: '' }) {
      if (this.enableFilter) {
        return
      }
      this.$API.masterData.getUserList({ userName: e.text }).then((res) => {
        res.data.forEach((u) => {
          u.text = u.externalCode ? u.userName + `(${u.externalCode})` : u.userName
          u.value = u.externalCode
        })
        this.tenantUserList = res.data
      })
    },
    confirm() {
      this.$refs.createRef.validate((valid) => {
        if (valid) {
          let docId = this.modalData.query.rfxId
          let params = { ...this.formObject, docId }
          if (!this.editStatus) {
            //新增场景，不传id
            delete params.id
          }
          this.$API.rfxExt.saveRfxTeam(params).then((res) => {
            this.$emit('confirm-function', res.data)
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    //获取当前用户信息
    getUserInfo() {
      this.$API.iamService.getUserDetail().then((res) => {
        this.userInfo = res.data
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
