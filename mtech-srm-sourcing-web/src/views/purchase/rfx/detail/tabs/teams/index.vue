<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    />
  </div>
</template>
<script>
import { isEqual } from 'lodash'
import { toolbar, columnData, editSettings, disabledEditSettings } from './config'
export default {
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      pageConfig: [
        {
          toolbar,
          gridId:
            this.$permission.gridId['purchase'][this.$route?.query?.source]['detail']['tabs'][
              'teams'
            ],
          grid: {
            allowFiltering: true,
            columnData,
            dataSource: [],
            actionComplete: this.actionComplete,
            editSettings: editSettings
          }
        }
      ]
    }
  },
  computed: {
    isDisabled() {
      // 非草稿状态都要禁止编辑
      return this.detailInfo?.status !== 0
    }
  },
  mounted() {
    this.resetGridParams()
    //   this.getUserAdd();
    this.checkDisabledEdit()
  },
  methods: {
    resetGridParams() {
      if (this.isDisabled) {
        this.pageConfig[0].toolbar = toolbar
          .filter((v) => v.id === 'save')
          .map((v) => ({
            ...v,
            visibleCondition: () => false
          }))
      }

      this.$set(this.pageConfig[0].grid, 'asyncConfig', {
        url: this.$API.rfxExt.getRfxTeamList,
        queryBuilderWrap: 'requestParams',
        // defaultRules: [
        //   {
        //     label: "",
        //     field: "userCode",
        //     type: "number",
        //     operator: "equal",
        //     value: 94155933048643672,
        //   },
        // ],
        // transform: true,
        // rootTag: "0",
        params: {
          docId: this.$route.query.rfxId
        }
      })
    },
    // -----------------顶部按钮--------------------------
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (_selectGridRecords.length <= 0 && e.toolbar.id == 'delete') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        this.handleCreateTeam()
      } else if (e.toolbar.id == 'save') {
        this.handleExpert()
      } else if (e.toolbar.id == 'delete') {
        this.handleBatchDelete(_selectGridRecords)
      }
    },
    // 弹窗：创建寻源需求
    handleCreateTeam() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/rfx/detail/tabs/teams/components/createTeam" */ './components/createTeam'
          ),
        data: {
          title: this.$t('新增团队信息'),
          query: {
            rfxId: this.$route.query.rfxId
          }
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    handleExpert() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/rfx/detail/tabs/teams/components/createTeam" */ './components/experts'
          ),
        data: {
          title: this.$t('专家抽取'),
          query: {
            rfxId: this.$route.query.rfxId
          }
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //批量删除操作
    handleBatchDelete(_selectGridRecords) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      this.handleDeleteConfig(_selectIds)
    },
    //删除规则
    handleDeleteConfig(idList) {
      let params = {
        idList
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.rfxExt.RfxTeamDelete(params).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    actionComplete(args) {
      if (args.requestType == 'save' && args.action == 'edit') {
        //数据存储
        if (!isEqual(args.data, args.previousData)) {
          let sourcingTeamGroupLeaderSaveDTOS = new Array()
          sourcingTeamGroupLeaderSaveDTOS.push({
            groupLeader: args.data.groupLeader,
            userId: args.data.userId
          })
          this.$API.rfxExt
            .updateGroupLeader({
              docId: this.$route.query.rfxId,
              sourcingTeamGroupLeaderSaveDTOS
            })
            .then(() => {
              this.$toast({
                content: this.$t('专家组长修改成功'),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData()
            })
        }
      }
    },
    checkDisabledEdit() {
      if (this.detailInfo?.status !== 0) {
        this.$set(this.pageConfig[0].grid, 'editSettings', disabledEditSettings)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.sourceTeam-container {
  .action-boxs {
    .delete-line {
      color: #ed5633;
    }
  }
}
</style>
