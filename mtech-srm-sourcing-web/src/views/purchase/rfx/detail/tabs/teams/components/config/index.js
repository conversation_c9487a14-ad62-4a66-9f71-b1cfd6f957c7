import { i18n } from '@/main.js'
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'delete', icon: 'icon_solid_edit', title: i18n.t('删除') }
]
const columnData = [
  {
    width: '60',
    type: 'checkbox'
  },
  {
    field: 'expertSpecialty',
    headerText: i18n.t('专业领域')
  },
  {
    field: 'specialtyName',
    headerText: i18n.t('专业名称')
  }
]

export const pageConfig = () => [
  {
    toolbar: [toolbar],
    useBaseConfig: false,
    useToolTemplate: false,
    grid: {
      columnData,
      dataSource: []
    }
  }
]
