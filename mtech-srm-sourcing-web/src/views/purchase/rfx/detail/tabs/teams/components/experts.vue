<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content mt-flex-direction-column" style="height: 100%">
      <mt-form ref="createRef" :model="formObject" :rules="formObject.rules">
        <mt-form-item prop="expertCategory" :label="$t('专家类型')">
          <mt-select
            ref="companyRef"
            v-model="formObject.expertCategory"
            float-label-type="Never"
            :data-source="expertLists"
            :placeholder="$t('请选择专家类型')"
            :show-clear-button="true"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="quantity" :label="$t('抽取数量')">
          <mt-input
            v-model="formObject.quantity"
            float-label-type="Never"
            :placeholder="$t('请输入抽取数量')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="expertLevel" :label="$t('专家级别')">
          <mt-select
            ref="companyRef"
            v-model="formObject.expertLevel"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="expertList"
            :show-clear-button="true"
            :placeholder="$t('请选择专家级别')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="expertSource" :label="$t('专家来源')">
          <mt-select
            ref="companyRef"
            v-model="formObject.expertSource"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="companyList"
            :show-clear-button="true"
            :placeholder="$t('请选择专家来源')"
          ></mt-select>
        </mt-form-item>
      </mt-form>
      <div style="flex: 1">
        <mt-template-page
          ref="templateRef"
          :template-config="pageConfig"
          @handleClickToolBar="handleClickToolBar"
        />
      </div>
    </div>
  </mt-dialog>
</template>
<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      formRules: {
        quantity: [
          {
            required: true,
            message: this.$t('请填写抽取数量'),
            trigger: 'blur'
          }
        ]
      },
      pageConfig: pageConfig(),
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formObject: {
        expertCategory: null,
        expertLevel: null,
        expertSource: null,
        quantity: null,
        rules: {
          expertCategory: {
            required: true,
            message: this.$t('请输入专家类型'),
            trigger: 'blur'
          }, // 专家类型
          quantity: {
            required: true,
            message: this.$t('请输入抽取数量'),
            trigger: 'blur'
          } // 抽取数量
        }
      },
      editStatus: false,
      companyList: [
        { text: this.$t('内部'), value: 0 },
        { text: this.$t('外部'), value: 1 }
      ],
      expertList: [
        { text: this.$t('初级'), value: 0 },
        { text: this.$t('中级'), value: 1 },
        { text: this.$t('高级'), value: 2 }
      ],
      expertLists: [
        { text: this.$t('商务'), value: 0 },
        { text: this.$t('技术'), value: 1 }
        // { text: this.$t("商务&技术"), value: 2 },
      ],
      tempIndex: 0
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        width: '500px',
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.propsData?.data) {
      this.editStatus = true
    }
    // this.getFormValidRules("formRules", this.$API.rfxExt.saveRfxTeamValid);
  },
  methods: {
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (_selectGridRecords.length <= 0 && e.toolbar.id == 'delete') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'delete') {
        this.handleBatchDelete(_selectGridRecords)
      }
    },
    handleAdd() {
      this.$dialog({
        modal: () => import('./experts/professionalField.vue'),
        data: {
          title: this.$t('新增团队信息')
        },
        success: (params) => {
          params.id = this.tempIndex
          this.$refs.templateRef.getCurrentTabRef().grid.dataSource.push(params)
          this.$refs.templateRef.refreshCurrentGridData()
          this.tempIndex++
        }
      })
    },
    //批量删除操作
    handleBatchDelete(_selectGridRecords) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })

      for (let id of _selectIds) {
        let dataSource = this.$refs.templateRef.getCurrentTabRef().grid.dataSource
        dataSource.splice(
          dataSource.findIndex((item) => {
            item.id = id
          }),
          1
        )
      }
      this.$refs.templateRef.refreshCurrentGridData()
    },
    confirm() {
      this.$refs.createRef.validate((valid) => {
        if (valid) {
          let _selectGridRecords = this.$refs.templateRef
            .getCurrentTabRef()
            .gridRef.getMtechGridRecords()
          let expertSpecialtyFieldDTOS = []
          for (let item of _selectGridRecords) {
            expertSpecialtyFieldDTOS.push({
              expertSpecialty: item.expertSpecialty,
              specialtyName: item.specialtyName
            })
          }
          let docId = this.modalData.query.rfxId
          let params = { ...this.formObject, expertSpecialtyFieldDTOS, docId }
          // if (!this.editStatus) {
          //   //新增场景，不传id
          //   delete params.id;
          //   params.docId = this.propsData.docId;
          // }
          this.$API.rfxExt.editRfTeam(params).then((res) => {
            this.$emit('confirm-function', res.data)
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
