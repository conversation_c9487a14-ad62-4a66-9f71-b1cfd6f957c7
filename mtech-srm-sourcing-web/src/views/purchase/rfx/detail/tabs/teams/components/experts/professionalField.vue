<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="expertSpecialty" :label="$t('专业领域')">
          <mt-input
            v-model="formObject.expertSpecialty"
            float-label-type="Never"
            maxlength="30"
            :placeholder="$t('请输入专业领域')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="specialtyName" :label="$t('专业名称')">
          <mt-input
            v-model="formObject.specialtyName"
            float-label-type="Never"
            maxlength="100"
            :placeholder="$t('请输入领域名称')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formObject: {
        expertSpecialty: '', //专业领域
        specialtyName: '' //领域名称
      },
      formRules: {
        expertSpecialty: [
          {
            required: true,
            message: this.$t('请输入专业领域'),
            trigger: 'blur'
          }
        ],
        specialtyName: [
          {
            required: true,
            message: this.$t('请输入领域名称'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = { ...this.formObject }
          this.$emit('confirm-function', params)
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/ .div-auth {
    background: #fafafa;
    height: 35px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    color: #0f0f0f;
    font-size: 12px;
    cursor: pointer;
    border-radius: 5px;
  }
}
</style>
