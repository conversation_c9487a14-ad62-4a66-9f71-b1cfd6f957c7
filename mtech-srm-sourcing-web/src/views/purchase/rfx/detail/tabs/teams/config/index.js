import { i18n } from '@/main.js'
import { createEditInstance } from '@/utils/ej/dataGrid/index'
export const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'delete', icon: 'icon_solid_edit', title: i18n.t('删除') },
  { id: 'save', icon: 'icon_solid_edit', title: i18n.t('专家抽取') }
]
import { expertRuleList } from '@/constants'
expertRuleList.forEach((e) => {
  e.cssClass = ''
})
const editInstance = createEditInstance().onInput((ctx, { field, rowData, value }) => {
  console.log('onInput', { ctx, rowData, value, field })
})
export const columnData = [
  {
    width: '60',
    type: 'checkbox',
    allowEditing: false
  },
  {
    field: 'userCode',
    headerText: i18n.t('账号'),
    allowEditing: false
  },
  {
    field: 'expertType',
    headerText: i18n.t('来源'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('内部'), 1: i18n.t('外聘') }
    },
    allowEditing: false,
    edit: editInstance.create({
      getEditConfig: () => ({
        type: 'mt-select',
        readonly: true,
        disabled: true,
        dataSource: [
          { text: i18n.t('内部'), value: 0 },
          { text: i18n.t('外聘'), value: 1 }
        ],
        placeholder: ''
      })
    })
  },
  {
    field: 'userName',
    headerText: i18n.t('姓名'),
    allowEditing: false
  },
  {
    field: 'roleName',
    headerText: i18n.t('寻源角色'),
    valueConverter: {
      type: 'map',
      map: expertRuleList,
      fields: { text: 'text', value: 'value' }
    },
    allowEditing: false,
    edit: editInstance.create({
      getEditConfig: () => ({
        type: 'mt-select',
        readonly: true,
        disabled: true,
        dataSource: expertRuleList,
        placeholder: ''
      })
    })
  },
  {
    field: 'userPhone',
    headerText: i18n.t('手机号'),
    allowEditing: false
  },
  {
    field: 'userMail',
    headerText: i18n.t('电子邮箱'),
    allowEditing: false
  },
  {
    field: 'expertLevel',
    headerText: i18n.t('专家级别'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('初级'), 1: i18n.t('中级'), 2: i18n.t('高级') }
    },
    allowEditing: false,
    edit: editInstance.create({
      getEditConfig: () => ({
        type: 'mt-select',
        readonly: true,
        disabled: true,
        dataSource: [
          { text: i18n.t('初级'), value: 0 },
          { text: i18n.t('中级'), value: 1 },
          { text: i18n.t('高级'), value: 2 }
        ],
        placeholder: ''
      })
    })
  },
  // 组长的概念只有商务专家和技术专家有，
  {
    field: 'groupLeader',
    headerText: i18n.t('是否专家组长'),
    allowEditing: true,
    editType: 'dropdownedit',
    formatter: ({ field }, item) => {
      const cellVal = item[field]
      if (!['tech_expert', 'bus_expert'].includes(item.roleName)) {
        return '-'
      }
      const cellMap = { 0: i18n.t('否'), 1: i18n.t('是') }
      return cellMap[cellVal] || '-'
    },
    edit: editInstance.create({
      getEditConfig: ({ rowData }) => {
        const editConfig = {
          type: 'mt-select',
          dataSource: [
            { value: 1, text: i18n.t('是') },
            { value: 0, text: i18n.t('否') }
          ]
        }
        if (['tech_expert', 'bus_expert'].includes(rowData.roleName)) {
          return editConfig
        }
        return {
          ...editConfig,
          disabled: true,
          dataSource: editConfig.dataSource.map((e) => ({
            ...e,
            text: '-'
          }))
        }
      }
    })
  }
]

export const editSettings = {
  allowAdding: false,
  allowEditing: true,
  allowDeleting: false,
  mode: 'Normal', // 默认normal模式
  allowEditOnDblClick: true,
  showConfirmDialog: false,
  showDeleteConfirmDialog: false
}

export const disabledEditSettings = {
  allowAdding: false,
  allowEditing: false,
  allowDeleting: false,
  mode: 'Normal', // 默认normal模式
  allowEditOnDblClick: true,
  showConfirmDialog: false,
  showDeleteConfirmDialog: false
}
