<template>
  <div class="full-height r-d-container mt-flex">
    <div class="tree-view--wrap">
      <div class="trew-node--add">
        <div class="node-title">{{ $t('层级') }}</div>
      </div>
      <mt-common-tree
        v-if="treeViewData.dataSource.length"
        ref="treeView"
        class="tree-view--template"
        :un-button="true"
        :fields="treeViewData"
        :selected-nodes="selectedNodes"
        :expanded-nodes="expandedNodes"
        @nodeSelected="nodeSelected"
      ></mt-common-tree>
    </div>
    <div class="table-container">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
      />
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { rdToolBar, rdColumnData } from './config'
import { download } from '@/utils/utils'
import { i18n } from '@/main.js'
export default {
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      selectedNodes: [],
      expandedNodes: [],
      treeDataHash: {},
      treeViewData: {
        //相关文件-目录结构-原数据
        nodeTemplate: function () {
          return {
            template: Vue.component('common', {
              template: `<div class="action-boxs">
                            <div>{{getNodeName}}</div>
                          </div>`,
              data() {
                return { data: {} }
              },
              computed: {
                getNodeName() {
                  return i18n.t(this?.mtData?.nodeName)
                }
              },
              props: {
                mtData: {
                  // eslint-disable-line
                  // 拿到数据
                  type: Object,
                  default: () => {}
                }
              }
            })
          }
        },
        dataSource: [],
        id: 'id',
        text: 'nodeName',
        child: 'fileNodeResponseList'
      },
      pageConfig: [
        {
          toolbar: rdToolBar,
          gridId:
            this.$permission.gridId['purchase'][this.$route?.query?.source]['detail']['tabs'][
              'documents'
            ],
          grid: {
            allowFiltering: true,
            allowPaging: false,
            columnData: rdColumnData,
            dataSource: []
          }
        }
      ]
    }
  },
  computed: {
    isDisabled() {
      // 非草稿和已完成和关闭要禁止编辑
      return this.detailInfo?.status === 8 || this.detailInfo?.status === -1
    }
  },
  mounted() {
    if (!this.isDisabled) {
      this.pageConfig[0].toolbar.push('delete')
    }
    this.getFileFolderData()
  },

  methods: {
    getFileFolderData() {
      let _params = {
        docId: this.$route.query.rfxId
      }
      this.$API.rfxFiles.queryPorFileNodeByRfxId(_params).then((res) => {
        this.$set(this.treeViewData, 'dataSource', res.data)
        let treeDataHash = {}
        let recursiveQuery = function (children) {
          for (let item of children) {
            treeDataHash[item.id] = item
            if (Array.isArray(item.fileNodeResponseList) && item.fileNodeResponseList.length > 0) {
              recursiveQuery(item.fileNodeResponseList)
            }
          }
        }
        recursiveQuery(res.data)
        this.treeDataHash = treeDataHash
        if (Array.isArray(res.data) && res.data.length) {
          this.selectNodeId = res.data[0]['id']
          this.$set(this.pageConfig[0].grid, 'asyncConfig', {
            url: this.$API.rfxFiles.queryPorFileByRfxId,
            params: {
              docId: this.$route.query.rfxId,
              parentId: this.selectNodeId
            },
            methods: 'get',
            recordsPosition: 'data'
          })
          this.selectedNodes = [res.data[0]['id']]
          this.expandedNodes = [res.data[0]['id']]
        }
      })
    },
    nodeSelected(event) {
      if (event?.nodeData?.id) {
        this.selectNodeId = event?.nodeData?.id
        if (this.treeDataHash[this.selectNodeId].nodeCode.substring(0, 3) == 'sup') {
          if (this.pageConfig[0].grid.columnData.length == 6) {
            this.pageConfig[0].grid.columnData.push({
              field: 'supplierName',
              headerText: this.$t('上传供应商')
            })
          }
          // 供方
          this.$set(this.pageConfig[0], 'toolbar', [
            { id: 'pull', icon: 'icon_solid_Download', title: this.$t('下载') }
          ])
          this.$set(this.pageConfig[0].grid.columnData[1], 'cellTools', [
            {
              id: 'download',
              icon: 'icon_solid_Download',
              title: this.$t('下载')
            }
          ])
        } else {
          // 采方
          if (this.pageConfig[0].grid.columnData.length == 7) {
            this.pageConfig[0].grid.columnData.splice(-1, 1)
          }
          if (this.detailInfo.transferStatus > 31) {
            /*--------应标叔要求不再根据当前阶段状态限制新增删除附件操作--------------*/
            // this.$set(this.pageConfig[0], 'toolbar', [
            //   {
            //     id: 'pull',
            //     icon: 'icon_solid_Download',
            //     title: this.$t('下载')
            //   }
            // ])
            this.$set(this.pageConfig[0].grid.columnData[1], 'cellTools', [
              {
                id: 'download',
                icon: 'icon_solid_Download',
                title: this.$t('下载')
              }
            ])
          } else {
            console.log('当前流转阶段', this.detailInfo.transferStatus)
          }
          if (event.nodeData.hasChildren == false) {
            const toolbar = []
            if (![20, 100].includes(this.detailInfo.transferStatus)) {
              toolbar.push({
                id: 'Upload',
                icon: 'icon_solid_upload',
                title: this.$t('上传')
              })
            }
            toolbar.push({
              id: 'pull',
              icon: 'icon_solid_Download',
              title: this.$t('下载')
            })
            this.$set(this.pageConfig[0], 'toolbar', toolbar)
          } else {
            this.$set(this.pageConfig[0], 'toolbar', [
              {
                id: 'pull',
                icon: 'icon_solid_Download',
                title: this.$t('下载')
              }
            ])
          }
          if (!this.isDisabled) {
            this.pageConfig[0].toolbar.push({
              id: 'delete',
              icon: 'icon_solid_Delete',
              title: this.$t('删除')
            })
          }
          this.$set(this.pageConfig[0].grid.columnData[1], 'cellTools', [
            {
              id: 'download',
              icon: 'icon_solid_Download',
              title: this.$t('下载')
            },
            {
              id: 'delete',
              icon: 'icon_solid_Delete',
              title: this.$t('删除')
            }
          ])
        }
        this.$set(this.pageConfig[0].grid, 'asyncConfig', {
          url: this.$API.rfxFiles.queryPorFileByRfxId,
          params: {
            docId: this.$route.query.rfxId,
            parentId: this.selectNodeId
          },
          methods: 'get',
          recordsPosition: 'data'
        })
      }
    },
    handleClickToolBar(e) {
      console.log('use-handleClickToolBar', e)
      if (e.toolbar.id === 'Upload') {
        // 存在供应商报价，禁止上传外部附件
        const selectNodeCode = this.treeDataHash[this.selectNodeId].nodeCode
        if (selectNodeCode === 'pur_out_file' && this.detailInfo.uploadFile === 0) {
          this.$toast({ content: this.$t('已存在供应商报价，无法补充上传'), type: 'warning' })
          return
        }
        this.$dialog({
          modal: () =>
            import(/* webpackChunkName: "components/upload" */ 'COMPONENTS/Upload/index.vue'),
          data: {
            title: this.$t('上传')
          },
          success: (data) => {
            this.handleUploadFiles(data)
          }
        })
      } else if (e.toolbar.id == 'pull') {
        let _selectRows = e.gridRef.getMtechGridRecords()
        if (_selectRows.length == 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        } else {
          for (let item of _selectRows) {
            this.$API.fileService.downloadPrivateFile({ id: item.sysFileId }).then((res) => {
              download({
                fileName: item.fileName,
                blob: new Blob([res.data])
              })
            })
          }
        }
      } else if (e.toolbar.id == 'delete') {
        let _selectRows = e.gridRef.getMtechGridRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        } else {
          this.handleBatchDelete(_selectRows)
        }
      }
    },
    //执行上传文件
    handleUploadFiles(data) {
      let { id, fileName, fileSize, fileType, url, remoteUrl, sysName } = data
      let _params = {
        docId: this.$route.query.rfxId,
        parentId: this.selectNodeId,
        fileName: fileName,
        fileSize: fileSize,
        fileType: fileType,
        remoteUrl: remoteUrl,
        sysFileId: id,
        sysName: sysName,
        url: url
        // docType: "",
        // id: 0,
        // nodeId: 0,
        // nodeName: "",
        // nodeType: 0,
        // sortValue: 0,
      }
      this.$API.rfxFiles.saveRfxHeaderFile(_params).then(() => {
        this.$toast({
          content: this.$t('上传成功'),
          type: 'success'
        })
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },
    //批量删除操作
    handleBatchDelete(_selectGridRecords) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      this.handleDelete(_selectIds)
    },
    //删除文件
    handleDelete(ids) {
      let _params = {
        idList: ids
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.rfxFiles.deleteSourcingFileById(_params).then(() => {
            this.$store.commit('endLoading')
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    //单元格按钮，点击
    handleClickCellTool(e) {
      if (e.tool.id == 'download') {
        if (e.data.sysFileId == -999) {
          this.$toast({
            content: this.$t('报价未截止不能下载'),
            type: 'warning'
          })
          return
        }

        this.$API.fileService.downloadPrivateFile({ id: e.data.sysFileId }).then((res) => {
          download({ fileName: e.data.fileName, blob: new Blob([res.data]) })
        })
      } else if (e.tool.id == 'delete') {
        this.handleDelete([e.data.id])
      }
    },
    //单元格title文字点击，文件名点击预览
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
      if (e.field == 'fileName') {
        let params = {
          id: e?.data?.sysFileId || e?.data?.id,
          useType: 2
        }
        this.$API.fileService.getMtPreview(params).then((res) => {
          window.open(res.data)
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.r-d-container {
  width: 100%;
  margin-top: 20px;
  // margin-left: 10px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  .tree-view--wrap {
    min-width: 150px;
    background: #fff;
    margin-right: 20px;
    .trew-node--add {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      height: 50px;
      padding: 0 20px;
      color: #4f5b6d;
      box-shadow: inset 0 -1px 0 0 rgba(232, 232, 232, 1);
      .node-title {
        font-size: 14px;
        color: #292929;
        display: inline-block;
        position: relative;
        padding-left: 10px;

        &:before {
          content: '';
          position: absolute;
          width: 3px;
          height: 14px;
          background: #6386c1;
          border-radius: 0 2px 2px 0;
          left: 0;
          top: 0;
        }
      }
    }
    .tree-view--template {
      width: 100%;
      padding-top: 10px;
      padding-left: 20px;
    }
    /deep/.mt-commom-tree-view {
      padding-left: 0;
    }
    // /deep/.e-list-parent {
    //   padding-left: 10px;
    // }
    /deep/ {
      .e-treeview .e-list-item:not(.e-has-child).e-active,
      .e-treeview .e-list-item:not(.e-has-child).e-hover {
        background: #e8e8e8 !important;
      }
      .mt-commom-tree-view .mt-tree-view .e-treeview .e-list-item.e-active > .e-fullrow {
        border: 1px solid #e8e8e8 !important;
        background: #e8e8e8;
        opacity: 1;
      }

      .mt-commom-tree-view .expandedLevel1 {
        background: transparent !important;
      }
    }
  }
}

::v-deep {
  .e-grid td.e-rowcell {
    height: 40px !important;
  }
  .grid-edit-column {
    height: 40px !important;
  }
}
</style>
