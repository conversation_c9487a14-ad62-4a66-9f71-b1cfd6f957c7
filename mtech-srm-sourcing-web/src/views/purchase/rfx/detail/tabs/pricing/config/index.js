import { i18n } from '@/main.js'
import { Formatter } from '@/utils/ej/dataGrid'
import selectedItemCode from '../components/selectItemCode.vue' // 物料

// 提交定标审批之后
// 定标页面的保存、提交、驳回议价、发起下一轮
export const canEdit = ({ transferStatus }) => ![20, 100].includes(transferStatus)

export const tabToolbar = (status, transferStatus, type) => {
  const commonToolbar = [
    {
      id: 'Save',
      icon: 'icon_solid_Createorder',
      title: i18n.t('保存'),
      visibleCondition: () => {
        return status != 8 && status != -1 && canEdit({ transferStatus }) //已完成
      }
    },
    {
      id: 'submit',
      icon: 'icon_solid_edit',
      title: i18n.t('提交审批'),
      visibleCondition: () => {
        return status != 8 && status != -1 && canEdit({ transferStatus }) //已完成
      }
    },
    {
      id: 'redirectOA',
      icon: 'icon_solid_edit',
      title: i18n.t('OA审批进度'),
      visibleCondition: () => {
        return status != -1 //已完成
      }
    },
    {
      id: 'rejectBargain',
      icon: 'icon_solid_edit',
      title: i18n.t('驳回议价'),
      visibleCondition: () => {
        return canEdit({ transferStatus }) //已完成
      }
    },
    {
      id: 'startNewRound',
      icon: 'icon_solid_edit',
      title: i18n.t('发起新一轮'),
      visibleCondition: () => {
        return canEdit({ transferStatus }) //已完成
      }
    },
    {
      id: 'createMoldBuildTable',
      icon: 'icon_solid_edit',
      title: i18n.t('生成模具制造审批表'),
      visibleCondition: () => {
        // 只有流程状态为100（已完成时）允许去生成审批表
        return transferStatus == 100
      }
    },
    { id: 'relative', icon: 'icon_solid_edit', title: i18n.t('比价助手') },
    {
      id: 'batchVmi',
      icon: 'icon_solid_edit',
      title: i18n.t('批量VMI'),
      visibleCondition: () => {
        return status != 8 && status != -1 && canEdit({ transferStatus }) //已完成
      }
    },
    {
      id: 'exportPricing',
      icon: 'icon_solid_edit',
      title: i18n.t('导出')
    }
  ]
  if (type === 'quota') {
    let _toolbar = commonToolbar.filter((item) => item.id !== 'exportPricing')
    return [
      ..._toolbar,
      {
        id: 'upload',
        icon: 'icon_solid_Createorder',
        title: i18n.t('导入'),
        visibleCondition: () => {
          return type && status != 8 && status != -1 && canEdit({ transferStatus })
        }
      },
      {
        id: 'download',
        icon: 'icon_solid_Createorder',
        title: i18n.t('导出')
      }
    ]
  }
  return commonToolbar
}

export const quotaColumnData = (that) => {
  return [
    // 添加isPrimaryKey
    {
      field: 'id',
      width: 0,
      isPrimaryKey: true,
      visible: false
    },
    {
      field: 'lineNo',
      headerText: i18n.t('行号'),
      width: 80,
      allowFiltering: false,
      allowEditing: false
    },
    {
      field: 'siteCode',
      headerText: i18n.t('工厂编码'),
      allowEditing: false
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      allowEditing: false
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      allowEditing: [34].includes(that.detailInfo?.transferStatus),
      editTemplate: () => {
        return {
          template: selectedItemCode
        }
      }
    },
    {
      field: 'untaxedUnitPrice',
      headerText: i18n.t('单价（未税）'),
      allowEditing: false
    },
    {
      field: 'allocationRatio',
      headerText: i18n.t('配额'),
      allowEditing: true,
      editType: 'number',
      edit: {
        params: {
          min: 0,
          max: 100,
          precision: '0'
        }
      },
      formatter: ({ field }, item) => {
        let val = Number(item[field]).toFixed(0) || 0
        return val + '%'
      }
    },
    {
      field: 'minSplitQuantity',
      headerText: i18n.t('最小起拆点'),
      width: 100,
      allowFiltering: false,
      editType: 'numericedit',
      edit: {
        params: {
          min: 0
        }
      }
    },
    {
      field: 'stepNum',
      headerText: i18n.t('阶梯数量'),
      allowEditing: false,
      valueConverter: {
        type: 'function',
        filter: (e, data) => {
          return data.stepValue || ''
        }
      }
    },
    {
      field: 'temporaryItemCode',
      headerText: i18n.t('临时物料编码'),
      allowEditing: false
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料名称'),
      allowEditing: false
    },
    {
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      allowEditing: false
    },
    {
      field: 'taxedUnitPrice',
      headerText: i18n.t('单价（含税）'),
      allowEditing: false
    },
    {
      field: 'quotaEffectiveStartDate',
      headerText: i18n.t('报价有效期从'),
      formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
      allowEditing: false,
      editType: 'datepickeredit',
      type: 'date',
      edit: {
        params: {
          format: 'yyyy-MM-dd'
        }
      }
    },
    {
      field: 'quotaEffectiveEndDate',
      headerText: i18n.t('报价有效期止'),
      formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
      allowEditing: false,
      editType: 'datepickeredit',
      type: 'date',
      edit: {
        params: {
          format: 'yyyy-MM-dd'
        }
      }
    },
    {
      field: 'sourceType',
      headerText: i18n.t('来源'),
      allowEditing: false,
      valueConverter: {
        type: 'map',
        map: {
          0: i18n.t('当前来源'),
          1: i18n.t('历史配额')
        }
      }
    },
    {
      field: 'unitCode',
      headerText: i18n.t('基本单位'),
      allowEditing: false
    },
    {
      field: 'purUnitName',
      headerText: i18n.t('订单单位'),
      allowEditing: false
    },
    {
      field: 'priceUnitName',
      headerText: i18n.t('价格单位'),
      allowEditing: false
    },
    {
      field: 'minPurQuantity',
      headerText: i18n.t('最小采购量'),
      allowEditing: false
    },
    {
      field: 'quoteAttribute',
      headerText: i18n.t('报价属性'),
      allowEditing: false,
      valueConverter: {
        type: 'map',
        map: {
          mailing_price: i18n.t('寄售价'),
          standard_price: i18n.t('标准价'),
          K: i18n.t('寄售价'),
          '': i18n.t('标准价'),
          outsource: i18n.t('委外价')
        }
      }
    },
    {
      field: 'ranking',
      headerText: i18n.t('名次'),
      allowEditing: false
    }
  ]
}
export const agQuotaColumnData = (that) => {
  return [
    {
      option: 'checkboxSelection',
      width: 55,
      hide: [20, 100].includes(that.detailInfo?.transferStatus)
    },
    {
      field: 'lineNo',
      headerText: i18n.t('行号'),
      width: 80
    },
    {
      field: 'siteCode',
      headerText: i18n.t('工厂编码')
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称')
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      allowEditing: [34].includes(that.detailInfo?.transferStatus)
    },
    {
      field: 'untaxedUnitPrice',
      headerText: i18n.t('单价（未税）')
    },
    {
      field: 'allocationRatio',
      headerText: i18n.t('配额'),
      allowEditing: true,
      width: 100
    },
    {
      field: 'minSplitQuantity',
      headerText: i18n.t('最小起拆点'),
      width: 120,
      allowEditing: true
    },
    {
      field: 'stepNum',
      headerText: i18n.t('阶梯数量')
    },
    {
      field: 'temporaryItemCode',
      headerText: i18n.t('临时物料编码')
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料名称')
    },
    {
      field: 'siteName',
      headerText: i18n.t('工厂名称')
    },
    {
      field: 'taxedUnitPrice',
      headerText: i18n.t('单价（含税）')
    },
    {
      field: 'quotaEffectiveStartDate',
      headerText: i18n.t('报价有效期从')
    },
    {
      field: 'quotaEffectiveEndDate',
      headerText: i18n.t('报价有效期止')
    },
    {
      field: 'sourceType',
      headerText: i18n.t('来源')
    },
    {
      field: 'unitCode',
      headerText: i18n.t('基本单位')
    },
    {
      field: 'purUnitName',
      headerText: i18n.t('订单单位')
    },
    {
      field: 'priceUnitName',
      headerText: i18n.t('价格单位')
    },
    {
      field: 'minPurQuantity',
      headerText: i18n.t('最小采购量')
    },
    {
      field: 'quoteAttribute',
      headerText: i18n.t('报价属性')
    },
    // 以下列为隐藏列，物料更新时候使用
    {
      field: 'itemId',
      headerText: i18n.t('物料ID'),
      hide: true
    },
    {
      field: 'spec',
      headerText: i18n.t('规格描述'),
      hide: true
    },
    {
      field: 'material',
      headerText: i18n.t('材质'),
      hide: true
    },
    {
      field: 'unitName',
      headerText: i18n.t('单位名称'),
      hide: true
    },
    {
      field: 'categoryId',
      headerText: i18n.t('品类ID'),
      hide: true
    },
    {
      field: 'categoryCode',
      headerText: i18n.t('品类Code'),
      hide: true
    },
    {
      field: 'categoryName',
      headerText: i18n.t('品类名称'),
      hide: true
    }
  ]
}
export const generalQuotaColumnData = (that) => {
  return [
    // {
    //   option: 'checkboxSelection',
    //   width: 55,
    //   hide: [20, 100].includes(that.detailInfo?.transferStatus)
    // },
    {
      fieldCode: 'lineNo',
      fieldName: i18n.t('行号'),
      width: 80
    },
    {
      fieldCode: 'siteCode',
      fieldName: i18n.t('工厂编码')
    },
    {
      fieldCode: 'supplierName',
      fieldName: i18n.t('供应商名称'),
      tableName: 'rfx_bidding_item'
    },
    {
      fieldCode: 'itemCode',
      fieldName: i18n.t('物料编码'),
      editable: [34].includes(that.detailInfo?.transferStatus)
    },
    {
      fieldCode: 'untaxedUnitPrice',
      fieldName: i18n.t('单价（未税）'),
      tableName: 'rfx_bidding_item'
    },
    {
      fieldCode: 'allocationRatio',
      fieldName: i18n.t('配额'),
      tableName: 'rfx_bidding_item',
      editable: true,
      width: 100
    },
    {
      fieldCode: 'minSplitQuantity',
      fieldName: i18n.t('最小起拆点'),
      width: 120,
      editable: true
    },
    {
      fieldCode: 'stepNum',
      fieldName: i18n.t('阶梯数量')
    },
    {
      fieldCode: 'temporaryItemCode',
      fieldName: i18n.t('临时物料编码')
    },
    {
      fieldCode: 'itemName',
      fieldName: i18n.t('物料名称')
    },
    {
      fieldCode: 'siteName',
      fieldName: i18n.t('工厂名称')
    },
    {
      fieldCode: 'taxedUnitPrice',
      fieldName: i18n.t('单价（含税）')
    },
    {
      fieldCode: 'quotaEffectiveStartDate',
      fieldName: i18n.t('报价有效期从')
    },
    {
      fieldCode: 'quotaEffectiveEndDate',
      fieldName: i18n.t('报价有效期止')
    },
    {
      fieldCode: 'sourceType',
      fieldName: i18n.t('来源')
    },
    {
      fieldCode: 'unitCode',
      fieldName: i18n.t('基本单位')
    },
    {
      fieldCode: 'purUnitName',
      fieldName: i18n.t('订单单位')
    },
    {
      fieldCode: 'priceUnitName',
      fieldName: i18n.t('价格单位')
    },
    {
      fieldCode: 'minPurQuantity',
      fieldName: i18n.t('最小采购量'),
      tableName: 'rfx_bidding_item'
    },
    {
      fieldCode: 'quoteAttribute',
      fieldName: i18n.t('报价属性'),
      tableName: 'rfx_bidding_item'
    },
    // 以下列为隐藏列，物料更新时候使用
    {
      fieldCode: 'itemId',
      fieldName: i18n.t('物料ID'),
      hide: true
    },
    {
      fieldCode: 'spec',
      fieldName: i18n.t('规格描述'),
      hide: true
    },
    {
      fieldCode: 'material',
      fieldName: i18n.t('材质'),
      hide: true
    },
    {
      fieldCode: 'unitName',
      fieldName: i18n.t('单位名称'),
      hide: true
    },
    {
      fieldCode: 'categoryId',
      fieldName: i18n.t('品类ID'),
      hide: true
    },
    {
      fieldCode: 'categoryCode',
      fieldName: i18n.t('品类Code'),
      hide: true
    },
    {
      fieldCode: 'categoryName',
      fieldName: i18n.t('品类名称'),
      hide: true
    }
  ]
}
