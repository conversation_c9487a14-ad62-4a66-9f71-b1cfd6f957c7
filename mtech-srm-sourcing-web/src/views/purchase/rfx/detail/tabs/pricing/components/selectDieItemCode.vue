<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <mt-input
        id="itemDieResponse.dieFormalCode"
        v-model="displayValue"
        :disabled="isDisabled"
        @input="input"
        @keyup.native="keyup"
        class="field-input"
      ></mt-input>
      <mt-icon
        v-if="isDisplay"
        class="field-icon"
        name="icon_list_refuse"
        @click.native="handleClear"
      ></mt-icon>
      <mt-icon
        v-if="isDisplay"
        class="field-icon"
        name="icon_input_search"
        @click.native="showDialog"
      ></mt-icon>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      inputDisabled: false,
      allowEditing: true,
      displayValue: null
    }
  },
  computed: {
    isDisabled() {
      return this.inputDisabled
    },
    isDisplay() {
      return this.allowEditing && this.inputDisabled
    }
  },
  mounted() {
    if (!this.allowEditing) return
    // //0手工定价  2价格记录   定标页面，未返回itemExtMap?.referChannel
    // if ([2].includes(this?.editInstance?.rowData?.itemExtMap?.referChannel)) {
    //   this.inputDisabled = true;
    // } else {
    //   this.inputDisabled = false;
    // }
  },
  methods: {
    input() {
      // this.$emit("input", this.displayValue);
    },
    keyup(e) {
      let _value = e.target.value
      if (_value) {
        _value = _value.replace(/[\W]/g, '')
        if (_value.length && _value.length > 50) {
          _value = _value.substring(0, 50)
        }
        this.displayValue = _value
      } else {
        this.displayValue = null
      }
      this.$emit('input', this.displayValue)
    },
    handleClear() {
      this.$emit('input', null)
    },
    showDialog() {
      if (sessionStorage.getItem('dieItemParam') == null) {
        this.$toast({
          content: this.$t('当前行，无物料数据，不可以选择模具'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        modal: () => import('COMPONENTS/NormalEdit/checkSelectItemDie/components/selectGrid'),
        data: {
          title: this.$t('选择模具'),
          rfxId: sessionStorage.getItem('temp_rfxId'),
          itemParam: JSON.parse(sessionStorage.getItem('dieItemParam')),
          source: this.$route?.query?.source
        },
        success: (data) => {
          this.displayValue = data[0]?.itemCode
          this.$emit('input', data[0])
        }
      })
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(value) {
        this.displayValue = value
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;

  .in-cell {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .field-input {
      flex: 1;
    }

    .field-icon {
      width: 20px;
      flex-shrink: 0;
      margin-left: 10px;
      cursor: pointer;
    }

    > .mt-icons {
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
</style>
