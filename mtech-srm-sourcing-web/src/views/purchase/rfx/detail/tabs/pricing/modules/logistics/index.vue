<template>
  <div class="flex-full-height">
    <mt-tabs
      class="custom-tab"
      :tab-id="$utils.randomString()"
      :e-tab="false"
      :data-source="tabSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <!-- 议价、当前报价、历史报价公用 -->
    <CustomAgGrid
      ref="CustomAgGrid"
      v-clickoutside="clickoutside"
      :search-config="searchConfig"
      :toolbar="toolbar"
      :columns="columns"
      :suppress-row-transform="true"
      :row-data="tableData"
      :animate-rows="false"
      :is-stop-editing-on-blur="isStopEditingOnBlur"
      :get-row-id="getRowId"
      :context="context"
      @cell-value-changed="cellValueChanged"
      @handleClickToolbar="handleClickToolbar"
      @onGridReady="onGridReady"
      @row-selected="onRowSelected"
      @refresh="refresh"
      @search="search"
    >
    </CustomAgGrid>

    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadInitBalanceRef"
      :down-template-name="$t('配额导入模板')"
      :down-template-params="downTemplateParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import CustomAgGrid from '@/components/CustomAgGrid'
import cellFile from '@/components/AgCellComponents/cellFile'
import checkbox from '@/components/AgCellComponents/checkbox'
import cellLink from '@/components/AgCellComponents/cellLink'
import MixIn from '../../config/mixin.js'
export default {
  inject: ['reload'],
  components: {
    CustomAgGrid,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default,
    // eslint-disable-next-line
    cellFile,
    // eslint-disable-next-line
    checkbox,
    // eslint-disable-next-line
    cellLink // 单元格点击跳转
  },
  mixins: [MixIn],
  data() {
    return {}
  },
  watch: {},
  computed: {
    // allowEditing() {
    //   return canEdit(this.detailInfo?.transferStatus)
    // }
  },
  beforeDestroy() {},
  beforeMount() {},
  async mounted() {},
  methods: {}
}
</script>

<style lang="scss">
.price-dialog {
  width: 80% !important;
  height: 100% !important;
}
.rise-price .dialog-content {
  white-space: pre-line;
}
</style>
<style lang="scss" scoped>
.custom-tab /deep/.mt-tabs-container {
  background: #ffffff;
}
</style>
