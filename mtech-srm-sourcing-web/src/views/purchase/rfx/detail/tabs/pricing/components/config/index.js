import { createEditInstance, EditConfig, Formatter } from '@/utils/ej/dataGrid/index'
import Vue from 'vue'
import { getValueByPath } from '@/utils/obj'
import { i18n } from '@/main.js'
export const columnData = ({
  // siteNameDataSource = [],
  // unitDataSource = [],
  // deliveryPlaceDataSource = [],
  // procurementDataSource = [],
  visible = true,
  self
} = {}) => {
  //选择工厂之后调接口
  const editInstance = createEditInstance().onInput((ctx, { field, value }) => {
    if (field === 'siteName') {
      // 工厂编辑带出工厂编码
      const { dataSource, fields } = ctx.getOptions(field)
      const value = ctx.getValueByField('siteName')
      const row = dataSource.find((e) => e[fields?.value || 'value'] === value)
      if (row) {
        let param = {
          organizationId: row.organizationId,
          businessOrganizationTypeCode: 'BUORG002ADM'
        }
        ctx.setValueByField('siteCode', row.organizationCode)

        ctx.setValueByField('siteId', row.organizationId)
        self.$API.priceService.getByOrgIdAndBgOrgTypeCode(param).then((res) => {
          //渲染采购组织的下拉框，通过采购组织的file获取对dataSource进行赋值
          ctx.setOptions('purGroupName', {
            dataSource: res.data
          })
        })
      }
    }
    if (field === 'purUnitName') {
      // 订单单位编辑带出订单编码
      const { dataSource, fields } = ctx.getOptions(field)
      const row = dataSource.find((e) => e[fields?.value || 'value'] === value)
      if (row) {
        ctx.setValueByField('purUnitCode', row.unitCode)
      }
    }
    if (field === 'purGroupName') {
      // 订单单位编辑带出订单编码
      const { dataSource, fields } = ctx.getOptions(field)
      const row = dataSource.find((e) => e[fields?.value || 'value'] === value)
      if (row) {
        ctx.setValueByField('purGroupCode', row.organizationCode)
        ctx.setValueByField('purGroupId', row.id)
      }
    }
  })
  return [
    {
      width: '60',
      type: 'checkbox',
      allowEditing: false
    },
    {
      field: 'syncStatus',
      visible,
      headerText: i18n.t('价格记录状态'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          dataSource: [
            { text: i18n.t('未生成'), value: 0 },
            { text: i18n.t('已生成'), value: 1 }
          ],
          disabled: true,
          readonly: true
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        switch (Number(cellVal)) {
          case 0:
            return i18n.t('未生成')
          case 1:
            return i18n.t('已生成')
          default:
            return cellVal
        }
      },
      allowEditing: false
    },
    {
      field: 'syncSapStatus',
      visible,
      headerText: i18n.t('导入Sap状态'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          dataSource: [
            { text: i18n.t('无需导入'), value: 0 },
            { text: i18n.t('未导入'), value: 1 },
            { text: i18n.t('导入中'), value: 2 },
            { text: i18n.t('导入成功'), value: 3 },
            { text: i18n.t('导入失败'), value: 4 }
          ],
          disabled: true,
          readonly: true
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        switch (Number(cellVal)) {
          case 0:
            return i18n.t('无需导入')
          case 1:
            return i18n.t('未导入')
          case 2:
            return i18n.t('导入中')
          case 3:
            return i18n.t('导入成功')
          case 4:
            return i18n.t('导入失败')
          default:
            return cellVal
        }
      },
      allowEditing: false
    },
    {
      field: 'vendorCompleteFlag',
      visible,
      headerText: i18n.t('供方报价状态'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          dataSource: [
            { text: i18n.t('未报价'), value: 0 },
            { text: i18n.t('已报价'), value: 1 }
          ],
          disabled: true,
          readonly: true
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        switch (Number(cellVal)) {
          case 0:
            return i18n.t('未报价')
          case 1:
            return i18n.t('已报价')
          default:
            return cellVal
        }
      },
      allowEditing: false
    },
    {
      field: 'syncResultMsg',
      visible,
      headerText: i18n.t('同步结果'),
      allowEditing: false
    },
    {
      field: 'approvalStatus',
      visible,
      headerText: i18n.t('审核状态'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          dataSource: [
            { text: i18n.t('待审核'), value: 0 },
            { text: i18n.t('审核驳回'), value: -1 },
            { text: i18n.t('采购员审核通过'), value: 1 },
            { text: i18n.t('采购经理审核通过'), value: 2 }
          ],
          disabled: true,
          readonly: true
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        switch (Number(cellVal)) {
          case 0:
            return i18n.t('待审核')
          case -1:
            return i18n.t('审核驳回')
          case 1:
            return i18n.t('采购员审核通过')
          case 2:
            return i18n.t('采购经理审核通过')
          default:
            return cellVal
        }
      },
      allowEditing: false
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      allowEditing: false
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料名称'),
      allowEditing: false
    },
    {
      field: 'unitName',
      headerText: i18n.t('单位'),
      allowEditing: false
    },
    {
      field: 'priceUnitName',
      headerText: i18n.t('价格单位'),
      // cssClass: 'field-content',
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'select',
          dataSource: [
            { text: '1', value: '1' },
            { text: '1000', value: '1000' }
          ]
        })
      })
    },
    {
      field: 'purUnitName',
      headerText: i18n.t('订单单位名称'),
      edit: editInstance.create({
        getEditConfig: EditConfig.readonly
      })
    },
    {
      field: 'purUnitCode', // 订单单位编码
      headerText: i18n.t('订单单位编码'),
      edit: editInstance.create({
        getEditConfig: EditConfig.readonly
      })
    },
    {
      field: 'supplierItemCode',
      headerText: i18n.t('供应商物料编码'),
      allowEditing: false
    },
    {
      field: 'priceClassification',
      headerText: i18n.t('价格分类'),
      allowEditing: false,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          dataSource: [
            { text: i18n.t('暂估价格'), value: 'predict_price' },
            { text: i18n.t('SRM价格'), value: 'srm_price' },
            { text: i18n.t('执行价格'), value: 'execute_price' },
            { text: i18n.t('基价'), value: 'basic_price' }
          ],
          disabled: true,
          readonly: true
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        switch (cellVal) {
          case 'predict_price':
            return i18n.t('暂估价格')
          case 'srm_price':
            return i18n.t('SRM价格')
          case 'execute_price':
            return i18n.t('执行价格')
          case 'basic_price':
            return i18n.t('基价')
          default:
            return cellVal
        }
      }
    },
    {
      field: 'conversionRate',
      headerText: i18n.t('转换率'),
      allowEditing: false
    },
    {
      field: 'currencyExchangeValue',
      headerText: i18n.t('汇率'),
      allowEditing: false
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      allowEditing: false
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      allowEditing: false
    },
    {
      field: 'siteName',
      headerText: i18n.t('工厂'),
      headerTemplate: () => {
        return {
          template: Vue.component('requiredCell', {
            template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{fieldName}}</span>
                  </div>
                `,
            data() {
              return {
                data: {},
                fieldName: ''
              }
            },
            mounted() {
              this.fieldName = i18n.t('工厂')
            }
          })
        }
      },
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          dataSource: self.siteNameDataSource || [], //
          fields: { value: 'organizationName', text: 'organizationName' },
          placeholder: i18n.t('请选择工厂')
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return self.siteNameDataSource.find((e) => e.value === cellVal)?.text ?? cellVal
      }
    },
    {
      field: 'siteCode',
      headerText: i18n.t('工厂编码'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          readonly: true
        })
      })
    },
    {
      field: 'siteId',
      headerText: i18n.t('工厂Id'),
      width: 1,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          readonly: true
        })
      })
    },
    {
      field: 'purGroupName',
      headerText: i18n.t('采购组织'),
      cssClass: 'click',
      headerTemplate: () => {
        return {
          template: Vue.component('requiredCell', {
            template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{fieldName}}</span>
                  </div>
                `,
            data() {
              return {
                data: {},
                fieldName: ''
              }
            },
            mounted() {
              this.fieldName = i18n.t('采购组织')
            }
          })
        }
      },
      edit: editInstance.create({
        getEditConfig: ({ rowData }) => ({
          type: 'mt-select',
          created: async function () {
            const dataSource = await self.queryProcurementDataSource(
              rowData.siteId,
              rowData.siteCode
            )
            editInstance.setOptions('purGroupName', {
              dataSource
            })
          },
          // dataSource: [],
          dataSource: self.procurementDataSource,
          fields: { value: 'organizationName', text: 'organizationName' },
          placeholder: i18n.t('请选择采购组织')
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return (
          // procurementDataSource.find((e) => e.value === cellVal)?.text ??
          cellVal
        )
      }
    },
    {
      field: 'purGroupCode',
      headerText: i18n.t('采购组织编码'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          readonly: true
        })
      })
    },
    {
      field: 'purGroupId',
      // visible: false,
      width: 1,
      headerText: i18n.t('采购组织id'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          readonly: true
        })
      })
    },
    {
      field: 'deliveryPlace',
      headerText: i18n.t('直送地'),
      headerTemplate: () => {
        return {
          template: Vue.component('requiredCell', {
            template: `
                  <div class="headers">
                    <span style="color: red">*</span>
                    <span class="e-headertext">{{fieldName}}</span>
                  </div>
                `,
            data() {
              return {
                data: {},
                fieldName: ''
              }
            },
            mounted() {
              this.fieldName = i18n.t('直送地')
            }
          })
        }
      },
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          dataSource: self.deliveryPlaceDataSource,
          fields: { value: 'itemName', text: 'itemName' },
          placeholder: i18n.t('直送地')
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return self.deliveryPlaceDataSource.find((e) => e.value === cellVal)?.text ?? cellVal
      }
    },
    {
      field: 'bidTaxRateValue',
      headerText: i18n.t('税率'),
      allowEditing: false
    },
    {
      field: 'bidCurrencyName',
      headerText: i18n.t('原币'),
      allowEditing: false
    },
    {
      field: 'bidCurrencyCode',
      headerText: i18n.t('原币编码'),
      allowEditing: false
    },
    {
      field: 'localCurrencyName',
      headerText: i18n.t('本币名称'),
      with: 200,
      allowEditing: false
    },
    {
      field: 'localCurrencyCode',
      with: 200,
      headerText: i18n.t('本币编码'),
      visible: false,
      allowEditing: false
    },
    {
      field: 'quoteAttribute',
      headerText: i18n.t('报价属性'),
      allowEditing: false,
      edit: editInstance.create({
        getEditConfig: () => ({
          disabled: true,
          // readonly: true,
          type: 'mt-select',
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { text: i18n.t('标准价'), value: 'standard_price' },
            { text: i18n.t('寄售价'), value: 'mailing_price' },
            { text: i18n.t('委外价'), value: 'outsource' }
          ]
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        switch (cellVal) {
          case 'standard_price':
            return i18n.t('标准价')
          case 'mailing_price':
            return i18n.t('寄售价')
          case 'outsource':
            return i18n.t('委外价')
          default:
            return cellVal
        }
      }
    },
    {
      field: 'quoteMode',
      headerText: i18n.t('价格生效方式'),
      allowEditing: false,
      edit: editInstance.create({
        getEditConfig: () => ({
          disabled: true,
          readonly: true,
          type: 'mt-select',
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { text: i18n.t('按入库'), value: 'in_warehouse' },
            { text: i18n.t('按出库'), value: 'out_warehouse' },
            { text: i18n.t('按订单日期'), value: 'order_date' }
          ]
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        switch (cellVal) {
          case 'in_warehouse':
            return i18n.t('按入库')
          case 'out_warehouse':
            return i18n.t('按出库')
          case 'order_date':
            return i18n.t('按订单日期')
          default:
            return cellVal
        }
      }
    },
    {
      field: 'untaxedUnitPrice',
      headerText: i18n.t('单价（原币）'),
      allowEditing: false
    },
    {
      field: 'untaxedLocalUnitPrice',
      headerText: i18n.t('单价（本币）'),
      allowEditing: false
    },
    {
      field: 'quoteEffectiveStartDate',
      headerText: i18n.t('有效期从'),
      allowEditing: false,
      formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'date',
          format: 'yyyy-MM-dd',
          'time-stamp': true,
          'show-clear-button': false,
          readonly: true,
          disabled: true
        })
      })
    },
    {
      field: 'quoteEffectiveEndDate',
      headerText: i18n.t('有效期至'),
      allowEditing: false,
      formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'date',
          format: 'yyyy-MM-dd',
          'time-stamp': true,
          'show-clear-button': false,
          readonly: true,
          disabled: true
        })
      })
    },
    {
      field: 'leadTime',
      headerText: i18n.t('L/T'),
      allowEditing: false
    },
    {
      field: 'unconditionalLeadTime',
      headerText: i18n.t('无条件L/T'),
      allowEditing: false
    },
    {
      field: 'minPackageQuantity',
      headerText: i18n.t('最小包装数量'),
      allowEditing: false
    },
    {
      field: 'minPurQuantity',
      headerText: i18n.t('最小采购数量'),
      allowEditing: false
    },
    {
      field: 'directDestLocalPrice',
      headerText: i18n.t('直送地价格（本币）'),
      allowEditing: false
    },
    {
      field: 'directDestOriginalPrice',
      allowEditing: false,
      headerText: i18n.t('直送地价格（原币）')
    }
  ]
}
export const toolbarOne = (canExtendInfoRow, infoAppendStatus) => [
  {
    id: 'Save',
    icon: 'icon_solid_Createorder',
    title: i18n.t('保存'),
    visibleCondition: () => {
      return canExtendInfoRow == true && infoAppendStatus == 0
    }
  },
  {
    id: 'Expand',
    icon: 'icon_solid_edit',
    title: i18n.t('扩展'),
    visibleCondition: () => {
      return canExtendInfoRow == true && infoAppendStatus == 0
    }
  },
  { id: 'Release', icon: 'icon_solid_Createorder', title: i18n.t('发布') },
  {
    id: 'Del',
    icon: 'icon_solid_edit',
    title: i18n.t('删除'),
    visibleCondition: () => {
      return canExtendInfoRow == true && infoAppendStatus == 0
    }
  },
  { id: 'synchronous', icon: 'icon_solid_edit', title: i18n.t('同步') },
  { id: 'importSap', icon: 'icon_solid_edit', title: i18n.t('导入SAP') }
  // { id: "stop", icon: "icon_solid_Cancel", title: i18n.t("返回") },
]
export const toolbarTwo = [
  { id: 'Pass', icon: 'icon_solid_edit', title: i18n.t('审核通过') },
  { id: 'overrule', icon: 'icon_solid_edit', title: i18n.t('审核驳回') },
  { id: 'synchronous', icon: 'icon_solid_edit', title: i18n.t('同步') },
  { id: 'importSap', icon: 'icon_solid_edit', title: i18n.t('导入SAP') }
]
