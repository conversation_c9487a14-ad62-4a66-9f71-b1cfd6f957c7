/**
 * ckd
 */
export function isCombination(detailInfo) {
  return detailInfo.sourcingObjType === 'combination'
}

const EVENT_GRID_INPUT = '844031c0-ec6e-11ec-8f89-9dda7784f37e'

export function defineGridColumnsAfter(vm, rows, isChild, editInstance) {
  if (!isCombination(vm.detailInfo)) {
    return rows
  }
  editInstance.onInput((ctx, { field }) => {
    vm.$bus.$emit(EVENT_GRID_INPUT, {
      ctx,
      field
    })
  })

  // 子件需求数量禁止编辑
  if (isChild) {
    // 子件
    rows.forEach((row) => {
      if (['allocationQuantity', 'allocationRatio'].includes(row.field)) {
        // 子件分配数量禁止编辑
        delete row.edit
        row.allowEditing = false
      }
      if (row.field === 'allocationRatio') {
        row.formatter = ({ field }, item) => {
          return (item[field] || '0') + '%'
        }
      }
    })
  } else {
    // 母件
    if (vm.allowEditing) {
      rows.forEach((row) => {
        // 分配数量
        if (row.field === 'allocationQuantity') {
          row.edit = editInstance.create({
            getEditConfig: () => ({
              type: 'number',
              min: 0,
              max: Number.MAX_SAFE_INTEGER,
              precision: 2
            })
          })
          row.allowEditing = true
        } else if (row.field === 'allocationRatio') {
          row.editType = null
          row.allowEditing = true
          row.edit = editInstance.create({
            getEditConfig: () => ({
              type: 'number',
              min: 0,
              max: 100,
              precision: 2
            })
          })
          row.formatter = ({ field }, item) => {
            return (item[field] || '0') + '%'
          }
        }
      })
    }
  }

  return rows
}

export function mounted(vm) {
  vm.$bus.$on(EVENT_GRID_INPUT, ({ ctx, field }) => {
    const dataSource = vm.$refs.templateRef.getCurrentTabRef().grid.dataSource
    console.log(dataSource)
    dataSource.forEach((row) => {
      if (row.rfxItemKey === ctx.rowData.rfxItemKey && Array.isArray(row.childItems)) {
        row.childItems.forEach((child) => {
          // 子件需求数量 = 母件需求数量 * 子件单机用量
          if (field === 'allocationQuantity') {
            const pAllocationQuantity = ctx.getValueByField(field) || 0
            child.allocationQuantity = (pAllocationQuantity * (child.singleQuantity || 1)).toFixed(
              2
            )
          } else if (field === 'allocationRatio') {
            child[field] = ctx.getValueByField(field)
          }
        })
      }
    })
  })
}

export function beforeDestroy(vm) {
  vm.$bus.$off(EVENT_GRID_INPUT)
}
