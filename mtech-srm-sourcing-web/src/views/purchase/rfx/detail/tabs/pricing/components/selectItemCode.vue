<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <mt-select
        v-if="showSelect"
        :id="fieldName"
        :allow-filtering="true"
        :data-source="itemList"
        :filtering="onFiltering"
        :fields="{ text: 'itemCode', value: 'itemCode' }"
        :value="data[fieldName]"
        :width="130"
        @select="handleSelectChange"
        :placeholder="headerTxt"
        popup-width="180px"
      ></mt-select>
      <mt-input
        :id="fieldName"
        v-model="data[fieldName]"
        disabled
        :width="130"
        :placeholder="headerTxt"
        v-show="!allowEditing"
      ></mt-input>
      <mt-icon
        style="width: 20px"
        name="icon_list_refuse"
        @click.native="handleClear"
        v-if="allowEditing"
      ></mt-icon>
      <mt-icon
        v-if="allowEditing"
        style="width: 20px"
        name="icon_input_search"
        @click.native="showDialog"
      ></mt-icon>
    </div>
  </div>
</template>

<script>
import { getValueByPath } from '@/utils/obj'
import debounce from 'lodash.debounce'
export default {
  data() {
    return {
      data: {},
      fieldName: '',
      headerTxt: '',
      allowEditing: true,
      showSelect: true,
      itemList: []
    }
  },
  async mounted() {
    this.sourcingType = this.data.column.sourcingType
    this.fieldName = this.data.column.field
    this.allowEditing = this.data.column.allowEditing === false ? false : true
    this.$nextTick(() => {
      // this.showSelect = false
      this.getDataSource()
      if (!this.allowEditing) return
    })
  },
  methods: {
    async getDataSource(e, type) {
      let _param = JSON.parse(sessionStorage.getItem('siteParam'))
      if (sessionStorage.getItem('siteParam') == null) {
        this.showSelect = true
        return
      }
      let _defaultParams = {
        page: { current: 1, size: 20 },
        organizationId: _param.organizationId,
        siteCode: _param.siteCode,
        rfxId: this.$route.query?.rfxId,
        defaultRules: [
          {
            label: this.$t('品类编码'),
            field: 'categoryResponse.categoryCode',
            type: 'string',
            operator: 'equal',
            value: this.data.categoryCode
          }
        ]
      }
      if (type === 'filter' || this.data.itemCode) {
        _defaultParams.rules = [
          {
            label: this.$t('物料编码'),
            field: 'itemCode',
            type: 'string',
            operator: 'contains',
            value: type === 'filter' ? e.text : this.data.itemCode
          }
        ]
      }
      await this.$API.comparativePrice
        .queryItemsData(_defaultParams)
        .then((res) => {
          this.itemList.length = 0
          res.data.records.forEach((item) => {
            item.text = `(${item.itemCode})${item.itemName}`
            this.itemList.push(item)
          })
          if (type === 'filter') e.updateData(this.itemList)
          this.showSelect = true
        })
        .catch(() => {
          this.showSelect = true
        })
    },
    handleClear() {
      this.$set(this.data, 'itemCode', null)
      const fieldMap = {
        itemId: 'id',
        itemName: 'itemName',
        spec: 'itemDescription', // 规格描述
        material: 'materialTypeName', // 材质
        unitName: 'baseMeasureUnitName', // 单位
        categoryId: 'categoryResponse.categoryId', // 品类ID
        categoryCode: 'categoryResponse.categoryCode', // 品类编码
        categoryName: 'categoryResponse.categoryName' //品类名称
      }
      Object.entries(fieldMap).map(([field]) => {
        this.$bus.$emit(`${field}Change`, null)
      })
    },
    showDialog() {
      if (sessionStorage.getItem('siteParam') == null) {
        this.$toast({
          content: this.$t('当前未选择工厂，不可以选择物料'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        modal: () => import('COMPONENTS/NormalEdit/tempSelectItemCode/selectGrid'),
        data: {
          title: this.$t('选择物料'),
          rfxId: sessionStorage.getItem('temp_rfxId'),
          siteParam: JSON.parse(sessionStorage.getItem('siteParam')),
          sourcingType: this.sourcingType,
          source: this.$route?.query?.source,
          categoryCode: this.data.categoryCode
        },
        success: (data) => {
          this.setItemCode(data)
        }
      })
    },
    // 选择itemCode
    setItemCode(data) {
      this.$set(this.data, 'itemCode', data[0]['itemCode'])
      const fieldMap = {
        itemId: 'id',
        itemName: 'itemName',
        spec: 'itemDescription', // 规格描述
        material: 'materialTypeName', // 材质
        unitName: 'baseMeasureUnitName', // 单位
        categoryId: 'categoryResponse.categoryId', //品类名称
        categoryCode: 'categoryResponse.categoryCode', //品类名称
        categoryName: 'categoryResponse.categoryName', //品类名称
        // purUnitName: "purchaseUnitName", //采购单位
        priceUnitName: 'purchaseUnitName'
      }
      Object.entries(fieldMap).map(([field, key]) => {
        this.$bus.$emit(`${field}Change`, getValueByPath(data[0], key))
      })
      this.updateItemCode(data[0])
    },
    onFiltering: debounce(function (e) {
      this.getDataSource(e, 'filter')
    }, 1000),
    handleSelectChange(e) {
      if (this.sourcingType == 'new_products' && e.itemData.isPriceRecord == 1) {
        this.$toast({
          content: this.$t('请选择没有价格记录的物料'),
          type: 'warning'
        })
        return
      } else if (
        ['exist', 'second_inquiry'].includes(this.sourcingType) &&
        e.itemData.isPriceRecord == 0
      ) {
        this.$toast({
          content: this.$t('请选择有价格记录的物料'),
          type: 'warning'
        })
        return
      }
      this.setItemCode([e.itemData])
    },
    updateItemCode(data) {
      let _params = {
        rfxId: sessionStorage.getItem('temp_rfxId'),
        rfxItemId: sessionStorage.getItem('temp_rfxItemId'),
        itemId: data.id,
        itemCode: data.itemCode,
        itemName: data.itemName,
        unitCode: data.unitCode || data.baseMeasureUnitCode,
        unitName: data.unitName || data.baseMeasureUnitName
      }
      this.$API.rfxExt.updateItemCodeByRfxItemId(_params).then(() => {
        this.$bus.$emit(`refreshPricingTab`)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
</style>
