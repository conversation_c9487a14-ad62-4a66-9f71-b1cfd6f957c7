<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <mt-input
        id="ckdMaterialCode"
        v-model="displayValue"
        @keyup.native="keyup"
        class="field-input"
      ></mt-input>
      <mt-icon class="field-icon" name="icon_list_refuse" @click.native="handleClear"></mt-icon>
      <mt-icon class="field-icon" name="icon_input_search" @click.native="showDialog"></mt-icon>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      displayValue: null
    }
  },
  computed: {},
  mounted() {
    this.displayValue = this.data?.ckdMaterialCode
  },
  methods: {
    keyup(e) {
      let _value = e.target.value
      if (_value) {
        _value = _value.replace(/[\W]/g, '')
        if (_value.length && _value.length > 50) {
          _value = _value.substring(0, 50)
        }
        this.displayValue = _value
      } else {
        this.displayValue = null
      }
      this.$emit('input', this.displayValue)
    },
    handleClear() {
      this.$emit('input', null)
    },
    showDialog() {
      this.$dialog({
        modal: () => import('COMPONENTS/NormalEdit/checkSelectItemCkd/components/selectGrid'),
        data: {
          title: this.$t('选择CKD物料'),
          id: this.data.id
        },
        success: (data) => {
          this.displayValue = data[0]?.itemCode
          this.$set(this.data, 'ckdMaterialCode', data[0]['itemCode']) // 物料编码
          this.$bus.$emit('ckdPriceChange', data[0]?.exchangeUntaxedUnitPrice) // 换算后单价未税
        }
      })
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(value) {
        this.displayValue = value
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;

  .in-cell {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .field-input {
      flex: 1;
    }

    .field-icon {
      width: 20px;
      flex-shrink: 0;
      margin-left: 10px;
      cursor: pointer;
    }

    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
</style>
