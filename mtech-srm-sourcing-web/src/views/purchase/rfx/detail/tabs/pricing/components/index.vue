<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
        @actionComplete="actionComplete"
      />
    </div>
  </mt-dialog>
</template>

<script>
import { toolbarOne, toolbarTwo, columnData } from './config'
export default {
  data() {
    return {
      id: '',
      infoAppendStatus: 0,
      canExtendInfoRow: false,
      pageConfig: [
        {
          useToolTemplate: false,
          // toolbar: toolbarOne,
          toolbar: [],
          useBaseConfig: false,
          grid: {
            allowFiltering: true,
            lineIndex: true,
            editSettings: {
              allowAdding: true,
              allowEditing: true,
              allowDeleting: true,
              mode: 'Normal',
              allowEditOnDblClick: true,
              showConfirmDialog: false,
              showDeleteConfirmDialog: true,
              newRowPosition: 'Top'
            },
            columnData: [
              {
                width: '50',
                type: 'checkbox',
                allowEditing: false
              }
            ],
            dataSource: []
          }
        }
      ],
      rfxPricingDataSource: [],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],

      siteNameDataSource: [],
      unitDataSource: [],
      deliveryPlaceDataSource: [],
      procurementDataSource: [], //采购组织
      currencyDataSource: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  watch: {
    modalData: {
      handler(newVal) {
        let params = {
          biddingItemId: newVal.biddingItemId
        }
        this.id = newVal.biddingItemId
        console.log('newVal', newVal)
        this.editStatus = true
        this.getRFXDetailInformation(params)
      },
      immediate: true
    }
  },

  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  async mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.initColumnData()
  },
  methods: {
    actionComplete({ requestType, rowData, rowIndex }) {
      if (requestType === 'save') {
        this.$set(this.pageConfig[0].grid.dataSource, rowIndex, rowData)
      }
    },
    // 结束编辑状态
    endEdit() {
      let _current = this.$refs.templateRef.getCurrentTabRef()
      _current?.grid.endEdit()
    },
    async getcriteriaQuery(res) {
      console.log('getcriteriaQuery', res.data)
      if (res?.data?.infoAppendDTOList?.length != 0) {
        const masterDataRes = await this.$API.masterData
          .criteriaQuery({
            itemCode: res?.data?.infoAppendDTOList?.[0]?.itemCode
          })
          .catch(() => {})
        if (masterDataRes?.data) {
          this.siteNameDataSource = masterDataRes.data ?? []
        }
      }
      this.initColumnData()
    },
    async getcriteriaQueryAdd(res) {
      if (res.data.itemCode) {
        const itemDataRes = await this.$API.masterData
          .criteriaQuery({
            itemCode: res?.data?.itemCode
          })
          .catch(() => {})
        if (itemDataRes?.data) {
          this.siteNameDataSource = itemDataRes.data ?? []
        }
      }
      this.initColumnData()
    },
    async initData() {
      const [
        // siteNameDataSourceRes,
        unitDataSourceRes,
        deliveryPlaceRes,
        currencyDataSource
      ] = await Promise.all([
        this.$API.masterData.pagedQueryUnit(),
        this.$API.priceService.getDeliveryPlace(),
        this.$API.masterData.queryAllCurrency()
      ]).catch(() => {})
      if (!unitDataSourceRes?.data || !deliveryPlaceRes?.data || !currencyDataSource?.data) {
        return
      }
      this.unitDataSource = unitDataSourceRes.data.records
      //直送地
      this.deliveryPlaceDataSource = deliveryPlaceRes.data
      //币种
      this.currencyDataSource = currencyDataSource.data
    },
    async initColumnData() {
      await this.initData()
      this.$set(
        this.pageConfig[0].grid,
        'columnData',
        columnData({
          visible: this.infoAppendStatus == 0 ? false : true,
          self: this
        })
      )
      if (this.infoAppendStatus == 1) {
        this.pageConfig[0].grid.editSettings.allowEditing = false
      }
    },
    confirm() {
      this.endEdit()
      const grid = this.$refs.templateRef.getCurrentTabRef()?.grid
      this.$API.rfxPricing.getRFXPricingSave(grid?.getCurrentViewRecords()).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
          this.$emit('confirm-function', grid?.getCurrentViewRecords())
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
      this.id = ''
      this.infoAppendStatus = 0
    },
    async getRFXDetailInformation(params) {
      this.$API.rfxPricing.getRFXDetailInformationById(params).then(async (res) => {
        if (res.code == 200 && Array.isArray(res.data.infoAppendDTOList)) {
          this.infoAppendStatus = res.data.infoAppendStatus
          this.canExtendInfoRow = res.data.canExtendInfoRow
          if (res.data.infoAppendStatus == 0) {
            this.$set(this.pageConfig[0], 'toolbar', [
              toolbarOne(this.canExtendInfoRow, this.infoAppendStatus)
            ])
          } else {
            this.$set(this.pageConfig[0], 'toolbar', [toolbarTwo])
          }
          res.data.infoAppendDTOList.forEach((e) => {
            if (Number(e.quoteEffectiveEndDate) === 0) {
              e.quoteEffectiveEndDate = null
            }
            if (Number(e.quoteEffectiveStartDate) === 0) {
              e.quoteEffectiveStartDate = null
            }
          })
          this.rfxPricingDataSource = res.data.infoAppendDTOList
          this.infoAppendStatus = res.data.infoAppendStatus
          this.canExtendInfoRow = res.data.canExtendInfoRow
          this.getcriteriaQuery(res)
        } else {
          this.rfxPricingDataSource = []
        }
        this.$set(this.pageConfig[0].grid, 'dataSource', this.rfxPricingDataSource)
      })
    },

    async queryProcurementDataSource(organizationId, organizationCode) {
      if (organizationId !== '' && organizationId !== undefined && organizationId !== null) {
        const res = await this.$API.priceService.getByOrgIdAndBgOrgTypeCode({
          organizationId: organizationId,
          organizationCode: organizationCode,
          businessOrganizationTypeCode: 'BUORG002ADM'
        })
        return res?.data || []
      }
    },

    //保存方法
    handleSaveFn(e) {
      this.endEdit()
      this.$API.rfxPricing.getRFXPricingSave(e.grid.getCurrentViewRecords()).then((res) => {
        if (res.code == 200) {
          this.$toast({ content: this.$t('保存成功'), type: 'success' })
          let params = {
            biddingItemId: this.id
          }
          this.getRFXDetailInformation(params)
        }
      })
    },

    //扩展方法
    handleExpandFn() {
      this.endEdit()
      this.initData()
      let params = {
        biddingItemId: this.id
      }
      this.$API.rfxPricing.getRFXPricingextendById(params).then((res) => {
        if (res.code == 200) {
          if (Number(res.data.quoteEffectiveEndDate) === 0) {
            res.data.quoteEffectiveEndDate = null
          }
          if (Number(res.data.quoteEffectiveStartDate) === 0) {
            res.data.quoteEffectiveStartDate = null
          }
          res.data.id = Symbol()
          this.pageConfig[0].grid.dataSource.push(res.data)
          if (res?.data.itemName) {
            this.getcriteriaQueryAdd(res)
          }
          this.initColumnData()
        }
      })
    },

    //删除方法
    async handleDelFn(_selectGridRecords, rfxPricingDataSourceId) {
      if (_selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
      } else {
        let params = {
          ids: rfxPricingDataSourceId.filter(
            (e) => Object.prototype.toString.call(e) !== '[object Symbol]'
          )
        }
        if (params.ids.length) {
          await this.$API.rfxPricing
            .getRFXPricingEdit(params)
            .then(() => {
              this.$toast({ content: this.$t('删除成功'), type: 'success' })
            })
            .catch(() => {})
        }
        this.$set(
          this.pageConfig[0].grid,
          'dataSource',
          this.pageConfig[0].grid.dataSource.filter((e) => !rfxPricingDataSourceId.includes(e.id))
        )
      }
    },

    //发布
    handleReleaseFn() {
      let flag = false
      this.pageConfig[0].grid.dataSource.filter((e) => {
        if (!e.id) {
          flag = true
        }
      })
      let param = {
        biddingItemId: this.modalData.biddingItemId
      }
      if (this.modalData.priceStatus === 2) {
        this.$toast({
          content: this.$t('供应商的报价未被接受不能发布'),
          type: 'warning'
        })
      } else if (flag) {
        this.$toast({ content: this.$t('请先保存数据'), type: 'warning' })
      } else {
        this.$API.rfxPricing.getRFXPricingPublish(param).then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('发布成功'), type: 'success' })
            this.$set(this.pageConfig[0], 'toolbar', [toolbarTwo])
            this.infoAppendStatus = 1
            this.initColumnData()
            let params = {
              biddingItemId: this.id
            }
            this.getRFXDetailInformation(params)
          }
        })
      }
    },

    //审核通过
    handlePassFn(_selectGridRecords, rfxPricingDataSourceId) {
      if (_selectGridRecords.length <= 0) {
        this.$toast({
          content: this.$t('请选择一条数据再审核'),
          type: 'warning'
        })
      } else {
        let params = {
          infoAppendIdList: rfxPricingDataSourceId,
          approvalStatus: 1
        }
        this.$API.rfxPricing.getRFXPricingAudit(params).then((res) => {
          if (res.code == 200) {
            this.$toast({
              content: this.$t('审核通过'),
              type: 'success'
            })
            let params = {
              biddingItemId: this.id
            }
            this.getRFXDetailInformation(params)
          }
        })
      }
    },

    //审核驳回
    handleOverruleFn(_selectGridRecords, rfxPricingDataSourceId) {
      if (_selectGridRecords.length <= 0) {
        this.$toast({
          content: this.$t('请选择一条数据再驳回'),
          type: 'warning'
        })
      } else {
        let params = {
          infoAppendIdList: rfxPricingDataSourceId,
          approvalStatus: -1
        }
        this.$API.rfxPricing.getRFXPricingAudit(params).then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('驳回成功'), type: 'success' })
            let params = {
              biddingItemId: this.id
            }
            this.getRFXDetailInformation(params)
          }
        })
      }
    },
    handleImportSap(_selectGridRecords) {
      let infoAppendIdList = []
      if (_selectGridRecords.length == 0) {
        let records = this.$refs.templateRef.getCurrentTabRef().grid.dataSource
        for (let item of records) {
          if (![2, 3].includes(item.syncSapStatus)) {
            infoAppendIdList.push(item.id)
          }
        }
      } else {
        for (let item of _selectGridRecords) {
          if ([2, 3].includes(item.syncSapStatus)) {
            this.$toast({
              content: this.$t('状态为同步中和同步成功的无法同步'),
              type: 'warning'
            })
            return
          } else {
            infoAppendIdList.push(item.id)
          }
        }
      }
      this.$API.rfxPricing
        .syncToSap({
          infoAppendIdList: infoAppendIdList
        })
        .then((res) => {
          if (res.code == 200) {
            if (res.data === false) {
              this.$toast({
                content: this.$t(res.msg),
                type: 'warning'
              })
            } else {
              this.$toast({
                content: this.$t(res.msg),
                type: 'success'
              })
            }
            let params = {
              biddingItemId: this.id
            }
            this.getRFXDetailInformation(params)
          }
        })
    },
    //同步
    handleSynchronousFn(_selectGridRecords, rfxPricingDataSourceId) {
      if (_selectGridRecords.length <= 0) {
        this.$toast({
          content: this.$t('请选择一条数据再同步'),
          type: 'warning'
        })
      } else {
        let params = {
          infoAppendIdList: rfxPricingDataSourceId
        }
        this.$API.rfxPricing.getRFXPricingSynchronous(params).then((res) => {
          if (res.code == 200) {
            if (res.data === false) {
              this.$toast({
                content: this.$t(res.msg),
                type: 'warning'
              })
            }
            let params = {
              biddingItemId: this.id
            }
            this.getRFXDetailInformation(params)
          }
        })
      }
    },

    async handleClickToolBar(e) {
      let _selectGridRecords = e.grid.getSelectedRecords()
      let rfxPricingDataSourceId = _selectGridRecords.filter((e) => e.id).map((e) => e.id)
      if (e.toolbar.id == 'Save') {
        this.handleSaveFn(e) //保存
      } else if (e.toolbar.id == 'Expand') {
        this.handleExpandFn() //扩展
      } else if (e.toolbar.id == 'Del') {
        this.handleDelFn(_selectGridRecords, rfxPricingDataSourceId) // 删除
      } else if (e.toolbar.id == 'Release') {
        this.handleReleaseFn() //发布
      } else if (e.toolbar.id == 'Pass') {
        this.handlePassFn(_selectGridRecords, rfxPricingDataSourceId) //审核通过
      } else if (e.toolbar.id == 'overrule') {
        this.handleOverruleFn(_selectGridRecords, rfxPricingDataSourceId) //审核驳回
      } else if (e.toolbar.id == 'synchronous') {
        this.handleSynchronousFn(_selectGridRecords, rfxPricingDataSourceId) //同步
      } else if (e.toolbar.id == 'importSap') {
        this.handleImportSap(_selectGridRecords)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  height: 100%;
  width: 100%;
  position: relative;
}
</style>
