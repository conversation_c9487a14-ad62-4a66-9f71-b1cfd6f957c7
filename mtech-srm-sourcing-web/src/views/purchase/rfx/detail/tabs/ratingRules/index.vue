<!-- 评分规则 -->
<template>
  <div class="full-height rule-container">
    <div class="query-container">
      <mt-form ref="scoreSelet" :model="formObject" :rules="formRules">
        <mt-form-item :label="$t('评分模式')" prop="scoreModel">
          <mt-select
            v-model="formObject.scoreModel"
            :data-source="ruleModeList"
            @change="scoreModelChange"
            :fields="{ text: 'text', value: 'value' }"
            :disabled="isDisabled"
            :placeholder="$t('请选择评分模式')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('评分模板')" prop="scoreModelName">
          <mt-select
            v-model="formObject.scoreModelName"
            :data-source="ruleTemplateList"
            :disabled="!tabsFlag || isDisabled"
            :fields="{ text: 'scoreModelName', value: 'scoreModelName' }"
            :placeholder="$t('请选择评分模板')"
            :allow-filtering="true"
            :filtering="filteringResource"
            :show-clear-button="true"
            :open-dispatch-change="false"
            @change="scoreDetailNameChange"
          />
        </mt-form-item>
      </mt-form>
      <div class="tabsWeight">
        <mt-tabs
          v-if="tabsFlag"
          :tab-id="$utils.randomString()"
          :e-tab="false"
          :data-source="tabSource"
          @handleSelectTab="handleSelectTab"
        ></mt-tabs>
        <div v-if="tabsFlag" class="weight">
          <!-- 商务 -->
          <div v-show="tabsNumber == 0" class="business">
            <span>{{ $t('权重 : ') }}</span>
            <mt-input
              v-show="autoMarkScore == 0"
              class="weightInput"
              type="text"
              v-model="weightobj.Business"
              float-label-type="Never"
              @blur="changeBusiness"
            />
            <mt-input
              v-show="autoMarkScore == 1"
              class="weightInput"
              type="text"
              v-model="detailsData.bizAutoScoreWeight"
              float-label-type="Never"
              @blur="changeBusiness"
            />
            <span class="percent">%</span>
            <mt-switch
              v-show="autoMarkScore == 1"
              disabled
              style="margin-left: 15px"
              v-model="autoMarkScore"
              :on-label="$t('自动评分')"
              :off-label="$t('自动评分')"
              :active-value="1"
              :inactive-value="0"
            ></mt-switch>
            <span v-show="autoMarkScore == 1" style="margin-left: 15px">{{
              $t('自动评分公式')
            }}</span>
            <mt-tooltip :content="scoreExpression" class="weightInput" style="width: 400px"
              ><mt-input
                v-show="autoMarkScore == 1"
                class="weightInput"
                style="width: 400px"
                type="text"
                disabled
                v-model="scoreExpression"
                float-label-type="Never"
            /></mt-tooltip>
          </div>
          <!-- 技术 -->
          <div v-show="tabsNumber == 1" class="technology">
            <span>{{ $t('权重 : ') }}</span>
            <mt-input
              class="weightInput"
              type="text"
              v-model="weightobj.Technical"
              float-label-type="Never"
              @blur="changeTechnical"
            />
            <span class="percent">%</span>
          </div>
        </div>
      </div>
    </div>

    <div v-if="templateRefFlag" class="table-container">
      <mt-template-page
        ref="templateRef"
        :padding-top="true"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
      >
      </mt-template-page>
    </div>
  </div>
</template>

<script>
import { toolbar, columnData } from './config/index'
import { utils } from '@mtech-common/utils'
export default {
  name: 'RatingRules',
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      autoMarkScore: 0, //自动评分；0非自动评分，1自动评分
      scoreExpression:
        '递减：{1-(该供应商报价-最低价)/最低价}*100 递增：{1+(供应商报价-最高价)/最高价}*100', //自动评分公式
      pageConfig: [
        {
          toolbar: {
            useBaseConfig: false,
            tools: [toolbar(this.detailInfo?.status), []]
          },
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          grid: {
            allowFiltering: true,
            columnData: [],
            dataSource: [],
            editSettings: {
              allowAdding: false,
              allowEditing: true,
              allowDeleting: false,
              mode: 'Normal', // 默认normal模式
              allowEditOnDblClick: true,
              showConfirmDialog: false,
              showDeleteConfirmDialog: false
            }
          },
          gridId:
            this.$permission.gridId['purchase'][this.$route?.query?.source]['detail']['tabs'][
              'rules'
            ][this.tabsNumber === 0 ? 'business' : 'tech']
        }
      ],
      //必选项
      formRules: {
        scoreModel: [
          {
            required: true,
            message: this.$t('请选择评分模式'),
            trigger: 'blur'
          }
        ],
        scoreModelName: [
          {
            required: true,
            message: this.$t('请选择评分模板'),
            trigger: 'blur'
          }
        ]
      },
      //评分模式selet
      ruleModeList: [],
      ruleTemplateList: [], // 评分模板selet
      //切换tabs页
      tabSource: [
        { title: this.$t('商务评分要素'), moduleType: 1 },
        { title: this.$t('技术评分要素'), moduleType: 0 }
      ],
      //详情obj
      detailsData: {
        // rfxCode: null,
        // scoreModel: null,
        // scoreModelCode: null,
        // scoreModelName: null,
        // techScoreRuleDetailDTOList: [],
        // bizScoreRuleDetailDTOList: [],
        // allScoreRuleDetailDTOList: [],
      },
      bizScoreRuleDetailDTOList: [], //商务数组
      techScoreRuleDetailDTOList: [], //技术数组
      queryExpertInfo: [], //专家数组
      tabsNumber: 0, //tabs切换
      tabsFlag: false, //tabs页flag
      templateRefFlag: false,
      /*=========对象========== */
      formObject: {
        rfxCode: null, //this.detailInfo.rfxCode
        scoreModel: null, //模式
        scoreModelName: null, //模板
        scoreModelCode: null, // Modelcode
        weightValue: null //权重
      },
      //
      weightobj: {
        Business: 0,
        Technical: 0
      },
      oldList: [], //判断是否有未保存数据
      isChangeWeight: false //判断是否编辑权重
    }
  },
  computed: {
    isDisabled() {
      // 非草稿状态都要禁止编辑
      return this.detailInfo?.status !== 0
    }
  },
  mounted() {
    this.listRendering('init')
    this.getModeList()
    // this.$bus.$on("handleChangeExpertName", (params) => {
    //   this.handleChangeExpertName(params);
    // });
  },
  async activated() {
    if (this.tabsNumber === 0) {
      this.getExpertInfo(0)
    } else {
      this.getExpertInfo(1)
    }
  },
  methods: {
    getModeList() {
      this.$API.rfxDetailTabRatingRules
        .getModeList({ rfxId: this.$route.query.rfxId })
        .then((res) => {
          let ruleModeList = []
          for (let key in res.data) {
            ruleModeList.push({
              text: res.data[key],
              value: Number(key)
            })
          }
          this.ruleModeList = ruleModeList
        })
    },
    getExpertInfo(expertCategory) {
      let params = {
        rfxCode: this.detailInfo.rfxCode,
        expertCategory: expertCategory //专家类别 0：商务、1：技术、2：商务&技术
      }
      this.templateRefFlag = false //刷新控件，不然组件监听不到改动
      this.$API.rfxDetailTabRatingRules.queryExpertInfo(params).then((res) => {
        this.queryExpertInfo = res.data
        this.$set(this.pageConfig[0].grid, 'columnData', columnData(this.queryExpertInfo))
        this.templateRefFlag = true
      })
    },
    //接口
    listRendering(type) {
      //列表项接口
      let params = {
        rfxCode: this.detailInfo.rfxCode
      }
      /*this.detailInfo.rfxCode
          ? this.detailInfo.rfxCode
          :RFX10011202203031114  rfx20220209001 RFX10041202202281941 */
      //评分规则
      this.$API.rfxDetailTabRatingRules.ruleQuery(params).then((res) => {
        if (res.code == 200) {
          if (res.data) {
            this.detailsData = res.data
            // 当不是初始化的时候就不需要去查模板的数据了
            this.ruleQuery(type)
          }
        }
      })
      if (this.tabsNumber === 0) {
        this.getExpertInfo(0)
      } else {
        this.getExpertInfo(1)
      }
      //评分模板
      this.$API.rfxDetailTabRatingRules
        .queryBuilder({ rfxCode: this.detailInfo.rfxCode })
        .then((res) => {
          if (res.code == 200) {
            this.ruleTemplateList = res.data
          }
        })
    },
    //初始渲染数据
    ruleQuery(type) {
      this.tabsFlag = true
      this.formObject.scoreModel = this.detailsData.scoreModel
      this.formObject.scoreModelName = this.detailsData.scoreModelName
      let code = { scoreModelCode: this.detailsData.scoreModelCode }
      if (type === 'init') {
        this.$API.rfxDetailTabRatingRules.ItemQuery(code).then((res) => {
          if (res.code == 200) {
            this.autoMarkScore = res.data.bizAutoScore
            this.scoreExpression = res.data.scoreExpression
            const bizAutoScoreWeight = this.detailsData.bizAutoScoreWeight
            this.detailsData.bizAutoScoreWeight = bizAutoScoreWeight
              ? bizAutoScoreWeight
              : res.data.bizAutoScoreWeight
          }
        })
      }
      const bizScoreRuleDetailDTOList = this.detailsData.bizScoreRuleDetailDTOList
      const techScoreRuleDetailDTOList = this.detailsData.techScoreRuleDetailDTOList
      if (bizScoreRuleDetailDTOList && bizScoreRuleDetailDTOList.length > 0) {
        bizScoreRuleDetailDTOList.forEach((item) => {
          let arr = []
          if (item && item.scoreRuleExpertRelDTOList.length > 0) {
            item.scoreRuleExpertRelDTOList.forEach((items) => {
              arr.push(items.expertCode)
            })
          }
          item.expertCode = arr
        })
      }
      if (techScoreRuleDetailDTOList && techScoreRuleDetailDTOList.length > 0) {
        techScoreRuleDetailDTOList.forEach((item) => {
          let arr = []
          if (item && item.scoreRuleExpertRelDTOList.length > 0) {
            item.scoreRuleExpertRelDTOList.forEach((items) => {
              arr.push(items.expertCode)
            })
          }
          item.expertCode = arr
        })
      }
      if (this.tabsNumber === 0) {
        this.$set(this.pageConfig[0].grid, 'dataSource', this.detailsData.bizScoreRuleDetailDTOList)
      } else {
        this.$set(
          this.pageConfig[0].grid,
          'dataSource',
          this.detailsData.techScoreRuleDetailDTOList
        )
      }
      if (!this.isChangeWeight) {
        this.handleWeight()
      }
      this.oldList = utils.cloneDeep(this.pageConfig[0].grid.dataSource)
    },
    //监听下拉框 --评分模式
    scoreModelChange(e) {
      this.formObject.scoreModel = e.value
      if (e.value == 3) {
        this.tabSource = [{ title: this.$t('商务评分要素'), moduleType: 1 }]
      } else {
        this.tabSource = [
          { title: this.$t('商务评分要素'), moduleType: 1 },
          { title: this.$t('技术评分要素'), moduleType: 0 }
        ]
      }

      if (this.tabsFlag == false) {
        this.tabsFlag = true
      }
      if (this.tabsFlag == true) {
        if (e.value == 3) {
          // this.allBusiness(e);
        } else {
          this.detailsData.scoreModel = e.value
        }
      }
      if (!this.isChangeWeight) {
        this.handleWeight()
      }
    },
    // --评分模版明细
    scoreDetailNameChange(e) {
      if (!e.value) {
        return
      }
      this.$store.commit('startLoading')
      if (
        e.itemData.scoreModelName == this.detailsData.scoreModelName &&
        e.itemData.scoreModelCode == this.detailsData.scoreModelCode
      ) {
        this.$set(this.pageConfig[0].grid, 'dataSource', this.detailsData.bizScoreRuleDetailDTOList)
      } else {
        let code = { scoreModelCode: e.itemData.scoreModelCode }
        this.$API.rfxDetailTabRatingRules.ItemQuery(code).then((res) => {
          if (res.code == 200) {
            this.autoMarkScore = res.data.bizAutoScore
            this.detailsData.bizAutoScoreWeight = res.data.bizAutoScoreWeight
            this.scoreExpression = res.data.scoreExpression
            this.detailsData.rfxCode = this.detailInfo.rfxCode
            // this.detailInfo.rfxCode? this.detailInfo.rfxCode:
            this.detailsData.scoreModel = this.formObject.scoreModel
            this.detailsData.scoreModelCode = res.data.scoreModelCode
            this.detailsData.scoreModelName = res.data.scoreModelName
            this.detailsData.techScoreRuleDetailDTOList = res.data.techScoreModelItemDTOS
            this.detailsData.bizScoreRuleDetailDTOList = res.data.bizScoreModelItemDTOS
            this.detailsData.techScoreRuleDetailDTOList.forEach((item) => {
              let arr = []
              if (
                item &&
                item.scoreRuleExpertRelDTOList &&
                item.scoreRuleExpertRelDTOList.length > 0
              ) {
                item.scoreRuleExpertRelDTOList.forEach((items) => {
                  arr.push(items.expertCode)
                })
              }
              item.expertCode = arr
            })
            this.detailsData.bizScoreRuleDetailDTOList.forEach((item) => {
              let arr = []
              if (
                item &&
                item.scoreRuleExpertRelDTOList &&
                item.scoreRuleExpertRelDTOList.length > 0
              ) {
                item.scoreRuleExpertRelDTOList.forEach((items) => {
                  arr.push(items.expertCode)
                })
              }
              item.expertCode = arr
            })

            // this.detailsData.allScoreRuleDetailDTOList =
            //   res.data.allScoreModelItemDTOS;
            if (this.tabsNumber == 0) {
              if (
                this.detailsData.bizScoreRuleDetailDTOList &&
                this.detailsData.bizScoreRuleDetailDTOList.length > 0
              ) {
                this.$set(
                  this.pageConfig[0].grid,
                  'dataSource',
                  this.detailsData.bizScoreRuleDetailDTOList
                )
              } else {
                this.$set(
                  this.pageConfig[0].grid,
                  'dataSource',
                  this.detailsData.bizScoreRuleDetailDTOList
                )
              }
            } else {
              if (
                this.detailsData.techScoreRuleDetailDTOList &&
                this.detailsData.techScoreRuleDetailDTOList.length > 0
              ) {
                this.$set(
                  this.pageConfig[0].grid,
                  'dataSource',
                  this.detailsData.techScoreRuleDetailDTOList
                )
              } else {
                this.$set(
                  this.pageConfig[0].grid,
                  'dataSource',
                  this.detailsData.techScoreRuleDetailDTOList
                )
              }
            }
            this.handleWeight()
          }
        })
      }
      this.$store.commit('endLoading')
      this.oldList = utils.cloneDeep(this.pageConfig[0].grid.dataSource)
    },
    handleClickToolBar(e) {
      if (e.toolbar.id == 'Save') {
        this.$refs.scoreSelet.validate((valid) => {
          if (valid) {
            this.clickSave()
          }
        })
      }
    },
    // //保存
    clickSave() {
      // 判断是否是单个tab
      if (
        this.tabSource.length == 1 &&
        this.tabSource[0].moduleType == 1 &&
        this.weightobj.Business != 100
      ) {
        this.$toast({
          content: this.$t('只有商务评分时权重只能是100'),
          type: 'warning'
        })
        return
      }
      if (
        this.tabSource.length == 1 &&
        this.tabSource[0].moduleType == 0 &&
        this.weightobj.Technical != 100
      ) {
        this.$toast({
          content: this.$t('只有技术评分时权重只能是100'),
          type: 'warning'
        })
        return
      }
      let _selectRecords = this.$refs.templateRef.getCurrentTabRef().grid.getCurrentViewRecords()
      const params = JSON.parse(JSON.stringify(this.detailsData))
      //判断列表数据权重总和是否等于100
      let num = 0
      _selectRecords.forEach((v) => {
        num += Number(v.detailWeight)
      })
      if (num !== 100 && _selectRecords.length > 0) {
        this.$toast({
          content: this.$t('列表数据权重总和不等于100,请重新输入'),
          type: 'warning'
        })
        return
      }
      // 判断不同tab传参数据更改
      if (this.tabsNumber == 0) {
        params.bizScoreRuleDetailDTOList = _selectRecords
        params.bizScoreRuleDetailDTOList.forEach((v) => {
          v.scoreRuleExpertRelDTOList = []
          v.expertCode.forEach((x) => {
            this.queryExpertInfo.forEach((z) => {
              if (x == z.expertCode) {
                v.scoreRuleExpertRelDTOList.push(z)
              }
            })
          })
        })
      } else if (this.tabsNumber == 1) {
        params.techScoreRuleDetailDTOList = _selectRecords
        params.techScoreRuleDetailDTOList.forEach((v) => {
          v.scoreRuleExpertRelDTOList = []
          v.expertCode.forEach((x) => {
            this.queryExpertInfo.forEach((z) => {
              if (x == z.expertCode) {
                v.scoreRuleExpertRelDTOList.push(z)
              }
            })
          })
        })
      }
      params.bizScoreRuleDetailDTOList.forEach((v) => {
        v.itemWeight = this.weightobj.Business
      })
      params.techScoreRuleDetailDTOList.forEach((v) => {
        v.itemWeight = this.weightobj.Technical
      })
      // 纯商务
      if (this.formObject.scoreModel === 3) {
        params.techScoreRuleDetailDTOList = []
        params.bizScoreRuleDetailDTOList.forEach((item) => (item.itemWeight = 100))
      }
      params.bizAutoScore = this.autoMarkScore

      this.$API.rfxDetailTabRatingRules.ruleSave(params).then((res) => {
        if (res.code == 200) {
          this.$refs.templateRef.refreshCurrentGridData()
          this.listRendering()
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
        }
      })
    },
    //tab切换
    async handleSelectTab(e) {
      this.$store.commit('startLoading')
      setTimeout(() => {
        this.$store.commit('endLoading')
        this.tabsNumber = e
      }, 1000)
      let _selectRecords = this.$refs.templateRef.getCurrentTabRef().grid.getCurrentViewRecords()
      console.error(_selectRecords, '_selectRecords')
      if (e == 0) {
        await this.getExpertInfo(0)
        this.detailsData.techScoreRuleDetailDTOList = _selectRecords
        this.detailsData.techScoreRuleDetailDTOList.forEach((v) => {
          v.scoreRuleExpertRelDTOList = []
          v.expertCode.forEach((x) => {
            this.queryExpertInfo.forEach((z) => {
              if (x == z.expertCode) {
                v.scoreRuleExpertRelDTOList.push(z)
              }
            })
          })
        })
        if (
          this.detailsData.bizScoreRuleDetailDTOList &&
          this.detailsData.bizScoreRuleDetailDTOList.length > 0
        ) {
          this.$set(
            this.pageConfig[0].grid,
            'dataSource',
            this.detailsData.bizScoreRuleDetailDTOList
          )
        } else {
          this.$set(
            this.pageConfig[0].grid,
            'dataSource',
            this.detailsData.bizScoreRuleDetailDTOList
          )
        }
      }
      if (e == 1) {
        await this.getExpertInfo(1)
        this.detailsData.bizScoreRuleDetailDTOList = _selectRecords
        this.detailsData.bizScoreRuleDetailDTOList.forEach((v) => {
          v.scoreRuleExpertRelDTOList = []
          v.expertCode.forEach((x) => {
            this.queryExpertInfo.forEach((z) => {
              if (x == z.expertCode) {
                v.scoreRuleExpertRelDTOList.push(z)
              }
            })
          })
        })
        if (
          this.detailsData.techScoreRuleDetailDTOList &&
          this.detailsData.techScoreRuleDetailDTOList.length > 0
        ) {
          this.$set(
            this.pageConfig[0].grid,
            'dataSource',
            this.detailsData.techScoreRuleDetailDTOList
          )
        } else {
          this.$set(
            this.pageConfig[0].grid,
            'dataSource',
            this.detailsData.techScoreRuleDetailDTOList
          )
        }
      }
      this.oldList = utils.cloneDeep(this.pageConfig[0].grid.dataSource)
      if (!this.isChangeWeight) {
        this.handleWeight()
      }
    },
    //选择行内事件
    handleChangeExpertName(params) {
      if (this.tabsNumber == 0) {
        this.detailsData.bizScoreRuleDetailDTOList[params.index].scoreRuleExpertRelDTOList = []
        this.detailsData.bizScoreRuleDetailDTOList[params.index].expertCode = []
        let paramsValue = Array.from(new Set(params.value))
        for (let i = 0; i < this.queryExpertInfo.length; i++) {
          for (let j = 0; j < paramsValue.length; j++) {
            if (paramsValue[j] == this.queryExpertInfo[i].expertCode) {
              this.detailsData.bizScoreRuleDetailDTOList[
                params.index
              ].scoreRuleExpertRelDTOList.push(this.queryExpertInfo[i])
              this.detailsData.bizScoreRuleDetailDTOList[params.index].expertCode.push(
                this.queryExpertInfo[i].expertCode
              )
            }
          }
        }
      }
      if (this.tabsNumber == 1) {
        this.detailsData.techScoreRuleDetailDTOList[params.index].scoreRuleExpertRelDTOList = []
        this.detailsData.techScoreRuleDetailDTOList[params.index].expertCode = []
        let paramsValue = Array.from(new Set(params.value))
        for (let i = 0; i < this.queryExpertInfo.length; i++) {
          for (let j = 0; j < paramsValue.length; j++) {
            if (paramsValue[j] == this.queryExpertInfo[i].expertCode) {
              this.detailsData.techScoreRuleDetailDTOList[
                params.index
              ].scoreRuleExpertRelDTOList.push(this.queryExpertInfo[i])
              this.detailsData.techScoreRuleDetailDTOList[params.index].expertCode.push(
                this.queryExpertInfo[i].expertCode
              )
            }
          }
        }
      }
    },

    // 设置权重的值
    handleWeight() {
      const { scoreModel } = this.formObject // 评分模式
      const { bizScoreRuleDetailDTOList, techScoreRuleDetailDTOList } = this.detailsData // 商务评分要素列表 和 技术评分要素列表
      if (scoreModel === 3) {
        // 纯商务
        this.weightobj.Business = 100
        this.weightobj.Technical = 0
      } else {
        // 初始化时bizScoreRuleDetailDTOList和techScoreRuleDetailDTOList都是undefined
        if (!bizScoreRuleDetailDTOList && !techScoreRuleDetailDTOList) {
          this.weightobj.Business = 100
          this.weightobj.Technical = 0
          return
        }
        this.weightobj.Business = bizScoreRuleDetailDTOList[0]?.itemWeight
        this.weightobj.Technical = techScoreRuleDetailDTOList[0]?.itemWeight
      }
    },
    filteringResource(e) {
      if (typeof e.text === 'string' && e.text) {
        e.updateData(this.ruleTemplateList.filter((f) => f?.scoreModelName.indexOf(e.text) > -1))
      } else {
        e.updateData(this.ruleTemplateList)
      }
    },
    // 商务评分项权重 change
    changeBusiness(e) {
      if (!isNaN(e)) {
        if (e > 100 || e < 0) {
          this.weightobj.Business = 0
          this.$toast({
            content: this.$t('请输入0-100之间的数值'),
            type: 'warning'
          })
        } else {
          if (this.autoMarkScore == 1) {
            this.detailsData.bizAutoScoreWeight = e
            this.weightobj.Technical = 100 - e
          } else {
            this.weightobj.Business = e
            this.weightobj.Technical = 100 - e
          }
          this.isChangeWeight = true
        }
      } else {
        this.weightobj.Business = 0
        this.$toast({
          content: this.$t('请输入0-100之间的数值'),
          type: 'warning'
        })
      }
    },
    //技术评分权重 change
    changeTechnical(e) {
      if (!isNaN(e)) {
        if (e > 100 || e < 0) {
          this.weightobj.Technical = 0
          this.$toast({
            content: this.$t('请输入0-100之间的数值'),
            type: 'warning'
          })
        } else {
          if (this.autoMarkScore == 1) {
            this.weightobj.Technical = e
            this.detailsData.bizAutoScoreWeight = 100 - e
            this.weightobj.Business = 100 - e
          } else {
            this.weightobj.Technical = e
            this.weightobj.Business = 100 - e
          }
          this.isChangeWeight = true
        }
      } else {
        this.weightobj.Technical = 0
        this.$toast({
          content: this.$t('请输入0-100之间的数值'),
          type: 'warning'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.rule-container {
  display: flex;
  flex-direction: column;
  padding-top: 20px;
  .query-container {
    height: 120px;
    flex-shrink: 0;
    .mt-form {
      width: 750px;
      height: 70px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .mt-form-item {
        width: 350px;
      }
    }
    // .mt-tabs {
    //   height: 50px;
    // }
    .tabsWeight {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: var(--common-tp-bg-fa);
      .mt-tabs {
        display: inline-block;
        vertical-align: middle;
        background-color: transparent;
        /deep/.mt-tabs-container {
          background-color: transparent;
        }
      }
      .weight {
        margin-right: 20px;
        font-size: 14px;
        position: relative;
        span {
          display: inline-block;
        }
        .weightInput {
          display: inline-block;
          width: 40px;
          /deep/.e-input {
            text-align: center;
          }
        }
        .percent {
          font-size: 12px;
        }
      }
    }
  }
  .table-container {
    flex: 1;
    .common-template-page {
      background-color: transparent;
    }
    .weight {
      position: relative;
      width: 135px;
      padding: 20px;
      box-sizing: border-box;
      font-size: 14px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .weightInput {
        text-align: center;
        outline: none;
        width: 50px;
        margin-left: 10px;
      }
      .percent {
        position: absolute;
        top: calc(50% - 8px);
        left: 101px;
      }
    }
  }
}
/deep/.bg-grey {
  background: #dedede;
}
</style>
