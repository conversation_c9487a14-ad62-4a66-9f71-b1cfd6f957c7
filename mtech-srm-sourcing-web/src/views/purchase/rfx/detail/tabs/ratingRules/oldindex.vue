<!-- 评分规则 -->
<template>
  <div class="full-height rule-container">
    <div class="query-container">
      <mt-form ref="scoreSelet" :model="formObject" :rules="formRules">
        <mt-form-item :label="$t('评分模式')" prop="scoreModel">
          <mt-select
            v-model="formObject.scoreModel"
            :data-source="ruleModeList"
            @change="scoreModelChange"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择评分项名称')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('评分模板')" prop="scoreModelName">
          <mt-select
            v-model="formObject.scoreModelName"
            :data-source="ruleTemplateList"
            :disabled="!tabSourceFlag"
            :fields="{ text: 'scoreModelName', value: 'scoreModelName' }"
            @change="scoreDetailNameChange"
            :placeholder="$t('请选择评分项名称')"
          />
        </mt-form-item>
      </mt-form>
      <mt-tabs
        v-if="tabSourceFlag"
        :tab-id="$utils.randomString()"
        :e-tab="false"
        :data-source="tabSource"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>
    </div>

    <div v-if="tabSourceFlag" class="table-container">
      <!-- 权重: -->
      <div class="weight">
        {{ $t(' 权重:') }}
        <mt-input
          class="weightInput"
          type="text"
          disabled
          float-label-type="Never"
          v-model="formObject.weightValue"
        />
        <span class="percent">%</span>
      </div>
      <mt-button @click="clickSave">{{ $t('保存') }}</mt-button>
      <!-- @click="clickSave" -->
      <mt-template-page
        ref="templateRef"
        :padding-top="true"
        :template-config="pageConfig"
        @actionBegin="actionBegin"
        @actionComplete="actionComplete"
      >
      </mt-template-page>
    </div>
  </div>
</template>

<script>
import { toolbar, columnData } from './config/index'
export default {
  name: 'RatingRules',
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      pageConfig: [
        {
          toolbar,
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          grid: {
            allowFiltering: true,
            editSettings: {
              allowEditing: true
            },
            columnData: [],
            dataSource: [],
            queryCellInfo: this.customiseCell,
            class: 'pe-edit-grid custom-toolbar-grid'
          }
        }
      ],
      // 下拉框
      formObject: {
        scoreModel: null, //评分模式
        scoreModelName: null, // 评分模板
        // scoreModelCode: null, //code
        weightValue: null //权重
      },
      //必选项
      formRules: {
        scoreModel: [
          {
            required: true,
            message: this.$t('请选择评分模式'),
            trigger: 'blur'
          }
        ],
        scoreModelName: [
          {
            required: true,
            message: this.$t('请选择评分模板'),
            trigger: 'blur'
          }
        ]
      },
      //评分模式selet
      ruleModeList: [
        { text: this.$t('先技术后商务'), value: 0 },
        { text: this.$t('先商务后技术'), value: 1 },
        { text: this.$t('同时评分'), value: 2 },
        { text: this.$t('纯商务（没有技术标）'), value: 3 }
      ],
      // 评分模板selet
      ruleTemplateList: [],
      //切换tabs页
      tabSource: [
        { title: this.$t('商务评分要素'), moduleType: 1 },
        { title: this.$t('技术评分要素'), moduleType: 0 }
      ],
      //tabs页flag
      tabSourceFlag: false,
      //详情obj
      detailsData: {
        // rfxCode: null,
        // scoreModel: null,
        // scoreModelCode: null,
        // scoreModelName: null,
        // techScoreRuleDetailDTOList: [],
        // bizScoreRuleDetailDTOList: [],
        // allScoreRuleDetailDTOList: [],
      },

      //tabs切换
      tabs: 0,
      //专家数组
      queryExpertInfo: []
    }
  },
  mounted() {
    this.listRendering()
    this.$bus.$on('handleChangeExpertName', (params) => {
      this.handleChangeExpertName(params)
    })
  },
  methods: {
    //接口
    listRendering() {
      // RFX10041202202281941;
      // rfx20220209001
      //列表项接口
      let params = {
        rfxCode: 'rfx20220209001'
      }
      /*this.detailInfo.rfxCode
          ? this.detailInfo.rfxCode
          :RFX10011202203031114  */
      //评分规则
      this.$API.rfxDetailTabRatingRules.ruleQuery(params).then((res) => {
        if (res.code == 200) {
          if (res.data) {
            // let arr = res.data;
            // if (res.data.scoreModel == 3) {
            //   arr.allScoreModelItemDTOSList =
            //     res.data.bizScoreRuleDetailDTOList;
            // } else {
            //   arr.allScoreModelItemDTOSList = [];
            // }
            this.detailsData = res.data
            this.ruleQuery()
          }
        }
      })
      //专家名称
      this.$API.rfxDetailTabRatingRules.queryExpertInfo(params).then((res) => {
        this.queryExpertInfo = res.data
        columnData(this.queryExpertInfo)
        this.$set(this.pageConfig[0].grid, 'columnData', columnData(this.queryExpertInfo))
      })
      //评分模板
      let page = {
        page: {
          current: 1,
          size: 100
        },
        rules: [
          {
            condition: 'and',
            field: 'status',
            operator: 'equal',
            value: '1'
          }
        ]
      }
      this.$API.rfxDetailTabRatingRules.queryBuilder(page).then((res) => {
        if (res.code == 200) {
          this.ruleTemplateList = res.data.records
        }
      })
    },
    //初始渲染数据
    ruleQuery() {
      this.tabSourceFlag = true
      this.formObject.scoreModel = this.detailsData.scoreModel
      this.formObject.scoreModelName = this.detailsData.scoreModelName
      const bizScoreRuleDetailDTOList = this.detailsData.bizScoreRuleDetailDTOList
      const allScoreRuleDetailDTOList = this.detailsData.allScoreRuleDetailDTOList

      if (this.formObject.scoreModelName == 3) {
        if (allScoreRuleDetailDTOList && allScoreRuleDetailDTOList.length > 0) {
          this.formObject.weightValue = allScoreRuleDetailDTOList[0]?.itemWeight
          allScoreRuleDetailDTOList.forEach((item) => {
            if (item && item.scoreRuleExpertRelDTOList.length > 0) {
              let arr = []
              item.scoreRuleExpertRelDTOList.forEach((items) => {
                arr.push(items.expertCode)
              })
              // arr.push(
              //   item.scoreRuleExpertRelDTOList[0]?.expertCode ?? "1000044"
              // );
              item.expertCode = arr
            }
          })
        }
        this.$set(this.pageConfig[0].grid, 'dataSource', this.detailsData.allScoreRuleDetailDTOList)
      } else {
        if (bizScoreRuleDetailDTOList && bizScoreRuleDetailDTOList.length > 0) {
          if (bizScoreRuleDetailDTOList && bizScoreRuleDetailDTOList.length > 0) {
            this.formObject.weightValue = bizScoreRuleDetailDTOList[0]?.itemWeight
            bizScoreRuleDetailDTOList.forEach((item) => {
              if (item && item.scoreRuleExpertRelDTOList.length > 0) {
                let arr = []
                item.scoreRuleExpertRelDTOList.forEach((items) => {
                  arr.push(items.expertCode)
                  // arr.push({ text: items.expertName, value: items.expertCode });
                })
                // arr.push(
                //   item.scoreRuleExpertRelDTOList[0]?.expertCode ?? "1000044"
                // );
                item.expertCode = arr
              }
            })
          }
          this.$set(
            this.pageConfig[0].grid,
            'dataSource',
            this.detailsData.bizScoreRuleDetailDTOList
          )
        }

        // this.formObject.weightValue = bizScoreRuleDetailDTOList[0]?.itemWeight;
        // if (bizScoreRuleDetailDTOList && bizScoreRuleDetailDTOList.length > 0) {
        //   this.detailsData.bizScoreRuleDetailDTOList.forEach((item) => {
        //     if (item && item.scoreRuleExpertRelDTOList.length > 0) {
        //       let arr = [];
        //       item.scoreRuleExpertRelDTOList.forEach((items) => {
        //         arr.push(items?.expertCode ?? "1000044");
        //       });
        //       // arr.push(
        //       //   item.scoreRuleExpertRelDTOList[0]?.expertCode ?? "1000044"
        //       // );
        //       item.expertCode = arr;
        //     }
        //   });
        // }
        // this.$set(
        //   this.pageConfig[0].grid,
        //   "dataSource",
        //   this.detailsData.bizScoreRuleDetailDTOList
        // );
      }
    },
    //监听下拉框 --评分模式
    scoreModelChange(e) {
      this.formObject.scoreModel = e.value
      if (e.value == 3) {
        this.tabSource = [{ title: this.$t('商务评分要素'), moduleType: 1 }]
      } else {
        this.tabSource = [
          { title: this.$t('商务评分要素'), moduleType: 1 },
          { title: this.$t('技术评分要素'), moduleType: 0 }
        ]
      }
      if (this.tabSourceFlag == false) {
        this.tabSourceFlag = true
      }
      if (this.tabSourceFlag == true) {
        if (e.value == 3) {
          this.allBusiness(e)
        } else {
          this.detailsData.scoreModel = e.value
        }
      }
    },
    //纯商务逻辑
    // allBusiness(e) {
    //   this.detailsData.scoreModel = e.value;
    //   this.detailsData.bizScoreRuleDetailDTOList =
    //     this.detailsData.allScoreRuleDetailDTOList;
    //   if (
    //     this.detailsData.bizScoreRuleDetailDTOList &&
    //     this.detailsData.bizScoreRuleDetailDTOList.length > 0
    //   ) {
    //     this.formObject.weightValue =
    //       this.detailsData.bizScoreRuleDetailDTOList[0].itemWeight;
    //   } else {
    //     this.formObject.weightValue = null;
    //   }
    //   this.$set(
    //     this.pageConfig[0].grid,
    //     "dataSource",
    //     this.detailsData.bizScoreRuleDetailDTOList
    //   );
    // },
    // --评分模版明细
    scoreDetailNameChange(e) {
      if (
        e.itemData.scoreModelName == this.detailsData.scoreModelName &&
        e.itemData.scoreModelCode == this.detailsData.scoreModelCode
      ) {
        this.$set(this.pageConfig[0].grid, 'dataSource', this.detailsData.bizScoreRuleDetailDTOList)
      } else {
        let code = { scoreModelCode: e.itemData.scoreModelCode }
        this.$API.rfxDetailTabRatingRules.ItemQuery(code).then((res) => {
          if (res.code == 200) {
            // this.renderingLogic(res.data);
            // let arr = {};
            // this.detailsData = res.data;
            //       detailsData: [
            //   {

            //     techScoreRuleDetailDTOList: [],
            //     bizScoreRuleDetailDTOList: [],
            //     allScoreRuleDetailDTOList: [],
            //   },
            // ],
            this.detailsData.rfxCode = 'rfx20220209001'
            // this.detailInfo.rfxCode? this.detailInfo.rfxCode:
            this.detailsData.scoreModel = this.formObject.scoreModel
            this.detailsData.scoreModelCode = res.data.scoreModelCode
            this.detailsData.scoreModelName = res.data.scoreModelName
            this.detailsData.techScoreRuleDetailDTOList = res.data.techScoreModelItemDTOS
            this.detailsData.bizScoreRuleDetailDTOList = res.data.bizScoreModelItemDTOS
            this.detailsData.allScoreRuleDetailDTOList = res.data.allScoreModelItemDTOS

            if (this.tabs == 0) {
              if (
                this.detailsData.bizScoreRuleDetailDTOList &&
                this.detailsData.bizScoreRuleDetailDTOList.length > 0
              ) {
                this.formObject.weightValue =
                  this.detailsData.bizScoreRuleDetailDTOList[0].itemWeight
                this.$set(
                  this.pageConfig[0].grid,
                  'dataSource',
                  this.detailsData.bizScoreRuleDetailDTOList
                )
              } else {
                this.formObject.weightValue = null
                this.$set(
                  this.pageConfig[0].grid,
                  'dataSource',
                  this.detailsData.bizScoreRuleDetailDTOList
                )
              }
            } else {
              if (
                this.detailsData.techScoreRuleDetailDTOList &&
                this.detailsData.techScoreRuleDetailDTOList.length > 0
              ) {
                this.formObject.weightValue =
                  this.detailsData.techScoreRuleDetailDTOList[0].itemWeight
                this.$set(
                  this.pageConfig[0].grid,
                  'dataSource',
                  this.detailsData.techScoreRuleDetailDTOList
                )
              } else {
                this.formObject.weightValue = null
                this.$set(
                  this.pageConfig[0].grid,
                  'dataSource',
                  this.detailsData.techScoreRuleDetailDTOList
                )
              }
            }
          }
        })
      }
    },
    //保存
    clickSave() {
      this.endEdit()
      this.$API.rfxDetailTabRatingRules.ruleSave(this.detailsData).then(() => {
        this.$toast({ content: this.$t('保存成功'), type: 'success' })
      })
    },
    //tab切换
    handleSelectTab(e) {
      this.tabs = e
      if (this.tabs == 0) {
        if (
          this.detailsData.bizScoreRuleDetailDTOList &&
          this.detailsData.bizScoreRuleDetailDTOList.length > 0
        ) {
          this.formObject.weightValue = this.detailsData.bizScoreRuleDetailDTOList[0].itemWeight
          this.$set(
            this.pageConfig[0].grid,
            'dataSource',
            this.detailsData.bizScoreRuleDetailDTOList
          )
        } else {
          this.formObject.weightValue = null
          this.$set(
            this.pageConfig[0].grid,
            'dataSource',
            this.detailsData.bizScoreRuleDetailDTOList
          )
        }
      }
      if (this.tabs == 1) {
        if (
          this.detailsData.techScoreRuleDetailDTOList &&
          this.detailsData.techScoreRuleDetailDTOList.length > 0
        ) {
          this.formObject.weightValue = this.detailsData.techScoreRuleDetailDTOList[0].itemWeight
          this.$set(
            this.pageConfig[0].grid,
            'dataSource',
            this.detailsData.techScoreRuleDetailDTOList
          )
        } else {
          this.formObject.weightValue = null
          this.$set(
            this.pageConfig[0].grid,
            'dataSource',
            this.detailsData.techScoreRuleDetailDTOList
          )
        }
      }
    },

    //选择行内事件
    handleChangeExpertName(params) {
      if (this.tabs == 0) {
        this.detailsData.bizScoreRuleDetailDTOList[params.index].scoreRuleExpertRelDTOList = []
        this.detailsData.bizScoreRuleDetailDTOList[params.index].expertCode = []
        console.log(this.queryExpertInfo)
        console.log(params.index, params.value)
        console.log(this.detailsData)
        let paramsValue = Array.from(new Set(params.value))
        for (let i = 0; i < this.queryExpertInfo.length; i++) {
          for (let j = 0; j < paramsValue.length; j++) {
            if (paramsValue[j] == this.queryExpertInfo[i].expertCode) {
              this.detailsData.bizScoreRuleDetailDTOList[
                params.index
              ].scoreRuleExpertRelDTOList.push(this.queryExpertInfo[i])
              this.detailsData.bizScoreRuleDetailDTOList[params.index].expertCode.push(
                this.queryExpertInfo[i].expertCode
              )
              console.log(paramsValue[j], this.queryExpertInfo[i].expertCode)
            }
          }
        }
        console.log(this.detailsData)
      }
      if (this.tabs == 1) {
        this.detailsData.techScoreRuleDetailDTOList[params.index].scoreRuleExpertRelDTOList = []
        this.detailsData.techScoreRuleDetailDTOList[params.index].expertCode = []
        console.log(this.queryExpertInfo)
        console.log(params.index, params.value)
        console.log(this.detailsData)
        let paramsValue = Array.from(new Set(params.value))
        for (let i = 0; i < this.queryExpertInfo.length; i++) {
          for (let j = 0; j < paramsValue.length; j++) {
            if (paramsValue[j] == this.queryExpertInfo[i].expertCode) {
              this.detailsData.techScoreRuleDetailDTOList[
                params.index
              ].scoreRuleExpertRelDTOList.push(this.queryExpertInfo[i])
              this.detailsData.techScoreRuleDetailDTOList[params.index].expertCode.push(
                this.queryExpertInfo[i].expertCode
              )
              console.log(paramsValue[j], this.queryExpertInfo[i].expertCode)
            }
          }
        }
        console.log(this.detailsData)
      }
    },
    // 结束编辑状态
    endEdit() {
      let _current = this.$refs.templateRef.getCurrentTabRef()
      _current?.grid.endEdit()
    },
    //行内不可编辑置灰
    customiseCell(args) {
      if (!args.column.allowEditing) {
        args.cell.classList.add('bg-grey')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.rule-container {
  display: flex;
  flex-direction: column;
  .query-container {
    height: 120px;
    flex-shrink: 0;
    .mt-form {
      width: 750px;
      height: 70px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .mt-form-item {
        width: 350px;
      }
    }
    .mt-tabs {
      height: 50px;
    }
  }
  .table-container {
    flex: 1;
    .weight {
      position: relative;
      width: 100%;
      padding: 20px;
      box-sizing: border-box;
      font-size: 14px;
      .weightInput {
        text-align: center;
        outline: none;
        width: 50px;
        margin-left: 10px;
      }
      .percent {
        position: absolute;
        top: calc(50% - 8px);
        left: 101px;
      }
    }
  }
}
/deep/.bg-grey {
  background: #dedede;
}
</style>
