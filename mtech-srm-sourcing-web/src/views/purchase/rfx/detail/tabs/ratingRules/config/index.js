// import Vue from "vue";
import { i18n } from '@/main.js'
// export const toolbar = [{ id: "Save", icon: "icon_solid_Save", title: i18n.t("保存") }];
import { createEditInstance } from '@/utils/ej/dataGrid/index'
const editInstance = createEditInstance()
const multiValue = (selectArr, allArr) => {
  let arr = []
  selectArr.forEach((v) => {
    allArr.forEach((x) => {
      if (v == x.expertCode) {
        arr.push(x.expertName)
      }
    })
  })
  return arr.toString()
}
export const toolbar = (status) => {
  return [
    {
      id: 'Save',
      icon: 'icon_solid_Save',
      title: i18n.t('保存'),
      visibleCondition: () => status === 0
    }
  ]
}

export const columnData = (list) => [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    field: 'scoreDetailCode',
    headerText: i18n.t('评分项编码'),
    allowEditing: false
  },
  {
    field: 'scoreDetailName',
    headerText: i18n.t('评分项名称'),
    allowEditing: false
  },
  {
    field: 'scoreItem',
    headerText: i18n.t('评分细则'),
    allowEditing: false
  },
  {
    field: 'itemType',
    headerText: i18n.t('细则类型'),
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('技术类'), 1: i18n.t('商务类') }
    },
    edit: editInstance.create({
      getEditConfig: () => ({
        type: 'mt-select',
        readonly: true,
        disabled: true,
        dataSource: [
          { text: i18n.t('技术类'), value: 0 },
          { text: i18n.t('商务类'), value: 1 }
        ]
      })
    })
  },
  {
    field: 'minScore',
    headerText: i18n.t('最低分'),
    allowEditing: false
  },
  {
    field: 'maxScore',
    headerText: i18n.t('最高分'),
    allowEditing: false
  },
  {
    field: 'defaultScore',
    headerText: i18n.t('缺省分值'),
    allowEditing: false
  },
  {
    field: 'detailWeight',
    headerText: i18n.t('权重'),
    allowEditing: true
  },
  {
    field: 'expertCode',
    headerText: i18n.t('评分专家'),
    allowEditing: true,
    edit: editInstance.create({
      getEditConfig: () => ({
        type: 'mt-multi-select',
        dataSource: list,
        fields: { text: 'expertName', value: 'expertCode' }
      })
    }),
    formatter: ({ field }, item) => {
      console.error(item.expertCode, field, 1234)
      const cellVal = item.expertCode
      return multiValue(cellVal, list)
    }
  }
]
