<template>
  <!-- 阶梯:采购明细页面 -->
  <div class="flex-full-height">
    <CustomAgGrid
      ref="CustomAgGrid"
      :search-config="searchConfig"
      :toolbar="toolbar"
      :columns="columns"
      :suppress-row-transform="true"
      :row-data="tableData"
      :animate-rows="false"
      @handleClickToolbar="handleClickToolbar"
      @onGridReady="onGridReady"
      @refresh="refresh"
      @search="search"
    >
    </CustomAgGrid>
  </div>
</template>

<script>
import CustomAgGrid from '@/components/CustomAgGrid'
import cellLink from '@/components/AgCellComponents/cellLink'
import { columnData as rfxItem } from '@/views/common/columnData/ag/item'
import { v4 as uuidv4 } from 'uuid'
import { cloneDeep } from 'lodash'
import Decimal from 'decimal.js'
import { download, getHeadersFileName } from '@/utils/utils'
import { stepNotMergeField } from '@/views/common/columnData/constant'
import { addRowSpan, setStepField, rowSpan, cellClassNoBg } from '@/views/common/columnData/utils'

export default {
  name: 'PurchaseDetail',
  props: {
    fieldDefines: {
      type: Array,
      default: () => []
    },
    childFields: {
      type: Array,
      default: () => []
    },
    fieldStructures: {
      type: Array,
      default: () => []
    },
    detailInfo: {
      type: Object,
      default: () => {}
    },
    moduleType: {
      type: Number,
      default: () => {}
    },
    ktFlag: {
      type: Number,
      default: () => {}
    }
  },
  components: {
    CustomAgGrid,
    // eslint-disable-next-line
    cellLink // 单元格点击跳转
  },
  data() {
    return {
      agGrid: null,
      searchConfig: [],
      toolbar: [
        {
          id: 'Export',
          icon: 'icon_solid_Download',
          title: this.$t('导出')
        }
      ],
      tableData: [],
      columns: [],
      rowSelections: null,
      isStopEditingOnBlur: false
    }
  },
  async mounted() {
    this.init()
  },
  computed: {
    calculation() {
      //是否成本测算
      return this.$route.query.calculation == 1 ? true : false
    }
  },
  watch: {
    $route(to, from) {
      if (from.name === 'calculation-purchase-cost') {
        // 由成本分析页面返回，则刷新当前页面
        this.refresh()
      }
    }
  },
  methods: {
    // <<<<<<<<<<<<<<<<<<<<<<<<<< 事件操作 >>>>>>>>>>>>>>>>>>>>>>>>>>>
    // ag - 初始监听
    onGridReady(params) {
      this.agGrid = params
    },
    // ag - toolbar点击监听
    handleClickToolbar(args) {
      let _id = args.toolbar.id
      if (_id === 'Export') {
        this.handleExport() // 导出
      }
    },
    // toolbar点击监听 - 导出
    handleExport() {
      let id = this.$route.query?.id || this.detailInfo.id
      let params = {
        page: {
          current: 1,
          size: 1000
        },
        defaultRules: [
          {
            field: 'rfx_item.rfxHeaderId',
            operator: 'equal',
            type: 'string',
            value: id
          }
        ]
      }
      this.$API.rfxRequireDetail.exportBuilder(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // toolbar点击监听 - 刷新
    refresh() {
      this.initTableData()
    },
    // toolbar点击监听 - 查询
    search(params) {
      this.initTableData(params)
    },
    // <<<<<<<<<<<<<<<<<<<<<<<<<<  初始化  >>>>>>>>>>>>>>>>>>>>>>>>>>>
    // 初始化 - 页面
    init() {
      this.initSearchConfig()
      this.initGridColumns()
      this.initTableData()
    },
    // 初始化 - 查询区域
    initSearchConfig() {
      let _config = [
        {
          field: 'itemCode',
          label: this.$t('物料编码')
        },
        {
          field: 'itemName',
          label: this.$t('物料名称')
        },
        {
          field: 'temporaryItemCode',
          label: this.$t('临时物料编码')
        },
        {
          field: 'categoryCode',
          label: this.$t('品类编码')
        }
      ]
      this.searchConfig = _config
    },
    // 初始化 - toolbar
    initToolbar() {
      if (this.moduleType === 1 || this.calculation) {
        // 历史报价||成本测算 不显示
        this.toolbar = []
        return
      }
      let _toolbar = [
        {
          id: 'Save',
          icon: 'icon_solid_Save',
          title: this.$t('保存')
        },
        {
          id: 'ReQuote',
          icon: 'icon_solid_edit',
          title: this.$t('重新报价')
        },
        {
          id: 'Import',
          icon: 'icon_solid_Import',
          title: this.$t('导入')
        }
      ]
      this.toolbar = this.status === 1 && this.joinStatus === 1 ? _toolbar : []
    },
    // 初始化 - 表头
    initGridColumns() {
      // this.fieldDefines 沿用之前逻辑，保留field,headerText,allowEdit,eidtConfig此处阶梯根据this.fieldDefines配置ag相关属性
      let _columnData = this.defineGridColumns(cloneDeep(this.fieldDefines))
      // 阶梯相关字段顺序调整
      _columnData = setStepField(_columnData, stepNotMergeField)
      // 设置表头属性
      _columnData = this.setColumnsAttribute(_columnData)
      this.columns = cloneDeep(_columnData)
    },
    // 表头 - 设置ag表头配置相关属性
    setColumnsAttribute(columns) {
      let _base = rfxItem()
      let _columns = columns.map((item) => {
        let _attrObj = {
          required: item.required,
          field: item.field || '',
          headerName: item.headerText,
          width: item.width,
          rowSpan: rowSpan,
          cellClass: cellClassNoBg
        }
        // // 处理附件查看
        // if (['drawing', 'supplierDrawing'].includes(item.field)) {
        //   _attrObj.cellRenderer = 'cellFile'
        // }
        // 特殊处理 - 成本模型、成本测算
        if (['costModelName', 'costEstimation'].includes(item.field)) {
          _attrObj.cellRenderer = 'cellLink'
          _attrObj.cellRendererParams = {
            handleable: true, //“可操作”（不可编辑）
            rfxId: this.$route.query.rfxId
          }
        }
        const _find = _base.find((x) => x.field === item.field)
        if (_find) {
          _attrObj = Object.assign({}, _attrObj, _find)
        }
        return _attrObj
      })
      return _columns
    },
    // 表头 - 列字段定义
    defineGridColumns(fieldDefines) {
      let _columnData = []
      let _columns = cloneDeep(fieldDefines)
      if (!Array.isArray(_columns) || _columns?.length < 1) return []
      _columns.forEach((col) => {
        let _field = { field: col.fieldCode }
        if (col.tableName == 'rfx_item_ext' && col.fieldCode != 'drawingUrl') {
          _field = {
            field: `itemExtMap.${col.fieldCode}`
          }
        } else if (col.tableName.trim() === 'mt_supplier_rfx_item_ext') {
          _field = {
            field: `itemExtMap.${col.fieldCode}`
          }
        } else if (col.tableName == 'rfx_item_die') {
          _field = {
            field: `itemDieResponse.${col.fieldCode}`
          }
        } else if (col.tableName == 'rfx_item_logistics') {
          _field = {
            field: `itemLogisticsResponse.${col.fieldCode}`
          }
        }
        let _one = {
          ...col,
          ..._field,
          headerText: this.$t(col.fieldName),
          width: 150
        }
        _columnData.push(_one)
      })
      return _columnData
    },
    // 初始化 - 表格数据
    async initTableData(rules) {
      this.$store.commit('startLoading')
      let params = this.mergeParams(rules)
      this.tableData = []
      await this.$API.rfxRequireDetail
        .getRFXItem({
          ...params
        })
        .then((res) => {
          if (res?.code === 200) {
            let _records = cloneDeep(res.data?.records || [])
            _records = addRowSpan(_records)
            _records = this.serializeGridList(_records)
            this.tableData = cloneDeep(_records)
          }
          this.$store.commit('endLoading')
        })
    },
    // 表格数据 - 拼接请求参数
    mergeParams(rules) {
      let params = {
        defaultRules: [
          {
            field: 'rfx_item.rfxHeaderId',
            operator: 'equal',
            type: 'string',
            value: this.$route.query.rfxId || ''
          }
        ],
        page: { current: 1, size: 10000 }
      }
      if (rules) params = Object.assign({}, params, rules)
      return params
    },
    // 表格数据 - 数据处理
    serializeGridList(list) {
      let _list = []
      list.forEach((e) => {
        e.addId = e.id
        e.groupId = uuidv4()
        e.drawing = e.fileList ? JSON.stringify(e.fileList) : null //单独处理附件字段
        e.supplierDrawing = e.supplierFileList ? JSON.stringify(e.supplierFileList) : null //单独处理"供应商附件"字段
        e.stepQuoteName =
          e.itemStageList && e.itemStageList.length > 0 ? cloneDeep(e.itemStageList) : null //单独处理阶梯报价
        //处理直送地
        e.itemExtMap.deliveryPlace =
          e.deliveryPlaceList && e.deliveryPlaceList?.length ? cloneDeep(e.deliveryPlaceList) : null
        e.stepNum = e.stepValue
        // 处理阶梯类型
        e.stepQuoteType === -1 && (e.stepQuoteType = null)
        // 接口没有返回税率值, 需要前端匹配
        if (e.taxRateCode && !e.taxRateValue) {
          const taxRow = this.taxRateNameList.find((tax) => tax.taxItemCode === e.taxRateCode)
          taxRow && (e.taxRateValue = taxRow.taxRate)
        }
        // 判断金额型和比例型
        if (e.itemExtMap && e.itemExtMap.minQuoteRangeType == 1) {
          e.itemExtMap.minQuoteRange = new Decimal(e.itemExtMap.minQuoteRange)
            .mul(new Decimal(100))
            .toNumber()
        }
        // 销售凭证空值转换
        if (e.itemExtMap && !e.itemExtMap.salesVouchers) {
          e.itemExtMap.salesVouchers = 'empty'
        }
        // 编辑时候子数据不作展开，提交时候展开(TODO 阶梯不作子数据逻辑)
        // if (Array.isArray(e?.childItems)) {
        //   e.childItems = this.serializeGridList(e.childItems)
        //   // 覆盖groupId
        //   e.childItems.forEach((item) => {
        //     item.groupId = e.gropuId
        //   })
        // }
        _list.push(e)
      })
      return _list
    }
  }
}
</script>
