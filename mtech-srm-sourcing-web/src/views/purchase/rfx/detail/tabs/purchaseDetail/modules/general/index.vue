<template>
  <!-- 通用:采购明细页面 -->
  <div class="flex-full-height">
    <CustomAgGrid
      ref="CustomAgGrid"
      v-clickoutside="clickoutside"
      :toolbar="toolbar"
      :columns="columns"
      :suppress-row-transform="true"
      :row-data="tableData"
      :animate-rows="false"
      :is-stop-editing-on-blur="isStopEditingOnBlur"
      :is-single-click-edit="isSingleClickEdit"
      :get-row-id="getRowId"
      @handleClickToolbar="handleClickToolbar"
      @onGridReady="onGridReady"
      @refresh="refresh"
      @search="search"
      @cell-value-changed="cellValueChanged"
      @processDataFromClipboard="processDataFromClipboard"
    >
    </CustomAgGrid>
    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadInitBalanceRef"
      :down-template-name="downTemplateName"
      :request-urls="requestUrls"
      :down-template-params="downTemplateParams"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import CustomAgGrid from '@/components/CustomAgGrid'
import MixIn from '../../config/mixin.js'
export default {
  name: 'PurchaseDetail',
  mixins: [MixIn],
  components: {
    CustomAgGrid,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  data() {
    return {
      downTemplateName: this.$t('采购明细模板'),
      requestUrls: {},
      downTemplateParams: {
        page: {
          current: 1,
          size: 10
        },
        rules: []
      }
    }
  },
  async created() {},
  computed: {},
  watch: {},
  methods: {
    // ag - 获取rowId
    getRowId(params) {
      return params.data.addId
    }
  }
}
</script>
