import Decimal from 'decimal.js'
import { getValueByPath } from '@/utils/obj'
import { cloneDeep } from 'lodash'

// import selectedItemCode from "COMPONENTS/NormalEdit/selectItemCode"; // 物料
import checkSelectedItemCode from 'COMPONENTS/NormalEdit/checkSelectItemCode'
import checkSelectItemDie from 'COMPONENTS/NormalEdit/checkSelectItemDie' //模具
import codeInput from 'COMPONENTS/NormalEdit/codeInput' //code输入框
import cellChanged from 'COMPONENTS/NormalEdit/cellChanged' // 单元格被改变（纯展示）
import cellUpload from 'COMPONENTS/NormalEdit/cellUpload' // 单元格上传
import cellFileView from 'COMPONENTS/NormalEdit/cellFileView' // 单元格附件查看
import stepViewTemplate from 'COMPONENTS/NormalEdit/stepTemplate' // 阶梯合并单元格查看
import stepEditTemplate from 'COMPONENTS/NormalEdit/stepTemplate/stepEdit' // 阶梯合并单元格编辑
import cellDrawingView from 'COMPONENTS/NormalEdit/cellDrawingView' // 图纸协同查看
import { i18n } from '@/main.js'
import { columnData as logisticsColumnData } from '@/views/common/columnData/rfxItemLogistics'
import { columnData as itemExtMapColumnData } from '@/views/common/columnData/itemExtMap'
import { columnData as itemDieColumnData } from '@/views/common/columnData/biddingItemDie'

import { eJComponentFactory, createEditInstance, EditConfig } from '@/utils/ej/dataGrid'
import { fieldLinkUpdate, mergeDataSource } from '@/utils/ej/dataGrid/utils'
import { useFiltering } from '@/utils/ej/select'

import { addArrTextField, makeTextFields, filteringByText } from '@/views/common/columnData/utils'

import { MAX_SAFE_INTEGER } from '@/constants/editConfig'
import * as combination from '../utils/combination'
import selectReferItem from 'COMPONENTS/NormalEdit/selectReferItem'

const Decimal32 = Decimal.clone({
  precision: 32,
  rounding: Decimal.ROUND_HALF_UP
})

const notAllowEditFields = [
  'porLineNo', //基础信息-采购申请行号
  'lineNo', //基础信息-行号
  'currencyCode', //成本因子-币种编码
  'bidCurrencyCode', //价格信息-币种编码
  'baseCurrencyCode', //价格信息-本位币
  'supplierCode', //供应商信息-供应商编码
  'referItemSupplierCode', //参考物料-参考物料供应商编码
  'siteId', //基础信息-工厂id
  'siteName', //基础信息-工厂名称
  'itemId', //物料信息-物料id  成本因子-成本因子编码id  BOM信息-物料id  todo多个
  // "itemName", //BOM信息-物料编码 模具信息-模具物料编码 成本因子-成本因子编码 物料信息-物料编码  todo多个
  'categoryId', //物料信息-品类id
  // "categoryCode", //物料信息-品类编码
  // "categoryName", //物料信息-品类名称
  // "spec", // 物料信息-规格描述
  // "material", // 物料信息-材质
  // "unitName", // 物料信息-基本单位
  'skuCode', //物料信息-SKU编码
  'skuId', //物料信息-SKUId
  'taxRateCode', //成本因子-税率编码
  'taxRateValue', //价格信息-税率值
  'bidTaxRateCode', //价格信息-税率编码
  'requireUserId', //基础信息-需求人id
  'unitId', //成本因子-单位id  物料信息-基本单位id  BOM信息-单位id  todo多个
  'unitCode', //成本因子-单位id  物料信息-基本单位id  BOM信息-单位id  todo多个
  'companyCode', //基础信息-公司code
  'companyId', //基础信息-公司id
  'purUnitId', //物料信息-订单单位id
  'purUnitCode', //物料信息-订单单位id
  'porId', //基础信息-采购申请Id
  'requireDeptId', //基础信息-需求部门id
  'requireDeptCode', //基础信息-需求部门编码
  'costModelId', //物料信息-成本模型id
  'temporaryItemCode' //临时物料编码
]

export const priceFields = [
  'transportCost', //价格信息-运费
  'processCost', //价格信息-加工费
  'diePriceUnTaxed', //价格信息-模具价格（未税)
  'diePriceTaxed', //价格信息-模具价格（含税）
  'notScreenPrice', //价格信息-非屏价格（含税）
  'untaxedTotalPrice', //价格信息-总价（未税）
  'taxedTotalPrice', //价格信息-总价（含税）
  'declinePercent', //价格信息-降幅%
  'dargaining', //价格信息-还价
  'discussPrice', //价格信息-议价
  'lastQuoteUntaxed', //价格信息-上次报价（未税）
  'lastQuoteTaxed', //价格信息-上次报价（含税）
  'untaxedUnitPrice', //价格信息-单价（未税）
  'conversionRate', //物料信息-转换率   todo
  // "taxedUnitPrice", //价格信息-单价（含税）
  'exchangeRate', //价格信息-汇率   todo
  'lastCostUntaxed', //模具信息-上次模具费用（未税）
  'lastCostTaxed', //模具信息-上次模具费用（含税）
  'bomProductPrice', //BOM信息-BOM成品价格
  'minPricePercent', //成本因子-价格下限%
  'maxPricePercent', //成本因子-价格上限%
  'lastShareCostUntaxed', //模具信息-上次模具分摊费用（未税)
  'tconPrice', //BOM信息-T-CON价格
  'lastShareCostTaxed', //模具信息-上次模具分摊费用（含税）
  'minQuoteRange', //价格信息-最小报价幅度
  'basicPrice', //成本因子-基准价
  'sharePriceUntaxed', //模具信息-模具分摊价格（未税）
  'minQuotePercent', //价格信息-报价下限
  'screenPrice', //BOM信息-屏价格
  'maxQuotePercent', //价格信息-报价上限
  'sharePriceTaxed', //模具信息-模具分摊价格（含税）
  'distributionQuantity', //数量信息-分配数量
  // "stepQuoteName", //价格信息-阶梯报价
  'budgetUnitPriceUntaxed', //价格信息-预估单价（未税）  财务信息-预算单价（未税）   todo多个
  'budgetUnitPriceTaxed', //价格信息-预估单价（含税）  财务信息-预算总价（含税）  todo多个
  'budgetTotalPriceTaxed', //财务信息-预算总价（含税）
  'referItemUnitPriceTaxed', //参考物料-参考物料单价（含税）
  'marketPrice', //价格信息-市场价格
  'historyPrice', //价格信息-历史价格
  'budgetTotalPriceUntaxed', //财务信息-预算总价（未税）
  'purchaseTargetPrice', //价格信息-采购目标价
  'financeTargetPrice' //价格信息-财务目标价
]

export const numberEditFields = [
  'transportCost', //价格信息-运费
  'processCost', //价格信息-加工费
  'diePriceUnTaxed', //价格信息-模具价格（未税)
  'diePriceTaxed', //价格信息-模具价格（含税）
  'notScreenPrice', //价格信息-非屏价格（含税）
  'untaxedTotalPrice', //价格信息-总价（未税）
  'taxedTotalPrice', //价格信息-总价（含税）
  'declinePercent', //价格信息-降幅%
  'dargaining', //价格信息-还价
  'discussPrice', //价格信息-议价
  'lastQuoteUntaxed', //价格信息-上次报价（未税）
  'lastQuoteTaxed', //价格信息-上次报价（含税）
  'untaxedUnitPrice', //价格信息-单价（未税）
  'conversionRate', //物料信息-转换率   todo
  // "taxedUnitPrice", //价格信息-单价（含税）
  'exchangeRate', //价格信息-汇率   todo
  'lastCostUntaxed', //模具信息-上次模具费用（未税）
  'lastCostTaxed', //模具信息-上次模具费用（含税）
  'bomProductPrice', //BOM信息-BOM成品价格
  'minPricePercent', //成本因子-价格下限%
  'maxPricePercent', //成本因子-价格上限%
  'lastShareCostUntaxed', //模具信息-上次模具分摊费用（未税)
  'tconPrice', //BOM信息-T-CON价格
  'lastShareCostTaxed', //模具信息-上次模具分摊费用（含税）
  'minQuoteRange', //价格信息-最小报价幅度
  'basicPrice', //成本因子-基准价
  'sharePriceUntaxed', //模具信息-模具分摊价格（未税）
  'minQuotePercent', //价格信息-报价下限
  'screenPrice', //BOM信息-屏价格
  'maxQuotePercent', //价格信息-报价上限
  'sharePriceTaxed', //模具信息-模具分摊价格（含税）
  'distributionQuantity', //数量信息-分配数量
  // "stepQuoteName", //价格信息-阶梯报价
  'quota', //数量信息-配额
  'shareQuantity', //模具信息-实际分摊数量（上传SAP）
  'adviseMinPurQuantity', //数量信息-建议最小采购量
  'planQuantity', //模具信息-规划量
  'subjectTotal', //财务信息-科目总额
  'minPurQuantity', //数量信息-最小采购量
  'referItemMinPur', //参考物料-参考物料最小采购量
  'singleQuantity', //数量信息-单机用量
  'budgetUnitPriceUntaxed', //价格信息-预估单价（未税）  财务信息-预算单价（未税）   todo多个
  'budgetUnitPriceTaxed', //价格信息-预估单价（含税）  财务信息-预算总价（含税）  todo多个
  'yearMinQuantity', //数量信息-年度最小用量
  'budgetTotalPriceTaxed', //财务信息-预算总价（含税）
  'referItemUnitPriceTaxed', //参考物料-参考物料单价（含税）
  'marketPrice', //价格信息-市场价格
  'yearAverageQuantity', //数量信息-年度平均用量
  'minPackageQuantity', //包装信息-最小包装数量
  'historyPrice', //价格信息-历史价格
  'yearMaxQuantity', //数量信息-年度最大用量
  'budgetTotalPriceUntaxed', //财务信息-预算总价（未税）
  'requireDelivery', //时间信息-要求交期（天）
  'adviseMinPackageQuantity', //包装信息-建议最小包装数量
  'forecastQuantity', //数量信息-预测采购量
  'purchaseTargetPrice', //价格信息-采购目标价
  'financeTargetPrice', //价格信息-财务目标价
  'requireQuantity', //数量信息-需求数量
  'specVolumeWeight', //物流信息-体积重（KG）
  'specVolume', //物流信息-体积（CBM）
  'specGrossWeight', //物流信息-毛重（KG）
  // "specSize", //物流信息-尺寸, 尺寸应为字符串
  'transportQuantity', //物流信息-件数（需求量）
  'adviseLeadTime', // 建议L/T
  'adviseUnconLeadTime', // 建议无条件L/T
  'leadTime', // L/T
  'unconditionalLeadTime' // 无条件L/T
]
//BOM成品供应商  部门 屏编码  成本中心  预算编号 运输周期 生产周期
export const dateEditFields = [
  'effectiveEndDate', //成本因子-失效日期
  'adviseEffectiveStartDate', //时间信息-建议报价有效期从
  'referItemQuoteEndDate', //参考物料-参考物料报价有效期至
  'referItemQuoteStartDate', //参考物料-参考物料报价有效期从
  'quoteEffectiveEndDate', //时间信息-报价有效期至
  'quoteEffectiveStartDate', //时间信息-报价有效期从
  'requireDate', //时间信息-需求日期
  'expectArriveTime', //物流信息-期望送达时间
  'lastDeliveryTime', //物流信息-最晚发运时间
  'goodsFinishTime' //物流信息-货好时间
]

export const booleanEditField = [
  'customized', //物料信息-是否定制
  'shareUpload', //模具信息-分摊是否上传
  'stepQuote', //价格信息-是否阶梯报价
  'modifyDie', //模具信息-是否改模
  'costModelQuote' //物料信息-是否成本模型报价  成本因子-是否成本模型报价   todo多个
]

const linkFileds = {
  currencyName: {
    //成本因子-币种名称
    currencyCode: 'currencyCode'
  },
  bidCurrencyName: {
    bidCurrencyCode: 'currencyCode'
  },
  baseCurrencyName: {
    //价格信息-本位币
    baseCurrencyCode: 'currencyCode'
  },
  supplierName: {
    ////供应商信息-供应商名称
    supplierCode: 'supplierCode'
  },
  referItemSupplierName: {
    //参考物料-参考物料供应商名称
    referItemSupplierCode: 'supplierCode'
  },
  siteCode: {
    ////基础信息-工厂名称
    siteId: 'id',
    siteName: 'siteName'
  },
  itemCode: {
    //todo  目前出现4组itemCode
    itemId: 'id',
    itemName: 'itemName',
    categoryId: 'categoryId', //品类ID  (新逻辑：品类是品项带出来的。不能独立输入)
    // categoryCode: "categoryCode", //品类编码
    categoryName: 'categoryName' //品类名称
  },
  skuName: {
    ////物料信息-SKU名称
    skuCode: 'barCode',
    skuId: 'id'
  },
  taxRateName: {
    //成本因子-税率名称
    taxRateCode: 'taxItemCode',
    taxRateValue: 'taxRate' //税率值
  },
  bidTaxRateName: {
    bidTaxRateCode: 'taxItemCode'
  },
  deliveryPlace: {}, //运输信息-直送地
  tradeTerms: {}, //运输信息-贸易条款
  logisticsTerm: {}, //物流信息-贸易条款
  logisticsMode: {}, //运输信息-物流方式
  requireUserName: {
    //基础信息-需求人名称
    requireUserId: 'id'
  },
  unitName: {
    //BOM信息-单位  物料信息-基本单位  成本因子-单位
    unitId: 'id',
    unitCode: 'unitCode'
  },
  purUnitName: {
    // 物料信息-订单单位
    purUnitId: 'id',
    purUnitCode: 'purUnitCode'
  },
  companyName: {
    //基础信息-公司
    companyCode: 'orgCode',
    companyId: 'id'
  },
  requireDeptName: {
    requireDeptId: 'id',
    requireDeptCode: 'departmentCode'
  }
}

/**
 * @typedef {object} editTemplateGeneratorOption
 * @property {'number', 'date', 'datetime', 'checkbox', 'select', 'multiSelect', 'text'} type
 * @property {function} callback
 * @property {object[]} [dataSource] - data source for select
 * @property {string} [placeholder]
 * @property {'Auto', 'Always', 'Never'} [floatLabelType]
 * */
/** 表格行内编辑有两个field交互时需要用到这个，比如实时得到两个field的和
 * @example {
 *  name: {
 *    inputElement: <HTMLInputElement>,
 *    ej2Element: <ej2Element>
 *  }
 * } */
const rowEditCacheMap = {}

/**
 * @desc 表格和行内编辑常用类型
 * @param {editTemplateGeneratorOption} options
 * */
const editTemplateGenerator = (options = {}) => {
  let row, field
  return {
    create: (objectData) => {
      field = objectData.column.field
      row = objectData.data
      rowEditCacheMap[field].inputElement = document.createElement('input')
      return rowEditCacheMap[field].inputElement
    },
    read: () => {
      options.callback &&
        options.callback(
          'blur',
          rowEditCacheMap[field].inputElement.value,
          field,
          row,
          rowEditCacheMap
        )
      return rowEditCacheMap[field].ej2Element.value
    },
    destroy: () => {
      rowEditCacheMap[field].ej2Element.destroy()
    },
    write: (args) => {
      const change = (args) =>
        options.callback && options.callback('change', args, field, row, rowEditCacheMap)
      const commonOption = {
        ...options,
        value: getValueByPath(args.rowData, args.column.field),
        floatLabelType: options?.floatLabelType ?? 'Never',
        change
      }
      switch (options.type) {
        case 'date':
        case 'datetime':
        case 'multiSelect':
        case 'number':
        case 'select':
        case 'text':
          rowEditCacheMap[field].ej2Element = eJComponentFactory({
            tag: commonOption.type,
            options: {
              ...commonOption,
              type: undefined
            }
          })
          break
        default:
          throw Error(
            `Invalid grid edit type: ${options.type}, expect one of ['number', 'date', 'datetime', 'checkbox', 'select', 'multiSelect', 'text']`
          )
      }
      rowEditCacheMap[field].ej2Element.appendTo(rowEditCacheMap[field].inputElement)
    }
  }
}

/**
 * @desc 用用户传入的columns生成上面的field cache对象
 * @param {{editConfig?: editTemplateGeneratorOption, field: string}[]} columns
 * @param {object} [editSettings]
 * @param {boolean} [editSettings.allowEditing]
 * @param {boolean} [editSettings.allowAdding]
 * */
export const editFieldCacheMapGenerator = (columns, editSettings) => {
  if (editSettings && (editSettings.allowAdding || editSettings.allowEditing)) {
    for (const item of columns) {
      if (item.edit) {
        continue
      }
      if (item.editConfig) {
        if (item.allowEditing == false) {
          if (typeof item.editConfig === 'object') {
            editSettings.disabled = true
          } else {
            return
          }
        }
        rowEditCacheMap[item.field] = {
          inputElement: null,
          ej2Element: null
        }
        item.edit = editTemplateGenerator(item.editConfig)
      } else {
        if (notAllowEditFields.indexOf(item.field) > -1) {
          item.allowEditing = false
        }
      }
    }
  }
}
/**
 * @desc 表格行内编辑时，更新*非可编辑*field区域的值
 * @param {HTMLInputElement} element - source input element
 * @param {string} field - target field
 * @param {any} value
 * */
const gridInlineEditUpdateCell = (element, field, value) => {
  let cur = element
  while (cur.parentElement && cur.parentElement.tagName !== 'TR') {
    cur = cur.parentElement
  }
  if (cur.parentElement.querySelector(`[name='${field}']`)) {
    cur.parentElement.querySelector(`[name='${field}']`).value = value
  }
}

const defineSingleFieldValue = (args, editObjectMap, field, rowField, selectField) => {
  gridInlineEditUpdateCell(
    editObjectMap[field].inputElement,
    rowField,
    args?.itemData?.[selectField] === undefined ? null : args?.itemData?.[selectField]
  )
}

export const handleSelectChange = (type, args, field, row, editObjectMap) => {
  const setVal = function (path, val) {
    editObjectMap[path].ej2Element.value = val
    editObjectMap[path].inputElement.value = val
  }
  const getVal = function (path) {
    return editObjectMap[path].ej2Element.value
  }
  if (type === 'change') {
    if (
      ['itemLogisticsResponse.endPlace', 'itemLogisticsResponse.startPlace'].includes(field) &&
      editObjectMap['itemLogisticsResponse.endPlace'] &&
      editObjectMap['itemLogisticsResponse.startPlace'] &&
      editObjectMap['itemLogisticsResponse.transportLine']
    ) {
      const startPlace = getVal('itemLogisticsResponse.startPlace') || ''
      const endPlace = getVal('itemLogisticsResponse.endPlace') || ''
      const transportLine = `${startPlace}-${endPlace}`
      if (startPlace && endPlace) {
        setVal('itemLogisticsResponse.transportLine', transportLine)
      } else {
        setVal('itemLogisticsResponse.transportLine', null)
      }
      return
    }

    let _fields = linkFileds[field]
    if (typeof _fields === 'string') {
      defineSingleFieldValue(args, editObjectMap, field, _fields, _fields)
    } else {
      for (let rowField in _fields) {
        defineSingleFieldValue(args, editObjectMap, field, rowField, _fields[rowField])
      }
    }
  }
}

export const toolbar = (flag = false) => [
  {
    id: 'Add',
    icon: 'icon_solid_Createorder',
    title: i18n.t('新增')
  },
  // {
  //   id: "choose",
  //   icon: "icon_solid_Createproject",
  //   title: i18n.t("选择"),
  // },
  {
    id: 'delete',
    icon: 'icon_solid_Delete1',
    title: i18n.t('删除')
  },
  {
    id: 'save',
    icon: 'icon_solid_Createproject',
    title: i18n.t('保存')
  },
  {
    id: 'upload',
    icon: 'icon_solid_Createproject',
    title: i18n.t('导入')
  },
  {
    id: 'download',
    icon: 'icon_solid_Download ',
    title: i18n.t('导出')
  },
  {
    id: 'batchAdd',
    icon: 'icon_solid_Createorder',
    title: i18n.t('批量添加'),
    visibleCondition: () => !flag
  }
]

export const toolbarNoBatch = () => [
  {
    id: 'Add',
    icon: 'icon_solid_Createorder',
    title: i18n.t('新增')
  },
  {
    id: 'delete',
    icon: 'icon_solid_Delete1',
    title: i18n.t('删除')
  },
  {
    id: 'save',
    icon: 'icon_solid_Createproject',
    title: i18n.t('保存')
  },
  {
    id: 'upload',
    icon: 'icon_solid_Createproject',
    title: i18n.t('导入')
  }
]

export const toolbarNoImport = [
  {
    id: 'Add',
    icon: 'icon_solid_Createorder',
    title: i18n.t('新增')
  },
  // {
  //   id: "choose",
  //   icon: "icon_solid_Createproject",
  //   title: i18n.t("选择"),
  // },
  {
    id: 'delete',
    icon: 'icon_solid_Delete1',
    title: i18n.t('删除')
  },
  {
    id: 'save',
    icon: 'icon_solid_Createproject',
    title: i18n.t('保存')
  },
  {
    id: 'upload',
    icon: 'icon_solid_Createproject',
    title: i18n.t('导入')
  },
  {
    id: 'download',
    icon: 'icon_solid_Download ',
    title: i18n.t('导出')
  }
]
export const subToolbar = [
  {
    id: 'Add',
    icon: 'icon_solid_Createorder',
    title: i18n.t('新增')
  },
  {
    id: 'delete',
    icon: 'icon_solid_Delete1',
    title: i18n.t('删除')
  }
]

export const noEditToolbar = [
  {
    id: 'save',
    icon: 'icon_solid_Createproject',
    title: i18n.t('保存'),
    visibleCondition: () => false
  },
  {
    id: 'upload',
    icon: 'icon_solid_Createproject',
    title: i18n.t('导入')
  },
  {
    id: 'download',
    icon: 'icon_solid_Download ',
    title: i18n.t('导出')
  }
]

export const editSettings = {
  allowAdding: true,
  allowEditing: true,
  allowDeleting: true,
  mode: 'Normal', // 默认normal模式
  allowEditOnDblClick: true,
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Top'
}

export const notAllowedEditSettings = {
  allowAdding: false,
  allowEditing: false,
  allowDeleting: false,
  mode: 'Normal', // 默认normal模式
  allowEditOnDblClick: false,
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Bottom'
}

export const editColumnBefore = [
  {
    width: '150',
    field: 'addId', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    headerText: i18n.t('addId主键'),
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true, // 一定要有主键，不然已有的行会被修改掉。。
    allowEditing: false
  }
]
export const childEditColumnBefore = [
  {
    width: '50',
    type: 'checkbox',
    allowEditing: false,
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'addId', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    headerText: i18n.t('addId主键'),
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true, // 一定要有主键，不然已有的行会被修改掉。。
    allowEditing: false
  }
]

const setDieItemValue = (ctx, value) => {
  const fieldMap = {
    'itemDieResponse.dieFormalCode': 'dieFormalCode', //正式模具编码
    'itemDieResponse.dieType': 'dieType', //模具类型
    'itemDieResponse.taxedUnitPrice': 'taxedUnitPrice', //未税单价
    'itemDieResponse.untaxedUnitPrice': 'untaxedUnitPrice', //含税单价
    'itemDieResponse.planSharePriceTaxed': 'planSharePriceTaxed', //规划分摊价（含税）
    'itemDieResponse.planSharePriceUntaxed': 'planSharePriceUntaxed', //规划分摊价（未税）
    'itemDieResponse.realSharePriceTaxed': 'realSharePriceTaxed', //实际分摊价（含税）
    'itemDieResponse.realSharePriceUntaxed': 'realSharePriceUntaxed', //实际分摊价（未税）
    'itemDieResponse.sharePriceTaxed': 'sharePriceTaxed', //分摊价格（含税）
    'itemDieResponse.sharePriceUntaxed': 'sharePriceUntaxed', //分摊价格（未税）
    'itemDieResponse.shareQuantity': 'shareQuantity', //实际分摊数量
    'itemDieResponse.planQuantity': 'planQuantity', //规划量
    supplierId: 'supplierId', //供应商ID
    supplierName: 'supplierName', //供应商名称
    supplierCode: 'supplierCode' //供应商编码
  }
  Object.entries(fieldMap).map(([field, key]) => {
    ctx.setValueByField(field, value && value[key] ? value[key] : null)
  })
}

// const setDieItemElementStatus = (ctx, value) => {
//   const fieldMap = {
//     "itemDieResponse.taxedUnitPrice": "taxedUnitPrice", //未税单价
//     "itemDieResponse.untaxedUnitPrice": "untaxedUnitPrice", //含税单价
//     "itemDieResponse.planSharePriceTaxed": "planSharePriceTaxed", //规划分摊价（含税）
//     "itemDieResponse.planSharePriceUntaxed": "planSharePriceUntaxed", //规划分摊价（未税）
//     "itemDieResponse.realSharePriceTaxed": "realSharePriceTaxed", //实际分摊价（含税）
//     "itemDieResponse.realSharePriceUntaxed": "realSharePriceUntaxed", //实际分摊价（未税）
//     // "itemDieResponse.sharePriceTaxed": "sharePriceTaxed", //分摊价格（含税）
//     // "itemDieResponse.sharePriceUntaxed": "sharePriceUntaxed", //分摊价格（未税）
//     // "itemDieResponse.shareQuantity": "shareQuantity", //实际分摊数量
//     // "itemDieResponse.planQuantity": "planQuantity", //规划量
//   };
//   Object.entries(fieldMap).map(([field]) => {
//     ctx.setOptions(field, { disabled: value > 0, readonly: value > 0 });
//   });
// };

export const editColumn = (
  {
    currencyNameData,
    supplierNameData,
    siteNameList,
    // itemNameList,
    skuNameList,
    taxRateNameList,
    shippingMethodNameData,
    tradeClauseNameData,
    receiveUserIdData,
    unitNameList,
    purUnitNameList,
    companyNameList,
    purGroupList,
    deptNameList,
    dictItems,
    detailInfo,
    // api
    self,
    pagedQueryUnit,
    categoryPagedQuery,
    // queryCompanyAndRel,
    queryRelCostModel,
    rfxId,
    submitTableData,
    sourcingType,
    source,
    sourcingObjType,
    priceClassification,
    companyCode,
    getPurGroup,
    rfxGeneralType
  },
  bus
) => {
  // 单位
  const unitNameListCN = addArrTextField(unitNameList, 'unitCode', 'unitName')
  const purUnitNameListCN = addArrTextField(purUnitNameList, 'unitCode', 'unitName')
  // 币种
  const currencyNameDataCN = addArrTextField(currencyNameData, 'currencyCode', 'currencyName')
  // 税率
  const taxRateNameListCN = addArrTextField(taxRateNameList, 'taxItemCode', 'taxItemName')
  // 采购组
  const purGroupListCN = addArrTextField(purGroupList, 'groupCode', 'groupName')
  // purUnitNameList
  const purUnitNameDataSource =
    rfxGeneralType === 1
      ? [
          { text: '1', value: '1' },
          { text: '1000', value: '1000' }
        ]
      : [
          { text: i18n.t('元'), value: '1' },
          { text: i18n.t('万元'), value: '0.0001' }
        ]

  const editInstance = createEditInstance()
    .component('checkSelectedItemCode', checkSelectedItemCode)
    .component('checkSelectItemDie', checkSelectItemDie)
    .component('codeInput', codeInput)
    .component('selectReferItem', selectReferItem)
    .onChange(async (ctx, { field }, event) => {
      const val = event?.value === null ? '' : event?.value
      if (ctx.rowData.itemCode && ctx.rowData.itemCode != ' ') {
        ctx.setOptions('unitName', { disabled: true, readonly: true })
      } else {
        ctx.setOptions('unitName', { disabled: false, readonly: false })
      }
      if (['categoryName', 'categoryCode'].includes(field)) {
        if (event.itemData) {
          ctx.setValueByField('categoryName', event.itemData.categoryName)
          ctx.setValueByField('categoryCode', event.itemData.categoryCode)
          ctx.setValueByField('categoryId', event.itemData.id)

          ctx.setOptions('costModelQuote', { disabled: false })
        } else {
          ctx.setValueByField('categoryName', null)
          ctx.setValueByField('categoryCode', null)
          ctx.setValueByField('categoryId', null)

          if (!ctx.rowData.itemCode) {
            updateCostModelFields()
            ctx.setValueByField('costModelQuote', 0)
            ctx.setOptions('costModelQuote', { disabled: true })
          }
        }
      }
      if (field === 'categoryName') {
        ctx.setValueByField(field, val)
        fieldLinkUpdate(
          ctx,
          field,
          {
            categoryCode: 'categoryCode',
            categoryId: 'id'
          },
          event.itemData
        )
      } else if (field === 'categoryCode') {
        if (ctx.rowData.costModelQuote == 0) {
          updateCostModelFields()
        } else if (ctx.rowData.costModelQuote == 1) {
          if (ctx.rowData.categoryCode) {
            await queryCompanyAndRelDataSource(ctx.rowData)
          }
          if (companyAndRelDataSource.length == 0) {
            updateCostModelFields()
          } else {
            updateCostModelFields(companyAndRelDataSource[0])
            ctx.setValueByField('costEstimation', i18n.t('成本测算'))
          }
        }
        ctx.setValueByField(field, val)
        fieldLinkUpdate(
          ctx,
          field,
          {
            categoryName: 'categoryName',
            categoryId: 'id'
          },
          event.itemData
        )
      } else if (field === 'itemExtMap.minQuoteRangeType') {
        ctx.setOptions('itemExtMap.minQuoteRange', {
          readonly: false
        })
      } else if (field === 'purUnitName') {
        if (event.itemData) {
          ctx.setValueByField('purUnitId', event.itemData.id)
          ctx.setValueByField('purUnitCode', event.itemData.unitCode)
        } else {
          ctx.setValueByField('purUnitId', '')
          ctx.setValueByField('purUnitCode', '')
        }
      } else if (field === 'costModelQuote') {
        if (val == 0) {
          updateCostModelFields()
          ctx.setOptions('costModelName', { disabled: true, readonly: true })
        } else {
          if (companyAndRelDataSource.length == 0) {
            updateCostModelFields()
          } else {
            updateCostModelFields(companyAndRelDataSource[0])
            ctx.setValueByField('costEstimation', i18n.t('成本测算'))
          }
          ctx.setOptions('costModelName', { disabled: false, readonly: false })
        }
      } else if (field === 'costModelName') {
        updateCostModelFields(event.itemData || {})
      } else if (field === 'taxRateCode' || field === 'taxRateName') {
        if (event.itemData) {
          ctx.setValueByField('taxRateValue', event.itemData.taxRate)
          ctx.setValueByField('taxRateCode', event.itemData.taxItemCode)
          ctx.setValueByField('taxRateName', event.itemData.taxItemName)
        } else {
          ctx.setValueByField('taxRateValue', '')
          ctx.setValueByField('taxRateCode', '')
          ctx.setValueByField('taxRateName', '')
        }
      } else if (
        field === 'itemExtMap.adviseMinPurQuantity' ||
        field === 'itemExtMap.adviseMinPackageQuantity'
      ) {
        if (
          ctx.rowData.itemExtMap.adviseMinPurQuantity != undefined &&
          ctx.rowData.itemExtMap.adviseMinPurQuantity != null &&
          ctx.rowData.itemExtMap.adviseMinPurQuantity != '' &&
          ctx.rowData.itemExtMap.adviseMinPackageQuantity != undefined &&
          ctx.rowData.itemExtMap.adviseMinPackageQuantity != null &&
          ctx.rowData.itemExtMap.adviseMinPackageQuantity != ''
        ) {
          let adviseMinPurQuantity = ctx.rowData.itemExtMap.adviseMinPurQuantity
          let adviseMinPackageQuantity = ctx.rowData.itemExtMap.adviseMinPackageQuantity
          let number = adviseMinPurQuantity / adviseMinPackageQuantity
          if (Math.floor(number) !== number) {
            bus.$emit(`contentDialog`)
            console.log('change')
          }
        }
      } else if (field === 'stepQuote') {
        // smt 切换设置1 - 60000默认值
        let itemStages = [
          { remark: '', startValue: '1' },
          { remark: '', startValue: '60000' }
        ]
        bus.$emit('changeStepQuote', val)
        if (val == 0) {
          ctx.setOptions('stepQuoteType', {
            readonly: true,
            disabled: true
          })
          ctx.setValueByField('stepQuoteType', null)
          if (sourcingObjType === 'smt') {
            ctx.setValueByField('stepQuoteName', [])
            ctx.setValueByField('itemStageList', [])
          }
        } else {
          ctx.setOptions('stepQuoteType', {
            readonly: false,
            disabled: false
          })
          ctx.setValueByField('stepQuoteType', 0)
          if (sourcingObjType === 'smt') {
            ctx.setValueByField('stepQuoteName', JSON.stringify(itemStages))
            ctx.setValueByField('itemStageList', cloneDeep(itemStages))
          }
        }
      } else if (field === 'stepQuoteType') {
        bus.$emit('changeStepQuoteType', val)
      } else if (field === 'purGroupCode') {
        if (event?.itemData) {
          ctx.setValueByField('purGroupId', event.itemData.id)
          ctx.setValueByField('purGroupName', event.itemData.groupName)
        } else {
          ctx.setValueByField('purGroupId', null)
          ctx.setValueByField('purGroupName', null)
        }
      }
      if (
        ['itemExtMap.referChannel', 'stepQuoteType', 'stepQuote', 'companyCode'].includes(field)
      ) {
        const data = {
          referChannel:
            field === 'itemExtMap.referChannel'
              ? event.itemData.value
              : ctx.rowData.itemExtMap.referChannel,
          stepQuoteType:
            field === 'stepQuoteType' ? event.itemData.value : ctx.rowData.stepQuoteType,
          sourcingObjType
        }
        sessionStorage.setItem('referItemParam', JSON.stringify(data))
      }
    })
    .onInput(async (ctx, { field, value }) => {
      bus.$emit(combination.EVENT_GRID_DATA_SOURCE_UPDATE, {
        isChildren: !!getValueByPath(editInstance.rowData, 'parentRfxItemKey'),
        resetChildItems: false,
        data: editInstance.rowData,
        field
      })
      if (field === 'itemCode') {
        if (ctx.rowData.itemCode && ctx.rowData.itemCode != ' ') {
          ctx.setOptions('unitName', { disabled: true, readonly: true })
          ctx.setOptions('costModelQuote', { disabled: false })
        } else {
          ctx.setOptions('unitName', { disabled: false, readonly: false })
          ctx.setOptions('costModelQuote', { disabled: true })
          ctx.setValueByField('costModelQuote', 0)
        }
      }
      if (field === 'costModelQuote') {
        if (ctx.rowData.costModelQuote == 0) {
          updateCostModelFields()
          ctx.setOptions('costEstimation', {
            placeholder: i18n.t('')
          })
        } else if (ctx.rowData.costModelQuote == 1) {
          await queryCompanyAndRelDataSource(ctx.rowData)
          if (companyAndRelDataSource.length == 0) {
            updateCostModelFields()
          } else {
            updateCostModelFields(companyAndRelDataSource[0])

            ctx.setValueByField('costEstimation', i18n.t('成本测算'))
            ctx.setOptions('itemExtMap.budgetUnitPriceUntaxe', {
              disabled: true,
              readonly: true
            })
          }
        }
      }
      if (field === 'unitName') {
        fieldLinkUpdate(ctx, field, {
          unitId: 'id',
          unitCode: 'unitCode'
        })
      } else if (field === 'currencyName') {
        fieldLinkUpdate(ctx, field, {
          currencyCode: 'currencyCode'
        })
      } else if (
        [
          'itemExtMap.budgetUnitPriceUntaxed',
          'taxRateValue',
          'itemExtMap.requireQuantity'
        ].includes(field)
      ) {
        //计算预算单价(含税)
        let unitPriceWithoutTax = ctx.getValueByField('itemExtMap.budgetUnitPriceUntaxed') //预算单价未税
        let taxRate = ctx.getValueByField('taxRateValue') || 0 //税率
        let demandQuantity = ctx.getValueByField('itemExtMap.requireQuantity') || 0 //需求量
        let unitPriceIncludesTax = new Decimal32(taxRate).add(1).mul(unitPriceWithoutTax).toFixed(2)
        //预算单价(含税)
        ctx.setValueByField('itemExtMap.budgetUnitPriceTaxed', unitPriceIncludesTax)
        //预算总价(未税)
        let totalPriceWithoutTax = new Decimal32(unitPriceWithoutTax).mul(demandQuantity).toFixed(2)
        ctx.setValueByField('itemExtMap.budgetTotalPriceUntaxed', totalPriceWithoutTax)
      } else if (field === 'itemExtMap.requireQuantity') {
        let unitPriceIncludesTax = ctx.getValueByField('itemExtMap.budgetUnitPriceTaxed') || 0 //预算单价含税
        let demandQuantity = ctx.getValueByField('itemExtMap.requireQuantity') || 0 //需求量
        // let unitPriceWithoutTax = ctx.getValueByField('itemExtMap.budgetUnitPriceUntaxed') || 0 //预算单价未税

        // //预算总价(未税)
        // let totalPriceWithoutTax = new Decimal32(unitPriceWithoutTax).mul(demandQuantity).toFixed(2)
        // ctx.setValueByField('itemExtMap.budgetTotalPriceUntaxed', vBudgetTotalPriceUntaxed)

        //预算总价(含税)
        let totalPriceIncludingTax = new Decimal32(unitPriceIncludesTax)
          .mul(demandQuantity)
          .toFixed(2)
        ctx.setValueByField('itemExtMap.budgetTotalPriceTaxed', totalPriceIncludingTax)
      } else if (field === 'itemDieResponse.dieFormalCode') {
        if (typeof value === 'object') {
          setDieItemValue(ctx, value)
          if (ctx.rowData?.parentRfxItemKey) {
            ctx.setValueByField('itemName', value?.itemName)
            ctx.setValueByField('itemCode', value?.itemCode)
          }
        } else {
          ctx.setValueByField('itemDieResponse.dieFormalCode', value)
        }
      } else if (field === 'itemExtMap.referChannel') {
        bus.$emit(`${field}Change`, value)
        //0手工定价  2价格记录
        //价格类型为‘价格记录’时，  规划量、实际分摊量、模具类型应只读；
        ctx.setOptions('itemDieResponse.planQuantity', {
          disabled: value > 0,
          readonly: value > 0
        })
        ctx.setOptions('itemDieResponse.shareQuantity', {
          disabled: value > 0,
          readonly: value > 0
        })
        ctx.setOptions('itemDieResponse.dieType', {
          disabled: value > 0,
          readonly: value > 0
        })
      } else if (field === 'itemDieResponse.planQuantity') {
        //默认为规划量的一半，如果不能整除的四舍五入为整数，可以修改
        ctx.setValueByField(
          'itemDieResponse.planQuantity',
          value > 0 && value < 1 ? 1 : parseInt(value)
        ) //规划量 为整数
        if (typeof value === 'number' && value > 0) {
          ctx.setValueByField(
            'itemDieResponse.shareQuantity',
            parseInt(value / 2) < 1 ? 1 : parseInt(value / 2)
          )
        } else {
          ctx.setValueByField('itemDieResponse.shareQuantity', 0)
        }
      } else if (field === 'itemDieResponse.shareQuantity') {
        //实际分摊数量 为整数
        ctx.setValueByField('itemDieResponse.shareQuantity', parseInt(value) ?? 0)
      } else if (field === 'itemDieResponse.alreadyShareQuantity') {
        //已分摊数量 为整数
        ctx.setValueByField('itemDieResponse.alreadyShareQuantity', parseInt(value) ?? 0)
      } else if (
        field === 'itemExtMap.adviseMinPurQuantity' ||
        field === 'itemExtMap.adviseMinPackageQuantity'
      ) {
        if (
          ctx.rowData.itemExtMap.adviseMinPurQuantity != undefined &&
          ctx.rowData.itemExtMap.adviseMinPurQuantity != null &&
          ctx.rowData.itemExtMap.adviseMinPurQuantity != '' &&
          ctx.rowData.itemExtMap.adviseMinPackageQuantity != undefined &&
          ctx.rowData.itemExtMap.adviseMinPackageQuantity != null &&
          ctx.rowData.itemExtMap.adviseMinPackageQuantity != ''
        ) {
          setTimeout(() => {
            let adviseMinPurQuantity = ctx.rowData.itemExtMap.adviseMinPurQuantity
            let adviseMinPackageQuantity = ctx.rowData.itemExtMap.adviseMinPackageQuantity
            let number = adviseMinPurQuantity / adviseMinPackageQuantity
            if (Math.floor(number) !== number) {
              bus.$emit(`contentDialog`)
            }
          }, 1500)
        }
      }
    })

  const updateCostModelFields = (itemData = {}) => {
    const { id, costModelCode, costModelName, versionCode } = itemData
    editInstance.setValueByField('costModelId', id || '')
    editInstance.setValueByField('costModelCode', costModelCode || '')
    editInstance.setValueByField('costModelName', costModelName || '')
    editInstance.setValueByField('costModelVersionCode', versionCode || '')
    editInstance.setValueByField('costModelVersion', versionCode || '')
    editInstance.setValueByField('costEstimation', id ? i18n.t('成本测算') : '')
  }

  let categoryDataSource = []
  let companyAndRelDataSource = []
  // 获取成本模型
  async function queryCompanyAndRelEx(arg) {
    console.log('detailInfo', arg)
    let params = {
      rfxId,
      categoryCode: arg.categoryCode || '',
      companyCodeList: [detailInfo.companyCode || ''],
      relCode: arg.itemCode || arg.categoryCode,
      relationType: sourcingObjType === 'cost_factor' ? 2 : 1, // 品类:0、物料:1、行情因子:2
      page: { current: 1, size: 20 }
    }
    const res = await queryRelCostModel(params).catch(() => {})
    return res?.data?.records || []
  }
  const queryCompanyAndRelDataSource = async (...arg) => {
    console.log('queryCompanyAndRelDataSource', arg)
    const records = await queryCompanyAndRelEx(...arg)
    // mergeCompanyAndRelDataSource(records)
    companyAndRelDataSource = records
    initCompanyAndRelDataSource()
    return records
  }

  // function mergeCompanyAndRelDataSource(records) {
  //   for (const record of records) {
  //     console.log('11111-merge')
  //     !companyAndRelDataSource.find((e) => e.id === record.id) &&
  //       companyAndRelDataSource.push(record)
  //   }
  //   companyAndRelDataSource.sort((a) => (records.find((e) => e.id === a.id) ? -1 : 1))
  //   initCompanyAndRelDataSource()
  // }

  function initCompanyAndRelDataSource() {
    editInstance.setOptions('costModelName', {
      dataSource: [...companyAndRelDataSource]
    })
    editInstance.setOptions('costModelId', {
      dataSource: [...companyAndRelDataSource]
    })
  }
  // 获取品类名称
  async function categoryPagedQueryEx(value) {
    let params = {
      page: { current: 1, size: 20 }
    }
    if (value) {
      params = {
        ...params,
        condition: 'or',
        rules: [
          {
            field: 'categoryName',
            type: 'string',
            operator: 'contains',
            value
          },
          {
            field: 'categoryCode',
            type: 'string',
            operator: 'contains',
            value
          }
        ]
      }
    }
    const res = await categoryPagedQuery(params).catch(() => {})
    const records = res?.data?.records || []
    return addArrTextField(records, 'categoryCode', 'categoryName')
  }

  const queryCategoryDataSource = async (...arg) => {
    const records = await categoryPagedQueryEx(...arg)
    mergeCategoryDataSource(records)
    return records
  }

  function mergeCategoryDataSource(records) {
    addArrTextField(records, 'categoryCode', 'categoryName')
    categoryDataSource = records
    initCategoryDataSource()
  }

  function initCategoryDataSource() {
    editInstance.setOptions('categoryName', {
      dataSource: categoryDataSource
    })
    editInstance.setOptions('categoryCode', {
      dataSource: categoryDataSource
    })
  }

  // 兼容之前的全局事件
  const busFields = [
    'purUnitName',
    'unitName',
    'unitCode',
    'categoryName',
    'categoryCode',
    'categoryId',
    'itemId',
    'itemCode',
    'itemName',
    'categoryResponse',
    'material',
    'stockSite',
    'spec',
    'brand'
  ]
  busFields.forEach((busField) => {
    bus.$on(`${busField}Change`, async (data) => {
      if (busField === 'categoryResponse') {
        if (data && typeof data === 'object') {
          mergeCategoryDataSource([
            {
              categoryName: data.categoryName,
              categoryCode: data.categoryCode,
              id: data.id
            }
          ])
          editInstance.setValueByField('categoryName', data.categoryName)
          editInstance.setValueByField('categoryCode', data.categoryCode)
          editInstance.setValueByField('categoryId', data.id)
          editInstance.setOptions('categoryName', {
            disabled: true
          })
          editInstance.setOptions('categoryCode', {
            disabled: true
          })
          editInstance.setOptions('categoryId', {
            disabled: true
          })
        } else {
          editInstance.setValueByField('categoryName', null)
          editInstance.setValueByField('categoryCode', null)
          editInstance.setValueByField('categoryId', null)
          editInstance.setOptions('categoryName', {
            disabled: false
          })
          editInstance.setOptions('categoryCode', {
            disabled: false
          })
          editInstance.setOptions('categoryId', {
            disabled: false
          })
        }
      } else {
        if (busField === 'itemId') {
          combination.onChangeItemId({
            editInstance,
            busField,
            data,
            cloneDeep,
            bus,
            self
          })
        }
        editInstance.setValueByField(busField, data)
      }
      if (busField === 'itemCode') {
        bus.$emit('drawingUrlChange', data)
        if (editInstance.getValueByField('siteCode') && !!data) {
          let _params = {
            itemCode: data, // 物料code
            organizationCode: editInstance.getValueByField('siteCode') //工厂code
          }
          //查询采购组
          getPurGroup(_params).then((res) => {
            editInstance.setValueByField('purGroupName', res.data?.purchaseGroupName)
            editInstance.setValueByField('purGroupCode', res.data?.purchaseGroupCode)
          })
        }
      }
    })
  })
  bus.$on('itemStageListChange', (data) => {
    editInstance.setValueByField('itemStageList', data)
    editInstance.setValueByField('stepQuoteName', JSON.stringify(data))
    // editInstance.setValueByField("itemDieResponse.taxedUnitPrice", data[0].untaxedUnitPrice);
  })
  // 选择参考物料编码
  bus.$on('referItemNameChange', (data) => {
    editInstance.setValueByField('itemExtMap.referItemName', data?.itemName || null)
    editInstance.setValueByField('itemExtMap.referItemCode', data?.itemCode || null)

    if (sourcingObjType === 'module_out_buy') {
      // 选择参考物料需要带出的相关信息
      const referItemExtFields = [
        { field: 'refScreenCode', valueField: 'screenCode' },
        { field: 'refScreenPrice', valueField: 'screenPrice' },
        { field: 'refTconCode', valueField: 'tconCode' },
        { field: 'refTconPrice', valueField: 'tconPrice' },
        { field: 'refTotalPrice', valueField: 'totalPrice' }
      ]
      referItemExtFields.forEach((item) => {
        editInstance.setValueByField('itemExtMap.' + item.field, data[item.valueField])
      })
    }
  })
  bus.$on('extStageSaveRequestsChange', (list) => {
    editInstance.setValueByField('extStageSaveRequests', list)

    const arr = ['power_panel', 'module_out_going']
    if (arr.includes(sourcingObjType) && editInstance.rowData.stepQuote === 1) {
      const itemStageList = []
      list.forEach((item) => {
        itemStageList.push({ startValue: item.startValue, remark: '' })
      })
      editInstance.setValueByField('itemStageList', itemStageList)
      editInstance.setValueByField('stepQuoteName', JSON.stringify(itemStageList))
    }
  })
  const referChannelList = [
    {
      text: i18n.t('手工定价'),
      value: 0
    },
    // {
    //   text: i18n.t("引用寻源结果"),
    //   value: 1,
    // },
    {
      text: i18n.t('价格记录'),
      value: 2
    }
  ]

  return [
    // ckd
    ...combination.editColumn(self, { editInstance }),
    {
      field: 'lineNo',
      headerText: i18n.t('行号'),
      allowEditing: false
    },
    {
      field: 'itemDieResponse.dieFormalCode',
      headerText: i18n.t('正式模具编码'),
      width: 140,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'checkSelectItemDie',
          field: 'itemDieResponse.dieFormalCode',
          rfxId: rfxId,
          sourcingType: sourcingType,
          source: source,
          editInstance
        })
      })
    },
    {
      field: 'itemDieResponse.dieTempCode',
      headerText: i18n.t('临时模具编码'),
      width: 140,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'codeInput'
        })
      })
    },
    //模具类型
    {
      field: 'itemDieResponse.dieType',
      headerText: i18n.t('模具类型'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'select',
          'show-clear-button': true,
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { value: 1, text: i18n.t('基础模具') },
            { value: 2, text: i18n.t('复制模具') },
            { value: 3, text: i18n.t('基础模改模') },
            { value: 4, text: i18n.t('复制模改模') }
          ],
          readonly: false,
          disabled: false
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        switch (Number(cellVal)) {
          case 1:
            return i18n.t('基础模具')
          case 2:
            return i18n.t('复制模具')
          case 3:
            return i18n.t('基础模改模')
          case 4:
            return i18n.t('复制模改模')
          default:
            return cellVal
        }
      }
    },
    // 物流模板
    ...logisticsColumnData({
      prefix: 'itemLogisticsResponse.',
      dictItems,
      detailInfo,
      handleSelectChange
    }),
    // 运输模板
    ...itemExtMapColumnData({
      dictItems,
      detailInfo,
      submitTableData,
      handleSelectChange
    }).map((e) => {
      let edit = editInstance.create()
      return {
        ...e,
        edit
      }
    }),
    // 模具分摊
    ...itemDieColumnData({
      dictItems,
      detailInfo,
      handleSelectChange
    }).map((e) => {
      let edit = editInstance.create()
      return {
        ...e,
        edit
      }
    }),
    {
      field: 'currencyName', //成本因子-币种名称
      headerText: i18n.t('币种名称'),
      edit: editInstance.create({
        getEditConfig: () => {
          const readonly = !!detailInfo.currencyCode
          return {
            type: 'select',
            'show-clear-button': true,
            fields: makeTextFields('currencyName'),
            dataSource: currencyNameDataCN,
            placeholder: i18n.t('选择币种'),
            // 创建标案选择的币种要带入采购明细，且不能编辑
            readonly: readonly,
            disabled: readonly,
            'allow-filtering': true,
            filtering: useFiltering(filteringByText)
          }
        }
      })
    },
    {
      field: 'currencyCode',
      headerText: i18n.t('币种编码'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          readonly: true,
          disabled: true
        })
      })
    },
    {
      field: 'bidCurrencyName', //价格信息-币种名称
      headerText: i18n.t('币种名称'),

      editConfig: {
        type: 'select',
        'show-clear-button': true,
        ignoreCase: false,
        fields: makeTextFields('currencyName'),
        dataSource: currencyNameDataCN,
        placeholder: i18n.t('选择币种'),
        callback: handleSelectChange,
        'allow-filtering': true,
        filtering: useFiltering(filteringByText)
      }
    },
    {
      field: 'baseCurrencyName', //价格信息-本位币
      headerText: i18n.t('币种名称'),

      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: makeTextFields('currencyName'),
        dataSource: currencyNameDataCN,
        placeholder: i18n.t('选择币种'),
        callback: handleSelectChange,
        'allow-filtering': true,
        filtering: useFiltering(filteringByText)
      }
    },
    {
      field: 'supplierName', //供应商信息-供应商名称
      headerText: i18n.t('供应商名称'),

      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: { value: 'supplierName', text: 'supplierName' },
        dataSource: supplierNameData,
        placeholder: i18n.t('选择供应商'),
        callback: handleSelectChange
      }
    },
    {
      field: 'referItemSupplierName', //参考物料-参考物料供应商名称
      headerText: i18n.t('参考物料供应商名称'),

      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: { value: 'supplierName', text: 'supplierName' },
        dataSource: supplierNameData,
        placeholder: i18n.t('选择供应商'),
        callback: handleSelectChange
      }
    },
    {
      field: 'siteCode', //基础信息-工厂名称
      headerText: i18n.t('工厂编码'),

      edit: editInstance.create({
        onChange: (ctx, _, args) => {
          if (args.itemData && typeof args.itemData === 'object') {
            ctx.setValueByField('siteId', args.itemData.id)
            ctx.setValueByField('siteName', args.itemData.siteName)
          } else {
            ctx.setValueByField('siteId', '')
            ctx.setValueByField('siteName', '')
          }
          if (args?.itemData?.organizationId) {
            sessionStorage.setItem(
              'siteParam',
              JSON.stringify({
                organizationId: args?.itemData?.organizationId,
                siteCode: args?.itemData?.siteCode
              })
            )
          } else {
            sessionStorage.removeItem('siteParam')
          }
        },
        getEditConfig: ({ rowData }) => ({
          type: 'select',
          'show-clear-button': true,
          fields: { value: 'siteCode', text: 'siteCode' },
          dataSource: siteNameList,
          placeholder: i18n.t('选择工厂/地点'),
          disabled: !!rowData.itemCode
        })
      })
    },
    {
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      allowEditing: false,
      edit: editInstance.create({
        getEditConfig: EditConfig.readonly
      })
    },
    {
      field: 'categoryId',
      headerText: i18n.t('品类ID'),
      allowEditing: false,
      edit: editInstance.create({
        valueConvert: (val) => (val === null ? '' : val),
        getEditConfig: () => ({
          type: 'text',
          readonly: true,
          disabled: true
        })
      })
    },
    {
      field: 'categoryCode',
      headerText: i18n.t('品类编码'),
      allowEditing: true,
      edit: editInstance.create({
        valueConvert: (val) => (val === null ? '' : val),
        getEditConfig: ({ rowData }) => {
          return {
            type: 'select',
            // 有物料的不能编辑
            // > 物料肯定是有品类的，没有品类的物料那应该是数据问题
            disabled: !!(rowData.itemCode && rowData.categoryCode),
            'show-clear-button': true,
            'allow-filtering': true,
            // 'item-template': () => setItemTemplate('__text'),
            isUseItemTemplate: false,
            created: async function () {
              // 品类是搜索带出的,非一次性加载
              // 品类名称需要和品类编码的 dataSource 保持一致
              // 品类名称需要和品类编码保持互相联动
              const categoryCode = rowData.categoryCode
              initCategoryDataSource()
              await queryCategoryDataSource().catch((err) => console.warn(err))
              if (!categoryDataSource.find((e) => e.categoryCode === categoryCode)) {
                await queryCategoryDataSource(categoryCode).catch((err) => console.warn(err))
              }
              setTimeout(() => {
                editInstance.setValueByField('categoryCode', categoryCode)
              }, 0)
            },
            filtering: useFiltering(async function (e) {
              e.updateData(await queryCategoryDataSource(e.text))
            }),
            fields: makeTextFields('categoryCode'),
            dataSource: []
          }
        }
      })
    },
    {
      field: 'categoryName',
      headerText: i18n.t('品类名称'),
      width: 200,
      allowEditing: true,
      edit: editInstance.create({
        valueConvert: (val) => (val === null ? '' : val),
        getEditConfig: ({ rowData }) => ({
          type: 'select',
          // 有物料的不能编辑
          disabled: !!(rowData.itemCode && rowData.categoryCode),
          'show-clear-button': true,
          'allow-filtering': true,
          // 'item-template': () => setItemTemplate('__text'),
          isUseItemTemplate: false,
          created: async function () {
            initCategoryDataSource()
            await queryCategoryDataSource().catch((err) => console.warn(err))
            if (!categoryDataSource.find((e) => e.categoryName === rowData.categoryName)) {
              await queryCategoryDataSource(rowData.categoryName).catch((err) => console.warn(err))
            }
          },
          filtering: useFiltering(async function (e) {
            e.updateData(await queryCategoryDataSource(e.text))
          }),
          fields: makeTextFields('categoryName'),
          dataSource: []
        })
      })
    },
    {
      field: 'costModelId',
      headerText: i18n.t('成本模型id'),
      allowEditing: false,
      width: '0',
      edit: editInstance.create({
        valueConvert: (val) => (val === null ? '' : val),
        // eslint-disable-next-line no-unused-vars
        getEditConfig: ({ rowData }) => ({
          type: 'text',
          disabled: true
        })
      })
    },
    {
      field: 'costModelCode',
      headerText: i18n.t('成本模版编码'),
      allowEditing: false,
      // visiable: false,
      width: '0',
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true
        })
      })
    },
    {
      field: 'costModelVersion',
      headerText: i18n.t('成本模版本号'),
      allowEditing: false,
      visiable: false,
      width: '0',
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true
        })
      })
    },
    {
      field: 'costModelVersionCode',
      headerText: i18n.t('成本模版本号'),
      allowEditing: false,
      visiable: false,
      width: '0',
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true
        })
      })
    },
    {
      field: 'costModelName',
      headerText: i18n.t('成本模型'),
      allowEditing: true,
      cssClass: 'field-content',
      edit: editInstance.create({
        valueConvert: (val) => (val === null ? '' : val),
        getEditConfig: ({ rowData }) => ({
          type: 'select',
          disabled: rowData.costModelQuote === 1 ? false : true,
          'show-clear-button': true,
          'allow-filtering': true,
          filtering: async (e) => {
            await queryCompanyAndRelDataSource(e.text, 'costModelName')
          },
          fields: { text: 'costModelName', value: 'costModelName' },
          dataSource: companyAndRelDataSource || []
        })
      })
    },
    {
      field: 'costModelQuote',
      headerText: i18n.t('是否成本模型'),
      allowEditing: true,
      edit: editInstance.create({
        getEditConfig: ({ rowData }) => {
          return {
            type: 'select',
            disabled: rowData.itemCode || rowData.categoryCode ? false : true,
            dataSource: [
              { text: i18n.t('否'), value: 0 },
              { text: i18n.t('是'), value: 1 }
            ],
            created: async function () {
              if (rowData.costModelQuote === 1) {
                await queryCompanyAndRelDataSource(rowData)
              }
              if (rowData.costModelQuote != 0 && rowData.costModelQuote != 1) {
                setTimeout(() => {
                  editInstance.setValueByField('costModelQuote', 0)
                }, 0)
              }
            }
          }
        }
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        switch (Number(cellVal)) {
          case 0:
            return i18n.t('否')
          case 1:
            return i18n.t('是')
          default:
            return cellVal
        }
      }
    },
    {
      field: 'costEstimation',
      headerText: i18n.t('成本测算'),
      allowEditing: false,
      cssClass: 'field-content',
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          disabled: true
        })
      })
    },
    {
      field: 'costModelEstimatePrice',
      headerText: i18n.t('测算价格'),
      allowEditing: false,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          disabled: true
        })
      })
    },
    {
      field: 'spec',
      headerText: i18n.t('规格描述'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'material',
      headerText: i18n.t('材质'),
      allowEditing: true,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'brand',
      headerText: i18n.t('品牌'),
      allowEditing: true,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'stockSite',
      headerText: i18n.t('组织'),
      allowEditing: true,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'unitName',
      headerText: i18n.t('基本单位'),
      allowEditing: true,

      edit: editInstance.create({
        getEditConfig: ({ rowData }) => ({
          type: 'select',
          'show-clear-button': true,
          readonly: rowData.itemCode ? true : false,
          disabled: rowData.itemCode ? true : false,
          fields: makeTextFields('unitName'),
          'allow-filtering': true,
          filtering: useFiltering(filteringByText),
          dataSource: unitNameListCN,
          created: () => {
            let row = null
            if (rowData.unitName) {
              row = unitNameListCN.find((e) => e.unitCode === rowData.unitCode)
            }
            if (row) {
              setTimeout(() => {
                editInstance.setValueByField('unitName', row.unitName)
              }, 0)
            }
          }
        })
      })
    },
    {
      field: 'unitId',
      headerText: i18n.t('基本单位ID'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          readonly: true,
          disabled: true
        })
      })
    },
    {
      field: 'unitCode',
      headerText: i18n.t('基本单位编码'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          readonly: true,
          disabled: true
        })
      })
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      width: 200,
      edit: editInstance.create({
        onInput: (ctx, { field }) => {
          if (ctx.getValueByField(field)) {
            ctx.setOptions('siteCode', {
              disabled: true
            })
          } else {
            ctx.setOptions('siteCode', {
              disabled: false
            })
          }
        },
        getEditConfig: () => ({
          type: 'checkSelectedItemCode',
          field: 'itemCode',
          rfxId: rfxId,
          submitTableData: submitTableData,
          sourcingType: sourcingType,
          // sourcingObjType: detailInfo.sourcingObjType,
          source: source,
          sourcingObjType: sourcingObjType,
          companyCode: companyCode,
          priceClassification: priceClassification
        })
      })
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料名称'),
      allowEditing: true,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          companyCode: detailInfo.companyCode,
          priceClassification: priceClassification
        })
      })
    },
    {
      field: 'itemId',
      headerText: i18n.t('物料Id'),
      allowEditing: false,
      width: 1,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'taxRateValue',
      headerText: i18n.t('税率值'),
      width: 200,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          min: 0,
          max: MAX_SAFE_INTEGER,
          precision: 2, //限制两小数点
          disabled: true
        })
      })
    },
    {
      field: 'taxRateCode',
      headerText: i18n.t('税率编码'),
      width: 200,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'select',
          'show-clear-button': true,
          fields: makeTextFields('taxItemCode'),
          dataSource: taxRateNameListCN,
          'allow-filtering': true,
          filtering: useFiltering(filteringByText)
        })
      })
    },
    {
      field: 'taxRateName',
      headerText: i18n.t('税率名称'),
      width: 200,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'select',
          'show-clear-button': true,
          fields: makeTextFields('taxItemName'),
          dataSource: taxRateNameListCN,
          'allow-filtering': true,
          filtering: useFiltering(filteringByText)
        })
      })
    },
    {
      field: 'drawingUrl',
      headerText: i18n.t('图纸协同'),
      width: 150,
      template: function () {
        return {
          template: cellDrawingView
        }
      },
      editTemplate: () => {
        return {
          template: cellDrawingView
        }
      }
    },
    {
      field: 'drawing',
      headerText: i18n.t('图纸'),
      width: 150,
      // 使用template时，新增一行且未保存时，获取不到index（编辑状态，正常的template不会的）
      // 使用editTemplate时，显示的值不能是对象
      template: function () {
        return {
          template: cellFileView
        }
      },
      editTemplate: () => {
        return {
          template: cellUpload
        }
      }
    },
    {
      field: 'supplierDrawing',
      headerText: i18n.t('供应商附件'),
      width: 150,
      // 使用template时，新增一行且未保存时，获取不到index（编辑状态，正常的template不会的）
      // 使用editTemplate时，显示的值不能是对象
      template: function () {
        return {
          template: cellFileView
        }
      },
      editTemplate: () => {
        return {
          template: cellFileView
        }
      }
    },
    {
      field: 'skuName', //物料信息-SKU名称
      headerText: i18n.t('SKU名称'),

      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: { value: 'name', text: 'name' },
        dataSource: skuNameList,
        placeholder: i18n.t('选择SKU'),
        callback: handleSelectChange
      }
    },
    {
      field: 'taxRateName', //成本因子-税率名称
      headerText: i18n.t('税率名称'),

      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: makeTextFields('taxItemName'),
        dataSource: taxRateNameListCN,
        placeholder: i18n.t('选择税率'),
        callback: handleSelectChange,
        'allow-filtering': true,
        filtering: useFiltering(function (e) {
          if (typeof e.text === 'string' && e.text) {
            e.updateData(this.dataSource.filter((f) => f?.taxItemName.indexOf(e.text) > -1))
          } else {
            e.updateData(this.dataSource)
          }
        })
      }
    },
    {
      field: 'bidTaxRateName', //价格信息-税率名称
      headerText: i18n.t('税率名称'),

      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: makeTextFields('taxItemName'),
        dataSource: taxRateNameListCN,
        placeholder: i18n.t('选择税率'),
        callback: handleSelectChange,
        'allow-filtering': true,
        filtering: useFiltering(function (e) {
          if (typeof e.text === 'string' && e.text) {
            e.updateData(this.dataSource.filter((f) => f?.taxItemName.indexOf(e.text) > -1))
          } else {
            e.updateData(this.dataSource)
          }
        })
      }
    },
    {
      field: 'tradeTerms', //运输信息-贸易条款
      headerText: i18n.t('贸易条款'),
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: { value: 'itemName', text: 'itemName' },
        dataSource: tradeClauseNameData,
        placeholder: i18n.t('选择贸易条款'),
        callback: handleSelectChange
      }
    },
    {
      field: 'logisticsTerm', //物流信息-贸易条款
      headerText: i18n.t('贸易条款'),
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: { value: 'itemName', text: 'itemName' },
        dataSource: tradeClauseNameData,
        placeholder: i18n.t('选择贸易条款'),
        callback: handleSelectChange
      }
    },
    {
      field: 'logisticsMode', //运输信息-物流方式
      headerText: i18n.t('物流方式'),

      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: { value: 'itemName', text: 'itemName' },
        dataSource: shippingMethodNameData,
        placeholder: i18n.t('选择物流方式'),
        callback: handleSelectChange
      }
    },
    {
      field: 'requireUserName', //基础信息-需求人名称
      headerText: i18n.t('需求人名称'),

      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: { value: 'employeeName', text: 'employeeName' },
        dataSource: receiveUserIdData,
        placeholder: i18n.t('选择需求人'),
        callback: handleSelectChange
      }
    },
    {
      field: 'priceUnitName', //BOM信息-价格单位  成本因子-价格单位  价格信息-价格单位
      headerText: i18n.t('价格单位'),
      valueAccessor: (field, data) => {
        const item = purUnitNameDataSource.find((el) => el.value === data[field])
        return item ? item.text : ''
      },
      // edit: editInstance.create({
      //   getEditConfig: () => {
      //     return {
      //       ...PRICE_EDIT_CONFIG,
      //       type: 'number'
      //     }
      //   }
      // })
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'select',
          dataSource: purUnitNameDataSource
        })
      })
    },
    {
      field: 'purUnitName',
      headerText: i18n.t('订单单位'),
      edit: editInstance.create({
        valueConvert: (val, { options, column }) => {
          const dataSource = options.dataSource || []
          const row = dataSource.find((e) => e.id === val)
          const purUnitName = row?.unitName || ''
          editInstance.setValueByField(column.field, purUnitName)
          return purUnitName
        },
        getEditConfig: ({ rowData }) => ({
          type: 'select',
          'show-clear-button': true,
          fields: makeTextFields('id'),
          dataSource: purUnitNameListCN,
          placeholder: rowData.purUnitName,
          'allow-filtering': true,
          created: () => {
            let row
            if (rowData.purUnitCode) {
              row = purUnitNameListCN.find((e) => e.unitCode === rowData.purUnitCode)
            } else if (rowData.purUnitName) {
              row = purUnitNameListCN.find((e) => e.unitName === rowData.purUnitName)
            }
            if (row) {
              setTimeout(() => {
                editInstance.setValueByField('purUnitName', row.id)
              }, 0)
            }
          },
          filtering: useFiltering(function (e) {
            pagedQueryUnit({
              condition: 'or',
              page: { current: 1, size: 20 },
              rules: [
                {
                  field: 'unitName',
                  type: 'string',
                  operator: 'contains',
                  value: e.text
                },
                {
                  field: 'unitCode',
                  type: 'string',
                  operator: 'contains',
                  value: e.text
                }
              ]
            }).then((res) => {
              if (Array.isArray(res?.data?.records)) {
                const records = addArrTextField(res.data.records, 'unitCode', 'unitName')
                mergeDataSource(editInstance, 'purUnitName', records)
                e.updateData(records)
              }
            })
          })
        })
      })
    },
    {
      field: 'purUnitId',
      headerText: i18n.t('订单单位ID'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          readonly: true,
          disabled: true
        })
      })
    },
    {
      field: 'purUnitCode',
      headerText: i18n.t('订单单位编码'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          readonly: true,
          disabled: true
        })
      })
    },
    {
      field: 'companyName', //基础信息-公司
      headerText: i18n.t('公司'),
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: { value: 'orgName', text: 'orgName' },
        dataSource: companyNameList,
        placeholder: i18n.t('选择公司'),
        callback: handleSelectChange
      }
    },
    {
      field: 'requireDeptName', //基础信息-需求部门
      headerText: i18n.t('需求部门'),
      editConfig: {
        type: 'select',
        'show-clear-button': true,
        fields: { value: 'departmentName', text: 'departmentName' },
        dataSource: deptNameList,
        placeholder: i18n.t('选择部门'),
        callback: handleSelectChange
      }
    },
    {
      field: 'stepQuoteName', //阶梯报价
      headerText: i18n.t('阶梯报价'),
      width: 150,
      allowFiltering: false,
      template: function () {
        return {
          template: stepViewTemplate
        }
      },
      editTemplate: () => {
        return {
          template: stepEditTemplate
        }
      }
    },
    {
      field: 'stepQuote', //阶梯类型
      headerText: i18n.t('是否阶梯报价'),
      width: 200,
      formatter: function ({ field }, item) {
        const cellVal = item[field]
        return cellVal === 1 ? i18n.t('是') : i18n.t('否')
      },
      allowEditing: true,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'select',
          'show-clear-button': true,
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { text: i18n.t('否'), value: 0 },
            { text: i18n.t('是'), value: 1 }
          ],
          readonly: false,
          disabled: false
        })
      }),
      valueConverter: {
        type: 'map',
        map: {
          0: i18n.t('否'),
          1: i18n.t('是'),
          null: ''
        }
      }
    },
    {
      field: 'stepQuoteType', //阶梯类型
      headerText: i18n.t('阶梯类型'),
      width: 200,
      edit: editInstance.create({
        getEditConfig: ({ rowData }) => ({
          type: 'select',
          'show-clear-button': true,
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { value: 0, text: i18n.t('数量累计阶梯') },
            { value: 3, text: i18n.t('数量逐层阶梯') }
          ],
          placeholder: i18n.t('请选择'),
          disabled: rowData.stepQuote !== 1,
          readonly: rowData.stepQuote !== 1,
          dictItems: false,
          callback: handleSelectChange
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return cellVal === 0
          ? i18n.t('数量累计阶梯')
          : cellVal === 3
          ? i18n.t('数量逐层阶梯')
          : '--'
      },
      valueConverter: {
        type: 'map',
        map: {
          0: i18n.t('数量累计阶梯'),
          3: i18n.t('数量逐层阶梯'),
          null: '--'
        }
      }
    },
    {
      field: 'itemExtMap.referChannel',
      headerText: i18n.t('价格来源'),
      allowEditing: false,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          dataSource: referChannelList,
          disabled: true,
          readonly: true
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return referChannelList.find((e) => e.value === cellVal)?.text ?? cellVal
      }
    },
    {
      field: 'itemDieResponse.taxedUnitPrice',
      headerText: i18n.t('单价（含税）'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          readonly: false,
          disabled: false
        })
      })
    },
    {
      field: 'itemDieResponse.untaxedUnitPrice',
      headerText: i18n.t('单价（未税）'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          readonly: false,
          disabled: false
        })
      })
    },
    {
      field: 'purGroupCode',
      headerText: i18n.t('采购组代码'),
      width: 230,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'select',
          'show-clear-button': true,
          fields: makeTextFields('groupCode'),
          dataSource: purGroupListCN,
          'allow-filtering': true,
          'filter-type': 'Contains'
        })
      })
    },
    {
      field: 'purGroupName',
      headerText: i18n.t('采购组名称'),
      width: 200,
      allowEditing: false,
      edit: editInstance.create({
        getEditConfig: () => ({
          disabled: true,
          readonly: true
        })
      })
    }
  ]
}
