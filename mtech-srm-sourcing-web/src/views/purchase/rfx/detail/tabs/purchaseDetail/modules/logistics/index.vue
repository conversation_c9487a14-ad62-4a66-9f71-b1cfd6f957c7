<template>
  <!-- 通用:采购明细页面 -->
  <div class="flex-full-height">
    <CustomAgGrid
      ref="CustomAgGrid"
      v-clickoutside="clickoutside"
      :toolbar="toolbar"
      :columns="columns"
      :suppress-row-transform="true"
      :row-data="tableData"
      :animate-rows="false"
      :is-stop-editing-on-blur="isStopEditingOnBlur"
      :get-row-id="getRowId"
      @handleClickToolbar="handleClickToolbar"
      @onGridReady="onGridReady"
      @refresh="refresh"
      @search="search"
      @cell-value-changed="cellValueChanged"
    >
    </CustomAgGrid>
  </div>
</template>

<script>
import CustomAgGrid from '@/components/CustomAgGrid'
import MixIn from '../../config/mixin.js'
export default {
  name: 'PurchaseDetail',
  mixins: [MixIn],
  components: {
    CustomAgGrid
  },
  data() {
    return {
      pageType: 'Logistics',
      logisticsPurItemConfigList: [], // 采购申请明细字段
      logisticsCanEdit: false
    }
  },
  async created() {
    // await this.getPurApplyItemConfig()
  },
  computed: {},
  watch: {
    logisticsCanEdit(value) {
      value && this.initToolbar(value)
    }
  },
  methods: {
    // ag - 获取rowId
    getRowId(params) {
      return params.data.addId
    },
    // 初始化 - searchConfig
    initSearchConfig() {
      let _config = [
        {
          field: 'itemName',
          label: this.$t('品名')
        }
      ]
      this.searchConfig = _config
    },
    // 初始化 - toolbar
    initToolbar(canEdit) {
      let _toolbar = [
        {
          id: 'Add',
          icon: 'icon_solid_Createproject',
          title: this.$t('新增'),
          hide: canEdit ? false : true
        },
        {
          id: 'Save',
          icon: 'icon_solid_Createproject',
          title: this.$t('保存'),
          hide: canEdit ? false : true
        },
        {
          id: 'Export',
          icon: 'icon_solid_Download ',
          title: this.$t('导出')
        }
      ]
      this.toolbar = _toolbar
    },
    // 接口请求 - 获取采购申请需求明细
    async getPurApplyItemConfig() {
      await this.$API.rfxRequireDetail
        .getBusinessConfigDetail({ docId: '1729320825673281537' }) //333
        .then((res) => {
          let _fieldDefines = []
          let moduleItems = res.data?.moduleItems || []
          moduleItems.forEach((item) => {
            if (item.moduleType === 0) {
              // 采购明细
              _fieldDefines = item.fieldDefines || []
            }
          })
          this.businessConfigList = _fieldDefines.map((item) => item.fieldCode)
        })
    }
  }
}
</script>
