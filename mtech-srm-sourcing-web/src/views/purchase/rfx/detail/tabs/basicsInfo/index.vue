<template>
  <div>
    <div class="toolbar">
      <div
        :class="{
          'svg-option-item': true
        }"
        @click="editProject"
        v-show="editable && !bidFailed"
      >
        <mt-icon name="icon_solid_Save" />
        <span>{{ $t('保存') }}</span>
      </div>
    </div>
    <div class="top-form-generator">
      <!-- <mt-button
        type="primary"
        v-show="editable && !bidFailed"
        @click.native="editProject"
        class="btn"
        >{{ $t('保存') }}</mt-button
      > -->
      <FormGenerator
        ref="form"
        :field-defines="fieldDefines"
        :form-field-config="formFieldConfig"
        :sourcing-expand-list="sourcingExpandList"
      ></FormGenerator>
    </div>
  </div>
</template>

<script>
import FormGenerator from '../../components/FormGenerator.vue'
import { createFiltering } from '@/utils/ej/select'
import { isEqual } from 'lodash'

export default {
  name: 'RFXHeader',
  components: {
    FormGenerator
  },
  props: {
    moduleType: {
      type: Number,
      default: -1
    },
    detailInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      formData: {},
      fieldDefines: [],
      formFieldConfig: {},
      sourcingExpandList: []
    }
  },
  watch: {
    detailInfo: {
      handler(val) {
        if (val.id) this.getUserConfigFields()
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {},
  computed: {
    editable() {
      return (
        this.detailInfo.approveStatus === 0 ||
        (this.detailInfo.transferStatus != 20 && this.detailInfo.transferStatus != 100)
      )
    },
    bidFailed() {
      // 竞价、流标状态下  不显示‘保存’按钮
      return this.detailInfo.status == -8 && this.$route.query.source == 'bidding_price'
    }
  },
  methods: {
    getUserConfigFields() {
      let remarkEditStatus = [20, 100]
      let queryEditStatus = [-1, 8, -8, '-1', '8', '-8']
      this.$API.rfxList
        .getUserConfigFields({
          sourcingObj: this.detailInfo.sourcingObj,
          sourcingMode: this.detailInfo.sourcingMode,
          businessTypeCode: this.detailInfo.businessTypeCode
        })
        .then((res) => {
          this.formData = {
            sourcingObj: this.detailInfo.sourcingObj,
            sourcingObjType: this.detailInfo.sourcingObjType,
            purExecutorName: this.detailInfo.purExecutorName,
            purExecutorId: this.detailInfo.purExecutorId
          }
          this.formFieldConfig = {
            // strategyConfigName: {
            //   type: "text",
            //   defaultValue: () => this.detailInfo.strategyConfigName,
            //   readonly: true,
            // },
            //标题
            rfxName: {
              defaultValue: () => this.detailInfo.rfxName,
              type: 'text',
              readonly:
                remarkEditStatus.indexOf(this.detailInfo.transferStatus) > -1 &&
                queryEditStatus.indexOf(this.$route.query.status) > -1
            },
            //寻源方式
            sourcingMode: {
              type: 'select',
              defaultValue: () => this.detailInfo.sourcingMode,
              readonly: true,
              dataSource: [
                { text: this.$t('询报价'), value: 'rfq' },
                // { text: this.$t("直接定价"), value: "direct_pricing" },
                { text: this.$t('招投标'), value: 'invite_bids' },
                { text: this.$t('竞价'), value: 'bidding_price' }
              ]
            },
            //寻源对象
            sourcingObj: {
              type: 'text',
              defaultValue: () => this.detailInfo.sourcingObj,
              readonly: true
            },
            //询价类型
            sourcingType: {
              type: 'select',
              readonly: this.detailInfo.approveStatus !== 0,
              dataSource: [
                { text: this.$t('新品'), value: 'new_products' },
                { text: this.$t('二次'), value: 'second_inquiry' },
                { text: this.$t('已有'), value: 'exist' },
                { text: this.$t('不限'), value: 'unlimited' }
              ],
              defaultValue: () => this.detailInfo.sourcingType
            },
            //价格分类
            priceClassification: {
              type: 'select',
              readonly: this.detailInfo.approveStatus !== 0,
              dataSource: [
                { text: this.$t('暂估价格'), value: 'predict_price' },
                { text: this.$t('SRM价格'), value: 'srm_price' },
                { text: this.$t('执行价格'), value: 'execute_price' },
                { text: this.$t('基价'), value: 'basic_price' }
              ],
              defaultValue: () => this.detailInfo.priceClassification
            },
            //公司名称
            companyName:
              this.detailInfo.approveStatus !== 0 &&
              this.detailInfo.transferStatus === 100 &&
              this.detailInfo.transferStatus === 20
                ? {
                    defaultValue: () => this.detailInfo.companyName,
                    type: 'text',
                    readonly: this.detailInfo.approveStatus !== 0
                  }
                : {
                    type: 'select',
                    readonly: this.detailInfo.approveStatus !== 0,
                    handler: (ej2EventObject) => {
                      if (ej2EventObject.itemData != null) {
                        console.log('this.formDatathis.formData123', this.formData)
                        Object.assign(this.formData, ej2EventObject.itemData.data)
                      }
                    },
                    defaultValue: () => this.detailInfo.companyName,
                    api: this.$API.masterData.permissionCompanyList().then((res) => {
                      return res?.data.map((item) => ({
                        text: item.orgCode + '-' + item.orgName,
                        value: item.orgName,
                        data: {
                          companyCode: item.orgCode,
                          companyName: item.orgName,
                          companyId: item.id
                        }
                      }))
                    })
                  },
            //扩展
            sourcingExpand:
              this.detailInfo.transferStatus == 100 || this.detailInfo.transferStatus == 20
                ? {
                    defaultValue: () => {
                      let rfxHeaderExpandResponseList = []
                      if (this.detailInfo.rfxHeaderExpandResponseList) {
                        this.detailInfo.rfxHeaderExpandResponseList.forEach((v) => {
                          rfxHeaderExpandResponseList.push(
                            v.companyCode +
                              '-' +
                              v.companyName +
                              '+' +
                              v.purOrgCode +
                              '-' +
                              v.purOrgName +
                              '+' +
                              v.siteCode +
                              '-' +
                              v.siteName
                          )
                        })
                      }
                      return rfxHeaderExpandResponseList.toString()
                    },
                    type: 'span',
                    readonly: true
                  }
                : {
                    type: 'multiSelect',
                    readonly: false,
                    handler: (ej2EventObject) => {
                      if (ej2EventObject) {
                        this.formData.rfxHeaderExpandResponseList = ej2EventObject
                      }
                    },
                    defaultValue: () => this.detailInfo.sourcingExpand,
                    api: this.$API.rfxDetail
                      .purAllOrgWithSite({
                        fuzzyParam: this.detailInfo.siteCode,
                        purOrgCode: this.detailInfo.purOrgCode
                      })
                      .then((res) => {
                        let dataSource = []
                        res.data.forEach((v) => {
                          if (v.siteOrgs) {
                            v.siteOrgs.forEach((x) => {
                              dataSource.push({
                                text:
                                  v.companyCode +
                                  '-' +
                                  v.businessOrganizationCode +
                                  '-' +
                                  v.businessOrganizationName +
                                  '+' +
                                  x.orgCode +
                                  '-' +
                                  x.orgName,
                                value:
                                  v.companyCode +
                                  '+' +
                                  v.businessOrganizationCode +
                                  '+' +
                                  x.orgCode,
                                data: {
                                  companyCode: v.companyCode,
                                  companyId: v.companyId,
                                  companyName: v.companyName,
                                  purOrgCode: v.businessOrganizationCode,
                                  purOrgId: v.id,
                                  purOrgName: v.businessOrganizationName,
                                  siteCode: x.orgCode,
                                  siteId: x.id,
                                  siteName: x.orgName
                                }
                              })
                            })
                          }
                        })
                        let text = this.detailInfo.purOrgName + '+' + this.detailInfo.siteName
                        this.sourcingExpandList = dataSource
                        dataSource = dataSource.filter((x) => x.text !== text)
                        return dataSource
                      })
                  },
            // 币种 currencyName currencyName--非草稿状态的单据，头部的币种是只读
            currencyName: {
              type: 'select',
              readonly: this.detailInfo.approveStatus !== 0,
              handler: (ej2EventObject) => {
                if (ej2EventObject.itemData != null) {
                  Object.assign(this.formData, ej2EventObject.itemData.data)
                }
              },
              defaultValue: () => this.detailInfo.currencyCode,
              api: this.$API.masterData.queryAllCurrency().then((res) => {
                return res.data.map((item) => ({
                  text: item.currencyName,
                  value: item.currencyCode,
                  data: {
                    currencyName: item.currencyName,
                    currencyCode: item.currencyCode
                  }
                }))
              })
            },
            //采购组织  purOrgName  purOrgCode  purOrgId
            purOrgName:
              this.detailInfo.approveStatus !== 0 &&
              this.detailInfo.transferStatus === 100 &&
              this.detailInfo.transferStatus === 20
                ? {
                    defaultValue: () => this.detailInfo.purOrgName,
                    type: 'text',
                    readonly: this.detailInfo.approveStatus !== 0
                  }
                : {
                    type: 'select',
                    readonly: this.detailInfo.approveStatus !== 0,
                    handler: (ej2EventObject) => {
                      if (ej2EventObject.itemData == null) {
                        Object.assign(this.formData, {
                          purOrgCode: null,
                          purOrgId: null,
                          purOrgName: null
                        })
                      } else {
                        Object.assign(this.formData, ej2EventObject.itemData.data)
                      }
                    },
                    defaultValue: () => this.detailInfo.purOrgId,
                    api: this.$API.masterData
                      .permissionOrgList({
                        orgId: this.detailInfo.companyId
                      })
                      .then((res) => {
                        return res.data.map((item) => ({
                          text: item.organizationCode + '-' + item.organizationName,
                          value: item.id,
                          data: {
                            purOrgName: item.organizationName,
                            purOrgCode: item.organizationCode,
                            purOrgId: item.id
                          }
                        }))
                      })
                  },
            //业务类型
            businessTypeName: {
              type: 'select',
              handler: (ej2EventObject) => {
                if (ej2EventObject.itemData != null) {
                  Object.assign(this.formData, ej2EventObject.itemData.data)
                }
              },
              defaultValue: () => this.detailInfo.businessTypeCode,
              api: this.$API.masterData
                .dictionaryGetBusinessType()
                // .dictionaryGetList({
                //   dictCode: "businessType",
                // })
                .then((res) => {
                  return res.data.map((item) => ({
                    text: item.itemName,
                    value: item.itemCode,
                    data: {
                      businessTypeName: item.itemName,
                      businessTypeCode: item.itemCode,
                      businessTypeId: item.id
                    }
                  }))
                }),
              readonly: true
            },
            //采购员  purExecutorName  purExecutorId
            purExecutorName: {
              type: 'text',
              defaultValue: () => this.detailInfo.purExecutorName,
              readonly: true
              // type: "debounce-select",
              // readonly: true,
              // // readonly: this.detailInfo.approveStatus !== 0,
              // defaultValue: () => this.detailInfo.purExecutorId,
              // handler: (ej2EventObject) => {
              //   if (ej2EventObject?.itemData) {
              //     Object.assign(this.formData, {
              //       purExecutorName: ej2EventObject.itemData?.employeeName,
              //       purExecutorId: ej2EventObject.itemData?.uid,
              //     });
              //   }
              // },
              // type: "select",
              // readonly: this.detailInfo.approveStatus !== 0,
              // handler: (ej2EventObject) => {
              //   Object.assign(this.formData, ej2EventObject.itemData.data);
              // },
              // defaultValue: () => this.detailInfo.purExecutorId,
              // api: this.$API.masterData
              //   .getUserPageList(DEFAULTPARAM)
              //   .then((res) => {
              //     return res?.data?.records.map((item) => ({
              //       text: item.employeeName,
              //       value: item.id,
              //       data: {
              //         purExecutorName: item.employeeName,
              //         purExecutorId: item.id,
              //       },
              //     }));
              //   }),
            },
            // supplierRange: { //后端定义的  supplierRange：拓展(定制)  暂不渲染
            //   type: "select",
            //   dataSource: [
            //     { text: "品类合格", value: "category_qualified" },
            //     { text: "品类合格+有价格记录", value: "price_record" },
            //     { text: "品类合格+无价格记录", value: "non_price_Record" },
            //     { text: "所有供应商", value: "all_supplier" },
            //   ],
            // },
            //备注
            remark: {
              col: 1,
              readonly:
                remarkEditStatus.indexOf(this.detailInfo.transferStatus) > -1 &&
                queryEditStatus.indexOf(this.$route.query.status) > -1,
              defaultValue: () => this.detailInfo.remark,
              type: 'text',
              maxlength: '500',
              multiline: true
            },
            //需求部门  deptName  deptCode deptId
            deptName: {
              type: 'select',
              readonly: this.detailInfo.approveStatus !== 0,
              allowFiltering: true,
              filtering: createFiltering('text'),
              handler: (ej2EventObject) => {
                if (ej2EventObject.itemData != null) {
                  Object.assign(this.formData, ej2EventObject.itemData.data)
                }
              },
              defaultValue: () => this.detailInfo.deptName,
              api: this.$API.masterData
                .getDepartmentList({
                  departmentName: ''
                })
                .then((res) => {
                  return res.data.map((item) => ({
                    text: item.departmentName,
                    value: item.departmentName,
                    data: {
                      deptCode: item.departmentCode,
                      deptName: item.departmentName
                    }
                  }))
                })
            },
            // 汇率名称 exchangeRateName
            exchangeRateName: {
              type: 'text',
              readonly: true,
              defaultValue: () => this.detailInfo.exchangeRateName
            },
            // 汇率 exchangeRateValue
            exchangeRateValue: {
              type: 'number',
              readonly: true,
              defaultValue: () => this.detailInfo.exchangeRateValue
            },
            //工厂 siteName siteId  siteCode
            siteName:
              this.detailInfo.approveStatus !== 0 &&
              this.detailInfo.transferStatus === 100 &&
              this.detailInfo.transferStatus === 20
                ? {
                    defaultValue: () => this.detailInfo.siteName,
                    type: 'text',
                    readonly: this.detailInfo.approveStatus !== 0
                  }
                : {
                    type: 'select',
                    handler: (ej2EventObject) => {
                      if (ej2EventObject.itemData == null) {
                        Object.assign(this.formData, {
                          siteCode: null,
                          siteId: null,
                          siteName: null
                        })
                      } else {
                        Object.assign(this.formData, ej2EventObject.itemData.data)
                      }
                    },
                    readonly: this.detailInfo.approveStatus !== 0,
                    // todo 这里产品没有说明，后端不知道从哪里取值，
                    // dataSource: [],
                    defaultValue: () => this.detailInfo.siteName,
                    api: this.$API.masterData
                      .permissionSiteList({
                        buOrgId: this.detailInfo.purOrgId,
                        companyId: sessionStorage.getItem('selectCompanyId'),
                        orgLevelTypeCode: 'ORG06'
                      })
                      .then((res) => {
                        if (res.data == null) {
                          return []
                        }
                        let siteNameArr = []
                        for (let item of res.data) {
                          siteNameArr.push({
                            text: item.orgCode + '-' + item.orgName,
                            value: item.orgName,
                            data: {
                              siteName: item.orgName,
                              siteId: item.id,
                              siteCode: item.orgCode
                            }
                          })
                        }
                        return siteNameArr
                      })
                  },
            // 基础/派生
            basicDerivation: {
              type: 'select',
              readonly: this.detailInfo.approveStatus !== 0,
              dataSource: [
                { text: this.$t('基础'), value: 0 },
                { text: this.$t('派生'), value: 1 }
              ],
              defaultValue: () => this.detailInfo.basicDerivation
            },
            // PCB版本号
            pcbCode:
              this.detailInfo.approveStatus !== 0 &&
              this.detailInfo.transferStatus === 100 &&
              this.detailInfo.transferStatus === 20
                ? {
                    defaultValue: () => this.detailInfo.pcbCode,
                    type: 'text',
                    readonly: this.detailInfo.approveStatus !== 0
                  }
                : {
                    type: 'selectedItemCode',
                    readonly: this.detailInfo.approveStatus !== 0,
                    handler: (ej2EventObject) => {
                      if (ej2EventObject.itemData != null) {
                        Object.assign(this.formData, ej2EventObject.itemData.data)
                      }
                    },
                    defaultValue: () => this.detailInfo.pcbCode
                  },

            // 获取组件号
            componentsCode: {
              type: 'getComponentCode',
              handler: (ej2EventObject) => {
                if (ej2EventObject.itemData == null) {
                  return
                }
                Object.assign(this.formData, ej2EventObject.itemData.data)
              }
            },
            // 预算金额
            budgetAmt: {
              type: 'number',
              readonly: this.detailInfo.approveStatus !== 0,
              defaultValue: () => this.detailInfo.budgetAmt
            }
          }
          res.data.fieldDefines.forEach((e) => {
            // 强制必填字段
            if (['companyName', 'purOrgName', 'purExecutorId'].includes(e.fieldCode)) {
              e.required = 1
            }
          })
          this.fieldDefines = res.data.fieldDefines
          this.fieldDefines.sort((a, b) => b.sortValue - a.sortValue)
          console.error(this.fieldDefines, 'this.fieldDefines123')
        })
    },
    //保存
    async editProject() {
      this.$store.commit('startLoading')
      let formData = await this.$refs.form
        .parentGetFormData()
        .catch(() => this.$store.commit('endLoading'))
      if (!formData) {
        this.$store.commit('endLoading')
        return
      }
      if (
        this.detailInfo.approveStatus !== 0 &&
        this.detailInfo.transferStatus != 34 &&
        this.detailInfo.transferStatus != 20 &&
        this.detailInfo.transferStatus != 100
      ) {
        this.formData.companyId = this.detailInfo.companyId
        this.formData.companyCode = this.detailInfo.companyCode
        this.formData.purOrgId = this.detailInfo.purOrgId
        this.formData.purOrgCode = this.detailInfo.purOrgCode
      }
      let res = await this.$API.rfxList
        .addRfxHeader(
          Object.assign({ id: this.detailInfo.id, strategyRequest: {} }, formData, this.formData)
        )
        .catch(() => {
          this.$store.commit('endLoading')
        })
      if (res.code === 200) this.$emit('editProject', res)
      return res
    },
    // 判断内容是否改变,存在差异返回true,不存在差异:false
    isContentChange() {
      let needToDiffFields = Object.keys(this.formFieldConfig)
      let oldData = this.needToDiff(this.$refs.form.oldForm, needToDiffFields)
      let newData = this.needToDiff(this.$refs.form.form, needToDiffFields)
      return !isEqual(oldData, newData)
    },
    // 取出需要对比的数据
    needToDiff(data, fields) {
      let _obj = {}
      for (let i in data) {
        if (fields.includes(i)) {
          _obj[i] = data[i]
        }
      }
      return _obj
    }
  }
}
</script>

<style lang="scss" scoped>
.btn {
  margin-bottom: 10px;
}
.toolbar {
  padding: 20px;
  display: flex;
  .svg-option-item {
    &.disabled {
      i,
      span {
        color: var(--plugin-tb-tool-item-disable-color);
      }

      &:hover {
        cursor: not-allowed !important;
      }
    }

    &:hover {
      color: #707b8b;
      color: var(--plugin-tb-tool-item-hover-color);
    }
  }
}

.top-form-generator {
  // background: rgba(245, 248, 251, 1);
  // padding-bottom: 10px;
  background: #fff;
  border: 1px solid #e8e8e8;
  padding: 20px;
  /deep/ .mt-button {
    margin-right: 0;
    height: 30px;
    button {
      background: transparent;
      border-radius: 4px;
      box-shadow: unset;
      padding: 6px 12px 4px;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(0, 70, 156, 1);
    }
  }
}
</style>
