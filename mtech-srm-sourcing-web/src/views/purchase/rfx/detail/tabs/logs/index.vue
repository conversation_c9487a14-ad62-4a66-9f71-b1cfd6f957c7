<template>
  <mt-template-page :template-config="pageConfig" />
</template>

<script>
import { logsColumnData } from './config'
export default {
  data() {
    return {
      pageConfig: [
        {
          toolbar: [],
          gridId:
            this.$permission.gridId['purchase'][this.$route?.query?.source]['detail']['tabs'][
              'logs'
            ],
          grid: {
            allowFiltering: true,
            useToolTemplate: false,
            columnData: logsColumnData,
            asyncConfig: {
              url: this.$API.rfxExt.getLogList,
              params: { dataId: this.$route.query.rfxId, dataType: 'rfx' },
              queryBuilderWrap: 'queryBuilderDTO'
            }
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped></style>
