const SYMBOL_PK = '____symbol_pk' // Symbol();

function makePk(dataSource) {
  return dataSource.map((e) => {
    if (!e[SYMBOL_PK]) {
      e[SYMBOL_PK] = Symbol()
    }
    return e
  })
}

function filterByPk(oldArr, delArr) {
  const delPk = delArr.map((e) => e[SYMBOL_PK]).filter((e) => e)
  return oldArr.filter((e) => !delPk.includes(e[SYMBOL_PK]))
}

export default {
  props: {
    detailInfo: {
      type: Object,
      default: () => ({})
    },
    examineInfo: {
      type: Object,
      default: () => ({})
    },
    examineStatus: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    toolbarVisible() {
      return this.detailInfo.status === 0 && this.examineStatus
    },
    examineId() {
      return this.examineInfo.id
    }
  },
  watch: {
    examineId: {
      immediate: true,
      handler() {
        this.initData()
      }
    }
  },
  data() {
    return {
      pageConfig: [
        {
          useToolTemplate: false,
          useBaseConfig: false,
          title: this.$t('审查项目'),
          toolbar: [
            [
              {
                id: 'methods@showExamineDialog',
                icon: 'icon_solid_Createorder',
                title: this.$t('新增'),
                visibleCondition: () => this.toolbarVisible
              },
              {
                id: 'methods@delSelectedRecords',
                icon: 'icon_solid_Delete',
                title: this.$t('删除'),
                visibleCondition: () => this.toolbarVisible
              }
            ]
          ],
          grid: {
            gridId: this.$permission.gridId.purchase.examine.tab.edit.examineList,
            dataSource: [],
            columnData: [
              {
                width: '60',
                type: 'checkbox'
              },
              {
                field: 'examineItemCode',
                headerText: this.$t('资质审查项目编码')
              },
              {
                field: 'examineItemName',
                headerText: this.$t('资质审查项目名称')
              },
              {
                field: 'examineItemSpec',
                headerText: this.$t('资质审查项目说明')
              },
              {
                field: 'needFile',
                headerText: this.$t('是否需要上传附件'),
                valueConverter: {
                  type: 'map',
                  map: { 0: this.$t('否'), 1: this.$t('是') }
                }
              }
            ]
          }
        }
      ]
    }
  },
  methods: {
    async initData() {
      if (!this.examineId) {
        return
      }
      const res = await this.$API.examine.postViewItemListV1({
        queryBuilderDTO: {
          page: { current: 1, size: 50 }
        },
        rfxId: this.$route.query.rfxId,
        examineId: this.examineId
      })
      if (res?.code === 200) {
        this.pageConfig[0].grid.dataSource = makePk(res.data.records)
      }
    },
    handleClickToolBar(arg) {
      if (arg.toolbar.id.indexOf('methods@') === 0) {
        this[arg.toolbar.id.replace(/^methods@/, '')](arg)
      }
    },
    showExamineDialog() {
      const modal = () => import('./components/examineDialog/index.vue')
      this.$dialog({
        modal,
        data: {
          title: this.$t('资质审查项目'),
          data: {}
        },
        success: (records) => {
          const hasExamineItemCodes = this.pageConfig[0].grid.dataSource.map(
            (e) => e.examineItemCode
          )
          const addRecords = makePk(
            records
              .filter((e) => !hasExamineItemCodes.includes(e.examineItemCode))
              .map((e) => {
                return {
                  ...e,
                  id: undefined
                }
              })
          )
          this.pageConfig[0].grid.dataSource.push(...addRecords)
        }
      })
    },
    delSelectedRecords({ grid }) {
      const selectedRecords = grid.getSelectedRecords()
      console.log('delSelectedRecords', selectedRecords)
      if (selectedRecords.length <= 0) {
        this.$toast({
          content: this.$t('请先选择一行'),
          type: 'warning'
        })
        return
      }
      this.pageConfig[0].grid.dataSource = filterByPk(
        this.pageConfig[0].grid.dataSource,
        selectedRecords
      )
    },
    getRfxExamineItemSaveRequestList() {
      return this.pageConfig[0].grid.dataSource
    }
  }
}
