<template>
  <mt-dialog ref="dialog" css-class="create-proj-dialog" :buttons="buttons" @beforeClose="cancel">
    <p>{{ $t('确认时候审查通过?') }}</p>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('拒绝') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.formObject = { ...this.modalData.data }
    }
  },
  methods: {
    confirm(idList) {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          const query = { rfxId: this.$route.query.rfxId }
          const rfxId = query.rfxId
          let params = {
            rfxId,
            idList
          }
          this.$API.rfxSupRel.getPackageById(params).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/ .div-auth {
    background: #fafafa;
    height: 35px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    color: #0f0f0f;
    font-size: 12px;
    cursor: pointer;
    border-radius: 5px;
  }
}
</style>
