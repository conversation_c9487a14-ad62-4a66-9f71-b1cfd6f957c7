<template>
  <div>
    <div class="flex top-row" ref="topRow">
      <div class="flex mr-20">
        <div class="mr-5">{{ $t('审查方法') }}:</div>
        <b>{{ EXAMINE_METHOD_MAP[examineInfo.examineMethod] }}</b>
      </div>
      <div class="flex mr-20">
        <div class="mr-5">{{ $t('合格上限') }}:</div>
        <b>{{ examineInfo.qualifiedLimit || '-' }}</b>
      </div>
      <div class="r flex">
        <div class="flex mr-20">
          <div class="mr-10">{{ $t('当前结果') }}：</div>
          <div class="mr-20" v-for="item in [2, 0, 1]" :key="item">
            <ExamineStatusNum :total="total" :val="item" />
          </div>
        </div>
        <div class="op-item mt-flex mr-10" @click="setExamineStatusAll(2)">
          <i class="mt-icons mt-icon-icon_table_new"></i>
          {{ $t('批量通过') }}
        </div>
        <div class="op-item mt-flex" @click="setExamineStatusAll(0)">
          <i class="mt-icons mt-icon-icon_table_new"></i>
          {{ $t('批量不通过') }}
        </div>
      </div>
    </div>
    <TopSticky class="sticky-top" ref="stickyTop">
      <template v-slot:left>
        <TimerClock />
      </template>
      <template v-slot:content>
        <div class="flex content">
          <div class="flex mr-20">
            <div class="mr-10">{{ $t('当前结果') }}：</div>
            <div class="mr-20" v-for="item in [2, 0, 1]" :key="item">
              <ExamineStatusNum :total="total" :val="item" />
            </div>
          </div>
          <div class="op-item mt-flex mr-10" @click="save(true)">
            <i class="mt-icons mt-icon-icon_solid_Save"></i>
            {{ $t('提交') }}
          </div>
          <div class="op-item mt-flex mr-10" @click="setExamineStatusAll(2)">
            <i class="mt-icons mt-icon-icon_table_new"></i>
            {{ $t('批量通过') }}
          </div>
          <div class="op-item mt-flex" @click="setExamineStatusAll(0)">
            <i class="mt-icons mt-icon-icon_table_new"></i>
            {{ $t('批量不通过') }}
          </div>
        </div>
      </template>
    </TopSticky>
    <div class="flex top-row">
      {{ $t('供应商') }}:
      <mt-multi-select
        width="300"
        :fields="{ text: 'supplierName', value: 'supplierId' }"
        :data-source="supplierList"
        v-model="filterSupplierIds"
        :show-clear-button="true"
      />
    </div>
    <div
      v-for="rfxExamineSupplierRel in filterrfxExamineSupplierRelV2ResponseList"
      :key="rfxExamineSupplierRel.id"
      :class="{
        list: true,
        expand: rfxExamineSupplierRel.$expand
      }"
    >
      <div class="header flex">
        <div
          :class="{
            arrow: true
          }"
          @click="rfxExamineSupplierRel.$expand = !rfxExamineSupplierRel.$expand"
        ></div>
        <Checkbox v-model="rfxExamineSupplierRel.$checked" />
        <div>{{ rfxExamineSupplierRel.supplierName }}</div>
        <span class="tag-status fail" v-if="rfxExamineSupplierRel.examineStatus === 0">
          {{ $t('未通过') }}
        </span>
        <span class="tag-status" v-else-if="rfxExamineSupplierRel.examineStatus === 1">
          {{ $t('待审核') }}
        </span>
        <span class="tag-status success" v-else>
          {{ $t('通过') }}
        </span>
      </div>
      <div class="content">
        <mt-data-grid v-bind="rfxExamineSupplierRel.$grid" />
      </div>
      <div class="footer">
        {{ $t('整单审核') }}:
        <mt-select
          :width="150"
          v-model="rfxExamineSupplierRel.examineStatus"
          :data-source="[
            { value: 0, text: $t('未通过') },
            { value: 1, text: $t('待审核') },
            { value: 2, text: $t('通过') }
          ]"
          :fields="{ value: 'value', text: 'text' }"
          :allow-filtering="true"
        ></mt-select>
      </div>
    </div>
  </div>
</template>

<script src="./script.js" />

<style lang="scss" scoped>
.flex {
  display: flex;
}
.mr-10 {
  margin-right: 10px;
}
.mr-20 {
  margin-right: 20px;
}
.ml-5 {
  margin-left: 5px;
}
.mr-5 {
  margin-right: 5px;
}
.mt-10 {
  margin-top: 10px;
}
.inline-block {
  display: inline-block;
}
.top-row {
  padding: 20px;
  line-height: 24px;
  height: 24px;
  vertical-align: baseline;

  .r {
    text-align: right;
    justify-content: end;
    flex: 1;
  }
}

/deep/ .op-item {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 24px;
  line-height: 24px;
  cursor: pointer;
  transition: all ease 0.3s;

  &:hover {
    opacity: 0.8;
  }

  i {
    margin-right: 5px;
  }
}

.list {
  margin-top: 20px;
  &.expand {
    .arrow {
      transform: rotate(-90deg);
    }

    .content,
    .footer {
      display: none;
    }
  }

  .header {
    padding-left: 10px;
    height: 40px;
    line-height: 40px;
    background: #f1f5f9;
    align-items: center;

    .mt-checkbox {
      margin-right: 5px;
    }

    .arrow {
      width: 0;
      height: 0;
      border: 5px solid;
      border-color: transparent;
      border-top-color: #00469c;
      cursor: pointer;
      margin-top: 5px;
      margin-right: 5px;
      transition: ease 0.3s;
    }

    .tag-status {
      height: 24px;
      line-height: 24px;
      font-size: 12px;
      font-weight: 500;
      color: #9a9a9a;
      background-color: #e7ebee;
      margin-left: 10px;
      padding: 0 10px;
      &.success {
        color: #8acc40;
        background-color: #e6f0e5;
      }
      &.fail {
        color: #ed5633;
        background-color: #f0e5e4;
      }
    }
  }

  .content {
    transition: ease 0.3s;
  }

  .footer {
    text-align: right;
    margin-top: 5px;
  }
}

.sticky-top {
  top: -100px;
  right: 0;
  position: fixed;
  z-index: -1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) 0s;
  opacity: 0;

  .content {
    line-height: 24px;
    vertical-align: baseline;
  }

  &.show {
    top: 0;
    z-index: 9;
    opacity: 1;
  }
}
</style>
