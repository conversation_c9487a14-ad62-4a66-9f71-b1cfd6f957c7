import { isEqual } from 'lodash'

export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.submit,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      form: {
        data: {
          examineItemCode: '',
          examineItemName: ''
        },
        rules: {},
        dataSource: {}
      },
      pageConfig: [
        {
          useToolTemplate: false,
          useBaseConfig: false,
          grid: {
            gridId: this.$permission.gridId.purchase.examine.tab.edit.examineDialog,
            asyncConfig: {
              url: this.$API.examineBaseItem.queryBuilder,
              rules: []
            },
            columnData: [
              {
                width: '60',
                type: 'checkbox'
              },
              {
                field: 'examineItemCode',
                headerText: this.$t('资质审查项目编码')
              },
              {
                field: 'examineItemName',
                headerText: this.$t('资质审查项目名称')
              },
              {
                field: 'examineItemSpec',
                headerText: this.$t('资质审查项目说明')
              },
              {
                field: 'needFile',
                headerText: this.$t('是否需要上传附件'),
                valueConverter: {
                  type: 'map',
                  map: { 0: this.$t('否'), 1: this.$t('是') }
                }
              }
            ]
          }
        }
      ]
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    query() {
      const rules = ['examineItemCode', 'examineItemName']
        .filter((field) => this.form.data[field])
        .map((field) => ({
          condition: 'and',
          field,
          operator: 'contains',
          type: 'string',
          value: this.form.data[field]
        }))
      if (isEqual(this.pageConfig[0].grid.asyncConfig.rules, rules)) {
        this.$refs.templateRef.refreshCurrentGridData()
      } else {
        this.pageConfig[0].grid.asyncConfig.rules = rules
      }
    },
    cancel() {
      this.emitConfirm()
    },
    emitConfirm(...arg) {
      this.$emit('confirm-function', ...arg)
    },
    submit() {
      const grid = this.$refs.templateRef.getCurrentTabRef().grid
      const records = (grid.getMtechGridRecords || grid.getSelectedRecords)()
      if (records.length === 0) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        return
      }
      this.emitConfirm(records)
    }
  }
}
