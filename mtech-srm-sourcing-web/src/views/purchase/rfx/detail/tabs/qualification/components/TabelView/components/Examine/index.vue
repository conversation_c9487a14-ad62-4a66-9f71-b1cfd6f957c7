<template>
  <mt-template-page
    ref="templateRef"
    :template-config="pageConfig"
    @handleClickToolBar="handleClickToolBar"
  />
</template>

<script>
import cellFileView from 'COMPONENTS/NormalEdit/cellFileView' // 单元格附件查看

function serializeListDrawing(list) {
  list.forEach((e) => {
    e.examineStatus = e.examineStatus ?? 0
    e.moneyStatus = e.moneyStatus ?? 1
    e.drawing = e.sourcingFileResponseList ? JSON.stringify(e.sourcingFileResponseList) : null
  })
  return list
}

export default {
  props: {
    examineId: {
      type: [Number, String],
      default: null
    }
  },
  data() {
    return {
      pageConfig: [
        {
          useToolTemplate: false,
          useBaseConfig: false,
          toolbar: [
            [
              {
                id: 'methods@earnestPass',
                icon: 'icon_solid_edit',
                title: this.$t('保证金审核')
              },
              {
                id: 'methods@earnestRefund',
                icon: 'icon_solid_Submit',
                title: this.$t('保证金退款')
              },
              {
                id: 'methods@paymentStatus',
                icon: 'icon_solid_Submit',
                title: this.$t('退款状态查询')
              }
            ]
          ],
          grid: {
            gridId: this.$permission.gridId.purchase.examine.tab.view.deposit,
            asyncConfig: {
              url: this.$API.examine.viewMoneyStatusListV1,
              queryBuilderWrap: 'queryBuilderDTO',
              params: {
                rfxId: this.$route.query.rfxId,
                examineId: this.examineId
              },
              serializeList: serializeListDrawing
            },
            columnData: [
              {
                width: '60',
                type: 'checkbox'
              },
              {
                field: 'supplierCode',
                headerText: this.$t('供应商编码')
              },
              {
                field: 'supplierName',
                headerText: this.$t('供应商名称')
              },
              {
                field: 'moneyStatus',
                headerText: this.$t('保证金缴纳'),
                valueConverter: {
                  type: 'map',
                  map: {
                    0: this.$t('已提交凭证'),
                    1: this.$t('未缴纳'),
                    2: this.$t('已缴纳'),
                    3: this.$t('退款中'),
                    4: this.$t('已全部退款'),
                    5: this.$t('无需保证金'),
                    6: this.$t('部分退款')
                  }
                }
              },
              {
                field: 'drawing',
                headerText: this.$t('保证金附件'),
                template: function () {
                  return {
                    template: cellFileView
                  }
                },
                editTemplate: function () {
                  return {
                    template: cellFileView
                  }
                }
              }
            ],
            dataSource: []
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(arg) {
      if (arg.toolbar.id.indexOf('methods@') === 0) {
        this[arg.toolbar.id.replace(/^methods@/, '')](arg)
      }
    },
    // 审核通过
    async earnestPass({ grid }) {
      const records = (grid.getMtechGridRecords || grid.getSelectedRecords)()
      if (records.length === 0) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        return
      }
      const res = await this.$API.examine.earnestPass({
        examineId: this.examineId,
        rfxId: this.$route.query.rfxId,
        supplierIdList: records.map((e) => e.supplierId).filter((e) => e)
      })
      if (res?.code === 200) {
        this.$toast({
          content: res.msg,
          type: 'success'
        })
      }
    },
    // 保证金退款
    async earnestRefund({ grid }) {
      const records = (grid.getMtechGridRecords || grid.getSelectedRecords)()
      if (records.length === 0) {
        this.$toast({ content: this.$t('请选择一条数据'), type: 'warning' })
        return
      }
      const res = await this.$API.examine.earnestRefund({
        examineId: this.examineId,
        rfxId: this.$route.query.rfxId,
        supplierIdList: records.map((e) => e.supplierId).filter((e) => e)
      })
      if (res?.code === 200) {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
      }
    },
    // 退款状态查询
    async paymentStatus() {
      const res = await this.$API.examine.paymentStatus({
        rfxId: this.$route.query.rfxId
      })
      if (res?.code === 200) {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
      }
    }
  }
}
</script>
