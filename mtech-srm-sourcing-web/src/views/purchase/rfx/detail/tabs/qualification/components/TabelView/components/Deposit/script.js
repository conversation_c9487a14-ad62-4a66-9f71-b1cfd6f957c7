import { EXAMINE_METHOD_MAP } from '@/constants/examine'
import { createEditInstance } from '@/utils/ej/dataGrid'
import { getValueByPath } from '@/utils/obj'
import cellFileView from 'COMPONENTS/NormalEdit/cellFileView' // 单元格附件查看
import Checkbox from '../Checkbox'
import ExamineStatusNum from './ExamineStatusNum'
import TopSticky from './TopSticky'
import TimerClock from './TimerClock'

export default {
  props: {
    examineId: {
      type: [Number, String],
      default: null
    },
    detailInfo: {
      type: Object,
      default: () => ({})
    },
    examineInfo: {
      type: Object,
      default: () => ({})
    }
  },
  mounted() {
    // 获取数据
    this.initData()
    const observer = this.initTopSticky()
    this.$once('hook:beforeDestroy', () => {
      observer.disconnect()
    })
  },

  components: {
    Checkbox,
    ExamineStatusNum,
    TopSticky,
    TimerClock
  },
  computed: {
    total() {
      return {
        examineStatus: this.rfxExamineSupplierRelV2ResponseList.map((e) => e.examineStatus),
        oldExamineStatus: this.rfxExamineSupplierRelV2ResponseList.map((e) => e.$oldExamineStatus)
      }
    },
    supplierList() {
      return this.rfxExamineSupplierRelV2ResponseList.map((e) => {
        return {
          supplierId: e.supplierId,
          supplierName: e.supplierName
        }
      })
    },
    filterrfxExamineSupplierRelV2ResponseList() {
      if (this.filterSupplierIds.length === 0) {
        return this.rfxExamineSupplierRelV2ResponseList
      }
      return this.rfxExamineSupplierRelV2ResponseList.filter((e) =>
        this.filterSupplierIds.includes(e.supplierId)
      )
    }
  },
  data() {
    return {
      EXAMINE_METHOD_MAP,
      expand: false,
      rfxExamineSupplierRelV2ResponseList: [],
      filterSupplierIds: []
    }
  },
  methods: {
    initTopSticky() {
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((item) => {
            if (item.isIntersecting) {
              this.$refs.stickyTop.$el.classList.remove('show')
            } else {
              this.$refs.stickyTop.$el.classList.add('show')
              this.$refs.stickyTop.$el.style.left = this.$refs.topRow.offsetLeft + 'px'
            }
          })
        },
        {
          threshold: 0.5,
          root: null
        }
      )
      observer.observe(this.$refs.topRow)
      return observer
    },
    async initData() {
      const res = await this.$API.examine
        .viewItemListByExamineId({
          queryBuilderDTO: {
            page: { current: 1, size: 1e3 }
          },
          rfxId: this.$route.query.rfxId,
          examineId: this.examineId
        })
        .catch(() => {})
      this.rfxExamineSupplierRelV2ResponseList = res.data.rfxExamineSupplierRelV2ResponseList.map(
        (e) => {
          const data = {
            ...e,
            $oldExamineStatus: e.examineStatus,
            $checked: false,
            $expand: false,
            $grid: {
              actionComplete: this.actionComplete,
              columnData: columnData(this),
              dataSource: serializeListDrawing(e.rfxExamineItemSupplierRelResponses),
              editSettings: {
                allowAdding: true,
                allowEditing: true,
                allowDeleting: true,
                mode: 'Normal',
                showConfirmDialog: false,
                showDeleteConfirmDialog: true,
                newRowPosition: 'Top'
              }
            }
          }
          data.$grid.actionComplete = this.actionComplete.bind(this, data)
          return data
        }
      )
    },
    setExamineStatusAll(examineStatus) {
      this.rfxExamineSupplierRelV2ResponseList.forEach((rfxExamineSupplierRel) => {
        if (rfxExamineSupplierRel.$checked) {
          rfxExamineSupplierRel.examineStatus = examineStatus
        }
      })
    },
    actionComplete(data, args) {
      if (args.requestType == 'save' && args.action == 'edit') {
        data.$grid.dataSource[args.rowIndex] = args.rowData
      }
    },
    async save(showToast = false) {
      const params = {
        rfxId: this.$route.query.rfxId,
        supplierItemDetailList: this.rfxExamineSupplierRelV2ResponseList
          .map((e) => e.$grid.dataSource)
          .flat()
          .map((e) => {
            return {
              id: e.id,
              supplierId: e.supplierId,
              examineItemId: e.examineItemId,
              examineItemCheck: e.examineItemCheck,
              supplierReply: e.supplierReply
            }
          }),
        supplierPassedList: this.rfxExamineSupplierRelV2ResponseList
          .filter((e) => e.examineStatus === 2)
          .map((e) => e.supplierId),
        supplierRejectedList: this.rfxExamineSupplierRelV2ResponseList
          .filter((e) => e.examineStatus === 0)
          .map((e) => e.supplierId)
      }
      const res = await this.$API.examine.bigSaveExamineForAllSupplier(params).catch(() => {})
      this.initData()
      if (res?.code === 200) {
        if (showToast) {
          this.$toast({
            content: '保存成功',
            type: 'success'
          })
        }
        return true
      }
      return false
    }
  }
}

function columnData(that) {
  const editInstance = createEditInstance()

  return [
    {
      field: 'examineItemCode',
      headerText: that.$t('资质审查项目编码'),
      allowEditing: false
    },
    {
      field: 'examineItemName',
      headerText: that.$t('资质审查项目名称'),
      allowEditing: false
    },
    {
      field: 'examineItemSpec',
      headerText: that.$t('资质审查项目说明'),
      allowEditing: false
    },
    {
      field: 'needFile',
      headerText: that.$t('是否需要附件'),
      allowEditing: false,
      formatter: function ({ field }, item) {
        const cellVal = getValueByPath(item, field)
        const dataSource = [
          { value: 0, text: that.$t('否') },
          { value: 1, text: that.$t('是') }
        ]
        return dataSource.find((e) => e.value === cellVal)?.text ?? cellVal
      }
    },
    {
      field: 'drawing',
      headerText: that.$t('附件'),
      allowEditing: false,
      template: function () {
        return {
          template: cellFileView
        }
      },
      editTemplate: function () {
        return {
          template: cellFileView
        }
      }
    },
    {
      field: 'purOpinion',
      headerText: that.$t('供应商回复'),
      allowEditing: false
    },
    {
      field: 'supplierReply',
      headerText: that.$t('采方审核意见'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text'
        })
      })
    },
    {
      field: 'examineItemCheck',
      headerText: that.$t('审核结果'),
      formatter: function ({ field }, item) {
        const cellVal = getValueByPath(item, field)
        const dataSource = [
          { value: 0, text: that.$t('不通过') },
          { value: 1, text: that.$t('通过') }
        ]
        return dataSource.find((e) => e.value === cellVal)?.text ?? cellVal
      },
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'select',
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { value: 0, text: that.$t('不通过') },
            { value: 1, text: that.$t('通过') }
          ]
        })
      })
    }
  ]
}

function serializeListDrawing(list) {
  list.forEach((e) => {
    e.drawing = e.sourcingFileResponseList ? JSON.stringify(e.sourcingFileResponseList) : null
  })
  return list
}
