<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="modalData.title"
    @beforeClose="cancel"
  >
    <div class="dialog-content full-height">
      <mt-form id="app-form" ref="appForm" :model="form.data" :rules="form.rules">
        <mt-form-item
          prop="examineItemCode"
          :label="$t('审查项编码')"
          label-style="left"
          label-width="100px"
          class="flex1"
        >
          <mt-input v-model="form.data.examineItemCode" class="full-width"></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="examineItemName"
          :label="$t('审查项名称')"
          label-style="left"
          label-width="100px"
          class="flex1"
        >
          <mt-input v-model="form.data.examineItemName" class="full-width"></mt-input>
        </mt-form-item>
        <mt-button type="primary" @click="query">{{ $t('查询') }}</mt-button>
      </mt-form>
      <mt-template-page ref="templateRef" :template-config="pageConfig" />
    </div>
  </mt-dialog>
</template>

<script src="./script.js" />

<style scoped lang="scss">
/deep/ .full-width {
  width: 100%;
}
/deep/ .mt-form-item.flex1 {
  flex: 1;
  padding-right: 5px;
}
</style>
