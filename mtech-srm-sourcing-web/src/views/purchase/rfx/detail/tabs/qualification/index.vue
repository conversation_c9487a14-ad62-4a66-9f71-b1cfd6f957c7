<template>
  <div class="qualification">
    <template>
      <div class="flex switch-examine">
        <div class="flex1">
          <span>{{ $t('启用资质审查') }} </span>
          <mt-switch
            :disabled="!!isView"
            :value="examineStatus"
            css-class="e-small"
            @change="changeExamineStatus"
          />
          <span class="only-earnest">{{ $t('仅启用保证金') }} </span>
          <mt-switch
            :disabled="!examineStatus || (examineStatus && !!isView)"
            v-model="onlyEnableEarnest"
            css-class="e-mini"
          />
        </div>
        <Expand v-model="formExpand" @click.native="formExpand = !formExpand" />
      </div>
      <div class="toolbar" v-if="!isView">
        <div
          :class="{
            'svg-option-item': true,
            disabled: disabledSave
          }"
          @click="saveExamine"
        >
          <mt-icon name="icon_solid_Save" />
          <span>{{ isView ? $t('提交审核结果') : $t('保存') }}</span>
        </div>
      </div>
      <mt-form
        class="flex-keep"
        ref="dialogRef"
        :model="formObject"
        :rules="formRules"
        v-if="formExpand"
      >
        <!-- 启用资质审查 -->
        <mt-form-item
          v-show="!onlyEnableEarnest"
          class="form-item"
          :label="$t('审查方法')"
          prop="examineMethod"
          label-width="100px"
        >
          <mt-select
            :disabled="!examineStatus || !!isView"
            v-model="formObject.examineMethod"
            :placeholder="$t('请选择审查方法')"
            float-label-type="Never"
            :data-source="upgradeRuleTypeList"
            @change="examineMethodChange"
            style="flex: 1"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          v-show="!onlyEnableEarnest"
          prop="qualifiedLimit"
          :label="$t('合格上限')"
          label-width="100px"
        >
          <mt-input-number
            v-model="formObject.qualifiedLimit"
            min="0"
            float-label-type="Never"
            placeholder=""
            :disabled="
              !examineStatus || isView || formObject.examineMethod == upgradeRuleTypeList[0].value
            "
            style="flex: 1"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item
          class="form-item"
          :label="$t('是否需要保证金')"
          label-width="100px"
          prop="needEarnestMoney"
        >
          <mt-select
            :disabled="onlyEnableEarnest || !examineStatus || !!isView"
            v-model="formObject.needEarnestMoney"
            :placeholder="$t('请选择是否需要保证金')"
            float-label-type="Never"
            :data-source="upgradeRuleTypeLists"
            @input="changeSelectRole"
            :show-clear-button="true"
            style="flex: 1"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="earnestMoney" :label="$t('保证金金额')" label-width="100px">
          <mt-input-number
            v-if="isRfxGeneralType"
            :disabled="!examineStatus || !!isView"
            v-model="formObject.earnestMoney"
            float-label-type="Never"
            placeholder=""
            style="flex: 1"
          ></mt-input-number>
          <Combobox
            v-else
            :disabled="!examineStatus || !!isView"
            type="number"
            v-model="formObject.earnestMoney"
            float-label-type="Never"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :data-source="earnestMoneyList"
            style="flex: 1"
          />
        </mt-form-item>
        <mt-form-item
          prop="remark"
          class="form-item"
          :label="$t('备注')"
          label-width="100px"
          style="flex: 1"
        >
          <mt-input
            :disabled="!examineStatus || !!isView"
            v-model="formObject.remark"
            :multiline="true"
            :rows="1"
            maxlength="200"
            float-label-type="Never"
            :placeholder="$t('字数不超过500字')"
            style="flex: 1"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </template>
    <div class="flex-fit" style="min-height: 600px" v-if="activatedRender">
      <!-- <TabelView
        v-if="isView && examineStatus"
        ref="tabelView"
        :detail-info="detailInfo"
        :examine-info="formObject"
      /> -->
      <TabelEdit
        v-if="examineStatus && !onlyEnableEarnest"
        ref="tabelEdit"
        :detail-info="detailInfo"
        :examine-info="formObject"
        :examine-status="examineStatus"
      />
    </div>
  </div>
</template>

<script>
import Combobox from '@/components/combobox'
import TabelEdit from './components/TabelEdit/index.vue'
import Expand from './components/Expand.vue'

export default {
  components: {
    TabelEdit,
    Expand,
    Combobox
  },
  data() {
    return {
      examineStatus: null,
      onlyEnableEarnest: null, // 仅使用保证金
      tabActive: 1,
      //对象内的值
      formObject: {
        examineMethod: '', //	审查方法（0合格制 1数量制）
        earnestMoney: 0, //需要缴纳的保证金金额
        examineCode: '', //审查规则代码
        id: '',
        needEarnestMoney: 0, //是否需要保证金（0不需要 1需要）
        qualifiedLimit: null, //合格上限
        remark: '',
        rfxCode: '', //采方询价单号
        rfxId: 0 //采方询价单ID
      },
      formRules: {
        qualifiedLimit: [
          {
            validator: this.qualifiedLimitValidator,
            trigger: 'blur'
          }
        ],
        earnestMoney: [
          {
            validator: this.earnestMoneyValidator,
            trigger: 'blur'
          }
        ]
      },
      // 审查方法
      upgradeRuleTypeList: [
        {
          text: this.$t('合格制'),
          value: 0
        },
        {
          text: this.$t('数量制'),
          value: 1
        }
        // {
        //   text: this.$t("审批晋级"),
        //   value: 3,
        // },
      ],
      upgradeRuleTypeLists: [
        {
          text: this.$t('否'),
          value: 0
        },
        {
          text: this.$t('是'),
          value: 1
        }
      ],
      activatedRender: true,
      formExpand: true,
      earnestMoneyList: []
    }
  },
  props: {
    detailInfo: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    onlyEnableEarnest(val) {
      if (val) {
        this.formObject.needEarnestMoney = 1
      }
    }
  },
  computed: {
    isView() {
      return this.detailInfo?.status !== 0
    },
    disabledSave() {
      return !(this.isView || this.examineStatus)
    },
    isRfxGeneralType() {
      return this.detailInfo.rfxGeneralType === 1
    }
  },
  mounted() {
    this.initPageData()
  },
  async activated() {
    // this.initPageData()
    // this.activatedRender = false
    // this.$nextTick(() => {
    //   this.activatedRender = true
    // })
  },
  methods: {
    async getEarnestMoneyList() {
      // 初始化 - 字典数据
      let codeList = [{ code: 'DEPOSIT_AMOUNT', type: 'number' }]
      await this.$API.masterData.getNumDictListAllByCode(codeList).then((res) => {
        if (res.code === 200) {
          let _res = res.data
          this.earnestMoneyList = _res['DEPOSIT_AMOUNT']
        }
      })
    },
    initPageData() {
      this.queryExamineStatus()
      this.getRfxSuperl()
      if (!this.isRfxGeneralType) {
        // 非采才请求
        this.getEarnestMoneyList()
      }
    },
    // 查询资质审查开启状态
    async queryExamineStatus() {
      const res = await this.$API.rfxSupRel
        .queryExamineStatus({
          rfxId: this.$route.query.rfxId
        })
        .catch(() => {})
      if (res) {
        this.examineStatus = !!(res.data === 1)
      }
    },
    // 切换资质审查开启状态
    async changeExamineStatus(e) {
      this.$store.commit('startLoading')
      await this.$API.rfxSupRel
        .openExamine({
          rfxId: this.$route.query.rfxId
        })
        .catch(() => {})
      await this.queryExamineStatus()
      this.$store.commit('endLoading')
      if (e == false) {
        this.formObject.examineMethod = ''
        this.formObject.earnestMoney = ''
        this.formObject.examineCode = ''
        this.formObject.needEarnestMoney = ''
        this.formObject.qualifiedLimit = ''
        this.formObject.remark = ''
      } else {
        this.getRfxSuperl()
      }
    },
    examineMethodChange(value) {
      if (value.itemData.value == 0) {
        this.formObject.qualifiedLimit = null
        this.$refs.dialogRef.clearValidate('qualifiedLimit')
      }
    },
    changeSelectRole(value) {
      this.formObject.needEarnestMoney = value
      if (value == 0) {
        this.formObject.earnestMoney = ''
      }
    },
    // 合格上限校验
    qualifiedLimitValidator(rule, value, callback) {
      if (!value && this.formObject.examineMethod == this.upgradeRuleTypeList[1].value) {
        // 合格上限校验，审查方式为数量制，则必输
        callback(new Error(this.$t('请输入合格上限')))
      } else {
        callback()
      }
    },
    // 保证金金额校验
    earnestMoneyValidator(rule, value, callback) {
      if (
        !value &&
        value !== 0 &&
        this.formObject.needEarnestMoney == this.upgradeRuleTypeLists[1].value
      ) {
        // 保证金金额，是否需要保证金为是，则必输
        callback(new Error(this.$t('请输入保证金金额')))
      } else if (
        value <= 0 &&
        this.formObject.needEarnestMoney == this.upgradeRuleTypeLists[1].value
      ) {
        callback(new Error(this.$t('保证金金额需大于0')))
      } else {
        callback()
      }
    },
    // 暂时不用
    getSupRelList(value) {
      this.$API.rfxSupRel
        .getRfxSupRelConfigs({
          examineId: value,
          queryBuilderDTO: { page: { current: 1, size: 1000 } }
        })
        .then(() => {
          // this.$set(this.pageConfig[0].grid, 'dataSource', res?.data?.records)
        })
    },
    getRfxSuperl() {
      let params = {
        id: this.$route.query.rfxId
      }
      this.$API.rfxSupRel.getRfxSupRelListAdd(params).then((res) => {
        this.onlyEnableEarnest = res.data.onlyEnableEarnest === 1 ? true : false
        this.formObject.examineMethod = res.data.examineMethod
        this.formObject.earnestMoney = res.data.earnestMoney
        this.formObject.examineCode = res.data.examineCode
        this.formObject.needEarnestMoney = this.onlyEnableEarnest ? 1 : res.data.needEarnestMoney
        this.formObject.qualifiedLimit = res.data.qualifiedLimit || null // 为 0 时不显示
        this.formObject.remark = res.data.remark
        if (res?.data?.id) {
          this.formObject.id = res.data.id
          // this.getSupRelList(this.formObject.id)
        }
      })
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    },
    saveExamine() {
      if (this.disabledSave) {
        return
      }
      if (this.$refs.tabelView) {
        this.$refs.tabelView.save()
        return
      }
      const params = {
        ...this.formObject,
        rfxCode: this.detailInfo.rfxCode,
        onlyEnableEarnest: this.onlyEnableEarnest ? 1 : 0,
        rfxId: this.$route.query.rfxId,
        // 采方保存资质审查规则明细DTO
        rfxExamineItemSaveRequestList: this.$refs.tabelEdit?.getRfxExamineItemSaveRequestList()
      }
      if (this.onlyEnableEarnest) {
        delete params.examineMethod
        delete params.qualifiedLimit
        delete params.rfxExamineItemSaveRequestList
      }
      this.$API.rfxSupRel.saveExamine(params).then((res) => {
        this.$toast({
          content: this.$t('保存成功'),
          type: 'success'
        })
        this.$emit('confirm-function', res.data)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.qualification {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  font-size: 16px;

  .flex-keep {
    flex: 0 0 auto;
  }

  .flex-fit {
    flex: 1 1 auto;
  }
  .dialog-Top {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .form-item {
      width: 35%;
    }
    .dialog-remark {
      // width: 20%;
      display: flex;
      justify-content: space-around;
      align-items: center;
    }
  }
}
.full-height {
  height: 100%;
}
.dialog-remark {
  width: 100%;
  .form-item {
    width: 100% !important;
    .e-input {
      width: 100%;
      // height: 20px;
      border: 1px solid #000;
    }
  }
}

.qualification {
  border: 1px solid #e8e8e8;
}

.mt-form {
  display: flex;
  flex-wrap: wrap;
  padding: 20px 20px 10px 10px;

  .mt-form-item,
  .form-item {
    width: 25%;
    padding: 0 10px;
    box-sizing: border-box;
  }
}

.toolbar {
  padding: 10px 20px;
  display: flex;
  .svg-option-item {
    color: var(--plugin-tb-tool-custom-btn-color);
    &.disabled {
      i,
      span {
        color: var(--plugin-tb-tool-item-disable-color);
      }

      &:hover {
        cursor: not-allowed !important;
      }
    }

    &:hover {
      color: #707b8b;
      color: var(--plugin-tb-tool-item-hover-color);
    }
  }
}

/deep/ .tab-container {
  background: #fafafa !important;
}

.switch-examine {
  padding: 10px 20px;
  vertical-align: middle;
  display: flex;
  justify-content: center;
  align-items: center;
  span {
    margin-right: 5px;
  }
}

.flex {
  display: flex;
}
.flex1 {
  flex: 1;
  display: flex;
  align-items: center;
  .only-earnest {
    margin-left: 20px;
  }
}
</style>
