<template>
  <div class="expand">
    <span> {{ value ? $t('收起') : $t('展开') }}</span>
    <div class="flex icons">
      <mt-icon :name="value ? 'icon_Sort_up' : 'icon_Sort_down'" />
      <mt-icon :name="value ? 'icon_Sort_up' : 'icon_Sort_down'" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: true
    }
  }
}
</script>

<style scoped lang="scss">
.flex {
  display: flex;
}
.flex1 {
  flex: 1;
}

.expand {
  display: flex;
  align-items: center;
  color: #6386c1;
  cursor: pointer;

  .icons {
    flex-direction: column;
    transform: scale(0.5);
    font-size: 12px;
  }
}
</style>
