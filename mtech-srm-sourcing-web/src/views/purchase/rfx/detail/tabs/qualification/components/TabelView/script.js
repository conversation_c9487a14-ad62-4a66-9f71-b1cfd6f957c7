import Deposit from './components/Deposit'
import Examine from './components/Examine'
import Tabs from './components/Tabs'

export default {
  props: {
    detailInfo: {
      type: Object,
      default: () => ({})
    },
    examineInfo: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    toolbarVisible() {
      return this.detailInfo.status === 0
    },
    examineId() {
      return this.examineInfo.id
    }
  },
  components: {
    Tabs,
    Deposit,
    Examine,
    UploaderDialog: () => import('@/components/NormalEdit/Upload/uploaderDialog.vue')
  },
  data() {
    return {
      tabVal: 0,
      tabSource: [
        { title: this.$t('审查项目'), value: 0 },
        { title: this.$t('保证金'), value: 1 }
      ]
    }
  },
  methods: {
    async save() {
      if (this.$refs.deposit) {
        return await this.$refs.deposit.save()
      }
      return Promise.resolve(true)
    }
  }
}
