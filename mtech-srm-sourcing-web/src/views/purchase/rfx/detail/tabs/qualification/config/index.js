import { i18n } from '@/main.js'
import { createEditInstance } from '@/utils/ej/dataGrid/index'
import Vue from 'vue'
const editInstance = createEditInstance().onInput((ctx, { field, rowData, value }) => {
  console.log('onInput', { ctx, rowData, value, field })
})
export const columnData = [
  {
    type: 'checkbox',
    width: '60',
    allowEditing: false
  },
  {
    field: 'lineIndexComponent',
    headerText: i18n.t('序号'),
    allowFiltering: false, // 序号列，不参与数据筛选
    allowResizing: false, // 序号列，不参与列顺序变化
    allowSorting: false, // 序号列，不参与列宽变化
    allowEditing: false, // 序号列，不参与数据编辑
    ignore: true, // 序号列，不参与数据筛选
    width: 80,
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: '<div>{{+data.index+1}}</div>',
          data() {
            return { data: {} }
          }
        })
      }
    },
    editTemplate: function () {
      return {
        template: Vue.component('actionOption', {
          template: '<div>{{+data.index+1}}</div>',
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    field: 'examineItemCode',
    headerText: i18n.t('审查代码'),
    allowEditing: false
  },
  {
    field: 'examineItemName',
    headerText: i18n.t('审查项目名称'),
    allowEditing: true
  },
  {
    field: 'examineItemSpec',
    headerText: i18n.t('审查项目说明'),
    allowEditing: true
  },
  // {
  //   field: "suppliersReply",
  //   headerText: i18n.t("供应商回复"),
  // },
  {
    field: 'needFile',
    headerText: i18n.t('是否需要上传附件'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    },
    allowEditing: true,
    edit: editInstance.create({
      getEditConfig: () => ({
        type: 'mt-select',
        dataSource: [
          { text: i18n.t('否'), value: 0 },
          { text: i18n.t('是'), value: 1 }
        ],
        placeholder: ''
      })
    })
  }
]
