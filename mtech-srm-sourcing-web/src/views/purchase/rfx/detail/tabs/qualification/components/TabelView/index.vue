<template>
  <div class="full-height">
    <!-- 需求附件弹窗 -->
    <UploaderDialog ref="uploaderDialog" />
    <Tabs :data-source="tabSource" v-model="tabVal"></Tabs>
    <Deposit
      ref="deposit"
      v-if="tabVal === 0 && examineId"
      :examine-id="examineId"
      :examine-info="examineInfo"
      :detail-info="detailInfo"
    />
    <Examine
      v-if="tabVal === 1 && examineId"
      :examine-id="examineId"
      :examine-info="examineInfo"
      :detail-info="detailInfo"
    />
  </div>
</template>
<script src="./script.js" />

<style scoped lang="scss">
.filter-form {
  display: flex;
  background: #fff;

  .mt-form-item {
    height: 55px;
    line-height: 55px;
    margin-bottom: 0 !important;
  }
}

// default.css
/deep/ .e-grid.e-bothlines .e-rowcell {
  border-left-width: 1px !important;
}
</style>
