<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject">
        <!-- <mt-form-item prop="examineItemCode" :label="$t('审查代码')">
          <mt-input
            v-model="formObject.examineItemCode"
            float-label-type="Never"
            :placeholder="$t('请输入审查代码')"
          ></mt-input>
        </mt-form-item> -->
        <mt-form-item prop="examineItemName" :label="$t('审查项目名称')">
          <mt-input
            v-model="formObject.examineItemName"
            float-label-type="Never"
            :placeholder="$t('请输入审查项目名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="needFile" :label="$t('是否需要上传附件')">
          <mt-select
            v-model="formObject.needFile"
            :data-source="needFileList"
            :show-clear-button="true"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="examineItemSpec" :label="$t('审查项目说明')">
          <mt-input
            v-model="formObject.examineItemSpec"
            float-label-type="Never"
            :placeholder="$t('请输入审查项目说明')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      editStatus: false,
      formObject: {
        examineItemCode: '',
        examineItemName: '',
        examineItemSpec: '',
        needFile: 0
      },
      needFileList: [
        { text: this.$t('是'), value: 1 },
        { text: this.$t('否'), value: 0 }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.formObject = { ...this.modalData.data }
    }
  },
  methods: {
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = { ...this.formObject }
          if (!this.editStatus) {
            delete params.id
          }
          this.$emit('confirm-function', params)
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/ .div-auth {
    background: #fafafa;
    height: 35px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    color: #0f0f0f;
    font-size: 12px;
    cursor: pointer;
    border-radius: 5px;
  }
}
</style>
