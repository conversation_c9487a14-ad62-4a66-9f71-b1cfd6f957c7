<template>
  <TimerClock
    v-if="timerClockInfo && timerClockInfo.isShow"
    v-bind="timerClockInfo"
    :direction-row="true"
    :no-padding="true"
  ></TimerClock>
</template>

<script>
import TimerClock from 'COMPONENTS/SourcingProject/timerClock.vue'

export default {
  components: {
    TimerClock
  },
  data() {
    return {
      timerClockInfo: null
    }
  },
  mounted() {
    const fn = (data) => {
      this.timerClockInfo = data
    }
    this.$bus.$on('TOP_INFO_TIMER_CLOCK_DATA_EMIT', fn)
    this.$once('hook:beforeDestroy', () => {
      this.$bus.$off('TOP_INFO_TIMER_CLOCK_DATA_EMIT', fn)
    })
  }
}
</script>

<style></style>
