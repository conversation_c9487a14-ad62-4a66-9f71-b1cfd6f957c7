<template>
  <div class="flex-full-height">
    <mt-tabs
      class="custom-tab"
      :tab-id="$utils.randomString()"
      :e-tab="false"
      :data-source="tabSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <!-- 报价详情、历史报价公用 -->
    <CustomAgGrid
      ref="CustomAgGrid"
      :search-config="searchConfig"
      :toolbar="toolbar"
      :columns="columns"
      :suppress-row-transform="true"
      :row-data="tableData"
      :animate-rows="false"
      :is-stop-editing-on-blur="isStopEditingOnBlur"
      :get-row-id="getRowId"
      @handleClickToolbar="handleClickToolbar"
      @onGridReady="onGridReady"
      @row-selected="onRowSelected"
      @refresh="refresh"
      @search="search"
    >
    </CustomAgGrid>
  </div>
</template>

<script>
import CustomAgGrid from '@/components/CustomAgGrid'
import cellFile from '@/components/AgCellComponents/cellFile'
import cellLink from '@/components/AgCellComponents/cellLink'
import { download, getHeadersFileName } from '@/utils/utils'
import { columnData as rfxItem } from '@/views/common/columnData/ag/item'
import { Query } from '@syncfusion/ej2-data'
import { hiddenFields } from '../../config'
import { columnData as logisticsColumnData } from '@/views/common/columnData/rfxItemLogistics'
import { columnData as biddingItemLogisticsColumnData } from '@/views/common/columnData/biddingItemLogistics'
import { columnData as itemDieMapColumnData } from '@/views/common/columnData/biddingItemDie'
import { columnData as itemExtMapColumnData } from '@/views/common/columnData/itemExtMap'
import { getFields as getSBIFields } from '@/views/common/columnData/supplierBiddingItem'
import { stepNotMergeFieldNoPrefix } from '@/views/common/columnData/constant'
import {
  setStepField,
  addRowSpan,
  rowSpan,
  cellClass,
  cellClassNoBg
} from '@/views/common/columnData/utils'
import cloneDeep from 'lodash/cloneDeep'

export default {
  inject: ['reload'],
  components: {
    CustomAgGrid,
    // eslint-disable-next-line
    cellFile,
    // eslint-disable-next-line
    cellLink
  },
  props: {
    fieldDefines: {
      type: Array,
      default: () => []
    },
    childFields: {
      type: Array,
      default: () => []
    },
    fieldStructures: {
      type: Array,
      default: () => []
    },
    detailInfo: {
      type: Object,
      default: () => {}
    },
    hasCountDown: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      agGrid: null,
      tabSource: [
        { title: this.$t('报价详情'), moduleType: 0 },
        { title: this.$t('历史报价'), moduleType: 1 }
      ],
      toolbar: [],
      searchConfig: [],
      searchParams: null,
      tableData: [],
      columns: [],
      rowSelections: null,
      isStopEditingOnBlur: false,
      showDialog: false,
      rfxId: this.$route.query.rfxId, // 需要传参
      dictItems: [],
      taxList: [], // 税率编码
      currencyList: [], // 货币名称
      purUnitList: [], // 采购单位
      directionalBargaining: null, //是否定向议价
      nonDirectionalSupNum: null, ////非定向议价供应商数量
      pointMode: '', //控制提交定点出现
      priceClassificationList: [
        { text: this.$t('暂估价格'), value: 'predict_price' },
        { text: this.$t('SRM价格'), value: 'srm_price' },
        { text: this.$t('执行价格'), value: 'execute_price' },
        { text: this.$t('基价'), value: 'basic_price' }
      ]
    }
  },
  async created() {
    this.$API.strategyConfig
      .findByRfxId({
        sourcingMode: this.$route.query.source,
        rfxId: this.$route.query.rfxId
      })
      .then((res) => {
        this.directionalBargaining = res.data.strategyConfigList[0].directionalBargaining
        this.nonDirectionalSupNum = res.data.strategyConfigList[0].nonDirectionalSupNum
        this.pointMode = res.data.strategyConfigList[0].pointMode
      })
    await this.initDictItems()
    await this.initTaxList()
    await this.initCurrencyList()
    await this.initPurUnitList()
  },
  mounted() {
    this.init()
  },
  methods: {
    // <<<<<<<<<<<<<<<<<<<<<<<<<< 事件操作 >>>>>>>>>>>>>>>>>>>>>>>>>>>
    // tab - 切换页签
    handleSelectTab(idx) {
      this.moduleType = this.tabSource[idx].moduleType
      // 初始数据
      this.initToolbar()
      this.initGridColumns()
      this.initTableData()
    },
    // ag - 初始监听
    onGridReady(params) {
      this.agGrid = params
    },
    // ag - 获取rowId
    getRowId(params) {
      return params.data.id
    },
    // ag - toolbar点击监听
    handleClickToolbar(args) {
      let _id = args.toolbar.id
      let _selectedRows = this.agGrid.api.getSelectedRows()
      switch (_id) {
        case 'Bargaining': // 议价
          this.handleBargaining(_selectedRows)
          break
        case 'PassExamine': // 审核通过
          this.handlePassExamine(_selectedRows)
          break
        case 'ToPricing': // 转定价
          this.handleoPricing()
          break
        case 'Assistant': // 比价助手
          this.handleAssistant()
          break
        case 'SubmitPoint': // 提交定点推荐
          this.handleSubmitPoint(_selectedRows)
          break
        case 'EndAccept': // 结束报/议价
          this.handleEndAccept()
          break
        case 'Export': // 导出
          this.handleExport()
          break
        default:
          break
      }
    },
    // toolbar点击监听 - 议价
    handleBargaining(selectedRows) {
      if (this.directionalBargaining !== 0 && selectedRows?.length < 1) {
        this.$toast({
          content: this.$t('当前为定向议价，请先选择一行'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        modal: () => import('../../components/bargaingDialog.vue'),
        data: {
          title: this.directionalBargaining == 0 ? this.$t('非定向议价') : this.$t('定向议价'),
          selectedRows
        },
        success: (data) => {
          let rfxBiddingItemIds = []
          let itemGroupIdList = []
          selectedRows.forEach((item) => {
            rfxBiddingItemIds.push(item.id)
            item.itemGroupId && itemGroupIdList.push(item.itemGroupId)
          })
          itemGroupIdList = Array.from(new Set(itemGroupIdList)) // 数据去重
          this.$API.comparativePrice
            .abate({
              rfxId: this.rfxId,
              ...data,
              abateEndDateTime: Number(data.abateEndDateTime), // 议价截止时间 时间戳
              rfxBiddingItemIds,
              itemGroupIdList
            })
            .then((res) => {
              if (res.code === 200) {
                this.$toast({ content: this.$t('议价成功'), type: 'success' })
                this.reload()
              }
            })
        }
      })
    },
    // toolbar点击监听 - 审核通过
    handlePassExamine(selectedRows) {
      if (selectedRows?.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认审核通过？')
        },
        success: () => {
          let rfxBiddingItemIdList = []
          selectedRows.forEach((e) => {
            rfxBiddingItemIdList.push(e.id)
            if (Array.isArray(e?.childItems)) {
              //接受报价时，如果有子级数据，将子级数据，遍历追加
              e.childItems.forEach((f) => {
                rfxBiddingItemIdList.push(f.id)
              })
            }
          })
          this.$API.comparativePrice
            .accept({ rfxId: this.rfxId, rfxBiddingItemIdList })
            .then(() => {
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
              this.refresh()
            })
        }
      })
    },
    // toolbar点击监听 - 转定价
    handleoPricing() {
      this.$API.comparativePrice.toPricing({ rfxId: this.rfxId }).then(() => {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        this.reload()
      })
    },
    // toolbar点击监听 - 比价助手
    handleAssistant() {
      this.$dialog({
        modal: () => import('../../../../../components/comparativePriceDialog.vue'),
        data: {
          rfxId: this.$route.query.rfxId,
          detailInfo: this.detailInfo
        }
      })
    },
    // toolbar点击监听 - 提交定点推荐
    handleSubmitPoint(selectedRows) {
      if (selectedRows?.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (this.pointMode != 2) {
        this.$toast({
          content: this.$t('此条单据不可提交定点'),
          type: 'warning'
        })
        return
      }
      let rfxPricingDataSourceId = selectedRows.filter((e) => e.id).map((e) => e.id)
      this.$API.comparativePrice.commitNewPointById(rfxPricingDataSourceId).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('提交成功'),
            type: 'success'
          })
        }
      })
    },
    // toolbar点击监听 - 导出
    handleExport() {
      let params = this.mergeParams(this.searchParams)
      this.$API.rfxQuotationDetails.excelExport(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // toolbar点击监听 - 结束报/议价
    handleEndAccept() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t("确认执行'结束报/议价'？")
        },
        success: () => {
          //结束议价
          if (this.detailInfo.finishAbate) {
            this.endAbate()
            return
          }
          // 结束报价
          const query = { rfxId: this.$route.query.rfxId }
          this.$API.inquiryBidding.checkIsCanAutoCloseRfx(query).then((res) => {
            if (res.data) {
              this.$dialog({
                data: {
                  title: this.$t('提示'),
                  message: this.$t('报价供应商不足，提前结束会关闭询价单，确认是否提前结束？')
                },
                success: () => {
                  this.endAccept()
                }
              })
            } else {
              this.endAccept()
            }
          })
        }
      })
    },
    // toolbar点击监听 - 结束报/议价 - 结束报价
    endAccept() {
      const query = { id: this.$route.query.rfxId }
      this.$store.commit('startLoading')
      this.$API.inquiryBidding.currentTurnFinish(query).then(() => {
        this.$store.commit('endLoading')
        this.$toast({
          content: this.$t("'结束报价'，操作成功"),
          type: 'success'
        })
        this.reload()
      })
    },
    // toolbar点击监听 - 结束报/议价 - 结束议价
    endAbate() {
      const query = { id: this.$route.query.rfxId }
      this.$store.commit('startLoading')
      this.$API.inquiryBidding.endAbate(query).then(() => {
        this.$store.commit('endLoading')
        this.$toast({
          content: this.$t("'结束议价'，操作成功"),
          type: 'success'
        })
        this.reload()
      })
    },
    // ag - 监听select选择框
    onRowSelected(e) {
      if (!e.data.isFirstLine) return
      let isSelected = e.node.selected
      let quoteGroupingKey = e.data.quoteGroupingKey
      //如果勾选的是阶梯第一行数据,同步其他行
      this.agGrid.api.forEachNode((node) => {
        if (node.data.quoteGroupingKey === quoteGroupingKey && !node.data.isFirstLine) {
          node.setSelected(isSelected)
        }
      })
    },
    // btn - 刷新
    refresh() {
      this.initTableData()
    },
    // btn - 查询
    search(params) {
      this.initTableData(params)
      this.searchParams = params
    },
    // <<<<<<<<<<<<<<<<<<<<<<<<<<  初始化  >>>>>>>>>>>>>>>>>>>>>>>>>>>
    // 初始化 - 页面
    init() {
      this.initSearchConfig()
      this.initToolbar()
      this.initGridColumns()
      this.initTableData()
    },
    // 初始化 - 查询区域
    initSearchConfig() {
      let _config = [
        {
          field: 'itemCode',
          label: this.$t('物料编码')
        },
        {
          field: 'itemName',
          label: this.$t('物料名称')
        },
        {
          field: 'temporaryItemCode',
          label: this.$t('临时物料编码')
        },
        {
          field: 'supplierCode',
          label: this.$t('供应商编码')
        },
        {
          field: 'supplierName',
          label: this.$t('供应商名称')
        }
      ]
      this.searchConfig = _config
    },
    // 初始化 - toolbar
    initToolbar() {
      let _toolbar = []
      if (this.moduleType === 1) {
        // 历史报价 不显示
        this.toolbar = []
        return
      }
      // toolbar 控制
      if (this.detailInfo.transferStatus === 31) {
        // 报价中 transferStatus = 31
        _toolbar = [
          {
            id: 'EndAccept',
            icon: 'icon_solid_edit',
            title: this.$t('结束报/议价')
          },
          {
            id: 'Export',
            icon: 'icon_solid_Download',
            title: this.$t('导出')
          }
        ]
      } else if (this.detailInfo.transferStatus === 32 && this.hasCountDown) {
        // 议价中 transferStatus = 31
        // if (!this.hasCountDown) {
        //   this.toolbar = []
        //   return
        // }
        _toolbar = [
          {
            id: 'Bargaining',
            icon: 'icon_solid_edit',
            title: this.$t('议价')
          },
          {
            id: 'EndAccept',
            icon: 'icon_solid_edit',
            title: this.$t('结束报/议价'),
            hide: !this.detailInfo.finishAbate
          },
          {
            id: 'Export',
            icon: 'icon_solid_Download',
            title: this.$t('导出')
          }
        ]
      } else if ([34, 20, 100].includes(this.detailInfo.transferStatus)) {
        // 定标、提交定点、已完成状态
        _toolbar = [
          {
            id: 'Export',
            icon: 'icon_solid_Download',
            title: this.$t('导出')
          }
        ]
      } else if (![31, 100].includes(this.detailInfo.transferStatus)) {
        // 非报价、 非完成
        _toolbar = [
          {
            id: 'Bargaining',
            icon: 'icon_solid_edit',
            title: this.$t('议价')
          },
          {
            id: 'PassExamine',
            icon: 'icon_solid_edit',
            title: this.$t('审核通过')
          },
          {
            id: 'ToPricing',
            icon: 'icon_solid_edit',
            title: this.$t('转定价')
          },
          {
            id: 'Assistant',
            icon: 'icon_solid_edit',
            title: this.$t('比价助手')
          },
          {
            id: 'SubmitPoint',
            icon: 'icon_solid_Submit',
            title: this.$t('提交定点推荐'),
            hide: this.detailInfo.transferStatus === 32
          },
          {
            id: 'EndAccept',
            icon: 'icon_solid_edit',
            title: this.$t('结束报/议价')
          },
          {
            id: 'Export',
            icon: 'icon_solid_Download',
            title: this.$t('导出')
          }
        ]
      }
      this.toolbar = _toolbar
    },
    // 初始化 - 表头
    initGridColumns() {
      // this.fieldDefines 沿用之前逻辑，保留field,headerText,allowEdit,eidtConfig此处阶梯根据this.fieldDefines配置ag相关属性
      let _columnData = this.defineGridColumns(cloneDeep(this.fieldDefines))
      // 阶梯相关字段顺序调整
      _columnData = setStepField(_columnData, stepNotMergeFieldNoPrefix)
      // 设置表头属性
      _columnData = this.setColumnsAttribute(_columnData)
      this.columns = cloneDeep(_columnData)
    },
    // 表头 - 字段处理(todo 逻辑细化)
    defineGridColumns(fieldDefines) {
      let arr = [
        {
          field: 'abateReason',
          headerText: this.$t('议价理由'),
          width: 120
        },
        {
          field: `priceStatus`,
          headerText: this.$t('状态'),
          width: 100
        }
      ]
      const rfxItemLogisticsColumn = logisticsColumnData({
        prefix: 'itemLogisticsResponse.',
        dictItems: this.dictItems,
        handleSelectChange: () => {}
      })
      const biddingItemLogisticsColumn = biddingItemLogisticsColumnData({
        prefix: 'biddingItemLogisticsResponse.',
        dictItems: this.dictItems,
        handleSelectChange: () => {}
      })
      const itemDieMapColumn = itemDieMapColumnData({
        prefix: 'itemDieResponse.',
        handleSelectChange: () => {}
      })
      const itemExtMapColumn = itemExtMapColumnData({
        prefix: 'itemExtMap.',
        dictItems: this.dictItems,
        handleSelectChange: () => {}
      })

      const sBIFields = getSBIFields({
        currencyList: this.currencyList,
        taxList: this.taxList,
        purUnitList: this.purUnitList
      })

      if (sBIFields.bidPurUnitName?.editConfig) {
        sBIFields.bidPurUnitName.editConfig = {
          type: 'select',
          props: {
            ...sBIFields.bidPurUnitName.editConfig.props,
            fields: { value: 'id', text: '__text' }
          }
        }
      }
      fieldDefines.forEach((e2) => {
        if (e2.fieldCode === 'lineNo') return //行号已用‘序号’处理
        let name = ''
        if (e2.tableName == 'rfx_item_ext') {
          name = 'itemExtMap'
          const field = `${name}.${e2.fieldCode}`
          // 通用信息显示
          const defColumn = itemExtMapColumn.find((e) => e.field === field)
          arr.push({
            field,
            headerText: this.$t(e2.fieldName),
            width: '120',
            editConfig: defColumn?.editConfig,
            formatter: defColumn?.formatter
          })
        } else if (e2.tableName == 'rfx_item_die') {
          name = 'itemDieResponse'
          const field = `${name}.${e2.fieldCode}`
          // 模具信息显示
          const defColumn = itemDieMapColumn.find((e) => e.field === field)
          arr.push({
            field,
            headerText: this.$t(e2.fieldName),
            width: '120',
            editConfig: defColumn?.editConfig,
            formatter: defColumn?.formatter
          })
        } else if (e2.tableName == 'rfx_item_logistics') {
          name = 'itemLogisticsResponse'
          const field = `${name}.${e2.fieldCode}`
          // 物流信息显示
          const defColumn = rfxItemLogisticsColumn.find((e) => e.field === field)
          arr.push({
            field,
            headerText: this.$t(e2.fieldName),
            width: '120',
            editConfig: defColumn?.editConfig,
            formatter: defColumn?.formatter
          })
        } else if (e2.tableName == 'rfx_bidding_item_logistics') {
          name = 'biddingItemLogisticsResponse'
          const field = `${name}.${e2.fieldCode}`
          // 物流信息显示
          const defColumn = biddingItemLogisticsColumn.find((e) => e.field === field)
          arr.push({
            field,
            headerText: this.$t(e2.fieldName),
            width: '120',
            editConfig: defColumn?.editConfig,
            formatter: defColumn?.formatter
          })
        } else if (e2.tableName === 'rfx_bidding_item') {
          name = ''
          const row = {
            formatter: sBIFields?.[e2.fieldCode]?.formatter,
            editConfig: sBIFields?.[e2.fieldCode]?.editConfig,
            field: e2.fieldCode,
            headerText: this.$t(e2.fieldName),
            allowEditing: ['allocationQuantity'].includes(e2.fieldCode) ? true : false,
            width: '200'
          }
          arr.push(row)
        } else if (e2.tableName === 'rfx_file') {
          arr.push({
            field: e2.fieldCode,
            headerText: this.$t(e2.fieldName),
            width: 150,
            allowEditing: false
          })
        } else {
          arr.push({
            field: e2.fieldCode,
            headerText: this.$t(e2.fieldName),
            width: '120'
          })
        }
      })
      arr.push(
        {
          field: 'bidTimes',
          headerText: this.$t('次数'),
          width: '90'
        },
        {
          field: 'roundNo',
          headerText: this.$t('轮次'),
          width: '90'
        }
      )
      // todo 逻辑待整理
      arr.forEach((e) => {
        if (e.field === 'priceClassification') {
          e.allowEditing = false
          e.editType = 'dropdownedit'
          e.formatter = ({ field }, item) => {
            // const cellVal = item[field];
            let cellVal = ''
            if (this.detailInfo.priceClassification) {
              cellVal = this.detailInfo.priceClassification
              item[field] = this.detailInfo.priceClassification
            } else {
              cellVal = ''
              item[field] = ''
            }
            return this.priceClassificationList.find((e) => e.value === cellVal)?.text
          }
          e.edit = {
            params: {
              dataSource: this.priceClassificationList,
              fields: { value: 'value', text: 'text' },
              query: new Query()
            }
          }
        } else if (e.field == 'costModelName') {
          e.cssClass = 'normal-class'
          e.cellTools = [
            {
              id: 'editCost',
              title: this.$t('报价'),
              visibleCondition: (data) => {
                return data['costModelName']
              }
            }
          ]
        }
      })
      // 添加序号(阶梯分组序号)
      arr.unshift({ field: 'rowGroupIndex', headerText: this.$t('序号') })
      // 添加勾选框
      if (this.isSelectControl()) {
        arr.unshift({ option: 'checkboxSelection', width: 55 })
      }
      return arr
    },
    // 表头 - 设置ag表头配置相关属性
    setColumnsAttribute(columns) {
      let _editable = false // 评标页面不可编辑
      let _base = rfxItem()
      let _columns = columns.map((item) => {
        // checkBox 特殊处理
        if (item.option === 'checkboxSelection') {
          return {
            ...item,
            rowSpan: rowSpan,
            cellClass: _editable ? cellClass : cellClassNoBg
          }
        }
        let _attrObj = {
          required: item.required,
          field: item.field || '',
          headerName: item.headerText,
          editable: _editable && item.allowEditing,
          editConfig: item.editConfig,
          width: item.width,
          rowSpan: rowSpan,
          cellClass: _editable ? cellClass : cellClassNoBg
        }
        // 特殊处理 - 成本分析
        if (['costAnalysis'].includes(item.field)) {
          _attrObj.cellRenderer = 'cellLink'
          _attrObj.cellRendererParams = {
            handleable: true, //“可操作”（不可编辑），取editable避免从历史报价进入成本分析页面
            rfxId: this.$route.query.rfxId,
            type: 'pur'
          }
        }
        // 特殊处理 - 附件查看
        if (['drawing', 'supplierDrawing'].includes(item.field)) {
          _attrObj.cellRenderer = 'cellFile'
        }
        const _find = _base.find((x) => x.field === item.field)
        if (_find) {
          // 特殊处理 (非编辑页签editable控制)
          if ('editable' in _find) {
            _find.editable = _editable && _find.editable
          }

          _attrObj.editConfig = _find?.editConfig
            ? { ..._attrObj.editConfig, ..._find?.editConfig }
            : { ..._attrObj.editConfig }
          delete _find.editConfig
          _attrObj = Object.assign({}, _attrObj, _find)
        }
        return _attrObj
      })
      return _columns
    },
    // 表头 - 工具 - 判断是否需要选择框
    isSelectControl() {
      // 历史报价 不显示select框
      // 报价详情、定标、提交定点、已完成状态 不显示select框
      if (this.moduleType === 1 || [31, 34, 20, 100].includes(this.detailInfo.transferStatus)) {
        return false
      }
      if (![31, 100].includes(this.detailInfo.transferStatus)) {
        // 非报价、 非完成
        return true
      }
      return false
    },
    // 初始化 - 表格数据
    async initTableData(rules) {
      this.$store.commit('startLoading')
      this.tableData = []
      let params = this.mergeParams(rules)
      const res = await this.$API.rfxQuotationDetails[
        this.moduleType === 1 ? 'queryHistoryBiddingItemRequest' : 'queryBiddingItemDetailRequest'
      ](params).catch(() => {})
      if (res) {
        let records = cloneDeep(res.data?.records || [])
        let list = this.serializeGridList(records)
        this.tableData = [...list]
        this.$store.commit('endLoading')
      }
    },
    // 表格数据 - 拼接请求参数
    mergeParams(rules) {
      let params = {
        defaultRules: [
          {
            condition: 'and',
            field: 'rfx_item.rfxHeaderId',
            operator: 'equal',
            value: this.$route.query.rfxId
          }
        ],
        page: { current: 1, size: 10000 }
      }
      if (rules) params = Object.assign({}, params, rules)
      return params
    },
    // 表格数据 - 表格数据序列化
    serializeGridList(list) {
      //处理密封报价  数据显示
      list.forEach((e) => {
        hiddenFields.forEach((k) => {
          e[k] = e[k] === -999 ? '******' : e[k] //设置密封报价
        })
        if (e.declinePercent !== '******') {
          //降幅%加密处理
          e.declinePercent = e.declinePercent ? (e.declinePercent * 100).toFixed(2) : ''
        }

        e.drawing = e.fileList ? JSON.stringify(e.fileList) : null //单独处理附件字段
        e.supplierDrawing = e.supplierFileList ? JSON.stringify(e.supplierFileList) : null //单独处理this.$t("供应商附件")字段
        e.stepQuoteName =
          e.itemStageList && e.itemStageList.length > 0 ? JSON.stringify(e.itemStageList) : null //单独处理阶梯报价
        e.stepNum = e.stepValue
        let _subItems = e['childItems']
        if (Array.isArray(_subItems)) {
          this.serializeGridList(_subItems)
        }
      })
      list = addRowSpan(list)
      return list
    },
    // 初始化 - 字典数据
    async initDictItems() {
      const tasks = [
        'TradeClause', // 贸易条款 tradeClauseNameData
        'DELIVERY_PLACE', // 直送地 deliveryPlaceData
        'TransportMode', // 物流方式 shippingMethodNameData
        'START-PORT', // 起始港
        'DESTINATION-PORT' // 目的港
      ].map((dictCode) => this.$API.masterData.dictionaryGetList({ dictCode }))
      const result = await Promise.all(tasks).catch((err) => {
        console.error(err)
      })
      if (!result) {
        return
      }
      this.dictItems = result.map((e) => e.data).flat()
    },
    // 初始化 - 税率列表
    async initTaxList() {
      const res = await this.$API.masterData.queryAllTaxItem().catch(() => {})
      if (res) {
        this.taxList = res.data
      }
    },
    // 初始化 - 币种编码
    async initCurrencyList() {
      const res = await this.$API.masterData.queryAllCurrency().catch(() => {})
      if (res) {
        this.currencyList = res.data
      }
    },
    // 初始化 - 采购单位
    async initPurUnitList() {
      this.$API.masterData.pagedQueryUnit().then((res) => {
        this.purUnitList = res?.data?.records || [] // FIXME 采购单位和基本单位的区别
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.custom-tab /deep/.mt-tabs-container {
  background: #ffffff;
}
</style>
