import CustomAgGrid from '@/components/CustomAgGrid'
import cellFile from '@/components/AgCellComponents/cellFile'
import cellLink from '@/components/AgCellComponents/cellLink'
import cellDialog from '@/components/AgCellComponents/cellDialog'
import { download, getHeadersFileName } from '@/utils/utils'
import { hiddenFields } from './index'
import { columnData as rfxItem } from '@/views/common/columnData/ag/item'
import { columnData as itemLogistics } from '@/views/common/columnData/ag/itemLogistics'
import { columnData as biddingItemLogistics } from '@/views/common/columnData/ag/biddingItemLogistics'
import { columnData as itemDie } from '@/views/common/columnData/ag/itemDie'
import { columnData as itemExt } from '@/views/common/columnData/ag/itemExt'
import { columnData as itemLogisticsSea } from '@/views/common/columnData/ag/itemLogisticsSea'
import { columnData as itemLogisticsRailway } from '@/views/common/columnData/ag/itemLogisticsRailway'
import { columnData as itemLogisticsTrunk } from '@/views/common/columnData/ag/itemLogisticsTrunk'
import { addRowSpan, rowSpan, cellClass, cellClassNoBg } from '@/views/common/columnData/utils'
import cloneDeep from 'lodash/cloneDeep'
import clickoutside from '@/directive/clickoutside'
import {
  quoTableNameMap,
  logisticsTableNameMap
  // notAllowEditFields,
  // ktNotAllowEditFields,
  // costFactorNotAllowEditFields,
  // generalNumberEditFields,
  // integerNumberEditFields,
  // priceNumberEditFields,
  // dateEditFields,
  // booleanEditField
} from '@/views/common/columnData/ag/purConstant'
export default {
  inject: ['reload'],
  components: {
    CustomAgGrid,
    // eslint-disable-next-line
    cellFile,
    // eslint-disable-next-line
    cellLink,
    // eslint-disable-next-line
    cellDialog
  },
  directives: { clickoutside: clickoutside },
  props: {
    fieldDefines: {
      type: Array,
      default: () => []
    },
    childFields: {
      type: Array,
      default: () => []
    },
    fieldStructures: {
      type: Array,
      default: () => []
    },
    detailInfo: {
      type: Object,
      default: () => {}
    },
    hasCountDown: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      agGrid: null,
      tabSource: [
        { title: this.$t('报价详情'), moduleType: 0 },
        { title: this.$t('历史报价'), moduleType: 1 }
      ],
      toolbar: [],
      searchConfig: [],
      searchParams: null,
      tableData: [],
      columns: [],
      rowSelections: null,
      isStopEditingOnBlur: false,
      showDialog: false,
      rfxId: this.$route.query.rfxId, // 需要传参
      dictItems: [],
      taxList: [], // 税率编码
      currencyList: [], // 货币名称
      purUnitList: [], // 采购单位
      directionalBargaining: null, //是否定向议价
      nonDirectionalSupNum: null, ////非定向议价供应商数量
      pointMode: '', //控制提交定点出现
      priceClassificationList: [
        { text: this.$t('暂估价格'), value: 'predict_price' },
        { text: this.$t('SRM价格'), value: 'srm_price' },
        { text: this.$t('执行价格'), value: 'execute_price' },
        { text: this.$t('基价'), value: 'basic_price' }
      ],
      hideStepFieldCode: []
    }
  },
  created() {
    this.$API.strategyConfig
      .findByRfxId({
        sourcingMode: this.$route.query.source,
        rfxId: this.$route.query.rfxId
      })
      .then((res) => {
        this.directionalBargaining = res.data.strategyConfigList[0].directionalBargaining
        this.nonDirectionalSupNum = res.data.strategyConfigList[0].nonDirectionalSupNum
        this.pointMode = res.data.strategyConfigList[0].pointMode
      })
  },
  async mounted() {
    await this.initDictItems()
    await this.initTaxList()
    await this.initCurrencyList()
    await this.initPurUnitList()
    this.init()
  },
  methods: {
    // <<<<<<<<<<<<<<<<<<<<<<<<<< 事件操作 >>>>>>>>>>>>>>>>>>>>>>>>>>>
    // tab - 切换页签
    handleSelectTab(idx) {
      this.moduleType = this.tabSource[idx].moduleType
      // 初始数据
      this.initToolbar()
      this.initGridColumns()
      this.initTableData()
    },
    // ag - 初始监听
    onGridReady(params) {
      this.agGrid = params
    },
    // ag - 点击事件监听
    clickoutside() {
      this.agGrid.api.stopEditing()
    },
    // ag - 获取rowId
    getRowId(params) {
      return params.data.id
    },
    // ag - toolbar点击监听
    handleClickToolbar(args) {
      let _id = args.toolbar.id
      let _selectedRows = this.agGrid.api.getSelectedRows()
      switch (_id) {
        case 'Bargaining': // 议价
          this.handleBargaining(_selectedRows)
          break
        case 'PassExamine': // 审核通过
          this.handlePassExamine(_selectedRows)
          break
        case 'ToPricing': // 转定价
          this.handleoPricing()
          break
        case 'Assistant': // 比价助手
          this.handleAssistant()
          break
        case 'SubmitPoint': // 提交定点推荐
          this.handleSubmitPoint(_selectedRows)
          break
        case 'EndAccept': // 结束报/议价
          this.handleEndAccept()
          break
        case 'Export': // 导出
          this.handleExport()
          break
        case 'SyncTMS': // 推送TMS
          this.handleSyncTMS()
          break
        default:
          break
      }
    },
    // toolbar点击监听 - 议价
    handleBargaining(selectedRows) {
      if (this.directionalBargaining !== 0 && selectedRows?.length < 1) {
        this.$toast({
          content: this.$t('当前为定向议价，请先选择一行'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        modal: () => import('../components/bargaingDialog.vue'),
        data: {
          title: this.directionalBargaining == 0 ? this.$t('非定向议价') : this.$t('定向议价'),
          selectedRows
        },
        success: (data) => {
          let rfxBiddingItemIds = []
          let itemGroupIdList = []
          selectedRows.forEach((item) => {
            rfxBiddingItemIds.push(item.id)
            item.itemGroupId && itemGroupIdList.push(item.itemGroupId)
          })
          itemGroupIdList = Array.from(new Set(itemGroupIdList)) // 数据去重
          this.$API.comparativePrice
            .abate({
              rfxId: this.rfxId,
              ...data,
              abateEndDateTime: Number(data.abateEndDateTime), // 议价截止时间 时间戳
              rfxBiddingItemIds,
              itemGroupIdList
            })
            .then((res) => {
              if (res.code === 200) {
                this.$toast({ content: this.$t('议价成功'), type: 'success' })
                this.reload()
              }
            })
        }
      })
    },
    // toolbar点击监听 - 审核通过
    handlePassExamine(selectedRows) {
      if (selectedRows?.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认审核通过？')
        },
        success: () => {
          let rfxBiddingItemIdList = []
          selectedRows.forEach((e) => {
            rfxBiddingItemIdList.push(e.id)
            if (Array.isArray(e?.childItems)) {
              //接受报价时，如果有子级数据，将子级数据，遍历追加
              e.childItems.forEach((f) => {
                rfxBiddingItemIdList.push(f.id)
              })
            }
          })
          this.$API.comparativePrice
            .accept({ rfxId: this.rfxId, rfxBiddingItemIdList })
            .then(() => {
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
              this.refresh()
            })
        }
      })
    },
    // toolbar点击监听 - 转定价
    handleoPricing() {
      this.$API.comparativePrice.toPricing({ rfxId: this.rfxId }).then(() => {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        this.reload()
      })
    },
    // toolbar点击监听 - 比价助手
    handleAssistant() {
      this.$dialog({
        modal: () => import('@/views/purchase/rfx/components/comparativePriceDialog.vue'),
        data: {
          rfxId: this.$route.query.rfxId,
          detailInfo: this.detailInfo
        }
      })
    },
    // toolbar点击监听 - 提交定点推荐
    handleSubmitPoint(selectedRows) {
      if (selectedRows?.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (this.pointMode != 2) {
        this.$toast({
          content: this.$t('此条单据不可提交定点'),
          type: 'warning'
        })
        return
      }
      let rfxPricingDataSourceId = selectedRows.filter((e) => e.id).map((e) => e.id)
      this.$API.comparativePrice.commitNewPointById(rfxPricingDataSourceId).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('提交成功'),
            type: 'success'
          })
        }
      })
    },
    // toolbar点击监听 - 导出
    handleExport() {
      let params = this.mergeParams(this.searchParams)
      this.$API.rfxQuotationDetails.excelExport(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // toolbar点击监听 - 推送TMS
    handleSyncTMS() {
      this.$API.rfxQuotationDetails.syncTMS({ rfxId: this.rfxId }).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: this.$t('推送成功'),
            type: 'success'
          })
        }
      })
    },
    // toolbar点击监听 - 结束报/议价
    handleEndAccept() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t("确认执行'结束报/议价'？")
        },
        success: () => {
          //结束议价
          if (this.detailInfo.finishAbate) {
            this.endAbate()
            return
          }
          // 结束报价
          const query = { rfxId: this.$route.query.rfxId }
          this.$API.inquiryBidding.checkIsCanAutoCloseRfx(query).then((res) => {
            if (res.data) {
              this.$dialog({
                data: {
                  title: this.$t('提示'),
                  message: this.$t('报价供应商不足，提前结束会关闭询价单，确认是否提前结束？')
                },
                success: () => {
                  this.endAccept()
                }
              })
            } else {
              this.endAccept()
            }
          })
        }
      })
    },
    // toolbar点击监听 - 结束报/议价 - 结束报价
    endAccept() {
      const query = { id: this.$route.query.rfxId }
      this.$store.commit('startLoading')
      this.$API.inquiryBidding.currentTurnFinish(query).then(() => {
        this.$store.commit('endLoading')
        this.$toast({
          content: this.$t("'结束报价'，操作成功"),
          type: 'success'
        })
        this.reload()
      })
    },
    // toolbar点击监听 - 结束报/议价 - 结束议价
    endAbate() {
      const query = { id: this.$route.query.rfxId }
      this.$store.commit('startLoading')
      this.$API.inquiryBidding.endAbate(query).then(() => {
        this.$store.commit('endLoading')
        this.$toast({
          content: this.$t("'结束议价'，操作成功"),
          type: 'success'
        })
        this.reload()
      })
    },
    // ag - 监听select选择框
    onRowSelected(e) {
      if (!e.data.isFirstLine) return
      let isSelected = e.node.selected
      let quoteGroupingKey = e.data.quoteGroupingKey
      //如果勾选的是阶梯第一行数据,同步其他行
      this.agGrid.api.forEachNode((node) => {
        if (node.data.quoteGroupingKey === quoteGroupingKey && !node.data.isFirstLine) {
          node.setSelected(isSelected)
        }
      })
    },
    // btn - 刷新
    refresh() {
      this.initTableData()
    },
    // btn - 查询
    search(params) {
      this.initTableData(params)
      this.searchParams = params
    },
    // <<<<<<<<<<<<<<<<<<<<<<<<<<  初始化  >>>>>>>>>>>>>>>>>>>>>>>>>>>
    // 初始化 - 页面
    init() {
      this.initSearchConfig()
      this.initToolbar()
      this.initGridColumns()
      this.initTableData()
    },
    // 初始化 - 查询区域
    initSearchConfig() {
      let _config = [
        {
          field: 'itemCode',
          label: this.$t('物料编码')
        },
        {
          field: 'itemName',
          label: this.$t('物料名称')
        },
        {
          field: 'temporaryItemCode',
          label: this.$t('临时物料编码')
        },
        {
          field: 'supplierCode',
          label: this.$t('供应商编码')
        },
        {
          field: 'supplierName',
          label: this.$t('供应商名称')
        }
      ]
      this.searchConfig = _config
    },
    // 初始化 - toolbar
    initToolbar() {
      let _toolbar = []
      if (this.moduleType === 1) {
        // 历史报价 不显示
        this.toolbar = []
        return
      }
      // toolbar 控制
      if (this.detailInfo.transferStatus === 31) {
        // 报价中 transferStatus = 31
        _toolbar = [
          {
            id: 'EndAccept',
            icon: 'icon_solid_edit',
            title: this.$t('结束报/议价')
          },
          {
            id: 'Export',
            icon: 'icon_solid_Download',
            title: this.$t('导出')
          }
        ]
      } else if (this.detailInfo.transferStatus === 32 && this.hasCountDown) {
        // 议价中 transferStatus = 31
        // if (!this.hasCountDown) {
        //   this.toolbar = []
        //   return
        // }
        _toolbar = [
          {
            id: 'Bargaining',
            icon: 'icon_solid_edit',
            title: this.$t('议价')
          },
          {
            id: 'EndAccept',
            icon: 'icon_solid_edit',
            title: this.$t('结束报/议价'),
            hide: !this.detailInfo.finishAbate
          },
          {
            id: 'Export',
            icon: 'icon_solid_Download',
            title: this.$t('导出')
          }
        ]
      } else if ([34, 20, 100].includes(this.detailInfo.transferStatus)) {
        // 定标、提交定点、已完成状态
        _toolbar = [
          {
            id: 'Export',
            icon: 'icon_solid_Download',
            title: this.$t('导出')
          }
        ]
      } else if (![31, 100].includes(this.detailInfo.transferStatus)) {
        // 非报价、 非完成
        _toolbar = [
          {
            id: 'Bargaining',
            icon: 'icon_solid_edit',
            title: this.$t('议价')
          },
          {
            id: 'PassExamine',
            icon: 'icon_solid_edit',
            title: this.$t('审核通过')
          },
          {
            id: 'ToPricing',
            icon: 'icon_solid_edit',
            title: this.$t('转定价')
          },
          {
            id: 'Assistant',
            icon: 'icon_solid_edit',
            title: this.$t('比价助手'),
            hide: ['sea_transport_annual', 'railway_transport_annual'].includes(
              this.detailInfo.sourcingScenarios
            )
          },
          {
            id: 'SubmitPoint',
            icon: 'icon_solid_Submit',
            title: this.$t('提交定点推荐'),
            hide: this.detailInfo.transferStatus === 32
          },
          {
            id: 'EndAccept',
            icon: 'icon_solid_edit',
            title: this.$t('结束报/议价')
          },
          {
            id: 'Export',
            icon: 'icon_solid_Download',
            title: this.$t('导出')
          }
        ]
        // 物流添加推送TMS按钮
        if (this.detailInfo.sourcingScenarios === 'LOGISTICS') {
          _toolbar.push({
            id: 'SyncTMS',
            icon: 'icon_solid_edit',
            title: this.$t('推送TMS')
          })
        }
      }
      this.toolbar = _toolbar
    },
    // 初始化 - 表头
    async initGridColumns() {
      // 添加表头固定字段
      let _columnData = this.setFixdColumns(cloneDeep(this.fieldDefines))
      // 设置表头属性
      _columnData = this.defineGridColumns(_columnData)
      // 物流表头特殊设置
      if (this.detailInfo.sourcingScenarios === 'sea_transport_annual') {
        _columnData = await this.setAnnualLogisticsItemConfig(_columnData) // 物流海运字段较多，需要设置为双表头结构
      }
      // 物流干线表头特殊设置
      if (this.detailInfo.sourcingScenarios === 'trunk_transport_annual') {
        _columnData = await this.setLogisticsDynamicConfig(_columnData) // 物流海运字段较多，需要设置为双表头结构
      }
      this.columns = cloneDeep(_columnData)
    },
    // 表头 - 获取表头是否可编辑
    getGridEditAble() {
      // 定价中
      if ([34, 100].includes(this.detailInfo.transferStatus)) return false
      return true
    },
    // 表头 - 字段处理
    defineGridColumns(fieldDefines) {
      let _gridEditAble = this.getGridEditAble()
      let _columnData = []
      const tableColumnMap = {
        rfx_item: rfxItem({
          currencyList: this.currencyList,
          taxList: this.taxList,
          purUnitList: this.purUnitList,
          rfxId: this.$route.query.rfxId,
          isFc: this?.detailInfo?.rfxGeneralType === 2
        }),
        rfx_item_logistics: itemLogistics({
          // 物流信息
          prefix: 'itemLogisticsResponse.',
          dictItems: this.dictItems
        }),
        rfx_bidding_item: rfxItem({
          // 采方直接去外层数据，不取bddingItemDTO
          currencyList: this.currencyList,
          taxList: this.taxList,
          purUnitList: this.purUnitList,
          rfxId: this.$route.query.rfxId
        }),
        rfx_bidding_item_logistics: biddingItemLogistics({
          prefix: 'biddingItemLogisticsResponse.',
          dictItems: this.dictItems
        }),
        rfx_item_die: itemDie({
          // 模具信息
          prefix: 'itemDieResponse.'
        }),
        rfx_item_ext: itemExt({
          prefix: 'itemExtMap.',
          dictItems: this.dictItems
        }),
        mt_rfx_annual_logistics_sea_bidding_item: itemLogisticsSea({
          prefix: 'rfxAnnualLogisticsSeaDTO.',
          dictItems: this.dictItems
        }),
        bidding_annual_railway: itemLogisticsRailway({
          prefix: 'annualLogisticsRailwayItemDTO.',
          dictItems: this.dictItems
        }),
        annual_logistics_transport: itemLogisticsTrunk({
          prefix: 'annualLogisticsTrunkItem.',
          dictItems: this.dictItems
        })
      }

      fieldDefines.forEach((item) => {
        let tableName = item.tableName || 'rfx_item'
        let name = quoTableNameMap[tableName]?.key
        let field = name ? `${name}.${item.fieldCode}` : `${item.fieldCode}`
        let headerName = item.headerName || item.fieldName
        let editable = false
        // 与配置文件中字段进行匹配
        let column = tableColumnMap[tableName]?.find((e) => e.field === field) || {}
        /**
         * 编辑控制
         * 1.如果是物流场景，则物流意见可以编辑
         */
        if (item.fieldCode === 'suggestOpinionRemark') {
          editable = true
        }
        let col = {
          width: 130, //默认值130
          ...column,
          editable: editable,
          fieldCode: item.fieldCode,
          field,
          headerName,
          tableName,
          cellClass: () => {
            return editable ? 'cellEditable' : ''
          }
        }
        // 特殊处理 - 成本分析
        if (['costAnalysis'].includes(item.field)) {
          col.cellRenderer = 'cellLink'
          col.cellRendererParams = {
            handleable: true, //“可操作”（不可编辑）
            rfxId: this.$route.query.rfxId,
            type: 'pur'
          }
        }
        // 特殊处理 - 附件查看
        if (['drawing', 'supplierDrawing'].includes(item.fieldCode)) {
          col.cellRenderer = 'cellFile'
        }
        // 特殊处理 - 柜量
        if (['cabinetQuantity'].includes(item.fieldCode)) {
          col.cellRenderer = 'cellDialog'
          col.editable = false
          col.cellRendererParams = {
            position: 'rfxAnnualLogisticsSeaDTO',
            handleable: true, //“可操作”（不可编辑）（针对于附件查看类字段）
            rfxId: this.$route.query.rfxId
          }
        }
        // 重置editable 和 cellClass(禁用编辑，禁用背景色)
        if (!_gridEditAble) {
          col.editable = false
          col.cellClass = ''
        }
        _columnData.push(col)
      })

      // if (sBIFields.bidPurUnitName?.editConfig) {
      //   sBIFields.bidPurUnitName.editConfig = {
      //     type: 'select',
      //     props: {
      //       ...sBIFields.bidPurUnitName.editConfig.props,
      //       fields: { value: 'id', text: '__text' }
      //     }
      //   }
      // }
      // fieldDefines.forEach((e2) => {
      //   if (e2.fieldCode === 'lineNo') return //行号已用‘序号’处理
      //   let name = ''
      //   if (e2.tableName == 'rfx_item_ext') {
      //     name = 'itemExtMap'
      //     const field = `${name}.${e2.fieldCode}`
      //     // 通用信息显示
      //     const defColumn = itemExtMapColumn.find((e) => e.field === field)
      //     arr.push({
      //       field,
      //       headerText: this.$t(e2.fieldName),
      //       width: '120',
      //       editConfig: defColumn?.editConfig,
      //       formatter: defColumn?.formatter
      //     })
      //   } else if (e2.tableName == 'rfx_item_die') {
      //     name = 'itemDieResponse'
      //     const field = `${name}.${e2.fieldCode}`
      //     // 模具信息显示
      //     const defColumn = itemDieMapColumn.find((e) => e.field === field)
      //     arr.push({
      //       field,
      //       headerText: this.$t(e2.fieldName),
      //       width: '120',
      //       editConfig: defColumn?.editConfig,
      //       formatter: defColumn?.formatter
      //     })
      //   } else if (e2.tableName == 'rfx_item_logistics') {
      //     name = 'itemLogisticsResponse'
      //     const field = `${name}.${e2.fieldCode}`
      //     // 物流信息显示
      //     const defColumn = rfxItemLogisticsColumn.find((e) => e.field === field)
      //     arr.push({
      //       field,
      //       headerText: this.$t(e2.fieldName),
      //       width: '120',
      //       editConfig: defColumn?.editConfig,
      //       formatter: defColumn?.formatter
      //     })
      //   } else if (e2.tableName == 'rfx_bidding_item_logistics') {
      //     name = 'biddingItemLogisticsResponse'
      //     const field = `${name}.${e2.fieldCode}`
      //     // 物流信息显示
      //     const defColumn = biddingItemLogisticsColumn.find((e) => e.field === field)
      //     arr.push({
      //       field,
      //       headerText: this.$t(e2.fieldName),
      //       width: '120',
      //       editConfig: defColumn?.editConfig,
      //       formatter: defColumn?.formatter
      //     })
      //   } else if (e2.tableName === 'rfx_bidding_item') {
      //     name = ''
      //     const row = {
      //       formatter: sBIFields?.[e2.fieldCode]?.formatter,
      //       editConfig: sBIFields?.[e2.fieldCode]?.editConfig,
      //       field: e2.fieldCode,
      //       headerText: this.$t(e2.fieldName),
      //       allowEditing: ['allocationQuantity'].includes(e2.fieldCode) ? true : false,
      //       width: '200'
      //     }
      //     arr.push(row)
      //   } else if (e2.tableName === 'rfx_file') {
      //     arr.push({
      //       field: e2.fieldCode,
      //       headerText: this.$t(e2.fieldName),
      //       width: 150,
      //       allowEditing: false
      //     })
      //   } else {
      //     arr.push({
      //       field: e2.fieldCode,
      //       headerText: this.$t(e2.fieldName),
      //       width: '120'
      //     })
      //   }
      // })
      // 添加勾选框
      if (this.isSelectControl()) {
        _columnData.unshift({ option: 'checkboxSelection', width: 55 })
      }
      return _columnData
    },
    // 表头 - 设置ag表头配置相关属性
    setColumnsAttribute(columns) {
      let _editable = false // 评标页面不可编辑
      let _base = rfxItem()
      let _columns = columns.map((item) => {
        // checkBox 特殊处理
        if (item.option === 'checkboxSelection') {
          return {
            ...item,
            rowSpan: rowSpan,
            cellClass: _editable ? cellClass : cellClassNoBg
          }
        }
        let _attrObj = {
          required: item.required,
          field: item.field || '',
          headerName: item.headerText,
          editable: _editable && item.allowEditing,
          editConfig: item.editConfig,
          width: item.width,
          rowSpan: rowSpan,
          cellClass: _editable ? cellClass : cellClassNoBg
        }
        // 特殊处理 - 成本分析
        if (['costAnalysis'].includes(item.field)) {
          _attrObj.cellRenderer = 'cellLink'
          _attrObj.cellRendererParams = {
            handleable: true, //“可操作”（不可编辑），取editable避免从历史报价进入成本分析页面
            rfxId: this.$route.query.rfxId,
            type: 'pur'
          }
        }
        // 特殊处理 - 附件查看
        if (['drawing', 'supplierDrawing'].includes(item.field)) {
          _attrObj.cellRenderer = 'cellFile'
        }
        const _find = _base.find((x) => x.field === item.field)
        if (_find) {
          // 特殊处理 (非编辑页签editable控制)
          if ('editable' in _find) {
            _find.editable = _editable && _find.editable
          }

          _attrObj.editConfig = _find?.editConfig
            ? { ..._attrObj.editConfig, ..._find?.editConfig }
            : { ..._attrObj.editConfig }
          delete _find.editConfig
          _attrObj = Object.assign({}, _attrObj, _find)
        }
        return _attrObj
      })
      return _columns
    },
    // 表头 - 物流双表头字段处理
    async setAnnualLogisticsItemConfig(columns) {
      let res = await this.$API.rfxQuotationDetails.getLogisticsConfig({
        rfxId: this.$route.query.rfxId,
        moduleType: 9
      })
      if (res.code === 200) {
        this.annualLogisticsItemConfig = [...res.data]
      }
      let _columns = this.groupColumns(columns, this.annualLogisticsItemConfig)
      return _columns
    },
    // 物流 - 获取阶梯列配置
    async setLogisticsDynamicConfig(_columnData) {
      let dynamicConfig = []
      let hideStepFieldCode = []
      const res = await this.$API.rfxDetail.getLogisticsDynamicConfig({
        id: this.$route.query.rfxId
      })
      if (res.code === 200) {
        const _data = { ...res.data } || {}
        for (let i in _data) {
          _data[i].forEach((item) => {
            dynamicConfig.push({
              headerName: item.stepName,
              children: [
                {
                  headerName: this.$t('数量'),
                  width: 80,
                  field: `${item.stepCode}_fieldData`,
                  fieldCode: `${item.stepCode}_fieldData`
                },
                {
                  headerName: this.$t('未税单价'),
                  width: 120,
                  field: `${item.stepCode}_untaxedUnitPrice`,
                  fieldCode: `${item.stepCode}_untaxedUnitPrice`,
                  editable: true,
                  option: 'customEdit',
                  editConfig: {
                    type: 'number',
                    props: {
                      min: 0,
                      precision: '0'
                    }
                  }
                }
              ]
              // tableName: logisticsTableNameMap[this.quotedPriceData.sourcingScenarios]['tableName']
            })
            hideStepFieldCode.push(`${item.stepCode}_untaxedUnitPrice`)
          })
        }
        this.hideStepFieldCode = [...hideStepFieldCode]
      }
      // 找到字段三的索引
      const index = _columnData.findIndex((column) => column.fieldCode === 'totalQty')
      // 将 stepList 插入到字段三之前
      if (index !== -1) {
        _columnData.splice(index, 0, ...dynamicConfig)
      }
      // 将数据插入
      return _columnData
    },
    groupColumns(columns, obj) {
      const result = []
      const groupMap = new Map()

      obj.forEach((group) => {
        groupMap.set(group.fieldGroup, group.fields)
      })
      const addedFieldCodes = new Set()
      columns.forEach((column) => {
        const groupEntry = Array.from(groupMap.entries()).find(([_, fields]) =>
          fields.some((field) => field.fieldCode === column.fieldCode)
        )
        if (groupEntry) {
          const [groupName] = groupEntry
          let groupItem = result.find((item) => item.fieldName === groupName)
          if (!groupItem) {
            groupItem = { headerName: groupName, fieldName: groupName, children: [] }
            result.push(groupItem)
          }
          groupItem.children.push({ ...column })
          addedFieldCodes.add(column.fieldCode)
        } else {
          result.push({ ...column })
        }
      })

      // 过滤掉已添加到分组中的字段
      return result.filter((item) => !addedFieldCodes.has(item.fieldCode))
    },
    setFixdColumns(columnData) {
      let _preColumns = [
        {
          fieldCode: 'rowIndex',
          fieldName: this.$t('序号')
        },
        {
          fieldCode: 'abateReason',
          fieldName: this.$t('议价理由')
        },
        {
          fieldCode: `priceStatus`,
          fieldName: this.$t('状态')
        }
      ]
      let _columnData = _preColumns.concat(columnData)
      _columnData.push(
        {
          fieldCode: 'bidTimes',
          fieldName: this.$t('次数')
        },
        {
          fieldCode: 'roundNo',
          fieldName: this.$t('轮次')
        }
      )
      return _columnData
    },
    // 表头 - 工具 - 判断是否需要选择框
    isSelectControl() {
      // 历史报价 不显示select框
      // 报价详情、定标、提交定点、已完成状态 不显示select框
      if (this.moduleType === 1 || [31, 34, 20, 100].includes(this.detailInfo.transferStatus)) {
        return false
      }
      if (![31, 100].includes(this.detailInfo.transferStatus)) {
        // 非报价、 非完成
        return true
      }
      return false
    },
    // 初始化 - 表格数据
    async initTableData(rules) {
      this.$store.commit('startLoading')
      this.tableData = []
      let params = this.mergeParams(rules)
      const res = await this.$API.rfxQuotationDetails[
        this.moduleType === 1 ? 'queryHistoryBiddingItemRequest' : 'queryBiddingItemDetailRequest'
      ](params).catch(() => {})
      if (res) {
        let records = cloneDeep(res.data?.records || [])
        let list = this.serializeGridList(records)
        this.tableData = [...list]
        this.$store.commit('endLoading')
      }
    },
    // 表格数据 - 拼接请求参数
    mergeParams(rules) {
      let params = {
        defaultRules: [
          {
            condition: 'and',
            field: 'rfx_item.rfxHeaderId',
            operator: 'equal',
            value: this.$route.query.rfxId
          }
        ],
        page: { current: 1, size: 10000 }
      }
      if (rules) params = Object.assign({}, params, rules)
      return params
    },
    // 表格数据 - 表格数据序列化
    serializeGridList(list) {
      //处理密封报价  数据显示
      const resKey = logisticsTableNameMap[this.detailInfo.sourcingScenarios]?.resKey
      list.forEach((e, index) => {
        e.rowIndex = index + 1 //分页后另作处理 TODO
        if (e[resKey]?.dynamicFields) {
          for (let i in e[resKey]?.dynamicFields) {
            e[resKey]?.dynamicFields[i].forEach((item) => {
              const _fieldData = `${item.fieldCode}_fieldData`
              const _fieldUntaxedUnitPrice = `${item.fieldCode}_untaxedUnitPrice`
              e[_fieldData] = item.fieldData
              e[_fieldUntaxedUnitPrice] = item.untaxedUnitPrice
            })
          }
        }
        hiddenFields.forEach((k) => {
          e[k] = e[k] === -999 ? '******' : e[k] //设置密封报价
          if (e['rfxAnnualLogisticsSeaDTO'] && e['rfxAnnualLogisticsSeaDTO'][k]) {
            e['rfxAnnualLogisticsSeaDTO'][k] =
              e['rfxAnnualLogisticsSeaDTO'][k] === -999
                ? '******'
                : e['rfxAnnualLogisticsSeaDTO'][k]
          }
          if (e['annualLogisticsRailwayItemDTO'] && e['annualLogisticsRailwayItemDTO'][k]) {
            e['annualLogisticsRailwayItemDTO'][k] =
              e['annualLogisticsRailwayItemDTO'][k] === -999
                ? '******'
                : e['annualLogisticsRailwayItemDTO'][k]
          }
          if (e['annualLogisticsTrunkItem'] && e['annualLogisticsTrunkItem'][k]) {
            e['annualLogisticsTrunkItem'][k] =
              e['annualLogisticsTrunkItem'][k] === -999
                ? '******'
                : e['annualLogisticsTrunkItem'][k]
          }
        })
        this.hideStepFieldCode.forEach((h) => {
          if (e[h] === -999) {
            e[h] = '******'
          }
        })
        if (e.declinePercent !== '******') {
          //降幅%加密处理
          e.declinePercent = e.declinePercent ? (e.declinePercent * 100).toFixed(2) : ''
        }

        e.drawing = e.fileList ? JSON.stringify(e.fileList) : null //单独处理附件字段
        e.supplierDrawing = e.supplierFileList ? JSON.stringify(e.supplierFileList) : null //单独处理this.$t("供应商附件")字段
        e.stepQuoteName =
          e.itemStageList && e.itemStageList.length > 0 ? JSON.stringify(e.itemStageList) : null //单独处理阶梯报价
        e.stepNum = e.stepValue
        let _subItems = e['childItems']
        if (Array.isArray(_subItems)) {
          this.serializeGridList(_subItems)
        }
      })
      list = addRowSpan(list)
      return list
    },
    // 初始化 - 字典数据
    async initDictItems() {
      let codeList = [
        { code: 'TradeClause', type: 'string' },
        { code: 'DELIVERY_PLACE', type: 'string' },
        { code: 'TransportMode', type: 'string' },
        { code: 'START-PORT', type: 'string' },
        { code: 'DESTINATION-PORT', type: 'string' },
        { code: 'LOGISTICS_ANNUAL_POL_QUOTE_UNIT', type: 'string' },
        { code: 'LOGISTICS_DEMAND_TYPE', type: 'string' }
      ]
      await this.$API.masterData.getNumDictListAllByCode(codeList).then((res) => {
        if (res.code === 200) {
          this.dictItems = res.data
        }
      })
    },
    // 初始化 - 税率列表
    async initTaxList() {
      const res = await this.$API.masterData.queryAllTaxItem().catch(() => {})
      if (res) {
        this.taxList = res.data
      }
    },
    // 初始化 - 币种编码
    async initCurrencyList() {
      const res = await this.$API.masterData.queryAllCurrency().catch(() => {})
      if (res) {
        this.currencyList = res.data
      }
    },
    // 初始化 - 采购单位
    async initPurUnitList() {
      this.$API.masterData.pagedQueryUnit().then((res) => {
        this.purUnitList = res?.data?.records || [] // FIXME 采购单位和基本单位的区别
      })
    }
  }
}
