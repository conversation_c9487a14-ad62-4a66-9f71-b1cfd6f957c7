<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :current-tab="currentTab"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @handleSelectTab="handleSelectTab"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import { Query } from '@syncfusion/ej2-data'
import Vue from 'vue'
import { pageConfig, hiddenFields } from './config'
import { utils } from '@mtech-common/utils'
import { columnData as logisticsColumnData } from '@/views/common/columnData/rfxItemLogistics'
import { columnData as biddingItemLogisticsColumnData } from '@/views/common/columnData/biddingItemLogistics'
import { columnData as itemDieMapColumnData } from '@/views/common/columnData/biddingItemDie'
import { columnData as itemExtMapColumnData } from '@/views/common/columnData/itemExtMap'
import { getFields as getSBIFields } from '@/views/common/columnData/supplierBiddingItem'
import { createEditInstance } from '@/utils/ej/dataGrid'
import cellFileView from 'COMPONENTS/NormalEdit/cellFileView' // 单元格附件查看
import cellFileViewSupply from 'COMPONENTS/NormalEdit/cellFileViewSupply' // 单元格上传
import cloneDeep from 'lodash/cloneDeep'
export default {
  inject: ['reload'],
  props: {
    fieldDefines: {
      type: Array,
      default: () => []
    },
    childFields: {
      type: Array,
      default: () => []
    },
    fieldStructures: {
      type: Array,
      default: () => []
    },
    detailInfo: {
      type: Object,
      default: () => {}
    },
    hasCountDown: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      pageConfig: pageConfig(
        this.detailInfo.transferStatus,
        this.pointMode,
        this.$route?.query?.source,
        this.$route?.query?.rfxId,
        this.detailInfo.finishAbate
      ),
      rfxHeaderId: '', // 需要传参
      showDialog: false,
      rfxId: this.$route.query.rfxId, // 需要传参
      dataArr1: [],
      dataArr2: [],
      modelarr1: [],
      modelarr2: [],
      handleSelectTabShow: 0,
      dictItems: [],
      taxList: [], // 税率编码
      currencyList: [], // 货币名称
      purUnitList: [], // 采购单位
      directionalBargaining: null, //是否定向议价
      nonDirectionalSupNum: null, ////非定向议价供应商数量
      pointMode: '', //控制提交定点出现
      priceClassificationList: [
        { text: this.$t('暂估价格'), value: 'predict_price' },
        { text: this.$t('SRM价格'), value: 'srm_price' },
        { text: this.$t('执行价格'), value: 'execute_price' },
        { text: this.$t('基价'), value: 'basic_price' }
      ],
      isStepGrid: false,
      currentTab: -1
    }
  },
  async created() {
    if (this.$route.query?.source && this.$route.query?.rfxId) {
      this.$API.strategyConfig
        .findByRfxId({
          sourcingMode: this.$route.query.source,
          rfxId: this.$route.query.rfxId
        })
        .then((res) => {
          this.directionalBargaining = res.data.strategyConfigList[0].directionalBargaining
          this.nonDirectionalSupNum = res.data.strategyConfigList[0].nonDirectionalSupNum
          this.pointMode = res.data.strategyConfigList[0].pointMode
          console.log(69, res.data.strategyConfigList[0].pointMode)
        })
    }

    await this.initDictItems()
    await this.initTaxList()
    await this.initCurrencyList()
    await this.initPurUnitList()
    this.queryRfxConfig()
  },
  mounted() {
    // 定标,提交定点,已完成状态 禁止操作
    let status = [34, 20, 100]
    // 已完成和关闭禁止操作
    if (
      this.detailInfo.status === 8 ||
      this.detailInfo.status === -1 ||
      status.includes(this.detailInfo.transferStatus)
    ) {
      let _tempToolbar = this.pageConfig[0].toolbar.map((v) => ({
        ...v,
        visibleCondition: () => false
      }))
      delete _tempToolbar.find((e) => e.id == 'export').visibleCondition
      this.pageConfig[0].toolbar = _tempToolbar
    }
    const statusList = [20, 32, 33, 34, 37, 100]
    if (
      this.$route?.query?.source !== 'rfq' &&
      statusList.includes(this?.detailInfo?.transferStatus)
    ) {
      this.pageConfig[0].toolbar.push({
        id: 'signPreview',
        icon: 'icon_table_filter',
        title: this.$t('开标一览表')
      })
      this.pageConfig[0].toolbar.push({
        id: 'gradePreview',
        icon: 'icon_table_filter',
        title: this.$t('评分一览表')
      })
    }
    // 招投标和竞价删除结束报价
    if (this.$route?.query?.source !== 'rfq') {
      this.pageConfig[0].toolbar.forEach((v, i) => {
        if (v.id == 'endAccept') {
          this.pageConfig[0].toolbar.splice(i, 1)
        }
      })
    }
    this.setToolbar(this.hasCountDown)
  },
  activated() {
    this.currentTab = 0
  },
  deactivated() {
    this.currentTab = -1
  },
  methods: {
    // setToolbar
    setToolbar(hasCount) {
      let _tempToolbar = cloneDeep(this.pageConfig[0].toolbar)
      if (this.detailInfo.transferStatus === 32) {
        if (hasCount) {
          let _toolbar = _tempToolbar.map((v) => ({
            ...v,
            visibleCondition: () =>
              v.id === 'bargaining' || (v.id === 'endAccept' && this.detailInfo.finishAbate)
                ? true
                : false
          }))
          this.pageConfig[0].toolbar = _toolbar
        } else {
          this.pageConfig[0].toolbar = _tempToolbar
        }
      }
    },
    // 初始化字典数据
    async initDictItems() {
      const tasks = [
        'TradeClause', // 贸易条款 tradeClauseNameData
        'DELIVERY_PLACE', // 直送地 deliveryPlaceData
        'TransportMode', // 物流方式 shippingMethodNameData
        'START-PORT', // 起始港
        'DESTINATION-PORT' // 目的港
      ].map((dictCode) => this.$API.masterData.dictionaryGetList({ dictCode }))
      const result = await Promise.all(tasks).catch((err) => {
        console.error(err)
      })
      if (!result) {
        return
      }
      this.dictItems = result.map((e) => e.data).flat()
    },
    async initTaxList() {
      const res = await this.$API.masterData.queryAllTaxItem().catch(() => {})
      if (res) {
        this.taxList = res.data
      }
    },
    // 币种编码
    async initCurrencyList() {
      const res = await this.$API.masterData.queryAllCurrency().catch(() => {})
      if (res) {
        this.currencyList = res.data
      }
    },
    // 采购单位
    async initPurUnitList() {
      this.$API.masterData.pagedQueryUnit().then((res) => {
        this.purUnitList = res?.data?.records || [] // FIXME 采购单位和基本单位的区别
      })
    },
    show() {
      this.$dialog({
        modal: () => import('../../../components/comparativePriceDialog.vue'),
        data: {
          rfxId: this.$route.query.rfxId,
          detailInfo: this.detailInfo
        }
      })
    },
    hide() {
      this.$refs.toast.ejsRef.hide()
    },
    handleSelectTab(e) {
      this.handleSelectTabShow = e
    },
    queryRfxConfig() {
      this.initConfig(0, this.$API.rfxQuotationDetails.queryBiddingItemDetail)
      this.initConfig(1, this.$API.rfxQuotationDetails.queryHistoryBiddingItem)
    },
    initConfig(index, url) {
      if (Array.isArray(this.fieldDefines) && this.fieldDefines.length) {
        let arr = this.defineGridColumns(utils.cloneDeep(this.fieldDefines))
        if (this.detailInfo.rfxGeneralType === 2) {
          const priceUnitField = ['priceUnitName', 'biddingItemDTO.priceUnitName']
          const priceUnitCol = arr.find((item) => priceUnitField.includes(item.field))
          if (priceUnitCol) {
            priceUnitCol.valueAccessor = (field, data) => {
              // 非采
              const purUnitNameDataSource = [
                { text: this.$t('元'), value: '1' },
                { text: this.$t('万元'), value: '0.0001' }
              ]
              const item = purUnitNameDataSource.find((el) => el.value === data.priceUnitName)
              return item ? item.text : ''
            }
          }
          this.$set(this.pageConfig[0].grid, 'allowPaging', false) // 报价详情不支持分页
        }
        // 添加isPrimaryKey
        arr.push({
          field: 'id',
          width: 0,
          isPrimaryKey: true,
          visible: false
        })
        this.pageConfig[index].grid.columnData = arr
      }
      if (Array.isArray(this.childFields) && this.childFields.length) {
        let arr = this.defineGridColumns(utils.cloneDeep(this.childFields))
        // arr.shift() //子级数据 不允许勾选
        // 子集样式优化
        let _w = 100
        arr.forEach((item) => {
          let _itemW = item.width || '120'
          _w += Number(_itemW)
        })

        this.$set(this.pageConfig[index].grid, 'detailTemplate', function () {
          return {
            template: Vue.component('detailTemplate', {
              template: `<div style="padding:10px 0;width:${_w}px;">
                          <mt-template-page
                            ref="childRef"
                            :template-config="childTable"
                          ></mt-template-page>
                        </div>`,
              data: function () {
                return {
                  data: {},
                  childTable: [
                    {
                      grid: {
                        height: 'auto',
                        lineIndex: true,
                        allowPaging: false,
                        dataSource: [],
                        columnData: arr,
                        class: 'pe-edit-grid custom-toolbar-grid'
                      }
                    }
                  ]
                }
              },
              mounted() {
                let data = this.data
                console.log('detail-table', data)
                setTimeout(() => {
                  this.childTable[0].grid.dataSource = data?.childItems
                }, 0)
              }
            })
          }
        })
      }
      this.$set(this.pageConfig[index].grid, 'asyncConfig', {
        url,
        defaultRules: [
          {
            condition: 'and',
            field: 'rfx_item.rfxHeaderId',
            operator: 'equal',
            value: this.$route.query.rfxId
          }
        ],
        serializeList: (list) => {
          this.serializeGridList(list)
          return list
        }
      })
    },

    serializeGridList(list) {
      //处理密封报价  数据显示
      const { currentPage, pageSize } =
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.pageSettings
      list.forEach((e, index) => {
        e.index = (currentPage - 1) * pageSize + index
        hiddenFields.forEach((k) => {
          e[k] = e[k] === -999 ? '******' : e[k] //设置密封报价
        })
        if (e.declinePercent !== '******') {
          //降幅%加密处理
          e.declinePercent = e.declinePercent ? (e.declinePercent * 100).toFixed(2) : ''
        }

        e.drawing = e.fileList ? JSON.stringify(e.fileList) : null //单独处理附件字段
        e.supplierDrawing = e.supplierFileList ? JSON.stringify(e.supplierFileList) : null //单独处理"供应商附件"字段
        e.stepQuoteName =
          e.itemStageList && e.itemStageList.length > 0 ? JSON.stringify(e.itemStageList) : null //单独处理阶梯报价
        e.stepNum = e.stepValue
        let _subItems = e['childItems']
        if (Array.isArray(_subItems)) {
          this.serializeGridList(_subItems)
        }

        // 添加成本分析操作列
        if (e.costModelName && e.costModelQuote === 1) {
          e.costAnalysis = this.$t('成本分析')
        }
      })
    },
    // 列字段配置
    defineGridColumns(fieldDefines) {
      let arr = [
        // {
        //   width: '50',
        //   type: 'checkbox'
        // },
        {
          field: 'abateReason',
          headerText: this.$t('议价理由'),
          width: '120'
        },
        {
          field: `priceStatus`,
          headerText: this.$t('状态'),
          width: '120',
          valueConverter: {
            type: 'map',
            map: {
              1: this.$t('未审核'),
              2: this.$t('已审核'),
              3: this.$t('已拒绝'),
              4: this.$t('已议价'),
              5: this.$t('已中标'),
              6: this.$t('待定点'),
              7: this.$t('议价中')
            }
          }
        }
      ]
      const editInstance = createEditInstance()
      const rfxItemLogisticsColumn = logisticsColumnData({
        prefix: 'itemLogisticsResponse.',
        dictItems: this.dictItems,
        handleSelectChange: () => {}
      })
      const biddingItemLogisticsColumn = biddingItemLogisticsColumnData({
        prefix: 'biddingItemLogisticsResponse.',
        dictItems: this.dictItems,
        handleSelectChange: () => {}
      })
      const itemDieMapColumn = itemDieMapColumnData({
        prefix: 'itemDieResponse.',
        handleSelectChange: () => {}
      })
      const itemExtMapColumn = itemExtMapColumnData({
        prefix: 'itemExtMap.',
        dictItems: this.dictItems,
        handleSelectChange: () => {}
      })

      const sBIFields = getSBIFields({
        currencyList: this.currencyList,
        taxList: this.taxList,
        purUnitList: this.purUnitList
      })

      if (sBIFields.bidPurUnitName?.editConfig) {
        const dataSource = this.purUnitList
        sBIFields.bidPurUnitName.editConfig = {
          type: 'select',
          valueConvert: (val, { options, column }) => {
            const dataSource = options.dataSource || []
            const row = dataSource.find((e) => e.id === val)
            const purUnitName = row?.unitName || ''
            editInstance.setValueByField(column.field, purUnitName)
            return purUnitName
          },
          props: {
            ...sBIFields.bidPurUnitName.editConfig.props,
            fields: { value: 'id', text: '__text' },
            created: () => {
              let row
              const bidPurUnitCode = editInstance.getValueByField('biddingItemDTO.bidPurUnitCode')
              const bidPurUnitName = editInstance.getValueByField('biddingItemDTO.bidPurUnitName')
              if (bidPurUnitCode) {
                row = dataSource.find((e) => e.unitCode === bidPurUnitCode)
              } else {
                row = dataSource.find((e) => e.unitName === bidPurUnitName)
              }
              if (row) {
                setTimeout(() => {
                  editInstance.setValueByField('biddingItemDTO.bidPurUnitName', row.id)
                }, 0)
              }
            }
          }
        }
      }
      fieldDefines.forEach((e2) => {
        if (e2.fieldCode === 'lineNo') return //行号已用‘序号’处理
        let name = ''
        if (e2.tableName == 'rfx_item_ext') {
          name = 'itemExtMap'
          const field = `${name}.${e2.fieldCode}`
          // 通用信息显示
          const defColumn = itemExtMapColumn.find((e) => e.field === field)
          arr.push({
            field,
            headerText: this.$t(e2.fieldName),
            width: '120',
            editConfig: defColumn?.editConfig,
            formatter: defColumn?.formatter
          })
        } else if (e2.tableName == 'rfx_item_die') {
          name = 'itemDieResponse'
          const field = `${name}.${e2.fieldCode}`
          // 模具信息显示
          const defColumn = itemDieMapColumn.find((e) => e.field === field)
          arr.push({
            field,
            headerText: this.$t(e2.fieldName),
            width: '120',
            editConfig: defColumn?.editConfig,
            formatter: defColumn?.formatter
          })
        } else if (e2.tableName == 'rfx_item_logistics') {
          name = 'itemLogisticsResponse'
          const field = `${name}.${e2.fieldCode}`
          // 物流信息显示
          const defColumn = rfxItemLogisticsColumn.find((e) => e.field === field)
          arr.push({
            field,
            headerText: this.$t(e2.fieldName),
            width: '120',
            editConfig: defColumn?.editConfig,
            formatter: defColumn?.formatter
          })
        } else if (e2.tableName == 'rfx_bidding_item_logistics') {
          name = 'biddingItemLogisticsResponse'
          const field = `${name}.${e2.fieldCode}`
          // 物流信息显示
          const defColumn = biddingItemLogisticsColumn.find((e) => e.field === field)
          arr.push({
            field,
            headerText: this.$t(e2.fieldName),
            width: '120',
            editConfig: defColumn?.editConfig,
            formatter: defColumn?.formatter
          })
        } else if (e2.tableName === 'rfx_bidding_item') {
          name = ''
          const row = {
            formatter: sBIFields?.[e2.fieldCode]?.formatter,
            editConfig: sBIFields?.[e2.fieldCode]?.editConfig,
            field: e2.fieldCode,
            headerText: this.$t(e2.fieldName),
            allowEditing: ['allocationQuantity'].includes(e2.fieldCode) ? true : false,
            width: '200'
          }
          const readonly = !row.allowEditing
          row.edit = sBIFields?.[e2.fieldCode]
            ? editInstance.create({
                getEditConfig: () => ({
                  type: sBIFields?.[e2.fieldCode]?.editConfig.type,
                  ...sBIFields?.[e2.fieldCode]?.editConfig.props,
                  readonly,
                  enabled: readonly
                })
              })
            : undefined
          arr.push(row)
        } else if (e2.tableName === 'rfx_file') {
          arr.push({
            field: e2.fieldCode,
            headerText: this.$t(e2.fieldName),
            template: function () {
              return {
                template: e2.fieldCode === 'supplierDrawing' ? cellFileViewSupply : cellFileView
              }
            },
            editTemplate: function () {
              return {
                template: e2.fieldCode === 'supplierDrawing' ? cellFileViewSupply : cellFileView
              }
            },
            width: 150,
            allowEditing: false
          })
        } else {
          arr.push({
            field: e2.fieldCode,
            headerText: this.$t(e2.fieldName),
            width: '120'
          })
        }
      })
      arr.push(
        {
          field: 'bidTimes',
          headerText: this.$t('次数'),
          width: '90'
        },
        {
          field: 'roundNo',
          headerText: this.$t('轮次'),
          width: '90'
        }
      )
      let that = this
      arr.forEach((e) => {
        if (e.field === 'priceClassification') {
          e.allowEditing = false
          e.editType = 'dropdownedit'
          e.formatter = ({ field }, item) => {
            // const cellVal = item[field];
            let cellVal = ''
            if (this.detailInfo.priceClassification) {
              cellVal = this.detailInfo.priceClassification
              item[field] = this.detailInfo.priceClassification
            } else {
              cellVal = ''
              item[field] = ''
            }
            return this.priceClassificationList.find((e) => e.value === cellVal)?.text
          }
          e.edit = {
            params: {
              dataSource: this.priceClassificationList,
              fields: { value: 'value', text: 'text' },
              query: new Query()
            }
          }
        } else if (
          ['costModelName', 'costModelId', 'costModelVersionCode', 'costModelCode'].includes(
            e.field
          )
        ) {
          e.width = 0
        } else if (e.field == 'costModelQuote') {
          e.valueConverter = {
            type: 'map',
            map: { 0: this.$t('否'), 1: this.$t('是') }
          }
        } else if (e.field == 'eliminateFlag') {
          e.valueConverter = {
            type: 'map',
            map: { 0: this.$t('否'), 1: this.$t('是') }
          }
        } else if (e.field === 'stepQuote') {
          e.formatter = function ({ field }, item) {
            const cellVal = item[field]
            return cellVal === 1 ? that.$t('是') : that.$t('否')
          }
          e.allowEditing = false
          e.valueConverter = {
            type: 'map',
            map: {
              0: this.$t('否'),
              1: this.$t('是')
            }
          }
        } else if (e.field === 'stepQuoteType') {
          e.formatter = function ({ field }, item) {
            const cellVal = item[field]
            return cellVal === 0
              ? this.$t('数量累计阶梯')
              : cellVal === 3
              ? this.$t('数量逐层阶梯')
              : ''
          }
          e.allowEditing = false
          e.valueConverter = {
            type: 'map',
            map: {
              0: this.$t('数量累计阶梯'),
              3: this.$t('数量逐层阶梯')
            }
          }
        } else if (e.field === 'costAnalysis') {
          e.cssClass = 'field-content'
        }
      })
      return arr
    },
    //表格按钮-点击事件
    handleClickToolBar(e) {
      let records = []
      e.gridRef.dataSource.forEach((item) => {
        if (item.customChecked) {
          records.push(item)
        }
      })
      const _selectGridRecords = this.serializeSaveParams(records)
      if (e.toolbar.id == 'bargaining') {
        //议价
        if (this.directionalBargaining !== 0 && _selectGridRecords.length < 1) {
          this.$toast({
            content: this.$t('当前为定向议价，请先选择一行'),
            type: 'warning'
          })
          return
        }
        this.bargaining(_selectGridRecords)
      } else if (e.toolbar.id == 'accept') {
        if (_selectGridRecords.length < 1) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        this.accept(_selectGridRecords)
        //退回比价
      } else if (e.toolbar.id == 'toPricing') {
        this.toPricing()
      } else if (e.toolbar.id == 'relative') {
        this.show()
      } else if (e.toolbar.id == 'endAccept') {
        this.handleEndAccept()
      } else if (e.toolbar.id == 'startNewRound') {
        this.handleStartNewRound()
      } else if (e.toolbar.id == 'submit') {
        if (_selectGridRecords.length < 1) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        } else if (this.pointMode != 2) {
          this.$toast({
            content: this.$t('此条单据不可提交定点'),
            type: 'warning'
          })
        } else {
          let rfxPricingDataSourceId = _selectGridRecords.filter((e) => e.id).map((e) => e.id)
          console.log(337, rfxPricingDataSourceId)
          this.$API.comparativePrice.commitNewPointById(rfxPricingDataSourceId).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('提交成功'),
                type: 'success'
              })
            }
          })
        }
      } else if (e.toolbar.id == 'signPreview') {
        this.$emit('preview')
      } else if (e.toolbar.id == 'gradePreview') {
        this.gradePreview()
      } else if (e.toolbar.id == 'export') {
        const queryBuilderRules =
          this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        const params = {
          page: { current: 1, size: 10000 },
          ...queryBuilderRules,
          defaultRules: [
            {
              condition: 'and',
              field: 'rfx_item.rfxHeaderId',
              operator: 'equal',
              value: this.rfxId
            }
          ].concat(queryBuilderRules.rules || [])
        } // 筛选条件
        this.$API.rfxQuotationDetails.excelExport(params).then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
      } else if (e.toolbar.id == 'batchVmi') {
        if (_selectGridRecords.length < 1) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        this.handleBatchVmi(_selectGridRecords)
      }
    },
    serializeSaveParams(list) {
      let res = []
      const _list = cloneDeep(list)
      _list.forEach((e) => {
        let _sub = e?.childItems
        delete e.childItems
        res.push(e)
        if (Array.isArray(_sub)) {
          res = res.concat(_sub)
        }
      })
      return res
    },

    //单元格icons点击事件
    handleClickCellTool() {},
    //单元格标题点击操作
    handleClickCellTitle(e) {
      const { field, data } = e
      if (field === 'costAnalysis') {
        this.$router.push({
          name: 'purchase-cost',
          query: {
            rfxId: this.$route.query.rfxId,
            biddingItemId: data.id,
            costModelId: data.costModelId,
            refreshId: Date.now() // 每次进入页面进行数据更新
          }
        })
      }
    },
    // 审核通过（审核通过）
    accept(data) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认审核通过？')
        },
        success: () => {
          let rfxBiddingItemIdList = []
          data.forEach((e) => {
            rfxBiddingItemIdList.push(e.id)
            if (Array.isArray(e?.childItems)) {
              //接受报价时，如果有子级数据，将子级数据，遍历追加
              e.childItems.forEach((f) => {
                rfxBiddingItemIdList.push(f.id)
              })
            }
          })
          this.$API.comparativePrice
            .accept({ rfxId: this.rfxId, rfxBiddingItemIdList })
            .then(() => {
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData()
            })
        }
      })
    },
    //议价
    bargaining(e) {
      this.$dialog({
        modal: () => import('./components/bargaingDialog.vue'),
        data: {
          title: this.directionalBargaining == 0 ? this.$t('非定向议价') : this.$t('定向议价'),
          e
        },
        success: (data) => {
          let _ids = []
          e.forEach((f) => {
            _ids.push(f.id)
          })
          let itemGroupIds = []
          e.forEach((item) => {
            item.itemGroupId && itemGroupIds.push(item.itemGroupId)
          })
          let itemGroupIdList = Array.from(new Set(itemGroupIds))
          this.$API.comparativePrice
            .abate({
              rfxId: this.rfxId,
              ...data,
              abateEndDateTime: Number(data.abateEndDateTime), // 议价截止时间 时间戳
              rfxBiddingItemIds: _ids,
              itemGroupIdList
            })
            .then((r) => {
              this.dataArr1 = r.data
              this.$toast({ content: this.$t('议价成功'), type: 'success' })
              this.$parent.$refs.topInfoRef.getCurrentTurnsInfo()
              this.$refs.templateRef.refreshCurrentGridData()
              this.setToolbar(true)
            })
        }
      })
    },
    // 转定价
    toPricing() {
      this.$API.comparativePrice.toPricing({ rfxId: this.rfxId }).then(() => {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        this.reload()
      })
    },
    //开启下一轮
    handleStartNewRound() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t("确认执行'发起新一轮'操作？")
        },
        success: () => {
          const query = { rfxId: this.$route.query.rfxId }
          this.$store.commit('startLoading')
          this.$API.comparativePrice.startNewRound(query).then(() => {
            this.$store.commit('endLoading')
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    // 结束报价--执行结束之前做校验
    handleEndAccept() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t("确认执行'结束报/议价'？")
        },
        success: () => {
          //结束议价
          if (this.detailInfo.finishAbate) {
            this.endAbate()
            return
          }
          // 结束报价
          const query = { rfxId: this.$route.query.rfxId }
          this.$API.inquiryBidding.checkIsCanAutoCloseRfx(query).then((res) => {
            if (res.data) {
              this.$dialog({
                data: {
                  title: this.$t('提示'),
                  message: this.$t('报价供应商不足，提前结束会关闭询价单，确认是否提前结束？')
                },
                success: () => {
                  this.endAccept()
                }
              })
            } else {
              this.endAccept()
            }
          })
        }
      })
    },
    // 结束报价
    endAccept() {
      const query = { id: this.$route.query.rfxId }
      this.$store.commit('startLoading')
      this.$API.inquiryBidding.currentTurnFinish(query).then(() => {
        this.$store.commit('endLoading')
        this.$toast({
          content: this.$t("'结束报价'，操作成功"),
          type: 'success'
        })
        this.reload()
        // location.reload();
      })
    },
    // 结束议价
    endAbate() {
      const query = { id: this.$route.query.rfxId }
      this.$store.commit('startLoading')
      this.$API.inquiryBidding.endAbate(query).then(() => {
        this.$store.commit('endLoading')
        this.$toast({
          content: this.$t("'结束议价'，操作成功"),
          type: 'success'
        })
        this.reload()
      })
    },
    // 通过标的物搜索
    change1(e) {
      this.pageConfig[0].grid.asyncConfig.defaultRules[0].rfx_item = e.value
    },
    // 通过gong供应商搜索
    change2(e) {
      this.pageConfig[0].grid.asyncConfig.defaultRules[0].rfx_bidding_item = e.value
    },
    /**
     * 评分一览表
     */
    async gradePreview() {
      let buffer = await this.$API.rfxPricing
        .previewGrade({
          rfxCode: this.detailInfo.rfxCode,
          tableType: 2
        })
        .catch(() => {})
      buffer?.data && (buffer = buffer.data)
      if (buffer instanceof Blob) {
        if (buffer.type === 'application/json') {
          try {
            const json = JSON.parse(await buffer.text())
            if (json.msg) {
              this.$toast({ content: json.msg, type: 'warning' })
            }
          } catch (error) {
            console.warn(error)
          }
        } else if (buffer.type === 'text/html') {
          const url = URL.createObjectURL(buffer)
          window.open(url)
        }
      }
    },
    // 批量VMI标识
    handleBatchVmi(list) {
      const rfxBiddingItemIds = []
      list.forEach((item) => item.id && rfxBiddingItemIds.push(item.id))
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认执行“批量VMI标识”操作？')
        },
        success: () => {
          this.$API.comparativePrice
            .batchSetVmi({
              rfxId: this.rfxId,
              rfxBiddingItemIds
            })
            .then(() => {
              this.$toast({ content: this.$t('操作成功！'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
            })
        }
      })
    }
  }
}
</script>
<style lang="scss">
.form-design {
  padding-left: 10px;
  background: #fff;
}
.e-dlg-container > .right-wrapper {
  height: 100% !important;
  max-height: 100% !important;
  width: 95% !important;
  .dialog-panel {
    .panel-title {
      font-size: 16px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(41, 41, 41, 1);
      margin-bottom: 18px;
      &.price-title {
        margin-bottom: 0;
      }
    }
    .full-width {
      width: 100% !important;
    }
  }
}
</style>
<style lang="scss" scoped>
.normal-class {
  font: #000;
}
.full-height {
  /deep/.mt-tabs {
    background: #fff;
  }
  /deep/ .tab-container {
    background: #fff !important;
  }
  /deep/.e-grid .e-detailrowcollapse:not(.e-editedbatchcell):not(.e-updatedtd) {
    vertical-align: middle;
  }
  /deep/ .e-grid .e-detailrowexpand:not(.e-editedbatchcell):not(.e-updatedtd) {
    vertical-align: middle;
  }
  /deep/ .e-table {
    border-left: 1px solid #e0e0e0;
    .e-emptyrow {
      td {
        border: 1px solid #e0e0e0;
        border-top: none;
      }
    }
  }
  /deep/ .e-detailindentcell {
    border-right: none;
  }
  /deep/ .e-detailcell {
    .toolbar-container {
      height: 30px;
    }
  }
}
</style>
