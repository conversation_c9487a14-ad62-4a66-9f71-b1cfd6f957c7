import { i18n, permission, md5 } from '@/main.js'
const todoListToolBar = (transferStatus, pointMode, finishAbate) => [
  {
    id: 'bargaining',
    icon: 'icon_solid_edit',
    title: i18n.t('议价'),
    visibleCondition: () => {
      return ![31, 100].includes(transferStatus) //已完成 & 没结束报价不操作
    }
  },
  {
    id: 'accept',
    icon: 'icon_solid_edit',
    title: i18n.t('审核通过'),
    visibleCondition: () => {
      return ![31, 100].includes(transferStatus) //已完成 & 没结束报价不操作
    }
  },
  {
    id: 'toPricing',
    icon: 'icon_solid_edit',
    title: i18n.t('转定价'),
    visibleCondition: () => {
      return ![31, 100].includes(transferStatus) //已完成
    }
  },
  {
    id: 'relative',
    icon: 'icon_solid_edit',
    title: i18n.t('比价助手'),
    visibleCondition: () => {
      return ![31].includes(transferStatus)
    }
  },
  {
    id: 'submit',
    icon: 'icon_solid_edit',
    title: i18n.t('提交定点推荐'),
    visibleCondition: () => {
      return pointMode != 2 && ![20, 31, 100].includes(transferStatus)
    }
  }, // 530版本  暂时隐藏
  {
    id: 'endAccept',
    icon: 'icon_solid_edit',
    title: i18n.t('结束报/议价'),
    visibleCondition: () => {
      return transferStatus === 31 || transferStatus === 36 || finishAbate //报价中   投标中
    }
  },
  {
    id: 'export',
    icon: 'icon_solid_export',
    title: i18n.t('导出')
  }
]

const completedListToolBar = []

const completedListColumnData = [
  {
    field: 'businessTypeName',
    headerText: i18n.t('物料编码')
  }
]

//status  定点推荐主表状态 0待处理 3已处理 4已退回
export const pageConfig = (transferStatus = 0, pointMode, source, rfxId, finishAbate) => [
  {
    title: i18n.t('报价详情'),
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    toolbar: todoListToolBar(transferStatus, pointMode, finishAbate),
    gridId: md5(
      permission.gridId['purchase'][source]['detail']['tabs']['quotation']['list'] + rfxId
    ),
    // 注释掉的gridIds 是方便查看md5加密之前的数据
    // gridIds:
    //   permission.gridId["purchase"][source]["detail"]["tabs"]["quotation"][
    //     "list"
    //   ] + rfxId,
    grid: {
      customSelection: true,
      showSelected: false,
      allowFiltering: true,
      lineIndex: true,
      columnData: [],
      virtualPageSize: 30,
      enableVirtualization: true,
      // 最高支持5000
      pageSettings: {
        currentPage: 1,
        pageSize: 1000,
        pageSizes: [20, 50, 100, 200, 1000],
        totalRecordsCount: 0
      }
    }
  },
  {
    title: i18n.t('历史报价'),
    toolbar: completedListToolBar,
    gridId: md5(
      permission.gridId['purchase'][source]['detail']['tabs']['quotation']['history'] + rfxId
    ),
    // 注释掉的gridIds 是方便查看md5加密之前的数据
    // gridIds:
    //   permission.gridId["purchase"][source]["detail"]["tabs"]["quotation"][
    //     "history"
    //   ] + rfxId,
    grid: {
      allowFiltering: true,
      lineIndex: true,
      columnData: completedListColumnData
      // dataSource: [{ abateReason: "test-grid-2" }],
    }
  }
]

// e.taxedTotalPrice =
// e.taxedTotalPrice === -999 ? "******" : e.taxedTotalPrice; //含税总价
// e.taxedUnitPrice =
// e.taxedUnitPrice === -999 ? "******" : e.taxedUnitPrice; //含税单价
// e.untaxedTotalPrice =
// e.untaxedTotalPrice === -999 ? "******" : e.untaxedTotalPrice; //未税总价
// e.untaxedUnitPrice =
// e.untaxedUnitPrice === -999 ? "******" : e.untaxedUnitPrice; //未税单价
export const hiddenFields = [
  'taxedTotalPrice', //含税总价
  'taxedUnitPrice', //含税总价
  'untaxedTotalPrice', //未税总价
  'untaxedUnitPrice', //未税单价
  'lastQuoteTaxed', //上次报价（含税）
  'lastQuoteUntaxed', //上次报价（未税）
  'sharePriceTaxed', //模具分摊价格（含税）
  'sharePriceUntaxed', //模具分摊价格（未税）
  'realSharePriceTaxed', //实际模具分摊价格（含税）
  'realSharePriceUntaxed', //实际模具分摊价格（未税）
  'planSharePriceTaxed', //规划模具分摊价格（含税）
  'planSharePriceUntaxed', //规划模具分摊价格（未税）
  'lastSharePriceTaxed', //上次模具分摊价格（含税）
  'lastSharePriceUntaxed', //上次模具分摊价格（未税）
  'lastRealSharePriceTaxed', //上次实际模具分摊价格（含税）
  'lastRealSharePriceUntaxed', //上次实际模具分摊价格（未税）
  'lastPlanSharePriceTaxed', //上次规划模具分摊价格（含税）
  'lastPlanSharePriceUntaxed', //上次规划模具分摊价格（未税）
  'declinePercent', //降幅%
  'totalPrice', //汇总价格
  'twentyGpBaseFee', //基础海运费(20GP)
  'fortyGpBaseFee', //基础海运费(40GP)
  'fortyHqBaseFee', //基础海运费(40HQ)
  'twentyGpBaf', //BAF(20GP)
  'fortyGpBaf', //BAF(40GP)
  'fortyHqBaf', //BAF(40HQ)
  'insurance', //安保费
  'ams', //AMS
  'msc', //MSC
  'css', //CSS
  'cas', //CAS
  'arb', //ARB
  'polThcFee', //起运港THC
  'polDocTlxFee', //起运港DOC/TLX
  'sealFee', //封条费
  'intimidateFee', //打单费用
  'exportServiceFee', //出口服务费
  'shipCertificateFee', //船证费
  'vgm', //VGM
  'sundryFee', //杂费
  'portCharges', //港务费
  'steelLockFee', //钢锁费
  'csc', //CSC
  'plw', //PLW
  'isps', //ISPS
  'podThcFee', //目的港THC费用
  'podDocTlxFee', //目的港DOC/TLX
  'isf', //ISF
  'psf', //PSF
  'thd', //THD
  'ens', //ENS
  'twentyGpPodTrailerFee', //拖车费(20GP)
  'fortyGpPodTrailerFee', //拖车费(40GP)
  'fortyHqPodTrailerFee', //拖车费(40HQ)
  'declareCustomsFee', //报关费
  'usdTotalFeeSum', //总计费用USD
  'cnyTotalFeeSum', //总计费用CNY
  'rentalContainerFee', //租箱费
  'trailerFee', //拖车费
  'replacementStorageFee', //换装入库费
  'domesticRailwayFreightFee', //内贸铁路运费
  'railwayFreightFee', //铁路运费
  'qbjAroundFee', //青白江绕园费
  'socDeclareCustomsFee', //soc报关费
  'socInsurance', //soc保险费
  'cocDeclareCustomsFee', //coc报关费
  'cocInsurance', //coc保险费
  'stationDeclareCustomsFee', //门到站报关费
  'stationInsurance', //门到站保险费
  'totalFee' //总计费用
  // 'vehicleFee', //压车费
  // 'detentionOverSixtyDayFee', //超免箱期60天后费用标准
  // 'returnFee', //返空费
  // 'customsInspectionFee', //海关检查费
  // 'otherFee' //其他费用
]
