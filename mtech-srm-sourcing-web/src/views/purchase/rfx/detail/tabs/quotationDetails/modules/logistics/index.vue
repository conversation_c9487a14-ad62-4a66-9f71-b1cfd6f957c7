<template>
  <div class="flex-full-height">
    <mt-tabs
      class="custom-tab"
      :tab-id="$utils.randomString()"
      :e-tab="false"
      :data-source="tabSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <!-- 报价详情、历史报价公用 -->
    <CustomAgGrid
      ref="CustomAgGrid"
      v-clickoutside="clickoutside"
      :search-config="searchConfig"
      :toolbar="toolbar"
      :columns="columns"
      :suppress-row-transform="true"
      :row-data="tableData"
      :animate-rows="false"
      :is-stop-editing-on-blur="isStopEditingOnBlur"
      :get-row-id="getRowId"
      @handleClickToolbar="handleClickToolbar"
      @onGridReady="onGridReady"
      @row-selected="onRowSelected"
      @refresh="refresh"
      @search="search"
      @cellEditingStopped="cellEditingStopped"
    >
    </CustomAgGrid>
  </div>
</template>

<script>
import CustomAgGrid from '@/components/CustomAgGrid'
import cellFile from '@/components/AgCellComponents/cellFile'
import cellLink from '@/components/AgCellComponents/cellLink'
import MixIn from '../../config/mixin.js'

export default {
  inject: ['reload'],
  mixins: [MixIn],
  components: {
    CustomAgGrid,
    // eslint-disable-next-line
    cellFile,
    // eslint-disable-next-line
    cellLink
  },

  data() {
    return {}
  },
  async created() {},
  mounted() {},
  methods: {
    cellEditingStopped(params) {
      const { colDef, value, data } = params
      if (colDef?.field !== 'biddingItemLogisticsResponse.suggestOpinionRemark') return
      // 保存物流意见
      this.handleSaveSuggestion(value, data)
    },
    handleSaveSuggestion(suggestion, data) {
      let params = {
        logisticSuggestionDetailDTOS: [
          {
            rfxBiddingItemId: data?.id,
            suggestOpinionRemark: suggestion,
            supplierBiddingItemId: data.supplierBiddingItemId
          }
        ],
        rfxId: this.rfxId
      }
      this.$API.rfxQuotationDetails.saveSuggestion(params).then((res) => {
        if (res.code === 200) {
          this.refresh()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-tab /deep/.mt-tabs-container {
  background: #ffffff;
}
</style>
