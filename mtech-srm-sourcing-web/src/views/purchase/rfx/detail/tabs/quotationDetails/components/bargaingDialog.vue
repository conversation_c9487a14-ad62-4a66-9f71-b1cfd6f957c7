<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="rules">
        <mt-form-item prop="abateReason" :label="$t('议价理由')">
          <mt-input
            v-model="formObject.abateReason"
            :multiline="true"
            :rows="1"
            type="text"
            float-label-type="Never"
            :placeholder="$t('请输入议价理由')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="abateEndDateTime" :label="$t('议价截止时间')">
          <mt-date-time-picker
            format="yyyy-MM-dd HH:mm:ss"
            time-stamp
            :open-on-focus="true"
            v-model="formObject.abateEndDateTime"
            float-label-type="Never"
            :placeholder="$t('请选择议价截止时间')"
          ></mt-date-time-picker>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      editStatus: false,
      formObject: {
        abateReason: '',
        abateEndDateTime: ''
      },
      rules: {
        abateReason: [
          {
            required: true,
            message: this.$t('请输入议价理由'),
            trigger: 'blur'
          }
        ],
        abateEndDateTime: [
          {
            required: true,
            message: this.$t('请选择议价截止时间'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.formObject = { ...this.modalData.data }
    }
  },
  methods: {
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = { ...this.formObject }
          if (!this.editStatus) {
            delete params.id
          }
          if (
            this.formObject.abateEndDateTime &&
            this.formObject.abateEndDateTime < new Date().getTime()
          ) {
            this.$toast({
              content: this.$t('议价截止时间,有误.'),
              type: 'warning'
            })
            return
          }
          this.$emit('confirm-function', params)
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/ .div-auth {
    background: #fafafa;
    height: 35px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    color: #0f0f0f;
    font-size: 12px;
    cursor: pointer;
    border-radius: 5px;
  }
}
</style>
