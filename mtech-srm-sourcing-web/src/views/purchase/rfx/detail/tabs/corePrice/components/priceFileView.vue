<template>
  <div class="cell-upload" :id="'cell-upload-' + data.index">
    <!-- reviewPriceFileList:{{ data.reviewPriceFileList }} -->
    <mt-input
      id="reviewPriceFileList"
      style="display: none"
      :value="data.reviewPriceFileList"
    ></mt-input>
    <div
      @click.self="showFileBaseInfo"
      :class="['cell-operable-title', { active: isAvtive(data.reviewPriceFileList) }]"
    >
      {{ data.reviewPriceFileList | listNumFormat }}
    </div>

    <!-- 需求附件弹窗 -->
    <uploader-dialog @change="fileChange" @confirm="setFile" ref="uploaderDialog"></uploader-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { i18n } from '@/main.js'
// 草稿或审批拒绝状态才能再次上传
export default {
  components: {
    UploaderDialog: () => import('COMPONENTS/Upload/uploaderDialog')
  },
  data() {
    return {
      data: {
        // reviewPriceFileList: {},
      },
      uploadFileList: [] // 上传的附件(初始值赋值之前上传过的)
    }
  },
  filters: {
    byteToKB: (value) => Math.floor((value / 1024) * 100) / 100,
    listNumFormat(value) {
      if (value && value.length > 0) {
        return `${i18n.t('查看')}(${JSON.parse(value).length})`
        // return JSON.parse(value).length;
      } else {
        return i18n.t('暂无附件')
      }
    }
  },
  computed: {
    isAvtive() {
      return (value) => {
        return value && value.length > 0
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.data.reviewPriceFileList && this.data.reviewPriceFileList.length) {
        let _uploadFileList = JSON.parse(this.data.reviewPriceFileList)
        // _uploadFileList.forEach((e) => {
        //   delete e.sysFileId
        // })
        this.uploadFileList = _uploadFileList
      }
    })
  },
  methods: {
    showFileBaseInfo() {
      if (!this.uploadFileList.length) return
      const dialogParams = {
        fileData: cloneDeep(this.uploadFileList),
        isView: true, //是否可上传
        required: false, // 是否必须
        title: i18n.t('附件')
      }
      this.$refs.uploaderDialog.dialogInit(dialogParams)
    },

    // 行附件弹窗内文件变动
    fileChange(data) {
      console.log('fileChange', data)
      // this.uploadFileList = data;
    },
    // 点击行附件上传的确认按钮
    setFile() {
      console.log('点击了确认')
    }
  }
}
</script>

<style lang="scss" scoped>
.cell-operable-title {
  display: inline-block;
  padding: 10px;

  color: #00469c;
  font-size: 14px;
  cursor: pointer;
}
</style>
