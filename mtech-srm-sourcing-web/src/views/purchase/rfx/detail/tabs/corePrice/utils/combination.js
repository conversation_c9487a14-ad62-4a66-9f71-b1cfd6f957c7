import { v1 as uuidv1 } from 'uuid'
import { getValueByPath, setValueByPath } from '@/utils/obj'
import { EditConfig } from '@/utils/ej/dataGrid'
import { PRICE_EDIT_CONFIG, MAX_SAFE_INTEGER } from '@/constants/editConfig'
import { Decimal32 } from '@/utils/decimal'

// 母表修改后需要更新子表
// 子表修改后需要更新母表
export const EVENT_GRID_DATA_SOURCE_UPDATE = 'EVENT_GRID_DATA_SOURCE_UPDATE'

export function isCombination(detailInfo) {
  return detailInfo.sourcingObjType === 'combination'
}

// 部分字段不能编辑
export function defineGridColumnsAfter(columnDatas) {
  return columnDatas
}

// 获取母件需求数量
function getParentRow(vm, rfxItemKey) {
  return vm.submitTableData.find((e) => e.rfxItemKey === rfxItemKey)
}

export const editColumn = function (vm, { editInstance }) {
  if (!isCombination(vm.detailInfo)) {
    return []
  }
  editInstance
    .component('ReferSourceCodeCell', () => import('@/components/NormalEdit/ReferSourceCodeCell'))
    .onInput((ctx, { field }) => {
      if (['taxRateValue', 'taxRateCode', 'taxRateName'].includes(field)) {
        calculatePrice(ctx.rowData, (path, val) => {
          ctx.setValueByField(path, val)
        })
      }
    })

  return [
    {
      field: 'selfPurchasing', // 是否自购
      allowEditing: true,
      edit: editInstance.create({
        onInput: (ctx, { value }) => {
          ctx.setOptions('itemDieResponse.untaxedUnitPrice', {
            disabled: !value
          })
        },
        getEditConfig: () => {
          return {
            type: 'select',
            'show-clear-button': true,
            fields: { value: 'value', text: 'text' },
            dataSource: [
              { value: 0, text: vm.$t('否') },
              { value: 1, text: vm.$t('是') }
            ]
          }
        }
      }),
      valueConverter: {
        type: 'map',
        map: { 0: vm.$t('否'), 1: vm.$t('是') }
      }
    },
    // 未税单价
    {
      field: 'itemDieResponse.untaxedUnitPrice',
      edit: editInstance.create({
        onInput: (ctx) => {
          calculatePrice(ctx.rowData, (path, val) => {
            ctx.setValueByField(path, val)
          })
        },
        getEditConfig: ({ rowData }) => {
          return {
            ...PRICE_EDIT_CONFIG,
            type: 'number',
            disabled: !rowData.selfPurchasing
          }
        }
      })
    },
    // 含税单价
    {
      field: 'itemDieResponse.taxedUnitPrice',
      edit: editInstance.create({
        getEditConfig: () => {
          return {
            ...PRICE_EDIT_CONFIG,
            type: 'number',
            disabled: true
          }
        }
      })
    },
    {
      field: 'itemExtMap.requireQuantity',
      // allowEditing: true,
      edit: editInstance.create({
        onInput: (ctx) => {
          calculatePrice(ctx.rowData, (path, val) => {
            ctx.setValueByField(path, val)
          })
        },
        getEditConfig: ({ column, rowData }) => {
          column.allowEditing = !rowData.parentRfxItemKey
          return {
            type: 'number',
            disabled: !!rowData.parentRfxItemKey,
            min: 0,
            max: MAX_SAFE_INTEGER,
            precision: 0
          }
        }
      })
    },
    {
      // 单机用量：数值型，两位小数，手工录入，10位；含义是指一个组合物料中包含的子散件的标准用量；
      field: 'itemExtMap.singleQuantity',
      edit: editInstance.create({
        onInput: (ctx, { field }) => {
          const parentRfxItemKey = ctx.getValueByField('parentRfxItemKey')
          if (!parentRfxItemKey) {
            return
          }
          const pRow = getParentRow(vm, parentRfxItemKey)
          if (!pRow) {
            return
          }
          // 母件需求数量 * 子件单机用量
          const requireQuantity = getValueByPath(pRow, 'itemExtMap.requireQuantity') || 0
          const singleQuantity = ctx.getValueByField(field) || 0
          ctx.setValueByField('itemExtMap.requireQuantity', mul(requireQuantity, singleQuantity))
          calculatePrice(ctx.rowData, (path, val) => {
            ctx.setValueByField(path, val)
          })
        },
        getEditConfig: () => {
          return {
            type: 'number',
            min: 0,
            max: 1e9,
            precision: 2
          }
        }
      })
    },
    {
      field: 'itemExtMap.referChannel',
      valueConverter: {
        type: 'map',
        map: { '-1': vm.$t('无'), 0: vm.$t('手工定价'), 2: vm.$t('价格记录') }
      },
      edit: editInstance.create({
        onInput: (ctx, { field }) => {
          const val = ctx.getValueByField(field)
          const disabled = +val !== 2
          ctx.setOptions('itemExtMap.referSourceCode', {
            disabled
          })

          if (disabled) {
            ctx.setValueByField('itemExtMap.referItemUnitPriceUntaxed', null)
            ctx.setValueByField('itemExtMap.referItemUnitPriceTaxed', null)
            ctx.setValueByField('itemExtMap.referItemRateCode', null)
            ctx.setValueByField('itemExtMap.referItemRateName', null)
            ctx.setValueByField('itemExtMap.referItemRateValue', null)
            ctx.setValueByField('itemExtMap.referItemSupplierId', null)
            ctx.setValueByField('itemExtMap.referItemSupplierCode', null)
            ctx.setValueByField('itemExtMap.referItemSupplierName', null)
            ctx.setValueByField('itemExtMap.referItemCode', null)
            ctx.setValueByField('itemExtMap.referItemName', null)
            ctx.setValueByField('itemExtMap.referItemSpec', null)
            ctx.setValueByField('itemExtMap.referSourceCode', null)
          }
        },
        getEditConfig: () => {
          return {
            type: 'select',
            'show-clear-button': true,
            fields: { value: 'value', text: 'text' },
            dataSource: [
              { value: -1, text: vm.$t('无') },
              { value: 0, text: vm.$t('手工定价') },
              { value: 2, text: vm.$t('价格记录') }
            ]
          }
        }
      })
    },
    //  参考物料相关
    {
      field: 'itemExtMap.referSourceCode',
      width: '190',
      edit: editInstance.create({
        getEditConfig: ({ rowData }) => {
          return {
            type: 'ReferSourceCodeCell',
            disabled: +getValueByPath(rowData, 'itemExtMap.referChannel') !== 2,
            editInstance
          }
        }
      })
    },

    {
      field: 'itemExtMap.referItemQuoteStartDate',
      edit: editInstance.create({
        getEditConfig: EditConfig.readonly
      })
    },

    {
      field: 'itemExtMap.referItemRateName',
      edit: editInstance.create({
        getEditConfig: EditConfig.readonly
      })
    },

    {
      field: 'itemExtMap.referItemRateValue',
      edit: editInstance.create({
        getEditConfig: EditConfig.readonly
      })
    },
    {
      field: 'itemExtMap.referItemRateCode',
      edit: editInstance.create({
        getEditConfig: EditConfig.readonly
      })
    },
    {
      field: 'itemExtMap.referItemSpec',
      edit: editInstance.create({
        getEditConfig: EditConfig.readonly
      })
    },
    {
      field: 'itemExtMap.referItemSupplierName',
      edit: editInstance.create({
        getEditConfig: EditConfig.readonly
      })
    },
    {
      field: 'itemExtMap.referItemSupplierCode',
      edit: editInstance.create({
        getEditConfig: EditConfig.readonly
      })
    },
    {
      field: 'itemExtMap.referItemUnitPriceUntaxed',
      edit: editInstance.create({
        getEditConfig: EditConfig.readonly
      })
    },
    {
      field: 'itemExtMap.referItemUnitPriceTaxed',
      edit: editInstance.create({
        getEditConfig: EditConfig.readonly
      })
    },
    {
      field: 'itemExtMap.referItemName',
      edit: editInstance.create({
        getEditConfig: EditConfig.readonly
      })
    },
    {
      field: 'itemExtMap.referItemCode',
      edit: editInstance.create({
        getEditConfig: EditConfig.readonly
      })
    },
    // 物料材质：文本，手工录入，10位
    {
      field: 'material',
      edit: editInstance.create({
        getEditConfig: () => {
          return {
            type: 'text',
            maxLength: 10
          }
        }
      })
    }
  ]
}

/**
 * @returns true=不继续处理 false=继续处理
 */
export function handleSaveDetailsBefore(vm) {
  // 参考来源选择价格记录 来源单据必填 请选择来源单据
  if (!isCombination(vm.detailInfo)) {
    return false
  }
  for (const row of vm.submitTableData) {
    const referChannel = getValueByPath(row, 'itemExtMap.referChannel')
    const referSourceCode = getValueByPath(row, 'itemExtMap.referSourceCode')
    if (+referChannel === 2 && !referSourceCode) {
      vm.$toast({
        content: vm.$t(`请选择来源单据`),
        type: 'error'
      })
      return true
    }
  }
  return false
}

// 子表挂载
export function detailTemplateMounted({ pVm, sVm, SYMBOL_PK }) {
  if (!isCombination(pVm.detailInfo)) {
    return false
  }
  sVm.$bus.$on(EVENT_GRID_DATA_SOURCE_UPDATE, (event) => {
    if (event.isChildren) {
      // 子表数据更新
    } else {
      // 母表数据更新
      if (event.resetChildItems) {
        if (sVm.data[SYMBOL_PK] === event.data[SYMBOL_PK]) {
          if (Array.isArray(event.data.childItems)) {
            event.data.childItems.forEach((e) => pVm.intergrateEditData(e))
          }
          sVm.childTable[0].grid.dataSource = event.data.childItems
        }
      } else {
        // 需求数量更新 计算需求数量
        if (
          event.data &&
          typeof event.data === 'object' &&
          event.field === 'itemExtMap.requireQuantity' &&
          Array.isArray(sVm.childTable[0].grid.dataSource)
        ) {
          const requireQuantity = Number(
            getValueByPath(event.data, 'itemExtMap.requireQuantity') || 0
          )
          // 母件需求数量 * 子件单机用量
          for (const dataSource of sVm.childTable[0].grid.dataSource) {
            if (typeof dataSource.itemExtMap === 'object' && dataSource.itemExtMap) {
              dataSource.itemExtMap.requireQuantity = mul(
                requireQuantity,
                dataSource.itemExtMap.singleQuantity || 0
              )
              calculatePrice(dataSource, (path, val) => {
                setValueByPath(dataSource, path, val)
              })
            }
          }
        }
      }
    }
  })
}

// 子表销毁
export function detailTemplateBeforeDestroy(vm) {
  if (!isCombination(vm.detailInfo)) {
    return false
  }
  vm.$bus.$off(EVENT_GRID_DATA_SOURCE_UPDATE)
}

// 选择物料
export async function onChangeItemId({ editInstance, busField, data, cloneDeep, bus, self }) {
  if (!isCombination(self.detailInfo)) {
    return false
  }
  const isNewVal =
    editInstance.getValueByField(busField) !== data || !editInstance.getValueByField(busField)
  // 组合物料查询
  if (busField === 'itemId' && isNewVal) {
    if (data) {
      const res = await self.$API.masterData.packItemRelPagedQuery({
        page: {
          current: 1,
          size: 1000
        },
        condition: 'and',
        rules: [
          {
            field: 'parentItemId',
            type: 'number',
            operator: 'equal',
            value: data
          }
        ],
        defaultRules: []
      })
      if (res?.data?.records) {
        editInstance.setValueByField(
          'childItems',
          res.data.records.map(({ itemId, itemCode, itemName, unitCount }) => {
            const cloneRow = cloneDeep(editInstance.rowData)
            return Object.assign(cloneRow, {
              id: undefined,
              addId: Symbol(),
              rfxItemKey: uuidv1(),
              parentRfxItemKey: cloneRow.rfxItemKey,
              selfPurchasing: cloneRow.selfPurchasing || 0,
              itemId,
              itemCode,
              itemName,
              itemExtMap: {
                ...(cloneRow.itemExtMap || {}),
                singleQuantity: unitCount,
                // 需求数量 = 母件需求数量 * 子件单机用量
                requireQuantity: mul(cloneRow?.itemExtMap?.requireQuantity || 0, unitCount)
              }
            })
          })
        )
        bus.$emit(EVENT_GRID_DATA_SOURCE_UPDATE, {
          isChildren: false,
          resetChildItems: true,
          data: editInstance.rowData
        })
      }
    } else {
      editInstance.setValueByField('childItems', [])
      removeChildItems(self, editInstance.getValueByField('rfxItemKey'))
      bus.$emit(EVENT_GRID_DATA_SOURCE_UPDATE, {
        isChildren: false,
        resetChildItems: true,
        data: editInstance.rowData
      })
    }
  }
}

// 删除子件
function removeChildItems(vm, rfxItemKey) {
  if (!rfxItemKey) {
    return
  }
  const ids = []
  for (let i = vm.submitTableData.length - 1; i >= 0; i--) {
    if (
      vm.submitTableData[i].rfxItemKey === rfxItemKey &&
      Array.isArray(vm.submitTableData[i].childItems) &&
      vm.submitTableData[i].childItems.length
    ) {
      vm.submitTableData[i].childItems = []
    }
    if (vm.submitTableData[i].parentRfxItemKey === rfxItemKey) {
      vm.submitTableData[i].id && ids.push(vm.submitTableData[i].id)
      vm.submitTableData.splice(i, 1)
    }
  }
  // 需要调用接口删除
  ids.length &&
    vm.$API.rfxRequireDetail.deleteRFXItem({
      idList: ids,
      rfxId: vm.$route.query.rfxId
    })
}

// 整合编辑数据
export function intergrateEditDataBefore(rowData, vm) {
  if (vm) {
    if (Array.isArray(vm.childTable[0].grid.dataSource)) {
      for (let i = 0; i < vm.childTable[0].grid.dataSource.length; i++) {
        const row = vm.childTable[0].grid.dataSource[i]
        if (row.rfxItemKey === rowData.rfxItemKey) {
          vm.$set(vm.childTable[0].grid.dataSource, i, rowData)
        }
      }
    }
  }
}

// 工具条
export function toolbar(vm, toolbar) {
  if (!isCombination(vm.detailInfo)) {
    return toolbar
  }
  return [
    {
      id: 'Add',
      icon: 'icon_solid_Createorder',
      title: vm.$t('新增'),
      visibleCondition: () => {
        return !vm.submitTableData.length
      }
    },
    ...toolbar.filter((e) => e?.id !== 'Add')
  ]
}

// 价格计算
function calculatePrice(row, setVal) {
  // 单价含税 itemDieResponse.taxedUnitPrice = 单价未税 itemDieResponse.untaxedUnitPrice * (1 + 税率值 taxRateValue)
  const taxRate = getValueByPath(row, 'taxRateValue') || 0 // 税率值
  const untaxedUnitPrice = getValueByPath(row, 'itemDieResponse.untaxedUnitPrice') || 0 // 单价未税
  const taxedUnitPrice = new Decimal32(taxRate).add(1).mul(untaxedUnitPrice).toFixed(2)
  setVal('itemDieResponse.taxedUnitPrice', taxedUnitPrice)
}

function mul(a, b) {
  return new Decimal32(a).mul(b).valueOf()
}
