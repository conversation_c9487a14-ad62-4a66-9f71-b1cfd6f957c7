import { i18n } from '@/main.js'
const todoSubmitToolBar = [
  {
    id: 'submitApproval',
    icon: 'icon_Auction',
    title: i18n.t('提交审批')
  },
  {
    id: 'backComparePrice',
    icon: 'icon_Auction',
    title: i18n.t('撤回报价历史')
  }
]

const compareListColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料/品项编号'),
    allowEditing: false
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料/品项名称'),
    allowEditing: false
  },
  {
    field: 'spec',
    headerText: i18n.t('物料/品项描述'),
    allowEditing: false
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂/地点'),
    allowEditing: false
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司'),
    allowEditing: false
  },
  {
    field: 'purGroupName',
    headerText: i18n.t('采购组'),
    allowEditing: false
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织'),
    allowEditing: false
  },
  {
    field: 'bestFlag', //最优标志 1为最优，其他为否
    headerText: i18n.t('标记'),
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编号'),
    allowEditing: false
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    allowEditing: false
  },
  {
    field: 'quantity',
    headerText: i18n.t('需求数量'),
    allowEditing: true
  },
  {
    field: 'allocationQuantity',
    headerText: i18n.t('分配数量'),
    allowEditing: true
  },
  {
    field: 'allocationRatio',
    headerText: i18n.t('配额'),
    allowEditing: true
  },
  {
    field: 'supplyQuantity',
    headerText: i18n.t('可提供数量'),
    allowEditing: false
  },
  {
    field: 'containFreight', //包含运费 是1 否0
    headerText: i18n.t('是否含运费'),
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    field: 'complexScore',
    headerText: i18n.t('评分'),
    allowEditing: false
  },
  {
    field: 'businessScore',
    allowEditing: false,
    headerText: i18n.t('商务评分')
  },
  {
    field: 'technologyScore',
    headerText: i18n.t('技术评分'),
    allowEditing: false
  },

  {
    field: 'stageType',
    headerText: i18n.t('阶梯方式'),
    allowEditing: false
  },
  {
    field: 'XX',
    headerText: i18n.t('阶梯报价(弹框)'),
    allowEditing: false
  },
  {
    field: 'XX',
    headerText: i18n.t('明细报价(弹框)'),
    allowEditing: false
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('含税单价'),
    allowEditing: false
  },
  {
    field: 'taxRate',
    headerText: i18n.t('税率'),
    allowEditing: false
  },
  {
    field: 'taxedTotalPrice',
    headerText: i18n.t('含税总价'),
    allowEditing: false
  },
  {
    field: 'overBudgetFlag',
    headerText: i18n.t('是否超预算'),
    allowEditing: false
  },
  {
    field: 'budgetAmount',
    headerText: i18n.t('预算金额'),
    allowEditing: false
  },
  {
    field: 'overBudgetRatio',
    headerText: i18n.t('超预算百分比'),
    allowEditing: false
  },
  {
    field: 'orderAccountingFlag',
    headerText: i18n.t('是否超核算'),
    allowEditing: false
  },
  {
    field: 'accountingAmount',
    headerText: i18n.t('核算金额'),
    allowEditing: false
  },
  {
    field: 'orderAccountingRatio',
    headerText: i18n.t('超核算百分比'),
    allowEditing: false
  }
]

//status  比价主表状态 0待提交 1待审批 2审批拒绝 3审批通过 4已退回
export const pageConfig = (url, rfxId) => [
  {
    title: i18n.t('待提交'),
    toolbar: todoSubmitToolBar,
    grid: {
      allowFiltering: true,
      frozenColumns: 2,
      allowEditting: true,
      editSettings: {
        allowEditing: true,
        mode: 'Batch'
      },
      columnData: compareListColumnData,
      asyncConfig: {
        url,
        defaultRules: [
          {
            label: i18n.t('状态'),
            field: 'status',
            type: 'number',
            operator: 'equal',
            value: '0'
          },
          {
            label: 'rfxId',
            field: 'rfxId',
            type: 'string',
            operator: 'equal',
            value: rfxId
          }
        ]
      }
    }
  },
  {
    title: i18n.t('待审批'),
    toolbar: [],
    grid: {
      allowFiltering: true,
      frozenColumns: 2,
      columnData: compareListColumnData,
      asyncConfig: {
        url,
        defaultRules: [
          {
            label: i18n.t('状态'),
            field: 'status',
            type: 'number',
            operator: 'equal',
            value: '1'
          },
          {
            label: 'rfxId',
            field: 'rfxId',
            type: 'string',
            operator: 'equal',
            value: rfxId
          }
        ]
      }
    }
  },
  {
    title: i18n.t('审批通过'),
    toolbar: [],
    grid: {
      allowFiltering: true,
      frozenColumns: 2,
      columnData: compareListColumnData,
      asyncConfig: {
        url,
        defaultRules: [
          {
            label: i18n.t('状态'),
            field: 'status',
            type: 'number',
            operator: 'equal',
            value: '3'
          },
          {
            label: 'rfxId',
            field: 'rfxId',
            type: 'string',
            operator: 'equal',
            value: rfxId
          }
        ]
      }
    }
  },
  {
    title: i18n.t('审批拒绝'),
    toolbar: [],
    grid: {
      allowFiltering: true,
      frozenColumns: 2,
      columnData: compareListColumnData,
      asyncConfig: {
        url,
        defaultRules: [
          {
            label: i18n.t('状态'),
            field: 'status',
            type: 'number',
            operator: 'equal',
            value: '4'
          },
          {
            label: 'rfxId',
            field: 'rfxId',
            type: 'string',
            operator: 'equal',
            value: rfxId
          }
        ]
      }
    }
  }
]
