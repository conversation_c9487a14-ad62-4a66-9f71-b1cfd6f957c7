<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'

export default {
  data() {
    return {
      pageConfig: pageConfig(this.$API.tabComparePrice.getComparePriceList, this.$route.query.rfxId)
    }
  },
  mounted() {},
  methods: {
    //表格按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.grid.getSelectedRecords()
      console.log('use-handleClickToolBar', e, _selectGridRecords)
      if (e.toolbar.id == 'submitApproval') {
        //提交审批
        this.doSubmitApproval(_selectGridRecords, e.grid.getBatchChanges())
      } else if (e.toolbar.id == 'backComparePrice') {
        //撤回报价历史
        this.doBackComparePrice()
      }
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
    },
    //单元格标题点击操作
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
    },
    doSubmitApproval(_records, bathCommond) {
      if (_records.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _changeRecords = bathCommond?.changedRecords
      let _params = []
      if (_changeRecords.length) {
        _records.forEach((r) => {
          let _f = _changeRecords.find((e) => e.id === r.id)
          if (_f?.id) {
            _params.push({
              allocationQuantity: _f.allocationQuantity, //分配数量
              allocationRatio: _f.allocationRatio, //配额
              id: _f.id,
              packageId: _f.packageId
            })
          } else {
            _params.push({
              allocationQuantity: r.allocationQuantity, //分配数量
              allocationRatio: r.allocationRatio, //配额
              id: r.id,
              packageId: r.packageId
            })
          }
        })
      } else {
        _records.forEach((r) => {
          _params.push({
            allocationQuantity: r.allocationQuantity, //分配数量
            allocationRatio: r.allocationRatio, //配额
            id: r.id,
            packageId: r.packageId
          })
        })
      }
      for (let i in _params) {
        if (!_params[i]['allocationQuantity'] || !_params[i]['allocationRatio']) {
          this.$toast({
            content: this.$t("勾选的数据，未设置'分配数量'或'配额'"),
            type: 'warning'
          })
          return
        }
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认执行提交审批？')
        },
        success: () => {
          this.$API.tabComparePrice.doSubmitComparePrice(_params).then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.handleQueryReset()
          })
        }
      })
    },
    doBackComparePrice() {
      this.$dialog({
        // modal: () =>
        //   import(
        //     /* webpackChunkName: "router/purchase/rfx/detail/tabs/compare-price/components/backComparePrice" */ "./components/backComparePrice.vue"
        //   ),
        data: {
          title: this.$t('提示'),
          message: this.$t("确认执行'撤回报价历史'？")
        },
        success: () => {
          this.$API.tabComparePrice
            .doBackComparePrice({
              rfxId: this.$route.query.rfxId,
              backReason: ''
              // backReason: data?.reason,
            })
            .then(() => {
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData()
            })
        }
      })
    }
  }
}
</script>
