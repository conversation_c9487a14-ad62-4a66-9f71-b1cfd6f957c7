import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '60',
    type: 'checkbox'
  },
  {
    field: 'expertName',
    headerText: i18n.t('评分人')
  },
  {
    field: 'mobilePhone',
    headerText: i18n.t('电话')
  },
  {
    field: 'email',
    headerText: i18n.t('邮箱')
  },
  {
    field: 'scoreProcess',
    headerText: i18n.t('评分进度')
  }
]

export const scoreDetailCol = [
  {
    field: 'expertLevel1',
    headerText: i18n.t('查看评分明细'),
    cssClass: 'field-content',
    valueConverter: {
      type: 'placeholder',
      placeholder: i18n.t('明细')
    }
  }
]
const columnData1 = [
  {
    width: '60',
    type: 'checkbox'
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'biddingCode',
    headerText: i18n.t('投标编号')
  },
  {
    field: 'totalScore',
    headerText: i18n.t('商务总得分')
  },
  {
    field: 'viewLeaderEvaluation',
    headerText: i18n.t('专家组长综合评价'),
    cssClass: 'field-content',
    valueConverter: {
      type: 'placeholder',
      placeholder: i18n.t('查看')
    }
  },
  {
    field: 'expertLevel',
    headerText: i18n.t('评分'),
    cssClass: 'field-content',
    valueConverter: {
      type: 'placeholder',
      placeholder: i18n.t('查看')
    }
  }
]
export const pageConfig0 = [
  {
    // title: i18n.t("专家维度"),
    toolbar: [],
    grid: {
      allowFiltering: true,
      columnData: columnData,
      dataSource: [{ expertLevel1: i18n.t('明细') }]
    }
  }
]
export const pageConfig1 = (gridId) => [
  {
    // title: i18n.t("供应商维度"),
    toolbar: [],
    gridId: gridId,
    grid: {
      allowFiltering: true,
      lineIndex: true,
      columnData: columnData1,
      dataSource: [{ expertLevel1: i18n.t('明细') }]
    }
  }
]
