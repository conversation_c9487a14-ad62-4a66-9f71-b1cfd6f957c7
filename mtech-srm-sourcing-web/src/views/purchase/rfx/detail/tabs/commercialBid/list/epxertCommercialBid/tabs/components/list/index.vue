<template>
  <!-- @beforeClose="cancel" -->
  <mt-dialog ref="dialog" css-class="create-proj-dialog" :header="header" @beforeClose="cancel">
    <div class="dialog-content mt-flex-direction-column">
      <div class="expert-input">
        <div class="expert_box">
          <mt-select
            width="200"
            float-label-type="Never"
            :placeholder="$t('请选择供应商')"
            v-model="currentSupplierCode"
            :data-source="supplierList"
            @change="supplierChange"
            :fields="{
              text: 'supplierName',
              value: 'supplierCode'
            }"
            :show-clear-button="true"
          ></mt-select>
        </div>
        <div class="expert_box">
          <p>{{ $t('得分') }}：</p>
          <mt-input width="150" float-label-type="Never" v-model="totalScore" disabled></mt-input>
        </div>
      </div>
      <div class="dialog-box">
        <div class="expert_box" style="width: 100%">
          <div style="width: 105px">
            <p>{{ $t('综合评价') }}：</p>
          </div>
          <div style="width: 100%">
            <mt-input
              :multiline="true"
              :rows="3"
              style="width: 100%"
              maxlength="1000"
              v-model="remark"
              float-label-type="Never"
              :disabled="true"
              :placeholder="$t('请输入1000字以内')"
            ></mt-input>
          </div>
        </div>
      </div>
      <div style="flex: 1">
        <mt-template-page ref="templateRef" :padding-top="true" :template-config="pageConfig" />
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { pageConfig } from '../config'
export default {
  data() {
    return {
      pageConfig: pageConfig,
      totalScore: '',
      supplierList: [],
      currentSupplierCode: '',
      currentExpertCode: '',
      remark: ''
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    let bidScoreSupplierDTOList = this.modalData.dataInfo.bidScoreSupplierDTOList
    this.supplierList = bidScoreSupplierDTOList
    this.currentSupplierCode = bidScoreSupplierDTOList[0].supplierCode
    this.currentExpertCode = this.modalData.dataInfo.expertCode
  },
  methods: {
    supplierChange() {
      this.$nextTick(() => {
        this.getExpertList()
      })
    },
    getExpertList() {
      this.$set(this.pageConfig[0].grid, 'asyncConfig', {
        url: this.$API.rfxExperttechnicalBid.getComputedStyle,
        queryBuilderWrap: 'requestParams',
        recordsPosition: 'data.bidScoreSupplierDetailDTOIPage.records',
        afterAsyncData: (res) => {
          this.totalScore = res.data.totalScore
          this.remark = res.data.remark
        },
        params: {
          rfxCode: this.modalData.rfxCode,
          bidType: 1,
          supplierCode: this.currentSupplierCode,
          expertCode: this.currentExpertCode
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-box {
  display: flex;
  align-content: center;
  justify-content: space-between;
  .expert_box {
    display: flex;
    align-content: center;
    justify-content: center;
  }
}
.box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.expert-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .expert_box {
    display: flex;
    align-content: center;
    justify-content: center;
  }
}
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  height: 100%;
  /deep/ .div-auth {
    background: #fafafa;
    height: 35px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    color: #0f0f0f;
    font-size: 12px;
    cursor: pointer;
    border-radius: 5px;
  }
}
.op-item {
  cursor: pointer;
  color: #4f5b6d;
  align-items: center;
  margin-right: 20px;
  align-items: center;
}
</style>
