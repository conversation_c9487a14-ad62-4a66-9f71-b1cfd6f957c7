<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    >
      <div slot="slot-filter-0" class="tips-container" v-if="detailInfo.eliminateFlag && tipsMsg">
        <span @click="showTipsMsg">{{ tipsMsgShow }} <i v-if="showMore">>></i></span>
      </div>
    </mt-template-page>
  </div>
</template>

<script>
import { pageConfig } from './config'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  name: 'ExpertRating',
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      pageConfig: pageConfig(
        this.$permission.gridId['purchase'][this.$route?.query?.source]['detail']['tabs'][
          'businessBid'
        ]['list'],
        this.detailInfo
      ),
      tipsMsg: '',
      tipsMsgShow: '',
      showMore: false
    }
  },

  mounted() {
    this.getList()
    this.getEliminateTips()
  },
  methods: {
    getList() {
      this.$set(this.pageConfig[0].grid, 'asyncConfig', {
        url: this.$API.rfxExperttechnicalBid.getrfxEcpertList,
        queryBuilderWrap: 'requestParams',
        params: {
          rfxCode: this.detailInfo.rfxCode,
          roundNo: 1
        },
        serializeList: (list) => {
          //处理密封报价  数据显示
          list.forEach((e) => {
            e.taxedUnitPrice = e.taxedUnitPrice === -999 ? '******' : e.taxedUnitPrice //含税单价
            e.untaxedUnitPrice = e.untaxedUnitPrice === -999 ? '******' : e.untaxedUnitPrice //未税单价
            e.taxedTotalPrice = e.taxedTotalPrice === -999 ? '******' : e.taxedTotalPrice //含税总价
            e.untaxedTotalPrice = e.untaxedTotalPrice === -999 ? '******' : e.untaxedTotalPrice //未税总价
          })
          return list
        }
      })
    },
    getEliminateTips() {
      this.$API.rfxList.checkEliminate({ rfxId: this.$route.query?.rfxId }).then((res) => {
        if (res.code === 200) {
          // this.tipsMsg =
          //   '1.随便写点什么哈哈哈<br/>2.随便写点什么哈哈哈<br/>3.随便写点什么哈哈哈<br/>'
          this.tipsMsg = res.data || ''
          const _tipsMsg = this.tipsMsg.replace(/<br\/>/g, ' ')
          if (_tipsMsg.length > 20) {
            this.showMore = true
            this.tipsMsgShow = _tipsMsg?.substr(0, 20) + '...'
          } else {
            this.tipsMsgShow = this.tipsMsg
            this.showMore = false
          }
        }
      })
    },
    showTipsMsg() {
      if (!this.showMore) return
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.tipsMsg
        },
        success: () => {}
      })
    },

    handleClickToolBar(e) {
      if (e.toolbar?.id === 'Save') {
        const selectedRecords = e.grid.getSelectedRecords()
        if (selectedRecords?.length <= 0) {
          this.$toast({
            content: this.$t('请至少选择一条数据'),
            type: 'warning'
          })
          return
        }
        const rfxBiddingItem = selectedRecords.map((item) => {
          return {
            eliminateFlag: item.eliminateFlag,
            rfxBiddingItemId: item.rfxBiddingItemId
          }
        })
        const params = {
          rfxBiddingItem,
          rfxId: this.$route.query?.rfxId
        }
        this.$API.rfxList.updateEliminate(params).then((res) => {
          if (res.code === 200) {
            this.getEliminateTips()
            this.$toast({
              content: this.$t(res.msg),
              type: 'success'
            })
          }
        })
      }
      if (e.toolbar?.id === 'ItemExport') {
        this.handleExport(e)
      }
    },
    handleExport() {
      const params = {
        requestParams: { page: { current: 1, size: 20 } },
        rfxCode: this.detailInfo.rfxCode,
        roundNo: 1
      } // 筛选条件
      this.$store.commit('startLoading')
      this.$API.rfxExperttechnicalBid.exportrfxEcpertList(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>
<style>
.tips-container {
  align-items: center;
  left: 150px;
  padding: 5px 20px;
  position: absolute;
  top: 13px;
  z-index: 2;
  cursor: pointer;
  span {
    color: red;
  }
}
</style>
