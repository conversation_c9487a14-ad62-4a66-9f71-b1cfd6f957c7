<template>
  <div class="mt-flex-direction-column" style="height: 100%">
    <div class="top-info">
      <mt-tabs :e-tab="false" :data-source="tabSource" @handleSelectTab="handleSelectTab"></mt-tabs>
      <div class="expert_box" v-show="tabIndex == 1">
        <div class="dimensionButton" v-show="dimension == 'expertDimension'">
          <img src="@/assets/iconSvg/expertDimension.svg" /><span
            style="color: #00469c; font-weight: bold"
            >{{ $t('专家维度') }}</span
          >
        </div>
        <div
          class="dimensionButton"
          v-show="dimension != 'expertDimension'"
          @click="switchDimension('expertDimension')"
        >
          <img src="@/assets/iconSvg/expertDimensionGrey.svg" /><span>{{ $t('专家维度') }}</span>
        </div>
        <div class="dimensionButton" v-show="dimension == 'supplierDimension'">
          <img src="@/assets/iconSvg/supplierDimension.svg" /><span
            style="color: #00469c; font-weight: bold"
            >{{ $t('供应商维度') }}</span
          >
        </div>
        <div
          class="dimensionButton"
          @click="switchDimension('supplierDimension')"
          v-show="dimension != 'supplierDimension'"
        >
          <img src="@/assets/iconSvg/supplierDimensionGrey.svg" /><span>{{
            $t('供应商维度')
          }}</span>
        </div>
      </div>
    </div>
    <div class="config-container">
      <!-- 0.报价明细  -->
      <!-- <div class="form-design">
        <mt-button
          @click="bidOpening"
          style="margin-right: 15px"
          :disabled="isDisabled"
          >{{ $t("开标") }}</mt-button
        >
        <mt-button @click="sendExpertRateScore" :disabled="isDisabled">{{
          $t("下发专家评分")
        }}</mt-button>
        <mt-button
          @click="endExpertRate"
          style="margin-left: 15px"
          :disabled="isDisabled"
          >{{ $t("结束投标") }}</mt-button
        >
      </div> -->
      <!-- 报价明细 -->
      <tab-params v-if="tabIndex == 0" :detail-info="detailInfo"></tab-params>
      <!-- 专家评分 -->
      <tab-details ref="expert" v-if="tabIndex == 1" :detail-info="detailInfo"></tab-details>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TechnicalBid',
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    }
  },
  components: {
    //商务标-报价明细
    tabParams: () =>
      import(
        /* webpackChunkName: "router/purchase/settings/cost/detail/tabs/params" */ './epxertCommercialBid/tabs/params/index.vue'
      ),
    //商务标-专家评分
    tabDetails: () =>
      import(
        /* webpackChunkName: "router/purchase/settings/cost/detail/tabs/details" */ './epxertCommercialBid/tabs/details/index.vue'
      )
  },
  data() {
    return {
      dimension: 'expertDimension',
      tabIndex: 0,
      tabSource: [
        {
          title: this.$t('报价明细')
        },
        {
          title: this.$t('专家评分')
        }
      ]
    }
  },
  computed: {
    isDisabled() {
      // 已完成和关闭要禁止编辑
      return this.detailInfo?.status === 8 || this.detailInfo?.status === -1
    }
  },
  methods: {
    switchDimension(type) {
      this.dimension = type
      if (type == 'expertDimension') {
        this.$refs.expert.tabIndex = 0
      } else if (type == 'supplierDimension') {
        this.$refs.expert.tabIndex = 1
      }
    },
    handleSelectTab(e) {
      this.tabIndex = e
    },
    sendExpertRateScore() {
      // if (this.detailInfo.transferStatus != 37) {
      //   this.$toast({ content: this.$t("该单据暂未开标"), type: "warning" });
      //   return;
      // }
      let params = {
        rfxCode: this.detailInfo.rfxCode
      }
      this.$API.rfxExpert.getExpertIssue(params).then(() => {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        return
      })
    },
    endExpertRate() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t("确认执行'结束投标'操作？")
        },
        success: () => {
          let params = {
            rfxId: this.$route.query.rfxId
          }
          this.$API.rfxTask.closeBusinessBid(params).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
            }
          })
        }
      })
    },
    bidOpening() {
      let params = {
        openBiddingType: 1,
        rfxId: this.$route.query.rfxId
      }
      this.$store.commit('startLoading')
      this.$API.rfxExpert
        .getExpertAdd(params)
        .then(() => {
          this.$store.commit('endLoading')
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$emit('reGetDetail')
          return
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    }
  }
}
</script>

<style scoped lang="scss">
.expert_box {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  padding-right: 20px;
  .dimensionButton {
    height: 28px;
    line-height: 24px;
    width: 100px;
    border: 1px solid #e8e8e8;
    border-radius: 4px 0 0 4px;
    text-align: center;
    cursor: pointer;
    img {
      vertical-align: middle;
    }
    span {
      margin-left: 5px;
      font-size: 12px;
    }
  }
}
.top-info {
  width: 100%;
  border-radius: 0 8px 0 0;
  display: flex;
  align-items: center;
  .first-line {
    display: flex;
    align-items: center;
    .code {
      width: 220px;
      display: flex;

      .strategy-name {
        width: 150px;
        font-size: 16px;
        font-family: DINAlternate;
        font-weight: bold;
        color: #9a9a9a;
        outline: none;
        border: none;
        background: transparent;
        &:focus {
          outline: none;
          border: none;
          background: transparent;
        }
      }
    }
    .tags {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      padding: 4px;
      border-radius: 2px;
      // margin-left: 25px;
      // margin-left: 10px;

      &-1 {
        color: rgba(237, 161, 51, 1);
        background: rgba(237, 161, 51, 0.1);
      }
      &-2 {
        margin-right: 10px;
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
        margin-left: 5px;
      }
    }
  }

  .cai-desc {
    margin-left: 60px;
  }
  ul {
    display: flex;
    li {
      margin-left: 30px;
      display: flex;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(157, 170, 191, 1);
      .mt-icons {
        font-size: 12px;
      }
      span {
        vertical-align: text-bottom;
        margin-left: 4px;
        // @extend .text-ellipsis;
      }
    }
  }

  // .btns-wrap {
  //   /deep/ .mt-button {
  //     margin-right: 20px;
  //     button {
  //       width: 76px;
  //       height: 34px;
  //       border-radius: 4px;
  //       box-shadow: unset;
  //       padding: 0;
  //       font-size: 14px;
  //       font-family: PingFangSC;
  //       font-weight: 500;
  //       color: rgba(0, 70, 156, 1);
  //     }
  //   }
  // }

  /deep/ .mt-tabs-container {
    background: #fff;
    .tabs-arrow {
      display: none;
    }
    .tab-wrap {
      padding: 0;
      height: 50px;
      .tab-item {
        padding: 6px 10px;
        span {
          line-height: 1;
        }
      }
    }
  }
}
.config-container {
  flex: 1;
  /deep/ .form-design {
    background: #fff;
    padding: 10px 10px 0 10px;
  }
  /deep/ .mt-button {
    button {
      padding: 0 10px !important;
      height: 34px;
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(0, 70, 156, 0.1);
      border-radius: 4px;
      box-shadow: unset;
      padding: 0;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(0, 70, 156, 1);
    }
    &:first-of-type {
      margin-right: 15px;
    }
  }
}
</style>
