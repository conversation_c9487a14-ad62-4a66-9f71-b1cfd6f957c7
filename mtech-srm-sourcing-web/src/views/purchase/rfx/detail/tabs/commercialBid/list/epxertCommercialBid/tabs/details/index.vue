<template>
  <div class="full-height">
    <!-- <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleSelectTab="handleSelectTab"
      :tab-config="{ mode: 'rectangle' }"
    ></mt-template-page> -->
    <!-- <mt-tabs
      :data-source="pageConfig"
      mode=""
      :tab-config="{ mode: 'rectangle' }"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs> -->
    <mt-template-page
      ref="template-0"
      :template-config="pageConfig0"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickToolBar="handleClickToolBar"
      v-if="tabIndex == 0 && pageConfig0[0].grid.columnData.length > 0"
    >
    </mt-template-page>
    <mt-template-page
      ref="template-1"
      :template-config="pageConfig1"
      @handleClickCellTitle="handleClickCellTitle"
      v-if="tabIndex == 1"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { pageConfig1, columnData, scoreDetailCol } from './config'
import { cloneDeep } from 'lodash'
export default {
  name: 'ExpertRating',
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      roundNo: 0,
      tabIndex: 0,
      pageConfig: [{ title: this.$t('专家维度') }, { title: this.$t('供应商维度') }],
      pageConfig0: [
        {
          toolbar: [],
          gridId:
            this.$permission.gridId['purchase'][this.$route?.query?.source]['detail']['tabs'][
              'businessBid'
            ]['expert']['expertDimensionality'],
          grid: {
            allowFiltering: true,
            lineIndex: true,
            columnData: [], // 还要加上自定义字段
            dataSource: []
          }
        }
      ],
      pageConfig1: pageConfig1(
        this.$permission.gridId['purchase'][this.$route?.query?.source]['detail']['tabs'][
          'businessBid'
        ]['expert']['supplierDimensionality']
      ),
      supplierList: []
    }
  },
  mounted() {
    this.getExpertList()
    this.handleSelectTab(this.tabIndex)
  },
  methods: {
    handleClickToolBar(e) {
      if (e.tabIndex == 0) {
        if (e.toolbar.id == 'filterDataByLocal') {
          this.getExpertDimensionalityList(e.rules)
        } else if (e.toolbar.id == 'refreshDataByLocal') {
          this.getExpertDimensionalityList({})
        }
      }
    },
    getExpertDimensionalityList(rules) {
      let param = {
        bidType: 1,
        rfxCode: this.detailInfo.rfxCode,
        requestParams: {
          page: { current: 1, size: 1000 },
          condition: rules.condition,
          rules: rules.rules
        }
      }
      let supplierCols = []
      let _dataSource = []
      this.$API.rfxExperttechnicalBid.getRfxExpert(param).then((res) => {
        if (res.code === 200 && res.data?.records.length > 0) {
          _dataSource = cloneDeep(res.data.records)

          if (res.data.records[0]?.bidScoreSupplierDTOList.length > 0) {
            res.data.records[0]?.bidScoreSupplierDTOList.forEach((e, index) => {
              let _field = `totalScore${index}`
              supplierCols.push({
                field: _field,
                headerText: this.$t(`${e.supplierName}得分`)
              })
            })
            _dataSource.forEach((element) => {
              supplierCols.forEach((x, i) => {
                element[x.field] = element.bidScoreSupplierDTOList[i].totalScore
              })
            })
          }
        }
        this.$set(
          this.pageConfig0[0].grid,
          'columnData',
          columnData.concat(supplierCols, scoreDetailCol)
        )
        this.$set(this.pageConfig0[0].grid, 'dataSource', _dataSource)
      })
    },
    // tab切换
    handleSelectTab(e) {
      if (e === 0) {
        this.getExpertDimensionalityList({})
      }
      this.tabIndex = e
    },
    getExpertList() {
      this.$set(this.pageConfig1[0].grid, 'asyncConfig', {
        url: this.$API.rfxExperttechnicalBid.getExpertScoreList,
        queryBuilderWrap: 'requestParams',
        afterAsyncData: (res) => {
          this.supplierList = res.data.records
        },
        params: {
          rfxCode: this.detailInfo.rfxCode,
          bidType: 1
        }
      })
    },
    handleClickCellTitle(e) {
      if (e.field == 'viewLeaderEvaluation') {
        this.$dialog({
          modal: () => import('./comprehensiveAssessment.vue'),
          data: {
            title: this.$t('综合评价'),
            leaderEvaluation: e.data.leaderEvaluation,
            needButton: false
          },
          success: () => {}
        })
      } else if (e.field == 'expertLevel1') {
        // 打分明细弹框
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "components/market/companyAuth" */ '../components/list/index.vue'
            ),
          data: {
            title: this.$t('评分'),
            rfxCode: this.detailInfo.rfxCode,
            dataInfo: e.data
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      } else if (e.field == 'expertLevel') {
        // 打分明细弹框
        let supplierArr = []
        for (let item of this.supplierList) {
          supplierArr.push({
            supplierName: item.supplierName,
            supplierCode: item.supplierCode,
            totalScore: item.totalScore
          })
        }
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "components/market/companyAuth" */ '../components/list/suplierExpert.vue'
            ),
          data: {
            title: this.$t('评分明细'),
            rfxCode: this.detailInfo.rfxCode,
            dataInfo: e.data,
            supplierArr: supplierArr
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  /deep/.mt-tabs {
    background: #fafafa;
  }
  /deep/ .tab-container {
    background: #fafafa !important;
  }
}
</style>
