import { i18n } from '@/main.js'
import { getValueByPath } from '@/utils/obj'
import { cloneDeep } from 'lodash'
import { createEditInstance } from '@/utils/ej/dataGrid/index'
const editInstance = createEditInstance()
const columnData = [
  {
    width: '60',
    type: 'checkbox',
    allowEditing: false
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    allowEditing: false
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    allowEditing: false
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    allowEditing: false
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    allowEditing: false
  },
  {
    field: 'eliminateRank',
    width: '100',
    headerText: i18n.t('排名')
  },
  {
    field: 'requireDesc',
    headerText: i18n.t('需求描述'),
    allowEditing: false
  },
  {
    field: 'requireName',
    headerText: i18n.t('需求名称'),
    allowEditing: false
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类'),
    allowEditing: false
  },
  {
    field: 'spec',
    headerText: i18n.t('规格描述'),
    allowEditing: false
  },
  {
    field: 'unitName',
    headerText: i18n.t('单位'),
    allowEditing: false
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('单价（含税）'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (isNaN(e)) {
          return '--'
        } else {
          return e.toFixed(2)
        }
      }
    },
    allowEditing: false
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('单价（未税）'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (isNaN(e)) {
          return '--'
        } else {
          return e.toFixed(2)
        }
      }
    },
    allowEditing: false
  },
  {
    field: 'taxedTotalPrice',
    headerText: i18n.t('总价（含税）'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (isNaN(e)) {
          return '--'
        } else {
          return e.toFixed(2)
        }
      }
    },
    allowEditing: false
  },
  {
    field: 'untaxedTotalPrice',
    headerText: i18n.t('总价（未税）'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (isNaN(e)) {
          return '--'
        } else {
          return e.toFixed(2)
        }
      }
    },
    allowEditing: false
  },
  {
    field: 'roundNo',
    headerText: i18n.t('轮次'),
    allowEditing: false
  },
  {
    field: 'bidTimes',
    headerText: i18n.t('次数'),
    allowEditing: false
  },
  {
    field: 'requireQuantity',
    headerText: i18n.t('需求数量'),
    allowEditing: false
  },
  {
    field: 'bidTaxRateValue',
    headerText: i18n.t('税率'),
    allowEditing: false
  },
  {
    field: 'bidCurrencyName',
    headerText: i18n.t('币种'),
    allowEditing: false
  },
  {
    field: 'maxQuotePercent',
    headerText: i18n.t('最高限价'),
    allowEditing: false
  },
  {
    field: 'minQuotePercent',
    headerText: i18n.t('最低限价'),
    allowEditing: false
  },
  {
    field: 'startingPrice',
    headerText: i18n.t('起价（未税）'),
    allowEditing: false
  },
  {
    field: 'minQuoteRange',
    headerText: i18n.t('最小竞价幅度'),
    formatter: function ({ field }, item) {
      const cellVal = getValueByPath(item, field)
      const typeVal = getValueByPath(item, 'minQuoteRangeType')
      return typeVal === 1 ? cellVal * 100 + '%' : cellVal
    },
    allowEditing: false
  },
  {
    field: 'requireDate',
    headerText: i18n.t('需求日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (isNaN(parseInt(e)) || e == 0) {
          return '--'
        } else {
          let date = new Date(Number(e))
          let year = date.getFullYear()
          let month = date.getMonth() + 1
          let day = date.getDate()
          month = month < 10 ? '0' + month : month
          day = day < 10 ? '0' + day : day
          return year + '-' + month + '-' + day
        }
      }
    },
    allowEditing: false
  },
  {
    field: 'requireEndDate',
    headerText: i18n.t('截止日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (isNaN(parseInt(e)) || e == 0) {
          return '--'
        } else {
          let date = new Date(Number(e))
          let year = date.getFullYear()
          let month = date.getMonth() + 1
          let day = date.getDate()
          month = month < 10 ? '0' + month : month
          day = day < 10 ? '0' + day : day
          return year + '-' + month + '-' + day
        }
      }
    },
    allowEditing: false
  }
]
const eliminateColumn = [
  {
    field: 'eliminateFlag',
    headerText: i18n.t('是否淘汰'),
    edit: editInstance.create({
      getEditConfig: () => ({
        type: 'mt-select',
        'show-clear-button': true,
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { text: i18n.t('否'), value: 0 },
          { text: i18n.t('是'), value: 1 }
        ]
      })
    }),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  }
]
export const pageConfig = (gridId, detailInfo) => {
  let _columnData = cloneDeep(columnData)
  let _tools = []
  if (detailInfo.eliminateFlag) {
    _columnData.splice(1, 0, ...eliminateColumn)
    _tools = [{ id: 'Save', icon: 'icon_solid_save', title: i18n.t('保存') }]
  }
  _tools.push({ id: 'ItemExport', icon: 'icon_solid_save', title: i18n.t('导出') })
  return [
    {
      useToolTemplate: false, // 不使用预置(新增、编辑、删除)
      useBaseConfig: false, // 使用组件中的toolbar配置
      toolbar: {
        useBaseConfig: false,
        tools: [_tools, ['Refresh', 'Setting']]
      },
      gridId: gridId,
      grid: {
        allowFiltering: true,
        lineIndex: true,
        columnData: _columnData,
        dataSource: [],
        editSettings: {
          allowEditing: !!detailInfo.eliminateFlag
        }
      }
    }
  ]
}
