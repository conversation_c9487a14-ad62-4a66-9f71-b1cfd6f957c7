import { i18n } from '@/main.js'
const columnData = [
  {
    width: '60',
    type: 'checkbox'
  },
  {
    field: 'scoreOperate',
    headerText: i18n.t('行号')
  },
  {
    field: 'rfxCode',
    headerText: i18n.t('评分项编码')
  },
  {
    field: 'scoreStatus',
    headerText: i18n.t('评分项名称')
  },
  {
    field: 'scoreGroup',
    headerText: i18n.t('评分细则')
    // valueConverter: {
    //   type: "map",
    //   map: { 0: "技术组", 1: "商务组" },
    // },
  },
  {
    field: 'rfxName',
    headerText: i18n.t('最低分')
  },
  {
    field: 'sourcingObj',
    headerText: i18n.t('最高分')
  },
  {
    field: 'expertLevel',
    headerText: i18n.t('得分'),
    cssClass: 'field-content'
  },
  {
    field: 'expertLevel',
    headerText: i18n.t('权重'),
    cssClass: 'field-content'
  },
  {
    field: 'expertLevel',
    headerText: i18n.t('专家1评分')
  },
  {
    field: 'expertLevel',
    headerText: i18n.t('专家2评分')
  }
]
export const pageConfig = [
  {
    useToolTemplate: false,
    toolbar,
    grid: { allowFiltering: true, columnData, dataSource: [] }
  }
]
