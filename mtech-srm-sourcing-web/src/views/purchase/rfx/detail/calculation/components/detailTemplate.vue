<template>
  <div v-if="show && sport" class="tree-grid-detail" :class="[active ? 'active' : '']">
    <mt-DataGrid
      v-if="sport"
      :data-source="pageConfig.dataSource"
      :column-data="pageConfig.columnData"
      :edit-settings="editing"
      :action-complete="saveItemData"
      :toolbar="toolbar"
      ref="dataGrid"
    ></mt-DataGrid>

    <!-- <mt-template-page ref="template123" :template-config="pageConfig" /> -->
  </div>
</template>
<script>
import { createEditInstance } from '@/utils/ej/dataGrid/index'
import checkSelectedItemCode from 'COMPONENTS/NormalEdit/checkSelectItemCodeAll'
export default {
  data() {
    return {
      data: {},
      activeData: {},
      pageConfig: {
        dataSource: [],
        columnData: []
      },
      columnData: [],
      sport: true,
      toolbar: ['Edit', 'Update', 'Cancel'],
      editing: {
        allowEditing: true
      },
      unitNameList: [],
      tab2ItemDataList: [],
      tab2Column: [],
      biddingItemId: 0
    }
  },
  watch: {
    activeData: {
      handler(v) {
        this.data.taskData = v
      },
      deep: true
    }
  },
  computed: {
    show() {
      return this.data.taskData.enableItem == 1 ? true : false
    },
    active() {
      return JSON.stringify(this.activeData) == '{}' ? false : true
    }
  },
  created() {
    if (this.data.taskData.enableItem == 1) {
      this.init()
      this.getUnitName()
    }
  },
  mounted() {
    if (this.data.taskData.enableItem == 1) {
      this.$bus.$on('detailTemplateCancel', () => {
        console.log('detailTemplateCancel', this.$refs.dataGrid)
        this.$refs.dataGrid.ejsRef.closeEdit()
      })
    }
    this.tab2ItemDataList = this.data.itemDataList
    this.tab2Column = this.data?.columnList?.map((item) => {
      return {
        columnId: item.id,
        columnAlias: item.columnAlias
      }
    })
  },
  methods: {
    getUnitName() {
      this.$API.masterData.pagedQueryUnit().then((res) => {
        this.unitNameList = res?.data?.records || []
      })
    },
    init() {
      if (this.data.taskData.enableItem == 1) {
        console.log('this.data', this.data)
        const editInstance = createEditInstance().component(
          'checkSelectedItemCode',
          checkSelectedItemCode
        )
        if (this.data.columnList.length > 0) {
          const a = this.data.columnList.map((item) => {
            let dis = !['1003', '1005'].includes(item.columnCode)
            // let dis = false;
            return {
              field: item.id,
              headerText: item.columnAlias || item.columnName,
              edit: editInstance.create({
                getEditConfig: (ctx) => {
                  console.log('rowData', ctx)
                  if (item.valueSet == 'item') {
                    return {
                      type: 'checkSelectedItemCode',
                      field: 'itemCode',
                      disabled: dis
                    }
                  } else if (item.valueSet == 'unit') {
                    return {
                      type: 'text',
                      // fields: { text: "unitCode", value: "unitCode" },
                      disabled: true
                      // "show-clear-button": true,
                      // dataSource: this.unitNameList,
                    }
                  } else if (item.valueSet == 'cost_factor') {
                    return {
                      type: 'checkSelectedItemCode',
                      sourcingObjType: 'cost_factor',
                      field: 'itemCode'
                    }
                  } else if (item.columnType == 0) {
                    return {
                      type: dis ? 'text' : 'number',
                      disabled: dis
                    }
                  } else {
                    return {
                      type: 'text',
                      disabled: dis,
                      maxLength: 512
                    }
                  }
                }
              })
            }
          })
          this.$set(this.pageConfig, 'columnData', a)
          let c = []
          let b = {}
          this.data.itemDataList.forEach((item, i) => {
            b = {}
            item.forEach((item2) => {
              // a[i][item2.columnAlias] = item2.dataValue;
              b[item2.columnId] = item2.dataValue
              b.rowKey = item2.rowKey
            })
            console.log('queryLeafNodeData', i, b)
            c.push(b)
          })
          this.$set(this.pageConfig, 'dataSource', c)
        }
      }
    },
    getType(type) {
      switch (type) {
        case 0:
          return 'number'
        case 1:
          return 'text'
        case 2:
          return 'select'
        default:
          return 'text'
      }
    },
    queryURLParams(URL) {
      let url = URL.split('?')[1]
      const urlSearchParams = new URLSearchParams(url)
      const params = Object.fromEntries(urlSearchParams.entries())
      return params
    },
    saveItemData(e) {
      if (e.requestType != 'save') {
        return
      }
      let arr = []
      let b = [...this.tab2ItemDataList]
      let c = []
      for (let i = 0; i < b.length; i++) {
        this.tab2ItemDataList[i].forEach((item) => {
          if (item.rowKey == e.data.rowKey) {
            c = this.tab2ItemDataList[i]
            b[i] = '1'
          }
        })
      }
      for (const key in e.data) {
        if (key == 'id' || key == 'rowKey') {
          continue
        }
        console.log('saveItemData', key, e.data[key], this.tab2Column)
        arr.push({ dataValue: e.data[key] })
      }
      console.log('arrcc', arr, c)
      if (c.length > 0) {
        arr.forEach((item, i) => {
          item.columnId = this.tab2Column[i].columnId
          item.columnAlias = this.tab2Column[i].columnAlias
          item.id = c[i]?.id
        })
      } else {
        arr.forEach((item, i) => {
          item.columnId = this.tab2Column[i].columnId
          item.columnAlias = this.tab2Column[i].columnAlias
        })
      }
      b[b.indexOf('1')] = arr
      let par = this.queryURLParams(window.location.href)
      console.log('arrarrarr', arr, par)
      let params = {
        itemDataSaveRequestList: b,
        rfxItemId: this.data.rfxItemId,
        rfxCostModelItemId: this.data.id,
        abateFlag: par.abateFlag == 'true' ? true : false,
        newPrice: parseInt(par.newPrice) || 0
      }
      this.$API.rfxCostModel.saveItemData(params).then(() => {
        this.queryLeafNodeData()
      })
    },
    queryLeafNodeData() {
      let params = {
        rfxItemId: this.data.rfxItemId,
        rfxCostModelItemId: this.data.id
      }
      this.$API.rfxCostModel.queryLeafNodeData(params).then((r) => {
        let a = []
        let b = {}
        this.tab2ItemDataList = r.data
        r.data.forEach((item) => {
          b = {}
          item.forEach((item2) => {
            b[item2.columnId] = item2.dataValue
            b.rowKey = item2.rowKey
            b.id = item2.id
          })
          a.push(b)
        })
        this.$set(this.pageConfig, 'dataSource', a)
        // this.queryLeafNodeData();
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.tree-grid-detail {
  border: 1px solid #e0e0e0;
  padding: 10px 40px;
  // background: #e0e0e0;
}
.active {
  background: #e0e0e0;
}
/deep/ .e-toolbar.e-control {
  text-align: left !important;
}
/deep/ .mt-data-grid {
  border: 1px solid #e0e0e0;
  // .e-grid {
  //   .e-content {
  //     height: 300px !important;
  //   }
  // }
}
</style>
