<template>
  <div class="tempalte-detail mt-flex-direction-column">
    <div class="page-bottom mt-flex">
      <div class="page-content fbox">
        <div class="detail-top">
          <div class="left">
            <div class="title">
              <span :style="formObject.costModelCode && 'margin-right: 20px;'">{{
                formObject.costModelCode
              }}</span>
              <span class="titleC" :style="formObject.costModelName && 'margin-right: 20px;'">{{
                formObject.costModelName
              }}</span>
              <span class="statusC">{{ status }}</span>
            </div>
            <div class="form">
              <div class="form_div">
                {{ $t('版本号') }}：{{ formObject.costModelVersionCode || '--' }}
              </div>
              <template v-if="$route.query.type === 'calculation'">
                <div class="form_div">
                  {{ $t('物料编码：') }}{{ tempForm.materialCode || '--' }}
                </div>
                <div class="form_div">
                  {{ $t('临时物编：') }}{{ tempForm.tempMaterialCode || '--' }}
                </div>
                <div class="form_div">{{ $t('阶梯数量：') }}{{ tempForm.stepValue || '--' }}</div>
                <div class="form_div">{{ $t('直送地：') }}{{ tempForm.deliveryPlace || '--' }}</div>
              </template>

              <div class="form_div">{{ $t('业务方：') }}{{ $t('采购方') }}</div>
            </div>
          </div>
          <div class="right">
            <vxe-button class="right_but" size="small" @click="back">
              {{ $t('返回') }}
            </vxe-button>
            <vxe-button
              v-if="$route.query.type === 'calculation'"
              class="right_but"
              size="small"
              status="primary"
              @click="save"
            >
              {{ $t('保存') }}
            </vxe-button>
            <vxe-button class="right_but" size="small" status="primary" @click="submit">
              {{ $t('提交') }}
            </vxe-button>
          </div>
        </div>
        <CostModelDetail
          ref="costModelDetailRef"
          :form-object="formObject"
          @updateFormData="updateFormData"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { pageConfig } from './config'
import CostModelDetail from './costModelDetail.vue'

export default {
  components: { CostModelDetail },
  computed: {
    bottomShow() {
      return JSON.stringify(this.activeData) == '{}' ? false : true
    },
    costModelId() {
      return this.$route.query.configId ? this.$route.query.configId : '176786814758723680'
    },
    status() {
      switch (this.tempForm.status) {
        case 0:
          return this.$t('未提交')
        case 1:
          return this.$t('已提交')
        default:
          return this.$t('暂无状态')
      }
    }
  },
  data() {
    return {
      editStatus: false,
      treeGridRef: {},
      show: true,
      pageConfig: pageConfig(
        {
          rfxItemId: this.$route.query.rfxItemId,
          costModelType: 1
        },
        this
      ),
      activeData: {},
      formData: {},
      formObject: {
        itemCode: this.$route.query.itemCode,
        itemName: this.$route.query.itemName,
        costModelName: this.$route.query.costModelName
      },
      tempForm: {}
    }
  },
  mounted() {
    this.findById()
  },
  methods: {
    findById() {
      this.$API.rfxCostModel.findById({ id: this.$route.query.rfxItemId }).then((r) => {
        this.formObject = { ...r.data }
      })
    },
    updateFormData(data) {
      const { stepValue, directDeliverAddr, status, materialCode, tempMaterialCode } = data
      this.tempForm = {
        ...this.tempForm,
        stepValue,
        deliveryPlace: directDeliverAddr,
        status,
        materialCode,
        tempMaterialCode
      }
    },
    // 返回
    back() {
      if (this.$refs.costModelDetailRef.isContentChange) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('有未保存的记录，是否确认返回？')
          },
          success: () => {
            this.$refs.costModelDetailRef.isContentChange = false
            this.$router.go(-1)
          }
        })
      } else {
        this.$router.go(-1)
      }
    },
    // 保存
    save() {
      if (this.$route.query.type === 'calculation') {
        this.$refs.costModelDetailRef.handleEstimate('save')
      }
    },
    // 提交
    submit() {
      if (this.$route.query.type === 'calculation') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('提交后将会生成历史测算记录，是否确认提交？')
          },
          success: () => {
            this.$refs.costModelDetailRef?.handleEstimate('submit')
          }
        })
      } else {
        this.$refs.costModelDetailRef?.hanldeSaveCostModel()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .e-grid .e-detailcell {
  padding: 0;
  border: none;
}
.tempalte-detail {
  background: transparent;
  // height: 100%;

  .page-top {
    height: 60px;
    flex-shrink: 0;
  }
  .page-bottom {
    flex: 1;
    height: 100%;
  }

  .page-content {
    background: #fff;
    flex: 1;
    // padding: 20px;
    height: 100%;
    padding: 19px 20px 0 20px;
    flex-direction: column;
    width: 100%;
  }
  .detail-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 15px 30px 5px 30px;
    background: rgba(99, 134, 193, 0.08);
    border: 1px solid #e8e8e8ff;
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    .right_but {
      margin-left: 10px;
    }
    .left {
      color: #292929;
      .title {
        font-size: 20px;
        font-weight: 600;
        .statusC {
          display: inline-block;
          padding: 0 5px;
          height: 24px;
          line-height: 22px;
          text-align: center;
          background: #e8ecf5;
          border-radius: 2px;
          font-size: 12px;
          color: rgba(99, 134, 193, 1);
          font-weight: 500;
        }
        .titleC {
          display: inline-block;
          height: 22px;
          font-size: 16px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(154, 154, 154, 1);
        }
      }
      .form {
        padding: 10px 0;
        font-size: 12px;
        font-weight: normal;
      }
      .form_div {
        display: inline-block;
        padding-right: 20px;
      }
      .data {
        padding: 10px 0;
        font-size: 14px;
        font-weight: 600;
        span {
          margin-left: 3px;
          color: #00469c;
        }
      }
    }
  }
  .detail-content-no {
    background: #e8e8e8;
    // height: calc(100vh - 240px);
    overflow: auto;
    transition: 0.3s;
  }
  .detail-content {
    background: #e8e8e8;
    // height: calc(100vh - 550px);
    min-height: 500px;
    overflow: auto;
    // transition: 0.3s;
  }
}

/deep/.e-grid {
  .e-content {
    overflow: auto !important;
    position: relative;
    // height: calc(100vh - 580px) !important;
    height: 100% !important;
  }
  .e-rowcell {
    text-align: left !important;
    .grid-edit-column {
      // display: inline-block !important;
    }
  }
}
</style>
