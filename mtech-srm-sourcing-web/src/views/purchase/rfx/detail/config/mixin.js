export default {
  computed: {
    getModulesFields() {
      return (moduleType, key) => {
        let item = this.RFXModuleTabList.find((e) => e.moduleType === moduleType)
        return item && item[key] ? item[key] : []
      }
    }
  },
  methods: {
    //序列化 配置数据
    defineModuleFields(item) {
      const defineTreeFields = (module) => {
        let _structures = module.structures
        _structures.forEach((e) => {
          let _field = []
          module.fieldDefines.forEach((f) => {
            f.fieldCode = f.fieldCode ? f.fieldCode.trim() : 'undefined'
            if (e.structureKey === f.structureKey) {
              _field.push(f)
            }
          })
          e.fields = _field
        })
        return _structures
      }
      if (Array.isArray(item.fieldDefines)) {
        //兼容数据库返回的 fieldCode存在空格
        item.fieldDefines.forEach((f) => {
          f.fieldCode = f.fieldCode.trim()
        })
      }
      let _structures = item.structures
      item.parentFields = []
      item.childFields = []
      if (Array.isArray(_structures) && _structures.length > 1) {
        //_structures.length > 1，配置的结构，为父子结构
        _structures = defineTreeFields(item)
        item.parentFields = Array.isArray(_structures[0]['fields']) ? _structures[0]['fields'] : []
        item.childFields = Array.isArray(_structures[1]['fields']) ? _structures[1]['fields'] : []
      } else {
        item.parentFields = Array.isArray(item.fieldDefines) ? item.fieldDefines : []
      }
    }
  }
}
