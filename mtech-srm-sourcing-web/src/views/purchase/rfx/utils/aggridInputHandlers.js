import Vue from 'vue'
// import { isNullOrUndefined } from '@/utils/is'
// import { PRICE_FRACTION_DIGITS } from '@/constants/editConfig'\
import Decimal from 'decimal.js'
const Decimal32 = Decimal.clone({
  precision: 32,
  rounding: Decimal.ROUND_HALF_UP
})

const fieldMap = {
  'biddingItemDTO.bidTaxRateName': 'taxItemName',
  'biddingItemDTO.bidPurUnitName': 'unitName',
  unitName: 'unitName',
  currencyName: 'currencyName',
  siteCode: 'siteCode',
  purGroupCode: 'groupCode',
  purUnitName: 'purUnitName',
  taxRateName: 'taxItemName',
  'annualLogisticsTrunkItem.logisticsMethodName': 'dictCode'
}
/**
 * 数据设置
 * @param {} params
 * @returns
 */
const setData = (params, data) => {
  const rowData = Object.assign({}, params.node.data, data)
  params.node.setData(rowData)
}

/**
 * 数据联动
 * @param {} params
 * @returns
 */
const linkUpdate = (mapData, params) => {
  const field = params.column.colId
  if (!Object.keys(mapData).includes(field)) return
  const { row } = getOptions(params)
  let obj = {}
  for (const linkField in mapData[field]) {
    obj[linkField] = row[mapData[field][linkField]]
  }
  // 设置数据
  setData(params, obj)
}
export const getOptions = (params) => {
  const field = params.column.colId
  const value = params.value
  let dataSource = params.colDef?.cellEditorParams?.editConfig?.props?.dataSource
    ? params.colDef?.cellEditorParams?.editConfig?.props?.dataSource
    : params.colDef?.cellEditorParams?.editConfig?.dataSource
  dataSource = dataSource ? dataSource : []
  const findField = fieldMap[field]
  let row = null
  if (dataSource?.length) {
    row = dataSource.find((e) => e[findField] === value)
  }
  return {
    field,
    value,
    row
  }
}
/**
 * 单位联动
 * unitName => unitId unitCode 单位联动
 */
export const inputUnit = (params) => {
  const unitMap = {
    unitName: {
      unitId: 'id',
      unitCode: 'unitCode'
    },
    purUnitName: {
      purUnitId: 'id',
      purUnitCode: 'unitCode'
    }
  }
  linkUpdate(unitMap, params)
}
/**
 * 币种名称 => 币种编码联动编辑（暂不设置双向联动）
 */
export const inputCurrency = (params) => {
  const currencyMap = {
    currencyName: {
      currencyCode: 'currencyCode'
    }
  }
  linkUpdate(currencyMap, params)
}
/**
 * 采购组织联动
 * purGroupCode => purGroupId purGroupName
 */
export const inputPurGroup = (params) => {
  const currencyMap = {
    purGroupCode: {
      purGroupId: 'id',
      purGroupName: 'groupName'
    }
  }
  linkUpdate(currencyMap, params)
}
/**
 * 工厂 => siteId siteName
 */
export const inputSite = (params) => {
  const currencyMap = {
    siteCode: {
      siteId: 'id',
      siteName: 'siteName'
    }
  }
  linkUpdate(currencyMap, params)
}
/**
 * 物流方式
 * logisticsMethodName => logisticsMethodCode 物流方式
 */
export const inputLogisticsMethod = (params) => {
  if (params.colDef.fieldCode !== 'logisticsMethodName') return
  const { row } = getOptions(params)
  const annualLogisticsTrunkItem = { ...params.node.data.annualLogisticsTrunkItem }
  annualLogisticsTrunkItem.logisticsMethodCode = row?.dictCode
  annualLogisticsTrunkItem.logisticsMethodName = row?.dictName
  const rowData = Object.assign({}, params.node.data, { annualLogisticsTrunkItem })
  params.node.setData(rowData)
}
/**
 * 成本模型联动 => 物料、品类、是否成本模型 => 成本模型、成本测算
 */
export const inputCost = async (params, detailInfo, rfxId, queryRelCostModel) => {
  const field = params.column.colId
  if (!['itemCode', 'categoryCode', 'costModelQuote'].includes(field)) return
  let args = {
    itemCode: params.data.itemCode,
    categoryCode: params.data.categoryCode,
    costModelQuote: params.data.costModelQuote,
    detailInfo: detailInfo,
    rfxId: rfxId,
    queryRelCostModel: queryRelCostModel
  }
  const res = await getCostModel(args)
  // 取出第一行的值
  const firstRow = res?.length ? res[0] : {}
  const costData = {
    costModelId: firstRow?.id || '',
    costModelCode: firstRow?.costModelCode || '',
    costModelName: firstRow?.costModelName || '',
    costModelVersionCode: firstRow?.versionCode || '',
    costModelVersion: firstRow?.versionCode || ''
  }
  const rowData = Object.assign({}, params.node.data, costData)
  params.node.setData(rowData)
}
/**
 * 获取成本模型数据
 */
const getCostModel = async (args) => {
  if (!args.itemCode || !args.categoryCode || !args.costModelQuote) return
  const params = {
    rfxId: args.rfxId,
    categoryCode: args.categoryCode || '',
    companyCodeList: [args.detailInfo?.companyCode || ''],
    relCode: args.itemCode || args.categoryCode,
    relationType: args.detailInfo.sourcingObjType === 'cost_factor' ? 2 : 1, // 品类:0、物料:1、行情因子:2
    page: { current: 1, size: 20 }
  }
  const res = await args.queryRelCostModel(params).catch(() => {})
  return res?.data?.records || []
}
/**
 * 税率 taxRateName => taxRateValue、taxRateCode
 */
export const inputTaxRate = (params) => {
  const currencyMap = {
    taxRateName: {
      taxRateValue: 'taxRate',
      taxRateCode: 'taxItemCode'
    }
  }
  linkUpdate(currencyMap, params)
}
/**
 * 建议最小采购量 <=> 建议最小包装量 整数联动校验
 */
export const inputQuantity = (params) => {
  const field = params.column.colId
  if (!['itemExtMap.adviseMinPurQuantity', 'itemExtMap.adviseMinPackageQuantity'].includes(field))
    return
  const adviseMinPurQuantity = params.node.data?.itemExtMap?.adviseMinPurQuantity
  const adviseMinPackageQuantity = params.node.data?.itemExtMap?.adviseMinPackageQuantity
  if (!adviseMinPurQuantity || !adviseMinPackageQuantity) return
  if (judgePurAndQuantiry(adviseMinPurQuantity, adviseMinPackageQuantity)) {
    Vue.prototype.$toast({ content: '建议最小采购量需是建议最小包装量的整数倍', type: 'warning' })
  }
}
/**
 * 判断最小采购量和最小包装量
 */
export const judgePurAndQuantiry = (minPurQuantity, minPackageQuantity) => {
  if (!minPurQuantity || !minPackageQuantity) return false
  let _num = minPurQuantity / minPackageQuantity
  return Math.floor(_num) !== _num ? true : false
}
/**
 * 订单单位名称联动 => 订单单位编码 和 单位编码一致 则转换率为1
 */
export const inputBidPurUnit = (params, grid, columns) => {
  const field = params.column.colId
  if (field !== 'biddingItemDTO.bidPurUnitName') return
  // 设置订单单位编码
  const { row } = getOptions(params)
  params.node.setDataValue('biddingItemDTO.bidPurUnitCode', row.unitCode)
  // 设置转换率
  if (!judgeUnitExist(columns)) return
  if (judgeUnitConsistent(params.data)) {
    // 符合更新条件
    params.node.setDataValue('biddingItemDTO.bidConversionRate', 1)
  }
}
/**
 * 校验订单单位编码 和 单位编码 是否存在 存在返回ture
 */
export const judgeUnitExist = (columns) => {
  const cols = columns.map((item) => item.field)
  const flag =
    (cols.includes('biddingItemDTO.bidPurUnitCode') || cols.includes('purUnitCode')) &&
    cols.includes('unitCode') &&
    cols.includes('biddingItemDTO.bidConversionRate')
  return flag
}
/**
 * 校验订单单位编码 和 单位编码是否一致 (一致返回true)
 */
export const judgeUnitConsistent = (row) => {
  const purUnitCode = row.biddingItemDTO?.bidPurUnitCode || row.purUnitCode
  const unitCode = row.unitCode
  return purUnitCode === unitCode
}
/**
 * 预算价格计算
 */
export const inputPrice = (params) => {
  const field = params.column.colId
  if (
    ![
      'itemExtMap.budgetUnitPriceTaxed',
      'itemExtMap.budgetUnitPriceUntaxed',
      'taxRateValue',
      'taxRateName',
      'itemExtMap.requireQuantity'
    ].includes(field)
  ) {
    return
  }
  //计算预算单价(含税)
  let unitPriceWithoutTax = params.node.data?.itemExtMap?.budgetUnitPriceUntaxed || 0 //预算单价未税
  let unitPriceIncludesTax = params.node.data?.itemExtMap?.budgetUnitPriceTaxed || 0 //预算单价含税
  let taxRate = params.node.data?.taxRateValue || 1 //税率
  let demandQuantity = params.node.data?.itemExtMap?.requireQuantity || 0 //需求量
  unitPriceIncludesTax = unitPriceIncludesTax
    ? new Decimal32(unitPriceIncludesTax).toFixed(2)
    : new Decimal32(taxRate).add(1).mul(unitPriceWithoutTax).toFixed(2)
  let totalPriceWithoutTax = new Decimal32(unitPriceWithoutTax).mul(demandQuantity).toFixed(2)
  let totalPriceIncludesTax = new Decimal32(unitPriceIncludesTax).mul(demandQuantity).toFixed(2)
  let rowData = params.node.data
  rowData.itemExtMap = Object.assign({}, rowData.itemExtMap, {
    budgetUnitPriceTaxed: unitPriceIncludesTax, //预算单价(含税)
    budgetTotalPriceUntaxed: totalPriceWithoutTax, //预算总价(未税)
    budgetTotalPriceTaxed: totalPriceIncludesTax //预算总价(含税)
  })
  params.node.setData(rowData)
}
/**
 * 规划量联动实际分摊量 itemDieResponse.planQuantity => itemDieResponse.shareQuantity
 * 规划量，实际分摊量、已分摊数量
 */
export const inputItemDieQuantity = (params) => {
  const field = params.column.colId
  if (!['itemDieResponse.planQuantity'].includes(field)) {
    return
  }
  let rowData = params.node.data
  let value = parseInt(params.value / 2) < 1 ? 1 : parseInt(params.value / 2)
  rowData.itemDieResponse.shareQuantity = value
  params.node.setData(rowData)
}

/**
 * 报价有效期从联动 => 报价有效期止
 */
export const inputEffectiveDate = (params, quotedPriceData) => {
  if (quotedPriceData?.companyCode !== '1503') return
  const field = params.column.colId
  if (field !== 'biddingItemDTO.quoteEffectiveStartDate') return
  let _startDate = params.node.data.biddingItemDTO.quoteEffectiveStartDate
  let _endDate = getEndDate(_startDate)
  _startDate && params.node.setDataValue('biddingItemDTO.quoteEffectiveEndDate', _endDate)
}
/**
 * 获取生效日期止
 * @returns date
 */
function getEndDate(date) {
  let _end = ''
  let currentYear = new Date(date).getFullYear()
  let judgeTime = new Date(currentYear + '-07-01').getTime()
  if (date < judgeTime) {
    _end = currentYear + '-12-31' // 当年12-31
  } else {
    _end = currentYear + 1 + '-12-31' // 次年12-31
  }

  return new Date(_end).getTime()
}
