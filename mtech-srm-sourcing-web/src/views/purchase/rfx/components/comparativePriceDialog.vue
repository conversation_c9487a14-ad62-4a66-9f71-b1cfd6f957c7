<template>
  <mt-dialog
    ref="comparativePriceDialog"
    :position="{ X: 'right', Y: 'top' }"
    css-class="create-proj-dialog right-wrapper price-dialog"
    :enable-resize="false"
  >
    <ComparativePrice
      :rfx-id="rfxId"
      :sourcing-obj-type="detailInfo.sourcingObjType"
      :current-round="detailInfo.currentRound"
    ></ComparativePrice>
  </mt-dialog>
</template>

<script>
import ComparativePrice from '@/views/purchase/comparativePrice/index.vue'
export default {
  components: {
    ComparativePrice
  },
  data() {
    return {}
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    detailInfo() {
      return this.modalData.detailInfo
    },
    rfxId() {
      return this.modalData.rfxId
    }
  },
  mounted() {
    this.$refs.comparativePriceDialog.ejsRef.show()
  },
  methods: {}
}
</script>
