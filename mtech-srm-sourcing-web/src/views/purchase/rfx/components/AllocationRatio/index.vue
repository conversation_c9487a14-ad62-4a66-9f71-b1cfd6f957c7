<template>
  <mt-select
    :data-source="select.dataSource"
    :fields="select.fields"
    :item-template="itemTemplate"
    @select="onSelect"
    :placeholder="$t('查看/修改')"
    @focus="open"
    :readonly="true"
  />
</template>

<script>
import ARItemTemplate from './ARItemTemplate'

const ALLOCATION_RATIO = '0,0,0,0,0'

export default {
  props: {
    value: {
      type: String,
      default: ALLOCATION_RATIO
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      itemTemplate: () => {
        return {
          template: ARItemTemplate
        }
      },
      select: {
        fields: { text: 'desc', value: 'text' },
        dataSource: [
          { text: this.$t('第一标'), value: 0, desc: this.$t('查看') },
          { text: this.$t('第二标'), value: 0, desc: this.$t('查看') },
          { text: this.$t('第三标'), value: 0, desc: this.$t('查看') },
          { text: this.$t('第四标'), value: 0, desc: this.$t('查看') },
          { text: this.$t('第五标'), value: 0, desc: this.$t('查看') }
        ]
      }
    }
  },

  beforeMount() {
    // let value = this.value;
    let value = sessionStorage.getItem('allocationRatio')
    if (!value || typeof value !== 'string') {
      value = ALLOCATION_RATIO
    }
    value = value.split(',')
    if (value.length !== 5) {
      console.warn('allocationRatio', value)
      value = ALLOCATION_RATIO.split(',')
    }

    this.select.dataSource = this.select.dataSource.map((e, index) => {
      return {
        ...e,
        value: value[index]
      }
    })
  },
  mounted() {
    this.$emit('input', sessionStorage.getItem('allocationRatio'))
  },

  methods: {
    onSelect() {
      if (this.readonly) {
        return
      }
      this.$dialog({
        modal: () => import('./AllocationRatioEdit.vue'),
        data: {
          title: this.$t('修改配额分配比例'),
          data: {
            value: this.select.dataSource
          }
        },
        success: ({ type, data } = {}) => {
          if (type === 'save') {
            this.select.dataSource = data
            this.$emit('input', data.map((e) => Number(e.value)).join(','))
          }
        }
      })
    },
    open() {
      // if (this.readonly) {
      //   return
      // }
      this.$dialog({
        modal: () => import('./AllocationRatioEdit.vue'),
        data: {
          title: this.$t('配额分配比例'),
          data: {
            value: this.select.dataSource,
            readonly: this.readonly
          }
        },
        success: ({ type, data } = {}) => {
          if (type === 'save') {
            const res = data.map((e) => e.value)
            sessionStorage.setItem('allocationRatio', res.join(','))
            this.select.dataSource = data
            this.$emit('input', data.map((e) => Number(e.value)).join(','))
          }
        }
      })
    }
  }
}
</script>
