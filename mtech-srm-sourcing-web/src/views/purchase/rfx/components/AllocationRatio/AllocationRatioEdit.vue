<template>
  <mt-dialog
    ref="dialog"
    css-class="ratio-dialog"
    :buttons="buttons"
    :header="modalData.title"
    @beforeClose="emitConfirm"
  >
    <div class="dialog-content" style="padding-top: 20px; width: 400px">
      <mt-form ref="appForm" :rules="form.rules">
        <mt-row :gutter="20">
          <mt-col :span="24" v-for="(item, index) in form.data" :key="index">
            <mt-form-item :label="item.text">
              <mt-input-number
                v-model="item.value"
                :precision="2"
                :min="0"
                :readonly="modalData.data.readonly"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
  </mt-dialog>
</template>
<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.emitConfirm,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('保存') }
        }
      ],
      form: {
        data: []
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()

    this.form.data = [...this.modalData.data.value]
    if (this.modalData.data.readonly) {
      this.buttons = []
    }
  },
  methods: {
    emitConfirm(...arg) {
      this.$emit('confirm-function', ...arg)
    },
    save() {
      const total = this.form.data.reduce((p, v) => Number(v.value) + p, 0)
      if (total !== 100 && total !== 0) {
        this.$toast({
          content: this.$t('比例总和应当为0或100'),
          type: 'warning'
        })
        return
      }
      this.emitConfirm({
        type: 'save',
        data: this.form.data
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
}
</style>
