<template>
  <mt-form :model="form" :rules="formRules" ref="form" class="form-generator-form">
    <template v-for="(formItem, index) in fieldDefines">
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'text' && !formItem.form.hideFormItem"
        label-style="top"
      >
        <mt-input
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          :show-clear-button="false"
          @change="handleFormItemChange($event, formItem)"
          :placeholder="$t(formItem.form.label)"
          :label="$t(formItem.form.label)"
          type="text"
          :maxlength="formItem.form.maxlength"
          :multiline="formItem.form.multiline"
        />
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'number' && !formItem.form.hideFormItem"
        label-style="top"
      >
        <mt-input-number
          class="input-number"
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          @change="handleFormItemChange($event, formItem)"
          :placeholder="$t(formItem.form.label)"
          :min="typeof formItem.form.min === 'number' ? formItem.form.min : 0"
          :max="formItem.form.max"
          :show-clear-button="false"
        />
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'select' && !formItem.form.hideFormItem"
        label-style="top"
      >
        <mt-select
          v-if="formItem.form.modalName == 'exchangeRateName'"
          :data-source="formItem.form.dataSource"
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          :show-clear-button="true"
          @select="handleFormSelectItemChange($event, formItem)"
          @change="handleFormItemChange($event, formItem)"
          :fields="{ text: 'text', value: 'value' }"
          :allow-filtering="formItem.form.allowFiltering"
          filter-type="Contains"
          :placeholder="$t(formItem.form.label)"
        />
        <mt-select
          v-else
          :data-source="formItem.form.dataSource"
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          :show-clear-button="true"
          @select="handleFormSelectItemChange($event, formItem)"
          @change="handleFormItemChange($event, formItem)"
          :fields="{ text: 'text', value: 'value' }"
          :allow-filtering="formItem.form.allowFiltering"
          :filtering="formItem.form.filtering"
          :placeholder="$t(formItem.form.label)"
        />
        <span
          v-if="showBadgeList.includes(formItem.form.modalName)"
          class="label-badge-content"
          :style="{ left: badgeMesMap[formItem.form.modalName]['left'] }"
          :badge-msg="$t(badgeMesMap[formItem.form.modalName]['msg'])"
        >
          ?
        </span>
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'multiSelect' && !formItem.form.hideFormItem"
        label-style="top"
      >
        <div :title="form[formItem.form.modalName].toString()" style="width: 100%">
          <mt-multi-select
            v-if="formItem.form.modalName == 'sourcingExpand'"
            class="sourcing-expand"
            :data-source="formItem.form.dataSource"
            v-model="form[formItem.form.modalName]"
            :disabled="formItem.form.readonly"
            :allow-filtering="true"
            :item-template="selectItemTemplate"
            filter-type="Contains"
            @change="handleFormItemChange($event, formItem)"
            popup-width="470px"
          />
          <mt-multi-select
            v-else
            :data-source="formItem.form.dataSource"
            v-model="form[formItem.form.modalName]"
            :disabled="formItem.form.readonly"
            :item-template="selectItemTemplate"
            :show-clear-button="false"
            @change="handleFormItemChange($event, formItem)"
            :placeholder="$t(formItem.form.label)"
          />
        </div>
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :label="$t(formItem.form.interactionLabel)"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        :key="formItem.form.interactionModel"
        v-if="formItem.form.interactionModel"
        label-style="top"
      >
        <mt-input
          v-model="form[formItem.form.interactionModel]"
          :show-clear-button="false"
          :placeholder="$t(formItem.form.interactionLabel)"
          disabled
        />
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'date' && !formItem.form.hideFormItem"
        label-style="top"
      >
        <div
          style="min-width: 185px; width: 100%"
          :title="formatDate(form[formItem.form.modalName], 'YYYY-mm-dd')"
        >
          <mt-date-picker
            @change="handleFormItemChange($event, formItem)"
            :open-on-focus="true"
            format="yyyy-MM-dd"
            time-stamp
            :show-clear-button="false"
            v-model="form[formItem.form.modalName]"
            :disabled="formItem.form.readonly"
            :placeholder="$t(formItem.form.label)"
            :min="formItem.form.min"
            :max="formItem.form.max"
          />
        </div>
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'time' && !formItem.form.hideFormItem"
        label-style="top"
      >
        <mt-time-picker
          @change="handleFormItemChange($event, formItem)"
          :open-on-focus="true"
          :show-clear-button="false"
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          :placeholder="$t(formItem.form.label)"
      /></mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'datetime' && !formItem.form.hideFormItem"
        label-style="top"
      >
        <div
          style="min-width: 185px; width: 100%"
          :title="formatDate(form[formItem.form.modalName], 'YYYY-mm-dd HH:MM')"
        >
          <DateTimePicker
            @change="handleFormItemChange($event, formItem)"
            :open-on-focus="true"
            format="yyyy-MM-dd HH:mm"
            time-stamp
            :show-clear-button="false"
            v-model="form[formItem.form.modalName]"
            :disabled="formItem.form.readonly"
            :placeholder="$t(formItem.form.label)"
            :min="formItem.form.min"
            :max="formItem.form.max"
          />
        </div>
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'dropdown-tree' && !formItem.form.hideFormItem"
        label-style="top"
      >
        <mt-drop-down-tree
          @change="handleFormItemChange($event, formItem)"
          :open-on-focus="true"
          :show-clear-button="false"
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          :placeholder="$t(formItem.form.label)"
          :fields="[]"
        />
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'checkbox' && !formItem.form.hideFormItem"
        label-style="top"
      >
        <mt-select
          :data-source="[
            { text: $t('是'), value: 1 },
            { text: $t('否'), value: 0 }
          ]"
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          :show-clear-button="true"
          @change="handleFormItemChange($event, formItem)"
          :fields="{ text: 'text', value: 'value' }"
          :placeholder="$t(formItem.form.label)"
        />
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.field"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'allocationRatio' && !formItem.form.hideFormItem"
        label-style="top"
      >
        <AllocationRatio
          :value="form[formItem.form.modalName]"
          @input="(v) => (form[formItem.fieldCode] = v)"
          :readonly="formItem.form.readonly"
        />
      </mt-form-item>

      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.field"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'selectedItemCode'"
        label-style="top"
      >
        <SelectedItemCode
          :value="form[formItem.form.modalName]"
          @input="(v) => (form[formItem.fieldCode] = v)"
          @change="pcbChange"
          :readonly="formItem.form.readonly"
        />
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.field"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'getComponentCode'"
        label-style="top"
      >
        <div class="get-component-code" v-if="showAueryItemBom" @click="queryItemBom">
          {{ $t('获取组件号') }}
        </div>
        <div class="get-component-code" v-else>{{ $t('已获取') }}</div>
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.field"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'debounce-select' && !formItem.form.hideFormItem"
        label-style="top"
      >
        <debounce-filter-select
          v-model="form[formItem.form.modalName]"
          :request="getCurrentEmployees"
          :data-source="currentEmployees"
          @change="handleFormItemChange($event, formItem)"
          :show-clear-button="false"
          :fields="{ text: 'text', value: 'uid' }"
          :placeholder="$t(formItem.form.label)"
          popup-width="300px"
          width="100%"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :key="index"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'null'"
        label-style="top"
      >
        <!-- 为了布局，空的占位符-->
      </mt-form-item>
    </template>
    <slot :records="fieldDefines"></slot>
  </mt-form>
</template>

<script>
import utils from '@/utils/utils'
import { cloneDeep } from 'lodash'
// 这个组件跟着需求一步步做的，到后面就非常恶了心，并不比直接页面写死然后if-else好
import DateTimePicker from '@/components/DateTimePicker'
import AllocationRatio from 'ROUTER_PURCHASE_RFX/components/AllocationRatio'
import debounceFilterSelect from 'ROUTER_PURCHASE_RFX/components/debounceFilterSelect'
import ItemTemplate from './ItemTemplate'
import SelectedItemCode from 'COMPONENTS/NormalEdit/selectPcbCode'
export default {
  name: 'FormGenerator',
  props: {
    fieldDefines: {
      type: Array,
      required: true
    },
    formFieldConfig: {
      type: Object,
      required: true
    },
    formRules: {
      type: Object,
      required: false,
      default: () => {}
    },
    userSelect: {
      type: Boolean,
      default: false
    },
    rfxCode: {
      type: String,
      default: ''
    }
  },
  components: {
    DateTimePicker,
    AllocationRatio,
    debounceFilterSelect,
    SelectedItemCode
  },
  data() {
    return {
      selectItemTemplate: () => {
        return {
          template: ItemTemplate
        }
      },
      showAueryItemBom: true,
      form: {},
      oldForm: {},
      rules: {},
      currentEmployees: [],
      sourcingExpandList: [],
      pcbData: {
        sourcingObjType: '',
        siteCode: ''
      },
      showBadgeList: ['priceControl', 'priceDirectionControl', 'accountingMode'],
      badgeMesMap: {
        priceControl: {
          left: '70px',
          msg: `1. 整单报价：供方报价时同一标案的所有明细行需全部报价;
            2. 首次整单报价：供方报价时，首次填写需同一标案所有明细行全部报价，重新报价时可只更改某一行;
            3. 无限制：供方报价时同一标案可自由选择报那些行；
          `
        },
        priceDirectionControl: {
          left: '100px',
          msg: `1. 整单控制：同一标案多标的时，按照总价控制供方报价的递增/递减；
            2. 单行控制：同一标案多标的时，按照单行控制供方报价的递增/递减；
          `
        },
        accountingMode: {
          left: '70px',
          msg: `1. 部分中标：定标时，标案下的每一行标的可选择多个供应商中标；
            2. 整单中标：一个标案只能一个供应商中标；
          `
        }
      }
    }
  },
  watch: {
    fieldDefines: {
      immediate: true,
      handler() {
        console.log('fieldDefines', this.fieldDefines)
        this.handlePropsChange()
      }
    },
    formRules: {
      immediate: true,
      handler() {
        console.log('formRules', this.formRules)
      }
    }
  },
  mounted() {
    if (this.userSelect) {
      this.getCurrentEmployees({ text: '' })
    }
  },
  methods: {
    formatDate(e, format) {
      let _tempDate = null
      if (e) {
        _tempDate = utils.formatTime(new Date(parseInt(e)), format)
      }
      return _tempDate
    },
    queryItemBom() {
      if (!this.form.pcbCode) {
        this.$toast({
          content: this.$t('请先选择PCB！'),
          type: 'warning'
        })
        return
      }
      const params = {
        pcbCode: this.form.pcbCode,
        // rfxCode: this.rfxCode,
        siteCode: this.pcbData.siteCode
        // sourcingObjType: sessionStorage.getItem('sourcingObjType')
      }
      this.$store.commit('startLoading')
      this.$API.rfxDetail
        .getMaterialInfoByPCB(params)
        .then((r) => {
          this.$store.commit('endLoading')
          if (r.code === 200) {
            this.showAueryItemBom = false
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    pcbChange(e) {
      console.log('pcbChange', e)
      this.showAueryItemBom = true
    },
    handlePropsChange() {
      if (this.fieldDefines.length && this.formFieldConfig) {
        let _form = cloneDeep(this.form)
        for (const field of this.fieldDefines) {
          const config = this.formFieldConfig[field.fieldCode]
          if (config) {
            if (field.fieldCode === 'sourcingExpand') {
              this.$set(field, 'form', {
                col: 2,
                handler: () => {},
                readonly: field.readonly,
                hideFormItem: field.hideFormItem,
                ...config,
                required: !!field.required,
                label: field.fieldName,
                modalName: field.fieldCode,
                dataSource: config.dataSource || []
              })
            } else {
              const formVal = {
                col: 4,
                handler: () => {},
                readonly: field.readonly,
                hideFormItem: field.hideFormItem,
                ...config,
                required: !!field.required,
                label: field.fieldName,
                modalName: field.fieldCode,
                dataSource: config.dataSource || []
              }
              // 技术标相关的字段，在操作“是否需要技术投标”这个选择时有关联
              const tecFields = [
                'tecOpenBidTime',
                'tecScoreEndTime',
                'tecBidStartTime',
                'biddingPromotion'
              ]
              if (tecFields.includes(field.fieldCode)) {
                formVal.hideFormItem = this.formFieldConfig['needTecBid']?.defaultValue() === '0'
              }
              this.$set(field, 'form', formVal)
            }
            if (config.defaultValue) {
              if (config.type === 'select' && !config.api) {
                config.dataSource = Array.isArray(config?.dataSource) ? config?.dataSource : []
                this.form[field.fieldCode] = config.dataSource.find(
                  (item) => item.value === config.defaultValue()
                )?.value
              } else {
                this.form[field.fieldCode] = config.defaultValue()
              }
            }
            if (config.api) {
              config.api.then((data) => {
                this.$set(field.form, 'dataSource', data)
                if (config.defaultValue) {
                  this.form[field.fieldCode] = data.find(
                    (item) => item.value === config.defaultValue() || item?.data?.isDefault === 1
                  )?.value
                }
                if (field.fieldCode === 'businessTypeName' && this.form[field.fieldCode]) {
                  this.businessTypeNameChangeHandler({
                    value: this.form[field.fieldCode]
                  })
                }
              })
            }
            if (field.required) {
              this.$set(this.formRules, field.fieldCode, [
                {
                  required: true,
                  message: this.$t('请输入') + field.fieldName
                }
              ])
            }
            // if (config.valid) {
            //   this.$set(this.rules, field.fieldCode, config.valid);
            // }
            _form[field.fieldCode] = config.defaultValue ? config.defaultValue() : ''
            // this.$set(
            //   this.form,
            //   field.fieldCode,
            //   config.defaultValue ? config.defaultValue() : ""
            // );
          } else {
            field.form = {}
          }
        }
        this.form = _form
        this.oldForm = { ..._form }
      }
    },
    parentGetFormData() {
      return new Promise((resolve, reject) => {
        this.$refs.form.validate((valid) => {
          valid ? resolve(this.form) : reject()
        })
      })
    },
    handleFormItemChange(event, formItem) {
      console.log('handleFormItemChange', event, formItem)
      if (formItem.fieldCode === 'needTecBid') {
        const { value } = event
        const readonly = !(Number(value) === 1)
        const fields = ['tecOpenBidTime', 'tecScoreEndTime', 'tecBidStartTime', 'biddingPromotion']
        for (const field of fields) {
          const row = this.fieldDefines.find((item) => item.fieldCode === field)
          if (row) {
            // 是否为非采, 若是非采 fields 这几个字段就一直不允许编辑
            const isFc = sessionStorage.getItem('rfxGeneralType') == '2'
            row.readonly = readonly
            row.form.readonly = isFc ? true : readonly
            row.form.hideFormItem = readonly
          }
        }
      }
      if (formItem.fieldCode == 'sourcingExpand') {
        let expandSaveRequestList = []
        event.value?.forEach((v) => {
          formItem.form.dataSource.forEach((x) => {
            if (v == x.value) {
              expandSaveRequestList.push(x.data)
            }
          })
        })
        this.form.expandSaveRequestList = expandSaveRequestList
      } else if (formItem.fieldCode == 'sourcingObj') {
        this.pcbData.sourcingObjType = event.itemData?.data?.sourcingObjType
        sessionStorage.setItem('sourcingObjType', this.pcbData.sourcingObjType)
      } else if (formItem.fieldCode == 'siteName') {
        this.pcbData.siteCode = event.itemData?.data?.siteCode
        const sourcingExpandConf = this.fieldDefines.find(
          (item) => item.fieldCode === 'sourcingExpand'
        )
        if (sourcingExpandConf) {
          sourcingExpandConf.form.dataSource = this.sourcingExpandList.filter(
            (x) => x.data.siteCode !== event.itemData?.data?.siteCode
          )
          this.form.sourcingExpand = []
        }
      }
      if (formItem.form.interactionModel && formItem.form.dataSource.length) {
        this.$set(
          this.form,
          formItem.form.interactionModel,
          formItem.form.dataSource.find((item) => item.value === event.value)[
            formItem.form.interactionModel
          ]
        )
      }
      if (formItem.fieldCode === 'companyName') {
        this.companyNameChangeHandler(event.itemData)
      }
      if (formItem.fieldCode === 'purOrgName') {
        this.purOrgNameChangeHandler(event.itemData)
      }
      formItem.form.handler(event, formItem, this)
    },
    handleFormSelectItemChange(event, formItem) {
      if (this[formItem.fieldCode + 'ChangeHandler']) {
        this[formItem.fieldCode + 'ChangeHandler'](event.itemData)
      }
      setTimeout(() => {
        document.getElementById('createHiddenInput')?.focus()
      }, 0)
    },
    //招标方式 biddingMode字段联动
    //公开 合作伙伴公开，供应商选择范围为‘所有供应商’，只读
    biddingModeChangeHandler(itemData) {
      let { value } = itemData
      const supplierSelectionRangeConf = this.fieldDefines.find(
        (item) => item.fieldCode === 'supplierSelectionRange'
      )
      if (['open', 'supplierOpen'].includes(value)) {
        if (supplierSelectionRangeConf) {
          this.form['supplierSelectionRange'] = 'all_supplier'
          supplierSelectionRangeConf.form.readonly = true
        }
      } else if (value == 'target') {
        this.form['supplierSelectionRange'] = 'potential_qualified'
        supplierSelectionRangeConf.form.readonly = false
      } else {
        if (supplierSelectionRangeConf) {
          supplierSelectionRangeConf.form.readonly = false
        }
      }
    },
    async companyNameChangeHandler(itemData) {
      this.updatePurOrgName(itemData)
    },
    async purOrgNameChangeHandler(itemData) {
      this.updateSiteName(itemData)
    },
    siteNameChangeHandler(itemData) {
      const sourcingExpandConf = this.fieldDefines.find(
        (item) => item.fieldCode === 'sourcingExpand'
      )
      const purOrgNameConf = this.fieldDefines.find((item) => item.fieldCode === 'purOrgName')
      if (sourcingExpandConf) {
        // sourcingExpandConf.form.dataSource = this.sourcingExpandList.filter((x) => x.text !== text)
        this.updateSourcingExpand(
          itemData?.data?.siteCode,
          purOrgNameConf.form.dataSource[0]?.data?.purOrgCode
        )
      }
    },
    // 采购组织
    async updatePurOrgName(itemData) {
      if (this.$route.query?.pageType !== 'gf') {
        this.form.purOrgName = ''
        this.form.siteName = ''
      }
      const purOrgNameConf = this.fieldDefines.find((item) => item.fieldCode === 'purOrgName')
      sessionStorage.setItem('selectCompanyId', itemData.data.companyId)
      if (purOrgNameConf) {
        const res = await this.$API.masterData
          .permissionOrgList({
            orgId: itemData.data.companyId
          })
          .catch(() => {})
        if (res && res.data) {
          purOrgNameConf.form.dataSource = res.data.map((item) => ({
            text: item.organizationCode + '-' + item.organizationName,
            value: item.organizationCode,
            data: {
              purOrgName: item.organizationName,
              purOrgCode: item.organizationCode,
              purOrgId: item.id
            }
          }))
        }
      }
      const siteNameConf = this.fieldDefines.find((item) => item.fieldCode === 'siteName')
      if (siteNameConf) {
        siteNameConf.form.dataSource = []
      }
    },
    // 工厂
    async updateSiteName(itemData) {
      this.form.siteName = ''
      const siteNameConf = this.fieldDefines.find((item) => item.fieldCode === 'siteName')
      const purOrgNameConf = this.fieldDefines.find((item) => item.fieldCode === 'purOrgName')
      if (siteNameConf) {
        siteNameConf.form.dataSource = await this.getSiteNameDataSource(itemData.data.purOrgId)
        if (siteNameConf.form.dataSource?.length === 1) {
          // 如果工厂只有一条数据，则直接默认值
          this.form.siteName = siteNameConf.form.dataSource[0].value
          this.updateSourcingExpand(
            siteNameConf.form.dataSource[0]?.data?.siteCode,
            purOrgNameConf.form.dataSource[0]?.data?.purOrgCode
          )
        }
      }
    },
    async getSiteNameDataSource(organizationId) {
      const res = await this.$API.masterData
        .permissionSiteList({
          buOrgId: organizationId,
          companyId: sessionStorage.getItem('selectCompanyId'),
          orgLevelTypeCode: 'ORG06'
        })
        .catch(() => {})
      if (res.data == null) {
        return []
      }
      return res.data.map((item) => ({
        text: item.orgCode + '-' + item.orgName,
        value: item.orgName,
        data: {
          siteName: item.orgName,
          siteId: item.id,
          siteCode: item.orgCode
        }
      }))
    },
    async businessTypeNameChangeHandler(itemData) {
      console.log('businessTypeNameChangeHandler-')
      this.form.sourcingObj = ''
      const sourcingObjConf = this.fieldDefines.find((item) => item.fieldCode === 'sourcingObj')
      if (sourcingObjConf) {
        sourcingObjConf.form.dataSource = await this.getSourcingObjDataSource(itemData)
      }
    },
    async getSourcingObjDataSource(data) {
      console.log('getSourcingObjDataSource--', data)
      const res = await this.$API.businessConfig
        .getConfigList({
          page: { current: 1, size: 1000 },
          condition: 'and',
          rules: [
            {
              field: 'sourcingMode',
              type: 'string',
              operator: 'equal',
              value: this.form.sourcingMode
            },
            {
              field: 'businessTypeCode',
              type: 'string',
              operator: 'equal',
              value: data.value
            }
          ]
        })
        .catch(() => {})
      if (!res) {
        return []
      }
      return res.data.records
        .filter((item) => item.status === 1)
        .map((item) => ({
          text: item.sourcingObj,
          value: item.sourcingObj,
          data: {
            sourcingObj: item.sourcingObj,
            sourcingObjType: item.sourcingObjType,
            templateCode: item.templateCode
          }
        }))
    },
    // 获取 用户列表
    getCurrentEmployees(e) {
      const { text: fuzzyName } = e
      this.currentEmployees = []
      this.$API.masterData.getCurrentTenantEmployees({ fuzzyName }).then((res) => {
        const tmp = []
        res.data.forEach((item) => {
          tmp.push({
            ...item,
            text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`
          })
        })
        this.currentEmployees = tmp
      })
    },
    // 扩展
    async updateSourcingExpand(siteCode, purOrgCode) {
      this.form.sourcingExpand = []
      this.form.expandSaveRequestList = []
      const sourcingExpandConf = this.fieldDefines.find(
        (item) => item.fieldCode === 'sourcingExpand'
      )
      if (sourcingExpandConf) {
        const res = await this.$API.rfxDetail
          .purAllOrgWithSite({ fuzzyParam: siteCode, purOrgCode })
          .catch(() => {})
        if (res && res.data) {
          let dataSource = []
          res.data.forEach((v) => {
            v.siteOrgs?.forEach((x) => {
              dataSource.push({
                text: `${v.companyCode}-${v.businessOrganizationCode}-${v.businessOrganizationName}-${x.orgCode}-${x.orgName}`,
                value: v.companyCode + '+' + v.businessOrganizationCode + '+' + x.orgCode,
                data: {
                  companyCode: v.companyCode,
                  companyId: v.companyId,
                  companyName: v.companyName,
                  purOrgCode: v.businessOrganizationCode,
                  purOrgId: v.id,
                  purOrgName: v.businessOrganizationName,
                  siteCode: x.orgCode,
                  siteId: x.id,
                  siteName: x.orgName
                }
              })
            })
          })
          this.sourcingExpandList = dataSource
          sourcingExpandConf.form.dataSource = dataSource
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.sourcing-expand {
  /deep/ .e-multiselect {
    height: 30px;
    overflow: hidden;
    .e-multi-select-wrapper {
      display: flex;
      flex-flow: nowrap;
      overflow: hidden;
      .e-searcher {
        width: 10px;
      }
      .e-chips {
        width: 80px;
      }
      .e-chips-collection {
        height: 30px;
        overflow-y: auto;
        overflow-x: hidden;
      }
    }
  }
}
.form-generator-form {
  display: flex;
  flex-flow: row wrap;
  row-gap: 20px;
  margin: 0 -10px 20px 0;
  .get-component-code {
    color: #00469c;
    cursor: pointer;
    line-height: 60px;
    font-size: 14px;
    font-weight: 600;
  }
  .form-generator-form-item {
    padding-left: 10px;
    padding-right: 10px;
    margin-bottom: 0;
  }

  .form-generator-item-col-4 {
    display: block;
    flex: 0 0 25%;
    max-width: 25%;
  }
  .form-generator-item-col-2 {
    display: block;
    flex: 0 0 50%;
    max-width: 50%;
  }
  .form-generator-item-col-1 {
    display: block;
    flex: 0 0 100%;
    max-width: 100%;
    /deep/ .label {
      width: 118px !important;
    }
  }
}
.mt-form-item {
  .mt-input,
  .mutliselect-container {
    width: 100%;
  }
  /deep/ .mt-date-time-picker,
  .input-number {
    width: 100%;
  }
  /deep/ .mt-input-number {
    width: 100%;
    input {
      padding-right: 0 !important;
    }
  }
  /deep/ .label {
    white-space: unset;
  }
}
// .mt-form-item {
//   /deep/ .label {
//     font-weight: normal;
//   }
// }

// .mt-form-item[disabled] {
//   /deep/ .label {
//     color: #9a9a9a !important;
//   }

//   /deep/ input,
//   /deep/ .e-disabled {
//     background: transparent !important;
//   }

//   /deep/ input {
//     color: #9a9a9a;
//     border-bottom-color: #9a9a9a !important;
//   }
// }
/deep/ input[disabled] {
  background: #fafafa;
  background-image: none;
  background-position: initial;
  background-repeat: no-repeat;
  background-size: 0;
  border-color: rgba(0, 0, 0, 0.06);
  color: rgba(0, 0, 0, 0.38);
  width: 100%;
}
</style>
<style>
::placeholder {
  color: #9a9a9a;
  font-size: 12px;
}
</style>
