import { pageConfig } from './index'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  data() {
    return {
      pageConfig: pageConfig(this.$API.rfxList.getRFXList, this.biddingSource),
      biddingSource: 'rfq' //单据类型
      // { text: this.$t("询报价"), value: "rfq" },
      // { text: this.$t("直接定价"), value: "direct_pricing" },
      // { text: this.$t("招投标"), value: "invite_bids" },
      // { text: this.$t("竞价"), value: "bidding_price" },
    }
  },
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (e.toolbar.id == 'Add') {
        this.handleCreateRFX()
        return
      }

      let idList = []
      _selectGridRecords.map((item) => {
        idList.push(item.id)
      })
      if (e.toolbar.id == 'zAdd') {
        this.handleZAddFn() //直接定价 - 新增
        return
      } else if (e.toolbar.id == 'xAdd') {
        this.handleXAddFn() //系列物料 - 新增
        return
      } else if (e.toolbar.id == 'bdAdd') {
        this.handleBdAddFn() // 白电直接定价 - 新增
        return
      } else if (e.toolbar.id == 'zDel') {
        this.handleZDelFn(_selectGridRecords, idList) //直接定价 - 删除
        return
      } else if (e.toolbar.id == 'xDel') {
        this.handleXDelFn(_selectGridRecords, idList) //系列物料 - 删除
        return
      } else if (e.toolbar.id == 'bdDel') {
        this.handleBdDelFn(_selectGridRecords, idList) //白电直接定价 - 删除
        return
      } else if (e.toolbar.id == 'zSubmit') {
        this.handleZSubmitFn(_selectGridRecords) //系列物料 - 提交
        return
      } else if (e.toolbar.id == 'xSubmit') {
        this.handleXSubmitFn(_selectGridRecords) //直接定价 - 提交
        return
      } else if (e.toolbar.id == 'bdSubmit') {
        this.handleBdSubmitFn(_selectGridRecords) //白电直接定价 - 提交
        return
      } else if (e.toolbar.id == 'bdClose') {
        this.handleBdClose(idList, _selectGridRecords) //白电直接定价 - 关闭
        return
      } else if (e.toolbar.id == 'ExportRfx') {
        this.handleExport(this.$API.rfxList.exportRFXList, e.toolbar.id) //询价 - 导出
        return
      } else if (e.toolbar.id == 'zExportRfx') {
        this.handleExport(this.$API.rfxList.exportRFXListZ, e.toolbar.id) //直接定价 - 导出
        return
      } else if (e.toolbar.id == 'bdExportRfx') {
        this.handleExport(this.$API.rfxList.exportRFXListBD, e.toolbar.id) //白电直接定价 - 导出
        return
      } else if (e.toolbar.id == 'xExportRfx') {
        this.handleExport(this.$API.rfxList.exportRFXListX, e.toolbar.id) //系列物料定价 - 导出
        return
      }

      if (_selectGridRecords.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Delete') {
        this.handleDeleteRFX(_selectGridRecords)
        return
      }
      if (e.toolbar.id == 'copyRFX') {
        if (_selectGridRecords.length > 1) {
          this.$toast({
            content: this.$t('复制操作只能选择一行'),
            type: 'warning'
          })
          return
        }
        this.handleCopyRFX(_selectGridRecords)
      }
      let _id = e.gridRef.getMtechGridRecords().map((item) => item.id)
      if (e.toolbar.id == 'closeRFX') {
        this.handleCloseRFX(_id)
      }
      if (e.toolbar.id == 'closeSourcing') {
        this.handleCloseSourcing(_id)
      } else if (e.toolbar.id == 'pauseDan') {
        this.handleDealRFX(_id, 'pauseRFXHeader')
      } else if (e.toolbar.id == 'activeDan') {
        this.handleDealRFX(_id, 'activeRFXHeader')
      }
    },
    handleExport(API, toolbarId) {
      // 先要点击“查询”按钮才能取得queryBuilderRules
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let defaultRules = []
      if (toolbarId == 'ExportRfx') {
        defaultRules = [
          {
            field: 'sourcingMode',
            label: '',
            operator: 'equal',
            type: 'string',
            value: this.biddingSource
          }
        ]
      } else if (toolbarId == 'zExportRfx') {
        defaultRules = [
          {
            field: 'decidePriceType',
            operator: 'in',
            value: ['0']
          }
        ]
      }
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules,
        defaultRules
      } // 筛选条件
      API(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleClickCellTool(e) {
      if (e.tool.id === 'view') {
        this.handleRedirectDetailPage(e)
      } else if (e.tool.id === 'submit_project') {
        this.handleSubmitProject([e.data.id])
      } else if (e.tool.id === 'publish_zhaobiao') {
        this.handlePublishZhao([e.data.id])
      } else if (e.tool.id === 'end_bao') {
        this.handleEndBao(e.data.id)
      } else if (e.tool.id === 'open_biao') {
        this.handleOpenBiao(e.data)
      } else if (e.tool.id === 'ping_biao') {
        this.handlePingBiao(e.data)
      } else if (e.tool.id === 'submit_ding') {
        this.handleSubmitDing([e.data.id])
      }
    },
    // 单元格title
    handleClickCellTitle(e) {
      if (e.field === 'rfxCode') {
        this.handleRedirectDetailPage(e, 0)
      } else if (e.field === 'costModelQuote') {
        this.handleRedirectDetailPage(e, 1)
      } else if (e.field === 'oaApplyUrl') {
        if (!e.data.oaApplyUrl) {
          this.$toast({
            content: this.$t('暂无申请单'),
            type: 'error'
          })
        } else {
          window.open(e.data.oaApplyUrl)
        }
      } else if (e.field == 'pointNo') {
        let decidePriceType = e.data?.decidePriceType
        let extObj = {}
        let name = ''
        // this.currentTab === 1 ? 'direct-pricing-detail-tv' : 'series-item-pricing-detail'
        if (this.currentTab === 1) {
          name = 'direct-pricing-detail-tv'
        } else if (this.currentTab === 2) {
          name = e.data?.status == 13 ? 'direct-price-detail' : 'direct-price'
          decidePriceType = 0
          extObj =
            e.data?.status == 13
              ? { pointNo: e.data.pointNo, isView: true }
              : { pointNo: e.data.pointNo }
        } else {
          name = 'series-item-pricing-detail'
        }
        let query = Object.assign(
          {},
          {
            id: e.data.id,
            decidePriceType,
            status: e.data.status,
            timeStamp: new Date().getTime()
          },
          extObj
        )
        this.$router.push({
          name: name,
          query
        })
      }
    },
    // 点击 tab
    handleSelectTab(e) {
      this.currentTab = e
    },
    handleRedirectDetailPage(e, bool) {
      // this.$router.push({
      //   path: "/sourcing/bid-hall/hall-detail",
      //   query: {
      //     source: this.biddingSource,
      //     rfxId: e.data.id,
      //     status: e.data.status,
      //     key: this.$utils.randomString(),
      //     calculation: bool,
      //   },
      // });
      this.$router.push(
        `/sourcing/bid-hall/hall-detail?source=${this.biddingSource}&rfxId=${e.data.id}&status=${
          e.data.status
        }&companyCode=${
          e.data.companyCode
        }&key=${this.$utils.randomString()}&calculation=${bool}&sourcingObjType=${
          e.data.sourcingObjType
        }&rfxGeneralType=${e.data.rfxGeneralType}`
      )
    },
    //创建RFX
    handleCreateRFX() {
      this.$router.push(
        `/sourcing/bid-hall/create?source=${this.biddingSource}&key=${this.$utils.randomString()}`
      )
    },
    //复制RFX
    handleCopyRFX(_selectGridRecords) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认复制数据？')
        },
        success: () => {
          this.$API.rfxList
            .copyAdd({
              rfxId: _selectGridRecords[0].id
            })
            .then((res) => {
              this.$toast({
                content: this.$t('成功执行‘复制’操作'),
                type: 'success'
              })
              if (res?.data?.itemErrorInfo) {
                this.$toast({
                  content: res?.data?.itemErrorInfo,
                  type: 'warning'
                })
              }
              if (res?.data?.supplierErrorInfo) {
                this.$toast({
                  content: res?.data?.supplierErrorInfo,
                  type: 'warning'
                })
              }
              this.$refs.templateRef.getCurrentTabRef().grid.clearFiltering()
              this.$nextTick(() => {
                this.$refs.templateRef.refreshCurrentGridData()
              })
            })
        }
      })
    },
    //删除RFX
    handleDeleteRFX(_selectGridRecords) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          let idList = []
          for (let item of _selectGridRecords) {
            idList.push(item.id)
          }
          this.$API.rfxList.deleteRFXHeader({ idList: idList }).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    //关闭RFX
    handleCloseRFX(_id) {
      this.$dialog({
        data: {
          title: this.$t('关闭RFX'),
          message: this.$t('是否确认关闭RFX？'),
          confirm: () => this.$API.rfxList.closeRFXHeader({ idList: _id })
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //关闭询价单
    handleCloseSourcing(_id) {
      this.$dialog({
        data: {
          title: this.$t('关闭询价单'),
          message: this.$t('是否确认关闭询价单？'),
          confirm: () => this.$API.rfxList.closeSourcing({ idList: _id })
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 激活/暂停 询价单
    handleDealRFX(ids, url) {
      this.$API.rfxList[url]({ idList: ids }).then(() => {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },
    // 操作：提交立项
    handleSubmitProject(ids) {
      this.$API.rfxList.saveProject({ idList: ids }).then(() => {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },
    // 操作：发布询价招标
    handlePublishZhao(ids) {
      this.$API.rfxList.publishBid({ idList: ids }).then(() => {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },
    // 操作：结束报价
    handleEndBao(id) {
      this.$API.rfxList.endNowTurn({ id: id }).then(() => {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },
    // 操作：开标
    handleOpenBiao(row) {
      let openForm = {
        openPwd: '',
        rfxRoundId: row.currentRound,
        rfxId: row.id
      }
      this.$API.inquiryBidding.biddingOpen(openForm).then(() => {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },
    // 操作：评标
    // handlePingBiao(ids) {},
    // 操作：提交定点
    handleSubmitDing(ids) {
      this.$API.rfxList.submitRFXPoint({ idList: ids }).then(() => {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        this.$refs.templateRef.refreshCurrentGridData()
      })
    }
  }
}
