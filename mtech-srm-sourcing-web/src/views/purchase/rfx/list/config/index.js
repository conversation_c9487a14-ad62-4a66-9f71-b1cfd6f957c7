import { i18n, permission } from '@/main.js'
import {
  transferStatusList,
  RFX_STATUS,
  approveStatusList,
  rfxDocStatusList,
  bidDocStatusList
} from '@/constants'

const colortList = {
  2: 'title-#ED5633',
  3: 'custom-bg-yellow',
  13: 'title-#ED5633'
}
// 直接定价
export const zToolBar = [
  { id: 'zAdd', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'zDel', icon: 'icon_solid_Cancel', title: i18n.t('删除') },
  { id: 'zSubmit', icon: 'icon_solid_Submit', title: i18n.t('提交') },
  { id: 'zExportRfx', icon: 'icon_solid_edit', title: i18n.t('导出') }
]
export const zColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        '-1': i18n.t('已失效'),
        0: i18n.t('草稿'),
        1: i18n.t('审批中'),
        2: i18n.t('审批通过'),
        3: i18n.t('审批驳回')
      }
    }
  },
  {
    field: 'pointNo',
    width: '230',
    headerText: i18n.t('定价单号'),
    cssClass: 'field-content'
  },
  {
    field: 'title',
    headerText: i18n.t('标题')
  },
  {
    field: 'decidePriceType',
    headerText: i18n.t('定价单类型'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('直接定价'), 1: i18n.t('寻源结果定价'), 2: i18n.t('系列物料定价') }
    }
  },
  {
    field: 'priceObjectName',
    headerText: i18n.t('寻源对象')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司')
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织')
  },
  {
    field: 'purName',
    headerText: i18n.t('采购员')
  },
  {
    width: '1',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '1',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '1',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '1',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    type: 'date',
    format: 'yyyy-MM-dd HH:mm:ss',
    searchOptions: {
      elementType: 'date-range',
      operator: 'between',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return e.map((x, i) => {
          if (i === 1) {
            return Number(new Date(x.toString().replace('00:00:00', '23:59:59')))
          }
          return Number(new Date(x.toString()))
        })
      }
    }
  }
]

// 白电直接定价
export const bdToolBar = [
  { id: 'bdAdd', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'bdDel', icon: 'icon_solid_Cancel', title: i18n.t('删除') },
  { id: 'bdSubmit', icon: 'icon_solid_Submit', title: i18n.t('下发供应商确认') },
  { id: 'bdClose', icon: 'icon_solid_Closeorder', title: i18n.t('关闭') },
  { id: 'bdExportRfx', icon: 'icon_solid_edit', title: i18n.t('导出') }
]
export const bdColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('草稿'),
        1: i18n.t('供应商确认中'),
        2: i18n.t('供应商部分确认'),
        3: i18n.t('供应商部分驳回'),
        4: i18n.t('供应商已确认'),
        5: i18n.t('供应商已驳回'),
        6: i18n.t('审批中'),
        7: i18n.t('审批拒绝'),
        8: i18n.t('SAP处理中'),
        9: i18n.t('推送SAP失败'),
        10: i18n.t('已完成'),
        11: i18n.t('审批通过'),
        12: i18n.t('审批废弃'),
        13: i18n.t('已关闭')
      }
    }
  },
  {
    field: 'pointNo',
    headerText: i18n.t('定价单号'),
    cssClass: 'field-content'
  },
  {
    field: 'title',
    headerText: i18n.t('标题')
  },
  {
    field: 'decidePriceType',
    headerText: i18n.t('定价单类型'),
    valueConverter: {
      type: 'map',
      map: { 6: i18n.t('白电直接定价'), 1: i18n.t('寻源结果定价') }
    }
  },
  {
    field: 'priceClassification',
    headerText: i18n.t('价格分类'),
    valueConverter: {
      type: 'map',
      map: { 3: i18n.t('暂估价格'), 4: i18n.t('执行价格') }
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司')
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织')
  },
  {
    field: 'purName',
    headerText: i18n.t('采购员')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('时间'),
    type: 'date',
    format: 'yyyy-MM-dd HH:mm:ss',
    searchOptions: {
      elementType: 'date-range',
      operator: 'between',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return e.map((x, i) => {
          if (i === 1) {
            return Number(new Date(x.toString().replace('00:00:00', '23:59:59')))
          }
          return Number(new Date(x.toString()))
        })
      }
    }
  }
]
// 系列物料定价
export const xToolBar = [
  { id: 'xAdd', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'xDel', icon: 'icon_solid_Cancel', title: i18n.t('删除') },
  { id: 'xSubmit', icon: 'icon_solid_Submit', title: i18n.t('提交') },
  { id: 'xExportRfx', icon: 'icon_solid_edit', title: i18n.t('导出') }
]
export const xColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('草稿'),
        1: i18n.t('已保存'),
        2: i18n.t('已提交'),
        3: i18n.t('审批通过'),
        4: i18n.t('审批驳回')
      }
    }
  },
  {
    field: 'pointNo',
    headerText: i18n.t('定价单号'),
    cssClass: 'field-content'
  },
  {
    field: 'title',
    headerText: i18n.t('标题')
  },
  {
    field: 'decidePriceType',
    headerText: i18n.t('定价单类型'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('直接定价'),
        1: i18n.t('寻源结果定价'),
        2: i18n.t('系列物料定价')
      }
    }
  },
  {
    field: 'priceObjectName',
    headerText: i18n.t('定价对象')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司')
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织')
  },
  {
    field: 'purName',
    headerText: i18n.t('采购员')
  },
  {
    width: '1',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '1',
    field: 'texture',
    headerText: i18n.t('物料材质')
  },
  {
    width: '1',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '1',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    type: 'date',
    format: 'yyyy-MM-dd HH:mm:ss',
    searchOptions: {
      elementType: 'date-range',
      operator: 'between',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return e.map((x, i) => {
          if (i === 1) {
            return Number(new Date(x.toString().replace('00:00:00', '23:59:59')))
          }
          return Number(new Date(x.toString()))
        })
      }
    }
  }
]
// 询价
const toolbar = [
  'add',
  'delete',
  {
    id: 'copyRFX',
    icon: 'icon_solid_Copy',
    title: i18n.t('复制')
  },
  {
    id: 'closeRFX',
    icon: 'icon_solid_Closeorder',
    title: i18n.t('关闭RFX')
  },
  {
    id: 'closeSourcing',
    icon: 'icon_solid_Closeorder',
    title: i18n.t('关闭询价单'),
    permission: ['O_02_1766']
  },
  { id: 'ExportRfx', icon: 'icon_solid_edit', title: i18n.t('导出') }
  // {
  //   id: "pauseDan",
  //   icon: "icon_solid_Pauseorder",
  //   title: i18n.t("暂停单据"),
  // },
  // {
  //   id: "activeDan",
  //   icon: "icon_solid_pushorder",
  //   title: i18n.t("激活单据"),
  // },
]

const columnDataJudge = function (sourcingMode) {
  if (sourcingMode == 'rfq') {
    return [
      {
        width: '50',
        type: 'checkbox',
        showInColumnChooser: false,
        allowResizing: false,
        customAttributes: {
          class: 'sticky-col-0'
        }
      },
      {
        width: '180',
        field: 'rfxCode',
        headerText: i18n.t('询价招标单编号'),
        cssClass: 'field-content',
        allowResizing: false,
        customAttributes: {
          class: 'sticky-col-1'
        }
      },
      {
        width: '160',
        field: 'rfxName',
        headerText: i18n.t('项目名称')
      },
      {
        width: '120',
        field: 'sourcingObj',
        headerText: i18n.t('询价对象')
      },
      {
        width: '100',
        field: 'sourcingType',
        headerText: i18n.t('询价类型'),
        valueConverter: {
          type: 'map',
          map: {
            new_products: i18n.t('新品'),
            second_inquiry: i18n.t('二次'),
            exist: i18n.t('已有'),
            unlimited: i18n.t('不限')
          }
        }
      },
      {
        width: '100',
        field: 'priceClassification',
        headerText: i18n.t('价格分类'),
        valueConverter: {
          type: 'map',
          map: {
            predict_price: i18n.t('暂估价格'),
            srm_price: i18n.t('SRM价格'),
            execute_price: i18n.t('执行价格'),
            basic_price: i18n.t('基价')
          }
        }
      },
      {
        width: '180',
        field: 'strategyConfigName',
        headerText: i18n.t('询价策略')
      },
      {
        field: 'costModelQuote',
        headerText: i18n.t('成本测算'),
        cssClass: 'field-content',
        valueConverter: {
          type: 'function',
          filter: (e) => {
            if (e == 0) {
              return ''
            } else if (e == 1) {
              return i18n.t('成本测算')
            } else {
              return ''
            }
          }
        }
        // visibleCondition: (data) => {
        //   return data["costModelQuote"] == 1;
        // },
      },
      {
        field: 'status',
        headerText: i18n.t('单据状态'),
        searchOptions: {
          operator: 'equal'
        },
        valueConverter: {
          type: 'map',
          map: Object.keys(RFX_STATUS).map(function (i) {
            return {
              text: RFX_STATUS[i],
              value: +i,
              cssClass: 'title-#6386c1'
            }
          })
        }
      },
      {
        width: '130',
        field: 'approveStatus',
        headerText: i18n.t('审批状态'),
        searchOptions: {
          elementType: 'select',
          dataSource: approveStatusList,
          fields: { text: 'label', value: 'status' }
        },
        valueConverter: {
          type: 'map',
          map: Object.keys(approveStatusList).map(function (i) {
            let _class = 'custom-9A9A9A custom--color'
            let _key = Number(i)
            switch (_key) {
              case -2:
                _class = 'custom-ED5633 custom--color'
                break
              case 2:
                _class = 'custom-6386C1 custom--color'
                break
              case 1:
                _class = 'custom-EDA133 custom--color'
                break
              default:
                _class = 'custom-9A9A9A custom--color'
            }
            return {
              text: approveStatusList[_key],
              value: +_key,
              cssClass: _class
            }
          })
        }
      },
      {
        width: '100',
        field: 'transferStatus',
        headerText: i18n.t('当前阶段'),
        valueConverter: {
          type: 'map',
          map: Object.keys(transferStatusList).map(function (i) {
            return {
              text: transferStatusList[i],
              value: +i,
              cssClass: 'title-#6386c1'
            }
          })
        },
        cellTools: [
          {
            id: 'submit_project',
            // icon: "icon_solid_Submit",
            title: i18n.t('提交立项'),
            visibleCondition: (data) => {
              return data['transferStatus'] == 0 && data['status'] != -2 && sourcingMode !== 'rfq'
            }
          },
          {
            id: 'publish_zhaobiao',
            // icon: "icon_solid_export",
            title: i18n.t('发布询价招标'),
            visibleCondition: (data) => {
              return data['transferStatus'] == 1
            }
          },
          {
            id: 'end_bao',
            // icon: "icon_solid_Submit",
            title: i18n.t('结束报价'),
            visibleCondition: (data) => {
              return data['transferStatus'] == 5
            }
          },
          {
            id: 'open_biao',
            // icon: "icon_solid_export",
            title: i18n.t('开标'),
            visibleCondition: (data) => {
              return data['transferStatus'] == 6
            }
          },
          // {
          //   id: "ping_biao",
          //   //icon: "icon_solid_export",
          //   title: i18n.t("评标"),
          //   visibleCondition: (data) => {
          //     return data["transferStatus"] == 7;
          //   },
          // },
          {
            id: 'submit_ding',
            // icon: "icon_solid_export",
            title: i18n.t('提交定点'),
            visibleCondition: (data) => {
              return data['transferStatus'] == 10
            }
          }
        ]
      },
      {
        width: '100',
        field: 'docStatus',
        headerText: i18n.t('状态'),
        valueConverter: {
          type: 'map',
          map: Object.keys(rfxDocStatusList).map(function (i) {
            return {
              text: rfxDocStatusList[i],
              value: +i,
              cssClass: i in colortList ? colortList[i] : 'title-#6386c1'
            }
          })
        }
      },
      {
        width: '100',
        field: 'currentRound',
        headerText: i18n.t('当前轮次'),
        ignore: true,
        allowFiltering: false
      },
      {
        width: '150',
        field: 'quoteEndTime',
        headerText: i18n.t('报价截止时间'),
        format: 'yyyy-MM-dd HH:mm:ss',
        type: 'date',
        ignore: true,
        allowFiltering: false
      },
      {
        width: '150',
        field: 'companyName',
        headerText: i18n.t('公司')
      },
      {
        width: '100',
        field: 'purOrgName',
        headerText: i18n.t('采购组织')
      },
      {
        width: '100',
        field: 'docSource',
        headerText: i18n.t('单据来源'),
        valueConverter: {
          type: 'map',
          map: {
            0: i18n.t('手动创建'),
            1: i18n.t('引用申请')
          }
        }
      },
      {
        width: '100',
        field: 'sourceDocCode',
        headerText: i18n.t('来源单号')
      },
      {
        width: '120',
        field: 'oaApplyUrl',
        headerText: i18n.t('OA申请单查看'),
        cssClass: 'field-content',
        valueConverter: {
          type: 'function',
          filter: (e) => {
            if (!e) {
              return '--'
            } else {
              return i18n.t('查看')
            }
          }
        }
      },
      {
        width: '100',
        field: 'responseValue',
        headerText: i18n.t('报价响应'),
        ignore: true,
        allowFiltering: false
      },
      {
        width: '100',
        field: 'biddingMode',
        headerText: i18n.t('招标方式'),
        valueConverter: {
          type: 'map',
          map: {
            open: i18n.t('公开'),
            target: i18n.t('邀请')
            // supplierOpen: i18n.t('合作伙伴公开')
          }
        }
      },
      {
        width: '100',
        field: 'sourcingDirection',
        headerText: i18n.t('报价方案'),
        valueConverter: {
          type: 'map',
          map: {
            forward: i18n.t('递增'),
            reverse: i18n.t('递减'),
            unlimited: i18n.t('无限制')
          }
        }
      },
      {
        width: '130',
        field: 'currentRoundSupplierNum',
        headerText: i18n.t('本轮供应商数量'),
        ignore: true,
        allowFiltering: false
      },
      {
        width: '100',
        field: 'createUserName',
        headerText: i18n.t('创建人')
      },
      {
        width: '150',
        field: 'createTime',
        headerText: i18n.t('创建时间'),
        type: 'date',
        format: 'yyyy-MM-dd HH:mm:ss',
        searchOptions: {
          elementType: 'date-range',
          operator: 'between',
          serializeValue: (e) => {
            //自定义搜索值，规则
            return e.map((x, i) => {
              if (i === 1) {
                return Number(new Date(x.toString().replace('00:00:00', '23:59:59')))
              }
              return Number(new Date(x.toString()))
            })
          }
        }
      },
      {
        width: '100',
        field: 'remark',
        headerText: i18n.t('备注')
      },
      {
        width: '1',
        field: 'itemCode',
        headerText: i18n.t('物料编码')
      },
      {
        width: '1',
        field: 'itemName',
        headerText: i18n.t('物料名称')
      },
      {
        width: '1',
        field: 'supplierCode',
        headerText: i18n.t('供应商编码')
      },
      {
        width: '1',
        field: 'supplierName',
        headerText: i18n.t('供应商名称')
      }
    ]
  } else if (sourcingMode == 'invite_bids') {
    return [
      {
        width: '50',
        type: 'checkbox',
        showInColumnChooser: false,
        allowResizing: false,
        customAttributes: {
          class: 'sticky-col-0'
        }
      },
      {
        width: '180',
        field: 'rfxCode',
        headerText: i18n.t('询价招标单编号'),
        cssClass: 'field-content',
        allowResizing: false,
        customAttributes: {
          class: 'sticky-col-1'
        }
      },
      {
        width: '160',
        field: 'rfxName',
        headerText: i18n.t('项目名称')
      },
      {
        width: '120',
        field: 'sourcingObj',
        headerText: i18n.t('询价对象')
      },
      {
        width: '100',
        field: 'priceClassification',
        headerText: i18n.t('价格分类'),
        valueConverter: {
          type: 'map',
          map: {
            predict_price: i18n.t('暂估价格'),
            srm_price: i18n.t('SRM价格'),
            execute_price: i18n.t('执行价格'),
            basic_price: i18n.t('基价')
          }
        }
      },
      {
        width: '180',
        field: 'strategyConfigName',
        headerText: i18n.t('询价策略')
      },
      {
        width: '100',
        field: 'status',
        headerText: i18n.t('单据状态'),
        searchOptions: {
          operator: 'equal'
        },
        valueConverter: {
          type: 'map',
          map: Object.keys(RFX_STATUS).map(function (i) {
            return {
              text: RFX_STATUS[i],
              value: +i,
              cssClass: 'title-#6386c1'
            }
          })
        }
      },
      {
        width: '130',
        field: 'approveStatus',
        headerText: i18n.t('审批状态'),
        searchOptions: {
          elementType: 'select',
          dataSource: approveStatusList,
          fields: { text: 'label', value: 'status' }
        },
        valueConverter: {
          type: 'map',
          map: Object.keys(approveStatusList).map(function (i) {
            let _class = 'custom-9A9A9A custom--color'
            let _key = Number(i)
            switch (_key) {
              case -2:
                _class = 'custom-ED5633 custom--color'
                break
              case 2:
                _class = 'custom-6386C1 custom--color'
                break
              case 1:
                _class = 'custom-EDA133 custom--color'
                break
              default:
                _class = 'custom-9A9A9A custom--color'
            }
            return {
              text: approveStatusList[_key],
              value: +_key,
              cssClass: _class
            }
          })
        }
      },
      {
        width: '100',
        field: 'rfxGeneralType',
        headerText: i18n.t('招标类型'),
        valueConverter: {
          type: 'map',
          map: {
            1: i18n.t('通采'),
            2: i18n.t('非采')
          }
        }
      },
      {
        width: '100',
        field: 'transferStatus',
        headerText: i18n.t('当前阶段'),
        valueConverter: {
          type: 'map',
          map: Object.keys(transferStatusList).map(function (i) {
            return {
              text: transferStatusList[i],
              value: +i,
              cssClass: 'title-#6386c1'
            }
          })
        },
        cellTools: [
          {
            id: 'submit_project',
            // icon: "icon_solid_Submit",
            title: i18n.t('提交立项'),
            visibleCondition: (data) => {
              return data['transferStatus'] == 0 && data['status'] != -2 && sourcingMode !== 'rfq'
            }
          },
          {
            id: 'publish_zhaobiao',
            // icon: "icon_solid_export",
            title: i18n.t('发布询价招标'),
            visibleCondition: (data) => {
              return data['transferStatus'] == 1
            }
          },
          {
            id: 'end_bao',
            // icon: "icon_solid_Submit",
            title: i18n.t('结束报价'),
            visibleCondition: (data) => {
              return data['transferStatus'] == 5
            }
          },
          {
            id: 'open_biao',
            // icon: "icon_solid_export",
            title: i18n.t('开标'),
            visibleCondition: (data) => {
              return data['transferStatus'] == 6
            }
          },
          // {
          //   id: "ping_biao",
          //   //icon: "icon_solid_export",
          //   title: i18n.t("评标"),
          //   visibleCondition: (data) => {
          //     return data["transferStatus"] == 7;
          //   },
          // },
          {
            id: 'submit_ding',
            // icon: "icon_solid_export",
            title: i18n.t('提交定点'),
            visibleCondition: (data) => {
              return data['transferStatus'] == 10
            }
          }
        ]
      },
      {
        width: '100',
        field: 'docStatus',
        headerText: i18n.t('状态'),
        valueConverter: {
          type: 'map',
          map: Object.keys(bidDocStatusList).map(function (i) {
            return {
              text: bidDocStatusList[i],
              value: +i,
              cssClass: i in colortList ? colortList[i] : 'title-#6386c1'
            }
          })
        }
      },
      {
        width: '100',
        field: 'currentRound',
        headerText: i18n.t('当前轮次'),
        ignore: true,
        allowFiltering: false
      },
      {
        width: '150',
        field: 'companyName',
        headerText: i18n.t('公司')
      },
      {
        width: '100',
        field: 'purOrgName',
        headerText: i18n.t('采购组织')
      },
      {
        width: '100',
        field: 'docSource',
        headerText: i18n.t('单据来源'),
        valueConverter: {
          type: 'map',
          map: {
            0: i18n.t('手动创建'),
            1: i18n.t('引用申请')
          }
        }
      },
      {
        width: '100',
        field: 'responseValue',
        headerText: i18n.t('报价响应'),
        ignore: true,
        allowFiltering: false
      },
      {
        width: '100',
        field: 'biddingMode',
        headerText: i18n.t('招标方式'),
        valueConverter: {
          type: 'map',
          map: {
            open: i18n.t('公开'),
            target: i18n.t('邀请')
            // supplierOpen: i18n.t('合作伙伴公开')
          }
        }
      },
      {
        width: '180',
        field: 'failReason',
        headerText: i18n.t('流标原因')
      },
      {
        width: '100',
        field: 'sourcingDirection',
        headerText: i18n.t('报价方案'),
        valueConverter: {
          type: 'map',
          map: {
            forward: i18n.t('递增'),
            reverse: i18n.t('递减'),
            unlimited: i18n.t('无限制')
          }
        }
      },
      {
        width: '130',
        field: 'currentRoundSupplierNum',
        headerText: i18n.t('本轮供应商数量'),
        ignore: true,
        allowFiltering: false
      },
      {
        width: '100',
        field: 'createUserName',
        headerText: i18n.t('创建人')
      },
      {
        width: '150',
        field: 'createTime',
        headerText: i18n.t('创建时间'),
        type: 'date',
        format: 'yyyy-MM-dd HH:mm:ss',
        searchOptions: {
          elementType: 'date-range',
          operator: 'between',
          serializeValue: (e) => {
            //自定义搜索值，规则
            return e.map((x, i) => {
              if (i === 1) {
                return Number(new Date(x.toString().replace('00:00:00', '23:59:59')))
              }
              return Number(new Date(x.toString()))
            })
          }
        }
      },
      {
        width: '100',
        field: 'remark',
        headerText: i18n.t('备注')
      },
      {
        width: '1',
        field: 'itemCode',
        headerText: i18n.t('物料编码')
      },
      {
        width: '1',
        field: 'itemName',
        headerText: i18n.t('物料名称')
      },
      {
        width: '1',
        field: 'supplierCode',
        headerText: i18n.t('供应商编码')
      },
      {
        width: '1',
        field: 'supplierName',
        headerText: i18n.t('供应商名称')
      }
    ]
  } else {
    return [
      {
        width: '50',
        type: 'checkbox',
        showInColumnChooser: false,
        allowResizing: false,
        customAttributes: {
          class: 'sticky-col-0'
        }
      },
      {
        width: '180',
        field: 'rfxCode',
        headerText: i18n.t('询价招标单编号'),
        cssClass: 'field-content',
        allowResizing: false,
        customAttributes: {
          class: 'sticky-col-1'
        }
      },
      {
        width: '160',
        field: 'rfxName',
        headerText: i18n.t('询价招标单名称')
      },
      {
        width: '120',
        field: 'sourcingObj',
        headerText: i18n.t('询价对象')
      },
      {
        width: '100',
        field: 'priceClassification',
        headerText: i18n.t('价格分类'),
        valueConverter: {
          type: 'map',
          map: {
            predict_price: i18n.t('暂估价格'),
            srm_price: i18n.t('SRM价格'),
            execute_price: i18n.t('执行价格'),
            basic_price: i18n.t('基价')
          }
        }
      },
      {
        width: '180',
        field: 'strategyConfigName',
        headerText: i18n.t('询价策略')
      },
      {
        width: '100',
        field: 'status',
        headerText: i18n.t('单据状态'),
        searchOptions: {
          operator: 'equal'
        },
        valueConverter: {
          type: 'map',
          map: Object.keys(RFX_STATUS).map(function (i) {
            return {
              text: RFX_STATUS[i],
              value: +i,
              cssClass: 'title-#6386c1'
            }
          })
        }
      },
      {
        width: '180',
        field: 'failReason',
        headerText: i18n.t('流标原因')
      },
      {
        width: '130',
        field: 'approveStatus',
        headerText: i18n.t('审批状态'),
        searchOptions: {
          elementType: 'select',
          dataSource: approveStatusList,
          fields: { text: 'label', value: 'status' }
        },
        valueConverter: {
          type: 'map',
          map: Object.keys(approveStatusList).map(function (i) {
            let _class = 'custom-9A9A9A custom--color'
            let _key = Number(i)
            switch (_key) {
              case -2:
                _class = 'custom-ED5633 custom--color'
                break
              case 2:
                _class = 'custom-6386C1 custom--color'
                break
              case 1:
                _class = 'custom-EDA133 custom--color'
                break
              default:
                _class = 'custom-9A9A9A custom--color'
            }
            return {
              text: approveStatusList[_key],
              value: +_key,
              cssClass: _class
            }
          })
        }
      },
      {
        width: '100',
        field: 'rfxGeneralType',
        headerText: i18n.t('招标类型'),
        valueConverter: {
          type: 'map',
          map: {
            1: i18n.t('通采'),
            2: i18n.t('非采')
          }
        }
      },
      {
        width: '100',
        field: 'transferStatus',
        headerText: i18n.t('当前阶段'),
        valueConverter: {
          type: 'map',
          map: Object.keys(transferStatusList).map(function (i) {
            return {
              text: transferStatusList[i],
              value: +i,
              cssClass: 'title-#6386c1'
            }
          })
        },
        cellTools: [
          {
            id: 'submit_project',
            // icon: "icon_solid_Submit",
            title: i18n.t('提交立项'),
            visibleCondition: (data) => {
              return data['transferStatus'] == 0 && data['status'] != -2 && sourcingMode !== 'rfq'
            }
          },
          {
            id: 'publish_zhaobiao',
            // icon: "icon_solid_export",
            title: i18n.t('发布询价招标'),
            visibleCondition: (data) => {
              return data['transferStatus'] == 1
            }
          },
          {
            id: 'end_bao',
            // icon: "icon_solid_Submit",
            title: i18n.t('结束报价'),
            visibleCondition: (data) => {
              return data['transferStatus'] == 5
            }
          },
          {
            id: 'open_biao',
            // icon: "icon_solid_export",
            title: i18n.t('开标'),
            visibleCondition: (data) => {
              return data['transferStatus'] == 6
            }
          },
          // {
          //   id: "ping_biao",
          //   //icon: "icon_solid_export",
          //   title: i18n.t("评标"),
          //   visibleCondition: (data) => {
          //     return data["transferStatus"] == 7;
          //   },
          // },
          {
            id: 'submit_ding',
            // icon: "icon_solid_export",
            title: i18n.t('提交定点'),
            visibleCondition: (data) => {
              return data['transferStatus'] == 10
            }
          }
        ]
      },
      {
        width: '100',
        field: 'docStatus',
        headerText: i18n.t('状态'),
        valueConverter: {
          type: 'map',
          map: Object.keys(bidDocStatusList).map(function (i) {
            return {
              text: bidDocStatusList[i],
              value: +i,
              cssClass: i in colortList ? colortList[i] : 'title-#6386c1'
            }
          })
        }
      },
      {
        width: '100',
        field: 'currentRound',
        headerText: i18n.t('当前轮次'),
        ignore: true,
        allowFiltering: false
      },
      {
        width: '150',
        field: 'companyName',
        headerText: i18n.t('公司')
      },
      {
        width: '100',
        field: 'purOrgName',
        headerText: i18n.t('采购组织')
      },
      {
        width: '100',
        field: 'docSource',
        headerText: i18n.t('单据来源'),
        valueConverter: {
          type: 'map',
          map: {
            0: i18n.t('手动创建'),
            1: i18n.t('引用申请')
          }
        }
      },
      {
        width: '100',
        field: 'responseValue',
        headerText: i18n.t('报价响应'),
        ignore: true,
        allowFiltering: false
      },
      {
        width: '100',
        field: 'biddingMode',
        headerText: i18n.t('招标方式'),
        valueConverter: {
          type: 'map',
          map: {
            open: i18n.t('公开'),
            target: i18n.t('邀请')
            // supplierOpen: i18n.t('合作伙伴公开')
          }
        }
      },
      {
        width: '100',
        field: 'sourcingDirection',
        headerText: i18n.t('报价方案'),
        valueConverter: {
          type: 'map',
          map: {
            forward: i18n.t('递增'),
            reverse: i18n.t('递减'),
            unlimited: i18n.t('无限制')
          }
        }
      },
      {
        width: '130',
        field: 'currentRoundSupplierNum',
        headerText: i18n.t('本轮供应商数量'),
        ignore: true,
        allowFiltering: false
      },
      {
        width: '100',
        field: 'createUserName',
        headerText: i18n.t('创建人')
      },
      {
        width: '150',
        field: 'createTime',
        headerText: i18n.t('创建时间'),
        type: 'date',
        format: 'yyyy-MM-dd HH:mm:ss',
        searchOptions: {
          elementType: 'date-range',
          operator: 'between',
          serializeValue: (e) => {
            //自定义搜索值，规则
            return e.map((x, i) => {
              if (i === 1) {
                return Number(new Date(x.toString().replace('00:00:00', '23:59:59')))
              }
              return Number(new Date(x.toString()))
            })
          }
        }
      },
      {
        width: '100',
        field: 'remark',
        headerText: i18n.t('备注')
      }
    ]
  }
}

export const pageConfig = (url, sourcingMode = 'rfq') => {
  if (sourcingMode === 'rfq') {
    return [
      {
        title: i18n.t('询价'),
        useToolTemplate: false,
        toolbar,
        gridId: permission.gridId['purchase'][sourcingMode]['list'],
        grid: {
          allowFiltering: true,
          allowSorting: false,
          columnData: columnDataJudge(sourcingMode), //询价大厅隐藏提交立项
          frozenColumns: 2,
          autoWidthColumns: 15,
          asyncConfig: {
            url,
            defaultRules: [
              {
                label: '',
                field: 'sourcingMode',
                type: 'string',
                operator: 'equal',
                value: sourcingMode
              }
            ]
          }
        }
      },
      {
        title: i18n.t('直接定价'),
        useToolTemplate: false,
        toolbar: zToolBar,
        gridId: permission.gridId['purchase']['recommendFixedPoint'],
        grid: {
          allowFiltering: true,
          asyncConfig: {
            url: '/sourcing/tenant/point2/pagePoint?BU_CODE=TV',
            defaultRules: [
              {
                field: 'decidePriceType',
                operator: 'in',
                value: ['0']
              }
            ]
          },
          lineIndex: true,
          columnData: zColumnData,
          dataSource: []
        }
      },
      {
        title: i18n.t('白电直接定价'),
        useToolTemplate: false,
        toolbar: bdToolBar,
        gridId: permission.gridId['purchase']['whiteElectricityDirectPrice'],
        grid: {
          allowFiltering: true,
          asyncConfig: {
            url: '/sourcing/tenant/whitePoint/buyer/queryHeaderByPage'
          },
          lineIndex: true,
          columnData: bdColumnData,
          dataSource: []
        }
      },
      {
        title: i18n.t('系列物料定价'),
        useToolTemplate: false,
        toolbar: xToolBar,
        gridId: permission.gridId['purchase']['seriesItem']['seriesItemPricing']['list'],
        grid: {
          allowFiltering: true,
          asyncConfig: {
            url: '/sourcing/tenant/series/item/apply/list'
          },
          lineIndex: true,
          columnData: xColumnData,
          dataSource: []
        }
      },
      {
        title: i18n.t('联动定价')
      },
      {
        title: i18n.t('价格延期')
      }
    ]
  }
  return [
    {
      // freezeOperationColumn: true,
      useToolTemplate: false,
      toolbar,
      gridId: permission.gridId['purchase'][sourcingMode]['list'],
      grid: {
        allowFiltering: true,
        allowSorting: false,
        columnData: columnDataJudge(sourcingMode), //询价大厅隐藏提交立项
        frozenColumns: 2,
        autoWidthColumns: 15,
        asyncConfig: {
          url,
          defaultRules: [
            {
              label: '',
              field: 'sourcingMode',
              type: 'string',
              operator: 'equal',
              value: sourcingMode
            }
          ]
        }
      }
    }
  ]
}
