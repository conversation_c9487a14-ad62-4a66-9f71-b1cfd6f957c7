<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      :current-tab="currentTab"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @handleSelectTab="handleSelectTab"
    >
      <!-- 联动定价 -->
      <cost-factor-relate-pricing slot="slot-4" />
      <!-- 价格延期 -->
      <postpone-pricing slot="slot-5" />
    </mt-template-page>
  </div>
</template>

<script>
import MixIn from 'ROUTER_PURCHASE_RFX/list/config/mixin'
import { pageConfig } from 'ROUTER_PURCHASE_RFX/list/config/index'
import CostFactorRelatePricing from '@/views/purchase/costFactorLinkagePricing/index.vue'
import PostponePricing from '@/views/purchase/postponePricing/index.vue'

export default {
  components: { CostFactorRelatePricing, PostponePricing },
  mixins: [MixIn],
  data() {
    return {
      pageConfig: pageConfig(this.$API.rfxList.getRFXList, 'rfq'),
      biddingSource: 'rfq', //询报价
      currentTab: 0
    }
  },
  methods: {
    /**
     *  直接定价----------------------
     */
    //新增
    handleZAddFn() {
      this.$dialog({
        modal: () => import('@/views/purchase/recommendFixedPointOld/newAddComponents/index.vue'),
        data: {
          title: this.$t('创建定价'),
          type: 'recommendations'
        },
        success: (data) => {
          if (data) {
            this.$router.push({
              name: 'direct-pricing-detail-tv',
              query: {
                id: data.id,
                decidePriceType: data.decidePriceType,
                status: 0,
                timeStamp: new Date().getTime()
              }
            })
          } else {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        }
      })
    },

    //删除
    handleZDelFn(selectGridRecords, idList) {
      if (selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            let params = {
              idList: idList
            }
            this.$API.rfxList.deletePointListTV(params).then(() => {
              this.$toast({ content: this.$t('删除成功'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
            })
          }
        })
      }
    },

    //提交
    handleZSubmitFn(selectGridRecords) {
      if (selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else if (selectGridRecords.length > 1) {
        this.$toast({ content: this.$t('只选择一行提交'), type: 'warning' })
      } else {
        if ([-1, 0, 3].includes(selectGridRecords[0].status)) {
          if (this.isSubmit) {
            this.$toast({
              content: this.$t('正在提交中，请勿重复点击'),
              type: 'warning'
            })
            return
          }
          this.isSubmit = true
          let params = {
            id: selectGridRecords[0].id
          }
          this.$API.rfxList
            .submitPointTv(params)
            .then(() => {
              this.$toast({ content: this.$t('提交成功'), type: 'success' })
              this.isSubmit = false
              this.$refs.templateRef.refreshCurrentGridData()
            })
            .catch(() => {
              this.isSubmit = false
            })
        } else {
          this.$toast({
            content: this.$t('审批通过，不可重复提交'),
            type: 'warning'
          })
        }
      }
    },
    /**
     *  白电直接定价----------------------
     */
    //新增
    handleBdAddFn() {
      this.$dialog({
        modal: () =>
          import('@/views/purchase/whiteElectricityDirectPrice/newAddComponents/index.vue'),
        data: {
          title: this.$t('创建定价')
        },
        success: (data) => {
          this.$router.push({
            name: 'direct-price',
            query: {
              id: data.id,
              decidePriceType: 0,
              status: data.status,
              pointNo: data.pointNo
            }
          })
        }
      })
    },

    //删除
    handleBdDelFn(selectGridRecords, idList) {
      if (selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            let params = {
              idList: idList
            }
            this.$API.whitePoint.deleteHeader(params).then(() => {
              this.$toast({ content: this.$t('删除成功'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
            })
          }
        })
      }
    },

    //提交
    handleBdSubmitFn(selectGridRecords) {
      if (selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else if (selectGridRecords.length > 1) {
        this.$toast({ content: this.$t('只选择一行提交'), type: 'warning' })
      } else {
        let params = {
          id: selectGridRecords[0].id
        }
        this.$API.whitePoint.sendToSupplier(params).then(() => {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        })
      }
    },
    // 关闭
    handleBdClose(idlist = [], records) {
      if (idlist.length <= 0) {
        this.$toast({
          type: 'warning',
          content: this.$t('请先选择一行数据')
        })
        return
      }
      for (let key = 0; key < records.length; key++) {
        if (!['5', 5, '7', 7].includes(records[key].status)) {
          this.$toast({
            type: 'warning',
            content: this.$t('只能关闭状态为 "供应商已驳回" 或者 "审批拒绝" 的数据')
          })
          return
        }
      }
      this.$store.commit('startLoading')
      this.$API.whitePoint
        .directPricing({ idList: idlist })
        .then((res) => {
          if (res && res.code === 200) {
            this.$toast({
              type: 'success',
              content: this.$t('操作成功')
            })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    /**
     *  系列物料----------------------
     */
    //新增
    handleXAddFn() {
      this.$dialog({
        modal: () =>
          import(
            '@/views/purchase/seriesItem/seriesItemPricing/list/components/addDialog/index.vue'
          ),
        data: {
          title: this.$t('创建定价')
        },
        success: (data) => {
          this.$router.push({
            name: 'series-item-pricing-detail',
            query: {
              id: data.id,
              decidePriceType: data.decidePriceType,
              status: data.status
            }
          })
        }
      })
    },

    //删除
    handleXDelFn(selectGridRecords, idList) {
      if (selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else {
        let params = {
          ids: idList
        }
        this.$API.seriesItem.itemApplyDelete(params).then(() => {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        })
      }
    },

    //提交
    handleXSubmitFn(selectGridRecords) {
      if (selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else if (selectGridRecords.length > 1) {
        this.$toast({ content: this.$t('只选择一行提交'), type: 'warning' })
      } else {
        if ([0, 1, 4].includes(selectGridRecords[0].status)) {
          let params = {
            id: selectGridRecords[0].id
          }
          this.$API.seriesItem.itemApplySubmit(params).then(() => {
            this.$toast({ content: this.$t('提交成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        } else {
          this.$toast({
            content: this.$t('只草稿/已保存状态的定价单可提交'),
            type: 'warning'
          })
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/styles/mixin.scss';
.full-height {
  /deep/.e-headercelldiv {
    padding: 0 0.6em 0 0.6em !important;
  }
  /deep/.e-rowcell.sticky-col-0,
  /deep/.e-headercell.sticky-col-0 {
    @include sticky-col;
    left: 0px;
  }
  /deep/.e-rowcell.sticky-col-1,
  /deep/.e-headercell.sticky-col-1 {
    @include sticky-col;
    left: 50px;
  }
}
</style>
