import { i18n } from '@/main.js'
export const defaultHeaderFields = [
  {
    recordId: null,
    fieldId: '179393898183004200',
    fieldKey: '56ebab79-da5c-11eb-96d2-0242ac150162',
    fieldCode: 'rfxName',
    fieldGroup: i18n.t('询价信息'),
    fieldGroupCode: '',
    fieldName: i18n.t('标题'),
    fieldType: 0,
    fixed: 0,
    required: 0,
    fieldData: null,
    tableField: 1,
    customer: 0,
    tableName: 'rfx_header',
    sortValue: 100,
    pageShow: 1,
    structureKey: '',
    defaultFieldName: i18n.t('标题')
  },
  {
    recordId: null,
    fieldId: '170720062705745936',
    fieldKey: 'e9442289-8be1-11ec-b83b-fa163e4f7edd',
    fieldCode: 'sourcingMode',
    fieldGroup: i18n.t('询价信息'),
    fieldGroupCode: '',
    fieldName: i18n.t('寻源方式'),
    fieldType: 0,
    fixed: 1,
    required: 0,
    fieldData: null,
    tableField: 1,
    customer: 0,
    tableName: 'rfx_header',
    sortValue: 97,
    pageShow: 1,
    structureKey: '',
    defaultFieldName: i18n.t('寻源方式')
  },
  {
    recordId: null,
    fieldId: '170720062772854858',
    fieldKey: 'd2d4e2de-8ba2-11ec-b83b-fa163e4f7edd',
    fieldCode: 'businessTypeName',
    fieldGroup: i18n.t('询价信息'),
    fieldGroupCode: '',
    fieldName: i18n.t('业务类型'),
    fieldType: 0,
    fixed: 1,
    required: 0,
    fieldData: null,
    tableField: 1,
    customer: 0,
    tableName: 'rfx_header',
    sortValue: 98,
    pageShow: 1,
    structureKey: '',
    defaultFieldName: i18n.t('业务类型')
  },
  {
    recordId: null,
    fieldId: '170720062735106060',
    fieldKey: 'd9b6e285-8ba2-11ec-b83b-fa163e4f7edd',
    fieldCode: 'sourcingObj',
    fieldGroup: i18n.t('询价信息'),
    fieldGroupCode: '',
    fieldName: i18n.t('寻源对象'),
    fieldType: 0,
    fixed: 1,
    required: 0,
    fieldData: null,
    tableField: 1,
    customer: 0,
    tableName: 'rfx_header',
    sortValue: 96,
    pageShow: 1,
    structureKey: '',
    defaultFieldName: i18n.t('寻源对象')
  }
]
