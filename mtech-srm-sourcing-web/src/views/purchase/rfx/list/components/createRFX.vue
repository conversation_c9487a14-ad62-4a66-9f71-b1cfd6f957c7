<template>
  <div class="dialog-content">
    <input
      type="text"
      id="createHiddenInput"
      style="position: absolute; opacity: 0; width: 0; height: 0"
    />
    <div class="header">
      <div class="left">
        <span class="split">|</span><span class="page-name">{{ $t('创建标案') }}</span>
      </div>
      <div class="space"></div>
      <div class="right">
        <mt-button css-class="e-flat" :is-primary="true" @click="back">{{ $t('返回') }}</mt-button>
        <mt-button
          css-class="e-flat"
          :is-primary="true"
          @click="createRFX()"
          :disabled="createRFXLoading"
          >{{ $t('保存') }}</mt-button
        >
      </div>
    </div>
    <FormGenerator
      :field-defines="fixedFieldDefines"
      :form-field-config="fixedFormFieldConfig"
      ref="fixedForm"
      :form-rules="formRules"
    />
    <FormGenerator
      :field-defines="headerFieldDefines"
      :form-field-config="headerFormFieldConfig"
      ref="headerForm"
      :key="updateIndex"
      :form-rules="formRules"
      :user-select="true"
      :rfx-code="formObject.rfxCode"
    />
    <FormGenerator
      :field-defines="strategyFieldDefines"
      :form-field-config="strategyFormFieldConfig"
      ref="strategyForm"
      :form-rules="formRules"
    />
    <div v-if="roundObj.length > 0">
      <div class="round-container" v-for="(row, index) in roundObj" :key="index">
        <span class="tag-current-round" v-if="showTagCurrentRound">
          {{ $t(`第${row.roundNo || 1}轮`) }}
        </span>
        <FormGenerator
          :field-defines="row.definesList"
          :form-field-config="roundFormFieldConfig"
          :ref="'roundForm' + index"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { defaultHeaderFields } from './config'
import FormGenerator from '../../components/FormGenerator.vue'
import { isDate } from '@/utils/is'
import cloneDeep from 'lodash/cloneDeep'
import { useFiltering } from '@/utils/ej/select'

export default {
  name: 'CreateRequirement',
  components: { FormGenerator },
  data() {
    return {
      wheelMeterList: [],
      createRFXLoading: false,
      formObject: {
        rfxCode: null
      },
      formRules: {},
      strategyInfo: {},
      // 头部固定字段
      headerFixedParams: {
        rfxName: '', //标题
        sourcingObj: '', //寻源对象
        sourcingMode: '', //寻源方式
        businessTypeCode: ''
      },
      // 头部字段
      headerHeaderParams: {
        currencyCode: 'CNY', //货币编码
        pcbCode: null,
        companyCode: '', //公司编码
        departmentCode: this.$t('采购部'), //部门编码
        siteCode: '', //工厂
        strategyConfigId: '' //策略
      },
      // 获取策略字段
      strategyConfigParams: {
        businessTypeCode: '',
        sourcingMode: '', //寻源方式
        sourcingType: 'new_products', //询价类型
        sourcingObjType: 'common', //询价对象
        biddingMode: 'target', //招标方式
        sourcingDirection: 'forward', //寻源方向
        minSupplierBiddingQuantity: 1, //最少供应商报价数量
        nonDirectionalSupNum: 0, //非定向议价供应商数量
        // supplierRange: "category_qualified", //拓展（定制）   字段暂不显示   不是拓展
        supplierSelectionRange: 0, //供应商选择范围
        supplierQuotationRule: 0, //供应商报价规则
        allocationRange: 0, //配额分配范围
        directionalBargaining: 1, //是否定向议价
        sealedPrice: 1, //是否密封报价
        publicOptimalPrice: 1, //公开最优价
        publicRanking: 1, //公开排名
        publicQuotation: 1, //公开报价
        publicIdentity: 1, //公开身份
        publicParticipantNum: 1, //公开参与者数量
        roundEndTime: '',
        roundStartTime: '',
        allocationRequired: 1, //配额是否必输
        bidNumRequired: 1, //中标数量是否必输
        biddingPromotion: 1, //招标单是否晋级
        roundCount: '1', //轮次总计
        // stepQuote: 1,
        accountingMode: 'multi_item', //核算方式   改名'定价规则'
        bidEvaluationMode: 'mini_price', //招标评估方式
        tecBidStartTime: null,
        tecOpenBidTime: null,
        tecScoreEndTime: null,
        needTecBid: null,
        pointMode: null, // 定点模式 1：内部定点，2：推荐定点
        priceControl: 'all', // 报价控制
        priceDirectionControl: 'all', // 报价方向控制
        totalRanking: null
      },
      updateIndex: 0,
      fixedFieldDefines: defaultHeaderFields,
      headerFieldDefines: [],
      strategyFieldDefines: [],
      fixedFormFieldConfig: {},
      headerFormFieldConfig: {},
      strategyFormFieldConfig: {},
      cacheHeaderFixedParams: {}, //存一下sourcingObj，sourcingMode，businessTypeCode
      showTagCurrentRound: false,
      roundObj: [],
      roundFormFieldConfig: {},
      // 头部固定字段
      roundParams: {
        publicParticipantNum: '',
        publicIdentity: '',
        publicQuotation: '',
        publicRanking: '',
        publicOptimalPrice: '',
        roundEndTime: '',
        roundStartTime: ''
      },
      tvStrategyId: null,
      tvStrategyDetails: null
    }
  },
  computed: {
    pageType() {
      return this.$route.query?.pageType
    }
  },
  created() {
    const that = this
    this.$API.rfxList.getRfxCode({ sourcingMode: this.$route.query.source }).then((res) => {
      if (res.code === 200) {
        this.formObject.rfxCode = res.data
      }
    })
    this.headerFixedParams.sourcingMode = this.$route.query.source
    this.strategyConfigParams.sourcingMode = this.$route.query.source

    this.fixedFormFieldConfig = {
      //标题
      rfxName: {
        type: 'text',
        valid: [
          {
            max: 50,
            message: this.$t(`标题长度不能超过50`),
            trigger: 'blur'
          }
        ],
        handler: (ej2Value) => {
          if (ej2Value && this.headerFixedParams.rfxName !== ej2Value) {
            this.headerFixedParams.rfxName = ej2Value
            Object.assign(this.formObject, { rfxName: ej2Value })
          }
        },
        defaultValue: () => this.headerFixedParams.rfxName
      },
      //询价单号  rfxCode  不显示
      //寻源方式
      sourcingMode: {
        type: 'select',
        readonly: true,
        hideFormItem: true,
        handler: (ej2EventObject) => {
          if (
            ej2EventObject.value &&
            this.headerFixedParams.sourcingMode !== ej2EventObject.value
          ) {
            this.headerFixedParams.sourcingMode = ej2EventObject.value
            this.strategyConfigParams.sourcingMode = ej2EventObject.value
          }
        },
        defaultValue: () => this.headerFixedParams.sourcingMode,
        dataSource: [
          { text: this.$t('询报价'), value: 'rfq' },
          // { text: this.$t("直接定价"), value: "direct_pricing" },
          { text: this.$t('招投标'), value: 'invite_bids' },
          { text: this.$t('竞价'), value: 'bidding_price' }
        ]
      },
      //业务类型
      businessTypeName: {
        type: 'select',
        handler: (ej2EventObject) => {
          if (
            ej2EventObject.itemData?.data &&
            ej2EventObject.itemData?.data.businessTypeCode !==
              this.headerFixedParams.businessTypeCode
          ) {
            this.headerFixedParams.sourcingObj = '' //切换业务类型，清除'询价对象'
            this.resetFieldForm()
            this.headerFixedParams.businessTypeCode = ej2EventObject.itemData.data.businessTypeCode
            this.strategyConfigParams.businessTypeCode =
              ej2EventObject.itemData.data.businessTypeCode
            Object.assign(this.formObject, ej2EventObject.itemData.data)
          }
        },
        defaultValue: () => this.headerFixedParams.businessTypeCode,
        api: this.$API.masterData
          .dictionaryGetBusinessType()
          // .dictionaryGetList({
          //   dictCode: "businessType",
          // })
          .then((res) => {
            return res.data.map((item) => ({
              text: item.itemName,
              value: item.itemCode,
              data: {
                businessTypeName: item.itemName,
                businessTypeCode: item.itemCode,
                businessTypeId: item.id,
                isDefault: item.defaultValue
              }
            }))
          })
      },
      //寻源对象
      sourcingObj: {
        type: 'select',
        handler: (ej2EventObject) => {
          if (ej2EventObject.value && this.headerFixedParams.sourcingObj !== ej2EventObject.value) {
            this.resetFieldForm()
            this.headerFixedParams.sourcingObj = ej2EventObject.value
            this.strategyConfigParams.sourcingObjType =
              ej2EventObject.itemData.data?.sourcingObjType
            Object.assign(this.formObject, ej2EventObject.itemData.data)

            if (ej2EventObject.itemData.data?.templateCode === 'TV_CommonRFQ') {
              // 通用物料设置公司为1503公司、价格分类为执行价
              this.headerFormFieldConfig.companyName?.api.then((res) => {
                let _find = res?.find((item) => item.data.companyCode === '1503')
                this.headerHeaderParams.companyCode = _find.value
              })
              this.strategyConfigParams.priceClassification = 'execute_price'
            } else {
              if (this.pageType === 'gf') {
                this.headerFormFieldConfig.companyName?.api.then((res) => {
                  let _find = res?.find(
                    (item) => item.data.companyCode === this.$route.query?.companyCode
                  )
                  this.headerHeaderParams.companyCode = _find.value
                })
                this.strategyConfigParams.priceClassification = 'execute_price'
              } else {
                this.headerHeaderParams.companyCode = ''
                this.strategyConfigParams.priceClassification = ''
              }
            }
          }
        },
        dataSource: [],
        defaultValue: () => this.headerFixedParams.sourcingObj
      }
    }

    this.headerFormFieldConfig = {
      //公司名称  companyCode companyId companyName
      companyName: {
        type: 'select',
        handler: (ej2EventObject) => {
          if (ej2EventObject.itemData != null) {
            Object.assign(this.formObject, ej2EventObject.itemData.data)
            if (this.pageType === 'gf') {
              this.headerHeaderParams.purOrgCode = this.$route.query?.purOrgCode
            }
          }
        },
        defaultValue: () => this.headerHeaderParams.companyCode,
        api: this.$API.masterData.permissionCompanyList().then((res) => {
          return res?.data.map((item) => ({
            text: item.orgCode + '-' + item.orgName,
            value: item.orgName,
            data: {
              companyCode: item.orgCode,
              companyName: item.orgName,
              companyId: item.id
            }
          }))
        })
      },
      //工厂 siteName siteId  siteCode
      siteName: {
        type: 'select',
        dataSource: [],
        defaultValue: () => this.headerHeaderParams.siteCode,
        handler: (ej2EventObject) => {
          if (ej2EventObject.itemData == null) {
            Object.assign(this.formObject, {
              siteCode: null,
              siteId: null,
              siteName: null
            })
          } else {
            Object.assign(this.formObject, ej2EventObject.itemData.data)
          }
        }
      },
      //采购组织  purOrgName  purOrgCode  purOrgId
      purOrgName: {
        type: 'select',
        dataSource: [],
        handler: (ej2EventObject) => {
          if (ej2EventObject.itemData == null) {
            Object.assign(this.formObject, {
              purOrgCode: null,
              purOrgId: null,
              purOrgName: null
            })
          } else {
            Object.assign(this.formObject, ej2EventObject.itemData.data)
            if (this.pageType === 'gf') {
              this.headerHeaderParams.siteCode = this.$route.query?.siteCode
            }
          }
        },
        defaultValue: () => this.headerHeaderParams.purOrgCode
      },
      //采购员  purExecutorName  purExecutorId
      purExecutorName: {
        type: 'debounce-select',
        defaultValue: () => this.$store.state.userInfo.uid,
        handler: (ej2EventObject) => {
          if (ej2EventObject?.itemData) {
            Object.assign(this.formObject, {
              purExecutorName: ej2EventObject.itemData?.employeeName,
              purExecutorId: ej2EventObject.itemData?.uid
            })
          }
        }
      },
      //备注
      remark: {
        col: 1,
        type: 'text',
        maxlength: 500,
        multiline: true
      },
      //需求部门  deptName  deptCode deptId
      deptName: {
        type: 'select',
        allowFiltering: true,
        filtering: useFiltering(function (e) {
          that.$API.masterData
            .getDepartmentList({
              departmentName: e.text
            })
            .then((res) => {
              e.updateData(
                res.data.map((item) => ({
                  text: item.departmentName,
                  value: item.departmentName,
                  data: {
                    deptCode: item.departmentCode,
                    deptName: item.departmentName,
                    deptId: item.organizationId
                  }
                }))
              )
            })
        }),
        handler: (ej2EventObject) => {
          Object.assign(this.formObject, ej2EventObject.itemData.data)
        },
        defaultValue: () => this.headerHeaderParams.departmentCode,
        api: this.$API.masterData
          .getDepartmentList({
            departmentName: ''
          })
          .then((res) => {
            return res.data.map((item) => ({
              text: item.departmentName,
              value: item.departmentName,
              data: {
                deptCode: item.departmentCode,
                deptName: item.departmentName,
                deptId: item.organizationId
              }
            }))
          })
      },
      //询价类型
      sourcingType: {
        type: 'select',
        dataSource: [
          { text: this.$t('新品'), value: 'new_products' },
          { text: this.$t('二次'), value: 'second_inquiry' },
          { text: this.$t('已有'), value: 'exist' },
          { text: this.$t('不限'), value: 'unlimited' }
        ],
        defaultValue: () => this.strategyConfigParams.sourcingType
      },
      //价格分类
      priceClassification: {
        type: 'select',
        dataSource: [
          { text: this.$t('暂估价格'), value: 'predict_price' },
          { text: this.$t('SRM价格'), value: 'srm_price' },
          { text: this.$t('执行价格'), value: 'execute_price' },
          { text: this.$t('基价'), value: 'basic_price' }
        ],
        defaultValue: () => {
          var DefaultPriceClassification = this.strategyConfigParams.priceClassification
          let _arr = ['structure_component', 'single_module', 'serial_item', 'cost_factor']
          if (_arr.includes(this.strategyConfigParams.sourcingObjType)) {
            DefaultPriceClassification = 'srm_price'
          }
          return DefaultPriceClassification
        }
      },
      //基础/派生
      basicDerivation: {
        type: 'select',
        dataSource: [
          { text: this.$t('基础'), value: 0 },
          { text: this.$t('派生'), value: 1 }
        ]
      },
      //策略地图配置名称 strategyConfigId  strategyConfigName
      strategyConfigName: {
        type: 'select',
        handler: (ej2EventObject) => {
          let _flag = false
          this.wheelMeterList = []
          let list = []
          if (ej2EventObject.itemData == null) {
            return
          }
          if (
            this.strategyConfigParams.sourcingMode === 'invite_bids' ||
            ej2EventObject.itemData.details
          ) {
            let flag = false
            for (let i = 0; i < ej2EventObject.itemData.details.length; i++) {
              if (ej2EventObject.itemData.details[i].strategyCode === 'roundCount') {
                flag = true
                _flag = true
                break
              }
            }
            ej2EventObject.itemData.details.forEach((e) => {
              if (e.strategyCode == 'allocationRatio') {
                sessionStorage.setItem('allocationRatio', e.defaultValue)
              }
              if (flag) {
                //代码优化 轮次列表添加 轮次开始时间、轮次结束时间、公开最优价、公开排名、公开参与者数量、公开身份、公开报价
                let _arr = [
                  'roundStartTime',
                  'roundEndTime',
                  'publicOptimalPrice',
                  'publicRanking',
                  'publicParticipantNum',
                  'publicIdentity',
                  'publicQuotation',
                  'eliminateRule'
                ]
                if (_arr.includes(e.strategyCode)) {
                  list.push({
                    configId: e.configId,
                    defaultValue: e.defaultValue,
                    editEnable: e.editEnable,
                    enableStatus: e.enableStatus,
                    groupCode: e.groupCode,
                    sortValue: e.sortValue,
                    strategyCode: e.strategyCode,
                    strategyName: e.strategyName,
                    supplierVisible: e.supplierVisible,
                    defaultFieldName: e.strategyName,
                    fieldName: e.strategyName,
                    fieldCode: e.strategyCode
                  })
                }
              }
            })
            this.wheelMeterList = list
          }
          this.strategyInfo = {
            strategyConfigName: ej2EventObject.itemData.strategyName,
            strategyConfigId: ej2EventObject.itemData.id
          }
          // 更新默认值
          ej2EventObject.itemData.details.forEach((detail) => {
            const { defaultValue, strategyCode } = detail
            this.strategyConfigParams[strategyCode] = defaultValue
            if (strategyCode === 'roundCount') {
              let _vm = this.$refs.strategyForm
              this.handlerRound({ value: defaultValue }, _vm)
            }
          })
          // 处理关联
          let strategyFieldDefines = ej2EventObject.itemData.data
          // let tenderExtendTimeRecord = strategyFieldDefines.find(
          //   (e) => e.fieldCode === 'tenderExtendTime'
          // )
          // if (tenderExtendTimeRecord) {
          //   tenderExtendTimeRecord.fieldName = this.$t('投标时间扩展(单位：分钟)')
          // }
          // let triggerExtendRemainingTimeRecord = strategyFieldDefines.find(
          //   (e) => e.fieldCode === 'triggerExtendRemainingTime'
          // )
          // if (triggerExtendRemainingTimeRecord) {
          //   triggerExtendRemainingTimeRecord.fieldName = this.$t('剩余时间触发扩展（单位：分钟）')
          // }
          const findField = (fieldCode) =>
            strategyFieldDefines.find((e) => e.fieldCode === fieldCode)
          // 定向数量
          const nonDirectionalSupNum = findField('nonDirectionalSupNum')
          const directionalBargaining = findField('directionalBargaining')
          const allocationRange = findField('allocationRange')
          if (nonDirectionalSupNum && directionalBargaining && !nonDirectionalSupNum.readonly) {
            const readonly = !!(Number(this.strategyConfigParams.directionalBargaining) === 1)
            nonDirectionalSupNum.readonly = readonly
          }

          // 根据询价对象控制配额是否必输
          if (
            allocationRange &&
            ['structure_component', 'serial_item'].includes(
              this.strategyConfigParams.sourcingObjType
            )
          ) {
            allocationRange.readonly = true
          }
          const tecFields = [
            'tecOpenBidTime',
            'tecScoreEndTime',
            'tecBidStartTime',
            'biddingPromotion'
          ]
          const needTecBid = findField('needTecBid')
          if (needTecBid) {
            for (const tecField of tecFields) {
              const row = findField(tecField)
              if (row) {
                if (!row.readonly) {
                  const readonly = !(Number(this.strategyConfigParams.needTecBid) === 1)
                  row.readonly = readonly
                }
                row.hideFormItem = row.readonly
              }
            }
          }
          // 非采竞价-隐藏技术投标及商务投标字段
          if (
            this.strategyConfigParams.businessTypeCode !== 'BTTCL004' &&
            this.strategyConfigParams.sourcingMode === 'bidding_price'
          ) {
            let timeFields = [
              'tecOpenBidTime',
              'tecScoreEndTime',
              'tecBidStartTime',
              'tenderStartTime',
              'openBidTime'
            ]
            strategyFieldDefines = strategyFieldDefines.filter(
              (item) => !timeFields.includes(item.fieldCode)
            )
          }
          this.strategyFieldDefines = cloneDeep(strategyFieldDefines)
          if (
            _flag &&
            (this.strategyConfigParams.sourcingMode === 'invite_bids' ||
              ej2EventObject.itemData.details)
          ) {
            let arr = []
            for (let x = 0; x < strategyFieldDefines.length; x++) {
              if (strategyFieldDefines[x].fieldCode === 'publicOptimalPrice') {
                continue
              } else if (strategyFieldDefines[x].fieldCode === 'publicRanking') {
                continue
              } else if (strategyFieldDefines[x].fieldCode === 'publicParticipantNum') {
                continue
              } else if (strategyFieldDefines[x].fieldCode === 'publicIdentity') {
                continue
              } else if (strategyFieldDefines[x].fieldCode === 'publicQuotation') {
                continue
              } else {
                arr.push(strategyFieldDefines[x])
              }
            }
            this.strategyFieldDefines = cloneDeep(arr)
          }
        },
        defaultValue: () => this.headerHeaderParams.strategyConfigId
      },
      // 币种 currencyName currencyName
      currencyName: {
        type: 'select',
        handler: (ej2EventObject) => {
          if (ej2EventObject.itemData == null) {
            return
          }
          Object.assign(this.formObject, ej2EventObject.itemData.data)
        },
        defaultValue: () => this.headerHeaderParams.currencyCode,
        api: this.$API.masterData.queryAllCurrency().then((res) => {
          return res.data.map((item) => ({
            text: item.currencyName,
            value: item.currencyCode,
            data: {
              currencyName: item.currencyName,
              currencyCode: item.currencyCode
            }
          }))
        })
      },
      // 汇率名称 exchangeRateName
      exchangeRateName: {
        type: 'select',
        allowFiltering: true,
        handler: (ej2EventObject, _, formVm) => {
          if (ej2EventObject.itemData == null) {
            return
          }
          Object.assign(this.formObject, ej2EventObject.itemData.data)
          formVm.form['exchangeRateCode'] = ej2EventObject.itemData.data.exchangeRateCode
          formVm.form['exchangeRateValue'] = ej2EventObject.itemData.data.exchangeRateValue
        },
        defaultValue: () => this.headerHeaderParams.exchangeRateName,
        api: this.$API.masterData.getExchange({ page: { current: 1, size: 1000 } }).then((res) => {
          return res.data?.records.map((item) => ({
            text:
              item.sourceCurrencyCode +
              '/' +
              item.targetCurrencyCode +
              '-' +
              item.sourceCurrencyName +
              '/' +
              item.targetCurrencyName,
            value: item.sourceCurrencyName + '/' + item.targetCurrencyName,
            data: {
              exchangeRateName: item.sourceCurrencyName + '/' + item.targetCurrencyName,
              exchangeRateCode: item.sourceCurrencyCode + '/' + item.targetCurrencyCode,
              exchangeRateValue: item.rate
            }
          }))
        })
      },
      // 汇率 exchangeRateValue
      exchangeRateValue: {
        type: 'number',
        readonly: true,
        defaultValue: () => this.headerHeaderParams.exchangeRateValue
      },
      // PCB版本号
      pcbCode: {
        type: 'selectedItemCode',
        handler: (ej2EventObject) => {
          if (ej2EventObject.itemData == null) {
            return
          }
          Object.assign(this.formObject, ej2EventObject.itemData.data)
        },
        defaultValue: () => this.headerHeaderParams.pcbCode
        // api: this.$API.masterData.queryAllCurrency().then((res) => {
        //   return res.data.map((item) => ({
        //     text: item.currencyName,
        //     value: item.pcbCode,
        //     data: {
        //       pcbCode: item.pcbCode
        //     }
        //   }))
        // })
      },
      // 获取组件号
      componentsCode: {
        type: 'getComponentCode',
        handler: (ej2EventObject) => {
          if (ej2EventObject.itemData == null) {
            return
          }
          Object.assign(this.formObject, ej2EventObject.itemData.data)
        }
        // defaultValue: () => this.headerHeaderParams.currencyCode
        // api: this.$API.masterData.queryAllCurrency().then((res) => {
        //   return res.data.map((item) => ({
        //     text: item.currencyName,
        //     value: item.currencyCode,
        //     data: {
        //       currencyName: item.currencyName,
        //       currencyCode: item.currencyCode
        //     }
        //   }))
        // })
      },
      // 扩展
      sourcingExpand: {
        type: 'multiSelect',
        handler: (ej2EventObject) => {
          if (ej2EventObject.itemData == null) {
            return
          }
          Object.assign(this.formObject, ej2EventObject.itemData.data)
        }
      }
    }
    this.strategyFormFieldConfig = {
      //寻源方向
      sourcingDirection: {
        type: 'select',
        // handler: (ej2EventObject) => {
        //   Object.assign(this.formObject, {
        //     sourcingDirection: ej2EventObject.value,
        //   });
        // },
        dataSource: [
          { text: this.$t('递增'), value: 'forward' },
          { text: this.$t('递减'), value: 'reverse' },
          { text: this.$t('无限制'), value: 'unlimited' }
        ],
        defaultValue: () => this.strategyConfigParams.sourcingDirection
      },
      //招标方式
      biddingMode: {
        type: 'select',
        api: this.$API.masterData
          .dictionaryGetList({
            dictCode: 'biddingMode'
          })
          .then((res) => {
            return res.data.map((item) => ({
              text: item.itemName,
              value: item.itemCode
            }))
          }),
        // dataSource: [
        //   { text: this.$t("公开"), value: "open" },
        //   { text: this.$t("邀请"), value: "target" },
        //   { text: this.$t("合作伙伴公开"), value: "supplierOpen" },
        // ],
        defaultValue: () => this.strategyConfigParams.biddingMode
      },
      //最少供应商报价数量
      minSupplierBiddingQuantity: {
        type: 'number',
        defaultValue: () => this.strategyConfigParams.minSupplierBiddingQuantity
      },
      //是否定向议价
      directionalBargaining: {
        type: 'select',
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { text: this.$t('是'), value: '1' },
          { text: this.$t('否'), value: '0' }
        ],
        defaultValue: () => this.strategyConfigParams.directionalBargaining,
        handler: (event, _, formVm) => {
          const nonDirectionalSupNum = this.strategyFieldDefines.find(
            (e) => e.fieldCode === 'nonDirectionalSupNum'
          )
          if (nonDirectionalSupNum) {
            const oldReadonly = !nonDirectionalSupNum.detail.editEnable
            let readonly = !!(Number(event.value) === 1)
            if (oldReadonly) {
              readonly = oldReadonly
            }
            if (readonly) {
              formVm.form = {
                ...formVm.form,
                nonDirectionalSupNum: 0
              }
            }
            nonDirectionalSupNum.readonly = readonly
            nonDirectionalSupNum.form.readonly = readonly
          }
        }
      },
      //非定向议价供应商数量
      nonDirectionalSupNum: {
        type: 'number',
        defaultValue: () => this.strategyConfigParams.nonDirectionalSupNum
      },
      //供应商选择范围
      supplierSelectionRange: {
        type: 'select',
        api: this.$API.masterData
          .dictionaryGetList({
            dictCode: 'SupplierRange'
          })
          .then((res) => {
            return res.data.map((item) => ({
              text: item.itemName,
              value: item.itemCode
            }))
          }),
        // dataSource: [
        //   { text: this.$t("品类合格"), value: "category_qualified" },
        //   { text: this.$t("品类合格+有价格记录"), value: "price_record" },
        //   { text: this.$t("品类合格+无价格记录"), value: "non_price_Record" },
        //   { text: this.$t("所有供应商"), value: "all_supplier" },
        // ],
        defaultValue: () => this.strategyConfigParams.supplierSelectionRange
      },
      //是否密封报价
      sealedPrice: {
        type: 'select',
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { text: this.$t('是'), value: '1' },
          { text: this.$t('否'), value: '0' }
        ],
        defaultValue: () => this.strategyConfigParams.sealedPrice
      },
      //报价截止时间
      quotationEndTime: {
        type: 'datetime',
        min: this.getMinTime(new Date()),
        defaultValue: () => this.strategyConfigParams.quotationEndTime
      },
      //应标截止时间
      responseBidEndTime: {
        type: 'datetime',
        min: this.getMinTime(new Date()),
        defaultValue: () => this.strategyConfigParams.responseBidEndTime
      },
      //投标开始时间
      tenderStartTime: {
        type: 'datetime',
        format: 'yyyy-MM-dd HH:mm:ss',
        'time-stamp': true,
        'show-clear-button': false,
        min: this.getMinTime(new Date()),
        defaultValue: () => this.strategyConfigParams.tenderStartTime,
        handler: (ej2EventObject) => {
          if (ej2EventObject.value) {
            this.strategyConfigParams.tenderStartTime = ej2EventObject.value
            // 直接更新第一轮开始时间
            if (this.roundObj.length > 0) {
              this.$nextTick(() => {
                this.$refs.roundForm0[0].form.roundStartTime = ej2EventObject.value
                this.$refs.roundForm0[0].$forceUpdate()
              })
            }
          }
        }
      },
      //开标时间
      openBidTime: {
        type: 'datetime',
        format: 'yyyy-MM-dd HH:mm:ss',
        'time-stamp': true,
        'show-clear-button': false,
        min: this.getMinTime(new Date()),
        defaultValue: () => this.strategyConfigParams.openBidTime,
        handler: (ej2EventObject) => {
          if (ej2EventObject.value) {
            this.strategyConfigParams.openBidTime = ej2EventObject.value
            // 直接更新最后一轮结束时间
            if (this.roundObj.length > 0) {
              this.$nextTick(() => {
                const lastRoundIndex = this.roundObj.length - 1
                this.$refs[`roundForm${lastRoundIndex}`][0].form.roundEndTime = ej2EventObject.value
                this.$refs[`roundForm${lastRoundIndex}`][0].$forceUpdate()
              })
            }
          }
        }
      },
      //公开最优价
      publicOptimalPrice: {
        type: 'select',
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { text: this.$t('是'), value: '1' },
          { text: this.$t('否'), value: '0' }
        ],
        defaultValue: () => this.strategyConfigParams.publicOptimalPrice
      },
      //公开排名
      publicRanking: {
        type: 'select',
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { text: this.$t('是'), value: '1' },
          { text: this.$t('否'), value: '0' }
        ],
        defaultValue: () => this.strategyConfigParams.publicRanking
      },
      //公开报价
      publicQuotation: {
        type: 'select',
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { text: this.$t('是'), value: '1' },
          { text: this.$t('否'), value: '0' }
        ],
        defaultValue: () => this.strategyConfigParams.publicQuotation
      },
      //公开身份
      publicIdentity: {
        type: 'select',
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { text: this.$t('是'), value: '1' },
          { text: this.$t('否'), value: '0' }
        ],
        defaultValue: () => this.strategyConfigParams.publicIdentity
      },
      //公开参与者数量
      publicParticipantNum: {
        type: 'select',
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { text: this.$t('是'), value: '1' },
          { text: this.$t('否'), value: '0' }
        ],
        defaultValue: () => this.strategyConfigParams.publicParticipantNum
      },
      //供应商报价规则
      supplierQuotationRule: {
        type: 'select',
        dataSource: [
          { text: this.$t('不限(默认)'), value: '0' },
          { text: this.$t('报价胜过整体最优'), value: '1' },
          { text: this.$t('报价胜过竞价人最优报价'), value: '2' }
        ],
        defaultValue: () => this.strategyConfigParams.supplierQuotationRule
      },
      //投标时间扩展
      tenderExtendTime: {
        type: 'number'
      },
      //扩展次数
      expansionNum: {
        type: 'number'
      },
      //剩余时间触发扩展
      triggerExtendRemainingTime: {
        type: 'number'
      },
      //配额是否必输
      allocationRequired: {
        type: 'select',
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { text: this.$t('是'), value: '1' },
          { text: this.$t('否'), value: '0' }
        ],
        defaultValue: () => {
          var defaultAllocationRequired = this.strategyConfigParams.allocationRequired
          let _arr = ['structure_component', 'single_module', 'serial_item', 'cost_factor']
          if (_arr.includes(this.strategyConfigParams.sourcingObjType)) {
            defaultAllocationRequired = '0'
          }
          return defaultAllocationRequired
        },
        handler: (event, _, formVm) => {
          let { value } = event
          if (value === '0') {
            formVm.form['allocationRange'] = '0'
          } else if (value === '1') {
            formVm.form['allocationRange'] = '1'
          }
        }
      },
      //中标数量是否必输
      bidNumRequired: {
        type: 'select',
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { text: this.$t('是'), value: '1' },
          { text: this.$t('否'), value: '0' }
        ],
        defaultValue: () => this.strategyConfigParams.bidNumRequired
      },
      //配额分配范围
      allocationRange: {
        type: 'select',
        dataSource: [
          { text: this.$t('当前寻源单'), value: '0' },
          { text: this.$t('所有有效配额'), value: '1' }
        ],
        defaultValue: () => {
          var defaultAllocationRange = this.strategyConfigParams.allocationRange
          let _arr = ['structure_component', 'single_module', 'serial_item', 'cost_factor']
          if (_arr.includes(this.strategyConfigParams.sourcingObjType)) {
            defaultAllocationRange = '0'
          }
          return defaultAllocationRange
        }
      },
      //配额分配比例
      allocationRatio: {
        type: 'allocationRatio'
      },
      //招标单是否晋级
      biddingPromotion: {
        type: 'select',
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { text: this.$t('是'), value: '1' },
          { text: this.$t('否'), value: '0' }
        ],
        defaultValue: () => this.strategyConfigParams.biddingPromotion
      },
      //轮次总计
      roundCount: {
        type: 'select',
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { text: '1', value: '1' },
          { text: '2', value: '2' },
          { text: '3', value: '3' },
          { text: '4', value: '4' },
          { text: '5', value: '5' }
        ],
        defaultValue: () => this.strategyConfigParams.roundCount,
        handler: (event, _, formVm) => {
          // 处理报价轮次
          this.handlerRound(event, formVm)
        }
      },
      // 核算方式
      accountingMode: {
        type: 'select',
        dataSource: [
          { text: this.$t('整单中标'), value: 'single_item' },
          { text: this.$t('部分中标'), value: 'multi_item' }
        ],
        defaultValue: () => this.strategyConfigParams.accountingMode,
        handler: (event, _, formVm) => {
          let { value } = event
          if (value === 'single_item') {
            formVm.form['priceControl'] = 'all'
          }
        }
      },
      // 评标方法
      bidEvaluationMode: {
        type: 'select',
        dataSource: [
          { text: this.$t('最低价评分法'), value: 'mini_price' },
          { text: this.$t('综合评分法'), value: 'no_controller' }
        ],
        defaultValue: () => this.strategyConfigParams.bidEvaluationMode
      },
      // 技术投标开始时间
      tecBidStartTime: {
        type: 'datetime',
        format: 'yyyy-MM-dd HH:mm:ss',
        'time-stamp': true,
        'show-clear-button': false,
        min: this.getMinTime(new Date()),
        defaultValue: () => this.strategyConfigParams.tecBidStartTime
      },
      // 技术开标时间
      tecOpenBidTime: {
        type: 'datetime',
        format: 'yyyy-MM-dd HH:mm:ss',
        'time-stamp': true,
        'show-clear-button': false,
        min: this.getMinTime(new Date()),
        defaultValue: () => this.strategyConfigParams.tecOpenBidTime
      },
      // 技术评分截止时间
      tecScoreEndTime: {
        type: 'datetime',
        format: 'yyyy-MM-dd HH:mm:ss',
        'time-stamp': true,
        'show-clear-button': false,
        min: this.getMinTime(new Date()),
        defaultValue: () => this.strategyConfigParams.tecScoreEndTime
      },
      // 是否需要技术投标
      needTecBid: {
        type: 'select',
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { text: this.$t('是'), value: '1' },
          { text: this.$t('否'), value: '0' }
        ],
        defaultValue: () => this.strategyConfigParams.needTecBid,
        handler: (arg, _, formVm) => {
          const { value } = arg
          const readonly = !(Number(value) === 1)
          const fields = [
            'tecOpenBidTime',
            'tecScoreEndTime',
            'tecBidStartTime',
            'biddingPromotion'
          ]
          for (const field of fields) {
            const row = this.strategyFieldDefines.find((item) => item.fieldCode === field)
            const oldReadonly = !row?.detail.editEnable
            if (row && !oldReadonly) {
              // 如果原始配置不让编辑, 则不处理
              row.readonly = readonly
              if (row.form) {
                row.form.readonly = readonly
                row.form.hideFormItem = readonly
              }
            }
            // 如果【是否需要技术投标】 = 【否】
            // 则 【招标单是否晋级】 = 【否】 且只读
            if (row && readonly && field === 'biddingPromotion') {
              formVm.form = {
                ...formVm.form,
                biddingPromotion: '0'
              }
            }
          }
        }
      },
      // 定点模式 1：内部定点，2：推荐定点
      pointMode: {
        type: 'select',
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { text: this.$t('内部定点'), value: '1' },
          { text: this.$t('推荐定点'), value: '2' }
        ],
        defaultValue: () => this.strategyConfigParams.pointMode
      },
      // 报价控制
      priceControl: {
        type: 'select',
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { text: this.$t('整单报价'), value: 'all' },
          { text: this.$t('首次整单报价'), value: 'first_all' },
          { text: this.$t('无限制'), value: 'unlimited' }
        ],
        defaultValue: () => this.strategyConfigParams.priceControl
      },
      // 报价方向控制
      priceDirectionControl: {
        type: 'select',
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { text: this.$t('整单控制'), value: 'all' },
          { text: this.$t('单行控制'), value: 'item' }
        ],
        defaultValue: () => this.strategyConfigParams.priceDirectionControl
      },
      // 整单排名
      totalRanking: {
        type: 'select',
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { text: this.$t('否'), value: '' },
          { text: this.$t('是'), value: 'quantityWeighting' }
        ],
        defaultValue: () => this.strategyConfigParams.totalRanking
      },
      // 是否每轮淘汰供应商
      eliminateFlag: {
        type: 'select',
        dataSource: [
          { text: this.$t('是'), value: '1' },
          { text: this.$t('否'), value: '0' }
        ],
        handler: (event, _, formVm) => {
          const supplierNoLimit = this.strategyFieldDefines.find(
            (e) => e.fieldCode === 'supplierNoLimit'
          )
          if (supplierNoLimit) {
            const oldReadonly = !supplierNoLimit.detail.editEnable
            let readonly = !(Number(event.value) === 1)
            if (oldReadonly) {
              readonly = oldReadonly
            }
            if (readonly) {
              formVm.form = {
                ...formVm.form,
                supplierNoLimit: 0
              }
            }
            supplierNoLimit.readonly = readonly
            supplierNoLimit.form.readonly = readonly
          }

          this.roundObj.forEach((item) => {
            item.definesList.forEach((item) => {
              if (item.fieldCode === 'eliminateRule') {
                item.form.readonly = !(Number(event.value) === 1)
                item.form.hideFormItem = !(Number(event.value) === 1)
              }
            })
          })
        }
      },
      // 拟中标供应商数量
      supplierNoLimit: {
        type: 'number'
      }
    }
    this.roundFormFieldConfig = {
      //公开最优价
      publicOptimalPrice: {
        type: 'select',
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { text: this.$t('是'), value: '1' },
          { text: this.$t('否'), value: '0' }
        ],

        defaultValue: () => this.strategyConfigParams.publicOptimalPrice
      },
      //公开排名
      publicRanking: {
        type: 'select',
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { text: this.$t('是'), value: '1' },
          { text: this.$t('否'), value: '0' }
        ],
        defaultValue: () => this.strategyConfigParams.publicRanking
      },
      //公开报价
      publicQuotation: {
        type: 'select',
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { text: this.$t('是'), value: '1' },
          { text: this.$t('否'), value: '0' }
        ],
        defaultValue: () => this.strategyConfigParams.publicQuotation
      },
      //公开身份
      publicIdentity: {
        type: 'select',
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { text: this.$t('是'), value: '1' },
          { text: this.$t('否'), value: '0' }
        ],
        defaultValue: () => this.strategyConfigParams.publicIdentity
      },
      //公开参与者数量
      publicParticipantNum: {
        type: 'select',
        fields: { value: 'value', text: 'text' },
        dataSource: [
          { text: this.$t('是'), value: '1' },
          { text: this.$t('否'), value: '0' }
        ],
        defaultValue: () => this.strategyConfigParams.publicParticipantNum
      },
      //结束时间
      roundEndTime: {
        type: 'datetime',
        format: 'yyyy-MM-dd HH:mm:ss',
        'time-stamp': true,
        'show-clear-button': false,
        min: this.getMinTime(new Date()),
        defaultValue: () => this.strategyConfigParams.roundEndTime
      },
      //开始时间
      roundStartTime: {
        type: 'datetime',
        format: 'yyyy-MM-dd HH:mm:ss',
        'time-stamp': true,
        'show-clear-button': false,
        min: this.getMinTime(new Date()),
        defaultValue: () => this.strategyConfigParams.roundStartTime
      },
      // 淘汰供应商
      eliminateRule: {
        type: 'select',
        dataSource: [
          { text: this.$t('不淘汰'), value: 0 },
          { text: this.$t('最后1名'), value: 1 },
          { text: this.$t('最后2名'), value: 2 },
          { text: this.$t('最后3名'), value: 3 },
          { text: this.$t('最后4名'), value: 4 },
          { text: this.$t('最后5名'), value: 5 },
          { text: this.$t('最后6名'), value: 6 },
          { text: this.$t('最后7名'), value: 7 },
          { text: this.$t('最后8名'), value: 8 },
          { text: this.$t('最后9名'), value: 9 },
          { text: this.$t('最后10名'), value: 10 }
        ]
      }
    }
  },
  mounted() {
    this.$API.rfxList.addRfxHeaderValid().then((res) => {
      if (res.code === 200) {
        let _formRules = this.$utils.formatRules(res.data)
        Object.keys(_formRules).forEach((e) => {
          if (_formRules[e].length < 1) {
            delete _formRules[e]
          }
        })
        this.formRules = _formRules
        console.log('表头创建-校验', this.formRules)
      } else {
        Promise.reject(res)
      }
    })
    this.getUserInfo()
  },
  watch: {
    headerFixedParams: {
      handler() {
        if (
          this.headerFixedParams.sourcingObj &&
          this.headerFixedParams.sourcingMode &&
          this.headerFixedParams.businessTypeCode
        ) {
          this.createRFXLoading = false
          if (
            this.headerFixedParams.sourcingObj == this.cacheHeaderFixedParams.sourcingObj &&
            this.headerFixedParams.sourcingMode == this.cacheHeaderFixedParams.sourcingMode &&
            this.headerFixedParams.businessTypeCode == this.cacheHeaderFixedParams.businessTypeCode
          ) {
            return
          } else {
            this.cacheHeaderFixedParams.sourcingObj = this.headerFixedParams.sourcingObj
            this.cacheHeaderFixedParams.sourcingMode = this.headerFixedParams.sourcingMode
            this.cacheHeaderFixedParams.businessTypeCode = this.headerFixedParams.businessTypeCode
            this.getHeaderConfigFields().catch(() => {
              this.headerFieldDefines = []
              this.strategyFieldDefines = []
            })
          }
        } else {
          this.createRFXLoading = true
        }
      },
      deep: true
    },
    tvStrategyId: {
      handler(v) {
        this.headerHeaderParams.strategyConfigId = v
        let _obj = {
          itemData: this.tvStrategyDetails
        }
        this.headerFormFieldConfig.strategyConfigName.handler(_obj)
      }
    }
  },
  methods: {
    handlerRound(event, formVm) {
      this.roundObj = []
      this.showTagCurrentRound = true
      for (let index = 0; index < Number(event.value); index++) {
        this.roundObj.push({
          roundNo: index + 1,
          definesList: this.wheelMeterList
        })
      }
      if (event.value == 1) {
        this.$nextTick(() => {
          this.$refs.roundForm0[0].form.roundStartTime = formVm.form.tenderStartTime
          this.$refs.roundForm0[0].form.roundEndTime = formVm.form.openBidTime
        })
      }
      if (event.value == 2) {
        this.$nextTick(() => {
          this.$refs.roundForm0[0].form.roundStartTime = formVm.form.tenderStartTime
          this.$refs.roundForm0[0].form.roundEndTime = ''
          this.$refs.roundForm1[0].form.roundEndTime = formVm.form.openBidTime
        })
      }
      if (event.value == 3) {
        this.$nextTick(() => {
          this.$refs.roundForm0[0].form.roundStartTime = formVm.form.tenderStartTime
          this.$refs.roundForm1[0].form.roundStartTime = ''
          this.$refs.roundForm1[0].form.roundEndTime = ''
          this.$refs.roundForm2[0].form.roundEndTime = formVm.form.openBidTime
        })
      }
      if (event.value == 4) {
        this.$nextTick(() => {
          this.$refs.roundForm0[0].form.roundStartTime = formVm.form.tenderStartTime
          this.$refs.roundForm1[0].form.roundStartTime = ''
          this.$refs.roundForm1[0].form.roundEndTime = ''
          this.$refs.roundForm2[0].form.roundStartTime = ''
          this.$refs.roundForm2[0].form.roundEndTime = ''
          this.$refs.roundForm3[0].form.roundEndTime = formVm.form.openBidTime
        })
      }
      if (event.value == 4) {
        this.$nextTick(() => {
          this.$refs.roundForm0[0].form.roundStartTime = formVm.form.tenderStartTime
          this.$refs.roundForm1[0].form.roundStartTime = ''
          this.$refs.roundForm1[0].form.roundEndTime = ''
          this.$refs.roundForm2[0].form.roundStartTime = ''
          this.$refs.roundForm2[0].form.roundEndTime = ''
          this.$refs.roundForm3[0].form.roundStartTime = ''
          this.$refs.roundForm3[0].form.roundEndTime = ''
          this.$refs.roundForm4[0].form.roundEndTime = formVm.form.openBidTime
        })
      }
      if (this.$route.query.source != 'invite_bids') {
        return
      }
    },
    getMinTime(data) {
      let _time
      if (data.getMinutes() > 30) {
        _time = new Date(data.toLocaleDateString() + ' ' + (data.getHours() + 1) + ':00:00')
      } else {
        _time = new Date(data.toLocaleDateString() + ' ' + data.getHours() + ':30:00')
      }
      return _time
    },
    ValidatorNum(rule, value, callback) {
      if (value > 9999999999999999) {
        callback(new Error('数量不能多于16位数字'))
      } else {
        callback()
      }
    },
    resetFieldForm() {
      // 切换询价对象、业务类型，重置表头信息、策略配置信息、轮次信息
      this.headerFieldDefines = []
      this.strategyFieldDefines = []
      this.roundObj = []
    },
    back() {
      this.$router.back()
      // this.$destroy("create");
    },
    getHeaderConfigFields() {
      return this.$API.rfxList.getUserConfigFields(this.headerFixedParams).then((res) => {
        if (res?.data?.fieldDefines) {
          if (Array.isArray(res.data.fieldDefines) && res.data.fieldDefines.length) {
            this.headerFieldDefines = res.data.fieldDefines
            this.headerFieldDefines.sort((a, b) => {
              return b.sortValue - a.sortValue
            })
            return this.getStrategyConfigFields()
          } else {
            this.$toast({
              type: 'warning',
              content: this.$t('头部字段，为空数组')
            })
            return Promise.reject(res)
          }
        } else {
          return Promise.reject(res)
        }
      })
    },
    getStrategyConfigFields() {
      return this.$API.rfxList.getStrategyConfigFields(this.strategyConfigParams).then((res) => {
        if (res.data?.length) {
          this.headerFormFieldConfig.strategyConfigName.dataSource = res.data.map((item) => {
            if (item.strategyCode === 'TV_common') {
              this.tvStrategyId = this.formObject?.templateCode === 'TV_CommonRFQ' ? item.id : ''
              this.tvStrategyDetails =
                this.formObject?.templateCode === 'TV_CommonRFQ' ? item : null
            }
            if (item.details?.length) {
              item.details.sort((a, b) => b.sortValue - a.sortValue)
              item.text = item.strategyName
              item.value = item.id
              item.data = item.details
                .filter((detail) => detail.enableStatus)
                .map((detail) => ({
                  detail,
                  fieldCode: detail.strategyCode,
                  fieldName: detail.strategyName,
                  readonly: !detail.editEnable
                })) // 改为过滤enableStatus=1的数据。后端反馈，可以不用过滤，返回的数据都是enableStatus=1。
              return item
            }
          })
          this.updateIndex++
        }
      })
    },
    //获取当前用户信息
    getUserInfo() {
      this.$API.iamService.getUserDetail().then((res) => {
        // this.formObject.companyId = res.data.companyOrg.id;
        // this.formObject.companyCode = res.data.companyOrg.orgCode;
        this.formObject.deptId = res?.data?.department?.id
        this.formObject.deptCode = res?.data?.department?.orgCode
        this.formObject.deptName = res?.data?.department?.orgName
      })
    },
    hasForm(fieldCode) {
      return !!this.strategyFieldDefines.find((e) => e.fieldCode === fieldCode)
    },
    validateRfxHeader(headerFormData) {
      const rules = [
        {
          message: this.$t('是否需要技术投标不能为空'),
          handler: (row) =>
            this.strategyConfigParams.businessTypeCode !== 'BTTCL004' &&
            this.hasForm('needTecBid') &&
            !row.needTecBid &&
            row.needTecBid !== '0'
        },
        {
          message: this.$t('商务投标开始时间不能为空'),
          handler: (row) => this.hasForm('tenderStartTime') && !row.tenderStartTime
        },
        {
          message: this.$t('商务开标时间不能为空'),
          handler: (row) => this.hasForm('openBidTime') && !row.openBidTime
        },
        {
          message: this.$t('技术投标开始时间不能为空'),
          handler: (row) =>
            this.hasForm('needTecBid') &&
            this.hasForm('tecBidStartTime') &&
            !!Number(row.needTecBid) &&
            !row.tecBidStartTime
        },
        {
          message: this.$t('技术开标时间不能为空'),
          handler: (row) =>
            this.hasForm('needTecBid') &&
            this.hasForm('tecOpenBidTime') &&
            !!Number(row.needTecBid) &&
            !row.tecOpenBidTime
        },
        {
          message: this.$t('非定向议价供应商数量应大于0'),
          handler: (row) => {
            return (
              this.hasForm('directionalBargaining') &&
              this.hasForm('nonDirectionalSupNum') &&
              Number(row?.directionalBargaining) === 0 &&
              (!row?.nonDirectionalSupNum || row?.nonDirectionalSupNum <= 0)
            )
          }
        }
      ]
      for (let i = 0; i < rules.length; i++) {
        const rule = rules[i]
        if (rule.handler(headerFormData.strategyRequest)) {
          this.$toast({ type: 'warning', content: rule.message })
          return false
        }
      }
      return true
    },
    validateRound(list) {
      if (!list || list.length === 0) return true
      for (let i = 0; i < list.length; i++) {
        if (!list[i].roundStartTime) {
          this.$toast({ type: 'warning', content: this.$t('请选择轮次开始时间') })
          return false
        }
        if (!list[i].roundEndTime) {
          this.$toast({ type: 'warning', content: this.$t('请选择轮次结束时间') })
          return false
        }
        if (list[i].roundEndTime < list[i].roundStartTime) {
          this.$toast({ type: 'warning', content: this.$t('轮次开始时间不能大于轮次结束时间') })
          return false
        }
      }
      return true
    },
    createRFX() {
      let list = []
      if (this.roundObj.length == 1) {
        this.$refs.roundForm0[0].form.roundNo = 1
        list.push(this.$refs.roundForm0[0].form)
      }
      if (this.roundObj.length == 2) {
        this.$refs.roundForm0[0].form.roundNo = 1
        list.push(this.$refs.roundForm0[0].form)
        this.$refs.roundForm1[0].form.roundNo = 2
        list.push(this.$refs.roundForm1[0].form)
      }
      if (this.roundObj.length == 3) {
        this.$refs.roundForm0[0].form.roundNo = 1
        list.push(this.$refs.roundForm0[0].form)
        this.$refs.roundForm1[0].form.roundNo = 2
        list.push(this.$refs.roundForm1[0].form)
        this.$refs.roundForm2[0].form.roundNo = 3
        list.push(this.$refs.roundForm2[0].form)
      }
      if (this.roundObj.length == 4) {
        this.$refs.roundForm0[0].form.roundNo = 1
        list.push(this.$refs.roundForm0[0].form)
        this.$refs.roundForm1[0].form.roundNo = 2
        list.push(this.$refs.roundForm1[0].form)
        this.$refs.roundForm2[0].form.roundNo = 3
        list.push(this.$refs.roundForm2[0].form)
        this.$refs.roundForm3[0].form.roundNo = 4
        list.push(this.$refs.roundForm3[0].form)
      }
      if (this.roundObj.length == 5) {
        this.$refs.roundForm0[0].form.roundNo = 1
        list.push(this.$refs.roundForm0[0].form)
        this.$refs.roundForm1[0].form.roundNo = 2
        list.push(this.$refs.roundForm1[0].form)
        this.$refs.roundForm2[0].form.roundNo = 3
        list.push(this.$refs.roundForm2[0].form)
        this.$refs.roundForm3[0].form.roundNo = 4
        list.push(this.$refs.roundForm3[0].form)
        this.$refs.roundForm4[0].form.roundNo = 5
        list.push(this.$refs.roundForm4[0].form)
      }
      this.createRFXLoading = true
      Promise.all([
        this.$refs.headerForm.parentGetFormData().then((formData) => {
          let purExecutorName = this.formObject.purExecutorName
          let purOrgName = this.formObject.purOrgName
          let returnObject = Object.assign(this.formObject, formData, this.strategyInfo)
          returnObject.purExecutorName = purExecutorName
          returnObject.purOrgName = purOrgName
          return returnObject
        }),
        this.$refs.fixedForm.parentGetFormData(),
        this.$refs.strategyForm.parentGetFormData()
      ])
        .then(([headerFormData, fixedFormData, strategyFormData]) => {
          let _saveParams = { ...headerFormData, ...fixedFormData }
          _saveParams.strategyRequest = strategyFormData
          if (!this.validateRfxHeader(_saveParams) || !this.validateRound(list)) {
            return Promise.reject()
          }
          list.forEach((e) => {
            e.roundCount = list.length
          })
          Object.keys(_saveParams.strategyRequest).forEach((e) => {
            if (isDate(_saveParams.strategyRequest[e])) {
              _saveParams.strategyRequest[e] = +_saveParams.strategyRequest[e]
            }
          })
          _saveParams.strategyConfigList = cloneDeep(list)
          const ids = this.$route.query?.ids
          if (ids) {
            _saveParams.detailIdList = JSON.parse(ids)
          }
          const saveAPI =
            this.pageType === 'gf'
              ? this.$API.rfxList.toSourcingApi
              : this.$API.rfxList.addRfxHeader

          return saveAPI(_saveParams).then((res) => {
            this.createRFXLoading = false
            if (res.code === 200) {
              this.$toast({
                type: 'success',
                content: this.$t('保存成功')
              })
              this.$router.push({
                name: 'hall-detail',
                query: {
                  status: 0, // 单据状态 0:草稿 1:进行中 2:已暂停 3:已关闭
                  rfxId: res.data.id,
                  source: this.$route.query.source,
                  companyCode: res.data.companyCode,
                  isNew: true //新建单据默认聚焦采购明细
                }
              })
            } else {
              this.$toast({ type: 'error', content: this.$t('保存失败') })
            }
          })
        })
        .catch(() => {
          this.createRFXLoading = false
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding: 20px 60px 20px 20px;
  .header {
    display: flex;
    // margin-bottom: 30px;
    .left {
      .split {
        font-weight: bold;
        color: #00469c;
        margin-left: 10px;
        margin-right: 8px;
        vertical-align: middle;
      }
      .page-name {
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(41, 41, 41, 1);
        vertical-align: middle;
      }
    }
    .space {
      flex: 1;
    }
  }
}
.round-container {
  background: #fff;

  .tag-current-round {
    background-color: #6386c1;
    border-radius: 10px;
    padding: 5px 20px;
    color: #fff;
    display: inline-block;
    margin-bottom: 20px;
  }

  /deep/ .strategy-form:not(:first-child) {
    border-top: solid 1px rgb(232, 232, 232);
  }

  /deep/ .e-input-group.e-error {
    border-bottom-color: rgba(0, 0, 0, 0.42);
    &::before,
    &::after {
      background: transparent !important;
    }
  }
}
</style>
