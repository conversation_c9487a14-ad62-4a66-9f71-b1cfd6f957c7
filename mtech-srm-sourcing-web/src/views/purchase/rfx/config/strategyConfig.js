import { i18n } from '@/main.js'

/**
 * 策略报告头部公共字段
 */
export const HEADER_FIELD = [
  'biddingMode',
  'sourcingDirection',
  'responseBidEndTime',
  'tenderStartTime', // 商务投标开始时间
  'openBidTime', // 商务开标时间
  'roundCount',
  'tecBidStartTime',
  'tecOpenBidTime',
  'tecScoreEndTime',
  'needTecBid',
  'biddingPromotion', // 招标单是否晋级
  'accountingMode', // 核算方式   改名‘定价规则’
  'bidEvaluationMode', // 评标方法
  'pointMode', // 定点模式 1：内部定点，2：推荐定点
  'allocationRatio', // 配额分配比例
  'allocationRequired', // 配额是否必输
  'allocationRange', // 配额分配范围
  'supplierSelectionRange',
  'sealedPrice',
  'minSupplierBiddingQuantity',
  'directionalBargaining', // 是否定向议价
  'nonDirectionalSupNum', // 非定向议价供应商数量
  'bidNumRequired', // 中标数量是否必输
  'triggerExtendRemainingTime', // 剩余时间触发扩展
  'expansionNum', // 扩展次数
  'tenderExtendTime', // 投标时间扩展
  'supplierQuotationRule', // 供应商报价规则
  'needExamAudit', // 是否需要资质审查
  'roundInterval', // 轮次间隔
  'priceControl', // 报价控制
  'priceDirectionControl', // 报价方向控制
  'totalRanking', //整单排名
  'eliminateFlag', // 淘汰标识 是否每轮淘汰供应商
  'supplierNoLimit' // 拟中标供应商数里
]

const MAX_SAFE_INTEGER = 999999999999999

const getMinTime = (data) => {
  let _time
  if (data.getMinutes() > 30) {
    _time = new Date(data.toLocaleDateString() + ' ' + (data.getHours() + 1) + ':00:00')
  } else {
    _time = new Date(data.toLocaleDateString() + ' ' + data.getHours() + ':30:00')
  }
  return _time
}

export const getStrategyConfig = () => {
  return {
    // bidEvaluationConfig
    // bidOpening
    // 招标方式 0：公开 1：邀请
    biddingMode: {
      type: 'select',
      fields: { value: 'value', text: 'text' },
      dataSource: [
        { text: i18n.t('邀请'), value: 'target' },
        { text: i18n.t('公开'), value: 'open' },
        { text: i18n.t('合作伙伴公开'), value: 'supplierOpen' }
      ]
    },
    // 招标单是否晋级(0否/1是)
    biddingPromotion: {
      type: 'select',
      fields: { value: 'value', text: 'text' },
      dataSource: [
        { text: i18n.t('是'), value: '1' },
        { text: i18n.t('否'), value: '0' }
      ]
    },
    // biddingStrategyConfig
    // crossRoundQuotationConfig
    // 寻源方向 0：不限（默认） 1：递增 2：递减
    sourcingDirection: {
      type: 'select',
      fields: { value: 'value', text: 'text' },
      dataSource: [
        { text: i18n.t('递增'), value: 'forward' },
        { text: i18n.t('递减'), value: 'reverse' },
        { text: i18n.t('无限制'), value: 'unlimited' }
      ]
    },
    // 是否定向议价 0：否1：是
    directionalBargaining: {
      type: 'select',
      fields: { value: 'value', text: 'text' },
      dataSource: [
        { text: i18n.t('是'), value: '1' },
        { text: i18n.t('否'), value: '0' }
      ]
    },
    // 扩展次数 false integer
    expansionNum: {
      type: 'number',
      min: 0,
      max: MAX_SAFE_INTEGER
    },
    // extConfig
    // id
    // innerRoundQuotationConfig
    // 最少供应商报价数量 false integer
    minSupplierBiddingQuantity: {
      type: 'number',
      min: 0,
      max: MAX_SAFE_INTEGER
    },
    // 非定向议价供应商数量 false integer
    nonDirectionalSupNum: {
      type: 'number',
      min: 0,
      max: MAX_SAFE_INTEGER
    },
    // 商务开标时间 openBidTime 日期控件
    openBidTime: {
      type: 'datetime',
      format: 'yyyy-MM-dd HH:mm:ss',
      'time-stamp': true,
      'show-clear-button': true,
      min: getMinTime(new Date())
    },
    // projectStrategyConfig
    // 是否公开身份(0否/1是)
    publicIdentity: {
      type: 'select',
      fields: { value: 'value', text: 'text' },
      dataSource: [
        { text: i18n.t('是'), value: '1' },
        { text: i18n.t('否'), value: '0' }
      ]
    },
    // 公开最优价 是否公开最优价(0否/1是)
    publicOptimalPrice: {
      type: 'select',
      fields: { value: 'value', text: 'text' },
      dataSource: [
        { text: i18n.t('是'), value: '1' },
        { text: i18n.t('否'), value: '0' }
      ]
    },
    // 公开参与者数量
    publicParticipantNum: {
      type: 'select',
      fields: { value: 'value', text: 'text' },
      dataSource: [
        { text: i18n.t('是'), value: '1' },
        { text: i18n.t('否'), value: '0' }
      ]
    },
    // 供应商选择范围 supplierSelectionRange 下拉框 供应商选择范围 0：品类合格 1：品类合格-有价格记录 2：品类合格-无价格记录
    supplierSelectionRange: {
      type: 'select',
      fields: { value: 'value', text: 'text' },
      dataSource: [
        { text: i18n.t('潜在'), value: 'category_potential' },
        { text: i18n.t('合格'), value: 'category_qualified' },
        { text: i18n.t('潜在/合格'), value: 'potential_qualified' },
        { text: i18n.t('所有供应商'), value: 'all_supplier' }
      ]
    },
    // 是否密封报价 sealedPrice 下拉框 是和否 是：1 否：0
    sealedPrice: {
      type: 'select',
      fields: { value: 'value', text: 'text' },
      dataSource: [
        { text: i18n.t('是'), value: '1' },
        { text: i18n.t('否'), value: '0' }
      ]
    },
    // 报价截止时间 quotationEndTime 日期控件
    quotationEndTime: {
      type: 'datetime',
      format: 'yyyy-MM-dd HH:mm:ss',
      'time-stamp': true,
      'show-clear-button': true,
      min: getMinTime(new Date())
    },
    // 应标截止时间 responseBidEndTime 日期控件
    responseBidEndTime: {
      type: 'datetime',
      format: 'yyyy-MM-dd HH:mm:ss',
      'time-stamp': true,
      'show-clear-button': true,
      min: getMinTime(new Date())
    },
    // 商务投标开始时间 tenderStartTime 日期控件
    tenderStartTime: {
      type: 'datetime',
      format: 'yyyy-MM-dd HH:mm:ss',
      'time-stamp': true,
      'show-clear-button': true,
      min: getMinTime(new Date())
    },
    // 是否公开报价(0否/1是)
    publicQuotation: {
      type: 'select',
      fields: { value: 'value', text: 'text' },
      dataSource: [
        { text: i18n.t('是'), value: '1' },
        { text: i18n.t('否'), value: '0' }
      ]
    },
    // 是否公开排名(0否/1是)
    publicRanking: {
      type: 'select',
      fields: { value: 'value', text: 'text' },
      dataSource: [
        { text: i18n.t('是'), value: '1' },
        { text: i18n.t('否'), value: '0' }
      ]
    },
    // quotaAllocation
    // quotaRange
    // 供应商报价规则 supplierQuotationRule 下拉框： 供应商报价规则 0：不限（默认）1：报价胜过整体最优2：报价胜过竞价人最优报价
    supplierQuotationRule: {
      type: 'select',
      fields: { value: 'value', text: 'text' },
      dataSource: [
        { text: i18n.t('不限(默认)'), value: '0' },
        { text: i18n.t('报价胜过整体最优'), value: '1' },
        { text: i18n.t('报价胜过竞价人最优报价'), value: '2' }
      ]
    },
    // 投标时间扩展 tenderExtendTime 输入款（分钟）
    tenderExtendTime: {
      type: 'number',
      min: 0,
      max: MAX_SAFE_INTEGER
    },

    // 剩余时间触发扩展 triggerExtendRemainingTime 输入款（分钟）
    triggerExtendRemainingTime: {
      type: 'number',
      min: 0,
      max: MAX_SAFE_INTEGER
    },
    // 配额是否必输 allocationRequired 下拉框 是和否 是：1 否：0
    allocationRequired: {
      type: 'select',
      fields: { value: 'value', text: 'text' },
      dataSource: [
        { text: i18n.t('是'), value: '1' },
        { text: i18n.t('否'), value: '0' }
      ],
      handler: (event, _, formVm) => {
        let { value } = event
        if (value === '0') {
          formVm.form['allocationRange'] = '0'
        } else if (value === '1') {
          formVm.form['allocationRange'] = '1'
        }
      }
    },
    // 配额分配范围 allocationRange 下拉框 当前寻源单：0 所有有效配额：1
    allocationRange: {
      type: 'select',
      fields: { value: 'value', text: 'text' },
      dataSource: [
        { text: i18n.t('当前寻源单'), value: '0' },
        { text: i18n.t('所有有效配额'), value: '1' }
      ]
    },
    // 配额分配比例
    allocationRatio: {
      type: 'allocationRatio'
    },
    // 中标数量是否必输 bidNumRequired 下拉框 是和否 是：1 否：0
    bidNumRequired: {
      type: 'select',
      fields: { value: 'value', text: 'text' },
      dataSource: [
        { text: i18n.t('是'), value: '1' },
        { text: i18n.t('否'), value: '0' }
      ]
    },
    // 轮次总计
    roundCount: {
      type: 'select',
      fields: { value: 'value', text: 'text' },
      dataSource: [
        { text: '1', value: '1' },
        { text: '2', value: '2' },
        { text: '3', value: '3' },
        { text: '4', value: '4' },
        { text: '5', value: '5' }
      ]
    },
    // 轮次结束时间
    roundEndTime: {
      type: 'datetime',
      format: 'yyyy-MM-dd HH:mm:ss',
      'time-stamp': true,
      'show-clear-button': true,
      min: getMinTime(new Date())
    },
    // 轮次开始时间
    roundStartTime: {
      type: 'datetime',
      format: 'yyyy-MM-dd HH:mm:ss',
      'time-stamp': true,
      'show-clear-button': true,
      min: getMinTime(new Date())
    },
    // 核算方式   改名‘定价规则’
    accountingMode: {
      type: 'select',
      fields: { value: 'value', text: 'text' },
      dataSource: [
        { text: i18n.t('整单中标'), value: 'single_item' },
        { text: i18n.t('部分中标'), value: 'multi_item' }
      ]
    },
    // 评标方法
    bidEvaluationMode: {
      type: 'select',
      fields: { value: 'value', text: 'text' },
      dataSource: [
        { text: i18n.t('最低价评分法'), value: 'mini_price' },
        { text: i18n.t('综合评分法'), value: 'no_controller' }
      ]
    },

    // 技术投标开始时间
    tecBidStartTime: {
      type: 'datetime',
      format: 'yyyy-MM-dd HH:mm:ss',
      'time-stamp': true,
      'show-clear-button': true,
      min: getMinTime(new Date())
    },
    // 技术开标时间
    tecOpenBidTime: {
      type: 'datetime',
      format: 'yyyy-MM-dd HH:mm:ss',
      'time-stamp': true,
      'show-clear-button': true,
      min: getMinTime(new Date())
    },
    // 技术评分截止时间
    tecScoreEndTime: {
      type: 'datetime',
      format: 'yyyy-MM-dd HH:mm:ss',
      'time-stamp': true,
      'show-clear-button': true,
      min: getMinTime(new Date())
    },
    // 是否需要技术投标
    needTecBid: {
      type: 'select',
      fields: { value: 'value', text: 'text' },
      dataSource: [
        { text: i18n.t('是'), value: '1' },
        { text: i18n.t('否'), value: '0' }
      ]
    },
    // 整单排名
    totalRanking: {
      type: 'select',
      fields: { value: 'value', text: 'text' },
      dataSource: [
        { text: i18n.t('否'), value: '' },
        { text: i18n.t('是'), value: 'quantityWeighting' }
      ]
    },
    // 定点模式 1：内部定点，2：推荐定点
    pointMode: {
      type: 'select',
      fields: { value: 'value', text: 'text' },
      dataSource: [
        { text: i18n.t('内部定点'), value: '1' },
        { text: i18n.t('推荐定点'), value: '2' }
      ]
    },
    // 是否需要资质审查
    needExamAudit: {
      type: 'select',
      fields: { value: 'value', text: 'text' },
      dataSource: [
        { text: i18n.t('是'), value: '1' },
        { text: i18n.t('否'), value: '0' }
      ]
    },
    // 轮次间隔（分钟）
    roundInterval: {
      type: 'number',
      min: 0,
      max: MAX_SAFE_INTEGER,
      precision: '0'
    },
    // 报价控制
    priceControl: {
      type: 'select',
      fields: { value: 'value', text: 'text' },
      dataSource: [
        { text: i18n.t('整单报价'), value: 'all' },
        { text: i18n.t('首次整单报价'), value: 'first_all' },
        { text: i18n.t('无限制'), value: 'unlimited' }
      ]
    },
    // 报价方向控制
    priceDirectionControl: {
      type: 'select',
      fields: { value: 'value', text: 'text' },
      dataSource: [
        { text: i18n.t('整单控制'), value: 'all' },
        { text: i18n.t('单行控制'), value: 'item' }
      ]
    },
    eliminateRule: {
      type: 'select',
      dataSource: [
        { text: i18n.t('不淘汰'), value: 0 },
        { text: i18n.t('最后1名'), value: 1 },
        { text: i18n.t('最后2名'), value: 2 },
        { text: i18n.t('最后3名'), value: 3 },
        { text: i18n.t('最后4名'), value: 4 },
        { text: i18n.t('最后5名'), value: 5 },
        { text: i18n.t('最后6名'), value: 6 },
        { text: i18n.t('最后7名'), value: 7 },
        { text: i18n.t('最后8名'), value: 8 },
        { text: i18n.t('最后9名'), value: 9 },
        { text: i18n.t('最后10名'), value: 10 }
      ]
    },
    // 是否每轮淘汰供应商
    eliminateFlag: {
      type: 'select',
      dataSource: [
        { text: i18n.t('是'), value: '1' },
        { text: i18n.t('否'), value: '0' }
      ]
      // handler: (event, _, formVm) => {
      //   const supplierNoLimit = this.strategyFieldDefines.find(
      //     (e) => e.fieldCode === 'supplierNoLimit'
      //   )
      //   if (supplierNoLimit) {
      //     const oldReadonly = !supplierNoLimit.detail.editEnable
      //     let readonly = !!(Number(event.value) === 1)
      //     if (oldReadonly) {
      //       readonly = oldReadonly
      //     }
      //     if (readonly) {
      //       formVm.form = {
      //         ...formVm.form,
      //         supplierNoLimit: 0
      //       }
      //     }
      //     supplierNoLimit.readonly = readonly
      //     supplierNoLimit.form.readonly = readonly
      //   }

      //   this.roundObj.forEach((item) => {
      //     item.definesList.forEach((item) => {
      //       if (item.fieldCode === 'eliminateRule') {
      //         item.form.readonly = !(Number(event.value) === 1)
      //         item.form.hideFormItem = !(Number(event.value) === 1)
      //       }
      //     })
      //   })
      // }
    },
    // 拟中标供应商数量
    supplierNoLimit: {
      type: 'number'
    }
  }
}
