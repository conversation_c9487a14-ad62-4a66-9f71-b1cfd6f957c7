<template>
  <div class="full-height">
    <mt-local-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
              <mt-select
                v-model="searchFormModel.companyCode"
                css-class="rule-element"
                :data-source="companyList"
                :fields="{ text: 'text', value: 'orgCode' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择公司')"
                @change="(e) => getPurOrgList(e.itemData.id)"
              />
            </mt-form-item>
            <mt-form-item prop="purchaseOrgCode" :label="$t('采购组织')" label-style="top">
              <mt-select
                v-model="searchFormModel.purchaseOrgCode"
                css-class="rule-element"
                :data-source="purOrgList"
                :fields="{ text: 'text', value: 'organizationCode' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择采购组织')"
                @change="(e) => getIndicatorNameList(null, e.itemData.itemCode)"
              />
            </mt-form-item>
            <mt-form-item prop="indexId" :label="$t('币种')" label-style="top">
              <mt-select
                v-model="searchFormModel.currencyCode"
                css-class="rule-element"
                :data-source="currencyList"
                :fields="{ text: 'text', value: 'currencyCode' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择币种')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-local-template-page>
  </div>
</template>

<script>
import { pageConfig } from './config/index'
import mixin from './config/mixin'
// 引入本地的emplate-page组件
import MtLocalTemplatePage from '@/components/template-page'

export default {
  components: {
    MtLocalTemplatePage
  },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  mixins: [mixin],
  data() {
    return {
      searchFormModel: {},
      pageConfig
    }
  },
  methods: {
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    handleClickCellTitle(e) {
      if (e.field == 'templateCode') {
        this.redirectConfigDetail(e.data)
      }
    },
    // 点击按钮工具栏
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const selectedRecords = grid.getSelectedRecords()
      if (toolbar.id === 'Add') {
        this.handleAdd()
      } else if (toolbar.id === 'Delete') {
        if (selectedRecords.length === 0) {
          this.$toast({ content: this.$t('请先至少选择一行数据'), type: 'warning' })
          return
        }
        this.handleOperateData('delete', selectedRecords)
      }
    },
    // 点击单元格工具栏事件
    handleClickCellTool(e) {
      const { tool, data } = e
      switch (tool.id) {
        case 'edit':
          this.handleEdit(data)
          break
        case 'delete':
        case 'enable':
        case 'disable':
          this.handleOperateData(tool.id, [data])
          break
        default:
          break
      }
    },
    // 新增
    handleAdd() {
      this.$dialog({
        modal: () => import('./add.vue'),
        data: {
          title: this.$t('新增价格追溯配置'),
          type: 'add'
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 编辑
    handleEdit(data) {
      this.$dialog({
        modal: () => import('./add.vue'),
        data: {
          title: this.$t('编辑价格追溯配置'),
          type: 'edit',
          data
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 删除、启用、禁用
    handleOperateData(type, list) {
      const idList = []
      list.forEach((item) => {
        idList.push(item.id)
      })
      const titleMap = {
        delete: this.$t('删除'),
        enable: this.$t('启用'),
        disable: this.$t('禁用')
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认${titleMap[type]}数据？`)
        },
        success: () => {
          this.$loading()
          this.$API.priceTraceConfig[type + 'PriceTraceConfig']({ idList })
            .then(() => {
              this.$toast({ content: this.$t(`${titleMap[type]}成功！`), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
              this.$hloading()
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  /deep/.e-headercelldiv {
    padding: 0 0.6em 0 0.6em !important;
  }
}
</style>
