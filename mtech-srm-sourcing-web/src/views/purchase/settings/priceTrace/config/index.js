import Vue from 'vue'
import { i18n, permission } from '@/main.js'

export const statusList = [
  { value: 0, label: i18n.t('草稿'), cssClass: 'title-#9baac1' },
  { value: 1, label: i18n.t('启用'), cssClass: 'title-#6386c1' },
  { value: 2, label: i18n.t('禁用'), cssClass: 'title-#6386c1' }
]
export const booleanList = [
  { text: i18n.t('否'), value: 0, cssClass: '' },
  { text: i18n.t('是'), value: 1, cssClass: '' }
]

//模块功能流程配置
const columnData = [
  {
    width: 50,
    type: 'checkbox'
  },
  {
    field: 'lineIndexComponent',
    headerText: i18n.t('行号'),
    allowFiltering: false, // 序号列，不参与数据筛选
    allowResizing: false, // 序号列，不参与列顺序变化
    allowSorting: false, // 序号列，不参与列宽变化
    ignore: true, // 序号列，不参与数据筛选
    width: 80,
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: '<div>{{+data.index+1}}</div>',
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    width: 120,
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: statusList,
      fields: { text: 'label', value: 'value' }
    },
    cellTools: [
      'edit',
      'delete',
      {
        id: 'enable',
        title: i18n.t('启用'),
        visibleCondition: (data) => {
          return data['status'] !== 1
        }
      },
      {
        id: 'disable',
        title: i18n.t('禁用'),
        visibleCondition: (data) => {
          return data['status'] === 1
        }
      }
    ]
  },
  {
    width: 100,
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    width: '150',
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'purchaseOrgCode',
    headerText: i18n.t('采购组织编码')
  },
  {
    field: 'purchaseOrgName',
    headerText: i18n.t('采购组织名称')
  },
  {
    width: 100,
    field: 'currencyCode',
    headerText: i18n.t('币种编码')
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种名称')
  },
  {
    width: 120,
    field: 'isUntaxed',
    headerText: i18n.t('是否未税'),
    valueConverter: {
      type: 'map',
      map: booleanList
    }
  },
  {
    width: 100,
    field: 'emailAddrList',
    headerText: i18n.t('邮件地址'),
    valueAccessor: (field, data) => {
      if (Array.isArray(data.emailAddrList) && data.emailAddrList.length) {
        return data.emailAddrList.join(';')
      }
      return '-'
    }
  },
  {
    width: 80,
    field: 'version',
    headerText: i18n.t('版本')
  },
  {
    width: 100,
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: 150,
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    width: 100,
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

export const pageConfig = [
  {
    gridId: permission.gridId['purchase']['priceTraceConfig'],
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    useToolTemplate: false,
    toolbar: {
      useBaseConfig: false,
      tools: [
        ['Add', 'Delete'],
        ['Refresh', 'Setting']
      ]
    },
    grid: {
      columnData,
      asyncConfig: {
        url: '/price/tenant/priceRecord/retrospect/config/pageQuery'
      }
    }
  }
]
