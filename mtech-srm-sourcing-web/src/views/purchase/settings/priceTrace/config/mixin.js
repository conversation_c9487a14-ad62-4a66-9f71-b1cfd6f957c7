export default {
  data() {
    return {
      companyList: [],
      purOrgList: [],
      currencyList: []
    }
  },
  created() {
    this.getSelectList()
  },
  methods: {
    // 获取下拉列表
    getSelectList() {
      this.getCompanyList()
      this.getPurOrgList()
      this.getCurrencyList()
    },
    //获取公司
    async getCompanyList() {
      const res = await this.$API.masterData.permissionCompanyList()
      if (res.code === 200) {
        res.data?.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.companyList = res.data
      }
    },
    // 采购组织
    async getPurOrgList(companyId = '') {
      const res = await this.$API.masterData.permissionOrgList({ orgId: companyId })
      if (res.code === 200) {
        res.data?.forEach((item) => {
          item.text = item.organizationCode + '-' + item.organizationName
        })
        this.purOrgList = res.data
      }
    },
    // 获取币种下拉列表
    async getCurrencyList() {
      const res = await this.$API.masterData.queryAllCurrency()
      if (res.code === 200) {
        res.data?.forEach((item) => {
          item.text = item.currencyCode + '-' + item.currencyName
        })
        this.currencyList = res.data
      }
    }
  }
}
