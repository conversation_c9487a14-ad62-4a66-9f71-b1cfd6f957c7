<template>
  <div>
    <mt-dialog
      ref="dialogRef"
      css-class="create-proj-dialog"
      :buttons="buttons"
      :header="header"
      @beforeClose="cancel"
    >
      <div class="dialog-content">
        <mt-form ref="formRef" :model="formModel" :rules="rules">
          <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
            <mt-select
              v-model="formModel.companyCode"
              css-class="rule-element"
              :data-source="companyList"
              :fields="{ text: 'text', value: 'orgCode' }"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择公司')"
              @change="handleCompanyChange"
            />
          </mt-form-item>
          <mt-form-item prop="purchaseOrgCode" :label="$t('采购组织')" label-style="top">
            <mt-select
              v-model="formModel.purchaseOrgCode"
              css-class="rule-element"
              :data-source="purOrgList"
              :fields="{ text: 'text', value: 'organizationCode' }"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择采购组织')"
              :disabled="!formModel.companyCode"
              @change="handlePurOrgChang"
            />
          </mt-form-item>
          <mt-form-item prop="currencyCode" :label="$t('币种')" label-style="top">
            <mt-select
              v-model="formModel.currencyCode"
              css-class="rule-element"
              :data-source="currencyList"
              :fields="{ text: 'text', value: 'currencyCode' }"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择币种')"
              @change="(e) => (formModel.currencyName = e.itemData.currencyName)"
            />
          </mt-form-item>
          <mt-form-item prop="isUntaxed" :label="$t('是否未税')" label-style="top">
            <mt-select
              v-model="formModel.isUntaxed"
              css-class="rule-element"
              :data-source="booleanList"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择是否未税')"
            />
          </mt-form-item>
          <mt-form-item prop="emailStr" :label="$t('邮件地址')" label-style="top">
            <mt-input
              v-model="formModel.emailStr"
              :show-clear-button="true"
              :placeholder="$t('请填写邮件地址（多个用英文分号隔开）')"
            />
          </mt-form-item>
          <mt-form-item prop="remark" :label="$t('备注')" label-style="top">
            <mt-input
              v-model="formModel.remark"
              :show-clear-button="true"
              :placeholder="$t('请填写备注')"
            />
          </mt-form-item>
        </mt-form>
      </div>
    </mt-dialog>
  </div>
</template>
<script>
import mixin from './config/mixin'
import { booleanList } from './config/index'

export default {
  mixins: [mixin],
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formModel: {
        isUntaxed: 1
      },
      booleanList,
      rules: {},
      isInit: false
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    // 初始化
    async init() {
      if (this.modalData.type === 'edit') {
        const res = await this.$API.priceTraceConfig.getPriceTraceConfigDetailById({
          id: this.modalData.data.id
        })
        if (res.code === 200) {
          this.isInit = true
          this.formModel = {
            ...res.data,
            emailStr: res.data.emailAddrList.join(';')
          }
        }
      }
      this.$refs.dialogRef.ejsRef.show()
      this.$nextTick(() => {
        this.rules = {
          companyCode: [{ required: true, message: this.$t('请选择公司') }],
          purchaseOrgCode: [{ required: true, message: this.$t('请选择采购组织') }],
          currencyCode: [{ required: true, message: this.$t('请选择币种') }],
          isUntaxed: [{ required: true, message: this.$t('请选择是否未税') }],
          emailStr: [{ required: true, message: this.$t('请填写邮件地址（多个用英文分号隔开）') }]
        }
      })
    },
    // 选择公司
    handleCompanyChange(e) {
      if (!e.itemData) {
        this.formModel.companyName = null
      } else {
        const { id, orgName } = e.itemData
        this.formModel.companyName = orgName
        // 获取采购组织下拉列表
        this.getPurOrgList(id)
      }
      // 重置采购组织
      !this.isInit && this.handlePurOrgChang()
      this.isInit = false
    },
    // 选择采购组织
    handlePurOrgChang(e) {
      if (!e) {
        this.formModel = {
          ...this.formModel,
          purchaseOrgCode: null,
          purchaseOrgName: null
        }
        return
      }
      this.formModel.purchaseOrgName = e.itemData?.organizationName
    },
    // 确定
    confirm() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          const params = { ...this.formModel, emailAddrList: this.formModel.emailStr?.split(';') }
          this.$API.priceTraceConfig.savePriceTraceConfig(params).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('保存成功！'), type: 'success' })
              this.$emit('confirm-function')
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  padding-top: 20px;
}
</style>
