<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" width="1000" @beforeClose="cancel">
    <div class="dialog-content">
      <ul class="type-list">
        <li
          v-for="(item, index) in costTypeList"
          :key="index"
          @click="activeType = index"
          :class="[{ active: activeType == index }]"
        >
          {{ item.type }}
        </li>
      </ul>

      <div class="grid-content">
        <mt-template-page
          ref="templateRef"
          class="son-grid"
          :template-config="templateConfig"
          @handleClickToolBar="handleClickToolBar"
        />
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { costTypeList, pageConfig } from './config'

export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      pageConfig: pageConfig(this),
      dataSourceArr: [],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('确认') }
        }
      ],
      costTypeList,
      factorInfo: {},
      activeType: 0,
      columnMap: {
        0: 'category',
        1: 'item',
        2: 'factor'
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    editAble() {
      // 草稿、停用、驳回、废弃状态可编辑
      const enable = [-1, 0, 2, 4].includes(this.factorInfo.status)

      // 创建用户与当前登录用户是否一致

      // return enable && isSameUser
      return enable
    },
    templateConfig() {
      return pageConfig(this)
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.factorInfo = { ...this.modalData.data }
      // this.queryRel()
    }
  },
  methods: {
    queryRel() {
      if (this.factorInfo.id) {
        const DEFAULTPARAM = {
          condition: '',
          page: {
            current: 1,
            size: 1000
          },
          pageFlag: false,
          defaultRules: [
            {
              label: this.$t('因子ID'),
              field: this.modalData.queryId,
              type: 'number',
              operator: 'equal',
              value: this.factorInfo.id
            }
          ]
        }
        this.$API.costModel.queryRel(DEFAULTPARAM).then((r) => {
          let categoryRefArr = []
          let itemRefRefArr = []
          let factorRefArr = []
          r.data.records.forEach((e) => {
            const { relationId, relationCode, relationName, relationType } = e
            switch (relationType) {
              case 0:
                categoryRefArr.push({
                  id: relationId,
                  categoryCode: relationCode,
                  categoryName: relationName,
                  relationType
                })
                break
              case 1:
                itemRefRefArr.push({
                  id: e.relationId,
                  itemCode: e.relationCode,
                  itemName: e.relationName,
                  relationType
                })
                break
              case 2:
                factorRefArr.push({
                  id: relationId,
                  marketFactorCode: relationCode,
                  marketFactorName: relationName,
                  relationType
                })
                break
              default:
                break
            }
          })
          this.dataSourceArr = [categoryRefArr, itemRefRefArr, factorRefArr]
          // this.categoryConfig[0].grid.dataSource = categoryRefArr
          // this.itemConfig[0].grid.dataSource = itemRefRefArr
          // this.factorConfig[0].grid.dataSource = itemRefRefArr
        })
      }
    },
    cancel() {
      this.$emit('cancel-function')
    },
    handleClickToolBar(e) {
      const { toolbar, gridRef } = e

      const _selectGridRecords = gridRef.getMtechGridRecords()
      if (_selectGridRecords.length <= 0 && toolbar.id == 'del') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (toolbar.id === 'add') {
        this.handleAdd()
      } else if (toolbar.id === 'del') {
        this.handleDelete(_selectGridRecords)
      }
    },
    // 新增
    handleAdd() {
      this.$dialog({
        modal: () => import('./selectDialog.vue'),
        data: {
          title: costTypeList[this.activeType].type,
          type: this.columnMap[this.activeType],
          relationType: this.activeType,
          costModelId: this.factorInfo.id
        },
        success: () => {
          this.$toast({
            content: this.$t('操作成功！'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 删除
    handleDelete(_selectRecords) {
      const idList = []
      _selectRecords.forEach((item) => {
        idList.push(item.id)
      })
      const params = {
        costModelId: this.factorInfo.id,
        relationType: this.activeType,
        idList
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认执行删除操作？')
        },
        success: () => {
          this.$API.costModel.deleteRel(params).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: res.message || this.$t('删除成功'),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
.type-list {
  width: 150px;
  margin-right: 15px;
  flex-shrink: 0;
  text-align: center;
  li {
    line-height: 50px;
    border: 1px solid #e8e8e8;
    border-top: none;
    &:nth-of-type(1) {
      border-top: 1px solid #e8e8e8;
    }
    &:hover {
      background-color: #eeeeee;
    }
    &.active {
      background-color: #dadada;
    }
  }
}
.grid-content {
  position: relative;
  flex: 1;
  .son-grid {
    position: absolute;
  }
}
</style>
