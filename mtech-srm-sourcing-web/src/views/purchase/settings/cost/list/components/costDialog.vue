<template>
  <!-- @beforeClose="cancel" -->
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
    z-index="997"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="costModelName" :label="$t('成本模板名称')">
          <mt-input
            v-model="formObject.costModelName"
            float-label-type="Never"
            :placeholder="$t('请输入成本模板名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')">
          <mt-input
            v-model="formObject.remark"
            float-label-type="Never"
            :placeholder="$t('请输入备注')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('适用公司')">
          <div class="div-auth" @click="handleAuthDialog">
            {{ getFormAuthText }}
          </div>
        </mt-form-item>
        <!-- <mt-form-item :label="$t('适用品类')">
          <div class="div-auth" @click="handleAssocDialog">
            {{ getFormAssocText }}
          </div>
        </mt-form-item> -->
        <mt-form-item :label="$t('类型')" prop="costModelType">
          <mt-select
            :open-dispatch-change="false"
            v-model="formObject.costModelType"
            :data-source="typeList"
            :placeholder="$t('请选择')"
            @change="costModelTypeChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('是否暖通')" prop="isHavc" v-if="formObject.costModelType == 2">
          <mt-select
            v-model="formObject.isHavc"
            :data-source="booleanList"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('是否联动')" prop="isLinkage" v-if="formObject.costModelType == 2">
          <mt-select
            :open-dispatch-change="false"
            v-model="formObject.isLinkage"
            :data-source="booleanList"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          :label="$t('成本模型分类')"
          prop="classifyCode"
          v-if="formObject.costModelType == 1"
        >
          <mt-select
            v-model="formObject.classifyCode"
            :data-source="costModelClassifyList"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :placeholder="$t('请选择')"
            @change="
              (e) => {
                formObject.classifyName = e.itemData ? e.itemData.itemName : null
              }
            "
          />
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      booleanList: [
        { text: this.$t('否'), value: 0 },
        { text: this.$t('是'), value: 1 }
      ],
      typeList: [
        { text: this.$t('成本模板'), value: 1 },
        { text: this.$t('核价模板'), value: 2 }
      ],
      costModelClassifyList: [],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formObject: {
        id: null,
        costModelName: '', //模板名称
        companyRelSaveRequestList: [], //模板关联公司
        relSaveRequestList: [], //	模板关联项
        remark: '', //备注
        isHavc: '',
        costModelType: null,
        isLinkage: null,
        classifyCode: null
      },
      companyRelSaveRequestList: [], //模板关联公司
      relSaveRequestList: [], //模板关联项
      formRules: {},
      editStatus: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    getFormAuthText() {
      let _list = this?.formObject?.companyRelSaveRequestList ?? []
      if (_list.length) {
        let _name = []
        _list.forEach((e) => {
          _name.push(e.companyName)
        })
        return _name.join(',')
      } else {
        return this.$t('请选择适用公司')
      }
    },
    getFormAssocText() {
      let _list = this?.formObject?.relSaveRequestList ?? []
      if (_list.length) {
        let _name = []
        _list.forEach((e) => {
          _name.push(e.relationName)
        })
        return _name.join(',')
      } else {
        return this.$t('请选择适用品类')
      }
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()

    this.getCostModelClassifyList()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.formObject = { ...this.modalData.data }
    }
    if (this.formObject.id) {
      // this.$API.costModel.getAuthCompanyList
      this.queryRel()
      this.getAuthList()
    }
    this.$API.costModel.saveModelValid().then((res) => {
      if (res.code == 200) {
        this.formRules = this.$utils.formatRules(res.data)
        this.formRules.costModelName.push({
          max: 110,
          message: this.$t(`成本模板名称长度不能超过110个字符`),
          trigger: 'blur'
        })
        this.formRules.remark.push({
          max: 512,
          message: this.$t(`备注长度不能超过512个字符`),
          trigger: 'blur'
        })
        this.formRules.isLinkage.push({
          required: true,
          message: this.$t(`请选择`),
          trigger: 'blur'
        })
        this.formRules.classifyCode.push({
          required: true,
          message: this.$t(`请选择成本模型分类`),
          trigger: 'blur'
        })
      }
    })
  },
  methods: {
    costModelTypeChange(e) {
      if (e.value != 1) {
        this.formObject.isLinkage = null
      }
    },
    //公司授权弹框
    handleAuthDialog() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "components/market/companyAuth" */ 'COMPONENTS/market/companyAuth/index.vue'
          ),
        data: {
          localList: this.companyRelSaveRequestList,
          data: this.formObject,
          queryId: 'costModelId',
          getAuthList: this.$API.costModel.getAuthCompanyList
        },
        success: (res) => {
          let { companyRelList } = res
          this.companyRelSaveRequestList = companyRelList
          // this.formObject.companyRelSaveRequestList = companyRelList;
          this.$set(this.formObject, 'companyRelSaveRequestList', companyRelList)
        }
      })
    },
    //关联项弹框
    handleAssocDialog() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/settings/cost/list/components/assocDialog" */ './assocDialog/index.vue'
          ),
        data: {
          queryId: 'costModelId',
          data: this.formObject,
          title: this.$t('成本模板关联')
        },
        success: (res) => {
          let { relSaveRequestList } = res
          this.relSaveRequestList = relSaveRequestList
          // this.formObject.relSaveRequestList = relSaveRequestList;
          this.$set(this.formObject, 'relSaveRequestList', relSaveRequestList)
        }
      })
    },
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = { ...this.formObject }
          if (!this.editStatus) {
            delete params.id
          }
          this.$API.costModel.saveModel([params]).then((res) => {
            if (res.code == 200) {
              this.$emit('confirm-function')
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    // 获取已选关联项
    queryRel() {
      const DEFAULTPARAM = {
        condition: '',
        page: {
          current: 1,
          size: 1000
        },
        pageFlag: false,
        defaultRules: [
          {
            label: this.$t('因子ID'),
            field: 'costModelId',
            type: 'number',
            operator: 'equal',
            value: this.formObject.id
          }
        ]
      }
      this.$API.costModel.queryRel(DEFAULTPARAM).then((r) => {
        this.$set(this.formObject, 'relSaveRequestList', r.data.records)
      })
    },
    // 获取已选公司

    //获取当前授权列表
    getAuthList() {
      const DEFAULTPARAM = {
        condition: '',
        page: {
          current: 1,
          size: 1000
        },
        pageFlag: false,
        defaultRules: [
          {
            label: this.$t('因子ID'),
            field: 'costModelId',
            type: 'number',
            operator: 'equal',
            value: this.formObject.id
          }
        ]
      }
      this.$API.costModel.getAuthCompanyList(DEFAULTPARAM).then((r) => {
        this.$set(this.formObject, 'companyRelSaveRequestList', r.data.records)
      })
    },
    // 获取成本模型分类列表
    async getCostModelClassifyList() {
      const res = await this.$API.masterData.dictionaryGetList({
        dictCode: 'COST_MODEL_CLASSIFY_CODE'
      })
      if (res.code === 200) {
        this.costModelClassifyList = res.data || []
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/ .div-auth {
    background: #fafafa;
    // height: 35px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    color: #0f0f0f;
    font-size: 12px;
    cursor: pointer;
    border-radius: 5px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
