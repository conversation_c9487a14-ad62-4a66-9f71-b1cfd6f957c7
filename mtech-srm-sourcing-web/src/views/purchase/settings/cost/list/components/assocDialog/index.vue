<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" z-index="998">
    <div class="dialog-content">
      <ul class="type-list">
        <li
          v-for="(item, index) in costTypeList"
          :key="index"
          @click="activeType = index"
          :class="[{ active: activeType == index }]"
        >
          {{ item.type }}
        </li>
      </ul>

      <div class="grid-content">
        <mt-template-page
          ref="categoryRef"
          class="son-grid"
          :style="activeType == 0 ? 'zIndex:1000' : 'zIndex:999'"
          :template-config="categoryConfig"
        />
        <mt-template-page
          ref="itemRef"
          class="son-grid"
          :style="activeType == 1 ? 'zIndex:1000' : 'zIndex:999'"
          :template-config="itemConfig"
        />
        <!-- <mt-template-page
          v-show="activeType == 2"
          ref="factorRef"
          :template-config="factorConfig"
        /> -->
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { categoryConfig, itemConfig, factorConfig } from './config'
export default {
  data() {
    return {
      categoryConfig: categoryConfig(this.$API.masterData.getCategoryPagedUrl),
      itemConfig: itemConfig(this.$API.masterData.getItemListUrlPage),
      factorConfig: factorConfig(this.$API.marketFactor.getMarketFactorList),
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      factorInfo: {},
      activeType: 0,
      costTypeList: [
        { type: this.$t('品类'), ref: 'categoryRef' },
        { type: this.$t('物料'), ref: 'itemRef' }
        // { type: this.$t("行情因子"), ref: "factorRef" },
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.factorInfo = { ...this.modalData.data }
      this.queryRel()
    }
  },
  methods: {
    confirm() {
      // let _typeObj = this.costTypeList[this.activeType];
      // let _currentTabRef = this.$refs[_typeObj["ref"]].getCurrentTabRef();
      let _currentTabRef1 =
        this.$refs.categoryRef.getCurrentTabRef()?.gridRef?.getMtechGridRecords() ?? []
      let _currentTabRef2 =
        this.$refs.itemRef.getCurrentTabRef()?.gridRef?.getMtechGridRecords() ?? []
      // let _currentTabRef3 =
      //   this.$refs.factorRef
      //     .getCurrentTabRef()
      //     ?.gridRef?.getMtechGridRecords() ?? [];
      let _selectRecords = _currentTabRef1
        .map((e) => {
          return { ...e, relationType: 0 }
        })
        .concat(
          _currentTabRef2.map((e) => {
            return { ...e, relationType: 1 }
          })
        )
      // .concat(
      //   _currentTabRef3.map((e) => {
      //     return { ...e, relationType: 2 };
      //   })
      // );
      console.log('_selectRecords', _selectRecords)
      if (_selectRecords.length) {
        let relSaveRequestList = []
        _selectRecords.forEach((e) => {
          if (e.relationType === 0) {
            //品类
            relSaveRequestList.push({
              relationCode: e.categoryCode,
              relationId: e.id,
              relationName: e.categoryName,
              relationType: e.relationType
            })
          } else if (e.relationType === 1) {
            //物料
            relSaveRequestList.push({
              relationCode: e.itemCode,
              relationId: e.id,
              relationName: e.itemName,
              relationType: e.relationType
            })
          }
          // else if (e.relationType === 2) {
          //   //因子
          //   relSaveRequestList.push({
          //     relationCode: e.marketFactorCode,
          //     relationId: e.id,
          //     relationName: e.marketFactorName,
          //     relationType: e.relationType,
          //   });
          // }
        })
        this.$emit('confirm-function', { relSaveRequestList })
      } else {
        this.$toast({ content: this.$t('您未选择数据'), type: 'warning' })
        return
      }
    },
    queryRel() {
      if (this.factorInfo.id) {
        const DEFAULTPARAM = {
          condition: '',
          page: {
            current: 1,
            size: 1000
          },
          pageFlag: false,
          defaultRules: [
            {
              label: this.$t('因子ID'),
              field: this.modalData.queryId,
              type: 'number',
              operator: 'equal',
              value: this.factorInfo.id
            }
          ]
        }
        this.$API.costModel.queryRel(DEFAULTPARAM).then((r) => {
          let categoryRefArr = []
          let itemRefRefArr = []
          // let factorRefArr = [];
          r.data.records.forEach((e) => {
            if (e.relationType == 0) {
              categoryRefArr.push({
                categoryCode: e.relationCode,
                id: e.relationId,
                categoryName: e.relationName,
                relationType: 0
              })
            } else if (e.relationType == 1) {
              itemRefRefArr.push({
                itemCode: e.relationCode,
                id: e.relationId,
                itemName: e.relationName,
                relationType: 1
              })
            }
            // else {
            //   factorRefArr.push({
            //     marketFactorCode: e.relationCode,
            //     id: e.relationId,
            //     marketFactorName: e.relationName,
            //     relationType: 2,
            //   });
            // }
          })
          this.$refs.categoryRef.getCurrentUsefulRef().gridRef.selectIdRecords = categoryRefArr
          this.$refs.categoryRef.getCurrentUsefulRef().gridRef.dataBound()

          this.$refs.itemRef.getCurrentUsefulRef().gridRef.selectIdRecords = itemRefRefArr
          this.$refs.itemRef.getCurrentUsefulRef().gridRef.dataBound()

          // this.$refs.factorRefRef.getCurrentUsefulRef().gridRef.selectIdRecords =
          //   factorRefArr;
          // this.$refs.factorRefRef.getCurrentUsefulRef().gridRef.dataBound();
        })
      }
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
.type-list {
  width: 150px;
  margin-right: 15px;
  flex-shrink: 0;
  text-align: center;
  li {
    line-height: 50px;
    border: 1px solid #e8e8e8;
    border-top: none;
    &:nth-of-type(1) {
      border-top: 1px solid #e8e8e8;
    }
    &:hover {
      background-color: #eeeeee;
    }
    &.active {
      background-color: #dadada;
    }
  }
}
.grid-content {
  position: relative;
  flex: 1;
  .son-grid {
    position: absolute;
  }
}
</style>
