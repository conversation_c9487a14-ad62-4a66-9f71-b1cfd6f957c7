import { i18n } from '@/main.js'
//顶部操作表单按钮
// const toolbar = [["add", "delete"]];
export const categoryColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]
export const factorColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'itemCode',
    headerText: i18n.t('成本因子编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('成本因子名称')
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称')
  }
]
export const itemColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]
export const categoryConfig = (url) => [
  {
    useToolTemplate: false,
    useBaseConfig: false,
    grid: {
      selectId: 'categoryCode',
      allowFiltering: true,
      asyncConfig: {
        url,
        recordsPosition: 'data.records'
      },
      columnData: categoryColumnData
    }
  }
]
export const factorConfig = (url, params) => [
  {
    useToolTemplate: false,
    useBaseConfig: false,
    grid: {
      allowFiltering: true,
      asyncConfig: {
        url,
        params
      },
      columnData: factorColumnData
    }
  }
]
export const itemConfig = (url) => [
  {
    useToolTemplate: false,
    useBaseConfig: false,
    grid: {
      selectId: 'itemCode',
      allowFiltering: true,
      asyncConfig: {
        url,
        recordsPosition: 'data.records'
      },
      columnData: itemColumnData
    }
  }
]

export const costTypeList = [
  { type: i18n.t('品类'), ref: 'categoryRef' },
  { type: i18n.t('物料'), ref: 'itemRef' },
  { type: i18n.t('成本因子'), ref: 'factorRef' }
]

export const pageConfig = (that) => {
  const columnMap = {
    0: i18n.t('品类'),
    1: i18n.t('物料'),
    2: i18n.t('成本因子')
  }
  const columnData = [
    {
      width: '50',
      type: 'checkbox'
    },
    {
      field: 'relationCode',
      headerText: i18n.t(`${columnMap[that.activeType]}编码`)
    },
    {
      field: 'relationName',
      headerText: i18n.t(`${columnMap[that.activeType]}名称`)
    }
  ]
  return [
    {
      useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
      toolbar: {
        useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
        tools: [
          [
            {
              id: 'add',
              icon: 'icon_solid_Createorder',
              title: i18n.t('新增'),
              visibleCondition: () => that.editAble
            },
            {
              id: 'del',
              icon: 'icon_solid_delete',
              title: i18n.t('删除'),
              visibleCondition: () => that.editAble
            }
          ]
        ]
      },
      grid: {
        allowFiltering: true,
        columnData,
        asyncConfig: {
          url: that.$API.costModel.queryRelUrl,
          defaultRules: [
            {
              label: '因子ID',
              field: that.modalData?.queryId,
              type: 'number',
              operator: 'equal',
              value: that.modalData.data?.id
            }
          ],
          params: {
            pageFlag: false,
            condition: '',
            relationType: that.activeType
          }
        }
      }
    }
  ]
}
