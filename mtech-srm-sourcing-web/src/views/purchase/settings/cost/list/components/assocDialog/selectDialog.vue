<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-template-page ref="templateRef" :template-config="pageConfig" />
    </div>
  </mt-dialog>
</template>

<script>
import { categoryColumnData, itemColumnData, factorColumnData } from './config'

export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    pageConfig() {
      const obj = {
        category: {
          gridId: '56546821-24aa-480d-a031-3e9e3edca90e',
          url: this.$API.masterData.getCategoryPagedUrl,
          columnData: categoryColumnData
        },
        item: {
          gridId: 'eb2477a4-ae60-4dc1-9706-c81a21d5f935',
          url: this.$API.masterData.getItemListUrlPage,
          columnData: itemColumnData
        },
        factor: {
          gridId: 'fa5c61ee-d4de-4597-8970-d3d5876b4d51',
          url: this.$API.marketFactor.getCostFactorListUrl,
          params: {
            onlyCurrentLevel: 0,
            costModelId: this.modalData.costModelId
          },
          columnData: factorColumnData
        }
      }
      return [
        {
          gridId: obj[this.modalData.type].gridId,
          useToolTemplate: false,
          toolbar: [],
          grid: {
            columnData: obj[this.modalData.type].columnData,
            asyncConfig: {
              url: obj[this.modalData.type].url,
              params: obj[this.modalData.type].params || null
            }
          }
        }
      ]
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    async confirm() {
      const _selectRecords =
        this.$refs.templateRef.getCurrentTabRef()?.gridRef?.getMtechGridRecords() ?? []

      if (!_selectRecords.length) {
        this.$toast({ content: this.$t('您未选择数据'), type: 'warning' })
        return
      }
      const relList = []
      const prefix = {
        0: 'category', // 品类
        1: 'item', // 物料
        2: 'item' // 成本因子
      }
      const { costModelId, relationType } = this.modalData
      _selectRecords.forEach((item) => {
        relList.push({
          relationId: item.id,
          relationCode: item[prefix[relationType] + 'Code'],
          relationName: item[prefix[relationType] + 'Name']
        })
      })
      const params = {
        costModelId,
        relationType,
        relList
      }

      const res = await this.$API.costModel.saveRel(params)
      if (res.code === 200) {
        this.$toast({ content: this.$t('操作成功！'), type: 'success' })
        this.$emit('confirm-function')
      }
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 0 0 0;
  display: flex;
  height: 100%;
  width: 100%;
}
</style>
