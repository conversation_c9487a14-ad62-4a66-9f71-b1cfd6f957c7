<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>
<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      pageConfig: [],
      costModelClassifyList: [],
      companyList: []
    }
  },
  mounted() {
    this.initSelectList()
  },
  methods: {
    async initSelectList() {
      await Promise.all([this.getCostModelClassifyList(), this.getCompanyList()])
      this.pageConfig = pageConfig(this)
    },
    // 获取成本模型分类列表
    async getCostModelClassifyList() {
      const res = await this.$API.masterData.dictionaryGetList({
        dictCode: 'COST_MODEL_CLASSIFY_CODE'
      })
      if (res.code === 200) {
        this.costModelClassifyList = res.data || []
      }
    },
    // 获取公司下拉列表
    async getCompanyList() {
      const res = await this.$API.masterData.permissionCompanyList()
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.orgCode + '-' + item.orgName
        })
        this.companyList = res.data
      }
    },
    //表格头部按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      const validArr = ['Save', 'Delete', 'Stop', 'Submit', 'Enable', 'copy']
      if (_selectGridRecords.length <= 0 && validArr.includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      if (e.toolbar.id == 'Add') {
        //新增
        this.handleAddCost()
      } else if (e.toolbar.id == 'Save') {
        //保存  console
        this.handleBatchSave(_selectGridRecords)
      } else if (e.toolbar.id == 'Stop') {
        //停用  ok
        this.handleBatchStop(_selectGridRecords)
      } else if (e.toolbar.id == 'Submit') {
        //提交  ok
        this.handleBatchSubmit(_selectGridRecords)
      } else if (e.toolbar.id == 'Delete') {
        //删除  ok
        this.handleBatchDelete(_selectGridRecords)
      } else if (e.toolbar.id === 'copy') {
        this.handleCopy(_selectGridRecords)
      }
    },
    //单元格按钮
    handleClickCellTool(e) {
      if (e.tool.id == 'edit') {
        this.handleEditCost(e.data)
      } else if (e.tool.id == 'delete') {
        //删除  ok
        this.handleBatchDelete([e.data])
      } else if (e.tool.id == 'Stop') {
        //停用  ok
        this.handleBatchStop([e.data])
      }
    },
    //批量停用
    handleBatchStop(_selectGridRecords) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      //单据状态 -1:已停用 0:草稿 1:审批中 2:审批驳回 3:已生效
      let _disableStatusRecords = _selectGridRecords.filter((s) => {
        return s.status !== 3
      })
      if (_disableStatusRecords.length > 0) {
        this.$toast({
          content: this.$t('非已生效状态，不能停用，请检查！'),
          type: 'warning'
        })
        return
      } else {
        this.handleStopModel(_selectIds)
      }
    },
    //执行停用操作
    handleStopModel(idList) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`停用后版本号将更新，是否确认停用？`)
        },
        success: () => {
          this.$API.costModel
            .disableById({
              idList
            })
            .then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('操作成功'),
                  type: 'success'
                })
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
        }
      })
    },
    //执行删除操作
    handleDeleteModel(idList) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认执行删除操作？')
        },
        success: () => {
          this.$API.costModel.deleteById({ idList }).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        }
      })
    },
    //保存
    // handleBatchSave(_selectGridRecords) {
    // },
    // 提交
    handleBatchSubmit(_selectGridRecords) {
      let item = _selectGridRecords.find((s) => ![-1, 0, 2].includes(s.status))
      if (item) {
        this.$toast({
          content: this.$t("仅'已停用、草稿、审批驳回'状态的数据可以执行提交操作"),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认执行提交操作？`)
        },
        success: () => {
          let idList = []
          _selectGridRecords.map((item) => {
            idList.push(item.id)
          })
          this.$API.costModel
            .commitById({
              idList
            })
            .then((res) => {
              if (res.code == 200) {
                this.$refs.templateRef.refreshCurrentGridData()
                this.$toast({
                  content: res.msg,
                  type: 'success'
                })
              }
            })
        }
      })
    },
    //编辑成本模型
    handleEditCost(e) {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/settings/cost/list/components/costDialog" */ './components/costDialog.vue'
          ),
        data: {
          data: { ...e },
          title: this.$t('编辑成本模型')
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //新增成本模型
    handleAddCost() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/settings/cost/list/components/costDialog" */ './components/costDialog.vue'
          ),
        data: {
          title: this.$t('新增成本模型')
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //单元格标题
    handleClickCellTitle(e) {
      if (e.field == 'costModelCode') {
        localStorage.marketCostModelInfo = JSON.stringify(e.data)
        const { id, costModelType, classifyCode } = e.data
        this.$router.push({
          name: `purchase-settings-cost-detail`,
          query: {
            configId: id,
            type: costModelType,
            classifyCode: classifyCode,
            refreshId: Date.now()
          }
        })
      } else if (e.field == 'relationName') {
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "router/purchase/settings/cost/list/components/assocDialog" */ './components/assocDialog/index.vue'
            ),
          data: {
            title: this.$t('成本模板关联'),
            field: 'relatedItems'
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      } else if (e.field == 'relationNameField') {
        // 关联项的弹框
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "router/purchase/settings/cost/list/components/assocDialog/viewRelationField" */ './components/assocDialog/viewRelationField.vue'
            ),
          data: {
            title: this.$t('成本模板关联'),
            queryId: 'costModelId',
            data: e.data
          },
          success: (res) => {
            let { relSaveRequestList } = res
            this.relSaveRequestList = relSaveRequestList
            this.formObject.relSaveRequestList = relSaveRequestList
          }
        })
      } else if (e.field == 'companyAuth') {
        const editable = [-1, 0, 2, 4].includes(e.data.status)
        const comp = editable
          ? import('COMPONENTS/market/companyAuth/index.vue')
          : import('./components/companyAuth/index.vue')
        // 公司授权的弹框
        this.$dialog({
          modal: () => comp,
          data: {
            data: e.data,
            queryId: 'costModelId',
            getAuthList: this.$API.costModel.getAuthCompanyList
          },
          success: async (data) => {
            if (editable) {
              const params = {
                ...e.data,
                companyRelSaveRequestList: data.companyRelList
              }
              const res = await this.$API.costModel.saveModel([params])
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功！'), type: 'success' })
              }
            }
          }
        })
      } else if (e.field == 'oaApproveLink') {
        // OA申请单查看
        window.open(e.data.oaApproveLink)
      } else if (e.field == 'operatingRecords') {
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "components/market/operatingRecords" */ 'COMPONENTS/market/operatingRecords/index.vue'
            ),
          data: {
            title: this.$t('操作记录'),
            data: e.data
          },
          success: () => {}
        })
      }
    },
    handleBatchDelete(_selectGridRecords) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      let _statusRecords = _selectGridRecords.filter((s) => {
        return [1, 3, 5].includes(s.status)
      })
      if (_statusRecords.length > 0) {
        this.$toast({
          content: this.$t("仅'已停用、草稿、审批驳回、审批废弃'状态的数据可以执行删除操作"),
          type: 'warning'
        })
        return
      } else {
        this.handleDeleteModel(_selectIds)
      }
    },
    // 复制
    async handleCopy(_selectGridRecords) {
      const ids = []
      _selectGridRecords.forEach((item) => {
        ids.push(item.id)
      })
      const res = await this.$API.costModel.batchCopy({ ids })
      if (res?.code === 200) {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        this.$refs.templateRef.refreshCurrentGridData()
      }
    }
  }
}
</script>
