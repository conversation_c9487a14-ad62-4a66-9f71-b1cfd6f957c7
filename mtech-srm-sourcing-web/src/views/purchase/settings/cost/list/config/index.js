import { i18n, permission } from '@/main.js'
import utils from '@/utils/utils'
import { searchOptionsList } from '@/constants'
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') },
  { id: 'copy', icon: 'icon_solid_Createorder', title: i18n.t('复制') },
  { id: 'Stop', icon: 'icon_solid_Cancel', title: i18n.t('停用') },
  { id: 'Submit', icon: 'icon_solid_Submit', title: i18n.t('提交') }
]
const columnData = (that) => {
  return [
    {
      type: 'checkbox',
      width: '60'
    },
    {
      field: 'costModelCode',
      headerText: i18n.t('成本模板编码'),
      cssClass: 'field-content',
      cellTools: [
        {
          id: 'edit',
          icon: 'icon_Editor',
          title: i18n.t('编辑'),
          visibleCondition: (data) => {
            // 停用、草稿、审批驳回、审批废弃且登录账号为创建人时可编辑
            // return [-1, 0, 2].includes(data.status) && userId === data.createUserId
            return [-1, 0, 2, 4].includes(data.status)
          }
        },
        {
          id: 'delete',
          icon: 'icon_solid_Delete',
          title: i18n.t('删除'),
          visibleCondition: (data) => {
            // 草稿、审批驳回、审批废弃状态可删除
            return [0, 2, 4].includes(data.status)
          }
        }
      ],
      searchOptions: {
        renameField: 'header.costModelCode'
      }
    },
    {
      field: 'companyCode',
      headerText: i18n.t('公司'),
      width: 0,
      searchOptions: {
        elementType: 'select',
        dataSource: that.companyList,
        fields: { text: 'text', value: 'orgCode' }
      }
    },
    {
      field: 'categoryCode',
      headerText: i18n.t('品类编码'),
      width: 0,
      searchOptions: {
        operator: 'likeright'
      }
    },
    {
      field: 'materialCode',
      headerText: i18n.t('物料编码'),
      width: 0,
      searchOptions: {
        operator: 'likeright'
      }
    },
    {
      field: 'costFactorCode',
      headerText: i18n.t('成本因子编码'),
      width: 0,
      searchOptions: {
        operator: 'likeright'
      }
    },
    {
      field: 'costModelName',
      headerText: i18n.t('成本模板名称'),
      searchOptions: {
        renameField: 'header.costModelName'
      }
    },
    {
      field: 'status',
      headerText: i18n.t('状态'), //单据状态 -1:已停用 0:草稿 1:审批中 2:审批驳回 3:已生效
      valueConverter: {
        type: 'map',
        map: [
          { status: -1, label: i18n.t('已停用'), cssClass: 'title-#9a9a9a' },
          { status: 0, label: i18n.t('草稿'), cssClass: 'title-#9baac1' },
          { status: 1, label: i18n.t('审批中'), cssClass: 'title-#eda133' },
          { status: 2, label: i18n.t('审批驳回'), cssClass: 'title-#ed5633' },
          { status: 3, label: i18n.t('已生效'), cssClass: 'title-#6386c1' },
          { status: 4, label: i18n.t('审批废弃'), cssClass: 'title-#ed5633' },
          { status: 5, label: i18n.t('历史'), cssClass: 'title-#9a9a9a' }
        ],
        fields: { text: 'label', value: 'status' }
      },
      cellTools: [
        {
          id: 'Stop',
          // icon: "icon_solid_Cancel",
          title: i18n.t('停用'),
          visibleCondition: (data) => {
            return data['status'] == 3
          }
        }
      ],
      searchOptions: {
        renameField: 'header.status'
      }
    },
    {
      field: 'versionCode',
      headerText: i18n.t('版本号')
    },
    {
      field: 'companyAuth', //companyRelList
      headerText: i18n.t('适用公司'),
      cssClass: 'field-content',
      ignore: true,
      valueConverter: {
        type: 'placeholder',
        placeholder: i18n.t('适用公司')
      }
    },
    {
      field: 'relationNameField',
      headerText: i18n.t('适用范围'),
      cssClass: 'field-content',
      ignore: true,
      valueConverter: {
        type: 'placeholder',
        placeholder: i18n.t('适用范围')
      }
    },
    {
      field: 'oaApproveLink',
      headerText: i18n.t('OA申请单查看'),
      cssClass: 'field-content',
      ignore: true,
      valueConverter: {
        type: 'function',
        filter: (e) => {
          return e ? i18n.t('查看') : ''
        }
      }
    },
    {
      field: 'costModelType',
      headerText: i18n.t('类型'),
      valueConverter: {
        type: 'map',
        map: {
          1: i18n.t('成本模板'),
          2: i18n.t('核价模板')
        }
      }
    },
    {
      field: 'classifyName',
      headerText: i18n.t('成本模型分类'),
      searchOptions: {
        renameField: 'classifyCode',
        elementType: 'select',
        dataSource: that.costModelClassifyList,
        fields: { text: 'itemName', value: 'itemCode' }
      }
    },
    {
      field: 'remark',
      headerText: i18n.t('备注'),
      searchOptions: {
        renameField: 'header.remark'
      }
    },
    {
      field: 'createUserName',
      headerText: i18n.t('创建人'),
      searchOptions: {
        renameField: 'header.createUserName'
      }
    },
    {
      field: 'createTime',
      headerText: i18n.t('创建日期'),
      valueConverter: {
        type: 'function',
        filter: (e) => {
          if (e) {
            if (e == 0) {
              return (e = '')
            } else if (typeof e == 'object') {
              return utils.formatTime(e)
            } else if (typeof e == 'string') {
              if (e.indexOf('T') != -1) {
                return e.substr(0, 10)
              } else {
                let val = parseInt(e)
                return utils.formatTime(new Date(val))
              }
            } else if (typeof e == 'number') {
              return utils.formatTime(new Date(e))
            } else {
              return e
            }
          } else {
            return e
          }
        }
      },
      searchOptions: {
        renameField: 'header.createTime',
        ...searchOptionsList.timeRange
      }
    }
  ]
}
export const pageConfig = (that) => [
  {
    toolbar,
    gridId: permission.gridId['purchase']['coastModel']['list'],
    grid: {
      allowFiltering: true,
      columnData: columnData(that),
      asyncConfig: {
        url: that.$API.costModel.getModelList
      }
    }
  }
]
