<template>
  <div class="tempalte-detail mt-flex-direction-column">
    <div class="page-bottom mt-flex">
      <div class="page-content fbox">
        <div class="detail-top">
          <div class="left">
            <div class="title">
              {{ formObject.costModelCode }}

              <span class="titleC">{{ formObject.costModelName }}</span>
              <span class="statusC">{{ status }}</span>
            </div>
            <div class="form">
              <div class="form_div">
                {{ $t('类型') }}：{{ costModelType[formObject.costModelType] }}
              </div>
              <div class="form_div" v-if="$route.query.type == 1">
                {{ $t('成本模板分类') }}：{{ formObject.classifyName }}
              </div>

              <div class="form_div">{{ $t('版本号') }}：{{ formObject.versionCode }}</div>
              <div class="form_div">{{ $t('创建人') }}：{{ formObject.createUserName }}</div>
              <div class="form_formulaTotal" v-if="$route.query.type == 2">
                {{ $t('公式') }}：{{ formObject.formulaTotal }}
              </div>
            </div>
          </div>
          <div class="right">
            <template v-if="$route.query.type == 1">
              <vxe-button
                v-show="formObject.status === 3"
                class="right_but"
                size="small"
                @click="handleStop"
              >
                {{ $t('停用') }}
              </vxe-button>
              <vxe-button
                v-show="[-1, 0, 2].includes(formObject.status)"
                class="right_but"
                size="small"
                @click="handleSubmit"
              >
                {{ $t('提交') }}
              </vxe-button>
              <vxe-button class="right_but" size="small" @click="handleCopy">
                {{ $t('复制') }}
              </vxe-button>
              <vxe-button
                v-show="[-1, 0, 2, 4].includes(formObject.status)"
                class="right_but"
                size="small"
                @click="handleDelete"
              >
                {{ $t('删除') }}
              </vxe-button>
            </template>
            <vxe-button class="right_but" size="small" status="primary" @click="handleViewOA">
              {{ $t('OA审批进度') }}
            </vxe-button>
            <vxe-button class="right_but" size="small" status="primary" @click="handleBack">
              {{ $t('返回') }}
            </vxe-button>
          </div>
        </div>
        <!-- 成本模板 -->
        <CostModelDetail
          ref="costModelDetailRef"
          v-if="$route.query.type == 1"
          :form-object="formObject"
        />
        <!-- 核价模板 -->
        <div v-else>
          <div :class="[bottomShow ? 'detail-content' : 'detail-content-no']">
            <mt-template-page
              v-if="show"
              ref="templateRefTree"
              :template-config="pageConfig"
              :tree-column-index="1"
              @rowSelected="rowSelected"
              @rowDeselected="rowDeselected"
              @handleClickToolBar="handleClickToolBar"
              @handleClickCellTitle="handleClickCellTitle"
            />
          </div>
          <transition name="detail-bottom">
            <div class="detail-bottom" v-if="bottomShow && bottomShow2">
              <mt-template-page
                ref="templateRef"
                :template-config="pageConfig2"
                @handleClickToolBar="handleClickToolBar1"
                @handleClickCellTool="handleClickCellTool1"
                @handleSelectTab="handleSelectTab1"
              >
                <Slot0
                  slot="slot-0"
                  :form-data="activeData"
                  @nodeChange="nodeChange"
                  @startEdit="startEdit"
                  @formChange="formChange"
                >
                </Slot0>
                <!-- <Slot3
              slot="slot-3"
              :form-data="activeData"
              @nodeChange="nodeChange"
              @formChange="formChange"
            >
            </Slot3> -->
              </mt-template-page>
            </div>
          </transition>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { pageConfig, pageConfig2 } from './config'
import Slot0 from './components/slot0.vue'
// import Slot3 from './components/slot3.vue'
import CostModelDetail from './costModelDetail.vue'
import HandleFormulaDialog from './dialog/handleFormulaDialog.vue'
import HandleBaseFieldDialog from './dialog/handleBaseFieldDialog.vue'
import { EventBus } from '@/utils/event-bus.js'
import { createEditInstance } from '@/utils/ej/dataGrid/index'
import checkSelectedItemCode from 'COMPONENTS/NormalEdit/checkSelectItemCodeAll'

import Vue from 'vue'
export default {
  inject: ['reload'],
  components: {
    Slot0,
    // Slot3,
    CostModelDetail
  },
  computed: {
    bottomShow() {
      return JSON.stringify(this.activeData) == '{}' ? false : true
    },
    costModelId() {
      return this.$route.query.configId ? this.$route.query.configId : '176786814758723680'
    },
    status() {
      switch (this.formObject.status) {
        case -1:
          return this.$t('已停用')
        case 0:
          return this.$t('草稿')
        case 1:
          return this.$t('审批中')
        case 2:
          return this.$t('审批驳回')
        case 3:
          return this.$t('已生效')
        default:
          return this.$t('暂无状态')
      }
    }
  },
  watch: {
    activeData: {
      handler(v) {
        console.log('delactiveData', v)
        if (v.id) {
          EventBus.$emit(`treeGridRowSelect-${v.id}`, v)
        }
      },
      deep: true
    }
  },
  data() {
    return {
      detailTemplate: function () {
        return {
          template: Vue.component('detailTemplate', {
            // define the empty div
            template: `
       <div class="detailtable" width="100%">
    </div>`,
            data: function () {
              return {
                data: {}
              }
            },
            methods: {},
            computed: {}
          })
        }
      },
      dataSession: [],
      detailList: [],
      nodeName: '',
      pageConfig2: pageConfig2('0', this),
      bottomShow2: true,
      editStatus: false,
      treeGridRef: {},
      show: false,
      pageConfig: pageConfig(false, this.$API.costModel.getCostModelItem),
      activeData: {},
      formData: {},
      formObject: {},
      dataSource: [
        {
          nodeName: this.$t('初始化') + '1',
          id: '1',
          node: '0',
          children: [
            {
              nodeName: this.$t('合同指标') + '1',
              id: '2',
              node: '1',
              children: []
            },
            {
              nodeName: this.$t('合同指标') + '2',
              id: '3',
              node: '1'
            }
          ]
        },
        {
          nodeName: this.$t('初始化') + '2',
          id: '4',
          node: '0',
          indexList: [
            {
              nodeName: this.$t('合同指标') + '3',
              id: '5',
              node: '1'
            }
          ]
        }
      ],
      tab2Column: [],
      tab2ItemDataList: [],
      dimensionList: [], //维度列表
      unitNameList: [],
      costModelType: {
        1: this.$t('成本模板'),
        2: this.$t('核价模板')
      }
    }
  },
  created() {
    this.getUnitName()
  },
  mounted() {
    this.queryCostModelItem()
    this.queryById()
    this.show = true
  },
  methods: {
    // 返回
    handleBack() {
      if (this.$route.query.type == 1 && this.$refs.costModelDetailRef?.isContentChange) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('有未保存的记录，是否确认返回？')
          },
          success: () => {
            this.$router.go(-1)
          }
        })
      } else {
        this.$router.go(-1)
      }
    },
    getUnitName() {
      this.$API.masterData.pagedQueryUnit().then((res) => {
        console.log('getUnitName', res)
        this.unitNameList = res?.data?.records || []
      })
    },
    queryById() {
      this.$API.costModel.queryById({ costModelId: this.costModelId }).then((r) => {
        this.formObject = { ...r.data }
      })
    },
    nodeChange(e) {
      console.log('nodeChange', e)
      this.pageConfig2 = pageConfig2(e.value, this)
    },
    formChange(e) {
      console.log('formChange', e, this.$refs.templateRefTree.getCurrentTabRef().treeGrid)
      const arr = [
        'id',
        'costModelId',
        'parentId',
        'nodeCode',
        'nodeKey',
        'sortValue',
        'parentName'
      ]
      for (const key in e) {
        if (arr.includes(key)) {
          continue
        }
        console.log('key', key)
        this.$refs.templateRefTree
          .getCurrentTabRef()
          .treeGrid.setCellValue(this.activeData.id, key, e[key])
      }
    },
    handleSelectTab1(e) {
      console.log('handleSelectTab1', e)
      const editInstance = createEditInstance().component(
        'checkSelectedItemCode',
        checkSelectedItemCode
      )
      if (e == 1) {
        this.queryColumn()
      } else if (e == 2) {
        console.log('this.activeData e=2', this.activeData)
        this.tab2Column = []
        // this.tab2ItemDataList = [...this.activeData.itemDataList];

        this.$API.costModel
          .queryColumn({
            costModelId: this.costModelId,
            costModelItemId: this.activeData.id
          })
          .then((r) => {
            let a = r.data.map((item) => {
              this.tab2Column.push({
                columnId: item.id,
                columnAlias: item.columnAlias,
                valueSet: item.valueSet
              })
              return {
                field: item.id,
                headerText: item.columnAlias || item.columnName,
                edit: editInstance.create({
                  getEditConfig: (ctx) => {
                    console.log('rowData', ctx)
                    if (item.valueSet == 'item') {
                      return {
                        type: 'checkSelectedItemCode',
                        field: 'itemCode'
                      }
                    } else if (item.valueSet == 'unit') {
                      return {
                        type: 'select',
                        fields: { text: 'unitCode', value: 'unitCode' },
                        'show-clear-button': true,
                        dataSource: this.unitNameList
                      }
                    } else if (item.valueSet == 'cost_factor') {
                      return {
                        type: 'checkSelectedItemCode',
                        sourcingObjType: 'cost_factor',
                        field: 'itemCode'
                      }
                    } else if (item.valueSet == 'quota_no') {
                      return {
                        type: 'checkSelectedItemCode',
                        sourcingObjType: 'quota_no',
                        field: 'itemCode'
                      }
                    } else if (item.valueSet == 'base_price') {
                      return {
                        type: 'checkSelectedItemCode',
                        sourcingObjType: 'base_price',
                        field: 'itemCode'
                      }
                    } else if (item.valueSet == 'conditions_base_price') {
                      return {
                        type: 'checkSelectedItemCode',
                        sourcingObjType: 'conditions_base_price',
                        field: 'itemCode'
                      }
                    } else if (item.valueSet == 'item_quota_no') {
                      return {
                        type: 'checkSelectedItemCode',
                        sourcingObjType: 'item_quota_no',
                        field: 'itemCode'
                      }
                    } else if (item.valueSet == 'cost_factor_quota_no') {
                      return {
                        type: 'checkSelectedItemCode',
                        sourcingObjType: 'cost_factor_quota_no',
                        field: 'itemCode'
                      }
                    } else if (item.columnType == 0) {
                      return {
                        type: 'number',
                        max: 10000000000000000
                      }
                    } else {
                      return {
                        type: 'text',
                        maxLength: 512
                      }
                    }
                  }
                })
              }
            })
            this.$set(this.activeData, 'columnData', a)
            a.unshift({
              type: 'checkbox',
              width: '50',
              allowEditing: false
            })
            this.$set(this.pageConfig2[2].grid, 'columnData', a)
            this.queryLeafNodeData()
          })
      }
    },
    //查询末级节点信息
    queryLeafNodeData() {
      this.$API.costModel
        .queryLeafNodeData({
          costModelId: this.costModelId,
          costModelItemId: this.activeData.id
        })
        .then((r) => {
          if (r.data == null) {
            this.$set(this.pageConfig2[2].grid, 'dataSource', [])
            this.$set(this.activeData, 'dataSource', [])
            return
          }
          this.tab2ItemDataList = [...r.data]
          let a = []
          let b = {}
          let _tempArr = [
            'cost_factor',
            'base_price',
            'quota_no',
            'conditions_base_price',
            'item_quota_no',
            'cost_factor_quota_no'
          ]
          r.data.forEach((item) => {
            b = {}
            item.forEach((item2) => {
              if (item2?.valueSet && _tempArr.includes(item2.valueSet) && item2?.dataCode) {
                b[item2.columnId] = `(${item2.dataCode})${item2.dataValue}`
              } else {
                b[item2.columnId] = item2.dataValue
              }
              // a[i][item2.columnAlias] = item2.dataValue;
              // b[item2.columnId] = item2.dataValue;
              b.rowKey = item2.rowKey
              b.id = item2.id
            })
            a.push(b)
          })
          console.log('queryLeafNodeData', b, 'queryLeafNodeData--a-', a)
          this.$set(this.pageConfig2[2].grid, 'dataSource', a)
          this.$set(this.activeData, 'dataSource', a)
        })
    },
    //查询末列信息
    queryColumn() {
      this.$API.costModel
        .queryColumn({
          costModelId: this.costModelId,
          costModelItemId: this.activeData.id
        })
        .then((r) => {
          const a = r.data.map((item) => {
            return {
              field: item.id,
              headerText: item.columnAlias || item.columnName
            }
          })
          // this.activeData. = a;
          this.$set(this.activeData, 'columnData', a)
          this.$set(this.pageConfig2[1].grid, 'dataSource', [])
          this.$nextTick(() => {
            this.$set(this.pageConfig2[1].grid, 'dataSource', r.data)
          })
        })
    },
    saveColumn(e) {
      let params = {
        columnSaveRequestList: [{ ...e }],
        costModelId: this.costModelId,
        costModelItemId: this.activeData.id
      }
      if (params.columnSaveRequestList[0].columnType != 0) {
        params.columnSaveRequestList[0].calculationFormula = ''
      }
      this.$API.costModel
        .saveColumn(params)
        .then(() => {
          this.queryColumn()
          this.queryById()
        })
        .catch(() => {
          // this.queryColumn()
        })
    },
    saveItemData(e) {
      let arr = []
      let b = [...this.tab2ItemDataList]
      let c = []
      console.log('this.tab2ItemDataList', this.tab2ItemDataList, e)
      for (let i = 0; i < b.length; i++) {
        b[i].forEach((item) => {
          if (item.rowKey == e.rowKey) {
            c.push(item)
          }
        })
      }
      let _tempArr = [
        'cost_factor',
        'base_price',
        'quota_no',
        'conditions_base_price',
        'item_quota_no',
        'cost_factor_quota_no'
      ]
      for (const key in e) {
        if (key == 'id' || key == 'rowKey') {
          continue
        }
        console.log('saveItemData', key, e, e[key], this.tab2Column)
        let _valueSet = this.tab2Column.find((item) => item.columnId == key)?.valueSet
        if (_valueSet && _tempArr.includes(_valueSet)) {
          arr.push({
            dataCode: e[key] ? e[key].replaceAll('(', '').replace(/\).*/, '') : null
          })
          console.log('_valueSet', _valueSet, arr)
        } else {
          arr.push({ dataValue: e[key] })
        }
      }
      console.log('arrcc', arr, c)
      if (c.length > 0) {
        arr.forEach((item, i) => {
          item.columnId = this.tab2Column[i].columnId
          item.columnAlias = this.tab2Column[i].columnAlias
          item.id = c[i]?.id
        })
      } else {
        arr.forEach((item, i) => {
          item.columnId = this.tab2Column[i].columnId
          item.columnAlias = this.tab2Column[i].columnAlias
        })
      }
      let params = {
        itemDataSaveRequestList: [arr],
        costModelId: this.costModelId,
        costModelItemId: this.activeData.id
      }
      this.$API.costModel.saveItemData(params).then(() => {
        this.queryLeafNodeData()
      })
    },
    deleteColumn(e) {
      const params = {
        deleteIdList: e,
        costModelId: this.costModelId,
        costModelItemId: this.activeData.id
      }
      this.$API.costModel.deleteColumn(params).then(() => {
        this.queryColumn()
        this.queryById()
      })
    },
    deleteRowData(e) {
      const params = {
        deleteIdList: e.map((item) => {
          return item.id
        }),
        rowKeyList: e.map((item) => {
          return item.rowKey
        }),
        costModelId: this.costModelId,
        costModelItemId: this.activeData.id
      }
      this.$API.costModel.deleteRowData(params).then(() => {
        this.queryLeafNodeData()
      })
    },
    startEdit(e) {
      this.editDimension(e)
    },
    rowSelected(e) {
      console.log('cost-rowSelected', e)
      this.activeData = e.data.taskData || e.data[0]
      this.pageConfig2 = pageConfig2(this.activeData.node, this)
      this.queryLeafNodeData()
      this.bottomShow2 = false
      this.$nextTick(() => {
        this.bottomShow2 = true
      })
    },
    rowDeselected(e) {
      console.log('cost-rowDeselected', e)
      this.activeData = {}
      this.bottomShow2 = false
      this.$nextTick(() => {
        this.bottomShow2 = true
      })
      this.pageConfig2 = pageConfig2(this.activeData.node, this)
      EventBus.$emit(`treeGridRowSelect-${e.data.id}`, {})
    },
    //表格顶部按钮点击
    handleClickToolBar(e) {
      let _selectRecords = e.treeGrid.getSelectedRecords()
      console.log('use-handleClickToolBar', e, _selectRecords)
      if (e.toolbar.id == 'Add') {
        if (this.activeData?.leafNode == 1) {
          this.$toast({ content: this.$t('已经是末级节点'), type: 'warning' })
          return
        }
        this.handleAddDimension(e, _selectRecords)
      } else if (e.toolbar.id == 'Delete') {
        this.deleteItem()
      }
    },
    //表格顶部按钮点击
    handleClickToolBar1(e) {
      // let _selectRecords = e.gridRef.getSelectedRecords();
      console.log('use-handleClickToolBar1', e.gridRef.getMtechGridRecords())
      if (e.toolbar.id == 'Add') {
        //新增维度操作
        e.grid.addRecord()
      } else if (e.toolbar.id == 'Delete') {
        // 删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('请确认是否删除所选项？')
          },
          success: () => {
            this.deleteColumn(
              e.gridRef.getMtechGridRecords().map((item) => {
                return item.id
              })
            )
          }
        })
      } else if (e.toolbar.id == 'Delete2') {
        // 删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('请确认是否删除所选项？')
          },
          success: () => {
            this.deleteRowData(e.gridRef.getMtechGridRecords())
          }
        })
      } else if (e.toolbar.id == 'Updata') {
        e.grid.endEdit()
      } else if (e.toolbar.id == 'Cancel') {
        e.grid.closeEdit()
        this.queryColumn()
      }
    },
    //单元格icons，点击
    handleClickCellTool1(e) {
      console.log('use-handleClickCellTool1', e)
      if (e.tool.id == 'formula') {
        this.handleFormulaDialog(e.data)
      } else if (e.tool.id == 'definition') {
        this.handleDefinitionDialog(e.data)
      }
    },
    queryCostModelItem(add = false) {
      const params = {
        costModelId: this.costModelId
      }
      if (add) {
        try {
          this.$store.commit('startLoading')
          this.getTable()
        } catch {
          this.$store.commit('endLoading')
        }
      }
      this.$API.costModel
        .queryCostModelItem(params)
        .then((r) => {
          // this.$set(this.pageConfig[0].treeGrid, 'dataSource', r.data)
          this.pageConfig[0].treeGrid.dataSource = r.data
          if ((r.data && r.data.length == 1) || r.data == null) {
            this.show = false
            this.$nextTick(() => {
              this.show = true
            })
          }
          if (r.data == null) {
            this.activeData = {}
          }

          if (add) {
            setTimeout(() => {
              this.setTable()
              this.$store.commit('endLoading')
            }, 1000)
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    // 写入树表样式
    setTable() {
      let a = document.getElementsByTagName('tr')
      for (let i = 0; i < a.length; i++) {
        if (a[i].getAttribute('role') == 'row') {
          // 是否有子集
          for (let j = 0; j < a[i].children[1]?.children[0]?.children?.length; j++) {
            let e = a[i].children[1].children[0].children[j]
            this.dataSession.forEach((el) => {
              if (el.cell == a[i].children[1].children[0].lastChild.textContent) {
                // if (el.style) {
                a[i].style.display = el.style
                // }
              }
            })
            if (
              e.getAttribute('class').indexOf('e-treegridexpand') != -1 ||
              e.getAttribute('class').indexOf('e-treegridcollapse') != -1
            ) {
              this.dataSession.forEach((el) => {
                if (el.cell == a[i].children[1].children[0].lastChild.textContent) {
                  e.setAttribute('class', el.icon)
                  if (el['aria-expanded'] != null) {
                    a[i].setAttribute('aria-expanded', el['aria-expanded'])
                  }
                }
              })
            }
          }
        }
        if (a[i].getAttribute('class')?.indexOf('e-detailrow') != -1) {
          a[i].style.display = this.detailList[0]
          this.detailList.shift()
          // console.log(this.detailList)
        }
      }
    },
    // 获取当前表格样式data
    getTable() {
      this.dataSession = []
      this.detailList = []
      let a = document.getElementsByTagName('tr')
      for (let i = 0; i < a.length; i++) {
        if (a[i].getAttribute('role') == 'row') {
          // a[i].setAttribute('aria-expanded', false)
          // 是否有子集
          let cell = ''
          let icon = ''
          for (let j = 0; j < a[i].children[1]?.children[0]?.children?.length; j++) {
            let e = a[i].children[1].children[0].children[j]
            if (
              e.getAttribute('class').indexOf('e-treegridexpand') != -1 ||
              e.getAttribute('class').indexOf('e-treegridcollapse') != -1
            ) {
              icon = e.getAttribute('class')
            } else if (e.getAttribute('class').indexOf('e-treecell') != -1) {
              cell = e.textContent
            }
          }
          if (a[i].getAttribute('aria-selected')) {
            icon = 'e-icons e-treegridexpand'
          }
          this.dataSession.push({
            style: a[i].style.display,
            'aria-expanded': a[i].getAttribute('aria-expanded'),
            'aria-selected': a[i].getAttribute('aria-selected'),
            icon,
            cell
          })
          // 在有节点被选中的情况下，手动添加一行
          if (a[i].getAttribute('aria-selected')) {
            this.dataSession.push({
              style: '',
              'aria-expanded': '',
              'aria-selected': null,
              icon: '',
              cell: this.nodeName
            })
            this.detailList.push('')
          }
        }
        if (a[i].getAttribute('class')?.indexOf('e-detailrow') != -1) {
          this.detailList.push(a[i].style.display)
        }
      }
      console.log('dataSession', this.dataSession)
      console.log('detailList', this.detailList)
      // this.reload()
      // this.$refs.templateRefTree.getCurrentTabRef().treeGrid.collapseAtLevel(0)
      // setTimeout(() => {
      //   this.$refs.templateRefTree.getCurrentTabRef().treeGrid.collapseAll()
      // }, 5000)
    },
    handleFormulaDialog(e) {
      const a = this.activeData.columnData.map((item) => {
        return {
          value: item.field,
          text: item.headerText
        }
      })
      this.$dialog({
        modal: HandleFormulaDialog,
        data: {
          title: this.$t('公式编辑器'),
          items: a,
          calVal: e?.calculationFormula
        },
        success: (res) => {
          console.log('公式', res)
          e.calculationFormula = res.calculationFormula
          this.saveColumn(e)
        }
      })
    },
    handleDefinitionDialog(e) {
      this.$dialog({
        modal: HandleBaseFieldDialog,
        data: {
          title: this.$t('值集选择')
        },
        success: (res) => {
          e.valueSet = res.calculationFormula
          this.saveColumn(e)
        }
      })
    },
    // 单元格title，点击
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
    },
    //编辑节点
    editDimension(e) {
      console.log('edit', e)
      this.$API.costModel.saveItem(e).then(() => {
        this.$toast({ content: this.$t('编辑成功'), type: 'success' })
        this.formChange(e)
        // this.queryCostModelItem();
      })
    },
    //新增节点
    handleAddDimension(e, _selectRecords) {
      console.log('handleAddDimension', e, this.activeData)
      let index = 0
      if (Array.isArray(_selectRecords) && _selectRecords.length > 0) {
        index = _selectRecords[0].index
      }
      const params = {
        costModelId: this.costModelId,
        leafNode: 0,
        nodeName: `${this.$t('新建成本项')}${JSON.stringify(Date.now()).substring(8)}`,
        parentId: this.activeData.id
      }
      this.nodeName = params.nodeName
      this.$API.costModel.saveItem(params).then(() => {
        this.$toast({ content: this.$t('新增成功'), type: 'success' })
        this.queryCostModelItem(true)
        console.log('this.activeData', index, this.$refs.treegrid)
        // this.$refs.templateRefTree
        //   .getCurrentTabRef()
        //   .treeGrid.ej2Instances.addRecord(params, index, this.activeData.id ? 'Child' : 'Bottom')
        // this.$refs.templateRefTree2.$refs.ejsRef.addRecord(
        //   params,
        //   index,
        //   this.activeData.id ? 'Child' : ''
        // )
        // this.$refs.treegrid.ej2Instances.addRecord(params, index, this.activeData.id ? 'Child' : '')
        this.queryById()
      })
    },
    //删除节点
    deleteItem() {
      const params = {
        id: this.activeData.id
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          this.$API.costModel.deleteItem(params).then(() => {
            this.$toast({ content: this.$t('删除成功'), type: 'success' })
            this.activeData = {}
            this.$refs.templateRefTree.getCurrentTabRef().treeGrid.ej2Instances.deleteRecord()
            // this.queryCostModelItem()
            this.queryById()
          })
        }
      })
    },
    // 头部-提交（成本模型）
    handleSubmit() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认执行提交操作？`)
        },
        success: async () => {
          this.$refs.costModelDetailRef?.handleSubmit()
        }
      })
    },
    // 头部-停用（成本模型）
    handleStop() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认执行停用操作？`)
        },
        success: async () => {
          const { configId, type, classifyCode } = this.$route.query
          this.$loading()
          const res = await this.$API.costModel
            .disableById({
              idList: [configId]
            })
            .catch(() => this.$hloading())
          this.$hloading()
          if (res.code === 200) {
            this.$toast({ content: this.$t('停用成功！'), type: 'success' })
            this.$router.replace({
              name: `purchase-settings-cost-detail`,
              query: {
                configId: res.data,
                type,
                classifyCode,
                refreshId: Date.now()
              }
            })
          }
        }
      })
    },
    // 头部-复制（成本模型）
    async handleCopy() {
      const { configId, type, classifyCode } = this.$route.query
      this.$loading()
      const res = await this.$API.costModel
        .batchCopy({ ids: [configId] })
        .catch(() => this.$hloading())
      this.$hloading()
      if (res.code === 200) {
        this.$toast({ content: this.$t('复制成功！'), type: 'success' })
        this.$router.replace({
          name: `purchase-settings-cost-detail`,
          query: {
            configId: res.data,
            type,
            classifyCode,
            refreshId: Date.now()
          }
        })
      }
    },
    // 头部-删除（成本模型）
    handleDelete() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认执行删除操作？')
        },
        success: async () => {
          const { configId } = this.$route.query
          this.$loading()
          const res = await this.$API.costModel
            .deleteById({ idList: [configId] })
            .catch(() => this.$hloading())
          this.$hloading()
          if (res.code == 200) {
            this.$toast({ content: this.$t('删除成功！'), type: 'success' })
            this.$router.go(-1)
          }
        }
      })
    },
    // 头部-查看OA进度（成本模型）
    async handleViewOA() {
      let oaApproveLink = this.formObject?.oaApproveLink
      if (!oaApproveLink) {
        const res = await this.$API.costFactorLinkagePricing.queryOALinkCflp({
          id: this.$route.query.configId
        })
        res.code === 200 && (oaApproveLink = res.data)
      }
      oaApproveLink
        ? window.open(oaApproveLink)
        : this.$toast({ content: this.$t('暂无申请单'), type: 'error' })
    }
  }
}
</script>
<style lang="scss" scoped>
// .detail-bottom-leave-active {
//   transition: opacity 0.5s;
// }
.detail-bottom-enter-active {
  transition: opacity 0.3s;
}
.detail-bottom-enter {
  opacity: 0;
}
.detail-bottom-leave-active {
  transition: opacity 0.3s;
}
.detail-bottom-leave-to {
  opacity: 0;
}
/deep/ .e-grid .e-detailcell {
  padding: 0;
  border: none;
}
.tempalte-detail {
  // position: absolute;
  background: transparent;
  // // z-index: 1001;
  // left: 0;
  // top: 0;
  // bottom: 0;
  // right: 0;
  // height: 100%;
  // padding: 0;

  .page-top {
    height: 60px;
    flex-shrink: 0;
  }
  .page-bottom {
    flex: 1;
    height: 100%;
    .detail-bottom {
      // height: 280px;
      background: rgba(0, 70, 156, 0.02);
      border: 1px solid rgba(0, 70, 156, 0.06);
      border-radius: 2px;
      margin: 10px 0 0 0;
      // position: fixed;
      // bottom: 0;
      // width: calc(100% - 280px);
      overflow: auto;
    }
  }

  .page-content {
    background: #fff;
    flex: 1;
    height: 100%;
    padding: 19px 20px 10px 20px;
    flex-direction: column;
    width: 100%;
  }
  .detail-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 15px 30px 5px 30px;
    background: rgba(99, 134, 193, 0.08);
    border: 1px solid #e8e8e8ff;
    border-radius: 8px;
    overflow: auto;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    .right_but {
      display: inline-block;
      cursor: pointer;
      // font-size: 14px;
      // font-weight: 500;
      // color: #00469cff;
      margin: 0 0 0 10px;
    }
    .left {
      color: #292929;
      .title {
        font-size: 20px;
        font-weight: 600;
        .statusC {
          display: inline-block;
          margin-left: 22px;
          // width: 48px;
          padding: 0 5px;
          height: 24px;
          line-height: 22px;
          text-align: center;
          background: #e8ecf5;
          border-radius: 2px;
          font-size: 12px;
          color: rgba(99, 134, 193, 1);
          font-weight: 500;
        }
        .titleC {
          display: inline-block;
          margin-left: 11px;
          height: 22px;
          font-size: 16px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(154, 154, 154, 1);
        }
      }
      .form {
        padding: 10px 0;
        font-size: 12px;
        font-weight: normal;
      }
      .form_div {
        display: inline-block;
        padding-right: 20px;
      }
      .form_formulaTotal {
        padding-top: 8px;
      }
      .data {
        padding: 10px 0;
        font-size: 14px;
        font-weight: 600;
        span {
          margin-left: 3px;
          color: #00469c;
        }
      }
    }
  }
  .detail-content-no {
    background: #e8e8e8;
    // height: calc(100vh - 240px);
    overflow: auto;
    transition: 0.3s;
  }
  .detail-content {
    background: #e8e8e8;
    // height: calc(100vh - 550px);
    // min-height: 500px;
    overflow: auto;
    // transition: 0.3s;
  }
}

/deep/.e-grid {
  .e-content {
    overflow: auto !important;
    position: relative;
    // height: calc(100vh - 580px) !important;
    height: 100% !important;
  }
  .e-rowcell {
    text-align: left !important;
    .grid-edit-column {
      // display: inline-block !important;
    }
  }
}
</style>
