<template>
  <div class="cost-slot3">
    <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
      <mt-form-item prop="node" :label="$t('成本项名称')">
        <mt-select
          v-model="ruleForm.node"
          :width="360"
          :data-source="selectArr"
          :show-clear-button="true"
          @change="changeNode"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item :label="$t('明细列字段')">
        <mt-select
          v-model="ruleForm.detaile"
          :width="360"
          :data-source="selectArr2"
          :show-clear-button="true"
          @change="change"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
    </mt-form>

    <mt-DataGrid
      :data-source="dataSource_3"
      :column-data="columnData_3"
      :row-height="30"
      ref="dataGrid"
      :edit-settings="editSettings"
      @rowSelected="getSelectedRecords"
    ></mt-DataGrid>
  </div>
</template>
<script>
export default {
  props: {
    formData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  watch: {
    formData: {
      handler(v) {
        this.ruleForm = v
      },
      deep: true
    },
    ruleForm: {
      handler(v) {
        this.$emit('formChange', v)
      },
      deep: true
    }
  },
  computed: {
    show() {
      return this.ruleForm.node == '1' ? true : false
    }
  },
  data() {
    return {
      editSettings: {
        allowEditing: true
      },
      ruleForm: {
        nodeName: this.$t('成本项明细'),
        node: '0',
        parentName: '',
        detaile: '0'
      },
      selectArr: [
        { text: this.$t('是'), value: '1' },
        { text: this.$t('否'), value: '0' }
      ],
      selectArr2: [
        { text: this.$t('是'), value: '1' },
        { text: this.$t('否'), value: '0' }
      ],

      columnData_3: [
        {
          field: 'name1',
          headerText: this.$t('属性名称')
        },
        {
          field: 'name2',
          headerText: this.$t('属性描述')
        },
        {
          field: 'name3',
          headerText: this.$t('属性值')
        },
        {
          field: 'name4',
          headerText: this.$t('关联表单')
        }
      ],
      dataSource_3: [
        {
          name1: 'origin',
          name2: this.$t('值来源'),
          name3: this.$t('用户录入'),
          name4: ''
        },
        {
          name1: 'disabeld',
          name2: this.$t('编辑描述'),
          name3: this.$t('用户录入'),
          name4: ''
        },
        {
          name1: 'required',
          name2: this.$t('编辑描述'),
          name3: this.$t('用户录入'),
          name4: ''
        },
        {
          name1: 'default',
          name2: this.$t('默认值'),
          name3: '',
          name4: ''
        }
      ],
      rules: {
        nodeName: [{ required: true, message: this.$t('请输入成本项名称'), trigger: 'blur' }],
        node: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      }
    }
  },
  methods: {
    change(e) {
      this.$emit('nodeChange', e)
    },
    changeNode() {}
  }
}
</script>
<style lang="scss" scoped>
.cost-slot3 {
  padding: 16px;
  /deep/ .mt-form {
    margin-bottom: 16px;
    display: flex;
    .mt-form-item {
      margin: 0 16px 0 0;
    }
  }
  /deep/ .e-grid td.e-rowcell {
    height: 30px;
  }
}
</style>
