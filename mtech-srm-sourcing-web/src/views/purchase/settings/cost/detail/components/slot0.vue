<template>
  <div class="cost-slot0">
    <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
      <mt-form-item prop="nodeName" :label="$t('成本项名称')">
        <mt-input
          item-label-style="left"
          v-model="ruleForm.nodeName"
          :disabled="false"
          :width="360"
          :show-clear-button="true"
          :max-length="30"
          type="text"
          :placeholder="$t('请输入成本项名称')"
          :blur="startEdit"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="leafNode" :label="$t('是否末级节点')">
        <mt-select
          v-model="ruleForm.leafNode"
          :width="360"
          :data-source="selectArr"
          :show-clear-button="true"
          :placeholder="$t('请选择')"
          @blur="startEdit"
        ></mt-select>
      </mt-form-item>
      <mt-form-item :label="$t('上级节点名称')" v-show="show">
        <mt-input
          item-label-style="left"
          v-model="ruleForm.parentName"
          :readonly="true"
          :width="360"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入成本项名称')"
          :blur="startEdit"
        ></mt-input>
      </mt-form-item>
      <mt-form-item :label="$t('是否启用明细管理')" v-show="show">
        <mt-select
          v-model="ruleForm.enableItem"
          :width="360"
          :data-source="selectArr2"
          :show-clear-button="true"
          @change="change"
          :placeholder="$t('请选择')"
          @blur="startEdit"
        ></mt-select>
      </mt-form-item>
    </mt-form>
  </div>
</template>
<script>
export default {
  props: {
    formData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  watch: {
    formData: {
      handler(v) {
        this.ruleForm = { ...v }
      },
      deep: true
    },
    ruleForm: {
      handler() {
        // this.$emit("formChange", v);
      },
      deep: true
    },
    show(v) {
      if (!v) {
        this.ruleForm.enableItem = 0
      }
    }
  },
  computed: {
    show() {
      return this.ruleForm.leafNode == 1 ? true : false
    }
  },
  data() {
    return {
      ruleForm: { ...this.formData },
      selectArr: [
        { text: this.$t('是'), value: 1 },
        { text: this.$t('否'), value: 0 }
      ],
      selectArr2: [
        { text: this.$t('是'), value: 1 },
        { text: this.$t('否'), value: 0 }
      ],
      rules: {
        nodeName: [
          {
            required: true,
            message: this.$t('请输入成本项名称'),
            trigger: 'blur'
          }
        ],
        leafNode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      }
    }
  },
  mounted() {
    this.$emit('nodeChange', { value: this.ruleForm.enableItem })
  },
  methods: {
    change(e) {
      this.$emit('nodeChange', e)
    },
    startEdit() {
      console.log('startEdit', this.ruleForm, this.formData)
      if (JSON.stringify(this.ruleForm) !== JSON.stringify(this.formData)) {
        this.$emit('startEdit', this.ruleForm)
        // this.$emit("formChange", this.ruleForm);
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.cost-slot0 {
  padding: 16px;
  /deep/ .mt-form {
    display: flex;
    .mt-form-item {
      margin: 0 16px 0 0;
    }
  }
}
</style>
