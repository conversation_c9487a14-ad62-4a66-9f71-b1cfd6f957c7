<template>
  <div v-show="show && sport" class="tree-grid-detail" :class="[active ? 'active' : '']">
    <mt-DataGrid
      v-if="sport"
      :data-source="pageConfig.dataSource"
      :column-data="pageConfig.columnData"
      ref="dataGrid"
    ></mt-DataGrid>

    <!-- <mt-template-page ref="template123" :template-config="pageConfig" /> -->
  </div>
</template>
<script>
import { EventBus } from '@/utils/event-bus.js'
export default {
  data() {
    return {
      data: {},
      activeData: {},
      pageConfig: {
        dataSource: [],
        columnData: []
      },
      columnData: [],
      sport: true
    }
  },
  watch: {
    activeData: {
      handler(v) {
        this.data.taskData = v
      },
      deep: true
    }
  },
  computed: {
    show() {
      return this.data.taskData.enableItem == 1 ? true : false
    },
    active() {
      return JSON.stringify(this.activeData) == '{}' ? false : true
    }
  },
  created() {
    console.log('this.data', this.data)
    if (this.data.columnList.length > 0) {
      const a = this.data.columnList.map((item) => {
        return {
          field: item.id,
          headerText: item.columnAlias || item.columnName
        }
      })
      this.$set(this.pageConfig, 'columnData', a)
      let c = []
      let b = {}
      let _tempArr = [
        'cost_factor',
        'base_price',
        'quota_no',
        'conditions_base_price',
        'item_quota_no',
        'cost_factor_quota_no'
      ]
      this.data.itemDataList.forEach((item, i) => {
        b = {}
        item.forEach((item2) => {
          // a[i][item2.columnAlias] = item2.dataValue;
          if (item2?.valueSet && _tempArr.includes(item2.valueSet) && item2?.dataCode) {
            b[item2.columnId] = `(${item2.dataCode})${item2.dataValue}`
          } else {
            b[item2.columnId] = item2.dataValue
          }
        })
        console.log('queryLeafNodeData', i, b)
        c.push(b)
      })
      this.$set(this.pageConfig, 'dataSource', c)
    }
  },
  mounted() {
    EventBus.$on(`treeGridRowSelect-${this.data.id}`, (e) => {
      console.log(`treeGridRowSelect-${this.data.id}`, e)
      this.activeData = e
      if (this.activeData.columnData) {
        this.$set(this.pageConfig, 'columnData', this.activeData.columnData)
      }
      if (this.activeData.dataSource) {
        this.$set(this.pageConfig, 'dataSource', this.activeData.dataSource)
      }
    })
  }
}
</script>
<style lang="scss" scoped>
.tree-grid-detail {
  border: 1px solid #e0e0e0;
  padding: 10px 40px;
  // background: #e0e0e0;
}
.active {
  background: #e0e0e0;
}
/deep/ .mt-data-grid {
  border: 1px solid #e0e0e0;
  .e-grid {
    .e-content {
      // height: 300px !important;
    }
  }
}
</style>
