import { i18n } from '@/main.js'
import detailTemplate from '../components/detailTemplate.vue'
import selectAllCostFactorCode from 'COMPONENTS/NormalEdit/selectAllCostFactorCode' // 成本因子
import { createEditInstance } from '@/utils/ej/dataGrid/index'
import { getValueByPath } from '@/utils/obj'
import Vue from 'vue'

const editInstance = createEditInstance().onInput((ctx, { field }) => {
  const val = ctx.getValueByField(field)

  if (field === 'columnType') {
    if (val === 0) {
      // ctx.setValueByField("supplierVisible", 0);
      ctx.setOptions('calculationFormula', {
        disabled: false
      })
      ctx.setOptions('valueSet', {
        disabled: true,
        value: ''
      })
    } else if (val === 2) {
      ctx.setOptions('calculationFormula', {
        disabled: true
      })
      ctx.setOptions('valueSet', {
        disabled: false
      })
    } else {
      ctx.setOptions('calculationFormula', {
        disabled: true
      })
      ctx.setOptions('valueSet', {
        disabled: true,
        value: ''
      })
    }
  }
})
//
const toolbar = (enabled) => {
  return {
    useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
    tools: [
      [
        {
          id: 'Add',
          icon: 'icon_solid_Createproject',
          title: i18n.t('新增'),
          visibleCondition: () => {
            console.log('enabled:', enabled)
            return enabled !== 0
          }
        },
        {
          id: 'Delete',
          icon: 'icon_list_delete',
          title: i18n.t('删除'),
          visibleCondition: () => {
            console.log('enabled:', enabled)
            return enabled !== 0
          }
        }
      ]
    ]
  }
}
const columnData = () => {
  return [
    {
      field: 'nodeName',
      headerText: i18n.t('成本项明细'),
      width: 1000,
      cssClass: ''
      // cellTools: [
      //   {
      //     id: "Add",
      //     icon: "icon_solid_Createproject",
      //     title: "新增下级",
      //     visibleCondition: (data) => {
      //       return (
      //         data["dimensionId"] && data["dimensionName"] && enabled !== 0
      //       );
      //     },
      //   },
      //   {
      //     id: "Delete",
      //     icon: "icon_list_delete",
      //     title: "删除",
      //     visibleCondition: (data) => {
      //       return data["showDeleteIcon"] && enabled !== 0;
      //     },
      //   },
      // ],
    },
    {
      field: 'price',
      width: 200,
      headerText: i18n.t('总价')
    },
    {
      field: 'id',
      isPrimaryKey: true,
      visible: false
    },
    {
      field: 'id',
      visible: false
    },
    {
      field: 'enableItem',
      visible: false
    },
    {
      field: 'childrenList',
      visible: false
    },
    {
      field: 'leafNode',
      visible: false
    }
  ]
}
// 树状表
export const pageConfig = (enabled) => {
  return [
    {
      gridId: '8e7bf6a0-d23f-43f2-90d6-01ad41ac3b1c',
      useToolTemplate: false,
      toolbar: toolbar(enabled),
      treeGrid: {
        useRowSelect: false,
        detailTemplate: () => {
          return { template: detailTemplate }
        },
        allowPaging: false,
        columnData: columnData(enabled),
        childMapping: 'childrenList',
        // rowSelected: (e) => {
        //   _this.rowSelected(e);
        // },
        editSettings: {
          allowEditing: false,
          allowAdding: true,
          allowDeleting: true,
          mode: 'Row'
        },
        selectionSettings: { type: 'Single', checkboxOnly: false },
        dataSource: []
      }
    }
  ]
}
const toolbar_1 = {
  useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
  tools: [
    [
      {
        id: 'Add',
        icon: 'icon_solid_Createproject',
        title: i18n.t('新增')
      },
      {
        id: 'Delete',
        icon: 'icon_list_delete',
        title: i18n.t('删除')
      },
      {
        id: 'Updata',
        icon: 'icon_solid_Save',
        title: i18n.t('保存')
      },
      {
        id: 'Cancel',
        icon: 'icon_solid_Cancel',
        title: i18n.t('取消')
      }
    ]
  ]
}
const toolbar_2 = {
  useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
  tools: [
    [
      {
        id: 'Add',
        icon: 'icon_solid_Createproject',
        title: i18n.t('新增')
      },
      {
        id: 'Delete2',
        icon: 'icon_solid_edit',
        title: i18n.t('删除')
      },
      {
        id: 'Updata',
        icon: 'icon_solid_Save',
        title: i18n.t('保存')
      },
      {
        id: 'Cancel',
        icon: 'icon_solid_Cancel',
        title: i18n.t('取消')
      }
    ]
  ]
}
const columnData_1 = [
  {
    type: 'checkbox',
    width: '60',
    allowEditing: false
  },
  {
    field: 'columnCode',
    headerText: i18n.t('字段编码'),
    validationRules: {
      required: true,
      maxLength: [20, i18n.t('只允许输入1-20个字母和数字')],
      minLength: [1, i18n.t('只允许输入1-20个字母和数字')],
      regex: ['^[A-Za-z0-9_]*$', i18n.t('只允许输入1-20个字母和数字')]
    },
    headerTemplate: () => {
      return {
        template: Vue.component('requiredCell', {
          template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{fieldName}}</span>
                </div>
              `,
          data() {
            return {
              data: {},
              fieldName: i18n.t('字段编码')
            }
          }
        })
      }
    }
  },
  {
    field: 'columnName',
    headerText: i18n.t('字段名称'),
    validationRules: {
      required: true,
      maxLength: [30, i18n.t('只允许输入1-30个字符')],
      minLength: [1, i18n.t('只允许输入1-30个字符')]
    },
    headerTemplate: () => {
      return {
        template: Vue.component('requiredCell', {
          template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{fieldName}}</span>
                </div>
              `,
          data() {
            return {
              data: {},
              fieldName: i18n.t('字段名称')
            }
          }
        })
      }
    }
  },
  {
    field: 'builtIn',
    headerText: i18n.t('是否内置'),
    allowEditing: false,
    edit: editInstance.create({
      getEditConfig: () => ({
        type: 'select',
        disabled: true,
        dataSource: [
          { text: i18n.t('否'), value: 0 },
          { text: i18n.t('是'), value: 1 }
        ]
      })
    }),
    formatter: ({ field }, item) => {
      const cellVal = getValueByPath(item, field)
      switch (Number(cellVal)) {
        case 0:
          return i18n.t('否')
        case 1:
          return i18n.t('是')
        default:
          return cellVal
      }
    }
  },
  {
    field: 'columnAlias',
    headerText: i18n.t('字段别名'),
    validationRules: {
      required: false,
      maxLength: [30, i18n.t('只允许输入1-30个字符')],
      minLength: [1, i18n.t('只允许输入1-30个字符')]
    }
  },
  {
    field: 'sortValue',
    headerText: i18n.t('列顺序（从左到右）'),
    validationRules: {
      required: true,
      maxLength: [5, i18n.t('只允许输入1-5个字符')],
      minLength: [1, i18n.t('只允许输入1-5个字符')],
      min: [0, i18n.t('只允许输入大于0的数字')]
    },
    headerTemplate: () => {
      return {
        template: Vue.component('requiredCell', {
          template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{fieldName}}</span>
                </div>
              `,
          data() {
            return {
              data: {},
              fieldName: i18n.t('列顺序（从左到右）')
            }
          }
        })
      }
    }
  },
  {
    field: 'columnType',
    headerText: i18n.t('组件类型'),
    headerTemplate: () => {
      return {
        template: Vue.component('requiredCell', {
          template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{fieldName}}</span>
                </div>
              `,
          data() {
            return {
              data: {},
              fieldName: i18n.t('组件类型')
            }
          }
        })
      }
    },
    edit: editInstance.create({
      getEditConfig: () => ({
        type: 'select',
        readonly: false,
        dataSource: [
          { text: i18n.t('数值'), value: 0 },
          { text: i18n.t('文本'), value: 1 },
          { text: i18n.t('值集'), value: 2 }
        ]
      })
    }),
    formatter: ({ field }, item) => {
      const cellVal = getValueByPath(item, field)
      switch (Number(cellVal)) {
        case 0:
          return i18n.t('数值')
        case 1:
          return i18n.t('文本')
        case 2:
          return i18n.t('值集')
        default:
          return cellVal
      }
    }
  },
  {
    field: 'calculationFormula',
    headerText: i18n.t('公式'),
    allowEditing: false,
    // edit: editInstance.create({
    //   getEditConfig: ({ rowData }) => {
    //     return {
    //       type: "text",
    //       disabled: Number(rowData.columnType) === 0,
    //     };
    //   },
    // }),
    cellTools: [
      {
        id: 'formula',
        title: i18n.t('配置公式'),
        visibleCondition: (data) => {
          return data['columnType'] == 0
        }
      }
    ]
  },
  {
    field: 'valueSet',
    headerText: i18n.t('值集'),
    allowEditing: false,
    edit: editInstance.create({
      getEditConfig: ({ rowData }) => {
        console.log('rowdata', rowData)
        return {
          type: 'select',
          disabled: rowData.builtIn == 1 ? true : false,
          dataSource: [
            { text: i18n.t('单位'), value: 'unit' },
            { text: i18n.t('物料'), value: 'item' },
            { text: i18n.t('成本因子'), value: 'cost_factor' },
            { text: i18n.t('定额'), value: 'quota_no' },
            { text: i18n.t('基价'), value: 'base_price' },
            { text: i18n.t('条件基价'), value: 'conditions_base_price' },
            { text: i18n.t('物料定额'), value: 'item_quota_no' },
            { text: i18n.t('成本因子定额'), value: 'cost_factor_quota_no' }
          ]
        }
      }
    }),
    formatter: ({ field }, item) => {
      const cellVal = getValueByPath(item, field)
      switch (cellVal) {
        case 'unit':
          return i18n.t('单位')
        case 'item':
          return i18n.t('物料')
        case 'cost_factor':
          return i18n.t('成本因子')
        case 'quota_no':
          return i18n.t('定额')
        case 'base_price':
          return i18n.t('基价')
        case 'conditions_base_price':
          return i18n.t('条件基价')
        case 'item_quota_no':
          return i18n.t('物料定额')
        case 'cost_factor_quota_no':
          return i18n.t('成本因子定额')
        default:
          return cellVal
      }
    }
    // cellTools: [
    //   {
    //     id: "definition",
    //     title: "选择",
    //     visibleCondition: (data) => {
    //       return data["columnType"] == 2;
    //     },
    //   },
    // ],
  },
  {
    field: 'costFactorCode',
    headerText: i18n.t('成本因子编码'),
    allowEditing: true,
    editTemplate: () => {
      return {
        template: selectAllCostFactorCode
      }
    }
  }
  // {
  //   field: "nodeName",
  //   allowEditing: false,
  //   headerText: i18n.t("组件属性"),
  //   cellTools: [{ id: "definition", title: "定义" }],
  // },
]

// 基础表
export const pageConfig2 = (isLastNode = '0', _this) => {
  let arr = [
    {
      title: i18n.t('基础配置')
    }
  ]
  if (isLastNode == '1') {
    arr.push(
      ...[
        {
          title: i18n.t('字段配置'),
          toolbar: toolbar_1,
          grid: {
            allowPaging: false,
            allowEditting: true,
            // actionComplete: (e) => {
            //   console.log("actionComplete", e);
            //   if (e.type == "actionComplete" && e.action) {
            //     _this.saveColumn(e.data);
            //   }
            // },
            actionComplete(e) {
              console.log('actionComplete--', e, _this)
              const { rowIndex, data } = e
              if (e.requestType === 'save') {
                let _currentData = _this.$refs.templateRef
                  .getCurrentUsefulRef()
                  .ejsRef.getCurrentViewRecords()
                const { columnCode, columnName, columnAlias } = data
                let _columnName = columnName
                  ? columnName.replace(/[\x2f-\x39]|[\x28-\x29]/g, function ($0) {
                      return $0 == ' ' ? '\u3000' : String.fromCharCode($0.charCodeAt(0) + 0xfee0)
                    })
                  : ''
                let _columnAlias = columnAlias
                  ? columnAlias.replace(/[\x2f-\x39]|[\x28-\x29]/g, function ($0) {
                      return $0 == ' ' ? '\u3000' : String.fromCharCode($0.charCodeAt(0) + 0xfee0)
                    })
                  : ''
                let valid = true
                let flag = new RegExp(
                  "[`~!@#$^&*()=|{}':;',\\[\\].<>《》/?~！@#￥……&*——|{}【】‘；：”“'。，、？ ]"
                )
                if (_currentData.filter((e) => e.columnCode == columnCode).length > 1) {
                  _this.$toast({ content: _this.$t('字段编码不能重复'), type: 'warning' })
                  valid = false
                } else if (!isNaN(_columnName) || flag.test(_columnName)) {
                  _this.$toast({
                    content: _this.$t('字段名称不能为纯数字或包含特殊字符'),
                    type: 'warning'
                  })
                  valid = false
                } else if (_currentData.filter((e) => e.columnName == _columnName).length > 1) {
                  _this.$toast({
                    content: _this.$t('字段名称不能重复'),
                    type: 'warning'
                  })
                  valid = false
                } else if (_currentData.filter((e) => e.columnAlias == _columnAlias).length > 1) {
                  _this.$toast({ content: _this.$t('字段别名不能重复'), type: 'warning' })
                  valid = false
                } else if (_columnAlias && (!isNaN(_columnAlias) || flag.test(_columnAlias))) {
                  _this.$toast({
                    content: _this.$t('字段别名不能为纯数字或包含特殊字符'),
                    type: 'warning'
                  })
                  valid = false
                }
                if (!valid) {
                  // 当出现错误时，指定行进入编辑状态
                  _this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
                  _this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
                  return
                } else {
                  if (e.type == 'actionComplete' && e.action) {
                    if (!e.data.columnAlias) {
                      e.data.columnAlias = _columnName
                    } else {
                      e.data.columnAlias = _columnAlias
                    }
                    e.data.columnName = _columnName
                    _this.saveColumn(e.data)
                  }
                }
              }
            },
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true
            },
            // cellSaved: this.handleEditRequireFields,
            columnData: columnData_1,
            dataSource: []
          }
        },
        {
          title: i18n.t('成本明细表'),
          toolbar: toolbar_2,
          grid: {
            allowPaging: false,
            allowEditting: true,
            actionComplete: (e) => {
              console.log('actionComplete', e)
              if (e.type == 'actionComplete' && e.action) {
                _this.saveItemData(e.data)
              }
            },
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true
            },
            // cellSaved: this.handleEditRequireFields,
            columnData: [
              {
                type: 'checkbox',
                width: '50',
                allowEditing: false
              }
            ],
            dataSource: []
          }
        }
        // {
        //   title: "业务属性定义",
        // },
      ]
    )
  }
  return arr
}

// 成本模型明细-工具栏按钮
export const costModelToolbar = (that) => [
  {
    code: 'add',
    name: i18n.t('新增一级明细'),
    status: 'info',
    disabled: !that.editable
  },
  {
    code: 'save',
    name: i18n.t('保存'),
    status: 'info',
    disabled: !that.editable
  },
  {
    code: 'import',
    name: i18n.t('导入'),
    status: 'info',
    disabled: !that.editable
  },
  {
    code: 'export',
    name: i18n.t('导出'),
    status: 'info'
  }
]
