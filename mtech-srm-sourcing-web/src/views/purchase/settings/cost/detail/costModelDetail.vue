<template>
  <div class="detail-content">
    <!-- 附件参数表单 -->
    <div class="toggle-container">
      <div v-if="extraFormItems.length" class="common-form">
        <div class="common-title">
          <span style="margin-right: 5px">{{ $t('结果公式') }}</span>
          <i
            v-if="showExtraForm"
            class="vxe-icon-arrow-up"
            @click="() => (showExtraForm = false)"
          />
          <i v-else class="vxe-icon-arrow-down" @click="() => (showExtraForm = true)" />
        </div>
        <div class="common-body" v-if="showExtraForm">
          <custom-form
            :data="extraFormData"
            :form-items="extraFormItems"
            type="extra"
            @change="(data) => handleFormValueChange('extra', data)"
          />
          <div class="extra-tip">{{ extraTipInfo }}</div>
        </div>
      </div>
      <!-- 通用参数表格 -->
      <div v-if="commonFormItems.length" class="common-form">
        <div class="common-title">
          <span style="margin-right: 5px">{{ $t('公共参数') }}</span>
          <i
            v-if="showCommonForm"
            class="vxe-icon-arrow-up"
            @click="() => (showCommonForm = false)"
          />
          <i v-else class="vxe-icon-arrow-down" @click="() => (showCommonForm = true)" />
        </div>
        <div class="common-body" v-if="showCommonForm">
          <custom-form
            :data="commonFormData"
            :form-items="commonFormItems"
            type="common"
            @change="(data) => handleFormValueChange('common', data)"
          />
        </div>
      </div>
    </div>
    <sc-table
      ref="sctableRef"
      class="sortable-tree-demo"
      row-id="id"
      :keep-source="true"
      :tree-config="treeConfig"
      :edit-config="editConfig"
      :tooltip-config="tooltipConfig"
      :is-show-refresh-bth="true"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      @edit-closed="handleCalculate"
      @refresh="handleRefresh"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :disabled="item.disabled"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
        <vxe-switch
          v-model="enableCalculate"
          :open-label="$t('已启用成本测算')"
          :close-label="$t('已禁用成本测算')"
          @change="handleEnableCalculateChange"
        />
      </template>
    </sc-table>
    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-name="downTemplateName"
      :request-urls="requestUrls"
      :down-template-params="downTemplateParams"
      file-key="file"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    />
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'
import CustomForm from './components/customForm.vue'
import Sortable from 'sortablejs'
import XEUtils from 'xe-utils'
import { v1 as uuidv1 } from 'uuid'

import costModelDetailMixin from './config/costModelDetailMixin.js'
import selectMixin from './config/selectMixin.js'

import { download, getHeadersFileName } from '@/utils/utils'
import cloneDeep from 'lodash/cloneDeep'

export default {
  components: {
    ScTable,
    CustomForm,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  mixins: [costModelDetailMixin, selectMixin],
  props: {
    formObject: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      enableCalculate: false, // 是否启用成本测算
      loading: false,
      extraFormItems: [],
      commonFormItems: [],
      extraFormData: {},
      commonFormData: {},
      columns: [],
      dynamicColumns: [],
      tableData: [],
      treeConfig: {
        children: 'itemList',
        expandAll: true
      },
      editConfig: {
        trigger: 'click',
        mode: 'row',
        showStatus: true
      },
      tooltipConfig: {
        enterable: true,
        contentMethod: ({ column, row }) => {
          const { field } = column
          switch (field) {
            case 'nodeName':
              return row?.nodeName + '\n\n' + row?.nodeCode
            case 'calculationFormulaSpec':
              return row?.calculationFormulaSpec + '\n\n' + row?.calculationFormula
            default:
              // 其余的单元格展示默认的内容
              return null
          }
        }
      },
      sortable2: null,

      requestUrls: {},
      downTemplateName: this.$t('导入模板'),
      downTemplateParams: {},
      extraFormula: [],
      showExtraForm: true,
      showCommonForm: true,
      isContentChange: false
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    editable() {
      // 停用、草稿、驳回可编辑
      const isEdit = [-1, 0, 2].includes(this.formObject.status)

      // 停用、草稿、驳回状且登录账号为创建人时可编辑
      // return isEdit && sameUser
      return isEdit
    },
    extraTipInfo() {
      const arr = []
      this.extraFormula.forEach((item) => {
        const { columnName, calculationFormulaSpec } = item
        const spec =
          !calculationFormulaSpec && calculationFormulaSpec !== 0 ? '' : calculationFormulaSpec
        spec && arr.push(columnName + '=' + spec)
      })
      return arr.join(';')
    }
  },
  mounted() {
    this.init()
  },
  watch: {
    formObject: {
      handler() {
        this.getCostModelDetailColumns()
      },
      deep: true,
      immediate: true
    }
  },
  beforeDestroy() {
    if (this.sortable2) {
      this.sortable2.destroy()
    }
  },
  methods: {
    // 初始化
    init() {
      this.treeDrop()
      // this.getCostModelDetailColumns()
      this.getCostModelDetailList()
    },
    // 表单数据修改
    handleFormValueChange(type, formData) {
      this.isContentChange = true
      this[type + 'FormData'] = formData
      if (type === 'common') {
        this.handleCalculate({})
      }
    },
    // 开启/关闭成本测算按钮
    handleEnableCalculateChange() {
      this.getCostModelDetailColumns()
    },
    // 获取配置列、表单信息
    async getCostModelDetailColumns() {
      const res = await this.$API.costModel.getCostModelDetailColumns({
        costModelId: this.$route.query.configId
      })
      if (res.code === 200 && res.data) {
        this.formateColumns(res.data)
      }
    },
    // 刷新
    handleRefresh() {
      // 草稿、停用、审批驳回、审批废弃，点击刷新可以从成本模型字段配置进行更新模型
      if ([-1, 0, 2, 4].includes(this.formObject.status)) {
        this.reloadCostModelDetailColumns()
      }
      this.getCostModelDetailList('refresh')
    },
    // 重载
    async reloadCostModelDetailColumns() {
      const res = await this.$API.costModel.reloadCostModelDetailColumns({
        costModelId: this.$route.query.configId
      })
      if (res.code === 200 && res.data) {
        this.formateColumns(res.data)
      }
    },
    // 处理动态配置信息
    formateColumns(obj) {
      const { extra_formula, form, header } = obj
      const typeMap = {
        0: 'number',
        1: 'text',
        2: 'select'
      }
      // 附加动态表单信息
      this.extraFormItems = []
      this.extraFormula = extra_formula || []
      extra_formula?.forEach((item) => {
        const {
          id,
          columnCode,
          columnName,
          columnType,
          columnAlias,
          valueSet,
          calculationFormula,
          calculationFormulaSpec
        } = item
        this.extraFormItems.push({
          id,
          type: typeMap[item.columnType],
          fieldCode: columnCode,
          fieldName: columnName,
          columnAlias,
          columnType,
          valueSet,
          calculationFormula,
          calculationFormulaSpec,
          readonly: this.enableCalculate
        })

        this.enableCalculate
          ? (this.extraFormData = {})
          : (this.extraFormData[item.columnCode] = calculationFormulaSpec)
      })

      // 通用动态表单信息
      this.commonFormItems = []
      form?.forEach((item) => {
        this.commonFormItems.push({
          type: typeMap[item.columnType],
          fieldCode: item.columnCode,
          fieldName: item.columnName,
          columnAlias: item.columnAlias,
          columnType: item.columnType,
          valueSet: item.valueSet,
          readonly: !this.enableCalculate
        })
      })

      // 动态列信息
      this.dynamicColumns = []
      const defaultColumns = cloneDeep(this.defaultColumns)
      header?.forEach((item) => {
        this.dynamicColumns.push({
          field: item.columnCode,
          title: item.columnName,
          columnAlias: item.columnAlias,
          columnType: item.columnType,
          minWidth: 150,
          editRender: {
            enabled: !this.editable || this.enableCalculate
          },
          slots: {
            header: () => {
              return [
                <vxe-tooltip content={item.columnName + '\n\n' + item.columnCode} enterable>
                  <span>{item.columnName}</span>
                </vxe-tooltip>
              ]
            },
            default: ({ row, level }) => {
              const tip =
                level === 0 ? this.$t('合计：') : row.itemList?.length ? this.$t('小计：') : ''
              let template = [<span>{row[item.columnCode]}</span>]

              // 外发类型，非末级金额列，显示为“合计/小计XXXXX”
              if (this.$route.query.classifyCode === 'out_going' && item.columnCode === 'amount') {
                template = [
                  <span>
                    {tip}
                    {row[item.columnCode]}
                  </span>
                ]
              }
              return template
            },
            edit: ({ row, column, level }) => {
              let template = [<span>{row[item.columnCode]}</span>]

              // 成本因子，自动带出规格、品牌、单位、单价
              // const costFactorCol = header?.find((t) => t.columnCode === 'costFactor')
              // const arr = ['costFactorSpec', 'costFactorBrand', 'unitName', 'price']
              // const colDisabled = costFactorCol && arr.includes(item.columnCode)

              // 成本构成为‘其他/它XXX’，成本因子可选择、可输入
              const isInput =
                item.valueSet === 'cost_factor' && this.judgeStartWithStr(row.nodeName)
              // 仅末级可编辑动态列
              if (!row.itemList?.length) {
                switch (typeMap[item.columnType]) {
                  case 'select':
                    template = [
                      <div style='display: flex'>
                        <vxe-input
                          v-model={row[item.columnCode]}
                          readonly={!isInput}
                          clearable
                          type='search'
                          on-clear={() => this.handleClearItem(row, column, item.valueSet)}
                          on-search-click={() => this.handleSelectItem(row, column, item.valueSet)}
                          on-input={({ value }) => {
                            if (item.valueSet === 'cost_factor') {
                              row[item.columnCode + 'Json'] = value
                                ? {
                                    costFactorCode: value,
                                    costFactorName: value
                                  }
                                : null
                            }
                          }}
                        />
                      </div>
                    ]
                    break
                  case 'number':
                  case 'text':
                    template = [
                      <vxe-input
                        v-model={row[item.columnCode]}
                        type={typeMap[item.columnType]}
                        min='0'
                        clearable
                      />
                    ]
                    break
                  default:
                    template = [<span>{row[item.columnCode]}</span>]
                    break
                }
              }
              // 外发类型，非末级金额列，显示为“合计/小计XXXXX”
              else if (
                this.$route.query.classifyCode === 'out_going' &&
                item.columnCode === 'amount'
              ) {
                const tip =
                  level === 0 ? this.$t('合计：') : row.itemList?.length ? this.$t('小计：') : ''
                template = [
                  <span>
                    {tip}
                    {row[item.columnCode]}
                  </span>
                ]
              }

              return template
            }
          }
        })
      })
      // 成本测算，提示信息显示在table表的提示信息列
      if (this.enableCalculate) {
        const column = {
          field: 'errorMsg',
          title: this.$t('提示信息'),
          minWidth: 150,
          slots: {
            default: ({ row }) => {
              return [<span style='color: red'>{row.errorMsg}</span>]
            }
          }
        }
        // 启用成本测算，增加“提示信息”列，隐藏“操作”列
        defaultColumns.push(column)
        defaultColumns.shift()
        defaultColumns.splice(3, 0, ...this.dynamicColumns)
      } else {
        defaultColumns.splice(4, 0, ...this.dynamicColumns)
      }
      this.columns = defaultColumns
    },
    // 获取列表
    async getCostModelDetailList(type) {
      this.loading = true
      const res = await this.$API.costModel.getCostModelDetailList({
        costModelId: this.$route.query.configId
      })
      this.loading = false
      if (res.code === 200) {
        const expandRows = this.tableRef.getTreeExpandRecords()

        this.tableData = res.data || []
        if (type === 'refresh') {
          // 仅展开原本展开的行
          expandRows.forEach((row) => {
            const node = XEUtils.findTree(
              this.tableData,
              (r) => r.id === row.id || (row.isAdd && r.sortCode === row.sortCode),
              {
                children: 'itemList'
              }
            )?.item
            if (node) {
              this.$nextTick(() => this.tableRef.setTreeExpand(node, true))
            }
          })
        }
        this.isContentChange = false
      }
    },
    // 清除动态列（选择弹窗）
    handleClearItem(row, column, valueSet) {
      row[column.field] = null
      row[column.field + 'Json'] = {}
      row.isUpdate = true

      // 成本因子，带出规格、品牌、单位、单价
      if (valueSet === 'cost_factor' && !this.judgeStartWithStr(row.nodeName)) {
        const tempList = ['costFactorSpec', 'costFactorBrand', 'unitName', 'price']
        tempList.forEach((field) => {
          row[field] = null
          if (field === 'unitName') {
            row.unitNameJson = {}
          }
        })
      }

      this.handleUpdateRow(row)
    },
    // 选择动态列（选择弹窗）
    handleSelectItem(row, column, valueSet) {
      this.$dialog({
        modal: () => import('@/views/common/components/dialog/itemCodeDialog.vue'),
        data: {
          title: column.title,
          valueSet
        },
        success: (data) => {
          const oldCellValue = row[column.field]
          const fields = this.getJsonValueFields(valueSet)
          const newValue = data[fields.valueCode]
          const jsonValue = {}
          if (valueSet === 'cost_factor') {
            jsonValue['costFactorCode'] = data[fields.valueCode]
            jsonValue['costFactorName'] = data[fields.nameCode]
          } else {
            jsonValue[fields.valueCode] = data[fields.valueCode]
            jsonValue[fields.nameCode] = data[fields.nameCode]
          }

          if (newValue !== oldCellValue) {
            row[column.field] = newValue
            row[column.field + 'Json'] = jsonValue
            row.isUpdate = true

            // 成本因子，带出规格、品牌、单位、单价
            if (valueSet === 'cost_factor') {
              const tempList = [
                { field: 'costFactorSpec', valueKey: 'costFactorSpecification' },
                { field: 'costFactorBrand', valueKey: 'costFactorBrand' },
                { field: 'unitName', valueKey: 'baseMeasureUnitCode' },
                { field: 'price', valueKey: 'unitPriceUntaxed' }
              ]
              tempList.forEach((item) => {
                row[item.field] = data[item.valueKey]
                if (item.field === 'unitName') {
                  row.unitNameJson = {
                    unitCode: data.baseMeasureUnitCode,
                    unitName: data.baseMeasureUnitName
                  }
                }
              })
            }
            this.handleUpdateRow(row)
          }
        },
        close: () => {
          this.tableRef.setEditRow(row)
        }
      })
    },
    // 拖动行，调整层级
    treeDrop() {
      this.$nextTick(() => {
        const xTable = this.tableRef
        this.sortable2 = Sortable.create(
          xTable.$el.querySelector('.body--wrapper>.vxe-table--body tbody'),
          {
            handle: '.drag-btn',
            onEnd: ({ item, oldIndex }) => {
              const options = { children: 'itemList' }

              const targetTrElem = item
              const wrapperElem = targetTrElem.parentNode
              const prevTrElem = targetTrElem.previousElementSibling
              const tableTreeData = this.tableData
              const selfRow = xTable.getRowNode(targetTrElem).item
              const selfNode = XEUtils.findTree(tableTreeData, (row) => row === selfRow, options)
              if (prevTrElem) {
                // 移动到节点
                const prevRow = xTable.getRowNode(prevTrElem).item
                const prevNode = XEUtils.findTree(tableTreeData, (row) => row === prevRow, options)

                if (
                  XEUtils.findTree(selfRow[options.children], (row) => prevRow === row, options)
                ) {
                  // 错误的移动
                  const oldTrElem = wrapperElem.children[oldIndex]
                  wrapperElem.insertBefore(targetTrElem, oldTrElem)
                  return this.$toast({
                    content: this.$t('不允许自己给自己拖动'),
                    type: 'warning'
                  })
                }
                const currRow = selfNode.items.splice(selfNode.index, 1)[0]
                if (xTable.isTreeExpandByRow(prevRow)) {
                  // 移动到当前的子节点
                  prevRow[options.children].splice(0, 0, currRow)
                } else {
                  // 移动到相邻节点
                  prevNode.items.splice(
                    prevNode.index + (selfNode.index < prevNode.index ? 0 : 1),
                    0,
                    currRow
                  )
                }
              } else {
                // 移动到第一行
                const currRow = selfNode.items.splice(selfNode.index, 1)[0]
                tableTreeData.unshift(currRow)
              }
              // 如果变动了树层级，需要刷新数据
              this.tableData = [...tableTreeData]
            }
          }
        )
      })
    },
    // 点击工具栏
    handleClickToolBar(e) {
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'save':
          this.handleSave()
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          this.handleExport()
          break
        default:
          break
      }
    },
    // 新增
    handleAdd() {
      const newRow = { id: uuidv1(), isAdd: true }
      this.tableData.unshift(newRow)
      // 设置行为编辑状态
      this.tableRef.setEditRow(newRow)
    },
    // 保存
    async handleSave() {
      const params = {
        costModelId: this.$route.query.configId,
        deleteIdList: [],
        itemList: this.resetNewItemId(this.tableData),
        extraFormulaList: this.formatFormValueList('extra')
      }
      const res = await this.$API.costModel.saveDetailItemList(params)
      if (res.code === 200) {
        this.$toast({ content: this.$t('保存成功'), type: 'success' })
        this.getCostModelDetailColumns()
        this.getCostModelDetailList('refresh')
      }
    },
    // 提交
    async handleSubmit() {
      const { configId, type, classifyCode } = this.$route.query
      const params = {
        costModelId: configId,
        deleteIdList: [],
        itemList: this.resetNewItemId(this.tableData),
        extraFormulaList: this.formatFormValueList('extra')
      }
      this.$loading()
      const res = await this.$API.costModel
        .submitDetailItemList(params)
        .catch(() => this.$hloading())
      this.$hloading()
      if (res.code === 200) {
        this.$toast({ content: this.$t('提交成功！'), type: 'success' })
        this.$router.replace({
          name: `purchase-settings-cost-detail`,
          query: {
            configId,
            type,
            classifyCode,
            refreshId: Date.now()
          }
        })
      }
    },
    // 导入
    handleImport() {
      this.requestUrls = {
        templateUrlPre: 'costModel',
        uploadUrl: 'importItem',
        costModelId: this.$route.query.configId,
        noDown: true
      }
      this.showUploadExcel(true)
    },
    // 导出
    async handleExport() {
      const res = await this.$API.costModel.exportItem({
        costModelId: this.$route.query.configId
      })

      if (res.data) {
        this.$toast({
          type: 'success',
          content: this.$t('正在导出，请稍后！')
        })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      }
    },
    // 成本测算
    async handleCalculate(e) {
      const { row } = e
      let isRowUpdate = row ? this.tableRef.isUpdateByRow(row) : true
      // 解决弹窗编辑单元格，更新之后isUpdateByRow方法获取为false的问题，但实际值更新了（用row的isUpdate属性标识行是否被更新）
      if (row && row.isUpdate) {
        isRowUpdate = true
      }
      !this.enableCalculate && (this.isContentChange = true)
      // 未启用成本测算、行未更新，不进行成本测算
      if (!this.enableCalculate || !isRowUpdate) {
        return
      }
      const { costModelCode, versionCode } = this.formObject
      const params = {
        costModelCode,
        versionCode,
        priceUnit: this.tableData[0]?.priceUnit || 1,
        extraList: this.formatFormValueList('extra'),
        formValueList: this.formatFormValueList(),
        itemList: this.formatItemList(this.tableData)
      }
      this.loading = true
      const res = await this.$API.costModel
        .costCalculate(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const expandRows = this.tableRef.getTreeExpandRecords()
        this.tableData = this.serializeItemList(res.data?.itemList || [])
        this.extraFormData = this.serializeFormData(res.data?.extraList || [])

        // 仅展开原本展开的行
        expandRows.forEach((row) => {
          const node = XEUtils.findTree(this.tableData, (r) => r.id === row.id, {
            children: 'itemList'
          })?.item
          if (node) {
            this.$nextTick(() => this.tableRef.setTreeExpand(node, true))
          }
        })
      }
    },
    // 递归处理，新增项，自定id义置空
    resetNewItemId(treeList) {
      const resList = treeList.map((item) => {
        return {
          ...item,
          id: item.isAdd ? null : item.id,
          nodeCode: item.isAdd ? null : item.nodeCode,
          itemList: item.itemList ? this.resetNewItemId(item.itemList) : []
        }
      })
      return resList
    },
    // 处理成本测算参数
    formatFormValueList(type) {
      const formValueList = []
      if (type === 'extra') {
        this.extraFormItems.forEach((item) => {
          const { id, fieldCode, fieldName, calculationFormula, columnAlias, columnType } = item
          formValueList.push({
            id,
            fieldCode,
            fieldName,
            columnCode: fieldCode,
            columnName: fieldName,
            columnAlias,
            columnType,
            calculationFormula,
            calculationFormulaSpec: this.extraFormData[fieldCode]
          })
        })
      } else {
        this.commonFormItems.forEach((item) => {
          const { fieldCode, fieldName, columnAlias, columnType } = item
          formValueList.push({
            columnCode: fieldCode,
            columnName: fieldName,
            columnAlias,
            dataValue: columnType === 0 ? this.commonFormData[fieldCode] : null,
            stringValue: columnType === 1 ? this.commonFormData[fieldCode] : null,
            jsonValue: columnType === 2 ? this.commonFormData[fieldCode + 'Json'] : null
          })
        })
      }
      return formValueList
    },
    // 递归处理，成本测算参数
    formatItemList(treeList) {
      const resList = treeList.map((item) => {
        const itemValueList = []
        this.dynamicColumns.forEach((c) => {
          const { field, title, columnAlias, columnType } = c
          let dataValue = null
          if (columnType === 0 && (item[field] || item[field] === 0)) {
            dataValue = Number(item[field])
          }
          itemValueList.push({
            columnCode: field,
            columnName: title,
            columnAlias,
            columnType,
            dataValue,
            stringValue: columnType === 1 ? item[field] : null,
            jsonValue: columnType === 2 ? item[field + 'Json'] : null
          })
        })
        return {
          ...item,
          itemList: item.itemList ? this.formatItemList(item.itemList) : [],
          itemValueList
        }
      })
      return resList
    },
    // 序列化，成本测算结果-表单
    serializeFormData(dataList) {
      const formData = {}
      dataList.forEach((item) => {
        formData[item.fieldCode] = item.result
      })
      return formData
    },
    // 序列化，成本测算结果-列表
    serializeItemList(treeList) {
      const resList = treeList.map((item) => {
        item.itemValueList?.forEach((v) => {
          if (v.columnType === 0) {
            item[v.columnCode] = v.dataValue
          } else if (v.columnType === 1) {
            item[v.columnCode] = v.stringValue
          } else {
            const key = Object.keys(v.jsonValue || {})?.find((k) => k?.includes('Code'))
            item[v.columnCode] = key ? v.jsonValue[key] : null
            item[v.columnCode + 'Json'] = key ? v.jsonValue : null
          }
        })
        return {
          ...item,
          itemList: item.itemList ? this.serializeItemList(item.itemList) : []
        }
      })
      return resList
    },
    // 展示/不展示 上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = [] // 清空数据
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 导入成功
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.getCostModelDetailList('refresh')
    },
    // utils - 判断是否以指定字符串开头
    judgeStartWithStr(nodeName) {
      let flag = this.judgeParentNode(nodeName)
      let arr = [this.$t('其他'), this.$t('其它')]
      arr.forEach((item) => {
        if (nodeName.startsWith(item)) {
          flag = true
        }
      })
      return flag
    },
    // utils - 匹配父节点
    judgeParentNode(nodeName) {
      let arr = ['profit', 'cost']
      for (const data of this.tableData) {
        if (data.itemList?.length) {
          for (const child of data.itemList) {
            if (child.nodeName === nodeName && arr.includes(data.nodeCode)) {
              return true
            }
            if (child.itemList) {
              for (const subChild of child.itemList) {
                if (subChild.nodeName === nodeName && arr.includes(data.nodeCode)) {
                  return true
                }
              }
            }
          }
        } else {
          return arr.includes(data.nodeCode)
        }
      }
      return false
    }
  }
}
</script>
<style lang="scss" scoped>
.detail-content {
  background: #fff !important;
  overflow: hidden !important;
  height: calc(100vh - 205px);

  .common-form {
    padding: 10px 0;
    .extra-tip {
      margin: 5px 5px 0 5px;
      color: red;
      font-weight: bold;
    }
    .common-title {
      padding: 0 0 5px 5px;
      font-weight: bold;
      font-size: 15px;
    }
    .common-body {
      padding: 5px 10px 10px 10px;
      border: 1px solid #e8e8e8;
      border-radius: 8px;
    }
  }
}

::v-deep {
  .mt-form {
    width: 100%;
    box-sizing: border-box;
    display: grid;
    grid-template-columns: repeat(auto-fill, calc(100% / 6 - 10px));
    justify-content: space-between;
    grid-gap: 5px;
  }
  .mt-input {
    width: 100%;
  }
  .vxe-button {
    margin: 0 10px 0 0;
  }
  .type--text {
    padding: 0;
  }
}
.sortable-tree-demo .drag-btn {
  cursor: move;
  font-size: 12px;
}
.sortable-tree-demo .vxe-body--row.sortable-ghost,
.sortable-tree-demo .vxe-body--row.sortable-chosen {
  background-color: #dfecfb;
}
</style>
