<template>
  <div>
    <mt-dialog
      ref="dialog"
      css-class="create-proj-dialog"
      :header="header"
      :buttons="buttons"
      @beforeClose="cancel"
    >
      <div class="dialog-content">
        <mt-form>
          <mt-form-item :label="$t('值集')">
            <mt-select
              :width="300"
              :data-source="dataArr"
              :show-clear-button="false"
              v-model="v"
            ></mt-select>
          </mt-form-item>
        </mt-form>
      </div>
    </mt-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      dataArr: [
        { text: this.$t('单位'), value: 'unit' },
        { text: this.$t('成本因子'), value: 'factor' }
      ],
      v: 'unit',
      editStatus: false,
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.success,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    success() {
      this.$emit('confirm-function', { calculationFormula: this.v })
      this.hide()
    },
    error() {
      this.$toast({ type: 'error', content: this.$t('公式错误') })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  padding-top: 20px;
}
</style>
