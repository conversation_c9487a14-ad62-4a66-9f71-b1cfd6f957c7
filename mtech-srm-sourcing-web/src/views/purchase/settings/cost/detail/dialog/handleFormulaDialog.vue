<template>
  <div class="formula-dialog">
    <mt-dialog ref="dialog" css-class="create-proj-dialog" :header="header" @beforeClose="cancel">
      <div class="dialog-content">
        <Calculator
          :item-list="items"
          :cal-val="calVal"
          :show-func-select="showFuncSelect"
          :is-sort-item-list="isSortItemList"
          :is-validate="isValidate"
          @success="success"
          @error="error"
        />
      </div>
    </mt-dialog>
  </div>
</template>
<script>
import Calculator from 'COMPONENTS/Calculator'
export default {
  components: {
    Calculator
  },
  data() {
    return {
      items: this.modalData.items,
      calVal: this.modalData.calVal,
      editStatus: false,
      showFuncSelect: this.modalData.showFuncSelect === false ? false : true,
      isSortItemList: this.modalData.isSortItemList === false ? false : true,
      isValidate: this.modalData.isValidate === false ? false : true
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    success(formula) {
      this.$emit('confirm-function', { calculationFormula: formula })
    },
    error() {
      this.$toast({ type: 'error', content: this.$t('公式错误') })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  padding-top: 20px;
}
</style>
