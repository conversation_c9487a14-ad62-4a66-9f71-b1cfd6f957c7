<template>
  <div>
    <mt-dialog
      ref="dialogRef"
      css-class="create-proj-dialog"
      :buttons="buttons"
      :header="header"
      @beforeClose="cancel"
    >
      <div class="dialog-content">
        <mt-form ref="formRef" :model="formModel" :rules="rules">
          <mt-form-item prop="fieldCode" :label="$t('字段编码')" label-style="top">
            <mt-input
              v-model="formModel.fieldCode"
              :show-clear-button="true"
              :placeholder="$t('请输入字段编码')"
            />
          </mt-form-item>
          <mt-form-item prop="fieldName" :label="$t('字段名称')" label-style="top">
            <mt-input
              v-model="formModel.fieldName"
              :show-clear-button="true"
              :placeholder="$t('请输入字段名称')"
            />
          </mt-form-item>
          <mt-form-item prop="fieldAlias" :label="$t('字段别名')" label-style="top">
            <mt-input
              v-model="formModel.fieldAlias"
              :show-clear-button="true"
              :disabled="modalData.type === 'edit'"
              :placeholder="$t('请输入字段别名')"
            />
          </mt-form-item>
          <mt-form-item prop="costModelType" :label="$t('模板类型')" label-style="top">
            <mt-select
              v-model="formModel.costModelType"
              css-class="rule-element"
              :data-source="costModelTypeList"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择模板类型')"
            />
          </mt-form-item>
          <mt-form-item prop="classifyCode" :label="$t('分类')" label-style="top">
            <mt-select
              v-model="formModel.classifyCode"
              css-class="rule-element"
              :data-source="classifyList"
              :fields="{ text: 'itemName', value: 'itemCode' }"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择分类')"
            />
          </mt-form-item>
          <mt-form-item prop="columnAttribute" :label="$t('组件属性')" label-style="top">
            <mt-input
              v-model="formModel.columnAttribute"
              :show-clear-button="true"
              :placeholder="$t('请输入组件属性')"
            />
          </mt-form-item>
          <mt-form-item prop="columnType" :label="$t('组件类型')" label-style="top">
            <mt-select
              v-model="formModel.columnType"
              css-class="rule-element"
              :data-source="columnTypeList"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择组件类型')"
              @change="handleColumnTypeChange"
            />
          </mt-form-item>
          <mt-form-item prop="valueSet" :label="$t('值集')" label-style="top">
            <mt-select
              v-model="formModel.valueSet"
              css-class="rule-element"
              :data-source="valueSetList"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择值集')"
            />
          </mt-form-item>
          <mt-form-item prop="sortValue" :label="$t('排序')" label-style="top">
            <mt-input-number
              :min="0"
              precision="0"
              v-model="formModel.sortValue"
              :placeholder="$t('请输入排序')"
            />
          </mt-form-item>
          <mt-form-item prop="costFactorCode" :label="$t('成本因子编码')" label-style="top">
            <div class="factor-input">
              <mt-input
                v-model="formModel.costFactorCode"
                disabled
                :placeholder="$t('请输入成本因子编码')"
              />
              <mt-icon
                class="factor-input-icon"
                name="icon_list_refuse"
                @click.native="hanldeClearCostFactor"
              />
              <mt-icon
                class="factor-input-icon"
                name="icon_input_search"
                @click.native="hanldeSelectCostFactor"
              />
            </div>
          </mt-form-item>
          <mt-form-item prop="calculationFormulaSpec" :label="$t('计算公式')" label-style="top">
            <mt-input
              v-model="formModel.calculationFormulaSpec"
              :show-clear-button="true"
              :placeholder="$t('请输入计算公式')"
            />
          </mt-form-item>
        </mt-form>
      </div>
    </mt-dialog>
  </div>
</template>
<script>
import selectAllCostFactorCodeDailog from 'COMPONENTS/NormalEdit/selectAllCostFactorCode/components/selectGrid/index.vue' // 成本因子
import { costModelTypeList, columnTypeList, valueSetList, rules } from './config/index'

export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formModel: {},
      costModelTypeList,
      classifyList: [],
      columnTypeList,
      valueSetList,
      rules: {}
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    // 初始化
    async init() {
      this.getClassifyList()
      if (this.modalData.type === 'edit') {
        this.formModel = {
          ...this.modalData.data
        }
      }
      this.$refs.dialogRef.ejsRef.show()
      this.$nextTick(() => {
        this.rules = rules(this)
      })
    },
    // 获取品类列表
    async getClassifyList() {
      const res = await this.$API.masterData.dictionaryGetList({
        dictCode: 'COST_MODEL_BASE_FIELD_CLASSIFY_CODE'
      })
      if (res.code === 200) {
        this.classifyList = res.data || []
      }
    },
    // 选择组件类型
    handleColumnTypeChange(e) {
      this.$set(this.formModel, 'columnType', e.itemData?.value)
      this.rules = rules(this)
    },
    // 选择成本因子编码
    hanldeSelectCostFactor() {
      this.$dialog({
        modal: selectAllCostFactorCodeDailog,
        data: {
          title: this.$t('值集选择')
        },
        success: (res) => {
          this.$set(this.formModel, 'costFactorCode', res[0].itemCode)
        }
      })
    },
    // 清除成本因子编码
    hanldeClearCostFactor() {
      this.$set(this.formModel, 'costFactorCode', '')
    },
    // 确定
    confirm() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          const params = [{ ...this.formModel }]
          this.$API.constModelFieldConfig.saveCmff(params).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('保存成功！'), type: 'success' })
              this.$emit('confirm-function')
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  padding-top: 20px;
}
.factor-input {
  display: flex;
  .mt-input {
    width: 90%;
    margin-right: 10px;
  }
  .factor-input-icon {
    margin-left: 5px;
    line-height: 35px !important;
  }
}
</style>
