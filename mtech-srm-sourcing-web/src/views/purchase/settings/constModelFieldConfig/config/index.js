import Vue from 'vue'
import { i18n } from '@/main.js'

export const statusList = [
  { value: 0, label: i18n.t('停用'), cssClass: 'title-#9baac1' },
  { value: 1, label: i18n.t('启用'), cssClass: 'title-#6386c1' }
]

// 模板类型
export const costModelTypeList = [
  { text: i18n.t('成本模型'), value: 1, cssClass: '' },
  { text: i18n.t('核价模型'), value: 2, cssClass: '' }
]

// 组件类型
export const columnTypeList = [
  { text: i18n.t('数值'), value: 0, cssClass: '' },
  { text: i18n.t('文本'), value: 1, cssClass: '' },
  { text: i18n.t('值集'), value: 2, cssClass: '' }
]

// 值集列表
export const valueSetList = [
  { text: i18n.t('单位'), value: 'unit', cssClass: '' },
  { text: i18n.t('物料'), value: 'item', cssClass: '' },
  { text: i18n.t('成本因子'), value: 'cost_factor', cssClass: '' },
  { text: i18n.t('定额'), value: 'quota_no', cssClass: '' },
  { text: i18n.t('基价'), value: 'base_price', cssClass: '' },
  { text: i18n.t('条件基价'), value: 'conditions_base_price', cssClass: '' },
  { text: i18n.t('物料定额'), value: 'item_quota_no', cssClass: '' },
  { text: i18n.t('成本因子定额'), value: 'cost_factor_quota_no', cssClass: '' }
]

const columnData = [
  {
    width: 50,
    type: 'checkbox'
  },
  {
    field: 'lineIndexComponent',
    headerText: i18n.t('行号'),
    allowFiltering: false, // 序号列，不参与数据筛选
    allowResizing: false, // 序号列，不参与列顺序变化
    allowSorting: false, // 序号列，不参与列宽变化
    ignore: true, // 序号列，不参与数据筛选
    width: 80,
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: '<div>{{+data.index+1}}</div>',
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    width: 150,
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: statusList,
      fields: { text: 'label', value: 'value' }
    },
    cellTools: [
      'edit',
      'delete',
      {
        id: 'enable',
        title: i18n.t('启用'),
        visibleCondition: (data) => {
          return data['status'] !== 1
        }
      },
      {
        id: 'disable',
        title: i18n.t('禁用'),
        visibleCondition: (data) => {
          return data['status'] === 1
        }
      }
    ]
  },
  {
    field: 'fieldCode',
    headerText: i18n.t('字段编码')
  },
  {
    field: 'fieldKey',
    headerText: i18n.t('字段Key')
  },
  {
    field: 'fieldName',
    headerText: i18n.t('字段名称')
  },
  {
    field: 'fieldAlias',
    headerText: i18n.t('字段别名')
  },
  {
    field: 'costModelType',
    headerText: i18n.t('模板类型'),
    width: 100,
    valueConverter: {
      type: 'map',
      map: costModelTypeList
    }
  },
  {
    field: 'classifyCode',
    headerText: i18n.t('分类编码')
  },
  {
    field: 'classifyName',
    headerText: i18n.t('分类名称')
  },
  {
    field: 'columnAttribute',
    headerText: i18n.t('组件属性')
  },
  {
    field: 'columnType',
    headerText: i18n.t('组件类型'),
    width: 100,
    valueConverter: {
      type: 'map',
      map: columnTypeList
    }
  },
  {
    field: 'valueSet',
    headerText: i18n.t('值集'),
    width: 100,
    valueConverter: {
      type: 'map',
      map: [...valueSetList, { text: '-', value: '', cssClass: '' }]
    }
  },
  {
    field: 'sortValue',
    headerText: i18n.t('排序'),
    width: 80
  },
  {
    field: 'costFactorCode',
    headerText: i18n.t('成本因子编码')
  },
  {
    field: 'calculationFormulaSpec',
    headerText: i18n.t('计算公式')
  }
]

const toolbar = [
  { id: 'add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'delete', icon: 'icon_solid_edit', title: i18n.t('删除') },
  { id: 'enable', icon: 'icon_solid_Createorder', title: i18n.t('启用') },
  { id: 'disable', icon: 'icon_solid_Cancel', title: i18n.t('禁用') }
]

export const pageConfig = [
  {
    gridId: '7f985542-4820-4d84-82f3-c8210ea8a966',
    toolbar,
    grid: {
      columnData,
      asyncConfig: {
        url: '/sourcing/tenant/costModel/baseField/queryBuilder'
      }
    }
  }
]

export const rules = (that) => {
  return {
    fieldCode: [{ required: true, message: i18n.t('请输入字段编码') }],
    fieldName: [{ required: true, message: i18n.t('请输入字段名称') }],
    costModelType: [{ required: true, message: i18n.t('请选择模板类型') }],
    classifyCode: [{ required: true, message: i18n.t('请选择分类') }],
    columnType: [{ required: true, message: i18n.t('请选择组件类型') }],
    valueSet: [{ required: that.formModel.columnType === 2, message: i18n.t('请选择值集') }]
  }
}
