<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    />
  </div>
</template>

<script>
import { pageConfig } from './config/index'

export default {
  data() {
    return {
      pageConfig
    }
  },
  methods: {
    handleClickCellTitle(e) {
      if (e.field == 'templateCode') {
        this.redirectConfigDetail(e.data)
      }
    },
    // 点击按钮工具栏
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const selectedRecords = grid.getSelectedRecords()
      if (selectedRecords.length === 0 && toolbar.id !== 'add') {
        this.$toast({ content: this.$t('请先至少选择一行数据'), type: 'warning' })
        return
      }
      switch (toolbar.id) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
        case 'enable':
        case 'disable':
          this.handleOperateData(toolbar.id, selectedRecords)
          break
        default:
          break
      }
    },
    // 点击单元格工具栏事件
    handleClickCellTool(e) {
      const { tool, data } = e
      switch (tool.id) {
        case 'edit':
          this.handleEdit(data)
          break
        case 'delete':
        case 'enable':
        case 'disable':
          this.handleOperateData(tool.id, [data])
          break
        default:
          break
      }
    },
    // 新增
    handleAdd() {
      this.$dialog({
        modal: () => import('./add.vue'),
        data: {
          title: this.$t('新增成本模型字段'),
          type: 'add'
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 编辑
    handleEdit(data) {
      this.$dialog({
        modal: () => import('./add.vue'),
        data: {
          title: this.$t('编辑成本模型字段'),
          type: 'edit',
          data
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 删除、启用、禁用
    handleOperateData(type, list) {
      const idList = []
      list.forEach((item) => {
        idList.push(item.id)
      })
      const titleMap = {
        delete: this.$t('删除'),
        enable: this.$t('启用'),
        disable: this.$t('禁用')
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认${titleMap[type]}数据？`)
        },
        success: () => {
          this.$loading()
          this.$API.constModelFieldConfig[type + 'Cmff']({ ids: idList })
            .then(() => {
              this.$toast({ content: this.$t(`${titleMap[type]}成功！`), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
              this.$hloading()
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  /deep/.e-headercelldiv {
    padding: 0 0.6em 0 0.6em !important;
  }
}
</style>
