// 修改审批流弹窗
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="approvalObject" :rules="formRules">
        <mt-form-item :label="$t('业务类型：')">
          <mt-input
            :disabled="true"
            :show-clear-button="false"
            v-model="approvalObject.businessTypeName"
            float-label-type="Never"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="enableStatus" :label="$t('是否启用审批：')">
          <mt-switch
            v-model="approvalObject.useApproval"
            :on-label="$t('启用')"
            :off-label="$t('停用')"
          ></mt-switch>
        </mt-form-item>
        <mt-form-item :label="$t('审批节点名称：')">
          <mt-input
            :disabled="true"
            :show-clear-button="false"
            v-model="approvalObject.operationTypeName"
            float-label-type="Never"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('关联工作流：')">
          <mt-input
            :disabled="true"
            :show-clear-button="false"
            v-model="approvalObject.processName"
            float-label-type="Never"
          ></mt-input>
        </mt-form-item>
        <mt-form-item class="process-desc" prop="processDesc" :label="$t('审批节点描述：')">
          <mt-input
            :multiline="true"
            :rows="3"
            type="text"
            float-label-type="Never"
            v-model="approvalObject.processDesc"
            :placeholder="$t('请输入审批节点描述')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      approvalObject: {
        configId: 0, //配置Id
        enableStatus: 1, //是否启用 1启用 2 停用
        useApproval: false,
        operationType: '', //	操作类型
        operationTypeName: '', //	操作类型名称
        processDesc: '', //审批描述
        processDefId: '', //流程定义Id
        processDefKey: '', //流程Key
        processGroup: '', //流程分组
        processGroupId: '',
        processName: '', //审批流程名称
        version: 0 //	版本号
      },
      editStatus: false,
      formRules: {}
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      let _data = this.modalData.data
      this.approvalObject = {
        configId: _data.id, //配置Id
        enableStatus: _data.enableStatus, //是否启用 1启用 2 停用
        useApproval: _data.enableStatus == 1,
        businessTypeId: _data.businessTypeId, //业务类型
        businessTypeCode: _data.businessTypeCode,
        businessTypeName: _data.businessTypeName,
        operationType: _data.operationType, //	操作类型
        operationTypeName: _data.operationTypeName, //操作类型名称
        processDesc: _data.processDesc, //审批描述
        processDefId: _data.processDefId, //流程定义Id
        processDefKey: _data.processDefKey, //流程Key
        processGroup: _data.processGroup, //流程分组
        processName: _data.processName, //审批流程名称
        version: _data.version //	版本号
      }
    }
    this.getFormValidRules('formRules', this.$API.approvalConfig.saveApprovalConfigValid)
  },
  methods: {
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = JSON.parse(JSON.stringify(this.approvalObject))
          params.enableStatus = params.useApproval ? 1 : 2
          delete params.useApproval
          delete params.operationTypeName
          if (!this.editStatus) {
            delete params.configId
            delete params.version
          }
          this.$API.approvalConfig.saveApprovalConfig(params).then((res) => {
            if (res.code == 200) {
              this.$emit('confirm-function')
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/ span.e-input-group {
    padding: 0;
  }
  /deep/ .process-desc {
    width: 820px !important;
  }
}
</style>
