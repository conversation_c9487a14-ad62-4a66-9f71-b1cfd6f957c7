// 创建审批流弹窗
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="approvalObject" :rules="formRules">
        <mt-form-item :label="$t('请选择业务类型')"
          ><mt-select
            css-class="rule-element"
            ref="businessTypeRef"
            v-model="approvalObject.businessTypeId"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="businessTypeList"
            :fields="{ text: 'itemName', value: 'id' }"
            :placeholder="$t('请选择业务类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="enableStatus" :label="$t('是否启用审批：')">
          <mt-switch
            v-model="approvalObject.useApproval"
            :on-label="$t('启用')"
            :off-label="$t('停用')"
          ></mt-switch>
        </mt-form-item>
        <mt-form-item prop="operationType" :label="$t('审批节点名称：')">
          <mt-select
            v-model="approvalObject.operationType"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="approvalList"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('请选择审批节点名称')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          prop="processName"
          :label="$t('关联工作流：')"
          v-if="flowGroupFields.dataSource.length"
        >
          <mt-input
            :readonly="true"
            :show-clear-button="false"
            v-model="approvalObject.processName"
            float-label-type="Never"
            @focus="focusGroupInput"
          ></mt-input>
          <mt-treeView
            v-show="showTreeView"
            class="tree-view-container"
            ref="innerTreeView"
            :fields="flowGroupFields"
            @nodeSelected="nodeSelected"
            @nodeClicked="nodeClicked"
          ></mt-treeView>
        </mt-form-item>
        <mt-form-item class="process-desc" prop="processDesc" :label="$t('审批节点描述：')">
          <mt-input
            :multiline="true"
            :rows="3"
            type="text"
            float-label-type="Never"
            v-model="approvalObject.processDesc"
            :placeholder="$t('请输入审批节点描述')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      approvalObject: {
        configId: 0, //配置Id
        enableStatus: 1, //是否启用 1启用 2 停用
        useApproval: true,
        businessTypeId: null, //业务类型
        businessTypeCode: null,
        businessTypeName: null,
        operationType: '', //	操作类型
        processDesc: '', //审批描述
        processDefId: '', //流程定义Id
        processDefKey: '', //流程Key
        processGroup: '', //流程分组
        processName: '', //审批流程名称
        version: 0 //	版本号
      },
      formRules: {},
      approvalList: [], //审批流节点类型列表
      statusList: [
        { text: this.$t('启用'), value: 1 },
        { text: this.$t('停用'), value: 2 }
      ],
      editStatus: false,
      flowGroupFields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'subChild'
      },
      selectedNodeSet: new Set(),
      currentTemplateNode: null,
      showTreeView: false,
      businessTypeList: []
    }
  },
  props: {
    modalData: {
      //弹框传入的数据 在此接收
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        //结构modalData
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      //结构标题弹框
      return this.modalData.title
    }
  },
  mounted() {
    //显示弹框
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      //如果传入了data 那么就标记为编辑状态
      this.editStatus = true
    }
    this.getFlowOperationTypes()
    this.getTenantCategoryTree()
    this.getBusinessTypeList()
    this.getFormValidRules('formRules', this.$API.approvalConfig.saveApprovalConfigValid)
  },
  methods: {
    getBusinessTypeList() {
      // 业务类型 - businessType
      this.$API.masterData
        .dictionaryGetList({
          dictCode: 'businessType'
        })
        .then((res) => {
          this.businessTypeList = res.data
        })
    },
    focusGroupInput() {
      this.showTreeView = true
    },
    // 判断是否模板节点, true: 是
    checkTemplateNode(treeRef, id) {
      const treeNodes = treeRef.ejsInstances.getTreeData(id)
      return treeNodes.length && treeNodes[0].categoryName
    },
    // 先触发 nodeSelected , 然后触发nodeClicked 事件， 这里只处理叶子节点
    nodeClicked() {
      if (this.currentTemplateNode) {
        const { categoryName, id, key, name } = this.currentTemplateNode
        this.approvalObject.processGroup = categoryName //流程分组
        this.approvalObject.processDefId = id //流程定义Id
        this.approvalObject.processDefKey = key //流程Key
        this.approvalObject.processName = name //审批流程名称
        this.showTreeView = false
      }
    },
    // 模板树的选中事件，在其中调用接口，获取数据， 忽略叶子节点
    nodeSelected(e) {
      const { nodeData } = e
      this.currentTemplateNode = undefined // 每次事件触发先，先将最近选中的模板置空
      this.approvalObject.processGroup = null //流程分组
      this.approvalObject.processDefId = null //流程定义Id
      this.approvalObject.processDefKey = null //流程Key
      this.approvalObject.processName = null //审批流程名称

      if (this.selectedNodeSet.has(nodeData.id)) {
        return
      }
      const treeRef = this.$refs.innerTreeView
      if (this.checkTemplateNode(treeRef, nodeData.id)) {
        this.currentTemplateNode = treeRef.ejsInstances.getTreeData(nodeData.id)[0]
      } else {
        this.getTemplateByCategory(nodeData, treeRef)
      }
    },
    // 获取分类下的所有模板
    getTemplateByCategory(nodeData, treeRef) {
      let _params = {
        category: nodeData.id,
        current: 1,
        size: 100,
        type: 'ProcessModel',
        key: ''
      }
      this.$API.workFlowService.getTemplatePage(_params).then((res) => {
        let _records = [...res.data.records]
        _records.forEach((e) => {
          e.categoryName = nodeData.text
        })
        treeRef.ejsInstances.addNodes([..._records], nodeData.id)
        this.selectedNodeSet.add(nodeData.id)
      })
    },

    //获取审批流结点类型列表
    getFlowOperationTypes() {
      this.$API.commonConfig.getFlowOperationTypes().then((res) => {
        this.approvalList = res.data
      })
    },
    //获取关联工作流-列表
    getTenantCategoryTree() {
      this.$API.workFlowService.getTenantCategoryTree().then((res) => {
        this.$set(this.flowGroupFields, 'dataSource', res.data)
      })
    },
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = { ...this.approvalObject }
          params.enableStatus = params.useApproval ? 1 : 2
          delete params.useApproval
          if (!this.editStatus) {
            delete params.configId
            delete params.version
          }
          //处理下拉框数据赋值
          this.$utils.assignDataFromRefs(params, [
            {
              key: 'businessTypeId', //业务类型 businessTypeId下拉框数据
              ref: this.$refs.businessTypeRef.ejsRef,
              fields: {
                businessTypeCode: 'itemCode',
                businessTypeName: 'itemName'
              }
            }
          ])
          this.$API.approvalConfig.saveApprovalConfig(params).then((res) => {
            if (res.code == 200) {
              this.$emit('confirm-function')
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/ span.e-input-group {
    padding: 0;
  }
  /deep/ .process-desc {
    width: 820px !important;
  }
  /deep/ .tree-view-container {
    box-shadow: inset 0 0 0 1px rgba(232, 232, 232, 1);
    width: 100%;
    position: absolute;
    left: 0;
    top: 50px;
    z-index: 2;
    background: #fff;
    height: 200px;
    overflow: auto;
  }
}
</style>
