<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'

export default {
  data() {
    return {
      pageConfig: pageConfig(
        this.$API.approvalConfig.getApprovalConfigList,
        this.changeApprovalStatus
      )
    }
  },
  methods: {
    //表格按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.grid.getSelectedRecords()
      console.log('use-handleClickToolBar', e, _selectGridRecords)

      if (_selectGridRecords.length <= 0 && (e.toolbar.id == 'Delete' || e.toolbar.id == 'Edit')) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      if (e.toolbar.id == 'Add') {
        this.handleAddApprovalConfig()
      } else if (e.toolbar.id == 'Delete') {
        this.handleBatchDelete(_selectGridRecords)
      } else if (e.toolbar.id == 'Edit') {
        if (_selectGridRecords.length > 1) {
          this.$toast({
            content: this.$t('只支持修改一行数据'),
            type: 'warning'
          })
        } else {
          this.handleEditApprovalConfig(_selectGridRecords[0])
        }
      }
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      if (e.tool.id == 'edit') {
        this.handleEditApprovalConfig(e.data)
      } else if (e.tool.id == 'delete') {
        this.handleDeleteConfig([e.data.configId])
      }
    },
    //单元格标题点击操作
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
    },
    //批量删除操作
    handleBatchDelete(_selectGridRecords) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.configId)
      })
      this.handleDeleteConfig(_selectIds)
    },
    //新增配置
    handleAddApprovalConfig() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/settings/rfx/approval/components/approvalDialog" */ './components/approvalDialog.vue'
          ),
        data: {
          title: this.$t('新增审批流')
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //编辑配置
    handleEditApprovalConfig(data) {
      if (data && data.configId) {
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "router/purchase/settings/rfx/approval/components/editApprovalDialog" */ './components/editApprovalDialog.vue'
            ),
          data: {
            title: this.$t('编辑审批流'),
            data: data
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    },
    //更新配置的状态(行内操作)
    changeApprovalStatus(e, data) {
      let _params = { id: data.configId, enableStatus: e ? 1 : 2 }
      this.$API.approvalConfig.updateApprovalConfig(_params).then(() => {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },
    //删除规则
    handleDeleteConfig(ids) {
      let _params = {
        configIds: ids.join(',')
      }
      this.$dialog({
        data: {
          title: this.$t('删除审批流'),
          message: this.$t(
            '删除工作流将默认该节点工作流不启用，在相应节点则会默认审批自动通过。请慎重操作'
          )
        },
        success: () => {
          this.$API.approvalConfig.deleteApprovalConfig(_params).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    }
  }
}
</script>
