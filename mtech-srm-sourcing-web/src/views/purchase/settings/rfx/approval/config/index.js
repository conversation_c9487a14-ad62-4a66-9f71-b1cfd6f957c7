import { i18n } from '@/main.js'
import Vue from 'vue'
const approvalListToolBar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'Edit', icon: 'icon_solid_edit', title: i18n.t('修改') },
  { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') }
]

const approvalListColumnData = (changeApprovalStatus = () => {}) => [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
  },
  {
    field: 'processName',
    headerText: i18n.t('审批节点名称'),
    cellTools: ['edit', 'delete']
  },
  {
    field: 'processDesc',
    headerText: i18n.t('审批节点描述')
  },
  {
    field: 'operationTypeName',
    headerText: i18n.t('关联审批流')
  },
  {
    field: 'enableStatus',
    headerText: i18n.t('是否启用审批'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<mt-switch
                      v-model="useApproval"
                      :on-label="$t('启用')"
                      :off-label="$t('停用')"
                      @change="changeApproval"
                    ></mt-switch>`,
          data() {
            return { data: {} }
          },
          computed: {
            useApproval: {
              get() {
                // { type: "map", map: { 1: i18n.t("启用"), 2: i18n.t("停用") } },
                return this.data.enableStatus == 1
              },
              set() {}
            }
          },
          methods: {
            changeApproval(e) {
              changeApprovalStatus(e, this.data)
            }
          }
        })
      }
    }
  },
  {
    field: 'createTime',
    headerText: i18n.t('更新时间'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'createUserName',
    headerText: i18n.t('更新人')
  }
]

export const pageConfig = (url, func) => [
  {
    toolbar: approvalListToolBar,
    grid: {
      allowFiltering: true,
      columnData: approvalListColumnData(func),
      asyncConfig: {
        url
      }
    }
  }
]
