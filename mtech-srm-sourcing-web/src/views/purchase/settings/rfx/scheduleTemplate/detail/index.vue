<template>
  <div class="full-height schedule-template-detail mt-flex-direction-column">
    <div class="top-info mt-flex">
      <div class="name-wrap">
        <div class="first-line">
          <div class="code">
            <input
              class="strategy-name"
              type="text"
              disabled
              v-model="scheduleTemplateInfo.templateName"
              :placeholder="$t('请输入模板名称')"
            />
          </div>
          <div class="btns-wrap">
            <mt-button @click="$router.go(-1)">{{ $t('取消') }}</mt-button>
          </div>
        </div>
        <div class="second-line">
          <div class="cai-name">
            <input
              class="strategy-remark"
              type="text"
              disabled
              v-model="scheduleTemplateInfo.templateCode"
              :placeholder="$t('请输入模板编号')"
            />
          </div>

          <div class="cai-names">
            <input
              class="strategy-remark"
              type="text"
              disabled
              v-model="scheduleTemplateInfo.remark"
              :placeholder="$t('请输入模板描述')"
            />
          </div>
          <ul class="labels">
            <li>
              <mt-icon name="icon_Creator"></mt-icon>
              <span>{{ $t('创建人：') }}{{ scheduleTemplateInfo.createUserName }}</span>
            </li>
            <li>
              <mt-icon name="icon_Company"></mt-icon>
              <span>{{ $t('创建时间：') }}{{ scheduleTemplateInfo.createTime }}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="schedule-container mt-flex">
      <div
        v-for="(phase, index) in phaseDataList"
        :key="phase.planPhase"
        class="schedule-item mt-flex-direction-column"
      >
        <div class="item-title mt-flex">
          <span class="title">{{ phase.phaseName }}</span>
          <span
            class="option"
            :tabIndex="index"
            @click="openOptionList(index)"
            @blur="blurOptionList(index)"
            >...
            <div class="card-option" v-show="phase.showOption">
              <div class="option-item option-edit" @click="editPhase(phase)">
                {{ $t('编辑') }}
              </div>
              <div class="option-item option-delete" @click="deletePhase(phase)">
                {{ $t('删除') }}
              </div>
            </div>
          </span>
        </div>
        <div
          class="item-content mt-flex-direction-column"
          :id="'schedule-container-' + phase.id"
          :tabIndex="index"
          :phaseName="phase.phaseName"
          :phaseId="phase.id"
        >
          <schedule-card
            v-for="task in phase.planTemplateItemResponseList"
            :key="task.id"
            :tab-index="index"
            @click="activeScheduleId = task.id"
            :card-data="task"
            :active="task.id == activeScheduleId"
            @handleClickNode="editPlan($event, phase)"
            @handleClickTask="clickTaskNode($event, phase)"
          ></schedule-card>
        </div>
        <div class="add-card mt-flex" @click="createPlan(phase)">
          <mt-icon name="icon_Close_1"></mt-icon>
        </div>
      </div>
      <div class="schedule-item mt-flex-direction-column empty-item">
        <div class="item-title mt-flex">
          <span class="title">{{ $t('待新建阶段') }}</span>
          <span class="option" @click="createPhase">...</span>
        </div>
        <div class="item-content mt-flex-direction-column">
          <div class="add-card mt-flex" @click="createPhase">
            <mt-icon name="icon_Close_1"></mt-icon>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import ScheduleCard from './components/ScheduleCard.vue'
import Sortable from 'sortablejs'
export default {
  components: { ScheduleCard },
  data() {
    return {
      phaseDataList: [],
      scheduleTemplateInfo: {}
    }
  },
  mounted() {
    this.scheduleTemplateInfo = localStorage?.scheduleTemplateInfo
      ? JSON.parse(localStorage.scheduleTemplateInfo)
      : {}
    this.resetListAndSortable()
  },
  methods: {
    async resetListAndSortable() {
      await this.planPhaseListGet()
      let _this = this
      this.$nextTick(() => {
        this.phaseDataList.map((e) => {
          if (document.getElementById(`schedule-container-${e.id}`)) {
            Sortable.create(document.getElementById(`schedule-container-${e.id}`), {
              handle: '.card-header',
              group: 'schedule',
              disabled: false,
              // 元素从一个列表拖拽到另一个列表
              onAdd: function (/**Event*/ { oldIndex, newIndex, item, from, to }) {
                let _fromList = [
                  ..._this.phaseDataList[from.tabIndex]['planTemplateItemResponseList']
                ]
                let _toList = [..._this.phaseDataList[to.tabIndex]['planTemplateItemResponseList']]
                let _toPhase = { ..._this.phaseDataList[to.tabIndex] }
                const targetRow = _fromList.splice(oldIndex, 1)[0]
                _toList.splice(newIndex, 0, targetRow)
                let _currentIndex = _toList.findIndex((e) => {
                  return e.id === item.id
                })
                let _plan = _toList[_currentIndex]
                let _frontId = _currentIndex === 0 ? 0 : _toList[_currentIndex - 1]['id']
                let _nextId =
                  _currentIndex === _toList.length - 1 ? 100 : _toList[_currentIndex + 1]['id']
                _plan.beforePlanId = _frontId
                _plan.afterPlanId = _nextId
                _plan.planPhaseId = _toPhase.id //	计划阶段id
                _plan.planPhaseName = _toPhase.phaseName //	计划阶段名称
                _this.handleSortPlanList(_plan)
              },

              // 列表内元素顺序更新的时候触发
              onUpdate: function (/**Event*/ { oldIndex, newIndex, item }) {
                let _list = [..._this.phaseDataList[item.tabIndex]['planTemplateItemResponseList']]
                const targetRow = _list.splice(oldIndex, 1)[0]
                _list.splice(newIndex, 0, targetRow)
                let _currentIndex = _list.findIndex((e) => {
                  return e.id === item.id
                })
                let _plan = _list[_currentIndex]
                let _frontId = _currentIndex === 0 ? 0 : _list[_currentIndex - 1]['id']
                let _nextId =
                  _currentIndex === _list.length - 1 ? 100 : _list[_currentIndex + 1]['id']
                _plan.beforePlanId = _frontId
                _plan.afterPlanId = _nextId
                _this.handleSortPlanList(_plan)
              }
            })
          }
        })
      })
    },
    async planPhaseListGet() {
      const query = { id: this.$route.query.configId }
      console.log(query)
      this.$store.commit('startLoading')
      await this.$API.scheduleConfig
        .querySourcingPlanTemplateItemByTemplateId(query)
        .then((res) => {
          // console.log(res);
          this.$store.commit('endLoading')
          if (Array.isArray(res?.data) && res.data.length > 0) {
            //返回的任务计划列表，存在数据 planResponseList
            let _phase = res.data[0]
            if (
              Array.isArray(_phase?.planTemplateItemResponseList) &&
              _phase.planTemplateItemResponseList.length > 0
            ) {
              this.activeScheduleId = _phase.planTemplateItemResponseList[0].id
            }

            this.phaseDataList = res.data.map((e) => {
              e.showOption = false
              return e
            })
          } else {
            this.activeScheduleId = null
            this.phaseDataList = []
          }
          // console.log(this.phaseDataList);
        })
    },
    openOptionList(i) {
      this.phaseDataList[i]['showOption'] = true
    },
    blurOptionList(i) {
      this.phaseDataList[i]['showOption'] = false
    },
    createPhase(phase) {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/rfx/detail/tabs/schedule/components/phaseDialog" */ './components/phaseDialog.vue'
          ),
        data: {
          title: this.$t('阶段新增'),
          phase: phase,
          templateId: this.$route.query.configId
        },
        success: () => {
          this.$toast({
            content: this.$t('成功新增阶段'),
            type: 'success'
          })
          this.planPhaseListGet()
          this.resetListAndSortable()
        }
      })
      // console.log(phase);
    },
    editPhase(phase) {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/rfx/detail/tabs/schedule/components/phaseDialog" */ './components/phaseDialog.vue'
          ),
        data: {
          title: this.$t('阶段编辑'),
          data: phase,
          templateId: this.$route.query.configId
        },
        success: () => {
          this.$toast({
            content: this.$t('成功编辑阶段'),
            type: 'success'
          })
          this.planPhaseListGet()
        }
      })
    },
    deletePhase(phase) {
      this.$dialog({
        data: {
          title: this.$t('删除阶段'),
          phase: phase,
          message: this.$t('删除阶段将清空该阶段下的所有任务，请谨慎操作')
        },
        success: () => {
          this.$API.scheduleConfig
            .deleteSourcingPlanTemplatePhaseById({ idList: [phase.id] })
            .then(() => {
              this.$toast({
                content: this.$t('成功删除阶段'),
                type: 'success'
              })
              this.resetListAndSortable()
            })
        }
      })
    },
    createPlan(phase) {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/rfx/detail/tabs/schedule/components/planDialog" */ './components/planDialog.vue'
          ),
        data: {
          title: this.$t('新增节点'),
          id: this.$route.query.configId,
          phase: phase,
          frontPlans: this.getFrontPlanLists()
        },
        success: () => {
          this.$toast({
            content: this.$t('成功新增任务节点'),
            type: 'success'
          })
          this.planPhaseListGet()
        }
      })
    },
    editPlan(node, phase) {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/rfx/detail/tabs/schedule/components/planDialog" */ './components/planDialog.vue'
          ),
        data: {
          title: this.$t('编辑节点'),
          docId: this.$route.query.rfxId,
          phase: phase,
          data: node,
          frontPlans: this.getFrontPlanLists(node)
        },
        success: (e) => {
          this.$toast({
            content: e?.type === 'edit' ? this.$t('成功编辑任务节点') : this.$t('成功删除任务节点'),
            type: 'success'
          })
          this.planPhaseListGet()
        }
      })
    },
    clickTaskNode(node, phase) {
      console.log('click-task:', node, phase)
    },
    getFrontPlanLists(node = {}) {
      let _list = [...this.phaseDataList],
        res = []
      _list.forEach((e) => {
        if (Array.isArray(e.planTemplateItemResponseList)) {
          res = res.concat(e.planTemplateItemResponseList)
        }
      })
      if (node?.id) {
        res = res.filter((e) => {
          return e.id !== node.id
        })
      }
      return res
    },
    handleSortPlanList(plan) {
      this.$API.scheduleConfig.saveSourcingPlanTemplateItem(plan).then(() => {
        this.$toast({
          content: this.$t(`成功编辑任务节点`),
          type: 'success'
        })
        this.resetListAndSortable()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.mticon-icon {
  color: #6386c1;
  font-size: 5px;
  margin-left: -50px;
  margin-top: 5px;
}
.mticon-icons {
  color: #6386c1;
  font-size: 5px;
  margin-top: 6px;
  margin-left: -25px;
}
.schedule-template-detail {
  padding-top: 20px;
  width: 100%;
}
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.top-info {
  width: 100%;
  height: 120px;
  padding: 20px;
  background: rgba(245, 248, 251, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 0 8px 0 0;
  // display: flex;
  .name-wrap {
    flex: 1;
    .first-line {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .code {
        width: 150px;
        display: flex;

        .strategy-name {
          width: 150px;
          font-size: 16px;
          font-family: DINAlternate;
          font-weight: bold;
          color: #9a9a9a;
          outline: none;
          border: none;
          background: transparent;
          &:focus {
            outline: none;
            border: none;
            background: transparent;
          }
        }
      }
      .tags {
        font-size: 12px;
        font-family: PingFangSC;
        font-weight: 500;
        padding: 4px;
        border-radius: 2px;
        // margin-left: 25px;
        // margin-left: 10px;

        &-1 {
          color: rgba(237, 161, 51, 1);
          background: rgba(237, 161, 51, 0.1);
        }
        &-2 {
          margin-right: 10px;
          color: #6386c1;
          background: rgba(99, 134, 193, 0.1);
          margin-left: 5px;
        }
      }
    }

    .second-line {
      display: flex;
      align-items: center;
      margin-top: 30px;
      .cai-name {
        width: 150px;
        display: flex;
        .strategy-remark {
          // width: 100px;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          @extend .text-ellipsis;
          outline: none;
          border: none;
          background: transparent;
          &:focus {
            outline: none;
            border: none;
            background: transparent;
          }
        }
      }
      .cai-names {
        width: 150px;
        display: flex;
        margin-left: 50px;
        .strategy-remark {
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          @extend .text-ellipsis;
          outline: none;
          border: none;
          background: transparent;
          &:focus {
            outline: none;
            border: none;
            background: transparent;
          }
        }
      }
      .cai-desc {
        margin-left: 60px;
      }
      ul {
        display: flex;
        li {
          margin-left: 30px;
          display: flex;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(157, 170, 191, 1);
          .mt-icons {
            font-size: 12px;
          }
          span {
            vertical-align: text-bottom;
            margin-left: 4px;
            @extend .text-ellipsis;
          }
        }
      }
    }
  }

  .btns-wrap {
    /deep/ .mt-button {
      button {
        width: 76px;
        height: 34px;
        background: rgba(255, 255, 255, 1);
        border: 1px solid rgba(0, 70, 156, 0.1);
        border-radius: 4px;
        box-shadow: unset;
        padding: 0;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
      }
    }
  }
  /deep/ .mt-tabs-container {
    background: transparent;
    .tabs-arrow {
      display: none;
    }
    .tab-wrap {
      padding: 0;
      .tab-item {
        padding: 6px 10px;
        span {
          line-height: 1;
        }
      }
    }
  }
}
.strategy-common-container {
  width: 270px;
  display: inline-block;
  height: 40px;
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 0 20px;
}
/deep/ .strategy-element {
  border: none;
  border-color: transparent !important;
  position: relative;
  top: 4px;
  &:before,
  &:after {
    display: none;
  }
  .e-float-line {
    display: none;
  }
  .e-control {
    color: #292929;
    border: none;
    border-color: transparent !important;
  }
  .e-spin-down {
    position: absolute;
    right: -5px;
    top: 9px;
  }
  .e-spin-up {
    position: absolute;
    right: -1px;
    top: -4px;
  }
  .e-ddl-icon,
  .e-date-icon {
    position: absolute;
    right: 0;
    top: 3px;
  }
}
.schedule-container {
  background: #f8f8f8;
  padding: 20px;
  flex: 1;
  overflow: auto;
  .schedule-item {
    &:not(:first-of-type) {
      margin-left: 30px;
    }
    &.empty-item {
      .item-title {
        color: #9daabf;
      }
    }
    .item-title {
      flex-shrink: 0;
      height: 36px;
      padding: 0 10px;
      justify-content: space-between;
      .title {
        font-size: 16px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(41, 41, 41, 1);
      }
      .option {
        cursor: pointer;
        font-size: 16px;
        line-height: 16px;
        position: relative;
        .card-option {
          position: absolute;
          width: 68px;
          height: 76px;
          background: rgba(255, 255, 255, 1);
          border: 1px solid rgba(232, 232, 232, 1);
          border-radius: 2px;
          box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.2);
          padding: 0;
          right: 0;
          top: 20px;
          z-index: 3;
          .option-item {
            line-height: 34px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: normal;
            text-align: center;
          }
          .option-edit {
            color: rgba(41, 41, 41, 1);
          }
          .option-delete {
            color: rgba(237, 86, 51, 1);
          }
        }
      }
    }

    // .item-content {
    //   // flex: 1;
    // }
    .add-card {
      width: 300px;
      height: 30px;
      flex-shrink: 0;
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(232, 232, 232, 1);
      border-radius: 4px;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      color: #9daabf;
      cursor: pointer;
      .mt-icons {
        transform: rotate(45deg);
      }
      &:hover {
        color: #6386c1;
      }
    }
  }
}
</style>
