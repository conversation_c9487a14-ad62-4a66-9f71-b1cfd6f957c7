// 任务计划-节点
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="planObject" :rules="formRules">
        <mt-form-item prop="planPhaseName" :label="$t('阶段名称')">
          <mt-input
            :disabled="true"
            :show-clear-button="false"
            v-model="planObject.planPhaseName"
            float-label-type="Never"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="planName" :label="$t('任务名称')">
          <mt-input
            :show-clear-button="false"
            v-model="planObject.planName"
            float-label-type="Never"
            :placeholder="$t('请输入任务名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="planCode" :label="$t('任务编号')">
          <mt-input
            :show-clear-button="false"
            v-model="planObject.planCode"
            float-label-type="Never"
            :placeholder="$t('请输入任务编号')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="taskUserId" :label="$t('任务责任人')">
          <mt-select
            ref="teskUserRef"
            v-model="planObject.taskUserId"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="taskUserIdList"
            :fields="{ text: 'employeeName', value: 'id' }"
            :placeholder="$t('请选择责任人')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="linkTaskIds" :label="$t('关联任务')">
          <mt-multi-select
            ref="linkTaskRef"
            v-model="linkTaskIds"
            :data-source="linkTaskList"
            :fields="{ text: 'taskName', value: 'taskId' }"
            :placeholder="$t('请选择关联任务')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="durationDays" :label="$t('标准持续时间(天)')">
          <mt-input-number
            :min="0"
            :show-clear-button="false"
            v-model="planObject.durationDays"
            float-label-type="Never"
            :placeholder="$t('请输入标准持续时间')"
          ></mt-input-number>
        </mt-form-item>
        <!-- <mt-form-item prop="durationFrontDay" :label="$t('标准提前时间(天)')">
          <mt-input-number
            :min="0"
            :show-clear-button="false"
            v-model="planObject.durationFrontDay"
            float-label-type="Never"
            :placeholder="$t('请输入标准提前时间')"
          ></mt-input-number>
        </mt-form-item> -->
        <mt-form-item prop="frontPlanId" :label="$t('前置节点')" v-if="haveFrontPlan">
          <mt-select
            ref="frontPlanRef"
            v-model="planObject.frontPlanId"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="frontPlanList"
            :fields="{ text: 'planName', value: 'id' }"
            :placeholder="$t('请选择前置节点')"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
//todo  关联任务多选
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      planObject: {
        frontPlanId: null,
        frontPlanName: null,
        id: null,
        taskRelList: null,
        planCode: null,
        planName: null,
        planPhaseId: null,
        planPhaseName: null,
        remark: null,
        taskUserId: null,
        taskUserName: null,
        templateId: null,
        durationDays: 0
      },
      formRules: {},
      //节点状态
      nodeStatusList: [
        { text: this.$t('未开始'), value: 0 },
        { text: this.$t('进行中'), value: 1 },
        { text: this.$t('已完成'), value: 2 }
      ],
      //节点类型
      nodeTypeList: [
        { text: this.$t('任务'), value: '0' },
        { text: this.$t('里程碑'), value: '1' }
      ],
      taskUserIdList: [], //任务责任人 列表
      linkTaskIds: null, //关联任务Ids
      linkTaskList: [], //关联任务 列表
      haveFrontPlan: false, //当前，是否可以设置前置节点
      frontPlanList: [], //前置节点列表
      editStatus: false,
      phaseObject: {}
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.phaseObject = this.propsData?.phase

    if (Array.isArray(this.propsData?.frontPlans) && this.propsData.frontPlans.length > 0) {
      //如果所有阶段中，存在数据，则可以设置前置节点。前置节点，可以跨阶段。
      this.frontPlanList = this.propsData.frontPlans
      this.haveFrontPlan = true
    } else {
      this.frontPlanList = []
      this.haveFrontPlan = false
    }
    if (this.propsData?.data) {
      this.editStatus = true
      let _data = { ...this.propsData.data }
      this.planObject = {
        id: _data.id, //id
        planCode: _data.planCode, //	计划编码
        planName: _data.planName, //	计划名称
        planPhaseName: _data.planPhaseName, //计划阶段名称
        remark: _data.remark, //备注
        frontPlanId: _data.frontPlanId, //	前置计划id
        frontPlanName: _data.frontPlanName, //前置计划名称
        taskRelList: _data.taskRelList, //	计划所关联任务
        taskUserId: _data.taskUserId, //	任务责任人id
        taskUserName: _data.taskUserName, //	任务责任人id
        templateId: _data.templateId //模板id
        // linkTaskIds: _data.linkTaskIds,
      }
      //序列化关联任务列表，将对应的ID解析出来
      let _linkTasks = _data.taskRelList
      if (_linkTasks && Array.isArray(_linkTasks) && _linkTasks.length > 0) {
        let _tasks = []
        for (let i in _linkTasks) {
          _tasks.push(_linkTasks[i]['linkTaskId'])
        }
        this.linkTaskIds = _tasks
      }

      this.buttons.unshift({
        click: this.deletePlan,
        buttonModel: { content: this.$t('删除') }
      })
    } else {
      this.planObject.planPhaseId = this.phaseObject.id
      this.planObject.planPhaseName = this.phaseObject.phaseName
      this.planObject.templateId = this.phaseObject.templateId
    }
    this.getTaskUserList()
    this.getFlowTaskList()
    this.getFormValidRules('formRules', this.$API.scheduleConfig.saveSourcingPlanTemplateItemValid)
  },
  methods: {
    getTaskUserList() {
      // 获取弹窗中的人员列表
      this.$API.masterData.getUserListByTenantId({ employeeName: '' }).then((res) => {
        this.taskUserIdList = res.data
      })
    },
    getFlowTaskList() {
      // 获取弹窗中的关联任务列表
      this.$API.commonConfig.getBaseTasks().then((res) => {
        if (Array.isArray(res.data)) {
          this.linkTaskList = res.data
        } else {
          this.linkTaskList = []
        }
      })
    },
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = { ...this.planObject }
          if (!this.editStatus) {
            delete params.id
          }

          //处理下拉框数据赋值
          this.$utils.assignDataFromRefs(params, [
            {
              key: 'taskUserId', //任务责任人 taskUserId下拉框数据
              ref: this.$refs.teskUserRef.ejsRef,
              fields: {
                taskUserName: 'employeeName'
              }
            }
          ])
          if (this.frontPlanList.length) {
            this.$utils.assignDataFromRefs(params, [
              {
                key: 'frontPlanId', //前置节点 frontPlanId下拉框数据
                ref: this.$refs.frontPlanRef.ejsRef,
                fields: {
                  frontPlanName: 'planName'
                }
              }
            ])
          }

          //根据当前选择的linkTaskId，获取linkTask对象
          let _taskIds = this.linkTaskIds
          if (_taskIds && Array.isArray(_taskIds) && _taskIds.length > 0) {
            let _tasks = []
            for (let i in _taskIds) {
              let _task = this.$refs.linkTaskRef.ejsRef.getDataByValue(_taskIds[i])
              _tasks.push({
                linkTaskCode: _task.taskType,
                linkTaskId: _task.taskId,
                linkTaskName: _task.taskName
              })
            }
            params.taskRelList = _tasks
          }
          // delete params.durationFrontDay; //后端暂无此字段
          this.$API.scheduleConfig.saveSourcingPlanTemplateItem(params).then((res) => {
            if (res.code == 200) {
              this.$emit('confirm-function', { type: 'edit' })
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    deletePlan() {
      let _useCurrentAsFrontPlan = false
      if (this.haveFrontPlan && this.frontPlanList.length > 0) {
        let _fronts = [...this.frontPlanList]
        let _currentAsFrontPlan = _fronts.filter((e) => {
          return e.frontPlanId === this.planObject.id
        }) //从所有节点中，寻找将当前节点设置为前置节点的数据
        if (_currentAsFrontPlan && _currentAsFrontPlan.length > 0) {
          _useCurrentAsFrontPlan = true
        }
      }
      this.$dialog({
        data: {
          title: this.$t('删除节点'),
          message: _useCurrentAsFrontPlan
            ? this.$t('删除节点会将删除和该节点关联的信息，请确认是否继续操作？')
            : this.$t('确认删除当前节点？')
        },
        success: () => {
          this.$API.scheduleConfig
            .deleteSourcingPlanTemplateItemById({
              idList: [this.planObject.id]
            })
            .then(() => {
              this.$emit('confirm-function', { type: 'delete' })
            })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
