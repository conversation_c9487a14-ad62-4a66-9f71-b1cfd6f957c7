// 任务计划-阶段
<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="phaseName" :label="$t('阶段名称')">
          <mt-input
            v-model="formObject.phaseName"
            float-label-type="Never"
            :placeholder="$t('请输入阶段名称')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formObject: {
        id: 0,
        phaseName: '',
        templateId: 0
      },
      formRules: {},
      editStatus: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.propsData?.data) {
      this.editStatus = true
      let _data = { ...this.propsData.data }
      this.formObject = {
        id: _data.id,
        phaseName: _data.phaseName,
        templateId: _data.templateId
      }
    }
    if (this.propsData?.templateId) {
      this.formObject.templateId = this.propsData?.templateId
      console.log(this.formObject.templateId)
    }
    this.getFormValidRules('formRules', this.$API.scheduleConfig.saveSourcingPlanTemplatePhaseValid)
  },
  methods: {
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = { ...this.formObject }
          if (!this.editStatus) {
            delete params.id
          }
          this.$API.scheduleConfig.saveSourcingPlanTemplatePhase(params).then((res) => {
            if (res.code == 200) {
              this.$emit('confirm-function')
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
