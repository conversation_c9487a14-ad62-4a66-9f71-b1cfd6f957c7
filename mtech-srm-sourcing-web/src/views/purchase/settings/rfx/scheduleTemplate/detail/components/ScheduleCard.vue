<template>
  <div
    :class="['schedule-card', 'mt-flex-direction-column', { active: active }]"
    :tabIndex="tabIndex"
    :id="card.id"
  >
    <div class="card-header mt-flex-direction-column">
      <div class="header-title mt-flex">
        <div class="header-name mt-flex-direction-column">
          <span class="name">{{ card.planName }}</span>
          <span class="desc">{{ card.planPhase }}</span>
        </div>
        <span :class="['header-tags', getTagOption['class']]">{{ getTagOption['name'] }}</span>
      </div>
      <div class="header-task mt-flex">
        <div
          :tabIndex="randomId"
          class="svg-option-item task-count"
          @click="showLinkTaskList()"
          @blur="hideLinkTaskList()"
        >
          <mt-icon name="icon_relate" />
          <span>{{ $t('关联任务：') }}{{ getLinkTasks.length }}</span>
          <div class="task-list mt-flex-direction-column" v-show="card.showOption">
            <div class="svg-option-item task-title">
              <mt-icon name="icon_relate" />
              <span>{{ $t('关联任务：') }}{{ getLinkTasks.length }}</span>
            </div>
            <ul class="task-items">
              <li
                v-for="(task, _index) in card.taskRelList"
                :key="_index"
                @click="handleClickTask(task)"
              >
                {{ task.linkTaskName }}
              </li>
            </ul>
          </div>
        </div>
        <div class="task-percent mt-flex" :style="{ '--width': percentWidth }">
          <span class="percent">{{ cardProgress }}%</span>
          <div :class="['percent-line', getPercentStyle]"></div>
        </div>
      </div>
    </div>
    <div class="card-content mt-flex-direction-column" @click="handleClickNode">
      <!-- <div class="card-time-1">节点计划完成时间：{{ 1 }}天</div> -->
      <div class="card-time-1">{{ $t('节点标准提前期：') }}{{ 2 }}{{ $t('天') }}</div>
      <!-- <div class="card-time mt-flex-direction-column">
        <div class="time-swap mt-flex-direction-column">
          <span class="title">{{ $t("计划开始") }}</span>
          <span class="value">{{ getFormatDate("planStartTime") }}</span>
        </div>
        <div class="time-swap mt-flex-direction-column">
          <span class="title">{{ $t("计划结束") }}</span>
          <span class="value">{{ getFormatDate("planEndTime") }}</span>
        </div>
      </div>
      <div class="card-time mt-flex-direction-column">
        <div class="time-swap mt-flex-direction-column">
          <span class="title">{{ $t("实际开始") }}</span>
          <span class="value">{{ getFormatDate("realStartTime") }}</span>
        </div>
        <div class="time-swap mt-flex-direction-column">
          <span class="title">{{ $t("实际结束") }}</span>
          <span class="value">{{ getFormatDate("realEndTime") }}</span>
        </div>
      </div> -->
    </div>
  </div>
</template>
<script>
export default {
  name: 'ScheduleCard',
  props: {
    cardData: {
      type: Object,
      default: () => {
        return {
          name: this.$t('RFQ发出'),
          desc: 'RFQ Send Out',
          tag: 'task',
          taskList: [],
          percent: 40,
          planStart: '2020-12-11 16:45:56',
          planEnd: '2020-12-11 16:45:56',
          actualStart: '2020-12-11 16:45:56',
          actualEnd: '2020-12-11 16:45:56'
        }
      }
    },
    active: {
      type: Boolean,
      default: true
    },
    tabIndex: {
      type: Number,
      default: -1
    }
  },
  data() {
    return {
      data: {},
      card: this.cardData,
      randomId: parseInt(Math.random(10000) * 10000)
    }
  },
  mounted() {
    if (this.data && this.data.taskData) {
      this.card = this.data.taskData
    }
  },
  computed: {
    cardProgress() {
      // 计划类型0未开始1进行中2已完成
      let _progress = 0
      switch (this.card.status) {
        case 0: //未开始
          _progress = 0
          break
        case 1: //进行中
          _progress = 0
          break
        case 2: //已完成
          _progress = 100
          break
      }
      return _progress
    },
    percentWidth() {
      return `${(this.cardProgress * 60) / 100}px`
    },
    getPercentStyle() {
      if (this.cardProgress < 40) {
        return 'warning'
      } else if (this.cardProgress < 99) {
        return 'process'
      } else {
        return 'success'
      }
    },
    getTagOption() {
      //任务计划模板里，全是‘任务’
      return { name: this.$t('任务'), class: 'task' }
      // if (this.card.planType == "0") {
      //   return { name: this.$t("任务"), class: "task" };
      // } else {
      //   return { name: this.$t("里程碑"), class: "" };
      // }
    },
    getFormatDate() {
      return (key) => {
        if (this.card[key]) {
          return this.$utils.formatTime(new Date(this.card[key]), 'YYYY-mm-dd HH:MM:SS')
        } else {
          return '-'
        }
      }
    },
    getLinkTasks() {
      return this.card?.taskRelList || []
    }
  },
  methods: {
    showLinkTaskList() {
      this.$set(this.card, 'showOption', true)
    },
    hideLinkTaskList() {
      this.$set(this.card, 'showOption', false)
    },
    handleClickNode() {
      this.$emit('handleClickNode', this.card)
    },
    handleClickTask(task) {
      this.hideLinkTaskList()
      this.$emit('handleClickTask', task)
    }
  },
  watch: {
    cardData: {
      handler(n) {
        this.card = n
      },
      deep: true
    }
  }
}
</script>
<style lang="scss" scoped>
.schedule-card {
  width: 300px;
  height: 160px;
  margin-bottom: 10px;
  background: rgba(250, 250, 250, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 4px 4px 8px 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  .card-header {
    cursor: move;
    justify-content: space-between;
    height: 74px;
    background: rgba(255, 255, 255, 1);
    border-radius: 4px 4px 0 0;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    padding: 10px;
    .header-title {
      justify-content: space-between;
      .header-name {
        justify-content: space-between;
        .name {
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 600;
          color: rgba(35, 43, 57, 1);
        }
        .desc {
          margin-top: 6px;
          font-size: 12px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(154, 154, 154, 1);
        }
      }
      .header-tags {
        width: 44px;
        height: 20px;
        background: rgba(241, 178, 87, 0.1);
        border-radius: 2px;
        font-size: 12px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(241, 178, 87, 1);
        padding: 4px;
        text-align: center;
        &.task {
          background: rgba(99, 134, 193, 0.1);
          color: rgba(99, 134, 193, 1);
        }
      }
    }
    .header-task {
      justify-content: space-between;
      .task-count {
        font-size: 12px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(157, 170, 191, 1);
        .task-list {
          padding: 10px;
          position: absolute;
          width: 160px;
          min-height: 70px;
          background: rgba(255, 255, 255, 1);
          border: 1px solid rgba(232, 232, 232, 1);
          border-radius: 2px;
          box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.2);
          z-index: 2;
          top: 0px;
          left: -5px;
          .task-title {
            font-size: 12px;
            font-family: PingFangSC;
            font-weight: normal;
            color: rgba(157, 170, 191, 1);
            text-align: left;
          }
          .task-items {
            li {
              margin-top: 20px;
              font-size: 14px;
              font-family: PingFangSC;
              font-weight: normal;
              color: rgba(99, 134, 193, 1);
              text-align: left;
              padding-left: 20px;
              position: relative;
              &:before {
                content: '';
                width: 8px;
                height: 8px;
                border-radius: 50%;
                border: 1px solid #9daabf;
                background: #fafafa;
                position: absolute;
                left: 0;
                top: 3px;
                z-index: 3;
              }
              &:after {
                content: '';
                width: 14px;
                height: 0px;
                border-top: 1px solid #9daabf;
                background: #fafafa;
                position: absolute;
                left: 0;
                top: 7px;
                z-index: 2;
              }
            }
          }
        }
      }
      .task-percent {
        align-items: center;
        .percent {
          margin-right: 6px;
          font-size: 12px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(154, 154, 154, 1);
        }
        .percent-line {
          position: relative;
          width: 60px;
          height: 6px;
          background: rgba(232, 232, 232, 1);
          border-radius: 3px;
          box-shadow: inset 0 0 4px 0 rgba(0, 0, 0, 0.06);
          &:before {
            content: '';
            width: var(--width);
            height: 6px;
            left: 0;
            position: absolute;
            z-index: 2;
            border-radius: 3px;
          }
          &.process {
            &:before {
              background: linear-gradient(
                90deg,
                rgba(247, 207, 98, 1) 0%,
                rgba(237, 161, 51, 1) 100%
              );
            }
          }
          &.warning {
            &:before {
              background: linear-gradient(
                90deg,
                rgba(247, 143, 98, 1) 0%,
                rgba(237, 86, 51, 1) 100%
              );
            }
          }
          &.success {
            &:before {
              background: linear-gradient(
                90deg,
                rgba(190, 232, 116, 1) 0%,
                rgba(138, 204, 64, 1) 100%
              );
            }
          }
        }
      }
    }
  }
  .card-content {
    padding: 10px;
    cursor: pointer;
    flex: 1;
    justify-content: space-around;
    .card-time-1 {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      //       color: rgba(157, 170, 191, 1);
    }
    // .card-time {
    //   width: 140px;
    //   height: 80px;
    //   transform: scale(0.83);
    //   justify-content: space-between;
    //   .time-swap {
    //     span {
    //       font-size: 12px;
    //       font-family: PingFangSC;
    //       font-weight: normal;
    //       color: rgba(157, 170, 191, 1);
    //       &.value {
    //         margin-top: 5px;
    //       }
    //       &.title {
    //         &:before {
    //           content: "";
    //           width: 8px;
    //           height: 8px;
    //           border-radius: 50%;
    //           border: 1px solid #9daabf;
    //           background: #fafafa;
    //           position: absolute;
    //           left: -17px;
    //           top: 3px;
    //           z-index: 2;
    //         }
    //       }
    //     }
    //   }
    //   &:last-of-type {
    //     margin-left: 10px;
    //   }
    //   &:before {
    //     content: "";
    //     height: 50px;
    //     width: 0;
    //     border-left: 1px solid #9daabf;
    //     position: absolute;
    //     left: -13px;
    //     top: 8px;
    //     z-index: 1;
    //   }
    // }
  }
  //  &:hover
  &.active {
    border-left: none;
    position: relative;
    &:before {
      content: '';
      height: 100%;
      width: 0;
      position: absolute;
      border-left: 3px solid #00469c;
      border-radius: 4px 0 0 8px;
      left: 0;
      top: 0;
      z-index: 2;
      animation: active-animation 0.2s ease;
    }
    @keyframes active-animation {
      0% {
        top: 50%;
        height: 0;
      }
      100% {
        top: 0;
        height: 100%;
      }
    }
  }
}
</style>
