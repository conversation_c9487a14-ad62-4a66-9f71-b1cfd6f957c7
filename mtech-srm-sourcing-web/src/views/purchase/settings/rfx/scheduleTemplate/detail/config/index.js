import { i18n } from '@/main.js'
const detailListToolBar = [{ id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('添加行') }]

const detailListColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  // {

  //   field: "id",
  //   headerText: i18n.t("序号"),
  // },
  {
    width: '200',
    field: 'relationType',
    headerText: i18n.t('关联方式'),
    cssClass: '',
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('品类'), 1: i18n.t('物料') }
    },
    cellTools: [
      { id: 'Edit', icon: 'icon_Editor', title: i18n.t('编辑') },
      { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') }
    ]
  },
  {
    field: 'itemTypeName',
    headerText: i18n.t('品类类型')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料编号')
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
  }
  // {

  //   field: "column7",
  //   headerText: i18n.t("更新人"),
  // },
  // {

  //   field: "column8",
  //   headerText: i18n.t("更新时间"),
  // },
]
export const pageConfig = [
  {
    toolbar: detailListToolBar,
    grid: {
      allowFiltering: true,
      frozenColumns: 2,
      allowPaging: false,
      columnData: detailListColumnData,
      dataSource: []
    }
  }
]
