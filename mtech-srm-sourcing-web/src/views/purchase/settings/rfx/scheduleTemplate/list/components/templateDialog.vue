// 计划模板
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="templateName" :label="$t('模板名称')">
          <mt-input
            v-model="formObject.templateName"
            float-label-type="Never"
            :placeholder="$t('请输入模板名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="templateCode" :label="$t('模板编号')">
          <mt-input
            v-model="formObject.templateCode"
            float-label-type="Never"
            :placeholder="$t('请输入模板编号')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')">
          <mt-input
            v-model="formObject.remark"
            float-label-type="Never"
            :placeholder="$t('请输入模板内容')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formObject: {
        id: null,
        remark: '', //备注
        enabled: 1, //是否启用  1启用 2 停用
        templateCode: '', //模板编号
        templateName: '' //模板名称
      },
      formRules: {},
      editStatus: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.formObject = { ...this.modalData.data }
    }
    this.getFormValidRules('formRules', this.$API.scheduleConfig.saveSourcingPlanTemplateValid)
  },
  methods: {
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = { ...this.formObject }
          if (!this.editStatus) {
            delete params.id
          }
          this.$API.scheduleConfig.saveSourcingPlanTemplate(params).then((res) => {
            if (res.code == 200) {
              this.$emit('confirm-function')
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/ span.e-input-group {
    padding: 0;
  }
  /deep/ .process-desc {
    width: 820px !important;
  }
  /deep/ .tree-view-container {
    box-shadow: inset 0 0 0 1px rgba(232, 232, 232, 1);
    width: 100%;
    position: absolute;
    left: 0;
    top: 50px;
    z-index: 2;
    background: #fff;
    height: 200px;
    overflow: auto;
  }
}
</style>
