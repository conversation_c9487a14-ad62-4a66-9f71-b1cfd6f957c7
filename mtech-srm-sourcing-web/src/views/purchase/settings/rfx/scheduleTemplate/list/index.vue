<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      pageConfig: pageConfig(this.$API.scheduleConfig.getSourcingPlanTemplateList)
    }
  },
  methods: {
    //表格按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      console.log('use-handleClickToolBar', e, _selectGridRecords)

      if (
        _selectGridRecords.length <= 0 &&
        (e.toolbar.id == 'start' || e.toolbar.id == 'stop' || e.toolbar.id == 'Delete')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      if (e.toolbar.id == 'Add') {
        this.handleAddApprovalConfig()
      } else if (e.toolbar.id == 'Delete') {
        this.handleBatchDelete(_selectGridRecords)
      } else if (e.toolbar.id == 'Edit') {
        if (_selectGridRecords.length > 1) {
          this.$toast({
            content: this.$t('只支持修改一行数据'),
            type: 'warning'
          })
        } else {
          this.handleEditApprovalConfig(_selectGridRecords[0])
        }
      } else if (e.toolbar.id == 'start') {
        this.handleBatchUpdateStart(_selectGridRecords)
      } else if (e.toolbar.id == 'stop') {
        this.handleBatchUpdateStop(_selectGridRecords)
      }
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      if (e.tool.id == 'edit') {
        this.handleEditApprovalConfig(e.data)
      } else if (e.tool.id == 'delete') {
        this.handleDeleteConfig([e.data.id])
      } else if (e.tool.id == 'start') {
        this.handleUpdateConfigStatus([e.data.id], 1)
      } else if (e.tool.id == 'stop') {
        this.handleUpdateConfigStatus([e.data.id], 0)
      }
    },
    //单元格标题点击操作
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
      if (e.field == 'templateCode') {
        //点击模板编号--跳转详情
        localStorage.scheduleTemplateInfo = JSON.stringify(e.data)
        this.$router.push({
          name: `schedule-template-detail`,
          query: {
            configId: e.data.id
          }
        })
      }
    },
    //批量删除操作
    handleBatchDelete(_selectGridRecords) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      this.handleDeleteConfig(_selectIds)
    },

    //批量停用操作
    handleBatchUpdateStop(_selectGridRecords) {
      //是否启用0否1是
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      let _disableStatusRecords = _selectGridRecords.filter((s) => {
        return s.enabled === 0
      })
      if (_disableStatusRecords.length > 0) {
        //选中数据中，存在‘停用’状态
        this.$toast({
          content: this.$t("只有‘启用’状态可执行'停用'操作"),
          type: 'warning'
        })
        return
      } else {
        this.handleUpdateConfigStatus(_selectIds, 0)
      }
    },

    //批量启动操作
    handleBatchUpdateStart(_selectGridRecords) {
      //是否启用0否1是
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      let _disableStatusRecords = _selectGridRecords.filter((s) => {
        return s.enabled === 1
      })
      if (_disableStatusRecords.length > 0) {
        //选中数据中，存在‘启用’数据
        this.$toast({
          content: this.$t("只有'停用'状态可执行'启用'操作"),
          type: 'warning'
        })
        return
      } else {
        this.handleUpdateConfigStatus(_selectIds, 1)
      }
    },

    //更新状态(行内操作+批量操作)
    handleUpdateConfigStatus(idList, status) {
      //是否启用0否1是
      let _params = {
        idList
      }
      let _statusMap = [this.$t('停用'), this.$t('启用')]
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: `${this.$t('确认更新状态为')}${_statusMap[status]}？`
        },
        success: () => {
          let _url =
            status === 0 ? 'disableSourcingPlanTemplateById' : 'enableSourcingPlanTemplateById'
          this.$API.scheduleConfig[_url](_params).then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    //新增配置
    handleAddApprovalConfig() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/settings/rfx/scheduleTemplate/components/templateDialog" */ './components/templateDialog.vue'
          ),
        data: {
          title: this.$t('新增计划模板')
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //编辑配置
    handleEditApprovalConfig(data) {
      if (data) {
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "router/purchase/settings/rfx/scheduleTemplate/components/templateDialog" */ './components/templateDialog.vue'
            ),
          data: {
            title: this.$t('编辑计划模板'),
            data: data
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    },
    //更新配置的状态(行内操作)
    changeApprovalStatus(e, data) {
      let _params = { id: data.configId, enabled: e ? 1 : 2 }
      this.$API.approvalConfig.updateApprovalConfig(_params).then(() => {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },
    //删除规则
    handleDeleteConfig(idList) {
      let _params = {
        idList
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.scheduleConfig.deleteSourcingPlanTemplateById(_params).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    }
  }
}
</script>
