import { i18n } from '@/main.js'
const listToolBar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'Edit', icon: 'icon_solid_edit', title: i18n.t('修改') },
  { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') }
  // { id: "start", icon: "icon_solid_Createorder", title: i18n.t("启用") },
  // { id: "stop", icon: "icon_solid_Cancel", title: i18n.t("停用") },
]

const listColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    width: '200',
    field: 'templateCode',
    headerText: i18n.t('模板编号'),
    cellTools: [
      'edit',
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data['enabled'] < 1
        }
      }
    ]
  },
  {
    field: 'templateName',
    headerText: i18n.t('模板名称'),
    cssClass: ''
  },
  {
    field: 'remark',
    headerText: i18n.t('模板描述')
  },
  {
    field: 'enabled', //是否启用0否1是
    headerText: i18n.t('状态'),
    width: 200,
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('停用'), 1: i18n.t('启用') }
    },
    cellTools: [
      {
        id: 'start',
        // icon: "icon_solid_Createorder",
        title: i18n.t('启用'),
        visibleCondition: (data) => {
          return data['enabled'] == 0
        }
      },
      {
        id: 'stop',
        // icon: "icon_solid_Cancel",
        title: i18n.t('停用'),
        visibleCondition: (data) => {
          return data['enabled'] == 1
        }
      }
    ]
  },
  {
    field: 'createUserName',
    headerText: i18n.t('更新人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('更新时间'),
    type: 'date',
    format: 'yyyy-MM-dd'
  }
]

export const pageConfig = (url) => [
  {
    toolbar: listToolBar,
    grid: {
      allowFiltering: true,
      columnData: listColumnData,
      asyncConfig: {
        url
      }
    }
  }
]
