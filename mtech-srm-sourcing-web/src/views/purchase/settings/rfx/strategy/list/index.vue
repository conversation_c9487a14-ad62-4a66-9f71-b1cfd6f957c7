<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'

export default {
  data() {
    return {
      pageConfig: pageConfig(this.$API.strategyConfig.getRfxStrategyList)
    }
  },
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      console.log('use-handleClickToolBar', e, _selectGridRecords)

      if (
        _selectGridRecords.length <= 0 &&
        (e.toolbar.id == 'Start' ||
          e.toolbar.id == 'Stop' ||
          e.toolbar.id == 'Delete' ||
          e.toolbar.id == 'Edit')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      if (e.toolbar.id == 'Add') {
        this.handleAddStrategyConfig()
      } else if (e.toolbar.id == 'Start') {
        this.handleBatchUpdateStart(_selectGridRecords)
      } else if (e.toolbar.id == 'Stop') {
        this.handleBatchUpdateStop(_selectGridRecords)
      } else if (e.toolbar.id == 'Delete') {
        this.handleBatchDelete(_selectGridRecords)
      } else if (e.toolbar.id == 'Edit') {
        if (_selectGridRecords.length > 1) {
          this.$toast({
            content: this.$t('只支持修改一行数据'),
            type: 'warning'
          })
        } else {
          this.handleEditStrategyConfig(_selectGridRecords[0])
        }
      }
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      if (e.tool.id == 'edit') {
        //编辑操作
        this.handleEditStrategyConfig(e.data)
      } else if (e.tool.id == 'start') {
        //启用操作
        this.handleUpdateConfigStatus([e.data.id], 1)
      } else if (e.tool.id == 'stop') {
        //停用操作
        this.handleUpdateConfigStatus([e.data.id], 2)
      } else if (e.tool.id == 'delete') {
        this.handleDeleteConfig([e.data.id])
      }
    },
    //单元格标题点击操作
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
      if (e.field == 'strategyCode') {
        this.handleEditStrategyConfig(e.data)
      }
    },
    //批量启动操作
    handleBatchUpdateStart(_selectGridRecords) {
      //状态 0 未启用 1 启用 2 停用
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      let _disableStatusRecords = _selectGridRecords.filter((s) => {
        return s.enableStatus !== 0
      })
      if (_disableStatusRecords.length > 0) {
        //选中数据中，存在非‘草稿’状态
        this.$toast({
          content: this.$t("只有草稿状态可执行'启用'操作"),
          type: 'warning'
        })
        return
      } else {
        this.handleUpdateConfigStatus(_selectIds, 1)
      }
    },
    //批量停用操作
    handleBatchUpdateStop(_selectGridRecords) {
      //状态 0 未启用 1 启用 2 停用
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      let _disableStatusRecords = _selectGridRecords.filter((s) => {
        return s.enableStatus !== 1
      })
      if (_disableStatusRecords.length > 0) {
        //选中数据中，存在非‘启用’状态
        this.$toast({
          content: this.$t("只有启用状态可执行'停用'操作"),
          type: 'warning'
        })
        return
      } else {
        this.handleUpdateConfigStatus(_selectIds, 2)
      }
    },
    //批量删除操作
    handleBatchDelete(_selectGridRecords) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      this.handleDeleteConfig(_selectIds)
    },
    //新增策略
    handleAddStrategyConfig() {
      this.$router.push({
        path: `/sourcing/strategy-map-detail-flow`,
        query: {
          key: this.$utils.randomString()
        }
      })
    },
    //编辑策略
    handleEditStrategyConfig(data) {
      if (data && data.id) {
        this.$router.push({
          path: `/sourcing/strategy-map-detail-flow`,
          query: {
            configId: data.id
          }
        })
      }
    },
    //更新策略的状态(行内操作+批量操作)
    handleUpdateConfigStatus(ids, status) {
      //enableStatus状态 0 未启用 1 启用 2 停用
      let _params = {
        ids: ids,
        status: status
      }
      let _statusMap = [this.$t('草稿'), this.$t('启用'), this.$t('停用')]
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认更新状态为${_statusMap[status]}?`)
        },
        success: () => {
          this.$API.strategyConfig.updateRfxStrategyDataStatus(_params).then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    //删除规则
    handleDeleteConfig(ids) {
      let _params = {
        ids: ids
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          this.$API.strategyConfig.deleteRfxStrategyDataByIds(_params).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  /deep/.e-headercelldiv {
    padding: 0 0.6em 0 0.6em !important;
  }
}
</style>
