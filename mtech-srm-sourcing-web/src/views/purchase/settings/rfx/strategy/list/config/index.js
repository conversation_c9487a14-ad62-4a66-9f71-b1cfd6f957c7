import Vue from 'vue'
import { i18n, permission } from '@/main.js'

const listToolBar = () => [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'Edit', icon: 'icon_solid_edit', title: i18n.t('修改') },
  { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') }
]

const listColumnData = () => [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    width: '200',
    field: 'strategyCode',
    headerText: i18n.t('策略编码'),
    cellTools: ['edit', 'delete']
  },
  {
    width: '150',
    field: 'strategyName',
    headerText: i18n.t('策略名称'),
    cssClass: ''
  },
  {
    width: '120',
    field: 'strategyDesc',
    headerText: i18n.t('策略描述')
  },
  {
    width: '80',
    field: 'version',
    headerText: i18n.t('版本'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div class="action-boxs"><span class="version-text">V{{data.version}}</span></div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    width: '80',
    field: 'enableStatus',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('草稿'), 1: i18n.t('启用'), 2: i18n.t('停用') }
    },
    cellTools: [
      {
        id: 'start',
        // icon: "icon_solid_Activateorder",
        title: i18n.t('启用'),
        visibleCondition: (data) => {
          //enableStatus	状态 0 草稿 1 启用 2 停用
          return data['enableStatus'] !== 1
        }
      },
      {
        id: 'stop',
        // icon: "icon_solid_Cancel",
        title: i18n.t('停用'),
        visibleCondition: (data) => {
          //enableStatus	状态 0 草稿 1 启用 2 停用
          return data['enableStatus'] == 1
        }
      }
    ]
  },
  {
    width: '100',
    field: 'sourcingMode',
    headerText: i18n.t('寻源方式'),
    valueConverter: {
      type: 'map',
      map: [
        {
          status: 'rfq',
          label: i18n.t('询报价'),
          cssClass: 'title'
        },
        {
          status: 'direct_pricing',
          label: i18n.t('直接定价'),
          cssClass: 'title'
        },
        { status: 'invite_bids', label: i18n.t('招投标'), cssClass: 'title' },
        { status: 'bidding_price', label: i18n.t('竞价'), cssClass: 'title' }
      ],
      fields: { text: 'label', value: 'status' }
    }
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    type: 'date',
    format: 'yyyy-MM-dd HH:mm:ss'
  },
  {
    width: '150',
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    type: 'date',
    format: 'yyyy-MM-dd HH:mm:ss'
  },
  {
    width: '100',
    field: 'createUserName',
    headerText: i18n.t('更新人')
  }
]

export const pageConfig = (url) => [
  {
    toolbar: listToolBar(),
    gridId: permission.gridId['purchase']['strategyMaps'],
    grid: {
      allowFiltering: true,
      columnData: listColumnData(),
      asyncConfig: {
        url
      }
    }
  }
]
