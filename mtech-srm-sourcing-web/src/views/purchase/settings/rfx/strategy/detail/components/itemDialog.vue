// 策略地图-关联品类+业务类型
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="createProjFormRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="relationType" :label="$t('请选择关联方式')">
          <mt-select
            v-model="formObject.relationType"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="relationTypeList"
            :placeholder="$t('请选择关联方式')"
            :label="$t('请选择关联方式')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          prop="categoryId"
          v-if="formObject.relationType == 0"
          :label="$t('请选择品类类型')"
        >
          <mt-select
            ref="categoryIdRef"
            v-model="formObject.categoryId"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="propsData.categoryList"
            :fields="{ text: 'categoryName', value: 'id' }"
            :placeholder="$t('请选择品类类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="itemId" v-if="formObject.relationType == 1" :label="$t('请选择物料')">
          <mt-select
            ref="itemIdRef"
            v-model="formObject.itemId"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="propsData.itemList"
            :fields="{ text: 'itemName', value: 'id' }"
            :placeholder="$t('请选择物料')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          prop="itemTypeId"
          v-if="formObject.relationType == 0"
          :label="$t('请选择品类类型名')"
        >
          <mt-select
            ref="itemTypeIdRef"
            v-model="formObject.itemTypeId"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="propsData.itemTypeList"
            :fields="{ text: 'itemName', value: 'id' }"
            :placeholder="$t('请选择品类类型名')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="businessTypeId" :label="$t('请选择业务类型')">
          <mt-select
            ref="businessTypeIdRef"
            v-model="formObject.businessTypeId"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="propsData.businessTypeList"
            :fields="{ text: 'itemName', value: 'id' }"
            :placeholder="$t('请选择业务类型')"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      type: 'NEW',
      formItemIndex: null,
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      relationTypeList: [
        { text: this.$t('品类'), value: 0 },
        { text: this.$t('物料'), value: 1 }
      ],
      formObject: {
        businessTypeCode: null, //业务类型编码
        businessTypeId: null, //业务类型Id
        businessTypeName: null, //业务类型名称
        categoryCode: null, //品类代码
        categoryId: null, //	品类Id
        categoryName: null, //	品类名称
        // config: 0,//配置ID
        itemCode: null, //	物料编码
        itemId: null, //	物料Id
        itemName: null, //物料名称
        itemTypeCode: null, //品类类型编码
        itemTypeId: null, //品类类型id
        itemTypeName: null, //品类类型名称
        relationType: 0 //关联关系
      },
      formRules: {
        businessTypeId: [
          {
            required: true,
            message: this.$t('请选择业务类型'),
            trigger: 'blur'
          }
        ],
        relationType: [
          {
            required: true,
            message: this.$t('请选择关联关系'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        width: '500px',
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.propsData.formData) {
      let { businessTypeId, categoryId, itemId, itemTypeId, relationType, index } =
        this.propsData.formData
      this.$set(this.formObject, 'businessTypeId', businessTypeId)
      this.$set(this.formObject, 'categoryId', categoryId)
      this.$set(this.formObject, 'itemId', itemId)
      this.$set(this.formObject, 'itemTypeId', itemTypeId)
      this.$set(this.formObject, 'relationType', relationType)
      this.type = 'EDIT'
      this.formItemIndex = index
    }
  },
  methods: {
    confirm() {
      this.$refs.createProjFormRef.validate((valid) => {
        if (valid) {
          let params = { ...this.formObject }
          if (this.formObject.relationType == 0) {
            //0 品类   1 物料
            //处理下拉框数据赋值
            this.$utils.assignDataFromRefs(params, [
              {
                key: 'categoryId', //品类 categoryId下拉框数据
                ref: this.$refs.categoryIdRef.ejsRef,
                fields: ['categoryName', 'categoryCode']
              },
              {
                key: 'itemTypeId', //品类类型 itemTypeId下拉框数据
                ref: this.$refs.itemTypeIdRef.ejsRef,
                fields: {
                  itemTypeCode: 'itemCode',
                  itemTypeName: 'itemName'
                }
              }
            ])
          } else {
            //处理下拉框数据赋值
            this.$utils.assignDataFromRefs(params, [
              {
                key: 'itemId', //物料 itemId下拉框数据
                ref: this.$refs.itemIdRef.ejsRef,
                fields: ['itemCode', 'itemName']
              }
            ])
          }
          //处理下拉框数据赋值
          this.$utils.assignDataFromRefs(params, [
            {
              key: 'businessTypeId', //业务类型 businessTypeId下拉框数据
              ref: this.$refs.businessTypeIdRef.ejsRef,
              fields: {
                businessTypeCode: 'itemCode',
                businessTypeName: 'itemName'
              }
            }
          ])
          if (this.type == 'EDIT') {
            params.formItemIndex = this.formItemIndex
          }
          this.$emit('confirm-function', params)
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
