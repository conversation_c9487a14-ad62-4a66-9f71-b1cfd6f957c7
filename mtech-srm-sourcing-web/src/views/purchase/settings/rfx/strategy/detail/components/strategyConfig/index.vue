<template>
  <div class="strategy-config">
    <mt-DataGrid v-bind="grid" ref="grid" />
  </div>
</template>

<script>
import { getColumnData } from './config'
import { isEqual } from 'lodash'

export default {
  data() {
    return {
      grid: {
        allowFiltering: false,
        editSettings: {
          allowAdding: true,
          allowEditing: true,
          allowDeleting: true,
          mode: 'Normal',
          allowEditOnDblClick: true,
          showConfirmDialog: false,
          showDeleteConfirmDialog: true,
          newRowPosition: 'Top'
        },
        dataSource: [],
        columnData: [],
        actionComplete: this.actionComplete,
        allowEditing: true
      }
    }
  },
  props: {
    strategyDataDetail: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    strategyDataDetail: {
      deep: true,
      handler: function () {
        this.$nextTick(this.initGrid)
      }
    }
  },
  mounted() {
    this.initGrid()
  },
  methods: {
    async getDataSource() {
      this.$refs.grid.ejsRef.endEdit()
      return new Promise((c) => {
        this.$nextTick(() => {
          c(this.grid.dataSource)
        })
      })
    },
    initGrid() {
      this.grid.dataSource = this.strategyDataDetail?.details || []
      this.grid.columnData = getColumnData({
        dataSource: () => this.grid.dataSource
      })
    },
    actionComplete(args) {
      if (args.requestType === 'save') {
        if (args.action === 'edit' && !isEqual(args.data, args.previousData)) {
          this.actionCompleteEdit(args)
        }
      }
    },
    actionCompleteEdit(args) {
      const rowData = args.data
      const key = 'strategyCode'
      const idx = this.grid.dataSource.findIndex((item) => rowData[key] == item[key])
      const dataSource = this.grid.dataSource
      if (idx > -1) {
        dataSource[idx] = rowData
      } else {
        dataSource.push(rowData)
      }
      this.$set(this.grid, 'dataSource', dataSource)
    }
  }
}
</script>

<style scoped lang="scss">
.strategy-config {
  /deep/ .e-gridcontent .e-content {
    min-height: 80px;
  }
}
</style>
