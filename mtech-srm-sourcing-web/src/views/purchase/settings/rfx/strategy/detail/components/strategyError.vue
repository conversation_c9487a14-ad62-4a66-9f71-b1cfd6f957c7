<template>
  <mt-dialog
    ref="dialog"
    css-class="small-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <div class="content-title">{{ getErrorMsg[0] }}，{{ $t('请查看') }}</div>
      <div class="content-item first-line">
        <span class="item-label">{{ $t('策略地图编号') }}</span>
        <span class="item value">{{ getErrorMsg[1] }}</span>
      </div>
      <div class="content-item">
        <span class="item-label">{{ $t('策略地图名称') }}</span>
        <span class="item value">{{ getErrorMsg[3] }}</span>
      </div>
      <div class="content-item">
        <span class="item-label">{{ $t('策略地图描述') }}</span>
        <span class="item value">{{ getErrorMsg[4] }}</span>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('我知晓了') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        width: '500px',
        buttons: this.buttons,
        cssClass: ('small-dialog ' + this.modalData.cssClass).trim()
      }
    },
    header() {
      return this.modalData.title
    },
    getErrorMsg() {
      return this.modalData.errorMsg.split(':')
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    confirm() {
      this.$emit('confirm-function')
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss">
.dialog-content {
  padding: 20px;
  .content-title {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(41, 41, 41, 1);
  }
  .content-item {
    margin-top: 10px;
    &.first-line {
      margin-top: 20px;
    }
    display: flex;
    .item-label {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
      &:after {
        content: '：';
      }
    }
    .item-value {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(237, 161, 51, 1);
      margin-left: 30px;
    }
  }
}
</style>
