<template>
  <div class="full-height strategy-maps-page mt-flex-direction-column">
    <div class="top-info mt-flex-direction-column">
      <div class="detail-info">
        <div class="name-wrap">
          <div class="second-line">
            <ul class="labels">
              <li v-if="strategyDataDetail.createUserName">
                <mt-icon name="icon_Creator"></mt-icon>
                <span> {{ $t('创建人') }}：{{ strategyDataDetail.createUserName }} </span>
              </li>
              <li v-if="strategyDataDetail.createTime">
                <mt-icon name="icon_Company"></mt-icon>
                <span> {{ $t('创建时间') }}：{{ strategyDataDetail.createTime }} </span>
              </li>
            </ul>
          </div>
        </div>
        <div class="btns-wrap">
          <mt-button @click="saveStrategyConfig">
            {{ $t('保存') }}
          </mt-button>
          <mt-button @click="backToStrategyMapsPage">
            {{ $t('取消') }}
          </mt-button>
        </div>
      </div>
      <mt-form
        class="header-form"
        ref="headerForm"
        v-if="strategyDataDetail"
        :model="strategyDataDetail"
        :rules="strategyDataDetailRules"
      >
        <mt-form-item
          prop="strategyCode"
          class="form-item"
          :label="$t('策略编码')"
          label-width="200"
          label-align="right"
          label-style="top"
        >
          <mt-input
            item-label-style="left"
            v-model="strategyDataDetail.strategyCode"
            :show-clear-button="true"
            type="text"
            maxlength="32"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="strategyName"
          class="form-item"
          :label="$t('策略名称')"
          label-width="200"
          label-align="right"
          label-style="top"
        >
          <mt-input
            v-model="strategyDataDetail.strategyName"
            item-label-style="left"
            :disabled="false"
            :show-clear-button="true"
            type="text"
            maxlength="32"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="sourcingMode"
          class="form-item"
          :label="$t('寻源方式')"
          label-width="200"
          label-align="right"
          label-style="top"
        >
          <mt-select
            v-model="strategyDataDetail.sourcingMode"
            :data-source="[
              { text: $t('询报价'), value: 'rfq' },
              { text: $t('招投标'), value: 'invite_bids' },
              { text: $t('竞价'), value: 'bidding_price' }
            ]"
            :show-clear-button="true"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          prop="strategyDesc"
          class="form-item"
          :label="$t('策略描述')"
          label-width="200"
          label-align="right"
          label-style="top"
        >
          <mt-input
            v-model="strategyDataDetail.strategyDesc"
            item-label-style="left"
            :disabled="false"
            :show-clear-button="true"
            type="text"
            maxlength="32"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="name"
          class="form-item"
          :label="$t('业务类型')"
          label-width="200"
          label-align="right"
          label-style="top"
        >
          <mt-select
            v-model="strategyDataDetail.businessTypeCode"
            @change="onChangeBusinessTypeCode"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :data-source="businessTypeList"
            :show-clear-button="true"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </div>
    <mt-tabs
      :tab-id="$utils.randomString()"
      :e-tab="false"
      :data-source="tabSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <strategy-flow
      ref="strategyConfigRef"
      :strategy-config="strategyConfig"
      :strategy-model="strategyModel"
      v-show="tabIndex == 0"
    >
    </strategy-flow>
    <strategy-config
      ref="strategyConfig"
      v-show="tabIndex == 1"
      :strategy-data-detail="strategyDataDetail"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
import { utils } from '@mtech-common/utils'
import strategyFlow from 'COMPONENTS/StrategyFlow/index.vue'

export default {
  data() {
    return {
      configId: null,
      isdisabled: true,
      tabIndex: 0, // 当前选中的tab
      tagList: [this.$t('草稿'), 'V1.0.0'],
      tabSource: [
        {
          title: this.$t('策略地图')
        },
        {
          title: this.$t('策略配置')
        }
      ],
      strategyModel: {},
      strategyConfig: {},
      pageConfig,
      categoryList: [], //品类列表 getCategoryList
      itemList: [], //品项列表 itemCode itemName
      itemTypeList: [], //业务类型 - businessType列表

      strategyDataDetail: {
        strategyCode: null,
        strategyName: null, // 策略名称
        sourcingMode: null,
        strategyDesc: null,
        businessTypeCode: null, // 业务类型编码
        businessTypeName: null, // 业务类型名称
        strategyConfig: {}, // 策略配置
        details: [] // 表单配置
      }, // 策略地图详情
      businessTypeList: [], // 业务类型 - businessType列表
      // 策略地图表单验证
      strategyDataDetailRules: {
        strategyCode: [
          {
            required: true,
            message: this.$t('请输入策略编码'),
            trigger: 'blur'
          },
          {
            min: 1,
            max: 32,
            message: this.$t('长度在 1 到 32 个字符'),
            trigger: 'blur'
          }
        ],
        strategyName: [
          {
            required: true,
            message: this.$t('请输入策略名称'),
            trigger: 'blur'
          },
          {
            min: 1,
            max: 32,
            message: this.$t('长度在 1 到 32 个字符'),
            trigger: 'blur'
          }
        ],
        sourcingMode: {},
        strategyDesc: [
          {
            required: true,
            message: this.$t('请输入策略描述'),
            trigger: 'blur'
          },
          {
            min: 1,
            max: 32,
            message: this.$t('长度在 1 到 32 个字符'),
            trigger: 'blur'
          }
        ],
        businessTypeCode: {}, // 业务类型编码
        businessTypeName: {} // 业务类型名称
      },
      saveStrategyConfig: utils.debounce(this._saveStrategyConfig, 800, true)
    }
  },
  components: {
    strategyConfig: () => import('./components/strategyConfig/index.vue'),
    //工作流编辑器
    strategyFlow
  },
  async mounted() {
    this.configId = this.$route.query.configId
    // 获取基础策略配置
    const [details, strategyConfig] = await this.initBaseStragtegys()
    if (this.configId) {
      // 根据configId获取配置基本信息
      this.getStrategyMapConfigDetailById(details)
    } else {
      // 新增
      this.strategyDataDetail.details = details
      this.strategyDataDetail.strategyConfig = strategyConfig
    }
    this.getMainDataList()

    this.initBusinessTypeList() // 获取 businessType 配置
  },
  methods: {
    /**
     * 关联更新 strategyDataDetail.businessTypeName
     */
    onChangeBusinessTypeCode({ itemData }) {
      if (itemData?.itemName) {
        this.strategyDataDetail.businessTypeName = itemData.itemName
      }
    },
    /**
     * 获取基础策略配置
     */
    async initBaseStragtegys() {
      const res = await this.$API.commonBase.getStragtegys().catch(() => {})
      const strategyConfig = res.data.reduce((p, v) => {
        p[v.strategyCode] = null
        return p
      }, {})
      const details = res.data.map((e) => ({
        editEnable: 0,
        enableStatus: 1,
        ...e
      }))
      return [details, strategyConfig]
    },
    /**
     * 获取 businessType 配置
     */
    async initBusinessTypeList() {
      const res = await this.$API.masterData
        .dictionaryGetList({
          dictCode: 'businessType'
        })
        .catch(() => {})
      this.businessTypeList = res.data
    },
    AddData() {
      console.log(1111)
      this.isdisabled = false
    },
    // 合并基础配置
    mergeDetails(baseDetails, details) {
      return baseDetails.reduce((p, v) => {
        const detail = details.find((detail) => detail.strategyCode === v.strategyCode)
        if (detail) {
          p.push(detail)
        } else {
          p.push(v)
        }
        return p
      }, [])
    },
    getStrategyMapConfigDetailById(baseDetails) {
      this.$store.commit('startLoading')
      // 策略地图详情
      this.$API.strategyConfig.getRfxStrategyDetailData({ id: this.configId }).then((res) => {
        this.$store.commit('endLoading')

        this.strategyDataDetail = {
          ...res.data,
          details: this.mergeDetails(baseDetails, res.data.details)
        }
        let { processDefId, processDefKey, enableStatus, version, strategyConfig, relations } =
          res.data
        let _statusMap = [this.$t('草稿'), this.$t('启用'), this.$t('停用')]
        this.tagList = [_statusMap[enableStatus], this.$t('版本号') + `:V${version}`]
        this.strategyModel = { processDefId, processDefKey }
        this.strategyConfig = strategyConfig
        this.pageConfig[0]['grid']['dataSource'] = []
        this.$nextTick(() => {
          this.$forceUpdate()
          this.$set(this.pageConfig[0].grid, 'dataSource', relations)
        })
      })
    },
    getMainDataList() {
      // 品类列表 getCategoryList
      this.$API.masterData.getCategoryList().then((res) => {
        this.categoryList = res.data
      })

      // 品项列表 itemCode itemName
      this.$API.masterData.getItemList().then((res) => {
        this.itemList = res.data
      })

      // 品类类型 - itemType
      this.$API.masterData
        .dictionaryGetList({
          dictCode: 'itemType'
        })
        .then((res) => {
          this.itemTypeList = res.data || []
        })
    },
    asyncFormValidate(el) {
      return new Promise((c) => {
        this.$refs[el].validate((valid) => {
          c(valid)
        })
      })
    },
    // 保存
    async _saveStrategyConfig() {
      if (!(await this.asyncFormValidate('headerForm'))) {
        return
      }
      let processInfo = await this.$refs.strategyConfigRef.getModelInfo()
      let params = {
        ...this.strategyDataDetail,
        details: await this.$refs.strategyConfig.getDataSource(),
        processDefId: processInfo.processDefId,
        processDefKey: processInfo.processDefKey
      }
      if (this.configId) {
        params.id = this.configId
      }
      //保存策略地图
      this.$store.commit('startLoading')
      this.$API.strategyConfig
        .saveRfxStrategyData(params)
        .then(() => {
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
          this.$router.push({
            path: `/sourcing/strategy-maps`
          })
        })
        .catch((err) => {
          if (err.code == '503' && err.msg.indexOf(':') > -1) {
            this.$dialog({
              modal: () =>
                import(
                  /* webpackChunkName: "router/purchase/settings/rfx/strategy/detail/components/strategyError" */ './components/strategyError.vue'
                ),
              data: {
                title: this.$t('提示'),
                errorMsg: err.msg
              },
              success: () => {}
            })
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    backToStrategyMapsPage() {
      this.$router.go(-1)
    },
    handleSelectTab(e) {
      this.tabIndex = e
    },
    handleClickToolBar(e) {
      console.log('use-handleClickToolBar', e)
      if (e.toolbar.id == 'Add') {
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "router/purchase/settings/rfx/strategy/detail/components/itemDialog" */ './components/itemDialog.vue'
            ),
          data: {
            title: this.$t('新增条目'),
            businessTypeList: this.businessTypeList, //业务类型 - businessType列表
            categoryList: this.categoryList, //品类列表 getCategoryList
            itemList: this.itemList, //品项列表 itemCode itemName
            itemTypeList: this.itemTypeList //业务类型 - businessType列表
          },
          success: (data) => {
            let _dataSource = [...this.pageConfig[0].grid['dataSource']]
            _dataSource.push(data)
            this.$set(this.pageConfig[0].grid, 'dataSource', _dataSource)
          }
        })
      }
    },
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      if (e.tool.id == 'Edit') {
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "router/purchase/settings/rfx/strategy/detail/components/itemDialog" */ './components/itemDialog.vue'
            ),
          data: {
            title: this.$t('编辑条目'),
            businessTypeList: this.businessTypeList, //业务类型 - businessType列表
            categoryList: this.categoryList, //品类列表 getCategoryList
            itemList: this.itemList, //品项列表 itemCode itemName
            itemTypeList: this.itemTypeList, //业务类型 - businessType列表
            formData: e.data
          },
          success: (data) => {
            let _formItem = data,
              _formItemIndex = data.formItemIndex
            delete _formItem.formItemIndex
            let _dataSource = [...this.pageConfig[0].grid['dataSource']]
            _dataSource[_formItemIndex] = data
            this.$set(this.pageConfig[0].grid, 'dataSource', _dataSource)
          }
        })
      } else if (e.tool.id == 'Delete') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            let _dataSource = [...this.pageConfig[0].grid['dataSource']]
            _dataSource.splice(+e.data.index, 1)
            this.$set(this.pageConfig[0].grid, 'dataSource', _dataSource)
          }
        })
      }
    },
    handleGridCurrentChange(data) {
      console.log('use-handleGridCurrentChange', data)
    },
    handleGridSizeChange(data) {
      console.log('use-handleGridSizeChange', data)
    }
  }
}
</script>

<style lang="scss" scoped>
.header-form.mt-form {
  display: flex;
  margin-top: 10px;

  .mt-form-item {
    padding: 0 10px;
    flex: 1;
    margin-bottom: 0px;

    &:first-child {
      padding-left: 0;
    }
    &:last-child {
      padding-right: 0;
    }
  }
}

.mticon-icon {
  color: #6386c1;
  font-size: 5px;
  margin-left: 8px;
  margin-top: 6px;
}
.mticon-icons {
  color: #6386c1;
  font-size: 5px;
  margin-top: 6px;
}
.mticon-icones {
  color: #6386c1;
  font-size: 5px;
  margin-top: 6px;
  margin-left: -50px;
}
.strategy-maps-page {
  padding-top: 20px;
  width: 100%;
}
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.top-info {
  width: 100%;
  // height: 120px;
  padding: 20px;
  justify-content: space-between;
  background: rgba(245, 248, 251, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 0 8px 0 0;

  .detail-info {
    padding: 0;
    display: flex;
    line-height: 1;
    .name-wrap {
      flex: 1;
      overflow: hidden;
      .first-line {
        display: flex;
        align-items: center;
        .code {
          width: 150px;
          display: flex;

          .strategy-name {
            font-size: 16px;
            font-family: DINAlternate;
            font-weight: bold;
            color: #9a9a9a;
            outline: none;
            border: none;
            background: transparent;
            &:focus {
              outline: none;
              border: none;
              background: transparent;
            }
          }
        }
        .tags {
          font-size: 12px;
          font-family: PingFangSC;
          font-weight: 500;
          padding: 4px;
          border-radius: 2px;
          // margin-left: 25px;
          // margin-left: 10px;

          &-1 {
            color: rgba(237, 161, 51, 1);
            margin-left: 50px;
            background: rgba(237, 161, 51, 0.1);
          }
          &-2 {
            margin-right: 10px;
            color: #6386c1;
            background: rgba(99, 134, 193, 0.1);
            margin-left: 5px;
          }
        }
      }

      .second-line {
        display: flex;
        align-items: center;
        margin-top: 10px;
        .cai-name {
          width: 150px;
          display: flex;
          .strategy-remark {
            // width: 100px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: normal;
            color: rgba(41, 41, 41, 1);
            @extend .text-ellipsis;
            outline: none;
            border: none;
            background: transparent;
            &:focus {
              outline: none;
              border: none;
              background: transparent;
            }
          }
        }
        .cai-names {
          width: 150px;
          display: flex;
          margin-left: 50px;
          .strategy-remark {
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: normal;
            color: rgba(41, 41, 41, 1);
            @extend .text-ellipsis;
            outline: none;
            border: none;
            background: transparent;
            &:focus {
              outline: none;
              border: none;
              background: transparent;
            }
          }
        }
        .cai-desc {
          margin-left: 60px;
        }
        ul {
          display: flex;
          li {
            display: flex;
            margin-right: 20px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: normal;
            color: rgba(157, 170, 191, 1);

            .mt-icons {
              font-size: 12px;
            }
            span {
              vertical-align: text-bottom;
              margin-left: 4px;
              @extend .text-ellipsis;
            }
          }
        }
      }
    }

    .btns-wrap {
      display: flex;
      /deep/ .mt-button {
        margin-left: 20px;
        button {
          background: transparent;
          border-radius: 4px;
          box-shadow: unset;
          padding: 6px 12px 4px;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          border: none;
          color: #00469c;
        }
      }
    }
  }
}

/deep/ div.mt-tabs {
  border: 1px solid #e8e8e8;
  border: 1px solid var(--plugin-tb-border-color);
}
</style>
