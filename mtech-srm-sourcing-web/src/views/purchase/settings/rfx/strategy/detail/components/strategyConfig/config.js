import { i18n } from '@/main.js'
import { dateUtils } from '@mtech-common/utils'
import { createEditInstance } from '@/utils/ej/dataGrid/index'
import { getStrategyConfig } from 'ROUTER_PURCHASE_RFX/config/strategyConfig'
import AllocationRatio from 'ROUTER_PURCHASE_RFX/components/AllocationRatio'

const formatterSelect = function (column, item) {
  const { editConfig, field } = column
  return editConfig.dataSource.find((dataSource) => dataSource.value === item[field])?.text
}

const editConfigSelect = () => ({
  type: 'select',
  fields: { value: 'value', text: 'text' },
  dataSource: [
    { text: i18n.t('是'), value: 1 },
    { text: i18n.t('否'), value: 0 }
  ]
})

export const getColumnData = ({ dataSource }) => {
  const strategyConfig = getStrategyConfig()

  const editInstance = createEditInstance()
    .component('AllocationRatio', AllocationRatio)
    .onInput((ctx, { field }) => {
      const val = ctx.getValueByField(field)

      if (field === 'enableStatus') {
        if (val === 0) {
          ctx.setValueByField('supplierVisible', 0)
          ctx.setOptions('supplierVisible', {
            disabled: true
          })
        } else {
          ctx.setOptions('supplierVisible', {
            disabled: false
          })
        }
      }
    })

  return [
    {
      field: 'strategyName',
      headerText: i18n.t('策略'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text'
        })
      })
    },
    {
      field: 'defaultValue',
      headerText: i18n.t('默认值'),
      formatter: function (column, item) {
        const { field } = column
        const editConfig = strategyConfig?.[item.strategyCode] ?? {}
        const editType = editConfig?.type
        const cellVal = item[field]
        switch (editType) {
          case 'select':
            return editConfig?.dataSource?.find(
              (dataSource) => '' + dataSource.value === '' + cellVal
            )?.text
          case 'datetime':
            if (cellVal && typeof cellVal === 'object') {
              return dateUtils(cellVal).format('YYYY-MM-DD HH:mm:ss')
            } else if (/^\d{13}$/.test(cellVal)) {
              return dateUtils(new Date(Number(cellVal))).format('YYYY-MM-DD HH:mm:ss')
            } else if (cellVal && typeof cellVal === 'string') {
              return dateUtils(new Date(cellVal)).format('YYYY-MM-DD HH:mm:ss')
            }
            return cellVal
          case 'allocationRatio':
            return i18n.t('查看')
          default:
            return cellVal
        }
      },
      edit: editInstance.create({
        dataSource,
        getEditConfig: ({ rowData }) => {
          const strategyCode = rowData.strategyCode
          let value = rowData.defaultValue
          if (strategyCode === 'allocationRatio') {
            const ds = dataSource()
            value = ds.find((e) => e.strategyCode === strategyCode).defaultValue
          }
          return {
            ...(strategyConfig?.[strategyCode] ?? {
              type: 'text'
            }),
            value,
            showClearButton: true
          }
        }
      })
    },
    {
      field: 'editEnable',
      headerText: i18n.t('可以编辑'),
      edit: editInstance.create(),
      formatter: formatterSelect,
      editConfig: editConfigSelect()
    },
    {
      field: 'enableStatus',
      headerText: i18n.t('启用'),
      edit: editInstance.create(),
      formatter: formatterSelect,
      editConfig: editConfigSelect()
    },
    {
      field: 'supplierVisible',
      headerText: i18n.t('供方可见'),
      edit: editInstance.create({
        getEditConfig: ({ rowData }) => {
          return {
            ...editConfigSelect(),
            disabled: Number(rowData.enableStatus) === 0
          }
        }
      }),
      formatter: formatterSelect,
      editConfig: editConfigSelect()
    },
    {
      field: 'sortValue',
      headerText: i18n.t('排序'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          min: 0
        })
      })
    }
  ]
}
