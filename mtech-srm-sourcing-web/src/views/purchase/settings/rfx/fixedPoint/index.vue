<template>
  <div class="common-config">
    <div class="config-title">{{ $t('寻源策略配置') }}</div>
    <div class="config-content">
      <div class="config-item" v-for="(item, index) in strategyInfo" :key="index">
        <div class="strategy-common-label large">{{ item.name }}:</div>
        <mt-radio
          v-model="item.strategyValue"
          :data-source="item.radioDataSource"
          @input="onchang($event, index)"
        ></mt-radio>
      </div>
    </div>
  </div>
</template>
<script>
import Vue from 'vue'
import MtRadio from '@mtech-ui/radio'
Vue.use(MtRadio)

export default {
  /*
  供应商报价数据有效
  public static final String SUPPLIER_QUOTE_PRICE_EFFECTIVE = "supplier_quote_price_effective";
  供应商报价数据有效--仅最新的报价有效
  public static final String SUPPLIER_QUOTE_PRICE_EFFECTIVE_NEW = "0";
  供应商报价数据有效--历史数据有效
  public static final String SUPPLIER_QUOTE_PRICE_EFFECTIVE_HISTORY = "1";
  跳过定点推荐
  public static final String JUMP_FIXED_POINT = "jump_fixed_point";
  跳过定点推荐--否
  public static final String JUMP_FIXED_POINT_NO = "0";
  跳过定点推荐--是
  public static final String JUMP_FIXED_POINT_YES = "1";
*/
  data() {
    return {
      strategyInfo: [
        {
          id: null,
          strategyKey: 'jump_fixed_point',
          strategyValue: '0',
          name: this.$t('跳过定点推荐'),
          radioDataSource: [
            {
              label: this.$t('是'),
              value: '1'
            },
            {
              label: this.$t('否'),
              value: '0'
            }
          ]
        },
        {
          id: null,
          strategyKey: 'supplier_quote_price_effective',
          strategyValue: '0',
          name: this.$t('供应商报价数据有效'),
          radioDataSource: [
            {
              label: this.$t('仅最新的报价有效'),
              value: '0'
            },
            {
              label: this.$t('历史数据有效'),
              value: '1'
            }
          ]
        }
      ]
    }
  },
  mounted() {
    this.getGlobalConfig()
  },

  methods: {
    onchang(e, i) {
      this.$set(this.strategyInfo[i], 'strategyValue', `${e}`)
      let _data = []
      this.strategyInfo.forEach((e) => {
        _data.push({
          id: e.id,
          strategyKey: e.strategyKey,
          strategyValue: e.strategyValue
        })
      })
      this.$API.moduleConfig.batchSaveSourcingGlobalStrategy(_data).then(() => {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        this.getGlobalConfig()
      })
    },
    getGlobalConfig() {
      this.$API.moduleConfig
        .queryBuilderSourcingGlobalStrategy({
          page: {
            current: 1,
            size: 10
          }
        })
        .then((res) => {
          let _records = res?.data?.records
          if (Array.isArray(_records) && _records.length) {
            _records.forEach((r) => {
              this.strategyInfo.forEach((s) => {
                if (r.strategyKey === s.strategyKey) {
                  s.id = r.id
                  s.strategyValue = r.strategyValue
                }
              })
            })
          }
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.strategy-common-label {
  width: 70px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: #465b73;
  text-align: right;
  display: inline-block;
  margin-right: 20px;
  position: relative;
  &.middle {
    width: 100px;
  }
  &.large {
    width: 170px;
    margin-right: 30px;
  }
}
/deep/ .strategy-element {
  border: none;
  border-color: transparent !important;
  position: relative;
  top: 4px;
  &:before,
  &:after {
    display: none;
  }
  .e-float-line {
    display: none;
  }
  .e-control {
    color: #292929;
    border: none;
    border-color: transparent !important;
  }
}
.common-config {
  padding-top: 20px;
  .config-title {
    font-size: 16px;
    color: #292929;
    display: inline-block;
    padding-left: 13px;
    position: relative;
    margin-bottom: 20px;

    &:before {
      content: '';
      position: absolute;
      width: 3px;
      height: 14px;
      background: #6386c1;
      border-radius: 0 2px 2px 0;
      left: 0;
      top: 2px;
    }
  }
  .config-content {
    .config-item {
      margin-bottom: 20px;
      display: flex;
      margin-bottom: 20px;
      align-items: center;
      .select-container {
        width: 400px;
      }
    }
  }
}
</style>
