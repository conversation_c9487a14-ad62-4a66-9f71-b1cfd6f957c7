import dayjs from 'dayjs'
import { getHeadersFileName, download } from '@/utils/utils'
import VxeRemoteSearch from '@/components/VxeRemoteSearch/index.vue'
import cloneDeep from 'lodash/cloneDeep'

// 工具栏配置
export const TOOLBAR_CONFIG = {
  ADD: 'add',
  SAVE: 'save',
  DELETE: 'delete',
  ENABLE: 'enable',
  DISABLE: 'disable',
  IMPORT: 'import',
  EXPORT: 'export'
}

// 状态配置
export const STATUS_CONFIG = {
  DRAFT: 0,
  ENABLED: 1,
  DISABLED: 2
}

// API操作类型
const API_OPERATIONS = {
  DELETE: 'delete',
  ENABLE: 'enable',
  DISABLE: 'disable'
}

export default {
  components: {
    VxeRemoteSearch
  },
  computed: {
    columns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'companyCode',
          title: this.$t('公司'),
          minWidth: 160,
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [<span>{row.companyCode ? `${row.companyCode}-${row.companyName}` : ''}</span>]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.companyCode}
                  options={this.companyOptions}
                  option-props={{ label: 'text', value: 'value' }}
                  placeholder={this.$t('请选择')}
                  filterable
                  clearable
                  transfer
                  disabled={row.status === STATUS_CONFIG.ENABLED || row.status === STATUS_CONFIG.DISABLED}
                  onChange={() => {
                    row.companyName = this.companyOptions.find(
                      (item) => item.value === row.companyCode
                    )?.companyName
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商代码'),
          minWidth: 160,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.supplierCode}
                  placeholder={this.$t('请选择')}
                  fields={{ text: 'supplierName', value: 'supplierCode' }}
                  request-info={{
                    urlPre: 'masterData',
                    url: 'supplierPagedQuery',
                    searchFields: ['supplierCode', 'supplierName'],
                    params: {
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  disabled={row.status === STATUS_CONFIG.ENABLED || row.status === STATUS_CONFIG.DISABLED}
                  onChange={(e) => {
                    row.supplierName = e?.supplierName || null
                    row.supplierTenantId = e?.supplierTenantId || null
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'supplierName',
          title: this.$t('供应商名称'),
          minWidth: 160
        },
        {
          field: 'fixtureCode',
          title: this.$t('治具代码'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='integer'
                  v-model={row.fixtureCode}
                  placeholder={this.$t('请输入')}
                  clearable
                  controls={false}
                  disabled={row.status === STATUS_CONFIG.ENABLED || row.status === STATUS_CONFIG.DISABLED}
                />
              ]
            }
          }
        },
        {
          field: 'fixtureName',
          title: this.$t('治具名称'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.fixtureName}
                  placeholder={this.$t('请输入')}
                  clearable
                  disabled={row.status === STATUS_CONFIG.ENABLED || row.status === STATUS_CONFIG.DISABLED}
                />
              ]
            }
          }
        },
        {
          field: 'materialCode',
          title: this.$t('物料编码'),
          minWidth: 160,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.materialCode}
                  placeholder={this.$t('请选择')}
                  fields={{ text: 'itemName', value: 'itemCode' }}
                  request-info={{
                    urlPre: 'masterData',
                    url: 'getItemListUrlList',
                    searchFields: ['itemCode', 'itemName'],
                    params: {
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  disabled={row.status === STATUS_CONFIG.ENABLED || row.status === STATUS_CONFIG.DISABLED}
                  onChange={(e) => {
                    row.materialName = e?.itemName || null
                    row.categoryCode = e?.categoryCode || null
                    row.categoryName = e?.categoryName || null
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'materialName',
          title: this.$t('物料名称'),
          minWidth: 140
        },
        {
          field: 'categoryCode',
          title: this.$t('品类编码'),
          minWidth: 140,
        },
        {
          field: 'categoryName',
          title: this.$t('品类名称'),
          minWidth: 140
        },
        {
          field: 'unitPriceUntaxed',
          title: this.$t('治具价格'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='integer'
                  v-model={row.unitPriceUntaxed}
                  placeholder={this.$t('请输入')}
                  clearable
                  disabled={row.status === STATUS_CONFIG.ENABLED || row.status === STATUS_CONFIG.DISABLED}
                />
              ]
            }
          }
        },
        {
          field: 'costSharingNumber',
          title: this.$t('分摊数量'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='integer'
                  v-model={row.costSharingNumber}
                  placeholder={this.$t('请输入')}
                  clearable
                  disabled={row.status === STATUS_CONFIG.ENABLED || row.status === STATUS_CONFIG.DISABLED}
                />
              ]
            }
          }
        },
        {
          field: 'costSharingUnitPriceUntaxed',
          title: this.$t('单片分摊价格'),
          minWidth: 120
        },
        {
          field: 'poInOutNumber',
          title: this.$t('累计入库数量'),
          minWidth: 120
        },
        {
          field: 'emailScene',
          title: this.$t('邮件触发条件'),
          minWidth: 120
        },
        {
          field: 'status',
          title: this.$t('状态'),
          minWidth: 120,
          formatter: ({ row }) => {
            const statusOption = this.statusOptions.find((option) => option.value === row.status)
            return statusOption ? statusOption.text : ''
          }
        },
        {
          field: 'modifyDate',
          title: this.$t('修改日期'),
          minWidth: 120
        },
        {
          field: 'remark',
          title: this.$t('备注'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.remark} placeholder={this.$t('请输入')} clearable />]
            }
          }
        },
        {
          field: 'email',
          title: this.$t('邮件抄送人员'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.email} placeholder={this.$t('请输入')} clearable />]
            }
          }
        },
        {
          field: 'createUserName',
          title: this.$t('创建人'),
          minWidth: 120
        },
        {
          field: 'createTime',
          title: this.$t('创建日期'),
          minWidth: 160
        },
        {
          field: 'updateUserName',
          title: this.$t('更新人'),
          minWidth: 120
        },
        {
          field: 'updateTime',
          title: this.$t('更新日期'),
          minWidth: 160
        }
      ]
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      editConfig: {
        enabled: true,
        trigger: 'click',
        mode: 'row',
        showStatus: true,
        beforeEditMethod: this.beforeEditMethod
      },
      editRules: {
        companyCode: [{ required: true, message: this.$t('请选择公司') }],
        supplierCode: [{ required: true, message: this.$t('请选择供应商') }],
        fixtureCode: [
          { required: true, message: this.$t('请输入治具代码') },
          { pattern: /^\d+$/, message: this.$t('治具代码必须为数字') }
        ],
        fixtureName: [{ required: true, message: this.$t('请输入治具名称') }],
        materialCode: [{ required: true, message: this.$t('请选择物料') }],
        categoryCode: [{ required: true, message: this.$t('请选择品类') }],
        unitPriceUntaxed: [{ required: true, message: this.$t('请输入治具价格') }],
        costSharingNumber: [{ required: true, message: this.$t('请输入分摊数量') }]
      },
      _isForceClearEdit: false
    }
  },

  methods: {
    beforeEditMethod({ row }) {
      if (this.tableRef.getInsertRecords().length !== 0 && !row.id?.includes('row_')) {
        this.$toast({ content: this.$t('请先保存新增数据'), type: 'warning' })
        return false
      }
      return true
    },
    // 统一的API调用处理
    async handleApiOperation(api, params, successMessage) {
      try {
        const res = await api(params)
        if (res.code === 200) {
          this.$toast({ content: this.$t(successMessage), type: 'success' })
          await this.handleSearch()
          return true
        }
        return false
      } catch (error) {
        console.error('操作失败:', error)
        this.$toast({ content: error || this.$t('操作失败，请重试'), type: 'error' })
        return false
      }
    },

    // 统一的批量操作处理
    async handleBatchOperation(selectedRecords, operationType) {
      const ids = selectedRecords.map((v) => v.id)
      const apiMap = {
        [API_OPERATIONS.DELETE]: this.$API.Jig.deleteJigMaterialRelationshipApi,
        [API_OPERATIONS.ENABLE]: this.$API.Jig.enableJigMaterialRelationshipApi,
        [API_OPERATIONS.DISABLE]: this.$API.Jig.disableJigMaterialRelationshipApi
      }
      const messageMap = {
        [API_OPERATIONS.DELETE]: '删除成功',
        [API_OPERATIONS.ENABLE]: '启用成功',
        [API_OPERATIONS.DISABLE]: '停用成功'
      }

      return this.handleApiOperation(apiMap[operationType], { ids }, messageMap[operationType])
    },

    // 日期变更处理
    handleDateChange(e, field) {
      const { startDate, endDate } = e
      this.searchFormModel[`${field}S`] = startDate ? this.getUnix(dayjs(startDate)) : null
      this.searchFormModel[`${field}E`] = endDate ? this.getUnix(dayjs(endDate)) : null
    },

    // 重置表单
    handleReset() {
      Object.keys(this.searchFormModel).forEach((key) => {
        this.searchFormModel[key] = null
      })
      this.handleSearch()
    },

    // 搜索处理
    async handleSearch() {
      // 如果有正在编辑的行，先取消编辑状态
      const editRow = this.tableRef.getEditRecord()
      if (editRow) {
        // 设置标记，表示是强制取消编辑状态
        this._isForceClearEdit = true
        // 先取消编辑状态，再恢复数据
        this.tableRef.clearEdit()
        this.tableRef.revertData()
        // 等待DOM更新完成
        await this.$nextTick()
        // 清除标记
        this._isForceClearEdit = false
      }

      const valid = await this.$refs.searchFormRef.validate()
      if (valid) {
        this.currentPage = 1
        await this.getTableData()
      } else {
        this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
      }
    },

    // 分页处理
    async handlePageChange(type, value) {
      if (type === 'size') {
        this.currentPage = 1
        this.pageSettings.pageSize = value
      } else {
        this.currentPage = value
      }
      await this.getTableData()
    },

    // 获取表格数据
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }

      this.loading = true
      try {
        const res = await this.$API.Jig.pageJigMaterialRelationshipApi(params)
        if (res.code === 200) {
          this.updateTableData(res.data)
        }
      } catch (error) {
        console.error('获取数据失败:', error)
        this.$toast({ content: error || this.$t('获取数据失败'), type: 'error' })
      } finally {
        this.loading = false
      }
    },

    // 更新表格数据
    updateTableData(data) {
      const total = data?.total || 0
      this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
      this.pageSettings.totalRecordsCount = Number(total)
      this.tableData = data?.records || []
    },

    // 工具栏操作处理
    handleToolbarOperation(e, selectedRecords) {
      const operationMap = {
        [TOOLBAR_CONFIG.ADD]: () => this.handleAdd(),
        [TOOLBAR_CONFIG.DELETE]: () =>
          this.confirmOperation('删除', () =>
            this.handleBatchOperation(selectedRecords, API_OPERATIONS.DELETE)
          ),
        [TOOLBAR_CONFIG.ENABLE]: () =>
          this.confirmOperation('启用', () =>
            this.handleBatchOperation(selectedRecords, API_OPERATIONS.ENABLE)
          ),
        [TOOLBAR_CONFIG.DISABLE]: () =>
          this.confirmOperation('停用', () =>
            this.handleBatchOperation(selectedRecords, API_OPERATIONS.DISABLE)
          ),
        [TOOLBAR_CONFIG.IMPORT]: () => this.handleImport(),
        [TOOLBAR_CONFIG.EXPORT]: () => {
          e.loading = true
          this.handleExport(e)
        }
      }

      const operation = operationMap[e.code]
      if (operation) {
        operation()
      }
    },

    // 工具栏按钮点击处理
    handleClickToolBar(item) {
      const selectedRecords = this.tableRef.getCheckboxRecords()

      // 根据按钮类型判断是否需要选中记录
      const needSelected = [
        TOOLBAR_CONFIG.DELETE,
        TOOLBAR_CONFIG.ENABLE,
        TOOLBAR_CONFIG.DISABLE
      ].includes(item.code)
      if (needSelected && (!selectedRecords || selectedRecords.length === 0)) {
        this.$toast({ content: this.$t('请选择要操作的记录'), type: 'warning' })
        return
      }

      if (item.code === TOOLBAR_CONFIG.SAVE) {
        if (
          this.tableRef.getInsertRecords().length === 0 &&
          this.tableRef.getUpdateRecords().length === 0
        ) {
          this.$toast({ content: this.$t('暂无需要保存的数据'), type: 'warning' })
          return
        }
        let records = this.tableRef.getInsertRecords().concat(this.tableRef.getUpdateRecords())
        this.handleSave(records[0])
      }

      // 其他按钮通过handleToolbarOperation处理
      this.handleToolbarOperation(item, selectedRecords)
    },

    // 确认操作
    confirmOperation(title, callback) {
      this.$dialog({
        data: {
          title: this.$t('确认'),
          message: this.$t(`是否确认${title}选中的记录？`)
        },
        success: () => {
          callback()
        }
      })
    },

    // 新增处理
    handleAdd() {
      if (this.tableRef.getInsertRecords().length !== 0) {
        this.$toast({ content: this.$t('请先保存新增数据'), type: 'warning' })
        return
      }

      if (this.tableRef.getUpdateRecords().length !== 0) {
        this.$toast({ content: this.$t('请先保存编辑数据'), type: 'warning' })
        return
      }

      const item = {
        emailScene: this.$t(
          '50%的时候，发一封邮件，提醒采购员去谈判看看能不能降价，90%再发一次提醒修改，100%就每天发，直到价格修改'
        )
      }

      this.tableRef.insert([item])
      this.$nextTick(() => {
        const currentViewRecords = this.tableRef.getTableData().visibleData
        this.tableRef.setEditRow(currentViewRecords[0])
      })
    },
    editComplete(args) {
      // 如果是强制取消编辑状态，不执行保存操作
      if (this._isForceClearEdit) {
        return
      }

      const { row, $event } = args
      // 只有在以下条件都满足时才执行保存：
      // 1. 存在事件对象
      // 2. 事件类型是blur（失去焦点）
      // 3. 不是点击表格外部区域触发的blur
      if ($event && !$event.target.closest('.vxe-table--body-wrapper')) {
        // 校验必填
        this.tableRef.validate([row]).then((valid) => {
          if (valid) {
            this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
            return
          }
          this.handleSave(row)
        })
      }
    },
    handleSave(row) {
      // 防止重复提交
      if (row._saving) {
        return
      }
      row._saving = true

      let params = cloneDeep(row)
      if (params.id?.includes('row_')) {
        params.id = null
      }
      this.$API.Jig.saveJigMaterialRelationshipApi(params)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
            this.handleSearch()
          }
        })
        .catch(() => {
          // 当出现错误时，指定行进入编辑状态
          this.$nextTick(() => {
            this.tableRef.setEditRow(row)
          })
        })
        .finally(() => {
          // 清除保存状态标记
          row._saving = false
        })
    },

    // 导入处理
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.Jig.importJigMaterialRelationshipApi,
          downloadTemplateApi: this.$API.Jig.importDownloadJigMaterialRelationshipApi,
          paramsKey: 'file'
        },
        success: () => this.handleSearch()
      })
    },

    // 导出处理
    async handleExport(e) {
      try {
        const params = {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        }
        const res = await this.$API.Jig.exportJigMaterialRelationshipApi(params)
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      } catch (error) {
        console.error('导出失败:', error)
        this.$toast({ content: this.$t('导出失败'), type: 'error' })
      } finally {
        e.loading = false
      }
    }
  }
}
