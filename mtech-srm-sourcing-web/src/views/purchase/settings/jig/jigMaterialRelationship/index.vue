<!-- 物料治具关系表 -->
<template>
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item prop="companyCodeList" :label="$t('公司编码')" label-style="top">
          <mt-select
            v-model="searchFormModel.companyCodeList"
            :data-source="companyOptions"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierBatchCodes" :label="$t('供应商编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.supplierBatchCodes"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="fixtureCode" :label="$t('治具编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.fixtureCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="materialBatchCodes" :label="$t('物料编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.materialBatchCodes"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="categoryBatchCodes" :label="$t('品类编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.categoryBatchCodes"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.status"
            :data-source="statusOptions"
            :placeholder="$t('请选择')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('创建人')" prop="createUserName">
          <mt-input
            v-model="searchFormModel.createUserName"
            :placeholder="$t('支持模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            @change="(e) => dateChange(e, 'createTime')"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('更新人')" prop="updateUserName">
          <mt-input
            v-model="searchFormModel.updateUserName"
            :placeholder="$t('支持模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="updateTime" :label="$t('更新时间')">
          <mt-date-range-picker
            v-model="searchFormModel.updateTime"
            @change="(e) => dateChange(e, 'updateTime')"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="scTableRef"
      grid-id="2033f6d6-9577-1cbf-0ea3-9493de7a05b5"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      keep-source
      :sortable="false"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
      @edit-closed="editComplete"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="(page) => handlePageChange('current', page)"
      @sizeChange="(size) => handlePageChange('size', size)"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin, { TOOLBAR_CONFIG, STATUS_CONFIG } from './config/mixin'

export default {
  components: {
    CollapseSearch,
    ScTable
  },
  mixins: [mixin],
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      toolbar: [
        { code: TOOLBAR_CONFIG.ADD, name: this.$t('新增'), status: 'info', loading: false },
        { code: TOOLBAR_CONFIG.SAVE, name: this.$t('保存'), status: 'info', loading: false },
        { code: TOOLBAR_CONFIG.DELETE, name: this.$t('删除'), status: 'info', loading: false },
        { code: TOOLBAR_CONFIG.ENABLE, name: this.$t('启用'), status: 'info', loading: false },
        { code: TOOLBAR_CONFIG.DISABLE, name: this.$t('停用'), status: 'info', loading: false },
        { code: TOOLBAR_CONFIG.IMPORT, name: this.$t('导入'), status: 'info', loading: false },
        { code: TOOLBAR_CONFIG.EXPORT, name: this.$t('导出'), status: 'info', loading: false }
      ],
      statusOptions: [
        { text: this.$t('草稿'), value: STATUS_CONFIG.DRAFT },
        { text: this.$t('启用'), value: STATUS_CONFIG.ENABLED },
        { text: this.$t('停用'), value: STATUS_CONFIG.DISABLED }
      ],
      companyOptions: []
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    }
  },
  created() {
    this.initData()
  },
  methods: {
    // 初始化数据
    async initData() {
      this.setDefaultSearchFormModel()
      await this.getCompanyOptions()
      await this.getTableData()
    },
    // 获取公司选项
    async getCompanyOptions() {
      try {
        const res = await this.$API.masterData.getCompanyList()
        if (res.code === 200) {
          this.companyOptions = this.formatCompanyOptions(res.data)
        }
      } catch (error) {
        console.error('获取公司列表失败:', error)
        this.$toast({ content: this.$t('获取公司列表失败'), type: 'error' })
      }
    },
    // 格式化公司选项
    formatCompanyOptions(data) {
      return data.map(item => ({
        ...item,
        text: `${item.companyCode}-${item.companyName}`,
        value: item.companyCode
      }))
    },
    // 日期变更处理
    dateChange(e, field) {
      const { startDate, endDate } = e
      this.searchFormModel[`${field}S`] = startDate ? this.getUnix(dayjs(startDate)) : null
      this.searchFormModel[`${field}E`] = endDate ? this.getUnix(dayjs(endDate)) : null
    },
    // 重置表单
    handleReset() {
      Object.keys(this.searchFormModel).forEach(key => {
        this.searchFormModel[key] = null
      })
      this.setDefaultSearchFormModel()
      this.handleSearch()
    },
    setDefaultSearchFormModel() {
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      if (userInfo) {
        this.searchFormModel.createUserName = userInfo.accountName
      }
    },
    // 搜索处理
    async handleSearch() {
      const valid = await this.$refs.searchFormRef.validate()
      if (valid) {
        this.currentPage = 1
        await this.getTableData()
      } else {
        this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
      }
    }
  }
}
</script>
