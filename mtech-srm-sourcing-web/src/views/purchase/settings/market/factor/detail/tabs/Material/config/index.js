import { i18n, permission } from '@/main.js'
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'del', icon: 'icon_solid_edit', title: i18n.t('删除') },
  { id: 'start', icon: 'icon_solid_Createorder', title: i18n.t('保存') }
]
const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'spec',
    headerText: i18n.t('行号')
  },
  {
    field: 'required',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'requiredName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'decimalPlaces',
    headerText: i18n.t('品类编码')
  },
  {
    field: 'maxValue',
    headerText: i18n.t('品类名称')
  }
]
export const pageConfig = [
  {
    toolbar,
    gridId: permission.gridId['purchase']['factor']['detail']['tabs']['category'],
    grid: {
      allowFiltering: true,
      columnData,
      dataSource: [
        {
          spec: '1',
          required: 'wu11',
          requiredName: i18n.t('物料1'),
          decimalPlaces: 'pinlei1',
          maxValue: '品类1'
        }
      ]
    }
  }
]
