<template>
  <div class="supplier-box mt-flex">
    <div class="miam-container">
      <div class="operate-bar">
        <div class="op-item mt-flex" @click="saveDetail">{{ $t('保存') }}</div>
        <div class="op-item mt-flex">{{ $t('启用') }}</div>
        <div class="op-item mt-flex">{{ $t('停用') }}</div>
        <div class="op-item mt-flex" @click="backToBusinessConfig">
          {{ $t('返回') }}
        </div>
      </div>
      <!-- 顶部主要信息 -->
      <div class="mian-info">
        <mt-form ref="ruleForm" :model="formObject">
          <mt-form-item ref="formItemarea" :label="$t('成本因子编码：')">
            <mt-input
              width="100%"
              type="text"
              v-model="formObject.marketFactorCode"
              :placeholder="$t('请输入成本因子编码')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item ref="formItemarea" :label="$t('成本因子名称：')">
            <mt-input
              width="100%"
              type="text"
              v-model="formObject.marketFactorName"
              :placeholder="$t('请输入成本因子名称')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item ref="formItemarea" :label="$t('规格：')">
            <mt-input
              width="100%"
              type="text"
              v-model="formObject.marketFactorSpec"
              :placeholder="$t('请输入规格')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item ref="formItemarea" :label="$t('品牌：')">
            <mt-input
              width="100%"
              type="text"
              v-model="formObject.marketFactorBrand"
              :placeholder="$t('请输入品牌')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item ref="formItemarea" :label="$t('属性：')">
            <mt-select
              width="100%"
              v-model="formObject.attribute"
              float-label-type="Never"
              :data-source="attributeList"
              :placeholder="$t('请选择专家来源')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item ref="formItemarea" :label="$t('单位：')">
            <mt-select
              width="100%"
              ref="unitRef"
              v-model="formObject.unitCode"
              float-label-type="Never"
              :data-source="unitList"
              :fields="{ text: 'unitName', value: 'unitCode' }"
              :placeholder="$t('请选择单位')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item ref="formItemarea" :label="$t('报价上限：')">
            <mt-input width="100%" v-model="formObject.quoteUpperLimit" type="text"></mt-input>
          </mt-form-item>
          <mt-form-item ref="formItemarea" :label="$t('报价下限：')">
            <mt-input
              width="100%"
              type="text"
              v-model="formObject.quoteLowerLimit"
              :placeholder="$t('请输入报价')"
            ></mt-input>
          </mt-form-item>

          <mt-form-item ref="formItemarea" :label="$t('启用：')">
            <mt-select
              width="100%"
              v-model="formObject.status"
              :placeholder="$t('请选择启用')"
              :data-source="statusList"
            ></mt-select>
          </mt-form-item>
          <mt-form-item ref="formItemarea" :label="$t('备注：')">
            <mt-input
              width="100%"
              type="text"
              v-model="formObject.quoteUpperLimit"
              :placeholder="$t('字数不超过200字')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
      <div class="relation-ships">
        <div class="tab-box mt-flex">
          <mt-tabs
            :tab-id="$utils.randomString()"
            :e-tab="false"
            :data-source="tabSource"
            @handleSelectTab="handleSelectTab"
          ></mt-tabs>
        </div>
        <div class="config-container">
          <!-- 0.公司  -->
          <tab-CompanyRel v-if="tabIndex == 0"></tab-CompanyRel>
          <!-- 1.物料  -->
          <tab-Material v-if="tabIndex == 1"></tab-Material>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  components: {
    //成本模型详情页
    tabCompanyRel: () =>
      import(
        /* webpackChunkName: "router/purchase/settings/cost/detail/tabs/params" */ './tabs/CompanyRel/index.vue'
      ),
    //成本模型详情页
    tabMaterial: () =>
      import(
        /* webpackChunkName: "router/purchase/settings/cost/detail/tabs/details" */ './tabs/Material/index.vue'
      )
  },
  data() {
    return {
      tabSource: [
        {
          title: this.$t('公司')
        },
        {
          title: this.$t('物料')
        }
      ],
      tabIndex: 0,
      formObject: {
        attribute: null, //	属性 0:原材料 1:工序
        groupTypeId: null, //组别id
        groupTypeName: null, //	组别名称
        marketFactorBrand: null, //品牌
        marketFactorCode: null, //行情因子编码
        marketFactorName: null, //行情因子名称
        marketFactorSpec: null, //行情因子规格
        priceSourceType: null, //价格来源 0:线上寻源 1:线下寻源
        quoteLowerLimit: null, //报价下限
        quoteUpperLimit: null, //报价上限
        remark: null, //备注
        status: null, //	单据状态 0:有效 1:无效
        unitCode: null, //单位编码
        unitName: null, //单位名称
        companyRelList: [], //新增的成本因子授权公司
        removeCompanyRelList: [], //移除的成本因子授权公司
        removeSourcingFileList: [], //删除的附件
        sourcingFileList: [] //新增的附件
        // id: null, //id
        // tenantId: null, //tenantId
        // version: null, //版本号
      },
      companyRelList: [], //新增的成本因子授权公司
      removeCompanyRelList: [], //移除的成本因子授权公司
      removeSourcingFileList: [], //删除的附件
      sourcingFileList: [], //新增的附件
      unitList: [], //单位列表
      //属性 0:原材料 1:工序
      attributeList: [
        { text: this.$t('原材料'), value: 0 },
        { text: this.$t('工序'), value: 1 }
      ],
      //单据状态 0:有效 1:无效
      statusList: [
        { text: this.$t('有效'), value: 0 },
        { text: this.$t('无效'), value: 1 }
      ]
    }
  },
  methods: {
    saveDetail() {
      console.log('save-detail')
      // this.$toast({
      //   content: "保存操作--未处理",
      //   type: "warning",
      // });
    },
    backToBusinessConfig() {
      this.$router.go(-1)
    },
    getCommonList() {
      this.$API.masterData.pagedQueryUnit().then((res) => {
        this.unitList = res.data.records
      })
    },
    handleSelectTab(e) {
      this.tabIndex = e
    }
  },
  mounted() {
    this.formObject = localStorage?.marketFactorInfo
      ? JSON.parse(localStorage.marketFactorInfo)
      : {}
    this.getCommonList()
  }
}
</script>

<style lang="scss">
.supplier-box {
  .mt-form-item-label {
    display: flex !important;
    justify-content: space-between !important;
  }
}
</style>
<style lang="scss" scoped>
.operate-bar {
  height: 60px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  color: #4f5b6d;
  // font-size: 14px;
  .op-item {
    cursor: pointer;
    // color: #4f5b6d;
    align-items: center;
    margin-right: 20px;
    align-items: center;
    background-color: rgba(0, 0, 0, 0);
    border-color: rgba(0, 0, 0, 0);
    color: #00469c;
  }
}
/deep/.mt-form-item {
  width: calc(20% - 20px);
  min-width: 200px;
  display: inline-flex;
  margin-right: 20px;
}
.supplier-box {
  position: absolute;
  width: 100%;
  min-height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: #fff;
  overflow: auto;
  -webkit-overflow-scrolling: scroll;

  .flex1 {
    flex: 1;
  }

  .miam-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    background: #fff;
    padding: 0 20px;

    .flex-d-c {
      flex-direction: column;
    }

    .mian-info {
      background: rgba(255, 255, 255, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      // padding: 20px;

      .normal-title {
        width: 100%;
        height: 14px;
        line-height: 14px;
        font-size: 14px;
        color: #292929;
        font-family: PingFangSC;
        font-weight: 500;

        &:before {
          content: ' ';
          display: inline-block;
          vertical-align: middle;
          width: 2px;
          height: 10px;
          background: rgba(0, 70, 156, 1);
          border-radius: 1px;
          margin-right: 10px;
        }
      }

      .flex-d-c {
        flex-direction: column;
      }

      .input-item {
        margin-top: 20px;
        padding-right: 50px;
        .label-txt {
          height: 40px;
          width: 130px;
          font-size: 14px;
          // line-height: 40px;
          color: #292929;
        }
        .label-value {
          height: 40px;
          width: 130px;
          font-size: 14px;
          // line-height: 40px;
          color: #35404e;
        }
        .select-container {
          height: 40px;
        }
        .e-label {
          color: #35404e;
        }
        .label-text {
          color: #35404e;
        }
      }
      .input-item /deep/ .normal-width {
        width: 240px;
      }
      .input-item /deep/ .e-radio + label .e-label {
        color: #35404e;
      }
    }

    .relation-ships {
      flex: 1;
      background: rgba(255, 255, 255, 1);
      border-radius: 8px;
      box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
      // padding: 20px;
      margin-top: 20px;
      .config-container {
        height: calc(100% - 40px);
      }
      .tab-box {
        width: 100%;
        height: 40px;
        position: relative;

        .tab-item {
          font-size: 14px;
          height: 40px;
          line-height: 40px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(40, 41, 41, 1);
          padding: 0 38px;
          cursor: pointer;
        }
        .active {
          font-size: 16px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(0, 70, 156, 1);
          border-bottom: 4px solid #00469c;
        }

        .right-btn {
          height: 40px;
          position: absolute;
          right: 0;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          transform: all 0.6s ease-in;
          i {
            margin-right: 4px;
            color: #4f5b6d;
          }
          .op-item {
            color: #4f5b6d;
            align-items: center;
            margin-right: 20px;
            align-items: center;
            cursor: pointer;
          }
          .add-new {
            i {
              color: #6386c1;
            }
            color: #6386c1;
          }
        }
      }
      .tab-content {
        .grid-search {
          height: 60px;
          line-height: 60px;
          justify-content: flex-end;
          .search-box {
            .label-txt {
              font-size: 14px;
              font-family: PingFangSC;
              font-weight: normal;
            }
          }
        }
        /deep/ .common-template-page .page-grid-container {
          padding: 0 !important;
        }
      }
    }
  }

  .grid-content {
    padding-top: 20px;
  }
}
</style>
