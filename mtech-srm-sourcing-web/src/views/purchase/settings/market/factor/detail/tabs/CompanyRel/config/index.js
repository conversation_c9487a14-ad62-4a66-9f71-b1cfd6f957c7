import { i18n, permission } from '@/main.js'
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'del', icon: 'icon_solid_edit', title: i18n.t('删除') },
  { id: 'start', icon: 'icon_solid_Createorder', title: i18n.t('保存') }
]
const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'spec',
    headerText: i18n.t('行号')
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'maxValue',
    headerText: i18n.t('工厂')
  }
]
export const pageConfig = [
  {
    toolbar,
    gridId: permission.gridId['purchase']['factor']['detail']['tabs']['company'],
    grid: { allowFiltering: true, columnData, dataSource: [] }
  }
]
// export const pageConfig = (url) => [
//   {
//     lineSelection: true,
//     toolbar,
//     grid: { allowFiltering: true,
//       columnData,
//       //
//       // editSettings,
//       asyncConfig: {
//         url,
//       },
//     },
//   },
// ];
