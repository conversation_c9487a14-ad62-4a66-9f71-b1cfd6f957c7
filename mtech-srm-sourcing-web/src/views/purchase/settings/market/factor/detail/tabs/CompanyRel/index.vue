<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      pageConfig: pageConfig,
      companyRelList: [], //新增的成本因子授权公司
      removeCompanyRelList: [] //移除的成本因子授权公司
    }
  },
  mounted() {},
  methods: {
    //表格按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()

      if (
        _selectGridRecords.length <= 0 &&
        (e.toolbar.id == 'start' ||
          e.toolbar.id == 'stop' ||
          e.toolbar.id == 'start' ||
          e.toolbar.id == 'del')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      if (e.toolbar.id == 'Add') {
        this.handleAddParams()
      } else if (e.toolbar.id == 'del') {
        this.handleBatchDeleteParams(_selectGridRecords)
      } else if (e.toolbar.id == 'stop') {
        // this.handleBatchUpdateStop(_selectGridRecords);
      }
    },
    //新增配置
    handleAddParams() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/settings/cost/detail/params/components/paramsDialog" */ './components/index.vue'
          ),
        data: {
          localList: this.companyRelList
        },
        success: (res) => {
          let { companyRelList, removeCompanyRelList } = res
          this.companyRelList = companyRelList
          this.removeCompanyRelList = removeCompanyRelList
        }
      })
    },
    //批量删除操作
    handleBatchDeleteParams(_selectGridRecords) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      this.handleDeleteParams(_selectIds)
    },
    //执行删除操作
    handleDeleteParams(idList) {
      let _params = { idList }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除数据？')
        },
        success: () => {
          this.$API.costModel.preservationCostModelDelete(_params).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    }
  }
}
</script>
