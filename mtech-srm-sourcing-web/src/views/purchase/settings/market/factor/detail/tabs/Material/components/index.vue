<template>
  <div>
    <mt-dialog
      ref="dialog"
      css-class="create-proj-dialog"
      :buttons="buttons"
      :header="header"
      @beforeClose="cancel"
    >
      <div class="dialog-content">
        <mt-form ref="dialogRef" :model="formObjects">
          <mt-form-item prop="required" :label="$t('物料编码')">
            <mt-input
              v-model="formObjects.required"
              type="text"
              :placeholder="$t('请输入物料编码')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="requiredName" :label="$t('物料名称')">
            <mt-input
              v-model="formObjects.requiredName"
              type="text"
              :placeholder="$t('请输入物料名称')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="decimalPlaces" :label="$t('物料编码')">
            <mt-input
              v-model="formObjects.decimalPlaces"
              type="text"
              :placeholder="$t('请输入物料编码')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="maxValue" :label="$t('品类名称')">
            <mt-input
              v-model="formObjects.maxValue"
              type="text"
              :placeholder="$t('请输入品类名称')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </mt-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formObjects: {
        decimalPlaces: null,
        id: 0,
        maxValue: null,
        required: null,
        requiredName: null,
        spec: ''
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    confirm() {
      // this.$refs.dialogRef.validate((valid) => {
      //   // if (valid) {
      //   //   let params = { ...this.formObjects };
      //   // }
      // });
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  padding-top: 20px;
}
</style>
