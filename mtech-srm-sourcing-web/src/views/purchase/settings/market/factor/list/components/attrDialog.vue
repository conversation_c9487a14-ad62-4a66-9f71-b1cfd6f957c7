// 行情因子新增
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="marketFactorSpec" :label="$t('属性代码')">
          <mt-input
            v-model="formObject.marketFactorSpec"
            float-label-type="Never"
            :placeholder="$t('请输入属性代码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="marketFactorBrand" :label="$t('属性名称')">
          <mt-input
            v-model="formObject.marketFactorBrand"
            float-label-type="Never"
            :placeholder="$t('请输入属性名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="unitCode" :label="$t('父类')">
          <mt-select
            ref="unitRef"
            v-model="formObject.unitCode"
            float-label-type="Never"
            :data-source="unitList"
            :fields="{ text: 'unitName', value: 'unitCode' }"
            :placeholder="$t('请选择父类')"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formRules: {},
      formObject: {},
      editStatus: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData[0],
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.formObject = [{ ...this.modalData.data }]
    }
  },
  methods: {
    confirm() {},
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/ .div-auth {
    background: #fafafa;
    height: 35px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    color: #0f0f0f;
    font-size: 12px;
    cursor: pointer;
    border-radius: 5px;
  }
}
</style>
