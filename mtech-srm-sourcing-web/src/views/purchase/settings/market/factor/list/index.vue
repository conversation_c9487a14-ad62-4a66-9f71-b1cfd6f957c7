<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      @viewFileList="viewFileList"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      pageConfig: pageConfig(this.$API.marketFactor.getMarketFactorList)
    }
  },
  methods: {
    //表格按钮-点击事件
    viewFileList(data) {
      let fileList = data.sourcingFileList
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "components/Upload/uploaderDialog" */ 'COMPONENTS/Upload/uploaderDialog.vue'
          ),
        data: {
          fileData: fileList,
          isView: true, // 是否为预览
          required: false, // 是否必须
          title: this.$t('查看附件')
        },
        success: () => {}
      })
    },
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (
        _selectGridRecords.length < 1 &&
        (e.toolbar.id == 'start' || e.toolbar.id == 'stop' || e.toolbar.id == 'del')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        if (e.tabIndex == 0) {
          this.handleAddFactor()
        } else {
          this.handleAddAttr()
        }
      } else if (e.toolbar.id == 'start') {
        this.handleBatchStart(_selectGridRecords)
      } else if (e.toolbar.id == 'stop') {
        this.handleBatchStop(_selectGridRecords)
      }
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      if (e.tool.id == 'start') {
        // 单据状态 0:有效 1:无效
        this.handleUpdateStatus([e.data.id], 0)
      } else if (e.tool.id == 'stop') {
        this.handleUpdateStatus([e.data.id], 1)
      }
    },
    //新增配置
    handleAddFactor() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/settings/market/factor/components/factorDialog" */ './components/factorDialog.vue'
          ),
        data: {
          title: this.$t('新增行情因子')
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //新增属性大类
    handleAddAttr() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/settings/market/factor/components/attrDialog" */ './components/attrDialog.vue'
          ),
        data: {
          title: this.$t('新增属性大类')
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //批量启用操作
    handleBatchStart(_selectGridRecords) {
      // 单据状态 0:有效 1:无效
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      let _disableStatusRecords = _selectGridRecords.filter((s) => {
        return s.status === 0
      })
      if (_disableStatusRecords.length > 0) {
        //选中数据中，存在‘status = 0’启用状态的数据
        this.$toast({
          content: this.$t("只有'禁用'状态可执行'启用'操作"),
          type: 'warning'
        })
        return
      } else {
        this.handleUpdateStatus(_selectIds, 0)
      }
    },
    //批量禁用操作
    handleBatchStop(_selectGridRecords) {
      // 单据状态 0:有效 1:无效
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      let _disableStatusRecords = _selectGridRecords.filter((s) => {
        return s.status === 1
      })
      if (_disableStatusRecords.length > 0) {
        //选中数据中，存在‘status = 1’禁用状态的数据
        this.$toast({
          content: this.$t("只有'启用'状态可执行'禁用'操作"),
          type: 'warning'
        })
        return
      } else {
        this.handleUpdateStatus(_selectIds, 1)
      }
    },
    // 更新状态(行内操作+批量操作)
    handleUpdateStatus(idList, status) {
      // 单据状态 0:有效 1:无效
      let _params = {
        idList
      }
      let _statusMap = [this.$t('启用'), this.$t('禁用')]
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认更新状态为${_statusMap[status]}?`)
        },
        success: () => {
          let _url = status === 0 ? 'enableFactor' : 'disableFactor'
          this.$API.marketFactor[_url](_params).then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    //单元格标题点击
    handleClickCellTitle(e) {
      if (e.field == 'marketFactorCode') {
        localStorage.marketFactorInfo = JSON.stringify(e.data)
        this.$router.push({
          name: `purchase-settings-factor-detail`,
          query: {
            configId: e.data.id
          }
        })
      } else if (e.field == 'companyAuth') {
        // 公司授权的弹框
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "components/market/companyAuth" */ 'COMPONENTS/market/companyAuth/index.vue'
            ),
          data: {
            data: e.data,
            queryId: 'marketFactorId',
            getAuthList: this.$API.marketFactor.getAuthCompanyList
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      } else if (e.field == 'operatingRecords') {
        // 操作记录的弹框
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "components/market/operatingRecords" */ 'COMPONENTS/market/operatingRecords/index.vue'
            ),
          data: {
            title: this.$t('操作记录'),
            data: e.data
          },
          success: () => {}
        })
      }
    }
  }
}
</script>
