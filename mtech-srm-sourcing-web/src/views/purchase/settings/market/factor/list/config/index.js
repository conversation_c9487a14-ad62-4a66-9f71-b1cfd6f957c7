import Vue from 'vue'
import { i18n, permission } from '@/main.js'
const listToolBar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  // { id: "del", icon: "icon_solid_edit", title: i18n.t("删除") },
  // { id: "Edit", icon: "icon_solid_edit", title: i18n.t("更新") },
  // { id: "save", icon: "icon_solid_edit", title: i18n.t("保存") },
  { id: 'start', icon: 'icon_solid_Createorder', title: i18n.t('启用') },
  { id: 'stop', icon: 'icon_solid_Cancel', title: i18n.t('禁用') }
  // { id: "Cancel", icon: "icon_solid_Createorder", title: i18n.t("取消") },
]

const listColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'marketFactorCode',
    headerText: i18n.t('行情因子编码'),
    cssClass: 'field-content'
  },
  {
    field: 'marketFactorName',
    headerText: i18n.t('行情因子名称'),
    cssClass: ''
  },
  {
    field: 'marketFactorSpec',
    headerText: i18n.t('行情因子规格')
  },
  {
    field: 'status', // 单据状态 0:有效 1:无效
    headerText: i18n.t('状态'),
    width: 200,
    valueConverter: {
      type: 'map',
      map: [
        { status: 0, label: i18n.t('有效'), cssClass: 'title-#6386c1' },
        { status: 1, label: i18n.t('无效'), cssClass: 'title-#9baac1' }
      ],
      fields: { text: 'label', value: 'status' }
    },
    cellTools: [
      {
        id: 'start',
        // icon: "icon_solid_Createorder",
        title: i18n.t('启用'),
        visibleCondition: (data) => {
          return data['status'] == 1
        }
      },
      {
        id: 'stop',
        // icon: "icon_solid_Cancel",
        title: i18n.t('禁用'),
        visibleCondition: (data) => {
          return data['status'] == 0
        }
      }
    ]
  },
  {
    width: '100',
    field: 'marketFactorBrand',
    headerText: i18n.t('品牌')
  },
  {
    field: 'unitName',
    headerText: i18n.t('单位'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<span>{{data.unitName}}/{{data.unitCode}}</span>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
    // editTemplate: function () {
    //   return {
    //     template: Vue.component("unitId", {
    //       template: `
    //       <mt-select
    //         :width="100"
    //         ref="unitRef"
    //         :id="data.column.field"
    //         v-model="data.unitId"
    //         :placeholder="$t('请选择单位')"
    //         :fields="{ text: 'unitName', value: 'id' }"
    //         :data-source="unitList"
    //         @change="handleChangeCellCheckBox"
    //       ></mt-select>`,
    //       data() {
    //         return { data: {}, field: "", unitList: [] };
    //       },
    //       mounted() {
    //         this.getCommonList();
    //       },
    //       methods: {
    //         getCommonList() {
    //           this.$API.masterData.pagedQueryUnit().then((res) => {
    //             this.unitList = res.data.records;
    //           });
    //         },
    //         handleChangeCellCheckBox(e) {
    //           this.data.unitId = e.itemData;
    //           console.log(e.itemData);
    //           this.$parent.$emit("handleChangeCellCheckBox", {
    //             index: this.data.index,
    //             key: "unitName",
    //             value: e.itemData,
    //           });
    //         },
    //       },
    //     }),
    //   };
    // },
  },
  {
    field: 'attribute', //0:原材料 1:工序
    headerText: i18n.t('属性'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('原材料'), 1: i18n.t('工序') }
    }
  },
  {
    field: 'quoteUpperLimit',
    headerText: i18n.t('报价上限'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<span>{{getLimitValue}}</span>`,
          data() {
            return { data: {} }
          },
          computed: {
            getLimitValue() {
              return `${this.data.quoteUpperLimit.toFixed(2)}%`
            }
          }
        })
      }
    }
  },
  {
    field: 'quoteLowerLimit',
    headerText: i18n.t('报价下限'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<span>{{getLimitValue}}</span>`,
          data() {
            return { data: {} }
          },
          computed: {
            getLimitValue() {
              return `${this.data.quoteLowerLimit.toFixed(2)}%`
            }
          }
        })
      }
    }
  },
  // {
  //   field: "companyAuth", //companyRelList
  //   headerText: i18n.t("公司"),
  //   cssClass: "field-content",
  //   valueConverter: {
  //     type: "placeholder",
  //     placeholder: i18n.t("授权公司"),
  //   },
  // },
  // {
  //   field: "priceSourceType", //0:线上寻源 1:线下寻源
  //   headerText: i18n.t("价格来源"),
  //   valueConverter: {
  //     type: "map",
  //     map: { 0: i18n.t("线上寻源"), 1: i18n.t("线下寻源") },
  //   },
  // },
  {
    field: 'status', // 0:有效 1:无效
    headerText: i18n.t('启用'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('有效'), 1: i18n.t('无效') }
    }
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (isNaN(parseInt(e))) {
          return '--'
        } else {
          let date = new Date(Number(e))
          let year = date.getFullYear()
          let month = date.getMonth() + 1
          let day = date.getDate()
          month = month < 10 ? '0' + month : month
          day = day < 10 ? '0' + day : day
          return year + '-' + month + '-' + day
        }
      }
    }
    // valueConverter: {
    //   type: "placeholder",
    //   placeholder: "--",
    // },
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    valueConverter: {
      type: 'placeholder',
      placeholder: '--'
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  },
  {
    field: 'factorFile', //sourcingFileList
    headerText: i18n.t('附件'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<span @click="viewFileList" style="color: var(--plugin-ct-content-color);cursor: pointer;font-size: 14px;text-align: left;" v-if="getFileName == '查看附件'">{{getFileName}}</span><span v-else>{{getFileName}}</span>`,
          data() {
            return { data: {} }
          },
          computed: {
            getFileName() {
              if (this?.data?.sourcingFileList == null) {
                return '--'
              } else {
                return i18n.t('查看附件')
              }
            }
          },
          methods: {
            viewFileList() {
              this.$parent.$emit('viewFileList', this.data)
            }
          }
        })
      }
    }
  }
]
// const editSettings = {
//   allowAdding: true,
//   allowEditing: true,
//   allowDeleting: true,
//   mode: "Normal", // 默认normal模式
//   allowEditOnDblClick: true,
//   showConfirmDialog: false,
//   showDeleteConfirmDialog: true,
//   newRowPosition: "Bottom",
// };
const attributeTypeCols = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    width: '200',
    field: 'createUserName',
    headerText: i18n.t('属性代码')
  },
  {
    width: '200',
    field: 'createUserName',
    headerText: i18n.t('属性名称')
  },
  {
    width: '200',
    field: 'createUserName',
    headerText: i18n.t('父类')
  }
]
export const pageConfig = (url) => [
  {
    title: i18n.t('成本因子'),
    lineSelection: true,
    toolbar: listToolBar,
    gridId: permission.gridId['purchase']['factor']['list'],
    grid: {
      allowFiltering: true,
      columnData: listColumnData,
      //
      // editSettings,
      asyncConfig: {
        url
      }
    }
  },
  {
    title: i18n.t('属性大类'),
    lineSelection: true,
    toolbar: listToolBar,
    gridId: permission.gridId['purchase']['factor']['list'],
    grid: {
      allowFiltering: true,
      columnData: attributeTypeCols,
      //
      // editSettings,
      asyncConfig: {
        url
      }
    }
  }
]
