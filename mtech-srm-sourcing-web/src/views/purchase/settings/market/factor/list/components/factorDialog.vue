// 行情因子新增
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="marketFactorName" :label="$t('行情因子名称')">
          <mt-input
            v-model="formObject.marketFactorName"
            float-label-type="Never"
            :placeholder="$t('请输入行情因子名称')"
          ></mt-input>
        </mt-form-item>
        <!-- <mt-form-item prop="marketFactorCode" :label="$t('行情因子编码')">
          <mt-input
            v-model="formObject.marketFactorCode"
            float-label-type="Never"
            :placeholder="$t('请输入行情因子编码')"
          ></mt-input>
        </mt-form-item> -->
        <mt-form-item prop="marketFactorSpec" :label="$t('行情因子规格')">
          <mt-input
            v-model="formObject.marketFactorSpec"
            float-label-type="Never"
            :placeholder="$t('请输入因子规格')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="marketFactorBrand" :label="$t('品牌')">
          <mt-input
            v-model="formObject.marketFactorBrand"
            float-label-type="Never"
            :placeholder="$t('请输入品牌')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="unitCode" :label="$t('单位')">
          <mt-select
            ref="unitRef"
            v-model="formObject.unitCode"
            float-label-type="Never"
            :data-source="unitList"
            :fields="{ text: 'unitName', value: 'unitCode' }"
            :placeholder="$t('请选择单位')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="quoteLowerLimit" :label="$t('报价下限(%)')">
          <mt-input-number
            :min="0"
            :max="100"
            :show-clear-button="false"
            float-label-type="Never"
            :placeholder="$t('请输入报价下限')"
            v-model.number="formObject.quoteLowerLimit"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item prop="quoteUpperLimit" :label="$t('报价上限(%)')">
          <mt-input-number
            :min="0"
            :max="100"
            v-model.number="formObject.quoteUpperLimit"
            :show-clear-button="false"
            float-label-type="Never"
            :placeholder="$t('请输入报价上限')"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item prop="attribute" :label="$t('属性')">
          <mt-select
            ref="attributeRef"
            v-model="formObject.attribute"
            float-label-type="Never"
            :data-source="attributeList"
            :placeholder="$t('请选择属性')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="priceSourceType" :label="$t('价格来源')">
          <mt-select
            ref="priceSourceTypeRef"
            v-model="formObject.priceSourceType"
            float-label-type="Never"
            :data-source="priceSourceTypeList"
            :placeholder="$t('请选择价格来源')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('启用')">
          <mt-select
            ref="statusRef"
            v-model="formObject.status"
            float-label-type="Never"
            :data-source="statusList"
            :placeholder="$t('请选择启用状态')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="groupTypeId" :label="$t('审批组别')">
          <mt-select
            ref="groupTypeRef"
            v-model="formObject.groupTypeId"
            float-label-type="Never"
            :data-source="groupTypeIdList"
            :placeholder="$t('请选择审批组别')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('授权公司')">
          <div class="div-auth" @click="handleAuthDialog">
            {{ getFormAuthText }}
          </div>
        </mt-form-item>
        <mt-form-item :label="$t('附件')">
          <div class="div-auth" @click="handleUploadDialog">
            {{ getFormFileText }}
          </div>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')">
          <mt-input
            float-label-type="Never"
            v-model="formObject.remark"
            :placeholder="$t('请输入备注')"
          ></mt-input>
        </mt-form-item>
        <!-- <mt-form-item prop="remark" :label="$t('附件')">
          <mt-input
            float-label-type="Never"
            :placeholder="$t('附件')"
          ></mt-input>
        </mt-form-item> -->
        <!-- <mt-form-item prop="groupTypeId" :label="$t('审批组别')">
          <mt-select
            ref="groupTypeRef"
            v-model="formObject.groupTypeId"
            float-label-type="Never"
            :data-source="groupTypeIdList"
            :placeholder="$t('请选择审批组别')"
          ></mt-select>
        </mt-form-item> -->
        <!-- <mt-form-item prop="remark" :label="$t('操作记录')">
          <mt-input
            v-model="formObject.xxxField"
            float-label-type="Never"
            :placeholder="$t('操作记录')"
          ></mt-input>
        </mt-form-item> -->
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { utils } from '@mtech-common/utils'
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formRules: {},
      formObject: {
        attribute: null, //	属性 0:原材料 1:工序
        groupTypeId: null, //组别id
        groupTypeName: null, //	组别名称
        marketFactorBrand: null, //品牌
        marketFactorCode: null, //行情因子编码
        marketFactorName: null, //行情因子名称
        marketFactorSpec: null, //行情因子规格
        priceSourceType: null, //价格来源 0:线上寻源 1:线下寻源
        quoteLowerLimit: null, //报价下限
        quoteUpperLimit: null, //报价上限
        remark: null, //备注
        status: null, //	单据状态 0:有效 1:无效
        unitCode: null, //单位编码
        unitName: null, //单位名称
        companyRelList: [], //新增的成本因子授权公司
        removeCompanyRelList: [], //移除的成本因子授权公司
        removeSourcingFileList: [], //删除的附件
        sourcingFileList: [] //新增的附件
        // id: null, //id
        // tenantId: null, //tenantId
        // version: null, //版本号
      },
      companyRelList: [], //新增的成本因子授权公司
      removeCompanyRelList: [], //移除的成本因子授权公司
      removeSourcingFileList: [], //删除的附件
      sourcingFileList: [], //新增的附件
      unitList: [], //单位列表
      //属性 0:原材料 1:工序
      attributeList: [
        { text: this.$t('原材料'), value: 0 },
        { text: this.$t('工序'), value: 1 }
      ],
      // 价格来源 0:线上寻源 1:线下寻源
      priceSourceTypeList: [
        { text: this.$t('线上寻源'), value: 0 },
        { text: this.$t('线下寻源'), value: 1 }
      ],
      //单据状态 0:有效 1:无效
      statusList: [
        { text: this.$t('是'), value: 0 },
        { text: this.$t('否'), value: 1 }
      ],
      groupTypeIdList: [
        { text: this.$t('电子'), value: 0 },
        { text: this.$t('结构'), value: 1 },
        { text: this.$t('背光'), value: 2 }
      ],
      editStatus: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData[0],
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    getFormAuthText() {
      let _list = this?.formObject?.companyRelList ?? []
      if (_list.length) {
        let _name = []
        _list.forEach((e) => {
          _name.push(e.companyName)
        })
        return _name.join(',')
      } else {
        return this.$t('请选择授权公司')
      }
    },
    getFormFileText() {
      let _list = this?.sourcingFileList ?? []
      if (_list.length) {
        let _name = []
        _list.forEach((e) => {
          _name.push(e.fileName)
        })
        return _name.join(',')
      } else {
        return this.$t('上传附件')
      }
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    if (this.modalData && this.modalData.data) {
      this.editStatus = true
      this.formObject = [{ ...this.modalData.data }]
    }
    this.getCommonList()
    this.getFormValidRules('formRules', this.$API.marketFactor.saveMarketFactorValid)
  },
  methods: {
    getCommonList() {
      this.$API.masterData.pagedQueryUnit().then((res) => {
        this.unitList = res.data.records
      })
    },
    confirm() {
      this.formObject.quoteLowerLimit = +this.formObject.quoteLowerLimit
      this.formObject.quoteUpperLimit = +this.formObject.quoteUpperLimit
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = { ...this.formObject }
          if (!this.editStatus) {
            delete params.id
          }
          this.$utils.assignDataFromRefs(params, [
            {
              key: 'unitCode', //单位unitId下拉框数据
              ref: this.$refs.unitRef.ejsRef,
              fields: 'unitName'
            },
            {
              key: 'groupTypeId', //审批组别groupTypeId下拉框数据
              ref: this.$refs.groupTypeRef.ejsRef,
              fields: {
                groupTypeName: 'text'
              }
            }
          ])
          let _fileList = utils.cloneDeep(this.sourcingFileList),
            _saveFile = []
          _fileList.forEach((e) => {
            _saveFile.push({
              // docId: 0,
              // docType: "",
              // fileDetailId: 0,
              // fileDetailInfo: "",
              fileName: e.fileName,
              fileSize: e.fileSize,
              fileType: e.fileType,
              id: e.id,
              // lineNo: 0,
              // nodeCode: "",
              // nodeName: "",
              // nodeType: 0,
              // parentId: 0,
              // roundNo: "",
              // supplierCode: "",
              // supplierId: 0,
              // supplierName: "",
              // syncStatus: 0,
              sysFileId: e.id,
              url: e.url
            })
          })
          params.sourcingFileList = _saveFile
          this.$API.marketFactor.saveMarketFactor([params]).then((res) => {
            if (res.code == 200) {
              this.$emit('confirm-function')
            }
          })
        }
      })
    },
    //显示公司授权弹框
    handleAuthDialog() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "components/market/companyAuth" */ 'COMPONENTS/market/companyAuth/index.vue'
          ),
        data: { localList: this.companyRelList },
        success: (res) => {
          let { companyRelList, removeCompanyRelList } = res
          this.companyRelList = companyRelList
          this.formObject.companyRelList = companyRelList
          this.formObject.removeCompanyRelList = removeCompanyRelList
        }
      })
    },
    //上传附件弹框
    handleUploadDialog() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "components/Upload/uploaderDialog" */ 'COMPONENTS/Upload/uploaderDialog.vue'
          ),
        data: {
          fileData: utils.cloneDeep(this.sourcingFileList),
          isView: false, // 是否为预览
          required: false, // 是否必须
          title: this.$t('附件')
        },
        success: (res) => {
          this.sourcingFileList = res
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/ .div-auth {
    background: #fafafa;
    height: 35px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    color: #0f0f0f;
    font-size: 12px;
    cursor: pointer;
    border-radius: 5px;
  }
}
</style>
