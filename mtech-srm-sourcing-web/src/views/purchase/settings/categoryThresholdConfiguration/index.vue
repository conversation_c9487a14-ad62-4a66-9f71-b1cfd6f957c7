<template>
  <!-- 品类价格阈值配置 - 采方 -->
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      :is-grid-display="true"
      @reset="handleCustomReset"
      @search="handleCustomSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="categoryCode" :label="$t('品类编码')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="searchFormModel.categoryCode"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="categoryName" :label="$t('品类名称')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="searchFormModel.categoryName"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-select
            style="flex: 1"
            v-model="searchFormModel.status"
            :data-source="statusOptions"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <ScTable
        ref="xTable"
        class="xTable-class"
        :row-config="{ height: 130 }"
        :columns="columns"
        :table-data="tableData"
        show-overflow
        :min-height="600"
        border="none"
        header-align="left"
        row-class-name="table-row-class"
        cell-style="table-cell-class"
        align="center"
        style="padding-top: unset"
        :edit-config="{
          trigger: 'dblclick',
          mode: 'row',
          showStatus: true,
          activeMethod: this.activeRowMethod
        }"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
        @edit-closed="editComplete"
        @edit-actived="editBegin"
        @edit-disabled="editDisabledEvent"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
        </template>
        <!-- <template #categoryCodeEdit="{ row }">
          <vxe-select
            v-model="row.categoryCode"
            :placeholder="$t('请选择品类')"
            :options="categoryOptions"
            transfer
            filterable
            clearable
            @refreshOption="getItemDataSource"
            @change="(value) => categoryChange(value, row)"
          ></vxe-select>
        </template> -->
        <template #categoryCodeEdit="{ row }">
          <vxe-pulldown ref="xDown" transfer>
            <template #default>
              <vxe-input
                :value="row.categoryCode"
                :placeholder="$t('请选择品类')"
                readonly
                @click="focusCategoryCode"
              ></vxe-input>
            </template>
            <template #dropdown>
              <vxe-input
                prefix-icon="vxe-icon-search"
                :placeholder="$t('搜索')"
                @keyup="keyupCategoryCode"
                style="width: 100%"
              ></vxe-input>
              <vxe-list
                height="200"
                class="predict-vxe-dropdown"
                :data="categoryOptions"
                auto-resize
              >
                <template #default="{ items }">
                  <div
                    class="predict-vxe-list-item"
                    v-for="item in items"
                    :key="item.value"
                    @click="categoryChange(item, row)"
                  >
                    <span style="padding: 5px">{{ item.label }}</span>
                  </div>
                </template>
              </vxe-list>
            </template>
          </vxe-pulldown>
        </template>
        <template #categoryNameEdit="{ row }">
          <vxe-input v-model="row.categoryName" disabled placeholder=" " />
        </template>
        <template #priceFloorEdit="{ row }">
          <vxe-input v-model="row.priceIntervalMin" type="number" placeholder=" " />
        </template>
        <template #priceCeilingEdit="{ row }">
          <vxe-input v-model="row.priceLowerLimit" type="number" placeholder=" " />
        </template>
        <template #statusEdit="{ row }">
          <vxe-select
            v-model="row.status"
            :placeholder="$t('请选择状态')"
            :options="statusOptions"
            transfer
            :disabled="true"
          />
        </template>
      </ScTable>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { statusOptions, ForecastColumnData, NewRowData, ToolBar } from './config/constant'
import { utils } from '@mtech-common/utils'
export default {
  components: {
    ScTable,
    CollapseSearch
  },
  data() {
    return {
      toolbar: ToolBar,
      categoryOptions: [], // 品类下拉选项
      statusOptions,
      getSupplierDataSource: () => {}, // 供应商 下拉选项
      getItemDataSource: () => {},
      apiWaitingQuantity: 0, // 调用的api正在等待数
      searchFormModel: {
        status: null,
        categoryCode: '',
        categoryName: ''
      },
      forecastPageCurrent: 1,
      syncVersion: '',
      titleList: [],
      tableData: [],
      columns: ForecastColumnData,
      plannerListOptions: [], // 计划员 下列选项
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 50, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },
      isEditing: false, // 正在编辑数据
      isEdit: false
    }
  },
  mounted() {
    this.getItemDataSource = utils.debounce(this.getItem, 1000)
    this.getItemDataSource({ value: '' })
  },
  methods: {
    keyupCategoryCode(e) {
      this.getItemDataSource(e)
    },
    focusCategoryCode() {
      this.$refs.xDown.showPanel()
    },
    focusItemCode(e, row) {
      if (!row.factoryCode) {
        this.$toast({ content: this.$t('请先选择工厂'), type: 'warning' })
        return false
      }
      this.$refs.xDownItem.showPanel()
    },
    selectItemCode(e, row) {
      row.itemCode = e.itemCode
      row.itemName = e.itemName
      row.itemId = e.id
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRecords = $grid.getCheckboxRecords()
      const commonToolbar = [
        'lose_efficacy',
        'activation',
        'Filter',
        'Delete',
        'Refresh',
        'refreshDataByLocal',
        'filterDataByLocal',
        'CloseEdit',
        'Setting'
      ]
      if (code === 'CloseEdit') {
        // 清除编辑状态
        this.$refs.xTable.$refs.xGrid.clearEdit()
        this.handleCustomSearch()
        return
      }
      if (this.isEdit) {
        // this.$toast({ content: this.$t('请先完成或结束编辑操作'), type: 'warning' })
        return
      }

      if (selectedRecords.length == 0 && commonToolbar.includes(code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const idList = []
      selectedRecords.forEach((item) => {
        idList.push(item.id)
      })

      if (code === 'Add') {
        // 新增
        // const currentViewRecords = $grid.getTableData().visibleData
        // 新增一行
        $grid.insert([NewRowData])
        this.$nextTick(() => {
          // 获取最新的表格视图数据
          const currentViewRecords = $grid.getTableData().visibleData
          // 将新增的那一条设置为编辑状态
          this.$refs.xTable.$refs.xGrid.setEditRow(currentViewRecords[0])
        })
      } else if (code === 'Delete') {
        for (let i = 0; i < selectedRecords.length; i++) {
          if (selectedRecords[i].status !== 'draft') {
            this.$toast({
              type: 'error',
              content: this.$t('只能删除状态为"草稿"的数据')
            })
            return
          }
        }
        // 删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: idList.length
              ? this.$t('确认删除选中的数据？')
              : this.$t('确认删除符合筛选条件的数据？')
          },
          success: () => {
            this.deletecategoryInfo(idList)
          }
        })
      } else if (code === 'activation') {
        // 生效
        this.activation(idList)
      } else if (code === 'lose_efficacy') {
        // 失效
        this.loseEfficacy(idList)
      } else if (code === 'Import') {
        this.$dialog({
          modal: () => import('@/components/uploadDialog'),
          data: {
            title: this.$t('导入'),
            importApi:
              this.$API.CategoryThresholdConfiguration.importCategoryThresholdConfigurationInfo,
            downloadTemplateApi:
              this.$API.CategoryThresholdConfiguration.importDownlaodCategoryThresholdConfiguration,
            paramsKey: 'excel'
          },
          success: () => {
            // 导入之后刷新列表
            this.handleCustomSearch()
          }
        })
        return
      }
    },
    // 生效
    activation(idList) {
      const params = {
        ids: idList,
        status: 'valid'
      }
      this.$API.CategoryThresholdConfiguration.activeCategoryThresholdConfigurationInfo(
        params
      ).then((res) => {
        if (res && res.code === 200) {
          this.$toast({
            type: 'success',
            content: this.$t(res.msg || '操作成功')
          })
          this.handleCustomSearch()
        } else {
          this.$toast({
            type: 'error',
            content: this.$t(res.msg || '操作失败')
          })
        }
      })
    },
    // 失效
    loseEfficacy(idList) {
      const params = {
        ids: idList,
        status: 'invalid'
      }
      this.$API.CategoryThresholdConfiguration.invalidCategoryThresholdConfigurationInfo(
        params
      ).then((res) => {
        if (res && res.code === 200) {
          this.$toast({
            type: 'success',
            content: this.$t(res.msg || '操作成功')
          })
          this.handleCustomSearch()
        } else {
          this.$toast({
            type: 'error',
            content: this.$t(res.msg || '操作失败')
          })
        }
      })
    },
    // 删除信息
    deletecategoryInfo(list) {
      this.apiStartLoading()
      // const params = {
      //   ids: list.filter((n) => n !== null)
      // }
      let ids = list.filter((n) => n !== null)
      this.$API.CategoryThresholdConfiguration.deleteCategoryThresholdConfigurationInfo(ids)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 主数据 品类
    getItem(args) {
      const { value } = args
      const params = {
        categoryName: value || ''
      }
      this.$API.masterData.getCategoryList(params).then((res) => {
        if (res) {
          let list = res?.data || []
          list = list.map((item) => {
            item.value = item.categoryCode
            item.label = item.categoryName
            item.key = item.id
            return item
          })
          this.categoryOptions = list
        }
      })
    },
    categoryChange(val, row) {
      row.categoryId = val.id // id
      row.categoryCode = val.categoryCode // code
      row.categoryName = val.categoryName // name
      this.$refs.xDown.hidePanel()
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 校验点击的对象是否为取消编辑按钮
        if (args.$event.target.innerText === this.$t('取消编辑')) {
          // 清除编辑状态
          this.$refs.xTable.$refs.xGrid.clearEdit()
          this.handleCustomSearch()
          return
        }
        // 远程数据才有$event属性
        //1、 校验必填 没通过就是this.$refs.xTable.$refs.xGrid.setEditRow(row)
        if (!this.isValidData(row)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.xTable.$refs.xGrid.setEditRow(row)
          return
        }
        // 如果存在id，则表明当前是保存操作
        if (row.createUser) {
          this.handleSaveInfo(row)
          return
        }
        this.handleAddInfo(row)
      }
    },
    // 保存操作
    handleSaveInfo(row) {
      this.$API.CategoryThresholdConfiguration.editCategoryThresholdConfigurationInfo(row)
        .then((res) => {
          if (res && res.code === 200) {
            this.$toast({
              type: 'success',
              content: this.$t(res.msg || '修改成功')
            })
            this.handleCustomSearch()
          }
          this.$t({
            type: 'error',
            content: this.$t(res.msg || '修改失败')
          })
        })
        .catch((e) => {
          this.$toast({
            type: 'error',
            content: this.$t(e.msg)
          })
        })
    },
    // 新增操作
    handleAddInfo(row) {
      if (row.id.includes('row')) {
        row.id = null
      }
      this.$API.CategoryThresholdConfiguration.addCategoryThresholdConfigurationInfo(row)
        .then((res) => {
          if (res && res.code === 200) {
            this.$toast({
              type: 'success',
              content: this.$t(res.msg || '新增成功')
            })
            this.handleCustomSearch()
          }
          this.$t({
            type: 'error',
            content: this.$t(res.msg || '新增失败')
          })
        })
        .catch((e) => {
          this.$toast({
            type: 'error',
            content: this.$t(e.msg)
          })
        })
    },
    editBegin(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        this.getItemDataSource({ value: '' }, row)
      }
      this.isEdit = true
    },
    // 双击编辑触发的方法
    activeRowMethod({ row }) {
      // 已确认的数据不可修改
      if (row.status === 5) {
        return false
      }
      return true
    },
    editDisabledEvent() {
      this.$toast({
        content: this.$t('此状态数据不可编辑'),
        type: 'warning'
      })
    },
    // 校验数据
    isValidData(data) {
      const { priceLowerLimit, categoryCode, priceIntervalMin } = data
      let valid = false
      if (!categoryCode) {
        // 品类编码
        this.$toast({ content: this.$t('品类编码不能为空'), type: 'warning' })
      } else if (!priceLowerLimit) {
        // 价格上限
        this.$toast({ content: this.$t('价格上限不可为空'), type: 'warning' })
      } else if (!priceIntervalMin) {
        // 价格下限
        this.$toast({ content: this.$t('价格下限不可为空'), type: 'warning' })
      } else {
        valid = true
      }

      return valid
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'bigVersionNo') {
            this.searchFormModel[key] = this.versionList[0]
          }
        }
      }
      this.handleCustomSearch()
    },
    paramsAssble(obj) {
      if (!obj) return
      let _rules = []
      const keyLabel = {
        categoryName: this.$t('品类名称'),
        categoryCode: this.$t('品类编码'),
        status: this.$t('状态')
      }
      let keyValueList = Object.entries(obj)
      keyValueList.map((item) => {
        let itemForm = {
          field: item[0],
          label: keyLabel[item[0]],
          operator: 'equal',
          type: 'string',
          value: item[1]
        }
        if (item[1] === 0 || item[1]) {
          _rules.push(itemForm)
        }
      })
      return _rules
    },
    // 采方-获取信息列表
    handleCustomSearch() {
      this.isEdit = false
      const params = {
        condition: 'and',
        page: {
          size: this.forecastPageSettings.pageSize,
          current: this.forecastPageCurrent
        },
        rules: this.paramsAssble(this.searchFormModel)
      }
      this.apiStartLoading()
      this.$API.CategoryThresholdConfiguration.searchList(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const total = res?.data?.total || 0
            this.forecastPageSettings.totalPages = Math.ceil(
              Number(total) / this.forecastPageSettings.pageSize
            )
            this.forecastPageSettings.totalRecordsCount = Number(total)
            const records = res?.data?.records || [] // 表格数据
            // 处理表数据
            this.tableData = records
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    handleDataSource(data) {
      const { records } = data
      const tableData = []
      tableData.push(records)

      return tableData
      // return records
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.handleCustomSearch()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-select-selection {
  background-color: transparent !important;
}
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
}
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
::v-deep .xTable-class .vxe-table .table-row-class td {
  height: 40px !important;
}
</style>
