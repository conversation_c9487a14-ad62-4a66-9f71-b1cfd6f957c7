import { i18n } from '@/main.js'
export const ConstDynamicTitleStr = 'title_' // 动态表格数据的 key 标识

// 业务组类型数字字典编码
export const BusinessGroupTypeDictCode = 'BUSINESSGROUP'

// 表格组件类型
export const ComponentType = {
  view: 'view',
  edit: 'edit',
  mustEdit: 'mustEdit'
}

// 新增行固定数据
export const NewRowData = {
  thePrimaryKey: 'add' + Math.random().toString(36).substring(3, 8),
  status: 'draft', // 状态 0 新建、1 已修改、2 待反馈、3 反馈正常、4 反馈异常、5 已确认
  categoryCode: '',
  categoryName: '',
  createUserName: '',
  createTime: '',
  updateUserName: '',
  updateTime: ''
}

export const ToolBar = [
  { code: 'Add', name: i18n.t('新增'), icon: 'vxe-icon-square-plus', status: 'primary' },
  {
    code: 'Delete',
    name: i18n.t('删除'),
    icon: 'vxe-icon-delete',
    status: 'primary'
  },
  {
    code: 'CloseEdit',
    name: i18n.t('取消编辑'),
    icon: 'vxe-icon-edit',
    status: 'primary',
    transfer: true
  },
  {
    code: 'Import',
    name: i18n.t('导入'),
    icon: 'vxe-icon-cloud-upload',
    status: 'primary'
  },
  {
    code: 'activation',
    name: i18n.t('生效'),
    icon: '',
    status: 'primary'
  },
  {
    code: 'lose_efficacy',
    name: i18n.t('失效'),
    icon: '',
    status: 'primary'
  }
  // {
  //   code: 'ForecastExport',
  //   name: i18n.t('导出'),
  //   icon: 'vxe-icon-cloud-download',
  //   status: 'primary'
  // }
]

export const statusOptions = [
  { label: i18n.t('草稿'), value: 'draft' },
  { label: i18n.t('有效'), value: 'valid' },
  { label: i18n.t('失效'), value: 'invalid' }
]

// 配置表格列数据
export const ForecastColumnData = [
  {
    type: 'checkbox',
    width: 70,
    ignore: true
    // fixed: 'left'
  },
  {
    type: 'seq',
    width: 50,
    title: i18n.t('序号')
  },
  {
    field: 'categoryCode',
    title: i18n.t('品类编码'),
    width: 150,
    editRender: {},
    slots: {
      edit: 'categoryCodeEdit'
    }
  },
  {
    field: 'categoryName',
    title: i18n.t('品类名称'),
    width: 200,
    editRender: {},
    slots: {
      edit: 'categoryNameEdit'
    }
  },
  {
    field: 'priceIntervalMin',
    title: i18n.t('价格下限'),
    showOverflow: true,
    width: 120,
    editRender: {},
    slots: {
      edit: 'priceFloorEdit'
    }
  },
  {
    field: 'priceLowerLimit',
    title: i18n.t('价格上限'),
    showOverflow: true,
    width: 120,
    editRender: {},
    slots: {
      edit: 'priceCeilingEdit'
    }
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    width: 210,
    showOverflow: true,
    formatter: ({ row }) => {
      const item = statusOptions.find((item) => item.value === row.status)
      return item.label
    },
    editRender: {},
    slots: {
      edit: 'statusEdit'
    }
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人'),
    width: 150
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    width: 180
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人'),
    width: 150
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间')
  }
]

// 选择 物料 弹框 表格列数据
export const MaterielTableColumnData = [
  {
    fieldCode: 'itemCode', // 物料编号
    fieldName: i18n.t('物料编号')
  },
  {
    fieldCode: 'itemName', // 物料名称
    fieldName: i18n.t('物料名称')
  },
  {
    fieldCode: 'categoryResponse.categoryCode', // 品类
    fieldName: i18n.t('品类')
  },
  {
    fieldCode: 'itemDescription', // 规格型号
    fieldName: i18n.t('规格型号')
  },
  {
    fieldCode: 'oldItemCode', // 旧物料编号
    fieldName: i18n.t('旧物料编号')
  },
  {
    fieldCode: 'manufacturerName', // 制造商
    fieldName: i18n.t('制造商')
  }
]
