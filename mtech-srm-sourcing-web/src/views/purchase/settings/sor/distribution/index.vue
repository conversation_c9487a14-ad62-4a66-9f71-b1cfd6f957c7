<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { strategyToolbar, strategyColumnData } from './config'

export default {
  data() {
    return {
      queryDataSource: [], //新增、编辑中用到的下拉列表
      purchaseOrganizList: [], // 采购组织
      departmentList: [], //申请部门列表
      tenantIdList: [], //申请人列表
      pageConfig: [
        {
          toolbar: strategyToolbar,
          grid: {
            allowFiltering: true,
            columnData: strategyColumnData,
            asyncConfig: {
              url: this.$API.moduleConfig.getStrategyConfigs,
              defaultRules: [
                {
                  condition: 'and',
                  field: 'strategy_type',
                  operator: 'equal',
                  type: 'int',
                  value: 0 //0 分配策略  1分组建议
                }
              ]
            }
          }
        }
      ]
    }
  },

  mounted() {
    this.getStrategyConfigCondition()
  },
  methods: {
    handleSelectTab(e) {
      console.log('use-handleSelectTab', e)
    },
    //单元格标题点击操作
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
      if (e.field == 'strategyCode') {
        this.handleEditStrategyConfig(e.data)
      }
    },
    handleClickToolBar(e) {
      let _selectGridRecords = e.grid.getSelectedRecords()
      console.log('use-handleClickToolBar', e, _selectGridRecords)

      if (
        _selectGridRecords.length <= 0 &&
        (e.toolbar.id == 'start' || e.toolbar.id == 'stop' || e.toolbar.id == 'Delete')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      if (e.toolbar.id == 'Add') {
        this.handleAddStrategyConfig()
      } else if (e.toolbar.id == 'start') {
        this.handleBatchUpdateStart(_selectGridRecords)
      } else if (e.toolbar.id == 'stop') {
        this.handleBatchUpdateStop(_selectGridRecords)
      } else if (e.toolbar.id == 'Delete') {
        this.handleBatchUpdateDelete(_selectGridRecords)
      }
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      if (e.tool.id == 'edit') {
        //编辑操作
        this.handleEditStrategyConfig(e.data)
      } else if (e.tool.id == 'start') {
        //启用操作
        this.handleUpdateConfigStatus([e.data.id], 1)
      } else if (e.tool.id == 'stop') {
        //停用操作
        this.handleUpdateConfigStatus([e.data.id], 2)
      } else if (e.tool.id == 'delete') {
        this.handleDeleteConfig([e.data.id])
      }
    },
    //批量启动操作
    handleBatchUpdateStart(_selectGridRecords) {
      //状态 0 未启用 1 启用 2 停用
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      let _disableStatusRecords = _selectGridRecords.filter((s) => {
        return s.enableStatus === 1
      })
      if (_disableStatusRecords.length > 0) {
        //选中数据中，存在‘启用’状态
        this.$toast({
          content: this.$t("选中数据中，存在'启用'状态的数据"),
          type: 'warning'
        })
        return
      } else {
        this.handleUpdateConfigStatus(_selectIds, 1)
      }
    },
    //批量停用操作
    handleBatchUpdateStop(_selectGridRecords) {
      //状态 0 未启用 1 启用 2 停用
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      let _disableStatusRecords = _selectGridRecords.filter((s) => {
        return s.enableStatus !== 1
      })
      if (_disableStatusRecords.length > 0) {
        //选中数据中，存在非‘启用’状态
        this.$toast({
          content: this.$t("只有启用状态可执行'停用'操作"),
          type: 'warning'
        })
        return
      } else {
        this.handleUpdateConfigStatus(_selectIds, 2)
      }
    },
    //批量删除操作
    handleBatchUpdateDelete(_selectGridRecords) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.id)
      })
      this.handleDeleteConfig(_selectIds)
    },
    //新增分配策略
    handleAddStrategyConfig() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/settings/sor/distribution/components/strategyConfig" */ './components/strategyConfig.vue'
          ),
        data: {
          queryDataSource: this.queryDataSource,
          purchaseOrganizList: this.purchaseOrganizList, // 采购组织
          departmentList: this.departmentList, //申请部门列表
          tenantIdList: this.tenantIdList //申请人列表
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //编辑分配策略
    handleEditStrategyConfig(data) {
      if (data && data.id) {
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "router/purchase/settings/sor/distribution/components/strategyConfig" */ './components/strategyConfig.vue'
            ),
          data: {
            configId: data.id,
            queryDataSource: this.queryDataSource,
            purchaseOrganizList: this.purchaseOrganizList, // 采购组织
            departmentList: this.departmentList, //申请部门列表
            tenantIdList: this.tenantIdList //申请人列表
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    },
    //更新分组规则的状态(行内操作+批量操作)
    handleUpdateConfigStatus(ids, status) {
      // strategyType	策略类型 0 分配 1 建议
      //状态 0 未启用 1 启用 2 停用
      let _params = {
        configIds: ids.join(','),
        enableStatus: status,
        strategyType: 0
      }
      let _statusMap = [this.$t('草稿'), this.$t('启用'), this.$t('停用')]
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: `${this.$t('确认更新状态为')}${_statusMap[status]}？`
        },
        success: () => {
          this.$API.moduleConfig.updateStrategyConfigEnable(_params).then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    //删除规则
    handleDeleteConfig(ids) {
      //todo 弹框  确认
      let _params = {
        configIds: ids.join(','),
        strategyType: 0
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          this.$API.moduleConfig.deleteStrategyConfig(_params).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    getStrategyConfigCondition() {
      //获取规则下拉框字段列表
      this.$API.moduleConfig.getStrategyConfigCondition().then((res) => {
        let _res = res.data
        let _conditions = []
        for (let i in _res) {
          _conditions.push({
            field: i,
            headerText: _res[i],
            type: 'text'
          })
        }
        _conditions.forEach((e) => {
          if (e.field == 'requiredDeliveryDate' || e.field == 'applyTime') {
            e.type = 'date'
          }
        })
        this.queryDataSource = _conditions
        this.getMainData()
      })
    },
    getMainData() {
      // 采购组织
      // 数据源 purGroupName
      this.$API.masterData
        .purchaseOraginaze({
          organizationTypeCode: 'BUORG002ADM'
        })
        .then((res) => {
          this.purchaseOrganizList = res.data
          this.queryDataSource.forEach((e) => {
            if (e.field == 'purGroupName') {
              e.type = 'select'
              e.source = res.data
              // e.fields = { text: "organizationName", value: "id" };
              e.fields = {
                text: 'organizationName',
                value: 'organizationName'
              }
            }
          })
        })

      // 业务类型 - businessType
      // 数据源 businessTypeName
      this.$API.masterData
        .dictionaryGetList({
          dictCode: 'businessType'
        })
        .then((res) => {
          this.queryDataSource.forEach((e) => {
            if (e.field == 'businessTypeName') {
              e.type = 'select'
              e.source = res.data
              // e.fields = { text: "itemName", value: "id" };
              e.fields = { text: 'itemName', value: 'itemName' }
            }
          })
        })

      // 获取弹窗中的人员列表
      // 数据源 purName
      this.$API.masterData.getUserListByTenantId({ employeeName: '' }).then((res) => {
        this.tenantIdList = res.data
        this.queryDataSource.forEach((e) => {
          if (e.field == 'purName') {
            e.type = 'select'
            e.source = res.data
            // e.fields = { text: "employeeName", value: "id" };
            e.fields = { text: 'employeeName', value: 'employeeName' }
          }
        })
      })

      // 申请部门
      // 数据源 deptName
      this.$API.masterData
        .getDepartmentList({
          departmentName: ''
        })
        .then((res) => {
          this.departmentList = res.data
          this.queryDataSource.forEach((e) => {
            if (e.field == 'deptName') {
              e.type = 'select'
              e.source = res.data
              // e.fields = { text: "departmentName", value: "id" };
              e.fields = { text: 'departmentName', value: 'departmentName' }
            }
          })
        })

      // 供应商列表
      // 数据源 supplierName
      this.$API.masterData.getSupplierList().then((res) => {
        this.queryDataSource.forEach((e) => {
          if (e.field == 'supplierName') {
            e.type = 'select'
            e.source = res.data
            // e.fields = { text: "supplierName", value: "id" };
            e.fields = { text: 'supplierName', value: 'supplierName' }
          }
        })
      })

      // 品项列表
      // 数据源 itemCode itemName
      this.$API.masterData.getItemList().then((res) => {
        this.queryDataSource.forEach((e) => {
          if (e.field == 'itemName') {
            e.type = 'select'
            e.source = res.data
            // e.fields = { text: "itemName", value: "id" };
            e.fields = { text: 'itemName', value: 'itemName' }
          } else if (e.field == 'itemCode') {
            e.type = 'select'
            e.source = res.data
            e.fields = { text: 'itemCode', value: 'itemCode' }
          }
        })
      })

      // 品类列表
      // 数据源 getCategoryList
      this.$API.masterData.getCategoryList().then((res) => {
        // this.departmentList = res.data;
        this.queryDataSource.forEach((e) => {
          if (e.field == 'categoryName') {
            e.type = 'select'
            e.source = res.data
            // e.fields = { text: "categoryName", value: "id" };
            e.fields = { text: 'categoryName', value: 'categoryName' }
          }
        })
      })
      // 地点列表
      // 数据源 siteName
      this.$API.masterData.getSiteList().then((res) => {
        this.queryDataSource.forEach((e) => {
          if (e.field == 'siteName') {
            e.type = 'select'
            e.source = res.data.records
            // e.fields = { text: "siteName", value: "id" };
            e.fields = { text: 'siteName', value: 'siteName' }
          }
        })
      })

      // 库位、地点列表
      // 数据源 stockSite
      this.$API.masterData
        .getLocationList({
          page: {
            current: 1,
            size: 1000
          }
        })
        .then((res) => {
          this.queryDataSource.forEach((e) => {
            if (e.field == 'stockSite') {
              e.type = 'select'
              e.source = res.data.records
              // e.fields = { text: "locationName", value: "id" };
              e.fields = { text: 'locationName', value: 'locationName' }
            }
          })
        })
    }
  }
}
</script>
