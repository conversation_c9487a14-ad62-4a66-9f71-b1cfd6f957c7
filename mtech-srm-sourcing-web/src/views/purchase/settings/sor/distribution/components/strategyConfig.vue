<template>
  <div class="slider-panel-container">
    <div class="slider-modal mt-flex-direction-column">
      <div class="slider-header mt-flex">
        <div class="slider-title">{{ $t('规则配置') }}</div>
        <div class="slider-close" @click="cancel">+</div>
      </div>
      <div class="slider-content">
        <div class="rule-group">
          <div class="group-header">{{ $t('基础信息') }}</div>
          <div class="group-description"></div>
          <div class="group-content">
            <mt-form :rules="formRules">
              <mt-form-item v-if="strategyDetail.strategyCode" :label="$t('策略编号')">
                <mt-input
                  :readonly="true"
                  :show-clear-button="false"
                  v-model="strategyDetail.strategyCode"
                  type="text"
                  :placeholder="$t('策略编号')"
                ></mt-input>
              </mt-form-item>
              <mt-form-item :label="$t('策略名称')">
                <mt-input
                  v-model="strategyDetail.strategyName"
                  type="text"
                  :show-clear-button="false"
                  :placeholder="$t('策略名称')"
                ></mt-input>
              </mt-form-item>
              <mt-form-item :label="$t('策略优先级')">
                <mt-input
                  v-model="strategyDetail.sortValue"
                  type="text"
                  :show-clear-button="false"
                  :placeholder="$t('策略优先级')"
                ></mt-input> </mt-form-item
            ></mt-form>
          </div>
        </div>
        <div class="rule-group">
          <div class="group-header">{{ $t('筛选规则') }}</div>
          <div class="group-description">
            {{ $t('多个匹配值请使用英文分好进行区分 “;”') }}
          </div>
          <div class="group-content">
            <mt-rule-config
              ref="ruleRef"
              v-model="strategyRules"
              :query-fields="queryFields"
              :query-data-source="queryDataSource"
            ></mt-rule-config>
          </div>
        </div>
        <div class="rule-group">
          <div class="group-header">{{ $t('分配目标') }}</div>
          <div class="group-description"></div>
          <div class="group-content">
            <mt-form :rules="formRules">
              <mt-form-item :label="$t('分配到')"
                ><mt-select
                  v-model="strategyDetail.targetType"
                  :data-source="targetTypeList"
                  @change="handleChangeTargetType"
                  :placeholder="$t('分配到')"
                ></mt-select>
              </mt-form-item>
              <mt-form-item v-if="strategyDetail.targetType == 2" :label="$t('选择部门')">
                <mt-select
                  v-model="strategyDetail.targetId"
                  :data-source="propsData.departmentList"
                  :fields="{ text: 'departmentName', value: 'id' }"
                  :placeholder="$t('选择部门')"
                ></mt-select>
              </mt-form-item>
              <mt-form-item v-if="strategyDetail.targetType == 1" :label="$t('选择采购组')">
                <mt-select
                  v-model="strategyDetail.targetId"
                  :data-source="propsData.purchaseOrganizList"
                  :fields="{ text: 'organizationName', value: 'id' }"
                  :placeholder="$t('选择采购组')"
                ></mt-select>
              </mt-form-item>
              <mt-form-item v-if="strategyDetail.targetType == 0" :label="$t('选择申请人')">
                <mt-select
                  v-model="strategyDetail.targetId"
                  :data-source="propsData.tenantIdList"
                  :placeholder="$t('选择申请人')"
                  :fields="{ text: 'employeeName', value: 'id' }"
                ></mt-select> </mt-form-item
            ></mt-form>
          </div>
        </div>
      </div>
      <div class="slider-footer mt-flex">
        <span @click="confirm">{{ $t('确定') }}</span>
        <span @click="cancel">{{ $t('取消') }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import MtRuleConfig from '@mtech/common-rule-config'
Vue.component('mt-rule-config', MtRuleConfig)

export default {
  components: {},
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      strategyRules: [
        {
          groupLeft: '',
          dataSource: '',
          dataValue: '',
          symbol: '',
          groupRight: '',
          groupLink: 'or'
        }
      ],
      formRules: {},
      queryFields: { text: 'headerText', value: 'field' },
      queryDataSource: [],
      strategyDetail: {
        configId: null,
        strategyName: '',
        sortValue: 1,
        targetType: 0,
        targetId: 0
      },
      targetTypeList: [
        //选择分配到的类型
        { text: this.$t('部门'), value: 2 },
        { text: this.$t('采购组'), value: 1 },
        { text: this.$t('人'), value: 0 }
      ]
    }
  },
  mounted() {
    if (this.modalData && this.modalData.configId) {
      this.strategyDetail.configId = this.modalData.configId
      this.getStrategyDetailById()
    }
    if (this.modalData && this.modalData.queryDataSource) {
      this.queryDataSource = this.modalData.queryDataSource
    }
    this.getFormValidRules('formRules', this.$API.moduleConfig.saveStrategyConfigDistributeValid)
  },
  computed: {
    propsData() {
      return {
        ...this.modalData
      }
    }
  },
  methods: {
    getStrategyDetailById() {
      //根据configId获取配置详情
      this.$API.moduleConfig
        .getStrategyConfigDistributeDetail({
          configId: this.strategyDetail.configId
        })
        .then((res) => {
          let { strategyName, strategyCode, sortValue, targetType, targetId, conditions } = res.data
          this.$set(this, 'strategyDetail', {
            strategyCode,
            strategyName,
            sortValue,
            targetType,
            targetId
          })
          let _res = []
          for (let i in conditions) {
            let _temp = conditions[i]
            let _condition = {
              groupLeft: '',
              dataSource: '',
              dataValue: '',
              symbol: '',
              groupRight: '',
              groupLink: ''
            }
            if (_temp['join']) {
              _condition.groupLink = _temp['join']
            }
            if (_temp['bracket']) {
              if (_temp['bracket'] == '(') {
                _condition.groupLeft = '('
              } else {
                _condition.groupRight = ')'
              }
            }
            let _exp = _temp.expression
            _condition.dataSource = _exp.leftValue.field
            _condition.dataValue = _exp.rightValue.value
            _condition.symbol = _exp.operation
            _res.push(_condition)
          }
          this.strategyRules = _res
        })
    },
    handleChangeTargetType() {
      this.strategyDetail.targetId = null
    },
    confirm() {
      this.$refs.ruleRef
        .getValidResult()
        .then((e) => {
          let _rules = JSON.parse(JSON.stringify(e.rules)),
            _res = []
          for (let i in _rules) {
            let _temp = _rules[i]
            let _bracket = ''
            if (_temp['groupLeft'] == '(' && _temp['groupRight'] == ')') {
              _temp['groupLeft'] = ''
              _temp['groupRight'] = ''
            } else if (_temp['groupLeft'] == '(') {
              _bracket = '('
            } else if (_temp['groupRight'] == ')') {
              _bracket = ')'
            }
            let _join = ''
            if (_temp['groupLink']) {
              _join = _temp['groupLink']
            }
            let _stringTypeList = [
              'purGroupName',
              'supplierName',
              'deptName',
              'itemName',
              'itemCode',
              'purName',
              'businessTypeName',
              'siteName',
              'stockSite',
              'categoryName'
            ]
            _res.push({
              id: +i,
              expression: {
                leftValue: {
                  field: _temp['dataSource'],
                  type: ''
                },
                operation: _temp['symbol'],
                rightValue: {
                  value: _temp['dataValue'],
                  stringType: _stringTypeList.indexOf(_temp['dataSource']) > -1
                }
              },
              bracket: _bracket,
              join: _join
            })
          }

          let _params = {
            conditions: _res,
            sortValue: this.strategyDetail.sortValue, //优先级
            strategyName: this.strategyDetail.strategyName, //策略名称
            targetId: this.strategyDetail.targetId, //策略名称
            targetType: this.strategyDetail.targetType //策略名称
          }
          if (this.strategyDetail.configId) {
            //更新分配策略
            _params.configId = this.strategyDetail.configId
            this.$API.moduleConfig.updateStrategyConfigDistribute(_params).then((res) => {
              this.$emit('confirm-function')
              console.log('update-stragety--config', res)
            })
          } else {
            //保存分配策略
            this.$API.moduleConfig.saveStrategyConfigDistribute(_params).then((res) => {
              this.$emit('confirm-function')
              console.log('save-stragety--config', res)
            })
          }
        })
        .catch((e) => {
          console.warn('valid-error', e)
        })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
