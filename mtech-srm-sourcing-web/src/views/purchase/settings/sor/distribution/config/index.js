import { i18n } from '@/main.js'
//需求分配策略配置
export const strategyToolbar = [
  'Add',
  { id: 'start', icon: 'icon_solid_Createorder', title: i18n.t('启用') },
  { id: 'stop', icon: 'icon_solid_Cancel', title: i18n.t('停用') },
  'Delete'
]
export const strategyColumnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'strategyCode',
    headerText: i18n.t('策略编号'),
    width: 200,
    cellTools: ['edit', 'delete']
  },
  {
    field: 'strategyName',
    headerText: i18n.t('策略名称')
  },
  {
    field: 'sortValue',
    headerText: i18n.t('策略优先级')
  },
  {
    field: 'enableStatus',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('草稿'), 1: i18n.t('启用'), 2: i18n.t('停用') }
    },
    cellTools: [
      {
        id: 'start',
        // icon: "icon_solid_Createorder",
        title: i18n.t('启用'),
        visibleCondition: (data) => {
          return data['enableStatus'] !== 1
        }
      },
      {
        id: 'stop',
        // icon: "icon_solid_Cancel",
        title: i18n.t('停用'),
        visibleCondition: (data) => {
          return data['enableStatus'] == 1
        }
      }
    ]
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
