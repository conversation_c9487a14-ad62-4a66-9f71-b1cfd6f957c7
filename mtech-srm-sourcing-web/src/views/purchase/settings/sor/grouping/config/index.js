import { i18n } from '@/main.js'
//需求分组规则配置
export const groupToolbar = [
  'Add',
  { id: 'start', icon: 'icon_solid_Createorder', title: i18n.t('启用') },
  { id: 'stop', icon: 'icon_solid_Cancel', title: i18n.t('停用') },
  'Delete'
]
export const groupColumnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    width: 200,
    field: 'strategyCode',
    headerText: i18n.t('分组规则编号'),
    cssClass: 'field-content',
    cellTools: ['edit', 'delete']
  },
  {
    field: 'strategyName',
    headerText: i18n.t('分组规则名称')
  },
  {
    field: 'enableStatus',
    headerText: i18n.t('状态'),
    width: 200,
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('草稿'), 1: i18n.t('启用'), 2: i18n.t('停用') }
    },
    cellTools: [
      {
        id: 'start',
        // icon: "icon_solid_Createorder",
        title: i18n.t('启用'),
        visibleCondition: (data) => {
          return data['enableStatus'] !== 1
        }
      },
      {
        id: 'stop',
        // icon: "icon_solid_Cancel",
        title: i18n.t('停用'),
        visibleCondition: (data) => {
          return data['enableStatus'] == 1
        }
      }
    ]
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
